﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;

namespace Data
{
    public class ExceptionLogger
    {
        public static void LogException(Exception exception)
        {
            StreamWriter writer = null;
            try
            {
                /*if(Directory.Exists("Files")==false)  //Αν ο κατάλογος Files δεν υπάρχει
                {
                    Directory.CreateDirectory("Files");
                }*/
                
                if (exception.GetType() != typeof(System.Threading.ThreadAbortException))
                {
                    string exceptionsFilePath = HttpContext.Current.Server.MapPath("~/MentalViewExceptions.txt");

                    if (File.Exists(exceptionsFilePath) == false)  //Αν το αρχείο δεν υπάρχει
                    {
                        FileStream stream = File.Create(exceptionsFilePath);  //τοτε το δημιουργεί
                        stream.Close();  //και το κλείνει
                    }
                    writer = File.AppendText(exceptionsFilePath);
                    writer.WriteLine("Product Version: WebUI" + "\r\nDATE: " + DateTime.Now.ToString() + "\r\nSTACK TRACE: " + exception.StackTrace + "\r\nERROR: " + exception.Message + "\r\n(METHOD: " + exception.TargetSite + ")" + "\r\n(Source: " + exception.Source + ")" + "\r\nErrorID: " + Guid.NewGuid().ToString().ToUpper() + "\r\n");
                    if (exception.InnerException != null)
                    {
                        writer.WriteLine("InnerException: \r\nSTACK TRACE: " + exception.InnerException.StackTrace + "\r\nERROR: " + exception.InnerException.Message + "\r\n(METHOD: " + exception.InnerException.TargetSite + ")" + "\r\n");
                    }
                }
            }
            catch
            {
            }
            finally
            {
                if (writer != null)
                {
                    writer.Close();
                }
            }
        }
    }
}
