/*!
*  filename: ej.globalize.min.js
*  version : 18.1.0.42
*  Copyright Syncfusion Inc. 2001 - 2020. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./ej.core.min"],n):n()})(function(){(function(n,t){function ot(n,t){var s=n||"",k=p,t=t.toString(),ct=t.indexOf(".")>-1||n.indexOf(".")>-1,r=0,rt=0,i="",d="",a=n.split(","),ut="0",lt,at=n.toLowerCase().indexOf("e"),w,o,ft=s.indexOf("#"),v,st,b,l,y,nt,tt,f,u,c,e;if(n.indexOf("\\")>-1&&(d=n.substr(0,n.lastIndexOf("\\")+1),n=n.substr(n.lastIndexOf("\\")+1,n.length),ft=n.indexOf("#")),at>-1){for(v="",s="",o=n.toLowerCase().split("e"),lt=n.indexOf("+")>-1?n.split("+")[1]:n.split("-")[1],t=parseInt(t).toExponential(),w=t.split("e"),r=o[1].length-w[1].length,u=o[1].length-1;u>0;u--)o[1][u]!="0"?s+=o[1][u]:r>1?(s+="#",r--):s+="0";for(st=n.indexOf("+")>-1?"+":"",s=st+s.split("").reverse().join(""),u=0;u<w[0].length;u++)v=w[0][u]!="."?v.concat("#"):v.concat(".");v.length>o[0].length&&(v=o[0]);s=d+v+"e"+s}else if(ct){if(o=n.split("."),w=t.split("."),o[1]=o[1].replace(/[,.]/g,""),r=o[0].replace(/[,.]/g,"").length-w[0].replace(/[,.]/g,"").length,r<0&&ej.isNullOrUndefined(n.match(/[\[\(\)\]]/g))){for(a=o[0].split(","),i=o[0].split(","),e=a.length-1;e>=0;e--)if(a[e])for(f=a[e].length,u=0,c=Math.abs(r);u<c;u++){if(f===3)break;i[e]="0"+i[e];f++;r++}if(i=i.join(),r<0)for(ej.isNullOrUndefined(f)||f==3||(i=","+i),u=0,c=Math.abs(r);u<c;u++)f===3&&(i=","+i,f=0),i="0"+i,f++;r=0;s=d+i+"."+o[1]}else if(ej.isNullOrUndefined(n.match(/[\[\(\)\]]/g))){for(i=o[0].replace(/[,.]/g,""),b="",f=0,l=i.length-1;l>=0;l--)f===3?(b=","+b,f=0):f++,b=i[l]+b;s=d+b+"."+o[1]}}else{if(y=0,nt=a.splice(1,a.length),r=n.replace(/[,.\[\(\]\)]/g,"").length-t.replace(/[,.]/g,"").length,ft>-1){for(tt=0,c=n.length;tt<c;tt++)n[tt]==="#"&&y++;(y===1||a[1]&&y===2)&&(ut="#");y===1&&(nt=a[0])}if(r<0){for(o=s.split(","),i=o.splice(1,o.length),e=a.length-1;e>=0;e--)if(nt[e])for(f=nt[e].length,i[e]||(i[e]=""),u=0,c=Math.abs(r)+1;u<c;u++){if(y!=1&&f===3){f=0;break}i[e]=i[e].concat(ut);f++;r++}if(i=i.join(),r<0)for(ej.isNullOrUndefined(f)||f==3||(i=","+i),u=0,c=Math.abs(r)+1;u<c;u++)y!=1&&f===3&&(i=","+i,f=0),i=ut+i,f++;r=0;s=d+i}rt=0}for(var et=[],vt=s.split(""),it=0,l=0,h,g,ot=!1,ht=!1,yt=n.indexOf("\\");l<s.length;l++)if(h=vt[l],h==="e"&&(ht=!0),h==="0"&&ft<0?r>0&&rt<=l?(r--,rt++):r>0?r--:g=k[h]:h=="0"&&(ht||h!="0")||(g=k[h]),h==="0"&&yt>-1&&(g=k[h]),l===s.lastIndexOf("\\")&&(ot=!1),g&&!ot)et[it]={rule:g},it+=1;else for(h==="\\"&&(h="",l===s.lastIndexOf("\\")||(ot=!0)),h=h.split(""),e=0;e<h.length;e++)et[it]=h[e],it+=1;return k=et,{rules:k,format:s}}function st(n,t,i){var u,r,e,o;if(ej.isNullOrUndefined(n)||typeof n=="string"||!t)throw"Bad Number Format Exception";if(o=t,u=ot(t,n),e=u.rules,t=u.format,t.indexOf("\\")>=0){var s=t.lastIndexOf("\\"),l=t.slice(0,s),f=t.slice(s+1,t.length),h;f=f.replace(/[9?CANa#&]/g,"_");h=l+f;r=h.replace(/[\\]/g,"");t=t.replace(/[\\]/g,"")}else r=t.replace(/[9?CANa#&]/g,"_");return r=c(r,i),ht(n,t,r,e,i,o)}function c(n,t){var r,f,e,o,u,i;if(n.length!=0){for(r=ej.preferredCulture(t),u="",f=r.numberFormat[","],e=r.numberFormat.currency.symbol,o=r.numberFormat["."],i=0;i<n.length;i++)u+=n[i]==","?f:n[i]=="."?o:n[i]=="$"?e:n[i];n=u}return n}function ht(i,r,u,f,e,o){var ft,l,nt,it;if(!ej.isNullOrUndefined(i)){r.toLowerCase().indexOf("e")>-1&&(ft=o.indexOf("+")>-1?o.split("+")[1]:o.split("-")[1],i=i.toExponential(),o.indexOf("-")>-1&&(i=i.replace("+","")));var p,ut,k,et,rt=p=ut=i.toString(),s=u,a=k=0,h,g="_",d,v,y,tt,ot=r.match(/[\(\[\]\)]/g);if(rt=!r.indexOf("\\")>=0?i=ut.replace(/[\(\)-]/g,""):et,l=f.length-1,nt=p.length-1,ej.isNullOrUndefined(ot))while(a<f.length){if(h=p[k],d=f[a],h==t)break;if(h===d||h===g||h==="e"&&h===d.toLowerCase()?(h===g?g:"",v=s.substring(0,a),y=s.substring(a),h=c(h,e),s=v+h+y.substr(1,y.length),k+=1,a+=1):f[a].rule!=t?(it=p.charCodeAt(k),w(r,it,a)?(v=s.substring(0,a),y=s.substring(a),tt=b(p,k,a,r,u),s=v+tt+y.substr(1,y.length),a++,k++):a++):(d==="e"&&(k=p.indexOf("e")+1),a++),k>rt.length||l<0)break}else while(l>=0){if(h=p[nt],d=f[l],h==t)break;if(h===d||h===g||h==="e"&&h===d.toLowerCase()?(h===g?g:"",v=s.substring(0,l+1),y=s.substring(l+1),h=c(h,e),s=v.substr(0,v.length-1)+h+y,l--,nt--):f[l].rule!=t?(it=p.charCodeAt(nt),w(r,it,l)?(v=s.substring(0,l+1),y=s.substring(l+1),tt=b(p,nt,l,r,u),s=v.substr(0,v.length-1)+tt+y,l--,nt--):l--):l--,k>rt.length||l<0)break}if(i)return(s.indexOf("_")-s.indexOf(",")==1||s.indexOf("_")-s.indexOf(".")==1)&&(s=s.slice(0,s.indexOf("_")-1)),n.trim(s.replace(/[_]/g,""))==""?null:s.replace(/[_]/g,"")}}function w(t,i,r){var f=p,u=!1,e=t.substr(r,1),o=String.fromCharCode(i);return n.each(f,function(n,t){e==n&&(u=o.match(new RegExp(t))?!0:!1)}),u}function b(n,t,i,r,u){var f=!1;return r.indexOf(".")>-1&&i===u.length-1&&n[t+1]>5&&(f=!0),f?(parseInt(n[t])+1).toString():n[t]}function s(n,t){return n.indexOf(t)===0}function l(n,t){return n.substr(n.length-t.length)===t}function e(n){return(n+"").replace(rt,"")}function ct(n){return isNaN(n)?NaN:Math[n<0?"ceil":"floor"](n)}function h(n,t,i){for(var r=n.length;r<t;r++)n=i?"0"+n:n+"0";return n}function a(n,t,i){var r=t["-"],u=t["+"],f;switch(i){case"n -":r=" "+r;u=" "+u;case"n-":l(n,r)?f=["-",n.substr(0,n.length-r.length)]:l(n,u)&&(f=["+",n.substr(0,n.length-u.length)]);break;case"- n":r+=" ";u+=" ";case"-n":s(n,r)?f=["-",n.substr(r.length)]:s(n,u)&&(f=["+",n.substr(u.length)]);break;case"(n)":s(n,"(")&&l(n,")")&&(f=["-",n.substr(1,n.length-2)])}return f||["",n]}function lt(n,t,i){var l=i.groupSizes||[3],c=l[0],a=1,v=ej._round(n,t),p;isFinite(v)||(v=n);n=v;var r=n+"",u="",e=r.split(/e/i),f=e.length>1?parseInt(e[1],10):0;r=e[0];e=r.split(".");r=e[0];u=e.length>1?e[1]:"";f>0?(u=h(u,f,!1),r+=u.slice(0,f),u=u.substr(f)):f<0&&(f=-f,r=h(r,f+1,!0),u=r.slice(-f,r.length)+u,r=r.slice(0,-f));p=i["."]||".";u=t>0?p+(u.length>t?u.slice(0,t):h(u,t)):"";for(var o=r.length-1,y=i[","]||",",s="";o>=0;){if(c===0||c>o)return r.slice(0,o+1)+(s.length?y+s+u:u);s=r.slice(o-c+1,o+1)+(s.length?y+s:"");o-=c;a<l.length&&(c=l[a],a++)}return r.slice(0,o+1)+y+s+u}function at(n,t,i){var s,r;if(!t||t==="i")return i.name.length?n.toLocaleString():n.toString();t=t||"D";var e=i.numberFormat,u=Math.abs(n),f=-1,o;t.length>1&&(f=parseInt(t.slice(1),10));s=t.charAt(0).toUpperCase();switch(s){case"D":o="n";u=ct(u);f!==-1&&(u=h(""+u,f,!0));n<0&&(u=-u);break;case"N":r=e;r.pattern=r.pattern||["-n"];case"C":r=r||e.currency;r.pattern=r.pattern||["-$n","$n"];case"P":r=r||e.percent;r.pattern=r.pattern||["-n %","n %"];o=n<0?r.pattern[0]||"-n":r.pattern[1]||"n";f===-1&&(f=r.decimals);u=lt(u*(s==="P"?100:1),f,r);break;default:return st(n,t,i)}return vt(u,o,e)}function vt(n,t,i){for(var f=/n|\$|-|%/g,r="",e,u;;){if(e=f.lastIndex,u=f.exec(t),r+=t.slice(e,u?u.index:t.length),!u)break;switch(u[0]){case"n":r+=n;break;case"$":r+=i.currency.symbol||"$";break;case"-":/[1-9]/.test(n)&&(r+=i["-"]||"-");break;case"%":r+=i.percent.symbol||"%"}}return r}function k(n,t,i){var p,o,s,r,w,d,b,k,g,h,nt;typeof i=="string"&&(t=i,i=10);t=ej.globalize.findCulture(t);var c=NaN,u=t.numberFormat,y=t.numberFormat.pattern[0];if(n=n.replace(/ /g,""),n.indexOf(t.numberFormat.currency.symbol)>-1?(n=n.replace(t.numberFormat.currency.symbol||"$",""),n=n.replace(t.numberFormat.currency["."]||".",t.numberFormat["."]||"."),y=e(t.numberFormat.currency.pattern[0].replace("$",""))):n.indexOf(t.numberFormat.percent.symbol)>-1&&(n=n.replace(t.numberFormat.percent.symbol||"%",""),n=n.replace(t.numberFormat.percent["."]||".",t.numberFormat["."]||"."),y=e(t.numberFormat.percent.pattern[0].replace("%",""))),n=e(n),ut.test(n))c=parseFloat(n,"",i);else if(ft.test(n))c=parseInt(n,16);else{var l=a(n,u,y),v=l[0],f=l[1];v===""&&u.pattern[0]!=="-n"&&(l=a(n,u,"-n"),v=l[0],f=l[1]);v=v||"+";s=f.indexOf("e");s<0&&(s=f.indexOf("E"));s<0?(o=f,p=null):(o=f.substr(0,s),p=f.substr(s+1));d=u["."]||".";b=o.indexOf(d);b<0?(r=o,w=null):(r=o.substr(0,b),w=o.substr(b+d.length));k=u[","]||",";r=r.split(k).join("");g=k.replace(/\u00A0/g," ");k!==g&&(r=r.split(g).join(""));h=v+r;w!==null&&(h+="."+w);p!==null&&(nt=a(p,u,y),h+="e"+(nt[0]||"+")+nt[1]);!i&&et.test(h)?c=parseFloat(h):i&&(c=parseInt(h,i))}return c}function r(n,t,i){return n<t||n>i}function yt(n,t){var u=new Date,i,r;return t<100&&(i=n.twoDigitYearMax,i=typeof i=="string"?(new Date).getFullYear()%100+parseInt(i,10):i,r=u.getFullYear(),t+=r-r%100,t>i&&(t-=100)),t}function o(n,t){if(n.indexOf)return n.indexOf(t);for(var i=0,r=n.length;i<r;i++)if(n[i]===t)return i;return-1}function v(n){return n.split(" ").join(" ").toUpperCase()}function f(n){for(var i=[],t=0,r=n.length;t<r;t++)i[t]=v(n[t]);return i}function pt(n,t,i){var r,e=n.days,u=n._upperDays;return u||(n._upperDays=u=[f(e.names),f(e.namesAbbr),f(e.namesShort)]),t=v(t),i?(r=o(u[1],t),r===-1&&(r=o(u[2],t))):r=o(u[0],t),r}function wt(n,t,i){var s=n.months,h=n.monthsGenitive||n.months,r=n._upperMonths,e=n._upperMonthsGen,u;return r||(n._upperMonths=r=[f(s.names),f(s.namesAbbr)],n._upperMonthsGen=e=[f(h.names),f(h.namesAbbr)]),t=v(t),u=o(i?r[1]:r[0],t),u<0&&(u=o(i?e[1]:e[0],t)),u}function y(n,t){for(var r,f=0,i=!1,u=0,e=n.length;u<e;u++)r=n.charAt(u),r=="'"?(i?t.push("'"):f++,i=!1):r=="\\"?(i&&t.push("\\"),i=!i):(t.push(r),i=!1);return f}function bt(n,t,i,r){var s,e;if(!n)return null;var u=0,f=0,o=null;t=t.split("");for(var h=t.length,c=function(n){for(var i=0;t[u]===n;)i++,u++;return i>0&&(u-=1),i},l=function(t){var r=new RegExp("^\\d{1,"+t+"}"),i=n.substr(f,t).match(r);return i?(i=i[0],f+=i.length,parseInt(i,10)):null},a=function(t,i){for(var r=0,s=t.length,e,o,u;r<s;r++)if(e=t[r],o=e.length,u=n.substr(f,o),i&&(u=u.toLowerCase()),u==e)return f+=o,r+1;return null},v=function(n){for(var t=0,r=n.length,i=[];t<r;t++)i[t]=(n[t]+"").toLowerCase();return i},y=function(n){var t={};for(var i in n)t[i]=v(n[i]);return t};u<h;u++)s=t[u],s==="d"&&(e=c("d"),r._lowerDays||(r._lowerDays=y(r.days)),o=e<3?l(2):a(r._lowerDays[e==3?"namesAbbr":"names"],!0));return o}function d(n,t){t=t||"F";var i,u=n.patterns,r=t.length;if(r===1){if(i=u[t],!i)throw"Invalid date format string '"+t+"'.";t=i}else r===2&&t.charAt(0)==="%"&&(t=t.charAt(1));return t}function g(t,u,f){var d,b,at,c,tt,it,h,kt,k,st;t=e(t);u=e(u);var o=f.calendar,ht=ej.globalize._getDateParseRegExp(o,u),ct=new RegExp(ht.regExp).exec(t);if(ct===null){for(formats=[],d=0,isArray=n.isArray,numRegExp=/^(\+|-?)\d+(\.?)\d*$/,formats.push(u),formats=isArray(formats)?formats:[formats],length=formats.length;d<length;d++)if(l=dt(t,formats[d],f),l)return l;return l||null}var lt=ht.groups,p=null,y=null,l=null,rt=null,v=0,w,ut=0,ft=0,et=0,g=null,ot=!1;for(b=0,at=lt.length;b<at;b++)if(c=ct[b+1],c){var vt=lt[b],nt=vt.length,a=parseInt(c,10);switch(vt){case i.DAY_OF_MONTH_DOUBLE_DIGIT:case i.DAY_OF_MONTH_SINGLE_DIGIT:if(l=a,r(l,1,31))return null;break;case i.MONTH_THREE_LETTER:case i.MONTH_FULL_NAME:if(y=wt(o,c,nt===3),r(y,0,11))return null;break;case i.MONTH_SINGLE_DIGIT:case i.MONTH_DOUBLE_DIGIT:if(y=a-1,r(y,0,11))return null;break;case i.YEAR_SINGLE_DIGIT:case i.YEAR_DOUBLE_DIGIT:case i.YEAR_FULL:if(p=nt<4?yt(o,a):a,r(p,0,9999))return null;break;case i.HOURS_SINGLE_DIGIT_12_HOUR_CLOCK:case i.HOURS_DOUBLE_DIGIT_12_HOUR_CLOCK:if(v=a,v===12&&(v=0),r(v,0,11))return null;break;case i.HOURS_SINGLE_DIGIT_24_HOUR_CLOCK:case i.HOURS_DOUBLE_DIGIT_24_HOUR_CLOCK:if(v=a,r(v,0,23))return null;break;case i.MINUTES_SINGLE_DIGIT:case i.MINUTES_DOUBLE_DIGIT:if(ut=a,r(ut,0,59))return null;break;case i.SECONDS_SINGLE_DIGIT:case i.SECONDS_DOUBLE_DIGIT:if(ft=a,r(ft,0,59))return null;break;case i.MERIDIAN_INDICATOR_FULL:case i.MERIDIAN_INDICATOR_SINGLE:if(ot=o.PM&&(c===o.PM[0]||c===o.PM[1]||c===o.PM[2]),!ot&&(!o.AM||c!==o.AM[0]&&c!==o.AM[1]&&c!==o.AM[2]))return null;break;case i.DECISECONDS:case i.CENTISECONDS:case i.MILLISECONDS:if(et=a*Math.pow(10,3-nt),r(et,0,999))return null;break;case i.DAY_OF_WEEK_THREE_LETTER:l=bt(t,u,f,o);break;case i.DAY_OF_WEEK_FULL_NAME:if(pt(o,c,nt===3),r(rt,0,6))return null;break;case i.TIME_ZONE_OFFSET_FULL:if((tt=c.split(/:/),tt.length!==2)||(w=parseInt(tt[0],10),r(w,-12,13))||(it=parseInt(tt[1],10),r(it,0,59)))return null;g=w*60+(s(c,"-")?-it:it);break;case i.TIME_ZONE_OFFSET_SINGLE_DIGIT:case i.TIME_ZONE_OFFSET_DOUBLE_DIGIT:if(w=a,r(w,-12,13))return null;g=w*60}}if(h=new Date,k=o.convert,kt=k?k.fromGregorian(h)[0]:h.getFullYear(),p===null&&(p=kt),y===null&&(y=0),l===null&&(l=1),k){if(h=k.toGregorian(p,y,l),h===null)return null}else if((h.setFullYear(p,y,l),h.getDate()!==l)||rt!==null&&h.getDay()!==rt)return null;return ot&&v<12&&(v+=12),h.setHours(v,ut,ft,et),g!==null&&(st=h.getMinutes()-(g+h.getTimezoneOffset()),h.setHours(h.getHours()+parseInt(st/60,10),st%60)),h}function kt(n){for(var t=0,r=n.length,i=[];t<r;t++)i[t]=(n[t]+"").toLowerCase();return i}function nt(n){var t={};for(var i in n)t[i]=kt(n[i]);return t}function dt(n,i,r){if(!n)return null;var c=function(n){for(var t=0;i[k]===n;)t++,k++;return t>0&&(k-=1),t},y=function(t){var r=numRegExp[t]||new RegExp("^\\d{1,"+t+"}"),i=n.substr(a,t).match(r);return i?(i=i[0],a+=i.length,parseInt(i,10)):null},rt=function(t,i){for(var r=0,o=t.length,f,e,u;r<o;r++)if(f=t[r],e=f.length,u=n.substr(a,e),i&&(u=u.toLowerCase()),u==f)return a+=e,r+1;return null},g=function(){var t=!1;return n.charAt(a)===i[k]&&(a++,t=!0),t},h=r.calendars.standard,o=null,p=null,l=null,e=null,b=null,d=null,w=null,k=0,a=0,st=!1,ht=new Date,ut=h.twoDigitYearMax||2029,tt=ht.getFullYear(),s,f,vt,ct,lt,at,yt,v,ft,et,ot,it,pt;for(i||(i="d"),ct=h.patterns[i],ct&&(i=ct),i=i.split(""),vt=i.length;k<vt;k++)if(s=i[k],st)s==="'"?st=!1:g();else if(s==="d"){if(f=c("d"),h._lowerDays||(h._lowerDays=nt(h.days)),l=f<3?y(2):rt(h._lowerDays[f==3?"namesAbbr":"names"],!0),l===null||u(l,1,31))return null}else if(s==="M"){if(f=c("M"),h._lowerMonths||(h._lowerMonths=nt(h.months)),p=f<3?y(2):rt(h._lowerMonths[f==3?"namesAbbr":"names"],!0),p===null||u(p,1,12))return null;p-=1}else if(s==="y"){if(f=c("y"),o=y(f),o===null)return null;f==2&&(typeof ut=="string"&&(ut=tt+parseInt(ut,10)),o=tt-tt%100+o,o>ut&&(o-=100))}else if(s==="h"){if(c("h"),e=y(2),e==12&&(e=0),e===null||u(e,0,11))return null}else if(s==="H"){if(c("H"),e=y(2),e===null||u(e,0,23))return null}else if(s==="m"){if(c("m"),b=y(2),b===null||u(b,0,59))return null}else if(s==="s"){if(c("s"),d=y(2),d===null||u(d,0,59))return null}else if(s==="f"){if(f=c("f"),w=y(f),w!==null&&f>3&&(w=parseInt(w.toString().substring(0,3),10)),w===null||u(w,0,999))return null}else if(s==="t"){if(f=c("t"),ft=h.AM,et=h.PM,f===1&&(ft=mapDesignators(ft),et=mapDesignators(et)),lt=rt(et),!lt&&!rt(ft))return null}else if(s==="z"){if(at=!0,f=c("z"),n.substr(a,1)==="Z"){if(!yt)return null;g();continue}if((v=n.substr(a,6).match(f>2?longTimeZoneRegExp:shortTimeZoneRegExp),!v)||(v=v[0],a=v.length,v=v.split(":"),ot=parseInt(v[0],10),u(ot,-12,13))||f>2&&(it=parseInt(v[1],10),isNaN(it)||u(it,0,59)))return null}else if(s==="T")yt=g();else if(s==="'")st=!0,g();else if(!g())return null;return(pt=e!==null||b!==null||d||null,o===null&&p===null&&l===null&&pt?(o=tt,p=ht.getMonth(),l=ht.getDate()):(o===null&&(o=tt),l===null&&(l=1)),lt&&e<12&&(e+=12),at?(ot&&(e+=-ot),it&&(b+=-it),n=new Date(Date.UTC(o,p,l,e,b,d,w))):(n=new Date(o,p,l,e,b,d,w),gt(n,e)),o<100&&n.setFullYear(o),n.getDate()!==l&&at===t)?null:n}function u(n,t,i){return!(n>=t&&n<=i)}function gt(n,t){t||n.getHours()!==23||n.setHours(n.getHours()+2)}function tt(n,t,r){function o(n,t){var i,r=n+"";return t>1&&r.length<t?(i=it[t-2]+r,i.substr(i.length-t,t)):r}function ut(){return l||b?l:(l=rt.test(t),b=!0,l)}var e=r.calendar,p=e.convert,u,w,v,f,nt,h;if(!t||!t.length||t==="i")return r&&r.name.length?p?tt(n,e.patterns.F,r):n.toLocaleString():n.toString();w=t==="s";t=d(e,t);u=[];var s,it=["0","00","000"],l,b,rt=/([^d]|^)(d|dd)([^d]|$)/g,k=0,g=/\/|dddd|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yy|y|hh|h|HH|H|mm|m|ss|s|tt|t|fff|ff|f|zzz|zz|z|gg|g/g,c;for(!w&&p&&(c=p.fromGregorian(n));;){var ft=g.lastIndex,a=g.exec(t),et=t.slice(ft,a?a.index:t.length);if(k+=y(et,u),!a)break;if(k%2){u.push(a[0]);continue}v=a[0];f=v.length;switch(v){case i.DAY_OF_WEEK_THREE_LETTER:case i.DAY_OF_WEEK_FULL_NAME:nt=f===3?e.days.namesAbbr:e.days.names;u.push(nt[n.getDay()]);break;case i.DAY_OF_MONTH_SINGLE_DIGIT:case i.DAY_OF_MONTH_DOUBLE_DIGIT:l=!0;u.push(o(c?c[2]:n.getDate(),f));break;case i.MONTH_THREE_LETTER:case i.MONTH_FULL_NAME:h=c?c[1]:n.getMonth();u.push(e.monthsGenitive&&ut()?e.monthsGenitive[f===3?"namesAbbr":"names"][h]:e.months[f===3?"namesAbbr":"names"][h]);break;case i.MONTH_SINGLE_DIGIT:case i.MONTH_DOUBLE_DIGIT:u.push(o((c?c[1]:n.getMonth())+1,f));break;case i.YEAR_SINGLE_DIGIT:case i.YEAR_DOUBLE_DIGIT:case i.YEAR_FULL:h=c?c[0]:n.getFullYear();f<4&&(h=h%100);u.push(o(h,f));break;case i.HOURS_SINGLE_DIGIT_12_HOUR_CLOCK:case i.HOURS_DOUBLE_DIGIT_12_HOUR_CLOCK:s=n.getHours()%12;s===0&&(s=12);u.push(o(s,f));break;case i.HOURS_SINGLE_DIGIT_24_HOUR_CLOCK:case i.HOURS_DOUBLE_DIGIT_24_HOUR_CLOCK:u.push(o(n.getHours(),f));break;case i.MINUTES_SINGLE_DIGIT:case i.MINUTES_DOUBLE_DIGIT:u.push(o(n.getMinutes(),f));break;case i.SECONDS_SINGLE_DIGIT:case i.SECONDS_DOUBLE_DIGIT:u.push(o(n.getSeconds(),f));break;case i.MERIDIAN_INDICATOR_SINGLE:case i.MERIDIAN_INDICATOR_FULL:h=n.getHours()<12?e.AM?e.AM[0]:" ":e.PM?e.PM[0]:" ";u.push(f===1?h.charAt(0):h);break;case i.DECISECONDS:case i.CENTISECONDS:case i.MILLISECONDS:u.push(o(n.getMilliseconds(),3).substr(0,f));break;case i.TIME_ZONE_OFFSET_SINGLE_DIGIT:case i.TIME_ZONE_OFFSET_DOUBLE_DIGIT:s=n.getTimezoneOffset()/60;u.push((s<=0?"+":"-")+o(Math.floor(Math.abs(s)),f));break;case i.TIME_ZONE_OFFSET_FULL:s=n.getTimezoneOffset()/60;u.push((s<=0?"+":"-")+o(Math.floor(Math.abs(s)),2)+":"+o(Math.abs(n.getTimezoneOffset()%60),2));break;case i.DATE_SEPARATOR:u.push(e["/"]||"/");break;default:throw"Invalid date format pattern '"+v+"'.";}}return u.join("")}function it(n,t){return t.length?it(n[t[0]],t.slice(1)):n}var i;ej.globalize={};ej.cultures={};ej.cultures["default"]=ej.cultures["en-US"]=n.extend(!0,{name:"en-US",englishName:"English",nativeName:"English",language:"en",isRTL:!1,numberFormat:{pattern:["-n"],decimals:2,",":",",".":".",groupSizes:[3],"+":"+","-":"-",percent:{pattern:["-n %","n %"],decimals:2,groupSizes:[3],",":",",".":".",symbol:"%"},currency:{pattern:["($n)","$n"],decimals:2,groupSizes:[3],",":",",".":".",symbol:"$"}},calendars:{standard:{"/":"/",":":":",firstDay:0,week:{name:"Week",nameAbbr:"Wek",nameShort:"Wk"},days:{names:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],namesAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],namesShort:["Su","Mo","Tu","We","Th","Fr","Sa"]},months:{names:["January","February","March","April","May","June","July","August","September","October","November","December",""],namesAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec",""]},AM:["AM","am","AM"],PM:["PM","pm","PM"],twoDigitYearMax:2029,patterns:{d:"M/d/yyyy",D:"dddd, MMMM dd, yyyy",t:"h:mm tt",T:"h:mm:ss tt",f:"dddd, MMMM dd, yyyy h:mm tt",F:"dddd, MMMM dd, yyyy h:mm:ss tt",M:"MMMM dd",Y:"yyyy MMMM",S:"yyyy'-'MM'-'dd'T'HH':'mm':'ss"}}}},ej.cultures["en-US"]);ej.cultures["en-US"].calendar=ej.cultures["en-US"].calendar||ej.cultures["en-US"].calendars.standard;var rt=/^\s+|\s+$/g,ut=/^[+-]?infinity$/i,ft=/^0x[a-f0-9]+$/i,et=/^[+-]?\d*\.?\d*(e[+-]?\d+)?$/,p={"9":"[0-9 ]","0":"[0-9 ]",a:"[A-Za-z0-9 ]",A:"[A-Za-z0-9]",N:"[0-9]","#":"[0-9]","&":"[^]+","<":"",">":"",C:"[A-Za-z ]","?":"[A-Za-z]"};i={DAY_OF_WEEK_THREE_LETTER:"ddd",DAY_OF_WEEK_FULL_NAME:"dddd",DAY_OF_MONTH_SINGLE_DIGIT:"d",DAY_OF_MONTH_DOUBLE_DIGIT:"dd",MONTH_THREE_LETTER:"MMM",MONTH_FULL_NAME:"MMMM",MONTH_SINGLE_DIGIT:"M",MONTH_DOUBLE_DIGIT:"MM",YEAR_SINGLE_DIGIT:"y",YEAR_DOUBLE_DIGIT:"yy",YEAR_FULL:"yyyy",HOURS_SINGLE_DIGIT_12_HOUR_CLOCK:"h",HOURS_DOUBLE_DIGIT_12_HOUR_CLOCK:"hh",HOURS_SINGLE_DIGIT_24_HOUR_CLOCK:"H",HOURS_DOUBLE_DIGIT_24_HOUR_CLOCK:"HH",MINUTES_SINGLE_DIGIT:"m",MINUTES_DOUBLE_DIGIT:"mm",SECONDS_SINGLE_DIGIT:"s",SECONDS_DOUBLE_DIGIT:"ss",MERIDIAN_INDICATOR_SINGLE:"t",MERIDIAN_INDICATOR_FULL:"tt",DECISECONDS:"f",CENTISECONDS:"ff",MILLISECONDS:"fff",TIME_ZONE_OFFSET_SINGLE_DIGIT:"z",TIME_ZONE_OFFSET_DOUBLE_DIGIT:"zz",TIME_ZONE_OFFSET_FULL:"zzz",DATE_SEPARATOR:"/"};ej.globalize._getDateParseRegExp=function(n,t){var e=n._parseRegExp,s,p,o,w,r,b,k;if(e){if(s=e[t],s)return s}else n._parseRegExp=e={};for(var h=d(n,t).replace(/([\^\$\.\*\+\?\|\[\]\(\)\{\}])/g,"\\\\$1"),u=["^"],l=[],c=0,a=0,v=/\/|dddd|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yy|y|hh|h|HH|H|mm|m|ss|s|tt|t|fff|ff|f|zzz|zz|z|gg|g/g,f;(f=v.exec(h))!==null;){if(p=h.slice(c,f.index),c=v.lastIndex,a+=y(p,u),a%2){u.push(f[0]);continue}o=f[0];w=o.length;switch(o){case i.DAY_OF_WEEK_THREE_LETTER:case i.DAY_OF_WEEK_FULL_NAME:case i.MONTH_FULL_NAME:case i.MONTH_THREE_LETTER:r="(\\D+)";break;case i.MERIDIAN_INDICATOR_FULL:case i.MERIDIAN_INDICATOR_SINGLE:r="(\\D*)";break;case i.YEAR_FULL:case i.MILLISECONDS:case i.CENTISECONDS:case i.DECISECONDS:r="(\\d{"+w+"})";break;case i.DAY_OF_MONTH_DOUBLE_DIGIT:case i.DAY_OF_MONTH_SINGLE_DIGIT:case i.MONTH_DOUBLE_DIGIT:case i.MONTH_SINGLE_DIGIT:case i.YEAR_DOUBLE_DIGIT:case i.YEAR_SINGLE_DIGIT:case i.HOURS_DOUBLE_DIGIT_24_HOUR_CLOCK:case i.HOURS_SINGLE_DIGIT_24_HOUR_CLOCK:case i.HOURS_DOUBLE_DIGIT_12_HOUR_CLOCK:case i.HOURS_SINGLE_DIGIT_12_HOUR_CLOCK:case i.MINUTES_DOUBLE_DIGIT:case i.MINUTES_SINGLE_DIGIT:case i.SECONDS_DOUBLE_DIGIT:case i.SECONDS_SINGLE_DIGIT:r="(\\d\\d?)";break;case i.TIME_ZONE_OFFSET_FULL:r="([+-]?\\d\\d?:\\d{2})";break;case i.TIME_ZONE_OFFSET_DOUBLE_DIGIT:case i.TIME_ZONE_OFFSET_SINGLE_DIGIT:r="([+-]?\\d\\d?)";break;case i.DATE_SEPARATOR:r="(\\"+n["/"]+")";break;default:throw"Invalid date format pattern '"+o+"'.";}r&&u.push(r);l.push(f[0])}return y(h.slice(c),u),u.push("$"),b=u.join("").replace(/\s+/g,"\\s+"),k={regExp:b,groups:l},e[t]=k};ej.globalize.addCulture=function(t,i){ej.cultures[t]=n.extend(!0,n.extend(!0,{},ej.cultures["default"],i),ej.cultures[t]);ej.cultures[t].calendar=ej.cultures[t].calendars.standard};ej.globalize.preferredCulture=function(n){return n=typeof n!="undefined"&&typeof n==typeof this.cultureObject?n.name:n,this.cultureObject=ej.globalize.findCulture(n),this.cultureObject};ej.globalize.setCulture=function(n){return ej.isNullOrUndefined(this.globalCultureObject)&&(this.globalCultureObject=ej.globalize.findCulture(n)),n=typeof n!="undefined"&&typeof n==typeof this.globalCultureObject?n.name:n,n&&(this.globalCultureObject=ej.globalize.findCulture(n)),ej.cultures.current=this.globalCultureObject,this.globalCultureObject};ej.globalize.culture=function(n){ej.cultures.current=ej.globalize.findCulture(n)};ej.globalize.findCulture=function(t){var f,i,e,u,r,o;if(t){if(n.isPlainObject(t)&&t.numberFormat&&(f=t),typeof t=="string"){if(i=ej.cultures,i[t])return i[t];if(t.indexOf("-")>-1){if(e=t.split("-")[0],i[e])return i[e]}else for(u=n.map(i,function(n){return n}),r=0;r<u.length;r++)if(o=u[r].name.split("-")[0],o===t)return u[r];return ej.cultures["default"]}}else f=ej.cultures.current||ej.cultures["default"];return f};ej.globalize.format=function(n,t,i){var r=ej.globalize.findCulture(i);return typeof n=="number"?n=at(n,t,r):n instanceof Date&&(n=tt(n,t,r)),n};ej.globalize._round=function(n,t){var i=Math.pow(10,t);return Math.round(n*i)/i};ej.globalize.parseInt=function(n,t,i){return t||(t=10),Math.floor(k(n,i,t))};ej.globalize.getISODate=function(n){if(n instanceof Date)return n.toISOString()};ej.globalize.parseFloat=function(n,t,i){return typeof t=="string"&&(i=t,t=10),k(n,i)};ej.globalize.parseDate=function(n,t,i){var r,o,f,u,s,e;if(i=ej.globalize.findCulture(i),t){if(typeof t=="string"&&(t=[t]),t.length)for(u=0,s=t.length;u<s;u++)if(e=t[u],e&&(r=g(n,e,i),r))break}else{f=i.calendar.patterns;for(o in f)if(r=g(n,f[o],i),r)break}return r||null};ej.globalize.getLocalizedConstants=function(t,i){var r,u=t.replace("ej.","").split(".");return r=it(ej,u),n.extend(!0,{},r.Locale["default"],r.Locale[i?i:this.cultureObject.name])};n.extend(ej,ej.globalize)})(jQuery)});
