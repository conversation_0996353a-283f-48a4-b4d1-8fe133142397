﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Admin.Data.Model;

[Index("ClinicSupervisorId", Name = "ClinicSupervisorIdIndex")]
[Index("DiagnosticianId", Name = "DiagnosticianIdIndex")]
[Index("Email", "Phone1", "Mobile1", Name = "EmailPhoneMobileIndex")]
[Index("FirstName", "LastName", Name = "FirstLastNameIndex")]
[Index("TherapistId", Name = "TherapistIdIndex")]
[Index("ContactCode", Name = "UniqueContactCode2", IsUnique = true)]
public partial class Contact
{
    [Key]
    public long ContactId { get; set; }

    public long TenantId { get; set; }

    public long? TherapistId { get; set; }

    public long? DiagnosticianId { get; set; }

    public long? ClinicSupervisorId { get; set; }

    [StringLength(20)]
    public string ContactCode { get; set; }

    public long? AppointmentCategoryId { get; set; }

    public bool Active { get; set; }

    [Required]
    [StringLength(40)]
    public string InactiveReason { get; set; }

    public bool Waiting { get; set; }

    [Column(TypeName = "date")]
    public DateTime? PsychotherapyStartDate { get; set; }

    [Required]
    [StringLength(40)]
    public string ReferralToAnotherSpecialist { get; set; }

    [Required]
    [StringLength(500)]
    public string ReferralToAnotherSpecialistComment { get; set; }

    [Required]
    [StringLength(50)]
    public string FirstName { get; set; }

    [Required]
    [StringLength(50)]
    public string LastName { get; set; }

    [Required]
    [StringLength(50)]
    public string FatherName { get; set; }

    [Required]
    [StringLength(50)]
    public string Email { get; set; }

    [Required]
    [StringLength(50)]
    public string Email2 { get; set; }

    [Required]
    [StringLength(20)]
    public string Phone1 { get; set; }

    [Required]
    [StringLength(20)]
    public string Mobile1 { get; set; }

    [Required]
    [StringLength(50)]
    public string EmergencyContactName { get; set; }

    [Required]
    [StringLength(50)]
    public string EmergencyPhone { get; set; }

    /// <summary>
    /// Τόπος Κατοικίας
    /// </summary>
    [Required]
    [StringLength(500)]
    public string Residence { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? BirthDate { get; set; }

    [Required]
    [StringLength(50)]
    public string BirthPlace { get; set; }

    [Required]
    [StringLength(20)]
    public string Sex { get; set; }

    [Required]
    [StringLength(500)]
    public string SexComment { get; set; }

    [Required]
    [StringLength(10)]
    public string PoliceIdentity { get; set; }

    [Required]
    [Column("AMA")]
    [StringLength(20)]
    public string Ama { get; set; }

    [Required]
    [Column("AMKA")]
    [StringLength(20)]
    public string Amka { get; set; }

    public byte[] Photo { get; set; }

    [Required]
    [Column("AFM")]
    [StringLength(20)]
    public string Afm { get; set; }

    [Required]
    [Column("DOY")]
    [StringLength(50)]
    public string Doy { get; set; }

    /// <summary>
    /// ΠΡΟΕΛΕΥΣΗ ΣΥΝΕΔΡΙΑΣ
    /// 
    /// DropDown:
    /// ΠΑΡΑΠΟΜΠΗ
    /// INTERNET/SITE
    /// ΠΡΟΩΘΗΤΙΚΗ ΕΝΕΡΓΕΙΑ
    /// ΠΡΟΩΘΗΤΙΚΗ ΠΛΑΤΦΟΡΜΑ
    /// ΓΝΩΣΤΟΣ ΠΕΛΑΤΗ
    /// ΆΛΛΟ
    /// </summary>
    [Required]
    [StringLength(20)]
    public string SessionOrigin { get; set; }

    [Required]
    [StringLength(40)]
    public string Occupation { get; set; }

    [Required]
    [StringLength(40)]
    public string EducationLevel { get; set; }

    [Required]
    [StringLength(40)]
    public string MaritalStatus { get; set; }

    /// <summary>
    /// Τόπος Κατοικίας
    /// </summary>
    [Required]
    [StringLength(500)]
    public string Children { get; set; }

    /// <summary>
    /// Τόπος Κατοικίας
    /// </summary>
    [Required]
    [StringLength(500)]
    public string ChildrenAge { get; set; }

    [Required]
    [StringLength(40)]
    public string LivingStatus { get; set; }

    [Required]
    [StringLength(40)]
    public string EconomicStatus { get; set; }

    [Required]
    [StringLength(40)]
    public string CommunicationMethod { get; set; }

    [Required]
    [StringLength(40)]
    public string SessionFrequency { get; set; }

    [Required]
    [StringLength(40)]
    public string EyeContact { get; set; }

    [Required]
    [StringLength(40)]
    public string BodyLanguage { get; set; }

    [Required]
    [StringLength(40)]
    public string VoiceTone { get; set; }

    [Required]
    [StringLength(40)]
    public string Narration { get; set; }

    [Required]
    [StringLength(40)]
    public string SexualOrientation { get; set; }

    [Required]
    [StringLength(40)]
    public string GeneralRequest { get; set; }

    [Required]
    [StringLength(500)]
    public string SpecialRequest { get; set; }

    [Required]
    [StringLength(500)]
    public string HealthHistory { get; set; }

    [Required]
    [StringLength(500)]
    public string Medication { get; set; }

    [Required]
    [StringLength(40)]
    public string HealingExperience { get; set; }

    [Required]
    [StringLength(500)]
    public string OtherActivities { get; set; }

    [Required]
    [StringLength(500)]
    public string TherapistFirstView { get; set; }

    [Required]
    [StringLength(40)]
    public string AppointmentsStart { get; set; }

    [Required]
    [StringLength(40)]
    public string AppointmentsFrequency { get; set; }

    [Required]
    [StringLength(40)]
    public string InterventionModel { get; set; }

    [Column(TypeName = "date")]
    public DateTime? LastMedicalCheckupDate { get; set; }

    [Required]
    [StringLength(50)]
    public string LastMedicalCheckup { get; set; }

    [Required]
    [StringLength(40)]
    public string OtherImportantFromMotherFamily { get; set; }

    [Required]
    [StringLength(500)]
    public string OtherImportantFromMotherFamilyTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string OtherImportantFromMotherFamilySupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string MotherCharacteristics { get; set; }

    [Required]
    [StringLength(500)]
    public string MotherCharacteristicsTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string MotherCharacteristicsSupervisorComment { get; set; }

    [Required]
    [StringLength(500)]
    public string MotherInfo { get; set; }

    [Required]
    [StringLength(500)]
    public string MotherInfoTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string MotherInfoSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string FatherCharacteristics { get; set; }

    [Required]
    [StringLength(500)]
    public string FatherCharacteristicsTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string FatherCharacteristicsSupervisorComment { get; set; }

    [Required]
    [StringLength(500)]
    public string FatherInfo { get; set; }

    [Required]
    [StringLength(500)]
    public string FatherInfoTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string FatherInfoSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string OtherImportantFromFatherFamily { get; set; }

    [Required]
    [StringLength(500)]
    public string OtherImportantFromFatherFamilyTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string OtherImportantFromFatherFamilySupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string MotherFamilyHistory { get; set; }

    [Required]
    [StringLength(500)]
    public string MotherFamilyHistoryTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string MotherFamilyHistorySupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string FatherFamilyHistory { get; set; }

    [Required]
    [StringLength(500)]
    public string FatherFamilyHistoryTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string FatherFamilyHistorySupervisorComment { get; set; }

    [Required]
    [StringLength(500)]
    public string FamilyMedicalHistory { get; set; }

    [Required]
    [StringLength(500)]
    public string FamilyMedicalHistoryTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string FamilyMedicalHistorySupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string OtherImportant { get; set; }

    [Required]
    [StringLength(500)]
    public string OtherImportantTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string OtherImportantSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string MotherFeedbackInMySuccess { get; set; }

    [Required]
    [StringLength(500)]
    public string MotherFeedbackInMySuccessTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string MotherFeedbackInMySuccessSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string MotherFeedbackInMyFailure { get; set; }

    [Required]
    [StringLength(500)]
    public string MotherFeedbackInMyFailureTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string MotherFeedbackInMyFailureSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string FatherFeedbackInMySuccess { get; set; }

    [Required]
    [StringLength(500)]
    public string FatherFeedbackInMySuccessTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string FatherFeedbackInMySuccessSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string FatherFeedbackInMyFailure { get; set; }

    [Required]
    [StringLength(500)]
    public string FatherFeedbackInMyFailureTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string FatherFeedbackInMyFailureSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string ImportantFeedbackInMySuccess { get; set; }

    [Required]
    [StringLength(500)]
    public string ImportantFeedbackInMySuccessTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string ImportantFeedbackInMySuccessSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string ImportantFeedbackInMyFailure { get; set; }

    [Required]
    [StringLength(500)]
    public string ImportantFeedbackInMyFailureTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string ImportantFeedbackInMyFailureSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string AdhesionType { get; set; }

    [Required]
    [StringLength(500)]
    public string AdhesionTypeTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string AdhesionTypeSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string PreschoolExperiences { get; set; }

    [Required]
    [StringLength(500)]
    public string PreschoolExperiencesTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string PreschoolExperiencesSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string SchoolExperiences { get; set; }

    [Required]
    [StringLength(500)]
    public string SchoolExperiencesTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string SchoolExperiencesSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string TeenageExperiences { get; set; }

    [Required]
    [StringLength(500)]
    public string TeenageExperiencesTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string TeenageExperiencesSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string AdultExperiences { get; set; }

    [Required]
    [StringLength(500)]
    public string AdultExperiencesTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string AdultExperiencesSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string WorkExperiences { get; set; }

    [Required]
    [StringLength(500)]
    public string WorkExperiencesTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string WorkExperiencesSupervisorComment { get; set; }

    [Required]
    [StringLength(500)]
    public string GeneralTraumaHistory { get; set; }

    [Required]
    [StringLength(500)]
    public string GeneralTraumaHistoryTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string GeneralTraumaHistorySupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string SpecificTraumaHistory { get; set; }

    [Required]
    [StringLength(500)]
    public string SpecificTraumaHistoryTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string SpecificTraumaHistorySupervisorComment { get; set; }

    [Required]
    [StringLength(500)]
    public string GeneralBiographicalInfo { get; set; }

    [Required]
    [StringLength(500)]
    public string GeneralBiographicalInfoTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string GeneralBiographicalInfoSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string Developmental { get; set; }

    [Required]
    [StringLength(500)]
    public string DevelopmentalTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string DevelopmentalSupervisorComment { get; set; }

    [Required]
    [StringLength(500)]
    public string EmotionalDifficulties { get; set; }

    [Required]
    [StringLength(500)]
    public string EmotionalDifficultiesTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string EmotionalDifficultiesSupervisorComment { get; set; }

    [Required]
    [StringLength(500)]
    public string EmotionalRemarks { get; set; }

    [Required]
    [StringLength(500)]
    public string EmotionalRemarksTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string EmotionalRemarksSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string EatingDisorder { get; set; }

    [Required]
    [StringLength(500)]
    public string EatingDisorderTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string EatingDisorderSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string Moods { get; set; }

    [Required]
    [StringLength(500)]
    public string MoodsTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string MoodsSupervisorComment { get; set; }

    [Required]
    [StringLength(500)]
    public string OtherMoodObservations { get; set; }

    [Required]
    [StringLength(500)]
    public string OtherMoodObservationsTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string OtherMoodObservationsSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string Anxiety { get; set; }

    [Required]
    [StringLength(500)]
    public string AnxietyTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string AnxietySupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string Sleep { get; set; }

    [Required]
    [StringLength(500)]
    public string SleepTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string SleepSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string Psychotic { get; set; }

    [Required]
    [StringLength(500)]
    public string PsychoticTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string PsychoticSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string Paranoid { get; set; }

    [Required]
    [StringLength(500)]
    public string ParanoidTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string ParanoidSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string Schizoid { get; set; }

    [Required]
    [StringLength(500)]
    public string SchizoidTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string SchizoidSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string Schizotype { get; set; }

    [Required]
    [StringLength(500)]
    public string SchizotypeTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string SchizotypeSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string OnLimit { get; set; }

    [Required]
    [StringLength(500)]
    public string OnLimitTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string OnLimitSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string Antisocial { get; set; }

    [Required]
    [StringLength(500)]
    public string AntisocialTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string AntisocialSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string Histronic { get; set; }

    [Required]
    [StringLength(500)]
    public string HistronicTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string HistronicSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string Narcissistic { get; set; }

    [Required]
    [StringLength(500)]
    public string NarcissisticTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string NarcissisticSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string Ideopsychocompression { get; set; }

    [Required]
    [StringLength(500)]
    public string IdeopsychocompressionTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string IdeopsychocompressionSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string Avoidable { get; set; }

    [Required]
    [StringLength(500)]
    public string AvoidableTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string AvoidableSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string Addictive { get; set; }

    [Required]
    [StringLength(500)]
    public string AddictiveTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string AddictiveSupervisorComment { get; set; }

    [Required]
    [StringLength(40)]
    public string PassiveAggressive { get; set; }

    [Required]
    [StringLength(500)]
    public string PassiveAggressiveTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string PassiveAggressiveSupervisorComment { get; set; }

    [Required]
    [StringLength(500)]
    public string OtherDisorderInfo { get; set; }

    [Required]
    [StringLength(500)]
    public string OtherDisorderInfoTherapistComment { get; set; }

    [Required]
    [StringLength(500)]
    public string OtherDisorderInfoSupervisorComment { get; set; }

    [Required]
    public string Notes { get; set; }

    [Required]
    public string CaseFormulation { get; set; }

    [Required]
    [StringLength(40)]
    public string AccessLevel { get; set; }

    [Required]
    [StringLength(40)]
    public string AppointmentsState { get; set; }

    [InverseProperty("Contact")]
    public virtual ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();
}