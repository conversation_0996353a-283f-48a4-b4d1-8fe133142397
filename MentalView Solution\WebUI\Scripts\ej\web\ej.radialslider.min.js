/*!
*  filename: ej.radialslider.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.globalize.min","./../common/ej.core.min","./../common/ej.touch.min"],n):n()})(function(){(function(n,t){t.widget("ejRadialSliderBase","ej.RadialSliderBase",{defaults:{radius:200,ticks:[0,10,20,30,40,50,60,70,80,90,100],enableRoundOff:!0,value:10,autoOpen:!0,enableAnimation:!0,cssClass:null,labelSpace:30,stop:null,slide:null,start:null,change:null,create:null,destroy:null},dataTypes:{radius:"number",enableRoundOff:"boolean",enableAnimation:"boolean",cssClass:"string"},observables:["value"],observableArray:["ticks"],value:t.util.valueFunction("value"),ticks:t.util.valueFunction("ticks"),_outerTextCalculation:function(n,t,i){var e=this._isMobile()?this.model.radius-this.model.labelSpace:this.model.radius+this.model.labelSpace,f,s,o,r,u;for(this._point=(i-t)/(n-1),i=t+(i-t)/(n-1),f=i,s=f,this._textPoints=[],o=0;o<n;o++)r={},u=t,t=t*Math.PI/180,i=i*Math.PI/180,r.X2=this._startXY+e*Math.cos(t),r.Y2=this._startXY+e*Math.sin(t),r.textAlignment=u<=270&&90<=u?"middle":"start",t=f,i=f+this._point,f+=this._point,this._textPoints.push(r);return r={},u=this._startValueAngle,this._startValueAngle=this._startValueAngle*Math.PI/180,r.X2=this._startXY+e*Math.cos(this._startValueAngle),r.Y2=this._startXY+e*Math.sin(this._startValueAngle),r.textAlignment=u<=270&&90<=u?"middle":"start",this._textPoints.push(r),this._textPoints},_polarToCartesian:function(n,t,i,r,u){var f=r*Math.PI/180;return{x:n+i*Math.cos(f),y:t+i*Math.sin(u?-f:f)}},_tapHandlerEvent:function(i){var f,s,h=n("#"+this._prefix+this._elementID+"-radial-slider-svg").offset(),c=i.clientY,l=i.clientX,a=h.top+this._radialWidth/2-n(window).scrollTop(),v=h.left+this._radialWidth/2,e,o,y,u,r,p;if(this._dynamicAngleCalculation(v,l,a,c),e=this._isMobile()&&this.model.renderMode=="ios7"?6.5:this._isMobile()&&this.model.renderMode=="windows"?2.5:5,!this._isMobile()&&this._angle>=360-this._endAngle&&this._angle<=360-this._startAngle||this._isMobile()&&this._angle>=360-(this._endAngle-e)&&this._angle<=360-(this._startAngle+e)||this._isMobile()&&this.model.position.charAt(0).toLowerCase()=="l"&&(this._angle>270+e||this._angle<90-e)){if(this._lineAngleCalculation(!0),this._previousAngle=this._angle,this._isMobile()||(n(this._overLine).remove(),this._pathBeforeAddlength=this._tickCount+1,this._pathAfterAddLength=this._directionLine.toString().replace(/[^M]/g,"").length,this._pathBeforeAddlength<this._pathAfterAddLength?(o=this._isTapSelected?2:1,this._directionLine.remove(this._tickCount,o,o)):this._directionLine.remove(this._tickCount,1),this._dynamicLineCalculation(this._angle,!1,!0,!0,this._tickCount,this._tickCount+1,!0),n(this._pathLineElement).attr("d",this._directionLine.toString())),n(this._textGroupElement).find("[id="+this._prefix+this._elementID+"-dynamic-text]").length>0&&this._textGroupElement.find("[id="+this._prefix+this._elementID+"-dynamic-text]").remove(),y=this._selectPart(),u=y.select,this._isTicksControl()){if(r=this._ticksCalculation(),this._isMobile()&&this.model.position.charAt(0).toLowerCase()=="l"?this.line.textAlignment="end":this._isMobile()&&this.model.position.charAt(0).toLowerCase()=="r"?this.line.textAlignment="start":this._isMobile()&&(this.model.position.charAt(0).toLowerCase()=="b"||this.model.position.charAt(0).toLowerCase()=="t")&&(this.line.textAlignment="middle"),(this._isMobile()&&this.ticks().indexOf(r)==0||this.ticks().indexOf(r)==this._tickCount-1)&&(this.line.textAlignment="middle"),this.ticks().indexOf(r)<0){f=r.toString().split(".");p=t.preferredCulture(this.model.locale).numberFormat["."];f=t.isNullOrUndefined(f[1])?f[0]:f[0]+p+f[1];s=f;this._outerTextElement=this._createSVGElements("text",{"stroke-width":.5,x:this.line.X2,y:this.line.Y2,"class":this._prefix+"dynamic-text",id:this._prefix+this._elementID+"-dynamic-text",textContent:s,"text-anchor":this.line.textAlignment});this._textGroupElement.append(this._outerTextElement);var w=document.getElementById(this._prefix+this._elementID+"-dynamic-text").getBoundingClientRect(),v=this._textPoints[u.toFixed()].X2,a=this._textPoints[u.toFixed()].Y2,l=this.line.X2,c=this.line.Y2,nt=Math.sqrt(Math.pow(l-v,2)+Math.pow(c-a,2));if(nt<w.width*72/96){this.line={};var b=r.toString().length,k=w.width*72/96-(this._isMobile()?b*2:b*4),tt=parseFloat(u.toFixed(3).substr(0,1))==u.toFixed()?this._degPoint[u.toFixed()]+k:this._degPoint[u.toFixed()]-k,d=(360-tt)*(Math.PI/180),g=this._isMobile()?this.model.radius-this.model.labelSpace:this.model.radius+this.model.labelSpace;this.line.X2=this._startXY+g*Math.cos(d);this.line.Y2=this._startXY+g*Math.sin(-d);n("#"+this._prefix+this._elementID+"-dynamic-text").attr({x:this.line.X2,y:this.line.Y2})}}}else r=this.ticks()[u.toFixed()];this._trigger("change",{value:r,oldValue:this.value()});this._needleStop&&this.model.stop&&this._trigger("stop",{value:r});this.value(r);this._needleStop=!0;this._needleMove=!1;this._isMobile()&&(this.model.renderMode=="windows"||this.model.renderMode=="flat"?this._dynamicWindowsRadial():this._dynamicIOSandAndroidRadial())}},_selectPart:function(){var t=this._dynamicAngle,i=this._startAngle,u=this._endAngle,n;this._isMobile()&&(n=this.model.renderMode=="ios7"?5:this.model.renderMode=="windows"?2.5:5,i=this._startAngle+n,u=this._endAngle-n,this.model.position.charAt(0).toLowerCase()=="l"&&this._dynamicAngle<180&&(t=this._dynamicAngle+360));var r=(t-i)/((u-i)/(this.ticks().length-1)),f=this.ticks()[parseInt(r)],e=this.ticks()[parseInt(r)+1],o=e-f;return{select:r,firstValue:f,space:n,difference:o,dynamicAngle:t}},_lineAngleCalculation:function(n){var t=this._selectPart(),i,f,r,u;this._degPoint.splice(this.ticks().length,this.ticks().length+1);this._degPoint.push(this._degPoint[this._degPoint.length-1]+this._point);i=(this._degPoint[parseInt(t.select)+1]-this._degPoint[parseInt(t.select)]-(this._degPoint[parseInt(t.select)+1]-t.dynamicAngle))/(this._point/t.difference);f=parseInt(t.select)!=0?this._degPoint[parseInt(t.select)]:this._degPoint[parseInt(t.select)];this.model.enableRoundOff&&n?(u=parseFloat(i.toFixed(2)),i=parseInt(i.toFixed()),r=t.difference==.5&&u>=.25?this._point*1:this._point/t.difference*i):r=this._point/t.difference*parseFloat(i.toFixed(2));this._angle=360-this._degPoint[parseInt(t.select)]-Math.abs(r)},_isTicksControl:function(){var t=this._selectPart(),i,r;return this._degPoint.splice(this.ticks().length,this.ticks().length+1),this._degPoint.push(this._degPoint[this._degPoint.length-1]+this._point),i=(this._degPoint[parseInt(t.select)+1]-this._degPoint[parseInt(t.select)]-(this._degPoint[parseInt(t.select)+1]-this._dynamicAngle))/(this._point/t.difference),r=this.model.enableRoundOff?parseInt(i.toFixed())==0||parseInt(i.toFixed())==t.difference?!1:!0:n.inArray(this._angle,this._degPoint)>-1?!1:!0,r},_ticksCalculation:function(){var t=this._selectPart(),r=t.select,s=t.dynamicAngle,h=t.difference,e=t.firstValue,l=t.space,e=this.ticks()[parseInt(r)],u,f,o,n,i,c;return this.line={},u=this._angle*(Math.PI/180),f=this._isMobile()?this.model.radius-this.model.labelSpace:this.model.radius+this.model.labelSpace,this.line.X2=this._startXY+f*Math.cos(u),this.line.Y2=this._startXY+f*Math.sin(-u),this.line.textAlignment=this._angle<=270&&90<=this._angle?"middle":"start",o=parseInt(r)!=0?this._degPoint[parseInt(r)]:this._degPoint[parseInt(r)],n=(s-o)/(this._point/h),this.model.enableRoundOff?(c=parseFloat(n.toFixed(1).substr(1,3)),n=parseInt(n.toFixed()),i=e+Math.abs(n)):(i=e+Math.abs(parseFloat(n.toFixed(2))),i=parseFloat(i.toFixed(2))),i},_dynamicAngleCalculation:function(n,t,i,r){var u=Math.atan2(r-i,t-n);this._angle=(360-u*180/Math.PI)%360;this._dynamicAngle=(360+u*180/Math.PI)%360},_createSVGElements:function(t,i){var r=document.createElementNS(this._svgLink,t);return n.each(i,function(n,t){n=="xlink:href"&&r.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",t);n!="textContent"?r.setAttribute(n,t):r.textContent=t}),r},show:function(){this.model.enableAnimation&&(this.element.removeClass(this._prefix+"slider-hide").addClass(this._prefix+"slider-show"),this._radialSVG.attr("class","").attr("class",this._prefix+"radialslider-svg-show "+this._prefix+"rs-svg"));this.element.css("display","block");this.model.autoOpen=!0},hide:function(){var n=this;this.model.enableAnimation?(this.element.removeClass(this._prefix+"slider-show").addClass(this._prefix+"slider-hide"),this._radialSVG.attr("class","").attr("class",this._prefix+"radialslider-svg-hide "+this._prefix+"rs-svg"),this.model.autoOpen?setTimeout(function(){n.element.css("display","none")},this._isMobile?150:400):n.element.css("display","none")):n.element.css("display","none");this.model.autoOpen=!1},_setModel:function(n){t.isNullOrUndefined(n.inline)||n.inline||(this.model.radius+=50);n.ticks&&(this.model.ticks=n.ticks);n.locale&&(this.model.locale=n.locale);n.enableRoundOff&&this.model.value(Math.round(t.util.getVal(this.model.value)));this._refresh()},_clearElement:function(){this.element.removeAttr("class");this.element.html(this._orgEle.html())},_destroy:function(){this._wireEvents(!0);this._clearElement()},_isMobile:function(){return this._prefix=="e-m-"?!0:!1}})})(jQuery,Syncfusion),function(n,t,i){t.widget("ejRadialSlider","ej.RadialSlider",{_setFirst:!0,validTags:["div"],_rootCSS:"e-radialslider",defaults:{mouseover:null,strokeWidth:2,inline:!1,endAngle:360,startAngle:0,innerCircleImageClass:null,innerCircleImageUrl:null,showInnerCircle:!0,inline:!1,locale:"en-US"},dataTypes:{innerCircleImageClass:"string",innerCircleImageUrl:"string",showInnerCircle:"boolean",inline:"boolean",strokeWidth:"number",endAngle:"number",startAngle:"number",locale:"string"},_init:function(){this._orgEle=this.element.clone();this._renderEJControl()},_renderEJControl:function(){this._prefix="e-";this._directionLine=this._getStringBuilder();this._initialization();this.model.locale=t.preferredCulture(this.model.locale).name=="en"?"en-US":t.preferredCulture(this.model.locale).name;this._localizedLabels=this._getLocalizedLabels();this._setLocaleCulture(this._localizedLabels);this.culture=t.preferredCulture(this.model.locale);this._renderControl();this._wireEvents(!1)},_initialization:function(){this.element.addClass(this.model.cssClass);this._svgLink="http://www.w3.org/2000/svg";this._startXY=this.model.radius-(this.model.inline?50:0);this._startAngle=this.model.startAngle;this._endAngle=this.model.endAngle;this._diameter=2*this.model.radius;this._elementID=this.element.attr("id");this.model.radius=this._startXY;this._tickCount=this.ticks().length;this._labelSpacing=this.model.inline?0:200;this._radialWidth=this._diameter+this._labelSpacing;this.element.css({width:this._diameter+this._labelSpacing,height:this._diameter+this._labelSpacing});this._radialSliderWrapper=t.buildTag("div",{},{},{"class":this._prefix+"radail-slider-wrapper"}).css({width:this._radialWidth,height:this._radialWidth});this.model.showInnerCircle&&(this._innerCircle=t.buildTag("div",{},{},{"class":this._prefix+"inner-circle"}).css({left:this._radialWidth/2-(20+this.model.strokeWidth+1),top:this._radialWidth/2-(20+this.model.strokeWidth+1),"border-width":this.model.strokeWidth+1}),this.model.innerCircleImageClass?this._innerCircle.addClass(this.model.innerCircleImageClass):this._innerCircle.css({"background-image":"url('"+this.model.innerCircleImageUrl+"')"}),this._radialSliderWrapper.append(this._innerCircle));this.element.append(this._radialSliderWrapper)},_renderControl:function(){var h=[],c=[],l=[],u,r=[],i,a,f,e,v,s;this._radialSVG=n(this._createSVGElements("svg",{id:this._prefix+this._elementID+"-radial-slider-svg","class":this._prefix+"rs-svg",width:this._diameter+this._labelSpacing,height:this._diameter+this._labelSpacing}));var o=this.model.inline?50:100,y=this.model.inline?45:95,p=this.model.inline?55:105;for(this._pathLineGroupElement=n(this._createSVGElements("g",{id:"outerLineCircle",transform:"translate("+o+","+o+")"})),this._lineDirection=this._outerLineCalculation(50,40,this._tickCount,!0,!0,"path"),this._textGroupElement=n(this._createSVGElements("g",{id:"outerTextCircle",transform:"translate("+y+","+p+")"})),this._circleGroupElement=n(this._createSVGElements("g",{id:"circlegroup",transform:"translate("+o+","+o+")"})),this._circlePath=this._createSVGElements("path",{id:"circlepath",d:this._circleArcDirection(this._startAngle,this._endAngle),"class":this._prefix+"radialarcdefault",fill:"none","stroke-width":this.model.strokeWidth}),this._circleGroupElement.append(this._circlePath),this._circleGroupElement.append(this._radialCircle),this._outerTextDirection=this._outerTextCalculation(this._tickCount,this._startAngle,this._endAngle),i=0;i<this._tickCount+1;i++){for(a=i==0&&this._startAngle==0&&this._endAngle==360?this._tickCount-1:i,this._outerTextElement,f=0;f<this.ticks().length;f++)h[f]=this.ticks()[f].toString().split(".");for(u=0;u<h.length;u++)c[u]=h[u],e=c[u],s=t.preferredCulture(this.model.locale).numberFormat["."],v=t.isNullOrUndefined(e[1])?e[0]:e[0]+s+e[1],l[u]=v;if(t.isNullOrUndefined(this.value())||(r=this.value().toString().split("."),s=t.preferredCulture(this.model.locale).numberFormat["."],r=t.isNullOrUndefined(r[1])?r[0]:r[0]+s+r[1]),i==this._tickCount)if(this.ticks()[0]==this.value())continue;else this._outerTextElement=this._createSVGElements("text",{"stroke-width":.5,x:this._outerTextDirection[i].X2,y:this._outerTextDirection[i].Y2,id:this._prefix+this._elementID+"-dynamic-text","class":this._prefix+"dynamic-text",textContent:this.model.enableRoundOff?Math.round(r):r,"text-anchor":this._outerTextDirection[i].textAlignment});else this._outerTextElement=this._createSVGElements("text",{"stroke-width":.5,x:this._outerTextDirection[i].X2,y:this._outerTextDirection[i].Y2,"class":this._prefix+"ticks-text",textContent:l[a],"text-anchor":this._outerTextDirection[i].textAlignment});this._textGroupElement.append(this._outerTextElement)}this._pathLineElement=this._createSVGElements("path",{"class":this._prefix+"radial-needle",d:this._lineDirection,fill:"none","stroke-width":this.model.strokeWidth});this._pathLineGroupElement.append(this._pathLineElement);this._radialSliderWrapper.append(this._radialSVG.append(this._pathLineGroupElement).append(this._textGroupElement).append(this._circleGroupElement));this.model.autoOpen?this.show():this.hide()},_circleArcDirection:function(n,t){var i=.5*Math.min(this._diameter-(this.model.inline?100:0),this._diameter-(this.model.inline?100:0)),r=this._polarToCartesian(this._startXY,this._startXY,i,n),u=this._polarToCartesian(this._startXY,this._startXY,i,t),f=t-n<=180?"0":"1";return["M",r.x,r.y,"A",i,i,0,f,1,u.x,u.y-1].join(" ")},_overNeedleMoveHandler:function(t){var r,f,e,o;t.preventDefault();var u=n("#"+this._prefix+this._elementID+"-radial-slider-svg").offset(),s=t.clientY,h=t.clientX,c=u.top+this._radialWidth/2-n(window).scrollTop(),l=u.left+this._radialWidth/2;this._dynamicAngleCalculation(l,h,c,s);this._angle>360-this._endAngle&&this._angle<360-this._startAngle&&(this.line={},r=this.model.radius,this.line.X1=this._startXY,this.line.Y1=this._startXY,f=Math.cos(this._angle*Math.PI/180),e=-Math.sin(this._angle*Math.PI/180),this.line.X2=this._startXY+(r-5)*f,this.line.Y2=this._startXY+(r-5)*e,this._overLine!=i?n(this._overLine).attr({d:[" M",this.line.X1,this.line.Y1,"L",this.line.X2,this.line.Y2].join(" "),"stroke-width":this.model.strokeWidth==1?this.model.strokeWidth-.5:this.model.strokeWidth-1}):this._overLine=this._createSVGElements("path",{"class":this._prefix+"needle-over",d:[" M",this.line.X1,this.line.Y1,"L",this.line.X2,this.line.Y2].join(" "),"stroke-width":this.model.strokeWidth==1?this.model.strokeWidth-.5:this.model.strokeWidth-1}),this._pathLineGroupElement.append(this._overLine),this._isNeedleOver=!0,o=this._ticksCalculation(),this.model.mouseover&&this._trigger("mouseover",{value:o,selectedValue:this.value()}))},_needleMoveHandler:function(i){var u,f;t.blockDefaultActions(i);t.isTouchDevice()&&(i=i.touches?i.touches[0]:i);var r=n("#"+this._prefix+this._elementID+"-radial-slider-svg").offset(),e=i.clientY,o=i.clientX,s=r.top+this._radialWidth/2-n(window).scrollTop(),h=r.left+this._radialWidth/2;this._dynamicAngleCalculation(h,o,s,e);this._angle>360-this._endAngle&&this._angle<360-this._startAngle&&(n(this._overLine).remove(),this._lineAngleCalculation(),u=this._isTapSelected?this._tickCount+1:this._tickCount,this._directionLine.remove(u,1,1),this._dynamicLineCalculation(this._angle,!1,!0,!0,this._tickCount,this._tickCount+1,!1),n(this._pathLineElement).attr("d",this._directionLine.toString()),this.model.slide&&(f=this._ticksCalculation(),this._trigger("slide",{value:f,selectedValue:this.value()})),this._needleMove=!0)},_dynamicLineDirection:function(n,t,i){var r={},u=this._polarToCartesian(this._startXY,this._startXY,this.model.radius,n,i),f=this._polarToCartesian(this._startXY,this._startXY,this.model.radius+10,n,i);return r.X1=u.x,r.Y1=u.y,r.X2=f.x,r.Y2=f.y,this._isTapSelected=!0,this._directionLine.insert(t+2,[" M",r.X1,r.Y1,"L",r.X2,r.Y2].join(" ")),this._directionLine.toString()},_inLineCalculation:function(n,t){var i={},r=this._polarToCartesian(this._startXY,this._startXY,this.model.radius-5,n,t);return i.X1=this._startXY,i.Y1=this._startXY,i.X2=r.x,i.Y2=r.y,this._directionLine.append([" M",i.X1,i.Y1,"L",i.X2,i.Y2].join(" ")),this._directionLine.toString()},_dynamicLineCalculation:function(n,t,i,r,u,f,e){var o,s,h;return this.line={},o=this.model.radius,this.line.X1=this._startXY,this.line.Y1=this._startXY,s=Math.cos(n*Math.PI/180),h=-Math.sin(n*Math.PI/180),this.line.X2=this._startXY+(o-5)*s,this.line.Y2=this._startXY+(o-5)*h,e&&this._dynamicLineDirection(n,u,!0),t&&this._directionLine.append([" M",this.line.X1,this.line.Y1,"L",this.line.X2,this.line.Y2].join(" ")),r&&this._directionLine.insert(u+3,[" M",this.line.X1,this.line.Y1,"L",this.line.X2,this.line.Y2].join(" ")),this._directionLine.toString()},_setLocaleCulture:function(n,t){this.defaults.ticks===this.model.ticks&&(this.model.ticks=n.ticks);t&&(this.model.ticks=this._localizedLabels.ticks,this.model.value=this._localizedLabels.value);JSON.stringify(this.model.ticks)===JSON.stringify(this.defaults.ticks)&&(this.model.ticks=n.ticks);this.model.value===this.defaults.value&&(this.model.value=n.value)},_getStringBuilder:function(){var n=[],t=0;return{append:function(i){return n[t++]=i,this},remove:function(i,r,u){return t=t-(u||1),n.splice(i,r||1),this},insert:function(i,r){return n.splice(i,0,r),t++,this},toString:function(t){return n.join(t||"")}}},_outerLineCalculation:function(n,t,i,r,u,f){var c=this.model.radius,s=this._startAngle,l=s+(this._endAngle-this._startAngle)/(i-1),v,o,e,b,k;this._point=(this._endAngle-this._startAngle)/(i-1);var h=l,tt=h,w=[];if(this._degPoint=[],this._degPoint.push(s),r)for(v=0;v<i;v++)o={},s=s*Math.PI/180,l=l*Math.PI/180,this._degPoint.push(h),f!="text"&&(o.X1=this._startXY+c*Math.cos(s),o.Y1=this._startXY+c*Math.sin(s)),o.X2=this._startXY+(c+10)*Math.cos(s),o.Y2=this._startXY+(c+10)*Math.sin(s),s=h,l=h+this._point,h+=this._point,f=="path"?this._directionLine.append([" M",o.X1,o.Y1,"L",o.X2,o.Y2].join(" ")):w.push(o);if(u)for(e=0;e<this.ticks().length;e++){var a=e+1,y=e==this.ticks().length-1?this.ticks()[this.ticks().length-1]+.5:this.ticks()[a],p=e==0?this.ticks()[e]-.5:this.ticks()[e],d=p<=0&&p>=this.value()?!0:!1,g=y<=0&&y<=this.value()?!0:!1,nt=d&&g?!0:p<=this.value()&&this.value()<y;nt&&(this.ticks()[e]!=this.value()?(b=this._point/(this.ticks()[a]-this.ticks()[e]),k=this.ticks()[a]-this.value(),this._startValueAngle=this._degPoint[a]-b*k,this._dynamicLineDirection(this._startValueAngle,this._tickCount-2,!1),this._inLineCalculation(this._startValueAngle,!1)):(this._startValueAngle=this._degPoint[e],this._inLineCalculation(this._startValueAngle,!1)))}return this._path=f=="path"?this._directionLine.toString():w,this._path},_refresh:function(){this._destroy();this.element.addClass("e-radialslider e-js");this._renderEJControl()},_createDelegates:function(){this._touchStartDelegate=n.proxy(this._touchStartHandler,this);this._needleMoveDelegate=n.proxy(this._needleMoveHandler,this);this._touchEndDelegate=n.proxy(this._touchEndHandler,this);this._overNeedleMoveDelegate=n.proxy(this._overNeedleMoveHandler,this);this._mouseOutDelegate=n.proxy(this._mouseOutHandler,this);this._enterMouseDelegates=n.proxy(this._entermouse,this)},_wireEvents:function(n){var i=n?"off":"on";this._createDelegates();t.listenEvents([this._radialSVG,this._radialSVG,this._radialSVG,this._radialSVG],[t.endEvent(),t.startEvent(),"mouseenter","mouseleave"],[this._touchEndDelegate,this._touchStartDelegate,this._enterMouseDelegates,this._mouseOutDelegate],!1)},_entermouse:function(){t.listenEvents([this._radialSVG],[t.moveEvent()],[this._overNeedleMoveDelegate],!1)},_mouseOutHandler:function(r){if(r.preventDefault(),this._radialSVG.has(r.target).length==0&&this._needleMove){t.listenEvents([this._radialSVG],[t.moveEvent()],[this._needleMoveDelegate],!0);var u=this._previousAngle!=i?this._previousAngle:this._startValueAngle;this._pathAfterAddLength=this._directionLine.toString().replace(/[^M]/g,"").length;this._directionLine.remove(this._pathAfterAddLength-1,1);this._inLineCalculation(u,!0);n(this._pathLineElement).attr("d",this._directionLine.toString());this._needleMove=!1}n(this._overLine).remove()},_touchEndHandler:function(n){n=n.touches?n.changedTouches[0]:n;t.listenEvents([this._radialSVG],[t.moveEvent()],[this._needleMoveDelegate],!0);this._tapHandlerEvent(n)},_touchStartHandler:function(n){t.blockDefaultActions(n);t.isTouchDevice()&&(n=n.touches?n.touches[0]:n);this._needleStop=!1;this.model.start&&this._trigger("start",{value:this.value()});t.listenEvents([this._radialSVG],[t.moveEvent()],[this._needleMoveDelegate],!1)},_getLocalizedLabels:function(){return t.getLocalizedConstants(this.sfType,this.model.locale)}});n.extend(!0,t.RadialSlider.prototype,t.RadialSliderBase.prototype);t.RadialSlider.Locale=t.RadialSlider.Locale||{};t.RadialSlider.Locale["default"]=t.RadialSlider.Locale["en-US"]={ticks:[0,10,20,30,40,50,60,70,80,90,100],value:10}}(jQuery,Syncfusion)});
