﻿CREATE TABLE [dbo].[Appointments] (
    [AppointmentId]                BIGINT         IDENTITY (1, 1) NOT NULL,
    [TenantId]                     BIGINT         NOT NULL,
    [ContactId]                    BIGINT         NULL,
    [UserId]                       BIGINT         NULL,
    [StartTime]                    DATETIME       NOT NULL,
    [StartTimeZone]                VARCHAR (50)   CONSTRAINT [DF_Appointments_StartTimeZone] DEFAULT ('') NOT NULL,
    [EndTime]                      DATETIME       NOT NULL,
    [EndTimeZone]                  VARCHAR (50)   CONSTRAINT [DF_Appointments_EndTimeZone] DEFAULT ('') NOT NULL,
    [Subject]                      NVARCHAR (50)  CONSTRAINT [DF_Appointments_Subject] DEFAULT ('') NOT NULL,
    [Description]                  NVARCHAR (250) CONSTRAINT [DF_Appointments_Description] DEFAULT ('') NOT NULL,
    [AllDay]                       BIT            CONSTRAINT [DF_Appointments_AllDay] DEFAULT ((0)) NOT NULL,
    [Canceled]                     BIT            CONSTRAINT [DF_Appointments_Canceled] DEFAULT ((0)) NOT NULL,
    [ChargeableCancellation]       BIT            CONSTRAINT [DF_Appointments_ChangedCancellation] DEFAULT ((0)) NOT NULL,
    [Categorize]                   VARCHAR (MAX)  CONSTRAINT [DF_Appointments_Categorize] DEFAULT ('') NOT NULL,
    [ResourceFields]               VARCHAR (MAX)  CONSTRAINT [DF_Appointments_ResourceFields] DEFAULT ('') NOT NULL,
    [Recurrence]                   BIT            CONSTRAINT [DF_Appointments_Recurrence] DEFAULT ((0)) NOT NULL,
    [RecurrenceRule]               VARCHAR (MAX)  CONSTRAINT [DF_Appointments_RecurrenceRule] DEFAULT ('') NOT NULL,
    [RecurrenceId]                 BIGINT         NULL,
    [RecurrenceExDate]             VARCHAR (MAX)  CONSTRAINT [DF_Appointments_RecurrenceExDate] DEFAULT ('') NOT NULL,
    [Request]                      NVARCHAR (MAX) CONSTRAINT [DF_Appointments_Request] DEFAULT ('') NOT NULL,
    [State]                        NVARCHAR (40)  CONSTRAINT [DF_Appointments_State] DEFAULT ('') NOT NULL,
    [Therapist]                    NVARCHAR (MAX) CONSTRAINT [DF_Appointments_Therapist] DEFAULT ('') NOT NULL,
    [SupervisorInstructionsBefore] NVARCHAR (MAX) CONSTRAINT [DF_Appointments_SupervisorInstructionsBefore] DEFAULT ('') NOT NULL,
    [IntervetionModel]             NVARCHAR (40)  CONSTRAINT [DF_Appointments_IntervetionModel] DEFAULT ('') NOT NULL,
    [IntervetionTechniques]        NVARCHAR (MAX) CONSTRAINT [DF_Appointments_IntervationTechniques] DEFAULT ('') NOT NULL,
    [TherapistComments]            NVARCHAR (MAX) CONSTRAINT [DF_Appointments_TherapistComments] DEFAULT ('') NOT NULL,
    [SupervisionRequest]           BIT            CONSTRAINT [DF_Appointments_SupervisionRequest] DEFAULT ((0)) NOT NULL,
    [SupervisorCommentsAfter]      NVARCHAR (MAX) CONSTRAINT [DF_Appointments_SupervisorCommentsAfter] DEFAULT ('') NOT NULL,
    [Notes]                        NTEXT          CONSTRAINT [DF_Appointments_Notes] DEFAULT ('') NOT NULL,
    [Room]                         NVARCHAR (30)  CONSTRAINT [DF__Appointmen__Room__1C5231C2] DEFAULT ('') NOT NULL,
    [CustomRecurrence]             BIT            CONSTRAINT [DF_Appointments_CustomRecurrence] DEFAULT ((0)) NOT NULL,
    [CustomRecurrenceId]           NVARCHAR (50)  CONSTRAINT [DF_Appointments_CustomRecurrenceId] DEFAULT ('') NOT NULL,
    [CustomRecurrenceRule]         NVARCHAR (MAX) CONSTRAINT [DF_Appointments_CustomRecurrenceRule] DEFAULT ('') NOT NULL,
    [AppointmentType]              NVARCHAR (20)  CONSTRAINT [DF_Appointments_AppointmentType] DEFAULT (N'Appointment') NOT NULL,
    [VisibleToAll]                 BIT            CONSTRAINT [DF_Appointments_ConcernsAll] DEFAULT ((0)) NOT NULL,
    [BlockOther]                   BIT            CONSTRAINT [DF_Appointments_BlockOthers] DEFAULT ((0)) NOT NULL,
    [Price]                        MONEY          CONSTRAINT [DF_Appointments_Price] DEFAULT ((0)) NOT NULL,
    [Deductions]                   MONEY          CONSTRAINT [DF_Appointments_Deductions] DEFAULT ((0)) NOT NULL,
    [PaymentType]                  NVARCHAR (40)  CONSTRAINT [DF_Appointments_PaymentType] DEFAULT ('') NOT NULL,
    [Bank]                         NVARCHAR (40)  CONSTRAINT [DF_Appointments_Bank] DEFAULT ('') NOT NULL,
    [TaskSupervision]              BIT            CONSTRAINT [DF_Appointments_TaskSupervision] DEFAULT ((0)) NOT NULL,
    [TaskSupervisionTherapistIDs]  NVARCHAR (50)  CONSTRAINT [DF_Appointments_TaskSupervisionTherapistIDs] DEFAULT ('') NOT NULL,
    [TaskSupervisionCustomers]     NVARCHAR (100) CONSTRAINT [DF_Appointments_TaskSupervisionCustomer] DEFAULT ('') NOT NULL,
    [TaskSupervisionSubject]       NVARCHAR (MAX) CONSTRAINT [DF_Appointments_TaskSupervisionSubject] DEFAULT ('') NOT NULL,
    [TaskSupervisionReplies]       NVARCHAR (MAX) CONSTRAINT [DF_Appointments_TaskSupervisionSubject1] DEFAULT ('') NOT NULL,
    [ContactEmailNotificationSent] BIT            CONSTRAINT [DF_Appointments_ClientEmailNotificationSent_1] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_Appointments] PRIMARY KEY CLUSTERED ([AppointmentId] ASC),
    CONSTRAINT [FK_Appointments_Contacts] FOREIGN KEY ([ContactId]) REFERENCES [dbo].[Contacts] ([ContactId]) ON DELETE CASCADE,
    CONSTRAINT [FK_Appointments_Users] FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users] ([UserId])
);


























GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Ο γιατρός ο οποίος δέχεται το ραντεβού', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Appointments', @level2type = N'COLUMN', @level2name = N'UserId';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'n', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Appointments', @level2type = N'COLUMN', @level2name = N'IntervetionModel';


GO
CREATE NONCLUSTERED INDEX [UserIdIndex]
    ON [dbo].[Appointments]([UserId] ASC);


GO
CREATE NONCLUSTERED INDEX [SubjectIndex]
    ON [dbo].[Appointments]([Subject] ASC);


GO
CREATE NONCLUSTERED INDEX [StartEndTimeIndex]
    ON [dbo].[Appointments]([StartTime] ASC, [EndTime] ASC);


GO
CREATE NONCLUSTERED INDEX [DescriptionIndex]
    ON [dbo].[Appointments]([Description] ASC);


GO
CREATE NONCLUSTERED INDEX [ContactIdIndex]
    ON [dbo].[Appointments]([ContactId] ASC);


GO
CREATE NONCLUSTERED INDEX [CustomRecurrenceIndex]
    ON [dbo].[Appointments]([CustomRecurrence] ASC);


GO
CREATE NONCLUSTERED INDEX [CustomRecurrenceIdIndex]
    ON [dbo].[Appointments]([CustomRecurrenceId] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Appointment= Συνεδρία, Event=Συμβάν', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Appointments', @level2type = N'COLUMN', @level2name = N'AppointmentType';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Μόνο για Συμβάν που είναι εποπτεία. Τα IDs των θεραπευτών που θα συμμετέχουν στην εποπτεία. ', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Appointments', @level2type = N'COLUMN', @level2name = N'TaskSupervisionTherapistIDs';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Μόνο για Συμβάν που είναι εποπτεία. Το Ζήτημα της εποπτείας.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Appointments', @level2type = N'COLUMN', @level2name = N'TaskSupervisionSubject';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Μόνο για Συμβάν που είναι εποπτεία. Οι απαντήσεις/προτάσεις στο Ζήτημα/Θεμα της εποπτείας.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Appointments', @level2type = N'COLUMN', @level2name = N'TaskSupervisionReplies';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Μόνο για Συμβάν που είναι εποπτεία. Τα ονόματα των πελατών.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Appointments', @level2type = N'COLUMN', @level2name = N'TaskSupervisionCustomers';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Μόνο για Συμβάν. Καθορίζει αν είναι Εποπτεία ', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Appointments', @level2type = N'COLUMN', @level2name = N'TaskSupervision';

