/*!
*  filename: ej.treeview.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min","./../common/ej.draggable.min","./ej.checkbox.min","./ej.waitingpopup.min"],n):n()})(function(){(function(n,t,i){t.widget("ejTreeView","ej.TreeView",{_rootCSS:"e-treeview",element:null,model:null,validTags:["ul","div"],_addToPersist:["expandedNodes","checkedNodes","selectedNodes"],_setFirst:!1,_requiresID:!0,defaults:{allowMultiSelection:!1,showCheckbox:!1,enableAnimation:!0,allowDragAndDrop:!1,htmlAttributes:{},allowDropChild:!0,allowDropSibling:!0,allowDragAndDropAcrossControl:!0,allowEditing:!1,allowKeyboardNavigation:!0,items:null,fields:{dataSource:null,query:null,tableName:null,child:null,id:"id",parentId:"parentId",text:"text",spriteCssClass:"spriteCssClass",expanded:"expanded",hasChild:"hasChild",selected:"selected",linkAttribute:"linkAttribute",imageAttribute:"imageAttribute",htmlAttribute:"htmlAttribute",imageUrl:"imageUrl",isChecked:"isChecked"},autoCheckParentNode:!1,loadOnDemand:!1,cssClass:"",template:null,enableRTL:!1,expandOn:"dblclick",enablePersistence:!1,enabled:!0,expandedNodes:[],checkedNodes:[],selectedNode:-1,selectedNodes:[],width:null,height:null,autoCheck:!0,enableMultipleExpand:!0,fullRowSelect:!1,sortSettings:{allowSorting:!1,sortOrder:"ascending"},nodeClick:null,beforeExpand:null,nodeExpand:null,beforeCollapse:null,nodeCollapse:null,beforeSelect:null,nodeSelect:null,nodeUnselect:null,nodeCheck:null,nodeUncheck:null,inlineEditValidation:null,beforeEdit:null,nodeEdit:null,keyPress:null,nodeDragStart:null,nodeDrag:null,nodeDragStop:null,nodeDropped:null,beforeAdd:null,nodeAdd:null,beforeDelete:null,nodeDelete:null,beforeCut:null,nodeCut:null,beforePaste:null,nodePaste:null,beforeLoad:null,loadSuccess:null,loadError:null,ready:null,create:null,destroy:null},dataTypes:{allowMultiSelection:"boolean",cssClass:"string",showCheckbox:"boolean",enableAnimation:"boolean",allowDragAndDrop:"boolean",allowDropChild:"boolean",allowDragAndDropAcrossControl:"boolean",allowEditing:"boolean",allowKeyboardNavigation:"boolean",autoCheckParentNode:"boolean",loadOnDemand:"boolean",enableRTL:"boolean",expandOn:"string",enablePersistence:"boolean",enableMultipleExpand:"boolean",fullRowSelect:"boolean",items:"data",fields:{dataSource:"data",query:"data",child:"data"},expandedNodes:"array",checkedNodes:"array",selectedNode:"number",selectedNodes:"array",htmlAttributes:"data",sortSettings:{allowSorting:"boolean",sortOrder:"enum"}},observables:["fields.dataSource"],dataSource:t.util.valueFunction("fields.dataSource"),_setModel:function(i){var r,l,s,h,c,e,f,u,o,a;for(r in i)switch(r){case"allowMultiSelection":this.model.allowMultiSelection=i[r];this.model.allowMultiSelection||(l=n(this._liList[this.model.selectedNodes[0]]),this._unselectAll(),this._isRender=!1,this._nodeSelectionAction(l),this._isRender=!0);break;case"cssClass":this._changeSkin(i[r]);break;case"fields":if(this._deepWatch&&!t.isNullOrUndefined(i[r].dataSource)&&JSON.stringify(this._oldDataSource)==JSON.stringify(typeof i[r].dataSource=="function"?i[r].dataSource():i[r].dataSource))return;this._unWireEvents();h=this.element.hasClass("e-js")?!1:!0;c=h?this.element.children("ul"):this.element;c.empty();this.model.fields==null||i[r]==null?this.model.fields=i[r]:this._extendFields(this.model.fields,i[r]);this.model.expandedNodes=[];this.model.checkedNodes=[];this.model.selectedNodes=[];this.model.selectedNode=-1;this._persistValues(this.model.expandedNodes,"expandedNodes");this._persistValues(this.model.checkedNodes,"checkedNodes");this._persistValues(this.model.selectedNodes,"selectedNodes");this._newDataSource=this.dataSource();!t.isNullOrUndefined(this.model.fields)&&this.dataSource()!=null?this._checkDataBinding():this._initialize();h&&(s=this.element.children(".e-treeview-ul"),c.append(s.children()),s.remove());break;case"allowDragAndDropAcrossControl":this.model.allowDragAndDropAcrossControl=i[r];this._enableDragDrop();break;case"enabled":this._enabledAction(i[r]);break;case"showCheckbox":i[r]?(this.model.showCheckbox=i[r],this._showCheckBox(),this.model.enabled||this.element.find(".nodecheckbox").ejCheckBox("disable")):(this.element.find(".e-item > div > .e-chkbox-wrap").remove(),this._updateCheckedNodes());break;case"autoCheck":this.model.autoCheck=i[r];break;case"autoCheckParentNode":this.model.autoCheckParentNode=i[r];break;case"expandedNodes":if(u=i[r].length,e=JSON.parse(JSON.stringify(this.model.expandedNodes)),u>0)for(this._expandNodes(i[r]),f=0,u=e.length;f<u;f++)i[r].indexOf(e[f])==-1&&this._collapseNode(n(this._liList[e[f]]));else u==0&&this._collapseAll();i[r]=this.model.expandedNodes;break;case"checkedNodes":if(this.model.showCheckbox)if(u=i[r].length,e=JSON.parse(JSON.stringify(this.model.checkedNodes)),u>0)for(this._checkedNodes(i[r]),f=0,u=e.length;f<u;f++)i[r].indexOf(e[f])==-1&&this._nodeUncheck(n(this._liList[e[f]]).find("> div > .e-chkbox-wrap > .nodecheckbox:first")[0]);else u==0&&this._uncheckAll();i[r]=this.model.checkedNodes;break;case"expandOn":this._off(this.element,this._isDevice&&n.isFunction(n.fn.tap)?this._touchExpandOn:this.model.expandOn,this._expandEventHandler);this._assignTouchExpandOn(i[r]);this._on(this.element,this._isDevice&&n.isFunction(n.fn.tap)?this._touchExpandOn:i[r],this._expandEventHandler);break;case"allowEditing":this._preventEditable();i[r]&&this._allowEditable();break;case"allowKeyboardNavigation":o=i[r]?"_on":"_off";this[o](this.element,"keydown",this._KeyPress);break;case"allowDragAndDrop":this.model.allowDragAndDrop=i[r];i[r]?this._addDragableClass():this._preventDraggable();break;case"allowDropChild":this.model.allowDropChild=i[r];i[r]?this._addDragableClass():this._preventDropChild();break;case"allowDropSibling":this.model.allowDropSibling=i[r];i[r]?this._addDragableClass():this._preventDropSibling();break;case"enableRTL":this.model.enableRTL=i[r];a=this.element.is("UL")?this.element.parent(".e-treeview-wrap"):this.element;o=this.model.enableRTL?"addClass":"removeClass";a[o]("e-rtl");break;case"height":this.element.is("ul")?this.element.parent().height(i[r]):this.element.height(i[r]);break;case"width":this.element.is("ul")?this.element.parent().width(i[r]):this.element.width(i[r]);break;case"selectedNode":case"selectedNodes":this.model.selectedNodes=r=="selectedNode"?[i[r]]:i[r];this.element.find("a.e-text.e-active").removeClass("e-node-focus e-active").closest("li").attr("aria-selected",!1);this._doSelectNodes(this.model.selectedNodes,!1);r!="selectedNode"&&(i[r]=this.model.selectedNodes);break;case"htmlAttributes":this._addAttr(i[r]);break;case"enableMultipleExpand":i[r]||this.collapseAll();this.model.enableMultipleExpand=i[r];break;case"sortSettings":n.extend(this.model.sortSettings,i[r]);t.isNullOrUndefined(this.model.fields)||this.dataSource()==null||(this.model.expandedNodes=[],this.model.checkedNodes=[],this.model.selectedNodes=[],this._checkDataBinding());break;case"fullRowSelect":this._wholeRowEvents("_off");this.model.fullRowSelect=i[r];this.model.fullRowSelect?this._addWholeRowWrap():this._removeWholeRowWrap();this._doWholeRowAction();this._wholeRowEvents("_on")}},_destroy:function(){this.element.html("");this._cloneElement.removeClass("e-treeview e-js e-treeview-wrap");var t=this.element.is("UL")?this.element.parent(".e-treeview-wrap"):this.element;t.replaceWith(this._cloneElement);n("#"+this._id+"_WaitingPopup").remove();window.localStorage?(window.localStorage.removeItem(this._id+"_childNodes"),window.localStorage.removeItem(this._id+"_persistedValues")):window.cookie&&(document.cookie=this._id+"_childNodes=; expires=Thu, 01 Jan 1970 00:00:00 UTC",document.cookie=this._id+"_persistedValues=; expires=Thu, 01 Jan 1970 00:00:00 UTC")},_init:function(){var i,r,n;this._cloneElement=this.element.clone();this._dataSource=[];this._fragment=[];this._templateType="";this._indexID=0;this._newDataSource=this.dataSource();this._id=this.element.prop("id");this._treeList=[];this._isTextbox=!1;this._isDevice=this._checkDevice();this._deepWatch=this.element.attr("e-deepwatch")==="true"&&!(this.dataSource()instanceof t.DataManager)?!0:!1;i=t.browserInfo();this.model.enablePersistence||(this.model.checkedNodes=[],this.model.expandedNodes=[]);this._isIE8=i.name=="msie"&&i.version=="8.0"?!0:!1;this._deepWatch&&(this._oldDataSource=JSON.parse(JSON.stringify(this.dataSource())));this._assignTouchExpandOn(this.model.expandOn);this.model.enablePersistence&&(r=this._getCookies("_persistedValues"),r||(n={selectedNodes:[],expandedNodes:[],checkedNodes:[]},n=this._updatePersistAttr(n),this._setCookies("_persistedValues",JSON.stringify(n))));t.isNullOrUndefined(this.model.fields)||this.dataSource()==null?(this._initialize(),this._completeRendering()):this._checkDataBinding()},_initialize:function(){this._cutNode=null;this._beforeEditText=null;this._CurrenctSelectedNodes=[];this._isLoaded=!1;this._renderMultiTouchDialog();this.element.is("ul")?this._createWrapper():(this.element.addClass("e-treeview-wrap e-widget").attr("tabindex",0).children("ul:first").addClass("e-ul e-box").attr("role","tree"),this.model.width!=null&&this.element.width(this.model.width),this.model.height!=null&&this.element.height(this.model.height),this.model.enableRTL&&this.element.addClass("e-rtl"),this.model.cssClass!=null&&this.element.addClass(this.model.cssClass),this._addAttr(this.model.htmlAttributes));this._elementSettings();this._beforeBaseClass();this.dataSource()==null&&(this._addBaseClass(),this._controlRender());this._addWholeRowWrap();this._isIE8&&(this.element.is("UL")?this.element.parent(".e-treeview-wrap").addClass("e-ie8"):this.element.addClass("e-ie8"));this._finalize()},_completeRendering:function(){this._treeList.length!=0||this._isLoaded||(this._finalizeNode(),this._enabledAction(this.model.enabled),this._triggerEvent("ready",{element:this.element[0]}),this._isLoaded=!0)},_addWholeRowWrap:function(){this.model.fullRowSelect&&(this.element.is("UL")?this.element.parent(".e-treeview-wrap").addClass("e-fullrow-wrap"):this.element.addClass("e-fullrow-wrap"))},_removeWholeRowWrap:function(){this.element.is("UL")?this.element.parent(".e-treeview-wrap").removeClass("e-fullrow-wrap"):this.element.removeClass("e-fullrow-wrap")},_doWholeRowAction:function(){var t,r,i,u;if(this.model.fullRowSelect)for(t=0,r=this._liList.length;t<r;t++)i=n(this._liList[t]),i[0]!=null&&(u=i.find("> .e-text-wrap"),this._renderWholeRow(i,u));else this.element.find(".e-fullrow").remove()},_renderWholeRow:function(t,i){if(t&&this.model.fullRowSelect){var r=document.createElement("div");r.setAttribute("class","e-fullrow");i?n(r).insertAfter(i):t[0].appendChild(r)}},_extendFields:function(t,i){if(t==null||i==null)t=i;else if(t.child==null||i.child==null)n.extend(t,i);else{this._extendFields(t.child,i.child);var r=t.child;n.extend(t,i);n.extend(t.child,r)}},_checkDevice:function(){return t.isDevice()&&t.isTouchDevice()},_assignTouchExpandOn:function(n){this._touchExpandOn=n=="dblclick"?"doubletap":n=="click"?"tap":n},_renderMultiTouchDialog:function(){var i,r,u;this._customPop=t.buildTag("div.e-fe-popup","",{display:"none"});i=t.buildTag("div.e-content");r=t.buildTag("div.e-downtail e-tail");this.model.allowMultiSelection&&(u=t.buildTag("span.e-rowselect e-icon"),i.append(u));this._customPop.append(i);this._customPop.append(r);this.element.is("ul")||this.element.append(this._customPop);this._on(i,this._isDevice&&n.isFunction(n.fn.tap)?"touchstart":"mousedown",this._popupClick)},_popupClick:function(){var n=this._customPop.find(".e-rowselect");n.hasClass("e-spanclicked")?this._hidePopup():(this._isPopup=!0,n.addClass("e-spanclicked"))},_hidePopup:function(){this._customPop!=null&&this._customPop.is(":visible")&&(this._customPop.find(".e-rowselect").removeClass("e-spanclicked"),this._customPop.hide(),this._isPopup=!1)},_elementSettings:function(){var n=this.element.is("UL")?this.element.parent(".e-treeview-wrap"):this.element;this.element.is("ul")&&n.attr("role","tree")},_beforeBaseClass:function(){var t=this.element.is("UL")?this.element.parent(".e-treeview-wrap")[0]:this.element[0];this._ulList=n(t.querySelectorAll("ul"));this._liList=n(t.querySelectorAll("li"))},_createWrapper:function(){var n=this.wrapper=t.buildTag("div.e-treeview-wrap e-widget "+this.model.cssClass,"","",{tabindex:0});this.model.width!=null&&n.width(this.model.width);this.model.height!=null&&n.height(this.model.height);this.model.enableRTL&&n.addClass("e-rtl");n.insertAfter(this.element);n.append(this.element.addClass("e-ul e-box").attr("tabindex",0));this.element.is("ul")&&n.append(this._customPop);this._addAttr(this.model.htmlAttributes)},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.element.addClass(n):t=="disabled"&&n=="disabled"?i._enabledAction(!1):i.element.attr(t,n)})},_changeSkin:function(n){if(this.model.cssClass!=n){var t=this.element.is("UL")?this.element.parent(".e-treeview-wrap"):this.element;t.removeClass(this.model.cssClass).addClass(n);t.find(".e-item > div > .e-chkbox-wrap").removeClass(this.model.cssClass).addClass(n);this._waitingPopup&&this._waitingPopup.option("cssClass",n)}},_enabledAction:function(n){this.model.enabled=n;n?(this.element.removeClass("e-disable"),this._wireEvents()):(this.element.addClass("e-disable"),this._unWireEvents())},_checkDataBinding:function(){this.dataSource()instanceof t.DataManager?this._initDataSource():(this._ensureDataSource(this.dataSource()),this._initialize(),this._completeRendering())},_initDataSource:function(){this.element.ejWaitingPopup({cssClass:this.model.cssClass});this._waitingPopup=this.element.ejWaitingPopup("instance");this._waitingPopup.maindiv.addClass("e-tree-popup");var n=this,t,r;this.element.ejWaitingPopup("refresh");this.element.ejWaitingPopup("show");this.dataSource().ready!=i?this.dataSource().ready.done(function(t){n._initAfterDataSource(t)}):(r=this._columnToSelect(this.model.fields),t=this.dataSource().executeQuery(r),t.done(function(t){n._initAfterDataSource(t)}))},_initAfterDataSource:function(n){var i=this;i.element.ejWaitingPopup("hide");i.retriveData=n.xhr&&n.xhr.responseJSON&&n.xhr.responseJSON.d?n.xhr.responseJSON.d:n.result?n.result:[];i._typeOfFieldId=i.retriveData[0]?(typeof t.getObject(i.model.fields.id,i.retriveData[0])).toLowerCase():"";i._ensureDataSource(i.retriveData);i._newDataSource=JSON.parse(JSON.stringify(i.retriveData));i._initialize();i.dataSource().dataSource.offline||!i.model.loadOnDemand&&t.isNullOrUndefined(i.model.fields.child)||(i._templateType=2);(i.model.loadOnDemand||t.isNullOrUndefined(i.model.fields.child))&&i._completeRendering()},_columnToSelect:function(n){var u=[],r=t.Query(),i;if(n.query||t.isNullOrUndefined(n.tableName))r=n.query?n.query.clone():r;else{for(i in n)i!=="tableName"&&i!=="child"&&i!=="dataSource"&&n[i]&&u.push(n[i]);u.length>0&&r.select(u);this.dataSource().dataSource.url.match(n.tableName+"$")||t.isNullOrUndefined(n.tableName)||r.from(n.tableName)}return r},_ensureDataSource:function(n){n=this._getSortAndFilterList(this.model.fields,n);this.currentSelectedData=n;this._renderTemplate(n);this.element.is("ul")?this.element.html(this._fragment.firstChild.children):this.element.html(this._fragment)},_getTemplateType:function(n,r){for(var u=0,f=n.length;u<f;u++){if(t.getObject(r.parentId,n[u])!==i||t.getObject(r.hasChild,n[u])!==i)return 1;if(n[u].hasOwnProperty("child"))return 2}return 1},_groupingObjects:function(n,t){for(var u,r={},f=[],i=0,e=n.length;i<e;i++)u=JSON.stringify(t(n[i])),r[u]=r[u]||[],r[u].push(n[i]);for(i in r)f.push(r[i]);return f},_renderTemplate:function(n){this._onlineData=!1;this._loadOnDemandNodes=!1;var i=this;this._templateType=this._getTemplateType(n,this.model.fields);this._dataSource=this._templateType==1?this._groupingObjects(n,function(n){return[!t.isNullOrUndefined(n)&&[t.getObject(i.model.fields.parentId,n)].toString()]}):n;this._templateNodeCreation(n,this.model.fields)},_templateNodeCreation:function(i,r){var o,u,f,s,e;if(this._fragment=document.createDocumentFragment(),o=document.createElement("ul"),this._fragment.appendChild(o),u=this._fragment.firstChild,(this._onlineData||this._loadOnDemandNodes)&&(u.className="e-treeview-ul",u.setAttribute("role","group"),n(u).attr("style","display:none")),this.dataSource()!=null){for(f=0,s=i.length;f<s;f++)i[f]&&(e=t.getObject(this.model.fields.parentId,i[f]),(t.isNullOrUndefined(e)||e==0||this._loadOnDemandNodes)&&u.appendChild(this._genTemplate(i[f],r)));n(u).find(".e-item:first-child:not(:last-child)").addClass("first");n(u).find(".e-item:last-child").addClass("last")}},_onlineDataSource:function(t,i,r){this._loadOnDemandNodes=!0;this._templateNodeCreation(t,r);var u=this.element.find(".e-item#"+i);u[0]!=null&&(n(u[0]).append(this._fragment),u.children().find("> div:first").addClass("e-icon e-plus"),this._finalizeLoadOnDemand(u));this._onlineData=!1},_genTemplate:function(i,r){var nt=document.createDocumentFragment(),u,tt,e,o,s,l,a,v,y,p,h,w,f,it,rt,b,k,d,g,c,ut;if(tt=document.createElement("li"),nt.appendChild(tt),u=nt.firstChild,u.id=t.getObject(r.id,i),u.id&&u.id!="undefined"||(u.id=""),u.setAttribute("role","treeitem"),l=t.getObject(r.htmlAttribute,i),l&&this._setAttributes(l,u),u.className+=" e-item",o=document.createElement("a"),a=t.getObject(r.imageUrl,i),v=t.getObject(r.spriteCssClass,i),a?(e=document.createElement("img"),e.className="e-align",e.src=a,y=t.getObject(r.imageAttribute,i),y&&this._setAttributes(y,e)):v&&(e=document.createElement("span"),e.className=v),this.model.template){if(e&&o.appendChild(e),typeof n.fn.render!="function")throw"Error : JsRender dependecy script missing";o.innerHTML+=this._renderEjTemplate(this.model.template,i)}else p=t.getObject(r.text,i),p?n(o).text(p):n(o).text("undefined"),e&&o.insertBefore(e,o.lastChild);if(h=t.getObject(r.linkAttribute,i),h&&(typeof h=="object"?this._setAttributes(h,o):o.href=h),u.appendChild(o),t.getObject(r.expanded,i)&&(u.className+=" expanded"),t.getObject(r.selected,i)&&(u.className+=" selected"),w=t.getObject(r.isChecked,i),t.isNullOrUndefined(w)||(u.className+=w?" checked":" unchecked"),u.setAttribute("aria-selected",!1),u.setAttribute("aria-expanded",!1),this.dataSource()instanceof t.DataManager)this._updateElement(u,!0),this.model.showCheckbox&&this._checkboxOnTemplate(u.children[0]),this.model.loadOnDemand?(t.getObject(r.hasChild,i)||i.hasOwnProperty("child"))&&(u.children[0].firstChild.className="e-icon e-plus"):!t.isNullOrUndefined(r.child)&&r.child.dataSource instanceof t.DataManager?(f=this,rt=r.child.parentId?r.child.parentId:f.model.fields.parentId,b,b=r.id?r.id:this.model.fields.id,this._treeList.push("false"),k=t.getObject(b,i),it=this._executeDataQuery(r.child,rt,this._typeOfFieldId=="number"?parseInt(k):k),it.done(function(n){if(f._treeList.pop(),s=n.xhr&&n.xhr.responseJSON&&n.xhr.responseJSON.d?n.xhr.responseJSON.d:n.result?n.result:[],s=f._getSortAndFilterList(r.child,s),s&&s.length>0){f._onlineData=!0;var t=s[0][r.child.parentId];f._updateRemoteData(f._newDataSource,t,s,f.model.fields);f._onlineDataSource(s,t,r.child)}f._treeList.length==0&&f._completeRendering()})):t.isNullOrUndefined(this.model.fields.child)&&this._childNodeCreation(i,u,r);else if(this.model.loadOnDemand){if(this._updateElement(u,!0),(t.getObject(r.hasChild,i)||i.hasOwnProperty("child"))&&(u.children[0].firstChild.className="e-icon e-plus"),this.model.showCheckbox&&this._checkboxOnTemplate(u.children[0]),t.getObject(r.expanded,i)&&this._childNodeCreation(i,u,r),this.model.enablePersistence&&(d=this._getCookies("_childNodes"),d))for(g=JSON.parse(d),c=0,ut=g.length;c<ut;c++)if(g[c].toString()==t.getObject(r.id,i).toString()){this._childNodeCreation(i,u,r);break}}else this._onlineData||(this._updateElement(u,!0),this.model.showCheckbox&&this._checkboxOnTemplate(u.children[0]),this._childNodeCreation(i,u,r));return u},_childNodeCreation:function(i,r,u){var f,s,h,o,e,c;if(f=this._templateType==2?!t.isNullOrUndefined(i.child)&&i.child.length>0&&i.child:this._getChildNodes(this._dataSource,{id:t.getObject(u.id,i)}),!t.isNullOrUndefined(f)&&f.length>0){for(r.children[0].firstChild.className="e-icon e-plus",s=document.createDocumentFragment(),h=document.createElement("ul"),s.appendChild(h),o=s.firstChild,o.className="e-treeview-ul",o.setAttribute("role","group"),n(o).attr("style","display:none"),e=0,c=f.length;e<c;e++)f[e]&&t.getObject(this.model.fields.parentId,[f[e]])!=0&&o.appendChild(this._genTemplate(f[e],u));r.appendChild(h)}},_checkboxOnTemplate:function(n){n.parentElement.id==""&&(n.parentElement.id=this._id+"_"+this._indexID,this._indexID++);var t=document.createElement("input");t.setAttribute("type","checkbox");t.setAttribute("class","nodecheckbox");t.setAttribute("name",this._id+"_Checkbox_"+n.parentElement.id);t.setAttribute("value",n.parentElement.id);n.insertBefore(t,n.children[1])},_updateElement:function(t,r){var f,o,u,s,h=t.firstChild,c,l,a,e;h&&(c=h.nodeName,c=="SPAN"||c=="IMG"?(s=h,u=t.lastChild):u=t.lastChild,u&&(a=u.lastChild!=null?u.lastChild.nodeValue:"",l=n.trim(u.innerHTML),e=n(u).clone()[0],n(u).remove(),e.className+=" e-text CanSelect",e.innerHTML=l,t.innerHTML=""),f=document.createElement("div"),f.setAttribute("class","e-text-wrap"),f.setAttribute("role","presentation"),r&&(o=document.createElement("div"),o.setAttribute("role","presentation"),f.appendChild(o)),s&&f.appendChild(s),f.appendChild(e),n(t).prop("name")==i&&n(t).prop("name",n.trim(a)),t.appendChild(f),this._renderWholeRow(n(t)))},_setAttributes:function(t,i){for(var r in t)n(i).attr(r,t[r])},_addDragableClass:function(){this.model.allowDragAndDrop&&(this._anchors=this._liList.map(function(){return n("a.e-text",this)[0]}),this._anchors.addClass("e-draggable e-droppable"),this._enableDragDrop(),this._on(this.element,"mouseup touchstart pointerdown MSPointerDown",this._anchors,this._focusElement),this._on(this.element,"focusout",this._anchors,this._blurElement))},_addBaseClass:function(){this._ulList.addClass("e-treeview-ul").attr("role","group");this._liList.addClass("e-item").attr("role","treeitem");this.element.is("ul")||this.element.find("ul:first").removeClass("e-treeview-ul").addClass("e-ul")},_controlRender:function(){var s=this.element,h,e,r,f,c,l,a,o,y,v,u,p;if(s!=null){for(h=s.find(".e-item"),e=0;e<h.length;e++)r=n(h[e]),a=r.children("ul.e-treeview-ul")[0],a&&n(r.children("ul.e-treeview-ul")[0]).remove(),o=r.children("a")[0],o?(l=n.trim(n(o).text()),f=n(o).clone(),n(o).remove(),c=r.clone(),n(f).prepend(c.children()),n(f).addClass("e-text CanSelect"),r.html("")):(l=n.trim(this._getText(r)),c=r.clone(),r.html(""),f=t.buildTag("a.e-text CanSelect","","",""),f.append(c.children()),f[0].innerHTML+=l),y=t.buildTag("div","",{},{role:"presentation"}),v=t.buildTag("div.e-text-wrap","",{},{role:"presentation"}),n(v).append(y).append(f),r.prepend(v),this._renderWholeRow(r),a&&r.append(a),r.prop("name")==i&&r.prop("name",l),this.model.showCheckbox&&this._checkboxOnTemplate(h[e].children[0]);this.model.showCheckbox&&s.find(".nodecheckbox").ejCheckBox({cssClass:this.model.cssClass,change:this._checkedChange})}u=s.find(".e-item");u.find(">ul").hide();p=u.find(".e-text");u.filter(".e-item:last-child").addClass("last");n(u[0]).addClass("first");n(u[0]).hasClass("first")&&n(u[0]).hasClass("last")&&n(u[0]).removeClass("first");n(u.filter(":has(ul)")).each(function(){n(this).attr("aria-expanded",!1).attr("aria-selected",!1);var t=n(this).children("ul:first");n(t).is(":hidden")?n(this).find("> div > div:first").removeClass("e-icon e-minus").addClass("e-icon e-plus"):n(this.childNodes[1]).removeClass("e-icon e-plus").addClass("e-icon e-minus")})},_getText:function(t){return n(t).clone().children().remove().end().text()},_expandNodes:function(i,r){for(var e,o=i.length,u=[],f=0;f<o;f++)(u=r?this._getNodeByID(i[f]):n(this._liList[i[f]]),t.isNullOrUndefined(u))||!this.model.enableMultipleExpand&&(e=u.siblings().find(">div>.e-minus").closest(".e-item"),e.length>0)||this.isExpanded(u)||(!this.model.loadOnDemand||this.dataSource()instanceof t.DataManager?this._expandNode(u):this._createChildNodesWhenExpand(u))},_checkedNodes:function(t){var r,i,u;for(t.length>0&&this._removeField(this._newDataSource,this.model.fields,"isChecked"),i=0,u=t.length;i<u;i++)r=n(this._liList[t[i]]),r[0]!=null&&this._nodeCheck(r.find("> div > .e-chkbox-wrap > .nodecheckbox:first")[0])},_finalize:function(){this.model.showCheckbox&&this.element.find(".e-item > div .nodecheckbox").ejCheckBox({cssClass:this.model.cssClass,change:this._checkedChange});this.model.allowEditing&&this._allowEditable()},_finalizeNode:function(){var a,h,r,o,c,y;this._isRender=!1;this._allowOnDemand=!0;var v=this.element,f=this._getCookies("_persistedValues"),i=[],u=[],l,e,s;if(!(this.model.expandedNodes instanceof Array&&this.model.expandedNodes.length>0))for(l=v.find("li.expanded"),o=l.length,r=0;r<o;r++)(a=n(l[r]),!this.model.enableMultipleExpand&&(h=a.siblings().find(">div>.e-minus").closest(".e-item"),h.length>0))||this._expandNode(a);if(f&&(e=JSON.parse(f),i=e.expandedNodes,u=[]),i&&i.length>0&&!t.isNullOrUndefined(this.model.fields)&&this.dataSource()!=null){for(r=0,o=i.length;r<o;r++)if(u=this.element.find(".e-item#"+i[r]),u[0]!=null){if(!this.model.enableMultipleExpand&&(h=u.siblings().find(">div>.e-minus").closest(".e-item"),h.length>0))continue;!this.model.loadOnDemand||this.dataSource()instanceof t.DataManager?this._expandNode(u):this._createChildNodesWhenExpand(u)}}else s=JSON.parse(JSON.stringify(this.model.expandedNodes)),this._expandNodes(s);if(this.model.showCheckbox)if(f&&(e=JSON.parse(f),i=e.checkedNodes,u=[]),i&&i.length>0&&!t.isNullOrUndefined(this.model.fields)&&this.dataSource()!=null)for(this._removeField(this._newDataSource,this.model.fields,"isChecked"),r=0,o=i.length;r<o;r++)u=this.element.find(".e-item#"+i[r]),u[0]!=null&&this._nodeCheck(u.find("> div > .e-chkbox-wrap > .nodecheckbox:first")[0]);else s=JSON.parse(JSON.stringify(this.model.checkedNodes)),this._checkedNodes(s);this.model.checkedNodes instanceof Array&&this.model.checkedNodes.length>0||this.model.showCheckbox&&this._isCheckedAction();c=!1;f&&(e=JSON.parse(f),i=e.selectedNodes,c=!0);(!f||i&&i.length==0)&&(i=this.model.selectedNodes.length>0?this.model.selectedNodes:this.model.selectedNode==-1?[]:[this.model.selectedNode],c=!1);y={ctrlKey:!0};i&&i.length>0?this._doSelectNodes(i,c):this._isSelectedAction(y);this._isRender=!0;this._allowOnDemand=!1;v.find(".e-item.checked, .e-item.expanded, .e-item.selected").removeClass("checked expanded selected")},_doSelectNodes:function(t,i){for(var u,f={ctrlKey:!0},r=0,e=t.length;r<e;r++)if(u=i?this._getNodeByID(t[r]):n(this._liList[t[r]]),u[0]!=null&&this._nodeSelectionAction(u,f),!this.model.allowMultiSelection)break},_doUnselectNodes:function(n){for(var i,t=0,r=n.length;t<r;t++)if(i=this._getNodeByID(n[t]),i[0]!=null&&this._nodeUnSelectionAction(i),!this.model.allowMultiSelection)break},_updateSelectedNode:function(){var t=n(this._liList).find(".e-text.e-active").closest(".e-item"),r,u,f;if(this.model.selectedNodes=[],this.model.allowMultiSelection||(this.model.selectedNode=null),t[0]==i||t.length<=0){this._persistValues(this.model.selectedNodes,"selectedNodes");return}if(this.model.allowMultiSelection){for(r=0,u=t.length;r<u;r++)f=n(this._liList).index(t[r]),this.model.selectedNodes.push(f);this._persistValues(this.model.selectedNodes,"selectedNodes")}else this._isRender=!1,this._nodeSelectionAction(n(t[0])),this._isRender=!0},_setCookies:function(n,i){window.localStorage?window.localStorage.setItem(this._id+n,i):document.cookie&&t.cookie.set(this._id+n,i)},_getCookies:function(n){return window.localStorage?window.localStorage.getItem(this._id+n):window.cookie?t.cookie.get(this._id+n):void 0},_updateCheckedNodes:function(){for(var r=[],t=this.element.find(".e-item > div > .e-chkbox-wrap > .nodecheckbox.checked").closest(".e-item"),i=0;i<t.length;i++)r.push(n(this._liList).index(t[i]));(r.length>0||t.length==0)&&(this.model.checkedNodes=r);t.length==0&&this.model.checkedNodes.push(-1);this._persistValues(this.model.checkedNodes,"checkedNodes")},_updateExpandedNodes:function(){var t,r=[],i,u;for(t=this.element.find(".e-item > div > .e-minus").closest(".e-item").addClass("e-collapse"),i=0,u=t.length;i<u;i++)r.push(n(this._liList).index(t[i]));(r.length>0||t.length==0)&&(this.model.expandedNodes=r);t.length==0&&this.model.expandedNodes.push(-1);this._persistValues(this.model.expandedNodes,"expandedNodes")},_isCheckedAction:function(){for(var r,u,f,i=this.element.find(".e-item.checked"),t=0;t<i.length;t++)if(r=n(i[t]).find(".e-item"),u=n(i[t]).find(".e-item.unchecked"),r.length==0||!(r.length>0&&u.length==r.length)||!this.model.autoCheck)for(this._nodeCheck(n(i[t]).find("> div > .e-chkbox-wrap > input.nodecheckbox:first")[0]),f=0;f<u.length;f++)this._nodeUncheck(n(u[f]).find("> div > .e-chkbox-wrap > .nodecheckbox:first")[0]);this.element.find(".e-item.checked, .e-item.unchecked").removeClass("checked unchecked")},_isExpandedAction:function(){for(var i,u,r=this.element.find("li.expanded"),f=r.length,t=0;t<f;t++)(i=n(r[t]),!this.model.enableMultipleExpand&&(u=i.siblings().find(" > div > .e-minus").closest(".e-item"),u.length>0))||this._expandNode(i);this.element.find(".e-item.expanded").removeClass("expanded")},_isSelectedAction:function(t){for(var r=this.element.find(".e-item.selected"),i=0,u=r.length;i<u;i++)if(r[i]!=null&&this._nodeSelectionAction(n(r[i]),t),!this.model.allowMultiSelection)break;this.element.find(".e-item.selected").removeClass("selected")},_checkedChange:function(t){if(t.isInteraction){var i=this.element.closest(".e-treeview.e-js").data("ejTreeView"),r=t.isChecked?"_nodeCheck":"_nodeUncheck";i[r](n(this.element)[0],t)}},_doClickAnimation:function(t){var r=n(t.target);r!=i&&t.type!=i&&(r.addClass("e-animate"),r.is("A")&&r.hasClass("e-text")&&r.closest(".e-text-wrap").addClass("e-animate"))},_ClickEventHandler:function(i){var p=this,u,r=n(i.target),c=r.closest(".e-item").find("> div > div:first"),l,a,e,f,o,s,v,h,y;if(this._isPopup=!1,c&&!c.hasClass("e-process")&&(u=r.closest(".e-item"),!u.hasClass("e-node-disable"))){if(this.element.find(".e-animate").removeClass("e-animate"),this._doClickAnimation(i),l=u[0]!=null?u[0].getAttribute("id"):"",this._triggerEvent("nodeClick",{event:i,currentElement:r[0],id:l,parentId:u.closest("ul").closest(".e-item").attr("id")}),!t.isNullOrUndefined(r)&&r.is("DIV"))if(r.hasClass("e-plus")||r.hasClass("e-minus"))if(a=null,r.is("SPAN")&&(r=r.parent("div").find("div:first")),a=u.children("ul:first"),o=this.isChildLoaded(u),!o&&this.model.loadOnDemand){if(r.removeClass("e-plus").addClass("e-load"),this._checkboxChecked=r.parent().find(".nodecheckbox").hasClass("checked"),f=this._getNodeDetails(u),e={currentElement:u,targetElement:r[0],id:f.id,value:f.text,parentId:f.parentId,isChildLoaded:o,hasParent:!0,async:!0},this._isEventTriggered=!0,this._triggerEvent("beforeExpand",e))return!1;this._loadOnDemand(e,r[0])}else s=!0,r.hasClass("e-plus")?this._expandNode(u,s):this._collapseNode(u,s);else this.model.fullRowSelect&&(r.hasClass("e-fullrow")||r.hasClass("e-text-wrap"))&&(r=r.closest(".e-item").find("> .e-text-wrap .e-text"));r.is("A")||r.hasClass("input-text")||(r=r.closest(".e-text"));!t.isNullOrUndefined(r)&&r.is("A")&&r.hasClass("CanSelect")&&(i&&i.pointerType=="touch"&&this._customPop!=null&&this.model.allowMultiSelection?(v=n(i.target),this._customPop.is(":visible")||this._customPop.show(),this._customPop.is(":visible")&&!this._customPop.find(".e-rowselect").hasClass("e-spanclicked")?(h=v.offset(),this._customPop.offset({left:h.left,top:h.top-40})):i.ctrlKey=!0):this._hidePopup(),r.hasClass("e-active")?this.model.allowMultiSelection&&this._nodeUnSelectionAction(r.closest(".e-item"),i):this._nodeSelectionAction(r.closest(".e-item"),i))}y=["INPUT","BUTTON","TEXTAREA","SELECT"];i.target&&y.indexOf(i.target.tagName)<0&&this.element.focus()},_getChildTables:function(n,i,r){if(!t.isNullOrUndefined(n.child))return i==r?n.child:this._getChildTables(n.child,i,r+1)},_loadOnDemand:function(i){var r,e,u,f=this,o;if(this.dataSource()instanceof t.DataManager){if(e=i.currentElement.parents("ul.e-treeview-ul").length,u=this._getChildTables(f.model.fields,e,1),t.isNullOrUndefined(u)&&t.isNullOrUndefined(this.model.fields.child)&&(u=this.model.fields),!u){n(i.targetElement).hasClass("e-load")&&n(i.targetElement).removeClass(n(i.targetElement).hasClass("e-plus")||n(i.targetElement).hasClass("e-minus")?"e-load":"e-icon e-load");return}o=this._executeDataQuery(u,u.parentId,this._typeOfFieldId=="number"?parseInt(i.currentElement[0].id):i.currentElement[0].id);o.done(function(e){if(r=e.xhr&&e.xhr.responseJSON&&e.xhr.responseJSON.d?e.xhr.responseJSON.d:e.result?e.result:[],r=f._getSortAndFilterList(u,r),r.length>0){f._onlineData=!0;var o=t.getObject(u.parentId,r[0]);f._updateRemoteData(f._newDataSource,o,r,f.model.fields);f._loadChildNodeWhenOnDemand(r,i,u)}else n(i.targetElement).hasClass("e-load")&&n(i.targetElement).removeClass(n(i.targetElement).hasClass("e-plus")||n(i.targetElement).hasClass("e-minus")?"e-load":"e-icon e-load")})}else r=this._getChildNodes(this._dataSource,i),!t.isNullOrUndefined(r)&&r.length>0?setTimeout(function(){f._createSubNodesWhenLoadOnDemand(r,i.targetElement,f.model.fields)},400):n(i.targetElement).hasClass("e-load")&&n(i.targetElement).removeClass(n(i.targetElement).hasClass("e-plus")||n(i.targetElement).hasClass("e-minus")?"e-load":"e-icon e-load")},_loadChildNodeWhenOnDemand:function(n,i,r){var u=this;!t.isNullOrUndefined(n)&&n.length>0&&setTimeout(function(){u._createSubNodesWhenLoadOnDemand(n,i.targetElement,r)},100)},_createSubNodesWhenLoadOnDemand:function(t,i,r){this._loadOnDemandNodes=!0;this._templateNodeCreation(t,r);n(this._fragment.firstChild).attr("style","display:none");this._fragment.firstChild.className="e-treeview-ul";this._fragment.firstChild.setAttribute("role","group");i.parentNode.parentNode.appendChild(this._fragment);var u=n(i).closest(".e-item");u.attr({"aria-expanded":!1,"aria-expanded":!0});n(i).removeClass("e-load").addClass("e-plus");this._expandNode(u);this._finalizeLoadOnDemand(u)},_finalizeLoadOnDemand:function(n){var r,u,i,f;this._beforeBaseClass();this._isRender=!1;this.model.showCheckbox&&(n.children("ul").find("li > div > input.nodecheckbox").ejCheckBox({cssClass:this.model.cssClass,change:this._checkedChange}),r=n.children("ul").find(".e-item"),u=n.children("ul").find(".e-item.unchecked"),u.length>=0&&u.length!=r.length&&this._checkboxChecked&&this.model.autoCheck?r.not(".unchecked").addClass("checked"):u.length==r.length&&this.isNodeChecked(n)&&this._nodeUncheck(n.find("> div > .e-chkbox-wrap > .nodecheckbox:first")[0]),this._isLoaded&&(this._isCheckedAction(),this._updateCheckedNodes()));this._isRender=!0;this._addDragableClass();this._preventEditable();this.model.allowEditing&&this._allowEditable();this._isLoaded&&(this._isSelectedAction(),this._updateSelectedNode(),this._allowOnDemand=!0,this._isExpandedAction(),this._updateExpandedNodes(),this._allowOnDemand=!1);!this.model.loadOnDemand||!this.model.enablePersistence||this.dataSource()instanceof t.DataManager||(f=this._getCookies("_childNodes"),f&&(i=JSON.parse(f)),!t.isNullOrUndefined(i)&&i.indexOf(n[0].id)==-1?i.push(n[0].id):i=[n[0].id],this._setCookies("_childNodes",JSON.stringify(i)))},_getChildNodes:function(n,i){var u=[],e,r,f;if(this._templateType==1){for(r=0,f=n.length;r<f;r++)if(e=t.getObject(this.model.fields.parentId,n[r][0]),!t.isNullOrUndefined(n[r][0])&&!t.isNullOrUndefined(e)&&e!=0&&e==i.id)return n[r]}else{for(r=0,f=n.length;r<f;r++){if(t.getObject(this.model.fields.id,n[r])==i.id&&t.getObject(this.model.fields.parentId,n[r])!=0)return n[r].child;if(n[r].hasOwnProperty("child")&&(u=this._getChildNodes(n[r].child,i),!t.isNullOrUndefined(u)&&u.length>0))break}return u}},_getPath:function(n){for(var i=n.prop("name"),t=n.parents(".e-item:first");t[0]!=null&&t[0].parentNode.id!=this._id;)i=t.prop("name")+"/"+i,t=t.parents(".e-item:first");return this._id+"/"+i},_nodeSelectionAction:function(t,i){var v,o,r,c,l;if(t[0]!=null||t.length!=0){var u=n(t.find("> div > .e-text")[0]),a=!1,y=this.model.selectedNodes.slice(),s=this._liList.index(t[0]);if(this._currNode=t,!this._triggerEvent("beforeSelect",{target:t,nodeDetails:this._getNodeDetails(t)})){if(this.model.allowMultiSelection&&i&&(!i||i.ctrlKey)||(this.element.find("a.e-text.e-active").removeClass("e-node-focus e-active"),this.element.find("[aria-selected=true]").attr("aria-selected",!1),this.element.find(".e-li-active").removeClass("e-li-active"),this.model.fullRowSelect&&(this.element.find(".e-li-focus").removeClass("e-li-focus"),this.element.find(".e-node-focus").removeClass("e-node-focus")),this.element.find("#"+this._id+"_active").removeAttr("id"),this.model.selectedNodes=[],this._deepWatch&&this._removeField(this._newDataSource,this.model.fields,"selected")),this.model.allowMultiSelection&&i&&i.shiftKey){this._startNode||(this._startNode=this._currNode);var h=this.getVisibleNodes(),f=h.index(this._startNode),e=h.index(t[0]);for(f>e&&(v=f,f=e,e=v),o=f;o<=e;o++)r=n(h.get(o)),c=this._liList.index(r[0]),n.inArray(c,this.model.selectedNodes)==-1&&(this.model.selectedNodes.push(c),l=r.find("> div > a.e-text"),l.removeClass("e-node-focus"),r.attr("aria-selected",!0),r.addClass("e-li-active"),l.addClass("e-active"),a=!0,this._deepWatch&&this._updateField(this._newDataSource,r.attr("id"),this.model.fields,"selected",!0))}else this._startNode=t;this.model.allowMultiSelection||(this.model.selectedNode=s);a||(this.element.find("a.e-text.e-node-focus").removeClass("e-node-focus"),t.attr("aria-selected",!0),t.addClass("e-li-active"),u.addClass("e-active"),this.model.allowMultiSelection||(u.closest(".e-item")[0].id!=null&&u.closest(".e-item")[0].id!=""?this.element.attr("aria-activedescendant",u.closest(".e-item")[0].id):u.closest(".e-item").attr("id",this._id+"_active")),n.inArray(s,this.model.selectedNodes)==-1&&this.model.selectedNodes.push(s),this._deepWatch&&this._updateField(this._newDataSource,t.attr("id"),this.model.fields,"selected",!0));this.model.enablePersistence&&this._persistValues(this.model.selectedNodes,"selectedNodes");this._deepWatch&&(this._oldDataSource=JSON.parse(JSON.stringify(this._newDataSource)),this.dataSource(this._newDataSource));this._triggerGivenEvent("nodeSelect",t)}}},_nodeUnSelectionAction:function(n,t){if(this.model.allowMultiSelection&&t&&!t.ctrlKey){this._nodeSelectionAction(n,t);return}n.removeClass("e-li-active");n.attr("aria-selected",!1).find("> div > .e-text").removeClass("e-active").attr("id","");this._updateUnselectModel(n[0]);this._deepWatch&&(this._updateField(this._newDataSource,n.attr("id"),this.model.fields,"selected",!1),this._oldDataSource=JSON.parse(JSON.stringify(this._newDataSource)),this.dataSource(this._newDataSource));this._triggerGivenEvent("nodeUnselect",n)},_updateUnselectModel:function(t){var r=this._liList.index(t),u=this.model.selectedNodes,i;this.model.allowMultiSelection||(this.model.selectedNode=null);i=n.inArray(r,u);i>-1&&this.model.selectedNodes.splice(i,1);this._persistValues(this.model.selectedNodes,"selectedNodes")},_triggerGivenEvent:function(n,t){if(this._isRender){var i={currentElement:t,value:t.find("> div > a.e-text").eq(0).text(),id:t[0].getAttribute("id"),parentId:t.closest("ul").closest(".e-item").attr("id"),selectedNodes:this.model.selectedNodes};this._triggerEvent(n,i)}},_getNodesIndex:function(n){for(var i=[],t=0,r=n.length;t<r;t++)i.push(this._liList.index(n[t]));return i},_nodeEnableAction:function(n){var t=this.getParent(n);t[0]!=null&&t.hasClass("e-node-disable")||(this.model.showCheckbox&&n.find("div > .e-chkbox-wrap > .nodecheckbox").ejCheckBox("enable").prop("disabled",!1),n.find(".e-text").removeClass("e-node-disable").closest(".e-item").removeClass("e-node-disable").removeProp("disabled"))},_nodeDisableAction:function(n){var i,t,r;for(this._collapseNode(n),this.model.showCheckbox&&n.find("div > .e-chkbox-wrap > .nodecheckbox").ejCheckBox("disable").prop("disabled",!0),i=n.find(".e-text.e-active").closest(".e-item"),n.find(".e-text").addClass("e-node-disable").removeClass("e-active").attr("id","").closest(".e-item").addClass("e-node-disable").prop("disabled",!0),t=0,r=i.length;t<r;t++)this._updateUnselectModel(i[t])},_getNodeDetails:function(n){var t,i,r,f,e,o,s,h,c,u;return n[0]!=null&&n.is("LI")&&n.hasClass("e-item")?(t=n[0].getAttribute("id"),i=n.children("div").find(".e-text:first").text(),this.model.template!=null&&(u=this._getNodeObject(t,!0),u.length>0&&(i=u[0])),r=n.closest("ul").closest(".e-item").attr("id"),f=n.parentsUntil(".e-treeview-wrap","ul").length,e=n.find(".e-item").length,o=this._isNodeExpanded(n),s=this._isChecked(n),h=n.find("> div > .e-text").hasClass("e-active"),c=this._liList.index(n),{id:t,text:i,parentId:r?r:"",level:f,count:e,expanded:o,checked:s,selected:h,index:c}):{id:"",text:"",parentId:"",level:"",count:"",expanded:"",checked:"",selected:"",index:""}},_denyMultipleExpand:function(t){for(var r=t.siblings().find(">div>.e-minus").closest(".e-item"),i=0,u=r.length;i<u;i++)this._collapseNode(n(r[i]))},_expandCollapseAction:function(t,i){var e,h,r,u,f,o,s;if(t&&!t.hasClass("e-process")&&(h=this,r=t.closest(".e-item"),t.is("SPAN")&&(t=n(t).parent("div").find("div:first")),e=r.children("ul:first"),e.find("> .e-item").length>0)){if(this.model.enableAnimation&&t.addClass("e-process"),s=n(t).closest(".e-treeview.e-js").data("ejTreeView"),u=s._getNodeDetails(r),o=this.isChildLoaded(r),f={currentElement:r,value:u.text,isChildLoaded:o,id:u.id,parentId:u.parentId,async:!1,isInteraction:i},!this._isNodeExpanded(r)&&o){if(!this._isEventTriggered&&this._triggerEvent("beforeExpand",f))return!1;this._isEventTriggered=!1;r.attr("aria-expanded",!0);this._addExpandedNodes(this._liList.index(r));n(t).removeClass("e-icon e-plus").addClass("e-icon e-minus");r.addClass("e-collapse");this._deepWatch&&this._updateField(this._newDataSource,u.id,this.model.fields,"expanded",!0);this._doAnimation(e,t,"nodeExpand",f,350)}else{if(this.element.find(".e-material-animate").removeClass("e-material-animate"),r.attr("aria-expanded",!1),this._triggerEvent("beforeCollapse",f)===!0)return!1;this._removeExpandedNodes(this._liList.index(f.currentElement));n(t).removeClass("e-icon e-minus").addClass("e-icon e-plus");r.removeClass("e-collapse");this._deepWatch&&this._updateField(this._newDataSource,u.id,this.model.fields,"expanded",!1);this._doAnimation(e,t,"nodeCollapse",f,200)}this._deepWatch&&(this._oldDataSource=JSON.parse(JSON.stringify(this._newDataSource)),this.dataSource(this._newDataSource))}},_doAnimation:function(n,i,r,u,f){var e=this;this.model.enableAnimation?n.animate({height:"toggle"},f,"linear",function(){t.isNullOrUndefined(e.model)||e._doAnimateAction(i,r,u)}):(n.css("display",f==200?"none":"block"),this._doAnimateAction(i,r,u))},_doAnimateAction:function(n,t,i){n.removeClass("e-process");this._triggerEvent(t,i)},_isChecked:function(n){return n.find("> div > .e-chkbox-wrap:first").attr("aria-checked")==="true"?!0:!1},_doRecursiveCheck:function(t,i){var o,r,f,t,u,s,e;u=this._getChildUl(t);o=u?u.querySelectorAll('.e-chkbox-wrap[aria-checked="true"]').length:0;s=u?u.querySelectorAll(".e-item > div > .e-chkbox-wrap > .nodecheckbox"):[];r=t.firstChild.querySelector(".nodecheckbox");r&&r.nodeName.toUpperCase()=="INPUT"&&(e=n(r).data("ejCheckBox"),o==s.length||this.model.autoCheckParentNode?(e.setModel({enableTriState:!1,checked:!0}),r.className+=" checked",f=t.firstChild.getElementsByTagName("a")[0].innerHTML,i.push({id:t.id,text:f}),this._deepWatch&&!this.model.autoCheckParentNode&&this._updateField(this._newDataSource,t.id,this.model.fields,"isChecked",!0),this._addHiddenInputElement(r,f),this._addCheckNodes(this._liList.index(t))):e.setModel({enableTriState:!0,checkState:"indeterminate"}),t=t.parentNode.parentNode,t.nodeName.toUpperCase()=="LI"&&this._doRecursiveCheck(t,i))},_nodeCheck:function(t,i){var s,r,u,h,e,o,c,f,l;if(n(t)[0]!=null){if(this._CurrenctSelectedNodes=[],this._checkedArray=[],t.className+=" checked",s=n(t).data("ejCheckBox"),s){if(s.setModel({enableTriState:!1,checked:!0}),r=t.parentNode.parentNode.parentNode,u=r.firstChild.getElementsByTagName("a")[0].lastChild.nodeValue,this._checkedArray.push({id:r.id,text:u}),this._deepWatch&&this._updateField(this._newDataSource,r.id,this.model.fields,"isChecked",!0),this._addHiddenInputElement(t,u),this._addCheckNodes(this._liList.index(r)),this.model.autoCheck){for(c=this._getChildUl(r),e=c?c.querySelectorAll(".e-item > div > .e-chkbox-wrap > .nodecheckbox"):[],f=0,l=e.length;f<l;f++)n(e[f]).ejCheckBox({checked:!0,enableTriState:!1})[0].className+=" checked",o=e[f].parentNode.parentNode.parentNode,u=o.firstChild.getElementsByTagName("a")[0].lastChild.nodeValue,this._checkedArray.push({id:o.id,text:u}),this._deepWatch&&this._updateField(this._newDataSource,o.id,this.model.fields,"isChecked",!0),this._addHiddenInputElement(e[f],u),this._addCheckNodes(this._liList.index(o));h=r.parentNode.parentNode;h.nodeName.toUpperCase()=="LI"&&this._doRecursiveCheck(h,this._checkedArray)}i&&(i.checknode=this._checkedArray);this._onChecked(n(t),i)}this._deepWatch&&(this._oldDataSource=JSON.parse(JSON.stringify(this._newDataSource)),this.dataSource(this._newDataSource));this._persistValues(this.model.checkedNodes,"checkedNodes")}},_addHiddenInputElement:function(n,t){if(n.firstChild==null){var i=document.createElement("input");i.setAttribute("type","hidden");i.setAttribute("name",this._id+"_Checkbox_"+n.value+"_Text");i.setAttribute("value",t);try{n.appendChild(i)}catch(r){}}},_doRecursiveUncheck:function(t,i){var e,r,o,t,u,s,f;u=this._getChildUl(t);e=u?u.querySelectorAll('.e-chkbox-wrap[aria-checked="true"]').length:0;s=u?u.querySelectorAll(".e-item > div > .e-chkbox-wrap > .nodecheckbox"):[];r=t.firstChild.querySelector(".nodecheckbox");r&&r.nodeName.toUpperCase()=="INPUT"&&(f=n(r).data("ejCheckBox"),e<=0&&(f.setModel({enableTriState:!1,checked:!1}),n(r).removeClass("checked").removeClass("checked").children().remove(),o=t.firstChild.getElementsByTagName("a")[0].innerHTML,this._removeCheckNodes(this._liList.index(t)),i.push({id:t.id,text:o}),this._deepWatch&&this._updateField(this._newDataSource,t.id,this.model.fields,"isChecked",!1)),e>0&&this.model.autoCheckParentNode?(f.setModel({checked:!0}),r.className+=" checked",o=t.firstChild.getElementsByTagName("a")[0].innerHTML,this._addHiddenInputElement(t,o),this._deepWatch&&this._updateField(this._newDataSource,t.id,this.model.fields,"isChecked",!1)):e>0&&!this.model.autoCheckParentNode&&(s.length>1?f.setModel({enableTriState:!0,checkState:"indeterminate"}):f.setModel({checked:!1}),n(r).removeClass("checked").removeClass("checked").children().remove(),this._removeCheckNodes(this._liList.index(t)),this._deepWatch&&this._updateField(this._newDataSource,t.id,this.model.fields,"isChecked",!1)),t=t.parentNode.parentNode,t.nodeName.toUpperCase()=="LI"&&this._doRecursiveUncheck(t,i))},_nodeUncheck:function(t,i){var a,r,s,e,h,o,u,l,f,c;if(n(t)[0]!=null){if(a=this,this._uncheckedArray=[],r=t.parentNode.parentNode.parentNode,n(r).find("div > .e-chkbox-wrap > .nodecheckbox").removeClass("checked").children().remove(),s=n(t).data("ejCheckBox"),this._removeCheckNodes(this._liList.index(r)),s){if(s.setModel({enableTriState:!1,checked:!1}),e=r.firstChild.getElementsByTagName("a")[0].lastChild.nodeValue,this._uncheckedArray.push({id:r.id,text:e}),this._deepWatch&&this._updateField(this._newDataSource,r.id,this.model.fields,"isChecked",!1),this.model.autoCheck){for(h=this._getChildUl(r),o=h?h.querySelectorAll(".e-item > div > .e-chkbox-wrap > .nodecheckbox"):[],u=0,l=o.length;u<l;u++)n(o[u]).ejCheckBox({enableTriState:!1,checked:!1}),f=o[u].parentNode.parentNode.parentNode,e=f.firstChild.getElementsByTagName("a")[0].lastChild.nodeValue,this._uncheckedArray.push({id:f.id,text:e}),this._deepWatch&&this._updateField(this._newDataSource,f.id,this.model.fields,"isChecked",!1),this._removeCheckNodes(this._liList.index(f));c=r.parentNode.parentNode;c.nodeName.toUpperCase()=="LI"&&this._doRecursiveUncheck(c,this._uncheckedArray)}i&&(i.unchecknode=this._uncheckedArray);this._onUnChecked(n(t),i)}this._deepWatch&&(this._oldDataSource=JSON.parse(JSON.stringify(this._newDataSource)),this.dataSource(this._newDataSource));this._persistValues(this.model.checkedNodes,"checkedNodes")}},_executeDataQuery:function(i,r,u){var f,o,e;if(f=t.Query(),f=this._columnToSelect(i),!t.isNullOrUndefined(r)&&r!=""){for(o=n.extend(!0,[],f._params),f._params=[],f.addParams(r,u),e=0;e<o.length;e++)o[e].key!=r&&f.addParams(o[e].key,o[e].value);f.where(r,t.FilterOperators.equal,u)}return i.dataSource.executeQuery(f)},_createChildNodesWhenExpand:function(i,r,u,f,e,o){var a,v,c,y,p,h,s,w,l;if(i.length>0&&i.find("ul .e-item").length==0){if(a=i.attr("id"),v=i.children("div").find(".e-text:first").text(),y=i.children("div").find("div.e-plus:first"),c={currentElement:i,targetElement:y[0],id:a,value:v,isChildLoaded:!1,hasParent:!0,async:!1},s=this,this._isEventTriggered=!0,this._triggerEvent("beforeExpand",c))return!1;if(this.dataSource()instanceof t.DataManager){if(p=c.currentElement.parents("ul.e-treeview-ul").length,h=this._getChildTables(this.model.fields,p,1),t.isNullOrUndefined(h)&&t.isNullOrUndefined(this.model.fields.child)&&(h=this.model.fields),!h){n(c.targetElement).hasClass("e-load")&&n(c.targetElement).removeClass(n(c.targetElement).hasClass("e-plus")||n(c.targetElement).hasClass("e-minus")?"e-load":"e-icon e-load");return}h.query&&h.query.queries.length>0&&(h.query.queries=[]);this._treeList.push("false");w=this._executeDataQuery(h,h.parentId,this._typeOfFieldId=="number"?parseInt(c.currentElement[0].id):c.currentElement[0].id);w.done(function(c){if(s._treeList.pop(),l=c.xhr&&c.xhr.responseJSON&&c.xhr.responseJSON.d?c.xhr.responseJSON.d:c.result?c.result:[],l=s._getSortAndFilterList(h,l),l.length>0){var a=t.getObject(h.parentId,l[0]);s._updateRemoteData(s._newDataSource,a,l,s.model.fields)}s._appendChild(l,i,h,r,u,f);u&&s._expandByLevel(i.find("> ul"),u-1,f);s._treeList.length==0&&(e&&s._doAfterExpand(i,e,o),s._completeRendering());s._parentLevelDropChecking.find("ul .e-item").length==0&&n(s._parentLevelDropChecking).find("> div > .e-icon").remove()})}else l=this._getChildNodes(this._dataSource,{id:i[0].id}),this._appendChild(l,i,this.model.fields,r,u,f)}else this._expandNode(i)},_appendChild:function(i,r,u,f,e,o){var h,s,c;if(!t.isNullOrUndefined(i)&&i.length>0){if(this._checkboxChecked=r.find("> div > .e-chkbox-wrap > .nodecheckbox").hasClass("checked"),this._loadOnDemandNodes=!0,r.find("ul .e-item").length==0&&(this._templateNodeCreation(i,u),r.append(this._fragment)),this._finalizeLoadOnDemand(r),this._expandNode(r),f&&!e&&!o)for(h=r.find(".e-item > div > .e-plus").closest(".e-item"),s=0,c=h.length;s<c;s++)this._createChildNodesWhenExpand(n(h[s]),!0,null,o)}else f&&r.find("> div > .e-plus").removeClass("e-plus e-icon")},_getSortAndFilterList:function(n,i){var r,u;return!i||!i.length||i.length<1?r=[]:this.model.sortSettings.allowSorting?(u=t.Query(),this._addSortingQuery(u,n),r=t.DataManager(i).executeLocal(u)):r=i,r},_addSortingQuery:function(n,i){var r=i&&i.text?i.text:"text",u=this.model.sortSettings.sortOrder==t.sortOrder.Descending?!0:!1;n.sortBy(r,u)},_expandNode:function(n,t){var i,r;return n[0]!=null&&n.length>0&&(i=n.find("> div > div:first"),this.model.loadOnDemand&&!this.model.enablePersistence?n.find("> ul > .e-item").length>0&&i.hasClass("e-plus")?(this.model.enableMultipleExpand||this._denyMultipleExpand(n),this._expandCollapseAction(n.find("> div > div.e-plus:first"))):i.hasClass("e-icon")&&!i.hasClass("e-minus")&&this._allowOnDemand&&this._createChildNodesWhenExpand(n):i.hasClass("e-plus")&&(r=n.find("> ul > .e-item").length>0?!0:!1,r&&(this.model.enableMultipleExpand||this._denyMultipleExpand(n),this._expandCollapseAction(i,t)))),!0},_collapseNode:function(n,t){if(n[0]!=null&&n.length>0&&n.find("> ul > .e-item").length>0){var i=n.find("> div > div:first");i.hasClass("e-minus")&&this._expandCollapseAction(i,t)}},_getElement:function(){return this.element.is("UL")?this.element:this.element.find("> ul")},_getImmediateChild:function(n,t){return t?n.find("> .e-item:not(:hidden)"):n.find("> .e-item")},_expandByLevel:function(n,t,i){t>0&&this._expandByNode(this._getImmediateChild(n,i),t,i)},_expandByNode:function(t,i,r){for(var u=0,f=t.length;u<f;u++)n(t[u]).find("> div > div").hasClass("e-plus")&&(this.model.loadOnDemand?this._createChildNodesWhenExpand(n(t[u]),!0,i,r):this._expandNode(n(t[u]))),this._expandByLevel(n(t[u]).find("> ul"),i-1,r)},_expandAll:function(t){var f=this.element,i,u,r;if(r=f.find(t?".e-item > div > .e-plus:not(:hidden)":".e-item > div > .e-plus").closest(".e-item"),this.model.loadOnDemand)for(i=0,u=r.length;i<u;i++)this._createChildNodesWhenExpand(n(r[i]),!0,null,t);else for(i=0,u=r.length;i<u;i++)this._expandNode(n(r[i]))},_collapseByLevel:function(n,t,i){t>0&&this._collapseByNode(this._getImmediateChild(n,i),t,i)},_collapseByNode:function(t,i,r){for(var u=0,f=t.length;u<f;u++)n(t[u]).find("> div > div").hasClass("e-minus")&&this._collapseNode(n(t[u])),this._collapseByLevel(n(t[u]).find("> ul"),i-1,r)},_collapseAll:function(t){var f=this.element,i,r,u;if(r=f.find(t?".e-item > div > .e-minus:not(:hidden)":".e-item > div > .e-minus").closest(".e-item"),r.length>0)for(i=0,u=r.length;i<u;i++)this._collapseNode(n(r[i]))},_checkAll:function(){var r,t,i,u;for(this._CurrenctSelectedNodes=[],r=this.element,t=r.find(".e-item > div > .e-chkbox-wrap > .nodecheckbox"),t.addClass("checked"),i=0,u=t.length;i<u;i++)n(t[i]).ejCheckBox("setModel",{checked:!0}),this._addHiddenInputElement(t[i],n(t[i]).parent().siblings(".e-text")[0].lastChild.nodeValue);this.model.checkedNodes=this.getCheckedNodesIndex()},_uncheckAll:function(){var u=this.element,t,i,r;for(t=u.find(".e-item > div > .e-chkbox-wrap > .nodecheckbox"),t.removeClass("checked").children().remove(),i=0,r=t.length;i<r;i++)n(t[i]).ejCheckBox("setModel",{checked:!1});this.model.checkedNodes=[];this.model.checkedNodes.push(-1)},_selectAll:function(){var r,t,u,i;for(this._isRender=!1,r={ctrlKey:!0},t=0,u=this._liList.length;t<u;t++)if(i=n(this._liList[t]),i[0]!=null&&this._nodeSelectionAction(i,r),!this.model.allowMultiSelection)break;this._isRender=!0},_unselectAll:function(){this.element.find("a.e-text.e-active").removeClass("e-node-focus e-active");this.element.find("[aria-selected=true]").attr("aria-selected",!1);this.element.find(".e-li-active").removeClass("e-li-active");this.element.find(".e-li-focus").removeClass("e-li-focus");this.element.find(".e-node-focus").removeClass("e-node-focus");this.model.selectedNodes=[];this.model.enablePersistence&&this._persistValues(this.model.selectedNodes,"selectedNodes")},_isNodeExpanded:function(n){if(n[0]!=null)return n.children("ul:first").length>0&&n.find("> div > div.e-minus").length>0},_showCheckBox:function(){for(var t=this.element,i=t.find("li"),n=0;n<i.length;n++)this._checkboxOnTemplate(i[n].children[0]);t.find(".e-item > div > .nodecheckbox").ejCheckBox({cssClass:this.model.cssClass,change:this._checkedChange});t.find(".e-item.e-node-disable > div > .e-chkbox-wrap > .nodecheckbox").ejCheckBox("disable")},_drag:function(){var i,u=!1,o=t.browserInfo(),r=null,s=null,f=null,e;e=o.name=="msie"&&o.version=="8.0"?!0:!1;this.element.is("ul")?(this._treeView=this.element.parent(),this.model.allowDragAndDropAcrossControl||(f=this.element.parent())):(this._treeView=this.element,this.model.allowDragAndDropAcrossControl||(f=this.element));n(this._treeView).find("ul li div a").not(".e-js").ejDraggable({autoScroll:!0,dragArea:f,clone:!0,dragStart:function(u){if(!i||t.isNullOrUndefined(u.target)||n(u.target).hasClass("e-node-disable")||i.element.find(".e-item > div > .e-text.e-editable").length!=0||(u.element.attr("aria-grabbed",!0),i.element.closest(".e-treeview-wrap").addClass("e-dragging"),n(u.target).is("A")&&!n(u.target).hasClass("e-draggable")))return!1;e&&(document.ondragstart=function(){return!1});var f={target:n(u.target).closest(".e-item"),targetElementData:i._getNodeDetails(n(u.target).closest(".e-item")),dragTarget:u.target,parentElement:n(u.target).closest("ul").closest(".e-item"),parentElementData:i._getNodeDetails(n(u.target).closest("ul").closest(".e-item")),event:u.event};if(i._triggerEvent("nodeDragStart",f))return u.cancel=!0,r&&r.remove(),!1},drag:function(f){var e,s,l,o,h,c,a;return u=!1,n(f.target).hasClass("e-sibling")&&(f.target=f.target.parentElement),n(".e-sibling").remove(),e=f.target,s=i._findTarget(n(e)),n(e).closest(".e-treeview-wrap").addClass("e-dragging"),o=n(e).closest(".e-treeview.e-js").data("ejTreeView"),o=o?o:i,n(r).css({"margin-top":f.event.clientY-20<0?"0px":"10px"}),n(e).closest(".e-treeview.e-js")!=null&&(h=n(e).closest(".e-treeview.e-js").data("ejTreeView")),l=h?{draggedElement:n(f.element).closest(".e-item"),draggedElementData:i._getNodeDetails(n(f.element).closest(".e-item")),dragTarget:e,target:s,targetElementData:h._getNodeDetails(s),event:f.event}:{draggedElement:n(f.element).closest(".e-item"),draggedElementData:i._getNodeDetails(n(f.element).closest(".e-item")),dragTarget:e,target:s,targetElementData:i._getNodeDetails(s),event:f.event},(n(f.element).parent().parent().has(n(e)).length==0||i.model.allowMultiSelection&&i.model.selectedNodes.length>1)&&(n(e).hasClass("e-droppable")||n(e).parent().hasClass("e-droppable"))&&n(e).hasClass("e-dropchild")&&!n(e).hasClass("e-node-disable")&&(i.model.allowDragAndDropAcrossControl||!i.model.allowDragAndDropAcrossControl&&n(e).parents(".e-treeview").is(i.element))?(document.body.style.cursor="",n(r).find("span.e-dropedStatus").removeClass().addClass("e-dropedStatus e-icon e-plus"),n(e).addClass("allowDrop")):o.model.allowDropChild&&o.model.allowDropSibling&&(n(e).hasClass("e-droppable")&&!n(e).hasClass("e-item")&&!n(e).hasClass("e-text")||n(e).is("UL")&&n(e).hasClass("e-ul")&&n(e).find(".e-item").length==0&&n(e).parent(".e-treeview-wrap").length>0)?(document.body.style.cursor="",n(r).find("span.e-dropedStatus").removeClass().addClass("e-dropedStatus e-icon e-plus")):(n(e).hasClass("e-sibling")||n(e).find("a").hasClass("e-text"))&&(n(e).hasClass("e-sibling")||n(e).parent().parent().hasClass("e-item")||n(e).parent().hasClass("e-item")||n(e).hasClass("e-item")||n(e).hasClass("e-text"))&&(e.nodeName.toUpperCase()!="LI"&&(e.parentElement===null||e.parentElement.nodeName.toUpperCase()!="LI")||o.model.allowDropSibling)&&(i.model.allowDragAndDropAcrossControl||n(e).parents(".e-treeview").is(i.element))||(document.body.style.cursor="not-allowed",n(r).find("span.e-dropedStatus").removeClass().addClass("e-dropedStatus e-icon e-minus"),n(e).removeClass("showline-hover"),n(e).removeClass("noline-hover")),e.nodeName!="A"&&(n(f.element).parent().parent().has(n(e)).length==0||i.model.allowMultiSelection&&i.model.selectedNodes.length>1)&&n(f.element).parent().parent()[0]!=n(e)[0]?(e.nodeName=="UL"&&n(e).children()[0]!=null&&(e=n(e).children()[0],u=!0),e.nodeName!="LI"&&(e=n(e).closest(".e-droppable")[0]||n(e).parent()),e.nodeName=="LI"&&n(e).hasClass("e-droppable")&&n(e).hasClass("e-dropsibling")&&(i.model.allowDragAndDropAcrossControl||!i.model.allowDragAndDropAcrossControl&&n(e).parents(".e-treeview").is(i.element))?(c=t.buildTag("div.e-sibling"),a=n(e).offset().top+n(e).find("a").height()||-1,u=f.event.pageY>a?!1:!0,u?c.insertBefore(n(e).find("> div > a").parent()):c.insertAfter(n(e).find("> div > a").parent()),n(e).parents().hasClass("e-rtl")?(document.body.style.cursor="",n(r).find("span.e-dropedStatus").removeClass().addClass("e-dropedStatus e-icon e-insertInbetween-rtl")):(document.body.style.cursor="",n(r).find("span.e-dropedStatus").removeClass().addClass("e-dropedStatus e-icon e-insertInbetween"))):e.nodeName=="A"&&n(e)&&n(e).hasClass("e-droppable")&&n(e).hasClass("e-dropchild")?(document.body.style.cursor="",n(r).find("span.e-dropedStatus").removeClass().addClass("e-dropedStatus e-icon e-plus"),n(e).addClass("allowDrop")):!n(e).hasClass("e-droppable")||n(e).hasClass("e-item")||n(e).hasClass("e-text")||(document.body.style.cursor="",n(r).find("span.e-dropedStatus").removeClass().addClass("e-dropedStatus e-icon e-plus"))):((n(f.element).parent().parent().has(n(e)).length==1||n(f.element).parent().parent().is(n(e)))&&(!i.model.allowMultiSelection||i.model.allowMultiSelection&&i.model.selectedNodes.length<=1)&&(document.body.style.cursor="not-allowed",n(r).find("span.e-dropedStatus").removeClass().addClass("e-dropedStatus e-icon e-minus")),n(".e-sibling").remove()),i._triggerEvent("nodeDrag",l)?!1:void 0},dragStop:function(t){var f,o,c,s,h,v,a,l,y;if((e&&(document.ondragstart=function(){return!0}),t.element.dropped||(r&&r.remove(),document.body.style.cursor=""),f=t.target,n(f).closest(".e-treeview-wrap").removeClass("e-dragging"),i.element.closest(".e-treeview-wrap").removeClass("e-dragging"),f.className=="e-sibling"&&(f=n(f).closest(".e-item")[0]),n(".e-sibling").remove(),n(f).hasClass("e-node-disable"))||(o=u?"Before":"After",s=i._findTarget(n(f)),o=f.nodeName=="A"?"Over":o,n(f).closest(".e-treeview.e-js")!=null&&(h=n(f).closest(".e-treeview.e-js").data("ejTreeView")),c=h?{draggedElementData:i._getNodeDetails(n(t.element).closest(".e-item")),draggedElement:n(t.element).closest(".e-item"),dropTarget:n(f),target:s,targetElementData:h._getNodeDetails(s),position:o,event:t.event,preventTargetExpand:!1}:{draggedElementData:i._getNodeDetails(n(t.element).closest(".e-item")),draggedElement:n(t.element).closest(".e-item"),dropTarget:n(f),target:s,targetElementData:i._getNodeDetails(s),position:o,event:t.event,preventTargetExpand:!1},i._triggerEvent("nodeDragStop",c)))return!1;if(v=i._isParentExpand(f),i.model.allowMultiSelection&&n(t.element).hasClass("e-active"))for(a=i.element.find(".e-item > div > .e-active"),l=0,y=a.length;l<y;l++)t.element=a[l],o=i._dropNode(f,t,o,u),o=="After"&&n(f).closest(".e-treeview.e-js").length&&(f=f.nodeName.toUpperCase()=="LI"?f.nextElementSibling:f.closest("li").nextElementSibling);else o=i._dropNode(f,t,o,u);if(c.preventTargetExpand&&!v&&i._preventParentNode(f),n(".allowDrop").removeClass("allowDrop"),n(t.element).attr("aria-grabbed",!1),n(f).hasClass("e-dropchild")||r&&r.remove(),s=i._findTarget(n(f)),c=h?{droppedElementData:h._getNodeDetails(n(t.element).closest(".e-item")),droppedElement:n(t.element).closest(".e-item"),dropTarget:n(f),target:s,targetElementData:h._getNodeDetails(s),position:o,event:t.event}:{droppedElementData:i._getNodeDetails(n(t.element).closest(".e-item")),droppedElement:n(t.element).closest(".e-item"),dropTarget:n(f),target:s,targetElementData:i._getNodeDetails(s),position:o,event:t.event},i._triggerEvent("nodeDropped",c))return!1;document.body.style.cursor=""},helper:function(u){var e,o,f,h;if(!t.isNullOrUndefined(u.element)&&!n(u.element).hasClass("e-node-disable")&&n(u.element).hasClass("e-draggable")&&(i=n(u.element).closest(".e-treeview.e-js").data("ejTreeView"),i))return r=t.buildTag("div.e-dragedNode e-widget"),e=t.util.getZindexPartial(i.element),r.css({"z-index":e}),r.addClass(i.model.cssClass+(i.model.enableRTL?" e-rtl":"")),o=i.element.find(".e-item > div > .e-active"),f=o.length,s=n(u.element).clone().addClass("dragClone"),this.spanEle=t.buildTag("span.e-icon e-plus e-dropedStatus"),r.append(this.spanEle),r.append(s),h=t.buildTag("span.e-drop-count",f),f>1&&i.model.allowMultiSelection&&n(u.element).hasClass("e-active")&&r.append(h),r.appendTo(n("body"))}})},_isParentExpand:function(t){var i=n(t).closest(".e-item"),r=!1;return i.length>0&&i[0].getAttribute("aria-expanded")=="true"&&(r=!0),r},_preventParentNode:function(t){var i=n(t).closest(".e-item"),u=n(i[0]).find(".e-treeview-ul"),r;n(u).css("display","none");i.length>0&&(i[0].setAttribute("aria-expanded",!1),r=n(i).find(".e-minus"),n(r).removeClass("e-minus").addClass("e-plus"))},_dropNode:function(t,i,r,u){var f=n(t).closest(".e-treeview.e-js").data("ejTreeView");return t.nodeName=="A"&&n(t).hasClass("e-dropchild")&&n(t).hasClass("e-droppable")||t.nodeName=="UL"&&n(t).children().length==0?(r="Over",n(t).is("UL")&&n(t).hasClass("e-ul")&&n(t).find(".e-item").length==0&&n(t).parent(".e-treeview-wrap").length>0&&f.model.allowDropChild?this._dropAsChildNode(n(t),n(i.element),i.event):f.model.allowDropChild&&n(i.element).parent().parent().has(n(t)).length==0&&(n(t).parent().parent().has(n(i.element)).length==0||this._isDescendant(n(t).parents("li:last").find(">ul>li"),n(i.element).parents("li:first")[0]))&&(this.model.allowDragAndDropAcrossControl||!this.model.allowDragAndDropAcrossControl&&n(t).parents(".e-treeview").is(this.element))&&this._dropAsChildNode(n(t).closest(".e-item"),n(i.element),i.event)):(t.nodeName=="UL"&&(t=n(t).children()[0]),t.nodeName!="LI"&&(t=n(t).closest(".e-droppable")[0]||n(t).parent()),t.nodeName=="LI"&&n(t).hasClass("e-dropsibling")&&n(t).hasClass("e-droppable")?f.model.allowDropSibling&&n(i.element).parent().parent().has(n(t)).length<1&&n(i.element).parent().parent()[0]!=n(t)[0]&&(this.model.allowDragAndDropAcrossControl||!this.model.allowDragAndDropAcrossControl&&n(t).parents(".e-treeview").is(this.element))&&this._dropAsSiblingNode(n(t),n(i.element),u,i.event):t.nodeName=="A"&&n(t).hasClass("e-dropchild")&&n(t).hasClass("e-droppable")&&(r="Over",f.model.allowDropChild&&n(i.element).parent().parent().has(n(t)).length==0&&(n(t).parent().parent().has(n(i.element)).length==0||this._isDescendant(n(t).parents("li:last").find(">ul>li"),n(i.element).parents("li:first")[0]))&&this._dropAsChildNode(n(t).closest(".e-item"),n(i.element),i.event))),r},_findTarget:function(n){return n.hasClass("e-text")||n.parents("a").hasClass("e-text")?n.closest(".e-item"):n.closest("ul").closest(".e-item").length>0?n.closest("ul").closest(".e-item"):this.element.find(n).length>0&&!this.model.template?n.parents("ul").first():this.element.find(n).length>0&&this.model.template?n.closest(".e-text").length>0?n.closest(".e-item"):n.parents("ul").first():n},_isDescendant:function(t,i){var r=!0;return n(t).each(function(n,t){if(t==i)return r=!1,!1;r=!0}),r},_childDrop:function(){n(this._treeView).find("ul .e-item div .e-text").ejDroppable({accept:n(this._treeView).find("ul .e-item div .e-text").addClass("e-dropchild"),drop:function(t,i){n(i.helper).hide()}})},_siblingDrop:function(){n(this._treeView).find("ul .e-item").addClass("e-dropsibling").ejDroppable({drop:function(t,i){n(i.helper).hide()}})},_dropAsSiblingNode:function(i,r,u){var f=r.parent().parent(),f=n(f),e;e=n(r.parents(".e-item")[1]);t.isNullOrUndefined(this.model.fields)||this.dataSource()==null||this.dataSource()instanceof t.DataManager||this._updateDataSource(r.parents(".e-item:first"),n(i),u,this);u?f.insertBefore(i):f.insertAfter(i);this.model.template||this._autoGenerateNodes(r.parents(".e-item:first"));this._modifyCss(e);this._isRender=!1;this._updateCheckState(f);this._updateCheckState(e);this._isRender=!0;f.find("> div > .e-minus").length>0&&!this.model.enableMultipleExpand&&this._denyMultipleExpand(f);this._updateChanges(n(i))},_dropAsChildNode:function(i,r){var u=r.parent().parent(),u=n(u),f;f=this._parentLevelDropChecking=n(r.parents(".e-item")[1]);n(i).is("UL")?n(i).append(u):this._appendNode(i,u);t.isNullOrUndefined(this.model.fields)||this.dataSource()==null||this.dataSource()instanceof t.DataManager||this._updateDataSource(r.parents(".e-item:first"),n(i).find("> div > .e-text").first(),"",this);this.model.template||this._autoGenerateNodes(r.parents(".e-item:first"));this._modifyCss(f);this._isRender=!1;this._updateCheckState(u);this._updateCheckState(f);this._isRender=!0;i.find("> div > .e-icon.e-plus:first").length>0&&this._expandNode(i);this._updateChanges(n(i))},_updateChanges:function(t){if(n(t)[0]!=null){var i=this,r=0;do i._updateNodes(),r==1&&i._updateTarget(this),r++,i=t.closest(".e-treeview.e-js").data("ejTreeView");while(i&&this._id!=i._id&&r==1)}},_updateTarget:function(n){this.model.allowDropChild!=n.model.allowDropChild&&(this.model.allowDropChild?this._childDrop():this._preventDropChild());this.model.allowDropSibling!=n.model.allowDropSibling&&(this.model.allowDropSibling?this._siblingDrop():this._preventDropSibling())},_updatePersistProb:function(){var f,t,s,e,i,o,u,r;for(this._removeField(this._newDataSource,this.model.fields,"selected"),this._removeField(this._newDataSource,this.model.fields,"expanded"),this._removeField(this._newDataSource,this.model.fields,"isChecked"),f=this.getSelectedNodes(),t=0,s=f.length;t<s;t++)this._updateField(this._newDataSource,n(f[t]).attr("id"),this.model.fields,"selected",!0);for(e=this.getExpandedNodes(),i=0,r=e.length;i<r;i++)this._updateField(this._newDataSource,n(e[i]).attr("id"),this.model.fields,"expanded",!0);if(this.model.showCheckbox)for(o=this.getCheckedNodes(),u=0,r=o.length;u<r;u++)this._updateField(this._newDataSource,n(o[u]).attr("id"),this.model.fields,"isChecked",!0)},_orderDataSource:function(){var f,r,u,e,i,o;if(this._beforeBaseClass(),this._updatePersistProb(),f=[],r=JSON.parse(JSON.stringify(this._newDataSource)),this.dataSource()instanceof t.DataManager&&this._templateType==2)return this._newDataSource;for(u=0,e=this._liList.length;u<e;u++)for(i=0,o=r.length;i<o;i++)n(document.getElementById(t.getObject(this.model.fields.id,r[i]))).length>0&&!t.isNullOrUndefined(r[i])&&!t.isNullOrUndefined(r[i][this.model.fields.id])&&r[i][this.model.fields.id].toString()==n(this._liList[u]).attr("id")&&(n(document.getElementById(t.getObject(this.model.fields.id,r[i]))).find("> ul .e-item").length==0?(this._deepPropertyDelete(this.model.fields.hasChild,r[i]),this._templateType==2&&delete r[i].child):this._templateType==1?t.createObject(this.model.fields.hasChild,!0,r[i]):this._updateChildData(r[i].child,this.model.fields),f.push(r[i]));return f},_updateDataSource:function(i,r,u,f){var o,e,c,s,l,h,a;n(i)[0]!=null&&n(r)[0]!=null&&(o=f,e=r.closest(".e-treeview.e-js").data("ejTreeView"),!o||!e||t.isNullOrUndefined(e.model.fields)||e.dataSource()==null||e.dataSource()instanceof t.DataManager||(r.is("A")?(c=r.closest(".e-item").attr("id"),l=r.closest(".e-item").find("> ul > .e-item"),s=l.length>0?l.length:r.closest(".e-item").index()):(c=r.parents(".e-item:first").attr("id"),h=r.next(".e-item"),a=r.prev(".e-item"),s=h.length>0?s=a.length>0?h.index()-1:u?r.index():h.index():u?r.index():r.index()+1),e&&o._findAndUpdate(i.attr("id"),c,o._newDataSource,s,e,r,u)))},_removeObject:function(n,i,r){for(var u=0,f=n.length;u<f;u++)if(!t.isNullOrUndefined(n[u])&&!t.isNullOrUndefined(n[u][r])&&n[u][r].toString()==i){n.splice(u,1);break}},_getObjectPosition:function(n,i,r){for(var f,u=0,e=n.length;u<e;u++){if(!t.isNullOrUndefined(n[u])&&!t.isNullOrUndefined(n[u][r])&&n[u][r].toString()==i)return u;if(n[u].hasOwnProperty("child")&&(f=this._getObjectPosition(n[u].child,i,r),f>=0))return f}return-1},_convertDataSourceTypes:function(n,i){for(var r=0;r<n.child.length;r++)t.createObject(this.model.fields.parentId,t.getObject(this.model.fields.id,n),n.child[r]),i.push(n.child[r]),n.child[r].hasOwnProperty("child")&&n.child[r].child.length>0&&this._convertDataSourceTypes(n.child[r],i);return n.child&&(delete n.child,t.createObject(this.model.fields.hasChild,!0,n)),i},_childObjectCollection:function(n,i,r,u){for(var e,f=0,o=n.length;f<o;f++)e=this._getChildNodes(i,{id:t.getObject(this.model.fields.id,n[f])}),e&&(u==2?(r[f].hasOwnProperty("child")||(r[f].child=[]),r[f].child=this._childObjectCollection(e,i,e,u)):(r=r.concat(e),r=this._childObjectCollection(e,i,r,u)));return r},_updateCopyData:function(t,r,u){var s=r.model.fields,o=u,f=JSON.parse(JSON.stringify(u[t])),e=this,h;return n.each(this.model.fields,function(n){(n!=="dataSource"||n!=="query")&&o[t][e.model.fields[n]]!==i&&(delete f[e.model.fields[n]],f[s[n]]=o[t][e.model.fields[n]])}),n.extend(h,f,!1)},_updateDataFields:function(n,i,r,u,f){for(var o=[],e=0,s=i.length;e<s;e++)f==1&&(this._removeObject(n,r,this.model.fields.parentId),this._deepPropertyDelete(this.model.fields.parentId,i[e]),this._deepPropertyDelete(this.model.fields.hasChild,i[e])),i[e].hasOwnProperty("child")&&(i[e].child=this._updateDataFields(n,i[e].child,t.getObject(this.model.fields.id,i[e]),u,f)),o.push(this._updateCopyData(e,u,i));return o},_deepPropertyDelete:function(n,r){if(!r||t.isNullOrUndefined(n))return i;var u=n.split(".");u.length>1?deepDelete(u.slice(1).join("."),r[u[0]]):delete r[n]},_findAndUpdate:function(n,i,r,u,f,e,o){var k,a,w,d,b,it,y,nt,c,p,s,v,h;if(!t.isNullOrUndefined(r))if(this._templateType==1){for(c=0,p=r.length;c<p;c++)if(k=t.getObject(this.model.fields.id,r[c]),!t.isNullOrUndefined(r[c])&&!t.isNullOrUndefined(k)&&k.toString()==n){if(this._id==f._id)t.isNullOrUndefined(i)?this._deepPropertyDelete(this.model.fields.parentId,r[c]):t.createObject(this.model.fields.parentId,i,r[c]),a=this.dataSource(),w=this,this._deepWatch&&(d=r.splice(c,1),h=this._getObjectPosition(a,e.attr("id"),this.model.fields.id),h>=0&&!e.is("A")?r.splice(o?h:h+1,0,d[0]):r.push(d[0]),this._oldDataSource=JSON.parse(JSON.stringify(r)),this.dataSource(r)),this._templateType=this._getTemplateType(a,this.model.fields),this._dataSource=this._templateType==1?this._groupingObjects(a,function(n){return[!t.isNullOrUndefined(n)&&[t.getObject(w.model.fields.parentId,n)].toString()]}):a;else{var s=[],l,w=this,g,v=[],tt=[];if(g=this._groupingObjects(r,function(n){return[!t.isNullOrUndefined(n)&&[t.getObject(w.model.fields.parentId,n)].toString()]}),tt=r.splice(c,1),s.push(this._updateCopyData(0,f,tt)),l=this._getChildNodes(g,{id:n}),l&&l.length>0&&(l=this._childObjectCollection(l,g,l,f._templateType)),f._templateType==2)l&&l.length>0&&(y=this._updateDataFields(r,l,n,f,this._templateType),s[0].hasOwnProperty("child")||(s[0].child=[]),s[0].child=y),this._deepPropertyDelete(f.model.fields.parentId,s[0]),this._deepPropertyDelete(f.model.fields.hasChild,s[0]),f._deepWatch?(h=this._getObjectPosition(f._newDataSource,e.attr("id"),f.model.fields.id),h=h>=0&&!e.is("A")?o?h:h+1:u,i?f._changeObjectPos(i,s,f._newDataSource,h):f._newDataSource.splice(h,0,s[0])):i?f._changeObjectPos(i,s,f._newDataSource,u):f._newDataSource.splice(u,0,s[0]);else{if(l&&l.length>0){for(b=0,it=l.length;b<it;b++)this._removeObject(r,t.getObject(this.model.fields.id,l[b]),this.model.fields.id);this._dataSource=this._templateType==1?this._groupingObjects(r,function(n){return[!t.isNullOrUndefined(n)&&[t.getObject(w.model.fields.parentId,n)].toString()]}):r}if(i?t.createObject(f.model.fields.parentId,i,s[0]):this._deepPropertyDelete(f.model.fields.parentId,s[0]),f._deepWatch?(h=this._getObjectPosition(f._newDataSource,e.attr("id"),f.model.fields.id),h>=0&&!e.is("A")?f._newDataSource.splice(o?h:h+1,0,s[0]):f._newDataSource.push(s[0])):f._newDataSource.push(s[0]),y=[],l&&l.length>0){if(!(this.dataSource()instanceof t.DataManager)){for(c=0;c<l.length;c++)y.push(this._updateCopyData(c,f,l));l=y}nt=f._newDataSource=f._newDataSource.concat(l);f._dataSource=f._templateType==1?f._groupingObjects(nt,function(n){return[!t.isNullOrUndefined(n)&&[t.getObject(f.model.fields.parentId,n)].toString()]}):nt}}f._deepWatch&&(f.dataSource(f._newDataSource),f._oldDataSource=JSON.parse(JSON.stringify(f._newDataSource)));this._deepWatch&&(this._oldDataSource=JSON.parse(JSON.stringify(this._newDataSource)),this.dataSource(this._newDataSource))}break}}else for(c=0,p=r.length;c<p;c++){if(t.getObject(this.model.fields.id,r[c]).toString()==n)return s=r.splice(c,1),this._id==f._id?this._deepWatch?(h=this._getObjectPosition(this._newDataSource,e.attr("id"),this.model.fields.id),h=h>=0&&!e.is("A")?o?h:h+1:u,i?this._changeObjectPos(i,s,this._newDataSource,h):this._newDataSource.splice(h,0,s[0]),this._oldDataSource=JSON.parse(JSON.stringify(this._newDataSource)),this.dataSource(this._newDataSource)):i?this._changeObjectPos(i,s,this._newDataSource,u):this._newDataSource.splice(u,0,s[0]):(s=this._updateDataFields(r,s,n,f,this._templateType),f._templateType==1?(v=[],s[0].hasOwnProperty("child")?(v=f._convertDataSourceTypes(s[0],v),s[0]=s.concat(v)):s[0]=s.concat(),i?t.createObject(f.model.fields.parentId,i,s[0][0]):this._deepPropertyDelete(f.model.fields.parentId,s[0][0]),f._deepWatch?(h=this._getObjectPosition(f._newDataSource,e.attr("id"),f.model.fields.id),h>=0&&!e.is("A")?f._newDataSource.splice(o?h:h+1,0,s[0][0]):f._newDataSource.push(s[0][0]),f._newDataSource=f._newDataSource.concat(v)):f._newDataSource=f._newDataSource.concat(s[0])):f._deepWatch?(h=this._getObjectPosition(f._newDataSource,e.attr("id"),f.model.fields.id),h=h>=0&&!e.is("A")?o?h:h+1:u,i?f._changeObjectPos(i,s,f._newDataSource,h):f._newDataSource.splice(h,0,s[0])):i?f._changeObjectPos(i,s,f._newDataSource,u):f._newDataSource.splice(u,0,s[0]),f._deepWatch&&(f.dataSource(f._newDataSource),f._oldDataSource=JSON.parse(JSON.stringify(f._newDataSource))),this._deepWatch&&(this._oldDataSource=JSON.parse(JSON.stringify(this._newDataSource)),this.dataSource(this._newDataSource))),!0;if(r[c].hasOwnProperty("child")&&this._findAndUpdate(n,i,r[c].child,u,f,e,o))break}},_changeObjectPos:function(n,i,r,u){for(var f=0,e=r.length;f<e;f++){if(t.getObject(this.model.fields.id,r[f]).toString()==n)return r[f].hasOwnProperty("child")?r[f].child.splice(u,0,i[0]):(r[f].child=[],r[f].child.push(i[0])),!0;if(r[f].hasOwnProperty("child")&&this._changeObjectPos(n,i,r[f].child,u))break}},_finalizeEditing:function(t){if(n(t)[0]!=null){var i=t.closest(".e-treeview.e-js").data("ejTreeView");i&&(i._preventEditable(),i.model.allowEditing&&i._allowEditable())}},_updateCheckState:function(n){var t,i,r;this._isTreeElement(n)&&(t=n.closest(".e-treeview.e-js").data("ejTreeView"),t&&t.model.showCheckbox&&(i=n.children("ul").find(".e-item > div > .e-chkbox-wrap > .checked").closest(".e-item").addClass("checked"),i.length>0?t._isCheckedAction():(r=t.isNodeChecked(n)?"_nodeCheck":"_nodeUncheck",t[r](n.find("> div > .e-chkbox-wrap > .nodecheckbox:first")[0]))))},_autoGenerateNodes:function(t){var i,u,r,f;if(n(t)[0]!=null)if(i=t.closest(".e-treeview.e-js").data("ejTreeView"),i&&i.model.showCheckbox){if(t.find(".e-chkbox-wrap").length==0){for(this._checkboxOnTemplate(t[0].children[0]),u=t.find(".e-item"),r=0,f=u.length;r<f;r++)this._checkboxOnTemplate(u[r].children[0]);t.find(".nodecheckbox").ejCheckBox({cssClass:i.model.cssClass,change:i._checkedChange})}}else t.find(".e-chkbox-wrap").remove()},_appendNode:function(i,r){var u,o,f,s,e;for(this._isTreeElement(i)?(i.find("ul.e-treeview-ul")[0]==null?(u=i.closest(".e-treeview.e-js").data("ejTreeView"),u&&(s=this._isNodeExpanded(i),!s&&u.model.loadOnDemand&&i.find("> div > div").first().hasClass("e-plus")&&u._createChildNodesWhenExpand(i,null,null,null,r,u)),this.dataSource()instanceof t.DataManager&&u.model.loadOnDemand&&i.find("> div > div").first().hasClass("e-plus")||this._doAfterExpand(i,r,u),this._deepWatch&&this._templateType==1&&(this._updateField(this._newDataSource,i.attr("id"),this.model.fields,"hasChild",!0),this._oldDataSource=JSON.parse(JSON.stringify(this._newDataSource)),this.dataSource(this._newDataSource))):n(i.find("ul.e-treeview-ul")[0]).append(r),this.isDisabled(i)||this._expandNode(i)):this.element.is("UL")?this.element.append(r):this.element.children("ul:first").length>0?this.element.children("ul:first").append(r):(o=t.buildTag("ul.e-treeview-ul","",{},{role:"group"}),o.append(r),this.element.append(o)),f=this.element.find(".e-item.expanded"),e=0;e<f.length;e++)this.model.loadOnDemand&&!(this.dataSource()instanceof t.DataManager)?this._createChildNodesWhenExpand(n(f[e])):this._expandNode(n(f[e]));f.removeClass("expanded");this.model.showCheckbox&&this._isCheckedAction()},_doAfterExpand:function(i,r,u){var e=t.buildTag("ul.e-treeview-ul","",{},{role:"group",style:"display:none"}),f;n(e).append(n(r));n(i.find("div")[1]).length==0?(f=document.createElement("div"),f.setAttribute("role","presentation"),f.className="e-icon e-minus",n(i.find("div")).append(n(f))):n(i.find("div")[1]).hasClass("e-minus")||n(i.find("div")[1]).addClass("e-icon e-plus");u&&u.model.loadOnDemand&&i.find("ul.e-treeview-ul").length>0?i.children("ul.e-treeview-ul").append(n(r)):i.append(n(e))},_modifyCss:function(t){t=n(t);t[0]!=null&&t.find(".e-item").length==0&&(t.removeClass("e-collapse").attr("aria-expanded",!1).find("> div > .e-icon").removeClass("e-icon e-minus").closest(".e-item").find("ul").remove(),this._deepWatch&&(this._updateField(this._newDataSource,t.attr("id"),this.model.fields,"expanded",!1),this._templateType==1&&this._updateField(this._newDataSource,t.attr("id"),this.model.fields,"hasChild",!1),this._oldDataSource=JSON.parse(JSON.stringify(this._newDataSource)),this.dataSource(this._newDataSource)))},_applyFirstLastChildClass:function(){n(this._liList).removeClass("first last").filter(":first-child:not(:last-child)").addClass("first");n(this._liList).filter(":last-child").addClass("last")},_expandEventHandler:function(t){var i=n(t.target),r,u;i.hasClass("e-icon")||i.closest(".e-item").hasClass("e-node-disable")||t.type==="dblclick"&&this.model.allowEditing||(t.preventDefault(),r=i.is("A")?n(i.siblings("div")):n(i.parents(".e-text-wrap").find("div")),this.model.fullRowSelect&&(i.is("div.e-text-wrap")||i.is("div.e-fullrow"))&&(r=i.closest(".e-item").find("> .e-text-wrap > div")),u=i.closest(".e-item"),r.hasClass("e-minus")?this._collapseNode(u):this.model.loadOnDemand&&r.find(".e-item").length==0?this._ClickEventHandler({target:r[0]}):this._expandNode(u))},_inlineEdit:function(t){t.preventDefault();var i=n(t.target);return i.hasClass("input-text")||i.hasClass("e-node-disable")||(i.is("A")||(i=i.closest(".e-text")),i.is("A")&&!i.hasClass("e-node-disable")&&this._inlineEditAction(i)),!1},_inlineEditAction:function(n){var t=this.element.find(".e-item > div > .e-text > #Edit_Input");t[0]==null&&this._createEditTextBox(n)},_createEditTextBox:function(r){var s={currentElement:r},u,h,f,o,e;return this._triggerEvent("beforeEdit",s)?!1:(u=this.element.find(".e-item > div > .e-text > #Edit_Input"),u[0]==null&&(f=t.buildTag("Input.input-text#Edit_Input","","",{type:"text",value:n.trim(r.text()).replace(/\n\s+/g," "),name:"inplace_value"}),n(f).css("width",r.outerWidth()+5+"px"),n(f).css("height",r.outerHeight()+"px"),f.addClass("e-tree-input e-textbox"),this._beforeEditText=r.text(),r[0].lastChild.nodeValue="",this.model.template&&(o=r.closest("li")["0"].id,e=this._getNodeObject(o,!0),r["0"].innerHTML=" ",e&&e.length!=0&&(f["0"].value=e["0"]!=i?e["0"]:"",n(f).css("width",r.outerWidth()+80+"px"),n(f).css("height",r.outerHeight()+10+"px"))),r.addClass("e-editable").append(f),u=f,h=u.val().length==""?3:r.outerWidth()+20,this._mousePositionAtEnd(u),this._currentEditableNode=r,this._on(u,"keypress",this._editTextBoxKeyPress)._on(u,"keydown",this,this._pressEscKey)._on(u,"mousedown pointerdown MSPointerDown",this,this._preventPropagation)._on(u,this.model.expandOn,this,this._preventPropagation)._on(u,"blur",this._focusout)),u)},_preventPropagation:function(n){n.stopImmediatePropagation();this._isTextbox=!0},_editTextBoxKeyPress:function(n){n.target.size=n.target.value.length+1},_mousePositionAtEnd:function(n){return n.focus&&n.focus(),n.select&&n.select(),!0},_focusElement:function(t){var r,i;this._isTextbox||(t&&(t.type=="touchstart"||t.type=="pointerdown"||t.type=="MSPointerDown")&&(r=n(t.currentTarget),r.hasClass("e-text")&&this.selectNode(r.closest(".e-item"))),this.model.template?(i=n(t.target).closest(".e-text")[0],i=i?i:t.target,this._focusNode(i)):this._focusNode(t.target))},_blurElement:function(t){n(t.target).hasClass("e-text")&&t.target.removeAttribute("tabindex")},_focusout:function(t){var i=n(t.currentTarget),r;this._isTextbox=!1;r={id:i.closest(".e-item").attr("id"),oldText:this._beforeEditText,newText:i.val()};this._triggerEvent("inlineEditValidation",r)?this._cancelAction(i):this._saveAction(i,t)},_pressEscKey:function(i){var r,f,u,e;i.cancelBubble=!0;i.returnValue=!1;r=n(i.currentTarget);r[0]!=null&&(f=r.closest(".e-text")[0],i.keyCode==13&&(i.stopPropagation(),this._focusout(i)),i.keyCode==27&&this._cancelAction(r),(i.keyCode==13||i.keyCode==27)&&(u=t.browserInfo(),e=u.name=="msie"&&u.version=="8.0"?!0:!1,e&&this._focusNode(f)))},_onFocusHandler:function(n){n.preventDefault()},_onKeyDown:function(n,t){var u=this.element.find("#"+this._id+"_active"),r,i;u.removeAttr("id");r=n.find("> div > .e-text:first");i=t.find("> div > .e-text:first");r.removeClass("e-node-focus");i.addClass("e-node-focus");this.element.removeAttr("aria-activedescendant");i.closest(".e-item")[0].id!=null&&i.closest(".e-item")[0].id!=""?this.element.attr("aria-activedescendant",i.closest(".e-item")[0].id):(i.closest(".e-item").attr("id",this._id+"_active"),this.element.attr("aria-activedescendant",this._id+"_active"));this.model.fullRowSelect&&(n.removeClass("e-li-focus"),t.addClass("e-li-focus"))},_KeyPress:function(t){var f,r=this,s,e,i,p,h,w,l,a,v,k,b,o,y,u,c,d;if(c=this.element.is("ul")?this.element:this.element.find("> ul"),f=t.keyCode?t.keyCode:t.which?t.which:t.charCode,r.model.allowKeyboardNavigation&&r.element.find("#Edit_Input").length<1&&r.element.find(".e-chkbox-wrap.e-focus").length<1)if(this.element.find(".e-animate").removeClass("e-animate"),s=n(c.find(".e-item > div > .e-text.e-active").closest(".e-item")[0]),e=c.find(".e-item:visible"),r._focusedNode?(i=r._focusedNode,r._focusedNode=null):i=c.find(".e-text.e-node-focus").closest(".e-item"),f==113&&(t.preventDefault(),c=i.length>0?i:s,c.length>0&&r.model.allowEditing&&!this.model.template&&(t.target=c.find("> div > .e-text:first"),r._inlineEdit(t))),f!=40||t.altKey)if(f!=38||t.altKey)if((f!=39||this.model.enableRTL||t.altKey)&&(f!=37||!this.model.enableRTL))if((f!=37||this.model.enableRTL||t.altKey)&&(f!=39||!this.model.enableRTL)){if(f!=36||t.altKey){if(f!=35||t.altKey){if(f==13)t.preventDefault(),u=i.length>0?i:s,k=u.find("> div > .e-text"),k.removeClass("e-node-focus"),this.model.fullRowSelect&&u.removeClass("e-li-focus"),k.hasClass("e-active")?r.model.allowMultiSelection&&r._nodeUnSelectionAction(u,event):r._nodeSelectionAction(u,t),r._focusedNode=u;else if(f==32)t.preventDefault(),u=i.length>0?i:s,u.length>0&&this.model.showCheckbox&&(b=u.find("> div > .e-chkbox-wrap > .nodecheckbox:first")[0],b.checked?this._nodeUncheck(b):this._nodeCheck(b),r._focusedNode=u);else if(f==46)t.preventDefault(),u=i.length>0?i:this.model.allowMultiSelection?c.find(".e-item > div > .e-text.e-active").closest(".e-item"):s,u.length>0&&(this._removeNode(u,t),o=n(e[0]).find("> div > .e-text:first"),this._focusNode(o[0]));else if(t&&t.ctrlKey==!0)if(f==88&&this.model.allowDragAndDrop&&this.model.allowDropChild){if(t.preventDefault(),u=i.length>0?i:s,u.length>0){if(y=u.parents(".e-item:first"),this._triggerEvent("beforeCut",{target:u,nodeDetails:this._getNodeDetails(u),keyCode:f,event:t}))return;this._cutNode=n(e[e.index(u)]).detach();n(this._cutNode)[0]!=null&&(this._isRender=!1,this.unselectNode(u),y.find("> ul > .e-item").length==0&&(this.collapseNode(y),this._modifyCss(y)),this._isRender=!0,this._triggerEvent("nodeCut",{parentElement:y,parentDetails:this._getNodeDetails(y),keyCode:f,event:t}))}}else if(f==86&&this._cutNode!=null&&this.model.allowDragAndDrop&&this.model.allowDropChild){if(t.preventDefault(),u=i.length>0?i:s,u=n(e[e.index(u)]),u.length===0&&(u=this.element),c=this._cutNode.find(" > div > .e-text").first(),n(e).length>0){if(this._triggerEvent("beforePaste",{target:u,nodeDetails:this._getNodeDetails(u),keyCode:f,event:t}))return;this._isRender=!1;this._dropAsChildNode(u,c,t);this._isRender=!0;this._cutNode=null;d=c.closest(".e-item");this._triggerEvent("nodePaste",{target:d,nodeDetails:this._getNodeDetails(d),keyCode:f,event:t})}}else r.model.allowMultiSelection&&(r._focusedNode=i)}else if(t.preventDefault(),v=i.length>0?i:s,a=n(e).last(),(a.hasClass("e-node-disable")||a.hasClass("hidden"))&&(a=this._getPrevEle(e,a)),s.length>0&&i.length==0&&s[0]!=a[0]||i.length>0&&i[0]!=a[0]){if(r._KeyPressEventHandler(a,r,f,t))return;r._onKeyDown(v,a)}}else if(t.preventDefault(),v=i.length>0?i:s,l=n(e).first(),(l.hasClass("e-node-disable")||l.hasClass("hidden"))&&(l=this._getNextEle(e,l)),s.length>0&&i.length==0&&s[0]!=l[0]||i.length>0&&i[0]!=l[0]){if(r._KeyPressEventHandler(l,r,f,t))return;r._onKeyDown(v,l)}}else if(t.preventDefault(),w=i.length>0?i.find("> div > div").first():s.find("> div > div").first(),h=w.closest(".e-item"),w.hasClass("e-minus"))this._collapseNode(n(e[e.index(w.closest(".e-item"))]));else{if(h=n(e[e.index(h)]).closest("ul").closest(".e-item"),r._KeyPressEventHandler(h.length>0?h:"",r,f,t))return;h.length>0&&r._onKeyDown(i,h)}else if(t.preventDefault(),p=i.length>0?i.find("> div > div").first():s.find("> div > div").first(),o=p.closest(".e-item"),p.hasClass("e-plus"))this.model.loadOnDemand&&o.find("> ul .e-item").length==0?this._ClickEventHandler({target:p[0]}):this._expandNode(n(e[e.index(o)]));else{if(o=this._getNextEle(e,o),r._KeyPressEventHandler(o.length>0?o:"",r,f,t))return;(o.length>0&&o[0]==p.closest(".e-item").find("ul > .e-item:first")[0]||o.find("ul > .e-item").first().hasClass("e-node-disable"))&&r._onKeyDown(i,o)}else{if(t.preventDefault(),v=i.length>0?i:s,h=this._getPrevEle(e,v),r._KeyPressEventHandler(h.length>0?h:null,r,f,t))return;h.length>0&&r._onKeyDown(i,h)}else{if(t.preventDefault(),v=i.length>0?i:s,o=this._getNextEle(e,v),r._KeyPressEventHandler(o.length>0?o:null,r,f,t))return;o.length>0&&r._onKeyDown(i,o)}},_getNextEle:function(t,i){var u=t.index(i),r;do u++,r=n(t[u]);while(r.hasClass("e-node-disable")||r.hasClass("hidden"));return r},_getPrevEle:function(t,i){var u=t.index(i),r;do u--,r=n(t[u]);while(r.hasClass("e-node-disable")||r.hasClass("hidden"));return r},_removeChildNodes:function(n,i,r){for(var e,f,o,s=0,h,u=0;u<i.length;u++)if(e=t.getObject(this.model.fields.parentId,i[u][0]),!t.isNullOrUndefined(e)&&e.toString()==r){s=i[u].length;break}for(u=0;u<s;u++)for(f=0;f<n.length;f++)if(o=t.getObject(this.model.fields.parentId,n[f]),!t.isNullOrUndefined(o)&&o.toString()==r){t.isNullOrUndefined(n[f])||(h=t.getObject(this.model.fields.id,n[f]).toString());n.splice(f,1);this._removeChildNodes(n,i,h);break}},_removeNode:function(i,r){var u,s,o,e,f;if(i[0]!=null&&i[0]!=null&&i.length>0){if(e=this,u=n(i[0]).closest("ul").closest(".e-item"),s=n(i[0]),o=n(i).closest(".e-treeview.e-js").data("ejTreeView"),o._triggerEvent("beforeDelete",{target:s,nodeDetails:o._getNodeDetails(s),parentElement:u[0]!=null?u:null,parentDetails:o._getNodeDetails(u),event:r,removedNodes:i}))return;if(this._isRender=!1,this.dataSource()instanceof t.DataManager?(f=this._newDataSource,this._removeData(i,f)):!t.isNullOrUndefined(this.dataSource())&&this.dataSource().length>0&&(f=this.dataSource(),this._removeData(i,f),this._deepWatch&&(this._oldDataSource=JSON.parse(JSON.stringify(f)),this.dataSource(f))),i.remove(),this._modifyCss(u),this._updateNodes(),this._updateCheckState(u),this._isRender=!0,o._triggerEvent("nodeDelete",{parentElement:u[0]!=null?u:null,parentDetails:o._getNodeDetails(u),event:r,removedNodes:i}))return;e=this;f=this.dataSource();setTimeout(function(){e.dataSource()==null||e.dataSource()instanceof t.DataManager||(e._dataSource=e._groupingObjects(f,function(n){return[!t.isNullOrUndefined(n)&&[t.getObject(e.model.fields.parentId,n)].toString()]}))},300)}},_removeData:function(t,i){for(var r=0,u=t.length;r<u;r++)this._templateType==2?this._removeRemoteData(i,n(t[r]),this.model.fields):this._removeLocalData(i,n(t[r]))},_removeLocalData:function(n,i){for(var u,f,e,o=this,r=0;r<n.length;r++)if(u=i.attr("id"),f=t.getObject(this.model.fields.id,n[r]),f&&f.toString()==u){e=this._groupingObjects(n,function(n){return[!t.isNullOrUndefined(n)&&[t.getObject(o.model.fields.parentId,n)].toString()]});n.splice(r,1);this._removeChildNodes(n,e,u);break}},_removeRemoteData:function(n,i,r){for(var e,f,u=0;u<n.length;u++){if(e=i.attr("id"),f=t.getObject(r.id,n[u]),f&&f.toString()==e){n.splice(u,1);break}n[u].hasOwnProperty("child")&&this._removeRemoteData(n[u].child,i,r.child?r.child:r)}},_updateNodes:function(){this._beforeBaseClass();this._applyFirstLastChildClass();this._updateExpandedNodes();this._updateCheckedNodes();this._updateSelectedNode()},_updateField:function(n,i,r,u,f){var c,e,s,l,o,h;if(!t.isNullOrUndefined(r)&&this.dataSource()!=null&&!t.isNullOrUndefined(n)&&!t.isNullOrUndefined(i)&&!t.isNullOrUndefined(u))if(this._templateType==1){for(e=0,s=n.length;e<s;e++)if(c=t.getObject(r.id,n[e]),!t.isNullOrUndefined(n[e])&&!t.isNullOrUndefined(c)&&c.toString()==i){o=r[u]?r[u]:u;n[e][o]=f;f==!1&&delete n[e][o];h=n[e];n.splice(e,1,h);break}}else for(e=0,s=n.length;e<s;e++){if(l=t.getObject(r.id,n[e]),!t.isNullOrUndefined(n[e])&&!t.isNullOrUndefined(l)&&l.toString()==i){o=r[u]?r[u]:u;n[e][o]=f;f==!1&&delete n[e][o];h=n[e];n.splice(e,1,h);break}n[e].hasOwnProperty("child")&&this._updateField(n[e].child,i,r.child?r.child:r,u,f)}},_removeField:function(n,r,u){if(!t.isNullOrUndefined(r)&&this.dataSource()!=null&&!t.isNullOrUndefined(n))for(var f=0;f<n.length;f++)n[f][r[u]]!=i&&delete n[f][r[u]],n[f].hasOwnProperty("child")&&this._removeField(n[f].child,r.child?r.child:r,u)},_KeyPressEventHandler:function(t,i,r,u){var f,e,o,s;return n(t)[0]!=null?(s=this._isNodeExpanded(t),f=this._getNodeDetails(t),o=i._getPath(t),e={keyCode:r,currentElement:t,value:f.text,isExpanded:s,path:o,event:u,id:f.id,parentId:f.parentId}):e={keyCode:r,currentElement:t,value:"",isExpanded:"",path:"",event:u,id:"",parentId:""},this._triggerEvent("keyPress",e)},_documentClick:function(t){var i,e,u,f;if(t.target.id!="Edit_Input"&&(i=n("#Edit_Input")[0]),i!=null){var e=n(i).closest(".e-text")[0],u=i.value,r=n(i).closest(".e-item");n(i).remove();e.lastChild.nodeValue=u;n(e).removeClass("e-editable").removeAttr("style");this._updateField(this._newDataSource,r.attr("id"),this.model.fields,"text",u);this._deepWatch&&(this._oldDataSource=JSON.parse(JSON.stringify(this._newDataSource)),this.dataSource(this._newDataSource));this._triggerEvent("nodeEdit",{id:r.attr("id"),oldText:this._beforeEditText,newText:u,target:r,nodeDetails:this._getNodeDetails(r),event:t})}this.element.find(t.target).length==0?(f=this.element.find(".e-item > div > .e-text.e-node-focus"),this._focusedNode=f.closest(".e-item"),f.removeClass("e-node-focus")):n(t.target).hasClass("e-active")&&(this._focusedNode=this.element.find(t.target).closest(".e-item"))},_saveAction:function(t,i){var e=t.val(),r=t.closest(".e-item"),u=t.closest(".e-text")[0],o=t.closest("li")["0"].id,f;t.remove();u!=null&&(u.lastChild.nodeValue=e,n(u).removeClass("e-editable"),this._updateField(this._newDataSource,r.attr("id"),this.model.fields,"text",e),this.model.template&&(f=this._getNodeObject(o),f&&f.length!=0&&(u.innerHTML=this._renderEjTemplate(this.model.template,f["0"]))),this._deepWatch&&(this._oldDataSource=JSON.parse(JSON.stringify(this._newDataSource)),this.dataSource(this._newDataSource)),this._triggerEvent("nodeEdit",{id:r.attr("id"),oldText:this._beforeEditText,newText:e,target:r,nodeDetails:this._getNodeDetails(r),event:i}),this.element.focus(),this._focusedNode=r)},_cancelAction:function(t){var i=t.closest(".e-text")[0];t.remove();i.lastChild.nodeValue=this._beforeEditText;n(i).removeClass("e-node-hover e-editable");this.element.focus()},_focusNode:function(t){n(t).hasClass("e-text")&&t.setAttribute("tabindex","0");t.focus()},_mouseEnterEvent:function(t){this.element.find(".e-node-hover").removeClass("e-node-hover");n(t.currentTarget).hasClass("e-text")&&!n(t.currentTarget).hasClass("e-node-disable")&&n(t.currentTarget).addClass("e-node-hover")},_mouseLeaveEvent:function(t){n(t.currentTarget).removeClass("e-node-hover")},_liMouseEnterEvent:function(t){this.element.find(".e-node-hover").removeClass("e-node-hover");this.element.find(".e-li-hover").removeClass("e-li-hover");var r=n(t.currentTarget).closest(".e-item"),i=r.find("> div .e-text");i.length>0&&!i.hasClass("e-node-disable")&&(i.addClass("e-node-hover"),r.addClass("e-li-hover"))},_liMouseLeaveEvent:function(t){var i=n(t.currentTarget).closest(".e-item");i.find("> div .e-text").removeClass("e-node-hover");i.removeClass("e-li-hover")},_onFocusOutHandler:function(){this._isPopup||this._hidePopup();this._isPopup=!1},_createObjectByText:function(i,r){if(typeof i=="string"){var u={};return t.createObject(this.model.fields.text,i,u),n(r)[0]!=null&&t.createObject(this.model.fields.parentId,r[0].getAttribute("id"),u),u}},_addNodesWhenObject:function(i,r){var l=JSON.stringify(this._dataSource),e=this.dataSource(),f,h,u,s,o,c;if(this._dataSource=[],this.dataSource()!=null&&this.dataSource()instanceof t.DataManager&&(e=[]),r[0]!=null&&r.length>0&&(f=r[0].getAttribute("id")),t.isNullOrUndefined(i.length))this._dataSource.push(i),t.isNullOrUndefined(this.dataSource())||(this._setNodeId(i,this.model.fields),t.isNullOrUndefined(f)||t.createObject(this.model.fields.parentId,f,i),e.push(i));else if(this._dataSource=i,!t.isNullOrUndefined(this.dataSource()))for(u=0;u<i.length;u++)this._setNodeId(i[u],this.model.fields),f?t.createObject(this.model.fields.parentId,f,i[u]):this._deepPropertyDelete(this.model.fields.parentId,i[u]),e.push(i[u]);for(h=this._templateType,this._templateType=2,this._liList||(this._liList=n("li",this.element)),s=document.createDocumentFragment(),o=0,c=this._dataSource.length;o<c;o++)s.appendChild(this._genTemplate(this._dataSource[o],this.model.fields)),this._liList.push(n(s).children()[0]),this.dataSource()instanceof t.DataManager&&this._addRemoteData(this._newDataSource,f,this._dataSource[o],this.model.fields);return this._templateType=h,this.dataSource()==null||this.dataSource()instanceof t.DataManager||(this._dataSource=JSON.parse(l),this._deepWatch&&(this._oldDataSource=JSON.parse(JSON.stringify(e)),this.dataSource(e))),s},_setNodeId:function(n,i){var u=i&&i.id?i.id:this.model.fields.id,f,r,e;if((t.isNullOrUndefined(n[u])||n[u]=="")&&(n[u]=this._id+"_"+this._indexID,this._indexID++),n.hasOwnProperty("child"))for(f=n.child,r=0,e=f.length;r<e;r++)this._setNodeId(f[r],i.child)},_addExpandedNodes:function(t){var i=this.model.expandedNodes;this._removeNullInArray(i);n.inArray(t,i)==-1&&this.model.expandedNodes.push(t);this._persistValues(this.model.expandedNodes,"expandedNodes")},_removeExpandedNodes:function(t){var i=this.model.expandedNodes;n.inArray(t,i)>-1&&(this.model.expandedNodes.splice(n.inArray(t,i),1),i.length==0&&i.push(-1));this._persistValues(this.model.expandedNodes,"expandedNodes")},_persistValues:function(i,r){var f,o,c,s,u,l,h,e;if(!t.isNullOrUndefined(this.model.fields)&&this.dataSource()!=null){for(f=i,o=[],u=0,l=f.length;u<l;u++)!t.isNullOrUndefined(f[u])&&f[u]>=0&&(c=this._liList[f[u]],s=n(c).attr("id"),s&&o.push(s));h=this._getCookies("_persistedValues");h&&(e=JSON.parse(h),e[r]=o,e=this._updatePersistAttr(e),this._setCookies("_persistedValues",JSON.stringify(e)))}},_updatePersistAttr:function(i){return t.isNullOrUndefined(this._ignoreOnPersist)?(n.inArray("selectedNodes",this._addToPersist)==-1&&delete i.selectedNodes,n.inArray("expandedNodes",this._addToPersist)==-1&&delete i.expandedNodes,n.inArray("checkedNodes",this._addToPersist)==-1&&delete i.checkedNodes):(n.inArray("selectedNodes",this._ignoreOnPersist)>-1&&delete i.selectedNodes,n.inArray("expandedNodes",this._ignoreOnPersist)>-1&&delete i.expandedNodes,n.inArray("checkedNodes",this._ignoreOnPersist)>-1&&delete i.checkedNodes),i},_onChecked:function(n,i){var u=n.closest(".e-item"),f,e,o,r=this._getNodeDetails(u);this._CurrenctSelectedNodes.push(r.text);f=this._isChecked(u);e=t.isNullOrUndefined(i)?"":t.isNullOrUndefined(i.event)?"":i.event;o={currentElement:u,id:r.id,parentId:r.parentId,value:r.text,currentNode:this._CurrenctSelectedNodes,currentCheckedNodes:this._checkedArray,isChecked:f,event:e};this._isRender&&this._triggerEvent("nodeCheck",o)},_onUnChecked:function(n,i){var u=n.closest(".e-item"),f,e,o,r=this._getNodeDetails(u);this._CurrenctSelectedNodes.push(r.text);f=this._isChecked(u);e=t.isNullOrUndefined(i)?"":t.isNullOrUndefined(i.event)?"":i.event;o={currentElement:u,id:r.id,parentId:r.parentId,value:r.text,currentNode:r.text,currentUncheckedNodes:this._uncheckedArray,isChecked:f,event:e};this._triggerEvent("nodeUncheck",o)},_addCheckNodes:function(n){var t=this.model.checkedNodes;this._removeNullInArray(t);!t instanceof Array&&(t=[]);t.indexOf(n)==-1&&t.push(n)},_removeCheckNodes:function(n){var t=this.model.checkedNodes,i;!t instanceof Array&&(t=[]);i=t.indexOf(n);i!=-1&&(t.splice(i,1),t.length==0&&t.push(-1))},_removeNullInArray:function(n){var t=n.indexOf(-1);t!=-1&&n.splice(t,1)},_afterInsertingNode:function(n){this._addDragableClass();this._finalizeEditing(n);var t=n.closest("ul").closest(".e-item");this._modifyCss(t);this._updateCheckState(t);this._isSelectedAction();t.length>0&&t.hasClass("e-node-disable")&&this._nodeDisableAction(t);this._updateSelectedNode()},_insertBeforeOrAfter:function(i,r,u){var f,o,e,s;if(r=this._getNodeByID(r),r[0]!=null&&r.is("LI")&&r.hasClass("e-item")){if(this._triggerEvent("beforeAdd",{data:i,targetParent:r[0]!=null?r:null,parentDetails:this._getNodeDetails(r)}))return;if(this._isRender=!1,typeof i!="object"&&(i=this._createObjectByText(i)),typeof i!="object")return;t.getObject(this.model.parentId,i)&&this._deepPropertyDelete(this.model.parentId,i);f=this._addNodesWhenObject(i,r);o=document.createElement("ul");n(o).append(f);f=n(o.children);this.model.showCheckbox&&f.children().find(".nodecheckbox").ejCheckBox({cssClass:this.model.cssClass,change:this._checkedChange});r.parents(".e-item:first").length>0?r.parents(".e-item:first").append(f):this.element.append(f);this.model.showCheckbox&&this._isCheckedAction();this._dropAsSiblingNode(r,f.find("> div > .e-text"),u,"");this._afterInsertingNode(f);this._isRender=!0;this._triggerEvent("nodeAdd",{data:i,nodes:f,parentElement:r[0]!=null?r:null,parentDetails:this._getNodeDetails(r)});e=this;s=this.dataSource();setTimeout(function(){e.dataSource()==null||e.dataSource()instanceof t.DataManager||(e._dataSource=e._groupingObjects(s,function(n){return[!t.isNullOrUndefined(n)&&[t.getObject(e.model.fields.parentId,n)].toString()]}))},300)}},_getNodeByID:function(t){return typeof t!="object"&&t!=""&&t!=i?this.element.find(this._checkValidId(t.toString())):(t=n(t),t.is("A")&&t.hasClass("e-text")&&(t=t.closest(".e-item")),t)},_checkValidId:function(n){return n[0]=="#"?n.replace(/(:|\.|\[|\]|,)/g,"\\$1"):"#"+n.replace(/(:|\.|\[|\]|,)/g,"\\$1")},_isTreeElement:function(t){return n(t)[0]!=null&&t.is("LI")&&t.hasClass("e-item")},_isUrl:function(n){return/(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/.test(n)},_getChildUl:function(n){return this.model.fullRowSelect?n.children[2]:n.children[1]},_sendAjaxOptions:function(n,t){var r=this,i,u;i=this._getNodeDetails(t);i.mapper=this.model.fields;i.model=this.model;u={url:n,data:i,type:"POST",async:!0,crossDomain:!0,dataType:"JSON",contentType:"application/json; charset=utf-8",success:function(n){try{r._ajaxSuccessHandler(n,t)}catch(i){}},error:function(n){try{r._ajaxErrorHandler(n)}catch(t){}}};this._sendAjaxRequest(u)},_sendAjaxRequest:function(t){var i=JSON.parse(JSON.stringify(t));(delete i.success,delete i.error,this._triggerEvent("beforeLoad",{ajaxOptions:i}))||(delete i.success,delete i.error,n.extend(t,i),n.ajax({type:t.type,url:t.url,dataType:t.dataType,data:t.data,async:t.async,contentType:t.contentType,crossDomain:t.crossDomain,success:t.success,error:t.error}))},_ajaxSuccessHandler:function(n,t){this._isRender=!1;typeof n=="object"&&this.addNode(n,t);this._isRender=!0;this._triggerEvent("loadSuccess",{data:n,targetParent:t,parentDetails:this._getNodeDetails(t)})},_ajaxErrorHandler:function(n){this._triggerEvent("loadError",{error:n})},_wireEvents:function(){this._isDevice&&n.isFunction(n.fn.tap)?this._on(this.element.find("li"),"tap",this._ClickEventHandler):this._on(this.element,"click",this._ClickEventHandler);this._on(this.element,this._isDevice&&n.isFunction(n.fn.tap)?this._touchExpandOn:this.model.expandOn,this._expandEventHandler)._on(this.element,"focus",this._onFocusHandler)._on(this.element,"blur",this._onFocusOutHandler);this._wholeRowEvents("_on");this.model.allowEditing&&this._allowEditable();this.model.allowDragAndDrop&&this._addDragableClass();this.model.showCheckbox&&this.element.find(".nodecheckbox").ejCheckBox("enable");this.model.allowKeyboardNavigation&&this._on(this.element,"keydown",this._KeyPress)},_unWireEvents:function(){this._isDevice&&n.isFunction(n.fn.tap)?this._off(this.element.find("li"),"tap"):this._off(this.element,"click");this._off(this.element,this._isDevice&&n.isFunction(n.fn.tap)?this._touchExpandOn:this.model.expandOn)._off(this.element,"focus")._off(this.element,"blur");this._wholeRowEvents("_off");this._preventEditable();this._preventDraggable();this.model.allowKeyboardNavigation&&this._off(this.element,"keydown");this.model.showCheckbox&&this.element.find(".nodecheckbox").ejCheckBox("disable")},_wholeRowEvents:function(t){this.model.fullRowSelect?(this[t](this.element,this._isDevice&&n.isFunction(n.fn.touchstart)?"touchstart":"mouseenter",".e-text-wrap",this._liMouseEnterEvent),this[t](this.element,this._isDevice&&n.isFunction(n.fn.touchend)?"touchend":"mouseleave",".e-text-wrap",this._liMouseLeaveEvent),this[t](this.element,this._isDevice&&n.isFunction(n.fn.touchstart)?"touchstart":"mouseenter",".e-fullrow",this._liMouseEnterEvent),this[t](this.element,this._isDevice&&n.isFunction(n.fn.touchend)?"touchend":"mouseleave",".e-fullrow",this._liMouseLeaveEvent)):(this[t](this.element,this._isDevice&&n.isFunction(n.fn.touchstart)?"touchstart":"mouseenter",".e-text",this._mouseEnterEvent),this[t](this.element,this._isDevice&&n.isFunction(n.fn.touchend)?"touchend":"mouseleave",".e-text",this._mouseLeaveEvent))},_enableDragDrop:function(){this.model.allowDragAndDrop&&(this._drag(),this.model.allowDropChild&&this._childDrop(),this.model.allowDropSibling&&this._siblingDrop())},_allowEditable:function(){this.element.find(".e-item").addClass("AllowEdit");this._on(n(document),"click",this._documentClick)._on(this.element,this._isDevice&&n.isFunction(n.fn.tap)?"doubletap":"dblclick","a.e-text",this._inlineEdit)},_preventEditable:function(){this.element.find(".e-item").removeClass("AllowEdit");this._off(n(document),"click",this._documentClick)._off(this.element,this._isDevice&&n.isFunction(n.fn.tap)?"doubletap":"dblclick","a.e-text")},_preventDraggable:function(){this.element.find(".e-draggable, .e-droppable").removeClass("e-draggable e-droppable");this._preventDropSibling();this._preventDropChild();this._off(this.element,"mouseup touchstart pointerdown MSPointerDown",this._anchors,this._focusElement);this._off(this.element,"focusout",this._anchors,this._blurElement)},_preventDropSibling:function(){this.element.find(".e-dropsibling").removeClass("e-dropsibling")},_preventDropChild:function(){this.element.find(".e-item > div > .e-dropchild").removeClass("e-dropchild")},_getNodeObject:function(n,r){if(!t.isNullOrUndefined(this.model.fields)&&this.dataSource()!=null&&n!=i){if(this._templateType===1){var f=t.Query().where(this.model.fields.id,"equal",n,!1),u=t.DataManager(this._newDataSource).executeLocal(f);return u.length>0&&r?[u[0][this.model.fields.text]]:u}return this._getRemoteNode(this._newDataSource,this.model.fields,n,r)}return[]},_getChildMapper:function(n){return typeof n.child=="string"||t.isNullOrUndefined(n.child)?n:n.child},_getNodeData:function(r){var f,y,p,e,w,s;if(!t.isNullOrUndefined(this.model.fields)&&this.dataSource()!=null&&r!=i){this._updatePersistProb();var u=[],h=[],c=this._orderDataSource().length,l=null;for(f=0;f<c;f++)this._orderDataSource()[f][this.model.fields.id]==r&&(l=r,f=c);if(l){if(this.dataSource()instanceof t.DataManager&&this._templateType==2)return this._getRemoteNode(this._newDataSource,this.model.fields,r);if(this._templateType==2)u=JSON.parse(JSON.stringify(this._getRemoteNode(this._newDataSource,this.model.fields,r)));else{var a=[],o=this._orderDataSource(),b=t.Query().where(this.model.fields.id,"equal",r,!1),k=t.DataManager(o).executeLocal(b),v=this._getFilterItems(k[0],o);for(f=0,y=v.length;f<y;f++)a.push(new t.Predicate(this.model.fields.id,"equal",v[f],!1));p=t.Query().where(t.Predicate.or(a));u=JSON.parse(JSON.stringify(t.DataManager(o).executeLocal(p)))}for(e=0,w=u.length;e<w;e++)s=t.getObject(this.model.fields.id,u[e]),n(document.getElementById(s)).length>0&&(n(document.getElementById(s)).find("> ul .e-item").length==0?(this._deepPropertyDelete(this.model.fields.hasChild,u[e]),this._templateType==2&&delete u[e].child):this._templateType==1?t.createObject(this.model.fields.hasChild,!0,u[e]):this._updateChildData(u[e].child,this.model.fields),h.push(u[e]));return h}}},_updateChildData:function(r,u){var f,o,e;if(r!=i)for(f=0,o=r.length;f<o;f++)e=t.getObject(u.id,r[f]),n(document.getElementById(e)).length>0&&(n(document.getElementById(e)).find("> ul .e-item").length==0?(this._deepPropertyDelete(u.hasChild,r[f]),delete r[f].child):this._updateChildData(r[f].child,u.child?u.child:u))},_getFilterItems:function(n,r){var u=[],o,c,s,f,l,h,e,a;if(n==i)return u;for(o=t.getObject(this.model.fields.id,n),u.push(o),c=t.Query().where(this.model.fields.parentId,"equal",o,!1),s=t.DataManager(r).executeLocal(c),f=0,l=s.length;f<l;f++)for(h=this._getFilterItems(s[f],r),e=0,a=h.length;e<a;e++)u.push(h[e]);return u},_getRemoteNode:function(n,i,r,u){for(var o,f=[],e=0,s=n.length;e<s;e++){if(o=t.getObject(i.id,n[e]),o&&o.toString()==r)return u?f.push(n[e][i.text]):f.push(n[e]),f;if(n[e].hasOwnProperty("child")&&(f=this._getRemoteNode(n[e].child,this._getChildMapper(i),r,u),f.length>0))return f}return f},_updateRemoteData:function(n,i,r,u){var f,o,e,s;if(this.dataSource()==null||!this.dataSource().dataSource.offline)for(f=0,o=n.length;f<o;f++){if(e=t.getObject(u.id,n[f]),e&&e.toString()==i){n[f].child=r;s=n[f];n.splice(f,1,s);break}n[f].hasOwnProperty("child")&&this._updateRemoteData(n[f].child,i,r,u.child?u.child:u)}},_addRemoteData:function(n,i,r,u){var f,o,e,s;if(this.dataSource()==null||!this.dataSource().dataSource.offline)if(t.isNullOrUndefined(i))n.push(r);else for(f=0,o=n.length;f<o;f++){if(e=t.getObject(u.id,n[f]),e&&e.toString()==i){t.isNullOrUndefined(n[f].child)?(n[f].child=[r],s=n[f],n.splice(f,1,s)):n[f].child.push(r);break}n[f].hasOwnProperty("child")&&this._addRemoteData(n[f].child,i,r,u.child?u.child:u)}},refresh:function(){this._unWireEvents();this.element.html("");this._init()},expandAll:function(n,t){this.model.enableMultipleExpand&&(n>0?this._expandByLevel(this._getElement(),n,t):this._expandAll(t))},collapseAll:function(n,t){this.model.enableMultipleExpand&&(n>0?this._collapseByLevel(this._getElement(),n,t):this._collapseAll(t))},checkAll:function(){this.model.showCheckbox&&this._checkAll()},unCheckAll:function(){this.model.showCheckbox&&this._uncheckAll()},selectNode:function(n){n&&typeof n=="object"&&n.length>0?(this._unselectAll(),this._doSelectNodes(n,!0)):(n=this._getNodeByID(n),this._isTreeElement(n)&&this._nodeSelectionAction(n))},unselectNode:function(n){n&&typeof n=="object"&&n.length>0?this._doUnselectNodes(n):(n=this._getNodeByID(n),this._isTreeElement(n)&&this._nodeUnSelectionAction(n))},selectAll:function(){this.model.allowMultiSelection&&this._selectAll()},unselectAll:function(){this.model.allowMultiSelection&&this._unselectAll()},enableNode:function(n){var t,i;if(n&&typeof n=="object"&&n.length>0)for(t=0;t<n.length;t++)this.enableNode(n[t]);else i=this._getNodeByID(n),this._isTreeElement(i)&&this._nodeEnableAction(i)},disableNode:function(n){var t,i;if(n&&typeof n=="object"&&n.length>0)for(t=0;t<n.length;t++)this.disableNode(n[t]);else i=this._getNodeByID(n),this._isTreeElement(i)&&this._nodeDisableAction(i)},addNodes:function(n,t,r){if(n&&typeof n=="object"&&t==i&&n.length>0)for(var u=0;u<n.length;u++)this.addNode(n[u],t,r);else this.addNode(n,t,r)},addNode:function(i,r,u){var e,y,c,h,f,l,a,s,o,v;t.isNullOrUndefined(i)||(e=null,y=null,f=r?this._getNodeByID(r):this.model.allowMultiSelection?this.getSelectedNodes():this.getSelectedNode(),typeof i=="object"&&t.isNullOrUndefined(r)&&(l=t.getObject(this.model.fields.parentId,i),t.isNullOrUndefined(i.length)&&!t.isNullOrUndefined(l)?h=l:t.isNullOrUndefined(i.length)||i.length!=1||(h=t.getObject(this.model.fields.parentId,i[0])),h&&(f=this._getNodeByID(h))),f=this._isTreeElement(f)?n(f[0]):[],a=this._isParentExpand(f),this._triggerEvent("beforeAdd",{data:i,targetParent:f[0]!=null?f:null,parentDetails:this._getNodeDetails(f)}))||(f.length==0||f.hasClass("e-node-disable")||this._expandNode(f),typeof i!="object"&&(i=this._createObjectByText(i,f)),typeof i!="object"||t.isNullOrUndefined(i.length)&&i.length==0)||(e=this._addNodesWhenObject(i,f),c=document.createElement("ul"),n(c).append(e),e=n(c.children),this.model.showCheckbox&&e.children().find(".nodecheckbox").ejCheckBox({cssClass:this.model.cssClass,change:this._checkedChange}),this._appendNode(f,e),f[0]!=null&&(s=n(f[0].childNodes[0].childNodes[0]),s.hasClass("e-plus")||s.hasClass("e-minus")?s.removeClass("e-load"):s.removeClass("e-icon e-load")),f[0]!=null&&f.find("> div > .e-minus").length>0&&!this.model.enableMultipleExpand&&this._denyMultipleExpand(f),this._updateNodes(),this._afterInsertingNode(e),this._isDevice&&n.isFunction(n.fn.tap)&&f[0]==null&&this._on(e,"tap",this._ClickEventHandler),u&&!a&&this._preventParentNode(f),this._triggerEvent("nodeAdd",{data:i,nodes:e,parentElement:f[0]!=null?f:null,parentDetails:this._getNodeDetails(f)}),o=this,v=this.dataSource(),setTimeout(function(){o.dataSource()==null||o.dataSource()instanceof t.DataManager||(o._dataSource=o._groupingObjects(v,function(n){return[!t.isNullOrUndefined(n)&&[t.getObject(o.model.fields.parentId,n)].toString()]}))},300))},removeNode:function(n){var t,i;if(n&&typeof n=="object"&&n.length>0)for(t=0;t<n.length;t++)this.removeNode(n[t]);else i=n?this._getNodeByID(n):this.model.allowMultiSelection?this.getSelectedNodes():this.getSelectedNode(),this._isTreeElement(i)&&this._removeNode(i)},removeAll:function(){this._liList.remove();this._updateNodes()},checkNode:function(n){var t,i;if(this.model.showCheckbox)if(n&&typeof n=="object"&&n.length>0)for(t=0;t<n.length;t++)this.checkNode(n[t]);else i=this._getNodeByID(n),this._isTreeElement(i)&&this._nodeCheck(i.find("> div > .e-chkbox-wrap > .nodecheckbox:first")[0])},uncheckNode:function(n){var t,i;if(this.model.showCheckbox)if(n&&typeof n=="object"&&n.length>0)for(t=0;t<n.length;t++)this.uncheckNode(n[t]);else i=this._getNodeByID(n),this._isTreeElement(i)&&this._nodeUncheck(i.find("> div > .e-chkbox-wrap > .nodecheckbox:first")[0])},expandNode:function(n){n&&typeof n=="object"&&n.length>0?(this._allowOnDemand=!0,this._expandNodes(n,!0),this._allowOnDemand=!1):(n=this._getNodeByID(n),this._isTreeElement(n)&&(this._allowOnDemand=!0,this._expandNode(n),this._allowOnDemand=!1))},collapseNode:function(n){var t,i;if(n&&typeof n=="object"&&n.length>0)for(t=0;t<n.length;t++)this.collapseNode(n[t]);else i=this._getNodeByID(n),this._isTreeElement(i)&&this._collapseNode(i)},showNode:function(n){var t,i;if(n&&typeof n=="object"&&n.length>0)for(t=0;t<n.length;t++)this.showNode(n[t]);else i=this._getNodeByID(n),this._isTreeElement(i)&&i.css("visibility","").removeClass("hidden")},hideNode:function(n){var t,i;if(n&&typeof n=="object"&&n.length>0)for(t=0;t<n.length;t++)this.hideNode(n[t]);else i=this._getNodeByID(n),this._isTreeElement(i)&&i.css("visibility","hidden").addClass("hidden")},show:function(){this.element.css("visibility","").find(".e-item").removeClass("hidden")},hide:function(){this.element.css("visibility","hidden").find(".e-item").addClass("hidden")},hasChildNode:function(n){return n=this._getNodeByID(n),this._isTreeElement(n)?n.find("> ul > .e-item").length>0?!0:!1:void 0},isChildLoaded:function(n){return n=this._getNodeByID(n),this._isTreeElement(n)&&n.find("ul > .e-item").length>0?!0:!1},isNodeChecked:function(n){return n=this._getNodeByID(n),this._isTreeElement(n)?this._isChecked(n):void 0},isExpanded:function(n){return n=this._getNodeByID(n),this._isTreeElement(n)?this._isNodeExpanded(n):void 0},isVisible:function(n){return n=this._getNodeByID(n),this._isTreeElement(n)&&n.css("visibility")!="hidden"?!0:!1},isExist:function(n){return n=this._getNodeByID(n),this._isTreeElement(n)&&this._liList.index(n)!=-1?!0:!1},isSelected:function(n){return n=this._getNodeByID(n),this._isTreeElement(n)?n.find("> div > .e-text").hasClass("e-active"):void 0},isDisabled:function(n){return n=this._getNodeByID(n),this._isTreeElement(n)&&n.hasClass("e-node-disable")?!0:!1},getTreeData:function(n){return n!=i?this._getNodeData(n):!t.isNullOrUndefined(this.model.fields)&&this.dataSource()!=null?this._orderDataSource():void 0},getText:function(t){return t=this._getNodeByID(t),this._isTreeElement(t)?n.trim(this.element.find(t).find("> div > .e-text").text()):void 0},getSelectedNode:function(){return this.model.allowMultiSelection?null:this.element.find(".e-item > div > .e-active").closest(".e-item")},getSelectedNodes:function(){return this.element.find(".e-item > div > .e-active").closest(".e-item")},getCheckedNodes:function(){if(this.model.showCheckbox)return this._liList.find('> div > .e-chkbox-wrap[aria-checked="true"]').closest(".e-item")},getExpandedNodes:function(){return this.element.find(".e-item > div > .e-minus").closest(".e-item")},getExpandedNodesIndex:function(){return this._getNodesIndex(this.getExpandedNodes())},getCheckedNodesIndex:function(){return this._getNodesIndex(this.getCheckedNodes())},getSelectedNodeIndex:function(){return this.model.allowMultiSelection?null:this._getNodesIndex(this.getSelectedNode())},getSelectedNodesIndex:function(){return this._getNodesIndex(this.getSelectedNodes())},getVisibleNodes:function(){return this.element.find(".e-item:visible:not(.hidden, .e-node-disable)")},getNodeCount:function(){return this.element.find(".e-item").length},getNode:function(n){return n=this._getNodeByID(n),this._isTreeElement(n)?this._getNodeDetails(n):null},getNodeIndex:function(n){return n=this._getNodeByID(n),this._isTreeElement(n)?this._liList.index(n):void 0},getNodeByIndex:function(t){if(typeof t=="number")return n(this._liList[t])},getParent:function(n){return n=this._getNodeByID(n),this._isTreeElement(n)?n.parents(".e-item:first"):void 0},getChildren:function(n,t){return n=this._getNodeByID(n),this._isTreeElement(n)?t?n.find(".e-item"):n.find("> ul > .e-item"):void 0},updateText:function(i,r){var u,e,o,s,f;if(i=this._getNodeByID(i),this._isTreeElement(i)&&!t.isNullOrUndefined(r)){if(u=this.element.find(i).children("div").find("> .e-text")[0],s=i.closest("li")["0"].id,u!=null){if(o={currentElement:n(u)},this._triggerEvent("beforeEdit",o))return!1;e=u.lastChild.nodeValue}u.lastChild.nodeValue=r;this._updateField(this._newDataSource,i.attr("id"),this.model.fields,"text",r);this.model.template&&(f=this._getNodeObject(s),f&&f.length!=0?u.innerHTML=this._renderEjTemplate(this.model.template,f["0"]):u.innerText=r);this._deepWatch&&(this._oldDataSource=JSON.parse(JSON.stringify(this._newDataSource)),this.dataSource(this._newDataSource));this._triggerEvent("nodeEdit",{id:i.attr("id"),oldText:e,newText:r,target:i,nodeDetails:this._getNodeDetails(i),event:null})}},insertAfter:function(n,t){this._insertBeforeOrAfter(n,t,!1)},insertBefore:function(n,t){this._insertBeforeOrAfter(n,t,!0)},moveNode:function(n,i,r){if(n=this._getNodeByID(n),i=this._getNodeByID(i),this._isTreeElement(n)){if(n.parents(".e-item")[0]==i[0]&&t.isNullOrUndefined(r)||i.find(n).index()==r||i[0]==null&&this._liList.index(n)==r)return;var u=i[0]!=null&&r>=0?i.find("> ul > .e-item").eq(r):this.getNodeByIndex(r);this._isRender=!1;this._isTreeElement(u)?u.parents(".e-item:first")[0]==n.parents(".e-item:first")[0]&&u.next(".e-item")[0]==null?this._dropAsSiblingNode(u,n.find(" > div > .e-text"),!1,""):this._dropAsSiblingNode(u,n.find(" > div > .e-text"),!0,""):this._dropAsChildNode(i,n.find(" > div > .e-text"),"");i.length>0&&i.hasClass("e-node-disable")&&this._nodeDisableAction(i);this._isRender=!0}},loadData:function(n,t){t=this._getNodeByID(t);this._isUrl(n)&&(t[0]==null||this._isTreeElement(t))&&this._sendAjaxOptions(n,t)},ensureVisible:function(t){var r,u,i,f;if(t=this._getNodeByID(t),this._isTreeElement(t)&&!this.isDisabled(t)&&this.isVisible(t)){for(r=t.parents(".e-item"),i=0,f=r.length;i<f;i++)this._expandNode(n(r[i]));return u=t.offset(),t.animate({scrollTop:u.top},this.model.enableAnimation?350:0,"linear",function(){t.find("> div > a.e-text")[0].scrollIntoView(!1)}),!0}return!1},_triggerEvent:function(n,t){if(this._isRender)return this._trigger(n,t)}})})(jQuery,Syncfusion)});
