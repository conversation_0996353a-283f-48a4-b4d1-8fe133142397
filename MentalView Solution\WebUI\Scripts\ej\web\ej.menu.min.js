/*!
*  filename: ej.menu.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min","./ej.checkbox.min"],n):n()})(function(){(function(n,t,i){t.widget("ejMenu","ej.Menu",{element:null,model:null,validTags:["ul"],_setFirst:!1,_rootCss:"e-menu",angular:{terminal:!1},defaults:{height:"",width:"",animationType:"default",orientation:t.Orientation.Horizontal,menuType:"normalmenu",isResponsive:!0,contextMenuTarget:null,contextMenuPopupTarget:"body",htmlAttributes:{},cssClass:"",openOnClick:!1,subMenuDirection:"none",enableCenterAlign:!1,showRootLevelArrows:!0,showSubLevelArrows:!0,enableAnimation:!0,container:null,enableSeparator:!0,enabled:!0,overflowHeight:"auto",overflowWidth:"auto",fields:{child:null,dataSource:null,query:null,tableName:null,id:"id",parentId:"parentId",text:"text",spriteCssClass:"spriteCssClass",url:"url",imageAttribute:"imageAttribute",htmlAttribute:"htmlAttribute",linkAttribute:"linkAttribute",imageUrl:"imageUrl"},enableRTL:!1,titleText:"Menu",locale:"en-US",excludeTarget:null,beforeOpen:null,open:null,close:null,mouseover:null,mouseout:null,click:null,keydown:null,overflowOpen:null,overflowClose:null,create:null,destroy:null},dataTypes:{animationType:"enum",cssClass:"string",titleText:"string",locale:"string",openOnClick:"boolean",enabled:"boolean",enableCenterAlign:"boolean",showArrow:"boolean",showRootLevelArrows:"boolean",showSubLevelArrows:"boolean",enableSeparator:"boolean",isResponsive:"boolean",enableRTL:"boolean",enableAnimation:"boolean",fields:{dataSource:"data",query:"data",child:"data"},excludeTarget:"string",htmlAttributes:"data"},_setModel:function(t){for(var i in t)switch(i){case"menuType":t[i]=this.model.menuType;break;case"fields":this._wireEvents("_off");this.element.empty().insertBefore(this.wrapper);this.wrapper.remove();n.extend(this.model.fields,t[i]);this._intializeData();this.model.enabled||this._wireEvents("_off");break;case"orientation":this._setOrientation(t[i]);break;case"showRootLevelArrows":this._addArrow(t[i],this.model.showSubLevelArrows);break;case"showSubLevelArrows":this._addArrow(this.model.showRootLevelArrows,t[i]);break;case"enableSeparator":this._setSeparator(t[i]);break;case"height":this._setHeight(t[i]);break;case"width":this._setWidth(t[i]);break;case"cssClass":this._setSkin(t[i]);break;case"isResponsive":this.model.isResponsive?this._responsiveLayout():(n(this.resWrap).remove(),n(this.wrapper).removeClass("e-menu-responsive"),n(this.element).removeClass("e-menu-responsive"),this.resWrap=null);break;case"htmlAttributes":this._addAttr(t[i]);break;case"enableRTL":this._setRTL(t[i]);break;case"enableCenterAlign":this._centerAlign(t[i]);break;case"excludeTarget":this.model.excludeTarget=t[i];break;case"enabled":this.model.enabled=t[i];this._controlStatus(t[i]);break;case"animationType":this._setAnimation(t[i]);break;case"enableAnimation":this.model.enableAnimation=t[i];break;case"openOnClick":this._hoverOpen=!t[i];this._hoverClose=!t[i];break;case"subMenuDirection":this._setSubMenuDirection(this.model.subMenuDirection);break;case"titleText":this._titleText(t[i]);break;case"locale":this.model.locale=t[i];this._updateLocalConstant();this._setLocale();break;case"overflowHeight":this._setOverflowDimensions("height",t[i]);break;case"overflowWidth":this._setOverflowDimensions("width",t[i]);break;case"contextMenuPopupTarget":this.model.contextMenuPopupTarget=t[i];this._contextMenu_Template()}},_updateLocalConstant:function(){this._localizedLabels=t.getLocalizedConstants("ej.Menu",this.model.locale)},_setLocale:function(){this._titleText(this._localizedLabels.titleText)},_titleText:function(i){this.model.menuType!=t.MenuType.ContextMenu&&this.model.orientation!="vertical"&&n(this.label).text(i)},_destroy:function(){this.model.menuType==t.MenuType.ContextMenu?this._referenceElement.append(this._cloneElement):this._cloneElement.insertBefore(this.wrapper);this._cloneElement.removeClass("e-menu e-js");this.wrapper.remove()},_init:function(){this._cloneElement=this.element.clone();this.element.css("visibility","hidden");this._setValues();this._intializeData();this.element.css("visibility","visible")},_setValues:function(){this._mouseOver=!0;this._hoverOpen=!0;this._hoverClose=!0;this._isMenuOpen=!1;this._hideSpeed=100;this._showSpeed=100;this._isSubMenuOpen=!1;this._isContextMenuOpen=!1;this._disabledMenuItems=[];this._hiddenMenuItems=[];this._delayMenuHover=0;this._delaySubMenuHover=0;this._delaySubMenuShow=0;this._preventContextOpen=!0;this._setAnimation(this.model.animationType);this._isFocused=!0;this._menuOverflowItems=[];this._menuHeaderItems=[];this._menuCloneItems=[];this._itemWidth=0},_intializeData:function(){t.isNullOrUndefined(this.model.fields)||this.model.fields.dataSource==null?(this._renderMenu(),this._wireEvents("_on"),this._calculateOverflowItems()):(this._generateTemplate(this.model.fields.dataSource),this._renderMenu())},_renderMenu:function(){this._renderControl();this._addArrow(this.model.showRootLevelArrows,this.model.showSubLevelArrows);this._renderArrow();this._intializeMenu();this._itemWidth=this.element.width();this.model.isResponsive&&this._ensureResponsiveClasses(n(window).width()<767);this.model.orientation=="horizontal"&&(this._on(this.element.parent().find("span.e-check-wrap.e-icon"),"click",this._mobileResponsiveMenu),this.model.fields.dataSource!=null&&this._calculateOverflowItems())},_renderControl:function(){this.model.menuType=="normalmenu"?(this.wrapper=t.buildTag("div"),this.wrapper.addClass(this.model.cssClass+" e-menu-wrap")):this.wrapper=t.buildTag("div.e-menu-wrap");this.model.isResponsive&&this._responsiveLayout();this.model.menuType!=t.MenuType.ContextMenu&&(this.wrapper.insertBefore(this.element),this.wrapper.append(this.element));this.element.addClass("e-menu e-widget e-box").attr({role:"menu",tabindex:0});this._addAttr(this.model.htmlAttributes);this.model.enableRTL&&this._setRTL(this.model.enableRTL);this._setSubMenuDirection(this.model.subMenuDirection);this.model.menuType=="normalmenu"?this.model.orientation=="horizontal"?this.element.addClass("e-horizontal"):this.element.addClass("e-vertical"):this._contextMenu_Template();this._addClass();this.model.enableCenterAlign&&this._centerAlign(this.model.enableCenterAlign);this.model.enableSeparator&&this._setSeparator(!0);this.model.enabled||this.disable()},_renderPopupWrapper:function(){if(this._ensureOverflowPopup()){this.popupWrapper=t.buildTag("div.e-menu-popwrap");this.popupWrapper.insertAfter(this.element);var n=typeof value=="number"?this.model.overflowHeight+"px":this.model.overflowHeight,i=typeof value=="number"?this.model.overflowWidth+"px":this.model.overflowWidth;this.popupWrapper.css({height:n,width:i});this.popupWrapper.hide();this._addOverflowItems()}},refresh:function(){this._onResize()},_calculateOverflowItems:function(){var f,i,u;if(this._ensureOverflowPopup()){this.element.find("li.e-list").removeClass("e-menu-show");n(this.lastelement).removeClass("e-last");this._menuHeaderItems=[];f=this.element.outerWidth();this.element.find("li.e-ham-wrap").length>0&&(this._itemWidth<=this.element.width()||this._itemWidth>=this.element.width()&&!this._isOverflowPopupOpen())&&(t.isNullOrUndefined(this.popupWrapper)||this.popupWrapper.hide());this._renderHamburgerIcon();this.element.find("li.e-ham-wrap").css({display:"list-item"});var o=this.element.find("li.e-ham-wrap").outerWidth(),r=0,e=!0;for(this.element.find("li.e-ham-wrap").hide(),this._menuHeaderItems=this.element.find(">li.e-list:not(.e-hidden-item)"),this._menuOverflowItems=[],i=0;i<this._menuHeaderItems.length;i++)if(u=n(this._menuHeaderItems[i]),r=r+u.outerWidth(),r<f)u.removeClass("e-menu-hide"),this.element.find(">li.e-list.e-haschild>ul").find("li.e-haschild").find("span.e-icon.e-arrowhead-down").removeClass("e-arrowhead-down").addClass("e-arrowhead-right"),this.model.enableSeparator&&this._setSeparator(!0);else{if(e){e=!1;this.element.find("li.e-ham-wrap").css({display:"list-item"});r=r-u.outerWidth()+o;i>1&&(r=r-n(this._menuHeaderItems[i-1]).outerWidth(),i=i-2);continue}this._menuOverflowItems.push(n(u).clone(!0));u.addClass("e-menu-hide")}this._menuOverflowItems.length>0?(this._renderHamburgerIcon(),n(".e-menu-popwrap").length?this._addOverflowItems():this._renderPopupWrapper(),this.lastelement=this.element.find(">li.e-list:visible").last().addClass("e-last"),this.element.find(">li.e-list.e-haschild>ul").find("li.e-haschild").find("span.e-icon.e-arrowhead-down").removeClass("e-arrowhead-down").addClass("e-arrowhead-right")):this._menuOverflowItems.length==0&&n("li.e-ham-wrap").length>0&&this.element.find("li.e-ham-wrap").remove()}(this.model.orientation=="vertical"||this.model.menuType==t.MenuType.ContextMenu&&n(window).width()>=768&&this.model.isResponsive)&&this.element.find("span.e-icon.e-arrowhead-down").removeClass("e-arrowhead-down").addClass("e-arrowhead-right")},_renderHamburgerIcon:function(){if(this._ensureOverflowPopup()&&this.element.find("li.e-ham-wrap").length==0){var i=t.buildTag("li.e-ham-wrap"),r=t.buildTag("div");this.hamburgerspan=t.buildTag("span.e-hamburger");r.append(this.hamburgerspan);i.append(r);this.element.append(i);this.model.height!=0?this._setHeight(this.model.height):n("li.e-ham-wrap").css({height:this.element.find("li.e-list").first().height()});this._on(this.element.find("li.e-ham-wrap"),"click",this._overflowOpen)}},_addOverflowItems:function(){var i,r,u;if(this._ensureOverflowPopup()&&n(".e-menu-popwrap").length>0&&!t.isNullOrUndefined(this.popupWrapper)){for(this.popupWrapper.empty(),this._menuCloneItems.length=0,i=0;i<this._menuOverflowItems.length;i++)this._menuCloneItems.push(n(this._menuOverflowItems[i]).clone(!0));for(this.ulTag=t.buildTag("ul"),this.ulTag.addClass("e-menu e-js e-responsive e-widget e-box e-vertical"),this.popupWrapper.append(this.ulTag),i=0;i<this._menuCloneItems.length;i++)n(this._menuCloneItems[i]).hasClass("e-haschild")&&(n(this._menuCloneItems[i]).find("span.e-icon").removeClass("e-arrowhead-down e-arrowhead-right").addClass("e-arrowhead-down"),n(this._menuCloneItems[i]).children("span.e-menu-arrow.e-menu-left").remove()),this.ulTag.append(this._menuCloneItems[i]);n(this.ulTag).children("li").removeClass("e-menu-hide");r=Math.round(this.popupWrapper.width());r>0&&(u=this.popupWrapper.innerWidth(),this.popupWrapper.find("ul.e-menu").css({width:u+"px"}));this.model.enableSeparator&&this._setSeparator(!0)}},_overflowOpen:function(n){if(this._isOverflowPopupOpen()){var i=t.util.getOffset(this.element),r=i.left+(this.model.enableRTL?0:this.element.outerWidth()-this.popupWrapper.outerWidth()),u=i.top+this.element.outerHeight();this.wrapper.parent().length&&(this.wrapper.parent().css("position")=="absolute"||this.wrapper.parent().css("position")=="relative")&&(i=t.util.getOffset(this.wrapper.parent()),r=r-i.left,u=u-i.top);this.popupWrapper.css({left:r,top:u});this.popupWrapper.show();this._trigger("overflowOpen",{e:n})}else this._overflowClose(n)},_overflowClose:function(n){this._ensureOverflowPopup()&&!t.isNullOrUndefined(this.popupWrapper)&&(this.popupWrapper.find("li.e-list").removeClass(".e-mhover.e-active.e-mfocused"),t.browserInfo().name==="chrome"||t.browserInfo().name==="opera"?this._hideAnimation(this.popupWrapper.find("li.e-list:has(ul)").find("> ul:visible"),this._hideAnim):this._hideAnimation(this.popupWrapper.find('li.e-list:has("> ul")').find("> ul:visible"),this._hideAnim),this.popupWrapper.hide(),this._trigger("overflowClose",{e:n}))},_isOverflowPopupOpen:function(){if(n(this.popupWrapper).length>0)return this.popupWrapper.css("display")=="none"},_removePopup:function(){n(window).width()<767&&this.model.isResponsive&&(this._ensureResponsiveClasses(n(window).width()),this.element.find("li.e-ham-wrap").length>0&&this.popupWrapper.length>0&&(this.element.find("li.e-ham-wrap").remove(),n(".e-menu-popwrap").remove(),this.element.find("li.e-list").addClass("e-menu-show")))},_mobileResponsiveMenu:function(){this.model.menuType!=t.MenuType.ContextMenu&&this.model.orientation!="vertical"&&this.element.css("display")=="none"?this.element.removeClass("e-res-hide").addClass("e-res-show"):this.model.menuType==t.MenuType.ContextMenu||this.model.orientation=="vertical"||this.element.css("display")=="none"||this.element.removeClass("e-res-show").addClass("e-res-hide")},_ensureOverflowPopup:function(){return this.model.menuType!=t.MenuType.ContextMenu&&this.model.orientation!="vertical"&&n(window).width()>=768&&this.model.isResponsive},_onResize:function(){this.element.find("li.e-ham-wrap").hide();n(window).width()>=768?this._calculateOverflowItems():this._removePopup()},_ensureResponsiveClasses:function(n){n&&this.element.find("span.e-icon").hasClass("e-arrowhead-right")&&this.element.find("span.e-icon.e-arrowhead-right").removeClass("e-arrowhead-right").addClass("e-arrowhead-down")},_responsiveLayout:function(){this.model.menuType!=t.MenuType.ContextMenu&&this.model.orientation!="vertical"&&(this.wrapper.addClass("e-menu-responsive"),this.element.addClass("e-menu-responsive"),this.resWrap=t.buildTag("span.e-menu-res-wrap e-menu-responsive"),this.inResWrap=t.buildTag("span.e-in-wrap e-box e-menu-res-in-wrap"),this.label=t.buildTag("span.e-res-title").html(this.model.locale=="en-US"?this.model.titleText:t.Menu.Locale[this.model.locale]&&t.Menu.Locale[this.model.locale].titleText?t.Menu.Locale[this.model.locale].titleText:this.model.titleText),this.check=t.buildTag("span.e-check-wrap e-icon"),this.wrapper.append(this.resWrap),this.resWrap.append(this.inResWrap),this.inResWrap.append(this.label).append(this.check))},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.wrapper.addClass(n):t=="disabled"&&n=="disabled"?i.disable():i.element.attr(t,n)})},_oncheck:function(n){var t=this.element.parents(".e-menu-wrap").children(".e-menu");n.isChecked?t.removeClass("e-res-hide").addClass("e-res-show"):t.removeClass("e-res-show").addClass("e-res-hide")},_addClass:function(){var e,o,i,u,f,r;for(t.browserInfo().name==="chrome"||t.browserInfo().name==="opera"?this.element.find("li:has(ul)").find("> a,> span").addClass("aschild"):this.element.find('li:has("> ul")').find("> a,> span").addClass("aschild"),this.element.find(">li").addClass("e-list").attr({role:"menuitem"}),this.element.find("li").find(">a, >span").addClass("e-menulink"),e=this.element.find(".e-list a.aschild").length==0?this.element.find(".e-list"):this.element.find(".e-list a.aschild"),o=this.element.find(".e-list span.aschild"),r=0;r<e.length;r++)if(i=n(e[r]),i.siblings().attr({"aria-hidden":!0}),i.parent().attr({"aria-haspopup":!0,role:"menuitem","aria-label":i.text()}).addClass("e-haschild"),i.siblings("ul").children("li").addClass("e-list").attr("role","menuitem"),t.isNullOrUndefined(i.siblings("ul").children("li").children("a")[r]))f=i.text(),i.attr({"aria-label":f});else for(j=0;j<i.siblings("ul").children("li").children("a").length;j++)f=i.siblings("ul").children("li").children("a")[j].text,i.siblings("ul").children("li")[j].setAttribute("aria-label",f);for(r=0;r<o.length;r++)u=n(o[r]),u.siblings().attr({"aria-hidden":!0}),u.parent().attr({"aria-haspopup":!0,role:"menu"}).addClass("e-haschild"),u.siblings("ul").children("li").addClass("e-list").attr("role","menuitem")},_renderArrow:function(){if(this.model.menuType!=t.MenuType.ContextMenu&&this.model.orientation!="vertical"&&n(n(this.element).find("span.e-menu-arrow")).length==0){var i=t.buildTag("span.e-menu-arrow e-menu-left");n(i).append("<span class='e-arrowMenuOuter'><\/span>").append("<span class='e-arrowMenuInner'><\/span>");this.element.find(">li.e-list.e-haschild").append(i)}},_generateTemplate:function(n){var i=this,r;n instanceof t.DataManager?(r=n.executeQuery(this._columnToSelect(this.model.fields)),r.done(function(n){i._odataFlag=!0;i._generateItemTemplate(n.result);i.model.height!=0&&i._setHeight(i.model.height);i._wireEvents("_on")})):(i._odataFlag=!1,this._generateItemTemplate(i.model.fields.dataSource),this._wireEvents("_on"))},_generateItemTemplate:function(n){for(var i,t=0;t<n.length;t++)(n[t][this.model.fields.parentId]==null||n[t][this.model.fields.parentId]==0)&&(i=this._menuTemplate(n[t],n,this.model.fields),this.element.append(i))},_menuTemplate:function(i,r,u){var f,o,l,y,s,a,p,e,c,h,v;if(f=n(document.createElement("li")),f.attr("class","e-list"),i[u.htmlAttribute]&&this._setAttributes(i[u.htmlAttribute],f),o=n(document.createElement("a")),o.attr("class","e-menulink"),i[u.imageUrl]&&i[u.imageUrl]!=""?(l=n(document.createElement("img")),l.attr("src",i[u.imageUrl]),i[u.imageAttribute]&&this._setAttributes(i[u.imageAttribute],l),o.append(l)):i[u.spriteCssClass]&&i[u.spriteCssClass]!=""&&(y=n(document.createElement("span")),y.addClass(i[u.spriteCssClass]),o.append(y)),o.append(i[u.text]),i[u.linkAttribute]&&this._setAttributes(i[u.linkAttribute],o),i[u.url]&&o.attr("href",i[u.url]),f.append(o),i[u.id]&&f.prop("id",i[u.id]),t.isNullOrUndefined(u.child)){if(!this._odataFlag&&(e=t.DataManager(u.dataSource).executeLocal(t.Query().where(u.parentId,t.FilterOperators.equal,i[u.id])),e&&e.length>0)){for(c=t.buildTag("ul"),h=0;h<e.length;h++)v=this._menuTemplate(e[h],u.dataSource,u),c.append(v);f.append(c)}}else if(this._odataFlag=!0,u.child.dataSource instanceof t.DataManager)s=this,a=t.Query(),n(f).attr({"aria-haspopup":!0,role:"menu"}).addClass("e-haschild"),a=this._columnToSelect(u.child),a.where(u.child.parentId,t.FilterOperators.equal,i[u.id]),p=u.child.dataSource.executeQuery(a),p.done(function(t){var i=t.result,e,r,o;if(i&&i.length>0){for(e=n(document.createElement("ul")),r=0;r<i.length;r++)o=s._menuTemplate(i[r],u.child.dataSource,u.child),e.append(o);f.append(e);n(f).children("a").addClass("aschild");n(f).parent().hasClass("e-menu")&&s.model.showRootLevelArrows?n(f).children("a.aschild").append(n("<span>").addClass("e-icon e-arrowhead-down")).addClass("e-arrow-space"):s.model.showSubLevelArrows&&n(f).children("a.aschild").append(n("<span>").addClass("e-icon e-arrowhead-right")).addClass("e-arrow-space");s.model.height!=0&&s._setHeight(s.model.height)}}),p.then(function(){s._renderArrow()});else if(t.isNullOrUndefined(i.child)||(t.isPlainObject(i.child)?e=t.DataManager(u.child.dataSource).executeLocal(t.Query().where(u.child.parentId,t.FilterOperators.equal,i[u.id])):i.child instanceof Array&&(e=i.child)),e&&e.length>0){for(c=n(document.createElement("ul")),h=0;h<e.length;h++)v=this._menuTemplate(e[h],u.child.dataSource,u.child),c.append(v);f.append(c)}return f},_setAttributes:function(n,t){for(var i in n)i=="class"?t.addClass(n[i]):t.attr(i,n[i])},_addArrow:function(i,r){if(i){var u=this.model.orientation=="horizontal"?"e-arrowhead-down":"e-arrowhead-right";t.browserInfo().name==="chrome"||t.browserInfo().name==="opera"?this.element.find(">li.e-list:has(ul)").children("a").append(n("<span>").addClass("e-icon "+u)).addClass("e-arrow-space"):this.element.find('>li.e-list:has("> ul")').children("a").append(n("<span>").addClass("e-icon "+u)).addClass("e-arrow-space")}else t.browserInfo().name==="chrome"||t.browserInfo().name==="opera"?this.element.find(">li.e-list:has(ul)").children("a").removeClass("e-arrow-space").children("span.e-icon").remove():this.element.find('>li.e-list:has("> ul")').children("a").removeClass("e-arrow-space").children("span.e-icon").remove();r?this.element.find(">li.e-list > ul li.e-list:has(>ul)").children("a").append(n("<span>").addClass("e-icon e-arrowhead-right")).addClass("e-arrow-space"):this.element.find(">li.e-list > ul li.e-list:has(>ul)").children("a").removeClass("e-arrow-space").children("span.e-icon").remove()},_intializeMenu:function(){this.model.height!=0&&this._setHeight(this.model.height);this.model.width!=0&&this._setWidth(this.model.width);this.model.menuType=="contextmenu"&&(this.model.openOnClick=!1);this.model.openOnClick&&(this._hoverOpen=!1,this._hoverClose=!1)},_setOrientation:function(n){n=="horizontal"?this.element.removeClass("e-vertical e-horizontal").addClass("e-horizontal"):this.element.removeClass("e-horizontal e-vertical").addClass("e-vertical");n=="vertical"&&this._removePopup()},_setHeight:function(i){this.model.orientation=="horizontal"&&i!=="auto"?(i=typeof i=="number"?i+"px":i,this.element.find("> li").find(">a:first").css("line-height",i),this.model.showRootLevelArrows&&this.element.find("> li").find(">a:first").find("> span:first").css({"line-height":i,top:"0px"}),this.model.menuType!=t.MenuType.ContextMenu&&this.model.orientation!="vertical"&&n("li.e-ham-wrap").length>0&&(this.element.find("li.e-ham-wrap").children("div").css({"line-height":i}),this.element.find("li.e-ham-wrap").css({height:i}),this.popupWrapper&&this.popupWrapper.find("a.e-menulink").css({"line-height":i}))):this.element.height(i)},_setWidth:function(n){this.element.css("width",n);this.model.orientation==="horizontal"&&n!=="auto"&&this.model.isResponsive&&this.resWrap.css("width",n);this.model.orientation=="horizontal"&&this.model.menuType!=t.MenuType.ContextMenu&&this.model.orientation!="vertical"&&this._calculateOverflowItems()},_setOverflowDimensions:function(n,i){this.model.menuType!=t.MenuType.ContextMenu&&this.model.orientation!="vertical"&&(i=typeof i=="number"?i+"px":i);n=="height"?this.popupWrapper.css({height:i}):n=="width"&&this.popupWrapper.css({width:i});this._addOverflowItems()},_setRTL:function(n){n?this.element.removeClass("e-rtl").addClass("e-rtl"):this.element.removeClass("e-rtl");n&&this.model.orientation==="horizontal"?this.wrapper.removeClass("e-menu-rtl").addClass("e-menu-rtl"):this.wrapper.removeClass("e-menu-rtl");this.model.subMenuDirection=n?"left":"right"},_setSubMenuDirection:function(n){n!="left"&&n!="right"&&(this.model.subMenuDirection=this.model.enableRTL?"left":"right")},_setAnimation:function(n){n==="none"?(this._showAnim="none",this._hideAnim="none"):(this._showAnim="slideDown",this._hideAnim="slideUp")},_controlStatus:function(n){n!=!0?this.disable():this.enable()},_centerAlign:function(n){this.model.orientation=="horizontal"&&n?this.element.css("text-align","center"):this.element.css("text-align","inherit")},_columnToSelect:function(n){var u=[],r=t.Query(),i;if(t.isNullOrUndefined(n.query)){for(i in n)i!=="tableName"&&i!=="child"&&i!=="dataSource"&&n[i]&&u.push(n[i]);u.length>0&&r.select(u);this.model.fields.dataSource.dataSource.url.match(n.tableName+"$")||t.isNullOrUndefined(n.tableName)||r.from(n.tableName)}else r=n.query;return r},_max_zindex:function(){var i,r,t,u;return this.model.menuType=="contextmenu"?(i=n(this._targetElement).parents(),i.push(this._targetElement)):i=n(this.element).parents(),r=n("body").children(),u=r.index(this.popup),r.splice(u,1),n(r).each(function(n,t){i.push(t)}),t=Math.max.apply(t,n.map(i,function(t){if(n(t).css("position")!="static")return parseInt(n(t).css("z-index"))||1})),!t||t<1e4?t=1e4:t+=1,t},_recursiveFunction:function(t,i){var r=this,u=!1;n.each(t,function(n,t){return t.Text==i?(r.selectedItem=t,u=!0,!1):(t.ChildItems!=null&&r._recursiveFunction(t.ChildItems,i),u?!1:void 0)})},_contextMenu_Template:function(){if(this.element[0].id!="")var t=n(".e-menu-wrap #"+this.element[0].id).get(0);t&&n(t.parentElement).remove();this.model.orientation="vertical";this.element.addClass(this.model.cssClass+" e-context");this.element.css("display","none");this._referenceElement=this.element.parent();n(this.model.contextMenuPopupTarget).append(this.element);this.wrapper.insertBefore(this.element);this.wrapper.append(this.element)},_closeMenu:function(){t.browserInfo().name==="chrome"||t.browserInfo().name==="opera"?this._hideAnimation(this.element.find("li.e-list:has(ul)").find("> ul:visible"),this._hideAnim):this._hideAnimation(this.element.find('li.e-list:has("> ul")').find("> ul:visible"),this._hideAnim)},_onMenuIntent:function(n,t,i){t._delayMenuHover=window.setTimeout(function(){if(t._mouseOver==!0&&i){var r=t._showAnim,u=t._hideAnim,f=t._showSpeed,e=t._hideSpeed;t._show(n,r,u)}},this._showSpeed)},_onHide:function(n,t,i){t._delaySubMenuHover=window.setTimeout(function(){if(t._mouseOver==!1&&i){var n=t._id,r=t._hideAnim,u=t._hideSpeed;t._closeAll()}},t._hideSpeed)},_subMenuPos:function(t,r){var e=n(t).offset(),a,v,o=r=="right"?e.left+n(t).width():e.left,u=n("ul:first",t),c=n(t).outerWidth(),f,h,l,w,s,b;if(e==null||e==i)return!1;if(f=u.outerWidth()+1,h=this.model.container?n(this.model.container).width()+n(document).scrollLeft():document.documentElement.clientWidth+n(document).scrollLeft(),this.model.menuType=="normalmenu")if(n(t.parentNode).is(this.element))this.model.orientation=="horizontal"?(u.css("top",n(t).outerHeight()+"px"),this.model.enableRTL?(v=o+c-f<0?o+c-f:1,u.css({left:"auto",right:v+"px"})):(a=h<o+f?o+f-h:1,u.css("left",a*-1+"px"))):r=="left"&&o>f||r=="right"&&h<=e.left+c+f&&o>f?u.css("left",-(f+4)+"px"):u.css("left",n(t).outerWidth()+4+"px");else if(r=="left"&&o>f||r=="right"&&h<=e.left+c+f&&o>f)u.css("left",-(f+4)+"px");else{u.css("left",n(t).outerWidth()+4+"px");var s=u.outerHeight(),y=n(window).height(),p=y-(e.top-n(window).scrollTop());y<s?(l=e.top-n(window).scrollTop(),u.css("top",-l+4+"px")):p<s?(l=p-s,u.css("top",l-2+"px")):u.css("top","")}else h-=o+2*f+4,h<0?(w=f==null?"-206.5px":"-"+(f+5)+"px",u.css("left",w)):u.parent("li.e-list").parent("ul").width()&&r=="right"?u.css("left",u.parent("li.e-list").parent("ul").width()+4+"px"):e.left>f&&u.css("left",-(f+4)+"px"),s=u.outerHeight(),e.top+s>n(window).height()?(b=-s+n(t).outerHeight(),s>e.top+n(t).outerHeight()/2?u.css("top",-(s/2)+"px"):u.css("top",b+"px")):u.css("top","0px")},_setSkin:function(n){this.wrapper.removeClass(this.model.cssClass).addClass(n+" e-menu-wrap")},_setSeparator:function(i){i?(this.element.addClass("e-separator"),n(".e-menu-popwrap").length>0&&!t.isNullOrUndefined(this.ulTag)&&this.ulTag.addClass("e-separator")):this.element.removeClass("e-separator")},_contextMenuEvents:function(t){this[t](n(this.model.contextMenuTarget),"mouseup taphold",this._ContextMenuHandler);this[t](this.element,"contextmenu",this._onDefaultPreventer);this[t](n(this.model.contextMenuTarget),"contextmenu",this._onDefaultPreventer);this[t](n(document),"mousedown",this._onContextClose)},_show:function(t,i,r){var f,u=n("> ul",t),e=this._max_zindex();u.attr({"aria-hidden":!1});this._hideAnimation(n(t).siblings().find(" > ul:visible"),r);n.inArray(this._disabledMenuItems,t)>-1||(u.css("display")!="none"?(f=this.model.openOnClick?n(u):u.children().find("> ul"),this._hideAnimation(f,r)):n("> ul",t).children().find("> ul").hide(),this._subMenuPos(t,this.model.subMenuDirection),u.css({"z-index":e+1}),n(t).children("span.e-menu-arrow").css({"z-index":e+2}),n("> ul",t).css("display")=="block"||n(t).hasClass("e-disable-item")||(this._showAnimation(u,i),u.closest("li").addClass("e-active e-mfocused")),n(t).siblings("li.e-active").length>0&&n(t).siblings("li.e-active").removeClass("e-active e-mfocused"))},_closeAll:function(){t.browserInfo().name==="chrome"||t.browserInfo().name==="opera"?this._hideAnimation(this.element.find("li.e-list:has(ul)").find("> ul:visible"),this._hideAnim):this._hideAnimation(this.element.find('li.e-list:has("> ul")').find("> ul:visible"),this._hideAnim);this._hideAnimation(this.element.find("> ul:visible"),this._hideAnim)},_showAnimation:function(i,r){var u=this;switch(r){case"slideDown":if(this.model.menuType=="contextmenu")i.slideDown(this.model.enableAnimation?200:0,function(){u._on(t.getScrollableParents(n(u.model.contextMenuTarget)),"scroll",u.hideContextMenu)});else{i.slideDown(this.model.enableAnimation?200:0);break}case"none":i.css("display","block")}},_hideAnimation:function(i,r){var u=this;switch(r){case"slideUp":if(n(i).attr({"aria-hidden":!0}),this.model.menuType=="contextmenu")i.slideUp(this.model.enableAnimation?100:0,function(){u._off(t.getScrollableParents(n(u.model.contextMenuTarget)),"scroll",u.hideContextMenu)});else{i.slideUp(this.model.enableAnimation?100:0);break}case"none":i.css("display","none")}i.closest("li").removeClass("e-active e-mfocused")},_removeValue:function(i,r){for(var f=t.browserInfo(),e=f.version==="8.0"&&f.name==="msie"?i[0].outerText:i[0].textContent,o=n(r).length,u=0,s=n(r).children("a").length==0?n(r).children("span"):n(r).children("a");u<=o;){if(n(s[u]).text()===e)return u;u++}},_createSubLevelItem:function(i,r){var u;u=n(document.createElement("ul"));u.append(r);i.append(u);i.attr({role:"menu","aria-haspopup":"true"});i.addClass("e-haschild");t.browserInfo().name==="chrome"||t.browserInfo().name==="opera"?this.element.find("li:has(ul)").find("> a,>span").addClass("aschild e-arrow-space"):this.element.find('li:has("> ul")').find("> a,>span").addClass("aschild e-arrow-space");this._insertArrows(u)},_insertArrows:function(t){this.model.showRootLevelArrows?t.find(">a,>span").append(n("<span>").addClass("e-icon e-arrowhead-down")).addClass("e-arrow-space"):t.find(">a,>span").removeClass("e-arrow-space").find(">span.e-icon").remove();this.model.showSubLevelArrows?t.parent("li.e-list:has(>ul)").children("a,span").append(n("<span>").addClass("e-icon e-arrowhead-right")).addClass("e-arrow-space"):t.parent("li.e-list:has(>ul)").children("a,span").removeClass("e-arrow-space").find(">span.e-icon").remove()},_createMenuItem:function(t){var r,i,u,f;return r=n(document.createElement("li")),r.attr({"class":"e-list",role:"menuitem"}),t.htmlAttribute&&this._setAttributes(t.htmlAttribute,r),t.text&&t.text!=""&&(i=n(document.createElement("a")),i.attr({"class":"e-menulink"}),t.imageUrl&&t.imageUrl!=""?(u=n(document.createElement("img")),u.attr("src",t.imageUrl),t.imageAttribute&&this._setAttributes(t.imageAttribute,u),i.append(u)):t.spriteCssClass&&t.spriteCssClass!=""&&(f=n(document.createElement("span")),f.addClass(t.spriteCssClass),i.append(f)),i.append(t.text),t.linkAttribute&&this._setAttributes(t.linkAttribute,i),t.url&&i.attr("href",t.url),r.append(i)),t.id&&r.prop("id",t.id),this.model.enabled||r.addClass("e-disable-item"),r},_insertNode:function(i,r,u){var e=0,o=0,s=0,f=[];for(n(r).is(this.element)?f.push(this.element):typeof r=="string"?f.push(this.element.find(r)):typeof r=="undefined"?f.push(this.element):f.push(r),o=0;o<f.length;o++)for(s=0;s<f[o].length;s++)for(e=0;e<i.length&&!t.isNullOrUndefined(i[e]);e++)this._addItem(i[e],f[o][s],u)},_addItem:function(t,i,r){var u,f;this._wireEvents("_off");u=this._createMenuItem(t);i=i==="default"?n("#"+t.parentId):n(i);switch(r){case"insert":f=n(i).is(this.element)?i:i.children("ul");f.length!=0?f.append(u):this._createSubLevelItem(i,u);break;case"insertBefore":n(i).is(this.element)?i.prepend(u):u.insertBefore(i);break;case"insertAfter":n(i).is(this.element)?i.append(u):u.insertAfter(i)}this._wireEvents("_on")},_removeItem:function(n){n.siblings("li").length==0?(n.closest("ul").siblings("a.aschild").removeClass("aschild e-arrow-space").children("span.e-icon").remove(),n.closest("ul").hasClass("e-menu")?n.remove():n.closest("ul").remove()):n.remove()},_hiddenElement:function(t){t.length>0&&n.inArray(t[0],this._hiddenMenuItems)==-1&&(t.addClass("e-hidden-item"),this._hiddenMenuItems.push(t[0]))},_showElement:function(t){t.length>0&&n.inArray(t[0],this._hiddenMenuItems)>-1&&(t.removeClass("e-hidden-item"),this._hiddenMenuItems.splice(this._hiddenMenuItems.indexOf(t[0]),1))},_getNodeByID:function(i){return t.isNullOrUndefined(this.popupWrapper)?typeof i!="object"&&i!=""&&(i=this.element.find(".e-list"+i)):typeof i!="object"&&i!=""&&(i=this.popupWrapper.children().find(".e-list"+i)),n(i)},_processItems:function(t,i){for(var u=this._getNodeByID(t),r=0;r<u.length;r++)i?this._showElement(n(u[r])):this._hiddenElement(n(u[r]))},insert:function(n,t){this._insertNode(n,t,"insert")},insertBefore:function(n,t){this._insertNode(n,t,"insertBefore")},insertAfter:function(n,t){this._insertNode(n,t,"insertAfter")},remove:function(t){for(var i=0,r=0,i=0;i<t.length;i++)for(t[i]=typeof t[i]=="string"?this.element.find(t[i]):t[i],r=0;r<t[i].length;r++)t[i][r].tagName==="LI"||t[i][r].tagName==="UL"?this._removeItem(n(t[i][r])):t[i][r].remove()},showContextMenu:function(n,i,r,u,f){if(this._closeMenu(),this._eventArgs=u,t.isNullOrUndefined(u)||!this._checkForExclusion(u.target)){if(this._trigger("beforeOpen",{target:r,events:u}))return!1;if(this._preventContextOpen){if(this._targetElement=t.isNullOrUndefined(r)?t.isNullOrUndefined(target)?this.element:target:r,f){var e=this._calculateContextMenuPosition(u);n=e.X;i=e.Y}this.element.css({left:n,top:i});this.element.css({"z-index":this._max_zindex()+1});this._showAnimation(this.element,this._showAnim);this._isContextMenuOpen=!0;this.element.focus();this._trigger("open",{target:r})}return!1}},_checkForExclusion:function(i){var u,r;if(!t.isNullOrUndefined(this.model.excludeTarget))for(u=this.model.excludeTarget.split(","),r=0;r<u.length;r++)if(n(i).closest(this.model.excludeTarget).is(n.trim(u[r])))return!0},hideContextMenu:function(t){this._closeMenu();this.element.find(".e-mhover").removeClass("e-mhover");this.element.find(".e-mfocused").removeClass("e-mfocused");this._hideAnimation(this.element,this._hideAnim);this._isContextMenuOpen=!1;this._trigger("close",n.extend({events:t},t))},disableItem:function(t){var i=n(this.element.find("li.e-list >a ,li.e-list >span")).filter(function(){return n.trim(n(this).text())===t});i.length>0&&!(n.inArray(i.parent()[0],this._disabledMenuItems)>-1)&&(i.parent().addClass("e-disable-item").attr({"aria-disabled":!0}),i.parent().find(">a.aschild span.e-icon").addClass("e-disable"),this._disabledMenuItems.push(i.parent()[0]))},disableItemByID:function(t){if(t&&t!=""){var r=this.element.find("#"+t)?this.element.find("#"+t)[0]:i;!r||n.inArray(r,this._disabledMenuItems)>-1||(n(r).addClass("e-disable-item").attr({"aria-disabled":!0}),n(r).find(">a.aschild span.e-icon").addClass("e-disable"),this._disabledMenuItems.push(r))}},getHiddenItems:function(){return this._hiddenMenuItems},hideItems:function(n){if(typeof n=="object"&&n.length!==i)for(var t=0;t<n.length;t++)this._processItems(n[t],!1);else this._processItems(n,!1)},showItems:function(n){if(typeof n=="object"&&n.length!==i)for(var t=0;t<n.length;t++)this._processItems(n[t],!0);else this._processItems(n,!0)},enableItem:function(t){var i=n(this.element.find("li.e-list >a ,li.e-list >span")).filter(function(){return n.trim(n(this).text())===t}),r;i.length>0&&n.inArray(i.parent()[0],this._disabledMenuItems)>-1&&(i.parent().removeClass("e-disable-item").attr({"aria-disabled":!1}),i.parent().find(">a.aschild span.e-icon").removeClass("e-disable"),r=this._removeValue(i,this._disabledMenuItems),this._disabledMenuItems.splice(r,1))},enableItemByID:function(t){var i,r;if(t&&t!=""&&(i=this.element.find("#"+t)[0],i&&n.inArray(i,this._disabledMenuItems)>-1))for(n(i).removeClass("e-disable-item").attr({"aria-disabled":!1}),n(i).find(">a.aschild span.e-icon").removeClass("e-disable"),r=this._disabledMenuItems.length-1;r>=0;r--)this._disabledMenuItems[r].id==t&&this._disabledMenuItems.splice(r,1)},disable:function(){this.model.enabled=!1;var i=this.element.find(">li[class~=e-list]"),t=this;n.each(i,function(i,r){n.inArray(r,t._disabledMenuItems)>-1||(n(r).addClass("e-disable-item").attr({"aria-disabled":!0}),n(r).find(">a.aschild span.e-icon").addClass("e-disable"),t._disabledMenuItems.push(r))})},enable:function(){var i=this,t;this.model.enabled=!0;t=this.element.find("li.e-disable-item");n.each(t,function(t,r){n(r).removeClass("e-disable-item").attr({"aria-disabled":!1});n(r).find(">a.aschild span.e-icon").removeClass("e-disable");i._disabledMenuItems.pop(r)})},show:function(n,t,i,r){if(!this.model.enabled)return!1;this.model.menuType=="contextmenu"?this.showContextMenu(n,t,i,r,!1):this.element.css("display","block")},hide:function(n){if(!this.model.enabled)return!1;this.model.menuType=="contextmenu"?this.hideContextMenu(n):(this._closeMenu(),this.element.css("display","none"))},_wireEvents:function(t){this[t](this.element.find("li.e-list"),"mouseout",this._mouseOutHandler);this[t](this.element.find("li.e-list"),"mouseover",this._mouseOverHandler);this[t](this.element.children(),"click",this._onClickHandler);this[t](this.element,"keydown",this._onKeyDownHandler);this[t](this.element,"focus",this._OnFocusHandler);this[t](this.element,"blur",this._OnFocusOutHandler);this.model.menuType=="contextmenu"&&n(this.model.contextMenuTarget)[0]!=null&&this._contextMenuEvents(t);this.model.menuType!="contextmenu"&&(this[t](n(document),"click",this._onDocumentClick),this[t](this.element,"mousedown",this._onMouseDownHandler));this[t](n(window),"resize",n.proxy(this._onResize,this))},_mouseOverHandler:function(i){var r,f="",e,u;if(this.element.find(".e-mhover").removeClass("e-mhover"),i.currentTarget=n(i.target).closest("li")[0],n(i.currentTarget).hasClass("e-disable-item")?this._isFocused=!1:n(i.currentTarget).addClass("e-mhover"),i.stopPropagation&&i.stopPropagation(),typeof this._delaySubMenuHover!="undefined"&&clearTimeout(this._delaySubMenuHover),typeof this._delaySubMenuHover!="undefined"&&clearTimeout(this._delayMenuHover),this._mouseOver=!0,this._isMenuOpen=!0,this._isSubMenuOpen=n(i.currentTarget.parentNode.parentNode).is(this.element)?!1:!0,i.currentTarget.nodeName=="LI")r=i.currentTarget;else if(i.currentTarget.parentNode)if(i.currentTarget.parentNode.nodeName=="LI")r=i.currentTarget.parentNode;else return!1;else return i.preventDefault(),!1;n(i.currentTarget).hasClass("e-disable-item")||this._onMenuIntent(r,this,this._hoverOpen);n.inArray(r,this._disabledMenuItems)>-1||(e=n(r).children("a,span").text(),f=t.isNullOrUndefined(r)?"":n(r)[0].id,u={text:e,element:r,event:i,ID:f},this._trigger("mouseover",n.extend({events:u},u)))},_onMouseDownHandler:function(t){n(t.target).hasClass("e-menu")&&(this._isFocused=!1)},_mouseOutHandler:function(i){var r,f="",e,u;if(n(i.currentTarget).removeClass("e-mhover"),i.stopPropagation&&i.stopPropagation(),typeof this._delaySubMenuHover!="undefined"&&clearTimeout(this._delaySubMenuHover),typeof this._delaySubMenuHover!="undefined"&&clearTimeout(this._delayMenuHover),this._mouseOver=!1,this._isMenuOpen=!1,i.currentTarget.nodeName=="LI")r=i.currentTarget;else if(i.currentTarget.parentNode)if(i.currentTarget.parentNode.nodeName=="LI")r=i.currentTarget.parentNode;else return!1;else return i.preventDefault(),!1;this._onHide(r,this,this._hoverClose);n.inArray(r,this._disabledMenuItems)>-1||(e=n(r).children("a,span").text(),f=t.isNullOrUndefined(r)?"":n(r)[0].id,u={text:e,element:r,event:i,ID:f},this._trigger("mouseout",n.extend({events:u},u)))},_onClickHandler:function(i){var r,h="",f,e,o,c,u,s;if(this._isFocused=!0,o=!1,!n(i.target).closest("li.e-list").hasClass("e-disable-item")&&n(i.target).closest("li.e-list").length>0)r=n(i.target).closest("li.e-list")[0],n(r).is(this.element.find(">li.e-list"))&&(this._activeElement=r);else{n(i.target).is(this.element)&&(this._activeElement=this.element.find(">li:first"));return}n(i.target).is("a")&&n(r).find(">a,>span").hasClass("aschild")&&this.model.openOnClick&&(this._isFocused=!1);!this._hoverOpen&&n(r).find(">a,>span").hasClass("aschild")&&(this._show(r,this._showAnim,this._hideAnim),this._hoverOpen=!1,o=!0);n.inArray(r,this._disabledMenuItems)>-1||(this.model.menuType=="contextmenu"&&this._isContextMenuOpen&&!n(r).hasClass("e-haschild")&&(this._hideAnimation(this.element,this._hideAnim),this._isContextMenuOpen=!1,this._trigger("close",n.extend({events:i},i))),o||n(r).find(">a,>span").hasClass("aschild")||(this._closeMenu(),this.model.openOnClick&&(this._hoverOpen=!1)),c=n(r).children("a,span").text(),u=n(r).closest("ul").parent("li"),u.length!=0?(f=t.isNullOrUndefined(u.attr("id"))?null:u.attr("id"),e=u.children("a,span").text()):(f=null,e=null),h=t.isNullOrUndefined(r)?"":n(r)[0].id,s={text:c,element:r,event:i,selectedItem:this.selectedItem,ID:h,parentId:f,parentText:e},this._trigger("click",n.extend({events:s},s)),this.selectedItem=null,this.model.openOnClick&&this.model.menuType!="contextmenu"&&this.element.focus())},_onKeyDownHandler:function(i){var l,c,o;if(i.target&&i.target.nodeName&&n(i.target).closest("input, textarea").length>0)return!0;if(this.model.menuType!="contextmenu"||this._isContextMenuOpen){var s,e,h="",r=this.element.find(".e-mhover"),f=this.element.find(".e-mfocused"),a,u;if(!n(r).length>0&&n(this._activeElement).length>0&&(r=f=n(this._activeElement)),i.keyCode==9?(this._isFocused=!1,this._OnFocusOutHandler()):(i.keyCode==37||i.keyCode==38||i.keyCode==39||i.keyCode==40)&&i.preventDefault(),i.keyCode==40&&(this.model.orientation=="horizontal"?this.element.find(">li.e-mhover").children("ul").length>0||n(this._activeElement).length>0?(n(r).children("ul").css("display")==="none"&&this._show(r[r.length-1],this._showAnim,this._hideAnim),r.removeClass("e-mhover e-mfocused").children("ul:first").find("li:first").addClass("e-mhover"),this._activeElement==null?r.addClass("e-mfocused"):n(this._activeElement).addClass("e-mfocused")):(u=r.parent().children("li.e-list:visible:not(.e-hidden-item, .e-disable-item)"),n(r[r.length-1]).removeClass("e-mfocused e-mhover"),e=n(u[u.index(r)+1]).length>0?n(u[u.index(r[r.length-1])+1]):u.first(),e.addClass("e-mhover")):this.model.orientation!="horizontal"&&(u=r.length==0?this.element.children("li.e-list:visible:not(.e-hidden-item, .e-disable-item)"):r.parent().children("li.e-list:visible:not(.e-hidden-item, .e-disable-item)"),r.removeClass("e-mfocused e-mfocused"),r.length>0?(r.removeClass("e-mhover"),e=n(u[u.index(r[r.length-1])+1]).length>0?n(u[u.index(r[r.length-1])+1]):u.first()):e=u.first(),e.addClass("e-mhover"))),i.keyCode==39&&(this.model.orientation=="horizontal"&&(this.element.find(">li.e-list").hasClass("e-mhover")||n(this._activeElement).length>0)?(r.removeClass("e-mfocused e-mhover"),u=this.element.children("li.e-list:visible:not(.e-hidden-item, .e-disable-item)"),e=n(u[u.index(r[r.length-1])+1]).length>0?n(u[u.index(r[r.length-1])+1]):u.first(),e.addClass("e-mhover")):n(r).children("ul").length>0?(r.removeClass("e-mfocused e-mhover"),l=r.children("ul:first").find("li:first"),this._show(r[r.length-1],this._showAnim,this._hideAnim),u=r.addClass("e-mfocused").children("ul:first").children("li.e-list:visible:not(.e-hidden-item, .e-disable-item)"),e=n(u[u.index(l)]).length>0?n(u[u.index(l)]):u.first(),e.addClass("e-mhover")):r.children("ul").length<=0&&this.model.orientation=="horizontal"&&r.parent().closest(".e-list").parent().hasClass("e-menu")&&(this._hideAnimation(r.parent(),this._hideAnim),r.removeClass("e-mfocused e-mhover"),n(f[f.length-1]).removeClass("e-mfocused"),u=r.parent().closest(".e-list").parent().children("li.e-list:visible:not(.e-hidden-item, .e-disable-item)"),e=n(u[u.index(f[f.length-1])+1]).length>0?n(u[u.index(f[f.length-1])+1]):n(u[u.index(f.first())]),e.addClass("e-mhover"))),i.keyCode==38&&(this.model.orientation=="horizontal"?(u=r.parent().children("li.e-list:visible:not(.e-hidden-item, .e-disable-item)"),r.removeClass("e-mfocused e-mhover"),e=n(u[u.index(r[r.length-1])-1]).length>0?n(u[u.index(r[r.length-1])-1]):u.last()):this.model.orientation!="horizontal"&&(u=r.length==0?this.element.children("li.e-list:visible:not(.e-hidden-item, .e-disable-item)"):r.parent().children("li.e-list:visible:not(.e-hidden-item, .e-disable-item)"),r.length>0?(r.removeClass("e-mfocused e-mhover"),e=n(u[u.index(r[r.length-1])-1]).length>0?n(u[u.index(r[r.length-1])-1]):u.last()):e=u.last()),e.addClass("e-mhover")),i.keyCode==37&&(this.model.orientation=="horizontal"?this.element.find(">li.e-list").hasClass("e-mhover")||n(this._activeElement).length>0?(r.removeClass("e-mfocused e-mhover"),u=this.element.find("li.e-list:visible:not(.e-hidden-item, .e-disable-item)"),e=n(u[u.index(r[r.length-1])-1]).length>0?n(u[u.index(r[r.length-1])-1]):u.last(),e.addClass("e-mhover")):(this._hideAnimation(r.parent(),this._hideAnim),r.removeClass("e-mfocused e-mhover"),n(f[f.length-1]).removeClass("e-mfocused e-active"),u=r.parent().closest(".e-list").parent().children("li.e-list:visible:not(.e-hidden-item, .e-disable-item)"),e=r.parent().closest(".e-list").parent(".e-menu").length>0?n(u[u.index(f[f.length-1])-1]).length>0?n(u[u.index(f[f.length-1])-1]):u.last():n(u[u.index(f[f.length-1])]).length>0?n(u[u.index(f[f.length-1])]):u.last(),e.addClass("e-mhover")):(r.parent(".e-menu").length==0||this.model.menuType=="contextmenu"&&r.parent("ul.e-context").length==0)&&(this._hideAnimation(r.parent(),this._hideAnim),r.removeClass("e-mfocused e-mhover"),n(f[f.length-1]).removeClass("e-mfocused"),u=r.parent().closest(".e-list").parent().children("li.e-list:visible:not(.e-hidden-item, .e-disable-item)"),e=n(u[u.index(f[f.length-1])]).length>0?n(u[u.index(f[f.length-1])]):n(u[u.index(f.last())]),e.addClass("e-mhover"))),i.keyCode==13&&(c=n(r).children("a,span").text(),h=t.isNullOrUndefined(n(r)[0])?"":n(r)[0].id,o={menuId:this.element[0].id,text:c,selectedItem:f,ID:h},this.model.menuType=="contextmenu"?this._isContextMenuOpen&&r.length>0&&!f.hasClass("e-disable-item")&&(this.model.click&&this._trigger("click",n.extend({events:o},o)),this.selectedItem=null,this.hideContextMenu(i)):r.length>0&&!r.hasClass("e-disable-item")&&(n(r).find(">a,>span").hasClass("aschild")&&n(r).children("ul").css("display")==="none"?(this._show(r[0],this._showAnim,this._hideAnim),r.removeClass("e-mhover").children("ul:first").find("li:first").addClass("e-mhover")):(this.element.find(".e-mhover >a,.e-mhover >span ").focus(),this.element.find("li.e-list").removeClass("e-mhover e-mfocused"),this._closeAll()),t.isNullOrUndefined(n(r).find(">a").attr("href"))&&this._trigger("click",n.extend({events:o},o)))),i.keyCode==27&&(this.model.menuType=="contextmenu"?this.hideContextMenu(i):this.element.find("li.e-list").removeClass("e-mhover"),t.browserInfo().name==="chrome"||t.browserInfo().name==="opera"?this.element.find("li.e-list:has(ul)").find("> ul:visible").parents("li.e-list").addClass("e-mhover"):this.element.find('li.e-list:has("> ul")').find("> ul:visible").parents("li.e-list").addClass("e-mhover"),this._closeAll()),n(i.target).is(this.element)&&i.target.parentNode)r.length&&(s=r);else return!1;n.inArray(s,this._disabledMenuItems)>-1||(c=n(s).children("a,span").text(),h=t.isNullOrUndefined(s)?"":n(s)[0].id,(this.element.find("li.e-mfocused.e-mhover").length||i.keyCode==13)&&(a=i.keyCode==13?r:this.element.find("li.e-mfocused.e-mhover")),o={text:c,element:s,targetElement:a,event:i,ID:h},this._trigger("keydown",n.extend({events:o},o)));this._activeElement=null;f=this.element.find(".e-mfocused")}},_OnFocusHandler:function(){this.model.menuType!="contextmenu"&&!this.element.find(">li:first").hasClass("e-disable-item")&&this._isFocused&&this.element.find(".e-mhover").length==0&&n("li.e-ham-wrap").length==0?this.element.find(">li:first").addClass("e-mhover"):this._isFocused=!0;this.model.menuType!="contextmenu"&&(this._activeElement=this.element.find(">li:first"))},_OnFocusOutHandler:function(){this._isFocused||(this.element.find("li.e-list").removeClass("e-mhover e-mfocused"),this._closeAll());this._isFocused=!1},_onDocumentClick:function(t){this.model.openOnClick&&(this._hoverOpen=!1);n(t.target).parents(".e-menu").is(this.element)||(this.element.find("li.e-list").removeClass("e-mhover e-mfocused"),this._closeAll(),this._isFocused=!0);n(t.target).parents("ul.e-menu").is(this.popupWrapper)||n(t.target).hasClass("e-ham-wrap")||n(t.target).parent().hasClass("e-ham-wrap")||n(t.target).hasClass("e-hamburger")||n(t.target).parent("li").hasClass("e-haschild")||n(t.target).is("span.e-icon.e-arrowhead-down")||this._isOverflowPopupOpen()||this.model.menuType=="contextmenu"||!(n("li.e-ham-wrap").length>0)||this._overflowClose()},_ContextMenuHandler:function(n){var t=!1,r,i,e,u,f;n.type=="taphold"&&n.button!=0?t=!0:n.button?t=n.button==2:n.which&&(t=n.which==3);r=n.target;t?(i=n,n.type=="taphold"&&(i=n.options.type=="touchstart"?n.options.touches[0]:n.options),e=this._showSpeed,this.showContextMenu(null,null,r,i,!0)):this._isContextMenuOpen&&(u=this._hideAnim,f=this._hideSpeed,this.hideContextMenu(n,u,f))},_calculateContextMenuPosition:function(t){var i,r,u;return this.element.css({top:"",left:""}),i=t.clientX+this.element.width()<n(window).width()?t.pageX:t.pageX-this.element.width(),r=t.clientY+this.element.height()<n(window).height()?t.pageY:t.clientY>this.element.height()?t.pageY-this.element.height():n(window).height()-this.element.outerHeight(),u=n("body").css("position")!="static"?n("body").offset():{left:0,top:0},i-=u.left,r-=u.top,{X:i,Y:r}},_onDefaultPreventer:function(n){return n.preventDefault(),n.stopPropagation(),!1},_onContextClose:function(t){var e=this,i,r,u,f;this._isContextMenuOpen&&(i=!1,(n(t.target).is(this.element)||n(t.target).parents(".e-context").is(this.element))&&(i=!0),i||(r=this._hideAnim,u=this._hideSpeed,this.hideContextMenu(t,r,u),f=n(t.target).parents(),n.each(f,function(n,t){t.id==e._ContextTargetId})))}});t.Menu.Locale=t.Menu.Locale||{};t.Menu.Locale["default"]=t.Menu.Locale["en-US"]={titleText:"Menu"};t.MenuType={NormalMenu:"normalmenu",ContextMenu:"contextmenu"};t.Direction={Left:"left",Right:"right",None:"none"};t.AnimationType={None:"none",Default:"default"}})(jQuery,Syncfusion)});
