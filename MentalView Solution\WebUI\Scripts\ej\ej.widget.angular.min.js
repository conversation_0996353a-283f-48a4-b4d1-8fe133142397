/*!
*  filename: ej.widget.angular.min.js
*  version : 20.3.0.57
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["angular","./ej.core.min"],n):n()})(function(){(function(n,t,i,r,u){"use strict";var s=r.module("ejangular",[]),o,f,h,c,l,e,a;t.module=s;o=u;f={firstCap:function(n){return n.charAt(0).toUpperCase()+n.slice(1)},generatePropMap:function(n,i,r,u){var o,s,e;typeof i!="string"||t.isNullOrUndefined(n)||(n.name=i,i={});o=i||{};u?u+=".":u="";r=r||"";for(e in n)s=r+f.firstCap(e.toLowerCase()),t.isPlainObject(n[e])&&f.generatePropMap(n[e],o,s,u+e,name),o[s]=u+e,t.isNullOrUndefined(n.name)||(o.ejCtlname=n.name);return o},generateAttr:function(n,i,r,e){var o,s;if(t.isPlainObject(n))for(o in n)s=o.toLowerCase(),f.generateAttr(n[o],i,(r||"e")+f.firstCap(s),(e||"e")+"-"+s);else r&&i[r]===u&&typeof n!="object"&&typeof n!="function"&&typeof n!="undefined"&&(i[r]=n.toString(),i.$attr[r]=e)},getModel:function(i,r,e,o,s,h,c){var p={},a,l,it=r.$attr||r,d=!i,y,w,g,ft,nt,v,k,b,rt,ut,tt;i=i||(r===it?{}:it);c=c||"";ft=f.getDirectiveName(i.ejCtlname||"");nt=r[ft];t.isNullOrUndefined(nt)||nt==""||(p=t.getObject(nt,e)||{},f.generateAttr(p,r));for(v in it)if(y=!0,w=g=null,d||v[0]==="e"&&/[A-Z]/.test(v[1])){if(l=r[v]||"",d||(v=v.slice(1)),typeof l=="object"){if(k=d?i[v]||v:v,l instanceof Array)for(b=0;b<l.length;b++)l[b]=f.getModel(null,l[b],e,o,s,h,c+k+"["+b+"]");else{rt={};for(ut in l)(a=l.$attr[ut],a)&&(rt[a]=f.getModel(null,l[ut],e,o,s,h,c+k+"."+a));l=rt;a=f.getObject(k,p);a&&n.extend(!0,a,l);continue}f.createObject(k,l,p);continue}if(a=i[v]||v,y=!isNaN(l)||/^['"].+['"]$/.test(l),y===!1&&(y=l.indexOf("["),y===-1?y=!(l.split(".")[0]in e||f.getObject(l.split(".")[0],e)!==u):(tt=f.buildProperty(e,l),y=!tt.available,y||(w=tt.getter,g=tt.setter))),y===!0){f.createObject(a,f.processData(l),p);continue}o.push({key:l,value:c+a,twoWay:s.indexOf(a)!==-1});w=w||f.getObject;g=g||f.createObject;l=d||s.indexOf(a)===-1||h&&v[0]==="V"&&v==="Value"?w(l,e):f.addTwoWays(l,e,w(l,e));f.createObject(a,l,p)}return p},buildProperty:function(n,i){var r=new Function("prop","instance","with (instance) { \nreturn "+i.replace(/^([^\[.]+)/,"instance['$1']")+"\n}"),u=new Function("prop","val","instance","with (instance) {"+i.replace(/^([^\[.]+)/,"instance['$1']")+" = val;\n}");return"$$ejProperties"in n||(n.$$ejProperties={}),n.$$ejProperties[i]={getter:r,setter:u},{available:!t.isNullOrUndefined(r(i,n)),getter:r,setter:u}},addTwoWays:function(n,t,i){var r=f.createObject;return"$$ejProperties"in t&&n in t.$$ejProperties&&(r=t.$$ejProperties[n].setter),function(e){if(e===u)return i;i=e;r(n,i,t);f.applyScope(t)}},valReplace:/^'(.+)'$|^"(.+)"$/g,processData:function(n){var t=n.replace(f.valReplace,"$1$2");return t==="true"?!0:t==="false"?!1:t!==n?t:(t=+t,t+""===n?t:n)},addWatches:function(n,i,r,u,e){for(var h,c,o,s=0;s<i.length;s++)h="$watch",c=f.getObject,o=i[s].key,"$$ejProperties"in n&&o in n.$$ejProperties&&(c=n.$$ejProperties[o].getter),c(o,n)instanceof Array&&!e&&(h="$watchCollection"),n[h](o,t.proxy(u,n,{control:r,watch:i[s]}),i[s].twoWay&&e)},raise:function(n,t,i){if(t!==i){var r=f.getObject(n.watch.value,n.control.model);(typeof r=="function"&&(r=r()),(r!==t||r instanceof Array)&&(r!==i||!(r instanceof Array)))&&(n.control.observables.indexOf(n.watch.value)===-1||n.control.type==="editor"&&n.watch.value==="value"||(t=f.addTwoWays(n.watch.key,this,t)),n.control.option(n.watch.value,t,typeof t=="undefined"||t instanceof Array))}},angToEjProp:function(n,t){for(var i in t)if(t[i]===n)return i},modelChange:function(n,t){var i,r,u;t&&(i=f.getObject,r=f.createObject,"$$ejProperties"in n&&t in n.$$ejProperties&&(u=n.$$ejProperties[t],i=u.getter,r=u.setter),this.option("_change",function(u){u.value!==i(t,n)&&(r(t,u.value,n),f.applyScope(n))}))},getDirectiveName:function(n){if(n.length<4)return"";var t=n[2]==="m"?4:3;return n.slice(0,t)+n.slice(t).toLowerCase()},applyScope:function(n){setTimeout(function(){n.$apply()},0)},loadTags:function(n,t){var e=n,u,o,i,r;if(e)return e;for(e={},i=0;i<t.length;i++){for(u=t[i].attr||[],o={},r=0;r<u.length;r++)typeof u[r]=="object"?o["e-tag"]=f.loadTags(null,u[r]):o[u[r].toLowerCase().replace(/\./g,"-")]=u[r];e["e-"+t[i].tag.toLowerCase().replace(/\./g,"-")]={field:t[i].tag,attr:o,content:t[i].content,singular:(t[i].singular||t[i].tag.replace(/s$/,"")).replace(/\./g,"-")}}return e},getTagValues:function(t,i){var h=n(i).children(),e={$attr:{}},o,l,a;if(h.length===0)return e;for(o=0;o<h.length;o++){var s=n(h.get(o)),c,u=s.prop("tagName").toLowerCase(),r=t[u];if(!r){if(u=s[0].attributes[0],!u)continue;if(u=u.nodeName,r=t[u],!r)continue}l="e-"+r.singular;c=s.children(l+",["+l+"]");a=c.toArray().map(function(t){var i=f.readAttributes(t,r.attr),u,e;return r.content?(u=t.innerHTML,u.length&&(i[r.content]=t.innerHTML,i.$attr[r.content]=r.content)):r.attr["e-tag"]&&(e=f.getTagValues(r.attr["e-tag"],t),n.extend(!0,i||{},e)),i}).filter(function(n){return n});c.remove();s.remove();a.length&&(f.createAndAddAttr(r.field,a,e),e.$attr[r.field.toLowerCase().replace(/\..+/g,"")]=r.field.replace(/\..+/g,""))}return e},readAttributes:function(n,t){for(var e,r=n.attributes,i,f={},o={},u=0;u<r.length;u++)i=r[u].nodeName.replace(/^e-/i,"").toLowerCase(),i=t[i]||i,e=i.toLowerCase().replace(/\.([a-z]?)/g,function(n,t){return t.toUpperCase()}),f[e]=r[u].value||r[u].nodeValue,o[e]=i;return r.length&&(f.$attr=o),f},getObject:function(n,i){return f.isExpression(n)?o(n)(i):t.getObject(n,i)},createObject:function(n,i,r){return f.isExpression(n)?(o(n).assign(r,i),r):t.createObject(n,i,r)},isExpression:function(n){var t=n.indexOf("("),i=n.indexOf(")");return t>0&&i>t?!0:!1},childRaise:function(n,i,r){var e,u;i!==r&&(e=t.getArrayObject(n.watch.value,n.control.model),e!==i||e instanceof Array)&&(t.setArrayObject(n.watch.value,i,n.control.model),u=f.parseFnAndIndex(n.watch.value),n.control[u[0]]&&n.control[u[0]](u[1],u[2],i,r))},parseFnAndIndex:function(n){for(var r="",e=n.lastIndexOf("]"),i=f.getAllFnIndices(n,[]),u={},t=0;t<i.length;t++)r+="_"+i[t].prop,u[i[t].prop]=i[t].index;return[r.replace(/\./g,"_"),u,n.slice(e+1)]},getAllFnIndices:function(n,t){var i=n.indexOf("["),r;return i===-1?null:(r=n.indexOf("]"),t.push({index:parseInt(n.substring(i+1,r),10),prop:n.substring(0,i)}),f.getAllFnIndices(n.slice(r+1),t),t)},addChildTwoway:function(n,t,i){for(var e={},u=f.createObject,r=0;r<t.length;r++)e[t[r].value]=t[r].key;n._notifyArrayChange=function(n,t){var r=e[n];r&&("$$ejProperties"in i&&r in i.$$ejProperties&&(u=i.$$ejProperties[r].setter),u(r,t,i),f.applyScope(i));u=f.createObject}},refreshTemplate:function(t,i,r){var e=n(t).find(".ej-angular-template"),u,f;for(u in r){if(f=e.filter("."+r[u].key),!f.length){r[u].scope.$destroy();delete r[u];continue}i(f.not(".ng-scope"))(r[u].scope)}},iterateAndGetModel:function(n,t,i,r){var s,o,e,u;if(n instanceof Array)for(e=0;e<n.length;e++)n[e]=f.getModel(null,n[e],i,t,[],!1,(r.endsWith(".")?r.slice(0,-1):r)+"["+e+"]");else for(u in n)u.slice(0,1)!=="$"&&(o=n.$attr&&n.$attr[u]?n.$attr[u]:u,s=f.iterateAndGetModel(n[u],t,i,r+o+"."),delete n[u],n[o]=s);return n},createAndAddAttr:function(n,i,r){for(var o=n.split("."),s=r||window,u=s,f,h=o.length,e=0;e<h;e++)f=o[e].toLowerCase(),e+1==h?(f in u||!u.$attr||(u.$attr[f]=o[e]),u[f]=t.isNullOrUndefined(i)?{$attr:{}}:i):t.isNullOrUndefined(u[f])&&(u.$attr&&(u.$attr[f]=o[e]),u[f]={$attr:{}}),u=u[f];return s},destroyWidget:function(n){var i,r;if(!t.isNullOrUndefined(n.element)){n.element.off(name+"refresh");n.destroy();i=n["tmpl.$newscope"]||{};for(r in i)i[r].scope.$destroy(),delete i[r];n=u}},setscope:function(n,t){n[0].isolateScope=function(){return t}}};t.getArrayObject=function(n,t){return n=n.replace(/\[([0-9]+)]/g,".$1."),f.getObject(n,t)};t.setArrayObject=function(n,t,i){return n=n.replace(/\[([0-9]+)]/g,".$1."),f.createObject(n,t,i)};h={transclude:{transclude:!0,template:function(){return"<div ng-transclude><\/div>"}},defaults:{terminal:!0}};c=function(i,r){l[i]=f.generatePropMap(r.defaults,i);r.observables=r.observables||[];r._notifyArrayChange=function(){};var e=n.extend({},h[r.type]||h.defaults,r.angular);s.directive(f.getDirectiveName(i),["$compile","$parse","$timeout",function(s,h,c){return o=h,n.extend({restrict:"CEA",compile:function(o){var v;if(i!==""&&o.attr("e-directive-name",i),v=!isNaN(o.attr("e-value"))||/^['"].+['"]$/.test(o.attr("e-value")),e.require&&e.require.length&&!v){var h=o.attr("ng-model"),a=o.attr("e-value"),y=o.attr("ng-model-options");if((!h||!a)&&(h||a)&&(a&&o.attr("ng-model",a),h&&o.attr("e-value",h),y||o.attr("ng-model-options","{updateOn: ' '}"),!o.attr("data-compile")))return function(n,t){n.$watch(t.attr("e-directive-name"),function(){t.attr("data-compile",!0);s(t)(n)})};o.addClass("ng-pristine").addClass("ng-valid")}return{pre:function(n,t,i,r){if(f.setscope(t,n),r&&r.length&&r[0]){var o=r[0],e=r[1],u=r[2];e&&e.$addControl(o);u&&!t.attr("ng-model-options")&&(u.$options.updateOn="",u.$options.updateOnDefault=!1)}},post:function(o,h,a,v){var g=[],nt=r.type==="editor",b=f.getModel(l[i],a,o,g,r.observables,nt),k=[],d=a[f.getDirectiveName(i)]||a.id,tt=h.attr("e-deepwatch")==="true",it,w,y,p;if(r._tags&&r._tags.length&&(it=r["ob.tags"]=f.loadTags(r["ob.tags"],r._tags),w=f.getTagValues(it,h),w=f.iterateAndGetModel(w,k,o,""),delete w.$attr,t.copyObject(b,w)),r._rootCSS=="e-button"&&(b._click=function(){f.applyScope(o)}),e.requireFormatters&&v&&v.length&&v[0]&&(b._change=function(n){(f.isExpression(a.eValue)||a.eValue in o)&&n.value!==f.getObject(a.eValue,o)&&f.createObject(a.eValue,n.value,o)}),e.requireParser&&v&&v.length&&v[0]&&(v[0].$$parserName="number",v[0].$parsers.push(function(n){return v[0].$isEmpty(n)?null:/^\s*(\-|\+)?(\d+|(\d*(\.\d*)))\s*$/.test(n)?parseFloat(n):u})),y=n(h)[i](b).data(i),!d||d in o||(o[d]=y,y.scopeId=d),f.addWatches(o,g,y,f.raise,tt),k.length&&(f.addWatches(o,k,y,f.childRaise,tt),f.addChildTwoway(y,k,o)),v&&v.length&&v[0]){p=v[0];p.$setPristine(!0);e.requireFormatters&&p.$formatters.push(function(){return y.element.val()});h.on(i+"_change",function(t){if(t.source!=="source"){var r=n(t.target),i=t.value;e.requireParser&&p.$$parserName=="number"&&(i=t.value!=null?t.value.toString():"");p.$setViewValue(i);p.$modelValue=t.model.value;p.$commitViewValue();f.applyScope(o)}})}nt&&f.modelChange.apply(y,[o,a.eValue]);"tmpl.$newscope"in y&&f.refreshTemplate(h,s,y["tmpl.$newscope"]);y.element.on(i+"refresh",function(){"tmpl.$newscope"in y&&f.refreshTemplate(h,s,y["tmpl.$newscope"])});o.$on("$destroy",function(){t.angularMobileSettings&&t.angularMobileSettings.enableAnimation?c(function(){f.destroyWidget(y)},t.angularMobileSettings.animationTime):f.destroyWidget(y)})}}}},e)}])};s.directive("ejTemplate",["$compile",function(n){return{restrict:"EA",priority:999,terminal:!0,link:function(t,i,r){var u=t.$parent.$new(!0);t.ejObject&&(u.ejId=t.ejId,u.ejObject=t.ejObject,u.items=t.items,u.model=t.model,u.data=t.items[r.ejProp],n(i.children(),t)(u),t.$parent.$$phase||t.$root.$$phase||u.$digest())}}}]);l={};e=i.registeredWidgets;for(a in e)c(e[a].name,e[a].proto);t.widget.extensions={registerWidget:function(n){c(n,e[n].proto)}};Array.prototype.map=Array.prototype.map||function(n,t){for(var i=[],r=0;r<this.length;)i.push(n.call(t,this[r++]));return i};t.template["text/ng-template"]=function(i,r,u,f){var h=angular.element(i.element[0]),e,s,o=i["tmpl.$newscope"];return o&&o[r]||(o=o||{},o[r]={scope:i.element[0].isolateScope().$new(!0),key:t.getGuid("tmpl")},e=o[r].scope,e.model=i.model,e.ejId=i._id,e.ejObject=i,i["tmpl.$newscope"]=o),e=o[r].scope,t.isNullOrUndefined(f)?(f=1,e.items=[u]):(e.items||(e.items=[]),e.items[f]=u),s=r||"",s.startsWith("#")&&(s=n(s).html()),"<div ej-template ej-prop='"+f+"' class='"+o[r].key+" ej-angular-template'>"+s+"<\/div>"};t.template.render=t.template["text/ng-template"]})(window.jQuery,window.Syncfusion,window.Syncfusion.widget,window.angular)});
