﻿using Syncfusion.JavaScript.DataVisualization.Models;
using Syncfusion.XlsIO;
using Syncfusion.XlsIO.Implementation;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;

namespace WebUI
{
    public class WorksheetToHTMLService
    {
        private readonly Dictionary<string, MemoryStream> fileDataValue;
        private string outputFile;
        public WorksheetToHTMLService(Dictionary<string, MemoryStream> fileData)
        {
            fileDataValue = fileData;
        }
        /// <summary>
        /// Convert the Excel document to HTML
        /// </summary>
        /// <returns>Return the created excel document as stream</returns>
        public string WorksheetToHTMLXlsIO(string option, string outputFile)
        {
            //New instance of XlsIO is created.[Equivalent to launching MS Excel with no workbooks open].
            //The instantiation process consists of two steps.
            //Step 1 : Instantiate the spreadsheet creation engine
            using (ExcelEngine excelEngine = new ExcelEngine())
            {
                //Step 2 : Instantiate the excel application object
                IApplication application = excelEngine.Excel;
                application.DefaultVersion = ExcelVersion.Excel2016;
                //An existing workbook is opened                    
                IWorkbook workbook = application.Workbooks.Open(fileDataValue["file"]);
                //The first worksheet object in the worksheets collection is accessed
                IWorksheet worksheet = workbook.Worksheets[0];
                //Save the document as a stream and return the stream
                using (MemoryStream stream = new MemoryStream())
                {
                    if (option == "Workbook")
                    {
                        //Save the created HTML document to MemoryStream
                        //workbook.SaveAsHtml(stream);

                        workbook.SaveAsHtml(outputFile, HtmlSaveOptions.Default);
                        return File.ReadAllText(outputFile);
                    }
                    else
                    {
                        //Save the created HTML document to MemoryStream
                        //worksheet.SaveAsHtml(stream);
                        worksheet.SaveAsHtml(outputFile, HtmlSaveOptions.Default);
                        return File.ReadAllText(outputFile);
                    }
                    return "";
                }

            }
        }
        #region HelperMethod
        public void Close()
        {
            foreach (KeyValuePair<string, MemoryStream> item in fileDataValue)
            {
                item.Value.Dispose();
            }
            fileDataValue.Clear();
        }
        #endregion
    }
}