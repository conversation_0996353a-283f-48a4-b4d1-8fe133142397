/*!
*  filename: ej.timepicker.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.globalize.min","./../common/ej.core.min","./../common/ej.scroller.min"],n):n()})(function(){(function(n,t,i){t.widget("ejTimePicker","ej.TimePicker",{element:null,model:null,validTags:["input"],_addToPersist:["value"],_rootCSS:"e-timepicker",_setFirst:!1,type:"editor",angular:{require:["?ngModel","^?form","^?ngModelOptions"],requireFormatters:!0},_requiresID:!0,defaults:{cssClass:"",timeFormat:"",value:null,enableAnimation:!0,locale:"en-US",htmlAttributes:{},readOnly:!1,showPopupButton:!0,enableStrictMode:!1,interval:30,hourInterval:1,minutesInterval:1,secondsInterval:1,height:"",width:"",minTime:"12:00 AM",maxTime:"11:59 PM",showRoundedCorner:!1,enableRTL:!1,popupHeight:"191px",popupWidth:"auto",enabled:!0,enablePersistence:!1,disableTimeRanges:null,validationRules:null,validationMessages:null,focusIn:null,focusOut:null,beforeChange:null,change:null,select:null,create:null,destroy:null,beforeOpen:null,beforeClose:null,open:null,close:null,watermarkText:"select a time"},dataTypes:{timeFormat:"string",minTime:"string",maxTime:"string",readOnly:"boolean",interval:"number",showPopupButton:"boolean",locale:"string",hourInterval:"number",minutesInterval:"number",secondsInterval:"number",enabled:"boolean",enablePersistence:"boolean",enableAnimation:"boolean",enableStrictMode:"boolean",disableTimeRanges:"data",htmlAttributes:"data",validationRules:"data",validationMessages:"data",watermarkText:"string"},observables:["value"],enable:function(){this.model.enabled||(this.element[0].disabled=!1,this.element.prop("disabled",!1),this.model.enabled=!0,this.wrapper.removeClass("e-disable"),this.element.removeClass("e-disable").attr("aria-disabled",!1),this.model.showPopupButton&&(this.timeIcon.removeClass("e-disable").attr("aria-disabled",!1),this.popupList&&this.popupList.removeClass("e-disable").attr("aria-disabled",!1)),this._isIE8&&this.timeIcon.children().removeClass("e-disable"))},disable:function(){this.model.enabled&&(this.element[0].disabled=!0,this.model.enabled=!1,this.element.attr("disabled","disabled"),this.wrapper.addClass("e-disable"),this.element.addClass("e-disable").attr("aria-disabled",!0),this.model.showPopupButton&&(this.timeIcon.addClass("e-disable").attr("aria-disabled",!0),this.popupList&&this.popupList.addClass("e-disable").attr("aria-disabled",!0)),this._isIE8&&this.timeIcon.children().addClass("e-disable"),this._hideResult())},getValue:function(){return this.element.val()},setCurrentTime:function(){this.model.readOnly||this._setMask()},setValue:function(n){var i=this.model.value,r;this.model.value=t.format(this._createObject(n,!0),this.model.timeFormat,this.model.locale);this._ensureValue();this._enableMask();this.model.enableStrictMode&&!this._isValid(n,!0)&&(r=this._isValid(this.model.value)?this._localizeTime(this.model.value):this.model.value,this.element.val(r));i!=this.model.value&&this._raiseChangeEvent(i,!0);this._checkErrorClass()},show:function(){this.showDropdown||this._getInternalEvents||this._showResult()},hide:function(){this.showDropdown&&this._hideResult()},_ISORegex:function(){this._tokens=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g;this._extISORegex=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/;this._basicISORegex=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/;this._numberRegex={2:/\d\d?/,4:/^\d{4}/,z:/Z|[+-]\d\d(?::?\d\d)?/gi,t:/T/,"-":/\-/,":":/:/};this._zeroRegex=/Z|[+-]\d\d(?::?\d\d)?/;this._dates=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/]];this._times=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]]},_timeFormat:function(n){var r,i;return n||(n=t.preferredCulture(this.model.locale).calendars.standard.patterns.t),r=this._validateTimeFormat(n),r&&(this.model.timeFormat=r,this.model.minTime=t.format(this._createObject(this._minTimeObj),this.model.timeFormat,this.model.locale),this.model.maxTime=t.format(this._createObject(this._maxTimeObj),this.model.timeFormat,this.model.locale),this.model.value?(this._setModelOption=!0,this.model.value=this._localizeTime(this.model.value),this.element.val(this.model.value)):(this._setModelOption=!1,i=this._localizeTime(this.element.val()),i&&this._checkMinMax(i)&&(this.model.value=i,this.element.val(i)))),r},_getTimeFormat:function(){this._prevTimeFormat&&(this.model.timeFormat=t.preferredCulture(this.model.locale).calendar.patterns.t||"h:mm tt");this.seperator=this._getSeperator()},_changeSkin:function(n){this.wrapper.removeClass(this.model.cssClass).addClass(n);this.popupList&&this.popupList.removeClass(this.model.cssClass).addClass(n)},_localize:function(n){var i=this._createObject(this.model.value,!0);this.model.locale=n;this._getTimeFormat();this.model.minTime=t.format(this._createObject(this._minTimeObj),this.model.timeFormat,this.model.locale);this.model.maxTime=t.format(this._createObject(this._maxTimeObj),this.model.timeFormat,this.model.locale);t.isNullOrUndefined(this._options)||t.isNullOrUndefined(this._options.watermarkText)||(this._localizedLabels.watermarkText=this._options.watermarkText);i?(this.model.value=this._localizeTime(i),this.element.val(this.model.value)):(i=this._localizeTime(this.element.val()),i&&this._checkMinMax(i)&&(this.model.value=i,this.element.val(i)));this._getAmPm()},_setWaterMark:function(){if(this.element!=null&&this.element.hasClass("e-input"))return this._localizedLabels.watermarkText&&this.element.val()==""&&(this.isValidState=!0,this._checkErrorClass()),this._isSupport||this.element.val()!=""?n(this.element).attr("placeholder",this._localizedLabels.watermarkText):this._hiddenInput.css("display","block").val(this._localizedLabels.watermarkText),!0},_localizedLabelToModel:function(){this.model.watermarkText=this._localizedLabels.watermarkText;this.model.buttonText=this._localizedLabels.buttonText},_setLocalize:function(n){var n=t.preferredCulture(n);n&&(this.model.locale=n.name=="en"?"en-US":n.name,t.isNullOrUndefined(this._options)||!t.isNullOrUndefined(this._options.timeFormat)&&this._options.timeFormat||(this.model.timeFormat=t.preferredCulture(this.model.locale).calendars.standard.patterns.t),this._prevTimeFormat=t.isNullOrUndefined(this._options.timeFormat)||this._options.timeFormat==""?!0:!1)},_updateInput:function(){if(!t.isNullOrUndefined(this._options)){var n=this._localizeTime(this._options.value);t.isNullOrUndefined(n)||typeof n=="string"&&this.model.enableStrictMode&&!this.model.value&&(this.element.val(this._options.value),this.isValidState=this.element.val()==""?!0:!1,this._checkErrorClass())}},_createMinMaxObj:function(){this._minTimeObj=this._createObject(this.model.minTime);this._minTimeObj||(this.model.minTime=t.format(this._createObject((new Date).setHours(0,0,0,0)),this.model.timeFormat,this.model.locale));this._maxTimeObj=this._createObject(this.model.maxTime);this._maxTimeObj||(this.model.maxTime=t.format(this._createObject((new Date).setHours(23,59,59,59)),this.model.timeFormat,this.model.locale))},_setMinMax:function(){var n=(new Date).setHours(0,0,0,0),i=(new Date).setHours(23,59,59,59);!t.isNullOrUndefined(this._options)&&t.isNullOrUndefined(this._options.minTime)&&(this.model.minTime=t.format(this._createObject(n),this.model.timeFormat,this.model.locale));!t.isNullOrUndefined(this._options)&&t.isNullOrUndefined(this._options.maxTime)&&(this.model.maxTime=t.format(this._createObject(i),this.model.timeFormat,this.model.locale));this._createMinMaxObj()},_init:function(n){if(this._options=n,this._cloneElement=this.element.clone(),this._ISORegex(),this._isSupport=document.createElement("input").placeholder==i?!1:!0,!this.element.is("input")||this.element.attr("type")&&this.element.attr("type")!="text")return!1;this._initialize();this._render();this._wireEvents();n&&n.value!=i&&n.value!=this.element.val()&&this._trigger("_change",{value:this.element.val()});this._updateInput();this._updateTextbox();this.model.validationRules!=null&&(this._initTimeValidator(),this._setTimeValidation())},_updateTextbox:function(){var t,n;if(this._options.disableTimeRanges){for(t=!0,n=0;n<this._options.disableTimeRanges.length;n++)if((this.model.minTime>=this._options.disableTimeRanges[n].startTime||this.model.minTime<=this._options.disableTimeRanges[n].endTime)&&this._options.disableTimeRanges[n].startTime==this.model.minTime){t=!1;break}(this._options===i||this._options.value===i&&!this.model.value&&t)&&this._setTime(this._localizeTime(this.model.minTime))}else this._options!==i&&(this._options.value!==i||this.model.value)||this._setTime(this._localizeTime(this.model.minTime))},_setMinMaxTime:function(i,r){!t.isNullOrUndefined(r.minTime)&&n.trim(r.minTime)&&this._isValid(r.minTime)&&(this.model.minTime=r.minTime,this._minTimeObj=this._createObject(this.model.minTime),this._validateTimes());!t.isNullOrUndefined(r.maxTime)&&n.trim(r.maxTime)&&this._isValid(r.maxTime)&&(this.model.maxTime=r.maxTime,this._maxTimeObj=this._createObject(this.model.maxTime),this._validateTimes());this._validateMinMax();this._createMinMaxObj();t.isNullOrUndefined(r.minTime)||(r.minTime=this.model.minTime);t.isNullOrUndefined(r.maxTime)||(r.maxTime=this.model.maxTime);this._checkMinMax(this.model.value)||(this.model.enableStrictMode?(this.isValidState=!1,this.model.value=null):(this.model.minTime&&!this._compareTime(this.model.value,this.model.minTime,!0)&&(this.model.value=this.model.minTime),this.model.maxTime&&!this._compareTime(this.model.maxTime,this.model.value,!0)&&(this.model.value=this.model.maxTime)));i!==this.model.value&&this._isValid(this.model.value,!0)&&this.element.val(this.model.value)},_setModel:function(n){var r=!1,o=this.model.value,i,e,u,f,s;for(i in n){i!="height"&&i!="width"&&i!="htmlAttributes"&&i!="watermarkText"&&i!="enabled"&&i!="validationRules"&&i!="validationMessages"&&t.isNullOrUndefined(this.popupList)&&this._renderDropdown();switch(i){case"timeFormat":u=this._createObject(this.model.value);this._preTimeformat=this.model.timeFormat;e=this._timeFormat(n[i]);n[i]=this.model.timeFormat;e&&(this.seperator=this._getSeperator());f=this._createObject(this.model.value);r=+u==+f?!1:!0;break;case"locale":u=this._createObject(this.model.value);this._localize(n[i]);this.model.minTime=t.format(this._createObject(this._minTimeObj),this.model.timeFormat,this.model.locale);this.model.maxTime=t.format(this._createObject(this._maxTimeObj),this.model.timeFormat,this.model.locale);f=this._createObject(this.model.value);r=+u==+f?!1:!0;break;case"interval":this.model.interval=n[i];break;case"cssClass":this._changeSkin(n[i]);break;case"showRoundedCorner":this._setRoundedCorner(n[i]);break;case"enableRTL":this._setRtl(n[i]);break;case"height":this._setHeight(n[i]);break;case"width":this.wrapper.width(n[i]);this._setListWidth();break;case"value":t.isPlainObject(n[i])&&(n[i]=null);this.model.value=t.format(this._createObject(n[i],!0),this.model.timeFormat,this.model.locale);this._ensureValue();this._enableMask();this.model.enableStrictMode&&!this._isValid(n[i],!0)&&(s=this._isValid(n[i])?this._localizeTime(n[i]):n[i],this.element.val(s));n[i]=this.model.value;r=!0;break;case"enableStrictMode":this.model.enableStrictMode=n[i];break;case"validationRules":this.model.validationRules!=null&&(this.element.rules("remove"),this.model.validationMessages=null);this.model.validationRules=n[i];this.model.validationRules!=null&&(this._initTimeValidator(),this._setTimeValidation());break;case"validationMessages":this.model.validationMessages=n[i];this.model.validationRules!=null&&this.model.validationMessages!=null&&(this._initTimeValidator(),this._setTimeValidation());break;case"popupHeight":this.model.popupHeight=n[i];this._setListHeight();break;case"popupWidth":this.model.popupWidth=n[i];this._setListWidth();break;case"enabled":n[i]?this.enable():this.disable();break;case"htmlAttributes":this._addAttr(n[i]);break;case"disableTimeRanges":this.model.disableTimeRanges=n[i];this._initStartEnd();this.model.value=t.format(this._createObject(this.element.val(),!0),this.model.timeFormat,this.model.locale);this._ensureValue();this._enableMask();this.model.enableStrictMode&&!this._isValid(this.element.val(),!0)&&this.element.val(this.element.val());r=!0;break;case"watermarkText":t.isNullOrUndefined(this._options)&&(this._options={});this._options[i]=this.model.watermarkText=n[i];this._localizedLabels.watermarkText=this.model.watermarkText;this._setWaterMark()}}t.isNullOrUndefined(n.minTime)&&t.isNullOrUndefined(n.maxTime)||(this._setMinMaxTime(o,n),r=!0);t.isNullOrUndefined(n.showPopupButton)?!this.model.showPopupButton||!e&&t.isNullOrUndefined(n.minTime)&&t.isNullOrUndefined(n.maxTime)&&t.isNullOrUndefined(n.locale)&&t.isNullOrUndefined(n.interval)&&t.isNullOrUndefined(n.disableTimeRanges)||this._reRenderDropdown():this._showButton(n[i]);r&&(this._raiseChangeEvent(o,!0),n.value=this.model.value);this._checkErrorClass()},_destroy:function(){this.element.insertAfter(this.wrapper);this.wrapper.remove();this.element.removeClass("e-input").removeAttr("ondragstart draggable aria-atomic aria-live aria-readonly").val(this.element.attr("value"));this._cloneElement.attr("name")||this.element.removeAttr("name");this.popupList&&this.popupList.remove()},_initialize:function(){this.target=this.element[0];this.timeIcon=null;this._disabledItems=[];this.popupList=null;this.focused=!1;this.start=0;this.end=0;this.min=null;this.max=null;this.incomplete=!1;this.downPosition=0;this._setLocalize(this.model.locale);this.model.enableStrictMode||this._setMinMax();this._getAmPm();this.showDropdown=!1;this._activeItem=0;this.isValidState=!0;this._manualFocus=!1;this._isIE7=this._checkIE7();this._initStartEnd();this._localizedLabels=this._getLocalizedLabels();t.isNullOrUndefined(this._options)||t.isNullOrUndefined(this._options.watermarkText)||(this._localizedLabels.watermarkText=this._options.watermarkText);t.isNullOrUndefined(this.model.value)&&this.element[0].value!=""&&(this.model.value=this.element[0].value);this._isIE8=t.browserInfo().name=="msie"&&t.browserInfo().version=="8.0"?!0:!1;this._getInternalEvents=!1;this._dateTimeInternal=!1;this.model.timeFormat?this.seperator=this._getSeperator():this._getTimeFormat()},_render:function(){this._renderWrapper();this._setDimentions();this._renderTimeIcon();this._validateTimes();this.model.enableStrictMode||this._createMinMaxObj();this._addAttr(this.model.htmlAttributes);this._checkProperties();this._enableMask();this._checkErrorClass();this.element.attr({"aria-atomic":"true","aria-live":"assertive","aria-readonly":this.model.readOnly,value:this.model.value});t.isNullOrUndefined(this.model.value)?this.wrapper.addClass("e-valid"):this.wrapper.removeClass("e-valid")},_renderWrapper:function(){if(this.element.addClass("e-input").attr({tabindex:"0",role:"combobox","aria-expanded":"false"}),this.wrapper=t.buildTag("span.e-timewidget e-widget "+this.model.cssClass+"#"+this.target.id+"_timewidget").insertAfter(this.element),this.wrapper.attr("style",this.element.attr("style")),this.element.removeAttr("style"),t.isTouchDevice()||this.wrapper.addClass("e-ntouch"),this.container=t.buildTag("span.e-in-wrap e-box").append(this.element),this.wrapper.append(this.container),!this._isSupport){this._hiddenInput=t.buildTag("input.e-input e-placeholder ","",{},{type:"text"}).insertAfter(this.element);this._hiddenInput.val(this._localizedLabels.watermarkText);this._hiddenInput.css("display","block");var i=this;n(this._hiddenInput).focus(function(){i.element.focus()})}},_addAttr:function(i){var r=this;n.map(i,function(n,i){var u=i.toLowerCase();u=="class"?r.wrapper.addClass(n):u=="disabled"&&n=="disabled"?r.disable():u=="readOnly"&&n=="readOnly"?r.model.readOnly=!0:u=="style"?r.wrapper.attr(i,n):u=="id"?(r.wrapper.attr(i,n+"_wrapper"),r.element.attr(i,n)):t.isValidAttr(r.element[0],i)?r.element.attr(i,n):r.wrapper.attr(i,n)})},_initTimeValidator:function(){this.element.closest("form").data("validator")||this.element.closest("form").validate()},_setTimeValidation:function(){var r,f,i,u,e;this.element.rules("add",this.model.validationRules);r=this.element.closest("form").data("validator");r=r?r:this.element.closest("form").validate();f=this.element.attr("name");r.settings.messages[f]={};for(i in this.model.validationRules)if(u=null,!t.isNullOrUndefined(this.model.validationRules[i])){if(t.isNullOrUndefined(this.model.validationRules.messages&&this.model.validationRules.messages[i])){r.settings.messages[f][i]=n.validator.messages[i];for(e in this.model.validationMessages)i==e?u=this.model.validationMessages[i]:""}else u=this.model.validationRules.messages[i];r.settings.messages[f][i]=u!=null?u:n.validator.messages[i]}},_renderTimeIcon:function(){if(this.model.showPopupButton){this.timeIcon=t.buildTag("span.e-select").attr({role:"button","aria-label":"select"});var n=t.buildTag("span.e-icon e-clock").attr("role","presentation");this._isIE8&&(this.timeIcon.attr("unselectable","on"),n.attr("unselectable","on"));this.timeIcon.append(n);this.container.append(this.timeIcon).addClass("e-padding");this._on(this.timeIcon,"mousedown",this._timeIconClick)}},_elementClick:function(){this.showDropdown||this.model.enableStrictMode||this._showResult()},_renderDropdown:function(){var i=n("#"+this.element[0].id+"_popup").get(0),r;if(i&&n(i).remove(),this.popupList)return!1;this.popupList=t.buildTag("div.e-time-popup e-popup e-widget e-box "+this.model.cssClass+"#"+this.target.id+"_popup","",{},{tabindex:0,role:"listbox"});t.isTouchDevice()||this.popupList.addClass("e-ntouch");this.popup=this.popupList;this.ul=t.buildTag("ul.e-ul");this._isIE8&&this.ul.attr("unselectable","on");r=t.buildTag("div").append(this.ul);n("body").append(this.popupList.append(r));this._renderLiTags();this._setListHeight();this._setListWidth();this.popupList.ejScroller({height:this.popupList.height(),width:0,scrollerSize:20});this.scrollerObj=this.popupList.ejScroller("instance");this.popupList.css("display","none");this._listSize=this.ul.find("li").length},_renderLiTags:function(){var r,o,f,s,h,e,i,u;for(this._disabledItems=[],s=this.model.interval*6e4,h=!t.isNullOrUndefined(this.model.disableTimeRanges)&&this.model.disableTimeRanges.length>0?!0:!1,r=this._minTimeObj,o=this._maxTimeObj,e=0;this._compareTime(o,r,!0);)f=this._localizeTime(r),i=n(document.createElement("li")),i[0].appendChild(document.createTextNode(f)),this._isIE8&&i.attr("unselectable","on"),h&&(this._ensureTimeRange(f)?(i.addClass("e-disable"),this._disabledItems.push(e)):i.removeClass("e-disable")),this.ul[0].appendChild(i[0]),r=new Date(r).getTime()+s,e++;u=this.ul.find("li");t.isTouchDevice()||(this._on(u,"mouseenter",n.proxy(this._OnMouseEnter,this)),this._on(u,"mouseleave",n.proxy(this._OnMouseLeave,this)));this._on(u,"click",n.proxy(this._OnMouseClick,this));(this.model.showPopupButton||!t.isNullOrUndefined(this.popupList))&&this.ul.find("li").attr({tabindex:-1,"aria-selected":!1,role:"option"})},_ensureTimeRange:function(n){var r,i;if(!t.isNullOrUndefined(this.model.disableTimeRanges))for(r=this._makeDateTimeObj(n),i=0;i<this.model.disableTimeRanges.length;i++)if(+r>=+this._makeDateTimeObj(this.model.disableTimeRanges[i].startTime)&&+r<=+this._makeDateTimeObj(this.model.disableTimeRanges[i].endTime))return!0;return!1},_initStartEnd:function(){if(this._startTime=[],this._endTime=[],!t.isNullOrUndefined(this.model.disableTimeRanges))for(var n=0;n<this.model.disableTimeRanges.length;n++)this._startTime[n]=this._makeDateTimeObj(this.model.disableTimeRanges[n].startTime),this._endTime[n]=this._makeDateTimeObj(this.model.disableTimeRanges[n].endTime)},_makeDateTimeObj:function(n){var u;if(typeof n=="string"){var r=t.preferredCulture(this.model.locale).calendar.patterns.d,f=t.format(new Date("1/1/2000"),r,this.model.locale),i=t.parseDate(f+" "+n,r+" "+this.model.timeFormat,this.model.locale);if(!i)if(u=new Date(n),isNaN(Date.parse(u))||t.isNullOrUndefined(n))i=new Date("1/1/2000 "+n);else return this._setEmptyDate(n);return i}return n instanceof Date?this._setEmptyDate(n):null},_reRenderDropdown:function(){this.ul.empty();this._renderLiTags();this._refreshScroller();this._changeActiveEle()},_refreshScroller:function(){var t=this.popupList.css("display")=="none"?!0:!1;this.popupList.css("height","auto");this.popupList.find(".e-content, .e-vscroll").removeAttr("style");this.popupList.find(".e-vscroll div").removeAttr("style");t&&this.popupList.css("display","block");this.scrollerObj.model.height=this.popupList.height();this.scrollerObj.model.scrollTop=0;this.scrollerObj.refresh();this._isIE8&&(n("#"+this.scrollerObj._id).children(".e-vscroll").children().attr("unselectable","on"),n("#"+this.scrollerObj._id).find(".e-vhandle").attr("unselectable","on"));t&&this.popupList.css("display","none")},_removeWatermark:function(){this.element.val()==""||this._isSupport||this._hiddenInput.css("display","none")},_setListWidth:function(){if(this.popupList){var n=this.model.popupWidth,t;t=typeof n=="string"&&n.indexOf("%")!=-1||typeof n=="string"?parseInt(n)>0?n:"auto"&&(this.model.popupWidth="auto"):n>0?n:"auto"&&(this.model.popupWidth="auto");t&&t!="auto"?this.popupList.css({width:t}):this.popupList.css({width:this.wrapper.width()})}this.scrollerObj&&(this._refreshScroller(),this._updateScrollTop())},_setListHeight:function(){this.popupList&&this.popupList.css({"max-height":this.model.popupHeight||"191px"});this.scrollerObj&&(this._refreshScroller(),this._updateScrollTop())},_updateScrollTop:function(){this.scrollerObj.setModel({scrollTop:this._calcScrollTop()})},_refreshPopup:function(){this.model.popupWidth=="auto"&&this.popupList.css({width:this.wrapper.width()});this._setListPosition();this._refreshScroller()},_setListPosition:function(){var t=this.wrapper,i=this._getOffset(t),f,h=n(document).scrollTop()+n(window).height()-(i.top+n(t).outerHeight()),c=i.top-n(document).scrollTop(),u=this.popupList.outerHeight(),e=this.popupList.outerWidth(),r=i.left,o=t.outerHeight(),l=(o-t.height())/2,a=this._getZindexPartial(),s=3,v=(u<h||u>c?i.top+o+s:i.top-u-s)-l;f=n(document).scrollLeft()+n(window).width()-r;(this.model.enableRTL||e>f&&e<r+t.outerWidth())&&(r-=this.popupList.outerWidth()-t.outerWidth());this.popupList.css({left:r+"px",top:v+"px","z-index":a})},_getOffset:function(n){return t.util.getOffset(n)},_getZindexPartial:function(){return t.util.getZindexPartial(this.element,this.popupList)},_enableMask:function(){this.isValidState=this.model.minTime&&this._compareTime(this.model.minTime,this.model.value)||this.model.maxTime&&this._compareTime(this.model.value,this.model.maxTime)?!1:!0;this._setTime(this.model.value);t.isNullOrUndefined(this.model.value)?this.wrapper.removeClass("e-valid"):this.wrapper.addClass("e-valid");this._getInternalEvents&&!this.isValidState&&this._trigger("outOfRange");this._changeActiveEle();this._preVal=this.element.val()},_setTime:function(n){var t=this._localizeTime(n);this.element.val(t);this.model.value=this.model.enableStrictMode?this._compareTime(this.model.value,this.model.minTime,!0)&&this._compareTime(this.model.maxTime,this.model.value,!0)?t:null:t;this._setWaterMark()},_timeFromISO:function(n){var i=this._extISORegex.exec(n)||this._basicISORegex.exec(n),c="",l="",a="",v,e,o,f,r,t,s,u,h,y;if(i){for(e=0;e<this._dates.length;e++)if(this._dates[e][1].exec(i[1])){c=this._dates[e][0];break}if(i[3])for(o=0;o<this._times.length;o++)if(this._times[o][1].exec(i[3])){l=(i[2]||" ")+this._times[o][0];break}for(i[4]&&this._zeroRegex.exec(i[4])&&(a="Z"),v=c+l+a,f=v.match(this._tokens),t=[],u=0;u<f.length;u++)h=f[u],s=this._checkLiteral(f[u]),y=this._numberRegex[s?f[u].toLowerCase():h.length]||new RegExp("^\\d{1,"+h.length+"}"),r=n.match(y),r&&(n.substr(0,n.indexOf(r))>=0&&!s&&(f[u].indexOf("M")>=0?t.push(parseInt(r[0])-1):t.push(parseInt(r[0]))),n=n.slice(n.indexOf(r[0])+r[0].length));return i[4]=="Z"?new Date(Date.UTC.apply(null,t)):new Date(t[0],t[1],t[2],t[3],t[4],t[5])}return new Date(n+"")},_checkLiteral:function(n){var t=n.toLowerCase();return t=="t"||t=="z"||t==":"||t=="-"?!0:!1},_setMask:function(){this.model.value=new Date;this._enableMask()},_validateTimes:function(){var n=this._validateTimeFormat(this.model.timeFormat);this.model.timeFormat=n?n:"h:mm tt";this._isValid(this.model.minTime)||(this.model.minTime="12:00 AM");this._isValid(this.model.maxTime)||(this.model.maxTime="11:59 PM");this._isValid(this.model.value,!0)||(this.model.value=null);this._checkMinMax(this.model.value)||this.model.enableStrictMode||(this.model.minTime&&!this._compareTime(this.model.value,this.model.minTime,!0)&&(this.model.value=this.model.minTime),this.model.maxTime&&!this._compareTime(this.model.maxTime,this.model.value,!0)&&(this.model.value=this.model.maxTime));this._validateMinMax()},_ensureValue:function(){!this._checkMinMax(this.model.value)&&this._isValid(this.model.value,!0)&&(this.model.enableStrictMode?this.isValidState=!1:(this.model.minTime&&!this._compareTime(this.model.value,this.model.minTime,!0)&&(this.model.value=this.model.minTime),this.model.maxTime&&!this._compareTime(this.model.maxTime,this.model.value,!0)&&(this.model.value=this.model.maxTime)))},_validateMinMax:function(){this.model.minTime&&this.model.maxTime&&this._compareTime(this.model.minTime,this.model.maxTime)&&(this.model.minTime=this.model.maxTime)},_checkProperties:function(){this.model.enabled?this.model.enabled&&this.element.hasClass("e-disable")&&(this.model.enabled=!1,this.enable()):(this.model.enabled=!0,this.disable());this._addProperty();this._checkAttributes()},_addProperty:function(){this._setRtl(this.model.enableRTL);this._setRoundedCorner(this.model.showRoundedCorner)},_setRtl:function(n){n?(this.wrapper.addClass("e-rtl"),this.popupList&&this.popupList.addClass("e-rtl")):(this.wrapper.removeClass("e-rtl"),this.popupList&&this.popupList.removeClass("e-rtl"))},_setRoundedCorner:function(n){n?(this.container.addClass("e-corner"),this.popupList&&this.popupList.addClass("e-corner")):(this.container.removeClass("e-corner"),this.popupList&&this.popupList.removeClass("e-corner"))},_showButton:function(t){this.model.showPopupButton=t;t?(this.container.addClass("e-padding"),this._renderTimeIcon(),this._renderDropdown(),this._addProperty()):(this.container.removeClass("e-padding"),this.timeIcon.remove(),this.popupList.remove(),this.timeIcon=this.popupList=null,n(document).off("mousedown",n.proxy(this._OnDocumentClick,this)))},_checkAttributes:function(){this.element.attr("name")||this.element.attr({name:this.element[0].id});"ondragstart"in document.createElement("input")&&this.element.attr({ondragstart:"return false"});"draggable"in document.createElement("input")&&this.element.attr({draggable:"false"})},_getAmPm:function(){var t=new Date;t.setHours(0);this.ttAM=n.trim(this._localizeMeridian(t));t.setHours(23);this.ttPM=n.trim(this._localizeMeridian(t))},_setDimentions:function(){this.model.height||(this.model.height=this.element.attr("height"));this.model.width||(this.model.width=this.element.attr("width"));this._setHeight(this.model.height);this.model.width&&this.wrapper.width(this.model.width)},_setHeight:function(n){n&&this.wrapper.height(n);this._isIE7&&this.element.height(this.container.height())},_validateTimeFormat:function(t){var i=t.split(" "),r="";return i.length==1||i.length==2?(n(i).each(function(t,i){r+=n.trim(i)+" "}),n.trim(r)):null},_getSeperator:function(){for(var i=this._getElePlace(),t=this._formatparts[i.time],r=new RegExp("^[a-zA-Z0-9]+$"),n=0;n<t.length;n++)if(!r.test(t.charAt(n)))return t.charAt(n)},_checkInComplete:function(){var i=this._getCaretSelection(),r=this._getStartEnd(i),u="00",t=this._getSelectedValue(r),n=this._getCategory(r);i.end-i.start==this.element.val().length&&this._checkAll();n&&n!="tt"&&(this._findCategoryPosition(n),t=="__"?((n=="h"||n=="hh")&&(u="12"),this._changeToDefault(u)):n.length!=1&&t.length==1&&(t=this._changeWhole(t),this.element.val(this._replaceAt(this.target.value,this.start,this.end,t))))},_checkAll:function(){for(var r=this._getElePlace(),i=this._formatparts[r.time].split(this.seperator),t,n=0;n<i.length;n++)this._findCategoryPosition(i[n]),t=this._getSelectedValue({start:this.start,end:this.end}),i[n].length!=1&&t.length==1&&(t=this._changeWhole(t),this.element.val(this._replaceAt(this.element.val(),this.start,this.end,t)))},_changeToDefault:function(n){var i,t;this.incomplete=!0;i=this.element[0].value;this.element[0].value=this._replaceAt(this.target.value,this.start,this.end,n);t=this._checkExceedRange(this.target.value);!t||this._setTime(this.model[t]);this._setSelection(this.start,this.end);this._raiseChangeEvent(i)},_setSelection:function(n,t){var i=this.element[0];i.setSelectionRange?i.setSelectionRange(n,t):i.createTextRange&&(i=i.createTextRange(),i.collapse(!0),i.moveEnd("character",t),i.moveStart("character",n),i.select())},_getSelectedValue:function(n){return this.target.value.substring(n.start,n.end)},_getMinMax:function(n,t){n=="hh"||n=="h"?(this.min=1,this.max=11,t&&(this.max=12)):n=="HH"||n=="H"?(this.min=0,this.max=23):(n=="mm"||n=="m"||n=="ss"||n=="s")&&(this.min=0,this.max=59)},_focusElement:function(){this._manualFocus=!0;this.element.focus()},_targetFocus:function(t){this._clearRange();t.preventDefault();this.focused=!0;this.element.on("mousewheel DOMMouseScroll",n.proxy(this._mouseWheel,this));this.wrapper.addClass("e-focus").removeClass("e-error").attr("aria-invalid","false");this._getExactPostions();this._manualFocus||(this._findCategoryPosition(this._getLeast(!1)),this._setSelection(this.start,this.end));this._manualFocus=!1;this._prevTimeVal=this.element.val();this._raiseEvent("focusIn");this.wrapper.addClass("e-valid");this.model.showPopupButton||this.model.enableStrictMode||this._showResult();this.model.showPopupButton||this._on(this.element,"click",this._elementClick)},_targetBlur:function(){this.focused=!1;this.element.off("mousewheel DOMMouseScroll",n.proxy(this._mouseWheel,this));this.model.showPopupButton||this._off(this.element,"click",this._elementClick);this.wrapper.removeClass("e-focus");this.model.enableStrictMode||this.target.value.indexOf("_")>-1&&this.element.val("");!this._checkMinMax(this.target.value)&&this._isValid(this.target.value,!0)?this.model.enableStrictMode?this.isValidState=!1:(this.model.minTime&&!this._compareTime(this._createObject(this.target.value),this.model.minTime,!0)&&this.element.val(this.model.minTime),this.model.maxTime&&!this._compareTime(this.model.maxTime,this._createObject(this.target.value),!0)&&this.element.val(this.model.maxTime),this._isValid(this.model.value,!0)||this.element.val(null),this.isValidState=!0,t.isNullOrUndefined(this.model.value)?this.wrapper.removeClass("e-valid"):this.wrapper.addClass("e-valid")):this.isValidState=!0;this._ensureValue();this._raiseChangeEvent();this._checkErrorClass();this._raiseEvent("focusOut");this.model.enableStrictMode||this._checkInComplete();t.isNullOrUndefined(this.model.value)?this.wrapper.removeClass("e-valid"):this.wrapper.addClass("e-valid")},_clearRange:function(){var n=this.element[0];isNaN(n.selectionStart)||(n.selectionStart=0,n.selectionEnd=0)},_checkErrorClass:function(){this.isValidState?this.wrapper.removeClass("e-error").attr("aria-invalid","false"):this.wrapper.addClass("e-error").attr("aria-invalid","true")},_getExactPostions:function(){var t=this.model.timeFormat,r=this.model.timeFormat.split(" "),n;this._formatparts=[];this._valueparts=[];this._amPMPosition=t.match("([t][t]+)")?t.match("([t][t]+)").index:null;this._amPMPosition==i?(this._formatparts=this.model.timeFormat.split(" "),this._valueparts=this.element.val().split(" "),this._noTT=!0):(this._amPMPosition==this.model.timeFormat.length-2?(this._ttAtEnd=!0,this._isSpace=this.model.timeFormat[this.model.timeFormat.split("").indexOf("t")-1]==" "):(this._ttAtEnd=!1,this._isSpace=this.model.timeFormat[this.model.timeFormat.split("").indexOf("t")+2]==" "),this._ttAtEnd==!0?(this._ttStartPostion=this.model.timeFormat.match("([t]?[t]+)").index,this._ttEndPosition=this.model.timeFormat.length,this._formatStartPosition=0,n=0,this.element&&this.element.val()&&(n=this.element.val().length-this.model.timeFormat.length),this._updatedttStartPosition=this._ttStartPostion+n,this._formatEndPostion=this.model.timeFormat.match("([t][t]+)").index,this._formatparts[0]=this.model.timeFormat.substr(this._formatStartPosition,this._formatEndPostion).trim(),this._formatparts[1]=this.model.timeFormat.substr(this._ttStartPostion,this._ttEndPosition).trim(),this._valueparts[0]=this.element.val().substr(this._formatStartPosition,this._formatEndPostion+n).trim(),this._valueparts[1]=this.element.val().substr(this._ttStartPostion+n,this._ttEndPosition).trim()):(this._ttStartPostion=0,this._ttEndPosition=this.model.timeFormat.match("([t]?[t]+)")?this._ttStartPostion+this.model.timeFormat.match("([t]?[t]+)")[0].length:0,this._formatStartPosition=this._isSpace?this._ttEndPosition+1:this._ttEndPosition,this._formatEndPostion=this.model.timeFormat.length,this._formatparts[1]=this.model.timeFormat.substr(this._formatStartPosition,this._formatEndPostion).trim(),this._formatparts[0]=this.model.timeFormat.substr(this._ttStartPostion,this._ttEndPosition).trim(),this._valueparts[1]=this.element.val().substr(this._formatStartPosition,this._formatEndPostion).trim(),this._valueparts[0]=this.element.val().substr(this._ttStartPostion,this._ttEndPosition).trim()))},_getCaretSelection:function(){var n=this.element[0],u=0,f=0,e,t,i,r,o;return isNaN(n.selectionStart)?(e=document.selection.createRange().getBookmark(),t=n.createTextRange(),t.moveToBookmark(e),i=n.createTextRange(),i.collapse(!0),i.setEndPoint("EndToStart",t),r=i.text.length,o=t.text.length,{start:r,end:r+o}):(u=n.selectionStart,f=n.selectionEnd,{start:Math.abs(u),end:Math.abs(f)})},_mouseDownOnInput:function(){this.focused||t.isTouchDevice()||this._focusElement();this._getExactPostions();this.downPosition=this._getCaretSelection();n(document).on("mouseup",n.proxy(this._mouseUpOnInput,this))},_mouseUpOnInput:function(t){t.preventDefault();n(document).off("mouseup",n.proxy(this._mouseUpOnInput,this));var i=this._getCaretSelection();this.incomplete&&(this.incomplete=!1,i=this.downPosition);this.target.value!=this._getSelectedText()&&(i=this._getStartEnd(i),this._setSelection(i.start,i.end))},_getCategoryPosition:function(n){var t=0,i=0,u=this._getTimeParts(),r=this._getElePlace(),h=this.seperator,e=!1,o=this._getFormatParts()[r.time].split(h),c=u[r.time].split(h),s,f;if(o.length>c.length)return{start:t,end:i,isValid:e};if(n=="tt")(u[r.tt]==this.ttAM||u[r.tt]==this.ttPM)&&(t=r.tt==0?0:u[r.time].length+1,i=t+u[r.tt].length,!this._isSpace&&this._ttAtEnd&&(t=t-1,i=i-1),e=!0);else if(t=r.time==0?0:u[r.tt].length+1,s=o.indexOf(n),s!=-1){for(f=0;f<o.length;f++)if(i=c[f].length+1,f==s)break;else t+=i;i+=t-1;this._noTT||this._isSpace||this._ttAtEnd||(t=t-1,i=i-1);e=!0}return{start:t,end:i,isValid:e}},_getCategory:function(n){var t=this._getFormatParts(),r=this.seperator,i=this._getElePlace();return n.isTT?t[i.tt]:t[i.time].split(r)[n.index]},_getTimeParts:function(){var n;return this.model.timeFormat=="h:mm tt"||this.model.timeFormat=="h:m tt"||this.model.timeFormat=="hh:m tt"||this.model.timeFormat=="hh:mm:s tt"?this.element.val().split(" "):this._valueparts},_getFormatParts:function(){var n;return this.model.timeFormat=="h:mm tt"||this.model.timeFormat=="h:m tt"||this.model.timeFormat=="hh:m tt"||this.model.timeFormat=="hh:mm:s tt"?this.model.timeFormat.split(" "):this._formatparts},_getStartEnd:function(n){this._getExactPostions();for(var e,h=this.seperator,a=this.element.val(),r=this._valueparts,t=0,u=0,o=e=null,f,i=0;i<r.length;i++)if(r[i]!=this.ttAM&&r[i]!=this.ttPM){var s=r[i].split(h),c=t,l=t+s[0].length;for(f=0;f<s.length;f++){if(u=s[f].length+t,!this._isSpace&&this._ttAtEnd&&n.start>=this._updatedttStartPosition&&n.end<=this._ttEndPosition&&this._formatparts[0].split(this._getSeperator())[1].length==this._valueparts[0].split(this._getSeperator())[1].length){t+=s[f].length+1;continue}if(n.start<=u){o=f;e=!1;i=r.length;break}else t+=s[f].length+1}}else{if(!this._isSpace&&!this._ttAtEnd?n.start<t+r[i].length:n.start<=t+r[i].length){u=r[i].length+t;o=0;e=!0;i=r.length;!this._isSpace&&this._ttAtEnd&&(t=t-1,u=u-1);break}else t+=r[i].length+1;this._isSpace||this._ttAtEnd||(t=t-1)}return o==null&&(t=c,u=l,o=0,e=!1),{start:t,end:u,index:o,isTT:e}},_modifyValue:function(n){var i,t,r,u;this._isValid(this.target.value)&&(this.model.enableStrictMode||this._checkInComplete(),i=this._getCaretSelection(),i.start==i.end?(r=this._getLeast(!0),u=this._getCategoryPosition(r),t=this._getStartEnd(u)):t=this._getStartEnd(i),this.start=t.start,this.end=t.end,this._changeValue(t,n))},_keyUpOnInput:function(n){var r,u;n.preventDefault();this._preVal!=this.element.val()&&(this._preVal=this.element.val());var e=this,t=this._getCaretSelection(),f=this._getStartEnd(t),i=this._getCategory(f);e=this;r=this._getSelectedValue(f);u=this.element.val();u.split(":");u[0]<3&&(this._poschange=isNaN(u[0]+u[1])==!0&&u[0]<10?!0:!1);i=="h"&&t.start==2&&t.end==2&&r>9&&r<13||i=="H"&&r>9&&r<24||i=="H"&&r<9&&t.start==2&&t.end==2||i=="mm"&&this._poschange==!0&&t.start==4&&t.end==4||i=="ss"&&t.start==8&&t.end==8||i=="ss"&&this._poschange==!0&&t.start==7&&t.end==7||i=="mm"&&t.start==5&&t.end==5?i=="mm"&&(this.model.timeFormat=="HH:mm"||this.model.timeFormat=="hh:mm"||this.model.timeFormat=="H:mm"||this.model.timeFormat=="h:mm")||i=="ss"&&(this.model.timeFormat=="HH:mm:ss"||this.model.timeFormat=="hh:mm:ss"||this.model.timeFormat=="H:mm:ss"||this.model.timeFormat=="h:mm:ss")||this._movePosition(t,null):(i=="hh"||i=="HH")&&t.start&&t.end==2&&r<24&&this._movePosition(t,null)},_getNextCategory:function(t,i){var r=[],e=this.seperator,o=this._getFormatParts(),u,f;return(n(o).each(function(n,t){if(t=="tt")r.push(t);else{var i=t.split(e);r=i.concat(r)}}),u=r.indexOf(t),u!=-1)?(f=i?u==0?r.length-1:u-1:u==r.length-1?0:u+1,r[f]):t},_getElePlace:function(){this._getExactPostions();var i=this._getFormatParts(),n,t;return i[0]=="tt"?(n=1,t=0):(n=0,t=1),{time:n,tt:t}},_movePosition:function(n,t){var i=this._getStartEnd(n),r=this._getCategory(i),u;r||(r=this._getLeast(t));u=this._getNextCategory(r,t);i=this._getCategoryPosition(u);i.isValid&&this._setSelection(i.start,i.end)},_findActiveIndex:function(){var t=this.ul.find("li"),i=this.element.val(),r=t.first().html(),n;n=(this._parse(i)-this._parse(r))/(this.model.interval*6e4);n=Math.round(n);this._activeItem=n==t.length?n:n+1;(this._activeItem<0||this._activeItem>t.length||isNaN(this._activeItem))&&(this._activeItem=0)},_keyDownOnInput:function(n){var t,u,i,y,f,d,c,g,l,o,p,w,nt,a,s,tt,v,b,e,k,h,r;if(this.model.readOnly&&!this._readOnlyKeys(n)||(i=n.keyCode,this._getInternalEvents&&i!=38&&i!=40&&i!=36&&i!=35&&i!=9))return!1;if(!this.model.enableStrictMode){if(this.showDropdown&&i!=38&&i!=40&&i!=27&&!this._readOnlyKeys(n))return!1;this.showDropdown&&(i==37||i==39)&&(n.keyCode=i==37?38:40)}t=this._getCaretSelection();r=this._getStartEnd(t);u=this._getCategory(r);switch(n.keyCode){case 38:n.preventDefault();this.showDropdown?this.showDropdown&&(n.preventDefault(),this._findActiveIndex(),f=this._activeItem,this._activeItem=this._disableItemSelectUp(this._activeItem-1),this._activeItem==0&&(this._activeItem=f),this._addListHover(),y=this._getActiveItem(),y.length&&this._selectTimeItem(y)):this._isValid(this.target.value)&&this._modifyValue(!0);break;case 40:n.preventDefault();n.altKey&&this.model.showPopupButton?this._showhidePopup():this.showDropdown?this.showDropdown&&(n.preventDefault(),this._findActiveIndex(),f=this._activeItem,this._activeItem=this._disableItemSelectDown(this._activeItem),this._activeItem<this._listSize?this._activeItem+=1:this._activeItem=f,this._addListHover(),this._selectTimeItem(this._getActiveItem())):this._isValid(this.target.value)&&this._modifyValue(!1);break;case 37:n.preventDefault();t.start==t.end?this._setSelection(t.start-1,t.start-1):this._movePosition(t,!0);break;case 39:n.preventDefault();t.start==t.end?this._setSelection(t.start+1,t.start+1):this._movePosition(t,!1);break;case 36:n.preventDefault();this.showDropdown?(this._activeItem=0,f=this._activeItem,this._activeItem=this._disableItemSelectDown(this._activeItem),this._activeItem<this._listSize?this._activeItem+=1:this._activeItem=f,this._addListHover(),this._selectTimeItem(this._getActiveItem())):(d=this._firstlastVal(!0),c=this._getCategoryPosition(d),c.isValid&&this._setSelection(c.start,c.end));break;case 35:n.preventDefault();this.showDropdown?(this._activeItem=this._listSize+1,f=this._activeItem,this._activeItem=this._disableItemSelectUp(this._activeItem-1),this._activeItem==0&&(this._activeItem=f),this._addListHover(),this._selectTimeItem(this._getActiveItem())):(g=this._firstlastVal(!1),l=this._getCategoryPosition(g),l.isValid&&this._setSelection(l.start,l.end));break;case 9:if(this._getInternalEvents)break;this._hideResult();o=null;n.shiftKey&&t.start>0?o=!0:!n.shiftKey&&t.end<this.element.val().length&&(o=!1);o!=null&&(n.preventDefault(),this._checkInComplete(),this._movePosition(t,o));break;case 13:if(!this.showDropdown){this._raiseChangeEvent();break}case 27:n.preventDefault();this._hideResult();break;case 8:case 46:if(this.model.enableStrictMode)return;this.target.value!=this._getSelectedText()&&(n.preventDefault(),u&&u!="tt"&&(this._findCategoryPosition(u),p=i==8&&t.start!=this.start,w=i==46&&t.end!=this.end,nt=this.end-this.start,(t.start!=t.end||nt==1)&&(p||w||t.start!=t.end)?(a=this.start,s=this.end,this.element[0].value=this._replaceAt(this.target.value,a,s,"__"),tt=s-a!=2?s+1:s,this._setSelection(a,tt)):p?(this.element[0].value=this._replaceAt(this.target.value,t.start-1,t.start,""),this._setSelection(t.start-1,t.start-1)):w&&(this.element[0].value=this._replaceAt(this.target.value,t.end,t.end+1,""),this._setSelection(t.end,t.end))))}v=this._getSelectedValue(r);b=n.keyCode?n.keyCode:n.charCode;n.keyCode>47&&n.keyCode<58?e=String.fromCharCode(b):n.keyCode>95&&n.keyCode<106&&(e=String.fromCharCode(b-48));u=="tt"&&(!n.shiftKey&&!n.ctrlKey&&!n.altKey&&n.keyCode>64&&n.keyCode<91||n.keyCode>47&&n.keyCode<58||n.keyCode>95&&n.keyCode<106)&&(n.preventDefault(),k=this._getCategoryPosition(u),this.start=k.start,this.end=k.end,this._changeAmPm(v),this._raiseChangeEvent());this.target.value!=this._getSelectedText()||n.shiftKey||n.ctrlKey||n.altKey||(n.keyCode>64&&n.keyCode<91&&!this.model.enableStrictMode&&n.preventDefault(),(n.keyCode>47&&n.keyCode<58||n.keyCode>95&&n.keyCode<106)&&(r=this._getStartEnd(t),this._setSelection(r.start,r.end)));!n.shiftKey&&!n.ctrlKey&&!n.altKey&&n.keyCode>47&&n.keyCode<58||n.keyCode>95&&n.keyCode<106?u!="tt"&&(this._getMinMax(u,!0),t.start==t.end?(this._findCategoryPosition(u),t.start==this.start?(h=e+v,this.model.enableStrictMode==!1&&(this._validateTimes(),this._targetBlur()),this.model.value==null&&this.element.val(this.model.minTime),r=this._getStartEnd(t),this._setSelection(r.start,r.end)):h=v+e,(h.length>2||!(Number(h)>=this.min&&this.max>=Number(h)))&&(this.model.enableStrictMode||n.preventDefault())):Number(e)>=this.min&&this.max>=Number(e)||this.model.enableStrictMode||n.preventDefault()):this._allowKeyCodes(n)||(this.model.enableStrictMode?n.stopPropagation():n.keyCode==8||n.keyCode==46?n.stopPropagation():n.preventDefault())},_getSelectedText:function(){if(window.getSelection){var t=n("#"+this.element[0].id).get(0);return t.value.substring(t.selectionStart,t.selectionEnd)}return document.selection.createRange().text},_allowKeyCodes:function(n){return n.ctrlKey&&(n.keyCode==65||n.keyCode==67||n.keyCode==90||n.keyCode==89)||n.keyCode==9||n.keyCode==116||n.keyCode==13?!0:!1},_readOnlyKeys:function(n){return n.keyCode==35||n.keyCode==36||n.keyCode==37||n.keyCode==39||this._allowKeyCodes(n)?!0:!1},_firstlastVal:function(n){var t=this._getFormatParts(),r=this.seperator,i;return n?t[0]!="tt"?t[0].split(r)[0]:"tt":t[0]!="tt"?"tt":t[1]?(i=t[1].split(r),i.length?i[i.length-1]:"tt"):"tt"},_mouseWheel:function(n){if(n.preventDefault(),this.model.readOnly)return!1;var t,i=n.originalEvent;i.wheelDelta?t=i.wheelDelta/120:i.detail&&(t=-i.detail/3);t>0?this._modifyValue(!0):t<0&&this._modifyValue(!1)},_addListHover:function(){this._addSelected();this._updateScrollTop()},_addSelected:function(){this.ul.find("li").removeClass("e-active e-hover");var n=this._getActiveItem();n.length&&!n.hasClass("e-disable")&&n.addClass("e-active")},_disableItemSelectDown:function(t){return(t==null||t<0)&&(t=0),t<this._listSize?n.inArray(t,this._disabledItems)<0?t:this._disableItemSelectDown(t+1):this._listSize},_disableItemSelectUp:function(t){if(t=t-1,(t==null||t<0)&&(t=0),t<this._listSize){if(n.inArray(t,this._disabledItems)<0)return t+1;if(t>0)return this._disableItemSelectUp(t)}return 0},_getActiveItem:function(){return n(this.ul.find("li")[this._activeItem-1])},_timeIconClick:function(n){var i,r;if(t.isNullOrUndefined(this.popupList)&&(this._renderDropdown(),this._addProperty()),i=!1,n.button?i=n.button==2:n.which&&(i=n.which==3),!i){if(n.preventDefault(),!this.model.enabled||this.model.readOnly||this.ul.find("li").length<1)return!1;this._showhidePopup();r=this.element.val().length;t.isTouchDevice()||this._setSelection(r,r)}},_showhidePopup:function(){if(this._getInternalEvents)return!1;this.showDropdown?this._hideResult():this._showResult()},_showResult:function(){if(this.popupList==null&&this._renderDropdown(),this._raiseEvent("beforeOpen"))return!1;this.focused||t.isTouchDevice()||this._focusElement();this.model.value?this._changeActiveEle():this.ul.find("li").removeClass("e-active");var i=this,r=this._visibleAndCalculateTop();this.popupList.slideDown(this.model.enableAnimation?200:0,function(){n(document).on("mousedown",n.proxy(i._OnDocumentClick,i))});this.scrollerObj.setModel({scrollTop:r});this.showDropdown=!0;this._listSize=this.ul.find("li").length;n(window).on("resize",n.proxy(this._OnWindowResize,this));this._on(t.getScrollableParents(this.wrapper),"scroll",this._hideResult);this._on(t.getScrollableParents(this.wrapper),"touchmove",this._hideResult);this._raiseEvent("open");this.wrapper.addClass("e-active")},_hideResult:function(i){if(this._raiseEvent("beforeClose"))return!1;i&&(i.type=="touchmove"||i.type=="scroll")&&n(i.target).parents("#"+this.popupList[0].id).length>0||this.showDropdown&&!this._getInternalEvents&&(this.showDropdown=!1,this.popupList.slideUp(this.model.enableAnimation?100:0),n(document).off("mousedown",n.proxy(this._OnDocumentClick,this)),n(window).off("resize",n.proxy(this._OnWindowResize,this)),this._off(t.getScrollableParents(this.wrapper),"scroll",this._hideResult),this._off(t.getScrollableParents(this.wrapper),"touchmove",this._hideResult),this._raiseEvent("close"),this.wrapper.removeClass("e-active"))},_visibleAndCalculateTop:function(){this.popupList.css({display:"block"});var n=this._calcScrollTop();return this._refreshPopup(),this.popupList.css({display:"none"}),n},_calcScrollTop:function(){var i=this.ul.outerHeight(),n=this.ul.find("li").outerHeight(),t;return t=this.ul.find("li.e-active").index(),n*t-(this.popupList.outerHeight()-n)/2},_changeActiveEle:function(){if(!this.popupList)return!1;var t=this.ul.find("li"),i=this.element.val(),r=t.first().html(),n;n=(this._parse(i)-this._parse(r))/(this.model.interval*6e4);n=Math.round(n);this._activeItem=n==t.length?n:n+1;(this._activeItem<0||this._activeItem>t.length||isNaN(this._activeItem)||this._ensureTimeRange(i))&&(this._activeItem=0);this._addListHover()},_OnDocumentClick:function(t){n(t.target).is(this.popupList)||n(t.target).parents(".e-time-popup").is(this.popupList)||n(t.target).is(this.wrapper)||n(t.target).parents(".e-timewidget").is(this.wrapper)?(n(t.target).is(this.popupList)||n(t.target).parents(".e-time-popup").is(this.popupList))&&t.preventDefault():this._hideResult()},_OnWindowResize:function(){this._refreshPopup()},_OnMouseEnter:function(t){var i=t.target;this.ul.find("li").removeClass("e-hover");n(i).hasClass("e-disable")||n(i).addClass("e-hover")},_OnMouseLeave:function(){(!this._dateTimeInternal||this.model.value)&&this.ul.find("li").removeClass("e-hover")},_OnMouseClick:function(t){(t.preventDefault(),n(t.target).hasClass("e-disable"))||(this.model.enabled&&!this.model.readOnly&&(this._activeItem=n(t.target).index()+1,this.ul.find("li").attr({tabindex:-1,"aria-selected":!1}),n(t.target).attr({"aria-selected":!0,tabindex:0}),this._addSelected(),this._selectTimeItem(n(t.target))),this._showhidePopup())},_selectTimeItem:function(n){this._beforeChange(n);var t=this._raiseChangeEvent();t&&this._trigger("select",{value:this.model.value,prevTime:this._previousValue})},_findCategoryPosition:function(n){n=="least"&&(n=this._getLeast(!0));var t=this._getCategoryPosition(n);this.start=t.start;this.end=t.end},_getLeast:function(t){var r=this._getFormatParts(),u=this.seperator,i=null;return n(r).each(function(n,r){if(r!="tt"){var f=r.split(u);i=t?f[f.length-1]:f[0]}}),i},_changeValue:function(n,i){var l=this.target.value,r,u=this._getCategory(n),f,o;if(!u)return!1;if(this._setSelection(this.start,this.end),r=this.target.value.substring(this.start,this.end),this._checkMinMax(this.target.value))if(r!=this.ttAM&&r!=this.ttPM){if(r=this._changeCurrentValue(r,u,i),u.length!=1&&(r=this._changeWhole(r)),this._findCategoryPosition(u),this.element.val(this._replaceAt(this.target.value,this.start,this.end,r)),this.end=this.start+r.toString().length,this._setSelection(this.start,this.end),this._ensureTimeRange(this.target.value)&&this._checkMinMax(this.target.value)){var e=this._createObject(this.target.value),s=e.getHours(),h=i?this._startTime:this._endTime,c=i?this._endTime:this._startTime;if(!t.isNullOrUndefined(this.model.disableTimeRanges))for(f=0;f<this.model.disableTimeRanges.length;f++)(h[f].getHours()===s||+e>=+this._startTime[f]&&+e<=+this._endTime[f])&&(this.target.value=this._localizeTime(c[f]),this._findCategoryPosition(u),this._setSelection(this.start,this.end),this._changeValue(n,i))}}else this._changeAmPm(r);else o=this._checkExceedRange(this.target.value),this._setTime(this.model[o]),this._findCategoryPosition(u),this._setSelection(this.start,this.end);this._checkMinMax(this.target.value)?this._raiseChangeEvent():(this.element.val(this.model.value),this._findCategoryPosition(u),this._setSelection(this.start,this.end))},_checkMinMax:function(n){var t=this._checkExceedRange(n);return t==null&&(t=!1),!t},_checkExceedRange:function(n){if(n){if(this.model.minTime&&!this._compareTime(n,this.model.minTime,!0))return"minTime";if(this.model.maxTime&&!this._compareTime(this.model.maxTime,n,!0))return"maxTime"}return null},_changeWhole:function(n){return n>9?""+n:"0"+n},_changeAmPm:function(n){n=n==this.ttAM?this.ttPM:this.ttAM;this.element.val(this._replaceAt(this.target.value,this.start,this.end,n));this._setSelection(this.start,this.end)},_changeMinute:function(n){var i=this._getExactFormat(["mm","m"]),t;i&&(this._findCategoryPosition(i),t=Number(this.target.value.substring(this.start,this.end)),this._getMinMax(i),n?t==this.max?(t=this.min,this._changeHour(n)):t+=1:t==this.min?(t=this.max,this._changeHour(n)):t-=1,this._findCategoryPosition(i),i.length!=1&&(t=this._changeWhole(t)),this.element.val(this._replaceAt(this.target.value,this.start,this.end,t)))},_changeHour:function(n){var i=this._getExactFormat(["hh","h","HH","H"]),t;i&&(this._findCategoryPosition(i),t=Number(this.target.value.substring(this.start,this.end)),this._getMinMax(i),n?t==this.max?(t+=1,this._changeMeridian()):t>this.max?t=this.min:t+=1:t==this.min?t=this.max+1:t>this.max?(t=this.max,this._changeMeridian()):t-=1,this._findCategoryPosition(i),i.length!=1&&(t=this._changeWhole(t)),this.element.val(this._replaceAt(this.target.value,this.start,this.end,t)))},_getExactFormat:function(n){for(var i=this.model.timeFormat,t=0;t<n.length;t++)if(i.indexOf(n[t])!=-1)return n[t];return null},_changeMeridian:function(){var t=this.model.timeFormat.indexOf("tt"),n;t!=-1&&(this._findCategoryPosition("tt"),n=this.target.value.substring(this.start,this.end),n=n==this.ttAM?this.ttPM:this.ttAM,this.element.val(this._replaceAt(this.target.value,this.start,this.end,n)))},_changeCurrentValue:function(n,t,i){n=Number(n);var r=t,u=1,f=!0;return(this._getMinMax(r),r=="hh"||r=="h"||r=="HH"||r=="H"?u=this.model.hourInterval:r=="mm"||r=="m"?u=this.model.minutesInterval:(r=="ss"||r=="s")&&(u=this.model.secondsInterval),u<=0)?n:(i?((r=="hh"||r=="h")&&n>this.max?n=this.min-1+u:n<this.max?n+=u:(f=!1,r!="hh"&&r!="h"?n=this.min-1+u:n+=u,this._changeAdjacent(r,i)),(r=="hh"||r=="h")&&n==this.max+1?f&&this._changeAdjacent(r,i):n>this.max+1&&(n=n-(this.max+1),f&&this._changeAdjacent(r,i)),r!="hh"&&r!="h"&&n==this.max+1&&(n=this.min,f&&this._changeAdjacent(r,i))):(r!="hh"&&r!="h"&&n>this.min?n-=u:(r=="hh"||r=="h")&&n>this.min&&n<=this.max?n-=u:(r=="hh"||r=="h")&&n==this.min?n=this.max+2-u:(f=!1,n=this.max+1-u,this._changeAdjacent(r,i)),n<this.min&&(n=n+(this.max+1),f&&this._changeAdjacent(r,i))),n)},_changeAdjacent:function(n,t){n=="ss"||n=="s"?this._changeMinute(t):n=="mm"||n=="m"?this._changeHour(t):(n=="hh"||n=="h"||n=="HH"||n=="H")&&this._changeMeridian()},_valueChange:function(){this._raiseChangeEvent()},_beforeChange:function(n){return this._raiseEvent("beforeChange")||this.element.val(n.text()),!0},_raiseChangeEvent:function(n,t){n=n===i?this.model.value:n;this._previousValue=n;var r=this.target.value?this.target.value:null;return n==r?!1:(this.isValidState=this._checkMinMax(this.target.value)&&this._isValid(this.target.value,this.model.enableStrictMode)||!this.target.value?!0:!1,this.model.value=this._isValid(this.target.value,!0)&&this._checkMinMax(this.target.value)?this.target.value:null,this.model.value||this.model.enableStrictMode||this._setTime(this.model.value),this.model.value==this._previousValue)?!1:(this._raiseEvent("change",t),this._raiseEvent("_change",t),!0)},_raiseEvent:function(n,t){var i={value:this.model.value,prevTime:this._previousValue};return n=="change"&&(i.isInteraction=!t),this._trigger(n,i)},_checkIE7:function(){if(navigator.appName=="Microsoft Internet Explorer"){var t=new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})"),n=-1;if(t.exec(navigator.userAgent)!=null&&(n=parseFloat(RegExp.$1)),n>=7&&n<8)return!0}return!1},_replaceAt:function(n,t,i,r){return n.substring(0,t)+r+n.substring(i)},_localizeTime:function(i){return i?n.trim(t.format(this._createObject(i),this.model.timeFormat,this.model.locale)):null},_localizeMeridian:function(i){return n.trim(t.format(i,"tt",this.model.locale))},_compareTime:function(n,t,i){return i=i?!0:!1,i?this._parse(n)>=this._parse(t):this._parse(n)>this._parse(t)},_isValid:function(n,t){return n=this._createObject(n,t),n&&typeof n.getTime=="function"&&isFinite(n.getTime())},_parse:function(n){return Date.parse(this._createObject(n))},_setEmptyDate:function(n){var t=new Date(n);return t.setDate(1),t.setMonth(0),t.setFullYear(2e3),t},_createObject:function(n,i){var r=null,f,e;if(typeof n=="string"){var o=this._setModelOption?this._preTimeformat:this.model.timeFormat,u=t.preferredCulture(this.model.locale).calendar.patterns.d,s=t.format(new Date("1/1/2000"),u,this.model.locale);r=t.parseDate(s+" "+n,u+" "+o,this.model.locale);(this._extISORegex.exec(n)||this._basicISORegex.exec(n))&&(this.model.value=r=this._timeFromISO(n));this._setModelOption=!1;r||(f=new Date(n),r=isNaN(Date.parse(f))||t.isNullOrUndefined(n)?!this._dateTimeInternal||n==""?null:new Date("1/1/2000 "+n):this._setEmptyDate(n))}else typeof n=="number"?r=new Date(n):n instanceof Date&&(r=this._setEmptyDate(n));return r&&!this._dateTimeInternal&&i&&(e=this._localizeTime(r),this._ensureTimeRange(e)&&(r=null)),r},_getLocalizedLabels:function(){return t.getLocalizedConstants(this.sfType,this.model.locale)},_wireEvents:function(){this._on(this.element,"focus",this._targetFocus);this._on(this.element,"blur",this._targetBlur);this._on(this.element,"mousedown",this._mouseDownOnInput);this._on(this.element,"keydown",this._keyDownOnInput);this._on(this.element,"keyup",this._keyUpOnInput)}});t.TimePicker.Locale=t.TimePicker.Locale||{};t.TimePicker.Locale["default"]=t.TimePicker.Locale["en-US"]={watermarkText:"select a time"}})(jQuery,Syncfusion)});
