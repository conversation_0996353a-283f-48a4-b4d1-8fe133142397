﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.WebApi
{
    public enum ApiResponseResultCode
    {
        //General result codes
        Ok,
        Exception,

        //Users result codes
        LoginFailedInvalidUser,
        LoginFailedSubscriptionExpired,
        LoginFailedAccountNotExists
    }


    public class ApiResponse
    {
        public ApiResponseResultCode ResultCode { get; set; }
        //public string ErrorMessage { get; set; } = "";
        public Exception? Exception { get; set; }
        //public List<AudioInfoDTO> Data { get; set; }
    }

    public class ApiResponseS<Y> : ApiResponse where Y : struct
    {
        public Y? Data { get; set; }
        public int? DataTotalCount { get; set; }
    }

    public class ApiResponse<T> : ApiResponse where T : class
    {
        public T? Data { get; set; }
        public int? DataTotalCount { get; set; }
    }
}
