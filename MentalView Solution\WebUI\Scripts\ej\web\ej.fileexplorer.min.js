/*!
*  filename: ej.fileexplorer.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["jsrender","./../common/ej.core.min","./../common/ej.data.min","./../common/ej.touch.min","./../common/ej.draggable.min","./../common/ej.scroller.min","./ej.button.min","./ej.checkbox.min","./ej.treeview.min","./ej.uploadbox.min","./ej.waitingpopup.min","./ej.dialog.min","./ej.splitter.min","./ej.toolbar.min","./ej.menu.min","./ej.splitbutton.min","./ej.grid.min"],n):n()})(function(){(function(n,t,i){t.widget("ejFileExplorer","ej.FileExplorer",{element:null,model:null,validTags:["div"],_addToPersist:["layout","selectedFolder","selectedItems","height","width"],_rootCSS:"e-fileexplorer",_requiresID:!0,defaults:{fileTypes:"*.*",filterSettings:{caseSensitiveSearch:!1,filterType:"contains",allowSearchOnTyping:!0},showToolbar:!0,showCheckbox:!0,showNavigationPane:!0,allowDragAndDrop:!0,showContextMenu:!0,showFooter:!0,layout:"grid",locale:"en-US",selectedFolder:"",selectedItems:"",virtualItemCount:0,gridSettings:{allowSorting:!0,columns:[{field:"name",headerText:"Name",width:"30%"},{field:"dateModified",headerText:"Date Modified",width:"30%"},{field:"type",headerText:"Type",width:"15%"},{field:"size",headerText:"Size",width:"12%",textAlign:"right",headerTextAlign:"left"}],allowResizing:!0},tools:{creation:["NewFolder"],navigation:["Back","Forward","Upward"],addressBar:["Addressbar"],editing:["Refresh","Upload","Delete","Rename","Download"],copyPaste:["Cut","Copy","Paste"],getProperties:["Details"],searchBar:["Searchbar"],layout:["Layout"],sortBy:["SortBy"]},toolsList:["layout","creation","navigation","addressBar","editing","copyPaste","sortBy","getProperties","searchBar"],path:"",rootFolderName:"",height:"400px",width:"850px",minWidth:"400px",maxWidth:null,minHeight:"250px",maxHeight:null,isResponsive:!1,uploadSettings:{allowMultipleFile:!0,maxFileSize:31457280,autoUpload:!1,showFileDetails:!0,dialogPosition:{X:"",Y:""},dialogAction:{modal:!1,closeOnComplete:!1,drag:!0,content:null}},enableResize:!1,cssClass:"",enableRTL:!1,allowKeyboardNavigation:!0,showThumbnail:!0,enableThumbnailCompress:!1,showRoundedCorner:!1,ajaxAction:"",ajaxDataType:"json",ajaxSettings:{read:{},createFolder:{},remove:{},rename:{},paste:{},getDetails:{},download:{},upload:{},getImage:{},search:{}},allowMultiSelection:!0,contextMenuSettings:{items:{navbar:["NewFolder","Upload","|","Delete","Rename","|","Cut","Copy","Paste","|","Getinfo"],cwd:["Refresh","Paste","|","SortBy","|","NewFolder","Upload","|","Getinfo"],files:["Open","Download","|","Upload","|","Delete","Rename","|","Cut","Copy","Paste","|","OpenFolderLocation","Getinfo"]},customMenuFields:[]},enablePersistence:!1,layoutChange:null,getImage:null,select:null,unselect:null,createFolder:null,remove:null,cut:null,copy:null,paste:null,open:null,beforeOpen:null,beforeUploadDialogOpen:null,beforeUpload:null,beforeUploadSend:null,uploadSuccess:null,uploadError:null,uploadComplete:null,beforeDownload:null,beforeGetImage:null,beforeAjaxRequest:null,resizeStart:null,resize:null,resizeStop:null,templateRefresh:null,dragStart:null,drag:null,dragStop:null,drop:null,menuClick:null,menuBeforeOpen:null,menuOpen:null,create:null,destroy:null},dataTypes:{filterSettings:"data",showToolbar:"boolean",showNavigationPane:"boolean",showContextMenu:"boolean",allowDragAndDrop:"boolean",allowKeyboardNavigation:"boolean",showRoundedCorner:"boolean",showFooter:"boolean",layout:"enum",tools:{creation:"array",navigation:"array",addressBar:"array",editing:"array",layout:"array",copyPaste:"array",getProperties:"array",searchBar:"array"},contextMenuSettings:{items:{navbar:"array",cwd:"array",files:"array"},customMenuFields:"array"},gridSettings:{allowSorting:"boolean",columns:"array",allowResizing:"boolean"},toolsList:"array",uploadSettings:"data",ajaxSettings:"data"},_setModel:function(i){var f=this,r,e,o,u;for(r in i)switch(r){case"showToolbar":i[r]?t.isNullOrUndefined(this._toolBarItems)?this._updateToolbar():this._toolBarItems.show():this._toolBarItems.hide();this.adjustSize();break;case"showNavigationPane":case"showTreeview":this.model.showNavigationPane=this.model.showTreeview=i[r];this._showHideSplitBar(this.model.showNavigationPane);break;case"showContextMenu":this._showHideContextMenu();break;case"height":this.element.css("height",this._getProperValue(this.model.height));this.adjustSize();break;case"width":this.element.css("width",this._getProperValue(this.model.width));this.adjustSize();break;case"layout":this._switchLayoutView(!0);break;case"allowDragAndDrop":this.model.allowDragAndDrop=i[r];this._draggableOption(i[r]?"_on":"_off");i[r]?this._allowDrag():this._preventDrag();break;case"showThumbnail":this.model.showThumbnail=i[r];this._switchLayoutView();break;case"enableThumbnailCompress":this.model.enableThumbnailCompress=i[r];this.model.showThumbnail&&this.model.layout!="grid"&&this._renderTileView(this._fileExplorer[this._originalPath]);break;case"path":i[r]?this._setPath(i[r]):this._getPath();break;case"enableRTL":this._enableRTL(i[r]);break;case"allowKeyboardNavigation":this._subControlsSetModel("allowKeyboardNavigation",i[r]);this._gridObj&&this._gridObj.setModel({allowKeyboardNavigation:i[r]});break;case"showFooter":i[r]?(this._statusbar.hasClass("e-statusbar")?(e=this._splittag.find(".e-cont2").outerHeight()-this._statusbar.outerHeight(),this._tileView.height(e),this._gridtag.height(e),this._statusbar.show()):(this._createStatusBar(),this._updateData(),this.model.enableResize&&this._resizeFileExplorer(),this._on(n("#"+this._ExplorerId+"_switchGridView"),"click",this._switchView),this._on(n("#"+this._ExplorerId+"_swithListView"),"click",this._switchView)),o=this._splittag.height()-this._gridtag.find(".e-gridheader").outerHeight(),this._tileContent.parent(".e-tile-wrapper").ejScroller({height:this._splittag.outerHeight()-this._statusbar.outerHeight(),scrollerSize:this._scrollerSize,thumbStart:function(n){f._onThumbStart(n)}})):(this._tileView.height("auto"),this._gridtag.height("auto"),this._statusbar.hide(),o=this._splittag.height()-this._gridtag.find(".e-gridheader").outerHeight(),this._tileContent.parent(".e-tile-wrapper").ejScroller({height:this._splittag.outerHeight(),scrollerSize:this._scrollerSize,thumbStart:function(n){f._onThumbStart(n)}}));break;case"gridSettings":u=JSON.parse(JSON.stringify(i[r]));u.columns&&(u.columns.unshift({field:"cssClass",headerText:"",cssClass:"e-grid-image",width:22,template:"<script type='text/x-jsrender'><span class='e-fe-icon {{:cssClass}}' unselectable='on'><\/span><\/script>",textAlign:t.TextAlign.Center,allowResizing:!1}),this.model.showCheckbox&&u.columns.unshift({field:"",headerText:"check",cssClass:"e-col-check",width:18,template:"<script type='text/x-jsrender'><input type='checkbox' class='e-grid-row-checkbox'/><\/script>",textAlign:t.TextAlign.Center,headerTextAlign:t.TextAlign.Center,allowResizing:!1,allowSorting:!1}));this._gridObj&&this._gridtag.ejGrid(u);this._showHideContextMenu();this._renderSortbyDrpdwn();break;case"locale":this.model.locale=i[r];this._destroy();this._init();break;case"cssClass":this._changeSkin(i[r]);break;case"fileTypes":n.each(f._fileExplorer,function(n){f._fileExplorer[n]=""});this._removeOldSelectionDetails();this._refreshItems(this._selectedNode,this._currentPath);this._uploadtag.ejUploadbox("option",{extensionsAllow:this.model.fileTypes=="*.*"?"":this.model.fileTypes.replace(/\*/g,"")});break;case"selectedFolder":this._selectedFolder(this.model.selectedFolder);break;case"selectedItems":this._selectedItems=i[r];this._setSelectedItems(i[r]);break;case"allowMultiSelection":i[r]||this._setSelectedItems([]);this._gridtag.find(".e-gridheader").length&&this._gridtag.ejGrid("option",{selectionType:i[r]?"multiple":"single"});t.isNullOrUndefined(this._gridObj)||(this.model.allowMultiSelection?this._gridtag.find(".e-headercelldiv>span.e-chkbox-wrap").show():this._gridtag.find(".e-headercelldiv>span.e-chkbox-wrap").hide());break;case"isResponsive":this.model.isResponsive=i[r];this._toolBarObj&&(this._toolBarObj.option("isResponsive",this.model.isResponsive),this.model.showToolbar?this._toolBarItems.show():this._toolBarItems.hide(),this.adjustSize());this._wireResizing();break;case"tools":case"toolsList":r=="tools"?n.extend(this.model.tools,i[r]):this.model.toolsList=i[r];this._toolBarObj&&(this._toolBarObj.destroy(),this.element.find("#"+this._ExplorerId+"_toolbar").remove(),this._updateToolbar(),this.model.showToolbar?this._toolBarItems.show():this._toolBarItems.hide(),this.adjustSize());break;case"enableResize":i[r]?i[r]&&this.model.showFooter&&(this._resizeItem=t.buildTag("div.e-icon e-fe-resize e-resize-handle"),this._resizeItem.insertBefore(this.element.find(".e-switchView")),this._resizeFileExplorer()):this._resizeItem&&this._resizeItem.remove();break;case"minHeight":this.element.css("min-height",this._getProperValue(this.model.minHeight));this._refreshResizeHandler();break;case"maxHeight":this.element.css("max-height",this._getProperValue(this.model.maxHeight));this._refreshResizeHandler();break;case"minWidth":this.element.css("min-width",this._getProperValue(this.model.minWidth));this._refreshResizeHandler();break;case"maxWidth":this.element.css("max-width",this._getProperValue(this.model.maxWidth));this._refreshResizeHandler();break;case"showCheckbox":this._changeCheckState=this.model.showCheckbox;this.model.layout=="grid"?this._renderGridView(this._fileExplorer[this._originalPath]):this._renderTileView(this._fileExplorer[this._originalPath],!0);this._setSelectedItems(this.model.selectedItems);break;case"showRoundedCorner":this._roundedCorner(i[r]);break;case"contextMenuSettings":n.extend(this.model[r],i[r]);this._showHideContextMenu();break;case"uploadSettings":this._uploadObj.option(i[r]);break;case"rootFolderName":this._changeRootFolderName();this._updateAddressBar();this._treeObj.element.find("li:first > div > .e-text").hasClass("e-active")&&this._treeObj.selectNode(this._treeObj.element.find("li:first > div > .e-text"));break;case"virtualItemCount":this.model.virtualItemCount=i[r];this._switchLayoutView()}},_init:function(){this._cloneElement=this.element.clone();t.isNullOrUndefined(this.model.uploadBoxSettings)||(this.model.uploadSettings=this.model.uploadBoxSettings);t.isNullOrUndefined(this.model.showTreeview)||(this.model.showNavigationPane=this.model.showTreeview);t.isNullOrUndefined(this.model.move)||(this.model.cut=this.model.move);this._initialize();this._render();this._changeLayoutActive(this.model.layout);this._initializeFlag=!0},_postInit:function(){this._enablePostInit=!1;this._enableRTL(this.model.enableRTL);this._wireEvents();this._wireResizing();this._setMinMaxSizeInInteger();this.model.enableResize&&this.model.showFooter&&this._resizeFileExplorer();this._isClicked=!1;this._selectedTreeFolder&&this._currentPath!=this._selectedTreeFolder&&this._selectedFolder(this._selectedTreeFolder);this._selectedNodes.length&&this._setSelectedItems(this._selectedNodes);this._isClicked=!0},_initialize:function(){this._ExplorerId=this.element[0].id;this._fileExplorer={};this._feParent={};this._updateImages={};this._selectedStates=[];this._updateOnNodeSelection=!1;this._isClicked=!0;this._toolBarObj=null;this._tileView=null;this._tileScroll=null;this._originalPath=null;this._initPath="";this._initUpdate=!1;this._scrollerSize=8;this._editingToolsState=!0;this._renderMultiTouchDialog();this._ensureResolution();this._isDevice=this._checkDevice();this.element.css({height:this._getProperValue(this.model.height),width:this._getProperValue(this.model.width),"min-width":this._getProperValue(this.model.minWidth),"max-width":this._getProperValue(this.model.maxWidth),"min-height":this._getProperValue(this.model.minHeight),"max-height":this._getProperValue(this.model.maxHeight)});this._customCssClass=this.model.cssClass;this.element.addClass(this.model.cssClass);this._isTreeNode=!1;this._selectedItems=[];this._selectedTileItems=[];this._downloadDialog=null;this._newFolderDialog=null;this._renameDialog=null;this._openDialog=null;this._detailsDialog=null;this._alertDialog=null;this._enablePostInit=!0;this._initialTime=(new Date).getTime();this._currentPath=this.model.path.replace(/\\/g,"/");this._rootPath=this._currentPath=this._currentPath.endsWith("/")?this._currentPath:this._currentPath+"/";this._gridObj=null;this._setUploadLocalization();this._restrictedToolbarOptions=[];this._restrictedMenuOption=[];this._changeCheckState=this.model.showCheckbox;this._perRow=1;this._suggestionItems=[];this._highlightedNodes="";this._prevsorting=!0;this._FilteredFiles=[];this._scrollListCount=0;this._previousFolder="";this._count=0;this._priviousScrollListCount=0;this._vScrollTop=0;this._renamedStatus=!1;this._virtualScrollRenamedItem="";this._initializeFlag=!1},_renderMultiTouchDialog:function(){var n,i,r;this._customPop=t.buildTag("div.e-fe-popup","",{display:"none"});n=t.buildTag("div.e-content");i=t.buildTag("div.e-downtail e-tail");this.model.allowMultiSelection&&(r=t.buildTag("span.e-rowselect e-icon"),n.append(r));this._customPop.append(n);this._customPop.append(i);this.element.append(this._customPop);this._on(this._customPop,"mousedown",this._popupClick)},_mouseSelection:function(i){if(this._target=n(i.target),(!this.model.allowDragAndDrop||!(this._target.closest("li").hasClass("e-tilenode")||this._target.is("li.e-tilenode")||this._target.closest(".e-rowcell").is("td")||this._target.closest(".e-row").is("tr")||this._target.is("tr")))&&!(this._target.closest(".e-scrollbar").length>0)&&this.model.allowMultiSelection&&i.button!=2){var r=this;this._currentTarget=n(i.currentTarget);this._initialx=i.pageX;this._initialy=i.pageY;this._selectionContainer=t.buildTag("div.e-fe-selection-rect");this._currentTarget.append(this._selectionContainer);this.element.hasClass("e-ie8")&&this._selectionContainer.addClass("e-active");i.preventDefault();this._containerleftpos=this._currentTarget.offset().left;this._containerrightpos=this._currentTarget.width()+this._containerleftpos;this._containertoppos=this._currentTarget.offset().top;this._containerbottompos=this._currentTarget.height()+this._containertoppos;this._currentTarget.hasClass("e-gridcontent")?(this._gridheader=this._gridObj.element.find(".e-headercontent").offset().top,this._gridcontent=this._containertoppos):(this._gridheader=null,this._gridcontent=null);this._prevGridItems=[];this._prevTileItems=[];this._prevScrollTop=null;i.ctrlKey||i.shiftKey||(this._selectedRows=[]);(i.ctrlKey||i.shiftKey)&&(this._currentTarget.hasClass("e-gridcontent")&&!t.isNullOrUndefined(this._selectedRows)?this._prevGridItems=this._selectedRows:this._prevTileItems=this._currentTarget.find("li.e-tilenode.e-active"));this._on(n(document),"mousemove",this._createSelectionRectangle);this._on(n(document),"mouseup",this._removeSelectionRectangle)}},_createSelectionRectangle:function(n){var i,r;n.preventDefault();this._selectedRows=[];this._scroller(n);i=this._selectionContainer;this._gridObj&&(r=this._gridObj.element.find(".e-headercontent").height());t.isNullOrUndefined(r)&&(r=null);n.pageX<this._initialx?n.pageX<this._containerleftpos?i.css({width:this._initialx-this._containerleftpos,left:0}):i.css({width:this._initialx-n.pageX,left:n.pageX-this._containerleftpos}):n.pageX>this._containerrightpos?i.css({width:this._containerrightpos-this._initialx,left:this._initialx-this._containerleftpos}):i.css({width:n.pageX-this._initialx,left:this._initialx-this._containerleftpos});n.pageY<this._initialy?n.pageY<this._containertoppos?i.css({height:this._initialy-this._containertoppos,top:0+(this._gridcontent-this._gridheader)}):i.css({height:this._initialy-n.pageY,top:n.pageY-(this._containertoppos-r)}):n.pageY>this._containerbottompos?i.css({height:this._containerbottompos-this._initialy,top:this._initialy-(this._containertoppos-r)}):i.css({height:n.pageY-this._initialy,top:this._initialy-(this._containertoppos-r)});this._changeItemState(i,n)},_removeSelectionRectangle:function(){this._firstSelectedElement=null;this._selectionContainer.remove();this._off(n(document),"mousemove",this._createSelectionRectangle);this._off(n(document),"mouseup",this._removeSelectionRectangle);this._currentTarget.css("pointer-events","initial")},_scroller:function(n){var u,f,i,r;this._scrollObj=this._currentTarget.data("ejScroller");i=r=20;this._currentTarget.hasClass("e-scroller")&&!t.isNullOrUndefined(this._firstSelectedElement)&&(this._currentTarget.find(".e-vscrollbar").length>0&&(u=this._firstSelectedElement.offset().top-this._initialy),this._currentTarget.find(".e-hscrollbar").length>0&&(f=this._firstSelectedElement.offset().left-this._initialx),n.pageY>this._containerbottompos?this._scrollObj.option({scrollTop:i+this._scrollObj.option("scrollTop")}):n.pageY<this._containertoppos?this._scrollObj.option({scrollTop:this._scrollObj.option("scrollTop")-i}):n.pageX>this._containerrightpos?this._scrollObj.option({scrollLeft:this._scrollObj.option("scrollLeft")+r}):n.pageX<this._containerleftpos&&this._scrollObj.option({scrollLeft:this._scrollObj.option("scrollLeft")-r}),this._currentTarget.find(".e-vscrollbar").length>0&&(this._initialy=this._firstSelectedElement.offset().top-u),this._currentTarget.find(".e-hscrollbar").length>0&&(this._initialx=this._firstSelectedElement.offset().left-f),this._prevScrollTop=this._scrollObj.option("scrollTop"))},_changeItemState:function(i,r){var a=this,o,s,u=n(this._currentTarget).find(".e-fe-selection-rect"),f,v,e,y,p,h,w,c,l;if(this._currentTarget.hasClass("e-gridcontent")){if(p=this._currentTarget.find(".e-vscrollbar").length>0?this._currentTarget.find(".e-content")[0].scrollHeight:this._currentTarget.children().outerHeight(),e=parseInt(u.css("top"))-this._gridObj.element.find(".e-gridheader").outerHeight()+this._scrollObj.model.scrollTop,h=e+parseInt(u.css("height")),f=a._currentTarget.find("tr:first").outerHeight(),o=parseInt(e/f),s=parseInt(h/f),w=p/f,(r.ctrlKey||r.shiftKey)&&!t.isNullOrUndefined(this._prevGridItems))for(c=0;c<this._prevGridItems.length;c++)this._selectedRows.push(this._prevGridItems[c]);this._selectGridRecords(r,i,o,s);t.isNullOrUndefined(this._prevScrollTop)||this._scrollObj&&this._scrollObj.scrollY(this._prevScrollTop)}else this._tileNodes=n(this._currentTarget).find("li.e-tilenode"),l=parseInt(this._tileNodes.first().outerWidth())+2*parseInt(this._tileNodes.first().css("margin-left")),f=parseInt(this._tileNodes.first().outerHeight())+parseInt(this._tileNodes.first().css("margin-top"))+parseInt(this._tileNodes.first().css("margin-bottom")),v=parseInt(u.css("left")),e=parseInt(u.css("top"))+this._scrollObj.model.scrollTop,y=parseInt(u.css("left"))+parseInt(u.css("width")),h=e+parseInt(u.css("height")),this._columnStart=parseInt(v/l)+1,o=parseInt(e/f)+1,this._columnEnd=parseInt(y/l)+1,s=parseInt(h/f)+1,this._totColumn=parseInt(a._currentTarget.find(".e-tile-content").outerWidth()/l),this._selectTileItems(i,r,o,s)},_selectGridRecords:function(i,r,u,f){for(var s,o,e=u;e<=f;e++)if(o=n(this._currentTarget.find("tr")[e]),!t.isNullOrUndefined(o)){if(o.length==0)continue;s=o.offset();t.isNullOrUndefined(this._firstSelectedElement)&&(this._firstSelectedElement=o);var h=s.left,c=s.top,l=s.left+o.width(),a=s.top+o.height();a<r.offset().top||c>r.offset().top+r.height()||l<r.offset().left||h>r.offset().left+r.width()||(this._currentTarget.css("pointer-events","none"),i.ctrlKey&&n.inArray(e,this._prevGridItems)>-1?this._selectedRows.splice(this._selectedRows.indexOf(e),1):i.shiftKey&&n.inArray(e,this._prevGridItems)>-1?this._prevGridItems.splice(this._prevGridItems.indexOf(e),1):this._selectedRows.push(e))}this.option("selectedItems",[]);this._selectedRows.length>0&&(this._currentTarget.find("table tr").hasClass("e-hover")&&this._currentTarget.find("table tr").removeClass("e-hover"),this._gridObj.selectRows(this._selectedRows))},_selectTileItems:function(i,r,u,f){var s,c,a,l,o,e;if(this.option("selectedItems",[]),(r.ctrlKey||r.shiftKey)&&!t.isNullOrUndefined(this._prevTileItems))for(e=0;e<this._prevTileItems.length;e++)this._prevTileItems[e].click();for(e=u;e<=f;e++)for(s=this._columnStart;s<=this._columnEnd;s++)if(c=s>this._totColumn?this._totColumn:s,a=this.model.enableRTL?this._totColumn-c+1:c,l=parseInt((e-1)*this._totColumn+a-1),!n(this._tileNodes.eq(l)[0]).hasClass("e-active")||r.ctrlKey||r.shiftKey){if(o=this._tileNodes.eq(l),o.length==0)continue;var h=o.offset(),v=h.left,y=h.top,p=h.left+o.width(),w=h.top+o.height();if(!(w<i.offset().top||y>i.offset().top+i.height()||p<i.offset().left||v>i.offset().left+i.width()))if(this._currentTarget.css("pointer-events","none"),o.removeClass("e-hover"),t.isNullOrUndefined(this._firstSelectedElement)&&(this._firstSelectedElement=o),r.shiftKey&&n.inArray(o[0],this._prevTileItems)>-1)for(e=0;e<this._prevTileItems.length;e++)o[0]==this._prevTileItems[e]&&this._prevTileItems.splice(e,1);else o.click()}},_popupClick:function(){var n=this._customPop.find(".e-rowselect");n.hasClass("e-spanclicked")?this._hidePopup():n.addClass("e-spanclicked")},_hidePopup:function(){this._customPop!=null&&this._customPop.is(":visible")&&(this._customPop.find(".e-rowselect").removeClass("e-spanclicked"),this._customPop.hide())},_ensureResolution:function(){this._isMobileOrTab=n(window).width()<=750?!0:!1;this._isMobileOrTab?(this.element.addClass("e-fe-mobile"),this._toolBarObj&&this._toolBarObj._liTemplte.css("max-width",this.element.width())):(this.element.removeClass("e-fe-mobile"),this._splitObj&&this.model.showNavigationPane&&this._splitObj.element.find(".e-cont1").hasClass("collapsed")&&(this.model.enableRTL?this._splitObj.expand(1):this._splitObj.expand(0)));this._toolBarObj&&this._toolBarObj.option("cssClass",this.model.cssClass+" e-fe-toolbar "+(this._isMobileOrTab?"e-fe-mobile":""));n(window).width()<=350||this.element.width()<=350?this.element.addClass("e-fe-small"):this.element.removeClass("e-fe-small");n(window).width()<=295||this.element.width()<=295?this.element.addClass("e-fe-short"):this.element.removeClass("e-fe-short")},_showHideNavigation:function(){this._splitObj.element.find(".e-cont1").hasClass("collapsed")?this.model.enableRTL?this._splitObj.collapse(0):this._splitObj.collapse(1):this.model.enableRTL?this._splitObj.expand(0):this._splitObj.expand(1)},_renderSplitIcon:function(){this.model.showNavigationPane&&(this._splitIcon=t.buildTag("div.e-fe-split-icon"),this._splitIcon.append("<span class='e-icon e-arrow-sans-left'><\/span>"),this._splitObj.element.find(".e-splitbar").append(this._splitIcon),this._on(this._splitIcon,"touchend click",this._showHideNavigation),this._isMobileOrTab&&this._splitObj&&this.model.showNavigationPane&&(this.model.enableRTL?this._splitObj.collapse(1):this._splitObj.collapse(0)))},_checkDevice:function(){return t.isDevice()&&t.isTouchDevice()},_initContextMenuOptions:function(n){for(var i,f,u=[],r=this.model.contextMenuSettings.items[n],e=this.model.contextMenuSettings.customMenuFields,t=0;t<r.length;t++)r[t]!="|"&&(i=this._getCustomItem(e,r[t]),f=r[t+1]=="|"?"e-fe-separator":null,this._addMenuItem(i,r[t],f,u,n),i&&i.hasOwnProperty("child")&&this._getChildItems(i.child,u,n,i.id));return u},_addMenuItem:function(n,i,r,u,f,e){var o=this._extendAttr(n,"text"),s,h;this["_menu"+i]=o?o:this._getLocalizedLabels("ContextMenu"+i);s=this._extendAttr(n,"htmlAttributes",r);h=this._extendAttr(n,"spriteCssClass","e-fileexplorer-toolbar-icon "+i);u.push({id:t.isNullOrUndefined(n)?this._ExplorerId+"_"+f+"_"+i:i,text:this["_menu"+i],parentId:e,sprite:h,htmlAttr:s})},_getChildItems:function(n,i,r,u){for(var e,f=0,o=n.length;f<o&&!t.isNullOrUndefined(n[f]);f++)e=n[f],this._addMenuItem(e,n[f].id,null,i,r,u),e&&e.hasOwnProperty("child")&&this._getChildItems(e.child,i,r,e.id)},_getCustomItem:function(n,i){for(var u,r=0,f=n.length;r<f&&!t.isNullOrUndefined(n[r]);r++){if(n[r].id==i){u=n[r];break}if(n[r].hasOwnProperty("child")){u=this._getCustomItem(n[r].child,i);break}}return u},_extendAttr:function(n,t,i){return i?t=="htmlAttributes"?{"class":n?n[t]?n[t]["class"]?n[t]["class"]+" "+i:i:i:i}:n?n[t]?n[t]+" "+i:i:i:n?n[t]?n[t]:null:null},_render:function(){this._selectedTreeFolder=this.model.selectedFolder;this._selectedNodes=this.model.selectedItems;this.element.addClass("e-widget e-box").attr({role:"fileexplorer",tabindex:0});this.model.showToolbar&&this._renderToolBar();this._createSplitPane();this._read();this._roundedCorner(this.model.showRoundedCorner);this._isIE8=t.browserInfo().name=="msie"&&t.browserInfo().version=="8.0"?!0:!1;this._isIE8&&this.element.addClass("e-ie8")},_read:function(){var r=this,u={data:{ActionType:"Read",Path:this._currentPath,ExtensionsAllow:this.model.fileTypes,SelectedItems:this._getSelectedItemDetails(this._getFolderPath(),this._selectedContent)},url:this.model.ajaxAction,type:"POST",async:!1,success:function(n){if(n!==i&&n!==null){if(n.hasOwnProperty("d")&&(n=n.d),!t.isNullOrUndefined(n.error)){r._showErrorDialog(n.error);return}r._feParent[r._currentPath]=n.cwd;r._readSuccess(n.files);r._sortingDateFormat=n.dateFormat;r._enablePostInit&&r._postInit()}},successAfter:this.model.ajaxSettings.read.success};this.model.ajaxSettings.read.success=i;n.extend(!0,u,this.model.ajaxSettings.read);this._sendAjaxRequest(u)},_sendAjaxRequest:function(i,r){var e,u,f;(!i.dataType&&this.model.ajaxDataType&&(i.dataType=this.model.ajaxDataType),this.model.ajaxAction!=""&&this._currentPath!="")&&((i.data.ActionType!="Read"&&(this._selectedItemDetails=i.data.SelectedItems),e={Name:"",ActionType:"",Path:"",ExtensionsAllow:"",LocationFrom:"",LocationTo:"",Action:"",NewName:"",Names:[],CaseSensitive:!1,SearchString:"",FileUpload:null,CommonFiles:null},r||this._waitingPopup.show(),n.extend(!0,e,i.data),u={data:e,ajaxSettings:i},f=this,u.data.ActionType=="Paste"?(u.data.LocationFrom=this._pathCorrection(u.data.LocationFrom),u.data.LocationTo=this._pathCorrection(u.data.LocationTo)):u.data.Path=this._pathCorrection(u.data.Path),this._trigger("beforeAjaxRequest",u))||(i=u.ajaxSettings,n.ajax({data:i.dataType&&i.dataType.toLowerCase()=="jsonp"||i.contentType=="application/x-www-form-urlencoded"?{json:JSON.stringify(u.data)}:JSON.stringify(u.data),url:i.url,type:i.dataType&&i.dataType.toLowerCase()=="jsonp"?"GET":i.type,async:i.async,success:function(n){f._waitingPopup.hide();i.success.call(this,n);typeof i.successAfter=="function"&&i.successAfter.apply(this,arguments)},contentType:i.contentType?i.contentType:"application/json",dataType:i.dataType,jsonpCallback:i.jsonpCallback?i.jsonpCallback:i.dataType&&i.dataType.toLowerCase()=="jsonp"?"MyCallbackFunction":"",error:i.error?i.error:function(n){f._waitingPopup.hide();var i=n.responseJSON?n.responseJSON.ExceptionType+", "+n.responseJSON.ExceptionMessage:n.statusText;f._alertDialog=f._createDialog(t.buildTag("div.e-fe-dialog-label",i),{width:400,height:"auto",title:f._getLocalizedLabels("Error")});f._alertDialogObj=f._alertDialog.data("ejDialog")},beforeSend:i.beforeSend,complete:function(){f._waitingPopup.hide();i.complete}})))},_pathCorrection:function(n){return this.model.path=="/"&&(n.indexOf(":")>=0?n=n.replace("//",""):n!="/"&&(n=n.replace("/",""))),n},_onBeforeOpen:function(){var t,n=[],i;return t=this._nodeType=="File"?this._currentPath.replace("~","..")+this._selectedFile:this._currentPath,this._selectedFile?n=this._getSelectedItemDetails(this._currentPath,this._selectedFile):this._selectedContent&&(n=this._getSelectedItemDetails(this._getFolderPath(),this._selectedContent)),i={path:t,itemType:this._nodeType,selectedItems:n},this._trigger("beforeOpen",i)},_readSuccess:function(n){var t,u,r;if(n!==i&&n!==null){for(this._update=!1,t=0;t<n.length;t++)n[t].sizeInByte=n[t].size,n[t].size=n[t].isFile?this._bytesToSize(n[t].size):"",n[t].cssClass=this._getCssClass(n[t]);for(u=0;u<this.model.gridSettings.columns.length;u++)if(this.model.gridSettings.columns[u].format)for(r=0;r<n.length;r++)n[r].dateModified=new Date(n[r].dateModified);var f=this.model.gridSettings.columns[0];this._changeActiveSortedoption(f.headerText,!0);this._sorting(f.field,!0,n);this._fileExplorer[this._currentPath]=n;this._itemStatus&&this._itemStatus.html(this._sorteditems.length+" "+(this._sorteditems.length==1?this._getLocalizedLabels("Item"):this._getLocalizedLabels("Items")));this._treetag.hasClass("e-treeview")||(this._renderTreeView(this._sorteditems),this._updateOnNodeSelection=!0);this.model.layout=="grid"?this._renderGridView(this._sorteditems):this._renderTileView(this._sorteditems);this._usePreviousValues()}},_getCssClass:function(n){var t=n.name.substr(n.name.lastIndexOf(".")+1).toLowerCase();return n.isFile?/\.(bmp|dib|jpg|jpeg|jpe|jfif|gif|tif|tiff|png|ico)$/i.test(n.name)?"e-fe-images":/\.(mp3|wav|aac|ogg|wma|aif|fla|m4a)$/i.test(n.name)?"e-fe-audio":/\.(webm|mkv|flv|vob|ogv|ogg|avi|wmv|mp4|3gp)$/i.test(n.name)?"e-fe-video":/\.(css|exe|html|js|msi|pdf|pptx|ppt|rar|zip|txt|docx|doc|xlsx|xls|xml|rtf|php)$/i.test(n.name)?"e-fe-"+t:"e-fe-unknown e-fe-"+t:n.permission&&!n.permission.Read?"e-fe-folder e-fe-lock":"e-fe-folder"},_bytesToSize:function(n){var t,i;return n==0?"0 Byte":(t=parseInt(Math.floor(Math.log(n)/Math.log(1024))),i=n/Math.pow(1024,t),i.toFixed(2)+" "+["Bytes","KB","MB","GB","TB"][t])},_setPath:function(n){this.model.path=n;this._treetag.remove();this._treetag=t.buildTag("div#"+this._ExplorerId+"_treeView");this._splittag.find(".e-cont1 > .e-tree-wrapper").append(this._treetag);this._fileExplorer=this._updateImages={};this._selectedStates=[];this._selectedItems=[];this._selectedTileItems=[];this._initUpdate=!1;this._initPath="";this._currentPath=this.model.path.replace(/\\/g,"/");this._originalPath=this._rootPath=this._currentPath=this._currentPath.endsWith("/")?this._currentPath:this._currentPath+"/";this._read()},_getPath:function(){return this.model.path},_changeSkin:function(n){this.element.removeClass(this._customCssClass).addClass(n);this._customCssClass=n;this._waitingPopup.option("cssClass",n);this._subControlsSetModel("cssClass",n)},_draggableOption:function(n){this._treeDragEvents(n);this._tileDragEvents(n);this._gridDragEvents(n);n=="_off"&&(this._previousPath=null);this._statusbar&&this[n](this._statusbar,"dragover",this._preventDropOption);this._toolBarItems&&this[n](this._toolBarItems,"dragover",this._preventDropOption)},_swapWith:function(t,i){t=n(t);i=n(i);var r=n("<span>").hide();t.before(r);i.before(t);r.replaceWith(i)},_enableRTL:function(n){var r=this._splittag.find(".e-cont1"),u=this._splittag.find(".e-cont2"),i,t;if(this.model.enableRTL=n,n){for(r.index()<u.index()&&this._swapWith(r,u),i=JSON.parse(JSON.stringify(this.model.gridSettings.columns)),this._oldFormat||(this._oldFormat=JSON.parse(JSON.stringify(i))),t=0;t<i.length;t++)i[t].textAlign=i[t].headerTextAlign="right";this._gridObj&&this._gridObj.columns(i);this.element.addClass("e-rtl");this.element.find(".e-scroller").addClass("e-rtl")}else{if(r.index()>u.index()&&this._swapWith(u,r),this._gridObj&&this._oldFormat){for(t=0;t<this._oldFormat.length;t++)this._oldFormat[t].textAlign||(this._oldFormat[t].textAlign="left"),this._oldFormat[t].headerTextAlign||(this._oldFormat[t].headerTextAlign="left");this._gridObj.columns(this._oldFormat)}this._oldFormat&&(this.model.gridSettings.columns=JSON.parse(JSON.stringify(this._oldFormat)),this._oldFormat=null);this.element.removeClass("e-rtl");this.element.find(".e-scroller").removeClass("e-rtl");this._gridtag&&this._gridtag.removeClass("e-rtl")}this._splitterCorrection();this._subControlsSetModel("enableRTL",n)},_roundedCorner:function(n){var t=n?"addClass":"removeClass";this.element[t]("e-corner-all");this._treeContextMenutag&&this._treeContextMenutag[t]("e-corner");this._tileContextMenutag&&this._tileContextMenutag[t]("e-corner");this._subControlsSetModel("showRoundedCorner",n)},_destroy:function(){this._toolBarObj&&this._toolBarObj.destroy();this._treeContextMenutag&&this._treeContextMenutag.parent().remove();this._tileContextMenutag&&this._tileContextMenutag.parent().remove();this._newFolderDialogObj&&this._newFolderDialogObj.isOpen()&&this._removeDialog(this._newFolderDialogObj);this._renameDialogObj&&this._renameDialogObj.isOpen()&&this._removeDialog(this._renameDialogObj);this._openDialogObj&&this._openDialogObj.isOpen()&&this._removeDialog(this._openDialogObj);this._detailsDialogObj&&this._detailsDialogObj.isOpen()&&this._removeDialog(this._detailsDialogObj);this._alertDialogObj&&this._alertDialogObj.isOpen()&&this._removeDialog(this._alertDialogObj);this._alertWindowObj&&this._alertWindowObj.isOpen()&&this._removeDialog(this._alertWindowObj);this._splitButtonObj&&this._splitButtonObj.destroy();this._splitButtonObj1&&this._splitButtonObj1.destroy();this._waitingPopup&&this._waitingPopup.destroy();this._gridObj&&this._gridObj.element.ejWaitingPopup("destroy");this._unwireEvents();this.element.html("");n(this._cloneElement).attr("style")?this.element.attr("style",n(this._cloneElement).attr("style")):this.element.removeAttr("style");this.element.removeClass("e-widget e-box e-rtl e-ie8");this.element.removeAttr("role");delete this._prevsortingoption;this._prevsorting},_createFolder:function(r){var u=this,f={data:{ActionType:"CreateFolder",Name:r,Path:this._originalPath,SelectedItems:this._getSelectedItemDetails(this._getFolderPath(this._originalPath),this._treeObj.getText(this._selectedNode))},url:this.model.ajaxAction,type:"POST",success:function(n){var r,e,f,o;if(n!==i&&n!==null){if(n.hasOwnProperty("d")&&(n=n.d),!t.isNullOrUndefined(n.error)){u._showErrorDialog(n.error);return}r=u._selectedNode;e=[{id:n.files[0].name,name:n.files[0].name,spriteCssClass:n.files[0].permission&&!n.files[0].permission.Read?"e-fe-icon e-fe-folder e-fe-lock":"e-fe-icon e-fe-folder",hasChild:!1}];u._treetag.ejTreeView("isExpanded",r)||u._treeObj&&u._treeObj.expandNode(r);u._nodeExpanded=!0;u._treeObj.addNode(e,r);u._nodeExpanded=!1;u._update=!0;u._treeObj.selectNode(r);u._refreshTreeScroller();u._treetag.find("li").removeAttr("tabindex");f=u._treetag.find("li div a").not(".e-js");u.model.allowDragAndDrop&&f.length&&(u._drag(f),f.addClass("e-file-draggable"));o={data:n,selectedItems:u._selectedItemDetails};u._trigger("createFolder",o);u._setSelectedItems([n.files[0].name])}},successAfter:this.model.ajaxSettings.createFolder.success};this.model.ajaxSettings.createFolder.success=i;n.extend(!0,f,this.model.ajaxSettings.createFolder);this._sendAjaxRequest(f)},_needToScroll:function(n,i){var f,e=i=="Tree"?n.find("a").eq(0).outerHeight():n.outerHeight()+(this.model.layout=="grid"?0:10),r=0,u;if(r+=n.position().top+e,i=="Tree"){while(n.parent().hasClass("e-treeview-ul"))n=n.parent(),r+=n.position().top;f=this._splittag.find(".e-cont1").height()}else this.model.layout==t.FileExplorer.layoutType.Grid?(f=this._gridtag.find(".e-gridcontent").height(),r=r-this._gridtag.find(".e-gridheader").outerHeight()):f=this._tileViewWrapper.height();u=n.closest(".e-scroller .e-content",".e-fileexplorer");f<r?u.animate({scrollTop:u.scrollTop()+e*2},500,"linear"):r<n.outerHeight()&&u.animate({scrollTop:u.scrollTop()-e*2},500,"linear")},_cut_copy:function(n){var i=this,r,u,e,f,o;if(this._sourcePath==this._currentPath&&this._option=="move"){this.element.find(".e-blur").removeClass("e-blur");this.model.layout!="grid"&&this._clearTileCheckBoxSelection();this._setSelectedItems(this._fileName);n&&(this._activeSource=this.model.selectedItems);return}for(r=this._fileExplorer[this._currentPath],u=typeof this._copiedNodes=="string"?[this._copiedNodes]:this._copiedNodes,this._pastedFiles=u.slice(),e=0;e<u.length;e++)if(!this._isNameExist(this._suggestionItems.length?this._suggestionItems:this._fileExplorer[this._sourcePath],u[e]))return;f=i._currentPath;o=function(){if(i._currentPath=f,(t.isNullOrUndefined(r)||r=="")&&(r=i._fileExplorer[f]),i._existingItems=[],i._sourcePath!=f&&r.length)i._getDuplicateItems(i._sourcePath,f,typeof i._fileName=="string"?[i._fileName]:i._fileName),i._existingItems.length?i._createReplaceConformationDiaolg("_pasteOperation","PasteReplaceAlert"):i._pasteOperation();else{for(var n=0;n<u.length;n++)i._pastedFiles[n]=i._getDuplicateName(i._fileExplorer[f],u[n]);i._pasteOperation()}};t.isNullOrUndefined(r)||r==""?this._getFileDetails(this._currentPath,"","",o):o()},_createReplaceConformationDiaolg:function(i,r,u){var f=this,s,e=0,h,p;(r=="PasteReplaceAlert"||r=="UploadReplaceAlert")&&this._getLocalizedLabels("ReplaceAlert")!="ReplaceAlert"?r="ReplaceAlert":s=i=="_customUpload"?"UploadError":i=="pasteOperation"?"PasteError":i=="_rename"?"RenameError":"Error";h=this.model.rootFolderName.length>0?String.format(this._getLocalizedLabels(r),this._changeName(this._existingItems[e].Path),!1):String.format(this._getLocalizedLabels(r),this._existingItems[e].Path);var c=t.buildTag("div.e-get-name"),w=t.buildTag("div.e-fe-dialog-label",h),l=t.buildTag("div.e-fe-dialog-btn e-replace"),o=t.buildTag("button.e-fe-btn-yes ",this._getLocalizedLabels("YesButton")),a=t.buildTag("button.e-fe-btn-yes e-all ",this._getLocalizedLabels("YesToAllButton")),v=t.buildTag("button.e-fe-btn-no ",this._getLocalizedLabels("NoButton")),y=t.buildTag("button.e-fe-btn-no e-all ",this._getLocalizedLabels("NoToAllButton"));o.ejButton({type:"button",cssClass:"e-flat",click:function(){/\.(gif|jpg|jpeg|tiff|png|bmp)$/i.test(f._existingItems[e].Name)&&(f._updateImages[f._existingItems[e].Path]=(new Date).getTime());f._existingItems[e].IsReplace=!0;e++;e<f._existingItems.length?f._alertDialog.find(".e-fe-dialog-label").text(String.format(f._getLocalizedLabels(r),f._existingItems[e].Path)):f._destroyReplaceConformationDiaolg(i);u&&f._trigger("beforeUploadDialogOpen",u)}});v.ejButton({type:"button",cssClass:"e-flat",click:function(){f._existingItems[e].IsReplace=!1;e++;e<f._existingItems.length?f._alertDialog.find(".e-fe-dialog-label").text(String.format(f._getLocalizedLabels(r),f._existingItems[e].Path)):f._destroyReplaceConformationDiaolg(i)}});a.ejButton({type:"button",cssClass:"e-flat",click:function(){for(var n=e;n<f._existingItems.length;n++)/\.(gif|jpg|jpeg|tiff|png|bmp)$/i.test(f._existingItems[n].Name)&&(f._updateImages[f._existingItems[n].Path]=(new Date).getTime()),f._existingItems[n].IsReplace=!0;f._destroyReplaceConformationDiaolg(i);u&&f._trigger("beforeUploadDialogOpen",u)}});y.ejButton({cssClass:"e-flat",type:"button",click:function(){for(var n=e;n<f._existingItems.length;n++)f._existingItems[n].IsReplace=!1;f._destroyReplaceConformationDiaolg(i)}});l.append(o,a,v,y);n(c).append(w,l);p=function(){o.focus()};this._alertDialog=this._createDialog(c,{width:500,height:"auto",title:this._getLocalizedLabels(s),open:p});this._alertDialogObj=this._alertDialog.data("ejDialog")},_destroyReplaceConformationDiaolg:function(n){this._removeDialog(this._alertDialogObj);this[n]()},_pasteOperation:function(){var i,u,r,o,e,c;for(this._removeBlurEffect(),i=this,u=-1,typeof this._fileName=="string"&&(this._fileName=[this._fileName]),r=0;r<this._fileName.length;r++)if(o=this._sourcePath+this._fileName[r]+"/",this._currentPath.indexOf(o)>=0){u=r;break}if(u!=-1){var l=this._getLocalizedLabels("CancelPasteAction"),s=t.buildTag("div"),a=t.buildTag("div.e-fe-dialog-label",l),h=t.buildTag("div.e-fe-dialog-centerbtn"),f=t.buildTag("button.e-fe-btn-cancel ",this._getLocalizedLabels("CancelButton"));f.ejButton({cssClass:"e-flat",type:"button",click:function(){i._removeDialog(i._alertDialogObj)}});e=t.buildTag("button.e-fe-btn-skip ",this._getLocalizedLabels("SkipButton"));e.ejButton({cssClass:"e-flat",type:"button",click:function(){i._fileName.splice(u,1);i._removeDialog(i._alertDialogObj);i._performPasteOperation()}});h.append(e,f);n(s).append(a,h);c=function(){f.focus()};this._alertDialog=this._createDialog(s,{width:400,height:"auto",title:this._getLocalizedLabels("Error"),open:c});this._alertDialogObj=this._alertDialog.data("ejDialog")}else this._performPasteOperation()},_performPasteOperation:function(){var r=this,u=this._currentPath.split("/"),f={data:{ActionType:"Paste",LocationFrom:this._sourcePath,LocationTo:this._currentPath,Names:typeof this._fileName=="string"?[this._fileName]:this._fileName,Action:this._option,CommonFiles:this._existingItems,SelectedItems:this._getSelectedItemDetails(this._sourcePath,this._fileName),TargetFolder:this._getSelectedItemDetails(this._getFolderPath(),u[u.length-2])},url:this.model.ajaxAction,type:"POST",success:function(i){var o,s,f,e,h,c;if(!t.isNullOrUndefined(i)&&(i.hasOwnProperty("d")&&(i=i.d),!t.isNullOrUndefined(i.error))){r._showErrorDialog(i.error);return}for(typeof r._fileName=="string"&&(r._fileName=[r._fileName]),f=0;f<r._pastedFiles.length;f++)/\.(bmp|dib|jpg|jpeg|jpe|jfif|gif|tif|tiff|png|ico)$/i.test(r._pastedFiles[f])&&(r._updateImages[r._currentPath+r._pastedFiles[f]]=(new Date).getTime());if(r._existingItems&&r._existingItems.filter(function(t){if(t.IsReplace==!1){var i=n.inArray(t.Name,r._pastedFiles);i>-1&&r._pastedFiles.splice(i,1)}}),r._existingItems=[],o=r._originalPath,s=r._selectedNode,r._option=="move"){for(f=0;f<r._fileName.length;f++)r._modifySelectedStates(r._sourcePath+r._fileName[f],"");if(r._fileName.length>0)if(r._fileName.length==1)r._sourceType=="Directory"&&r._treeObj.removeNode(r._refreshNode);else for(u=r._refreshNode.siblings(),r._sourceType=="Directory"&&r._treeObj.removeNode(r._refreshNode),f=0;f<r._fileName.length;f++)for(e=0;e<u.length;e++)r._fileName[f]==n(u[e]).text()&&r._treeObj.removeNode(u[e])}n.each(r._fileExplorer,function(n){n.startsWith(o)&&(r._fileExplorer[n]="")});r._currentPath=r._originalPath;r._highlightedNodes=r._pastedFiles;r._option=="move"?r._refreshItems(s,o,function(){r._fileExplorer[r._sourcePath]="";r._getFileDetails(r._sourcePath)}):r._refreshItems(s,o);h=r._getFolderPath();r._option=="move"&&(r._fileName="",r._option=null,r._toolBarItems&&r._toolBarItems.ejToolbar("disableItemByID",r._ExplorerId+"Paste"),r.model.showContextMenu&&(r._viewMenuObj.disableItem(r._menuPaste),r._treeMenuObj.disableItem(r._menuPaste)));c={name:r._fileName,targetPath:r.model.path,selectedItems:r._selectedItemDetails,targetFolder:r._getSelectedItemDetails(h,r._selectedContent)};r._trigger("paste",c)},successAfter:this.model.ajaxSettings.paste.success};this.model.ajaxSettings.paste.success=i;n.extend(!0,f,this.model.ajaxSettings.paste);this._sendAjaxRequest(f)},_deletion:function(r,u){var f=this,o=this._treeObj.getSelectedNode(),e;this._treeObj.isExpanded(o)||this._treeObj&&this._treeObj.expandNode(o);e={data:{ActionType:"Remove",Names:typeof r=="string"?[r]:r,Path:u,SelectedItems:this._getSelectedItemDetails(u,r)},traditional:!0,url:this.model.ajaxAction,type:"POST",success:function(i){var o,s,h;if(!t.isNullOrUndefined(i)&&(i.hasOwnProperty("d")&&(i=i.d),!t.isNullOrUndefined(i.error))){f._showErrorDialog(i.error);return}for(f._selectedItemsTag&&f._selectedItemsTag.html(""),f._fileExplorer[f._ajax_folderPath]="",n.each(f._fileExplorer,function(n){n.startsWith(f._ajax_folderPath)&&(f._fileExplorer[n]="")}),o=0;o<e.data.SelectedItems.length;o++)e.data.SelectedItems[o].isFile==!1&&f._modifySelectedStates(u+e.data.SelectedItems[o].name,"");f._currentPath=f._ajax_folderPath;s=f._selectedContent==f._selectedTreeText?f._parentNode:f._selectedNode;f._refreshItems(s,f._ajax_folderPath);f._treeObj.getSelectedNode().length==0?f._treeObj.selectNode(f._parentNode):f.model.layout=="grid"?f._gridtag.find(".e-gridcontent").click():f._tileViewWrapper.click();h={data:i,path:f._ajax_folderPath,name:r,selectedItems:f._selectedItemDetails};f._trigger("remove",h)},successAfter:this.model.ajaxSettings.remove.success};this.model.ajaxSettings.remove.success=i;n.extend(!0,e,this.model.ajaxSettings.remove);this._sendAjaxRequest(e)},_rename:function(){var r=this,u={data:{ActionType:"Rename",Path:r._currentPath,Name:r._selectedContent,NewName:r._ajax_person,CommonFiles:r._existingItems,SelectedItems:this._getSelectedItemDetails(r._currentPath,this._selectedContent)},url:this.model.ajaxAction,type:"POST",success:function(i){if(!t.isNullOrUndefined(i)&&(i.hasOwnProperty("d")&&(i=i.d),!t.isNullOrUndefined(i.error))){r._showErrorDialog(i.error);return}r._existingItems=[];r._fileExplorer[r._currentPath]="";r._nodeType!="File"&&r._modifySelectedStates(r._currentPath+r._selectedContent,r._currentPath+r._ajax_person);r._splittag.find(".e-cont2 .e-active").length<=0?(n.each(r._fileExplorer,function(n){n.startsWith(r._currentPath+r._selectedContent+"/")&&(r._fileExplorer[n]="")}),r._selectedNode.find("> div > .e-text")[0].lastChild.nodeValue=r._selectedContent=r._ajax_person,r._originalPath=r._currentPath+=r._ajax_person+"/",r._updateAddressBar()):(r.model.selectedItems.length>0?(r.model.selectedItems[r.model.selectedItems.length-1]=r._getOriginalName(r._ajax_person),r._selectedItems[0]=r._ajax_person,r._highlightedNodes=r.model.selectedItems,r._refreshItems(r._selectedNode,r._currentPath)):r._refreshItems(r._parentNode,r._currentPath),r._treeObj.getSelectedNode().length==0&&n(r._parentNode).find("li a").each(function(){if(n(this).text()==r._ajax_person)return r._treeObj.selectNode(n(this).closest("li.e-item")),!1}))},successAfter:this.model.ajaxSettings.rename.success};this.model.ajaxSettings.rename.success=i;n.extend(!0,u,this.model.ajaxSettings.rename);this._sendAjaxRequest(u)},_downloadFile:function(){var i=this._getSelectedItemDetails(this._currentPath,this._selectedItems),r={Path:this._pathCorrection(this._currentPath),ActionType:"Download",SelectedItems:JSON.stringify(i),Names:this._selectedItems},u=this.model.ajaxAction+"?"+n.param(r,!0),t;if(this.model.ajaxSettings.download.url&&(u=this.model.ajaxSettings.download.url.indexOf("{")>=0?String.format(this.model.ajaxSettings.download.url,"?"+n.param(r,!0)):this.model.ajaxSettings.download.url),t={path:this._pathCorrection(this._currentPath),files:this._selectedItems,selectedItems:i,url:u},this._trigger("beforeDownload",t))return!1;window.location=t.url},_removeBlurEffect:function(){this._currntNode&&(this._currntNode.hasClass("e-active")&&this._currntNode.removeClass("e-blur"),this._currntNode.find(".e-active").length&&this._currntNode.find(".e-active").removeClass("e-blur"))},_renderTreeView:function(t){var e=t.length>0?!0:!1,i,r,u,f;for(this._collapse=!1,i=this,r=this._currentPath.split("/"),u=0;u<r.length-2;u++)this._initPath+=r[u]+"/";f=r[r.length-2];this._localData=[{id:1,name:f?f:this._currentPath,spriteCssClass:this._hasReadPermission(this._currentPath)?"e-fe-icon e-fe-folder":"e-fe-icon e-fe-folder e-fe-lock",hasChild:e}];this._treetag._collapse=!1;this._treetag.ejTreeView({loadOnDemand:!0,cssClass:i.model.cssClass,enableRTL:i.model.enableRTL,allowKeyboardNavigation:i.model.allowKeyboardNavigation,fields:{dataSource:i._localData,id:"id",parentId:"pid",text:"name",hasChild:"hasChild",spriteCssClass:"spriteCssClass"},nodeCollapse:function(n){i._refreshTreeScroller(n)},nodeExpand:function(n){i._refreshTreeScroller(n)},nodeClick:function(n){i._treenodeClicked(n)},beforeExpand:function(n){i._treeNodeBeforeExpand(n)},nodeSelect:function(n){i._updateTreePath(n)},beforeCollapse:function(n){i._onBeforeCollapse(n)},beforeDelete:function(n){i._treeBeforeDelete(n)}});this._treeObj=this._treetag.data("ejTreeView");this._rootFolderName=this._treeObj.element.find("li:first > div > .e-text").text();this._changeRootFolderName();this._treeViewEvents("_off");this._treeViewEvents("_on");this._isClicked=!1;this._treetag.ejTreeView("selectNode",n(this._treetag).find("li")[0]);this._isClicked=!0;this._treeScroll=this._treetag.parent(".e-tree-wrapper").ejScroller({height:this._splittag.height(),width:parseInt(this._splittag.find(".e-cont1").width()),buttonSize:0,scrollerSize:this._scrollerSize,thumbStart:function(n){i._onThumbStart(n)}}).data("ejScroller");this._addChild(t)},_treeViewEvents:function(n){this[n](this._treetag,"focus",this._focusTreeView);this[n](this._treetag,"blur",this._blurTreeView);this.model.allowDragAndDrop&&this._treeDragEvents(n)},_treeDragEvents:function(n){var t=this._treetag.parent(".e-tree-wrapper");t&&(this[n](t,"dragover",this._onDragOverHandler),this[n](t,"drop",this._onDropHandler),this[n](t,"dragleave",this._onDragLeave))},_focusTreeView:function(){this._treetag.hasClass("e-focus")||(this._treetag.addClass("e-focus"),this._itemList=[],this._on(this._treetag,"keydown",this._OnKeyUp),this._hidePopup())},_blurTreeView:function(){this._treetag.removeClass("e-focus");this._off(this._treetag,"keydown",this._OnKeyUp)},_treeBeforeDelete:function(n){if(!t.isNullOrUndefined(n.event)){var i=this._getKeyCode(n.event);i==46&&(n.cancel=!0)}},_treeNodeBeforeExpand:function(n){if(!this._nodeExpanded){var t=this._updatePath(n.currentElement,n.value);this._fileExplorer[t]||this._getFileDetails(t,n.currentElement);this._treetag.ejTreeView("hasChildNode",n.currentElement)||this._fileExplorer[t]&&this._addChild(this._fileExplorer[t],n.currentElement)}},_treenodeClicked:function(t){if(t.event.currentTarget&&t.event.currentTarget.focus(),n(t.currentElement).hasClass("e-text")&&t.currentElement!=this._selectedNode.find("> div > .e-text")[0]){this._selectedContent=t.currentElement.text;this._selectedNode=n(t.currentElement).closest("li.e-item");var i=n(this._selectedNode.parents("li.e-item")[0]);this._parentNode=i.length!=0?i:this._selectedNode;this._nodeType="Directory";this._isTreeNode=!0}},_showHideContextMenu:function(){if(this.model.showContextMenu){var i={beforeOpen:"",click:""},r={id:"",targetId:""};this._treeMenuOptions=this._initContextMenuOptions("navbar");r.id=this._ExplorerId+"_treeView";r.targetId="#"+this._ExplorerId+"_treeView";i.beforeOpen=n.proxy(this._beforeOpenContextMenu,this);i.open=n.proxy(this._contextMenuOpen,this);i.close=n.proxy(this._onHideContextMenu,this);i.click=n.proxy(this._contextMenuClick,this);this._treeContextMenutag=this._createContextMenuTag(this._treeMenuOptions,r,i);this._treeMenuObj=this._treeContextMenutag.ejMenu("instance");this._cwdMenuOptions=this._initContextMenuOptions("cwd");this._addSortbyoptions();r.id=this._ExplorerId+"_tileView";r.targetId="#"+this._ExplorerId+"_tileWrapper,#"+this._ExplorerId+"_grid";i.beforeOpen=n.proxy(this._beforeOpenTileContextMenu,this);i.open=n.proxy(this._contextMenuOpen,this);i.click=n.proxy(this._fileContextMenuClick,this);this._tileContextMenutag=this._createContextMenuTag(this._cwdMenuOptions,r,i);this._viewMenuObj=this._tileContextMenutag.ejMenu("instance");(t.isNullOrUndefined(this._fileName)||this._fileName=="")&&this._treeMenuObj.disableItem(this._menuPaste);this._fileMenuOptions=this._initContextMenuOptions("files")}else this._treeContextMenutag&&this._tileContextMenutag&&(this._treeMenuObj.destroy(),this._viewMenuObj.destroy(),n("#"+this._ExplorerId+"_treeViewContextMenu").remove(),n("#"+this._ExplorerId+"_tileViewContextMenu").remove(),this._viewMenuObj=this._treeMenuObj=null)},_addSortbyoptions:function(){var u=this.model.contextMenuSettings.items.cwd.length,r=this.model.contextMenuSettings.items.cwd,t,n,i;if(r.indexOf("SortBy")>-1)for(t=this.model.gridSettings.columns.length,n=0;n<t;n++)i="e-fileexplorer-toolbar-icon "+this.model.gridSettings.columns[n].field,this._cwdMenuOptions.push({id:this._ExplorerId+"_cwd_"+this.model.gridSettings.columns[n].field,text:this.model.gridSettings.columns[n].headerText,parentId:this._ExplorerId+"_cwd_SortBy",sprite:i})},enableMenuItem:function(n){n=this._findCommand(n,this.model.contextMenuSettings.items,!0);for(var t=0;t<this._restrictedMenuOption.length;t++)if(this._restrictedMenuOption[t]==n){this._restrictedMenuOption.splice(t,1);break}this._treeMenuObj&&this._treeMenuObj.enableItem(n);this._viewMenuObj&&this._viewMenuObj.enableItem(n)},disableMenuItem:function(t){t=this._findCommand(t,this.model.contextMenuSettings.items,!0);n.inArray(t,this._restrictedMenuOption)==-1&&this._restrictedMenuOption.push(t);this._treeMenuObj&&this._treeMenuObj.disableItem(t);this._viewMenuObj&&this._viewMenuObj.disableItem(t)},_renderGridView:function(i){for(var r,f,s,o,u=JSON.parse(JSON.stringify(this.model.gridSettings.columns)),h=null,e=0;e<u.length;e++)u[e].template||u[e].format||(u[e].template="<script type='text/x-jsrender'><span title= '{{:"+u[e].field+"}}'>{{:"+u[e].field+"}}<\/span><\/script>");this._suggestionItems.length&&u.push({field:"filterPath",headerText:this._getLocalizedLabels("Folder"),width:"20%"});u.unshift({field:"cssClass",headerText:"",cssClass:"e-grid-image",width:22,template:"<script type='text/x-jsrender'><span class='e-fe-icon {{:cssClass}}' unselectable='on'><\/span><\/script>",textAlign:t.TextAlign.Center,allowResizing:!1});this.model.showCheckbox&&u.unshift({field:"",headerText:"check",cssClass:"e-col-check",width:22,template:"<script type='text/x-jsrender'><input type='checkbox' class='e-grid-row-checkbox'/><\/script>",textAlign:t.TextAlign.Center,headerTextAlign:t.TextAlign.Center,allowResizing:!1,allowSorting:!1});r=this;f=u[u.length-1];f&&(f.customAttributes?f.customAttributes["class"]?f.customAttributes["class"].search("e-rowcell e-last-rowcell")==-1&&(f.customAttributes["class"]=f.customAttributes["class"]+" e-rowcell e-last-rowcell"):f.customAttributes["class"]=" e-rowcell e-last-rowcell":f.customAttributes={"class":"e-rowcell e-last-rowcell"});this.model.virtualItemCount>0?(this._enableVirtualization=!0,this._allowVirtualScrolling=!0,this._virtualScrollMode="normal"):(this._enableVirtualization=!1,this._allowVirtualScrolling=!1);this._gridObj?(u.length!=this._gridObj.model.columns.length&&(this._gridObj.model.columns=u,this._gridObj.columns(this._gridObj.model.columns)),h=JSON.parse(JSON.stringify(this._gridObj.model.sortSettings)),this._gridObj.option({dataSource:i,enableRTL:this.model.enableRTL,sortSettings:h})):(this._gridtag.ejGrid({cssClass:r.model.cssClass,enableRTL:r.model.enableRTL,allowKeyboardNavigation:this.model.allowKeyboardNavigation,dataSource:i,selectionType:r.model.allowMultiSelection?"multiple":"single",allowSorting:r.model.gridSettings.allowSorting,columns:u,isResponsive:!0,scrollSettings:{width:186,height:200,buttonSize:0,scrollerSize:r._scrollerSize,allowVirtualScrolling:this._allowVirtualScrolling,enableVirtualization:this._enableVirtualization,virtualScrollMode:this._virtualScrollMode},allowScrolling:!0,enableResponsiveRow:!1,rowSelected:function(n){r._updatePathFromGrid(n)},recordDoubleClick:function(n){r._openAction(n)},create:this.model.showCheckbox?function(n){r._gridCheckboxState(n)}:null,actionBegin:function(n){r._gridActionBegin(n)},actionComplete:function(n){r._gridActionComplete(n)},allowResizing:r.model.gridSettings.allowResizing}),this.model.allowMultiSelection||this._gridtag.find(".e-headercelldiv>span.e-chkbox-wrap").hide());i.length&&i[0].filterPath&&this._setFilteredContent();this.model.templateRefresh&&this._gridtag.ejGrid({templateRefresh:function(n){r._templateRefresh(n)}});this._gridObj=this._gridtag.ejGrid("instance");s=this._getFilePermission(this._currentPath);s&&!s.Read?this._gridObj.getContentTable().find(".emptyrecord").html(this._getLocalizedLabels("ProtectedFolder")).addClass("e-fe-center"):this._gridObj.model.dataSource.length==0&&this._searchbar&&n.trim(this._searchbar.val())?this._gridObj.getContentTable().find(".emptyrecord").html(this._getLocalizedLabels("EmptyResult")).addClass("e-fe-center"):this._gridObj.model.dataSource.length==0&&this._gridObj.getContentTable().find(".emptyrecord").html(this._getLocalizedLabels("EmptyFolder")).addClass("e-fe-center");this.gridItems=this._gridObj.getRows();this.model.allowDragAndDrop&&(this._gridtag.children(".e-gridcontent").addClass("e-droppable"),this._drag(n(this.gridItems)),n(this.gridItems).addClass("e-file-draggable"));this._reSizeHandler();o=this._gridObj.getScrollObject();o&&(o.isVScroll()||o.isVScroll())&&o.element.ejScroller({thumbStart:function(n){r._onThumbStart(n)}});this.model.virtualItemCount>0&&o.element.ejScroller({thumbStart:function(n){r._onCheckThumbStart(n)},wheelStart:function(n){r._onCheckThumbStart(n)}});this._gridtag.attr("tabindex",-1);this._gridViewEvents("_off");this._gridViewEvents("_on");this._gridupdate(this._prevsortingoption)},_onCheckThumbStart:function(n){this._gridCheckboxState(n)},_setFilteredContent:function(){for(var t,r=this._gridtag.find(".e-gridcontent tr"),i=0;i<r.length;i++)t=n(r[i]).find("td:last"),n(r[i]).attr("data-parent-path",t.text()),this.model.rootFolderName.length>0?t.attr("title",this._changeName(t.text(),!1)):t.attr("title",t.text()),t.text("/"+t.text().replace(this._rootPath,""))},_templateRefresh:function(n){this._trigger("templateRefresh",n)},_gridActionBegin:function(n){var i,t;if(n.requestType=="sorting"){for(n.cancel=!0,i=this.model.gridSettings.columns.length,t=0;t<i;t++)if(n.columnName==this.model.gridSettings.columns[t].field){this._changeActiveSortedoption(this.model.gridSettings.columns[t].headerText);this._sorting(this._prevsortingoption,this._prevsorting);this._sortingActioncomplete();break}this._suggestionItems.length&&n.columnName=="filterPath"&&(this.removeSortingIcons(),this._suggestionitemsSorting("filterPath",this._prevsorting),this._sortingActioncomplete());this._suggestionItems.length&&this._setFilteredContent()}},_gridActionComplete:function(n){this.model.showCheckbox&&this._gridCheckboxState(n);t.isNullOrUndefined(this._gridObj)||this._gridupdate(this._prevsortingoption)},_gridCheckboxState:function(){var n=this;this._gridtag.find(".e-headercelldiv:first").html(" <input type='checkbox' id='headchk' />").addClass("e-col-check");this._headCheckObj=this._gridtag.find("#headchk").ejCheckBox({showRoundedCorner:n.model.showRoundedCorner,change:function(t){n._headCheckChange(t)}}).data("ejCheckBox");this._gridtag.find(".e-grid-row-checkbox").ejCheckBox({showRoundedCorner:n.model.showRoundedCorner});this._gridtag.find(".e-chkbox-wrap").removeAttr("tabindex")},_checkChange:function(t){var r,e;if(t.isInteraction||t.isInteraction==i){var o=this._gridtag.find(".e-grid-row-checkbox"),f=this._gridtag.find(" .e-gridcontent .e-checkbox:checked"),u=[];for(r=0;r<f.length;r++)u.push(n(f[r]).closest("tr").index());for(t&&!t.type&&(e=n.inArray(t.index(),u),t.find(".e-chk-act").length==1?u.splice(e,1):u.push(t.index())),this._changeCheckState=!1,this._gridObj.clearSelection(),u.length==o.length?this._gridtag.find("#headchk").ejCheckBox({checked:!0}):this._gridtag.find("#headchk").ejCheckBox({checked:!1}),this._isClicked=!1,r=0;r<u.length;r++)!0&&(this._gridObj.multiSelectCtrlRequest=!0,this._gridObj.selectRows(u[r]));this._isClicked=!0;this.model.checked==!1&&this._gridtag.find("#headchk").ejCheckBox({checked:!1});this._changeCheckState=!0}},_recordClick:function(){for(var r,i=this._gridtag.find(".e-grid-row-checkbox"),t=0;t<i.length;t++)n.inArray(t,this._gridObj.selectedRowsIndexes)<0?(n(i[t]).ejCheckBox({checked:!1}),this._gridtag.find("#headchk").ejCheckBox({checked:!1}),this._gridObj.multiSelectCtrlRequest||this._unselectEvent()):(n(i[t]).ejCheckBox({checked:!0}),r=this._gridtag.find(".e-grid-row-checkbox:checked"),r.length==i.length&&this._gridtag.find("#headchk").ejCheckBox({checked:!0}))},_headCheckChange:function(n){if(n.isInteraction){var i=this,t=this._gridtag.find(".e-grid-row-checkbox");t.ejCheckBox({change:function(n){i._checkChange(n)}});this._gridtag.find("#headchk").is(":checked")?(t.ejCheckBox({checked:!0}),this._gridObj.selectRows(0,t.length)):(t.ejCheckBox({checked:!1}),this._setSelectedItems([]),this._unselectEvent())}},_unselectEvent:function(){var t=this._getSelectedItemDetails(this.model.selectedFolder,this._filteredItemsName),r,f,o,u,e;if((this._filteredItemsName=[],!this._gridObj||!this._gridObj.multiSelectCtrlRequest)&&t.length>0){for(this._unselectedItems=[],r=0;r<t.length;r++)f=t[r].name,this._unselectedItems.push(f);o=n.inArray(i,this.model.selectedItems);n.each(this._fileExplorer[this._originalPath],function(n,t){u=t.isFile?"File":"Directory"});e={name:this._unselectedItems[this._unselectedItems.length-1],names:this._unselectedItems,path:this.model.selectedFolder,nodeType:u,unselectedItem:t[t.length-1],unselectedItems:t};this._trigger("unselect",e)}},_gridViewEvents:function(n){this[n](this._gridtag.find(".e-gridcontent"),"focusin",this._focusGridView);this[n](this._gridtag.find(".e-gridcontent"),"focusout",this._blurGridView);this[n](this._gridtag.find(".e-gridcontent"),"mousedown",this._mouseSelection);this.model.allowDragAndDrop&&this._gridDragEvents(n)},_gridDragEvents:function(n){var t=this._gridtag.children(".e-gridcontent");t&&(this[n](t,"dragover",this._onDragOverHandler),this[n](t,"drop",this._onDropHandler),this[n](t,"dragleave",this._onDragLeave),this[n](this._gridtag.find(".e-gridheader"),"dragover",this._preventDropOption))},_focusGridView:function(){this._gridtag.find(".e-gridcontent").hasClass("e-focus")||(this._gridtag.find(".e-gridcontent").addClass("e-focus"),this._itemList=[],this._on(this._gridtag.find(".e-gridcontent"),"keydown",this._OnKeyUp),this._hidePopup())},_blurGridView:function(){this._gridtag.find(".e-gridcontent").removeClass("e-focus");this._off(this._gridtag.find(".e-gridcontent"),"keydown",this._OnKeyUp)},_setThumbImageHeight:function(){var i=this._perRow=1,t,f,r,u;if(this.items){for(t=0;t<this.items.length-1;t++)if(this.items[t].getBoundingClientRect().top==this.items[t+1].getBoundingClientRect().top)i++;else break;if(!(i==null||i<2))for(t=0,f=this.items.length;t<f;t+=i)r=0,u=this.items.slice(t,t+i),u.each(function(){var t=parseInt(n(this).find(".e-thumb-image").outerHeight());t>r&&(r=t)}),u.find(".e-thumb-image.e-image").css("height",r);this._tileScroll&&this._tileScroll.refresh()}this._perRow=i},_renderTileView:function(t,i,r){var f=[],u=this,v=i,c=this._getFilePermission(this._currentPath),l,h,o,a,s,e;if(c&&!c.Read?this._tileView.html(this._getLocalizedLabels("ProtectedFolder")).addClass("e-fe-center"):this._searchbar&&n.trim(this._searchbar.val())&&t.length==0?this._tileView.html(this._getLocalizedLabels("EmptyResult")).addClass("e-fe-center"):t.length==0?this._tileView.html(this._getLocalizedLabels("EmptyFolder")).addClass("e-fe-center"):this._tileView.html("").removeClass("e-fe-center"),this.model.virtualItemCount>0)if(this._renamedStatus)f=this._virtualScrollRename(t);else if(this._diffFolder=!1,r!=!0&&(this._tileView.children()&&this._tileView.children().remove()&&this._tileView.removeClass("e-tileview"),this._tileView.addClass("e-tileview").attr("role","tileview"),this._scrollListCount=0,this._priviousScrollListCount=0,this._count=0),this._previousFolder==""?(this._previousFolder=this._currentPath,this._diffFolder=!0):this._previousFolder!=this._currentPath&&(this._tileView.html("").removeClass("e-fe-center"),this._previousFolder=this._currentPath,this._diffFolder=!0,this._scrollListCount=0,this._priviousScrollListCount=0,this._count=0),this._virtualCount?(l=t.length-this.model.virtualItemCount,this._count=this._virtualCount>l?this._virtualCount-this.model.virtualItemCount:this._virtualCount,this._scrollListCount=this._count+this.model.virtualItemCount,this._virtualCount=0):this._count=this._scrollListCount==this.model.virtualItemCount?0:this._priviousScrollListCount,this._scrollListCount==0&&(this._scrollListCount=this.model.virtualItemCount),t.length>this.model.virtualItemCount)for(h=0,this._count;this._count<this._scrollListCount;this._count++)f[h]=t[this._count],h++;else f=t;else this._tileView.children()&&this._tileView.children().remove()&&this._tileView.removeClass("e-tileview"),this._tileView.addClass("e-tileview").attr("role","tileview"),f=t;if(n.each(f,function(t,i){var f,s,o,r,h,e,c,l,a,y,p,w,nt,d,b,g,k;i&&(f=document.createElement("li"),f.className+="e-tilenode",i.filterPath&&f.setAttribute("data-parent-path",i.filterPath),s=document.createElement("div"),s.className+="e-align",o=document.createElement("div"),o.className+="e-thumb-image",o.className+=" e-image",o.setAttribute("unselectable","on"),s.appendChild(o),r=document.createElement("span"),r.className+="e-thumbImage",r.className+=" e-fe-icon",r.setAttribute("unselectable","on"),i.isFile?(f.appendChild(s),nt=i.name.substr(i.name.lastIndexOf(".")+1).toLowerCase(),d="e-fe-"+nt,/\.(bmp|dib|jpg|jpeg|jpe|jfif|gif|tif|tiff|png|ico)$/i.test(i.name)?u.model.showThumbnail?(b=i.filterPath?i.filterPath.replace("~","..")+i.name:v?u._originalPath.replace("~","..")+i.name:u._currentPath.replace("~","..")+i.name,g=u._getImage(b,i.name,u.model.enableThumbnailCompress),r=document.createElement("img"),r.className+="e-thumbImage",r.setAttribute("unselectable","on"),r.setAttribute("src",g?g:b+"?"+(u._updateImages[b]?u._updateImages[b]:u._initialTime))):r.className+=" e-fe-images":/\.(mp3|wav|aac|ogg|wma|aif|fla|m4a)$/i.test(i.name)?r.className+=" e-fe-audio":/\.(webm|mkv|flv|vob|ogv|ogg|avi|wmv|mp4|3gp)$/i.test(i.name)?r.className+=" e-fe-video":/\.(css|exe|html|js|msi|pdf|pptx|ppt|rar|zip|txt|docx|doc|xlsx|xls|xml|rtf|php)$/i.test(i.name)?r.className+=" "+d:(r.className+=" e-fe-unknown",r.className+=" "+d)):(f.appendChild(s),f.className+=" e-folder",r.className+=" e-fe-folder",i.permission&&!i.permission.Read&&(r.className+=" e-fe-lock")),o.appendChild(r),h=document.createElement("div"),h.className+="e-name-wrap",h.setAttribute("unselectable","on"),e=document.createElement("div"),e.className+="e-name",e.className+=" e-name-in-wrap",e.setAttribute("unselectable","on"),l=document.createElement("span"),l.className+="e-file-info",l.setAttribute("title",i.name),l.innerHTML=i.name,c=document.createElement("div"),c.className+="e-file-name",c.setAttribute("unselectable","on"),c.appendChild(l),e.appendChild(c),i.isFile&&u.model.layout=="tile"&&(a=document.createElement("div"),a.className+="e-file-type",a.setAttribute("unselectable","on"),y=document.createElement("span"),y.className+="e-file-info",y.setAttribute("unselectable","on"),y.innerHTML=i.type,a.appendChild(y),p=document.createElement("div"),p.className+="e-file-size",p.setAttribute("unselectable","on"),w=document.createElement("span"),w.className+="e-file-info",w.setAttribute("unselectable","on"),w.innerHTML=i.size,p.appendChild(w),e.appendChild(a),e.appendChild(p)),h.appendChild(e),f.setAttribute("aria-selected",!1),i.dateModified?f.setAttribute("title",i.isFile?i.dateModified+" ("+i.size+")":i.dateModified):i.isFile&&f.setAttribute("title",i.size),f.setAttribute("role","tileitem"),u.model.showCheckbox&&(k=document.createElement("input"),k.className+="e-tile-checkbox",k.setAttribute("type","checkbox"),n(k).ejCheckBox({size:"mini",showRoundedCorner:u.model.showRoundedCorner}),f.insertBefore(k.parentNode,f.firstChild)),f.appendChild(h),u._tileView[0].appendChild(f),u._tileView.find(".e-chkbox-wrap").removeAttr("tabindex"))}),this._diffFolder&&this._addVirtualHeight(this,t),u._scrollEvent(t,u),u._tileView.find(".e-image > img").length)for(o=u._tileView.find(".e-image > img"),a=0,s=0;s<o.length;s++)e=new Image,e.onload=e.onabort=e.onerror=function(n){if(n){++a==o.length&&u._setThumbImageHeight();var t={path:n.target.src,element:n.target,originalArgs:n,action:"thumbnailPreview"};u._trigger("getImage",t)}},e.src=n(o[s]).attr("src");else u._setThumbImageHeight()},_virtualScrollRename:function(n){for(var r,e,u,o=this._virtualScrollRenamedItem[0],f=[],t,i=0;i<n.length;i++)n[i].name==o&&this.model.layout=="tile"&&(t=i,this._count=t,r=n.length-t,r>=10?(this._count=t,this._scrollListCount=this._count+10):(e=10-r,this._count=this._count-e,this._scrollListCount=this._count+10));for(u=0,this._count;this._count<=this._scrollListCount;this._count++)f[u]=n[this._count],u++;return this._updatevirtualscrollerheight(t,n),this._tileView.children().remove(),this._renamedStatus=!1,f},_scrollEvent:function(n,i){this._activeItem=0;this.items=this._tileView.children("li.e-tilenode");this.model.allowDragAndDrop&&(this._tileViewWrapper.addClass("e-droppable"),this._drag(this.items),this.items.addClass("e-file-draggable"));this._tileViewEvents("_off");this._tileViewEvents("_on");this._tileContent.addClass("e-content");var r=this.model.showFooter?this._splittag.outerHeight()-this._statusbar.outerHeight():this._splittag.outerHeight(),u=parseInt(this._splittag.find(".e-cont2").width());!this.model.virtualItemCount>0?t.isNullOrUndefined(this._tileScroll)?this._tileScroll=this._tileContent.parent(".e-tile-wrapper").ejScroller({height:r,width:u,buttonSize:0,scrollerSize:this._scrollerSize,thumbStart:function(n){i._onThumbStart(n)}}).data("ejScroller"):(this._tileScroll.option({height:r,width:u}),this._tileScroll&&this._tileScroll.refresh()):this._tileScroll=this._tileContent.parent(".e-tile-wrapper").ejScroller({height:r,width:u,buttonSize:0,scrollerSize:this._scrollerSize,scroll:function(t){i._onScroll(n,t)},thumbStart:function(n){i._onThumbStart(n)},thumbEnd:function(t){i._onMaxScroll(n,t)}}).data("ejScroller")},_addVirtualHeight:function(t,i){var f,r,u;(t._tileView[0].parentElement.querySelector(".virtualBottom")||t._tileView[0].parentElement.querySelector(".virtualTop"))&&(n(t._tileView.parent()).find(".virtualTop").remove(),n(t._tileView.parent()).find(".virtualBottom").remove(),t._tileScroll.model.scrollTop="0");f=t._calculateLiElement(t,i);r=document.createElement("div");r.className="virtualBottom";n(r).css({height:f+"px",display:"block"});n(r).insertAfter(t._tileView[0]);u=document.createElement("div");u.className="virtualTop";n(u).css({height:"0px",display:"block"});n(u).insertBefore(t._tileView[0])},_calculateLiElement:function(n,t){var i,r,u;return n.model.layout=="tile"?(i=n._tileView[0].querySelectorAll("li"),i[0]&&(r=i[0].offsetHeight),u=t.length/2,n._totalHeight=u*r):n.model.layout=="largeicons"&&(i=n._tileView[0].querySelectorAll("li"),i[0]&&(r=i[0].offsetHeight),u=t.length/5,n._totalHeight=u*r),n._totalHeight},_updatevirtualscrollerheight:function(n,t){this._tileScroll.model.scrollTop=Math.ceil(n/(t.length/this._totalHeight));var i=this._totalHeight-this._tileScroll.model.scrollTop;this._tileView[0].parentElement.querySelector(".virtualTop").style.height=this._tileScroll.model.scrollTop+"px";i=i<0?0:i;this._tileView[0].parentElement.querySelector(".virtualBottom").style.height=i+"px"},_updateVirtualContentHeight:function(n,t){var i=this._totalHeight-n.scrollTop;t&&(this._tileView[0].parentElement.querySelector(".virtualTop").style.height=i<0?n.scrollTop+i+"px":n.scrollTop+"px");i=i<0?0:i;this._tileView[0].parentElement.querySelector(".virtualBottom")&&(this._tileView[0].parentElement.querySelector(".virtualBottom").style.height=i+"px")},_onMaxScroll:function(n,t){this._updateVirtualContentHeight(t.model,!0);this._priviousScrollListCount=this._scrollListCount;this._scrollListCount+=this.model.virtualItemCount;this._vScrollTop=t.scrollData.sTop;this._virtualCount=Math.ceil(n.length/t.scrollData.scrollable*t.scrollData.sTop);this._renderTileView(n,null)},_onScroll:function(n,t){if(t.source!="thumb"){var i=this;i._scrollLoad=!1;window.setTimeout(function(){i._scrollLoad||(i._scrollLoad=!0,t.source=="key"&&t.source?i._onMaxScroll(n,t):(i._updateVirtualContentHeight(t,!0),i._priviousScrollListCount=i._scrollListCount,i._scrollListCount+=i.model.virtualItemCount,t.scrollData&&(i._vScrollTop=t.scrollData.sTop,i._virtualCount=Math.ceil(n.length/t.scrollData.scrollable*t.scrollData.sTop)),i._renderTileView(n,null),i._vScrollTop=t.scrollTop))},0)}},_getImage:function(n,t,i){var f,r,u;return(n=n.replace("..","~"),f=this._getSelectedItemDetails(this._currentPath,t),r={path:n,canCompress:i,size:i?{Height:104,Width:116}:null,selectedItems:f},r.path=this._pathCorrection(r.path),this._trigger("beforeGetImage",r))?"":((this._currentPath.indexOf(":")==1||this._currentPath.indexOf("ftp:")==0||this.model.ajaxSettings.getImage.url||this._currentPath.startsWith("//")&&this.model.path=="/")&&(u=this.model.ajaxAction+"?Path="+r.path+"&ActionType=GetImage&CanCompress="+r.canCompress+"&Size="+JSON.stringify(r.size)+"&SelectedItems="+JSON.stringify(r.selectedItems),this.model.ajaxSettings.getImage.url&&(u=this.model.ajaxSettings.getImage.url.indexOf("{")>=0?String.format(this.model.ajaxSettings.getImage.url,"?CanCompress="+r.canCompress+"&Size="+JSON.stringify(r.size)+"&Path="+r.path+"&SelectedItems="+JSON.stringify(r.selectedItems)):this.model.ajaxSettings.getImage.url)),u?u:i?this.model.ajaxAction+"?ActionType=GetImage&CanCompress="+r.canCompress+"&Size="+JSON.stringify(r.size)+"&Path="+r.path+"&SelectedItems="+JSON.stringify(r.selectedItems):"")},_gridtagClick:function(t){t.stopPropagation();(n(t.target).hasClass("e-gridcontent")||n(t.target).hasClass("e-content")||n(t.target).hasClass("e-table"))&&this._selectedRows&&this._selectedRows.length==0&&(this._addFocus(this._gridtag.find(".e-gridcontent")),this.model.showCheckbox&&(this._gridtag.find(".e-grid-row-checkbox").ejCheckBox({checked:!1}),this._gridtag.find("#headchk").ejCheckBox({checked:!1})),this._gridObj.clearSelection(),this._unselectEvent(),this._updateCurrentPathPermission(),this._activeSource&&this._activeSource.length&&this._setSelectedItems(this._activeSource));this._activeSource=null},_updateGridSelection:function(t){t.events&&!t.events.ctrlKey&&n(t.target)[0]!=null&&this._gridObj.selectRows(n(t.target).closest("td").parent().index(),null,n(t.target).closest("td"))},_updateTileSelection:function(t){t.events&&!t.events.ctrlKey&&n(t.target)[0]!=null&&this._triggerClick(t.target)},_tileViewEvents:function(t){this[t](this.items,"mousedown",this._preventDefaultSelection);this[t](this._tileViewWrapper,"mousedown",this._mouseSelection);this[t](this.items,this._isDevice&&n.isFunction(n.fn.tap)?"tap":"click",this._upDatePathFromTileView);this[t](this.items,this._isDevice&&n.isFunction(n.fn.tap)?"doubletap":"dblclick",this._openAction);this[t](this.items,"mouseenter",this._onItemHover);this[t](this.items,"mouseleave",this._onItemLeave);this[t](this._tileViewWrapper,"focusin",this._focusTileView);this[t](this._tileViewWrapper,"focusout",this._blurTileView);this.model.allowDragAndDrop&&this._tileDragEvents(t)},_tileDragEvents:function(n){this._tileViewWrapper&&(this[n](this._tileViewWrapper,"dragover",this._onDragOverHandler),this[n](this._tileViewWrapper,"drop",this._onDropHandler),this[n](this._tileViewWrapper,"dragleave",this._onDragLeave))},_preventDefaultSelection:function(n){n.shiftKey&&n.preventDefault()},_tileViewWrapperClick:function(t){(n(t.target).hasClass("e-tile-wrapper")||n(t.target).hasClass("e-tile-content")||n(t.target).hasClass("e-tileview"))&&(this._lastItemIndex=this._lastItemIndex?this._lastItemIndex:this._itemList&&this._itemList.length>0?this._itemList.filter(".e-active").index():-1,this._addFocus(this._tileViewWrapper),this.items.hasClass("e-active")&&this.items.removeClass("e-active").attr("aria-selected",!1),this._updateCurrentPathPermission(),this.model.showCheckbox&&this._clearTileCheckBoxSelection(),this._hidePopup(),this._activeSource&&this._activeSource.length&&this._setSelectedItems(this._activeSource),this._unselectEvent());this._activeSource=null},_onItemHover:function(t){var i=t.currentTarget,r=t.target;n(i).hasClass("e-disable")||(this.items.removeClass("e-hover"),n(i).addClass("e-hover"))},_onItemLeave:function(t){var i=t.currentTarget,r=t.target;n(i).hasClass("e-disable")||n(i).removeClass("e-hover")},_focusTileView:function(){this._tileViewWrapper.hasClass("e-focus")||(this._tileViewWrapper.addClass("e-focus"),this._itemList=this.items,this._on(this._tileViewWrapper,"keydown",this._OnKeyUp),this._on(this._tileViewWrapper,"keydown",this._OnKeyDown))},_blurTileView:function(){this._tileViewWrapper.removeClass("e-focus");this._off(this._tileViewWrapper,"keydown",this._OnKeyUp);this._off(this._tileViewWrapper,"keydown",this._OnKeyDown)},_OnKeyDown:function(t){var i=this._itemList.length-1,r;if(this._activeItem=this._lastItemIndex?this._lastItemIndex:this._itemList?this._itemList.filter(".e-active").index():-1,r=this._getKeyCode(t),this.model.allowKeyboardNavigation){switch(r){case 38:if(t.preventDefault(),n(t.target).hasClass("e-statusbar")){this._focusLayout(this.model.layout);return}if(this._activeItem<this._perRow)return;this._activeItem-=this._perRow;this._beforeListHover(t);break;case 37:if(t.preventDefault(),this._activeItem==0)return;this._activeItem<0||this._activeItem==null||this._activeItem>i?this._activeItem=0:this._activeItem==0?this._activeItem=i:this._activeItem-=1;this._beforeListHover(t);break;case 40:if(t.preventDefault(),this._activeItem==this._itemList.length-1)return;if(this._activeItem+this._perRow>=this._itemList.length){if(this.items[this._activeItem].getBoundingClientRect().top==this.items[this._itemList.length-1].getBoundingClientRect().top)return;this._activeItem=this._itemList.length-1}else this._activeItem+=this._perRow;this._beforeListHover(t);break;case 39:if(t.preventDefault(),this._activeItem==i)return;this._activeItem>i||this._activeItem==null||this._activeItem<0?this._activeItem=i:this._activeItem==i?this._activeItem=0:this._activeItem+=1;this._beforeListHover(t);break;case 33:case 36:t.preventDefault();this._activeItem=0;this._beforeListHover(t);break;case 34:case 35:t.preventDefault();this._activeItem=i;this._beforeListHover(t)}this._lastItemIndex=t.shiftKey?this._activeItem:null}},_OnKeyUp:function(i){var o=this._getKeyCode(i),f,u,s,r,e;if(this.model.allowKeyboardNavigation&&!this._KeydownEventHandler(i))switch(o){case 13:i.preventDefault();i.altKey?(this._lastFocusedElement=i.currentTarget,this._getDetails()):(i.preventDefault(),this._lastFocusedElement=i.currentTarget,n(i.currentTarget).hasClass("e-treeview")||(f=!1,n(i.currentTarget).hasClass("e-tile-wrapper")?f=this.items.hasClass("e-active"):n(i.currentTarget).hasClass("e-gridcontent")&&(f=n(this.gridItems).find("td").hasClass("e-active")),f&&this._selectedContent&&this._openAction()));break;case 86:i.ctrlKey&&(i.preventDefault(),!t.isNullOrUndefined(this._option)&&this._selectedContent&&(this._currentPath!=this._originalPath&&(this._currentPath=this._originalPath),this._lastFocusedElement=i.currentTarget,this._cut_copy()));break;case 46:case 67:case 68:case 88:case 113:if(i.preventDefault(),this._lastFocusedElement=i.currentTarget,f=!1,n(i.currentTarget).hasClass("e-treeview")?this._rootPath!=this._currentPath&&(f=this._treetag.find(".e-active").length>0?!0:!1):n(i.currentTarget).hasClass("e-tile-wrapper")?f=this.items.hasClass("e-active"):n(i.currentTarget).hasClass("e-gridcontent")&&(f=n(this.gridItems).find("td").hasClass("e-active")),f&&this._selectedContent&&this._toRead&&(o==67&&this._toCopy&&i.ctrlKey&&this._copyMoveNode("copy"),this._toEdit))switch(o){case 46:this._deleteFolder();break;case 68:i.ctrlKey&&this._deleteFolder();break;case 88:i.ctrlKey&&this._copyMoveNode("move");break;case 113:this._renameFolder()}break;case 93:case 121:if((i.shiftKey||o==93)&&this.model.showContextMenu)if(i.preventDefault(),this._lastFocusedElement=i.currentTarget,u=null,n(i.currentTarget).hasClass("e-treeview"))r=this._treeObj.getSelectedNode().find(".e-active"),u=this._getMenuPosition(r),this._treeMenuObj.show(u.left,u.top,r,i,!0);else{if(n(i.currentTarget).hasClass("e-tile-wrapper")){for(e=0;e<this.items.length;e++)if(r=n(this.items[e]),r.text()==this._selectedContent){u=this._getMenuPosition(r);s=r;break}}else if(n(i.currentTarget).hasClass("e-gridcontent"))for(e=0;e<this.gridItems.length;e++)if(r=n(this.gridItems[e]).find("td.e-grid-image").next(),r.text()==this._selectedContent){u=this._getMenuPosition(r);s=r;break}u==null&&(u=n(i.currentTarget).offset(),s=n(i.currentTarget));this._viewMenuObj.show(u.left,u.top,s,i,!0)}}},_KeydownEventHandler:function(n){var t={keyCode:n.keyCode,altKey:n.altKey,shiftKey:n.shiftKey,ctrlKey:n.ctrlKey,originalArgs:n};return this._trigger("keydown",t)},_getMenuPosition:function(n){var i=n.outerHeight()/2,r=n.outerWidth()/2,t=n.offset();return{top:t.top+i,left:t.left+r}},_beforeListHover:function(i){var r=this._getActiveItem(),u;r.hasClass("e-disable")||(n(i.target).hasClass("e-statusbar")?(this.model.layout=r.hasClass("e-switchGridView")?t.FileExplorer.layoutType.Grid:t.FileExplorer.layoutType.LargeIcons,this._switchLayoutView(this.model.layout),this._addFocus(this._statusbar)):(u={keyCode:91,shiftKey:i.shiftKey,innerEvent:!0,currentTarget:r[0],target:r[0]},this._upDatePathFromTileView(u)))},_getActiveItem:function(){return n(n(this._itemList)[this._activeItem])},_getURL:function(){var n=this.model.ajaxAction+"?Path="+this._pathCorrection(this._currentPath)+"&ActionType=Upload"+(this._selectedContent?"&SelectedItems="+JSON.stringify(this._getSelectedItemDetails(this._getFolderPath(),[this._selectedContent])):"");return this.model.ajaxSettings.upload.url&&(n=this.model.ajaxSettings.upload.url.indexOf("{")>=0?String.format(this.model.ajaxSettings.upload.url,"?Path="+this._pathCorrection(this._currentPath)+(this._selectedContent?"&SelectedItems="+JSON.stringify(this._getSelectedItemDetails(this._getFolderPath(),[this._selectedContent])):"")):this.model.ajaxSettings.upload.url),n},_renderUploadBox:function(){var n=this,i=this._getURL(),r=n.model.uploadSettings.dialogAction.content==null?n.element:n.model.uploadSettings.dialogAction.content;this._uploadtag.ejUploadbox({cssClass:this.model.cssClass,enableRTL:this.model.enableRTL,height:"0px",width:"0px",uploadName:"FileUpload",autoUpload:this.model.uploadSettings.autoUpload,showFileDetails:this.model.uploadSettings.showFileDetails,dialogText:{title:n._getLocalizedLabels("Upload")},dialogAction:{modal:this.model.uploadSettings.dialogAction.modal,content:r,closeOnComplete:this.model.uploadSettings.dialogAction.closeOnComplete,drag:this.model.uploadSettings.dialogAction.drag},dialogPosition:this.model.uploadSettings.dialogPosition,showRoundedCorner:this.model.showRoundedCorner,extensionsAllow:this.model.fileTypes=="*.*"?"":this.model.fileTypes.replace(/\*/g,""),multipleFilesSelection:this.model.uploadSettings.allowMultipleFile,fileSize:this.model.uploadSettings.maxFileSize,buttonText:{browse:"Upload file"},saveUrl:i,removeUrl:this.model.ajaxAction+"?Path="+this._currentPath+"&ActionType=Remove",locale:t.Uploadbox.Locale[this.model.locale]?this.model.locale:"en-US",success:function(t){n._trigger("uploadSuccess",t)},beforeSend:function(t){n._trigger("beforeUploadSend",t)},complete:function(t){n._uploadSuccess(t)},remove:function(t){n._uploadSuccess(t)},fileSelect:function(t){var f,r,i,u;for(n._fileExplorer[n._currentPath]||n._getFileDetails(n._currentPath),f=n._fileExplorer[n._currentPath],n._existingItems=[],r=n._files=t.files,i=0;i<r.length;i++)for(u=0;u<f.length;u++)if(r[i].name==f[u].name){n._existingItems.push({Name:r[i].name,Path:n._currentPath+r[i].name,IsReplace:!0});break}n._existingItems.length?(n._createReplaceConformationDiaolg("_customUpload","UploadReplaceAlert",t),t.cancel=!0):n._trigger("beforeUploadDialogOpen",t)},error:function(i){n._alertDialog&&n._alertDialog.is(":visible")||(n._alertDialog=n._createDialog(t.buildTag("div.e-fe-dialog-label",i.error?i.error:i.responseText),{width:400,height:"auto",title:n._getLocalizedLabels("Error")}),n._alertDialogObj=n._alertDialog.data("ejDialog"));n._trigger("uploadError",i)},begin:function(t){var i=n._currentPath.split("/"),r={path:n._currentPath,selectedItems:n._getSelectedItemDetails(n._getFolderPath(),i[i.length-2]),uploadItemDetails:t.files,url:n._getURL()};n._trigger("beforeUpload",r)&&(t.cancel=!0);n._uploadtag.ejUploadbox({saveUrl:r.url})},cancel:function(){n._usePreviousValues()}});this._uploadObj=this._uploadtag.ejUploadbox("instance")},_usePreviousValues:function(){this._previousPath&&(this._currentPath=this._previousPath,this._selectedContent=this._previousSelectedContent,this._previousPath=null)},_isRestrictedUpload:function(n,t,i){var r=null,u;if(n.find(".e-fe-lock").length){if(r=i?i:this._getFolderPath(this._updatePath(n,t)),this._fileExplorer[r]&&this._fileExplorer[r].length)for(u=0;u<this._fileExplorer[r].length;u++)if(this._fileExplorer[r][u].name==t&&this._fileExplorer[r][u].permission)return!this._fileExplorer[r][u].permission.Upload;return!1}return!1},_onDragOverHandler:function(i){var e="",o="",r=n(i.target),u=r.hasClass("e-js")||r.hasClass("e-tileview")?"":r.text(),f,s;i.originalEvent.dataTransfer.dropEffect="copy";i.stopPropagation();i.preventDefault();r.hasClass("e-file-droppable")||(e=this._splittag.find(".e-file-droppable"),e&&e.removeClass("e-file-droppable"),n(i.currentTarget).hasClass("e-tree-wrapper")?!t.isNullOrUndefined(r)&&r.is("A")?(this._droppableElement=r,this._droppableElement.length&&this._droppableElement.addClass("e-file-droppable"),f=r.closest("li.e-item"),f.find(".e-icon").length&&!this._treeObj.isExpanded(f)&&(this._expandTimer=window.setTimeout(function(){this._treeObj&&this._treeObj.expandNode(f)},800))):i.originalEvent.dataTransfer.dropEffect="none":(this.model.layout=="grid"?(this._droppableElement=r.closest("tr","table.e-table"),this._droppableElement.length?(this._droppableElementData=this._gridObj.model.currentViewData[this._gridObj.getIndexByRow(this._droppableElement)],this._droppableElementData.isFile&&(this._gridtag.find(".e-gridcontent").addClass("e-file-droppable"),this._droppableElement=null)):this._gridtag.find(".e-gridcontent").addClass("e-file-droppable")):(this._droppableElement=r.closest("li",".e-tileview").has(".e-fe-folder"),this._droppableElement.length||this._tileViewWrapper.addClass("e-file-droppable")),this._droppableElement&&this._droppableElement.length&&this._droppableElement.addClass("e-file-droppable"),o=this._getHoverTreeElementPath(r),this._droppableElement&&this._droppableElement.length&&(u=this.model.layout=="grid"?this._droppableElementData.name:this._droppableElement.find(".e-file-name").text())));this._droppableElement&&this._droppableElement.length&&this._isRestrictedUpload(this._droppableElement,u?u:i.target.textContent,o)&&(i.originalEvent.dataTransfer.dropEffect="none");s={target:this._droppableElement&&this._droppableElement.length?this._droppableElement:r,targetElementName:u,targetPath:this._getHoverTreeElementPath(r)+u};this._trigger("drag",s)},_getHoverTreeElementPath:function(n){return n.hasClass("e-text")?this._updatePath(n,n.text()):this._originalPath},_onDropHandler:function(t){var r,i;if(t.originalEvent.dataTransfer.files&&t.originalEvent.dataTransfer.files.length){if(t.stopPropagation(),t.preventDefault(),this._expandTimer!=null&&(window.clearTimeout(this._expandTimer),this._expandTimer=null),this.element.find(".e-file-droppable").removeClass("e-file-droppable"),r=this._uploadtag.ejUploadbox("instance"),this._droppableElement&&this._droppableElement.length)this._previousPath=this._currentPath,this._previousSelectedContent=this._selectedContent,n(t.currentTarget).hasClass("e-tree-wrapper")?(this._selectedContent=t.target.text,this._currentPath=this._updatePath(n(t.target),this._selectedContent)):(this._selectedContent=this.model.layout=="grid"?this._droppableElementData.name:n(t.target).closest("li","e-tileview").find(".e-file-name").text(),this._currentPath=this._originalPath+this._selectedContent+"/");else{if(this._droppableElement&&!this._droppableElement.length&&this._isRestrictedUpload(this._selectedNode,this._selectedTreeText,this._getFolderPath(this._originalPath)))return t.originalEvent.dataTransfer.dropEffect="none",null;this._currentPath=this._originalPath;this._selectedContent=this._selectedTreeText}(i={dropAction:"upload",fileInfo:t.originalEvent.dataTransfer.files,target:this._droppableElement&&this._droppableElement.length?this._droppableElement:n(t.currentTarget),targetPath:this._currentPath,targetElementName:this._selectedContent},this._trigger("dragStop",i))||(r._onDropHandler(t),i={dropAction:"upload",fileInfo:t.originalEvent.dataTransfer.files,target:this._droppableElement&&this._droppableElement.length?this._droppableElement:n(t.currentTarget),targetPath:this._currentPath,targetFolder:this._selectedContent},this._trigger("drop",i))}},_onDragLeave:function(t){this._expandTimer!=null&&(window.clearTimeout(this._expandTimer),this._expandTimer=null);(this._gridtag.find(".e-gridcontent").hasClass("e-file-droppable")||n(t.target).hasClass("e-tile-wrapper")||n(t.target).hasClass("e-gridcontent")||n(t.target).closest(".e-fileexplorer .e-tile-wrapper")||n(t.target).closest(".e-fileexplorer .e-gridcontent"))&&(this.model.layout=="grid"?this._gridtag.find(".e-gridcontent").removeClass("e-file-droppable"):this._tileViewWrapper.removeClass("e-file-droppable"))},_customUpload:function(){for(var t,n=0;n<this._existingItems.length;n++)if(!this._existingItems[n].IsReplace)for(t=0;t<this._files.length;t++)if(this._files[t].name==this._existingItems[n].Name){this._files.splice(t,1);this._uploadObj._currentElement.find(".e-uploadinput").val("");this._uploadObj._resetFileInput(this._uploadObj._currentElement.find(".e-uploadinput"));break}this._files.length!=0&&this._uploadtag.ejUploadbox({pushFile:this._files})},_uploadSuccess:function(n){for(var r=this._currentPath,i=[],t=0;t<n.success;t++)i.push(n.success[t].name);this.element.find(".e-dialog.e-js .e-action-perform").remove();this._fileExplorer[this._currentPath]="";this._treeObj.selectNode(this._selectedNode);r==this._currentPath&&this._setSelectedItems(i);this._trigger("uploadComplete",n)},_preventDrag:function(){var t=this._treetag.find("li div a");t.removeClass("e-file-draggable");this.gridItems&&this.gridItems.length&&n(this.gridItems).removeClass("e-file-draggable");this.items&&this.items.length&&this.items.removeClass("e-file-draggable")},_allowDrag:function(){var t,r,i,u;this._gridtag.children(".e-gridcontent").addClass("e-droppable");this._tileViewWrapper.addClass("e-droppable");t=this._treetag.find("li div a").not(".e-js");t.length&&this._drag(t);r=n(this.gridItems).not(".e-js");r.length&&this._drag(n(this.gridItems));i=this.items.not(".e-js");i.length&&this._drag(i);u=this._treetag.find("li div a");u.addClass("e-file-draggable");this.gridItems&&this.gridItems.length&&n(this.gridItems).addClass("e-file-draggable");this.items&&this.items.length&&this.items.addClass("e-file-draggable")},_drag:function(i){var f,e,o,h,u,c,s,r=this;i.ejDraggable({dragOnTaphold:!0,clone:!0,dragStart:function(t){var u=!1,i,e;if(r._previousElement=r._selectedNode,n(t.target).hasClass("e-text")?(u=!0,r._copiedNodes=[n(t.target).text()],r._option="move",r._sourcePath=r._getFolderPath(r._getHoverTreeElementPath(n(t.target))),r._sourceType="Directory",r._fileName=n(t.target).text(),r._refreshNode=n(t.target).closest("li")):r.model.selectedItems.length<=1&&(r.model.layout=="grid"?r._gridObj.selectRows(n(t.target).closest("tr").index()):r._triggerClick(t.target)),r.model.selectedItems.length){for(i=0;i<r.model.selectedItems.length;i++)if(t.target&&r._isRestrictedMove(t.element,r.model.selectedItems[i],r.model.selectedFolder))return f.remove(),t.cancel=!0,null}else if(n(t.target).hasClass("e-text")&&(o=t.target.textContent),t.target&&o&&r._isRestrictedMove(t.element,o,""))return f.remove(),t.cancel=!0,null;u||r._copyMoveNode("move");r._previousElement.find("a").eq(0).click();e={target:t.element,targetPath:r._currentPath,selectedItems:r._getSelectedItemDetails(r._sourcePath,r._fileName)};r._trigger("dragStart",e)},drag:function(t){var a,v=u,l,i,y;t.target&&(l=n(t.target).closest(".e-droppable",".e-fileexplorer"),n(t.target).hasClass("e-droppable")?u=n(t.target):l.length?u=l:n(t.target).hasClass("e-text")?u=n(t.target).find(".e-droppable"):r._clearExpand(n(t.target).closest("li.e-item")),document.body.style.cursor="no-drop",e&&(e.style.cursor=h),e=t.target,h=t.target.style.cursor,n(f).css({"margin-left":"10px"}),u&&v&&u.hasClass("e-draggable")?(u.text()!=v.text()&&(i=n(t.target).closest("li.e-item"),r._clearExpand(i),i&&i.find(".e-icon").length&&!r._treeObj.isExpanded(i)&&(r._expandTimer=window.setTimeout(function(){r._treeObj&&r._treeObj.expandNode(i)},800)),clearTimeout(a),a=setTimeout(function(){n(e).hasClass("e-text")?r._needToScroll(n(e).closest("li"),"Tree"):u&&r._needToScroll(n(u),"")},100)),t.target.style.cursor="pointer"):t.target.style.cursor="no-drop",n(e).hasClass("e-text")?(o=e.textContent,s=""):(o=r.model.layout=="grid"?u?u.find("[data-cell='"+r._gridObj.getColumnByField("name").headerText+"']").text():"":u?u.hasClass("e-tile-wrapper")?"":u.text():"",s=r._getHoverTreeElementPath(n(e))),e&&r._isRestrictedMove(u,o,s)?(t.target.style.cursor="no-drop",c=!0):c=!1,s+(o?o+"/":"")!=r._sourcePath&&(t.target.style.cursor="pointer"),y={target:u&&u.length?u:n(t.target),targetElementName:o,targetPath:(s?s:r._originalPath)+o},r._trigger("drag",y))},dragStop:function(i){var o=!1,s;if(o=r.model.layout=="grid"?n(i.target).hasClass("e-gridcontent")||n(i.target).find("table").length&&n(i.target).closest(".e-gridcontent",".e-fileexplorer").length:n(i.target).hasClass("e-tileview")||n(i.target).find(".e-tileview").length&&n(i.target).closest(".e-fileexplorer").length,o&&r._sourcePath==r._originalPath&&(r._activeSource=r.model.selectedItems),o){if(r._setSelectedItems([]),t.isNullOrUndefined(r._copiedNodes)||!i.element.hasClass("e-file-draggable"))return;if(f.hide(),r.element.find(".e-blur").removeClass("e-blur"),r._currentPath==r._sourcePath+r._fileName+"/")return;r._cut_copy();f&&f.remove()}r._expandTimer!=null&&(window.clearTimeout(r._expandTimer),r._expandTimer=null);e&&(e.style.cursor=h);document.body.style.cursor="";r.element.find(".e-blur").removeClass("e-blur");!i.element.dropped&&((n(i.target).hasClass("e-tree-wrapper")||n(i.target).parents(".e-tree-wrapper").length>0)&&!n(i.target).is("a")||!n(i.target).hasClass("e-tile-wrapper")&&!n(i.target).parents(".e-tile-wrapper").length>0&&!n(i.target).hasClass("e-gridcontent")&&!n(i.target).parents(".e-gridcontent").length>0&&!n(i.target).hasClass("e-tree-wrapper")&&!n(i.target).parents(".e-tree-wrapper").length>0)&&f&&f.remove();s={dropAction:"move",fileInfo:r._getSelectedItemDetails(r._sourcePath,r._copiedNodes),target:u&&u.length?u:n(i.target),targetElementName:r._selectedContent,targetPath:r._currentPath};r._trigger("dragStop",s)},helper:function(i){var u,e,s,o,h;if(!t.isNullOrUndefined(i.element)){if(!i.element.hasClass("e-file-draggable"))return i.cancel=!0,null;if(r)return f=t.buildTag("div.e-dragedNode e-fe"),h=t.util.getZindexPartial(r.element),f.css({"z-index":h}),f.addClass(r.model.cssClass+(r.model.enableRTL?" e-rtl":"")),u=n(i.element).clone().addClass("dragClone"),e=u.find("img"),s=e.length?t.buildTag("img.e-thumbImage","","",{src:e.attr("src")}):t.buildTag("span","","",{"class":u.find(".e-fe-icon").attr("class")}),r.model.selectedItems.length>1&&(o=t.buildTag("div.e-count"),o.text(r.model.selectedItems.length),f.append(o)),f.append(s),f.appendTo(n("body"))}}});i.ejDroppable({accept:i,drop:function(i,u){var f,e;c||t.isNullOrUndefined(r._copiedNodes)||!u.draggable.hasClass("e-file-draggable")||(n(u.helper).hide(),f=function(t){var u,f;(t||r._clickTarget(i),r._sourcePath==r._originalPath&&(r._activeSource=r.model.selectedItems),r._currentPath!=r._sourcePath+r._fileName+"/")&&(u=r._getSelectedItemDetails(r._sourcePath,r._fileName),r._cut_copy(!0),f={dropAction:"move",fileInfo:u,target:n(i.dropTarget),targetPath:r._currentPath,targetFolder:r._selectedContent},r._trigger("drop",f))},r.model.layout!="grid"||n(i.dropTarget).hasClass("e-text")?(e=r._getHoverTreeElementPath(n(i.dropTarget)),r._fileExplorer[e]?f():r._getFileDetails(e,"","",f)):(r._gridObj.selectRows(n(i.dropTarget).closest("tr").index()),f(!0)),n("body").find(".e-dragedNode").length>0&&n("body").find(".e-dragedNode").remove())}})},_clickTarget:function(t){if(n(t.dropTarget).hasClass("e-text"))this._treeObj&&this._treeObj.selectNode(t.dropTarget);else if(this.model.layout=="grid"){var i=n(t.dropTarget).closest("tr").index();i>=0?this._gridObj.selectRows(n(t.dropTarget).closest("tr").index()):n(t.dropTarget).click()}else this._triggerClick(t.dropTarget)},_triggerClick:function(t){var i=n(t).closest(".e-tilenode");i.length>0&&this._isDevice&&n.isFunction(n.fn.tap)?n(t).trigger("tap"):n(t).click()},_clearExpand:function(){this._expandTimer!=null&&(window.clearTimeout(this._expandTimer),this._expandTimer=null)},_isRestrictedMove:function(n,t,i){var r=null,u;if(n&&n.find(".e-fe-lock").length){if(r=i?i:this._getFolderPath(this._updatePath(n,t)),this._fileExplorer[r]&&this._fileExplorer[r].length)for(u=0;u<this._fileExplorer[r].length;u++)if(this._fileExplorer[r][u].name==t&&this._fileExplorer[r][u].permission)return!this._fileExplorer[r][u].permission.Copy;return!1}return!1},_createAddressBar:function(){this._addresstag=t.buildTag("input.e-addressBar e-tool-input","",{},{id:this._ExplorerId+"_addressbar",type:"text"});this._addresstag.appendTo(this._toolBarItems.find("#"+this._ExplorerId+"Addressbar").html(""));var i=n("<span class='e-fe-icon e-fe-folder'><\/span>");i.insertBefore(this._addresstag);t.browserInfo().name=="msie"&&t.ieClearRemover(this._addresstag[0]);this._addressBarEvents("_on")},_addressBarEvents:function(t){this[t](n("#"+this._ExplorerId+"_addressbar"),"focus",this._inputFocusin);this[t](n("#"+this._ExplorerId+"_addressbar"),"keydown",this._searchPath);this[t](n("#"+this._ExplorerId+"_addressbar"),"blur",this._addressbarFocusout)},_inputFocusin:function(t){n(t.target).select()},_updateAddressBar:function(){if(this._addresstag){var n=this._currentPath;this.model.rootFolderName.length>0?this._addresstag.val(n.replace(this._initPath,"").replace(this._rootFolderName,this.model.rootFolderName)):this._addresstag.val(n.replace(this._initPath,""))}},_onEpand:function(n){this._splitObj.element.find(".e-cont1").hasClass("collapsed")?this._splitIcon&&this._splitIcon.find(".e-icon").hasClass("e-arrow-sans-left")&&this._splitIcon.find(".e-icon").addClass("e-arrow-sans-right").removeClass("e-arrow-sans-left"):this._splitIcon&&this._splitIcon.find(".e-icon").hasClass("e-arrow-sans-right")&&this._splitIcon.find(".e-icon").addClass("e-arrow-sans-left").removeClass("e-arrow-sans-right");this._reSizeHandler(n)},_createSplitPane:function(){var f=this,i=t.buildTag("div"),n=t.buildTag("div"),e,u;this._splittag=t.buildTag("div#"+this._ExplorerId+"_splitter");i.addClass("e-cont1");n.addClass("e-cont2");this.model.enableRTL?this._splittag.append(n,i):this._splittag.append(i,n);e=this.model.enableRTL?[{},{paneSize:this._isMobileOrTab?"150px":"25%"}]:[{paneSize:this._isMobileOrTab?"150px":"25%"},{}];this.element.append(this._splittag);this._splitObj=this._splittag.ejSplitter({enableAutoResize:!0,animationSpeed:50,width:"100%",cssClass:this.model.cssClass,enableRTL:this.model.enableRTL,allowKeyboardNavigation:this.model.allowKeyboardNavigation,height:this.element.height()-(this._toolBarItems?this._toolBarItems.outerHeight():0),properties:e,expandCollapse:function(n){f._onEpand(n)},resize:function(n){f._reSizeHandler(n)}}).data("ejSplitter");var r=this._splitObj.element.find(".e-splitbar"),o=r.css("border-width"),s=r.css("width");r.css({width:"0px","border-width":"0px"});this._splitObj.refresh();r.css({width:s,"border-width":o});this._renderSplitIcon();u=t.buildTag("div.e-tree-wrapper");i.append(u);this._treetag=t.buildTag("div#"+this._ExplorerId+"_treeView");u.append(this._treetag);this._gridtag=t.buildTag("div#"+this._ExplorerId+"_grid");n.append(this._gridtag);this._tileViewWrapper=t.buildTag("div.e-tile-wrapper#"+this._ExplorerId+"_tileWrapper","","",{tabindex:0});this.model.layout=="tile"&&this._tileViewWrapper.addClass("e-tileInfo-view");n.append(this._tileViewWrapper);this._tileViewWrapper.append("<div class='e-tile-content' > <\/div>");this._tileContent=this._tileViewWrapper.find(".e-tile-content");this._tileView=t.buildTag("ul#"+this._ExplorerId+"_tileView");this._tileContent.append(this._tileView);this._statusbar=t.buildTag("div","","",{tabindex:0});this._statusbar.insertAfter(this._tileView);n.append(this._statusbar);this.model.showFooter&&this._createStatusBar();this._waitingPopup=this._splittag.find(".e-cont2").ejWaitingPopup({showOnInit:!1,cssClass:this.model.cssClass}).data("ejWaitingPopup");this._showHideSplitBar(!1);this._showHideContextMenu();this.model.layout=="grid"?this._tileContent.parent().hide():this._gridtag.hide();this._createUploadBox()},_selectedFolder:function(t){var s=t.replace(this._initPath,""),f=this._treeObj.getSelectedNode(),e=s.split("/"),o=e.filter(function(n){return n!=""}),r,u,i;for(o.length>1&&(this._isClicked=!1),s&&this._treeObj.selectNode(this._treeObj.element.find("li:first")),r=0;r<e.length;r++)if(e[r]!="")for(f=this._treeObj.getSelectedNode(),this._treeObj.isExpanded(f)||this._treeObj&&this._treeObj.expandNode(f),u=f.find("ul:first>li").find("div:first .e-text"),i=0;i<u.length;i++)n(u[i]).text()==e[r]&&(o[o.length-1]==n(u[i]).text()&&(this._isClicked=!0),this._treeObj.selectNode(u[i].parentNode.parentNode));this._isClicked=!0},_setSelectedItems:function(t){var f,o,r,u,i,e,s;if(this._suggestionItems.length&&(f=t,t=this._selectedItems),typeof t=="string"&&(t=[t]),this._removeOldSelectionDetails(),this.model.layout=="grid"){for(this._gridObj.clearSelection(),o=this._gridObj.multiSelectCtrlRequest,this._gridObj.multiSelectCtrlRequest=!0,r=this._gridtag.find(".e-gridcontent tr"),u=0;u<t.length;u++)for(r=n("#"+this._ExplorerId+" .e-gridcontent td:contains("+(f?f[u]:t[u])+")"),i=0;i<r.length;i++)if(this._suggestionItems.length?this._originalPath+t[u]==n(r[i]).closest("tr").attr("data-parent-path")+n(r[i]).text():t[u]==n(r[i]).text()){this._gridObj.selectRows(n(r[i]).closest("tr").index());break}this._recordClick();this._gridObj.multiSelectCtrlRequest=o}else for(this.items.removeClass("e-active").attr("aria-selected",!1),r=this._tileView.find(".e-tilenode"),u=0;u<t.length;u++)for(i=0;i<r.length;i++)if(e=this.model.layout=="tile"?n(r[i]).find(".e-file-name").text():n(r[i]).text(),this._suggestionItems.length?this._originalPath+t[u]==n(r[i]).attr("data-parent-path")+e:t[u]==e){s={keyCode:91,ctrlKey:!0,currentTarget:r[i],target:r[i]};this._upDatePathFromTileView(s);break}},_refreshTreeScroller:function(){t.isNullOrUndefined(this.element)||(this.model.enableRTL&&(this._treeScroll.model.scrollLeft=0),this._treeScroll&&this._treeScroll.refresh())},_createStatusBar:function(){var i,n;this._statusbar.addClass("e-statusbar");this._itemStatus=t.buildTag("div.e-itemStaus");this._selectedItemsTag=t.buildTag("div.e-itemStaus e-selected-items");this._switchBtn=t.buildTag("div.e-switchView");this._statusbar.append(this._switchBtn,this._itemStatus,this._selectedItemsTag);this.model.enableResize&&(this._resizeItem=t.buildTag("div.e-icon e-fe-resize e-resize-handle"),this._resizeItem.insertBefore(this._switchBtn));i=this._splittag.find(".e-cont2").outerHeight()-this._statusbar.outerHeight();this._gridtag.height(i);n=t.buildTag("button.e-switchGridView#"+this._ExplorerId+"_switchGridView","",{},{title:this._getLocalizedLabels("Grid"),tabindex:0});this._switchBtn.append(n);n.ejButton({type:"button",size:"normal",contentType:"imageonly",prefixIcon:"e-icon e-fe-grid"});n=t.buildTag("button.e-swithListView#"+this._ExplorerId+"_swithListView","",{},{title:this._getLocalizedLabels("LargeIcons"),tabindex:0});this._switchBtn.append(n);n.ejButton({type:"button",size:"normal",contentType:"imageonly",prefixIcon:"e-icon e-fe-largeicons"});this.model.layout=="grid"&&this._statusbar.find(".e-switchGridView").addClass("e-active");this.model.layout=="largeicons"&&this._statusbar.find(".e-swithListView").addClass("e-active");this._statusBarEvents("_off");this._statusBarEvents("_on")},_statusBarEvents:function(n){this[n](this._statusbar,"focus",this._focusStatusBar);this[n](this._statusbar.find("button"),"focus",this._focusStatusBarButton);this[n](this._statusbar.find("button"),"blur",this._blurStatusBarButton);this[n](this._statusbar,"blur",this._blurStatusBar);this.model.allowDragAndDrop&&this[n](this._statusbar,"dragover",this._preventDropOption)},_focusStatusBar:function(){this._statusbar.hasClass("e-focus")||(this._statusbar.addClass("e-focus"),this._itemList=this._switchBtn.find("button"),this._on(this._statusbar,"keydown",this._OnKeyDown),this._hidePopup())},_focusStatusBarButton:function(){this._on(this._statusbar.find("button"),"keydown",this._check)},_blurStatusBarButton:function(){this._off(this._statusbar.find("button"),"keydown",this._check)},_blurStatusBar:function(){this._statusbar.removeClass("e-focus");this._off(this._statusbar,"keydown",this._OnKeyDown)},_check:function(){var n;this.model.layout==t.FileExplorer.layoutType.Grid?(this.model.layout=t.FileExplorer.layoutType.LargeIcons,n=!0):this.model.layout==t.FileExplorer.layoutType.LargeIcons&&(this.model.layout=t.FileExplorer.layoutType.Grid,n=!0);n&&this._switchLayoutView()},_refreshResizeHandler:function(){this._setMinMaxSizeInInteger();this.adjustSize();this.model.showFooter&&this.model.enableResize&&this._resizeFileExplorer()},_refreshResizeEventHandler:function(t){var i=n(t.element).parents("div.e-fileexplorer");this.model.height=n(i).outerHeight();this.model.width=n(i).outerWidth();this.element.css({height:this.model.height,width:this.model.width});this.adjustSize()},_convertPercentageToPixel:function(n,t){return Math.round(t*n/100)},_getProperValue:function(n){return n==null?null:isNaN(n)?n:n},_setMinMaxSizeInInteger:function(){var t;this._minWidth=parseInt(this.model.minWidth);this._minHeight=parseInt(this.model.minHeight);this._maxWidth=parseInt(this.model.maxWidth);this._maxHeight=parseInt(this.model.maxHeight);t=this.element.parent()[0].nodeName=="BODY"?n(window):n(this.element.parent()[0]);isNaN(this.model.minWidth)&&this.model.minWidth.indexOf("%")>0&&(this._minWidth=this._convertPercentageToPixel(t.outerWidth(),this._minWidth));isNaN(this.model.minHeight)&&this.model.minHeight.indexOf("%")>0&&(this._minHeight=this._convertPercentageToPixel(t.outerHeight(),this._minHeight));isNaN(this.model.maxWidth)&&this.model.maxWidth.indexOf("%")>0&&(this._maxWidth=this._convertPercentageToPixel(t.innerWidth(),this._maxWidth));isNaN(this.model.maxHeight)&&this.model.maxHeight.indexOf("%")>0&&(this._maxHeight=this._convertPercentageToPixel(t.innerHeight(),this._maxHeight))},_resizeFileExplorer:function(){var t=this;this.element.find("div.e-fe-resize").ejResizable({minHeight:t._minHeight,minWidth:t._minWidth,maxHeight:t._maxHeight,maxWidth:t._maxWidth,resizeStart:function(n){t._trigger("resizeStart",{event:n})},resize:function(n){t._refreshResizeEventHandler(n);t._trigger("resize",{event:n})},resizeStop:function(n){t._refreshResizeEventHandler(n);t._trigger("resizeStop",{event:n})},helper:function(){return n(t.element)}})},_showHideSplitBar:function(n){this._splittag.show();this._splitObj.model.properties[1].paneSize==0?(this.element.find(".e-splitbar").show(),this._splitObj.expand(1),!this._gridtag.hasClass("e-grid")&&n&&this._updateData()):this._splitObj.model.properties[0].paneSize==0&&(this.element.find(".e-splitbar").show(),this._splitObj.expand(0),this._treetag.parent(".e-tree-wrapper").css("display","block"));this.model.showNavigationPane||(this.model.enableRTL?this._splitObj.collapse(1):this._splitObj.collapse(0),this.element.find(".e-splitbar").hide(),!this._gridtag.hasClass("e-grid")&&n&&this._updateData())},_updateTreePath:function(i){var r,u,f,i;if(this._suggestionItems=[],this._toDownload=!1,this._toUpload=this._toEdit=this._toEditContents=this._toRead=this._toCopy=!0,this._removeBlurEffect(),this._searchbar&&this._searchbar.val(""),this._selectedTreeText=this.model.rootFolderName.length>0&&i.value==this.model.rootFolderName&&this._treeObj.element.find("li:first > div > .e-text").hasClass("e-active")?this._selectedContent=this._rootFolderName:this._selectedContent=i.value,this._selectedNode=i.currentElement,r=n(this._selectedNode.parents("li.e-item")[0]),this._parentNode=r.length!=0?r:this._selectedNode,this._nodeType="Directory",this._initUpdate?this._currentPath=this._updatePath(i.currentElement,i.value):this._initUpdate=!0,this._updateOnNodeSelection&&this._updateData(),this._originalPath=this._currentPath,!this._isStateNavigation){if(!t.isNullOrUndefined(this._currentState)){for(u=this._selectedStates.length-1;u>this._currentState;u--)this._selectedStates.pop();this._toolBarItems&&this._disableToolbarItem("Forward")}this._selectedStates[this._selectedStates.length-1]!=this._originalPath&&(this._currentState=this._selectedStates.length,this._selectedStates.push(this._originalPath),this._selectedStates.length==2&&this._toolBarItems&&this._enableToolbarItem("Back"))}this._updateAccessRules(this._originalPath);this._updateToolbarItems();this._updateNewFolderTool(this._toRead&&this._toEditContents);r.length||(this._disableEditingTools(),this._toolBarItems&&this._disableToolbarItem("Copy"));this.model.selectedFolder=this._currentPath;this._updateAddressBar();this._currntNode=this._selectedNode.find("> div > .e-text");this.model.selectedItems=[];this._filteredItemsName=[];this._selectedItems=[];this._selectedTileItems=[];f=this._getFolderPath();!this._isClicked||this._isTileViewClick||this._isGridViewClick||(i={name:[i.value],names:[i.value],path:f,nodeType:this._nodeType,selectedItems:this._getSelectedItemDetails(f,this._selectedContent)},this._initializeFlag&&this._trigger("select",i));this._isTileViewClick&&(this._isTileViewClick=!1);this._isGridViewClick&&(this._isGridViewClick=!1)},_modifySelectedStates:function(t,i){var r=this;n.each(r._selectedStates,function(n,u){u&&u.startsWith(t)&&(r._selectedStates[n]=i?r._selectedStates[n].replace(t,i):i)})},_onBeforeCollapse:function(){this._collapse=!0},_updatePath:function(t,i){for(var r="",u=0;u<t.parents("ul").length-1;u++)n(t.parents("ul")[u]).siblings("div").find("a").text()&&(r=n(t.parents("ul")[u]).siblings("div").find("a").text()+"/"+r);return this.model.rootFolderName.length>0?this._changeName(this._initPath+r+i+"/",!0):this._initPath+r+i+"/"},_updatePathFromGrid:function(r){var o,f,a,c,h,l,u,v,y,s,e;for(this._searchbar&&n.trim(this._searchbar.val())||(this._suggestionItems=[]),this.model.showCheckbox&&this._changeCheckState&&(this.model.allowMultiSelection||this._gridtag.find(".e-grid-row-checkbox").ejCheckBox({checked:!1}),n(r.target).closest(".e-chkbox-wrap").length&&this._checkChange(r.row),(r.target&&n(r.target).closest(".e-chkbox-wrap").length==0||t.isNullOrUndefined(r.target))&&this._recordClick()),this._FilteredFiles=[],o=this,f=n(r.target).closest("td.e-rowcell"),this._addFocus(this._gridtag.find(".e-gridcontent")),c=!0,this._toDownload=this._toUpload=this._toEdit=this._toEditContents=this._toRead=this._toCopy=!0,this._isTreeNode=!1,this._selectedItems=[],h=0,this._selectedRecords=this._gridtag.ejGrid("getSelectedRecords"),l=n.inArray(i,this._selectedRecords),l>-1&&this._selectedRecords.splice(l,1),u=0;u<this._selectedRecords.length;u++){if(!this._selectedRecords[u])return;this._suggestionItems&&this._suggestionItems.length?(s=this._selectedRecords[u].filterPath.replace(this._originalPath,"")+this._selectedRecords[u].name,this._selectedItems.push(s),v=this._selectedRecords[u].filterPath+this._selectedRecords[u].name,n.inArray(v,this._FilteredFiles)<0&&this._FilteredFiles.push(this._selectedRecords[u].filterPath+this._selectedRecords[u].name)):this._selectedRecords[u]&&this._selectedItems.push(this._selectedRecords[u].name);this._selectedRecords[u]&&(h+=this._selectedRecords[u].sizeInByte);this._selectedRecords[u]&&(this._selectedRecords[u].isFile||(c=!0,this._toDownload=!0));this._updateAccessValue(this._selectedRecords[u])}this._currentPath!=this._originalPath&&(this._currentPath=this._originalPath);n.each(o._suggestionItems.length>0?o._suggestionItems:o._fileExplorer[o._originalPath],function(n,t){t&&r.data&&(t.name!=r.data.name||t.isFile||(a=!0))});a?(this._nodeType="Directory",f.hasClass("e-active")&&this._updateNode(r.data.name)):this._nodeType="File";y=this._nodeType;this._currntNode=f[0]!=null&&f.parent("tr");this._selectedContent=this._selectedItems[this._selectedItems.length-1];!f.hasClass("e-active")&&this._selectedRecords.length>0&&(this._selectedContent=this._selectedItems[this._selectedItems.length-1],this._nodeType=this._selectedRecords[this._selectedRecords.length-1].isFile?"File":"Directory",this._nodeType=="Directory"&&this._updateNode(this._selectedContent));this._selectedItemsTag&&this._selectedItemsTag.html((this._selectedItems.length>0?this._selectedItems.length+" "+(this._selectedItems.length>1?this._getLocalizedLabels("Items"):this._getLocalizedLabels("Item"))+" "+this._getLocalizedLabels("Selected"):"")+(h?c?"  "+this._bytesToSize(h):"":""));this._setFilteredItemsName();this.model.selectedItems=this._filteredItemsName;e={name:this._selectedItems,names:this._selectedItems,path:this.model.selectedFolder,nodeType:this._nodeType,selectedItems:this._getSelectedItemDetails(this.model.selectedFolder,this._filteredItemsName)};this._urlTag&&this._urlTag.find("input").val(e.path);this._nameTag&&this._nameTag.find("input").val(e.name);this._updateSelectionDetails(this._nodeType);this._isClicked&&(!r.target||f&&f.hasClass("e-active")?this._initializeFlag&&this._trigger("select",e):(s=this._suggestionItems&&this._suggestionItems.length?r.data.filterPath.replace(this._originalPath,"")+r.data.name:r.data.name,e={name:s,names:[s],path:this.model.selectedFolder,nodeType:y,unselectedItem:r.data,unselectedItems:[r.data]},this._trigger("unselect",e)))},_enableEditingTools:function(){var t,n;if(!this._editingToolsState&&this._toolBarItems){for(t=["Rename","Delete","Cut"],n=0;n<t.length;n++)this._enableToolbarItem(t[n]);this._editingToolsState=!0}},_enableToolbarItem:function(n){if(this._restrictedToolbarOptions.indexOf(n)<0){var t=this._ExplorerId+n.replace(/ /g,"");this._toolBarObj&&this._toolBarObj.itemsContainer.find("li#"+t).hasClass("e-disable")&&this._toolBarObj.enableItemByID(t)}},_disableToolbarItem:function(n){var t=this._ExplorerId+n.replace(/ /g,"");this._toolBarObj&&!this._toolBarObj.itemsContainer.find("li#"+t).hasClass("e-disable")&&this._toolBarObj.disableItemByID(t)},_disableEditingTools:function(){var t,n;if(this._toolBarItems){for(t=["Rename","Delete","Cut"],n=0;n<t.length;n++)this._disableToolbarItem(t[n]);this._editingToolsState=!1}},_clearTileCheckBoxSelection:function(){this.model.showCheckbox&&this._tileView.find(".e-tile-checkbox ").ejCheckBox({checked:!1})},_upDatePathFromTileView:function(t){var b=n(t.target),v,l,s,a,f,y,u,e,o,h,c,w,r,i;if(t.pointerType=="touch"&&this._customPop!=null&&this.model.allowMultiSelection?(this._customPop.is(":visible")||this._customPop.show(),this._customPop.is(":visible")&&!this._customPop.find(".e-rowselect").hasClass("e-spanclicked")?(v=b.offset(),this._customPop.offset({left:v.left-40,top:v.top-40})):t.ctrlKey=!0):this._hidePopup(),this._searchbar&&n.trim(this._searchbar.val())||(this._suggestionItems=[]),t.innerEvent||(this._lastItemIndex=n(t.currentTarget).index()),this._addFocus(this._tileViewWrapper),this._isTreeNode=!1,l=this,this.model.showCheckbox&&(s=n(t.target).closest(".e-tilenode").find(".e-tile-checkbox").data("ejCheckBox"),n(t.target).hasClass("e-chk-image")&&(t.ctrlKey=this.model.showCheckbox)),t.shiftKey){for(this._selectItems=[],this._startNode||(this._startNode=this._currntNode),h=n(t.currentTarget).index(),c=this._startNode.index(),h>c&&(w=h,h=c,c=w),r=this._tileContent.find(".e-tilenode"),r.removeClass("e-active").attr("aria-selected",!1),i=h;i<=c;i++)this._selectItems.push(n(r.get(i)).find(".e-file-name").text());this._setSelectedItems(this._selectItems)}else{if(a=[],(!t.ctrlKey||!this.model.allowMultiSelection)&&this.element.find(".e-fe-selection-rect").length<=0&&(this._sizeInByte=0,this._selectedItems=[],this._selectedTileItems=[],n(t.currentTarget).siblings().removeClass("e-active").attr("aria-selected",!1),n(t.currentTarget).removeClass("e-active").attr("aria-selected",!1),this._clearTileCheckBoxSelection(),this._unselectEvent()),n(t.currentTarget).hasClass("e-active")?(n(t.currentTarget).removeClass("e-active").attr("aria-selected",!1),s&&s.option("checked",!1)):(n(t.currentTarget).addClass("e-active").attr("aria-selected",!0),s&&s.option("checked",!0)),f="File",y=!0,this._toDownload=this._toUpload=this._toEdit=this._toEditContents=this._toRead=this._toCopy=!0,u=n(t.currentTarget).find(".e-file-name").text(),this._currentPath!=this._originalPath&&(this._currentPath=this._originalPath),r=this._suggestionItems.length?this._suggestionItems:this._fileExplorer[this._originalPath],r)for(i=0;i<r.length;i++)if(this._suggestionItems<1&&r[i].name==u||this._suggestionItems.length>0&&r[i].filterPath==n(t.currentTarget).attr("data-parent-path")&&r[i].name==u){n(t.currentTarget).hasClass("e-active")?(this._sizeInByte+=r[i].sizeInByte,this._selectedTileItems.push(r[i])):t.ctrlKey&&(this._sizeInByte-=r[i].sizeInByte,e=n.inArray(r[i],this._selectedTileItems),e>-1&&(a.push(this._selectedTileItems[e]),this._selectedTileItems.splice(e,1)));break}for(n.each(this._selectedTileItems,function(n,t){t.isFile||(l._update=!0,y=!0,l._toDownload=!0);l._updateAccessValue(t)}),this._FilteredFiles=[],i=0;i<this._selectedTileItems.length;i++)this._FilteredFiles.push(this._selectedTileItems[i].filterPath+this._selectedTileItems[i].name);if(n(t.currentTarget).find(".e-fe-folder").length&&(f="Directory",n(t.currentTarget).hasClass("e-active")&&this._updateNode(u)),this._currntNode=n(t.currentTarget),this._currntNode.attr("data-parent-path")&&(u=this._currntNode.attr("data-parent-path").replace(this._originalPath,"")+u),this._selectedContent=u,n(t.currentTarget).hasClass("e-active"))this._selectedItems.push(this._selectedContent);else{var e=n.inArray(this._selectedContent,this._selectedItems),p=this._selectedContent,k=f;e>-1&&this._selectedItems.splice(e,1);this._selectedTileItems.length>0&&(this._selectedContent=this._selectedItems[this._selectedItems.length-1],f=this._selectedTileItems[this._selectedTileItems.length-1].type,f=="Directory"&&this._updateNode(this._selectedContent))}this._startNode&&this._selectItems.length!=0&&this._selectItems.length!=this._selectedItems.length||((!this._selectItems||this._selectItems.length<=0)&&(this._startNode=null),this._selectedItemsTag&&this._selectedItemsTag.html((this._selectedItems.length>0?this._selectedItems.length+" "+(this._selectedItems.length>1?this._getLocalizedLabels("Items"):this._getLocalizedLabels("Item"))+" "+this._getLocalizedLabels("Selected"):"")+(this._sizeInByte?y?"  "+this._bytesToSize(this._sizeInByte):"":"")),this._nodeType=f,this._setFilteredItemsName(),this.model.selectedItems=this._filteredItemsName,o={name:this._selectedItems,names:this._selectedItems,path:this.model.selectedFolder,nodeType:this._nodeType,selectedItems:this._getSelectedItemDetails(this.model.selectedFolder,this._filteredItemsName)},this._urlTag&&this._urlTag.find("input").val(o.url),this._nameTag&&this._nameTag.find("input").val(o.name),this._updateSelectionDetails(this._nodeType),this._isClicked&&(n(t.currentTarget).hasClass("e-active")?this._initializeFlag&&this._trigger("select",o):(o={name:p,names:[p],path:this.model.selectedFolder,nodeType:k,unselectedItem:a[0],unselectedItems:a},this._trigger("unselect",o))),this._selectItems=[])}},_setFilteredItemsName:function(){var t,n;for(this._filteredItemsName=[],t=0;t<this._selectedItems.length;t++)n=this._selectedItems[t].split("/"),this._filteredItemsName.push(n[n.length-1]?n[n.length-1]:n[n.length-2])},_getOriginalName:function(n){var t=n.split("/");return t[t.length-1]?t[t.length-1]:t[t.length-2]},_updateNode:function(n){this._currentPath+=n+"/"},_getFileURL:function(){return this._nodeType=="File"?(/\.(gif|jpg|jpeg|tiff|png|bmp)$/i.test(this._selectedContent)?(this._widthTag&&this._widthTag.show().removeClass("e-hide"),this._heightTag&&this._heightTag.show().removeClass("e-hide")):(this._widthTag&&this._widthTag.hide().addClass("e-hide"),this._heightTag&&this._heightTag.hide().addClass("e-hide")),this._currentPath.replace("~","..")+this._selectedContent):""},_updateData:function(){var n=this._fileExplorer[this._currentPath],t;if(this._selectedItemsTag&&this._selectedItemsTag.html(""),n&&!this._update){this._itemStatus&&this._itemStatus.html(n.length+" "+(n.length==1?this._getLocalizedLabels("Item"):this._getLocalizedLabels("Items")));var i=this.model.gridSettings.columns[0];this._changeActiveSortedoption(i.headerText,!0);this._sorting(i.field,!0,n);t=this._sorteditems;this.model.layout=="grid"?this._renderGridView(t):this._renderTileView(t)}else this._read()},_addChild:function(t,i){var e=[],r,o,u,f;if(t)for(r=0;r<t.length;r++)t[r].isFile||(t[r].id=t[r].name,t[r].spriteCssClass=t[r].permission&&!t[r].permission.Read?"e-fe-icon e-fe-folder e-fe-lock":"e-fe-icon e-fe-folder",e.push(t[r]));this._nodeExpanded=!0;o=i?n(i).closest("li.e-item"):this._treeObj.getSelectedNode();e.length&&this._treeObj.addNode(e,o);u=o.find(".e-load");u&&(u.hasClass("e-plus")||u.hasClass("e-minus")?u.removeClass("e-load"):u.removeClass("e-icon e-load"));this._nodeExpanded=!1;this._treetag.find("li").removeAttr("tabindex");f=this._treetag.find("li div a").not(".e-js");f.length&&this.model.allowDragAndDrop&&(this._drag(f),f.addClass("e-file-draggable"))},_createContextMenuTag:function(i,r,u){var e=n("body").find("#"+r.id+"ContextMenu"),f=e.length?e:t.buildTag("ul.fe-context-menu #"+r.id+"ContextMenu");return f.ejMenu({menuType:t.MenuType.ContextMenu,enableSeparator:!0,enableRTL:this.model.enableRTL,cssClass:this.model.cssClass,contextMenuTarget:r.targetId,beforeOpen:u.beforeOpen,open:u.open,close:u.close,click:u.click,fields:{dataSource:i,id:"id",text:"text",htmlAttribute:"htmlAttr",spriteCssClass:"sprite"}}),f.hide(),f},_beforeOpenContextMenu:function(t){if(n(t.target).hasClass("e-text")){if(this._menuNode=n(t.target).closest("li.e-item"),this._treeObj&&this._treeObj.element.find(".e-node-focus").removeClass("e-node-focus"),this._menuNode.find("> div > .e-text:first").addClass("e-node-focus"),t.dataSource=this._treeMenuOptions.slice(),t.contextMenu="navbar",t.element=this._treeMenuObj.element,this._trigger("menuBeforeOpen",t))return!1;JSON.stringify(this._treeMenuObj.model.fields.dataSource)!=JSON.stringify(this._treeMenuOptions)&&this._treeMenuObj.option("fields",{dataSource:t.dataSource});for(var i=0;i<this._restrictedMenuOption.length;i++)this._treeMenuObj&&this._treeMenuObj.disableItem(this._restrictedMenuOption[i]);this._toRead&&this._toEdit?this._enableEditingMenus():this._disableEditingMenus();this._toRead&&this._toCopy?this._restrictedMenuOption.indexOf(this._menuCopy)<0&&this._treeMenuObj.enableItem(this._menuCopy):this._treeMenuObj.disableItem(this._menuCopy);this._toRead?this._restrictedMenuOption.indexOf(this._menuOpen)<0&&this._treeMenuObj.enableItem(this._menuOpen):this._treeMenuObj.disableItem(this._menuOpen);this._toRead&&this._toEditContents?this._restrictedMenuOption.indexOf(this._menuNewFolder)<0&&this._treeMenuObj.enableItem(this._menuNewFolder):this._treeMenuObj.disableItem(this._menuNewFolder);this._toRead&&this._toUpload?this._restrictedMenuOption.indexOf(this._menuUpload)<0&&this._treeMenuObj.enableItem(this._menuUpload):this._treeMenuObj.disableItem(this._menuUpload);n(t.target).parents("li.e-item:first").attr("id")==1&&this._disableEditingMenus()}else t.cancel=!0},_beforeOpenTileContextMenu:function(t){if(n(t.target).closest("th.e-headercell").hasClass("e-col-check")&&this._headCheckObj.wrapper.click(),n(t.target).hasClass("e-scrollbar")||n(t.target).parents().hasClass("e-scrollbar")||n(t.target).closest("th.e-headercell").hasClass("e-col-check")){t.cancel=!0;return}if(!n(t.target).hasClass("e-rowcell")&&!n(t.target).closest("td.e-rowcell").hasClass("e-col-check")&&n(t.target).closest("td.e-rowcell").length>0&&!n(t.target).closest("td.e-rowcell").hasClass("e-active")&&t.events&&(t.events.button==2||t.events.which==3)?this._updateGridSelection(t):(n(t.target).hasClass("e-file-info")||n(t.target).hasClass("e-thumb-image")||n(t.target).closest(".e-thumb-image").length>0||n(t.target).closest(".e-tilenode").length>0)&&n(t.target).closest(".e-chkbox-wrap").length==0&&!n(t.target).closest(".e-tilenode").hasClass("e-active")&&this._updateTileSelection(t),n(t.target).is(".e-tilenode.e-active")||n(t.target).closest(".e-tilenode").hasClass("e-active")||n(t.target).closest("td.e-rowcell").is(".e-active")||(t.events.ctrlKey||t.events.shiftKey||n(t.target).closest("td.e-rowcell").hasClass("e-col-check")||n(t.target).closest(".e-chkbox-wrap").length>0)&&(n(this.items).hasClass("e-active")||n(this.gridItems).find("td").hasClass("e-active"))?(t.dataSource=this._fileMenuOptions.slice(),t.contextMenu="files",t.element=this._viewMenuObj.element):(n(t.events.currentTarget).hasClass("e-grid")&&(n(t.target).hasClass("e-gridcontent")||n(t.target).hasClass("e-content")||n(t.target).hasClass("e-table")||!n(t.target).is(".e-rowcell.e-active"))&&(this._gridObj.clearSelection(),this.model.showCheckbox&&(this._gridtag.find(".e-grid-row-checkbox").ejCheckBox({checked:!1}),this._gridtag.find("#headchk").ejCheckBox({checked:!1}))),n(t.events.currentTarget).hasClass("e-tile-wrapper")&&(n(t.target).hasClass("e-tile-wrapper")||n(t.target).hasClass("e-tile-content")||n(t.target).hasClass("e-tileview")||!(n(t.target).is(".e-tilenode.e-active")||n(t.target).parent(".e-tilenode").hasClass("e-active")))&&(this.model.showCheckbox&&this._clearTileCheckBoxSelection(),this.items.hasClass("e-active")&&this.items.removeClass("e-active").attr("aria-selected",!1)),this._updateCurrentPathPermission(),t.dataSource=this._cwdMenuOptions.slice(),t.contextMenu="cwd",t.element=this._viewMenuObj.element),this._trigger("menuBeforeOpen",t))return!1;JSON.stringify(this._viewMenuObj.model.fields.dataSource)!=JSON.stringify(t.dataSource)&&this._viewMenuObj.option("fields",{dataSource:t.dataSource});for(var i=0;i<this._restrictedMenuOption.length;i++)this._viewMenuObj&&this._viewMenuObj.disableItem(this._restrictedMenuOption[i]);this._toRead&&(/\.(bmp|dib|jpg|jpeg|jpe|jfif|gif|tif|tiff|png|ico)$/i.test(this.model.selectedItems)||this._nodeType!="File")?this._restrictedMenuOption.indexOf(this._menuOpen)<0&&this._viewMenuObj.enableItem("Open"):this._viewMenuObj&&this._viewMenuObj.disableItem("Open");this._option&&this._toRead||this._viewMenuObj.disableItem(this._menuPaste);this._isupdate?this._restrictedMenuOption.indexOf(this._menuDownload)<0&&this._viewMenuObj.enableItem(this._menuDownload):this._viewMenuObj.disableItem(this._menuDownload);this._searchbar&&n.trim(this._searchbar.val())?this._restrictedMenuOption.indexOf(this._menuOpenFolderLocation)<0&&this._viewMenuObj.enableItem(this._menuOpenFolderLocation):this._viewMenuObj.disableItem(this._menuOpenFolderLocation);this._toRead&&this._toDownload?this._restrictedMenuOption.indexOf(this._menuDownload)<0&&this._viewMenuObj.enableItem(this._menuDownload):this._viewMenuObj.disableItem(this._menuDownload);this._toRead&&this._toUpload?this._restrictedMenuOption.indexOf(this._menuUpload)<0&&this._viewMenuObj.enableItem(this._menuUpload):this._viewMenuObj.disableItem(this._menuUpload);this._toRead&&this._toEdit?this._enableEditingMenus():this._disableEditingMenus();this._toRead&&this._toCopy?this._restrictedMenuOption.indexOf(this._menuCopy)<0&&this._viewMenuObj.enableItem(this._menuCopy):this._viewMenuObj.disableItem(this._menuCopy);this._hasEditContentsPermission(this._originalPath)?this._restrictedMenuOption.indexOf(this._menuNewFolder)<0&&this._viewMenuObj.enableItem(this._menuNewFolder):this._viewMenuObj.disableItem(this._menuNewFolder)},_contextMenuOpen:function(n){var i,t;for(n.contextMenu=n.model.contextMenuTarget==this._ExplorerId+"_treeView"?"navbar":this.model.selectedItems.length>0?"files":"cwd",n.element=n.contextMenu=="navbar"?this._treeMenuObj.element:this._viewMenuObj.element,n.contextMenu=="cwd"&&(i=this.model.gridSettings.columns.length),t=0;t<i;t++)this._prevsortingoption==this.model.gridSettings.columns[t].field&&this._changeActiveSortedoption(this.model.gridSettings.columns[t].headerText,!1,!0);this._trigger("menuOpen",n)},_removeOldSelectionDetails:function(n){this._currentPath!=this._originalPath&&(this._currentPath=this._originalPath);this._sizeInByte=0;this._selectedItems=[];this._selectedTileItems=[];this.model.selectedItems=[];this._selectedContent=this._selectedTreeText;this._nodeType=n?n:"Directory";this._selectedItemsTag&&this._selectedItemsTag.html("");this._toolBarItems&&this._disableToolbarItem("Download");this._viewMenuObj&&this._viewMenuObj.disableItem(this._menuDownload);this._disableEditingTools()},_contextMenuClick:function(n){this._treeObj.selectNode(this._menuNode);this._fileContextMenuClick(n)},_fileContextMenuClick:function(n){var u,f,i,r;if(this.model.ajaxAction!=""&&this._currentPath!=""){if(n.contextMenu=n.model.contextMenuTarget==this._ExplorerId+"_treeView"?"navbar":this.model.selectedItems.length>0?"files":"cwd",(t.isNullOrUndefined(n.selectedItem)||n.selectedItem.attr("id")!=this._ExplorerId+"_cwd_SortBy")&&(t.isNullOrUndefined(n.element)||n.element.parentElement.parentElement.id!=this._ExplorerId+"_cwd_SortBy"))switch(n.text){case this._menuOpen:this._openAction();break;case this._menuNewFolder:this._createNewFolder();break;case this._menuDelete:this._deleteFolder();break;case this._menuRefresh:this.refresh();break;case this._menuRename:this._renameFolder();break;case this._menuUpload:this.element.find(".e-uploadinput").click();break;case this._menuDownload:this._downloadFile();break;case this._menuCut:this._copyMoveNode("move");break;case this._menuCopy:this._copyMoveNode("copy");break;case this._menuPaste:this._cut_copy();break;case this._menuGetinfo:this._getDetails();break;case this._menuOpenFolderLocation:this._setFilteredItemsName();u=this.model.selectedItems;this._selectedFolder(this._originalPath+this._selectedContent.replace(this._filteredItemsName,""));this._setSelectedItems(u);break;case n.text:f=this.model.contextMenuSettings.customMenuFields;r=this._getCustomItem(f,n.ID);r&&(i=r.action);typeof i=="string"&&(i=t.util.getObject(i,window));i&&i(n)}else this._changeActiveSortedoption(n.text),this._sorting(this._prevsortingoption,this._prevsorting),this._sortingActioncomplete();this._trigger("menuClick",n)}},_createNewFolder:function(){var i=this,r="",c=this._getLocalizedLabels("NewFolderAlert"),e=t.buildTag("div.e-get-name"),l=t.buildTag("div.e-fe-dialog-label",c),u=t.buildTag("input.e-fe-dialog-text e-ejinputtext e-textbox","","",{type:"text"}),o=t.buildTag("div.e-fe-dialog-label e-error-msg");u.val("New folder");var s=t.buildTag("div.e-fe-dialog-btn"),f=t.buildTag("button.e-fe-btn-ok ",this._getLocalizedLabels("OkButton")),h=t.buildTag("button.e-fe-btn-cancel ",this._getLocalizedLabels("CancelButton"));f.ejButton({type:"button",cssClass:"e-flat",click:function(){var f,e,l;if(i._removeDialog(i._newFolderDialogObj),r=u.val(),n.trim(r)||(r="New folder"),i._fileExplorer[i._currentPath]||i._getFileDetails(i._currentPath),f=i._fileExplorer[i._currentPath],f.length)for(e=0;e<f.length;e++)if(f[e].isFile||f[e].name!=r)e==f.length-1&&r&&i._createFolder(r);else{var s=t.buildTag("div.e-get-name"),a=t.buildTag("div.e-fe-dialog-label",String.format(i._getLocalizedLabels("ErrorOnFolderCreation"),r)),h=t.buildTag("div.e-fe-dialog-btn"),o=t.buildTag("button.e-fe-btn-ok ",i._getLocalizedLabels("YesButton")),c=t.buildTag("button.e-fe-btn-cancel ",i._getLocalizedLabels("NoButton"));o.ejButton({cssClass:"e-flat",type:"button",click:function(){i._removeDialog(i._alertDialogObj)}});c.ejButton({cssClass:"e-flat",type:"button",click:function(){i._removeDialog(i._alertDialogObj);i._createFolder(i._getDuplicateName(f,"New folder"))}});h.append(o,c);n(s).append(a,h);l=function(){o.focus()};i._alertDialog=i._createDialog(s,{width:400,height:"auto",title:i._getLocalizedLabels("Error"),open:l});i._alertDialogObj=i._alertDialog.data("ejDialog");break}else r&&i._createFolder(r)}});h.ejButton({cssClass:"e-flat",type:"button",click:function(){i._removeDialog(i._newFolderDialogObj)}});s.append(f,h);n(e).append(l,u,o,s);this._newFolderDialog=this._createDialog(e,{width:350,height:"auto",open:function(){i._openInputDialog(u,f,o,this)},title:this._getLocalizedLabels("NewFolder")});this._newFolderDialogObj=this._newFolderDialog.data("ejDialog")},_openInputDialog:function(n,t,i,r){var u=this;n.focus();this._isDevice||n.select();(this._isMobileOrTab||this._isDevice)&&r.option("position",{Y:"20%"});n.keyup(function(n){var i=u._getKeyCode(n);i==13&&t.click()});n.bind("input",function(n){for(var f,t=n.target.value,r=0;r<t.length;r++)if(f=t.charCodeAt(r),/[/\\|*?"<>:]/.test(String.fromCharCode(f))){i.html(u._getLocalizedLabels("InvalidFileName"));t=t.replace(/[/\\|*?"<>:]/g,"");break}document.activeElement.value=t});n.keydown(function(){i.html("")})},_deleteFolder:function(){var r,n,e;this._alertWindow=t.buildTag("div#e-fe_deleteAlert");r=this._selectedItems.length>1?String.format(this._getLocalizedLabels("DeleteMultipleFolder"),this._selectedItems.length):this._getLocalizedLabels("DeleteFolder")+this._selectedContent+"?";var o=t.buildTag("div.e-fe-dialog-label",r),u=t.buildTag("div.e-fe-dialog-btn"),i=t.buildTag("button.e-fe-btn-ok",this._getLocalizedLabels("OkButton")),f=t.buildTag("button.e-fe-btn-cancel",this._getLocalizedLabels("CancelButton"));u.append(i,f);this._alertWindow.append(o,u);n=this;i.ejButton({type:"button",cssClass:"e-flat",click:function(){n._removeDialog(n._alertWindowObj);n._ajax_folderPath=n._nodeType=="Directory"?n._getFolderPath():n._currentPath;n._deletion(n._selectedItems.length>1?n._selectedItems:n._selectedContent,n._ajax_folderPath);n._disableEditingTools();n._disableToolbarItem("Copy")}});f.ejButton({cssClass:"e-flat",type:"button",click:function(){n._removeDialog(n._alertWindowObj)}});e=function(){i.focus()};this._alertWindow=this._createDialog(this._alertWindow,{width:350,height:"auto",title:this._getLocalizedLabels("Delete"),open:e});this._alertWindowObj=this._alertWindow.data("ejDialog")},_getFileDetails:function(r,u,f,e){var o=this,s={data:{ActionType:"Read",Path:r,ExtensionsAllow:this.model.fileTypes,SelectedItems:this._getSelectedItemDetails(this._getFolderPath(r),u?u.text():this.model.selectedItems.length?this.model.selectedItems:this._selectedContent)},url:this.model.ajaxAction,type:"POST",async:!1,success:function(i){if(i=i.hasOwnProperty("d")?i.d:i,!t.isNullOrUndefined(i.error)){o._showErrorDialog(i.error);return}for(var s=0;s<i.files.length;s++)i.files[s].sizeInByte=i.files[s].size,i.files[s].size=i.files[s].size?o._bytesToSize(i.files[s].size):"",i.files[s].cssClass=o._getCssClass(i.files[s]);o._feParent[r]=i.cwd;o._fileExplorer[r]=i.files;u&&o._addChild(o._fileExplorer[r],n(u));f&&(o.model.virtualItemCount>0&&(o._virtualScrollRenamedItem=o._highlightedNodes),o._sorting(o._prevsortingoption,o._prevsorting,i.files),o.model.layout=="grid"?o._renderGridView(o._sorteditems):o._renderTileView(o._sorteditems),o._updateItemStatus(o._sorteditems));o._highlightedNodes&&!o._suggestionItems.length&&(o._setSelectedItems(o._highlightedNodes),o._highlightedNodes="");o._searchbar&&n.trim(o._searchbar.val())&&o._searchFiles(o._originalPath);e&&typeof e=="function"&&e()},successAfter:this.model.ajaxSettings.read.success};this.model.ajaxSettings.read.success=i;n.extend(!0,s,this.model.ajaxSettings.read);this._sendAjaxRequest(s)},_searchFiles:function(r){var u=this,f={data:{ActionType:"Search",SearchString:this._queryString,Path:r,CaseSensitive:this.model.filterSettings.caseSensitiveSearch,ExtensionsAllow:this.model.fileTypes,SelectedItems:this._getSelectedItemDetails(this._getFolderPath(r),this.model.selectedItems.length?this.model.selectedItems:this._selectedContent)},url:this.model.ajaxAction,type:"POST",async:!1,success:function(n){if(n=n.hasOwnProperty("d")?n.d:n,!t.isNullOrUndefined(n.error)){u._showErrorDialog(n.error);return}for(var i=0;i<n.files.length;i++)n.files[i].sizeInByte=n.files[i].size,n.files[i].size=n.files[i].size?u._bytesToSize(n.files[i].size):"",n.files[i].cssClass=u._getCssClass(n.files[i]),n.files[i].filterPath=u._originalPath+n.files[i].filterPath.replace(/\\/g,"/");u._sorting(u._prevsortingoption,u._prevsorting,n.files);u._suggestionItems=n.files=u._sorteditems;u._suggestionItems=n.files;u.model.layout=="grid"?u._renderGridView(n.files):u._renderTileView(n.files);u._updateItemStatus(n.files);u._setSelectedItems(u._highlightedNodes);u._highlightedNodes=""},successAfter:this.model.ajaxSettings.search.success};this.model.ajaxSettings.search.success=i;n.extend(!0,f,this.model.ajaxSettings.search);this._sendAjaxRequest(f)},_getDuplicateName:function(n,t){for(var i=0,r=t;this._isNameExist(n,t);)i++,t=r.split(".")[0]+(i>0?"("+i+")":"")+(t.split(".")[1]?"."+t.split(".")[1]:"");return t},_isNameExist:function(n,t){for(var i=0;n&&i<n.length;i++)if(t==n[i].name)return!0;return!1},_renameFolder:function(){var i=this,c=this._getLocalizedLabels("RenameAlert"),e=t.buildTag("div.e-rename"),l=t.buildTag("div.e-fe-dialog-label",c),r;i.model.virtualItemCount>0&&(i._renamedStatus=!0);r=i._nodeType=="Directory"?this._selectedContent:this._selectedContent.substr(0,this._selectedContent.lastIndexOf("."));var u=t.buildTag("input.e-fe-dialog-text e-ejinputtext e-textbox","","",{type:"text",value:r}),o=t.buildTag("div.e-fe-dialog-label e-error-msg"),s=t.buildTag("div.e-fe-dialog-btn"),f=t.buildTag("button.e-fe-btn-ok",this._getLocalizedLabels("OkButton")),h=t.buildTag("button.e-fe-btn-cancel",this._getLocalizedLabels("CancelButton"));f.ejButton({cssClass:"e-flat",type:"button",click:function(){var f=u.val(),s=r,o,e,v;if(i._removeDialog(i._renameDialogObj),n.trim(f)&&f!=s)for(i._currentPath=i._nodeType=="Directory"?i._getFolderPath():i._currentPath,i._fileExplorer[i._currentPath]||i._getFileDetails(i._currentPath),o=i._fileExplorer[i._currentPath],i._ajax_person=i._selectedContent.replace(s,f),i._nodeType=="File"&&/\.(bmp|dib|jpg|jpeg|jpe|jfif|gif|tif|tiff|png|ico)$/i.test(i._ajax_person)&&(i._updateImages[i._currentPath+i._ajax_person]=(new Date).getTime()),e=0;e<o.length;e++)if(o[e].name==f){i._nodeType=="File"&&(f=i._getDuplicateName(i._fileExplorer[i._currentPath],f),i._ajax_person=i._selectedContent.replace(s,f));var c=t.buildTag("div.e-get-name"),y=t.buildTag("div.e-fe-dialog-label",String.format(i._getLocalizedLabels(i._nodeType=="File"?"DuplicateFileCreation":"ErrorOnFolderCreation"),f)),l=t.buildTag("div.e-fe-dialog-btn"),h=t.buildTag("button.e-fe-btn-ok ",i._getLocalizedLabels("OkButton")),a=t.buildTag("button.e-fe-btn-cancel ",i._getLocalizedLabels("CancelButton"));h.ejButton({cssClass:"e-flat",type:"button",click:function(){i._removeDialog(i._alertDialogObj);i._nodeType=="File"?i._rename():(i._existingItems=[],i._getDuplicateItems(i._currentPath+i._selectedContent+"/",i._currentPath+f+"/",!0),i._existingItems.length?i._createReplaceConformationDiaolg("_rename","DuplicateAlert"):i._rename())}});a.ejButton({cssClass:"e-flat",type:"button",click:function(){i._removeDialog(i._alertDialogObj)}});l.append(h,a);n(c).append(y,l);v=function(){h.focus()};i._alertDialog=i._createDialog(c,{width:400,height:"auto",title:i._getLocalizedLabels("Error"),open:v});i._alertDialogObj=i._alertDialog.data("ejDialog");break}else e==o.length-1&&i._rename()}});h.ejButton({cssClass:"e-flat",type:"button",click:function(){i._removeDialog(i._renameDialogObj)}});s.append(f,h);n(e).append(l,u,o,s);this._renameDialog=this._createDialog(e,{width:350,height:"auto",open:function(){i._openInputDialog(u,f,o,this)},title:this._getLocalizedLabels("Rename")});this._renameDialogObj=this._renameDialog.data("ejDialog")},_isSelectedFile:function(n,t){for(var i=0;i<n.length;i++)if(t==n[i])return!0;return!1},_refreshItems:function(t,i,r){var e,f,u,o;if(this._treeObj){for(t=n(t),!this._treeObj.isExpanded(t)&&this._treeObj.hasChildNode(t)&&this._treeObj.expandNode(t),this._fileExplorer[i]="",e=this,n.each(e._fileExplorer,function(n){n.startsWith(i)&&(e._fileExplorer[n]="")}),f=t.find("ul:first > li"),u=0;u<f.length;u++)t.find(f[u]).length&&this._treeObj.removeNode(n(f[u]));o=t.find(".e-process");o.length&&o.removeClass("e-process");this._getFileDetails(i,t.find("> div > .e-text"),!0,r)}else this._currentPath=this.model.path,this._read()},_getDuplicateItems:function(n,t,i){var r,f,e,u;if(this._fileExplorer[t]||this._getFileDetails(t),r=this._fileExplorer[t],this._fileExplorer[n]||this._getFileDetails(n),f=this._fileExplorer[n],f&&r)for(e=0;e<f.length;e++)for(u=0;u<r.length;u++)f[e].name==r[u].name&&(i==!0||this._isSelectedFile(i,r[u].name))&&(this._existingItems[this._existingItems.length]={Name:r[u].name,Path:t+r[u].name+(r[u].isFile?"":"/"),IsReplace:!0},r[u].isFile||this._getDuplicateItems(n+f[e].name+"/",t+r[u].name+"/",!0))},_backward:function(){var i;if(this._currentState>0){var n=!0,t=0;for(this._isStateNavigation=!0,i=this._currentState,--this._currentState;this._selectedStates[this._currentState]==""||this._selectedStates[this._currentState]==this._selectedStates[i];)this._currentState?--this._currentState:n=!1;n&&this._selectedFolder(this._selectedStates[this._currentState]);this._isStateNavigation=!1;(this._currentState==t||this._selectedStates.length-2==this._currentState)&&this._toolBarItems&&this._enableToolbarItem("Forward");this._currentState==t&&this._toolBarItems&&this._disableToolbarItem("Back")}},_forward:function(){var t;if(this._currentState+1<this._selectedStates.length){var n=!0;for(this._isStateNavigation=!0,t=this._currentState,++this._currentState;this._selectedStates[this._currentState]==""||this._selectedStates[this._currentState]==this._selectedStates[t];)this._currentState<this._selectedStates.length-1?++this._currentState:n=!1;n&&this._selectedFolder(this._selectedStates[this._currentState]);this._isStateNavigation=!1;this._selectedStates.length-1==this._currentState&&this._toolBarItems&&this._disableToolbarItem("Forward");this._currentState==0&&this._toolBarItems&&this._disableToolbarItem("Back");this._currentState==1&&this._toolBarItems&&this._enableToolbarItem("Back")}},_copyMoveNode:function(n){var t,i;n=="move"&&(this.element.find(".e-blur").removeClass("e-blur"),t=this.element.find(".e-splitter .e-active"),t.length&&t.length==1?t.addClass("e-blur"):this.element.find(".e-cont2 .e-active").addClass("e-blur"));this._copiedNodes=this._filteredItemsName;this._option=n;this._sourcePath=this._nodeType=="Directory"?this._getFolderPath():this._currentPath;this._sourceType=this._nodeType;this._fileName=this._selectedItems.length>1?this._selectedItems:this._selectedContent;this._refreshNode=this._originalPath!=this._currentPath?this._findMatchingElement(this._selectedNode.find("ul:first"),this._selectedContent):this._selectedNode;this._toolBarItems&&this._enableToolbarItem("Paste");this.model.showContextMenu&&(this._restrictedMenuOption.indexOf(this._menuPaste)<0&&this._viewMenuObj.enableItem(this._menuPaste),this._restrictedMenuOption.indexOf(this._menuPaste)<0&&this._treeMenuObj.enableItem(this._menuPaste));i={name:this._fileName,sourcePath:this._sourcePath,selectedItems:this._getSelectedItemDetails(this._sourcePath,this._fileName)};n=="move"?this._trigger("cut",i):this._trigger("copy",i)},_openAction:function(i){var r,h,u,c,s,f,e,o,v,y;if((t.isNullOrUndefined(i)||(n(i.currentTarget).is("li.e-tilenode")&&(this._isTileViewClick=!0),i.type=="recordDoubleClick"&&(this._isGridViewClick=!0)),this._toRead)&&(r=this,this._nodeType=="File"&&(this._selectedFile=this._selectedContent),!this._onBeforeOpen())){if(this._nodeType=="Directory")if(this._suggestionItems.length)this._selectedFolder(this._originalPath+this._selectedContent);else{for(u=this._treeObj.getSelectedNode(),c=this._selectedContent,this._treeObj.isExpanded(u)||this._treeObj&&this._treeObj.expandNode(u),s=u.find("ul:first>li").find("div:first .e-text"),this._treeObj.hasChildNode(u)||(f=this._isTreeNode==!0?this._currentPath:this._currentPath.replace(this._selectedContent+"/",""),this._isTreeNode=!1,this._addChild(this._fileExplorer[f])),e=0;e<s.length;e++)if(n(s[e]).text()==c){this._treeObj.selectNode(s[e].parentNode.parentNode);break}h=this._getSelectedItemDetails(this._getFolderPath(),c);this._selectedItems=[];this._selectedTileItems=[]}else if(this._nodeType=="File"){if(/\.(bmp|dib|jpg|jpeg|jpe|jfif|gif|tif|tiff|png|ico)$/i.test(this._selectedFile)){r._openDialog=t.buildTag("div.e-imageViewer","","",{id:r._ExplorerId+"_basicDialog",title:r._selectedFile});var f=r._currentPath.replace("~","..")+this._selectedFile,l=this._getImage(f,this._selectedFile,!1),a=t.buildTag("img","","",{src:l?l:f});n(r._openDialog).append(a);n(r.element).append(r._openDialog);o=new Image;o.onload=o.onabort=o.onerror=function(n){if(n){var t={path:n.target.src,element:n.target,originalArgs:n,action:"open"};r._trigger("getImage",t)}};o.src=a.attr("src");r._openDialog.ejDialog({width:450,height:350,minHeight:200,minWidth:300,maxWidth:"100%",isResponsive:r.model.isResponsive,target:r.element,closeIconTooltip:r._getLocalizedLabels("DialogCloseToolTip"),enableRTL:r.model.enableRTL,showRoundedCorner:r.model.showRoundedCorner,cssClass:r.model.cssClass,drag:function(n){r._dialogDragEvent(n)},close:function(n){r._onDialogClose(n)}}).parents(".e-dialog-wrap").addClass("e-imageViewer-wrap");r._openDialog.css("height","90%");r._openDialogObj=r._openDialog.data("ejDialog")}h=r._getSelectedItemDetails(r._currentPath,r._selectedContent)}v=this._nodeType=="File"?f:this._currentPath;y={path:v,itemType:this._nodeType,selectedItems:h};this._trigger("open",y)}},_dialogDragEvent:function(){this._splitObj&&this._splitObj._windowResized()},_getSelectedItemDetails:function(n,t){var f,e,o,i,u,r;if(typeof t=="string"&&(t=[t]),f=[],e=this.model.layout=="grid"?this._selectedRecords:this._suggestionItems,i=this._suggestionItems&&this._suggestionItems.length?e:this._fileExplorer[n],i)for(u=0;u<t.length;u++)for(r=0;r<i.length;r++)if(o=this._suggestionItems.length>0?i[r].filterPath+i[r].name:t[u],this._suggestionItems.length<1&&i[r].name==this._getOriginalName(t[u])||this._suggestionItems.length>0&&o==this._FilteredFiles[u]&&i[r].name==this._getOriginalName(t[u])){f.push(i[r]);break}return f},_getDetails:function(){var e=this._nodeType=="Directory"&&this._currentPath!="/"&&this._currentPath!="~/"?this._getFolderPath():this._currentPath,r=this,u,f;u=this.model.rootFolderName.length>0&&this._selectedContent==this.model.rootFolderName&&this._treeObj.element.find("li:first > div > .e-text").hasClass("e-active")&&this._selectedItems.length==0?[this._rootFolderName]:typeof this._selectedContent=="string"?[this._selectedContent]:this._selectedContent;u[0].endsWith("/")&&u[0]!="/"&&(u[0]=u[0].substring(0,u[0].length-1));f={data:{ActionType:"GetDetails",Path:e,Names:u,SelectedItems:this._getSelectedItemDetails(e,this.model.selectedItems.length?this.model.selectedItems:this._selectedContent),RootFolderPath:this._initPath,OldRootFolderName:this._rootFolderName,NewRootFolderName:this.model.rootFolderName},url:this.model.ajaxAction,type:"POST",async:!1,success:function(u){if(u.hasOwnProperty("d")&&(u=u.d),!t.isNullOrUndefined(u.error)){r._showErrorDialog(u.error);return}var c=t.buildTag("div.e-fe-table"),o=t.buildTag("table"),e,l,s,f,h,a;n.each(u.details[0],function(u,c){u=u[0].toUpperCase()+u.substr(1);e=t.buildTag("tr");l=t.buildTag("td",t.isNullOrUndefined(r._getLocalizedLabels(u))?u:r._getLocalizedLabels(u));s=t.buildTag("td");u=="Name"||u=="Location"?(f=t.buildTag("div"),h=t.buildTag("input.e-readonly","","",{style:"border:none;",type:"text",value:c,title:c,readonly:!0}),h.focus(function(){n(this).blur()}),f.append(h)):u=="Size"?(a=r._bytesToSize(c),f=t.buildTag("span",a+" ("+c+" Bytes)")):f=u=="Permission"?c!=i?t.buildTag("span",r._objToString(c),"",{style:"word-break: break-word;"}):null:t.buildTag("span",c);f!=i&&(u=="Permission"&&n(o).find("tr:last").addClass("e-border"),s.append(f),n(o).append(e),n(e).append(l,s),(u=="Name"||u=="Size")&&e.addClass("e-border"))});n(c).append(o);r._detailsDialog=r._createDialog(c,{width:500,height:"auto",title:r._getLocalizedLabels("Details")});r._detailsDialogObj=r._detailsDialog.data("ejDialog");r._detailsDialogObj.focus()},successAfter:this.model.ajaxSettings.getDetails.success};this.model.ajaxSettings.getDetails.success=i;n.extend(!0,f,this.model.ajaxSettings.getDetails);this._sendAjaxRequest(f,!0)},_objToString:function(n){var i="";for(var t in n)n.hasOwnProperty(t)&&(i+=t+": "+n[t]+", ");return i},_uploadFile:function(){var n=this.element.find(".e-uploadbox");n=n.ejUploadbox("instance")},_getFolderPath:function(n){for(var i=n?n.split("/"):this._currentPath.split("/"),r="",t=0;t<i.length-2;t++)r+=i[t]+"/";return r},_renderToolBar:function(){this._crateToolbarTemplate();this._initToolbarItems();var t={};t.click=n.proxy(this._toolBarClick,this);t.cssClass=this.model.cssClass;t.enableRTL=this.model.enableRTL;t.enableSeparator=!0;t.isResponsive=this.model.isResponsive;t.height=t.isResponsive?"":"auto";t.cssClass=this.model.cssClass+" e-fe-toolbar "+(this._isMobileOrTab?"e-fe-mobile":"");t.enableRTL=this.model.enableRTL;this._toolBarItems.ejToolbar(t);this._toolBarObj=this._toolBarItems.ejToolbar("instance");this._isMobileOrTab&&this._toolBarObj.model.isResponsive&&this._toolBarObj._liTemplte.css("max-width",this.element.width());this._disableToolbarItem("Paste");this._disableToolbarItem("Download");this._disableToolbarItem("Back");this._disableToolbarItem("Forward");this._disableEditingTools()},_initToolbarItems:function(){this._toolBarItems.find("#"+this._ExplorerId+"Addressbar").length>0&&this._createAddressBar();this._toolBarItems.find("#"+this._ExplorerId+"Searchbar").length>0&&this._searchDetails();this._toolBarItems.find("#"+this._ExplorerId+"Layout").length>0&&this._renderLayoutDrpdwn();this._toolBarItems.find("#"+this._ExplorerId+"SortBy").length>0&&this._renderSortbyDrpdwn()},_renderSortbyDrpdwn:function(){var f,e,u,i,r;for(this._showSortbyDDL=t.buildTag("button#"+this._ExplorerId+"_sortby","","",{"data-role":"none",type:"button"}),f=n("<ul id="+this._ExplorerId+"_splitMenu1 class='e-fe-split-context'>"),e=this.model.gridSettings.columns.length,u=0;u<e;u++)f.append(n("<li><a class=' e-arrow-space'><span class='e-icon'><\/span>"+this.model.gridSettings.columns[u].headerText+"<\/a><\/li>"));f.appendTo(this._toolBarItems.find("#"+this._ExplorerId+"SortBy").html(""));i={};r=this;i.height="24px";i.enableRTL=this.model.enableRTL;i.showRoundedCorner=this.model.showRoundedCorner;i.targetID=this._ExplorerId+"_splitMenu1";i.contentType="imageonly";i.buttonMode="dropdown";i.itemSelected=function(n){r._changeActiveSortedoption(n.text);r._sorting(r._prevsortingoption,r._prevsorting);r._sortingActioncomplete()};i.prefixIcon="e-icon e-fe-sortby";this._showSortbyDDL.appendTo(this._toolBarItems.find("#"+this._ExplorerId+"SortBy").addClass("e-fe-split-button"));this._showSortbyDDL.ejSplitButton(i);this._splitButtonObj1=this._showSortbyDDL.data("ejSplitButton")},_gridupdate:function(n){var i;i=this._prevsorting?"ascending":"descending";!t.isNullOrUndefined(this._gridObj)&&this.model.gridSettings.allowSorting&&(this._gridObj.getHeaderTable().find("[ej-mappingname="+n+"]").parent().attr("aria-sort",i),this._gridObj._addSortElementToColumn(n,i))},removeSortingIcons:function(){n("#"+this._ExplorerId+"_tileViewContextMenu").find("li span").removeClass("e-fe-ascending e-fe-descending");n("#"+this._ExplorerId+"_splitMenu1").find("li span").removeClass("e-fe-ascending e-fe-descending");var t=this._gridObj.getHeaderTable().find("[ej-mappingname=filterPath]").parent();this._prevsortingoption=="filterPath"?this._prevsorting?(t.attr("aria-sort","descending"),this._gridObj._addSortElementToColumn("filterPath","descending")):(t.attr("aria-sort","ascending"),this._gridObj._addSortElementToColumn("filterPath","ascending")):this._prevsorting?(t.attr("aria-sort","ascending"),this._gridObj._addSortElementToColumn("filterPath","ascending")):(t.attr("aria-sort","descending"),this._gridObj._addSortElementToColumn("filterPath","descending"));this._prevsorting=this._gridObj.getHeaderContent().find("span").hasClass("e-icon e-ascending")?!0:!1},_changeActiveSortedoption:function(i,r,u){var y,s,p,h,v,c,l,a,f,e,o;for(u==!0&&(this._prevsorting=!this._prevsorting),y=this.model.gridSettings.columns.length,p=n("#"+this._ExplorerId+"_tileViewContextMenu").find("li").length,h=0;h<p;h++)v=n("#"+this._ExplorerId+"_tileViewContextMenu").find("li")[h],v.id==this._ExplorerId+"_cwd_SortBy"&&(s=n(v).find("ul li"));for(c=n("#"+this._ExplorerId+"_splitMenu1").find("li"),n(s).find("span").removeClass("e-fe-ascending e-fe-descending"),n(c).find("span").removeClass("e-fe-ascending e-fe-descending"),o=0;o<y;o++)if(l=!t.isNullOrUndefined(s)&&s[o],a=!t.isNullOrUndefined(c)&&c[o],f=n(l).find("span"),e=n(a).find("span"),!t.isNullOrUndefined(l)&&n(l).text()==i||!t.isNullOrUndefined(a)&&n(a).find("a").text()==i){r?(f.addClass("e-fe-ascending"),e.addClass("e-fe-ascending")):this._prevsortingoption==this.model.gridSettings.columns[o].field?this._prevsorting?(f.addClass("e-fe-descending"),e.addClass("e-fe-descending")):(f.addClass("e-fe-ascending"),e.addClass("e-fe-ascending")):this._prevsorting?(f.addClass("e-fe-ascending"),e.addClass("e-fe-ascending")):(f.addClass("e-fe-descending"),e.addClass("e-fe-descending"));this._prevsortingoption=this.model.gridSettings.columns[o].field;this._prevsorting=f.hasClass("e-fe-ascending")||e.hasClass("e-fe-ascending")?!0:!1;break}},_suggestionitemsSorting:function(n,i){var u=this._suggestionItems,r=t.DataManager(u);this._prevsortingoption=n;this._sorteditems=i?r.executeLocal(t.Query().sortBy(n)):r.executeLocal(t.Query().sortByDesc(n))},_sorting:function(i,r,u){var s,o,e,h,y,f,p,c,l,a,v;t.support.stableSort=!1;s=t.isNullOrUndefined(u)?this._searchbar&&this._searchbar.val()!=""?this._suggestionItems.length>=0?this._suggestionItems:this._fileExplorer[this._originalPath]:this._fileExplorer[this._originalPath]:u;o=i=="type"?"name":i=="size"?"sizeInByte":i;e=t.DataManager(s);switch(i){case"type":case"dateModified":for(h=[],y=[],f=0;f<s.length;f++)h[f]=s[f].dateModified,y[f]=t.isNullOrUndefined(this._sortingDateFormat)?s[f].dateModified=new Date(h[f]):s[f].dateModified=t.parseDate(h[f],this._sortingDateFormat);for(e=t.DataManager(s),r?(c=e.executeLocal(t.Query().where("isFile",t.FilterOperators.equal,!1).sortBy(o)),l=e.executeLocal(t.Query().where("isFile",t.FilterOperators.equal,!0).sortBy(o)),this._sorteditems=c.concat(l)):(a=e.executeLocal(t.Query().where("isFile",t.FilterOperators.equal,!1).sortByDesc(o)),v=e.executeLocal(t.Query().where("isFile",t.FilterOperators.equal,!0).sortByDesc(o)),this._sorteditems=v.concat(a)),f=0;f<this._sorteditems.length;f++)p=n.inArray(this._sorteditems[f].dateModified,y),this._sorteditems[f].dateModified=n(h).eq(p)[0];break;case"name":case"size":default:r?(c=e.executeLocal(t.Query().where("isFile",t.FilterOperators.equal,!1).sortBy(o)),l=e.executeLocal(t.Query().where("isFile",t.FilterOperators.equal,!0).sortBy(o)),this._sorteditems=c.concat(l)):(a=e.executeLocal(t.Query().where("isFile",t.FilterOperators.equal,!1).sortByDesc(o)),v=e.executeLocal(t.Query().where("isFile",t.FilterOperators.equal,!0).sortByDesc(o)),this._sorteditems=v.concat(a))}},_sortingActioncomplete:function(){switch(this.model.layout){case t.FileExplorer.layoutType.Grid:this._gridObj.option("dataSource",this._sorteditems);break;case t.FileExplorer.layoutType.LargeIcons:case t.FileExplorer.layoutType.Tile:default:this._renderTileView(this._sorteditems,!0)}t.isNullOrUndefined(this._selectedItems)||this._selectedItems.length!=1||this._setSelectedItems(this._selectedContent);this._gridObj&&this._gridObj._scrollObject&&this._gridObj._scrollObject.refresh()},_renderLayoutDrpdwn:function(){var f,u,i,r;for(this._showLayoutDDL=t.buildTag("button#"+this._ExplorerId+"_layout","","",{"data-role":"none",type:"button"}),f=n("<ul id="+this._ExplorerId+"_splitMenu class='e-fe-split-context'>"),this._layoutList=["Tile","Grid","LargeIcons"],u=0;u<this._layoutList.length;u++)f.append(n("<li><a class=' e-arrow-space'><span class='e-icon e-fe-activeicon'><\/span>"+this._getLocalizedLabels(this._layoutList[u])+"<\/a><\/li>"));f.appendTo(this._toolBarItems.find("#"+this._ExplorerId+"Layout").html(""));i={};r=this;i.height="24px";i.enableRTL=this.model.enableRTL;i.showRoundedCorner=this.model.showRoundedCorner;i.targetID=this._ExplorerId+"_splitMenu";i.contentType="imageonly";i.buttonMode="dropdown";i.itemSelected=function(n){switch(n.text){case r._getLocalizedLabels("LargeIcons"):r.model.layout="largeicons";break;case r._getLocalizedLabels("Tile"):r.model.layout="tile";break;case r._getLocalizedLabels("Grid"):default:r.model.layout="grid"}r._switchLayoutView()};i.prefixIcon="e-icon e-fe-"+this.model.layout;this._showLayoutDDL.appendTo(this._toolBarItems.find("#"+this._ExplorerId+"Layout").addClass("e-fe-split-button"));this._showLayoutDDL.ejSplitButton(i);this._splitButtonObj=this._showLayoutDDL.data("ejSplitButton")},_changeLayoutActive:function(i){if(this._splitButtonObj&&this._splitButtonObj.option("prefixIcon","e-icon e-fe-"+i),this._toolBarItems&&this._toolBarItems.find("#"+this._ExplorerId+"Layout").length>0){n(n("#"+this._ExplorerId+"_splitMenu").find("li span").removeClass("e-fe-activeicon"));switch(i){case t.FileExplorer.layoutType.LargeIcons:n(n("#"+this._ExplorerId+"_splitMenu").find("li")[2]).find("span").addClass("e-fe-activeicon");break;case t.FileExplorer.layoutType.Tile:n(n("#"+this._ExplorerId+"_splitMenu").find("li")[0]).find("span").addClass("e-fe-activeicon");break;case t.FileExplorer.layoutType.Grid:default:n(n("#"+this._ExplorerId+"_splitMenu").find("li")[1]).find("span").addClass("e-fe-activeicon")}}},_createUploadBox:function(){this._uploadtag=t.buildTag("div#"+this._ExplorerId+"FileUpload","",{padding:"0px",height:"0px",width:"0px"});this.element.prepend(this._uploadtag);this._renderUploadBox();this._uploadtag.find(".e-inputbtn").hide();this._uploadtag.find(".e-uploadinput").attr("tabindex",-1)},_searchDetails:function(){this._isWatermark="placeholder"in document.createElement("input");var i=this._getLocalizedLabels("Search");this._searchbar=t.buildTag("input.e-searchBar e-tool-input","",{},{id:this._ExplorerId+"_searchbar",type:"text",placeholder:i});this._searchbar.appendTo(this._toolBarItems.find("#"+this._ExplorerId+"Searchbar").html(""));t.browserInfo().name=="msie"&&t.ieClearRemover(this._searchbar[0]);this._isWatermark||(this._hiddenSpan=t.buildTag("span.e-input e-placeholder ",i,{display:"block"}).insertAfter(this._searchbar));this._on(n("#"+this._ExplorerId+"_searchbar"),"focus",this._inputFocusin);this._on(n("#"+this._ExplorerId+"_searchbar"),"keyup",this._onSearchKeyup)},_setUploadLocalization:function(){n.each(t.FileExplorer.Locale,function(n,i){i.UploadSettings&&(t.Uploadbox.Locale[n]=i.UploadSettings)})},_getLocalizedLabels:function(n){return t.FileExplorer.Locale[this.model.locale]===i||t.FileExplorer.Locale[this.model.locale][n]===i?t.FileExplorer.Locale["en-US"][n]?t.FileExplorer.Locale["en-US"][n]:n:t.FileExplorer.Locale[this.model.locale][n]},_crateToolbarTemplate:function(){var i,n;for(this._toolBarItems=t.buildTag("div#"+this._ExplorerId+"_toolbar").prependTo(this.element),i=0;i<this.model.toolsList.length;i++)n=this.model.toolsList[i],t.isNullOrUndefined(this.model.tools[n])||(n=="customTool"?t.isNullOrUndefined(this.model.tools[n])||this._customTools(this.model.tools[n]):this.model.tools[n].length>0&&this._createToolsItems(this.model.tools[n],n))},_createToolsItems:function(i,r){var f=t.buildTag("ul#"+(this._ExplorerId+r)),e,u;for(f.addClass("e-ul-"+r),u=0;u<i.length;u++)e=n("<li id='"+(this._ExplorerId+i[u].replace(/ /g,""))+"' class='e-feItem-"+i[u]+"' title='"+this._getLocalizedLabels(i[u].replace(/ /g,""))+"' ><div class='e-fileexplorer-toolbar-icon "+i[u]+"'><\/div><\/li>"),e.appendTo(f);f.appendTo(this._toolBarItems)},_customTools:function(i){for(var e,f,u,r=0;r<i.length;r++)e=t.buildTag("ul"),f=n("<li id='"+(this._ExplorerId+i[r].name.replace(/ /g,""))+"' title='"+i[r].tooltip+"' ><div class='"+(t.isNullOrUndefined(i[r].css)?"":i[r].css)+"'><\/div><\/li>"),u=i[r].action,typeof u=="string"&&(u=t.util.getObject(u,window)),t.isNullOrUndefined(i[r].action)||this._on(f,"click",u),n(i[r].template).appendTo(f.find("div")),f.appendTo(e),e.appendTo(this._toolBarItems)},_toolBarClick:function(t){var i,r,u;if(this._hidePopup(),i=this,t.event.which&&(t.event.which==3||t.event.which==2)||t.event.button&&t.event.button==2)return!1;if(this.model.ajaxAction!=""&&this._currentPath!=""){this._lastFocusedElement=n(t.currentTarget);r=n(t.currentTarget);u=r.attr("id");switch(u){case this._ExplorerId+"Addressbar":case this._ExplorerId+"Searchbar":this._searchbar&&!this._isWatermark&&(this._searchbar.blur(function(){i._searchbar.val()||i._hiddenSpan.css("display","block")}),this._hiddenSpan.css("display","none"));t.event.type=="keyup"&&r.find("input").focus();break;case this._ExplorerId+"Download":this._downloadFile();break;case this._ExplorerId+"Upward":this._upward();break;case this._ExplorerId+"NewFolder":this._createNewFolder();break;case this._ExplorerId+"Delete":this._deleteFolder();break;case this._ExplorerId+"Rename":this._renameFolder();break;case this._ExplorerId+"Refresh":this._currentPath=this._originalPath;this._highlightedNodes=this.model.selectedItems;this._refreshItems(this._treeObj?this._treeObj.getSelectedNode():"",this._originalPath);break;case this._ExplorerId+"Back":this._backward();break;case this._ExplorerId+"Forward":this._forward();break;case this._ExplorerId+"Cut":this._copyMoveNode("move");break;case this._ExplorerId+"Copy":this._copyMoveNode("copy");break;case this._ExplorerId+"Paste":this._currentPath=this._originalPath;this._cut_copy();break;case this._ExplorerId+"Open":this._openAction();break;case this._ExplorerId+"Details":this._getDetails();break;case this._ExplorerId+"Upload":this.element.find(".e-uploadinput").click()}}},_upward:function(){this._treeObj&&this._treeObj.selectNode(this._treeObj.getSelectedNode().parent().closest("li.e-item"))},_getFilteredList:function(){if(this._suggestionItems=[],n.trim(this._queryString)){switch(this.model.filterSettings.filterType){case t.FileExplorer.filterType.StartsWith:this._queryString=this._queryString+"*";break;case t.FileExplorer.filterType.EndsWith:this._queryString="*"+this._queryString;break;case t.FileExplorer.filterType.Contains:this._queryString="*"+this._queryString+"*"}this._searchFiles(this._originalPath)}else this._suggestionItems=[],this._sorting(this._prevsortingoption,this._prevsorting,this._fileExplorer[this._originalPath]),this.model.layout=="grid"?this._renderGridView(this._sorteditems):this._renderTileView(this._sorteditems),this._updateItemStatus(this._fileExplorer[this._originalPath]);this._gridupdate(this._prevsortingoption)},_updateItemStatus:function(n){n&&this._itemStatus&&this._itemStatus.html(n.length+" "+(n.length==1?this._getLocalizedLabels("Item"):this._getLocalizedLabels("Items")))},_onSearchKeyup:function(n){var t=this,n=n;clearTimeout(this._searchTimer);this._searchTimer=setTimeout(function(){t._validateKeyCode(n)},300)},_validateKeyCode:function(n){var t;switch(n.which){case 38:case 40:case 37:case 39:case 20:case 16:case 17:case 18:case 35:case 36:case 144:break;case 27:this._searchbar&&this._searchbar.val("");this._queryString="";t=this._currentPath.replace(this._initPath,"");this.model.rootFolderName.length>0&&(t=t.replace(this.model.rootFolderName,this._rootFolderName));this._removeOldSelectionDetails();this._currentPath=this._currentPath.split(t)[0]+t;this._getFilteredList(this._fileExplorer[this._currentPath]);this.model.layout=="grid"?this._addFocus(this._gridtag.find(".e-gridcontent")):this._addFocus(this._tileViewWrapper);break;case 9:case 46:case 8:case 13:default:if(!this.model.filterSettings.allowSearchOnTyping&&n.which==13||this.model.filterSettings.allowSearchOnTyping){this._queryString=n.currentTarget.value;t=this._currentPath.replace(this._initPath,"");this.model.rootFolderName.length>0&&(t=t.replace(this.model.rootFolderName,this._rootFolderName));this._removeOldSelectionDetails();this._currentPath=this._currentPath.split(t)[0]+t;this._getFilteredList(this._fileExplorer[this._currentPath]);break}}},_onDialogClose:function(){n("body").find("#"+this._ExplorerId+"_basicDialog_wrapper").remove();this._lastFocusedElement&&this._lastFocusedElement.focus()},_switchView:function(n){var i=!1;n.currentTarget.getAttribute("id")==this._ExplorerId+"_swithListView"?this.model.layout!=t.FileExplorer.layoutType.LargeIcons&&(this.model.layout=t.FileExplorer.layoutType.LargeIcons,i=!0):this.model.layout!=t.FileExplorer.layoutType.Grid&&(this.model.layout=t.FileExplorer.layoutType.Grid,i=!0);i&&this._switchLayoutView()},_switchLayoutView:function(n){var i=!1,r=this._sorteditems,u;this._currentPath=this._originalPath;switch(this.model.layout){case t.FileExplorer.layoutType.LargeIcons:case t.FileExplorer.layoutType.Tile:this._gridtag.hide();this._tileContent.parent().show();this._tileViewWrapper.removeClass("e-tileInfo-view");this._statusbar&&(this._statusbar.find(".e-swithListView").removeClass("e-active"),this._statusbar.find(".e-switchGridView").removeClass("e-active"));this.model.layout==t.FileExplorer.layoutType.LargeIcons?this._statusbar&&this._statusbar.find(".e-swithListView").addClass("e-active"):(this._tileViewWrapper.addClass("e-tileInfo-view"),this.items=this._tileView.find("li.e-tilenode"),this._setThumbImageHeight());this._renderTileView(r);i=!0;break;case t.FileExplorer.layoutType.Grid:default:this._tileView&&this._tileContent.parent().hide();this._gridtag.show();this._renderGridView(r);this._statusbar&&(this._statusbar.find(".e-swithListView").removeClass("e-active"),this._statusbar.find(".e-switchGridView").addClass("e-active"));i=!0}this._changeLayoutActive(this.model.layout);i&&(this._updateItemStatus(r),this._setSelectedItems(this.model.selectedItems),u={layoutType:this.model.layout,isInteraction:!n},this._trigger("layoutChange",u))},_wireEvents:function(){this._on(n("#"+this._ExplorerId+"_newFolder"),"click",this._createNewFolder);this._on(n("#"+this._ExplorerId+"_switchGridView"),"click",this._switchView);this._on(n("#"+this._ExplorerId+"_swithListView"),"click",this._switchView);this._on(this.element,"keydown",this._keyDownOnInput);this._on(this._gridtag,"click",this._gridtagClick);this._on(this._tileViewWrapper,"click",this._tileViewWrapperClick);this.model.allowDragAndDrop&&this._toolBarItems&&this._on(this._toolBarItems,"dragover",this._preventDropOption)},_preventDropOption:function(n){n.preventDefault();n.stopPropagation();n.originalEvent.dataTransfer.dropEffect="none"},_onHideContextMenu:function(){this._treeObj&&this._treeObj.element.find(".e-node-focus").removeClass("e-node-focus")},_adjustSize:function(n){this._isWindowResized=n?!0:!1;this.adjustSize()},_closeDialog:function(t){this._unwireDialogEvent(t);n(t.target).closest("div.e-dialog").find(".e-dialog").ejDialog("close")},_searchPath:function(i){var o=this._getKeyCode(i),r;switch(o){case 13:this._searchbar&&this._searchbar.val("");var e=this._addresstag.val(),u=e.split("/"),f=this._treetag;for(this.model.path=="/"&&(u[0]="/"),r=0;r<u.length;r++)u[r]&&(f=this._findMatchingElement(n(f).children("ul"),u[r]),f.length?this._treetag.ejTreeView("selectNode",f):(this._setSelectedItems([u[r]]),this.model.selectedItems.length?this._openAction():(this._alertDialog=this._createDialog(t.buildTag("div.e-fe-dialog-label",String.format(this._getLocalizedLabels("ErrorPath"),e)),{width:400,height:"auto",title:this._getLocalizedLabels("Error")}),this._alertDialogObj=this._alertDialog.data("ejDialog"))));break;case 27:i.preventDefault();this._updateAddressBar();this._toolBarItems.focus()}},_addressbarFocusout:function(){this._updateAddressBar()},_createDialog:function(i,r){var u=this,f=t.buildTag("div#"+this._ExplorerId+"_basicDialog.e-fe-dialog");return n(i).css("overflow","hidden"),n(f).append(i),f.ejDialog({title:r.title?r.title:"",width:r.width,maxWidth:"100%",isResponsive:u.model.isResponsive,target:u.element,closeIconTooltip:u._getLocalizedLabels("DialogCloseToolTip"),height:r.height,enableModal:!0,showHeader:!0,enableResize:!1,enableAnimation:!1,allowKeyboardNavigation:u.model.allowKeyboardNavigation,enableRTL:u.model.enableRTL,showRoundedCorner:u.model.showRoundedCorner,cssClass:this.model.cssClass+" e-fe-dialog",open:r.open,drag:function(n){u._dialogDragEvent(n)},close:function(n){u._onDialogClose(n)}}),f},_showErrorDialog:function(n){this._alertDialog=this._createDialog(t.buildTag("div.e-fe-dialog-label",n),{width:400,height:"auto",title:this._getLocalizedLabels("Error")});this._alertDialogObj=this._alertDialog.data("ejDialog")},_findMatchingElement:function(t,i){return n(t).children("li").filter(function(){if(n(this).find(".e-text:first").text()==i)return n(this)})},_getKeyCode:function(n){return n.keyCode?n.keyCode:n.which?n.which:n.charCode},_keyDownOnInput:function(i){var f,r,u,i;if(!n(i.target).hasClass("e-tool-input")&&(f=this._getKeyCode(i),this.model.allowKeyboardNavigation)&&!this._KeydownEventHandler(i))switch(f){case 49:i.shiftKey&&i.ctrlKey&&this.model.showToolbar&&(i.preventDefault(),this._addFocus(this._toolBarItems));break;case 50:i.shiftKey&&i.ctrlKey&&this.model.showNavigationPane&&(i.preventDefault(),this._addFocus(this._treetag));break;case 51:i.shiftKey&&i.ctrlKey&&(i.preventDefault(),this._addFocus(n(this._splittag.find(".e-splitbar")[0])));break;case 52:i.shiftKey&&i.ctrlKey&&(i.preventDefault(),this._changeLayout(t.FileExplorer.layoutType.Grid));break;case 53:i.shiftKey&&i.ctrlKey&&(i.preventDefault(),this._changeLayout(t.FileExplorer.layoutType.Tile));break;case 54:i.shiftKey&&i.ctrlKey&&(i.preventDefault(),this._changeLayout(t.FileExplorer.layoutType.LargeIcons));break;case 55:i.shiftKey&&i.ctrlKey&&this.model.showFooter&&(i.preventDefault(),this._addFocus(this._statusbar));break;case 65:if(i.ctrlKey)if(i.preventDefault(),this.model.layout=="grid")r=this.gridItems,this._gridObj.clearSelection(),this._removeOldSelectionDetails(),this._addFocus(this._gridtag.find(".e-gridcontent")),this._gridObj.selectRows(0,r.length-1,n(r[r.length-1]).find("td:first"));else for(r=this.items,this.items.hasClass("e-active")&&this.items.removeClass("e-active").attr("aria-selected",!1),this._removeOldSelectionDetails(),this._addFocus(this._tileViewWrapper),u=0;u<r.length;u++)i={keyCode:91,ctrlKey:!0,currentTarget:r[u],target:r[u]},this._upDatePathFromTileView(i);break;case 78:i.altKey&&this._hasEditContentsPermission(this._originalPath)&&(i.preventDefault(),this._createNewFolder());break;case 85:i.ctrlKey&&this._toUpload&&(i.preventDefault(),this.element.find(".e-uploadinput").click());break;case 116:i.preventDefault();this._currentPath=this._originalPath;this._highlightedNodes=this.model.selectedItems;this._refreshItems(this._treeObj?this._treeObj.getSelectedNode():"",this._originalPath)}},_changeLayout:function(n){this.model.layout!=n&&(this.model.layout=n,this._removeFocus(),this._switchLayoutView(n));this._focusLayout(n)},_focusLayout:function(n){switch(n){case t.FileExplorer.layoutType.Grid:this._addFocus(this._gridtag.find(".e-gridcontent"));break;case t.FileExplorer.layoutType.Tile:case t.FileExplorer.layoutType.LargeIcons:default:this._addFocus(this._tileViewWrapper)}},_getFocusedElement:function(){var t=this.element.find(".e-focus");return t?t:n(":focus")},_addFocus:function(n){n.hasClass("e-focus")||(this._removeFocus(),n.focus())},_removeFocus:function(){var n=this._getFocusedElement();n.length>0&&n.blur()},_subControlsSetModel:function(n,t){var i={};i[n]=t;this._treeObj&&this._treeObj.option(n,t);this._downloadDialogObj&&this._downloadDialogObj.isOpen()&&this._downloadDialogObj.option(n,t);this._newFolderDialogObj&&this._newFolderDialogObj.isOpen()&&this._newFolderDialogObj.option(n,t);this._renameDialogObj&&this._renameDialogObj.isOpen()&&this._renameDialogObj.option(n,t);this._openDialogObj&&this._openDialogObj.isOpen()&&this._openDialogObj.option(n,t);this._detailsDialogObj&&this._detailsDialogObj.isOpen()&&this._detailsDialogObj.option(n,t);this._alertDialogObj&&this._alertDialogObj.isOpen()&&this._alertDialogObj.option(n,t);this._alertWindowObj&&this._alertWindowObj.isOpen()&&this._alertWindowObj.option(n,t);this._treeContextMenutag&&this._treeMenuObj.option(n,t);this._tileContextMenutag&&this._viewMenuObj.option(n,t);this._toolBarObj&&this._toolBarObj.option(n,t);this._uploadtag&&this._uploadtag.data("ejUploadbox").option(n,t);this._splitObj&&this._splitObj.option(n,t);this._splitButtonObj&&this._splitButtonObj.option(n,t);this._splitButtonObj1&&this._splitButtonObj1.option(n,t);this._statusbar.find("button").length&&this._statusbar.find("button").ejButton(i);this._headCheckObj&&this._headCheckObj.option(n,t);this._tileView.find(".e-tile-checkbox").length&&this._tileView.find(".e-tile-checkbox").ejCheckBox(i);this._gridtag.find(".e-grid-row-checkbox").length&&this._gridtag.find(".e-grid-row-checkbox").ejCheckBox(i)},_removeDialog:function(n){n.close();var t=n._overLay;t&&t.remove();n._ejDialog.remove()},_reSizeHandler:function(){if(this._splitterCorrection(),this.model.ajaxAction!=""&&this._currentPath!=""){if(this.model.layout=="grid"){var n=this._splittag.outerHeight()-this._gridtag.find(".e-gridheader").outerHeight();this._gridObj&&this._gridObj.option("scrollSettings",{height:this.model.showFooter?n-this._statusbar.outerHeight():n,width:this._splittag.find(".e-cont2").width()})}else this._tileScroll&&this._tileScroll.option("width",parseInt(this._splittag.find(".e-cont2").width())),this._setThumbImageHeight();this._treeScroll&&this._treeScroll.option("width",parseInt(this._splittag.find(".e-cont1").width()));this._waitingPopup&&this._waitingPopup.refresh()}},_splitterCorrection:function(){var n=this._splittag.find(this.model.enableRTL?".e-cont2":".e-cont1").width()-1;this._splittag.find(".e-split-divider").css("left",n).css("z-index",1)},_findCommand:function(t,i,r){var u=this;return n.each(i,function(n,i){for(var f=0,e=i.length;f<e;f++)if(i[f].toLowerCase()==t.toLowerCase())return t=r?u["_menu"+i[f]]:i[f],!1}),t},_getElement:function(t){return typeof t=="string"&&(t=this._findCommand(t,this.model.tools)),typeof t!="object"&&(t=typeof t=="string"?this._toolBarObj.itemsContainer.find("li#"+t).length>0?this._toolBarObj.itemsContainer.find("li#"+t):this._toolBarObj.itemsContainer.find("li#"+this._ExplorerId+t.replace(/ /g,"")):this._toolBarObj.itemsContainer.find("li").eq(t)),t=n(t),n(t[0])},_updateToolbar:function(){this._renderToolBar();this._selectedStates.length>=2&&this._enableToolbarItem("Back");this._updateAddressBar();this._selectedItems.length>0&&this._toRead&&(this._toDownload&&this._enableToolbarItem("Download"),this._enableEditingTools(),this._toCopy&&this._enableToolbarItem("Copy"));t.isNullOrUndefined(this._fileName)||this._fileName==""||this._toRead&&this._enableToolbarItem("Paste")},_enableEditingMenus:function(){for(var t=[this._menuRename,this._menuDelete,this._menuCut],n=0;n<t.length;n++)this._restrictedMenuOption.indexOf(t[n])<0&&(this._viewMenuObj&&this._viewMenuObj.enableItem(t[n]),this._treeMenuObj&&this._treeMenuObj.enableItem(t[n]))},_disableEditingMenus:function(){for(var t=[this._menuRename,this._menuDelete,this._menuCut],n=0;n<t.length;n++)this._viewMenuObj&&this._viewMenuObj.disableItem(t[n]),this._treeMenuObj&&this._treeMenuObj.disableItem(t[n])},_updateSelectionDetails:function(n){this._selectedItems.length==0?this._removeOldSelectionDetails(n):(this._updateToolbarItems(),this._updateNewFolderTool(this._hasEditContentsPermission(this._originalPath)))},_updatePasteTool:function(){this._toolBarItems&&(this._option&&this._toRead?this._enableToolbarItem("Paste"):this._disableToolbarItem("Paste"))},_updateAccessRules:function(n){var t=this._feParent[n];this._updateAccessValue(t)},_updateAccessValue:function(n){t.isNullOrUndefined(n)||t.isNullOrUndefined(n.permission)||(n.permission.Copy||(this._toCopy=!1),n.permission.Download||(this._toDownload=!1),n.permission.Edit||(this._toEdit=!1),n.permission.EditContents||(this._toEditContents=!1),n.permission.Read||(this._toRead=!1),n.permission.Upload||(this._toUpload=!1))},_updateToolbarItems:function(){this._toolBarItems&&(this._toRead&&this._toDownload?this._enableToolbarItem("Download"):this._disableToolbarItem("Download"),this._toRead&&this._toUpload?this._enableToolbarItem("Upload"):this._disableToolbarItem("Upload"),this._toRead&&this._toEdit?this._enableEditingTools():this._disableEditingTools(),this._toRead&&this._toCopy?this._enableToolbarItem("Copy"):this._disableToolbarItem("Copy"),this._updatePasteTool())},_getFilePermission:function(n){return this._feParent[n]?this._feParent[n].permission:null},_updateCurrentPathPermission:function(){this._removeOldSelectionDetails();this._toDownload=!1;this._toUpload=this._toEdit=this._toEditContents=this._toRead=this._toCopy=!0;this._updateAccessRules(this._originalPath);this._toolBarItems&&(this._toUpload?this._enableToolbarItem("Upload"):this._disableToolbarItem("Upload"));this._updateNewFolderTool(this._hasEditContentsPermission(this._originalPath));this._disableToolbarItem("Copy");this._updatePasteTool()},_updateNewFolderTool:function(n){this._toolBarItems&&(n?this._enableToolbarItem("NewFolder"):this._disableToolbarItem("NewFolder"))},_hasEditContentsPermission:function(n){var t=this._getFilePermission(n);return t?t.Read&&t.EditContents?!0:!1:!0},_hasReadPermission:function(n){var t=this._getFilePermission(n);return t&&!t.Read?!1:!0},_changeRootFolderName:function(){if(this.model.rootFolderName.length!=0){var n=this._treeObj.element.find("li:first > div > .e-text");this._treeObj.updateText(n,this.model.rootFolderName)}},_changeName:function(n,t){var i;return i=t?n.replace(this._initPath,"").replace(this.model.rootFolderName,this._rootFolderName):n.replace(this._initPath,"").replace(this._rootFolderName,this.model.rootFolderName),this._initPath+i},_unwireEvents:function(){this._off(n("#"+this._ExplorerId+"_newFolder"),"click",this._createNewFolder);this._off(n("#"+this._ExplorerId+"_switchGridView"),"click",this._switchView);this._off(n("#"+this._ExplorerId+"_swithListView"),"click",this._switchView);this._addressBarEvents("_off");this._off(n("#"+this._ExplorerId+"_searchbar"),"focus",this._inputFocusin);this._off(n("#"+this._ExplorerId+"_searchbar"),"keyup",this._onSearchKeyup);this._off(this.element,"keydown",this._keyDownOnInput);this._off(this._gridtag,"click");this._off(this._tileViewWrapper,"click");this.model.isResponsive&&this._off(n(window),"resize",this._adjustSize);this._toolBarItems&&this._off(this._toolBarItems,"dragover",this._preventDropOption)},_wireResizing:function(){this.model.isResponsive?this._on(n(window),"resize",this._adjustSize):this._off(n(window),"resize",this._adjustSize)},adjustSize:function(){this._ensureResolution();!this._isWindowResized&&this.model.showToolbar&&this.model.isResponsive&&this._toolBarObj._reSizeHandler();this._isWindowResized=!1;this._splittag.css("height",this.element.height()-(this.model.showToolbar&&this._toolBarItems?this._toolBarItems.outerHeight():0));var n=this._splittag.outerHeight()-(this.model.showFooter?this._statusbar.outerHeight():0);this._tileContent&&this._tileContent.parent(".e-tile-wrapper").height(n);this._tileScroll&&this._tileScroll.option("height",n);this._gridtag&&this._gridtag.height(n);this._splitObj&&this._splitObj._windowResized();this._treeScroll&&this._treeScroll.option("height",this._splittag.height());this._reSizeHandler()},refresh:function(){this._refreshItems(this._selectedNode,this._currentPath);this._splitObj.refresh()},enableToolbarItem:function(n){this._removeRestrictedToolItem(n);n=this._getElement(n);n[0]!=null&&this._toolBarObj&&this._toolBarObj.enableItem(n)},disableToolbarItem:function(n){if(n=this._getElement(n),n[0]!=null&&this._toolBarObj){var t=n.attr("id").replace(this._ExplorerId,"");this._restrictedToolbarOptions.push(t);this._toolBarObj.disableItem(n)}},removeToolbarItem:function(n){this._removeRestrictedToolItem(n);n=this._getElement(n);n[0]!=null&&this._toolBarObj&&this._toolBarObj.removeItem(n)},_removeRestrictedToolItem:function(n){for(var i=typeof n=="string"?n:n.attr("id").replace(this._ExplorerId,""),t=0;t<this._restrictedToolbarOptions.length;t++)if(this._restrictedToolbarOptions[t]==i){this._restrictedToolbarOptions.splice(t,1);break}},_onThumbStart:function(t){this.model.allowMultiSelection&&t.originalEvent.type!="touchstart"&&(t.cancel=!0);var i=n(t.originalEvent.target);if(i.hasClass("e-draggable")||i.closest(".e-draggable").length)return t.cancel=!0,!1}});t.FileExplorer.Locale={};t.FileExplorer.Locale["en-US"]={Folder:"Folder",EmptyFolder:"This folder is empty",ProtectedFolder:"You don't currently have permission to access this folder",EmptyResult:"No items match your search",Back:"Backward",Forward:"Forward",Upward:"Upward",Refresh:"Refresh",Addressbar:"Address bar",Upload:"Upload",Rename:"Rename",Delete:"Delete",Download:"Download",Error:"Error",PasteError:"Error",UploadError:"Error",RenameError:"Error",Cut:"Cut",Copy:"Copy",Paste:"Paste",Details:"Details",Searchbar:"Search bar",Open:"Open",Search:"Search",NewFolder:"New folder",SortBy:"Sort by",Size:"Size",RenameAlert:"Please enter new name",NewFolderAlert:"Please enter new folder name",ContextMenuOpen:"Open",ContextMenuNewFolder:"New folder",ContextMenuDelete:"Delete",ContextMenuRename:"Rename",ContextMenuUpload:"Upload",ContextMenuDownload:"Download",ContextMenuCut:"Cut",ContextMenuCopy:"Copy",ContextMenuPaste:"Paste",ContextMenuGetinfo:"Get info",ContextMenuRefresh:"Refresh",ContextMenuOpenFolderLocation:"Open folder location",Item:"item",Items:"items",Selected:"selected",ErrorOnFolderCreation:"This destination already contains a folder named '{0}'. Do you want to merge this folder content with already existing folder '{0}'?",InvalidFileName:"A file name can't contain any of the following characters: \\/:*?\"<>|",GeneralError:"Please see browser console window for more information",ErrorPath:"FileExplorer can't find '{0}'. Check the spelling and try again.",PasteReplaceAlert:"File named '{0}' already exists. Replace old file with new one?",UploadReplaceAlert:"File named '{0}' already exists. Replace old file with new one?",DuplicateAlert:"There is already a file with the same name '{0}'. Do you want to create file with duplicate name",DuplicateFileCreation:"There is already a file with the same name in this location. Do you want to rename '{0}' to '{1}'?",DeleteFolder:" Are you sure you want to delete ",DeleteMultipleFolder:"Are you sure you want to delete these {0} items?",CancelPasteAction:"The destination folder is a subfolder of source folder.",OkButton:"OK",ContextMenuSortBy:"Sort by",CancelButton:"Cancel",YesToAllButton:"Yes to all",NoToAllButton:"No to all",YesButton:"Yes",NoButton:"No",SkipButton:"Skip",Grid:"Grid view",Tile:"Tile view",LargeIcons:"Large icons",Name:"Name",Location:"Location",Type:"Item type",Layout:"Layout",Created:"Created",Accessed:"Accessed",Modified:"Modified",Permission:"Permission",DialogCloseToolTip:"Close",UploadSettings:{buttonText:{upload:"Upload",browse:"Browse",cancel:"Cancel",close:"Close"},dialogText:{title:"Upload Box",name:"Name",size:"Size",status:"Status"},dropAreaText:"Drop files or click to upload",filedetail:"The selected file size is too large. Please select a file within the valid size.",denyError:"Files with #Extension extensions are not allowed.",allowError:"Only files with #Extension extensions are allowed.",cancelToolTip:"Cancel",removeToolTip:"Remove",retryToolTip:"Retry",completedToolTip:"Completed",failedToolTip:"Failed",closeToolTip:"Close"}};t.FileExplorer.filterType={StartsWith:"startswith",Contains:"contains",EndsWith:"endswith"};t.FileExplorer.layoutType={Tile:"tile",Grid:"grid",LargeIcons:"largeicons"}})(jQuery,Syncfusion)});
