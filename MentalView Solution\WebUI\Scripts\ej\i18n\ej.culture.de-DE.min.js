/*!
*  filename: ej.culture.de-DE.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/

ej.addCulture("de-DE", { name: "de-DE", englishName: "German (Germany)", nativeName: "Deutsch (Deutschland)", language: "de", numberFormat: { pattern: ["-n"], ",": ".", ".": ",", groupSizes: [3], negativeInfinity: "-unendlich", positiveInfinity: "+unendlich", percent: { pattern: ["-n%", "n%"], groupSizes: [3], ",": ".", ".": ",", symbol: "%" }, currency: { pattern: ["-n $", "n $"], groupSizes: [3], ",": ".", ".": ",", symbol: "€" } }, calendars: { standard: { "/": ".", firstDay: 1, days: { names: ["<PERSON>nta<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>eitag", "<PERSON>stag"], namesAbbr: ["So", "Mo", "Di", "Mi", "Do", "Fr", "Sa"], namesShort: ["So", "Mo", "Di", "Mi", "Do", "Fr", "Sa"] }, months: { names: ["Januar", "Februar", "März", "April", "Mai", "Juni", "Juli", "August", "September", "Oktober", "November", "Dezember", ""], namesAbbr: ["Jan", "Feb", "Mrz", "Apr", "Mai", "Jun", "Jul", "Aug", "Sep", "Okt", "Nov", "Dez", ""] }, AM: null, PM: null, patterns: { d: "dd.MM.yyyy", D: "dddd, d. MMMM yyyy", t: "HH:mm", T: "HH:mm:ss", f: "dddd, d. MMMM yyyy HH:mm", F: "dddd, d. MMMM yyyy HH:mm:ss", M: "d. MMMM" } } } });;