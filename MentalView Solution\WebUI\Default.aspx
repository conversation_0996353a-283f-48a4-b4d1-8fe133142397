﻿<%@ Page Language="C#" MasterPageFile="~/Main.master" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="WebUI.Default" meta:resourcekey="Page" Async="true" %>

<asp:Content ID="mainHeadContent" ContentPlaceHolderID="mainHead" runat="server">
</asp:Content>
<asp:Content ID="mainBodyContent" ContentPlaceHolderID="mainBody" runat="server">
    <br />
    <div class="row">
        <div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-aqua"><i class="fa fa-users"></i></span>

                <div class="info-box-content">
                    <span class="info-box-text">
                        <asp:Literal ID="contactsTitleLbl" runat="server" meta:resourceKey="contactsTitleLbl"></asp:Literal></span>
                    <span class="info-box-number">
                        <asp:Literal ID="contactsCountLbl" runat="server"></asp:Literal></span>
                </div>
                <!-- /.info-box-content -->
            </div>
            <!-- /.info-box -->
        </div>
        <!-- /.col -->
        <div runat="server" id="calendarDiv" class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-green"><i class="fa fa-calendar"></i></span>

                <div class="info-box-content">
                    <span class="info-box-text">
                        <asp:Literal ID="futureTasksTitleLbl" runat="server" meta:resourceKey="futureTasksTitleLbl"></asp:Literal></span>
                    <span class="info-box-number">
                        <asp:Literal ID="futureTasksCountLbl" runat="server"></asp:Literal></span>
                </div>
                <!-- /.info-box-content -->
            </div>
            <!-- /.info-box -->
        </div>
        <!-- /.col -->
        <div runat="server" id="waitingContactsDiv" class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-orange"><i class="fa fa-clock"></i></span>

                <div class="info-box-content">
                    <span class="info-box-text">
                        <asp:Literal ID="waitingContactsTitleLbl" runat="server" meta:resourceKey="waitingContactsTitleLbl"></asp:Literal></span>
                    <span class="info-box-number">
                        <asp:Literal ID="waitingContactsCountLbl" runat="server"></asp:Literal></span>
                </div>
                <!-- /.info-box-content -->
            </div>
            <!-- /.info-box -->
        </div>



        <%--<asp:Button ID="Button2" OnClick="Button2_Click" runat="server" Text="Button" />--%>
        <!-- /.col -->
        <%--<div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-yellow"><i class="fa fa-files-o"></i></span>

                <div class="info-box-content">
                    <span class="info-box-text">Uploads</span>
                    <span class="info-box-number">13,648</span>
                </div>
                <!-- /.info-box-content -->
            </div>
            <!-- /.info-box -->
        </div>--%>
        <!-- /.col -->
        <%--<div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-red"><i class="fa fa-star-o"></i></span>

                <div class="info-box-content">
                    <span class="info-box-text">Likes</span>
                    <span class="info-box-number">93,139</span>
                </div>
                <!-- /.info-box-content -->
            </div>
            <!-- /.info-box -->
        </div>--%>
        <!-- /.col -->
    </div>
    <div class="row">
        <div class="col-xs-12">
            <h4>
                <asp:Literal ID="appointmentsWithImportantNotesHeaderLbl" runat="server" meta:resourceKey="appointmentsWithImportantNotesHeaderLbl"></asp:Literal>
            </h4>
            <ej:Grid ID="importanNotesGrid" runat="server" Selectiontype="Single" AllowFiltering="false" EnablePersistence="false" AllowPaging="true" Locale="el-GR" AllowSorting="true" Height="250" OnServerRecordDoubleClick="importanNotesGrid_ServerRecordDoubleClick" OnServerCommandButtonClick="importanNotesGrid_ServerCommandButtonClick">
                <Columns>
                    <ej:Column Field="AppoitmentId" IsPrimaryKey="true" Type="string" Visible="false" />
                    <ej:Column Field="StartTime" HeaderText="Ημ/νία" Type="string" Format="{0:dd/MM/yyyy}" Width="110" AllowFiltering="false" />
                    <ej:Column Field="StartTime" HeaderText="Έναρξη" Type="string" Format="{0:HH:mm}" Width="80" AllowFiltering="false" />
                    <ej:Column Field="EndTime" HeaderText="Ληξη" Type="string" Format="{0:HH:mm}" Width="80" AllowFiltering="false" />
                    <ej:Column Field="ImportantNotes" HeaderText="Σημαντικές Σημείωσεις" Type="string" />
                    <ej:Column HeaderText=" " IsUnbound="True" Width="60" AllowResizing="False" AllowSorting="False" AllowTextWrap="false">
                        <Command>
                            <ej:Commands Type="Edit">
                                <ButtonOptions Size="Small" Text="Edit" Type="Button" ContentType="ImageOnly" PrefixIcon="e-icon e-edit" Width="30"></ButtonOptions>
                            </ej:Commands>
                        </Command>
                        <Command></Command>
                    </ej:Column>
                </Columns>
            </ej:Grid>
        </div>
    </div>
</asp:Content>
