using Data.MentalViewDataSetTableAdapters;
using System;
using System.CodeDom;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Hosting;
using System.Text.Json;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Data
{
    public class Business
    {
        private static SqlConnection staticConnection;
        private static string connectionString;
        //private static FieldValuesMappings fieldValuesMappings;

        public static string ConnectionString
        {
            get
            {
                return connectionString;
            }
            set
            {
                connectionString = value;
                staticConnection = new SqlConnection(connectionString);
            }
        }

        public static SqlConnection Connection
        {
            get
            {
                return staticConnection;
            }
            set
            {
                staticConnection = value;
            }
        }

        //public static FieldValuesMappings FieldValuesMappings
        //{
        //    get
        //    {
        //        return fieldValuesMappings;
        //    }
        //    set
        //    {
        //        fieldValuesMappings = value;    
        //    }
        //}

        public Business()
        {
            //Properties.Settings.Default.Reload();
            this.InitializeMembers();
        }

        static Business()
        {
            //fieldValuesMappings = new FieldValuesMappings();

        }

        private void InitializeMembers()
        {
            if (staticConnection != null)
            {
                staticConnection.Close();
                staticConnection.Dispose();
                staticConnection = null;
            }
            staticConnection = new SqlConnection(connectionString);


        }

        public static void ConfigureDataSet(ref MentalViewDataSet ds)
        {
            //Tenants
            foreach (DataColumn column in ds.Tenants.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
                else if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
            }

            //Contacts
            foreach (DataColumn column in ds.Contacts.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
            }
            ds.Contacts.BirthDateColumn.AllowDBNull = true;
            ds.Contacts.BirthDateColumn.DefaultValue = null;
            ds.Contacts.PhotoColumn.AllowDBNull = true;
            ds.Contacts.ActiveColumn.DefaultValue = true;

            //ContactEmailTemplates
            foreach (DataColumn column in ds.ContactEmailTemplates.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
            }

            //Roles
            foreach (DataColumn column in ds.Roles.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
            }

            //Users
            foreach (DataColumn column in ds.Users.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = 0;
                }
            }

            //Appointments
            foreach (DataColumn column in ds.Appointments.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
                else if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
                else if (column.DataType == typeof(int))
                {
                    column.DefaultValue = 1;
                }
            }
            ds.Appointments.RecurrenceIdColumn.AllowDBNull = true;
            ds.Appointments.ContactIdColumn.AllowDBNull = true;

            //Questionnaires
            foreach (DataColumn column in ds.Questionnaires.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
            }

            //QuestionnaireQuestions
            foreach (DataColumn column in ds.QuestionnaireQuestions.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
            }

            //Partners
            foreach (DataColumn column in ds.Partners.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
            }

            //Links
            foreach (DataColumn column in ds.Links.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                else if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
            }
        }

        public static void InitTableAdapterManager(ref MentalViewDataSetTableAdapters.TableAdapterManager taManager, SqlConnection connection)
        {
            taManager = new MentalViewDataSetTableAdapters.TableAdapterManager();

            MentalViewDataSetTableAdapters.TenantsTableAdapter tenantsTA = new MentalViewDataSetTableAdapters.TenantsTableAdapter();
            tenantsTA.Connection = connection;
            MentalViewDataSetTableAdapters.ContactsTableAdapter contactsTA = new MentalViewDataSetTableAdapters.ContactsTableAdapter();
            contactsTA.Connection = connection;
            MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
            rolesTA.Connection = connection;
            MentalViewDataSetTableAdapters.UsersTableAdapter usersRA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
            usersRA.Connection = connection;
            MentalViewDataSetTableAdapters.AppointmentsTableAdapter appointmentsTA = new MentalViewDataSetTableAdapters.AppointmentsTableAdapter();
            appointmentsTA.Connection = connection;
            MentalViewDataSetTableAdapters.AppointmentCategoriesTableAdapter appointmentCategoriesTA = new MentalViewDataSetTableAdapters.AppointmentCategoriesTableAdapter();
            appointmentCategoriesTA.Connection = connection;
            MentalViewDataSetTableAdapters.QuestionnairesTableAdapter questionnairesTA = new MentalViewDataSetTableAdapters.QuestionnairesTableAdapter();
            questionnairesTA.Connection = connection;
            MentalViewDataSetTableAdapters.QuestionnaireQuestionsTableAdapter questionnaireQuestionsTA = new MentalViewDataSetTableAdapters.QuestionnaireQuestionsTableAdapter();
            questionnaireQuestionsTA.Connection = connection;
            MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter emailTemplatesTA = new MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter();
            emailTemplatesTA.Connection = connection;
            MentalViewDataSetTableAdapters.ContactEmailTemplatesTableAdapter contactEmailTemplatesTA = new MentalViewDataSetTableAdapters.ContactEmailTemplatesTableAdapter();
            contactEmailTemplatesTA.Connection = connection;


            taManager.Connection = connection;
            taManager.ContactsTableAdapter = contactsTA;
            taManager.TenantsTableAdapter = tenantsTA;
            taManager.RolesTableAdapter = rolesTA;
            taManager.UsersTableAdapter = usersRA;
            taManager.AppointmentsTableAdapter = appointmentsTA;
            taManager.AppointmentCategoriesTableAdapter = appointmentCategoriesTA;
            taManager.QuestionnairesTableAdapter = questionnairesTA;
            taManager.QuestionnaireQuestionsTableAdapter = questionnaireQuestionsTA;
            taManager.EmailTemplatesTableAdapter = emailTemplatesTA;
            taManager.ContactEmailTemplatesTableAdapter = contactEmailTemplatesTA;
        }

        public static void SaveAllData(MentalViewDataSet ds)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSetTableAdapters.TableAdapterManager taManager = new MentalViewDataSetTableAdapters.TableAdapterManager();
                    InitTableAdapterManager(ref taManager, connection);

                    using (taManager)
                    {
                        taManager.UpdateAll(ds);

                        ds.AcceptChanges();
                    }
                }
            }
            catch (Exception exp)
            {
                ds.RejectChanges();
                throw exp;
            }
            finally
            {

            }
        }
      
        public class GeneralBusiness
        {
            public static DashboardInfo GetDashboardInfo(Int64 tenantId, Int64 userId)
            {
                DataSet ds = new DataSet();

                DashboardInfo info = new DashboardInfo();
                try
                {
                    MentalViewDataSet userDS = Data.Business.UsersBusiness.GetUserById(userId);
                    string queryText = string.Empty;

                    //Αν ο χρήστης έχει ρόλο Guest
                    if (userDS.Users[0].RoleName == "Guest")
                    {
                        //Contacts count query
                        queryText = @"SELECT ISNULL(COUNT(*), 0) AS ContactsCount FROM Contacts WHERE [Contacts].TenantId =" + tenantId + " AND [Contacts].GuestId=" + userId.ToString() + " ";
                        //Future appointments count query
                        queryText += @"SELECT 0 AS FutureAppointmentsCount FROM Appointments ";
                        //Waiting contacts count query
                        queryText += @"SELECT COUNT(*) FROM Contacts WHERE TenantId = " + tenantId + " AND Waiting = 1";
                    }
                    else
                    {
                        //Contacts count query
                        queryText = @"SELECT ISNULL(COUNT(*), 0) AS ContactsCount FROM Contacts WHERE [Contacts].TenantId =" + tenantId + " ";
                        //Future appointments count query
                        queryText += @"SELECT ISNULL(COUNT(*), 0) AS FutureAppointmentsCount FROM Appointments WHERE [StartTime] >= '@startDate' AND [Appointments].UserId =" + userId + " ";
                        //Waiting contacts count query
                        queryText += @"SELECT COUNT(*) FROM Contacts WHERE TenantId = " + tenantId + " AND Waiting = 1";
                    }

                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        //string queryText = @"SELECT ISNULL(COUNT(*), 0) AS ContactsCount FROM Contacts WHERE [Contacts].TenantId =" + tenantId + " ";
                        //queryText += @"SELECT ISNULL(COUNT(*), 0) AS FutureAppointmentsCount FROM Appointments WHERE [StartTime] >= '@startDate' AND [Appointments].UserId =" + userId + " ";

                        using (SqlDataAdapter da = new SqlDataAdapter(queryText, connection))
                        {
                            da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", DateTime.UtcNow.ToString("yyyy-MM-dd"));
                            da.Fill(ds);
                        }

                        info.ContactsCount = int.Parse(ds.Tables[0].Rows[0][0].ToString());
                        info.FutureUserAppointmentsCount = int.Parse(ds.Tables[1].Rows[0][0].ToString());
                        info.WaitingContactsCount = int.Parse(ds.Tables[2].Rows[0][0].ToString());

                        connection.Close();
                    }
                }
                catch (Exception exp)
                {
                    Data.ExceptionLogger.LogException(exp);
                }
                finally
                {

                }

                return info;
            }
        }

        public class AdministrationBusiness
        {
            public static MentalViewDataSet GetAllRoles(Int64 tenantId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
                    rolesTA.Connection = connection;
                    rolesTA.FillByTenantId(ds.Roles, tenantId);

                    return ds;
                }
            }

            public static MentalViewDataSet GetAllUsers()
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
                    rolesTA.Connection = connection;
                    rolesTA.Fill(ds.Roles);

                    MentalViewDataSetTableAdapters.UsersTableAdapter usersTA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
                    usersTA.Connection = connection;
                    usersTA.Fill(ds.Users);

                    connection.Close();
                    return ds;
                }
            }


            public static async Task<AuthenticationResult> AuthenticateUser(string joomlaUrl, string username, string password)
            {
                try
                {
                    //Αρχικά βρίσκει το User από το username
                    MentalViewDataSet userDS = Business.UsersBusiness.GetUserByUsername(username);

                    #region  Αρχικά ελέγχει αν υπάρχει ο User στη βάση δεδομένων.
                    bool isValidUser = false;
                    if (userDS.Users.Count > 0 && userDS.Users[0].Username == username)
                    {
                        //Αν ο User είναι SuperAdmin
                        if (userDS.Users[0].RolesRow.Name == "SuperAdmin")
                        {
                            //Αν ο χρήστης υπαρχει στη Joomla βάση δεδομ.
                            if (await TenantsBusiness.AuthenticateUserInJoomla(joomlaUrl, username, password) == true)
                            {
                                Console.WriteLine("SuperUser found in Joomla.");
                                isValidUser = true;
                            }
                        }
                        else  //Αν ο User είναι Admin ή User.
                        {
                            //Ελέγχει αν υπάρχει στη βάση δεδομένων SqlServer.
                            //MentalViewDataSet userDataSet = UsersBusiness.GetUserByUsername(username);
                            if (userDS.Users.Count > 0)
                            {
                                if (userDS.Users[0].Password == password)
                                {
                                    Console.WriteLine("User is Admin or User");
                                    isValidUser = true;
                                }
                            }
                        }
                    }

                    if (isValidUser == false)
                    {
                        Console.WriteLine("User not found.");
                        return new AuthenticationResult() { Result = "InvalidUsernameOrPassword", UserDS = null };
                    }
                    #endregion

                    #region Αφού ο χρήστης ειναι valid ελέγχει αν έχει valid Subscription.
                    MentalViewDataSet tenantDS = await TenantsBusiness.GetTenant(username);


                    if (isValidUser == true)
                    {
                        //Βρίσκει τα active subscriptions του user στο MembershipPro
                        MembershipSubscription membership = await TenantsBusiness.GetActiveSubscriptionDetails(joomlaUrl, tenantDS.Tenants[0].JoomlaUserId);

                        //Αν ο χρήστης έχει active subscription.
                        if (membership != null)
                        {
                            Console.WriteLine("User has active subscription");
                            //UserToken userToken = BuildToken(tenant.Username, tenant.FullName, "", "Tenant", "", tenant.TenantId.ToString(), membership.PlanId.ToString(), membership.FromDate);
                            //return new ApiResponse<UserToken>() { ResultCode = ApiResponseResultCode.Ok, Data = userToken };
                            return new AuthenticationResult() { Result = "Ok", UserDS = userDS };
                        }
                        else
                        {
                            Console.WriteLine("User does not have active subscription");
                            //return new ApiResponse<UserToken>() { ResultCode = ApiResponseResultCode.LoginFailedSubscriptionExpired, Data = null };
                            return new AuthenticationResult() { Result = "LoginFailedSubscriptionExpired", UserDS = null };
                        }
                    }
                    #endregion

                    return new AuthenticationResult() { Result = "InvalidUsernameOrPassword", UserDS = null };
                }
                finally
                {

                }
            }
            //public static bool AuthenticateUser(string username, string password, out string fullname)
            //{
            //    try
            //    {
            //        using (SqlConnection connection = new SqlConnection(connectionString))
            //        {
            //            MentalViewDataSet ds = new Data.MentalViewDataSet();
            //            ds.EnforceConstraints = false;

            //            MentalViewDataSetTableAdapters.UsersTableAdapter usersTA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
            //            usersTA.Connection = connection;
            //            usersTA.FillByUsernameAndPassword(ds.Users, username, password);

            //            if (ds.Users.Count == 1)
            //            {
            //                fullname = ds.Users[0].FullName;
            //                return true;
            //            }
            //            else
            //            {
            //                fullname = "";
            //                return false;
            //            }
            //        }
            //    }
            //    finally
            //    {

            //    }
            //}

            public static bool CheckUsernameExistence(string username, int userId)
            {
                SqlDataReader reader;
                SqlCommand command;

                int result = 0;
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        command = new SqlCommand();
                        command.CommandText = @"SELECT Count(*) AS Result FROM Users WHERE [Users].[Username]='" + username + "' AND NOT [Users].[UserId]=" + userId.ToString() + ";";
                        command.Connection = connection;
                        connection.Open();

                        reader = command.ExecuteReader();
                        if (reader == null)
                        {
                            throw new Exception("variable reader=null");
                        }
                        reader.Read();
                        result = reader.GetInt32(0);
                        reader.Close();
                        connection.Close();
                    }
                }
                finally
                {

                }

                if (result >= 1) //Αν βρέθηκε τουλάχιστον ένα πρόσωπο
                {
                    return true;
                }
                else  //Αν δεν βρεθηκε πρόσωπο 
                {
                    return false;
                }
            }

            public static string GetRoleOfUser(string username)
            {
                SqlDataReader reader;
                SqlCommand command;

                string result;
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        command = new SqlCommand();
                        command.CommandText = @"SELECT Roles.Name FROM Users INNER JOIN Roles ON Roles.RoleId = Users.RoleId WHERE [Users].[Username] ='" + username + "'";
                        command.Connection = connection;
                        connection.Open();

                        reader = command.ExecuteReader();
                        if (reader == null)
                        {
                            throw new Exception("variable reader=null");
                        }
                        reader.Read();
                        result = reader.GetString(0);
                        reader.Close();
                        connection.Close();
                    }
                }
                finally
                {

                }

                return result;
            }

            public static MentalViewDataSet GetUserByUsername(string username)
            {
                bool connectionMustClose = true;  //Indicates if we must close the connection after the execution of query.
                SqlConnection connection = null;

                try
                {
                    connection = new SqlConnection(connectionString);
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    Business.ConfigureDataSet(ref ds);
                    ds.EnforceConstraints = false;

                    if (connection.State == ConnectionState.Closed)
                    {
                        connection.Open();
                        connectionMustClose = true;  //We must close the connection because we found it closed.
                    }
                    else
                    {
                        connectionMustClose = false;
                    }

                    //Διαβάζουμε πρώτα το User για να πάρουμε το TenantId
                    MentalViewDataSetTableAdapters.TenantsTableAdapter tenantsTA = new MentalViewDataSetTableAdapters.TenantsTableAdapter();
                    tenantsTA.Connection = connection;
                    tenantsTA.FillByUser(ds.Tenants, username);

                    MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
                    rolesTA.Connection = connection;
                    rolesTA.FillByTenantId(ds.Roles, ds.Tenants[0].TenantId);

                    MentalViewDataSetTableAdapters.UsersTableAdapter usersTA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
                    usersTA.Connection = connection;
                    usersTA.FillByUsername(ds.Users, username);



                    ds.EnforceConstraints = true;

                    if (ds.Users.Count > 0)
                    {
                        return ds;
                    }
                    else
                    {
                        return null;
                    }
                }
                finally
                {
                    if (connectionMustClose)  //If we must close the connection
                    {
                        if (connection != null)
                        {
                            connection.Close();
                        }
                    }
                }
            }

            public static MentalViewDataSet GetUserByUserId(Int64 userId)
            {
                bool connectionMustClose = true;  //Indicates if we must close the connection after the execution of query.
                SqlConnection connection = null;

                try
                {
                    connection = new SqlConnection(connectionString);
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    Business.ConfigureDataSet(ref ds);
                    ds.EnforceConstraints = false;

                    if (connection.State == ConnectionState.Closed)
                    {
                        connection.Open();
                        connectionMustClose = true;  //We must close the connection because we found it closed.
                    }
                    else
                    {
                        connectionMustClose = false;
                    }

                    //Αυτή είναι η σωστή λύση γιατί να μην χτυπάει exception στο EnforcConstraints.
                    MentalViewDataSetTableAdapters.UsersTableAdapter usersTA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
                    usersTA.Connection = connection;
                    usersTA.FillByUserId(ds.Users, userId);

                    MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
                    rolesTA.Connection = connection;
                    rolesTA.FillByTenantId(ds.Roles, ds.Users[0].TenantId);

                    MentalViewDataSetTableAdapters.TenantsTableAdapter tenantsTA = new MentalViewDataSetTableAdapters.TenantsTableAdapter();
                    tenantsTA.Connection = connection;
                    tenantsTA.FillByUser(ds.Tenants, ds.Users[0].Username);






                    ds.EnforceConstraints = true;

                    if (ds.Users.Count > 0)
                    {
                        return ds;
                    }
                    else
                    {
                        return null;
                    }
                }
                finally
                {
                    if (connectionMustClose)  //If we must close the connection
                    {
                        if (connection != null)
                        {
                            connection.Close();
                        }
                    }
                }
            }

            public static void SetUserAppointmentsSort(string username, string sortValue)
            {
                int affectedRows;
                SqlCommand command;

                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        command = new SqlCommand();
                        command.CommandText = @"Update Users SET AppointmentsListSort='" + sortValue + @"' WHERE [Users].[Username] ='" + username + "'";
                        command.Connection = connection;
                        connection.Open();

                        affectedRows = command.ExecuteNonQuery();
                        if (affectedRows == 0)
                        {
                            throw new Exception("User setting 'AppointmentsListSort' not updated in database.");
                        }
                        connection.Close();
                    }
                }
                finally
                {

                }
            }

            public static void SetUserAppointmentsScheduleView(string username, string view)
            {
                int affectedRows;
                SqlCommand command;

                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        command = new SqlCommand();
                        command.CommandText = @"Update Users SET AppointmentsScheduleView='" + view + @"' WHERE [Users].[Username] ='" + username + "'";
                        command.Connection = connection;
                        connection.Open();

                        affectedRows = command.ExecuteNonQuery();
                        if (affectedRows == 0)
                        {
                            throw new Exception("User setting 'AppointmentsScheduleView' not updated in database.");
                        }
                        connection.Close();
                    }
                }
                finally
                {

                }
            }
        }

        public class ContactsBusiness
        {
            /// <summary>
            /// Saves only data of Contacts.
            /// </summary>
            public static void SaveContacts(ref MentalViewDataSet ds)
            {
                SqlConnection connection = null;

                try
                {
                    using (connection = new SqlConnection(connectionString))
                    {
                        using (MentalViewDataSetTableAdapters.ContactsTableAdapter contactsTA = new MentalViewDataSetTableAdapters.ContactsTableAdapter())
                        {
                            contactsTA.Connection = connection;
                            contactsTA.Update(ds.Contacts);

                            ds.Contacts.AcceptChanges();
                        }
                    }
                }
                catch (Exception exp)
                {
                    ds.Contacts.RejectChanges();
                    throw exp;
                }
                finally
                {
                    if (connection != null)
                    {
                        connection.Close();
                    }
                }
            }

            public static MentalViewDataSet GetContactById(Int64 contactId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    Data.Business.ConfigureDataSet(ref ds);
                    MentalViewDataSetTableAdapters.ContactsTableAdapter customersTA = new MentalViewDataSetTableAdapters.ContactsTableAdapter();
                    customersTA.Connection = connection;
                    customersTA.FillByContactId(ds.Contacts, contactId);

                    MentalViewDataSetTableAdapters.AppointmentCategoriesTableAdapter appointmentCategoriesDT = new MentalViewDataSetTableAdapters.AppointmentCategoriesTableAdapter();
                    appointmentCategoriesDT.Connection = connection;
                    appointmentCategoriesDT.FillByTenantId(ds.AppointmentCategories, ds.Contacts[0].TenantId);

                    MentalViewDataSetTableAdapters.QuestionnairesTableAdapter questionnairesDT = new MentalViewDataSetTableAdapters.QuestionnairesTableAdapter();
                    questionnairesDT.Connection = connection;
                    questionnairesDT.FillByContactId(ds.Questionnaires, contactId);

                    MentalViewDataSetTableAdapters.QuestionnaireQuestionsTableAdapter questionnaireQuestionsTA = new MentalViewDataSetTableAdapters.QuestionnaireQuestionsTableAdapter();
                    questionnaireQuestionsTA.Connection = connection;
                    questionnaireQuestionsTA.FillByContactId(ds.QuestionnaireQuestions, contactId);

                    MentalViewDataSetTableAdapters.AppointmentsTableAdapter appointmensTA = new MentalViewDataSetTableAdapters.AppointmentsTableAdapter();
                    appointmensTA.Connection = connection;
                    appointmensTA.FillByContactId(ds.Appointments, contactId);

                    MentalViewDataSetTableAdapters.ContactEmailTemplatesTableAdapter contactEmailTemplatesTA = new MentalViewDataSetTableAdapters.ContactEmailTemplatesTableAdapter();
                    contactEmailTemplatesTA.Connection = connection;
                    contactEmailTemplatesTA.FillByContactId(ds.ContactEmailTemplates, contactId);

                    return ds;
                }
            }

            public static MentalViewDataSet GetContacts(Int64 tenantId, Int64? userId, Int64? guestId, string filter, string sortExpression, Data.SortDirection? sortDirection, int pageIndex, int pageSize, out int totalCount)
            {
                //Οι παράμετροι userId και guestId, θα πρέπει να συμπληρώνονται η μία από τις δυο (ή άλλή θα πρέπει να είναι null), αλλιώς δεν θα φέρνει αποτελέσματα.

                MentalViewDataSet ds = new MentalViewDataSet();
                ds.EnforceConstraints = false;
                totalCount = 0;

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("GetContacts", connection))
                    {
                        DataSet genericDS = new DataSet();

                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add("@TenantId", SqlDbType.BigInt).Value = tenantId;
                        cmd.Parameters.Add("@Filter", SqlDbType.NVarChar).Value = filter;
                        cmd.Parameters.Add("@OrderBy", SqlDbType.NVarChar).Value = sortExpression;
                        if (sortDirection.HasValue)
                        {
                            cmd.Parameters.Add("@SortType", SqlDbType.NVarChar).Value = sortDirection == SortDirection.Ascending ? "ASC" : "DESC";
                        }
                        cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                        cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                        if (userId.HasValue)  //Δεν πρέπει να συμπληρώνεται και το userId και το guestId
                        {
                            cmd.Parameters.Add("@UserId", SqlDbType.BigInt).Value = userId;
                        }
                        if (guestId.HasValue)  //Δεν πρέπει να συμπληρώνεται και το userId και το guestId
                        {
                            cmd.Parameters.Add("@GuestId", SqlDbType.BigInt).Value = guestId;
                        }

                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(genericDS);
                        }

                        ds.Contacts.Merge(genericDS.Tables[0]);
                        ds.AcceptChanges();
                        totalCount = int.Parse(genericDS.Tables[1].Rows[0][0].ToString());
                    }
                }

                return ds;
            }

            public static DataTable GetContactsForExport(Int64 tenantId, string orderBy)
            {
                DataTable dt = new DataTable();
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    string sqlText = "select ContactCode As 'Κωδικός Πελάτη', LastName AS N'Επώνυμο', FirstName AS N'Όνομα', Case When (SELECT Active)=1 Then N'Ναι' ELse N'Όχι' END AS Ενεργός, Mobile1 AS N'Κινητό', Email, ISNULL((SELECT FullName From Users Where UserId=TherapistId), '') As Θεραπευτής, (SELECT COUNT(*) FROM Appointments WHERE Appointments.ContactId = Contacts.ContactId) AS Συνεδρείες, " +
                        //"--(SELECT MIN(StartTime) FROM Appointments WHERE Appointments.ContactId = Contacts.ContactId) AS FirstAppointmentDate, " +
                        //"--(SELECT MAX(StartTime) FROM Appointments WHERE Appointments.ContactId = Contacts.ContactId) AS LastAppointmentDate, " +
                        "ISNULL(DATEDIFF(month, (SELECT MIN(StartTime) FROM Appointments WHERE Appointments.ContactId = Contacts.ContactId), (SELECT MAX(StartTime) FROM Appointments WHERE Appointments.ContactId = Contacts.ContactId)), 0) AS 'Διάρκεια (μήνες)' " +
                        "From Contacts ";
                    if (orderBy == "FirstLastName")
                    {
                        sqlText += " ORDER BY LastName, FirstName";
                    }
                    else if (orderBy == "MostAppointments")
                    {
                        sqlText += " ORDER BY (SELECT COUNT(*) FROM Appointments WHERE Appointments.ContactId = Contacts.ContactId) DESC";
                    }

                    using (SqlCommand cmd = new SqlCommand(sqlText, connection))
                    {
                        cmd.CommandType = CommandType.Text;

                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(dt);
                        }
                    }
                }

                return dt;
            }

            public Int64? GetTherapistIdOfContact(Int64 tenantId, Int64 contactId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    string sqlText = "select TherapistId From Contacts WHERE TenantId =" + tenantId.ToString() + " AND ContactId=" + contactId.ToString();

                    using (SqlCommand cmd = new SqlCommand(sqlText, connection))
                    {
                        cmd.CommandType = CommandType.Text;
                        connection.Open();
                        object result = cmd.ExecuteScalar();

                        if (result != null && result.ToString() != "")
                        {
                            return Convert.ToInt64(result);
                        }
                        else
                        {
                            return null;
                        }

                        //using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        //{
                        //    da.Fill(dt);

                        //    if()
                        //}
                    }
                }
            }

            public static DataTable GetAllContactsList(Int64 tenantId)
            {
                DataTable dt = new DataTable();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("SELECT ContactId, FirstName, LastName, ContactCode FROM Contacts WHERE TenantId=" + tenantId + " ORDER BY LastName, FirstName", connection))
                    {
                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(dt);
                            dt.Columns.Add("FullName", typeof(string), "LastName+ISNULL(' '+FirstName,'')");
                        }
                    }
                }

                return dt;
            }

            public static DataTable GetContactsForContactsReport(Int64 tenantId, string contactType)
            {
                DataTable dt = new DataTable();

                string whereClause = "";
                if (contactType == "Active")
                {
                    whereClause = " AND Active=1 ";
                }
                else if (contactType == "Deactive")
                {
                    whereClause = " AND Active = 0 ";
                }
                else if (contactType == "Waiting")
                {
                    whereClause = " AND Waiting = 1 ";
                }

                string query = @"SELECT ContactId, Active, Waiting, FirstName, LastName, FatherName, Email, Phone1, Mobile1, 
                            ISNULL((SELECT FullName From Users WHERE UserId = TherapistId), '') AS TherapistFullName,
                            ISNULL((SELECT FullName From Users WHERE UserId = DiagnosticianId), '') AS DiagnosticianFullName,
                            ISNULL((SELECT FullName From Users WHERE UserId = ClinicSupervisorId), '') AS ClinicSupervisorFullName
                        FROM Contacts
                        WHERE TenantId = " + tenantId.ToString() + whereClause +
                        @" ORDER BY LastName, FirstName";

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(query, connection))
                    {
                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(dt);
                            dt.Columns.Add("FullName", typeof(string), "LastName+ISNULL(' '+FirstName,'')");
                        }
                    }
                }

                return dt;
            }

            public static DataTable GetContactsForTherapistContactsReport(Int64 tenantId, Int64 therapistId)
            {
                DataTable dt = new DataTable();

                string query = @"SELECT ContactId, Active, Waiting, FirstName, LastName, FatherName, Email, Phone1, Mobile1, 
                            ISNULL((SELECT FullName From Users WHERE UserId = TherapistId), '') AS TherapistFullName,
                            ISNULL((SELECT FullName From Users WHERE UserId = DiagnosticianId), '') AS DiagnosticianFullName,
                            ISNULL((SELECT FullName From Users WHERE UserId = ClinicSupervisorId), '') AS ClinicSupervisorFullName
                        FROM Contacts
                        WHERE TenantId = " + tenantId.ToString() + " AND TherapistId=" + therapistId.ToString() +
                        @" ORDER BY LastName, FirstName";

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(query, connection))
                    {
                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(dt);
                            dt.Columns.Add("FullName", typeof(string), "LastName+ISNULL(' '+FirstName,'')");
                        }
                    }
                }

                return dt;
            }

            public static void DeleteContact(Int64 contactId)
            {
                string sqlQuery = "DELETE FROM Contacts WHERE ContactId=" + contactId.ToString();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(sqlQuery, connection))
                    {
                        connection.Open();
                        cmd.CommandType = CommandType.Text;
                        cmd.ExecuteNonQuery();
                    }
                }
            }
        }

        public class AppointmentsBusiness
        {

            public static MentalViewDataSet GetAppointmentById(Int64 appointmentId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    Data.Business.ConfigureDataSet(ref ds);
                    ds.EnforceConstraints = false;

                    connection.Open();

                    MentalViewDataSetTableAdapters.AppointmentsTableAdapter AppointmentsTA = new MentalViewDataSetTableAdapters.AppointmentsTableAdapter();
                    AppointmentsTA.Connection = connection;
                    AppointmentsTA.FillByAppointmentId(ds.Appointments, appointmentId);

                    if (ds.Appointments[0].IsContactIdNull() == false)
                    {
                        MentalViewDataSetTableAdapters.ContactsTableAdapter contactsTA = new MentalViewDataSetTableAdapters.ContactsTableAdapter();
                        contactsTA.Connection = connection;
                        contactsTA.FillByContactId(ds.Contacts, ds.Appointments[0].ContactId);
                    }


                    connection.Close();

                    return ds;
                }
            }

            public static int GetTotalAppointmentByUserId(Int64 userId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    Data.Business.ConfigureDataSet(ref ds);
                    ds.EnforceConstraints = false;

                    connection.Open();

                    MentalViewDataSetTableAdapters.AppointmentsTableAdapter AppointmentsTA = new MentalViewDataSetTableAdapters.AppointmentsTableAdapter();
                    AppointmentsTA.Connection = connection;
                    int totalAppointments = (int)AppointmentsTA.GetTotalAppointmentsOfUser(userId);

                    connection.Close();

                    return totalAppointments;
                }
            }

            public static DataTable GetAppointments(Int64 tenantId, string filter, string sortExpression, Data.SortDirection? sortDirection, int pageIndex, int pageSize, DateTime? startTimeFilter, DateTime? endTimeFilter, out int totalCount)
            {
                DataSet genericDS = new DataSet();
                totalCount = 0;

                using (SqlConnection connection = new SqlConnection(connectionString))
                {

                    using (SqlCommand cmd = new SqlCommand("GetAppointments", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add("@TenantId", SqlDbType.BigInt).Value = tenantId;
                        cmd.Parameters.Add("@Filter", SqlDbType.NVarChar).Value = filter;
                        cmd.Parameters.Add("@OrderBy", SqlDbType.NVarChar).Value = sortExpression;
                        if (sortDirection.HasValue)
                        {
                            cmd.Parameters.Add("@SortType", SqlDbType.NVarChar).Value = sortDirection == SortDirection.Ascending ? "ASC" : "DESC";
                        }
                        cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                        cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                        cmd.Parameters.Add("@StartTimeFilter", SqlDbType.DateTime).Value = startTimeFilter;
                        cmd.Parameters.Add("@EndTimeFilter", SqlDbType.DateTime).Value = endTimeFilter;
                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(genericDS);
                        }


                        totalCount = int.Parse(genericDS.Tables[1].Rows[0][0].ToString());
                    }
                }

                return genericDS.Tables[0];
            }

            public static DataTable GetAppointmentsByDates(Int64 tenantId, DateTime startDate, DateTime endDate, AppointmentsSearchByDateType appointmentsSearchByDateType, Int64? contactId, Int64[] usersIds = null)
            {
                DataTable appointmentsDT = new DataTable();
                DataTable reccurentAppointmentsTable = new DataTable();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    #region  Βρίσκει όλα τα appointments (κανονικά και με recurrence) μεταξύ των ημερομηνιών startDate και endDate, και τα Tasks που είναι visible σε όλους τους θεραπευτές
                    string queryText = @"SELECT *, 
	                    (SELECT Contacts.FirstName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactFirstName,
                        (SELECT Contacts.LastName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactLastName,
                        ISNULL((SELECT Users.FullName FROM Users WHERE Users.UserId = Appointments.UserId), '') AS UserFullName
                        -- ISNULL((SELECT AppointmentCategories.CategoryName FROM AppointmentCategories WHERE AppointmentCategories.AppointmentCategoryId = Appointments.AppointmentCategoryId), '') AS AppointmentCategoryName
                        FROM Appointments
                        WHERE TenantId=" + tenantId.ToString() + " AND (" +
                            "(" + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " BETWEEN '@startDate' AND '@endDate') " +
                            " OR VisibleToAll=1";

                    //Προσθέτει το φίλτρο για όσους θεραπευτές έχουν το ID τους μέσα στη στήλη TaskSupervisionTherapistIDs
                    if (usersIds != null && usersIds.Length > 0)
                    {
                        queryText += @" OR  (TaskSupervision=1 AND ( ";
                        foreach (Int64 userId in usersIds)
                        {
                            queryText += userId.ToString() + " in (SELECT * FROM STRING_SPLIT([TaskSupervisionTherapistIDs], N'|')) OR ";
                        }
                        queryText = queryText.Substring(0, queryText.Length - 3);
                        queryText += @" )) ";
                    }

                    queryText += ")";


                    if (contactId.HasValue)
                    {
                        queryText += @" AND ContactId = " + contactId.ToString() + " ";
                    }
                    if (usersIds != null && usersIds.Length > 0)
                    {
                        queryText += @" AND (";
                        foreach (Int64 userId in usersIds)
                        {
                            queryText += @" UserId=" + userId.ToString() + " OR ";
                        }
                        queryText = queryText.Substring(0, queryText.Length - 3);
                        queryText += @" ) ";
                    }
                    queryText += " ORDER BY " + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " ASC";
                    // Για ημερομηνίες σε sql CONVERT(VARCHAR(5),StartDate,108)


                    using (SqlDataAdapter da = new SqlDataAdapter(queryText, connection))
                    {
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.ToString("yyyy/MM/dd 00:00:00"));
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", endDate.ToString("yyyy/MM/dd 23:59:59"));

                        da.Fill(appointmentsDT);
                    }

                    //Βρίσκει τα appointments που είναι visisble σε όλους, δημιουργεί αντίγραφα με το UserId των άλλων θεραπευτών ώστε να εμφανιστεί στο ημερολόγιο όλων.
                    DataRow[] visibleToAllAppointmentRows = appointmentsDT.Select("VisibleToAll=1");
                    foreach (DataRow visibleToAllAppointmentsRow in visibleToAllAppointmentRows)
                    {
                        foreach (int userId in usersIds)
                        {
                            if (userId != Convert.ToInt32(visibleToAllAppointmentsRow["UserId"]))  //Παρακάτω δημιουργούμε το νέο fake appointment μόνο για τους υπόλοιπους θεραπευτές (εκτός από αυτόν που του ανήκει το appointment)
                            {
                                DataRow fakeAppointmentsRow = appointmentsDT.LoadDataRow(visibleToAllAppointmentsRow.ItemArray, true);
                                fakeAppointmentsRow["UserId"] = userId;  //Αλλάζει το UserId για να εμφανιστεί και στον άλλο θεραπευτή.
                            }
                        }
                    }

                    //Βρίσκει τα appointments που είναι TaskSupervision=1 και στη στήλη TaskSupervisionTherapistIDs υπάρχει το ID του θεραπευτή, δημιουργεί αντίγραφα με το UserId των άλλων θεραπευτών ώστε να εμφανιστεί στο ημερολόγιο όλων.
                    DataRow[] taskSupervisionAppointmentRows = appointmentsDT.Select("TaskSupervision=1");
                    foreach (DataRow taskSupervisionAppointmentRow in taskSupervisionAppointmentRows)
                    {
                        foreach (int userId in usersIds)
                        {
                            //Αν το UserId του User που θέλουμε να εμφανίστεί στο ημερολόγιο υπάρχει στη στήλη TaskSupervisionTherapistIDs
                            if (taskSupervisionAppointmentRow["TaskSupervisionTherapistIDs"].ToString().Split('|').Any(x => x == userId.ToString()))
                            {
                                if (userId != Convert.ToInt32(taskSupervisionAppointmentRow["UserId"]))  //Παρακάτω δημιουργούμε το νέο fake appointment μόνο για τους υπόλοιπους θεραπευτές (εκτός από αυτόν που του ανήκει το appointment)
                                {
                                    DataRow fakeAppointmentsRow = appointmentsDT.LoadDataRow(taskSupervisionAppointmentRow.ItemArray, true);
                                    fakeAppointmentsRow["UserId"] = userId;  //Αλλάζει το UserId για να εμφανιστεί και στον άλλο θεραπευτή.
                                }
                            }
                        }
                    }
                    #endregion

                    #region  //Βρίσκει μόνο τα reccurent appointments που έχουν δημιουργηθεί πριν από το startDate και ένα έτος πίσω.
                    string query2Text = @"SELECT *, 
	                    (SELECT Contacts.FirstName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactFirstName,
                        (SELECT Contacts.LastName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactLastName,
                        ISNULL((SELECT Users.FullName FROM Users WHERE Users.UserId = Appointments.UserId), '') AS UserFullName
                        -- ISNULL((SELECT AppointmentCategories.CategoryName FROM AppointmentCategories WHERE AppointmentCategories.AppointmentCategoryId = Appointments.AppointmentCategoryId), '') AS AppointmentCategoryName
                        FROM Appointments
                        WHERE TenantId=" + tenantId.ToString() + " AND Recurrence=1 AND (StartTime BETWEEN '@startDate' AND '@endDate') ";
                    if (contactId.HasValue)
                    {
                        query2Text += @" AND ContactId = " + contactId.ToString() + " ";
                    }
                    if (usersIds != null && usersIds.Length > 0)
                    {
                        query2Text += @" AND (";
                        foreach (Int64 userId in usersIds)
                        {
                            query2Text += @" UserId=" + userId.ToString() + " OR ";
                        }
                        query2Text = query2Text.Substring(0, query2Text.Length - 3);
                        query2Text += @" ) ";
                    }
                    query2Text += " ORDER BY " + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " ASC";
                    // Για ημερομηνίες σε sql CONVERT(VARCHAR(5),StartDate,108)

                    using (SqlDataAdapter da = new SqlDataAdapter(query2Text, connection))
                    {
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.AddYears(-2).ToString("yyyy/MM/dd 00:00:00"));  //Ημερομηνία έναρξης βάζουμε 2 χρόνια πριν το startDate.
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", startDate.AddDays(-1).ToString("yyyy/MM/dd 23:59:59"));  //Ημερομηνία λήξης βάζουμε τη προηγούμενη ημέρα από το startDate.

                        da.Fill(reccurentAppointmentsTable);
                    }
                    #endregion
                }

                appointmentsDT.Merge(reccurentAppointmentsTable);
                appointmentsDT.Columns.Add("ContactFullName", typeof(string), @"ContactLastName+ISNULL(' '+ContactFirstName,'')");
                appointmentsDT.Columns.Add("Summary", typeof(string), @"IIF(0=1, '<i style=""margin-right:5px;"" class=""fa fa-asterisk""></i>', '') + IIF(SupervisionRequest=1, '<i style=""margin-right:5px; color:orange;"" class=""fa fa-star fa-solid""></i>', '') + ISNULL(TRIM(ContactFullName), '')");

                return appointmentsDT;
            }

            public static DataTable GetAppointmentsInDebtByDates(Int64 tenantId, DateTime startDate, DateTime endDate, AppointmentsSearchByDateType appointmentsSearchByDateType, Int64? contactId, Int64[] usersIds = null)
            {
                DataTable appointmentsDT = new DataTable();
                DataTable reccurentAppointmentsTable = new DataTable();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    #region  Βρίσκει όλα τα appointments (κανονικά και με recurrence) μεταξύ των ημερομηνιών startDate και endDate, και τα Tasks που είναι visible σε όλους τους θεραπευτές
                    string queryText = @"SELECT *, 
	                    (SELECT Contacts.FirstName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactFirstName,
                        (SELECT Contacts.LastName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactLastName,
                        ISNULL((SELECT Users.FullName FROM Users WHERE Users.UserId = Appointments.UserId), '') AS UserFullName
                        --ISNULL((SELECT AppointmentCategories.CategoryName FROM AppointmentCategories WHERE AppointmentCategories.AppointmentCategoryId = Appointments.AppointmentCategoryId), '') AS AppointmentCategoryName
                        FROM Appointments
                        WHERE TenantId=" + tenantId.ToString() + " AND PaymentType='Pending' AND ((" + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " BETWEEN '@startDate' AND '@endDate') " +
                        " OR VisibleToAll=1)";
                    if (contactId.HasValue)
                    {
                        queryText += @" AND ContactId = " + contactId.ToString() + " ";
                    }
                    if (usersIds != null && usersIds.Length > 0)
                    {
                        queryText += @" AND (";
                        foreach (Int64 userId in usersIds)
                        {
                            queryText += @" UserId=" + userId.ToString() + " OR ";
                        }
                        queryText = queryText.Substring(0, queryText.Length - 3);
                        queryText += @" ) ";
                    }
                    queryText += " ORDER BY " + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " ASC";
                    // Για ημερομηνίες σε sql CONVERT(VARCHAR(5),StartDate,108)


                    using (SqlDataAdapter da = new SqlDataAdapter(queryText, connection))
                    {
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.ToString("yyyy/MM/dd 00:00:00"));
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", endDate.ToString("yyyy/MM/dd 23:59:59"));

                        da.Fill(appointmentsDT);
                    }

                    ////Βρίσκει τα appointments που είναι visisble σε όλους, δημιουργεί αντίγραφα με το UserId των άλλων θεραπευτών ώστε να εμφανιστεί στο ημερολόγιο όλων.
                    //DataRow[] visibleToAllAppointmentRows = appointmentsDT.Select("VisibleToAll=1");
                    //foreach (DataRow visibleToAllAppointmentsRow in visibleToAllAppointmentRows)
                    //{
                    //    foreach (int userId in usersIds)
                    //    {
                    //        if (userId != Convert.ToInt32(visibleToAllAppointmentsRow["UserId"]))  //Παρακάτω δημιουργούμε το νέο fake appointment μόνο για τους υπόλοιπους θεραπευτές (εκτός από αυτόν που του ανήκει το appointment)
                    //        {
                    //            DataRow fakeAppointmentsRow = appointmentsDT.LoadDataRow(visibleToAllAppointmentsRow.ItemArray, true);
                    //            fakeAppointmentsRow["UserId"] = userId;  //Αλλάζει το UserId για να εμφανιστεί και στον άλλο θεραπευτή.
                    //        }
                    //    }
                    //}
                    #endregion

                    #region  //Βρίσκει μόνο τα reccurent appointments που έχουν δημιουργηθεί πριν από το startDate και ένα έτος πίσω.
                    string query2Text = @"SELECT *, 
	                    (SELECT Contacts.FirstName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactFirstName,
                        (SELECT Contacts.LastName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactLastName,
                        ISNULL((SELECT Users.FullName FROM Users WHERE Users.UserId = Appointments.UserId), '') AS UserFullName
                        --ISNULL((SELECT AppointmentCategories.CategoryName FROM AppointmentCategories WHERE AppointmentCategories.AppointmentCategoryId = Appointments.AppointmentCategoryId), '') AS AppointmentCategoryName
                        FROM Appointments
                        WHERE TenantId=" + tenantId.ToString() + " AND PaymentType='Pending' AND Recurrence=1 AND (StartTime BETWEEN '@startDate' AND '@endDate') ";
                    if (contactId.HasValue)
                    {
                        query2Text += @" AND ContactId = " + contactId.ToString() + " ";
                    }
                    if (usersIds != null && usersIds.Length > 0)
                    {
                        query2Text += @" AND (";
                        foreach (Int64 userId in usersIds)
                        {
                            query2Text += @" UserId=" + userId.ToString() + " OR ";
                        }
                        query2Text = query2Text.Substring(0, query2Text.Length - 3);
                        query2Text += @" ) ";
                    }
                    query2Text += " ORDER BY " + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " ASC";
                    // Για ημερομηνίες σε sql CONVERT(VARCHAR(5),StartDate,108)

                    using (SqlDataAdapter da = new SqlDataAdapter(query2Text, connection))
                    {
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.AddYears(-2).ToString("yyyy/MM/dd 00:00:00"));  //Ημερομηνία έναρξης βάζουμε 2 χρόνια πριν το startDate.
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", startDate.AddDays(-1).ToString("yyyy/MM/dd 23:59:59"));  //Ημερομηνία λήξης βάζουμε τη προηγούμενη ημέρα από το startDate.

                        da.Fill(reccurentAppointmentsTable);
                    }
                    #endregion
                }

                appointmentsDT.Merge(reccurentAppointmentsTable);
                appointmentsDT.Columns.Add("ContactFullName", typeof(string), @"ContactLastName+ISNULL(' '+ContactFirstName,'')");
                appointmentsDT.Columns.Add("Summary", typeof(string), @"IIF(0=1, '<i style=""margin-right:5px;"" class=""fa fa-asterisk""></i>', '') + IIF(SupervisionRequest=1, '<i style=""margin-right:5px; color:orange;"" class=""fa fa-star fa-solid""></i>', '') + ISNULL(TRIM(ContactFullName), '')");

                return appointmentsDT;
            }

            public static DataTable GetCanceledAppointmentsByDates(Int64 tenantId, DateTime startDate, DateTime endDate, AppointmentsSearchByDateType appointmentsSearchByDateType, Int64? contactId, Int64[] usersIds = null)
            {
                DataTable appointmentsDT = new DataTable();
                DataTable reccurentAppointmentsTable = new DataTable();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    #region  Βρίσκει όλα τα appointments (κανονικά και με recurrence) μεταξύ των ημερομηνιών startDate και endDate, και τα Tasks που είναι visible σε όλους τους θεραπευτές
                    string queryText = @"SELECT *, 
	                    (SELECT Contacts.FirstName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactFirstName,
                        (SELECT Contacts.LastName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactLastName,
                        ISNULL((SELECT Users.FullName FROM Users WHERE Users.UserId = Appointments.UserId), '') AS UserFullName
                        --ISNULL((SELECT AppointmentCategories.CategoryName FROM AppointmentCategories WHERE AppointmentCategories.AppointmentCategoryId = Appointments.AppointmentCategoryId), '') AS AppointmentCategoryName
                        FROM Appointments
                        WHERE TenantId=" + tenantId.ToString() + " AND Canceled=1 AND ((" + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " BETWEEN '@startDate' AND '@endDate') " +
                        " OR VisibleToAll=1)";
                    if (contactId.HasValue)
                    {
                        queryText += @" AND ContactId = " + contactId.ToString() + " ";
                    }
                    if (usersIds != null && usersIds.Length > 0)
                    {
                        queryText += @" AND (";
                        foreach (Int64 userId in usersIds)
                        {
                            queryText += @" UserId=" + userId.ToString() + " OR ";
                        }
                        queryText = queryText.Substring(0, queryText.Length - 3);
                        queryText += @" ) ";
                    }
                    queryText += " ORDER BY " + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " ASC";
                    // Για ημερομηνίες σε sql CONVERT(VARCHAR(5),StartDate,108)


                    using (SqlDataAdapter da = new SqlDataAdapter(queryText, connection))
                    {
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.ToString("yyyy/MM/dd 00:00:00"));
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", endDate.ToString("yyyy/MM/dd 23:59:59"));

                        da.Fill(appointmentsDT);
                    }

                    #endregion

                    //#region  //Βρίσκει μόνο τα reccurent appointments που έχουν δημιουργηθεί πριν από το startDate και ένα έτος πίσω.
                    //string query2Text = @"SELECT *, 
                    // (SELECT Contacts.FirstName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactFirstName,
                    //    (SELECT Contacts.LastName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactLastName,
                    //    ISNULL((SELECT Users.FullName FROM Users WHERE Users.UserId = Appointments.UserId), '') AS UserFullName,
                    //    ISNULL((SELECT AppointmentCategories.CategoryName FROM AppointmentCategories WHERE AppointmentCategories.AppointmentCategoryId = Appointments.AppointmentCategoryId), '') AS AppointmentCategoryName
                    //    FROM Appointments
                    //    WHERE TenantId=" + tenantId.ToString() + " AND PaymentType='Pending' AND Recurrence=1 AND (StartTime BETWEEN '@startDate' AND '@endDate') ";
                    //if (contactId.HasValue)
                    //{
                    //    query2Text += @" AND ContactId = " + contactId.ToString() + " ";
                    //}
                    //if (usersIds != null && usersIds.Length > 0)
                    //{
                    //    query2Text += @" AND (";
                    //    foreach (Int64 userId in usersIds)
                    //    {
                    //        query2Text += @" UserId=" + userId.ToString() + " OR ";
                    //    }
                    //    query2Text = query2Text.Substring(0, query2Text.Length - 3);
                    //    query2Text += @" ) ";
                    //}
                    //query2Text += " ORDER BY " + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " ASC";
                    //// Για ημερομηνίες σε sql CONVERT(VARCHAR(5),StartDate,108)

                    //using (SqlDataAdapter da = new SqlDataAdapter(query2Text, connection))
                    //{
                    //    da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.AddYears(-2).ToString("yyyy/MM/dd 00:00:00"));  //Ημερομηνία έναρξης βάζουμε 2 χρόνια πριν το startDate.
                    //    da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", startDate.AddDays(-1).ToString("yyyy/MM/dd 23:59:59"));  //Ημερομηνία λήξης βάζουμε τη προηγούμενη ημέρα από το startDate.

                    //    da.Fill(reccurentAppointmentsTable);
                    //}
                    //#endregion
                }

                appointmentsDT.Merge(reccurentAppointmentsTable);
                appointmentsDT.Columns.Add("ContactFullName", typeof(string), @"ContactLastName+ISNULL(' '+ContactFirstName,'')");
                appointmentsDT.Columns.Add("Summary", typeof(string), @"IIF(0=1, '<i style=""margin-right:5px;"" class=""fa fa-asterisk""></i>', '') + IIF(SupervisionRequest=1, '<i style=""margin-right:5px; color:orange;"" class=""fa fa-star fa-solid""></i>', '') + ISNULL(TRIM(ContactFullName), '')");

                return appointmentsDT;
            }

            public static MentalViewDataSet GetAppointmentsWithImportantNotes(Int64 tenantId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    Data.Business.ConfigureDataSet(ref ds);
                    ds.EnforceConstraints = false;

                    connection.Open();

                    MentalViewDataSetTableAdapters.AppointmentsTableAdapter AppointmentsTA = new MentalViewDataSetTableAdapters.AppointmentsTableAdapter();
                    AppointmentsTA.Connection = connection;
                    AppointmentsTA.FillAppointmentsWithImportantNotes(ds.Appointments, tenantId);

                    connection.Close();

                    return ds;
                }
            }

            public static MentalViewDataSet GetUpcomingRecurrentAppointment(DateTime startTime, string customRecurrenceId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    Data.Business.ConfigureDataSet(ref ds);
                    ds.EnforceConstraints = false;

                    connection.Open();

                    MentalViewDataSetTableAdapters.AppointmentsTableAdapter AppointmentsTA = new MentalViewDataSetTableAdapters.AppointmentsTableAdapter();
                    AppointmentsTA.Connection = connection;
                    AppointmentsTA.FillByUpcomingRecurrentAppointments(ds.Appointments, customRecurrenceId, startTime.Date);

                    connection.Close();

                    return ds;
                }
            }

            public static MentalViewDataSet GetNextAppointmentOfRecurrence(Int64 tenantId, Int64 appointmentId)
            {

            }

            public static DataTable GetEventsByDates(Int64 tenantId, DateTime startDate, DateTime endDate, AppointmentsSearchByDateType appointmentsSearchByDateType, Int64? contactId, Int64[] usersIds = null)
            {
                DataTable appointmentsDT = new DataTable();
                DataTable reccurentAppointmentsTable = new DataTable();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    #region  Βρίσκει όλα τα appointments (κανονικά και με recurrence) μεταξύ των ημερομηνιών startDate και endDate, και τα Tasks που είναι visible σε όλους τους θεραπευτές
                    string queryText = @"SELECT *, 
	                    (SELECT Contacts.FirstName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactFirstName,
                        (SELECT Contacts.LastName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactLastName,
                        ISNULL((SELECT Users.FullName FROM Users WHERE Users.UserId = Appointments.UserId), '') AS UserFullName
                        --ISNULL((SELECT AppointmentCategories.CategoryName FROM AppointmentCategories WHERE AppointmentCategories.AppointmentCategoryId = Appointments.AppointmentCategoryId), '') AS AppointmentCategoryName
                        FROM Appointments
                        WHERE TenantId=" + tenantId.ToString() + " AND AppointmentType='Event' AND ((" + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " BETWEEN '@startDate' AND '@endDate') " +
                        " OR VisibleToAll=1)";
                    if (contactId.HasValue)
                    {
                        queryText += @" AND ContactId = " + contactId.ToString() + " ";
                    }
                    if (usersIds != null && usersIds.Length > 0)
                    {
                        queryText += @" AND (";
                        foreach (Int64 userId in usersIds)
                        {
                            queryText += @" UserId=" + userId.ToString() + " OR ";
                        }
                        queryText = queryText.Substring(0, queryText.Length - 3);
                        queryText += @" ) ";
                    }
                    queryText += " ORDER BY " + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " ASC";
                    // Για ημερομηνίες σε sql CONVERT(VARCHAR(5),StartDate,108)


                    using (SqlDataAdapter da = new SqlDataAdapter(queryText, connection))
                    {
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.ToString("yyyy/MM/dd 00:00:00"));
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", endDate.ToString("yyyy/MM/dd 23:59:59"));

                        da.Fill(appointmentsDT);
                    }

                    ////Βρίσκει τα appointments που είναι visisble σε όλους, δημιουργεί αντίγραφα με το UserId των άλλων θεραπευτών ώστε να εμφανιστεί στο ημερολόγιο όλων.
                    //DataRow[] visibleToAllAppointmentRows = appointmentsDT.Select("VisibleToAll=1");
                    //foreach (DataRow visibleToAllAppointmentsRow in visibleToAllAppointmentRows)
                    //{
                    //    foreach (int userId in usersIds)
                    //    {
                    //        if (userId != Convert.ToInt32(visibleToAllAppointmentsRow["UserId"]))  //Παρακάτω δημιουργούμε το νέο fake appointment μόνο για τους υπόλοιπους θεραπευτές (εκτός από αυτόν που του ανήκει το appointment)
                    //        {
                    //            DataRow fakeAppointmentsRow = appointmentsDT.LoadDataRow(visibleToAllAppointmentsRow.ItemArray, true);
                    //            fakeAppointmentsRow["UserId"] = userId;  //Αλλάζει το UserId για να εμφανιστεί και στον άλλο θεραπευτή.
                    //        }
                    //    }
                    //}
                    #endregion


                }

                //appointmentsDT.Merge(reccurentAppointmentsTable);
                appointmentsDT.Columns.Add("ContactFullName", typeof(string), @"ContactLastName+ISNULL(' '+ContactFirstName,'')");
                appointmentsDT.Columns.Add("Summary", typeof(string), @"IIF(0=1, '<i style=""margin-right:5px;"" class=""fa fa-asterisk""></i>', '') + IIF(SupervisionRequest=1, '<i style=""margin-right:5px; color:orange;"" class=""fa fa-star fa-solid""></i>', '') + ISNULL(TRIM(ContactFullName), '')");

                return appointmentsDT;
            }

            /// <summary>
            /// Checks if the specified Appointment is conflicting with any other Appointment or Task. This function should be used only for checking Appointments (not Tasks).
            /// </summary>
            /// <param name="tenantId"></param>
            /// <param name="selfAppointmentId"></param>
            /// <param name="customRecurrenceId"></param>
            /// <param name="roomIDsToSearch"></param>
            /// <param name="startDate"></param>
            /// <param name="endDate"></param>
            /// <returns></returns>
            public static List<string> GetOccupiedRooms(Int64 tenantId, Int64 selfAppointmentId, Int64 selfUserId, string customRecurrenceId, string[] roomIDsToSearch, DateTime startDate, DateTime endDate)
            {
                DataTable dt = new DataTable();
                List<string> rooms = new List<string>();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(@"SELECT DISTINCT Room FROM Appointments WHERE TenantId=" + tenantId.ToString()
                        + " AND AppointmentId <> " + selfAppointmentId.ToString()
                        + " AND ((StartTime  BETWEEN '" + startDate.ToString("yyyy-MM-dd HH:mm") + @"' AND '" + endDate.ToString("yyyy-MM-dd HH:mm") + @"')
                        OR(EndTime  BETWEEN '" + startDate.ToString("yyyy-MM-dd HH:mm") + @"' AND '" + endDate.ToString("yyyy-MM-dd HH:mm") + @"') 
                        OR (StartTime < '" + startDate.ToString("yyyy-MM-dd HH:mm") + @"' AND EndTime > '" + endDate.ToString("yyyy-MM-dd HH:mm") + @"')) 
                        AND StartTime <> '" + endDate.ToString("yyyy-MM-dd HH:mm") + @"' 
	                    AND EndTime <> '" + startDate.ToString("yyyy-MM-dd HH:mm") + @"'
                        AND (AppointmentType='Appointment' AND Canceled = 0  
                        AND Room IN (" + string.Join(",", roomIDsToSearch) + @") )  
                        AND ( 
                            (AppointmentType='Appointment' AND Canceled = 0 ) " + @"   
                            OR (AppointmentType='Event' AND BlockOther=1 AND VisibleToAll=0 AND UserId=" + selfUserId.ToString() + @") " +   //Αν είναι Event του ίδιου Θεραπευτή που εμποδίζει τα άλλα Events/Appointments.
                            @" OR(AppointmentType = 'Event' AND BlockOther = 1 AND VisibleToAll = 0 AND UserId <> " + selfUserId.ToString() + @") " +   //Αν είναι Event άλλου Θεραπευτή που εμποδίζει τα άλλα Events/Appointments αλλά όχι VisibleToAll=1.
                            @" OR(AppointmentType = 'Event' AND BlockOther = 1  AND VisibleToAll=1) )" +   //Αν είναι Event άλλου Θεραπευτή αλλά VisibleToAll=1 που εμποδίζει τα άλλα Events/Appointments.
                        @"", connection))
                    {
                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(dt);
                        }
                    }
                }

                foreach (DataRow dr in dt.Rows)
                {
                    rooms.Add(dr["Room"].ToString());
                }

                return rooms;
            }

            /// <summary>
            /// Checks if the specified Task is conflicting with any other Task or Appointment. <b>This function should be used only for checking Tasks (not Appointments).</b>
            /// </summary>
            /// <param name="tenantId"></param>
            /// <param name="selfAppointmentId"></param>
            /// <param name="customRecurrenceId"></param>
            /// <param name="roomIDsToSearch"></param>
            /// <param name="startDate"></param>
            /// <param name="endDate"></param>
            /// <returns></returns>
            public static List<Int64> CheckTaskConflicts(Int64 tenantId, Int64 selfAppointmentId, Int64 selfUserId, string customRecurrenceId, DateTime startDate, DateTime endDate)
            {
                DataTable dt = new DataTable();
                List<Int64> appointments = new List<Int64>();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(@"SELECT DISTINCT AppointmentId FROM Appointments WHERE TenantId=" + tenantId.ToString()
                        + " AND AppointmentId <> " + selfAppointmentId.ToString()
                        + " AND ((StartTime  BETWEEN '" + startDate.ToString("yyyy-MM-dd HH:mm") + @"' AND '" + endDate.ToString("yyyy-MM-dd HH:mm") + @"')
                        OR(EndTime  BETWEEN '" + startDate.ToString("yyyy-MM-dd HH:mm") + @"' AND '" + endDate.ToString("yyyy-MM-dd HH:mm") + @"') 
                        OR (StartTime < '" + startDate.ToString("yyyy-MM-dd HH:mm") + @"' AND EndTime > '" + endDate.ToString("yyyy-MM-dd HH:mm") + @"')) 
                        AND StartTime <> '" + endDate.ToString("yyyy-MM-dd HH:mm") + @"' 
	                    AND EndTime <> '" + startDate.ToString("yyyy-MM-dd HH:mm") + @"'
                        AND ( " +
                            //@"(AppointmentType='Appointment' AND Canceled = 0 ) " 
                            @" (AppointmentType='Event' AND BlockOther=1 AND VisibleToAll=0 AND UserId=" + selfUserId.ToString() + @") " +   //Αν είναι Event του ίδιου Θεραπευτή που εμποδίζει τα άλλα Events/Appointments.
                            @" OR(AppointmentType = 'Event' AND BlockOther = 1 AND VisibleToAll = 0 AND UserId <> " + selfUserId.ToString() + @") " +   //Αν είναι Event άλλου Θεραπευτή που εμποδίζει τα άλλα Events/Appointments αλλά όχι VisibleToAll=1.
                            @" OR(AppointmentType = 'Event' AND BlockOther = 1  AND VisibleToAll=1) )" +   //Αν είναι Event άλλου Θεραπευτή αλλά VisibleToAll=1 που εμποδίζει τα άλλα Events/Appointments.
                        @"", connection))
                    {
                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(dt);
                        }
                    }
                }

                foreach (DataRow dr in dt.Rows)
                {
                    appointments.Add(Convert.ToInt64(dr["AppointmentId"]));
                }

                return appointments;
            }

            public static void DeleteAppointment(Int64 appointmentId)
            {
                string sqlQuery = "DELETE FROM Appointments WHERE AppointmentId=" + appointmentId.ToString();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(sqlQuery, connection))
                    {
                        connection.Open();
                        cmd.CommandType = CommandType.Text;
                        cmd.ExecuteNonQuery();
                    }
                }
            }

            public static DataTable GetPaymentTypes()
            {
                DataTable dt = new DataTable();
                dt.Columns.Add("PaymentTypeValue", typeof(string));
                dt.Columns.Add("PaymentTypeText", typeof(string));

                dt.Rows.Add(new string[] { "Check", "Επιταγή" });
                dt.Rows.Add(new string[] { "Cash", "Μετρητά" });
                dt.Rows.Add(new string[] { "Card", "Κάρτα" });

                dt.AcceptChanges();
                return dt;
            }

            /// <summary>
            /// Returns all the appointments that Clients are not notified by email.
            /// </summary>
            /// <param name="appointmentDate">The date that appointment happens.</param>
            /// <returns></returns>
            public static MentalViewDataSet GetAppointmentForContactsEmailNotification(Int64 tenantId, DateTime startDate, DateTime endDate)
            {
                MentalViewDataSet ds = new MentalViewDataSet();
                ds.EnforceConstraints = false;

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    //Διάβάζει πρώτα όλους τους θεραπευτές.
                    UsersTableAdapter usersTA = new UsersTableAdapter();
                    usersTA.Connection = connection;
                    usersTA.Fill(ds.Users);

                    string appointmensQuery = @"SELECT * FROM Appointments WHERE TenantId=" + tenantId.ToString() +
                    " AND ((StartTime BETWEEN '@startDate' AND '@endDate') AND ContactEmailNotificationSent = 0 )";


                    //Βρίσκει τις συνεδρίες που πρέπει να στείλει email στους πελάτες.
                    using (SqlDataAdapter da = new SqlDataAdapter(appointmensQuery, connection))
                    {
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.ToString("yyyy/MM/dd 00:00:00"));
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", endDate.ToString("yyyy/MM/dd 23:59:59"));

                        da.Fill(ds.Appointments);
                    }

                    //Βρίσκει τους πελάτες που σχετίζονται με τις συνεδρίες.
                    string contactsQuery = @"SELECT * FROM Contacts WHERE ContactId IN (SELECT ContactId FROM Appointments WHERE TenantId=" + tenantId.ToString() +
                        " AND ((StartTime BETWEEN '@startDate' AND '@endDate') AND ContactEmailNotificationSent = 0))";

                    using (SqlDataAdapter da = new SqlDataAdapter(contactsQuery, connection))
                    {
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.ToString("yyyy/MM/dd 00:00:00"));
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", endDate.ToString("yyyy/MM/dd 23:59:59"));

                        da.Fill(ds.Contacts);
                    }
                }

                return ds;
            }
        }

        public class AppointmentCategoriesBusiness
        {
            public static MentalViewDataSet.AppointmentCategoriesDataTable GetAllAppointmentCategories(Int64 tenantId)
            {
                MentalViewDataSet ds = new MentalViewDataSet();
                ds.EnforceConstraints = false;
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        MentalViewDataSetTableAdapters.TableAdapterManager taManager = new MentalViewDataSetTableAdapters.TableAdapterManager();
                        InitTableAdapterManager(ref taManager, connection);

                        using (taManager)
                        {
                            taManager.AppointmentCategoriesTableAdapter.FillByTenantId(ds.AppointmentCategories, tenantId);
                            return ds.AppointmentCategories;
                        }
                    }
                }
                catch (Exception exp)
                {
                    throw exp;
                }
                finally
                {

                }
                //return ds.AppointmentCategories;
            }

            //public static MentalViewDataSet GetByAppointmentCategoryId(Int64 appointmentCategoryId)
            //{
            //    using (SqlConnection connection = new SqlConnection(connectionString))
            //    {
            //        MentalViewDataSet ds = new Data.MentalViewDataSet();
            //        ds.EnforceConstraints = false;
            //        Data.Business.ConfigureDataSet(ref ds);
            //        MentalViewDataSetTableAdapters.AppointmentCategoriesTableAdapter appointmentCategoriesTA = new MentalViewDataSetTableAdapters.AppointmentCategoriesTableAdapter();
            //        appointmentCategoriesTA.Connection = connection;
            //        appointmentCategoriesTA.FillByAppointmentCategoryId(ds.AppointmentCategories, appointmentCategoryId);

            //        return ds;
            //    }
            //}
        }

        public class UsersBusiness
        {
            public static void SaveUsers(ref MentalViewDataSet ds)
            {
                SqlConnection connection = null;
                SqlTransaction transaction = null;

                try
                {
                    using (connection = new SqlConnection(connectionString))
                    {
                        connection.Open();
                        transaction = connection.BeginTransaction();

                        //Διαβάζει όλα τα Roles.
                        if (ds.Users.Count > 0)
                        {
                            MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
                            rolesTA.Connection = connection;
                            rolesTA.Transaction = transaction;
                            rolesTA.FillByTenantId(ds.Roles, ds.Users[0].TenantId);
                        }

                        // First check and process Guest users
                        foreach (MentalViewDataSet.UsersRow userDR in ds.Users)
                        {
                            if (userDR.RoleName == "Guest")
                            {
                                // If guest user is being deleted
                                if (userDR.RowState == DataRowState.Deleted)
                                {
                                    // Find all Contacts where the User is Guest
                                    using (MentalViewDataSetTableAdapters.ContactsTableAdapter contactsTA = new MentalViewDataSetTableAdapters.ContactsTableAdapter())
                                    {
                                        contactsTA.Connection = connection;
                                        contactsTA.Transaction = transaction;
                                        ds.Contacts.Merge(contactsTA.GetDataByGuestId(userDR.UserId));
                                    }

                                    // Process contacts associated with the guest user
                                    foreach (MentalViewDataSet.ContactsRow contactDR in ds.Contacts)
                                    {
                                        if (contactDR.GuestId == userDR.UserId)
                                        {
                                            contactDR.SetGuestIdNull();
                                        }
                                    }
                                }
                            }

                            // If guest user is being changed και πλέον δεν είναι Guest.
                            if (userDR.RowState == DataRowState.Modified && userDR.RoleName != "Guest")
                            {
                                // Find all Contacts where the User is Guest
                                using (MentalViewDataSetTableAdapters.ContactsTableAdapter contactsTA = new MentalViewDataSetTableAdapters.ContactsTableAdapter())
                                {
                                    contactsTA.Connection = connection;
                                    contactsTA.Transaction = transaction;
                                    ds.Contacts.Merge(contactsTA.GetDataByGuestId(userDR.UserId));
                                }

                                // Process contacts associated with the guest user
                                foreach (MentalViewDataSet.ContactsRow contactDR in ds.Contacts)
                                {
                                    if (contactDR.GuestId == userDR.UserId)
                                    {
                                        contactDR.SetGuestIdNull();
                                    }
                                }
                            }
                        }

                        // Save Users
                        using (MentalViewDataSetTableAdapters.UsersTableAdapter usersTA = new MentalViewDataSetTableAdapters.UsersTableAdapter())
                        {
                            usersTA.Connection = connection;
                            usersTA.Transaction = transaction;
                            usersTA.Update(ds.Users);
                        }

                        // Save Contacts
                        using (MentalViewDataSetTableAdapters.ContactsTableAdapter contactsTA = new MentalViewDataSetTableAdapters.ContactsTableAdapter())
                        {
                            contactsTA.Connection = connection;
                            contactsTA.Transaction = transaction;
                            contactsTA.Update(ds.Contacts);
                        }

                        // If we got here, commit the transaction
                        transaction.Commit();

                        // Accept changes in the dataset
                        ds.Users.AcceptChanges();
                        ds.Contacts.AcceptChanges();
                    }
                }
                catch (Exception exp)
                {
                    // Roll back transaction and changes in dataset
                    if (transaction != null)
                    {
                        try
                        {
                            transaction.Rollback();
                        }
                        catch { /* Ignore rollback errors */ }
                    }

                    ds.Users.RejectChanges();
                    ds.Contacts.RejectChanges();
                    throw exp;
                }
                finally
                {
                    if (transaction != null)
                    {
                        transaction.Dispose();
                    }
                    if (connection != null && connection.State == ConnectionState.Open)
                    {
                        connection.Close();
                    }
                }
            }


            /// <summary>
            /// Saves only data of Users.
            /// </summary>
            //public static void SaveUsers(ref MentalViewDataSet ds)
            //{
            //    SqlConnection connection = null;

            //    try
            //    {
            //        using (connection = new SqlConnection(connectionString))
            //        {
            //            //Διαβάζει όλα τα Roles.
            //            if (ds.Users.Count > 0)
            //            {
            //                MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
            //                rolesTA.Connection = connection;
            //                rolesTA.FillByTenantId(ds.Roles, ds.Users[0].TenantId);
            //            }

            //            #region Για κάθε User που είναι Guest.
            //            foreach (MentalViewDataSet.UsersRow userDR in ds.Users)
            //            {
            //                if (userDR.RoleName == "Guest")
            //                {
            //                    //Αν πρόκειτα να διαγραφεί ο Guest User.
            //                    if (userDR.RowState == DataRowState.Deleted)
            //                    {
            //                        //Βρίσκουμε όλα τα Contacts που ο User είναι Guest.
            //                        using (MentalViewDataSetTableAdapters.ContactsTableAdapter contactsTA = new MentalViewDataSetTableAdapters.ContactsTableAdapter())
            //                        {
            //                            contactsTA.Connection = connection;
            //                            ds.Contacts.Merge(contactsTA.GetDataByGuestId(userDR.UserId));
            //                        }

            //                        foreach (MentalViewDataSet.ContactsRow contactDR in ds.Contacts)
            //                        {
            //                            //Αν ο Guest User είναι ο μόνος θεραπευτής του Contact, τότε διαγράφουμε και το Contact.
            //                            if (contactDR.GuestId == userDR.UserId)
            //                            {
            //                                contactDR.SetGuestIdNull();
            //                            }
            //                        }
            //                    }
            //                }
            //            }
            //            #endregion



            //            using (MentalViewDataSetTableAdapters.UsersTableAdapter usersTA = new MentalViewDataSetTableAdapters.UsersTableAdapter())
            //            {
            //                usersTA.Connection = connection;
            //                usersTA.Update(ds.Users);

            //                ds.Users.AcceptChanges();
            //            }
            //        }
            //    }
            //    catch (Exception exp)
            //    {
            //        ds.Users.RejectChanges();
            //        throw exp;
            //    }
            //    finally
            //    {
            //        if (connection != null)
            //        {
            //            connection.Close();
            //        }
            //    }
            //}

            public static MentalViewDataSet GetUserById(Int64 userId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    Data.Business.ConfigureDataSet(ref ds);

                    MentalViewDataSetTableAdapters.UsersTableAdapter usersTA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
                    usersTA.Connection = connection;
                    usersTA.FillByUserId(ds.Users, userId);

                    //MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
                    //rolesTA.Connection = connection;
                    //rolesTA.FillByTenantId(ds.Roles, ds.Users[0].TenantId);

                    //Αν υπάρχει User, βρίσκει και τα Roles.
                    if (ds.Users.Count > 0)
                    {
                        MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new RolesTableAdapter();
                        rolesTA.Connection = connection;
                        rolesTA.FillByTenantId(ds.Roles, ds.Users[0].TenantId);
                    }

                    return ds;
                }
            }

            public static MentalViewDataSet GetUserByUsername(string username)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    Data.Business.ConfigureDataSet(ref ds);

                    MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
                    rolesTA.Connection = connection;
                    rolesTA.Fill(ds.Roles);
                    MentalViewDataSetTableAdapters.UsersTableAdapter customersTA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
                    customersTA.Connection = connection;
                    customersTA.FillByUsername(ds.Users, username);

                    return ds;
                }
            }

            public static MentalViewDataSet GetUsers(Int64 tenantId, string filter, string sortExpression, Data.SortDirection? sortDirection, int pageIndex, int pageSize, out int totalCount)
            {
                MentalViewDataSet ds = new MentalViewDataSet();
                totalCount = 0;

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet rolesDS = Business.AdministrationBusiness.GetAllRoles(tenantId);

                    using (SqlCommand cmd = new SqlCommand("GetUsers", connection))
                    {
                        DataSet genericDS = new DataSet();

                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add("@TenantId", SqlDbType.BigInt).Value = tenantId;
                        cmd.Parameters.Add("@Filter", SqlDbType.NVarChar).Value = filter;
                        cmd.Parameters.Add("@OrderBy", SqlDbType.NVarChar).Value = sortExpression;
                        if (sortDirection.HasValue)
                        {
                            cmd.Parameters.Add("@SortType", SqlDbType.NVarChar).Value = sortDirection == SortDirection.Ascending ? "ASC" : "DESC";
                        }
                        cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                        cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;

                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(genericDS);
                        }

                        ds.Users.Merge(genericDS.Tables[0]);
                        ds.Roles.Merge(rolesDS.Roles);
                        ds.AcceptChanges();
                        totalCount = int.Parse(genericDS.Tables[1].Rows[0][0].ToString());
                    }
                }

                return ds;
            }

            public static DataTable GetAllUsersList(Int64 tenantId)
            {
                DataTable dt = new DataTable();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM Users WHERE TenantId=" + tenantId + " ORDER BY FullName", connection))
                    {
                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(dt);
                        }
                    }
                }

                return dt;
            }


            public static MentalViewDataSet GetAllDoctorUsersList(Int64 tenantId)
            {
                MentalViewDataSet ds = new MentalViewDataSet();
                ds.EnforceConstraints = false;

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    Data.Business.ConfigureDataSet(ref ds);
                    MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
                    rolesTA.Connection = connection;
                    rolesTA.FillByTenantId(ds.Roles, tenantId);

                    MentalViewDataSetTableAdapters.UsersTableAdapter doctorsTA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
                    doctorsTA.Connection = connection;
                    doctorsTA.FillWithAllDoctors(ds.Users, tenantId);

                    return ds;
                }

                //using (SqlConnection connection = new SqlConnection(connectionString))
                //{
                //    using (SqlCommand cmd = new SqlCommand("SELECT * FROM Users WHERE TenantId=" + tenantId + " AND IsDoctor=1 ORDER BY FullName", connection))
                //    {
                //        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                //        {
                //            da.Fill(dt);
                //        }
                //    }
                ////}

                // return dt;
            }

            public static MentalViewDataSet GetAllGuestUsers(Int64 tenantId)
            {
                MentalViewDataSet ds = new MentalViewDataSet();
                ds.EnforceConstraints = false;

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    Data.Business.ConfigureDataSet(ref ds);
                    //MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
                    //rolesTA.Connection = connection;
                    //rolesTA.FillByTenantId(ds.Roles, tenantId);

                    MentalViewDataSetTableAdapters.UsersTableAdapter doctorsTA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
                    doctorsTA.Connection = connection;
                    doctorsTA.FillGuestsByTenantId(ds.Users, tenantId);

                    return ds;
                }

                //using (SqlConnection connection = new SqlConnection(connectionString))
                //{
                //    using (SqlCommand cmd = new SqlCommand("SELECT * FROM Users WHERE TenantId=" + tenantId + " AND IsDoctor=1 ORDER BY FullName", connection))
                //    {
                //        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                //        {
                //            da.Fill(dt);
                //        }
                //    }
                ////}

                // return dt;
            }

            public static void DeleteUser(Int64 userId)
            {
                string sqlQuery = "DELETE FROM Users WHERE UserId=" + userId.ToString();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(sqlQuery, connection))
                    {
                        connection.Open();
                        cmd.CommandType = CommandType.Text;
                        cmd.ExecuteNonQuery();
                    }
                }
            }

            public static bool CheckUsernameExists(Int64 userId, string username)
            {
                SqlDataReader reader;
                SqlCommand command;

                int result = 0;
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        command = new SqlCommand();
                        command.CommandText = @"SELECT Count(*) AS Result FROM Users WHERE [Users].[Username]='" + username + "' AND UserId !=" + userId.ToString() + ";";
                        command.Connection = connection;
                        connection.Open();

                        reader = command.ExecuteReader();
                        if (reader == null)
                        {
                            throw new Exception("variable reader=null");
                        }
                        reader.Read();
                        result = reader.GetInt32(0);
                        reader.Close();
                        connection.Close();
                    }
                }
                finally
                {

                }

                if (result >= 1) //Αν βρέθηκε τουλάχιστον ένα πρόσωπο
                {
                    return true;
                }
                else  //Αν δεν βρεθηκε πρόσωπο 
                {
                    return false;
                }
            }
        }

        public class TenantsBusiness
        {
            public static MentalViewDataSet GetAllTenants()
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    MentalViewDataSetTableAdapters.TenantsTableAdapter tenantsTA = new MentalViewDataSetTableAdapters.TenantsTableAdapter();
                    tenantsTA.Connection = connection;
                    tenantsTA.Fill(ds.Tenants);

                    return ds;
                }
            }

            public static async Task<bool> AuthenticateUserInJoomla(string joomlaUrl, string username, string password)
            {
                try
                {
                    HttpClient client = new HttpClient();
                    string requestUri = joomlaUrl + @"AuthenticateUser.php?username=" + HttpUtility.UrlEncode(username); //+ @"&password=" + password;

                    //string responseString = await client.GetStringAsync(requestUri);
                    ////HttpContent content = new StringContent(@"{""password"":""" + password + @"""}", Encoding.UTF8, "application/json");
                    //HttpContent content = new StringContent(@"{'password':'" + password + @"'}", Encoding.UTF8, "application/json");
                    HttpContent content = new StringContent(password, Encoding.UTF8, "application/json");
                    HttpResponseMessage response = client.PostAsync(requestUri, content).Result;
                    if (response.IsSuccessStatusCode)
                    {
                        string responseString = await response.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse

                        if (responseString.Contains("Authenticated"))
                        {
                            return true;
                        }
                        else
                        {
                            return false;
                        }
                    }

                    return false;
                }
                catch (Exception exp)
                {
                    Data.ExceptionLogger.LogException(exp);
                    return false;
                }
            }

            public static async Task<MentalViewDataSet> GetTenant(string username)
            {
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        MentalViewDataSet ds = new Data.MentalViewDataSet();
                        ds.EnforceConstraints = false;
                        MentalViewDataSetTableAdapters.TenantsTableAdapter tenantsTA = new MentalViewDataSetTableAdapters.TenantsTableAdapter();
                        tenantsTA.Connection = connection;
                        tenantsTA.FillByUser(ds.Tenants, username);

                        return ds;
                    }
                }
                catch (Exception ex)
                {
                    //logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString() });
                    throw;
                }
            }

            /// <summary>
            /// Επιστρέφει πληροφορίες για το 1ο active subscription του χρήστη που θα βρει. Πιθανόν ο χρήστης να έχει περισσότερα από ένα active subscriptions.
            /// </summary>
            /// <param name="userId"></param>
            /// <returns></returns>
            public static async Task<MembershipSubscription> GetActiveSubscriptionDetails(string joomlaWebsiteDomain, int userId)
            {
                try
                {
                    HttpClient client = new HttpClient();

                    MembershipSubscription subscription = new MembershipSubscription();

                    //string domain = joomlaWebsiteDomain;
                    string requestUri = joomlaWebsiteDomain + @"index.php?option=com_osmembership&task=api.get_user_active_subscriptions&api_key=A8SF7A9F7V9SF87AS09DF87SDSD&user_id=" + userId;
                    Console.WriteLine("JoomlaWebsiteDomain: " + joomlaWebsiteDomain);
                    Data.ExceptionLogger.LogException(new ApplicationException("JoomlaWebsiteDomain: " + joomlaWebsiteDomain));

                    HttpMessageHandler handler = new HttpClientHandler();
                    var httpClient = new HttpClient(handler)
                    {
                        Timeout = new TimeSpan(0, 2, 0)
                    };

                    //httpClient.DefaultRequestHeaders.Add("ContentType", "application/json");
                    httpClient.DefaultRequestHeaders.Add("Accept", "*/*");

                    //var plainTextBytes = System.Text.Encoding.UTF8.GetBytes("petermanesis:ljb33bcbg@A33");
                    //string val = System.Convert.ToBase64String(plainTextBytes);
                    //httpClient.DefaultRequestHeaders.Add("Authorization", "Basic " + val);

                    HttpResponseMessage response = httpClient.GetAsync(requestUri).Result;
                    string content = string.Empty;
                    string subscriptionsArray;

                    using (StreamReader stream = new StreamReader(response.Content.ReadAsStreamAsync().Result))
                    {
                        content = stream.ReadToEnd();
                        Data.ExceptionLogger.LogException(new ApplicationException("Content: " + content));
                    }

                    if (content.Contains("data"))
                    {
                        var jsonObj = JObject.Parse(content);
                        var dataArray = jsonObj["data"] as JArray;

                        //Αν υπάρχει τουλάχιστον ένα subscription στα data
                        if (dataArray != null && dataArray.Count > 0)
                        {
                            var firstSubscription = dataArray[0];
                            subscription.Id = firstSubscription["id"].Value<int>();
                            subscription.PlanId = firstSubscription["plan_id"].Value<int>();
                            subscription.UserId = firstSubscription["user_id"].Value<int>();

                            string from_date = firstSubscription["from_date"]?.ToString();
                            if (!string.IsNullOrEmpty(from_date))
                            {
                                subscription.FromDate = DateTime.Parse(from_date);
                            }

                            string to_date = firstSubscription["to_date"]?.ToString();
                            if (!string.IsNullOrEmpty(to_date))
                            {
                                subscription.ToDate = DateTime.Parse(to_date);
                            }

                            return subscription;
                        }

                        return null;
                    }

                    return null;
                }
                catch (Exception exp)
                {
                    Data.ExceptionLogger.LogException(exp);
                    return null;
                }
            }

            public static void SetLastLoggedUserData(Int64 tenantId, string userFullName, Int64 userId, DateTime loggedDate)
            {
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        MentalViewDataSetTableAdapters.TenantsTableAdapter tenantsTA = new MentalViewDataSetTableAdapters.TenantsTableAdapter();
                        tenantsTA.Connection = connection;
                        tenantsTA.SetLastLoggedUserData(userId, userFullName, loggedDate, tenantId);
                    }
                }
                catch (Exception ex)
                {
                    //logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString() });
                    throw;
                }
            }
        }

        public class EmailTemplatesBusiness
        {
            /// <summary>
            /// Saves only data of EmailTemplates.
            /// </summary>
            public static void SaveEmailTemplates(ref MentalViewDataSet ds)
            {
                SqlConnection connection = null;

                try
                {
                    using (connection = new SqlConnection(connectionString))
                    {
                        using (MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter emailTemplatesTA = new MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter())
                        {
                            emailTemplatesTA.Connection = connection;
                            emailTemplatesTA.Update(ds.EmailTemplates);

                            ds.EmailTemplates.AcceptChanges();
                        }
                    }
                }
                catch (Exception exp)
                {
                    ds.EmailTemplates.RejectChanges();
                    throw exp;
                }
                finally
                {
                    if (connection != null)
                    {
                        connection.Close();
                    }
                }
            }

            public static MentalViewDataSet GetEmailTemplateById(Int64 emailTemplateId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    Data.Business.ConfigureDataSet(ref ds);
                    MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter emailTemplatesTA = new MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter();
                    emailTemplatesTA.Connection = connection;
                    emailTemplatesTA.FillByEmailTemplateId(ds.EmailTemplates, emailTemplateId);

                    return ds;
                }
            }

            public static MentalViewDataSet GetEmailTemplateByTitle(Int64 tenantId, string emailTemplateTitle)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    Data.Business.ConfigureDataSet(ref ds);
                    MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter emailTemplatesTA = new MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter();
                    emailTemplatesTA.Connection = connection;
                    emailTemplatesTA.FillByTitle(ds.EmailTemplates, emailTemplateTitle, tenantId);

                    return ds;
                }
            }

            public static MentalViewDataSet GetEmailTemplateCategoriesByTenantId(Int64 tenantId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    Data.Business.ConfigureDataSet(ref ds);
                    MentalViewDataSetTableAdapters.EmailTemplateCategoriesTableAdapter emailTemplateCategoriesTA = new MentalViewDataSetTableAdapters.EmailTemplateCategoriesTableAdapter();
                    emailTemplateCategoriesTA.Connection = connection;
                    emailTemplateCategoriesTA.FillByTenantId(ds.EmailTemplateCategories, tenantId);

                    return ds;
                }
            }

            //public static MentalViewDataSet GetEmailTemplates(Int64 tenantId, string filter, string sortExpression, Data.SortDirection? sortDirection, int pageIndex, int pageSize, out int totalCount)
            //{
            //    MentalViewDataSet ds = new MentalViewDataSet();
            //    totalCount = 0;

            //    using (SqlConnection connection = new SqlConnection(connectionString))
            //    {
            //        using (SqlCommand cmd = new SqlCommand("GetEmailTemplates", connection))
            //        {
            //            DataSet genericDS = new DataSet();

            //            cmd.CommandType = CommandType.StoredProcedure;
            //            cmd.Parameters.Add("@TenantId", SqlDbType.BigInt).Value = tenantId;
            //            cmd.Parameters.Add("@Filter", SqlDbType.NVarChar).Value = filter;
            //            cmd.Parameters.Add("@OrderBy", SqlDbType.NVarChar).Value = sortExpression;
            //            if (sortDirection.HasValue)
            //            {
            //                cmd.Parameters.Add("@SortType", SqlDbType.NVarChar).Value = sortDirection == SortDirection.Ascending ? "ASC" : "DESC";
            //            }
            //            cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
            //            cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;

            //            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            //            {
            //                da.Fill(genericDS);
            //            }

            //            ds.EmailTemplates.Merge(genericDS.Tables[0]);
            //            ds.AcceptChanges();
            //            totalCount = int.Parse(genericDS.Tables[1].Rows[0][0].ToString());
            //        }
            //    }

            //    return ds;
            //}

            public static MentalViewDataSet GetAllEmailTemplatesList(Int64 tenantId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    Data.Business.ConfigureDataSet(ref ds);

                    MentalViewDataSetTableAdapters.EmailTemplateCategoriesTableAdapter emailTemplateCategoriesTA = new MentalViewDataSetTableAdapters.EmailTemplateCategoriesTableAdapter();
                    emailTemplateCategoriesTA.Connection = connection;
                    emailTemplateCategoriesTA.FillByTenantId(ds.EmailTemplateCategories, tenantId);

                    MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter emailTemplatesTA = new MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter();
                    emailTemplatesTA.Connection = connection;
                    emailTemplatesTA.FillByTenantId(ds.EmailTemplates, tenantId);

                    return ds;
                }

                //DataTable dt = new DataTable();

                //using (SqlConnection connection = new SqlConnection(connectionString))
                //{
                //    using (SqlCommand cmd = new SqlCommand("SELECT * FROM EmailTemplates WHERE TenantId=" + tenantId + " ORDER BY Title", connection))
                //    {
                //        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                //        {
                //            da.Fill(dt);
                //        }
                //    }
                //}

                //return dt;
            }

            public static void DeleteEmailTemplate(Int64 emailTemplateId)
            {
                string sqlQuery = "DELETE FROM EmailTemplates WHERE EmailTemplateId=" + emailTemplateId.ToString();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(sqlQuery, connection))
                    {
                        connection.Open();
                        cmd.CommandType = CommandType.Text;
                        cmd.ExecuteNonQuery();
                    }
                }
            }

            public static bool CheckEmailTemplateTitleExists(Int64 emailTemplateId, string title)
            {
                SqlDataReader reader;
                SqlCommand command;

                int result = 0;
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        command = new SqlCommand();
                        command.CommandText = @"SELECT Count(*) AS Result FROM EmailTemplates WHERE [EmailTemplates].[Title]='" + title + "' AND EmailTemplateId !=" + emailTemplateId.ToString() + ";";
                        command.Connection = connection;
                        connection.Open();

                        reader = command.ExecuteReader();
                        if (reader == null)
                        {
                            throw new Exception("variable reader=null");
                        }
                        reader.Read();
                        result = reader.GetInt32(0);
                        reader.Close();
                        connection.Close();
                    }
                }
                finally
                {

                }

                if (result >= 1) //Αν βρέθηκε τουλάχιστον ένα πρόσωπο
                {
                    return true;
                }
                else  //Αν δεν βρεθηκε πρόσωπο 
                {
                    return false;
                }
            }
        }

        public class PartnersBusiness
        {
            /// <summary>
            /// Saves only data of Partners.
            /// </summary>
            public static void SavePartners(ref MentalViewDataSet ds)
            {
                SqlConnection connection = null;

                try
                {
                    using (connection = new SqlConnection(connectionString))
                    {
                        using (MentalViewDataSetTableAdapters.PartnersTableAdapter partnersTA = new MentalViewDataSetTableAdapters.PartnersTableAdapter())
                        {
                            partnersTA.Connection = connection;
                            partnersTA.Update(ds.Partners);

                            ds.Partners.AcceptChanges();
                        }
                    }
                }
                catch (Exception exp)
                {
                    ds.Partners.RejectChanges();
                    throw exp;
                }
                finally
                {
                    if (connection != null)
                    {
                        connection.Close();
                    }
                }
            }

            //public static MentalViewDataSet GetPartnerById(Int64 partnerId)
            //{
            //    using (SqlConnection connection = new SqlConnection(connectionString))
            //    {
            //        MentalViewDataSet ds = new Data.MentalViewDataSet();
            //        ds.EnforceConstraints = false;
            //        Data.Business.ConfigureDataSet(ref ds);
            //        MentalViewDataSetTableAdapters.PartnersTableAdapter customersTA = new MentalViewDataSetTableAdapters.PartnersTableAdapter();
            //        customersTA.Connection = connection;
            //        customersTA.FillByPartnerId(ds.Partners, partnerId);

            //        MentalViewDataSetTableAdapters.AppointmentCategoriesTableAdapter appointmentCategoriesDT = new MentalViewDataSetTableAdapters.AppointmentCategoriesTableAdapter();
            //        appointmentCategoriesDT.Connection = connection;
            //        appointmentCategoriesDT.FillByTenantId(ds.AppointmentCategories, ds.Partners[0].TenantId);

            //        MentalViewDataSetTableAdapters.QuestionnairesTableAdapter questionnairesDT = new MentalViewDataSetTableAdapters.QuestionnairesTableAdapter();
            //        questionnairesDT.Connection = connection;
            //        questionnairesDT.FillByPartnerId(ds.Questionnaires, partnerId);

            //        MentalViewDataSetTableAdapters.QuestionnaireQuestionsTableAdapter questionnaireQuestionsTA = new MentalViewDataSetTableAdapters.QuestionnaireQuestionsTableAdapter();
            //        questionnaireQuestionsTA.Connection = connection;
            //        questionnaireQuestionsTA.FillByPartnerId(ds.QuestionnaireQuestions, partnerId);

            //        MentalViewDataSetTableAdapters.AppointmentsTableAdapter appointmensTA = new MentalViewDataSetTableAdapters.AppointmentsTableAdapter();
            //        appointmensTA.Connection = connection;
            //        appointmensTA.FillByPartnerId(ds.Appointments, partnerId);

            //        MentalViewDataSetTableAdapters.PartnerEmailTemplatesTableAdapter partnerEmailTemplatesTA = new MentalViewDataSetTableAdapters.PartnerEmailTemplatesTableAdapter();
            //        partnerEmailTemplatesTA.Connection = connection;
            //        partnerEmailTemplatesTA.FillByPartnerId(ds.PartnerEmailTemplates, partnerId);

            //        return ds;
            //    }
            //}

            public static MentalViewDataSet GetAllPartnersOfTenant(Int64 tenantId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    Data.Business.ConfigureDataSet(ref ds);
                    MentalViewDataSetTableAdapters.PartnersTableAdapter customersTA = new MentalViewDataSetTableAdapters.PartnersTableAdapter();
                    customersTA.Connection = connection;
                    customersTA.FillByTenantId(ds.Partners, tenantId);

                    return ds;
                }
            }

            public static void DeletePartner(Int64 partnerId)
            {
                string sqlQuery = "DELETE FROM Partners WHERE PartnerId=" + partnerId.ToString();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(sqlQuery, connection))
                    {
                        connection.Open();
                        cmd.CommandType = CommandType.Text;
                        cmd.ExecuteNonQuery();
                    }
                }
            }

            //public static MentalViewDataSet GetPartners(Int64 tenantId, Int64? userId, string filter, string sortExpression, Data.SortDirection? sortDirection, int pageIndex, int pageSize, out int totalCount)
            //{
            //    MentalViewDataSet ds = new MentalViewDataSet();
            //    ds.EnforceConstraints = false;
            //    totalCount = 0;

            //    using (SqlConnection connection = new SqlConnection(connectionString))
            //    {
            //        using (SqlCommand cmd = new SqlCommand("GetPartners", connection))
            //        {
            //            DataSet genericDS = new DataSet();

            //            cmd.CommandType = CommandType.StoredProcedure;
            //            cmd.Parameters.Add("@TenantId", SqlDbType.BigInt).Value = tenantId;
            //            cmd.Parameters.Add("@Filter", SqlDbType.NVarChar).Value = filter;
            //            cmd.Parameters.Add("@OrderBy", SqlDbType.NVarChar).Value = sortExpression;
            //            if (sortDirection.HasValue)
            //            {
            //                cmd.Parameters.Add("@SortType", SqlDbType.NVarChar).Value = sortDirection == SortDirection.Ascending ? "ASC" : "DESC";
            //            }
            //            cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
            //            cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
            //            if (userId.HasValue)
            //            {
            //                cmd.Parameters.Add("@UserId", SqlDbType.BigInt).Value = userId;
            //            }

            //            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            //            {
            //                da.Fill(genericDS);
            //            }

            //            ds.Partners.Merge(genericDS.Tables[0]);
            //            ds.AcceptChanges();
            //            totalCount = int.Parse(genericDS.Tables[1].Rows[0][0].ToString());
            //        }
            //    }

            //    return ds;
            //}

            //public static DataTable GetPartnersForExport(Int64 tenantId, string orderBy)
            //{
            //    DataTable dt = new DataTable();
            //    using (SqlConnection connection = new SqlConnection(connectionString))
            //    {
            //        string sqlText = "select PartnerCode As 'Κωδικός Πελάτη', LastName AS N'Επώνυμο', FirstName AS N'Όνομα', Case When (SELECT Active)=1 Then N'Ναι' ELse N'Όχι' END AS Ενεργός, Mobile1 AS N'Κινητό', Email, ISNULL((SELECT FullName From Users Where UserId=TherapistId), '') As Θεραπευτής, (SELECT COUNT(*) FROM Appointments WHERE Appointments.PartnerId = Partners.PartnerId) AS Συνεδρείες, " +
            //            //"--(SELECT MIN(StartTime) FROM Appointments WHERE Appointments.PartnerId = Partners.PartnerId) AS FirstAppointmentDate, " +
            //            //"--(SELECT MAX(StartTime) FROM Appointments WHERE Appointments.PartnerId = Partners.PartnerId) AS LastAppointmentDate, " +
            //            "ISNULL(DATEDIFF(month, (SELECT MIN(StartTime) FROM Appointments WHERE Appointments.PartnerId = Partners.PartnerId), (SELECT MAX(StartTime) FROM Appointments WHERE Appointments.PartnerId = Partners.PartnerId)), 0) AS 'Διάρκεια (μήνες)' " +
            //            "From Partners ";
            //        if (orderBy == "FirstLastName")
            //        {
            //            sqlText += " ORDER BY LastName, FirstName";
            //        }
            //        else if (orderBy == "MostAppointments")
            //        {
            //            sqlText += " ORDER BY (SELECT COUNT(*) FROM Appointments WHERE Appointments.PartnerId = Partners.PartnerId) DESC";
            //        }

            //        using (SqlCommand cmd = new SqlCommand(sqlText, connection))
            //        {
            //            cmd.CommandType = CommandType.Text;

            //            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            //            {
            //                da.Fill(dt);
            //            }
            //        }
            //    }

            //    return dt;
            //}



            //public static DataTable GetPartnersForPartnersReport(Int64 tenantId, string partnerType)
            //{
            //    DataTable dt = new DataTable();

            //    string whereClause = "";
            //    if (partnerType == "Active")
            //    {
            //        whereClause = " AND Active=1 ";
            //    }
            //    else if (partnerType == "Deactive")
            //    {
            //        whereClause = " AND Active = 0 ";
            //    }
            //    else if (partnerType == "Waiting")
            //    {
            //        whereClause = " AND Waiting = 1 ";
            //    }

            //    string query = @"SELECT PartnerId, Active, Waiting, FirstName, LastName, FatherName, Email, Phone1, Mobile1, 
            //                ISNULL((SELECT FullName From Users WHERE UserId = TherapistId), '') AS TherapistFullName,
            //                ISNULL((SELECT FullName From Users WHERE UserId = DiagnosticianId), '') AS DiagnosticianFullName,
            //                ISNULL((SELECT FullName From Users WHERE UserId = ClinicSupervisorId), '') AS ClinicSupervisorFullName
            //            FROM Partners
            //            WHERE TenantId = " + tenantId.ToString() + whereClause +
            //            @" ORDER BY LastName, FirstName";

            //    using (SqlConnection connection = new SqlConnection(connectionString))
            //    {
            //        using (SqlCommand cmd = new SqlCommand(query, connection))
            //        {
            //            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            //            {
            //                da.Fill(dt);
            //                dt.Columns.Add("FullName", typeof(string), "LastName+ISNULL(' '+FirstName,'')");
            //            }
            //        }
            //    }

            //    return dt;
            //}


            //public static void DeletePartner(Int64 partnerId)
            //{
            //    string sqlQuery = "DELETE FROM Partners WHERE PartnerId=" + partnerId.ToString();

            //    using (SqlConnection connection = new SqlConnection(connectionString))
            //    {
            //        using (SqlCommand cmd = new SqlCommand(sqlQuery, connection))
            //        {
            //            connection.Open();
            //            cmd.CommandType = CommandType.Text;
            //            cmd.ExecuteNonQuery();
            //        }
            //    }
            //}
        }

        public class LinksBusiness
        {
            /// <summary>
            /// Saves only data of Links.
            /// </summary>
            public static void SaveLinks(ref MentalViewDataSet ds)
            {
                SqlConnection connection = null;

                try
                {
                    using (connection = new SqlConnection(connectionString))
                    {
                        using (MentalViewDataSetTableAdapters.LinksTableAdapter linksTA = new MentalViewDataSetTableAdapters.LinksTableAdapter())
                        {
                            linksTA.Connection = connection;
                            linksTA.Update(ds.Links);

                            ds.Links.AcceptChanges();
                        }
                    }
                }
                catch (Exception exp)
                {
                    ds.Links.RejectChanges();
                    throw exp;
                }
                finally
                {
                    if (connection != null)
                    {
                        connection.Close();
                    }
                }
            }


            public static MentalViewDataSet GetAllLinksOfTenant(Int64 tenantId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    Data.Business.ConfigureDataSet(ref ds);
                    MentalViewDataSetTableAdapters.LinksTableAdapter customersTA = new MentalViewDataSetTableAdapters.LinksTableAdapter();
                    customersTA.Connection = connection;
                    customersTA.FillByTenantId(ds.Links, tenantId);

                    return ds;
                }
            }

            public static void DeleteLink(Int64 linkId)
            {
                string sqlQuery = "DELETE FROM Links WHERE LinkId=" + linkId.ToString();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(sqlQuery, connection))
                    {
                        connection.Open();
                        cmd.CommandType = CommandType.Text;
                        cmd.ExecuteNonQuery();
                    }
                }
            }


        }

    }
}
