/*!
*  filename: ej.mobile.menu.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min","./ej.mobile.scrollpanel.min"],n):n()})(function(){(function(n,t,r){t.widget("ejmMenu","ej.mobile.Menu",{_setFirst:!0,_rootCSS:"e-m-menu",_tags:[{tag:"items",attr:["text","touchStart","touchEnd","href","color"],content:"textTemplate"}],defaults:{renderMode:"auto",height:null,width:null,templateId:null,dataSource:[],fields:{text:"",href:null,color:""},query:t.Query(),allowScrolling:!0,enablePersistence:!1,text:"",color:"",target:null,showOn:"tap",cancelButton:{text:null,show:!0,color:""},showTitle:!0,title:null,showArrow:!0,type:"actionsheet",locale:"en-US",load:null,loadComplete:null,touchStart:null,touchEnd:null,hide:null,show:null,cssClass:"",enableRippleEffect:t.isAndroid()?!0:!1,items:[],href:null,actionSuccess:null,actionFailure:null,actionComplete:null},dataTypes:{dataSource:"data",query:"data",enableRippleEffect:"boolean",renderMode:"enum",locale:"string"},observableArray:["dataSource"],dataSource:t.util.valueFunction("dataSource"),_init:function(){t.getCurrentPage().append(this.element);this._getLocalizedLabels();this.model.title=t.isNullOrUndefined(this.model.title)?this._localizedLabels.title:this.model.title;this.model.cancelButton.text=t.isNullOrUndefined(this.model.cancelButton.text)?this._localizedLabels.cancelButtonText:this.model.cancelButton.text;this._template=t.getCurrentPage().find("#"+this.model.templateId);Object.keys(this.model.dataSource).length>1?this._initDataSource():this._render()},_getLocalizedLabels:function(){this._localizedLabels=t.getLocalizedConstants(this.sfType,this.model.locale)},_load:function(){t.setRenderMode(this);this._touchStart=this.model.touchStart;this._touchEnd=this.model.touchEnd;this._orgEle=this.element.clone();(this._orgEle.find("ul").length||Object.keys(this.model.dataSource).length>1)&&(this.model.items=[]);this.model.id=this.element[0].id;Object.keys(this.model.dataSource).length>1&&this._dataSourceList(this._dataSource);this.model.load&&this._trigger("load")},_renderItems:function(n){var f,o,e,u,s;if(n.model.items.length<1)for(e=this.element.find("li"),i=0;i<e.length;i++)f={},u=e[i],f.text=t.getAttrVal(u,"data-ej-text",""),f.href=t.getAttrVal(u,"data-ej-href",null),f.color=t.getAttrVal(u,"data-ej-color",""),f.templateId=t.getAttrVal(u,"data-ej-templateid",r),f.touchStart=t.getAttrVal(u,"data-ej-touchstart",r),f.touchEnd=t.getAttrVal(u,"data-ej-touchend",r),this.model.items.push(f);else for(o=t.buildTag("ul.e-m-menu-hdr e-m-clearall"),e=this.model.items,i=0;i<e.length;i++)u=e[i],u.text=u.text?u.text:"",u.href=u.href?u.href:null,u.color=u.color?u.color:"",u.templateId=u.templateId?u.templateId:r,u.touchStart=u.touchStart?u.touchStart:r,u.touchEnd=u.touchEnd?u.touchEnd:r,s=t.buildTag("li",e[i].textTemplate),this.element.append(o.append(s));this._setEnableRippleEffect()},_setEnableRippleEffect:function(){this.element.find("li")[this.model.enableRippleEffect?"addClass":"removeClass"]("e-ripple");this.element.find("span.e-m-menu-btn")[this.model.enableRippleEffect&&this.model.cancelButton.show?"addClass":"removeClass"]("e-ripple")},_render:function(){this._renderControl();this._wireEvents()},_initDataSource:function(){t.mobile.WaitingPopup.show();var n=this;this.dataSource()instanceof t.DataManager?this.dataSource().executeQuery(n.model.query).done(function(i){n._dataSource=i.result;n._render();n._trigger("actionSuccess",i);t.mobile.WaitingPopup.hide()}).fail(function(t){n._trigger("actionFailure",t)}).always(function(t){n._trigger("actionComplete",t)}):(this._dataSource=t.DataManager(this.dataSource()).executeLocal(this.model.query),this._render(),t.mobile.WaitingPopup.hide())},_renderControl:function(){this._load();this.element.addClass("e-m-"+this.model.renderMode+" "+this.model.cssClass);var n=this;this._renderItems(n);this._menuUl=this.element.find("ul").addClass("e-m-menulist");this.model.templateId&&Object.keys(this.model.dataSource).length<1?(t.getCurrentPage().find("#"+this.model.templateId).remove(),this.element.append(t.buildTag("div.e-m-menutemplate").append(this._template.html())),this._menuProperties()):(this._menuLiItems=this.element.find("li").addClass("e-m-state-default e-m-user-select e-m-menuitem"),this._menuLiItems.first().addClass("e-m-firstitem"),this._menuLiItems.last().addClass("e-m-lastitem"),this._menuProperties(),this._menuLiItems.each(function(t,i){n._renderMenuItems(i,t)}),this._liHeight=t.getDimension(this._menuLiItems,"height"));t.widget.init(this.element);this.hide();this._setHeightWidth();this.model.allowScrolling&&this._renderScrollPanel();this.model.loadComplete&&this._trigger("loadComplete")},_getTarget:function(){return typeof this.model.target=="string"?t.getCurrentPage().find("#"+this.model.target).length>0?t.getCurrentPage().find("#"+this.model.target):n("body").find("#"+this.model.target):this.model.target},_setHeightWidth:function(){if(this.model.width&&this.element.width(this.model.width),this._titleHeight=Math.abs(this._titleDiv?t.getDimension(this._titleDiv,"outerHeight"):0),this._menuHeight=this._liHeight*5+this._titleHeight+this._menuArrowHeight,this.model.height)this._menuUl.height(this.model.height-(this._titleHeight+this.element.find(".e-m-menu-cancelbtn").outerHeight()+(this.model.type=="actionsheet"?10:0)));else if(this.model.allowScrolling)if(this.model.type=="actionsheet")window.innerHeight<this._liHeight*5+(this._titleDiv?this._titleDiv.outerHeight():0)+this.element.find(".e-m-menu-cancelbtn").outerHeight()+25?this._menuUl.css("max-height",window.innerHeight-((this._titleDiv?this._titleDiv.outerHeight():0)+this.element.find(".e-m-menu-cancelbtn").outerHeight()+20)):this._menuUl.css("max-height",this._liHeight*5);else{var n=this._getTarget().offset().top,i=n+this._getTarget().outerHeight();this.element.css({top:"auto",bottom:"auto"});this._menuHeight+(this.element.offset().top+10)>window.innerHeight?this._menuUl.css("max-height",this._liHeight*5-(this._menuHeight-(window.innerHeight-this.element.offset().top))):!this.model.height&&this.model.allowScrolling&&t.isNullOrUndefined(this.model.templateId)&&window.innerHeight>=this._menuHeight&&this._menuUl.css("max-height",this._liHeight*5)}},_renderScrollPanel:function(){var n=t.buildTag("div.e-m-menuscrollwrapper");t.isNullOrUndefined(this.model.templateId)?this._menuUl.wrap(n):this.element.find(".e-m-menutemplate").wrap(n);this.element.find(".e-m-menuscrollwrapper").ejmScrollPanel({isRelative:!0})},_dataSourceList:function(i){var r,u;t.isNullOrUndefined(this.model.templateId)?(r=this,this._datasrcUl=t.buildTag("ul"),this.element.append(this._datasrcUl),n(i).each(function(n,i){var u=t.buildTag("li",i[r.model.fields.text],{color:i[r.model.fields.color]},{}).attr("data-ej-href",i[r.model.fields.href]).appendTo(r.element);r._datasrcUl.append(u)})):(u=t.getCurrentPage().find("#"+this.model.templateId),this._dataSource.length&&(this._temp=t.buildTag("div.e-m-menutemplate"),this.element.append(this._temp),this._temp.html(u.render(this._dataSource))))},_menuProperties:function(){this.element.addClass("e-m-menu-"+this.model.type+(this.model.type=="actionsheet"?" e-m-abs":" e-m-fixed"));this.model.type=="popover"&&this.element.addClass("e-m-corner-all").prepend(this.model.showArrow?t.buildTag("div.e-m-icon-arrow e-m-top"):"").append(this.model.showArrow?t.buildTag("div.e-m-icon-arrow e-m-bottom"):"");!this.model.showTitle||this.model.type=="popup"||(this._titleDiv=t.buildTag("span.e-m-menu-title",this.model.title),this.model.type=="actionsheet"?this.element.prepend(this._titleDiv):this.model.showArrow?this._titleDiv.insertAfter(this.element.find(".e-m-icon-arrow.e-m-top")):this.element.prepend(this._titleDiv));this._menuArrowHeight=this.element.find(".e-m-icon-arrow").height();this.model.cancelButton.show&&this.model.type=="actionsheet"&&this.element.append(t.buildTag("span.e-m-menu-btn").addClass("e-m-menu-cancelbtn").text(this.model.cancelButton.text).css("color",this.model.cancelButton.color));this._createOverlayDiv()},_renderMenuItems:function(i,u){if(!n(i).hasClass("e-m-menu-title")){var f=t.getAttrVal(i,"data-ej-text")==(""||r)?this.model.items[u].text==""?n(this._menuLiItems[u]).html():this.model.items[u].text:t.getAttrVal(i,"data-ej-text");ctrl=t.buildTag("a.e-m-menu-btn#"+this.model.id+u,f,{},{href:t.getAttrVal(i,"data-ej-href")||this.model.items[u].href}).css("color",t.getAttrVal(i,"data-ej-color")||this.model.items[u].color);n(i).empty().append(ctrl.css("color",i.style.color==""?this.model.items[u].color:i.style.color))}},_normalPosition:function(n){var r,t,u,i,f;n.length&&(r=this.element.width(),this.element.find(".e-m-icon-arrow.e-m-top").removeClass("e-m-state-hide").addClass("e-m-state-block"),this.element.find(".e-m-icon-arrow.e-m-bottom").removeClass("e-m-state-block").addClass("e-m-state-hide"),t=n.offset().left+n.outerWidth()/2-r/2,u=window.innerWidth,this._windowHeight=window.innerHeight,this._targetHeight=this.element.outerHeight(),t<0?t=5:u-t<r&&(t=u-r),this.model.type=="popover"?this.element.css({left:t+"px"}):this.element.css({left:u/2-this.element.width()/2+"px"}),this.element.find(".e-m-icon-arrow").css("left",n.offset().left+(n.outerWidth()-this.element.find(".e-m-icon-arrow").width())/2-t),i=n.offset().top,f=i+n.outerHeight(),this.element.css({top:f+10,bottom:"auto"}),this.model.showArrow||this.model.type!="popover"||this.element.css("top",this.element.offset().top-(n.height()+10)),window.innerHeight<this.element.offset().top&&this.element.css({top:"auto"}),this.model.type=="popover"&&(!this.model.height&&window.innerHeight-(i+10)<this._targetHeight&&this._menuUl.css("max-height",this._liHeight*5-((this._titleDiv?this._titleDiv.outerHeight():0)+this._menuArrowHeight+10)),i>this._targetHeight+10&&window.innerHeight-i<this._targetHeight&&(this.element.css({bottom:this._windowHeight-i+10,top:"auto"}),this.element.find(".e-m-icon-arrow.e-m-bottom").removeClass("e-m-state-hide").addClass("e-m-state-block"),this.element.find(".e-m-icon-arrow.e-m-top").removeClass("e-m-state-block").addClass("e-m-state-hide"))),this.model.showArrow||this.model.type!="popover"||this.element.css("bottom",this._windowHeight-i+10-(n.height()+10)),this.model.type=="popup"&&(this.element.css({top:(this._windowHeight-this._targetHeight)/2}),this.element.css({left:(this._windowWidth-this.element.outerWidth())/2})))},_createOverlayDiv:function(){this._overlay=t.buildTag("div#"+this.model.id+"_overlay","","",{"class":"e-m-overlay e-m-menu-overlay e-m-menu-"+this.model.type});this.element.after(this._overlay)},_cancelbtnTouchEnd:function(t){this.element.find(".e-m-menu-cancelbtn").removeClass("e-m-state-active");var i=t.currentTarget,r={item:n(i),text:n(i).text()};this._trigger("touchEnd",r);this.hide()},_cancelbtnTouchStart:function(t){this.element.find(".e-m-menu-cancelbtn").addClass("e-m-state-active");var i=t.currentTarget,r={item:n(i),text:n(i).text()};this._trigger("touchStart",r)},_docTouchStartHandler:function(t){this._currentTarget=this.model.target&&(t.currentTarget.id!=this.model.target||t.target.id!=this.model.target);!n(t.target).closest(".e-m-menu").length&&this._currentTarget&&(this._hide=!0,this.hide())},_docTouchEndHandler:function(t){this._hide&&!n(t.target).closest(".e-m-menu").length&&this._currentTarget&&(this._hide=!1)},_onTouchHandler:function(i,r){if(t.isNullOrUndefined(this.model.templateId)){this._currentItem[r=="touchStart"?"addClass":"removeClass"]("e-m-state-active");var u=n(i.currentTarget).index();this.model[r]=this.model.items[u][r]?this.model.items[u][r]:this["_"+r];this.model[r]&&this._trigger(r,{item:this._currentItem,text:this._currentItem.text()})}},_onTouchStartHandler:function(t){this._currentItem=n(t.currentTarget);this._onTouchHandler(t,"touchStart")},_onTouchEndHandler:function(n){this._onTouchHandler(n,"touchEnd")},_onTapEventHandler:function(i){this.model.showOn=="taphold"&&i.currentTarget==this._getTarget()[0]&&(t.listenEvents([n(document),n(document)],[t.startEvent(),t.endEvent],[this._docTouchStartDelegate,this._docTouchEndDelegate],!0),this.show(),t.listenEvents([n(document),n(document)],[t.startEvent(),t.endEvent],[this._docTouchStartDelegate,this._docTouchEndDelegate],!1))},_onTouchMoveHandler:function(t){this._currentItem=n(t.currentTarget);this._currentItem.removeClass("e-m-state-active")},_orientation:function(){var i=this._getTarget(),n=this;setTimeout(function(){n._setHeightWidth();n.model.type!="actionsheet"&&n._normalPosition(i)},t.isAndroid()?100:0)},_createDelegates:function(){this._showDelegate=n.proxy(this.show,this);this._cancelbtnTouchEndDelegate=n.proxy(this._cancelbtnTouchEnd,this);this._cancelbtnTouchStartDelegate=n.proxy(this._cancelbtnTouchStart,this);this._docTouchStartDelegate=n.proxy(this._docTouchStartHandler,this);this._docTouchEndDelegate=n.proxy(this._docTouchEndHandler,this);this._touchStartDelegate=n.proxy(this._onTouchStartHandler,this);this._touchEndDelegate=n.proxy(this._onTouchEndHandler,this);this._tapDelegate=n.proxy(this._onTapEventHandler,this);this._touchMoveDelegate=n.proxy(this._onTouchMoveHandler,this);this._resizeDelegate=n.proxy(this._orientation,this);this._orientationDelegate=n.proxy(this._orientation,this)},_wireEvents:function(i){var r=i?"unbind":"bind";i||this._createDelegates();this._li=this.element.find("li");t.listenEvents([this._li,this._li,this._li,window],[t.startEvent(),t.endEvent(),t.moveEvent(),"orientationchange"],[this._touchStartDelegate,this._touchEndDelegate,this._touchMoveDelegate,this._orientationDelegate],i);this._tapEvent=this.model.showOn=="taphold"?t.tapHoldEvent():t.tapEvent();this.model.target&&(this.model.showOn=="taphold"?t.listenTouchEvent(this._getTarget(),this._tapEvent,this._tapDelegate,i):t.listenEvents([this._getTarget(),n(document),n(document)],[this._tapEvent,t.startEvent(),t.endEvent()],[this._showDelegate,this._docTouchStartDelegate,this._docTouchEndDelegate],i));this.model.cancelButton.show&&t.listenEvents([this.element.find(".e-m-menu-cancelbtn"),this.element.find(".e-m-menu-cancelbtn")],[t.startEvent(),t.endEvent()],[this._cancelbtnTouchStartDelegate,this._cancelbtnTouchEndDelegate],i);t.isTouchDevice()||n(window)[r]("resize",this._resizeDelegate)},_refresh:function(){this._destroy();this.element.addClass("e-m-menu");this._render()},_clearElement:function(){this.element.removeAttr("class").removeAttr("style");this._overlay.remove();this.element.html(this._orgEle.html())},_destroy:function(){this._wireEvents(!0);this._clearElement()},_setCancelButton:function(){this.element.find(".e-m-menu-cancelbtn").css("color",this.model.cancelButton.color).text(this.model.cancelButton.text)},_setShowCancelButton:function(n){this.element.find(".e-m-menu-cancelbtn").css("display",n?"block":"none")},_setType:function(){this._refresh()},_setAllowScrolling:function(){this._refresh()},_setTitle:function(){this._titleDiv.text(this.model.title)},_setRendermode:function(){this.element.removeClass("e-m-ios7 e-m-android e-m-windows e-m-flat").addClass("e-m-"+this.model.renderMode)},_setLocale:function(){this._getLocalizedLabels();this.model.title=this._localizedLabels.title;this.model.cancelButton.text=this._localizedLabels.cancelButtonText;this._setTitle();this._setCancelButtonText()},_setModel:function(n){var r=!1,t,i;for(t in n)i="_set"+t.charAt(0).toUpperCase()+t.slice(1),this[i]?this[i](n[t]):r=!0;r&&this._refresh()},show:function(t){if(this.element.addClass("e-m-menushow"),this.model.type=="popover"||this.model.type=="popup"){var i=n(t.currentTarget);this.element.show().css({opacity:"1"});this._setHeightWidth();this._normalPosition(i)}this.model.type=="actionsheet"&&(this.element.css("visibility","visible"),this.element.css({bottom:0}));this._overlay.removeClass("e-m-overlay-hide").css("pointer-events","auto");this.model.show&&this._trigger("show")},hide:function(){(this.model.type=="actionsheet"?(this.element.css({bottom:-t.getDimension(this.element,"outerHeight")-this.model.height+"px"}),this.element.css("visibility","hidden")):this.element.css("display","none"),this._overlay.addClass("e-m-overlay-hide").css("pointer-events","none"),this.model.hide&&this.element.hasClass("e-m-menushow")&&this._trigger("hide"))||this.element.removeClass("e-m-menushow")},_enableDisable:function(n){this.element[n?"removeClass":"addClass"]("e-m-state-disabled")},_enableDisableItem:function(t,i){t&&n(this.element.find("li.e-m-menuitem")[t-1])[i?"removeClass":"addClass"]("e-m-state-disabled")},enableItem:function(n){this._enableDisableItem(n,!0)},disableItem:function(n){this._enableDisableItem(n,!1)},enable:function(){this._enableDisable(!0)},disable:function(){this._enableDisable(!1)},disableCancelButton:function(){this.element.find(".e-m-menu-cancelbtn").addClass("e-m-state-disabled")},enableCancelButton:function(){this.element.find(".e-m-menu-cancelbtn").removeClass("e-m-state-disabled")},addItem:function(t,i){n(this._orgEle.children()).length?i?t.insertBefore(n(this._orgEle.children().children()[i-1])):this._orgEle.children().append(t):this.model.items.push(t);this._refresh()},removeItem:function(t){n(this._orgEle.children()).length?t?n(this._orgEle.children().children()[t-1]).remove():n(this._orgEle.children().children()).last().remove():t?this.model.items.splice(t-1,1):this.model.items.pop();this._refresh()}});t.mobile.Menu.ShowOn={Tap:"tap",TapHold:"taphold"};t.mobile.Menu.Type={Popover:"popover",Popup:"popup",ActionSheet:"actionsheet"};t.mobile.Menu.Locale=t.mobile.Menu.Locale||{};t.mobile.Menu.Locale["default"]=t.mobile.Menu.Locale["en-US"]={title:"Title",cancelButtonText:"Cancel"}})(jQuery,Syncfusion)});
