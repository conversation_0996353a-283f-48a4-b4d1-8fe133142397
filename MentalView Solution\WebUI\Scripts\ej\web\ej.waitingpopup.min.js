/*!
*  filename: ej.waitingpopup.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function(n,t){t.widget("ejWaitingPopup","ej.WaitingPopup",{element:null,model:null,validTags:["div","span"],_setFirst:!1,_requiresID:!0,_rootCSS:"e-waitingpopup",defaults:{showOnInit:!1,target:null,appendTo:null,showImage:!0,htmlAttributes:{},cssClass:"",text:null,template:null,create:null,destroy:null},dataTypes:{showOnInit:"boolean",showImage:"boolean",cssClass:"string"},_isTargetVisible:function(){return this.element.css("display")!="none"},show:function(){this._isTargetVisible()&&(this._refreshPanel(),this.maindiv.css("display","block"),this.model.showOnInit=!0)},hide:function(){this.maindiv.css("display","none");this.model.showOnInit=!1},refresh:function(){this._isTargetVisible()&&this._refreshPanel()},_setText:function(n){n?this.popupText?this.popupText.html(n):(this._generateTextTag(n),this._setContentPosition()):this.popupText&&(this.popupText.remove(),this.popupText=null)},_showImage:function(n){n?(this.popupImage=t.buildTag("span.e-image"),this.popupText?this.popupImage.insertBefore(this.popupText):this.maindiv.append(this.popupImage)):this.popupImage&&(this.popupImage.remove(),this.popupImage=null)},_setTemplate:function(){var i=this.model.template;typeof i=="string"&&(i=n(i));this.templateObj=typeof i=="object"&&typeof i.css=="function"?i:t.buildTag("div","",{"text-align":"center"}).append(i);this.templateObj.css({visibility:"visible",display:"block"});this.maindiv.append(this.templateObj)},_setTheme:function(n){this.maindiv.removeClass(this.model.cssClass).addClass(n)},_init:function(){this._initialize();this._render();this._wireEvents()},_wireEvents:function(){n(window).on("resize",n.proxy(this._resizeHandler,this))},_unwireEvents:function(){n(window).off("resize",n.proxy(this._resizeHandler,this))},_resizeHandler:function(){this.refresh()},_setModel:function(i){for(var r in i)switch(r){case"text":this._setText(i[r]);break;case"cssClass":this._setTheme(i[r]);break;case"htmlAttributes ":this._addAttr(i[r]);break;case"showOnInit":this._setVisibility(i[r]);break;case"showImage":this._showImage(i[r]);this._setContentPosition();break;case"target":this.model.target=i[r];this._setTarget();this.refresh();break;case"appendTo":this.model.appendTo=i[r];this._setTarget();t.isNullOrUndefined(this.model.appendTo)||this.model.appendTo=="document"||this.model.appendTo=="window"?n("body").append(this.maindiv):this.maindiv.appendTo(n(this.model.appendTo));this.refresh();break;case"template":this.maindiv.empty();i[r]?(this.model.template=i[r],this._setTemplate()):(this.model.template=i[r]=null,this._showImage(this.model.showImage),this.model.text&&this._generateTextTag(this.model.text));this._setContentPosition()}},_setTarget:function(){this.targetElement=this.model.target=="document"?n(document):this.model.target=="window"?n(window):this.model.target?n(this.model.target):this.element},_destroy:function(){this.maindiv.remove();this._unwireEvents()},_initialize:function(){this.maindiv=null;this.popupText=null;this.popupImage=null;this.templateObj=null;this.targetElement=null},_render:function(){this._setTarget();var i=n("#"+this.element[0].id+"_WaitingPopup").get(0);i&&n(i).remove();this.maindiv=t.buildTag("div.e-waitpopup-pane e-widget "+this.model.cssClass+"#"+this.element[0].id+"_WaitingPopup");this.model.template?this._setTemplate():(this._showImage(this.model.showImage),this.model.text&&this._generateTextTag(this.model.text));t.isNullOrUndefined(this.model.appendTo)||this.model.appendTo=="document"||this.model.appendTo=="window"?n("body").append(this.maindiv):this.maindiv.appendTo(n(this.model.appendTo));this._setVisibility(this.model.showOnInit);this._addAttr(this.model.htmlAttributes)},_refreshPanel:function(){this.maindiv.width(this.targetElement.outerWidth());this.maindiv.height(this.targetElement.outerHeight());this._setPanelPosition();this._setContentPosition()},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.maindiv.addClass(n):i.maindiv.attr(t,n)})},_setPanelPosition:function(){var i=t.util.getOffset(this.targetElement);this.model.appendTo!=null&&n(this.model.appendTo).length>0&&((this.targetElement.css("position")=="relative"||this.targetElement.css("position")=="absolute")&&this.targetElement[0]===n(this.model.appendTo)[0]?i={left:0,top:0}:(i.left-=this.targetElement.offsetParent().offset().left,i.top-=this.targetElement.offsetParent().offset().top));this.maindiv.css({position:"absolute",left:Math.ceil(i.left)+"px",top:Math.ceil(i.top)+"px","z-index":this._maxZindex()+1})},_setContentPosition:function(){if(this.model.template==null){var t=0,r=0,i,n=null;i=this.targetElement.outerHeight();this.popupText&&(t=this.popupText.outerHeight());this.popupImage&&(r=this.popupImage.outerHeight());this.popupImage&&(n=Math.ceil((i-(r+t))/2),this.popupImage.css("top",n+"px"));this.popupText&&(n||(n=Math.ceil((i-t)/2)),this.popupText.css("top",n+"px"))}else this.templateObj.css({position:"relative",left:Math.ceil((this.targetElement.outerWidth()-this.templateObj.outerWidth())/2),top:Math.ceil((this.targetElement.outerHeight()-this.templateObj.outerHeight())/2)})},_generateTextTag:function(n){this.popupText=t.buildTag("div.e-text",n);this.maindiv.append(this.popupText)},_setVisibility:function(n){n&&this._isTargetVisible()?this.show():this.hide()},_maxZindex:function(){return t.util.getZindexPartial(this.element)}})})(jQuery,Syncfusion)});
