﻿<%@ Page Language="C#" MasterPageFile="~/Main.master" AutoEventWireup="true" CodeBehind="Reports.aspx.cs" Inherits="WebUI.Reports" meta:resourcekey="Page" %>

<%@ Register TagPrefix="ej" Namespace="Syncfusion.JavaScript.Web" Assembly="Syncfusion.EJ.Web" %>
<asp:Content ID="mainHeadContent" ContentPlaceHolderID="mainHead" runat="server">
    <script type="text/javascript">
        function Initialization() {
            SetLocalization();

        }
    </script>
</asp:Content>
<asp:Content ID="mainBodyContent" ContentPlaceHolderID="mainBody" runat="server">
    <div class="row">
        <div class="col-xs-12">
            <div class="panel-group">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseOne" class="collapsed">
                                <asp:Literal ID="contactsReportHeaderLbl" meta:resourceKey="contactsReportHeaderLbl" runat="server"></asp:Literal>
                                <i class="fa fa-angle-down pull-right"></i><i class="fa fa-angle-up pull-right"></i>
                            </a>
                        </h4>
                    </div>
                    <div id="collapseOne" class="panel-collapse collapse" style="height: 0;">
                        <div class="panel-body">
                            <div class="form-horizontal label-left">
                                <div class="form-group">
                                    <asp:Label runat="server" for="contactsTypeDDL" CssClass="col-sm-2 control-label" ID="contactsTypeLbl" meta:resourcekey="contactsTypeLbl"></asp:Label>
                                    <div class="col-sm-10">
                                        <ej:DropDownList ID="contactsTypeDDL" runat="server" Width="200">
                                            <Items>
                                                <ej:DropDownListItem Text="Όλοι" Value="All" Selected="True"></ej:DropDownListItem>
                                                <ej:DropDownListItem Text="Ενεργοί" Value="Active"></ej:DropDownListItem>
                                                <ej:DropDownListItem Text="Ανενεργοί" Value="Deactive"></ej:DropDownListItem>
                                                <ej:DropDownListItem Text="Σε Αναμονή" Value="Waiting"></ej:DropDownListItem>
                                            </Items>
                                        </ej:DropDownList>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <asp:Button ID="previewContactsReportBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, Preview %>" OnClick="previewContactsReportBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    <asp:Button ID="exportContactsToPdfBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToPdf %>" OnClick="exportContactsToPdfBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    <asp:Button ID="exportContactsToExcelBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToExcel %>" OnClick="exportContactsToExcelBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseTwo" class="collapsed">
                                <asp:Literal ID="therapistContactsReportHeaderLbl" meta:resourceKey="therapistContactsReportHeaderLbl" runat="server"></asp:Literal>
                                <i class="fa fa-angle-down pull-right"></i><i class="fa fa-angle-up pull-right"></i>
                            </a>
                        </h4>
                    </div>
                    <div id="collapseTwo" class="panel-collapse collapse" style="height: 0;">
                        <div class="panel-body">
                            <div class="form-horizontal label-left">
                                <div class="form-group">
                                    <asp:Label runat="server" for="therapistContactsTherapistIdDDL" CssClass="col-sm-2 control-label" ID="therapistContactsTherapistIdLbl" meta:resourcekey="therapistContactsTherapistIdLbl"></asp:Label>
                                    <div class="col-sm-10">
                                        <ej:DropDownList ID="therapistContactsTherapistIdDDL" runat="server" DataTextField="FullName" DataValueField="UserId" Width="200">
                                        </ej:DropDownList>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <asp:Button ID="previewTherapistContactsReportBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, Preview %>" OnClick="previewTherapistContactsReportBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    <asp:Button ID="exportTherapistContactsReportBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToPdf %>" OnClick="exportTherapistContactsReportBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    <asp:Button ID="exportTherapistContactsReportToExcelBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToExcel %>" OnClick="exportTherapistContactsReportToExcelBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseThree" class="collapsed">
                                <asp:Literal ID="appointmentsReportHeaderLbl" meta:resourceKey="appointmentsReportHeaderLbl" runat="server"></asp:Literal>
                                <i class="fa fa-angle-down pull-right"></i><i class="fa fa-angle-up pull-right"></i>
                            </a>
                        </h4>
                    </div>
                    <div id="collapseThree" class="panel-collapse collapse" style="height: 0;">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-horizontal">
                                        <div class="form-group">
                                            <asp:Label for="appointmentsStartDateTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="appointmentsStartDateLbl"></asp:Label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <ej:DatePicker ID="appointmentsStartDateTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:DatePicker>
                                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-horizontal">
                                        <div class="form-group">
                                            <asp:Label for="appointmentsEndDateTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="appointmentsEndDateLbl"></asp:Label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <ej:DatePicker ID="appointmentsEndDateTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:DatePicker>
                                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-horizontal label-left">
                                        <div class="form-group">
                                            <asp:Label runat="server" for="appointmentsTherapistIdDDL" CssClass="col-sm-2 control-label" ID="appointmentsTherapistIdLbl" meta:resourcekey="appointmentsTherapistIdLbl"></asp:Label>
                                            <div class="col-sm-10">
                                                <ej:DropDownList ID="appointmentsTherapistIdDDL" runat="server" DataTextField="FullName" DataValueField="UserId" Width="200" ShowCheckbox="true" MultiSelectMode="VisualMode">
                                                </ej:DropDownList>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <asp:Button ID="previewAppointmentsReportBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, Preview %>" OnClick="previewAppointmentsReportBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    <asp:Button ID="printAppointmentsReportBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToPdf %>" OnClick="printAppointmentsReportBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    <asp:Button ID="exportAppointmentsReportToExcelBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToExcel %>" OnClick="exportAppointmentsReportToExcelBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseTen" class="collapsed">
                                <asp:Literal ID="Literal2" meta:resourceKey="eventsReportHeaderLbl" runat="server"></asp:Literal>
                                <i class="fa fa-angle-down pull-right"></i><i class="fa fa-angle-up pull-right"></i>
                            </a>
                        </h4>
                    </div>
                    <div id="collapseTen" class="panel-collapse collapse" style="height: 0;">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-horizontal">
                                        <div class="form-group">
                                            <asp:Label for="eventsStartDateTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="eventsStartDateLbl"></asp:Label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <ej:DatePicker ID="eventsStartDateTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:DatePicker>
                                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-horizontal">
                                        <div class="form-group">
                                            <asp:Label for="eventsEndDateTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="eventsEndDateLbl"></asp:Label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <ej:DatePicker ID="eventsEndDateTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:DatePicker>
                                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-horizontal label-left">
                                        <div class="form-group">
                                            <asp:Label runat="server" for="eventsTherapistIdDDL" CssClass="col-sm-2 control-label" ID="Label4" meta:resourcekey="eventsTherapistIdLbl"></asp:Label>
                                            <div class="col-sm-10">
                                                <ej:DropDownList ID="eventsTherapistIdDDL" runat="server" DataTextField="FullName" DataValueField="UserId" Width="200" ShowCheckbox="true" MultiSelectMode="VisualMode">
                                                </ej:DropDownList>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <asp:Button ID="previewEventsReportBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, Preview %>" OnClick="previewEventsReportBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    <asp:Button ID="exportEventsReportBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToPdf %>" OnClick="printEventsReportBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    <asp:Button ID="exportEventsReportToExcelBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToExcel %>" OnClick="exportEventsReportToExcelBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseFour" class="collapsed">
                                <asp:Literal ID="appointmentsInDebtReportHeaderLbl" meta:resourceKey="appointmentsInDebtReportHeaderLbl" runat="server"></asp:Literal>
                                <i class="fa fa-angle-down pull-right"></i><i class="fa fa-angle-up pull-right"></i>
                            </a>
                        </h4>
                    </div>
                    <div id="collapseFour" class="panel-collapse collapse" style="height: 0;">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-horizontal">
                                        <div class="form-group">
                                            <asp:Label for="appointmentsInDebtStartDateTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="appointmentsInDebtStartDateLbl"></asp:Label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <ej:DatePicker ID="appointmentsInDebtStartDateTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:DatePicker>
                                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-horizontal">
                                        <div class="form-group">
                                            <asp:Label for="appointmentsInDebtEndDateTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="appointmentsInDebtEndDateLbl"></asp:Label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <ej:DatePicker ID="appointmentsInDebtEndDateTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:DatePicker>
                                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <%--<div class="row">
                                <div class="col-sm-12">
                                    <div class="form-horizontal label-left">
                                        <div class="form-group">
                                            <asp:Label runat="server" for="appointmentsTherapistIdDDL" CssClass="col-sm-2 control-label" ID="Label1" meta:resourcekey="appointmentsTherapistIdLbl"></asp:Label>
                                            <div class="col-sm-10">
                                                <ej:DropDownList ID="DropDownList1" runat="server" DataTextField="FullName" DataValueField="UserId" Width="200" ShowCheckbox="true" MultiSelectMode="VisualMode">
                                                </ej:DropDownList>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>--%>
                            <div class="row">
                                <div class="col-sm-12">
                                    <asp:Button ID="previewAppointmentsInDebtReportBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, Preview %>" OnClick="previewAppointmentsInDebtReportBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    <asp:Button ID="printAppointmentsInDebtReportBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToPdf %>" OnClick="printAppointmentsInDebtReportBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    <asp:Button ID="exportAppointmentsInDebtReportToExcelBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToExcel %>" OnClick="exportAppointmentsInDebtReportToExcelBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseFive" class="collapsed">
                                <asp:Literal ID="canceledAppointmentsReportHeaderLbl" meta:resourceKey="canceledAppointmentsReportHeaderLbl" runat="server"></asp:Literal>
                                <i class="fa fa-angle-down pull-right"></i><i class="fa fa-angle-up pull-right"></i>
                            </a>
                        </h4>
                    </div>
                    <div id="collapseFive" class="panel-collapse collapse" style="height: 0;">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-horizontal">
                                        <div class="form-group">
                                            <asp:Label for="canceledAppointmentsStartDateTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="canceledAppointmentsStartDateLbl"></asp:Label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <ej:DatePicker ID="canceledAppointmentsStartDateTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:DatePicker>
                                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-horizontal">
                                        <div class="form-group">
                                            <asp:Label for="canceledAppointmentsEndDateTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="canceledAppointmentsEndDateLbl"></asp:Label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <ej:DatePicker ID="canceledAppointmentsEndDateTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:DatePicker>
                                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <%--<div class="row">
                                <div class="col-sm-12">
                                    <div class="form-horizontal label-left">
                                        <div class="form-group">
                                            <asp:Label runat="server" for="appointmentsTherapistIdDDL" CssClass="col-sm-2 control-label" ID="Label1" meta:resourcekey="appointmentsTherapistIdLbl"></asp:Label>
                                            <div class="col-sm-10">
                                                <ej:DropDownList ID="DropDownList1" runat="server" DataTextField="FullName" DataValueField="UserId" Width="200" ShowCheckbox="true" MultiSelectMode="VisualMode">
                                                </ej:DropDownList>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>--%>
                            <div class="row">
                                <div class="col-sm-12">
                                    <asp:Button ID="previewCanceledAppointmentsReportBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, Preview %>" OnClick="previewCanceledAppointmentsReportBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    <asp:Button ID="printCanceledAppointmentsReportBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToPdf %>" OnClick="printCanceledAppointmentsReportBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    <asp:Button ID="exportCanceledAppointmentsReportToExcelBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToExcel %>" OnClick="exportCanceledAppointmentsReportToExcelBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseSix" class="collapsed">
                                <asp:Literal ID="supervisionEventsReportHeaderLbl" meta:resourceKey="supervisionEventsReportHeaderLbl" runat="server"></asp:Literal>
                                <i class="fa fa-angle-down pull-right"></i><i class="fa fa-angle-up pull-right"></i>
                            </a>
                        </h4>
                    </div>
                    <div id="collapseSix" class="panel-collapse collapse" style="height: 0;">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-horizontal">
                                        <div class="form-group">
                                            <asp:Label for="supervisionEventsStartDateTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="supervisionEventsStartDateLbl"></asp:Label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <ej:DatePicker ID="supervisionEventsStartDateTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:DatePicker>
                                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-horizontal">
                                        <div class="form-group">
                                            <asp:Label for="supervisionEventsEndDateTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="supervisionEventsEndDateLbl"></asp:Label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <ej:DatePicker ID="supervisionEventsEndDateTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:DatePicker>
                                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <%--<div class="row">
                                <div class="col-sm-12">
                                    <div class="form-horizontal label-left">
                                        <div class="form-group">
                                            <asp:Label runat="server" for="appointmentsTherapistIdDDL" CssClass="col-sm-2 control-label" ID="Label1" meta:resourcekey="appointmentsTherapistIdLbl"></asp:Label>
                                            <div class="col-sm-10">
                                                <ej:DropDownList ID="DropDownList1" runat="server" DataTextField="FullName" DataValueField="UserId" Width="200" ShowCheckbox="true" MultiSelectMode="VisualMode">
                                                </ej:DropDownList>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>--%>
                            <div class="row">
                                <div class="col-sm-12">
                                    <asp:Button ID="previewSupervisionEventsReportBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, Preview %>" OnClick="previewSupervisionEventsReportBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    <asp:Button ID="printSupervisionEventsReportBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToPdf %>" OnClick="printSupervisionEventsReportBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    <asp:Button ID="exportSupervisionEventsReportToExcelBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToExcel %>" OnClick="exportSupervisionEventsReportToExcelBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseSeven" class="collapsed">
                                <asp:Literal ID="Literal1" meta:resourceKey="exportsContactsToExcelHeaderLbl" runat="server"></asp:Literal>
                                <i class="fa fa-angle-down pull-right"></i><i class="fa fa-angle-up pull-right"></i>
                            </a>
                        </h4>
                    </div>
                    <div id="collapseSeven" class="panel-collapse collapse" style="height: 0;">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-horizontal label-left">
                                        <div class="form-group">
                                            <asp:Label runat="server" for="contactsExportOrderByDDL" CssClass="col-sm-2 control-label" ID="Label1" meta:resourcekey="contactsExportOrderByLbl"></asp:Label>
                                            <div class="col-sm-10">
                                                <ej:DropDownList ID="contactsExportOrderByDDL" runat="server" Width="200">
                                                    <Items>
                                                        <ej:DropDownListItem ID="contactsExportOrderByFirstLastName" runat="server" Text="Επώνυμο, Όνομα" Value="FirstLastName"></ej:DropDownListItem>
                                                        <ej:DropDownListItem ID="contactsExportOrderByMostAppointments" runat="server" Text="Συνεδρείες" Value="MostAppointments"></ej:DropDownListItem>
                                                    </Items>
                                                </ej:DropDownList>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <asp:Button ID="exportsContactsToExcelBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToExcel %>" OnClick="exportsContactsToExcelBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseEight" class="collapsed">
                                <asp:Literal ID="replaceContactsInExcelHeaderLbl" meta:resourceKey="replaceContactsInExcelHeaderLbl" runat="server"></asp:Literal>
                                <i class="fa fa-angle-down pull-right"></i><i class="fa fa-angle-up pull-right"></i>
                            </a>
                        </h4>
                    </div>
                    <div id="collapseEight" class="panel-collapse collapse" style="height: 0;">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-horizontal label-left">
                                        <div class="form-group">
                                            <asp:Label runat="server" for="replaceContactsInExcelColumnTxtBox" CssClass="col-sm-2 control-label" ID="Label2" meta:resourcekey="replaceContactsInExcelColumnLbl"></asp:Label>
                                            <div class="col-sm-10">
                                                <asp:TextBox runat="server" ID="replaceContactsInExcelColumnTxtBox" MaxLength="1" required="required"></asp:TextBox>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div style="display: inline-block;">
                                        <asp:FileUpload ID="replaceContactsInExcelFileUpload" runat="server" AllowMultiple="false" accept=".xls,.xlsx" CssClass="" Style="float: left;" />
                                        <asp:Button ID="replaceContactsInExcelBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" meta:resourcekey="replaceContactsInExcelBtn" OnClick="replaceContactsInExcelBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseNine" class="collapsed">
                                <asp:Literal ID="incomeClearanceHeaderLbl" meta:resourceKey="incomeClearanceHeaderLbl" runat="server"></asp:Literal>
                                <i class="fa fa-angle-down pull-right"></i><i class="fa fa-angle-up pull-right"></i>
                            </a>
                        </h4>
                    </div>
                    <div id="collapseNine" class="panel-collapse collapse" style="height: 0;">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-horizontal">
                                        <div class="form-group">
                                            <asp:Label for="incomeClearanceStartDateTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="eventsStartDatebl"></asp:Label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <ej:DatePicker ID="incomeClearanceStartDateTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:DatePicker>
                                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-horizontal">
                                        <div class="form-group">
                                            <asp:Label for="incomeClearanceEndDateTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="incomeClearanceEndDateLbl"></asp:Label>
                                            <div class="col-sm-8">
                                                <div class="input-group">
                                                    <ej:DatePicker ID="incomeClearanceEndDateTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:DatePicker>
                                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-horizontal label-left">
                                        <div class="form-group">
                                            <asp:Label runat="server" for="incomeClearanceTherapistIdDDL" CssClass="col-sm-2 control-label" ID="Label3" meta:resourcekey="incomeClearanceTherapistIdLbl"></asp:Label>
                                            <div class="col-sm-10">
                                                <div class="input-group">
                                                    <ej:DropDownList ID="incomeClearanceTherapistIdDDL" runat="server" DataTextField="FullName" DataValueField="UserId" Width="500px" ShowCheckbox="true" MultiSelectMode="VisualMode">
                                                    </ej:DropDownList>
                                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div style="display: inline-block;">
                                        <asp:Button ID="previewIncomeClearanceBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, Preview %>" OnClick="previewIncomeClearanceBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                        <asp:Button ID="printIncomeClearanceBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToPdf %>" OnClick="printIncomeClearanceBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                        <asp:Button ID="exportIncomeClearanceToExcelBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat" Text="<%$ Resources:GlobalResources, ExportToExcel %>" OnClick="exportIncomeClearanceToExcelBtn_Click" CausesValidation="False" ValidateRequestMode="Disabled" UseSubmitBehavior="False" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
