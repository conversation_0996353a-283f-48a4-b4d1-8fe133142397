﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="IncludeFiles.master.cs" Inherits="WebUI.IncludeFiles" %>

<%@ Import Namespace="System.Web.Optimization" %>

<!DOCTYPE html>

<html>
<head runat="server">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <asp:PlaceHolder runat="server">
        <%:Scripts.Render("~/bundles/modernizr") %>
    </asp:PlaceHolder>
    <webopt:BundleReference runat="server" Path="~/Content/css" />

    <link href="~/favicon.ico" rel="shortcut icon" type="image/x-icon" />

    <link href="Content/AdminLTE/AdminLTE.min.css" rel="stylesheet" />
    <link href="Content/AdminLTE/skins/skin-blue-light.min.css" rel="stylesheet" />
    <link href="Content/AdminLTE/skins/adminlte.min.css.map" />
    <link href="Content/font-awesome-template/css/font-awesome.min.css" rel="stylesheet" />
    <link href="Content/font-awesome/css/all.min.css" rel="stylesheet" />
    <link href="Content/select2.min.css" rel="stylesheet" />

    <%:Styles.Render("https://cdn.syncfusion.com/*********/js/web/ej.widgets.core.min.css") %>
    <%:Styles.Render("https://cdn.syncfusion.com/*********/js/web/flat-azure/ej.theme.min.css") %>


    <%:Scripts.Render("~/Scripts/jquery-3.6.0.min.js")%>    <%--<%:Scripts.Render("~/Scripts/jquery-3.2.1.min.js")%>--%>
    <%:Scripts.Render("~/Scripts/jquery.cookie.min.js")%>
    <%:Scripts.Render("~/Scripts/bootstrap.min.js")%>
    <%:Scripts.Render("~/Scripts/bootstrap-datepicker.js")%>
    <%:Scripts.Render("~/Scripts/bootstrap-datepicker.el.js")%>
    <%:Scripts.Render("~/Scripts/bootstrap.touchspin.js")%>
    <%:Scripts.Render("~/Scripts/bootstrap-switch.min.js")%>
    <%-- <%:Scripts.Render("~/Scripts/jsrender.min.js")%>--%>
    <%--  <%:Scripts.Render("~/Scripts/ej/ej.web.all.min.js")%>
    <%:Scripts.Render("~/Scripts/ej/ej.webform.min.js")%>--%>
    <%:Scripts.Render("~/Scripts/AdminLTE/adminlte.min.js")%>
    <%:Scripts.Render("~/Scripts/jquery.validate.min.js")%>
    <%:Scripts.Render("~/Scripts/custom.js")%>
    <%:Scripts.Render("https://cdn.syncfusion.com/js/assets/external/jsrender.min.js") %>
    <%:Scripts.Render("https://cdn.syncfusion.com/*********/js/web/ej.web.all.min.js") %>
    <%:Scripts.Render("https://cdn.syncfusion.com/*********/js/common/ej.webform.min.js") %>
    <%:Scripts.Render("~/Scripts/ej/l10n/ej.localetexts.el-GR.min.js")%> <%-- Για ελληνική μετάφραση των Syncfusion controls--%>
    <%:Scripts.Render("~/LocalizedResources/ej.culture.el-GR.min.js")%>
    <%:Scripts.Render("~/Scripts/moment.min.js")%>
    <%:Scripts.Render("~/Scripts/select2.min.js")%>
    <%:Scripts.Render("~/Scripts/speech-to-text.js")%>

    <%:Styles.Render("~/Content/bootstrap.min.css") %>
    <%:Styles.Render("~/Content/bootstrap-switch.min.css") %>
    <%:Styles.Render("~/Content/ej/web/ej.widgets.core.min.css") %>
    <%--<%:Styles.Render("~/Content/ej/web/default-theme/ej.theme.min.css") %>--%>
    <%:Styles.Render("~/Content/Site.css") %>

    <!-- include summernote css/js -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.js"></script>


    <asp:ContentPlaceHolder ID="includeFilesHead" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body>
    <form runat="server">
        <asp:ScriptManager ID="scriptManager" runat="server" EnablePartialRendering="true"></asp:ScriptManager>

        <div>
            <asp:ContentPlaceHolder ID="includeFilesBody" runat="server">
            </asp:ContentPlaceHolder>
        </div>
    </form>
</body>
</html>
