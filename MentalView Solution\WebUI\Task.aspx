﻿<%@ Page Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Task.aspx.cs" Inherits="WebUI.Task" meta:resourcekey="Page" %>

<%@ Register Assembly="Syncfusion.EJ.Web" Namespace="Syncfusion.JavaScript.Web" TagPrefix="ej" %>
<%@ Register Assembly="Syncfusion.EJ" Namespace="Syncfusion.JavaScript.Models" TagPrefix="ej" %>

<%@ Register TagPrefix="cc1" Namespace="System" Assembly="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" %>
<asp:Content ID="mainHeadContent" ContentPlaceHolderID="mainHead" runat="server">
    <script type="text/javascript">
        var initialized = false;

        $(document).ready(function () {
            $('select').select2({ width: '100%' });
            $('select.no-search-select').select2({ width: '100%', minimumResultsForSearch: -1 });

            try {
                if (document.getElementById("recurrenceEditorWrapper") != null) {
                    var recObj = $("#recurrenceEditor").ejRecurrenceEditor('instance');
                    recObj._recRule = $("#recurrenceValueHiddenField").val();
                    recObj.recurrenceRuleSplit(recObj._recRule, ''); //splitting the recurrence rule. Κανανονικά στη 2η παράμετρο πρέπει να μπει το πεδίο appointment.recurrenceExDate που είναι οι ημερομηνίες που εξαιρούνται από το recurrence.
                    //Αν το AppointmentId είναι >0 (δηλαδή το Appointment δεν είναι καινούριο) αλλιώς βγάζει σφάλμα.
                    if ($("#appointmentIdHiddenField").val() > 0) {
                        if ($("#recurrenceValueHiddenField").val().length > 0) {
                            recObj.showRecurrenceSummary($("#appointmentIdHiddenField").val()); // updating the recurrence rule in Recurrence editor
                        }
                    }

                    initialized = true;

                    updateRecurrenceEditorVisibility();
                }

                updateTaskSupervisionAreaVisibility();
            }
            catch (exp) {
                alert('exception:' + exp);
                throw exp;
            }
        });

        function Initialization() {
            SetLocalization();

            //Σετάρει τα Currency TextBoxes
            $(document).on('keydown', 'input[pattern]', function (e) {
                var input = $(this);
                var oldVal = input.val();
                var regex = new RegExp(input.attr('pattern'), 'g');

                setTimeout(function () {
                    var newVal = input.val();
                    if (!regex.test(newVal)) {
                        input.val(oldVal);
                    }
                }, 0);
            });

            $("input[pattern]").change(function () {
                this.value = parseFloat(this.value).toFixed(2);
                if (this.value == 'NaN') {
                    this.value = 0;
                }
            });

            $("#recurrenceChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#visibleToAllChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#blockOtherChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#taskSupervisionChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
        }

        function onRecurrenceEditorChange(args) {
            if (initialized) {
                var obj = $("#recurrenceEditor").data("ejRecurrenceEditor");
                $("#recurrenceValueHiddenField").val(obj.getRecurrenceRule());
                console.log('onRecurrenceEditorChange' + obj.getRecurrenceRule());
            }
        }

        function recurrenceCheckClicked() {
            updateRecurrenceEditorVisibility();
        }

        function updateRecurrenceEditorVisibility() {
            var checked = $('#recurrenceChkBox').is(':checked');
            if (checked == true) {
                $("#recurrenceEditorWrapper").css("display", "");
            }
            else {
                $("#recurrenceEditorWrapper").css("display", "none");
            }
        }

        //Εκτελείται όταν ο χρήστης πατήσει Save πριν το server-side event.
        function onClientSaveClose() {
            if (initialized) {
                //Διαβάζει το recurrenceRule που έφτιαξε ο χρήστης, για να αποθηκευτεί στο server side.
                var obj = $("#recurrenceEditor").data("ejRecurrenceEditor");
                obj.closeRecurPublic();
                $("#recurrenceValueHiddenField").val(obj.getRecurrenceRule());
                console.log('clientSaveClose' + obj.getRecurrenceRule());
            }
        }

        function taskSupervisionCheckClicked() {
            updateTaskSupervisionAreaVisibility();
        }

        function updateTaskSupervisionAreaVisibility() {
            var checked = $('#taskSupervisionChkBox').is(':checked');
            if (checked == true) {
                $("#taskSupervisionArea").css("display", "");

                //Απενεργοποιεί το recurrence
                $("#recurrenceChkBox").bootstrapSwitch('state', false);
                updateRecurrenceEditorVisibility();
                $("#recurrenceChkBox").bootstrapSwitch('disabled', true);

                //Απενεργοποιεί το VisibleToAll
                $("#visibleToAllChkBox").bootstrapSwitch('state', false);
                $("#visibleToAllChkBox").bootstrapSwitch('disabled', true);
            }
            else {
                $("#taskSupervisionArea").css("display", "none");

                //Ενεργοποιεί το recurrence
                $("#recurrenceChkBox").bootstrapSwitch('disabled', false);

                //Ενεργοποιεί το VisibleToAll
                $("#visibleToAllChkBox").bootstrapSwitch('disabled', false);
            }
        }
    </script>


</asp:Content>
<asp:Content ID="mainBodyContent" ContentPlaceHolderID="mainBody" runat="server">
    <div class="row margin-bottom">
        <div class="col-xs-8">
            <div class="btn-group margin-r-5">
                <asp:LinkButton ID="saveCloseBtn" runat="server" CssClass="btn btn-primary btn-flat" OnClientClick="onClientSaveClose()" OnClick="saveCloseBtn_Click"><span class="hidden-md hidden-lg"><i class="fa fa-save "></i></span><asp:Label Text="<%$ Resources:GlobalResources, SaveClose %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
                <button runat="server" id="saveToggleBtn" type="button" class="btn btn-primary btn-flat dropdown-toggle" data-toggle="dropdown">
                    <span class="caret"></span>
                    <span class="sr-only">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu" role="menu">
                    <li>
                        <asp:LinkButton ID="saveBtn" runat="server" Text="<%$ Resources:GlobalResources, Save %>" OnClientClick="onClientSaveClose()" OnClick="saveBtn_Click"></asp:LinkButton>
                    </li>
                </ul>
            </div>
            <asp:LinkButton ID="deleteBtn" runat="server" DisableValidation="True" CssClass="btn btn-danger btn-flat margin-bottom margin-r-5" OnClick="deleteBtn_Click" CausesValidation="False"><span class="hidden-md hidden-lg"><i class="fa fa-trash"></i></span><asp:Label Text="<%$ Resources:GlobalResources, Delete %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
            <asp:LinkButton ID="closeBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat margin-bottom margin-r-5" OnClick="closeBtn_Click" CausesValidation="False" PostBackUrl="~/Appointments.aspx"><span class="hidden-md hidden-lg"><i class="fa fa-remove"></i></span><asp:Label Text="<%$ Resources:GlobalResources, Close %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
        </div>
        <div class="col-cs-4">
            <div class="btn-group margin-bottom margin-r-5 pull-right">
            </div>
            <div class="btn-group margin-bottom margin-r-5 pull-right">
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="startTimeTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="startTimeLbl"></asp:Label>
                            <div class="col-sm-8">
                                <div class="input-group">
                                    <ej:DateTimePicker ID="startDateTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:DateTimePicker>
                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="endTimeTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="endTimeLbl"></asp:Label>
                            <div class="col-sm-8">
                                <div class="input-group">
                                    <ej:DateTimePicker ID="endDateTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:DateTimePicker>
                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="therapistIdCmbBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="therapistIdLbl"></asp:Label>
                            <div class="col-sm-10">
                                <div class="input-group">
                                    <ej:DropDownList ID="therapistIdCmbBox" DataTextField="FullName" DataValueField="UserId" runat="server" WatermarkText="" Width="100%">
                                    </ej:DropDownList>
                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="subjectTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="subjectLbl"></asp:Label>
                            <div class="col-sm-10">
                                <asp:TextBox runat="server" ID="subjectTxtBox" class="form-control" MaxLength="50"></asp:TextBox>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="visibleToAllChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="visibleToAllLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="visibleToAllChkBox" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="blockOtherChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="blockOtherLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="blockOtherChkBox" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="notesTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="notesLbl"></asp:Label>
                            <div class="col-sm-10">
                                <asp:TextBox runat="server" ID="notesTxtBox" class="form-control" TextMode="MultiLine" Rows="3" MaxLength="1000" Style="resize: none;"></asp:TextBox>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-xs-2">
                </div>
                <div class="col-sm-10">
                    <div class="box box-default">
                        <div class="box-header">
                            <h3 class="box-title">Εποπτεία</h3>
                        </div>
                        <div class="box-body">
                            <div class="mb-3">
                                <input runat="server" type="checkbox" id="taskSupervisionChkBox" class="inl" onchange="taskSupervisionCheckClicked()" />
                            </div>
                            <div runat="server" id="taskSupervisionArea">
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="form-horizontal">
                                            <div class="form-group">
                                                <asp:Label for="taskSupervisionTherapistIDsDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="taskSupervisionTherapistIDsLbl"></asp:Label>
                                                <div class="col-sm-10">
                                                    <%--<ej:DropDownList ID="taskSupervisionTherapistIDsDDL" runat="server" DataTextField="FullName" DataValueField="UserId" EnableSorting="true" ShowCheckbox="true" MultiSelectMode="VisualMode" Width="auto" Height="30px"></ej:DropDownList>--%>
                                                    <select runat="server" id="taskSupervisionTherapistIDsDDL" datavaluefield="UserId" datatextfield="FullName" multiple="true" class="form-control no-search-select"></select>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="form-horizontal">
                                            <div class="form-group">
                                                <asp:Label for="taskSupervisionCustomersTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="taskSupervisionCustomersLbl"></asp:Label>
                                                <div class="col-sm-10">
                                                    <asp:TextBox runat="server" ID="taskSupervisionCustomersTxtBox" class="form-control" TextMode="MultiLine" Rows="3" MaxLength="100" Style="resize: none;"></asp:TextBox>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="form-horizontal">
                                            <div class="form-group">
                                                <asp:Label for="taskSupervisionSubjectTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="taskSupervisionSubjectLbl"></asp:Label>
                                                <div class="col-sm-10">
                                                    <asp:TextBox runat="server" ID="taskSupervisionSubjectTxtBox" class="form-control" TextMode="MultiLine" Rows="3" MaxLength="1000" Style="resize: none;"></asp:TextBox>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-12">
                                        <div class="form-horizontal">
                                            <div class="form-group">
                                                <asp:Label for="taskSupervisionRepliesTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="taskSupervisionRepliesLbl"></asp:Label>
                                                <div class="col-sm-10">
                                                    <asp:TextBox runat="server" ID="taskSupervisionRepliesTxtBox" class="form-control" TextMode="MultiLine" Rows="3" MaxLength="1000" Style="resize: none;"></asp:TextBox>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>




            <%-- <div runat="server" id="recurrenceInfoPanel" class="row">
                <div class="col-xs-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="recurrenceInfoLbl" runat="server" class="col-sm-2 control-label"></asp:Label>
                            <div class="col-sm-10">
                                <div class="callout callout-info">
                                    <p></p>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>--%>
            <div runat="server" id="recurrenceEditorPanel" class="row">
                <div class="col-xs-12">
                    <div class="row">
                        <div class="col-xs-2">
                        </div>
                        <div class="col-sm-10">
                            <div class="box box-default">
                                <div class="box-header">
                                    <h3 class="box-title">Επανάληψη</h3>
                                </div>
                                <div class="box-body">
                                    <input type="checkbox" id="recurrenceChkBox" onchange="recurrenceCheckClicked()" class="inl" runat="server" />
                                    <p>
                                        <asp:Label runat="server" ID="recurrenceInfoLbl" class="text-light-blue"></asp:Label>
                                    </p>

                                    <div runat="server" id="recurrenceEditorWrapper" style="display: none;">
                                        <br />
                                        <br />
                                        <div class="callout callout-warning">
                                            <%--<h4>I am a warning callout!</h4>--%>
                                            <i class="icon fa fa-warning margin-r-5" style="float: left;"></i>
                                            <p>
                                                <asp:Label runat="server" class="control-label" meta:resourcekey="recurrentAppointmentWarningMessageLbl"></asp:Label>
                                            </p>
                                        </div>
                                        <ej:RecurrenceEditor ID="recurrenceEditor" Locale="el-GR" runat="server" EnableViewState="true" Frequencies="daily,weekly,monthly,everyweekday" Change="onRecurrenceEditorChange">
                                        </ej:RecurrenceEditor>
                                    </div>
                                    <asp:HiddenField ID="recurrenceValueHiddenField" runat="server" />
                                    <asp:HiddenField ID="appointmentIdHiddenField" runat="server" />
                                    <%--<asp:HiddenField ID="originalStartTimeHiddenField" runat="server" />
                                    <asp:HiddenField ID="originalEndTimeHiddenField" runat="server" />--%>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <ej:Dialog ID="cancelAppointmentDialog" meta:resourceKey="cancelAppointmentDialog" runat="server" ShowOnInit="false" IsResponsive="true" EnableResize="false" EnableModal="true">
        <DialogContent>
            <div class="row margin-bottom">
                <div class="col-sm-12">
                    <asp:Label runat="server" class="control-label" meta:resourcekey="cancelAppointmentInfoLbl"></asp:Label>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <%--<asp:Label for="noChargeableCanceledAppointmentChkBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="noChargeableCanceledAppointmentChkBox"></asp:Label>--%>
                    <asp:RadioButton GroupName="ChargableCanceledAppointmentOptions" ValidationGroup="CancelAppointmentGroup" ValidateRequestMode="Enabled" TextAlign="Left" ID="noChargeableCanceledAppointmentRad" runat="server" meta:resourcekey="noChargeableCanceledAppointmentChkBox" />
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <asp:RadioButton GroupName="ChargableCanceledAppointmentOptions" ValidationGroup="CancelAppointmentGroup" ValidateRequestMode="Enabled" TextAlign="Left" ID="chargeableCanceledAppointmentRad" runat="server" meta:resourcekey="chargeableCanceledAppointmentChkBox" />
                </div>
            </div>
            <div runat="server" id="cancelUpcomingAppointmentsDiv" class="row margin-bottom">
                <div class="col-sm-12">
                    <div class="form-group left-side">
                        <asp:Label for="cancelUpcomingAppointmentsChkBox" runat="server" class="control-label" meta:resourcekey="cancelUpcomingAppointmentsLbl"></asp:Label>
                        <%--<input type="checkbox" runat="server" id="cancelUpcomingAppointmentsChkBox" />--%>
                        <ej:CheckBox runat="server" ID="cancelUpcomingAppointmentsChkBox" Size="Medium" EnableViewState="true"></ej:CheckBox>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 text-right">
                    <asp:LinkButton ID="cancelAppointmentBtn" runat="server" CssClass="btn btn-danger btn-flat margin-r-5" Text="<%$ Resources:GlobalResources, Cancel %>" OnClientClick="return onCancelAppointmentClick();" OnClick="cancelAppointmentBtn_Click"></asp:LinkButton>
                    <asp:LinkButton ID="closeCancelAppointmentDialogBtn" runat="server" CssClass="btn btn-default btn-flat margin-r-5" Text="<%$ Resources:GlobalResources, Close %>" OnClientClick="closeCancelAppointmentDialog(); return false;"></asp:LinkButton>
                </div>
            </div>
        </DialogContent>
    </ej:Dialog>
    <script language="javascript" type="text/javascript">
        function onCancelAppointmentClick() {
            if (document.getElementById("<%= noChargeableCanceledAppointmentRad.ClientID %>").checked || document.getElementById("<%= chargeableCanceledAppointmentRad.ClientID %>").checked) {
                return true;
            }
            else {
                alert("<%= this.GetLocalResourceObject("ChargeableOptionRequiredMessage") %>");

                return false;
            }
        }
        //-->
    </script>
</asp:Content>
