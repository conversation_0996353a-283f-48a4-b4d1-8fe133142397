/*!
*  filename: ej.togglebutton.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./ej.checkbox.min"],n):n()})(function(){(function(n,t){t.widget("ejToggleButton","ej.ToggleButton",{element:null,model:null,validTags:["input"],_addToPersist:["toggleState"],_setFirst:!1,_rootCSS:"e-togglebutton",defaults:{size:"normal",type:"button",width:"",height:"",enabled:!0,toggleState:!1,defaultText:null,preventToggle:!1,activeText:null,contentType:"textonly",htmlAttributes:{},imagePosition:"imageleft",showRoundedCorner:!1,enablePersistence:!1,cssClass:"",defaultPrefixIcon:null,defaultSuffixIcon:null,activePrefixIcon:null,activeSuffixIcon:null,enableRTL:!1,create:null,click:null,change:null,destroy:null},dataTypes:{size:"string",type:"enum",enabled:"boolean",showRoundedCorner:"boolean",preventToggle:"boolean",defaultText:"string",activeText:"string",contentType:"enum",imagePosition:"enum",defaultPrefixIcon:"string",defaultSuffixIcon:"string",activePrefixIcon:"string",activeSuffixIcon:"string",cssClass:"string",enableRTL:"boolean",enablePersistence:"boolean",htmlAttributes:"data"},observables:["toggleState"],toggleState:t.util.valueFunction("toggleState"),disable:function(){this.buttontag.addClass("e-disable").attr({"aria-disabled":!0});this.model.enabled=!1},enable:function(){this.buttontag.hasClass("e-disable")&&(this.buttontag.removeClass("e-disable").attr({"aria-disabled":!1}),this.model.enabled=!0)},_init:function(){this._cloneElement=this.element.clone();this._initialize();this._controlStatus(this.model.enabled);this._wireEvents();this.initialRender=!1},_destroy:function(){this._off(this.buttontag,"blur",this._tglebtnblur);this.element.unwrap();this.element.removeClass("e-chkbx-hidden e-tbtn");!this._cloneElement.attr("name")&&this.element.attr("name")&&this.element.removeAttr("name");this.labelFinder.empty();this.labelFinder.text(this.model.defaultText);this.buttontag.remove();this.element.unwrap();this.defaultLabel.insertAfter(this.element)},_setModel:function(n){for(var i in n)switch(i){case"size":this._setSize(n[i]);break;case"type":this._settype(n[i]);break;case"height":this._setHeight(n[i]);break;case"width":this._setWidth(n[i]);break;case"contentType":this._setContentType(n[i]);break;case"imagePosition":this._setImagePosition(n[i]);break;case"defaultText":this._setDefaultText(n[i]);break;case"activeText":this._setActiveText(n[i]);break;case"defaultPrefixIcon":this._setDefaultMajorIcon(n[i]);break;case"defaultSuffixIcon":this._setDefaultMinorIcon(n[i]);break;case"activePrefixIcon":this._setActiveMajorIcon(n[i]);break;case"activeSuffixIcon":this._setActiveMinorIcon(n[i]);break;case"enabled":this._controlStatus(n[i]);break;case"toggleState":this._tglevaluestatus(t.util.getVal(n[i]));break;case"showRoundedCorner":this._roundedCorner(n[i]);break;case"cssClass":this._setSkin(n[i]);break;case"enableRTL":this._setRTL(n[i]);break;case"htmlAttributes":this._addAttr(n[i])}},_setSize:function(n){this.buttontag.removeClass("e-btn-mini e-btn-medium e-btn-small e-btn-large e-btn-normal");this.buttontag.addClass("e-btn-"+n)},_settype:function(n){this.model.type=n},_setHeight:function(n){this.buttontag.css("height",n)},_setWidth:function(n){this.buttontag.css("width",n)},_setDefaultText:function(n){this.toggleState()||(this.model.contentType==t.ContentType.TextOnly?this.buttontag.html(n):this.defaulttxtspan.html(n))},_setActiveText:function(n){this.toggleState()&&(this.model.contentType==t.ContentType.TextOnly?this.buttontag.html(n):this.defaulttxtspan.html(n))},_setDefaultMajorIcon:function(n){this.defMainIcon.removeClass(this.model.defaultPrefixIcon);this.defMainIcon.addClass(n)},_setDefaultMinorIcon:function(n){this.defMiniIcon.removeClass(this.model.defaultSuffixIcon);this.defMiniIcon.addClass(n)},_setActiveMajorIcon:function(n){this.toggleState()&&(this.defMainIcon.removeClass(this.model.activePrefixIcon),this.defMainIcon.addClass(n))},_setActiveMinorIcon:function(n){this.toggleState()&&(this.defMiniIcon.removeClass(this.model.activeSuffixIcon),this.defMiniIcon.addClass(n))},_setContentType:function(n){n!=this.model.contentType&&(this.buttontag.empty(),this.model.contentType=n,this._renderButtonContent())},_setImagePosition:function(n){this.model.contentType==t.ContentType.TextAndImage&&n!=this.model.imagePosition&&(this.buttontag.empty(),this.model.imagePosition=n,this._renderButtonContent())},_setRTL:function(n){n?this.buttontag.addClass("e-rtl"):this.buttontag.removeClass("e-rtl")},_controlStatus:function(n){n?this.enable():this.disable()},_setSkin:function(n){this.model.cssClass!=n&&(this.buttontag.removeClass(this.model.cssClass),this.buttontag.addClass(n))},_initialize:function(){this.element.is('[type = "checkbox"]')?this._render():this.element.removeClass("e-togglebutton")},_render:function(){this.initialRender=!0;var i,r;this.element.addClass("e-chkbx-hidden e-tbtn");this.element.attr("name")||this.element.attr("name",this.element[0].id);this.element.attr("tabindex","-1");i=this.element.parents().last();r="label[for='"+this.element[0].id+"']";this.labelFinder=i.find(r);this.defaultLabel=i.find(r).clone();this.labelFinder.length||(this.labelFinder=t.buildTag("label","Button",{},{"for":this.element[0].id}),this.labelFinder.insertAfter(this.element));(this.model.defaultText==null||this.model.defaultText=="")&&(this.model.defaultText=this.labelFinder.text());this.labelFinder.empty();this.wrapper=n('<span id="'+this.element[0].id+'-wrapper" class="e-tbtn-wrap e-widget"><\/span>');this.labelFinder.append(this.element);this.buttontag=t.buildTag("button.e-togglebutton e-btn e-tbtn "+this.model.cssClass+" e-select","",{},{role:"button",tabindex:0,type:this.model.type,"data-role":"none"});t.isTouchDevice()||this.buttontag.addClass("e-ntouch");n(this.labelFinder).wrap(this.wrapper);this.buttontag.insertAfter(this.labelFinder);t.util.isNullOrUndefined(this.model.activeText)&&(this.model.activeText=this.model.defaultText);t.util.isNullOrUndefined(this.model.activePrefixIcon)&&(this.model.activePrefixIcon=this.model.defaultPrefixIcon);t.util.isNullOrUndefined(this.model.activeSuffixIcon)&&(this.model.activeSuffixIcon=this.model.defaultSuffixIcon);this._setSize(this.model.size);this._setHeight(this.model.height);this._setWidth(this.model.width);this._setRTL(this.model.enableRTL);this._renderButtonContent();this.toggleState()||t.isNullOrUndefined(this.element.attr("checked"))||this.toggleState(!0);this._tglevaluestatus(this.toggleState());this._roundedCorner(this.model.showRoundedCorner);this._addAttr(this.model.htmlAttributes)},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.wrapper.addClass(n):t=="disabled"&&n=="disabled"?i.disable():t=="checked"&&n=="checked"?i._tglevaluestatus(this.toggleState()):i.wrapper.attr(t,n)})},_renderButtonContent:function(){if(this.imgtxtwrap=t.buildTag("span").addClass("e-btn-span"),this.defaulttxtspan=t.buildTag("span.e-btntxt#"+this.element[0].id+"textstatic",this.toggleState()?this.model.activeText:this.model.defaultText),this.model.contentType.indexOf("image")>-1&&(this.defMainIcon=t.buildTag("span #"+this.element[0].id+"mainiconstatic"),this.defMiniIcon=t.buildTag("span #"+this.element[0].id+"miniconstatic"),this.toggleState()?this.defMainIcon.addClass(this.model.activePrefixIcon):this.defMainIcon.addClass(this.model.defaultPrefixIcon),this.toggleState()?this.defMiniIcon.addClass(this.model.activeSuffixIcon):this.defMiniIcon.addClass(this.model.defaultSuffixIcon)),this.model.contentType==t.ContentType.TextAndImage){switch(this.model.imagePosition){case t.ImagePosition.ImageRight:this.imgtxtwrap.append(this.defaulttxtspan,this.defMainIcon);break;case t.ImagePosition.ImageLeft:this.imgtxtwrap.append(this.defMainIcon,this.defaulttxtspan);break;case t.ImagePosition.ImageBottom:this.defMainIcon.attr("style","display:inherit");this.imgtxtwrap.append(this.defaulttxtspan,this.defMainIcon);break;case t.ImagePosition.ImageTop:this.defMainIcon.attr("style","display:inherit");this.imgtxtwrap.append(this.defMainIcon,this.defaulttxtspan)}this.buttontag.append(this.imgtxtwrap)}else this.model.contentType==t.ContentType.ImageTextImage?(this.imgtxtwrap.append(this.defMainIcon,this.defaulttxtspan,this.defMiniIcon),this.buttontag.append(this.imgtxtwrap)):this.model.contentType==t.ContentType.ImageOnly?(this.imgtxtwrap.append(this.defMainIcon),this.buttontag.append(this.imgtxtwrap)):this.model.contentType==t.ContentType.ImageBoth?(this.imgtxtwrap.append(this.defMainIcon,this.defMiniIcon),this.buttontag.append(this.imgtxtwrap)):(this.buttontag.addClass("e-txt"),this.buttontag.text(this.model.defaultText))},_tglevaluestatus:function(n){n?(this._toggleButtonStatus(n),this.element.attr("checked","checked")):(this._toggleButtonStatus(n),this.element.prop("checked",!1))},_roundedCorner:function(n){n==!0?this.buttontag.addClass("e-corner"):this.buttontag.removeClass("e-corner")},_wireEvents:function(){this._on(this.buttontag,"click",this._tglebtnclicked);this._on(this.buttontag,"blur",this._tglebtnblur)},_tglebtnblur:function(){this.buttontag.removeClass("e-animate")},_tglebtnclicked:function(i){if(this.model.preventToggle&&!this.model.enabled)return!1;this.model.preventToggle||this.buttontag.hasClass("e-disable")||(this.toggleState(this.toggleState()?!1:!0),t.browserInfo().name=="msie"&&t.browserInfo().version=="8.0"?this._tglevaluestatus(this.toggleState()):this._toggleButtonStatus(this.toggleState()),n(this.element).prop("checked",this.toggleState()),i.preventDefault(),this.buttontag.addClass("e-animate"),this._trigger("click",{isChecked:this.toggleState(),status:this.model.enabled}))},_toggleButtonStatus:function(n){n?(this.model.contentType==t.ContentType.TextOnly?this.buttontag.html(this.model.activeText):(this.defaulttxtspan.html(this.model.activeText),this.defMainIcon.removeClass(this.model.defaultPrefixIcon).addClass(this.model.activePrefixIcon),this.defMiniIcon.removeClass(this.model.defaultSuffixIcon).addClass(this.model.activeSuffixIcon)),this.buttontag.addClass("e-active").attr("aria-pressed",!0)):(this.model.contentType==t.ContentType.TextOnly?this.buttontag.html(this.model.defaultText):(this.defaulttxtspan.html(this.model.defaultText),this.defMainIcon.removeClass(this.model.activePrefixIcon).addClass(this.model.defaultPrefixIcon),this.defMiniIcon.removeClass(this.model.activeSuffixIcon).addClass(this.model.defaultSuffixIcon)),this.buttontag.removeClass("e-active").attr("aria-pressed",!1));this.toggleState(n);this.initialRender||this._trigger("change",{isChecked:this.toggleState()})}});t.ContentType={TextOnly:"textonly",ImageOnly:"imageonly",ImageBoth:"imageboth",TextAndImage:"textandimage",ImageTextImage:"imagetextimage"};t.ImagePosition={ImageRight:"imageright",ImageLeft:"imageleft",ImageTop:"imagetop",ImageBottom:"imagebottom"};t.ButtonSize={Normal:"normal",Mini:"mini",Small:"small",Medium:"medium",Large:"large"};t.ButtonType={Button:"button",Reset:"reset",Submit:"submit"}})(jQuery,Syncfusion)});
