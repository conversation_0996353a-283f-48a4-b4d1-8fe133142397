﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace WebUI
{
    public class DateFunctions
    {
        public static DateTime GetFirstDayOfWeek(DateTime dayInWeek)
        {
            // DayOfWeek firstDay = cultureInfo.DateTimeFormat.FirstDayOfWeek;
            DateTime firstDayInWeek = dayInWeek.Date;
            while (firstDayInWeek.DayOfWeek != DayOfWeek.Monday)
                firstDayInWeek = firstDayInWeek.AddDays(-1);

            return firstDayInWeek;
        }
    }
}