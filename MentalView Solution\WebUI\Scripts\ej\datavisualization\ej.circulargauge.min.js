/*!
*  filename: ej.circulargauge.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function(n,t,i){var r,f,u;t.widget({ejCircularGauge:"ej.datavisualization.CircularGauge"},{_rootCSS:"e-circulargauge",validTags:["div","span"],_savedPoints:[],_labelRadius:0,_customLblMaxSize:0,defaults:{exportSettings:{mode:"client",type:"png",fileName:"CircularGauge",action:""},locale:null,enableGroupSeparator:!1,value:0,minimum:0,maximum:100,radius:180,width:360,height:360,frame:{frameType:"fullcircle",backgroundImageUrl:null,halfCircleFrameStartAngle:180,halfCircleFrameEndAngle:360},backgroundColor:null,interiorGradient:null,readOnly:!0,enableAnimation:!0,animationSpeed:500,theme:"flatlight",isRadialGradient:!1,enableResize:!1,isResponsive:!1,tooltip:{showLabelTooltip:!1,showCustomLabelTooltip:!1,templateID:null},outerCustomLabelPosition:"bottom",gaugePosition:"center",distanceFromCorner:20,rangeZOrder:"rear",drawTicks:null,drawLabels:null,drawPointers:null,drawRange:null,drawCustomLabel:null,drawIndicators:null,drawPointerCap:null,load:null,doubleClick:"",rightClick:"",renderComplete:null,mouseClick:null,mouseClickMove:null,mouseClickUp:null,legendItemRender:null,legendItemClick:null,tooltipRendering:null,rangeMouseMove:null,scales:null,legend:{visible:!1,toggleVisibility:!0,border:{color:"transparent",width:1},itemPadding:20,shape:"circle",alignment:"center",position:"bottom",itemStyle:{height:9,width:9,border:{color:"transparent",width:1}},opacity:1,fill:null,font:{color:null,fontFamily:"Segoe UI",fontStyle:"Normal",fontWeight:"Regular",size:"12px"},size:{height:null,width:null}},themeProperties:{flatlight:{backgroundColor:"#FFFFFF",scales:{pointerCap:{borderColor:"#424242",backgroundColor:"#424242"},backgroundColor:"#777777",border:{color:"#777777"},pointers:{backgroundColor:"#424242",border:{color:"#424242"}},ticks:{color:"#777777"},labels:{color:"#282828"}}},flatdark:{backgroundColor:"#1F1F1F",scales:{pointerCap:{borderColor:"#686868",backgroundColor:"#686868"},backgroundColor:"#7a7a7a",border:{color:"#7a7a7a"},pointers:{backgroundColor:"#686868",border:{color:"#686868"}},ticks:{color:"#7a7a7a"},labels:{color:"#d3d3d3"}}}}},_defaultScaleValues:function(){return{size:6,pointerCap:{radius:7,borderWidth:3,interiorGradient:null,borderColor:null,backgroundColor:null},showScaleBar:!1,sweepAngle:310,radius:170,startAngle:115,majorIntervalValue:10,minorIntervalValue:2,maximum:null,minimum:null,border:{color:null,width:1.5},backgroundColor:null,direction:"clockwise",showPointers:!0,showRanges:!1,showTicks:!0,showLabels:!0,showIndicators:!1,opacity:1,shadowOffset:0,customLabels:[{value:null,color:null,textAngle:0,positionType:"inner",position:{x:0,y:0},font:{size:"11px",fontFamily:"arial",fontStyle:"bold"}}],pointers:[{distanceFromScale:0,showBackNeedle:!1,backNeedleLength:10,length:150,placement:"near",width:7,opacity:1,value:null,border:{color:null,width:1.5},backgroundColor:null,gradients:null,type:"needle",needleType:"triangle",markerType:"rectangle",imageUrl:"",pointerValueText:{showValue:!1,distance:20,font:{size:"11px",fontFamily:"Arial",fontStyle:"Bold"},color:"#8c8c8c",opacity:1,autoAngle:!1,angle:0}}],ranges:[{legendText:null,distanceFromScale:25,size:5,placement:"near",startValue:null,endValue:null,startWidth:null,endWidth:null,gradients:null,opacity:null,backgroundColor:"#32b3c6",border:{color:"#32b3c6",width:1.5}}],ticks:[{angle:0,distanceFromScale:0,color:null,type:"major",placement:"near",height:16,width:3},{angle:0,distanceFromScale:0,color:null,type:"minor",placement:"near",height:7,width:1}],labels:[{angle:0,autoAngle:!1,opacity:null,font:{size:"11px",fontFamily:"Arial",fontStyle:"Bold"},color:null,distanceFromScale:0,includeFirstValue:!0,placement:"near",type:"major",unitText:"",unitTextPosition:"back"}],indicators:[{height:15,width:15,type:"circle",imageUrl:null,position:{x:0,y:0},stateRanges:[{endValue:0,startValue:0,backgroundColor:null,borderColor:null,text:"",textColor:null,font:null}]}],subGauges:[{height:150,width:150,position:{x:0,y:0}}]}},dataTypes:{scales:"data",isResponsive:"boolean"},observables:["value","minimum","maximum"],_tags:[{tag:"scales",attr:["showScaleBar","pointerCap.radius","pointerCap.borderWidth","pointerCap.interiorGradient","pointerCap.borderColor","pointerCap.backgroundColor","sweepAngle","startAngle","showPointers","showTicks","backgroundColor","scaleRadius","majorIntervalValue","minorIntervalValue","shadowOffset","showRanges","showLabels","showCustomLabels","showIndicators","border.width","border.color",[{tag:"pointers",attr:["distanceFromScale","showBackNeedle","backNeedleLength","backgroundColor","needleType","markerType","border.width","border.color","imageUrl","pointerValueText.showValue","pointerValueText.distance","pointerValueText.font.size","pointerValueText.font.fontFamily","pointerValueText.font.fontStyle","pointerValueText.angle","pointerValueText.autoAngle","pointerValueText.color","pointerValueText.opacity"]},{tag:"labels",attr:["autoAngle","distanceFromScale","includeFirstValue","unitText","unitTextPosition","font.size","font.fontFamily","font.fontStyle"]},{tag:"ticks",attr:["distanceFromScale"]},{tag:"ranges",attr:["legendText","distanceFromScale","startValue","endValue","startWidth","endWidth","backgroundColor","border.color","border.width"]},{tag:"indicators",attr:["imageUrl","position.x","position.y",[{tag:"stateRanges",attr:["endValue","startValue","backgroundColor","borderColor","textColor"]}]]},{tag:"subGauges",attr:["controlID","position.x","position.y"]},{tag:"customLabels",attr:["positionType","textAngle","position.x","position.y","font.size","font.fontFamily","font.fontStyle"]}]]}],value:t.util.valueFunction("value"),minimum:t.util.valueFunction("minimum"),maximum:t.util.valueFunction("maximum"),_setModel:function(t){var i,r,u,f,e;for(i in t)switch(i){case"height":this.model.height=t[i];break;case"width":this.model.width=t[i];break;case"radius":this.model.radius=t[i];break;case"frame":n.extend(this.model.frame,t[i]);break;case"tooltip":n.extend(this.model.tooltip,t[i]);break;case"backgroundColor":this.model.backgroundColor=t[i];break;case"interiorGradient":this.model.interiorGradient=t[i];break;case"readOnly":this.model.readOnly=t[i];break;case"theme":this.model.theme=t[i];break;case"isRadialGradient":this.model.isRadialGradient=t[i];break;case"outerCustomLabelPosition":this.model.outerCustomLabelPosition=t[i];break;case"gaugePosition":this.model.gaugePosition=t[i];break;case"distanceFromCorner":this.model.distanceFromCorner=t[i];break;case"rangeZOrder ":this.model.rangeZOrder=t[i];break;case"value":for(this.value()==""&&this.value(0),r=0;this.model.scales[r]!=null;r++)for(u=0;this.model.scales[r].pointers[u]!=null;u++)this.model.scales[r].pointers[u].value=parseFloat(this.value());break;case"minimum":for(this.minimum()==""&&this.minimum(0),f=0;this.model.scales[f]!=null;f++)this.model.scales[f].minimum=parseInt(this.minimum());break;case"maximum":for(this.maximum()==""&&this.maximum(100),e=0;this.model.scales[e]!=null;e++)this.model.scales[e].maximum=parseInt(this.maximum());break;case"scales":n.extend(!0,this.model.scales,t[i])}this._init()},_destroy:function(){this._unWireEvents();this.element.empty().removeClass("e-circulargauge e-js e-widget")},_init:function(){r=n(".e-circulargauge").length;f=r;this._scaleRedrawn=!1;this._scalesInitialize();this._trigger("load");this._setTheme();this._initialize();this.model.frame.backgroundImageUrl&&this.initialize();this._wireEvents();this._onWindowResize()},_onWindowResize:function(){(this.model.enableResize||this.model.isResponsive)&&(t.isTouchDevice()?this._on(n(window),"orientationchange",this.resizeCanvas):this._on(n(window),"resize",this.resizeCanvas))},_initialize:function(){this._androidAnimation=this.isAndroid()&&t.datavisualization.CircularGauge.animationPolyfill;this._initObject(this);this._drawOuterLayer();this.model.legend.visible&&this.model.legend._legendItemHeight>0&&this.model.legend._legendItemWidth>0&&(this._drawLegend(),this._legendDrawn=!0);this._drawScales();this.model.renderComplete&&this._onRenderComplete()},_drawLegend:function(){var r=this.model.legend,n,p=this.GaugeEl,u=20,c=this.model.legend.position.toLowerCase(),l=this.model.legend.alignment.toLowerCase(),y,f=this.model.legend.border.width,e=this.model.width,o=this.model.height,s,a,h;for(n=this.model.legendActualBounds,c=="top"||c=="bottom"?(n.y=c=="top"?f+u:o-n.Height,n.x=l=="center"?e/2-n.Width/2:l=="near"?f+u:e-n.Width-u,n.x=n.x+n.Width>e?n.x-Math.abs(n.x+n.Width-e):n.x):(n.x=c=="left"?f:e-n.Width,n.y=l=="center"?o/2-n.Height/2:l=="near"?f+u:o-n.Height-u,n.y=n.y+n.Height>o?n.y-Math.abs(n.y+n.Height-o):n.y),this._legendBounds=n,this.contextEl.lineWidth=f,this.contextEl.save(),this.contextEl.strokeStyle=this.model.legend.border.color,this.contextEl.strokeRect(n.x,n.y,n.Width,n.Height),this.contextEl.restore(),this.model._legendCollection=[],s=0;s<this.model.scales.length;s++)for(a=this.model.scales[s],h=0;h<a.ranges.length;h++){var i=a.ranges[h],v;i._visible=t.util.isNullOrUndefined(i._visible)?!0:i._visible;t.util.isNullOrUndefined(i.legendText)||(v=this._getLegendSize(i,10),y={Bounds:{Height:v.Height,Width:v.Width},legendStyle:{font:r.font,opacity:r.opacity,BorderColor:r.itemStyle.border.color,BorderWidth:r.itemStyle.border.width},displayText:i.legendText,fill:t.util.isNullOrUndefined(r.fill)?i.backgroundColor:r.fill,ScaleIndex:s,RangeIndex:h,visible:i._visible},this.model._legendCollection.push(y))}this._drawLegendItem(n)},legendMouseClick:function(n){var i,t,u,f,e,o,s,h,r=this.calMousePosition(n);if(this.model.legend.toggleVisibility)for(i=0;i<this.legendRegion.length;i++)t=this.legendRegion[i],u=t.X,f=t.X+t.Width,e=t.Y,o=t.Y+t.Height,r.X>=u&&r.X<=f&&r.Y>=e&&r.Y<=o&&(s={type:"legendItemClick",cancel:!1,data:t,model:this.model},this._trigger("legendItemClick",s),h=this.model.scales[t.item.ScaleIndex].ranges[t.item.RangeIndex]._visible?!1:!0,this.setRangeVisible(t.item.ScaleIndex,t.item.RangeIndex,h))},calMousePosition:function(t){var e=jQuery.uaMatch(navigator.userAgent),r,u,f,o,i;return r=this.GaugeEl[0],i=r.querySelector("canvas"),o=e.browser.toLowerCase(),u=t.pageX-n(i).offset().left,f=t.pageY-n(i).offset().top,{X:u,Y:f}},rangesMouseMove:function(t){var ot=this,a,v,p,w,b,k,d,g,ft,f,r,nt,y,s,tt,h=this.calMousePosition(t),i,e,o,et,it,rt,ut,u,c,l;if(this.model.legend.toggleVisibility&&this._legendDrawn)for(v=0;v<this.legendRegion.length;v++)if(i=this.legendRegion[v],p=i.X,w=i.X+i.Width,b=i.Y,k=i.Y+i.Height,h.X>=p&&h.X<=w&&h.Y>=b&&h.Y<=k){n(this.GaugeEl[0]).css("cursor","pointer");break}else n(this.GaugeEl[0]).css("cursor","default");for(d=h.X,g=h.Y,ft=[],f=this.model.scales[0].startAngle,r=this.model.scales[0].sweepAngle,nt=f*(Math.PI/180),y=0;y<this._rangeCollectionRegions.length;y++)if(i=this._rangeCollectionRegions[y],e=d-i.centerRadius.centerX,o=g-i.centerRadius.centerY,et=i.Radius.endRadius?i.Radius.endRadius:0,f=f<0?f+360:f,r=r<0?r+360:r,it=r-f,rt=-.5*Math.PI+nt,it<0){if(r=r/360,ut=r?2*Math.PI*(r<0?1+r:r):0,u=(Math.atan2(o,e)-rt-ut)%(2*Math.PI),u<0&&(u=2*Math.PI+u),c=Math.PI*(i.Region.startAngle/180),l=Math.PI*(i.Region.endAngle/180),u<=c&&u>=l&&(s=Math.sqrt(Math.pow(Math.abs(e),2)+Math.pow(Math.abs(o),2)),s<=i.Radius.startRadius&&s>i.Radius.endRadius)){a={type:"rangeMouseMove",cancel:!1,data:i,model:this.model};this._trigger("rangeMouseMove",a);break}}else if(u=Math.atan2(o,e)%(2*Math.PI),u<0&&(u=2*Math.PI+u),i.Region.endAngle<i.Region.startAngle?(c=e>0&&o>0?0:Math.PI*(i.Region.startAngle/180),l=e>0&&o>0?Math.PI*(i.Region.endAngle/180):Math.PI*((360+i.Region.endAngle)/180)):(c=Math.PI*(i.Region.startAngle/180),l=Math.PI*(i.Region.endAngle/180)),u>=c&&u<=l&&(s=Math.sqrt(Math.pow(Math.abs(e),2)+Math.pow(Math.abs(o),2)),s<=i.Radius.startRadius&&s>i.Radius.endRadius)){tt=i;a={type:"rangeMouseMove",cancel:!1,data:i,model:this.model};this._trigger("rangeMouseMove",a);break}return tt},_cgDoubleClick:function(n){this.model.doubleClick!=""&&this._trigger("doubleClick",{data:{event:n}})},_cgRightClick:function(n){this.model.rightClick!=""&&this._trigger("rightClick",{data:{event:n}})},_drawLegendItem:function(i){var s=[],e=[],st,tt,r,nt,pt;for(this.legendRegion=[],this.model.legend._legendTextMaxHeight=0,this.model.legend._legendTextMaxWidth=0,tt=this.model.legend.position.toLowerCase(),r=0;r<this.model._legendCollection.length;r++){var c=this.model.legend.shape.toLowerCase(),y=10,p=10,w=3,a,v,it,rt,ut,u,b,ht,k,ft,ct,et,lt,at,o,vt,ot,f,d,h,l,g,yt=10;u=this.model._legendCollection[r];t.util.isNullOrUndefined(u.displayText)||(st={type:"legendItemRender",cancel:!1,data:u,model:this.model},this._trigger("legendItemRender",st),this.model.scales[u.ScaleIndex].ranges[u.RangeIndex]._visible=t.util.isNullOrUndefined(this.model.scales[u.ScaleIndex].ranges[u.RangeIndex]._visible)?!0:this.model.scales[u.ScaleIndex].ranges[u.RangeIndex]._visible,ht=this.model.scales[u.ScaleIndex].ranges[u.RangeIndex]._visible?u.fill:"#808080",lt=this.model.scales[u.ScaleIndex].ranges[u.RangeIndex]._visible?u.legendStyle.font.color:"#808080",at=this._getFontString(this,u.legendStyle.font),o=this.calcText(u.displayText,i.Width,u.legendStyle.font),this.model.legend._legendTextMaxHeight=Math.max(o.height,this.model.legend._legendTextMaxHeight),this.model.legend._legendTextMaxWidth=Math.max(o.width,this.model.legend._legendTextMaxWidth),vt=this.model.legend.itemStyle.height,ot=this.model.legend.itemStyle.width,f=(vt+ot)/2,d={angle:0,width:f,isFill:!0,isStroke:!0,height:f,lineWidth:this.model.legend.itemStyle.border.width,opacity:u.legendStyle.opacity,strokeStyle:u.legendStyle.BorderColor,fillStyle:ht},y=f>o.height/2?y+f/2:y+o.height/4,p=c=="circle"?p+f/2:p,r==0?(a=p+i.x,v=i.y+y):tt=="top"||tt=="bottom"?(nt=e[r-1].x+e[r-1].width+this.model.legend.itemPadding,nt+f+w+o.width>=i.x+i.Width?(a=p+i.x,v=this.model.legend._legendTextMaxHeight/2>f?e[r-1].y+s[r-1].actualBounds.height/2+f/2+yt:yt+s[r-1].actualBounds.y+s[r-1].actualBounds.height/2+f/2):(a=c.toLowerCase()=="circle"?nt+f/2:nt,v=s[r-1].actualBounds.y)):(ut=e[r-1].height>f?e[r-1].y+this.model.legend.itemPadding:s[r-1].actualBounds.y+f+this.model.legend.itemPadding,pt=e[r-1].height>f?o.height:f,ut+pt-2>i.y+i.Height+2?(v=i.y+y,a=s[r-1].actualBounds.x+s[r-1].actualBounds.width+(this.model.legend._legendTextMaxWidth+w)+10):(a=s[r-1].actualBounds.x,v=ut)),h={startX:a,startY:v},c=c.charAt(0).toUpperCase()+c.slice(1),l=n.extend({},h,!0),l.startX=c.toLowerCase()=="circle"?l.startX-f/2:l.startX,l.startY=l.startY-f/2,s.push({actualBounds:{x:h.startX,y:h.startY,height:f,width:f},item:u}),b={angle:0,width:o.width,isFill:!0,isStroke:!0,height:o.height,textValue:u.displayText,font:at,fillStyle:lt,opacity:u.legendStyle.opacity,strokeStyle:u.legendStyle.BorderColor},it=c.toLowerCase()=="circle"?h.startX+ot/2+w:h.startX+d.width+w,rt=h.startY+o.height/4,e.push({x:it,y:rt,height:o.height/2,width:o.width}),k=l.startX,ct=Math.abs(k+s[r].actualBounds.width-k)+w+Math.abs(e[r].x+e[r].width-e[r].x),f>e[r].height?(ft=l.startY,et=f):(ft=e[r].y-e[r].height,et=e[r].height),c.toLowerCase()=="circle"?(g=this._setPointerDimension(d,this),this._contextOpenPath(g,this),this._setContextRotation(g,this),this.contextEl.arc(h.startX,h.startY,f/2,0,2*Math.PI),this._contextClosePath(g,this)):this["_draw"+c](h,d,this),this._contextOpenPath(b,this),this.contextEl.font=b.font,this.contextEl.translate(it,rt),this.contextEl.fillText(b.textValue,0,0),this._contextClosePath(b,this),this.legendRegion.push({X:k,Y:ft,Width:ct,Height:et,item:u}))}},calcText:function(t,r,u){var h=n(document).find("#measureTex"),f,c=null,l=null,a=null,v=null,o,s,e,y;if(n("#measureTex").css("display","block"),h.length==0?(f=document.createElement("text"),n(f).attr({id:"measureTex"}),document.body.appendChild(f)):f=h[0],typeof t=="string"&&(t.indexOf("<")>-1||t.indexOf(">")>-1)){for(o=t.split(" "),s=0;s<o.length;s++)o[s].indexOf("<br/>")==-1&&(o[s]=o[s].replace(/[<>]/g,"&"));t=o.join(" ")}return f.innerHTML=t,u!=i&&u.size==i&&(e=u,e=e.split(" "),c=e[0],l=e[1],a=e[2],v=e[3]),u!=null&&(f.style.fontSize=u.size>0?u.size+"px":u.size?u.size:l,f.style.fontStyle&&(f.style.fontStyle=u.fontStyle?u.fontStyle:c),f.style.fontFamily=u.fontFamily?u.fontFamily:a,window.navigator.userAgent.indexOf("MSIE 8.0")==-1&&(f.style.fontWeight=u.fontWeight?u.fontWeight:v)),f.style.backgroundColor="white",f.style.position="absolute",f.style.top=-100,f.style.left=0,f.style.visibility="hidden",f.style.whiteSpace="nowrap",r&&(f.style.maxwidth=r+"px"),y={width:f.offsetWidth,height:f.offsetHeight},n("#measureTex").css("display","none"),y},_calculateLegendBounds:function(){var i=this,r=i.model.legend,f=0,a,e=i.model.legend.position.toLowerCase(),o,v,c,u=10,y=i.model.legend.border.width,p=i.model.legend.itemPadding,s,l,h;return s=i.findMaxHeightWidth(),this._gaugeResizeState||t.util.isNullOrUndefined(i.model.legend.size.height)&&t.util.isNullOrUndefined(i.model.legend.size.width)?(h=i.findLegendMax(),l={Height:h.Height,Width:h.Width},i.model.legend.size._height=i.model.legend.size._width=null):parseInt(i.model.legend.size.width)<s.width&&!t.util.isNullOrUndefined(i.model.legend.size.width)||parseInt(i.model.legend.size.height)<s.height&&!t.util.isNullOrUndefined(i.model.legend.size.height)?(h=i.findLegendMax(),l={Height:h.Height,Width:h.Width},i.model.legend.size._height=i.model.legend.size._width=null):(i.model.legend._legendItemHeight=0,i.model.legend._columnWidth=0,i.model.legend._legendItemWidth=0,i.model.legend._rowCount=1,i.model.legend._columnCount=1,i.model.legend._maxWidth=0,i.model.legend._maxHeight=0,i._columnIncreasing=0,i._columnDecreasing=0,n.each(i.model.scales,function(w,b){if(b.showRanges&&(i.scaleIndex=w,b.ranges!=null)){for(i.rangeEl=b.ranges,i.index=0,a=0;a<i.model.scales[i.scaleIndex].ranges.length;a++)t.util.isNullOrUndefined(i.model.scales[i.scaleIndex].ranges[a].legendText)||f++;n.each(b.ranges,function(n,a){i.rangeIndex=n;!t.util.isNullOrUndefined(a.legendText)&&a.legendText.replace(/\s/g,"").length>0&&i.index<f&&(o=i._getLegendSize(a),parseInt(i.model.legend.size.width)?(c=i.index==0?u+y:e=="top"||e=="bottom"?p:u,v=i.index==0?u+y:0,r._legendItemWidth=e=="top"||e=="bottom"?c+o.Width+r._legendItemWidth:c+s.width+r._legendItemWidth,r._legendItemHeight=v+Math.max(o.Height,r._legendItemHeight),i._columnIncreasing++,i._columnDecreasing=Math.max(i._columnDecreasing,i._columnIncreasing),parseInt(i.model.legend.size.width)<=r._legendItemWidth?(i.model.legend._rowCount++,i._columnIncreasing=0,r._legendItemHeight+=e=="top"||e=="bottom"?i.index==f-1?o.Height+u*2:o.Height+u:i.index==f-1?o.Height+u+p:o.Height+p,r._legendItemWidth=e=="top"||e=="bottom"?i.index==f-1?parseInt(i.model.legend.size.width):o.Width+u:s.width+u):i.index==f-1&&i.model.legend._rowCount>1&&(r._legendItemHeight+=u,i.model.legend.size._width=i.model.legend.size.width,parseInt(i.model.legend.size.width)>s.width*(i._columnDecreasing-1)+(i._columnDecreasing-1)*u+u&&(r._legendItemWidth=s.width*(i._columnDecreasing-1)+(i._columnDecreasing-1)*u+u,i.model.legend.size._width=null))):e=="left"||e=="right"?(c=i.index==0?u+y:i.index==f-1?u:0,v=i.index==0?u+y:p,r._legendItemWidth=c+Math.max(s.width,r._legendItemWidth),r._legendItemHeight=v+o.Height+r._legendItemHeight,i.model.legend.size._height=parseInt(i.model.legend.size.height),parseInt(i.model.legend.size.height)<r._legendItemHeight?(i.model.legend._rowCount++,r._legendItemWidth+=i.index==f-1?s.width+u*2:s.width+u,r._legendItemHeight=o.Height+u):f==1&&parseInt(i.model.legend.size.height)>r._legendItemHeight&&(i.model.legend.size._height=null,r._legendItemWidth+=u,r._legendItemHeight+=u)):(h=i.findLegendMax(),l={Height:h.Height,Width:h.Width},i.index=f-1));i.index++})}}),l={Height:r._legendItemHeight,Width:r._legendItemWidth}),l},findLegendMax:function(){var i=this,r;i.model.legend._legendItemHeight=0;i.model.legend._rowWidth=0;i.model.legend._legendItemWidth=0;i.model.legend._rowCount=1;i.model.legend._columnCount=1;i.model.legend._maxWidth=0;i.model.legend._maxHeight=0;var r=i.model.legend,f=0,a,v,y=i.model.legend.position.toLowerCase(),o,h,c,e=20,u=10,p,s=i.model.legend.border.width,l=i.model.legend.itemPadding;return i.model.legend.size._height=i.model.legend.size._width=null,n.each(i.model.scales,function(w,b){if(b.showRanges&&(i.scaleIndex=w,b.ranges!=null)){for(i.rangeEl=b.ranges,i.index=0,a=0;a<i.model.scales[i.scaleIndex].ranges.length;a++)t.util.isNullOrUndefined(i.model.scales[i.scaleIndex].ranges[a].legendText)||f++;n.each(b.ranges,function(n,a){i.rangeIndex=n;t.util.isNullOrUndefined(a.legendText)||(o=i._getLegendSize(a),y=="top"||y=="bottom"?(f!=1?(c=i.index==0?u*2+s:i.index==f-1?u+l:l,h=i.index==0?u:i.index==f-1?u:0):h=c=u*2,r._legendItemHeight=h+Math.max(o.Height,r._legendItemHeight),r._maxHeight=Math.max(o.Height,r._maxHeight),r._legendItemWidth=c+o.Width+r._legendItemWidth,p=i.model.legend._rowCount>1&&i.index==f-1?u:0,i.model.width-(e-s)<=r._legendItemWidth-p?(i.model.legend._rowCount++,r._rowWidth=Math.max(r._rowWidth,r._legendItemWidth-l-o.Width),r._legendItemWidth=i.index==f-1?r._rowWidth<i.model.width-e*2?r._rowWidth:i.model.width-e*2:i.index==0?e+u+s:o.Width+e+u+s,r._legendItemHeight+=r._maxHeight+(u+s)):i.index==f-1&&i.model.legend._rowCount>1&&(r._legendItemWidth=r._rowWidth<i.model.width-e*2?r._rowWidth:i.model.width-e*2)):(f!=1?(h=i.index==0?u+s:i.index==f-1?u+l:l,c=i.index==0?u:i.index==f-1?u*2:0):h=c=u*2,r._legendItemHeight=o.Height+h+r._legendItemHeight,r._legendItemWidth=c+Math.max(o.Width,r._legendItemWidth),r._maxWidth=Math.max(o.Width,r._maxWidth),i.model.height-e<r._legendItemHeight?(i.model.legend._columnCount++,v=i.model.legend._columnCount==2?e:0,r._legendItemHeight=i.index==f-1?i.model.height-e*2:o.Height+e+u+s+v,r._legendItemWidth+=r._maxWidth+u):i.index==f-1&&i.model.legend._columnCount>1&&(r._legendItemHeight=i.model.height-e*2)),i.index++)})}}),{Height:r._legendItemHeight,Width:r._legendItemWidth}},findMaxHeightWidth:function(){var u=this,t=0,i=0,r;return n.each(u.model.scales,function(f,e){n.each(e.ranges,function(n,f){r=u._getLegendSize(f);t=Math.max(r.Height,t);i=Math.max(r.Width,i)})}),{height:t,width:i}},_getLegendSize:function(n){var t,i,r,u,f=this.model.legend.font;return t=(this.model.legend.itemStyle.width+this.model.legend.itemStyle.height)/2,i=this.calcText(n.legendText,null,f),r=Math.max(t,i.height/2),u=t+3+i.width,{Height:r,Width:u}},_scalesInitialize:function(){var t=this;this.model.scales!=null?n.each(this.model.scales,function(i,r){r=t._checkArrayObject(r,i);var u=t._defaultScaleValues();n.extend(u,r);n.extend(r,u)}):this.model.scales=[this._defaultScaleValues()]},_checkArrayObject:function(t,i){var r=this,u;return n.each(t,function(n,t){if(u=typeof n,(u!="string"||u=="string"&&n.indexOf("_")==-1&&n.indexOf("__")==-1)&&typeof t!="function")if(t instanceof Array)r._checkArrayObject(t,n);else if(t!=null&&typeof t=="object"&&!t.setter&&!t.factory&&!t.key){var f=r._defaultScaleValues();r._LoadIndividualDefaultValues(t,f,typeof n=="number"?i:n)}}),t},_LoadIndividualDefaultValues:function(t,i,r){var u=null,e=this,f;return n.each(i,function(n,t){if(r==n){u=t;return}}),u instanceof Array&&(u=u[0]),f=typeof r,n.each(t,function(n,t){t instanceof Array?e._checkArrayObject(t,r):t!=null&&typeof t=="object"&&(f!="string"||f=="string"&&r.indexOf("_")==-1&&r.indexOf("__")==-1)&&e._LoadIndividualDefaultValues(t,t,typeof n=="number"?r:n)}),n.extend(u,t),n.extend(t,u),t},initialize:function(){this._initObject(this);this.model.frame.backgroundImageUrl?this._drawCustomImage(this,this.model.frame.backgroundImageUrl):this.model.scales!=null&&this._drawScales()},_initObject:function(i){var w,v,l,p,s,h,o,it,g,nt,a,e,c,tt,rt,y;for(this._savedPoints=[],this.element.addClass("e-widget"),i.GaugeEl=this.element,i.canvasEl?(i.canvasEl.remove(),i.GaugeEl.empty()):i.canvasEl=n("<canvas><\/canvas>"),w=0,e=0;this.model.scales[e]!=null;e++){for(this.model.scales[e].minimum==null&&(this.model.scales[e].minimum=this.minimum()),this.model.scales[e].maximum==null&&(this.model.scales[e].maximum=this.maximum()),v=0;this.model.scales[e].pointers[v]!=null;v++)this.model.scales[e].pointers[v].value==null&&(this.model.scales[e].pointers[v].value=this.value());for(l=0;this.model.scales[e].customLabels[l]!=null;l++)if(this.model.scales[e].customLabels[l].value!=null&&(w++,i.GaugeEl.find("div").length==0))if(this.model.scales[e].customLabels[l]!=null&&this.model.scales[e].customLabels[l].positionType!=null&&this.model.scales[e].customLabels[l].positionType=="outer")if(i.outerDiv=t.buildTag("div"),i.model.outerCustomLabelPosition=="bottom")i.GaugeEl.append(i.canvasEl),i.GaugeEl.append(i.outerDiv),i.outerDiv.css("text-align","center");else if(i.model.outerCustomLabelPosition!="top"){p=t.buildTag("TABLE");p.css("width","100%");var b=t.buildTag("TR"),k=t.buildTag("TD"),d=t.buildTag("td");i.model.outerCustomLabelPosition=="left"?(k.append(i.outerDiv),d.append(i.canvasEl)):(k.append(i.canvasEl),d.append(i.outerDiv));b.append(k);b.append(d);p.append(b);i.GaugeEl.append(p);i.outerDiv.css({width:this.element.width()-i.model.width})}else i.GaugeEl.append(i.outerDiv),i.GaugeEl.append(i.canvasEl),i.outerDiv.css("text-align","center");else i.GaugeEl.append(i.canvasEl);w==0&&i.GaugeEl.append(i.canvasEl)}if(i.canvasEl.attr("role","presentation"),r==f&&(u=window.innerWidth),i.canvasEl[0].setAttribute("width",i.model.width),i.canvasEl[0].setAttribute("height",i.model.height),i.centerX=i.canvasEl[0].width/2,i.centerY=i.canvasEl[0].height/2,s=20,a=this.model.gaugePosition.toLowerCase(),this.model.legend.visible){if(this.model.legend.font.color=this.model.theme=="flatdark"?t.util.isNullOrUndefined(this.model.legend.font.color)?"#8c8c8c":this.model.legend.font.color:t.util.isNullOrUndefined(this.model.legend.font.color)?"#282828":this.model.legend.font.color,it=this.model.legend.position.toLowerCase(),o=this._calculateLegendBounds(),o.Height=t.util.isNullOrUndefined(this.model.legend.size._height)?o.Height:parseInt(this.model.legend.size.height),o.Width=t.util.isNullOrUndefined(this.model.legend.size._width)?o.Width:parseInt(this.model.legend.size.width),this.model.legendActualBounds=o,o.Height>0&&o.Width>0){switch(it){case"top":g=i.model.height-(o.Height+s);i.centerY=o.Height+s+g/2;h=g/2-s;break;case"bottom":i.centerY=(i.model.height-(o.Height+s))/2;h=i.centerY-s;break;case"left":nt=i.model.width-(o.Width+s);i.centerX=o.Width+s+nt/2;h=nt/2-s*2;break;case"right":i.centerX=(i.model.width-(o.Width+s))/2;h=i.centerX-s*2}for(e=0;e<this.model.scales.length;e++)c=this.model.scales[e],tt=0,this._scaleRedrawn||(c._radius=c.radius),n.each(c.ranges,function(n,t){tt+=t.size}),rt=tt/c.ranges.length,h=c.radius=c._radius<h?c._radius:h,h=Math.abs((h-rt)/(e+1))}}else n.each(this.model.scales,function(n,i){i.radius=t.util.isNullOrUndefined(i._radius)?i.radius:i._radius});if(this._isHalfCircle&&this._isHalfCircle())if(i.model.frame.halfCircleFrameEndAngle-i.model.frame.halfCircleFrameStartAngle>=180){if(i.model.frame.halfCircleFrameStartAngle==0)switch(a){case"center":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.width/2;break;case"topleft":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"topright":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.width-(i.model.radius+i.model.distanceFromCorner);break;case"topcenter":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.width/2;break;case"middleleft":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"middleright":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.width-(i.model.radius+i.model.distanceFromCorner);break;case"bottomleft":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"bottomright":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.width-(i.model.radius+i.model.distanceFromCorner);break;case"bottomcenter":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.width/2}else if(i.model.frame.halfCircleFrameStartAngle==90)switch(a){case"center":i.centerY=i.model.height/2;i.centerX=i.model.width/2+i.model.radius/2;break;case"topleft":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.distanceFromCorner+i.model.radius;break;case"topright":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width-i.model.distanceFromCorner;break;case"topcenter":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width/2+i.model.radius/2;break;case"middleleft":i.centerY=i.model.height/2;i.centerX=i.model.distanceFromCorner+i.model.radius;break;case"middleright":i.centerY=i.model.height/2;i.centerX=i.model.width-i.model.distanceFromCorner;break;case"bottomleft":i.centerY=i.model.height-(i.model.distanceFromCorner+i.model.radius);i.centerX=i.model.distanceFromCorner+i.model.radius;break;case"bottomright":i.centerY=i.model.height-(i.model.distanceFromCorner+i.model.radius);i.centerX=i.model.width-i.model.distanceFromCorner;break;case"bottomcenter":i.centerY=i.model.height-(i.model.distanceFromCorner+i.model.radius);i.centerX=i.model.width/2+i.model.radius/2}else if(i.model.frame.halfCircleFrameStartAngle==180)switch(a){case"center":i.centerY=i.model.height/2+i.model.radius/2;i.centerX=i.model.width/2;break;case"topleft":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.distanceFromCorner+i.model.radius;break;case"topright":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"topcenter":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width/2;break;case"middleleft":i.centerY=i.model.height/2+i.model.radius/2;i.centerX=i.model.distanceFromCorner+i.model.radius;break;case"middleright":i.centerY=i.model.height/2+i.model.radius/2;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"bottomleft":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.distanceFromCorner+i.model.radius;break;case"bottomright":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"bottomcenter":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.width/2}}else if(i.model.frame.halfCircleFrameStartAngle==270&&i.model.frame.halfCircleFrameEndAngle==90)switch(a){case"center":i.centerY=i.model.height/2;i.centerX=i.model.width/2-i.model.radius/2;break;case"topleft":i.centerY=i.model.radius+i.model.distanceFromCorner;i.centerX=i.model.distanceFromCorner;break;case"topright":i.centerY=i.model.radius+i.model.distanceFromCorner;i.centerX=i.model.width-(i.model.radius+i.model.distanceFromCorner);break;case"topcenter":i.centerY=i.model.radius+i.model.distanceFromCorner;i.centerX=i.model.width/2-i.model.radius/2;break;case"middleleft":i.centerY=i.model.height/2;i.centerX=i.model.distanceFromCorner;break;case"middleright":i.centerY=i.model.height/2;i.centerX=i.model.width-(i.model.radius+i.model.distanceFromCorner);break;case"bottomleft":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.distanceFromCorner;break;case"bottomright":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.width-(i.model.radius+i.model.distanceFromCorner);break;case"bottomcenter":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.width/2-i.model.radius/2}else if(i.model.frame.halfCircleFrameEndAngle-i.model.frame.halfCircleFrameStartAngle<=90)if(i.model.frame.halfCircleFrameStartAngle==0)switch(i.model.gaugePosition){case"center":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.width/2-i.model.radius/2;break;case"topleft":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.distanceFromCorner;break;case"topright":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"topcenter":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.width/2-i.model.radius/2;break;case"middleleft":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.distanceFromCorner;break;case"middleright":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"bottomleft":i.centerY=i.model.height-(i.model.distanceFromCorner+i.model.radius);i.centerX=i.model.distanceFromCorner;break;case"bottomright":i.centerY=i.model.height-(i.model.distanceFromCorner+i.model.radius);i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"bottomcenter":i.centerY=i.model.height-(i.model.distanceFromCorner+i.model.radius);i.centerX=i.model.width/2-i.model.radius/2}else if(i.model.frame.halfCircleFrameStartAngle==90)switch(a){case"center":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.width/2+i.model.radius/2;break;case"topleft":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"topright":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.width-i.model.distanceFromCorner;break;case"topcenter":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.width/2+i.model.radius/2;break;case"middleleft":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"middleright":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.width-i.model.distanceFromCorner;break;case"bottomleft":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"bottomright":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.width-i.model.distanceFromCorner;break;case"bottomcenter":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.width/2+i.model.radius/2}else if(i.model.frame.halfCircleFrameStartAngle==180)switch(i.model.gaugePosition){case"center":i.centerY=i.model.height/2+i.model.radius/2;i.centerX=i.model.width/2+i.model.radius/2;break;case"topleft":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"topright":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width-i.model.distanceFromCorner;break;case"topcenter":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width/2+i.model.radius/2;break;case"middleleft":i.centerY=i.model.height/2+i.model.radius/2;i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"middleright":i.centerY=i.model.height/2+i.model.radius/2;i.centerX=i.model.width-i.model.distanceFromCorner;break;case"bottomleft":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"bottomright":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.width-i.model.distanceFromCorner;break;case"bottomcenter":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.width/2+i.model.radius/2}else if(i.model.frame.halfCircleFrameStartAngle==270)switch(i.model.gaugePosition){case"center":i.centerY=i.model.radius/2+i.model.height/2;i.centerX=i.model.width/2-i.model.radius/2;break;case"topleft":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.distanceFromCorner;break;case"topright":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"topcenter":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width/2-i.model.radius/2;break;case"middleleft":i.centerY=i.model.height/2+i.model.radius/2;i.centerX=i.model.distanceFromCorner;break;case"middleright":i.centerY=i.model.radius/2+i.model.height/2;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"bottomleft":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.distanceFromCorner;break;case"bottomright":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"bottomcenter":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.width/2-i.model.radius/2}(y=i.canvasEl[0],typeof G_vmlCanvasManager!="undefined"&&(y=window.G_vmlCanvasManager.initElement(y)),y&&y.getContext)&&(i.contextEl=i.canvasEl[0].getContext("2d"))},_browserInfo:function(){var n={},t=[],i={webkit:/(chrome)[ \/]([\w.]+)/i,safari:/(webkit)[ \/]([\w.]+)/i,msie:/(msie) ([\w.]+)/i,opera:/(opera)(?:.*version|)[ \/]([\w.]+)/i,mozilla:/(mozilla)(?:.*? rv:([\w.]+)|)/i};for(var r in i)if(i.hasOwnProperty(r)&&(t=navigator.userAgent.match(i[r]),t)){n.name=t[1].toLowerCase();n.version=t[2];!navigator.userAgent.match(/Trident\/7\./)||(n.name="msie");break}return n.isMSPointerEnabled=n.name=="msie"&&n.version>9&&window.navigator.msPointerEnabled,n.pointerEnabled=window.navigator.pointerEnabled,n},_wireEvents:function(){var f=jQuery.uaMatch(navigator.userAgent),r=this._browserInfo(),i=r.isMSPointerEnabled,t=r.pointerEnabled,u;this.startEv=i?t?"pointerdown":"MSPointerDown":"touchstart mousedown";this.endEv=i?t?"pointerup":"MSPointerUp":"touchend mouseup";this.moveEv=i?t?"pointermove":"MSPointerMove":"touchmove mousemove";this.leaveEv=i?t?"pointerleave":"MSPointerOut":"touchleave mouseleave";this.scrollEv=f.browser.toLowerCase()=="mozilla"?t?"mousewheel":"DOMMouseScroll":"mousewheel";this.model.browserInfo=r;u=this.model.readOnly?"pan-y pan-x":"none";n(this.element).css("touch-action",u);this.onMouseMoveHandler=n.proxy(this._onMouseMove,this);this.onMouseUpHandler=n.proxy(this._onMouseUp,this);this.onMouseDownHandler=n.proxy(this._onMouseDown,this);this.onHoverOCustomLabel=n.proxy(this._onHoverOCustomLabel,this);this.onLeaveOCustomLabel=n.proxy(this._onLeaveOCustomLabel,this);this.model.legend.visible&&this._on(n(this.canvasEl),"click",this.legendMouseClick);this._on(n(this.canvasEl),"mousemove",this.rangesMouseMove);this._on(n(this.canvasEl),this.startEv,this.rangeMouseDown);this._on(n(this.canvasEl),this.endEv,this.rangeMouseUp);this._on(n(this.canvasEl),"contextmenu",this._cgRightClick);(this.model.tooltip.showCustomLabelTooltip||this.model.tooltip.showLabelTooltip)&&(n(this.canvasEl).bind(this.moveEv,this.onMouseMoveHandler),n(this.canvasEl).bind(this.scrollEv,this.onMouseMoveHandler),n(this.canvasEl).bind(this.startEv,this.onMouseDownHandler),n(this.canvasEl).bind(this.endEv,this.onLeaveOCustomLabel),n(this.canvasEl).bind(this.leaveEv,this.onLeaveOCustomLabel));this.model.readOnly||n(this.canvasEl).bind(this.startEv,this.onMouseDownHandler);this.model.tooltip.showCustomLabelTooltip&&(n("."+this._id+"outercustomlbl").bind("mouseenter",this.onHoverOCustomLabel),n("."+this._id+"outercustomlbl").bind("this.leaveEv",this.onLeaveOCustomLabel))},_unWireEvents:function(){this._off(n(this.canvasEl),this.startEv,this.rangeMouseDown);this._off(n(this.canvasEl),this.endEv,this.rangeMouseUp);this._off(n(this.canvasEl),"contextmenu",this._cgRightClick);n(this.canvasEl).unbind(this.startEv,this.onMouseDownHandler)},rangeMouseDown:function(){t.isTouchDevice()&&this.model.rightClick!=""&&(this._longPressTimer=new Date)},rangeMouseUp:function(n){var i=new Date;this._doubleTapTimer!=null&&i-this._doubleTapTimer<300&&this._cgDoubleClick(n);this._doubleTapTimer=i;t.isTouchDevice()&&this.model.rightClick!=""&&i-this._longPressTimer>1500&&this._cgRightClick()},_onHoverOCustomLabel:function(n){(n.currentTarget.innerHTML!=null||n.currentTarget.innerHTML!="")&&this._showTooltip(n,n.currentTarget.innerHTML)},_onLeaveOCustomLabel:function(t){this.isTouch(t)?(this._performTooltip(t),window.clearTimeout(this.model.timer),this.model.timer=setTimeout(function(){n(".tooltipDiv").fadeOut(500)},1200)):this._hideTooltip()},isTouch:function(n){var t=n.originalEvent?n.originalEvent:n;return t.pointerType=="touch"||t.pointerType==2||t.type.indexOf("touch")>-1?!0:!1},_showTooltip:function(t,i){var s=this.model.locale,o=s&&this.model.enableGroupSeparator?i.toLocaleString(s):i.toString(),r=n(".tooltipDiv"),h,e;r.length==0&&(r=n("<div class='tooltipDiv' style='pointer-events:none; position: absolute; z-index: 105; display: block;'><\/div>"),n(document.body).append(r));this.model.tooltip.templateID!=""&&this.model.tooltip.templateID!=null?(h=n("#"+this.model.tooltip.templateID).clone(),n(".tooltipDiv")[0].innerHTML="",n(h).css("display","block").appendTo(r),n(r).css({"pointer-events":"none","background-color":this.model.backgroundColor,border:"1px solid #bbbcbb","border-radius":"3px",color:"#565656"}),r.html(r.html().replace("#label#",o))):(n(r).html(o),n(r).css({"pointer-events":"none","background-color":"white",border:"2px solid #bbbcbb",position:"absolute",padding:"10px 20px","margin-top":"5px","text-align":"left",font:"12px Segoe UI","font-stretch":"condensed",display:"inline-block","border-radius":"3px",color:"#565656",width:"auto"}));var c=10,u=t.pageX+c,f=t.pageY+c;u=u+n(r).width()<this.model.width?u:u-n(r).width();f=f+n(r).height()<this.model.height?f:f-n(r).height();n(r).css("left",u);n(r).css("top",f);n(".tooltipDiv").show();e={type:"tooltipRendering",cancel:!1,text:o,model:this.model,location:{x:u,y:f},target:r,event:t};this._trigger("tooltipRendering",e);e.cancel||e.text==o||(r[0].innerText=e.text)},_hideTooltip:function(){n(".tooltipDiv").remove()},_onMouseDown:function(t){this._blockDefaultActions(t);this._mouseDown=!0;var s=this.isTouch(t)?10:0,o=t.originalEvent.touches?t.originalEvent.touches[0]:t,u={x:this.centerX,y:this.centerY},f={x:o.pageX-n(this.canvasEl).offset().left,y:o.pageY-n(this.canvasEl).offset().top},h=180*this._getCirucumferenceAngle(u,f)/Math.PI,r,e,i=this;this.model.readOnly||n.each(this.model.scales,function(t,o){i.scaleIndex=t;o.pointers!=null&&(i.pointerEl=o.pointers,r=o.radius,e=o.ticks[0].height,n.each(o.pointers,function(t,c){var l;i._isHalfCircle()&&(o.showBackNeedle=!1);l=i._getAngle(c.value);l>360&&(l=l-360);var v=l+c.width,y=l-c.width,a=Math.sqrt((f.x-u.x)*(f.x-u.x)+(f.y-u.y)*(f.y-u.y)),p=a<(c.placement=="far"?r+c.width+c.distanceFromScale:c.placement=="center"?r-c.distanceFromScale:r-15-e-c.distanceFromScale)&&a>(c.placement=="far"?r+c.distanceFromScale:c.placement=="center"?r-c.width-c.distanceFromScale:r-c.width-15-e-c.distanceFromScale),w=c.type=="needle"?a<=c.length:p;i._isBetween(y,v,h,s)&&w&&(i._onMouseClick(l,o.value),i.activeElement=c,n(document).bind(i.moveEv,i.onMouseMoveHandler),n(document).bind(i.endEv,i.onMouseUpHandler))}))})},_onMouseUp:function(){this._mouseDown=!1;n(document).unbind(self.moveEv,self.onMouseMoveHandler);n(document).unbind(self.endEv,self.onMouseUpHandler);this.activeElement&&this._onMouseClickUp(this._getAngle(this.activeElement.value),this.activeElement.value);this.activeElement=null},_mousePosition:function(n){if(!t.util.isNullOrUndefined(n.pageX)&&n.pageX>0)return{x:n.pageX,y:n.pageY};if(n.originalEvent&&!t.util.isNullOrUndefined(n.originalEvent.pageX)&&n.originalEvent.pageX>0)return{x:n.originalEvent.pageX,y:n.originalEvent.pageY};if(n.originalEvent&&n.originalEvent.changedTouches!=i){if(!t.util.isNullOrUndefined(n.originalEvent.changedTouches[0].pageX)&&n.originalEvent.changedTouches[0].pageX>0)return{x:n.originalEvent.changedTouches[0].pageX,y:n.originalEvent.changedTouches[0].pageY}}else return{x:0,y:0}},_calTouchPosition:function(n){var i=jQuery.uaMatch(navigator.userAgent),t=this._mousePosition(n);n.pageX=t.x;n.pageY=t.y},getEvent:function(n){return n.targetTouches&&n.targetTouches[0]?n.targetTouches[0]:n},_onMouseMove:function(i){if(this._mouseDown&&!t.isNullOrUndefined(this.activeElement)){this._blockDefaultActions(i);var f={x:this.centerX,y:this.centerY},u=i.originalEvent.touches?i.originalEvent.touches[0]:i,e={x:u.pageX-n(this.canvasEl).offset().left,y:u.pageY-n(this.canvasEl).offset().top},r=180*this._getCirucumferenceAngle(f,e)/Math.PI;r<this.scaleEl[this.scaleIndex].startAngle&&!this._isHalfCircle()&&(r=r+360);this._onMouseClickMove(this._getAngle(this.activeElement.value),this.activeElement.value);this._getValue(r)<=this.scaleEl[this.scaleIndex].maximum&&(this.activeElement.value=this.scaleEl[this.scaleIndex].direction=="clockwise"?this._getValue(r):this.scaleEl[this.scaleIndex].maximum-this._getValue(r));this.contextEl.putImageData?this._reDrawPointer():this._init()}else(this.model.tooltip.showCustomLabelTooltip||this.model.tooltip.showLabelTooltip)&&!this.isTouch(i)&&this._performTooltip(i)},_performTooltip:function(t){for(var r,u=!1,f=10,o=this.isTouch(t),i=0;this._savedPoints[i]!=null;i++)if(o){var c=this._calTouchPosition(t),e=this.getEvent(t),s=e.pageX,h=e.pageY,r={X:s-n(this.canvasEl).offset().left,Y:h-n(this.canvasEl).offset().top};r.X>this._savedPoints[i].startX-f&&r.X<this._savedPoints[i].startX+this._savedPoints[i].width+f&&r.Y>this._savedPoints[i].startY-f&&r.Y<this._savedPoints[i].startY+this._savedPoints[i].height+f?(this._showTooltip(t,this._savedPoints[i].value),u=!0):u==!1&&this._hideTooltip()}else r={X:t.pageX-n(this.canvasEl).offset().left,Y:t.pageY-n(this.canvasEl).offset().top},r.X>this._savedPoints[i].startX&&r.X<this._savedPoints[i].startX+this._savedPoints[i].width&&r.Y>this._savedPoints[i].startY&&r.Y<this._savedPoints[i].startY+this._savedPoints[i].height?(this._showTooltip(t,this._savedPoints[i].value),u=!0):u==!1&&this._hideTooltip()},_isHalfCircle:function(){return this.model.frame.frameType.toLowerCase()=="halfcircle"?!0:!1},_calFontLength:function(t){var i=this.model.scales[0].minimum+this.model.scales[0].labels[0].unitText,r=this.model.scales[0].maximum+this.model.scales[0].labels[0].unitText,u=r.length>i.length?r:i,f=n('<span id="test"><\/span>').css({font:t,display:"none",whiteSpace:"nowrap"}).appendTo(n("body")).text(u).width();return n("#test").remove(),f/2},_getHalfCircleYPosition:function(){return this._getYCordinate(this.centerY,0,(this.model.frame.halfCircleFrameStartAngle+this.model.frame.halfCircleFrameEndAngle)/2)},_getHalfCircleXPosition:function(){return this._getXCordinate(this.centerX,0,(this.model.frame.halfCircleFrameStartAngle+this.model.frame.halfCircleFrameEndAngle)/2)},_getXCordinate:function(n,t,i){return n+t*Math.cos(Math.PI*(i/180))},_getYCordinate:function(n,t,i){return n+t*Math.sin(Math.PI*(i/180))},_getAngle:function(n){var t;return t=n>=this.scaleEl[this.scaleIndex].minimum&&n<=this.scaleEl[this.scaleIndex].maximum?this.scaleEl[this.scaleIndex].direction=="clockwise"?n-this.scaleEl[this.scaleIndex].minimum:this.scaleEl[this.scaleIndex].maximum-n:this.scaleEl[this.scaleIndex].direction=="clockwise"?n<=this.scaleEl[this.scaleIndex].minimum?this.scaleEl[this.scaleIndex].minimum:this.scaleEl[this.scaleIndex].maximum:n<=this.scaleEl[this.scaleIndex].minimum?this.scaleEl[this.scaleIndex].maximum:this.scaleEl[this.scaleIndex].minimum,t*(this.scaleEl[this.scaleIndex].sweepAngle/(this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum))+this.scaleEl[this.scaleIndex].startAngle},_subtractDecimal:function(n,t){var r=n.toString(),u=t.toString(),f,e,i,o;return f=r.indexOf(".")>-1?r.length-r.indexOf(".")-1:0,e=u.indexOf(".")>-1?u.length-u.indexOf(".")-1:0,i=f>e?f:e,o=(n*Math.pow(10,i)-t*Math.pow(10,i))/Math.pow(10,i),o},_getCirucumferenceAngle:function(n,t){return t.x>n.x?t.y>n.y?this._tangent(n,t):t.y==n.y?0:2*Math.PI+this._tangent(n,t):t.x==n.x?t.y==n.y?0:t.y>n.y?Math.PI/2:1.5*Math.PI:t.y==n.y?Math.PI:t.y>n.y?Math.PI+this._tangent(n,t):Math.PI+this._tangent(n,t)},_calcDistanceFactor:function(n,t,i){var r;return r=n>240&&n<=300||n>60&&n<=120?0:n>330&&n<=360||n>=0&&n<=30?-i*.5:n>30&&n<=60||n>300&&n<=330?-i/2:n>150&&n<=210?i*.5:i/2,t=="far"?-r:r},_tangent:function(n,t){var i=(t.y-n.y)/(t.x-n.x);return Math.atan(i)},_getValue:function(n){return this.scaleEl[this.scaleIndex].direction=="counterclockwise"?(n-this.scaleEl[this.scaleIndex].startAngle)/this.scaleEl[this.scaleIndex].sweepAngle*(this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum):(n-this.scaleEl[this.scaleIndex].startAngle)/this.scaleEl[this.scaleIndex].sweepAngle*(this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum)+this.scaleEl[this.scaleIndex].minimum},_drawScales:function(){var r=this,u,i,f,t;if(this.scaleEl=this.model.scales,n.each(this.model.scales,function(n,t){r.scaleIndex=n;t.showScaleBar&&r._setScaleCordinates(t)}),this.model.rangeZOrder=="rear"?(this._setRanges(),this._setTicks()):(this._setTicks(),this._setRanges()),this._setLabels(),this._subGauge(),this._setCustomLabel(),this._setPointers(),this.contextEl.putImageData||(this.model.enableAnimation=!1),this.model.animationSpeed!=null&&this.model.animationSpeed>0&&(u=this.model.animationSpeed/25,u>=0&&(i=navigator.userAgent.toLowerCase(),f=i.indexOf("msie")!=-1?parseInt(i.split("msie")[1]):0,this.model.enableAnimation&&f!=9))){for(t=0;t<this.model.scales[0].pointers.length;t++)this.pointerValue=[],this.currentValue=[],this.pointerValue[t]=null,this.currentValue[t]=null,this.updatePointerOnAnimation=!1;this.dt=(new Date).getTime();this._onAnimate(this)}this._setIndicators()},_setTicks:function(){var t=this;n.each(this.model.scales,function(i,r){r.showTicks&&(t.scaleIndex=i,r.ticks!=null&&(t.tickEl=r.ticks,n.each(r.ticks,function(n,i){t.tickIndex=n;t._setTicksCordinates(n,i)})))})},_setLabels:function(){var t=this;n.each(this.model.scales,function(i,r){r.showLabels&&(t.scaleIndex=i,r.labels!=null&&(t.labelEl=r.labels,n.each(r.labels,function(n,i){t.labelIndex=n;t._setLabelCoridnates(n,i)})))})},_setIndicators:function(){var t=this;n.each(this.model.scales,function(i,r){r.showIndicators&&(t.scaleIndex=i,r.indicators!=null&&(t.indicatorEl=r.indicators,n.each(r.indicators,function(n,i){t.indicatorIndex=n;t._drawIndicator(n,i)})))})},_setPointers:function(){var t=this;n.each(this.model.scales,function(i,r){r.showPointers&&(t.scaleIndex=i,r.pointers!=null&&(t.pointerEl=r.pointers,n.each(r.pointers,function(n,i){t._isHalfCircle()&&(r.showBackNeedle=!1);t.pointerIndex=n;t._drawPointers(n,i)})))})},_onAnimate:function(n){if(!this._androidAnimation&&!t.util.isNullOrUndefined(n.model)){for(var o=n,i=o,l=i.model.animationSpeed/25,u,f,s,h,c,a=n.model.scales[0].pointers.length,e=[],r=0;r<a;r++)e[r]=!0,i.model.scales[0].pointers[r].value!=i.model.scales[0].pointers[r]._value&&(i.model.scales[0].pointers[r]._value=i.model.scales[0].pointers[r].value),i.pointerValue[r]=t.util.isNullOrUndefined(i.pointerValue[r])?i.model.scales[0].pointers[r]._value:i.pointerValue[r],i.currentValue[r]=t.util.isNullOrUndefined(i.currentValue[r])?i.model.scales[0].minimum:i.currentValue[r],u=i.pointerValue[r],s=(new Date).getTime(),h=s-i.dt,h>l&&u>i.currentValue[r]?(i.currentValue[r]=i.currentValue[r]+(i.model.scales[0].maximum-i.model.scales[0].minimum)/100,i.updatePointerOnAnimation=!0,u>i.currentValue[r]?i.setPointerValue(0,r,i.currentValue[r]):i.setPointerValue(0,r,u),i.dt=s-h%l):i.currentValue>=u&&(e[r]=!1);for(f=0;f<e.length;f++)if(c=e[f],c)break;c&&requestAnimationFrame(function(){o._onAnimate(o)})}},_pointInterval:function(n,t,i,r){this.timer=setTimeout(function(){t>n?(n=n+(r.model.scales[0].maximum-r.model.scales[0].minimum)/100,t>n?r.setPointerValue(0,0,n):r.setPointerValue(0,0,t)):n>=t&&window.clearInterval(r.timer);r._pointInterval(n,t,i,r)},i)},_setRanges:function(){var t=this;this._rangeCollectionRegions=[];n.each(this.model.scales,function(i,r){r.showRanges&&(t.scaleIndex=i,r.ranges!=null&&(t.rangeEl=r.ranges,n.each(r.ranges,function(n,i){t.rangeIndex=n;(i._visible||!t.model.legend.visible)&&t._setRangeCordinates(n,i)})))})},_setCustomLabel:function(){var t=this,i;n.each(this.model.scales,function(r,u){t.scaleIndex=r;u.customLabels!=null&&(t.customLabelEl=u.customLabels,n.each(u.customLabels,function(n,r){t.customLabelIndex=n;i=t.model.scales[t.scaleIndex].customLabels[t.customLabelIndex];i.value!=null&&(i.positionType=="outer"?t._setOuterCustomLabels(n,r):t._setCustomLabelCordinates(n,r))}))})},_subGauge:function(){var t=this;n.each(this.model.scales,function(i,r){t.scaleIndex=i;r.subGauges!=null&&(t.subGaugeEl=r.subGauges,n.each(r.subGauges,function(n,r){t.subGaugeIndex=i;t._setSubGauge(n,r)}))})},_setOuterCustomLabels:function(n,i){var r,u;this._customLblMaxSize=this._customLblMaxSize<parseFloat(i.font.size.match(/\d+/)[0])?parseFloat(i.font.size.match(/\d+/)[0]):this._customLblMaxSize;r=t.buildTag("div."+this._id+"outercustomlbl");r.text(this.model.scales[this.scaleIndex].customLabels[n].value);u=this.model.outerCustomLabelPosition=="right"||this.model.outerCustomLabelPosition=="left"?"left":"center";this.outerDiv.append(r);this.outerDiv.append("<\/br>");u=="center"?r.css({display:"inline-block",margin:"0 auto","max-width":this.model.width}):r.css({display:"inline-block","max-width":this.element.width()-this.model.width>10?this.element.width()-this.model.width:10});r.css({color:i.color!=null?i.color:"black",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap","font-size":i.font!=null&&i.font.size!=null?i.font.size:"12px","font-family":i.font!=null&&i.font.fontFamily!=null?i.font.fontFamily:"Arial","font-weight":i.font!=null&&i.font.fontStyle!=null?i.font.fontStyle:"Normal","text-align":u})},_setScaleCordinates:function(n){if(this.region={centerX:this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,centerY:this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,startAngle:n.startAngle,endAngle:n.startAngle+n.sweepAngle,startRadius:this.scaleEl[this.scaleIndex].radius-this.scaleEl[this.scaleIndex].size/2,endRadius:this.scaleEl[this.scaleIndex].radius+this.scaleEl[this.scaleIndex].size/2},this.style={radius:n.radius-n.size/2,strokeStyle:n.border.color=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.border.color),lineWidth:n.border.width,size:n.size,isFill:!0,opacity:isNaN(n.opacity)?1:n.opacity,isStroke:!0,shadowOffset:n.shadowOffset,fillStyle:n.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.backgroundColor),counterClockwise:n.direction=="clockwise"?!1:!0},n.maximum<n.minimum){var i=n.maximum;n.maximum=n.minimum;n.minimum=i}n.maximum==n.minimum&&(n.maximum=n.maximum+1);this.maximum(n.maximum);this.minimum(n.minimum);this._notifyArrayChange&&(this._notifyArrayChange("scales["+this.scaleIndex+"]maximum",n.maximum),this._notifyArrayChange("scales["+this.scaleIndex+"]minimum",n.minimum));this._drawScaleBar(this.region,this.style);this.contextEl.getImageData&&(this.scaleImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height))},_drawOuterLayer:function(){var n,i,r;this._isHalfCircle()&&(i={x:this.centerX,y:this.centerY},r={x:this.centerX+this.model.radius,y:this.centerY/2});n=this.model.isRadialGradient?this.contextEl.createRadialGradient(this.canvasEl[0].width/2,this.canvasEl[0].height/2,0,this.canvasEl[0].width/2,this.canvasEl[0].height/2,this.model.radius):this.contextEl.createLinearGradient(0,0,0,this.canvasEl[0].height);t.isNullOrUndefined(this.model.interiorGradient)||this._setGradientColor(this,n,this.model.interiorGradient.colorInfo);this.frameOuterLocation={centerX:this.centerX,hcCenterX:this.centerX,hcCenterY:this.centerY,centerY:this.centerY,startAngle:this._isHalfCircle()?Math.PI*(this.model.frame.halfCircleFrameStartAngle/180):0,endAngle:this._isHalfCircle()?Math.PI*(this.model.frame.halfCircleFrameEndAngle/180):2*Math.PI};this._isHalfCircle()&&(this.frameInnerLocation={centerX:this.centerX,hcCenterX:this._getXCordinate(this.centerX,0,(this.model.frame.halfCircleFrameStartAngle+this.model.frame.halfCircleFrameEndAngle)/2),hcCenterY:this._getYCordinate(this.centerX,0,(this.model.frame.halfCircleFrameStartAngle+this.model.frame.halfCircleFrameEndAngle)/2),centerY:this.centerY,startAngle:Math.PI*(this.model.frame.halfCircleFrameStartAngle/180),endAngle:Math.PI*(this.model.frame.halfCircleFrameEndAngle/180)});this.frameInnerStyle={radius:this.model.radius,isStroke:!1,isFill:!0,fillStyle:this.model.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(null,this.model.backgroundColor),counterClockwise:!1};this.model.frame.frameType.toLowerCase()=="fullcircle"?this._drawCircleFrame(this.frameOuterLocation,this.frameInnerStyle):this.model.frame.frameType.toLowerCase()=="halfcircle"&&this._drawHalfCircle(this.frameInnerLocation,this.frameInnerStyle);this.contextEl.getImageData&&(this.outerImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height))},_setTicksCordinates:function(n,i){var o,s,h,c,e,u,r,f;for(i.type=="major"?(e=this.scaleEl[this.scaleIndex].majorIntervalValue,this.majorTickHeight=i.height):e=this.scaleEl[this.scaleIndex].minorIntervalValue,i.placement=="far"&&(u=this.scaleEl[this.scaleIndex].radius+this.scaleEl[this.scaleIndex].size/2+this.scaleEl[this.scaleIndex].border.width/2+i.distanceFromScale),i.placement=="center"&&(u=this.scaleEl[this.scaleIndex].radius-i.height/2-this.scaleEl[this.scaleIndex].border.width/2-i.distanceFromScale),i.placement=="near"&&(u=this.scaleEl[this.scaleIndex].radius-this.scaleEl[this.scaleIndex].size/2-this.scaleEl[this.scaleIndex].border.width/2-i.distanceFromScale),h=i.placement=="near"?-i.height:i.height,f=this.scaleEl[this.scaleIndex].maximum;f>=this.scaleEl[this.scaleIndex].minimum;f-=e)(e==this.scaleEl[this.scaleIndex].minorIntervalValue&&f%this.scaleEl[this.scaleIndex].majorIntervalValue!=0||e==this.scaleEl[this.scaleIndex].majorIntervalValue)&&(f==this.scaleEl[this.scaleIndex].minimum&&(c=!0),r=this._getAngle(f),r=r>360?r-360:r,o=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,u,r),s=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,u,r),this.region={startX:o,startY:s},this.style={angle:i.angle+r,isStroke:!0,isFill:!1,lineHeight:h,lineWidth:i.width,strokeStyle:i.color=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.color)},this.model.drawTicks&&this._onDrawTicks(r,f),this._drawTickMark(this.region,this.style));c||(r=this._getAngle(this.scaleEl[this.scaleIndex].minimum),r=r>360?r-360:r,o=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,u,r),s=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,u,r),this.region={startX:o,startY:s},this.style={angle:i.angle+r,isStroke:!0,isFill:!1,lineHeight:h,lineWidth:i.width,strokeStyle:i.color=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.color)});this.contextEl.getImageData&&(this.tickImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height))},_setLabelCoridnates:function(n,i){var h,e,o,c,l,s,f,u,r;for(s=i.type=="major"?this.scaleEl[this.scaleIndex].majorIntervalValue:this.scaleEl[this.scaleIndex].minorIntervalValue,l=i.type=="major"?this.majorIntervalAngle=this.scaleEl[this.scaleIndex].sweepAngle/((this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum)/s):this.scaleEl[this.scaleIndex].sweepAngle/((this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum)/s),i.placement=="far"&&(f=this.scaleEl[this.scaleIndex].radius+this.scaleEl[this.scaleIndex].size/2+this.majorTickHeight+i.distanceFromScale),i.placement=="center"&&(f=this.scaleEl[this.scaleIndex].radius-10-i.distanceFromScale),i.placement=="near"&&(f=this.scaleEl[this.scaleIndex].radius-this.scaleEl[this.scaleIndex].size/2-10-this.majorTickHeight-i.distanceFromScale),this._labelRadius=f,u=this.scaleEl[this.scaleIndex].maximum;u>=this.scaleEl[this.scaleIndex].minimum;u=this._subtractDecimal(u,s))if(u==this.scaleEl[this.scaleIndex].minimum&&(h=!0),s==this.scaleEl[this.scaleIndex].minorIntervalValue&&u%this.scaleEl[this.scaleIndex].majorIntervalValue!=0||s==this.scaleEl[this.scaleIndex].majorIntervalValue){if(u==this.scaleEl[this.scaleIndex].minimum&&!i.includeFirstValue)continue;r=this._getAngle(u);r=r>360?r-360:r;e=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,f,r);o=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,f,r);this.region={startX:e,startY:o};this.style={placement:i.placement,textPositionAngle:r,angle:this.scaleEl[this.scaleIndex].labels[this.labelIndex].autoAngle?r+i.angle:i.angle,isStroke:!1,isFill:!0,textValue:u,opacity:i.opacity?i.opacity:1,font:this._getFontString(i,i.font),fillStyle:i.color=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.color)};this.model.drawLabels&&this._onDrawLabels(this.scaleEl[this.scaleIndex].labels[this.labelIndex].autoAngle?r+i.angle:i.angle,u);this._drawLabel(this.region,this.style,!1);this.model.tooltip.showLabelTooltip&&this._savedPoints.push({startX:e-10,startY:o-5,width:20,height:parseInt(i.font.size.replace(/\D/g,"")),value:u})}h||(r=this._getAngle(this.scaleEl[this.scaleIndex].minimum),r=r>360?r-360:r,e=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,f,r),o=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,f,r),this.region={startX:e,startY:o},this.style={angle:this.scaleEl[this.scaleIndex].labels[this.labelIndex].autoAngle?r+i.angle:i.angle,isStroke:!1,isFill:!0,textValue:this.scaleEl[this.scaleIndex].minimum,opacity:i.opacity?i.opacity:1,font:this._getFontString(i,i.font),lineHeight:c,lineWidth:i.width,fillStyle:i.color=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.color)},this.model.drawLabels&&this._onDrawLabels(r,u),this._drawLabel(this.region,this.style,!1),this.model.tooltip.showLabelTooltip&&this._savedPoints.push({startX:e-10,startY:o-5,width:20,height:parseInt(i.font.size.replace(/\D/g,"")),value:u}),r=this._getAngle(u),r=r>360?r-360:r);this.contextEl.getImageData&&(this.labelImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height))},_setRangeCordinates:function(n,i){if(i.startValue<this.scaleEl[this.scaleIndex].maximum&&i.endValue<=this.scaleEl[this.scaleIndex].maximum){var o=i.startValue>=this.scaleEl[this.scaleIndex].minimum?i.startValue:this.scaleEl[this.scaleIndex].minimum,s=i.endValue>this.scaleEl[this.scaleIndex].maximum?this.scaleEl[this.scaleIndex].maximum:i.endValue,h,c,l,a,r,u,f,v,y,e,p;p=this._getAngle((o+s)/2);r=this._getAngle(o);f=this._getAngle(s);u=this.scaleEl[this.scaleIndex].radius-i.distanceFromScale-this.scaleEl[this.scaleIndex].size/2-i.size-(t.isNullOrUndefined(this.scaleEl[this.scaleIndex].ticks[0])?16:this.scaleEl[this.scaleIndex].ticks[0].height);v=this.scaleEl[this.scaleIndex].radius-i.distanceFromScale-this.scaleEl[this.scaleIndex].size/2-(t.isNullOrUndefined(this.scaleEl[this.scaleIndex].ticks[0])?16:this.scaleEl[this.scaleIndex].ticks[0].height);y=this.scaleEl[this.scaleIndex].radius-i.distanceFromScale-i.size-this.scaleEl[this.scaleIndex].size/2-(t.isNullOrUndefined(this.scaleEl[this.scaleIndex].ticks[0])?16:this.scaleEl[this.scaleIndex].ticks[0].height);h=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,u,r);c=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,u,r);l=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,u,f);a=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,u,f);r=180*this._getCirucumferenceAngle({x:this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,y:this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY},{x:h,y:c})/Math.PI;(i.startValue!=0||i.endValue!=this.scaleEl[this.scaleIndex].maximum)&&(f=180*this._getCirucumferenceAngle({x:this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,y:this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY},{x:l,y:a})/Math.PI);!t.isNullOrUndefined(i.gradients)&&i.gradients.colorInfo.length>0?(e=this.contextEl.createRadialGradient(this.centerX,this.centerY,y,this.centerX,this.centerY,v),this._setGradientColor(this,e,i.gradients.colorInfo)):e=i.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.backgroundColor);this.region={startX:h,startY:c,endX:l,endY:a,startAngle:r,endAngle:f};this.style={placement:i.placement,radius:i.placement=="near"?this.scaleEl[this.scaleIndex].radius-i.distanceFromScale:this.scaleEl[this.scaleIndex].radius+i.distanceFromScale,rangeStart:o,rangeEnd:s,startWidth:i.startWidth,isFill:!0,fillStyle:e,strokeStyle:i.border.color=="transparent"?"rgba(0,0,0,0)":i.border.color,opacity:isNaN(i.opacity)?1:i.opacity,counterClockwise:this.scaleEl[this.scaleIndex].direction=="clockwise"?!1:!0,startRadius:v,endRadius:y,endWidth:i.endWidth,lineWidth:i.border.width,isStroke:!0};this.model.drawRange&&this._onDrawRange(this.region,this.style);this._rangeCollectionRegions.push({Range:i,Region:this.region,scaleIndex:this.scaleIndex,rangeIndex:this.rangeIndex,Radius:{startRadius:this.style.startRadius,endRadius:this.style.endRadius},centerRadius:{centerX:this.centerX,centerY:this.centerY,radius:this.style.radius}});this._drawRange(this.region,this.style);this.contextEl.getImageData&&(this.rangeImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height))}},_setSubGauge:function(i,r){var u=n("div[id="+r.controlID+"]"),f;u.length>0&&u.find("canvas").length&&(f=u.find("canvas")[0].getContext("2d"),this.contextEl.drawImage(f.canvas,r.position.x,r.position.y,r.width,r.height),u.css("display","none"),this.contextEl.getImageData&&(this.subGaugeImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height)))},_setCustomLabelCordinates:function(n,i){this._customLblMaxSize=this._customLblMaxSize<parseFloat(i.font.size.match(/\d+/)[0])?parseFloat(i.font.size.match(/\d+/)[0]):this._customLblMaxSize;i.color=i.color?i.color:"#282828";this.region=i.position?{startX:i.position.x,startY:i.position.y}:{startX:0,startY:0};this.style={angle:i.textAngle,textValue:i.value,fillStyle:i.color=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.color),font:this._getFontString(i,i.font)};this.model.drawCustomLabel&&this._onDrawCustomLabel(this.region,this.style);this._drawLabel(this.region,this.style,!0);this.model.tooltip.showCustomLabelTooltip&&this._savedPoints.push({startX:this.region.startX-30,startY:this.region.startY-5,width:60,height:t.isNullOrUndefined(i.font)?10:parseInt(i.font.size.replace(/\D/g,"")),value:this.style.textValue});this.contextEl.getImageData&&(this.customLabelImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height))},_drawIndicator:function(i,r){var u=this,o=!1,f=r.type.toLowerCase(),e;this.region={centerX:r.position.x-r.width/2,startX:r.position.x,startY:r.position.y,textLocation:r.position,centerY:r.position.y-r.height/2,startAngle:0,endAngle:2*Math.PI};this.style={radius:r.width/2,strokeStyle:"#2BA104",cornerRadius:r.type=="roundedrectangle"?2:0,height:r.height,width:r.width,lineWidth:r.indicatorBorderWidth,fillStyle:"#2BA104",isStroke:!0,isFill:!0,indicatorText:r.indicatorText,textColor:null,font:null,counterClockwise:!1};this.model.drawIndicators&&this._onDrawIndicators(this.style,this.region);f==t.datavisualization.CircularGauge.IndicatorType.Image?(e=new Image,e.onload=function(){u.contextEl.drawImage(this,r.position.x,r.position.y)},e.src=r.imageUrl):r.stateRanges!=null&&n.each(r.stateRanges,function(n,i){u.pointerEl[u.pointerIndex].value>=i.startValue&&u.pointerEl[u.pointerIndex].value<=i.endValue?(o=!0,!t.isNullOrUndefined(i.text)&&i.text.length>0&&(u.style.indicatorText=i.text,u.style.textColor=i.textColor=="transparent"?"rgba(0,0,0,0)":i.textColor,u.style.font=r.font),u.style.strokeStyle=i.borderColor=="transparent"?"rgba(0,0,0,0)":i.borderColor,u.style.fillStyle=i.backgroundColor=="transparent"?"rgba(0,0,0,0)":i.backgroundColor,u._drawIndicatorFrame(f,u.region,u.style)):(u.style.strokeStyle=i.borderColor=="transparent"?"rgba(0,0,0,0)":i.borderColor,u.style.fillStyle=i.backgroundColor=="transparent"?"rgba(0,0,0,0)":i.backgroundColor)});o||f==t.datavisualization.CircularGauge.IndicatorType.Image||u._drawIndicatorFrame(f,u.region,u.style);this.contextEl.getImageData&&f!=t.datavisualization.CircularGauge.IndicatorType.Image&&(this.indicatorImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height))},_drawIndicatorFrame:function(n,t,i){switch(n){case"circle":this._drawIndicatorCircle(t,i);break;case"roundedrectangle":case"rectangle":this._drawRectangleFrame(t,i);break;case"text":this._drawText(t,i);break;case"triangle":this._drawIndicatorTriangle(t,i,this);break;case"diamond":this._drawDiamond(t,i,this);break;case"trapezoid":this._drawTrapezoid(t,i,this);break;case"pentagon":this._drawPentagon(t,i,this);break;case"wedge":this._drawWedge(t,i,this);break;case"star":this._drawIndicatorStar(t,i,this);break;case"ellipse":this._drawIndicatorEllipse(t,i,this);break;case"horizontalline":this._drawHorizontalLine(t,i,this);break;case"verticalline":this._drawVerticalLine(t,i,this);break;case"cross":this._drawCross(t,i,this);break;case"uparrow":this._drawUpArrow(t,i,this);break;case"downarrow":this._drawDownArrow(t,i,this);break;case"invertedtriangle":this._drawIndicatorInvertedTriangle(t,i,this);break;case"leftarrow":this._drawLeftArrow(t,i,this);break;case"rightarrow":this._drawRightArrow(t,i,this)}},_drawScaleBar:function(n,t){this.contextEl.shadowColor=t.strokeStyle=="transparent"?"rgba(0,0,0,0)":t.strokeStyle;t.shadowOffset&&(this.contextEl.shadowBlur=t.shadowOffset);this._contextOpenPath(t,this);n.endAngle-n.startAngle==0?this.contextEl.arc(n.centerX,n.centerY,n.startRadius,Math.PI*0,Math.PI*0,!1):(n.endAngle-n.startAngle)%360==0?(this.contextEl.arc(n.centerX,n.centerY,n.startRadius,Math.PI*0,Math.PI*2,!1),this.contextEl.arc(n.centerX,n.centerY,n.endRadius,Math.PI*2,Math.PI*0,!0)):(this.contextEl.arc(n.centerX,n.centerY,n.startRadius,Math.PI*(n.startAngle/180),Math.PI*(n.endAngle/180),!1),this.contextEl.arc(n.centerX,n.centerY,n.endRadius,Math.PI*(n.endAngle/180),Math.PI*(n.startAngle/180),!0));this._contextClosePath(t,this)},_drawTickMark:function(n,t){this._contextOpenPath(t,this);this.contextEl.translate(n.startX,n.startY);this.contextEl.lineTo(0,0);this.contextEl.rotate(Math.PI*(t.angle/180));this.contextEl.lineTo(t.lineHeight,0);this._contextClosePath(t,this)},_drawLabel:function(n,i,r){var f=0,e=this.model.locale,u;t.isNullOrUndefined(r)||r||(u=this.model.scales[this.scaleIndex].labels[this.labelIndex].unitTextPosition,i.textValue=i.textValue%1!=0&&typeof i.textValue!="string"?+parseFloat(i.textValue.toFixed(3)):i.textValue,i.textValue=e&&this.model.enableGroupSeparator?i.textValue.toLocaleString(e):i.textValue,t.isNullOrUndefined(u)||u.toString()!="back"?t.isNullOrUndefined(u)||u.toString()!="front"||(i.textValue=this.model.scales[this.scaleIndex].labels[this.labelIndex].unitText+i.textValue):i.textValue+=this.model.scales[this.scaleIndex].labels[this.labelIndex].unitText,f=this._calcDistanceFactor(i.textPositionAngle,i.placement,this._calFontLength(i.font)));this._contextOpenPath(i,this);this.contextEl.textAlign="center";this.contextEl.textBaseline="middle";this.contextEl.font=i.font;this.contextEl.translate(n.startX+f,n.startY);this.contextEl.lineTo(0,0);r?r&&i.angle!=0&&this.contextEl.rotate(Math.PI*(i.angle/180)):this.scaleEl[this.scaleIndex].labels[this.labelIndex].autoAngle?this.contextEl.rotate(Math.PI*((i.angle-270)/180)):this.contextEl.rotate(Math.PI*(i.angle/180));this.contextEl.fillText(i.textValue,0,0);this._contextClosePath(i,this)},_drawCircleFrame:function(n,t){this._contextOpenPath(t,this);this.contextEl.arc(n.centerX,n.centerY,t.radius,n.startAngle,n.endAngle,t.counterClockwise);this._contextClosePath(t,this);t.indicatorText&&this._drawText(n,t)},_drawIndicatorCircle:function(n,t){this._contextOpenPath(t,this);this.contextEl.arc(n.startX+t.width/2,n.startY,t.radius,n.startAngle,n.endAngle,t.counterClockwise);this._contextClosePath(t,this);t.indicatorText&&this._drawText(n,t)},_drawHalfCircle:function(n,t){this._contextOpenPath(t,this);this.contextEl.lineJoin="round";this.contextEl.arc(n.centerX,n.centerY,t.radius,n.startAngle,n.endAngle,!1);this.contextEl.lineTo(n.centerX,n.centerY);this._contextClosePath(t,this)},_drawRectangleFrame:function(n,t){this._contextOpenPath(t,this);this.contextEl.translate(n.startX,n.startY-t.height/2);this.contextEl.lineTo(t.cornerRadius,0);this.contextEl.lineTo(t.width-t.cornerRadius,0);this.contextEl.quadraticCurveTo(t.width,0,t.width,t.cornerRadius);this.contextEl.lineTo(t.width,t.height-t.cornerRadius);this.contextEl.quadraticCurveTo(t.width,t.height,t.width-t.cornerRadius,t.height);this.contextEl.lineTo(t.cornerRadius,t.height);this.contextEl.quadraticCurveTo(0,t.height,0,t.height-t.cornerRadius);this.contextEl.lineTo(0,t.cornerRadius);this.contextEl.quadraticCurveTo(0,0,t.cornerRadius,0);this._contextClosePath(t,this);t.indicatorText&&this._drawText(n,t)},_drawText:function(n,t){this.contextEl.beginPath();this.contextEl.textAlign="center";this.contextEl.fillStyle=t.textColor=="transparent"?"rgba(0,0,0,0)":t.textColor;this.contextEl.font=t.font;this.contextEl.fillText(t.indicatorText,n.textLocation.x,n.textLocation.y);this.contextEl.closePath()},_drawRange:function(n,t){var i;if(t.startWidth==null&&t.endWidth==null)this._contextOpenPath(t,this),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.startRadius,Math.PI*(n.startAngle/180),Math.PI*(n.endAngle/180),t.counterClockwise),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.endRadius,Math.PI*(n.endAngle/180),Math.PI*(n.startAngle/180),!t.counterClockwise),this._contextClosePath(t,this);else{var u=t.startWidth>t.endWidth?t.startWidth-t.endWidth:t.endWidth-t.startWidth,f=this.scaleEl[this.scaleIndex].direction=="clockwise"?n.startAngle<n.endAngle?n.endAngle-n.startAngle:360-(n.startAngle-n.endAngle):n.endAngle<n.startAngle?n.startAngle-n.endAngle:n.startAngle+(360-n.endAngle),r=f/(u*2);if(t.startWidth<t.endWidth)for(t.startWidth!=0&&(this._contextOpenPath(t,this),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.radius,Math.PI*(n.startAngle/180),Math.PI*(n.endAngle/180),t.counterClockwise),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.placement=="near"?t.radius-t.startWidth:t.radius+t.startWidth,Math.PI*(n.endAngle/180),Math.PI*(n.startAngle/180),!t.counterClockwise),this._contextClosePath(t,this)),t.radius=t.placement=="near"?t.radius-t.startWidth:t.radius+t.startWidth,t.endWidth-=t.startWidth,t.startWidth=0,i=t.startWidth;i<t.endWidth;i+=.5)this.contextEl.beginPath(),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.radius,Math.PI*(n.endAngle/180),Math.PI*(n.startAngle/180),!t.counterClockwise),this.contextEl.lineWidth=2,this.contextEl.strokeStyle=t.fillStyle,this.contextEl.stroke(),this.contextEl.restore(),n.startAngle=this.scaleEl[this.scaleIndex].direction=="clockwise"?n.startAngle+r:n.startAngle-r,t.radius=t.placement=="near"?t.radius-.5:t.radius+.5;else for(t.endWidth!=0&&(this._contextOpenPath(t,this),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.radius,Math.PI*(n.startAngle/180),Math.PI*(n.endAngle/180),t.counterClockwise),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.placement=="near"?t.radius-t.endWidth:t.radius+t.endWidth,Math.PI*(n.endAngle/180),Math.PI*(n.startAngle/180),!t.counterClockwise),this._contextClosePath(t,this)),t.radius=t.placement=="near"?t.radius-t.endWidth:t.radius+t.endWidth,t.startWidth-=t.endWidth,t.endWidth=0,i=t.endWidth;i<t.startWidth;i+=.5)this.contextEl.beginPath(),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.radius,Math.PI*(n.startAngle/180),Math.PI*(n.endAngle/180),t.counterClockwise),this.contextEl.lineWidth=2,this.contextEl.strokeStyle=t.fillStyle,this.contextEl.stroke(),this.contextEl.restore(),n.endAngle=this.scaleEl[this.scaleIndex].direction=="clockwise"?n.endAngle-r:n.endAngle+r,t.radius=t.placement=="near"?t.radius-.5:t.radius+.5}},_drawPointers:function(n,i){i.value=i.value>this.scaleEl[this.scaleIndex].maximum?this.scaleEl[this.scaleIndex].maximum:i.value;i.value=i.value<this.scaleEl[this.scaleIndex].minimum?this.scaleEl[this.scaleIndex].minimum:i.value;var e,u,r,f,o,s=this.model.locale;r=this._getAngle(i.value);r=r>360?r-360:r;i.type=="needle"?(i.placement=="far"&&(f=i.length+this.scaleEl[this.scaleIndex].size/2),i.placement=="center"&&(f=i.length),i.placement=="near"&&(f=i.length-this.scaleEl[this.scaleIndex].size/2)):(f=i.length,i.placement=="far"&&(e=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this.scaleEl[this.scaleIndex].radius+this.scaleEl[this.scaleIndex].size/2+i.distanceFromScale,r),u=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerX,this.scaleEl[this.scaleIndex].radius+this.scaleEl[this.scaleIndex].size/2+i.distanceFromScale,r)),i.placement=="center"&&(e=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this.scaleEl[this.scaleIndex].radius-this.scaleEl[this.scaleIndex].size/2-i.distanceFromScale,r),u=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerX,this.scaleEl[this.scaleIndex].radius-this.scaleEl[this.scaleIndex].size/2-i.distanceFromScale,r)),i.placement=="near"&&(e=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this.scaleEl[this.scaleIndex].radius-this.scaleEl[this.scaleIndex].size/2-i.distanceFromScale-this.majorTickHeight-15,r),u=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerX,this.scaleEl[this.scaleIndex].radius-this.scaleEl[this.scaleIndex].size/2-i.distanceFromScale-this.majorTickHeight-15,r)),this._isHalfCircle&&this._isHalfCircle()||(this.model.height>this.model.width?u+=(this.model.height-this.model.width)/2:this.model.height<this.model.width&&(u-=(this.model.width-this.model.height)/2)));f>this.model.radius&&(f=this.model.radius);this.region={startX:i.type=="needle"?this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX:e,startY:i.type=="needle"?this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY:u};!t.isNullOrUndefined(i.gradients)&&i.gradients.colorInfo.length>0?(o=this.contextEl.createLinearGradient(0,0,0,i.width),this._setGradientColor(this,o,i.gradients.colorInfo)):o=i.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.backgroundColor);this.style={width:i.width,isFill:!0,isStroke:!0,radius:0,showBackNeedle:i.showBackNeedle,backNeedleLength:i.backNeedleLength,angle:i.type=="needle"?r:i.placement=="far"?r:r+180,height:f,lineWidth:i.border.width,opacity:isNaN(i.opacity)?1:i.opacity,strokeStyle:i.border.color=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.border.color),fillStyle:o,imageUrl:i.imageUrl,type:i.type};this.model.drawPointers&&this._onDrawPointers(r,i.value);i.type=="needle"?(this._drawNeedlePointer(this.region,this.style,i),this._setPointerCap(i)):(i.markerType=="roundedrectangle"&&(this.style.radius=5),this._drawMarkerType(i.markerType,this.region,this.style));i.pointerValueText.showValue&&(e=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._labelRadius+i.pointerValueText.distance,r),u=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,this._labelRadius+i.pointerValueText.distance,r),this.region={startX:e,startY:u},this.style={angle:i.pointerValueText.autoAngle?i.pointerValueText.angle+r:i.pointerValueText.angle,textValue:s&&this.model.enableGroupSeparator?i.value.toLocaleString(s):i.value,fillStyle:i.pointerValueText.color=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.pointerValueText.color),font:this._getFontString(i,i.pointerValueText.font),opacity:i.pointerValueText.opacity},this._drawPointerValueText(this.region,this.style));this.contextEl.getImageData&&(this.pointerImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height));this._notifyArrayChange&&this._notifyArrayChange("scales["+this.scaleIndex+"]pointers["+n+"]value",i.value);this.value(i.value)},_drawPointerValueText:function(n,t){this._contextOpenPath(t,this);this.contextEl.textAlign="center";this.contextEl.textBaseline="middle";this.contextEl.font=t.font;this.contextEl.translate(n.startX,n.startY);this.contextEl.lineTo(0,0);this.scaleEl[this.scaleIndex].pointers[this.pointerIndex].pointerValueText.autoAngle?this.contextEl.rotate(Math.PI*((t.angle-270)/180)):this.contextEl.rotate(Math.PI*(t.angle/180));this.contextEl.fillText(t.textValue,0,0);this._contextClosePath(t,this)},_drawMarkerType:function(n,i,r){switch(n){case"rectangle":this._drawRectangle(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"triangle":this._drawTriangle(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"ellipse":this._drawEllipse(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"diamond":this._drawDiamond(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"pentagon":this._drawPentagon(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"circle":this._drawCircle(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"slider":this._drawSlider(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"pointer":this._drawPointer(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"wedge":this._drawWedge(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"trapezoid":this._drawTrapezoid(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"roundedrectangle":this._drawRoundedRectangle(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"image":this._drawImagePointer(i,r,this)}},_drawNeedlePointer:function(n,t,i){this.pointerEl[this.pointerIndex].needleType=="image"?this._drawImagePointer(n,t,i):(this._contextOpenPath(t,this),this.contextEl.translate(n.startX,n.startY),this.contextEl.rotate(Math.PI*(t.angle/180)),this.contextEl.lineTo(0,-t.width/2),this.pointerEl[this.pointerIndex].needleType=="triangle"?this.contextEl.lineTo(t.height,0):this.pointerEl[this.pointerIndex].needleType=="rectangle"?(this.contextEl.lineTo(t.height,-t.width/2),this.contextEl.lineTo(t.height,t.width/2)):this.pointerEl[this.pointerIndex].needleType=="trapezoid"?(this.contextEl.lineTo(t.height,-t.width/4),this.contextEl.lineTo(t.height,t.width/4)):this.pointerEl[this.pointerIndex].needleType=="arrow"&&(this.contextEl.lineTo(t.height-t.height/4,-t.width/6),this.contextEl.lineTo(t.height-t.height/4,-t.width/2),this.contextEl.lineTo(t.height,0),this.contextEl.lineTo(t.height-t.height/4,t.width/2),this.contextEl.lineTo(t.height-t.height/4,t.width/6)),this.contextEl.lineTo(0,t.width/2),t.showBackNeedle&&(this.contextEl.lineTo(-(t.backNeedleLength+this.scaleEl[this.scaleIndex].pointerCap.radius/2),t.width/2),this.contextEl.lineTo(-(t.backNeedleLength+this.scaleEl[this.scaleIndex].pointerCap.radius/2),-t.width/2)),this._contextClosePath(t,this));this.canvasEl.attr("aria-label",this.model.scales[this.scaleIndex].pointers[this.pointerIndex].value)},_drawImagePointer:function(n,t){var i=this,r=new Image,f=t.angle,e=n.startX,o=n.startY,s=t.width,u=t.height,h=t.type;r.onload=function(){i.contextEl.save();i.contextEl.translate(e,o);i.contextEl.rotate(Math.PI*(f/180));i.contextEl.drawImage(this,0,-u/2,s,u);i.contextEl.restore()};r.src=t.imageUrl},_setPointerCap:function(n){var i;i=this.contextEl.createRadialGradient(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.canvasEl[0].height/2,0,this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.canvasEl[0].height/2,this.scaleEl[this.scaleIndex].pointerCap.radius);t.isNullOrUndefined(this.scaleEl[this.scaleIndex].pointerCap.interiorGradient)?i=this.scaleEl[this.scaleIndex].pointerCap.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(this,this._getColor(n,this.scaleEl[this.scaleIndex].pointerCap.backgroundColor)):this._setGradientColor(this,i,this.scaleEl[this.scaleIndex].pointerCap.interiorGradient.colorInfo);this.region={centerX:this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,centerY:this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,startAngle:0,endAngle:2*Math.PI};this.style={isStroke:!0,isFill:!0,strokeStyle:this.scaleEl[this.scaleIndex].pointerCap.borderColor=="transparent"?"rgba(0,0,0,0)":this._getColor(this,this._getColor(n,this.scaleEl[this.scaleIndex].pointerCap.borderColor)),radius:this.scaleEl[this.scaleIndex].pointerCap.radius,lineWidth:this.scaleEl[this.scaleIndex].pointerCap.borderWidth,fillStyle:i};this.model.drawPointerCap&&this._onDrawPointerCap();this._drawCircleFrame(this.region,this.style)},isAndroid:function(){return/android/i.test(navigator.userAgent.toLowerCase())},redraw:function(n){switch(n){case"scale":this._reDrawScale();break;case"pointer":this._reDrawPointer();break;case"range":this._reDrawRange();break;case"label":this._reDrawLabel();break;case"tickMark":this._reDrawTickMark();break;case"subGauges":this._reDrawSubGauge();break;case"CustomLabel":this._reDrawCustomLabel();break;default:this._init()}},_getIndicatorImage:function(){return this.pointerImage?this.pointerImage:this._getPointerImage()},_getPointerImage:function(){return this.customLabelImage?this.customLabelImage:this._getCustomLabelImage()},_getSubGaugeImage:function(){return this.labelImage?this.labelImage:this._getLabelImage()},_getCustomLabelImage:function(){return this.subGaugeImage?this.subGaugeImage:this._getSubGaugeImage()},_getRangeImage:function(){return this.model.rangeZOrder=="rear"?this.scaleImage?this.scaleImage:this.outerImage:this.tickImage?this.tickImage:this._getTickImage()},_getLabelImage:function(){return this.model.rangeZOrder=="rear"?this.tickImage?this.tickImage:this._getTickImage():this.tickImage?this.rangeImage:this._getRangeImage()},_getTickImage:function(){return this.model.rangeZOrder=="rear"?this.rangeImage?this.rangeImage:this._getRangeImage():this.scaleImage?this.scaleImage:this.outerImage},getPointerValue:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].value:null},setMarkerDistanceFromScale:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(this.scaleEl[n].pointers[t].distanceFromScale=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawPointer():this._initialize())},getMarkerDistanceFromScale:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].distanceFromScale:null},setPointerLength:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(this.scaleEl[n].pointers[t].length=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawPointer():this._initialize())},getPointerLength:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].length:null},setPointerWidth:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(this.scaleEl[n].pointers[t].width=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawPointer():this._initialize())},getPointerWidth:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].width:null},setBackNeedleLength:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(this.scaleEl[n].pointers[t].backNeedleLength=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawPointer():this._initialize())},getBackNeedleLength:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].backNeedleLength:null},setNeedleStyle:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(this.scaleEl[n].pointers[t].needleType=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawPointer():this._initialize())},getNeedleStyle:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].needleType:null},setPointerPlacement:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(this.scaleEl[n].pointers[t].placement=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawPointer():this._initialize())},getPointerPlacement:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].placement:null},setPointerNeedleType:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(this.scaleEl[n].pointers[t].type=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawCustomLabel():this._initialize())},getPointerNeedleType:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].type:null},setMarkerStyle:function(n,i,r){n<this.model.scales.length&&i<this.model.scales[n].pointers.length&&r!=null&&(r!="roundedrectangle"&&t.isNullOrUndefined(t.datavisualization.CircularGauge.MarkerType[r.replace(/\w\S*/g,function(n){return n.charAt(0).toUpperCase()+n.substr(1).toLowerCase()})])||(this.scaleEl[n].pointers[i].markerType=r,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawCustomLabel():this._initialize()))},getMarkerStyle:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].markerType:null},setCustomLabelValue:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].customLabels.length&&i!=null&&(this.scaleEl[n].customLabels[t].value=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawCustomLabel():this._initialize())},getCustomLabelValue:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].customLabels.length?this.scaleEl[n].customLabels[t].value:null},setSubGaugeLocation:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].subGauges.length&&i!=null&&(this.scaleEl[n].subGauges[t].position.x=i.x,this.scaleEl[n].subGauges[t].position.y=i.y,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawSubGauge():this._initialize())},getSubGaugeLocation:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].subGauges.length?this.scaleEl[n].subGauges[t].position:null},setCustomLabelAngle:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].customLabels.length&&i!=null&&(this.scaleEl[n].customLabels[t].textAngle=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawCustomLabel():this._initialize())},getCustomLabelAngle:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].customLabels.length?this.scaleEl[n].customLabels[t].textAngle:null},setRangeStartValue:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].startValue=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawRange():this._initialize())},getRangeStartValue:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ranges.length?this.scaleEl[n].ranges[t].startValue:null},setRangeEndValue:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].endValue=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawRange():this._initialize())},getRangeEndValue:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ranges.length?this.scaleEl[n].ranges[t].endValue:null},setRangeSize:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].size=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawRange():this._initialize())},setRangeVisible:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t]._visible=i,this.contextEl.putImageData&&!this.isAndroid()?(this._initialize(),this._wireEvents()):this._initialize())},getRangeSize:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ranges.length?this.scaleEl[n].ranges[t].size:null},setRangeDistanceFromScale:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].distanceFromScale=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawRange():this._initialize())},getRangeDistanceFromScale:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ranges.length?this.scaleEl[n].ranges[t].distanceFromScale:null},setRangePosition:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].placement=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawRange():this._initialize())},getRangePosition:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ranges.length?this.scaleEl[n].ranges[t].placement:null},setRangeBorderWidth:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].border.width=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawRange():this._initialize())},getRangeBorderWidth:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ranges.length?this.scaleEl[n].ranges[t].border.width:null},setPointerValue:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(i>=this.scaleEl[n].minimum&&i<=this.scaleEl[n].maximum&&(this.scaleEl[n].pointers[t].value=i),!this.updatePointerOnAnimation&&this.model.enableAnimation&&(this.pointerValue[t]=i),this.contextEl.putImageData&&!this._androidAnimation?(this._reDrawPointer(),this.updatePointerOnAnimation=!1):this._initialize())},setLabelAngle:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].angle=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawLabel():this._initialize())},getLabelAngle:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].labels.length?this.scaleEl[n].labels[t].angle:null},setLabelDistanceFromScale:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].distanceFromScale=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawLabel():this._initialize())},getLabelDistanceFromScale:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].labels.length?this.scaleEl[n].labels[t].distanceFromScale:null},setLabelStyle:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].type=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawLabel():this._initialize())},getLabelStyle:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].labels.length?this.scaleEl[n].labels[t].type:null},setLabelPlacement:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].placement=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawLabel():this._initialize())},getLabelPlacement:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].labels.length?this.scaleEl[n].labels[t].placement:null},setTickAngle:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].angle=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawTickMark():this._initialize())},getTickAngle:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ticks.length?this.scaleEl[n].ticks[t].angle:null},setTickStyle:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].type=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawTickMark():this._initialize())},getTickStyle:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ticks.length?this.scaleEl[n].ticks[t].type:null},setTickPlacement:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].placement=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawTickMark():this._initialize())},getTickPlacement:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ticks.length?this.scaleEl[n].ticks[t].placement:null},setTickWidth:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].width=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawTickMark():this._initialize())},getTickWidth:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ticks.length?this.scaleEl[n].ticks[t].width:null},setTickHeight:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].height=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawTickMark():this._initialize())},getTickHeight:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ticks.length?this.scaleEl[n].ticks[t].height:null},setTickDistanceFromScale:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].distanceFromScale=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawTickMark():this._initialize())},getTickDistanceFromScale:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ticks.length?this.scaleEl[n].ticks[t].distanceFromScale:null},setStartAngle:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].startAngle=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getStartAngle:function(n){return n<this.model.scales.length?this.scaleEl[n].startAngle:null},setSweepAngle:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].sweepAngle=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getSweepAngle:function(n){return n<this.model.scales.length?this.scaleEl[n].sweepAngle:null},setMinimumValue:function(n,t){n<this.model.scales.length&&t!=null&&(t<this.scaleEl[n].maximum&&(this.scaleEl[n].minimum=t),this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getMinimumValue:function(n){return n<this.model.scales.length?this.scaleEl[n].minimum:null},setMaximumValue:function(n,t){n<this.model.scales.length&&t!=null&&(t>this.scaleEl[n].minimum&&(this.scaleEl[n].maximum=t),this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getMaximumValue:function(n){return n<this.model.scales.length?this.scaleEl[n].maximum:null},setScaleBarSize:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].size=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getScaleBarSize:function(n){return n<this.model.scales.length?this.scaleEl[n].size:null},setScaleRadius:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].radius=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getScaleRadius:function(n){return n<this.model.scales.length?this.scaleEl[n].radius:null},setMajorIntervalValue:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].majorIntervalValue=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getMajorIntervalValue:function(n){return n<this.model.scales.length?this.scaleEl[n].majorIntervalValue:null},setMinorIntervalValue:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].minorIntervalValue=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getMinorIntervalValue:function(n){return n<this.model.scales.length?this.scaleEl[n].minorIntervalValue:null},setPointerCapRadius:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].pointerCap.radius=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getPointerCapRadius:function(n){return n<this.model.scales.length?this.scaleEl[n].pointerCap.radius:null},setScaleBorderWidth:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].border.width=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getScaleBorderWidth:function(n){return n<this.model.scales.length?this.scaleEl[n].border.width:null},setPointerCapBorderWidth:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].pointerCap.borderWidth=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getPointerCapBorderWidth:function(n){return n<this.model.scales.length?this.scaleEl[n].pointerCap.borderWidth:null},setScaleDirection:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].direction=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getScaleDirection:function(n){return n<this.model.scales.length?this.scaleEl[n].direction:null},includeFirstValue:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].includeFirstValue=i,this.contextEl.putImageData?this._reDrawLabel():this.initialize())},_reDrawScale:function(){t.isNullOrUndefined(this.model.frame.backgroundImageUrl)&&(this.contextEl.putImageData(this.outerImage,0,0),this._drawScales())},_reDrawPointer:function(){t.isNullOrUndefined(this.model.frame.backgroundImageUrl)&&(this.contextEl.putImageData(this._getPointerImage(),0,0),this._setPointers(),this._setIndicators())},_reDrawCustomLabel:function(){t.isNullOrUndefined(this.model.frame.backgroundImageUrl)&&(this.contextEl.putImageData(this._getCustomLabelImage(),0,0),this._setCustomLabel(),this._setPointers(),this._setIndicators())},_reDrawRange:function(){t.isNullOrUndefined(this.model.frame.backgroundImageUrl)&&(this.contextEl.putImageData(this._getRangeImage(),0,0),this.model.rangeZOrder=="rear"?(this._setRanges(),this._setTicks()):this._setRanges(),this._setLabels(),this._subGauge(),this._setCustomLabel(),this._setPointers(),this._setIndicators())},_reDrawLabel:function(){t.isNullOrUndefined(this.model.frame.backgroundImageUrl)&&(this.contextEl.putImageData(this._getLabelImage(),0,0),this._setLabels(),this._subGauge(),this._setCustomLabel(),this._setPointers(),this._setIndicators())},_reDrawTickMark:function(){t.isNullOrUndefined(this.model.frame.backgroundImageUrl)&&(this.contextEl.putImageData(this._getTickImage(),0,0),this.model.rangeZOrder=="rear"?this._setTicks():(this._setTicks(),this._setRanges()),this._setLabels(),this._subGauge(),this._setCustomLabel(),this._setPointers(),this._setIndicators())},_reDrawSubGauge:function(){t.isNullOrUndefined(this.model.frame.backgroundImageUrl)&&(this.contextEl.putImageData(this._getSubGaugeImage(),0,0),this._subGauge(),this._setCustomLabel(),this._setPointers(),this._setIndicators())},refreshSubGauge:function(){this.contextEl.putImageData?this._reDrawSubGauge():this._initialize()},refresh:function(){this._scaleRedrawn=!0;this._initialize();this._wireEvents()},"export":function(){var i=this.model.exportSettings,u,f,e,r,o,s;i.mode.toLowerCase()==="client"?this.exportImage(i.fileName,i.fileType):(f=i.type.toLowerCase()==="jpg"?"image/jpeg":"image/png",u=this.canvasEl[0].toDataURL(f),e={action:i.action,method:"post"},r=t.buildTag("form","",null,e),o={name:"Data",type:"hidden",value:u},s=t.buildTag("input","",null,o),r.append(s).append(this),n("body").append(r),r.submit())},exportImage:function(n,i){var c,o,e,f,l,h,u,r,v,s,w,y;if(t.browserInfo().name==="msie"&&parseFloat(t.browserInfo().version)<10)return!1;if(c=document.getElementById(this._id),o=new Image,c!==null){for(e=document.createElement("canvas"),f=e.getContext("2d"),e.width=c.clientWidth,e.height=c.clientHeight,l=20,h=!0,this.model.outerCustomLabelPosition!="top"&&this.model.outerCustomLabelPosition!="left"&&f.drawImage(this.canvasEl[0],0,0),u=document.getElementsByClassName(this._id+"outercustomlbl"),r=0;r<u.length;r++)f.fillStyle=u[r].style.color,f.font=u[r].style.fontWeight+" "+u[r].style.fontSize+" "+u[r].style.fontFamily,this.model.outerCustomLabelPosition==="top"?(h==!0&&f.drawImage(this.canvasEl[0],0,e.height-this.model.height),f.fillText(u[r].innerHTML,u[r].getBoundingClientRect().left,u[r].getBoundingClientRect().top+u[r].getBoundingClientRect().height+l),h=!1):this.model.outerCustomLabelPosition==="bottom"?f.fillText(u[r].innerHTML,u[r].getBoundingClientRect().left,u[r].getBoundingClientRect().top+u[r].getBoundingClientRect().height-l):this.model.outerCustomLabelPosition==="right"?f.fillText(u[r].innerHTML,u[r].getBoundingClientRect().left,u[r].getBoundingClientRect().top+u[r].getBoundingClientRect().height):(h===!0&&f.drawImage(this.canvasEl[0],e.width/2,(e.height-this.model.height)/2),f.fillText(u[r].innerHTML,u[r].getBoundingClientRect().left,u[r].getBoundingClientRect().top+u[r].getBoundingClientRect().height),h=!1);o=e.toDataURL()}else o=this.canvasEl[0].toDataURL();o=o.replace(/^data:[a-z]*;,/,"");var b=o.split(","),a=atob(b[1]),p=new ArrayBuffer(a.length),k=new Uint8Array(p);for(r=0;r<a.length;r++)k[r]=a.charCodeAt(r);return v=new Blob([p],{type:"image/png"}),t.browserInfo().name==="msie"?window.navigator.msSaveOrOpenBlob(v,n+"."+i):(s=document.createElement("a"),w=URL.createObjectURL(v),s.href=w,s.setAttribute("download",n+"."+i),document.createEvent?(y=document.createEvent("MouseEvents"),y.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),s.dispatchEvent(y)):s.fireEvent&&s.fireEvent("onclick")),!0},resizeCanvas:function(){var s,h,i,o,f;if(r=r!=0?r-1:n(".e-circulargauge").length-1,h=!0,t.isNullOrUndefined(this.GaugeEl.parent().attr("style"))||(s=this.GaugeEl.parent().attr("style").split(";")),t.isNullOrUndefined(s)||n.each(s,function(n,t){while(t.indexOf("width")!=-1){h=t.indexOf("px")==-1?!0:!1;break}}),h){var e=window.innerWidth/u,c=this.model.width,l=this.model.height;for((c*e>window.innerWidth||l*e>window.innerHeight)&&(e=1),this.model.width*=e,this.model.height*=e,this.model.radius*=e,this._gaugeResizeState=!0,i=0;this.model.scales[i]!=null;i++){for(this.model.scales[i].radius*=e,this.model.scales[i].pointerCap.radius*=e,o=0;o<this.model.scales[i].customLabels.length;o++)t.isNullOrUndefined(this.model.scales[i].customLabels[o])||(this.model.scales[i].customLabels[o].positionType!="outer"&&(this.model.scales[i].customLabels[o].position.x*=e),this.model.scales[i].customLabels[o].position.y*=e,this.model.scales[i].customLabels[o].font.size=parseFloat(this.model.scales[i].customLabels[o].font.size.match(/\d+/)[0])*e<10?"10px":parseFloat(this.model.scales[i].customLabels[o].font.size.match(/\d+/)[0])*e>this._customLblMaxSize?this._customLblMaxSize.toString()+"px":(parseFloat(this.model.scales[i].customLabels[o].font.size.match(/\d+/)[0])*e).toString()+"px");for(f=0;this.model.scales[i].labels[f]!=null||this.model.scales[i].pointers[f]!=null||this.model.scales[i].ranges[f]!=null||this.model.scales[i].indicators[f]!=null||this.model.scales[i].subGauges[f]!=null||this.model.scales[i].ticks[f]!=null;f++)t.isNullOrUndefined(this.model.scales[i].ticks[f])||(this.model.scales[i].ticks[f].height*=e),t.isNullOrUndefined(this.model.scales[i].pointers[f])||(this.model.scales[i].pointers[f].length*=e,this.model.scales[i].pointers[f].width*=e,this.model.scales[i].pointers[f].backNeedleLength*=e),t.isNullOrUndefined(this.model.scales[i].ranges[f])||(this.model.scales[i].ranges[f].distanceFromScale*=e,this.model.scales[i].ranges[f].size*=e),!t.isNullOrUndefined(this.model.scales[i].indicators[f])&&this.model.scales[i].showIndicators&&(this.model.scales[i].indicators[f].height*=e,this.model.scales[i].indicators[f].width*=e,this.model.scales[i].indicators[f].position.x*=e,this.model.scales[i].indicators[f].position.y*=e),!t.isNullOrUndefined(this.model.scales[i].subGauges[f])&&t.isNullOrUndefined(this.model.scales[i].subGauges[f].controlID)&&(this.model.scales[i].subGauges[f].height*=e,this.model.scales[i].subGauges[f].width*=e,this.model.scales[i].subGauges[f].position.x*=e)}this.refresh();r==0&&(u=window.innerWidth)}},_onDrawTicks:function(n,t){var i={index:this.tickIndex,element:this.tickEl[this.tickIndex],angle:parseInt(n)},r={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,pointerValue:t,style:this.style,position:this.region,tick:i};this._trigger("drawTicks",r)},_onDrawLabels:function(n,t){var i={index:this.labelIndex,element:this.labelEl[this.labelIndex],angle:parseInt(n)},r={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,pointerValue:t,style:this.style,position:this.region,label:i};this._trigger("drawLabels",r)},_onDrawPointers:function(n,t){var i={index:this.pointerIndex,element:this.pointerEl[this.pointerIndex],angle:parseInt(n),pointerValue:t},r={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region,pointer:i};this._trigger("drawPointers",r)},_onDrawRange:function(){var n={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,rangeIndex:this.rangeIndex,rangeElement:this.rangeEl[this.rangeEl],context:this.contextEl,style:this.style,position:this.region};this._trigger("drawRange",n)},_onDrawCustomLabel:function(){var n={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,customLabelIndex:this.customLabelIndex,customLabelElement:this.customLabelEl[this.customLabelIndex],context:this.contextEl,style:this.style,position:this.region};this._trigger("drawCustomLabel",n)},_onDrawIndicators:function(){var n={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,indicatorIndex:this.indicatorIndex,indicatorEl:this.indicatorEl[this.indicatorIndex],context:this.contextEl,style:this.style,position:this.region};this._trigger("drawIndicators",n)},_onDrawPointerCap:function(){var n={object:this,scaleElement:this.model.scales,position:this.region,style:this.style,context:this.contextEl};this._trigger("drawPointerCap",n)},_onRenderComplete:function(){var n={object:this,scaleElement:this.model.scales,context:this.contextEl};this._trigger("renderComplete",n)},_onMouseClick:function(n,t){var i={index:this.pointerIndex,element:this.pointerEl[this.pointerIndex],value:t,angle:parseInt(n)},r={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.position,pointers:i};this._trigger("mouseClick",r)},_onMouseClickMove:function(n,t){var i={index:this.pointerIndex,element:this.pointerEl[this.pointerIndex],value:t,angle:parseInt(n)},r={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region,pointers:i};this._trigger("mouseClickMove",r)},_onMouseClickUp:function(n,t){var i={index:this.pointerIndex,element:this.pointerEl[this.pointerIndex],value:t,angle:parseInt(n)},r={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region,pointers:i};this._trigger("mouseClickUp",r)},_setTheme:function(){var n=this.model.theme.toLowerCase(),t=this.model.themeProperties[n];this._setThemeColors(t)},_setThemeColors:function(n){var r=[],f=this.model.themeProperties,s,i,t,u,e,o;for(s in f)r.push(s);for(i=0;i<r.length;i++)for(this.model.backgroundColor=!this.model.backgroundColor||this.model.backgroundColor==f[r[i]].backgroundColor?n.backgroundColor:this.model.backgroundColor,t=0;t<this.model.scales.length;t++){for(this.model.scales[t].backgroundColor=!this.model.scales[t].backgroundColor||this.model.scales[t].backgroundColor==f[r[i]].scales.backgroundColor?n.scales.backgroundColor:this.model.scales[t].backgroundColor,this.model.scales[t].border.color=!this.model.scales[t].border.color||this.model.scales[t].border.color==f[r[i]].scales.border.color?n.scales.border.color:this.model.scales[t].border.color,u=0;u<this.model.scales[t].pointers.length;u++)this.model.scales[t].pointers[u].backgroundColor=!this.model.scales[t].pointers[u].backgroundColor||this.model.scales[t].pointers[u].backgroundColor==f[r[i]].scales.pointers.backgroundColor?n.scales.pointers.backgroundColor:this.model.scales[t].pointers[u].backgroundColor,this.model.scales[t].pointers[u].border.color=!this.model.scales[t].pointers[u].border.color||this.model.scales[t].pointers[u].border.color==f[r[i]].scales.pointers.border.color?n.scales.pointers.border.color:this.model.scales[t].pointers[u].border.color,this.model.scales[t].pointerCap.backgroundColor=!this.model.scales[t].pointerCap.backgroundColor||this.model.scales[t].pointerCap.backgroundColor==f[r[i]].scales.pointerCap.backgroundColor?n.scales.pointerCap.backgroundColor:this.model.scales[t].pointerCap.backgroundColor,this.model.scales[t].pointerCap.borderColor=!this.model.scales[t].pointerCap.borderColor||this.model.scales[t].pointerCap.borderColor==f[r[i]].scales.pointerCap.borderColor?n.scales.pointerCap.borderColor:this.model.scales[t].pointerCap.borderColor;for(e=0;e<this.model.scales[t].ticks.length;e++)this.model.scales[t].ticks[e].color=!this.model.scales[t].ticks[e].color||this.model.scales[t].ticks[e].color==f[r[i]].scales.ticks.color?n.scales.ticks.color:this.model.scales[t].ticks[e].color;for(o=0;o<this.model.scales[t].labels.length;o++)this.model.scales[t].labels[o].color=!this.model.scales[t].labels[o].color||this.model.scales[t].labels[o].color==f[r[i]].scales.labels.color?n.scales.labels.color:this.model.scales[t].labels[o].color}},_getFontString:function(n,t){return t?(t.fontStyle?t.fontStyle:"")+" "+(t.size==null?"11px":t.size)+" "+t.fontFamily:""},_drawTriangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);this._contextClosePath(t,i)},_drawPointer:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(t.width,t.height/4);i.contextEl.lineTo(t.width,-t.height/4);i.contextEl.lineTo(t.width/2,-t.height/4);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/2,t.height/2);i.contextEl.lineTo(t.width/2,t.height/4);this._contextClosePath(t,i)},_drawWedge:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(3*t.width/4,0);i.contextEl.lineTo(t.width,t.height/2);this._contextClosePath(t,i)},_drawSlider:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/4,-t.height/2);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);i.contextEl.lineTo(t.width/4,t.height/2);this._contextClosePath(t,i)},_drawIndicatorStar:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);i.contextEl.lineTo(0,t.height/2);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);i.contextEl.lineTo(0,-t.height/4);i.contextEl.lineTo(t.width,-t.height/4);i.contextEl.lineTo(0,t.height/2);this._contextClosePath(t,i)},_drawStar:function(n,t,i){this._contextOpenPath(t,i);i.model.Orientation=="Horizontal"&&i.markerPlacement=="near"?(i.contextEl.lineTo(n.startX+t.width-t.width/6,n.startY),i.contextEl.lineTo(n.startX,n.startY+t.height-t.height/3),i.contextEl.lineTo(n.startX+t.width,n.startY+t.height-t.height/3),i.contextEl.lineTo(n.startX+t.width/6,n.startY),i.contextEl.lineTo(n.startX+t.width/2,n.startY+t.height)):(i.contextEl.lineTo(n.startX+t.width/6,n.startY+t.height),i.contextEl.lineTo(n.startX+t.width,n.startY+t.height/3),i.contextEl.lineTo(n.startX,n.startY+t.height/3),i.contextEl.lineTo(n.startX+t.width-t.width/6,n.startY+t.height),i.contextEl.lineTo(n.startX+t.width/2,n.startY));this._contextClosePath(t,i)},_drawPentagon:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/3,-t.height/2);i.contextEl.lineTo(t.width,-t.height/4);i.contextEl.lineTo(t.width,t.height/4);i.contextEl.lineTo(t.width/3,t.height/2);this._contextClosePath(t,i)},_drawDiamond:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.lineTo(t.width,0);i.contextEl.lineTo(t.width/2,t.height/2);i.contextEl.lineTo(0,0);this._contextClosePath(t,i)},_drawCircle:function(n,t,i){var r=Math.sqrt(t.height*t.height+t.width*t.width)/2;t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.arc(r/2,0,r/2,0,Math.PI*2,!0);this._contextClosePath(t,i)},_drawLine:function(n,t,i){i.contextEl.beginPath();i.contextEl.strokeStyle=t.fillStyle;i.contextEl.globalAlpha=t.opacity;i.contextEl.lineWidth=t.lineWidth;i.contextEl.moveTo(n.startX,n.startY);i.contextEl.lineTo(n.startX+t.width,n.startY);i.contextEl.closePath();i.contextEl.stroke()},_drawHorizontalLine:function(n,t,i){i.contextEl.beginPath();i.contextEl.strokeStyle=t.fillStyle;i.contextEl.globalAlpha=t.opacity;i.contextEl.lineWidth=t.lineWidth;i.contextEl.moveTo(n.startX,n.startY);i.contextEl.lineTo(n.startX+t.width,n.startY);i.contextEl.closePath();i.contextEl.stroke()},_drawVerticalLine:function(n,t,i){i.contextEl.beginPath();i.contextEl.strokeStyle=t.fillStyle;i.contextEl.globalAlpha=t.opacity;i.contextEl.lineWidth=t.lineWidth;i.contextEl.moveTo(n.startX,n.startY+t.height/2);i.contextEl.lineTo(n.startX,n.startY+-t.height/2);i.contextEl.closePath();i.contextEl.stroke()},_drawCross:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width,0);i.contextEl.moveTo(t.width/2,0);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.moveTo(t.width/2,0);i.contextEl.lineTo(t.width/2,t.height/2);this._contextClosePath(t,i)},_drawIndicatorTriangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,t.height/2);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);this._contextClosePath(t,i)},_drawIndicatorInvertedTriangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);i.contextEl.lineTo(0,-t.height/2);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width/2,t.height/2);this._contextClosePath(t,i)},_drawUpArrow:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.lineTo(t.width,0);i.contextEl.lineTo(t.width*(3/4),0);i.contextEl.lineTo(t.width*(3/4),t.height/2);i.contextEl.lineTo(t.width/4,t.height/2);i.contextEl.lineTo(t.width/4,0);this._contextClosePath(t,i)},_drawDownArrow:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/4,0);i.contextEl.lineTo(t.width/4,-t.height/2);i.contextEl.lineTo(t.width*(3/4),-t.height/2);i.contextEl.lineTo(t.width*(3/4),0);i.contextEl.lineTo(t.width,0);i.contextEl.lineTo(t.width/2,t.height/2);this._contextClosePath(t,i)},_drawLeftArrow:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.moveTo(n.startX,n.startY);i.contextEl.lineTo(n.startX+t.width/2,n.startY+-t.height/2);i.contextEl.lineTo(n.startX+t.width/2,n.startY+-t.height/4);i.contextEl.lineTo(n.startX+t.width,n.startY+-t.height/4);i.contextEl.lineTo(n.startX+t.width,n.startY+t.height/4);i.contextEl.lineTo(n.startX+t.width/2,n.startY+t.height/4);i.contextEl.lineTo(n.startX+t.width/2,n.startY+t.height/2);i.contextEl.lineTo(n.startX,n.startY);this._contextClosePath(t,i)},_drawRightArrow:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.lineTo(n.startX,n.startY);i.contextEl.lineTo(n.startX,n.startY+-t.height/4);i.contextEl.lineTo(n.startX+t.width/2,n.startY+-t.height/4);i.contextEl.lineTo(n.startX+t.width/2,n.startY+-t.height/2);i.contextEl.lineTo(n.startX+t.width,n.startY);i.contextEl.lineTo(n.startX+t.width/2,n.startY+t.height/2);i.contextEl.lineTo(n.startX+t.width/2,n.startY+t.height/4);i.contextEl.lineTo(n.startX,n.startY+t.height/4);i.contextEl.lineTo(n.startX,n.startY);this._contextClosePath(t,i)},_drawTrapezoid:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(0,-t.height/4);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);i.contextEl.lineTo(0,t.height/4);this._contextClosePath(t,i)},_drawRectangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(0,-t.height/2);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);i.contextEl.lineTo(0,t.height/2);this._contextClosePath(t,i)},_drawRoundedRectangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(t.radius,-t.height/2);i.contextEl.lineTo(t.width-t.radius,-t.height/2);i.contextEl.quadraticCurveTo(t.width,-t.height/2,t.width,-t.height/2+t.radius);i.contextEl.lineTo(t.width,t.height/2-t.radius);i.contextEl.quadraticCurveTo(t.width,t.height/2,t.width-t.radius,t.height/2);i.contextEl.lineTo(t.radius,t.height/2);i.contextEl.quadraticCurveTo(0,t.height/2,0,t.height/2-t.radius);i.contextEl.lineTo(0,-t.height/2+t.radius);i.contextEl.quadraticCurveTo(0,-t.height/2,t.radius,-t.height/2);this._contextClosePath(t,i)},_drawCustomImage:function(t,i){var r=new Image;n(r).on("load",function(){t.contextEl.drawImage(this,0,0,t.model.width,t.model.height);t.model.scales!=null&&t._drawScales();t.model.items!=null&&t._renderItems()}).attr("src",i)},_drawIndicatorEllipse:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.moveTo(n.startX,n.startY);i.contextEl.bezierCurveTo(n.startX,n.startY-t.height/2,n.startX+t.width,n.startY-t.height/2,n.startX+t.width,n.startY);i.contextEl.bezierCurveTo(n.startX+t.width,n.startY+t.height/2,n.startX,n.startY+t.height/2,n.startX,n.startY);this._contextClosePath(t,i)},_drawEllipse:function(n,t,i){var r=Math.sqrt(t.height*t.height+t.width*t.width)/2;t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.scale(2,1);i.contextEl.arc(r/2,0,r/2,0,Math.PI*2,!0);this._contextClosePath(t,i)},_setPointerDimension:function(n,i){if(!t.isNullOrUndefined(i.model.Orientation)&&i.model.Orientation=="Horizontal"){var r=n.width,u=n.height;n.height=r;n.width=u}return n},_setContextRotation:function(n,t){t.contextEl.rotate(Math.PI*(n.angle/180))},_contextOpenPath:function(n,t){t.contextEl.save();t.contextEl.beginPath();n.strokeStyle&&(t.contextEl.strokeStyle=n.strokeStyle);n.opacity!=i&&(t.contextEl.globalAlpha=n.opacity);n.lineWidth&&(t.contextEl.lineWidth=n.lineWidth);n.fillStyle&&(t.contextEl.fillStyle=n.fillStyle)},_contextClosePath:function(n,t){t.contextEl.closePath();n.isFill&&t.contextEl.fill();n.isStroke&&t.contextEl.stroke();t.contextEl.restore()},_blockDefaultActions:function(n){n.cancelBubble=!0;n.returnValue=!1;n.preventDefault&&n.preventDefault();n.stopPropagation&&n.stopPropagation()},_isBetween:function(n,t,i,r){return n<t?i>=n-r&&i<=t+r:i>=t-r&&i<=n+r},_getColor:function(n,t){return typeof t=="string"?t:"rgba("+t.r+", "+t.g+","+t.b+", "+t.a/255+")"},_setGradientColor:function(t,i,r){var u=t;r.Name||typeof r=="string"?(i.addColorStop(0,this._getColor(t,r)),i.addColorStop(1,this._getColor(t,r))):n.each(r,function(n,r){i.addColorStop(r.colorStop!=NaN?r.colorStop:0,typeof r.color=="string"?r.color:u._getColor(t,r.color))})}}),function(){for(var u=0,f=["ms","moz","webkit","o"],i,r,n=0;n<f.length&&!window.requestAnimationFrame;++n)window.requestAnimationFrame=window[f[n]+"RequestAnimationFrame"];window.requestAnimationFrame||(t.datavisualization.CircularGauge.animationPolyfill=!0,window.requestAnimationFrame=function(n,t){return i=(new Date).getTime(),r=Math.max(0,16-(i-u)),t=window.setTimeout(function(){n(i+r)},r),u=i+r,t})}();t.datavisualization.CircularGauge.Frame={FullCircle:"fullcircle",HalfCircle:"halfcircle"};t.datavisualization.CircularGauge.Directions={Clockwise:"clockwise",CounterClockwise:"counterclockwise"};t.datavisualization.CircularGauge.PointerPlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.CircularGauge.PointerType={Needle:"needle",Marker:"marker"};t.datavisualization.CircularGauge.NeedleType={Triangle:"triangle",Rectangle:"rectangle",Trapezoid:"trapezoid",Arrow:"arrow",Image:"image"};t.datavisualization.CircularGauge.MarkerType={Rectangle:"rectangle",Triangle:"triangle",Ellipse:"ellipse",Diamond:"diamond",Pentagon:"pentagon",Circle:"circle",Slider:"slider",Pointer:"pointer",Wedge:"wedge",Trapezoid:"trapezoid",RoundedRectangle:"roundedrectangle",Image:"image"};t.datavisualization.CircularGauge.RangePlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.CircularGauge.TickType={Major:"major",Minor:"minor"};t.datavisualization.CircularGauge.gaugePosition={TopLeft:"topleft",TopRight:"topright",TopCenter:"topcenter",MiddleLeft:"middleleft",MiddleRight:"middleright",Center:"center",BottomLeft:"bottomleft",BottomRight:"bottomright",BottomCenter:"bottomcenter"};t.datavisualization.CircularGauge.TickPlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.CircularGauge.CustomLabelPositionType={Inner:"inner",Outer:"outer"};t.datavisualization.CircularGauge.OuterCustomLabelPosition={Left:"left",Right:"right",Top:"top",Bottom:"bottom"};t.datavisualization.CircularGauge.LabelPlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.CircularGauge.LabelType={Major:"major",Minor:"minor"};t.datavisualization.CircularGauge.UnitTextPlacement={Back:"back",Front:"front"};t.datavisualization.CircularGauge.RangeZOrderPosition={Rear:"rear",Front:"front"};t.datavisualization.CircularGauge.IndicatorType={Rectangle:"rectangle",Circle:"circle",RoundedRectangle:"roundedrectangle",Text:"text",Image:"image",Cross:"cross",Diamond:"diamond",DownArrow:"downarrow",Ellipse:"ellipse",HorizontalLine:"horizontalLine",InvertedTriangle:"invertedtriangle",LeftArrow:"leftarrow",Pentagon:"pentagon",RightArrow:"rightarrow",Star:"star",Trapezoid:"trapezoid",Triangle:"triangle",UpArrow:"uparrow",VerticalLine:"verticalline",Wedge:"wedge"};t.datavisualization.CircularGauge.Themes={FlatLight:"flatlight",FlatDark:"flatdark"};t.datavisualization.CircularGauge.LegendPosition={Top:"top",Bottom:"bottom",Left:"left",Right:"right"};t.datavisualization.CircularGauge.LegendAlignment={Near:"near",Center:"center",Far:"far"};t.datavisualization.CircularGauge.LegendShape={Rectangle:"rectangle",Triangle:"triangle",Diamond:"diamond",Pentagon:"pentagon",Circle:"circle",Slider:"slider",Wedge:"wedge",Trapezoid:"trapezoid",Line:"line"}})(jQuery,Syncfusion)});
