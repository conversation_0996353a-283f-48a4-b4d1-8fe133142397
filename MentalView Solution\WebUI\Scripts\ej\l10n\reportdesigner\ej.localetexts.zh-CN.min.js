/*!
*  filename: ej.localetexts.zh-CN.min.js
*  version : *********
*  Copyright Syncfusion Inc. 2001 - 2020. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
ej.ReportDesigner.Locale["zh-CN"]={itemPanel:{waterMarkText:"搜索小工具",noDataFound:"找不到匹配项...",customCategory:"条形码",customRptItemName:"1D条码",dataRequirements:"數據要求",customTooltip:{tooltip:{requirements:"將報表項添加到設計器區域。",description:"顯示自定義報告項目。",title:"自定義報告項目"}},groupItems:{basicItems:{groupName:"基本项目",Items:{line:{displayText:"線",tooltip:{requirements:"通過報告部分中的線分隔區域。",description:"用於分隔報告區域的圖形元素。",title:"線"}},image:{displayText:"圖片",tooltip:{requirements:"要顯示數據庫中的圖像，請嵌入圖像。",description:"顯示圖像。",title:"圖片"}},textBox:{displayText:"文本框",tooltip:{requirements:"添加任何文字。",description:"顯示靜態和動態文本。",title:"文本框"}},rectangle:{displayText:"長方形",tooltip:{requirements:"將一個或多個報表項合併到其中。",description:"圖形容器元素。",title:"長方形"}}}},comparison:{groupName:"對照",Items:{column:{displayText:"柱",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"使用水平排列的垂直條比較各種類別中的一組無序項的值。",title:"柱"}},bar:{displayText:"酒吧",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"使用垂直排列的水平條比較各種類別中的一組無序項的值。",title:"酒吧"}},stackedColumn:{displayText:"堆積柱",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"使用垂直堆疊的條比較多個度量。",title:"堆積柱"}},stackedBar:{displayText:"堆積吧",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"使用水平堆疊的條比較多個度量。",title:"堆積吧"}},stackedColumnPercent:{displayText:"堆積柱100％",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"使用垂直堆疊的條將多個度量作為整體的一部分進行比較。",title:"堆積柱100％"}},stackedBarPercent:{displayText:"堆積條100％",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"使用水平堆疊的條將多個度量作為整體的一部分進行比較。",title:"堆積條100％"}}}},proportion:{groupName:"比例",Items:{pie:{displayText:"餡餅",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"以餅圖的形式展示每個項目對總數的貢獻比例。",title:"餡餅"}},explodedPie:{displayText:"爆炸的餡餅",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"強調餅圖的單個切片。",title:"爆炸的餡餅"}},doughnut:{displayText:"甜甜圈",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"以甜甜圈切片的形式展示每個項目對總數的貢獻比例。",title:"甜甜圈"}},pyramid:{displayText:"金字塔",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"以逐步增加的方式展示值之間的比例比較。",title:"金字塔"}},funnel:{displayText:"漏斗",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"以逐漸減少的方式展示值之間的比例比較。",title:"漏斗"}}}},distribution:{groupName:"Distribution",Items:{area:{displayText:"區",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"通過垂直排序的填充曲線比較不同類別的一組無序項的值。",title:"區"}},smoothArea:{displayText:"光滑的區域",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"通過垂直排列的具有光滑表面的填充曲線，比較不同類別中的一組無序項的值。",title:"光滑的區域"}},stackedArea:{displayText:"堆積區域",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"通過垂直堆疊的填充曲線比較多個度量。",title:"堆積區域"}},stackedAreaPercent:{displayText:"堆積面積100％",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"通過垂直堆疊的填充曲線將多個度量作為整體的一部分進行比較。",title:"堆積面積100％"}},line:{displayText:"線",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"展示使用直線連接數據點的一段時間內的分析趨勢。",title:"線"}},smoothLine:{displayText:"流暢的線條",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"比較使用平滑線連接的時間段內的值分佈。",title:"流暢的線條"}},steppedLine:{displayText:"階梯線",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"比較使用階梯線連接的時間段內的值分佈。",title:"階梯線"}},lineWithMarkers:{displayText:"與標記線",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"比較多個組在同一時間段內的更改。",title:"與標記線"}},smoothLineWithMarkers:{displayText:"標記線條流暢",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"繪製的值用標記點表示，這些點用平滑線連接。",title:"標記線條流暢"}},scatter:{displayText:"分散",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"將一系列顯示為一組點，並且值由圖表上的點的位置表示。",title:"分散"}},bubble:{displayText:"泡沫",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"根據氣泡的大小顯示數據點的兩個值之間的差異。",title:"泡沫"}},polar:{displayText:"極性",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"將一系列顯示為一組在360度圓上按類別分組的點。",title:"極性"}},radar:{displayText:"雷達",tooltip:{requirements:"1個或更多個值和1個或更多列。",description:"將系列顯示為圓形線或區域。",title:"雷達"}}}},dataRegions:{groupName:"數據區域",Items:{tablix:{displayText:"表",tooltip:{requirements:"1行或多行。",description:"顯示單元格中的分頁報告數據。",title:"表"}},list:{displayText:"名单",tooltip:{requirements:"1行或多行。",description:"列表以自由格式显示数据。将字段放在列表中的任何位置。",title:"名单"}}}},subReports:{groupName:"數據要求",Items:{subreport:{displayText:"子報表",tooltip:{requirements:"在主報告中顯示/嵌入報告。",description:"在主報表正文中顯示另一個報表。",title:"子報表"}}}}}},toolbar:{newReport:"新",open:"打开",openMenu:{fromDevice:"从设备",fromServer:"从服务器"},save:"保存",saveMenu:{saveLabel:"保存",saveAs:"另存为",saveAsMenu:{saveToDevice:"到设备",saveToServer:"到服务器"}},cut:"切",copy:"复制",paste:"糊",deleteItem:"删除",undo:"解开",redo:"重做",zoomIn:"放大",zoomOut:"缩小",header:"头",footer:"页脚",order:"订购",orderMenu:{sendBackward:"向後發送",bringForward:"向前進",sendToBack:"發送回",bringToFront:"帶到前面"},left:"左对齐",center:"中央",right:"对齐",top:"顶部对齐",middle:"中间",bottom:"底部对齐",distributeHorizontally:"水平分布",distributeVertically:"垂直分布",centerHorizontally:"水平居中",centerVertically:"垂直居中",sizing:"浆纱",sizingMenu:{sameSize:"相同的大小",sameWidth:"相同的宽度",sameHeight:"同样的高度"},alignToGrid:"对齐网格",sizeToGrid:"尺寸到网格",gridLine:"网格线",snapToShape:"捕捉形状",fullScreen:"全屏",preview:"预习",reportUpload:{alertLabel:"上传",alertMessage:"上传文件时出错。请重新上传"},grouping:"分组",view:"視圖"},newReport:{cancel:"取消",create:"创建",title:"新报告",fileName:"文件名",waterMark:"报告名称",close:"关"},reportAction:{enableLink:"启用链接",linkTo:"链接到",report:"报告",url:"网址"},linkReport:{reportCaption:"报告",setParameter:"设置参数"},imageProperty:{basicSettings:{categoryName:"基本设置",source:"资源",sourceTypes:{external:"外部",embedded:"嵌入式",database:"数据库"},value:"值",mimeType:"MIME类型",mimeTypes:{bmp:"图像/ BMP",jpeg:"图像/ JPEG",gif:"图像/ GIF",png:"图像/ PNG",xPng:"图像/ X-PNG"}},categoryName:"链接",linkReport:"链接报告",appearance:{categoryName:"出现",styleTooltip:"樣式",colorTooltip:"顏色",sizeTooltip:"尺寸",borderTypes:{border:"边境",borderLeft:"剩下",borderTop:"最佳",borderRight:"对",borderBottom:"底部"},borderStyles:{solid:"固体",none:"没有",double:"双",dashed:"虚线",dotted:"带点"}},size:{categoryName:"尺寸",paddingTypes:{padding:"填充",paddingLeft:"剩下",paddingTop:"最佳",paddingRight:"对",paddingBottom:"底部"},sizing:"浆纱",sizeTypes:{auto:"自动尺寸",fit:"适合",proportional:"适合比例",clip:"夹"}},position:{categoryPosition:"位置",positionLabel:"位置",left:"剩下",top:"最佳",sizeLabel:"尺寸",width:"宽度",height:"高度"},visibility:{categoryName:"能见度",visible:"可见",toggleItem:"切換項目"}},chartProperty:{commonProperties:{showBorder:"显示边框",border:{border:"边境",borderLeft:"剩下",borderTop:"最佳",borderRight:"对",borderBottom:"底部"},background:"背景颜色",font:"字形",fontStyle:"字体样式",labelRotation:"标签旋转",categoryAxis:"类别轴",valueAxis:"价值轴",defaultText:"默认",auto:"汽车",borderStyles:{solid:"固体",none:"没有",double:"双",dashed:"虚线",dotted:"带点",dashDot:"短跑点",dashDotDot:"点划线点"},horizontalAlignments:{near:"近",far:"远"},textAlignments:{right:"对",bottom:"底部",center:"中央",topLeft:"左上方",topCenter:"顶尖中心",topRight:"右上",rightTop:"右上角",rightCenter:"正确的中心",rightBottom:"右下",bottomLeft:"左下方",bottomCenter:"底部中心",bottomRight:"底部右侧",leftTop:"左上方",leftCenter:"左中心",leftBottom:"左下"},fontStyleTypes:{normal:"正常",italic:"斜体"},fontWeightTypes:{light:"光",bold:"胆大"}},basicSettings:{categoryName:"基本设置",showLegend:{showLegendText:"显示图例",title:"标题",titleFont:"标题字体",titleFontStyle:"标题字体样式",titleAlignment:"标题对齐",legendPosition:"传奇位置",enableCustomBounds:"启用自定义边界"},chooseSeries:"选择系列",showMarker:{showMarkerText:"显示标记",color:"颜色",markerType:"标记类型",markerTypes:{square:"广场",circle:"圈",diamond:"钻石",triangle:"三角形",cross:"交叉",star5:"明星5"},size:"尺寸"},showDataLabel:{showDataLabelText:"显示数据标签",dataLabelFormat:"格式",dataLabelText:"标签",dataLabelValueAsText:"使用值作为标签",dataLabelTypes:{valueX:"#VALX",valueY:"#VALY",valueY2:"#VALY2",valueY3:"#VALY3",valueY4:"#VALY4",valueY5:"#VALY5",valueY6:"#VALY6",index:"#指数",percent:"#百分",total:"#总",axisLabel:"#轴标签"}},enableSmartLabel:{smartLabelText:"启用智能标签",labelStyle:"标签样式",value:"值",smartLabelPositions:{outside:"外",inside:"内",outsideInColumn:"列在外面"},smartLabelStyles:{pieLabelStyle:"馅饼标签样式",funnelLabelStyle:"漏斗标签样式",pyramidLabelStyle:"金字塔标签样式",barLabelStyle:"酒吧标签样式",smartLabelStyle:"标签样式"}},seriesBorder:"系列边框",seriesColor:"系列颜色"},categoryName:"出现",customAttribute:{categoryName:"自定义属性",userDefined:"用户自定义",alertHeader:"圖表報告項目",alertMessage:"自定义属性格式无效。 正确的例子：'AttrName1 = Value1，AttrName2 = Value2 '."},chartArea:{categoryName:"图表区域",colorPalette:"调色板",colorPaletteTypes:{earthTones:"地球色调",excel:"高强",grayScale:"灰阶",pastel:"粉彩",semiTransparent:"半透明",berry:"浆果",chocolate:"巧克力",fire:"火",seaGreen:"海绿色",brightPastel:"明亮的粉彩",pacific:"和平的",pacificLight:"太平洋之光",pacificSemiTransparent:"和平的 半透明"}},title:{categoryName:"标题",showChartTitle:"显示图表标题",titleText:"标题文本",titlePosition:"标题位置"},axis:{enableAxis:"启用轴",axisTitle:"轴标题",alignment:"对准",lineStyle:"线条样式",labelOverflowMode:"标签溢出模式",overFlowModeTypes:{trim:"修剪",hide:"隐藏"},labelFont:"标签字体",labelFormat:"標籤格式",enableMajorTicks:"启用主要蜱虫",enableMinorTicks:"启用小窍门",tickProperties:{tickSize:"勾号大小",tickColor:"勾选颜色",tickWidth:"宽度",length:"长度"},tickPosition:"勾号位置"},gridLine:{categoryName:"网格线",gridLineStyle:{minorGridLine:"显示小网格线",majorGridLineStyle:"主要网格线样式",minorGridLineStyle:"次要网格线样式"}},pageBreak:{categoryName:"分页符",enablePageBreak:"启用分页符",breakLocation:"休息地点",breakLocationTypes:{none:"没有",start:"开始",end:"结束",startAndEnd:"开始和结束",between:"之间"},pageNumberReset:"页码重置",pageName:"页面名称"},position:{categoryPosition:"位置",positionLabel:"位置",left:"剩下",top:"最佳",sizeLabel:"尺寸",width:"宽度",height:"高度"},visibility:{categoryName:"能见度",visible:"可见",toggleItem:"切換項目"},fontStyleTooltip:"樣式",fontWeightTooltip:"重量",fontSizeTooltip:"尺寸",fontColorTooltip:"顏色",fontFamilyTooltip:"字體系列",styleTooltip:"樣式",colorTooltip:"顏色",sizeTooltip:"尺寸"},lineProperty:{basicSettings:{categoryBasicSettings:"基本设置",line:"线",lineTypes:{solid:"固体",dashed:"虚线",dotted:"带点"}},position:{categoryPosition:"位置",positionLabel:"位置",left:"剩下",top:"最佳",sizeLabel:"尺寸",width:"宽度",height:"高度"},visibility:{categoryName:"能见度",visible:"可见",toggleItem:"切換項目"},styleTooltip:"樣式",colorTooltip:"顏色",sizeTooltip:"尺寸"},subReportProperty:{basicSettings:{categoryBasicSettings:"基本设置"},appearance:{categoryAppearance:"出现",borderTypes:{border:"边境",borderLeft:"剩下",borderTop:"最佳",borderRight:"对",borderBottom:"底部"},borderStyles:{solid:"固体",none:"没有",double:"双",dashed:"虚线",dotted:"带点"}},noRows:{noRowsLabel:"没有行",font:"字形",fontStyle:{fontStyleLabel:"字体样式",fontItem:{defaultStyle:"默认",fontNormal:"正常",italic:"斜体"},fontWeight:{defaultElement:"默认",normal:"正常",thin:"瘦",extraLight:"额外的光",light:"光",medium:"中",semiBold:"半粗体",bold:"胆大",extraBold:"特大号",heavy:"重"}},textDecoration:{textDecorationLabel:"文本装饰",defaultDecoration:"默认",none:"没有",underLine:"强调",overLine:"上划线",lineThrough:"通过"},format:"格式",lineHeight:"线条高度",message:"信息",paddingTypes:{padding:"填充",paddingLeft:"剩下",paddingRight:"对",paddingTop:"最佳",paddingBottom:"底部"},textAlign:{textAlignLabel:"文本对齐",textAlignDefault:"默认",textAlignGeneral:"一般",textAlignLeft:"剩下",textAlignRight:"对",textAlignCenter:"中央"},verticalAlign:{verticalAlignlabel:"垂直对齐",verticalAlignDefault:"默认",verticalAlignTop:"最佳",verticalAlignMiddle:"中间",verticalAlignBottom:"底部"},writingMode:{writingModeLabel:"写模式",writingModeDefault:"默认",writingModeHorizontal:"横",writingModeVertical:"垂直",writingModeRotate:"旋转270"}},visibility:{categoryName:"能见度",visible:"可见",toggleItem:"切換項目"},position:{categoryPosition:"位置",positionLabel:"位置",left:"剩下",top:"最佳",sizeLabel:"尺寸",width:"宽度",height:"高度"},miscellaneous:{categoryMiscellaneous:"杂",keepTogether:"保持在一起"},fontStyleTooltip:"樣式",fontWeightTooltip:"重量",fontSizeTooltip:"尺寸",fontColorTooltip:"顏色",fontFamilyTooltip:"字體系列",styleTooltip:"樣式",colorTooltip:"顏色",sizeTooltip:"尺寸"},rectangleProperty:{basicSettings:{categoryBasicSettings:"基本设置",styleTooltip:"樣式",colorTooltip:"顏色",sizeTooltip:"尺寸",borderTypes:{border:"边境",borderLeft:"剩下",borderTop:"最佳",borderRight:"对",borderBottom:"底部"},borderStyles:{solid:"固体",none:"没有",double:"双",dashed:"虚线",dotted:"带点"},backGround:"背景颜色"},pageBreak:{pageBreak:"分页符",enablePageBreak:{enablePageBreak:"启用分页符",breakLocation:{breakLocationLabel:"休息地点",none:"没有",start:"开始",end:"结束",startAndEnd:"开始和结束",between:"之间"},pageNumberReset:"页码重置"}},position:{categoryPosition:"位置",positionLabel:"位置",left:"剩下",top:"最佳",sizeLabel:"尺寸",width:"宽度",height:"高度"},visibility:{categoryName:"能见度",visible:"可见",toggleItem:"切換項目"},rectangleMiscellaneous:{categoryMiscellaneous:"杂",keepTogether:"保持在一起",pageName:"页面名称"}},browseFile:{openFile:{selectReport:"选择报告",open:"打开"},saveFile:{saveAsReport:"另存为报告",name:"名称",save:"保存"},close:"关",cancel:"取消",waterMark:"报告名称",emptyMessage:"此类别为空",alertMessage:{reportServer:"报表服务器",selectCategory:"请选择类别"},warningMessage:{fileNameLabel:"一个物品",fileNameExist:".rdl“已经存在，你想替换现有的项目吗？",populateCategory:"报表设计器无法从报表服务器检索资源"}},expressionMenu:{reset:"重启",expression:"表达",advanced:"高级"},propertyPanel:{property:"性能",data:"数据",name:"名称",toolTipStyle:"样式",toolTipColor:"颜色",toolTipWidth:"宽度",setSorts:"设置分类",setFilters:"设置过滤器",advancedOptions:"高级选项",codemodules:"码",expressionList:{top:"最佳",right:"对",bottom:"底部",left:"剩下",style:"样式",color:"颜色",size:"尺寸",fontFamily:"字体系列",width:"宽度",height:"高度",weight:"重量",image:"图片"},alertMessage:{nameWarning:"名称不能为空",nameAlert:"名称已存在",nameValidation:"名称不应包含空格和特殊字符"},unitType:{inchText:"在",centimeterText:"厘米",pixelText:"像素",pointText:"點",millimeterText:"毫米",picaText:"異食癖"},setGroups:"设置组",addDatasource:"添加数据源",dataAlertMsg:"没有添加数据源 ！"},dataSource:{newDatasource:"新数据库",datasource:"数据源",datasourceList:{data:"数据",contextMenu:{editItem:"编辑",deleteItem:"删除",createDataSet:"创建数据集",cloneDatasource:"克隆"}},datasourceType:{existOption:"现有",newOption:"创建新的",selectDatasoure:"选择数据源",connectDatasource:"连接数据源",datasourceType:"选择要连接的类型",sqlLabel:"SQL",sqlCeLabel:"SQLCE",odbcLabel:"ODBC",oracleLabel:"ORACLE",oledbLabel:"OLEDB",xmlLabel:"XML",sharedLabel:"共享"},datasourceConnection:{newConnection:"新的连接",editConnection:"编辑连接",name:"名称",save:"保存",connect:"连",cancel:"取消"},sqlDatasource:{authenticationType:"认证类型",window:"视窗",sqlServer:"SQL 服务器",userName:"用户名",password:"密码",switchLabel:"数据源 预先小组",switchAlert:"切换到视觉设计师将放弃手动更改 连接字符串。你是否想要使用视觉设计师?",basicOption:{serverName:"服务器名称",savePassword:"保存密码",database:"数据库",advanceSwitch:"高级选项"},advanceOption:{connectionString:"连接字符串",promptLabel:"提示文字",prompt:"提示",none:"没有",savePassword:"保存密码",basicSwitch:"基本选项"},alertMessage:{alertConnectionString:"指定连接字符串",alertPrompt:"指定提示文本",alertUserName:"指定用户名",alertPassword:"指定密码",alertServerName:"指定服务器名称",alertDatabaseName:"指定数据库名称"}},sqlceDatasource:{connectionString:"连接字符串",authenticationType:"认证类型",authentication:"认证",none:"没有",password:"密码",savePassword:"保存密码",alertMessage:{alertConnectionString:"指定连接字符串",alertPassword:"指定密码"}},odbcDatasource:{connectionString:"连接字符串",authenticationType:"认证类型",authentication:"认证",prompt:"提示",none:"没有",userName:"用户名",password:"密码",promptLabel:"提示文字",savePassword:"保存密码",alertMessage:{alertConnectionString:"指定连接字符串",alertPrompt:"指定提示文本",alertUserName:"指定用户名",alertPassword:"指定密码"}},oracleDatasource:{connectionString:"连接字符串",authenticationType:"认证类型",authentication:"认证",prompt:"提示",none:"没有",userName:"用户名",password:"密码",promptLabel:"提示文字",savePassword:"保存密码",alertMessage:{alertConnectionString:"指定连接字符串",alertPrompt:"指定提示文本",alertUserName:"指定用户名",alertPassword:"指定密码"}},oledbDatasource:{connectionString:"连接字符串",authenticationType:"认证类型",authentication:"认证",prompt:"提示",none:"没有",userName:"用户名",password:"密码",promptLabel:"提示文字",savePassword:"保存密码",alertMessage:{alertConnectionString:"指定连接字符串",alertPrompt:"指定提示文本",alertUserName:"指定用户名",alertPassword:"指定密码"}},xmlDatasource:{connectionString:"连接字符串"},sharedDatasource:{datasource:"共享数据源",alertMessage:"选择一个共享的数据源"},alertMessage:{alertLabel:"数据源",deleteValue:"删除数据源 '",alertConnectionFailed:"报表设计器 无法连接数据源",dataExtensionFailed:"所选数据提供程序不可用。 请检查数据扩展名。",connectStringValidation:"由于连接字符串包含数据源中的表达式 ",validationMessage:" 请使用有效的连接字符串进行更新.",executionMessage:", 我们无法执行此连接的数据集.",confirmMessage:" 您确定要保存数据源吗？",nameWarning:"指定数据源名称",nameAlert:"指定的名称已存在于“数据源”列表中",nameValidation:"名称不应包含空格和特殊字符"}},imageManager:{headerText:"图像管理器",addImageButton:"添加图片",deleteImage:"删除嵌入的图像",image:"图片"},linkParameter:{title:"参数",headerTxt:"链接参数",descriptionText:"报告参数",addText:"加",ok:"好",cancel:"取消",nameWaterMark:"参数名称",valueWaterMark:"值",errorMessage:"为此属性输入一个值",closeToolTip:"关"},filter:{title:"过滤",descriptionLable:"包含满足以下条件的行.",add:"加",ok:"好",cancel:"取消",valueWaterMark:"值",fieldWaterMark:"选择字段",closeToolTip:"关",errorMessage:{booleanValidation:"值不是布尔值.",intValidation:"值不是整数.",floatValidation:"价值不是浮动.",dateTimeValidation:"值是无效的日期/时间格式.",topBottomFilter:"Top％和Bottom％过滤器运算符需要float或integer数据类型。",expressionValidation:"为表达式字段选择值"},operatorTypes:{like:"喜欢",topN:"最佳N",bottomN:"底部N",topPercent:"最佳%",bottomPercent:"底部%",between:"之间",inFilter:"在"}},dataField:{title:"数据字段",descriptionLable:"包括数据字段行",add:"加",ok:"好",cancel:"取消",fieldNameWaterMark:"字段名称",closeToolTip:"关",errorMessages:{emptyField:"指定字段名称",invalidCharacters:"字段名称不应包含空格和特殊字符",sameCharacter:"字段名称已存在"},dsNameLabel:"名稱",dsNameWaterMark:"數據名稱",dsNameValidation:{nameWarning:"指定數據集名稱",nameAlert:"指定的名稱已存在於“數據集”列表中",nameValidation:"數據集名稱不應包含空格和特殊字符"}},dataPanel:{itemTooltip:{properties:"属性",data:"数据",parameters:"参数",imageManager:"图像管理器",expand:"扩大",collapse:"坍方"},dataSourceNewAlert:{title:"数据源",contentMessage:"你想取消创建数据源吗？?"},dataSourceEditAlert:{title:"数据源",contentMessage:"你想取消数据源编辑?"},dataSetNewAlert:{title:"数据集",contentMessage:"你想取消创建 数据集 吗?"},dataSetEditAlert:{title:"数据集",contentMessage:"你想取消 数据集 编辑吗?"},parameterNewAlert:{title:"参数",contentMessage:"你想取消参数创建?"},parameterEditAlert:{title:"参数",contentMessage:"你想取消参数编辑?"}},dataSet:{headerText:"数据",newData:"添加数据集",shareDataset:{headerText:"新数据",editHeaderText:"編輯數據集",save:"保存",cancel:"取消",nameLable:"名称",sharedDatasetLabel:"共享数据集",errorMessage:{nameValidation:"指定 数据集 名称",datasetValidation:"选择一个共享的数据源",duplicateName:"指定的名称已经存在于数据集列表中",specialCharacter:"名称不应包含空格和特殊字符"}},contextMenu:{edit:"编辑",remove:"删除",cloneDataset:"克隆",filter:"过滤",setField:"字段"},datasourceSwitcher:"数据源",deleteDataset:"删除数据集",deleteField:"删除字段",newDataText:"新數據",sharedDataText:"共享數據",dataRestriction:{dsCreateRestriction:"數據源創建受到限制",title:"數據"},dataFieldSearch:{errorMessage:"找不到匹配项",searchText:"搜索"}},reportViewer:{toolbar:{print:"打印",exportText:"出口",pageFit:"适合页面",exportformat:{Pdf:"PDF",Excel:"高强",Word:"字",Html:"Html",PPT:"微軟幻燈片軟件",CSV:"CSV"},pageSetup:"页面设置",gotoFirst:"转到第一",gotoLast:"转到最后",gotoNext:"转到下一步",gotoPrevious:"转到上一页",gotoParanet:"转到父母",zoomIn:"放大",zoomOut:"缩小",fittopage:{pageWidth:"页面宽度",pageHeight:"整个页面"},printLayout:"打印布局",refresh:"刷新",documentMap:"文档图",parameter:"参数",viewDesign:"关闭预览"},pagesetupDialog:{close:"关",paperSize:"纸张大小",height:"高度",width:"宽度",margins:"边距",top:"最佳",bottom:"底部",right:"对",left:"剩下",unit:"在",orientation:"方向",portrait:"肖像",landscape:"景观",doneButton:"好",cancelButton:"取消"},credential:{userName:"用户名",password:"密码"},waterMark:{selectOption:"选择选项",selectValue:"选择一个值"},errorMessage:{startMessage:"报表查看器在加载此报表时遇到一些问题。请",middleMessage:"点击这里",endMessage:"查看错误的详细信息",closeMessage:"关闭此消息"},alertMessage:{close:"关",title:"的报告查看器",done:"好",showDetails:"显示详细资料",hideDetails:"隐藏细节",reportLoad:"加载报告：",RVERR0001:"报告查看器 无法加载报告",RVERR0002:"报告查看器 无法呈现报告",RVERR0003:"ajax回发中发生错误",RVERR0004:"请为参数选择一个值",RVERR0005:"{参数名称}参数缺少一个值",RVERR0006:"请给出浮点数据类型输入",RVERR0007:"请给出整数数据类型输入",RVERR0008:"报告查看器 无法验证数据源凭据",RVERR0009:"边距重叠，或者它们不在纸上。输入不同的边距大小。",RVERR0010:"请为参数输入一个值",RVERR0011:"该参数不能为空",RVERR0012:"为报告参数{参数提示}提供的值对其类型无效."},selectAll:"全选",viewButton:"查看报告"},sortData:{sorting:"排序",headerText:"排序过滤器",add:"加",changeSortingOptions:"更改排序选项.",sortBy:"排序方式",thenBy:"然后通过",direction:{ascending:"上升",descending:"降序"},chooseField:"选择字段",errorMessage:"为表达式字段选择值",ok:"好",cancel:"取消",close:"关"},groupData:{grouping:"分组",headerTxt:"组",headerTxtLabel:"组标签",name:"名称",label:"标签",changeGroupingOptions:"更改分组选项.",add:"加",groupBy:"通过...分组",andOn:"并且",chooseField:"选择字段",ok:"好",cancel:"取消",close:"关",errorMessage:{nameErrorMessage:"請輸入有效名稱",expressionErrorMessage:"为表达式字段选择值"}},alertMessage:{yes:"是",no:"没有",showDetails:"显示详细资料",hideDetails:"隐藏细节",close:"关"},parameter:{listPanel:{headerText:"参数",newParameter:"新参数",editMenu:{edit:"编辑",remove:"删除"},alertTitle:"参数"},configurationPanel:{newHeaderText:"新参数",editHeaderText:"编辑参数",nameLabel:"名称",promptLable:"提示",dataTypeLable:"数据类型",blankValueLable:'允许空白值 ("")',nullValueLable:"允许空值",multipleValueLable:"允许多个值",visibilityLable:"能见度",assignValueLable:"分配值 >>",save:"保存",cancel:"取消",visibility:{visible:"可见",hidden:"隐",internal:"内部"},dataType:{stringType:"串",booleanType:"布尔",dateTimeType:"约会时间",integerType:"整数",floatType:"浮动"}},errorMessage:{nameField:"请输入名称",promptField:"请输入值",nameAlreadyExists:"参数名称已存在"},warningMessage:{specialCharacter:"名称不应包含空格和特殊字符",multipleValueAlert:"指定了多个默认值。该参数不允许多个值.",nullValueAlert:"在值字段中，指定了空值。该参数不允许空值."},alertMessage:{confirmNullCheck:"可用或默认值可能包含空值。是否要启用允许空值复选框？",confirmBlankValue:"可用或默认值可能包含空白值。是否要启用空白值复选框？",dataTypeChange:"更改数据类型将放弃对可用值和默认值所做的更改. 无论如何，你是否想改变数据类型?",deleteAlert:"删除报告参数"},assignData:{title:"参数",availableValue:"可用值",defaultValue:"默认值",none:"没有",specify:"指定",query:"查询值",ok:"好",cancel:"取消",availableFields:{specifyDescriptionText:"添加参数的可用值:",queryDescriptionText:"为可用值选择数据集和字段:",nameFieldWaterMark:"标签",valueFieldWaterMark:"值"},defaultFields:{specifyDescriptionText:"添加参数的默认值:",queryDescriptionText:"选择数据集和字段作为默认值:",defValueWaterMark:"选择默认值"},datasetWaterMark:"选择数据集值",valueWaterMark:"选择价值",lableWaterMark:"选择标签",add:"加",datasetLableText:"数据集",valueLableText:"值字段",labelFieldText:"标签字段",errorMessage:{boolTypeCheck:"值不是布尔值.",dateTypeCheck:"值是无效的日期格式.",intTypeCheck:"值不是整数.",floatTypeCheck:"价值不是浮动.",multipleValuesCheck:"多值参数不能包含空值",datasetFieldCheck:"数据集字段是必需的.",valueFieldCheck:"值字段是必需的.",syntaxLabelField:"标签字段中输入的值不是有效的标记语法.",syntaxValueField:"值字段中输入的值不是有效的标记语法.",blankValueCheck:"值字段为空。该参数不允许空白值.",nullValueCheck:"在值字段中，指定了空值。该参数不允许空值."},closeToolTip:"关"}},formatData:{title:"格式对话框",typeSelect:"类型",typeFormat:{numberType:{numberType:"数",decimalPlaces:"小数位",negativeValues:"负值",showZeroAs:{showZeroAs:"显示为零",none:"(没有)"},representation:"表示",repDropDwn:{thousands:"成千上万",millions:"百万",billions:"数十亿"},useRegionFormating:"使用区域格式",use1000Separator:"使用1000分隔符 (,)"},currency:{currencyType:"货币",decimalPlaces:"小数位",negativeValues:"负值",cultureCurrency:"货币文化",showZeroAs:{none:"(没有)"},representation:"表示",repDropDwn:{thousands:"成千上万",millions:"百万",billions:"数十亿"},useRegionFormating:"使用区域格式",use1000Separator:"使用1000分隔符 (,)",includeSpace:"包含一个空格"},date:{dateType:"日期",date:"日期"},time:{timeType:"时间",time:"时间"},percentage:{percentageType:"百分比",decimalPlaces:"小数位",includeSpace:"包含一个空格"},scientific:{scientificType:"科学",decimalPlaces:"小数位"},custom:{customType:"习惯",customFormat:"自定义格式"}},preview:"预习",ok:"好",cancel:"取消",close:"关"},expression:{title:"表达",descriptionText:"设置表达式 : ",optionLabel:"选项",dataLabel:"数据",descritionLabel:"描述",exampleLabelText:"例",ok:"好",cancel:"取消",textAreaWaterMark:"表达",parameters:"参数",optionWaterMark:"选择一个选项",dataWaterMark:"选择一个数据",reportData:"没有找到报告数据",closeToolTip:"关",category:{builtInFields:"内置字段",operators:"运营商",functions:"功能"},description:{executionTime:"报告开始运行的日期和时间。",overallPageNumber:"当前整体页码只能用于页眉或页脚.",overallTotalPages:"报告中的页面总数只能用于页眉和页脚.",pageName:"报告中当前页面的名称只能用于页眉或页脚.",pageNumber:"当前页码可通过使用分页符重置",isInteractive:"指示当前呈现请求是否使用交互式格式的布尔值.",renderName:"在 RS 报表服务器 配置文件中注册的渲染器的名称.",reportFolder:"包含报告的文件夹的完整路径不包含报告服务器URL.",reportName:"运行报告的报告服务器的URL",reportServerUrl:"运行报表的报表服务器的URL.",totalPages:"当前连续页面序列中的页面总数 只能在页眉和页脚中使用. 该号码可以通过使用分页符重置.",language:"运行报表的客户端的语言ID.",userID:"运行报告的用户的ID.",powerNumberType:"将数字提高到另一个数字的大小.",multiply:"相乘两个数字.",integerDivision:"将两个数字相除并返回一个整数.",modulus:"将两个数字相除并仅返回余数.",add:"添加两个数字，可用于连接两个字符串.",floatDivision:"将两个数字相除并返回一个浮点数.",difference:"产生两个数字之间的差异或指示数字表达式的负值.",lesser:"少于.",lesserOrEqual:"小于或等于.",greater:"比...更棒.",greaterOrEqual:"大于或等于.",equal:"等于",notEqual:"不等于.",like:"比较两个字符串.",isOperator:"比较两个对象引用变量.",expression:"生成两个表达式的字符串连接.",stringType:"添加两个数字，它可以用来连接两个字符串。",and:"对两个布尔表达式执行逻辑连接，或对两个布尔表达式执行逻辑连接",not:"对布尔表达式执行逻辑否定或按位否定在数字表达式上.",or:"用于对两个布尔表达式执行逻辑异或，或者对两个数字值.",xor:"对两个布尔表达式执行逻辑排除操作，或按位排除在两个数字表达式上.",andAlso:"对两个表达式执行短路逻辑连接.",orElse:"用于对两个表达式执行短路逻辑析取.",left:"对位模式执行算术左移.",right:"对位模式执行算术右移.",asc:"返回表示与某个字符对应的字符代码的整数值.",ascW:"返回表示与某个字符对应的字符代码的整数值.",chr:"返回与指定字符代码关联的字符.",chrW:"返回与指定字符代码关联的字符.",filter:"根据指定的过滤条件返回包含字符串数组子集的从零开始的数组.",formatStringType:"根据格式字符串表达式中的指令返回格式化的字符串.",currency:"返回使用货币符号格式化为货币值的表达式.在系统控制面板中定义.",dateTime:"返回表示日期/时间值的字符串表达式.",numberType:"返回格式为数字的表达式.",percent:"返回格式为百分比的表达式（即乘以100）.",getChar:"返回一个char值，表示来自提供的字符串中指定索引的字符.",inStr:"返回一个整数，指定在另一个字符串中第一次出现的起始位置.",inStrRev:"从右侧开始，返回一个字符串中第一次出现的位置.的字符串.",join:"返回通过连接数组中的多个子字符串而创建的字符串.",lCase:"返回转换为小写字符串或字符.",leftStringType:"返回一个字符串，其中包含字符串左侧的指定数量的字符.",stringLength:"返回一个整数，其中包含字符串中的字符数或数字.",lSet:"返回包含调整为指定长度的指定字符串的左对齐字符串.",leftTrim:"返回给定字符串中没有左侧尾随空格的字符串.",middle:"返回包含字符串中指定数量字符的字符串.",replace:"返回指定的子字符串已被另一个字符串替换的字符串.",rightString:"从字符串的右侧返回一个包含指定数量字符的字符串.",rightSet:"返回包含调整为指定长度的指定字符串的右对齐字符串.",rightTrim:"返回给定字符串中没有右侧尾随空格的字符串.",stringSpace:"返回由指定数量的空格组成的字符串.",splitString:"返回包含指定数量的子字符串的基于零的一维数组.",strComp:"根据字符串比较的结果返回-1,0或1.",strConv:"返回按照指定转换的字符串.",duplicateString:"返回由指定字符组成的字符串或对象，重复指定的次数.",strReverse:"返回指定字符串的字符顺序颠倒的字符串.",trim:"返回给定字符串中没有尾随空格的字符串.",upperCase:"返回包含转换为大写字符的指定字符串的字符串或字符.",cDate:"转换为日期.",dateAdd:"返回包含添加了指定时间间隔的日期和时间值的日期值.",dateDiff:"返回一个长整型值，指定两个日期值之间的时间间隔数.",datePart:"返回包含给定日期值的指定组件的整数值.",dateSerial:"返回表示指定年份，月份和日期的日期值，并将时间信息设置为.午夜（00:00:00.",dateString:"根据您的系统返回或设置表示当前日期的字符串值.",dateValue:"返回包含日期信息的日期值，该日期信息由字符串表示，并带有时间信息",day:"返回从1到31的整数值，表示月份的某一天.",format:"返回表示日期/时间值的字符串表达式.",hour:"返回从0到23的整数值，表示一天中的小时.",minute:"返回从0到59的整数值，表示小时的分钟数.",month:"返回1到12的整数值，表示一年中的月份.",monthName:"返回包含指定月份名称的字符串值.",now:"根据您的系统返回包含当前日期和时间的日期值.",second:"返回从0到59的整数值，表示分钟的秒数.",timeOfDay:"根据您的系统返回或设置包含当前时间的日期值.",timer:"返回一个double值，表示从午夜开始经过的秒数.",timeSerial:"返回代表指定小时，分钟和秒的日期值，并设置日期信息.相对于1年1月1日.",timeString:"根据您的系统返回或设置表示当前时间的字符串值.",timeValue:"返回一个日期值，其中包含由字符串表示的时间信息，并设置日期信息.到第1年的1月1日.",timeToday:"根据您的系统返回或设置包含当前日期的日期值.",timeWeekday:"返回一个包含表示星期几的数字的整数值。",timeWeekdayName:"返回包含指定星期几的名称的字符串值.",year:"返回代表年份的从1到9999的整数值.",abs:"返回单精度浮点数的绝对值.",acos:"返回余弦为指定数字的角度.",asin:"返回其正弦为指定数字的角度.",atan:"返回其切线是指定数字的角度.",atan2:"返回其切线是两个指定数字的商的角度.",bigMultiply:"生成两个32位数字的完整产品.",ceiling:"返回大于或等于指定整数的最小整数。",cos:"返回指定角度的余弦.",cosh:"返回指定角度的双曲余弦.",exponent:"将e提升到指定的功率.",fixNumberType:"返回数字的整数部分.",floor:"返回小于或等于指定的最大整数",integer:"返回数字的整数部分.",logrithm:"返回指定数字的自然对数（基数e）.",logrithm10:"返回指定数字的10进制对数.",maximum:"返回指定表达式的所有非空值的最大值.",minimum:"返回指定表达式的所有非空值中的最小值.",power:"返回指定的数字，并将其提升到指定的功率.",random:"返回一个单一类型的随机数.",round:"将双精度浮点值舍入为最接近的整数.",sign:"返回一个值，指示8位有符号整数的符号.",sin:"返回指定角度的正弦值.",sinh:"返回指定角度的双曲正弦.",squareRoot:"返回指定数字的平方根.",tangent:"返回指定角度的正切值.",tangentH:"返回指定角度的双曲正切值.",isArray:"返回一个布尔值，指示变量是否指向数组.",isDate:"返回一个布尔值，指示表达式是否表示有效",isNothing:"返回一个布尔值，指示表达式是否没有对象",isNumeric:"返回一个布尔值，指示表达式是否可以评估为一个数字.",flowChoose:"从参数列表中选择并返回一个值.",flowIIf:"根据对表达式的评估返回两个对象中的一个.",switchFlow:"计算表达式列表并返回相应的对象值到列表中的第一个表达式是真实的.",avg:"返回指定表达式中所有非空值的平均值.",count:"返回指定表达式中值的计数.",countDistinct:"返回指定表达式中所有不同值的计数。",countRows:"返回指定范围内的行数.",first:"返回指定表达式的第一个值.",last:"返回指定表达式的最后一个值.",standardDev:"返回指定表达式的所有非空值的标准偏差。",standardDevP:"返回指定表达式的所有非空值的总体标准偏差。",sum:"返回指定表达式的值的总和.",variance:"返回指定表达式的所有非空值的方差.",varianceP:"返回指定表达式的所有非空值的总体方差.",runningValue:"使用指定的函数来返回指定表达式的正在运行的聚合.",aggregate:"按照数据提供者的定义返回指定表达式的自定义聚合.",doubleDeclining:"使用双倍余额递减方法或您指定的其他方法，返回一个double值，用于指定特定时间段内资产的折旧。",futureValue:"基于定期固定支付和固定利率，返回指定年金未来价值的双倍值。",interestPayment:"根据定期付款，固定付款和固定利率返回指定给定期间年金利息支付的双倍值。",numberOfPeriods:"根据定期固定支付和固定利率返回一个double值，指定年金的期数",annuityPayment:"根据定期，固定付款和固定利率返回一个双重值，指定年金支付。",principalPayment:"基于定期固定支付和固定利率，返回指定给定期间年金本金的双倍值。",presentValue:"基于将来支付的定期，固定支付和固定利率，返回指定年金现值的双重值。",rateOfInterest:"返回一个double值，指定年金的每期利率。",straightLine:"返回一个double值，指定单个期间的资产的直线折旧。",sumOfYearsDigits:"返回一个double值，用于指定指定时间段内资产的年数总和折旧。",convertBool:"转换为布尔值.",convertByte:"转换为字节.",convertChar:"转换为字符.",convertDate:"转换为日期.",convertDouble:"转换为双倍.",convertDecimal:"转换为十进制.",convertInteger:"转换为整数.",convertLong:"转换为长.",convertObject:"转换为对象",convertShort:"转换为空格.",convertSingle:"转换为单个.",convertString:"转换为字符串.",fix:"返回数字的整数部分.",hexaDecimal:"返回表示数字的十六进制值的字符串.",integerPortion:"返回数字的整数部分.",octal:"返回表示数字的八进制值的字符串.",stringOfNumber:"返回表示数字的字符串。",stringAsNumeric:"返回字符串中的数字作为适当类型的数值.",inScope:"如果当前实例在指定范围内，则返回true.",depthLevel:"返回表示当前深度级别的从零开始的整数.",previous:"返回前一行数据的表达式的值.",rowNumber:"返回指定范围内所有行的运行计数."}},dataAssign:{measures:"措施",dimensions:"外形尺寸",addDatasource:"添加數據源",errorMessagePrefix:"您尚未配置數據源.",errorMessageSuffix:"添加數據源以將數據綁定到設計器中的報表項.",search:"搜索",dragOnDrop:"拖放"},reportProperty:{header:"头",body:"身体",footer:"页脚",report:"报告",basicSettings:{categoryName:"基本设置",background:"背景颜色",borderTypes:{border:"边境",borderLeft:"剩下",borderTop:"最佳",borderRight:"对",borderBottom:"底部"},borderStyles:{solid:"固体",none:"没有",double:"双",dashed:"虚线",dotted:"带点"}},generalSettings:{categoryName:"一般",printFirstPage:"在第一页打印",printLastPage:"在最后一页打印"},size:{sizeLabel:"尺寸",paddingTypes:{padding:"填充",paddingLeft:"剩下",paddingRight:"对",paddingTop:"最佳",paddingBottom:"底部"}},position:{categoryPosition:"位置",positionLabel:"位置",left:"剩下",top:"最佳",sizeLabel:"尺寸",width:"宽度",height:"高度"},margin:{categoryName:"余量",categoryHeader:"余量",types:{left:"剩下",right:"对",bottom:"底部",top:"最佳"}},pageUnit:{header:"頁面單位",label:"頁面單位",types:{inches:"英寸",centimeters:"公分",pixels:"像素",points:"點",millimeters:"毫米",picas:"異食癖"}},columns:{header:"页面列",label:"列",columnSpacing:"列间距"},codeModule:{code:"码"},paperSize:{orientation:"方向",header:"纸张大小",label:"纸张大小",orientationTypes:{landScape:"景观",portrait:"肖像"},types:{a3Size:"A3",a4Size:"A4",b4Size:"B4(JIS)",b5Size:"B5(JIS)",envelope:"信封 #10",envelopeMonarch:"信封君主",executive:"行政人员",legal:"法律",letter:"信",tabloid:"小报",custom:"习惯"}},styleTooltip:"樣式",colorTooltip:"顏色",sizeTooltip:"尺寸"},textBoxProperty:{contents:{categoryName:"內容",content:"內容"},basicSettings:{categoryName:"基本设置",font:{categoryName:"字形",defaultStyle:"默认",normal:"正常",italic:"斜体"},fontStyle:{categoryName:"字体样式",defaultStyle:"默认",normal:"正常",thin:"瘦",extraLight:"额外的光",light:"光",medium:"中",semiBold:"半粗体",bold:"胆大",extraBold:"特大号",heavy:"重"},textDecoration:{categoryName:"文本装饰",defaultStyle:"默认",none:"没有",underline:"强调",lineThrough:"通过",overline:"上划线"},format:"格式"},alignment:{categoryName:"对准",textAlignment:{categoryName:"文本对齐",defaultStyle:"默认",left:"剩下",center:"中央",right:"对"},verticalAlignment:{categoryName:"垂直对齐",defaultStyle:"默认",top:"最佳",middle:"中间",bottom:"底部"},lineSpacing:"线高"},appearance:{categoryName:"Appearance",borderTypes:{border:"边境",borderLeft:"剩下",borderTop:"最佳",borderRight:"对",borderBottom:"底部"},borderStyles:{solid:"固体",none:"没有",double:"双",dashed:"虚线",dotted:"带点"},background:"背景颜色"},link:"链接",linkReport:"链接报告",position:{categoryPosition:"位置",positionLabel:"位置",left:"剩下",top:"最佳",sizeLabel:"尺寸",width:"宽度",height:"高度",direction:{categoryName:"方向",leftToRight:"左到右",rightToLeft:"右到左"}},visibility:{categoryName:"能见度",visible:"可见",toggleItem:"切換項目",intialToggleState:"初始切換狀態"},miscellaneous:{categoryName:"杂",canGrow:"可以增长",canShrink:"可以收缩"},paragraphSettings:{categoryName:"段落设置",textAlignment:{categoryName:"文本对齐",defaultStyle:"默认",left:"剩下",center:"中央",right:"对"},indent:{categoryName:"缩进",leftIndent:"剩下",rightIndent:"对"},space:{categoryName:"空间",topSpace:"最佳",bottomSpace:"底部"},listLevel:{categoryName:"列表级别",zeroLevel:"",oneLevel:"",twoLevel:"",threeLevel:"",fourLevel:""},listStyle:{categoryName:"列表样式",none:"没有",numbered:"编号",bulleted:"项目符号"}},padding:{padding:"填充",paddingLeft:"剩下",paddingRight:"对",paddingTop:"最佳",paddingBottom:"底部"},contextMenu:{cut:"切",copy:"复制",paste:"糊",expression:"表达",pasteAlert:"您的瀏覽器不支持直接訪問剪貼板。請使用Ctrl + V鍵盤快捷鍵而不是粘貼操作。"},fontStyleTooltip:"樣式",fontWeightTooltip:"重量",fontSizeTooltip:"尺寸",fontColorTooltip:"顏色",fontFamilyTooltip:"字體系列",styleTooltip:"樣式",colorTooltip:"顏色",sizeTooltip:"尺寸",selectedText:"选定的文字"},designPanel:{headerText:"头",footerText:"页脚",pasteAlert:"页眉和页脚区域仅支持基本项目",pasteTitle:"糊"},customProperty:{position:{categoryPosition:"位置",positionLabel:"位置",left:"剩下",top:"最佳",sizeLabel:"尺寸",width:"宽度",height:"高度"},appearance:{categoryAppearance:"出现",borderTypes:{border:"边境",borderLeft:"剩下",borderTop:"最佳",borderRight:"对",borderBottom:"底部"},borderStyles:{solid:"固体",none:"没有",double:"双",dashed:"虚线",dotted:"带点"},backGround:"背景颜色"},visibility:{categoryName:"能见度",visible:"可见"},styleTooltip:"樣式",colorTooltip:"顏色",sizeTooltip:"尺寸"},tablixProperty:{data:{categoryName:"数据",datasetName:"数据集",datasetNone:"没有"},appearance:{categoryName:"出现",borderTypes:{border:"边境",borderLeft:"剩下",borderTop:"最佳",borderRight:"对",borderBottom:"底部"},borderStyles:{solid:"固体",none:"没有",double:"双",dashed:"虚线",dotted:"带点"},backGround:"背景颜色"},miscellaneous:{categoryName:"杂",noRowsMessage:"没有行消息",pageName:"页面名称",keepTogether:"保持在一起",repeatColumnHeaders:"重复列标题",repeatRowHeaders:"重复行标题",fixedColumnHeaders:"固定列标题",fixedRowHeaders:"固定行标题"},font:{categoryName:"字形",defaultStyle:"默认",normal:"正常",italic:"斜体"},fontStyle:{categoryName:"字体样式",defaultStyle:"默认",normal:"正常",thin:"瘦",extraLight:"额外的光",light:"光",medium:"中",semiBold:"半粗体",bold:"胆大",extraBold:"特大号",heavy:"重"},textDecoration:{categoryName:"文本装饰",defaultStyle:"默认",none:"没有",underline:"强调",lineThrough:"通过",overline:"上划线"},alignment:{categoryName:"对准",textAlignment:{categoryName:"文本对齐",defaultStyle:"默认",left:"剩下",center:"中央",right:"对"},verticalAlignment:{categoryName:"垂直对齐",defaultStyle:"默认",top:"最佳",middle:"中间",bottom:"底部"}},padding:{padding:"填充",paddingLeft:"剩下",paddingTop:"最佳",paddingRight:"对",paddingBottom:"底部"},position:{categoryPosition:"位置",positionLabel:"位置",left:"剩下",top:"最佳",sizeLabel:"尺寸",width:"宽度",height:"高度"},visibility:{categoryName:"能见度",visible:"可见",toggleItem:"切换"},staticGroupProp:{categoryName:"基本设置",filters:"过滤器",sorts:"排序",fixedData:"固定数据",groupExp:"组",hideIfNoRows:"隐藏如果没有行",keepWithGroup:"与群体保持联系",repeatOnNewPage:"在新页面上重复",afterGroup:"后",beforeGroup:"之前",pageBreak:{categoryName:"分页符",enablePageBreak:"启用分页符",breakLocation:{categoryName:"休息地点",none:"没有",start:"开始",end:"结束",startAndEnd:"开始和结束",between:"之间"},pageNumberReset:"页码重置"}},fontStyleTooltip:"樣式",fontWeightTooltip:"重量",fontSizeTooltip:"尺寸",fontColorTooltip:"顏色",fontFamilyTooltip:"字體系列",styleTooltip:"樣式",colorTooltip:"顏色",sizeTooltip:"尺寸",tablixMember:"表矩阵 会员"},rowColumnGroup:{rowGroupLable:"行组",columnGroupLable:"列组",tablixAlertHeader:"表矩阵",alertMessage:"启用展开选项以选择Tablix报表项",contextMenu:{addgroup:"添加组",advanced:"高级",deletegroup:"删除组",addtotal:"添加总计",groupproperties:"组属性",addColumnGroup:"添加列组",addRowGroup:"添加行组"},contextSubMenu:{adjacentafter:"相邻之后",adjacentbefore:"相邻之前",childgroup:"儿童组",parentgroup:"家长小组",totalafter:"后",totalbefore:"之前",childGroupAlert:"无法在细节内插入组。"}},tablixContextMenu:{rowMenu:{insertRow:"插入行",above:"以上",below:"下面"},columnMenu:{insertColumn:"插入列",left:"剩下",right:"对"},rowGroupMenu:{insideGroupAbove:"内部组 - 上方",insideGroupBelow:"内部组 - 下面",outsideGroupAbove:"外部团体 - 以上",outsideGroupBelow:"外部团体 - 下面"},columnGroupMenu:{insideGroupLeft:"内部组 - 左",insideGroupRight:"内部集团 - 权利",outsideGroupLeft:"外部组 - 左",outsideGroupRight:"外部集团 - 对"},deleteRows:"删除行",deleteColumns:"删除列",rowVisibility:"行可见性",columnVisibility:"列可见性",tablixProperties:"Tablix属性",splitcells:"分裂细胞",mergecells:"合并单元格",groupMenu:{adjacentAbove:"相邻的上方",adjacentleft:"相邻的左边",adjacentright:"相邻的右边",adjacentBelow:"相邻下方",childGroup:"儿童组",parentGroup:"家长小组",deleteRowGroup:"删除行组",deleteColGroup:"删除列组",addRowGroup:"行组",addColGroup:"专栏组"},reportItemMenu:{insertItem:"插入",chart:"圖表"},totalMenu:{total:"Add Total",row:"Row",column:"Column",before:"Before",after:"After"},cellMenu:{addExpression:"添加表达式",editExpression:"编辑表达式",datasource:"添加数据源",noFields:"没有领域",addText:"添加文字",editText:"编辑文字"},basicItems:{deleteItem:"删除",cut:"切",copy:"复制",paste:"糊"}},tablixAlertDialog:{ok:"好",cancel:"取消",closeToolTip:"關",deleteRowTitle:"刪除行",deleteRow:"僅刪除行",deleteRowGroup:"刪除行和關聯的組",deleteRowContent:"刪除行選項",deleteBodyRow:"Tablix主体必须至少包含一行。",deleteColumnTitle:"刪除列",deleteColumn:"僅刪除列",deleteColumnGroup:"刪除列和關聯的組",deleteColumnContent:"刪除列選項",deleteBodyColumn:"Tablix主体必须至少包含一列。",deleteGroup:"僅刪除組",deleteGroupRowColumn:"刪除組及相關的行和列",deleteGroupTitle:"刪除組",deleteGroupContent:"刪除組選項",deleteStructure:"组结构不可用。",removeRowAlert:"无法删除Tablix报表项中的行",removeRow:"删除行",removeColumn:"删除列",addRow:"添加行",addColumn:"添加列",removeColumnAlert:"无法删除tablix报表项中的列",addRowAlert:"无法在tablix报表项中添加行",addColumnAlert:"无法在tablix报表项中添加列"},tablixAlertInfo:{addGroup:"添加组",removeGroup:"删除组",adjacentAfterAlert:"无法在层次结构中添加相邻组",adjacentBeforeAlert:"无法在层次结构中添加相邻组",childGroupALert:"无法在层次结构中添加子组",title:"Tablix报告项目",parentGroupAlert:"无法在层次结构中添加父组",removeGroupAlert:"无法删除层次结构中的组",selectedMemberAlert:"选定的成员不是组成员",pasteActionAlert:"无法发布信息，因为复制区域和粘贴区域的大小和形状不同。",pasteTitle:"糊"},cellMergingAlertInfo:{merge:"合并单元格",mergeAlert:"无法合并tablix报表项中的单元格",split:"分裂细胞",splitAlert:"无法在Tablix报表项中拆分单元格"},tablixGroup:{title:"Tablix集團",headerTxt:"组标签",groupBy:"通過...分組：",chooseField:"選擇字段",showDetailData:"顯示詳細數據",addGroupHeader:"添加標題",addGroupFooter:"添加頁腳",ok:"好",cancel:"取消",closeToolTip:"關"},tablixDataAssignMenu:{datasource:"添加数据源",addExpression:"添加表达式",editExpression:"编辑表达式",addText:"添加文字",editText:"编辑文字",search:"搜索",noFieldsFound:"找不到任何领域"},tablixTotalAlert:{totalHeader:"添加总标题",totalStatic:"添加总计",headerMessage:"无法将总行数或列添加到tablix报告项目中的组标题",staticMessage:"无法在Tablix报表项中向Tablix主体添加总行数或列数"},tablixAddTextDialog:{save:"保存",add:"加",cancel:"取消",closeToolTip:"關",addText:"添加文字",editText:"編輯文字"},queryDesigner:{storeParameter:{title:"参数",ok:"好",cancel:"取消",parameterLable:"参数",nullLable:"空值",valueLable:"值",dataTypeLable:"数据类型",closeToolTip:"关"},parameter:{title:"参数",ok:"好",cancel:"取消",parameterLable:"参数",nullLable:"空值",valueLable:"值",dataTypeLable:"数据类型",auto:"汽车",text:"文本",closeToolTip:"关"},filter:{title:"查询过滤器",descriptionLable:"表格过滤器列表",add:"加",save:"好",cancel:"取消",nullLable:"空值",trueLable:"真正",falseLable:"假",parameterTooltip:"包含作为参数",closeToolTip:"关",intOperatorType:{equals:"等于",doesNotEqual:"不相等",greaterThan:"比...更棒",greaterThanOrEqual:"大于或等于",lessThan:"少于",lessThanOrEqual:"小于或等于",between:"之间",notBetween:"不在"},stringOpertorType:{equals:"等于",startsWith:"以。。开始",endWith:"以。。结束",contains:"包含",notContains:"不包含"},errorMessage:{dateValidation:"值是无效的日期格式.",commonContent:"过滤器 ",booleanValidation:" 没有任何值可以过滤。请提供过滤器的值.",stringValidation:" 没有适当的值来过滤. "}},previewArea:{dataPreview:"数据预览",noRecords:"无记录可显示",executeRecords:"执行以预览记录",record:"记录",records:"记录",retrieved:"检索",loadRecord:"装载更多"},schemaArea:{search:"搜索",rename:"改名",aggregation:"聚合",dialogHeader:"数据集",matchesFound:"找不到匹配项",alertMessage:{datasourceAlert:"选择一个 数据源 来配置 报告 数据集",removeTable:"下面的关联表将随此删除",duplicateName:"指定的列名已存在",specialCharacter:"列名不应包含特殊字符.",switcherAlert:"切换到可视化设计器将放弃对查询进行的手动更改。你想要使用视觉设计师吗？",duplicateDatasetName:"指定的名称已存在于 数据集 列表中",datasetSpecialCharacter:"名称不应包含空格和特殊字符"},errorMessage:{specifyName:"指定列名称",specifyDatasetName:"指定 数据集 名称",previewFailed:"数据集 无法预览选定的表",specifyQuery:"指定 数据集 查询",selectTable:"选择表来保存 数据集",queryFailed:"数据集 无法保存所选表的查询",tableProcedure:"数据集 无法检索选定的表过程"}},toolBar:{datasourceLable:"数据源",datasetName:"名称",run:"跑",join:"加入",expression:"表达",filter:"过滤",code:"码",finish:"完",cancel:"取消",parameter:"参数",datasourceWaterMark:"选择一个数据源",autoPreview:"自动预览"},joiner:{title:"查询连接器",descriptionLable:"表关系列表",add:"加",save:"好",cancel:"取消",addField:"添加字段",closeToolTip:"关",leftFieldWaterMark:"左场",rightFieldWaterMark:"右场",operatorWaterMark:"操作者",joinTypeWaterMark:"加入类型",leftTableWaterMark:"左表",rightTableWaterMark:"右表",joinTypes:{inner:"内",outer:"左外",rightOuter:"正确的外面",fullOuter:"完全外面"},errorMessage:{removeField:"每个关系必须有一个现场条件。所以，它不允许删除这个字段",noRelationAlert:" 与其他表格无关",selectLeftTable:"选择左表值",selectRightTable:"选择正确的表值",selectRelation:"选择表格的关系",selectLeftColumn:"选择字段行的左列值 #",selectRightColumn:"选择字段行的右列值 #",selectOperator:"选择字段行的运算符 #",relationExists:"表之间已经存在关系"}},credentialDialog:{title:"凭据对话框",userName:"用户名",password:"密码",userNameWaterMark:"用户名",passwordWaterMark:"密码",userNameErrorMessage:"请输入用户名",passwordErrorMessage:"请输入密码",connect:"连",close:"关"},queryExpression:{title:"查询表达式",functionLabel:"功能",columnLabel:"列设置",expressionLabel:"表达",nameLabel:"名称",descriptionLabel:"描述 ",exampleLabelText:"例",ok:"保存",cancel:"取消",add:"加",textAreaWaterMark:"查询表达式",nameFieldWaterMark:"表达式名称",closeToolTip:"关",errorMessage:{saveAlert:"表达式不被保存。你想保存并继续?",bracketSyntax:"打开/关闭支架附近的语法不正确（s）.",parseAlert:"报表设计器 无法解析指定的表达式.",nameAlert:"姓名字段不能为空",emptyAlert:"表达字段不应该是空的",duplicateName:"指定的表达式名称已经存在",specialCharacter:"表达式名称不应包含特殊字符.",referenceError:"列不能在其自己的表达式中引用!",invalidSyntax:"打开/关闭括号附近的语法无效.",retrieveExpression:"报表设计器 无法检索指定的表达式"},datasetTitle:"数据集",expressions:{all:"所有",numbers:"数字",logical:"合乎逻辑",conditional:"条件",date:"日期",stringType:"串",text:"文本",miscellenuous:"杂 ",abs:"返回给定表达式的绝对值.",acos:"返回给定数字表达式的反余弦（也称为反余弦）.",asin:"返回给定数字表达式的反正弦（也称为反正弦）.",atan:"返回给定数字表达式的反正切（也称为反正切）.",cos:"返回以给定表达式的弧度指定的角度的余弦.",degree:"以给定数值表达式的弧度指定的角度返回角度度数.",exponent:"返回给定表达式的指数值.",logrithm:"将给定表达式的对数返回给指定的基数.",pi:"返回PI的常量值.",power:"将给定表达式（表达式1）的值返回到指定的功率（表达式2）.",radians:"返回给定数字表达式中以度数指定的角度的弧度角度.",round:"返回一个舍入值.",sign:"返回表示给定数字表达式的正数（+1），零（0）或负数（-1）符号的值.",sin:"返回以给定表达式的弧度指定的角度的正弦值.",squareRoot:"返回给定数字表达式的平方根.",tan:"返回给定数字表达式的正切值.",ifCondition:"根据对表达式的评估，返回true部分或false部分.",ifNull:"如果表達式是數字 / 串 / 日期，则返回第一个表达式. 如果第一个表达式为空值，则返回第二个表达式.",isNotNull:"如果數字 / 串 / 日期表達为空值，则返回表示false的字符串; 否则表示为true.",isNull:"如果數字 / 串 / 日期表達为空值，则返回表示true的字符串; 否则表示false.",and:"如果两个表达式求值为true，则返回true。",notOperation:"返回正在评估的表达式的反转逻辑值.",orOperation:"如果任何表达式的计算结果为true，则返回true.",addDate:"将天数添加到指定的日期.",name:"返回表示给定日期表达式的指定日期部分的字符串.",part:"返回表示给定日期表达式的指定日期部分的整数值.",sub:"返回从指定日期减去的日期.",day:"返回表示指定日期的日期部分的数值.",daydiff:"返回表示两个指定日期之间差异的数字值.",hour:"以整数形式返回给定日期的小时.",minute:"返回一个数字值，表示由指定的日期表达式产生的日期的分钟部分.",month:"返回表示指定日期表达式的日期的月份部分的数值.",now:"返回当前的日期和时间.",today:"返回当前日期.",year:"返回表示指定日期表达式所产生日期的年份部分的数值.",char:"将给定的整数ASCII码转换为一个字符.",concat:"返回由两个或更多字符串值连接产生的字符串值.",contains:"如果给定的字符串表达式包含指定的字符串表达式，则返回true.",endsWith:"如果给定的字符串表达式以指定的子字符串表达式结束，则返回true.",left:"返回给定字符串表达式开始处的指定字符数.",length:"返回给定表达式的自然对数.",lower:"从给定的字符串表达式中返回小写字母转换后的字符串值.",leftTrim:"返回从字符串表达式中删除前导空白的字符串值.",maximum:"返回给定表达式中的最大值.",minimum:"返回给定表达式中的最小值.",right:"返回给定字符串表达式末尾的指定字符数.",rightTrim:"返回给定字符串中没有右侧尾随空格的字符串.",startswith:"如果给定的字符串表达式以指定的子字符串表达式开头，则返回true.",subString:"返回从给定字符串表达式的特定索引开始的特定字符串长度.",upper:"从给定的字符串表达式中返回大写字母转换后的字符串值."}},reportParameter:{title:"参数",descriptionText:"报告参数",addText:"加",ok:"好",cancel:"取消",nameWaterMark:"参数名称",valueWaterMark:"值",closeToolTip:"关"}},chartItem:{categoryItems:{yvalue:"Y值(s)",size:"尺寸(s)",xvalue:"X值(s)",column:"柱",row:"行(s)"},categoryItemsMenu:{filter:"过滤器",sort:"排序",group:"组",expression:"表达",aggregate:"骨料"}},codeDialog:{title:"代码模块",ok:"好",cancel:"取消",add:"加",closeToolTip:"关 ",reference:{title:"参考",waterMark:"参考",errorMessage:"该字段为空",headerText:"裝配參考列表",infoTipText:"添加裝配參考以在報告中使用裝配功能。"},classes:{title:"类",classWaterMark:"班级名称",instanceWaterMark:"实例名称",classErrorMessage:"该字段为空",instanceErrorMessage:"该字段为空",headerText:"類實例列表",infoTipText:"添加類實例以訪問報表中的對象函數。"},code:{title:"码",headerText:"用於報告的VB代碼功能",infoTipText:"Syncfusion報告引擎支持VB代碼函數與報表元素和數據集成。"}},previewData:{title:"预览数据",ok:"好",cancel:"取消",description:"綁定JSON數據以進行預覽",close:"关",infoToolTip:"報告需要預覽JSON格式數據，它包含數組格式列表中的鍵和值。",jsonHeader:"JSON数据：",errorMessage:"指定有效的JSON格式",previewDataAlert:{title:"预览数据",alertMessage:"您想切換到報表設計器嗎？"}},sampleDataSource:{sampleDSHeader:"進口樣品數據",addText:"加",searchText:"搜索",noDataFound:"沒有找到數據。",welcomeContentPrefix:"首先創建一個數據源",welcomeContentSuffix:"您可以連接到自己的自定義數據，也可以從我們提供的預定義共享數據中導入一個。",sampleDSText:"導入樣本數據",exploreSampleText:"探索樣本數據",accordionText:"啟動您的第一個報告並使用示例數據探索自定義選項。",errorMessage:"網絡錯誤",alertHeaderText:"導入數據",alertMessage:"報表設計器無法從報表服務器導入數據"},field:{title:"字段",nameWaterMark:"字段名称",sourceWaterMark:"场源",ok:"好",cancel:"取消",description:"更改查询和计算字段",query:"查询字段",calculated:"计算字段",fieldError:"场是空的",fieldsError:"字段是空的",add:"加",closeToolTip:"关",invalidFormat:"字段名称不应包含空格和特殊字符",sameFieldName:"字段名称已存在"},commonProperty:{commonProperties:"共同属性",basicSettings:{categoryBasicSettings:"基本设置",borderTypes:{border:"边境",borderLeft:"剩下",borderTop:"最佳",borderRight:"对",borderBottom:"底部"},borderStyles:{solid:"固体",none:"没有",double:"双",dashed:"虚线",dotted:"带点"},backGround:"背景颜色",styleTooltip:"樣式",colorTooltip:"顏色",sizeTooltip:"尺寸"},position:{categoryPosition:"位置",positionLabel:"位置",left:"剩下",top:"最佳"},visibility:{categoryVisibility:"能见度",visible:"可见"}}};
