﻿CREATE TABLE [dbo].[ContactEmailTemplates] (
    [ContactEmailTemplateId] BIGINT         IDENTITY (1, 1) NOT NULL,
    [ContactId]              BIGINT         NOT NULL,
    [EmailTemplateTitle]     NVARCHAR (200) CONSTRAINT [DF_ContactEmailTemplates_EmailTemplateTitle] DEFAULT ('') NOT NULL,
    [Date]                   DATETIME       CONSTRAINT [DF_ContactEmailTemplates_Date] DEFAULT (getdate()) NOT NULL,
    [Status]                 NVARCHAR (30)  CONSTRAINT [DF_ContactEmailTemplates_Status] DEFAULT ('') NOT NULL,
    CONSTRAINT [PK_ContactEmailTemplates] PRIMARY KEY CLUSTERED ([ContactEmailTemplateId] ASC),
    CONSTRAINT [FK_ContactEmailTemplates_Contacts] FOREIGN KEY ([ContactId]) REFERENCES [dbo].[Contacts] ([ContactId]) ON DELETE CASCADE ON UPDATE CASCADE
);

