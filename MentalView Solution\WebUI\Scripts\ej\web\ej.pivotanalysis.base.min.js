/*!
*  filename: ej.pivotanalysis.base.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function($,ej,undefined){ej.PivotAnalysis={_initProperties:function(){this._colkeyvalues=[];this._rowKeyValues=[];this._colKeysCalcValues=[];this._rowKeysCalcValues=[];this._tableKeysCalcValues=[];this._gridMatrix=null;this._rowTotCalc=[];this._colTotCalc=[];this._transposeEngine=[];this._tRowCnt=0;this._tColCnt=0;this._cellType="";this._valueFilterArray=ej.isNullOrUndefined(this._valueFilterArray)?[]:this._valueFilterArray;this._currentFilterVal={};this._fieldMembers={};this._summaryTypes=[];this._editCellsInfo=[];this._locale="en-US";this._valueSorting=ej.isNullOrUndefined(this._valueSorting)?null:this._valueSorting;this._sort=ej.isNullOrUndefined(this._sort)?null:this._sort;this._isPaging=!1;this._colHdrLen=1;this._rowHdrLen=1;this._rowMembers={};this._colMembers={};this._calcMembers={}},setFieldCaptions:function(n){$.each(n.rows,function(n,t){ej.isNullOrUndefined(t.fieldCaption)&&(t.fieldCaption=t.fieldName)});$.each(n.columns,function(n,t){ej.isNullOrUndefined(t.fieldCaption)&&(t.fieldCaption=t.fieldName)});$.each(n.filters,function(n,t){ej.isNullOrUndefined(t.fieldCaption)&&(t.fieldCaption=t.fieldName)});$.each(n.values,function(n,t){ej.isNullOrUndefined(t.fieldCaption)&&(t.fieldCaption=t.fieldName)})},getTreeViewData:function(n){var t=[],e=[],o,f,i,u,r,s;if(n.data!=null)for(i=0;i<n.data.length;i++)jQuery.each(n.data[i],function(n){jQuery.inArray(n,t)==-1&&t.push(n)});for(jQuery.each(n.values,function(n,i){i.isCalculatedField==!0&&jQuery.inArray(i.fieldName,t)==-1&&t.push(i.fieldName)}),o=[n.rows,n.columns,n.filters,n.values],f=[].concat.apply([],o),i=0;i<t.length;i++){for(u=0;u<f.length;u++)(t[i].caption==undefined||t[i].caption=="")&&f[u].fieldName==t[i]&&(t[i]={name:f[u].fieldName,caption:f[u].fieldCaption,format:f[u].format,formatString:f[u].formatString,showSubTotal:ej.isNullOrUndefined(f[u].showSubTotal)?!0:f[u].showSubTotal});(t[i].caption==undefined||t[i].caption=="")&&(t[i]={name:t[i].name||t[i],caption:t[i].name||t[i]})}for(r=0;r<t.length;r++)s=$.grep(n.rows,function(n){return n.fieldName==t[r].name}).length>0||$.grep(n.columns,function(n){return n.fieldName==t[r].name}).length>0||$.grep(n.values,function(n){return n.fieldName==t[r].name}).length>0||$.grep(n.filters,function(n){return n.fieldName==t[r].name}).length>0,e.push({id:t[r].name,name:t[r].name,caption:t[r].caption,format:t[r].format,formatString:t[r].formatString,showSubTotal:ej.isNullOrUndefined(t[r].showSubTotal)?!0:t[r].showSubTotal,isSelected:s,spriteCssClass:""});return e},pivotEnginePopulate:function(model){var dataSource=model.dataSource,currentObj=this,isLiteral=!1,rowFilters,lit,formatSt,uV,fields,vC,list,cnt,nC,outputString,rC,cC,isFiltered,cCnt,rCnt,temp,value,val,j,k,formula,oldValue,newValue,i;this._initProperties();model.dataSource.enableAdvancedFilter&&this._valueFilterArray.length==0&&(this._valueFilterArray=$.grep(dataSource.columns,function(n){if(n.advancedFilter&&n.advancedFilter.length>0&&n.advancedFilter[0].advancedFilterType==ej.Pivot.AdvancedFilterType.ValueFilter)return n.advancedFilter}),rowFilters=$.grep(dataSource.rows,function(n){if(n.advancedFilter&&n.advancedFilter.length>0&&n.advancedFilter[0].advancedFilterType==ej.Pivot.AdvancedFilterType.ValueFilter)return n.advancedFilter}),rowFilters.length>0&&$.merge(this._valueFilterArray,rowFilters));this._locale=model.locale;var colAxis=dataSource.columns,rowAxis=dataSource.rows,filters=dataSource.filters,calcValues=dataSource.values,colLen,jsonObj=[],pivotFieldList=[],rowLen;for(lit=0;dataSource.values.length>lit;lit++)if(formatSt="",dataSource.values[lit].format&&(formatSt=dataSource.values[lit].format.toLowerCase()),formatSt=="string"||formatSt=="text"||formatSt=="literal"){isLiteral=!0;break}if(this._editCellsInfo=[],!ej.isNullOrUndefined(model.editCellsInfo)){var rowHeaders=model.editCellsInfo.rowHeader,colHeaders=model.editCellsInfo.columnHeader,values=model.editCellsInfo.JSONRecords;if(!ej.isNullOrUndefined(values)){for(uV=0;uV<values.length;uV++)fields={},rowAxis.length>0&&(fields.row=rowHeaders[uV].toString().split("#").splice(0,rowAxis.length).join(">#>")),colAxis.length>0&&(fields.column=colHeaders[uV].toString().split("#").splice(0,colAxis.length).join(">#>")),fields.value=colHeaders[uV].toString().split("#")[colHeaders[uV].toString().split("#").length-1]+"#"+values[uV].Value,this._editCellsInfo.push(fields);for(vC=0;vC<calcValues.length;vC++)$.each(this._editCellsInfo,function(n,t){calcValues[vC].fieldName==t.value.split("#")[0]&&(ej.PivotAnalysis._editCellsInfo[n].value=vC+"#"+t.value.split("#")[1])})}}for(i=0;i<calcValues.length;i++)this._summaryTypes.push(calcValues[i].summaryType!=null&&calcValues[i].summaryType!=undefined?calcValues[i].summaryType:ej.PivotAnalysis.SummaryType.Sum);if(dataSource.data!=null)for(cnt=0;cnt<dataSource.data.length;cnt++){list=jQuery.extend(!0,{},dataSource.data[cnt]);var table={keys:[],uniqueName:"",value:null},row={keys:[],uniqueName:"",value:null},col={keys:[],uniqueName:"",value:null},values={keys:[],uniqueName:"",value:null},isExcluded=!1;for(i=0;i<filters.length;i++)if(val=this._getReflectedValue(list,filters[i].fieldName,null,null),this._fieldMembers[filters[i].fieldName]==undefined||this._fieldMembers[filters[i].fieldName]==null?this._fieldMembers[filters[i].fieldName]=[val]:$.inArray(val,this._fieldMembers[filters[i].fieldName])==-1&&this._fieldMembers[filters[i].fieldName].push(val),filters[i].filterItems!=null&&filters[i].filterItems!=undefined&&((filters[i].filterItems.filterType==ej.PivotAnalysis.FilterType.Exclude||filters[i].filterItems.filterType==null)&&$.inArray(val,filters[i].filterItems.values)>=0||filters[i].filterItems.filterType==ej.PivotAnalysis.FilterType.Include&&$.inArray(val,filters[i].filterItems.values)<0)){isExcluded=!0;break}for(nC=0;nC<calcValues.length;nC++)val="",calcValues[nC]!=undefined&&calcValues[nC]!=null&&(list[calcValues[nC].fieldName]=model.dataSource.isFormattedValues?this._getNumber($(list).prop(calcValues[nC].fieldName),calcValues[nC].formatString):list[calcValues[nC].fieldName],val=this._getReflectedValue(list,calcValues[nC].fieldName,calcValues[nC].format,calcValues[nC].formatString),this._fieldMembers[calcValues[nC].fieldName]==undefined||this._fieldMembers[calcValues[nC].fieldName]==null?this._fieldMembers[calcValues[nC].fieldName]=[val]:$.inArray(val,this._fieldMembers[calcValues[nC].fieldName])==-1&&this._fieldMembers[calcValues[nC].fieldName].push(val)),outputString=val!=null?val.toString().replace(/([AMPME~!@#$%^&*()_+=`{}\[\]\|\\:;'<>,-.\/? ])+/g,""):"0",outputString=outputString.replace(ej.globalize.preferredCulture(this._locale).numberFormat.currency.symbol,""),$.isNumeric(outputString)||calcValues[nC].format=="date"?values.keys.push(val!=null?val:null):(values.keys.push(1),isLiteral&&(values.uniqueName==""&&(values.uniqueName=[]),ej.isNullOrUndefined(calcValues[nC].format)||calcValues[nC].format.toLowerCase()!="string"&&calcValues[nC].format.toLowerCase()!="literal"&&calcValues[nC].format.toLowerCase()!="text"?values.uniqueName.push(" "):values.uniqueName.push(val)));for(rC=0;rC<rowAxis.length;rC++){if(val="",rowAxis[rC]!=undefined&&rowAxis[rC]!=null)if(val=this._getReflectedValue(list,rowAxis[rC].fieldName,ej.isNullOrUndefined(rowAxis[rC].format)?null:rowAxis[rC].format,!ej.isNullOrUndefined(rowAxis[rC].format)&&!ej.isNullOrUndefined(rowAxis[rC].formatString)?rowAxis[rC].formatString:null),this._fieldMembers[rowAxis[rC].fieldName]==undefined||this._fieldMembers[rowAxis[rC].fieldName]==null?this._fieldMembers[rowAxis[rC].fieldName]=[val]:$.inArray(val,this._fieldMembers[rowAxis[rC].fieldName])==-1&&this._fieldMembers[rowAxis[rC].fieldName].push(val),dataSource.enableAdvancedFilter&&!ej.isNullOrUndefined(rowAxis[rC].advancedFilter)&&rowAxis[rC].advancedFilter.length&&rowAxis[rC].advancedFilter[0].advancedFilterType==ej.Pivot.AdvancedFilterType.LabelFilter){if(isFiltered=this._applyLabelFilter(rowAxis[rC].advancedFilter,val),isFiltered){isExcluded=!0;break}}else if(rowAxis[rC].filterItems!=null&&rowAxis[rC].filterItems!=undefined&&((rowAxis[rC].filterItems.filterType==ej.PivotAnalysis.FilterType.Exclude||rowAxis[rC].filterItems.filterType==null)&&$.inArray(val,rowAxis[rC].filterItems.values)>=0||rowAxis[rC].filterItems.filterType==ej.PivotAnalysis.FilterType.Include&&$.inArray(val,rowAxis[rC].filterItems.values)<0)){isExcluded=!0;break}val!=null&&val.toString().replace(/^\s+|\s+$/gm,"")||(val="(blank)");row.keys.push(val);row.uniqueName+=row.uniqueName==""?val:">#>"+val;table.keys.push(val);table.uniqueName+=table.uniqueName==""?val:">#>"+val}for(cC=0;cC<colAxis.length;cC++){if(val="",colAxis[cC]!=undefined&&colAxis[cC]!=null)if(val=this._getReflectedValue(list,colAxis[cC].fieldName,ej.isNullOrUndefined(colAxis[cC].format)?null:colAxis[cC].format,!ej.isNullOrUndefined(colAxis[cC].format)&&!ej.isNullOrUndefined(colAxis[cC].formatString)?colAxis[cC].formatString:null),this._fieldMembers[colAxis[cC].fieldName]==undefined||this._fieldMembers[colAxis[cC].fieldName]==null?this._fieldMembers[colAxis[cC].fieldName]=[val]:$.inArray(val,this._fieldMembers[colAxis[cC].fieldName])==-1&&this._fieldMembers[colAxis[cC].fieldName].push(val),dataSource.enableAdvancedFilter&&!ej.isNullOrUndefined(colAxis[cC].advancedFilter)&&colAxis[cC].advancedFilter.length&&colAxis[cC].advancedFilter[0].advancedFilterType==ej.Pivot.AdvancedFilterType.LabelFilter){if(isFiltered=this._applyLabelFilter(colAxis[cC].advancedFilter,val),isFiltered){isExcluded=!0;break}}else if(colAxis[cC].filterItems!=null&&colAxis[cC].filterItems!=undefined&&((colAxis[cC].filterItems.filterType==ej.PivotAnalysis.FilterType.Exclude||colAxis[cC].filterItems.filterType==null)&&$.inArray(val,colAxis[cC].filterItems.values)>=0||colAxis[cC].filterItems.filterType==ej.PivotAnalysis.FilterType.Include&&$.inArray(val,colAxis[cC].filterItems.values)<0)){isExcluded=!0;break}val!=null&&val.toString().replace(/^\s+|\s+$/gm,"")||(val="(blank)");col.keys.push(val);col.uniqueName+=col.uniqueName==""?val:">#>"+val;table.keys.push(val);table.uniqueName+=table.uniqueName==""?val:">#>"+val}if(!isExcluded)if(this._isMemberExist("row",row,$.extend(!0,{},values),dataSource),this._isMemberExist("column",col,$.extend(!0,{},values),dataSource),!(model.enablePaging||model.enableVirtualScrolling)||row.keys.length==0&&col.keys.length==0)this._isMemberExist("calc",table,$.extend(!0,{},values),dataSource);else if(row.keys.length>0&&col.keys.length>0)for(rCnt=0;rCnt<row.keys.length;rCnt++)for(cCnt=0;cCnt<col.keys.length;cCnt++)row.keys.length==rCnt+1&&col.keys.length==cCnt+1?this._isMemberExist("calc",{keys:row.keys.slice(0,rCnt+1).concat(col.keys.slice(0,cCnt+1)),uniqueName:row.keys.slice(0,rCnt+1).concat(col.keys.slice(0,cCnt+1)).join(">#>"),value:null},$.extend(!0,{},values),dataSource):this._isMemberExist("calc",{cellType:"SubTot",keys:row.keys.slice(0,rCnt+1).concat(col.keys.slice(0,cCnt+1)),uniqueName:row.keys.slice(0,rCnt+1).concat(col.keys.slice(0,cCnt+1)).join(">#>"),value:null},$.extend(!0,{},values),dataSource);else if(row.keys.length==0)for(cCnt=0;cCnt<col.keys.length;cCnt++)col.keys.length==cCnt+1?this._isMemberExist("calc",{keys:col.keys.slice(0,cCnt+1),uniqueName:col.keys.slice(0,cCnt+1).join(">#>"),value:null},$.extend(!0,{},values),dataSource):this._isMemberExist("calc",{cellType:"SubTot",keys:col.keys.slice(0,cCnt+1),uniqueName:col.keys.slice(0,cCnt+1).join(">#>"),value:null},$.extend(!0,{},values),dataSource);else for(rCnt=0;rCnt<row.keys.length;rCnt++)row.keys.length==rCnt+1?this._isMemberExist("calc",{keys:row.keys.slice(0,rCnt+1),uniqueName:row.keys.slice(0,rCnt+1).join(">#>"),value:null},$.extend(!0,{},values),dataSource):this._isMemberExist("calc",{cellType:"SubTot",keys:row.keys.slice(0,rCnt+1),uniqueName:row.keys.slice(0,rCnt+1).join(">#>"),value:null},$.extend(!0,{},values),dataSource)}for(i=0;i<calcValues.length;i++)if(temp=$.grep(calcValues,function(n){return n.isCalculatedField==!0}),calcValues[i].isCalculatedField&&temp.length!=calcValues.length)for(j=0;j<this._tableKeysCalcValues.length;j++){for(formula=calcValues[i].formula.replace(/\s/g,""),k=0;k<calcValues.length;k++)calcValues[k].isCalculatedField!=!0&&(value=ej.globalize.parseFloat(this._tableKeysCalcValues[j].value.keys[k]==null?"0":calcValues[k].format=="date"?this._dateToInt(this._tableKeysCalcValues[j].value.keys[k].toString()).toString():calcValues[k].format=="time"?this._dateToInt(new Date("1900",this._tableKeysCalcValues[j].value.keys[k].toString().split(":")[0],this._tableKeysCalcValues[j].value.keys[k].toString().split(":")[1],this._tableKeysCalcValues[j].value.keys[k].toString().split(":")[2].split(" ")[0]).toString()).toString():this._tableKeysCalcValues[j].value.keys[k].toString(),this._locale),formula=formula.replace(new RegExp(calcValues[k].fieldName,"g"),calcValues[k].format=="percentage"&&value!=0?value<0?"("+value/100+")":value:value<0?"("+value+")":value));formula.indexOf("^")>-1&&(formula=this._powerFunction(formula));oldValue=!isNaN(eval(formula))&&isFinite(eval(formula))?this._getReflectedValue({calcField:eval(formula)},"calcField",calcValues[i].format,calcValues[i].formatString):dataSource.values[i].format==null?0:"0";newValue=dataSource.values[i].format==null?0:"0";this._tableKeysCalcValues[j].value.keys[i]=this._getSummaryValue(oldValue,newValue,1,this._summaryTypes[i],dataSource.values[i].format,dataSource.values[i].formatString)}else if(calcValues[i].isCalculatedField)for(val={keys:[],uniqueName:"",value:null},j=0;j<this._tableKeysCalcValues.length;j++){for(k=0;k<calcValues.length&&calcValues[k].isCalculatedField;k++)formula=calcValues[k].formula.replace(/\s/g,""),formula.indexOf("^")>-1&&(formula=this._powerFunction(formula)),oldValue=!isNaN(eval(formula))&&isFinite(eval(formula))?this._getReflectedValue({calcField:eval(formula)},"calcField",calcValues[k].format,calcValues[k].formatString):dataSource.values[k].format==null?0:"0",newValue=dataSource.values[k].format==null?0:"0",val.keys.push(this._getSummaryValue(oldValue,newValue,1,"sum",dataSource.values[k].format,dataSource.values[k].formatString));this._tableKeysCalcValues[j].value=val;val={keys:[],uniqueName:"",value:null}}if(rowAxis.length>0&&(this._rowKeysCalcValues=this._sortHeaders(this._rowKeysCalcValues,rowAxis,0)),colAxis.length>0&&(this._colKeysCalcValues=this._sortHeaders(this._colKeysCalcValues,colAxis,0)),this._colkeyvalues=$.extend([],this._colKeysCalcValues),this._rowKeyValues=$.extend([],this._rowKeysCalcValues),colAxis.length>0&&this._insertTotalHeader(colAxis,this._colKeysCalcValues),rowAxis.length>0)this._insertTotalHeader(rowAxis,this._rowKeysCalcValues);else if(calcValues.length>0)if(this._rowKeysCalcValues.length>0)this._rowKeysCalcValues[0].keys[0]="Grand Total";else{for(this._rowKeysCalcValues.push({keys:[""],value:{keys:[]}}),i=0;i<calcValues.length;i++)this._rowKeysCalcValues[0].value.keys.push();this._rowKeysCalcValues[0].keys[0]="Grand Total"}return this._fillEngine(model)},_fillEngine:function(n){var p,c,l,i,u,r,s,a,f,e;(n.enablePaging||n.enableVirtualScrolling)&&(this._isPaging=!0,this._colHdrLen=1,this._rowHdrLen=1,this._controlObj._colKeysCalcValues=$.extend(!0,[],this._colKeysCalcValues),this._controlObj._rowKeysCalcValues=$.extend(!0,[],this._rowKeysCalcValues),this._controlObj._tableKeysCalcValues=$.extend(!0,[],this._tableKeysCalcValues),this._cropHeaders(),this._applyPaging());var t=n.dataSource,v=[],o=t.columns,h=t.rows,w=t.filters,y=t.values;for(this._tRowCnt=0,this._tColCnt=0,this._currentFilterVal=this._valueFilterArray.length>0?this._valueFilterArray[0]:{},this._calculateValues(t),i=0;i<this._valueFilterArray.length;i++)this._advancedFilterInfo.length>0&&(p=this,this._rowKeysCalcValues=this._filterKeyValues(this._rowKeyValues),this._colKeysCalcValues=this._filterKeyValues(this._colkeyvalues),this._gridMatrix=null,this._colkeyvalues=$.extend([],this._colKeysCalcValues),this._rowKeyValues=$.extend([],this._rowKeysCalcValues),o.length>0&&this._insertTotalHeader(o,this._colKeysCalcValues),h.length>0?this._insertTotalHeader(h,this._rowKeysCalcValues):y.length>0&&(this._rowKeysCalcValues[0].keys[0]="Grand Total"),this._tRowCnt=0,this._tColCnt=0,this._currentFilterVal=this._valueFilterArray.length!=i+1?this._valueFilterArray[i+1]:{},this._calculateValues(t));if(c=this._colKeysCalcValues.length==1&&this._colKeysCalcValues[0].cellType=="RGTot",this._colTotCalc=[],this._rowTotCalc=[],this._colKeysCalcValues=[],this._rowKeysCalcValues=[],this._tableKeysCalcValues=[],this._transposeEngineCreation(),!ej.isNullOrUndefined(n.valueSortSettings)&&!ej.isNullOrUndefined(n.valueSortSettings.sortOrder)&&n.valueSortSettings.sortOrder!="none"&&n.dataSource.values.length>0&&(!ej.isNullOrUndefined(this._valueSorting)||!ej.isNullOrUndefined(n.valueSortSettings.headerText)&&n.valueSortSettings.headerText!=""))if(l=0,n.valueSortSettings.headerDelimiters=n.valueSortSettings.headerDelimiters==undefined?"##":n.valueSortSettings.headerDelimiters,ej.isNullOrUndefined(this._valueSorting)){for(i=0;i<this._transposeEngine.length;i++){for(u=0;u<n.valueSortSettings.headerText.split(n.valueSortSettings.headerDelimiters).length;u++)ej.isNullOrUndefined(this._transposeEngine[i][u])||this._transposeEngine[i][u].Value!=n.valueSortSettings.headerText.split(n.valueSortSettings.headerDelimiters)[u]||l++;if(l==n.valueSortSettings.headerText.split(n.valueSortSettings.headerDelimiters).length){this._valueSorting=i;break}l=0}this._sort=!ej.isNullOrUndefined(n.valueSortSettings)&&!ej.isNullOrUndefined(n.valueSortSettings.sortOrder)?n.valueSortSettings.sortOrder:this._sort;n.dataSource.columns.length+1!=n.valueSortSettings.headerText.split(n.valueSortSettings.headerDelimiters).length||ej.isNullOrUndefined(this._valueSorting)||this._applyValueSorting(n)}else this._applyValueSorting(n);for(r=0;r<this._tColCnt-1;r++){if(s=h.length==0?1:this._isPaging?this._rowHdrLen:h.length,r<s)for(a=(c?1:this._isPaging?this._colHdrLen:o.length)+(t.values.length>0?1:0),a==0&&s>0&&(a=1),f=0;f<a;f++)this._gridMatrix.length>0&&v.push({Index:r+","+f,CSS:"none",Value:"",State:0,ColSpan:r==0&&f==0?s:1,RowSpan:r==0&&f==0?(c?1:this._isPaging?this._colHdrLen:o.length)+(t.values.length>0?1:0):1,Info:"",Span:"None",Expander:0});for(e=0;e<this._tRowCnt-1;e++)this._transposeEngine[r]==undefined||this._transposeEngine[r][e]==undefined||e<(c?1:this._isPaging?this._colHdrLen:o.length)+(t.values.length>0?1:0)&&r<s||v.push(this._transposeEngine[r][e])}return this._gridMatrix=[],t.rows[0]==undefined&&(t.rows.length=0),{json:v,pivotEngine:this._transposeEngine}},_cropHeaders:function(){var i,s=this._controlObj._drillHeaders.row,h=this._controlObj._drillHeaders.column,e=[],u=2,f=this._controlObj.model.enableCollapseByDefault,a=this._controlObj.model.dataSource.rows.length,v=this._controlObj.model.dataSource.columns.length,t,r,n;do{if(t=u==2?$.extend(!0,[],this._rowKeysCalcValues):$.extend(!0,[],this._colKeysCalcValues),f||(u==2?s.length>0:h.length>0)){for(r=0,i="",n=t.length-1;n>=0;n--)if(!ej.isNullOrUndefined(t[n])){ej.isNullOrUndefined(t[n].cellType)||ej.isNullOrUndefined(t[n].uniqueName)||(t[n].uniqueName.indexOf(">#>")==-1?t[n].uniqueName==i:t[n].uniqueName.indexOf(i)!=-1)||(i="",r=t[n].level);f&&t[n].level==0&&i==""&&(r=0);(f?t[n].level!=r:i!="")||ej.isNullOrUndefined(t[n].cellType)||ej.isNullOrUndefined(t[n].uniqueName)||!(u==2?s.indexOf(t[n].uniqueName)>-1:h.indexOf(t[n].uniqueName)>-1)||(i=t[n].uniqueName,r++);var o=i==t[n].uniqueName,c=t[n].cellType=="RGTot",l=t[n].level==r;(f?o||c||l||(u==2?r>a-2:r>v-2):i==""||ej.isNullOrUndefined(t[n].uniqueName)||o)&&((f?l:o)&&!ej.isNullOrUndefined(t[n].keys)&&t[n].keys.length>0&&!c&&t[n].uniqueName!=""&&(t[n].keys[t[n].keys.length-1]=t[n].keys[t[n].keys.length-1].replace(" Total","").replace("%####%",""),t[n].expander=2),e.push(t[n]))}u==2?this._rowKeysCalcValues=$.extend(!0,[],e.reverse()):this._colKeysCalcValues=$.extend(!0,[],e.reverse())}e=[];u--}while(u>0)},_applyPaging:function(){var n=[],t;this._controlObj._seriesPageCount=this._controlObj.model.enablePaging?this._rowKeysCalcValues.length:Math.ceil(this._rowKeysCalcValues.length/this._controlObj.model.dataSource.pagerOptions.seriesPageSize);this._controlObj._categPageCount=this._controlObj.model.enablePaging?this._colKeysCalcValues.length*(this._controlObj.model.dataSource.values.length>0?this._controlObj.model.dataSource.values.length:1):Math.ceil(this._colKeysCalcValues.length*(this._controlObj.model.dataSource.values.length>0?this._controlObj.model.dataSource.values.length:1)/this._controlObj.model.dataSource.pagerOptions.categoricalPageSize);this._controlObj._categPageCount=this._controlObj._categPageCount==0?1:this._controlObj._categPageCount;this._controlObj.model.dataSource.pagerOptions.seriesCurrentPage=this._controlObj.model.dataSource.pagerOptions.seriesCurrentPage*this._controlObj.model.dataSource.pagerOptions.seriesPageSize>this._rowKeysCalcValues.length?Math.ceil(this._rowKeysCalcValues.length/this._controlObj.model.dataSource.pagerOptions.seriesPageSize):this._controlObj.model.dataSource.pagerOptions.seriesCurrentPage;this._controlObj.model.dataSource.pagerOptions.categoricalCurrentPage=this._controlObj.model.dataSource.pagerOptions.categoricalCurrentPage*this._controlObj.model.dataSource.pagerOptions.categoricalPageSize>this._colKeysCalcValues.length*(this._controlObj.model.dataSource.values.length>0?this._controlObj.model.dataSource.values.length:1)?Math.ceil(this._colKeysCalcValues.length*(this._controlObj.model.dataSource.values.length>0?this._controlObj.model.dataSource.values.length:1)/this._controlObj.model.dataSource.pagerOptions.categoricalPageSize):this._controlObj.model.dataSource.pagerOptions.categoricalCurrentPage;this._controlObj.model.dataSource.pagerOptions.categoricalCurrentPage=this._controlObj.model.dataSource.pagerOptions.categoricalCurrentPage==0?1:this._controlObj.model.dataSource.pagerOptions.categoricalCurrentPage;this._controlObj._seriesCurrentPage=this._controlObj.model.dataSource.pagerOptions.seriesCurrentPage;this._controlObj._categCurrentPage=this._controlObj.model.dataSource.pagerOptions.categoricalCurrentPage;var r=this._controlObj.model.dataSource.pagerOptions.seriesCurrentPage*this._controlObj.model.dataSource.pagerOptions.seriesPageSize-this._controlObj.model.dataSource.pagerOptions.seriesPageSize,i=Math.ceil((this._controlObj.model.dataSource.pagerOptions.categoricalCurrentPage*this._controlObj.model.dataSource.pagerOptions.categoricalPageSize-this._controlObj.model.dataSource.pagerOptions.categoricalPageSize)/(this._controlObj.model.dataSource.values.length>0?this._controlObj.model.dataSource.values.length:1)),u=r+this._controlObj.model.dataSource.pagerOptions.seriesPageSize,f=Math.ceil(this._controlObj.model.dataSource.pagerOptions.categoricalCurrentPage*this._controlObj.model.dataSource.pagerOptions.categoricalPageSize/(this._controlObj.model.dataSource.values.length>0?this._controlObj.model.dataSource.values.length:1)),i=(this._controlObj.model.dataSource.pagerOptions.categoricalCurrentPage-1)*this._controlObj.model.dataSource.pagerOptions.categoricalPageSize%this._controlObj.model.dataSource.values.length>0?i-1:i;for(t=r;t<u;t++)n.push(this._rowKeysCalcValues[t]);for(n=this._spanCalculation(n),this._rowKeysCalcValues=$.extend(!0,[],n),n=[],t=i;t<f;t++)n.push(this._colKeysCalcValues[t]);n=this._spanCalculation(n);this._colKeysCalcValues=$.extend(!0,[],n)},_spanCalculation:function(n){for(var u,e,i,r,f,t=0;t<n.length;t++)if(n.length>0&&!ej.isNullOrUndefined(n[t])&&!ej.isNullOrUndefined(n[t].uniqueName)){for(u=n[t].uniqueName.split(">#>"),e=[],n[t].keys[n[t].level]=n[t].keys[0],n[t].span=ej.isNullOrUndefined(n[t].span)?[]:n[t].span,i=ej.isNullOrUndefined(n[t].level)?n[t].span.length>0?n[t].span.length-2:n[t].keys.length-2:n[t].level-1;i>=0;i--){r=t+1;n[t].keys[i]=u[i];n[t].span[i]=1;do{if(!ej.isNullOrUndefined(n[r])&&n[r].uniqueName.split(">#>").indexOf(u[i])>-1&&n[r].uniqueName.split(">#>").indexOf(u[i])<n[r].uniqueName.split(">#>").length-1)n[t].span[i]++;else break;r++}while(r<n.length)}ej.isNullOrUndefined(n[t].span)||n[t].span.length!=0||delete n[t].span}return f=n.length,$.map(n,function(n,t){if(!ej.isNullOrUndefined(n)&&!ej.isNullOrUndefined(n.span))for(var i=0;i<n.span.length;i++)n.span[i]=n.span[i]>f-t?f-t:n.span[i];return n})},_applyValueSorting:function(n){for(var s,o,g,nt,a,p,tt,y,c,e,t,rt,f=[],u=[],r=0,v=0,d="",i=0;i<this._transposeEngine.length;i++)for(f[r]=[],ej.isNullOrUndefined(u[v])&&(u[v]=[]),t=0;t<this._transposeEngine[i].length&&this._valueSorting==Number(this._transposeEngine[i][t].Index.split(",")[0]);t++)(this._transposeEngine[i][t].CSS=="colheader"||$.trim(this._transposeEngine[i][t].CSS)=="calc"||this._transposeEngine[i][t].CSS=="colheader calc"||this._transposeEngine[i][t].CSS=="summary cstot"||this._transposeEngine[i][t].CSS=="summary cstot calc"||this._transposeEngine[i][t].CSS=="summary cgtot"||this._transposeEngine[i][t].CSS=="summary cgtot calc")&&(d=d==""?this._transposeEngine[i][t].Value:d+n.valueSortSettings.headerDelimiters+this._transposeEngine[i][t].Value),this._transposeEngine[n.dataSource.rows.length][t].CSS=="value"&&(f[r].push(this._transposeEngine[i][t]),v=0),this._transposeEngine[n.dataSource.rows.length][t].CSS=="summary value"&&(u[v].push(this._transposeEngine[i][t]),f[r].length>0&&(r=r+1,f[r]=[]),this._transposeEngine[0][t].CSS!="summary rgtot"&&u[v].length>0&&v++,ej.isNullOrUndefined(u[v])&&(u[v]=[]));for(n.valueSortSettings.headerText=d,n.valueSortSettings.sortOrder=this._sort,r=0;r<f.length;r++)for(i=0;i<f[r].length;i++)for(t=i;t<f[r].length;t++)g=this._sort=="descending"?this._getNumber(f[r][i].Value)<this._getNumber(f[r][t].Value):this._getNumber(f[r][i].Value)>this._getNumber(f[r][t].Value),g&&(nt=f[r][t],f[r][t]=f[r][i],f[r][i]=nt);for(r=0;r<f.length;r++)f[r].length==0&&f.splice(r,1);for(s=[],i=0;i<this._tRowCnt-1;i++)s[i]={index:0,level:-1};var l=0,h=[],b=[];for(r=u.length-2;r>=0;r--){for(o=0,t=0;t<u[r].length;t++)for(i=t;i<u[r].length;i++)g=this._sort=="descending"?this._getNumber(u[r][t].Value)<this._getNumber(u[r][i].Value):this._getNumber(u[r][t].Value)>this._getNumber(u[r][i].Value),g&&(nt=u[r][i],u[r][i]=u[r][t],u[r][t]=nt);if(l==0)for(t=0;t<u[r].length;t++)o=o==0?this._transposeEngine[l][Number(u[r][t].Index.split(",")[1])].MCnt+n.dataSource.columns.length+1:this._transposeEngine[l][Number(u[r][t].Index.split(",")[1])].MCnt+o+1,s[o]={index:Number(u[r][t].Index.split(",")[1]),level:l},h[t]==undefined&&(h[t]={}),h[t].end=Number(u[r][t].Index.split(",")[1]),h[t].start=Number(u[r][t].Index.split(",")[1])-this._transposeEngine[l][Number(u[r][t].Index.split(",")[1])].MCnt;else{for(a=[],p=0,c=0;c<h.length;c++){for(a=[],t=0;t<u[r].length;t++)Number(u[r][t].Index.split(",")[1])>h[c].start&&Number(u[r][t].Index.split(",")[1])<h[c].end&&a.push(u[r][t]);for(t=0;t<a.length;t++)o=o==0?this._transposeEngine[l][Number(a[t].Index.split(",")[1])].MCnt+n.dataSource.columns.length+1:this._transposeEngine[l][Number(a[t].Index.split(",")[1])].MCnt+o+1,s[o]={index:Number(a[t].Index.split(",")[1]),level:l},b[p]==undefined&&(b[p]={}),b[p].end=Number(a[t].Index.split(",")[1]),b[p].start=Number(a[t].Index.split(",")[1])-this._transposeEngine[l][Number(a[t].Index.split(",")[1])].MCnt,p++;while(o<this._tRowCnt-2&&s[o].level>s[o+1].level)o++;o--}h=jQuery.extend(!0,[],b)}l++}if(u.length==1&&u[0].length>0)for(tt=n.dataSource.columns.length+1,i=0;i<f[0].length;i++)s[tt]={index:Number(f[0][i].Index.split(",")[1])},tt++;for(y=n.dataSource.columns.length+1,c=0;c<h.length;c++)for(r=0;r<f.length;r++)if(Number(f[r][0].Index.split(",")[1])>=h[c].start&&Number(f[r][0].Index.split(",")[1])<=h[c].end){for(t=0,i=h[c].start;i<h[c].end;i++)s[y]={index:Number(f[r][t].Index.split(",")[1])},t++,y++;for(y++;s[y].index!=0&&y<=s.length;)y++}for(e=[],i=0;i<n.dataSource.columns.length+1;i++)s[i]={index:i};for(i=0;i<s.length-1;i++)e.push(this._gridMatrix[s[i].index]);for(e.push(this._gridMatrix[i]),i=0;i<this._gridMatrix.length&&this._gridMatrix[i]!=undefined&&e[i]!=undefined;i++)for(t=0;t<this._gridMatrix[i].length&&this._gridMatrix[i][t]!=undefined;t++)this._gridMatrix[i][t]!=undefined&&e[i][t]!=undefined&&(e[i][t].Index=this._gridMatrix[i][t].Index);for(t=0;t<n.dataSource.rows.length-1;t++){var w=n.dataSource.columns.length+1,k=0,r=0,it=!1;for(i=n.dataSource.columns.length+(n.dataSource.values.length>0?1:0);i<e.length;i++)if(e[i]!=undefined&&e[i][t]!=undefined&&e[i][t].CSS=="rowheader"&&e[i][t].RowSpan!=undefined&&(it=!0,k=k>e[i][t].RowSpan?k:e[i][t].RowSpan),e[i]!=undefined&&e[i][t]!=undefined&&e[i][t].CSS=="summary rstot"&&it&&!ej.isNullOrUndefined(e[w])){for(it=!1;e[w]==undefined||e[w][t]==undefined;)w++;e[w][t].RowSpan=k;k=0;rt=0;w=i+1;r++}}this._gridMatrix=e;this._transposeEngineCreation()},_transposeEngineCreation:function(){var n,t,i;for(this._tColCnt=this._isPaging&&this._gridMatrix.length>0?!ej.isNullOrUndefined(this._gridMatrix[0])&&this._gridMatrix[0].length>0?this._gridMatrix[0].length+1:this._gridMatrix[this._gridMatrix.length-1].length+1:this._tColCnt,n=0;n<this._tRowCnt-1;n++)for(t=0;t<this._tColCnt-1;t++)this._transposeEngine[t]==undefined&&(this._transposeEngine[t]=[]),this._gridMatrix[n]!=undefined&&this._gridMatrix[n][t]!=undefined?(i=typeof this._gridMatrix[n][t]=="string"||typeof this._gridMatrix[n][t]=="number"||typeof this._gridMatrix[n][t]=="object"?this._gridMatrix[n][t].Value==0?this._gridMatrix[n][t].Value:this._gridMatrix[n][t].Value==undefined?"":ej.isNullOrUndefined(this._gridMatrix[n][t].valueText)?this._gridMatrix[n][t].Value:this._gridMatrix[n][t].valueText:"",this._transposeEngine[t][n]={Index:t+","+n,CSS:this._gridMatrix[n][t].CSS,Value:typeof i=="string"&&i.indexOf("%####%")>-1?i.replace("%####%",""):i,State:this._gridMatrix[n][t].State,RowSpan:this._gridMatrix[n][t].RowSpan!=undefined?this._gridMatrix[n][t].RowSpan:1,ColSpan:this._gridMatrix[n][t].ColSpan!=undefined?this._gridMatrix[n][t].ColSpan:1,Info:this._gridMatrix[n][t].Info,Span:"None",Expander:this._gridMatrix[n][t].Expander,MCnt:this._gridMatrix[n][t].MCnt!=undefined?this._gridMatrix[n][t].MCnt:1}):this._gridMatrix[n]!=undefined&&(this._transposeEngine[t][n]={Index:t+","+n,CSS:"none",Value:"",State:0,RowSpan:1,ColSpan:1,Info:"",Span:"None",Expander:0})},_filterKeyValues:function(n){var t=this;return $.grep(n,function(n){for(var i=!1,r=0;r<(!ej.isNullOrUndefined(n.uniqueName)&&n.uniqueName.split(">#>").length);r++)if(i=$.inArray(n.uniqueName.split(">#>")[r],t._advancedFilterInfo)>-1?!0:i,i=$.inArray(n.uniqueName,t._advancedFilterInfo)>-1?!0:i,i)break;if(!i)return n})},_getFilteredIndex:function(n,t,i,r){return $.map(t,function(t,u){if(n.length>0&&r[i].keys.length>0&&n[r[i].keys.length-1].advancedFilter&&n[r[i].keys.length-1].advancedFilter.length>0&&(n[r[i].keys.length-1].advancedFilter[0].measure==t.fieldName||n[r[i].keys.length-1].advancedFilter[0].name==t.fieldName))return u})[0]},_applyValueFilter:function(n,t){var i=!1,u,r;if(n.length>0&&!ej.isNullOrUndefined(t)){t=this._isNumber(t)?t:ej.parseFloat(t,this._locale);u=n[0].valueFilterOperator;r=n[0].values;switch(u.toLowerCase()){case"equals":i=!(t==JSON.parse(n[0].values[0]));break;case"notequals":i=!(t!=JSON.parse(n[0].values[0]));break;case"greaterthan":i=!(t>JSON.parse(n[0].values[0]));break;case"greaterthanorequalto":i=!(t>=JSON.parse(n[0].values[0]));break;case"lessthan":i=!(t<JSON.parse(n[0].values[0]));break;case"lessthanorequalto":i=!(t<=JSON.parse(n[0].values[0]));break;case"between":i=!(t>JSON.parse(r[0]))||!(t<JSON.parse(r[1]));break;case"notbetween":i=!(!(t>JSON.parse(r[0]))||!(t<JSON.parse(r[1])));break;default:i=!(t==JSON.parse(n[0].values[0]))}}return i},_applyLabelFilter:function(n,t){if(n.length>0){var u=n[0].labelFilterOperator,i=n[0].values,r=!1;t=ej.isNullOrUndefined(t)?"":t.toString();switch(u.toLowerCase()){case"equals":r=!(t.toLowerCase()==i[0].toLowerCase());break;case"notequals":r=t.toLowerCase()==i[0].toLowerCase();break;case"contains":r=!(t.toLowerCase().indexOf(i[0].toLowerCase())>-1);break;case"notcontains":r=t.toLowerCase().indexOf(i[0].toLowerCase())>-1;break;case"beginswith":r=!(t.toLowerCase().indexOf(i[0].toLowerCase())==0);break;case"notbeginswith":r=t.toLowerCase().indexOf(i[0].toLowerCase())==0;break;case"endswith":r=ej.isNullOrUndefined(t.toLowerCase().match(i[0].toLowerCase()+"$"));break;case"notendswith":r=!ej.isNullOrUndefined(t.toLowerCase().match(i[0].toLowerCase()+"$"));break;case"greaterthan":r=!(t.toLowerCase()>i[0].toLowerCase());break;case"greaterthanorequalto":r=!(t.toLowerCase()>=i[0].toLowerCase());break;case"lessthan":r=!(t.toLowerCase()<i[0].toLowerCase());break;case"lessthanorequalto":r=!(t.toLowerCase()<=i[0].toLowerCase());break;case"between":r=!(t.toLowerCase()>i[0].toLowerCase())&&!(t.toLowerCase()<i[1].toLowerCase());break;case"notbetween":r=!(!(t.toLowerCase()>i[0].toLowerCase())&&!(t.toLowerCase()<i[1].toLowerCase()));break;default:r=!(t.toLowerCase().indexOf(i[0].toLowerCase())>-1)}return r}},getMembers:function(n,t){var i,r,u;if(ej.isNullOrUndefined(t))return ej.isNullOrUndefined(this._fieldMembers)?[]:ej.isNullOrUndefined(this._fieldMembers[n])?[]:this._fieldMembers[n];for(i={},r=0;r<t.length;r++)u=t[r],$.each(u,function(n,t){ej.isNullOrUndefined(i[n])?i[n]=[t]:i[n].indexOf(t)<0&&i[n].push(t)});return i[n]},_powerFunction:function(n){if(n.indexOf("^")>-1){for(var t=[];n.indexOf("(")>-1;)n=n.replace(/(\([^\(\)]*\))/g,function(n,i){return t.push(i),"~"+(t.length-1)});for(t.push(n),n="~"+(t.length-1);n.indexOf("~")>-1;)n=n.replace(new RegExp("~(\\d+)","g"),function(n,i){return t[i].replace(/(\w*)\^(\w*)/g,"Math.pow($1,$2)")})}return n},_calculatedFieldSummaryValue:function(index,calValue,dataSource){var formula,i,value,oldValue,newValue;if(dataSource.values[index].isCalculatedField==!0){for(formula=dataSource.values[index].formula.replace(/\s/g,""),i=0;i<dataSource.values.length;i++)dataSource.values[i].isCalculatedField!=!0&&(value=ej.globalize.parseFloat(calValue[i]==null?"0":dataSource.values[i].format=="date"?this._dateToInt(calValue[i].toString()).toString():dataSource.values[i].format=="time"?this._dateToInt(new Date("1900",calValue[i].toString().split(":")[0],calValue[i].toString().split(":")[1],calValue[i].toString().split(":")[2].split(" ")[0]).toString()).toString():calValue[i].toString(),this._locale),formula=formula.replace(new RegExp(dataSource.values[i].fieldName,"g"),dataSource.values[i].format=="percentage"&&value!=0?value<0?"("+value/100+")":value:value<0?"("+value+")":value));return formula.indexOf("^")>-1&&(formula=this._powerFunction(formula)),oldValue=!isNaN(eval(formula))&&isFinite(eval(formula))?this._getReflectedValue({calcField:eval(formula)},"calcField",dataSource.values[index].format,dataSource.values[index].formatString):dataSource.values[index].format==null?0:"0",newValue=dataSource.values[index].format==null?0:"0",this._getSummaryValue(oldValue,newValue,1,this._summaryTypes[index]=="count"?"sum":this._summaryTypes[index],dataSource.values[index].format,dataSource.values[index].formatString)}},_sortHeaders:function(n,t,i){for(var r=[],e=[],f=0,o,u=t[i].sortOrder!=ej.PivotAnalysis.SortOrder.None?ej.isNullOrUndefined(t[i].format)?ej.DataManager(n).executeLocal(ej.Query().sortBy("keys."+i,t[i].sortOrder!=null&&t[i].sortOrder==ej.PivotAnalysis.SortOrder.Descending?ej.sortOrder.Descending:ej.sortOrder.Ascending,!1)):this._getSortedHeaders(n,t,i):n;f<u.length;)o=u[f].keys[i],r=$.grep(u,function(n){return n.keys[i]==o}),r.length>1&&i+1<u[f].keys.length&&(r=this._sortHeaders(r,t,i+1)),e=e.concat(r),f+=r.length;return e},_getSortedHeaders:function(n,t,i){return t[i].sortOrder==ej.PivotAnalysis.SortOrder.Descending?n.sort(function(n,r){return ej.PivotAnalysis._formatToInt(r.keys[i],ej.isNullOrUndefined(t[i].format)?null:t[i].format,ej.isNullOrUndefined(t[i].formatString)?null:t[i].formatString)-ej.PivotAnalysis._formatToInt(n.keys[i],ej.isNullOrUndefined(t[i].format)?null:t[i].format,ej.isNullOrUndefined(t[i].formatString)?null:t[i].formatString)}):n.sort(function(n,r){return ej.PivotAnalysis._formatToInt(n.keys[i],ej.isNullOrUndefined(t[i].format)?null:t[i].format,ej.isNullOrUndefined(t[i].formatString)?null:t[i].formatString)-ej.PivotAnalysis._formatToInt(r.keys[i],ej.isNullOrUndefined(t[i].format)?null:t[i].format,ej.isNullOrUndefined(t[i].formatString)?null:t[i].formatString)})},_isValidTime:function(n){var t=n.indexOf(":")>-1?n.split(":"):[];return t.length==3?(t[3]=t[2].indexOf(" ")>-1?t[2].split(" ")[1]:"AM",t[2]=t[2].indexOf(" ")>-1?t[2].split(" ")[0]:t[2],Number(t[0])>-1&&Number(t[0])<13&&Number(t[1])>-1&&Number(t[1])<61&&jQuery.isNumeric(t[2])&&(t[3]=="AM"||t[3]=="PM")?!0:!1):!1},_getNumber:function(n,t){return ej.isNullOrUndefined(n)||(jQuery.isNumeric(n.toString().replace(/[,.]/g,""))?(n=n.toString().replace(/[,]/g,ej.preferredCulture(this._locale).numberFormat[","]==","?"":"~").replace(/[.]/g,ej.preferredCulture(this._locale).numberFormat["."]=="."?".":""),n=Number(n.toString().replace(/[~]/g,"."))):n=this._isValidTime(n)?this._formatToInt(n,"time",t):ej.isNullOrUndefined(ej.parseDate(n,t==undefined?"MM/dd/yyyy":t))?n.indexOf("/")!=-1&&n.split("/")==2?this._formatToInt(n,"fraction",undefined):$.trim(n).indexOf("%")==$.trim(n).length-1?Number(n.replace(/[^\d.-]/g,""))/100:/[a-zA-Z]/.test(n)?1:Number(n.replace(/[^\d.-]/g,"")):this._formatToInt(n,"date",t)),n},_setFormat:function(n,t,i){switch(t){case"percentage":var r=jQuery.isNumeric(n)?1:100;n=typeof n=="string"?$.trim(n.toString().split(ej.preferredCulture(this._locale).numberFormat[","]).join("").replace(",",".").replace(ej.preferredCulture(this._locale).numberFormat.percent.symbol,"")):n;n=jQuery.isNumeric(n)?n:0;n=ej.widgetBase.formatting(i||"{0:P}",n==""?0:n/r,this._locale);break;case"decimal":case"number":n=typeof n=="string"?n.toString().split(ej.preferredCulture(this._locale).numberFormat[","]).join("").replace(",","."):n;n=jQuery.isNumeric(n)?n:0;n=ej.widgetBase.formatting(i||"{0:N}",n,this._locale);break;case"currency":n=typeof n=="string"?$.trim(n.toString().split(ej.preferredCulture(this._locale).numberFormat[","]).join("").replace(",",".").replace(ej.preferredCulture(this._locale).numberFormat.currency.symbol,"")):n;n=jQuery.isNumeric(n)?n:0;n=ej.widgetBase.formatting(i||"{0:C2}",n==""?0:n,this._locale);break;case"date":jQuery.isNumeric(n)?(n=new Date((Number(n)-2)*864e5+new Date("01/01/1900").getTime()),this._isDateTime(n)&&(n=ej.widgetBase.formatting("{0:"+(i==undefined?"MM/dd/yyyy":i)+"}",n,this._locale))):new Date(n)!="Invalid Date"?(n=new Date(n),n=ej.widgetBase.formatting("{0:"+(i==undefined?"MM/dd/yyyy":i)+"}",n,this._locale)):(n=new Date((Number(0)-2)*864e5+new Date("01/01/1900").getTime()),n=ej.widgetBase.formatting("{0:"+(i==undefined?"MM/dd/yyyy":i)+"}",n,this._locale));break;case"scientific":n=typeof n=="string"?n.split(ej.preferredCulture(this._locale).numberFormat[","]).join("").replace(",","."):n;n=jQuery.isNumeric(n)?n:0;n=Number(n).toExponential(2).replace("e","E");break;case"accounting":n=this._toAccounting(n,i||"{0:C2}",this._locale);break;case"time":jQuery.isNumeric(n)?(n=new Date((Number(n)-2)*864e5+new Date("01/01/1900").getTime()),this._isDateTime(n)&&(n=ej.widgetBase.formatting("{0:h:mm:ss tt}",n,this._locale))):this._isValidTime(n)?n=ej.widgetBase.formatting("{0:h:mm:ss tt}",n,this._locale):(n=new Date((Number(0)-2)*864e5+new Date("01/01/1900").getTime()),n=ej.widgetBase.formatting("{0:h:mm:ss tt}",n,this._locale));break;case"fraction":n=typeof n=="string"?n.split(ej.preferredCulture(this._locale).numberFormat[","]).join("").replace(",","."):n;n=jQuery.isNumeric(n)?this._toFraction(n):this._toFraction(0);n="numerator"in n?n.integer+" "+n.numerator+"/"+n.denominator:n.integer;break;default:n}return n},_getReflectedValue:function(n,t,i,r){var u=$(n).prop(t);return u==null?u:(jQuery.isNumeric(u)||new Date(u)!="Invalid Date"||(i=r=null),this._setFormat(u,i,r))},_formatToInt:function(value,format,formatString){var time;switch(format){case"date":value=ej.parseDate(value.toString(),formatString==undefined?"MM/dd/yyyy":formatString,this._locale)!=null?this._dateToInt(ej.parseDate(value,formatString==undefined?"MM/dd/yyyy":formatString,this._locale)):value;break;case"percentage":value=value!=0?ej.globalize.parseFloat(value.toString(),this._locale)/100:Number(value);break;case"currency":case"accounting":value=value!=0?ej.globalize.parseFloat(ej.globalize.format(value.toString(),"c",this._locale),this._locale):Number(value);break;case"decimal":case"number":value=value!=0?ej.globalize.parseFloat(value.toString(),this._locale):Number(value);break;case"scientific":value=value!=0||value==0?parseFloat(value):Number(value);break;case"time":this._isNumber(value)||(time=value.toString().split(":"),time=new Date("1900",time[0],time[1],time[2].split(" ")[0]).toString(),value=this._dateToInt(time));break;case"fraction":var temp1=parseFloat(value.toString().split(" ")[0]),temp2=eval(value.toString().split(" ")[1])!=undefined?parseFloat(eval(value.toString().split(" ")[1])):parseFloat(0),value=value!=0?temp1+temp2:Number(value)}return value},_isDateTime:function(n){return Object.prototype.toString.call(n)==="[object Date]"&&!isNaN(n.valueOf())},_toAccounting:function(n,t,i){var s=ej.preferredCulture(i).numberFormat,f,e,u=s.currency.symbol,r,o;return val=ej.widgetBase.formatting(t,n,i),val=val.indexOf(" ")>-1?val.split(" ").join(""):val,r=val.replace(u,""),o=jQuery.isNumeric(r.toString().split(s[","]).join("").replace(",","."))?val.indexOf(u):0,r=jQuery.isNumeric(r.toString().split(",").join(""))?r:"0.00",!o||Number(r)<0&&o===1?(f=u,e=r):(f=r,e=u),f+"   "+e},_toFraction:function(n){if(this._isNumber(n)){var r=n.toString(),e=r.split(".")[0],t=r.split(".")[1];if(!t)return{integer:n};var i=(+t).toString(),u=this._getPlaceValue(t,i),f=this._getGCD(i,u);return{integer:e,numerator:Number(i)/f,denominator:Number(u)/f}}return null},_isNumber:function(n){return n-parseFloat(n)>=0},_getGCD:function(n,t){return(n=Number(n),t=Number(t),!t)?n:this._getGCD(t,n%t)},_getPlaceValue:function(n,t){var i=n.indexOf(t)+t.length;return"1"+Array(i+1).join("0")},_cellEdit:function(n,t,i,r){return $.each(this._editCellsInfo,function(u,f){if(n==(ej.isNullOrUndefined(f.row)&&ej.isNullOrUndefined(f.column)?"":ej.isNullOrUndefined(f.row)?f.column:ej.isNullOrUndefined(f.column)?f.row:f.row+">#>"+f.column)&&i==parseInt(f.value.split("#")[0]))return t=ej.PivotAnalysis._setFormat(ej.isNullOrUndefined(r.format)?jQuery.isNumeric(f.value.split("#")[1].toString().split(",").join(""))?parseFloat(f.value.split("#")[1].toString().split(",").join("")):0:f.value.split("#")[1],r.format,r.formatString),!1}),t},_isMemberExist:function(n,t,i,r){var c=this,f,o,s,u,h,e;switch(n){case"row":if(o=!1,s=this._rowKeysCalcValues.length==0?0:this._rowMembers[t.uniqueName],s>-1&&(o=!0,f=s),this._rowKeysCalcValues.length==0&&(this._rowKeysCalcValues.push(t),this._rowMembers[t.uniqueName]=this._rowKeysCalcValues.length-1),o)for(u=0;u<i.keys.length;u++)if(this._rowKeysCalcValues[f].value==null){this._rowKeysCalcValues[f].value=i;break}else this._rowKeysCalcValues[f].value.keys[u]+=i.keys[u];else this._rowKeysCalcValues.push(t),this._rowMembers[t.uniqueName]=this._rowKeysCalcValues.length-1;break;case"column":if(o=!1,s=this._colKeysCalcValues.length==0?0:this._colMembers[t.uniqueName],s>-1&&(o=!0,f=s),this._colKeysCalcValues.length==0&&(r.columns.length>0||r.values.length>0)&&(this._colKeysCalcValues.push(t),this._colMembers[t.uniqueName]=this._colKeysCalcValues.length-1),o)for(u=0;u<i.keys.length;u++)if(this._colKeysCalcValues[f].value==null){this._colKeysCalcValues[f].value=i;break}else this._colKeysCalcValues[f].value.keys[u]+=i.keys[u];else(r.columns.length>0||r.values.length>0)&&(this._colKeysCalcValues.push(t),this._colMembers[t.uniqueName]=this._colKeysCalcValues.length-1);break;case"calc":if(o=!1,s=this._tableKeysCalcValues.length==0?0:this._calcMembers[t.uniqueName],s>-1&&(o=!0,f=s),this._tableKeysCalcValues.length==0&&(this._tableKeysCalcValues.push(t),this._calcMembers[t.uniqueName]=this._tableKeysCalcValues.length-1),o)for(u=0;u<i.keys.length;u++)if(this._tableKeysCalcValues[f].value==null){for(this._tableKeysCalcValues[f].value=i,this._tableKeysCalcValues[f].value.count=1,e=0;e<i.keys.length;e++)this._summaryTypes[e]==ej.PivotAnalysis.SummaryType.Count&&(this._tableKeysCalcValues[f].value.keys[e]=this._tableKeysCalcValues[f].value.count);break}else h=r.values[u],this._tableKeysCalcValues[f].value.uniqueName[u]==i.uniqueName[u]&&(ej.isNullOrUndefined(h.formatString)||h.formatString.toLowerCase()!="single-entry")||(this._tableKeysCalcValues[f].value.uniqueName[u]=" "),u==0&&this._tableKeysCalcValues[f].value.count++,r.values[u].isCalculatedField!=!0&&(this._tableKeysCalcValues[f].value.keys[u]=this._getSummaryValue(this._tableKeysCalcValues[f].value.keys[u],i.keys[u],this._tableKeysCalcValues[f].value.count,this._summaryTypes[u],r.values[u].format,r.values[u].formatString));else for(this._tableKeysCalcValues.push(t),this._calcMembers[t.uniqueName]=this._tableKeysCalcValues.length-1,this._tableKeysCalcValues[this._tableKeysCalcValues.length-1].value=i,e=0;e<i.keys.length;e++)this._summaryTypes[e]==ej.PivotAnalysis.SummaryType.Count&&(this._tableKeysCalcValues[this._tableKeysCalcValues.length-1].value.keys[e]=1),this._tableKeysCalcValues[this._tableKeysCalcValues.length-1].value.count=1}},_getSummaryValue:function(n,t,i,r,u,f){if(t=ej.isNullOrUndefined(t)?null:t==0?0:t,n=ej.isNullOrUndefined(n)?null:n==0?0:n,!ej.isNullOrUndefined(t)&&!ej.isNullOrUndefined(n)){n=this._formatToInt(n,u,f);t=this._formatToInt(t,u,f);switch(r){case ej.PivotAnalysis.SummaryType.Sum:n+=t;break;case ej.PivotAnalysis.SummaryType.Average:n=(n*(i-1)+t)/i;Math.floor(n)!=n&&(n=Number(n.toFixed(2)));break;case ej.PivotAnalysis.SummaryType.Min:n=i==1?t:Math.min(n,t);break;case ej.PivotAnalysis.SummaryType.Max:n=i==1?t:Math.max(n,t);break;case ej.PivotAnalysis.SummaryType.Count:n=i}switch(u){case"percentage":n=ej.widgetBase.formatting(f||"{0:P}",n,this._locale);break;case"decimal":case"number":n=ej.widgetBase.formatting(f||"{0:N}",n,this._locale);break;case"date":n=new Date((Number(n)-2)*864e5+new Date("01/01/1900").getTime());this._isDateTime(n)&&(n=ej.widgetBase.formatting("{0:"+(f==undefined?"MM/dd/yyyy":f)+"}",n,this._locale));break;case"currency":n=ej.widgetBase.formatting(f||"{0:C2}",n,this._locale);break;case"scientific":n=n.toExponential(2).replace("e","E");break;case"accounting":n=this._toAccounting(n,f||"{0:C2}",this._locale);break;case"time":n=new Date((Number(n)-2)*864e5+new Date("01/01/1900").getTime());this._isDateTime(n)&&(n=ej.widgetBase.formatting("{0:h:mm:ss tt}",n,this._locale));break;case"fraction":n=this._toFraction(n);n="numerator"in n?n.integer+" "+n.numerator+"/"+n.denominator:n.integer;break;default:n}}return n},_dateToInt:function(n){var t=new Date("01/01/1900"),i=this._isDateTime(n)?n:new Date(n),r=i.getTime()-t.getTime();return r/864e5+2},_insertTotalHeader:function(n,t){var s,r,o,u,h,i;if(n.length<=1){t.push({cellType:"RGTot",keys:["Grand Total"],tot:"sub"});return}for(s=-1,r=n.length-2;r>=0;r--)if(s++,o=t.length,o>0){u=t[0].uniqueName.split(">#>");h=t[0].keys[s];u=u.slice(0,r+1).join(">#>");var e="",f=0,c="";for(i=1;i<=o;i++)t[i]!=undefined?(e=t[i].uniqueName.split(">#>"),e=e.slice(0,r+1).join(">#>"),c=t[i].keys[r+1]):e=undefined,e!=undefined&&e.indexOf("%####%")==-1?u!=undefined&&e!=u&&(t.splice(i,0,{keys:[u.split(">#>")[r]+" Total%####%"],cellType:"SubTot",uniqueName:u,level:r,mSpan:i-f}),t[f].span==undefined&&(t[f].span=[]),t[f].span[r]=i-f,i++,o++,u=e,f=i,h=c):u!=undefined&&(t.splice(i,0,{keys:[u.split(">#>")[r]+" Total%####%"],cellType:"SubTot:",uniqueName:u,level:r,mSpan:i-f}),t[f].span==undefined&&(t[f].span=[]),t[f].span[r]=i-f,i++,o++,u=e,f=i,h=c)}t.push({cellType:"RGTot",keys:["Grand Total"]})},_isPreviousLevelEqual:function(n,t,i,r){for(var u=r-1;u>=0;u--)return n[t].keys[u]!=n[i].keys[u]?!0:!1},_calculateValues:function(n){var t=this._rowKeysCalcValues.length+(n.values.length>0?1:0),u=this._colKeysCalcValues.length==0?1:this._colKeysCalcValues.length*(n.values.length==0?1:n.values.length)+1-(this._isPaging&&n.values.length>0&&n.pagerOptions.categoricalPageSize<n.values.length*this._colKeysCalcValues.length?n.values.length-(n.pagerOptions.categoricalCurrentPage*n.pagerOptions.categoricalPageSize%n.values.length==0?n.pagerOptions.categoricalCurrentPage*n.pagerOptions.categoricalPageSize:n.pagerOptions.categoricalCurrentPage*n.pagerOptions.categoricalPageSize%n.values.length):0),i,r;this._isPaging&&(n.columns.length==0?ej.PivotAnalysis._colHdrLen=0:$.each(this._colKeysCalcValues,function(n,t){!ej.isNullOrUndefined(t)&&t.keys.length>ej.PivotAnalysis._colHdrLen&&(ej.PivotAnalysis._colHdrLen=t.keys.length)}),n.rows.length==0?ej.PivotAnalysis._rowHdrLen=0:$.each(this._rowKeysCalcValues,function(n,t){!ej.isNullOrUndefined(t)&&t.keys.length>ej.PivotAnalysis._rowHdrLen&&(ej.PivotAnalysis._rowHdrLen=t.keys.length)}));this._gridMatrix=new Array(t+(this._isPaging?this._rowHdrLen:n.rows.length)+1);this._populateRowHeaders(n,t+(this._isPaging?this._rowHdrLen:n.rows.length)+1,n.rows.length==0?1:this._isPaging?this._rowHdrLen:n.rows.length);i=(this._isPaging?this._colHdrLen:n.columns.length)+1;r=(this._isPaging&&n.pagerOptions.categoricalPageSize+1<u?n.pagerOptions.categoricalPageSize+1:u)+(n.rows.length==0?1:this._isPaging?this._rowHdrLen:n.rows.length);this._populateColumnHeaders(n,i,r);this._advancedFilterInfo=[];n.values.length>0&&this._populateCalcTable(n);this._tRowCnt=t+i;this._tColCnt=r},_populateRowHeaders:function(n,t,i){var e,c,r,u,l,a;if(this._rowKeysCalcValues.length!=0){for(e=this._colKeysCalcValues.length==1&&this._colKeysCalcValues[0].cellType=="RGTot"?1+(n.values.length>0?1:0):(this._isPaging?this._colHdrLen:n.columns.length)+(n.values.length>0?1:0),this._gridMatrix=[],c="",r=0;r<this._rowKeysCalcValues.length;r++){c=this._rowKeysCalcValues[r];var f=0,o=0,s=0,h=0;if(this._rowKeysCalcValues[r].keys!=undefined&&this._rowKeysCalcValues[r].keys.length!=0)for(this._gridMatrix[e]=new Array(i),h=this._rowKeysCalcValues[r].keys.length,u=0;u<h;u++)this._rowKeysCalcValues[r].cellType!=undefined&&ej.isNullOrUndefined(this._rowKeysCalcValues[r].span)?this._rowKeysCalcValues[r].cellType.indexOf("SubTot")>-1?(o=f==0?this._rowKeysCalcValues[r].level+f:f,s=i-this._rowKeysCalcValues[r].level,this._cellType="summary rstot"):(this._cellType="summary rgtot",o=f,s=this._isPaging?this._rowHdrLen:n.rows.length):(this._cellType="rowheader",o=f,s=1),l=h>1?h-u>1?1:ej.isNullOrUndefined(this._rowKeysCalcValues[r].expander)?0:2:ej.isNullOrUndefined(this._rowKeysCalcValues[r].expander)?0:2,a=$.extend(!0,[],this._rowKeysCalcValues[r].keys),this._gridMatrix[e][o]={Index:o+","+e,CSS:this._cellType,Value:this._rowKeysCalcValues[r].keys[u],State:l,RowSpan:this._rowKeysCalcValues[r].span!=undefined?this._rowKeysCalcValues[r].span[u]:1,ColSpan:s,Info:a.splice(0,u+1).join(">#>"),Span:"None",Expander:this._rowKeysCalcValues[r].span!=undefined?this._rowKeysCalcValues[r].span[u]!=undefined?1:ej.isNullOrUndefined(this._rowKeysCalcValues[r].expander)?0:1:ej.isNullOrUndefined(this._rowKeysCalcValues[r].expander)?0:2,MCnt:this._rowKeysCalcValues[r].mSpan!=undefined&&ej.isNullOrUndefined(this._rowKeysCalcValues[r].span)?this._rowKeysCalcValues[r].mSpan:0},this._rowKeysCalcValues[r].level-u==1&&delete this._rowKeysCalcValues[r].span,f++;e++}this._cellType=""}},_populateColumnHeaders:function(n,t){var i,e,b,k,y,r,o;if(this._colKeysCalcValues.length!=0){var v,c,s,l,d,u,h,w=v=n.rows.length==0?1:this._isPaging?this._rowHdrLen:n.rows.length,a=n.values.length==0?1:n.values.length,g="",f=c=0,p=this._colKeysCalcValues.length==1&&this._colKeysCalcValues[0].cellType=="RGTot"?1:this._isPaging?this._colHdrLen:n.columns.length;for(i=0;i<this._colKeysCalcValues.length;i++){for(f=0,s=0,l=0,d=0,u=a,h=0,this._isPaging&&n.values.length>0&&(h=(n.pagerOptions.categoricalCurrentPage-1)*n.pagerOptions.categoricalPageSize%n.values.length,i==0?(u=(n.pagerOptions.categoricalCurrentPage==1&&n.values.length>n.pagerOptions.categoricalPageSize?n.pagerOptions.categoricalPageSize:n.values.length)-(n.pagerOptions.categoricalCurrentPage-1)*n.pagerOptions.categoricalPageSize%n.values.length,u=u<1?n.values.length:n.values.length>n.pagerOptions.categoricalPageSize&&u>n.pagerOptions.categoricalPageSize?n.pagerOptions.categoricalPageSize:u):this._colKeysCalcValues.length-i==1?(u=n.pagerOptions.categoricalCurrentPage*n.pagerOptions.categoricalPageSize%n.values.length,u=u<1||n.pagerOptions.categoricalPageSize>=n.values.length*this._colKeysCalcValues.length-(n.pagerOptions.categoricalCurrentPage-1)*n.pagerOptions.categoricalPageSize%n.values.length?n.values.length:u):u=n.values.length),g=this._colKeysCalcValues[i].keys,this._gridMatrix[f]==undefined&&(this._gridMatrix[f]=[]),c=this._colKeysCalcValues[i].keys.length,e=0;e<c;e++){for(this._gridMatrix[f]==undefined&&(this._gridMatrix[f]=[]),this._colKeysCalcValues[i].cellType!=undefined&&ej.isNullOrUndefined(this._colKeysCalcValues[i].span)?this._colKeysCalcValues[i].cellType.indexOf("SubTot")>-1?(s=f==0?this._colKeysCalcValues[i].level+f:f,l=t-this._colKeysCalcValues[i].level-1,this._cellType="summary cstot"):(s=f,l=this._colKeysCalcValues.length==1&&this._colKeysCalcValues[0].cellType=="RGTot"?1:this._isPaging?this._colHdrLen:n.columns.length,this._cellType="summary cgtot"):(s=f,l=1,this._cellType="colheader"),b=c>1?c-e>1?1:ej.isNullOrUndefined(this._colKeysCalcValues[i].expander)?0:2:ej.isNullOrUndefined(this._colKeysCalcValues[i].expander)?0:2,r=0;r<u;r++)k=$.extend(!0,[],this._colKeysCalcValues[i].keys),this._gridMatrix[s][a*i+r+v-(i>0?h:0)]={Index:w+","+s,CSS:this._cellType,Value:this._colKeysCalcValues[i].keys[e],State:b,ColSpan:r>0?1:this._isPaging&&i==0&&!ej.isNullOrUndefined(this._colKeysCalcValues[i].span)&&this._colKeysCalcValues[i].span[e]>=this._colKeysCalcValues.length?n.pagerOptions.categoricalPageSize:(this._colKeysCalcValues[i].span!=undefined?this._colKeysCalcValues[i].span[e]==undefined?1:this._colKeysCalcValues[i].span[e]:1)*(this._colKeysCalcValues.length-i==1?u:a)-(i==0?h:0),RowSpan:l,Info:k.splice(0,e+1).join(">#>"),Span:"None",Expander:r>0?ej.isNullOrUndefined(this._colKeysCalcValues[i].expander)?0:1:this._colKeysCalcValues[i].span!=undefined?this._colKeysCalcValues[i].span[e]!=undefined?1:ej.isNullOrUndefined(this._colKeysCalcValues[i].expander)?0:1:ej.isNullOrUndefined(this._colKeysCalcValues[i].expander)?0:1};this._colKeysCalcValues[i].level-e==1&&delete this._colKeysCalcValues[i].span;f++}if(n.values.length>0)for(r=0;r<u;r++)this._gridMatrix[p]==undefined&&(this._gridMatrix[p]=[]),y=this._isPaging&&n.values.length>0&&i==0?(n.pagerOptions.categoricalCurrentPage-1)*n.pagerOptions.categoricalPageSize%n.values.length+r:r,this._gridMatrix[p][a*i+r+v-(i>0?h:0)]={Index:a*i+r+v-(i>0?h:0)+","+f,CSS:this._cellType+" calc",Value:n.values[y].fieldCaption==undefined?n.values[y].fieldName:n.values[y].fieldCaption,State:0,ColSpan:1,RowSpan:1,Info:"",Span:"None",Expander:0};w++}if(this._cellType="",this._isPaging&&n.values.length>0&&this._colKeysCalcValues.length>0&&t>1)for(r=0;r<t;r++)for(o=0;o<this._gridMatrix[r].length;o++)!ej.isNullOrUndefined(this._gridMatrix[r][o])&&this._gridMatrix[r][o].ColSpan>this._gridMatrix[r].length-o&&(this._gridMatrix[r][o].ColSpan=this._gridMatrix[r].length-o)}},_populateCalcTable:function(n){var i=[],l=null,h=n.values.length,f=this,nt,wt,r,lt,b,bt,it,o,v,ft,t,p,vt,et,yt,pt,d,ot,u,tt,a,s;this._rowTotCalc=[];this._rowTotCalc=[];var e=n.rows.length==0?1:this._isPaging?this._rowHdrLen:n.rows.length,st=0,rt=!1,g=0,c=this._colKeysCalcValues.length==1&&this._colKeysCalcValues[0].cellType=="RGTot"?2:(this._isPaging?this._colHdrLen:n.columns.length)+1,ht=c,w,ut=!1,ct={};for($.grep(f._tableKeysCalcValues,function(n){ct[n.uniqueName]=n;return}),nt=$.grep(this._tableKeysCalcValues,function(n){if(ej.isNullOrUndefined(n.cellType))return n}),wt=$.grep(this._tableKeysCalcValues,function(n){if(!ej.isNullOrUndefined(n.cellType))return n}),u=0;u<this._rowKeysCalcValues.length;u++){for(this._colTotCalc=[],w=e,rt=!0,this._gridMatrix[u+c]==undefined&&(this._gridMatrix[u+c]=[]),this._rowTotCalc[g]=[],r=0;r<this._colKeysCalcValues.length;r++){if(i=[],l=null,this._cellType="value",tt=this._isPaging&&n.values.length>0&&r==0?(n.pagerOptions.categoricalCurrentPage-1)*n.pagerOptions.categoricalPageSize%n.values.length:0,a=this._isPaging&&n.values.length>0&&this._colKeysCalcValues.length-r==1&&r>0?n.pagerOptions.categoricalCurrentPage*n.pagerOptions.categoricalPageSize%n.values.length:h,a=a<1||this._isPaging&&n.pagerOptions.categoricalPageSize>=n.values.length*this._colKeysCalcValues.length-(n.pagerOptions.categoricalCurrentPage-1)*n.pagerOptions.categoricalPageSize%n.values.length?h:a,n.columns.length>0&&n.rows.length>0){if(f._isPaging&&(f._rowKeysCalcValues[u].cellType=="RGTot"||f._colKeysCalcValues[r].cellType=="RGTot"))o=0,$.grep(nt,function(e){if(f._rowKeysCalcValues[u].cellType=="RGTot"&&f._colKeysCalcValues[r].cellType=="RGTot"?!0:f._rowKeysCalcValues[u].cellType=="RGTot"?e.uniqueName.indexOf(f._colKeysCalcValues[r].uniqueName)>-1:e.uniqueName.indexOf(f._rowKeysCalcValues[u].uniqueName)>-1)for(o++,t=0;t<e.value.keys.length;t++)ej.isNullOrUndefined(i[t])?i.push(e.value.keys[t]):i[t]=n.values[t].isCalculatedField?ej.PivotAnalysis._calculatedFieldSummaryValue(t,i,n):ej.PivotAnalysis._getSummaryValue(i[t],e.value.keys[t],o,ej.PivotAnalysis._summaryTypes[t]=="count"?"sum":ej.PivotAnalysis._summaryTypes[t],n.values[t].format,n.values[t].formatString)});else if(lt=f._rowKeysCalcValues[u].uniqueName+">#>"+f._colKeysCalcValues[r].uniqueName,b=ct[lt],b){for(t=0;t<b.value.keys.length;t++)i.push(b.value.keys[t]);b.value.uniqueName!=""&&(l=b.value.uniqueName)}}else n.rows.length==0?f._isPaging?(o=0,$.grep(nt,function(u){if(f._colKeysCalcValues[r].cellType=="RGTot"?!0:u.uniqueName.indexOf(f._colKeysCalcValues[r].uniqueName)>-1)for(o++,t=0;t<u.value.keys.length;t++)ej.isNullOrUndefined(i[t])?i.push(u.value.keys[t]):i[t]=n.values[t].isCalculatedField?ej.PivotAnalysis._calculatedFieldSummaryValue(t,i,n):ej.PivotAnalysis._getSummaryValue(i[t],u.value.keys[t],o,ej.PivotAnalysis._summaryTypes[t]=="count"?"sum":ej.PivotAnalysis._summaryTypes[t],n.values[t].format,n.values[t].formatString)})):$.grep(f._tableKeysCalcValues,function(n){if(n.uniqueName==f._colKeysCalcValues[r].uniqueName){for(t=0;t<n.value.keys.length;t++)i.push(n.value.keys[t]);n.value.uniqueName!=""&&(l=n.value.uniqueName)}return}):f._isPaging?(o=0,$.grep(nt,function(r){if(f._rowKeysCalcValues[u].cellType=="RGTot"?!0:r.uniqueName.indexOf(f._rowKeysCalcValues[u].uniqueName)>-1)for(o++,t=0;t<r.value.keys.length;t++)ej.isNullOrUndefined(i[t])?i.push(r.value.keys[t]):i[t]=n.values[t].isCalculatedField?ej.PivotAnalysis._calculatedFieldSummaryValue(t,i,n):ej.PivotAnalysis._getSummaryValue(i[t],r.value.keys[t],o,ej.PivotAnalysis._summaryTypes[t]=="count"?"sum":ej.PivotAnalysis._summaryTypes[t],n.values[t].format,n.values[t].formatString)})):$.grep(f._tableKeysCalcValues,function(n){if(n.uniqueName==f._rowKeysCalcValues[u].uniqueName){for(t=0;t<n.value.keys.length;t++)i.push(n.value.keys[t]);n.value.uniqueName!=""&&(l=n.value.uniqueName)}return});if(this._editCellsInfo.length>0)for(s=0;s<n.values.length;s++)i[s]=ej.PivotAnalysis._cellEdit(n.columns.length>0&&n.rows.length>0?this._rowKeysCalcValues[u].uniqueName+">#>"+this._colKeysCalcValues[r].uniqueName:n.rows.length==0?this._colKeysCalcValues[r].uniqueName:this._rowKeysCalcValues[u].uniqueName,i[s],s,n.values[s]);if(this._colKeysCalcValues[r].cellType!=undefined&&(this._colKeysCalcValues[r].cellType.indexOf("SubTot")>-1||this._colKeysCalcValues[r].cellType.indexOf("RGTot")>-1)){if(!this._isPaging){for(i=[],l=null,t=0;t<h;t++)for(p=0,o=0,v=w;v<=r*h+e;v++)this._gridMatrix[u+c]!=undefined&&this._gridMatrix[u+c][w+p*h+t]!=undefined&&this._gridMatrix[u+c][w+p*h+t].Value!=undefined&&(i[t]==undefined&&(i[t]=0),o++,i[t]=n.values[t].isCalculatedField?this._calculatedFieldSummaryValue(t,i,n):this._getSummaryValue(i[t],this._gridMatrix[u+c][w+p*h+t].Value,o,this._summaryTypes[t]=="count"?"sum":this._summaryTypes[t],n.values[t].format,n.values[t].formatString)),p++;st=0;w=(r+1)*h+e;ej.isNullOrUndefined(i)||this._colTotCalc.push({uniqueName:this._colKeysCalcValues[r].uniqueName,level:this._colKeysCalcValues[r].level,value:i});this._colKeysCalcValues[r-1]!=undefined&&this._colKeysCalcValues[r-1].cellType!=undefined&&this._colKeysCalcValues[r-1].cellType.indexOf("SubTot")>-1&&this._colKeysCalcValues[r-1].cellType.indexOf("RGTot")==-1&&(it=$(this._colTotCalc).filter(function(n,t){return t!=undefined&&t.uniqueName!=undefined&&f._colKeysCalcValues[r].uniqueName!=undefined&&t.level!=undefined&&f._colKeysCalcValues[r].level!=undefined&&t.uniqueName.indexOf(f._colKeysCalcValues[r].uniqueName)===0&&f._colKeysCalcValues[r].level+1==t.level}).map(function(t,r){for(var u=0;u<r.value.length;u++)i[u]=ej.isNullOrUndefined(i[u])?0:i[u],r.value[u]=ej.isNullOrUndefined(r.value[u])?0:r.value[u],n.values[u].isCalculatedField?i[u]=ej.PivotAnalysis._calculatedFieldSummaryValue(u,i,n):i[u]+=r.value[u];return i}),ej.isNullOrUndefined(i)||this._colTotCalc.push({uniqueName:this._colKeysCalcValues[r].uniqueName,level:this._colKeysCalcValues[r].level,value:i}))}this._cellType="summary value"}if(this._colKeysCalcValues[r].cellType!=undefined&&this._colKeysCalcValues[r].cellType.indexOf("RGTot")>-1&&i==0&&(this._isPaging||(i=[],l=null,o=0,it=$(this._colTotCalc).filter(function(n,t){return t!=undefined&&t!=undefined&&t.level!=undefined&&t.level==0}).map(function(t,r){for(var u=0;u<r.value.length;u++)i[u]=ej.isNullOrUndefined(i[u])?0:i[u],r.value[u]=ej.isNullOrUndefined(r.value[u])?0:r.value[u],o++,i[u]=n.values[u].isCalculatedField?ej.PivotAnalysis._calculatedFieldSummaryValue(u,i,n):ej.PivotAnalysis._getSummaryValue(i[u],r.value[u],o,f._summaryTypes[u]=="count"?"sum":f._summaryTypes[u],n.values[u].format,n.values[u].formatString);return i})),this._cellType="summary value"),this._rowKeysCalcValues[u].cellType!=undefined&&(this._rowKeysCalcValues[u].cellType.indexOf("SubTot")>-1||this._rowKeysCalcValues[u].cellType.indexOf("RGTot")>-1)){if(!this._isPaging){for(i=[],l=null,bt=this._rowKeysCalcValues[u].uniqueName,this._rowKeysCalcValues[u-1]!=undefined&&this._rowKeysCalcValues[u-1].cellType!=undefined&&this._rowKeysCalcValues[u-1].cellType.indexOf("SubTot")>-1&&this._rowKeysCalcValues[u].cellType.indexOf("RGTot")==-1&&(o=0,it=$(this._rowTotCalc).filter(function(n,t){return t[0]!=undefined&&t[0].uniqueName!=undefined&&f._rowKeysCalcValues[u].uniqueName!=undefined&&t[0].level!=undefined&&f._rowKeysCalcValues[u].level!=undefined&&t[0].uniqueName.indexOf(f._rowKeysCalcValues[u].uniqueName)===0&&f._rowKeysCalcValues[u].level+1==t[0].level}).map(function(t,u){if(u[e+r].length>0){o++;for(var s=0;s<u[e+r].length;s++)i[s]=ej.isNullOrUndefined(i[s])?0:i[s],u[e+r][s]=ej.isNullOrUndefined(u[e+r][s])?0:u[e+r][s],i[s]=n.values[s].isCalculatedField?ej.PivotAnalysis._calculatedFieldSummaryValue(s,i,n):ej.PivotAnalysis._getSummaryValue(i[s],u[e+r][s],o,f._summaryTypes[s]=="count"?"sum":f._summaryTypes[s],n.values[s].format)}return i})),this._rowKeysCalcValues[u].cellType!=undefined&&this._rowKeysCalcValues[u].cellType.indexOf("RGTot")>-1&&(o=0,it=$(this._rowTotCalc).filter(function(n,t){return t!=undefined&&t[0]!=undefined&&t[0].level!=undefined&&t[0].level==0}).map(function(t,u){if(u[e+r].length>0){o++;for(var s=0;s<u[e+r].length;s++)i[s]=ej.isNullOrUndefined(i[s])?0:i[s],u[e+r][s]=ej.isNullOrUndefined(u[e+r][s])?0:u[e+r][s],i[s]=n.values[s].isCalculatedField?ej.PivotAnalysis._calculatedFieldSummaryValue(s,i,n):ej.PivotAnalysis._getSummaryValue(i[s],u[e+r][s],o,f._summaryTypes[s]=="count"?"sum":f._summaryTypes[s],n.values[s].format,n.values[s].formatString)}return i})),o=1,v=ht;v<=u+c;v++){for(ft=!1,t=0;t<h;t++)p=0,this._gridMatrix[v]!=undefined&&this._gridMatrix[v][e+r*h+t]!=undefined&&this._gridMatrix[v][e+r*h+t].Value!=undefined&&(i[t]==undefined&&(i[t]=0),ft=!0,i[t]=n.values[t].isCalculatedField?this._calculatedFieldSummaryValue(t,i,n):this._getSummaryValue(i[t],this._gridMatrix[v][e+r*h+t].Value,o,this._summaryTypes[t]=="count"?"sum":this._summaryTypes[t],n.values[t].format,n.values[t].formatString)),p++;ft&&o++}st=0;ut=!0;rt&&(this._rowTotCalc[g][0]={uniqueName:this._rowKeysCalcValues[u].uniqueName,level:this._rowKeysCalcValues[u].level},rt=!1);this._rowTotCalc[g][e+r]=i}this._cellType="summary value"}if(n.enableAdvancedFilter){var k=0,at=this,y=this._currentFilterVal.fieldName?ej.Pivot.getReportItemByFieldName(this._currentFilterVal.fieldName,n):null;y!=null&&y.axis=="rows"&&(k=this._getFilteredIndex(n.rows,n.values,u,at._rowKeysCalcValues));ej.isNullOrUndefined(k)||y==null||y.axis=="rows"||(k=this._getFilteredIndex(n.columns,n.values,r,at._colKeysCalcValues));ej.isNullOrUndefined(k)||(vt=this._rowKeysCalcValues[u].keys.length>0&&n.rows.length>0&&n.rows[this._rowKeysCalcValues[u].keys.length-1].advancedFilter&&n.rows[this._rowKeysCalcValues[u].keys.length-1].advancedFilter.length>0&&n.rows[this._rowKeysCalcValues[u].keys.length-1].advancedFilter[0].advancedFilterType==ej.Pivot.AdvancedFilterType.ValueFilter?1:null,et=y&&y!=null&&vt!=null&&n.rows[this._rowKeysCalcValues[u].keys.length-1].fieldName==y.item.fieldName?this._applyValueFilter(n.rows[this._rowKeysCalcValues[u].keys.length-1].advancedFilter,i[k]):!1,this._colKeysCalcValues[r].keys.indexOf("Grand Total")>=0&&et&&this._rowKeysCalcValues[u].uniqueName?this._advancedFilterInfo.push(this._rowKeysCalcValues[u].uniqueName):n.columns.length==0&&et&&this._rowKeysCalcValues[u].uniqueName&&this._advancedFilterInfo.push(this._rowKeysCalcValues[u].uniqueName),yt=this._colKeysCalcValues[r].keys.length>0&&n.columns.length>0&&n.columns[this._colKeysCalcValues[r].keys.length-1].advancedFilter&&n.columns[this._colKeysCalcValues[r].keys.length-1].advancedFilter.length>0&&n.columns[this._colKeysCalcValues[r].keys.length-1].advancedFilter[0].advancedFilterType==ej.Pivot.AdvancedFilterType.ValueFilter?1:null,pt=y!=null&&yt!=null&&n.columns[this._colKeysCalcValues[r].keys.length-1].fieldName==y.item.fieldName?this._applyValueFilter(n.columns[this._colKeysCalcValues[r].keys.length-1].advancedFilter,i[k]):!1,this._colKeysCalcValues[r].uniqueName&&jQuery.type(this._rowKeysCalcValues[u].keys[0])==="string"&&this._rowKeysCalcValues[u].keys[0].indexOf("Grand Total")>-1&&pt&&this._advancedFilterInfo.push(this._colKeysCalcValues[r].uniqueName))}for(d=this._isPaging&&n.values.length>0?(n.pagerOptions.categoricalCurrentPage-1)*n.pagerOptions.categoricalPageSize%n.values.length:0,s=tt;s<a;s++)i!=undefined&&i.length!=undefined?(ot=null,l!=null&&l[s]!=""&&l[s]!=" "&&(ot=l[s]),this._gridMatrix[c+u][h*r+s+e-d]={Index:h*r+s+e-d+","+c+u,CSS:this._cellType,Value:i[s],valueText:ot,State:0,ColSpan:1,RowSpan:1,Info:"",Span:"None",Expander:0}):this._gridMatrix[c+u][h*r+s+e-d]=0}ut&&(g++,ht=u+c+1,ut=!1)}for(u=0;u<this._rowKeysCalcValues.length;u++)for(r=0;r<this._colKeysCalcValues.length;r++)for(tt=this._isPaging&&n.values.length>0&&r==0?(n.pagerOptions.categoricalCurrentPage-1)*n.pagerOptions.categoricalPageSize%n.values.length:0,a=this._isPaging&&n.values.length>0&&this._colKeysCalcValues.length-r==1&&r>0?n.pagerOptions.categoricalCurrentPage*n.pagerOptions.categoricalPageSize%n.values.length:h,a=a<1||this._isPaging&&n.pagerOptions.categoricalPageSize>=n.values.length*this._colKeysCalcValues.length-(n.pagerOptions.categoricalCurrentPage-1)*n.pagerOptions.categoricalPageSize%n.values.length?h:a,s=tt;s<a;s++)n.values[s].isCalculatedField&&this._gridMatrix[c+u][h*r+s+e-d].Value==null&&(this._gridMatrix[c+u][h*r+s+e-d].Value=this._calculatedFieldSummaryValue(s,[],n));return this._gridMatrix}};ej.PivotAnalysis.SortOrder={Ascending:"ascending",Descending:"descending",None:"none"};ej.PivotAnalysis.FilterType={Exclude:"exclude",Include:"include"};ej.PivotAnalysis.SummaryType={Sum:"sum",Average:"average",Count:"count",Min:"min",Max:"max"}})(jQuery,Syncfusion)});
