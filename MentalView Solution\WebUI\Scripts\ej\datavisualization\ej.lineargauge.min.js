/*!
*  filename: ej.lineargauge.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function(n,t,i){var u,r,f;t.widget({ejLinearGauge:"ej.datavisualization.LinearGauge"},{element:null,_rootCSS:"e-lineargauge",_animationFlag:!0,model:null,_customLblMaxSize:0,_savedPoints:[],validTags:["div","span"],defaults:{exportSettings:{mode:"client",type:"png",fileName:"CircularGauge",action:""},locale:null,enableGroupSeparator:!1,value:0,minimum:0,maximum:100,width:150,height:400,theme:"flatlight",orientation:"Vertical",pointerGradient1:null,pointerGradient2:null,backgroundColor:null,borderColor:null,labelColor:null,tickColor:null,readOnly:!0,enableResize:!1,isResponsive:!1,tooltip:{showLabelTooltip:!1,showCustomLabelTooltip:!1,templateID:null},outerCustomLabelPosition:"bottom",frame:{backgroundImageUrl:null,outerWidth:12,innerWidth:8},scales:null,enableAnimation:!0,enableMarkerPointerAnimation:!0,animationSpeed:500,drawTicks:null,drawLabels:null,drawBarPointers:null,drawMarkerPointers:null,drawRange:null,drawCustomLabel:null,drawIndicators:null,load:null,doubleClick:"",rightClick:"",init:null,renderComplete:null,mouseClick:null,mouseClickMove:null,mouseClickUp:null,themeProperties:{flatlight:{scales:{backgroundColor:"#FFFFFF",border:{color:"#1d1e1e"},barPointers:{backgroundColor:"#8abc3b",border:{color:"#8abc3b"}},markerPointers:{backgroundColor:"#212121",border:{color:"#212121"}},ticks:{color:"#1d1e1e"},labels:{labelColor:"#222222"}}},flatdark:{scales:{backgroundColor:"#808080",border:{color:"#808080"},barPointers:{backgroundColor:"#8abc3b",border:{color:"#8abc3b"}},markerPointers:{backgroundColor:"#CCCCCC",border:{color:"#CCCCCC"}},ticks:{color:"#808080"},labels:{labelColor:"#CCCCCC"}}}}},_defaultScaleValues:function(){return{minimum:null,maximum:null,majorIntervalValue:10,minorIntervalValue:2,direction:"counterclockwise",backgroundColor:null,border:{color:null,width:1.5},opacity:NaN,width:30,shadowOffset:0,length:290,type:"rectangle",position:{x:50,y:50},showRanges:!1,showIndicators:!1,showCustomLabels:!1,showLabels:!0,showTicks:!0,showBarPointers:!0,showMarkerPointers:!0,ticks:[{distanceFromScale:{x:0,y:0},angle:0,color:null,type:"majorinterval",placement:"near",opacity:0,height:10,width:3},{distanceFromScale:{x:0,y:0},angle:0,color:null,type:"minorinterval",placement:"near",opacity:0,height:5,width:2}],ranges:[{endWidth:10,placement:"center",startWidth:10,distanceFromScale:0,endValue:60,startValue:20,gradients:null,backgroundColor:null,border:{color:null,width:1.5},opacity:null}],labels:[{distanceFromScale:{x:-10,y:0},angle:0,font:{size:"11px",fontFamily:"Arial",fontStyle:"bold"},textColor:null,opacity:0,type:"major",placement:"near",includeFirstValue:!0,unitText:"",unitTextPlacement:"back"}],markerPointers:[{type:"triangle",length:30,placement:"far",gradients:null,distanceFromScale:0,width:30,value:null,backgroundColor:null,border:{color:null,width:1.5},opacity:1}],barPointers:[{gradients:null,distanceFromScale:0,width:30,value:null,backgroundColor:null,border:{color:null,width:1.5},opacity:1}],indicators:[{font:{size:"11px",fontFamily:"Arial",fontStyle:"bold"},height:30,type:"rectangle",width:30,position:{x:0,y:0},textLocation:{x:0,y:0},stateRanges:[{endValue:60,startValue:50,backgroundColor:null,borderColor:null,text:"",textColor:null}],backgroundColor:null,border:{color:null,width:1.5},opacity:NaN}],customLabels:[{font:{size:"11px",fontFamily:"Arial",fontStyle:"bold"},color:null,opacity:0,value:"",textAngle:0,position:{x:0,y:0},positionType:"inner"}]}},dataTypes:{scales:"data",isResponsive:"boolean"},observables:["value","minimum","maximum"],_tags:[{tag:"scales",attr:["majorIntervalValue","minorIntervalValue","backgroundColor","shadowOffset","showRanges","showIndicators","showCustomLabels","showLabels","showTicks","showBarPointers","showMarkerPointers","border.color","border.width","position.x","position.y",[{tag:"markerPointers",attr:["distanceFromScale","backgroundColor","border.width","border.color"]},{tag:"barPointers",attr:["distanceFromScale","backgroundColor","border.width","border.color"]},{tag:"ranges",attr:["distanceFromScale","startValue","endValue","startWidth","endWidth","backgroundColor","border.color","border.width"]},{tag:"ticks",attr:["distanceFromScale.x","distanceFromScale.y"]},{tag:"indicators",attr:["backgroundColor","textLocation","font.size","font.fontFamily","font.fontStyle","position.x","position.y","textLocation.x","textLocation.y","borderColor","textColor",[{tag:"stateRanges",attr:["endValue","startValue","backgroundColor","borderColor","textColor"]}]]},{tag:"labels",attr:["distanceFromScale.x","distanceFromScale.y","textColor","includeFirstValue","unitText","unitTextPlacement","font.size","font.fontFamily","font.fontStyle"]},{tag:"customLabels",attr:["textAngle","font.size","font.fontFamily","font.fontStyle","position.x","position.y"]}]]}],value:t.util.valueFunction("value"),minimum:t.util.valueFunction("minimum"),maximum:t.util.valueFunction("maximum"),_init:function(){r=n(".e-lineargauge").length;f=r;this._initialize();this._trigger("load");this._setTheme();this._render();this.wireEvents();this._onWindowResize()},_onWindowResize:function(){(this.model.enableResize||this.model.isResponsive)&&(t.isTouchDevice()?this._on(n(window),"orientationchange",this.resizeCanvas):this._on(n(window),"resize",this.resizeCanvas))},_setModel:function(t){var i,r,f,u,e,o,s;for(i in t)switch(i){case"theme":this.model.theme=t[i];this._init();break;case"height":this.model.height=t[i];break;case"width":this.model.width=t[i];break;case"orientation":this.model.orientation=t[i];break;case"pointerGradient1":this.model.pointerGradient1=t[i];break;case"pointerGradient2":this.model.pointerGradient2=t[i];break;case"labelColor":this.model.labelColor=t[i];break;case"tick":n.extend(this.model.tick,t[i]);break;case"backgroundColor":this.model.backgroundColor=t[i];break;case"borderColor":this.model.borderColor=t[i];break;case"frame":n.extend(this.model.frame,t[i]);case"outerCustomLabelPosition":this.model.outerCustomLabelPosition=t[i];break;case"tooltip":n.extend(this.model.tooltip,t[i]);break;case"readOnly":this.model.readOnly=t[i];break;case"value":for(this.value()==""&&this.value(0),r=0;this.model.scales[r]!=null;r++)for(f=0;this.model.scales[r].markerPointers[f]!=null;f++)this.model.scales[r].markerPointers[f].value=parseFloat(this.value());for(u=0;this.model.scales[u]!=null;u++)for(e=0;this.model.scales[u].barPointers[e]!=null;e++)this.model.scales[u].barPointers[e].value=parseFloat(this.value());break;case"minimum":for(this.minimum()==""&&this.minimum(0),o=0;this.model.scales[o]!=null;o++)this.model.scales[o].minimum=parseInt(this.minimum());break;case"maximum":for(this.maximum()==""&&this.maximum(0),s=0;this.model.scales[s]!=null;s++)this.model.scales[s].maximum=parseInt(this.maximum());break;case"scales":this.model.scales=t[i];this._itemInitialize()}this._render();this.wireEvents()},_destroy:function(){this.activeElement=null;this.canvasEl=null;this.contextEl=null;this.unWireEvents();this.element.empty().removeClass("e-lineargauge e-js e-widget")},_scales:function(){this._itemInitialize();this._render()},_scales_markerPointers:function(){this.refresh();this._trigger("refresh")},_scales_barPointers:function(){this.refresh();this._trigger("refresh")},_scales_ranges:function(){this.refresh();this._trigger("refresh")},_scales_ticks:function(){this.refresh();this._trigger("refresh")},_scales_indicators:function(){this.refresh();this._trigger("refresh")},_scales_indicators_stateRanges:function(){this.refresh();this._trigger("refresh")},_scales_labels:function(){this.refresh();this._trigger("refresh")},_scales_customLabels:function(){this.refresh();this._trigger("refresh")},_initialize:function(){this.GaugeEl=this.element;this.scaleStartX=[];this.scaleStartY=[];this.isScaleModified=!1;this.target=this.element[0];this._itemInitialize();this.Model=this.model},_render:function(){this.initialize();this.wireEvents()},_itemInitialize:function(){var t=this;this.model.scales!=null?n.each(this.model.scales,function(i,r){r=t._checkArrayObject(r,i);var u=t._defaultScaleValues();n.extend(u,r);n.extend(r,u)}):this.model.scales=[this._defaultScaleValues()]},_checkArrayObject:function(t,i){var r=this,u;return n.each(t,function(n,t){if(u=typeof n,(u!="string"||u=="string"&&n.indexOf("_")==-1&&n.indexOf("__")==-1)&&typeof t!="function")if(t instanceof Array)r._checkArrayObject(t,n);else if(t!=null&&typeof t=="object"&&!t.setter&&!t.factory&&!t.key){var f=r._defaultScaleValues();r._LoadIndividualDefaultValues(t,f,typeof n=="number"?i:n)}}),t},_LoadIndividualDefaultValues:function(t,i,r){var u=null,e=this,f;return n.each(i,function(n,t){if(r==n){u=t;return}}),u instanceof Array&&(u=u[0]),f=typeof r,n.each(t,function(n,t){t instanceof Array?e._checkArrayObject(t,r):t!=null&&typeof t=="object"&&(f!="string"||f=="string"&&r.indexOf("_")==-1&&r.indexOf("__")==-1)&&e._LoadIndividualDefaultValues(t,u,typeof n=="number"?r:n)}),n.extend(u,t),n.extend(t,u),t},initialize:function(){this._initObject(this);this.Model.frame.backgroundImageUrl?this._drawCustomImage(this,this.Model.frame.backgroundImageUrl):this.Model.scales!=null&&this._drawScales()},_initObject:function(i){var a,e,s,h,o,l,c;for(this._savedPoints=[],this.element.addClass("e-widget"),i.GaugeEl=i.element,i.canvasEl&&(i.canvasEl.parent().empty(),i.GaugeEl.empty()),i.canvasEl=n("<canvas><\/canvas>"),a=0,e=0;this.model.scales[e]!=null;e++){for(this.model.scales[e].minimum==null&&(this.model.scales[e].minimum=this.minimum()),this.model.scales[e].maximum==null&&(this.model.scales[e].maximum=this.maximum()),s=0;this.model.scales[e].markerPointers[s]!=null;s++)this.model.scales[e].markerPointers[s].value==null&&(this.model.scales[e].markerPointers[s].value=this.value());for(h=0;this.model.scales[e].barPointers[h]!=null;h++)this.model.scales[e].barPointers[h].value==null&&(this.model.scales[e].barPointers[h].value=this.value());for(o=0;this.model.scales[e].customLabels[o]!=null&&this.model.scales[e].showCustomLabels==!0;o++)if(a++,this.model.scales[e].customLabels[o].value!=null&&i.GaugeEl.find("div").length==0)if(this.model.scales[e].customLabels[o]!=null&&this.model.scales[e].customLabels[o].positionType!=null&&this.model.scales[e].customLabels[o].positionType=="outer")if(i.outerDiv=t.buildTag("div"),i.model.outerCustomLabelPosition=="bottom")i.GaugeEl.append(i.canvasEl),i.GaugeEl.append(i.outerDiv),i.outerDiv.css("text-align","center"),i.GaugeEl.css({width:i.model.width});else if(i.model.outerCustomLabelPosition!="top"){l=t.buildTag("TABLE");l.css("width","100%");var v=t.buildTag("TR"),y=t.buildTag("TD"),p=t.buildTag("td");i.model.outerCustomLabelPosition=="left"?(y.append(i.outerDiv),p.append(i.canvasEl)):(y.append(i.canvasEl),p.append(i.outerDiv));v.append(y);v.append(p);l.append(v);i.GaugeEl.append(l);i.outerDiv.css({width:this.element.width()-i.model.width})}else i.GaugeEl.append(i.outerDiv),i.GaugeEl.append(i.canvasEl),i.GaugeEl.css({width:i.model.width}),i.outerDiv.css("text-align","center");else i.GaugeEl.append(i.canvasEl);a==0&&i.GaugeEl.append(i.canvasEl)}(i.canvasEl.attr("role","presentation"),r==f&&(u=window.innerWidth),i.canvasEl[0].setAttribute("width",i.model.width),i.canvasEl[0].setAttribute("height",i.model.height),i.centerX=i.canvasEl[0].width/2,i.centerY=i.canvasEl[0].height/2,c=i.canvasEl[0],typeof G_vmlCanvasManager!="undefined"&&(c=window.G_vmlCanvasManager.initElement(c)),c&&c.getContext)&&(i.contextEl=i.canvasEl[0].getContext("2d"))},_drawFrameCircle:function(n,t,i){this._contextOpenPath(t,i);i.contextEl.arc(n.startX,n.startY,t.circleRadius,0,2*Math.PI,!0);this._contextClosePath(t,i);t.indicatorText&&i._drawText(n,t)},_drawFrameRectangle:function(n,t,i){this._contextOpenPath(t,i);i.contextEl.lineTo(n.startX+t.radius,n.startY);i.contextEl.lineTo(n.startX+t.width-t.radius,n.startY);i.contextEl.lineTo(n.startX+t.width,n.startY+t.height-t.radius);i.contextEl.lineTo(n.startX+t.radius,n.startY+t.height);this._contextClosePath(t,i);t.indicatorText&&i._drawText(n,t)},_drawFrameThermometer:function(n,t,i){var r=i.Model.orientation=="Vertical"?Math.sqrt(t.width*t.width+t.width*t.width)/2:Math.sqrt(t.height*t.height+t.height*t.height)/2;this._contextOpenPath(t,i);i.Model.orientation=="Vertical"?i.scaleEl[i.scaleIndex].direction=="counterclockwise"?(i.contextEl.arc(n.startX+Math.cos(Math.PI*(45/180))*r,n.startY+t.height-Math.sin(Math.PI*(45/180))*r,r,Math.PI*(-45/180),Math.PI*(225/180),!1),i.contextEl.lineTo(n.startX,n.startY+t.calDistance+t.width/2),t.topRounded?i.contextEl.arc(n.startX+t.width/2,n.startY+t.width/2,t.width/2,-Math.PI,0,!1):i.contextEl.lineTo(n.startX+t.width,n.startY+t.calDistance+t.width/2)):(i.contextEl.arc(n.startX+Math.cos(Math.PI*(45/180))*r,n.startY+Math.sin(Math.PI*(45/180))*r,r,Math.PI*(45/180),Math.PI*(-225/180),!0),i.contextEl.lineTo(n.startX,n.startY+t.height-t.width/2),t.topRounded?i.contextEl.arc(n.startX+t.width/2,n.startY+t.height-t.width/2,t.width/2,-Math.PI,0,!0):i.contextEl.lineTo(n.startX+t.width,n.startY+t.height-t.width/2)):i.scaleEl[i.scaleIndex].direction=="counterclockwise"?(i.contextEl.arc(n.startX+t.width-r/4-Math.cos(Math.PI*(45/180))*r,n.startY+Math.sin(Math.PI*(45/180))*r,r,Math.PI*(135/180),Math.PI*(225/180),!0),i.contextEl.lineTo(n.startX+t.height/2,n.startY),t.topRounded?i.contextEl.arc(n.startX+t.height/2,n.startY+t.height/2,t.height/2,Math.PI*(270/180),Math.PI*(90/180),!0):i.contextEl.lineTo(n.startX+t.height/2,n.startY+t.height)):(i.contextEl.arc(n.startX+r/4+Math.cos(Math.PI*(45/180))*r,n.startY+Math.sin(Math.PI*(45/180))*r,r,Math.PI*(45/180),Math.PI*(315/180),!1),i.contextEl.lineTo(n.startX+t.width-t.height/2,n.startY),t.topRounded?i.contextEl.arc(n.startX+t.width-t.height/2,n.startY+t.height/2,t.height/2,Math.PI*(270/180),Math.PI*(90/180),!1):i.contextEl.lineTo(n.startX+t.width-t.height/2,n.startY+t.height));this._contextClosePath(t,i)},_drawFrameRoundedRectangle:function(n,t,i){this._contextOpenPath(t,i);i.contextEl.lineTo(n.startX+t.radius,n.startY);i.contextEl.lineTo(n.startX+t.width-t.radius,n.startY);i.contextEl.quadraticCurveTo(n.startX+t.width,n.startY,n.startX+t.width,n.startY+t.radius);i.contextEl.lineTo(n.startX+t.width,n.startY+t.height-t.radius);i.contextEl.quadraticCurveTo(n.startX+t.width,n.startY+t.height,n.startX+t.width-t.radius,n.startY+t.height);i.contextEl.lineTo(n.startX+t.radius,n.startY+t.height);i.contextEl.quadraticCurveTo(n.startX,n.startY+t.height,n.startX,n.startY+t.height-t.radius);i.contextEl.lineTo(n.startX,n.startY+t.radius);i.contextEl.quadraticCurveTo(n.startX,n.startY,n.startX+t.radius,n.startY);this._contextClosePath(t,i);t.indicatorText&&this._drawText(n,t)},_contextOpenPath:function(n,t){t.contextEl.save();t.contextEl.beginPath();n.strokeStyle&&(t.contextEl.strokeStyle=n.strokeStyle);n.opacity!=i&&(t.contextEl.globalAlpha=n.opacity);n.lineWidth&&(t.contextEl.lineWidth=n.lineWidth);n.fillStyle&&(t.contextEl.fillStyle=n.fillStyle)},_contextClosePath:function(n,t){t.contextEl.closePath();n.isFill&&t.contextEl.fill();n.isStroke&&t.contextEl.stroke();t.contextEl.restore()},_drawScales:function(){var t=this,i;this.scaleEl=this.Model.scales;this.contextEl.save();this.contextEl.translate(this.Model.frame.outerWidth+this.Model.frame.innerWidth,this.Model.frame.outerWidth+this.Model.frame.innerWidth);n.each(this.Model.scales,function(n,i){t.scaleIndex=n;t._setScaleCordinates(i,i.type)});this._setTicks();this._setLabels();this._setRange();this._setCustomLabel();this._flagPointer=!1;this._tempOpacity=this.model.scales[0].barPointers[0].opacity;this._setBarPointers();this._setMarkerPointers();this._setIndicators();n.each(this.Model.scales,function(n,i){i.showBarPointers&&i.barPointers.length>1&&(t.model.enableAnimation=!1);i.showMarkerPointers&&i.markerPointers.length>1&&(t.model.enableAnimation=!1)});this.contextEl.putImageData||(this.model.enableAnimation=!1);this.model.animationSpeed!=null&&this.model.animationSpeed>0&&(i=this.model.animationSpeed/25,i>=0&&this.model.enableAnimation&&this._animationFlag&&this._onAnimate(i))},_setTicks:function(){var t=this;n.each(this.Model.scales,function(i,r){r.showTicks&&(t.scaleIndex=i,r.ticks!=null&&(t.tickEl=r.ticks,n.each(r.ticks,function(n,i){t.tickIndex=n;t._setTicksCordinates(i,n)})))})},_setLabels:function(){var t=this;n.each(this.Model.scales,function(i,r){r.showLabels&&(t.scaleIndex=i,r.labels!=null&&(t.labelEl=r.labels,n.each(r.labels,function(n,i){t.labelIndex=n;t._setLabelCordinates(i,n)})))})},_setIndicators:function(){var t=this;n.each(this.Model.scales,function(i,r){t.scaleIndex=i;r.indicators!=null&&r.showIndicators&&(t.indicatorEl=r.indicators,n.each(r.indicators,function(n,i){t.indicatorIndex=n;t._drawIndicator(n,i)}))})},_setBarPointers:function(){var t=this;n.each(this.Model.scales,function(i,r){r.showBarPointers&&(t.scaleIndex=i,r.barPointers!=null&&(t.barPointerEl=r.barPointers,n.each(r.barPointers,function(n,i){t.barPointerIndex=n;r.opacity=t.scaleIndex==0&&t.barPointerIndex==0&&t.model.enableAnimation==!0&&t._flagPointer==!1&&t._animationFlag==!0&&t.model.scales[0].type=="thermometer"?0:t._tempOpacity;t._drawScaleBarPointer(i,n);t._flagPointer=!0})))})},_setMarkerPointers:function(){var t=this;n.each(this.Model.scales,function(i,r){r.showMarkerPointers&&(t.scaleIndex=i,r.markerPointers!=null&&(t.markerPointerEl=r.markerPointers,n.each(r.markerPointers,function(n,i){t.markerPointerIndex=n;t._drawMarkerPointer(i,n);t.canvasEl.attr("aria-label",t.model.scales[t.scaleIndex].markerPointers[t.markerPointerIndex].value)})))})},_onAnimate:function(n){var t=this,f,e,i=t.model.scales[0].minimum,r=t.model.scales[0].barPointers[0].value,u=t.model.scales[0].markerPointers[0].value;f=setInterval(function(){t.model?r>i||i==t.model.scales[0].minimum?(i=i+(t.model.scales[0].maximum-t.model.scales[0].minimum)/100,t.scaleEl[0].type=="thermometer"?(t.model.scales[0].barPointers[0].value=r>i?i:r,t.contextEl.putImageData!="undefined"?t._setBarPointers():r>i?t.setBarPointerValue(0,0,i):t.setBarPointerValue(0,0,r)):r>i?t.setBarPointerValue(0,0,i):t.setBarPointerValue(0,0,r)):(t._animationFlag=!1,t.setBarPointerValue(0,0,r),window.clearInterval(f)):window.clearInterval(f)},n);e=setInterval(function(){t.model&&t.model.enableMarkerPointerAnimation&&t.model.enableAnimation?u>i||i==t.model.scales[0].minimum?(i=i+(t.model.scales[0].maximum-t.model.scales[0].minimum)/100,t.scaleEl[0].type=="thermometer"?(t.model.scales[0].markerPointers[0].value=u>i?i:u,t.contextEl.putImageData!="undefined"?t._setMarkerPointers():u>i?t.setPointerValue(0,0,i):t.setPointerValue(0,0,u)):u>i?t.setPointerValue(0,0,i):t.setPointerValue(0,0,u)):(t._animationFlag=!1,t.setPointerValue(0,0,u),window.clearInterval(e)):window.clearInterval(e)},n)},_setRange:function(){var t=this;n.each(this.Model.scales,function(i,r){t.scaleIndex=i;r.ranges!=null&&r.showRanges&&(t.rangeEl=r.ranges,n.each(r.ranges,function(n,i){t.rangeIndex=n;t._drawRange(i)}))})},_setCustomLabel:function(){var t=this;n.each(this.Model.scales,function(i,r){t.scaleIndex=i;r.customLabels!=null&&r.showCustomLabels&&(t.customLabelEl=r.customLabels,n.each(r.customLabels,function(n,i){t.customLabelIndex=n;t.model.scales[t.scaleIndex].customLabels[t.customLabelIndex]!=null&&t.model.scales[t.scaleIndex].customLabels[t.customLabelIndex].positionType!=null&&t.model.scales[t.scaleIndex].customLabels[t.customLabelIndex].positionType=="outer"?t._setOuterCustomLabelCordinates(n,i):t._setCustomLabelCordinates(n,i)}))})},_setOuterCustomLabelCordinates:function(n,i){var r,u;this._customLblMaxSize=this._customLblMaxSize<parseFloat(i.font.size.match(/\d+/)[0])?parseFloat(i.font.size.match(/\d+/)[0]):this._customLblMaxSize;r=t.buildTag("div."+this._id+"outercustomlbl");r.text(this.model.scales[this.scaleIndex].customLabels[n].value);u=this.model.outerCustomLabelPosition=="right"||this.model.outerCustomLabelPosition=="left"?"left":"center";this.outerDiv.append(r);this.outerDiv.append("<\/br>");u=="center"?r.css({display:"inline-block",margin:"0 auto","max-width":this.model.width}):r.css({display:"inline-block","max-width":this.element.width()-this.model.width>10?this.element.width()-this.model.width:10});r.css({color:i.color,overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap","font-size":i.font!=null&&i.font.size!=null?i.font.size:"12px","font-family":i.font!=null&&i.font.fontFamily!=null?i.font.fontFamily:"Arial","font-weight":i.font!=null&&i.font.fontStyle!=null?i.font.fontStyle:"Normal","text-align":u})},_setScaleCordinates:function(n,t){var r,i,u,f;this.opacity=1;this.bottomRadius=Math.sqrt(n.width*n.width+n.width*n.width)/2;this.bounds={height:this.canvasEl[0].height-2*(this.Model.frame.outerWidth+this.Model.frame.innerWidth),width:this.canvasEl[0].width-2*(this.Model.frame.outerWidth+this.Model.frame.innerWidth)};this.Model.orientation=="Vertical"?(this.scaleStartX[this.scaleIndex]=(this.bounds.width-n.width)*(n.position.x/100),this.scaleStartY[this.scaleIndex]=(this.bounds.height-n.length)*(n.position.y/100)):(this.scaleStartX[this.scaleIndex]=(this.bounds.width-n.length)*(n.position.x/100),this.scaleStartY[this.scaleIndex]=(this.bounds.height-n.width)*(n.position.y/100));u=t=="roundedrectangle"?5:0;r={startX:this.scaleStartX[this.scaleIndex],startY:this.scaleStartY[this.scaleIndex]};i={width:this.Model.orientation=="Vertical"?n.width:n.length,isStroke:!0,topRounded:!0,fillStyle:n.backgroundColor?n.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.backgroundColor):this.Model.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,this.Model.backgroundColor),lineWidth:n.border.width,radius:u,height:this.Model.orientation=="Vertical"?n.length:n.width,isFill:!0,strokeStyle:n.border.color?n.border.color=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.border.color):this.Model.borderColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,this.Model.borderColor)};n.maximum<n.minimum&&(f=n.maximum,n.maximum=n.minimum,n.minimum=f);n.maximum==n.minimum&&(n.maximum=n.maximum+1);this.minimum(n.minimum);this.maximum(n.maximum);this._notifyArrayChange&&(this._notifyArrayChange("scales["+this.scaleIndex+"]maximum",n.maximum),this._notifyArrayChange("scales["+this.scaleIndex+"]minimum",n.minimum));n.shadowOffset&&(this.contextEl.shadowBlur=n.shadowOffset,this.contextEl.shadowColor=i.fillStyle=="transparent"?"rgba(0,0,0,0)":i.fillStyle);this._drawFrame(t,r,i);this.scaleEl[this.scaleIndex].type!="thermometer"||this.isScaleModified||(this._modifyWidth(),this.isScaleModified=!0);this.contextEl.getImageData&&(this.scaleImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height))},_setTicksCordinates:function(n){var r,u,f,i,t;if(this.scaleEl[this.scaleIndex].majorIntervalValue>this.scaleEl[this.scaleIndex].minorIntervalValue){for(i=n.type.toLowerCase()=="majorinterval"?this.scaleEl[this.scaleIndex].majorIntervalValue:this.scaleEl[this.scaleIndex].minorIntervalValue,n.placement=="near"?r=this.Model.orientation=="Vertical"?this.scaleStartX[this.scaleIndex]:this.scaleStartY[this.scaleIndex]:n.placement=="far"?r=this.Model.orientation=="Vertical"?this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width:n.placement=="center"&&(r=this.Model.orientation=="Vertical"?this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2),u=n.placement=="near"?-n.height:n.height,t=this.scaleEl[this.scaleIndex].maximum;t>=this.scaleEl[this.scaleIndex].minimum&&i!="";t-=i)(i==this.scaleEl[this.scaleIndex].minorIntervalValue&&t%this.scaleEl[this.scaleIndex].majorIntervalValue!=0||i==this.scaleEl[this.scaleIndex].majorIntervalValue)&&(f=this._getClockwiseLinePosition(t),this.region={lineChangePosition:f+(this.Model.orientation=="horizontal"?n.distanceFromScale.x:n.distanceFromScale.y),lineStaticPosition:r+(this.Model.orientation=="horizontal"?n.distanceFromScale.y:n.distanceFromScale.x)},this.style={lineHeight:u,angle:this.Model.orientation=="Vertical"?n.angle:n.angle+270,tickShape:n.TickShape,strokeStyle:n.color?n.color=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.color):this.Model.tickColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,this.Model.tickColor),lineWidth:n.width},this.Model.drawTicks&&this._onDrawTicks(this.Model.orientation=="Vertical"?n.angle:n.angle+270,t),this._drawTickMark(this.region,this.style));this.contextEl.getImageData&&(this.tickImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height))}},_drawTickMark:function(n,t){this.contextEl.beginPath();this.contextEl.save();this.contextEl.lineWidth=t.lineWidth;this.contextEl.strokeStyle=t.strokeStyle;this.Model.orientation=="Vertical"?this.contextEl.translate(n.lineStaticPosition,n.lineChangePosition):this.contextEl.translate(n.lineChangePosition,n.lineStaticPosition);this.contextEl.lineTo(0,0);this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this.contextEl.rotate(Math.PI*(t.angle/180)):this.contextEl.rotate(-(Math.PI*(t.angle/180)));this.contextEl.lineTo(t.lineHeight,0);this.contextEl.stroke();this.contextEl.restore();this.contextEl.closePath()},_addDecimal:function(n,t){var r=n.toString(),u=t.toString(),f,e,i,o;return f=r.indexOf(".")>-1?r.length-r.indexOf(".")-1:0,e=u.indexOf(".")>-1?u.length-u.indexOf(".")-1:0,i=f>e?f:e,o=(n*Math.pow(10,i)+t*Math.pow(10,i))/Math.pow(10,i),o},_setLabelCordinates:function(n){var u,f,i,e,r,o=this.model.locale,t;if(this.scaleEl[this.scaleIndex].majorIntervalValue>this.scaleEl[this.scaleIndex].minorIntervalValue)for(this.Model.orientation=="Vertical"?(u=n.distanceFromScale.x,f=n.distanceFromScale.y):(u=n.distanceFromScale.y,f=n.distanceFromScale.x),r=n.type=="major"?this.scaleEl[this.scaleIndex].majorIntervalValue:this.scaleEl[this.scaleIndex].minorIntervalValue,n.placement=="near"?(i=this.Model.orientation=="Vertical"?this.scaleStartX[this.scaleIndex]-this.scaleEl[this.scaleIndex].border.width/2:this.scaleStartY[this.scaleIndex]-this.scaleEl[this.scaleIndex].border.width-5,this.contextEl.textAlign=this.Model.orientation=="Vertical"?"right":"center"):n.placement=="far"?(i=this.Model.orientation=="Vertical"?this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width+this.scaleEl[this.scaleIndex].border.width/2:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width+this.scaleEl[this.scaleIndex].border.width+5,this.contextEl.textAlign=this.Model.orientation=="Vertical"?"left":"center"):(this.contextEl.textAlign="center",i=this.Model.orientation=="Vertical"?this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2+this.scaleEl[this.scaleIndex].border.width/2:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2+this.scaleEl[this.scaleIndex].border.width/2),t=this.scaleEl[this.scaleIndex].minimum;t<=this.scaleEl[this.scaleIndex].maximum;t=this._addDecimal(t,r))(r==this.scaleEl[this.scaleIndex].minorIntervalValue&&t%this.scaleEl[this.scaleIndex].majorIntervalValue!=0||r==this.scaleEl[this.scaleIndex].majorIntervalValue)&&(e=this.scaleEl[this.scaleIndex].direction=="counterclockwise"?this._getCounterClockwiseLinePosition(t):this._getClockwiseLinePosition(t),this.labelValue=t,this.region={lineChangePosition:e+f,lineStaticPosition:i+u},this.style={angle:this.Model.orientation=="Vertical"?n.angle:n.angle+270,fillStyle:n.textColor?n.textColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.textColor):this.Model.labelColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,this.Model.labelColor),opacity:isNaN(n.opacity)?1:n.opacity,font:this._getFontString(this,n.font),textValue:this.labelValue},this.style.textValue=this.labelValue=o&&this.model.enableGroupSeparator?this.labelValue.toLocaleString(o):this.labelValue,this.Model.drawLabels&&this._onDrawLabels(this.Model.orientation=="Vertical"?n.angle:n.angle+270),this._drawLabel(this.region,this.style,!1));this.contextEl.getImageData&&(this.labelImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height))},_drawLabel:function(n,i,r){if(this.contextEl.beginPath(),this.contextEl.save(),this.contextEl.textBaseline="middle",this.contextEl.fillStyle=i.fillStyle,this.contextEl.font=i.font,i.opacity&&(this.contextEl.globalAlpha=i.opacity),this.Model.orientation=="Vertical"?(this.contextEl.translate(n.lineStaticPosition,n.lineChangePosition),this.model.tooltip.showLabelTooltip&&!r&&this._savedPoints.push({startX:n.lineStaticPosition+5,startY:n.lineChangePosition+10,width:15,height:15,value:i.textValue}),this.model.tooltip.showCustomLabelTooltip&&r&&this._savedPoints.push({startX:n.lineStaticPosition-35,startY:n.lineChangePosition+10,width:110,height:15,value:i.textValue})):(this.contextEl.translate(n.lineChangePosition,n.lineStaticPosition),this.model.tooltip.showLabelTooltip&&!r&&this._savedPoints.push({startX:n.lineChangePosition+10,startY:n.lineStaticPosition+10,width:15,height:15,value:i.textValue}),this.model.tooltip.showCustomLabelTooltip&&r&&this._savedPoints.push({startX:n.lineChangePosition-35,startY:n.lineStaticPosition+10,width:110,height:15,value:i.textValue})),this.contextEl.lineTo(0,0),this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this.contextEl.rotate(Math.PI*(i.angle/180)):this.contextEl.rotate(-(Math.PI*(i.angle/180))),!t.isNullOrUndefined(r)&&!r){var u=this.model.scales[this.scaleIndex].labels[this.labelIndex].unitTextPlacement;t.isNullOrUndefined(u)||u.toString()!="back"?t.isNullOrUndefined(u)||u.toString()!="front"||(i.textValue=this.model.scales[this.scaleIndex].labels[this.labelIndex].unitText+i.textValue):i.textValue=i.textValue+this.model.scales[this.scaleIndex].labels[this.labelIndex].unitText}this.contextEl.fillText(i.textValue,0,0);this.contextEl.fill();this.contextEl.restore()},_drawScaleBarPointer:function(n,t){n.value=n.value>this.scaleEl[this.scaleIndex].maximum?this.scaleEl[this.scaleIndex].maximum:n.value;n.value=n.value<this.scaleEl[this.scaleIndex].minimum?this.scaleEl[this.scaleIndex].minimum:n.value;var u,r,s,f,e,o,c,i,h;h=[{ColorStop:0,Color:this.Model.pointerGradient1=="transparent"?"rgba(0,0,0,0)":this.Model.pointerGradient1},{ColorStop:1,Color:this.Model.pointerGradient2=="transparent"?"rgba(0,0,0,0)":this.Model.pointerGradient2}];s=this.scaleEl[this.scaleIndex].type.toLowerCase()=="roundedrectangle"?5:0;this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?(r=this._getClockwiseLinePosition(n.value),this.scaleEl[this.scaleIndex].type=="thermometer"&&this.isScaleModified&&(this._restoreWidth(),this.isModify=!0),this.Model.orientation=="Vertical"?(i=this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2+n.distanceFromScale,u=this.contextEl.createLinearGradient(i,this.scaleStartY[this.scaleIndex],i+n.width,this.scaleStartY[this.scaleIndex])):(i=this.scaleStartX[this.scaleIndex],u=this.contextEl.createLinearGradient(i,this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2,i,this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2+n.width/2))):(r=this._getCounterClockwiseLinePosition(n.value),this.scaleEl[this.scaleIndex].type=="thermometer"&&this.isScaleModified&&(this._restoreWidth(),this.isModify=!0),this.Model.orientation=="Vertical"?(i=this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2+n.distanceFromScale,u=this.contextEl.createLinearGradient(i,this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].length-r,i+n.width,this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].length-r)):(i=this.scaleEl[this.scaleIndex].type=="thermometer"?r-this.scaleEl[this.scaleIndex].width/2-this.scaleEl[this.scaleIndex].border.width/2:r-this.scaleEl[this.scaleIndex].border.width/2,u=this.contextEl.createLinearGradient(i,this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2,i,this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2+n.width/2)));n.backgroundColor?c=n.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.backgroundColor):n.gradients?this._setGradientColor(this,u,n.gradients.colorInfo):this.Model.ScaleInterior?this._setGradientColor(this,u,this.Model.ScaleInterior.colorInfo):this._setGradientColor(this,u,h);this.Model.orientation=="Vertical"?(o=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].border.width/2:r,this.scaleEl[this.scaleIndex].direction=="counterclockwise"&&this.scaleEl[this.scaleIndex].type=="thermometer"&&(o=o-this.scaleEl[this.scaleIndex].width/2),f=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?r-this.scaleStartY[this.scaleIndex]:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].length-r-this.scaleEl[this.scaleIndex].border.width/2,e=n.width):(o=this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2+this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale,f=n.width,e=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?r-this.scaleStartX:this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].length-r);this.region={startX:i+this.scaleEl[this.scaleIndex].border.width/2,startY:o};this.style={width:this.scaleEl[this.scaleIndex].type=="thermometer"&&this.Model.orientation=="horizontal"?e+f/2-this.scaleEl[this.scaleIndex].border.width/2:e,lineWidth:n.border.width,radius:s,topRounded:!1,isStroke:!1,isFill:!0,height:this.scaleEl[this.scaleIndex].type=="thermometer"&&this.Model.orientation=="Vertical"?f+e/2:f,strokeStyle:n.border.color==null?this.Model.borderColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,this.Model.borderColor):n.border.color=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.border.color),fillStyle:n.backgroundColor?n.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.backgroundColor):u=="transparent"?"rgba(0,0,0,0)":this._getColor(n,u),opacity:isNaN(n.opacity)?.4:n.opacity,calDistance:this.scaleEl[this.scaleIndex].type=="thermometer"&&this.Model.orientation=="Vertical"?(this.scaleEl[this.scaleIndex].width-this.barPointerEl[this.barPointerIndex].width)/2:0};this.value(n.value);this._notifyArrayChange&&this._notifyArrayChange("scales["+this.scaleIndex+"]barpointers["+t+"]value",n.value);this.Model.drawBarPointers&&this._onDrawBarPointers(n.value);this._drawFrame(this.scaleEl[this.scaleIndex].type,this.region,this.style);this.contextEl.getImageData&&(this.barPointerImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height))},_drawMarkerPointer:function(n,t){n.value=n.value>this.scaleEl[this.scaleIndex].maximum?this.scaleEl[this.scaleIndex].maximum:n.value;n.value=n.value<this.scaleEl[this.scaleIndex].minimum?this.scaleEl[this.scaleIndex].minimum:n.value;var i,f,o,e,r,u,s,h=[{ColorStop:0,Color:this.Model.pointerGradient1=="transparent"?"rgba(0,0,0,0)":this.Model.pointerGradient1},{ColorStop:1,Color:this.Model.pointerGradient2=="transparent"?"rgba(0,0,0,0)":this.Model.pointerGradient2}];this.markerPlacement=n.placement;o=Math.sqrt(n.width*n.width+n.length*n.length)/2;this.scaleEl[this.scaleIndex].type=="thermometer"&&this.isModify&&this._modifyWidth();this.Model.orientation=="Vertical"?(this.markerPlacement=="far"&&(i=this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width+this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale,r=0),this.markerPlacement=="near"&&(i=n.type=="star"?this.scaleStartX[this.scaleIndex]+n.distanceFromScale-n.width:this.scaleStartX[this.scaleIndex]+n.distanceFromScale,r=180),this.markerPlacement=="center"&&(i=n.type=="circle"?this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2+n.distanceFromScale+n.border.width:this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2+n.distanceFromScale,r=0)):(this.markerPlacement=="far"&&(i=this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width+this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale,r=90),this.markerPlacement=="near"&&(i=n.type=="star"?this.scaleStartY[this.scaleIndex]-this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale-n.length:this.scaleStartY[this.scaleIndex]-this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale,r=270),this.markerPlacement=="center"&&(i=n.type=="circle"?this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.length/2+n.distanceFromScale+n.border.width:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.length/2+n.distanceFromScale,r=90));e=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this._getClockwiseLinePosition(n.value):this._getCounterClockwiseLinePosition(n.value);n.type=="star"?this.Model.orientation=="Vertical"?(u=this.contextEl.createLinearGradient(i,this.scaleStartY[this.scaleIndex],i+n.width,this.scaleStartY[this.scaleIndex]),f=e-n.length/3):(u=this.contextEl.createLinearGradient(e,i,e,i+n.length),f=e-n.width/2):(u=this.contextEl.createLinearGradient(0,0,n.width,0),f=e);n.type.toLowerCase()=="roundedrectangle"&&(this.Model.orientation=="Vertical"&&this.markerPlacement=="near"?f+=n.length:this.Model.orientation=="horizontal"&&(this.markerPlacement=="near"&&(f-=n.width),i+=n.width/2));n.backgroundColor?s=n.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.backgroundColor):n.gradients?this._setGradientColor(this,u,n.gradients.colorInfo):this.Model.PointerInterior?this._setGradientColor(this,u,this.Model.PointerInterior.colorInfo):this._setGradientColor(this,u,h);this.region={startX:this.Model.orientation=="Vertical"?i:f,startY:this.Model.orientation=="Vertical"?f:i};this.style={width:n.width,radius:n.type=="rectangle"?0:o,height:n.length,lineWidth:n.border.width,isFill:!0,isStroke:!0,angle:r,strokeStyle:n.border.color=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.border.color),markerPlacement:this.markerPlacement,opacity:isNaN(n.opacity)?.4:n.opacity,fillStyle:n.backgroundColor?n.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.backgroundColor):u=="transparent"?"rgba(0,0,0,0)":this._getColor(n,u)};this.value(n.value);this._notifyArrayChange&&this._notifyArrayChange("scales["+this.scaleIndex+"]markerpointers["+t+"]value",n.value);this.Model.drawMarkerPointers&&this._onDrawMarkerPointers(r,n.value);n.type.toLowerCase()=="roundedrectangle"&&(this.style.radius=5);this._drawMarkerType(n.type,this.region,this.style);this.scaleEl[this.scaleIndex].type=="thermometer"&&this.isModify&&(this._restoreWidth(),this.isScaleModified=!1);this.contextEl.getImageData&&(this.markerPointerImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height))},_drawMarkerType:function(n,t,i){switch(n){case"rectangle":this._drawRectangle(t,i,this);break;case"triangle":this._drawTriangle(t,i,this);break;case"ellipse":this._drawEllipse(t,i,this);break;case"diamond":this._drawDiamond(t,i,this);break;case"pentagon":this._drawPentagon(t,i,this);break;case"circle":this._drawCircle(t,i,this);break;case"slider":this._drawSlider(t,i,this);break;case"star":this._drawStar(t,i,this);break;case"pointer":this._drawPointer(t,i,this);break;case"wedge":this._drawWedge(t,i,this);break;case"trapezoid":this._drawTrapezoid(t,i,this);break;case"roundedrectangle":this._drawRoundedRectangle(t,i,this)}},_drawRange:function(n){var e,o,u,t,f,r,i,s,h;n.startValue<this.scaleEl[this.scaleIndex].maximum&&n.endValue>this.scaleEl[this.scaleIndex].minimum&&this.scaleEl[this.scaleIndex].minimum<this.scaleEl[this.scaleIndex].maximum&&n.endValue<=this.scaleEl[this.scaleIndex].maximum&&(e=n.startValue<this.scaleEl[this.scaleIndex].minimum?this.scaleEl[this.scaleIndex].minimum:n.startValue,o=n.endValue>this.scaleEl[this.scaleIndex].maximum?this.scaleEl[this.scaleIndex].maximum:n.endValue,this.rangePosition=n.placement,h=[{ColorStop:0,Color:this.Model.pointerGradient1=="transparent"?"rgba(0,0,0,0)":this.Model.pointerGradient1},{ColorStop:1,Color:this.Model.pointerGradient2=="transparent"?"rgba(0,0,0,0)":this.Model.pointerGradient2}],u=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this._getClockwiseLinePosition(e):this._getCounterClockwiseLinePosition(e),t=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this._getClockwiseLinePosition(o):this._getCounterClockwiseLinePosition(o),this.Model.orientation=="Vertical"?(n.placement=="far"&&(f=this.scaleStartX[this.scaleIndex]+n.distanceFromScale+this.scaleEl[this.scaleIndex].width+this.scaleEl[this.scaleIndex].border.width),n.placement=="near"&&(f=this.scaleStartX[this.scaleIndex]+n.distanceFromScale),n.placement=="center"&&(f=n.startWidth>n.endWidth?this.scaleStartX[this.scaleIndex]+n.distanceFromScale+this.scaleEl[this.scaleIndex].width/2-n.startWidth/2:this.scaleStartX[this.scaleIndex]+n.distanceFromScale+this.scaleEl[this.scaleIndex].width/2-n.endWidth/2),i=this.contextEl.createLinearGradient(t,t,t,u),this.region={startX:f,startY:u,endY:t}):(n.placement=="far"&&(r=this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width+n.distanceFromScale+this.scaleEl[this.scaleIndex].border.width),n.placement=="near"&&(r=this.scaleStartY[this.scaleIndex]+n.distanceFromScale),n.placement=="center"&&(r=n.startWidth>n.endWidth?this.scaleStartY[this.scaleIndex]+n.distanceFromScale+this.scaleEl[this.scaleIndex].width/2-n.startWidth/2:this.scaleStartY[this.scaleIndex]+n.distanceFromScale+this.scaleEl[this.scaleIndex].width/2-n.endWidth/2),i=this.contextEl.createLinearGradient(t,r,u,r),this.region={startX:u,startY:r,endX:t}),n.backgroundColor?s=n.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.backgroundColor):n.gradients?this._setGradientColor(this,i,n.gradients.colorInfo):this.Model.RangeInterior?this._setGradientColor(this,i,this.Model.RangeInterior.colorInfo):this._setGradientColor(this,i,h),this.style={startWidth:n.startWidth,lineWidth:n.border.width,isStroke:!0,isFill:!0,opacity:isNaN(n.opacity)?.4:n.opacity,endWidth:n.endWidth,fillStyle:n.backgroundColor?s=="transparent"?"rgba(0,0,0,0)":this._getColor(n,s):i=="transparent"?"rgba(0,0,0,0)":this._getColor(n,i),strokeStyle:n.border.color?n.border.color=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.border.color):this.Model.borderColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,this.Model.borderColor)},this.Model.drawRange&&this._onDrawRange(),this._drawRangeBar(this.region,this.style),this.contextEl.getImageData&&(this.rangeImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height)))},_drawRangeBar:function(n,t){this._contextOpenPath(t,this);this.Model.orientation=="Vertical"?(this.contextEl.lineTo(n.startX,n.startY),this.contextEl.lineTo(n.startX,n.endY),this.rangePosition=="near"?(this.contextEl.lineTo(n.startX-t.endWidth,n.endY),this.contextEl.lineTo(n.startX-t.startWidth,n.startY)):(this.contextEl.lineTo(n.startX+t.endWidth,n.endY),this.contextEl.lineTo(n.startX+t.startWidth,n.startY))):(this.contextEl.lineTo(n.startX,n.startY),this.contextEl.lineTo(n.endX,n.startY),this.rangePosition=="near"?(this.contextEl.lineTo(n.endX,n.startY-t.endWidth),this.contextEl.lineTo(n.startX,n.startY-t.startWidth)):(this.contextEl.lineTo(n.endX,n.startY+t.endWidth),this.contextEl.lineTo(n.startX,n.startY+t.startWidth)));this._contextClosePath(t,this)},_setCustomLabelCordinates:function(n,t){this._customLblMaxSize=this._customLblMaxSize<parseFloat(t.font.size.match(/\d+/)[0])?parseFloat(t.font.size.match(/\d+/)[0]):this._customLblMaxSize;var i,r;this.contextEl.textAlign="center";this.Model.orientation=="Vertical"?(i=this.bounds.width*(t.position.x/100),r=this.bounds.height*(t.position.y/100)):(i=this.bounds.width*(t.position.x/100),r=this.bounds.height*(t.position.y/100));this.region={lineStaticPosition:this.Model.orientation=="Vertical"?i:r,lineChangePosition:this.Model.orientation=="Vertical"?r:i};this.style={angle:t.textAngle,textValue:t.value,fillStyle:t.color?t.color=="transparent"?"rgba(0,0,0,0)":this._getColor(t,t.color):this.Model.labelColor=="transparent"?"rgba(0,0,0,0)":this._getColor(t,this.Model.labelColor),font:this._getFontString(this,t.font)};this.Model.drawCustomLabel&&this._onDrawCustomLabel();this._drawLabel(this.region,this.style,!0);this.contextEl.getImageData&&(this.customLabelImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height))},_drawIndicator:function(i,r){var u=this,f,e,o,s=!1;f=(this.bounds.width-2*r.width)*(r.position.x/100);e=(this.bounds.height-2*r.height)*(r.position.y/100);o={x:this.bounds.width*(r.textLocation.x/100),y:this.bounds.height*(r.textLocation.y/100)};u.region={startX:r.type=="circle"?f+r.width:f,textLocation:o,startY:r.type=="circle"?e+r.height:e,startAngle:0,endAngle:2*Math.PI};u.style={radius:r.type=="roundedrectangle"?2:0,strokeStyle:r.border.color?r.border.color=="transparent"?"rgba(0,0,0,0)":this._getColor(r,r.border.color):this._getColor(r,"#FFFFFF"),angle:0,circleRadius:(r.height+r.width)/2,height:r.height,width:r.width,lineWidth:r.border.width,fillStyle:r.backgroundColor?r.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(r,r.backgroundColor):this._getColor(r,"#FFFFFF"),isStroke:!0,isFill:!0,indicatorText:null,textColor:null,font:null,counterClockwise:!1};this.Model.drawIndicators&&this._onDrawIndicators(u.style,u.region);r.stateRanges!=null&&n.each(r.stateRanges,function(n,i){u.markerPointerEl[u.markerPointerIndex].value>=i.startValue&&u.markerPointerEl[u.markerPointerIndex].value<=i.endValue&&(s=!0,!t.isNullOrUndefined(i.text)&&i.text.length>0&&(u.style.indicatorText=i.text,u.style.textColor=i.textColor=="transparent"?"rgba(0,0,0,0)":u._getColor(r,i.textColor),u.style.font=u._getFontString(u,r.font)),r.type!="text"?(u.style.strokeStyle=i.borderColor=="transparent"?"rgba(0,0,0,0)":u._getColor(r,i.borderColor),u.style.fillStyle=i.backgroundColor=="transparent"?"rgba(0,0,0,0)":u._getColor(r,i.backgroundColor),u._drawFrame(r.type,u.region,u.style,u)):r.type=="text"&&u._drawText(u.region,u.style))});s||r.type=="text"||this._drawFrame(r.type,u.region,u.style,u);this.contextEl.getImageData&&(this.indicatorImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height))},_drawFrame:function(n,t,i){switch(n.toLowerCase()){case"circle":this._drawFrameCircle(t,i,this);break;case"rectangle":this._drawFrameRectangle(t,i,this);break;case"roundedrectangle":this._drawFrameRoundedRectangle(t,i,this);break;case"thermometer":this._drawFrameThermometer(t,i,this)}},_drawText:function(n,t){this.contextEl.beginPath();this.contextEl.textAlign="center";this.contextEl.fillStyle=t.textColor=="transparent"?"rgba(0,0,0,0)":t.textColor;this.contextEl.font=t.font;this.contextEl.fillText(t.indicatorText,n.textLocation.x,n.textLocation.y);this.contextEl.closePath()},_drawTriangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);this._contextClosePath(t,i)},_drawPointer:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(t.width,t.height/4);i.contextEl.lineTo(t.width,-t.height/4);i.contextEl.lineTo(t.width/2,-t.height/4);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/2,t.height/2);i.contextEl.lineTo(t.width/2,t.height/4);this._contextClosePath(t,i)},_drawWedge:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(3*t.width/4,0);i.contextEl.lineTo(t.width,t.height/2);this._contextClosePath(t,i)},_drawSlider:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/4,-t.height/2);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);i.contextEl.lineTo(t.width/4,t.height/2);this._contextClosePath(t,i)},_drawStar:function(n,t,i){this._contextOpenPath(t,i);i.Model.orientation=="horizontal"&&i.markerPlacement=="near"?(i.contextEl.lineTo(n.startX+t.width-t.width/6,n.startY),i.contextEl.lineTo(n.startX,n.startY+t.height-t.height/3),i.contextEl.lineTo(n.startX+t.width,n.startY+t.height-t.height/3),i.contextEl.lineTo(n.startX+t.width/6,n.startY),i.contextEl.lineTo(n.startX+t.width/2,n.startY+t.height)):(i.contextEl.lineTo(n.startX+t.width/6,n.startY+t.height),i.contextEl.lineTo(n.startX+t.width,n.startY+t.height/3),i.contextEl.lineTo(n.startX,n.startY+t.height/3),i.contextEl.lineTo(n.startX+t.width-t.width/6,n.startY+t.height),i.contextEl.lineTo(n.startX+t.width/2,n.startY));this._contextClosePath(t,i)},_drawPentagon:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/3,-t.height/2);i.contextEl.lineTo(t.width,-t.height/4);i.contextEl.lineTo(t.width,t.height/4);i.contextEl.lineTo(t.width/3,t.height/2);this._contextClosePath(t,i)},_drawDiamond:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.lineTo(t.width,0);i.contextEl.lineTo(t.width/2,t.height/2);i.contextEl.lineTo(0,0);this._contextClosePath(t,i)},_drawCircle:function(n,t,i){var r=Math.sqrt(t.height*t.height+t.width*t.width)/2;t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.arc(r/2,0,r/2,0,Math.PI*2,!0);this._contextClosePath(t,i)},_drawTrapezoid:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(0,-t.height/4);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);i.contextEl.lineTo(0,t.height/4);this._contextClosePath(t,i)},_drawRectangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(0,-t.height/2);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);i.contextEl.lineTo(0,t.height/2);this._contextClosePath(t,i)},_drawRoundedRectangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY-t.height/2);this._setContextRotation(t,i);i.contextEl.lineTo(t.radius,0);i.contextEl.lineTo(t.width-t.radius,0);i.contextEl.quadraticCurveTo(t.width,0,t.width,t.radius);i.contextEl.lineTo(t.width,t.height-t.radius);i.contextEl.quadraticCurveTo(t.width,t.height,t.width-t.radius,t.height);i.contextEl.lineTo(t.radius,t.height);i.contextEl.quadraticCurveTo(0,t.height,0,t.height-t.radius);i.contextEl.lineTo(0,t.radius);i.contextEl.quadraticCurveTo(0,0,t.radius,0);this._contextClosePath(t,i)},_drawCustomImage:function(t,i){var r=new Image;n(r).on("load",function(){t.contextEl.drawImage(this,0,0,t.Model.width,t.Model.height);t.Model.scales!=null&&t._drawScales();t.Model.Items!=null&&t._renderItems()}).attr("src",i)},_drawEllipse:function(n,t,i){var r=Math.sqrt(t.height*t.height+t.width*t.width)/2;t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.scale(2,1);i.contextEl.arc(r/2,0,r/2,0,Math.PI*2,!0);this._contextClosePath(t,i)},_getIndicatorImage:function(){return this.pointerImage?this.pointerImage:this._getMarkerPointerImage()},_getBarPointerImage:function(){return this.customLabelImage?this.customLabelImage:this._getCustomLabelImage()},_getMarkerPointerImage:function(){return this.barPointerImage?this.barPointerImage:this._getCustomLabelImage()},_getCustomLabelImage:function(){return this.rangeImage?this.rangeImage:this._getRangeImage()},_getRangeImage:function(){return this.labelImage?this.labelImage:this._getLabelImage()},_getLabelImage:function(){return this.tickImage?this.tickImage:this._getTickImage()},_getTickImage:function(){return this.scaleImage?this.scaleImage:this.outerImage},setPointerValue:function(n,i,r){n<this.Model.scales.length&&i<this.Model.scales[n].markerPointers.length&&r!=null&&(r<=this.scaleEl[n].maximum&&r>=this.scaleEl[n].minimum&&(this.scaleEl[n].markerPointers[i].value=r),this.contextEl.putImageData?(this.contextEl.putImageData(this._getMarkerPointerImage(),0,0),!t.isNullOrUndefined(this.outerDiv)&&this.model.scale[n].showCustomLabels&&this.outerDiv.empty(),this._setCustomLabel(),this._setMarkerPointers(),this._setIndicators()):this.initialize())},getPointerValue:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length?this.scaleEl[n].markerPointers[t].value:null},setPointerWidth:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length&&i!=null&&(this.scaleEl[n].markerPointers[t].width=i,this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():(this.contextEl.putImageData(this._getMarkerPointerImage(),0,0),this._setMarkerPointers()):this.initialize())},getPointerWidth:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length?this.scaleEl[n].markerPointers[t].width:null},setPointerHeight:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length&&i!=null&&(this.scaleEl[n].markerPointers[t].length=i,this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():(this.contextEl.putImageData(this._getMarkerPointerImage(),0,0),this._setMarkerPointers()):this.initialize())},getPointerHeight:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length?this.scaleEl[n].markerPointers[t].length:null},_getColor:function(n,t){return typeof t=="string"?t:"rgba("+t.r+", "+t.g+","+t.b+", "+t.a/255+")"},setPointerDistanceFromScale:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length&&i!=null&&(this.scaleEl[n].markerPointers[t].distanceFromScale=i,this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():(this.contextEl.putImageData(this._getMarkerPointerImage(),0,0),this._setMarkerPointers()):this.initialize())},getPointerDistanceFromScale:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length?this.scaleEl[n].markerPointers[t].distanceFromScale:null},setPointerPlacement:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length&&i!=null&&(this.scaleEl[n].markerPointers[t].placement=i,this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():(this.contextEl.putImageData(this._getMarkerPointerImage(),0,0),this._setMarkerPointers()):this.initialize())},getPointerPlacement:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length?this.scaleEl[n].markerPointers[t].placement:null},setMarkerStyle:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length&&i!=null&&(this.scaleEl[n].markerPointers[t].type=i,this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():(this.contextEl.putImageData(this._getMarkerPointerImage(),0,0),this._setMarkerPointers()):this.initialize())},getMarkerStyle:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length?this.scaleEl[n].markerPointers[t].type:null},setBarPointerValue:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].barPointers.length&&i!=null&&(i<=this.scaleEl[n].maximum&&i>=this.scaleEl[n].minimum&&(this.scaleEl[n].barPointers[t].value=i),this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():this._reDrawBarPointer():this.initialize())},getBarPointerValue:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].barPointers.length?this.scaleEl[n].barPointers[t].value:null},setBarWidth:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].barPointers.length&&i!=null&&(this.scaleEl[n].barPointers[t].width=i,this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():this._reDrawBarPointer():this.initialize())},getBarWidth:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].barPointers.length?this.scaleEl[n].barPointers[t].width:null},setBarDistanceFromScale:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].barPointers.length&&i!=null&&(this.scaleEl[n].barPointers[t].distanceFromScale=i,this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():this._reDrawBarPointer():this.initialize())},getBarDistanceFromScale:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].barPointers.length?this.scaleEl[n].barPointers[t].distanceFromScale:null},setCustomLabelValue:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].customLabels.length&&i!=null&&(this.scaleEl[n].customLabels[t].value=i,this.contextEl.putImageData?this._reDrawCustomLabel():this.initialize())},getCustomLabelValue:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].customLabels.length?this.scaleEl[n].customLabels[t].value:null},setCustomLabelAngle:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].customLabels.length&&i!=null&&(this.scaleEl[n].customLabels[t].textAngle=i,this.contextEl.putImageData?this._reDrawCustomLabel():this.initialize())},getCustomLabelAngle:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].customLabels.length?this.scaleEl[n].customLabels[t].textAngle:null},setRangeStartValue:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].startValue=i,this.contextEl.putImageData?this._reDrawRange():this.initialize())},getRangeStartValue:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length?this.scaleEl[n].ranges[t].startValue:null},setRangeEndValue:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].endValue=i,this.contextEl.putImageData?this._reDrawRange():this.initialize())},getRangeEndValue:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length?this.scaleEl[n].ranges[t].endValue:null},setRangeStartWidth:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].startWidth=i,this.contextEl.putImageData?this._reDrawRange():this.initialize())},getRangeStartWidth:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length?this.scaleEl[n].ranges[t].startWidth:null},setRangeEndWidth:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].endWidth=i,this.contextEl.putImageData?this._reDrawRange():this.initialize())},getRangeEndWidth:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length?this.scaleEl[n].ranges[t].endWidth:null},setRangeDistanceFromScale:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].distanceFromScale=i,this.contextEl.putImageData?this._reDrawRange():this.initialize())},getRangeDistanceFromScale:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length?this.scaleEl[n].ranges[t].distanceFromScale:null},setRangePosition:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].placement=i,this.contextEl.putImageData?this._reDrawRange():this.initialize())},getRangePosition:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length?this.scaleEl[n].ranges[t].placement:null},setRangeBorderWidth:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].border.width=i,this.contextEl.putImageData?this._reDrawRange():this.initialize())},getRangeBorderWidth:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length?this.scaleEl[n].ranges[t].border.width:null},setLabelAngle:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].angle=i,this.contextEl.putImageData?this._reDrawLabel():this.initialize())},getLabelAngle:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].labels.length?this.scaleEl[n].labels[t].angle:null},setLabelStyle:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].type=i,this.contextEl.putImageData?this._reDrawLabel():this.initialize())},getLabelStyle:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].labels.length?this.scaleEl[n].labels[t].type:null},setLabelPlacement:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].placement=i,this.contextEl.putImageData?this._reDrawLabel():this.initialize())},getLabelPlacement:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].labels.length?this.scaleEl[n].labels[t].placement:null},setLabelXDistanceFromScale:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].distanceFromScale.x=i,this.contextEl.putImageData?this._reDrawLabel():this.initialize())},getLabelXDistanceFromScale:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].labels.length?this.scaleEl[n].labels[t].distanceFromScale.x:null},setLabelYDistanceFromScale:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].distanceFromScale.y=i,this.contextEl.putImageData?this._reDrawLabel():this.initialize())},getLabelYDistanceFromScale:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].labels.length?this.scaleEl[n].labels[t].distanceFromScale.y:null},setTickAngle:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].angle=i,this.contextEl.putImageData?this._reDrawTickMark():this.initialize())},getTickAngle:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length?this.scaleEl[n].ticks[t].angle:null},setTickWidth:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].width=i,this.contextEl.putImageData?this._reDrawTickMark():this.initialize())},getTickWidth:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length?this.scaleEl[n].ticks[t].width:null},setTickHeight:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].height=i,this.contextEl.putImageData?this._reDrawTickMark():this.initialize())},getTickHeight:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length?this.scaleEl[n].ticks[t].height:null},setTickStyle:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].type=i,this.contextEl.putImageData?this._reDrawTickMark():this.initialize())},getTickStyle:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length?this.scaleEl[n].ticks[t].type:null},setTickPlacement:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].placement=i,this.contextEl.putImageData?this._reDrawTickMark():this.initialize())},getTickPlacement:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length?this.scaleEl[n].ticks[t].placement:null},setTickXDistanceFromScale:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].distanceFromScale.x=i,this.contextEl.putImageData?this._reDrawTickMark():this.initialize())},getTickXDistanceFromScale:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length?this.scaleEl[n].ticks[t].distanceFromScale.x:null},setTickYDistanceFromScale:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].distanceFromScale.y=i,this.contextEl.putImageData?this._reDrawTickMark():this.initialize())},getTickYDistanceFromScale:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length?this.scaleEl[n].ticks[t].distanceFromScale.y:null},setScaleLocation:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].position.x=t.x,this.scaleEl[n].position.y=t.y,this.initialize())},getScaleLocation:function(n){return n<this.Model.scales.length?{x:this.scaleEl[n].position.x,y:this.scaleEl[n].position.y}:null},setMaximumValue:function(n,t){n<this.Model.scales.length&&t!=null&&(t>this.scaleEl[n].minimum&&(this.scaleEl[n].maximum=t),this.initialize())},getMaximumValue:function(n){return n<this.Model.scales.length?this.scaleEl[this.scaleIndex].maximum:null},setMinimumValue:function(n,t){n<this.Model.scales.length&&t!=null&&(t<this.scaleEl[n].maximum&&(this.scaleEl[n].minimum=t),this.initialize())},getMinimumValue:function(n){return n<this.Model.scales.length?this.scaleEl[this.scaleIndex].minimum:null},setScaleBarSize:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].width=t,this.initialize())},getScaleBarSize:function(n){return n<this.Model.scales.length?this.scaleEl[n].width:null},setScaleBarLength:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].length=t,this.initialize())},setScaleStyle:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].type=t,this.initialize())},getScaleStyle:function(n){return n<this.Model.scales.length?this.scaleEl[n].type:null},getScaleBarLength:function(n){return n<this.Model.scales.length?this.scaleEl[n].length:null},setScaleBorderWidth:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].border.width=t,this.initialize())},setScaleDirection:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].direction=t,this.initialize())},getScaleDirection:function(n){return n<this.Model.scales.length?this.scaleEl[n].direction:null},getScaleBorderWidth:function(n){return n<this.Model.scales.length?this.scaleEl[n].border.width:null},setMajorIntervalValue:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].majorIntervalValue=t,this.initialize())},getMajorIntervalValue:function(n){return n<this.Model.scales.length?this.scaleEl[n].majorIntervalValue:null},setMinorIntervalValue:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].minorIntervalValue=t,this.initialize())},getMinorIntervalValue:function(n){return n<this.Model.scales.length?this.scaleEl[n].minorIntervalValue:null},_reDrawBarPointer:function(){if(this.Model.frame.backgroundImageUrl){var n=t.isNullOrUndefined(this.customLabelImage)?t.isNullOrUndefined(this.rangeImage)?t.isNullOrUndefined(this.labelImage)?t.isNullOrUndefined(this.tickImage)?t.isNullOrUndefined(this.scaleImage)?null:this.scaleImage:this.tickImage:this.labelImage:this.rangeImage:this.customLabelImage();this.contextEl.putImageData(n,0,0);this._setBarPointers();this._setMarkerPointers();this._setIndicators()}else this.contextEl.putImageData!="undefined"&&(this.contextEl.putImageData(this._getBarPointerImage(),0,0),this._setBarPointers(),this._setMarkerPointers(),this._setIndicators())},_reDrawMarkerPointer:function(){if(this.Model.frame.backgroundImageUrl){var n=t.isNullOrUndefined(this.customLabelImage)?t.isNullOrUndefined(this.rangeImage)?t.isNullOrUndefined(this.labelImage)?t.isNullOrUndefined(this.tickImage)?t.isNullOrUndefined(this.scaleImage)?null:this.scaleImage:this.tickImage:this.labelImage:this.rangeImage:this.customLabelImage();this.contextEl.putImageData(n,0,0);this._setMarkerPointers()}else this.contextEl.putImageData!="undefined"&&(this.contextEl.putImageData(this._getMarkerPointerImage(),0,0),this._setMarkerPointers())},_reDrawCustomLabel:function(){if(this.Model.frame.backgroundImageUrl){var n=t.isNullOrUndefined(this.rangeImage)?t.isNullOrUndefined(this.labelImage)?t.isNullOrUndefined(this.tickImage)?t.isNullOrUndefined(this.scaleImage)?null:this.scaleImage:this.tickImage:this.labelImage:this.rangeImage;this.contextEl.putImageData(n,0,0);this._setCustomLabel();this._setIndicators();this._setBarPointers();this._setMarkerPointers()}else this.contextEl.putImageData(this._getCustomLabelImage(),0,0),this._setCustomLabel(),this._setIndicators(),this._setBarPointers(),this._setMarkerPointers()},_reDrawRange:function(){if(this.Model.frame.backgroundImageUrl){var n=t.isNullOrUndefined(this.labelImage)?t.isNullOrUndefined(this.tickImage)?t.isNullOrUndefined(this.scaleImage)?null:this.scaleImage:this.tickImage:this.labelImage;this.contextEl.putImageData(n,0,0);this._setRange();this._setCustomLabel();this._setIndicators();this._setBarPointers();this._setMarkerPointers()}else this.contextEl.putImageData(this._getRangeImage(),0,0),this._setRange(),this._setCustomLabel(),this._setIndicators(),this._setBarPointers(),this._setMarkerPointers()},_reDrawLabel:function(){if(this.Model.frame.backgroundImageUrl){var n=t.isNullOrUndefined(this.tickImage)?t.isNullOrUndefined(this.scaleImage)?null:this.scaleImage:this.tickImage;this.contextEl.putImageData(n,0,0);this._setLabels();this._setRange();this._setCustomLabel();this._setIndicators();this._setBarPointers();this._setMarkerPointers()}else this.contextEl.putImageData(this._getLabelImage(),0,0),this._setLabels(),this._setRange(),this._setCustomLabel(),this._setIndicators(),this._setBarPointers(),this._setMarkerPointers()},_reDrawTickMark:function(){if(this.Model.frame.backgroundImageUrl){var n=t.isNullOrUndefined(this.scaleImage)?null:this.scaleImage;this.contextEl.putImageData(n,0,0);this._setTicks();this._setLabels();this._setRange();this._setCustomLabel();this._setIndicators();this._setBarPointers();this._setMarkerPointers()}else this.contextEl.putImageData(this._getTickImage(),0,0),this._setTicks(),this._setLabels(),this._setRange(),this._setCustomLabel(),this._setIndicators(),this._setBarPointers(),this._setMarkerPointers()},refresh:function(){this._init()},"export":function(){var i=this.model.exportSettings,u,f,e,r,o,s;i.mode.toLowerCase()==="client"?this.exportImage(i.fileName,i.fileType):(f=i.type.toLowerCase()==="jpg"?"image/jpeg":"image/png",u=this.canvasEl[0].toDataURL(f),e={action:i.action,method:"post"},r=t.buildTag("form","",null,e),o={name:"Data",type:"hidden",value:u},s=t.buildTag("input","",null,o),r.append(s).append(this),n("body").append(r),r.submit())},exportImage:function(n,i){var f,u,o,r,c,s;if(t.browserInfo().name==="msie"&&parseFloat(t.browserInfo().version)<10)return!1;f=this.canvasEl[0].toDataURL();f=f.replace(/^data:[a-z]*;,/,"");var l=f.split(","),e=atob(l[1]),h=new ArrayBuffer(e.length),a=new Uint8Array(h);for(u=0;u<e.length;u++)a[u]=e.charCodeAt(u);return o=new Blob([h],{type:"image/png"}),t.browserInfo().name==="msie"?window.navigator.msSaveOrOpenBlob(o,n+"."+i):(r=document.createElement("a"),c=URL.createObjectURL(o),r.href=c,r.setAttribute("download",n+"."+i),document.createEvent?(s=document.createEvent("MouseEvents"),s.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),r.dispatchEvent(s)):r.fireEvent&&r.fireEvent("onclick")),!0},resizeCanvas:function(){var o,s,e,i,f;if(r=r!=0?r-1:n(".e-lineargauge").length-1,s=!0,t.isNullOrUndefined(this.GaugeEl.parent().attr("style"))||(o=this.GaugeEl.parent().attr("style").split(";")),t.isNullOrUndefined(o)||n.each(o,function(n,t){while(t.indexOf("width")!=-1){s=t.indexOf("px")==-1?!0:!1;break}}),s){for(e=window.innerWidth/u,this.model.width*=e,i=0;this.model.scales[i]!=null;i++)for(this.model.scales[i].length*=e,f=0;this.model.scales[i].markerPointers[f]!=null||this.model.scales[i].barPointers[f]!=null||this.model.scales[i].indicators[f]!=null||this.model.scales[i].customLabels[f]!=null||this.model.scales[i].ranges[f]!=null||this.model.scales[i].labels[f]!=null||this.model.scales[i].ticks[f]!=null;f++)t.isNullOrUndefined(this.model.scales[i].markerPointers[f])||(this.model.scales[i].markerPointers[f].length*=e,this.model.scales[i].markerPointers[f].width*=e),t.isNullOrUndefined(this.model.scales[i].barPointers[f])||(this.model.scales[i].barPointers[f].distanceFromScale*=e,this.model.scales[i].barPointers[f].width*=e),!t.isNullOrUndefined(this.model.scales[i].indicators[f])&&this.model.scales[i].showIndicators&&(this.model.scales[i].indicators[f].height*=e,this.model.scales[i].indicators[f].width*=e,this.model.scales[i].indicators[f].position.x*=e,this.model.scales[i].indicators[f].textLocation.x*=e),t.isNullOrUndefined(this.model.scales[i].ticks[f])||(this.model.scales[i].ticks[f].length*=e,this.model.scales[i].ticks[f].width*=e),t.isNullOrUndefined(this.model.scales[i].ranges[f])||(this.model.scales[i].ranges[f].startWidth*=e,this.model.scales[i].ranges[f].endWidth*=e),t.isNullOrUndefined(this.model.scales[i].customLabels[f])||(this.model.scales[i].customLabels[f].positionType!="outer"&&(this.model.scales[i].customLabels[f].position.x*=e),this.model.scales[i].customLabels[f].font.size=parseFloat(this.model.scales[i].customLabels[f].font.size.match(/\d+/)[0])*e<10?"10px":parseFloat(this.model.scales[i].customLabels[f].font.size.match(/\d+/)[0])*e>this._customLblMaxSize?this._customLblMaxSize.toString()+"px":(parseFloat(this.model.scales[i].customLabels[f].font.size.match(/\d+/)[0])*e).toString()+"px");this._render();r==0&&(u=window.innerWidth)}},_onDrawTicks:function(n,t){var r={index:this.tickIndex,element:this.tickEl[this.tickIndex],angle:parseInt(n),value:t},i={Object:this,Model:this.Model,scaleElement:this.Model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region};this._trigger("drawTicks",i)},_onDrawLabels:function(n){var t={index:this.labelIndex,element:this.labelEl[this.labelIndex],angle:parseInt(n),value:this.labelValue},i={object:this,Model:this.Model,scaleElement:this.Model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region,label:t};this._trigger("drawLabels",i)},_onDrawBarPointers:function(n){var t={object:this,Model:this.Model,scaleElement:this.Model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,barPointerIndex:this.barPointerIndex,barElement:this.barPointerEl[this.barPointerIndex],context:this.contextEl,style:this.style,position:this.region,pointerValue:n};this._trigger("drawBarPointers",t)},_onDrawMarkerPointers:function(n,t){var i={object:this,Model:this.Model,scaleElement:this.Model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,markerPointerIndex:this.markerPointerIndex,markerElement:this.markerPointerEl[this.markerPointerIndex],context:this.contextEl,style:this.style,position:this.region,pointerValue:t,pointerAngle:parseInt(n)};this._trigger("drawMarkerPointers",i)},_onDrawRange:function(){var n={object:this,Model:this.Model,scaleElement:this.Model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,rangeIndex:this.rangeIndex,rangeElement:this.rangeEl[this.rangeIndex],context:this.contextEl,style:this.style,position:this.region};this._trigger("drawRange",n)},_onDrawCustomLabel:function(){var n={object:this,Model:this.Model,scaleElement:this.Model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,customLabelIndex:this.customLabelIndex,customLabelElement:this.customLabelEl[this.customLabelIndex],context:this.contextEl,style:this.style,position:this.region};this._trigger("drawCustomLabel",n)},_onDrawIndicators:function(){var n={object:this,Model:this.Model,scaleElement:this.Model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,indicatorIndex:this.indicatorIndex,indicatorEl:this.indicatorEl[this.indicatorIndex],context:this.contextEl,style:this.style,position:this.region};this._trigger("drawIndicators",n)},onLoad:function(){var n={object:this,Model:this.Model,scaleElement:this.Model.scales,context:this.contextEl};this._trigger("load",n)},_onInit:function(){var n={object:this,Model:this.Model,scaleElement:this.Model.scales,context:this.contextEl};this._trigger("init",n)},_onRenderComplete:function(){var n={object:this,Model:this.Model,scaleElement:this.Model.scales,context:this.contextEl};this._trigger("renderComplete",n)},_onMouseClick:function(n){var t={index:this.markerPointerIndex,element:this.markerPointerEl[this.markerPointerIndex],value:n},i={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region,markerPointer:t};this._trigger("mouseClick",i)},_onMouseClickMove:function(n){var t={index:this.markerPointerIndex,element:this.markerPointerEl[this.markerPointerIndex],value:n},i={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region,markerPointer:t};this._trigger("mouseClickMove",i)},_onMouseClickUp:function(n){var t={index:this.markerPointerIndex,element:this.markerPointerEl[this.markerPointerIndex],value:n},i={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region,markerPointer:t};this._trigger("mouseClickUp",i)},_restoreWidth:function(){this.scaleEl[this.scaleIndex].length=this.scaleEl[this.scaleIndex].length+this.bottomRadius+this.scaleEl[this.scaleIndex].width;this.Model.orientation=="Vertical"?this.scaleStartY[this.scaleIndex]=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this.scaleStartY[this.scaleIndex]-this.bottomRadius-this.scaleEl[this.scaleIndex].width/2:this.scaleStartY[this.scaleIndex]-this.scaleEl[this.scaleIndex].width/2:this.scaleStartX[this.scaleIndex]=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this.scaleStartX[this.scaleIndex]-this.bottomRadius-this.scaleEl[this.scaleIndex].width/2:this.scaleStartX[this.scaleIndex]-this.scaleEl[this.scaleIndex].width/2},_modifyWidth:function(){this.scaleEl[this.scaleIndex].length=this.scaleEl[this.scaleIndex].length-this.bottomRadius-this.scaleEl[this.scaleIndex].width;this.Model.orientation=="Vertical"?this.scaleStartY[this.scaleIndex]=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this.scaleStartY[this.scaleIndex]+this.bottomRadius+this.scaleEl[this.scaleIndex].width/2:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2:this.scaleStartX[this.scaleIndex]=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this.scaleStartX[this.scaleIndex]+this.bottomRadius+this.scaleEl[this.scaleIndex].width/2:this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2},_getClockwiseLinePosition:function(n){var t;return t=(n-this.scaleEl[this.scaleIndex].minimum)/(this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum)*100,this.Model.orientation=="Vertical"?this.scaleStartY[this.scaleIndex]+parseInt(t*this.scaleEl[this.scaleIndex].length/100):this.scaleStartX[this.scaleIndex]+parseInt(t*this.scaleEl[this.scaleIndex].length/100)},_getCounterClockwiseLinePosition:function(n){var t;return t=this.scaleEl[this.scaleIndex].maximum-n+this.scaleEl[this.scaleIndex].minimum,t=(t-this.scaleEl[this.scaleIndex].minimum)/(this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum)*100,this.Model.orientation=="Vertical"?this.scaleStartY[this.scaleIndex]+parseInt(t*this.scaleEl[this.scaleIndex].length/100):this.scaleStartX[this.scaleIndex]+parseInt(t*this.scaleEl[this.scaleIndex].length/100)},_getValue:function(n){var i,t;return i=this.Model.orientation=="Vertical"?(n.y-this.scaleStartY[this.scaleIndex])/this.scaleEl[this.scaleIndex].length*100:(n.x-this.scaleStartX[this.scaleIndex])/this.scaleEl[this.scaleIndex].length*100,t=(i*(this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum)+this.scaleEl[this.scaleIndex].minimum)/100,this.scaleEl[this.scaleIndex].direction=="counterclockwise"?this.scaleEl[this.scaleIndex].maximum-t:this.scaleEl[this.scaleIndex].minimum+t},_getPointerXPosition:function(n){var t,i;return this.Model.orientation=="Vertical"?(this.markerPlacement=="far"&&(t=this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width+this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale,i=0),this.markerPlacement=="near"&&(t=this.scaleStartX[this.scaleIndex]+n.distanceFromScale,i=180),this.markerPlacement=="center"&&(t=n.type=="circle"?this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-Math.sqrt(n.length*n.length+n.width*n.width)/2+n.distanceFromScale:this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2+n.distanceFromScale,i=0)):(this.markerPlacement=="far"&&(t=this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width+this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale,i=90),this.markerPlacement=="near"&&(t=this.scaleStartY[this.scaleIndex]-this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale,i=270),this.markerPlacement=="center"&&(t=n.type=="circle"?this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-Math.sqrt(n.length*n.length+n.width*n.width)/2+n.distanceFromScale:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.length/2+n.distanceFromScale,i=90)),{startx:t,angle:i}},_hexFromRGB:function(t,i,r){var u=[t.toString(16),i.toString(16),r.toString(16)];return n.each(u,function(n,t){t.length===1&&(u[n]="0"+t)}),u.join("").toUpperCase()},_setGradientColor:function(t,i,r){r.Name||typeof r=="string"?(i.addColorStop(0,r),i.addColorStop(1,r)):n.each(r,function(n,t){i.addColorStop(t.colorStop!=NaN?t.colorStop:0,typeof t.color=="string"?t.color:t.color)})},_getFontString:function(n,t){return t.fontStyle+" "+(t.size==null?"11px":t.size)+" "+t.fontFamily},_setPointerDimension:function(n,t){if(t.Model.orientation&&t.Model.orientation=="horizontal"){var i=n.width,r=n.height;n.height=i;n.width=r}return n},_setContextRotation:function(n,t){t.contextEl.rotate(Math.PI*(n.angle/180))},_browserInfo:function(){var n={},t=[],i={webkit:/(chrome)[ \/]([\w.]+)/i,safari:/(webkit)[ \/]([\w.]+)/i,msie:/(msie) ([\w.]+)/i,opera:/(opera)(?:.*version|)[ \/]([\w.]+)/i,mozilla:/(mozilla)(?:.*? rv:([\w.]+)|)/i};for(var r in i)if(i.hasOwnProperty(r)&&(t=navigator.userAgent.match(i[r]),t)){n.name=t[1].toLowerCase();n.version=t[2];!navigator.userAgent.match(/Trident\/7\./)||(n.name="msie");break}return n.isMSPointerEnabled=n.name=="msie"&&n.version>9&&window.navigator.msPointerEnabled,n.pointerEnabled=window.navigator.pointerEnabled,n},wireEvents:function(){var u;n(this.canvasEl).off();var f=jQuery.uaMatch(navigator.userAgent),r=this._browserInfo(),i=r.isMSPointerEnabled,t=r.pointerEnabled;this.startEv=i?t?"pointerdown":"MSPointerDown":"touchstart mousedown";this.endEv=i?t?"pointerup":"MSPointerUp":"touchend mouseup";this.moveEv=i?t?"pointermove":"MSPointerMove":"touchmove mousemove";this.leaveEv=i?t?"pointerleave":"MSPointerOut":"touchleave mouseleave";this.scrollEv=f.browser.toLowerCase()=="mozilla"?t?"mousewheel":"DOMMouseScroll":"mousewheel";this.model.browserInfo=r;u=this.model.readOnly?"pan-y pan-x":"none";n(this.element).css("touch-action",u);this.onMouseMoveHandler=n.proxy(this._onMouseMove,this);this.onMouseUpHandler=n.proxy(this._onMouseUp,this);this.onHoverOCustomLabel=n.proxy(this._onHoverOCustomLabel,this);this.onLeaveOCustomLabel=n.proxy(this._onLeaveOCustomLabel,this);this.model.readOnly||(this.onMouseDownHandler=n.proxy(this._onMouseDown,this),this._on(n(this.element),this.startEv,this._onMouseDown));this._on(n(this.canvasEl),this.startEv,this.lgMouseDown);this._on(n(this.canvasEl),this.endEv,this.lgMouseUp);(this.model.tooltip.showCustomLabelTooltip||this.model.tooltip.showLabelTooltip)&&(n(this.canvasEl).bind(this.moveEv,this.onMouseMoveHandler),n(this.canvasEl).bind(this.scrollEv,this.onMouseMoveHandler),n(this.canvasEl).bind(this.startEv,this.onMouseDownHandler),n(this.canvasEl).bind(this.endEv,this.onLeaveOCustomLabel),n(this.canvasEl).bind(this.leaveEv,this.onLeaveOCustomLabel));this.element.bind(this.startEv,this.onMouseDownHandler);this.model.tooltip.showCustomLabelTooltip&&(n("."+this._id+"outercustomlbl").bind("mouseenter",this.onHoverOCustomLabel),n("."+this._id+"outercustomlbl").bind(this.leaveEv,this.onLeaveOCustomLabel));this._on(n(this.canvasEl),"contextmenu",this._lgRightClick)},unWireEvents:function(){this.element.unbind(this.startEv,this.onMouseDownHandler);this._off(n(this.canvasEl),"contextmenu",this._lgRightClick);this._off(n(this.canvasEl),this.startEv,this.lgMouseDown);this._off(n(this.canvasEl),this.endEv,this.lgMouseUp)},lgMouseDown:function(){t.isTouchDevice()&&this.model.rightClick!=""&&(this._longPressTimer=new Date)},lgMouseUp:function(n){var i=new Date;this._doubleTapTimer!=null&&i-this._doubleTapTimer<300&&this._lgDoubleClick(n);this._doubleTapTimer=i;t.isTouchDevice()&&this.model.rightClick!=""&&new Date-this._longPressTimer>1500&&this._lgRightClick(n)},_onHoverOCustomLabel:function(n){(n.currentTarget.innerHTML!=null||n.currentTarget.innerHTML!="")&&this._showTooltip(n,n.currentTarget.innerHTML)},_onLeaveOCustomLabel:function(t){this.isTouch(t)?(this._performTooltip(t),window.clearTimeout(this.model.timer),this.model.timer=setTimeout(function(){n(".tooltipDiv").fadeOut(500)},1200)):this._hideTooltip()},isTouch:function(n){var t=n.originalEvent?n.originalEvent:n;return t.pointerType=="touch"||t.pointerType==2||t.type.indexOf("touch")>-1?!0:!1},_blockDefaultActions:function(n){n.cancelBubble=!0;n.returnValue=!1;n.preventDefault&&n.preventDefault();n.stopPropagation&&n.stopPropagation()},_onMouseDown:function(t){var r,u,e,o,h,f,s,i;this._blockDefaultActions(t);this._mouseDown=!0;f=this.isTouch(t)?10:0;s=t.originalEvent.touches?t.originalEvent.touches[0]:t;r={x:s.pageX-n(this.canvasEl).offset().left-(this.Model.frame.outerWidth+this.Model.frame.innerWidth),y:s.pageY-n(this.canvasEl).offset().top-(this.Model.frame.outerWidth+this.Model.frame.innerWidth)};i=this;this.model.readOnly||n.each(this.Model.scales,function(t,s){i.scaleIndex=t;s.markerPointers!=null&&(i.markerPointerEl=s.markerPointers,n.each(s.markerPointers,function(t,s){u=i.scaleEl[i.scaleIndex].direction.toLowerCase()=="clockwise"?i._getClockwiseLinePosition(s.value):i._getCounterClockwiseLinePosition(s.value);e=u+s.width;o=u-s.width;h=i._getPointerXPosition(s).startx;var c=i._isBetween((i.Model.orientation=="horizontal"?r.y:r.x)-s.width,(i.Model.orientation=="horizontal"?r.y:r.x)+s.width,h,f);(i.Model.orientation=="horizontal"?i._isBetween(o,e,r.x,f):i._isBetween(o,e,r.y,f))&&c&&(i.activeElement=s);i.Model.scales[i.scaleIndex].barPointers[t]!=null&&(i.activeBarElement=i.Model.scales[i.scaleIndex].barPointers[t]);i.model.mouseClick&&i._onMouseClick(s.value);i.onMouseMoveHandler=n.proxy(i._onMouseMove,i);i.onMouseUpHandler=n.proxy(i._onMouseUp,i);n(document).bind(i.moveEv,i.onMouseMoveHandler);n(document).bind(i.endEv,i.onMouseUpHandler)}))})},_isBetween:function(n,t,i,r){return n<t?i>=n-r&&i<=t+r:i>=t-r&&i<=n+r},_lgDoubleClick:function(n){this.model.doubleClick!=""&&this._trigger("doubleClick",{data:{event:n}})},_lgRightClick:function(n){this.model.rightClick!=""&&this._trigger("rightClick",{data:{event:n}})},_onMouseUp:function(){this._mouseDown=!1;this.mouseMove=!1;n(document).unbind(self.moveEv,self.onMouseMoveHandler);n(document).unbind(self.endEv,self.onMouseUpHandler);this.model.mouseClickUp&&this.activeElement&&this._onMouseClickUp(this.activeElement.value);this.activeElement=null},_mousePosition:function(n){if(!t.util.isNullOrUndefined(n.pageX)&&n.pageX>0)return{x:n.pageX,y:n.pageY};if(n.originalEvent&&!t.util.isNullOrUndefined(n.originalEvent.pageX)&&n.originalEvent.pageX>0)return{x:n.originalEvent.pageX,y:n.originalEvent.pageY};if(n.originalEvent&&n.originalEvent.changedTouches!=i){if(!t.util.isNullOrUndefined(n.originalEvent.changedTouches[0].pageX)&&n.originalEvent.changedTouches[0].pageX>0)return{x:n.originalEvent.changedTouches[0].pageX,y:n.originalEvent.changedTouches[0].pageY}}else return{x:0,y:0}},_calTouchPosition:function(n){var i=jQuery.uaMatch(navigator.userAgent),t=this._mousePosition(n);n.pageX=t.x;n.pageY=t.y},getEvent:function(n){return n.targetTouches&&n.targetTouches[0]?n.targetTouches[0]:n},_onMouseMove:function(i){if(this._mouseDown&&!t.isNullOrUndefined(this.activeElement)){this._blockDefaultActions(i);var r=i.originalEvent.touches?i.originalEvent.touches[0]:i,u={x:r.pageX-n(this.canvasEl).offset().left-(this.Model.frame.outerWidth+this.Model.frame.innerWidth),y:r.pageY-n(this.canvasEl).offset().top-(this.Model.frame.outerWidth+this.Model.frame.innerWidth)};this.activeElement.value=this._getValue(u);this.value(this.activeElement.value);this.model.mouseClickMove&&this._onMouseClickMove(this.activeElement.value);this.activeBarElement&&(this.activeBarElement.value=this._getValue(u));this.contextEl.putImageData?this._reDrawBarPointer():this._init()}else(this.model.tooltip.showCustomLabelTooltip||this.model.tooltip.showLabelTooltip)&&!this.isTouch(i)&&this._performTooltip(i)},_performTooltip:function(t){for(var r,u=!1,f=10,o=this.isTouch(t),i=0;this._savedPoints[i]!=null;i++)if(o){var c=this._calTouchPosition(t),e=this.getEvent(t),s=e.pageX,h=e.pageY,r={X:s-n(this.canvasEl).offset().left,Y:h-n(this.canvasEl).offset().top};r.X>this._savedPoints[i].startX-f&&r.X<this._savedPoints[i].startX+this._savedPoints[i].width+f&&r.Y>this._savedPoints[i].startY-f&&r.Y<this._savedPoints[i].startY+this._savedPoints[i].height+f?(this._showTooltip(t,this._savedPoints[i].value),u=!0):u==!1&&this._hideTooltip()}else r={X:t.pageX-n(this.canvasEl).offset().left,Y:t.pageY-n(this.canvasEl).offset().top},r.X>this._savedPoints[i].startX&&r.X<this._savedPoints[i].startX+this._savedPoints[i].width&&r.Y>this._savedPoints[i].startY&&r.Y<this._savedPoints[i].startY+this._savedPoints[i].height?(this._showTooltip(t,this._savedPoints[i].value),u=!0):u==!1&&this._hideTooltip()},_showTooltip:function(t,i){var e=i+"",r=n(".tooltipDiv"),o;r.length==0&&(r=n("<div class='tooltipDiv' style='position: absolute; z-index: 105; display: block;'><\/div>"),n(document.body).append(r));this.model.tooltip.templateID!=""&&this.model.tooltip.templateID!=null?(o=n("#"+this.model.tooltip.templateID).clone(),n(".tooltipDiv")[0].innerHTML="",n(o).css("display","block").appendTo(r),n(r).css({"background-color":this.model.backgroundColor,border:"1px solid #bbbcbb","border-radius":"3px",color:"#565656"}),r.html(r.html().replace("#label#",e))):(n(r).html(e),n(r).css({"background-color":"white",border:"2px solid #bbbcbb",position:"absolute",padding:"10px 20px","margin-top":"5px","text-align":"left",font:"12px Segoe UI","font-stretch":"condensed",display:"inline-block","border-radius":"3px",color:"#565656",width:"auto"}));var s=10,u=t.pageX+s,f=t.pageY+s;u=u+n(r).width()<n(window).width()?u:u-n(r).width();f=f+n(r).height()<n(window).height()?f:f-n(r).height();n(r).css("left",u);n(r).css("top",f);n(".tooltipDiv").show()},_hideTooltip:function(){n(".tooltipDiv").remove()},_setTheme:function(){var n=this.model.theme.toLowerCase(),t=this.model.themeProperties[n];this._setThemeColors(t)},_setThemeColors:function(n){var f=[],e=this.model.themeProperties,o,i,t,r,u;for(o in e)f.push(o);for(i=0;i<f.length;i++)for(this.model.backgroundColor=!this.model.backgroundColor||this.model.backgroundColor==e[f[i]].scales.backgroundColor?n.scales.backgroundColor:this.model.backgroundColor,this.model.borderColor=!this.model.borderColor||this.model.borderColor==e[f[i]].scales.border.color?n.scales.border.color:this.model.borderColor,this.model.labelColor=!this.model.labelColor||this.model.labelColor==e[f[i]].scales.labels.labelColor?n.scales.labels.labelColor:this.model.labelColor,this.model.tickColor=!this.model.tickColor||this.model.tickColor==e[f[i]].scales.ticks.color?n.scales.ticks.color:this.model.tickColor,t=0;t<this.model.scales.length;t++){for(r=0;r<this.model.scales[t].markerPointers.length;r++)this.model.scales[t].markerPointers[r].backgroundColor=!this.model.scales[t].markerPointers[r].backgroundColor||this.model.scales[t].markerPointers[r].backgroundColor==e[f[i]].scales.markerPointers.backgroundColor?n.scales.markerPointers.backgroundColor:this.model.scales[t].markerPointers[r].backgroundColor,this.model.scales[t].markerPointers[r].border.color=!this.model.scales[t].markerPointers[r].border.color||this.model.scales[t].markerPointers[r].border.color==e[f[i]].scales.markerPointers.border.color?n.scales.markerPointers.border.color:this.model.scales[t].markerPointers[r].border.color;for(u=0;u<this.model.scales[t].barPointers.length;u++)this.model.scales[t].barPointers[u].backgroundColor=!this.model.scales[t].barPointers[u].backgroundColor||this.model.scales[t].barPointers[u].backgroundColor==e[f[i]].scales.barPointers.backgroundColor?n.scales.barPointers.backgroundColor:this.model.scales[t].barPointers[u].backgroundColor,this.model.scales[t].barPointers[u].border.color=!this.model.scales[t].barPointers[u].border.color||this.model.scales[t].barPointers[u].border.color==e[f[i]].scales.barPointers.border.color?n.scales.barPointers.border.color:this.model.scales[t].barPointers[u].border.color}}});t.datavisualization.LinearGauge.TickType={MajorInterval:"majorinterval",MinorInterval:"minorinterval"};t.datavisualization.LinearGauge.LabelType={Major:"major",Minor:"minor"};t.datavisualization.LinearGauge.FontStyle={Bold:"bold",Italic:"italic",Regular:"regular",Strikeout:"strikeout",Underline:"underline"};t.datavisualization.LinearGauge.PointerPlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.LinearGauge.TickPlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.LinearGauge.LabelPlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.LinearGauge.RangePlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.LinearGauge.UnitTextPlacement={Front:"front",Back:"back"};t.datavisualization.LinearGauge.Directions={Clockwise:"clockwise",CounterClockwise:"counterclockwise"};t.datavisualization.LinearGauge.ScaleType={Rectangle:"rectangle",RoundedRectangle:"roundedrectangle",Thermometer:"thermometer"};t.datavisualization.LinearGauge.IndicatorType={Rectangle:"rectangle",Circle:"circle",RoundedRectangle:"roundedrectangle",Text:"text"};t.datavisualization.LinearGauge.MarkerType={Rectangle:"rectangle",Triangle:"triangle",Ellipse:"ellipse",Diamond:"diamond",Pentagon:"pentagon",Circle:"circle",Star:"star",Slider:"slider",Pointer:"pointer",Wedge:"wedge",Trapezoid:"trapezoid",RoundedRectangle:"roundedrectangle"};t.datavisualization.LinearGauge.CustomLabelPositionType={Inner:"inner",Outer:"outer"};t.datavisualization.LinearGauge.OuterCustomLabelPosition={Left:"left",Right:"right",Top:"top",Bottom:"bottom"};t.datavisualization.LinearGauge.Themes={FlatLight:"flatlight",FlatDark:"flatdark"}})(jQuery,Syncfusion)});
