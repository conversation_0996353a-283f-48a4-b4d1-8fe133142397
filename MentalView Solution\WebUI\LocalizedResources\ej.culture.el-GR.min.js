/*!
*  filename: ej.culture.fr-FR.min.js
*  version : 18.1.0.42
*  Copyright Syncfusion Inc. 2001 - 2020. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/

ej.addCulture("el-GR", {
    name: "el-GR",
    englishName: "Greek (Greece)",
    nativeName: "Ελληνικά (Ελλάδα)",
    language: "el",
    numberFormat: {
        pattern: ["-n"], ",": " ", ".": ",", groupSizes: [3], NaN: "Non numérique",
        negativeInfinity: "-Άπειρο",
        positiveInfinity: "+Άπειρο",
        percent: { pattern: ["-n%", "n%"], groupSizes: [3], ",": " ", ".": ",", symbol: "%" },
        currency: { pattern: ["-n €", "n €"], groupSizes: [3], ",": " ", ".": ",", symbol: "€" }
    },
    calendars: {
        standard: {
            "/": "/", ":": ":",
            firstDay: 1,
            days: {
                names: ["Κυριακή", "Δευτέρα", "Τρίτη", "Τετάρτη", "Πέμπτη", "Παρασκευή", "Σάββατο"],
                namesAbbr: ["Κυρ.", "Δευ.", "Τρι.", "Τετ.", "Πεμ.", "Παρ.", "Σαβ."],
                namesShort: ["Κυ", "Δε", "Τρ", "Τε", "Πε", "Πα", "Σα"]
            }, months: {
                names: ["Ιανουάριος", "Φεβρουάριος", "Μάρτιος", "Απρίλιος", "Μάιος", "Ιούνιος", "Ιούλιος", "Αύγουστος", "Σεπτέμβριος", "Οκτώβριος", "Νοέμβριος", "Δεκέμβριος", ""],
                namesAbbr: ["Ιαν.", "Φεβ.", "Μαρτ.", "Απρ.", "Μάι.", "Ιούν.", "Ιούλ.", "Αύγ.", "Σεπ.", "Οκτ.", "Νοεμ.", "Δεκ.", ""]
            }, AM: null, PM: null, eras: [{ name: "ap. J.-C.", start: null, offset: 0 }], patterns: { d: "dd/MM/yyyy", D: "dddd d MMMM yyyy", t: "HH:mm", T: "HH:mm:ss", f: "dddd d MMMM yyyy HH:mm", F: "dddd d MMMM yyyy HH:mm:ss", M: "d MMMM" }
        }
    }
   
});