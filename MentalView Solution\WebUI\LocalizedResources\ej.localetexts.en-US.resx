﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Autocomplete_addNewText" xml:space="preserve">
    <value>Add New</value>
  </data>
  <data name="Autocomplete_emptyResultText" xml:space="preserve">
    <value>No suggestions</value>
  </data>
  <data name="Autocomplete_actionFailure" xml:space="preserve">
    <value>The specified field doesn't exist in the given data source</value>
  </data>
  <data name="Autocomplete_watermarkText" xml:space="preserve">
    <value> </value>
  </data>
  <data name="ColorPicker_buttonText_apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="ColorPicker_buttonText_cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="ColorPicker_buttonText_swatches" xml:space="preserve">
    <value>Swatches</value>
  </data>
  <data name="ColorPicker_tooltipText_switcher" xml:space="preserve">
    <value>Switcher</value>
  </data>
  <data name="ColorPicker_tooltipText_addButton" xml:space="preserve">
    <value>Add color</value>
  </data>
  <data name="ColorPicker_tooltipText_basic" xml:space="preserve">
    <value>Basic</value>
  </data>
  <data name="ColorPicker_tooltipText_monoChrome" xml:space="preserve">
    <value>Mono chrome</value>
  </data>
  <data name="ColorPicker_tooltipText_flatColors" xml:space="preserve">
    <value>Flat colors</value>
  </data>
  <data name="ColorPicker_tooltipText_seaWolf" xml:space="preserve">
    <value>Sea wolf</value>
  </data>
  <data name="ColorPicker_tooltipText_webColors" xml:space="preserve">
    <value>Web colors</value>
  </data>
  <data name="ColorPicker_tooltipText_sandy" xml:space="preserve">
    <value>Sandy</value>
  </data>
  <data name="ColorPicker_tooltipText_pinkShades" xml:space="preserve">
    <value>Pink shades</value>
  </data>
  <data name="ColorPicker_tooltipText_misty" xml:space="preserve">
    <value>Misty</value>
  </data>
  <data name="ColorPicker_tooltipText_citrus" xml:space="preserve">
    <value>Citrus</value>
  </data>
  <data name="ColorPicker_tooltipText_vintage" xml:space="preserve">
    <value>Vintage</value>
  </data>
  <data name="ColorPicker_tooltipText_moonLight" xml:space="preserve">
    <value>Moon light</value>
  </data>
  <data name="ColorPicker_tooltipText_candyCrush" xml:space="preserve">
    <value>Candy crush</value>
  </data>
  <data name="ColorPicker_tooltipText_currentColor" xml:space="preserve">
    <value>Current color</value>
  </data>
  <data name="ColorPicker_tooltipText_selectedColor" xml:space="preserve">
    <value>Selected color</value>
  </data>
  <data name="DatePicker_watermarkText" xml:space="preserve">
    <value>Select date</value>
  </data>
  <data name="DatePicker_buttonText" xml:space="preserve">
    <value>Today</value>
  </data>
  <data name="DateRangePicker_ButtonText_apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="DateRangePicker_ButtonText_cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="DateRangePicker_ButtonText_reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="DateRangePicker_watermarkText" xml:space="preserve">
    <value>Select range</value>
  </data>
  <data name="DateRangePicker_customPicker" xml:space="preserve">
    <value>Custom picker</value>
  </data>
  <data name="DateTimePicker_watermarkText" xml:space="preserve">
    <value>Select datetime</value>
  </data>
  <data name="DateTimePicker_buttonText_today" xml:space="preserve">
    <value>Today</value>
  </data>
  <data name="DateTimePicker_buttonText_timeNow" xml:space="preserve">
    <value>Time Now</value>
  </data>
  <data name="DateTimePicker_buttonText_done" xml:space="preserve">
    <value>Done</value>
  </data>
  <data name="DateTimePicker_buttonText_timeTitle" xml:space="preserve">
    <value>Time</value>
  </data>
  <data name="Dialog_tooltip_close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Dialog_tooltip_collapse" xml:space="preserve">
    <value>Collapse</value>
  </data>
  <data name="Dialog_tooltip_restore" xml:space="preserve">
    <value>Restore</value>
  </data>
  <data name="Dialog_tooltip_maximize" xml:space="preserve">
    <value>Maximize</value>
  </data>
  <data name="Dialog_tooltip_minimize" xml:space="preserve">
    <value>Minimize</value>
  </data>
  <data name="Dialog_tooltip_expand" xml:space="preserve">
    <value>Expand</value>
  </data>
  <data name="Dialog_tooltip_unPin" xml:space="preserve">
    <value>UnPin</value>
  </data>
  <data name="Dialog_tooltip_pin" xml:space="preserve">
    <value>Pin</value>
  </data>
  <data name="Dialog_closeIconTooltip" xml:space="preserve">
    <value>close</value>
  </data>
  <data name="DropDownList_emptyResultText" xml:space="preserve">
    <value>No suggestions,</value>
  </data>
  <data name="DropDownList_watermarkText" xml:space="preserve">
    <value> </value>
  </data>
  <data name="ComboBox_noRecordsTemplate" xml:space="preserve">
    <value>No Records Found</value>
  </data>
  <data name="ComboBox_actionFailureTemplate" xml:space="preserve">
    <value>The Request Failed</value>
  </data>
  <data name="Menu_titleText" xml:space="preserve">
    <value>Menu</value>
  </data>
  <data name="Captcha_placeHolderText" xml:space="preserve">
    <value>Type the code shown</value>
  </data>
  <data name="Captcha_CustomErrorMessage" xml:space="preserve">
    <value>Invalid Captcha</value>
  </data>
  <data name="NumericTextbox_watermarkText" xml:space="preserve">
    <value>Enter value</value>
  </data>
  <data name="PercentageTextbox_watermarkText" xml:space="preserve">
    <value>Enter value</value>
  </data>
  <data name="CurrencyTextbox_watermarkText" xml:space="preserve">
    <value>Enter value</value>
  </data>
  <data name="ExcelFilter_SortNoSmaller" xml:space="preserve">
    <value>Sort Smallest to Largest</value>
  </data>
  <data name="ExcelFilter_SortNoLarger" xml:space="preserve">
    <value>Sort Largest to Smallest</value>
  </data>
  <data name="ExcelFilter_SortTextAscending" xml:space="preserve">
    <value>Sort A to Z</value>
  </data>
  <data name="ExcelFilter_SortTextDescending" xml:space="preserve">
    <value>Sort Z to A</value>
  </data>
  <data name="ExcelFilter_SortDateOldest" xml:space="preserve">
    <value>Sort by Oldest</value>
  </data>
  <data name="ExcelFilter_SortDateNewest" xml:space="preserve">
    <value>Sort by Newest</value>
  </data>
  <data name="ExcelFilter_SortByColor" xml:space="preserve">
    <value>Sort By Color</value>
  </data>
  <data name="ExcelFilter_SortByCellColor" xml:space="preserve">
    <value>Sort by Cell Color</value>
  </data>
  <data name="ExcelFilter_SortByFontColor" xml:space="preserve">
    <value>Sort by Font Color</value>
  </data>
  <data name="ExcelFilter_FilterByColor" xml:space="preserve">
    <value>Filter By Color</value>
  </data>
  <data name="ExcelFilter_CustomSort" xml:space="preserve">
    <value>Custom Sort</value>
  </data>
  <data name="ExcelFilter_FilterByCellColor" xml:space="preserve">
    <value>Filter by Cell Color</value>
  </data>
  <data name="ExcelFilter_FilterByFontColor" xml:space="preserve">
    <value>Filter by Font Color</value>
  </data>
  <data name="ExcelFilter_ClearFilter" xml:space="preserve">
    <value>Clear Filter</value>
  </data>
  <data name="ExcelFilter_NumberFilter" xml:space="preserve">
    <value>Number Filters</value>
  </data>
  <data name="ExcelFilter_GuidFilter" xml:space="preserve">
    <value>Guid Filters</value>
  </data>
  <data name="ExcelFilter_TextFilter" xml:space="preserve">
    <value>Text Filters</value>
  </data>
  <data name="ExcelFilter_DateFilter" xml:space="preserve">
    <value>Date Filters</value>
  </data>
  <data name="ExcelFilter_DateTimeFilter" xml:space="preserve">
    <value>Date Time Filters</value>
  </data>
  <data name="ExcelFilter_SelectAll" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="ExcelFilter_Blanks" xml:space="preserve">
    <value>Blanks</value>
  </data>
  <data name="ExcelFilter_Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="ExcelFilter_Showrowswhere" xml:space="preserve">
    <value>Show rows where</value>
  </data>
  <data name="ExcelFilter_NumericTextboxWaterMark" xml:space="preserve">
    <value>Enter value</value>
  </data>
  <data name="ExcelFilter_StringMenuOptions" xml:space="preserve">
    <value> [{ text: "Equal", value: "equal" }, { text: "Not Equal", value: "notequal" }, { text: "Starts With", value: "startswith" }, { text: "Ends With", value: "endswith" }, { text: "Contains", value: "contains" }, { text: "Custom Filter", value: "customfilter" }],</value>
  </data>
  <data name="ExcelFilter_NumberMenuOptions" xml:space="preserve">
    <value> [{ text: "Equal", value: "equal" }, { text: "Not Equal", value: "notequal" }, { text: "Less Than", value: "lessthan" }, { text: "Less Than Or Equal", value: "lessthanorequal" }, { text: "Greater Than", value: "greaterthan" }, { text: "Greater Than Or Equal", value: "greaterthanorequal" }, { text: "Between", value: "between" }, { text: "Custom Filter", value: "customfilter" }],</value>
  </data>
  <data name="ExcelFilter_GuidMenuOptions" xml:space="preserve">
    <value> [{ text: "Equal", value: "equal" }, { text: "Not Equal", value: "notequal" }, { text: "Custom Filter", value: "customfilter" }],</value>
  </data>
  <data name="ExcelFilter_DateMenuOptions" xml:space="preserve">
    <value> [{ text: "Equal", value: "equal" }, { text: "Not Equal", value: "notequal" }, { text: "Less Than", value: "lessthan" }, { text: "Less Than Or Equal", value: "lessthanorequal" }, { text: "Greater Than", value: "greaterthan" }, { text: "Greater Than Or Equal", value: "greaterthanorequal" }, { text: "Between", value: "between" }, { text: "Custom Filter", value: "customfilter" }],</value>
  </data>
  <data name="ExcelFilter_DatetimeMenuOptions" xml:space="preserve">
    <value> [{ text: "Equal", value: "equal" }, { text: "Not Equal", value: "notequal" }, { text: "Less Than", value: "lessthan" }, { text: "Less Than Or Equal", value: "lessthanorequal" }, { text: "Greater Than", value: "greaterthan" }, { text: "Greater Than Or Equal", value: "greaterthanorequal" }, { text: "Between", value: "between" }, { text: "Custom Filter", value: "customfilter" }],</value>
  </data>
  <data name="ExcelFilter_Top10MenuOptions" xml:space="preserve">
    <value> [{ text: "Top", value: "top" }, { text: "Bottom", value: "bottom" }],</value>
  </data>
  <data name="ExcelFilter_title" xml:space="preserve">
    <value>Custom Filter</value>
  </data>
  <data name="ExcelFilter_PredicateAnd" xml:space="preserve">
    <value>AND</value>
  </data>
  <data name="ExcelFilter_PredicateOr" xml:space="preserve">
    <value>OR</value>
  </data>
  <data name="ExcelFilter_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="ExcelFilter_MatchCase" xml:space="preserve">
    <value>Match Case</value>
  </data>
  <data name="ExcelFilter_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="ExcelFilter_NoResult" xml:space="preserve">
    <value>No Matches Found</value>
  </data>
  <data name="ExcelFilter_CheckBoxStatusMsg" xml:space="preserve">
    <value>Not showing all items</value>
  </data>
  <data name="ExcelFilter_DatePickerWaterMark" xml:space="preserve">
    <value>Select date</value>
  </data>
  <data name="ExcelFilter_DateTimePickerWaterMark" xml:space="preserve">
    <value>Select date time</value>
  </data>
  <data name="ExcelFilter_True" xml:space="preserve">
    <value>true</value>
  </data>
  <data name="ExcelFilter_False" xml:space="preserve">
    <value>false</value>
  </data>
  <data name="ExcelFilter_AddToFilter" xml:space="preserve">
    <value>Add current selection to filter</value>
  </data>
  <data name="FileExplorer_Folder" xml:space="preserve">
    <value>Folder</value>
  </data>
  <data name="FileExplorer_EmptyFolder" xml:space="preserve">
    <value>This folder is empty</value>
  </data>
  <data name="FileExplorer_ProtectedFolder" xml:space="preserve">
    <value>You don't currently have permission to access this folder</value>
  </data>
  <data name="FileExplorer_EmptyResult" xml:space="preserve">
    <value>No items match your search</value>
  </data>
  <data name="FileExplorer_Back" xml:space="preserve">
    <value>Backward</value>
  </data>
  <data name="FileExplorer_Forward" xml:space="preserve">
    <value>Forward</value>
  </data>
  <data name="FileExplorer_Upward" xml:space="preserve">
    <value>Upward</value>
  </data>
  <data name="FileExplorer_Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="FileExplorer_Addressbar" xml:space="preserve">
    <value>Address bar</value>
  </data>
  <data name="FileExplorer_Upload" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="FileExplorer_Rename" xml:space="preserve">
    <value>Rename</value>
  </data>
  <data name="FileExplorer_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="FileExplorer_Download" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="FileExplorer_Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="FileExplorer_PasteError" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="FileExplorer_UploadError" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="FileExplorer_RenameError" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="FileExplorer_Cut" xml:space="preserve">
    <value>Cut</value>
  </data>
  <data name="FileExplorer_Copy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="FileExplorer_Paste" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="FileExplorer_Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="FileExplorer_Searchbar" xml:space="preserve">
    <value>Search bar</value>
  </data>
  <data name="FileExplorer_Open" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="FileExplorer_Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="FileExplorer_NewFolder" xml:space="preserve">
    <value>New folder</value>
  </data>
  <data name="FileExplorer_SortBy" xml:space="preserve">
    <value>Sort by</value>
  </data>
  <data name="FileExplorer_Size" xml:space="preserve">
    <value>Size</value>
  </data>
  <data name="FileExplorer_RenameAlert" xml:space="preserve">
    <value>Please enter new name</value>
  </data>
  <data name="FileExplorer_NewFolderAlert" xml:space="preserve">
    <value>Please enter new folder name</value>
  </data>
  <data name="FileExplorer_ContextMenuOpen" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="FileExplorer_ContextMenuNewFolder" xml:space="preserve">
    <value>New folder</value>
  </data>
  <data name="FileExplorer_ContextMenuDelete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="FileExplorer_ContextMenuRename" xml:space="preserve">
    <value>Rename</value>
  </data>
  <data name="FileExplorer_ContextMenuUpload" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="FileExplorer_ContextMenuDownload" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="FileExplorer_ContextMenuCut" xml:space="preserve">
    <value>Cut</value>
  </data>
  <data name="FileExplorer_ContextMenuCopy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="FileExplorer_ContextMenuPaste" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="FileExplorer_ContextMenuGetinfo" xml:space="preserve">
    <value>Get info</value>
  </data>
  <data name="FileExplorer_ContextMenuRefresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="FileExplorer_ContextMenuOpenFolderLocation" xml:space="preserve">
    <value>Open folder location</value>
  </data>
  <data name="FileExplorer_Item" xml:space="preserve">
    <value>item</value>
  </data>
  <data name="FileExplorer_Items" xml:space="preserve">
    <value>items</value>
  </data>
  <data name="FileExplorer_Selected" xml:space="preserve">
    <value>selected</value>
  </data>
  <data name="FileExplorer_ErrorOnFolderCreation" xml:space="preserve">
    <value>This destination already contains a folder named '{0}'. Do you want to merge this folder content with already existing folder '{0}'?</value>
  </data>
  <data name="FileExplorer_InvalidFileName" xml:space="preserve">
    <value>A file name can't contain any of the following characters: \/: *? \ &lt;&gt; |</value>
  </data>
  <data name="FileExplorer_GeneralError" xml:space="preserve">
    <value>Please see browser console window for more information</value>
  </data>
  <data name="FileExplorer_ErrorPath" xml:space="preserve">
    <value>FileExplorer can't find '{0}'. Check the spelling and try again.</value>
  </data>
  <data name="FileExplorer_UploadReplaceAlert" xml:space="preserve">
    <value>File named '{0}' already exists. Replace old file with new one?</value>
  </data>
  <data name="FileExplorer_PasteReplaceAlert" xml:space="preserve">
    <value>File named '{0}' already exists. Replace old file with new one?</value>
  </data>
  <data name="FileExplorer_DuplicateAlert" xml:space="preserve">
    <value>There is already a file with the same name '{0}'. Do you want to create file with duplicate name</value>
  </data>
  <data name="FileExplorer_DuplicateFileCreation" xml:space="preserve">
    <value>There is already a file with the same name in this location. Do you want to rename '{0}' to '{1}'?</value>
  </data>
  <data name="FileExplorer_DeleteFolder" xml:space="preserve">
    <value> Are you sure you want to delete </value>
  </data>
  <data name="FileExplorer_DeleteMultipleFolder" xml:space="preserve">
    <value>Are you sure you want to delete these {0} items?</value>
  </data>
  <data name="FileExplorer_CancelPasteAction" xml:space="preserve">
    <value>The destination folder is a subfolder of source folder.</value>
  </data>
  <data name="FileExplorer_OkButton" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="FileExplorer_ContextMenuSortBy" xml:space="preserve">
    <value>Sort by</value>
  </data>
  <data name="FileExplorer_CancelButton" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="FileExplorer_YesToAllButton" xml:space="preserve">
    <value>Yes to all</value>
  </data>
  <data name="FileExplorer_NoToAllButton" xml:space="preserve">
    <value>No to all</value>
  </data>
  <data name="FileExplorer_YesButton" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="FileExplorer_NoButton" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="FileExplorer_SkipButton" xml:space="preserve">
    <value>Skip</value>
  </data>
  <data name="FileExplorer_Grid" xml:space="preserve">
    <value>Grid view</value>
  </data>
  <data name="FileExplorer_Tile" xml:space="preserve">
    <value>Tile view</value>
  </data>
  <data name="FileExplorer_LargeIcons" xml:space="preserve">
    <value>Large icons</value>
  </data>
  <data name="FileExplorer_Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="FileExplorer_Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="FileExplorer_Type" xml:space="preserve">
    <value>Item type</value>
  </data>
  <data name="FileExplorer_Layout" xml:space="preserve">
    <value>Layout</value>
  </data>
  <data name="FileExplorer_Created" xml:space="preserve">
    <value>Created</value>
  </data>
  <data name="FileExplorer_Accessed" xml:space="preserve">
    <value>Accessed</value>
  </data>
  <data name="FileExplorer_Modified" xml:space="preserve">
    <value>Modified</value>
  </data>
  <data name="FileExplorer_Permission" xml:space="preserve">
    <value>Permission</value>
  </data>
  <data name="FileExplorer_DialogCloseToolTip" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="FileExplorer_UploadSettings_buttonText_upload" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="FileExplorer_UploadSettings_buttonText_browse" xml:space="preserve">
    <value>Browse</value>
  </data>
  <data name="FileExplorer_UploadSettings_buttonText_cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="FileExplorer_UploadSettings_buttonText_close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="FileExplorer_UploadSettings_dialogText_title" xml:space="preserve">
    <value>Upload Box</value>
  </data>
  <data name="FileExplorer_UploadSettings_dialogText_name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="FileExplorer_UploadSettings_dialogText_size" xml:space="preserve">
    <value>Size</value>
  </data>
  <data name="FileExplorer_UploadSettings_dialogText_status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="FileExplorer_UploadSettings_dropAreaText" xml:space="preserve">
    <value>Drop files or click to upload</value>
  </data>
  <data name="FileExplorer_UploadSettings_filedetail" xml:space="preserve">
    <value>The selected file size is too large. Please select a file within the valid size.</value>
  </data>
  <data name="FileExplorer_UploadSettings_denyError" xml:space="preserve">
    <value>Files with #Extension extensions are not allowed.</value>
  </data>
  <data name="FileExplorer_UploadSettings_allowError" xml:space="preserve">
    <value>Only files with #Extension extensions are allowed.</value>
  </data>
  <data name="FileExplorer_UploadSettings_cancelToolTip" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="FileExplorer_UploadSettings_removeToolTip" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="FileExplorer_UploadSettings_retryToolTip" xml:space="preserve">
    <value>Retry</value>
  </data>
  <data name="FileExplorer_UploadSettings_completedToolTip" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="FileExplorer_UploadSettings_failedToolTip" xml:space="preserve">
    <value>Failed</value>
  </data>
  <data name="FileExplorer_UploadSettings_closeToolTip" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Gantt_emptyRecord" xml:space="preserve">
    <value>No records to display</value>
  </data>
  <data name="Gantt_unassignedTask" xml:space="preserve">
    <value>Unassigned Task</value>
  </data>
  <data name="Gantt_alertTexts_indentAlert" xml:space="preserve">
    <value>There is no Gantt record is selected to perform the indent</value>
  </data>
  <data name="Gantt_alertTexts_outdentAlert" xml:space="preserve">
    <value>There is no Gantt record is selected to perform the outdent</value>
  </data>
  <data name="Gantt_alertTexts_predecessorEditingValidationAlert" xml:space="preserve">
    <value>Cyclic dependency occurred, please check the predecessor</value>
  </data>
  <data name="Gantt_alertTexts_predecessorAddingValidationAlert" xml:space="preserve">
    <value>Fill all the columns in predecessor table</value>
  </data>
  <data name="Gantt_alertTexts_idValidationAlert" xml:space="preserve">
    <value>Duplicate ID</value>
  </data>
  <data name="Gantt_alertTexts_dateValidationAlert" xml:space="preserve">
    <value>Invalid end date</value>
  </data>
  <data name="Gantt_alertTexts_dialogResourceAlert" xml:space="preserve">
    <value>Fill all the columns in resource table</value>
  </data>
  <data name="Gantt_columnHeaderTexts_taskId" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="Gantt_columnHeaderTexts_taskName" xml:space="preserve">
    <value>Task Name</value>
  </data>
  <data name="Gantt_columnHeaderTexts_startDate" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="Gantt_columnHeaderTexts_endDate" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="Gantt_columnHeaderTexts_resourceInfo" xml:space="preserve">
    <value>Resources</value>
  </data>
  <data name="Gantt_columnHeaderTexts_duration" xml:space="preserve">
    <value>Duration</value>
  </data>
  <data name="Gantt_columnHeaderTexts_status" xml:space="preserve">
    <value>Progress</value>
  </data>
  <data name="Gantt_columnHeaderTexts_taskMode" xml:space="preserve">
    <value>Task Mode</value>
  </data>
  <data name="Gantt_columnHeaderTexts_serialNumber" xml:space="preserve">
    <value>S.No</value>
  </data>
  <data name="Gantt_columnHeaderTexts_subTasksStartDate" xml:space="preserve">
    <value>SubTasks Start Date</value>
  </data>
  <data name="Gantt_columnHeaderTexts_subTasksEndDate" xml:space="preserve">
    <value>SubTasks End Date</value>
  </data>
  <data name="Gantt_columnHeaderTexts_scheduleStartDate" xml:space="preserve">
    <value>Schedule Start Date</value>
  </data>
  <data name="Gantt_columnHeaderTexts_scheduleEndDate" xml:space="preserve">
    <value>Schedule End Date</value>
  </data>
  <data name="Gantt_columnHeaderTexts_predecessor" xml:space="preserve">
    <value>Predecessors</value>
  </data>
  <data name="Gantt_columnHeaderTexts_type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Gantt_columnHeaderTexts_offset" xml:space="preserve">
    <value>Offset</value>
  </data>
  <data name="Gantt_columnHeaderTexts_baselineStartDate" xml:space="preserve">
    <value>Baseline Start Date</value>
  </data>
  <data name="Gantt_columnHeaderTexts_baselineEndDate" xml:space="preserve">
    <value>Baseline End Date</value>
  </data>
  <data name="Gantt_columnHeaderTexts_WBS" xml:space="preserve">
    <value>WBS</value>
  </data>
  <data name="Gantt_columnHeaderTexts_WBSPredecessor" xml:space="preserve">
    <value>WBS Predecessor</value>
  </data>
  <data name="Gantt_columnHeaderTexts_dialogCustomFieldName" xml:space="preserve">
    <value>Column Name</value>
  </data>
  <data name="Gantt_columnHeaderTexts_dialogCustomFieldValue" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="Gantt_columnHeaderTexts_notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="Gantt_columnHeaderTexts_taskType" xml:space="preserve">
    <value>Task Type</value>
  </data>
  <data name="Gantt_columnHeaderTexts_work" xml:space="preserve">
    <value>Work</value>
  </data>
  <data name="Gantt_columnHeaderTexts_unit" xml:space="preserve">
    <value>Unit</value>
  </data>
  <data name="Gantt_columnHeaderTexts_effortDriven" xml:space="preserve">
    <value>Effort Driven</value>
  </data>
  <data name="Gantt_columnHeaderTexts_resourceName" xml:space="preserve">
    <value>Resource Name</value>
  </data>
  <data name="Gantt_editDialogTexts_addFormTitle" xml:space="preserve">
    <value>New Task</value>
  </data>
  <data name="Gantt_editDialogTexts_editFormTitle" xml:space="preserve">
    <value>Edit Task</value>
  </data>
  <data name="Gantt_editDialogTexts_saveButton" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Gantt_editDialogTexts_deleteButton" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Gantt_editDialogTexts_cancelButton" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Gantt_editDialogTexts_addPredecessor" xml:space="preserve">
    <value>Add New</value>
  </data>
  <data name="Gantt_editDialogTexts_removePredecessor" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="Gantt_editDialogTexts_addButton" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Gantt_columnDialogTexts_field" xml:space="preserve">
    <value>Field</value>
  </data>
  <data name="Gantt_columnDialogTexts_headerText" xml:space="preserve">
    <value>Header Text</value>
  </data>
  <data name="Gantt_columnDialogTexts_editType" xml:space="preserve">
    <value>Edit Type</value>
  </data>
  <data name="Gantt_columnDialogTexts_filterEditType" xml:space="preserve">
    <value>Filter Edit Type</value>
  </data>
  <data name="Gantt_columnDialogTexts_allowFiltering" xml:space="preserve">
    <value>Allow Filtering</value>
  </data>
  <data name="Gantt_columnDialogTexts_allowFilteringBlankContent" xml:space="preserve">
    <value>Allow Filtering Blank Content</value>
  </data>
  <data name="Gantt_columnDialogTexts_allowSorting" xml:space="preserve">
    <value>Allow Sorting</value>
  </data>
  <data name="Gantt_columnDialogTexts_visible" xml:space="preserve">
    <value>Visible</value>
  </data>
  <data name="Gantt_columnDialogTexts_width" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="Gantt_columnDialogTexts_textAlign" xml:space="preserve">
    <value>Text Alignment</value>
  </data>
  <data name="Gantt_columnDialogTexts_headerTextAlign" xml:space="preserve">
    <value>Header Text Alignment</value>
  </data>
  <data name="Gantt_columnDialogTexts_columnsDropdownData" xml:space="preserve">
    <value>Column Dropdown Data</value>
  </data>
  <data name="Gantt_columnDialogTexts_dropdownTableText" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="Gantt_columnDialogTexts_dropdownTableValue" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="Gantt_columnDialogTexts_addData" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Gantt_columnDialogTexts_deleteData" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="Gantt_columnDialogTexts_allowCellSelection" xml:space="preserve">
    <value>Allow Cell Selection</value>
  </data>
  <data name="Gantt_columnDialogTexts_displayAsCheckbox" xml:space="preserve">
    <value>Display As Checkbox</value>
  </data>
  <data name="Gantt_columnDialogTexts_clipMode" xml:space="preserve">
    <value>Clip Mode</value>
  </data>
  <data name="Gantt_columnDialogTexts_tooltip" xml:space="preserve">
    <value>Tooltip</value>
  </data>
  <data name="Gantt_columnDialogTexts_showInColumnChooser" xml:space="preserve">
    <value>Show In Column Chooser</value>
  </data>
  <data name="Gantt_columnDialogTexts_headerTooltip" xml:space="preserve">
    <value>Header Tooltip</value>
  </data>
  <data name="Gantt_editTypeTexts_string" xml:space="preserve">
    <value>String</value>
  </data>
  <data name="Gantt_editTypeTexts_numeric" xml:space="preserve">
    <value>Numeric</value>
  </data>
  <data name="Gantt_editTypeTexts_datePicker" xml:space="preserve">
    <value>Date Picker</value>
  </data>
  <data name="Gantt_editTypeTexts_dateTimePicker" xml:space="preserve">
    <value>Date Time Picker</value>
  </data>
  <data name="Gantt_editTypeTexts_dropdown" xml:space="preserve">
    <value>Dropdown</value>
  </data>
  <data name="Gantt_editTypeTexts_boolean" xml:space="preserve">
    <value>Boolean</value>
  </data>
  <data name="Gantt_textAlignTypes_right" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="Gantt_textAlignTypes_left" xml:space="preserve">
    <value>Left</value>
  </data>
  <data name="Gantt_textAlignTypes_center" xml:space="preserve">
    <value>Center</value>
  </data>
  <data name="Gantt_clipModeTexts_clip" xml:space="preserve">
    <value>Clip</value>
  </data>
  <data name="Gantt_clipModeTexts_ellipsis" xml:space="preserve">
    <value>Ellipsis</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_addTool" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_editTool" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_saveTool" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_deleteTool" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_cancelTool" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_searchTool" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_indentTool" xml:space="preserve">
    <value>Indent</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_outdentTool" xml:space="preserve">
    <value>Outdent</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_expandAllTool" xml:space="preserve">
    <value>Expand All</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_collapseAllTool" xml:space="preserve">
    <value>Collapse All</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_nextTimeSpanTool" xml:space="preserve">
    <value>Next Timespan</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_prevTimeSpanTool" xml:space="preserve">
    <value>Previous Timespan</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_criticalPathTool" xml:space="preserve">
    <value>Critical Path</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_excelExportTool" xml:space="preserve">
    <value>Excel Export</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_printTool" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="Gantt_toolboxTooltipTexts_pdfExportTool" xml:space="preserve">
    <value>PDF Export</value>
  </data>
  <data name="Gantt_durationUnitTexts_days" xml:space="preserve">
    <value>days</value>
  </data>
  <data name="Gantt_durationUnitTexts_hours" xml:space="preserve">
    <value>hours</value>
  </data>
  <data name="Gantt_durationUnitTexts_minutes" xml:space="preserve">
    <value>minutes</value>
  </data>
  <data name="Gantt_durationUnitTexts_day" xml:space="preserve">
    <value>day</value>
  </data>
  <data name="Gantt_durationUnitTexts_hour" xml:space="preserve">
    <value>hour</value>
  </data>
  <data name="Gantt_durationUnitTexts_minute" xml:space="preserve">
    <value>minute</value>
  </data>
  <data name="Gantt_durationUnitEditText_minute" xml:space="preserve">
    <value> ["m", "min", "minute", "minutes"],</value>
  </data>
  <data name="Gantt_durationUnitEditText_hour" xml:space="preserve">
    <value> ["h", "hr", "hour", "hours"],</value>
  </data>
  <data name="Gantt_durationUnitEditText_day" xml:space="preserve">
    <value> ["d", "dy", "day", "days"]</value>
  </data>
  <data name="Gantt_workUnitTexts_days" xml:space="preserve">
    <value>days</value>
  </data>
  <data name="Gantt_workUnitTexts_hours" xml:space="preserve">
    <value>hours</value>
  </data>
  <data name="Gantt_workUnitTexts_minutes" xml:space="preserve">
    <value>minutes</value>
  </data>
  <data name="Gantt_taskTypeTexts_fixedWork" xml:space="preserve">
    <value>Fixed Work</value>
  </data>
  <data name="Gantt_taskTypeTexts_fixedUnit" xml:space="preserve">
    <value>Fixed Units</value>
  </data>
  <data name="Gantt_taskTypeTexts_fixedDuration" xml:space="preserve">
    <value>Fixed Duration</value>
  </data>
  <data name="Gantt_effortDrivenTexts_yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="Gantt_effortDrivenTexts_no" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="Gantt_contextMenuTexts_taskDetailsText" xml:space="preserve">
    <value>Task Details...</value>
  </data>
  <data name="Gantt_contextMenuTexts_addNewTaskText" xml:space="preserve">
    <value>Add New Task</value>
  </data>
  <data name="Gantt_contextMenuTexts_indentText" xml:space="preserve">
    <value>Indent</value>
  </data>
  <data name="Gantt_contextMenuTexts_outdentText" xml:space="preserve">
    <value>Outdent</value>
  </data>
  <data name="Gantt_contextMenuTexts_deleteText" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Gantt_contextMenuTexts_aboveText" xml:space="preserve">
    <value>Above</value>
  </data>
  <data name="Gantt_contextMenuTexts_belowText" xml:space="preserve">
    <value>Below</value>
  </data>
  <data name="Gantt_newTaskTexts_newTaskName" xml:space="preserve">
    <value>New Task</value>
  </data>
  <data name="Gantt_columnMenuTexts_sortAscendingText" xml:space="preserve">
    <value>Sort Ascending</value>
  </data>
  <data name="Gantt_columnMenuTexts_sortDescendingText" xml:space="preserve">
    <value>Sort Descending</value>
  </data>
  <data name="Gantt_columnMenuTexts_columnsText" xml:space="preserve">
    <value>Columns</value>
  </data>
  <data name="Gantt_columnMenuTexts_insertColumnLeft" xml:space="preserve">
    <value>Insert Column Left</value>
  </data>
  <data name="Gantt_columnMenuTexts_insertColumnRight" xml:space="preserve">
    <value>Insert Column Right</value>
  </data>
  <data name="Gantt_columnMenuTexts_deleteColumn" xml:space="preserve">
    <value>Delete Column</value>
  </data>
  <data name="Gantt_columnMenuTexts_renameColumn" xml:space="preserve">
    <value>Rename Column</value>
  </data>
  <data name="Gantt_taskModeTexts_manual" xml:space="preserve">
    <value>Manual</value>
  </data>
  <data name="Gantt_taskModeTexts_auto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="Gantt_columnDialogTitle_insertColumn" xml:space="preserve">
    <value>Insert Column</value>
  </data>
  <data name="Gantt_columnDialogTitle_deleteColumn" xml:space="preserve">
    <value>Delete Column</value>
  </data>
  <data name="Gantt_columnDialogTitle_renameColumn" xml:space="preserve">
    <value>Rename Column</value>
  </data>
  <data name="Gantt_deleteColumnText" xml:space="preserve">
    <value>Are you sure you want to delete this column?</value>
  </data>
  <data name="Gantt_okButtonText" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Gantt_cancelButtonText" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Gantt_confirmDeleteText" xml:space="preserve">
    <value>Confirm Delete</value>
  </data>
  <data name="Gantt_predecessorEditingTexts_fromText" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="Gantt_predecessorEditingTexts_toText" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="Gantt_dialogTabTitleTexts_generalTabText" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="Gantt_dialogTabTitleTexts_predecessorsTabText" xml:space="preserve">
    <value>Predecessors</value>
  </data>
  <data name="Gantt_dialogTabTitleTexts_resourcesTabText" xml:space="preserve">
    <value>Resources</value>
  </data>
  <data name="Gantt_dialogTabTitleTexts_customFieldsTabText" xml:space="preserve">
    <value>Custom Fields</value>
  </data>
  <data name="Gantt_dialogTabTitleTexts_notesTabText" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="Gantt_predecessorCollectionText" xml:space="preserve">
    <value> [                            { id: "SS", text: "Start-Start", value: "Start-Start" },                            { id: "SF", text: "Start-Finish", value: "Start-Finish" },                            { id: "FS", text: "Finish-Start", value: "Finish-Start" },                            { id: "FF", text: "Finish-Finish", value: "Finish-Finish" }],</value>
  </data>
  <data name="Gantt_linkValidationRuleText_taskBeforePredecessor" xml:space="preserve">
    <value>You moved '{0}' to start before '{1}' finishes and the two tasks are linked. As the result, the links cannot be honored. Select one action below to perform</value>
  </data>
  <data name="Gantt_linkValidationRuleText_taskAfterPredecessor" xml:space="preserve">
    <value>You moved '{0}' away from '{1}' and the two tasks are linked. As the result, the links cannot be honored. Select one action below to perform</value>
  </data>
  <data name="Gantt_linkValidationDialogTitle" xml:space="preserve">
    <value>Validate Editing</value>
  </data>
  <data name="Gantt_linkValidationRuleOptions_cancel" xml:space="preserve">
    <value>Cancel, keep the existing link</value>
  </data>
  <data name="Gantt_linkValidationRuleOptions_removeLink" xml:space="preserve">
    <value>Remove the link and move '{0}' to start on '{1}'.</value>
  </data>
  <data name="Gantt_linkValidationRuleOptions_preserveLink" xml:space="preserve">
    <value>Move the '{0}' to start on '{1}' and keep the link.</value>
  </data>
  <data name="Gantt_connectorLineDialogText_from" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="Gantt_connectorLineDialogText_to" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="Gantt_connectorLineDialogText_taskLink" xml:space="preserve">
    <value>Task Link</value>
  </data>
  <data name="Gantt_connectorLineDialogText_lag" xml:space="preserve">
    <value>Lag</value>
  </data>
  <data name="Gantt_connectorLineDialogText_okButtonText" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Gantt_connectorLineDialogText_cancelButtonText" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Gantt_connectorLineDialogText_deleteButtonText" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Gantt_connectorLineDialogText_title" xml:space="preserve">
    <value>Task Dependency</value>
  </data>
  <data name="Gantt_nullText" xml:space="preserve">
    <value>Null</value>
  </data>
  <data name="Grid_EmptyRecord" xml:space="preserve">
    <value>No records to display</value>
  </data>
  <data name="Grid_GroupDropArea" xml:space="preserve">
    <value>Drag a column header here to group its column</value>
  </data>
  <data name="Grid_DeleteOperationAlert" xml:space="preserve">
    <value>No records selected for delete operation</value>
  </data>
  <data name="Grid_EditOperationAlert" xml:space="preserve">
    <value>No records selected for edit operation</value>
  </data>
  <data name="Grid_SaveButton" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Grid_OKButton" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Grid_CancelButton" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Grid_EditFormTitle" xml:space="preserve">
    <value>Details of </value>
  </data>
  <data name="Grid_AddFormTitle" xml:space="preserve">
    <value>Add New Record</value>
  </data>
  <data name="Grid_GroupCaptionFormat" xml:space="preserve">
    <value>{{:headerText}}: {{:key}} - {{:count}} {{if count == 1 }} item {{else}} items {{/if}} </value>
  </data>
  <data name="Grid_BatchSaveConfirm" xml:space="preserve">
    <value>Are you sure you want to save changes?</value>
  </data>
  <data name="Grid_BatchSaveLostChanges" xml:space="preserve">
    <value>Unsaved changes will be lost. Are you sure you want to continue?</value>
  </data>
  <data name="Grid_ConfirmDelete" xml:space="preserve">
    <value>Are you sure you want to Delete Record?</value>
  </data>
  <data name="Grid_CancelEdit" xml:space="preserve">
    <value>Are you sure you want to Cancel the changes?</value>
  </data>
  <data name="Grid_PagerInfo" xml:space="preserve">
    <value>{0} of {1} pages ({2} items)</value>
  </data>
  <data name="Grid_FrozenColumnsViewAlert" xml:space="preserve">
    <value>Frozen columns should be in grid view area</value>
  </data>
  <data name="Grid_FrozenColumnsScrollAlert" xml:space="preserve">
    <value>Enable allowScrolling while using frozen Columns</value>
  </data>
  <data name="Grid_FrozenNotSupportedException" xml:space="preserve">
    <value>Frozen Columns and Rows are not supported for Grouping, Row Template, Detail Template, Hierarchy Grid and Batch Editing</value>
  </data>
  <data name="Grid_Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Grid_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Grid_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Grid_Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="Grid_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Grid_Done" xml:space="preserve">
    <value>Done</value>
  </data>
  <data name="Grid_Columns" xml:space="preserve">
    <value>Columns</value>
  </data>
  <data name="Grid_SelectAll" xml:space="preserve">
    <value>(Select All)</value>
  </data>
  <data name="Grid_PrintGrid" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="Grid_ExcelExport" xml:space="preserve">
    <value>Excel Export</value>
  </data>
  <data name="Grid_WordExport" xml:space="preserve">
    <value>Word Export</value>
  </data>
  <data name="Grid_PdfExport" xml:space="preserve">
    <value>PDF Export</value>
  </data>
  <data name="Grid_StringMenuOptions" xml:space="preserve">
    <value> [{ text: "StartsWith", value: "StartsWith" }, { text: "EndsWith", value: "EndsWith" }, { text: "Contains", value: "Contains" }, { text: "Equal", value: "Equal" }, { text: "NotEqual", value: "NotEqual" }],</value>
  </data>
  <data name="Grid_NumberMenuOptions" xml:space="preserve">
    <value> [{ text: "LessThan", value: "LessThan" }, { text: "GreaterThan", value: "GreaterThan" }, { text: "LessThanOrEqual", value: "LessThanOrEqual" }, { text: "GreaterThanOrEqual", value: "GreaterThanOrEqual" }, { text: "Equal", value: "Equal" }, { text: "NotEqual", value: "NotEqual" }, { text: "Between", value: "Between" }],</value>
  </data>
  <data name="Grid_PredicateAnd" xml:space="preserve">
    <value>AND</value>
  </data>
  <data name="Grid_PredicateOr" xml:space="preserve">
    <value>OR</value>
  </data>
  <data name="Grid_Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="Grid_FilterMenuCaption" xml:space="preserve">
    <value>Filter Value</value>
  </data>
  <data name="Grid_FilterMenuFromCaption" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="Grid_FilterMenuToCaption" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="Grid_FilterbarTitle" xml:space="preserve">
    <value>'s filter bar cell</value>
  </data>
  <data name="Grid_MatchCase" xml:space="preserve">
    <value>Match Case</value>
  </data>
  <data name="Grid_Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="Grid_ResponsiveFilter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="Grid_ResponsiveSorting" xml:space="preserve">
    <value>Sort</value>
  </data>
  <data name="Grid_Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Grid_NumericTextBoxWaterMark" xml:space="preserve">
    <value>Enter value</value>
  </data>
  <data name="Grid_DatePickerWaterMark" xml:space="preserve">
    <value>Select date</value>
  </data>
  <data name="Grid_EmptyDataSource" xml:space="preserve">
    <value>DataSource must not be empty at initial load since columns are generated from dataSource in AutoGenerate Column Grid</value>
  </data>
  <data name="Grid_ForeignKeyAlert" xml:space="preserve">
    <value>The updated value should be a valid foreign key value</value>
  </data>
  <data name="Grid_True" xml:space="preserve">
    <value>true</value>
  </data>
  <data name="Grid_False" xml:space="preserve">
    <value>false</value>
  </data>
  <data name="Grid_UnGroup" xml:space="preserve">
    <value>Click here to ungroup</value>
  </data>
  <data name="Grid_AddRecord" xml:space="preserve">
    <value>Add Record</value>
  </data>
  <data name="Grid_EditRecord" xml:space="preserve">
    <value>Edit Record</value>
  </data>
  <data name="Grid_DeleteRecord" xml:space="preserve">
    <value>Delete Record</value>
  </data>
  <data name="Grid_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Grid_Grouping" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="Grid_Ungrouping" xml:space="preserve">
    <value>Ungroup</value>
  </data>
  <data name="Grid_SortInAscendingOrder" xml:space="preserve">
    <value>Sort In Ascending Order</value>
  </data>
  <data name="Grid_SortInDescendingOrder" xml:space="preserve">
    <value>Sort In Descending Order</value>
  </data>
  <data name="Grid_NextPage" xml:space="preserve">
    <value>Next Page</value>
  </data>
  <data name="Grid_PreviousPage" xml:space="preserve">
    <value>Previous Page</value>
  </data>
  <data name="Grid_FirstPage" xml:space="preserve">
    <value>First Page</value>
  </data>
  <data name="Grid_LastPage" xml:space="preserve">
    <value>Last Page</value>
  </data>
  <data name="Grid_EmptyRowValidationMessage" xml:space="preserve">
    <value>Atleast one field must be updated</value>
  </data>
  <data name="Grid_NoResult" xml:space="preserve">
    <value>No Matches Found</value>
  </data>
  <data name="PivotChart_Measure" xml:space="preserve">
    <value>Measure</value>
  </data>
  <data name="PivotChart_Row" xml:space="preserve">
    <value>Row</value>
  </data>
  <data name="PivotChart_Column" xml:space="preserve">
    <value>Column</value>
  </data>
  <data name="PivotChart_Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="PivotChart_Expand" xml:space="preserve">
    <value>Expand</value>
  </data>
  <data name="PivotChart_Collapse" xml:space="preserve">
    <value>Collapse</value>
  </data>
  <data name="PivotChart_Exit" xml:space="preserve">
    <value>Exit</value>
  </data>
  <data name="PivotChart_ChartTypes" xml:space="preserve">
    <value>Chart Types</value>
  </data>
  <data name="PivotChart_TDCharts" xml:space="preserve">
    <value>3D Charts</value>
  </data>
  <data name="PivotChart_Tooltip" xml:space="preserve">
    <value>Tooltip</value>
  </data>
  <data name="PivotChart_Exporting" xml:space="preserve">
    <value>Exporting</value>
  </data>
  <data name="PivotChart_Line" xml:space="preserve">
    <value>Line</value>
  </data>
  <data name="PivotChart_Spline" xml:space="preserve">
    <value>Spline</value>
  </data>
  <data name="PivotChart_Area" xml:space="preserve">
    <value>Area</value>
  </data>
  <data name="PivotChart_SplineArea" xml:space="preserve">
    <value>Spline Area</value>
  </data>
  <data name="PivotChart_StepLine" xml:space="preserve">
    <value>Step Line</value>
  </data>
  <data name="PivotChart_StepArea" xml:space="preserve">
    <value>Step Area</value>
  </data>
  <data name="PivotChart_Pie" xml:space="preserve">
    <value>Pie</value>
  </data>
  <data name="PivotChart_Bar" xml:space="preserve">
    <value>Bar</value>
  </data>
  <data name="PivotChart_StackingArea" xml:space="preserve">
    <value>Stacking Area</value>
  </data>
  <data name="PivotChart_StackingColumn" xml:space="preserve">
    <value>Stacking Column</value>
  </data>
  <data name="PivotChart_StackingBar" xml:space="preserve">
    <value>Stacking Bar</value>
  </data>
  <data name="PivotChart_Pyramid" xml:space="preserve">
    <value>Pyramid</value>
  </data>
  <data name="PivotChart_Funnel" xml:space="preserve">
    <value>Funnel</value>
  </data>
  <data name="PivotChart_Doughnut" xml:space="preserve">
    <value>Doughnut</value>
  </data>
  <data name="PivotChart_Scatter" xml:space="preserve">
    <value>Scatter</value>
  </data>
  <data name="PivotChart_Bubble" xml:space="preserve">
    <value>Bubble</value>
  </data>
  <data name="PivotChart_TreeMap" xml:space="preserve">
    <value>TreeMap</value>
  </data>
  <data name="PivotChart_ColumnTD" xml:space="preserve">
    <value>Column 3D</value>
  </data>
  <data name="PivotChart_PieTD" xml:space="preserve">
    <value>Pie 3D</value>
  </data>
  <data name="PivotChart_BarTD" xml:space="preserve">
    <value>Bar 3D</value>
  </data>
  <data name="PivotChart_StackingBarTD" xml:space="preserve">
    <value>StackingBar 3D</value>
  </data>
  <data name="PivotChart_StackingColumnTD" xml:space="preserve">
    <value>StackingColumn 3D</value>
  </data>
  <data name="PivotChart_Excel" xml:space="preserve">
    <value>Excel</value>
  </data>
  <data name="PivotChart_Word" xml:space="preserve">
    <value>Word</value>
  </data>
  <data name="PivotChart_Pdf" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="PivotChart_PNG" xml:space="preserve">
    <value>PNG</value>
  </data>
  <data name="PivotChart_EMF" xml:space="preserve">
    <value>EMF</value>
  </data>
  <data name="PivotChart_GIF" xml:space="preserve">
    <value>GIF</value>
  </data>
  <data name="PivotChart_JPG" xml:space="preserve">
    <value>JPG</value>
  </data>
  <data name="PivotChart_BMP" xml:space="preserve">
    <value>BMP</value>
  </data>
  <data name="PivotChart_ZoomIn" xml:space="preserve">
    <value>Zoom In</value>
  </data>
  <data name="PivotChart_ZoomOut" xml:space="preserve">
    <value>Zoom Out</value>
  </data>
  <data name="PivotChart_Legend" xml:space="preserve">
    <value>Legend</value>
  </data>
  <data name="PivotChart_SmartLabels" xml:space="preserve">
    <value>Smart Labels</value>
  </data>
  <data name="PivotChart_Interactions" xml:space="preserve">
    <value>Interactions</value>
  </data>
  <data name="PivotChart_Zooming" xml:space="preserve">
    <value>Zooming</value>
  </data>
  <data name="PivotChart_Rotate45" xml:space="preserve">
    <value>Rotate45</value>
  </data>
  <data name="PivotChart_Rotate90" xml:space="preserve">
    <value>Rotate90</value>
  </data>
  <data name="PivotChart_Trim" xml:space="preserve">
    <value>Trim</value>
  </data>
  <data name="PivotChart_MultipleRows" xml:space="preserve">
    <value>Multiple Rows</value>
  </data>
  <data name="PivotChart_Wrap" xml:space="preserve">
    <value>Wrap</value>
  </data>
  <data name="PivotChart_Hide" xml:space="preserve">
    <value>Hide</value>
  </data>
  <data name="PivotChart_WrapByWord" xml:space="preserve">
    <value>Wrap By word</value>
  </data>
  <data name="PivotChart_CrossHair" xml:space="preserve">
    <value>Cross Hair</value>
  </data>
  <data name="PivotChart_TrackBall" xml:space="preserve">
    <value>Track Ball</value>
  </data>
  <data name="PivotChart_DisableTD" xml:space="preserve">
    <value>Disable 3D Charts</value>
  </data>
  <data name="PivotChart_None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="PivotChart_Exception" xml:space="preserve">
    <value>Exception</value>
  </data>
  <data name="PivotChart_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="PivotClient_NoReports" xml:space="preserve">
    <value>No Reports Found in DB</value>
  </data>
  <data name="PivotClient_Sort" xml:space="preserve">
    <value>Sort</value>
  </data>
  <data name="PivotClient_Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="PivotClient_SelectField" xml:space="preserve">
    <value>Select field</value>
  </data>
  <data name="PivotClient_LabelFilterLabel" xml:space="preserve">
    <value>Show the items for which the label</value>
  </data>
  <data name="PivotClient_ValueFilterLabel" xml:space="preserve">
    <value>Show the items for which</value>
  </data>
  <data name="PivotClient_ClearSorting" xml:space="preserve">
    <value>Clear Sorting</value>
  </data>
  <data name="PivotClient_ClearFilterFrom" xml:space="preserve">
    <value>Clear Filter From</value>
  </data>
  <data name="PivotClient_SortAtoZ" xml:space="preserve">
    <value>Sort A to Z</value>
  </data>
  <data name="PivotClient_SortZtoA" xml:space="preserve">
    <value>Sort Z to A</value>
  </data>
  <data name="PivotClient_LabelFilters" xml:space="preserve">
    <value>Label Filters  </value>
  </data>
  <data name="PivotClient_BeginsWith" xml:space="preserve">
    <value>Begins With</value>
  </data>
  <data name="PivotClient_DoesNotBeginsWith" xml:space="preserve">
    <value>Does Not Begin With</value>
  </data>
  <data name="PivotClient_EndsWith" xml:space="preserve">
    <value>Ends With</value>
  </data>
  <data name="PivotClient_NotEndsWith" xml:space="preserve">
    <value>Not Ends With</value>
  </data>
  <data name="PivotClient_DoesNotEndsWith" xml:space="preserve">
    <value>Does Not End With</value>
  </data>
  <data name="PivotClient_Contains" xml:space="preserve">
    <value>Contains</value>
  </data>
  <data name="PivotClient_DoesNotContains" xml:space="preserve">
    <value>Does Not Contain</value>
  </data>
  <data name="PivotClient_ValueFilters" xml:space="preserve">
    <value>Value Filters</value>
  </data>
  <data name="PivotClient_ClearFilter" xml:space="preserve">
    <value>Clear Filter</value>
  </data>
  <data name="PivotClient_Equals" xml:space="preserve">
    <value>Equals</value>
  </data>
  <data name="PivotClient_DoesNotEquals" xml:space="preserve">
    <value>Does Not Equal</value>
  </data>
  <data name="PivotClient_NotEquals" xml:space="preserve">
    <value>Not Equals</value>
  </data>
  <data name="PivotClient_GreaterThan" xml:space="preserve">
    <value>Greater Than</value>
  </data>
  <data name="PivotClient_GreaterThanOrEqualTo" xml:space="preserve">
    <value>Greater Than Or Equal To</value>
  </data>
  <data name="PivotClient_LessThan" xml:space="preserve">
    <value>Less Than</value>
  </data>
  <data name="PivotClient_LessThanOrEqualTo" xml:space="preserve">
    <value>Less Than Or Equal To</value>
  </data>
  <data name="PivotClient_Between" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="PivotClient_NotBetween" xml:space="preserve">
    <value>Not Between</value>
  </data>
  <data name="PivotClient_Top10" xml:space="preserve">
    <value>Top Count</value>
  </data>
  <data name="PivotClient_IsGreaterThan" xml:space="preserve">
    <value>Is Greater Than</value>
  </data>
  <data name="PivotClient_IsGreaterThanOrEqualTo" xml:space="preserve">
    <value>Is Greater Than Or Equal To</value>
  </data>
  <data name="PivotClient_IsLessThan" xml:space="preserve">
    <value>Is Less Than</value>
  </data>
  <data name="PivotClient_IsLessThanOrEqualTo" xml:space="preserve">
    <value>Is Less Than Or Equal To</value>
  </data>
  <data name="PivotClient_DeferUpdate" xml:space="preserve">
    <value>Defer Update</value>
  </data>
  <data name="PivotClient_MDXQuery" xml:space="preserve">
    <value>MDX Query</value>
  </data>
  <data name="PivotClient_Column" xml:space="preserve">
    <value>Column</value>
  </data>
  <data name="PivotClient_Row" xml:space="preserve">
    <value>Row</value>
  </data>
  <data name="PivotClient_Slicer" xml:space="preserve">
    <value>Slicer</value>
  </data>
  <data name="PivotClient_CubeSelector" xml:space="preserve">
    <value>Cube Selector</value>
  </data>
  <data name="PivotClient_ReportName" xml:space="preserve">
    <value>Report Name</value>
  </data>
  <data name="PivotClient_NewReport" xml:space="preserve">
    <value>New Report</value>
  </data>
  <data name="PivotClient_CubeDimensionBrowser" xml:space="preserve">
    <value>Cube Dimension Browser</value>
  </data>
  <data name="PivotClient_AddReport" xml:space="preserve">
    <value>Add Report</value>
  </data>
  <data name="PivotClient_RemoveReport" xml:space="preserve">
    <value>Remove Report</value>
  </data>
  <data name="PivotClient_CannotRemoveSingleReport" xml:space="preserve">
    <value>Cannot remove single report</value>
  </data>
  <data name="PivotClient_AreYouSureToDeleteTheReport" xml:space="preserve">
    <value>Are you sure to delete the Report</value>
  </data>
  <data name="PivotClient_RenameReport" xml:space="preserve">
    <value>Rename Report</value>
  </data>
  <data name="PivotClient_ChartTypes" xml:space="preserve">
    <value>Chart Types</value>
  </data>
  <data name="PivotClient_ToggleAxis" xml:space="preserve">
    <value>Toggle Axis</value>
  </data>
  <data name="PivotClient_Load" xml:space="preserve">
    <value>Load</value>
  </data>
  <data name="PivotClient_ExportToExcel" xml:space="preserve">
    <value>Export To Excel</value>
  </data>
  <data name="PivotClient_ExportToWord" xml:space="preserve">
    <value>Export To Word</value>
  </data>
  <data name="PivotClient_ExportToPdf" xml:space="preserve">
    <value>Export To Pdf</value>
  </data>
  <data name="PivotClient_FullScreen" xml:space="preserve">
    <value>Full Screen</value>
  </data>
  <data name="PivotClient_Grid" xml:space="preserve">
    <value>Grid</value>
  </data>
  <data name="PivotClient_Chart" xml:space="preserve">
    <value>Chart</value>
  </data>
  <data name="PivotClient_OK" xml:space="preserve">
    <value>&lt;u&gt;O&lt;/u&gt;K</value>
  </data>
  <data name="PivotClient_Cancel" xml:space="preserve">
    <value>&lt;u&gt;C&lt;/u&gt;ancel</value>
  </data>
  <data name="PivotClient_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="PivotClient_AddToColumn" xml:space="preserve">
    <value>Add to Column</value>
  </data>
  <data name="PivotClient_AddToRow" xml:space="preserve">
    <value>Add to Row</value>
  </data>
  <data name="PivotClient_AddToSlicer" xml:space="preserve">
    <value>Add to Slicer</value>
  </data>
  <data name="PivotClient_MeasureEditor" xml:space="preserve">
    <value>Measure Editor</value>
  </data>
  <data name="PivotClient_MemberEditor" xml:space="preserve">
    <value>Member Editor</value>
  </data>
  <data name="PivotClient_Measures" xml:space="preserve">
    <value>Measures</value>
  </data>
  <data name="PivotClient_SortOrFilterColumn" xml:space="preserve">
    <value>Sort/Filter (Column)</value>
  </data>
  <data name="PivotClient_SortOrFilterRow" xml:space="preserve">
    <value>Sort/Filter (Row)</value>
  </data>
  <data name="PivotClient_SortingAndFiltering" xml:space="preserve">
    <value>Sorting And Filtering</value>
  </data>
  <data name="PivotClient_Sorting" xml:space="preserve">
    <value>Sorting</value>
  </data>
  <data name="PivotClient_Measure" xml:space="preserve">
    <value>&lt;u&gt;M&lt;/u&gt;easure</value>
  </data>
  <data name="PivotClient_Order" xml:space="preserve">
    <value>Order</value>
  </data>
  <data name="PivotClient_Filtering" xml:space="preserve">
    <value>Filtering</value>
  </data>
  <data name="PivotClient_Condition" xml:space="preserve">
    <value>C&lt;u&gt;o&lt;/u&gt;ndition</value>
  </data>
  <data name="PivotClient_Value" xml:space="preserve">
    <value>Val&lt;u&gt;u&lt;/u&gt;e</value>
  </data>
  <data name="PivotClient_PreserveHierarchy" xml:space="preserve">
    <value>P&lt;u&gt;r&lt;/u&gt;eserve  Hierarchy</value>
  </data>
  <data name="PivotClient_Ascending" xml:space="preserve">
    <value>&lt;u&gt;A&lt;/u&gt;scending</value>
  </data>
  <data name="PivotClient_Descending" xml:space="preserve">
    <value>D&lt;u&gt;e&lt;/u&gt;scending</value>
  </data>
  <data name="PivotClient_Enable" xml:space="preserve">
    <value>E&lt;u&gt;n&lt;/u&gt;able</value>
  </data>
  <data name="PivotClient_Disable" xml:space="preserve">
    <value>D&lt;u&gt;i&lt;/u&gt;sable</value>
  </data>
  <data name="PivotClient_and" xml:space="preserve">
    <value>&lt;u&gt;a&lt;/u&gt;nd</value>
  </data>
  <data name="PivotClient_EqualTo" xml:space="preserve">
    <value>EqualTo</value>
  </data>
  <data name="PivotClient_ReportList" xml:space="preserve">
    <value>Report List</value>
  </data>
  <data name="PivotClient_Line" xml:space="preserve">
    <value>Line</value>
  </data>
  <data name="PivotClient_Spline" xml:space="preserve">
    <value>Spline</value>
  </data>
  <data name="PivotClient_Area" xml:space="preserve">
    <value>Area</value>
  </data>
  <data name="PivotClient_SplineArea" xml:space="preserve">
    <value>Spline Area</value>
  </data>
  <data name="PivotClient_StepLine" xml:space="preserve">
    <value>Step Line</value>
  </data>
  <data name="PivotClient_StepArea" xml:space="preserve">
    <value>Step Area</value>
  </data>
  <data name="PivotClient_Pie" xml:space="preserve">
    <value>Pie</value>
  </data>
  <data name="PivotClient_Bar" xml:space="preserve">
    <value>Bar</value>
  </data>
  <data name="PivotClient_StackingArea" xml:space="preserve">
    <value>Stacking Area</value>
  </data>
  <data name="PivotClient_StackingColumn" xml:space="preserve">
    <value>Stacking Column</value>
  </data>
  <data name="PivotClient_StackingBar" xml:space="preserve">
    <value>Stacking Bar</value>
  </data>
  <data name="PivotClient_Pyramid" xml:space="preserve">
    <value>Pyramid</value>
  </data>
  <data name="PivotClient_Funnel" xml:space="preserve">
    <value>Funnel</value>
  </data>
  <data name="PivotClient_Doughnut" xml:space="preserve">
    <value>Doughnut</value>
  </data>
  <data name="PivotClient_Scatter" xml:space="preserve">
    <value>Scatter</value>
  </data>
  <data name="PivotClient_Bubble" xml:space="preserve">
    <value>Bubble</value>
  </data>
  <data name="PivotClient_WaterFall" xml:space="preserve">
    <value>WaterFall</value>
  </data>
  <data name="PivotClient_TreeMap" xml:space="preserve">
    <value>TreeMap</value>
  </data>
  <data name="PivotClient_Alert" xml:space="preserve">
    <value>Alert</value>
  </data>
  <data name="PivotClient_MDXAlertMsg" xml:space="preserve">
    <value>Please add a measure, dimension, or hierarchy in an appropriate axis to view the MDX Query.</value>
  </data>
  <data name="PivotClient_FilterSortRowAlertMsg" xml:space="preserve">
    <value>Dimension is not found in the row axis. Please add Dimension element in the row axis for sorting/filtering.</value>
  </data>
  <data name="PivotClient_FilterSortColumnAlertMsg" xml:space="preserve">
    <value>Dimension is not found in the column axis. Please add Dimension element in the column axis for sorting/filtering.</value>
  </data>
  <data name="PivotClient_FilterSortcolMeasureAlertMsg" xml:space="preserve">
    <value>Please add measure to the column axis</value>
  </data>
  <data name="PivotClient_FilterSortrowMeasureAlertMsg" xml:space="preserve">
    <value>Please add measure to the row axis</value>
  </data>
  <data name="PivotClient_FilterSortElementAlertMsg" xml:space="preserve">
    <value>Element is not found in the column axis. Please add an element in column axis for sorting/filtering.</value>
  </data>
  <data name="PivotClient_SelectRecordAlertMsg" xml:space="preserve">
    <value>Please select a valid report.</value>
  </data>
  <data name="PivotClient_FilterMeasureSelectionAlertMsg" xml:space="preserve">
    <value>Please select a valid measure.</value>
  </data>
  <data name="PivotClient_FilterConditionAlertMsg" xml:space="preserve">
    <value>Please set a valid condition.</value>
  </data>
  <data name="PivotClient_FilterStartValueAlertMsg" xml:space="preserve">
    <value>Please set a start value.</value>
  </data>
  <data name="PivotClient_FilterEndValueAlertMsg" xml:space="preserve">
    <value>Please set a end value.</value>
  </data>
  <data name="PivotClient_FilterInvalidAlertMsg" xml:space="preserve">
    <value>Invalid operation !</value>
  </data>
  <data name="PivotClient_Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="PivotClient_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="PivotClient_SaveAs" xml:space="preserve">
    <value>Save As</value>
  </data>
  <data name="PivotClient_SelectReport" xml:space="preserve">
    <value>Select Report</value>
  </data>
  <data name="PivotClient_DBReport" xml:space="preserve">
    <value>Report Manipulation in DB</value>
  </data>
  <data name="PivotClient_Rename" xml:space="preserve">
    <value>Rename</value>
  </data>
  <data name="PivotClient_SetReportNameAlertMsg" xml:space="preserve">
    <value>Please set report name.</value>
  </data>
  <data name="PivotClient_MultipleItems" xml:space="preserve">
    <value>Multiple items</value>
  </data>
  <data name="PivotClient_All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="PivotClient_CalculatedMember" xml:space="preserve">
    <value>Calculated Member</value>
  </data>
  <data name="PivotClient_Caption" xml:space="preserve">
    <value>Caption:</value>
  </data>
  <data name="PivotClient_Expression" xml:space="preserve">
    <value>Expression:</value>
  </data>
  <data name="PivotClient_MemberType" xml:space="preserve">
    <value>MemberType:</value>
  </data>
  <data name="PivotClient_FormatString" xml:space="preserve">
    <value>Format String:</value>
  </data>
  <data name="PivotClient_MultipleMeasure" xml:space="preserve">
    <value>More than one measure cannot be sliced.</value>
  </data>
  <data name="PivotClient_DuplicateCalcMeasure" xml:space="preserve">
    <value>Calculated Member with same name already exists.</value>
  </data>
  <data name="PivotClient_EmptyField" xml:space="preserve">
    <value>Calculated Member name or Expression should not be empty.</value>
  </data>
  <data name="PivotClient_EmptyFormat" xml:space="preserve">
    <value>Format String for Calculated Member is empty.</value>
  </data>
  <data name="PivotClient_Warning" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="PivotClient_Confirm" xml:space="preserve">
    <value>Calculated Member with the same name already exists. Do you want to replace?</value>
  </data>
  <data name="PivotClient_KPIs" xml:space="preserve">
    <value>KPIs</value>
  </data>
  <data name="PivotClient_Collection" xml:space="preserve">
    <value>Collection</value>
  </data>
  <data name="PivotClient_Report" xml:space="preserve">
    <value>Report</value>
  </data>
  <data name="PivotClient_AddCurrentSelectionToFilter" xml:space="preserve">
    <value>Add current selection to filter</value>
  </data>
  <data name="PivotClient_SaveMsg" xml:space="preserve">
    <value>Report saved successfully!!!</value>
  </data>
  <data name="PivotClient_RenameMsg" xml:space="preserve">
    <value>Report renamed successfully!!!</value>
  </data>
  <data name="PivotClient_RemoveMsg" xml:space="preserve">
    <value>Report removed successfully!!!</value>
  </data>
  <data name="PivotClient_Success" xml:space="preserve">
    <value>Success</value>
  </data>
  <data name="PivotClient_KpiAlertMsg" xml:space="preserve">
    <value>The field you are moving cannot be placed in that area of the report</value>
  </data>
  <data name="PivotClient_NotAllItemsShowing" xml:space="preserve">
    <value>Not all child nodes are shown</value>
  </data>
  <data name="PivotClient_EditorLinkPanelAlert" xml:space="preserve">
    <value>The members have more than 1000 items under one or more parent. Only the first 1000 items are displayed under each parent.</value>
  </data>
  <data name="PivotClient_NamedSetAlert" xml:space="preserve">
    <value>Named sets of same field cannot be added to the PivotTable report at the same time. Click OK to remove ' &lt;Set 1&gt; ' named set and add ' &lt;Set 2&gt; ' named set.</value>
  </data>
  <data name="PivotClient_Exception" xml:space="preserve">
    <value>Exception</value>
  </data>
  <data name="PivotGauge_RevenueGoal" xml:space="preserve">
    <value>Revenue Goal</value>
  </data>
  <data name="PivotGauge_RevenueValue" xml:space="preserve">
    <value>Revenue Value</value>
  </data>
  <data name="PivotGauge_Exception" xml:space="preserve">
    <value>Exception</value>
  </data>
  <data name="Pager_pagerInfo" xml:space="preserve">
    <value>{0} of {1} pages ({2} items)</value>
  </data>
  <data name="Pager_firstPageTooltip" xml:space="preserve">
    <value>Go to first page</value>
  </data>
  <data name="Pager_lastPageTooltip" xml:space="preserve">
    <value>Go to last page</value>
  </data>
  <data name="Pager_nextPageTooltip" xml:space="preserve">
    <value>Go to next page</value>
  </data>
  <data name="Pager_previousPageTooltip" xml:space="preserve">
    <value>Go to previous page</value>
  </data>
  <data name="Pager_nextPagerTooltip" xml:space="preserve">
    <value>Go to next Pager</value>
  </data>
  <data name="Pager_previousPagerTooltip" xml:space="preserve">
    <value>Go to previous Pager</value>
  </data>
  <data name="PdfViewer_toolbar_print_headerText" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="PdfViewer_toolbar_print_contentText" xml:space="preserve">
    <value>Print the PDF document.</value>
  </data>
  <data name="PdfViewer_toolbar_first_headerText" xml:space="preserve">
    <value>First</value>
  </data>
  <data name="PdfViewer_toolbar_first_contentText" xml:space="preserve">
    <value>Go to the first page of the PDF document.</value>
  </data>
  <data name="PdfViewer_toolbar_previous_headerText" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="PdfViewer_toolbar_previous_contentText" xml:space="preserve">
    <value>Go to the previous page of the PDF document.</value>
  </data>
  <data name="PdfViewer_toolbar_next_headerText" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="PdfViewer_toolbar_next_contentText" xml:space="preserve">
    <value>Go to the next page of the PDF document.</value>
  </data>
  <data name="PdfViewer_toolbar_last_headerText" xml:space="preserve">
    <value>Last</value>
  </data>
  <data name="PdfViewer_toolbar_last_contentText" xml:space="preserve">
    <value>Go to the last page of the PDF document.</value>
  </data>
  <data name="PdfViewer_toolbar_zoomIn_headerText" xml:space="preserve">
    <value>Zoom-In</value>
  </data>
  <data name="PdfViewer_toolbar_zoomIn_contentText" xml:space="preserve">
    <value>Zoom in to the PDF document.</value>
  </data>
  <data name="PdfViewer_toolbar_zoomOut_headerText" xml:space="preserve">
    <value>Zoom-Out</value>
  </data>
  <data name="PdfViewer_toolbar_zoomOut_contentText" xml:space="preserve">
    <value>Zoom out of the PDF document.</value>
  </data>
  <data name="PdfViewer_toolbar_pageIndex_headerText" xml:space="preserve">
    <value>Page Number</value>
  </data>
  <data name="PdfViewer_toolbar_pageIndex_contentText" xml:space="preserve">
    <value>Current page number to view.</value>
  </data>
  <data name="PdfViewer_toolbar_zoom_headerText" xml:space="preserve">
    <value>Zoom</value>
  </data>
  <data name="PdfViewer_toolbar_zoom_contentText" xml:space="preserve">
    <value>Zoom in or out on the PDF document.</value>
  </data>
  <data name="PdfViewer_toolbar_fitToWidth_headerText" xml:space="preserve">
    <value>Fit to Width</value>
  </data>
  <data name="PdfViewer_toolbar_fitToWidth_contentText" xml:space="preserve">
    <value>Fit the PDF page to the width of the container.</value>
  </data>
  <data name="PdfViewer_toolbar_fitToPage_headerText" xml:space="preserve">
    <value>Fit to Page</value>
  </data>
  <data name="PdfViewer_toolbar_fitToPage_contentText" xml:space="preserve">
    <value>Fit the PDF page to the container.</value>
  </data>
  <data name="PdfViewer_toolbar_search_headerText" xml:space="preserve">
    <value>Search Text</value>
  </data>
  <data name="PdfViewer_toolbar_search_contentText" xml:space="preserve">
    <value>Search text in the PDF pages.</value>
  </data>
  <data name="PdfViewer_toolbar_download_headerText" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="PdfViewer_toolbar_download_contentText" xml:space="preserve">
    <value>Download the PDF document.</value>
  </data>
  <data name="PdfViewer_toolbar_highlight_headerText" xml:space="preserve">
    <value>Highlight Text</value>
  </data>
  <data name="PdfViewer_toolbar_highlight_contentText" xml:space="preserve">
    <value>Highlight text in the PDF pages.</value>
  </data>
  <data name="PdfViewer_toolbar_strikeout_headerText" xml:space="preserve">
    <value>Strikethrough Text</value>
  </data>
  <data name="PdfViewer_toolbar_strikeout_contentText" xml:space="preserve">
    <value>Strikethrough text in the PDF pages.</value>
  </data>
  <data name="PdfViewer_toolbar_underline_headerText" xml:space="preserve">
    <value>Underline Text</value>
  </data>
  <data name="PdfViewer_toolbar_underline_contentText" xml:space="preserve">
    <value>Underline text in the PDF pages.</value>
  </data>
  <data name="PdfViewer_toolbar_signature_headerText" xml:space="preserve">
    <value>Signature</value>
  </data>
  <data name="PdfViewer_toolbar_signature_contentText" xml:space="preserve">
    <value>Add or create hand-written signature.</value>
  </data>
  <data name="PdfViewer_toolbar_select_headerText" xml:space="preserve">
    <value>Selection</value>
  </data>
  <data name="PdfViewer_toolbar_select_contentText" xml:space="preserve">
    <value>Selection tool for text.</value>
  </data>
  <data name="PdfViewer_toolbar_scroll_headerText" xml:space="preserve">
    <value>Panning</value>
  </data>
  <data name="PdfViewer_toolbar_scroll_contentText" xml:space="preserve">
    <value>Click to pan around the document</value>
  </data>
  <data name="PdfViewer_contextMenu_copy_contentText" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="PdfViewer_contextMenu_googleSearch_contentText" xml:space="preserve">
    <value>Search google</value>
  </data>
  <data name="PdfViewer_contextMenu_Find_contentText" xml:space="preserve">
    <value>Find:</value>
  </data>
  <data name="PdfViewer_contextMenu_matchCase_contentText" xml:space="preserve">
    <value>Match Case</value>
  </data>
  <data name="PdfViewer_contextMenu_auto_contentText" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="PdfViewer_contextMenu_openPopup_contentText" xml:space="preserve">
    <value>Open Pop-Up Note</value>
  </data>
  <data name="PdfViewer_contextMenu_Delete_contentText" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="PdfViewer_contextMenu_properties_contentText" xml:space="preserve">
    <value>Properties....</value>
  </data>
  <data name="PdfViewer_propertyWindow_underlineProperties_contentText" xml:space="preserve">
    <value>Underline Properties</value>
  </data>
  <data name="PdfViewer_propertyWindow_strikeOutProperties_contentText" xml:space="preserve">
    <value>StrikeOut Properties</value>
  </data>
  <data name="PdfViewer_propertyWindow_highlightProperties_contentText" xml:space="preserve">
    <value>Highlight Properties</value>
  </data>
  <data name="PdfViewer_propertyWindow_signatureProperties_contentText" xml:space="preserve">
    <value>Signature Properties</value>
  </data>
  <data name="PdfViewer_propertyWindow_appearance_contentText" xml:space="preserve">
    <value>Appearance</value>
  </data>
  <data name="PdfViewer_propertyWindow_general_contentText" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="PdfViewer_propertyWindow_color_contentText" xml:space="preserve">
    <value>Color:</value>
  </data>
  <data name="PdfViewer_propertyWindow_opacity_contentText" xml:space="preserve">
    <value>Opacity:</value>
  </data>
  <data name="PdfViewer_propertyWindow_author_contentText" xml:space="preserve">
    <value>Author:</value>
  </data>
  <data name="PdfViewer_propertyWindow_subject_contentText" xml:space="preserve">
    <value>Subject:</value>
  </data>
  <data name="PdfViewer_propertyWindow_modified_contentText" xml:space="preserve">
    <value>Modified:</value>
  </data>
  <data name="PdfViewer_propertyWindow_ok_contentText" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="PdfViewer_propertyWindow_cancel_contentText" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="PdfViewer_propertyWindow_locked_contentText" xml:space="preserve">
    <value>Locked</value>
  </data>
  <data name="PdfViewer_signatureWindow_Signature_contentText" xml:space="preserve">
    <value>Add Signature</value>
  </data>
  <data name="PdfViewer_signatureWindow_Add_contentText" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="PdfViewer_signatureWindow_clear_contentText" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="PdfViewer_waitingPopup_print_contentText" xml:space="preserve">
    <value>Preparing document for printing...</value>
  </data>
  <data name="PivotGrid_Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="PivotGrid_GrandTotal" xml:space="preserve">
    <value>Grand Total</value>
  </data>
  <data name="PivotGrid_Sort" xml:space="preserve">
    <value>Sort</value>
  </data>
  <data name="PivotGrid_SelectField" xml:space="preserve">
    <value>select Field</value>
  </data>
  <data name="PivotGrid_LabelFilterLabel" xml:space="preserve">
    <value>Show the items for which the label</value>
  </data>
  <data name="PivotGrid_ValueFilterLabel" xml:space="preserve">
    <value>Show the items for which</value>
  </data>
  <data name="PivotGrid_ClearSorting" xml:space="preserve">
    <value>Clear Sorting</value>
  </data>
  <data name="PivotGrid_ClearFilterFrom" xml:space="preserve">
    <value>Clear Filter From</value>
  </data>
  <data name="PivotGrid_SortAtoZ" xml:space="preserve">
    <value>Sort A to Z</value>
  </data>
  <data name="PivotGrid_SortZtoA" xml:space="preserve">
    <value>Sort Z to A</value>
  </data>
  <data name="PivotGrid_and" xml:space="preserve">
    <value>&lt;u&gt;a&lt;/u&gt;nd</value>
  </data>
  <data name="PivotGrid_LabelFilters" xml:space="preserve">
    <value>Label Filters  </value>
  </data>
  <data name="PivotGrid_BeginsWith" xml:space="preserve">
    <value>Begins With</value>
  </data>
  <data name="PivotGrid_NotBeginsWith" xml:space="preserve">
    <value>Not Begins With</value>
  </data>
  <data name="PivotGrid_EndsWith" xml:space="preserve">
    <value>Ends With</value>
  </data>
  <data name="PivotGrid_NotEndsWith" xml:space="preserve">
    <value>Not Ends With</value>
  </data>
  <data name="PivotGrid_Contains" xml:space="preserve">
    <value>Contains</value>
  </data>
  <data name="PivotGrid_NotContains" xml:space="preserve">
    <value>Not Contains</value>
  </data>
  <data name="PivotGrid_ValueFilters" xml:space="preserve">
    <value>Value Filters</value>
  </data>
  <data name="PivotGrid_ClearFilter" xml:space="preserve">
    <value>Clear Filter</value>
  </data>
  <data name="PivotGrid_Equals" xml:space="preserve">
    <value>Equals</value>
  </data>
  <data name="PivotGrid_NotEquals" xml:space="preserve">
    <value>Not Equals</value>
  </data>
  <data name="PivotGrid_GreaterThan" xml:space="preserve">
    <value>Greater Than </value>
  </data>
  <data name="PivotGrid_GreaterThanOrEqualTo" xml:space="preserve">
    <value>Greater Than Or Equal To </value>
  </data>
  <data name="PivotGrid_LessThan" xml:space="preserve">
    <value>Less Than </value>
  </data>
  <data name="PivotGrid_LessThanOrEqualTo" xml:space="preserve">
    <value>Less Than Or Equal To </value>
  </data>
  <data name="PivotGrid_Between" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="PivotGrid_NotBetween" xml:space="preserve">
    <value>Not Between</value>
  </data>
  <data name="PivotGrid_DoesNotBeginsWith" xml:space="preserve">
    <value>Does Not Begin With</value>
  </data>
  <data name="PivotGrid_DoesNotEndsWith" xml:space="preserve">
    <value>Does Not Ends With</value>
  </data>
  <data name="PivotGrid_DoesNotContains" xml:space="preserve">
    <value>Does Not Contains</value>
  </data>
  <data name="PivotGrid_DoesNotEquals" xml:space="preserve">
    <value>Does Not Equals</value>
  </data>
  <data name="PivotGrid_IsGreaterThan" xml:space="preserve">
    <value>Is Greater Than</value>
  </data>
  <data name="PivotGrid_IsGreaterThanOrEqualTo" xml:space="preserve">
    <value>Is Greater Than Or Equal To</value>
  </data>
  <data name="PivotGrid_IsLessThan" xml:space="preserve">
    <value>Is Less Than</value>
  </data>
  <data name="PivotGrid_IsLessThanOrEqualTo" xml:space="preserve">
    <value>Is Less Than Or Equal To</value>
  </data>
  <data name="PivotGrid_NumberFormatting" xml:space="preserve">
    <value>Number Formatting</value>
  </data>
  <data name="PivotGrid_FrozenHeaders" xml:space="preserve">
    <value>Frozen Headers</value>
  </data>
  <data name="PivotGrid_CellSelection" xml:space="preserve">
    <value>Cell Selection</value>
  </data>
  <data name="PivotGrid_CellContext" xml:space="preserve">
    <value>Cell Context</value>
  </data>
  <data name="PivotGrid_ColumnResize" xml:space="preserve">
    <value>Column Resize</value>
  </data>
  <data name="PivotGrid_Layouts" xml:space="preserve">
    <value>Layouts</value>
  </data>
  <data name="PivotGrid_NormalLayout" xml:space="preserve">
    <value>Normal Layout</value>
  </data>
  <data name="PivotGrid_NormalTopSummary" xml:space="preserve">
    <value>NormalTopSummary Layout</value>
  </data>
  <data name="PivotGrid_NoSummaries" xml:space="preserve">
    <value>NoSummaries Layout</value>
  </data>
  <data name="PivotGrid_ExcelLikeLayout" xml:space="preserve">
    <value>Excel Like Layout</value>
  </data>
  <data name="PivotGrid_FrozenHeader" xml:space="preserve">
    <value>Frozen Header</value>
  </data>
  <data name="PivotGrid_AdvancedFiltering" xml:space="preserve">
    <value>Advanced Filtering</value>
  </data>
  <data name="PivotGrid_Amount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="PivotGrid_Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="PivotGrid_Measures" xml:space="preserve">
    <value>Measures</value>
  </data>
  <data name="PivotGrid_NumberFormats" xml:space="preserve">
    <value>Number Formats</value>
  </data>
  <data name="PivotGrid_Exporting" xml:space="preserve">
    <value>Exporting</value>
  </data>
  <data name="PivotGrid_FileName" xml:space="preserve">
    <value>File Name</value>
  </data>
  <data name="PivotGrid_ToolTip" xml:space="preserve">
    <value>Tool Tip</value>
  </data>
  <data name="PivotGrid_RTL" xml:space="preserve">
    <value>RTL</value>
  </data>
  <data name="PivotGrid_CollapseByDefault" xml:space="preserve">
    <value>Collapse By Default</value>
  </data>
  <data name="PivotGrid_EnableDisablePaging" xml:space="preserve">
    <value>Enable / Disable Paging</value>
  </data>
  <data name="PivotGrid_PagingOptions" xml:space="preserve">
    <value>Paging Options</value>
  </data>
  <data name="PivotGrid_CategoricalPageSize" xml:space="preserve">
    <value>Categorical Page Size</value>
  </data>
  <data name="PivotGrid_SeriesPageSize" xml:space="preserve">
    <value>Series Page Size</value>
  </data>
  <data name="PivotGrid_HyperLink" xml:space="preserve">
    <value>HyperLink</value>
  </data>
  <data name="PivotGrid_CellEditing" xml:space="preserve">
    <value>Cell Editing</value>
  </data>
  <data name="PivotGrid_GroupingBar" xml:space="preserve">
    <value>Grouping Bar</value>
  </data>
  <data name="PivotGrid_SummaryCustomization" xml:space="preserve">
    <value>Summary Customization</value>
  </data>
  <data name="PivotGrid_SummaryTypes" xml:space="preserve">
    <value>Summary Types</value>
  </data>
  <data name="PivotGrid_SummaryType" xml:space="preserve">
    <value>Summary Type</value>
  </data>
  <data name="PivotGrid_EnableRowHeaderHyperlink" xml:space="preserve">
    <value>Enable RowHeaderHyperLink</value>
  </data>
  <data name="PivotGrid_EnableColumnHeaderHyperlink" xml:space="preserve">
    <value>Enable ColumnHeaderHyperLink</value>
  </data>
  <data name="PivotGrid_EnableValueCellHyperlink" xml:space="preserve">
    <value>Enable ValueCellHyperLink</value>
  </data>
  <data name="PivotGrid_EnableSummaryCellHyperlink" xml:space="preserve">
    <value>Enable SummaryCellHyperLink</value>
  </data>
  <data name="PivotGrid_HideGrandTotal" xml:space="preserve">
    <value>Hide GrandTotal</value>
  </data>
  <data name="PivotGrid_HideSubTotal" xml:space="preserve">
    <value>Hide SubTotal</value>
  </data>
  <data name="PivotGrid_Row" xml:space="preserve">
    <value>Row</value>
  </data>
  <data name="PivotGrid_Column" xml:space="preserve">
    <value>Column</value>
  </data>
  <data name="PivotGrid_Both" xml:space="preserve">
    <value>Both</value>
  </data>
  <data name="PivotGrid_Sum" xml:space="preserve">
    <value>Sum</value>
  </data>
  <data name="PivotGrid_Average" xml:space="preserve">
    <value>Average</value>
  </data>
  <data name="PivotGrid_Count" xml:space="preserve">
    <value>Count</value>
  </data>
  <data name="PivotGrid_Min" xml:space="preserve">
    <value>Min</value>
  </data>
  <data name="PivotGrid_Max" xml:space="preserve">
    <value>Max</value>
  </data>
  <data name="PivotGrid_Excel" xml:space="preserve">
    <value>Excel</value>
  </data>
  <data name="PivotGrid_Word" xml:space="preserve">
    <value>Word</value>
  </data>
  <data name="PivotGrid_PDF" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="PivotGrid_CSV" xml:space="preserve">
    <value>CSV</value>
  </data>
  <data name="PivotGrid_AddToFilter" xml:space="preserve">
    <value>Add to Filter</value>
  </data>
  <data name="PivotGrid_AddToRow" xml:space="preserve">
    <value>Add to Row</value>
  </data>
  <data name="PivotGrid_AddToColumn" xml:space="preserve">
    <value>Add to Column</value>
  </data>
  <data name="PivotGrid_AddToValues" xml:space="preserve">
    <value>Add to Values</value>
  </data>
  <data name="PivotGrid_Warning" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="PivotGrid_Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="PivotGrid_GroupingBarAlertMsg" xml:space="preserve">
    <value>The field you are moving cannot be placed in that area of the report</value>
  </data>
  <data name="PivotGrid_Expand" xml:space="preserve">
    <value>Expand</value>
  </data>
  <data name="PivotGrid_Collapse" xml:space="preserve">
    <value>Collapse</value>
  </data>
  <data name="PivotGrid_ToolTipRow" xml:space="preserve">
    <value>Row</value>
  </data>
  <data name="PivotGrid_ToolTipColumn" xml:space="preserve">
    <value>Column</value>
  </data>
  <data name="PivotGrid_ToolTipValue" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="PivotGrid_NoValue" xml:space="preserve">
    <value>No value</value>
  </data>
  <data name="PivotGrid_SeriesPage" xml:space="preserve">
    <value>Series Page</value>
  </data>
  <data name="PivotGrid_CategoricalPage" xml:space="preserve">
    <value>Categorical Page</value>
  </data>
  <data name="PivotGrid_DragFieldHere" xml:space="preserve">
    <value>Drag field here</value>
  </data>
  <data name="PivotGrid_ColumnArea" xml:space="preserve">
    <value>Drop column here</value>
  </data>
  <data name="PivotGrid_RowArea" xml:space="preserve">
    <value>Drop row here</value>
  </data>
  <data name="PivotGrid_ValueArea" xml:space="preserve">
    <value>Drop values here</value>
  </data>
  <data name="PivotGrid_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="PivotGrid_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="PivotGrid_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="PivotGrid_Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="PivotGrid_Goal" xml:space="preserve">
    <value>Goal</value>
  </data>
  <data name="PivotGrid_Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="PivotGrid_Trend" xml:space="preserve">
    <value>Trend</value>
  </data>
  <data name="PivotGrid_Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="PivotGrid_ConditionalFormattingErrorMsg" xml:space="preserve">
    <value>The given value is not matched</value>
  </data>
  <data name="PivotGrid_ConditionalFormattingConformMsg" xml:space="preserve">
    <value>Are you sure you want to remove the selected format?</value>
  </data>
  <data name="PivotGrid_EnterOperand1" xml:space="preserve">
    <value>Enter Operand1</value>
  </data>
  <data name="PivotGrid_EnterOperand2" xml:space="preserve">
    <value>Enter Operand2</value>
  </data>
  <data name="PivotGrid_ConditionalFormatting" xml:space="preserve">
    <value>Conditional Formatting</value>
  </data>
  <data name="PivotGrid_Condition" xml:space="preserve">
    <value>Conditional Type</value>
  </data>
  <data name="PivotGrid_Value1" xml:space="preserve">
    <value>Value1</value>
  </data>
  <data name="PivotGrid_Value2" xml:space="preserve">
    <value>Value2</value>
  </data>
  <data name="PivotGrid_Editcondtion" xml:space="preserve">
    <value>Edit Condition</value>
  </data>
  <data name="PivotGrid_AddNew" xml:space="preserve">
    <value>Add New</value>
  </data>
  <data name="PivotGrid_Format" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="PivotGrid_Backcolor" xml:space="preserve">
    <value>Back Color</value>
  </data>
  <data name="PivotGrid_Borderrange" xml:space="preserve">
    <value>Border Range</value>
  </data>
  <data name="PivotGrid_Borderstyle" xml:space="preserve">
    <value>Border Style</value>
  </data>
  <data name="PivotGrid_Fontsize" xml:space="preserve">
    <value>Font Size</value>
  </data>
  <data name="PivotGrid_Fontstyle" xml:space="preserve">
    <value>Font Style</value>
  </data>
  <data name="PivotGrid_Bordercolor" xml:space="preserve">
    <value>Border Color</value>
  </data>
  <data name="PivotGrid_NoMeasure" xml:space="preserve">
    <value>Please add any measure</value>
  </data>
  <data name="PivotGrid_Solid" xml:space="preserve">
    <value>Solid</value>
  </data>
  <data name="PivotGrid_Dashed" xml:space="preserve">
    <value>Dashed</value>
  </data>
  <data name="PivotGrid_Dotted" xml:space="preserve">
    <value>Dotted</value>
  </data>
  <data name="PivotGrid_Double" xml:space="preserve">
    <value>Double</value>
  </data>
  <data name="PivotGrid_Groove" xml:space="preserve">
    <value>Groove</value>
  </data>
  <data name="PivotGrid_Inset" xml:space="preserve">
    <value>Inset</value>
  </data>
  <data name="PivotGrid_Outset" xml:space="preserve">
    <value>Outset</value>
  </data>
  <data name="PivotGrid_Ridge" xml:space="preserve">
    <value>Ridge</value>
  </data>
  <data name="PivotGrid_None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="PivotGrid_Algerian" xml:space="preserve">
    <value>Algerian</value>
  </data>
  <data name="PivotGrid_Arial" xml:space="preserve">
    <value>Arial</value>
  </data>
  <data name="PivotGrid_BodoniMT" xml:space="preserve">
    <value>Bodoni MT</value>
  </data>
  <data name="PivotGrid_BritannicBold" xml:space="preserve">
    <value>Britannic Bold</value>
  </data>
  <data name="PivotGrid_Cambria" xml:space="preserve">
    <value>Cambria</value>
  </data>
  <data name="PivotGrid_Calibri" xml:space="preserve">
    <value>Calibri</value>
  </data>
  <data name="PivotGrid_CourierNew" xml:space="preserve">
    <value>Courier New</value>
  </data>
  <data name="PivotGrid_DejaVuSans" xml:space="preserve">
    <value>DejaVu Sans</value>
  </data>
  <data name="PivotGrid_Forte" xml:space="preserve">
    <value>Forte</value>
  </data>
  <data name="PivotGrid_Gerogia" xml:space="preserve">
    <value>Gerogia</value>
  </data>
  <data name="PivotGrid_Impact" xml:space="preserve">
    <value>Impact</value>
  </data>
  <data name="PivotGrid_SegoeUI" xml:space="preserve">
    <value>Segoe UI</value>
  </data>
  <data name="PivotGrid_Tahoma" xml:space="preserve">
    <value>Tahoma</value>
  </data>
  <data name="PivotGrid_TimesNewRoman" xml:space="preserve">
    <value>Times New Roman</value>
  </data>
  <data name="PivotGrid_Verdana" xml:space="preserve">
    <value>Verdana</value>
  </data>
  <data name="PivotGrid_CubeDimensionBrowser" xml:space="preserve">
    <value>Cube Dimension Browser</value>
  </data>
  <data name="PivotGrid_SelectHierarchy" xml:space="preserve">
    <value>Select Hierarchy</value>
  </data>
  <data name="PivotGrid_CalculatedField" xml:space="preserve">
    <value>Calculated Field</value>
  </data>
  <data name="PivotGrid_Name" xml:space="preserve">
    <value>Name:</value>
  </data>
  <data name="PivotGrid_Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="PivotGrid_Formula" xml:space="preserve">
    <value>Formula:</value>
  </data>
  <data name="PivotGrid_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="PivotGrid_Fields" xml:space="preserve">
    <value>Fields:</value>
  </data>
  <data name="PivotGrid_CalculatedFieldNameNotFound" xml:space="preserve">
    <value>Given CalculatedField name is not found</value>
  </data>
  <data name="PivotGrid_InsertField" xml:space="preserve">
    <value>Insert Field</value>
  </data>
  <data name="PivotGrid_EmptyField" xml:space="preserve">
    <value>Please enter Calculated field name or formula</value>
  </data>
  <data name="PivotGrid_NotValid" xml:space="preserve">
    <value>Given formula is not valid</value>
  </data>
  <data name="PivotGrid_NotPresent" xml:space="preserve">
    <value>Value field used in any of the Calculated Field formula is not present in the PivotGrid</value>
  </data>
  <data name="PivotGrid_Confirm" xml:space="preserve">
    <value>Calculated field with the same name already exists. Do you want to Replace ?</value>
  </data>
  <data name="PivotGrid_CalcValue" xml:space="preserve">
    <value>Calculated field can be inserted only in value area field</value>
  </data>
  <data name="PivotGrid_MultipleItems" xml:space="preserve">
    <value>Multiple items</value>
  </data>
  <data name="PivotGrid_All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="PivotGrid_Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="PivotGrid_Fontcolor" xml:space="preserve">
    <value>Font Color</value>
  </data>
  <data name="PivotGrid_AddCurrentSelectionToFilter" xml:space="preserve">
    <value>Add current selection to filter</value>
  </data>
  <data name="PivotGrid_Months" xml:space="preserve">
    <value>Months</value>
  </data>
  <data name="PivotGrid_Days" xml:space="preserve">
    <value>Days</value>
  </data>
  <data name="PivotGrid_Quarters" xml:space="preserve">
    <value>Quarters</value>
  </data>
  <data name="PivotGrid_Years" xml:space="preserve">
    <value>Years</value>
  </data>
  <data name="PivotGrid_Qtr" xml:space="preserve">
    <value>Qtr</value>
  </data>
  <data name="PivotGrid_Quarter" xml:space="preserve">
    <value>Quarter</value>
  </data>
  <data name="PivotGrid_NoRecordsToDisplay" xml:space="preserve">
    <value>No records to display.</value>
  </data>
  <data name="PivotGrid_EnterFormatName" xml:space="preserve">
    <value>Enter Format Name</value>
  </data>
  <data name="PivotGrid_FormatName" xml:space="preserve">
    <value>Format Name</value>
  </data>
  <data name="PivotGrid_RemoveFormat" xml:space="preserve">
    <value>Remove Format</value>
  </data>
  <data name="PivotGrid_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="PivotGrid_DuplicateFormatName" xml:space="preserve">
    <value>Duplicate Format Name</value>
  </data>
  <data name="PivotGrid_NotAllItemsShowing" xml:space="preserve">
    <value>Not all child nodes are shown</value>
  </data>
  <data name="PivotGrid_EditorLinkPanelAlert" xml:space="preserve">
    <value>The members have more than 1000 items under one or more parent. Only the first 1000 items are displayed under each parent.</value>
  </data>
  <data name="PivotGrid_Exception" xml:space="preserve">
    <value>Exception</value>
  </data>
  <data name="PivotPager_SeriesPage" xml:space="preserve">
    <value>Series Page</value>
  </data>
  <data name="PivotPager_CategoricalPage" xml:space="preserve">
    <value>Categorical Page</value>
  </data>
  <data name="PivotPager_Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="PivotPager_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="PivotPager_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="PivotPager_PageCountErrorMsg" xml:space="preserve">
    <value>Enter valid page number</value>
  </data>
  <data name="PivotSchemaDesigner_Sort" xml:space="preserve">
    <value>Sort</value>
  </data>
  <data name="PivotSchemaDesigner_SelectField" xml:space="preserve">
    <value>select Field</value>
  </data>
  <data name="PivotSchemaDesigner_LabelFilterLabel" xml:space="preserve">
    <value>Show the items for which the label</value>
  </data>
  <data name="PivotSchemaDesigner_ValueFilterLabel" xml:space="preserve">
    <value>Show the items for which</value>
  </data>
  <data name="PivotSchemaDesigner_ClearSorting" xml:space="preserve">
    <value>Clear Sorting</value>
  </data>
  <data name="PivotSchemaDesigner_ClearFilterFrom" xml:space="preserve">
    <value>Clear Filter From</value>
  </data>
  <data name="PivotSchemaDesigner_SortAtoZ" xml:space="preserve">
    <value>Sort A to Z</value>
  </data>
  <data name="PivotSchemaDesigner_SortZtoA" xml:space="preserve">
    <value>Sort Z to A</value>
  </data>
  <data name="PivotSchemaDesigner_and" xml:space="preserve">
    <value>&lt;u&gt;a&lt;/u&gt;nd</value>
  </data>
  <data name="PivotSchemaDesigner_LabelFilters" xml:space="preserve">
    <value>Label Filters  </value>
  </data>
  <data name="PivotSchemaDesigner_BeginsWith" xml:space="preserve">
    <value>Begins With</value>
  </data>
  <data name="PivotSchemaDesigner_NotBeginsWith" xml:space="preserve">
    <value>Not Begins With</value>
  </data>
  <data name="PivotSchemaDesigner_EndsWith" xml:space="preserve">
    <value>Ends With</value>
  </data>
  <data name="PivotSchemaDesigner_NotEndsWith" xml:space="preserve">
    <value>Not Ends With</value>
  </data>
  <data name="PivotSchemaDesigner_Contains" xml:space="preserve">
    <value>Contains</value>
  </data>
  <data name="PivotSchemaDesigner_NotContains" xml:space="preserve">
    <value>Not Contains</value>
  </data>
  <data name="PivotSchemaDesigner_ValueFilters" xml:space="preserve">
    <value>Value Filters</value>
  </data>
  <data name="PivotSchemaDesigner_ClearFilter" xml:space="preserve">
    <value>Clear Filter</value>
  </data>
  <data name="PivotSchemaDesigner_Equals" xml:space="preserve">
    <value>Equals</value>
  </data>
  <data name="PivotSchemaDesigner_NotEquals" xml:space="preserve">
    <value>Not Equals</value>
  </data>
  <data name="PivotSchemaDesigner_GreaterThan" xml:space="preserve">
    <value>Greater Than </value>
  </data>
  <data name="PivotSchemaDesigner_GreaterThanOrEqualTo" xml:space="preserve">
    <value>Greater Than Or Equal To </value>
  </data>
  <data name="PivotSchemaDesigner_LessThan" xml:space="preserve">
    <value>Less Than </value>
  </data>
  <data name="PivotSchemaDesigner_LessThanOrEqualTo" xml:space="preserve">
    <value>Less Than Or Equal To </value>
  </data>
  <data name="PivotSchemaDesigner_Between" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="PivotSchemaDesigner_NotBetween" xml:space="preserve">
    <value>Not Between</value>
  </data>
  <data name="PivotSchemaDesigner_DoesNotBeginsWith" xml:space="preserve">
    <value>Does Not Begins With</value>
  </data>
  <data name="PivotSchemaDesigner_DoesNotEndsWith" xml:space="preserve">
    <value>Does Not Ends With</value>
  </data>
  <data name="PivotSchemaDesigner_DoesNotContains" xml:space="preserve">
    <value>Does Not Contains</value>
  </data>
  <data name="PivotSchemaDesigner_DoesNotEquals" xml:space="preserve">
    <value>Does Not Equals</value>
  </data>
  <data name="PivotSchemaDesigner_IsGreaterThan" xml:space="preserve">
    <value>Is Greater Than</value>
  </data>
  <data name="PivotSchemaDesigner_IsGreaterThanOrEqualTo" xml:space="preserve">
    <value>Is Greater Than Or Equal To</value>
  </data>
  <data name="PivotSchemaDesigner_IsLessThan" xml:space="preserve">
    <value>Is Less Than</value>
  </data>
  <data name="PivotSchemaDesigner_IsLessThanOrEqualTo" xml:space="preserve">
    <value>Is Less Than Or Equal To</value>
  </data>
  <data name="PivotSchemaDesigner_Measures" xml:space="preserve">
    <value>Measures</value>
  </data>
  <data name="PivotSchemaDesigner_Warning" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="PivotSchemaDesigner_AlertMsg" xml:space="preserve">
    <value>The field you are moving cannot be placed in that area of the report</value>
  </data>
  <data name="PivotSchemaDesigner_Goal" xml:space="preserve">
    <value>Goal</value>
  </data>
  <data name="PivotSchemaDesigner_Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="PivotSchemaDesigner_Trend" xml:space="preserve">
    <value>Trend</value>
  </data>
  <data name="PivotSchemaDesigner_Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="PivotSchemaDesigner_AddToFilter" xml:space="preserve">
    <value>Add to Filter</value>
  </data>
  <data name="PivotSchemaDesigner_AddToRow" xml:space="preserve">
    <value>Add to Row</value>
  </data>
  <data name="PivotSchemaDesigner_AddToColumn" xml:space="preserve">
    <value>Add to Column</value>
  </data>
  <data name="PivotSchemaDesigner_AddToValues" xml:space="preserve">
    <value>Add to Value</value>
  </data>
  <data name="PivotSchemaDesigner_SummarizeValueBy" xml:space="preserve">
    <value>Summarize value by</value>
  </data>
  <data name="PivotSchemaDesigner_Sum" xml:space="preserve">
    <value>Sum</value>
  </data>
  <data name="PivotSchemaDesigner_Average" xml:space="preserve">
    <value>Average</value>
  </data>
  <data name="PivotSchemaDesigner_Count" xml:space="preserve">
    <value>Count</value>
  </data>
  <data name="PivotSchemaDesigner_Min" xml:space="preserve">
    <value>Min</value>
  </data>
  <data name="PivotSchemaDesigner_Max" xml:space="preserve">
    <value>Max</value>
  </data>
  <data name="PivotSchemaDesigner_DoubleSum" xml:space="preserve">
    <value>DoubleSum</value>
  </data>
  <data name="PivotSchemaDesigner_DoubleAverage" xml:space="preserve">
    <value>DoubleAverage</value>
  </data>
  <data name="PivotSchemaDesigner_DoubleMin" xml:space="preserve">
    <value>DoubleMin</value>
  </data>
  <data name="PivotSchemaDesigner_DoubleMax" xml:space="preserve">
    <value>DoubleMax</value>
  </data>
  <data name="PivotSchemaDesigner_DoubleStandardDeviation" xml:space="preserve">
    <value>DoubleStandardDeviation</value>
  </data>
  <data name="PivotSchemaDesigner_DoubleVariance" xml:space="preserve">
    <value>DoubleVariance</value>
  </data>
  <data name="PivotSchemaDesigner_DecimalSum" xml:space="preserve">
    <value>DecimalSum</value>
  </data>
  <data name="PivotSchemaDesigner_IntSum" xml:space="preserve">
    <value>IntSum</value>
  </data>
  <data name="PivotSchemaDesigner_Custom" xml:space="preserve">
    <value>Custom</value>
  </data>
  <data name="PivotSchemaDesigner_Discrete" xml:space="preserve">
    <value>Discrete</value>
  </data>
  <data name="PivotSchemaDesigner_CountNumbers" xml:space="preserve">
    <value>Count Numbers</value>
  </data>
  <data name="PivotSchemaDesigner_StdDev" xml:space="preserve">
    <value>StdDev</value>
  </data>
  <data name="PivotSchemaDesigner_StdDevP" xml:space="preserve">
    <value>StdDevP</value>
  </data>
  <data name="PivotSchemaDesigner_Variance" xml:space="preserve">
    <value>Var</value>
  </data>
  <data name="PivotSchemaDesigner_VarP" xml:space="preserve">
    <value>VarP</value>
  </data>
  <data name="PivotSchemaDesigner_SummaryOf" xml:space="preserve">
    <value>of</value>
  </data>
  <data name="PivotSchemaDesigner_PivotTableFieldList" xml:space="preserve">
    <value>PivotTable Field List</value>
  </data>
  <data name="PivotSchemaDesigner_ChooseFieldsToAddToReport" xml:space="preserve">
    <value>Choose fields to add to the report:</value>
  </data>
  <data name="PivotSchemaDesigner_DragFieldBetweenAreasBelow" xml:space="preserve">
    <value>Drag fields between areas below:</value>
  </data>
  <data name="PivotSchemaDesigner_ReportFilter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="PivotSchemaDesigner_ColumnLabel" xml:space="preserve">
    <value>Column</value>
  </data>
  <data name="PivotSchemaDesigner_RowLabel" xml:space="preserve">
    <value>Row</value>
  </data>
  <data name="PivotSchemaDesigner_Values" xml:space="preserve">
    <value>Values</value>
  </data>
  <data name="PivotSchemaDesigner_DeferLayoutUpdate" xml:space="preserve">
    <value>Defer Layout Update</value>
  </data>
  <data name="PivotSchemaDesigner_Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="PivotSchemaDesigner_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="PivotSchemaDesigner_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="PivotSchemaDesigner_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="PivotSchemaDesigner_Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="PivotSchemaDesigner_Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="PivotSchemaDesigner_AddCurrentSelectionToFilter" xml:space="preserve">
    <value>Add current selection to filter</value>
  </data>
  <data name="PivotSchemaDesigner_NotAllItemsShowing" xml:space="preserve">
    <value>Not all child nodes are shown</value>
  </data>
  <data name="PivotSchemaDesigner_EditorLinkPanelAlert" xml:space="preserve">
    <value>The members have more than 1000 items under one or more parent. Only the first 1000 items are displayed under each parent.</value>
  </data>
  <data name="PivotSchemaDesigner_NamedSetAlert" xml:space="preserve">
    <value>Named sets cannot be added to the PivotTable report at the same time. Click OK to remove ' &lt;Set 1&gt; ' named set and add ' &lt;Set 2&gt; ' named set.</value>
  </data>
  <data name="Diagram_cut" xml:space="preserve">
    <value>Cut</value>
  </data>
  <data name="Diagram_copy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="Diagram_paste" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="Diagram_undo" xml:space="preserve">
    <value>Undo</value>
  </data>
  <data name="Diagram_redo" xml:space="preserve">
    <value>Redo</value>
  </data>
  <data name="Diagram_selectAll" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="Diagram_grouping" xml:space="preserve">
    <value>Grouping</value>
  </data>
  <data name="Diagram_group" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="Diagram_ungroup" xml:space="preserve">
    <value>Ungroup</value>
  </data>
  <data name="Diagram_order" xml:space="preserve">
    <value>Order</value>
  </data>
  <data name="Diagram_bringToFront" xml:space="preserve">
    <value>Bring To Front</value>
  </data>
  <data name="Diagram_moveForward" xml:space="preserve">
    <value>Move Forward</value>
  </data>
  <data name="Diagram_sendBackward" xml:space="preserve">
    <value>Send Backward</value>
  </data>
  <data name="Diagram_sendToBack" xml:space="preserve">
    <value>Send To Back</value>
  </data>
  <data name="Chart_zoom" xml:space="preserve">
    <value>Zoom</value>
  </data>
  <data name="Chart_pan" xml:space="preserve">
    <value>Pan</value>
  </data>
  <data name="Chart_reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="RangeNavigator_intervals_quarter_longQuarters" xml:space="preserve">
    <value>Quarter</value>
  </data>
  <data name="RangeNavigator_intervals_quarter_shortQuarters" xml:space="preserve">
    <value>Q</value>
  </data>
  <data name="RangeNavigator_intervals_week_longWeeks" xml:space="preserve">
    <value>Week</value>
  </data>
  <data name="RangeNavigator_intervals_week_shortWeeks" xml:space="preserve">
    <value>W</value>
  </data>
  <data name="Map_zoomIn" xml:space="preserve">
    <value>Zoom In</value>
  </data>
  <data name="Map_zoomOut" xml:space="preserve">
    <value>Zoom Out</value>
  </data>
  <data name="Map_panTop" xml:space="preserve">
    <value>Pan Top</value>
  </data>
  <data name="Map_panBottom" xml:space="preserve">
    <value>Pan Bottom</value>
  </data>
  <data name="Map_panLeft" xml:space="preserve">
    <value>Pan Left</value>
  </data>
  <data name="Map_panRight" xml:space="preserve">
    <value>Pan Right</value>
  </data>
  <data name="Map_home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="ReportViewer_toolbar_print_headerText" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="ReportViewer_toolbar_print_contentText" xml:space="preserve">
    <value>Print the report.</value>
  </data>
  <data name="ReportViewer_toolbar_exportformat_headerText" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="ReportViewer_toolbar_exportformat_contentText" xml:space="preserve">
    <value>Select the exported file format.</value>
  </data>
  <data name="ReportViewer_toolbar_exportformat_Pdf" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="ReportViewer_toolbar_exportformat_Excel" xml:space="preserve">
    <value>Excel</value>
  </data>
  <data name="ReportViewer_toolbar_exportformat_Word" xml:space="preserve">
    <value>Word</value>
  </data>
  <data name="ReportViewer_toolbar_exportformat_Html" xml:space="preserve">
    <value>HTML</value>
  </data>
  <data name="ReportViewer_toolbar_exportformat_PPT" xml:space="preserve">
    <value>PowerPoint</value>
  </data>
  <data name="ReportViewer_toolbar_exportformat_CSV" xml:space="preserve">
    <value>CSV</value>
  </data>
  <data name="ReportViewer_toolbar_first_headerText" xml:space="preserve">
    <value>First</value>
  </data>
  <data name="ReportViewer_toolbar_first_contentText" xml:space="preserve">
    <value>Go to the first page of the report.</value>
  </data>
  <data name="ReportViewer_toolbar_previous_headerText" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="ReportViewer_toolbar_previous_contentText" xml:space="preserve">
    <value>Go to the previous page of the report.</value>
  </data>
  <data name="ReportViewer_toolbar_next_headerText" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="ReportViewer_toolbar_next_contentText" xml:space="preserve">
    <value>Go to the next page of the report.</value>
  </data>
  <data name="ReportViewer_toolbar_last_headerText" xml:space="preserve">
    <value>Last</value>
  </data>
  <data name="ReportViewer_toolbar_last_contentText" xml:space="preserve">
    <value>Go to the last page of the report.</value>
  </data>
  <data name="ReportViewer_toolbar_documentMap_headerText" xml:space="preserve">
    <value>Document Map</value>
  </data>
  <data name="ReportViewer_toolbar_documentMap_contentText" xml:space="preserve">
    <value>Show or hide the document map.</value>
  </data>
  <data name="ReportViewer_toolbar_parameter_headerText" xml:space="preserve">
    <value>Parameter</value>
  </data>
  <data name="ReportViewer_toolbar_parameter_contentText" xml:space="preserve">
    <value>Show or hide the parameters pane.</value>
  </data>
  <data name="ReportViewer_toolbar_zoomIn_headerText" xml:space="preserve">
    <value>Zoom-In</value>
  </data>
  <data name="ReportViewer_toolbar_zoomIn_contentText" xml:space="preserve">
    <value>Zoom in to the report.</value>
  </data>
  <data name="ReportViewer_toolbar_zoomOut_headerText" xml:space="preserve">
    <value>Zoom-Out</value>
  </data>
  <data name="ReportViewer_toolbar_zoomOut_contentText" xml:space="preserve">
    <value>Zoom out of the report.</value>
  </data>
  <data name="ReportViewer_toolbar_refresh_headerText" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="ReportViewer_toolbar_refresh_contentText" xml:space="preserve">
    <value>Refresh the report.</value>
  </data>
  <data name="ReportViewer_toolbar_printLayout_headerText" xml:space="preserve">
    <value>Print Layout</value>
  </data>
  <data name="ReportViewer_toolbar_printLayout_contentText" xml:space="preserve">
    <value>Change between print layout and normal modes.</value>
  </data>
  <data name="ReportViewer_toolbar_pageIndex_headerText" xml:space="preserve">
    <value>Page Number</value>
  </data>
  <data name="ReportViewer_toolbar_pageIndex_contentText" xml:space="preserve">
    <value>Current page number to view.</value>
  </data>
  <data name="ReportViewer_toolbar_zoom_headerText" xml:space="preserve">
    <value>Zoom</value>
  </data>
  <data name="ReportViewer_toolbar_zoom_contentText" xml:space="preserve">
    <value>Zoom in or out on the report.</value>
  </data>
  <data name="ReportViewer_toolbar_back_headerText" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="ReportViewer_toolbar_back_contentText" xml:space="preserve">
    <value>Go back to the parent report.</value>
  </data>
  <data name="ReportViewer_toolbar_fittopage_headerText" xml:space="preserve">
    <value>Fit to Page</value>
  </data>
  <data name="ReportViewer_toolbar_fittopage_contentText" xml:space="preserve">
    <value>Fit the report page to the container.</value>
  </data>
  <data name="ReportViewer_toolbar_fittopage_pageWidth" xml:space="preserve">
    <value>Page Width</value>
  </data>
  <data name="ReportViewer_toolbar_fittopage_pageHeight" xml:space="preserve">
    <value>Whole Page</value>
  </data>
  <data name="ReportViewer_toolbar_pagesetup_headerText" xml:space="preserve">
    <value>Page Setup</value>
  </data>
  <data name="ReportViewer_toolbar_pagesetup_contentText" xml:space="preserve">
    <value>Choose page setup option to change paper size, orientation and margins.</value>
  </data>
  <data name="ReportViewer_pagesetupDialog_close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="ReportViewer_pagesetupDialog_paperSize" xml:space="preserve">
    <value>Paper Size</value>
  </data>
  <data name="ReportViewer_pagesetupDialog_height" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="ReportViewer_pagesetupDialog_width" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="ReportViewer_pagesetupDialog_margins" xml:space="preserve">
    <value>Margins</value>
  </data>
  <data name="ReportViewer_pagesetupDialog_top" xml:space="preserve">
    <value>Top</value>
  </data>
  <data name="ReportViewer_pagesetupDialog_bottom" xml:space="preserve">
    <value>Bottom</value>
  </data>
  <data name="ReportViewer_pagesetupDialog_right" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="ReportViewer_pagesetupDialog_left" xml:space="preserve">
    <value>Left</value>
  </data>
  <data name="ReportViewer_pagesetupDialog_unit" xml:space="preserve">
    <value>in</value>
  </data>
  <data name="ReportViewer_pagesetupDialog_orientation" xml:space="preserve">
    <value>Orientation</value>
  </data>
  <data name="ReportViewer_pagesetupDialog_portrait" xml:space="preserve">
    <value>Portrait</value>
  </data>
  <data name="ReportViewer_pagesetupDialog_landscape" xml:space="preserve">
    <value>Landscape</value>
  </data>
  <data name="ReportViewer_pagesetupDialog_doneButton" xml:space="preserve">
    <value>Done</value>
  </data>
  <data name="ReportViewer_pagesetupDialog_cancelButton" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="ReportViewer_credential_userName" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="ReportViewer_credential_password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="ReportViewer_waterMark_selectOption" xml:space="preserve">
    <value>Select option</value>
  </data>
  <data name="ReportViewer_waterMark_selectValue" xml:space="preserve">
    <value>Select a value</value>
  </data>
  <data name="ReportViewer_errorMessage_startMessage" xml:space="preserve">
    <value>Report Viewer encountered some issues loading this report. Please</value>
  </data>
  <data name="ReportViewer_errorMessage_middleMessage" xml:space="preserve">
    <value> Click here</value>
  </data>
  <data name="ReportViewer_errorMessage_endMessage" xml:space="preserve">
    <value>to see the error details</value>
  </data>
  <data name="ReportViewer_errorMessage_closeMessage" xml:space="preserve">
    <value>Close this message</value>
  </data>
  <data name="ReportViewer_errorMessage_exportAjaxFailureMsg" xml:space="preserve">
    <value>Unable to export the document due to failure of connecting Report Service.</value>
  </data>
  <data name="ReportViewer_errorMessage_printAjaxFailureMsg" xml:space="preserve">
    <value>Unable to print the document due to failure of connecting Report Service.</value>
  </data>
  <data name="ReportViewer_errorMessage_reportLoadAjaxFailureMsg" xml:space="preserve">
    <value>Unable to progress the Report action due to failure of connecting Report Service.</value>
  </data>
  <data name="ReportViewer_progressMessage_exportLoadingMessage" xml:space="preserve">
    <value>Preparing exporting document... Please wait...</value>
  </data>
  <data name="ReportViewer_progressMessage_printLoadingMessage" xml:space="preserve">
    <value>Preparing print data� Please wait...</value>
  </data>
  <data name="ReportViewer_progressMessage_printPreparationMessage" xml:space="preserve">
    <value>Preparing print data... {0}% completed... Please wait...</value>
  </data>
  <data name="ReportViewer_progressMessage_exportPreparationMessage" xml:space="preserve">
    <value>Preparing exporting document... {0}% completed... Please wait...</value>
  </data>
  <data name="ReportViewer_alertMessage_close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="ReportViewer_alertMessage_title" xml:space="preserve">
    <value>ReportViewer</value>
  </data>
  <data name="ReportViewer_alertMessage_done" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="ReportViewer_alertMessage_showDetails" xml:space="preserve">
    <value>Show details</value>
  </data>
  <data name="ReportViewer_alertMessage_hideDetails" xml:space="preserve">
    <value>Hide details</value>
  </data>
  <data name="ReportViewer_alertMessage_reportLoad" xml:space="preserve">
    <value>Report loaded:</value>
  </data>
  <data name="ReportViewer_alertMessage_RVERR0001" xml:space="preserve">
    <value>ReportViewer could not load the Report</value>
  </data>
  <data name="ReportViewer_alertMessage_RVERR0002" xml:space="preserve">
    <value>ReportViewer could not process the Report</value>
  </data>
  <data name="ReportViewer_alertMessage_RVERR0003" xml:space="preserve">
    <value>An error occurred in the return of ajax data</value>
  </data>
  <data name="ReportViewer_alertMessage_RVERR0004" xml:space="preserve">
    <value>Select a value for the parameter</value>
  </data>
  <data name="ReportViewer_alertMessage_RVERR0005" xml:space="preserve">
    <value>The parameter {parameter name} is missing a value</value>
  </data>
  <data name="ReportViewer_alertMessage_RVERR0006" xml:space="preserve">
    <value>Please enter the input of the float data type</value>
  </data>
  <data name="ReportViewer_alertMessage_RVERR0007" xml:space="preserve">
    <value>Enter the integer data type entry</value>
  </data>
  <data name="ReportViewer_alertMessage_RVERR0008" xml:space="preserve">
    <value>ReportViewer could not validate Datasource credentials</value>
  </data>
  <data name="ReportViewer_alertMessage_RVERR0009" xml:space="preserve">
    <value>The margins are superimposed or are outside the paper. Enter a different margin size.</value>
  </data>
  <data name="ReportViewer_alertMessage_RVERR0010" xml:space="preserve">
    <value>Enter a value for the parameter</value>
  </data>
  <data name="ReportViewer_alertMessage_RVERR0011" xml:space="preserve">
    <value>The parameter cannot be blank</value>
  </data>
  <data name="ReportViewer_alertMessage_RVERR0012" xml:space="preserve">
    <value>The value provided for the report parameter {parameterprompt} is not valid for its type.</value>
  </data>
  <data name="ReportViewer_selectAll" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="ReportViewer_viewButton" xml:space="preserve">
    <value>View Report</value>
  </data>
  <data name="ReportViewer_parameterProcessingMessage" xml:space="preserve">
    <value>Loading dependent parameters...</value>
  </data>
  <data name="Ribbon_CustomizeQuickAccess" xml:space="preserve">
    <value>Customize Quick Access Toolbar</value>
  </data>
  <data name="Ribbon_RemoveFromQuickAccessToolbar" xml:space="preserve">
    <value>Remove from Quick Access Toolbar</value>
  </data>
  <data name="Ribbon_AddToQuickAccessToolbar" xml:space="preserve">
    <value>Add to Quick Access Toolbar</value>
  </data>
  <data name="Ribbon_ShowAboveTheRibbon" xml:space="preserve">
    <value>Show Above the Ribbon</value>
  </data>
  <data name="Ribbon_ShowBelowTheRibbon" xml:space="preserve">
    <value>Show Below the Ribbon</value>
  </data>
  <data name="Ribbon_MoreCommands" xml:space="preserve">
    <value>More Commands...</value>
  </data>
  <data name="Kanban_EmptyCard" xml:space="preserve">
    <value>No cards to display</value>
  </data>
  <data name="Kanban_SaveButton" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Kanban_CancelButton" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Kanban_EditFormTitle" xml:space="preserve">
    <value>Details of </value>
  </data>
  <data name="Kanban_AddFormTitle" xml:space="preserve">
    <value>Add New Card</value>
  </data>
  <data name="Kanban_SwimlaneCaptionFormat" xml:space="preserve">
    <value>- {{:count}}{{if count == 1 }} item {{else}} items {{/if}}</value>
  </data>
  <data name="Kanban_FilterSettings" xml:space="preserve">
    <value>Filters:</value>
  </data>
  <data name="Kanban_FilterOfText" xml:space="preserve">
    <value>of</value>
  </data>
  <data name="Kanban_Max" xml:space="preserve">
    <value>Max</value>
  </data>
  <data name="Kanban_Min" xml:space="preserve">
    <value>Min</value>
  </data>
  <data name="Kanban_Cards" xml:space="preserve">
    <value>  Cards</value>
  </data>
  <data name="Kanban_ItemsCount" xml:space="preserve">
    <value>Items Count :</value>
  </data>
  <data name="Kanban_Unassigned" xml:space="preserve">
    <value>Unassigned</value>
  </data>
  <data name="Kanban_AddCard" xml:space="preserve">
    <value>Add Card</value>
  </data>
  <data name="Kanban_EditCard" xml:space="preserve">
    <value>Edit Card</value>
  </data>
  <data name="Kanban_DeleteCard" xml:space="preserve">
    <value>Delete Card</value>
  </data>
  <data name="Kanban_TopofRow" xml:space="preserve">
    <value>Top of Row</value>
  </data>
  <data name="Kanban_BottomofRow" xml:space="preserve">
    <value>Bottom of Row</value>
  </data>
  <data name="Kanban_MoveUp" xml:space="preserve">
    <value>Move Up</value>
  </data>
  <data name="Kanban_MoveDown" xml:space="preserve">
    <value>Move Down</value>
  </data>
  <data name="Kanban_MoveLeft" xml:space="preserve">
    <value>Move Left</value>
  </data>
  <data name="Kanban_MoveRight" xml:space="preserve">
    <value>Move Right</value>
  </data>
  <data name="Kanban_MovetoSwimlane" xml:space="preserve">
    <value>Move to Swimlane</value>
  </data>
  <data name="Kanban_HideColumn" xml:space="preserve">
    <value>Hide Column</value>
  </data>
  <data name="Kanban_VisibleColumns" xml:space="preserve">
    <value>Visible Columns</value>
  </data>
  <data name="Kanban_PrintCard" xml:space="preserve">
    <value>Print Card</value>
  </data>
  <data name="RTE_bold" xml:space="preserve">
    <value>Bold</value>
  </data>
  <data name="RTE_italic" xml:space="preserve">
    <value>Italic</value>
  </data>
  <data name="RTE_underline" xml:space="preserve">
    <value>Underline</value>
  </data>
  <data name="RTE_strikethrough" xml:space="preserve">
    <value>Strikethrough</value>
  </data>
  <data name="RTE_superscript" xml:space="preserve">
    <value>Superscript</value>
  </data>
  <data name="RTE_subscript" xml:space="preserve">
    <value>Subscript</value>
  </data>
  <data name="RTE_justifyCenter" xml:space="preserve">
    <value>Align text center</value>
  </data>
  <data name="RTE_justifyLeft" xml:space="preserve">
    <value>Align text left</value>
  </data>
  <data name="RTE_justifyRight" xml:space="preserve">
    <value>Align text right</value>
  </data>
  <data name="RTE_justifyFull" xml:space="preserve">
    <value>Justify</value>
  </data>
  <data name="RTE_unorderedList" xml:space="preserve">
    <value>Insert unordered list</value>
  </data>
  <data name="RTE_orderedList" xml:space="preserve">
    <value>Insert ordered list</value>
  </data>
  <data name="RTE_indent" xml:space="preserve">
    <value>Increase Indent</value>
  </data>
  <data name="RTE_fileBrowser" xml:space="preserve">
    <value>File Browser</value>
  </data>
  <data name="RTE_outdent" xml:space="preserve">
    <value>Decrease Indent</value>
  </data>
  <data name="RTE_cut" xml:space="preserve">
    <value>Cut</value>
  </data>
  <data name="RTE_copy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="RTE_paste" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="RTE_paragraph" xml:space="preserve">
    <value>Paragraph</value>
  </data>
  <data name="RTE_undo" xml:space="preserve">
    <value>Undo</value>
  </data>
  <data name="RTE_redo" xml:space="preserve">
    <value>Redo</value>
  </data>
  <data name="RTE_upperCase" xml:space="preserve">
    <value>Upper Case</value>
  </data>
  <data name="RTE_lowerCase" xml:space="preserve">
    <value>Lower Case</value>
  </data>
  <data name="RTE_clearAll" xml:space="preserve">
    <value>Clear All</value>
  </data>
  <data name="RTE_clearFormat" xml:space="preserve">
    <value>Clear Format</value>
  </data>
  <data name="RTE_createLink" xml:space="preserve">
    <value>Insert/Edit Hyperlink</value>
  </data>
  <data name="RTE_removeLink" xml:space="preserve">
    <value>Remove Hyperlink</value>
  </data>
  <data name="RTE_tableProperties" xml:space="preserve">
    <value>Table Properties</value>
  </data>
  <data name="RTE_insertTable" xml:space="preserve">
    <value>Insert</value>
  </data>
  <data name="RTE_deleteTables" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="RTE_imageProperties" xml:space="preserve">
    <value>Image Properties</value>
  </data>
  <data name="RTE_openLink" xml:space="preserve">
    <value>Open Hyperlink</value>
  </data>
  <data name="RTE_image" xml:space="preserve">
    <value>Insert image</value>
  </data>
  <data name="RTE_video" xml:space="preserve">
    <value>Insert video</value>
  </data>
  <data name="RTE_editTable" xml:space="preserve">
    <value>Edit Table Properties</value>
  </data>
  <data name="RTE_embedVideo" xml:space="preserve">
    <value>Paste your embed code below</value>
  </data>
  <data name="RTE_viewHtml" xml:space="preserve">
    <value>View HTML</value>
  </data>
  <data name="RTE_fontName" xml:space="preserve">
    <value>Select font family</value>
  </data>
  <data name="RTE_fontSize" xml:space="preserve">
    <value>Select font size</value>
  </data>
  <data name="RTE_fontColor" xml:space="preserve">
    <value>Select color</value>
  </data>
  <data name="RTE_format" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="RTE_backgroundColor" xml:space="preserve">
    <value>Background color</value>
  </data>
  <data name="RTE_style" xml:space="preserve">
    <value>Styles</value>
  </data>
  <data name="RTE_deleteAlert" xml:space="preserve">
    <value>Are you sure you want to clear all the contents?</value>
  </data>
  <data name="RTE_copyAlert" xml:space="preserve">
    <value>Your browser doesn't support direct access to the clipboard. Please use the Ctrl+C keyboard shortcut instead of copy operation.</value>
  </data>
  <data name="RTE_pasteAlert" xml:space="preserve">
    <value>Your browser doesn't support direct access to the clipboard. Please use the Ctrl+V keyboard shortcut instead of paste operation.</value>
  </data>
  <data name="RTE_cutAlert" xml:space="preserve">
    <value>Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X keyboard shortcut instead of cut operation.</value>
  </data>
  <data name="RTE_videoError" xml:space="preserve">
    <value>The text area can not be empty</value>
  </data>
  <data name="RTE_imageWebUrl" xml:space="preserve">
    <value>Web Address</value>
  </data>
  <data name="RTE_imageAltText" xml:space="preserve">
    <value>Alternate text</value>
  </data>
  <data name="RTE_dimensions" xml:space="preserve">
    <value>Dimensions</value>
  </data>
  <data name="RTE_constrainProportions" xml:space="preserve">
    <value>Constrain Proportions</value>
  </data>
  <data name="RTE_linkWebUrl" xml:space="preserve">
    <value>Web Address</value>
  </data>
  <data name="RTE_imageLink" xml:space="preserve">
    <value>Image as Link</value>
  </data>
  <data name="RTE_imageBorder" xml:space="preserve">
    <value>Image Border</value>
  </data>
  <data name="RTE_imageStyle" xml:space="preserve">
    <value>Style</value>
  </data>
  <data name="RTE_linkText" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="RTE_linkTooltipLabel" xml:space="preserve">
    <value>Tooltip</value>
  </data>
  <data name="RTE_html5Support" xml:space="preserve">
    <value>This tool icon only enabled in HTML5 supported browsers</value>
  </data>
  <data name="RTE_linkOpenInNewWindow" xml:space="preserve">
    <value>Open link in new window</value>
  </data>
  <data name="RTE_tableColumns" xml:space="preserve">
    <value>No.of Columns</value>
  </data>
  <data name="RTE_tableRows" xml:space="preserve">
    <value>No.of Rows</value>
  </data>
  <data name="RTE_tableWidth" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="RTE_tableHeight" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="RTE_tableCellSpacing" xml:space="preserve">
    <value>Cell spacing</value>
  </data>
  <data name="RTE_tableCellPadding" xml:space="preserve">
    <value>Cell padding</value>
  </data>
  <data name="RTE_tableBorder" xml:space="preserve">
    <value>Border</value>
  </data>
  <data name="RTE_tableCaption" xml:space="preserve">
    <value>Caption</value>
  </data>
  <data name="RTE_tableAlignment" xml:space="preserve">
    <value>Alignment</value>
  </data>
  <data name="RTE_textAlign" xml:space="preserve">
    <value>Text align</value>
  </data>
  <data name="RTE_dialogUpdate" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="RTE_dialogInsert" xml:space="preserve">
    <value>Insert</value>
  </data>
  <data name="RTE_dialogCancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="RTE_dialogApply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="RTE_dialogOk" xml:space="preserve">
    <value>Ok</value>
  </data>
  <data name="RTE_createTable" xml:space="preserve">
    <value>Insert Table</value>
  </data>
  <data name="RTE_addColumnLeft" xml:space="preserve">
    <value>Insert Columns to the Left</value>
  </data>
  <data name="RTE_addColumnRight" xml:space="preserve">
    <value>Insert Columns to the Right</value>
  </data>
  <data name="RTE_addRowAbove" xml:space="preserve">
    <value>Insert Rows Above</value>
  </data>
  <data name="RTE_addRowBelow" xml:space="preserve">
    <value>Insert Rows Below</value>
  </data>
  <data name="RTE_deleteRow" xml:space="preserve">
    <value>Delete entire row</value>
  </data>
  <data name="RTE_deleteColumn" xml:space="preserve">
    <value>Delete entire column</value>
  </data>
  <data name="RTE_deleteTable" xml:space="preserve">
    <value>Delete Table</value>
  </data>
  <data name="RTE_customTable" xml:space="preserve">
    <value>Create custom table...</value>
  </data>
  <data name="RTE_characters" xml:space="preserve">
    <value>Characters</value>
  </data>
  <data name="RTE_words" xml:space="preserve">
    <value>Words</value>
  </data>
  <data name="RTE_general" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="RTE_advanced" xml:space="preserve">
    <value>Advanced</value>
  </data>
  <data name="RTE_table" xml:space="preserve">
    <value>Table</value>
  </data>
  <data name="RTE_row" xml:space="preserve">
    <value>Row</value>
  </data>
  <data name="RTE_column" xml:space="preserve">
    <value>Column</value>
  </data>
  <data name="RTE_cell" xml:space="preserve">
    <value>Cell</value>
  </data>
  <data name="RTE_solid" xml:space="preserve">
    <value>Solid</value>
  </data>
  <data name="RTE_dotted" xml:space="preserve">
    <value>Dotted</value>
  </data>
  <data name="RTE_dashed" xml:space="preserve">
    <value>Dashed</value>
  </data>
  <data name="RTE_doubled" xml:space="preserve">
    <value>Double</value>
  </data>
  <data name="RTE_maximize" xml:space="preserve">
    <value>Maximize</value>
  </data>
  <data name="RTE_resize" xml:space="preserve">
    <value>Minimize</value>
  </data>
  <data name="RTE_swatches" xml:space="preserve">
    <value>Swatches</value>
  </data>
  <data name="RTE_quotation" xml:space="preserve">
    <value>Quotation</value>
  </data>
  <data name="RTE_heading1" xml:space="preserve">
    <value>Heading 1</value>
  </data>
  <data name="RTE_heading2" xml:space="preserve">
    <value>Heading 2</value>
  </data>
  <data name="RTE_heading3" xml:space="preserve">
    <value>Heading 3</value>
  </data>
  <data name="RTE_heading4" xml:space="preserve">
    <value>Heading 4</value>
  </data>
  <data name="RTE_heading5" xml:space="preserve">
    <value>Heading 5</value>
  </data>
  <data name="RTE_heading6" xml:space="preserve">
    <value>Heading 6</value>
  </data>
  <data name="RTE_segoeui" xml:space="preserve">
    <value>Segoe UI</value>
  </data>
  <data name="RTE_arial" xml:space="preserve">
    <value>Arial</value>
  </data>
  <data name="RTE_couriernew" xml:space="preserve">
    <value>Courier New</value>
  </data>
  <data name="RTE_georgia" xml:space="preserve">
    <value>Georgia</value>
  </data>
  <data name="RTE_impact" xml:space="preserve">
    <value>Impact</value>
  </data>
  <data name="RTE_lucidaconsole" xml:space="preserve">
    <value>Lucida Console</value>
  </data>
  <data name="RTE_tahoma" xml:space="preserve">
    <value>Tahoma</value>
  </data>
  <data name="RTE_timesnewroman" xml:space="preserve">
    <value>Times New Roman</value>
  </data>
  <data name="RTE_trebuchetms" xml:space="preserve">
    <value>Trebuchet MS</value>
  </data>
  <data name="RTE_verdana" xml:space="preserve">
    <value>Verdana</value>
  </data>
  <data name="RTE_disc" xml:space="preserve">
    <value>Disc</value>
  </data>
  <data name="RTE_circle" xml:space="preserve">
    <value>Circle</value>
  </data>
  <data name="RTE_square" xml:space="preserve">
    <value>Square</value>
  </data>
  <data name="RTE_number" xml:space="preserve">
    <value>Number</value>
  </data>
  <data name="RTE_loweralpha" xml:space="preserve">
    <value>Lower Alpha</value>
  </data>
  <data name="RTE_upperalpha" xml:space="preserve">
    <value>Upper Alpha</value>
  </data>
  <data name="RTE_lowerroman" xml:space="preserve">
    <value>Lower Roman</value>
  </data>
  <data name="RTE_upperroman" xml:space="preserve">
    <value>Upper Roman</value>
  </data>
  <data name="RTE_none" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="RTE_linkTooltip" xml:space="preserve">
    <value>ctrl + click to follow link</value>
  </data>
  <data name="RTE_charSpace" xml:space="preserve">
    <value>Characters (with spaces)</value>
  </data>
  <data name="RTE_charNoSpace" xml:space="preserve">
    <value>Characters (no spaces)</value>
  </data>
  <data name="RTE_wordCount" xml:space="preserve">
    <value>Word Count</value>
  </data>
  <data name="RTE_left" xml:space="preserve">
    <value>Left</value>
  </data>
  <data name="RTE_right" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="RTE_center" xml:space="preserve">
    <value>Center</value>
  </data>
  <data name="RTE_zoomIn" xml:space="preserve">
    <value>Zoom In</value>
  </data>
  <data name="RTE_zoomOut" xml:space="preserve">
    <value>Zoom Out</value>
  </data>
  <data name="RTE_print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="RTE_import" xml:space="preserve">
    <value>Import a Document</value>
  </data>
  <data name="RTE_wordExport" xml:space="preserve">
    <value>Export as Word Document</value>
  </data>
  <data name="RTE_pdfExport" xml:space="preserve">
    <value>Export as Pdf File</value>
  </data>
  <data name="RTE_FindAndReplace" xml:space="preserve">
    <value>Find and Replace</value>
  </data>
  <data name="RTE_Find" xml:space="preserve">
    <value>Find</value>
  </data>
  <data name="RTE_MatchCase" xml:space="preserve">
    <value>Match Case</value>
  </data>
  <data name="RTE_WholeWord" xml:space="preserve">
    <value>Whole Word</value>
  </data>
  <data name="RTE_ReplaceWith" xml:space="preserve">
    <value>Replace with</value>
  </data>
  <data name="RTE_Replace" xml:space="preserve">
    <value>Replace</value>
  </data>
  <data name="RTE_ReplaceAll" xml:space="preserve">
    <value>Replace All</value>
  </data>
  <data name="RTE_FindErrorMsg" xml:space="preserve">
    <value>Couldn't find specified word.</value>
  </data>
  <data name="RTE_customFontColor" xml:space="preserve">
    <value>More Colors...</value>
  </data>
  <data name="RTE_customBGColor" xml:space="preserve">
    <value>More Colors...</value>
  </data>
  <data name="RTE_TransBGColor" xml:space="preserve">
    <value>Transparent</value>
  </data>
  <data name="RTE_addtodictionary" xml:space="preserve">
    <value>Add to Dictionary</value>
  </data>
  <data name="RTE_ignoreall" xml:space="preserve">
    <value>IgnoreAll</value>
  </data>
  <data name="RecurrenceEditor_Repeat" xml:space="preserve">
    <value>Repeat</value>
  </data>
  <data name="RecurrenceEditor_Never" xml:space="preserve">
    <value>Never</value>
  </data>
  <data name="RecurrenceEditor_Daily" xml:space="preserve">
    <value>Daily</value>
  </data>
  <data name="RecurrenceEditor_Weekly" xml:space="preserve">
    <value>Weekly</value>
  </data>
  <data name="RecurrenceEditor_Monthly" xml:space="preserve">
    <value>Monthly</value>
  </data>
  <data name="RecurrenceEditor_Yearly" xml:space="preserve">
    <value>Yearly</value>
  </data>
  <data name="RecurrenceEditor_First" xml:space="preserve">
    <value>First</value>
  </data>
  <data name="RecurrenceEditor_Second" xml:space="preserve">
    <value>Second</value>
  </data>
  <data name="RecurrenceEditor_Third" xml:space="preserve">
    <value>Third</value>
  </data>
  <data name="RecurrenceEditor_Fourth" xml:space="preserve">
    <value>Fourth</value>
  </data>
  <data name="RecurrenceEditor_Last" xml:space="preserve">
    <value>Last</value>
  </data>
  <data name="RecurrenceEditor_EveryWeekDay" xml:space="preserve">
    <value>Every weekday</value>
  </data>
  <data name="RecurrenceEditor_Every" xml:space="preserve">
    <value>Every</value>
  </data>
  <data name="RecurrenceEditor_RecurrenceDay" xml:space="preserve">
    <value>Day(s)</value>
  </data>
  <data name="RecurrenceEditor_RecurrenceWeek" xml:space="preserve">
    <value>Week(s)</value>
  </data>
  <data name="RecurrenceEditor_RecurrenceMonth" xml:space="preserve">
    <value>Month(s)</value>
  </data>
  <data name="RecurrenceEditor_RecurrenceYear" xml:space="preserve">
    <value>Year(s)</value>
  </data>
  <data name="RecurrenceEditor_RepeatOn" xml:space="preserve">
    <value>Repeat on</value>
  </data>
  <data name="RecurrenceEditor_RepeatBy" xml:space="preserve">
    <value>Repeat by</value>
  </data>
  <data name="RecurrenceEditor_StartsOn" xml:space="preserve">
    <value>Starts on</value>
  </data>
  <data name="RecurrenceEditor_Times" xml:space="preserve">
    <value>times</value>
  </data>
  <data name="RecurrenceEditor_Ends" xml:space="preserve">
    <value>Ends</value>
  </data>
  <data name="RecurrenceEditor_Day" xml:space="preserve">
    <value>Day</value>
  </data>
  <data name="RecurrenceEditor_The" xml:space="preserve">
    <value>The</value>
  </data>
  <data name="RecurrenceEditor_OfEvery" xml:space="preserve">
    <value>Of</value>
  </data>
  <data name="RecurrenceEditor_After" xml:space="preserve">
    <value>After</value>
  </data>
  <data name="RecurrenceEditor_On" xml:space="preserve">
    <value>On</value>
  </data>
  <data name="RecurrenceEditor_Occurrence" xml:space="preserve">
    <value>Occurrence(s)</value>
  </data>
  <data name="RecurrenceEditor_Until" xml:space="preserve">
    <value>Until</value>
  </data>
  <data name="Schedule_ReminderWindowTitle" xml:space="preserve">
    <value>Reminder window</value>
  </data>
  <data name="Schedule_CreateAppointmentTitle" xml:space="preserve">
    <value>Create Appointment</value>
  </data>
  <data name="Schedule_RecurrenceEditTitle" xml:space="preserve">
    <value>Edit Repeat Appointment</value>
  </data>
  <data name="Schedule_RecurrenceEditMessage" xml:space="preserve">
    <value>How would you like to change the appointment in the series?</value>
  </data>
  <data name="Schedule_RecurrenceEditOnly" xml:space="preserve">
    <value>Only this appointment</value>
  </data>
  <data name="Schedule_RecurrenceEditFollowingEvent" xml:space="preserve">
    <value>Following Events</value>
  </data>
  <data name="Schedule_RecurrenceEditSeries" xml:space="preserve">
    <value>Entire series</value>
  </data>
  <data name="Schedule_PreviousAppointment" xml:space="preserve">
    <value>Previous Appointment</value>
  </data>
  <data name="Schedule_NextAppointment" xml:space="preserve">
    <value>Next Appointment</value>
  </data>
  <data name="Schedule_AppointmentSubject" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="Schedule_StartTime" xml:space="preserve">
    <value>Start Time</value>
  </data>
  <data name="Schedule_EndTime" xml:space="preserve">
    <value>End Time</value>
  </data>
  <data name="Schedule_AllDay" xml:space="preserve">
    <value>All day</value>
  </data>
  <data name="Schedule_StartTimeZone" xml:space="preserve">
    <value>Start TimeZone</value>
  </data>
  <data name="Schedule_EndTimeZone" xml:space="preserve">
    <value>End TimeZone</value>
  </data>
  <data name="Schedule_Today" xml:space="preserve">
    <value>Today</value>
  </data>
  <data name="Schedule_Recurrence" xml:space="preserve">
    <value>Repeat</value>
  </data>
  <data name="Schedule_Done" xml:space="preserve">
    <value>Done</value>
  </data>
  <data name="Schedule_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Schedule_Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Schedule_Repeat" xml:space="preserve">
    <value>Repeat</value>
  </data>
  <data name="Schedule_RepeatBy" xml:space="preserve">
    <value>Repeat by</value>
  </data>
  <data name="Schedule_RepeatEvery" xml:space="preserve">
    <value>Repeat every</value>
  </data>
  <data name="Schedule_RepeatOn" xml:space="preserve">
    <value>Repeat on</value>
  </data>
  <data name="Schedule_StartsOn" xml:space="preserve">
    <value>Starts on</value>
  </data>
  <data name="Schedule_Ends" xml:space="preserve">
    <value>Ends</value>
  </data>
  <data name="Schedule_Summary" xml:space="preserve">
    <value>Summary</value>
  </data>
  <data name="Schedule_Daily" xml:space="preserve">
    <value>Daily</value>
  </data>
  <data name="Schedule_Weekly" xml:space="preserve">
    <value>Weekly</value>
  </data>
  <data name="Schedule_Monthly" xml:space="preserve">
    <value>Monthly</value>
  </data>
  <data name="Schedule_Yearly" xml:space="preserve">
    <value>Yearly</value>
  </data>
  <data name="Schedule_Every" xml:space="preserve">
    <value>Every</value>
  </data>
  <data name="Schedule_EveryWeekDay" xml:space="preserve">
    <value>Every weekday</value>
  </data>
  <data name="Schedule_Never" xml:space="preserve">
    <value>Never</value>
  </data>
  <data name="Schedule_After" xml:space="preserve">
    <value>After</value>
  </data>
  <data name="Schedule_Occurrence" xml:space="preserve">
    <value>Occurrence(s)</value>
  </data>
  <data name="Schedule_On" xml:space="preserve">
    <value>On</value>
  </data>
  <data name="Schedule_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Schedule_RecurrenceDay" xml:space="preserve">
    <value>Day(s)</value>
  </data>
  <data name="Schedule_RecurrenceWeek" xml:space="preserve">
    <value>Week(s)</value>
  </data>
  <data name="Schedule_RecurrenceMonth" xml:space="preserve">
    <value>Month(s)</value>
  </data>
  <data name="Schedule_RecurrenceYear" xml:space="preserve">
    <value>Year(s)</value>
  </data>
  <data name="Schedule_The" xml:space="preserve">
    <value>The</value>
  </data>
  <data name="Schedule_OfEvery" xml:space="preserve">
    <value>of every</value>
  </data>
  <data name="Schedule_First" xml:space="preserve">
    <value>First</value>
  </data>
  <data name="Schedule_Second" xml:space="preserve">
    <value>Second</value>
  </data>
  <data name="Schedule_Third" xml:space="preserve">
    <value>Third</value>
  </data>
  <data name="Schedule_Fourth" xml:space="preserve">
    <value>Fourth</value>
  </data>
  <data name="Schedule_Last" xml:space="preserve">
    <value>Last</value>
  </data>
  <data name="Schedule_WeekDay" xml:space="preserve">
    <value>Weekday</value>
  </data>
  <data name="Schedule_WeekEndDay" xml:space="preserve">
    <value>Weekendday</value>
  </data>
  <data name="Schedule_Subject" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="Schedule_Categorize" xml:space="preserve">
    <value>Categories</value>
  </data>
  <data name="Schedule_DueIn" xml:space="preserve">
    <value>Due In</value>
  </data>
  <data name="Schedule_DismissAll" xml:space="preserve">
    <value>Dismiss All</value>
  </data>
  <data name="Schedule_Dismiss" xml:space="preserve">
    <value>Dismiss</value>
  </data>
  <data name="Schedule_OpenItem" xml:space="preserve">
    <value>Open Item</value>
  </data>
  <data name="Schedule_Snooze" xml:space="preserve">
    <value>Snooze</value>
  </data>
  <data name="Schedule_Day" xml:space="preserve">
    <value>Ημέρα</value>
  </data>
  <data name="Schedule_Week" xml:space="preserve">
    <value>Week</value>
  </data>
  <data name="Schedule_WorkWeek" xml:space="preserve">
    <value>Work Week</value>
  </data>
  <data name="Schedule_Month" xml:space="preserve">
    <value>Month</value>
  </data>
  <data name="Schedule_AddEvent" xml:space="preserve">
    <value>Add Event</value>
  </data>
  <data name="Schedule_CustomView" xml:space="preserve">
    <value>Custom View</value>
  </data>
  <data name="Schedule_Agenda" xml:space="preserve">
    <value>Agenda</value>
  </data>
  <data name="Schedule_Detailed" xml:space="preserve">
    <value>Edit Appointment</value>
  </data>
  <data name="Schedule_EventBeginsin" xml:space="preserve">
    <value>Appointment begins in</value>
  </data>
  <data name="Schedule_Editevent" xml:space="preserve">
    <value>Edit Appointment</value>
  </data>
  <data name="Schedule_Editfollowingevent" xml:space="preserve">
    <value>Following Events</value>
  </data>
  <data name="Schedule_Editseries" xml:space="preserve">
    <value>Edit Series</value>
  </data>
  <data name="Schedule_Times" xml:space="preserve">
    <value>times</value>
  </data>
  <data name="Schedule_Until" xml:space="preserve">
    <value>until</value>
  </data>
  <data name="Schedule_Eventwas" xml:space="preserve">
    <value>Appointment was</value>
  </data>
  <data name="Schedule_Hours" xml:space="preserve">
    <value>hrs</value>
  </data>
  <data name="Schedule_Minutes" xml:space="preserve">
    <value>mins</value>
  </data>
  <data name="Schedule_Overdue" xml:space="preserve">
    <value>Overdue Appointment</value>
  </data>
  <data name="Schedule_Days" xml:space="preserve">
    <value>day(s)</value>
  </data>
  <data name="Schedule_Event" xml:space="preserve">
    <value>Event</value>
  </data>
  <data name="Schedule_Select" xml:space="preserve">
    <value>select</value>
  </data>
  <data name="Schedule_Previous" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="Schedule_Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="Schedule_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Schedule_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Schedule_Date" xml:space="preserve">
    <value>Ημερομηνία</value>
  </data>
  <data name="Schedule_Showin" xml:space="preserve">
    <value>Show in</value>
  </data>
  <data name="Schedule_Gotodate" xml:space="preserve">
    <value>Πήγαινε στη ημερομηνία</value>
  </data>
  <data name="Schedule_Resources" xml:space="preserve">
    <value>RESOURCES</value>
  </data>
  <data name="Schedule_RecurrenceDeleteTitle" xml:space="preserve">
    <value>Delete Repeat Appointment</value>
  </data>
  <data name="Schedule_Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="Schedule_Priority" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="Schedule_RecurrenceAlert" xml:space="preserve">
    <value>Alert</value>
  </data>
  <data name="Schedule_NoTitle" xml:space="preserve">
    <value>No Title</value>
  </data>
  <data name="Schedule_OverFlowAppCount" xml:space="preserve">
    <value>more appointment(s)</value>
  </data>
  <data name="Schedule_AppointmentIndicator" xml:space="preserve">
    <value>Click for more appointments</value>
  </data>
  <data name="Schedule_WrongPattern" xml:space="preserve">
    <value>The recurrence pattern is not valid</value>
  </data>
  <data name="Schedule_CreateError" xml:space="preserve">
    <value>The duration of the appointment must be shorter than how frequently it occurs. Shorten the duration, or change the recurrence pattern in the Appointment Recurrence dialog box.</value>
  </data>
  <data name="Schedule_DragResizeError" xml:space="preserve">
    <value>Cannot reschedule an occurrence of the recurring appointment if it skips over a later occurrence of the same appointment.</value>
  </data>
  <data name="Schedule_StartEndError" xml:space="preserve">
    <value>The selected end date occurs before the start date.</value>
  </data>
  <data name="Schedule_MouseOverDeleteTitle" xml:space="preserve">
    <value>Delete Appointment</value>
  </data>
  <data name="Schedule_DeleteConfirmation" xml:space="preserve">
    <value>Are you sure you want to delete this appointment?</value>
  </data>
  <data name="Schedule_Time" xml:space="preserve">
    <value>Time</value>
  </data>
  <data name="Schedule_EmptyResultText" xml:space="preserve">
    <value>No suggestions</value>
  </data>
  <data name="Schedule_BlockIntervalAlertTitle" xml:space="preserve">
    <value>Alert</value>
  </data>
  <data name="Schedule_BlockIntervalError" xml:space="preserve">
    <value>The selected time interval has been blocked and is unavailable for selection.</value>
  </data>
  <data name="Schedule_RecurrenceDateValidation" xml:space="preserve">
    <value>Some months have fewer than selected dates. For these months, the occurrence will fall on the last date of the month.</value>
  </data>
  <data name="Schedule_SeriesChangeAlert" xml:space="preserve">
    <value>The changes made to specific instances of this series will be cancelled and those appointments will match the series again.</value>
  </data>
  <data name="Spreadsheet_Cut" xml:space="preserve">
    <value>Cut</value>
  </data>
  <data name="Spreadsheet_Copy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="Spreadsheet_FormatPainter" xml:space="preserve">
    <value>Format Painter</value>
  </data>
  <data name="Spreadsheet_Paste" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="Spreadsheet_PasteValues" xml:space="preserve">
    <value>Paste Values Only</value>
  </data>
  <data name="Spreadsheet_PasteSpecial" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="Spreadsheet_Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="Spreadsheet_FilterContent" xml:space="preserve">
    <value>Turn on filtering for the selected cells.</value>
  </data>
  <data name="Spreadsheet_FilterSelected" xml:space="preserve">
    <value>Filter by Selected Cell's value</value>
  </data>
  <data name="Spreadsheet_Sort" xml:space="preserve">
    <value>Sort</value>
  </data>
  <data name="Spreadsheet_Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="Spreadsheet_ClearContent" xml:space="preserve">
    <value>Delete everything in the cell, or remove just the formatting, contents, comments or hyperlinks.</value>
  </data>
  <data name="Spreadsheet_ClearFilter" xml:space="preserve">
    <value>Clear Filter</value>
  </data>
  <data name="Spreadsheet_ClearFilterContent" xml:space="preserve">
    <value>Clear the filter and sort state for the current range of data.</value>
  </data>
  <data name="Spreadsheet_SortAtoZ" xml:space="preserve">
    <value>Sort A to Z</value>
  </data>
  <data name="Spreadsheet_SortAtoZContent" xml:space="preserve">
    <value>Lowest to Highest.</value>
  </data>
  <data name="Spreadsheet_SortZtoA" xml:space="preserve">
    <value>Sort Z to A</value>
  </data>
  <data name="Spreadsheet_SortZtoAContent" xml:space="preserve">
    <value>Highest to Lowest.</value>
  </data>
  <data name="Spreadsheet_SortSmallesttoLargest" xml:space="preserve">
    <value>Sort Smallest to Largest</value>
  </data>
  <data name="Spreadsheet_SortLargesttoSmallest" xml:space="preserve">
    <value>Sort Largest to Smallest</value>
  </data>
  <data name="Spreadsheet_SortOldesttoNewest" xml:space="preserve">
    <value>Sort Oldest to Newest</value>
  </data>
  <data name="Spreadsheet_SortNewesttoOldest" xml:space="preserve">
    <value>Sort Newest to Oldest</value>
  </data>
  <data name="Spreadsheet_Insert" xml:space="preserve">
    <value>Insert</value>
  </data>
  <data name="Spreadsheet_InsertTitle" xml:space="preserve">
    <value>Insert Cells</value>
  </data>
  <data name="Spreadsheet_InsertContent" xml:space="preserve">
    <value>Add new cells, rows, or columns to your workbook.</value>
  </data>
  <data name="Spreadsheet_MultipleInsertContent" xml:space="preserve">
    <value>FYI: To insert multiple rows or columns at a time, select multiple rows or columns in the sheet, and click Insert.</value>
  </data>
  <data name="Spreadsheet_InsertSBContent" xml:space="preserve">
    <value>Add cells, rows, columns, or sheets to your workbook.</value>
  </data>
  <data name="Spreadsheet_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Spreadsheet_DeleteTitle" xml:space="preserve">
    <value>Delete Cells</value>
  </data>
  <data name="Spreadsheet_DeleteContent" xml:space="preserve">
    <value>Delete cells, rows, columns, or sheets from your workbook.</value>
  </data>
  <data name="Spreadsheet_MultipleDeleteContent" xml:space="preserve">
    <value>FYI: To delete multiple rows or columns at a time, select multiple rows or columns in the sheet, and click Delete.</value>
  </data>
  <data name="Spreadsheet_FindSelectTitle" xml:space="preserve">
    <value>Find &amp; Select</value>
  </data>
  <data name="Spreadsheet_FindSelectContent" xml:space="preserve">
    <value>Click to see options for finding text in your document.</value>
  </data>
  <data name="Spreadsheet_CalculationOptions" xml:space="preserve">
    <value>Calculation Options</value>
  </data>
  <data name="Spreadsheet_CalcOptTitle" xml:space="preserve">
    <value>Calculation Options</value>
  </data>
  <data name="Spreadsheet_CalcOptContent" xml:space="preserve">
    <value>Choose to calculate formulas automatically or manually.</value>
  </data>
  <data name="Spreadsheet_CalcOptRecalcContent" xml:space="preserve">
    <value>If you make a change that affects a value, Spreadsheet will automatically recalculate it.</value>
  </data>
  <data name="Spreadsheet_CalculateSheet" xml:space="preserve">
    <value>Calculate Sheet</value>
  </data>
  <data name="Spreadsheet_CalculateNow" xml:space="preserve">
    <value>Calculate Now</value>
  </data>
  <data name="Spreadsheet_CalculateNowContent" xml:space="preserve">
    <value>Calculate the entire workbook now.</value>
  </data>
  <data name="Spreadsheet_CalculateNowTurnOffContent" xml:space="preserve">
    <value>You only need to use this if automatic calculation is turned off.</value>
  </data>
  <data name="Spreadsheet_CalculateSheetContent" xml:space="preserve">
    <value>Calculate the active sheet now.</value>
  </data>
  <data name="Spreadsheet_CalculateSheetTurnOffContent" xml:space="preserve">
    <value>You only need to use this if automatic calculation is turned off.</value>
  </data>
  <data name="Spreadsheet_Title" xml:space="preserve">
    <value>Spreadsheet</value>
  </data>
  <data name="Spreadsheet_ColorPicker" xml:space="preserve">
    <value>Color Picker</value>
  </data>
  <data name="Spreadsheet_Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Spreadsheet_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Spreadsheet_Alert" xml:space="preserve">
    <value>We couldn't do this for the selected range of cells. Select a single cell within a range of data and then try again.</value>
  </data>
  <data name="Spreadsheet_HeaderAlert" xml:space="preserve">
    <value>The command could not be completed as you are attempting to filter with the filter header. Select a single cell in the filter range and try the command again.</value>
  </data>
  <data name="Spreadsheet_FlashFillAlert" xml:space="preserve">
    <value>All the data next to your selection was checked and there was no pattern for filling in values.</value>
  </data>
  <data name="Spreadsheet_Formatcells" xml:space="preserve">
    <value>Format Cells</value>
  </data>
  <data name="Spreadsheet_FontFamily" xml:space="preserve">
    <value>Font</value>
  </data>
  <data name="Spreadsheet_FFContent" xml:space="preserve">
    <value>Pick a new font for your text.</value>
  </data>
  <data name="Spreadsheet_FontSize" xml:space="preserve">
    <value>Font Size</value>
  </data>
  <data name="Spreadsheet_FSContent" xml:space="preserve">
    <value>Change the size of your text.</value>
  </data>
  <data name="Spreadsheet_IncreaseFontSize" xml:space="preserve">
    <value>Increase Font Size</value>
  </data>
  <data name="Spreadsheet_IFSContent" xml:space="preserve">
    <value>Make your text a bit bigger.</value>
  </data>
  <data name="Spreadsheet_DecreaseFontSize" xml:space="preserve">
    <value>Decrease Font Size</value>
  </data>
  <data name="Spreadsheet_DFSContent" xml:space="preserve">
    <value>Make your text a bit smaller.</value>
  </data>
  <data name="Spreadsheet_Bold" xml:space="preserve">
    <value>Bold</value>
  </data>
  <data name="Spreadsheet_Italic" xml:space="preserve">
    <value>Italic</value>
  </data>
  <data name="Spreadsheet_Underline" xml:space="preserve">
    <value>Underline</value>
  </data>
  <data name="Spreadsheet_Linethrough" xml:space="preserve">
    <value>Linethrough</value>
  </data>
  <data name="Spreadsheet_FillColor" xml:space="preserve">
    <value>Fill Color</value>
  </data>
  <data name="Spreadsheet_FontColor" xml:space="preserve">
    <value>Font Color</value>
  </data>
  <data name="Spreadsheet_TopAlign" xml:space="preserve">
    <value>Top Align</value>
  </data>
  <data name="Spreadsheet_TopAlignContent" xml:space="preserve">
    <value>Align text to the top.</value>
  </data>
  <data name="Spreadsheet_MiddleAlign" xml:space="preserve">
    <value>Middle Align</value>
  </data>
  <data name="Spreadsheet_MiddleAlignContent" xml:space="preserve">
    <value>Align text so that it is centered between the top and bottom of the cell.</value>
  </data>
  <data name="Spreadsheet_BottomAlign" xml:space="preserve">
    <value>Bottom Align</value>
  </data>
  <data name="Spreadsheet_BottomAlignContent" xml:space="preserve">
    <value>Align text to the bottom.</value>
  </data>
  <data name="Spreadsheet_WrapText" xml:space="preserve">
    <value>Wrap Text</value>
  </data>
  <data name="Spreadsheet_WrapTextContent" xml:space="preserve">
    <value>Wrap extra-long text into multiple lines so you can see all of it.</value>
  </data>
  <data name="Spreadsheet_AlignLeft" xml:space="preserve">
    <value>Align Left</value>
  </data>
  <data name="Spreadsheet_AlignLeftContent" xml:space="preserve">
    <value>Align your content to the left.</value>
  </data>
  <data name="Spreadsheet_AlignCenter" xml:space="preserve">
    <value>Center</value>
  </data>
  <data name="Spreadsheet_AlignCenterContent" xml:space="preserve">
    <value>Center your content.</value>
  </data>
  <data name="Spreadsheet_AlignRight" xml:space="preserve">
    <value>Align Right</value>
  </data>
  <data name="Spreadsheet_AlignRightContent" xml:space="preserve">
    <value>Align your content to the right.</value>
  </data>
  <data name="Spreadsheet_IncreaseIndent" xml:space="preserve">
    <value>Increase Indent</value>
  </data>
  <data name="Spreadsheet_IncreaseIndentContent" xml:space="preserve">
    <value>Move your content farther away from the cell border.</value>
  </data>
  <data name="Spreadsheet_DecreaseIndent" xml:space="preserve">
    <value>Decrease Indent</value>
  </data>
  <data name="Spreadsheet_DecreaseIndentContent" xml:space="preserve">
    <value>Move your content closer to the cell border.</value>
  </data>
  <data name="Spreadsheet_Undo" xml:space="preserve">
    <value>Undo</value>
  </data>
  <data name="Spreadsheet_Redo" xml:space="preserve">
    <value>Redo</value>
  </data>
  <data name="Spreadsheet_NumberFormat" xml:space="preserve">
    <value>Number Format</value>
  </data>
  <data name="Spreadsheet_NumberFormatContent" xml:space="preserve">
    <value>Choose the format for your cells, such as percentage, currency, date or time.</value>
  </data>
  <data name="Spreadsheet_AccountingStyle" xml:space="preserve">
    <value>Accounting Style</value>
  </data>
  <data name="Spreadsheet_AccountingStyleContent" xml:space="preserve">
    <value>Format as dollar accounting number format.</value>
  </data>
  <data name="Spreadsheet_PercentageStyle" xml:space="preserve">
    <value>Percent Style</value>
  </data>
  <data name="Spreadsheet_PercentageStyleContent" xml:space="preserve">
    <value>Format as a percent.</value>
  </data>
  <data name="Spreadsheet_CommaStyle" xml:space="preserve">
    <value>Comma Style</value>
  </data>
  <data name="Spreadsheet_CommaStyleContent" xml:space="preserve">
    <value>Format with a thousands separator.</value>
  </data>
  <data name="Spreadsheet_IncreaseDecimal" xml:space="preserve">
    <value>Increase Decimal</value>
  </data>
  <data name="Spreadsheet_IncreaseDecimalContent" xml:space="preserve">
    <value>Show more decimal places for a more precise value.</value>
  </data>
  <data name="Spreadsheet_DecreaseDecimal" xml:space="preserve">
    <value>Decrease Decimal</value>
  </data>
  <data name="Spreadsheet_DecreaseDecimalContent" xml:space="preserve">
    <value>Show fewer decimal places.</value>
  </data>
  <data name="Spreadsheet_AutoSum" xml:space="preserve">
    <value>AutoSum</value>
  </data>
  <data name="Spreadsheet_AutoSumTitle" xml:space="preserve">
    <value>Sum</value>
  </data>
  <data name="Spreadsheet_AutoSumContent" xml:space="preserve">
    <value>Automatically add a quick calculation to your worksheet, such as sum or average.</value>
  </data>
  <data name="Spreadsheet_Fill" xml:space="preserve">
    <value>Fill</value>
  </data>
  <data name="Spreadsheet_ExportXL" xml:space="preserve">
    <value>Excel</value>
  </data>
  <data name="Spreadsheet_ExportCsv" xml:space="preserve">
    <value>CSV</value>
  </data>
  <data name="Spreadsheet_ExportPdf" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="Spreadsheet_BackgroundColor" xml:space="preserve">
    <value>Fill Color</value>
  </data>
  <data name="Spreadsheet_BGContent" xml:space="preserve">
    <value>Color the background of the cells to make them stand out.</value>
  </data>
  <data name="Spreadsheet_ColorContent" xml:space="preserve">
    <value>Change the color of your text.</value>
  </data>
  <data name="Spreadsheet_Border" xml:space="preserve">
    <value>Border</value>
  </data>
  <data name="Spreadsheet_BorderContent" xml:space="preserve">
    <value>Apply borders to the currently selected cells.</value>
  </data>
  <data name="Spreadsheet_BottomBorder" xml:space="preserve">
    <value>Bottom Border</value>
  </data>
  <data name="Spreadsheet_TopBorder" xml:space="preserve">
    <value>Top Border</value>
  </data>
  <data name="Spreadsheet_LeftBorder" xml:space="preserve">
    <value>Left Border</value>
  </data>
  <data name="Spreadsheet_RightBorder" xml:space="preserve">
    <value>Right Border</value>
  </data>
  <data name="Spreadsheet_OutsideBorder" xml:space="preserve">
    <value>Outside Borders</value>
  </data>
  <data name="Spreadsheet_NoBorder" xml:space="preserve">
    <value>No Border</value>
  </data>
  <data name="Spreadsheet_AllBorder" xml:space="preserve">
    <value>All Borders</value>
  </data>
  <data name="Spreadsheet_ThickBoxBorder" xml:space="preserve">
    <value>Thick Box Border</value>
  </data>
  <data name="Spreadsheet_ThickBottomBorder" xml:space="preserve">
    <value>Thick Bottom Border</value>
  </data>
  <data name="Spreadsheet_TopandThickBottomBorder" xml:space="preserve">
    <value>Top and Thick Bottom Border</value>
  </data>
  <data name="Spreadsheet_BottomDoubleBorder" xml:space="preserve">
    <value>Bottom Double Border</value>
  </data>
  <data name="Spreadsheet_TopandBottomDoubleBorder" xml:space="preserve">
    <value>Top and Bottom Double Border</value>
  </data>
  <data name="Spreadsheet_DrawBorderGrid" xml:space="preserve">
    <value>Draw Border Grid</value>
  </data>
  <data name="Spreadsheet_DrawBorder" xml:space="preserve">
    <value>Draw Border</value>
  </data>
  <data name="Spreadsheet_TopandBottomBorder" xml:space="preserve">
    <value>Top and Bottom Border</value>
  </data>
  <data name="Spreadsheet_BorderColor" xml:space="preserve">
    <value>Line Color</value>
  </data>
  <data name="Spreadsheet_BorderStyle" xml:space="preserve">
    <value>Line Style</value>
  </data>
  <data name="Spreadsheet_Number" xml:space="preserve">
    <value>Number is used for general display of numbers. Currency and Accounting offer specialized formatting for monetary value.</value>
  </data>
  <data name="Spreadsheet_General" xml:space="preserve">
    <value>General format cells have no specific number format.</value>
  </data>
  <data name="Spreadsheet_Currency" xml:space="preserve">
    <value>Currency formats are used for general monetary values. Use Accounting formats to align decimal points in a column.</value>
  </data>
  <data name="Spreadsheet_Accounting" xml:space="preserve">
    <value>Accounting formats line up the currency symbols and decimal points in a column.</value>
  </data>
  <data name="Spreadsheet_Text" xml:space="preserve">
    <value>Text format cells are treated as text even when a number is in the cell.  The cell is displayed exactly as entered.</value>
  </data>
  <data name="Spreadsheet_Percentage" xml:space="preserve">
    <value>Percentage formats multiply the cell value by 100 and displays the result with a percent symbol.</value>
  </data>
  <data name="Spreadsheet_CustomMessage" xml:space="preserve">
    <value>Type number format code, using one of existing codes as a starting point.</value>
  </data>
  <data name="Spreadsheet_Fraction" xml:space="preserve">
    <value>Fraction is used to indicate a part of a whole number or a ratio between two numbers.</value>
  </data>
  <data name="Spreadsheet_Scientific" xml:space="preserve">
    <value>Scientific is used to represent a decimal number between 1 and 10 multiplied by ten, so the large numbers using less digits.</value>
  </data>
  <data name="Spreadsheet_Type" xml:space="preserve">
    <value>Type:</value>
  </data>
  <data name="Spreadsheet_CustomFormatAlert" xml:space="preserve">
    <value>Enter a valid format</value>
  </data>
  <data name="Spreadsheet_Date" xml:space="preserve">
    <value>Date formats display date and time serial numbers as date values.</value>
  </data>
  <data name="Spreadsheet_Time" xml:space="preserve">
    <value>Time formats display date and time serial numbers as date values.</value>
  </data>
  <data name="Spreadsheet_File" xml:space="preserve">
    <value>FILE</value>
  </data>
  <data name="Spreadsheet_New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="Spreadsheet_Open" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="Spreadsheet_SaveAs" xml:space="preserve">
    <value>Save As</value>
  </data>
  <data name="Spreadsheet_Print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="Spreadsheet_PrintContent" xml:space="preserve">
    <value>Print the current sheet.</value>
  </data>
  <data name="Spreadsheet_PrintSheet" xml:space="preserve">
    <value>Print Sheet</value>
  </data>
  <data name="Spreadsheet_PrintSelected" xml:space="preserve">
    <value>Print Selected</value>
  </data>
  <data name="Spreadsheet_PrintSelectedContent" xml:space="preserve">
    <value>Select an area on the sheet you would like to print.</value>
  </data>
  <data name="Spreadsheet_HighlightVal" xml:space="preserve">
    <value>Format Invalid Data</value>
  </data>
  <data name="Spreadsheet_ClearVal" xml:space="preserve">
    <value>Clear Validation</value>
  </data>
  <data name="Spreadsheet_Validation" xml:space="preserve">
    <value>Validation</value>
  </data>
  <data name="Spreadsheet_DataValidation" xml:space="preserve">
    <value>Data Validation</value>
  </data>
  <data name="Spreadsheet_DVContent" xml:space="preserve">
    <value>Pick from a list of rules to limit the type of data that can be entered in a cell.</value>
  </data>
  <data name="Spreadsheet_PageSize" xml:space="preserve">
    <value>Page Size</value>
  </data>
  <data name="Spreadsheet_PageSizeContent" xml:space="preserve">
    <value>Choose a page size for your document.</value>
  </data>
  <data name="Spreadsheet_ConditionalFormat" xml:space="preserve">
    <value>Conditional Formatting</value>
  </data>
  <data name="Spreadsheet_CFContent" xml:space="preserve">
    <value>Easily spot trends and patterns in your data using colors to visually highlight important values.</value>
  </data>
  <data name="Spreadsheet_And" xml:space="preserve">
    <value>and</value>
  </data>
  <data name="Spreadsheet_With" xml:space="preserve">
    <value>with</value>
  </data>
  <data name="Spreadsheet_GTTitle" xml:space="preserve">
    <value>Greater Than</value>
  </data>
  <data name="Spreadsheet_GTContent" xml:space="preserve">
    <value>Format cells that are GREATER THAN:</value>
  </data>
  <data name="Spreadsheet_LTTitle" xml:space="preserve">
    <value>Less Than</value>
  </data>
  <data name="Spreadsheet_LTContent" xml:space="preserve">
    <value>Format cells that are LESS THAN:</value>
  </data>
  <data name="Spreadsheet_BWTitle" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="Spreadsheet_BWContent" xml:space="preserve">
    <value>Format cells that are BETWEEN:</value>
  </data>
  <data name="Spreadsheet_EQTitle" xml:space="preserve">
    <value>Equal To</value>
  </data>
  <data name="Spreadsheet_EQContent" xml:space="preserve">
    <value>Format cells that are EQUAL TO:</value>
  </data>
  <data name="Spreadsheet_DateTitle" xml:space="preserve">
    <value>A Date Occurring</value>
  </data>
  <data name="Spreadsheet_DateContent" xml:space="preserve">
    <value>Format cells that contain a DATE:</value>
  </data>
  <data name="Spreadsheet_ContainsTitle" xml:space="preserve">
    <value>Text That Contains</value>
  </data>
  <data name="Spreadsheet_ContainsContent" xml:space="preserve">
    <value>Format cells that contain the text:</value>
  </data>
  <data name="Spreadsheet_GreaterThan" xml:space="preserve">
    <value>Greater Than</value>
  </data>
  <data name="Spreadsheet_LessThan" xml:space="preserve">
    <value>Less Than</value>
  </data>
  <data name="Spreadsheet_Between" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="Spreadsheet_EqualTo" xml:space="preserve">
    <value>Equal To</value>
  </data>
  <data name="Spreadsheet_TextthatContains" xml:space="preserve">
    <value>Text that Contains</value>
  </data>
  <data name="Spreadsheet_DateOccurring" xml:space="preserve">
    <value>A Date Occurring</value>
  </data>
  <data name="Spreadsheet_ClearRules" xml:space="preserve">
    <value>Clear Rules</value>
  </data>
  <data name="Spreadsheet_ClearRulesfromSelected" xml:space="preserve">
    <value>Clear Rules from Selected Cells</value>
  </data>
  <data name="Spreadsheet_ClearRulesfromEntireSheets" xml:space="preserve">
    <value>Clear Rules from Entire Sheet</value>
  </data>
  <data name="Spreadsheet_CellStyles" xml:space="preserve">
    <value>Cell Styles</value>
  </data>
  <data name="Spreadsheet_CellStylesContent" xml:space="preserve">
    <value>A colorful style is a great way to make important data stand out on the sheet.</value>
  </data>
  <data name="Spreadsheet_CellStyleHeaderText" xml:space="preserve">
    <value>Good, Bad and Neutral/Titles and Headings/Themed Cell Styles</value>
  </data>
  <data name="Spreadsheet_Custom" xml:space="preserve">
    <value>Type the number format code, using one of the existing codes as starting point.</value>
  </data>
  <data name="Spreadsheet_CellStyleGBN" xml:space="preserve">
    <value>Normal/Bad/Good/Neutral</value>
  </data>
  <data name="Spreadsheet_CellStyleTH" xml:space="preserve">
    <value>Heading 4/Title</value>
  </data>
  <data name="Spreadsheet_Accent" xml:space="preserve">
    <value>Accent</value>
  </data>
  <data name="Spreadsheet_Style" xml:space="preserve">
    <value>Style</value>
  </data>
  <data name="Spreadsheet_FormatAsTable" xml:space="preserve">
    <value>Format As Table</value>
  </data>
  <data name="Spreadsheet_FATContent" xml:space="preserve">
    <value>Quickly convert a range of cells to a table with its own style.</value>
  </data>
  <data name="Spreadsheet_FATHeaderText" xml:space="preserve">
    <value>Light/Medium/Dark</value>
  </data>
  <data name="Spreadsheet_FATNameDlgText" xml:space="preserve">
    <value>Table Name :/My table has headers</value>
  </data>
  <data name="Spreadsheet_InvalidReference" xml:space="preserve">
    <value>The range you have specified is invalid</value>
  </data>
  <data name="Spreadsheet_ResizeAlert" xml:space="preserve">
    <value>The specified range is invalid. The top of the table must remain in the same row, and the resulting table must overlap the original table. Specify a valid range.</value>
  </data>
  <data name="Spreadsheet_RangeNotCreated" xml:space="preserve">
    <value>Increasing the row beyond the maximum sheet row count is restricted in Format as Table.</value>
  </data>
  <data name="Spreadsheet_ResizeRestrictAlert" xml:space="preserve">
    <value>Increase or decrease of column count and decrease of row count is restricted in Format as Table.</value>
  </data>
  <data name="Spreadsheet_FATResizeTableText" xml:space="preserve">
    <value>Enter new data range for your table:</value>
  </data>
  <data name="Spreadsheet_FATReizeTableNote" xml:space="preserve">
    <value>Note: The headers must remain in the same row and the resulting table range must overlap the original table range.</value>
  </data>
  <data name="Spreadsheet_FormatAsTableAlert" xml:space="preserve">
    <value>Cannot create a table with a single row. A table must have at least two rows, one for the table header, and one for data</value>
  </data>
  <data name="Spreadsheet_FormatAsTableTitle" xml:space="preserve">
    <value>Light 1/Light 2/Light 3/Light 4/Light 5/Light 6/Light 7/Light 8/Light 9/Light 10/Light 11/Light 12/Medium 1/Medium 2/Medium 3/Medium 4/Medium 5/Medium 6/Medium 7/Medium 8/Dark 1/Dark 2/Dark 3/Dark 4</value>
  </data>
  <data name="Spreadsheet_NewTableStyle" xml:space="preserve">
    <value>New Table Style</value>
  </data>
  <data name="Spreadsheet_ResizeTable" xml:space="preserve">
    <value>Resize Table</value>
  </data>
  <data name="Spreadsheet_ResizeTableContent" xml:space="preserve">
    <value>Resize this table by adding or removing rows and columns.</value>
  </data>
  <data name="Spreadsheet_ConvertToRange" xml:space="preserve">
    <value>Convert to Range</value>
  </data>
  <data name="Spreadsheet_ConvertToRangeContent" xml:space="preserve">
    <value>Convert this table into a normal range of cells.</value>
  </data>
  <data name="Spreadsheet_ConverToRangeAlert" xml:space="preserve">
    <value>Do you want to convert the table to a normal range?</value>
  </data>
  <data name="Spreadsheet_TableID" xml:space="preserve">
    <value>Table ID:</value>
  </data>
  <data name="Spreadsheet_Table" xml:space="preserve">
    <value>Table</value>
  </data>
  <data name="Spreadsheet_TableContent" xml:space="preserve">
    <value>Create a table to organize and analyze related data.</value>
  </data>
  <data name="Spreadsheet_TableStyleOptions" xml:space="preserve">
    <value>First Column/Last Column/Total Row/Filter Button</value>
  </data>
  <data name="Spreadsheet_Format" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="Spreadsheet_NameManager" xml:space="preserve">
    <value>Name Manager</value>
  </data>
  <data name="Spreadsheet_NameManagerContent" xml:space="preserve">
    <value>Create, edit, delete and find all the names used in the workbook.</value>
  </data>
  <data name="Spreadsheet_NameManagerFormulaContent" xml:space="preserve">
    <value>Names can be used in formulas as substitutes for cell references.</value>
  </data>
  <data name="Spreadsheet_DefinedNames" xml:space="preserve">
    <value>Defined Names</value>
  </data>
  <data name="Spreadsheet_DefineName" xml:space="preserve">
    <value>Define Name</value>
  </data>
  <data name="Spreadsheet_DefineNameContent" xml:space="preserve">
    <value>Define and apply names.</value>
  </data>
  <data name="Spreadsheet_UseInFormula" xml:space="preserve">
    <value>Use In Formula</value>
  </data>
  <data name="Spreadsheet_UseInFormulaContent" xml:space="preserve">
    <value>Choose a name used in this workbook and insert it into the current formula.</value>
  </data>
  <data name="Spreadsheet_RefersTo" xml:space="preserve">
    <value>Refers To</value>
  </data>
  <data name="Spreadsheet_Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Spreadsheet_Scope" xml:space="preserve">
    <value>Scope</value>
  </data>
  <data name="Spreadsheet_NMUniqueNameAlert" xml:space="preserve">
    <value>The name entered already exists. Enter a unique name.</value>
  </data>
  <data name="Spreadsheet_NMRangeAlert" xml:space="preserve">
    <value>Enter a valid range</value>
  </data>
  <data name="Spreadsheet_FORMULAS" xml:space="preserve">
    <value>FORMULAS</value>
  </data>
  <data name="Spreadsheet_Value" xml:space="preserve">
    <value>Values</value>
  </data>
  <data name="Spreadsheet_DataValue" xml:space="preserve">
    <value>Values:</value>
  </data>
  <data name="Spreadsheet_Formula" xml:space="preserve">
    <value>Formulas</value>
  </data>
  <data name="Spreadsheet_MissingParenthesisAlert" xml:space="preserve">
    <value>Your formula is missing a parenthesis--) or (. Check the formula, and then add the parenthesis in the appropriate place.</value>
  </data>
  <data name="Spreadsheet_UnsupportedFile" xml:space="preserve">
    <value>Unsupported File</value>
  </data>
  <data name="Spreadsheet_IncorrectPassword" xml:space="preserve">
    <value>Unable to open the file or worksheet with the given password</value>
  </data>
  <data name="Spreadsheet_InvalidUrl" xml:space="preserve">
    <value>Please specify proper URL</value>
  </data>
  <data name="Spreadsheet_Up" xml:space="preserve">
    <value>Up</value>
  </data>
  <data name="Spreadsheet_Down" xml:space="preserve">
    <value>Down</value>
  </data>
  <data name="Spreadsheet_Sheet" xml:space="preserve">
    <value>Sheet</value>
  </data>
  <data name="Spreadsheet_Workbook" xml:space="preserve">
    <value>Workbook</value>
  </data>
  <data name="Spreadsheet_Rows" xml:space="preserve">
    <value>By Rows</value>
  </data>
  <data name="Spreadsheet_Columns" xml:space="preserve">
    <value>By Columns</value>
  </data>
  <data name="Spreadsheet_FindReplace" xml:space="preserve">
    <value>Find Replace</value>
  </data>
  <data name="Spreadsheet_FindnReplace" xml:space="preserve">
    <value>Find and Replace</value>
  </data>
  <data name="Spreadsheet_Find" xml:space="preserve">
    <value>Find</value>
  </data>
  <data name="Spreadsheet_Replace" xml:space="preserve">
    <value>Replace</value>
  </data>
  <data name="Spreadsheet_FindLabel" xml:space="preserve">
    <value>Find what:</value>
  </data>
  <data name="Spreadsheet_ReplaceLabel" xml:space="preserve">
    <value>Replace with:</value>
  </data>
  <data name="Spreadsheet_ReplaceAll" xml:space="preserve">
    <value>Replace All</value>
  </data>
  <data name="Spreadsheet_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Spreadsheet_FindNext" xml:space="preserve">
    <value>Find Next</value>
  </data>
  <data name="Spreadsheet_FindPrev" xml:space="preserve">
    <value>Find Prev</value>
  </data>
  <data name="Spreadsheet_Automatic" xml:space="preserve">
    <value>Automatic</value>
  </data>
  <data name="Spreadsheet_Manual" xml:space="preserve">
    <value>Manual</value>
  </data>
  <data name="Spreadsheet_Settings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="Spreadsheet_MatchCase" xml:space="preserve">
    <value>Match case</value>
  </data>
  <data name="Spreadsheet_MatchAll" xml:space="preserve">
    <value>Match entire cell contents</value>
  </data>
  <data name="Spreadsheet_Within" xml:space="preserve">
    <value>Within:</value>
  </data>
  <data name="Spreadsheet_Search" xml:space="preserve">
    <value>Search:</value>
  </data>
  <data name="Spreadsheet_Lookin" xml:space="preserve">
    <value>Look in:</value>
  </data>
  <data name="Spreadsheet_ShiftRight" xml:space="preserve">
    <value>Shift cells right</value>
  </data>
  <data name="Spreadsheet_ShiftBottom" xml:space="preserve">
    <value>Shift cells down</value>
  </data>
  <data name="Spreadsheet_EntireRow" xml:space="preserve">
    <value>Entire row</value>
  </data>
  <data name="Spreadsheet_EntireColumn" xml:space="preserve">
    <value>Entire column</value>
  </data>
  <data name="Spreadsheet_ShiftUp" xml:space="preserve">
    <value>Shift cells up</value>
  </data>
  <data name="Spreadsheet_ShiftLeft" xml:space="preserve">
    <value>Shift cells left</value>
  </data>
  <data name="Spreadsheet_Direction" xml:space="preserve">
    <value>Direction:</value>
  </data>
  <data name="Spreadsheet_GoTo" xml:space="preserve">
    <value>Go To</value>
  </data>
  <data name="Spreadsheet_GoToName" xml:space="preserve">
    <value>Go to:</value>
  </data>
  <data name="Spreadsheet_Reference" xml:space="preserve">
    <value>Reference:</value>
  </data>
  <data name="Spreadsheet_Special" xml:space="preserve">
    <value>Special</value>
  </data>
  <data name="Spreadsheet_Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="Spreadsheet_Comments" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="Spreadsheet_Constants" xml:space="preserve">
    <value>Constants</value>
  </data>
  <data name="Spreadsheet_RowDiff" xml:space="preserve">
    <value>Row differences</value>
  </data>
  <data name="Spreadsheet_ColDiff" xml:space="preserve">
    <value>Column differences</value>
  </data>
  <data name="Spreadsheet_LastCell" xml:space="preserve">
    <value>Last cell</value>
  </data>
  <data name="Spreadsheet_CFormat" xml:space="preserve">
    <value>Conditional formats</value>
  </data>
  <data name="Spreadsheet_Blanks" xml:space="preserve">
    <value>Blanks</value>
  </data>
  <data name="Spreadsheet_GotoError" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="Spreadsheet_GotoLogicals" xml:space="preserve">
    <value>Logicals</value>
  </data>
  <data name="Spreadsheet_GotoNumbers" xml:space="preserve">
    <value>Numbers</value>
  </data>
  <data name="Spreadsheet_GotoText" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="Spreadsheet_FindSelect" xml:space="preserve">
    <value>Find &amp; Select</value>
  </data>
  <data name="Spreadsheet_Comment" xml:space="preserve">
    <value>Comment</value>
  </data>
  <data name="Spreadsheet_NewComment" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="Spreadsheet_InsertComment" xml:space="preserve">
    <value>Insert Comment</value>
  </data>
  <data name="Spreadsheet_EditComment" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Spreadsheet_DeleteComment" xml:space="preserve">
    <value>Delete Comment</value>
  </data>
  <data name="Spreadsheet_DeleteCommentContent" xml:space="preserve">
    <value>Delete the selected comment.</value>
  </data>
  <data name="Spreadsheet_HideComment" xml:space="preserve">
    <value>Hide Comment</value>
  </data>
  <data name="Spreadsheet_Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="Spreadsheet_NextContent" xml:space="preserve">
    <value>Jump to the next comment.</value>
  </data>
  <data name="Spreadsheet_Previous" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="Spreadsheet_PreviousContent" xml:space="preserve">
    <value>Jump to the previous comment.</value>
  </data>
  <data name="Spreadsheet_ShowHide" xml:space="preserve">
    <value>Show/Hide Comment</value>
  </data>
  <data name="Spreadsheet_ShowHideContent" xml:space="preserve">
    <value>Show or hide the comment on the active cell.</value>
  </data>
  <data name="Spreadsheet_ShowAll" xml:space="preserve">
    <value>Show All Comments</value>
  </data>
  <data name="Spreadsheet_ShowAllContent" xml:space="preserve">
    <value>Display all comments in the sheet.</value>
  </data>
  <data name="Spreadsheet_UserName" xml:space="preserve">
    <value>User Name</value>
  </data>
  <data name="Spreadsheet_Hide" xml:space="preserve">
    <value>Hide</value>
  </data>
  <data name="Spreadsheet_Unhide" xml:space="preserve">
    <value>Unhide</value>
  </data>
  <data name="Spreadsheet_Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Spreadsheet_DropAlert" xml:space="preserve">
    <value>Do you want to replace the existing data?</value>
  </data>
  <data name="Spreadsheet_PutCellColor" xml:space="preserve">
    <value>Put Selected Cell Color To The Top</value>
  </data>
  <data name="Spreadsheet_PutFontColor" xml:space="preserve">
    <value>Put Selected Font Color To The Top</value>
  </data>
  <data name="Spreadsheet_WebPage" xml:space="preserve">
    <value>Web Page</value>
  </data>
  <data name="Spreadsheet_WorkSheet" xml:space="preserve">
    <value>Worksheet Reference</value>
  </data>
  <data name="Spreadsheet_SheetReference" xml:space="preserve">
    <value>Sheet Reference</value>
  </data>
  <data name="Spreadsheet_InsertHyperLink" xml:space="preserve">
    <value>Insert Hyperlink</value>
  </data>
  <data name="Spreadsheet_HyperLink" xml:space="preserve">
    <value>Hyperlink</value>
  </data>
  <data name="Spreadsheet_EditLink" xml:space="preserve">
    <value>Editlink</value>
  </data>
  <data name="Spreadsheet_OpenLink" xml:space="preserve">
    <value>Openlink</value>
  </data>
  <data name="Spreadsheet_HyperlinkText" xml:space="preserve">
    <value>Text:</value>
  </data>
  <data name="Spreadsheet_RemoveLink" xml:space="preserve">
    <value>Removelink</value>
  </data>
  <data name="Spreadsheet_WebAddress" xml:space="preserve">
    <value>Web Address:</value>
  </data>
  <data name="Spreadsheet_CellAddress" xml:space="preserve">
    <value>Cell Reference:</value>
  </data>
  <data name="Spreadsheet_SheetIndex" xml:space="preserve">
    <value>Select a place in this document</value>
  </data>
  <data name="Spreadsheet_ClearAll" xml:space="preserve">
    <value>Clear All</value>
  </data>
  <data name="Spreadsheet_ClearFormats" xml:space="preserve">
    <value>Clear Formats</value>
  </data>
  <data name="Spreadsheet_ClearContents" xml:space="preserve">
    <value>Clear Contents</value>
  </data>
  <data name="Spreadsheet_ClearComments" xml:space="preserve">
    <value>Clear Comments</value>
  </data>
  <data name="Spreadsheet_ClearHyperLinks" xml:space="preserve">
    <value>Clear Hyperlinks</value>
  </data>
  <data name="Spreadsheet_SortFilter" xml:space="preserve">
    <value>Sort &amp; Filter</value>
  </data>
  <data name="Spreadsheet_SortFilterContent" xml:space="preserve">
    <value>Organize your data so it's easier to analyze.</value>
  </data>
  <data name="Spreadsheet_NumberStart" xml:space="preserve">
    <value>Minimum:</value>
  </data>
  <data name="Spreadsheet_NumberEnd" xml:space="preserve">
    <value>Maximum:</value>
  </data>
  <data name="Spreadsheet_DecimalStart" xml:space="preserve">
    <value>Minimum:</value>
  </data>
  <data name="Spreadsheet_DecimalEnd" xml:space="preserve">
    <value>Maximum:</value>
  </data>
  <data name="Spreadsheet_DateStart" xml:space="preserve">
    <value>Start Date:</value>
  </data>
  <data name="Spreadsheet_DateEnd" xml:space="preserve">
    <value>End Date:</value>
  </data>
  <data name="Spreadsheet_ListStart" xml:space="preserve">
    <value>Source:</value>
  </data>
  <data name="Spreadsheet_FreeText" xml:space="preserve">
    <value>Show error alert after invalid data is entered</value>
  </data>
  <data name="Spreadsheet_ListEnd" xml:space="preserve">
    <value>Cell Reference:</value>
  </data>
  <data name="Spreadsheet_TimeStart" xml:space="preserve">
    <value>Start Time:</value>
  </data>
  <data name="Spreadsheet_TimeEnd" xml:space="preserve">
    <value>End Time:</value>
  </data>
  <data name="Spreadsheet_TextLengthStart" xml:space="preserve">
    <value>Minimum:</value>
  </data>
  <data name="Spreadsheet_TextLengthEnd" xml:space="preserve">
    <value>Maximum:</value>
  </data>
  <data name="Spreadsheet_CommentFindEndAlert" xml:space="preserve">
    <value>Spreadsheet reached the end of the workbook. Do you want to continue reviewing from the beginning of the workbook?</value>
  </data>
  <data name="Spreadsheet_InsertSheet" xml:space="preserve">
    <value>Insert</value>
  </data>
  <data name="Spreadsheet_DeleteSheet" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Spreadsheet_RenameSheet" xml:space="preserve">
    <value>Rename</value>
  </data>
  <data name="Spreadsheet_MoveorCopy" xml:space="preserve">
    <value>Move or Copy</value>
  </data>
  <data name="Spreadsheet_HideSheet" xml:space="preserve">
    <value>Hide</value>
  </data>
  <data name="Spreadsheet_UnhideSheet" xml:space="preserve">
    <value>Unhide</value>
  </data>
  <data name="Spreadsheet_SheetRenameAlert" xml:space="preserve">
    <value>That name is already taken. Try a different one.</value>
  </data>
  <data name="Spreadsheet_SheetRenameEmptyAlert" xml:space="preserve">
    <value>You typed an invalid name for a sheet. Make sure that:</value>
  </data>
  <data name="Spreadsheet_SheetRenameEmptyCharExceedAlert" xml:space="preserve">
    <value>The name that you type does not exceed 31 characters.</value>
  </data>
  <data name="Spreadsheet_SheetRenameEmptySplCharAlert" xml:space="preserve">
    <value>The name does not contain any of the following characters:</value>
  </data>
  <data name="Spreadsheet_SheetRenameEmptyBlankAlert" xml:space="preserve">
    <value>You did not leave the name blank.</value>
  </data>
  <data name="Spreadsheet_SheetDeleteAlert" xml:space="preserve">
    <value>You can't undo deleting sheets, and you might be removing some data. If you don't need it, click OK to delete.</value>
  </data>
  <data name="Spreadsheet_SheetDeleteErrorAlert" xml:space="preserve">
    <value>A workbook must contain at least one visible worksheet. To hide, delete, or move the selected sheet, you must first insert a new sheet or unhide a sheet that is already hidden.</value>
  </data>
  <data name="Spreadsheet_CtrlKeyErrorAlert" xml:space="preserve">
    <value>That command cannot be used on  multiple selections.</value>
  </data>
  <data name="Spreadsheet_ClipboardAccessError" xml:space="preserve">
    <value>Your browser can't access the clipboard, so use these shortcuts:</value>
  </data>
  <data name="Spreadsheet_MoveToEnd" xml:space="preserve">
    <value>Move To End</value>
  </data>
  <data name="Spreadsheet_Beforesheet" xml:space="preserve">
    <value>Before sheet:</value>
  </data>
  <data name="Spreadsheet_CreateaCopy" xml:space="preserve">
    <value>Create a copy</value>
  </data>
  <data name="Spreadsheet_AutoFillOptions" xml:space="preserve">
    <value>Copy Cells/Fill Series/Fill Formatting Only/Fill Without Formatting/Flash Fill</value>
  </data>
  <data name="Spreadsheet_NumberValidationMsg" xml:space="preserve">
    <value>Enter only digits</value>
  </data>
  <data name="Spreadsheet_DateValidationMsg" xml:space="preserve">
    <value>Enter only date</value>
  </data>
  <data name="Spreadsheet_Required" xml:space="preserve">
    <value>Required</value>
  </data>
  <data name="Spreadsheet_TimeValidationMsg" xml:space="preserve">
    <value>The value you entered for the Time is invalid.</value>
  </data>
  <data name="Spreadsheet_CellAddrsValidationMsg" xml:space="preserve">
    <value>Reference is not valid.</value>
  </data>
  <data name="Spreadsheet_PivotTable" xml:space="preserve">
    <value>Pivot Table</value>
  </data>
  <data name="Spreadsheet_PivotTableContent" xml:space="preserve">
    <value>Easily arrange and summarize complex data in a PivotTable.</value>
  </data>
  <data name="Spreadsheet_NumberTab" xml:space="preserve">
    <value>Number</value>
  </data>
  <data name="Spreadsheet_AlignmentTab" xml:space="preserve">
    <value>Alignment</value>
  </data>
  <data name="Spreadsheet_FontTab" xml:space="preserve">
    <value>Font</value>
  </data>
  <data name="Spreadsheet_FillTab" xml:space="preserve">
    <value>Fill</value>
  </data>
  <data name="Spreadsheet_TextAlignment" xml:space="preserve">
    <value>Text alignment</value>
  </data>
  <data name="Spreadsheet_Horizontal" xml:space="preserve">
    <value>Horizontal:</value>
  </data>
  <data name="Spreadsheet_Vertical" xml:space="preserve">
    <value>Vertical:</value>
  </data>
  <data name="Spreadsheet_Indent" xml:space="preserve">
    <value>Indent</value>
  </data>
  <data name="Spreadsheet_TextControl" xml:space="preserve">
    <value>Text Control</value>
  </data>
  <data name="Spreadsheet_FontGroup" xml:space="preserve">
    <value>Font:</value>
  </data>
  <data name="Spreadsheet_FontStyle" xml:space="preserve">
    <value>Font style:</value>
  </data>
  <data name="Spreadsheet_Size" xml:space="preserve">
    <value>Size:</value>
  </data>
  <data name="Spreadsheet_PSize" xml:space="preserve">
    <value>Page size</value>
  </data>
  <data name="Spreadsheet_Effects" xml:space="preserve">
    <value>Effects:</value>
  </data>
  <data name="Spreadsheet_StrikeThrough" xml:space="preserve">
    <value>Strikethrough</value>
  </data>
  <data name="Spreadsheet_Overline" xml:space="preserve">
    <value>Overline</value>
  </data>
  <data name="Spreadsheet_NormalFont" xml:space="preserve">
    <value>Normal font</value>
  </data>
  <data name="Spreadsheet_Preview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="Spreadsheet_Line" xml:space="preserve">
    <value>Line</value>
  </data>
  <data name="Spreadsheet_Presets" xml:space="preserve">
    <value>Presets</value>
  </data>
  <data name="Spreadsheet_None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="Spreadsheet_Outline" xml:space="preserve">
    <value>Outline</value>
  </data>
  <data name="Spreadsheet_AllSide" xml:space="preserve">
    <value>All sides</value>
  </data>
  <data name="Spreadsheet_InsCells" xml:space="preserve">
    <value>Insert Cells</value>
  </data>
  <data name="Spreadsheet_InsRows" xml:space="preserve">
    <value>Insert Sheet Rows</value>
  </data>
  <data name="Spreadsheet_InsCols" xml:space="preserve">
    <value>Insert Sheet Columns</value>
  </data>
  <data name="Spreadsheet_InsSheet" xml:space="preserve">
    <value>Insert Sheet</value>
  </data>
  <data name="Spreadsheet_DelCells" xml:space="preserve">
    <value>Delete Cells</value>
  </data>
  <data name="Spreadsheet_DelRows" xml:space="preserve">
    <value>Delete Sheet Rows</value>
  </data>
  <data name="Spreadsheet_DelCols" xml:space="preserve">
    <value>Delete Sheet Columns</value>
  </data>
  <data name="Spreadsheet_DelSheet" xml:space="preserve">
    <value>Delete Sheet</value>
  </data>
  <data name="Spreadsheet_HyperLinkAlert" xml:space="preserve">
    <value>The address of this site is not valid.Check the address and try again.</value>
  </data>
  <data name="Spreadsheet_ReplaceData" xml:space="preserve">
    <value>All done. We made / replacements.</value>
  </data>
  <data name="Spreadsheet_NotFound" xml:space="preserve">
    <value>We couldn't find what you were looking for. Select settings tab for more ways to search</value>
  </data>
  <data name="Spreadsheet_Data" xml:space="preserve">
    <value>Data:</value>
  </data>
  <data name="Spreadsheet_Allow" xml:space="preserve">
    <value>Allow:</value>
  </data>
  <data name="Spreadsheet_IgnoreBlank" xml:space="preserve">
    <value>Ignore blank</value>
  </data>
  <data name="Spreadsheet_NotFind" xml:space="preserve">
    <value>Unable to find the match to replace</value>
  </data>
  <data name="Spreadsheet_FreezeTopRow" xml:space="preserve">
    <value>Freeze Top Row</value>
  </data>
  <data name="Spreadsheet_FreezeFirstColumn" xml:space="preserve">
    <value>Freeze First Column</value>
  </data>
  <data name="Spreadsheet_UnFreezePanes" xml:space="preserve">
    <value>Unfreeze Panes</value>
  </data>
  <data name="Spreadsheet_DestroyAlert" xml:space="preserve">
    <value>Are you sure you want to destroy the current workbook without saving and create a new workbook?</value>
  </data>
  <data name="Spreadsheet_ImageValAlert" xml:space="preserve">
    <value>Upload image files only</value>
  </data>
  <data name="Spreadsheet_Pictures" xml:space="preserve">
    <value>Pictures</value>
  </data>
  <data name="Spreadsheet_PicturesTitle" xml:space="preserve">
    <value>From File</value>
  </data>
  <data name="Spreadsheet_PicturesContent" xml:space="preserve">
    <value>Insert pictures from computer or from other computers that you are connected to.</value>
  </data>
  <data name="Spreadsheet_ImportAlert" xml:space="preserve">
    <value>Are you sure you want to destroy the current workbook without saving and open a new workbook?</value>
  </data>
  <data name="Spreadsheet_UnmergeCells" xml:space="preserve">
    <value>Unmerge Cells</value>
  </data>
  <data name="Spreadsheet_MergeCells" xml:space="preserve">
    <value>Merge Cells</value>
  </data>
  <data name="Spreadsheet_MergeAcross" xml:space="preserve">
    <value>Merge Across</value>
  </data>
  <data name="Spreadsheet_MergeAndCenter" xml:space="preserve">
    <value>Merge &amp; Center</value>
  </data>
  <data name="Spreadsheet_MergeAndCenterContent" xml:space="preserve">
    <value>Combine and center the contents of the selected cells in a new larger cell.</value>
  </data>
  <data name="Spreadsheet_MergeCellsAlert" xml:space="preserve">
    <value>Merging Cells keeps only upper left cell value and discards the other values.</value>
  </data>
  <data name="Spreadsheet_MergeInsertAlert" xml:space="preserve">
    <value>This operation will causes some merged cells to unmerge. Do you wish to continue?</value>
  </data>
  <data name="Spreadsheet_Axes" xml:space="preserve">
    <value>Axes</value>
  </data>
  <data name="Spreadsheet_PHAxis" xml:space="preserve">
    <value>Primary Horizontal</value>
  </data>
  <data name="Spreadsheet_PVAxis" xml:space="preserve">
    <value>Primary Vertical</value>
  </data>
  <data name="Spreadsheet_AxisTitle" xml:space="preserve">
    <value>Axis Title</value>
  </data>
  <data name="Spreadsheet_CTNone" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="Spreadsheet_CTCenter" xml:space="preserve">
    <value>Center</value>
  </data>
  <data name="Spreadsheet_CTFar" xml:space="preserve">
    <value>Far</value>
  </data>
  <data name="Spreadsheet_CTNear" xml:space="preserve">
    <value>Near</value>
  </data>
  <data name="Spreadsheet_DataLabels" xml:space="preserve">
    <value>Data Labels</value>
  </data>
  <data name="Spreadsheet_DLNone" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="Spreadsheet_DLCenter" xml:space="preserve">
    <value>Center</value>
  </data>
  <data name="Spreadsheet_DLIEnd" xml:space="preserve">
    <value>Inside End</value>
  </data>
  <data name="Spreadsheet_DLIBase" xml:space="preserve">
    <value>Inside Base</value>
  </data>
  <data name="Spreadsheet_DLOEnd" xml:space="preserve">
    <value>Outside End</value>
  </data>
  <data name="Spreadsheet_ErrorBar" xml:space="preserve">
    <value>Error Bars</value>
  </data>
  <data name="Spreadsheet_Gridline" xml:space="preserve">
    <value>Gridlines</value>
  </data>
  <data name="Spreadsheet_PMajorH" xml:space="preserve">
    <value>Primary Major Horizontal</value>
  </data>
  <data name="Spreadsheet_PMajorV" xml:space="preserve">
    <value>Primary Major Vertical</value>
  </data>
  <data name="Spreadsheet_PMinorH" xml:space="preserve">
    <value>Primary Minor Horizontal</value>
  </data>
  <data name="Spreadsheet_PMinorV" xml:space="preserve">
    <value>Primary Minor Vertical</value>
  </data>
  <data name="Spreadsheet_Legend" xml:space="preserve">
    <value>Legends</value>
  </data>
  <data name="Spreadsheet_LNone" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="Spreadsheet_LLeft" xml:space="preserve">
    <value>Left</value>
  </data>
  <data name="Spreadsheet_LRight" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="Spreadsheet_LBottom" xml:space="preserve">
    <value>Bottom</value>
  </data>
  <data name="Spreadsheet_LTop" xml:space="preserve">
    <value>Top</value>
  </data>
  <data name="Spreadsheet_ChartTitleDlgText" xml:space="preserve">
    <value>Enter Title</value>
  </data>
  <data name="Spreadsheet_ChartTitle" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="Spreadsheet_InvalidTitle" xml:space="preserve">
    <value>You typed an invalid name for the Title.</value>
  </data>
  <data name="Spreadsheet_CorrectFormat" xml:space="preserve">
    <value>Select the correct Format File</value>
  </data>
  <data name="Spreadsheet_ResetPicture" xml:space="preserve">
    <value>Reset Picture</value>
  </data>
  <data name="Spreadsheet_ResetPictureContent" xml:space="preserve">
    <value>Discard all of the formatting changes made to this picture.</value>
  </data>
  <data name="Spreadsheet_PictureBorder" xml:space="preserve">
    <value>Picture Border</value>
  </data>
  <data name="Spreadsheet_PictureBorderContent" xml:space="preserve">
    <value>Pick the color, width, and line style for the outline of your shape.</value>
  </data>
  <data name="Spreadsheet_ResetSize" xml:space="preserve">
    <value>Reset Picture &amp; Size</value>
  </data>
  <data name="Spreadsheet_Height" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="Spreadsheet_Width" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="Spreadsheet_ThemeColor" xml:space="preserve">
    <value>Theme Colors</value>
  </data>
  <data name="Spreadsheet_NoOutline" xml:space="preserve">
    <value>No Outline</value>
  </data>
  <data name="Spreadsheet_Weight" xml:space="preserve">
    <value>Weight</value>
  </data>
  <data name="Spreadsheet_Dashes" xml:space="preserve">
    <value>Dashes</value>
  </data>
  <data name="Spreadsheet_ColumnChart" xml:space="preserve">
    <value>Column</value>
  </data>
  <data name="Spreadsheet_ColumnChartTitle" xml:space="preserve">
    <value>Insert Column Chart</value>
  </data>
  <data name="Spreadsheet_ColumnChartContent" xml:space="preserve">
    <value>Use this chart type to visually compare values across a few categories.</value>
  </data>
  <data name="Spreadsheet_BarChart" xml:space="preserve">
    <value>Bar</value>
  </data>
  <data name="Spreadsheet_BarChartTitle" xml:space="preserve">
    <value>Insert Bar Chart</value>
  </data>
  <data name="Spreadsheet_BarChartContent" xml:space="preserve">
    <value>Use this chart type to visually compare values across a few categories when the chart shows duration or the category text is long.</value>
  </data>
  <data name="Spreadsheet_StockChart" xml:space="preserve">
    <value>Radar</value>
  </data>
  <data name="Spreadsheet_StockChartTitle" xml:space="preserve">
    <value>Insert Radar Chart</value>
  </data>
  <data name="Spreadsheet_StockChartContent" xml:space="preserve">
    <value>Use this chart type to show values relative to a center point.</value>
  </data>
  <data name="Spreadsheet_LineChart" xml:space="preserve">
    <value>Line</value>
  </data>
  <data name="Spreadsheet_LineChartTitle" xml:space="preserve">
    <value>Insert Line Chart</value>
  </data>
  <data name="Spreadsheet_LineChartContent" xml:space="preserve">
    <value>Use this chart type to show trends over time (years, months, and days) or categories.</value>
  </data>
  <data name="Spreadsheet_AreaChart" xml:space="preserve">
    <value>Area</value>
  </data>
  <data name="Spreadsheet_AreaChartTitle" xml:space="preserve">
    <value>Insert Area Chart</value>
  </data>
  <data name="Spreadsheet_AreaChartContent" xml:space="preserve">
    <value>Use this chart type to show trends over time (years, months, and days) or categories. Use it to highlight the magnitude of change over time.</value>
  </data>
  <data name="Spreadsheet_ComboChart" xml:space="preserve">
    <value>Combo</value>
  </data>
  <data name="Spreadsheet_PieChart" xml:space="preserve">
    <value>Pie</value>
  </data>
  <data name="Spreadsheet_PieChartTitle" xml:space="preserve">
    <value>Insert Pie/Doughnut Chart</value>
  </data>
  <data name="Spreadsheet_PieChartContent" xml:space="preserve">
    <value>Use this chart type to show proportions of a whole. Use it when the total of your numbers is 100%.</value>
  </data>
  <data name="Spreadsheet_ScatterChart" xml:space="preserve">
    <value>Scatter</value>
  </data>
  <data name="Spreadsheet_ScatterChartTitle" xml:space="preserve">
    <value>Insert Scatter (X, Y) Chart</value>
  </data>
  <data name="Spreadsheet_ScatterChartContent" xml:space="preserve">
    <value>Use this chart type to show the relationship between sets of values.</value>
  </data>
  <data name="Spreadsheet_ClusteredColumn" xml:space="preserve">
    <value>Clustered&amp;nbsp;Column</value>
  </data>
  <data name="Spreadsheet_StackedColumn" xml:space="preserve">
    <value>Stacked&amp;nbsp;Column</value>
  </data>
  <data name="Spreadsheet_ClusteredBar" xml:space="preserve">
    <value>Clustered&amp;nbsp;Bar</value>
  </data>
  <data name="Spreadsheet_StackedBar" xml:space="preserve">
    <value>Stacked&amp;nbsp;Bar</value>
  </data>
  <data name="Spreadsheet_Radar" xml:space="preserve">
    <value>Radar</value>
  </data>
  <data name="Spreadsheet_RadarMarkers" xml:space="preserve">
    <value>Radar&amp;nbsp;with&amp;nbsp;Markers</value>
  </data>
  <data name="Spreadsheet_LineMarkers" xml:space="preserve">
    <value>Line&amp;nbsp;with&amp;nbsp;Markers</value>
  </data>
  <data name="Spreadsheet_Area" xml:space="preserve">
    <value>Area</value>
  </data>
  <data name="Spreadsheet_StackedArea" xml:space="preserve">
    <value>Stacked&amp;nbsp;Area</value>
  </data>
  <data name="Spreadsheet_Pie" xml:space="preserve">
    <value>Pie</value>
  </data>
  <data name="Spreadsheet_Doughnut" xml:space="preserve">
    <value>Doughnut</value>
  </data>
  <data name="Spreadsheet_Scatter" xml:space="preserve">
    <value>Scatter</value>
  </data>
  <data name="Spreadsheet_ChartRange" xml:space="preserve">
    <value>Chart Range</value>
  </data>
  <data name="Spreadsheet_XAxisRange" xml:space="preserve">
    <value>Enter X-axis range:</value>
  </data>
  <data name="Spreadsheet_YAxisRange" xml:space="preserve">
    <value>Enter Y-axis range:</value>
  </data>
  <data name="Spreadsheet_LegendRange" xml:space="preserve">
    <value>Enter legend range:</value>
  </data>
  <data name="Spreadsheet_YAxisMissing" xml:space="preserve">
    <value>Enter Y-axis range to create chart</value>
  </data>
  <data name="Spreadsheet_InvalidYAxis" xml:space="preserve">
    <value>Y-axis range must be within the selected range</value>
  </data>
  <data name="Spreadsheet_InvalidXAxis" xml:space="preserve">
    <value>X-axis range must be within the selected range</value>
  </data>
  <data name="Spreadsheet_InvalidLegend" xml:space="preserve">
    <value>Legend range must be within the selected range</value>
  </data>
  <data name="Spreadsheet_InvalidXAxisColumns" xml:space="preserve">
    <value>X-axis range should be within a single column</value>
  </data>
  <data name="Spreadsheet_FreezePanes" xml:space="preserve">
    <value>Freeze Panes</value>
  </data>
  <data name="Spreadsheet_FreezePanesContent" xml:space="preserve">
    <value>Freeze a portion of the sheet to keep it visible while you scroll through the rest of the sheet.</value>
  </data>
  <data name="Spreadsheet_PasteContent" xml:space="preserve">
    <value>Add content on the Clipboard to your document.</value>
  </data>
  <data name="Spreadsheet_PasteSplitContent" xml:space="preserve">
    <value>Pick a paste option, such as keeping formatting or pasting only content.</value>
  </data>
  <data name="Spreadsheet_CutContent" xml:space="preserve">
    <value>Remove the selection and put it on the Clipboard so you can paste it somewhere else.</value>
  </data>
  <data name="Spreadsheet_CopyContent" xml:space="preserve">
    <value>Put a copy of the selection on the Clipboard so you can paste it somewhere else.</value>
  </data>
  <data name="Spreadsheet_FPTitle" xml:space="preserve">
    <value>Format Painter</value>
  </data>
  <data name="Spreadsheet_FPContent" xml:space="preserve">
    <value>Like the look of a particular selection? You can apply that look to other content in the document.</value>
  </data>
  <data name="Spreadsheet_BoldContent" xml:space="preserve">
    <value>Make your text bold.</value>
  </data>
  <data name="Spreadsheet_ItalicContent" xml:space="preserve">
    <value>Italicize your text.</value>
  </data>
  <data name="Spreadsheet_ULineContent" xml:space="preserve">
    <value>Underline your text.</value>
  </data>
  <data name="Spreadsheet_LineTrContent" xml:space="preserve">
    <value>Cross something out by drawing a strike through it.</value>
  </data>
  <data name="Spreadsheet_UndoContent" xml:space="preserve">
    <value>Undo your last action.</value>
  </data>
  <data name="Spreadsheet_RedoContent" xml:space="preserve">
    <value>Redo your last action.</value>
  </data>
  <data name="Spreadsheet_HyperLinkTitle" xml:space="preserve">
    <value>Add a Hyperlink</value>
  </data>
  <data name="Spreadsheet_HyperLinkContent" xml:space="preserve">
    <value>Create a link in your document for quick access to webpages and files.</value>
  </data>
  <data name="Spreadsheet_HyperLinkPlaceContent" xml:space="preserve">
    <value>Hyperlinks can also take you to places in your document.</value>
  </data>
  <data name="Spreadsheet_NewCommentTitle" xml:space="preserve">
    <value>Insert a Comment</value>
  </data>
  <data name="Spreadsheet_NewCommentContent" xml:space="preserve">
    <value>Add a note about this part of the document.</value>
  </data>
  <data name="Spreadsheet_RefreshTitle" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="Spreadsheet_RefreshContent" xml:space="preserve">
    <value>Get the latest data from the source connected to the active cell</value>
  </data>
  <data name="Spreadsheet_FieldListTitle" xml:space="preserve">
    <value>Field List</value>
  </data>
  <data name="Spreadsheet_FieldListContent" xml:space="preserve">
    <value>Show or hide the Field List.</value>
  </data>
  <data name="Spreadsheet_FieldListRemoveContent" xml:space="preserve">
    <value>The field list allows you to add and remove fields from your PivotTable report</value>
  </data>
  <data name="Spreadsheet_AddChartElement" xml:space="preserve">
    <value>Add Chart Element</value>
  </data>
  <data name="Spreadsheet_AddChartElementContent" xml:space="preserve">
    <value>Add elements to the created chart.</value>
  </data>
  <data name="Spreadsheet_SwitchRowColumn" xml:space="preserve">
    <value>Switch Row/ Column</value>
  </data>
  <data name="Spreadsheet_SwitchRowColumnContent" xml:space="preserve">
    <value>Swap the data over the axis.</value>
  </data>
  <data name="Spreadsheet_MergeAlert" xml:space="preserve">
    <value>We can't do that to a merged cell.</value>
  </data>
  <data name="Spreadsheet_UnhideDlgText" xml:space="preserve">
    <value>Unhide Sheet:</value>
  </data>
  <data name="Spreadsheet_ChartThemes" xml:space="preserve">
    <value>Chart Themes</value>
  </data>
  <data name="Spreadsheet_ChartThemesContent" xml:space="preserve">
    <value>Pick a new theme for your chart.</value>
  </data>
  <data name="Spreadsheet_ChangePicture" xml:space="preserve">
    <value>Change Picture</value>
  </data>
  <data name="Spreadsheet_ChangePictureContent" xml:space="preserve">
    <value>Change to a different picture, preserving the formatting and size of the current picture.</value>
  </data>
  <data name="Spreadsheet_ChangeChartType" xml:space="preserve">
    <value>Change Chart Type</value>
  </data>
  <data name="Spreadsheet_SelectData" xml:space="preserve">
    <value>Select Data</value>
  </data>
  <data name="Spreadsheet_SelectDataContent" xml:space="preserve">
    <value>Change the data range included in the chart.</value>
  </data>
  <data name="Spreadsheet_Sum" xml:space="preserve">
    <value>Sum</value>
  </data>
  <data name="Spreadsheet_Average" xml:space="preserve">
    <value>Average</value>
  </data>
  <data name="Spreadsheet_CountNumber" xml:space="preserve">
    <value>Count Numbers</value>
  </data>
  <data name="Spreadsheet_Max" xml:space="preserve">
    <value>Max</value>
  </data>
  <data name="Spreadsheet_Min" xml:space="preserve">
    <value>Min</value>
  </data>
  <data name="Spreadsheet_ChartType" xml:space="preserve">
    <value>Change Chart Type</value>
  </data>
  <data name="Spreadsheet_ChartTypeContent" xml:space="preserve">
    <value>Change to a different chart type.</value>
  </data>
  <data name="Spreadsheet_AllCharts" xml:space="preserve">
    <value>All Charts</value>
  </data>
  <data name="Spreadsheet_defaultfont" xml:space="preserve">
    <value>Default</value>
  </data>
  <data name="Spreadsheet_LGeneral" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="Spreadsheet_LCurrency" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="Spreadsheet_LAccounting" xml:space="preserve">
    <value>Accounting</value>
  </data>
  <data name="Spreadsheet_LDate" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="Spreadsheet_LTime" xml:space="preserve">
    <value>Time</value>
  </data>
  <data name="Spreadsheet_LPercentage" xml:space="preserve">
    <value>Percentage</value>
  </data>
  <data name="Spreadsheet_LFraction" xml:space="preserve">
    <value>Fraction</value>
  </data>
  <data name="Spreadsheet_LScientific" xml:space="preserve">
    <value>Scientific</value>
  </data>
  <data name="Spreadsheet_LText" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="Spreadsheet_LCustom" xml:space="preserve">
    <value>Custom</value>
  </data>
  <data name="Spreadsheet_FormatSample" xml:space="preserve">
    <value>Sample</value>
  </data>
  <data name="Spreadsheet_Category" xml:space="preserve">
    <value>Category:</value>
  </data>
  <data name="Spreadsheet_Top" xml:space="preserve">
    <value>Top</value>
  </data>
  <data name="Spreadsheet_Center" xml:space="preserve">
    <value>Center</value>
  </data>
  <data name="Spreadsheet_Bottom" xml:space="preserve">
    <value>Bottom</value>
  </data>
  <data name="Spreadsheet_Left" xml:space="preserve">
    <value>Left (Indent)</value>
  </data>
  <data name="Spreadsheet_Right" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="Spreadsheet_Justify" xml:space="preserve">
    <value>Justify</value>
  </data>
  <data name="Spreadsheet_GeneralTxt" xml:space="preserve">
    <value>General format cells have no specific number format.</value>
  </data>
  <data name="Spreadsheet_NegativeNumbersTxt" xml:space="preserve">
    <value>Negative Numbers</value>
  </data>
  <data name="Spreadsheet_ThousandSeparatorTxt" xml:space="preserve">
    <value>Use 1000 Separator</value>
  </data>
  <data name="Spreadsheet_DecimalPlacesTxt" xml:space="preserve">
    <value>Decimal Places:</value>
  </data>
  <data name="Spreadsheet_TextTxt" xml:space="preserve">
    <value>Text format cells are treated as text even when a number is in the cell. The cell is displayed exactly as entered.</value>
  </data>
  <data name="Spreadsheet_BoldItalic" xml:space="preserve">
    <value>Bold Italic</value>
  </data>
  <data name="Spreadsheet_Regular" xml:space="preserve">
    <value>Regular</value>
  </data>
  <data name="Spreadsheet_HyperLinkHide" xml:space="preserve">
    <value>&lt;&lt;Selection in Document&gt;&gt;</value>
  </data>
  <data name="Spreadsheet_InvalidSheetIndex" xml:space="preserve">
    <value>Specify proper SheetIndex</value>
  </data>
  <data name="Spreadsheet_HugeDataAlert" xml:space="preserve">
    <value>File too large to open.</value>
  </data>
  <data name="Spreadsheet_ImportExportUrl" xml:space="preserve">
    <value>Give import/export URL and try again.</value>
  </data>
  <data name="Spreadsheet_BetweenAlert" xml:space="preserve">
    <value>The Maximum must be greater or equal to the Minimum.</value>
  </data>
  <data name="Spreadsheet_BorderStyles" xml:space="preserve">
    <value>Solid/Dashed/Dotted/Double</value>
  </data>
  <data name="Spreadsheet_FPaneAlert" xml:space="preserve">
    <value>Freeze Pane is not applied for the First Cell</value>
  </data>
  <data name="Spreadsheet_ReplaceNotFound" xml:space="preserve">
    <value>Spreadsheet cannot find a match.</value>
  </data>
  <data name="Spreadsheet_BlankWorkbook" xml:space="preserve">
    <value>Blank workbook</value>
  </data>
  <data name="Spreadsheet_SaveAsExcel" xml:space="preserve">
    <value>Save As Excel</value>
  </data>
  <data name="Spreadsheet_SaveAsCsv" xml:space="preserve">
    <value>Save As CSV</value>
  </data>
  <data name="Spreadsheet_SaveAsPdf" xml:space="preserve">
    <value>Save As PDF</value>
  </data>
  <data name="Spreadsheet_Design" xml:space="preserve">
    <value>DESIGN</value>
  </data>
  <data name="Spreadsheet_NewName" xml:space="preserve">
    <value>New Name</value>
  </data>
  <data name="Spreadsheet_FormulaBar" xml:space="preserve">
    <value>Formula Bar</value>
  </data>
  <data name="Spreadsheet_NameBox" xml:space="preserve">
    <value>Name Box</value>
  </data>
  <data name="Spreadsheet_NumberValMsg" xml:space="preserve">
    <value>Decimal values cannot be used for number conditions.</value>
  </data>
  <data name="Spreadsheet_NumberAlertMsg" xml:space="preserve">
    <value>Enter only digits.</value>
  </data>
  <data name="Spreadsheet_ListAlert" xml:space="preserve">
    <value>Cell range is incorrect, Please enter correct cell range.</value>
  </data>
  <data name="Spreadsheet_ListValAlert" xml:space="preserve">
    <value>The list source must be a delimited list, or a reference to single row or column.</value>
  </data>
  <data name="Spreadsheet_ListAlertMsg" xml:space="preserve">
    <value>The value you entered is not valid</value>
  </data>
  <data name="Spreadsheet_AutoFillTitle" xml:space="preserve">
    <value>AutoFillOptions</value>
  </data>
  <data name="Spreadsheet_NewSheet" xml:space="preserve">
    <value>New Sheet</value>
  </data>
  <data name="Spreadsheet_FullSheetCopyPasteAlert" xml:space="preserve">
    <value>We can't paste because the Copy area and paste area aren't the same size.</value>
  </data>
  <data name="Spreadsheet_Heading" xml:space="preserve">
    <value>Headings</value>
  </data>
  <data name="Spreadsheet_Gridlines" xml:space="preserve">
    <value>Gridlines</value>
  </data>
  <data name="Spreadsheet_Firstsheet" xml:space="preserve">
    <value>Scroll to the first sheet</value>
  </data>
  <data name="Spreadsheet_Lastsheet" xml:space="preserve">
    <value>Scroll to the last sheet</value>
  </data>
  <data name="Spreadsheet_Nextsheet" xml:space="preserve">
    <value>Scroll to the next sheet</value>
  </data>
  <data name="Spreadsheet_Prevsheet" xml:space="preserve">
    <value>Scroll to the previous sheet</value>
  </data>
  <data name="Spreadsheet_ProtectWorkbook" xml:space="preserve">
    <value>Protect Workbook</value>
  </data>
  <data name="Spreadsheet_UnProtectWorkbook" xml:space="preserve">
    <value>Unprotect Workbook</value>
  </data>
  <data name="Spreadsheet_ProtectWBContent" xml:space="preserve">
    <value>Keep others from making structural changes to your workbook</value>
  </data>
  <data name="Spreadsheet_Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="Spreadsheet_ConfirmPassword" xml:space="preserve">
    <value>Reenter password to proceed:</value>
  </data>
  <data name="Spreadsheet_PasswordAlert1" xml:space="preserve">
    <value>Confirmation password is not identical.</value>
  </data>
  <data name="Spreadsheet_PasswordAlert2" xml:space="preserve">
    <value>Please enter a password.</value>
  </data>
  <data name="Spreadsheet_PasswordAlert3" xml:space="preserve">
    <value>The password you supplied is not correct. Verify that the CAPS LOCK key is off and be sure to use the correct capitalization.</value>
  </data>
  <data name="Spreadsheet_Protect" xml:space="preserve">
    <value>is protected.</value>
  </data>
  <data name="Spreadsheet_Lock" xml:space="preserve">
    <value>LockCell</value>
  </data>
  <data name="Spreadsheet_Unlock" xml:space="preserve">
    <value>UnlockCell</value>
  </data>
  <data name="Spreadsheet_Protectsheet" xml:space="preserve">
    <value>Protect Sheet</value>
  </data>
  <data name="Spreadsheet_ProtectSheetToolTip" xml:space="preserve">
    <value>Prevent unwanted changes from others by limiting their ability to edit</value>
  </data>
  <data name="Spreadsheet_Unprotect" xml:space="preserve">
    <value>Unprotect Sheet</value>
  </data>
  <data name="Spreadsheet_LockAlert" xml:space="preserve">
    <value>The cell you are trying to change is on protected sheet. To make changes, click Unprotect Sheet in the Review tab.</value>
  </data>
  <data name="Spreadsheet_InsertDeleteAlert" xml:space="preserve">
    <value>This operation is not allowed. The operation is attempting to shift cells in a table on your worksheet.</value>
  </data>
  <data name="Spreadsheet_CreateRule" xml:space="preserve">
    <value>New Rule</value>
  </data>
  <data name="Spreadsheet_NewRule" xml:space="preserve">
    <value>New Formatting Rule</value>
  </data>
  <data name="Spreadsheet_NewRuleLabelContent" xml:space="preserve">
    <value>Format values where this formula is true:</value>
  </data>
  <data name="Spreadsheet_ReadOnly" xml:space="preserve">
    <value>The range you are trying to change contains readonly cells.</value>
  </data>
  <data name="Spreadsheet_CreatePivotTable" xml:space="preserve">
    <value>Create PivotTable</value>
  </data>
  <data name="Spreadsheet_Range" xml:space="preserve">
    <value>Range:</value>
  </data>
  <data name="Spreadsheet_ChoosePivotTable" xml:space="preserve">
    <value>Choose where you want the PivotTable to be placed</value>
  </data>
  <data name="Spreadsheet_NewWorksheet" xml:space="preserve">
    <value>New Worksheet</value>
  </data>
  <data name="Spreadsheet_ExistingWorksheet" xml:space="preserve">
    <value>Existing Worksheet</value>
  </data>
  <data name="Spreadsheet_Location" xml:space="preserve">
    <value>Location:</value>
  </data>
  <data name="Spreadsheet_Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="Spreadsheet_PivotRowsAlert" xml:space="preserve">
    <value>This command requires at least two rows of source data. You cannot use the command on a selection in only one row.</value>
  </data>
  <data name="Spreadsheet_PivotLabelsAlert" xml:space="preserve">
    <value>The PivotTable field name is not valid, To create a PivotTable report, you must use data that is organized as a list with labeled columns. If you are changing the name of a PivotTable field, you must type a new name for the field.</value>
  </data>
  <data name="Spreadsheet_FieldList" xml:space="preserve">
    <value>Field List</value>
  </data>
  <data name="Spreadsheet_MergeSortAlert" xml:space="preserve">
    <value>To do this, all the merged cells need to be the same size.</value>
  </data>
  <data name="Spreadsheet_FormulaSortAlert" xml:space="preserve">
    <value>The sort range with formula cannot be sorted.</value>
  </data>
  <data name="Spreadsheet_MergePreventInsertDelete" xml:space="preserve">
    <value>This operation is not allowed. The operation is attempting to shift a merge cells on your worksheet.</value>
  </data>
  <data name="Spreadsheet_FormulaRuleMsg" xml:space="preserve">
    <value>Please enter correct format.</value>
  </data>
  <data name="Spreadsheet_MovePivotTable" xml:space="preserve">
    <value>Move PivotTable</value>
  </data>
  <data name="Spreadsheet_MovePivotTableContent" xml:space="preserve">
    <value>Move the PivotTable to another location in the workbook.</value>
  </data>
  <data name="Spreadsheet_ClearAllContent" xml:space="preserve">
    <value>Remove fields and filters.</value>
  </data>
  <data name="Spreadsheet_ChangeDataSource" xml:space="preserve">
    <value>Modify</value>
  </data>
  <data name="Spreadsheet_ChangeDataSourceContent" xml:space="preserve">
    <value>Change the source data for this PivotTable</value>
  </data>
  <data name="Spreadsheet_ChangePivotTableDataSource" xml:space="preserve">
    <value>Change PivotTable Data Source</value>
  </data>
  <data name="Spreadsheet_TotalRowAlert" xml:space="preserve">
    <value>This operation is not allowed. The operation is attempting to shift cells in a table on your worksheet. Click OK to proceed with entire row.</value>
  </data>
  <data name="Spreadsheet_CellTypeAlert" xml:space="preserve">
    <value>This operation is not allowed in cell type applied range.</value>
  </data>
  <data name="Spreadsheet_PivotOverlapAlert" xml:space="preserve">
    <value>A PivotTable report cannot overlap another PivotTable report</value>
  </data>
  <data name="Spreadsheet_NoCellFound" xml:space="preserve">
    <value>No cells were found</value>
  </data>
  <data name="Spreadsheet_CorrectArgument" xml:space="preserve">
    <value>Please enter the correct argument</value>
  </data>
  <data name="Spreadsheet_CorrectFormula" xml:space="preserve">
    <value>Please enter the correct formula</value>
  </data>
  <data name="Spreadsheet_CorrectCellAddress" xml:space="preserve">
    <value>Please enter the correct cell address</value>
  </data>
  <data name="Spreadsheet_NumberValidationAlert" xml:space="preserve">
    <value>The Maximum must be greater than or equal to Minimum</value>
  </data>
  <data name="Spreadsheet_DateValidationAlert" xml:space="preserve">
    <value>The End Date must be greater than or equal to Start Date</value>
  </data>
  <data name="Spreadsheet_TimeValidationAlert" xml:space="preserve">
    <value>The End Time must be greater than or equal to Start Time</value>
  </data>
  <data name="Spreadsheet_NewRuleAlert" xml:space="preserve">
    <value>There's a problem with this formula</value>
  </data>
  <data name="Spreadsheet_DragAlert" xml:space="preserve">
    <value>The command could not be completed as you are attempting to move cells within the filter range. Select a range out of the filter range and try the command again.</value>
  </data>
  <data name="Spreadsheet_OR" xml:space="preserve">
    <value>or</value>
  </data>
  <data name="Spreadsheet_HOME" xml:space="preserve">
    <value>HOME</value>
  </data>
  <data name="Spreadsheet_DATATAB" xml:space="preserve">
    <value>DATA</value>
  </data>
  <data name="Spreadsheet_PAGELAYOUT" xml:space="preserve">
    <value>PAGE LAYOUT</value>
  </data>
  <data name="Spreadsheet_REVIEW" xml:space="preserve">
    <value>REVIEW</value>
  </data>
  <data name="Spreadsheet_OTHERS" xml:space="preserve">
    <value>OTHERS</value>
  </data>
  <data name="Spreadsheet_CHARTDESIGN" xml:space="preserve">
    <value>Chart Design</value>
  </data>
  <data name="Spreadsheet_ANALYZE" xml:space="preserve">
    <value>Analyze</value>
  </data>
  <data name="Spreadsheet_FileName" xml:space="preserve">
    <value>File Name</value>
  </data>
  <data name="Spreadsheet_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Spreadsheet_SaveFile" xml:space="preserve">
    <value>Save the file</value>
  </data>
  <data name="Spreadsheet_HighlightCellRules" xml:space="preserve">
    <value>Highlight Cell Rules</value>
  </data>
  <data name="Spreadsheet_LightRedFillDark" xml:space="preserve">
    <value>Light Red Fill with Dark Red Text</value>
  </data>
  <data name="Spreadsheet_YellowFillDark" xml:space="preserve">
    <value>Yellow Fill with Dark Yellow Text</value>
  </data>
  <data name="Spreadsheet_GreenFillDark" xml:space="preserve">
    <value>Green Fill with Dark Green Text</value>
  </data>
  <data name="Spreadsheet_RedFill" xml:space="preserve">
    <value>Red Fill</value>
  </data>
  <data name="Spreadsheet_RedText" xml:space="preserve">
    <value>Red Text</value>
  </data>
  <data name="Spreadsheet_Column" xml:space="preserve">
    <value>Column</value>
  </data>
  <data name="Spreadsheet_Bar" xml:space="preserve">
    <value>Bar</value>
  </data>
  <data name="Spreadsheet_DataValidationType" xml:space="preserve">
    <value>Number/Decimal/Date/Time/TextLength/List</value>
  </data>
  <data name="Spreadsheet_DataValidationAction" xml:space="preserve">
    <value>greater than/greater than or equal to/less than/less than or equal to/equal to/not equal to/between/not between</value>
  </data>
  <data name="Spreadsheet_Modify" xml:space="preserve">
    <value>Modify</value>
  </data>
  <data name="Spreadsheet_Apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="Spreadsheet_NewCellStyle" xml:space="preserve">
    <value>New Cell Style</value>
  </data>
  <data name="Spreadsheet_cellStyleAlert" xml:space="preserve">
    <value>This style name already exists</value>
  </data>
  <data name="Spreadsheet_modifyCellStyleAlert" xml:space="preserve">
    <value>This style name does not exists</value>
  </data>
  <data name="Spreadsheet_Color" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="Spreadsheet_StyleName" xml:space="preserve">
    <value>Style Name</value>
  </data>
  <data name="Spreadsheet_LShortdate" xml:space="preserve">
    <value>Short Date</value>
  </data>
  <data name="Spreadsheet_LLongdate" xml:space="preserve">
    <value>Long Date</value>
  </data>
  <data name="Spreadsheet_Clipboard" xml:space="preserve">
    <value>Clipboard</value>
  </data>
  <data name="Spreadsheet_Font" xml:space="preserve">
    <value>Font</value>
  </data>
  <data name="Spreadsheet_Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="Spreadsheet_Styles" xml:space="preserve">
    <value>Styles</value>
  </data>
  <data name="Spreadsheet_Editing" xml:space="preserve">
    <value>Editing</value>
  </data>
  <data name="Spreadsheet_Tables" xml:space="preserve">
    <value>Tables</value>
  </data>
  <data name="Spreadsheet_Illustrations" xml:space="preserve">
    <value>Illustrations</value>
  </data>
  <data name="Spreadsheet_Links" xml:space="preserve">
    <value>Links</value>
  </data>
  <data name="Spreadsheet_Charts" xml:space="preserve">
    <value>Charts</value>
  </data>
  <data name="Spreadsheet_DataTools" xml:space="preserve">
    <value>Data Tools</value>
  </data>
  <data name="Spreadsheet_Show" xml:space="preserve">
    <value>Show</value>
  </data>
  <data name="Spreadsheet_Changes" xml:space="preserve">
    <value>Changes</value>
  </data>
  <data name="Spreadsheet_Window" xml:space="preserve">
    <value>Window</value>
  </data>
  <data name="Spreadsheet_Cells" xml:space="preserve">
    <value>Cells</value>
  </data>
  <data name="Spreadsheet_Calculation" xml:space="preserve">
    <value>Calculation</value>
  </data>
  <data name="Spreadsheet_Properties" xml:space="preserve">
    <value>Properties</value>
  </data>
  <data name="Spreadsheet_Tools" xml:space="preserve">
    <value>Tools</value>
  </data>
  <data name="Spreadsheet_TableStyleOption" xml:space="preserve">
    <value>Table Style Options</value>
  </data>
  <data name="Spreadsheet_ChartLayouts" xml:space="preserve">
    <value>Chart Layouts</value>
  </data>
  <data name="Spreadsheet_ChartDesignData" xml:space="preserve">
    <value>Data</value>
  </data>
  <data name="Spreadsheet_ChartDesignType" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Spreadsheet_ChartDesignSize" xml:space="preserve">
    <value>Size</value>
  </data>
  <data name="Spreadsheet_Adjust" xml:space="preserve">
    <value>Adjust</value>
  </data>
  <data name="Spreadsheet_FormatSize" xml:space="preserve">
    <value>Size</value>
  </data>
  <data name="Spreadsheet_AnalyzePivotTable" xml:space="preserve">
    <value>PivotTable</value>
  </data>
  <data name="Spreadsheet_DataSource" xml:space="preserve">
    <value>DataSource</value>
  </data>
  <data name="Spreadsheet_FATTitlePrefix" xml:space="preserve">
    <value>Table Style</value>
  </data>
  <data name="Spreadsheet_HighPoint" xml:space="preserve">
    <value>High Point</value>
  </data>
  <data name="Spreadsheet_LowPoint" xml:space="preserve">
    <value>Low Point</value>
  </data>
  <data name="Spreadsheet_FirstPoint" xml:space="preserve">
    <value>First Point</value>
  </data>
  <data name="Spreadsheet_LastPoint" xml:space="preserve">
    <value>Last Point</value>
  </data>
  <data name="Spreadsheet_NegativePoint" xml:space="preserve">
    <value>Negative Points</value>
  </data>
  <data name="Spreadsheet_Markers" xml:space="preserve">
    <value>Markers</value>
  </data>
  <data name="Spreadsheet_NegativePoints" xml:space="preserve">
    <value>Negative Points</value>
  </data>
  <data name="Spreadsheet_LineSparklineTitle" xml:space="preserve">
    <value>Insert Line Sparkline</value>
  </data>
  <data name="Spreadsheet_LineSparklineContent" xml:space="preserve">
    <value>Sparklines are mini charts placed in a single cells,each representing a row of data in your selection</value>
  </data>
  <data name="Spreadsheet_ColumnSparklineTitle" xml:space="preserve">
    <value>Insert Column Sparkline</value>
  </data>
  <data name="Spreadsheet_ColumnSparklineContent" xml:space="preserve">
    <value>Sparklines are mini charts placed in a single cells,each representing a row of data in your selection</value>
  </data>
  <data name="Spreadsheet_WinLossSparklineTitle" xml:space="preserve">
    <value>Insert Win/Loss Sparkline</value>
  </data>
  <data name="Spreadsheet_WinLossSparklineContent" xml:space="preserve">
    <value>Sparklines are mini charts placed in a single cells,each representing a row of data in your selection</value>
  </data>
  <data name="Spreadsheet_SparklineColor" xml:space="preserve">
    <value>Sparkline Color</value>
  </data>
  <data name="Spreadsheet_SparklineColorTitle" xml:space="preserve">
    <value>Sparkline Color</value>
  </data>
  <data name="Spreadsheet_SparklineColorContent" xml:space="preserve">
    <value>Specify the color of the sparklines in the selected sparkline group</value>
  </data>
  <data name="Spreadsheet_MarkerColor" xml:space="preserve">
    <value>Marker Color</value>
  </data>
  <data name="Spreadsheet_MarkerColorContent" xml:space="preserve">
    <value>Change the color for negative points, markers, and all other points for selected sparkline group</value>
  </data>
  <data name="Spreadsheet_ChooseDataRange" xml:space="preserve">
    <value>Choose the Data Range</value>
  </data>
  <data name="Spreadsheet_ChooseLocationRange" xml:space="preserve">
    <value>Choose the Location Range</value>
  </data>
  <data name="Spreadsheet_DataRange" xml:space="preserve">
    <value>Data Range</value>
  </data>
  <data name="Spreadsheet_LocationRange" xml:space="preserve">
    <value>Location Range</value>
  </data>
  <data name="Spreadsheet_EmptyDLRnge" xml:space="preserve">
    <value>Data Source or Location reference is not valid</value>
  </data>
  <data name="Spreadsheet_SourceDataRange" xml:space="preserve">
    <value>Select a source data range for the sparkline</value>
  </data>
  <data name="Spreadsheet_SparklineAlert" xml:space="preserve">
    <value>Location reference is not valid because the cells are not all in same column or same row. Select cells that are all in single row or single column.</value>
  </data>
  <data name="Spreadsheet_SparklineDataAlert" xml:space="preserve">
    <value>Enter the Correct Data Format</value>
  </data>
  <data name="Spreadsheet_SparklineLocAlert" xml:space="preserve">
    <value>The reference for the location or data range is not valid</value>
  </data>
  <data name="Spreadsheet_SDataRangeAlert" xml:space="preserve">
    <value>Data Source reference is not valid</value>
  </data>
  <data name="Spreadsheet_LineAlert" xml:space="preserve">
    <value>Cannot create the line chart for single value</value>
  </data>
  <data name="Spreadsheet_EditData" xml:space="preserve">
    <value>Edit Data</value>
  </data>
  <data name="Spreadsheet_EditDataContent" xml:space="preserve">
    <value>Edit Group Location &amp; Data</value>
  </data>
  <data name="Spreadsheet_EditSingleSparklineData" xml:space="preserve">
    <value>Edit Single Sparkline Data</value>
  </data>
  <data name="Spreadsheet_EditSparklineData" xml:space="preserve">
    <value>Edit Sparkline Data</value>
  </data>
  <data name="Spreadsheet_HiddenEmptyCells" xml:space="preserve">
    <value>Hidden &amp; Empty Cells</value>
  </data>
  <data name="Spreadsheet_SwitchRowCol" xml:space="preserve">
    <value>Switch Row/Column</value>
  </data>
  <data name="Spreadsheet_CreateSparkline" xml:space="preserve">
    <value>Create Sparkline</value>
  </data>
  <data name="Spreadsheet_SelectDataSource" xml:space="preserve">
    <value>Select a source data range for the sparkline</value>
  </data>
  <data name="Spreadsheet_SPARKLINEDESIGN" xml:space="preserve">
    <value>Sparkline Design</value>
  </data>
  <data name="Spreadsheet_CancellationRequested" xml:space="preserve">
    <value>Couldn't open within the timeout specified</value>
  </data>
  <data name="Spreadsheet_ImportPreventedOnUnknownFormula" xml:space="preserve">
    <value>This file cannot be loaded since it contains unsupported formulas.</value>
  </data>
  <data name="Spreadsheet_PivotTableName" xml:space="preserve">
    <value>PivotTable Name</value>
  </data>
  <data name="Spreadsheet_ArrayaFormula" xml:space="preserve">
    <value>You can't change part of an array</value>
  </data>
  <data name="Spreadsheet_ArrayaFormulaTableAlert" xml:space="preserve">
    <value>Multi-cell array formulas aren't allowed in tables</value>
  </data>
  <data name="Spreadsheet_ValueFieldSettings" xml:space="preserve">
    <value>Value Field Settings</value>
  </data>
  <data name="Spreadsheet_FieldTab" xml:space="preserve">
    <value>Summarize Values By</value>
  </data>
  <data name="Spreadsheet_SummarizeValue" xml:space="preserve">
    <value>Summarize Value Field By</value>
  </data>
  <data name="Spreadsheet_SummarizeChooseType" xml:space="preserve">
    <value>Choose the type of calculation that you want to use to summarize data from the selected field</value>
  </data>
  <data name="Spreadsheet_FieldValue" xml:space="preserve">
    <value>Source Name:</value>
  </data>
  <data name="TreeGrid_toolboxTooltipTexts_addTool" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="TreeGrid_toolboxTooltipTexts_editTool" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="TreeGrid_toolboxTooltipTexts_updateTool" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="TreeGrid_toolboxTooltipTexts_deleteTool" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="TreeGrid_toolboxTooltipTexts_cancelTool" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="TreeGrid_toolboxTooltipTexts_expandAllTool" xml:space="preserve">
    <value>Expand All</value>
  </data>
  <data name="TreeGrid_toolboxTooltipTexts_collapseAllTool" xml:space="preserve">
    <value>Collapse All</value>
  </data>
  <data name="TreeGrid_toolboxTooltipTexts_pdfExportTool" xml:space="preserve">
    <value>PDF Export</value>
  </data>
  <data name="TreeGrid_toolboxTooltipTexts_excelExportTool" xml:space="preserve">
    <value>Excel Export</value>
  </data>
  <data name="TreeGrid_toolboxTooltipTexts_printTool" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="TreeGrid_toolboxTooltipTexts_searchTool" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="TreeGrid_contextMenuTexts_addRowText" xml:space="preserve">
    <value>Add Row</value>
  </data>
  <data name="TreeGrid_contextMenuTexts_editText" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="TreeGrid_contextMenuTexts_deleteText" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="TreeGrid_contextMenuTexts_saveText" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="TreeGrid_contextMenuTexts_cancelText" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="TreeGrid_contextMenuTexts_aboveText" xml:space="preserve">
    <value>Above</value>
  </data>
  <data name="TreeGrid_contextMenuTexts_belowText" xml:space="preserve">
    <value>Below</value>
  </data>
  <data name="TreeGrid_columnMenuTexts_sortAscendingText" xml:space="preserve">
    <value>Sort Ascending</value>
  </data>
  <data name="TreeGrid_columnMenuTexts_sortDescendingText" xml:space="preserve">
    <value>Sort Descending</value>
  </data>
  <data name="TreeGrid_columnMenuTexts_columnsText" xml:space="preserve">
    <value>Columns</value>
  </data>
  <data name="TreeGrid_columnMenuTexts_freezeText" xml:space="preserve">
    <value>Freeze</value>
  </data>
  <data name="TreeGrid_columnMenuTexts_unfreezeText" xml:space="preserve">
    <value>Unfreeze</value>
  </data>
  <data name="TreeGrid_columnMenuTexts_freezePrecedingColumnsText" xml:space="preserve">
    <value>Freeze Preceding Columns</value>
  </data>
  <data name="TreeGrid_columnMenuTexts_insertColumnLeft" xml:space="preserve">
    <value>Insert Column Left</value>
  </data>
  <data name="TreeGrid_columnMenuTexts_insertColumnRight" xml:space="preserve">
    <value>Insert Column Right</value>
  </data>
  <data name="TreeGrid_columnMenuTexts_deleteColumn" xml:space="preserve">
    <value>Delete Column</value>
  </data>
  <data name="TreeGrid_columnMenuTexts_renameColumn" xml:space="preserve">
    <value>Rename Column</value>
  </data>
  <data name="TreeGrid_columnMenuTexts_menuFilter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_field" xml:space="preserve">
    <value>Field</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_headerText" xml:space="preserve">
    <value>Header Text</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_editType" xml:space="preserve">
    <value>Edit Type</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_filterEditType" xml:space="preserve">
    <value>Filter Edit Type</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_allowFiltering" xml:space="preserve">
    <value>Allow Filtering</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_allowFilteringBlankContent" xml:space="preserve">
    <value>Allow Filtering Blank Content</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_allowSorting" xml:space="preserve">
    <value>Allow Sorting</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_visible" xml:space="preserve">
    <value>Visible</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_width" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_textAlign" xml:space="preserve">
    <value>Text Alignment</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_headerTextAlign" xml:space="preserve">
    <value>Header Text Alignment</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_isFrozen" xml:space="preserve">
    <value>Is Frozen</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_allowFreezing" xml:space="preserve">
    <value>Allow Freezing</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_columnsDropdownData" xml:space="preserve">
    <value>Column Dropdown Data</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_dropdownTableText" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_dropdownTableValue" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_addData" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_deleteData" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_allowCellSelection" xml:space="preserve">
    <value>Allow Cell Selection</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_showInColumnChooser" xml:space="preserve">
    <value>Show In Column Chooser</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_displayAsCheckbox" xml:space="preserve">
    <value>Display As Checkbox</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_clipMode" xml:space="preserve">
    <value>Clip Mode</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_tooltip" xml:space="preserve">
    <value>Tooltip</value>
  </data>
  <data name="TreeGrid_columnDialogTexts_headerTooltip" xml:space="preserve">
    <value>Header Tooltip</value>
  </data>
  <data name="TreeGrid_editTypeTexts_string" xml:space="preserve">
    <value>String</value>
  </data>
  <data name="TreeGrid_editTypeTexts_numeric" xml:space="preserve">
    <value>Numeric</value>
  </data>
  <data name="TreeGrid_editTypeTexts_datePicker" xml:space="preserve">
    <value>Date Picker</value>
  </data>
  <data name="TreeGrid_editTypeTexts_dateTimePicker" xml:space="preserve">
    <value>Date Time Picker</value>
  </data>
  <data name="TreeGrid_editTypeTexts_dropdown" xml:space="preserve">
    <value>Dropdown</value>
  </data>
  <data name="TreeGrid_editTypeTexts_boolean" xml:space="preserve">
    <value>Boolean</value>
  </data>
  <data name="TreeGrid_textAlignTypes_right" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="TreeGrid_textAlignTypes_left" xml:space="preserve">
    <value>Left</value>
  </data>
  <data name="TreeGrid_textAlignTypes_center" xml:space="preserve">
    <value>Center</value>
  </data>
  <data name="TreeGrid_clipModeTexts_clip" xml:space="preserve">
    <value>Clip</value>
  </data>
  <data name="TreeGrid_clipModeTexts_ellipsis" xml:space="preserve">
    <value>Ellipsis</value>
  </data>
  <data name="TreeGrid_columnDialogTitle_insertColumn" xml:space="preserve">
    <value>Insert Column</value>
  </data>
  <data name="TreeGrid_columnDialogTitle_deleteColumn" xml:space="preserve">
    <value>Delete Column</value>
  </data>
  <data name="TreeGrid_columnDialogTitle_renameColumn" xml:space="preserve">
    <value>Rename Column</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_stringMenuOptions" xml:space="preserve">
    <value> [{ text: "Starts With", value: "startswith" },                                { text: "Ends With", value: "endswith" },                                { text: "Contains", value: "contains" },                                { text: "Equals", value: "equal" },{ text: "Does Not Equal", value: "notequal" }],</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_numberMenuOptions" xml:space="preserve">
    <value> [{ text: "Less Than", value: "lessthan" },                                    { text: "Greater Than", value: "greaterthan" },                                    { text: "Less Than Or Equal To", value: "lessthanorequal" },                                    { text: "Greater Than Or Equal To", value: "greaterthanorequal" },                                    { text: "Equals", value: "equal" },{ text: "Does Not Equal", value: "notequal" }],</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_filterValue" xml:space="preserve">
    <value>Filter Value</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_filterButton" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_clearButton" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_enterValueText" xml:space="preserve">
    <value>enter value</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_deleteColumnText" xml:space="preserve">
    <value>Are you sure you want to delete this column?</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_deleteRecordText" xml:space="preserve">
    <value>Are you sure you want to delete record?</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_okButtonText" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_cancelButtonText" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_confirmDeleteText" xml:space="preserve">
    <value>Confirm Delete</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_batchSaveConfirmText" xml:space="preserve">
    <value>Are you sure you want to save changes?</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_batchSaveLostChangesText" xml:space="preserve">
    <value>Unsaved changes will be lost. Are you sure you want to continue?</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_cancelEditText" xml:space="preserve">
    <value>Are you sure you want to cancel the changes?</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_dropDownListBlanksText" xml:space="preserve">
    <value>(Blanks)</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_dropDownListClearText" xml:space="preserve">
    <value>(Clear Filter)</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_trueText" xml:space="preserve">
    <value>True</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_falseText" xml:space="preserve">
    <value>False</value>
  </data>
  <data name="TreeGrid_filterMenuTexts_emptyRecord" xml:space="preserve">
    <value>No records to display</value>
  </data>
  <data name="TreeGrid_Uploadbox_buttonText_upload" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="TreeGrid_Uploadbox_buttonText_browse" xml:space="preserve">
    <value>Browse</value>
  </data>
  <data name="TreeGrid_Uploadbox_buttonText_cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="TreeGrid_Uploadbox_buttonText_close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="TreeGrid_Uploadbox_dialogText_title" xml:space="preserve">
    <value>Upload Box</value>
  </data>
  <data name="TreeGrid_Uploadbox_dialogText_name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="TreeGrid_Uploadbox_dialogText_size" xml:space="preserve">
    <value>Size</value>
  </data>
  <data name="TreeGrid_Uploadbox_dialogText_status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="TreeGrid_Uploadbox_dropAreaText" xml:space="preserve">
    <value>Drop files or click to upload</value>
  </data>
  <data name="TreeGrid_Uploadbox_filedetail" xml:space="preserve">
    <value>The selected file size is too large. Please select a file within the valid size.</value>
  </data>
  <data name="TreeGrid_Uploadbox_denyError" xml:space="preserve">
    <value>Files with #Extension extensions are not allowed.</value>
  </data>
  <data name="TreeGrid_Uploadbox_allowError" xml:space="preserve">
    <value>Only files with #Extension extensions are allowed.</value>
  </data>
  <data name="TreeGrid_Uploadbox_cancelToolTip" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="TreeGrid_Uploadbox_removeToolTip" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="TreeGrid_Uploadbox_retryToolTip" xml:space="preserve">
    <value>Retry</value>
  </data>
  <data name="TreeGrid_Uploadbox_completedToolTip" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="TreeGrid_Uploadbox_failedToolTip" xml:space="preserve">
    <value>Failed</value>
  </data>
  <data name="TreeGrid_Uploadbox_closeToolTip" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="TreeGrid_SpellCheck_SpellCheckButtonText" xml:space="preserve">
    <value>Spelling:</value>
  </data>
  <data name="TreeGrid_SpellCheck_NotInDictionary" xml:space="preserve">
    <value>Not in Dictionary:</value>
  </data>
  <data name="TreeGrid_SpellCheck_SuggestionLabel" xml:space="preserve">
    <value>Suggestions:</value>
  </data>
  <data name="TreeGrid_SpellCheck_IgnoreOnceButtonText" xml:space="preserve">
    <value>Ignore Once</value>
  </data>
  <data name="TreeGrid_SpellCheck_IgnoreAllButtonText" xml:space="preserve">
    <value>Ignore All</value>
  </data>
  <data name="TreeGrid_SpellCheck_AddToDictionary" xml:space="preserve">
    <value>Add to Dictionary</value>
  </data>
  <data name="TreeGrid_SpellCheck_ChangeButtonText" xml:space="preserve">
    <value>Change</value>
  </data>
  <data name="TreeGrid_SpellCheck_ChangeAllButtonText" xml:space="preserve">
    <value>ChangeAll</value>
  </data>
  <data name="TreeGrid_SpellCheck_CloseButtonText" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="TreeGrid_SpellCheck_CompletionPopupMessage" xml:space="preserve">
    <value>Spell check is complete</value>
  </data>
  <data name="TreeGrid_SpellCheck_CompletionPopupTitle" xml:space="preserve">
    <value>Spell check</value>
  </data>
  <data name="TreeGrid_SpellCheck_Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="TreeGrid_SpellCheck_NoSuggestionMessage" xml:space="preserve">
    <value>No suggestions available</value>
  </data>
  <data name="TreeGrid_SpellCheck_NotValidElement" xml:space="preserve">
    <value>Specify the valid control id or class name to spell check</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Play" xml:space="preserve">
    <value>Play</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Pause" xml:space="preserve">
    <value>Pause</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Mute" xml:space="preserve">
    <value>Mute</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Unmute" xml:space="preserve">
    <value>Unmute</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Settings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="TreeGrid_MediaPlayer_FullScreen" xml:space="preserve">
    <value>Full screen</value>
  </data>
  <data name="TreeGrid_MediaPlayer_ExitFullScreen" xml:space="preserve">
    <value>Exit full screen</value>
  </data>
  <data name="TreeGrid_MediaPlayer_HidePlaylist" xml:space="preserve">
    <value>Hide playlist</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Previous" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="TreeGrid_MediaPlayer_TogglePlaylist" xml:space="preserve">
    <value>Toggle playlist</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Rewind" xml:space="preserve">
    <value>Rewind</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Forward" xml:space="preserve">
    <value>Forward</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Playlist" xml:space="preserve">
    <value>Playlist</value>
  </data>
  <data name="TreeGrid_MediaPlayer_RepeatPlaylist" xml:space="preserve">
    <value>Repeat playlist</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Shuffle" xml:space="preserve">
    <value>Shuffle</value>
  </data>
  <data name="TreeGrid_MediaPlayer_VideoTitle" xml:space="preserve">
    <value>Video</value>
  </data>
  <data name="TreeGrid_MediaPlayer_PlaylistTitle" xml:space="preserve">
    <value>Playlist</value>
  </data>
  <data name="TreeGrid_MediaPlayer_PlaylistItemName" xml:space="preserve">
    <value>List item</value>
  </data>
  <data name="TreeGrid_MediaPlayer_PlaylistItemAuthor" xml:space="preserve">
    <value>Author</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Media" xml:space="preserve">
    <value>Media</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Speed" xml:space="preserve">
    <value>Speed</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Quality" xml:space="preserve">
    <value>Quality</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="TreeGrid_MediaPlayer_Auto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="TreeGrid_Tile_captionText" xml:space="preserve">
    <value>text</value>
  </data>
  <data name="TreeGrid_ListView_headerTitle" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="TreeGrid_ListView_headerBackButtonText" xml:space="preserve">
    <value>Back</value>
  </data>
</root>