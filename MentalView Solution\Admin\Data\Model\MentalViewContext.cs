﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace Admin.Data.Model;

public partial class MentalViewContext : DbContext
{
    //public MentalViewContext(DbContextOptions<MentalViewContext> options)
    //    : base(options)
    //{
    //    ChangeTracker.LazyLoadingEnabled = false;
    //}

    public virtual DbSet<AppointmentCategory> AppointmentCategories { get; set; }

    public virtual DbSet<Appointment> Appointments { get; set; }

    public virtual DbSet<Contact> Contacts { get; set; }

    public virtual DbSet<Tenant> Tenants { get; set; }

    public virtual DbSet<Role> Roles { get; set; }

    public virtual DbSet<User> Users { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Appointment>(entity =>
        {
            entity.Property(e => e.AppointmentType)
                .HasDefaultValue("Appointment")
                .HasComment("Appointment= Συνεδρία, Event=Συμβάν");
            entity.Property(e => e.Bank).HasDefaultValue("");
            entity.Property(e => e.Categorize).HasDefaultValue("");
            entity.Property(e => e.CustomRecurrenceId).HasDefaultValue("");
            entity.Property(e => e.CustomRecurrenceRule).HasDefaultValue("");
            entity.Property(e => e.Description).HasDefaultValue("");
            entity.Property(e => e.EndTimeZone).HasDefaultValue("");
            entity.Property(e => e.IntervetionModel)
                .HasDefaultValue("")
                .HasComment("n");
            entity.Property(e => e.IntervetionTechniques).HasDefaultValue("");
            entity.Property(e => e.Notes).HasDefaultValue("");
            entity.Property(e => e.PaymentType).HasDefaultValue("");
            entity.Property(e => e.RecurrenceExDate).HasDefaultValue("");
            entity.Property(e => e.RecurrenceRule).HasDefaultValue("");
            entity.Property(e => e.Request).HasDefaultValue("");
            entity.Property(e => e.ResourceFields).HasDefaultValue("");
            entity.Property(e => e.Room).HasDefaultValue("");
            entity.Property(e => e.StartTimeZone).HasDefaultValue("");
            entity.Property(e => e.State).HasDefaultValue("");
            entity.Property(e => e.Subject).HasDefaultValue("");
            entity.Property(e => e.SupervisorCommentsAfter).HasDefaultValue("");
            entity.Property(e => e.SupervisorInstructionsBefore).HasDefaultValue("");
            entity.Property(e => e.TaskSupervision).HasComment("Μόνο για Συμβάν. Καθορίζει αν είναι Εποπτεία ");
            entity.Property(e => e.TaskSupervisionCustomers)
                .HasDefaultValue("")
                .HasComment("Μόνο για Συμβάν που είναι εποπτεία. Τα ονόματα των πελατών.");
            entity.Property(e => e.TaskSupervisionReplies)
                .HasDefaultValue("")
                .HasComment("Μόνο για Συμβάν που είναι εποπτεία. Οι απαντήσεις/προτάσεις στο Ζήτημα/Θεμα της εποπτείας.");
            entity.Property(e => e.TaskSupervisionSubject)
                .HasDefaultValue("")
                .HasComment("Μόνο για Συμβάν που είναι εποπτεία. Το Ζήτημα της εποπτείας.");
            entity.Property(e => e.TaskSupervisionTherapistIds)
                .HasDefaultValue("")
                .HasComment("Μόνο για Συμβάν που είναι εποπτεία. Τα IDs των θεραπευτών που θα συμμετέχουν στην εποπτεία. ");
            entity.Property(e => e.Therapist).HasDefaultValue("");
            entity.Property(e => e.TherapistComments).HasDefaultValue("");
            entity.Property(e => e.UserId).HasComment("Ο γιατρός ο οποίος δέχεται το ραντεβού");

            entity.HasOne(d => d.Contact).WithMany(p => p.Appointments)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_Appointments_Contacts");

            entity.HasOne(d => d.User).WithMany(p => p.Appointments).HasConstraintName("FK_Appointments_Users");
        });

        modelBuilder.Entity<Contact>(entity =>
        {
            entity.Property(e => e.AccessLevel).HasDefaultValue("");
            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.Addictive).HasDefaultValue("");
            entity.Property(e => e.AddictiveSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.AddictiveTherapistComment).HasDefaultValue("");
            entity.Property(e => e.AdhesionType).HasDefaultValue("");
            entity.Property(e => e.AdhesionTypeSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.AdhesionTypeTherapistComment).HasDefaultValue("");
            entity.Property(e => e.AdultExperiences).HasDefaultValue("");
            entity.Property(e => e.AdultExperiencesSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.AdultExperiencesTherapistComment).HasDefaultValue("");
            entity.Property(e => e.Afm).HasDefaultValue("");
            entity.Property(e => e.Ama).HasDefaultValue("");
            entity.Property(e => e.Amka).HasDefaultValue("");
            entity.Property(e => e.Antisocial).HasDefaultValue("");
            entity.Property(e => e.AntisocialSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.AntisocialTherapistComment).HasDefaultValue("");
            entity.Property(e => e.Anxiety).HasDefaultValue("");
            entity.Property(e => e.AnxietySupervisorComment).HasDefaultValue("");
            entity.Property(e => e.AnxietyTherapistComment).HasDefaultValue("");
            entity.Property(e => e.AppointmentsFrequency).HasDefaultValue("");
            entity.Property(e => e.AppointmentsStart).HasDefaultValue("");
            entity.Property(e => e.AppointmentsState).HasDefaultValue("");
            entity.Property(e => e.Avoidable).HasDefaultValue("");
            entity.Property(e => e.AvoidableSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.AvoidableTherapistComment).HasDefaultValue("");
            entity.Property(e => e.BirthPlace).HasDefaultValue("");
            entity.Property(e => e.BodyLanguage).HasDefaultValue("");
            entity.Property(e => e.CaseFormulation).HasDefaultValue("");
            entity.Property(e => e.Children)
                .HasDefaultValue("")
                .HasComment("Τόπος Κατοικίας");
            entity.Property(e => e.ChildrenAge)
                .HasDefaultValue("")
                .HasComment("Τόπος Κατοικίας");
            entity.Property(e => e.CommunicationMethod).HasDefaultValue("");
            entity.Property(e => e.ContactCode).HasDefaultValueSql("(left(abs(checksum(newid())),(6)))");
            entity.Property(e => e.Developmental).HasDefaultValue("");
            entity.Property(e => e.DevelopmentalSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.DevelopmentalTherapistComment).HasDefaultValue("");
            entity.Property(e => e.Doy).HasDefaultValue("");
            entity.Property(e => e.EatingDisorder).HasDefaultValue("");
            entity.Property(e => e.EatingDisorderSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.EatingDisorderTherapistComment).HasDefaultValue("");
            entity.Property(e => e.EconomicStatus).HasDefaultValue("");
            entity.Property(e => e.EducationLevel).HasDefaultValue("");
            entity.Property(e => e.Email).HasDefaultValue("");
            entity.Property(e => e.Email2).HasDefaultValue("");
            entity.Property(e => e.EmergencyContactName).HasDefaultValue("");
            entity.Property(e => e.EmergencyPhone).HasDefaultValue("");
            entity.Property(e => e.EmotionalDifficulties).HasDefaultValue("");
            entity.Property(e => e.EmotionalDifficultiesSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.EmotionalDifficultiesTherapistComment).HasDefaultValue("");
            entity.Property(e => e.EmotionalRemarks).HasDefaultValue("");
            entity.Property(e => e.EmotionalRemarksSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.EmotionalRemarksTherapistComment).HasDefaultValue("");
            entity.Property(e => e.EyeContact).HasDefaultValue("");
            entity.Property(e => e.FamilyMedicalHistory).HasDefaultValue("");
            entity.Property(e => e.FamilyMedicalHistorySupervisorComment).HasDefaultValue("");
            entity.Property(e => e.FamilyMedicalHistoryTherapistComment).HasDefaultValue("");
            entity.Property(e => e.FatherCharacteristics).HasDefaultValue("");
            entity.Property(e => e.FatherCharacteristicsSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.FatherCharacteristicsTherapistComment).HasDefaultValue("");
            entity.Property(e => e.FatherFamilyHistory).HasDefaultValue("");
            entity.Property(e => e.FatherFamilyHistorySupervisorComment).HasDefaultValue("");
            entity.Property(e => e.FatherFamilyHistoryTherapistComment).HasDefaultValue("");
            entity.Property(e => e.FatherFeedbackInMyFailure).HasDefaultValue("");
            entity.Property(e => e.FatherFeedbackInMyFailureSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.FatherFeedbackInMyFailureTherapistComment).HasDefaultValue("");
            entity.Property(e => e.FatherFeedbackInMySuccess).HasDefaultValue("");
            entity.Property(e => e.FatherFeedbackInMySuccessSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.FatherFeedbackInMySuccessTherapistComment).HasDefaultValue("");
            entity.Property(e => e.FatherInfo).HasDefaultValue("");
            entity.Property(e => e.FatherInfoSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.FatherInfoTherapistComment).HasDefaultValue("");
            entity.Property(e => e.FatherName).HasDefaultValue("");
            entity.Property(e => e.FirstName).HasDefaultValue("");
            entity.Property(e => e.GeneralBiographicalInfo).HasDefaultValue("");
            entity.Property(e => e.GeneralBiographicalInfoSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.GeneralBiographicalInfoTherapistComment).HasDefaultValue("");
            entity.Property(e => e.GeneralRequest).HasDefaultValue("");
            entity.Property(e => e.GeneralTraumaHistory).HasDefaultValue("");
            entity.Property(e => e.GeneralTraumaHistorySupervisorComment).HasDefaultValue("");
            entity.Property(e => e.GeneralTraumaHistoryTherapistComment).HasDefaultValue("");
            entity.Property(e => e.HealingExperience).HasDefaultValue("");
            entity.Property(e => e.HealthHistory).HasDefaultValue("");
            entity.Property(e => e.Histronic).HasDefaultValue("");
            entity.Property(e => e.HistronicSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.HistronicTherapistComment).HasDefaultValue("");
            entity.Property(e => e.Ideopsychocompression).HasDefaultValue("");
            entity.Property(e => e.IdeopsychocompressionSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.IdeopsychocompressionTherapistComment).HasDefaultValue("");
            entity.Property(e => e.ImportantFeedbackInMyFailure).HasDefaultValue("");
            entity.Property(e => e.ImportantFeedbackInMyFailureSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.ImportantFeedbackInMyFailureTherapistComment).HasDefaultValue("");
            entity.Property(e => e.ImportantFeedbackInMySuccess).HasDefaultValue("");
            entity.Property(e => e.ImportantFeedbackInMySuccessSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.ImportantFeedbackInMySuccessTherapistComment).HasDefaultValue("");
            entity.Property(e => e.InactiveReason).HasDefaultValue("");
            entity.Property(e => e.InterventionModel).HasDefaultValue("");
            entity.Property(e => e.LastMedicalCheckup).HasDefaultValue("");
            entity.Property(e => e.LastName).HasDefaultValue("");
            entity.Property(e => e.LivingStatus).HasDefaultValue("");
            entity.Property(e => e.MaritalStatus).HasDefaultValue("");
            entity.Property(e => e.Medication).HasDefaultValue("");
            entity.Property(e => e.Mobile1).HasDefaultValue("");
            entity.Property(e => e.Moods).HasDefaultValue("");
            entity.Property(e => e.MoodsSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.MoodsTherapistComment).HasDefaultValue("");
            entity.Property(e => e.MotherCharacteristics).HasDefaultValue("");
            entity.Property(e => e.MotherCharacteristicsSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.MotherCharacteristicsTherapistComment).HasDefaultValue("");
            entity.Property(e => e.MotherFamilyHistory).HasDefaultValue("");
            entity.Property(e => e.MotherFamilyHistorySupervisorComment).HasDefaultValue("");
            entity.Property(e => e.MotherFamilyHistoryTherapistComment).HasDefaultValue("");
            entity.Property(e => e.MotherFeedbackInMyFailure).HasDefaultValue("");
            entity.Property(e => e.MotherFeedbackInMyFailureSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.MotherFeedbackInMyFailureTherapistComment).HasDefaultValue("");
            entity.Property(e => e.MotherFeedbackInMySuccess).HasDefaultValue("");
            entity.Property(e => e.MotherFeedbackInMySuccessSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.MotherFeedbackInMySuccessTherapistComment).HasDefaultValue("");
            entity.Property(e => e.MotherInfo).HasDefaultValue("");
            entity.Property(e => e.MotherInfoSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.MotherInfoTherapistComment).HasDefaultValue("");
            entity.Property(e => e.Narcissistic).HasDefaultValue("");
            entity.Property(e => e.NarcissisticSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.NarcissisticTherapistComment).HasDefaultValue("");
            entity.Property(e => e.Narration).HasDefaultValue("");
            entity.Property(e => e.Notes).HasDefaultValue("");
            entity.Property(e => e.Occupation).HasDefaultValue("");
            entity.Property(e => e.OnLimit).HasDefaultValue("");
            entity.Property(e => e.OnLimitSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.OnLimitTherapistComment).HasDefaultValue("");
            entity.Property(e => e.OtherActivities).HasDefaultValue("");
            entity.Property(e => e.OtherDisorderInfo).HasDefaultValue("");
            entity.Property(e => e.OtherDisorderInfoSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.OtherDisorderInfoTherapistComment).HasDefaultValue("");
            entity.Property(e => e.OtherImportant).HasDefaultValue("");
            entity.Property(e => e.OtherImportantFromFatherFamily).HasDefaultValue("");
            entity.Property(e => e.OtherImportantFromFatherFamilySupervisorComment).HasDefaultValue("");
            entity.Property(e => e.OtherImportantFromFatherFamilyTherapistComment).HasDefaultValue("");
            entity.Property(e => e.OtherImportantFromMotherFamily).HasDefaultValue("");
            entity.Property(e => e.OtherImportantFromMotherFamilySupervisorComment).HasDefaultValue("");
            entity.Property(e => e.OtherImportantFromMotherFamilyTherapistComment).HasDefaultValue("");
            entity.Property(e => e.OtherImportantSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.OtherImportantTherapistComment).HasDefaultValue("");
            entity.Property(e => e.OtherMoodObservations).HasDefaultValue("");
            entity.Property(e => e.OtherMoodObservationsSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.OtherMoodObservationsTherapistComment).HasDefaultValue("");
            entity.Property(e => e.Paranoid).HasDefaultValue("");
            entity.Property(e => e.ParanoidSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.ParanoidTherapistComment).HasDefaultValue("");
            entity.Property(e => e.PassiveAggressive).HasDefaultValue("");
            entity.Property(e => e.PassiveAggressiveSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.PassiveAggressiveTherapistComment).HasDefaultValue("");
            entity.Property(e => e.Phone1).HasDefaultValue("");
            entity.Property(e => e.PoliceIdentity).HasDefaultValue("");
            entity.Property(e => e.PreschoolExperiences).HasDefaultValue("");
            entity.Property(e => e.PreschoolExperiencesSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.PreschoolExperiencesTherapistComment).HasDefaultValue("");
            entity.Property(e => e.Psychotic).HasDefaultValue("");
            entity.Property(e => e.PsychoticSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.PsychoticTherapistComment).HasDefaultValue("");
            entity.Property(e => e.ReferralToAnotherSpecialist).HasDefaultValue("");
            entity.Property(e => e.ReferralToAnotherSpecialistComment).HasDefaultValue("");
            entity.Property(e => e.Residence)
                .HasDefaultValue("")
                .HasComment("Τόπος Κατοικίας");
            entity.Property(e => e.Schizoid).HasDefaultValue("");
            entity.Property(e => e.SchizoidSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.SchizoidTherapistComment).HasDefaultValue("");
            entity.Property(e => e.Schizotype).HasDefaultValue("");
            entity.Property(e => e.SchizotypeSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.SchizotypeTherapistComment).HasDefaultValue("");
            entity.Property(e => e.SchoolExperiences).HasDefaultValue("");
            entity.Property(e => e.SchoolExperiencesSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.SchoolExperiencesTherapistComment).HasDefaultValue("");
            entity.Property(e => e.SessionFrequency).HasDefaultValue("");
            entity.Property(e => e.SessionOrigin)
                .HasDefaultValue("")
                .HasComment("ΠΡΟΕΛΕΥΣΗ ΣΥΝΕΔΡΙΑΣ\r\n\r\nDropDown:\r\nΠΑΡΑΠΟΜΠΗ\r\nINTERNET/SITE\r\nΠΡΟΩΘΗΤΙΚΗ ΕΝΕΡΓΕΙΑ\r\nΠΡΟΩΘΗΤΙΚΗ ΠΛΑΤΦΟΡΜΑ\r\nΓΝΩΣΤΟΣ ΠΕΛΑΤΗ\r\nΆΛΛΟ");
            entity.Property(e => e.SexComment).HasDefaultValue("");
            entity.Property(e => e.SexualOrientation).HasDefaultValue("");
            entity.Property(e => e.Sleep).HasDefaultValue("");
            entity.Property(e => e.SleepSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.SleepTherapistComment).HasDefaultValue("");
            entity.Property(e => e.SpecialRequest).HasDefaultValue("");
            entity.Property(e => e.SpecificTraumaHistory).HasDefaultValue("");
            entity.Property(e => e.SpecificTraumaHistorySupervisorComment).HasDefaultValue("");
            entity.Property(e => e.SpecificTraumaHistoryTherapistComment).HasDefaultValue("");
            entity.Property(e => e.TeenageExperiences).HasDefaultValue("");
            entity.Property(e => e.TeenageExperiencesSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.TeenageExperiencesTherapistComment).HasDefaultValue("");
            entity.Property(e => e.TherapistFirstView).HasDefaultValue("");
            entity.Property(e => e.VoiceTone).HasDefaultValue("");
            entity.Property(e => e.WorkExperiences).HasDefaultValue("");
            entity.Property(e => e.WorkExperiencesSupervisorComment).HasDefaultValue("");
            entity.Property(e => e.WorkExperiencesTherapistComment).HasDefaultValue("");
        });

        modelBuilder.Entity<Tenant>(entity =>
        {
            entity.Property(e => e.DateCreated).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FullName).HasDefaultValue("");
            entity.Property(e => e.LastLoggedUserFullName).HasDefaultValue("");
            entity.Property(e => e.SubscriptionPlan).HasDefaultValue("");
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.Property(e => e.AcademicTitle).HasDefaultValue("");
            entity.Property(e => e.AppointmentsListSort).HasDefaultValue("");
            entity.Property(e => e.AppointmentsScheduleView).HasDefaultValue("Week");
            entity.Property(e => e.AppointmentsScheduleVisibleResources).HasDefaultValue("");
            entity.Property(e => e.Email).HasDefaultValue("");
            entity.Property(e => e.FullName).HasDefaultValue("");
            entity.Property(e => e.JobTitle).HasDefaultValue("");
            entity.Property(e => e.Specialisation).HasDefaultValue("");
            entity.Property(e => e.Username).HasDefaultValue("");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}