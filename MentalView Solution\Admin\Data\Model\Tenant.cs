﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Admin.Data.Model;

public partial class Tenant
{
    [Key]
    public long TenantId { get; set; }

    [Required]
    [StringLength(50)]
    public string FullName { get; set; }

    public int? JoomlaUserId { get; set; }

    [Required]
    [StringLength(50)]
    public string SubscriptionPlan { get; set; }

    public int? SubscriptionPlanId { get; set; }

    [Required]
    [StringLength(50)]
    public string LastLoggedUserFullName { get; set; } = string.Empty;

    public long? LastLoggedUserId { get; set; } = null;

    [Column(TypeName = "datetime")]
    public DateTime? LastLoggedUserDate { get; set; }


    [Column(TypeName = "date")]
    public DateTime DateCreated { get; set; }
}