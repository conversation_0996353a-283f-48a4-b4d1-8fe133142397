/*!
*  filename: ej.touch.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["jquery"],n):n()})(function(){(function(n,t){function k(){var n={},t=[],i={webkit:/(chrome)[ \/]([\w.]+)/i,safari:/(webkit)[ \/]([\w.]+)/i,msie:/(msie) ([\w.]+)/i,opera:/(opera)(?:.*version|)[ \/]([\w.]+)/i,mozilla:/(mozilla)(?:.*? rv:([\w.]+)|)/i};for(var r in i)if(i.hasOwnProperty(r)&&(t=navigator.userAgent.match(i[r]),t)){n.name=t[1].toLowerCase();n.version=t[2];!navigator.userAgent.match(/Trident\/7\./)||(n.name="msie");break}return n.isMSPointerEnabled=n.name=="msie"&&n.version>9&&window.navigator.msPointerEnabled,n.pointerEnabled=window.navigator.pointerEnabled,n}function a(t,i,r){var u=r.type;r.type=i;n.event.dispatch.call(t,r);r.type=u}function it(n,t){if(t)for(var i in t)i in n||(n[i]=t[i])}function o(n){return n.originalEvent.touches?n.originalEvent.touches[0]:i?n.originalEvent:n}function d(n){var r=i?n.originalEvent.pointerType:n.originalEvent.touches?"touch":"mouse",u=i&&l==t?r==4?"mouse":"touch":r;return n.pointerType=u,n.type!="mousedown"&&n.type!="mouseup"&&it(n,n.originalEvent),u=="touch"&&(n.button=t),n}function s(t,i,r){var o,s,h,u={},e,f;return r&&(f=r.originalEvent.touches?[r.originalEvent.touches[0],t.originalEvent.changedTouches[0]]:[r.originalEvent,t.originalEvent],i._isSwipe||i._isdrag?(o=n.event.special.pinch._getdistance(f[0],f[1]),s=i.time,e={pageX:i.stopPoint.pageX,pageY:i.stopPoint.pageY}):i._isPinch&&(o=n.event.special.pinch.distance(t),s=t.timeStamp-r.timeStamp,h=i._pinchDistance),i._isDelta&&(u._dTime=t.timeStamp-r.timeStamp,u._x=f[1].pageX-f[0].pageX,u._y=f[1].pageY-f[0].pageY)),{options:t,delta:{time:u._dTime||null,X:u._x||null,Y:u._y||null},distance:o,scale:i._isPinch?h:null,time:s,velocity:{XY:o/u._dTime||null,X:u._x/u._dTime||null,Y:u._y/u._dTime||null},currentPosition:{pageX:e?e.pageX:null,pageY:e?e.pageY:null}}}function h(){var n=new Date;return n.getTime()}function v(n){i&&n.css("-ms-touch-action","pinch-zoom").css("touch-action","pinch-zoom")}var c=n(document);n.each("touchstart touchmove touchend tap doubletap taphold swipe swipeleft swiperight pinch pinchin pinchout pinchstop scrollstart scrollstop".split(" "),function(t,i){n.fn[i]=function(n){return n?this.on(i,n):this.trigger(i)};n.attrFn&&(n.attrFn[i]=!0)});var i=k().isMSPointerEnabled,l=k().pointerEnabled,y="ontouchstart"in window,rt="scroll",p=typeof orientation=="undefined",g=navigator.userAgent.match(/iPhone|iPad|iPod/i),u=i?l?"pointerdown":"MSPointerDown":y?"touchstart":"mousedown",f=i?l?"pointerup":"MSPointerUp":g?"touchend":y?"touchend":"mouseup",r=i?l?"pointermove":"MSPointerMove":y?"touchmove":"mousemove",w=i?l?"pointercancel":"MSPointerCancel":y?"touchcancel":"mouseleave",ut=i||!p?u:g?"touchstart":"touchstart mousedown",e=i||!p?f:"touchend mouseup",b=i||!p?r:"touchmove mousemove",nt=k(),tt=nt.name=="msie"&&nt.version=="9.0"?!0:!1;n.event.special.ejtouchmove={setup:function(){function s(){}function h(n){if(n.originalEvent&&!(n.which&&n.which!==1)){var f=n.target,u=n.originalEvent;i&&(t={x:u.x,y:u.y});o.on(r,l)}}function l(n){if(!(n.which&&n.which!==1)){var u=n.target,r=n.originalEvent;it(n,n.originalEvent);(!i||!t||Math.abs(t.x-r.x)>10||Math.abs(t.y-r.y)>10&&i)&&a(e,"ejtouchmove",n)}}var e=this,o=n(e),t;o.on(u,h);c.on(f,s);t={}}};n.event.special.swipeupdown={setup:function(){var e=this,i=n(e);v(i);i.on(u,function(u){function l(n){if(n.preventDefault(),e){var t=o(n);h={time:(new Date).getTime(),coords:[t.pageX,t.pageY]};Math.abs(e.coords[1]-h.coords[1])>10&&n.preventDefault()}}if(u.originalEvent){var a=u,c=o(u),e={time:(new Date).getTime(),coords:[c.pageX,c.pageY],origin:n(u.target)},h;i.on(r,l).one(f,function(u){if(i.off(r,l),e&&h&&h.time-e.time<1e3&&Math.abs(e.coords[1]-h.coords[1])>30&&Math.abs(e.coords[0]-h.coords[0])<75){var o={time:h.time-e.time,_isSwipe:!0,_isDelta:!0,stopPoint:h},f=s(u,o,a);e.origin.trigger(n.extend(!0,{type:"swipeupdown"},f)).trigger(n.extend(!0,{type:e.coords[1]>h.coords[1]?"swipeup":"swipedown"},f))}e=h=t})}})}};n.event.special.scrollstart={isEnabled:!0,setup:function(){function u(n,r){t=r;a(i,t?"scrollstart":"scrollstop",n)}var i=this,f=n(i),t,r;f.on(rt,function(i){n.event.special.scrollstart.isEnabled&&(t||u(i,!0),clearTimeout(r),r=setTimeout(function(){u(i,!1)},250))})}};n.event.special.tap={doubleTapThreshold:500,tapholdThreshold:650,canDoubleTap:function(t){return h()-t.doubleTapStartTime<=n.event.special.tap.doubleTapThreshold},setup:function(){var u=this,i=n(u),t=i.data(),r;v(i);t.isDoubleTapWait=!1;t.stopProcess=!1;t.preTouchend=null;t.preTouchstart=null;i.on(ut,function(f){function o(){clearTimeout(v);i.off(e,s);tt&&c.off(e,s);c.off(e,o);i.off(w,o);i.off(b,y);i.off("dragstart",it)}function y(n){var t=10,r=n.originalEvent.changedTouches?n.originalEvent.changedTouches[0]:n.originalEvent,u=f.originalEvent.changedTouches?f.originalEvent.changedTouches[0]:f.originalEvent;r.pageX-u.pageX<t&&r.pageX-u.pageX>-t&&r.pageY-u.pageY<t&&r.pageY-u.pageY>-t||(n.type=="mousemove"||n.type=="pointermove"&&n.originalEvent.pointerType=="mouse"||n.type=="MSPointerMove"&&n.originalEvent.pointerType==4?(clearTimeout(v),i.off(w,o),i.off(b,y)):o())}function s(i){var e,s;i.type=="touchend"&&(t.preTouchend=h());o();r!=i.target&&(i.type=="mouseup"||f.type=="pointerup"||"MSPointerUp")&&(e=i.target,jQuery.contains(r,e)?nt(i,r):jQuery.contains(e,r)||(s=n(r).parents().has(n(e)).first()[0],ej.isNullOrUndefined(s)||nt(i,s)));g===i.currentTarget&&(a(u,"tap",n.extend(d(i),{time:h()-t.startTime})),t.isDoubleTapWait&&n.event.special.tap.canDoubleTap(t)?(t.isDoubleTapWait=!1,a(u,"doubletap",n.extend(d(i),{time:h()-t.doubleTapStartTime}))):(t.isDoubleTapWait&&(t.isDoubleTapWait=!1,t.doubleTapStartTime=t.startTime),n.event.special.tap.canDoubleTap(t)&&(t.isDoubleTapWait=!0)))}function nt(n,t){n.target=t;n.toElement=t}function it(){o()}var g,l,v,p,k;if(f.originalEvent)if((f.type=="mousedown"||f.type=="pointerdown"||"MSPointerDown")&&(r=f.target),t=i.data(),t.startTime=h(),t.isDoubleTapWait||(t.doubleTapStartTime=t.startTime),f.type=="touchstart"&&(t.preTouchstart=t.startTime),t.stopProcess=f.type=="mousedown"&&(t.startTime-t.preTouchend<300||t.startTime-t.preTouchstart<30)?!0:!1,g=f.currentTarget,l=f.originalEvent,f.which&&f.which!==1||t.stopProcess)t.stopProcess&&(t.stopProcess=!1);else{i.on(e,s);c.on(e,o);if(tt)c.on(e,s);i.on(w,o);i.on(b,y);i.on("dragstart",it);p={};for(k in l)p[k]=l[k];v=setTimeout(function(){t.isDoubleTapWait&&(t.isDoubleTapWait=!1);a(u,"taphold",n.extend(d(f),{options:p,time:h()-t.startTime}))},n.event.special.tap.tapholdThreshold)}})}};n.event.special.swipe={scrollSupression:10,duration:1e3,horizontalDistance:30,verticalDistance:75,pointers:window.navigator.msPointerEnabled,startPoint:function(t){var i=o(t);return{time:(new Date).getTime(),Items:i,coords:[i.pageX,i.pageY],origin:n(t.target)}},stopPoint:function(n){var t=o(n);return{time:(new Date).getTime(),Items:t,coords:[t.pageX,t.pageY]}},handleSwipe:function(t,i,r,u){if((t.Items.pageY!=t.Items.clientY||i.Items.pageY!=i.Items.clientY)&&(n.event.special.swipe.horizontalDistance=130),i.time-t.time<n.event.special.swipe.duration&&Math.abs(t.coords[0]-i.coords[0])>n.event.special.swipe.horizontalDistance&&Math.abs(t.coords[1]-i.coords[1])<n.event.special.swipe.verticalDistance){var e={time:i.time-t.time,_isSwipe:!0,_isDelta:!0,stopPoint:i},f=s(r,e,u);t.origin.trigger(n.extend(!0,{type:"swipe"},f)).trigger(n.extend(!0,{type:t.coords[0]>i.coords[0]?"swipeleft":"swiperight"},f))}},setup:function(){var e=this,i=n(e);v(i);i.on(u,function(u){function s(t){e&&(o=n.event.special.swipe.stopPoint(t),Math.abs(e.coords[0]-o.coords[0])>n.event.special.swipe.scrollSupression&&t.preventDefault())}if(u.originalEvent){var e=n.event.special.swipe.startPoint(u),o,h=u;n(u.target).data("_dataTouchStart",{event:u,_now:(new Date).getTime()});i.on(r,s).one(f,function(u){i.off(r,s);e&&o&&n.event.special.swipe.handleSwipe(e,o,u,h);e=o=t})}})}};n.event.special.pinch={distance:function(t){return t.originalEvent.touches.length<2?null:n.event.special.pinch._getdistance(t.originalEvent.touches[0],t.originalEvent.touches[1])},_getdistance:function(n,t){return Math.sqrt((n.pageX-t.pageX)*(n.pageX-t.pageX)+(n.pageY-t.pageY)*(n.pageY-t.pageY))},setup:function(){var e=this,i=n(e);v(i);i.on(u,function(u){var h;if(u.originalEvent&&(h=u,u.originalEvent.touches&&u.originalEvent.touches.length>=2)){var o=n.event.special.pinch.distance(u),e,v=5,c=o,l,y=s(u,{_isPinch:!0,_pinchDistance:c},h);n(u.target).trigger(n.extend(!0,{type:"pinch"},y));function a(t){l=t;e=n.event.special.pinch.distance(t)||null;o&&e&&Math.abs(o-e)>v&&(n(t.target).trigger(n.extend(!0,{type:o>e?"pinchin":"pinchout"},s(t,{_isPinch:!0,_pinchDistance:c},h))),o=e)}i.on(r,a).one(f,function(){i.off(r,a);n(u.target).trigger(n.extend(!0,{type:"pinchstop"},s(l,{_isPinch:!0,_pinchDistance:e},h)));o=e=t})}})}};n.event.special.touchdrag={setup:function(){var e=this,i=n(e);v(i);i.on(u,function(u){function c(t){h&&(e=o(t),n.event.special.pinch._getdistance(h,e)>5&&n(t.target).trigger(n.extend(!0,{type:"touchdrag"},s(t,{_isdrag:!0,stopPoint:e,_isDelta:!0},l))))}if(u.originalEvent){var h=o(u),e,l=u;n(u.target).data("_dataTouchStart",{event:u,_now:(new Date).getTime()});i.on(r,c).one(f,function(){i.off(r,c);h=e=t})}})}};n.each({scrollstop:"scrollstart",doubletap:"tap",taphold:"tap",swipeleft:"swipe",swiperight:"swipe",swipedown:"swipeupdown",swipeup:"swipeupdown",pinchin:"pinch",pinchout:"pinch",pinchstop:"pinch"},function(t,i){n.event.special[t]={setup:function(){n(this).on(i,n.noop)}}})})(jQuery)});
