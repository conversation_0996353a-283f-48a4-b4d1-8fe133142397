/*!
*  filename: ej.pivotchart.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.globalize.min","./../common/ej.core.min","./../common/ej.data.min","./../common/ej.touch.min","./../common/ej.draggable.min","./../common/ej.scroller.min","./ej.dialog.min","./ej.waitingpopup.min","./../datavisualization/ej.chart.min","./ej.pivotanalysis.base.min","./ej.olap.base.min","./ej.pivot.common.min"],n):n()})(function(){(function(n,t,i){t.widget("ejPivotChart","ej.PivotChart",{_rootCSS:"e-pivotchart",element:null,model:null,_requiresID:!0,validTags:["div","span"],defaults:n.extend(t.datavisualization.Chart.prototype.defaults,{url:"",analysisMode:"pivot",operationalMode:"clientmode",cssClass:"",currentReport:"",customObject:{},enableRTL:!1,enableDefaultValue:!1,isResponsive:!1,enable3D:!1,enableContextMenu:!1,enableMultiLevelLabels:!1,enableXHRCredentials:!1,rotation:0,serviceMethodSettings:{initialize:"InitializeChart",drillDown:"DrillChart",exportPivotChart:"Export",paging:"paging"},dataSource:{data:null,sourceInfo:"",providerName:"ssas",isFormattedValues:!1,columns:[],cube:"",catalog:"",rows:[],values:[],filters:[]},locale:null,drillSuccess:null,load:null,beforeServiceInvoke:null,afterServiceInvoke:null,renderComplete:null,renderFailure:null,renderSuccess:null,beforeExport:null,beforeSeriesRender:null,beforePivotEnginePopulate:null}),dataTypes:{dataSource:{data:"data",columns:"array",rows:"array",values:"array",filters:"array"},marker:"data",crossHair:"data",size:"data",serviceMethodSettings:{initialize:"enum",drillDown:"enum",exportPivotChart:"enum",paging:"enum"},zooming:"data",customObject:"data"},observables:["title.text","commonSeriesOptions.type","locale"],titleText:t.util.valueFunction("title.text"),seriesType:t.util.valueFunction("commonSeriesOptions.type"),locale:t.util.valueFunction("locale"),getOlapReport:function(){return this._olapReport},setOlapReport:function(n){this._olapReport=n},getJSONRecords:function(){return this._JSONRecords},setJSONRecords:function(t){this._JSONRecords=n.parseJSON(t)},getPivotEngine:function(){return this._pivotEngine},setPivotEngine:function(n){this._pivotEngine=n},_init:function(){this._initPrivateProperties();this._load()},_destroy:function(){this._pivotEngine=null;this.element.empty().removeClass("e-pivotchart"+this.model.cssClass).removeAttr("tabindex");this._waitingPopup!=i&&this._waitingPopup.destroy();this.element.attr("class")==""&&this.element.removeAttr("class")},_initPrivateProperties:function(){this._id=this.element.attr("id");this._olapReport="";this._JSONRecords=null;this._currentAction="initialize";this._selectedItem="";this._selectedIndex=-1;this._selectedTagInfo=null;this._tagCollection=[];this._selectedTags=[];this._labelCurrentTags=[];this._startDrilldown=!1;this._drillAction="";this._initZooming=!1;this._dimensionIndex=-1;this._selectedMenuItem="";this._pivotEngine=null;this._curFocus=null;this._prevDrillElements=[];this._drillParams=[];this._currentDrillInfo=[];this._selectedSeriesInfo=[];this._waitingPopup=null;this._pivotClientObj=null;this._xTitle=null;this._yTitle=null;this._pagingSavedObjects={drillEngine:[],savedHdrEngine:[],curDrilledItem:{}}},_load:function(){var r,u;if(this.model.locale=!t.isNullOrUndefined(t.util.getVal(this.locale()))&&n.isFunction(this.locale)?this.locale():"en-US",r={action:"initialize",element:this.element,customObject:this.model.customObject},this._trigger("load",r),this.element.addClass(this.model.cssClass),n(this.element).parents(".e-pivotclient").length>0?(this._pivotClientObj=n(this.element).parents(".e-pivotclient").data("ejPivotClient"),n("#"+this._pivotClientObj._id+"_maxView")[0]?n("#"+this._pivotClientObj._id+"_maxView").ejWaitingPopup({showOnInit:!0}):t.isNullOrUndefined(this._waitingPopup)||this._waitingPopup.show()):(n("#"+this._id).parent()[0]&&(n("#"+this._id).parent()[0].style.position="relative",this.element.ejWaitingPopup({showOnInit:!0,appendTo:n("#"+this._id).parent()[0]})),this._waitingPopup=this.element.data("ejWaitingPopup"),this._waitingPopup.show()),this.model.zooming!=""&&(this._initZooming=!0),this.model.dataSource.data==null&&this.model.url!=""||this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode){if(this.model.operationalMode=t.PivotChart.OperationalMode.ServerMode,this.model.url==""){this.renderChartFromJSON("");return}this.model.beforeServiceInvoke!=null&&this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:this._currentAction,element:this.element,customObject:this.model.customObject});u=JSON.stringify(this.model.customObject);this.model.customObject!=""&&this.model.customObject!=null&&this.model.customObject!=i?this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.initialize,JSON.stringify({action:"initialize",currentReport:this.model.currentReport,customObject:u}),this.model.enableMultiLevelLabels&&this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot?this._generateData:this.renderControlSuccess):this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.initialize,JSON.stringify({action:"initialize"}),this.model.enableMultiLevelLabels&&this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot?this._generateData:this.renderControlSuccess)}else this.model.operationalMode=t.PivotChart.OperationalMode.ClientMode,this.model.analysisMode=this.model.dataSource.cube!=""?t.PivotChart.AnalysisMode.Olap:t.PivotChart.AnalysisMode.Pivot,this.refreshControl()},refreshControl:function(){var r,u,f,i;if(this.model.analysisMode==t.Pivot.AnalysisMode.Pivot)if(t.PivotAnalysis.setFieldCaptions(this.model.dataSource),t.isNullOrUndefined(this._pivotClientObj)||(this.model.valueSortSettings=this._pivotClientObj.model.valueSortSettings),r=t.PivotAnalysis.pivotEnginePopulate(this.model),u=n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejPivotClient"):null,this._pivotEngine=r.pivotEngine,r.pivotEngine.length>0)if(t.isNullOrUndefined(this._labelCurrentTags.expandedMembers)||this.model.enableMultiLevelLabels)this._generateData(this._pivotEngine);else{for(f=this._cloneEngine(this._pivotEngine),i=0;i<this._labelCurrentTags.expandedMembers.length;i++)this._cropData(f,this._labelCurrentTags.expandedMembers[i],0,!0);this._generateData(f)}else t.isNullOrUndefined(this._waitingPopup)||(t.isNullOrUndefined(u)||(u._isTimeOut=!1),this._waitingPopup.hide());else t.olap.base.getJSONData({action:"initialLoad"},this.model.dataSource,this)},_setFirst:!1,_setModel:function(i){var u=this.element.find("#"+this._id+"Container").data("ejChart"),r,f;for(r in i)switch(r){case"olapReport":this.setOlapReport(i[r]);this._load();break;case"jsonData":this.setJSONRecords(i[r]);this.element.renderChartFromJSON(i[r]);break;case"refreshPivotChart":this.element.renderChartFromJSON(i[r]);break;case"customObject":this.model.customObject=i[r];break;case"height":this.model.size.height=i[r];u&&u.redraw();break;case"width":this.model.size.width=i[r];u&&u.redraw();break;case"enableDefaultValue":this.model.enableDefaultValue=i[r];break;case"operationalMode":this.model.operationalMode=i[r];break;case"analysisMode":this.model.analysisMode=i[r];break;case"commonSeriesOptions":if(u){for(!t.isNullOrUndefined(t.util.getVal(i[r]).type)&&n.isFunction(t.util.getVal(i[r]).type)?this.seriesType(i[r].type()):t.isNullOrUndefined(i[r].type)||this.seriesType(i[r].type),u.model.commonSeriesOptions=n.extend({},u.model.commonSeriesOptions,i[r]),u.model.type=u.model.commonSeriesOptions.type=this.seriesType(),f=0;f<u.model.series.length;f++)u.model.series[f].type=u.model.type;u.redraw()}break;case"title":u&&(!t.isNullOrUndefined(t.util.getVal(i[r]).text)&&n.isFunction(t.util.getVal(i[r]).text)?this.titleText(i[r].text()):t.isNullOrUndefined(i[r].text)||this.titleText(i[r].text),u.model.title.text=this.titleText(),u.redraw());break;case"animation":this.model.animation=i[r];break;case"crossHair":this.model.crossHair=i[r];break;case"marker":this.model.marker=i[r];break;case"zooming":this.model.zooming=i[r];break;case"legend":this.model.legend=i[r];break;case"primaryXAxis":this.model.primaryXAxis=i[r];break;case"primaryYAxis":this.model.primaryYAxis=i[r];break;case"dataSource":this.model.dataSource=n.extend({},this.model.dataSource,i[r]);this.refreshControl();this._schemaData&&this._schemaData._load();break;case"locale":this.locale(t.util.getVal(i[r]));this._load();break;case"enableRTL":this.model.enableRTL=i[r];this._load();break;case"isResponsive":this.model.isResponsive=i[r];u&&u.redraw();break;case"enable3D":this.model.enable3D=i[r];u&&u.redraw();break;case"enableContextMenu":this.model.enableContextMenu=i[r];u&&u.redraw();break;case"enableMultiLevelLabels":this.model.enableMultiLevelLabels=i[r];this._load();break;case"rotation":this.model.rotation=i[r];u&&u.redraw();break;default:n.extend(!0,this.model,{},i[r])}u&&this.renderControlSuccess({JsonRecords:JSON.stringify(this.getJSONRecords()),OlapReport:this.getOlapReport()})},_keyDownPress:function(n){var u,r,i;n.which!==13||t.isNullOrUndefined(this._curFocus)||this._curFocus.click();(n.which===39||n.which===37||n.which===38||n.which===40)&&this.element.find(".e-dialog").length>0&&(n.preventDefault(),u=this.element.find(".e-dialog"),u.tabindex=-1,u.focus(),r=u.find("li"),n.which===39||n.which===40?(t.isNullOrUndefined(this._curFocus)?this._curFocus=r.eq(0):(this._curFocus.removeClass("e-hoverCell"),i=this._curFocus.next(),this._curFocus=i.length>0?i:r.eq(0)),this._curFocus.addClass("e-hoverCell")):(n.which===37||n.which===38)&&(t.isNullOrUndefined(this._curFocus)?this._curFocus=r.last():(this._curFocus.removeClass("e-hoverCell"),i=this._curFocus.prev(),this._curFocus=i.length>0?i:r.last()),this._curFocus.addClass("e-hoverCell")))},_wireEvents:function(){if(this._on(n(document),"keydown",this._keyDownPress),!this.model.enableContextMenu||this.element.parents(".e-pivotclient").length>0||this._on(this.element,"contextmenu","#"+this._id+"Container",t.proxy(function(){var u="<li class='chartTypes'><a>"+this._getLocalizedLabels("ChartTypes")+"<\/a><ul><li class='e-line'><a>"+this._getLocalizedLabels("Line")+"<\/a><\/li><li class='e-spline'><a>"+this._getLocalizedLabels("Spline")+"<\/a><\/li><li class='e-column'><a>"+this._getLocalizedLabels("Column")+"<\/a><\/li><li class='e-area'><a>"+this._getLocalizedLabels("Area")+"<\/a><\/li><li class='e-splinearea'><a>"+this._getLocalizedLabels("SplineArea")+"<\/a><\/li><li class='e-stepline'><a>"+this._getLocalizedLabels("StepLine")+"<\/a><\/li><li class='e-steparea'><a>Step Area<\/a><\/li><li class='e-pie'><a>Pie<\/a><\/li><li class='e-bar'><a>Bar<\/a><\/li><li class='e-stackingarea'><a>Stacking Area<\/a><\/li><li class='e-stackingcolumn'><a>"+this._getLocalizedLabels("StackingColumn")+"<\/a><\/li><li class='e-stackingbar'><a>"+this._getLocalizedLabels("StackingBar")+"<\/a><\/li><li class='e-funnel'><a>"+this._getLocalizedLabels("Funnel")+"<\/a><\/li><li class='e-pyramid'><a>"+this._getLocalizedLabels("Pyramid")+"<\/a><\/li><li class='e-doughnut'><a>"+this._getLocalizedLabels("Doughnut")+"<\/a><\/li><li class='e-scatter'><a>"+this._getLocalizedLabels("Scatter")+"<\/a><\/li><li class='e-bubble'><a>"+this._getLocalizedLabels("Bubble")+"<\/a><\/li><\/ul><\/li>",f="<li class='chart3DTypes'><a>"+this._getLocalizedLabels("TDCharts")+"<\/a><ul><li class='e-column'><a>"+this._getLocalizedLabels("ColumnTD")+"<\/a><\/li><li class='e-pie'><a>"+this._getLocalizedLabels("PieTD")+"<\/a><\/li><li class='e-bar'><a>"+this._getLocalizedLabels("BarTD")+"<\/a><\/li><li class='e-stackingbar'><a>"+this._getLocalizedLabels("StackingBarTD")+"<\/a><\/li><li class='e-stackingcolumn'><a>"+this._getLocalizedLabels("StackingColumnTD")+"<\/a><\/li><li class='e-disable3D'><a>"+this._getLocalizedLabels("DisableTD")+"<\/a><\/li><\/ul><\/li>",e="<li class='exportTypes'><a>"+this._getLocalizedLabels("Exporting")+"<\/a><ul><li class='e-excel'><a>"+this._getLocalizedLabels("Excel")+"<\/a><\/li><li class='e-word'><a>"+this._getLocalizedLabels("Word")+"<\/a><\/li><li class='e-pdf'><a>"+this._getLocalizedLabels("Pdf")+"<\/a><\/li><li class='e-png'><a>"+this._getLocalizedLabels("PNG")+"<\/a><\/li><li class='e-emf'><a>"+this._getLocalizedLabels("EMF")+"<\/a><\/li><li class='e-gif'><a>"+this._getLocalizedLabels("GIF")+"<\/a><\/li><li class='e-jpg'><a>"+this._getLocalizedLabels("JPG")+"<\/a><\/li><li class='e-bmp'><a>"+this._getLocalizedLabels("BMP")+"<\/a><\/li><\/ul><\/li>",o="<li class='e-smartLabels'><a>"+this._getLocalizedLabels("SmartLabels")+"<\/a><ul><li class='e-rotate45'><a>"+this._getLocalizedLabels("Rotate45")+"<\/a><\/li><li class='e-rotate90'><a>"+this._getLocalizedLabels("Rotate90")+"<\/a><\/li><li class='e-trim'><a>"+this._getLocalizedLabels("Trim")+"<\/a><\/li><li class='e-multipleRows'><a>"+this._getLocalizedLabels("MultipleRows")+"<\/a><\/li><li class='e-wrap'><a>"+this._getLocalizedLabels("Wrap")+"<\/a><\/li><li class='e-hiding'><a>"+this._getLocalizedLabels("Hide")+"<\/a><\/li><li class='e-wrapByWord'><a>"+this._getLocalizedLabels("WrapByWord")+"<\/a><\/li><\/ul><\/li>",s="<li class='e-interaction'><a>"+this._getLocalizedLabels("Interactions")+"<\/a><ul><li class='e-crossHair'><a>"+this._getLocalizedLabels("CrossHair")+"<\/a><\/li><li class='e-trackBall'><a>"+this._getLocalizedLabels("TrackBall")+"<\/a><\/li><li class='none'><a>"+this._getLocalizedLabels("None")+"<\/a><\/li><\/ul><\/li>",h=t.buildTag("ul#"+this._id+"_ContextMenu",t.buildTag("li.e-toolTip",t.buildTag("a",this._getLocalizedLabels("Tooltip"))[0].outerHTML)[0].outerHTML+t.buildTag("li.e-legend",t.buildTag("a",this._getLocalizedLabels("Legend"))[0].outerHTML)[0].outerHTML+(this.model.enable3D?"":t.buildTag("li.e-zooming",t.buildTag("a",this._getLocalizedLabels("Zooming"))[0].outerHTML)[0].outerHTML)+u+f+e+(this.model.enable3D?"":s+o))[0].outerHTML,r,i;n("#"+this._id).append(h);r=this.element.find("#"+this._id+"Container");this.element.find("#"+this._id+"_ContextMenu").ejMenu({width:"150px",menuType:t.MenuType.ContextMenu,contextMenuTarget:r,enableRTL:this.model.enableRTL,click:t.proxy(this._contextMenuClick,this)});i=t.buildTag("span.e-enabledState").addClass("e-icon").attr("aria-label",this._getLocalizedLabels("EnabledState"))[0].outerHTML;this.model.enable3D?n("#"+this._id+"_ContextMenu").find(".chart3DTypes ."+this.model.commonSeriesOptions.type+" a:eq(0)").append(i):n("#"+this._id+"_ContextMenu").find(".chartTypes ."+this.model.commonSeriesOptions.type+" a:eq(0)").append(i);this.model.commonSeriesOptions.tooltip.visible&&n("#"+this._id+"_ContextMenu").find(".e-toolTip a:eq(0)").append(i);this.model.zooming.enable&&n("#"+this._id+"_ContextMenu").find(".e-zooming a:eq(0)").append(i);this.model.legend.visible&&n("#"+this._id+"_ContextMenu").find(".e-legend a:eq(0)").append(i);this.model.primaryXAxis.labelIntersectAction!="none"&&(n("#"+this._id+"_ContextMenu").find(".e-smartLabels a:eq(0)").append(i),n("#"+this._id+"_ContextMenu").find(".e-smartLabels ."+this.model.primaryXAxis.labelIntersectAction+" a:eq(0)").append(i),this.model.primaryXAxis.labelIntersectAction=="hide"&&n("#"+this._id+"_ContextMenu").find(".e-smartLabels .e-hiding a:eq(0)").append(i));this.model.crosshair.type=="crosshair"&&this.model.crosshair.visible&&(n("#"+this._id+"_ContextMenu").find(".e-interaction a:eq(0)").append(i),n("#"+this._id+"_ContextMenu").find(".e-interaction .e-crossHair a:eq(0)").append(i),this.mode.enable3D||n("#"+this._id+"_ContextMenu").ejMenu("disableItem",this._getLocalizedLabels("Tooltip")));this.model.crosshair.type=="trackBall"&&this.model.crosshair.visible&&(n("#"+this._id+"_ContextMenu").find(".e-interaction a:eq(0)").append(i),n("#"+this._id+"_ContextMenu").find(".e-interaction .e-trackBall a:eq(0)").append(i),this.mode.enable3D||n("#"+this._id+"_ContextMenu").ejMenu("disableItem",this._getLocalizedLabels("Tooltip")));this.model.crosshair.visible||n("#"+this._id+"_ContextMenu").find(".e-interaction .none a:eq(0)").append(i)})),this._on(this.element,"click",".e-menuList.exp, .e-menuList.clp, .e-menuList.exit",this._seriesContextClick),t.isNullOrUndefined(this._pivotClientObj))n(window).on("resize",n.proxy(this._reSizeHandler,this));this._on(this.element,"click",".e-chart3DImg",function(i){var r=this;r._createDialog(i);n(".e-chart3DTypesIcon").click(function(n){r._contextMenuClick(n);t.isNullOrUndefined(this._pivotClientObj)||(r._pivotClientObj._unWireEvents(),r._pivotClientObj._wireEvents())})});this.element.parents(".e-pivotclient").length>0&&this._pivotClientObj.model.enableToolBar&&!this._pivotClientObj.model.displaySettings.enableFullScreen&&(this._on(this.element,"click",".e-toolTipImg",function(){var n=this;this.model.commonSeriesOptions.tooltip.visible=!this.model.commonSeriesOptions.tooltip.visible;this.renderControlSuccess({JsonRecords:JSON.stringify(this.getJSONRecords()),OlapReport:this.getOlapReport()});this.model.commonSeriesOptions.tooltip.visible?this.element.find(".e-toolTipImg").addClass("e-enabled"):this.element.find(".e-toolTipImg").removeClass("e-enabled")}),this._on(this.element,"click",".e-exportImg",function(t){var i=this;i._createDialog(t);n(".e-exportTypesIcon").click(function(n){i._contextMenuClick(n)})}),this._on(this.element,"click",".e-smartLabels",function(t){var i=this;i._createDialog(t);n(".e-smartLabelsIcon").click(function(n){i._contextMenuClick(n)})}),this._on(this.element,"click",".e-interaction",function(t){var i=this;i._createDialog(t);n(".e-interactionsIcon").click(function(n){i._contextMenuClick(n)})}),this._on(this.element,"click",".e-icon-xAxis-title",function(){this.element.find(".e-icon-xAxis-title").hasClass("e-enabled")?(this.element.find(".e-icon-xAxis-title").removeClass("e-enabled"),this.model.primaryXAxis.title.enable=!1):(this.element.find(".e-icon-xAxis-title").addClass("e-enabled"),this.model.primaryXAxis.title.enable=!0);this.renderControlSuccess({JsonRecords:JSON.stringify(this.getJSONRecords()),OlapReport:this.getOlapReport()})}),this._on(this.element,"click",".e-icon-yAxis-title",function(){this.element.find(".e-icon-yAxis-title").hasClass("e-enabled")?(this.element.find(".e-icon-yAxis-title").removeClass("e-enabled"),this.model.primaryYAxis.title.enable=!1):(this.element.find(".e-icon-yAxis-title").addClass("e-enabled"),this.model.primaryYAxis.title.enable=!0);this.renderControlSuccess({JsonRecords:JSON.stringify(this.getJSONRecords()),OlapReport:this.getOlapReport()})}),this._on(this.element,"click",".e-legend",function(n){var t=this;t._contextMenuClick(n)}),this._on(this.element,"click",".e-zooming",function(n){var t=this;t._contextMenuClick(n)}))},_unWireEvents:function(){this._off(n(document),"keydown",this._keyDownPress);n(document.body).off("click");this._off(this.element,"click",".e-menuList.exp, .e-menuList.clp, .e-menuList.exit",this._seriesContextClick);n(window).off("resize",n.proxy(this._reSizeHandler,this));this._off(this.element,"contextmenu");this._off(this.element,"click",".e-chartTypesImg");this._off(this.element,"click",".e-toolTipImg");this._off(this.element,"click",".e-chart3DImg");this._off(this.element,"click",".e-exportImg");this._off(this.element,"click",".e-legend");this._off(this.element,"click",".e-zooming");this._off(this.element,"click",".e-smartLabels");this._off(this.element,"click",".e-interaction");this._off(this.element,"click",".e-icon-xAxis-title");this._off(this.element,"click",".e-icon-yAxis-title")},_seriesContextClick:function(r){var h,c,f,s,o,e,u;if(this._curFocus=null,r.target.innerHTML.indexOf(this._getLocalizedLabels("Expand"))==-1&&r.target.innerHTML.indexOf(this._getLocalizedLabels("Collapse"))==-1&&r.target.innerHTML.indexOf(this._getLocalizedLabels("Exit"))==-1)return!1;if(r.target.innerHTML==this._getLocalizedLabels("Exit"))this.element.find("#"+this._id+"ExpandMenu, .e-expandMenu, .e-dialog").remove();else if(r.target.innerHTML.indexOf(this._getLocalizedLabels("Expand"))>-1)if(this._drillAction="drilldown",n(this.element).parents(".e-pivotclient").length>0?(o=n(this.element).parents(".e-pivotclient").data("ejPivotClient"),n("#"+o._id+"_maxView")[0]?n("#"+o._id+"_maxView").ejWaitingPopup({showOnInit:!0}):t.isNullOrUndefined(this._waitingPopup)||this._waitingPopup.show()):t.isNullOrUndefined(this._waitingPopup)||this._waitingPopup.show(),this.model.operationalMode==t.PivotChart.OperationalMode.ClientMode)f=this.model.enableRTL?r.target.innerHTML.split(" - ")[0]:r.target.innerHTML.split(" - ")[1],this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot?this._getChartDrillDown("",f,r):(this._labelCurrentTags.collapsedMembers=n.grep(this._labelCurrentTags.collapsedMembers,function(n){return n!=f}),h=n(this.getJSONRecords().seriesTags).filter(function(n,t){return t.split("::")[2]==f})[0],c=t.olap._mdxParser._splitCellInfo(h),this._getChartDrillDown(h,c,r));else if(s=JSON.stringify(this.model.customObject),this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot)f=this.model.enableRTL?r.target.innerHTML.split(" - ")[0]:r.target.innerHTML.split(" - ")[1],this._selectedItem=f,t.isNullOrUndefined(this._labelCurrentTags.expandedMembers)&&(this._labelCurrentTags.expandedMembers=[]),this._labelCurrentTags.expandedMembers.push(f),this.model.beforeServiceInvoke!=null&&this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:this._drillAction,element:this.element,customObject:this.model.customObject}),this.model.customObject!=""&&this.model.customObject!=null&&this.model.customObject!=i?this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:"drilldown",drilledSeries:JSON.stringify(this._labelCurrentTags.expandedMembers)+(t.isNullOrUndefined(this._pivotClientObj)?"":"-##-"+JSON.stringify(this._pivotClientObj.model.valueSortSettings)),customObject:s,currentReport:JSON.parse(this.getOlapReport()).Report}),this.renderControlSuccess):this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:"drilldown",drilledSeries:JSON.stringify(this._labelCurrentTags.expandedMembers),currentReport:JSON.parse(this.getOlapReport()).Report}),this.renderControlSuccess);else{u=this;this._isDrilled||(this._isDrilled=!0,jQuery.each(r.target.parentElement.children,function(n,t){t.innerHTML==r.target.innerHTML&&(u._dimensionIndex=n)}));try{e=JSON.parse(this.getOlapReport()).Report}catch(v){e=this.getOlapReport()}this._drillAction="drilldown";this._selectedItem=this.model.enableRTL?n.trim(r.target.innerHTML.replace(" - "+this._getLocalizedLabels("Expand"),"")):n.trim(r.target.innerHTML.replace(this._getLocalizedLabels("Expand")+" - ",""));jQuery.each(this._labelCurrentTags,function(n,t){if(t.name==u._selectedItem)return u._tagCollection=[],u._tagCollection=u._selectedTags.slice(),u._selectedTagInfo=t.tag,u._selectedMenuItem=t.name,!1});this._startDrilldown=!0;this.model.beforeServiceInvoke!=null&&this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:this._drillAction,element:this.element,customObject:this.model.customObject});this.model.customObject!=""&&this.model.customObject!=null&&this.model.customObject!=i?t.isNullOrUndefined(this._pivotClientObj)?this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:this.model.enableMultiLevelLabels?"drilldown#fullchart":"drilldown",drilledSeries:this._selectedTagInfo,olapReport:e,customObject:s}),this.renderControlSuccess):this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:this.model.enableMultiLevelLabels?"drilldown#fullchart":"drilldown",drilledSeries:this._selectedTagInfo,olapReport:e,clientReports:this._pivotClientObj.reports,customObject:s}),this.renderControlSuccess):t.isNullOrUndefined(this._pivotClientObj)?this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:this.model.enableMultiLevelLabels?"drilldown#fullchart":"drilldown",drilledSeries:this._selectedTagInfo,olapReport:e}),this.renderControlSuccess):this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:this.model.enableMultiLevelLabels?"drilldown#fullchart":"drilldown",drilledSeries:this._selectedTagInfo,olapReport:e,clientReports:this._pivotClientObj.reports}),this.renderControlSuccess)}else if(this._drillAction="drillup",n(this.element).parents(".e-pivotclient").length>0?(o=n(this.element).parents(".e-pivotclient").data("ejPivotClient"),n("#"+o._id+"_maxView")[0]?n("#"+o._id+"_maxView").ejWaitingPopup({showOnInit:!0}):t.isNullOrUndefined(this._waitingPopup)||this._waitingPopup.show()):t.isNullOrUndefined(this._waitingPopup)||this._waitingPopup.show(),this.model.operationalMode==t.PivotChart.OperationalMode.ClientMode)f=this.model.enableRTL?r.target.innerHTML.split(" - ")[0]:r.target.innerHTML.split(" - ")[1],this._getChartDrillDown("",f,r);else if(s=JSON.stringify(this.model.customObject),this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot){var f=this.model.enableRTL?r.target.innerHTML.split(" - ")[0]:r.target.innerHTML.split(" - ")[1],l=!1,a=[];jQuery.map(this._labelCurrentTags.expandedMembers,function(n){n==f&&(l=!0);l||a.push(n)});this._selectedItem=f;this._labelCurrentTags.expandedMembers=a;this._currentDrillInfo=n.extend([],this._labelCurrentTags.expandedMembers,!0).concat([f]);n(this.element).parents(".e-pivotclient").length>0&&(this._labelCurrentTags.expandedMembers=[],o=n(this.element).parents(".e-pivotclient").data("ejPivotClient"),o._drillParams.length>0&&(o._drillParams=n.grep(o._drillParams,function(n){return n.indexOf(f)<0}),o._getDrilledMember()));this.model.customObject!=""&&this.model.customObject!=null&&this.model.customObject!=i?this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:"drilldown",drilledSeries:JSON.stringify(this._labelCurrentTags.expandedMembers)+(t.isNullOrUndefined(this._pivotClientObj)?"":"-##-"+JSON.stringify(this._pivotClientObj.model.valueSortSettings)),customObject:s,currentReport:JSON.parse(this.getOlapReport()).Report}),this.renderControlSuccess):this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:"drilldown",drilledSeries:JSON.stringify(this._labelCurrentTags.expandedMembers),currentReport:JSON.parse(this.getOlapReport()).Report}),this.renderControlSuccess)}else{try{e=JSON.parse(this.getOlapReport()).Report}catch(v){e=this.getOlapReport()}this._drillAction="drillup";this._selectedItem=this.model.enableRTL?n.trim(r.target.innerHTML.replace(" - "+this._getLocalizedLabels("Collapse"),"")):n.trim(r.target.innerHTML.replace(this._getLocalizedLabels("Collapse")+" - ",""));this._tagCollection=[];this._tagCollection=this._selectedTags.slice();u=this;jQuery.each(this._tagCollection,function(n,t){if(t.name==u._selectedItem)return u._selectedIndex=n,u._selectedTagInfo=t.tag,u._tagCollection.splice(n,u._tagCollection.length),!1});this._tagCollection.length==0&&(this._isDrilled=!1);this._startDrilldown=!0;this.model.beforeServiceInvoke!=null&&this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:this._drillAction,element:this.element,customObject:this.model.customObject});this.model.customObject!=""&&this.model.customObject!=null&&this.model.customObject!=i?t.isNullOrUndefined(this._pivotClientObj)?this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:this.model.enableMultiLevelLabels?"drillup#fullchart":"drillup",drilledSeries:this._selectedTagInfo,olapReport:e,customObject:s}),this.renderControlSuccess):this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:this.model.enableMultiLevelLabels?"drillup#fullchart":"drillup",drilledSeries:this._selectedTagInfo,olapReport:e,clientReports:this._pivotClientObj.reports,customObject:s}),this.renderControlSuccess):t.isNullOrUndefined(this._pivotClientObj)?this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:this.model.enableMultiLevelLabels?"drillup#fullchart":"drillup",drilledSeries:this._selectedTagInfo,olapReport:e}),this.renderControlSuccess):this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:this.model.enableMultiLevelLabels?"drillup#fullchart":"drillup",drilledSeries:this._selectedTagInfo,olapReport:e,clientReports:reports}),this.renderControlSuccess)}},_createDialog:function(r){var u=this,f,e,o,s;n(r.target).hasClass("e-exportImg")?(f=t.buildTag("div.e-exportTypesDialog",t.buildTag("table",t.buildTag("tbody",t.buildTag("tr",t.buildTag("td",t.buildTag("div.e-excel e-exportTypesIcon").attr({title:this._getLocalizedLabels("Excel"),"aria-label":this._getLocalizedLabels("Excel"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-word e-exportTypesIcon").attr({title:this._getLocalizedLabels("Word"),"aria-label":this._getLocalizedLabels("Word"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-pdf e-exportTypesIcon").attr({title:this._getLocalizedLabels("Pdf"),"aria-label":this._getLocalizedLabels("Pdf"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-png e-exportTypesIcon").attr({title:this._getLocalizedLabels("PNG"),"aria-label":this._getLocalizedLabels("PNG"),tabindex:0})[0].outerHTML)[0].outerHTML)[0].outerHTML+t.buildTag("tr",t.buildTag("td",t.buildTag("div.e-emf e-exportTypesIcon").attr({title:this._getLocalizedLabels("EMF"),"aria-label":this._getLocalizedLabels("EMF"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-gif e-exportTypesIcon").attr({title:this._getLocalizedLabels("GIF"),"aria-label":this._getLocalizedLabels("GIF"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-jpg e-exportTypesIcon").attr({title:this._getLocalizedLabels("JPG"),"aria-label":this._getLocalizedLabels("JPG"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-bmp e-exportTypesIcon").attr({title:this._getLocalizedLabels("BMF"),"aria-label":this._getLocalizedLabels("BMF"),tabindex:0})[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML),this.element.find("div.e-exportTypesDialog").length==0&&(n(f).appendTo(this.element),n(f).css("left",r.target.offsetLeft+20+"px").css("top",r.target.offsetTop+15+"px"),this.model.enableRTL&&n(f).addClass("e-rtl"))):n(r.target).hasClass("e-chart3DImg")?(e=t.buildTag("div.e-chart3DTypesDialog",t.buildTag("table",t.buildTag("tbody",t.buildTag("tr",t.buildTag("td",t.buildTag("div.e-column3D e-chart3DTypesIcon").attr({title:this._getLocalizedLabels("ColumnTD"),"aria-label":this._getLocalizedLabels("ColumnTD"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-pie3D e-chart3DTypesIcon").attr({title:this._getLocalizedLabels("PieTD"),"aria-label":this._getLocalizedLabels("PieTD"),tabindex:0})[0].outerHTML)[0].outerHTML)[0].outerHTML+t.buildTag("tr",t.buildTag("td",t.buildTag("div.e-bar3D e-chart3DTypesIcon").attr({title:this._getLocalizedLabels("BarTD"),"aria-label":this._getLocalizedLabels("BarTD"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-stackingbar3D e-chart3DTypesIcon").attr({title:this._getLocalizedLabels("StackingBarTD"),"aria-label":this._getLocalizedLabels("StackingBarTD"),tabindex:0})[0].outerHTML)[0].outerHTML)[0].outerHTML+t.buildTag("tr",t.buildTag("td",t.buildTag("div.e-stackingcolumn3D e-chart3DTypesIcon").attr({title:this._getLocalizedLabels("StackingColumnTD"),"aria-label":this._getLocalizedLabels("StackingColumnTD"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-disable3D e-chart3DTypesIcon").attr({title:this._getLocalizedLabels("DisableTD"),"aria-label":this._getLocalizedLabels("DisableTD"),tabindex:0})[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML),this.element.find("div.e-chart3DTypesDialog").length==0&&(n(e).appendTo(this.element),n(e).css("left",r.target.offsetLeft+20+"px").css("top",r.target.offsetTop+15+"px"),u.model.enable3D&&n("."+this.model.type+"3D").addClass("e-enabled"),this.model.enableRTL&&n(e).addClass("e-rtl"))):n(r.target).hasClass("e-smartLabels")?(o=t.buildTag("div.e-smartLabelsDialog",t.buildTag("table",t.buildTag("tbody",t.buildTag("tr",t.buildTag("td",t.buildTag("div.e-rotate45 e-smartLabelsIcon").attr({title:this._getLocalizedLabels("Rotate45"),"aria-label":this._getLocalizedLabels("Rotate45"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-rotate90 e-smartLabelsIcon").attr({title:this._getLocalizedLabels("Rotate90"),"aria-label":this._getLocalizedLabels("Rotate90"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-trim e-smartLabelsIcon").attr({title:this._getLocalizedLabels("Trim"),"aria-label":this._getLocalizedLabels("Trim"),tabindex:0})[0].outerHTML)[0].outerHTML)[0].outerHTML+t.buildTag("tr",t.buildTag("td",t.buildTag("div.e-multipleRows e-smartLabelsIcon").attr({title:this._getLocalizedLabels("MultipleRows"),"aria-label":this._getLocalizedLabels("MultipleRows"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-wrap e-smartLabelsIcon").attr({title:this._getLocalizedLabels("Wrap"),"aria-label":this._getLocalizedLabels("Wrap"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-hiding e-smartLabelsIcon").attr({title:this._getLocalizedLabels("Hide"),"aria-label":this._getLocalizedLabels("Hide"),tabindex:0})[0].outerHTML)[0].outerHTML)[0].outerHTML+t.buildTag("tr",t.buildTag("td",t.buildTag("div.e-wrapByWord smartLabelsIcon").attr({title:this._getLocalizedLabels("WrapByWord"),"aria-label":this._getLocalizedLabels("WrapByWord"),tabindex:0})[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML),this.element.find("div.e-smartLabelsDialog").length==0&&(n(o).appendTo(this.element),n(o).css("left",r.target.offsetLeft+20+"px").css("top",r.target.offsetTop+15+"px"),u.model.primaryXAxis.labelIntersectAction!="none"&&u.model.primaryXAxis.labelIntersectAction!="hide"&&u.model.primaryXAxis.labelIntersectAction!=i&&n("."+u.model.primaryXAxis.labelIntersectAction).addClass("e-enabled"),u.model.primaryXAxis.labelIntersectAction=="hide"&&n(".e-hiding").addClass("e-enabled"),this.model.enableRTL&&n(o).addClass("e-rtl"))):n(r.target).hasClass("e-interaction")&&(s=t.buildTag("div.e-interactionsDialog",t.buildTag("table",t.buildTag("tbody",t.buildTag("tr",t.buildTag("td",t.buildTag("div.e-crossHair e-interactionsIcon").attr({title:this._getLocalizedLabels("CrossHair"),"aria-label":this._getLocalizedLabels("CrossHair"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-trackBall e-interactionsIcon").attr({title:this._getLocalizedLabels("TrackBall"),"aria-label":this._getLocalizedLabels("TrackBall"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.none e-interactionsIcon").attr({title:this._getLocalizedLabels("None"),"aria-label":this._getLocalizedLabels("None"),tabindex:0})[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML),this.element.find("div.e-interactionsDialog").length==0&&(n(s).appendTo(this.element),n(s).css("left",r.target.offsetLeft+20+"px").css("top",r.target.offsetTop+15+"px"),this.model.crosshair.type=="crosshair"&&this.model.crosshair.visible&&this.element.find(".e-crossHair").addClass("e-enabled"),this.model.crosshair.type=="trackBall"&&this.model.crosshair.visible&&this.element.find(".e-trackBall").addClass("e-enabled"),this.model.crosshair.visible||this.element.find(".none").addClass("e-enabled"),this.model.enableRTL&&n(s).addClass("e-rtl")))},_contextMenuClick:function(i){chrtObj=n("#"+this._id).data("ejPivotChart");var r=n(this.element).parents(".e-pivotclient").length>0?n(i.target).attr("class").split(" ")[0]:n(i.element).attr("class").split(" ")[0];if(n(i.element).parents().hasClass("chartTypes"))chrtObj.model.enable3D=!1,chrtObj.model.type=chrtObj.model.commonSeriesOptions.type=!t.isNullOrUndefined(r)&&r.split("-").length>1?r.split("-")[1]:r,chrtObj.model.type=="funnel"&&(chrtObj.model.commonSeriesOptions.marker={dataLabel:{visible:!0,shape:"none",font:{color:"white",size:"12px",fontWeight:"lighter"}}}),(chrtObj.model.type=="pie"||chrtObj.model.type=="doughnut")&&(chrtObj.model.commonSeriesOptions.explode=!0),chrtObj.renderControlSuccess({JsonRecords:JSON.stringify(chrtObj.getJSONRecords()),OlapReport:chrtObj.getOlapReport()});else if(n(i.element).hasClass("e-toolTip"))chrtObj.model.commonSeriesOptions.tooltip.visible=!chrtObj.model.commonSeriesOptions.tooltip.visible,chrtObj.renderControlSuccess({JsonRecords:JSON.stringify(chrtObj.getJSONRecords()),OlapReport:chrtObj.getOlapReport()});else if(n(i.element).hasClass("e-legend")||n(i.target).hasClass("e-legend"))chrtObj.model.legend.visible=!chrtObj.model.legend.visible,chrtObj.renderControlSuccess({JsonRecords:JSON.stringify(chrtObj.getJSONRecords()),OlapReport:chrtObj.getOlapReport()});else if(n(i.element).hasClass("e-zooming")||n(i.target).hasClass("e-zooming"))chrtObj.model.zooming.enable=!chrtObj.model.zooming.enable,chrtObj.renderControlSuccess({JsonRecords:JSON.stringify(chrtObj.getJSONRecords()),OlapReport:chrtObj.getOlapReport()});else if(n(i.element).parents().hasClass("e-smartLabels")||n(i.target).hasClass("e-smartLabelsIcon"))chrtObj.model.primaryXAxis.labelIntersectAction=r=="e-hiding"?"hide":!t.isNullOrUndefined(r)&&r.split("-").length>1?r.split("-")[1]:r,chrtObj.renderControlSuccess({JsonRecords:JSON.stringify(chrtObj.getJSONRecords()),OlapReport:chrtObj.getOlapReport()});else if(n(i.element).parents().hasClass("e-interaction")||n(i.target).hasClass("e-interactionsIcon"))r=="e-crossHair"&&(chrtObj.model.crosshair.type=t.datavisualization.Chart.CrosshairType.Crosshair,chrtObj.model.crosshair.visible=!0),r=="e-trackBall"&&(chrtObj.model.crosshair.type=t.datavisualization.Chart.CrosshairType.TrackBall,chrtObj.model.crosshair.visible=!0),r=="none"&&(chrtObj.model.crosshair.visible=!1),chrtObj.renderControlSuccess({JsonRecords:JSON.stringify(chrtObj.getJSONRecords()),OlapReport:chrtObj.getOlapReport()});else if(n(i.element).parents().hasClass("chart3DTypes")||n(i.target).hasClass("e-chart3DTypesIcon"))chrtObj.model.enable3D=!0,r!="e-disable3D"&&(r=r.split("3")[0]),r=="e-disable3D"&&(chrtObj.model.enable3D=!1,r=chrtObj.model.commonSeriesOptions.type),chrtObj.model.type=chrtObj.model.commonSeriesOptions.type=!t.isNullOrUndefined(r)&&r.split("-").length>1?r.split("-")[1]:r,r=="e-pie"&&(chrtObj.model.rotation=24),chrtObj.renderControlSuccess({JsonRecords:JSON.stringify(chrtObj.getJSONRecords()),OlapReport:chrtObj.getOlapReport()});else{n(this.element).parents(".e-pivotclient").length>0&&!t.isNullOrUndefined(this._pivotClientObj)&&(this.model.beforeExport=this._pivotClientObj.model.beforeExport);r=!t.isNullOrUndefined(r)&&r.split("-").length>1?r.split("-")[1]:r;switch(r){case"excel":case"word":case"pdf":chrtObj.exportPivotChart(r,"Sample");break;case"png":chrtObj.exportPivotChart(r,"Sample",t.PivotChart.ExportOptions.PNG);break;case"emf":chrtObj.exportPivotChart(r,"Sample",t.PivotChart.ExportOptions.EMF);break;case"gif":chrtObj.exportPivotChart(r,"Sample",t.PivotChart.ExportOptions.GIF);break;case"jpg":chrtObj.exportPivotChart(r,"Sample",t.PivotChart.ExportOptions.JPG);break;case"bmp":chrtObj.exportPivotChart(r,"Sample",t.PivotChart.ExportOptions.BMP)}}},generateJSON:function(n,i){var u,r,f;if(!t.isNullOrUndefined(this._labelCurrentTags.expandedMembers)&&this._labelCurrentTags.expandedMembers.length>0&&!this.model.enableMultiLevelLabels)for(u=0,r=0;r<this._labelCurrentTags.expandedMembers.length;r++)if(!t.isNullOrUndefined(this._labelCurrentTags.expandedMembers[r])&&this._labelCurrentTags.expandedMembers[r].length>0)for(r>0&&!t.isNullOrUndefined(this._labelCurrentTags.expandedMembers[r-1])&&this._labelCurrentTags.expandedMembers[r-1].length>0&&this._cropData(i,this._selectedSeriesInfo[r-1].split("::")[2],r-1+u,!1),f=0;f<this._labelCurrentTags.expandedMembers[r].length;f++)this._cropData(i,this._labelCurrentTags.expandedMembers[r][f].split("::")[2],r+u,!0),u++;this._generateData(i)},_jsonToEngine:function(n){for(var u,f=JSON.parse(n),i=[],r=0;r<f.length;r++)u=parseInt(f[r].Index.split(",")[0]),t.isNullOrUndefined(i[u])?(i[u]=[],i[u].push(f[r])):i[u].push(f[r]);return i},_generateData:function(r){var tt={},ht,s,lt,k,o,a,ut,ft,d,v,w,g,b,at,p,f,st,u;if(!t.isNullOrUndefined(this._pivotClientObj)&&this._pivotClientObj._isExporting&&(tt=n.extend(!0,{},{pivotEngine:this._pivotEngine,labelCurrentTags:this._labelCurrentTags,drillParams:this._drillParams})),ht=!1,ht=r[2]!=i?r[2].Value=="Olap"||r[2]=="Olap"?!0:!1:r.d!=i?!t.isNullOrUndefined(r.d[2])&&r.d[2].Value=="Olap"?!0:!1:r.AnalysisMode=="Olap"?!0:!1,ht)this.renderControlSuccess(r);else{var e={seriesNames:[],chartLables:[],labelTags:[],multiLevelLabels:[],multiLevelLabelTags:[],seriesTags:[],points_Y:[],labelFormat:[],measureNames:""},it="",ct=[];if(this.model.enableMultiLevelLabels&&this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot&&this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode&&(s={},r[0]!=i?(n(this.element).parents(".e-pivotclient").length>0?s.JsonRecords=t.isNullOrUndefined(r[0].Value)?r[0]:r[0].Value:(s.JsonRecords={},s.JsonRecords.ChartJSON=t.isNullOrUndefined(r[0].Value)?r[0]:r[0].Value,s.JsonRecords.GridJSON=t.isNullOrUndefined(r[3].Value)?r[3]:r[3].Value),s.PivotReport=t.isNullOrUndefined(r[1].Value)?r[1]:r[1].Value,r=s):r.d!=i&&(n(this.element).parents(".e-pivotclient").length>0?t.isNullOrUndefined(r.d[0])||(lt=r.d[0].Key,s.JsonRecords=lt=="JsonRecords"?r.d[0].Value:[]):(s.JsonRecords={},s.JsonRecords.ChartJSON=!t.isNullOrUndefined(r.d[0])&&r.d[0].Key=="JsonRecords"?r.d[0].Value:[],s.JsonRecords.GridJSON=!t.isNullOrUndefined(r.d[3])&&r.d[3].Key=="GridJSON"?r.d[3].Value:[]),s.PivotReport=!t.isNullOrUndefined(r.d[1])&&(r.d[1].Key=="PivotReport"||r.d[1].Key=="OlapReport")?r.d[1].Value:[],r=s),it=r.PivotReport||r.OlapReport,r=t.isNullOrUndefined(r.JsonRecords.GridJSON)?t.isNullOrUndefined(r.GridJSON)?r.JsonRecords:this._jsonToEngine(r.GridJSON):this._jsonToEngine(r.JsonRecords.GridJSON),this._pivotEngine=r,ct=t.isNullOrUndefined(JSON.parse(it).values)?JSON.parse(it).PivotCalculations:JSON.parse(JSON.parse(it).values),e.labelFormat=n.map(ct,function(n){return n.Format.toLowerCase()})),k=r.length,k>0&&r[0].length>0){var ot=r[0].length,rt=[],nt=[];for(o=this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot?!t.isNullOrUndefined(this._labelCurrentTags.expandedMembers)&&!this.model.enableMultiLevelLabels?this.model.dataSource.rows.length-this._labelCurrentTags.expandedMembers.length:this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode?r[0][0].ColSpan:this.model.dataSource.rows.length:r[0][0].ColSpan,this.model.enableMultiLevelLabels&&(this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot&&this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode||this.model.analysisMode==t.PivotChart.AnalysisMode.Olap)?a=r[0][0].RowSpan:(a=this.model.dataSource.columns.length,this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot?a+=this.model.dataSource.values.length>0?1:0:a=r[0][0].RowSpan),ut="",u=o;u<k;u++){for(ft="",d=!1,f=0;f<a;f++)if(!t.isNullOrUndefined(r[u][f])){if(r[u][f].CSS.indexOf("summary")>=0){d=!0;rt.push(u);break}r[u][f].Info.indexOf("Measure")>=0&&ut.indexOf(r[u][f].Value)<0&&(ut+=ut==""?r[u][f].Value:"~~"+r[u][f].Value);ft+=(ft==""?"":r[u][f].Value==""?"":"~~")+r[u][f].Value}d||ft==""||e.seriesNames.push(ft)}for(u=a;u<ot;u++)if(this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot){if(this.model.enableMultiLevelLabels&&o>1){this._drillParams=this._drillAction!=""?this._drillParams:[];var c=n.extend(!0,[],this._drillParams.sort()),l=c.length>0?-1:0,et="";for(u=a;u<ot;u++)for(v=c[0],w="",f=0;f<o;f++)w+=w==""?r[f][u].Value:">#>"+r[f][u].Value,(l==-1||et==""||w==c[1])&&(w==v||w==c[1])&&(w==c[1]&&(v=c[1],c.splice(0,1)),l=f+1,et=w),!t.isNullOrUndefined(v)&&l>0&&l==o-1&&f==o-1?(nt.push(u),e.chartLables.push(v.split(">#>").join("~~")+"~~"+r[f][u].Value)):!t.isNullOrUndefined(v)&&l>0&&l==f&&r[f][u].CSS.indexOf("summary")>-1?(nt.push(u),e.chartLables.push(v.split(">#>").join("~~")+"~~"+r[f][u].Value.split(" Total")[0])):l-1==f&&r[f][u].CSS.indexOf("summary")>-1?(et!=""&&(c[0]=c[0].split(">#>").slice(0,c[0].split(">#>").length-1).join(">#>"),c[0]==""&&(c.splice(0,1),et="")),l=l-1):(l==0||et=="")&&f==0&&r[0][u].CSS.indexOf("summary")>-1&&(t.isNullOrUndefined(v)?!0:v.split(">#>")[0]!=r[0][u].Value.split(" Total")[0])&&r[f][u].Value!="Grand Total"&&(nt.push(u),e.chartLables.push(r[f][u].Value.split(" Total")[0]))}else o>1?r[0][u].CSS=="summary rstot"&&r[0][u].Value!="Grand Total"&&e.chartLables.push(r[0][u].Value.replace(" Total","")):r[0][u].CSS=="rowheader"&&e.chartLables.push(r[0][u].Value);this.model.enableMultiLevelLabels&&(e.multiLevelLabels=n.extend(!0,[],e.chartLables))}else if(t.isNullOrUndefined(this._labelCurrentTags.collapsedMembers)&&(this._labelCurrentTags.collapsedMembers=[]),o>1){var h="",d=!1,y="";for(f=0;f<o;f++)if(r[f][u].CSS=="rowheader")h+=h==""?r[f][u].Value:(r[f][u].Info!=""&&r[f-1][u].Info!=""&&r[f][u].Info.split("::")[3]==r[f-1][u].Info.split("::")[0]?"#":"~~")+r[f][u].Value,r[f][u].Info.indexOf("[Measures]")==-1&&(y+=y==""?r[f][u].Info:(r[f][u].Info!=""&&r[f-1][u].Info!=""&&r[f][u].Info.split("::")[3]==r[f-1][u].Info.split("::")[0]?"#":"~~")+r[f][u].Info),n.inArray(r[f][u].Value,this._labelCurrentTags.collapsedMembers)<0&&r[f][u].State==2&&this._labelCurrentTags.collapsedMembers.push(r[f][u].Value),n.inArray(r[f][u].Info,e.seriesTags)<0&&r[f][u].Info.indexOf("[Measures]")==-1&&e.seriesTags.push(r[f][u].Info);else if((!this.model.enableMultiLevelLabels||h==""||r[f][u].CSS.indexOf("summary")>=0)&&(this.model.enableMultiLevelLabels?!0:r[f][u].CSS.indexOf("none")==-1&&r[f][u].CSS.indexOf("value")==-1)){d=!0;nt.push(u);break}d||(e.chartLables.push(h),e.labelTags.push(y))}else r[0][u].CSS=="rowheader"&&(e.chartLables.push(r[0][u].Value),e.labelTags.push(r[0][u].Info),e.seriesTags.push(r[0][u].Info)),r[0][u].State==2&&this._labelCurrentTags.collapsedMembers.push(r[0][u].Value);if(this.model.enableMultiLevelLabels)if(this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot){for(g=0,f=0;f<e.multiLevelLabels.length;f++)e.multiLevelLabels[f]=n.map(e.multiLevelLabels[f].split("~~"),function(n){return n+"#1"}).join("~~"),g=e.multiLevelLabels[f].split("~~").length>g?e.multiLevelLabels[f].split("~~").length:g;for(b=0;b<e.multiLevelLabels.length;b++)if(g>1&&e.multiLevelLabels[b].split("~~").length<g){at=e.multiLevelLabels[b].split("~~")[e.multiLevelLabels[b].split("~~").length-1].split("#1")[0];do e.multiLevelLabels[b]+="~~"+at+"#0";while(e.multiLevelLabels[b].split("~~").length<g)}}else for(u=a;u<ot;u++)if(o>1){var h="",y="",d=!1;for(f=0;f<o;f++)if(r[f][u].CSS=="rowheader")h+=h==""?r[f][u].Value+"#"+r[f][u].Expander:"~~"+(r[f][u].Value+"#"+r[f][u].Expander),y+=y==""?r[f][u].Info+"::"+r[f][u].State:"~~"+(r[f][u].Info+"::"+r[f][u].State);else if(r[f][u].CSS.indexOf("none")>=0&&h!="")h+="~~"+h.split("~~")[h.split("~~").length-1].split("#")[0]+"#"+r[f][u].Expander,y+="~~"+y.split("~~")[y.split("~~").length-1];else if(h==""||r[f][u].CSS.indexOf("summary")>=0||r[f][u].CSS.indexOf("value")>=0){d=r[f][u].CSS.indexOf("summary")>=0?!0:!1;break}d||h==""||(e.multiLevelLabels.push(h),e.multiLevelLabelTags.push(y))}else r[0][u].CSS=="rowheader"&&(e.multiLevelLabels.push(r[0][u].Value+"#"+r[0][u].Expander),e.multiLevelLabelTags.push(r[0][u].Info+"::"+r[0][u].State));for(u=a;u<ot;u++)if(p=[],o>1){if(this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot){if(this.model.enableMultiLevelLabels){if(n.inArray(u,nt)>-1){for(f=o;f<k;f++)n.inArray(f,rt)<0&&p.push(r[f][u].Value);e.points_Y.push(p)}}else if(r[0][u].CSS=="summary rstot"&&r[0][u].Value!="Grand Total"){for(f=o;f<k;f++)n.inArray(f,rt)<0&&p.push(r[f][u].Value);e.points_Y.push(p)}}else if(n.inArray(u,nt)<0){for(f=o;f<k;f++)n.inArray(f,rt)<0&&r[f][u].CSS.indexOf("value")>-1&&(p.push(r[f][u].Value==""?0:t.parseFloat(r[f][u].ActualValue)),t.isNullOrUndefined(r[f][u].Format)||(e.labelFormat=this.model.analysisMode==t.PivotChart.AnalysisMode.Olap?e.labelFormat.length>0?e.labelFormat==r[f][u].Format?r[f][u].Format:e.labelFormat+"_"+r[f][u].Format:r[f][u].Format:"Number"));e.points_Y.push(p)}}else if(r[0][u].CSS=="rowheader"){for(f=r[0][0].ColSpan;f<k;f++)n.inArray(f,rt)<0&&(p.push(this.model.analysisMode==t.PivotChart.AnalysisMode.Olap?r[f][u].Value==""?0:t.parseFloat(r[f][u].ActualValue):r[f][u].Value),t.isNullOrUndefined(r[f][u].Format)||(e.labelFormat=this.model.analysisMode==t.PivotChart.AnalysisMode.Olap?e.labelFormat.length>0?e.labelFormat==r[f][u].Format?r[f][u].Format:e.labelFormat+"_"+r[f][u].Format:r[f][u].Format:"Number"));e.points_Y.push(p)}}if(this.model.analysisMode==t.PivotChart.AnalysisMode.Olap)e.measureNames=ut;else for(st=this.model.enableMultiLevelLabels&&this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode?n(ct).map(function(n,t){return t.FieldHeader}):n(this.model.dataSource.values).map(function(n,t){return t.fieldCaption}),u=0;u<st.length;u++)e.measureNames+=e.measureNames==""?st[u]:"~"+st[u];if(this.model.enableMultiLevelLabels&&this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot&&this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode)this.renderControlSuccess({JsonRecords:JSON.stringify(e),OlapReport:it});else{if(!t.isNullOrUndefined(this._pivotClientObj)&&this._pivotClientObj._isExporting)return tt=n.extend(!0,{},{pivotEngine:this._pivotEngine,labelCurrentTags:this._labelCurrentTags,drillParams:this._drillParams}),this._pivotEngine=tt.pivotEngine,this._labelCurrentTags=tt.labelCurrentTags,this._drillParams=tt.drillParams,this._getChartSeries(e);this.renderControlSuccess({JsonRecords:JSON.stringify(e),OlapReport:JSON.stringify(this.model.dataSource)})}e={}}},_cloneEngine:function(t){for(var r=n.extend(!0,[],t),i=0;i<t.length;i++)r[i]=n.extend(!0,[],t[i]);return r},_cropData:function(n,i,r,u){var e=this.model.analysisMode==t.PivotChart.AnalysisMode.Olap?n[0][0].RowSpan:this.model.dataSource.columns.length+(this.model.dataSource.values.length==0||this.model.dataSource.values[0].axis=="rows"?0:1),s,o,f;if(this.model.analysisMode==t.PivotChart.AnalysisMode.Olap){s=r;do{for(o=!1,f=0;f<n[r].length;)if(n[r][f].Value==i){o=!0;break}else f++;if(o)break;else r=r==s?n[0][0].ColSpan:r,s="",r--}while(!o&&r>-1)}for(r=r==-1?0:r;e<n[r].length;)if(n[r][e].Value!=i)for(f=0;f<n.length;f++)n[f].splice(e,1);else e+=n[r][e].RowSpan;this.model.analysisMode!=t.PivotChart.AnalysisMode.Olap&&u&&n.splice(r,1)},refreshPagedPivotChart:function(n,i){if(typeof ochartWaitingPopup!="undefined"&&ochartWaitingPopup!=null&&ochartWaitingPopup.show(),n=n.indexOf("categ")!=-1?"categorical":"series",this.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.dataSource.providerName==t.olap.Providers.Mondrian)n=="categorical"?this._categCurrentPage=parseInt(i):this._seriesCurrentPage=parseInt(i),t.olap.base.getJSONData({action:"navPaging"},this.model.dataSource,this);else{var r;try{r=JSON.parse(this.getOlapReport())}catch(u){r=this.getOlapReport()}this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.paging,JSON.stringify({action:"paging",pagingInfo:n+":"+i,currentReport:r,customObject:JSON.stringify(this.model.customObject)}),this.renderControlSuccess)}},_updatePageSettings:function(t,i){var r=n.map(t,function(n){var t=n.Key;if(t=="PageSettings")return n}),u=n.map(t,function(n){var t=n.Key;if(t=="HeaderCounts")return n});r.length>0&&i._pagerObj!=null&&(i._pagerObj.element.css("opacity","1"),i._pagerObj.element.find(".e-pagerTextBox").removeAttr("disabled"),i._pagerObj._unwireEvents(),i._pagerObj._wireEvents(),i._pagerObj.initPagerProperties(JSON.parse(u[0].Value),JSON.parse(r[0].Value)))},_setTitleText:function(i,r){var s,h,c,a,v,e,o;this._xTitle=t.isNullOrUndefined(this._xTitle)?!t.isNullOrUndefined(this.model.primaryXAxis.title.text)&&!this.model.primaryXAxis.title.enable?this.model.primaryXAxis.title.text:this._xTitle:this._xTitle;this._yTitle=t.isNullOrUndefined(this._yTitle)?!t.isNullOrUndefined(this.model.primaryYAxis.title.text)&&!this.model.primaryYAxis.title.enable?this.model.primaryYAxis.title.text:this._yTitle:this._yTitle;var u="",f="",l;if(this.model.operationalMode==t.PivotChart.OperationalMode.ClientMode||this.model.analysisMode==t.Pivot.AnalysisMode.Pivot){if(s=this.model.analysisMode==t.Pivot.AnalysisMode.Pivot?JSON.parse(this.getOlapReport()).PivotRows:this.model.dataSource.rows,h=this.model.analysisMode==t.Pivot.AnalysisMode.Pivot?JSON.parse(this.getOlapReport()).PivotColumns:this.model.dataSource.columns,this.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode?(s=JSON.parse(this.getOlapReport()).PivotRows,h=JSON.parse(this.getOlapReport()).PivotColumns,l="FieldName"):(s=this.model.dataSource.rows,h=this.model.dataSource.columns,l="fieldCaption"),!t.isNullOrUndefined(s))for(e=0;e<s.length;e++)u+=(u==""?"":"~")+s[e][l];if(!t.isNullOrUndefined(h))for(o=0;o<h.length;o++)f+=(f==""?"":"~")+h[o][l];if(this.model.analysisMode==t.PivotChart.AnalysisMode.Olap)for(c=0;c<this.model.dataSource.values[0].measures.length;c++)this.model.dataSource.values[0].axis=="rows"?u+=(u==""?"":"~")+this.model.dataSource.values[0].measures[c].fieldCaption:f+=(f==""?"":"~")+this.model.dataSource.values[0].measures[c].fieldCaption}else{for(a=i.split("><")[0].split("||"),v=i.split("><")[1].split("||"),e=0;e<a.length;e++)u+=(u==""?"":"~")+r.element.find(".e-cubeTreeView").find("li[data-tag='"+a[e]+"'] a:eq(0)").text();for(o=0;o<v.length;o++)f+=(f==""?"":"~")+r.element.find(".e-cubeTreeView").find("li[data-tag='"+v[o]+"'] a:eq(0)").text()}this.model.primaryXAxis.title=this.model.primaryXAxis.title.enable?n.extend({},{enable:!0,enableTrim:!0,text:u}):n.extend({},{enable:!1,enableTrim:!0,text:u});this.model.primaryYAxis.title=this.model.primaryYAxis.title.enable?n.extend({},{enable:!0,enableTrim:!0,text:f}):n.extend({},{enable:!1,enableTrim:!0,text:f})},renderControlSuccess:function(r){var u,y,c,h,o,s,l,a,p,w,e,f,v;t.isNullOrUndefined(this._pivotClientObj)||this.model.operationalMode!=t.PivotChart.OperationalMode.ServerMode||t.Pivot._updateValueSortingIndex(r,this._pivotClientObj);typeof oclientWaitingPopup!="undefined"&&oclientWaitingPopup!=null&&(n("#"+this._pivotClientObj._id+"_maxView")[0]?n("#"+this._pivotClientObj._id+"_maxView").ejWaitingPopup({showOnInit:!0}):oclientWaitingPopup.show());try{if(r[0]!=i){if(this.setJSONRecords(r[0].Value),this.setOlapReport(r[1].Value),t.isNullOrUndefined(r[2])||(u=r[2].Key,u=="AnalysisMode"&&r[2].Value=="Olap"&&(this.model.analysisMode=t.PivotChart.AnalysisMode.Olap)),!t.isNullOrUndefined(this._pivotClientObj)){if(this.model.analysisMode==t.Pivot.AnalysisMode.Pivot)this._pivotClientObj.currentReport=JSON.parse(this.getOlapReport()).Report;else try{this._pivotClientObj.currentReport=JSON.parse(r[1].Value).Report}catch(b){this._pivotClientObj.currentReport=r[1].Value}r[2]!=null&&r[2]!=i&&(u=r[2].Key,u=="ClientReports"&&(this._pivotClientObj.reports=r[2].Value));r[3]&&(u=r[3].Key,u=="Title"&&r.d[3].Value!=""&&this._setTitleText(r[3].Value,this._pivotClientObj));this._updatePageSettings(r,this._pivotClientObj)}r[2]!=null&&r[2]!=i&&(u=r[2].Key,!u=="ClientReports"&&u!="AnalysisMode"&&(this.model.customObject=r[2].Value));n(".e-pivotpager")[0]!=null&&n(".e-pivotpager")[0]!=i&&this._pagerObj!=null&&r[2]!=null&&r.d[2]!=i&&this._pagerObj.initPagerProperties(JSON.parse(r.d[3].Value),JSON.parse(r.d[2].Value))}else if(r.d!=i){if(this.setJSONRecords(r.d[0].Value),this.setOlapReport(r.d[1].Value),r.d[2].Key=="AnalysisMode"&&r.d[2].Value=="Olap"&&(this.model.analysisMode=t.PivotChart.AnalysisMode.Olap),!t.isNullOrUndefined(this._pivotClientObj)){if(this.model.analysisMode==t.Pivot.AnalysisMode.Pivot)this._pivotClientObj.currentReport=JSON.parse(this.getOlapReport()).Report;else try{this._pivotClientObj.currentReport=JSON.parse(r.d[1].Value).Report}catch(b){this._pivotClientObj.currentReport=r.d[1].Value}r.d[2]!=null&&r.d[2]!=i&&(u=r.d[2].Key,u=="ClientReports"&&(this._pivotClientObj.reports=r.d[2].Value));r.d[3]&&(u=r.d[3].Key,u=="Title"&&r.d[3].Value!=""&&this._setTitleText(r.d[3].Value,this._pivotClientObj));this._updatePageSettings(r.d,this._pivotClientObj)}r.d[2]!=null&&r.d[2]!=i&&(u=r.d[2].Key,u=="ClientReports"||(this.model.customObject=r.d[2].Value));this._pagerObj!=null&&n(".e-pivotpager")[0]!=null&&n(".e-pivotpager")[0]!=i&&(r.d[2]!=null&&r.d[2]!=i&&(u=r.d[2].Key,u=="PageSettings"&&this._pagerObj.initPagerProperties(JSON.parse(r.d[3].Value),JSON.parse(r.d[2].Value))),r.d[3]!=null&&r.d[3]!=i&&(u=r.d[3].Key,u=="PageSettings"&&this._pagerObj.initPagerProperties(JSON.parse(r.d[4].Value),JSON.parse(r.d[3].Value))))}else if(this.setJSONRecords(r.JsonRecords),this.setOlapReport(r.OlapReport),r.AnalysisMode=="Olap"&&(this.model.analysisMode=t.PivotChart.AnalysisMode.Olap),r.customObject!=null&&r.customObject!=null&&(this.model.customObject=r.customObject),!t.isNullOrUndefined(this._pivotClientObj)){if(this.model.analysisMode==t.Pivot.AnalysisMode.Pivot)this._pivotClientObj.currentReport=JSON.parse(this.getOlapReport()).Report;else try{this._pivotClientObj.currentReport=JSON.parse(r.OlapReport).Report}catch(b){this._pivotClientObj.currentReport=r.OlapReport}typeof this._pivotClientObj.reports!="undefined"&&r.reports!=i&&r.reports!="undefined"&&(this._pivotClientObj.reports=r.reports);r&&r.Title&&this._setTitleText(r.Title,this._pivotClientObj)}if(this.model.afterServiceInvoke!=null&&this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode&&(y=this._drillAction!=""?{action:this._drillAction,element:this.element,customObject:this.model.customObject}:{action:this._currentAction,element:this.element,customObject:this.model.customObject},this._trigger("afterServiceInvoke",y)),o=this.model.size.height,s=this.model.size.width,c=t.isNullOrUndefined(o)?this.element.height()>0?this.element.height():450:typeof o=="string"&&o.indexOf("%")!=-1?this.element.height()/100*parseInt(o):o,h=t.isNullOrUndefined(s)?this.element.width()>0?this.element.width():600:typeof s=="string"&&s.indexOf("%")!=-1?this.element.width()/100*parseInt(s):s,n(this.element).parents(".e-pivotclient").length>0||(this.model.size.height=c+"px",this.model.size.width=h+"px",this.model.isResponsive||this.element.width(h)),l=t.buildTag("div#"+this._id+"Container","",{height:t.isNullOrUndefined(this._pivotClientObj)?c:this._pivotClientObj._chartHeight,width:t.isNullOrUndefined(this._pivotClientObj)?h:this._pivotClientObj.model.enableSplitter?this._pivotClientObj.element.find(".controlPanelTD").width():this._pivotClientObj._toggleExpand?this._pivotClientObj.element.find(".controlPanelTD").width()-15:n("#"+this._pivotClientObj._id+"_maxView").length>0&&this._pivotClientObj._pivotChart?n("#"+this._pivotClientObj._pivotChart._id+"Container").width():this._pivotClientObj._chartWidth})[0].outerHTML,n(this.element).parents(".e-pivotclient").length>0&&this._pivotClientObj.model.enableToolBar&&!this._pivotClientObj.model.displaySettings.enableFullScreen?(a=this._pivotClientObj.model.enableSplitter?n("<div id="+this._id+"_toolBar style='width:"+this._pivotClientObj._chartWidth+";height:"+this._pivotClientObj._chartHeight+";margin-top:35px;margin-left:5px'><\/div>"):n("<div id="+this._id+"_toolBar style='width:"+this._pivotClientObj._chartWidth+";height:"+this._pivotClientObj._chartHeight+";overflow:auto;margin-top:35px;margin-left:5px'><\/div>"),n(a).html(l),this.element.html(a)):this.element.html(l),this.model.commonSeriesOptions.type==t.PivotChart.ChartTypes.Funnel||this.model.commonSeriesOptions.type==t.PivotChart.ChartTypes.Pyramid?(this.model.legend.toggleSeriesVisibility=!1,this.model.commonSeriesOptions.marker={dataLabel:{visible:!0,shape:"none",font:{color:this.element.css("color"),size:"12px",fontWeight:"lighter"}}}):this.model.legend.toggleSeriesVisibility=!0,this.model.operationalMode!=t.PivotChart.OperationalMode.ClientMode&&this.model.analysisMode!=t.Pivot.AnalysisMode.Pivot||t.isNullOrUndefined(this._pivotClientObj)||this._setTitleText("Title",this._pivotClientObj),this.renderChartFromJSON(this.getJSONRecords()),n(this.element).parents(".e-pivotclient").length>0&&this._pivotClientObj.model.enableToolBar&&!this._pivotClientObj.model.displaySettings.enableFullScreen&&(n("#"+this._id).prepend("<div class='e-chartToolBar' style='height:40px;width:"+this._pivotClientObj.element.find(".chartContainer").width()+"px'><\/div>"),p=t.buildTag("ul",t.buildTag("li.e-chart3DImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("TDCharts")).attr({title:this._getLocalizedLabels("TDCharts"),tabindex:0})[0].outerHTML+t.buildTag("li.e-exportImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("Exporting")).attr({title:this._getLocalizedLabels("Exporting"),tabindex:0})[0].outerHTML+(this.model.enable3D?"":t.buildTag("li.e-smartLabels e-icon","",{}).attr("aria-label",this._getLocalizedLabels("SmartLabels")).attr({title:this._getLocalizedLabels("SmartLabels"),tabindex:0})[0].outerHTML)+(this.model.enable3D?"":t.buildTag("li.e-interaction e-icon","",{}).attr("aria-label",this._getLocalizedLabels("Interactions")).attr({title:this._getLocalizedLabels("Interactions"),tabindex:0})[0].outerHTML))[0].outerHTML+t.buildTag("ul",t.buildTag("li#"+this._id+"_toolTipImg.e-toolTipImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("Tooltip")).attr({title:this._getLocalizedLabels("Tooltip"),tabindex:0})[0].outerHTML+t.buildTag("li.e-legend e-icon","",{}).attr("aria-label",this._getLocalizedLabels("Legend")).attr({title:this._getLocalizedLabels("Legend"),tabindex:0})[0].outerHTML+(this.model.enable3D?"":t.buildTag("li.e-zooming e-icon","",{}).attr("aria-label",this._getLocalizedLabels("Zooming")).attr({title:this._getLocalizedLabels("Zooming"),tabindex:0})[0].outerHTML)+t.buildTag("li.e-icon-xAxis-title e-icon","",{}).attr("aria-label","X-Axis title").attr({title:"X-Axis title",tabindex:0})[0].outerHTML+t.buildTag("li.e-icon-yAxis-title e-icon","",{}).attr("aria-label","Y-Axis title").attr({title:"Y-Axis title",tabindex:0})[0].outerHTML)[0].outerHTML,n(".e-chartToolBar").append(n(p)),n(".e-chartToolBar").ejToolbar({enableRTL:this.model.enableRTL,enableSeparator:!0,height:"35px"}),this.model.enable3D&&this.element.find(".e-chart3DImg").addClass("e-enabled"),this.model.zooming.enable&&this.element.find(".e-zooming").addClass("e-enabled"),this.model.legend.visible&&this.element.find(".e-legend").addClass("e-enabled"),t.isNullOrUndefined(this.model.primaryXAxis.labelIntersectAction)||this.element.find(".e-smartLabels").addClass("e-enabled"),this.model.primaryXAxis.title.enable&&this.element.find(".e-icon-xAxis-title").addClass("e-enabled"),this.model.primaryYAxis.title.enable&&this.element.find(".e-icon-yAxis-title").addClass("e-enabled"),this.model.commonSeriesOptions.tooltip.visible&&this.element.find(".e-toolTipImg").addClass("e-enabled"),this.model.crosshair.visible&&this.element.find(".e-interaction").addClass("e-enabled"),this.model.crosshair.type=="trackball"&&this.element.find(".e-trackBall").addClass("e-enabled"),this.model.crosshair.type=="crosshair"&&this.element.find(".e-crossHair").addClass("e-enabled"),this.model.crosshair.visible||this.element.find(".none").addClass("e-enabled"),this._pivotClientObj._unWireEvents(),this._pivotClientObj._wireEvents()),this._unWireEvents(),this._wireEvents(),this._drillAction!=""){if(this.model.currentReport=this.getOlapReport(),this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot&&!this.model.enableMultiLevelLabels)if(this.model.operationalMode==t.PivotChart.OperationalMode.ClientMode){if(e=this.getJSONRecords(),this.model.dataSource.rows.length-this._labelCurrentTags.expandedMembers.length>1)for(this._labelCurrentTags.collapsedMembers=[],f=0;f<e.chartLables.length;f++)this._labelCurrentTags.collapsedMembers.push(e.chartLables[f])}else if(e=this.getJSONRecords(),w=t.isNullOrUndefined(JSON.parse(this.getOlapReport()).rows)?JSON.parse(this.getOlapReport()).PivotRows:JSON.parse(JSON.parse(this.getOlapReport()).rows),this._labelCurrentTags.collapsedMembers=[],w.length-this._labelCurrentTags.expandedMembers.length>1)for(f=0;f<e.chartLables.length;f++)this._labelCurrentTags.collapsedMembers.push(e.chartLables[f]);t.isNullOrUndefined(this._pivotClientObj)||this._pivotClientObj._trigger("chartDrillSuccess",this.element);this._trigger("drillSuccess",this.element)}else if(this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot)if(this.model.operationalMode==t.PivotChart.OperationalMode.ClientMode){if(e=this.getJSONRecords(),this._labelCurrentTags={collapsedMembers:[]},this.model.dataSource.rows.length>1)for(f=0;f<e.chartLables.length;f++)this._labelCurrentTags.collapsedMembers.push(e.chartLables[f])}else if(e=this.getJSONRecords(),this.model.currentReport=JSON.parse(this.getOlapReport()),this._labelCurrentTags={collapsedMembers:[]},n(this.element).parents(".e-pivotclient").length>0?this.model.currentReport.PivotRows.length>1:JSON.parse(this.model.currentReport.rows).length>1)for(f=0;f<e.chartLables.length;f++)this._labelCurrentTags.collapsedMembers.push(e.chartLables[f]);v=n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejPivotClient"):null;t.isNullOrUndefined(v)||(v._isTimeOut=!1);t.isNullOrUndefined(this._pivotClientObj)||this._pivotClientObj._waitingPopup==null?this._waitingPopup.hide():(n("#"+this._pivotClientObj._id+"_maxView")[0]?(n("#"+this._pivotClientObj._id+"_maxView").ejWaitingPopup({showOnInit:!1}),this._pivotClientObj._waitingPopup.hide()):t.isNullOrUndefined(this._pivotClientObj._pivotGrid)||(this._pivotClientObj&&this._pivotClientObj._pivotGrid._drillAction&&!this._pivotClientObj._pivotGrid._startDrilldown||this._pivotClientObj._pivotChart._drillAction&&!this._pivotClientObj._pivotChart._startDrilldown?this._pivotClientObj._waitingPopup.hide():!this._pivotClientObj||this._pivotClientObj._pivotGrid._drillAction!=""||this._pivotClientObj._pivotChart._drillAction!=""||this._pivotClientObj._pivotGrid._startDrilldown||this._pivotClientObj._pivotChart._startDrilldown||this._pivotClientObj._pivotGrid._JSONRecords==null&&this._pivotClientObj._pivotChart._JSONRecords!=null?!this._pivotClientObj._pivotChart._startDrilldown||this._pivotClientObj._pivotGrid._startDrilldown||n("#"+this._pivotClientObj._id+"_maxView")[0]?this._pivotClientObj._pivotChart._startDrilldown||this._pivotClientObj._pivotGrid._startDrilldown||this._pivotClientObj._pivotChart._drillAction!=""||this._pivotClientObj._pivotGrid._drillAction!=""||this._pivotClientObj._pivotGrid._JSONRecords==null&&this._pivotClientObj._pivotChart._JSONRecords!=null||this._pivotClientObj._waitingPopup.hide():this._pivotClientObj._waitingPopup.show():this._pivotClientObj._waitingPopup.hide()),this._pivotClientObj.model.displaySettings.mode=="chartonly"&&this._pivotClientObj._waitingPopup.hide());t.isNullOrUndefined(this._pivotClientObj)||(this._pivotClientObj._pivotChart._startDrilldown=!1)}catch(b){}t.isNullOrUndefined(r.Exception)||t.Pivot._createErrorDialog(r,"Exception",this);this._trigger("renderSuccess",this)},_getChartSeries:function(r){var f=this,c=[],e,y,l;this.model.enableRTL&&(r.chartLables=n.map(r.chartLables,function(n){return n.split("~~").map(function(n){return n.split("#").reverse().join("#")}).reverse().join("~~")}),r.seriesNames=n.map(r.seriesNames,function(n){return n.split("~~").map(function(n){return n.split("#").reverse().join("#")}).reverse().join("~~")}));e=[];e=f.model.analysisMode==t.PivotChart.AnalysisMode.Olap?n.map(n.extend([],r.chartLables,!0),function(n){return n.split("#").join("~~")}):n.map(n.extend([],r.chartLables,!0),function(n){return!t.isNullOrUndefined(f._labelCurrentTags.expandedMembers)&&f._labelCurrentTags.expandedMembers.length>0?f._labelCurrentTags.expandedMembers.join("~~")+"~~"+n:n});var a=[],w=e,h=[],s=0,p=0,u,o=0,v=this.model.series[0].trendlines[0];if(o=r.measureInfo!=""?parseInt(r.measureInfo):0,jQuery.each(e,function(n){var l=[],c,h,s;if(o>1&&r.addInfo.action!="DrillChart"||o>1&&r.addInfo.levelHash=="1"){for(c=(n+1)*o-o,h=0;h<o;h++)if(r.points_Y[c+h]!=i&&e[n].split("~~")[0]==r.points_Y[c+h][0].Item1)for(s=0;s<r.points_Y[n].length;s++)u={xValues:e[n],yValues:t.isNullOrUndefined(r.points_Y[c+h][s].Item2)?0:r.points_Y[c+h][s].Item2.indexOf(",")>-1?t.globalize.parseFloat(t.globalize.format(r.points_Y[c+h][s].Item2,"c",f.locale()),f.locale()):r.points_Y[c+h][s].Item2==""?0:t.globalize.parseFloat(r.points_Y[c+h][s].Item2,f.locale()),text:r.points_Y[c+h][s].Item2==""?0:r.points_Y[c+h][s].Item2},u.yValues=t.isNullOrUndefined(u.yValues)?u.yValues:jQuery.isNumeric(u.yValues)?u.yValues:t.globalize.parseFloat(r.points_Y[c+h][s].Item2.toString().replace(/[^\d.,-]/g,""),f.locale()),l.push(u)}else for(s=0;s<r.points_Y[n].length;s++)f.model.analysisMode==t.PivotChart.AnalysisMode.Olap&&f.model.operationalMode==t.PivotChart.OperationalMode.ServerMode?(u=f.seriesType()=="bubble"?{xValues:e[n],yValues:t.isNullOrUndefined(r.points_Y[n][s].Item2)||r.points_Y[n][s].Item2==""?0:r.points_Y[n][s].Item2,size:r.points_Y[n][s].Item2==""?0:r.points_Y[n][s].Item2}:{xValues:e[n],yValues:t.isNullOrUndefined(r.points_Y[n][s].Item2)||r.points_Y[n][s].Item2==""?0:t.globalize.parseFloat(r.points_Y[n][s].Item2,f.locale()),text:t.isNullOrUndefined(r.points_Y[n][s].Item2)||r.points_Y[n][s].Item2==""?"0":r.points_Y[n][s].Item2.toString()},u.yValues=t.isNullOrUndefined(u.yValues)?u.yValues:jQuery.isNumeric(u.yValues)?u.yValues:t.globalize.parseFloat(r.points_Y[n][s].Item2.toString().replace(/[^\d.,-]/g,""),f.locale())):(u=f.seriesType()=="bubble"?{xValues:e[n].toString().split("#")[e[n].toString().split("#").length-1],yValues:t.isNullOrUndefined(r.points_Y[n][s])||r.points_Y[n][s]==""?0:r.points_Y[n][s],size:r.points_Y[n][s]==""?0:r.points_Y[n][s]}:{xValues:e[n].toString().split("#")[e[n].toString().split("#").length-1],yValues:t.isNullOrUndefined(r.points_Y[n][s])||r.points_Y[n][s]==""?0:r.points_Y[n][s],text:t.isNullOrUndefined(r.points_Y[n][s])||r.points_Y[n][s]==""?"0":r.points_Y[n][s].toString()},u.yValues=t.isNullOrUndefined(u.yValues)?u.yValues:jQuery.isNumeric(u.yValues)?u.yValues:t.globalize.parseFloat(r.points_Y[n][s].toString().replace(/[^\d.,-]/g,""),f.locale())),l.push(u);a.push(l)}),o>1&&r.addInfo.action!="DrillChart"||o>1&&r.addInfo.levelHash=="1")for(y=0;y<o*r.seriesNames.length;y++)h.push([]);else jQuery.each(r.seriesNames,function(){h.push([])});if(jQuery.each(e,function(n){for(var i=0;i<a[n].length;i++)t.isNullOrUndefined(h[i])&&(h[i]=[]),h[i].push(a[n][i])}),o>1&&r.addInfo.action!="DrillChart"||o>1&&r.addInfo.levelHash=="1")for(l=0;l<o*r.seriesNames.length;l++)c[p]={dataSource:h[l],xName:"xValues",yName:"yValues",name:r.seriesNames[s],trendlines:[v]},s++,p++,s==r.seriesNames.length&&(s=0);else this.seriesType()=="bubble"?jQuery.each(r.seriesNames,function(n){c[s]={dataSource:h[n],xName:"xValues",yName:"yValues",size:"size",name:r.seriesNames[s],trendlines:[v]};s++}):jQuery.each(r.seriesNames,function(n){c[s]={dataSource:h[n],xName:"xValues",yName:"yValues",name:r.seriesNames[s],marker:{dataLabel:{textMappingName:"text"}},trendlines:[v]};s++});return c},renderChartFromJSON:function(i){var s,at,it,rt,nt,w,f,v,ut,e,y,ft,b,p,k,tt,d,c,o,et,ot,r,u,st,h,l,g,vt,yt,a,lt;if(!this.model.enableDefaultValue&&!t.isNullOrUndefined(i)&&this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&i.measureNames==""&&!t.isNullOrUndefined(i.points_Y)&&i.points_Y.length>0&&(i.points_Y=n.map(i.points_Y,function(i){return n.map(i,function(n){return t.isNullOrUndefined(n.Item2)?n=0:n.Item2="",n})})),this.setJSONRecords(JSON.stringify(i)),s=[],i==null||i=="")i==""&&(at=t.buildTag("div#"+this._id+"Container")[0].outerHTML,this.element.html(at)),s=[],this.model.primaryXAxis.multiLevelLabels=[];else if(i!=null&&i.chartLables.length>0&&i.points_Y.length>0){if(s=this._getChartSeries(i),it={series:n.extend(!0,[],s),axes:this.model.axes},this.model.axes.length<=s.length)for(r=0;r<this.model.axes.length;r++)if(this.model.axes[r].name.indexOf(":")>-1)for(rt=this.model.axes[r].name.split(":").length>0?this.model.axes[r].name.split(":")[0]:"y",nt=this.model.axes[r].name.split(":")[1].split(","),w=0;w<nt.length;w++)t.isNullOrUndefined(rt)||(rt.toLowerCase().indexOf("x")==-1?s[parseInt(nt[w])].yAxisName=this.model.axes[r].name:s[parseInt(nt[w])].xAxisName=this.model.axes[r].name);if(this._trigger("beforeSeriesRender",it),t.isNullOrUndefined(this.model.beforeSeriesRender)||(s=it.series),this.model.enableMultiLevelLabels&&!t.isNullOrUndefined(i.multiLevelLabels)){for(this.model.primaryXAxis.majorTickLines={size:0},f=[],v=[],r=0;r<i.multiLevelLabels.length;r++)v.push(i.multiLevelLabels[r].split("~~"));for(r=i.multiLevelLabels[0].split("~~").length-1;r>=0;r--){for(ut=[],u=0;u<i.multiLevelLabels.length;u++){if(e={},e.name=i.multiLevelLabels[u].split("~~")[r].split("#")[0],this.model.analysisMode==t.PivotChart.AnalysisMode.Olap&&this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode)e.rowSpan=parseInt(i.multiLevelLabels[u].split("~~")[r].split("#")[1]),e.colSpan=parseInt(i.multiLevelLabels[u].split("~~")[r].split("#")[2]),e.tag=i.multiLevelLabelTags[u].split("~~")[r];else{if(r==i.multiLevelLabels[0].split("~~").length-1)e.colSpan=1;else{for(y="",ft=0,b=u;b<v.length;b++){for(p="",k=r;k>=0;k--)p+=p==""?v[b][k]:"$"+v[b][k];if(ft+=y==""||y==p?1:0,y!=p&&y!="")break;else y=p}e.colSpan=ft}if(parseInt(i.multiLevelLabels[u].split("~~")[r].split("#")[1])==0){for(c=0,tt=r;tt>=0;tt--)if(c++,parseInt(v[u][tt].split("#")[1])>0)break;e.rowSpan=c}else e.rowSpan=1;e.tag=t.isNullOrUndefined(i.multiLevelLabelTags[u])?"":i.multiLevelLabelTags[u].split("~~")[r]}ut.push(e)}f.push(ut)}if(d=[],this.model.analysisMode==t.PivotChart.AnalysisMode.Olap&&this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode){for(u=0;u<f[0].length;u++)for(r=f.length-1;r>=0;r--)if(!t.isNullOrUndefined(f[r][u])&&f[r][u].rowSpan>1&&d.indexOf(r+"-"+u)==-1){c=f[r][u].rowSpan;o=1;do delete f[r-o][u],o++;while(c>o);d.push(r+"-"+u)}}else for(u=0;u<f[0].length;u++)for(r=0;r<f.length;r++)if(!t.isNullOrUndefined(f[r][u])&&f[r][u].rowSpan>1&&d.indexOf(r+"-"+u)==-1){c=f[r][u].rowSpan;o=0;do delete f[r+o][u],o++;while(c>o+1);f[r+o][u].rowSpan=c;d.push(r+o+"-"+u)}for(et=[],ot=this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot&&this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode?t.isNullOrUndefined(JSON.parse(this.getOlapReport()).PivotRows)?JSON.parse(JSON.parse(this.getOlapReport()).rows).length:JSON.parse(this.getOlapReport()).PivotRows.length:this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot&&this.model.operationalMode==t.PivotChart.OperationalMode.ClientMode?this.model.dataSource.rows.length:0,r=0;r<f.length;r++)for(u=0;u<f[r].length;u++)t.isNullOrUndefined(f[r][u])||(st=u-.5,h={},h.visible=!0,h.text=this.model.analysisMode==t.PivotChart.AnalysisMode.Olap?f[r][u].tag.endsWith("2")?"+ "+f[r][u].name:f[r][u].tag.endsWith("1")?"- "+f[r][u].name:f[r][u].name:ot==f.length&&r==0?f[r][u].name:r==0||f[r][u].rowSpan>1?"+ "+f[r][u].name:"- "+f[r][u].name,h.tag=this.model.analysisMode==t.PivotChart.AnalysisMode.Olap?f[r][u].tag:ot==f.length&&r==0?f[r][u].name+"#0":r==0||f[r][u].rowSpan>1?f[r][u].name+"#1":f[r][u].name+"#2",h.start=st,h.end=st+f[r][u].colSpan,h.level=r,h.span=f[r][u].rowSpan>1?!0:!1,et.push(h),u+=f[r][u].colSpan-1);this.model.primaryXAxis.multiLevelLabels=et;this.model.zooming.enableScrollbar=!0}else i.chartLables.length>10&&this.model.zooming==""?this.model.zooming={enable:!0,type:"x,y",enableMouseWheel:!0}:i.chartLables.length<10&&this._initZooming==""&&(this.model.zooming=this._initZooming);if(this.model.crosshair.visible?this.model.commonSeriesOptions.tooltip.format="#point.x# : #point.y#":this.model.enableRTL?this.model.commonSeriesOptions.tooltip={visible:!0,template:"toolTipTemplate"}:this.model.commonSeriesOptions.tooltip.format=this._getLocalizedLabels("Measure")+" : "+i.measureNames+" <br/>"+this._getLocalizedLabels("Column")+" : #series.name# <br/>"+this._getLocalizedLabels("Row")+" : #point.x# <br/>"+this._getLocalizedLabels("Value")+" : #point.y#",this.model.analysisMode==t.PivotChart.AnalysisMode.Olap?this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode?(l="",(this.model.primaryYAxis.labelFormat!=null||this.model.primaryYAxis.labelFormat!="")&&this.model.primaryYAxis.labelFormat?l=this.model.primaryYAxis.labelFormat:i.labelFormat.indexOf("#")>-1&&i.measureNames.indexOf("~")<0&&!(i.labelFormat.indexOf("$")>-1)?i.labelFormat=="#,#"?l="Number":i.labelFormat.indexOf("#,#")>-1&&i.labelFormat.split("#")[i.labelFormat.split("#").length-1].indexOf(")")>-1?i.labelFormat.indexOf(";")>-1?(g=i.labelFormat.split(";")[0],l="n"+(g.split("#")[g.split("#").length-1]=="0"?0:g.split("#")[g.split("#").length-1].split(".")[1].split("0").length-1)):l="":l=n.isNumeric(i.labelFormat.split("#")[i.labelFormat.split("#").length-1])?"n"+(i.labelFormat.split("#")[i.labelFormat.split("#").length-1]=="0"?0:i.labelFormat.split("#")[i.labelFormat.split("#").length-1].split(".")[1].split("0").length-1):i.labelFormat.split("#,").length>1?"n"+(i.labelFormat.split(".").length==1?0:i.labelFormat.split(".")[1].split("0").length-1):"{value}"+i.labelFormat.split("##")[i.labelFormat.split("##").length-1]:l=i.labelFormat.toLowerCase().indexOf("percent")>-1&&i.measureNames.indexOf("~")<0?"{value}%":(i.labelFormat.toLowerCase().indexOf("currency")>-1||i.labelFormat.indexOf("$")>-1)&&i.measureNames.indexOf("~")<0?"Currency":"",this.model.primaryYAxis.labelFormat=l):this.model.dataSource.values[0].measures.length==1&&(t.isNullOrUndefined(this.model.dataSource.values[0].measures[0].format)||(this.model.primaryYAxis.labelFormat=(this.model.primaryYAxis.labelFormat!=null||this.model.primaryYAxis.labelFormat!="")&&this.model.primaryYAxis.labelFormat?this.model.primaryYAxis.labelFormat:this.model.dataSource.values[0].measures[0].format.toLowerCase()=="decimal"?"n2":this.model.dataSource.values[0].measures[0].format.toLowerCase()=="number"?"n":this.model.dataSource.values[0].measures[0].format.toLowerCase()=="percent"?"p2":this.model.dataSource.values[0].measures[0].format.toLowerCase()=="currency"?"c":this.model.dataSource.values[0].measures[0].format.toLowerCase()=="date"?"MM/dd/yyyy":this.model.dataSource.values[0].measures[0].format.toLowerCase()=="time"?"hh:mm:ss":"")):this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot&&(this.model.operationalMode==t.PivotChart.OperationalMode.ClientMode?this.model.dataSource.values.length==1&&(t.isNullOrUndefined(this.model.dataSource.values[0].format)||(this.model.primaryYAxis.labelFormat=(this.model.primaryYAxis.labelFormat!=null||this.model.primaryYAxis.labelFormat!="")&&this.model.primaryYAxis.labelFormat?this.model.primaryYAxis.labelFormat:this.model.dataSource.values[0].format.toLowerCase()=="decimal"?"n2":this.model.dataSource.values[0].format.toLowerCase()=="number"?"n":this.model.dataSource.values[0].format.toLowerCase()=="percentage"?"p2":this.model.dataSource.values[0].format.toLowerCase()=="currency"?"c":this.model.dataSource.values[0].format.toLowerCase()=="date"?t.isNullOrUndefined(this.model.dataSource.values[0].formatString)?"dd/MMMM/yyyy":this.model.dataSource.values[0].formatString:this.model.dataSource.values[0].format.toLowerCase()=="time"?"hh:mm:ss":"")):(vt=t.isNullOrUndefined(JSON.parse(this.getOlapReport()).PivotCalculations)?JSON.parse(JSON.parse(this.getOlapReport()).values):JSON.parse(this.getOlapReport()).PivotCalculations,i.labelFormat.length==1&&vt.length==1&&(this.model.primaryYAxis.labelFormat=(this.model.primaryYAxis.labelFormat!=null||this.model.primaryYAxis.labelFormat!="")&&this.model.primaryYAxis.labelFormat?this.model.primaryYAxis.labelFormat:i.labelFormat[0].toLowerCase()=="#.##"?"n2":i.labelFormat[0].toLowerCase()=="###"?"n":i.labelFormat[0].toLowerCase()=="p"?"p2":i.labelFormat[0].toLowerCase()=="c"?"c":""))),this.model.zooming.enableScrollbar){t.isNullOrUndefined(this.model.size.width)&&(this.model.size.width="800px");t.isNullOrUndefined(this.model.size.height)&&(this.model.size.height="400px");var pt=this.model.size.width.indexOf("%")>-1?this.element.width()*this.model.size.width.split("%")[0]/100:this.model.size.width.split("px")[0],wt=this.model.size.height.indexOf("%")>-1?this.element.height()*this.model.size.height.split("%")[0]/100:this.model.size.height.split("px")[0],bt=this.seriesType()=="bar"||this.seriesType()=="stackingbar"?wt/100*6:pt/100*3;this.model.primaryXAxis.zoomFactor=bt/(i.chartLables.length*i.seriesNames.length)}else this.model.primaryXAxis.zoomFactor=1;this.model.primaryXAxis.labelRotation=t.isNullOrUndefined(this.model.primaryXAxis.labelRotation)?270:this.model.primaryXAxis.labelRotation}else this.model.primaryXAxis.multiLevelLabels=[];var kt=this,ht=jQuery.extend(!0,{},this.model.primaryXAxis),ct=jQuery.extend(!0,{},this.model.primaryYAxis);t.isNullOrUndefined(this._pivotClientObj)||(t.isNullOrUndefined(this._xTitle)?this.model.primaryXAxis.title.enable||(ht.title.text=""):ht.title.text=this._xTitle);t.isNullOrUndefined(this._pivotClientObj)||(t.isNullOrUndefined(this._yTitle)?this.model.primaryYAxis.title.enable||(ct.title.text=""):ct.title.text=this._yTitle);n("#"+this._id+"Container").ejChart({axes:this.model.axes,border:this.model.border,backGroundImageUrl:this.model.backGroundImageUrl,palette:this.model.palette,chartArea:this.model.chartArea,primaryXAxis:ht,primaryYAxis:ct,secondaryX:this.model.secondaryX,secondaryY:this.model.secondaryY,striplineDefault:this.model.striplineDefault,title:{text:this.titleText(),textAlignment:this.model.title.textAlignment,font:{color:this.model.title.color,fontFamily:this.model.title.fontFamily,fontWeight:this.model.title.fontWeight,opacity:this.model.title.opacity,size:this.model.title.size,fontStyle:this.model.title.fontStyle}},locale:this.locale(),lineCap:this.model.lineCap,lineJoin:this.model.lineJoin,legendAlignment:this.model.legendAlignment,legendPosition:this.model.legendPosition,legend:this.model.legend,animation:this.model.animation,crosshair:this.model.crosshair,commonSeriesOptions:{doughnutCoefficient:this.model.commonSeriesOptions.doughnutCoefficient,explodeOffset:this.model.commonSeriesOptions.explodeOffset,pyramidMode:this.model.commonSeriesOptions.pyramidMode,gapRatio:this.model.commonSeriesOptions.gapRatio,pieCoefficient:this.model.commonSeriesOptions.pieCoefficient,doughnutSize:this.model.commonSeriesOptions.doughnutSize,startAngle:this.model.commonSeriesOptions.startAngle,xAxisName:this.model.commonSeriesOptions.xAxisName,yAxisName:this.model.commonSeriesOptions.yAxisName,explodeAll:this.model.commonSeriesOptions.explodeAll,explodeIndex:this.model.commonSeriesOptions.explodeIndex,tooltipOptions:this.model.commonSeriesOptions.tooltipOptions,marker:this.model.commonSeriesOptions.marker||this.model.marker,font:this.model.commonSeriesOptions.font,type:this.seriesType(),enableAnimation:this.model.commonSeriesOptions.enableAnimation,style:this.model.commonSeriesOptions.style,explode:this.model.commonSeriesOptions.explode,labelPosition:this.model.commonSeriesOptions.labelPosition,tooltip:this.model.commonSeriesOptions.tooltip,zOrder:this.model.commonSeriesOptions.zOrder,drawType:this.model.commonSeriesOptions.drawType,isStacking:this.model.commonSeriesOptions.isStacking,enableSmartLabels:this.model.commonSeriesOptions.enableSmartLabels},seriesStyle:this.model.seriesStyle,pointStyle:this.model.pointStyle,textStyle:this.model.textStyle,initSeriesRender:this.model.initSeriesRender,theme:this.model.theme,canResize:this.model.isResponsive,rotation:this.model.rotation,enable3D:this.model.enable3D,zooming:this.model.zooming,margin:this.model.margin,elementSpacing:this.model.elementSpacing,seriesColors:this.model.seriesColors,seriesBorderColors:this.model.seriesBorderColors,pointColors:this.model.pointColors,pointBorderColors:this.model.pointBorderColors,series:s,size:this.model.size,load:this.model.enableMultiLevelLabels?this._onChartLoad:this.model.load,loaded:this.model.enableMultiLevelLabels?t.proxy(this._onChartLoaded,this):null,axesRangeCalculate:this.model.axesRangeCalculate,axesTitleRendering:this.model.axesTitleRendering,chartAreaBoundsCalculate:this.model.chartAreaBoundsCalculate,legendItemRendering:this.model.legendItemRendering,lengendBoundsCalculate:this.model.lengendBoundsCalculate,preRender:this.model.preRender,seriesRendering:this.model.seriesRendering,symbolRendering:this.model.symbolRendering,titleRendering:this.model.titleRendering,axesLabelsInitialize:this.model.axesLabelsInitialize,pointRegionMouseMove:this.model.pointRegionMouseMove,legendItemClick:this.model.legendItemClick,legendItemMouseMove:this.model.legendItemMouseMove,displayTextRendering:this.model.displayTextRendering,toolTipInitialize:this.model.toolTipInitialize,trackAxisToolTip:this.model.trackAxisToolTip,trackToolTip:this.model.trackToolTip,animationComplete:this.model.animationComplete,destroy:this.model.destroy,create:this.model.create,axesLabelRendering:t.proxy(this._labelRenders,this),multiLevelLabelClick:t.isNullOrUndefined(this._pivotClientObj)?t.proxy(this._multiLevelLabelClick,this):this._pivotClientObj.model.enableDeferUpdate?null:t.proxy(this._multiLevelLabelClick,this),pointRegionClick:t.isNullOrUndefined(this._pivotClientObj)?t.proxy(this._seriesClick,this):this._pivotClientObj.model.enableDeferUpdate?null:t.proxy(this._seriesClick,this)});this.model.enableRTL&&i!=null&&(yt=t.buildTag("div#"+this._id+"_toolTipTemplate.e-toolTip",t.buildTag("div.toolTipInfo",t.buildTag("div",i.measureNames+" : "+this._getLocalizedLabels("Measure")+" <br/>")[0].outerHTML+t.buildTag("div","#"+this._id+"_series.name#: "+this._getLocalizedLabels("Row")+" <br/>")[0].outerHTML+t.buildTag("div","#"+this._id+"_point.x# : "+this._getLocalizedLabels("Column")+" <br/>")[0].outerHTML+t.buildTag("div","#"+this._id+"_point.y# : "+this._getLocalizedLabels("Value"))[0].outerHTML).addClass("e-rtl")[0].outerHTML,{display:"none",width:"auto","min-width":"200px","font-size":"12px",padding:"2px 5px","background-color":"rgb(255, 255, 255)",border:"1px solid rgb(0, 0, 0)"})[0].outerHTML,n("#"+this._id).append(yt));n("#"+this._id+"progressContainer").hide();this.model.analysisMode==t.PivotChart.AnalysisMode.Olap&&this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode&&i!=null&&i!=""&&(this._labelCurrentTags.splice(0,this._labelCurrentTags.length),a=this,jQuery.each(i.labelTags,function(n,t){var r={},i=t.split("::");r={name:i[2],state:i[i.length-1],tag:t};a._labelCurrentTags.push(r)}),t.isNullOrUndefined(a._selectedTagInfo)||jQuery.each(this._labelCurrentTags,function(n,t){if(t.tag.split("::")[0]==a._selectedTagInfo.split("::")[0]&&a._drillAction=="drillup")return a._selectedTagInfo=a._labelCurrentTags[n].tag,!1}));lt=n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejPivotClient"):null;t.isNullOrUndefined(lt)||(lt._isTimeOut=!1);this._waitingPopup!=null&&this._waitingPopup.hide();n(this.element).parents(".e-pivotclient").length>0&&this._pivotClientObj.model.enableToolBar&&!this._pivotClientObj.model.displaySettings.enableFullScreen&&this.model.commonSeriesOptions.tooltip.visible&&this.element.find(".e-toolTipImg").addClass("e-enabled");t.isNullOrUndefined(this._pivotClientObj)||t.isNullOrUndefined(this._pivotClientObj)||!this._pivotClientObj.model.isResponsive||n("#"+this._id+"Container").width("100%")},_onChartLoad:function(n){for(var u,i=t.isNullOrUndefined(n.model.primaryXAxis.multiLevelLabels)?[]:n.model.primaryXAxis.multiLevelLabels,r=0;r<i.length;r++)if(i[r].span)for(i[r].border={type:"withouttopborder"},u=0;u<i[r].level;u++)i.push({start:i[r].start,end:i[r].end,visible:!0,text:" ",tag:i[r].tag,level:u,border:{type:"withouttopandbottomborder"}});return this.model.load},_onChartLoaded:function(){this.element.find("#"+this._id+"Container_svg_XAxisMultiLevelLabels_0").attr("cursor","pointer")},_setDrillParams:function(t,i){for(var e,o,f=this.getJSONRecords().chartLables[i.start+.5].split("~~"),r="",u=0;u<f.length;u++)if(r+=r==""?f[u]:">#>"+f[u],t==f[u])break;return e=r,this._drillAction=="drilldown"?this._drillParams.push(r):this._drillParams=n.map(this._drillParams,function(n){if(n.indexOf(r)==-1)return n}),n(this.element).parents(".e-pivotclient").length>0&&(o=n(this.element).parents(".e-pivotclient").data("ejPivotClient"),o._drillParams=this._drillParams),e},_getChartDrillDown:function(i,r,u){var et=this.model.dataSource.providerName==t.olap.Providers.Mondrian,k,nt,p,y,o,l,a,s,v,h,e,rt,d,b,f;if(this.model.analysisMode==t.PivotChart.AnalysisMode.Olap){for(this._drillAction=="drillup"&&(k=n.map(this.model.dataSource.rows,function(n,t){if(n.fieldName==r.hierarchyUniqueName)return{index:t,item:n}}),k.length>0&&!t.isNullOrUndefined(this._labelCurrentTags.expandedMembers)&&!t.isNullOrUndefined(this._labelCurrentTags.expandedMembers[k[0].index])&&(nt=0,n.each(this._labelCurrentTags.expandedMembers[k[0].index],function(n,t){i==t&&nt++}),nt==0&&this._labelCurrentTags.expandedMembers[k[0].index].push(i.split("&").join("&amp;")))),e=[],f=0;f<this.model.dataSource.rows.length;f++){if(!t.isNullOrUndefined(this._labelCurrentTags.expandedMembers))for(o=0;o<this._labelCurrentTags.expandedMembers.length;o++)if(!t.isNullOrUndefined(this._labelCurrentTags.expandedMembers[o]))for(p=0;p<this._labelCurrentTags.expandedMembers[o].length;p++)this._labelCurrentTags.expandedMembers[o][p].indexOf(this.model.dataSource.rows[f].fieldName)>=0&&e.indexOf(this._labelCurrentTags.expandedMembers[o][p].replace("&amp;","&"))<0&&e.indexOf(this._labelCurrentTags.expandedMembers[o][p])<0&&e.push(this._labelCurrentTags.expandedMembers[o][p]);for(o=0;o<this._selectedSeriesInfo.length;o++)this._selectedSeriesInfo[o].indexOf(this.model.dataSource.rows[f].fieldName)>=0&&e.indexOf(this._selectedSeriesInfo[o])<0&&e.indexOf(this._selectedSeriesInfo[o].replace(/&/g,"&amp;"))<0&&e.push(this._selectedSeriesInfo[o].replace(/&/g,"&amp;"))}for(f=0;f<this._selectedSeriesInfo.length;f++)this._selectedSeriesInfo[f]=this._selectedSeriesInfo[f].replace(/&/g,"&amp;");if(y=e,this._drillAction=="drillup"){for(f=0;f<this._labelCurrentTags.expandedMembers.length;f++)if(!t.isNullOrUndefined(this._labelCurrentTags.expandedMembers[f]))for(o=0;o<this._labelCurrentTags.expandedMembers[f].length;o++)this.model.enableMultiLevelLabels&&i!=""?this._labelCurrentTags.expandedMembers[f][o]==i&&(e=[],jQuery.map(this._labelCurrentTags.expandedMembers[f],function(n){n==i&&(h=!0);h||e.push(n)}),this._labelCurrentTags.expandedMembers[f]=e):this._labelCurrentTags.expandedMembers[f][o].split("::")[2]==r&&(i=this._labelCurrentTags.expandedMembers[f][o],e=[],jQuery.map(this._labelCurrentTags.expandedMembers[f],function(n){n.split("::")[2]==r&&(h=!0);h||e.push(n)}),this._labelCurrentTags.expandedMembers[f]=e);r=t.olap._mdxParser._splitCellInfo(i)}else i=i.indexOf("&amp")==-1?i.replace(/&/g,"&amp;"):i;for(l=-1,f=0;f<this.model.dataSource.rows.length;f++)if(this.model.dataSource.rows[f].fieldName==r.hierarchyUniqueName){l=f;break}for(et&&this._drillAction=="drillup"&&!t.isNullOrUndefined(this._selectedSeriesInfo[l])&&(this._selectedSeriesInfo[l]=i),a=n.inArray(i,this._selectedSeriesInfo),e=[],f=0;f<=a;f++)e.push(this._selectedSeriesInfo[f]);var ut=e,a=n.inArray(i,y),e=[];for(f=0;f<=a;f++)e.push(y[f]);if(y=e,s=n(this.element).parents(".e-pivotclient").length>0?this.element.parents(".e-pivotclient").data("ejPivotClient"):t.isNullOrUndefined(this._pivotClientObj)?this:this._pivotClientObj,this._drillAction=="drilldown")t.isNullOrUndefined(this._labelCurrentTags.expandedMembers)&&(this._labelCurrentTags.expandedMembers=[]),t.isNullOrUndefined(this._labelCurrentTags.expandedMembers[l])&&(this._labelCurrentTags.expandedMembers[l]=[]),this._labelCurrentTags.expandedMembers[l].push(i),this.element.parents(".e-pivotclient").length>0&&(s=this.element.parents(".e-pivotclient").data("ejPivotClient"),s._drillParams.push(n.map(y,function(n){return n.split("&amp;").join("&")}).join(">#>")));else if(this.element.parents(".e-pivotclient").length>0){s=this.element.parents(".e-pivotclient").data("ejPivotClient");var ft=[],tt="",w=[];for(n.each(y,function(n,t){(tt==""||tt.split("::")[0]!=t.split("::")[3])&&ft.push(t);tt=t}),n.each(ft,function(n,t){n<l&&t.length>0&&w.push(t.split("&amp;").join("&"))}),s._drillParams=n.grep(s._drillParams,function(t){return l<1?t.indexOf(i.split("&amp;").join("&"))==-1:!(t.indexOf(i.split("&amp;").join("&"))>-1&&n.map(t.split(">#>"),function(n){if(w.indexOf(n)>-1)return n}).length==w.length)}),h=!1,f=0;f<this._labelCurrentTags.expandedMembers.length;f++)if(!t.isNullOrUndefined(this._labelCurrentTags.expandedMembers[f])&&this._labelCurrentTags.expandedMembers[f].length>0){h=!0;break}if(s._drillParams.length>0&&this._drilledCellSet.length>0&&!h&&!t.isNullOrUndefined(s._drillParams[s._drillParams.length-1])){var a=0,g="",c="",it=!1;for(f=0;f<s._drillParams[s._drillParams.length-1].split(">#>").length;f++)c=s._drillParams[s._drillParams.length-1].split(">#>")[f],c.indexOf("undefined")==-1&&(a=g==""?0:c.split("::")[0].split(".")[0]+"."+c.split("::")[0].split(".")[1]==g.split("::")[0].split(".")[0]+"."+g.split("::")[0].split(".")[1]?a:a+1,n.each(this._drilledCellSet,function(t,i){(n.map(i[0].key.split("][").join("]>#>[").split(">#>"),function(n){if(w.indexOf(n)>-1)return n}).length!=w.length||w.length==0)&&n.each(i[0].key.split("][").join("]>#>[").split(">#>"),function(n,t){c.split("::")[0]+" !#rowheader"==t&&(it=!0)})}),it&&(c=c.split("&").join("&amp;"),t.isNullOrUndefined(this._labelCurrentTags.expandedMembers[a])?this._labelCurrentTags.expandedMembers[a]=c:this._labelCurrentTags.expandedMembers[a].push(c),it=!1),g=c)}}this._drillAction=="drilldown"?t.olap._mdxParser.updateDrilledReport({uniqueName:i,seriesInfo:ut,uniqueNameArray:y},"rowheader",s):t.olap._mdxParser.updateDrilledReport({uniqueName:i,seriesInfo:ut,uniqueNameArray:y,action:"collapse"},"rowheader",s)}else{if(v="",this._drillAction==""&&this.model.enableMultiLevelLabels)return this._waitingPopup.hide(),!1;if(this._drillAction=="drilldown"){if(this.model.enableMultiLevelLabels)v=this._setDrillParams(r,i),this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode?(this._selectedItem=r,this._generateData({JsonRecords:this._pivotEngine,PivotReport:this.getOlapReport()})):this._generateData(this._pivotEngine);else{for(t.isNullOrUndefined(this._labelCurrentTags.expandedMembers)&&(this._labelCurrentTags.expandedMembers=[]),this._labelCurrentTags.expandedMembers.push(r),b=this._cloneEngine(this._pivotEngine),f=0;f<this._labelCurrentTags.expandedMembers.length;f++)this._cropData(b,this._labelCurrentTags.expandedMembers[f],0,!0);this._generateData(b)}t.isNullOrUndefined(this._pivotClientObj)||this._pivotClientObj._trigger("chartDrillSuccess",{chartObj:this,drillAction:"drilldown",drilledMember:this.model.enableMultiLevelLabels?v:this._labelCurrentTags.expandedMembers.join(">#>"),event:u});this._trigger("drillSuccess",{chartObj:this,drillAction:"drilldown",drilledMember:this.model.enableMultiLevelLabels?v:this._labelCurrentTags.expandedMembers.join(">#>"),event:u})}else{if(this.model.enableMultiLevelLabels)v=this._setDrillParams(r,i),this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode?(this._selectedItem=r,this._generateData({JsonRecords:this._pivotEngine,PivotReport:this.getOlapReport()})):this._generateData(this._pivotEngine);else{for(h=!1,e=[],jQuery.map(this._labelCurrentTags.expandedMembers,function(n){n==r&&(h=!0);h||e.push(n)}),this._labelCurrentTags.expandedMembers=e,v=this._labelCurrentTags.expandedMembers.length==0?r:this._labelCurrentTags.expandedMembers.join(">#>")+">#>"+r,rt=this.model.operationalMode==t.PivotChart.OperationalMode.ClientMode,(this._labelCurrentTags.expandedMembers.length==0||rt)&&n(this.element).parents(".e-pivotclient").length>0&&(d=n(this.element).parents(".e-pivotclient").data("ejPivotClient"),d._drillParams.length>0&&(d._drillParams=n.grep(d._drillParams,function(n){return n.indexOf(rt?v:r)<0}),d._getDrilledMember())),b=this._cloneEngine(this._pivotEngine),f=0;f<this._labelCurrentTags.expandedMembers.length;f++)this._cropData(b,this._labelCurrentTags.expandedMembers[f],0,!0);this._generateData(b)}t.isNullOrUndefined(this._pivotClientObj)||this._pivotClientObj._trigger("chartDrillSuccess",{chartObj:this,drillAction:"drillup",drilledMember:v,event:u});this._trigger("drillSuccess",{chartObj:this,drillAction:"drillup",drilledMember:v,event:u})}}},_multiLevelLabelClick:function(r){var h,c,e,o,u,s,l;if(n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejWaitingPopup").show():t.isNullOrUndefined(this._waitingPopup)||this._waitingPopup.show(),this.model.analysisMode==t.PivotChart.AnalysisMode.Olap)if(this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode){if(h=parseInt(r.data.multiLevelLabel.tag.split("::")[r.data.multiLevelLabel.tag.split("::").length-1]),r.data.multiLevelLabel.tag.startsWith("[Measures]")||h==0)return n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejWaitingPopup").hide():t.isNullOrUndefined(this._waitingPopup)||this._waitingPopup.hide(),!1;this._selectedTagInfo=r.data.multiLevelLabel.tag;this._drillAction=h==2?"drilldown":"drillup";this._startDrilldown=!0;c=JSON.stringify(this.model.customObject);e=this.getOlapReport();this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:this._drillAction,element:this.element,customObject:this.model.customObject});this.model.customObject!=""&&this.model.customObject!=null&&this.model.customObject!=i?t.isNullOrUndefined(this._pivotClientObj)?this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:this._drillAction+"#fullchart",drilledSeries:this._selectedTagInfo,olapReport:e,customObject:c}),this.renderControlSuccess):this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:this._drillAction+"#fullchart",drilledSeries:this._selectedTagInfo,olapReport:e,clientReports:this._pivotClientObj.reports,customObject:c}),this.renderControlSuccess):t.isNullOrUndefined(this._pivotClientObj)?this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:this._drillAction+"#fullchart",drilledSeries:this._selectedTagInfo,olapReport:e,clientReports:this._pivotClientObj.reports}),this.renderControlSuccess):this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.drillDown,JSON.stringify({action:this._drillAction+"#fullchart",drilledSeries:this._selectedTagInfo,olapReport:e}),this.renderControlSuccess)}else{if(r.data.multiLevelLabel.tag.endsWith("0"))return t.isNullOrUndefined(this._waitingPopup)||this._waitingPopup.hide(),!1;var f=r.data.multiLevelLabel.tag.split("::").splice(0,r.data.multiLevelLabel.tag.split("::").length-1).join("::"),l=t.olap._mdxParser._splitCellInfo(f),a=this.getJSONRecords().labelTags[r.data.multiLevelLabel.start+.5].split("~~");for(this._selectedSeriesInfo=[],o=0;o<a.length;o++)this._selectedSeriesInfo.push(a[o].split("#")[a[o].split("#").length-1]);if(this._labelCurrentTags.collapsedMembers=n.grep(this._labelCurrentTags.collapsedMembers,function(n){return n!=r.data.multiLevelLabel.text.slice(2,r.data.multiLevelLabel.text.length)}),this._drillAction=r.data.multiLevelLabel.tag.split("::")[r.data.multiLevelLabel.tag.split("::").length-1]=="2"?"drilldown":r.data.multiLevelLabel.tag.split("::")[r.data.multiLevelLabel.tag.split("::").length-1]=="1"?"drillup":"",!t.isNullOrUndefined(this._labelCurrentTags.expandedMembers))for(u=0;u<this._labelCurrentTags.expandedMembers.length;u++)if(!t.isNullOrUndefined(this._labelCurrentTags.expandedMembers[u]))for(s=0;s<this._labelCurrentTags.expandedMembers[u].length;s++)if(this._labelCurrentTags.expandedMembers[u][s].indexOf(f.replace(/&/g,"&amp;"))>=0||this._labelCurrentTags.expandedMembers[u][s].indexOf(f)>=0){f=f.replace(/&/g,"&amp;");break}this._getChartDrillDown(f,l,r)}else l=r.data.multiLevelLabel.tag.split("#")[0],this._drillAction=r.data.multiLevelLabel.tag.endsWith("1")?"drilldown":r.data.multiLevelLabel.tag.endsWith("2")?"drillup":"",this._getChartDrillDown(r.data.multiLevelLabel,l,r)},_labelRenders:function(r){if(!t.isNullOrUndefined(this.model)&&this.model.enableMultiLevelLabels&&(this.seriesType()=="bar"||this.seriesType()=="stackingbar"?r.data.axis.orientation.toLowerCase()=="vertical":r.data.axis.orientation.toLowerCase()=="horizontal"))r.data.label.Text="";else if((t.isNullOrUndefined(this._pivotClientObj)?!0:!this._pivotClientObj._isExporting)&&(t.isNullOrUndefined(this._pivotClientObj)?this._trigger("axesLabelRendering",r):this._pivotClientObj._trigger("axesLabelRendering",r),(r.data.axis.orientation.toLowerCase()=="horizontal"&&r.data.label.Text!=i||(this.seriesType()=="bar"||this.seriesType()=="stackingbar")&&r.data.axis.orientation.toLowerCase()=="vertical"&&r.data.label.Text!=i)&&this.model.analysisMode==t.PivotChart.AnalysisMode.Olap)){var f=this.model.enableRTL?r.data.label.Text.toString().split("~~").reverse():r.data.label.Text.toString().split("~~"),u=this;t.isNullOrUndefined(u.getJSONRecords())||n.each(n.extend([],u.getJSONRecords().chartLables,!0),function(n,t){if(t.toString().split("#").join("~~")==f.join("~~"))return r.data.label.Text=t.toString().split("~~").map(function(n){return n.split("#")[n.split("#").length-1]}).join("~~"),!1})}},_reSizeHandler:function(){if(!t.isNullOrUndefined(this.element)){var n=this.element.find("#"+this._id+"Container").height(this.element.height()).width(this.element.width()).data("ejChart");t.isNullOrUndefined(n)||n.redraw()}},_getLocalizedLabels:function(n){return t.isNullOrUndefined(t.PivotChart.Locale[this.locale()])||t.PivotChart.Locale[this.locale()][n]==i?t.PivotChart.Locale["en-US"][n]:t.PivotChart.Locale[this.locale()][n]},_onPreventPanelClose:function(i){n("body").find("#preventDiv").remove();!n(".e-pivotgrid").data("ejWaitingPopup")||!t.isNullOrUndefined(i)&&(n(i.target).hasClass("pivotTree")||n(i.target).hasClass("pivotTreeContext")||n(i.target).hasClass("pivotTreeContextMenu"))||n(".e-pivotgrid").data("ejWaitingPopup").hide()},_seriesClick:function(r){var s,e,p,v,b,h,k,y,c,f,d;if(!this.model.enableMultiLevelLabels){this.element.find("#"+this._id+"ExpandMenu, .e-expandMenu, .e-dialog").remove();var o,u=[],w=this.element.position(),l=this,a=this;if(this.model.analysisMode==t.PivotChart.AnalysisMode.Olap&&this.model.operationalMode==t.PivotChart.OperationalMode.ServerMode)this._selectedTags=[],r.data.region!=null&&r.data.region!=i?(p=this.model.enableRTL?n.map(r.model.primaryXAxis.labels,function(n){return n.split("~~").reverse().join("~~")}):r.model.primaryXAxis.labels,o=p[r.data.region.Region.PointIndex],e=r.data.region.Region.PointIndex):r.data.point.x!=null&&r.data.point.x!=i&&r.data.point.x!=""&&(p=this.model.enableRTL?n.map(r.data.series.xAxis.labels,function(n){return n.split("~~").reverse().join("~~")}):r.data.series.xAxis.labels,o=p[r.data.point.X],e=r.data.point.X),this.model.enableMultiLevelLabels?s=r.model.primaryXAxis.labels[e].split("~~")[r.model.primaryXAxis.labels[e].split("~~").length-1]:n(n("#"+this._id+"Container_vml_XAxisLabels_0")[0]).children()[e]!=i?s=n(n("#"+this._id+"Container_vml_XAxisLabels_0")[0]).children()[e].innerHTML:n("#"+this._id+"Container_svg_XAxisLabels_0").children()[e]?s=n("#"+this._id+"Container_svg_XAxisLabels_0").children()[e].textContent:this.seriesType()=="bar"||this.seriesType()=="stackingbar"?n(n("#"+this._id+"Container_vml_YAxisLabels_0")[0]).children()[e]!=i?s=n(n("#"+this._id+"Container_vml_YAxisLabels_0")[0]).children()[e].innerHTML:n("#"+this._id+"Container_svg_YAxisLabels_0").children()[e]?s=n("#"+this._id+"Container_svg_YAxisLabels_0").children()[e].textContent:(v=r.model.primaryXAxis.labels[e].split("~~"),s=v[v.length-1]):(v=r.model.primaryXAxis.labels[e].split("~~"),s=v[v.length-1]),s&&(n("#"+this._id).find("#"+this._id+"ExpandMenu, .e-expandMenu, .e-dialog").remove(),b=o.split("~~"),jQuery.each(b,function(t,i){jQuery.each(a._labelCurrentTags,function(t,r){if(n.trim(i)==r.name)return a._selectedTags.push(r),!1})}),jQuery.each(this._selectedTags,function(i,r){r.state>1&&(l.model.enableRTL?u.push(n(t.buildTag("li.e-menuList exp",r.name+" - "+a._getLocalizedLabels("Expand"))[0]).attr("role","presentation")[0].outerHTML):u.push(n(t.buildTag("li.e-menuList exp",a._getLocalizedLabels("Expand")+" - "+r.name)[0]).attr("role","presentation")[0].outerHTML))}),jQuery.each(this._selectedTags,function(i,r){for(var f=0;f<o.split("~~").length-1;f++)o.split("~~")[f]==r.name&&r.state==1&&(l.model.enableRTL?u.push(n(t.buildTag("li.e-menuList clp",r.name+" - "+a._getLocalizedLabels("Collapse"))[0]).attr("role","presentation")[0].outerHTML):u.push(n(t.buildTag("li.e-menuList clp",a._getLocalizedLabels("Collapse")+" - "+o.split("~~")[f])[0]).attr("role","presentation")[0].outerHTML))}));else{if(o=r.data.region!=null&&r.data.region!=i?this.getJSONRecords().chartLables[r.data.region.Region.PointIndex]:r.data.series.xAxis.labels[r.data.point.X],this.model.analysisMode==t.PivotChart.AnalysisMode.Olap&&(o=o.split("~~").map(function(n){return n.split("#")[n.split("#").length-1]}).join("~~")),!t.isNullOrUndefined(this._labelCurrentTags.collapsedMembers))if(this.model.analysisMode==t.PivotChart.AnalysisMode.Olap)for(h=o.split("~~"),this.model.dataSource.values[0].axis==t.olap.AxisName.Row&&(k=n.map(this.model.dataSource.values[0].measures,function(n){return n.fieldName}),n.inArray(h[h.length-1],k)>=0&&h.splice(h.length-1,1)),this._selectedSeriesInfo=[],f=0;f<h.length;f++)y=h[f].split("#")[h[f].split("#").length-1],n(this.getJSONRecords().seriesTags).filter(function(n,t){return t.split("::")[2]==y}).length>0&&this._selectedSeriesInfo.push(n(this.getJSONRecords().seriesTags).filter(function(n,t){return t.split("::")[2]==y})[0]),n.inArray(y,this._labelCurrentTags.collapsedMembers)>=0&&(l.model.enableRTL?u.push(n(t.buildTag("li.e-menuList exp",y+" - "+this._getLocalizedLabels("Expand"))[0]).attr("role","presentation")[0].outerHTML):u.push(n(t.buildTag("li.e-menuList exp",this._getLocalizedLabels("Expand")+" - "+y)[0]).attr("role","presentation")[0].outerHTML));else n.inArray(o,this._labelCurrentTags.collapsedMembers)>=0&&(l.model.enableRTL?u.push(n(t.buildTag("li.e-menuList exp",o+" - "+this._getLocalizedLabels("Expand"))[0]).attr("role","presentation")[0].outerHTML):u.push(n(t.buildTag("li.e-menuList exp",this._getLocalizedLabels("Expand")+" - "+o)[0]).attr("role","presentation")[0].outerHTML));if(!t.isNullOrUndefined(this._labelCurrentTags.expandedMembers))if(this.model.analysisMode==t.PivotChart.AnalysisMode.Olap){for(c=0;c<this._labelCurrentTags.expandedMembers.length;c++)if(!t.isNullOrUndefined(this._labelCurrentTags.expandedMembers[c]))for(f=0;f<this._labelCurrentTags.expandedMembers[c].length;f++)l.model.enableRTL?u.push(n(t.buildTag("li.e-menuList clp",this._labelCurrentTags.expandedMembers[c][f].split("::")[2]+" - "+this._getLocalizedLabels("Collapse"))[0]).attr("role","presentation")[0].outerHTML):u.push(n(t.buildTag("li.e-menuList clp",this._getLocalizedLabels("Collapse")+" - "+this._labelCurrentTags.expandedMembers[c][f].split("::")[2])[0]).attr("role","presentation")[0].outerHTML)}else for(f=0;f<this._labelCurrentTags.expandedMembers.length;f++)l.model.enableRTL?u.push(n(t.buildTag("li.e-menuList clp",this._labelCurrentTags.expandedMembers[f]+" - "+this._getLocalizedLabels("Collapse"))[0]).attr("role","presentation")[0].outerHTML):u.push(n(t.buildTag("li.e-menuList clp",this._getLocalizedLabels("Collapse")+" - "+this._labelCurrentTags.expandedMembers[f])[0]).attr("role","presentation")[0].outerHTML)}u.length>0&&(u.push(n(t.buildTag("li.e-menuList exit",this._getLocalizedLabels("Exit"))[0]).attr("role","presentation")[0].outerHTML),d=n(t.buildTag("div#"+this._id+"ExpandMenu.e-expandMenu",u)[0]).attr("role","presentation")[0].outerHTML,n(d).ejDialog({width:"auto",target:"#"+this._id,enableResize:!1,enableRTL:this.model.enableRTL}),n("#"+this._id+"ExpandMenu_wrapper").appendTo(this.element).css({left:r.data.location.x+8+w.left,top:r.data.location.y+8+w.top,"min-height":navigator.userAgent.toLowerCase().indexOf("webkit")>0?"initial":"auto"}),n("#"+this._id+"ExpandMenu").css({"min-height":navigator.userAgent.toLowerCase().indexOf("webkit")>0?"initial":"auto"}));this.element.find(".e-titlebar, .e-header").remove()}t.isNullOrUndefined(this._pivotClientObj)?this._trigger("pointRegionClick",r):this._pivotClientObj._trigger("pointRegionClick",r)},_getExportModel:function(){var i,r,u,f;if(t.browserInfo().name!="msie"||!(t.browserInfo().version<=8)){i=n("#"+this._id+"Container").data("ejChart");r=i.model.primaryXAxis.zoomFactor;i.model.primaryXAxis.zoomFactor=1;i.model.enableCanvasRendering=!0;i.redraw();u=i["export"]();f=u.toDataURL("image/png");i.model.primaryXAxis.zoomFactor=r;i.model.enableCanvasRendering=!1;i.redraw();var e=n(this.element).css("background-color")!=""?n(this.element).css("background-color"):"rgb(255, 255, 255)",o={args:JSON.stringify({fileName:"Export",chartdata:f.split(",")[1],bgColor:e,exportFormat:"xls",title:"",description:"PivotChart"})};return o}},exportPivotChart:function(i,r,u){var f={},s={},v=null,y,l,a,p,w,o,h;if(f=t.isNullOrUndefined(u)||u.toLowerCase()!="excel"?{url:"",fileName:"PivotChart",exportMode:t.PivotChart.ExportMode.ClientMode,title:"",description:"",exportType:i,controlName:this,titleAlignment:"center"}:{url:"",fileName:"PivotChart",exportMode:t.PivotChart.ExportMode.ClientMode,title:"",description:"",exportType:i,controlName:this,exportChartAsImage:!0,titleAlignment:"center"},this._trigger("beforeExport",f),t.browserInfo().name!="msie"||!(t.browserInfo().version<=8)){(t.isNullOrUndefined(r)||r=="")&&(r="Sample");var e=n("#"+this._id+"Container").data("ejChart"),c={height:e.model.size.height,width:e.model.size.width},b=e.model.primaryXAxis.zoomFactor;e.model.primaryXAxis.zoomFactor=1;e.model.enableCanvasRendering=!0;e.model.size=v={height:t.isNullOrUndefined(f.height)?e.model.size.height:f.height,width:t.isNullOrUndefined(f.width)?e.model.size.width:f.width};e.redraw();y=e["export"]();l=y.toDataURL("image/png");e.model.primaryXAxis.zoomFactor=b;e.model.enableCanvasRendering=!1;e.model.size={height:c.height,width:c.width};e.redraw();a=n(this.element).css("background-color")!=""?n(this.element).css("background-color"):"rgb(255, 255, 255)";t.isNullOrUndefined(f.exportChartAsImage)||f.exportChartAsImage||(e.model.size=v,s=this._excelExport(e,c.height,c.width),s=s.replace(new RegExp("<br/>","g"),""));f.exportMode==t.PivotChart.ExportMode.ClientMode?(o={fileName:t.isNullOrUndefined(r)?t.isNullOrUndefined(f.fileName)?"PivotChart":f.fileName:r,chartdata:l.split(",")[1],bgColor:a,exportFormat:u,title:f.title,description:f.description,titleAlignment:f.titleAlignment},t.isNullOrUndefined(f.exportChartAsImage)||f.exportChartAsImage||(o.chartModel=s),h={args:JSON.stringify(o)},t.raiseWebFormsServerEvents&&(i=="excelExport"||i=="pdfExport"||i=="wordExport"||i=="imageExport")&&f.exportMode==t.PivotChart.ExportMode.ClientMode&&n.trim(f.url)==""?(p={model:this.model,originalEventType:i},w=h,t.raiseWebFormsServerEvents(i,p,w),setTimeout(function(){t.isOnWebForms=!0},1e3)):this.doPostBack(n.trim(f.url)!=""?f.url:i,h)):(o={exportOption:i,chartdata:l.split(",")[1],bgColor:a,title:f.title,description:f.description,titleAlignment:f.titleAlignment},t.isNullOrUndefined(f.exportChartAsImage)||f.exportChartAsImage||(o.chartModel=s),h={args:JSON.stringify(o)},this.doPostBack(n.trim(f.url)!=""?f.url:this.model.url+"/"+this.model.serviceMethodSettings.exportPivotChart,h))}},_excelExport:function(t,i,r){var u=n.extend(!0,{},t.model),c=t.svgHeight,l=t.svgWidth,s=i,h=r,e,a=t,f,o;if(u.event=null,u.primaryXAxis.range=u.primaryXAxis.actualRange,u.primaryYAxis.range=u.primaryYAxis.actualRange,t._ignoreOnExport)for(e=u.series,f=0;f<e.length;f++)delete e[f].dataSource,delete e[f].query,e[f].fill=jQuery.type(e[f].fill)=="array"?e[f].fill[0].color:e[f].fill;return u.size.height&&u.size.height.indexOf("%")!=-1&&(u.size.height=n(t.svgObject).height().toString()),u.size.width&&u.size.width.indexOf("%")!=-1&&(u.size.width=n(t.svgObject).width().toString()),o=JSON.stringify(u),u.size={width:h,height:s},o},doAjaxPost:function(r,u,f,e,o,s){var h,c,l,v=!0,y=this.model.enableXHRCredentials||!t.isNullOrUndefined(this._pivotClientObj)&&this._pivotClientObj.model.enableXHRCredentials,a;f.XMLA==i?(h="application/json; charset=utf-8",c="json",l=n.proxy(e,this)):(h="text/xml",c="xml",f=f.XMLA,l=n.proxy(e,t.olap.base,s),v=t.browserInfo().name=="msie"&&t.browserInfo().version<=9?!1:!0);a={type:r,url:u,data:f,async:v,contentType:h,dataType:c,success:l,xhrFields:{withCredentials:y},complete:t.proxy(function(){var n={action:!t.isNullOrUndefined(s)&&!t.isNullOrUndefined(s.action)?s.action:JSON.parse(f).action,customObject:this.model.customObject,element:this.element};this._trigger("renderComplete",n)},this),error:t.proxy(function(n){typeof this._waitingPopup!="undefined"&&this._waitingPopup!=null&&this._waitingPopup.hide();typeof oclientWaitingPopup!="undefined"&&oclientWaitingPopup!=null&&oclientWaitingPopup.hide();var t={action:this._currentAction,customObject:this.model.customObject,element:this.element,Message:n};this._trigger("renderFailure",t);this.renderChartFromJSON("")},this)};y||delete a.xhrFields;n.ajax(a)},doPostBack:function(t,i){var r=n("<form>").attr({action:t,method:"POST",name:"export"}),f=function(t,i){var u=n('<input type="hidden" title="params">').attr({id:t,name:t,value:i}).appendTo(r)};for(var u in i)f(u,i[u]);r.appendTo(document.body).submit().remove()}});t.PivotChart.Locale=t.PivotChart.Locale||{};t.PivotChart.Locale["en-US"]={Measure:"Measure",Row:"Row",Column:"Column",Value:"Value",Expand:"Expand",Collapse:"Collapse",Exit:"Exit",ChartTypes:"Chart Types",TDCharts:"3D Charts",Tooltip:"Tooltip",Exporting:"Exporting",Line:"Line",Spline:"Spline",Column:"Column",Area:"Area",SplineArea:"Spline Area",StepLine:"Step Line",StepArea:"Step Area",Pie:"Pie",Bar:"Bar",StackingArea:"Stacking Area",StackingColumn:"Stacking Column",StackingBar:"Stacking Bar",Pyramid:"Pyramid",Funnel:"Funnel",Doughnut:"Doughnut",Scatter:"Scatter",Bubble:"Bubble",TreeMap:"TreeMap",ColumnTD:"Column 3D",PieTD:"Pie 3D",BarTD:"Bar 3D",StackingBarTD:"StackingBar 3D",StackingColumnTD:"StackingColumn 3D",Excel:"Excel",Word:"Word",Pdf:"PDF",PNG:"PNG",EMF:"EMF",GIF:"GIF",JPG:"JPG",BMP:"BMP",ZoomIn:"Zoom In",ZoomOut:"Zoom Out",Legend:"Legend",SmartLabels:"Smart Labels",Interactions:"Interactions",Zooming:"Zooming",Rotate45:"Rotate45",Rotate90:"Rotate90",Trim:"Trim",MultipleRows:"Multiple Rows",Wrap:"Wrap",Hide:"Hide",WrapByWord:"Wrap By word",CrossHair:"Cross Hair",TrackBall:"Track Ball",DisableTD:"Disable 3D Charts",None:"None",Exception:"Exception",OK:"OK"};t.PivotChart.ChartTypes={Line:"line",Spline:"spline",Column:"column",Area:"area",SplineArea:"splinearea",StepLine:"stepline",StepArea:"steparea",Pie:"pie",Bar:"bar",StackingArea:"stackingarea",StackingColumn:"stackingcolumn",StackingBar:"stackingbar",Pyramid:"pyramid",Funnel:"funnel",Doughnut:"doughnut",Scatter:"scatter",Bubble:"bubble",WaterFall:"waterfall"};t.PivotChart.ExportOptions={Excel:"excel",Word:"word",PDF:"pdf",CSV:"csv",PNG:"png",JPG:"jpg",EMF:"emf",GIF:"gif",BMP:"bmp"};t.PivotChart.SymbolShapes={None:"none",LeftArrow:"leftarrow",RightArrow:"rightarrow",Circle:"circle",Cross:"cross",HorizLine:"horizline",VertLine:"vertline",Diamond:"diamond",Rectangle:"rectangle",Triangle:"triangle",InvertedTriangle:"invertedtriangle",Hexagon:"hexagon",Pentagon:"pentagon",Star:"star",Ellipse:"ellipse",Wedge:"wedge",Trapezoid:"trapezoid",UpArrow:"uparrow",DownArrow:"downarrow",Image:"image"};t.PivotChart.AnalysisMode={Olap:"olap",Pivot:"pivot"};t.PivotChart.OperationalMode={ClientMode:"clientmode",ServerMode:"servermode"};t.PivotChart.ExportMode={ClientMode:"clientmode",ServerMode:"servermode"}})(jQuery,Syncfusion)});
