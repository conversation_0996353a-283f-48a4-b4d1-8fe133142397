/*!
*  filename: ej.splitter.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.scroller.min"],n):n()})(function(){(function(n,t,i){t.widget("ejSplitter","ej.Splitter",{element:null,model:null,validTags:["div","span"],_rootCSS:"e-splitter",_setFirst:!1,angular:{terminal:!1},defaults:{cssClass:"",orientation:"horizontal",enableAnimation:!0,properties:[],height:null,width:null,enableAutoResize:!1,isResponsive:!1,enableRTL:!1,allowKeyboardNavigation:!0,htmlAttributes:{},expanderTemplate:null,animationSpeed:300,beforeExpandCollapse:null,clickOnExpander:null,expandCollapse:null,resize:null,create:null,destroy:null},dataTypes:{cssClass:"string",orientation:"enum",properties:"data",enableAutoResize:"boolean",expanderTemplate:"string",isResponsive:"boolean",enableRTL:"boolean",allowKeyboardNavigation:"boolean",animationSpeed:"number",enableAnimation:"boolean",htmlAttributes:"data"},_init:function(){this._initialize();this._render();this.model.isResponsive||this.model.enableAutoResize?this._wireEvents(!0):this._wireEvents(!1)},_setModel:function(n){for(var t in n)switch(t){case"cssClass":this._changeSkin(n[t]);break;case"enableAutoResize":this._windowResizing(n[t]);break;case"isResponsive":this._windowResizing(n[t]);break;case"enableRTL":this._rtl(n[t]);break;case"htmlAttributes":this._addAttr(n[t]);break;case"orientation":this._refreshSplitter("orientation",n[t]);break;case"properties":this._refreshSplitter("properties",n[t]);break;case"width":this.model.width=n[t];this._setHeightWidth();break;case"height":this.model.height=n[t];this._setHeightWidth()}},refresh:function(){this._setPanesSize();this._getPanesPercent()},collapse:function(n){this._clickArrow(n,!0,!0)},expand:function(n){this._clickArrow(n,!1,!0)},_clickArrow:function(i,r,u){if(this._inMovement||i<0||i>this.panes.length||this.panes.length<=1)return!1;var f,o=r?"e-collapse":"e-expand",e=this.element.children(".e-splitbar:not(.e-shadowbar)");if(t.isNullOrUndefined(this.model.expanderTemplate)?f=i==e.length?this._clickArrow(i-1,!r,!1):n(e[i]).children("."+o):(f=n(e[i]).children(".e-splitter-"+this.model.orientation.substr(0,1)+"-template"),r?this._collapseArrowClick(this.templateargs):this._expandArrowClick(this.templateargs)),t.isNullOrUndefined(this.model.expanderTemplate)||this._templateIconPositioning(!0),u)f.css("display")=="none"&&t.isNullOrUndefined(this.model.expanderTemplate)||(t.isDevice()?f.tap():f.mouseup());else return f},addItem:function(i,r,u){var e=this.panes.length,d,r,f,g,a,k,it;if(u=this._getNumber(u),d=this.element[this.containerCss](),t.isNullOrUndefined(u)&&(u=e),u<0||u>e)return"";r=this._getPaneProperty(r);r=this._checkMinMaxSize(r);f=t.buildTag("div.e-pane e-"+this.model.orientation.substr(0,1)+"-pane");this.element.append(f[this.containerCss](r.paneSize));g=r.paneSize=f[this.containerCss]();e==0&&this.panes.push(f);f.remove();var v=u,nt=e>0?e:this.panes.length,o,y,p,w,s={},c=0,l=!1;for(a=g+this._bar,y=0;y<2;y++){for(o=v;o<nt;o++){var tt=n(this.panes[o])[this.containerCss](),b=t.isNullOrUndefined(this.model.properties[o])?r.minSize:this.model.properties[o].minSize,h=tt-b;if(h>=a-c){s[o]=tt-(a-c);l=!0;break}else e==0&&h>=0?(s[o]=b,c+=h,l=!0):h>0&&(s[o]=b,c+=h)}if(l)break;else nt=v,v=0}if(!l)return"";for(k in s)n(this.panes[k])[this.containerCss](s[k]);return e<=0?f.append(n(this.panes[u])):u==e?(p="insertBefore",w=1,f.insertAfter(n(this.panes[u-1]))):(p="insertAfter",w=0,f.insertBefore(n(this.panes[u]))),this.model.properties.splice(u,0,r),e==0&&this.element.append(f[this.containerCss](d)),e>0&&(this.panes.splice(u,0,f),it=this._createSplitBar(u-w),it[p](f)),f.append(i),this._updateModel(),f},removeItem:function(i){var r=this.panes.length-1,f,u,e,o;if(i=this._getNumber(i),t.isNullOrUndefined(i)&&(i=r),i<0||i>r||r<0)return null;f=n(this.panes[i]);o=f[this.containerCss]()+this._bar;f.remove();e=this.element.children(".e-splitbar:not(.e-shadowbar)");i==r?(u=n(this.panes[i-1]),n(e[i-1]).remove()):(u=n(this.panes[i+1]),n(e[i]).remove());u[this.containerCss](u[this.containerCss]()+o);this._removeArrays(i);this._updateModel()},_checkMinMaxSize:function(n){return!t.isNullOrUndefined(n.minSize)&&n.paneSize<n.minSize&&(n.paneSize=n.minSize),!t.isNullOrUndefined(n.maxSize)&&n.paneSize>n.maxSize&&(n.paneSize=n.maxSize),n},_removeArrays:function(n){this.model.properties.splice(n,1);this.panes.splice(n,1);this.oldPaneSize.splice(n,1);this.oldPanePercent.splice(n,1);this._sizePercent.splice(n,1)},_getNumber:function(n){return n=parseFloat(n),isNaN(n)?null:n},_updateModel:function(){for(var t=0;t<this.panes.length;t++)this.model.properties[t].paneSize=n(this.panes[t])[this.containerCss]();this._getPanesPercent()},_getPaneProperty:function(t){return n.extend({paneSize:10,minSize:10,maxSize:null,collapsible:!0,resizable:!0,expandable:!0},t)},_changeSkin:function(n){this.element.removeClass(this.model.cssClass).addClass(n)},_windowResizing:function(n){n?this._wireEvents(n):this._unWireEvents()},_refreshSplitter:function(n,t){this._unWireEvents();this._refreshDestroy();this.model[n]=t;this._init()},_destroy:function(){this.element.removeClass("e-splitter");this._refreshDestroy();this._unWireEvents()},_refreshDestroy:function(){this.element.removeClass("e-widget e-box e-rtl"+this.model.cssClass+" e-"+this.model.orientation);this.element.children(".e-splitbar").remove();this.element.children(".e-pane").removeClass("e-pane e-"+this.model.orientation.substr(0,1)+"-pane").height("").width("")},_initialize:function(){this.panes=[];this.oldPaneSize=[];this.oldPanePercent=[];this._initialPropertiesValue=[];this._updateHeightWidth=!1;this.shadowBar=null;this._inMovement=!1;this.containerCss=this.model.orientation=="horizontal"?"width":"height";this.displayCss=this.model.orientation=="horizontal"?"left":"top";this.borderCss=this.model.orientation=="horizontal"?"right":"bottom";this._bar=9},_render:function(){this.element.addClass("e-widget e-box "+this.model.cssClass+" e-"+this.model.orientation).attr("data-role","splitter");for(var r=this.element[0],i=0,u=0;i<r.children.length;i++)n(r.children[i]).addClass("e-pane"),this.panes.push(r.children[i]);this._setPanesProperty();this._insertSplitBar();this._setDimentions();t.isNullOrUndefined(this.model.expanderTemplate)||this.element.find(".e-splitter-"+this.model.orientation.substr(0,1)+"-template").css("z-index",t.getMaxZindex()+1);this._setPanesSize();this._getPanesPercent();this._addAttr(this.model.htmlAttributes);this._checkProperties();this.element.find(".e-pane").addClass("e-"+this.model.orientation.substr(0,1)+"-pane");this.model.isResponsive&&(isNaN(this.model.height)&&this.model.height.indexOf("%")>0&&this.element.css("height",this.model.height),isNaN(this.model.width)&&this.model.width.indexOf("%")>0&&this.element.css("width",this.model.width))},_templateClick:function(t){var i=this.templateargs={cancel:!1,targetElement:n(t.target),event:t.type,model:this.model,currentTarget:n(t.currentTarget)};this._trigger("clickOnExpander",i)},_templateIconPositioning:function(n){var i=this;t.browserInfo().name=="webkit"&&n&&(i.element.find(".e-splitter-"+i.model.orientation.substr(0,1)+"-template").css("display","none"),setTimeout(function(){i.element.find(".e-splitter-"+i.model.orientation.substr(0,1)+"-template").css("display","block")},i._isMouseMove?10:i.model.animationSpeed),i._isMouseMove=!1);n||setTimeout(function(){i.element.find(".e-splitter-"+i.model.orientation.substr(0,1)+"-template").css("display","inline-block")},100)},_setPanesProperty:function(){for(var n=0;n<this.panes.length;n++)this.model.properties[n]!=i?(this.model.properties[n].paneSize=this.model.properties[n].paneSize==i?"0px":this.model.properties[n].paneSize,this.model.properties[n].minSize=isNaN(parseFloat(this.model.properties[n].minSize))?10:parseFloat(this.model.properties[n].minSize),this.model.properties[n].maxSize=isNaN(parseFloat(this.model.properties[n].maxSize))?null:parseFloat(this.model.properties[n].maxSize),this.model.properties[n].collapsible=this.model.properties[n].collapsible!=!1?!0:!1,this.model.properties[n].resizable=this.model.properties[n].resizable!=!1?!0:!1,this.model.properties[n].expandable=this.model.properties[n].expandable!=!1?!0:!1):this.model.properties.push({paneSize:"0px",minSize:10,maxSize:null,collapsible:!0,resizable:!0,expandable:!0}),this._initialPropertiesValue[n]=this.model.properties[n].paneSize},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.element.addClass(n):i.element.attr(t,n)})},_insertSplitBar:function(){if(this.panes.length>1)for(var t,n=0;n<this.panes.length-1;n++)t=this._createSplitBar(n),t.insertAfter(this.panes[n])},_createSplitBar:function(i){var u=this.model.orientation.substr(0,1),o,f,s,r,h=!1,c,e;if(r=t.buildTag("span.e-box e-splitbar e-split-divider e-"+u+"-bar").attr("aria-expanded",!0),t.browserInfo().name=="msie"&&r.addClass("e-pinch"),t.isNullOrUndefined(this.model.expanderTemplate))o=t.buildTag("span.e-icon e-collapse e-"+u+"-arrow "+(u=="h"?"e-arrow-sans-left":"e-arrow-sans-up")),r.append(o),s=t.buildTag("span.e-activebar e-"+u+"-arrow "),r.append(s),f=t.buildTag("span.e-icon e-expand e-"+u+"-arrow "+(u=="h"?"e-arrow-sans-right":"e-arrow-sans-down")),r.append(f),h=!0,this._on(o,t.isDevice()?"tap":"mouseup",this._collapseArrowClick),this._on(f,t.isDevice()?"tap":"mouseup",this._expandArrowClick),this.model.properties[i].collapsible||o.css("display","none"),this.model.properties[i].expandable||f.css("display","none"),this.model.properties[i+1].collapsible&&f.css("display","block");else{c=document.getElementsByClassName("e-split-divider")[0];e=document.createElement("span");n(e).append(this.model.expanderTemplate);n(e).attr("class","e-splitter-"+this.model.orientation.substr(0,1)+"-template e-resize");n(e).on(t.isTouchDevice()?"tap":"click",n.proxy(this._templateClick,this));r.append(e);this._templateIconPositioning(!1)}return this.model.properties[i].resizable&&this.model.properties[i+1].resizable&&(r.addClass("e-resize").removeClass("e-icon-hide"),h=!0,this._on(r,t.eventType.mouseDown,this._mouseDownOnDivider),t.isNullOrUndefined(this.model.expanderTemplate)&&this._on(s,t.eventType.mouseDown,this._mouseDownOnDivider)),h?(r.attr({role:"separator",tabindex:"0"}),this._on(r,"focus focusout",this._focusOnDivider)):r.attr({role:"presentation"}),r},_getTemplatedString:function(){for(var n=this.model.expanderTemplate,t=n.indexOf("${"),i=n.indexOf("}"),r;t!=-1&&i!=-1;)r=n.substring(t,i+1);return r},_getPanesPercent:function(){this._sizePercent=[];for(var r=this.element[this.containerCss](),i=r-(this.panes.length-1)*this._bar,t=0;t<this.panes.length;t++)n(this.panes[t]).hasClass("collapsed")||(this.oldPaneSize[t]=n(this.panes[t])[this.containerCss]()),this.oldPanePercent[t]=this._convertToPercent(i,this.oldPaneSize[t]),this._sizePercent.push(this._convertToPercent(i,n(this.panes[t])[this.containerCss]()))},_setDimentions:function(){var i=this._getParentObj(),n=parseInt(this.model.width),t=parseInt(this.model.height);isNaN(this.model.width)&&this.model.width.indexOf("%")>0&&(n=this.model.isResponsive?this._convertToPixel(i.innerWidth(),n):this.model.width);isNaN(this.model.height)&&this.model.height.indexOf("%")>0&&(t=this.model.isResponsive?this._convertToPixel(i.innerHeight(),t):this.model.height);this.model.height&&this.element.css("height",t);this.model.width&&this.element.css("width",n)},_setHeightWidth:function(){this._updateHeightWidth=!0;this._setDimentions();this._setPanesSize();this._windowResized();this._getPanesPercent()},_getParentObj:function(){return this.element.parent()},_checkProperties:function(){this.model.enableRTL&&this._rtl(this.model.enableRTL);this._prevSize=this.element[this.containerCss]()},_getExactInnerWidth:function(){var i=t.browserInfo();return i.name=="msie"?i.version==8||i.version==9?n(this.element)[this.containerCss]():parseFloat(window.getComputedStyle(this.element[0])[this.containerCss]):parseFloat(window.getComputedStyle(this.element[0])[this.containerCss])-(parseFloat(this.element.css("border-"+this.displayCss+"-width"))+parseFloat(this.element.css("border-"+this.borderCss+"-width"))+parseFloat(this.element.css("padding-"+this.displayCss))+parseFloat(this.element.css("padding-"+this.borderCss)))},_rtl:function(n){n?this.element.addClass("e-rtl"):this.element.removeClass("e-rtl")},_setPanesSize:function(){var r=this.containerCss,v=0,u=0,o=this.element[r](),b=!1,s=this._bar=n(this.element).find(">.e-splitbar").length>0?parseFloat(n(this.element).find(">.e-splitbar").css(r)):this._bar,p=[],e,i,f=this.panes.length,y,k,c,h,l,w,a;if(f>1)for(i=0;i<f;i++)n(this.panes[i]).css(r,this._updateHeightWidth==!0?this._initialPropertiesValue[i]:this.model.properties[i].paneSize),this._updateHeightWidth=!1,s=i==f-1?0:s,e=parseFloat(n(this.panes[i])[r]()),t.isNullOrUndefined(this.model.properties[i].maxSize)||(e=e>this.model.properties[i].maxSize?this.model.properties[i].maxSize:e),n(this.panes[i]).css(r,e),e<=0?(v++,p.push(i),u+=s):b?(n(this.panes[i]).css(r,0),u+=e+s,this.model.properties[i].paneSize=0):(u+=e+s,u>o&&(y=u-o+s,k=f-i-1,y+=k*s,n(this.panes[i]).css(r,y),b=!0,u+=y+s),this.model.properties[i].paneSize=e);else f==1&&(n(this.panes[0]).css(r,"100%"),u=o);if(f>1&&u!=o)if(h=n(this.panes[f-1]),u>o)c=u-o,h.css(r,c);else if(u<o)if(c=o-u,v>0)for(w=parseFloat(c/v),l=0;l<v;l++)n(this.panes[p[l]]).css(r,w),this.model.properties[p[l]].paneSize=w;else{for(a=f;a>0;a--)if(t.isNullOrUndefined(this.model.properties[a-1].maxSize)){h=n(this.panes[a-1]);break}h.css(r,parseFloat(h[r]()+c));this.model.properties[f-1].paneSize=h[r]()}f>1&&this._checkPaneSize()},_getUnit:function(n){return n=="px"?"px":n=="pt"?"pt":n.substr(1)=="%"?"%":"px"},_getNormalValue:function(n){var i,t,r,u;return this.model.orientation=="vertical"?(i=n.y-this.element.offset().top,t=i/this.element.outerHeight(),r=this.element.height()):(i=n.x-this.element.offset().left,t=i/this.element.outerWidth(),r=this.element.width()),t>1&&(t=1),t<0&&(t=0),u=t*r,this._trimValue(u)},_trimValue:function(n){var t,i,r;return t=1,i=n%t,r=n-i,Math.abs(i)*2>=t&&(r+=i>0?t:-t),parseFloat(r.toFixed(5))},_getSplitbarIndex:function(){return this.element.children(".e-splitbar:not(.e-shadowbar)").index(this.currentSplitBar)},_paneResize:function(){var t,i,r,u,f,e,o,s;if(this.shadowBar==null)return!1;this.currentSplitBar=this.shadowBar.next();e=this._getSplitbarIndex();i=this.shadowBar.prev();r=this.currentSplitBar.next();u=e;f=e+1;t=this.shadowBar.offset()[this.displayCss];t=t-this.currentSplitBar.offset()[this.displayCss];n(i).css(this.containerCss,t+n(i)[this.containerCss]()+"px");n(r).css(this.containerCss,n(r)[this.containerCss]()-t+"px");this.oldPaneSize[u]=n(i)[this.containerCss]();this.oldPaneSize[f]=n(r)[this.containerCss]();this.shadowBar.remove();this._checkPaneSize();o={item:i,index:u,size:this.oldPaneSize[u]};s={item:r,index:f,size:this.oldPaneSize[f]};this._updateModelValue(o,s);this._trigger("resize",{prevPane:o,nextPane:s,splitbarIndex:e})},_checkPaneSize:function(){for(var r=0,o,f,u=this.panes.length,e,i,t=0;t<u;t++)o=this.containerCss=="width"?n(this.panes[t]).outerWidth():n(this.panes[t]).outerHeight(),r+=o+this._bar;if(r-=this._bar,f=this._getExactInnerWidth(),r!=f)if(e=f-r,i=n(this.panes[u-1])[this.containerCss](),i==0){for(t=u-1;t>=0;t--)if(n(this.panes[t]).hasClass("expanded")&&!n(this.panes[t]).hasClass("collapsed")){i=n(this.panes[t])[this.containerCss]();n(this.panes[t]).css(this.containerCss,parseFloat(i+e));break}}else n(this.panes[u-1]).css(this.containerCss,parseFloat(i+e))},_maxminDraggableRange:function(n){var l,a,v,y,o,r,u,f,e,t,i,s,h,c;l=this.shadowBar.prev();this.currentSplitBar=this.shadowBar.next();a=this.currentSplitBar.next();v=l[this.containerCss]();y=a[this.containerCss]();o=this.displayCss=="left"?this.currentSplitBar[0].offsetLeft:this.currentSplitBar[0].offsetTop;r=o-v;u=y+o;c=this._getSplitbarIndex();f=c;e=c+1;t=this.model.properties[f].maxSize;i=this.model.properties[e].maxSize;t=t!=null?parseInt(t,10):null;i=i!=null?parseInt(i,10):null;this.model.properties[f].minSize=parseInt(this.model.properties[f].minSize,10);this.model.properties[e].minSize=parseInt(this.model.properties[e].minSize,10);s=this.model.properties[f].minSize;h=this.model.properties[e].minSize;this.shadowBar.removeClass("e-end-indicaton");n>u-h?(this.resizedPosition=u-h,this.shadowBar.addClass("e-end-indicaton")):n<r+s&&(this.resizedPosition=r+s,this.shadowBar.addClass("e-end-indicaton"));t!=null?n>r+t&&(this.resizedPosition=r+t,this.shadowBar.addClass("e-end-indicaton")):i!=null&&n<u-i&&(this.resizedPosition=u-i,this.shadowBar.addClass("e-end-indicaton"))},_collapseArrowClick:function(i){var r,s,u,f,h,o,c,p,e,b,w,a,v,y,l;if(this.shadowBar==null){if(r=t.isNullOrUndefined(i.target)?n(i.currentTarget):n(i.target),this._inMovement=!0,this.currentSplitBar=r.parent(),e={},b=this.panes.length,s=this._getSplitbarIndex(),u=this.currentSplitBar.prev(),f=this.currentSplitBar.next(),h=s,o=s+1,c=u[this.containerCss](),p=f[this.containerCss](),w=this,a={item:u,index:h,size:c},v={item:f,index:o,size:p},this._raiseEvent("beforeExpandCollapse",a,v,s,"beforeCollapse"))return!1;if(f.hasClass("collapsed")){if(c<this.oldPaneSize[o]){r.addClass("e-end-indicaton");this._inMovement=!1;n(document).on("mouseup",n.proxy(this._mouseUpOnArrow,this));return!1}u.removeClass("expanded");f.removeClass("collapsed");r.parent().addClass("e-resize").removeClass("e-icon-hide");n(r.siblings()).not(".e-activebar").css("display","block");this.model.properties[h].collapsible||r.css("display","none");o!=b-1&&(l=f.next(),this.model.properties[o+1].collapsible||l.find(".e-expand").css("display","none"),l.find(".e-collapse").css("display","block"),l.next().hasClass("collapsed")||(l.addClass("e-resize").removeClass("e-icon-hide"),l.attr("aria-expanded",!0)));e[this.containerCss]=this.oldPaneSize[o];f.animate(e,this.model.enableAnimation?this.model.animationSpeed:0);e[this.containerCss]=c-this.oldPaneSize[o];u.animate(e,this.model.enableAnimation?this.model.animationSpeed:0,function(){w._raiseEvent("expandCollapse",a,v,s,"collapsed")})}else this.oldPaneSize[h]=c,u.addClass("collapsed"),f.addClass("expanded"),this.currentSplitBar.attr("aria-expanded",!1),r.parent().removeClass("e-resize").addClass("e-icon-hide"),t.isNullOrUndefined(this.model.expanderTemplate)&&r.css("display","none"),this.model.properties[o].collapsible||n(r.siblings()).not(".e-activebar").css("display","block"),h!=0&&(y=u.prev(),y.find(".e-expand").css("display","none"),!this.model.properties[h-1].collapsible&&n(u.prev().prev()[0]).hasClass("expanded")&&y.find(".e-collapse").css("display","block"),y.removeClass("e-resize").addClass("e-icon-hide")),e[this.containerCss]=0,u.animate(e,this.model.enableAnimation?this.model.animationSpeed:0),e[this.containerCss]=c+p,f.animate(e,this.model.enableAnimation?this.model.animationSpeed:0,function(){w._raiseEvent("expandCollapse",a,v,s,"collapsed")})}},_expandArrowClick:function(i){var r,o,u,f,s,h,p,c,e,b,w,v,y,l,a;if(this.shadowBar==null){if(r=t.isNullOrUndefined(i.target)?n(i.currentTarget):n(i.target),this._inMovement=!0,this.currentSplitBar=r.parent(),e={},b=this.panes.length,o=this._getSplitbarIndex(),u=this.currentSplitBar.prev(),f=this.currentSplitBar.next(),s=o,h=o+1,p=u[this.containerCss](),c=f[this.containerCss](),w=this,v={item:f,index:h,size:c},y={item:u,index:s,size:p},this._raiseEvent("beforeExpandCollapse",v,y,o,"beforeExpand"))return!1;if(u.hasClass("collapsed")){if(c<this.oldPaneSize[s]){r.addClass("e-end-indicaton");this._inMovement=!1;n(document).on("mouseup",n.proxy(this._mouseUpOnArrow,this));return!1}u.removeClass("collapsed");f.removeClass("expanded");this.currentSplitBar.attr("aria-expanded",!0);r.parent().addClass("e-resize").removeClass("e-icon-hide");n(r.siblings()).not(".e-activebar").css("display","block");this.model.properties[h].collapsible||r.css("display","none");s!=0&&(a=u.prev(),this.model.properties[o-1].collapsible||a.find(".e-collapse").css("display","none"),a.find(".e-expand").css("display","block"),a.prev().hasClass("collapsed")||a.addClass("e-resize").removeClass("e-icon-hide"));e[this.containerCss]=this.oldPaneSize[s];u.animate(e,this.model.enableAnimation?this.model.animationSpeed:0);e[this.containerCss]=c-this.oldPaneSize[s];f.animate(e,this.model.enableAnimation?this.model.animationSpeed:0,function(){w._raiseEvent("expandCollapse",v,y,o,"expanded")})}else this.oldPaneSize[h]=c,u.addClass("expanded"),f.addClass("collapsed"),r.parent().removeClass("e-resize").addClass("e-icon-hide"),t.isNullOrUndefined(this.model.expanderTemplate)&&r.css("display","none"),this.model.properties[s].collapsible||n(r.siblings()).not(".e-activebar").css("display","block"),h!=b-1&&(l=f.next(),l.find(".e-collapse").css("display","none"),!this.model.properties[h+1].collapsible&&n(f.next().next()[0]).hasClass("collapsed")&&l.find(".e-expand").css("display","block"),l.removeClass("e-resize").addClass("e-icon-hide"),l.attr("aria-expanded",!1)),e[this.containerCss]=p+c,u.animate(e,this.model.enableAnimation?this.model.animationSpeed:0),e[this.containerCss]=0,f.animate(e,this.model.enableAnimation?this.model.animationSpeed:0,function(){w._raiseEvent("expandCollapse",v,y,o,"expanded")})}},_raiseEvent:function(n,t,i,r,u){return n=="expandCollapse"&&(this._inMovement=!1,this._updateModelValue(t,i)),this._trigger(n,{collapsed:t,expanded:i,splitbarIndex:r,action:u})},_updateModelValue:function(n,t){this.model.properties[n.index].paneSize=n.item[this.containerCss]();this.model.properties[t.index].paneSize=t.item[this.containerCss]();this._getPanesPercent()},_mouseUpOnArrow:function(){this.element.find(".e-end-indicaton").removeClass("e-end-indicaton");n(document).off("mouseup",n.proxy(this._mouseUpOnArrow,this))},_keydownOnDivider:function(t){var i=t.keyCode,u,r;if(i==37||i==38||i==39||i==40){if(t.preventDefault(),u=n(t.data.target),t.ctrlKey)this.shadowBar==null&&(this.currentSplitBar=u,r=this._getSplitbarIndex(),this.model.orientation=="vertical"?t.keyCode==38?this.collapse(r):t.keyCode==40&&this.expand(r):t.keyCode==37?this.collapse(r):t.keyCode==39&&this.expand(r));else if(u.hasClass("e-resize")){var e=this.shadowBar!=null?this.shadowBar:u,f=e.offset(),o={pageX:f.left,pageY:f.top};n.extend(!0,t,o);(this.model.orientation=="vertical"&&(t.keyCode==38||t.keyCode==40)||this.model.orientation=="horizontal"&&(t.keyCode==37||t.keyCode==39))&&(t.keyCode==38?t.pageY-=5:t.keyCode==40?t.pageY+=5:t.keyCode==37?t.pageX-=5:t.keyCode==39&&(t.pageX+=5),this._mouseMoveOnDivider(t))}}else i==13?(t.preventDefault(),this._mouseUpOnDivider()):i==27&&(t.preventDefault(),this.shadowBar!=null&&this.shadowBar.remove(),this.shadowBar=null,this._mouseUpOnDivider(),this.element.children(".e-splitbar.e-hover").focusout())},_focusOnDivider:function(t){if(t.type=="focus"){if(!n(t.target).hasClass("e-hover")&&(n(t.target).addClass("e-hover"),this.model.allowKeyboardNavigation))n(document).on("keydown",{target:t.target},n.proxy(this._keydownOnDivider,this))}else this.element.children(".e-splitbar.e-hover").removeClass("e-hover"),this._mouseUpOnDivider(),n(document).off("keydown",n.proxy(this._keydownOnDivider,this))},_mouseDownOnDivider:function(i){i.preventDefault();var r;if(r=n(i.target).hasClass("e-activebar")?n(i.target.parentElement):!t.isNullOrUndefined(this.model.expanderTemplate)&&n(i.target).parents(".e-splitbar").length>0?n(i.target).parents(".e-splitbar"):n(i.target),r.hasClass("e-splitbar")&&r.hasClass("e-resize")){this._overlayElement=t.buildTag("div.e-pane-overlay");r.hasClass("e-hover")||r.focus();this.element.find(".e-pane").not(".e-splitter").append(this._overlayElement);n(document).on(t.eventType.mouseMove,{target:n(i.target).hasClass("e-activebar")?i.target.parentElement:i.target},n.proxy(this._mouseMoveOnDivider,this));n(document).on(t.eventType.mouseUp,n.proxy(this._mouseUpOnDivider,this));n(document).on("mouseleave",n.proxy(this._mouseUpOnDivider,this))}else(r.hasClass("e-expand")||r.hasClass("e-collapse"))&&r.parent().focus()},_mouseMoveOnDivider:function(i){var r=i.data,f,u;i=i.type=="touchmove"?i.originalEvent.changedTouches[0]:i;this._isMouseMove=!0;f={x:i.pageX,y:i.pageY};this.resizedPosition=this._getNormalValue(f);this.shadowBar==null&&(u=n(r.target).hasClass("e-activebar")?n(r.target.parentElement):!t.isNullOrUndefined(this.model.expanderTemplate)&&n(i.target).parents(".e-splitbar").length>0?n(r.target).parents(".e-splitbar"):n(r.target),this.shadowBar=u.clone().addClass("e-shadowbar").removeClass("e-hover").removeClass("e-split-divider").insertBefore(u),this.shadowBar.children().remove());this._maxminDraggableRange(this.resizedPosition);this.shadowBar.css(this.displayCss,this.resizedPosition)},_mouseUpOnDivider:function(){this._paneResize();t.isNullOrUndefined(this.model.expanderTemplate)||this._templateIconPositioning(!0);this.element.find(".e-pane").not(".e-splitter").find(".e-pane-overlay").remove();n(document).off(t.eventType.mouseMove,n.proxy(this._mouseMoveOnDivider,this));n(document).off(t.eventType.mouseUp,n.proxy(this._mouseUpOnDivider,this));n(document).off("mouseleave",n.proxy(this._mouseUpOnDivider,this));this.shadowBar=null},_windowResized:function(){var i=this._getExactInnerWidth(),r,u,t,f,e;if(this._prevSize==i)return!1;for(r=this.panes.length,u=i-(r-1)*this._bar,this._prevSize=i,t=0;t<r;t++)f=this._convertToPixel(u,this._sizePercent[t]),n(this.panes[t]).css(this.containerCss,f+"px");for(t=0;t<this.oldPaneSize.length;t++)this.oldPaneSize[t]=this._convertToPixel(u,this.oldPanePercent[t]);e=n(this.panes[this.panes.length-1])[this.containerCss]();e==0&&this._checkPaneSize()},_convertToPercent:function(n,t){return t*100/n},_convertToPixel:function(n,t){return parseFloat(n*t/100)},_wireEvents:function(t){if(t)n(window).on("resize",n.proxy(this._windowResized,this))},_unWireEvents:function(){n(window).off("resize",n.proxy(this._windowResized,this))}})})(jQuery,Syncfusion)});
