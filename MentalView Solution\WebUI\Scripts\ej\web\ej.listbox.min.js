/*!
*  filename: ej.listbox.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min","./../common/ej.scroller.min","./../common/ej.draggable.min","./ej.checkbox.min"],n):n()})(function(){(function($,ej,undefined){ej.widget("ejListBox","ej.ListBox",{element:null,_ignoreOnPersist:["dataSource","query","itemRequestCount","fields","create","change","select","unselect","itemDragStart","itemDrag","itemDragStop","itemDrop","checkChange","destroy","actionComplete","actionFailure","actionSuccess","actionBegin","itemDropped","selected"],model:null,validTags:["ul"],_setFirst:!1,_rootCSS:"e-listbox",defaults:{itemsCount:null,totalItemsCount:null,dataSource:null,query:ej.Query(),itemRequestCount:5,itemHeight:null,fields:{id:null,text:null,imageUrl:null,imageAttributes:null,spriteCssClass:null,htmlAttributes:null,tooltipText:null,selectBy:null,checkBy:null,groupBy:null,tableName:null,selected:null,category:null,toolTipText:null},height:"auto",width:"200",template:null,text:"",selectedIndex:null,checkedIndices:[],selectedIndices:[],cascadeTo:null,value:"",cssClass:"",targetID:null,htmlAttributes:{},showRoundedCorner:!1,enableRTL:!1,enabled:!0,showCheckbox:!1,allowVirtualScrolling:!1,virtualScrollMode:"normal",enablePersistence:!1,allowMultiSelection:!1,allowDrag:!1,allowDrop:!1,enableIncrementalSearch:!1,enableWordWrap:!0,caseSensitiveSearch:!1,loadDataOnInit:!0,create:null,change:null,select:null,unselect:null,itemDragStart:null,itemDrag:null,itemDragStop:null,itemDrop:null,checkChange:null,destroy:null,actionComplete:null,actionSuccess:null,actionBeforeSuccess:null,focusIn:null,focusOut:null,actionFailure:null,actionBegin:null,cascade:null,sortOrder:"none",enableVirtualScrolling:!1,checkAll:!1,uncheckAll:!1,enableLoadOnDemand:!1,itemRequest:null,allowDragAndDrop:undefined,selectedItemIndex:null,enableItemsByIndex:null,checkItemsByIndex:null,disableItemsByIndex:null,uncheckItemsByIndex:null,itemDropped:null,selected:null,selectIndexChanged:null,selectedItems:[],checkedItems:[],checkedItemlist:[],selectedItemlist:[]},dataTypes:{cssClass:"string",itemsCount:"number",itemRequestCount:"number",allowDrag:"boolean",allowDrop:"boolean",enableWordWrap:"boolean",enableIncrementalSearch:"boolean",caseSensitiveSearch:"boolean",template:"string",targetID:"string",cascadeTo:"string",showRoundedCorner:"boolean",enableRTL:"boolean",enablePersistence:"boolean",enabled:"boolean",allowMultiSelection:"boolean",dataSource:"data",query:"data",checkedIndices:"data",selectedIndices:"data",htmlAttributes:"data",loadDataOnInit:"boolean",showCheckbox:"boolean",sortOrder:"enum"},observables:["value","dataSource"],value:ej.util.valueFunction("value"),dataSource:ej.util.valueFunction("dataSource"),enable:function(){var n,t;this.listContainer.hasClass("e-disable")&&(this.target.disabled=!1,this.model.enabled=this.model.enabled=!0,this.element.removeAttr("disabled"),this.listContainer.removeClass("e-disable"),this.model.allowMultiSelection&&this.listContainer.removeClass("e-disable"),n=this.listContainer.find(".e-vscrollbar,.e-hscrollbar"),this.model.showCheckbox&&(t=this.listContainer.find("li:not(.e-disable)"),t.find(".listcheckbox").ejCheckBox("enable")),n.length>0&&this.scrollerObj.enable())},disable:function(){if(!this.listContainer.hasClass("e-disable")){this.target.disabled=!0;this.model.enabled=this.model.enabled=!1;this.element.attr("disabled","disabled");this.listContainer.addClass("e-disable");this.model.allowMultiSelection&&this.listContainer.addClass("e-disable");var n=this.listContainer.find(".e-vscrollbar,.e-hscrollbar");this.model.showCheckbox&&this.element.find(".listcheckbox").ejCheckBox("disable");n.length>0&&this.scrollerObj.disable()}},selectItemByIndex:function(n){var r=this._lastEleSelect=this.model.selectedIndex,u=this.listitems?this.listitems:this.listitem,t,i;n!=0&&(n=parseInt(n));n!=null&&((n>this.element.find("li:not('.e-ghead')").length||n<0||1/n==-Infinity)&&(n=this.model.selectedIndex),t=$(this.element.find("li:not('.e-ghead')")[n]),t.hasClass("e-select")||(this._activeItem=n,this.element.children("li").removeClass("e-select"),this._selectedItems=[],this.model.selectedIndices=[],t.addClass("e-select"),this.model.showCheckbox&&($(t).find(".listcheckbox").ejCheckBox("isChecked")||($(t).find(".listcheckbox").ejCheckBox("option","checked",!0),t.removeClass("e-select"),$.inArray(this._activeItem,this._checkedItems)>-1||this._checkedItems.push(this._activeItem),$.inArray(t[0],this.model.checkedIndices)>-1||this.model.checkedIndices.push(this._activeItem))),this._selectedItems.push(t),this.model.selectedIndices.push(n),i=this._getItemObject(t,null),i.isInteraction=!1,this.model.select&&this._trigger("select",i)));this.model.cascadeTo&&(this._activeItem=n,this._cascadeAction());this._setSelectionValues()._onlistselection(r,this._activeItem)},checkItemByIndex:function(n){typeof n=="number"&&this.checkItemsByIndices(n.toString())},uncheckItemByIndex:function(n){typeof n=="number"&&this.uncheckItemsByIndices(n.toString())},checkItemsByIndices:function(n){var t,i,r,u;if(ej.isNullOrUndefined(n))return!1;if(t=n.toString().split(","),t.length>0)for(i=0;i<t.length;i++)t[i]!=null&&(this._activeItem=parseInt(t[i]),this._activeItem<0&&(this._activeItem=0),r=$(this.element.children("li:not('.e-ghead')")[this._activeItem]),this.model.showCheckbox&&($(r).find(".listcheckbox").ejCheckBox("isChecked")||($(r).find(".listcheckbox").ejCheckBox("option","checked",!0),this.checkedStatus=!0,$.inArray(this._activeItem,this._checkedItems)>-1||this._checkedItems.push(this._activeItem),$.inArray(r[0],this.model.checkedIndices)>-1||this.model.checkedIndices.push(this._activeItem),u=this._getItemObject(r,null),u.isInteraction=!1,this.model.checkChange&&this._trigger("checkChange",u))));this._setSelectionValues()},uncheckItemsByIndices:function(n){var t,i,r,u,f,e;if(ej.isNullOrUndefined(n))return!1;if(t=n.toString().split(","),t.length>0)for(i=0;i<t.length;i++)t[i]!=null&&(r=parseInt(t[i]),u=$(this.element.children("li:not('.e-ghead')")[parseInt(r)]),this.model.showCheckbox&&$(u).find(".listcheckbox").ejCheckBox("isChecked")&&($(u).find(".listcheckbox").ejCheckBox("option","checked",!1),this.checkedStatus=!1,f=$.inArray(r,this.model.checkedIndices),$.inArray(r,this._checkedItems)>-1&&this._checkedItems.splice(f,1),f>-1&&this.model.checkedIndices.splice(f,1),e=this._getItemObject(u,null),e.isInteraction=!1,this.model.checkChange&&this._trigger("checkChange",e)));this._setSelectionValues()},selectAll:function(){var t,n,i;if(!this.model.showCheckbox&&this.model.allowMultiSelection)for(t=this.element.children("li:not('.e-ghead')"),n=0;n<t.length;n++)$(t[n]).hasClass("e-select")||$(t[n]).hasClass("e-disable")||($(t[n]).addClass("e-select"),this._selectedItems.push($(t[n])),this.model.selectedIndices.push(n),i=this._getItemObject(t,null),i.isInteraction=!1,this.model.select&&this._trigger("select",i));this._setSelectionValues()},unSelectAll:function(){this.unselectAll()},unselectAll:function(){return this.model.showCheckbox||this._removeListHover(),this._setSelectionValues(),this},selectItemsByIndex:function(n){this.selectItemsByIndices(n)},selectItemsByIndices:function(n){var r,i,u,t,f;if(ej.isNullOrUndefined(n))return!1;if(r=n.toString().split(","),this.model.allowMultiSelection)for(i=0;i<r.length;i++)r[i]!=null&&!isNaN(parseInt(r[i]))&&r[i]<this.element.children("li").length&&(u=parseInt(r[i]),this._activeItem=u,t=$(this.element.children("li:not('.e-ghead')")[this._activeItem]),t.hasClass("e-select")||(t.addClass("e-select"),this._selectedItems.push(t),this.model.selectedIndices.push(u),this.model.showCheckbox&&($(t).find(".listcheckbox").ejCheckBox("isChecked")||($(t).find(".listcheckbox").ejCheckBox("option","checked",!0),t.removeClass("e-select"),$.inArray(this._activeItem,this._checkedItems)>-1||this._checkedItems.push(this._activeItem),$.inArray(t[0],this.model.checkedIndices)>-1||this.model.checkedIndices.push(this._activeItem))),f=this._getItemObject(t,null),f.isInteraction=!1,this.model.select&&this._trigger("select",f)));this._setSelectionValues()},unselectItemsByIndex:function(n){this.unselectItemsByIndices(n)},unselectItemsByIndices:function(n){for(var i,r,t,e,f=n.toString().split(","),u=0;u<f.length;u++)f[u]!=null&&(i=parseInt(f[u]),r=$(this.listItemsElement[i]),this._activeItem=i,r.removeClass("e-active e-select"),this.model.showCheckbox&&$(r).find(".listcheckbox").ejCheckBox("isChecked")&&($(r).find(".listcheckbox").ejCheckBox("option","checked",!1),t=$.inArray(i,this.model.checkedIndices),$.inArray(i,this._checkedItems)>-1&&this._checkedItems.splice(t,1),t>-1&&this.model.checkedIndices.splice(t,1)),this.model.selectedIndex==i&&(this.model.selectedIndex=this._activeItem=null),t=this._selectedItems.indexOf(r[0]),this._selectedItems.splice(this.model.selectedIndices.indexOf(t),1),this.model.selectedIndices.splice(this.model.selectedIndices.indexOf(t),1),e=this._getItemObject(r,null),e.isInteraction=!1,this.model.unselect&&this._trigger("unselect",e));this._setSelectionValues()},unselectItemByIndex:function(n){var t,i,r;n=parseInt(n);t=$(this.element.children("li:not('.e-ghead')")[n]);this.model.showCheckbox&&$(t).find(".listcheckbox").ejCheckBox("isChecked")&&($(t).find(".listcheckbox").ejCheckBox("option","checked",!1),i=$.inArray(n,this.model.checkedIndices),$.inArray(n,this._checkedItems)>-1&&this._checkedItems.splice(i,1),i>-1&&this.model.checkedIndices.splice(i,1));t.hasClass("e-select")&&(t.removeClass("e-active e-select"),this.model.selectedIndex==n&&(this.model.selectedIndex=this._activeItem=null),i=this._selectedItems.indexOf(t[0]),this._selectedItems.splice(this.model.selectedIndices.indexOf(i),1),this.model.selectedIndices.splice(this.model.selectedIndices.indexOf(i),1),r=this._getItemObject(t,null),r.isInteraction=!1,this.model.unselect&&this._trigger("unselect",r));this._setSelectionValues()},selectItemByText:function(n){ej.isNullOrUndefined(n)||this[this.model.allowMultiSelection?"selectItemsByIndices":"selectItemByIndex"](this.getIndexByText(n))},selectItemByValue:function(n){this[this.model.allowMultiSelection?"selectItemsByIndices":"selectItemByIndex"](this.getIndexByValue(n))},unselectItemByText:function(n){this[this.model.allowMultiSelection?"unselectItemsByIndices":"unselectItemByIndex"](this.getIndexByText(n))},unselectItemByValue:function(n){this[this.model.allowMultiSelection?"unselectItemsByIndices":"unselectItemByIndex"](this.getIndexByValue(n))},getSelectedItems:function(){var n=[],t=this;return $(t.model.selectedIndices).each(function(i,r){n.push(t.getItemByIndex(r))}),n},getCheckedItems:function(){var n=[],t=this;return $(t.model.checkedIndices).each(function(i,r){n.push(t.getItemByIndex(r))}),n},removeItem:function(){return this.removeSelectedItems()},removeItemByText:function(n){return ej.isNullOrUndefined(this.getItemByText(n))?!1:this.removeItemByIndex(this.getItemByText(n).index)},hideSelectedItems:function(){var n=this.getSelectedItems();this._hideOrShowItemsByIndex(n,"hide")},hideCheckedItems:function(){var n=this.getCheckedItems();this._hideOrShowItemsByIndex(n,"hide")},_hideOrShowItemsByIndex:function(n,t){if($.type(n)=="number")t=="hide"?($(this.listItemsElement[n]).hide(),$(this.listItemsElement[n]).next().hasClass("e-ghead")&&$(this.listItemsElement[n]).prev().hide()):($(this.listItemsElement[n]).show(),$(this.listItemsElement[n]).prev().hasClass("e-ghead")&&$(this.listItemsElement[n]).prev().show());else for(var i=0;i<n.length;i++)t=="hide"?n[i].item?n[i].item.hide():$(this.listItemsElement[n[i]]).hide():n[i].item?n[i].item.show():$(this.listItemsElement[n[i]]).show();this._refreshScroller()},showItemsByIndices:function(n){this._hideOrShowItemsByIndex(n,"show")},hideItemsByIndices:function(n){this._hideOrShowItemsByIndex(n,"hide")},_hideOrShowItemsByValue:function(n,t){var r,i;if($.type(n)=="array")for(i=0;i<this.listItemsElement.length;i++)for(r=0;r<=n.length;r++)$(this.listItemsElement[i]).attr("value")==n[r]&&(t=="hide"?$(this.listItemsElement[i]).hide():$(this.listItemsElement[i]).show());else for(i=0;i<this.listItemsElement.length;i++)$(this.listItemsElement[i]).attr("value")==n&&(t=="hide"?$(this.listItemsElement[i]).hide():$(this.listItemsElement[i]).show());this._refreshScroller()},showItemsByValues:function(n){this._hideOrShowItemsByValue(n,"show")},hideItemsByValues:function(n){this._hideOrShowItemsByValue(n,"hide")},showItemByValue:function(n){this._hideOrShowItemsByValue(n,"show")},hideItemByValue:function(n){this._hideOrShowItemsByValue(n,"hide")},showItemByIndex:function(n){this._hideOrShowItemsByIndex(n,"show")},hideItemByIndex:function(n){this._hideOrShowItemsByIndex(n,"hide")},hide:function(){this.listContainer.hide()},show:function(){this.listContainer.show()},hideAllItems:function(){this.element.find("li:visible").hide();this._refreshScroller()},showAllItems:function(){this.element.find("li:hidden").show();this._refreshScroller()},_stateMaintained:function(n){var f,u,t,i,r;if(this.model.disableItemsByIndex=[],this.model.selectedIndices=[],this.model.checkedIndices=[],this.model.selectedIndex>=n&&this.model.selectedIndex!=null&&(this.model.selectedIndex==n||$(this.element.children()[n-1]).hasClass("e-disable")?this.model.selectedIndex=null:this.model.selectedIndex!=n&&(this.model.selectedIndex-=1)),u=$(n).length,u>1){for(r=u;r>=0;r--)$(this.element.children()[n[r]]).remove();for(f=this.element.children().length,i=0;i<f;i++)$(this.element.children()[i]).hasClass("e-disable")&&this.model.disableItemsByIndex.push(i)}else{for(t=n-1,t;t>=0;t--)$(this.element.children()[t]).hasClass("e-disable")&&this.model.disableItemsByIndex.push(t),$(this.element.children()[t]).hasClass("e-select")&&this.model.selectedIndices.push(t),$(this.element.children()[t]).find(".listcheckbox").ejCheckBox("isChecked")&&this.model.checkedIndices.push(t);for(n=parseInt(n)+1,n;n<this._listSize;n++)$(this.element.children()[n]).hasClass("e-disable")&&this.model.disableItemsByIndex.push(n-1),$(this.element.children()[n]).hasClass("e-select")&&this.model.selectedIndices.push(n-1),$(this.element.children()[n]).find(".listcheckbox").ejCheckBox("isChecked")&&this.model.checkedIndices.push(n-1)}},removeAll:function(){var n,r,t,u,i;if(ej.isNullOrUndefined(this.dataSource()))return n=[],r=this.element.find("li"),$(r).each(function(t,i){n.push($(this).text());i.remove()}),this._refreshItems(),n;if(!(this.dataSource()instanceof ej.DataManager)){for(t=[],u=$(this.listItemsElement).length,i=0;i<u;i++)t.push(this._getRemovedItems([parseInt(0)]));return t}},removeItemByIndex:function(n){var i,t=this.model.selectedIndex,r=this.element.find("li:not('.e-ghead')");return ej.isNullOrUndefined(this.dataSource())?(i=$(r[n]).remove().text(),this._stateMaintained(n),this._refreshItems()):this.dataSource()instanceof ej.DataManager||(i=this._getRemovedItems([parseInt(n)])),this.model.selectedIndex=n==t?null:n<t?t-1:t,i},removeSelectedItems:function(){if(this.model.showCheckbox)return!1;if(ej.isNullOrUndefined(this.dataSource())){var n=this.value();return $(this.getSelectedItems()).each(function(n,t){t.item.remove()}),this._refreshItems(),n}if(!(this.dataSource()instanceof ej.DataManager))return this.model.selectedIndex=null,this._getRemovedItems(this.model.selectedIndices)},_getRemovedItems:function(n){var t=[];return this._stateMaintained(n),this.value(null),this._activeItem=null,this.dataSource(this.dataSource().filter(function(i,r){if(n.indexOf(r)!=-1)t.push(i);else return!0})),this.refresh(!0),t},getIndexByValue:function(n){for(var i,t=0;t<this.listItemsElement.length;t++)if($(this.listItemsElement[t]).attr("value")==n){i=t;break}return i},getIndexByText:function(n){var i,n,t,r;for(this.model.allowMultiSelection&&(n=n.split(","),i=[]),t=0;t<this.listItemsElement.length;t++)if(typeof n=="object"){for(r=0;r<n.length;r++)if($(this.listItemsElement[t]).text()==n[r]){i.push(t);break}}else if($(this.listItemsElement[t]).text()==n){i=t;break}return i},getTextByIndex:function(n){return $(this.element.find("li:not('.e-ghead')")[n]).text()},getItemByText:function(n){var i=this,t;return this.listItemsElement.each(function(){if($(this).text()==n)return t=i._getItemObject($(this)),!1}),t},getItemByIndex:function(n){return this._getItemObject($(this.element.children("li:not('.e-ghead')")[n]))},getListData:function(){if(ej.DataManager&&this.dataSource()instanceof ej.DataManager)return this.model.allowVirtualScrolling?(this.listitems=this.element.find("li"),this.listitems):this.listitems;if(this.dataSource()){if(this.model.sortOrder!="none"&&!(this.mapCateg&&this.mapCateg!="")){var n=ej.Query().sortBy(this.model.fields.text,this.model.sortOrder,!0);return ej.DataManager(this.dataSource()).executeLocal(n)}return this.dataSource()}return},enableItem:function(n){var t=this;this.listItemsElement.each(function(){if($(this).text()==n)return $(this).removeClass("e-disable"),t.model.showCheckbox&&$(this).find(".listcheckbox").ejCheckBox("enable"),t._disabledItems.splice($(this).index().toString()),!1})},disableItem:function(n){var t=this;this.listItemsElement.each(function(){if($(this).text()==n)return $(this).addClass("e-disable"),t.model.showCheckbox&&$(this).find(".listcheckbox").ejCheckBox("disable"),t._disabledItems.push($(this).index().toString()),!1})},moveUp:function(){var n=this.model.fields.groupBy!=null?this.model.allowMultiSelection||this.model.showCheckbox?!1:!0:!0;n&&(this.checkedorselected=this.model.checkedIndices.length==0?this.model.selectedIndices.reverse():this.model.checkedIndices.reverse(),this._checkstate(!0))},moveDown:function(){var n=this.model.fields.groupBy!=null?this.model.allowMultiSelection||this.model.showCheckbox?!1:!0:!0;n&&(this.checkedorselected=this.model.checkedIndices.length==0?this.model.selectedIndices:this.model.checkedIndices,this._checkstate())},_checkstate:function(n){var i=$(this.element.children("li:not('.e-ghead')")[this.checkedorselected[0]]),t,r;(!n||i.prev().hasClass("e-ghead"))&&i.next().hasClass("e-ghead")||ej.isNullOrUndefined(this.checkedorselected)||(t=0,r=this._getItem(this.checkedorselected[t]),this._moveupdown(r,t,n?"up":"down"))},_moveItem:function(n,t,i){var u=n,r=n.index(),f=i=="up",e=i=="down";this._addListHover();this._getItem(this._selectedItem).removeClass("e-hover");f?(t.insertAfter(u),t.hasClass("e-disable")&&$.inArray(r.toString(),this._disabledItems)>-1&&(this._disabledItems.splice($.inArray(r.toString(),this._disabledItems),1),this._disabledItems.push((r+1).toString())),this._selectedItem-=1,this._refreshItems()):e&&(t.insertBefore(u),t.hasClass("e-disable")&&$.inArray(r.toString(),this._disabledItems)>-1&&(this._disabledItems.splice($.inArray(r.toString(),this._disabledItems),1),this._disabledItems.push((r-1).toString())),this._selectedItem+=1,this._refreshItems())},_moveupdown:function(list,index,direction){for(var j=this.checkedorselected[index],next,k,i=0,i=j,ele,oneafter,length;i<$(this.element.children("li:not('.e-ghead')")).length;){if(next=$(this.element.children("li:not('.e-ghead')")[i]),ej.isNullOrUndefined(next))break;if(next.hasClass("e-select")||next.find("span").hasClass("e-checkmark")){k=i;direction=="down"?eval(i++):eval(i--);continue}else break}if(!ej.isNullOrUndefined(next)&&i<$(this.element.children("li")).length&&this._moveItem(list,next,direction),index<this.checkedorselected.length&&(ele=$(this.element.children("li")[this.checkedorselected[index]]),ele.next().hasClass("e-select")||ele.next().find("span").hasClass("e-checkmark")?oneafter=direction=="down"?!0:!1:ej.isNullOrUndefined(ele[0])?oneafter=direction=="up"?!0:!1:(ele.hasClass("e-select")||ele.find("span").hasClass("e-checkmark"))&&this._moveupdown(ele,index+1,direction)),length=this.element.children("li:not('.e-ghead')").length,this.model.checkedIndices.length==0)for(this.model.selectedIndices=[],i=0;i<length;i++)$(this.element.children("li:not('.e-ghead')")[i]).hasClass("e-select")&&this.model.selectedIndices.push(i);else for(this.model.checkedIndices=[],j=0;j<length;j++)$.parseJSON($(this.element.children("li:not('.e-ghead')")[j]).find("span").attr("aria-checked"))&&this.model.checkedIndices.push(j)},checkAll:function(){var t,n;if(!this.model.showCheckbox)return!1;for(t=this.element.find("li:not('.e-ghead')"),n=0;n<t.length;n++)$(t[n].firstChild).find(".listcheckbox").ejCheckBox("isChecked")||($(t[n].firstChild).find(".listcheckbox").ejCheckBox("option","checked",!0),this._checkedItems.push(t[n]),this.model.checkedIndices.push(n));this._setSelectionValues();this.model.uncheckAll=!1},unCheckAll:function(){this.uncheckAll()},uncheckAll:function(){var t,n;if(!this.model.showCheckbox)return!1;for(t=this.element.find("li:not('.e-ghead')"),n=0;n<t.length;n++)$(t[n].firstChild).find(".listcheckbox").ejCheckBox("isChecked")&&$(t[n].firstChild).find(".listcheckbox").ejCheckBox("option","checked",!1);this._checkedItems=[];this.model.checkedIndices=[];this._setSelectionValues();this.model.checkAll=!1},addItem:function(n,t){var o,s,t=!ej.isNullOrUndefined(t)&&t<=this.element.find("li:not('.e-ghead')").length?t:this.element.find("li:not('.e-ghead')").length,u=this,r=t,c,e,f,l,h,i;if(ej.isNullOrUndefined(this.dataSource()))if(n instanceof Array)$(n).each(function(n,i){u.addItem(i,t);t=t+1});else{if(this.model.fields.groupBy&&typeof n=="object")for(c=ej.Query().group(this.model.fields.groupBy),e=ej.DataManager([n]).executeLocal(c),this.dataSource([]),f=0;f<e.length;f++)this._setMapFields(),this.dummyUl.push(ej.buildTag("li.e-ghead",e[f].key)[0]),this._loadlist(e[f].items),this.dataSource(this.dataSource().concat(e[f].items));else o=typeof n=="object"?n[this.model.fields.text]:n,i=ej.isNullOrUndefined(this.model.fields.value)?"":n[this.model.fields.value],s=ej.isNullOrUndefined(this.model.fields.id)?"":n[this.model.fields.id],this.listitem=this.element.find("li:not('.e-ghead')").length?t-1<0?$(this.element.find("li:not('.e-ghead')")[0]).before('<li role="option" value="'+i+'" id="'+s+'">'+o+"<\/li>"):$(this.element.find("li:not('.e-ghead')")[t-1]).after('<li role="option" value="'+i+'" id="'+s+'">'+o+"<\/li>"):$(this.element).html('<li role="option" value="'+i+'" id="'+s+'">'+o+"<\/li>");this.listitems=this.element.find("li:not('.e-ghead')");this._addItemIndex=t;this.model.showCheckbox&&(l=ej.buildTag("input.listcheckbox e-align#popuplist"+(this.listitems.length-1)+"_"+this._id,"",{},{type:"checkbox",name:"list"+(this.listitems.length-1)}),$(this.listitems[t]).prepend(l),$($(this.listitems[t]).find(".listcheckbox")).ejCheckBox({change:$.proxy(this._onClickCheckList,this)}));(this.model.allowDrag||this.model.allowDrop)&&this._enableDragDrop();this._addItemIndex=null;this._refreshItems()}else if(!(this.dataSource()instanceof ej.DataManager)){for(u.dataSource()instanceof Object?(h={},n instanceof Object||(h[u.model.fields.text]=n,n=h)):n instanceof Array||(n=[n]),$(n).each(function(n,i){u.model.fields.groupBy!=null&&ej.isNullOrUndefined(i[u.model.fields.groupBy])||(u.dataSource().splice(t,0,i),t=t+1)}),this.model.disableItemsByIndex=[],this.model.selectedIndices=[],this.model.checkedIndices=[],this.model.selectedIndex>=r&&(this.model.selectedIndex+=1),i=r-1,i;i>=0;i--)$(this.element.find("li:not('.e-ghead')")[i]).hasClass("e-disable")&&this.model.disableItemsByIndex.push(i),$(this.element.find("li:not('.e-ghead')")[i]).hasClass("e-select")&&this.model.selectedIndices.push(i),$(this.element.children()[i]).hasClass("e-checkmark")&&this.model.checkedIndices.push(i);for(r;r<this._listSize;r++)$(this.element.find("li:not('.e-ghead')")[r]).hasClass("e-disable")&&this.model.disableItemsByIndex.push(r+1),$(this.element.find("li:not('.e-ghead')")[r]).hasClass("e-select")&&this.model.selectedIndices.push(r+1),$(this.element.find("li:not('.e-ghead')")[r]).find(".listcheckbox").ejCheckBox("isChecked")&&this.model.checkedIndices.push(r+1);this.refresh(!0);this.listItemsElement=this.element.find("li:not('.e-ghead')")}},enableItemByIndex:function(n){typeof n=="number"&&this.enableItemsByIndices(n.toString())},disableItemByIndex:function(n){typeof n=="number"&&this.disableItemsByIndices(n.toString())},disableItemsByIndices:function(n){var t,i,r;if(ej.isNullOrUndefined(n))return!1;for(t=n.toString().split(","),i=0;i<t.length;i++)t.length>0&&!($.inArray(t[i],this._disabledItems)>-1)&&(r=$(this.element.children("li:not('.e-ghead')")[parseInt(t[i])]).addClass("e-disable"),r.find(".listcheckbox").ejCheckBox("disable"),this._disabledItems.push(t[i]))},enableItemsByIndices:function(n){for(var u,t=n.toString().split(","),r,i=0;i<t.length;i++)t.length>0&&$.inArray(t[i],this._disabledItems)>-1&&(r=$.inArray(t[i],this._disabledItems),u=$(this.element.children("li:not('.e-ghead')")[parseInt(t[i])]).removeClass("e-disable"),u.find(".listcheckbox").ejCheckBox("enable"),this._disabledItems.splice(r,1))},_init:function(){this._id=this.element[0].id;this._isMozilla=ej.browserInfo().name=="mozilla"?!0:!1;this._cloneElement=this.element.clone();this._deprecatedValue()._initialize()._render()._wireEvents();this._initValue=this.focused=this.datamerged=this.groupData=!1;this._typeInterval=null;this._dummyVirtualUl=[];this._virtualCount=0;this._liItemHeight=0;this._typingThreshold=2e3;this._dataUrl=this.dataSource();this.model.checkAll&&this.checkAll();this.model.uncheckAll&&this.uncheckAll();this.model.disableItemsByIndex&&this.disableItemsByIndices(this.model.disableItemsByIndex.toString());this.model.enableItemsByIndex&&this.enableItemsByIndices(this.model.enableItemsByIndex.toString());this.model.uncheckItemsByIndex&&this.uncheckItemsByIndices(this.model.uncheckItemsByIndex.toString());this._deprecatedValue()._enabled(this.model.enabled);this.listContainer&&this.listContainer.attr("role","listbox");this.element.children("li").attr("role","option")},_deprecatedValue:function(){return this.model.itemDrop=this.model.itemDrop||this.model.itemDropped,this.model.change=this.model.change||this.model.selectIndexChanged,this.model.fields.checkBy=this.model.fields.selected||this.model.fields.checkBy,this.model.fields.tooltipText=this.model.fields.toolTipText||this.model.fields.tooltipText,this.model.fields.groupBy=this.model.fields.category||this.model.fields.groupBy,this.model.select=this.model.select||this.model.selected,this.model.allowDragAndDrop!=undefined&&(this.model.allowDrag=this.model.allowDrop=!0),this.model.selectedIndex=this.model.selectedIndex!=null?this.model.selectedIndex:this.model.selectedItemIndex,this.model.checkedIndices=(this.model.checkedIndices.length?this.model.checkedIndices:null)||(this.model.checkItemsByIndex?this.model.checkItemsByIndex:null)||(this.model.checkedItems.length?this.model.checkedItems:null)||(this.model.checkedItemlist.length?this.model.checkedItemlist:[]),this.model.selectedIndices=(this.model.selectedIndices.length?this.model.selectedIndices:null)||(this.model.selectedItems.length?this.model.selectedItems:null)||(this.model.selectedItemlist.length?this.model.selectedItemlist:[]),this},_setModel:function(n){var t,r=!1,f,i,s,u;for(t in n)switch(t){case"value":this._setText(ej.util.getVal(n[t]));break;case"dataSource":ej.isNullOrUndefined(this._isCasCadeTarget)||(this.model.selectedIndex=null);this.model.checkedIndices=[];n[t]=ej.util.getVal(n[t]);this._checkModelDataBinding(n[t]);break;case"query":this._queryCheck(n[t]);break;case"fields":this.model.fields=$.extend(this.model.fields,n[t]);this._checkModelDataBinding(this.dataSource());break;case"template":this.model.template=n[t];this.refresh(!0);break;case"loadDataOnInit":this._loadContent=n[t];this._checkModelDataBinding(this.dataSource());break;case"enableRTL":this.model.enableRTL=n[t];this.model.enableRTL?this.listContainer.addClass("e-rtl"):this.listContainer.removeClass("e-rtl");break;case"enabled":this.model.enabled=n[t];this._enabled(n[t]);break;case"enableWordWrap":this.model.enableWordWrap=n[t];this._wordWrapItems(n[t]);break;case"height":case"width":this.model[t]=n[t];this._setDimensions();break;case"cssClass":this.model.cssClass=n[t];this.listContainer.addClass(this.model.cssClass);break;case"showCheckbox":this._checkboxHideShow(n[t]);n[t]&&this._removeListHover();break;case"showRoundedCorner":this.model.showRoundedCorner=n[t];this._roundedCorner();break;case"selectedItemIndex":case"selectedIndex":this.listitem[n[t]]||n[t]==null||this.listitems[n[t]]?(this.selectItemByIndex(n[t]),this.model.selectedIndex=this.model.selectedItemIndex=n[t]):n[t]=this.model.selectedIndex;break;case"sortOrder":this.model.sortOrder=n[t];this.display=!0;this.dataSource()!=null?this._showFullList():this._renderlistContainer();break;case"checkItemsByIndex":case"checkedItemlist":case"checkedItems":case"checkedIndices":this.uncheckAll();this.checkItemsByIndices(n[t].toString());n[t]=this.model[t]=this.model.checkedIndices;break;case"uncheckItemsByIndex":this.uncheckItemsByIndices(n[t].toString());this.model[t]=n[t];break;case"selectedItemlist":case"selectedItems":case"selectedIndices":this.unselectAll();this.selectItemsByIndices(n[t].toString());n[t]=this.model.selectedIndices;break;case"enableItemsByIndex":this.model[t]=n[t];this.enableItemsByIndices(n[t].toString());break;case"disableItemsByIndex":this.model[t]=n[t];this.disableItemsByIndices(n[t].toString());break;case"enableVirtualScrolling":this.model.allowVirtualScrolling=n[t];r=!0;break;case"allowDrag":case"allowDrop":case"allowDragAndDrop":case"allowVirtualScrolling":case"virtualScrollMode":this.model[t]=n[t];r=!0;break;case"checkAll":this.model[t]=n[t];n[t]?this.checkAll():this.uncheckAll();break;case"uncheckAll":this.model[t]=n[t];n[t]?this.uncheckAll():this.checkAll();break;case"htmlAttributes":this._addAttr(n[t]);break;case"itemsCount":f=this.model.itemsCount;this.model.height?(this.model.itemsCount=n[t],this._setItemsCount()._setDimensions()):n[t]=f;break;case"itemHeight":var e=this.listItemsElement,o=ej.isNullOrUndefined(n[t])?n[t]:n[t].toString().replace("px",""),h=ej.isNullOrUndefined(this.model.itemHeight)?this.model.itemHeight:this.model.itemHeight.toString().replace("px","");for(i=0;i<e.length;i++)s=ej.isNullOrUndefined(n[t])?{"min-height":ej.isNullOrUndefined(this.model.itemHeight)?"20px":h}:{"min-height":o+"px",height:o+"px"},e.eq(i).css(s);this.refresh();break;case"allowMultiSelection":this.model.allowMultiSelection=n[t];n[t]||(u=this.model.selectedIndex,this._removeListHover(),ej.isNullOrUndefined(u)?"":this.selectItemByIndex(u));break;case"totalItemsCount":ej.isNullOrUndefined(this.dataSource())||(this.model.totalItemsCount=n[t],this.model.query&&this._queryCheck(this.model.query))}r&&this._refresh()},_destroy:function(){return ej.isNullOrUndefined(this._lilist)||$(this._lilist).ejDraggable("destroy"),this.element.insertAfter(this.listContainer),this.element.find(".e-chkbox-wrap").remove(),this.listContainer.remove(),this.element.removeClass("e-ul"),this._isList||this.element.empty(),$(window).off("resize",$.proxy(this._OnWindowResize,this)),this._ListEventUnbind(this.element.children("li")),this},_ListEventUnbind:function(n){n.off("contextmenu",$.proxy(this._OnMouseContext,this));n.off("click",$.proxy(this._OnMouseClick,this));n.off("touchstart mouseenter",$.proxy(this._OnMouseEnter,this));n.off("touchend mouseleave",$.proxy(this._OnMouseLeave,this))},_refresh:function(){this._destroy()._init()},_finalize:function(){return this.model.selectedIndex!=null?this.selectItemByIndex(this.model.selectedIndex):this.model.showCheckbox==!0&&this._selectedItems.length>0&&this._selectCheckedItem(this._selectedItems),this.model.checkedIndices!=null&&this.checkItemsByIndices(this.model.checkedIndices.toString()),this},_initialize:function(){return this._isList=this.element.children().length?!0:!1,this.target=this.element[0],this._queryString=null,this._disabledItems=[],this._itemId=null,this._up=this._down=this._ctrlClick=!1,this.checkedStatus=this._isScrollComplete=!1,this._incqueryString="",this._totalCount=0,this._activeItem=null,this._initValue=!0,this.model.allowVirtualScrolling=this.model.allowVirtualScrolling?this.model.allowVirtualScrolling:this.model.enableLoadOnDemand,this.model.virtualScrollMode=this.model.enableVirtualScrolling?"continuous":this.model.virtualScrollMode,this._selectedItems=[],this._checkedItems=[],this._loadContent=this.model.loadDataOnInit,this._loadInitialRemoteData=!0,this._skipInitialRemoteData=!1,this.model.enableVirtualScrolling&&(this.model.allowVirtualScrolling=!0),this._setItemsCount(),this},_render:function(){return this._savedQueries=this.model.query.clone(),this.model.totalItemsCount&&this._savedQueries.take(this.model.totalItemsCount),this._renderContainer()._addAttr(this.model.htmlAttributes),ej.DataManager&&this.dataSource()instanceof ej.DataManager?this._loadInitialRemoteData&&this._initDataSource(this.dataSource()):this._showFullList(),this.dataSource()||this._finalize(),this.listItemsElement=this.element.find("li:not('.e-ghead')"),this.model.showRoundedCorner&&this._roundedCorner(),this},_queryCheck:function(n){this._savedQueries=n.clone();this.element.empty();this.dataSource()&&this._checkModelDataBinding(this.dataSource())},_checkModelDataBinding:function(n){this.mergeValue=null;this.dataSource(n);n!=null&&n.length!=0?ej.DataManager&&n instanceof ej.DataManager?this._initDataSource(n):this._showFullList():(this.element.empty(),this._refreshScroller())},_initDataSource:function(n){var t=this,i;t.model.actionBegin&&t._trigger("actionBegin",{});t.listitems=t.dataSource();t._updateLoadingClass(!0);i=n.executeQuery(this._getQuery());i.done(function(n){t._totalCount=n.count;t.listitems=n.result;t._updateLoadingClass()._showFullList()._trigger("actionSuccess",n);t._finalize();t._virtualPages=[0]}).fail(function(n){t.dataSource(null);t._updateLoadingClass(!0)._trigger("actionFailure",n)}).always(function(n){t.model.checkAll&&t.checkAll();t.model.uncheckAll&&t.uncheckAll();t._trigger("actionComplete",n)})},_getQuery:function(){var n,i,t,r;if(ej.isNullOrUndefined(this.model.query)){i=[];t=this.model.fields;n=ej.Query();for(r in t)r!=="tableName"&&i.push(t[r]);i.length>0&&n.select(i);this.dataSource().dataSource.url.match(t.tableName+"$")||ej.isNullOrUndefined(t.tableName)||n.from(t.tableName)}else n=this.model.query.clone();return this.model.allowVirtualScrolling&&(n.requiresCount(),n.take(this.model.itemRequestCount)),n},_getLiHeight:function(){this._liItemHeight=$(this.element.find("li")[0]).outerHeight()},_addDragableClass:function(){if(this.model.allowDrag||this.model.allowDrop){this.element.css("cursor","pointer");this.model.allowDrop&&(this.listContainer.addClass("e-droppable"),this.listBoxScroller.addClass("e-droppable"));var n=this;this.element.children("li").each(function(){n.model.allowDrag&&$(this).addClass("e-draggable");n.model.allowDrop&&$(this).addClass("e-droppable")})}return this},_enableDragDrop:function(){(this.model.allowDrag||this.model.allowDrop)&&this._drag()},_updateLoadingClass:function(n){return this.listContainer[n?"addClass":"removeClass"]("e-load"),this},_addAttr:function(n){var t=this;$.map(n,function(n,i){i=="class"?t.listContainer.addClass(n):i=="required"?t.element.attr(i,n):i=="disabled"&&n=="disabled"?t._enabled(!1):t.listContainer.attr(i,n)})},_renderContainer:function(){return this.listContainer=ej.buildTag("div.e-ddl-popup e-box e-popup e-widget "+this.model.cssClass,"",{visibility:"hidden"},{tabIndex:0,id:this._id+"_container"}),this.listBoxScroller=ej.buildTag("div.e-listbox-container"),this.ultag=ej.buildTag("ul.e-ul","",{},{role:"listbox"}),this.element=this.element.addClass("e-ul"),this.listContainer.append(this.listBoxScroller).insertAfter(this.element),this.listBoxScroller.append(this.element),this.element.attr("data-ej-unselectable","on").css("user-select","none"),this._hiddenInput=ej.buildTag("input#"+this._id+"_hidden","",{},{type:"hidden"}).insertBefore(this.element),this._hiddenInput.attr("name",this._id),this},_setMapFields:function(){var n=this.model.fields;this.mapFld={_id:null,_imageUrl:null,_imageAttributes:null,_tooltipText:null,_spriteCSS:null,_text:null,_value:null,_htmlAttributes:null,_selectBy:null,_checkBy:null};this.mapFld._id=n&&n.id?n.id:"id";this.mapFld._imageUrl=n&&n.imageUrl?n.imageUrl:"imageUrl";this.mapFld._tooltipText=n&&n.tooltipText?n.tooltipText:"tooltipText";this.mapFld._imageAttributes=n&&n.imageAttributes?n.imageAttributes:"imageAttributes";this.mapFld._spriteCSS=n&&n.spriteCssClass?n.spriteCssClass:"spriteCssClass";this.mapFld._text=n&&n.text?n.text:this.listitems[0].text?"text":this._getObjectKey(this.listitems[0])[0];this.mapFld._value=n&&n.value?n.value:"value";this.mapFld._htmlAttributes=n&&n.htmlAttributes?n.htmlAttributes:"htmlAttributes";this.mapFld._checkBy=n&&n.checkBy?n.checkBy:"checkBy";this.mapFld._selectBy=n&&n.selectBy?n.selectBy:"selectBy";this.mapCateg=n&&n.groupBy?n.groupBy:""},_getObjectKey:function(n){var t,i;if(Object.keys)return Object.keys(n);t=[];for(i in n)n.hasOwnProperty(i)&&t.push(i);return t},_itemStyle:function(){var n=ej.isNullOrUndefined(this.model.itemHeight)?this.model.itemHeight:this.model.itemHeight.toString().replace("px",""),t=ej.isNullOrUndefined(this.model.itemHeight)?"":"min-height:"+n+"px;height:"+n+"px";return{style:t}},sort:function(){var n=document.createElement("ul"),s,r,h,u,f,t,i,o;if($(n).append(this.itemsContainer.children()),this.model.fields.groupBy!=null||$(n).find(">.e-ghead").length>0){for(s=0;s<$(n).find(">.e-ghead").length;s++)r=$(n).find(">.e-ghead").eq(0).first().nextUntil(".e-ghead").get(),this._setSortList(n,r);for(h=document.createElement("ul"),u=$(n).clone().find(">.e-ghead").get(),f=0;f<u.length;f++)h.append(u[f]);var e=this._customSort(h,u),c=document.createElement("ul"),l,a=$(n).find("li.e-ghead").get();for(this.model.sortOrder.toLowerCase()=="descending"&&e.reverse(),t=0;t<e.length;t++)for(c.append(e[t]),i=0;i<a.length;i++)if(e[t].textContent==a[i].textContent)for(l=$(n).find(">.e-ghead").eq(i).first().nextUntil(".e-ghead").get(),o=0;o<l.length;o++)c.append(l[o]);this.itemsContainer=$(c)}else r=$(n).children("li").get(),this._setSortList(n,r),this.itemsContainer=$(n)},_customSort:function(n,t){return t.sort(function(n,t){var i=$(n).text().toUpperCase(),r=$(t).text().toUpperCase();return i<r?-1:i>r?1:0}),t},_setSortList:function(n,t){this._customSort(n,t);this.model.sortOrder.toLowerCase()=="descending"&&t.reverse();(this.model.fields.groupBy!=null||$(n).find(">.e-ghead").length>0)&&($(n).append($("<li>").text($(n).find(">.e-ghead").eq(0).text()).addClass("e-ghead")),$(n).find(">.e-ghead").eq(0).remove());$.each(t,function(t,i){$(n).append(i)})},_renderlistContainer:function(){var i,f,d,h,c,t,u,v,y,l,p,w,n,e,o,s,a,b,r,k;if(this.hold=this.touchhold=!1,this.item="",this.startime=0,this.listitemheight=24,i=this.listitems,d=this.model.fields,this.lastScrollTop=-1,this.dummyUl=$(),this.model.enableRTL&&this.listContainer.addClass("e-rtl"),this._wordWrapItems(),this.dataSource()==null||this.dataSource().length<1)h=this.element.parents().last(),this.docbdy=this.model.targetID?h.find("#"+this.model.targetID):h.find("#"+this._id),this.itemsContainer=this.docbdy,this.model.sortOrder!="none"&&this.sort(),this.itemsContainer.children("ol,ul").remove(),this.items=this.itemsContainer.children("li"),this.items.children("img").addClass("e-align"),this.items.children("div").addClass("e-align"),c=parseInt(this.model.itemHeight)+"px",this.model.itemHeight&&$("li").css({"min-height":c,height:c}),this.element.append(this.itemsContainer.children());else if(this.dataSource()!=null&&typeof i[0]!="object")if(this._loadInitialRemoteData&&this.mergeValue&&this.model.virtualScrollMode=="continuous"&&this.model.totalItemsCount)this._loadlist(this.mergeValue);else if(this._loadInitialRemoteData&&this.mergeValue&&this.model.virtualScrollMode=="normal"&&this.model.totalItemsCount){for(this.realUllength=0,this.mergeUl=[],n=0;n<this.mergeValue.length;n++)this.mergeUl.push(ej.buildTag("li",this.mergeValue[n][this.model.fields.text],null,this._itemStyle())[0]);for(this.element.append(this.mergeUl),n=0;n<this.model.totalItemsCount-this.mergeValue.length;n++)this.dummyUl.push(ej.buildTag("li",null,null,this._itemStyle())[0]);this.element.append(this.dummyUl);this._refreshScroller()}else this._loadInitialRemoteData&&this.mergeValue&&!this.model.totalItemsCount&&this._initDataSource(this.dataSource());else if(this._setMapFields(),u=this._savedQueries,this.listContainer.height(this.model.height),this.listitemheight=24,this.model.allowVirtualScrolling){if(this.model.virtualScrollMode=="normal"){if(this.realUllength=0,this.dataSource().length<0&&(v=this._savedQueries.take(parseInt(this.listContainer.height()/this.listitemheight)),r=this,ej.DataManager&&this.dataSource()instanceof ej.DataManager&&(r.listitems=r.dataSource(),y=this.dataSource().executeQuery(v),y.done(function(n){r._trigger("actionBeforeSuccess",n);r.listitems=n.result;r._trigger("actionSuccess",n)}).fail(function(n){r._trigger("actionFailure",n)}).always(function(n){r._trigger("actionComplete",n)}))),this.mergeValue!=t&&!ej.isNullOrUndefined(this.mergeValue)){for(this.mergeUl=[],n=0;n<this.mergeValue.length;n++)l=ej.buildTag("li",this.model.template?"":this.mergeValue[n][this.model.fields.text],null,this._itemStyle())[0],this.model.template&&l.append(this._getTemplatedString(i[n])),this.mergeUl.push(l[0]);this.element.append(this.mergeUl)}for(p=this.model.totalItemsCount?this.mergeValue?this.model.totalItemsCount-this.mergeValue.length:this.model.totalItemsCount:this.listitems.length,n=0;n<p;n++)w=ej.buildTag("li",null,null,this._itemStyle()),this.dummyUl.push(w[0]);this.dummyUl.attr("data-ej-page",0);this.element.append(this.dummyUl)}this._loadInitialData(u,i)}else if(this.mapCateg&&this.mapCateg!="")if(this.model.sortOrder.toLowerCase()!="none"&&(o=ej.Query().sortBy(this.mapFld._text,this.model.sortOrder,!0),t=ej.DataManager(i).executeLocal(o)),u=ej.Query().group(this.mapCateg),this.model.sortOrder.toLowerCase()=="none"&&u.queries.splice(0,1),t=ej.DataManager(i).executeLocal(u),this.dataSource([]),this.datamerged&&this.model.fields.groupBy){for(this.mergeUl=[],n=0;n<this.mergeValue.length;n++)for(this.mergeUl.push(this.mergeValue[n]),e=0;e<t[0].items.length;e++)this.mergeValue[n].category==t[0].items[e].key&&t[0].items[e].items.push(this.mergeUl[n]);for(n=0;n<t[0].items.length;n++)this.dummyUl.push(ej.buildTag("li.e-ghead",t[0].items[n].key)[0]),this._loadlist(t[0].items[n].items),this.dataSource(this.dataSource().concat(t[0].items[n].items))}else for(n=0;n<t.length;n++)this.dummyUl.push(ej.buildTag("li.e-ghead",t[n].key)[0]),this._loadlist(t[n].items),this.dataSource(this.dataSource().concat(this._newList));else if(t=ej.DataManager(i).executeLocal(u),t.length>0){if(this.mergeValue!=t&&!ej.isNullOrUndefined(this.mergeValue))for(this.mergeUl=[],n=0;n<this.mergeValue.length;n++)this.mergeUl.push(ej.buildTag("li",this.mergeValue[n][this.model.fields.text],null,this._itemStyle())[0]),t.push(this.mergeValue[n]);if(this.model.template!=null&&this._loadContent){for(this.model.sortOrder.toLowerCase()!="none"&&(o=ej.Query().sortBy(this.mapFld._text,this.model.sortOrder,!0),i=ej.DataManager(i).executeLocal(o)),n=0;n<i.length;n++)s=this._getField(i[n],this.mapFld._htmlAttributes),a=this._getField(i[n],this.mapFld._id),f=ej.buildTag("li"),s&&s!=""&&f.attr(s),a&&f.attr("id",a),this.model.template&&f.append(this._getTemplatedString(i[n])),this.dummyUl.push(f[0]);this.model.allowVirtualScrolling||this.element.children().remove();b=this.model.virtualScrollMode=="continuous"&&this.mergeValue?this.realUllength+this.mergeValue.length:this.realUllength;this.element.children()[b]==null&&(!this.model.allowVirtualScrolling||this.model.virtualScrollMode==ej.VirtualScrollMode.Continuous)&&this._loadContent&&this.element.append(this.dummyUl)}else this.realUllength=0,this._loadlist(t)}r=this;t&&(this.listitems=t);this._setDimensions();this.listContainer.css({position:"relative",height:""});this.listBoxScroller.css({height:"",width:""});this.model.allowVirtualScrolling==!0&&this.model.virtualScrollMode=="normal"?(this._getLiHeight(),k=this._totalCount*this._liItemHeight,this.element.height(k)):this.model.allowVirtualScrolling==!0&&this.model.virtualScrollMode=="continuous"&&this.element.css("height","auto");this.listContainer.ejScroller({height:this.listContainer.height(),width:0,scrollerSize:20,scroll:function(n){r._onScroll(n)}});this.scrollerObj=this.listContainer.ejScroller("instance");this._setDimensions();ej.isNullOrUndefined(this.display)?this.listContainer.css({display:"none",visibility:"visible"}):this.display=null;this._checkboxHideShow(this.model.showCheckbox)._checkitems()._showResult()},_wordWrapItems:function(){this.model.enableWordWrap?this.listContainer.addClass("e-wrap").removeClass("e-nowrap"):this.listContainer.addClass("e-nowrap").removeClass("e-wrap")},_loadInitialData:function(n,t){var r=n.clone(),u,i;if(this.realUllength=0,r=ej.DataManager&&this.dataSource()instanceof ej.DataManager?r.range(0,parseInt(this.listContainer.height()/this.listitemheight)):r.range(0,this.listitems.length),u=t,this.mergeValue!=u&&this.mergeValue!=undefined&&this.model.virtualScrollMode=="continuous"){for(this.mergeUl=[],i=0;i<this.mergeValue.length;i++)this.mergeUl.push(ej.buildTag("li",this.mergeValue[i][this.model.fields.text],null,this._itemStyle())[0]);this.element.append(this.mergeUl)}(!this.mergeValue||this.mergeValue&&this._loadInitialRemoteData)&&this._loadlist(u)},_loadlist:function(n){var f,p,i,t,y,k,o,r,u;if(this._dummyVirtualUl=[],this._newList=[],this.element!=null){for(f=[],this.model.sortOrder.toLowerCase()!="none"&&(p=ej.Query().sortBy(this.mapFld._text,this.model.sortOrder,!0),n=ej.DataManager(n).executeLocal(p)),i=0;i<n.length;i++){var w=this._getField(n[i],this.mapFld._id),h=this._getField(n[i],this.mapFld._imageUrl),c=this._getField(n[i],this.mapFld._imageAttributes),l=this._getField(n[i],this.mapFld._spriteCSS),e=this._getField(n[i],this.mapFld._text),s=this._getField(n[i],this.mapFld._value),a=this._getField(n[i],this.mapFld._htmlAttributes),d=this._getField(n[i],this.mapFld._selectBy),g=this._getField(n[i],this.mapFld._checkBy),v=this._getField(n[i],this.mapFld._tooltipText),b=this.model.virtualScrollMode=="continuous"&&this.mergeValue?this.realUllength+this.mergeValue.length:this.realUllength;t=ej.isNullOrUndefined(s)||s==""&&s!=0?ej.buildTag("li",null,null,this._itemStyle()):ej.buildTag("li","","",$.extend(this._itemStyle(),{value:s}));w&&t.attr("id",w);h&&h!=""&&(y=ej.buildTag("img.e-align","",{},{src:h,alt:e}),c&&c!=""&&y.attr(c),t.append(y));l&&l!=""&&(k=ej.buildTag("div.e-align "+l+" sprite-image"),t.append(k));ej.isNullOrUndefined(e)||(this.model.template?t.append(this._getTemplatedString(n[i])):e==!1?t.append(document.createTextNode(e)):t.append(e));a&&a!=""&&t.attr(a);v&&v!=""&&t.attr("data-content",v).addClass("e-tooltip");(g||this.model.checkAll)&&t.addClass("checkItem");(d||this.model.selectAll)&&t.addClass("selectItem");this.model.allowVirtualScrolling&&this.model.virtualScrollMode=="normal"?($(t[0]).attr("data-ej-page",0),$(this.dummyUl[b]).replaceWith(t[0]),this._dummyVirtualUl.push(t[0])):this.dummyUl.push(t[0]);this.realUllength+=1}if(this.model.allowVirtualScrolling||this.element.children().remove(),this.element.children()[b]==null&&(!this.model.allowVirtualScrolling||this.model.virtualScrollMode==ej.VirtualScrollMode.Continuous)&&this._loadContent&&this.element.append(this.dummyUl),o=this.element.find("li:not('.e-ghead')"),this.listItemsElement=this.element.find("li:not('.e-ghead')"),this.model.showCheckbox&&this.model.checkedIndices)for(r=0;r<o.length;r++)this.model.checkedIndices.indexOf(r)!=-1&&$(o[r]).addClass("checkItem");else if(!this.model.showCheckbox)for(this.value()==""||this.mapCateg||!this.mapCateg==""||this.selectItemByText(this.value()),r=0;r<o.length;r++)(this.model.selectedIndices.indexOf(r)!=-1||this.model.selectedIndex==r)&&$(o[r]).addClass("selectItem");this.element.find(".selectItem").each(function(n,t){f.push($(t).parent().find("li").index($(t)))});u=this;u.model.showCheckbox||this.mapCateg||!this.mapCateg==""||u._selectListItems();this.element.find(".checkItem").each(function(n,t){u.model.checkedIndices.push(u._elementIndex(t))});this.mapCateg||!this.mapCateg==""||(f.length&&(this.model.allowMultiSelection?this.model.selectedIndices=f:this.model.selectedIndex=f[0]),this.model.checkedIndices?this.model.checkedIndices=$.grep(u.model.checkedIndices,function(n,t){return t==$.inArray(n,u.model.checkedIndices)}):this.model.selectedIndices&&(this.model.selectedIndices=$.grep(u.model.selectedIndices,function(n,t){return t==$.inArray(n,u.model.selectedIndices)})));this._loadContent=!0}return this._newList=n,this.model.sortOrder.toLowerCase()!="none"&&this.dataSource()&&!this.mapCateg&&this.dataSource(this._newList),this},_applySelection:function(){if(!(this.model.fields.checkBy||this.model.fields.selectBy))return!1;this.model.showCheckbox?(this.uncheckAll(),this.checkItemsByIndices(this.model.checkedIndices)):this.model.allowMultiSelection?this.selectItemsByIndices(this.model.selectedIndices):(this.unselectAll(),this.selectItemByIndex(this.model.selectedIndex))},_getField:function(n,t){return ej.pvt.getObject(t,n)},_getTemplatedString:function(n){for(var t=this.model.template,i=t.indexOf("${"),r=t.indexOf("}"),u,f;i!=-1&&r!=-1;)u=t.substring(i,r+1),f=u.replace("${","").replace("}",""),t=t.replace(u,this._getField(n,f)),i=t.indexOf("${"),r=t.indexOf("}");return t},_checkboxHideShow:function(n){return this.model.showCheckbox=n,n?this._createCheckbox():this._removeCheckbox(),this},_createCheckbox:function(){var u,f=this,i,t,r,n;for(this._listitems=this.listContainer.find("ol,ul").length>0?this.listContainer.find("ol,ul").children("li:not('.e-ghead')"):this.element.children("li:not('.e-ghead')"),u=this._listitems.find("input[type=checkbox]"),n=0;n<this._listitems.length;n++)$(this._listitems[n]).text()!=""&&(i=ej.buildTag("input.listcheckbox e-align#popuplist"+n+"_"+this._id,"",{},{type:"checkbox",name:"list"+n}),$(this._listitems[n]).find("input[type=checkbox]").length||$(this._listitems[n]).prepend(i));for(this.listContainer.find(".listcheckbox").ejCheckBox({cssClass:this.model.cssClass,change:$.proxy(this._onClickCheckList,this)}),n=0;n<this._listitems.length;n++)t=$(this._listitems[n]).find(".listcheckbox"),$(this._listitems[n]).hasClass("e-disable")?t.ejCheckBox("disable"):$(this._listitems[n]).hasClass("checkItem")&&!t.ejCheckBox("isChecked")&&(t.ejCheckBox({checked:!0}),this._activeItem=n,this.checkedStatus=!0,r=this._getItemObject($(this._listitems[n]),null),r.isInteraction=!0,$(this._listitems[n]).removeClass("checkItem"));for(n=0;n<this.model.selectedIndices.length;n++)this.checkItemsByIndices(this.model.selectedIndices)},_removeCheckbox:function(){var n,t;if(this.listitem=this.listContainer.find("ol,ul").children("li"),t=this.listitem.find(".listcheckbox"),t.length>0){if(this.listitem.find(".listcheckbox").ejCheckBox("destroy"),this.listitem.find("input[type=checkbox]").remove(),this.model.allowMultiSelection)for(n=0;n<this.model.checkedIndices.length;n++)this.selectItemsByIndices(this.model.checkedIndices);else this.selectItemByIndex(this.model.checkedIndices[0]);this._checkedItems=this.model.checkedIndices=[]}},_selectCheckedItem:function(n){if(n.length>0)for(var t=0;t<n.length;t++)this._selectedItems.push(n[t])},_refreshScroller:function(){if(this.model.virtualScrollMode=="continuous")this.listContainer.css({display:"block"}),this.scrollerObj&&(this.scrollerObj.model.height=this.listContainer.height(),this.scrollerObj.refresh());else{this.listContainer.find(".e-vhandle div").removeAttr("style");var n=this.listBoxScroller.height();this.listContainer.css({display:"block"});this.scrollerObj&&(this.scrollerObj.model.height=this.listContainer.css("height"),this.scrollerObj.refresh());this.listBoxScroller.css("height","100%")}this.model.enabled||this.scrollerObj&&this.scrollerObj.disable();this.listContainer.css("height",this.model.height)},_setDimensions:function(){return this.listContainer.css({width:this.model.width,height:this.model.height}),this._refreshScroller(),this},_setItemsCount:function(){return this.model.height=="auto"?this.model.height=this.model.itemsCount&&this.model.itemsCount!=0&&this.model.height=="auto"?this.model.itemsCount*30:this.model.height=="auto"?"220":this.model.height:this.model.height!="auto"&&this.model.itemsCount?this.model.height=this.model.itemHeight?this.model.height=="auto"?"220":this.model.itemsCount*this.model.itemHeight.replace(/[^-\d\.]/g,""):this.model.height=="auto"?"220":this.model.itemsCount*30:this.model.height!="auto"&&this.model.itemsCount!=0&&this.model.height,this},_setTotalItemsCount:function(){this.model.virtualScrollMode!="continuous"&&(this.element.height(this.element.find("li").outerHeight()*this.model.totalItemsCount),this.scrollerObj.refresh())},_refreshContainer:function(){this.listContainer.css({position:"relative"});this._setDimensions()._roundedCorner()._refreshScroller()},_drag:function(){var n=this,i=!1,t=null;this._listitem=this.element.parent();this._lilist=this._addItemIndex?$($(this._listitem).find("li")[this._addItemIndex]):$(this._listitem).find("li");this._lilist.not(".e-js").ejDraggable({dragArea:null,clone:!0,dragStart:function(i){var u,r;if(n.model.allowDrag||n.model.allowDragAndDrop){if($(i.element.closest(".e-ddl-popup.e-js")).hasClass("e-disable")||i.element.hasClass("e-disable"))return t&&t.remove(),!1;if(u=$("#"+this.element.parent()[0].id).data("ejListBox"),u._refreshItems(),r=n.getSelectedItems(),r.length>1?n._onDragStarts(r,i.target):n._onDragStarts([n._getItemObject(i.element,i)],i.target))return i.cancel=!0,t&&t.remove(),!1}else return!1},drag:function(t){var i=t.target,r=n.getSelectedItems();if(r.length>1?n._onDrag(r,i):n._onDrag([n._getItemObject(t.element,t)],i))return!1;($(i).hasClass("e-droppable")||$(i).parent().hasClass("e-droppable"))&&$(i).addClass("allowDrop")},dragStop:function(r){if(r.element.dropped||t&&t.remove(),!$(r.target).closest(".e-js.e-widget").hasClass("e-disable")){var u=r.target,e=n,o=i?"Before":"After",f=n.getSelectedItems();if((f.length>1?n._onDragStop(f,u):n._onDragStop([n._getItemObject(r.element,r)],u))||($(r.element).removeClass("e-active"),u.nodeName=="UL"&&(u=$(u)[0]),$(u).closest("li").length?u=$(r.target).closest("li")[0]:u.nodeName!="LI"&&(u=$(u).closest(".e-ddl-popup.e-droppable")[0]),u&&u.nodeName=="LI"&&$(u).hasClass("e-droppable")&&$(u).closest(".e-ddl-popup.e-droppable").length?n._dropItem(u,r.element,i,r.event):$(u).hasClass("e-droppable")&&$(u).closest(".e-ddl-popup.e-droppable").length&&n._dropItemContainer(u,r.element,r.event),$(".allowDrop").removeClass("allowDrop"),r.target!=n.element[0]&&r.element.parent().length&&$(r.element.parent()[0]).data().ejWidgets[0]=="ejListBox"&&(n=$("#"+r.element.parent()[0].id).data($(r.element.parent()[0]).data().ejWidgets[0]),f.length>1?n._onDropped(f,u,r):n._onDropped(n._getItemObject(r.element),r.target,r))))return!1}n.model.allowDrag||n.model.allowDragAndDrop||n.element.children().removeClass("e-draggable")},helper:function(i){if(!ej.isNullOrUndefined(i.element)&&!$(i.element.closest(".e-ddl-popup.e-js")).hasClass("e-disable")&&$(i.element).hasClass("e-draggable")&&(n=$(i.element).closest(".e-listbox.e-js").data("ejListBox"),n._tempTarget=$(i.element).text(),(n.model.allowDrag||n.model.allowDragAndDrop)&&n)){t=$(i.sender.target).clone().addClass("dragClone e-dragClonelist");t.addClass(n.model.cssClass+(n.model.enableRTL?" e-rtl":""));var r=ej.util.getZindexPartial(n.element);return t.css({width:n.element.width(),height:$(i.element).height(),padding:"5px 5px 5px 0.857em","list-style":"none","text-align":n.model.enableRTL?"right":"left",opacity:"1","z-index":r}),t.appendTo($("body"))}}})},_dropItem:function(n,t,i,r){var e,s,h,c;t.addClass("e-droppable");var l=$(n).closest(".e-ddl-popup.e-droppable")[0].id.replace("_container",""),o=[],f=[],u=$("#"+l).data("ejListBox"),a=u.model.showCheckbox?!this.model.showCheckbox:this.model.showCheckbox;a||(e=this._getDropObject(n,t,r),o=e.dataIndex,f=e.dataObj,s=u.element.find("li"),h=s.index(n),h==0&&(i=!0),i?$(this.li).insertBefore(n):$(this.li).insertAfter(n),this._refreshItems(),c=$(this.li.parent()[0]).find("li:not('.e-ghead')"),f&&this.dataSource()&&this._dropDataSource(u,o,f,c.index(this.li)),u._refreshItems())},_dropItemContainer:function(n,t,i){t.addClass("e-droppable");var o=$(n)[0].id.replace("_container",""),r=$("#"+o).data("ejListBox"),s=r.model.showCheckbox?!this.model.showCheckbox:this.model.showCheckbox;if(!s){var f=[],u=[],e=this._getDropObject(n,t,i);f=e.dataIndex;u=e.dataObj;this.li.insertAfter($($(n).find("li")).last());$(n).find("ul").length>0?$(n).find("ul").append(this.li):$(n).find("ej-listbox").append(this.li);this._refreshItems();u&&this.dataSource()&&this._dropDataSource(r,f,u,r.dataSource()?r.dataSource().length:0);r.model.allowDrag||$(this.li).ejDraggable("instance")._destroy();r._refreshItems()}},_dropDataSource:function(n,t,i,r){var f=ej.DataManager&&this.dataSource()instanceof ej.DataManager,u;f||(t instanceof Array?(u=this,$.each(i,function(n){var t=u.dataSource().indexOf(i[n]);u.dataSource().splice(t,1)})):this.dataSource().splice(t,1),n.dataSource()instanceof Array?n.dataSource().splice.apply(n.dataSource(),[r,0].concat(i)):n.dataSource(i))},_getDropObject:function(n,t){var r=[],u=[],i,f;return this.model.allowMultiSelection?(this.li=$(t).parent().find(".e-select").removeClass("e-select e-hover"),this.li.index(t[0])==-1&&(this.li=t),this.li.length||(this.li=t.removeClass("e-select e-hover"))):this.li=t.removeClass("e-select e-hover"),this.li.length?(i=this,f=this.model.sortOrder.toLowerCase(),$.each(this.li,function(){var n=$(this.parentElement).find("li:not('.e-ghead')"),t,e;r.push(n.index(this));f!="none"?(t=ej.Query().sortBy(i.mapFld._text,f,!0),e=ej.DataManager(i.dataSource()).executeLocal(t),u.push(i.dataSource()?e[n.index(this)]:null)):u.push(i.dataSource()?i.dataSource()[n.index(this)]:null)})):(r=this.li.index(),u=this.dataSource()?this.dataSource()[r]:null),{dataIndex:r,dataObj:u}},_showResult:function(){var t=this,n;this._refreshContainer();this.element.attr({"aria-expanded":!0});n=this.element.children("li:not('.e-ghead')");this._listSize=n.length;this._ListEventUnbind(n);n.on("touchstart mouseenter",$.proxy(this._OnMouseEnter,this));n.on("touchend mouseleave",$.proxy(this._OnMouseLeave,this));n.on("click",$.proxy(this._OnMouseClick,this));n.on("contextmenu",$.proxy(this._OnMouseContext,this));return t.model.showCheckbox&&t.element.find(".listcheckbox").ejCheckBox({enabled:t.model.enabled}),this},_OnWindowResize:function(){this._refreshContainer();this.listContainer.css("display","block")},refresh:function(){ej.isNullOrUndefined(this.model.query)||(this._savedQueries=this.model.query);this.display=!0;this.model.dataSource?(this.model.template&&this.element.empty(),this._checkModelDataBinding(this.dataSource())):(this.listContainer.css({height:this.model.height,width:this.model.width}),this._refreshScroller())},_removeListHover:function(){return this._selectedItems=[],this.model.selectedIndices=[],this.model.selectedIndex=null,this.element.children("li").removeClass("e-hover e-select selectItem"),this},_addListHover:function(){this._activeItem=this._selectedItem;var n=this._getItem(this._selectedItem);n.addClass("e-select e-hover");this.scrollerObj.setModel({scrollTop:this._calcScrollTop()});n.focus();this._OnListSelect(this.prevselectedItem,this._selectedItem)},_calcScrollTop:function(n){for(var f=this.element.outerHeight(),r=this.element.find("li"),u=0,i=n?n:this.element.find("li.e-select").index(),t=0;t<i;t++)u+=r.eq(t).outerHeight();return u-(this.listContainer.outerHeight()-r.eq(i).outerHeight())/2},_refreshItems:function(){this.listBoxScroller.append(this.element);this.listContainer.append(this.listBoxScroller);this._refreshContainer();this._showResult();this._setSelectionValues();this._setDisableValues()},_selectedIndices:function(){var n;return this.element.children("li:not('.e-ghead')").each(function(t){if($(this).hasClass("e-select"))return n=t,!1}),this._selectedItem=n,n},_addSelectedItem:function(n){(!Array.isArray(this.model.disableItemsByIndex)&&this.model.disableItemsByIndex!=null||Array.isArray(this.model.disableItemsByIndex)&&this.model.disableItemsByIndex.length>0)&&(n.keyCode==40||n.keyCode==39?this._disableItemSelectDown():this._disableItemSelectUp(),this._selectedItem=this._activeItem);var t=this._getItem(this._selectedItem);this._selectedItems.push(t)},_getItem:function(n){return $(this.element.children("li:not('.e-ghead')")[n])},_getItemObject:function(n,t){var i=this._elementIndex(n);return{item:n,index:i,text:n.text(),value:n.attr("value")?n.attr("value"):n.text(),isEnabled:!n.hasClass("e-disable"),isSelected:n.hasClass("e-select"),isChecked:n.find(".e-chk-image").hasClass("e-checkmark"),data:this.dataSource()?this.getListData()[i]:null,event:t?t:null}},_roundedCorner:function(){return this.listContainer[this.model.showRoundedCorner?"addClass":"removeClass"]("e-corner-all"),this},_enabled:function(n){return n?this.enable():this.disable(),this},_showFullList:function(){return this.dataSource()!=null&&(ej.DataManager&&this.dataSource()instanceof ej.DataManager||(this.listitems=this.dataSource()),!this._savedQueries.queries.length||ej.DataManager&&this.dataSource()instanceof ej.DataManager||(this.listitems=ej.DataManager(this.dataSource()).executeLocal(this._savedQueries))),this._renderlistContainer(),this.dataSource()instanceof ej.DataManager||this._trigger("actionComplete"),this._addDragableClass()._enableDragDrop(),this._disabledItems=[],this.disableItemsByIndices(this.model.disableItemsByIndex),this.model.selectedIndex==0?this.selectItemByIndex(this.model.selectedIndex):this.model.selectedIndex&&this.selectItemByIndex(this.model.selectedIndex),this.selectItemsByIndices(this.model.selectedIndices),this.checkItemsByIndices(this.model.checkedIndices),this._tooltipList(),this},_tooltipList:function(){this.listContainer.find("li").hasClass("e-tooltip")&&$(this.listContainer).ejTooltip({target:".e-tooltip",isBalloon:!1,position:{target:{horizontal:"center",vertical:"bottom"},stem:{horizontal:"left",vertical:"top"}}})},_cascadeAction:function(){if(this.model.cascadeTo){this._currentValue=this._getField(this.listitems[this._activeItem],this.mapFld._value);this.selectDropObj=$("#"+this.model.cascadeTo).ejListBox("instance");$.extend(!0,this.selectDropObj,{_isCasCadeTarget:!0});ej.isNullOrUndefined(this._dSource)&&(this._dSource=this.selectDropObj.dataSource());this._performJsonDataInit();var n={cascadeModel:this.selectDropObj.model,cascadeValue:this._currentValue,setCascadeModel:{},requiresDefaultFilter:!0};this._trigger("cascade",n);this.selectDropObj._setCascadeModel=n.setCascadeModel}},_performJsonDataInit:function(){this._changedSource=ej.DataManager(this._dSource).executeLocal(ej.Query().where(this.mapFld._value,"==",this._currentValue));this.selectDropObj.setModel({dataSource:this._changedSource,enable:!0,value:"",selectedIndex:-1})},_OnMouseContext:function(n){return n.preventDefault(),!1},_OnMouseEnter:function(n){var t,i,r;this.startime=0;this.item="";n.type=="touchstart"&&(this.item=$(n.target).text(),this.startime=(new Date).getTime());this.model.enabled&&(this.element.children("li").removeClass("e-hover"),$(n.target).is("li")&&$(n.target).addClass("e-hover"),$(n.target).hasClass("e-disable")?$(n.target).removeClass("e-hover"):n.target.tagName!="li"&&(t=$(n.target).parents("li"),$(t).addClass("e-hover")),r=0,this.element.children("li:not('.e-ghead')").each(function(n){if($(this).hasClass("e-hover"))return i=n,!1}),this._hoverItem=i)},_OnMouseLeave:function(n){this.element.children("li").removeClass("e-hover");this.endtime=(new Date).getTime();(this.endtime-this.startime)/200>2&&this.item==$(n.target).text()&&(this.hold=(this.endtime-this.startime)/200>2?!this.hold:!1)},_OnMouseClick:function(n){var t,u,f,r,i,o,e;if($(n.currentTarget).hasClass("e-disable"))return!1;if(n.which==3&&(this.hold=!0),this.endtime=(new Date).getTime(),(this.endtime-this.startime)/200>2&&(this.model.template||this.item!=$(n.target).text()||this.hold||(this.hold=(this.endtime-this.startime)/200>2)),n.shiftKey&&this._shiftkey&&(this._shiftkey=!1,this.prevselectedItem=this._activeItem),ej.isNullOrUndefined(this._hoverItem)||(this._activeItem=this._hoverItem),this.model.enabled&&this._activeItem!=undefined){if((!n.shiftKey||isNaN(this.prevselectedItem))&&(this._shiftkey=!0,this.prevselectedItem=this._lastEleSelect?this._lastEleSelect:this._activeItem,this._lastEleSelect==0&&(this.prevselectedItem=this._lastEleSelect)),this.model.showCheckbox){if($(n.currentTarget).is("li")&&$(n.target).is("li"))$(n.currentTarget.firstChild).find(".listcheckbox").ejCheckBox("isChecked")?($(n.currentTarget.firstChild).find(".listcheckbox").ejCheckBox("option","checked",!1),i=this.model.checkedIndices.indexOf($(n.currentTarget).index()),this._checkedItems.splice(i,1),this.model.checkedIndices.splice(i,1),this.checkedStatus=!1):($(n.currentTarget.firstChild).find(".listcheckbox").ejCheckBox("option","checked",!0),this._checkedItems.push(this._activeItem),this.model.checkedIndices.push(this._elementIndex(n.currentTarget)),this.checkedStatus=!0);else if($(n.currentTarget).is("li")&&$(n.target).is("span"))$(n.currentTarget.firstChild).find(".listcheckbox").ejCheckBox("isChecked")?(this._checkedItems.push(this._activeItem),this.model.checkedIndices.push($(n.currentTarget).index()),this.checkedStatus=!0):(i=this.model.checkedIndices.indexOf($(n.currentTarget).index()),this._checkedItems.splice(i,1),this.model.checkedIndices.splice(i,1),this.checkedStatus=!1);else return!1;this.selectedTextValue=$(n.currentTarget).text();!this.element.hasClass("e-disable")&&$(n.target).is("li")&&(o={status:this.model.enabled,isChecked:this.checkedStatus,selectedTextValue:this.selectedTextValue},e=this._getItemObject($(n.target),n),e.isInteraction=!0,this._trigger("checkChange",e));this._lastEleSelect=$(n.currentTarget).index()}else t=$(this.element.children("li:not('.e-ghead')")[this._hoverItem]),this.model.allowMultiSelection&&(n.ctrlKey||this.touchhold||this.hold||n.shiftKey)||this._removeListHover(),this.element.children("li").removeClass("e-hover"),!t.hasClass("e-select")||n.shiftKey&&this.model.allowMultiSelection?(t.addClass("e-select"),this._selectedItems.push(t),this.model.selectedIndices.push(this._activeItem),n.shiftKey&&this.model.allowMultiSelection&&(n.ctrlKey||this._removeListHover(),this.prevselectedItem<this._activeItem?(u=this.prevselectedItem,f=this._activeItem):(u=this._activeItem,f=this.prevselectedItem),this._activeItemLoop(u,f))):(t.removeClass("e-select"),this._selectedItems.splice(this.model.selectedIndices.indexOf(this._activeItem),1),this.model.selectedIndices.splice(this.model.selectedIndices.indexOf(this._activeItem),1)),this._selectedItem=this._selectedIndices(),this.model.selectedIndex=this._activeItem,this._cascadeAction(),r=$(this.element.children("li:not('.e-ghead')")[this._selectedItem]),$(r).text()!=""&&(this.element.val($(r).text()),this.element.attr({value:this.element.val()})),this.model.selectedText=t.text(),this._selectedData=this._getItemObject($(r),n),this._selectedData.isInteraction=!0,this._prevSelectedData&&this._selectedData.text!=this._prevSelectedData.text&&this._trigger("unselect",this._prevSelectedData),this._trigger("select",this._selectedData),this._prevSelectedData=this._selectedData,this._lastEleSelect=this._activeItem,this._selectedItems&&this._selectedItems.length!=1&&(this._ctrlClick=!0);n.ctrlKey||n.shiftKey?n.shiftKey?(this._shiftSelectItem=this._activeItem,this._ctrlSelectItem=null):(this._ctrlSelectItem=this._activeItem,this._shiftSelectItem=null):(this._shiftSelectItem=null,this._ctrlSelectItem=null);this._setSelectionValues()._OnListSelect(this.prevselectedItem,this._activeItem)}n.target.nodeName!="INPUT"&&this.listContainer.focus();this._pageUpStep=this._pageDownStep=null},_activeItemLoop:function(n,t){var u,i,r;for(this.model.showCheckbox&&(u=this.listContainer.find("li:not(.e-disable)"),u.find(".listcheckbox").ejCheckBox("option","checked",!1),this._checkedItems=[],this.model.checkedIndices=[]),i=n;i<=t;i++)this.model.showCheckbox&&!this.listContainer.find("li").eq(i).hasClass("e-disable")?(this.element.find(".listcheckbox").eq(i).ejCheckBox("option","checked",!0),this._checkedItems.push(i),this.model.checkedIndices.push(i),this.checkedStatus=!0):(r=$(this.element.children("li:not('.e-ghead')")[i]),r.hasClass("e-disable")||(r.hasClass("e-select")||r.addClass("e-select"),this._selectedItems.push(r),this.model.selectedIndices.push(i)))},_setSelectionValues:function(){var i=[],r=this.model.selectedIndices,u=this.model.checkedIndices,t,o,n,f,e;if(this.model.selectedIndices=[],this.model.checkedIndices=[],t=this,this.model.showCheckbox?this.element.find("li:not('.e-ghead') .listcheckbox:checked").closest("li").each(function(n,r){i.push($(r).attr("value")?$(r).attr("value"):!ej.isNullOrUndefined(t.model.fields.text)&&t.dataSource()?t.getListData()[t._elementIndex(r)][t.model.fields.text]:$(r).text());t.model.checkedIndices.push(t._elementIndex(r))}):(!ej.isNullOrUndefined(this._activeItem)&&this._activeItem>=0&&(this.model.selectedIndex=this._activeItem),o=this.element.children("li:not('.e-ghead')"),this.element.children("li:not('.e-ghead').e-select").each(function(n,r){i.push($(r).attr("value")?$(r).attr("value"):!ej.isNullOrUndefined(t.model.fields.text)&&t.dataSource()?t.getListData()[t._elementIndex(r)][t.model.fields.text]:$(r).text());t.model.selectedIndices.push(o.index(r))})),ej.DataManager&&ej.DataManager&&this.dataSource()instanceof ej.DataManager&&this.model.allowVirtualScrolling)if(this.model.showCheckbox)for(n=0;n<u.length;n++)this.model.checkedIndices.indexOf(u[n])==-1&&this.model.checkedIndices.push(u[n]);else for(n=0;n<r.length;n++)this.model.selectedIndices.indexOf(r[n])==-1&&this.model.selectedIndices.push(r[n]);if(this.model.selectedItemIndex=this.model.selectedIndex,this.model.selectedItems=this.model.selectedItemlist=this.model.selectedIndices,this.model.checkedItems=this.model.checkedItemlist=this.model.checkItemsByIndex=this.model.checkedIndices,this.model.text="",this.model.showCheckbox)for(f=this.getCheckedItems(),n=0;n<f.length;n++)this.model.text+=f[n].text+",";else for(e=this.getSelectedItems(),n=0;n<e.length;n++)this.model.text+=e[n].text+",";return this.value(i.toString()),this._hiddenInput.val(this.value()),this},_setDisableValues:function(){var t,n;for(this._disabledItems=[],this.model.disableItemsByIndex=[],t=this.element.children().length,n=0;n<t;n++)$(this.element.children()[n]).hasClass("e-disable")&&this.model.disableItemsByIndex.push(n);this.disableItemsByIndices(this.model.disableItemsByIndex)},_onClickCheckList:function(n){if(n.isChecked||$("#"+n.model.id).closest("li").removeClass("checkItem"),n.isInteraction&&(this.checkedStatus=n.isChecked?!0:!1,!this._initValue)){this.checkedStatus?this.model.checkedIndices.push($(n.event.target).closest('li:not(".e-ghead")').index()):this.model.checkedIndices.splice($.inArray($(n.event.target).closest('li:not(".e-ghead")').index(),this.model.checkedIndices),1);var t=this._getItemObject($(n.event.target).closest("li"),n);t.isInteraction=!0;this._trigger("checkChange",t)}},_elementIndex:function(n){return $(n).parent().children("li:not('.e-ghead')").index(n)},_disableItemSelectCommon:function(){this.listitems=this.element.find("li");this._activeItem=this.listitems.index(this.element.find(".e-select"))},_disableItemSelectUp:function(){var n,t;for(this._disableItemSelectCommon(),n=typeof this.model.disableItemsByIndex!="object"?this.model.disableItemsByIndex.split(",").sort().reverse():this.model.disableItemsByIndex,this._activeItem==0?this._activeItem=this.listitems.length-1:this._activeItem--,t=0;$.inArray(this._activeItem.toString(),n.toString())>-1;t++)this._activeItem--,this._activeItem<0&&(this._activeItem=this.listitems.length-1);$(this.element.children("li")[this._activeItem]).addClass("e-select")},_disableItemSelectDown:function(){var n,t;for(this._disableItemSelectCommon(),n=typeof this.model.disableItemsByIndex!="object"?this.model.disableItemsByIndex.split(",").sort():this.model.disableItemsByIndex,this.listitems.length-1==this._activeItem?this._activeItem=0:this._activeItem++,t=0;$.inArray(this._activeItem.toString(),n.toString())>-1;t++)this._activeItem++,this.listitems.length==this._activeItem&&(this._activeItem=0);$(this.element.children("li")[this._activeItem]).addClass("e-select")},_checkitems:function(){var i,n,t;if(this.model.showCheckbox)for(i=this.element.find("li:not('.e-ghead')"),n=0;n<this.model.checkedIndices.length;n++)t=this.model.checkedIndices[n],$(i[t]).find(".listcheckbox").ejCheckBox("option","checked",!0),this._checkedItems.push(i[t]);else if(this.model.allowMultiSelection)for(n=0;n<this.model.selectedIndices.length;n++)t=this.model.selectedIndices[n],$(this.listitem[t]).hasClass("e-select")||($(this.listitem[t]).addClass("e-select"),this._selectedItems.push($(this.listitem[t])));else $(this.listitem[this.model.selectedIndex]).hasClass("e-select")||$(this.listitem[this.model.selectedIndex]).addClass("e-select"),this._selectedItems.push($(this.listitem[this.model.selectedIndex]));return this._setSelectionValues(),this},_onlistselection:function(n,t,i){if(n!=t){var r=this._getItemObject($(this.element.find("li:not('.e-ghead')")[t]),i);r.isInteraction=!0;ej.isNullOrUndefined(r.event)||this._trigger("change",r)}},_OnListSelect:function(n,t,i){if(!ej.isNullOrUndefined(n)&&n!=t&&!this.model.showCheckbox){var r=this._getItemObject($(this.element.find("li:not('.e-ghead')")[t]),i);r.isInteraction=!0;this._trigger("change",r)}},_OnKeyDown:function(n){var r,v,c,l,a,e,u,s,i,o,h,f,t;if(this.model.enabled){this._selectedItems&&this._selectedItems.length==1&&!this.model.showCheckbox&&(this._lastEleSelect=$(this.element.children("li.e-select")).index());this._itemId=null;r=this.element.children("li:not('.e-ghead')");v=this;l=this.listContainer.height();c=r.outerHeight();a=Math.round(l/c)!=0?Math.floor(l/c):7;this._listSize=this.element.children("li").length;n.shiftKey||(this._up=this._down);n.keyCode!=33&&n.keyCode!=34&&(this._pageUpStep=this._pageDownStep=null);switch(n.keyCode){case 37:case 38:if(s=this.listItemsElement,i=this._shiftSelectItem?this._shiftSelectItem:this._ctrlSelectItem?this._ctrlSelectItem:this.model.showCheckbox?this._lastEleSelect||0:s.index(this.element.find("li.e-select")),n.shiftKey&&this.model.allowMultiSelection&&!this.model.showCheckbox){if(this._lastEleSelect==0)return!1;for(this._lastEleSelect=this._ctrlClick?this._lastEleSelect-1:this._lastEleSelect,i=this._lastEleSelect,this._selectedItem=i||i==0?i==0?this._listSize-1:this._down?i:i-1:0,t=this._selectedItem;$(r[t]).hasClass("e-disable");t--)this._selectedItem-=1;if($(r[this._selectedItem]).hasClass("e-select")&&this.element.find("li.e-select").length==1&&(this._selectedItem-=1),o=$(r[this._selectedItem]),o.hasClass("e-select")){if(this._selectedItem==0)return;o.removeClass("e-select");this._selectedItems.pop()}else o.addClass("e-select"),this._selectedItems.push(o);this.scrollerObj.setModel({scrollTop:this._calcScrollTop(this._selectedItem)});this._up=!0;this._down=!1;this._ctrlClick=!1}else{for(this._selectedItem=i||i==0?i==0?this._listSize-1:i-1:0,t=this._selectedItem;$(r[t]).hasClass("e-disable");t--)this._selectedItem-=1;this._selectedItem==-1&&(this._selectedItem=this._listSize-1);this._addSelectedItem(n);$(r).removeClass("e-hover e-select");h=this.model.showCheckbox?"e-hover":"e-select";$(r[this._selectedItem]).addClass(h);this.scrollerObj.setModel({scrollTop:this._calcScrollTop(this._selectedItem)})}return this._activeItem=this.prevselectedItem=this._selectedItem,this._OnListSelect(this._selectedItem+1,this._selectedItem,n),this._lastEleSelect=this._selectedItem,this._keyCascade(r[this._selectedItem]),this._setSelectionValues(),this._shiftSelectItem=this._ctrlSelectItem=null,n.preventDefault(),!1;case 39:case 40:if(s=this.listItemsElement,i=this._shiftSelectItem?this._shiftSelectItem:this._ctrlSelectItem?this._ctrlSelectItem:this.model.showCheckbox?this._lastEleSelect||0:s.index(this.element.find("li.e-select")),n.shiftKey&&this.model.allowMultiSelection&&!this.model.showCheckbox){if(this._lastEleSelect==this._listSize-1)return!1;for(this._lastEleSelect=this._ctrlClick?this._lastEleSelect+1:this._lastEleSelect,i=this._lastEleSelect,this._selectedItem=i||i==0?i==this._listSize-1?0:this._up||this._ctrlClick?i:i+1:0,t=this._selectedItem;$(r[t]).hasClass("e-disable");t++)this._selectedItem+=1;$(r[this._selectedItem]).hasClass("e-select")&&this.element.find("li.e-select").length==1&&(this._selectedItem+=1);o=$(r[this._selectedItem]);o.hasClass("e-select")?(o.removeClass("e-select"),this._selectedItems.pop()):(o.addClass("e-select"),this._selectedItems.push(o));this.scrollerObj.setModel({scrollTop:this._calcScrollTop(this._selectedItem)});this._up=!1;this._down=!0;this._ctrlClick=!1}else{for(this._selectedItem=i||i==0?i==this._listSize-1?0:i+1:0,t=this._selectedItem;$(r[t]).hasClass("e-disable");t++)this._selectedItem+=1;this._selectedItem==this._listSize&&(this._selectedItem=0);this._addSelectedItem(n);$(r).removeClass("e-hover e-select");h=this.model.showCheckbox?"e-hover":"e-select";$(r[this._selectedItem]).addClass(h);this.scrollerObj.setModel({scrollTop:this._calcScrollTop(this._selectedItem)});this.element.find("li").removeClass("selectItem");this.model.selectedIndices.length=0;this.model.selectedIndices.push(this._selectedItem)}return this._activeItem=this.prevselectedItem=this._selectedItem,this._OnListSelect(this._selectedItem-1,this._selectedItem),this._lastEleSelect=this._selectedItem,this._keyCascade(r[this._selectedItem]),this._setSelectionValues(),this._shiftSelectItem=this._ctrlSelectItem=null,!1;case 8:case 9:case 13:this.model.showCheckbox&&(this.model.checkedIndices.indexOf(this._selectedItem)<0?this.checkItemByIndex(this._selectedItem):this.uncheckItemByIndex(this._selectedItem));break;case 18:case 33:f=n.keyCode==33?a:1;n.shiftKey&&this.model.allowMultiSelection?(this._pageUpStep==null&&(this._pageUpStep=this.prevselectedItem),this._pageDownStep==null&&(this._pageDownStep=this.prevselectedItem),this._pageDownStep<=this.prevselectedItem?(e=this._pageUpStep-f>0?this._pageUpStep-f:0,u=this._pageDownStep):(e=this.prevselectedItem,u=this._pageDownStep-f>this.prevselectedItem?this._pageDownStep-f:this.prevselectedItem),this._shiftHomeAndEndKeyProcess(e,u,u>this.prevselectedItem?u:e),this._pageUpStep=e,this._pageDownStep=u):this._moveUp(this._activeItem,f);this.scrollerObj.setModel({scrollTop:this._calcScrollTop()});this._preventDefaultAction(n);break;case 34:f=n.keyCode==34?a:1;n.shiftKey&&this.model.allowMultiSelection?(this._pageUpStep==null&&(this._pageUpStep=this.prevselectedItem),this._pageDownStep==null&&(this._pageDownStep=this.prevselectedItem),this._pageUpStep==0&&this.prevselectedItem!=0?this._pageUpStep+f>=this.prevselectedItem?e=u=this.prevselectedItem:(e=this._pageUpStep+f,u=this._pageDownStep+f<this.element.children("li").length?this._pageDownStep+f:this.element.children("li").length-1):this._pageUpStep!=this.prevselectedItem&&this._pageUpStep+f>=this.prevselectedItem?e=u=this.prevselectedItem:(e=this._pageUpStep,u=this._pageDownStep+f<this.element.children("li").length?this._pageDownStep+f:this.element.children("li").length-1),e<this.prevselectedItem&&u>this.prevselectedItem&&(u=this.prevselectedItem),this._shiftHomeAndEndKeyProcess(e,u,e<this.prevselectedItem?e:u),this._pageUpStep=e,this._pageDownStep=u):this._moveDown(this._activeItem,f);this.scrollerObj.setModel({scrollTop:this._calcScrollTop()});this._preventDefaultAction(n);break;case 35:for(n.shiftKey&&this.model.allowMultiSelection?this._shiftHomeAndEndKeyProcess(this._activeItem,this._listSize-1,this._listSize-1):this._homeAndEndKeyProcess(n,r,this._listSize-1),t=this._listSize-1;t>0;t--)if(!$(this.element.find("li")[t]).hasClass("e-disable"))return this.model.selectedIndex=t,this._shiftSelectItem=t,this.model.allowVirtualScrolling==!0&&v._onScroll(n),!1;break;case 36:for(n.shiftKey&&this.model.allowMultiSelection?this._shiftHomeAndEndKeyProcess(0,this._activeItem,0):this._homeAndEndKeyProcess(n,r,0),t=0;t<this._listSize;t++)if(!$(this.element.find("li")[t]).hasClass("e-disable"))return this.model.selectedIndex=t,!1}}},_moveUp:function(n,t){n==null||n<=0?this._checkDisableStep(0,t,!1):n>this._listSize-1?this._checkDisableStep(this._listSize-1,t,!1):n>0&&n<=this._listSize-1&&this._checkDisableStep(n,t,!1)},_moveDown:function(n,t){n==null||n<0?this._checkDisableStep(-1,t,!0):n==0?this._checkDisableStep(0,t,!0):n>=this._listSize-1?this._checkDisableStep(this._listSize-1,t,!0):n<this._listSize-1&&this._checkDisableStep(n,t,!0)},_checkDisableStep:function(n,t,i){var e=i?"_disableItemSelectDown":"_disableItemSelectUp",f=i?n+t:n-t,r=this[e](f),u;if(r==null)for(u=t;u>=0;u--)if(f=i?n+u:n-u,r=this[e](f),r!=null)break;r!=null&&this.selectItemByIndex(r)},_disableItemSelectDown:function(n){return(n==null||n<0)&&(n=0),n<this._listSize?$.inArray(n,this._disabledItems)<0?n:this._disableItemSelectDown(n+1):this._listSize-1},_disableItemSelectUp:function(n){if((n==null||n<0)&&(n=0),n<this._listSize){if($.inArray(n,this._disabledItems)<0)return n;if(n>0)return this._disableItemSelectUp(n-1)}},_preventDefaultAction:function(n,t){n.preventDefault?n.preventDefault():n.returnValue=!1;t&&(n.stopPropagation?n.stopPropagation():n.cancelBubble=!0)},_homeAndEndKeyProcess:function(n,t,i){if($(":focus").length&&$(":focus")[0].nodeName!="INPUT")return this._OnListSelect(this._selectedItem,i),this.selectItemByIndex(i),this._selectedItem=i,this.scrollerObj.setModel({scrollTop:this._calcScrollTop(i)}),this.model.showCheckbox&&(this._removeListHover(),$(t[i]).addClass("e-hover"),this._lastEleSelect=this._selectedItem=i),this._keyCascade(t[i],n),n.preventDefault(),!1},_shiftHomeAndEndKeyProcess:function(n,t,i){return this._removeListHover(),this._activeItemLoop(n,t),this.scrollerObj.setModel({scrollTop:this._calcScrollTop(i)}),!1},_keyCascade:function(n,t){var i=this._getItemObject($(n),t);this.model.selectedText=i.text;i.isInteraction=!0;this._trigger("select",i);this.model.cascadeTo&&(this._activeItem=this._selectedItem,this._cascadeAction())},mergeData:function(n,t){var i,r;this.datamerged=!0;this.mergeUl=$();this._setMapFields();i=this;this._skipInitialRemoteData=t?t:!1;ej.DataManager&&n instanceof ej.DataManager?(r=n.executeQuery(this._getQuery()),r.done(function(n){i.mergeValue=n.result;i._renderlistContainer()})):(this.mergeValue=n,!ej.isNullOrUndefined(this.model.fields.groupBy)&&this.datamerged&&this.groupData?this.listitems=this.listitems[0].items?this.listitems[0].items:this.dataSource():(this.groupData=!0,this.listitems=this.listitems?this.listitems:this.dataSource()),this._renderlistContainer());this._loadInitialRemoteData=!1},_onScroll:function(n){if(n.scrollTop){var i=n.scrollTop,t=this;this.model.actionBegin&&this._trigger("actionBegin",{});this.realUllength=this.element.find("li").length;this.model.allowVirtualScrolling&&this.model.virtualScrollMode=="normal"?n.scrollTop!=n.scrollData.scrollOneStepBy+n.scrollData.scrollable&&window.setTimeout(function(){t._virtualCount==0&&t._loadVirtualList()},300):this.model.allowVirtualScrolling&&this.model.virtualScrollMode=="continuous"&&i>=Math.round($(this.listContainer).find("ul").height()-$(this.listContainer).height())&&this.listitems.length<this._totalCount&&(this._updateLoadingClass(!0),ej.DataManager&&this.model.dataSource instanceof ej.DataManager&&this._queryPromise(this.realUllength,t,this.realUllength+this.model.itemRequestCount,n))}},_queryPromise:function(n,t,i,r){this._trigger("itemRequest",{event:r,isInteraction:!0});this._setMapFields();var u=this._savedQueries.clone(),f=this.dataSource().executeQuery(u.range(n,i));this._updateLoadingClass(!0);f.done(function(i){t._trigger("actionBeforeSuccess",i);t.realUllength=r.source!="wheel"?t.mergeValue?t.mergeValue.length+n:n:n;t._loadlist(i.result)._checkboxHideShow(t.model.showCheckbox)._showResult()._updateLoadingClass();t._applySelection();t.model.virtualScrollMode=="continuous"&&t.scrollerObj.refresh();t._trigger("actionSuccess",i)}).fail(function(n){t._trigger("actionFailure",n)}).always(function(n){t._trigger("actionComplete",n)})},_loadVirtualList:function(){var n,o,i,s,h,e;this._virtualCount++;this._getLiHeight();var c=this.scrollerObj.scrollTop(),t=this,u=0,r,f=null;if(this._currentPageindex=Math.round(c/(this._liItemHeight*this.model.itemRequestCount)),$.inArray(this._currentPageindex,this._virtualPages.sort(function(n,t){return n-t}))!=-1)if(this._currentPageindex==0){if($.inArray(this._currentPageindex+1,this._virtualPages)!=-1)return this._virtualCount--,!1;this._currentPageindex=this._currentPageindex+1}else if($.inArray(this._currentPageindex-1,this._virtualPages)!=-1){if($.inArray(this._currentPageindex+1,this._virtualPages)!=-1)return this._virtualCount--,!1;this._currentPageindex=this._currentPageindex+1}else this._currentPageindex=this._currentPageindex-1;for(r=!($.inArray(this._currentPageindex-1,this._virtualPages)!=-1),this._updateLoadingClass(!0),n=this._virtualPages.length-1;n>=0;n--)if(this._virtualPages[n]<this._currentPageindex){u=this._virtualPages[n];n+1==this._virtualPages.length||(f=this._virtualPages[n+1]);break}if(o=r?(this._currentPageindex-1)*this.model.itemRequestCount:this._currentPageindex*this.model.itemRequestCount,i=ej.Query().range(o,this._currentPageindex*this.model.itemRequestCount+this.model.itemRequestCount),ej.DataManager){if(e=r?(this._currentPageindex-1)*this.model.itemRequestCount:this._currentPageindex*this.model.itemRequestCount,i=this.dataSource().dataSource.offline==!0?ej.Query().skip(e).take(this.model.itemRequestCount):this._getQuery().skip(e),r){for(n=0;n<i.queries.length;n++)if(i.queries[n].fn=="onTake"){i.queries.splice(n,1);break}i.take(2*this.model.itemRequestCount)}t._trigger("actionBegin")||(s=t._dataUrl.executeQuery(i),s.done(function(n){t._appendVirtualList(n.result,u,t._currentPageindex,f,r);t._trigger("actionSuccess",{e:n})}).fail(function(n){t._virtualCount--;t._trigger("actionFailure",{e:n})}).always(function(n){t._updateLoadingClass(!1);t._trigger("actionComplete",{e:n})}))}else h=ej.DataManager(t.model.dataSource).executeLocal(i),this._appendVirtualList(h,u,this._currentPageindex,f,r),this._updateLoadingClass(!1)},_appendVirtualList:function(n,t,i,r,u){var f,o,h,e,s,c;if(this._virtualCount--,this._getLiHeight(),$.inArray(i,this._virtualPages.sort(function(n,t){return n-t}))!=-1)return!1;u&&$.inArray(i-1,this._virtualPages.sort())!=-1&&(n.splice(0,this.model.itemRequestCount),u=!1);f=this.model.itemRequestCount;o=$("<ul>");h=u?(i-1)*f*this._liItemHeight-(t*f+f)*this._liItemHeight:i*f*this._liItemHeight-(t*f+f)*this._liItemHeight;h!=0&&o.append($("<span>").addClass("e-virtual").css({display:"block",height:h}));this._loadlist(n);$(this._dummyVirtualUl).attr("data-ej-page",i);u&&$(this._dummyVirtualUl).slice(0,f).attr("data-ej-page",i-1);o.append(this._dummyVirtualUl);s=this.element;e=(i*f+f)*this._liItemHeight;e=r!=null?r*f*this._liItemHeight-e:s.height()-e;e!=0&&o.append($("<span>").addClass("e-virtual").css({display:"block",height:e}));c=s.find("li[data-ej-page="+t+"]").last();c.next().remove();o.children().insertAfter(c);this.model.showCheckbox&&this._checkboxHideShow(!0);this._virtualPages.push(i);u&&this._virtualPages.push(i-1);this._virtualUl=s.clone(!0);this._showResult();this._addDragableClass()._enableDragDrop()},_selectListItems:function(){for(var t=this.element.find("li:not('.e-ghead')"),n=0;n<t.length;n++)$(t[n]).hasClass("selectItem")&&!$(t[n]).hasClass("e-select")&&$(t[n]).addClass("e-select").removeClass("selectItem")},_setText:function(n){for(var t=0;t<this.listitems.length;t++)$(this.element.children("li")[t]).text()==n&&this.unselectAll().selectItemByIndex(t)},_getLiCount:function(){return parseInt(this.listContainer.height()/this.listItemsElement.height())},_onDragStarts:function(n,t){return this._trigger("itemDragStart",{items:n,target:t})},_onDrag:function(n,t){return this._trigger("itemDrag",{items:n,target:t})},_onDragStop:function(n,t){return this._trigger("itemDragStop",{items:n,target:t})},_onDropped:function(n,t,i){return n.length>1?n={items:n,droppedElementData:n,dropTarget:[i.target],event:i.event}:(ej.isOnWebForms&&i.target.tagName=="LI"&&i.target.classList.contains("e-droppable")&&(i.target=this.element.parent()[0]),n={items:[n],droppedItemText:n.text,droppedItemValue:n.value,droppedItemIsChecked:n.isChecked,droppedElementData:n,dropTarget:[i.target],event:i.event}),this._trigger("itemDrop",n)},_OnKeyPress:function(n){this.model.enableIncrementalSearch&&this.model.enabled&&this._incrementalSearch(this._isMozilla?n.charCode:n.keyCode)},_incrementalSearch:function(n){var o=this,s=String.fromCharCode(n),f,e,t;this._incqueryString!=s?this._incqueryString+=s:this._incqueryString=s;this._incqueryString.length>0&&this._typeInterval==null&&(this._typeInterval=setTimeout(function(){o._incqueryString="";o._typeInterval=null},o._typingThreshold));var r=this.listContainer.find("li:not('.e-ghead')"),t,c=this.model.caseSensitiveSearch,i,h=this._incqueryString,l=this._incqueryString.length,u=!1;for(c||(h=h.toLowerCase()),f=this._activeItem,--f,e=this._activeItem!=r.length-1?this._activeItem+1:0,this._incqueryString.length>1&&(e=this._activeItem),t=e;t<r.length&&f!=t;t++)if(i=$.trim($(r[t]).text()),i=c?i:i.toLowerCase(),i.substr(0,l)===h?(this._removeListHover(),this.element.children("li").removeClass("e-active"),this._selectedItem=t,this._addListHover(),u=!0):t==r.length-1&&u==!1&&(e!=0?(t=-1,++f):u=!0),u)break},_wireEvents:function(){this._on(this.listContainer,"focus",this._OnFocusIn);this._on(this.listContainer,"blur",this._OnFocusOut);$(window).on("resize",$.proxy(this._OnWindowResize,this))},_OnFocusIn:function(){this._focused||(this._trigger("focusIn"),this._on(this.listContainer,"keydown",this._OnKeyDown),this._on(this.listContainer,"keypress",this._OnKeyPress),this._focused=!0)},_OnFocusOut:function(){this._focused&&(this._trigger("focusOut"),this._off(this.listContainer,"keydown",this._OnKeyDown),this._off(this.listContainer,"keypress",this._OnKeyPress),this._focused=!1)}});ej.VirtualScrollMode={Normal:"normal",Continuous:"continuous"};ej.SortOrder={Ascending:"ascending",Descending:"descending",None:"none"}})(jQuery,Syncfusion)});
