/*!
*  filename: ej.localetexts.es-ES.min.js
*  version : 18.1.0.42
*  Copyright Syncfusion Inc. 2001 - 2020. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
ej.ReportDesigner.Locale["es-ES"]={itemPanel:{waterMarkText:"Widgets de búsqueda",noDataFound:"No se encontraron coincidencias...",customCategory:"Códigos de barras",customRptItemName:"Código de barras 1D",dataRequirements:"Requerimientos de datos",customTooltip:{tooltip:{requirements:"Agregar un elemento de informe al área de diseñador.",description:"Muestra el elemento de informe personalizado.",title:"Informe personalizado"}},groupItems:{basicItems:{groupName:"Artículos básicos",Items:{line:{displayText:"Línea",tooltip:{requirements:"Para separar una región a través de una línea en las secciones del informe.",description:"Elemento gráfico para separar la región del informe.",title:"Línea"}},image:{displayText:"Imagen",tooltip:{requirements:"Para mostrar una imagen de la base de datos, incruste la imagen.",description:"Muestra las imágenes.",title:"Imagen"}},textBox:{displayText:"Caja de texto",tooltip:{requirements:"Agrega cualquier texto.",description:"Muestra el texto estático y dinámico.",title:"Caja de texto"}},rectangle:{displayText:"Rectángulo",tooltip:{requirements:"Combina uno o más elementos del informe dentro de él.",description:"Elemento contenedor gráfico.",title:"Rectángulo"}}}},comparison:{groupName:"Comparación",Items:{column:{displayText:"Columna",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Compara valores para un conjunto de elementos desordenados en varias categorías usando las barras verticales dispuestas horizontalmente.",title:"Columna"}},bar:{displayText:"Bar",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Compara valores para un conjunto de elementos desordenados en varias categorías usando las barras horizontales dispuestas verticalmente.",title:"Bar"}},stackedColumn:{displayText:"Columna apilada",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Compara medidas múltiples usando las barras apiladas verticalmente.",title:"Columna apilada"}},stackedBar:{displayText:"Barra apilada",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Compara medidas múltiples usando las barras apiladas horizontalmente.",title:"Barra apilada"}},stackedColumnPercent:{displayText:"Columna apilada100%",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Compara medidas múltiples como partes de un todo usando las barras apiladas verticalmente.",title:"Columna apilada100%"}},stackedBarPercent:{displayText:"Barre empilée100%",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Compara medidas múltiples como partes de un todo usando las barras apiladas horizontalmente.",title:"Barre empilée100%"}}}},proportion:{groupName:"Proporción",Items:{pie:{displayText:"Tarta",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Muestra las proporciones de la contribución de cada artículo al total en forma de sectores de sectores.",title:"Tarta"}},explodedPie:{displayText:"Pie estallado",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Enfatiza una porción individual de un gráfico circular.",title:"Pie estallado"}},doughnut:{displayText:"Rosquilla",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Muestra las proporciones de cada contribución de artículos al total en forma de rodajas de donuts.",title:"Rosquilla"}},pyramid:{displayText:"Pirámide",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Muestra la comparación proporcional entre valores de una manera progresivamente creciente.",title:"Pirámide"}},funnel:{displayText:"Embudo",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Muestra la comparación proporcional entre valores de una manera progresivamente decreciente.",title:"Embudo"}}}},distribution:{groupName:"Distribución",Items:{area:{displayText:"Zona",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Compara valores para un conjunto de elementos desordenados en varias categorías a través de las curvas rellenas ordenadas verticalmente.",title:"Zona"}},smoothArea:{displayText:"Área suave",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Compara los valores de un conjunto de elementos desordenados en varias categorías a través de las curvas rellenas ordenadas verticalmente con una superficie lisa.",title:"Área suave"}},stackedArea:{displayText:"Área apilada",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Compara medidas múltiples a través de las curvas llenas apiladas verticalmente.",title:"Área apilada"}},stackedAreaPercent:{displayText:"Área apilada100%",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Compara medidas múltiples como partes de un todo a través de las curvas llenas apiladas verticalmente.",title:"Área apilada100%"}},line:{displayText:"Línea",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Muestra las tendencias para el análisis durante un tiempo con puntos de datos conectados utilizando las líneas rectas.",title:"Línea"}},smoothLine:{displayText:"Línea suave",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Compara la distribución de valores durante un período de tiempo conectado utilizando líneas suaves.",title:"Línea suave"}},steppedLine:{displayText:"Línea escalonada",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Compara la distribución de valores durante un período de tiempo conectado utilizando las líneas escalonadas.",title:"Línea escalonada"}},lineWithMarkers:{displayText:"Línea con marcadores",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Compare los cambios en el mismo período de tiempo para más de un grupo.",title:"Línea con marcadores"}},smoothLineWithMarkers:{displayText:"SmoothLine con marcadores",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Los valores trazados se representan con un punto de marcador y esos puntos se conectan usando una línea suave.",title:"SmoothLine con marcadores"}},scatter:{displayText:"Dispersión",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Muestra una serie como un conjunto de puntos y los valores están representados por la posición de los puntos en el gráfico.",title:"Dispersión"}},bubble:{displayText:"Burbuja",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Muestra la diferencia entre dos valores de un punto de datos en función del tamaño de la burbuja.",title:"Burbuja"}},polar:{displayText:"Polar",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Muestra una serie como un conjunto de puntos que están agrupados por categoría en un círculo de 360 ​​grados.",title:"Polar"}},radar:{displayText:"Radar",tooltip:{requirements:"1 o más valores y 1 o más columnas.",description:"Muestra una serie como una línea o área circular.",title:"Radar"}}}},dataRegions:{groupName:"Regiones de datos",Items:{tablix:{displayText:"Mesa",tooltip:{requirements:"1 o más filas / columnas.",description:"Muestra datos de informes paginados en celdas.",title:"Mesa"}},list:{displayText:"Lista",tooltip:{requirements:"1 o más filas / columnas.",description:"Una lista muestra los datos en formato libre. Coloque los campos en cualquier lugar dentro de la lista.",title:"Lista"}}}},subReports:{groupName:"Informes secundarios",Items:{subreport:{displayText:"Subinforme",tooltip:{requirements:"Mostrar / Incrustar el informe en el informe principal.",description:"Muestra otro informe en el cuerpo principal del informe.",title:"Subinforme"}}}}}},toolbar:{newReport:"Nuevo",open:"Abierto",openMenu:{fromDevice:"Del dispositivo",fromServer:"Del servidor"},save:"Salvar",saveMenu:{saveLabel:"Salvar",saveAs:"Guardar como",saveAsMenu:{saveToDevice:"Al dispositivo",saveToServer:"Al servidor"}},cut:"Cortar",copy:"Dupdo",paste:"Pegar",deleteItem:"Borrar",undo:"Deshacer",redo:"Rehacer",zoomIn:"acercarse",zoomOut:"Disminuir el zoom",header:"Encabezamiento",footer:"Pie de página",order:"Orden",orderMenu:{sendBackward:"Enviar atrás",bringForward:"Adelantar",sendToBack:"Mandado hacia atras",bringToFront:"Traer al frente"},left:"Alinear a la izquierda",center:"Centrar",right:"Alinear a la derecha",top:"Alineación superior",middle:"Medio",bottom:"Alineación inferior",distributeHorizontally:"Distribuir horizontalmente",distributeVertically:"Distribuir verticalmente",centerHorizontally:"Centrar horizontalmente",centerVertically:"Centrar verticalmente",sizing:"Dimensionamiento",sizingMenu:{sameSize:"Mismo tamaño",sameWidth:"Mismo ancho",sameHeight:"Misma altura"},alignToGrid:"Alinear a la red",sizeToGrid:"Tamaño a la cuadrícula",gridLine:"Líneas de cuadrícula",snapToShape:"Ajustar a forma",fullScreen:"Pantalla completa",preview:"Avance",reportUpload:{alertLabel:"Subir",alertMessage:"Error al cargar el archivo. Por favor carga de nuevo"},grouping:"Agrupamiento",view:"Ver"},newReport:{title:"Nuevo reporte",fileName:"Nombre del archivo",waterMark:"Reportar nombre",create:"Crear",cancel:"Cancelar",close:"Cerca"},reportAction:{enableLink:"Habilitar enlace",linkTo:"Enlace a",report:"Informe",url:"URL"},linkReport:{reportCaption:"Informe",setParameter:"Establecer parámetros"},imageProperty:{basicSettings:{categoryName:"Ajustes básicos",source:"Fuente",sourceTypes:{external:"Externo",embedded:"Incrustado",database:"Base de datos"},value:"Valor",mimeType:"Tipo MIME",mimeTypes:{bmp:"imagen/bmp",jpeg:"imagen/jpeg",gif:"imagen/gif",png:"imagen/png",xPng:"imagen/x-png"}},categoryName:"Enlazar",linkReport:"Informe de enlace",appearance:{categoryName:"Apariencia",styleTooltip:"Estilo",colorTooltip:"Color",sizeTooltip:"tamaño",borderTypes:{border:"Frontera",borderLeft:"Izquierda",borderTop:"Parte superior",borderRight:"Derecha",borderBottom:"Fondo"},borderStyles:{solid:"Sólido",none:"Ninguna",double:"Doble",dashed:"Disparo",dotted:"Punteado"}},size:{categoryName:"Tamaño",paddingTypes:{padding:"Relleno",paddingLeft:"Izquierda",paddingTop:"Parte superior",paddingRight:"Derecha",paddingBottom:"Fondo"},sizing:"Dimensionamiento",sizeTypes:{auto:"Tamaño automático",fit:"Ajuste",proportional:"FitProportional",clip:"Acortar"}},position:{categoryPosition:"Posición",positionLabel:"Posición",left:"Izquierda",top:"Parte superior",sizeLabel:"Tamaño",width:"Anchura",height:"Altura"},visibility:{categoryName:"Visibilidad",visible:"Visible",toggleItem:"Alternar artículo"}},chartProperty:{commonProperties:{showBorder:"Mostrar borde",border:{border:"Frontera",borderLeft:"Izquierda",borderTop:"Parte superior",borderRight:"Derecha",borderBottom:"Fondo"},background:"Color de fondo",font:"Fuente",fontStyle:"Estilo de fuente",labelRotation:"Rotación de la etiqueta",categoryAxis:"Categoría Eje",valueAxis:"Eje de valor",defaultText:"Defecto",auto:"Auto",borderStyles:{solid:"Sólido",none:"Ninguna",double:"Double",dashed:"Disparo",dotted:"Punteado",dashDot:"Guion punto",dashDotDot:"DashDotDot"},horizontalAlignments:{near:"Cerca",far:"Lejos"},textAlignments:{right:"Derecha",bottom:"Fondo",center:"Centrar",topLeft:"Arriba a la izquierda",topCenter:"Centro Superior",topRight:"Parte superior derecha",rightTop:"Justo arriba",rightCenter:"Centro de la derecha",rightBottom:"Boton derecho",bottomLeft:"BottomLeft",bottomCenter:"Parte inferior central",bottomRight:"Abajo a la derecha",leftTop:"LeftTop",leftCenter:"Centro izquierda",leftBottom:"Abajo a la izquierda"},fontStyleTypes:{normal:"Normal",italic:"Itálico"},fontWeightTypes:{light:"Ligero",bold:"Negrita"}},basicSettings:{categoryName:"Ajustes básicos",showLegend:{showLegendText:"Mostrar leyenda",title:"Título",titleFont:"Fuente del titulo",titleFontStyle:"Título Estilo de fuente",titleAlignment:"Alineación del título",legendPosition:"Posición de leyenda",enableCustomBounds:"Habilitar límites personalizados"},chooseSeries:"Elija la serie",showMarker:{showMarkerText:"Mostrar marcador",color:"Color",markerType:"Tipo de marcador",markerTypes:{square:"Cuadrado",circle:"Circulo",diamond:"Diamante",triangle:"Triángulo",cross:"Cruzar",star5:"Star5"},size:"Tamaño"},showDataLabel:{showDataLabelText:"Mostrar etiqueta de datos",dataLabelFormat:"Formato",dataLabelText:"Etiqueta",dataLabelValueAsText:"Use el valor como etiqueta",dataLabelTypes:{valueX:"#VALX",valueY:"#VALY",valueY2:"#VALY2",valueY3:"#VALY3",valueY4:"#VALY4",valueY5:"#VALY5",valueY6:"#VALY6",index:"#ÍNDICE",percent:"#POR CIENTO",total:"#TOTAL",axisLabel:"#ETIQUETA AXIS"}},enableSmartLabel:{smartLabelText:"Habilitar etiqueta inteligente",labelStyle:"Estilo de etiqueta",value:"Valor",smartLabelPositions:{outside:"Fuera de",inside:"Dentro",outsideInColumn:"OutsideInColumn"},smartLabelStyles:{pieLabelStyle:"Estilo de etiqueta de tarta",funnelLabelStyle:"Estilo de etiqueta de embudo",pyramidLabelStyle:"Estilo de etiqueta de pirámide",barLabelStyle:"Estilo de etiqueta de barra",smartLabelStyle:"Estilo de etiqueta"}},seriesBorder:"Borde de la serie",seriesColor:"Color de la serie"},categoryName:"Apariencia",customAttribute:{categoryName:"Atributos personalizados",userDefined:"Usuario definido",alertHeader:"Informe de tabla",alertMessage:"Formato de atributos personalizados no válido. Ejemplo correcto: 'AttrName1 = Value1, AttrName2 = Value2 '."},chartArea:{categoryName:"Área del gráfico",colorPalette:"Paleta de color",colorPaletteTypes:{earthTones:"Tonos terrestres",excel:"Sobresalir",grayScale:"Escala de grises",pastel:"Pastel",semiTransparent:"Semitransparente",berry:"Baya",chocolate:"Chocolate",fire:"Fuego",seaGreen:"Mar verde",brightPastel:"BrightPastel",pacific:"Pacífico",pacificLight:"Luz del Pacífico",pacificSemiTransparent:"Pacífico semi transparente"}},title:{categoryName:"Título",showChartTitle:"Mostrar título de gráfico",titleText:"Texto del título",titlePosition:"Posición del título"},axis:{enableAxis:"Habilitar eje",axisTitle:"Título del eje",alignment:"Alineación",lineStyle:"Estilo de línea",labelOverflowMode:"Modo de desbordamiento de etiqueta",overFlowModeTypes:{trim:"Recortar",hide:"Esconder"},labelFont:"Etiqueta de fuente",labelFormat:"Formato de etiqueta",enableMajorTicks:"Habilitar las garrapatas principales",enableMinorTicks:"Habilitar marcaciones menores",tickProperties:{tickSize:"Tamaño de la garrapata",tickColor:"Marque Color",tickWidth:"Anchura",length:"Longitud"},tickPosition:"Marque la posición"},gridLine:{categoryName:"Cuadricula",gridLineStyle:{minorGridLine:"Mostrar Minor Cuadricula",majorGridLineStyle:"Estilo principal de Cuadricula",minorGridLineStyle:"Estilo Menor de Cuadricula"}},pageBreak:{categoryName:"Salto de página",enablePageBreak:"Habilitar salto de página",breakLocation:"Lugar de descanso",breakLocationTypes:{none:"Ninguna",start:"comienzo",end:"final",startAndEnd:"InicioAndEnd",between:"Entre"},pageNumberReset:"Restablecer número de página",pageName:"Nombre de la página"},position:{categoryPosition:"Posición",positionLabel:"Posición",left:"Izquierda",top:"Parte superior",sizeLabel:"Tamaño",width:"Anchura",height:"Altura"},visibility:{categoryName:"Visibilidad",visible:"Visible",toggleItem:"Alternar artículo"},fontStyleTooltip:"Estilo",fontWeightTooltip:"Peso",fontSizeTooltip:"tamaño",fontColorTooltip:"Color",fontFamilyTooltip:"Familia tipográfica",styleTooltip:"Estilo",colorTooltip:"Color",sizeTooltip:"tamaño"},lineProperty:{basicSettings:{categoryBasicSettings:"Ajustes básicos",line:"Línea",lineTypes:{solid:"Sólido",dashed:"Disparo",dotted:"Punteado"}},position:{categoryPosition:"Posición",positionLabel:"Posición",left:"Izquierda",top:"Parte superior",sizeLabel:"Tamaño",width:"Anchura",height:"Altura"},visibility:{categoryName:"Visibilidad",visible:"Visible",toggleItem:"Alternar artículo"},styleTooltip:"Estilo",colorTooltip:"Color",sizeTooltip:"tamaño"},subReportProperty:{basicSettings:{categoryBasicSettings:"Ajustes básicos"},appearance:{categoryAppearance:"Apariencia",borderTypes:{border:"Frontera",borderLeft:"Izquierda",borderTop:"Parte superior",borderRight:"Derecha",borderBottom:"Fondo"},borderStyles:{solid:"Sólido",none:"Ninguna",double:"Double",dashed:"Disparo",dotted:"Punteado"}},noRows:{noRowsLabel:"Sin Filas",font:"Fuente",fontStyle:{fontStyleLabel:"Estilo de fuente",fontItem:{defaultStyle:"Defecto",fontNormal:"Normal",italic:"Itálico"},fontWeight:{defaultElement:"Defecto",normal:"Normal",thin:"Delgado",extraLight:"Extra ligero",light:"Ligero",medium:"Medio",semiBold:"Semi negrita",bold:"Negrita",extraBold:"Extra Negrita",heavy:"Pesado"}},textDecoration:{textDecorationLabel:"Decoración de texto",defaultDecoration:"Defecto",none:"Ninguna",underLine:"Subrayar",overLine:"Overline",lineThrough:"Línea a través"},format:"Formato",lineHeight:"Altura de la línea",message:"Mensaje",paddingTypes:{padding:"Relleno",paddingLeft:"Izquierda",paddingRight:"Derecha",paddingTop:"Parte superior",paddingBottom:"Fondo"},textAlign:{textAlignLabel:"Texto alineado",textAlignDefault:"Defecto",textAlignGeneral:"General",textAlignLeft:"Izquierda",textAlignRight:"Derecha",textAlignCenter:"Centrar"},verticalAlign:{verticalAlignlabel:"Alineación vertical",verticalAlignDefault:"Defecto",verticalAlignTop:"Parte superior",verticalAlignMiddle:"Medio",verticalAlignBottom:"Fondo"},writingMode:{writingModeLabel:"Modo de escritura",writingModeDefault:"Defecto",writingModeHorizontal:"Horizontal",writingModeVertical:"Vertical",writingModeRotate:"Girar270"}},visibility:{categoryName:"Visibilidad",visible:"Visible",toggleItem:"Alternar artículo"},position:{categoryPosition:"Posición",positionLabel:"Posición",left:"Izquierda",top:"Parte superior",sizeLabel:"Tamaño",width:"Anchura",height:"Altura"},miscellaneous:{categoryMiscellaneous:"Diverso",keepTogether:"Mantenerse juntos"},fontStyleTooltip:"Estilo",fontWeightTooltip:"Peso",fontSizeTooltip:"tamaño",fontColorTooltip:"Color",fontFamilyTooltip:"Familia tipográfica",styleTooltip:"Estilo",colorTooltip:"Color",sizeTooltip:"tamaño"},rectangleProperty:{basicSettings:{categoryBasicSettings:"Ajustes básicos",styleTooltip:"Estilo",colorTooltip:"Color",sizeTooltip:"tamaño",borderTypes:{border:"Frontera",borderLeft:"Izquierda",borderTop:"Parte superior",borderRight:"Derecha",borderBottom:"Fondo"},borderStyles:{solid:"Sólido",none:"Ninguna",double:"Double",dashed:"Disparo",dotted:"Punteado"},backGround:"Fondo"},pageBreak:{pageBreak:"Salto de página",enablePageBreak:{enablePageBreak:"Habilitar salto de página",breakLocation:{breakLocationLabel:"Break Location",none:"Ninguna",start:"Comienzo",end:"Fin",startAndEnd:"StartAndEnd",between:"Entre"},pageNumberReset:"Restablecer número de página"}},position:{categoryPosition:"Posición",positionLabel:"Posición",left:"Izquierda",top:"Parte superior",sizeLabel:"Tamaño",width:"Anchura",height:"Altura"},visibility:{categoryName:"Visibilidad",visible:"Visible",toggleItem:"Alternar artículo"},rectangleMiscellaneous:{categoryMiscellaneous:"Diverso",keepTogether:"Mantenerse juntos",pageName:"Nombre de la página"}},browseFile:{openFile:{selectReport:"Seleccionar informe",open:"Abierto"},saveFile:{saveAsReport:"Guardar como informe",name:"Nombre",save:"Salvar"},close:"Cerca",cancel:"Cancelar",waterMark:"Reportar nombre",emptyMessage:"Esta categoría está vacía",alertMessage:{reportServer:"Servidor de informes",selectCategory:"Seleccione Categoría"},warningMessage:{fileNameLabel:'Un artículo "',fileNameExist:'.rdl" ya existe. ¿Desea reemplazar el elemento existente?',populateCategory:"El diseñador de informes no pudo recuperar los recursos del servidor de informes"}},expressionMenu:{reset:"Reiniciar",expression:"Expresión",advanced:"Avanzado"},propertyPanel:{property:"Propiedades",data:"DATOS",name:"Nombre",toolTipStyle:"Estilo",toolTipColor:"Color",toolTipWidth:"Anchura",setSorts:"Establecer clases",setFilters:"Establecer filtros",advancedOptions:"Opciones avanzadas",codemodules:"Código",expressionList:{top:"Parte superior",right:"Derecha",bottom:"Fondo",left:"Izquierda",style:"Estilo",color:"Color",size:"Tamaño",fontFamily:"Familia tipográfica",width:"Anchura",height:"Altura",weight:"Peso",image:"Imagen"},alertMessage:{nameWarning:"El nombre no puede estar vacío",nameAlert:"Nombre ya existe",nameValidation:"El nombre no debe contener espacios ni caracteres especiales"},unitType:{inchText:"en",centimeterText:"cm",pixelText:"píxel",pointText:"pt",millimeterText:"mm",picaText:"pc"},setGroups:"Grupos establecidos",addDatasource:"Añadir fuente de datos",dataAlertMsg:"No se ha añadido ninguna fuente de datos !"},dataSource:{newDatasource:"NUEVO DATOSOURCE",datasource:"FUENTES DE DATOS",datasourceList:{data:"Datos",contextMenu:{editItem:"Editar",deleteItem:"Borrar",createDataSet:"Crear conjunto de datos",cloneDatasource:"Clon"}},datasourceType:{existOption:"Existente",newOption:"Crear nuevo",selectDatasoure:"Seleccione la fuente de datos",connectDatasource:"Conectar fuente de datos",datasourceType:"Elige el tipo para conectarte",sqlLabel:"SQL",sqlCeLabel:"SQLCE",odbcLabel:"ODBC",oracleLabel:"ORACLE",oledbLabel:"OLEDB",xmlLabel:"XML",sharedLabel:"Compartido"},datasourceConnection:{newConnection:"NUEVA CONEXIÓN",editConnection:"EDITAR CONEXIÓN",name:"Nombre",save:"Salvar",connect:"Conectar",cancel:"Cancelar"},sqlDatasource:{authenticationType:"Tipo de autenticación",window:"ventanas",sqlServer:"servidor SQL",userName:"Nombre de usuario",password:"Contraseña",switchLabel:"Panel de avance de fuente de datos",switchAlert:"El cambio al diseñador visual descartará los cambios manuales realizados en la consulta. ¿Quieres usar el diseñador visual de todos modos?",basicOption:{serverName:"Nombre del servidor",savePassword:"Guardar contraseña",database:"Base de datos",advanceSwitch:"Opción de avance"},advanceOption:{connectionString:"Cadena de conexión",promptLabel:"Texto rápido",prompt:"Rápido",none:"Ninguna",savePassword:"Guardar contraseña",basicSwitch:"Opción Básica"},alertMessage:{alertConnectionString:"Especifique la cadena de conexión",alertPrompt:"Especifique el texto de aviso",alertUserName:"Especifique el nombre de usuario",alertPassword:"Especifique la contraseña",alertServerName:"Especifique el nombre del servidor",alertDatabaseName:"Especifique el nombre de la base de datos"}},sqlceDatasource:{connectionString:"Cadena de conexión",authenticationType:"Tipo de autenticación",authentication:"Autenticación",none:"Ninguna",password:"Contraseña",savePassword:"Guardar contraseña",alertMessage:{alertConnectionString:"Especifique la cadena de conexión",alertPassword:"Especificar la contraseña"}},odbcDatasource:{connectionString:"Cadena de conexión",authenticationType:"Tipo de autenticación",authentication:"Autenticación",prompt:"Rápido",none:"Ninguna",userName:"Nombre de usuario",password:"Contraseña",promptLabel:"Texto de aviso",savePassword:"Guardar contraseña",alertMessage:{alertConnectionString:"Especificar la cadena de conexión",alertPrompt:"Especificar el texto de aviso",alertUserName:"Especificar el nombre de usuario",alertPassword:"Especificar la contraseña"}},oracleDatasource:{connectionString:"Cadena de conexión",authenticationType:"Tipo de autenticación",authentication:"Autenticación",prompt:"Rápido",none:"Ninguna",userName:"Nombre de usuario",password:"Contraseña",promptLabel:"Texto de aviso",savePassword:"Guardar contraseña",alertMessage:{alertConnectionString:"Especificar la cadena de conexión",alertPrompt:"Especificar el texto de aviso",alertUserName:"Especificar el nombre de usuario",alertPassword:"Especificar la contraseña"}},oledbDatasource:{connectionString:"Cadena de conexión",authenticationType:"Tipo de autenticación",authentication:"Autenticación",prompt:"Rápido",none:"Ninguna",userName:"Nombre de usuario",password:"Contraseña",promptLabel:"Texto de aviso",savePassword:"Guardar contraseña",alertMessage:{alertConnectionString:"Especificar la cadena de conexión",alertPrompt:"Especificar el texto de aviso",alertUserName:"Especificar el nombre de usuario",alertPassword:"Especificar la contraseña"}},xmlDatasource:{connectionString:"Cadena de conexión"},sharedDatasource:{datasource:"Fuente de datos compartida",alertMessage:"Seleccione una fuente de datos compartida"},alertMessage:{alertLabel:"Fuente de datos",alertConnectionFailed:"El diseñador de informes no pudo conectar la fuente de datos",dataExtensionFailed:"El proveedor de datos seleccionado no está disponible. Por favor, compruebe la extensión de datos.",connectStringValidation:"Como la cadena de conexión contiene expresiones en la fuente de datos ",validationMessage:" Por favor, actualice con una cadena de conexión válida.",executionMessage:", No podemos ejecutar el conjunto de datos para esta conexión.",confirmMessage:" ¿Seguro que quieres guardar la fuente de datos?",deleteValue:"Eliminar origen de datos '",nameWarning:"Especifique el nombre de la fuente de datos",nameAlert:"El nombre especificado ya existe en la lista Fuente de datos",nameValidation:"El nombre no debe contener espacios ni caracteres especiales"}},imageManager:{headerText:"Administrador de imagen",addImageButton:"Añadir imagen",deleteImage:"Eliminar imagen incrustada",image:"Imagen"},linkParameter:{title:"Parámetros",headerTxt:"Parámetro de enlace",descriptionText:"Parámetros del informe",addText:"AÑADIR",ok:"DE ACUERDO",cancel:"Cancelar",nameWaterMark:"Nombre del parámetro",valueWaterMark:"Valor",errorMessage:"Ingrese un valor para esta propiedad",closeToolTip:"Cerca"},filter:{title:"Filtrar",descriptionLable:"Incluye filas donde las siguientes condiciones son verdaderas.",add:"AÑADIR",ok:"DE ACUERDO",cancel:"Cancelar",valueWaterMark:"Valor",fieldWaterMark:"Elegir campo",closeToolTip:"Cerca",errorMessage:{booleanValidation:"El valor no es un valor booleano.",intValidation:"El valor no es un número entero.",floatValidation:"El valor no es un flotador.",dateTimeValidation:"El valor es un formato de fecha / hora no válido.",topBottomFilter:"Los operadores de filtro% superior e inferior% requieren un tipo de datos flotante o entero.",expressionValidation:"Elegir valor para el campo de expresión"},operatorTypes:{like:"Me gusta",topN:"TopN",bottomN:"BottomN",topPercent:"Top%",bottomPercent:"Bottom%",between:"Entre",inFilter:"En"}},dataField:{title:"Campos de información",descriptionLable:"Incluir las filas del campo de datos",add:"AÑADIR",ok:"DE ACUERDO",cancel:"Cancelar",fieldNameWaterMark:"Nombre del campo",closeToolTip:"Cerca",errorMessages:{emptyField:"Especifique el nombre del campo",invalidCharacters:"El nombre del campo no debe contener espacios y caracteres especiales",sameCharacter:"El nombre del campo ya existe"},dsNameLabel:"Nombre",dsNameWaterMark:"Nombre de datos",dsNameValidation:{nameWarning:"Especifique el nombre del conjunto de datos",nameAlert:"El nombre especificado ya existe en la lista DataSet",nameValidation:"El nombre de DataSet no debe contener espacios y caracteres especiales"}},dataPanel:{itemTooltip:{properties:"Propiedades",data:"Datos",parameters:"Parámetros",imageManager:"Administrador de imagen",expand:"Expandir",collapse:"Colapso"},dataSourceNewAlert:{title:"Fuente de datos",contentMessage:"¿Desea cancelar la creación del origen de datos?"},dataSourceEditAlert:{title:"Fuente de datos",contentMessage:"¿Desea cancelar la edición del origen de datos?"},dataSetNewAlert:{title:"Conjunto de datos",contentMessage:"¿Desea cancelar la creación del conjunto de datos?"},dataSetEditAlert:{title:"Conjunto de datos",contentMessage:"¿Desea cancelar la edición del conjunto de datos?"},parameterNewAlert:{title:"Parámetro",contentMessage:"¿Desea cancelar la creación de parámetros?"},parameterEditAlert:{title:"Parámetro",contentMessage:"¿Desea cancelar la edición de parámetros?"}},dataSet:{headerText:"DATOS",newData:"AGREGAR DATOSET",shareDataset:{headerText:"NUEVO DATASET",editHeaderText:"EDITAR DATASET",save:"Salvar",cancel:"Cancelar",nameLable:"Nombre",sharedDatasetLabel:"Shared DataSet",errorMessage:{nameValidation:"Especifique el nombre del conjunto de datos",datasetValidation:"Seleccione un DataSource compartido",duplicateName:"El nombre especificado ya existe en la lista del conjunto de datos",specialCharacter:"El nombre no debe contener espacios ni caracteres especiales"}},contextMenu:{edit:"Editar",remove:"Borrar",cloneDataset:"Clon",filter:"Filtrar",setField:"Campos"},datasourceSwitcher:"Fuentes de datos",deleteDataset:"Eliminar conjunto de datos",deleteField:"Eliminar campo",newDataText:"Nuevos datos",sharedDataText:"Datos compartidos",dataRestriction:{dsCreateRestriction:"La creación de la fuente de datos ha sido restringida",title:"Datos"},dataFieldSearch:{errorMessage:"No se encontraron coincidencias",searchText:"Buscar"}},reportViewer:{toolbar:{print:"Impresión",exportText:"Exportar",pageFit:"Ajustar a la página",exportformat:{Pdf:"PDF",Excel:"Sobresalir",Word:"Palabra",Html:"Html",PPT:"PowerPoint",CSV:"CSV"},pageSetup:"Configuración de página",gotoFirst:"Ir a la primera",gotoLast:"Ir al último",gotoNext:"Ir a Siguiente",gotoPrevious:"Ir a Anterior",gotoParanet:"Ir a los padres",zoomIn:"acercarse",zoomOut:"Disminuir el zoom",fittopage:{pageWidth:"Ancho de página",pageHeight:"Toda la pagina"},printLayout:"Diseño de impresión",refresh:"Refrescar",documentMap:"Mapa del documento",parameter:"Parámetro",viewDesign:"Cerrar vista previa"},pagesetupDialog:{close:"Cerca",paperSize:"Tamaño de papel",height:"Altura",width:"Anchura",margins:"Márgenes",top:"Parte superior",bottom:"Fondo",right:"Derecha",left:"Izquierda",unit:"en",orientation:"Orientación",portrait:"Retrato",landscape:"Paisaje",doneButton:"DE ACUERDO",cancelButton:"Cancelar"},credential:{userName:"Nombre de usuario",password:"Contraseña"},waterMark:{selectOption:"Seleccionar opción",selectValue:"Seleccione un valor"},errorMessage:{startMessage:"El Visor de informes encontró algunos problemas al cargar este informe. Por favor",middleMessage:" haga clic aquí",endMessage:"para ver los detalles del error",closeMessage:"Cerrar este mensaje"},alertMessage:{close:"Cerca",title:"Visor de informes",done:"DE ACUERDO",showDetails:"Mostrar detalles",hideDetails:"Ocultar detalles",reportLoad:"Informe cargado:",RVERR0001:"Visor de informes no pudo cargar el Informe",RVERR0002:"Visor de informes no pudo procesar el Informe",RVERR0003:"Se produjo un error en la devolución de datos de ajax",RVERR0004:"Seleccione un valor para el parámetro",RVERR0005:"Al parámetro {nombre de parámetro} le falta un valor",RVERR0006:"Por favor ingrese la entrada del tipo de datos float",RVERR0007:"Ingrese la entrada de tipo de datos enteros",RVERR0008:"Visor de informes no pudo validar las credenciales de Fuente de datos",RVERR0009:"Los márgenes están superpuestos, o están fuera del papel. Ingrese un tamaño de margen diferente.",RVERR0010:"Ingrese un valor para el parámetro",RVERR0011:"El parámetro no puede estar en blanco",RVERR0012:"El valor proporcionado para el parámetro de informe {indicador de parámetros} no es válido para su tipo."},selectAll:"Seleccionar todo",viewButton:"Vista del informe"},sortData:{sorting:"Clasificación",headerText:"Filtro de clasificación",add:"AÑADIR",changeSortingOptions:"Cambiar opciones de clasificación.",sortBy:"Ordenar por",thenBy:"Entonces por",direction:{ascending:"Ascendente",descending:"Descendente"},chooseField:"Elige el campo",errorMessage:"Elegir valor para el campo de expresión",ok:"DE ACUERDO",cancel:"Cancelar",close:"Cerca"},groupData:{grouping:"Agrupamiento",headerTxt:"Grupo",headerTxtLabel:"Etiqueta de grupo",name:"Nombre",label:"Etiqueta",changeGroupingOptions:"Cambiar las opciones de agrupamiento.",add:"AÑADIR",groupBy:"Agrupar por",andOn:"Y en",chooseField:"Elegir campo",ok:"DE ACUERDO",cancel:"Cancelar",close:"Cerca",errorMessage:{nameErrorMessage:"Por favor ingrese el nombre válido",expressionErrorMessage:"Elegir valor para un campo de expresión"}},alertMessage:{yes:"Sí",no:"No",showDetails:"Mostrar detalles",hideDetails:"Ocultar detalles",close:"Cerca"},parameter:{listPanel:{headerText:"Parámetros",newParameter:"NUEVO PARÁMETRO",editMenu:{edit:"Editar",remove:"Borrar"},alertTitle:"Parámetro"},configurationPanel:{newHeaderText:"NUEVO PARÁMETRO",editHeaderText:"EDITAR PARÁMETRO",nameLabel:"Nombre",promptLable:"Rápido",dataTypeLable:"Tipo de datos",blankValueLable:'Permitir valor en blanco ("")',nullValueLable:"Permitir valor nulo",multipleValueLable:"Permitir múltiples valores",visibilityLable:"Visibilidad",assignValueLable:"Asignar valor >>",save:"Salvar",cancel:"Cancelar",visibility:{visible:"Visible",hidden:"Oculto",internal:"Interno"},dataType:{stringType:"Cuerda",booleanType:"Booleano",dateTimeType:"Booleano",integerType:"Entero",floatType:"Flotador"}},errorMessage:{nameField:"Por favor ingrese el nombre",promptField:"Por favor ingrese el valor",nameAlreadyExists:"El nombre del parámetro ya existe"},warningMessage:{specialCharacter:"El nombre no debe contener espacios ni caracteres especiales",multipleValueAlert:"Se especificaron múltiples valores predeterminados. El parámetro no permite valores múltiples.",nullValueAlert:"En el campo de valor, se especificó un valor nulo. El parámetro no permite valores nulos. "},alertMessage:{confirmNullCheck:"Los valores disponibles o predeterminados pueden contener valor nulo, ¿desea habilitar la casilla de verificación permitir el valor nulo?",confirmBlankValue:"Los valores disponibles o predeterminados pueden contener un valor en blanco, ¿desea habilitar la casilla de verificación de valor en blanco?",dataTypeChange:"Al cambiar el tipo de datos, se descartarán los cambios realizados en los valores disponibles y predeterminados. ¿Desea cambiar el tipo de datos de todos modos? ",deleteAlert:"Eliminar parámetro de informe"},assignData:{title:"Parámetro",availableValue:"Valor disponible",defaultValue:"Valor por defecto",none:"Ninguna",specify:"Especificar",query:"Valor de consulta",ok:"DE ACUERDO",cancel:"Cancelar",availableFields:{specifyDescriptionText:"Agregue los valores disponibles para los parámetros:",queryDescriptionText:"Elija el conjunto de datos y los campos para los valores disponibles:",nameFieldWaterMark:"Etiqueta",valueFieldWaterMark:"Valor"},defaultFields:{specifyDescriptionText:"Agregue los valores predeterminados para los parámetros:",queryDescriptionText:"Elija el conjunto de datos y los campos para los valores predeterminados:",defValueWaterMark:"Elija el valor predeterminado "},datasetWaterMark:"Elegir valor del conjunto de datos",valueWaterMark:"Elegir valor",lableWaterMark:"Elegir etiqueta",add:"AÑADIR",datasetLableText:"Conjunto de datos",valueLableText:"Campo de valor",labelFieldText:"Etiqueta de campo",errorMessage:{boolTypeCheck:"El valor no es un valor booleano.",dateTypeCheck:"El valor es un formato de fecha no válido.",intTypeCheck:"El valor no es un número entero.",floatTypeCheck:"El valor no es un flotador.",multipleValuesCheck:"Un parámetro multivalor no puede incluir valores nulos",datasetFieldCheck:"El campo de conjunto de datos es obligatorio.",valueFieldCheck:"Se requiere un campo de valor.",syntaxLabelField:"El valor ingresado en el campo de etiqueta no es una sintaxis de token válida.",syntaxValueField:"El valor ingresado en el campo de valor no es una sintaxis de token válida.",blankValueCheck:"El campo de valor está en blanco. El parámetro no permite valores en blanco."},closeToolTip:"Cerca"}},formatData:{title:"Formato de diálogo ",typeSelect:"Tipo",typeFormat:{numberType:{numberType:"Número",decimalPlaces:"Lugares decimales",negativeValues:"Valores negativos",showZeroAs:{showZeroAs:"Mostrar cero como",none:"(ninguna)"},representation:"Representación",repDropDwn:{thousands:"Miles de personas",millions:"Millones",billions:"Miles de millones"},useRegionFormating:"Use el formato regional",use1000Separator:"Use 1000 Separador (,)"},currency:{currencyType:"Moneda",decimalPlaces:"Lugares decimales",negativeValues:"Valores negativos",cultureCurrency:"Moneda Cultura",showZeroAs:{none:"(ninguna)"},representation:"Representación",repDropDwn:{thousands:"Miles de personas",millions:"Millones",billions:"Miles de millones"},useRegionFormating:"Use el formato regional",use1000Separator:"Use 1000 Separador (,)",includeSpace:"Incluye un espacio"},date:{dateType:"Fecha",date:"Fecha"},time:{timeType:"Hora",time:"Hora"},percentage:{percentageType:"Porcentaje",decimalPlaces:"Lugares decimales",includeSpace:"Incluye un espacio"},scientific:{scientificType:"Científico",decimalPlaces:"Lugares decimales"},custom:{customType:"Personalizado",customFormat:"Formato personalizado"}},preview:"Avance",ok:"DE ACUERDO",cancel:"Cancelar",close:"Cerca"},expression:{title:"Expresión",descriptionText:"Establecer expresión para: ",optionLabel:"Opciones",dataLabel:"DATOS",descritionLabel:"Descripción",exampleLabelText:"Ejemplo",ok:"DE ACUERDO",cancel:"Cancelar",closeToolTip:"Cerca",textAreaWaterMark:"Expresión",category:{builtInFields:"Campos incorporados",operators:"Operadores",functions:"Funciones"},parameters:"Parámetros",optionWaterMark:"Seleccione una opcion",dataWaterMark:"Seleccione una información",reportData:"No se encontraron datos de informes",description:{executionTime:"La fecha y la hora en que los informes comienzan a ejecutarse.",overallPageNumber:"El número de página general actual solo se puede usar en el encabezado o pie de página.",overallTotalPages:"El número total de páginas en el informe solo se puede usar en el encabezado y pie de página..",pageName:"El nombre de la página actual en el informe se puede usar solo en el encabezado o pie de página.",pageNumber:"El número de página actual que se puede restablecer mediante el uso de saltos de página",isInteractive:"Un booleano que indica si la solicitud de representación actual usa un formato interactivo.",renderName:"El nombre del representador registrado en el archivo de configuración RSReportServer.",reportFolder:"La ruta completa a la carpeta que contiene el informe no incluye la URL del servidor de informes.",reportName:"La URL del servidor de informes donde se ejecuta el informe.",reportServerUrl:"La URL del servidor de informes en el que se ejecuta el informe.",totalPages:"El número total de páginas en la secuencia de página continua actual solo se puede usar en el encabezado y pie de página. El número puede restablecerse mediante el uso de saltos de página.",language:"El ID de idioma del cliente que ejecuta el informe.",userID:"El ID del usuario que ejecuta el informe.",powerNumberType:"Aumenta un número al poder de otro número.",multiply:"Multiplica dos números.",integerDivision:"Divide dos números y devuelve un número entero.",floatDivision:"Divide dos números y devuelve un punto flotante.",modulus:"Divide dos números y devuelve solo el resto.",add:"Agrega dos números y se puede usar para concatenar dos cadenas.",difference:"Otorga la diferencia entre dos números o indica el valor negativo de una expresión numérica.",lesser:"Menos que.",lesserOrEqual:"Menos que o igual a.",greater:"Mas grande que.",greaterOrEqual:"Mayor qué o igual a.",equal:"Igual a.",notEqual:"No igual a.",like:"Compara dos cadenas.",isOperator:"Compara dos variables de referencia de objeto.",expression:"Genera una concatenación de cadenas de dos expresiones.",stringType:"Agrega dos números y se puede usar para concatenar dos cadenas.",and:"Realiza una conjunción lógica en dos expresiones booleanas, o una conjunción bit a bit en dos",not:"Realiza una negación lógica en una expresión booleana o en una negación a nivel de bit en una expresión numérica.",or:"Se utiliza para realizar una disyunción lógica en dos expresiones booleanas, o una disyunción bit a bit en dos valores numéricos.",xor:"Realiza una operación de exclusión lógica en dos expresiones booleanas, o en un bit exclusión en dos expresiones numéricas.",andAlso:"Realiza un cortocircuito de la conjunción lógica en dos expresiones.",orElse:"Se usa para realizar un cortocircuito de disyunción lógica en dos expresiones.",left:"Realiza un desplazamiento aritmético a la izquierda en un patrón de bits.",right:"Realiza un desplazamiento aritmético a la derecha en un patrón de bits.",asc:"Devuelve un valor entero que representa el código de carácter correspondiente a un personaje.",ascW:"Devuelve un valor entero que representa el código de carácter correspondiente a un carácter.",chr:"Devuelve el carácter asociado con el código de carácter especificado.",chrW:"Devuelve el carácter asociado con el código de carácter especificado.",filter:"Devuelve una matriz basada en cero que contiene un subconjunto de una matriz de cadenas basada en criterios de filtro especificados.",formatStringType:"Devuelve una cadena formateada según las instrucciones en una expresión de cadena de formato.",currency:"Devuelve una expresión formateada como un valor de moneda utilizando el símbolo de moneda definido en el panel de control del sistema.",dateTime:"Devuelve una expresión de cadena que representa un valor de fecha / hora.",numberType:"Devuelve una expresión formateada como un número.",percent:"Devuelve una expresión formateada como un porcentaje (es decir, multiplicado por 100).",getChar:"Devuelve un valor char que representa el carácter del índice especificado en la cadena suministrada.",inStr:"Devuelve un entero que especifica la posición de inicio de la primera aparición de una cadena dentro de otra.",inStrRev:"Devuelve la posición de la primera aparición de una cadena dentro de otra, empezando por el lado derecho de la cuerda.",join:"Devuelve una cadena creada uniendo una cantidad de subcadenas en una matriz.",lCase:"Devuelve una cadena o un carácter convertido a minúscula.",leftStringType:"Devuelve una cadena que contiene un número específico de caracteres del lado izquierdo de una cadena.",stringLength:"Devuelve un número entero que contiene el número de caracteres en una cadena o el número.",lSet:"Devuelve una cadena alineada a la izquierda que contiene la cadena especificada ajustada a la longitud especificada.",leftTrim:"Devuelve la cadena sin espacios finales del lado izquierdo en la cadena dada.",middle:"Devuelve una cadena que contiene un número específico de caracteres de una cadena.",replace:"Devuelve una cadena en la cual una subcadena especificada ha sido reemplazada por otra.",rightString:"Devuelve una cadena en la cual una subcadena especificada ha sido reemplazada por otra.",rightSet:"Devuelve una cadena alineada a la derecha que contiene la cadena especificada ajustada a la longitud especificada.",rightTrim:"Devuelve la cadena sin espacios finales del lado derecho en la cadena dada.",stringSpace:"Devuelve una cadena que consta de la cantidad de espacios especificada.",splitString:"Devuelve una matriz unidimensional basada en cero que contiene un número especificado de subcadenas.",strComp:"Devuelve -1, 0 o 1, según el resultado de una comparación de cadenas.",strConv:"Devuelve una cadena convertida como se especifica.",duplicateString:"Devuelve una cadena u objeto que consiste en el carácter especificado repetido el número de veces especificado.",strReverse:"Devuelve una cadena en la que se invierte el orden de los caracteres de una cadena especificada.",trim:"Devuelve la cadena sin espacios finales en la cadena dada",upperCase:"Devuelve una cadena o carácter que contiene la cadena especificada convertida a mayúscula.",cDate:"Convertir a la fecha.",dateAdd:"Devuelve un valor de fecha que contiene valores de fecha y hora a los que se ha agregado un intervalo de tiempo especificado.",dateDiff:"Devuelve un valor largo que especifica la cantidad de intervalos de tiempo entre dos valores de fecha.",datePart:"Devuelve un valor entero que contiene el componente especificado de un valor de fecha determinado.",dateSerial:"Devuelve un valor de fecha que representa un año, mes y día especificados, con la información de hora configurada para midnight (00:00:00).",dateString:"Devuelve o establece un valor de cadena que representa la fecha actual de acuerdo con su sistema.",dateValue:"Devuelve un valor de fecha que contiene la información de fecha representada por una cadena, con la información de tiempo.",day:"Devuelve un valor entero del 1 al 31 que representa el día del mes.",format:"Devuelve una expresión de cadena que representa el valor de fecha / hora.",hour:"Devuelve un valor entero de 0 a 23 que representa la hora del día.",minute:"Devuelve un valor entero de 0 a 59 que representa el minuto de la hora.",month:"Devuelve un valor entero del 1 al 12 que representa el mes del año.",monthName:"Devuelve un valor de cadena que contiene el nombre del mes especificado.",now:"Devuelve un valor de fecha que contiene la fecha y hora actual de acuerdo con su sistema.",second:"Devuelve un valor entero de 0 a 59 que representa el segundo del minuto.",timeOfDay:"Devuelve o establece un valor de fecha que contiene la hora actual del día de acuerdo con su sistema.",timer:"Devuelve un valor doble que representa el número de segundos transcurridos desde la medianoche.",timeSerial:"Devuelve un valor de fecha que representa una hora, minuto y segundo especificados, con la información de fecha establecida con relación al 1 de enero del año 1.",timeString:"Devuelve o establece un valor de cadena que representa la hora actual del día de acuerdo con su sistema.",timeValue:"Devuelve un valor de fecha que contiene la información de tiempo representada por una cadena, con la información de fecha establecida al 1 de enero del año 1.",timeToday:"Devuelve o establece un valor de fecha que contiene la fecha actual de acuerdo con su sistema.",timeWeekday:"Devuelve un valor entero que contiene un número que representa el día de la semana.",timeWeekdayName:"Devuelve un valor de cadena que contiene el nombre del día de la semana especificado.",year:"Devuelve un valor entero del 1 al 9999 que representa el año.",abs:"Devuelve el valor absoluto de un número de punto flotante de precisión simple.",acos:"Devuelve el ángulo cuyo coseno es el número especificado.",asin:"Devuelve el ángulo cuyo seno es el número especificado.",atan:"Devuelve el ángulo cuya tangente es el número especificado.",atan2:"Devuelve el ángulo cuya tangente es el cociente de dos números especificados.",bigMultiply:"Produce el producto completo de dos números de 32 bits.",ceiling:"Devuelve el entero más pequeño que es mayor o igual que el entero especificado.",cos:"Devuelve el coseno del ángulo especificado.",cosh:"Devuelve el coseno hiperbólico del ángulo especificado.",exponent:"Devuelve e elevado a la potencia especificada.",fixNumberType:"Devuelve una parte entera de un número.",floor:"Devuelve el entero más grande menor o igual que el entero especificado.",integer:"Devuelve una parte entera de un número.",logrithm:"Devuelve el logaritmo natural (base e) de un número especificado.",logrithm10:"Devuelve el logaritmo de base 10 de un número especificado.",maximum:"Devuelve el valor máximo de todos los valores no nulos de la expresión especificada.",minimum:"Devuelve el valor mínimo de todos los valores no nulos de la expresión especificada.",power:"Devuelve un número especificado elevado a la potencia especificada.",random:"Devuelve un número aleatorio de tipo único.",round:"Redondea un valor de coma flotante de precisión doble al entero más cercano.",sign:"Devuelve un valor que indica el signo de un entero con signo de 8 bits.",sin:"Devuelve el seno del ángulo especificado.",sinh:"Devuelve el seno hiperbólico del ángulo especificado.",squareRoot:"Devuelve la raíz cuadrada de un número especificado.",tangent:"Devuelve la tangente del ángulo especificado.",tangentH:"Devuelve la tangente hiperbólica del ángulo especificado.",isArray:"Devuelve un valor booleano que indica si la variable apunta a una matriz.",isDate:"Devuelve un valor booleano que indica si una expresión representa un valor válido",isNothing:"Devuelve un valor booleano que indica si una expresión no tiene ningún objeto.",isNumeric:"Devuelve un valor booleano que indica si una expresión puede ser evaluado como un número.",flowChoose:"Selecciona y devuelve un valor de una lista de argumentos.",flowIIf:"Devuelve uno de dos objetos según la evaluación de una expresión.",switchFlow:"Evalúa una lista de expresiones y devuelve un valor de objeto correspondiente a la primera expresión en la lista que es verdadera.",avg:"Devuelve el promedio de todos los valores no nulos de la expresión especificada.",count:"Devuelve un recuento de los valores de la expresión especificada.",countDistinct:"Devuelve un recuento de todos los valores distintos de los especificados.",countRows:"Devuelve un recuento de filas dentro del alcance especificado.",first:"Devuelve el primer valor de la expresión especificada.",last:"Devuelve el último valor de la expresión especificada.",standardDev:"Devuelve la desviación estándar de todos los valores no nulos de los especificados.",standardDevP:"Devuelve la desviación estándar de la población de todos los valores no nulos de la expresión especificada.",sum:"Devuelve una suma de los valores de la expresión especificada.",variance:"Devuelve la varianza de todos los valores no nulos de la expresión especificada.",varianceP:"Devuelve la varianza poblacional de todos los valores no nulos de la expresión especificada.",runningValue:"Utiliza una función específica para devolver un agregado en ejecución de la expresión especificada",aggregate:"Devuelve un agregado personalizado de la expresión especificada, según lo definido por el proveedor de datos.",doubleDeclining:"Devuelve un doble que especifica la depreciación de un activo para un período de tiempo específico utilizando el método de saldo decreciente doble o cualquier otro método que especifique.",futureValue:"Devuelve el valor doble especificando el valor futuro de una anualidad basada en pagos fijos periódicos y una tasa de interés fija.",interestPayment:"Devuelve el valor doble que especifica el pago de intereses para un período determinado de una anualidad basado en pagos fijos periódicos y una tasa de interés fija.",numberOfPeriods:"Devuelve un valor doble que especifica el número de períodos de una anualidad basada en pagos fijos periódicos y una tasa de interés fija.",annuityPayment:"Devuelve un valor doble que especifica el pago de una anualidad basada en pagos fijos periódicos y una tasa de interés fija.",principalPayment:"Devuelve un valor doble que especifica el pago del principal para un período determinado de una anualidad basado en pagos fijos periódicos y una tasa de interés fija.",presentValue:"Devuelve un valor doble que especifica el valor presente de una anualidad basada en pagos fijos periódicos a ser pagados en el futuro y una tasa de interés fija.",rateOfInterest:"Devuelve un valor doble que especifica la tasa de interés por período para una anualidad.",straightLine:"Devuelve un valor doble que especifica la depreciación lineal de un activo por un período único.",sumOfYearsDigits:"Devuelve un valor doble que especifica la depreciación de los dígitos de la suma de años de un activo por un período especificado.",convertBool:"Convertir a Boolean.",convertByte:"Convertir a byte.",convertChar:"Convertir a char.",convertDate:"Convertir a la fecha.",convertDouble:"Convertir a doble.",convertDecimal:"Convertir a decimal.",convertInteger:"Convertir a entero.",convertLong:"Convertir a largo.",convertObject:"Convertir a objeto.",convertShort:"Convertir a corto.",convertSingle:"Convertir a single.",convertString:"Convertir a cadena.",fix:"Devuelve una parte entera de un número.",hexaDecimal:"Devuelve una cadena que representa el valor hexadecimal de un número.",integerPortion:"Devuelve una parte entera de un número.",octal:"Devuelve una cadena que representa el valor octal de un número.",stringOfNumber:"Devuelve una cadena que representa un número.",stringAsNumeric:"Devuelve números en una cadena como un valor numérico del tipo apropiado.",inScope:"Devuelve verdadero si la instancia actual está dentro del alcance especificado.",depthLevel:"Devuelve un número entero basado en cero que representa el nivel de profundidad actual.",previous:"Devuelve el valor de la expresión para la fila de datos anterior.",rowNumber:"Devuelve un conteo en ejecución de todas las filas en el alcance especificado."}},dataAssign:{measures:"Medidas",dimensions:"Dimensiones",addDatasource:"Agregar origen de datos",errorMessagePrefix:"Aún no ha configurado una fuente de datos.",errorMessageSuffix:"Agregue una fuente de datos para vincular datos para informar elementos en su diseñador.",search:"Buscar",dragOnDrop:"Arrastrar y soltar"},reportProperty:{header:"Encabezamiento",body:"Cuerpo",footer:"Pie de página",report:"Informe",basicSettings:{categoryName:"Ajustes básicos",background:"Color de fondo",borderTypes:{border:"Frontera",borderLeft:"Izquierda",borderTop:"Parte superior",borderRight:"Derecha",borderBottom:"Fondo"},borderStyles:{solid:"Sólido",none:"Ninguna",double:"Double",dashed:"Disparo",dotted:"Punteado"}},generalSettings:{categoryName:"General",printFirstPage:"Imprimir en la primera página",printLastPage:"Imprimir en la última página"},size:{sizeLabel:"Tamaño",paddingTypes:{padding:"Relleno",paddingLeft:"Izquierda",paddingTop:"Parte superior",paddingRight:"Derecha",paddingBottom:"Fondo"}},position:{categoryPosition:"Posición",positionLabel:"Posición",left:"Izquierda",top:"Parte superior",sizeLabel:"Tamaño",width:"Anchura",height:"Altura"},codeModule:{code:"Código"},margin:{categoryName:"Margen",categoryHeader:"Margen",types:{left:"Izquierda",right:"Derecha",bottom:"Fondo",top:"Parte superior"}},pageUnit:{header:"Unidades de página",label:"Unidad de la página",types:{inches:"Pulgadas",centimeters:"Centímetros",pixels:"Píxel",points:"Puntos",millimeters:"Milímetros",picas:"Picas"}},columns:{header:"Columna de la página",label:"Columnas",columnSpacing:"Espaciado de columna"},paperSize:{orientation:"Orientación",header:"Tamaño de papel",label:"Tamaño de papel",orientationTypes:{landScape:"Paisaje",portrait:"Retrato"},types:{a3Size:"A3",a4Size:"A4",b4Size:"B4(JIS)",b5Size:"B5(JIS)",envelope:"Sobre #10",envelopeMonarch:"Sobre Monarca",executive:"Ejecutivo",legal:"Legal",letter:"Carta",tabloid:"Tabloide",custom:"Personalizado"}},styleTooltip:"Estilo",colorTooltip:"Color",sizeTooltip:"tamaño"},textBoxProperty:{contents:{categoryName:"Contenido",content:"Contenido"},basicSettings:{categoryName:"Ajustes básicos",font:{categoryName:"Fuente",defaultStyle:"Defecto",normal:"Normal",italic:"Itálico"},fontStyle:{categoryName:"Estilo de fuente",defaultStyle:"Defecto",normal:"Normal",thin:"Delgado",extraLight:"Extra ligero",light:"Ligero",medium:"Medio",semiBold:"Semi Negrita",bold:"Negrita",extraBold:"Extra Negrita",heavy:"Pesado"},textDecoration:{categoryName:"Decoración de texto",defaultStyle:"Defecto",none:"Ninguna",underline:"Subrayar",lineThrough:"Línea a través",overline:"Overline"},format:"Formato"},alignment:{categoryName:"Alineación",textAlignment:{categoryName:"Alineación del texto",defaultStyle:"Defecto",left:"Izquierda",center:"Centrar",right:"Derecha"},verticalAlignment:{categoryName:"Alineamiento vertical",defaultStyle:"Defecto",top:"Parte superior",middle:"Medio",bottom:"Fondo"},lineSpacing:"Linjehöjd"},appearance:{categoryName:"Apariencia",borderTypes:{border:"Frontera",borderLeft:"Izquierda",borderTop:"Parte superior",borderRight:"Derecha",borderBottom:"Fondo"},borderStyles:{solid:"Sólido",none:"Ninguna",double:"Doble",dashed:"Disparo",dotted:"Punteado"},background:"Color de fondo"},link:"Enlazar",linkReport:"Informe de enlace",position:{categoryPosition:"Posición",positionLabel:"Posición",sizeLabel:"Tamaño",left:"Izquierda",top:"Parte superior",width:"Anchura",height:"Altura",direction:{categoryName:"Dirección",leftToRight:"De izquierda a derecha",rightToLeft:"De derecha a izquierda"}},visibility:{categoryName:"Visibilidad",visible:"Visible",toggleItem:"Alternar artículo",intialToggleState:"Estado de alternancia inicial"},miscellaneous:{categoryName:"Diverso",canGrow:"Puede crecer",canShrink:"Puede reducir"},paragraphSettings:{categoryName:"Configuración de párrafo",textAlignment:{categoryName:"Alineación del texto",defaultStyle:"Defecto",left:"Izquierda",center:"Centrar",right:"Derecha"},indent:{categoryName:"Sangrar",leftIndent:"Izquierda",rightIndent:"Derecha"},space:{categoryName:"Espacio",topSpace:"Parte superior",bottomSpace:"Fondo"},listLevel:{categoryName:"Nivel de lista",zeroLevel:"",oneLevel:"",twoLevel:"",threeLevel:"",fourLevel:""},listStyle:{categoryName:"Estilo de lista",none:"Ninguna",numbered:"Numerado",bulleted:"Bulleted"}},padding:{padding:"Relleno",paddingLeft:"Izquierda",paddingTop:"Parte superior",paddingRight:"Derecha",paddingBottom:"Fondo"},contextMenu:{cut:"Cortar",copy:"Dupdo",paste:"Pegar",expression:"Expresión",pasteAlert:"Su navegador no admite el acceso directo al portapapeles. Utilice el método abreviado de teclado Ctrl + V en lugar de la operación de pegado."},fontStyleTooltip:"Estilo",fontWeightTooltip:"Peso",fontSizeTooltip:"tamaño",fontColorTooltip:"Color",fontFamilyTooltip:"Familia tipográfica",styleTooltip:"Estilo",colorTooltip:"Color",sizeTooltip:"tamaño",selectedText:"Texto seleccionado"},designPanel:{headerText:"Encabezamiento",footerText:"Pie de página",pasteAlert:"Solo se admiten elementos básicos en el área de encabezado y pie de página",pasteTitle:"Pegar"},customProperty:{position:{categoryPosition:"Posición",positionLabel:"Posición",left:"Izquierda",top:"Parte superior",sizeLabel:"tamaño",width:"Anchura",height:"Altura"},appearance:{categoryAppearance:"Apariencia",borderTypes:{border:"Frontera",borderLeft:"Izquierda",borderTop:"Parte superior",borderRight:"Derecha",borderBottom:"Fondo"},borderStyles:{solid:"Sólido",none:"Ninguna",double:"Doble",dashed:"Dashed",dotted:"Punteado"},backGround:"Fondo"},visibility:{categoryName:"Visibilidad",visible:"Visible"},styleTooltip:"Estilo",colorTooltip:"Color",sizeTooltip:"tamaño"},tablixProperty:{data:{categoryName:"Datos",datasetName:"Conjunto de datos",datasetNone:"Ninguna"},appearance:{categoryName:"Apariencia",borderTypes:{border:"Frontera",borderLeft:"Izquierda",borderTop:"Parte superior",borderRight:"Derecha",borderBottom:"Fondo"},borderStyles:{solid:"Sólido",none:"Ninguna",double:"Doble",dashed:"Disparo",dotted:"Punteado"},backGround:"Color de fondo"},miscellaneous:{categoryName:"Diverso",noRowsMessage:"Mensaje sin filas",pageName:"Nombre de la página",keepTogether:"Mantenerse juntos",repeatColumnHeaders:"Repetir encabezados de columna",repeatRowHeaders:"Repetir encabezados de fila",fixedColumnHeaders:"Cabeceras de columna fija",fixedRowHeaders:"Cabeceras de fila fija"},font:{categoryName:"Fuente",defaultStyle:"Defecto",normal:"Normal",italic:"Itálico"},fontStyle:{categoryName:"Estilo de fuente",defaultStyle:"Defecto",normal:"Normal",thin:"Delgado",extraLight:"Extra ligero",light:"Ligero",medium:"Medio",semiBold:"Semi Negrita",bold:"Negrita",extraBold:"Extra Negrita",heavy:"Pesado"},textDecoration:{categoryName:"Decoración de texto",defaultStyle:"Defecto",none:"Ninguna",underline:"Subrayar",lineThrough:"Línea a través",overline:"Overline"},alignment:{categoryName:"Alineación",textAlignment:{categoryName:"Alineación del texto",defaultStyle:"Defecto",left:"Izquierda",center:"Centrar",right:"Derecha"},verticalAlignment:{categoryName:"Alineamiento vertical",defaultStyle:"Defecto",top:"Parte superior",middle:"Medio",bottom:"Fondo"}},padding:{padding:"Relleno",paddingLeft:"Izquierda",paddingTop:"Parte superior",paddingRight:"Derecha",paddingBottom:"Fondo"},position:{categoryPosition:"Posición",positionLabel:"Posición",left:"Izquierda",top:"Parte superior",sizeLabel:"Tamaño",width:"Anchura",height:"Altura"},visibility:{categoryName:"Visibilidad",visible:"Visible",toggleItem:"Palanca"},staticGroupProp:{categoryName:"Ajustes básicos",filters:"Filtros",sorts:"Clases",fixedData:"Datos fijos",groupExp:"Los grupos",hideIfNoRows:"Ocultar si no hay filas",keepWithGroup:"Mantener con el grupo",repeatOnNewPage:"Repetir en nueva pagina",afterGroup:"Después",beforeGroup:"antes de",pageBreak:{categoryName:"Salto de página",enablePageBreak:"Habilitar salto de página",breakLocation:{categoryName:"Lugar de descanso",none:"Ninguna",start:"Comienzo",end:"Fin",startAndEnd:"InicioAndEnd",between:"Entre"},pageNumberReset:"Restablecer número de página"}},fontStyleTooltip:"Estilo",fontWeightTooltip:"Peso",fontSizeTooltip:"tamaño",fontColorTooltip:"Color",fontFamilyTooltip:"Familia tipográfica",styleTooltip:"Estilo",colorTooltip:"Color",sizeTooltip:"tamaño",tablixMember:"Miembro de Tablix"},rowColumnGroup:{rowGroupLable:"Grupos de fila",columnGroupLable:"Grupos de columnas",tablixAlertHeader:"Tablix",alertMessage:"Habilitar la opción expandir para seleccionar el elemento de informe tablix",contextMenu:{addgroup:"Añadir grupo",advanced:"Avanzado",deletegroup:"Eliminar grupo",addtotal:"Agregar total",groupproperties:"Propiedades del grupo",addColumnGroup:"Agregar grupo de columnas",addRowGroup:"Agregar grupo de filas"},contextSubMenu:{adjacentafter:"Adyacente después",adjacentbefore:"Adyacente antes",childgroup:"Grupo de niños",parentgroup:"Grupo de padres",totalafter:"Después",totalbefore:"antes de",childGroupAlert:"No se puede insertar el grupo dentro de los detalles."}},tablixContextMenu:{rowMenu:{insertRow:"Insertar fila",above:"Encima",below:"Abajo"},columnMenu:{insertColumn:"Insertar columna",left:"Izquierda",right:"Derecha"},rowGroupMenu:{insideGroupAbove:"Grupo interno - arriba",insideGroupBelow:"Dentro del Grupo - Abajo",outsideGroupAbove:"Grupo exterior - arriba",outsideGroupBelow:"Grupo Exterior - Abajo"},columnGroupMenu:{insideGroupLeft:"Grupo interno - izquierda",insideGroupRight:"Dentro del grupo - derecha",outsideGroupLeft:"Grupo exterior - izquierda",outsideGroupRight:"Grupo exterior - derecha"},deleteRows:"Eliminar filas",deleteColumns:"Eliminar columnas",rowVisibility:"Visibilidad de la fila",columnVisibility:"Visibilidad de la columna",tablixProperties:"Propiedades de Tablix",splitcells:"Células partidas",mergecells:"Combinar células",groupMenu:{adjacentAbove:"Adyacente arriba",adjacentBelow:"Adyacente a continuación",adjacentleft:"Adyacente a la izquierda",adjacentright:"Derecho adyacente",childGroup:"Grupo infantil",parentGroup:"Grupo de padres",deleteRowGroup:"Eliminar grupo de filas",deleteColGroup:"Eliminar grupo de columnas",addRowGroup:"Grupo de filas",addColGroup:"Grupo de columnas"},reportItemMenu:{insertItem:"Insertar",chart:"Gráfico"},totalMenu:{total:"Añadir total",row:"Fila",column:"Columna",before:"antes de",after:"Después"},cellMenu:{addExpression:"Añadir expresión",editExpression:"Editar Expresión",datasource:"Añadir fuente de datos",noFields:"No hay campos",addText:"Añadir texto",editText:"Editar texto"},basicItems:{deleteItem:"Borrar",cut:"Cortar",copy:"Dupdo",paste:"Pegar"}},tablixAlertDialog:{ok:"DE ACUERDO",cancel:"Cancelar",closeToolTip:"Cerrar",deleteRowTitle:"Eliminar filas",deleteRow:"Eliminar solo las filas",deleteRowGroup:"Eliminar filas y grupos asociados",deleteRowContent:"Eliminar opciones de fila",deleteBodyRow:"El cuerpo de Tablix debe contener al menos una fila.",deleteColumnTitle:"Eliminar columnas",deleteColumn:"Eliminar columnas solamente",deleteColumnGroup:"Eliminar columnas y grupos asociados.",deleteColumnContent:"Eliminar opciones de columna",deleteBodyColumn:"El cuerpo de Tablix debe contener al menos una columna.",deleteGroup:"Eliminar solo el grupo",deleteGroupRowColumn:"Eliminar grupo y filas y columnas relacionadas",deleteGroupTitle:"Eliminar grupo",deleteGroupContent:"Eliminar opciones de grupo",deleteStructure:"Estructura de grupo no disponible.",removeRowAlert:"Error al eliminar la fila en el elemento del informe Tablix ",removeRow:"Eliminar filas",removeColumn:"Eliminar columnas",addRow:"Añadir fila",addColumn:"Añadir columna",removeColumnAlert:"Error al eliminar la columna en el elemento del informe Tablix",addRowAlert:"Error al agregar la fila en el elemento del informe Tablix",addColumnAlert:"Error al agregar la columna en el elemento de informe de Tablix"},cellMergingAlertInfo:{merge:"Combinar células",mergeAlert:"Error al combinar las celdas en el elemento de informe Tablix",split:"Células partidas",splitAlert:"Error al dividir las celdas en el ítem del reporte Tablix"},tablixAlertInfo:{addGroup:"Añadir grupo",removeGroup:"Eliminar grupo",adjacentAfterAlert:"Error al agregar el grupo adyacente en la estructura de jerarquía",adjacentBeforeAlert:"Error al agregar el grupo adyacente en la estructura de jerarquía",childGroupALert:"Error al agregar grupo secundario en la estructura de jerarquía",title:"Elemento de informe de Tablix",parentGroupAlert:"Error al agregar el grupo padre en la estructura de jerarquía",removeGroupAlert:"Error al eliminar el grupo en la estructura de jerarquía",selectedMemberAlert:"El miembro seleccionado no es un miembro del grupo",pasteActionAlert:"La información no se puede publicar porque el área de copia y el área de pegado no tienen el mismo tamaño y forma.",pasteTitle:"Pegar"},tablixGroup:{title:"Grupo Tablix",headerTxt:"Etiqueta de grupo",groupBy:"Agrupar por:",chooseField:"Elegir campo",showDetailData:"Mostrar datos detallados",addGroupHeader:"Añadir encabezado",addGroupFooter:"Añadir pie de página",ok:"DE ACUERDO",cancel:"Cancelar",closeToolTip:"Cerca"},tablixDataAssignMenu:{datasource:"Añadir fuente de datos",addExpression:"Añadir expresión",editExpression:"Editar Expresión",addText:"Añadir texto",editText:"Editar texto",search:"Buscar",noFieldsFound:"No se encontraron campos"},tablixTotalAlert:{totalHeader:"Añadir encabezado total",totalStatic:"Añadir total",headerMessage:"Error al agregar la fila o columna total al encabezado del grupo en el elemento de informe Tablix",staticMessage:"Error al agregar la fila o columna total al cuerpo de Tablix en el elemento de informe de Tablix"},tablixAddTextDialog:{save:"Salvar",add:"Añadir",cancel:"Cancelar",closeToolTip:"Cerrar",addText:"Añadir texto",editText:"Editar texto"},queryDesigner:{storeParameter:{title:"Parámetros",ok:"DE ACUERDO",cancel:"Cancelar",parameterLable:"Parámetro",nullLable:"Nulo",valueLable:"Valor",dataTypeLable:"Tipo de datos",closeToolTip:"Cerca"},parameter:{title:"Parámetros",ok:"DE ACUERDO",cancel:"Cancelar",parameterLable:"Parámetro",nullLable:"Nulo",valueLable:"Valor",dataTypeLable:"Tipo de datos",auto:"Auto",text:"texto",closeToolTip:"Cerca"},filter:{title:"Filtros de consulta",descriptionLable:"Lista de filtros de tabla",add:"AÑADIR",save:"DE ACUERDO",cancel:"Cancelar",nullLable:"Nulo",trueLable:"Cierto",falseLable:"Falso",parameterTooltip:"Incluir como parámetro",closeToolTip:"Cerca",intOperatorType:{equals:"Igual",doesNotEqual:"No es igual",greaterThan:"Mas grande que",greaterThanOrEqual:"Pero grande que",lessThan:"Menos que",lessThanOrEqual:"Menos que o igual a",between:"Entre",notBetween:"No entre"},stringOpertorType:{equals:"Igual",startsWith:"Comienza con",endWith:"Termina con",contains:"Contiene",notContains:"No contiene"},errorMessage:{dateValidation:"El valor es un formato de fecha no válido.",commonContent:"El filtro en ",booleanValidation:" no tiene ningún valor para filtrar. Proporcione los valores para el filtro.",stringValidation:" no tiene los valores adecuados para filtrar. "}},previewArea:{dataPreview:"Vista previa de datos",noRecords:"No hay registros que mostrar",generatePreview:"Generar vista previa",executeRecords:"Ejecutar para previsualizar registros",record:"Grabar",records:"Archivos",retrieved:"Obtenido",loadRecord:"Carga más",update:"Actualizar"},schemaArea:{search:"Buscar",matchesFound:"No se encontraron coincidencias",rename:"Rebautizar",aggregation:"Agregación",dialogHeader:"Conjunto de datos",alertMessage:{datasourceAlert:"Seleccione una fuente de datos para configurar el conjunto de datos de informe",removeTable:"Las tablas asociadas a continuación se eliminarán con esto",duplicateName:"El nombre de columna especificado ya existe",duplicateDatasetName:"El nombre especificado ya existe en la lista de DataSet",datasetSpecialCharacter:"El nombre no debe contener espacios ni caracteres especiales",specialCharacter:"El nombre de la columna no debe contener caracteres especiales.",switcherAlert:"El cambio al diseñador visual descartará los cambios manuales realizados en la consulta. ¿Quieres usar el diseñador visual de todos modos? "},errorMessage:{specifyName:"Especifique el nombre de la columna",specifyDatasetName:"Especifique el nombre del conjunto de datos",previewFailed:"Conjunto de datos no pudo obtener una vista previa de la tabla seleccionada",specifyQuery:"Especifique la consulta DataSet",selectTable:"Seleccione la tabla para guardar el conjunto de datos",queryFailed:"Conjunto de datos no pudo guardar la consulta de la tabla seleccionada",tableProcedure:"Conjunto de datos no pudo recuperar el procedimiento de tabla seleccionado"}},toolBar:{datasourceLable:"Fuente de datos",datasetName:"Nombre",run:"correr",join:"Unirse",expression:"Expresión",filter:"Filtrar",code:"Código",finish:"Terminar",cancel:"Cancelar",parameter:"Parámetro",datasourceWaterMark:"Seleccione una fuente de datos",autoPreview:"Vista previa automática"},joiner:{title:"Query Joiner",descriptionLable:"Lista de relaciones de tabla",add:"AÑADIR",save:"DE ACUERDO",cancel:"Cancelar",closeToolTip:"Cerca",addField:"Agregue campo",leftTableWaterMark:"Tabla izquierda",rightTableWaterMark:"Mesa derecha",leftFieldWaterMark:"Jardín izquierdo",rightFieldWaterMark:"El jardín derecho",operatorWaterMark:"Operador",joinTypeWaterMark:"Unirse al tipo",joinTypes:{inner:"Interior",outer:"Izquierda Exterior",rightOuter:"Derecha Exterior",fullOuter:"Completo exterior"},errorMessage:{removeField:"Cada relación debe tener una condición de campo. Por lo tanto, no permite borrar este campo ",noRelationAlert:" no tiene relación con otras tablas",selectLeftTable:"Seleccione el valor de la tabla izquierda",selectRightTable:"Seleccione el valor de la tabla correcta",selectRelation:"Seleccione la relación para las tablas",selectLeftColumn:"Seleccione el valor de la columna izquierda del campo fila #",selectRightColumn:"Seleccione el valor de la columna derecha de la fila del campo #",selectOperator:"Seleccione el operador de la fila de campo #",relationExists:"Ya existe relación entre tablas"}},credentialDialog:{title:"Diálogo de credenciales",userName:"Nombre de usuario",password:"Contraseña",userNameWaterMark:"Nombre de usuario",passwordWaterMark:"Contraseña",userNameErrorMessage:"Por favor ingrese el nombre de usuario",passwordErrorMessage:"Por favor, ingrese contraseña",connect:"Conectar",close:"Cerca"},queryExpression:{title:"Expresiones de consulta",functionLabel:"Funciones",columnLabel:"Configuración de columna",expressionLabel:"Expresión",nameLabel:"Nombre",descriptionLabel:"Descripción ",exampleLabelText:"Ejemplo",ok:"Salvar",cancel:"Cancelar",add:"AÑADIR",textAreaWaterMark:"Expresión de consulta",nameFieldWaterMark:"Nombre de expresión",closeToolTip:"Cerca",errorMessage:{saveAlert:"La expresión no se guarda. ¿Quieres guardar y continuar?",bracketSyntax:"Sintaxis incorrecta cerca de abrir / cerrar paréntesis.",parseAlert:"El diseñador de informes no pudo analizar la expresión especificada.",nameAlert:"El campo de nombre no debe estar vacío.",emptyAlert:"El campo de expresión no debe estar vacío.",duplicateName:"El nombre de expresión especificado ya existe",specialCharacter:"El nombre de la expresión no debe contener caracteres especiales.",referenceError:"¡La columna no puede ser referida dentro de su propia expresión!",invalidSyntax:"Sintaxis inválida cerca de Abrir / Cerrar grupo (s).",retrieveExpression:"El diseñador de informes no pudo recuperar la expresión especificada."},datasetTitle:"Conjunto de datos",expressions:{all:"Todas",numbers:"Números",logical:"Lógico",conditional:"Condicional",date:"Fecha",stringType:"Cuerda",text:"Texto",miscellenuous:"Diverso ",abs:"Devuelve el valor absoluto de la expresión dada.",acos:"Devuelve el coseno inverso (también conocido como arcocoseno) de la expresión numérica dada.",asin:"Devuelve el seno inverso (también conocido como arcoseno) de la expresión numérica dada.",atan:"Devuelve la tangente inversa (también conocida como arcotangente) de la expresión numérica dada.",cos:"Devuelve el coseno del ángulo especificado en radianes de la expresión dada.",degree:"Devuelve el ángulo en grados para el que se especifica en radianes de la expresión numérica dada.",exponent:"Devuelve el valor exponencial de la expresión dada.",logrithm:"Devuelve el logaritmo de la expresión dada a la base especificada.",pi:"Devuelve el valor constante de PI.",power:"Devuelve el valor de la expresión dada (expresión1) a la potencia especificada (expresión2).",radians:"Devuelve el ángulo en radianes para el que se especifica en grados en la expresión numérica dada.",round:"Devuelve un valor redondeado.",sign:"Devuelve un valor que representa el signo positivo (+1), cero (0) o negativo (-1) de la expresión numérica dada.",sin:"Devuelve el seno del ángulo especificado en radianes de la expresión dada.",squareRoot:"Devuelve la raíz cuadrada de la expresión numérica dada.",tan:"Devuelve la tangente de la expresión numérica dada.",ifCondition:"Devuelve una parte verdadera o una parte falsa, según la evaluación de la expresión.",ifNull:"Si la expresión es numérica / cadena / fecha, devuelve la primera expresión. Si la primera expresión es NULL, devuelve la segunda expresión.",isNotNull:"Si numérica / cuerda / expresión de fecha es NULL, devuelve una cadena que representa falso; de lo contrario representa verdadero.",isNull:"Si numérica / cuerda / expresión de fecha es NULL, devuelve una cadena que representa verdadero; de lo contrario representa falso.",and:"Devuelve verdadero si ambas expresiones se evalúan como verdaderas.",notOperation:"Devuelve verdadero si ambas expresiones se evalúan como verdaderas.",orOperation:"Devuelve verdadero si alguna de las expresiones se evalúa como verdadera.",addDate:"Agrega la cantidad de días a la fecha especificada.",name:"Devuelve una cadena que representa la parte de fecha especificada de la expresión de fecha dada.",part:"Devuelve un valor entero que representa la parte de fecha especificada de la expresión de fecha dada.",sub:"Devuelve la fecha restada de la fecha especificada.",day:"Devuelve un valor numérico que representa la parte del día de la fecha especificada.",daydiff:"Devuelve un valor numérico que representa la diferencia entre dos fechas especificadas.",hour:"Devuelve la hora de la fecha dada como un entero.",minute:"Devuelve un valor numérico que representa la parte de los minutos de la fecha resultante de la expresión de fecha especificada.",month:"Devuelve un valor numérico que representa la parte del mes de la fecha resultante de la expresión de fecha especificada.",now:"Devuelve la fecha y hora actual.",today:"Devuelve la fecha actual.",year:"Devuelve un valor numérico que representa la parte del año de la fecha resultante de la expresión de fecha especificada.",char:"Convierte el código ASCII entero dado en un personaje.",concat:"Devuelve un valor de cadena resultante de la concatenación de dos o más valores de cadena.",contains:"Devuelve true si la expresión de cadena dada contiene la expresión de subcadena especificada.",endsWith:"Devuelve verdadero si la expresión de cadena dada finaliza con la expresión de subcadena especificada.",left:"Devuelve el número especificado de caracteres desde el inicio de la expresión de cadena dada.",length:"Devuelve el logaritmo natural de la expresión dada.",lower:"Devuelve un valor de cadena convertido en minúsculas a partir de la expresión de cadena dada.",leftTrim:"Devuelve el valor de cadena con los espacios en blanco iniciales eliminados de la expresión de cadena.",maximum:"Devuelve el valor máximo en la expresión dada.",minimum:"Devuelve el valor mínimo en la expresión dada.",right:"Devuelve el número especificado de caracteres desde el final de la expresión de cadena dada.",rightTrim:"Devuelve la cadena sin espacios finales del lado derecho en la cadena dada.",startswith:"Devuelve verdadero si las expresiones de cadena dadas comienzan con la expresión de subcadena especificada.",subString:"Devuelve una longitud específica de cadena a partir del índice específico de la expresión de cadena dada.",upper:"Devuelve un valor de cadena convertido en mayúscula a partir de una expresión de cadena determinada."}},reportParameter:{title:"Parámetros",descriptionText:"Parámetros del informe",addText:"AÑADIR",ok:"DE ACUERDO",cancel:"Cancelar",nameWaterMark:"Nombre del parámetro",valueWaterMark:"Valor",closeToolTip:"Cerca"}},chartItem:{categoryItems:{yvalue:"Y Valor(s)",size:"Tamaño(s)",xvalue:"X Valor(s)",column:"Columna",row:"Fila(s)"},categoryItemsMenu:{filter:"Filtros",sort:"Ordena",group:"Grupos",expression:"Expresión",aggregate:"Agregar"}},codeDialog:{title:"Módulo de código",ok:"DE ACUERDO",cancel:"Cancelar",add:"AÑADIR",closeToolTip:"Cerrar",reference:{title:"Referencias",waterMark:"Referencia",errorMessage:"El campo esta vacio",headerText:"Lista de referencias de montaje",infoTipText:"Agregue una referencia de ensamblaje para utilizar sus funciones de ensamblaje en el informe."},classes:{title:"Las clases",classWaterMark:"Nombre de la clase",instanceWaterMark:"Nombre de instancia",classErrorMessage:"Los campos estan vacios",instanceErrorMessage:"El campo esta vacio",headerText:"Lista de instancias de clase",infoTipText:"Agregue instancias de clase para acceder a sus funciones de objeto en el informe."},code:{title:"Código",headerText:"Función de código VB para informe",infoTipText:"El motor de informes Syncfusion admite funciones de código VB para integrarse con elementos y datos del informe."}},previewData:{title:"Vista previa de datos",ok:"DE ACUERDO",cancel:"Cancelar",description:"Enlazar datos JSON para vista previa",close:"Cerrar",infoToolTip:"El informe requiere datos en formato JSON para obtener una vista previa y contiene la clave y el valor en la lista de formato de matriz.",jsonHeader:"Datos JSON:",errorMessage:"Especifique el formato JSON válido",previewDataAlert:{title:"Vista previa de datos",alertMessage:"Quieres cambiar al diseñador de informes ?"}},sampleDataSource:{sampleDSHeader:"DATOS DE MUESTRA DE IMPORTACIÓN",addText:"Añadir",searchText:"Buscar",noDataFound:"Datos no encontrados.",welcomeContentPrefix:"Comience creando una fuente de datos",welcomeContentSuffix:"Puede conectarse a sus propios datos personalizados o puede importar uno de los datos compartidos predefinidos que ofrecemos.",sampleDSText:"importar datos de muestra",exploreSampleText:"Explorar datos de muestra",accordionText:"Comience su primer informe y explore las opciones de personalización utilizando los datos de muestra.",errorMessage:"Error de red",alertHeaderText:"Datos de importacion",alertMessage:"Report Designer no pudo importar los datos del servidor de informes"},field:{title:"Campos",nameWaterMark:"Nombre del campo",sourceWaterMark:"Fuente de campo",ok:"DE ACUERDO",cancel:"Cancelar",description:"Cambiar consulta y campos calculados.",query:"Campo de consulta",calculated:"Campo calculado",fieldError:"El campo esta vacio",fieldsError:"Los campos estan vacios",add:"AÑADIR",closeToolTip:"Cerrar",invalidFormat:"El nombre del campo no debe contener espacios y caracteres especiales",sameFieldName:"El nombre del campo ya existe"},commonProperty:{commonProperties:"Propiedades comunes",basicSettings:{categoryBasicSettings:"Ajustes básicos",borderTypes:{border:"Frontera",borderLeft:"Izquierda",borderTop:"Parte superior",borderRight:"Derecha",borderBottom:"Fondo"},borderStyles:{solid:"Sólido",none:"Ninguna",double:"Doble",dashed:"Disparo",dotted:"Punteado"},backGround:"Color de fondo",styleTooltip:"Estilo",colorTooltip:"Color",sizeTooltip:"tamaño"},position:{categoryPosition:"Posición",positionLabel:"Posición",left:"Izquierda",top:"Parte superior"},visibility:{categoryVisibility:"Visibilidad",visible:"Visible"}}};
