﻿<%@ Master Language="C#" MasterPageFile="~/IncludeFiles.Master" AutoEventWireup="true" CodeBehind="Main.master.cs" Inherits="WebUI.Main" %>

<%@ Register Src="~/ServerMessage.ascx" TagPrefix="uc1" TagName="ServerMessage" %>

<asp:Content ContentPlaceHolderID="includeFilesHead" runat="server">
    <asp:ContentPlaceHolder runat="server" ID="mainHead">
    </asp:ContentPlaceHolder>
</asp:Content>
<asp:Content ContentPlaceHolderID="includeFilesBody" runat="server">
    <%-- <script type="text/javascript">
        $(function () {
            getTimezoneInfo();
        });

        function getTimezoneInfo() {
            var d = new Date();
            var timezoneOffset = d.getTimezoneOffset();
            var emptyTimezoneInfo = false;
            if (!$('#timezoneOffset').val() && !$('#dateWithTimezone').val()) {
                emptyTimezoneInfo = true;
            }

            $('#timezoneOffset').val(timezoneOffset);
            $('#dateWithTimezone').val(d.toString());
            alert('timezone');

            if (emptyTimezoneInfo) {
                //alert('reload');
                //javascript: __doPostBack('test', '');
            }
        }
    </script>--%>


    <div class="wrapper">

        <!-- Main Header -->
        <header class="main-header">

            <!-- Logo -->
            <a class="logo">
                <!-- mini logo for sidebar mini 50x50 pixels -->
                <img class="logo-mini" src="Content/images/MentalView%20logo%20compact.png" />
                <!-- logo for regular state and mobile devices -->
                <img class="logo-lg" src="Content/images/MentalView%20logo%20compact.png" style="float: left;" />
            </a>

            <!-- Header Navbar -->
            <nav class="navbar navbar-static-top" role="navigation">
                <!-- Sidebar toggle button-->
                <a id="sidebarToggle" class="sidebar-toggle" data-toggle="push-menu" role="button" runat="server">
                    <span class="sr-only">Toggle navigation</span>
                </a>
                <!-- Navbar Right Menu -->
                <div class="navbar-custom-menu">
                    <ul class="nav navbar-nav">

                        <!-- User Account Menu -->
                        <li class="dropdown user user-menu">
                            <!-- Menu Toggle Button -->
                            <a class="dropdown-toggle" data-toggle="dropdown">
                                <i class="fa fa-user"></i>
                                <!-- hidden-xs hides the username on small devices so only the image appears. -->
                                <span class="hidden-xs">
                                    <asp:Literal ID="usernameLbl" runat="server"></asp:Literal></span>
                            </a>
                            <ul class="dropdown-menu">
                                <!-- Menu Body -->
                                <%-- <li class="menu">
                                        <a href="https://adminlte.io/docs"><i class="fa fa-book"></i><span>Documentation</span></a>
                                    </li>
                                    <li>
                                        <a href="https://adminlte.io/docs"><i class="fa fa-book"></i><span>Documentation</span></a>
                                    </li>--%>
                                <!-- Menu Footer-->
                                <li class="user-footer">
                                    <%-- <div class="pull-left">
                                            <a class="btn btn-default btn-flat">Profile</a>
                                        </div>--%>
                                    <div class="pull-right">
                                        <asp:LinkButton ID="signOutLnk" class="btn btn-default btn-flat" meta:resourceKey="signOutLnk" OnClick="signOutLnk_Click" runat="server"></asp:LinkButton>
                                    </div>
                                </li>
                            </ul>
                        </li>
                        <!-- Control Sidebar Toggle Button -->
                        <%-- <li>
                                <a data-toggle="control-sidebar"><i class="fa fa-gears"></i></a>
                            </li>--%>
                    </ul>
                </div>
            </nav>
        </header>

        <!-- Left side column. contains the logo and sidebar -->
        <aside id="mainSidebar" class="main-sidebar" runat="server">

            <!-- sidebar: style can be found in sidebar.less -->
            <section class="sidebar">

                <!-- Sidebar user panel (optional) -->
                <%--<div class="user-panel">
                    <div class="pull-left image">
                        <img src="dist/img/user2-160x160.jpg" class="img-circle" alt="User Image">
                    </div>
                    <div class="pull-left info">
                        <p>Alexander Pierce</p>
                        <!-- Status -->
                        <a href="#"><i class="fa fa-circle text-success"></i>Online</a>
                    </div>
                </div>--%>

                <%--<!-- search form (Optional) -->
                    <form action="#" method="get" class="sidebar-form">
                        <div class="input-group">
                            <input type="text" name="q" class="form-control" placeholder="Search...">
                            <span class="input-group-btn">
                                <button type="submit" name="search" id="search-btn" class="btn btn-flat">
                                    <i class="fa fa-search"></i>
                                </button>
                            </span>
                        </div>
                    </form>--%>
                <!-- /.search form -->

                <!-- Sidebar Menu -->
                <ul class="sidebar-menu" data-widget="tree">
                    <%--<li class="header">HEADER</li>--%>
                    <li runat="server" id="dashboardMenuItem"><a href="Default.aspx"><i class="fa fa-caret-square-right"></i><span>
                        <asp:Literal ID="dashboardMenuTitleLbl" runat="server" meta:resourcekey="dashboardMenuTitleLbl"></asp:Literal></span></a></li>

                    <li runat="server" id="contactsMenuItem"><a href="Contacts.aspx"><i class="fa fa-id-card"></i><span>
                        <asp:Literal ID="contactsMenuTitleLbl" runat="server" meta:resourcekey="contactsMenuTitleLbl"></asp:Literal></span></a></li>

                    <li runat="server" id="partnersMenuItem"><a href="Partners.aspx"><i class="fa-solid fa-people-group"></i><span>
                        <asp:Literal ID="partnersMenuTitleLbl" runat="server" meta:resourcekey="partnersMenuTitleLbl"></asp:Literal></span></a></li>

                    <li runat="server" id="appointmentsMenuItem"><a href="Appointments.aspx"><i class="fa fa-calendar-alt"></i><span>
                        <asp:Literal ID="appointmentsMenuTitleLbl" runat="server" meta:resourcekey="appointmentsMenuTitleLbl"></asp:Literal></span></a></li>

                    <li runat="server" id="reportsMenuItem"><a href="Reports.aspx"><i class="fa fa-print"></i><span>
                        <asp:Literal ID="reportsMenuTitleLbl" runat="server" meta:resourcekey="reportsMenuTitleLbl"></asp:Literal></span></a></li>

                    <li runat="server" id="emailTemplatesMenuItem"><a href="EmailTemplates.aspx"><i class="fa fa-envelope"></i><span>
                        <asp:Literal ID="emailTemplatesTitleLbl" runat="server" meta:resourcekey="emailTemplatesTitleLbl"></asp:Literal></span></a></li>

                    <li runat="server" id="usersMenuItem"><a href="Users.aspx"><i class="fa fa-users"></i><span>
                        <asp:Literal ID="usersMenuTitleLbl" runat="server" meta:resourcekey="usersMenuTitleLbl"></asp:Literal></span></a></li>

                    <li runat="server" id="linksMenuItem"><a href="Links.aspx"><i class="fa fa-link"></i><span>
                        <asp:Literal ID="linksMenuTitleLbl" runat="server" meta:resourcekey="linksMenuTitleLbl"></asp:Literal></span></a></li>
                    <%--<li>
                        <hr style="margin: 3px 10px 3px 10px; border-top: 1px solid darkgray;" />
                    </li>--%>
                    <%--<li class="treeview">
                        <a><i class="fa fa-link"></i><span>Multilevel</span>
                            <span class="pull-right-container">
                                <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                            <li><a>Link in level 2</a></li>
                            <li><a>Link in level 2</a></li>
                        </ul>
                    </li>--%>
                </ul>
                <div runat="server" id="appointmentsSubmenu">
                    <ul class="sidebar-menu" data-widget="tree">
                        <li><a href="Appointments.aspx?View=Schedule">
                            <asp:Literal ID="appointmentsTimelineLbl" runat="server" meta:resourcekey="appointmentsTimelineLbl"></asp:Literal></a></li>
                        <li><a href="Appointments.aspx?View=Grid">
                            <asp:Literal ID="appointmentsListLbl" runat="server" meta:resourcekey="appointmentsListLbl"></asp:Literal></a></li>
                    </ul>
                </div>


                <!-- /.sidebar-menu -->

            </section>
            <!-- /.sidebar -->
        </aside>


        <!-- Content Wrapper. Contains page content -->
        <div id="contentWrapper" class="content-wrapper" runat="server">
            <!-- Content Header (Page header) -->
            <section class="content-header">
                <h1>
                    <asp:Label ID="pageHeaderLbl" runat="server" Text="Page Header"></asp:Label>
                </h1>
            </section>

            <!-- Main content -->
            <section class="content container-fluid">

                <!--------------------------
                    | Your Page Content Here |
                    -------------------------->
                <asp:ContentPlaceHolder ID="mainBody" runat="server"></asp:ContentPlaceHolder>

            </section>
            <!-- /.content -->
        </div>
        <!-- /.content-wrapper -->

        <!-- Main Footer -->
        <footer id="footer" class="main-footer" runat="server">
            <!-- To the right -->
            <%--<div class="pull-right hidden-xs">
                Anything you want
            </div>--%>
            <!-- Default to the left -->
            <strong>Copyright &copy; 2020 <a href="https://intel-soft.gr">IntelSoft</a>.</strong> All rights reserved.
       
        </footer>

        <!-- Control Sidebar -->
        <aside class="control-sidebar control-sidebar-dark">
            <!-- Create the tabs -->
            <ul class="nav nav-tabs nav-justified control-sidebar-tabs">
                <li class="active"><a href="#control-sidebar-home-tab" data-toggle="tab"><i class="fa fa-home"></i></a></li>
                <li><a href="#control-sidebar-settings-tab" data-toggle="tab"><i class="fa fa-gears"></i></a></li>
            </ul>
            <!-- Tab panes -->
            <div class="tab-content">
                <!-- Home tab content -->
                <div class="tab-pane active" id="control-sidebar-home-tab">
                    <h3 class="control-sidebar-heading">Recent Activity</h3>
                    <ul class="control-sidebar-menu">
                        <li>
                            <a href="javascript:;">
                                <i class="menu-icon fa fa-birthday-cake bg-red"></i>

                                <div class="menu-info">
                                    <h4 class="control-sidebar-subheading">Langdon's Birthday</h4>

                                    <p>Will be 23 on April 24th</p>
                                </div>
                            </a>
                        </li>
                    </ul>
                    <!-- /.control-sidebar-menu -->

                    <h3 class="control-sidebar-heading">Tasks Progress</h3>
                    <ul class="control-sidebar-menu">
                        <li>
                            <a href="javascript:;">
                                <h4 class="control-sidebar-subheading">Custom Template Design
                   
                                    <span class="pull-right-container">
                                        <span class="label label-danger pull-right">70%</span>
                                    </span>
                                </h4>

                                <div class="progress progress-xxs">
                                    <div class="progress-bar progress-bar-danger" style="width: 70%"></div>
                                </div>
                            </a>
                        </li>
                    </ul>
                    <!-- /.control-sidebar-menu -->

                </div>
                <!-- /.tab-pane -->
                <!-- Stats tab content -->
                <div class="tab-pane" id="control-sidebar-stats-tab">Stats Tab Content</div>
                <!-- /.tab-pane -->
                <!-- Settings tab content -->
                <div class="tab-pane" id="control-sidebar-settings-tab">
                    <%--<form method="post">--%>
                    <h3 class="control-sidebar-heading">General Settings</h3>

                    <div class="form-group">
                        <label class="control-sidebar-subheading">
                            Report panel usage
                 
                            <input type="checkbox" class="pull-right" checked>
                        </label>

                        <p>
                            Some information about this general settings option
                       
                        </p>
                    </div>
                    <!-- /.form-group -->
                    <%--</form>--%>
                </div>
                <!-- /.tab-pane -->
            </div>
        </aside>
        <!-- /.control-sidebar -->
        <!-- Add the sidebar's background. This div must be placed immediately after the control sidebar -->
        <div class="control-sidebar-bg"></div>
    </div>
    <!-- ./wrapper -->

    <uc1:ServerMessage runat="server" ID="serverMessage" />

    <asp:HiddenField ID="timezoneOffset" runat="server" EnableViewState="true" />
    <asp:HiddenField ID="dateWithTimezone" runat="server" EnableViewState="true" />
</asp:Content>
