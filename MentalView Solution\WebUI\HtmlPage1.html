﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <script>
        var formattedBody = "<p>& Kappa;& alpha;& lambda;& eta;& mu; έ & rho;& alpha; & sigma;& alpha;& sigmaf;,</p>\n <p>& Theta; έ & lambda;& omicron;& upsilon;& mu;& epsilon; & nu;& alpha; & sigma;& alpha;& sigmaf; & epsilon;& nu;& eta;& mu;& epsilon;& rho; ώ & sigma;& epsilon;& iota; ό & tau;& iota;</p >\n < ul >\n < li >& alpha;& sigma;& delta;& phi;& alpha;& sigma;& delta;& phi;</li >\n < li >& alpha;& sigma;& delta;& phi;& alpha;& sigma;& delta;& phi;& alpha;& sigma;& delta;& phi;& alpha;& sigma;& delta;& phi;& alpha;& sigma;</li >\n < li >& alpha;& sigma;& delta;& phi;& alpha;& sigma;& delta;& phi;& alpha;& sigma;& delta;& phi;</li >\n</ul >\n < p >& nbsp;</p >\n < p >& Epsilon;& upsilon;& chi;& alpha;& rho;& iota;& sigma;& tau;& omicron; ύ & mu;& epsilon; & pi;& omicron;& lambda; ύ, <br>Intelsoft</p>";
        var mailToLink = "mailto:<EMAIL>?subject=καλημέρα%20τι%20κανεις&body=" + <%# HttpUtility.UrlEncode(formattedBody) #%>;
        window.location.href = mailToLink;
    </script>
</head>
<body>

</body>
</html>