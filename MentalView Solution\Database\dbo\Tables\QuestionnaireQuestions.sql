﻿CREATE TABLE [dbo].[QuestionnaireQuestions] (
    [QuestionnaireQuestionId] BIGINT         IDENTITY (1, 1) NOT NULL,
    [QuestionnaireId]         BIGINT         NOT NULL,
    [Title]                   NVARCHAR (250) CONSTRAINT [DF_QuestionnareQuestions_Title] DEFAULT ('') NOT NULL,
    [Value]                   NVARCHAR (250) CONSTRAINT [DF_QuestionnareQuestions_Value] DEFAULT ('') NOT NULL,
    [Help]                    NVARCHAR (MAX) CONSTRAINT [DF_QuestionnaireQuestions_Value1] DEFAULT ('') NOT NULL,
    CONSTRAINT [PK_QuestionnareQuestions] PRIMARY KEY CLUSTERED ([QuestionnaireQuestionId] ASC),
    CONSTRAINT [FK_QuestionnareQuestions_Questionnaires] FOREIGN KEY ([QuestionnaireId]) REFERENCES [dbo].[Questionnaires] ([QuestionnaireId]) ON DELETE CASCADE ON UPDATE CASCADE
);






GO
CREATE NONCLUSTERED INDEX [QuestionnaireIdIndex]
    ON [dbo].[QuestionnaireQuestions]([QuestionnaireId] ASC);

