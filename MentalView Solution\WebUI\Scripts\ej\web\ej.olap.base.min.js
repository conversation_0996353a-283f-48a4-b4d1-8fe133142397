/*!
*  filename: ej.olap.base.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function(n,t,i){t.olap=t.olap||{};t.olap.base={_initProperties:function(){this._columnHeaders=[];this._rowHeaders=[];this._valueCells=[];this.pivotEngine=[];this.olapCtrlObj=null;this._cBIndx=null;this._rBIndx=null;this._OlapDataSource;this._measureDt={};this._cTotIndexInfo=null;this._rTotIndexInfo=null;this._kpi=null;this._rowCount=0;this._colCount=0;this._isPaging=!1;this._indexRCell=0;this._indexCCell=0},_initControlProperties:function(n){n._drilledCellSet=n.model.enablePaging||n.model.enableVirtualScrolling?t.isNullOrUndefined(n._drilledCellSet)?[]:n._drilledCellSet:[];n.XMLACellSet=null;n._isMondrian=n.model.dataSource.providerName==t.olap.Providers.Mondrian;(t.isNullOrUndefined(n._fieldData)||!t.isNullOrUndefined(n._fieldData)&&t.isNullOrUndefined(n._fieldData.hierarchy))&&this._getFieldItemsInfo(n);this._currIndex={};this._isRowDrilled=!1;this._isColDrilled=!1;this._isNoSummary=n.model.layout=="nosummaries"?!0:!1},getJSONData:function(i,r,u){var f,s,o,e;i.action&&(i.action=="initialLoad"||i.action=="navPaging")&&(this._initControlProperties(u),this._applyTrim(u));i.action=="loadFieldElements"&&(u._baseSavedProperties=n.extend(!0,{},{drilledCellSet:u._drilledCellSet,XMLACellSet:u.XMLACellSet,columnHeaders:this._columnHeaders,rowHeaders:this._rowHeaders,valueCells:this._valueCells,pivotEngine:this.pivotEngine,olapCtrlObj:this.olapCtrlObj,cBIndx:this._cBIndx,rBIndx:this._rBIndx,OlapDataSource:this._OlapDataSource,measureDt:this._measureDt,cTotIndexInfo:this._cTotIndexInfo,rTotIndexInfo:this._rTotIndexInfo,kpi:this._kpi,rowCount:this._rowCount,colCount:this._colCount,isPaging:this._isPaging,indexRCell:this._indexRCell,indexCCell:this._indexCCell}));this._initProperties();this.olapCtrlObj=u;this._OlapDataSource=r;f="";s=r.data;u.model.enableGroupingBar&&i.action!="loadFieldElements"&&u._createGroupingBar(r);this._isPaging=(this.olapCtrlObj.model.enablePaging||this.olapCtrlObj.model.enableVirtualScrolling)&&!t.isNullOrUndefined(this._OlapDataSource.pagerOptions)&&this._OlapDataSource.pagerOptions.categoricalPageSize!=0&&this._OlapDataSource.pagerOptions.seriesPageSize!=0?!0:!1;f=r.MDXQuery?r.MDXQuery:this._getParsedMDX(r,r.cube,i.action&&i.action!="navPaging"&&this._isPaging&&!t.isNullOrUndefined(this.olapCtrlObj.model.dataSource.pagerOptions)?!0:!1);e=this._getConnectionInfo(this.olapCtrlObj.model.dataSource.data);o=t.isNullOrUndefined(r.providerName)||r.providerName==t.olap.Providers.SSAS?'<Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/"> <Header><\/Header> <Body> <Execute xmlns="urn:schemas-microsoft-com:xml-analysis"> <Command> <Statement> '+f+" <\/Statement> <\/Command> <Properties> <PropertyList> <Catalog>"+r.catalog+"<\/Catalog> <LocaleIdentifier>"+e.LCID+"<\/LocaleIdentifier> <\/PropertyList> <\/Properties><\/Execute> <\/Body> <\/Envelope>":'<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"><SOAP-ENV:Body><Execute xmlns="urn:schemas-microsoft-com:xml-analysis"><Command><Statement><![CDATA['+f+"]\]><\/Statement><\/Command><Properties><PropertyList><DataSourceInfo>"+r.sourceInfo+"<\/DataSourceInfo><Catalog>"+r.catalog+"<\/Catalog><AxisFormat>TupleFormat<\/AxisFormat><Content>Data<\/Content><Format>Multidimensional<\/Format><\/PropertyList><\/Properties><\/Execute><\/SOAP-ENV:Body><\/SOAP-ENV:Envelope>";i.action!="drilldown"||this._isPaging?this.olapCtrlObj.doAjaxPost("POST",e.url,{XMLA:o},i.action&&i.action!="navPaging"&&this._isPaging&&!t.isNullOrUndefined(this.olapCtrlObj.model.dataSource.pagerOptions)?this._generatePagingData:this._generateJSONData,null,i):this.olapCtrlObj.doAjaxPost("POST",e.url,{XMLA:o},this._onDemandExpand,null,i)},_getParsedMDX:function(n,r,u){var o,e,f,c,s,h;if(n!=i){if(o="",s=n.providerName==t.olap.Providers.Mondrian,s&&n.columns.length==0&&!(n.values[0].axis=="columns"&&n.values[0].measures.length>0))return"";e=t.olap._mdxParser._getRowMDX(n);f=t.olap._mdxParser._getcolumnMDX(n);delete n._isCollapse;e=e.length>0?this._isPaging&&!u||s?"("+e+")":"NON EMPTY("+e+")":"";f=f.length>0?this._isPaging&&!u||s?"("+f+")":"NON EMPTY("+f+")":"";!this._isPaging||u||t.isNullOrUndefined(this.olapCtrlObj.model.dataSource.pagerOptions)||(h=this._getPagingQuery(e,f),e=h.rowQuery,f=h.columnQuery);c=t.olap._mdxParser._getSlicerMDX(n,this.olapCtrlObj);o="\nSelect \n"+(f==""?"{}":f)+"\ndimension properties MEMBER_TYPE,CHILDREN_CARDINALITY, PARENT_UNIQUE_NAME  ON COLUMNS \n"+(e==""?"":","+e+"\ndimension properties MEMBER_TYPE,CHILDREN_CARDINALITY, PARENT_UNIQUE_NAME  ON ROWS")+"\n "+t.olap._mdxParser._getIncludefilterQuery(n,r,this.olapCtrlObj)+c+"\n CELL PROPERTIES VALUE, FORMAT_STRING, FORMATTED_VALUE \n ";f==""&&o.replace(/NON EMPTY/g," ");o=this._getCalculatedItems(o)}return o},_getCalculatedItems:function(i){var r=this.olapCtrlObj.model.calculatedMembers,f,u;if(r&&r.length>0){for(f="\nWITH",u=0;u<r.length;u++){var o=r[u].expression.indexOf("Measure")>-1?"[Measures].":r[u].hierarchyUniqueName+".",s=o+"["+(r[u].caption||r[u].name)+"]",e=t.isNullOrUndefined(r[u].formatString)?t.isNullOrUndefined(r[u].format)?null:r[u].format:r[u].formatString;f+="\n MEMBER "+s+" as ("+r[u].expression+" ) "+(t.isNullOrUndefined(e)?"":', FORMAT_STRING ="'+n.trim(e)+'"')}return f=f.replace(/\&/g,"&amp;"),f+" "+i}return i},_getPagingQuery:function(n,t){var i=Math.ceil(this._colCount/this.olapCtrlObj.model.dataSource.pagerOptions.categoricalPageSize)<parseInt(this.olapCtrlObj.model.dataSource.pagerOptions.categoricalCurrentPage)||parseInt(this.olapCtrlObj.model.dataSource.pagerOptions.categoricalCurrentPage)==0?Math.ceil(this._colCount/this.olapCtrlObj.model.dataSource.pagerOptions.categoricalPageSize)<parseInt(this.olapCtrlObj._categCurrentPage)&&this._colCount>0?Math.ceil(this._colCount/this.olapCtrlObj.model.dataSource.pagerOptions.categoricalPageSize):this.olapCtrlObj._categCurrentPage:parseInt(this.olapCtrlObj.model.dataSource.pagerOptions.categoricalCurrentPage),r=Math.ceil(this._rowCount/this.olapCtrlObj.model.dataSource.pagerOptions.seriesPageSize)<parseInt(this.olapCtrlObj.model.dataSource.pagerOptions.seriesCurrentPage)||parseInt(this.olapCtrlObj.model.dataSource.pagerOptions.seriesCurrentPage)==0?Math.ceil(this._rowCount/this.olapCtrlObj.model.dataSource.pagerOptions.seriesPageSize)<parseInt(this.olapCtrlObj._seriesCurrentPage)&&this._rowCount>0?Math.ceil(this._rowCount/this.olapCtrlObj.model.dataSource.pagerOptions.seriesPageSize):this.olapCtrlObj._seriesCurrentPage:parseInt(this.olapCtrlObj.model.dataSource.pagerOptions.seriesCurrentPage),u=n;return{rowQuery:n!=""?"SUBSET({"+(this.olapCtrlObj._isMondrian?"":"NONEMPTY")+"("+n+(!this.olapCtrlObj._isMondrian&&t!=""?","+t:"")+")},"+((r==0?1:r)-1)*this.olapCtrlObj.model.dataSource.pagerOptions.seriesPageSize+","+this.olapCtrlObj.model.dataSource.pagerOptions.seriesPageSize+")":"",columnQuery:t!=""?"SUBSET({"+(this.olapCtrlObj._isMondrian?"":"NONEMPTY")+"("+t+(!this.olapCtrlObj._isMondrian&&u!=""?","+u:"")+")},"+((i==0?1:i)-1)*this.olapCtrlObj.model.dataSource.pagerOptions.categoricalPageSize+","+this.olapCtrlObj.model.dataSource.pagerOptions.categoricalPageSize+")":""}},_getAxisElementsUName:function(n){for(var i="",t=0;t<n.length;t++)i+=i==""?"{"+n[t].uniqueName+"}":", {"+n[t].uniqueName+"}";return i},_generatePagingData:function(i,r){var u,f,e;if(n(r).find("Error").length>0)return t.Pivot._createErrorDialog(n(r).find("faultstring").text(),i.action,this.olapCtrlObj),!1;u=n(r).find("Axes, CellData");this._rowCount=n(u.find("Axis[name|='Axis1']").find("Tuples")).length>0?n(u.find("Axis[name|='Axis1']").find("Tuples")[0]).children().length:0;this._colCount=n(u.find("Axis[name|='Axis0']").find("Tuples")).length>0?n(u.find("Axis[name|='Axis0']").find("Tuples")[0]).children().length:0;f=this._getParsedMDX(this.olapCtrlObj.model.dataSource,this.olapCtrlObj.model.dataSource.cube,!1);e=this._getConnectionInfo(this.olapCtrlObj.model.dataSource.data);this.olapCtrlObj._isMondrian&&(this._controlObj=this.olapCtrlObj);this.olapCtrlObj.doAjaxPost("POST",e.url,{XMLA:t.olap._mdxParser.getSoapMsg(f,this.olapCtrlObj.model.dataSource.data,this.olapCtrlObj.model.dataSource.catalog)},this._generateJSONData,null,i)},_generateJSONData:function(r,u){var o,s,c,w,k,b,l,y,p,a,v,f,h,e;if(this.olapCtrlObj.model.dataSource=this._OlapDataSource,this._isPaging&&r.action=="drilldown"&&this._onDemandExpand(r,u),o=[],s=[],n(u).find("Error").length>0)return t.Pivot._createErrorDialog(n(u).find("faultstring").text(),"Error",this.olapCtrlObj),!1;if(n(u).find("Axis[name|='SlicerAxis']").remove(),this.olapCtrlObj.XMLACellSet=n(u).find("Axes, CellData"),k=[],r=="onDemandDrill"&&(this.olapCtrlObj.XMLACellSet=u),c=n(this.olapCtrlObj.XMLACellSet).find("Axis[name|='Axis0']").find("Tuple"),w=n(this.olapCtrlObj.XMLACellSet).find("Axis[name|='Axis1']").find("Tuple"),c.length>0){if(b=n(this.olapCtrlObj.XMLACellSet[1]).children(),this._rowHeaders=this._getHeaderCollection(w,"rowheader"),this._columnHeaders=this._getHeaderCollection(c,"colheader"),this._valueCells=this._getValueCells(b,c.length),this._indexRCell=this._measureDt.axis=="rowheader"?t.sum(this._rowHeaders.maxLvlLen)+1:t.sum(this._rowHeaders.maxLvlLen),this._indexCCell=this._measureDt.axis=="colheader"?t.sum(this._columnHeaders.maxLvlLen)+1:t.sum(this._columnHeaders.maxLvlLen),this._rowHeaders.headers.length>0&&t.sum(this._rowHeaders.maxLvlLen)==0&&(this._indexRCell=this._rowHeaders.headers[0].length),this._columnHeaders.headers.length>0&&t.sum(this._columnHeaders.maxLvlLen)==0&&(this._indexCCell=this._columnHeaders.headers[0].length),this._isPaging)for(f=0;f<this._rowHeaders.headers.length;f++)for(e=0;e<this._columnHeaders.headers.length;e++)t.isNullOrUndefined(this._valueCells[f])?(this._valueCells[f]=[],this._valueCells[f][e]={FormatString:"#,#",Value:""}):t.isNullOrUndefined(this._valueCells[f][e])&&(this._valueCells[f][e]={FormatString:"#,#",Value:""});for(this._populateEngine(this._rowHeaders.headers,this._columnHeaders.headers,this._valueCells),n(this.olapCtrlObj).find(".e-pivotGridTable").remove(),this.olapCtrlObj._measureDt=this._measureDt,l=this._rowHeaders.headers.length+this._indexCCell,y=this._columnHeaders.headers.length+this._indexRCell,this.pivotEngine.length>1&&(l=this.pivotEngine.length),f=0;f<l;f++)for(e=0;e<y;e++)o[e]==i&&(o[e]=[]),this.pivotEngine[f]!=i&&this.pivotEngine[f][e]!=i?(o[e][f]={Index:e+","+f,CSS:this.pivotEngine[f][e].CSS==""?"none":this.pivotEngine[f][e].CSS==i?"value":this.pivotEngine[f][e].CSS,Value:this.pivotEngine[f][e].Value,State:this.pivotEngine[f][e].ChildCount>0?2:this.pivotEngine[f][e].ChildCount<0?1:0,RowSpan:this.pivotEngine[f][e].RowSpan!=i?this.pivotEngine[f][e].RowSpan:1,ColSpan:this.pivotEngine[f][e].ColSpan!=i?this.pivotEngine[f][e].ColSpan:1,Info:this.pivotEngine[f][e].UName+"::"+this.pivotEngine[f][e].LName+"::"+this.pivotEngine[f][e].Value+"::"+this.pivotEngine[f][e].PUName,Span:this.pivotEngine[f][e].Span=="Block"?"Block":"None",Expander:1},this.pivotEngine[f][e].kpiInfo&&(o[e][f].kpi=this.pivotEngine[f][e].kpi,o[e][f].kpiInfo=this.pivotEngine[f][e].kpiInfo),this.pivotEngine[f][e].ActualValue&&(o[e][f].ActualValue=this.pivotEngine[f][e].ActualValue,o[e][f].Format=this.pivotEngine[f][e].FormatString)):this.pivotEngine[f]!=i&&(p="none",f<this._indexCCell&&e<this._indexRCell&&(p="rowheader"),o[e][f]={Index:e+","+f,CSS:p,Value:"",State:0,RowSpan:1,ColSpan:1,Info:"",Span:"Block",Expander:0});for(f=0;f<y;f++)for(e=0;e<l;e++)o[f]!=i&&o[f][e]!=i&&s.push(o[f][e]);for(a=0,v=0;v<s.length;v++)if(parseInt(s[v].Index.split(",")[0])==0)a++;else break}if(this.olapCtrlObj._isExporting&&this.olapCtrlObj.model.enableCompleteDataExport){for(e=0;e<this._indexRCell;e++)for(h="",f=this._indexCCell;f<o[0].length;f++)o[e][f].Info!=""&&(h==o[e][f].Info?o[e][f].Span="Block":h=o[e][f].Info);for(f=0;f<this._indexCCell;f++)for(h="",e=this._indexRCell;e<o.length;e++)o[e][f].Info!=""&&(h==o[e][f].Info?o[e][f].Span="Block":h=o[e][f].Info);this.olapCtrlObj._fullExportedData={jsonObj:s,tranposeEngine:o,rowCount:a};this.olapCtrlObj._drilledCellSet=this.olapCtrlObj._baseSavedProperties.drilledCellSet;this.olapCtrlObj.XMLACellSet=this.olapCtrlObj._baseSavedProperties.XMLACellSet;this._columnHeaders=this.olapCtrlObj._baseSavedProperties.columnHeaders;this._rowHeaders=this.olapCtrlObj._baseSavedProperties.rowHeaders;this._valueCells=this.olapCtrlObj._baseSavedProperties.valueCells;this.pivotEngine=this.olapCtrlObj._baseSavedProperties.pivotEngine;this.olapCtrlObj=this.olapCtrlObj._baseSavedProperties.olapCtrlObj;this._cBIndx=this.olapCtrlObj._baseSavedProperties.cBIndx;this._rBIndx=this.olapCtrlObj._baseSavedProperties.rBIndx;this._OlapDataSource=this.olapCtrlObj._baseSavedProperties.OlapDataSource;this._measureDt=this.olapCtrlObj._baseSavedProperties.measureDt;this._cTotIndexInfo=this.olapCtrlObj._baseSavedProperties.cTotIndexInfo;this._rTotIndexInfo=this.olapCtrlObj._baseSavedProperties.rTotIndexInfo;this._kpi=this.olapCtrlObj._baseSavedProperties.kpi;this._rowCount=this.olapCtrlObj._baseSavedProperties.rowCount;this._colCount=this.olapCtrlObj._baseSavedProperties.colCount;this._isPaging=this.olapCtrlObj._baseSavedProperties.isPaging;this._indexRCell=this.olapCtrlObj._baseSavedProperties.indexRCell;this._indexCCell=this.olapCtrlObj._baseSavedProperties.indexCCell;this.olapCtrlObj._baseSavedProperties={}}else this.olapCtrlObj._pivotRecords={records:s,rowCount:a},typeof this.olapCtrlObj.model.dataSource.data=="string"&&this.olapCtrlObj.pluginName!="ejPivotGrid"?typeof this.olapCtrlObj.model.dataSource.data=="string"&&this.olapCtrlObj.pluginName!="ejPivotChart"&&typeof this.olapCtrlObj.model.dataSource.data=="string"&&this.olapCtrlObj.pluginName!="ejPivotGauge"&&typeof this.olapCtrlObj.model.dataSource.data=="string"&&this.olapCtrlObj.pluginName!="ejPivotTreeMap"?(this._renderPager(r),this.olapCtrlObj.generateJSON({baseObj:this,tranposeEngine:o,jsonObj:s})):(this.olapCtrlObj.generateJSON({baseObj:this},o),this._isPaging&&this._renderPager(r)):this._renderPivotGrid(r,s),this._columnHeaders=this._rowHeaders=this._valueCells=this.pivotEngine=[],this._cBIndx=this._rBIndx=null,this._measureDt={axis:"",posision:null},this._cTotIndexInfo=this._rTotIndexInfo=null},_renderPager:function(n){var i,r,u,f;this._isPaging&&(n.action!="navPaging"&&(t.isNullOrUndefined(this.olapCtrlObj.model.dataSource.pagerOptions)?this.olapCtrlObj.model.enablePaging&&(u={CategoricalCurrentPage:1,CategoricalPageSize:1,SeriesCurrentPage:1,SeriesPageSize:1},f={Column:1,Row:1},this.olapCtrlObj._pagerObj.initPagerProperties(f,u),this.olapCtrlObj._pagerObj.element.css("opacity","0.5"),this.olapCtrlObj._pagerObj.element.find(".e-pagerTextBox").attr("disabled","disabled"),this.olapCtrlObj._pagerObj._unwireEvents()):(i=Math.ceil(this._colCount/this.olapCtrlObj.model.dataSource.pagerOptions.categoricalPageSize)<parseInt(this.olapCtrlObj.model.dataSource.pagerOptions.categoricalCurrentPage)||parseInt(this.olapCtrlObj.model.dataSource.pagerOptions.categoricalCurrentPage)==0?Math.ceil(this._colCount/this.olapCtrlObj.model.dataSource.pagerOptions.categoricalPageSize)<parseInt(this.olapCtrlObj._categCurrentPage)?Math.ceil(this._colCount/this.olapCtrlObj.model.dataSource.pagerOptions.categoricalPageSize):this.olapCtrlObj._categCurrentPage:parseInt(this.olapCtrlObj.model.dataSource.pagerOptions.categoricalCurrentPage),r=Math.ceil(this._rowCount/this.olapCtrlObj.model.dataSource.pagerOptions.seriesPageSize)<parseInt(this.olapCtrlObj.model.dataSource.pagerOptions.seriesCurrentPage)||parseInt(this.olapCtrlObj.model.dataSource.pagerOptions.seriesCurrentPage)==0?Math.ceil(this._rowCount/this.olapCtrlObj.model.dataSource.pagerOptions.seriesPageSize)<parseInt(this.olapCtrlObj._seriesCurrentPage)?Math.ceil(this._rowCount/this.olapCtrlObj.model.dataSource.pagerOptions.seriesPageSize):this.olapCtrlObj._seriesCurrentPage:parseInt(this.olapCtrlObj.model.dataSource.pagerOptions.seriesCurrentPage),i=i==0?1:i,r=r==0?1:r,this._colCount=this._colCount==0?1:this._colCount,this._rowCount=this._rowCount==0?1:this._rowCount,this.olapCtrlObj.model.enablePaging?(u={CategoricalCurrentPage:i,CategoricalPageSize:this.olapCtrlObj.model.dataSource.pagerOptions.categoricalPageSize,SeriesCurrentPage:r,SeriesPageSize:this.olapCtrlObj.model.dataSource.pagerOptions.seriesPageSize},f={Column:this._colCount,Row:this._rowCount},this.olapCtrlObj._pagerObj.element.css("opacity","1"),this.olapCtrlObj._pagerObj.element.find(".e-pagerTextBox").removeAttr("disabled"),this.olapCtrlObj._pagerObj._unwireEvents(),this.olapCtrlObj._pagerObj._wireEvents(),this.olapCtrlObj._pagerObj.initPagerProperties(f,u)):this.olapCtrlObj.model.enableVirtualScrolling&&(this.olapCtrlObj._categPageCount=Math.ceil(this._colCount/this.olapCtrlObj.model.dataSource.pagerOptions.categoricalPageSize),this.olapCtrlObj._seriesPageCount=Math.ceil(this._rowCount/this.olapCtrlObj.model.dataSource.pagerOptions.seriesPageSize)),this.olapCtrlObj._categCurrentPage=i,this.olapCtrlObj._seriesCurrentPage=r,t.isNullOrUndefined(this.olapCtrlObj._pivotGrid)||(this.olapCtrlObj._pivotGrid._categCurrentPage=i,this.olapCtrlObj._pivotGrid._seriesCurrentPage=r,this.olapCtrlObj._pivotGrid._categPageCount=this.olapCtrlObj._categPageCount,this.olapCtrlObj._pivotGrid._seriesPageCount=this.olapCtrlObj._seriesPageCount))),t.isNullOrUndefined(this.olapCtrlObj.model.dataSource.pagerOptions)||(this.olapCtrlObj.model.dataSource.pagerOptions.categoricalCurrentPage=this.olapCtrlObj._categCurrentPage,this.olapCtrlObj.model.dataSource.pagerOptions.seriesCurrentPage=this.olapCtrlObj._seriesCurrentPage))},_renderPivotGrid:function(n,i){this.olapCtrlObj._dataModel="XMLA";this.olapCtrlObj.setJSONRecords(JSON.stringify(i));this.olapCtrlObj.setOlapReport(this.olapCtrlObj.model.dataSource);this._renderPager(n);this.olapCtrlObj.model.enableGroupingBar&&this.olapCtrlObj._createGroupingBar(this.olapCtrlObj.model.dataSource);this.olapCtrlObj.model.layout!=null&&this.olapCtrlObj.layout().toLowerCase()==t.PivotGrid.Layout.ExcelLikeLayout?this.olapCtrlObj.excelLikeLayout(i):this.olapCtrlObj.renderControlFromJSON(i);n.action&&(n.action=="initialLoad"||n.action=="navPaging")&&(this.olapCtrlObj._trigger("renderSuccess",this.olapCtrlObj),this.olapCtrlObj._fieldData={hierarchy:this.olapCtrlObj._fieldData.hierarchy,measures:this.olapCtrlObj._fieldData.measures})},_onDemandExpand:function(t,i){var ot=i,e,rt,r="",o=this.olapCtrlObj.XMLACellSet,k,d,g=n(this.olapCtrlObj.XMLACellSet).find("Axis[name='Axis0'] Tuple").length,l=n(this.olapCtrlObj.XMLACellSet).find("Axis[name='Axis1'] Tuple").length,p,ut=0,ft=0,c,u,tt,et,f,it;l=l?l:1;ut=n(i).find("CellData Cell").length;ft=n(this.olapCtrlObj.XMLACellSet[1]).children().length;rt=t.cellInfo.previousElements.split(">#>");var a=n(i).find("Axis[name|='Axis0']").find("Tuple"),v=n(i).find("Axis[name|='Axis1']").find("Tuple"),k=a.length;d=v.length;n(i).find("Axis[name='SlicerAxis']").remove();var nt=t.cellInfo.itemPosition,y,w,s=0,h=!1,b="";for(w=t.cellInfo.axis=="rowheader"?v[0]:a[0],r=y=n(w).find("Member:nth-child("+(nt+1)+") PARENT_UNIQUE_NAME").text(),c=nt+1;c>0;c--)o=n(o).find("Tuple Member:nth-child("+c+") UName:contains('"+y+"')").parents("tuple"),y=n(w).find("Member:nth-child("+(c-1)+") UName").text(),b=y+b;if(r=b+r,p=o.index()+1,r=t.cellInfo.previousElements.split(">#>").join().replace(/,/g,""),e=t.cellInfo.preRepItm.split(">#>").join().replace(/,/g,""),this.olapCtrlObj._drilledCellSet&&(s=this.olapCtrlObj._drilledCellSet.length),r=r+" !#"+t.cellInfo.axis,this.olapCtrlObj._drilledCellSet[0]&&s)for(u=0;u<s;u++){for(tt=this.olapCtrlObj._drilledCellSet[u].length,et=t.cellInfo.parentUniqueName,f=0;f<tt;f++)if(t.cellInfo.previousElements.startsWith(this.olapCtrlObj._drilledCellSet[u][f].key)){this.olapCtrlObj._drilledCellSet[u].push({key:r,repItms:e});h=!0;break}else if(this.olapCtrlObj._drilledCellSet[u][f].key.startsWith(t.cellInfo.previousElements)){this.olapCtrlObj._drilledCellSet[u].splice(f,0,{key:r,repItms:e});h=!0;break}if(h)break}else this.olapCtrlObj._drilledCellSet[0]=[],this.olapCtrlObj._drilledCellSet[0][0]={key:r,repItms:e},h=!0;h||(this.olapCtrlObj._drilledCellSet[s]=[],this.olapCtrlObj._drilledCellSet[s].push({key:r,repItms:e}));this._isPaging||(t.cellInfo.axis=="rowheader"?(it=n(i).find("Axis[name|='Axis1']").find("Tuple"),this._insertCellSet("rowheader",o,v,a,d,g,k,p,i)):(it=n(i).find("Axis[name|='Axis1']").find("Tuple"),this._insertCellSet("colheader",o,a,v,l,g,k,p,i)),this._generateJSONData("onDemandDrill",this.olapCtrlObj.XMLACellSet))},_insertCellSet:function(t,i,r,u,f,e,o,s,h){var g=n(n(h).find("CellData Cell")[0]).clone(),a,l,c,v,it;n(g).find("FmtValue").text("");i.length>1?n(i[0]).after(r):n(i).after(r);l=e;t=="rowheader"?a=n(this.olapCtrlObj.XMLACellSet).find("Axis[name|='Axis0']").find("Tuple"):(a=n(this.olapCtrlObj.XMLACellSet).find("Axis[name|='Axis1']").find("Tuple"),l=o);var y=0,nt=n(a).filter(function(t,i){if(u[y]&&n(i).find("UName").text()==n(u[y]).find("UName").text())return y++,!0}),p=0,w=!1;for(c=0;c<f;c++){var tt="",b=0,k=0,d;for(d=t=="rowheader"?(s+c)*l:c*(e+l)+s,v=0;v<l;v++)(t!="rowheader"||n(nt[b]).index()!=v)&&(t!="colheader"||n(nt[p]).index()!=c)&&a.length?n(this.olapCtrlObj.XMLACellSet).find("CellData Cell:nth-child("+(d+k)+")").after(n(g).clone()):(it=t=="rowheader"?c*o:p*o,tt=n(n(h).find("CellData Cell")[it+b]).clone(),n(this.olapCtrlObj.XMLACellSet).find("CellData Cell:nth-child("+(d+k)+")").after(tt),b++,w=!0),k++;t=="colheader"&&(w&&p++,w=!1)}this._setCellOrdinal(this.olapCtrlObj.XMLACellSet[1])},_clearDrilledCellSet:function(){for(var u=n.extend([],this.olapCtrlObj._drilledCellSet),t="",i,r=0;r<u.length;r++)t=u[r][0].key.split("!#"),t.length&&(i=this._getDrilledMemeber({item:{previousElements:t[0]}})),i.length&&this._onDemandCollapse({drilledMembers:i,action:""},t[1])},_onDemandCollapse:function(i,r){var u,f;f=n(this.olapCtrlObj.XMLACellSet).find("Axis[name|='Axis0']").find("Tuple");u=n(this.olapCtrlObj.XMLACellSet).find("Axis[name|='Axis1']").find("Tuple");try{this.olapCtrlObj.model.dataSource=t.olap._mdxParser._clearCollapsedItems(r,i.drilledMembers[i.drilledMembers.length-1],this.olapCtrlObj.model.dataSource);i.drilledMembers=i.drilledMembers.splice(0,i.drilledMembers.length-1)}catch(e){this.olapCtrlObj._ogridWaitingPopup.hide()}i.drilledMembers.length?this._onDemandCollapse({drilledMembers:i.drilledMembers,action:i.action},r):i.action=="collapse"&&(this.olapCtrlObj.model.dataSource._isCollapse=!0,delete this._currIndex.axis,this.getJSONData({action:"onDemandCollapse"},this.olapCtrlObj.model.dataSource,this.olapCtrlObj))},_removeCellSets:function(t,i,r,u,f,e){for(var c,s,v,p,o,l=t.length,h,y,w=f[0],a=0;a<i.length;a++)if(n(i[a]).find("UName").text()==n(w).find("UName").text()){y=i[a];break}if(c=l,s=n(y).index(),s)if(e=="rowheader"){for(h=r.length;c>0;)n(n(this.olapCtrlObj.XMLACellSet).find("Axis[name|='Axis1']").find("Tuple")[s]).remove(),c--;for(o=ext=s;o<ext+l;o++)v=n(n(this.olapCtrlObj.XMLACellSet[1]).children()).splice(s*h,h),n(v).remove()}else{for(h=i.length,p=r.length;c>0;)n(n(this.olapCtrlObj.XMLACellSet).find("Axis[name|='Axis0']").find("Tuple")[s]).remove(),c--;for(o=0;o<p;o++)v=n(n(this.olapCtrlObj.XMLACellSet[1]).children()).splice(h*o-o*l+s,l),n(v).remove()}this._setCellOrdinal(this.olapCtrlObj.XMLACellSet[1])},_getDrilledMemeber:function(t){for(var e=[],o=t.item.previousElements.split(">#>").join().replace(/,/g,""),u=this.olapCtrlObj._drilledCellSet,r,f,e=n.map(this.olapCtrlObj._drilledCellSet,function(t){return e=n.map(t,function(n){if(n.key.startsWith(o))return n})}),i=0;i<u.length;i++)for(r=0,f=u[i];u[i]&&r<u[i].length;r++)f[r]&&f[r].key.startsWith(o)&&(f.splice(r,1),r--,f.length||(u.splice(i,1),i--));return e},_getHeaderCollection:function(t,r){var f,lt,c=[],tt,it="",w=[],k=null,rt={},p={},o=this,at,e,pt,ot,st,ht,ct,u,ft,h,dt,y,gt,nt;if(f=n(t).filter(function(t,i){var f=n(i).find("Member"),e=!(n(f[0]).find("MEMBER_TYPE").text()=="4")&&n(f[0]).find("LName").text().indexOf("[(All)]")!=-1,a=e?1:0,s=0,u,h,l;for(k=null,e?(tt=0,it="total"):(it="",tt=f.length),u=1,n(f[0]).find("LName").text().toLowerCase().indexOf("[measures]")!=-1&&(u=0),u;u<f.length;u++)h=!1,r=="colheader"&&o._OlapDataSource.columns[u]?(o._OlapDataSource.columns[u].hasAllMember||o._OlapDataSource.columns[u].expression!=null)&&(h=!0):r=="rowheader"&&o._OlapDataSource.rows[u]&&(o._OlapDataSource.rows[u].hasAllMember||o._OlapDataSource.rows[u].expression!=null)&&(h=!0),n(f[u]).find("LName").text().toLowerCase().indexOf("[measures]")!=-1||h?(s=e,p.axis=r,p.posision=u,parseInt(n(f[u]).find("MEMBER_TYPE").text())==4&&(l=n(f[u]).find("UName").text().toLowerCase(),l.indexOf("trend]")!=-1?(o._kpi||o._loadKpi(o.olapCtrlObj.model.dataSource,this._loadKpiSuccess,""),(rt=o._isKpi(n(f[u]).find("UName").text(),"trend"))&&(k="trend",p.isKpiExist=!0)):l.indexOf("status]")!=-1?(o._kpi||o._loadKpi(o.olapCtrlObj.model.dataSource,this._loadKpiSuccess,""),(rt=o._isKpi(n(f[u]).find("UName").text(),"status"))&&(k="status",p.isKpiExist=!0)):l.indexOf("goal]")!=-1&&(o._kpi||o._loadKpi(o.olapCtrlObj.model.dataSource,this._loadKpiSuccess,""),(rt=o._isKpi(n(f[u]).find("UName").text()+"::"+n(f[u]).find("Caption").text(),"goal"))&&(k="goal",p.isKpiExist=!0)))):s=n(f[u]).find("LName").text().indexOf("[(All)]")!=-1,(e==!1&&s==!0||e==!0&&s==!1)&&(a++,tt=u,it="total"),e=s;return lt=t,a<2&&c.push({selIndx:lt,totLvl:tt,type:it,kpi:k,kpiInfo:rt}),a<2}).map(function(t,u){for(var l,s,v,a,e=n(u).find("Member"),h=[],f=0;f<e.length;f++)l=!1,s=parseInt(n(e[f]).find("LNum").text()),r=="colheader"&&o._OlapDataSource.columns[f]?o._OlapDataSource.columns[f].hasAllMember||o._OlapDataSource.columns[f].expression!=null?s+=1:l=o._OlapDataSource.columns[f].expression!=null:r=="rowheader"&&o._OlapDataSource.rows[f]&&(o._OlapDataSource.rows[f].hasAllMember||o._OlapDataSource.rows[f].expression!=null?s+=1:l=o._OlapDataSource.rows[f].expression!=null),v=n(e[f]).find("PARENT_UNIQUE_NAME").length?n(e[f]).find("PARENT_UNIQUE_NAME").text():"",h.push({CSS:r,Value:n(e[f]).find("Caption").text()==""?"(Blank)":n(e[f]).find("Caption").text(),ColSpan:1,RowSpan:1,HUName:n(e[f]).attr("Hierarchy"),LName:n(e[f]).find("LName").text(),UName:n(e[f]).find("UName").text(),ChildCount:parseInt(n(e[f]).find("CHILDREN_CARDINALITY").text()),PUName:v,isCalcMem:l,LNum:s,MemberType:parseInt(n(e[f]).find("MEMBER_TYPE").text())}),(n(e[f]).find("LName").text().toLowerCase().indexOf("[measures]")!=-1||l)&&c[t].kpi&&(h[h.length-1].kpiInfo=c[t].kpiInfo,h[h.length-1].kpi=c[t].kpi),w[f]==i?w[f]=s:w[f]<s&&(w[f]=s);return a=[],a[0]=h,a}),r=="colheader"?this._cTotIndexInfo=c:this._rTotIndexInfo=c,f={headers:f,indxInfo:c,maxLvlLen:w},n.isEmptyObject(p)||(this._measureDt=n.extend({},p)),at=1,r.indexOf(this._OlapDataSource.values[0].axis.substring(0,3))>-1&&(at=this._measureDt.measureCount=this._OlapDataSource.values[0].measures.length),!this._OlapDataSource._enableBasicEngine&&!this._isPaging){var v={headers:[],indxInfo:[]},ni=c,s=c.length,et=v.length,ut=[],vt=[];try{for(u=0;u<s;u++)u<s&&f.indxInfo[u].type=="total"&&(v.indxInfo.push(f.indxInfo.splice(u,1)[0]),v.headers.push(f.headers.splice(u,1)[0]),s--,u--);var b=f.headers.length>0?f.headers[0].length:0,yt=0,d=0;for(this._measureDt.posision==b-1&&this._measureDt.axis==r&&(d=1),s=f.headers.length,u=0;u<=s;u++){var h=null,ft=null,l=null,a=null;for(a=this._updateMemberVariables(f.headers,h,ft,l,u),h=a.prev,ft=a.next,l=a.cur,a=null,e=b-2-d;e>=0;e--)if(pt=this._comparePrevMembers(f.headers,s,u,e,b,-2),u==s||u>0&&(h&&h[e].UName!=l[e].UName||h[e].UName==l[e].UName&&pt))if(l&&h[e].UName==l[e].PUName)ot=this._removeSubtotalMembers(f,u,e),u-=ot,s-=ot,a=this._updateMemberVariables(f.headers,h,ft,l,u),h=a.prev,ft=a.next,l=a.cur,a=null;else{var wt=h,et=0,g,bt=0,kt=0;for(h&&(kt=h[e].LNum),l&&(bt=l[e].LNum),st=!1,ht=bt;ht<=kt;ht++){for(g=this._getSummaryHeaders(wt,v,e,st,d),et=g.headers.length;g.headers.length>0;)f.headers.splice(u,0,g.headers.pop()),f.indxInfo.splice(u,0,g.indexInfo.pop());st=!0;u+=et;s+=et;wt=f.headers[u-1]}}if(u==s&&v.headers.length>0){for(ct=0;v.headers.length>0;)f.headers.splice(u,0,v.headers.pop()),f.indxInfo.splice(u,0,v.indxInfo.pop()),s++,ct++;u+=ct}}var e=0,dt="";for(ut={headers:[],indxInfo:[]},e=b-1-d,s=f.headers.length,u=0;u<s;u++)if(h=this._findPreviousMember(f.headers,u-1,e),u>0&&f.headers[u-1][e].UName!=f.headers[u][e].UName&&e==b-1-d&&w[e]>1){if(u>0&&f.headers[u]!=i&&f.headers[u-1][e].LNum!=0&&f.headers[u-1][e].UName==f.headers[u][e].PUName)for(y=h;y<0;y++)f.indxInfo[u+y].lnum=f.headers[u+y][e].LNum,f.indxInfo[u+y].type="total",ut.indxInfo.push(f.indxInfo.splice(u+y,1)[0]),ut.headers.push(f.headers.splice(u+y,1)[0]),u+1==s?dt=f.headers[u][e].PUName:(u--,s--);(u!=0&&f.headers[u]!=i&&f.headers[u-1][e].LNum>f.headers[u][e].LNum||!1)&&(gt=[],nt=this._insertSummaryHeaders(f,ut,gt,u,e,b,yt,dt,!1,vt),s=nt.itmCnt,u=nt.hCnt,yt=nt.insCnt,vt[e]=nt.subPos)}}catch(ti){this.olapCtrlObj._waitingPopup.hide()}}return r=="rowheader"?this._rBIndx=c:this._cBIndx=c,f},_insertSummaryHeaders:function(t,r,u,f,e,o,s,h,c,l){var u=[],w,b,v,k="",s=0,y,p,a,d;for(t.headers[f-1]&&(h=t.headers[f-1][e].PUName),v=t.headers.length,w=t.headers[f][e].LNum,b=t.headers[f-1][e].LNum,y=this._comparePrevMembers(t.headers,v,f,e,o,s),p=w;p<b;p++){for(n.grep(r.headers,function(n,r){(h==n[e].UName||t.headers[f+1+s]!=i&&t.headers[f][e].UName!=t.headers[f+1+s][e].UName&&t.headers[f][e].PUName==n[e].UName&&y)&&(k=n[e].PUName,u.push(r))}),h=k,y&&(u.reverse(),y=!1);u.length>0;){for(a=u.length-1,d=u[u.length-1],t.headers.splice(f,0,r.headers.splice(u[u.length-1],1)[0]),t.indxInfo.splice(f,0,r.indxInfo.splice(u[u.length-1],1)[0]);a>=0;)d<u[a]&&u[a]--,a--;u.pop();v++;s++;c&&l[e]++}s>0&&(f+=s,s=0)}return{itmCnt:v,hCnt:f,insCnt:s,subPos:l[e]}},_comparePrevMembers:function(n,t,i,r,u,f){var o=!1,e;if(r>0&&i<t&&i+1+f>=0&&i+1+f<t)for(e=r-1;e>=0;e--)n[i][e].UName!=n[i+1+f][e].UName&&(o=!0);return o},_updateMemberVariables:function(n,t,i,r,u){return n[u]&&(r=n[u]),i=n[u+1]?n[u+1]:null,t=n[u-1]?n[u-1]:null,{cur:r,prev:t,next:i}},_removeSubtotalMembers:function(n,t,i){for(var r=t-1,u=n.headers[r][i].UName;n.headers[r]&&n.headers[r][i].UName==u;)n.indxInfo.splice(r,1),n.headers.splice(r,1),r--;return t-(r+1)},_getSummaryHeaders:function(t,i,r,u){var o=i.headers,f=t,e=r,s=[],h=[],c=i.indxInfo,l;return l=n.grep(o,function(n,t){for(var r=!1,i=e;i>=0;i--)if(n[i].UName==f[i].UName||u&&i==e&&n[i].UName==f[i].PUName)i==0&&(r=!0,h.push(c[t]));else break;return r}),i.headers=n.grep(o,function(n,t){for(var r=!0,i=e;i>=0;i--)if(n[i].UName==f[i].UName||u&&i==e&&n[i].UName==f[i].PUName)i==0&&(r=!1);else break;return r&&s.push(c[t]),r}),i.indxInfo=s,{headers:l,indexInfo:h}},_drillOrderHeaderCollection:function(t,i,r){var o=t,s=i,e=r,u=0,f=this;return n(o).map(function(t,i){var o=n(s[i.selIndx]).find("Member"),h=[],r,l,a,c;for(h[u]=[],r=0;r<o.length;r++)l=parseInt(n(o[r]).find("LNum").text()),e=="colheader"&&f._OlapDataSource.columns[r]?f._OlapDataSource.columns[r].hasAllMember&&(l+=1):e=="rowheader"&&f._OlapDataSource.rows[r]&&f._OlapDataSource.rows[r].hasAllMember&&(l+=1),a=n(o[r]).find("PARENT_UNIQUE_NAME").length?n(o[r]).find("PARENT_UNIQUE_NAME").text():"",h[u].push({CSS:e,Value:n(o[r]).find("Caption").text(),ColSpan:1,RowSpan:1,HUName:n(o[r]).attr("Hierarchy"),LName:n(o[r]).find("LName").text(),UName:n(o[r]).find("UName").text(),ChildCount:parseInt(n(o[r]).find("CHILDREN_CARDINALITY").text()),PUName:a,LNum:l,MemberType:parseInt(n(o[r]).find("MEMBER_TYPE").text())}),c=h[u][h[u].length-1],i.kpiInfo&&c.UName.toLowerCase().indexOf("[measures]")>-1&&c.MemberType==4&&(h[u][h[u].length-1].kpiInfo=i.kpiInfo),i.kpi&&c.UName.toLowerCase().indexOf("[measures]")>-1&&c.MemberType==4&&(h[u][h[u].length-1].kpi=i.kpi);return h})},_loadKpi:function(n,t,i){i={action:"loadFieldElements"};var r=this._getConnectionInfo(n.data),u='<Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/"><Header/><Body><Discover xmlns="urn:schemas-microsoft-com:xml-analysis"><RequestType>MDSCHEMA_KPIS<\/RequestType><Restrictions><RestrictionList><CATALOG_NAME>'+n.catalog+"<\/CATALOG_NAME><CUBE_NAME>"+n.cube+"<\/CUBE_NAME><\/RestrictionList><\/Restrictions><Properties><PropertyList><Catalog>"+n.catalog+"<\/Catalog><\/PropertyList><\/Properties><\/Discover><\/Body><\/Envelope>";this.olapCtrlObj.doAjaxPost("POST",r.url,{XMLA:u},this._loadKpiSuccess,null,i)},_loadKpiSuccess:function(n,t){this._kpi=t},_isKpi:function(t,i){var u=n(this._kpi).find("row:contains("+t.split("::")[0]+")"),r={};return u.length?(i=="trend"?(r.Graphic=n(u).find("KPI_TREND_GRAPHIC").text(),r.Value=n(u).find("KPI_NAME").text()):i=="status"?(r.Graphic=n(u).find("KPI_STATUS_GRAPHIC").text(),r.Value=n(u).find("KPI_NAME").text()):i=="goal"&&(r.Caption=t.split("::")[1],r.Value=n(u).find("KPI_NAME").text()),r):!1},_getValueCells:function(i,r){var h=this._columnHeaders.headers.length,l=this._rowHeaders.headers.length?this._rowHeaders.headers.length:1,e=0,f=0,o,a,u,s,c;if(this._valueCells[e]=[],this._fillAllValueCells(),i=n(this.olapCtrlObj.XMLACellSet[1]).children(),this._OlapDataSource._enableBasicEngine||this._isPaging)for(u=0;u<i.length;u++)f<h&&(this._valueCells[e][f]={ActualValue:n(i[u]).find("Value").text(),Value:n(i[u]).find("FmtValue")[0]?n(i[u]).find("FmtValue").text():"",FormatString:n(i[u]).find("FormatString")[0]?n(i[u]).find("FormatString").text():"",kpi:!t.isNullOrUndefined(this._cBIndx[f])&&this._cBIndx[f].kpi?this._cBIndx[f].kpi:!t.isNullOrUndefined(this._rBIndx[e])&&this._rBIndx[e].kpi?this._rBIndx[e].kpi:""},f++),f==h&&e+1<l&&(f=0,e++,this._valueCells[e]=[]);else for(o=0;o<l;o++)for(a=this._rBIndx.length?this._rBIndx[o].selIndx:0,u=0;u<h;u++)i&&(s=n(i[a*r+this._cBIndx[u].selIndx]),c="",(this._columnHeaders.indxInfo[u]&&this._columnHeaders.indxInfo[u].type=="total"||this._rowHeaders.indxInfo[o]&&this._rowHeaders.indxInfo[o].type=="total")&&(c="summary"),f<h&&(this._valueCells[o][u]={ActualValue:n(s).find("Value").text(),Value:n(s).find("FmtValue")[0]?this._getFormatedValue(n(s),n(s).find("FormatString")[0]):"",FormatString:n(s).find("FormatString")[0]?n(s).find("FormatString").text():"",kpi:this._cBIndx[u].kpi?this._cBIndx[u].kpi:this._rBIndx[o]?this._rBIndx[o].kpi:"",summary:c,Span:this._isNoSummary&&c=="summary"?"Block":"None"},f++),f==h&&e+1<l&&(f=0,e++,this._valueCells[e]=[]));return this._valueCells},_fillAllValueCells:function(i){var i=n(this.olapCtrlObj.XMLACellSet[1]).children(),f,s,r,e,o,u;if(!t.isNullOrUndefined(i))for(f=!n(i[0]).clone(),n(f).find("FmtValue").text(""),s=i,r=0;r<i.length;r++)if(e=parseInt(n(i[r]).attr("CellOrdinal")),i[r+1]&&(o=parseInt(n(i[r+1]).attr("CellOrdinal"))),e+1<o)for(u=e+1;u<o;u++)n(i[r]).after(n(f).clone().attr("CellOrdinal",u)[0]);return i},_setCellOrdinal:function(t){for(var r=n(t).find("Cell"),i=0;i<r.length;i++)n(r[i]).attr("CellOrdinal",i)},_getFormatedValue:function(i,r){var r,u;if((this.olapCtrlObj.model.locale=="en-US"||!n.isNumeric(parseFloat(n(i).find("Value").text())))&&n(i).length>0)u=n(i).find("FmtValue").text();else{r=n(r).length>0?n(r).text().toLowerCase():t.isNullOrUndefined(r)?"":r.length>0?r:"";u=n(i).length>0?parseFloat(i.find("Value").text()):i;switch(r){case"decimal":u=parseFloat(t.widgetBase.formatting("{0:D2}",u,this.olapCtrlObj.model.locale));break;case"percent":u=t.widgetBase.formatting("{0:P0}",u,this.olapCtrlObj.model.locale);break;case"number":u=t.widgetBase.formatting("{0:N}",u,this.olapCtrlObj.model.locale);break;case"currency":u=t.widgetBase.formatting("{0:C2}",u,this.olapCtrlObj.model.locale);break;case"date":u=new Date((Number(u)-2)*864e5+new Date("01/01/1900").getTime());this._isDateTime(u)&&(u=t.widgetBase.formatting("{0:MM/dd/yyyy}",u,this.olapCtrlObj.model.locale));break;case"scientific":u=Number(u).toExponential(2).replace("e","E");break;case"accounting":u=this._toAccounting(u,"{0:C2}",this.olapCtrlObj.model.locale);break;case"time":u=new Date((Number(u)-2)*864e5+new Date("01/01/1900").getTime());this._isDateTime(u)&&(u=t.widgetBase.formatting("{0:h:mm:ss tt}",u,this.olapCtrlObj.model.locale));break;case"fraction":u=this._toFraction(u);u="numerator"in u?u.integer+" "+u.numerator+"/"+u.denominator:u.integer;break;default:u=n(i).find("FmtValue").text()}}return u},_isDateTime:function(n){return Object.prototype.toString.call(n)==="[object Date]"&&!isNaN(n.valueOf())},_toAccounting:function(n,i,r){var h=t.preferredCulture(r).numberFormat,f,e,u=h.currency.symbol,o,s;return val=t.widgetBase.formatting(i,n,this.olapCtrlObj.model.locale),o=val.replace(u,""),s=val.indexOf(u),!s||n<0&&s===1?(f=u,e=Number(n)?o:"-"):(f=Number(n)?o:"-",e=u),f+"   "+e},_toFraction:function(n){if(this._isNumber(n)){var r=n.toString(),e=r.split(".")[0],t=r.split(".")[1];if(!t)return{integer:n};var i=(+t).toString(),u=this._getPlaceValue(t,i),f=this._getGCD(i,u);return{integer:e,numerator:Number(i)/f,denominator:Number(u)/f}}return null},_isNumber:function(n){return n-parseFloat(n)>=0},_getGCD:function(n,t){return(n=Number(n),t=Number(t),!t)?n:this._getGCD(t,n%t)},_getPlaceValue:function(n,t){var i=n.indexOf(t)+t.length;return"1"+Array(i+1).join("0")},_populateEngine:function(r,u,f){var y=r.length,p=u.length,w=y?r[0].length:y,h=p?u[0].length:p,b=[],g=f.length,nt=f[0].length,o,v,ft,d,a,e,c;try{this.pivotEngine=[];var tt=[],it=[],et=!1,k=[],l=!1,s=[];for(e=0;e<p;e++)for(this.pivotEngine[e+h]==i&&(this.pivotEngine[e+h]=[]),e==0&&(this.pivotEngine[0]=[],this.pivotEngine[0][0]={CSS:"none",Value:"",ColSpan:this._indexCCell?this._indexCCell:1,RowSpan:this._indexRCell?this._indexRCell:1,HUName:"",LName:"",UName:"",LNum:""}),o=0;o<h;o++)l=!1,this._OlapDataSource.columns&&this._OlapDataSource.columns[o]&&this._OlapDataSource.columns[o].isNamedSets&&(l=!0),this._pivotEngineSpanCalculation("colheader",u,r,this._columnHeaders.maxLvlLen,f,o,e,it,tt,this._cTotIndexInfo,l,k);for(s=this.pivotEngine.slice(),this.pivotEngine=[],e=0;e<s.length;e++)if(s[e])for(o=0;o<s[e].length;o++)s[e][o]&&(this.pivotEngine[o]==i&&(this.pivotEngine[o]=[]),this.pivotEngine[o][e]={CSS:s[e][o].CSS,ColSpan:s[e][o].RowSpan,HUName:s[e][o].HUName,LName:s[e][o].LName,LNum:s[e][o].LNum,RowSpan:s[e][o].ColSpan,UName:s[e][o].UName,PUName:s[e][o].PUName,Value:s[e][o].Value,ChildCount:s[e][o].ChildCount,MemberType:s[e][o].MemberType,Span:s[e][o].Span=="Block"?"Block":"None"},s[e][o].kpiInfo&&(this.pivotEngine[o][e].kpiInfo=s[e][o].kpiInfo,this.pivotEngine[o][e].kpi=s[e][o].kpi));var rt=[],ut=[],et=!1,k=[];for(e=0;e<y;e++)for(this.pivotEngine[e+this._indexCCell]==i&&(this.pivotEngine[e+this._indexCCell]=[]),o=0;o<w;o++)l=!1,this._OlapDataSource.rows&&this._OlapDataSource.rows[o]&&this._OlapDataSource.rows[o].isNamedSets&&(l=!0),this._pivotEngineSpanCalculation("rowheader",r,u,this._rowHeaders.maxLvlLen,f,o,e,ut,rt,this._rTotIndexInfo,l,k);for(b=n.extend(!0,[],this.pivotEngine),w=this._measureDt.axis=="rowheader"?this._indexRCell+1:this._indexRCell,h=this._indexCCell?this._indexCCell:h,e=0;e<g;e++)for(this.pivotEngine[e+h]!=i&&(w=this.pivotEngine[e+h].length),o=0;o<nt;o++)t.isNullOrUndefined(f[e][o])||(this.pivotEngine[e+h]==i&&(this.pivotEngine[e+h]=[]),v=this._setClassName(f[e][o]),f[e][o].CSS=v,v.indexOf("kpiiconvalue")>-1&&(!1||this.olapCtrlObj.pluginName=="ejPivotGrid"||this.olapCtrlObj.pluginName=="ejPivotClient")?f[e][o].Value="":v.indexOf("kpiiconvalue")>-1&&(f[e][o].Value=parseInt(f[e][o].Value)),this.pivotEngine[e+this._indexCCell][o+this._indexRCell]=f[e][o]);if(ft=n.grep(this.pivotEngine,function(n){return n}),this._isPaging){for(t.isNullOrUndefined(this.olapCtrlObj._pagingSavedObjects.curDrilledItem.uniqueName)||this.olapCtrlObj._pagingSavedObjects.curDrilledItem.action!="collapse"?t.isNullOrUndefined(this.olapCtrlObj._pagingSavedObjects.curDrilledItem.index)?t.isNullOrUndefined(this.olapCtrlObj._pagingSavedObjects.curDrilledItem.uniqueName)||(n.each(this.olapCtrlObj._pagingSavedObjects.savedHdrEngine,function(i,r){n.each(r,function(n,r){if(!t.isNullOrUndefined(r)&&r.UName==t.olap.base.olapCtrlObj._pagingSavedObjects.curDrilledItem.uniqueName.split("::")[0].split("amp;").join(""))return t.olap.base.olapCtrlObj._pagingSavedObjects.drillEngine.push(t.olap.base.olapCtrlObj._pagingSavedObjects.savedHdrEngine[i][n]),!1})}),this.olapCtrlObj._pagingSavedObjects.drillEngine[this.olapCtrlObj._pagingSavedObjects.drillEngine.length-1].ChildCount=-1):(this.olapCtrlObj._pagingSavedObjects.drillEngine.push(this.olapCtrlObj._pagingSavedObjects.savedHdrEngine[parseInt(this.olapCtrlObj._pagingSavedObjects.curDrilledItem.index.split(",")[1])][parseInt(this.olapCtrlObj._pagingSavedObjects.curDrilledItem.index.split(",")[0])]),this.olapCtrlObj._pagingSavedObjects.drillEngine[this.olapCtrlObj._pagingSavedObjects.drillEngine.length-1].ChildCount=-1):n.each(this.olapCtrlObj._pagingSavedObjects.drillEngine,function(n,i){if(i.UName==t.olap.base.olapCtrlObj._pagingSavedObjects.curDrilledItem.uniqueName.split("::")[0].split("amp;").join(""))return t.olap.base.olapCtrlObj._pagingSavedObjects.drillEngine.splice(n,1),!1}),n.map(this.olapCtrlObj._pagingSavedObjects.drillEngine,function(i){var r=0,u=n.map(t.olap.base.olapCtrlObj.model.dataSource.columns,function(n,u){return t.olap.base.olapCtrlObj._isMondrian?(r=n.fieldName.replace(/\]/g,"").replace(/\[/g,"")==i.HUName.replace(/\[/g," ").replace(/\[/g,"")?u:r,n.fieldName.replace(/\]/g,"").replace(/\[/g,"")==i.HUName.replace(/\[/g," ").replace(/\[/g,"")?!0:!1):(r=n.fieldName==i.HUName?u:r,n.fieldName==i.HUName?!0:!1)});i.CSS=u[r]?"colheader":"rowheader"}),this.olapCtrlObj._pagingSavedObjects.curDrilledItem={},e=this.pivotEngine[0][0].RowSpan-2;e>=0;e--){for(d="empty",o=1;o<this.pivotEngine[this.pivotEngine[0][0].RowSpan-1].length;o++)t.isNullOrUndefined(this.pivotEngine[e])&&(this.pivotEngine[e]=[]),t.isNullOrUndefined(this.pivotEngine[e+1])||!t.isNullOrUndefined(this.pivotEngine[e][o])||t.isNullOrUndefined(this.pivotEngine[e+1][o])||(this.pivotEngine[e][o]=n.grep(this.olapCtrlObj._pagingSavedObjects.drillEngine,function(n){return n.UName==t.olap.base.pivotEngine[e+1][o].PUName})[0],t.isNullOrUndefined(this.pivotEngine[e][o])||(this.pivotEngine[e][o].ColSpan=t.isNullOrUndefined(this.pivotEngine[e][o-1])||this.pivotEngine[e][o-1].UName!=this.pivotEngine[e][o].UName?1:this.pivotEngine[e][o-1].ColSpan+1,this.pivotEngine[e][o].RowSpan=1,d=this.pivotEngine[e][o].UName));for(a=1,o=this.pivotEngine[this.pivotEngine[0][0].RowSpan-1].length-2;o>0;o--)t.isNullOrUndefined(this.pivotEngine[e])||t.isNullOrUndefined(this.pivotEngine[e][o])||t.isNullOrUndefined(this.pivotEngine[e][o+1])||this.pivotEngine[e][o+1].UName!=this.pivotEngine[e][o].UName?t.isNullOrUndefined(this.pivotEngine[e])||t.isNullOrUndefined(this.pivotEngine[e][o+1])||(this.pivotEngine[e][o+1].ColSpan=a==1?this.pivotEngine[e][o+1].ColSpan:a,a=1):(this.pivotEngine[e][o].ColSpan==this.pivotEngine[e][o+1].ColSpan,a++)}for(e=this.pivotEngine[0][0].RowSpan;e<this.pivotEngine.length;e++)for(c="empty",o=this.pivotEngine[0][0].ColSpan-1;o>=0;o--)t.isNullOrUndefined(this.pivotEngine[e][o])?t.isNullOrUndefined(this.pivotEngine[e-1][o])||this.pivotEngine[e-1][o].UName!=c?(this.pivotEngine[e][o]=n.grep(this.olapCtrlObj._pagingSavedObjects.drillEngine,function(n){return n.UName==c})[0],t.isNullOrUndefined(this.pivotEngine[e][o])||(this.pivotEngine[e][o].RowSpan=1,this.pivotEngine[e][o].ColSpan=1,c=this.pivotEngine[e][o].PUName)):(this.pivotEngine[e][o]=n.grep(this.olapCtrlObj._pagingSavedObjects.drillEngine,function(n){return n.UName==c})[0],t.isNullOrUndefined(this.pivotEngine[e][o])||(this.pivotEngine[e][o].RowSpan=this.pivotEngine[e-1][o].RowSpan+1,this.pivotEngine[e][o].ColSpan=1,c=this.pivotEngine[e][o].PUName)):c=this.pivotEngine[e][o].PUName;this.olapCtrlObj._pagingSavedObjects.savedHdrEngine=b}}catch(ot){this.olapCtrlObj._waitingPopup.hide()}},_pivotEngineSpanCalculation:function(t,r,u,f,e,o,s,h,c,l,a,v){var at=r.length,ot="",k,gt=u.length,g=at?r[0].length:at,p=gt?u[0].length:gt,oi=e.length,si=e[0].length,ti,tt,ut,y,nt,ni,wt,ft,it,ht,ri,vt,bt,kt,yt,dt,ui,fi,ei,lt;t=="rowheader"?(p=this._indexCCell?this._indexCCell:p,ot="row"):(p=this._indexRCell?this._indexRCell:p,ot="col");ti=1;tt=0;a&&(r[s][o].ChildCount=0);this._measureDt.axis==t&&this._measureDt.posision==g-1&&(tt=1);h[o]||(h[o]=[]);this._isNoSummary&&!v[o]&&(v[o]=[]);this.pivotEngine[s+p]||(this.pivotEngine[s+p]=[]);c[o]=s;var ii=f[o]?f[o]:1,w=0,pt=0;for(ut=0;ut<o;ut++)f[ut]&&(w+=f[ut]-1,pt+=f[ut]);if(!this._OlapDataSource._enableBasicEngine||this._OlapDataSource._checkSummaryHeaders)for(y=1;y<=ii;y++){var d=null,st=!1,b=r[s][o].LNum;if(h[o][y]==i&&(h[o][y]=0),this._isNoSummary&&v[o][y]==i&&(v[o][y]=0),r[s-1]&&h[o][b]&&(r[s][o].LNum!=0&&r[s-1][o].PUName==r[s][o].UName||o+tt==g-1&&r[s-1][o].LNum<r[s][o].LNum)&&(nt=h[o][b],this._isNoSummary&&(nt+=v[o][b],v[o][b]=0),this.pivotEngine[s+p-nt][o+b-1+w]=r[s][o],o+tt!=g-1&&(this.pivotEngine[s+p][o+b-1+w]=n.extend({},r[s][o]),this._isNoSummary&&(this.pivotEngine[s+p][o+b-1+w].Span="Block")),this._isNoSummary&&(this.pivotEngine[s+p-nt][o+b-1+w].RowSpan=0),a||(this.pivotEngine[s+p-nt][o+b-1+w].ChildCount=-1),this.pivotEngine[s+p-nt][o+b-1+w].ColSpan=1,this.pivotEngine[s+p-nt][o+b-1+w].RowSpan+=h[o][b],this._drilledJSONData(s+p-nt,s+p,o+b-1+w,r[s][o]),h[o][b]=0,o+tt==g-1&&r[s-1][o].LNum>r[s][o].LNum)){for(ni=f[o]-b,wt=this._findNextMember(r,s,o),ft=0;ft<wt;ft++)this.pivotEngine[s+p+ft]||(this.pivotEngine[s+p+ft]=[]),this.pivotEngine[s+p+ft][o+w+b]={CSS:"summary "+ot,Value:"Total",ColSpan:ni,RowSpan:1,HUName:"",LName:"",UName:"",LNum:"",MemberType:"",ChildCount:"",Span:this._isNoSummary?"Block":"None"};this._isNoSummary||(this.pivotEngine[s+p-nt][o+b-1+w].RowSpan+=wt-1)}if(r[s][o].LNum>y&&!(this._isNoSummary&&l[s].type=="total")?h[o][y]++:r[s][o].LNum>y&&this._isNoSummary&&l[s].type=="total"&&v[o][y]++,d=s+p,y==r[s][o].LNum&&s==0&&r[s][o].LNum>1)this.pivotEngine[d]||(this.pivotEngine[d]=[]),this.pivotEngine[d][o+y-1+w]=r[s][o],(!(r[s+1]!=i&&r[s][o].LNum<r[s+1][o].LNum)||o+tt==g-1&&r[s+1]&&r[s][o].LNum<r[s+1][o].LNum)&&(this.pivotEngine[d][o+y-1+w].ColSpan+=f[o]-this.pivotEngine[d][o+y-1+w].LNum);else if(s!=0&&r[s][o].LNum>1&&y==r[s][o].LNum&&r[s-1][o].UName!=r[s][o].UName&&r[s-1][o].PUName!=r[s][o].UName)this.pivotEngine[d]==i&&(this.pivotEngine[d]=[]),this.pivotEngine[d][o+y-1+w]=r[s][o],(!(r[s+1]!=i&&r[s][o].LNum<r[s+1][o].LNum)||o+tt==g-1&&r[s+1]&&r[s][o].LNum<r[s+1][o].LNum)&&(this.pivotEngine[d][o+y-1+w].ColSpan+=f[o]-this.pivotEngine[d][o+y-1+w].LNum);else if(s!=0&&y==r[s][o].LNum&&r[s-1][o].UName==r[s][o].UName)this._isNoSummary&&l[s].type=="total"?this._isNoSummary&&l[s].type=="total"&&v[o][y]++:h[o][y]++,this.pivotEngine[s+p][o+y-1+w]=r[s][o],this._isNoSummary&&l[s].type=="total"&&(this.pivotEngine[s+p][o+y-1+w].Span="Block"),(r[s+1]!=i&&r[s+1][o].UName!=r[s][o].UName||s+1==at&&h[o][y]>0)&&(it=h[o][y],ht=r[s][o].LNum,this._isNoSummary&&(it+=v[o][y],v[o][y]=0),it&&this.pivotEngine[s+p-it][o+ht-1+w]!=i&&this.pivotEngine[s+p-it][o+ht-1+w].Value!=i&&(this.pivotEngine[s+p-it][o+ht-1+w].RowSpan+=h[o][y],this._drilledJSONData(s+p-it,s+p,o+ht-1+w,r[s][o])),h[o][y]=0);else if(r[s][o].LNum<2&&(y==r[s][o].LNum||r[s][o].LNum==0)||r[s][o].LNum<y&&r[s-1]&&r[s][o].LNum<r[s-1][o].LNum&&l[s].type=="total"&&this._measureDt.axis==t)if(ri=y-1,r[s-1]&&r[s-1][o].UName==r[s][o].UName&&r[s][o].LName.toLowerCase().indexOf("[measures]")>-1&&(this.pivotEngine[s+p][o+y-1+w]=n.extend({},r[s][o]),this._measureDt.measureCount>1&&t==this._measureDt.axis||this._isNoSummary&&l[s].type=="total"?this._measureDt.measureCount>1&&t==this._measureDt.axis||!this._isNoSummary||l[s].type!="total"||v[o][y]++:h[o][y]++,this._isNoSummary&&l[s].type=="total"&&(this.pivotEngine[s+p][o+y-1+w].Span="Block")),r[s][o].UName.indexOf("Measures")!=-1&&this._isPaging||l[s].type=="total")if(l[s].type=="total"&&r[s][o].LName.toLowerCase().indexOf("[measures]")==-1&&r[s][o].LNum!=y&&f[o]!=0){if((r[s][o-1]&&r[s][o-1].LName.toLowerCase().indexOf("[measures]")==-1||this._measureDt.axis!=t)&&(o>l[s].totLvl||o==g-1&&y>l[s].totLvl&&l[s].totLvl!=0||l[s].totLvl==0&&y-1>l[s].totLvl))break;for(var rt=1,ct=1,et=0;r[s-ct][o]&&r[s-ct][o].LNum==0&&!r[s][o].LName.toLowerCase().indexOf("[measures]")>-1;)r[s][o-1]&&r[s-ct+1][o-1].UName!=r[s-ct][o-1].UName&&et++,ct++;if(0){for(vt=0,st=!1,et=0,o<this._measureDt.posision?(bt=o,kt=this._measureDt.posision):o>this._measureDt.posision&&(kt=f.length,bt=o,st=!0),yt=bt;yt<kt;yt++)vt+=f[yt];r[s][o+1]&&r[s][o+1].LName.toLowerCase().indexOf("[measures]")>-1&&f[o]>1&&(st=!0,vt-=r[s][o].LNum);rt=vt}else r[s][o].LNum==0&&!r[s][o].LName.toLowerCase().indexOf("[measures]")>-1&&(lt=0,dt=0,s>0&&o>0&&r[s][o].LName.indexOf("(All)")>-1&&r[s][o-1]&&f[o-1]>1&&(lt=this._findPreviousMember(r,s,o-1)),s!=0&&r[s][o-1]&&f[o-1]>1&&r[s+lt]&&r[s][o-1].LNum<r[s+lt][o-1].LNum&&(et=f[o-1]-r[s][o-1].LNum),t=="rowheader"?(rt=this._indexRCell-pt+et,dt=this._indexRCell-rt):t=="colheader"&&(rt=this._indexCCell-pt+et,dt=this._indexCCell-rt),(f.length!=1&&o==g-1||f.length>1&&o<g-1)&&(st=!0),this._measureDt.axis==t&&(rt-=1),fi=1,s!=0&&r[s][o-1]&&(ui=r[s][o-1].LNum+1),ei=1,lt=s+p,this.pivotEngine[s+p][o+y-1+w-et]={CSS:"summary "+ot,Value:"Total",ColSpan:rt,RowSpan:1,HUName:"",LName:"",UName:"",LNum:"",MemberType:"",ChildCount:"",Span:this._isNoSummary?"Block":"None"})}else r[s][o].LName.toLowerCase().indexOf("[measures]")!=-1?(r[s-1]&&r[s-1][o].UName==r[s][o].UName||(this.pivotEngine[s+p][o+y-1+w]=n.extend({},r[s][o]),this._isNoSummary&&l[s].type=="total"&&(this.pivotEngine[s+p][o+y-1+w].Span="Block")),(r[s-1]&&r[s-1][o].UName!=r[s][o].UName||s+1==at)&&(k=h[o][y],this._isNoSummary&&(k+=v[o][y],v[o][y]=0),k&&this.pivotEngine[s+p-k][o+y-1+w]!=i&&(this.pivotEngine[s+p-k][o+y-1+w].RowSpan+=h[o][y],this._drilledJSONData(s+p-k,s+p,o+y-1+w,r[s][o])),h[o][y]=0)):l[s].type=="total"&&r.length==1&&(this.pivotEngine[s+p][o+y-1+w]={CSS:"summary "+ot,Value:"Total",ColSpan:1,RowSpan:1,HUName:"",LName:"",UName:"",LNum:"",MemberType:"",ChildCount:"",Span:this._isNoSummary?"Block":"None"});else r[s-1]&&r[s-1][o].UName==r[s][o].UName||(this.pivotEngine[s+p][o+y-1+w]=r[s][o],k=h[o][y],this._isNoSummary&&(k+=v[o][y],v[o][y]=0),k&&this.pivotEngine[s+p-k][o+y-1+w]!=i&&(this.pivotEngine[s+p-k][o+y-1+w].RowSpan+=h[o][y],this._drilledJSONData(s+p-k,s+p,o+y-1+w,r[s][o])),h[o][y]=0),!r[s][o].LNum==0&&(!(r[s+1]!=i&&r[s][o].LNum<r[s+1][o].LNum)||o+tt==g-1&&r[s+1]&&r[s][o].LNum<r[s+1][o].LNum)&&this.pivotEngine[s+p][o+y-1+w]&&(this.pivotEngine[s+p][o+y-1+w].ColSpan+=f[o]-this.pivotEngine[s+p][o+y-1+w].LNum);if(st)break}else this.pivotEngine[s+p][o]=r[s][o]},_findNextMember:function(n,t,i){for(var r=0;n[t+r]&&n[t][i].UName==n[t+r][i].UName;)r++;return r},_findPreviousMember:function(n,t,i){for(var r=-1;n[t+r]&&n[t][i].UName==n[t+r][i].UName;)r--;return r},_drilledJSONData:function(t,i,r,u){for(var f=t+1;f<i;f++)this.pivotEngine[f][r]=n.extend({},u),this._isNoSummary&&(this.pivotEngine[f][r].Span="Block")},_setClassName:function(n){var i=parseInt(n.Value),t="value";if(n.kpi)if(n.kpi=="trend")switch(i){case-1:t="value kpiiconvalue kpidownarrow";break;case 0:t="value kpiiconvalue kpirightarrow";break;case 1:t="value kpiiconvalue kpiuparrow"}else if(n.kpi=="status")switch(i){case-1:t="value kpiiconvalue kpidiamond";break;case 0:t="value kpiiconvalue kpitriangle";break;case 1:t="value kpiiconvalue kpicircle"}return n.summary+" "+t},_getFieldItemsInfo:function(n){var t=this._getConnectionInfo(n.model.dataSource.data),i='<Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/"><Header/><Body><Discover xmlns="urn:schemas-microsoft-com:xml-analysis"><RequestType>MDSCHEMA_HIERARCHIES<\/RequestType><Restrictions><RestrictionList><CATALOG_NAME>'+n.model.dataSource.catalog+"<\/CATALOG_NAME><CUBE_NAME>"+n.model.dataSource.cube+"<\/CUBE_NAME><\/RestrictionList><\/Restrictions><Properties><PropertyList><Catalog>"+n.model.dataSource.catalog+"<\/Catalog> <LocaleIdentifier>"+t.LCID+"<\/LocaleIdentifier><\/PropertyList><\/Properties><\/Discover><\/Body><\/Envelope>";n.doAjaxPost("POST",t.url,{XMLA:i},this._getHierarchyInfo,null,{pvtGridObj:n,action:"loadFieldElements"})},_getHierarchyInfo:function(t,i){for(var u,s,o=[],r=t.pvtGridObj,f=this._getConnectionInfo(r.model.dataSource.data),e=0;e<n(i).find("row").length;e++)u=n(n(i).find("row")[e]),o.push({pid:u.find("DIMENSION_UNIQUE_NAME").text(),id:u.find("HIERARCHY_UNIQUE_NAME").text(),name:u.find("HIERARCHY_CAPTION").text(),tag:u.find("HIERARCHY_UNIQUE_NAME").text(),hasAllMember:u.children("ALL_MEMBER").length==0?!0:!1});r._fieldData={hierarchy:o,hierarchySuccess:i,measures:[]};f.LCID.indexOf("1033")>=0&&!r.model.enableDrillThrough||(s='<Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/"><Header/><Body><Discover xmlns="urn:schemas-microsoft-com:xml-analysis"><RequestType>MDSCHEMA_MEASURES<\/RequestType><Restrictions><RestrictionList><CATALOG_NAME>'+r.model.dataSource.catalog+"<\/CATALOG_NAME><CUBE_NAME>"+r.model.dataSource.cube+"<\/CUBE_NAME><\/RestrictionList><\/Restrictions><Properties><PropertyList><Catalog>"+r.model.dataSource.catalog+"<\/Catalog> <LocaleIdentifier>"+f.LCID+"<\/LocaleIdentifier> <\/PropertyList><\/Properties><\/Discover><\/Body><\/Envelope>",r.doAjaxPost("POST",f.url,{XMLA:s},this._getMeasureInfo,null,{pvtGridObj:r,action:"loadFieldElements"}))},_getMeasureInfo:function(t,i){for(var f=[],r=0;r<n(i).find("row").length;r++){var u=n(n(i).find("row")[r]),o=u.children("MEASUREGROUP_NAME").text(),e=u.find("MEASURE_UNIQUE_NAME").text();f.push({id:e,pid:o,name:u.children("MEASURE_CAPTION").text(),tag:e})}t.pvtGridObj._fieldData.measures=f;t.pvtGridObj._fieldData.measureSuccess=i},_getConnectionInfo:function(t){var i={url:"",LCID:"1033"};return t!=""&&n.map(t.split(";"),function(n){n.toLowerCase().indexOf("locale")<0&&i.url.length==0?i.url=n:n.toLowerCase().indexOf("locale")>=0&&(i.LCID=n.replace(/ /g,"").split("=")[1])}),i},_applyTrim:function(r){var f=[],u=r.model.dataSource,e=this;r._fieldData&&r._fieldData.measures&&r._fieldData.hierarchy?(n.merge(f,r._fieldData.hierarchy),n.merge(f,r._fieldData.measures)):r._fieldData&&r._fieldData.hierarchy?r._fieldData.hierarchy:[];u.rows=n.map(u.rows,function(r){if(!t.isNullOrUndefined(r)&&r.fieldName!=i)return r.fieldName=n.trim(r.fieldName),e._getCaption(r,f)});u.columns=n.map(u.columns,function(r){if(!t.isNullOrUndefined(r)&&r.fieldName!=i)return r.fieldName=n.trim(r.fieldName),e._getCaption(r,f)});u.filters=n.map(u.filters,function(r){if(!t.isNullOrUndefined(r)&&r.fieldName!=i)return r.fieldName=n.trim(r.fieldName),e._getCaption(r,f)});u.values=u.values.length>0&&u.values[0].measures!=i?u.values:[{measures:[],axis:"columns"}];u.values[0].measures=n.map(u.values[0].measures,function(r){if(!t.isNullOrUndefined(r)&&r.fieldName!=i)return r.fieldName=n.trim(r.fieldName),e._getCaption(r,f)});r.model.dataSource=u},_getCaption:function(r,u){var e=r.fieldName,f=[];return t.isNullOrUndefined(r.fieldCaption)&&(u.length>0?(f=n.map(u,function(t){if(t.tag!=i&&t.tag.toLowerCase()==n.trim(e.toLowerCase()))return t}),e.toLowerCase().indexOf("[measures]")>=0&&f.length==0&&e.split(".[").length>0?r.fieldCaption=e.split(".[")[1].replace(/]/g,""):f.length>0&&(r.hasAllMember=f[0].hasAllMember?!0:!1,r.fieldCaption=f[0].name)):f.length==0&&(r.fieldCaption=e)),r},clearDrilledItems:function(r,u,f){var e=u.action;return r.rows=n.grep(r.rows,function(n){return n.filterItems!=i&&e!="filtering"?delete n.filterItems:n,n.filterItems!=i&&e!="advancedFilter"?delete n.advancedFilter:n,n.drilledItems!=i?delete n.drilledItems:n,n._prevDimElements!=i?delete n._prevDimElements:n,n}),r.columns=n.grep(r.columns,function(n){return n.filterItems!=i&&e!="filtering"?delete n.filterItems:n,n.filterItems!=i&&e!="advancedFilter"?delete n.advancedFilter:n,n.drilledItems!=i?delete n.drilledItems:n,n._prevDimElements!=i?delete n._prevDimElements:n,n}),r.filters=n.grep(r.filters,function(n){return n.filterItems!=i&&e!="filtering"?delete n.filterItems:n,n.filterItems!=i&&e!="advancedFilter"?delete n.advancedFilter:n,n.drilledItems!=i?delete n.drilledItems:n,n._prevDimElements!=i?delete n._prevDimElements:n,n}),e!="filtering"&&(f._currentReportItems=n.map(f._currentReportItems,function(n){if(!t.isNullOrUndefined(n.dataSrc))if(n.dataSrc.cube==r.cube){if(!(n.dataSrc.reportName==r.reportName))return n}else return n}),t.isNullOrUndefined(f._schemaData)||f._schemaData.element.find(".filter").remove(),f._savedReportItems=n.extend(!0,[],f._currentReportItems)),t.olap.base._clearDrilledCellSet(),r}};t.olap._mdxParser={_getRowMDX:function(r){var e=n(r)[0].rows,u="",o="",c=[],l=!1,s=!1,a=r._isCollapse,y=r.values.length>0&&r.values[0].measures!=i&&r.values[0].axis==t.olap.AxisName.Row,f,w;if(t.isNullOrUndefined(r.providerName)||r.providerName==t.olap.Providers.SSAS){if(e.length>0){for(s=t.isNullOrUndefined(e[0].drillCellInfo)?!1:!0,f=0;f<e.length;f++){var p=t.isNullOrUndefined(e[f].isNamedSets)||!e[f].isNamedSets?!1:!0,h="",v="";e[f].fieldName==i||p?p&&(u=f>0?u+"*{"+e[f].fieldName+"}":"{"+e[f].fieldName+"}"):(h=this._getDimensionQuery(e[f],r,"rows",f,!1),c.push(h.replace(/["'\(\)]/g,"").replace(/["'\{\}]/g,"").replace(/\levels0/g,"levels(0)")),t.olap.base._isPaging&&s&&(v=this._getDimensionQuery(e[f],r,"rows",f,!0)),e[f].sortOrder&&e[f].sortOrder!=t.olap.SortOrder.None?(w=e[f].sortOrder==t.olap.SortOrder.Ascending?"asc":"desc",l=!0,u=u+(f>0?"*":"")+"{ORDER({HIERARCHIZE({"+(t.olap.base._isPaging&&s?v:h)+"})},"+e[f].fieldName+".CurrentMember.MEMBER_CAPTION,"+w+")}"):u=u+(f>0?"*":"")+"{HIERARCHIZE({({"+(t.olap.base._isPaging&&s?v:h)+"})})}")}t.olap.base._isPaging&&this._updateOlapReport(n(r)[0].rows,u,"rows",c);a&&(l=a);u=(l?" ":" HIERARCHIZE ")+"( {"+(t.olap.base._isPaging?u:this._updateOlapReport(n(r)[0].rows,u,"rows",c))+"})";a&&(u="HIERARCHIZE("+u+")")}y&&(o=t.olap._mdxParser._getMeasuresQuery(r),u=u!=""?(t.olap.base._isPaging?u.slice(0,-1):u)+(u!=""&&o!=""?t.olap.base._isPaging?"*":",":"")+o:(t.olap.base._isPaging?o!=""?"(":"":"")+o);t.olap.base._isPaging&&(u=u!=""?o==""?(u[u.length-1]!="}"?u.slice(0,-1):u)+this._getDrilledSection(r,n.extend(!0,{},e),"row",o)+")":u+this._getDrilledSection(r,n.extend(!0,{},e),"row",o)+")":"")}else u=this._generateAxisMDXOnMondrian(r,e,y);return u},_generateAxisMDXOnMondrian:function(n,i,r){for(var f,e="",s,o,u=0;u<i.length;u++)f=t.olap.base._isPaging?"({"+i[u].fieldName+".children})":"DrillDownLevel({"+i[u].fieldName+"})",!t.isNullOrUndefined(i[u].drilledItems)&&i[u].drilledItems.length>0&&(f=this._getDrilledMDXOnMondrian(i[u].drilledItems,f)),!t.isNullOrUndefined(i[u].filterItems)&&!t.isNullOrUndefined(i[u].filterItems.values)&&i[u].filterItems.values.length>0&&(s="Descendants("+i[u].fieldName+","+n._maxLevel+", SELF_AND_BEFORE)",o="{"+i[u].filterItems.values.join()+"}",f="({{hierarchize(Union(Intersect((Except("+s+","+o+")),"+f+"),Except("+f+","+o+")))}})"),e+=(e==""?"":"*")+f;return r&&(e+=(e==""?"":"*")+t.olap._mdxParser._getMeasuresQuery(n)),e},_getDrilledMDXOnMondrian:function(n,i){for(var r=0;r<n.length;r++)i=t.olap.base._isPaging?"Except(DrillDownMember("+i+", {"+n[r].join()+"}),{"+n[r].join()+"})":"(DrillDownMember("+i+", {"+n[r].join()+"}))";return i},_getcolumnMDX:function(r){var e=n(r)[0].columns,u="",o="",c=[],l=!1,s=!1,a=r._isCollapse,y=r.values.length>0&&r.values[0].measures!=i&&r.values[0].axis==t.olap.AxisName.Column,f,p;if(t.isNullOrUndefined(r.providerName)||r.providerName==t.olap.Providers.SSAS){if(e.length>0){for(s=t.isNullOrUndefined(e[0].drillCellInfo)?!1:!0,f=0;f<e.length;f++){var w=t.isNullOrUndefined(e[f].isNamedSets)||!e[f].isNamedSets?!0:!1,l=!1,h="",v="";e[f].fieldName!=i&&w?(h=this._getDimensionQuery(e[f],r,"columns",f,!1),c.push(h.replace(/["'\(\)]/g,"").replace(/["'\{\}]/g,"")),t.olap.base._isPaging&&s&&(v=this._getDimensionQuery(e[f],r,"columns",f,!0)),e[f].sortOrder&&e[f].sortOrder!=t.olap.SortOrder.None?(p=e[f].sortOrder==t.olap.SortOrder.Ascending?"asc":"desc",l=!0,u=u+(f>0?"*":"")+"{ ORDER ({HIERARCHIZE ({"+(t.olap.base._isPaging&&s?v:h)+"})},"+e[f].fieldName+".CurrentMember.MEMBER_CAPTION,"+p+")}"):u=u+(f>0?"*":"")+"{"+(t.olap.base._isPaging&&s?v:h)+"}"):u=f>0?u+"*{"+e[f].fieldName+"}":"{"+e[f].fieldName+"}"}t.olap.base._isPaging&&s&&this._updateOlapReport(n(r)[0].columns,u,"columns",c);a&&(l=a);u=(l?" ":" HIERARCHIZE ")+"( {"+(t.olap.base._isPaging?u:this._updateOlapReport(e,u,"columns",c))+"})";a&&(u="HIERARCHIZE("+u+")")}y&&(o=t.olap._mdxParser._getMeasuresQuery(r),u=u!=""?(t.olap.base._isPaging?u.slice(0,-1):u)+(u!=""&&o!=""?t.olap.base._isPaging?"*":",":"")+o:(t.olap.base._isPaging?o!=""?"(":"":"")+o);t.olap.base._isPaging&&(u=u!=""?o==""?(u[u.length-1]!="}"?u.slice(0,-1):u)+this._getDrilledSection(r,n.extend(!0,{},e),"column",o)+")":u+this._getDrilledSection(r,n.extend(!0,{},e),"column",o)+")":"")}else u=this._generateAxisMDXOnMondrian(r,e,y);return u},_getDrilledSection:function(i,r,u,f){var e="",r=r;return n.each(r,function(i,r){t.isNullOrUndefined(r.drilledItems)||n.each(r.drilledItems,function(t,r){var u="",o="";n.each(r,function(n,t){u=u==""?"{"+t+"}":u+"*{"+t+"}";t=n==i?t.replace(".children",""):t;o=o==""?"{"+t+"}":o+"*{"+t+"}"});f!=""&&(u=u+"*"+f,o=o+"*"+f);e=e==""?"+{"+u+"}-{"+o+"}":e+"+{"+u+"}-{"+o+"}"})}),e},_getSortedMembers:function(i){var r=i.fieldName,u;return i.sortOrder&&i.sortOrder!=t.olap.SortOrder.None?(u=i.sortOrder=="ascending"?"asc":"desc",n.map(i.drilledItems,function(t){return n.map(t,function(n){return n.indexOf(r)>=0&&(n="order ( "+n+" , "+r+".CurrentMember.MEMBER_CAPTION,"+u+")"),n})})):i.drilledItems},_getAvailCount:function(i,r,u){var f=0;return n.each(i.split("--"),function(n,i){i=i.replace(u[n],"");r.split("--").length>0&&!t.isNullOrUndefined(r.split("--")[n])&&(f+=i.indexOf(r.split("--")[n])>-1&&!((i+"$").indexOf(r.split("--")[n])>-1)||r.split("--")[n].indexOf(i)>-1&&!(r.split("--")[n].indexOf(i+"$")>-1)?1:0)}),f},_updateTmpItems:function(i,r,u,f){var e=n.map(r.split("--"),function(n,i){return t.isNullOrUndefined(u.split("--")[i])||n.replace(f[i],"")!=u.split("--")[i].replace(f[i],"")||n.indexOf(f[i])!=-1?n:u.split("--")[i]}).join("--");i.indexOf(e)==-1&&i.push(e)},_updateExpandCollection:function(i,r,u){return n.each(r,function(f,e){n.each(r,function(r,f){var s,o,h;e!=f&&(s=0,n.each(f.split("--"),function(n,i){i=i.replace(u[n],"");t.isNullOrUndefined(e.split("--")[n])||(s+=f.split(u[n]).join("").indexOf(e.split("--")[n].replace(u[n],""))>-1||e.split("--")[n].replace(u[n],"").indexOf(i)>-1?1:0)}),s==f.split("--").length&&t.olap._mdxParser._updateTmpItems(i,e,f,u),s==e.split("--").length&&t.olap._mdxParser._updateTmpItems(i,f,e,u),o=[],h=0,n.each(f.split("--"),function(n,i){var r=e.split("--").length>n?e.split("--")[n]:"";t.isNullOrUndefined(r)||r==""||(i!=r&&i.indexOf("$")>-1&&(i.indexOf(r)>-1||r.indexOf(i)>-1)&&e.split("--").length-1>n?(o.push(i),h++):o.push(r))}),h>0&&i.push(o.concat(n.map(e.split("--"),function(n,t){if(t>=o.length)return n})).join("--")))})}),i},_updateDrillQuery:function(i,r,u,f){var a=t.olap.base._isRowDrilled||t.olap.base._isColDrilled,e=n.map(i,function(i){var r=n.map(t.olap.base.olapCtrlObj._prevDrillElements,function(n){if(n.indexOf(i.fieldName)>-1)return n}).join("$");if(r!="")return r}).join("--"),h=n.map(f,function(n){return n.indexOf("DrillDownlevel")>=0&&(n=n.replace("DrillDownlevel","DrillDownlevel(")+")"),n}),o=n.map(i,function(n){return!t.isNullOrUndefined(n.hasAllMember)&&n.hasAllMember?".levels(0).AllMembers":".children"}),l,y,v,p;a||e==""||(t.isNullOrUndefined(i[e.split("--").length-1]._prevDimElements)||i[e.split("--").length-1]._prevDimElements.indexOf(e+o[e.split("--").length-1])!=-1?i[e.split("--").length-1]._prevDimElements=i[e.split("--").length-1]._prevDimElements.filter(function(n){if(t.olap._mdxParser._getAvailCount(n,e,o)!=e.split("--").length)return n}):i[e.split("--").length-1]._prevDimElements.push(e+o[e.split("--").length-1]+"!#collapse"));i=n.map(i,function(i){return t.isNullOrUndefined(i._prevDimElements)?i:(i._prevDimElements=n.map(i._prevDimElements,function(i){var r=i.indexOf("!#collapse")==-1?!1:!0;if(a)if(i.indexOf("!#collapse")>-1){if(i=i.replace("!#collapse",""),t.olap._mdxParser._getAvailCount(i,e,o)!=e.split("--").length)return i}else return i;else return e!=""?(i=i.replace("!#collapse",""),t.olap._mdxParser._getAvailCount(i,e,o)==e.split("--").length?n.map(i.split("--"),function(n,t){return t==e.split("--").length-1&&n.indexOf(o[t])==-1?e.split("--")[e.split("--").length-1]+(r?"":""):n}).join("--")+(r?"!#collapse":""):i+(r?"!#collapse":"")):i}),e!=""&&(i._prevDimElements=n.map(i._prevDimElements,function(n){if(!(n.indexOf("!#collapse")>-1&&i._prevDimElements.filter(function(n){if(n.indexOf("!#collapse")==-1)return n}).indexOf(n.replace("!#collapse",""))>-1)&&!(n.indexOf("!#collapse")==-1&&i._prevDimElements.map(function(n){if(n.indexOf("!#collapse")>-1)return n.replace("!#collapse","")}).indexOf(n)>-1))return n})),i)});var s=n.map(i,function(n){if(!t.isNullOrUndefined(n._prevDimElements))return n._prevDimElements}).filter(function(n){if(n.indexOf("!#collapse")==-1)return n}),c=n.map(i,function(n){if(!t.isNullOrUndefined(n._prevDimElements))return n._prevDimElements}).filter(function(n){if(n.indexOf("!#collapse")>-1)return n}).map(function(n){return n.replace("!#collapse","")}),w=e.length==0?i.length:e.split("--").length,v=[];return a&&(s=n.map(s,function(n){if(t.olap._mdxParser._getAvailCount(n,e,o)==e.split("--").length)return n})),l=t.olap._mdxParser._updateExpandCollection([],s,o),y=t.olap._mdxParser._updateExpandCollection([],l,o),s=s.concat(l.concat(y)),l=[],n.each(s,function(t,i){n.each(c,function(t,r){var u=[],f=0;i.split("--").length>r.split("--").length&&(n.each(r.split("--"),function(n,t){var r=i.split("--")[n];(t.replace(o[n],"").indexOf(r.replace(o[n],""))>-1||r.replace(o[n],"").indexOf(t.replace(o[n],""))>-1)&&(u.push(t),f++)}),r.split("--").length==f&&f<i.split("--").length&&l.indexOf(u.concat(n.map(i.split("--"),function(n,t){if(t>=u.length)return n})).join("--"))==-1&&c.indexOf(u.concat(n.map(i.split("--"),function(n,t){if(t>=u.length)return n})).join("--"))==-1&&l.push(u.concat(n.map(i.split("--"),function(n,t){if(t>=u.length)return n})).join("--")))})}),c=c.concat(l),s=n.map(s,function(i){var r=a?w:i.split("--").length;return n.map(i.split("--"),function(n,i){return i<r-1?!t.isNullOrUndefined(e.split("--")[i])&&(e.split("--")[i].indexOf(n.replace(o[i],"")>-1)||n.replace(o[i],"").indexOf(e.split("--")[i])>-1)&&a?h[i].split("&amp;").join("&"):n.split("$")[n.split("$").length-1]:i==r-1?a?h[i].split("&amp;").join("&"):n.replace(o[i],"").split("$")[n.split("$").length-1]+o[i]:n.split("$")[n.split("$").length-1]}).join("--")}).sort(function(n,t){return n.split("--").length-t.split("--").length}),s=n.map(s,function(t){var i=[];return i=t.split("--").length<h.length?[t.split("&").join("&amp;").split("--").concat(n.map(h,function(n,i){if(t.split("--").length-1<i)return n}))]:[t.split("&").join("&amp;").split("--")],i.join()!=h.join()?i:void 0}),c=n.map(c,function(t){return t.split("--").length<h.length?[t.split("&").join("&amp;").split("--").map(function(n){return n.split("$")[n.split("$").length-1]}).concat(n.map(h,function(n,i){if(t.split("--").length-1<i)return n}))]:[t.split("&").join("&amp;").split("--").map(function(n){return n.split("$")[n.split("$").length-1]})]}),v=[],n.each(n.map(s,function(n){return n.join("--")}),function(n,t){(v.length==0||v.indexOf(t)==-1)&&v.push(t)}),s=n.map(v,function(n){return[n.split("--")]}),p={expandCollection:s,collapseCollection:c},s.length>0&&i.length>1&&f[f.length-1].indexOf(".children")<0&&(r=this._getDrillQuery(p,r,i)),r},_updateOlapReport:function(r,u,f,e){var c=this,s,h,o;return f=="rows"?(s=n.map(r,function(n,t){if(r[t].drilledItems!=i)return c._getSortedMembers(n,r)}),t.olap.base._isRowDrilled&&"rows"==t.olap.base._currIndex.axis&&r[t.olap.base._currIndex.Index]!=i&&r[t.olap.base._currIndex.Index].drilledItems!=i?(o=n.map(e,function(n){return n.indexOf("DrillDownlevel")>=0&&(n=n.replace("DrillDownlevel","DrillDownlevel(")+")"),n}),r[t.olap.base._currIndex.Index].drilledItems.push(o),t.olap.base._isRowDrilled=!1):s.length>0&&(t.olap.base._currIndex.axis==i||t.olap.base._currIndex.axis=="columns"&&!t.olap.base._isRowDrilled)&&(u=this._getDrillQuery(s,u,r)),t.isNullOrUndefined(t.olap.base._currIndex.axis)||t.olap.base._currIndex.axis!="rows"||(t.olap.base._currIndex={})):(h=n.map(r,function(n,t){if(r[t].drilledItems!=i)return c._getSortedMembers(n,r)}),t.olap.base._isColDrilled&&"columns"==t.olap.base._currIndex.axis&&r[t.olap.base._currIndex.Index]!=i&&r[t.olap.base._currIndex.Index].drilledItems!=i?(o=n.map(e,function(n){return n.indexOf("DrillDownlevel")>=0&&(n=n.replace("DrillDownlevel","DrillDownlevel(")+")"),n}),r[t.olap.base._currIndex.Index].drilledItems.push(o),t.olap.base._isColDrilled=!1):h.length>0&&(t.olap.base._currIndex.axis==i||t.olap.base._currIndex.axis=="rows"&&!t.olap.base._isColDrilled)&&(u=this._getDrillQuery(h,u,r)),t.isNullOrUndefined(t.olap.base._currIndex.axis)||t.olap.base._currIndex.axis!="columns"||(t.olap.base._currIndex={})),u},_getDrillQuery:function(r,u,f){for(var c,s,l,h="",o=r,e=0;e<o.length;e++)if(o[e][o[e].length-1].toLowerCase().indexOf(".children")>=0||o[e][o[e].length-1].toLowerCase().indexOf("drilldownlevel")>=0||o[e][o[e].length-1].toLowerCase().indexOf("members")>=0||(o[e][o[e].length-1]=o[e][o[e].length-1]+".children"),o[e].length!=f.length){for(c=n.map(f,function(r){if(r.fieldName!=i&&r.fieldName!=o[e][[o[e].length-1]].split(".").splice(0,2).join("."))return{uniqueName:t.olap.base._isPaging?"("+n.trim(r.fieldName)+").children":"DrillDownLevel("+n.trim(r.fieldName)+")",fieldName:n.trim(r.fieldName)}}),s=0;s<c.length;s++)l=this._getItemPosition(f,c[s].fieldName)[0],o[e].toString().indexOf(c[s].fieldName)>=0||o[e].splice(l,0,c[s].uniqueName);h=h+""+(e>0?",":"")+"\n ("+o[e]+")"}else h=h+""+(e>0?",":"")+"\n ("+o[e].toString()+")\n";return"("+u+"),"+h},_createDrillThroughQuery:function(i,r){var e,s;if(r.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode)r._waitingPopup.show(),e=n(r.element).hasClass("e-pivotclient")?r.element.find(".e-pivotgrid").data("ejPivotGrid"):r,r.doAjaxPost("POST",r.model.url+"/"+r.model.serviceMethodSettings.drillThroughDataTable,JSON.stringify({currentReport:n(r.element).parents(".e-pivotclient").length>0?r.currentReport:JSON.parse(e.getOlapReport()).Report,layout:n(r.element).hasClass("e-pivotclient")?r.model.gridLayout:r.model.layout,cellPos:"",selector:i}),function(n){this._trigger("drillThrough",{element:n.element,data:n})}),r._waitingPopup.hide();else{var u=r.measureGrp,o="",f="DRILLTHROUGH Select("+r._colHeader.join()+","+r._rowHeader.join()+") on 0 from ["+r.model.dataSource.cube+"]";n.map(r._fieldData.measures,function(n){n.pid==u&&(o+=o==""?"["+u+"]."+n.id.split(".")[1]:",["+u+"]."+n.id.split(".")[1])});f=f.replace(/&/g,"&amp;");s=r._getConnectionInfo(r.model.dataSource.data);pData='<Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/"> <Header><\/Header> <Body> <Execute xmlns="urn:schemas-microsoft-com:xml-analysis"> <Command> <Statement> '+f+" <\/Statement> <\/Command> <Properties> <PropertyList> <Catalog>"+r.model.dataSource.catalog+"<\/Catalog> <\/PropertyList> <\/Properties><\/Execute> <\/Body> <\/Envelope>";r.doAjaxPost("POST",s.url,{XMLA:pData},t.Pivot._generateDrillData,null,{pvtGridObj:r,action:"loadFieldElements"})}},_getDimensionQuery:function(r,u,f,e,o){var s="";return r.drillCellInfo==i||o?s=t.isNullOrUndefined(r.expression)?t.isNullOrUndefined(r.hasAllMember)||!r.hasAllMember?t.olap.base._isPaging?"(("+n.trim(r.fieldName)+").children)":"DrillDownlevel(("+n.trim(r.fieldName)+"))":"(("+n.trim(r.fieldName)+").levels(0).AllMembers)":"("+r.hierarchyUniqueName+".["+(r.fieldCaption||r.fieldName)+"])":(s="{("+r.drillCellInfo.uniqueName+")}",delete r.drillCellInfo),s},_updateReport:function(i,r,u,f){var s,e,h={},o;f._isMondrian?(s=r.preRepItm.split(">#>"),o=r.itemPosition,e=o?r.previousElements.split(s[o-1])[1].split("][").length:r.previousElements.split("][").length,u.drilledItems&&(u.drilledItems.length<=e-1&&(u.drilledItems[e-1]=[]),r.action&&r.action=="collapse"?u.drilledItems[e-1]=n.grep(u.drilledItems[e-1],function(n){return n!=s[o]}):u.drilledItems[e-1].push(s[o])),h={action:"mondrianDrilldown"}):(u.drillCellInfo=n.extend({},r),u.drillCellInfo.uniqueName="("+n.trim(u.drillCellInfo.uniqueName)+".children)",h={action:"drilldown",cellInfo:r});t.olap.base.getJSONData(h,f.model.dataSource,f)},_splitCellInfo:function(n){var r=t.olap.base.olapCtrlObj._isMondrian?1:2,i;return n&&(i={hierarchyUniqueName:n.split("::")[1].split(".").splice(0,r).join("."),uniqueName:n.split("::")[0],levelUniqueName:n.split("::")[1],leveName:n.split("::")[2],parentUniqueName:n.split("::")[3]}),i},updateDrilledReport:function(r,u,f){var g,o,p,s,c,tt;t.olap.base._isPaging&&!t.isNullOrUndefined(f._pagingSavedObjects.curDrilledItem)&&(f._pagingSavedObjects.curDrilledItem=r);var it=t.olap._mdxParser,l=null,e,w,b="",a="",k,v;if(e=this._splitCellInfo(r.uniqueName),e.targetUName=r.uniqueName,e.previousElements="",w=u=="rowheader"?f.model.dataSource.rows:u=="colheader"?f.model.dataSource.columns:null,w.length>0){if(k=n.map(w,function(t,r){return t.fieldName!=i&&n.trim(t.fieldName).toLowerCase()==n.trim(e.hierarchyUniqueName).toLowerCase()?{report:t,index:r}:t.hierarchyUniqueName!=i&&n.trim(t.hierarchyUniqueName).toLowerCase()==n.trim(e.hierarchyUniqueName).toLowerCase()?{report:t,index:r}:void 0}),l=k[0].report,e.itemPosition=k[0].index,!t.isNullOrUndefined(f._pivotRecords)||t.isNullOrUndefined(f._pivotClientObj)||t.isNullOrUndefined(f._pivotClientObj._pivotRecords)||(f._pivotRecords=f._pivotClientObj._pivotRecords),g=n.map(f._pivotRecords.records,function(n){if(n.Info.replace(/&/g,"&amp;")==r.uniqueName)return n.Index}),l.drilledItems!=i?l.drilledItems:l.drilledItems=[],t.olap.base._currIndex={axis:u=="rowheader"?"rows":"columns",Index:k[0].index},f.pluginName=="ejPivotChart"||f.pluginName=="ejPivotTreeMap"||t.isNullOrUndefined(r.index)){for(e.cellIndex=g.length>0?"":r.index,e.axis=v=u,u!="rowheader"?t.olap.base._isColDrilled=r.action!=i&&r.action=="collapse"?!1:!0:t.olap.base._isRowDrilled=r.action!=i&&r.action=="collapse"?!1:!0,o=0;o<f.model.dataSource.rows.length;o++)if(r.seriesInfo[o]==r.uniqueName)break;else f.model.dataSource.rows[o].drillCellInfo=this._splitCellInfo(r.seriesInfo[o]);for(e.previousElements="",e.preRepItm="",o=0;o<r.uniqueNameArray.length;o++)e.previousElements+=this._splitCellInfo(r.uniqueNameArray[o]).uniqueName;for(o=0;o<r.seriesInfo.length;o++)e.preRepItm+=e.preRepItm==""?this._splitCellInfo(r.seriesInfo[o]).uniqueName:">#>"+this._splitCellInfo(r.seriesInfo[o]).uniqueName}else{e.cellIndex=r.index;var y=e.hierarchyUniqueName,v="",b="",h="",d=e.cellIndex.split(",")[0],nt=e.cellIndex.split(",")[1];if(u=="rowheader")for(v="rowheader",t.olap.base._isRowDrilled=r.action!=i&&r.action=="collapse"?!1:!0,o=parseInt(d);o>=0;o--)!f._pivotRecords.records[parseInt(o*f._pivotRecords.rowCount+parseInt(d))].Info.indexOf(y)>=0&&(h=f._pivotRecords.records[parseInt(o*f._pivotRecords.rowCount+parseInt(nt))].Info,p=this._splitCellInfo(h),p&&(b=p.uniqueName+b),h==""||h.indexOf(y)>=0||(s=this._splitCellInfo(h),s.uniqueName=s.uniqueName.replace(/&/g,"&amp;"),a=s.uniqueName+">#>"+a,currReport=n.map(w,function(t,r){return t.fieldName!=i&&n.trim(t.fieldName).toLowerCase()==n.trim(s.hierarchyUniqueName).toLowerCase()?r:t.hierarchyUniqueName!=i&&n.trim(t.hierarchyUniqueName).toLowerCase()==n.trim(s.hierarchyUniqueName).toLowerCase()?r:void 0}),r.action!="collapse"&&(f.model.dataSource.rows[currReport[0]].drillCellInfo=s),y=s.hierarchyUniqueName));else for(v="colheader",t.olap.base._isColDrilled=r.action!=i&&r.action=="collapse"?!1:!0,o=parseInt(nt);o>=0;o--)!f._pivotRecords.records[parseInt(d*f._pivotRecords.rowCount+parseInt(o))].Info.indexOf(y)>=0&&(h=f._pivotRecords.records[parseInt(d*f._pivotRecords.rowCount+parseInt(o))].Info,p=this._splitCellInfo(h),p&&(b=p.uniqueName+(f._isMondrian?"":">#>")+b),h==""||h.indexOf(y)>=0||(s=this._splitCellInfo(h),s.uniqueName=s.uniqueName.replace(/&/g,"&amp;"),a=s.uniqueName+(f._isMondrian?">#>":"")+a,currReport=n.map(w,function(t,r){return t.fieldName!=i&&n.trim(t.fieldName).toLowerCase()==n.trim(s.hierarchyUniqueName).toLowerCase()?r:t.expression!=i&&n.trim(t.hierarchyUniqueName).toLowerCase()==n.trim(s.hierarchyUniqueName).toLowerCase()?r:void 0}),r.action!="collapse"&&(f.model.dataSource.columns[currReport[0]].drillCellInfo=s),y=s.hierarchyUniqueName));a+=e.uniqueName;e.previousElements=b.replace(/&amp;/g,"&");e.preRepItm=a.replace(/&amp;/g,"&");e.axis=v}if(e.previousElements=e.previousElements.replace(/&amp;/g,"&"),e.preRepItm=e.preRepItm.replace(/&amp;/g,"&"),r.action)if(r.action){if(f._isMondrian){e.action="collapse";t.olap._mdxParser._updateReport(l.drilledItems,e,l,f);return}if(t.isNullOrUndefined(e)||t.isNullOrUndefined(e.previousElements)||e.previousElements==""||(t.olap.base.olapCtrlObj._prevDrillElements=e.previousElements.split("][").join("]>#>[").split(">#>")),c=t.olap.base._getDrilledMemeber({item:e}),c.length&&!t.olap.base._isPaging)t.olap.base._onDemandCollapse({drilledMembers:c,action:r.action},v);else{while(c.length!=0)t.olap.base.olapCtrlObj.model.dataSource=t.olap._mdxParser._clearCollapsedItems(v,c[c.length-1],t.olap.base.olapCtrlObj.model.dataSource),c=c.splice(0,c.length-1);t.olap.base.getJSONData(r,t.olap.base.olapCtrlObj.model.dataSource,t.olap.base.olapCtrlObj)}}else tt=n(r.drilledMember).clone(!0),t.olap.base._onDemandExpand({action:"drilldown",cellInfo:e,isExist:!0},r.drilledMember);else c=t.olap.base._getDrilledMemeber({item:e}),t.olap._mdxParser._updateReport(l.drilledItems,e,l,f)}},_clearCollapsedItems:function(t,r,u){return t=="rowheader"?u.rows=n.map(u.rows,function(t){return t.drilledItems!=i&&t.drilledItems.length&&(t.drilledItems=n.map(t.drilledItems,function(n){if(n.join("").indexOf(r.repItms.replace(/&/g,"&amp;"))<0)return[n]})),t}):t=="colheader"&&(u.columns=n.map(u.columns,function(t){return t.drilledItems!=i&&t.drilledItems.length&&(t.drilledItems=n.map(t.drilledItems,function(n){if(n.join("").indexOf(r.repItms.replace(/&/g,"&amp;"))<0)return[n]})),t})),u},_getSlicerMDX:function(t,r){for(var h,e=n(t)[0].filters,u="",o=this,s=r._fieldData,c=n.merge(n.merge([],t.columns),t.rows),f=0;f<e.length;f++)h=n.grep(c,function(n){var t=o._getDimensionUniqueName(n.fieldName,s),i=o._getDimensionUniqueName(e[f].fieldName,s);return t==i&&!(r._isMondrian&&i==""&&t=="")}).length>0,h||(e[f].fieldName!=i&&e[f].filterItems==i?u=u+(u!=""?"*":"")+"{"+this._getDimensionQuery(e[f])+"}":e[f].filterItems!=i&&(u=u+(u!=""?"*":"")+"{"+e[f].filterItems.values.toString()+"}"));return u!=""?"where ("+u.replace(/DrillDownlevel/g,"")+")":""},_getDimensionUniqueName:function(t,i){var u=i.hierarchy,r;return u?(r=n.map(u,function(n){if(n.id.toLowerCase()==t.toLowerCase())return n.pid}),r.length>0?r[0]:""):t.split(".")[0]},_getMeasuresQuery:function(t){var u=n(t)[0].values,f=[],e="",s="",r,o;return u.length>0&&(r=jQuery.map(u,function(n,t){if(n.measures!=i)return{measureElements:n.measures,Index:t,axisName:n.axis}}),o=r.length>0?n.map(r[0].measureElements,function(n){return n.fieldName}):null,o!=null?f.push({values:o,Index:r[0].Index}):f,s=u.axis),e=n.map(f,function(n){return n.values.toString()}),e[0]!=""?"{"+e+"}":""},_getIncludefilterQuery:function(t,r,u){var c="FROM ["+r+"]",e="FROM ( SELECT (",o,s,a=u._fieldData,h=[],y="COLUMNS",f;if(u._isMondrian)return c;for(o=n.map(n(t.rows),function(n){if(n.filterItems!=i)return[n.filterItems.values]}),s=n.map(n(t.columns),function(n){if(n.filterItems!=i)return[n.filterItems.values]}),f=0;f<t.filters.length;f++){var l=t.filters,v=this,p=!1;n.map(t.columns,function(n){v._getDimensionUniqueName(n.fieldName,a)==v._getDimensionUniqueName(l[f].fieldName,a)&&l[f].filterItems!=i&&(s.push(l[f].filterItems.values),p=!0)});p||n.map(t.rows,function(n){v._getDimensionUniqueName(n.fieldName,a)==v._getDimensionUniqueName(l[f].fieldName,a)&&l[f].filterItems!=i&&o.push(l[f].filterItems.values)})}if(t.enableAdvancedFilter){for(f=0;f<=t.columns.length-1;f++)t.columns[f].advancedFilter&&n.merge(h,this._getAdvancedFilterQuery(t.columns[f],e,y));for(f=0;f<=t.rows.length-1;f++)t.rows[f].advancedFilter&&n.merge(h,this._getAdvancedFilterQuery(t.rows[f],e,y))}for(f=0;f<=s.length-1;f++)e=f==0?e+"{"+s[f].toString()+"}":e+",{"+s[f].toString()+"}";for(s.length>0&&(e=o.length>0?e+" ) on COLUMNS ,(":e+" ) on COLUMNS "),f=0;f<=o.length-1;f++)e=f>0?e+",{"+o[f].toString()+"}":e+"{"+o[f].toString()+"}";return e=s.length>0&&o.length>0?e=e+") on ROWS ":s.length==0&&o.length>0?e+") on COLUMNS ":e,h.length>0&&(h=(s.length>0||o.length>0?e:"")+" "+h.join(" ")+" "+c+Array(h.length+1+s.length+o.length).join(")")),c=s.length==0&&o.length==0?c:e+c+")",h.length>0?h:c},_getAdvancedFilterQuery:function(i,r,u){var f=[],e=this;return n.map(i.advancedFilter,function(r){t.isNullOrUndefined(r.labelFilterOperator)&&r.labelFilterOperator==t.olap.LabelFilterOptions.None&&t.isNullOrUndefined(r.valueFilterOperator)&&r.valueFilterOperator==t.olap.ValueFilterOptions.None||f.push("FROM (SELECT Filter("+n.trim(r.name)+".AllMembers, "+e._getAdvancedFilterCondtions(i.fieldName,r.advancedFilterType==t.olap.AdvancedFilterType.LabelFilter||t.isNullOrUndefined(r.advancedFilterType)?r.labelFilterOperator:r.valueFilterOperator,r.values,r.advancedFilterType,r.measure)+")) on "+u)}),f},_getAdvancedFilterCondtions:function(n,i,r,u,f){var e="",u=t.isNullOrUndefined(u)?"label":u;switch(i.toLowerCase()){case"equals":e="("+(u!="value"?n+'.CurrentMember.member_caption ="'+r[0]+'"':f+" = "+r[0]);break;case"notequals":e="("+(u!="value"?n+'.CurrentMember.member_caption &lt;&gt;"'+r[0]+'"':f+" &lt;&gt;"+r[0]);break;case"contains":e="( InStr (1,"+n+'.CurrentMember.member_caption,"'+r[0]+'")&gt;0';break;case"notcontains":e="( InStr (1,"+n+'.CurrentMember.member_caption,"'+r[0]+'")=0';break;case"beginswith":e="( Left ("+n+".CurrentMember.member_caption,"+r[0].length+')="'+r[0]+'"';break;case"notbeginswith":e="( Left ("+n+".CurrentMember.member_caption,"+r[0].length+')&lt;&gt;"'+r[0]+'"';break;case"endswith":e="( Right ("+n+".CurrentMember.member_caption,"+r[0].length+')="'+r[0]+'"';break;case"notendswith":e="( Right ("+n+".CurrentMember.member_caption,"+r[0].length+')&lt;&gt;"'+r[0]+'"';break;case"greaterthan":e="("+(u!="value"?n+'.CurrentMember.member_caption &gt;"'+r[0]+'"':f+" &gt;"+r[0]+"");break;case"greaterthanorequalto":e="("+(u!="value"?n+'.CurrentMember.member_caption &gt;="'+r[0]+'"':f+" &gt;="+r[0]+"");break;case"lessthan":e="("+(u!="value"?n+'.CurrentMember.member_caption &lt;"'+r[0]+'"':f+" &lt;"+r[0]+"");break;case"lessthanorequalto":e="("+(u!="value"?n+'.CurrentMember.member_caption &lt;="'+r[0]+'"':f+" &lt;="+r[0]+"");break;case"between":e="("+(u!="value"?n+'.CurrentMember.member_caption &gt;="'+r[0]+'"AND '+n+'.CurrentMember.member_caption &lt;="'+r[1]+'"':f+" &gt;="+r[0]+" AND "+f+"&lt;="+r[1]);break;case"notbetween":e="("+(u!="value"?n+'.CurrentMember.member_caption &gt;="'+r[0]+'"OR '+n+'.CurrentMember.member_caption &lt;="'+r[1]+'"':f+" &gt;="+r[0]+" OR "+f+"&lt;="+r[1]);break;default:e="( InStr (1,"+n+'.CurrentMember.member_caption,"'+r[0]+'")&gt;0'}return e},_getItemPosition:function(t,r){return n.map(t,function(t,u){if(t.fieldName!=i&&n.trim(t.fieldName)==n.trim(r))return u})},getAllMember:function(i,r,u){if(!t.isNullOrUndefined(u)){var f="select {"+r+"} dimension properties CHILDREN_CARDINALITY on 0 from ["+n.trim(i.cube)+"]",e=t.olap._mdxParser.getSoapMsg(f,i.data,i.catalog),o=t.olap.base._getConnectionInfo(i.data);u.doAjaxPost("POST",o.url,{XMLA:e},u._generateAllMember,null,{action:"loadFieldElements"})}},getMembers:function(i,r,u){var f=t.Pivot.getReportItemByFieldName(r,i).item;t.olap.base.olapCtrlObj._isMondrian&&(t.isNullOrUndefined(u.model.pivotControl)||(this._controlObj=u.model.pivotControl));var e="select {"+(f&&f.hasAllMember?r+".levels(0).AllMembers":r+".children")+"}dimension properties CHILDREN_CARDINALITY, MEMBER_TYPE on 0 from ["+n.trim(i.cube)+"]",o=t.olap._mdxParser.getSoapMsg(e,i.data,i.catalog),s=t.olap.base._getConnectionInfo(i.data);u.doAjaxPost("POST",s.url,{XMLA:o},u._generateMembers,null,{action:"fetchMembers"})},getChildren:function(n,i,r){var e="select {"+i+".children} dimension properties CHILDREN_CARDINALITY on 0 from ["+n.cube+"]",o=r.model.pivotControl,u,f;!t.isNullOrUndefined(o)&&t.olap.base.olapCtrlObj._isMondrian&&(this._controlObj=r.model.pivotControl);u=t.olap._mdxParser.getSoapMsg(e,n.data,n.catalog);f=t.olap.base._getConnectionInfo(n.data);r.doAjaxPost("POST",f.url,{XMLA:u},t.Pivot.generateChildMembers,null,{action:"nodeExpand",currentNode:i})},getSoapMsg:function(n,i,r){var e=t.olap.base._getConnectionInfo(i),f="",u;return t.isNullOrUndefined(this._controlObj)&&(t.isNullOrUndefined(t.olap.base.olapCtrlObj)||!t.olap.base.olapCtrlObj._isMondrian)?u='<Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/"> <Header><\/Header> <Body> <Execute xmlns="urn:schemas-microsoft-com:xml-analysis"> <Command> <Statement> '+n+" <\/Statement> <\/Command> <Properties> <PropertyList> <Catalog>"+r+"<\/Catalog> <LocaleIdentifier>"+e.LCID+"<\/LocaleIdentifier><\/PropertyList> <\/Properties> <\/Execute> <\/Body> <\/Envelope>":(f=t.isNullOrUndefined(this._controlObj)?t.olap.base.olapCtrlObj.model.dataSource.sourceInfo:this._controlObj.model.dataSource.sourceInfo,u='<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"><SOAP-ENV:Body><Execute xmlns="urn:schemas-microsoft-com:xml-analysis"><Command><Statement><![CDATA['+n+"]\]><\/Statement><\/Command><Properties><PropertyList><DataSourceInfo>"+f+"<\/DataSourceInfo><Catalog>"+r+"<\/Catalog><AxisFormat>TupleFormat<\/AxisFormat><Content>Data<\/Content><Format>Multidimensional<\/Format><\/PropertyList><\/Properties><\/Execute><\/SOAP-ENV:Body><\/SOAP-ENV:Envelope>",delete this._controlObj),u}};t.olap.SortOrder={None:"none",Ascending:"ascending",Descending:"descending"};t.olap.Providers={SSAS:"ssas",Mondrian:"mondrian"};t.olap.AdvancedFilterType={LabelFilter:"label",ValueFilter:"value"};t.olap.ValueFilterOptions={None:"none",Equals:"equals",NotEquals:"notequals",GreaterThan:"greaterthan",GreaterThanOrEqualTo:"greaterthanorequalto",LessThan:"lessthan",LessThanOrEqualTo:"lessthanorequalto",Between:"between",NotBetween:"notbetween"};t.olap.LabelFilterOptions={None:"none",BeginsWith:"beginswith",NotBeginsWith:"notbeginswith",EndsWith:"endswith",NotEndsWith:"notendswith",Contains:"contains",NotContains:"notcontains",Equals:"equals",NotEquals:"notequals",GreaterThan:"greaterthan",GreaterThanOrEqualTo:"greaterthanorequalto",LessThan:"lessthan",LessThanOrEqualTo:"lessthanorequalto",Between:"between",NotBetween:"notbetween"};t.olap.AxisName={Row:"rows",Column:"columns"}})(jQuery,Syncfusion)});
