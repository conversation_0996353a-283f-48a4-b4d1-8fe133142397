/*!
*  filename: ej.checkbox.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function(n,t,i){t.widget("ejCheckBox","ej.CheckBox",{_rootCSS:"e-checkbox",element:null,model:null,validTags:["input"],_addToPersist:["checked","checkState"],_setFirst:!1,angular:{require:["?ngModel","^?form","^?ngModelOptions"]},defaults:{id:null,name:null,value:null,htmlAttributes:{},checked:!1,enabled:!0,enableTriState:!1,showRoundedCorner:!1,enablePersistence:!1,cssClass:"",text:"",enableRTL:!1,idPrefix:"ej",size:"small",checkState:"uncheck",validationRules:null,validationMessage:null,validationMessages:null,beforeChange:null,change:null,create:null,destroy:null},dataTypes:{id:"string",name:"string",enablePersistence:"boolean",enableTriState:"boolean",size:"enum",enabled:"boolean",idPrefix:"string",validationRules:"data",validationMessage:"data",validationMessages:"data",htmlAttributes:"data"},observables:["checked","checkState"],checked:t.util.valueFunction("checked"),checkState:t.util.valueFunction("checkState"),_init:function(n){this._cloneElement=this.element.clone();var i=t.browserInfo();this._isIE8=i.name=="msie"&&i.version=="8.0"?!0:!1;this._isIE9=i.name=="msie"&&i.version=="9.0"?!0:!1;this._isDevice=this._checkDevice();this._setValue();this._renderControl();this.model.enableRTL&&this._setRTL();this.model.enabled&&this._wireEvents();this._setEnabled(this.model.enabled);t.isNullOrUndefined(n)||t.isNullOrUndefined(n.validationMessage)||(this.model.validationMessages=this.model.validationMessage);this.model.validationRules!=null&&(this._initValidator(),this._setValidation());this._addAttr(this.model.htmlAttributes);(this._isIE9||this._isIE8)&&this.wrapper.addClass("e-tb-cell")},_checkDevice:function(){return t.isDevice()&&t.isTouchDevice()},_setRTL:function(){n(this.maindiv).addClass("e-rtl")},_initValidator:function(){this.wrapper.closest("form").data("validator")||this.wrapper.closest("form").validate()},_setValidation:function(){var r,f,i,u,e;this.wrapper.find("input").rules("add",this.model.validationRules);r=this.wrapper.closest("form").data("validator");r=r?r:this.wrapper.closest("form").validate();f=this.wrapper.find("input").attr("name");r.settings.messages[f]={};for(i in this.model.validationRules)if(u=null,!t.isNullOrUndefined(this.model.validationRules[i])){if(t.isNullOrUndefined(this.model.validationRules.messages&&this.model.validationRules.messages[i])){r.settings.messages[f][i]=n.validator.messages[i];for(e in this.model.validationMessages)i==e?u=this.model.validationMessages[i]:""}else u=this.model.validationRules.messages[i];r.settings.messages[f][i]=u!=null?u:n.validator.messages[i]}},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.wrapper.addClass(n):t=="name"?i.element.attr(t,n):t=="required"?i.element.attr(t,n):t=="disabled"&&n=="disabled"?i._setEnabled(!1):t=="checked"&&n=="checked"?i.checked()instanceof Array?i._updateCheckedItem():i._checked(!0):i.wrapper.attr(t,n)})},_triggerBeforeChange:function(){var n={isChecked:this._isChecked,isInteraction:!1};if(!0==this._trigger("beforeChange",n))return!1},_triggerChange:function(){var n={isChecked:this._isChecked,checkState:this.checkState(),isInteraction:!1};this._trigger("change",n)},_setModel:function(i){var r,u,f;for(r in i)switch(r){case"checked":n(this.maindiv).removeClass("e-material-animate");this.model.beforeChange&&this._triggerBeforeChange();this.checked()instanceof Array?(u=t.util.getVal(i[r]),f=u[u.length-1],this.model.value==f&&(this._isChecked=!0)):(this._isChecked=t.util.getVal(i[r]),this.checked(this._isChecked));this._isChecked?this._checked():this._unChecked();this.model.change&&this._triggerChange();break;case"enableTriState":i[r]&&(this.model.enableTriState=i[r],this._indeterminateState=i[r]);break;case"checkState":this.model.enableTriState&&(this.model.beforeChange&&this._triggerBeforeChange(),this._isChecked=t.util.getVal(i[r]),this.checkState(this._isChecked),this._changeState(this._isChecked),this._setCheckBoxState(),this.checked(this._isChecked),this.model.checkState=="indeterminate"&&this._setIndeterminate(this._indeterminateState),this.checked()instanceof Array?this._updateCheckedItem():i[r]=="check"?this._hiddenInput.removeAttribute("name"):this._hiddenInput.setAttribute("name",this.model.name),this.model.change&&(this.checked()instanceof Array||(this._isChecked=this.checkState()=="uncheck"?!1:!0),this._triggerChange()));break;case"cssClass":this._changeSkin(i[r]);break;case"enableRTL":i[r]?this._setRTL():n(this.maindiv).removeClass("e-rtl");break;case"text":this._setText(i[r]);break;case"validationRules":this.model.validationRules!=null&&(this.wrapper.find("input").rules("remove"),this.model.validationMessages=null);this.model.validationRules=i[r];this.model.validationRules!=null&&(this._initValidator(),this._setValidation());break;case"validationMessage":this.model.validationMessages=i[r];this.model.validationRules!=null&&this.model.validationMessages!=null&&(this._initValidator(),this._setValidation());break;case"validationMessages":this.model.validationMessages=i[r];this.model.validationRules!=null&&this.model.validationMessages!=null&&(this._initValidator(),this._setValidation());break;case"id":this._setIdAttr(i[r]);break;case"name":this.element.attr("name",i[r]);(!this._isChecked||this.spanImg.hasClass("e-chk-indeter"))&&this._hiddenInput.setAttribute("name",i[r]);this.model.name=i[r];break;case"value":this.element.attr("value",i[r]);break;case"size":this._setSize(i[r]);break;case"showRoundedCorner":this._setRoundedCorner(i[r]);break;case"enabled":this._setEnabled(i[r]);break;case"htmlAttributes":this._addAttr(i[r])}},_destroy:function(){this.element.removeClass("e-checkbox e-input");!this._cloneElement.attr("name")&&this.element.attr("name")&&this.element.removeAttr("name");!this._cloneElement.attr("value")&&this.element.attr("value")&&this.element.removeAttr("value");this.element.insertBefore(this.wrapper);this.wrapper.remove()},_changeSkin:function(t){this.model.cssClass!=t&&(this.wrapper.removeClass(this.model.cssClass).addClass(t),n("#"+this.model.idPrefix+this.model.id+"_wrapper").removeClass(this.model.cssClass).addClass(t))},_setValue:function(){this._indeterminateState=!1;this._isChecked=!1;var i=this.element[0].getAttribute("id"),r=this.element[0].getAttribute("name"),n=this.element[0].getAttribute("value");t.isNullOrUndefined(i)||(this.model.id=i);t.isNullOrUndefined(r)||(this.model.name=r);t.isNullOrUndefined(n)||n==""||(this.model.value=n);this.checked()||t.isNullOrUndefined(this.element.attr("checked"))||(this._isChecked=!0);t.isNullOrUndefined(this.model.name)&&(this.model.name=this.model.id);this.model.enabled=this.model.enabled&&!this.element.attr("disabled")},_setSize:function(i){i==t.CheckboxSize.Medium?(n(this.innerdiv).removeClass("e-chkbox-small").addClass("e-chkbox-medium"),n(this.maindiv).removeClass("e-check-small").addClass("e-check-medium")):(n(this.innerdiv).removeClass("e-chkbox-medium").addClass("e-chkbox-small"),n(this.maindiv).removeClass("e-check-medium").addClass("e-check-small"))},_setRoundedCorner:function(n){n?this.span.addClass("e-corner"):this.span.removeClass("e-corner")},_setEnabled:function(n){n?this.enable():this.disable()},_setCheckBoxState:function(){this.model.enableTriState?this.checkState()=="indeterminate"?this._indeterminateState=!0:this.checkState()=="check"?this._isChecked=!0:this.checkState()=="uncheck"&&(this._isChecked=!1):this.checkState()=="indeterminate"&&this.checkState("uncheck")},_createElement:function(n,t){var i=document.createElement(n);return this._setAttributes(i,t),i},_setAttributes:function(n,t){for(var i in t)n.setAttribute(i,t[i])},_renderControl:function(){this._setCheckBoxState();this.maindiv=this._createElement("span",{"class":"e-chkbox-wrap e-widget "+this.model.cssClass,role:"checkbox",tabindex:0});this._isValid(this.model.id)&&(this.maindiv.setAttribute("id",this.model.idPrefix+this.model.id),this.element[0].setAttribute("id",this.model.id));this.innerdiv=document.createElement("div");this._setSize(this.model.size);this.span=document.createElement("span");this.span=n(this.span);this.spanImg=this._createElement("span",{"class":"e-chk-image e-icon",role:"presentation"});this.spanImg=n(this.spanImg);this.element.addClass("e-input");this.model.name=t.isNullOrUndefined(this.model.name)?this.model.id:this.model.name;this.model.value=t.isNullOrUndefined(this.model.value)?!0:this.model.value;this._setAttributes(this.element[0],{name:this.model.name,value:this.model.value});var i=n("#"+this._id+"_hidden");this._hiddenInput=i.length?i[0]:this._createElement("input",{type:"checkbox",value:!1,style:"display:none"});this.element.attr("data-type")=="hidden"&&this._hiddenInput.setAttribute("type","hidden");this._isValid(this.model.name)&&this._hiddenInput.setAttribute("id",this.model.name+"_hidden");this._setRoundedCorner(this.model.showRoundedCorner);this.checked()&&this._setCheckedItem(this.checked());this._isChecked?(this.spanImg.addClass("e-checkmark"),this.span.addClass("e-chk-act"),this.maindiv.setAttribute("aria-checked",!0),this.element.attr("checked","checked")):(this.span.addClass("e-chk-inact"),this.maindiv.setAttribute("aria-checked",!1),this._hiddenInput.setAttribute("name",this.model.name));this.checked()instanceof Array||this.checked(this._isChecked);this.span[0].appendChild(this.spanImg[0]);this.innerdiv.appendChild(this.span[0]);this.element[0].parentNode&&this.element[0].parentNode.insertBefore(this.maindiv,this.element[0]);this.maindiv.appendChild(this.element[0]);this.maindiv.appendChild(this._hiddenInput);this.maindiv.appendChild(this.innerdiv);this.wrapper=n(this.maindiv);this._setTextWrapper(this.model.text);this.chkbx=this.element;this.model.enableTriState==!0&&this._indeterminateState==!0&&this._setIndeterminate(this._indeterminateState);this.checked()instanceof Array&&this._updateCheckedItem()},_changeState:function(n){n=="indeterminate"?(this.spanImg.removeClass("e-checkmark").addClass("e-stop"),this.span.removeClass("e-chk-act e-chk-inact").addClass("e-chk-indeter"),this.wrapper[0].setAttribute("aria-checked","mixed"),this.wrapper.find("input").prop("enableTriState",!0),this.checked()instanceof Array||this.checked(null)):n=="check"?(this.spanImg.removeClass("e-stop").addClass("e-checkmark"),this.span.removeClass("e-chk-act e-chk-inact e-chk-indeter").addClass("e-chk-act"),this.wrapper[0].setAttribute("aria-checked",!0)):n=="uncheck"&&(this.spanImg.removeClass("e-checkmark e-stop"),this.span.removeClass("e-chk-act e-chk-indeter").addClass("e-chk-inact"),this.wrapper[0].setAttribute("aria-checked",!1))},_setIndeterminate:function(n){n?(this.spanImg.removeClass("e-checkmark").addClass("e-stop"),this.span.removeClass("e-chk-act e-chk-inact").addClass("e-chk-indeter"),this.wrapper[0].setAttribute("aria-checked","mixed"),this.wrapper.find("input").prop("enableTriState",!0),this.checkState("indeterminate"),this.checked()instanceof Array||this.checked(null),this._hiddenInput.setAttribute("name",this.model.name)):(this.span.removeClass("e-chk-indeter"),this.spanImg.removeClass("e-stop"),this.wrapper.find("input").removeAttr("enableTriState"),this.wrapper.find("input").prop("enableTriState",!1),this.checked()?this._checked():this._unChecked())},_setTextWrapper:function(n){n!=""&&(this.txtSpan=t.buildTag("div.e-text",n),this.wrapper.append(this.txtSpan),this.model.enableRTL&&this._setRTL())},_setText:function(n){this.model.text==""&&n!=""?this._setTextWrapper(n):this.txtSpan.html(n)},_setIdAttr:function(t){n("#"+this.model.idPrefix+this.model.id+"_wrapper").attr("id",this.model.idPrefix+t+"_wrapper");this.element[0].setAttribute("id",t)},_isValid:function(n){return!t.isNullOrUndefined(n)&&n!=""?!0:!1},_wireEvents:function(){this._on(this.wrapper,"click",this._checkedHandler);this._isIE8&&this._isValid(this.model.id)&&this._on(n("label[for="+this.model.id+"]"),"click",function(){this.wrapper.click()});this._on(this.wrapper,"focus",this._focusIn);this._on(this.wrapper,"focusout",this._focusOut)},_unWireEvents:function(){this._off(this.wrapper,this._isDevice&&n.isFunction(n.fn.tap)?"tap":"click");this._isIE8&&this._isValid(this.model.id)&&this._off(n("label[for="+this.model.id+"]"),"click");this._off(this.wrapper,"focus");this._off(this.wrapper,"focusout")},_focusIn:function(){n(this.wrapper).addClass("e-focus");n(this.wrapper).on("keydown",n.proxy(this._checkUnCheck,this))},_focusOut:function(){n(this.wrapper).removeClass("e-focus");n(this.wrapper).off("keydown",n.proxy(this._checkUnCheck,this))},_checkUnCheck:function(n){n.keyCode==32&&(n.preventDefault(),this._checkedHandler())},_checkedHandler:function(t){var i={isChecked:this._isChecked,isInteraction:!0,event:t};return!0==this._trigger("beforeChange",i)?!1:(this.span.hasClass("e-chk-inact")?(this._checked(),this.checked()instanceof Array||this.checked(!0),this.model.enableTriState&&(this._indeterminateState=!0,this.checkState("check"))):this.span.hasClass("e-chk-act")?this.model.enableTriState==!0&&this.model.checkState=="check"&&this.model.checked==!0?(this._setIndeterminate(!0),this.checked()instanceof Array||(this.checked(!0),this.checkState("indeterminate"))):(this._unChecked(),this.checked()instanceof Array||(this.checked(!1),this.checkState("uncheck"))):this.span.hasClass("e-chk-indeter")&&(this.checked()instanceof Array?this._isChecked=!1:this.checked(!1),this._setIndeterminate(!1),this._indeterminateState=!1),this.checked()instanceof Array?this._updateCheckedItem():this._isChecked=this.checked(),n(this.maindiv).addClass("e-material-animate"),i={isChecked:this._isChecked,checkState:this.checkState(),isInteraction:!0,event:t},this._trigger("change",i),!0)},_checked:function(){this.span.removeClass("e-chk-inact").addClass("e-chk-act");this.spanImg.removeClass("e-stop").addClass("e-checkmark");this.wrapper[0].setAttribute("aria-checked",!0);this.wrapper.find("input[type=checkbox]").prop("checked",!0);this.checkState("check");this._hiddenInput.removeAttribute("name")},_unChecked:function(){this.span.removeClass("e-chk-act e-chk-indeter").addClass("e-chk-inact");this.wrapper[0].setAttribute("aria-checked",!1);this.spanImg.removeClass("e-checkmark e-stop");this.wrapper.find("input[type=checkbox]").prop("checked",!1);this.checkState("uncheck");this._hiddenInput.setAttribute("name",this.model.name)},_setCheckedItem:function(n){if(typeof n!="boolean"||n instanceof Array){if(n instanceof Array&&!t.isNullOrUndefined(this.model.value)&&this.model.value!="")for(var i=0;i<n.length;i++)n[i]==this.model.value&&(this._isChecked=!0)}else this._isChecked=!0},_updateCheckedItem:function(){var r=this.model.checked.splice===i?this.model.checked():this.model.checked;t.isNullOrUndefined(this.model.value)||this.model.value==""||this.wrapper.find("span:first").hasClass("e-chk-indeter")||(n.inArray(this.model.value,this.checked())<0&&this.wrapper.find("span:first").hasClass("e-chk-act")?(r.push(this.model.value),this._isChecked=!0,this._hiddenInput.removeAttribute("name")):n.inArray(this.model.value,this.checked())>-1&&this.wrapper.find("span:first").hasClass("e-chk-inact")&&(this._isChecked=!1,r.splice(n.inArray(this.model.value,this.model.checked()),1),this._hiddenInput.setAttribute("name",this.model.name)))},disable:function(){this.wrapper.hasClass("e-disable")||(this.wrapper.addClass("e-disable"),this.wrapper[0].setAttribute("aria-disabled",!0),this.element[0].setAttribute("disabled","disabled"),this._isIE8&&this.span.addClass("e-disable"),this._unWireEvents(),this.model.enabled=!1)},enable:function(){this.wrapper.hasClass("e-disable")&&(this.wrapper.removeClass("e-disable"),this.wrapper[0].setAttribute("aria-disabled",!1),this.element.prop("disabled",!1),this._isIE8&&this.span.removeClass("e-disable"),this._wireEvents(),this.model.enabled=!0)},isChecked:function(){if(this._isChecked!=null&&this._isChecked!=i)return this._isChecked}});t.CheckboxSize={Small:"small",Medium:"medium"};t.CheckState={Check:"check",Uncheck:"uncheck",Indeterminate:"indeterminate"}})(jQuery,Syncfusion)});
