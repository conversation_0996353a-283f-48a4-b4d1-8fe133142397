/*!
*  filename: ej.mobile.scrollpanel.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){function n(n,t){this._wrapper=typeof t.el=="string"?document.querySelector(t.el):t.el;this._sb=this._wrapper.children[0];this._sbStyle=this._sb.style;this.scroller=n;this.model={listenX:!0,listenY:!0,enableInteraction:!1,enableResize:!0,enableShrink:!1,enableFade:!1,speedRatioX:0,disableTouch:!1,speedRatioY:0,ignoreBoundaries:!1};for(var i in t)this.model[i]=t[i];this._sizeRatioX=1;this._sizeRatioY=1;this._maxPosX=0;this._maxPosY=0;this.model.enableFade&&(this._sbStyle[ej.transform]=this.scroller.model.translateZ,this._sbStyle[ej.transitionDuration]=ej.isLowerAndroid()?"0.001s":"0ms",this._sbStyle.opacity="0");this._createDelegates();this._wireEvents()}n.prototype={_createDelegates:function(){this._touchStartDelegate=$.proxy(this._touchStart,this);this._touchMoveDelegate=$.proxy(this._touchMove,this);this._touchEndDelegate=$.proxy(this._touchEnd,this)},_wireEvents:function(n){target=this.scroller.model.bindToWrapper?this.scroller._wrapper:window;$.support.hasTouch&&!this.model.disableTouch&&ej.listenEvents([this.scroller._wrapper,target,target],[ej.startEvent(),ej.cancelEvent(),ej.endEvent()],[this._scrollStartDelegate,this.scrollEndDelegate,this.scrollEndDelegate],n);this.model.enableInteraction&&(this.model.disableTouch||ej.listenEvents([this._sb,window,window],[ej.startEvent(),ej.cancelEvent(),ej.endEvent()],[this._touchStartDelegate,this._touchEndDelegate,this._touchEndDelegate],n),this.model.disablePointer||ej.listenEvents([this._sb,window],[ej.startEvent(),ej.endEvent()],[this._touchStartDelegate,this._touchEndDelegate],n),this.model.disableMouse||ej.listenEvents([this._sb,window],[ej.startEvent(),ej.endEvent()],[this._touchStartDelegate,this._touchEndDelegate],n))},_touchStart:function(n){var t=n.touches?n.touches[0]:n;ej.blockDefaultActions(n);this.transitionTime();this._startedScroll=!0;this._moved=!1;this._lastPointX=t.pageX;this._lastPointY=t.pageY;this._startTime=ej.getTime();this.model.disableTouch&&this.model.disableMouse||ej.listenTouchEvent(window,ej.moveEvent(),this._touchMoveDelegate);this.model.disablePointer||ej.listenTouchEvent(window,ej.moveEvent(),this._touchMoveDelegate)},_touchMove:function(n){ej.blockDefaultActions(n);var t=n.touches?n.touches[0]:n,i,r,u,f,e=ej.getTime();this._moved||this.scroller._triggerEvent("scrollStart",this.scroller._dataValue());this._moved=!0;i=t.pageX-this._lastPointX;this._lastPointX=t.pageX;r=t.pageY-this._lastPointY;this._lastPointY=t.pageY;u=this._x+i;f=this._y+r;this._pos(u,f)},_touchEnd:function(n){if(this._startedScroll){if(this._startedScroll=!1,ej.blockDefaultActions(n),ej.listenEvents([window,window,window],[ej.moveEvent(),ej.moveEvent(),ej.moveEvent()],[this._touchMoveDelegate,this._touchMoveDelegate,this._touchMoveDelegate],!0),this.scroller.model.snap){var t=this.scroller._nearestSnap(this.scroller._x,this.scroller._y),i=this.model.snapSpeed||Math.max(Math.max(Math.min(Math.abs(this.scroller._x-t.x),1e3),Math.min(Math.abs(this.scroller._y-t.y),1e3)),300);(this.scroller._x!=t.x||this.scroller._y!=t.y)&&(this.scroller._directionX=0,this.scroller._directionY=0,this.scroller._currentPage=t,this.scroller.scrollTo(t.x,t.y,i,this.scroller.model.bounceEasing))}this._moved&&this.scroller._triggerEvent("scrollStop",this.scroller._dataValue())}},_pos:function(n,t){n<0?n=0:n>this._maxPosX&&(n=this._maxPosX);t<0?t=0:t>this._maxPosY&&(t=this._maxPosY);n=this.model.listenX?Math.round(n/this._sizeRatioX):this.scroller._x;t=this.model.listenY?Math.round(t/this._sizeRatioY):this.scroller._y;this.scroller.scrollTo(n,t)},enableFade:function(n,t){if((!t||this._visible)&&this.model.enableFade){clearTimeout(this._fadeTimeout);this._fadeTimeout=null;var r=n?250:500,i=this,u=n?0:300;n=n?"1":"0";this._sbStyle[ej.transitionDuration]=r+"ms";this._fadeTimeout=setTimeout(function(n){i._sbStyle.opacity=n;i._visible=+n}.bind(this,n),u)}},destroy:function(){this.model.enableInteraction&&this._wireEvents(!0)},transitionTime:function(n){n=n||0;this._sbStyle[ej.transitionDuration]=n+"ms";!n&&ej.isLowerAndroid()&&(this._sbStyle[ej.transitionDuration]="0.001s")},transitionTimingFunction:function(n){this._sbStyle[ej.transitionTimingFunction]=n},refresh:function(){this.transitionTime(0);this._sbStyle.display=this.model.listenX&&!this.model.listenY?this.scroller._hasHScroll?"block":"none":this.model.listenY&&!this.model.listenX?this.scroller._hasVScroll?"block":"none":this.scroller._hasHScroll||this.scroller._hasVScroll?"block":"none";this.model.listenX&&(this._wrpWd=this._wrapper.clientWidth,this.model.enableResize?(this._sbWidth=Math.max(Math.round(this._wrpWd*this._wrpWd/(this.scroller._scrollerWidth||this._wrpWd||1)),8),this._sbStyle.width=this._sbWidth+"px"):this._sbWidth=this._sb.clientWidth,this._maxPosX=this._wrpWd-this._sbWidth,this.model.enableShrink=="clip"?(this._minBoundaryX=-this._sbWidth+8,this._maxBoundaryX=this._wrpWd-8):(this._minBoundaryX=0,this._maxBoundaryX=this._maxPosX),this._sizeRatioX=this.model.speedRatioX||this.scroller._maxScrollX&&this._maxPosX/this.scroller._maxScrollX);this.model.listenY&&(this._wrpHt=this._wrapper.clientHeight,this.model.enableResize?(this._sbHeight=Math.max(Math.round(this._wrpHt*this._wrpHt/(this.scroller._scrollerHeight||this._wrpHt||1)),8),this._sbStyle.height=this._sbHeight+"px"):this._sbHeight=this._sb.clientHeight,this._maxPosY=this._wrpHt-this._sbHeight,this.model.enableShrink=="clip"?(this._minBoundaryY=-this._sbHeight+8,this._maxBoundaryY=this._wrpHt-8):(this._minBoundaryY=0,this._maxBoundaryY=this._maxPosY),this._sizeRatioY=this.model.speedRatioY||this.scroller._maxScrollY&&this._maxPosY/this.scroller._maxScrollY);this.setPosition()},setPosition:function(){var n=this.model.listenX&&Math.round(this._sizeRatioX*this.scroller._x)||0,i=this,t=this.model.listenY&&Math.round(this._sizeRatioY*this.scroller._y)||0;this.model.ignoreBoundaries||(n<this._minBoundaryX?(this.model.enableShrink=="scale"&&(this._width=Math.max(this._sbWidth+n,8),this._sbStyle.width=this._width+"px"),n=this._minBoundaryX):n>this._maxBoundaryX?this.model.enableShrink=="scale"?(this._width=Math.max(this._sbWidth-(n-this._maxPosX),8),this._sbStyle.width=this._width+"px",n=this._maxPosX+this._sbWidth-this.width):n=this._maxBoundaryX:this.model.enableShrink=="scale"&&this._width!=this._sbWidth&&(this._width=this._sbWidth,this._sbStyle.width=this._width+"px"),t<this._minBoundaryY?(this.model.enableShrink=="scale"&&(this._height=Math.max(this._sbHeight+t*3,8),this._sbStyle.height=this._height+"px"),t=this._minBoundaryY):t>this._maxBoundaryY?this.model.enableShrink=="scale"?(this._height=Math.max(this._sbHeight-(t-this._maxPosY)*3,8),this._sbStyle.height=this._height+"px",t=this._maxPosY+this._sbHeight-this._height):t=this._maxBoundaryY:this.model.enableShrink=="scale"&&this._height!=this._sbHeight&&(this._height=this._sbHeight,this._sbStyle.height=this._height+"px"));$(this._wrapper).hasClass("e-m-ver")?n=0:t=0;this._x=n;this._y=t;this.scroller.model.enableTransform?this._sbStyle[ej.transform]="translate("+n+"px,"+t+"px)"+this.scroller.model.translateZ:(this._sbStyle.left=n+"px",this._sbStyle.top=t+"px")}},function(t,i,r){nextFrame=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(n){return setTimeout(n,1)}}();cancelFrame=function(){return window.cancelRequestAnimationFrame||window.webkitCancelAnimationFrame||window.webkitCancelRequestAnimationFrame||window.mozCancelRequestAnimationFrame||window.oCancelRequestAnimationFrame||window.msCancelRequestAnimationFrame||clearTimeout}();i.widget("ejmScrollPanel","ej.mobile.ScrollPanel",{_setFirst:!0,_rootCSS:"e-m-scroll",angular:{terminal:!1},defaults:{renderMode:"auto",enableResize:!0,targetHeight:null,targetWidth:null,scrollHeight:"auto",scrollWidth:"auto",enableFade:!0,enableShrink:i.isWindows()?!1:!0,allowPullToRefresh:!1,cssClass:null,pullToRefreshSettings:{thresholdDistance:75,textOnPull:"Pull to Refresh...",textOnRelease:"Release to Refresh...",textOnRefresh:"Refreshing..."},isRelative:!1,wheelSpeed:16,enableInteraction:!0,enabled:!0,enableHrScroll:i.isWindows()&&!i.isMobile()?!0:!1,enableVrScroll:!0,eventPassthrough:null,translateZ:i.browserInfo().name=="msie"?"":t.support.has3d?" translateZ(0)":"",zoomMin:1,zoomMax:6,startZoom:1,startX:0,startY:0,enableDisplacement:!1,displacementValue:94,displacementTime:800,deceleration:null,disableTouch:!1,directionLockThreshold:5,scrollEndThresholdDistance:5,momentum:!0,enableBounce:i.isAndroid()?!1:!0,bounceTime:450,bounceEasing:"",preventDefault:!0,preventDefaultException:{tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT)$/},enableTransition:!0,enableTransform:!0,showScrollbars:!0,enableMouseWheel:!0,enableKeys:!0,enableZoom:!1,enableNativeScrolling:i.isAndroid()||i.isIOS()?!1:i.isDevice()?!0:!1,invertWheel:!1,scrollStart:null,scroll:null,scrollStop:null,scrollEnd:null,zoomStart:null,zoomEnd:null,pull:null,target:null},dataTypes:{},_init:function(){if(i.setRenderMode(this),this._createDelegates(),this.model.enableDisplacement&&i.isWindows()&&(this.model.deceleration=6e-5),this._eventType={touchstart:1,touchmove:1,touchend:1,ejtouchmove:3,mousedown:2,mousemove:2,mouseup:2,MSPointerDown:3,MSPointerMove:3,MSPointerUp:3,pointerdown:3,pointermove:3,pointerup:3},this._scrollEvents={scrollStop:[this.model.scrollStop],scroll:[this.model.scroll],scrollStart:[this.model.scrollStart],zoomStart:[this.model.zoomStart],zoomEnd:[this.model.zoomEnd]},this._scale=Math.min(Math.max(this.model.startZoom,this.model.zoomMin),this.model.zoomMax),this._wrapper=this.model.target?typeof this.model.target=="string"?t("#"+this.model.target)[0]:this.model.target:this.element[0],this._$wrapper=t(this._wrapper),this._$wrapper.wrapInner("<div>"),this._wrapper&&(this._scroller=this._wrapper.children[0]),this._scroller){if(this._aniTime=null,t(this._scroller).addClass("e-m-scrollcontent e-m-user-select").css({height:this.model.scrollHeight,width:this.model.scrollWidth}),this.model.target&&this._$wrapper.append(this.element),this._$wrapper.addClass("e-m-scrollpanel "+(this.model.isRelative?"e-m-rel":"e-m-abs")+(this.model.cssClass?" "+this.model.cssClass:"")+" e-m-"+this.model.renderMode),this.element.addClass("e-m-scroll"),this.model.enableNativeScrolling)this.refresh(),this._$wrapper.addClass("e-m-scroll-native e-m-scroll-wrapper"),(this.model.renderMode=="ios7"||this.model.renderMode=="android")&&t(this._scroller).addClass("e-m-scroll-content"),this._scroller[i.transform]="translate(0px,0px)",(this._hasVScroll||this._maxScrollY!=0)&&(this._hasHScroll||this._maxScrollX!=0)||this.model.renderMode!="ios7"||this._$wrapper.addClass("e-m-scroll-box"),this.model.showScrollbars||this._$wrapper.addClass("e-m-scroll-hide"),this.model.enableZoom||this._$wrapper.addClass("e-m-zoom");else{var n=this;this._scrollerStyle=this._scroller.style;this.model.enableTransition=t.support.hasTransition&&this.model.enableTransition;this.model.enableTransform=t.support.hasTransform&&this.model.enableTransform;this.model.eventPassthrough=this.model.eventPassthrough===!0?"vertical":this.model.eventPassthrough;this.model.preventDefault=!this.model.eventPassthrough&&this.model.preventDefault;this.model.enableVrScroll=this.model.eventPassthrough=="vertical"?!1:this.model.enableVrScroll;this.model.enableHrScroll=this.model.eventPassthrough=="horizontal"?!1:this.model.enableHrScroll;this.model.directionLockThreshold=this.model.eventPassthrough?0:this.model.directionLockThreshold;this.model.invertWheel=this.model.invertWheel?-1:1;this._x=this._y=this._directionX=this._directionY=this._startX=this._startY=0;this._createRefresher();this.model.enableZoom&&(this._scrollerStyle[i.transformOrigin]="0 0");this.model.showScrollbars&&this._initScrollbars();this.model.enableMouseWheel&&this._initWheel();this.model.enableKeys&&this._initKeys();this.refresh();this.scrollTo(this.model.startX,this.model.startY)}this._wireEvents()}},_createRefresher:function(){if(this.model.allowPullToRefresh){var n=i.buildTag("div.e-m-scroll-refresher",'<span class="e-m-scroll-refreshcontent"><span class="e-m-left"><span class="e-m-icon e-m-pull"><\/span><\/span><span class="e-m-right e-m-text">'+this.model.pullToRefreshSettings.textOnPull+"<\/span><\/span>");t(this._scroller).prepend(n);this._refresherElement=t(this._scroller).find(".e-m-scroll-refresher");this._refresherIcon=this._refresherElement.find(".e-m-icon");this._refresherText=this._refresherElement.find(".e-m-text")}},_dataValue:function(){return data={x:this.model.enableNativeScrolling?this._nx:this._x,y:this.model.enableNativeScrolling?this._ny:this._y,object:this}},_transitionEnd:function(n){n.target==this._scroller&&this._isInTransition&&(this._transitionTime(),this.resetPosition(this.model.bounceTime)||(this._moved=this._isInTransition=!1,this._triggerEvent("scrollStop",this._dataValue()),this._y-this.model.scrollEndThresholdDistance<=this._maxScrollY&&this._trigger("scrollEnd",t.extend(this._dataValue(),{position:"bottom"})),this._y>=-this.model.scrollEndThresholdDistance&&this._trigger("scrollEnd",t.extend(this._dataValue(),{position:"top"}))))},_touchStart:function(n){if((!(t(n.target).parents(".e-m-scrollpanel").length>1)||t(n.target).closest(".e-m-scrollpanel")[0]==this._wrapper)&&!this._lockRefresh){var r=n.touches?n.touches[0]:n,u;if(this.model.enableNativeScrolling){this._moved=!1;(this.model.renderMode=="ios7"||this.model.renderMode=="android")&&(startY=r.pageY,startTopScroll=this._wrapper.scrollTop,startTopScroll<=0&&(this._wrapper.scrollTop=1),startTopScroll+this._wrapper.offsetHeight>=this._wrapper.scrollHeight&&(this._wrapper.scrollTop=this._wrapper.scrollHeight-this._wrapper.offsetHeight-1));return}(this._eventType[n.type]==1||n.button===0)&&this.model.enabled&&(!this._startedScroll||this._eventType[n.type]===this._startedScroll)&&(i.listenEvents([window,window,window],[i.moveEvent(),i.cancelEvent(),i.endEvent()],[this._touchMoveHandler,this._touchEndHandler,this._touchEndHandler],!1,this,this.model.disableMouse),!this.model.preventDefault||i.isDevice()||i._preventDefaultException(n.target,this.model.preventDefaultException)||n.preventDefault(),this.model.preventDefault&&this._moved&&i.blockDefaultActions(n),this._startedScroll=this._eventType[n.type],this._distX=this._distY=this._directionX=this._directionY=this._lockedDir=0,this._transitionTime(),this._startTime=this._getTime(),this.model.enableTransition&&this._isInTransition?(this._isInTransition=!1,u=this.getComputedPosition(),this._translate(Math.round(u.x),Math.round(u.y)),this._triggerEvent("scrollStop",this._dataValue())):!this.model.enableTransition&&this._animating&&(cancelFrame(this._aniTime),this._steps=[],this._animating=!1,this._translate(this._wrapper.scrollLeft,this._wrapper.scrollTop),this._triggerEvent("scrollStop",this._dataValue())),this._moved=!1,this._startX=this._x,this._startY=this._y,this._absStartX=this._x,this._absStartY=this._y,this._pointX=r.pageX,this._pointY=r.pageY,this.model.enableZoom&&n.touches&&n.touches.length>1&&this._zoomStart(n),(this._oldScrollX!=this._scroller.scrollWidth||this._oldScrollY!=this._scroller.scrollHeight)&&this._calculateXY()._refreshScrollbar())}},_scrollStart:function(){this._moved=!1;this._nx=this._$wrapper.scrollLeft();this._ny=this._$wrapper.scrollTop();this._trigger("scrollStart",this._dataValue())},_touchMove:function(n){if(this.model.enabled&&this._eventType[n.type]===this._startedScroll){if(this.model.preventDefault&&n.preventDefault(),this.model.enableZoom&&n.touches&&n.touches[1]){this._zoom(n);return}var f=n.touches?n.touches[0]:n,t=f.pageX-this._pointX,i=f.pageY-this._pointY,s=this._getTime(),u,r,e,o;if(this._pointX=f.pageX,this._pointY=f.pageY,this._distX+=t,this._distY+=i,e=Math.abs(this._distX),o=Math.abs(this._distY),!(s-this._endTime>300)||!(e<10)||!(o<10)){if(this._lockedDir||(this._lockedDir=e>o+this.model.directionLockThreshold?"horizontal":o>=e+this.model.directionLockThreshold?"vertical":"none"),this._lockedDir=="horizontal"){if(this.model.eventPassthrough=="vertical")n.preventDefault();else if(this.model.eventPassthrough=="horizontal"){this._startedScroll=!1;return}i=0}else if(this._lockedDir=="vertical"){if(this.model.eventPassthrough=="horizontal")n.preventDefault();else if(this.model.eventPassthrough=="vertical"){this._startedScroll=!1;return}t=0}t=this.model.enableHrScroll||this._hasHScroll?t:0;i=this.model.enableVrScroll||this._hasVScroll?i:0;u=this._x+t;r=this._y+i;(u>0||u<this._maxScrollX)&&(u=this.model.enableBounce?this._x+t/3:u>0?0:this._maxScrollX);(r>0||r<this._maxScrollY)&&(r=this.model.enableBounce?this._y+i/3:r>0?0:this._maxScrollY);this._directionX=t>0?-1:t<0?1:0;this._directionY=i>0?-1:i<0?1:0;this.model.allowPullToRefresh&&!this._lockRefresh&&(this._refresherIcon[r>=this.model.pullToRefreshSettings.thresholdDistance?"addClass":"removeClass"]("e-m-rotate"),this._refresherText.text(r>=this.model.pullToRefreshSettings.thresholdDistance?this.model.pullToRefreshSettings.textOnRelease:this.model.pullToRefreshSettings.textOnPull));this._translate(u,r);s-this._startTime>300&&(this._startTime=s,this._startX=this._x,this._startY=this._y);this._moved?this._triggerEvent("scroll",this._dataValue()):this._triggerEvent("scrollStart",this._dataValue());this._moved=this._lockedDir!="none"?!0:!1}}},_scroll:function(){this._moved=!0;this._nx=this._$wrapper.scrollLeft();this._ny=this._$wrapper.scrollTop();this._trigger("scroll",this._dataValue());proxy=this;clearTimeout(this._wheelEndTimeout);this._wheelEndTimeout=setTimeout(function(){proxy._trigger("scrollStop",proxy._dataValue());Math.abs(proxy._maxScrollY)-proxy.model.scrollEndThresholdDistance<=proxy._ny&&proxy._trigger("scrollEnd",t.extend(proxy._dataValue(),{position:"bottom"}));proxy._ny<=proxy.model.scrollEndThresholdDistance&&proxy._trigger("scrollEnd",t.extend(proxy._dataValue(),{position:"top"}))},200)},_touchEnd:function(n){if(i.listenEvents([window,window,window],[i.moveEvent(),i.cancelEvent(),i.endEvent()],[this._touchMoveHandler,this._touchEndHandler,this._touchEndHandler],!0,this,this.model.disableMouse),this.model.enabled&&this._eventType[n.type]===this._startedScroll){if(!this.model.preventDefault||i.isDevice()||i._preventDefaultException(n.target,this.model.preventDefaultException)||n.preventDefault(),this.model.enableNativeScrolling&&this._moved){i.blockDefaultActions(n);this._moved=!1;return}if(this._scaled){this._zoomEnd(n);return}var h=n.changedTouches?n.changedTouches[0]:n,f,e,o=this._getTime()-this._startTime,t=Math.round(this._x),r=Math.round(this._y),c=Math.abs(t-this._startX),l=Math.abs(r-this._startY),u=0,s="";if(this._isInTransition=this._startedScroll=0,this._endTime=this._getTime(),this.model.allowPullToRefresh&&(this._lockRefresh=this._refresherIcon.hasClass("e-m-rotate"),this._lockRefresh&&(this._refresherText.text(this.model.pullToRefreshSettings.textOnRefresh),this._refresherIcon.removeClass("e-m-pull").addClass("e-m-refresh"))),this.resetPosition(this.model.bounceTime)){this._lockRefresh&&this._trigger("pull",{Obj:this.element.data("ejmScrollPanel")});return}if(this.model.momentum&&o<500&&this._moved&&(f=this._hasHScroll?i._nativeMomentum(this._x,this._startX,o,this._maxScrollX,this.model.enableBounce?this._wrpWd:0,this.model.deceleration,i.isWindows()?10:1):{dest:t,duration:0},e=this._hasVScroll?i._nativeMomentum(this._y,this._startY,o,this._maxScrollY,this.model.enableBounce?this._wrpHt:0,this.model.deceleration,i.isWindows()?10:1):{dest:r,duration:0},t=f.dest,r=e.dest,u=Math.max(f.duration,e.duration),(t!=0&&t!=this._maxScrollX||r!=0&&r!=this._maxScrollY)&&(this._isInTransition=1)),this.model.displacementValue&&this.model.enableDisplacement&&(r=this.model.displacementValue*Math.round(r/this.model.displacementValue),t=this.model.displacementValue*Math.round(t/this.model.displacementValue),u<this.model.displacementTime&&(u=this.model.diplacementTime)),r<this._maxScrollY&&Math.abs(this._y)&&(r=this._maxScrollY),t<this._maxScrollX&&(t=this._maxScrollX),this._moved&&!this._isInTransition&&this._trigger("scrollStop",this._dataValue()),t!=this._x||r!=this._y){(t>0||t<this._maxScrollX||r>0||r<this._maxScrollY)&&(s=i.ease.quadratic);this.scrollTo(t,r,u,s);return}this._moved=!1}},_scrollStop:function(){this._nx=this._$wrapper.scrollLeft();this._ny=this._$wrapper.scrollTop();this._trigger("scrollStop",this._dataValue())},_zoomStart:function(n){var t=Math.abs(n.touches[0].pageX-n.touches[1].pageX),i=Math.abs(n.touches[0].pageY-n.touches[1].pageY);this._touchesDistanceStart=Math.sqrt(t*t+i*i);this._startScale=this._scale;this._originX=Math.abs(n.touches[0].pageX+n.touches[1].pageX)/2+this._wrapperOffset.left-this._x;this._originY=Math.abs(n.touches[0].pageY+n.touches[1].pageY)/2+this._wrapperOffset.top-this._y;this.model.allowPullToRefresh&&this._refresherElement.css("display","none");this._triggerEvent("zoomStart",this._dataValue())},_zoom:function(n){if(this.model.enabled&&this._eventType[n.type]===this._startedScroll){this.model.preventDefault&&n.preventDefault();var r=Math.abs(n.touches[0].pageX-n.touches[1].pageX),u=Math.abs(n.touches[0].pageY-n.touches[1].pageY),o=Math.sqrt(r*r+u*u),t=1/this._touchesDistanceStart*o*this._startScale,i,f,e;this._scaled=!0;t<this.model.zoomMin?t=.5*this.model.zoomMin*Math.pow(2,t/this.model.zoomMin):t>this.model.zoomMax&&(t=2*this.model.zoomMax*Math.pow(.5,this.model.zoomMax/t));i=t/this._startScale;f=this._originX-this._originX*i+this._startX;e=this._originY-this._originY*i+this._startY;this._scale=t;this.scrollTo(f,e,0)}},_zoomEnd:function(n){if(this.model.enabled&&this._eventType[n.type]===this._startedScroll){this.model.preventDefault&&n.preventDefault();var t,i,r;this._isInTransition=this._startedScroll=0;this._scale>this.model.zoomMax?this._scale=this.model.zoomMax:this._scale<this.model.zoomMin&&(this._scale=this.model.zoomMin);this.refresh();r=this._scale/this._startScale;t=this._originX-this._originX*r+this._startX;i=this._originY-this._originY*r+this._startY;t>0?t=0:t<this._maxScrollX&&(t=this._maxScrollX);i>0?i=0:i<this._maxScrollY&&(i=this._maxScrollY);(this._x!=t||this._y!=i)&&this.scrollTo(t,i,this.model.bounceTime);this._scaled=!1;this.model.allowPullToRefresh&&this._refresherElement.css("display","");this._triggerEvent("zoomEnd",this._dataValue())}},_resize:function(){if(this._$wrapper.is(":visible")){var n=this;clearTimeout(this._resizeTimeout);this._resizeTimeout=setTimeout(function(){n.refresh()},i.isAndroid()?250:0)}},_scrollable:function(){return t(this._wrapper).map(function(){var n=this,r=!n.nodeName||t.inArray(n.nodeName.toLowerCase(),["iframe","#document","html","body"])!=-1,i;return r?(i=(n.contentWindow||n).document||n.ownerDocument||n,/webkit/i.test(navigator.userAgent)||i.compatMode=="BackCompat"?i.body:i.documentElement):n})},_both:function(n){return t.isFunction(n)||typeof n=="object"?n:{top:n,left:n}},_scrollTo:function(n,i,r){var u=this;return typeof i=="object"&&(r=i,i=0),typeof r=="function"&&(r={onAfter:r}),n=="max"&&(n=9e9),r=t.extend({},{axis:"xy",duration:0,limit:!0},r),i=i||r.duration,r.queue=r.queue&&r.axis.length>1,r.queue&&(i/=2),r.offset=this._both(r.offset),r.over=this._both(r.over),this._scrollable().each(function(){function l(n){s.animate(e,i,r.easing,n&&function(){n.call(this,f,r)})}var c;if(n!=null){var o=this,s=t(o),f=n,h,e={},a=s.is("html,body");switch(typeof f){case"number":case"string":if(/^([+-]=?)?\d+(\.\d+)?(px|%)?$/.test(f)){f=this._both(f);break}if(f=t(f,this),!f.length)return;case"object":(f.is||f.style)&&(h=(f=t(f)).offset())}c=t.isFunction(r.offset)&&r.offset(o,f)||r.offset;t.each(r.axis.split(""),function(n,t){var p=t=="x"?"Left":"Top",v=p.toLowerCase(),i="scroll"+p,w=o[i],b=u._scrollToMax(o,t),y;h?(e[i]=h[v]+(a?0:w-s.offset()[v]),r.margin&&(e[i]-=parseInt(f.css("margin"+p))||0,e[i]-=parseInt(f.css("border"+p+"Width"))||0),e[i]+=c[v]||0,r.over[v]&&(e[i]+=f[t=="x"?"width":"height"]()*r.over[v])):(y=f[v],e[i]=y.slice&&y.slice(-1)=="%"?parseFloat(y)/100*b:y);r.limit&&/^\d+$/.test(e[i])&&(e[i]=e[i]<=0?0:Math.min(e[i],b));!n&&r.queue&&(w!=e[i]&&l(r.onAfterFirst),delete e[i])});l(r.onAfter)}}).end()},_scrollToMax:function(n,i){var r=i=="x"?"Width":"Height",u="scroll"+r;if(!t(n).is("html,body"))return n[u]-t(n)[r.toLowerCase()]();var f="client"+r,e=n.ownerDocument.documentElement,o=n.ownerDocument.body;return Math.max(e[u],o[u])-Math.min(e[f],o[f])},_transitionTime:function(n){if((!i.getAndroidVersion()||!(i.getAndroidVersion()<=4))&&(n=n||0,this._scrollerStyle[i.transitionDuration]=n+"ms",this._scrollbars))for(var t=this._scrollbars.length;t--;)this._scrollbars[t].transitionTime(n)},_transitionTimingFunction:function(n){if(this._scrollerStyle[i.transitionTimingFunction]=n,this._scrollbars)for(var t=this._scrollbars.length;t--;)this._scrollbars[t].transitionTimingFunction(n)},_translate:function(n,r){if(this.model.enableTransform?this._scrollerStyle[i.transform]="translate("+n+"px,"+r+"px) scale("+this._scale+")":(t(this._wrapper).scrollTop(Math.abs(r)),t(this._wrapper).scrollLeft(Math.abs(n))),this._x=n,this._y=this.model.enableTransform?r:-Math.abs(r),this._scrollbars)for(var u=this._scrollbars.length;u--;)this._scrollbars[u].setPosition()},_createDelegates:function(){this._resizeHandler=t.proxy(this._resize,this);this._touchStartHandler=t.proxy(this._touchStart,this);this._touchMoveHandler=t.proxy(this._touchMove,this);this._touchEndHandler=t.proxy(this._touchEnd,this);this._transitionEndHandler=t.proxy(this._transitionEnd,this);this._scrollStartHandler=t.proxy(this._scrollStart,this);this._scrollHandler=t.proxy(this._scroll,this);this._scrollStopHandler=t.proxy(this._scrollStop,this);this._wheelDelegate=t.proxy(this._wheel,this)},_isWinrt:function(){return!this.model.enableTransform&&i.isWindows()&&!i.isMobile()},_wireEvents:function(n){var u=n?"off":"on",r="onorientationchange"in window?"orientationchange":"resize";i.listenTouchEvent(window,r,this._resizeHandler,n);this.model.disableTouch||i.listenTouchEvent(this._scroller,i.startEvent(),this._touchStartHandler,n,this,this.model.disableMouse);this.model.enableNativeScrolling&&t(this._wrapper).bind("scroll",this._scrollHandler);i.listenTouchEvent(this._scroller,i.transitionEndEvent(),this._transitionEndHandler,n)},_initScrollbars:function(){var r=this.model.enableInteraction,t=[],i,u;for(this._scrollbars=[],this.model.showScrollbars&&(this.model.enableVrScroll&&(i={el:this._createDefaultScrollbar("vertical",r,this.model.showScrollbars),enableInteraction:r,enableShrink:this.model.enableShrink?"scale":!1,enableFade:this.model.enableFade,enableResize:this.model.enableResize,listenX:!1},this._wrapper.appendChild(i.el),t.push(i),this.element.append(this._sbw)),this.model.enableHrScroll&&(i={el:this._createDefaultScrollbar("horizontal",r,this.model.showScrollbars),enableInteraction:r,enableShrink:this.model.enableShrink?"scale":!1,enableFade:this.model.enableFade,enableResize:this.model.enableResize,listenY:!1},this._wrapper.appendChild(i.el),t.push(i),this.element.append(this._sbw))),this.model.showScrollbars&&(t=t.concat()),u=t.length;u--;)this._scrollbars[u]=new n(this,t[u]);if(this.model.enableFade){this.on("scrollStop",function(n){if(n.model.showScrollbars)for(var t=n.object._scrollbars.length;t--;)n.object._scrollbars[t].enableFade()});this.on("scrollCancel",function(n){if(n.model.showScrollbars)for(var t=n.object._scrollbars.length;t--;)n.object._scrollbars[t].enableFade()});this.on("scrollStart",function(n){if(n.model.showScrollbars)for(var t=n.object._scrollbars.length;t--;)n.object._scrollbars[t].enableFade(1)})}this.on("refresh",function(n){if(n.model.showScrollbars)for(var t=n.object._scrollbars.length;t--;)n.object._scrollbars[t].refresh()});this.on("destroy",function(n){if(n.model.showScrollbars)for(var t=n.object._scrollbars.length;t--;)n.object._scrollbars[t].destroy();delete this._scrollbars})},_refreshScrollbar:function(){if(this.model.showScrollbars&&this._scrollbars)for(var n=this._scrollbars.length;n--;)this._scrollbars[n].refresh()},_triggerEvent:function(n,t){if(this._scrollEvents[n]){var i=0,r=this._scrollEvents[n].length;if(r)for(;i<r;i++)this._triggerInternalEvent(this._scrollEvents[n][i],n,t,this)}},_triggerInternalEvent:function(n,r,u){if(n){var f=n;return typeof f=="string"&&(f=i.util.getObject(f,window)),r=t.Event(r),r.type=n,args=i.event(r,this.model,u),!(t.isFunction(f)&&f.call(this,args)===!1||r.isDefaultPrevented())}},_initWheel:function(){var n=this;this._bindWheelEvents();this.on("destroy",function(){n._bindWheelEvents(remove)})},_bindWheelEvents:function(n){i.listenEvents([this._wrapper,this._wrapper],["mousewheel","DOMMouseScroll"],[this._wheelDelegate,this._wheelDelegate],n)},_wheel:function(n){if(this.model.enabled){n.preventDefault();var u,f,e,o,i=this;if(this.wheelTimeout===r&&this._triggerEvent("scrollStart",this._dataValue()),clearTimeout(this.wheelTimeout),this.wheelTimeout=setTimeout(function(){i._triggerEvent("scrollStop",i._dataValue())},400),"deltaX"in n&&(u=-n.deltaX,f=-n.deltaY),"wheelDeltaX"in n)u=n.wheelDeltaX/120,f=n.wheelDeltaY/120;else if("wheelDelta"in n)u=f=n.wheelDelta/120;else if("detail"in n)u=f=-n.detail/3;else if(u==r)return;u*=this.model.wheelSpeed;f*=this.model.wheelSpeed;this._hasVScroll||(u=f,f=0);e=this._x+Math.round(this._hasHScroll?u*this.model.invertWheel:0);o=this._y+Math.round(this._hasVScroll?f*this.model.invertWheel:0);e>0?e=0:e<this._maxScrollX&&(e=this._maxScrollX);o>0?o=0:o<this._maxScrollY&&(o=this._maxScrollY);clearTimeout(this._wheelEndTimeout);this._wheelEndTimeout=setTimeout(function(){i._y-i.model.scrollEndThresholdDistance<=i._maxScrollY&&i._trigger("scrollEnd",t.extend(i._dataValue(),{position:"bottom"}));i._y>=-i.model.scrollEndThresholdDistance&&i._trigger("scrollEnd",t.extend(i._dataValue(),{position:"top"}))},400);this.scrollTo(e,o,0)}},_initKeys:function(){var r={pageUp:33,pageDown:34,end:35,home:36,left:37,up:38,right:39,down:40},n;if(typeof this.model.enableKeys=="object")for(n in this.model.enableKeys)typeof this.model.enableKeys[n]=="string"&&(this.mdoel.enableKeys[n]=this.model.enableKeys[n].toUpperCase().charCodeAt(0));else this.model.enableKeys={};for(n in r)this.model.enableKeys[n]=this.model.enableKeys[n]||r[n];this._keyDownDelegate=t.proxy(this._keyPress,this);i.listenTouchEvent(window,"keydown",this._keyDownDelegate)},_startAni:function(){var n=this,i=this._startX,r=this._startY,e=Date.now(),t,u,f,n=this;if(!this._animating){if(!this._steps.length){this.resetPosition(400);return}t=this._steps.shift();t.x==i&&t.y==r&&(t.time=0);this._animating=!0;this._moved=!0;f=function(){var o=Date.now(),s,h;if(o>=e+t.time){n._translate(t.x,t.y);n._animating=!1;n._moved=!1;n.model.scrollStop&&n._triggerEvent("scrollStop",n._dataValue());n._startAni();return}o=(o-e)/t.time-1;u=Math.sqrt(1-o*o);s=(t.x-i)*u+i;h=(t.y-r)*u+r;n._translate(s,h);n._animating&&(n._aniTime=nextFrame(f))};f()}},_keyPress:function(n){if(this.model.enabled){var t=this._x,i=this._y,u=this._getTime(),f=this._keyTime||0,r;this.model.enableTransition&&this._isInTransition&&(r=this.getComputedPosition(),this._translate(Math.round(r.x),Math.round(r.y)),this._isInTransition=!1);this._keyAcc=u-f<200?Math.min(this._keyAcc+.25,50):0;switch(n.keyCode){case this.model.enableKeys.pageUp:this._hasHScroll&&!this._hasVScroll?t+=this._wrpWd:i+=this._wrpHt;break;case this.model.enableKeys.pageDown:this._hasHScroll&&!this._hasVScroll?t-=this._wrpWd:i-=this._wrpHt;break;case this.model.enableKeys.end:t=this._maxScrollX;i=this._maxScrollY;break;case this.model.enableKeys.home:t=i=0;break;case this.model.enableKeys.left:t+=5+this._keyAcc>>0;break;case this.model.enableKeys.up:i+=5+this._keyAcc>>0;break;case this.model.enableKeys.right:t-=5+this._keyAcc>>0;break;case this.model.enableKeys.down:i-=5+this._keyAcc>>0;break;default:return}t>0?t=this.keyAcceleration=0:t<this._maxScrollX&&(t=this._maxScrollX,this._keyAcc=0);i>0?i=this._keyAcc=0:i<this._maxScrollY&&(i=this._maxScrollY,this._keyAcc=0);this.scrollTo(t,i,0);this._keyTime=u}},_setModel:function(n){var i=!1,r,t;for(r in n)switch(r){case"enabled":case"invertWheel":break;case"targetHeight":case"targetWidth":this.refresh();break;case"enableFade":for(t=this._scrollbars.length;t--;)this._scrollbars[t].model.enableFade=!1,this._sbw.children().css("opacity","");break;default:i=!0}i&&this._refresh()},_refresh:function(){this.scrollTo(0,0,0);this._destroy();this._init();this._wireEvents();t(this._scroller).offset({top:this._$wrapper.offset().top})},_clearElement:function(){this._sbw&&this._sbw.remove();this.element.removeAttr("class");var n=typeof this.model.target=="string"?t("#"+this.model.target)[0]:this.model.target==null?this.element[0]:this.model.target;t(n).removeAttr("style");t(this._scroller).children().unwrap();t(n).removeClass("e-m-scrollpanel e-m-scroll-native e-m-scroll-wrapper e-m-scroll-box e-m-scroll-hide e-m-zoom e-m-abs e-m-adjheader-1");this.model.enableTransform&&t(t(n).children()[0]).css(i.transform,"")},_destroy:function(){this._wireEvents(!0);this._keyDownDelegate&&i.listenTouchEvent(window,"keydown",this._keyDownDelegate,!0);this._bindWheelEvents(!0);this._clearElement()},_getTime:function(){return Date.now()},resetPosition:function(n){if(!this.model.enableNativeScrolling&&this.model.enableTransform){var i=this._x,t=this._y;return(n=n||0,!this._hasHScroll||this._x>0?i=0:this._x<this._maxScrollX&&(i=this._maxScrollX),!this._hasVScroll||this._y>0?t=0:this._y<this._maxScrollY&&(t=this._maxScrollY),this._lockRefresh&&t==0&&(t=this._refresherElement.height()),i==this._x&&t==this._y)?!1:(this.scrollTo(i,t,n,this.model.bounceEasing),!0)}},scrollToElement:function(n,t,i,u,f){if(n=n.nodeType?n:this._scroller.querySelector(n),n){var e=n.offsetLeft,o=n.offsetTop;i===!0&&(i=Math.round(n.offsetWidth/2-this._wrapper.offsetWidth/2));u===!0&&(u=Math.round(n.offsetHeight/2-this._wrapper.offsetHeight/2));e=-e;o=-o;e-=i||0;o-=u||0;e=e>0?0:e<this._maxScrollX?this._maxScrollX:e;o=o>0?0:o<this._maxScrollY?this._maxScrollY:o;t=t===r||t===null||t==="auto"?Math.max(Math.abs(this._x-e),Math.abs(this._y-o)):t;this.scrollTo(e,o,t,f)}},on:function(n,t){this._scrollEvents[n]||(this._scrollEvents[n]=[]);this._scrollEvents[n].push(t)},_createDefaultScrollbar:function(n,t,r){return this._sbw=i.buildTag("div.e-m-sbw e-m-"+this.model.renderMode),sb=i.buildTag("div.e-m-sb"),n=="horizontal"&&r?this._sbw.addClass("e-m-hr"):n=="vertical"&&r&&this._sbw.addClass("e-m-ver"),t||(this._sbw[0].style.pointerEvents="none"),this._sbw.append(sb),this._sbw[0]},_calculateXY:function(){return this._wrpWd=this._wrapper.clientWidth,this._wrpHt=this._wrapper.clientHeight,this._scrollerWidth=this._scroller.scrollWidth*this._scale,this._scrollerHeight=this._scroller.scrollHeight*this._scale,this._maxScrollX=this._wrpWd-this._scrollerWidth,this._maxScrollY=this._wrpHt-this._scrollerHeight,this._oldScrollX=this._scroller.scrollWidth,this._oldScrollY=this._scroller.scrollHeight,this._hasHScroll=this.model.enableHrScroll&&this._maxScrollX<0,this._hasVScroll=this.model.enableVrScroll&&this._maxScrollY<0,this._hasHScroll||(this._maxScrollX=0,this._scrollerWidth=this._wrpWd),this._hasVScroll||(this._maxScrollY=0,this._scrollerHeight=this._wrpHt),this},zoom:function(n,t,i,u){if(n<this.model.zoomMin?n=this.model.zoomMin:n>this.model.zoomMax&&(n=this.model.zoomMax),n!=this._scale){var f=n/this._scale;t=t===r?this._wrpWd/2:t;i=i===r?this._wrpHt/2:i;u=u===r?300:u;t=t+this._wrapperOffset.left-this._x;i=i+this._wrapperOffset.top-this._y;t=t-t*f+this._x;i=i-i*f+this._y;this._scale=n;this.refresh();t>0?t=0:t<this._maxScrollX&&(t=this._maxScrollX);i>0?i=0:i<this._maxScrollY&&(i=this._maxScrollY);this.scrollTo(t,i,u)}},disable:function(){this.model.enabled=!1},enable:function(){this.model.enabled=!0},closeRefresher:function(){this._refresherText.text(this.model.pullToRefreshSettings.textOnPull);this._refresherIcon.removeClass("e-m-refresh e-m-rotate").addClass("e-m-pull");this._lockRefresh=this._refresherIcon.hasClass("e-m-rotate");this.scrollTo(0,0)},refresh:function(){if(this._scroller){var n=this._wrapper.offsetHeight;i.adjustFixedElement(this._$wrapper);this.model.targetHeight&&this._$wrapper.height(this.model.targetHeight);this.model.targetWidth&&this._$wrapper.width(this.model.targetWidth);this._calculateXY();this._endTime=this._directionX=this._directionY=0;this._wrapperOffset=t(this._wrapper).offset();this.model.showScrollbars||this._$wrapper.addClass("e-m-hidescrollbar");this.model.enableNativeScrolling&&(this._hasHScroll&&this._hasVScroll?this._$wrapper.css("overflow","auto"):this._hasVScroll?this._$wrapper.css("overflow-y","auto"):this._hasHScroll?this._$wrapper.css("overflow-x","auto"):this.model.renderMode=="windows"&&!i.isMobile()?this._$wrapper.css("overflow","scroll"):this._$wrapper.css("overflow","auto"));this.model.enableNativeScrolling&&i.isWindows()&&(this._hasHScroll&&!this._hasVScroll?this._$wrapper.addClass("e-m-horizontal"):this._hasVScroll&&!this._hasHScroll&&this._$wrapper.addClass("e-m-vertical"));this.resetPosition();this._triggerEvent("refresh",this._dataValue())}},scrollBy:function(n,t,i,r){n=this._x+n;t=this._y+t;i=i||0;this.scrollTo(n,t,i,r)},scrollTo:function(n,r,u,f,e){var h=this,o,s,c;if(this.model.enableNativeScrolling)this._nx=n,this._ny=r,this._scrollTo({top:r,left:n},u,{queue:!0,easing:0,onAfter:function(){h._nx=t(h._wrapper).scrollLeft();h._ny=t(h._wrapper).scrollTop()}});else if((this._oldScrollX!=this._scroller.scrollWidth||this._oldScrollY!=this._scroller.scrollHeight)&&this._calculateXY()._refreshScrollbar(),e=e||i.ease.circular,this.model.enableTransition&&e.style)this._isInTransition=this.model.enableTransition&&u>0,this._transitionTimingFunction(e.style),this._transitionTime(u),this._translate(n,r);else{if(this._x==n&&this._y==r)return;for(o=n,this.stop(),o.length||(o=[{x:n,y:r,time:u,relative:f}]),s=0,c=o.length;s<c;s++)o[s].relative&&(o[s].x=this._x-o[s].x,o[s].y=this._y-o[s].y),this._steps.push({x:o[s].x,y:o[s].y,time:o[s].time||0});this._startAni()}},getComputedPosition:function(){var n=window.getComputedStyle(this._scroller,null),t,r;return this.model.enableTransform?(n=n[i.transform].split(")")[0].split(", "),t=+(n[12]||n[4]),r=+(n[13]||n[5])):(t=+n.left.replace(/[^-\d.]/g,""),r=+n.top.replace(/[^-\d.]/g,"")),{x:t,y:r}},stop:function(){cancelFrame(this._aniTime);this._steps=[];this._moved=!1;this._animating=!1},getScrollPosition:function(){return{x:this.model.enableNativeScrolling?this._nx:this._x,y:this.model.enableNativeScrolling?this._ny:this._y}}})}(jQuery,Syncfusion)});
