﻿<%@ Page Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Users.aspx.cs" Inherits="WebUI.Users" Culture="auto" meta:resourcekey="Page" UICulture="auto" %>

<asp:Content ID="mainHeadContent" ContentPlaceHolderID="mainHead" runat="server">
    <script src="LocalizedResources/ej.culture.en-US.min.js"></script>
    <script type="text/javascript">
        function Initialization() {
            SetLocalization();

            $('#filterTxtBox').keydown(function (e) {
                if (e.keyCode == 13) {
                    e.preventDefault();
                    javascript: __doPostBack('searchBtn', '');
                }
            });
        }
    </script>


</asp:Content>
<asp:Content ID="mainBodyContent" ContentPlaceHolderID="mainBody" runat="server">
    <div class="row">
        <div class="col-xs-12">
            <a id="newUserBtn" href="User.aspx" runat="server" type="button" class="btn btn-primary btn-flat margin-bottom margin-r-5">
                <asp:Literal meta:resourcekey="newUserBtn" runat="server"></asp:Literal></a>
        </div>
    </div>
    <asp:UpdatePanel ID="usersUpdatePanel" runat="server">
        <ContentTemplate>

            <div class="row">
                <div class="col-xs-12 ">
                    <div class="input-group input-group-sm margin-bottom pull-right" style="min-width: 200px; max-width: 250px;">
                        <input type="text" runat="server" id="filterTxtBox" class="form-control" maxlength="30">
                        <span class="input-group-btn">
                            <button type="button" id="clearSearchBtn" runat="server" class="btn btn-default btn-flat" style="border-radius: unset !important" onserverclick="clearSearchBtn_ServerClick">
                                <i class="fa fa-remove"></i>
                            </button>
                        </span>
                        <span class="input-group-btn">
                            <button type="button" id="searchBtn" runat="server" class="btn btn-info btn-flat" onserverclick="searchBtn_ServerClick">
                                <i class="fa fa-search  hidden-md hidden-lg"></i>
                                <asp:Label meta:resourcekey="searchBtn" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></button>
                        </span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <ej:Grid ID="usersGrid" runat="server" AllowSelection="false" Locale="el-GR" IsResponsive="true" EnableResponsiveRow="true" MinWidth="900" OnServerRecordDoubleClick="usersGrid_ServerRecordDoubleClick" OnServerCommandButtonClick="usersGrid_ServerCommandButtonClick">
                        <SortedColumns>
                            <ej:SortedColumn Field="LastName" Direction="Ascending" />
                            <ej:SortedColumn Field="FirstName" Direction="Ascending" />
                        </SortedColumns>
                        <Columns>
                            <ej:Column Field="FullName" HeaderText="Ονοματεπώνυμο">
                            </ej:Column>
                            <ej:Column Field="Email" HeaderText="Email" Width="200">
                            </ej:Column>
                            <ej:Column Field="Username" HeaderText="Username" Width="130">
                            </ej:Column>
                            <ej:Column Field="RoleName" HeaderText="Ρόλος" Width="100">
                            </ej:Column>
                            <ej:Column HeaderText=" " IsUnbound="True" Width="60" AllowResizing="False" AllowTextWrap="False">
                                <Command>
                                    <ej:Commands Type="Edit">
                                        <ButtonOptions Size="Small" Text="Edit" Type="Button" ContentType="ImageOnly" PrefixIcon="e-icon e-edit" Width="30"></ButtonOptions>
                                    </ej:Commands>
                                    <%--<ej:Commands Type="Delete">
                                        <ButtonOptions Size="Small" Text="Delete" Type="Button" ContentType="ImageOnly" PrefixIcon="e-icon e-delete" Width="30"></ButtonOptions>
                                    </ej:Commands>--%>
                                </Command>
                            </ej:Column>
                            <ej:Column Field="UserId" HeaderText="A/A" IsIdentity="True" IsPrimaryKey="True" Visible="False" Width="0">
                            </ej:Column>
                        </Columns>
                    </ej:Grid>
                    <ej:Pager ID="usersPager" runat="server" OnChange="usersPager_Change" IsResponsive="true" Locale="el-GR"></ej:Pager>
                </div>
            </div>
        </ContentTemplate>
    </asp:UpdatePanel>
</asp:Content>
