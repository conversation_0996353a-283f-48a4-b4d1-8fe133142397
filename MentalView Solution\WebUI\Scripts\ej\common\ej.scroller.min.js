/*!
*  filename: ej.scroller.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./ej.core.min"],n):n()})(function(){(function(n,t,i,r){"use strict";t.widget("ejScrollBar","ej.ScrollBar",{defaults:{orientation:"horizontal",viewportSize:0,height:18,width:18,smallChange:57,largeChange:57,value:0,maximum:0,minimum:0,buttonSize:18,infiniteScrolling:!1},validTags:["div"],type:"transclude",dataTypes:{buttonSize:"number",smallChange:"number",largeChange:"number"},observables:["value"],value:t.util.valueFunction("value"),_enabled:!0,content:function(){return this._content&&this._content.length||(this._content=this.model.orientation==="horizontal"?this.element.find(".e-hhandle"):this.element.find(".e-vhandle")),this._content},_init:function(){this.element.addClass("e-widget");this._ensureScrollers();this.content();this._setInitialValues()},_setInitialValues:function(){var n="X";this.model.orientation===t.ScrollBar.Orientation.Horizontal?this.element.addClass("e-hscrollbar"):(this.element.addClass("e-vscrollbar"),n="Y");(this.value()!==0||this.model.minimum!==0)&&(this.value()<this.model.minimum&&this.value(this.model.minimum),this.scroll(this.value(),"none"))},_ensureScrollers:function(){var t=n.fn.jquery;this.model.height&&this.element.height(this.model.height);this.model.width&&this.element.width(this.model.width);this._scrollData||(this._scrollData=this.model.orientation==="vertical"?this._createScroller("Height","Y","Top","e-v"):this._createScroller("Width","X","Left","e-h"))},_setModel:function(n){for(var t in n)if(t==="value")this.value()&&this.scroll(this.value(),"none");else{this.refresh();break}},_createScroller:function(t,i,r,u){var f={},o=n.fn.jquery,e;return f.dimension=t,f.xy=i,f.position=r,f.css=u,f.uDimension=t,this._calculateLayout(f),this._createLayout(f),e=this[f.main].find(".e-button"),this._off(e,"mousedown")._on(e,"mousedown",{d:f,step:1},this._spaceMouseDown),this._off(this[f.scroll],"mousedown")._on(this[f.scroll],"mousedown",{d:f},this._spaceMouseDown),this._off(this[f.handler],"mousedown touchstart")._on(this[f.handler],"mousedown touchstart",{d:f},this._mouseDown),f},_createLayout:function(i){var r="<div class='"+i.css+"{0}' style='"+i.dimension+":{1}px'>{2}<\/div>",u=n.fn.jquery,f={},o,e;f[i.dimension]=i.modelDim;e=t.buildTag("div."+i.css+"scroll e-box",String.format(r,"up e-chevron-up_01 e-icon e-box e-button",i.buttonSize)+String.format(r,"handlespace",i.handleSpace,String.format(r,"handle e-box e-pinch",i.handle))+String.format(r,"down e-chevron-down_01 e-icon e-box e-button",i.buttonSize),f);this.element.append(e);this.element.find(".e-vhandle").addClass("e-v-line e-icon");this.element.find(".e-hhandle").addClass("e-h-line e-icon");o=u==="1.7.1"||u==="1.7.2"?i.uDimension.toLowerCase():"outer"+i.uDimension;this[i.handler]=this.element.find("."+i.handler);this[i.handler].css("transition","none");this[i.scroll]=this[i.handler].parent();this[i.main]=this[i.scroll].parent();this[i.main].find(".e-button")["outer"+i.uDimension](i.buttonSize)},_calculateLayout:function(n){var i,u;n.scrollDim="scroll"+n.dimension;n.lPosition=n.position.toLowerCase();n.clientXy="page"+n.xy;n.scrollVal="scroll"+n.position;n.scrollOneStepBy=this.model.smallChange;n.modelDim=this.model[n.dimension=n.dimension.toLowerCase()];n.handler=n.css+"handle";n.buttonSize=this.model.buttonSize;n.main=n.css+"scroll";n.scroll=n.css+"ScrollSpace";n.handleSpace=n.modelDim-2*n.buttonSize;n.scrollable=this.model.maximum-this.model.minimum;i=this.model.height;this.model.orientation==="horizontal"&&(i=this.model.width);n.handle=this.model.viewportSize/(this.model.maximum-this.model.minimum+this.model.viewportSize)*(i-2*this.model.buttonSize);u=!t.isNullOrUndefined(this.model.elementHeight)&&typeof this.model.elementHeight=="string"&&this.model.elementHeight.indexOf("%")!=-1?!0:!1;n.handle<20&&!u&&(n.handle=20);n.onePx=n.scrollable/(n.handleSpace-n.handle);n.fromScroller=!1;n.up=!0;n.vInterval=r},_updateLayout:function(n){this.element.height(this.model.height);this.element.width(this.model.width);var t=this.element.find("."+n.css+"handle"),f=this.element.find("."+n.css+"handlespace"),u=n.dimension=="width"?t.css("left"):t.css("top"),i=n.dimension=="width"?f.outerWidth():f.outerHeight();u!==r&&u!=="auto"&&(i>=n.handle+parseFloat(u)||(this.model.enableRTL?t.css(n.dimension==="width"?"left":"top",parseFloat(i)-n.handle):t.css(n.dimension==="width"?"left":"top",parseFloat(i)-n.handle>0?parseFloat(i)-n.handle:0)));this.element.find("."+n.css+"scroll").css(n.dimension,n.modelDim+"px").find(".e-button").css(n.dimension,this.model.buttonSize).end().find("."+n.css+"handlespace").css(n.dimension,n.handleSpace+"px").find("."+n.css+"handle").css(n.dimension,n.handle+"px")},refresh:function(){this._ensureScrollers();this.value()&&this.scroll(this.value(),"none");this._scrollData&&(this._calculateLayout(this._scrollData),this._updateLayout(this._scrollData))},scroll:function(n,i,r,u){var o=this._scrollData,f,e;if(!r)if(this.model.orientation===t.ScrollBar.Orientation.Horizontal){if(this._trigger("scroll",{source:i||"custom",scrollData:this._scrollData,scrollLeft:n,originalEvent:u}))return}else if(this._trigger("scroll",{source:i||"custom",scrollData:this._scrollData,scrollTop:n,originalEvent:u}))return;this._scrollData&&(this._scrollData.enableRTL&&(u=="mousemove"||u=="touchmove")&&t.browserInfo().name!="msie"?this.value(-o.scrollable+n):this._scrollData.enableRTL&&(u=="mousemove"||u=="touchmove")&&t.browserInfo().name=="msie"?this.value(-1*n):this.value(n),this.content().length>0&&(this.model.orientation===t.ScrollBar.Orientation.Horizontal?(f=this.element.find(".e-hhandlespace").width()-this.element.find(".e-hhandle").outerWidth(),n=f<(n-this.model.minimum)/this._scrollData.onePx?f:(n-this.model.minimum)/this._scrollData.onePx,this._scrollData.enableRTL&&(u=="mousemove"||u=="touchmove")&&t.browserInfo().name!="msie"&&(n=f-n,n>0?n=n*-1:n),this._scrollData.enableRTL&&(u=="mousemove"||u=="touchmove")&&t.browserInfo().name=="msie"&&(n=-n),this._scrollData.enableRTL&&n>0&&!this._scrollData._scrollleftflag?n=0:n,this._scrollData._scrollleftflag&&(n>0?n=n*-1:n,this.value(n)),this.content()[0].style.left=n+"px",this._scrollData._scrollleftflag=!1):(e=this.element.find(".e-vhandlespace").height()-this.element.find(".e-vhandle").outerHeight(),n=e<(n-this.model.minimum)/this._scrollData.onePx?e:(n-this.model.minimum)/this._scrollData.onePx,t.browserInfo().name=="msie"&&isNaN(n)&&(n=""),this.content()[0].style.top=n+"px")))},_changeTop:function(n,t,i){var u,r;return u=n.dimension==="height"?this.value():this.value(),r=u+t,n.step=t,n.enableRTL&&t<0||t>0&&!n.enableRTL?n.enableRTL?r<this.model.maximum*-1&&(r=this.model.maximum*-1):r>this.model.maximum&&(r=this.model.maximum):n.enableRTL?r>this.model.minimum&&(r=this.model.minimum):r<this.model.minimum&&(r=this.model.minimum),(r!==u||this.model.infiniteScrolling)&&this.scroll(r,i),r!==u},_mouseUp:function(i){if(i.data){var r=i.data.d;clearInterval(r.vInterval);i.type=="touchend"&&n(i.target).removeClass("e-touch");i.type!=="mouseup"&&i.type!=="touchend"&&(i.toElement||i.relatedTarget||i.target)||(this._prevY=this._d=this._data=null,this._off(n(document),"mousemove touchmove",this._mouseMove),n(document).off("mouseup touchend",t.proxy(this._mouseUp,this)),r.fromScroller=!1,this[r.scroll].off("mousemove"),this[r.handler].off("mousemove").css("transition",""),i.data.source!=="thumb"||t.isNullOrUndefined(this.model)||n.when(this.content()).done(t.proxy(function(){this._trigger("thumbEnd",{originalEvent:i,scrollData:r})},this)));r.up=!0}},_mouseDown:function(i){if(this._enabled){this._d=i;this._data=this._d.data.d;this._data.target=this._d.target;this._data.fromScroller=!0;this[this._data.handler].css("transition","none");this._on(n(document),"mousemove touchmove",{d:this._data,source:"thumb"},this._mouseMove);this._trigger("thumbStart",{originalEvent:this._d,scrollData:this._data});n(document).one("mouseup touchend",{d:this._data,source:"thumb"},t.proxy(this._mouseUp,this));i.type=="touchstart"&&n(i.target).addClass("e-touch")}},_mouseCall:function(n){n.type="mouseup";this._mouseUp(n)},_mouseMove:function(i){var u,f=0,r=parseInt(this[this._data.handler].css(this._data.lPosition))||0,o,e;if(i.preventDefault(),o=1,t.isNullOrUndefined(i.target.tagName)){if(n(i.target).is(document)){this._mouseCall(i);return}}else if(i.target.tagName.toLowerCase()==="iframe"){this._mouseCall(i);return}e=i.type=="mousemove"?i[this._data.clientXy]:i.originalEvent.changedTouches[0][this._data.clientXy];this._prevY&&e!==this._prevY&&(f=e-this._prevY,this.model.infiniteScrolling?(r=r+f,this._data.step=f,(this._data.enableRTL?r>0:r<0)&&(r=0),r*(this._data.enableRTL?-1:1)+this._data.handle>=this._data.handleSpace&&(r=(this._data.handleSpace-this._data.handle)*(this._data.enableRTL?-1:1)),u=Math.ceil(r*this._data.onePx),this.scroll(u,"thumb")):(u=f*this._data.onePx,this._changeTop(this._data,u,"thumb",this._d)),this._trigger("thumbMove",{originalEvent:i,direction:this._data.step>0?1:-1,scrollData:this._data}));o===1&&(this._prevY=e)},_spaceMouseDown:function(r){var u,o,f,e;if(r.data&&this._enabled&&(u=r.data.d,o=this[u.handler][0].getBoundingClientRect(),r.which===1&&r.target!==this[u.handler][0])){f=r.data.step?this.model.smallChange:this.model.largeChange;e=r.data.top||o[u.lPosition];r[u.clientXy]=r[u.clientXy]||0;r[u.clientXy]-i.pageYOffset<e&&(f*=-1);u.target=r.target;this._changeTop(u,f,f===3?"track":"button",r);r.data.step!==1&&this[u.scroll].mousemove(function(){u.up=!0});u.up=!1;u.vInterval=setInterval(t.proxy(function(){if((f<0?e+f/u.onePx<r[u.clientXy]:e+u.handle+f/u.onePx>r[u.clientXy])&&(u.up=!0),u.up){clearInterval(u.vInterval);return}this._changeTop(u,f,f===3?"track":"button",r);e=r.data?r.data.top||o[u.lPosition]:o[u.lPosition]},this),150);n(document).one("mouseup",{d:u},t.proxy(this._mouseUp,this));n(document).mouseout({d:u},t.proxy(this._mouseUp,this))}},_remove:function(){this.model.orientation===t.ScrollBar.Orientation.Horizontal&&this.element.find(".e-hscroll").remove();this.model.orientation===t.ScrollBar.Orientation.Vertical&&this.element.find(".e-vscroll").remove();this._scrollData=null;this._content=null},_destroy:function(){this.element.remove()}});t.ScrollBar.Orientation={Horizontal:"horizontal",Vertical:"vertical"}})(jQuery,Syncfusion,window),function(n,t,i,r){"use strict";t.widget("ejScroller","ej.Scroller",{_addToPersist:["scrollLeft","scrollTop"],defaults:{height:250,autoHide:!1,animationSpeed:600,width:0,scrollOneStepBy:57,buttonSize:18,scrollLeft:0,scrollTop:0,targetPane:null,scrollerSize:18,enablePersistence:!1,enableRTL:r,enableTouchScroll:!0,preventDefault:!1,enabled:!0,create:null,destroy:null,wheelStart:null,wheelMove:null,wheelStop:null},validTags:["div"],type:"transclude",dataTypes:{buttonSize:"number",scrollOneStepBy:"number"},observables:["scrollTop","scrollLeft"],scrollTop:t.util.valueFunction("scrollTop"),scrollLeft:t.util.valueFunction("scrollLeft"),keyConfigs:{up:"38",down:"40",left:"37",right:"39",pageUp:"33",pageDown:"34",pageLeft:"ctrl+37",pageRight:"ctrl+39"},content:function(){return!this._contentOffsetParent&&this._content&&this._content[0]&&(this._contentOffsetParent=this._content[0].offsetParent),this._content&&this._content.length&&this._contentOffsetParent||(this._content=this.element.children().first().addClass("e-content")),this._content},_setFirst:!0,_updateScroll:!1,_init:function(){t.isNullOrUndefined(this.content()[0])||(this._isJquery3=parseInt(n.fn.jquery)>=3?!0:!1,this._tempWidth=this.model.width,this._prevScrollWidth=this.content()[0].scrollWidth,this._prevScrollHeight=this.content()[0].scrollHeight,this.element.addClass("e-widget"),this.content(),this._browser=t.browserInfo().name,this._wheelStart=!0,this._eleHeight=this.model.height,this._eleWidth=this.model.width,this._isNativeScroll=t.isDevice(),this.model.targetPane!=null&&this.content().find(this.model.targetPane).addClass("e-target-pane"),this.model.enableRTL===r&&(this.model.enableRTL=this.element.css("direction")==="rtl"),this.model.autoHide&&this._on(this.element,"mousedown",this._mouseDownInitContent),this._ensureScrollers(),this.model.enableRTL&&(this.element.addClass("e-rtl"),this._rtlScrollLeftValue=this.content().scrollLeft()),this._isNativeScroll&&this.element.addClass("e-native-scroll"),this._on(this.content(),"scroll",this._scroll),this.model.targetPane!=null&&this._on(this.content().find(this.model.targetPane),"scroll",this._scroll),this.scrollLeft()&&this._setScrollLeftValue(this.scrollLeft()),this.scrollTop()&&this.scrollTop(this._isJquery3?Math.ceil(this.scrollTop()):this.scrollTop()),this.content().scrollTop(this.scrollTop()),this.model.autoHide&&this._autohide(),this.model.enabled?this.enable():this.disable(),this._setDimension(),(this._prevScrollWidth!==this.content()[0].scrollWidth||this._prevScrollHeight!==this.content()[0].scrollHeight)&&this.refresh());this._addActionClass();this._isNativeScroll&&this._on(this.content(),"scrollstop",this._touchDown)},_mouseDownInitContent:function(){this.model.autoHide&&this._on(n(document),"mouseup",this._mouseUpContent);this.element.addClass("e-scroll-focus")},_addActionClass:function(){this._browser=="msie"&&(this.content().removeClass("e-pinch e-pan-x e-pan-y"),this._vScrollbar&&this._hScrollbar?this.content().addClass("e-pinch"):this._vScrollbar&&!this._hScrollbar?this.content().addClass("e-pan-x"):this._hScrollbar&&!this._vScrollbar&&this.content().addClass("e-pan-y"))},_setDimension:function(){t.isNullOrUndefined(this.model.height)||typeof this.model.height!="string"||this.model.height.indexOf("%")==-1||(this._vScroll||this._hScroll?this.model.height=this._convertPercentageToPixel(parseInt(this._eleHeight),this.element.parent().height()):n(this.content()[0]).height(""));t.isNullOrUndefined(this.model.width)||typeof this.model.width!="string"||this.model.width.indexOf("%")==-1||(this._hScroll||this._vScroll?this.model.width=this._convertPercentageToPixel(parseInt(this._eleWidth),this.element.parent().width()):n(this.content()[0]).width(""))},_setScrollLeftValue:function(n){this.model.enableRTL&&(n=t.browserInfo().name=="mozilla"?n<0?n:n*-1:!t.isNullOrUndefined(this._rtlScrollLeftValue)&&(t.browserInfo().name=="chrome"||this._rtlScrollLeftValue>0)?n<0?this._rtlScrollLeftValue+n:this._rtlScrollLeftValue-n:Math.abs(n));this.content().scrollLeft(n)},_ensureScrollers:function(){var u=n.fn.jquery,f;if(this.model.height=typeof this.model.height=="string"&&this.model.height.indexOf("px")!=-1?parseInt(this.model.height):this.model.height,this.model.width=typeof this.model.width=="string"&&this.model.width.indexOf("px")!=-1?parseInt(this.model.width):this.model.width,this.model.height&&this.element.height(this.model.height),this.model.width&&this.element.width(this.model.width),this._off(this.content(),"mousedown touchstart"),this.content().length>0){if(this.isVScroll()?(this._tempVscrollbar||(this._vScrollbar=this._createScrollbar(t.ScrollBar.Orientation.Vertical,this.isHScroll()),this._tempVscrollbar=this._vScrollbar),this.model.enableTouchScroll&&this._on(this.content(),"mousedown touchstart",{d:this._vScrollbar._scrollData},this._mouseDownOnContent)):(this._vScrollbar=null,this._tempVscrollbar=this._vScrollbar,this.element.children(".e-vscrollbar").remove()),this.isHScroll()?(this._tempHscrollbar||(this._hScrollbar=this._createScrollbar(t.ScrollBar.Orientation.Horizontal,this.isVScroll()),this._tempHscrollbar=this._hScrollbar),this.model.enableTouchScroll&&this._on(this.content(),"mousedown touchstart",{d:this._hScrollbar._scrollData},this._mouseDownOnContent)):(this._hScrollbar=null,this._tempHscrollbar=this._hScrollbar,this.element.children(".e-hscrollbar").remove()),this._vScrollbar||this._hScrollbar||this.content().css({width:"auto",height:"auto"}),this.element.find(".e-hscroll").length>0||this._vScrollbar&&this.content().outerHeight(this.content().outerHeight()-1),u==="1.7.1"||u==="1.7.2"?(this._contentHeight="height",this._contentWidth="width"):(this._contentHeight="outerHeight",this._contentWidth="outerWidth"),this._hScroll=this.isHScroll(),this._vScroll=this.isVScroll(),this._hScroll||this._vScroll){if(this.content().addClass("e-content"),f=this._exactElementDimension(this.element),this._elementDimension(f),this.model.targetPane!==null&&this.content().find(this.model.targetPane)[0]!==r?this.content().find(this.model.targetPane)[0].scrollLeft=this.scrollLeft():!this.isHScroll()&&this.element.children(".e-hscrollbar").length>0&&this._ensureScrollers(),isNaN(this._eleWidth)&&this._eleWidth.indexOf("%")>0&&isNaN(this._eleHeight)&&this._eleHeight.indexOf("%")>0)n(i).on("resize",n.proxy(this._resetScroller,this))}else this.content().removeClass("e-content");this._setDimension();this._parentHeight=n(this.element).parent().height();this._parentWidth=n(this.element).parent().width()}},_elementDimension:function(n){this._ElementHeight=n.height-(this.border_bottom+this.border_top+this.padding_bottom+this.padding_top);this.content()[this._contentHeight](this._ElementHeight-(this._hScroll&&!this.model.autoHide?this.model.scrollerSize:this.element.find(".e-hscrollbar").is(":visible")?this.model.scrollerSize:0));this._ElementWidth=n.width-(this.border_left+this.border_right+this.padding_left+this.padding_right);this.content()[this._contentWidth](this._ElementWidth-(this._vScroll&&!this.model.autoHide?this.model.scrollerSize:this.element.find(".e-vscrollbar").is(":visible")?this.model.scrollerSize:0))},_convertPercentageToPixel:function(n,t){return Math.floor(n*t/100)},isHScroll:function(){var u=parseFloat(n.fn.jquery)>=3?Math.ceil(this.element.width()):this.element.width(),i=this.model.width,r;if(t.isNullOrUndefined(this.model.width)||(i=typeof this.model.width=="string"&&this.model.width.indexOf("%")!=-1?u:parseFloat(n.fn.jquery)>=3&&!isNaN(parseFloat(this.model.width))?Math.ceil(parseFloat(this.model.width)):this.model.width),t.isNullOrUndefined(this._tempWidth)||typeof this._tempWidth!="string"||this._tempWidth.indexOf("%")==-1){if(i>0){if(r=this.content().find(this.model.targetPane),this.model.targetPane!=null&&r.length)return r[0].scrollWidth+r.siblings().width()>i;if(this.content()[0].scrollWidth>i)return!0;if(this.content()[0].scrollWidth==i){if(this.model.autoHide&&n(this.content()[0]).find("> *").length>0)return n(this.content()[0]).find("> *")[0].scrollWidth>n(this.content()[0]).width();if(n(this.content()[0]).find("> *").length>0)return n(this.content()[0]).find("> *")[0].scrollWidth>(t.isNullOrUndefined(this._tempVscrollbar)?i:i-this.model.scrollerSize)}return!1}return!1}if(t.isNullOrUndefined(this.model.width)||typeof this.model.width!="string"||this.model.width.indexOf("%")==-1){if(this.content()[0].scrollWidth>u)return!0}else return this.content()[0].scrollWidth>u},isVScroll:function(){if(t.isNullOrUndefined(this.model.height)||typeof this.model.height!="string"||this.model.height.indexOf("%")==-1){if(this.model.height>0&&(this.content()[0].scrollHeight>Math.ceil(this.model.height)||this.isHScroll()&&(this.content()[0].scrollHeight==this.model.height||this.content()[0].scrollHeight>this.model.height-(this.model.scrollerSize-2))))return!0}else return this.content()[0].scrollHeight>this.element.outerHeight();return!1},_setModel:function(n){for(var i in n)switch(i){case"enableRTL":n[i]?(this.element.addClass("e-rtl"),this._rtlScrollLeftValue=this.content().scrollLeft(),t.isNullOrUndefined(this._hScrollbar)||(this._hScrollbar._scrollData.enableRTL=!0)):(this.element.removeClass("e-rtl"),t.isNullOrUndefined(this._hScrollbar)||(this._hScrollbar._scrollData.enableRTL=!1));this._hScrollbar&&(this.element.find(".e-hhandle").css("left",0),this._hScrollbar.value(0));break;case"preventDefault":this.model.preventDefault=n[i];break;case"scrollLeft":(parseFloat(t.util.getVal(n[i]))<0||!this._hScroll)&&(n[i]=0);this._hScrollbar&&(n[i]=parseFloat(t.util.getVal(n[i]))>this._hScrollbar._scrollData.scrollable?this._hScrollbar._scrollData.scrollable:parseFloat(t.util.getVal(n[i])));this._setScrollLeftValue(parseFloat(n[i]));this.scrollLeft(n[i]);!this._hScrollbar||this._hScrollbar._scrollData._scrollleftflag&&this.model.enableRTL||this.scrollX(n[i],!0);break;case"scrollTop":this._vScrollbar&&(n[i]=parseFloat(t.util.getVal(n[i]))>this._vScrollbar._scrollData.scrollable?this._vScrollbar._scrollData.scrollable:parseFloat(t.util.getVal(n[i])));(parseFloat(n[i])<0||!this._vScroll)&&(n[i]=0);this.content().scrollTop(parseFloat(n[i]));this.scrollTop(n[i]);this.scrollY(n[i],!0);break;case"touchScroll":this.model.enableTouchScroll?(this._vScrollbar&&this._on(this.content(),"mousedown touchstart",{d:this._vScrollbar._scrollData},this._mouseDownOnContent),this._hScrollbar&&this._on(this.content(),"mousedown touchstart",{d:this._hScrollbar._scrollData},this._mouseDownOnContent)):this._off(this.content(),"mousedown touchstart");break;case"scrollOneStepBy":this._vScrollbar&&(this._vScrollbar._scrollData.scrollOneStepBy=n[i],this._vScrollbar.model.smallChange=n[i]);this._hScrollbar&&(this._hScrollbar._scrollData.scrollOneStepBy=n[i],this._hScrollbar.model.smallChange=n[i]);break;case"buttonSize":this._vScrollbar&&(this._vScrollbar.model.buttonSize=this.model.buttonSize);this._hScrollbar&&(this._hScrollbar.model.buttonSize=this.model.buttonSize);this.refresh();break;case"height":this._eleHeight=n[i];this.refresh();break;case"width":this._eleWidth=n[i];this.refresh();break;case"enabled":n[i]?this.enable():this.disable();break;default:this.refresh()}},_createScrollbar:function(i,r){var c=this,f,o,l,s,a,h=document.createElement("div"),e,u;return i===t.ScrollBar.Orientation.Vertical?(o=this.model.scrollerSize,l=t.isNullOrUndefined(this.model.height)||typeof this.model.height!="string"||this.model.height.indexOf("%")==-1?f=this.model.height-(r?this.model.scrollerSize:0):f=this.element.height()-(r?this.model.scrollerSize:0),s=this.content()[0].scrollHeight,a=this.scrollTop()):(o=f=this.model.width-(r?this.model.scrollerSize:0),l=this.model.scrollerSize,t.isNullOrUndefined(this.model.width)||typeof this.model.width!="string"||this.model.width.indexOf("%")==-1?(e=this.content().find(this.model.targetPane),s=this.model.targetPane!=null&&e.length?e[0].scrollWidth+e.parent().width()-e.width():this.content()[0].scrollWidth):(o=f=this.element.width()-(r?this.model.scrollerSize:0),s=this.content()[0].scrollWidth),a=this.scrollLeft()),this.element.children(".e-hscrollbar").length>0?n(this.element.children(".e-hscrollbar")).before(h):this.element.append(h),n(h).ejScrollBar({elementHeight:c._eleHeight,elementWidth:c._eleWidth,buttonSize:c.model.buttonSize,orientation:i,viewportSize:f,height:l,width:o,maximum:s-f,value:a,smallChange:this.model.scrollOneStepBy,largeChange:3*this.model.scrollOneStepBy,scroll:t.proxy(this._scrollChanged,this),thumbEnd:t.proxy(this._thumbEnd,this),thumbStart:t.proxy(this._thumbStart,this),thumbMove:t.proxy(this._thumbMove,this)}),u=n(h).ejScrollBar("instance"),i!==t.ScrollBar.Orientation.Vertical&&r||this._off(this.element,this._browser=="msie"?"wheel mousewheel":"mousewheel DOMMouseScroll",this._mouseWheel)._on(this.element,this._browser=="msie"?"wheel mousewheel":"mousewheel DOMMouseScroll",{d:u._scrollData},this._mouseWheel),i===t.ScrollBar.Orientation.Horizontal?this._scrollXdata=u._scrollData:this._scrollYdata=u._scrollData,i===t.ScrollBar.Orientation.Horizontal&&this.model.enableRTL&&(u._scrollData.enableRTL=!0),u._enabled=this.model.enabled,u},_updateScrollbar:function(i,r){var u=i===t.ScrollBar.Orientation.Vertical?this._vScrollbar:this._hScrollbar;u&&(i===t.ScrollBar.Orientation.Vertical?(u.model.width=this.model.scrollerSize,u.model.height=u.model.viewportSize=this.model.height-(r?this.model.scrollerSize:0),u.model.maximum=this.content()[0].scrollHeight-u.model.viewportSize,u.model.value=this.scrollTop()):(u.model.width=u.model.viewportSize=this.model.width-(r?this.model.scrollerSize:0),u.model.height=this.model.scrollerSize,u.model.maximum=(this.model.targetPane!=null&&this.content().find(this.model.targetPane).length>0?this.content().find(this.model.targetPane)[0].scrollWidth+(this.content().width()-this.content().find(n(this.model.targetPane)).outerWidth()):this.content()[0].scrollWidth)-u.model.viewportSize,this.model.enableRTL||(u.model.value=this.scrollLeft())))},_autohide:function(){this.model.autoHide?(this.element.addClass("e-autohide"),this._on(this.element,"mouseenter mouseleave touchstart touchend",this._scrollerHover),n(":hover").filter(this.element[0]).length||this.content().siblings(".e-scrollbar.e-js").hide(),this._elementDimension(this._exactElementDimension(this.element))):(this.element.removeClass("e-autohide"),this._off(this.element,"mouseenter mouseleave touchstart touchend",this._scrollerHover),this.content().siblings(".e-scrollbar.e-js").show())},_mouseUpContent:function(t){t.type=="mouseup"&&(this.element.removeClass("e-scroll-focus"),this._autohide(),this._off(n(document),"mouseup",this._mouseUpContent))},_scrollChanged:function(i){this._updateScroll=!0;i.scrollTop!==r?this.scrollY(i.scrollTop,!0,"",i.source):i.scrollLeft!==r&&this.scrollX(i.scrollLeft,!0,"",i.source);this._updateScroll=!1;var u=this;n.when(this.content()).done(t.proxy(function(){u._trigger("scrollEnd",{scrollData:i})}))},_bindBlurEvent:function(r,u){this._scrollEle=n(r).data("ejScrollBar");this._event=u;var f=this;this._listener=function(){this._scrollEle._off(n(document),"mousemove touchmove",this._scrollEle._mouseMove);n(document).off("mouseup touchend",t.proxy(this._scrollEle._mouseUp,this._scrollEle));this._scrollEle._prevY=null;this._off(n(document),"mousemove touchmove",this._mouseMove);this._off(n(document),"mouseup touchend",this._mouseUp);this._off(n(i),"blur");this._evtData.handler==="e-vhandle"?this._scrollEle._trigger("thumbEnd",{originalEvent:this._event,scrollData:this._evtData}):this._scrollEle._trigger("thumbEnd",{originalEvent:this._event,scrollData:this._evtData})};this._on(n(i),"blur",this._listener)},_thumbStart:function(n){this._evtData=n.scrollData;var t=n.scrollData.handler==="e-vhandle"?this.element.find("."+n.scrollData.handler).closest(".e-scrollbar"):this.element.find("."+n.scrollData.handler).closest(".e-scrollbar"),t=n.scrollData.handler==="e-vhandle"?this.element.find("."+n.scrollData.handler).closest(".e-scrollbar"):this.element.find("."+n.scrollData.handler).closest(".e-scrollbar");this._bindBlurEvent(t,n);this._trigger("thumbStart",n)},_thumbMove:function(n){this._trigger("thumbMove",n)},_thumbEnd:function(t){this._trigger("thumbEnd",t);this._off(n(i),"blur")},refresh:function(i){var r,u;i?(this._tempVscrollbar=null,this.element.children(".e-vscrollbar").remove(),this._tempHscrollbar=null,this.element.children(".e-hscrollbar").remove()):this.element.find(">.e-content").removeAttr("style");t.isNullOrUndefined(this._eleHeight)||typeof this._eleHeight!="string"||this._eleHeight.indexOf("%")==-1||this._parentHeight==n(this.element).parent().height()||(r=this._exactElementDimension(this.element.parent()),r=r.height-(this.border_bottom+this.border_top+this.padding_bottom+this.padding_top),this.model.height=this._convertPercentageToPixel(parseInt(this._eleHeight),r));t.isNullOrUndefined(this._eleWidth)||typeof this._eleWidth!="string"||this._eleWidth.indexOf("%")==-1||this._parentWidth==n(this.element).parent().width()||(r=this._exactElementDimension(this.element.parent()),r=r.width-(this.border_left+this.border_right+this.padding_left+this.padding_right),this.model.width=this._convertPercentageToPixel(parseInt(this._eleWidth),r));this._ensureScrollers();u=this.model.scrollLeft;this.model.enableRTL?(this.element.hasClass("e-rtl")||this.element.addClass("e-rtl"),this._rtlScrollLeftValue=this.content().scrollLeft(),u>0?this.content().scrollLeft(this._rtlScrollLeftValue-u):this._setScrollLeftValue(u)):this.content().scrollLeft(u);(this.scrollTop()&&t.isNullOrUndefined(this._vScrollbar)||!t.isNullOrUndefined(this._vScrollbar)&&this._vScrollbar&&this._vScrollbar._scrollData!=null&&!this._vScrollbar._scrollData.skipChange)&&this.scrollTop(this._isJquery3?Math.ceil(this.scrollTop()):this.scrollTop());this.content().scrollTop(this.scrollTop());this._vScrollbar&&(this._vScrollbar._scrollData.dimension="Height",this._updateScrollbar(t.ScrollBar.Orientation.Vertical,this._hScroll),this._vScroll&&!this._vScrollbar._calculateLayout(this._vScrollbar._scrollData)&&this._vScrollbar._updateLayout(this._vScrollbar._scrollData));this._hScrollbar&&(this._hScrollbar._scrollData.dimension="Width",this._updateScrollbar(t.ScrollBar.Orientation.Horizontal,this._vScroll),this._hScroll&&!this._hScrollbar._calculateLayout(this._hScrollbar._scrollData)&&this._hScrollbar._updateLayout(this._hScrollbar._scrollData));t.browserInfo().name=="msie"&&t.browserInfo().version=="8.0"?this.element.find(".e-hhandle").css("left","0px"):this.model.targetPane!=null&&this._on(this.content().find(this.model.targetPane),"scroll",this._scroll);this._addActionClass();this._autohide()},_exactElementDimension:function(n){var i=n.get(0).getBoundingClientRect(),r=["left","right","top","bottom"],u,f,t;for(u=i.width?i.width:i.right-i.left,f=i.height?i.height:i.bottom-i.top,t=0;t<r.length;t++)this["border_"+r[t]]=isNaN(parseFloat(n.css("border-"+r[t]+"-width")))?0:parseFloat(n.css("border-"+r[t]+"-width")),this["padding_"+r[t]]=isNaN(parseFloat(n.css("padding-"+r[t])))?0:parseFloat(n.css("padding-"+r[t]));return{width:u,height:f}},_keyPressed:function(n,i){if(this.model.enabled){if(["input","select","textarea"].indexOf(i.tagName.toLowerCase())!==-1)return!0;var r,u;if(["up","down","pageUp","pageDown"].indexOf(n)!==-1)this._vScrollbar&&(t.browserInfo().name=="msie"&&this.model.allowVirtualScrolling&&this._content.focus(),r=this._vScrollbar._scrollData),u="o";else if(["left","right","pageLeft","pageRight"].indexOf(n)!==-1)this._hScrollbar&&(r=this._hScrollbar._scrollData),u="i";else return!0;return r?!this._changeTop(r,(n.indexOf(u)<0?-1:1)*(n[0]!=="p"?1:3)*r.scrollOneStepBy,"key"):!0}},scrollY:function(n,i,r,u,f){var e=this,f;if(n!==""){if(i){if(f={source:u||"custom",scrollData:this._vScrollbar?this._vScrollbar._scrollData:null,scrollTop:n,originalEvent:f},n=this._isJquery3?Math.ceil(f.scrollTop):f.scrollTop,this.scrollTop(n),this._trigger("scroll",f))return;this.content().scrollTop(n);return}(t.isNullOrUndefined(r)||r==="")&&(r=100);this._vScrollbar&&(n=parseFloat(n)>this._vScrollbar._scrollData.scrollable?this._vScrollbar._scrollData.scrollable:parseFloat(n));n=this._isJquery3?Math.ceil(n):n;this.scrollTop(n);this.content().stop().animate({scrollTop:n},r,"linear",function(){e._trigger("scroll",{source:u||"custom",scrollData:e._vScrollbar?e._vScrollbar._scrollData:null,scrollTop:n,originalEvent:f})})}},scrollX:function(n,i,r,u,f){var o=this,e,s;if(n!==""){if(this._hScrollbar&&(n=parseFloat(n)>this._hScrollbar._scrollData.scrollable?this._hScrollbar._scrollData.scrollable:parseFloat(n)),e=t.browserInfo().name,this.model.enableRTL&&e!="mozilla"&&e!="chrome"&&(n<0&&(n=Math.abs(n)),s=this.model.targetPane!=null?this.content().find(this.model.targetPane)[0]:this.content()[0],f!="mousemove"&&f!="touchmove"&&e!="msie"&&e!="msie"&&(n=this._hScrollbar._scrollData.scrollable-n)),this.scrollLeft(n),i){if(this._trigger("scroll",{source:u||"custom",scrollData:this._hScrollbar?this._hScrollbar._scrollData:null,scrollLeft:n,originalEvent:f}))return;this.model.targetPane!=null&&this.content().find(this.model.targetPane).length?this.content().find(this.model.targetPane).scrollLeft(n):this.content().scrollLeft(n);return}(t.isNullOrUndefined(r)||r==="")&&(r=100);this.model.targetPane!=null&&this.content().find(this.model.targetPane).length?this.content().find(this.model.targetPane).stop().animate({scrollLeft:n},r,"linear"):this.content().stop().animate({scrollLeft:n},r,"linear",function(){o._trigger("scroll",{source:u||"custom",scrollData:o._hScrollbar?o._hScrollbar._scrollData:null,scrollLeft:n,originalEvent:f})})}},enable:function(){var n=this.element.find(".e-vscrollbar,.e-hscrollbar,.e-vscroll,.e-hscroll,.e-vhandle,.e-hhandle,.e-vscroll .e-icon,.e-hscroll .e-icon");n.hasClass("e-disable")&&(n.removeClass("e-disable").attr({"aria-disabled":!1}),this.model.enabled=!0);this._vScrollbar&&(this._vScrollbar._enabled=this.model.enabled);this._hScrollbar&&(this._hScrollbar._enabled=this.model.enabled)},disable:function(){var n=this.element.find(".e-vscrollbar,.e-hscrollbar,.e-vscroll,.e-hscroll,.e-vhandle,.e-hhandle,.e-vscroll .e-icon,.e-hscroll .e-icon");n.addClass("e-disable").attr({"aria-disabled":!0});this.model.enabled=!1;this._vScrollbar&&(this._vScrollbar._enabled=this.model.enabled);this._hScrollbar&&(this._hScrollbar._enabled=this.model.enabled)},_changeTop:function(n,i,r,u){var e=Math.ceil(this.model.targetPane!=null&&n.dimension!="height"?this.content().find(this.model.targetPane)[n.scrollVal]():this.content()[n.scrollVal]()),f;return n.dimension=="height"&&e==0&&(e=this.scrollTop()!=0?this.scrollTop():0),f=e+i,(n.enableRTL?f<n.scrollable:f>n.scrollable)&&(f=Math.round(n.scrollable)),(n.enableRTL?f>0:f<0)&&(f=0),f!==e&&(this["scroll"+n.xy](f,!0,"",r,u),n.xy!=="X"||t.isNullOrUndefined(this._hScrollbar)?t.isNullOrUndefined(this._vScrollbar)||this._vScrollbar.scroll(f,r,!0,u):this._hScrollbar.scroll(f,r,!0,u)),f!==e},_mouseWheel:function(t){var o;if((!this._vScrollbar||!t.ctrlKey)&&(this._vScrollbar||t.shiftKey)&&t.data&&this.model.enabled){var u=0,f=t.data.d,r=t,e;if(t=t.originalEvent,this._wheelStart&&this._trigger("wheelStart",{originalEvent:t,scrollData:r.data.d}),this._wheelStart=!1,clearTimeout(n.data(this,"timer")),this._wheelx!=1&&(t.wheelDeltaX==0||t.wheelDeltaY==0)&&(this._wheelx=1),navigator.platform.indexOf("Mac")==0&&this._wheelx==0&&(this._browser=="webkit"||this._browser=="chrome"))return!0;(this._browser=="mozilla"?t.axis==t.HORIZONTAL_AXIS?f=this._scrollXdata?this._scrollXdata:f:this._scrollYdata:this._browser=="msie"?(t.type=="wheel"&&(u=t.deltaX/120),t.type=="mousewheel"&&t.shiftKey&&(f=this._scrollXdata,t.preventDefault?t.preventDefault():t.returnValue=!1)):this._wheelx&&t.wheelDeltaX!=0&&t.wheelDeltaY==0&&this._scrollXdata&&(f=this._scrollXdata),t.wheelDeltaX==0&&(this._wheelx=t.wheelDeltaX),t.wheelDelta?(u=this._normalizingDelta(t),i.opera&&parseFloat(i.opera.version,10)<10&&(u=-u)):t.detail&&(u=t.detail/3),u)&&(r.originalEvent&&(e=r.originalEvent.wheelDelta&&r.originalEvent.wheelDelta>0||r.originalEvent.detail&&r.originalEvent.detail<0?-1:1),this._changeTop(f,u*f.scrollOneStepBy,"wheel",t)?(t.preventDefault?t.preventDefault():r.preventDefault(),this._trigger("wheelMove",{originalEvent:t,scrollData:r.data.d,direction:e})):(this._trigger("scrollEnd",{originalEvent:t,scrollData:r}),this._wheelx=0),o=this,n.data(this,"timer",setTimeout(function(){o._wheelStart=!0;o._trigger("wheelStop",{originalEvent:t,scrollData:r.data.d,direction:e})},250)))}},_normalizingDelta:function(n){return navigator.platform.indexOf("Mac")==0?Math.abs(n.wheelDelta)!==120?-n.wheelDelta/3:-n.wheelDelta/80:-n.wheelDelta/120},_contentHeightWidth:function(){this.content().siblings().css("display")=="block"&&this.model.autoHide?(this._hScroll&&this.content()[this._contentHeight](this._ElementHeight-this.model.scrollerSize),this._vScroll&&this.content()[this._contentWidth](this._ElementWidth-this.model.scrollerSize)):this.content().siblings().css("display")=="none"&&this.model.autoHide&&(this._vScroll||this._hScroll)&&(this.content()[this._contentHeight](this._ElementHeight),this.content()[this._contentWidth](this._ElementWidth))},_scrollerHover:function(n){this.model.enabled&&(n.type!="mouseenter"&&n.type!="touchstart"||this.content().siblings().is(":visible")?n.type!="mouseleave"&&n.type!="touchend"||this.element.hasClass("e-scroll-focus")||(this.content().siblings().hide(),this._contentHeightWidth(),this._trigger("scrollHide",{originalEvent:n})):(this.content().siblings().css("display","block"),this._contentHeightWidth(),this._ensureScrollers(),this._setScrollLeftValue(this.model.scrollLeft),this._trigger("scrollVisible",{originalEvent:n})))},_mouseUp:function(r){if(r.data){var u=r.data.d;this.model.enableRTL&&(r.type=="mouseup"||r.type=="touchend")&&(this.model.scrollLeft=this._rtlScrollLeftValue-this.model.scrollLeft);r.type!=="mouseup"&&r.type!=="touchend"&&(r.toElement||r.relatedTarget)||(this.content().css("cursor","default"),this._off(n(document),"mousemove touchmove"),this._off(this.content(),"touchmove",this._touchMove),this._off(n(document),"mouseup touchend",this._mouseUp),u.fromScroller=!1,this._mouseMoved!==!0||r.data.source!=="thumb"||t.isNullOrUndefined(this.model)||(n.when(this.content()).done(t.proxy(function(){this._trigger("thumbEnd",{originalEvent:r,scrollData:u})},this)),this._off(n(i),"blur")));u.up=!0;this._mouseMoved=!1;i.ontouchmove=null}},_mouseDownOnContent:function(u){var f,s;if((this._startX=u.clientX!=r?u.clientX:u.originalEvent.changedTouches[0].clientX,this._startY=u.clientY!=r?u.clientY:u.originalEvent.changedTouches[0].clientY,this._timeStart=u.timeStamp||Date.now(),this.model.enabled)&&(f=u.data.d,this._evtData=u.data,s=f.handler==="e-vhandle"?this.element.find("."+f.handler).closest(".e-scrollbar"):this.element.find("."+f.handler).closest(".e-scrollbar"),this._bindBlurEvent(s,u),!this._trigger("thumbStart",{originalEvent:u,scrollData:f}))&&(u.which!=3||u.button!=2)){f.fromScroller=!0;var e=null,o=1,c=5,h;this._document=n(document);this._window=n(i);this._mouseMove=function(n){var l,a,s;if(this.model.enableRTL&&this._UpdateScrollLeftValue(u),this._startX+this._startY!=n.clientX+n.clientY){if(this._relDisX=(this._startx=n.clientX!=r?n.clientX:n.originalEvent.changedTouches[0].clientX)-this._startX,this._relDisY=(this._starty=n.clientY!=r?n.clientY:n.originalEvent.changedTouches[0].clientY)-this._startY,this._duration=(n.timeStamp||Date.now())-this._timeStart,this._velocityY=Math.abs(this._relDisY)/this._duration,this._velocityX=Math.abs(this._relDisX)/this._duration,this._swipe=Math.abs(this._relDisX)>Math.abs(this._relDisY)?this._relDisX>0?"left":"right":this._relDisY>0?"up":"down",!t.isNullOrUndefined(n.target.tagName)&&n.target.tagName.toLowerCase()==="iframe"){n.type="mouseup";this._mouseUp(n);return}if(l=n.type=="mousemove"?n[f.clientXy]:n.originalEvent.changedTouches[0][f.clientXy],e&&l!==e&&(this._mouseMoved=!0,a=l-e,s=this.model[f.scrollVal]-a,o==1&&Math.abs(a)>c&&(h=f.position,o=0),o==0&&(e=l),s>=0&&s<=f.scrollable&&h===f.position)){var v=this._velocityY>.5&&this._duration<50&&f.position=="Top",y=this._velocityX>.5&&this._duration<50&&f.position=="Left",p=(this._velocityY>.5||this._velocityX>.5)&&this._duration<50;p?v?(s=Math.abs(this._relDisY)+this._duration*this._velocityY,this._startY>this._starty?(s+=this.scrollTop(),s>f.scrollable&&(s=f.scrollable)):(s<this.scrollTop()&&(s=Math.abs(s-this.scrollTop())),s>this.scrollTop()&&(s=0)),this.scrollTop()<=f.scrollable&&this.scrollY(s,!1,this.model.animationSpeed,"thumb")):y&&(s=Math.abs(this._relDisX),this._startX>this._startx?(s+=this.scrollLeft(),s>f.scrollable&&(s=f.scrollable)):(s-=this.scrollLeft(),s=Math.abs(s),(s>f.scrollable||s>=this.scrollLeft())&&(s=0)),this.scrollLeft()<=f.scrollable&&this.scrollX(s,!1,this.model.animationSpeed,"thumb")):(this["scroll"+f.xy](s,!0,"","thumb",n.type),f.xy==="X"?this._hScrollbar.scroll(s,"thumb",!0,n.type):t.isNullOrUndefined(this._vScrollbar)||this._vScrollbar.scroll(s,"thumb",!0,n.type),this.content().css("cursor","pointer"),this._trigger("thumbMove",{originalEvent:n,direction:this._swipe=="down"||this._swipe=="right"?1:-1,scrollData:f}))}i.ontouchmove=function(n){n=n||i.event;n.preventDefault&&n.preventDefault();n.returnValue=!1};e==null&&(e=l);(Math.round(this._content.scrollTop())==0&&this._swipe=="down"||(Math.ceil(this._content.scrollTop())==f.scrollable||Math.ceil(this._content.scrollTop())+1==f.scrollable)&&this._swipe=="up")&&(this._trigger("scrollEnd",{originalEvent:n.originalEvent,scrollData:n}),i.ontouchmove=null)}};this._trigger("touchStart",{originalEvent:u,direction:this._swipe=="down"||this._swipe=="right"?1:-1,scrollData:this._scrollData,scrollTop:this.content().scrollTop(),scrollLeft:this.content().scrollLeft()});this._on(n(document),"mousemove",{d:f,source:"thumb"},this._mouseMove);this._isNativeScroll?this._on(this.content(),"touchmove",{d:f,source:"thumb"},this._touchMove):this._on(n(document),"touchmove",{d:f,source:"thumb"},this._mouseMove);this._on(n(document),"mouseup touchend",{d:f,source:"thumb"},this._mouseUp)}},_touchMove:function(){this.content().css("cursor","pointer");this._mouseMoved=!0;this._tempLeft=this.model.targetPane!=null?this.content().find(this.model.targetPane).scrollLeft():this.content().scrollLeft();this._tempTop=this.content().scrollTop()},_touchDown:function(n){var t;t=this._tempLeft!=this.scrollLeft()?this._scrollXdata:this._tempTop!=this.scrollTop()?this._scrollYdata:this._scrollYdata?this._scrollYdata:this._scrollXdata;this._trigger("scrollStop",{source:"thumb",originalEvent:n,scrollData:t,scrollTop:this.content().scrollTop(),scrollLeft:this.content().scrollLeft()})},_speedScrolling:function(n){var r,i,u,n,t;if(this._mouseMoved){if(this.element.find(".e-vhandle").length>0&&(r=this.content().scrollTop(),this._tempTop!==r&&(this._trigger("thumbMove",{originalEvent:n,direction:this._swipe=="down"||this._swipe=="right"?1:-1,scrollData:this._scrollData}),this._vScrollbar.scroll(this.content().scrollTop(),"thumb",!0,"touchmove"),n={source:"thumb",scrollData:this._vScrollbar?this._vScrollbar._scrollData:null,scrollTop:this.content().scrollTop(),originalEvent:n},t=this._isJquery3?Math.ceil(n.scrollTop):n.scrollTop,this.scrollTop(t),this._trigger("scroll",n))))return;if(this.element.find(".e-hhandle").length>0&&(i=this.model.targetPane!=null?this.content().find(this.model.targetPane):this.content(),u=i.scrollLeft(),this._tempLeft!==u&&(this._trigger("thumbMove",{originalEvent:n,direction:this._swipe=="down"||this._swipe=="right"?1:-1,scrollData:this._scrollData}),this._hScrollbar.scroll(i.scrollLeft(),"thumb",!0,"touchmove"),n={source:"thumb",scrollData:this._hScrollbar?this._hScrollbar._scrollData:null,scrollLeft:this.content().scrollLeft(),originalEvent:n},t=this._isJquery3?Math.ceil(n.scrollLeft):n.scrollLeft,this.scrollLeft(t),this._trigger("scroll",n))))return;this.content().css("cursor","pointer")}},_scroll:function(r){var s=[this._vScrollbar?this._vScrollbar._scrollData:null,this._hScrollbar?this._hScrollbar._scrollData:null],h,e,u,o,f;for(this._evtData&&(h=this._evtData.d?this._evtData.d:this._evtData),e=0;e<2;e++)(u=s[e],u&&!u.skipChange)&&((this.model&&(!this.model.targetPane||this.model.targetPane&&h&&h.xy!="X")&&(u.dimension==="height"?this.scrollTop(r.target[u.scrollVal]):this.scrollLeft(r.target[u.scrollVal])),u.sTop=this.model&&this.model.targetPane!=null&&e==1&&this.content().find(this.model.targetPane).length?this.content().find(this.model.targetPane)[0][u.scrollVal]:u.scrollVal=="scrollTop"?this.scrollTop():this.scrollLeft(),this[u.scrollVal](u.sTop),u.fromScroller)||(e===1?(o=this.content()[0],this._rtlScrollLeftValue&&o.scrollWidth-o.clientWidth!=this._rtlScrollLeftValue&&(this._rtlScrollLeftValue=o.scrollWidth-o.clientWidth),u.sTop=this.model&&t.browserInfo().name!="mozilla"&&this.model.enableRTL&&!this._hScrollbar._scrollData._scrollleftflag?this._rtlScrollLeftValue==0?u.sTop*-1:u.sTop-this._rtlScrollLeftValue:u.sTop,this._hScrollbar.scroll(u.sTop,"",!0)):this._vScrollbar.scroll(u.sTop,"",!0),(s.length==2&&e==1||s.length==1&&e==0)&&(this._externalScroller=!1,this.model&&this._trigger("scroll",{source:"custom",scrollData:this._hScrollbar?this._hScrollbar._scrollData:null,scrollLeft:this.scrollLeft(),originalEvent:r}))));this._isNativeScroll&&this.model.enableTouchScroll&&this._speedScrolling(r);this._UpdateScrollLeftValue(r);f=this;this._vScrollbar&&this._scrollYdata&&this.model&&this._scrollYdata.scrollable-this.model.scrollOneStepBy>=this.scrollTop()&&(n(":hover").filter(this.element[0]).length||f._off(t.getScrollableParents(f.wrapper),"scroll",null),t.browserInfo().name!="chrome"?i.onmousewheel=function(t){f.model&&f.model.preventDefault&&n(":hover").filter(f.element[0]).length&&t.preventDefault()}:i.addEventListener("wheel",function(t){f.model&&f.model.preventDefault&&n(":hover").filter(f.element[0]).length&&t.preventDefault()},{passive:!1}))},_UpdateScrollLeftValue:function(n){this.model&&n.type!="touchstart"&&n.type!="mousedown"&&this.model.enableRTL&&this._rtlScrollLeftValue&&this.model.scrollLeft!=this._previousScrollLeft&&(this.model.scrollLeft=this._rtlScrollLeftValue-this.model.scrollLeft,this._previousScrollLeft=this.model.scrollLeft);(this.model&&n.type=="touchstart"||n.type=="mousedown")&&this.model.enableRTL&&(this.model.scrollLeft=this.content().scrollLeft(),this.option("scrollLeft",this.content().scrollLeft()))},_changevHandlerPosition:function(n){var t=this._vScrollbar;t&&(n=t._scrollData!=null&&n>=t._scrollData.scrollable?t._scrollData.scrollable:n,t!=null&&n>=0&&n<=t._scrollData.scrollable&&t[t._scrollData.handler].css(t._scrollData.lPosition,n/t._scrollData.onePx+"px"))},_changehHandlerPosition:function(n){var t=this._hScrollbar;t&&(n=t._scrollData!=null&&n>=t._scrollData.scrollable?t._scrollData.scrollable:n,t!=null&&top>=0&&n<=t._scrollData.scrollable&&t[t._scrollData.handler].css(t._scrollData.lPosition,n/t._scrollData.onePx+"px"))},_destroy:function(){this._off(this.content(),"scrollstop",this._touchDown);this._off(n(document),"mouseup",this._mouseUpContent);this.element.css({width:"",height:""}).children(".e-vscrollbar,.e-hscrollbar").remove();this.content().removeClass("e-content").css({width:"",height:""});this.element.removeClass("e-widget")},_preventDefault:function(n){n=n||i.event;n.preventDefault&&n.preventDefault();n.returnValue=!1}})}(jQuery,Syncfusion,window)});
