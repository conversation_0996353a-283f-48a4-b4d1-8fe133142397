﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Security;

namespace WebUI
{
    public static class CookieHandler
    {
        public static int SetAuthCookie(HttpResponse response, string name, bool persistentCookie, string userData)
        {
            /// In order to pickup the settings from config, we create a default cookie and use its values to create a 
            /// new one.
            var cookie = FormsAuthentication.GetAuthCookie(name, persistentCookie);
            var ticket = FormsAuthentication.Decrypt(cookie.Value);

            var newTicket = new FormsAuthenticationTicket(ticket.Version, ticket.Name, ticket.IssueDate, ticket.Expiration, ticket.IsPersistent, userData, ticket.CookiePath);
            var encTicket = FormsAuthentication.Encrypt(newTicket);

            /// Use existing cookie. Could create new one but would have to copy settings over...
            cookie.Value = encTicket;

            response.Cookies.Add(cookie);

            return encTicket.Length;
        }

        public static FormsAuthenticationTicket GetAuthCookie(System.Web.UI.Page page)
        {
            if (page.User.Identity.IsAuthenticated == true)
            {
                var cookie = page.Request.Cookies[FormsAuthentication.FormsCookieName];

                return FormsAuthentication.Decrypt(cookie.Value);
            }
            else
            {
                return null;
            }
        }
    }
}