/*!
*  filename: ej.print.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){"use strict";var n=this&&this.__extends||function(n,t){function r(){this.constructor=n}for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)};(function(t){var i=function(i){function r(n,r){return i.call(this),this._rootCSS="e-print",this.PluginName="ejPrint",this.id="null",this.defaults={globalStyles:!0,externalStyles:null,excludeSelector:null,append:null,printInNewWindow:!1,prepend:null,timeOutPeriod:1e3,title:null,height:454,width:1024,docType:"<!doctype html>",beforeStart:null},n&&(n.jquery||(n=t("#"+n)),n.length)?t(n).ejPrint(r).data(this.PluginName):void 0}return n(r,i),r.prototype._init=function(){this.id=this.element[0].id;this._initialize(this.element,undefined)},r.prototype._initialize=function(n,i){var s,e,r,u,f,o;if(this.styles=t(""),this.title=t("title"),this.model.globalStyles&&this._addglobalStyles(this.model.globalStyles),this.model.externalStyles&&this._addStylesToElement(this.model.externalStyles),this.model.title&&this._addTitleToElement(this.model.title),s=t("<html/>"),e=t("<head/>").append(this.title),e.append(this.styles.clone()),r=t("<body/>"),u=t("<div/>"),i){if(typeof n=="string")n=t(n),u.append(n.clone());else if(typeof n=="object")for(f=0;f<n.length;f++)u.append(t(n[f]).clone())}else u.append(n.clone());return(this.copy=u,r.append(this.copy),this.model.append&&r.append(this._getjQueryObject(this.model.append)),this.model.prepend&&r.prepend(this._getjQueryObject(this.model.prepend)),this.copy=s.append(e).append(r),this.model.excludeSelector!=null&&this._removeContentFromPrint(this.model.excludeSelector),o=this.copy,this.copy.remove(),i===undefined)?(this._printContent(o,null,!1),!0):o},r.prototype._getjQueryObject=function(n){return t(n).clone().length===0?t("<div />").html(n):t(n).clone()},r.prototype._addglobalStyles=function(){this.model.globalStyles&&(this.styles=t("style, link, meta, title"))},r.prototype._addStylesToElement=function(n){var r,i,u;if(this.model.externalStyles)for(r=t.isArray(n)?n:n.split(","),i=0;i<r.length;i++)u=t("<link rel='stylesheet' href='"+r[i]+"'>"),this.styles=t.merge(this.styles,u)},r.prototype._addTitleToElement=function(){this.model.title&&(this.title.length===0&&(this.title=t("<title />")),this.title.text(this.model.title))},r.prototype._removeContentFromPrint=function(n){var r,i;if(this.model.excludeSelector)for(r=t.isArray(n)?n:n.split(","),i=0;i<r.length;i++)this.copy.find(r[i]).remove()},r.prototype._printContent=function(n,i,r){var o=this,u,f,e;if(r){if(f=[],this.element.attr("id")!==undefined?f.push("#"+this.element.attr("id")):f.push("."+this.element.attr("class").replace(/ /g,".")),n!==undefined)if(t.isArray(n))for(e=0;e<n.length;e++)f.push(n[e]);else f.push(n);n=this._initialize(f,!0)}this._trigger("beforeStart",{content:n});i||(u=this.model.printInNewWindow?window.open():window.open(" ","print","height="+this.model.height+",width="+this.model.width+",tabbar=no"));u.document.write(this.model.docType);u.document.write(n[0].outerHTML);u.document.close();u.focus();setTimeout(function(){ej.isNullOrUndefined(u.window)||(u.print(),setTimeout(function(){u.close()},o.model.timeOutPeriod))},this.model.timeOutPeriod)},r.prototype.print=function(n,t){this._printContent(n,t,!0)},r}(ej.WidgetBase);window.ej.widget("ejPrint","ej.Print",new i)})(jQuery)});
