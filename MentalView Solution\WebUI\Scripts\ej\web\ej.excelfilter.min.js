/*!
*  filename: ej.excelfilter.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./ej.menu.min"],n):n()})(function(){(function(n,t,i){t.ExcelFilter=t.ExcelFilter||{};t.excelFilter=function(n){return t.loadLocale("ejExcelFilter"),this._ctrlInstance=n.instance,this.id=this._ctrlInstance._id,this._dialogContainer=null,this._showSort=n.showSortOptions||!1,this._interDeterminateState=n.interDeterminateState||!1,this._maxCount=t.isNullOrUndefined(n.maxFilterLimit)?1e3:n.maxFilterLimit,this._formatFiltering=!0,this._locale=this._ctrlInstance.model.locale||"en-US",this.localizedLabels=this._getLocalizedLabel(),this._filterHandler=n.filterHandler||null,this._searchHandler=this._ctrlInstance.model.searchSettings||null,this._cancelHandler=n.cancelHandler||null,this._customFilterHandler=n.customFilterHandler||null,this._cssClass=n.cssClass||null,this._matchCase=n.allowCaseSensitive||!1,this._accent=n.allowAccent||!1,this._title=n.title||this.localizedLabels.title,this._complexBlankCriteria=n.enableComplexBlankFilter,this._blankValue=n.blankValue,this.fName=n.initFilterCol||null,this._spliter=n.valueDelimiter||t.ExcelFilter.valueDelimiter,this._initialFName=this.fName,this._displayName=null,this._dataSource=null,this._isUrlAdaptor=!1,this._$tableID=null,this._$blankVal=null,this._$selectedColors=[],this._$enableColor=!1,this._$filteredByColor="",this._$colType=null,this._$key=0,this.filteredColumn=null,this.sortedColumns=null,this._chkList=null,this._listsWrap=null,this._menuWrap=null,this._localJSON=null,this._actualCount=0,this._totalRcrd=0,this._enableResponsiveRow=!1,this._searchCount=0,this._currentData=null,this._openedFltr=null,this._predicates=[],this.cFilteredCols=this.fName!=null?[this.fName]:[],this._columnsFiltered=[],this.guid=t.getGuid("excelfilter"),this._noDlg=[],this._sepAftr=["sortDesc","notequal","between","top10","endswith","contains"],this._posType=["number","date","datetime","string","boolean","guid"],this._empties=this._complexBlankCriteria?["null","undefined",""]:[this.guid],this._reqInProgess=!1,this._isFiltered=!1,this._onActionBegin=n.actionBegin||null,this._onActionComplete=n.actionComplete||null,this.maxItemOnQuery=0,this.enableNormalize=!0,this.enableSelect=!1,this._onDemandSearch=!1,this._maxFilterCount=!1,this._clearSearchValue=!1,this._islargeData=!1,this._checkedValue=[],this._searchRequest=!1,this._isIndeterminate=!1,this._selectAll="<div class='e-ftrchk'><input type='checkbox' class='e-selectall' value='selectall' class='e-ftrchk' /><label class='e-ftrchk'>("+this.localizedLabels.SelectAll+")<\/label><\/div>",this._blanks="<div class='e-ftrchk'><input type='checkbox' id='blanks' class='e-ftrchk' value='"+this._empties.join(this._spliter)+"' @@/><label class='e-ftrchk' for='blanks' value=''>("+this.localizedLabels.Blanks+")<\/label><\/div>",this._blank=i,this._addAtLast=!1,this._addToFilter="<div class='e-ftrchk'><input type='checkbox' class='e-addtofilter'/><label class='e-ftrchk'>"+this.localizedLabels.AddToFilter+"<\/label><\/div>",this._preChkList=[],this._checked=null,this._add=null,this.guidMenuOpt=[{id:1,text:this.localizedLabels.SortNoSmaller,sprite:"e-sortasc e-icon e-fnsort",htmlAttribute:{ejfnrole:"sortAsc"}},{id:2,text:this.localizedLabels.SortNoLarger,sprite:"e-sortdesc e-icon e-fnsort",htmlAttribute:{ejfnrole:"sortDesc"}},{id:3,text:this.localizedLabels.SortByColor,htmlAttribute:{ejfnrole:"popup"},child:[]},{id:4,text:this.localizedLabels.ClearFilter,sprite:"e-filternone e-icon",htmlAttribute:{ejfnrole:"clearfilter"}},{id:5,text:this.localizedLabels.FilterByColor,htmlAttribute:{ejfnrole:"popup"},child:[]},{id:6,text:this.localizedLabels.GuidFilter,htmlAttribute:{ejfnrole:"filterpopup"},child:this.localizedLabels.GuidMenuOptions}],this.numberMenuOpt=this.booleanMenuOpt=[{id:1,text:this.localizedLabels.SortNoSmaller,sprite:"e-sortasc e-icon e-fnsort",htmlAttribute:{ejfnrole:"sortAsc"}},{id:2,text:this.localizedLabels.SortNoLarger,sprite:"e-sortdesc e-icon e-fnsort",htmlAttribute:{ejfnrole:"sortDesc"}},{id:3,text:this.localizedLabels.SortByColor,htmlAttribute:{ejfnrole:"popup"},child:[]},{id:4,text:this.localizedLabels.ClearFilter,sprite:"e-filternone e-icon",htmlAttribute:{ejfnrole:"clearfilter"}},{id:5,text:this.localizedLabels.FilterByColor,htmlAttribute:{ejfnrole:"popup"},child:[]},{id:6,text:this.localizedLabels.NumberFilter,htmlAttribute:{ejfnrole:"filterpopup"},child:this.localizedLabels.NumberMenuOptions}],this.stringMenuOpt=[{id:1,text:this.localizedLabels.SortTextAscending,sprite:"e-sortasc e-icon e-fnsort",htmlAttribute:{ejfnrole:"sortAsc"}},{id:2,text:this.localizedLabels.SortTextDescending,sprite:"e-sortdesc e-icon e-fnsort",htmlAttribute:{ejfnrole:"sortDesc"}},{id:3,text:this.localizedLabels.SortByColor,htmlAttribute:{ejfnrole:"popup"},child:[]},{id:4,text:this.localizedLabels.ClearFilter,sprite:"e-filternone e-icon",htmlAttribute:{ejfnrole:"clearfilter"}},{id:5,text:this.localizedLabels.FilterByColor,htmlAttribute:{ejfnrole:"popup"},child:[]},{id:6,text:this.localizedLabels.TextFilter,htmlAttribute:{ejfnrole:"filterpopup"},child:this.localizedLabels.StringMenuOptions}],this.dateMenuOpt=[{id:1,text:this.localizedLabels.SortDateOldest,sprite:"e-sortasc e-icon e-fnsort",htmlAttribute:{ejfnrole:"sortAsc"}},{id:2,text:this.localizedLabels.SortDateNewest,sprite:"e-sortdesc e-icon e-fnsort",htmlAttribute:{ejfnrole:"sortDesc"}},{id:3,text:this.localizedLabels.SortByColor,htmlAttribute:{ejfnrole:"popup"},child:[]},{id:4,text:this.localizedLabels.ClearFilter,sprite:"e-filternone e-icon",htmlAttribute:{ejfnrole:"clearfilter"}},{id:5,text:this.localizedLabels.FilterByColor,htmlAttribute:{ejfnrole:"popup"},child:[]},{id:6,text:this.localizedLabels.DateFilter,htmlAttribute:{ejfnrole:"filterpopup"},child:this.localizedLabels.DateMenuOptions}],this.datetimeMenuOpt=[{id:1,text:this.localizedLabels.SortDateOldest,sprite:"e-sortasc e-icon e-fnsort",htmlAttribute:{ejfnrole:"sortAsc"}},{id:2,text:this.localizedLabels.SortDateNewest,sprite:"e-sortdesc e-icon e-fnsort",htmlAttribute:{ejfnrole:"sortDesc"}},{id:3,text:this.localizedLabels.SortByColor,htmlAttribute:{ejfnrole:"popup"},child:[]},{id:4,text:this.localizedLabels.ClearFilter,sprite:"e-filternone e-icon",htmlAttribute:{ejfnrole:"clearfilter"}},{id:5,text:this.localizedLabels.FilterByColor,htmlAttribute:{ejfnrole:"popup"},child:[]},{id:6,text:this.localizedLabels.DateTimeFilter,htmlAttribute:{ejfnrole:"filterpopup"},child:this.localizedLabels.DatetimeMenuOptions}],this};t.excelFilter.prototype={isNotBlank:function(n,i){var r=t.isNullOrUndefined(n)||n===""||n===null;return i||n!==""&&n!==null||(this._addAtLast=!0),!r},_checkBlank:function(n){if(this.isNotBlank(n))return!0;var t=this._blank==i&&!this._addAtLast;return t&&(this._blank=!0),t},_getValueData:function(n,i){var r=this._empties,u,f;if(this.isNotBlank(n,!0)&&(r=t.distinct(i,this._$foreignKey||this.fName,!1),!(t.browserInfo().name=="msie"&&t.browserInfo().version=="8.0")))for(u=0;u<r.length;u++)r[u]instanceof Date&&(f={dateString:r[u]},r[u]=JSON.parse(JSON.stringify(f)).dateString);return r.join(this._spliter)},getPredicate:function(n,r,u){var e,s,o,f,h;for(this._isUrlAdaptor=this._ctrlInstance._dataSource()instanceof t.DataManager&&(this._ctrlInstance._dataSource().adaptor instanceof t.UrlAdaptor||this._ctrlInstance._dataSource().adaptor instanceof t.WebMethodAdaptor),e=r!=i?[r]:t.distinct(n,"field",!1),o={},f=0,h=e.length;f<h;f++)s=new t.DataManager(n).executeLocal((new t.Query).where("field","equal",e[f])),o[e[f]]=this.generatePredicate(s),u&&(this._predicates[this._$key]=this._predicates[this._$key]||{},this._predicates[this._$key][e[f]]=o[e[f]]);return o},generatePredicate:function(n){var f=n?n.length:0,u,r,i;if(f){for(r=this._updateDateFilter(n[0]),u=this._isUrlAdaptor&&(r.type=="date"||r.type=="datetime")?this._getDatePredicate(r):r.ejpredicate?r.ejpredicate:t.Predicate(r.field,r.operator,r.value,r.ignoreCase||!r.matchcase,r.ignoreAccent||!r.accent),i=1;i<f;i++)n[i]=this._updateDateFilter(n[i]),this._isUrlAdaptor&&f>2&&i>1&&n[i].predicate=="or"?n[i].type=="date"||n[i].type=="datetime"?u.predicates.push(this._getDatePredicate(n[i])):u.predicates.push(t.Predicate(n[i].field,n[i].operator,n[i].value,n[i].ignoreCase||!n[i].matchcase,n[i].ignoreAccent||!n[i].accent)):u=this._isUrlAdaptor&&(n[i].type=="date"||n[i].type=="datetime")?u[n[i].predicate](this._getDatePredicate(n[i])):n[i].ejpredicate?u[n[i].predicate](n[i].ejpredicate):u[n[i].predicate](n[i].field,n[i].operator,n[i].value,n[i].ignoreCase||!n[i].matchcase,n[i].ignoreAccent||!n[i].accent);return u||null}},_getDatePredicate:function(n){return t.Predicate(n.field,n.operator,n.value,n.ignoreCase||!n.matchcase,n.ignoreAccent||!n.accent)},getFilterFrom:function(n,i){var i=t.distinct(i,this.fName,!1);if(this.maxItemOnQuery>0&&(i=i.slice(0,this.maxItemOnQuery)),i.length==1){var r=this._$foreignKeyType=="string"?"startswith":"equal",u=this._$foreignKeyType=="string"?this._matchCase:!0,f=this._$foreignKeyType=="string"?this._accent:!0;return t.Predicate(this.fName,r,i[0],u,f)}return t.UrlAdaptor.prototype.getFiltersFrom(i,(new t.Query).foreignKey(this._$foreignKey))},renderDialog:function(n){this._$colType=n;var e=this.id+n+"_excelDlg",i=t.buildTag("div#"+e+".e-excelfilter e-js e-dlgcontainer e-shadow"),r=t.buildTag("ul#"+this.id+n+"_MenuItem"),o=this._getMenuData(n);r=this._createLiTag(r,o,!1);var f=t.buildTag("span.e-searchbox e-fields").append(t.buildTag("input#"+this.id+n+"_SearchBox.e-ejinputtext e-searchinput",{},{},{type:"text",placeholder:this.localizedLabels.Search})).append(t.buildTag("span.e-search e-icon")),s=t.buildTag("div#"+this.id+n+"_CheckBoxList.e-checkboxlist e-fields").append(t.buildTag("div")),h=this._createBtn(),u=t.buildTag("div.e-searchcontainer"),c=t.buildTag("div.e-status e-fields e-hide",this.localizedLabels.CheckBoxStatusMsg);i.append(r);u.append(f);u.append(c);u.append(s);u.append(h);i.append(u);i.appendTo(this._ctrlInstance.element);i.css("display","none");this._renderCustomFDlg(n);this._dialogContainer=i;this._cssClass!=null&&i.addClass(this._cssClass);this._showSort||(r.find(".e-fnsort").closest("li").css("display","none"),r.find("li.e-separator:first").css("display","none"));this._lsitBoxTemplate();this._renderSubCtrls(n);t.browserInfo().name=="msie"&&t.browserInfo().version<10&&t.ieClearRemover(f.find("input")[0]);this._wireEvents()},_getDeprecatedLocalizedLabel:function(n){if(["Ok","OK"].indexOf(n)!=-1)return this.localizedLabels.Ok||this.localizedLabels.OK},_renderSubCtrls:function(i){n("#"+this.id+i+"_MenuItem").ejMenu({orientation:"vertical",width:266,container:"#"+this.id,click:t.proxy(this._menuHandler,this),enableRTL:this._ctrlInstance.model.enableRTL,enableSeparator:!1});n("#"+this.id+i+"_OkBtn").ejButton({text:this._getDeprecatedLocalizedLabel("OK"),showRoundedCorner:!0,width:60,click:t.proxy(this._fltrBtnHandler,this),enabled:!0});n("#"+this.id+i+"_CancelBtn").ejButton({text:this.localizedLabels.Cancel,showRoundedCorner:!0,width:60,click:t.proxy(this.closeXFDialog,this)});n("#"+this.id+i+"_CheckBoxList").ejScroller({height:130,width:234,scroll:t.proxy(this._virtualize,this)});n("#"+this.id+i+"_CheckBoxList").ejWaitingPopup({showOnInit:!1})},openXFDialog:function(r){var e,u,f;(this.fName=r.field,this._dataSource=r.dataSource,this._$colType=r.type||"string",this._$format=r.format||"",this._enableResponsiveRow=r.enableResponsiveRow,this.filteredColumn=r.filteredColumns||this._ctrlInstance.model.filterSettings.filteredColumns,this.sortedColumns=r.sortedColumns||this._ctrlInstance.model.sortSettings.sortedColumns,this._displayName=r.displayName,this.query=r.query||new t.Query,this._$key=r.key||0,this._$tableID=r.tableID,this._$blankVal=t.isNullOrUndefined(this._$tableID)?this._$blankVal:r.blank,this._$selectedColors=r.selectedColors||[],this._$enableColor=r.enableColor||!1,this._$filteredByColor=r.filteredByColor||this._$filteredByColor,this._$foreignField=r.foreignKeyValue,this._$foreignData=r.foreignDataSource,this._$foreignKey=r.foreignKey,this._$foreignKeyType=r.foreignKeyType,this._$foreignData instanceof t.DataManager&&(this._$foreignData.adaptor instanceof t.ODataAdaptor||this._$foreignData.adaptor instanceof t.ODataV4Adaptor||this._$foreignData.adaptor instanceof t.WebApiAdaptor)&&(this.maxItemOnQuery=50),n.extend(this.localizedLabels,r.localizedStrings||{}),f={requestType:"filterbeforeopen",filterModel:this,columnName:this.fName,columnType:this._$colType},this._ctrlInstance._trigger(this._onActionBegin,f))||(this._openedFltr!=null&&this._openedFltr.is(n("#"+this.id+this._$colType+"_excelDlg"))||(this.closeXFDialog(),this._openedFltr=n("#"+this.id+this._$colType+"_excelDlg")),this._listsWrap=n("#"+this.id+this._$colType+"_CheckBoxList"),this._menuWrap=n("#"+this.id+this._$colType+"_MenuItem"),this._searchBox=this._openedFltr.find(".e-searchbox input"),this._setPosition(this._openedFltr,r.position),this._openedFltr.addClass(r.cssClass),this._openedFltr.fadeIn(300,function(){}),u=n("#"+this.id+this._$colType+"_MenuItem").find("li[ejfnrole='popup']"),this._$enableColor?(this._createDivTag(u.eq(0).find(".e-shadow"),this._$selectedColors,!1,"sort"),this._$filteredByColor==-1||this._$filteredByColor.length<1||this._$filteredByColor===this.fName?this._createDivTag(u.eq(1).find(".e-shadow"),this._$selectedColors,!1,"filter"):u.eq(1).addClass("e-disable-item")):u.hide(),this._isFiltered=this._predicates[this._$key]!=i&&this._predicates[this._$key][this.fName]!=i,this._isFiltered=r.isFiltered||this._isFiltered,(this._$colType=="date"||this._$colType=="datetime")&&this._$format==""&&(this._$format=this._$colType=="date"?"{0:MM/dd/yyyy}":"{0:MM/dd/yyyy hh:mm:ss}"),this._processListData(),e=this._listsWrap.data("ejScroller"),this._setDisable(),f={requestType:"filterafteropen",filterModel:this,columnName:this.fName,columnType:this._$colType},this._ctrlInstance._trigger(this._onActionComplete,f))},closeXFDialog:function(i){if(i!=null){var r=n(i.target);if(!(r.closest("#"+this.id+this._$colType+"_CustomFDlg").length>0||r.closest("#"+this.id+this._$colType+"_excelDlg").length>0))return}this._openedFltr&&(this._openedFltr.hasClass("e-dlgcustom")?this._openedFltr.ejDialog("close"):(this._openedFltr.fadeOut(300,function(){}),this._listsWrap.ejWaitingPopup("hide")),t.isNullOrUndefined(this._cancelHandler)||this._cancelHandler(),this.resetFilterModel(),this._ctrlInstance._$fDlgIsOpen=!1)},_setPosition:function(n,t){n.css("position","absolute");n.css("left",t.X).css("top",t.Y)},_setDisable:function(){var o=this._menuWrap.find("li[ejfnrole='clearfilter']"),r=this._menuWrap.find("li[ejfnrole='filterpopup']"),e=this._menuWrap.find("li[ejfnrole *= 'sort']"),s=this._listsWrap.find("input").filter(":checked:not(.e-selectall)").length,f,u,i;if(this._isFiltered?o.removeClass("e-disable-item"):o.addClass("e-disable-item"),this._showSort&&(f=t.DataManager(this.sortedColumns).executeLocal((new t.Query).where("field","equal",this.fName)),f.length&&f[0].direction=="ascending"?e.filter("[ejfnrole='sortAsc']").addClass("e-disable-item"):e.filter("[ejfnrole='sortAsc']").removeClass("e-disable-item"),f.length&&f[0].direction=="descending"?e.filter("[ejfnrole='sortDesc']").addClass("e-disable-item"):e.filter("[ejfnrole ='sortDesc']").removeClass("e-disable-item")),u=r.find(".aschild"),this.cFilteredCols.length!=0&&n.inArray(this.fName,this.cFilteredCols)!=-1){for(i=0;i<this.filteredColumn.length;i++)if(this.filteredColumn[i].field==this.fName){u.find("#ejFiltercheck").length==0&&(u.append("<input type='checkbox' id='ejFiltercheck' />"),u.find("#ejFiltercheck").ejCheckBox({checked:!0}),u.find("#ejFiltercheck").ejCheckBox("disable"));r.find(".e-shadow .e-exceltick").length>0&&r.find(".e-shadow .e-exceltick").remove();this.filteredColumn[i].field==this.fName&&!t.isNullOrUndefined(this.filteredColumn[i+1])&&this.filteredColumn[i+1].field==this.fName&&(this._$foreignField&&this.filteredColumn[i+1].customFilter||!this._$foreignField)?this.filteredColumn[i].operator=="greaterthanorequal"&&this.filteredColumn[i+1].operator=="lessthanorequal"?r.find(".e-shadow .e-list[ejvalue=between]").find("a").append("<span class='e-exceltick e-icon' />"):r.find(".e-shadow .e-list[ejvalue=customfilter]").find("a").append("<span class='e-exceltick e-icon' />"):this._$foreignField?r.find(".e-shadow .e-list[ejvalue="+this.filteredColumn[i].actualFilterOperator+"]").find("a").append("<span class='e-exceltick e-icon' />"):r.find(".e-shadow .e-list[ejvalue="+this.filteredColumn[i].operator+"]").find("a").append("<span class='e-exceltick e-icon' />");break}}else for(i=0;i<this.filteredColumn.length;i++)if(this.filteredColumn[i].field==this.fName){this._removeTick(r,u);break}this._isFiltered||u.find("#ejFiltercheck").length==0||this._removeTick(r,u);this._$colType=="boolean"&&this._menuWrap.find("li[aria-haspopup=true]").addClass("e-hide");this._searchBox.val("");this._searchBox.siblings().addClass("e-search").removeClass("e-cancel")},_removeTick:function(n,t){t.find("#ejFiltercheck").ejCheckBox("destroy");n.find(".aschild #ejFiltercheck").remove();n.find(".e-shadow .e-exceltick.e-icon").remove()},_createBtn:function(n){var i=n?this.id+this._$colType+"Custom":this.id+this._$colType,u=t.buildTag("div.e-btncontainer e-fields"),r=t.buildTag("div");return r.append(t.buildTag("input#"+i+"_OkBtn.e-fltrbtn e-btnsub e-flat",{},{},{type:"button"})).append(t.buildTag("input#"+i+"_CancelBtn.e-fltrbtn e-btncan e-flat",{},{},{type:"button"})),u.append(r)},_menuHandler:function(t){var s=n(t.element),i=s.attr("ejfnrole"),r={},h,c,f,e,o,u;if(i==="filterbgcolor"||i==="filterfgcolor")h={field:this.fName,operation:i,color:n(t.element).css("background-color")},r={originalEvent:t.event,action:"filterbycolor",filterDetails:h,tableID:this._$tableID},this._isFiltered=!1;else if(i=="clearfilter")c={field:this.fName,operator:"",value:"",predicate:"or"},r={originalEvent:t.event,fieldName:this.fName,action:"clearfiltering",filterDetails:c,tableID:this._$tableID},f=n.inArray(this.fName,this.cFilteredCols),f!=-1&&this.cFilteredCols.splice(f,1),e=n.inArray(this.fName,this._columnsFiltered),e!=-1&&this._columnsFiltered.splice(e,1),this._initialFName==this.fName&&(this._initialFName=null);else{if(i=="popup"||i=="filterpopup")return;i=="operator"?this._openCustomFilter(s.attr("ejvalue")):i=="sortAsc"||i=="sortDesc"?(o=i=="sortAsc"?"ascending":"descending",u={field:this.fName,direction:o},r={originalEvent:t.event,action:"sorting",sortDetails:u,tableID:this._$tableID}):(u={field:this.fName,direction:o,operation:i,color:n(t.element).css("background-color")},r={originalEvent:t.event,action:"sortbycolor",sortDetails:u,tableID:this._$tableID})}i!="operator"&&(this._filterHandler(r),this.closeXFDialog())},_searchBoxFocus:function(t){var i=n(t.target);i.hasClass("e-cancel")&&(i.prev().val(""),i.next().addClass("e-cancel"),i.addClass("e-search"),i.prev().trigger("keyup"));t.type=="focusin"&&(i.next().addClass("e-cancel"),i.next().removeClass("e-search"))},_search:function(r){var e=r.target.value,u,o,f=n(r.target);(u=this.getType()!="string"&&parseFloat(e)?parseFloat(e):e,o="contains",u=u==""||u==i?i:u,this._$colType=="boolean"&&(u!=i&&this.localizedLabels.True.toLocaleLowerCase().indexOf(u.toLowerCase())!=-1?u="true":u!=i&&this.localizedLabels.False.toLocaleLowerCase().indexOf(u.toLowerCase())!=-1&&(u="false")),this._$colType!="date"&&this._$colType!="datetime"||(u=t.parseDate(e,this.replacer(this._$format,/{0:|}/g,"")),o="equal",this._previousValue!=null||u!=null))&&(this._previousValue=u,delay=this._dataSource instanceof t.DataManager&&this._ctrlInstance._gridRecordsCount>this._ctrlInstance.model.filterSettings.maxFilterChoices?1500:0,sender={type:"filterchoicesearch",value:u,operator:o,matchcase:["date","datetime"].indexOf(this._$colType)!=-1?!1:this._matchCase,accent:this._accent},this._processSearch(sender,delay),f.val()==""?(f.next().addClass("e-search"),f.next().removeClass("e-cancel")):(f.next().addClass("e-cancel"),f.next().removeClass("e-search")))},_processSearch:function(n,t){this._alreadySearchProcessed?(this._stopTimer(),this._startTimer(n,t)):(this._alreadySearchProcessed=!0,this._startTimer(n,t))},_startTimer:function(n,t){proxy=this;this._timer=window.setTimeout(function(){proxy._processListData(n)},t)},_stopTimer:function(){this._timer!=null&&window.clearTimeout(this._timer)},_getLocalizedLabel:function(){return t.getLocalizedConstants("ej.ExcelFilter",this._locale)},_getMenuData:function(n){return n!=i?this[n+"MenuOpt"]:[]},_checkHtmlEncode:function(){var n=t.isNullOrUndefined(this._ctrlInstance.getColumnByField)?!1:this._ctrlInstance.getColumnByField(this.fName).disableHtmlEncode;return!t.isNullOrUndefined(n)&&n?!0:!1},_lsitBoxTemplate:function(){var i={},u={},r;i[this.id+"isNotBlank"]=t.proxy(this.isNotBlank,this);i[this.id+"checkBlank"]=t.proxy(this._checkBlank,this);i[this.id+"_getValueData"]=t.proxy(this._getValueData,this);i[this.id+"_checkBoxState"]=t.proxy(this._setCheckState,this);i[this.id+"_htmlEncode"]=t.proxy(this._checkHtmlEncode,this);i[this.id+"_genId"]=this._genCheckID;n.views.helpers(i);r=this.id+this._$colType+"{{:~"+this.id+"_genId()}}";u[this.id+this._$colType+"_listBox_Template"]="{{if ~"+this.id+"checkBlank(key)}}<div class='e-ftrchk'><input type='checkbox' id='"+r+"' value='{{html:~"+this.id+"_getValueData(key, items)}}' class='e-ftrchk' {{:~"+this.id+"_checkBoxState(~"+this.id+"_getValueData(key, items))}}/><label class='e-ftrchk' for='"+r+"'>{{if ~"+this.id+"isNotBlank(key,'true')}} {{if ~"+this.id+"_htmlEncode(key)}} {{>key}} {{else}} {{:key}} {{/if}} {{else}} (Blanks) {{/if}}<\/label><\/div>{{/if}}";n.templates(u)},_processListData:function(r){var s,f={},u=new t.Query,nt=(new t.Query).requiresCount(),o={},y,a,p,h,w,c,k,d,g,l;this._searchCount=0;y=this._predicates[this._$key];proxy.query._expands.length&&(u._expands=proxy.query._expands);var e=null,v=null,b=null,l=null;t.isNullOrUndefined(this._$foreignField&&this._$foreignData)?(e=this._dataSource,v=this.fName,b=this._localJSON):(e=b=this._$foreignData,this._dataSource instanceof t.DataManager&&!(e instanceof t.DataManager)&&(e=t.DataManager(e)),v=this._$foreignField);for(p in y)p!=this.fName&&(h=y[p],w=h.from,w?u.skip(w=="top"?0:e.length-(e.length-h.take)).take(h.take):a=a!=i?a.and(h):h);(f.columnName=v,u.requiresCount(),this._dataSource instanceof t.DataManager&&!this._dataSource.dataSource.offline&&this._ctrlInstance._gridRecordsCount>this._ctrlInstance.model.filterSettings.maxFilterChoices&&(u.take(this.maxFilterChoices),this._onDemandSearch=!0),a&&u.where(a),t.merge(u.queries,this.query.queries)||t.merge(u._params,this.query._params),o.requestType=r?r.type:"filterchoicerequest",o.filterModel=this,o.query=u,o.dataSource=e,o.requestType=="filterchoicesearch"&&(o.queryParams=r),this._ctrlInstance._trigger(this._onActionBegin,o))||(!t.isNullOrUndefined(this._searchHandler)&&this._searchHandler.key.length&&(c=this._searchHandler,u.search(c.key,c.fields,c.operator||"contains",c.ignoreCase||!0,c.ignoreAccent||!0)),this.enableSelect&&u.select(this.fName),r&&r.type=="filterchoicesearch"?(this._clearSearchValue=t.isNullOrUndefined(r.value)?!0:!1,f.type=r.type,f.value=r.value,this._$foreignField&&(u=nt),r.value&&u.where(v,r.operator,r.value,!r.matchcase,r.accent),this._dataSource instanceof t.DataManager&&this._ctrlInstance._gridRecordsCount>this._ctrlInstance.model.filterSettings.maxFilterChoices?(this._searchRequest=!0,k=this._$colType,d=this.id+k+"_excelDlg",n("#"+d).is(":visible")&&this._listsWrap.ejWaitingPopup("show"),this._$foreignField?(g=this._$foreignData instanceof t.DataManager?this._$foreignData:t.DataManager(this._$foreignData),this._dataProcessing(g,u,f)):this._dataProcessing(this._dataSource,u,f)):this._dataProcessing(t.DataManager(this._localJSON),u,f)):this._dataSource instanceof t.DataManager?(proxy=this,f.type="filterchoicerequest",this._listsWrap.ejWaitingPopup("show"),this._reqInProgess||(this._reqInProgess=!0,this._$foreignField&&(e=this._dataSource),s=e.executeQuery(u),s.done(function(n){proxy._$foreignField?proxy._filterForeignData(n,f):(l=n.result,f.data=proxy._localJSON=proxy._currentData=l,proxy._totalRcrd=n.count,proxy._listsWrap.ejWaitingPopup("hide"),proxy._setCheckBoxList(f),proxy._reqInProgess=!1)}))):(l=[],s=t.DataManager(this._dataSource).executeLocal(u),f.type="filterchoicerequest",this._$foreignField?this._filterForeignData(s,f):(l=s.result,f.data=this._localJSON=this._currentData=l,this._totalRcrd=s.count,this._setCheckBoxList(f))))},_dataProcessing:function(n,t,i){var r;r=n.executeQuery(t);r.done(function(n){i.data=proxy._currentData=n.result;proxy._totalRcrd=proxy._searchCount=n.result.length;proxy._setCheckBoxList(i)})},_filterForeignData:function(i,r){var a=typeof r=="boolean",h=this._$foreignKey,c=this._$foreignField,b=this._$foreignKeyType,v,l,f=this,y={},e,p,o=new t.Query,k=this._$foreignData instanceof t.DataManager?this._$foreignData:t.DataManager(this._$foreignData),s,u,w;if(a){for(u=i[0],y=i[0],p=i.slice(),!u||this.closeXFDialog(),e=new t.Predicate(c,u.operator,u.value,!u.matchcase,!u.accent),s=1,w=i.length;s<w;s++)u=i[s],e=e[u.predicate](c,u.operator,u.value,!u.matchcase,!u.accent);o.where(e)}else v=i.result,l=i.count,!l||o.where(this.getFilterFrom(this._$foreignData,v)),this._listsWrap.ejWaitingPopup("model.showOnInit")||this._listsWrap.ejWaitingPopup("show");o.select([h,c]);k.executeQuery(o).done(function(i){if(a){var u=t.distinct(i.result,h,!0),u=0 in u?u:[{}],e=u.map(function(t){return n.extend({},y,{value:t[h],operator:"equal",actuals:p,type:b})});f.initiateFilter(e)}else r.data=f._localJSON=f._currentData=i.result,f._totalRcrd=l,f._listsWrap.ejWaitingPopup("hide"),f._setCheckBoxList(r),f._reqInProgess=!1})},_setCheckBoxList:function(i){var f={},r,o,e=this._blanks,u;(f.requestType=i.type,f.dataSource=this._dataSource,f.filterModel=this,this._currentData.length!=0?(r=this.getDistinct(i.data,i.columnName,!0,!!this._$foreignKey),o=this._isFiltered,this._actualCount=r.length,r.length=this._maxCount>this._actualCount?this._actualCount:this._maxCount,this._onDemandSearch&&this._actualCount==1e3||this._actualCount>=this._maxCount?this._openedFltr.find("div.e-status").removeClass("e-hide"):this._openedFltr.find("div.e-status").addClass("e-hide"),this._islargeData=this._maxCount<this._actualCount?!0:!1,this._filterdCol=t.DataManager(this.filteredColumn).executeLocal(t.Query().where("field","equal",this.fName)),i.type!="filterchoicesearch"||t.isNullOrUndefined(i.value)?this._listsWrap.find("div:first").html([this._selectAll,n.render[this.id+this._$colType+"_listBox_Template"](r),t.isNullOrUndefined(i.value)&&this._addAtLast?this.replacer(e,/@@/g,this._setCheckState,this._empties.join(this._spliter)):""].join("")):this._listsWrap.find("div:first").html([this._selectAll,this._addToFilter,n.render[this.id+this._$colType+"_listBox_Template"](r),t.isNullOrUndefined(i.value)&&this._addAtLast?this.replacer(e,/@@/g,this._setCheckState,this._empties.join(this._spliter)):""].join("")),this._chkList=this._listsWrap.find("input:checkbox").not(".e-selectall,.e-addtofilter"),$inView=this._chkList.slice(0,20),$inView.ejCheckBox({change:t.proxy(this._checkHandler,this)}),$inView.siblings().height(14).width(14),this._listsWrap.find(".e-addtofilter").ejCheckBox({change:t.proxy(this._addToFilterHandler,this)}),this._listsWrap.find(".e-addtofilter").attr("id",this.id+this._$colType+"AddToFilter"),this._listsWrap.find(".e-selectall").ejCheckBox({change:t.proxy(this._selectAllHandler,this),enableTriState:this._interDeterminateState,beforeChange:this._selectAllBeforeHandler}),this._listsWrap.find(".e-selectall").attr("id",this.id+this._$colType+"SelectAll"),this._listsWrap.find(".e-selectall,.e-addtofilter").siblings().height(14).width(14),this._listsWrap.find(".e-selectall").closest("span").siblings("label").attr("for",this.id+this._$colType+"SelectAll")):(this._listsWrap.find("div").first().html(t.buildTag("div.e-ftrchk",this.localizedLabels.NoResult,{},{})),this._chkList=this._listsWrap.find("input:checkbox").not(".e-selectall")),t.isNullOrUndefined(this._chkList)||(u=this._chkList.filter(":checked").length),this._isFiltered&&this._searchRequest&&u==0&&this._checkIsIndeterminate(i.columnName,this.filteredColumn),this._isFiltered&&this._actualCount!=u?(u>0||this._isIndeterminate&&this._interDeterminateState)&&this._listsWrap.find(".e-selectall").ejCheckBox("model.checkState","indeterminate"):this._listsWrap.find(".e-selectall").ejCheckBox({checked:!0}),n("#"+this.id+this._$colType+"_OkBtn").ejButton({enabled:u!=0}),this._listsWrap.ejScroller({scrollTop:0}).ejScroller("refresh"),this._listsWrap.hasClass("e-waitingpopup")&&this._listsWrap.ejWaitingPopup("hide"),this._ctrlInstance._trigger(this._onActionComplete,f))||(this._isIndeterminate=!1,this._checkedValue=[],i.type=="filterchoicerequest"&&(this._preChkList=this._chkList,this._checked=this._preChkList.filter(":checked")),this._add=this._listsWrap.find(".e-addtofilter").length?this._listsWrap.find(".e-addtofilter"):null)},_addToFilterHandler:function(t){n("#"+this.id+this._$colType+"_OkBtn").ejButton({enabled:t.isChecked||!!this._chkList.filter(":checked").length})},_checkIsIndeterminate:function(n,t){for(var i=0;i<t.length;i++)n==t[i].field&&(this._isIndeterminate=!0)},_createLiTag:function(r,u,f){return proxy=this,n.each(u,function(u,e){var s=t.buildTag("li",{},{},e.htmlAttribute||f&&{ejfnrole:"operator",ejvalue:e.value}||{}),h,c=f&&n.inArray(e.value,proxy._noDlg)==-1?"...":"",o=t.buildTag("a",e.text+c,{},{});e.sprite!=i&&o.append(t.buildTag("span",{},{},{"class":e.sprite}));e.child!=i&&(h=e.id!=3&&e.id!=5?proxy._createLiTag(t.buildTag("ul.e-shadow"),e.child,!0):e.id===3?proxy._createDivTag(t.buildTag("ul.e-shadow"),e.child,!0,"sort"):proxy._createDivTag(t.buildTag("ul.e-shadow"),e.child,!0,"filter"));e.child==i?r.append(s.append(o)):r.append(s.append(o).append(h));n.inArray(e.value||e.htmlAttribute&&e.htmlAttribute.ejfnrole,proxy._sepAftr)!=-1&&r.append(t.buildTag("li.e-separator"))}),r},_createDivTag:function(n,i,r,u){var e,l,s=[],h=[],c=!1,a="",v="",y="",p="",w="",b="",o,f;if(u=="sort"?(a=this.localizedLabels.SortByCellColor,v=this.localizedLabels.SortByFontColor):(a=this.localizedLabels.FilterByCellColor,v=this.localizedLabels.FilterByFontColor),y=u+"colorhdr",w=u+"fonthdr",p=u+"bgcolor",b=u+"fgcolor",n.length>0&&n.children().remove(),i.length>0){for(f=0;f<i.length;f++){for(o=0;o<s.length;o++)if(s[o]==i[f].background){c=!0;break}for(c||t.isNullOrUndefined(i[f].background)||i[f].background.startsWith("#6n")||s.push(i[f].background),c=!1,o=0;o<h.length;o++)if(h[o]==i[f].foreground){c=!0;break}c||t.isNullOrUndefined(i[f].foreground)||i[f].foreground.startsWith("#6n")||h.push(i[f].foreground);c=!1}if(s.length>0)for(e=t.buildTag("li.e-list e-bghdrcolor","","",{ejfnrole:y}),l=t.buildTag("a.e-menulink",a,{}),e.append(l),n.append(e),f=0;f<s.length;f++)e=t.buildTag("li.e-list e-valcolor",{},{"background-color":s[f]},{ejfnrole:p}),n.append(e);if(h.length>0)for(e=t.buildTag("li.e-list e-fghdrcolor","","",{ejfnrole:w}),l=t.buildTag("a.e-menulink",v,{}),e.append(l),n.append(e),f=0;f<h.length;f++)e=t.buildTag("li.e-list e-valcolor",{},{"background-color":h[f]},{ejfnrole:b}),n.append(e);s.length<1&&h.length<1?n.parent().addClass("e-disable-item"):n.parent().removeClass("e-disable-item")}else n.parent().addClass("e-disable-item");return n},_setCheckState:function(n){var i=n,s=n.split(this._spliter),h=s.length,u,c,r,l;if(!this._isFiltered||this._searchCount)return"checked";while(h--)if(i=this.processValue(s[h]),this._$colType=="date"||this._$colType=="datetime"||this._$colType=="boolean"){for(u=0,c=this._filterdCol.length;u<c;u++)if(this._$colType!="boolean"||i===this._filterdCol[u].value){if(this._$colType=="date"||this._$colType=="datetime"){var f=this._filterdCol[u].value,e=this._$colType=="date"&&i instanceof Date?this._formatting(this._$format,new Date(i.getFullYear(),i.getMonth(),i.getDate()),this._locale):this._formatting(this._$format,i,this._locale),o=this._$colType=="date"&&f instanceof Date?this._formatting(this._$format,new Date(f.getFullYear(),f.getMonth(),f.getDate()),this._locale):this._formatting(this._$format,f,this._locale);if(e===o&&this._filterdCol[u].operator=="equal"||this._maxFilterCount){if(this._maxFilterCount&&(this._onDemandSearch||this._islargeData)){if(e==o)return this._checkedValue.push(e),"";if(this._checkedValue.indexOf(o)==-1||this._checkedValue.length==this._filterdCol.length)return"checked";continue}}else continue}return"checked"}}else{if(this._$colType!="string"||t.isNullOrUndefined(i)||typeof i!="string"||this._filterdCol[0].value==i.toLowerCase()&&(i=i.toLowerCase()),r=t.DataManager(this._filterdCol).executeLocal(t.Query().where("value","equal",i,this._filterdCol[0].matchcase).where("operator","equal","equal")),r.length!=0)return r[0].operator=="equal"&&!t.isNullOrUndefined(r[0].actualFilterOperator)&&r[0].actualFilterOperator=="equal"&&r[0].isCustom==!0&&this._ctrlInstance.model.currentViewData.length>0?"checked":r[0].isCustom===!0?"":"checked";if((this._onDemandSearch||this._islargeData)&&this._maxFilterCount&&(l=t.DataManager(this._filterdCol).executeLocal(t.Query().where("value","equal",i).where("operator","notequal","equal")),l.length==0))return"checked"}},_genCheckID:function(){return"CheckBox"+this.getIndex()},_formatting:function(n,t,i){if(this._$colType=="date"&&n==""&&(n="{0:MM/dd/yyyy hh:mm:ss}"),this._$colType=="boolean")return t===""?"":this.localizedLabels[t==!0?"True":t==!1?"False":""];if(this._formatFiltering||this._$colType=="date"){var r=this._ctrlInstance.formatting;return n!=""?r(n,t,i):t}},_updateDateFilter:function(n){return n.type!="date"&&!(n.value instanceof Date)?n:(n.value=n.value instanceof Date?n.value:t.parseJSON({val:n.value}).val,["equal","notequal"].indexOf(n.operator)==-1?n:this._setDateObject(n))},_checkHandler:function(i){n("#"+i.model.id).prop("checked",i.isChecked);var e=this._listsWrap.find("input.e-ftrchk").filter(":checked:not(.e-selectall,.e-addtofilter)"),u=e.length,r=this._listsWrap.find(".e-selectall"),f;u==this._chkList.length?(r.prop("checked",!0),f=!0):u==0?(r.prop("checked",!1),f=!1):i.isInteraction&&r.ejCheckBox("model.checkState","indeterminate");r.ejCheckBox({checked:f});n("#"+this.id+this._$colType+"_OkBtn").ejButton({enabled:u!=0||!t.isNullOrUndefined(this._add)&&this._add.prop("checked")})},_selectAllBeforeHandler:function(n){n.isChecked&&(this.model.checkState=="indeterminate"?(n.cancel=!0,this.model.checkState="uncheck",this.option({checkState:"check"})):this.checkState("uncheck"))},_selectAllHandler:function(i){i.checkState=="check"?(this._chkList.filter(":not(:checked)").ejCheckBox({checked:i.isChecked,change:t.proxy(this._checkHandler,this)}),this._chkList.prop("checked",!0)):i.checkState=="uncheck"&&(this._chkList.filter(function(){if(n(this).hasClass("e-checkbox")&&n(this).prop("checked"))return this}).ejCheckBox({checked:i.isChecked}),this._chkList.prop("checked",!1),this._chkList.removeAttr("checked"));n("#"+this.id+this._$colType+"_OkBtn").ejButton({enabled:i.isChecked||!t.isNullOrUndefined(this._add)&&this._add.prop("checked")})},_renderCustomFDlg:function(i){var u=this.id+i+"_CustomFDlg",r;n("#"+u).length||(r=t.buildTag("div#"+u+".e-excelfilter e-dlgcustom"),r.addClass(this._cssClass),this._renderCDlgContent(r,i),r.ejDialog({showOnInit:!1,enableResize:!1,enableModal:!0,allowKeyboardNavigation:!1,title:this._title,width:370,content:"#"+this.id,enableRTL:this._ctrlInstance.model.enableRTL,closeIconTooltip:"Close",cssClass:"e-excelfilter e-customDlg"}),r.ejDialog("refresh"))},_renderCDlgContent:function(i,r){var k=t.buildTag("div.e-dlgfields",this.localizedLabels.Showrowswhere,{},{}),u=this.id+this._$colType,l=t.buildTag("fieldset.e-fieldset"),a=t.buildTag("tr.e-fields"),v=t.buildTag("tr.e-fields"),y=t.buildTag("tr.e-fields e-top"),f=t.buildTag("input#"+u+"_CustomDrop1"),e=t.buildTag("input#"+u+"_CustomDrop2"),d=t.buildTag("input#"+u+"_CustomDrop3"),o=t.buildTag("input#"+u+"_CustomValue1.e-ejinputtext e-excustmfltr",{},{},{type:"text"}),s=t.buildTag("input#"+u+"_CustomValue2.e-ejinputtext e-excustmfltr",{},{},{type:"text"}),g=t.buildTag("input#"+u+"_CustomValue3.e-ejinputtext e-excustmfltr",{},{},{type:"text"}),c=t.buildTag("tr.e-predicate"),p=t.buildTag("input#"+u+"_CustomAndPredicate",{},{},{type:"radio",name:"predicate",value:"and"}),w=t.buildTag("input#"+u+"_CustomOrPredicate",{},{},{type:"radio",name:"predicate",value:"or"}),b=t.buildTag("input#"+u+"_CustomMatchPredicate",{},{},{type:"checkbox"}),h,nt;i.append(k);h=t.buildTag("table.e-optable");a.append(t.buildTag("td.e-operator").append(f)).append(t.buildTag("td.e-value").append(o));c.append(t.buildTag("td",{},{},{}).append(p).append(t.buildTag("label.e-caption",this.localizedLabels.PredicateAnd,{},{"for":u+"_CustomAndPredicate"})).append(w).append(t.buildTag("label.e-caption",this.localizedLabels.PredicateOr,{},{"for":u+"_CustomOrPredicate"})));v.append(t.buildTag("td.e-operator").append(e)).append(t.buildTag("td.e-value").append(s));h.append(a).append(c).append(v);r=="string"&&c.append(t.buildTag("td",{},{},{}).append(b).append(t.buildTag("label.e-caption",this.localizedLabels.MatchCase,{},{"for":u+"_CustomMatchPredicate"})));(r=="number"||r=="guid")&&(y.append(t.buildTag("td.e-operator").append(d)).append(t.buildTag("td.e-value").append(g)),h.append(y));l.append(t.buildTag("legend")).append(h);i.append(t.buildTag("div.e-dlgfields").append(l));i.append(t.buildTag("div.e-dlgfields").append(this._createBtn(!0)));i.appendTo("body");nt=r.replace(r.charAt(0),r.charAt(0).toUpperCase());n([f,e]).ejDropDownList({fields:{text:"text",value:"value"},height:27,width:120,enableRTL:this._ctrlInstance.model.enableRTL});r=="number"?(n([f,e]).ejDropDownList({popupWidth:"170px"}),n([o,s]).ejNumericTextbox({showSpinButton:!1,height:"27px",decimalPlaces:2,width:"177px",enableRTL:this._ctrlInstance.model.enableRTL,watermarkText:this.localizedLabels.NumericTextboxWaterMark,focusOut:function(){this.model.decimalPlaces==0&&this.element.prev(".e-input").val(this.model.value)}})):r=="guid"?(n([f,e]).ejDropDownList({popupWidth:"170px"}),n(o).css({height:"22px",width:"175px"}),n(s).css({height:"22px",width:"177px"})):r=="date"?(n([f,e]).ejDropDownList({popupWidth:"170px"}),n([o,s]).ejDatePicker({cssClass:this._ctrlInstance.model.cssClass,height:"27px",width:"177px",enableRTL:this._ctrlInstance.model.enableRTL,watermarkText:this.localizedLabels.DatePickerWaterMark,locale:this._locale})):r=="datetime"?(n([f,e]).ejDropDownList({popupWidth:"170px"}),n([o,s]).ejDateTimePicker({cssClass:this._ctrlInstance.model.cssClass,height:"27px",width:"177px",enableRTL:this._ctrlInstance.model.enableRTL,watermarkText:this.localizedLabels.DateTimePickerWaterMark,locale:this._locale})):r=="string"&&n([o,s]).ejAutocomplete({cssClass:this._ctrlInstance.model.cssClass,enableRTL:this._ctrlInstance.model.enableRTL,enableDistinct:!0,width:"177px",height:"27px",locale:this._locale});n([p,w]).ejRadioButton({cssClass:this._ctrlInstance.model.cssClass,enableRTL:this._ctrlInstance.model.enableRTL});b.ejCheckBox({});n("#"+u+"Custom_OkBtn").ejButton({text:this._getDeprecatedLocalizedLabel("OK"),showRoundedCorner:!0,width:"23.6%",click:t.proxy(this._fltrBtnHandler,this),enabled:!0});n("#"+u+"Custom_CancelBtn").ejButton({text:this.localizedLabels.Cancel,showRoundedCorner:!0,width:"23.6%",click:t.proxy(this.closeXFDialog,this)})},_openCustomFilter:function(i){var e=i!="top10"?this._$colType:i,l=this.query,o=e.replace(e.charAt(0),e.charAt(0).toUpperCase()),r=this.id+this._$colType,s,f,h,c,u;(this.closeXFDialog(),this._openedFltr=n("#"+r+"_CustomFDlg"),u={requestType:"filterbeforeopen",filterModel:this,columnName:this.fName,columnType:this._$colType,isCustomFilter:!0},this._ctrlInstance._trigger(this._onActionBegin,u))||(this._openedFltr.ejDialog("open"),this._openedFltr.ejDialog({open:function(){n("#"+r+"_CustomValue1").hasClass("e-autocomplete")||n("#"+r+"_CustomValue1").hasClass("e-datepicker")||n("#"+r+"_CustomValue1").hasClass("e-datetimepicker")?n("#"+r+"_CustomValue1").focus():n("#"+r+"_CustomValue1").prev().focus()}}),this._openedFltr.find("legend").html(this._displayName),s=["Number","Date"].indexOf(o)!=-1?6:5,f=this.localizedLabels[o+"MenuOptions"].slice(0,s),f.unshift({text:"",value:""}),n("#"+r+"_CustomDrop1").ejDropDownList({dataSource:f}),n("#"+r+"_CustomDrop2").ejDropDownList({dataSource:f}),this._$colType=="number"?i=="top10"?(this._openedFltr.find(".e-optable tr").not(".e-top").addClass("e-hide"),this._openedFltr.find(".e-optable tr.e-top").removeClass("e-hide"),n("#"+r+"_CustomDrop3").ejDropDownList({dataSource:this.localizedLabels[o+"MenuOptions"]})):(this._openedFltr.find(".e-optable tr.e-top").addClass("e-hide"),this._openedFltr.find(".e-optable tr").not(".e-top").removeClass("e-hide")):(this._openedFltr.find(".e-optable tr.e-top").addClass("e-hide"),this._openedFltr.find(".e-optable tr").not(".e-top").removeClass("e-hide")),this._$colType=="string"&&(h=this._$foreignField?this._$foreignField:this.fName,c=this._$foreignData&&this._$foreignField?this._$foreignData:this._dataSource,this._openedFltr.find(".e-autocomplete").ejAutocomplete({fields:{text:h},dataSource:c,query:l,focusIn:function(){var n=this.element.closest("td").siblings().find(".e-dropdownlist").ejDropDownList("getSelectedValue"),t=this.element.closest(".e-dialog-scroller").find(".e-checkbox").prop("checked");this.model.caseSensitiveSearch=t;this.model.filterType=n==""?this.model.filterType:n}})),this._$colType=="date"&&this._$format!=""?this._openedFltr.find(".e-datepicker").ejDatePicker({dateFormat:this._$format.replace(/{0:|}/g,function(){return""}),enableStrictMode:!0}):this._$colType=="datetime"&&this._$format!=""&&this._openedFltr.find(".e-datetimepicker").ejDateTimePicker({dateTimeFormat:this._$format.replace(/{0:|}/g,function(){return""}),enableStrictMode:!0}),this._setFilteredData(r,i),t.isNullOrUndefined(this._customFilterHandler)||this._customFilterHandler(),u={requestType:"filterafteropen",filterModel:this,columnName:this.fName,columnType:this._$colType,isCustomFilter:!0},this._ctrlInstance._trigger(this._onActionComplete,u))},_setFilteredData:function(r,u){var o=n.inArray(this.fName,this.cFilteredCols),e=[],v=[],s,f,c,l,p,w;if(u!="top10"){o!=-1&&(e=t.DataManager(this.filteredColumn).executeLocal(t.Query().where("field","equal",this.fName)));o!=-1&&this._$foreignField&&(e=e[0].actuals);this._initialFName!=null&&this._initialFName==this.fName&&(e[0].isCustom=!0);s=e.length;var a=this._openedFltr.find(".e-dropdownlist"),h=this._openedFltr.find(".e-value input.e-ejinputtext.e-input"),y=this._openedFltr.find(".e-predicate");for(v=o!=-1&&s&&e[0].from!=i?[u,""]:u=="between"&&o!=-1&&s?[e[0].operator,t.isNullOrUndefined(e[1])?"":e[1].operator]:o!=-1&&s&&e[0].isCustom?[u!="customfilter"&&o!=-1?u:e[0].operator,u=="customfilter"&&e[1]?e[1].operator:""]:o==-1&&u=="customfilter"?["equal",""]:u=="between"||u=="customfilter"?u!="customfilter"?["greaterthanorequal","lessthanorequal"]:["",""]:[u,""],f=0;f<(o!=-1?s:2);f++)c=/\D*/.exec(v[f])[0],n(a[f]).prop("value",c),n(a[f]).ejDropDownList("setSelectedValue",c),n(a[f]).ejDropDownList({change:function(){this.element.closest(".e-fields").find(".e-autocomplete").val("")}}),l=o!=-1&&s&&e[f].isCustom&&(c==(this._$foreignField?e[f].actualFilterOperator:e[f].operator)||c=="customfilter"||c=="between")?this._$foreignField?c!=""?e[f].actualFilterValue:"":e[f].value:"",n(h[f]).hasClass("e-datepicker")?n("#"+h[f].id).ejDatePicker("model.value",o!=-1&&s&&e[f].isCustom?e[f].value:null):n(h[f]).hasClass("e-numerictextbox")?n("#"+h[f].id).ejNumericTextbox("model.value",l):n(h[f]).hasClass("e-datetimepicker")?n(h[f]).ejDateTimePicker("model.value",l):n(h[f]).val(l),p=o!=-1&&s&&e[f].isCustom&&e[f].predicate!=i?this._$foreignField?e[f].actualPredicate:e[f].predicate:"and",y.find("input[value="+p+"]").ejRadioButton({checked:!0}),this._$colType=="string"&&(w=o!=-1&&s&&e[f].isCustom?e[f].matchcase:this._matchCase,y.find("input.e-js[type='checkbox']").ejCheckBox({checked:w})),this._openedFltr.find(".e-value input:visible:eq(0)").select()}},_setDateObject:function(i){var f;if(i.value!=null){var s=i.value,u=n.extend(!0,{},i),r=n.extend(!0,{},i),e=new Date(u.value.setSeconds(u.value.getSeconds()-1)),o=new Date(r.value.setSeconds(r.value.getSeconds()+2));return i.value=new Date(i.value.setSeconds(r.value.getSeconds()-1)),u.value=e,r.value=o,i.operator=="equal"?(u.operator="greaterthan",u.predicate="and",r.operator="lessthan",r.predicate="and"):i.operator=="notequal"&&(u.operator="lessthanorequal",u.predicate="or",r.operator="greaterthanorequal",r.predicate="or"),f=t.Predicate(u.field,u.operator,u.value,!1),f=f[r.predicate](r.field,r.operator,r.value,!1),i.ejpredicate=f,i.type="date",i}return i},_getCDlgFields:function(){var h=this._openedFltr.find(".e-dropdownlist"),f,y=this._openedFltr.find(".e-checkbox"),c=!0,e=[],s=this._openedFltr.find(".e-value input.e-ejinputtext.e-input"),p=this._openedFltr.find(".e-value input"),w=this._openedFltr.find(".e-predicate  div[aria-checked = true]").find("input[type ='radio']").val(),r,l,t,u,a,v,o;for(n.inArray(this.fName,this.cFilteredCols)==-1&&this.cFilteredCols.push(this.fName),r=0,l=h.length;r<l;r++)if(t=n(h[r]).ejDropDownList("getSelectedValue"),this._$colType=="number"&&(u=parseFloat(s.eq(r).ejNumericTextbox("model.value"))),this._$colType=="guid"&&(u=p.eq(r).val()),this._$colType=="string"&&(u=s.eq(r).val(),c=y.is(":checked")),this._$colType=="date"&&(u=s.eq(r).ejDatePicker("model.value")),this._$colType=="datetime"&&(u=s.eq(r).ejDateTimePicker("model.value")),f={field:this.fName,predicate:r==1?w:"or",matchcase:c,isCustom:!0},t=="top"||t=="bottom")e.push(n.extend(!0,{value:"",operator:"notequal",take:u,from:t},f));else if(t!="")this._empties.indexOf(u+"")>-1||this._$colType=="number"&&isNaN(u)?(a=this.iterateAndGetCollection(this._empties.join(this._spliter),n.extend({},f,{predicate:t.toLowerCase()==="notequal"?"and":"or",operator:t.toLowerCase()})),v=this.generatePredicate(a),e.push(n.extend({},f,{ejpredicate:v,operator:t.toLowerCase()}))):(o={},n.extend(!0,o,{value:u,operator:t.toLowerCase(),isCustom:!0,actualFilterOperator:t.toLowerCase(),actualFilterValue:u,actualPredicate:f.predicate},f),this._$colType=="date"&&(o.type="date"),e.push(this._$colType=="date"&&["equal","notequal"].indexOf(t.toLowerCase())!=-1?this._setDateObject(o):o));else break;this._$foreignField==i?this.initiateFilter(e):this._filterForeignData(e,!0)},_fltrBtnHandler:function(){var i=[],s={},e="",r=[],h,c,l,o,a,u,v,y;if(this._maxFilterCount=!1,this._clearSearchValue&&(this._searchCount=0),this._openedFltr.hasClass("e-dlgcustom"))i=this._getCDlgFields(),n.inArray(this.fName,this.cFilteredCols)==-1&&i[0]&&this.cFilteredCols.push(this.fName);else{if(!this._isFiltered&&this._listsWrap.find(".e-selectall").ejCheckBox("model.checked")&&(this._searchCount==0||this._add&&this._add.prop("checked")))return this.closeXFDialog();this._onDemandSearch||this._islargeData?this._listsWrap.find("input.e-ftrchk").filter(":checked:not(.e-selectall)").length<=this._listsWrap.find("input.e-ftrchk").filter(":not(:checked):not(.e-selectall)").length||this._listsWrap.find(".e-selectall").ejCheckBox("model.checked")?(r=this._listsWrap.find("input.e-ftrchk").filter(":checked:not(.e-selectall)"),e="equal"):(r=this._listsWrap.find("input.e-ftrchk").filter(":not(:checked):not(.e-selectall)"),e="notequal",this._maxFilterCount=!0):(c=this._chkList.filter(":checked"),l=this._chkList.filter(":not(':checked')"),this._add&&this._add.prop("checked")?(l.length&&(h=l),r=this._checked.length==0?this._preChkList:this._checked,c.length&&this._checked.length!=0&&t.merge(r,c)):r=this._listsWrap.find("input.e-ftrchk").filter(":checked:not(.e-selectall,.e-addtofilter)"),e=this._colType=="string"?"startswith":"equal");var d=r.length,f,p=this.getType(),w=this._colType=="string"?this._matchCase:!0,b=this._colType=="string"?this._accent:!0,k=this._maxFilterCount?{field:this.fName,predicate:"and",operator:e,matchcase:w,accent:b}:{field:this.fName,predicate:"or",operator:e,matchcase:w,accent:b};for(o=0;o<d;o++){if(!t.isNullOrUndefined(h))for(u=0;u<h.length;u++)if(a=0,r[o].value==h[u].value){a=1;break}if(!a){if(f=r[o].value,this.enableNormalize&&f.indexOf(this._spliter)!=-1){t.merge(i,this.iterateAndGetCollection(f,k));continue}for(f=this.processValue(f,p),n.extend(!0,s,{value:f},k),u=0;u<i.length;u++)if(v=0,i[u].value==f){v=1;break}v||(i.push(p=="date"?this._setDateObject(s):s),s={})}}y=n.inArray(this.fName,this.cFilteredCols);y!=-1&&this.cFilteredCols.splice(y,1);this._isFiltered&&this._searchRequest&&(this._checkIsIndeterminate(this.fName,this.filteredColumn),this._isIndeterminate&&(t.merge(i,this.filteredColumn),i=t.distinct(i,"value",!0),this._searchRequest=!1));this.initiateFilter(i)}},initiateFilter:function(r){var f=r[0],e,u,o;if(!t.isNullOrUndefined(f)){for(isTake=f.from,e=f.ejpredicate?f.ejpredicate:t.Predicate(f.field,f.operator,f.value,!f.matchcase,!f.accent),u=1,o=r.length;u<o;u++)e=r[u].ejpredicate!=i?e[r[u].predicate](r[u].ejpredicate):e[r[u].predicate](r[u].field,r[u].operator,r[u].value,!r[u].matchcase,!r[u].accent);arg={action:"filtering",filterCollection:r,fieldName:this.fName,ejpredicate:e,tableID:this._$tableID};this._predicates[this._$key]==i&&(this._predicates[this._$key]={});this._predicates[this._$key][this.fName]=isTake?{from:f.from,take:f.take}:e;!this._openedFltr.hasClass("e-dlgcustom")&&this._listsWrap.find(".e-selectall").ejCheckBox("model.checked")&&(!this._searchCount||this._add&&this._add.prop("checked")&&(this._preChkList.filter(":not(':checked')").length==0||this._checked.length==0)||r.length==this._preChkList.length)&&n.inArray(this.fName,this._columnsFiltered)!=-1&&(arg={action:"clearfiltering",filterDetails:{field:this.fName,operator:"",predicate:"or",value:""},fieldName:this.fName,tableID:this._$tableID});n.inArray(this.fName,this._columnsFiltered)==-1&&this._columnsFiltered.push(this.fName);this._filterHandler(arg)}this.closeXFDialog()},getDistinct:function(n,i,r,u){for(var s={},h=n.length,c=[],e,f,o;h--;)e=n[h],f=t.getObject(i,e),o=f,t.isNullOrUndefined(e)||(this.enableNormalize&&(o=this._formatting(this._$format,f,this._locale)),e.ejvalue=o,!u&&f in s||c.push(r?e:f),s[f]=!0);return t.group(t.mergeSort(c,i),"ejvalue")},iterateAndGetCollection:function(t,i){var r=t.split(this._spliter),e=r.length,o=[],s,u=this._ctrlInstance._$colType,f;for((u=="number"||u=="date"||u=="datetime")&&(f=r.indexOf(""),f>-1&&r.splice(f,1));e--;)s=this.processValue(r[e]),o.push(n.extend(!0,{value:s},i));return o},processValue:function(n,r){r=r||this.getType();r=this._empties.indexOf(n)!=-1?"empty":r;n=n===this.guid?this._blankValue+"":n;switch(r){case"empty":n=n=="null"?null:n=="undefined"?i:"";break;case"date":case"datetime":n=new Date(n);break;case"number":n=+n;break;case"boolean":n=!isNaN(n)&&typeof n=="string"?t.parseInt(n)!=0:n==="true"?!0:!1}return n},getType:function(){return t.isNullOrUndefined(this._$foreignField)?this._$colType:this._$foreignKeyType},replacer:function(n,t,i,r){return typeof i=="function"&&(i=i.call(this,r)),n.replace(t,i)},_virtualize:function(i){var f=n("#"+this.id+this._$colType+"_CheckBoxList").height(),r=this._chkList.not(".e-checkbox").filter(function(){if(this.offsetTop>i.scrollTop-this.offsetHeight&&i.scrollTop+f+70>this.offsetTop+this.offsetHeight)return this}),u;r.length!=0&&(r.filter(":checked").ejCheckBox({checked:!0}),r.filter(":not(:checked)").ejCheckBox({checked:!1}),r.ejCheckBox({change:t.proxy(this._checkHandler,this)}),u=this._listsWrap.ejScroller("instance"),u.refresh())},resetFilterModel:function(){this._blank=i;this._addAtLast=!1;this._isFiltered=!1;this._searchCount=0},resetExcelFilter:function(){var u,r,e=this.id,f,t,i;for(this._predicates=[],this.cFilteredCols=[],this.resetFilterModel(),u=0,f=this._posType.length;u<f;u++)r=this._posType[u],t=e+r,i=n("#"+t+"_CustomFDlg"),i.length||(n("#"+t+"_CustomDrop1_popup_wrapper").remove(),n("#"+t+"_CustomDrop2_popup_wrapper").remove()),n("#"+t+"_CheckBoxList").length||n("#"+t+"_CheckBoxList_WaitingPopup").remove(),n("#"+t+"_CheckBoxList").ejWaitingPopup("destroy"),n("#"+t+"_excelDlg").remove(),i.find(".e-dropdownlist").ejDropDownList("destroy"),i.find(".e-button").ejButton("destroy"),(r=="string"||r=="boolean")&&i.find(".e-autocomplete").ejAutocomplete("destroy"),r=="number"&&i.find(".e-numerictextbox").ejNumericTextbox("destroy"),r=="date"&&i.find(".e-datepicker").ejDatePicker("destroy"),i.ejDialog("destroy"),n("#"+t+"_CustomFDlg").remove()},_wireEvents:function(){this._ctrlInstance._on(this._dialogContainer,"focus click",".e-searchbox",t.proxy(this._searchBoxFocus,this));this._ctrlInstance._on(this._dialogContainer,"keyup",".e-searchbox input",t.proxy(this._search,this))}};t.ExcelFilter.valueDelimiter="@|@";t.ExcelFilter.Locale=t.ExcelFilter.Locale||{};t.ExcelFilter.Locale["default"]=t.ExcelFilter.Locale["en-US"]={SortNoSmaller:"Sort Smallest to Largest",SortNoLarger:"Sort Largest to Smallest",SortTextAscending:"Sort A to Z",SortTextDescending:"Sort Z to A",SortDateOldest:"Sort by Oldest",SortDateNewest:"Sort by Newest",SortByColor:"Sort By Color",SortByCellColor:"Sort by Cell Color",SortByFontColor:"Sort by Font Color",FilterByColor:"Filter By Color",CustomSort:"Custom Sort",FilterByCellColor:"Filter by Cell Color",FilterByFontColor:"Filter by Font Color",ClearFilter:"Clear Filter",NumberFilter:"Number Filters",GuidFilter:"Guid Filters",TextFilter:"Text Filters",DateFilter:"Date Filters",DateTimeFilter:"Date Time Filters",SelectAll:"Select All",Blanks:"Blanks",Search:"Search",Showrowswhere:"Show rows where",NumericTextboxWaterMark:"Enter value",StringMenuOptions:[{text:"Equal",value:"equal"},{text:"Not Equal",value:"notequal"},{text:"Starts With",value:"startswith"},{text:"Ends With",value:"endswith"},{text:"Contains",value:"contains"},{text:"Custom Filter",value:"customfilter"}],NumberMenuOptions:[{text:"Equal",value:"equal"},{text:"Not Equal",value:"notequal"},{text:"Less Than",value:"lessthan"},{text:"Less Than Or Equal",value:"lessthanorequal"},{text:"Greater Than",value:"greaterthan"},{text:"Greater Than Or Equal",value:"greaterthanorequal"},{text:"Between",value:"between"},{text:"Custom Filter",value:"customfilter"}],GuidMenuOptions:[{text:"Equal",value:"equal"},{text:"Not Equal",value:"notequal"},{text:"Custom Filter",value:"customfilter"}],DateMenuOptions:[{text:"Equal",value:"equal"},{text:"Not Equal",value:"notequal"},{text:"Less Than",value:"lessthan"},{text:"Less Than Or Equal",value:"lessthanorequal"},{text:"Greater Than",value:"greaterthan"},{text:"Greater Than Or Equal",value:"greaterthanorequal"},{text:"Between",value:"between"},{text:"Custom Filter",value:"customfilter"}],DatetimeMenuOptions:[{text:"Equal",value:"equal"},{text:"Not Equal",value:"notequal"},{text:"Less Than",value:"lessthan"},{text:"Less Than Or Equal",value:"lessthanorequal"},{text:"Greater Than",value:"greaterthan"},{text:"Greater Than Or Equal",value:"greaterthanorequal"},{text:"Between",value:"between"},{text:"Custom Filter",value:"customfilter"}],Top10MenuOptions:[{text:"Top",value:"top"},{text:"Bottom",value:"bottom"}],title:"Custom Filter",PredicateAnd:"AND",PredicateOr:"OR",OK:"OK",MatchCase:"Match Case",Cancel:"Cancel",NoResult:"No Matches Found",CheckBoxStatusMsg:"Not all items showing",DatePickerWaterMark:"Select date",DateTimePickerWaterMark:"Select date time",True:"true",False:"false",AddToFilter:"Add current selection to filter"}})(jQuery,Syncfusion)});
