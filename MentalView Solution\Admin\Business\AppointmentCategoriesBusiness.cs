﻿using Admin;
using Admin.Business;
using Admin.Data.Model;
using Microsoft.EntityFrameworkCore;

namespace Admin.Business
{
    public class AppointmentCategoriesBusiness : IAppointmentCategoriesBusiness
    {
        private Admin.Data.Model.MentalViewContext dbContext;
        private IConfiguration configuration;
        private ILogger<AppointmentCategoriesBusiness> logger;

        public AppointmentCategoriesBusiness(Admin.Data.Model.MentalViewContext dbContext, IConfiguration configuration, ILogger<AppointmentCategoriesBusiness> logger)
        {
            this.dbContext = dbContext;
            this.logger = logger;
            this.configuration = configuration;
        }

        public async Task<List<Admin.Data.Model.AppointmentCategory>> GetAllAppointmentCategories(Int64 tenantId)
        {
            try
            {
                //Validation

                //Query
                IQueryable<Admin.Data.Model.AppointmentCategory> query = this.dbContext.AppointmentCategories.AsNoTracking().AsQueryable();
                query = query.Where(x => x.TenantId == tenantId).OrderBy(e => e.AppointmentCategoryId);

                //Διαβάζει τα δεδομένα.
                List<Admin.Data.Model.AppointmentCategory> appointmentCategories = await query.ToListAsync();

                return appointmentCategories;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task CreateAppointmentCategoriesForTenant(Int64 tenantId)
        {
            try
            {
                //Validation
                if (tenantId == null)
                {
                    throw new Exception(GlobalResources.InvalidDataMessage);
                }

                //Adds Διαγνωστική Ασύγχρονη
                AppointmentCategory appointmentCategory = new AppointmentCategory();
                appointmentCategory.TenantId = tenantId;
                appointmentCategory.CategoryName = "Διαγνωστική Ασύγχρονη";
                appointmentCategory.Color = "#179bd7";
                appointmentCategory.FontColor = "#fff";
                this.dbContext.Attach(appointmentCategory);

                //Adds Ψυχοθεραπείας
                appointmentCategory = new AppointmentCategory();
                appointmentCategory.TenantId = tenantId;
                appointmentCategory.CategoryName = "Ψυχοθεραπείας";
                appointmentCategory.Color = "#179bd7";
                appointmentCategory.FontColor = "#fff";
                this.dbContext.Attach(appointmentCategory);

                //Adds Φυσιολογίας
                appointmentCategory = new AppointmentCategory();
                appointmentCategory.TenantId = tenantId;
                appointmentCategory.CategoryName = "Φυσιολογίας";
                appointmentCategory.Color = "#179bd7";
                appointmentCategory.FontColor = "#fff";
                this.dbContext.Attach(appointmentCategory);

                //Adds Τραυματοθεραπεία
                appointmentCategory = new AppointmentCategory();
                appointmentCategory.TenantId = tenantId;
                appointmentCategory.CategoryName = "Τραυματοθεραπεία";
                appointmentCategory.Color = "#179bd7";
                appointmentCategory.FontColor = "#fff";
                this.dbContext.Attach(appointmentCategory);

                await this.dbContext.SaveChangesAsync();

                //Response
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { tenantId });
                throw;
            }
        }
    }
}
