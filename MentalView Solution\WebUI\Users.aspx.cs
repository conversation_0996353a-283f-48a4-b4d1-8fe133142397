﻿using Data;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebUI
{
    public partial class Users : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                ((Main)this.Master).ServerMessage.ButtonClicked += new ButtonClickedHandler(this.ServerMessageButtonClicked);
                ((Main)this.Master).PageTitle = GetLocalResourceObject("Page.Title").ToString();
                //Καλούμε την javascript Initialization() λόγω του UpdatePanel.
                ScriptManager.RegisterStartupScript(this, this.GetType(), "temp", "<script language='javascript'>Initialization();</script>", false);

                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                Int64 loggedEmailTemplateId = Convert.ToInt64(userData["UserId"]);
                string roleName = userData["Role"].ToString();

                if (roleName == "Guest")
                {
                    Response.StatusCode = 404;
                    Response.End();
                }

                this.usersPager.PageSize = 10;

                if (this.IsPostBack == false)
                {
                    this.FillUsersGrid();
                }
                else
                {
                    if (Request.Form["__EVENTTARGET"] == this.searchBtn.ID)
                    {
                        this.usersPager.CurrentPage = 0;
                        this.FillUsersGrid();
                    }
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void FillUsersGrid()
        {
            Data.SortDirection sortDirection = Data.SortDirection.Ascending;
            string sortExpression = "";
            int totalResults = 0;
            Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
            Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

            //ΣΗΜΕΙΩΣΗ: δεν βάζουμε το παρακάτω κώδικα να περάσει το sorting από το grid στο Stored Procedure γιατί το default sorting που κάνει το stored procedure είναι αυτό που κάνει και το grid.
            //if (this.usersGrid.SortedColumns.Count > 0)
            //{
            //    sortExpression = this.usersGrid.SortedColumns[0].Field;
            //    sortDirection = this.usersGrid.SortedColumns[0].Direction == Syncfusion.JavaScript.SortOrder.Descending ? Data.SortDirection.Descending : Data.SortDirection.Ascending;
            //}
            MentalViewDataSet ds = Data.Business.UsersBusiness.GetUsers(tenantId, this.filterTxtBox.Value, sortExpression, null, this.usersPager.CurrentPage - 1 >= 0 ? this.usersPager.CurrentPage - 1 : 0, this.usersPager.PageSize, out totalResults);

            this.usersGrid.DataSource = ds.Users;
            this.usersGrid.DataBind();

            //Ρυθμίζει το paging
            this.usersPager.TotalRecordsCount = totalResults;
            if (this.usersPager.CurrentPage == 0)
            {
                this.usersPager.CurrentPage = 1;
            }
        }

        protected void usersGrid_ServerCommandButtonClick(object sender, Syncfusion.JavaScript.Web.GridEventArgs e)
        {
            try
            {
                if (e.EventType == "commandButtonClick")
                {
                    if (e.Arguments["commandType"].ToString() == "Edit")
                    {
                        string userId = ((Dictionary<string, object>)e.Arguments["data"])["UserId"].ToString();
                        Response.Redirect("~/User.aspx?UserId=" + userId);
                    }
                    else if (e.Arguments["commandType"].ToString() == "Delete")
                    {
                        string userId = ((Dictionary<string, object>)e.Arguments["data"])["UserId"].ToString();
                        ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete", userId);
                    }
                }

                this.FillUsersGrid();
            }
            catch (ThreadAbortException exp)
            {
                //do nothing or handle as required
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void searchBtn_ServerClick(object sender, EventArgs e)
        {
            try
            {
                this.usersPager.CurrentPage = 0;
                this.FillUsersGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void usersPager_Change(object Sender, Syncfusion.JavaScript.Web.PagerChangeEventArgs e)
        {
            try
            {
                this.FillUsersGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void clearSearchBtn_ServerClick(object sender, EventArgs e)
        {
            try
            {
                this.usersPager.CurrentPage = 0;
                this.filterTxtBox.Value = "";
                this.FillUsersGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void ServerMessageButtonClicked(object sender, ButtonClickedArgs args)
        {
            try
            {
                if (args.Action == "Delete")
                {
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        Int64 userId = Int64.Parse(args.Tag);
                        Data.Business.UsersBusiness.DeleteUser(userId);
                    }
                }
                else if (args.Action == "SessionExpired")
                {
                    Response.Redirect("Default.aspx");
                }

                this.FillUsersGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void usersGrid_ServerRecordDoubleClick(object sender, Syncfusion.JavaScript.Web.GridEventArgs e)
        {
            try
            {
                if (e.EventType == "recordDoubleClick")
                {
                    string userId = ((Dictionary<string, object>)e.Arguments["data"])["UserId"].ToString();
                    Response.Redirect("~/User.aspx?UserId=" + userId, true);
                    //Server.Transfer("~/User.aspx?UserId=" + userId);
                }

                this.FillUsersGrid();
            }
            catch (ThreadAbortException exp)
            {
                //do nothing or handle as required
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }
    }
}