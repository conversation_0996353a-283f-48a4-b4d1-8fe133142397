/*!
*  filename: ej.autocomplete.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.globalize.min","./../common/ej.core.min","./../common/ej.data.min","./../common/ej.scroller.min"],n):n()})(function(){(function(n,t,i){t.widget("ejAutocomplete","ej.Autocomplete",{element:null,model:null,validTags:["input"],_ignoreOnPersist:["fields","dataSource","query","focusIn","focusOut","change","select","create","destroy","open","close","actionComplete","actionSuccess","actionFailure"],_setFirst:!1,_timeStamp:0,_rootCSS:"e-autocomplete",type:"editor",angular:{require:["?ngModel","^?form","^?ngModelOptions"]},_requiresID:!0,defaults:{dataSource:null,query:null,fields:{text:null,key:null,category:null,groupBy:null,htmlAttributes:null},locale:"en-US",name:null,template:null,allowGrouping:!1,enableDistinct:!1,ignoreAccent:!1,allowSorting:!0,sortOrder:"ascending",htmlAttributes:{},multiSelectMode:"none",delimiterChar:",",allowAddNew:!1,addNewText:"Add New",showRoundedCorner:!1,readOnly:!1,cssClass:"",watermarkText:null,value:"",selectValueByKey:null,filterType:"startswith",caseSensitiveSearch:!1,showLoadingIcon:!0,showResetIcon:!1,itemsCount:0,minCharacter:1,delaySuggestionTimeout:200,showPopupButton:!1,highlightSearch:!1,enableAutoFill:!1,enableRTL:!1,enabled:!0,visible:!0,height:"",width:"",emptyResultText:"No suggestions",animateType:"slide",showEmptyResultText:!0,enablePersistence:!1,popupHeight:"152px",popupWidth:"auto",autoFocus:!1,loadOnDemand:!1,isChangeOnBlur:!1,multiColumnSettings:{enable:!1,showHeader:!0,stringFormat:"{0}",searchColumnIndices:[],columns:[{field:null,headerText:null,textAlign:"left",headerTextAlign:"left",cssClass:"",type:"string",filterType:"startswith",visible:!0}]},validationRules:null,validationMessage:null,focusIn:null,focusOut:null,change:null,select:null,create:null,open:null,close:null,destroy:null,actionBegin:null,actionComplete:null,actionSuccess:null,actionFailure:null},dataTypes:{autoFocus:"boolean",watermarkText:"string",locale:"string",cssClass:"string",filterType:"enum",caseSensitiveSearch:"boolean",showLoadingIcon:"boolean",template:"string",allowGrouping:"boolean",enableDistinct:"boolean",allowSorting:"boolean",sortOrder:"enum",allowAddNew:"boolean",addNewText:"string",showRoundedCorner:"boolean",showResetIcon:"boolean",readOnly:"boolean",itemsCount:"number",animateType:"enum",minCharacter:"number",showPopupButton:"boolean",highlightSearch:"boolean",enableAutoFill:"boolean",enableRTL:"boolean",loadOnDemand:"boolean",multiSelectMode:"enum",delimiterChar:"string",emptyResultText:"string",showEmptyResultText:"boolean",enabled:"boolean",visible:"boolean",enablePersistence:"boolean",isChangeOnBlur:"boolean",dataSource:"data",query:"data",fields:"data",validationRules:"data",validationMessage:"data",htmlAttributes:"data",multiColumnSettings:"data",columns:"data",searchColumnIndices:"array"},observables:["value","selectValueByKey"],value:t.util.valueFunction("value"),_selectValueByKey:t.util.valueFunction("selectValueByKey"),enable:function(){this.model.enabled||(this.model.enabled=!0,this.target.disabled=!1,this.element.removeClass("e-disable").attr({"aria-disabled":!1}),this.element.removeAttr("disabled"),this.model.showPopupButton&&this.dropdownbutton.removeClass("e-disable").attr({"aria-disabled":!1}),this.model.multiSelectMode=="visualmode"&&this._ulBox.removeClass("e-disable").attr({"aria-disabled":!1}),this.wrapper.removeClass("e-disable-wrap"))},disable:function(){this.model.enabled&&(this._hideResult(),this.model.enabled=!1,this.target.disabled=!0,this.element.attr("disabled","disabled"),this.element.addClass("e-disable").attr({"aria-disabled":!0}),this.model.showPopupButton&&this.dropdownbutton.addClass("e-disable").attr({"aria-disabled":!0}),this.model.multiSelectMode=="visualmode"&&this._ulBox&&this._ulBox.addClass("e-disable").attr({"aria-disabled":!0}),this.wrapper.addClass("e-disable-wrap"))},clearText:function(){this.model.multiSelectMode=="visualmode"&&this._deleteBox(this._ulBox.children("li"));this.element.val("");this._preVal="";this._valueChange();this._isFocused||this._focusOutAction();this._hideResult()},getValue:function(){var n=this.value();return n==null?"":n},getSelectedItems:function(){return this._isFocused&&this.model.multiSelectMode!="visualmode"&&this._updateSelectedItemArray(this.getValue()),this._selectedItems},_setValue:function(i){var r,u,f;if(this._isWatermark||this._hiddenSpan.css("display","none"),(typeof i=="object"||typeof i=="number"&&isNaN(i)||n.trim(i)=="")&&(i=""),this.model.multiSelectMode=="visualmode"&&this._hiddenInput.val(""),this.element.val(""),this._hiddenInput||(this._hiddenInput=t.buildTag("input#"+this.target.id+"_hidden","",{},{type:"hidden",name:this.element.attr("name")}).insertBefore(this.element)),i)if(this.model.multiSelectMode=="visualmode")if(this._selectedItems=[],this._modelValue=i,this._deleteBox(this._ulBox.children("li")),r=i.split(this.model.delimiterChar),this.element.width(1).val("").removeAttr("placeholder"),t.DataManager&&this.model.dataSource instanceof t.DataManager)this._createBoxForObjectType(r);else if(this.suggestionListItems=this.model.dataSource,this.suggestionListItems&&typeof this.suggestionListItems[0]!="object")for(this._hiddenInput.val(i),u=0,f=r.length;u<f;u++)r[u]&&(this._ulBox.append(this._createBox(r[u])),this._selectedItems.push(r[u]));else this._createBoxForObjectType(r);else this.element.val(i),this._updateSelectedItemArray(i);return this.model.multiSelectMode!="visualmode"&&this.value(this.element.val()),this._preVal=this.element.val(),this._isWatermark||this._setWatermarkTxt(),i},_createBoxForObjectType:function(i){for(var r,u=this,f=this._declareVariable(),e=0,o=i.length;e<o;e++)r=n.trim(i[e]),r!=""&&(t.DataManager&&this.model.dataSource instanceof t.DataManager?(this._dataQuery=this._getQuery().where(f[0],"equal",r,!1),this._promise=this.model.dataSource.executeQuery(this._dataQuery),this._promise.done(function(n){var i=n.result;u._strData=i instanceof Array&&i.length?i[0]:r;t.isNullOrUndefined(u._strData[f[1]])||u._selectKeyInit();u._selectedItems.push(u._strData)}).fail(function(){u._selectedItems.push(r)})):(this._dataQuery=t.Query().where(f[0],"equal",r,!1),this._promise=t.DataManager(this.suggestionListItems).executeLocal(this._dataQuery),this._strData=this._promise instanceof Array&&this._promise.length?this._promise[0]:r,t.isNullOrUndefined(this._strData[f[1]])||this._selectKeyInit(),this._selectedItems.push(this._strData)),this.model.multiSelectMode=="visualmode"&&this._ulBox.append(this._createBox(r)))},_selectKeyInit:function(){var n=this._declareVariable(),i=this._delimiterChar(),r;this.model.multiSelectMode=="visualmode"&&this._hiddenInput.val(this._hiddenInput.val()+(t.isNullOrUndefined(this._strData[n[1]]||this._strData[n[0]])?"":(this._strData[n[1]]||this._strData[n[0]])+this.model.delimiterChar));t.isNullOrUndefined(this._strData[n[1]])||(this.model.multiSelectMode!="none"?(r=this._selectValueByKey()!=null?this._selectValueByKey()+this._strData[n[1]]+i:this._strData[n[1]]+i,r=r.split(i).reduce(function(n,t){return n.indexOf(t)<0&&n.push(t),n},[]),this._selectValueByKey(r.join(i))):this._selectValueByKey(this._strData[n[1]]))},_declareVariable:function(){var n=this.model.fields,i={_key:null,_text:null,_attr:null},r=[];if(!t.isNullOrUndefined(n))return i._text=n&&n.text?n.text:"text",i._key=n&&n.key?n.key:"key",r.push(i._text,i._key),r},selectValueByKey:function(i){var u,r;t.isNullOrUndefined(i)||(u=this,i=i.toString(),r=this.model.multiSelectMode!="none"?i.split(this.model.delimiterChar):i.split(","),Object.prototype.toString.call(r)==="[object Array]"&&n.each(r,function(t,i){u._setOperation(n.trim(i),"key")}))},selectValueByText:function(n){this._setOperation(n,"text")},_eventTrigger:function(){var n=this.model.multiSelectMode=="visualmode"?this._modelValue:this.element.val()==""?null:this.element.val();this._trigger("select",{isInteraction:!0,value:this.value(),text:n,key:this._selectValueKey,item:this._selectedItems})},setValue:function(n){this._setText(n)},_setOperation:function(n,i){var o="",u,f,s=this,r=this.model.dataSource,e;if(t.isNullOrUndefined(r))return!1;if(typeof r[0]=="object"||r instanceof t.DataManager)e=this.model.fields,o=e&&e[i]?e[i]:i;else if(i=="key")return!1;if(n)if(t.DataManager&&r instanceof t.DataManager)f=this._getQuery().where(o,"equal",n,!this.model.caseSensitiveSearch),u=r.executeQuery(f),u.done(function(n){s._setText(n.result[0]);s._eventTrigger()});else{if(!r||!r.length||r.length<1)return!1;f=t.Query().where(o,"equal",n,!this.model.caseSensitiveSearch);u=t.DataManager(r).executeLocal(f);this._setText(u[0]);this._eventTrigger()}},_setText:function(n){var r,u,i,f;if(!n)return!1;if(i=this.model.fields,typeof this.model.dataSource[0]=="object"||typeof n=="object"?(r=typeof n=="string"?n:i&&i.text?n[i.text]:n.text,u=typeof n=="string"?n:i&&i.key?n[i.key]:n.key):r=n,r){if(this.model.multiSelectMode=="visualmode"&&this._removeDuplicates(n))return!1;this._valueToTextBox(r,n,!0);f=this.model.multiSelectMode=="visualmode"?this._modelValue:this.element.val();this.value(f);this.model.showResetIcon&&this._showReset()}this._selectValueKey=u;this._selectkey.push(u);t.isNullOrUndefined(u)||this._selectValueByKey(this._selectkey+this.model.delimiterChar)},_textFormateString:function(n){var i=this.model.multiColumnSettings.stringFormat,r,u;if(this._columnsIndex(),t.isNullOrUndefined(n)||typeof n!="object")i=t.isNullOrUndefined(n)||typeof n=="object"?this._currList[this._activeItem-1]:i.replace("{"+this._columnIndex[0]+"}",n);else for(r=0,u=this._columnIndex.length;r<u;r++)i=i.replace("{"+this._columnIndex[r]+"}",n[this.model.multiColumnSettings.columns[parseInt(this._columnIndex[r])].field]);return i},_columnsIndex:function(){var t=this;this._columnIndex=[];n.each(this.model.multiColumnSettings.stringFormat.match(/\{.+?\}/g),function(n,i){t._columnIndex[n]=i.slice(1,-1)});this._searchColumnIndex=[];n.each(this.model.multiColumnSettings.searchColumnIndices,function(n,i){t._searchColumnIndex.push(i)})},_valueToTextBox:function(i,r,u){var f,o,e;!this._addNewTemplate&&this.model.multiColumnSettings.enable&&typeof r!="string"&&(i=this._textFormateString(r));e=this.model.delimiterChar;f=this._getUniqueKey();f=f?f:this.model.fields&&this.model.fields.key?r[this.model.fields.key]:t.isNullOrUndefined(r.key)?f:r.key;this._hiddenInput||(this._hiddenInput=t.buildTag("input#"+this.target.id+"_hidden","",{},{type:"hidden",name:this.element.attr("name")}).insertBefore(this.element));(!(this._hiddenInput.val()==f||f==null)||o>-1)&&(this.model.multiSelectMode=="none"?this._hiddenInput.val(f||i):this._hiddenInput.val(this._hiddenInput.val()+(f||i)+e));this.model.multiSelectMode=="visualmode"?(r=typeof r=="string"&&this._addNewTemplate&&r.substr(r.length-this._addNewTemplate.length)==this._addNewTemplate?r.replace(this._addNewTemplate,""):r,typeof i=="number"&&(i=this._textFormateString(i)),o=i.indexOf(this._addNewTemplate),this._addNewTemplate&&(i=i.substr(0,i.length-this._addNewTemplate.length)),(!(this._selectValueByKey()==f||f==null)||o>-1)&&this._selectValueByKey(this._selectValueByKey()!=null?this._selectValueByKey()+(o>-1?i:f)+e:(o>-1?i:f)+e),this._modelValue=t.isNullOrUndefined(this.value())?i+e:this.value().indexOf(i)!=-1&&u?i+e:this.value()+i+e,this.element.val("").removeAttr("placeholder").width(1),this._ulBox.append(this._createBox(i)),this.model.height!=""&&(n(this._ulBox).parent().css("overflow","auto"),this.model.showPopupButton&&this._ulBox.parent()[0].scrollHeight!=0&&this.dropdownbutton.css("height",this.model.height)),this._addNewTemplate=null):this.model.multiSelectMode=="delimiter"?(e=this.target.value.lastIndexOf(this.model.delimiterChar),this._typed||this.element.val()==""||u||!this.model.showPopupButton?u?this.element.val(this.element.val()==""?i+this.model.delimiterChar:this.element.val()+i+this.model.delimiterChar):e==-1?this.element.val(this._queryString.substr(0,e+1)+i+this.model.delimiterChar):this.element.val(this._queryString.substr(0,e+this.model.delimiterChar.length)+i+this.model.delimiterChar):!this.model.enableAutoFill||this.showSuggestionBox?this._checkDeli()?this.element.val(this._queryString.substr(0,e)+this.model.delimiterChar+i+this.model.delimiterChar):this.element.val(this.element.val()+this.model.delimiterChar):this._checkDeli()||this.element.val(this.element.val()+this.model.delimiterChar),this._typed=!1):(this.element.val(i),this._selectedItems=[]);this._selectedItems.push(r);this._originalval.push(i);this._moveCaretToEnd(this.element[0])},_removeDuplicates:function(n){return this._selectedItems.length==0?!1:this._selectedItems.indexOf(n)!=-1?!0:void 0},search:function(){this.model.enabled&&this._checkDelimiter()&&(this._hideResult(),this._autoFill=!1,this._queryString=n.trim(this._queryString),this._queryString.length>0&&this._OnTextEnter())},setVisible:function(n){n?this.wrapper.show():this.wrapper.hide()},hide:function(){this._hideResult()},open:function(){this._showFullList()},_changeWatermark:function(n){this._isWatermark?this.element.attr("placeholder",n):this._hiddenSpan.text(n)},_changeSkin:function(n){this.wrapper.removeClass(this.model.cssClass).addClass(n);this.suggestionList.removeClass(this.model.cssClass).addClass(n)},_setDropdown:function(n){this.model.showPopupButton=n;n?this._renderDropdown():this._destroyDropdown()},_changeHeight:function(n){this.wrapper.height(n)},_changeWidth:function(n){this.wrapper.width(n);this._setListWidth()},_setCulture:function(){this._localizedLabels=this._getLocalizedLabels();t.isNullOrUndefined(this._options)||(t.isNullOrUndefined(this._options.addNewText)||(this._localizedLabels.addNewText=this._options.addNewText),t.isNullOrUndefined(this._options.emptyResultText)||(this._localizedLabels.emptyResultText=this._options.emptyResultText),t.isNullOrUndefined(this._options.watermarkText)||(this._localizedLabels.watermarkText=this._options.watermarkText));this.model.addNewText=this._localizedLabels.addNewText;this.model.emptyResultText=this._localizedLabels.emptyResultText;this.model.watermarkText=this._localizedLabels.watermarkText},_init:function(n){if(this._options=n,this._selectkey=[],this._hiddenColumns=[],this._setCulture(),!this.element.is("input")||this.element.attr("type")&&this.element.attr("type")!="text")return!1;this.model.fields.groupBy=this.model.fields.groupBy?this.model.fields.groupBy:this.model.fields.category;this._initialize();this._render();this._wireEvents();this.initialRender=!1;this.model.validationRules!=null&&(this._initValidator(),this._setValidation())},_initValidator:function(){this.element.closest("form").data("validator")||this.element.closest("form").validate()},_setValidation:function(){var e,r,f,i,u,o;if(this.model.validationRules){e=this.model.multiSelectMode=="visualmode"?this._hiddenInput:this.element;e.rules("add",this.model.validationRules);r=this.element.closest("form").data("validator");r=r?r:this.element.closest("form").validate();f=e.attr("name");r.settings.messages[f]={};for(i in this.model.validationRules)if(u=null,!t.isNullOrUndefined(this.model.validationRules[i])){if(t.isNullOrUndefined(this.model.validationRules.messages&&this.model.validationRules.messages[i])){r.settings.messages[f][i]=n.validator.messages[i];for(o in this.model.validationMessage)i==o?u=this.model.validationMessage[i]:""}else u=this.model.validationRules.messages[i];r.settings.messages[f][i]=u!=null?u:n.validator.messages[i]}}},_removeDuplicateValue:function(n){return n.split(this.model.delimiterChar).reduce(function(n,t){return n.indexOf(t)<0&&n.push(t),n},[]).join(this.model.delimiterChar)},_delimiterChar:function(){return this.model.multiSelectMode!="none"?this.model.delimiterChar:""},_setModel:function(r){var u,l,f,e,a,y,p,v,c,o,s,h;for(u in r){switch(u){case"dataSource":this.model.loadOnDemand&&(l=n("#"+this.element[0].id+"_suggestion").get(0),l&&n(l).remove(),this.suggestionList=i);this.model.dataSource=r[u];break;case"watermarkText":this._changeWatermark(r[u]);break;case"delaySuggestionTimeout":this.model.delaySuggestionTimeout=parseInt(r[u]);break;case"value":f=t.util.getVal(r[u]);e=this._delimiterChar();this.model.multiSelectMode!="none"&&(s=f.substr(f.length-e.length)==e?f:f+e,f=f==""?this.value():(this.value()?this.value():"")+s,this.model.multiSelectMode=="visualmode"&&(f=f.split(e).reduce(function(n,t){return n.indexOf(t)<0&&n.push(t),n},[]),f=f.join(e)));this.value(this._setValue(f));a=this.model.dataSource;t.DataManager&&a instanceof t.DataManager?this._loadInitData():this._setHiddenkeyByValue(a);this._modelValue=this.value();y=this.model.multiSelectMode=="visualmode"?this._hiddenInput.val():this.element.val();this._changeEvtTrigger(y);this.model.showResetIcon&&this._showReset();break;case"showPopupButton":this._setDropdown(r[u]);break;case"enableRTL":this._RightToLeft(r[u]);break;case"showRoundedCorner":this._setRoundedCorner(r[u]);break;case"readOnly":this._checkReadOnly(r[u]);break;case"delimiterChar":p=this.model.delimiterChar;r[u]=this._validateDelimiter(r[u]);this.model.delimiterChar=r[u];this.element.val(this.value().replace(new RegExp(p,"g"),this.model.delimiterChar));this.value(this.element.val());break;case"validationRules":this.model.validationRules!=null&&(this.element.rules("remove"),this.model.validationMessage=null);this.model.validationRules=r[u];this.model.validationRules!=null&&(this._initValidator(),this._setValidation());break;case"validationMessage":this.model.validationMessage=r[u];this.model.validationRules!=null&&this.model.validationMessage!=null&&(this._initValidator(),this._setValidation());break;case"locale":t.Autocomplete.Locale[r[u]]?(this.model.locale=r[u],this._setCulture(),this._setWatermark()):this.model.locale=r[u];break;case"filterType":v=[];for(o in t.filterType)t.filterType.hasOwnProperty(o)&&v.push(t.filterType[o]);v.indexOf(r[u])>-1?this.model.filterType=r[u]:r[u]=this.model.filterType;break;case"multiSelectMode":this.model.multiSelectMode=r[u];c=this._delimiterChar();this.model.multiSelectMode!="none"&&(o=this._selectValueByKey(),s=this.value(),s&&(s=s.substr(s.length-c.length)==c?s:s+c,this.value(s),this.model.multiSelectMode=="visualmode"&&this.value(this._removeDuplicateValue(this.value()))),o&&(this._selectValueByKey(o.substr(o.length-c.length)==c?o:o+c),this.model.multiSelectMode=="visualmode"&&this._selectValueByKey(this._removeDuplicateValue(this._selectValueByKey()))));r[u]=="visualmode"?(this.model.validationRules!=null&&this.element.rules("remove"),this.element.val()!=""&&this._isWatermark&&this.element.removeAttr("placeholder")):this.element.hasClass("e-visual-mode")&&this._destroyBoxModel();this._destroy();this._init();this._setValue(this.value());this._setValidation();t.isNullOrUndefined(this.element.attr("placeholder"))&&this._isWatermark&&this._setWatermark();break;case"enabled":this._disabled(!r[u]);break;case"visible":this.setVisible(r[u]);break;case"height":this._changeHeight(r[u]);break;case"width":this._changeWidth(r[u]);break;case"cssClass":this._changeSkin(r[u]);break;case"itemsCount ":(r[u]<=0||isNaN(r[u]))&&(r[u]=0);break;case"popupHeight":this.suggestionList.css({"max-height":isNaN(r[u])?r[u]:r[u]+"px"});break;case"popupWidth":this.model.popupWidth=r[u];this._setListWidth();break;case"selectValueByKey":e=this._delimiterChar();o=t.util.getVal(r[u]);this.selectValueByKey(o);h=this.model.multiSelectMode!="none"?this._selectValueByKey()+r[u]:r[u];h=h.split(e).reduce(function(n,t){return n.indexOf(t)<0&&n.push(t),n},[]);h=h.join(e);r[u]=h.substr(h.length-e.length)==e?h:h+e;break;case"htmlAttributes":this._addAttr(r[u]);break;case"emptyResultText":t.isNullOrUndefined(this._options)&&(this._options={});this._options.emptyResultText=this.model.emptyResultText=r[u];this._setCulture();break;case"addNewText":t.isNullOrUndefined(this._options)&&(this._options={});this._options.addNewText=this.model.emptyResultText=r[u];this._setCulture();break;case"multiColumnSettings":t.isNullOrUndefined(r[u].enable)||(this.model.multiColumnSettings.enable=r[u].enable);t.isNullOrUndefined(r[u].showHeader)||(this.model.multiColumnSettings.showHeader=r[u].showHeader);t.isNullOrUndefined(r[u].stringFormat)||(this.model.multiColumnSettings.stringFormat=r[u].stringFormat);t.isNullOrUndefined(r[u].searchColumnIndices)||(this.model.multiColumnSettings.searchColumnIndices=r[u].searchColumnIndices);t.isNullOrUndefined(r[u].columns)||(this.model.multiColumnSettings.columns=r[u].columns);this.suggestionList.remove();this._renderSuggestionList();break;case"showResetIcon":this.model.showResetIcon=r[u];r[u]&&!t.isNullOrUndefined(this.value())&&this.value()!=""?this._showReset():this._removeReset()}this._hideResult()}},_destroy:function(){this.element.width("").removeAttr("role aria-label aria-expanded aria-haspopup aria-autocomplete autocomplete placeholder aria-owns aria-disabled disabled aria-activedescendant");this.element.insertAfter(this.wrapper);this.model.multiSelectMode=="visualmode"&&this.element.removeClass("e-visual-mode").attr("name",this._hiddenInput.attr("name"));this.model.enabled||this.element.removeClass("e-disable");this.wrapper.remove();this.element.removeClass("e-input").val("");this._isWatermark&&this.element.removeAttr("placeholder");this._hideResult();this.suggestionList&&this.suggestionList.remove()},_initialize:function(){this.value(this.value()===""?this.element[0].value:this.value());this.element.attr("role","combobox").attr("aria-label","Autocomplete textbox").attr("aria-expanded",!1).attr("aria-autocomplete","list").attr("aria-activedescendant","null");t.isNullOrUndefined(this.element.attr("tabindex"))&&this.element.attr("tabindex",0);/Edge\/12./i.test(navigator.userAgent)&&this.element.addClass("edge-browser");this.target=this.element[0];this.dropdownbutton=null;this._isIE8=t.browserInfo().name=="msie"&&t.browserInfo().version=="8.0"?!0:!1;this.showSuggestionBox=!1;this.noresult=!0;this._queryString=null;this.suggLen=0;this._selectedItems=[];this._modelValue="";this._activeItem=0;this._originalval=[];this.ctrlKeyPressed=!1;this._isFocused=!1;this._isOpened=!1;this._typed=!1;this._cancelEvent=!1;this._isWatermark=this._checkWatermarkSupport();this._selectedObj=[];this.eleClick=!1;this.lastScrollTop=0},_render:function(){this._renderWrapper();this._addAttr(this.model.htmlAttributes);this._setDimentions();this._renderDropdown();this._checkProperties();this._isWatermark?this._setWatermark():this._setWatermarkTxt();this.model.loadOnDemand||(this._renderSuggestionList(),this._setListPosition());this._RightToLeft(this.model.enableRTL);this._setRoundedCorner(this.model.showRoundedCorner);this.setVisible(this.model.visible)},_renderWrapper:function(){var n,i;this.element.addClass("e-input").attr("autocomplete","off");this.model.multiSelectMode=="visualmode"?(this.wrapper=t.buildTag("div.e-atc e-widget "+this.model.cssClass+"#"+this.target.id+"_wrapper").insertAfter(this.element),this.container=t.buildTag("div.e-in-wrap e-box").append(this.element)):(this.wrapper=t.buildTag("span.e-atc e-widget "+this.model.cssClass+"#"+this.target.id+"_wrapper").insertAfter(this.element),this.container=t.buildTag("span.e-in-wrap e-box").append(this.element));document.activeElement==this.element[0]&&(n=!0);this.wrapper.append(this.container);this._isWatermark||(this._hiddenSpan=t.buildTag("span.e-input e-placeholder ").insertAfter(this.element),this._hiddenSpan.text(this.model.watermarkText),this._hiddenSpan.css("display","none"),i=this,this._hiddenSpan.click(function(){this._isFocused||i.element.focus()}));n&&this.element.focus()},_renderDropdown:function(){if(this.model.showPopupButton){var i=t.buildTag("span.e-icon e-search").attr(this._isIE8?{unselectable:"on"}:{});this.dropdownbutton=t.buildTag("span.e-select#"+this.target.id+"_dropdown").attr(this._isIE8?{unselectable:"on"}:{}).append(i);this.container.append(this.dropdownbutton).addClass("e-padding");this.model.enabled||this.dropdownbutton.addClass("e-disable").attr({"aria-disabled":!0});this.dropdownbutton.on("mousedown",n.proxy(this._OnDropdownClick,this))}},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="required"&&i.element.attr(t,n);t=="class"?i.wrapper.addClass(n):t=="name"?i.element.attr(t,n):t=="disabled"&&n=="disabled"?i._disabled(!0):t=="readOnly"&&n===!0?i._checkReadOnly(!0):t=="id"?(i.wrapper.attr(t,n+"_wrapper"),i.element.attr(t,n)):i.wrapper.attr(t,n)})},_setDimentions:function(){this.model.height&&this.wrapper.height(this.model.height);this.model.width&&this.wrapper.width(this.model.width)},_renderBoxModel:function(){this._ulBox=t.buildTag("ul.e-ul e-boxes");var i=this.model.enabled?"":"e-disable";this._ulBox.addClass(i);this._hiddenInput=t.buildTag("input#"+this.target.id+"_hidden","",{},{type:"hidden"}).insertBefore(this.element);this._hiddenInput.attr("name",this.element.attr("name"));this.element.val("").removeAttr("name").addClass("e-visual-mode");this._isWatermark||this.element.width(1);this.container.prepend(this._hiddenInput,this._ulBox);this.model.height!=""?this.wrapper.height(this.model.height):this.wrapper.height("auto");this._on(this.container,"mousedown",function(t){if(!this.model.enabled)return!1;var i=n(t.target);i.is(this.element)||(t.preventDefault(),this._isFocused||this.element.focus(),i.hasClass("e-options")?(!t.ctrlKey&&i.siblings().hasClass("e-active")&&this._removeActive(),i.hasClass("e-active")?i.removeClass("e-active"):i.addClass("e-active")):this._moveCaretToEnd(this.element[0]));!t.ctrlKey&&(i.hasClass("e-boxes")||i.hasClass("e-input"))&&this._removeActive()})},_destroyBoxModel:function(){this.container.prepend(this.element);this.element.attr({name:this._hiddenInput.attr("name")}).removeAttr("style").removeClass("e-visual-mode");this.wrapper.height(this.model.height);this._hiddenInput.remove();this._hiddenInput=null;this._ulBox.remove();this._off(this.container,"mousedown")},_deleteLastBox:function(){var t=this._ulBox.children(),n=t.last(),i=n.hasClass("e-active");this._removeActive();i?this._deleteBox(n):n.addClass("e-active")},_deleteBox:function(t){for(var s,u,i,f,e,r=0,o=t.length;r<o;r++)s=this._ulBox.children(),u=s.index(t[r]),this._selectedItems.splice(u,1),i=this.model.delimiterChar,f=this._hiddenInput.val().split(i),f.splice(u,1),this._hiddenInput.val(f.join(i)),this._selectValueByKey(f.join(i)),e=this._modelValue.split(i),e.splice(u,1),this._modelValue=e.join(i),n(t[r]).remove(),this.deletedItem=n(t)[0].innerText;this.showSuggestionBox&&this._refreshPopup();this._valueChange();this.value()==""&&this.model.height!=""&&(n(this._ulBox).parent().css("overflow","hidden"),this.dropdownbutton&&this.dropdownbutton.css("height",this.model.height));this._hiddenInput.val()==""&&this._setWatermark()},_removeActive:function(){this._ulBox.children("li.e-active").removeClass("e-active")},_adjustWidth:function(){var n=t.buildTag("span",this.element.val()),i,r;this.container.append(n);r=30;i=n.width()+r;this.element.width()!=i&&this.element.width(i);this._hiddenInput.val()==""&&this._setWatermark();n.remove()},_checkProperties:function(){var r,i,u;this._checkReadOnly(this.model.readOnly);this.model.delimiterChar=this._validateDelimiter(this.model.delimiterChar);this.model.enabled?this.model.enabled&&n(this.element).hasClass("e-disable")&&(this.model.enabled=!1,this._disabled(!1)):(this.model.enabled=!0,this._disabled(!0));this._checkNameAttr();this.model.multiSelectMode=="visualmode"&&this._renderBoxModel();t.isNullOrUndefined(this._selectValueByKey())||this.value()!=""?(i=t.isNullOrUndefined(this.value())?this.value():this.value().toString(),this.model.multiSelectMode=="none"||t.isNullOrUndefined(i)||i==""||(i=i.substr(i.length-this.model.delimiterChar.length)==this.model.delimiterChar?i:i+this.model.delimiterChar),this.value(this._setValue(i)),i&&(u=this.model.dataSource,t.DataManager&&u instanceof t.DataManager?this._loadInitData(i):this._setHiddenkeyByValue(u)),this.model.showResetIcon&&this.value()!=""&&this._showReset()):(r=this._selectValueByKey().toString(),this.model.multiSelectMode!="none"&&(r=r.substr(r.length-this.model.delimiterChar.length)==this.model.delimiterChar?r:r+this.model.delimiterChar,this.model.multiSelectMode=="visualmode"?this._selectValueByKey(null):this._selectValueByKey(r)),this.selectValueByKey(r),this.model.showResetIcon&&this._showReset())},_loadInitData:function(n){var r=this.model.dataSource,u=this,f=this.model.fields,s=f&&f.text?f.text:"text",e,o,i;if(this.model.multiSelectMode!="none"&&(n=n.split(this.model.delimiterChar)),t.DataManager&&r instanceof t.DataManager&&!u._trigger("actionBegin",{requestFrom:"default"})){if(o=this._getQuery(),typeof n=="object"){for(i=0;i<n.length-1;i++)o.queries.push(t.Predicate(s,"equal",n[i].trim(),!this.model.caseSensitiveSearch));e=r.executeQuery(o)}else e=r.executeQuery(this._getQuery().where(s,"equal",n,!this.model.caseSensitiveSearch));e.done(function(n){u._trigger("actionSuccess",{e:n,requestFrom:"default"});(!n.result||n.result.length>0)&&u._setHiddenkeyByValue(n.result)})}},_setHiddenkeyByValue:function(n){var u,r,o,s,h;t.isNullOrUndefined(this.value())&&this.value("");var i=this.value(),f=n,c=[],e=[];if(i=this.model.multiSelectMode=="none"?i:i.split(this.model.delimiterChar),!t.isNullOrUndefined(f)){for(u=0;u<f.length;u++){if(r=this._getField(f[u],this.model.fields.text),typeof r=="number"&&(r=r.toString()),t.isNullOrUndefined(this.model.fields.key)){if(t.isNullOrUndefined(this.model.fields.key))if(typeof i=="object"){for(h=0;h<i.length-1;h++)r==i[h].trim()&&e.push(r);e=this._removeDuplicateVal(e);this._hiddenInput.val(e)}else r==i&&this._hiddenInput.val(r)}else if(o=this._getField(f[u],this.model.fields.key),typeof i=="string")r==i&&this._hiddenInput.val(o);else for(s=0;s<i.length-1;s++)r==i[s]&&(this._hiddenInput.val()==""?this._hiddenInput.val(o):this._hiddenInput.val(this._hiddenInput.val()+this.model.delimiterChar+o+this.model.delimiterChar));c.push(r)}typeof i!="object"&&c.indexOf(i)==-1&&this._hiddenInput.val(i)}},_checkNameAttr:function(){this.model.name=this.element.attr("name")!=null?this.element.attr("name"):this.model.name!=null?this.model.name:this.element[0].id;this.element.attr("name",this.model.name)},_disabled:function(n){n?this.disable():this.enable()},_destroyDropdown:function(){this.dropdownbutton.off("mousedown",n.proxy(this._OnDropdownClick,this));this.dropdownbutton.remove();this.dropdownbutton=null;this.container.removeClass("e-padding")},_validateDelimiter:function(t){if(n.trim(t).length==t.length||t.length!=null)if(!/^[a-zA-Z0-9]+$/.test(t))return t;return","},_checkWatermarkSupport:function(){return"placeholder"in document.createElement("input")},_setWatermark:function(){(this.model.watermarkText&&(this.value()==""||t.isNullOrUndefined(this.value()))||this.model.multiSelectMode!="visualmode")&&this.element.attr("placeholder",this.model.watermarkText).width("")},_setWatermarkTxt:function(){if(this.model.watermarkText!=null&&n.trim(this.element.val())==""&&(this.model.multiSelectMode!="visualmode"||n.trim(this._hiddenInput.val())=="")){var t=this.model.watermarkText!=null?this.model.watermarkText:this._localizedLabels.watermarkText;this._hiddenSpan.css("display","block").text(t)}},_renderSuggestionList:function(){var o=n("#"+this.element[0].id+"_suggestion").get(0),r,s,f,h,c,i,u,e;if(o&&n(o).remove(),this.suggestionList=t.buildTag("div.e-atc-popup e-popup e-widget e-box "+this.model.cssClass+"#"+this.target.id+"_suggestion","",{display:"none"}).attr("role","listbox"),this.element.attr("aria-owns",this.target.id+"_suggestion"),this.popup=this.suggestionList,r=t.buildTag("div"),this.model.multiColumnSettings.enable){if(this._tableColumn=t.buildTag("table","",{border:0,padding:0,"border-spacing":0},{role:"listbox","class":"e-atc-tableContent"}),this._listEventBind(this._tableColumn,"tr"),this._headerColGroup=document.createElement("colgroup"),this.model.multiColumnSettings.showHeader){for(this._tableHeaderDiv=t.buildTag("div",{},{},{"class":"e-atc-tableHeader "+(this.model.enableRTL?"e-atc-tableHeaderRTL":"e-atc-tableHeaderScroll")}),s=t.buildTag("div",{},{},{"class":"e-atc-tableHeaderContent e-atc-tableHeaderBorder"}),f=document.createElement("tr"),this._tableHeader=t.buildTag("table","",{"border-spacing":"0.25px"},{}),i=0,u=this.model.multiColumnSettings.columns.length;i<u;i++)this.model.multiColumnSettings.columns[i].visible!=!1?(h=t.buildTag("th",this.model.multiColumnSettings.columns[i].headerText?this.model.multiColumnSettings.columns[i].headerText:"column"+i,{"text-align":this.model.multiColumnSettings.columns[i].headerTextAlign?this.model.multiColumnSettings.columns[i].headerTextAlign:"left"},{"class":i==this.model.multiColumnSettings.columns.length-1?"":this.model.enableRTL?"e-atc-thleft":"e-atc-thright"}),n(f).append(h),c=document.createElement("col"),n(this._headerColGroup).append(c)):this._hiddenColumns.push(this.model.multiColumnSettings.columns[i]);n(this._tableHeaderDiv).append(n(s).append(n(this._tableHeader).append(f).append(this._headerColGroup)));this.suggestionList.append(this._tableHeaderDiv)}else{for(i=0,u=this.model.multiColumnSettings.columns.length;i<u;i++)n(this._headerColGroup).append(document.createElement("col"));this._tableColumn.append(this._headerColGroup)}e=t.buildTag("div");e.append(this._tableColumn);r.append(e)}else this.ul=t.buildTag("ul.e-ul").attr("role","listbox"),this._listEventBind(this.ul,"li:not('.e-category')"),r.append(this.ul);this.suggestionList.append(r);n("body").append(this.suggestionList);this._setListWidth();this._setListHeight();this.model.multiColumnSettings.enable?(r.ejScroller({height:0,width:0,scrollerSize:20}),this.scrollerObj=r.ejScroller("instance")):(this.suggestionList.ejScroller({height:0,width:0,scrollerSize:20}),this.scrollerObj=this.suggestionList.ejScroller("instance"))},_listEventBind:function(t,i){t.on({mouseenter:n.proxy(this._OnMouseEnter,this),mouseleave:n.proxy(this._OnMouseLeave,this),click:n.proxy(this._OnMouseClick,this)},i)},_checkEmptyList:function(){this.model.multiSelectMode=="visualmode"&&this._removeRepeated();this.suggestionListItems.length==0?(this.suggestionListItems.push(this.model.emptyResultText),this.noresult=!0):this.noresult=t.isNullOrUndefined(this.suggestionListItems)||this.suggestionListItems[0]!=this.model.emptyResultText?!1:!0},_showSuggestionList:function(i){this.model.loadOnDemand&&n("body").find(this.suggestionList).length==0&&this._renderPopup();this.suggestionListItems=this.model.enableDistinct?t.dataUtil.distinct(this.suggestionListItems,typeof this.suggestionListItems[0]!="object"?"":this.model.fields&&this.model.fields.text?this.model.fields.text:"text",!0):this.suggestionListItems;this.model.itemsCount>0&&(this.suggestionListItems=t.DataManager(this.suggestionListItems).executeLocal(t.Query().take(this.model.itemsCount)));this._checkEmptyList();this._addNewTemplate=null;this.noresult&&this.model.multiSelectMode=="visualmode"&&this.model.allowAddNew&&this.element.val()!=""&&!this._repeatRemove&&(this.noresult=!1,this.suggestionListItems.pop(),this._addNewTemplate="   ("+this.model.addNewText+")",this.suggestionListItems.push(this.element.val()+this._addNewTemplate),this._checkEmptyList());(!this.noresult||this.model.showEmptyResultText)&&this._generateSuggestionList(i)},_generateSuggestionList:function(i){var f=this.suggestionListItems,r,h,g,c,y,e,o,nt,p,w,u,s,tt,b,a,l;if(this.model.multiColumnSettings.enable?this._tableColumn.empty()&&this.model.multiColumnSettings.showHeader&&this._tableHeaderDiv.css("display",""):this.ul.empty(),r=this,h=document.createDocumentFragment(),typeof f[0]!="object"){if(r.model.multiColumnSettings.enable){var k=t.buildTag("tbody"),it=t.buildTag("tr").attr("role","option").attr(r._isIE8?{unselectable:"on"}:{}),rt=t.buildTag("td",{},{},{role:"option"});for(u=0,s=f.length;s>u;u++){var c=r.model.highlightSearch&&!r.noresult?r._highlightSuggestion(f[u]):f[u],d=it.clone(),v=rt.clone();n(v).attr(r._isIE8?{unselectable:"on"}:{}).attr(f[u]!=f.length-1?{"class":"e-atc-tdbottom"}:{}).html(c);d[0].appendChild(v[0]);k[0].appendChild(d[0]);h.appendChild(k[0]);r.model.showEmptyResultText&&r.model.emptyResultText==c&&r.model.multiColumnSettings.showHeader&&r._tableHeaderDiv.css("display","none")&&n(v).removeClass("e-atc-tdbottom");r._addNewTemplate&&!t.isNullOrUndefined(r._tableHeaderDiv)&&r._tableHeaderDiv.css("display","none")}r._tableColumn[0].appendChild(h)}else{for(g=t.buildTag("li",{},{},{role:"option"}).attr(r._isIE8?{unselectable:"on"}:{}),u=0,s=f.length;s>u;u++)c=r.model.highlightSearch&&!r.noresult?r._highlightSuggestion(f[u]):f[u],y=g.clone(),y[0].innerHTML=c,h.appendChild(y[0]);r.ul[0].appendChild(h)}this._currList=f;this._mapper={txt:null,key:null}}else e=this.model.fields,o={_key:null,_text:null,_attr:null},o._key=e&&e.key?e.key:"key",o._text=e&&e.text?e.text:"text",o._attr=e&&e.htmlAttributes?e.htmlAttributes:"htmlAttributes",this._mapper={txt:o._text,key:o._key},this._currList=[],this.model.fields.groupBy?(nt=e&&e.groupBy?e.groupBy:"groupBy",w=t.Query().group(nt),this._addSortingQuery(w,"key"),p=t.DataManager(f).executeLocal(w),this._swapUnCategorized(p),p.forEach(function(n,i){if(n.key)if(r.model.multiColumnSettings.enable){var u=t.buildTag("tr.e-category").attr("role","option").attr(r._isIE8?{unselectable:"on"}:{});u.append(t.buildTag("td",n.key).attr("role","option").attr(r._isIE8?{unselectable:"on"}:{}).attr(i!=f.length-1?{"class":"e-atc-tdbottom"}:{}));r._tableColumn.append(u)}else r.ul.append(t.buildTag("li.e-category",n.key).attr("role","option").attr(r._isIE8?{unselectable:"on"}:{}));r._generateLi(n.items,o)})):this._generateLi(f,o);for(u=0,s=f.length;s>u;u++)if(this.model.multiColumnSettings.enable)tt=this._textFormateString(this._currList[u]),tt==this.value()&&(l=this._getLiTags()[u],n(l).addClass("e-activeli"));else for(b=this.value().split(this.model.delimiterChar),a=0;a<=b.length;a++)this._currList[u]==b[a]&&(l=this._getLiTags()[u],n(l).addClass("e-activeli"));this._getLiTags().length>0&&this._isFocused&&this._showResult(i)},_swapUnCategorized:function(n){for(var t,r=n.length,i=0;i<r;i++)if(!n[i].key){for(t=i;t>0;t--)n[t]=n[t-1];return n[t]=n[i],!1}},_generateLi:function(i,r){var u=this,a=document.createDocumentFragment(),y=document.createDocumentFragment(),p,w,b,d,h,e,l,k,f,v,o,s,c;if(u.model.multiColumnSettings.enable){for(p=u.model.multiColumnSettings.columns.length,this._tableColumn.append(t.buildTag("tbody")),w=t.buildTag("tr"),b=t.buildTag("td"),f=0,v=i.length;v>f;f++)if(o=this._getField(i[f],r._text),s=this._getField(i[f],r._key),!t.isNullOrUndefined(o)||u.model.multiColumnSettings.enable){for(d=u._getField(i[f]),h=w.clone(),h[0].className=f%2?"e-atc-trbgcolor":"",e=0;e<p;e++)this.model.multiColumnSettings.columns[e].visible!=!1&&(l=b.clone(),l[0].innerHTML=this.model.multiColumnSettings.searchColumnIndices.length!=0?u.model.highlightSearch&&n.inArray(e,u._searchColumnIndex)>-1?u._highlightSuggestion(u._getField(i[f],u.model.multiColumnSettings.columns[e].field).toString()):u._getField(i[f],u.model.multiColumnSettings.columns[e].field):u.model.highlightSearch&&n.inArray(e.toString(),u._columnIndex)>-1?t.isNullOrUndefined(u._getField(i[f],u.model.multiColumnSettings.columns[e].field))?u._highlightSuggestion(""):u._highlightSuggestion(u._getField(i[f],u.model.multiColumnSettings.columns[e].field).toString()):u._getField(i[f],u.model.multiColumnSettings.columns[e].field),l[0].className=(f!=i.length-1?"e-atc-tdbottom ":"")+(e!=u.model.multiColumnSettings.columns.length-1?u.model.enableRTL?"e-atc-tdleft ":"e-atc-tdright ":"")+(u.model.multiColumnSettings.columns[e].cssClass?u.model.multiColumnSettings.columns[e].cssClass:""),l[0].style.textAlign=u.model.multiColumnSettings.columns[e].textAlign?u.model.multiColumnSettings.columns[e].textAlign:"left",y.appendChild(l[0]));h[0].appendChild(y);s&&h.attr("id",s);u._setAttributes(u._getField(i[f],r._attr),h[0]);a.appendChild(h[0]);u._currList=u._currList.concat([i[f]])}n(u._tableColumn).find("tbody")[0].appendChild(a)}else{for(k=n("<li><\/li>"),f=0,v=i.length;v>f;f++)o=this._getField(i[f],r._text),s=this._getField(i[f],r._key),t.isNullOrUndefined(o)||(u.model.highlightSearch&&(o=u._highlightSuggestion(o)),u.model.template&&(o=u._getTemplatedString(i[f],r._text,o)),c=k.clone(),c[0].innerHTML+=o,s&&c[0].setAttribute("id",s),u._setAttributes(u._getField(i[f],r._attr),c[0]),c.attr({role:"option","data-value":o}),a.appendChild(c[0]),u._currList=u._currList.concat([i[f]]));u.ul[0].appendChild(a)}},_getLiTags:function(){return this.model.multiColumnSettings.enable?this._tableColumn.find("tbody tr:not('.e-category')"):this.ul.children("li:not('.e-category')")},_getTemplatedString:function(n,t,i){for(var r=this.model.template,f=r.indexOf("${"),e=r.indexOf("}");f!=-1&&e!=-1;){var o=r.substring(f,e+1),s=o.replace("${","").replace("}",""),u=this._getField(n,s);t==s&&(u=i);u||(u="");r=r.split(o).join(u);f=r.indexOf("${");e=r.indexOf("}")}return r},_getField:function(n,i){return t.pvt.getObject(i,n)},_setAttributes:function(t,i){if(t){typeof t=="string"&&(t=n.parseJSON(t));for(var r in t)i.setAttribute(r,t[r])}},_setListWidth:function(){var n=this.model.popupWidth;n&&n!="auto"?this.suggestionList.css({width:n}):this.suggestionList.css({width:this.wrapper.width()})},_setListHeight:function(){this.suggestionList.css({"max-height":this.model.popupHeight})},_refreshPopup:function(){this.model.popupWidth=="auto"&&Math.floor(this.wrapper.outerWidth())!=Math.floor(this.suggestionList.outerWidth())&&(this.suggestionList.css({width:this.wrapper.width()}),this._refreshScroller());this._setListPosition()},_showResult:function(r){var u,e,o,f;if(this._refreshScroller(),this._refreshPopup(),this._isOpened)n(document).on("mousedown",n.proxy(this._OnDocumentClick,this));else this.suggestionList.css("display","none"),u=this,clearTimeout(this._typing),this._typing=setTimeout(function(){u.suggestionList[u.model.animateType=="slide"?"slideDown":"fadeIn"](u.model.animateType=="none"?0:300,function(){n(document).on("mousedown",n.proxy(u._OnDocumentClick,u))})},this.model.delaySuggestionTimeout),e=r!=i?{event:r,isInteraction:!0}:{isInteraction:!1},this._trigger("open",e),this.wrapper.addClass("e-active");this._isOpened=!0;this.showSuggestionBox=!0;o=this._getLiTags();this._listSize=o.length;n(window).on("resize",n.proxy(this._OnWindowResize,this));f=t.getScrollableParents(this.wrapper);f[0]!=window&&this._on(f,"scroll",this._hideResult)},_hideResult:function(r){var u,f;this.showSuggestionBox&&(this.showSuggestionBox=!1,this._activeItem=0,clearTimeout(this._hiding),this.element.attr("aria-expanded",!1),u=this,this._isOpened?(this.suggestionList.css("display","none"),f=r!=i?{event:r,isInteraction:!0}:{isInteraction:!1},this._trigger("close",f)):this._hiding=setTimeout(function(){u.model&&u.suggestionList[u.model.animateType=="slide"?"slideUp":"fadeOut"](u.model.animateType=="none"?0:100);u._activeItem=0},this.model.delaySuggestionTimeout),n(document).off("mousedown",n.proxy(this._OnDocumentClick,this)),n(window).off("resize",n.proxy(this._OnWindowResize,this)),this._off(t.getScrollableParents(this.wrapper),"scroll",this._hideResult),this.wrapper.removeClass("e-active"))},_refreshScroller:function(){this.suggestionList.css("height","auto");this.suggestionList.find(".e-content , .e-scroller").removeAttr("style");this.model.multiColumnSettings.enable&&this.model.multiColumnSettings.showHeader&&(n(this._tableColumn).css("width","auto"),n(this._tableHeader).css("width","auto"));this.suggestionList.css("display","block");this.model.multiColumnSettings.enable&&(this.scrollerObj.option("height","auto"),this._columnBorderAlign());var i=this.suggestionList.height()>parseInt(n.isNumeric(this.model.popupHeight)?this.model.popupHeight:this.model.popupHeight.replace("px",""))-4;i||this.model.multiColumnSettings.enable&&(this.suggestionList.height()<=this._tableColumn.height()||this.suggestionList.width()<=this._tableColumn.width()||this.model.multiColumnSettings.showHeader&&this.suggestionList.width()<=this._tableHeader.width())?(i&&(this.scrollerObj.model.height=this.model.multiColumnSettings.showHeader&&this._tableHeader?parseInt(this.suggestionList.height())-parseInt(this._tableHeader.height()):this.suggestionList.height()),!this.model.multiColumnSettings.enable&&this.suggestionList.width()<this.suggestionList.find(".e-ul").width()&&this.suggestionList.find(".e-ul").width(this.suggestionList.find(".e-ul").width()),this.scrollerObj.model.width=this.suggestionList.width(),this.scrollerObj.refresh(),this.scrollerObj.option({enableRTL:this.model.enableRTL,scrollTop:0,scrollLeft:0}),this.model.multiColumnSettings.enable&&this.model.multiColumnSettings.showHeader&&this._addThBorder(),this.suggestionList.addClass("e-scroller"),!this.scrollerObj._vScroll&&this.model.multiColumnSettings.enable&&this.model.multiColumnSettings.showHeader&&this._removeThBorder()):(this.scrollerObj.setModel({height:"auto",width:this.suggestionList.width()}),this.scrollerObj.refresh(),this.model.multiColumnSettings.enable&&this.model.multiColumnSettings.showHeader&&this._removeThBorder());this.suggestionList.css("height","auto");this._isIE8&&this.suggestionList.find(".e-vscroll div, .e-hscroll div").attr("unselectable","on");this.model.multiColumnSettings.enable&&this.model.multiColumnSettings.showHeader&&(this.suggestionList.find(".e-content").scroll(t.proxy(function(t){this._tableHeader.parent(".e-atc-tableHeaderContent").scrollLeft(n(t.currentTarget).scrollLeft())},this)),this.suggestionList.find(".e-content").length>0&&this._tableHeader.parent(".e-atc-tableHeaderContent").scrollLeft(this.model.enableRTL?this.suggestionList.find(".e-content")[0].scrollWidth-this.suggestionList.find(".e-content")[0].clientWidth:0))},_columnBorderAlign:function(){var i,t,r;if(this.model.multiColumnSettings.enable&&this.model.multiColumnSettings.showHeader&&this._tableHeaderDiv.css("display")!="none"){for(this._tableWid=this._tableHeader.outerWidth()>this._tableColumn.outerWidth()?this._tableHeader.outerWidth():this._tableColumn.outerWidth(),this._tableColumn.find("colgroup").remove(),n(this._headerColGroup.children).removeAttr("style"),t=0,r=this._headerColGroup.children.length;t<r;t++)n(this._headerColGroup.children[t]).css({width:parseInt(n(this._tableColumn).find("tr:first td").eq(t).outerWidth())>parseInt(this._tableHeader.find("tr:first th").eq(t).outerWidth())?n(this._tableColumn).find("tr:first td").eq(t).outerWidth():this._tableHeader.find("tr:first th").eq(t).outerWidth()});this._tableColumn.append(n(this._headerColGroup).clone());i=this._tableWid>this.suggestionList.width()?this._tableWid:"100%";n(this._tableColumn).css("width",i);n(this._tableHeader).css("width",i)}else if(this.model.multiColumnSettings.enable&&!this.model.multiColumnSettings.showHeader){if(n(this._tableColumn).css("width","auto"),this._tableWid=this._tableColumn.outerWidth(),n(this._headerColGroup.children).removeAttr("style"),i=this._tableWid>this.suggestionList.width()?this._tableWid:"100%",i!="100%"){for(t=0,r=this._headerColGroup.children.length;t<r;t++)n(this._headerColGroup.children[t]).css({width:n(this._tableColumn).find("tr:first td").eq(t).outerWidth()});this._tableColumn.append(this._headerColGroup)}n(this._tableColumn).css("width",i)}else n(this._tableColumn).css("width","100%")},_removeThBorder:function(){this._tableHeader.parents(".e-atc-tableHeader").removeClass(this.model.enableRTL?"e-atc-tableHeaderRTL":"e-atc-tableHeaderScroll");this._tableHeader.parent(".e-atc-tableHeaderContent").removeClass("e-atc-tableHeaderBorder")},_addThBorder:function(){this._tableHeader.parents(".e-atc-tableHeader").addClass(this.model.enableRTL?"e-atc-tableHeaderRTL":"e-atc-tableHeaderScroll").removeClass(this.model.enableRTL?"e-atc-tableHeaderScroll":"e-atc-tableHeaderRTL");this._tableHeader.parent(".e-atc-tableHeaderContent").addClass("e-atc-tableHeaderBorder")},_setListPosition:function(){var t=this.wrapper,i=this._getOffset(t),f,h=n(document).scrollTop()+n(window).height()-(i.top+n(t).outerHeight()),c=i.top-n(document).scrollTop(),u=this.suggestionList.outerHeight(),e=this.suggestionList.outerWidth(),r=i.left,o=t.outerHeight(),l=(o-t.height())/2,a=this._getZindexPartial(),s=3,v=(u<h||u>c?i.top+o+s:i.top-u-s)-l;f=n(document).scrollLeft()+n(window).width()-r;(this.model.enableRTL||e>f&&e<r+t.outerWidth())&&(r-=this.suggestionList.outerWidth()-t.outerWidth());this.suggestionList.css({left:r+"px",top:v+"px","z-index":a})},_getOffset:function(n){return t.util.getOffset(n)},_getZindexPartial:function(){return t.util.getZindexPartial(this.element,this.suggestionList)},_targetFocus:function(n){this.model.multiSelectMode=="visualmode"&&this._setWatermarkWidth(30);this._isWatermark||this._hiddenSpan.css("display","none");this.wrapper.addClass("e-focus");this._focusValue=this.model.value;this._preVal=this.model.value;this._isFocused=!0;this._trigger("focusIn",{event:n,isInteraction:!0,value:this.value()})},_onScroll:function(){var n=this.container.scrollTop();this.dropdownbutton[0].style.top=n>this.lastScrollTop?this.container[0].scrollHeight-parseInt(this.model.height)+"px":this.container[0].scrollTop+"px";this.lastScrollTop=n;this.dropdownbutton[0].style.float="right"},_focusOutAction:function(n){var i=this.model.multiSelectMode=="visualmode"?this._modelValue:this.element.val(),r,u,f;if(this._isFocused=!1,this.wrapper.removeClass("e-focus"),t.isNullOrUndefined(this.model.fields.key)&&(this.model.multiSelectMode=="none"?(r=this.model.dataSource,t.DataManager&&r instanceof t.DataManager?this._loadInitData():this._setHiddenkeyByValue(r)):this._hiddenInput.val(i)),this._focusValue==this.model.value||this.model.isChangeOnBlur)if(this.model.isChangeOnBlur&&this._preVal!=i)this.value(i),this._hiddenInput.val()==""&&this._hiddenInput.val(i),this._changeEvtTrigger(i,n);else return!1;else this._hiddenInput.val()==""&&this._hiddenInput.val(i),this._changeEvtTrigger(i,n);this.model.showPopupButton&&this.dropdownbutton.removeClass("e-active");this._isWatermark||this._setWatermarkTxt();this._removeSelection();this.model.multiSelectMode=="visualmode"&&this.model.showResetIcon&&this.element.val()!=""&&this._removeReset();this.model.multiSelectMode=="visualmode"?(this._removeActive(),this.element.val(""),this._setWatermarkWidth(1),this._preVal="",this._addNewTemplate=null):(this.model.multiSelectMode=="delimiter"&&this._valueChange(n),this._updateSelectedItemArray(this.getValue()));t.isNullOrUndefined(this.value())||this.value()==""||(u=this.value().split(this.model.delimiterChar),(this.model.multiSelectMode=="delimiter"||this.model.multiSelectMode=="none")&&(f=this._removeDuplicateVal(u),this._keyProcess(f)));this.element.val()==""&&(this.value()==""||this.value()==null)&&this._selectValueByKey(null)},_targetBlur:function(n){this._focusOutAction(n);this._trigger("focusOut",{event:n,isInteraction:!0,value:this.value()});this._targetEle()},_setWatermarkWidth:function(n){this.model.watermarkText&&this.element.attr("placeholder")?this.element.width(""):this.element.val("").width(n)},_checkDeli:function(){var n=this.element.val(),t=this.model.delimiterChar,i=n.substr(n.length-t.length,n.length);return i==t?(this.element.val(n.substr(0,n.length-t.length)),!0):!1},_removeSelection:function(){if(this.model.enableAutoFill){this.element.attr("aria-autocomplete","both");var n=this._getCaretSelection();n.end-n.start!=0&&n.end-n.start!=this.element.val().length&&(this.target.value=this.target.value.substr(0,n.start))}},_removeListHover:function(){this._getLiTags().removeClass("e-hover")},_addListHover:function(){var t=n(this._getLiTags()[this._activeItem-1]);t.addClass("e-hover");this.element.attr("aria-activedescendant",t.attr("id"));this.scrollerObj.setModel({scrollTop:this._calcScrollTop()});t.focus()},_calcScrollTop:function(){var r=this.model.multiColumnSettings.enable?this.model.multiColumnSettings.showHeader?this._tableColumn.outerHeight()+this._tableHeader.outerHeight():this._tableColumn.outerHeight():this.ul.outerHeight(),t=this.model.multiColumnSettings.enable?this._tableColumn.find("tr"):this.ul.find("li"),i=0,n;return n=this.model.multiColumnSettings.enable?this._tableColumn.find("tr.e-hover").index():this.ul.find("li.e-hover").index(),i=t.eq(1).outerHeight()*n,i-((this.model.multiColumnSettings.enable?this._tableColumn.parent().outerHeight():this.suggestionList.outerHeight())-t.eq(n).outerHeight())/2},getActiveText:function(){if(this._activeItem>0)return this.model.multiColumnSettings.enable?this._textFormateString(this._currList[this._activeItem-1]):!t.isNullOrUndefined(this._mapper)&&this._mapper.txt?this._getField(this._currList[this._activeItem-1],this._mapper.txt):this._currList[this._activeItem-1]},_getUniqueKey:function(){var n=null;return t.isNullOrUndefined(this._mapper)||(this._mapper.key&&(n=this._getField(this._currList[this._activeItem-1],this._mapper.key)),t.isNullOrUndefined(n)&&(n=null)),n},_setTextBoxValue:function(){var t,f,i,e,r,u;this._activeItem&&!this.noresult&&(t=this.model.multiColumnSettings.enable?this._textFormateString(this._currList[this._activeItem-1]):this.getActiveText(),f=this._getCaretSelection(),this.suggLen=f.start,i=this.target.value.substr(0,this.suggLen),r=this.target.value.lastIndexOf(this.model.delimiterChar),u=r==-1?n.trim(i.substr(r+1,this.suggLen)):n.trim(i.substr(r+this.model.delimiterChar.length,this.suggLen)),i&&(i.toLowerCase()==t.substr(0,this.suggLen).toLowerCase()||u.toLowerCase()==t.substr(0,this.suggLen-(r+this.model.delimiterChar.length)).toLowerCase())?(e=this.model.multiSelectMode=="delimiter"?i+t.substr(u.length,t.length):i+t.substr(i.length,t.length),this.element.val(e),this._autofilSelection(),this.model.multiSelectMode=="visualmode"&&(this._adjustWidth(),this.model.enableAutoFill&&this._refreshPopup()),this.model.autoFocus&&!this.noresult&&this._addListHover()):(this.model.multiSelectMode=="delimiter"&&t&&u?this.element.val(this.element.val().replace(u,t)):t&&this.element.val(t),this._removeSelection()))},_enterTextBoxValue:function(n){var t,i,r;this._timeStamp&&-(this._timeStamp-n.timeStamp)<400||(!this._activeItem||this.noresult||this.model.readOnly||(t=this.getActiveText(),i=this._currList[this._activeItem-1],this._preVal=t+(this.model.multiSelectMode=="delimiter"?this.model.delimiterChar:""),this._timeStamp=n.timeStamp,this._valueToTextBox(t,i,!1),r=this.model.multiSelectMode=="visualmode"?this._modelValue:this.element.val()==""?null:this.element.val(),this._trigger("select",{event:n,isInteraction:!0,value:r,text:t,key:this._getUniqueKey(),item:i}),this._valueChange(n),this.model.showResetIcon&&this._showReset()),this._isOpened&&this._trigger("close",{event:n,isInteraction:!0}))},_createBox:function(i){var r=t.buildTag("span.e-icon e-close"),u=t.buildTag("li.e-options",i).append(r);return this._on(r,"click",function(t){if(!this.model.enabled)return!1;this._deleteBox(n(t.target).parent())}),u},_addLoadingClass:function(){this.model.showLoadingIcon&&this.element.addClass("e-load")},_removeLoadingClass:function(){this.element.removeClass("e-load")},_highlightSuggestion:function(t){var o,s,u,e,f,i,h,r,c;if(n.trim(this._queryString)!=""&&(o=this.model.caseSensitiveSearch?"g":"gi",i=n.trim(this._queryString),i=/^[a-zA-Z0-9- ]*$/.test(i)?i:i.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&"),s=this.model.filterType=="startswith"?"^"+i:this.model.filterType=="endswith"?i+"$":i,u=new RegExp(s,o),u.test(t)))for(e=t.match(u),h=t.replace(u,"~^"),f=h.split("~^"),t="",r=0,c=f.length;c>r;r++)t+=e[r]?f[r]+"<span class='e-hilight-txt'>"+e[r]+"<\/span>":f[r];return t},_RightToLeft:function(n){n?(this.wrapper.addClass("e-rtl"),this.suggestionList&&this.suggestionList.addClass("e-rtl")):(this.wrapper.removeClass("e-rtl"),this.suggestionList&&this.suggestionList.removeClass("e-rtl"));this.model.multiColumnSettings.enable&&this.model.multiColumnSettings.showHeader&&this._tableHeaderDiv&&(this._tableHeaderDiv.removeClass(n?"e-atc-tableHeaderRTL":"e-atc-tableHeaderScroll"),this._tableHeader.find("tr :not(th:last)").addClass(n?"e-atc-thleft":"e-atc-thright").removeClass(n?"e-atc-thright":"e-atc-thleft"))},_setRoundedCorner:function(n){n?(this.container.addClass("e-corner"),this.suggestionList&&this.suggestionList.addClass("e-corner")):(this.container.removeClass("e-corner"),this.suggestionList&&this.suggestionList.removeClass("e-corner"))},_checkReadOnly:function(n){this.model.readOnly=n;this.model.readOnly?(this.element.attr({readonly:"readonly","aria-readonly":!0}),this._off(this.element,"keydown",this._OnKeyDown),this._off(this.element,"keyup",this._OnKeyUp),this._off(this.element,"paste",this._OnPaste),this._off(this.element,"keypress",this._onkeyPress)):(this.element.removeAttr("readonly aria-readonly"),this._on(this.element,"keydown",this._OnKeyDown),this._on(this.element,"keyup",this._OnKeyUp),this._on(this.element,"paste",this._OnPaste),this._on(this.element,"keypress",this._onkeyPress))},_onkeyPress:function(n){n.keyCode==13&&this._PreventDefaultAction(n)},_OnPaste:function(n){var t=this;setTimeout(function(){t._OnKeyUp(n)},0)},_OnKeyDown:function(t){var f;this.model.filterType!="startswith"&&(this.model.enableAutoFill=!1);switch(t.keyCode){case 37:case 35:case 36:this._removeSelection();break;case 38:this.showSuggestionBox&&this.suggestionList&&(this._removeListHover(),this._activeItem>1?this._activeItem-=1:this._activeItem=this._listSize,this._navigationHover());this._PreventDefaultAction(t);break;case 40:this.showSuggestionBox&&this.suggestionList?(this._removeListHover(),this._activeItem<this._listSize?this._activeItem+=1:this._activeItem=1,this._navigationHover()):t.ctrlKey&&this.element.val()==""?this._showFullList(t):t.ctrlKey&&this._showSuggestionList(t);this._PreventDefaultAction(t);break;case 33:case 34:if(this.showSuggestionBox&&this.suggestionList){this._removeListHover();var i=this.model.multiColumnSettings.enable?this._tableColumn.parent().height():this.suggestionList.height(),r=this.model.multiColumnSettings.enable?this._tableColumn.find("tr").outerHeight():this.ul.children("li").outerHeight(),u=Math.round(i/r)!=0?Math.round(i/r):this._listSize;this._activeItem=t.keyCode==33?this._activeItem-u:this._activeItem+u;this._activeItem<1&&(this._activeItem=1);this._activeItem>this._listSize&&(this._activeItem=this._listSize);this._navigationHover()}this._PreventDefaultAction(t);break;case 8:n.trim(this.element.val())==""&&(this._isOpened=!1);this.element.hasClass("e-load")&&this._queryString&&this._queryString.length<=this.model.minCharacter&&t.preventDefault();this.model.multiSelectMode=="visualmode"&&this.element.val()==""&&this._deleteLastBox();break;case 17:this.ctrlKeyPressed=!0;break;case 9:this.showSuggestionBox&&(this._queryString=this.element.val(),this._enterTextBoxValue(t),this._hideResult(t),this._isOpened=!1,this._PreventDefaultAction(t));break;case 27:this._isOpened=!1;f=this.showSuggestionBox;this._hideResult(t);this._PreventDefaultAction(t);f&&this._trigger("close",{event:t,isInteraction:!0})}this.model.multiSelectMode=="visualmode"&&(t.keyCode!=17&&t.keyCode!=8&&t.keyCode!=46&&this._removeActive(),this._adjustWidth())},_PreventDefaultAction:function(n){n.preventDefault();this._cancelEvent||(n.stopPropagation(),n.cancelBubble=!0,n.returnValue=!0)},_bubbleEvent:function(n){return typeof n=="boolean"&&(this._cancelEvent=n),this._cancelEvent},_navigationHover:function(){this._addListHover();this.model.enableAutoFill&&(this.element.attr("aria-autocomplete","both"),this._queryString=this.target.value,this._setTextBoxValue())},_OnKeyUp:function(t){if(this._keyDownComplete(t),this.ctrlKeyPressed&&t.type!="paste")return t.keyCode==17&&(this.ctrlKeyPressed=!1),!1;if(n.trim(this.element.val())==""&&t.keyCode==38&&t.keyCode==40)return this._hideResult(t),!1;if(!this._checkDelimiter())return!1;this._queryString=n.trim(this._queryString);switch(t.keyCode){case 34:case 33:case 38:case 40:case 37:case 39:case 20:case 16:case 17:case 18:case 35:case 36:case 144:case 9:case 27:break;case 13:t.preventDefault();this._queryString=this.element.val();this._enterTextBoxValue(t);this.model.enableAutoFill&&(this.element.attr("aria-autocomplete","both"),this.suggLen=this.element.val().length,this._autofilSelection());this._isOpened=!1;this._hideResult(t);break;case 46:if(this.model.multiSelectMode=="visualmode"&&this.element.val()==""){this._deleteBox(this._ulBox.children("li.e-active"));break}case 8:this._queryString.length>=this.model.minCharacter?(this._autoFill=!1,this._OnTextEnter(t)):(this.noresult=!0,this._hideResult(t),n.trim(this.element.val())==""&&(this._isOpened=!1));this._typed=!0;break;default:this._queryString.length>=this.model.minCharacter?(this._autoFill=!0,this._OnTextEnter(t)):(this.noresult=!0,this._isOpened=!1);this._typed=!0}},_getFilteredList:function(n,i){var u,r;clearTimeout(this.timeDelay);t.isNullOrUndefined(n)||typeof n[0]!="object"||(u=this.model.fields.text?this.model.fields.text:"text",this.model.actionFailure&&t.isNullOrUndefined(t.DataManager(n).executeLocal(t.Query().select(u))[0])&&this._trigger("actionFailure",{error:this._localizedLabels.actionFailure}));!n||!n.length||n.length<1?(this.suggestionListItems=[],this.model.actionFailure&&this._trigger("actionFailure",{error:this._localizedLabels.actionFailure})):(r=t.Query(),this._addQuery(r,typeof n[0]=="object"),this.suggestionListItems=t.DataManager(n).executeLocal(r),this.model.actionSuccess&&this._trigger("actionSuccess"));this._doneRemaining(i);this.model.actionComplete&&this._trigger("actionComplete")},_performSearch:function(n){var i,r;this.model.actionBegin&&this._trigger("actionBegin");i=this.model.dataSource;t.DataManager&&i instanceof t.DataManager?i.dataSource.offline||i.dataSource.json&&i.dataSource.json.length>0?(this._getFilteredList(i.dataSource.json,n),this._selectedObj.push(i.dataSource.json)):(window.clearTimeout(this.timer),r=this,this.timer=window.setTimeout(function(){r._fetchRemoteDat(i)},700)):this._getFilteredList(i,n)},_fetchRemoteDat:function(n){var t=this,i,r=this._getQuery();this._addQuery(r,!0);i=n.executeQuery(r);i.fail(function(n){t.suggestionListItems=null;t._removeLoadingClass();t._trigger("actionFailure",n)}).done(function(n){t._isServerFiltering?t.suggestionListItems=n.result:t._getFilteredList(n.result,n);t.model.multiSelectMode=="none"?t._selectedObj=n.result:t.model.multiSelectMode=="delimiter"&&(t._selectedObj=t._selectedObj.concat(n.result));t._doneRemaining(n);t._trigger("actionSuccess",n)}).always(function(n){t._trigger("actionComplete",n)})},_addSortingQuery:function(n,t){if(this.model.allowSorting){var i=this.model.sortOrder=="descending"?!0:!1;n.sortBy(t,i)}},_addQuery:function(n,i){var u="",f,e,o,s,r;if(this._predicates=[],i&&(o=this.model.fields,u=o&&o.text?o.text:"text"),this._queryString)if(this.model.multiColumnSettings.enable)if(this._columnsIndex(),i){if(u=[],this.model.multiColumnSettings.searchColumnIndices.length!=0)for(r=0,s=this._searchColumnIndex.length;r<s;r++)u.push(this.model.multiColumnSettings.columns[this._searchColumnIndex[r]].field),f=this._predicateConvertion(f,this.model.multiColumnSettings.columns[this._searchColumnIndex[r]].field,this.model.multiColumnSettings.columns[this._searchColumnIndex[r]].filterType?this.model.multiColumnSettings.columns[this._searchColumnIndex[r]].filterType:this.model.filterType,this._queryString,!this.model.caseSensitiveSearch,this.model.multiColumnSettings.columns[this._searchColumnIndex[r]].type?this.model.multiColumnSettings.columns[this._searchColumnIndex[r]].type:"string");else for(r=0,s=this._columnIndex.length;r<s;r++)u.push(this.model.multiColumnSettings.columns[this._columnIndex[r]].field),f=this._predicateConvertion(f,this.model.multiColumnSettings.columns[this._columnIndex[r]].field,this.model.multiColumnSettings.columns[this._columnIndex[r]].filterType?this.model.multiColumnSettings.columns[this._columnIndex[r]].filterType:this.model.filterType,this._queryString,!this.model.caseSensitiveSearch,this.model.multiColumnSettings.columns[this._columnIndex[r]].type?this.model.multiColumnSettings.columns[this._columnIndex[r]].type:"string");for(r=0;r<n.queries.length;r++)n.queries[r].fn=="onWhere"&&(e=n.queries.slice(r)[0].e);t.isNullOrUndefined(e)&&this.model.multiColumnSettings.searchColumnIndices.length!=0?this._predicates.length>0&&n.where(t.Predicate.or(this._predicates)):this.model.multiColumnSettings.searchColumnIndices.length!=0?n.where(e.and(this._predicates)):t.isNullOrUndefined(e)?n.where(f):n.where(e.and(f))}else n.where(u,this.model.filterType,this._queryString,!this.model.caseSensitiveSearch,this.model.ignoreAccent);else n.where(u,this.model.filterType,this._queryString,!this.model.caseSensitiveSearch,this.model.ignoreAccent);this._addSortingQuery(n,u)},_predicateConvertion:function(n,r,u,f,e,o){var s,h=this.model.ignoreAccent;return o=="number"?s=Number(f):o=="boolean"?f=="true"||f=="yes"||f=="1"?s=!0:(f=="false"||f=="no"||f=="0")&&(s=!1):s=o=="date"?new Date(f):f,o=="number"&&isNaN(s)||o=="boolean"&&s==i?n=n:this.model.multiColumnSettings.searchColumnIndices.length!=0?this._predicates.push(new t.Predicate(r,u,s,e,h)):n=n!=i?n.or(r,u,s,e,h):t.Predicate(r,u,s,e,h),n},_getQuery:function(){var r;if(t.isNullOrUndefined(this.model.query)){var u=[],i=t.Query(),n=this.model.fields;for(r in n)r!=="tableName"&&n[r]&&u.push(n[r]);u.length>0&&i.select(u);t.isNullOrUndefined(this.model.dataSource.dataSource.url)||this.model.dataSource.dataSource.url.match(n.tableName+"$")||t.isNullOrUndefined(n.tableName)||i.from(n.tableName)}else i=this.model.query.clone();return i},_OnTextEnter:function(n){var i=this;t.isDevice()?(clearTimeout(this.timeDelay),this.timeDelay=setTimeout(function(){i._onTextProcess(n)},i.model.delaySuggestionTimeout)):i._onTextProcess(n)},_onTextProcess:function(n){this._addLoadingClass();this.element.attr("aria-expanded",!1);this._performSearch(n);this.model.showResetIcon&&this._showReset()},_showReset:function(){t.isNullOrUndefined(this.resetSpan)&&(this.resetSpan=t.buildTag("span.e-icon e-iclose"),this.model.showPopupButton?(this.dropdownbutton.before(this.resetSpan),this.container.addClass("e-popup e-reset")):this.container.append(this.resetSpan).addClass("e-reset"),this._on(this.resetSpan,"mousedown",this._refreshSearch))},_targetEle:function(){this.eleClick&&(this.element[0].focus(),this.eleClick=!1)},_refreshSearch:function(){this.model.enabled&&!this.model.readOnly&&(this.resetSpan=null,this.clearText(),this.eleClick=!0,this._refreshPopup())},_removeReset:function(){this.resetSpan=this.resetSpan&&this.resetSpan[0].remove();this._refreshPopup()},_doneRemaining:function(n){this._showSuggestionList(n);this.element.attr({"aria-expanded":!0,"aria-haspopup":!0});(this.model.enableAutoFill&&this._autoFill&&!this.noresult||this.model.autoFocus)&&(this.element.attr("aria-autocomplete","both"),this._activeItem=1,this._queryString=this.target.value);this.model.enableAutoFill&&this._autoFill&&!this.noresult&&this._setTextBoxValue();this.model.autoFocus&&!this.noresult&&(this._addListHover(),this.wrapper.width()>0&&this.suggestionList.children("div.e-content").css({width:this.wrapper.width()}));this._removeLoadingClass();this.noresult&&!this.model.showEmptyResultText&&this._hideResult(n)},_removeRepeated:function(){var u=this.suggestionListItems,i,e,r,n,f;if(!u||this._selectedItems.length==0||this.suggestionListItems.length==1&&this.suggestionListItems[0]==this.element.val()+this._addNewTemplate)return!1;for(this._repeatRemove=!1,i=0,e=this._selectedItems.length;i<e;i++)t.DataManager&&this.model.dataSource instanceof t.DataManager?(n=this,f=0,u.forEach(function(u){(t.isNullOrUndefined(u[n.model.fields.text])?!0&&!t.isNullOrUndefined(u[n.model.fields.key])?u[n.model.fields.key]==n._selectedItems[i][n.model.fields.key]:!0&&!t.isNullOrUndefined(u[n.model.fields.htmlAttributes])?u[n.model.fields.htmlAttributes]==n._selectedItems[i][n.model.fields.htmlAttributes]:!0&&!t.isNullOrUndefined(u[n.model.fields.groupBy])?u[n.model.fields.groupBy]==n._selectedItems[i][n.model.fields.groupBy]:!0:u[n.model.fields.text]==n._selectedItems[i][n.model.fields.text])&&(r=f);f++})):(r=u.indexOf(this._selectedItems[i]),isNaN(parseFloat(this._selectedItems[i]))||(r=u.indexOf(parseFloat(this._selectedItems[i])))),r==-1||t.isNullOrUndefined(r)||this.suggestionListItems.splice(r,1),this.element.val()==this._selectedItems[i]&&(this._repeatRemove=!0)},_checkDelimiter:function(){this._queryString=this.element.val();var n=this.model.multiSelectMode!="delimiter"?-1:this._queryString.lastIndexOf(this.model.delimiterChar);return n==-1?!0:(this._queryString=this._queryString.substr(n+this.model.delimiterChar.length,this._queryString.length),!0)},_autofilSelection:function(){var n=this.element[0],t=this.element.val().length;n.setSelectionRange?n.setSelectionRange(this.suggLen,t):n.createTextRange&&(n=n.createTextRange(),n.collapse(!0),n.moveEnd("character",t),n.moveStart("character",this.suggLen),n.select())},_renderPopup:function(){this._renderSuggestionList();this._setListPosition();this.model.enableRTL&&this.suggestionList.addClass("e-rtl");this.model.showRoundedCorner&&this.suggestionList.addClass("e-corner")},_OnDropdownClick:function(i){this.model.enabled&&!this.model.readOnly&&(i.preventDefault(),this.model.loadOnDemand&&n("body").find(this.suggestionList).length==0&&this._renderPopup(),this.dropdownbutton.addClass("e-active"),this._iconEventBind(this.dropdownbutton,"span"),(i.which&&i.which==1||i.button&&i.button==0)&&(this._addLoadingClass(),this.showSuggestionBox?(this._hideResult(i),this._isOpened=!1,this._removeLoadingClass()):this._queryString&&this._queryString.length>0||this.value()&&this.model.multiSelectMode!="visualmode"?(t.isNullOrUndefined(this._queryString)&&(this._queryString=this.value()),this._isFocused=!0,this._OnTextEnter(i)):this._showFullList(i)))},_iconEventBind:function(t,i){t.on({mouseleave:n.proxy(this._OnMouseIconClick,this)},i)},_OnMouseIconClick:function(){this.dropdownbutton.removeClass("e-active")},_showFullList:function(n){this._isFocused||this.element.focus();this._queryString=null;this._autoFill=!1;this._performSearch(n)},_OnMouseEnter:function(n){this._getActiveItemIndex(n);this.model.showPopupButton&&this.dropdownbutton.removeClass("e-active")},_getActiveItemIndex:function(t){var i=t.target;this.model.multiColumnSettings.enable&&t.target.tagName.toLowerCase()!="tr"?i=n(t.target).parents("tr"):t.target.tagName.toLowerCase()!="li"&&(i=n(t.target).parents("li"));this._getLiTags().removeClass("e-hover");n(i).addClass("e-hover");this._queryString=this.element.val();this._activeItem=this._getLiTags().index(n(i))+1},_OnMouseLeave:function(){this._getLiTags().removeClass("e-hover");this.model.showPopupButton&&this.dropdownbutton.removeClass("e-active");this.model.highlightSearch&&this._getLiTags().find(".e-hilight-txt").removeClass("e-hover")},_OnMouseClick:function(n){this._getActiveItemIndex(n);this._checkEmptyList();this.noresult||(this._enterTextBoxValue(n),this._isOpened=!1,this._hideResult(n))},_OnDocumentClick:function(t){n(t.target).is(this.suggestionList)||n(t.target).parents(".e-atc-popup").is(this.suggestionList)||n(t.target).is(this.element)||n(t.target).parents(".e-atc").is(this.wrapper)?(n(t.target).is(this.suggestionList)||n(t.target).parents(".e-atc-popup").is(this.suggestionList))&&t.preventDefault():(this._isOpened=!0,this._hideResult(t))},_OnWindowResize:function(){this._refreshPopup()},_keyProcess:function(n){var r=this._declareVariable(),o=this,u,i,e,f;if(u=this._delimiterChar(),!t.isNullOrUndefined(n))for(i=0,e=n.length;i<e;i++){if(f=n[i],f=="")return!1;t.DataManager&&this.model.dataSource instanceof t.DataManager?o._selectValueByKey(null):(this._dataQuery=this.model.multiColumnSettings.enable?t.Query():t.Query().where(r[0],this.model.filterType,f,!1),this._promise=t.DataManager(this.model.dataSource).executeLocal(this._dataQuery),this.model.multiColumnSettings.enable?this._formatStringKey(n,r,u):this._promise instanceof Array&&this._promise.length==0?this._selectValueByKey()!=null?this._selectValueByKey():null:this._getFieldKey(this._promise[0],r[1],u))}},_formatStringKey:function(n,t,i){var r,e,o,f,u,s;for(this._selectValueByKey(null),r=0,e=n.length;r<e;r++)if(n[r]!=""){for(o=n[r],u=0,s=this._promise.length;u<s;u++)o==this._textFormateString(this._promise[u])&&(f=u);f?this._getFieldKey(this._promise[f],t[1],i):this._selectValueByKey()!=null?this._selectValueByKey():null}},_getFieldKey:function(i,r,u){if((t.isNullOrUndefined(this.model.template)||!t.isNullOrUndefined(this._getField(i,r)))&&!t.isNullOrUndefined(this._getField(i,r))){if(f=this._getField(i,r).toString(),this.model.multiSelectMode!="none"){var e,o,f=this._selectValueByKey(),s=this.model.delimiterChar;e=this._getField(i,r).toString();f!=null?(o=f.toString().split(u).filter(function(n){return n!==""}),n.inArray(e,o)==-1?(o.push(e),f=o.join(s)+s):f=e+s):f=e+s}this._selectValueByKey(f)}},_removeDuplicateVal:function(i){var r=[];return n.each(i,function(t,i){n.inArray(i,r)==-1&&r.push(i)}),i.length!=1||t.isNullOrUndefined(this._originalval)||n.inArray(i[0],this._originalval)==-1&&this._selectValueByKey(null),r},_valueChange:function(n){var i=this.model.multiSelectMode=="visualmode"?this._modelValue:this.element.val();(this.value()!=i||i==""&&this.element.val()!="")&&(this.value(i),(t.isNullOrUndefined(this.model.fields.key)||i=="")&&this._hiddenInput.val(i),this._changeEvtTrigger(i,n),this._queryString=this.model.multiColumnSettings.enable||this.model.multiSelectMode!="none"?null:this.element.val());this.model.showResetIcon&&!i&&(this.wrapper.find("span.e-iclose").remove(),this.resetSpan=null)},_changeEvtTrigger:function(n,t){var r=t!=i?{event:t,isInteraction:!0,value:n}:{isInteraction:!1,value:n,lastremovedItem:this.deletedItem};this.initialRender||(this._trigger("_change",{value:n}),this._trigger("change",r))},_updateSelectedItemArray:function(i){var r=[],u,s,e,o,f;if(this._selectedItems=[],this.suggestionListItems=this.model.dataSource,this.model.multiSelectMode=="delimiter"&&i)if(r=i.split(this.model.delimiterChar),t.DataManager&&this.model.dataSource instanceof t.DataManager||!t.isNullOrUndefined(this.suggestionListItems)&&typeof this.suggestionListItems[0]!="object"){for(u=0,s=r.length;u<s;u++)if(r[u]){var o=this,h=this._selectedObj.length,f={};for(e=0;e<h;e++)this._selectedObj[e][o.model.fields.text]==r[u]&&(f=this._selectedObj[e],e=h);this._selectedItems.push(n.isEmptyObject(f)?r[u]:f)}}else this._createBoxForObjectType(r);else this.model.multiSelectMode=="none"&&i&&(r.push(i),t.DataManager&&this.model.dataSource instanceof t.DataManager||!t.isNullOrUndefined(this.suggestionListItems)&&typeof this.suggestionListItems[0]!="object"?(o=this,f=n.grep(this._selectedObj,function(n){if(n[o.model.fields.text]==i)return n}),this._selectedItems.push(f.length>0?f:i)):this._createBoxForObjectType(r))},_keyDownComplete:function(n){var i=this.element.val(),f=this.value();if(!t.isNullOrUndefined(this.model.fields.key)&&(n.keyCode==46||n.keyCode==8||n.keyCode==65)&&this.model.multiSelectMode=="delimiter"){var e=this.value(),o=e.split(","),r=this.model.delimiterChar,s=o.indexOf(f.replace(/,/g,"")),u=this._hiddenInput.val().split(r);this._hiddenInput.val(u.join(r));u.splice(s,1)}this._preVal==i||this.model.isChangeOnBlur&&(n.keyCode!=13||this._activeItem&&!this.noresult&&!this.model.readOnly)||(this._preVal=i,this.model.multiSelectMode=="visualmode"&&this._adjustWidth(),this._valueChange(n))},_moveCaretToEnd:function(n){var t,i;n.scrollLeft=n.scrollWidth;n.createTextRange&&(t=n.createTextRange(),t.moveEnd("textedit"),t.moveStart("textedit"),t.select());typeof n.selectionStart=="number"?n.selectionStart=n.selectionEnd=n.value.length:typeof n.createTextRange!="undefined"&&(i=n.createTextRange(),i.collapse(!1),i.select())},_getCaretSelection:function(){var n=this.element[0],u=0,f=0,e,t,i,r,o;return isNaN(n.selectionStart)?(e=document.selection.createRange().getBookmark(),t=n.createTextRange(),t.moveToBookmark(e),i=n.createTextRange(),i.collapse(!0),i.setEndPoint("EndToStart",t),r=i.text.length,o=t.text.length,{start:r,end:r+o}):(u=n.selectionStart,f=n.selectionEnd,{start:Math.abs(u),end:Math.abs(f)})},_getLocalizedLabels:function(){return t.getLocalizedConstants(this.sfType,this.model.locale)},_wireEvents:function(){this._on(this.element,"focus",this._targetFocus);this._on(this.element,"blur",this._targetBlur);this._on(this.container,"scroll",this._onScroll)}});t.Autocomplete.Locale=t.Autocomplete.Locale||{};t.Autocomplete.Locale["default"]=t.Autocomplete.Locale["en-US"]={addNewText:"Add New",emptyResultText:"No suggestions",actionFailure:"The specified field doesn't exist in given data source",watermarkText:""};t.filterType={StartsWith:"startswith",Contains:"contains",EndsWith:"endswith",LessThan:"lessthan",GreaterThan:"greaterthan",LessThanOrEqual:"lessthanorequal",GreaterThanOrEqual:"greaterthanorequal",Equal:"equal",NotEqual:"notequal"};t.SortOrder={Ascending:"ascending",Descending:"descending"};t.MultiSelectMode={None:"none",Delimiter:"delimiter",VisualMode:"visualmode"};t.Animation={None:"none",Slide:"slide",Fade:"fade"};t.Type={Number:"number",String:"string",Boolean:"boolean",Date:"date"}})(jQuery,Syncfusion)});
