@charset "UTF-8";
/*!
*  filename: ej.mobile.ios7-core.css
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws.
*/

html {
  height: 100%;
  -ms-touch-action: none;
  min-height: 100%;
  touch-action: none;
  position: relative;
}
body.e-m-viewport {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
  line-height: normal;
}
* {
  -webkit-tap-highlight-color: transparent;
  font-family: sans-serif;
  margin: 0;
  padding: 0;
}
*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
body {
  -webkit-touch-callout: none;
  -webkit-text-size-adjust: none;
  -webkit-user-select: none;
  -ms-content-zooming: none;
  content-zooming: none;
  -ms-user-select: none;
  overflow: hidden;
  width: 100%;
  height: 100%;
  background-size: 100%;
}
.e-js {
  display: block;
}
.appview.e-m-flat,
.subpage.e-m-flat {
  color: black;
  background: white;
}
.appview.e-m-ios7 {
  cursor: pointer;
}
.appview.e-m-windows.e-m-light,
body.e-m-windows.e-m-light,
.subpage.e-m-windows.e-m-light,
.e-m-nb.e-m-windows.e-m-light {
  background: white;
}
.appview.e-m-android.e-m-light,
body.e-m-android.e-m-light,
.subpage.e-m-android.e-m-light,
.e-m-nb.e-m-android.e-m-light {
  background: #eeeeee;
}
.appview.e-m-ios7.e-m-light,
body.e-m-ios7.e-m-light,
.subpage.e-m-ios7.e-m-light,
.e-m-nb.e-m-ios7.e-m-light {
  background: #f9f9f9;
  /* Old browsers */

}
.e-m-grid-core.subpage.e-m-core,
.e-m-lv.subpage.e-m-windows {
  background: transparent;
}
.appview.e-m-android.light,
.appview.e-m-android.light *,
.appview.e-m-windows.e-m-light *,
.subpage.e-m-android.light,
.subpage.e-m-android.light *,
.subpage.e-m-windows.e-m-light * {
  color: #222222;
}
.e-m-waitingpopup {
  height: 100%;
  opacity: 0.4;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 100008;
  background: black;
}
.e-m-waitingpopup .e-m-image {
  background-repeat: no-repeat;
  color: White;
  height: 32px;
  margin: 0 auto;
  padding-top: 6px;
  position: relative;
  text-indent: 37px;
  width: 115px;
  top: calc(100% / 2 - 16px);
}
.e-m-ios7.e-m-waitingpopup .e-m-image {
  background: url("images/waitingpopup/waitingpopup_ios7.gif") no-repeat left center;
}
.e-m-android.e-m-waitingpopup .e-m-image {
  background: url("images/waitingpopup/waitingpopup_android.GIF") no-repeat left center;
}
.e-m-windows.e-m-waitingpopup .e-m-image {
  background: url("images/waitingpopup/waitingpopup_windows.gif") no-repeat left center;
  padding-top: 20px;
}
.e-m-windows.e-m-tablet.e-m-waitingpopup .e-m-image {
  background: url("images/waitingpopup/waitingpopup_winrt.gif") no-repeat left center;
  padding-top: 4px;
}
/*Tile Starts*/

.e-m-tile .e-m-tile-selected {
  border: 2px solid;
}
.e-m-tile {
  margin: 5px;
}
.e-m-tile .e-m-tile-selected::before {
  border-left: 2px solid;
  border-bottom: 2px solid;
}
.e-m-tile .e-m-tile-ovelay {
  height: 100%;
  width: 100%;
  display: block;
  position: absolute;
  top: 0px;
  right: 0px;
  left: 0px;
  bottom: 0px;
}
/*Badge*/

.e-m-tile-small.e-m-tile-badge.e-m-badge-position-topright::after {
  top: 5px;
  right: 5px;
}
.e-m-tile-small.e-m-tile-badge.e-m-badge-position-bottomright::after {
  bottom: 5px;
  right: 5px;
}
.e-m-tile.e-m-badge-position-bottomright::after {
  bottom: 6px;
}
.e-m-tile.e-m-caption-outer.e-m-badge-position-bottomright::after {
  bottom: 55px;
}
.e-m-tile.e-m-badge-position-topright::after {
  top: 10px;
}
.e-m-tile.e-m-tile-badge.e-m-tile-badge-value::after {
  content: attr(badgeValue);
  font-size: 14px;
  text-align: center;
}
.e-m-tile.e-m-tile-badge::after {
  font-size: 18px;
  line-height: 18px;
  right: 10px;
  min-height: 18px;
  min-width: 18px;
  position: absolute;
  z-index: 10;
}
/*Caption position*/

.e-m-tile-caption.e-m-tile-caption-icon.e-m-caption-innertop::before {
  top: 10px;
}
.e-m-tile-caption.e-m-tile-caption-icon.e-m-caption-innerbottom::before {
  bottom: 10px;
}
.e-m-tile-caption-text.e-m-caption-innertop::before {
  top: 0px;
  margin-top: 10px;
}
.e-m-tile-caption-text.e-m-caption-innerbottom::before {
  bottom: 0px;
  margin-bottom: 6px;
}
.e-m-tile-caption-text.e-m-caption-innerbottom::before,
.e-m-tile-caption-text.e-m-caption-innertop::before {
  max-height: 38px;
  height: auto;
  left: 0px;
}
.e-m-tile-caption-text.e-m-caption-outer::before {
  bottom: 0px;
  height: 45px;
}
.e-m-tile-caption-text::before {
  content: attr(text);
  width: 100%;
}
.e-m-tile-caption.e-m-tile-caption-icon::before {
  width: 30px;
  height: 30px;
  font-size: 30px;
}
.e-m-tile-caption.e-m-caption-outer.e-m-tile-caption-icon::before {
  bottom: 15px;
}
.e-m-tile-caption::before {
  font-size: 14px;
  position: absolute;
  display: block;
  float: left;
  overflow: hidden;
  z-index: 10;
}
/*Text alignment*/

.e-m-caption-align-right.e-m-caption-innertop.e-m-badge-position-topright.e-m-tile-caption-icon::before,
.e-m-caption-align-right.e-m-caption-innerbottom.e-m-badge-position-bottomright.e-m-tile-caption-icon::before {
  right: 35px;
}
.e-m-caption-align-normal.e-m-tile-caption-icon::before,
.e-m-caption-align-left.e-m-tile-caption-icon::before {
  left: 10px;
}
.e-m-tile-caption-icon.e-m-caption-align-center::before {
  margin-left: -10px;
  left: 50%;
}
.e-m-tile-caption-icon.e-m-caption-align-right::before {
  right: 10px;
}
.e-m-tile-caption-text.e-m-caption-align-right.e-m-caption-innertop.e-m-badge-position-topright::before,
.e-m-tile-caption-text.e-m-caption-align-right.e-m-caption-innerbottom.e-m-badge-position-bottomright::before {
  padding-right: 34px;
}
.e-m-tile-caption-text.e-m-caption-align-normal::before,
.e-m-tile-caption-text.e-m-caption-align-left::before {
  text-align: left;
}
.e-m-tile-caption-text::before {
  left: 0px;
  right: 0px;
  padding-left: 10px;
  padding-right: 10px;
}
.e-m-tile-caption-text.e-m-caption-align-center::before {
  text-align: center;
}
.e-m-tile-caption-text.e-m-caption-align-right::before {
  direction: rtl;
}
.e-m-tile .e-m-tile-image .e-m-tile-image {
  background-repeat: no-repeat;
  overflow: hidden;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagecenter {
  background-position: center center;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagefill {
  background-size: 100% 100%;
  backface-visibility: hidden;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagetopcenter {
  background-position: top center;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagebottomcenter {
  background-position: bottom center;
}
.e-m-tile .e-m-tile-image .e-m-tile-imageleftcenter {
  background-position: left center;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagerightcenter {
  background-position: right center;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagetopleft {
  background-position: top left;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagetopright {
  background-position: top right;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagebottomright {
  background-position: bottom right;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagebottomleft {
  background-position: bottom left;
}
.e-m-tile.e-m-tile-caption.e-m-caption-outer {
  padding: 0px;
}
.e-m-tile:not(.e-m-tile-imagefill):not(.e-m-windows) {
  padding: 10px;
}
.e-m-tile .e-m-tile-template {
  background-size: 100% 100%;
  width: inherit;
  height: inherit;
}
.e-m-tile .e-m-tile-image {
  width: 100%;
  height: 100%;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  position: absolute;
}
.e-m-tile .e-m-tile-image.e-m-tile-flip,
.e-m-tile .e-m-tile-image.e-m-tile-flipback {
  height: auto;
  backface-visibility: hidden;
}
/*Rounded corner*/

.e-m-tile-round-corner.e-m-caption-outer,
.e-m-tile-round-corner .e-m-tile-image {
  border-radius: 10px;
}
.e-m-tile .e-m-tile-selected::before {
  position: absolute;
  display: block;
  content: "";
  height: 5px;
  width: 10px;
  right: 4px;
  z-index: 102;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
/*Tile Animation Starts*/

.e-m-tile.e-m-livetile-enable .e-m-tile-slideback {
  position: absolute;
  transform: translateY(0px);
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -ms-transform: translateY(0px);
  transition-duration: 350ms;
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-tile.e-m-livetile-enable .e-m-tile-slide {
  position: absolute;
  transform: translateY(118%);
  -webkit-transform: translateY(118%);
  -moz-transform: translateY(118%);
  -ms-transform: translateY(118%);
  transition-delay: 350ms;
  -webkit-transition-delay: 350ms;
  -ms-transition-delay: 350ms;
  transition-duration: 0ms;
  -webkit-transition-duration: 0ms;
  -moz-transition-duration: 0ms;
  -ms-transition-duration: 0ms;
}
.e-m-tile.e-m-livetile-enable .e-m-tile-slideup {
  position: absolute;
  transform: translateY(-100%);
  -webkit-transform: translateY(-100%);
  -moz-transform: translateY(-100%);
  -ms-transform: translateY(-100%);
  transition-duration: 350ms;
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-tile.e-m-livetile-enable .e-m-tile-carouselback {
  position: absolute;
  transform: translateX(0px);
  -webkit-transform: translateX(0px);
  -moz-transform: translateX(0px);
  -ms-transform: translateX(0px);
  transition-duration: 350ms;
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-tile.e-m-livetile-enable .e-m-tile-carousel {
  position: absolute;
  transform: translateX(100%);
  -webkit-transform: translateX(100%);
  -moz-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transition-delay: 350ms;
  -webkit-transition-delay: 350ms;
  -ms-transition-delay: 350ms;
  transition-duration: 0ms;
  -webkit-transition-duration: 0ms;
  -moz-transition-duration: 0ms;
  -ms-transition-duration: 0ms;
  z-index: -1;
}
.e-m-tile.e-m-livetile-enable .e-m-tile-carouselup {
  position: absolute;
  transform: translateX(-100%);
  -webkit-transform: translateX(-100%);
  -moz-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  transition-duration: 350ms;
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-tile.e-m-livetile-enable .e-m-tile-image.e-m-tile-flip {
  position: absolute;
  transform: perspective(1000px) rotateX(180deg);
  -webkit-transform: perspective(1000) rot ateX(180deg);
  -moz-transform: perspective(1000) rotateX(180deg);
  transition-duration: 350ms;
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-tile.e-m-livetile-enable .e-m-tile-image.e-m-tile-flipback {
  position: absolute;
  transform: perspective(1000px) rotateX(0deg);
  -webkit-transform: perspective(1000) rotateX(0deg);
  -moz-transform: perspective(1000) rotateX(0deg);
  transition-duration: 350ms;
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-tile.e-m-state-active .e-m-tile-overlay {
  opacity: 0.5;
}
.e-m-tile .e-m-tile-overlay {
  height: 100%;
  width: 100%;
  display: block;
  position: absolute;
  top: 0px;
  right: 0px;
  left: 0px;
  bottom: 0px;
}
@media screen and (min-width: 800px) {
  .e-m-tile.e-js.e-m-ios7.e-m-tile-small,
  .e-m-tile.e-js.e-m-android.e-m-tile-small {
    margin: 40px;
  }
}
/*Tile Ends*/

.e-m-windows .e-m-tile-group,
.e-m-flat .e-m-tile-group {
  float: left;
}
.e-m-windows .e-m-tile-group.aligncenter,
.e-m-flat .e-m-tile-group.aligncenter {
  display: table;
  float: none;
  margin: 0 auto;
  padding: 0;
}
.e-m-windows .e-m-tile-group,
.e-m-flat .e-m-tile-group {
  float: left;
}
.e-m-windows .e-m-tile-small-col-2,
.e-m-flat .e-m-tile-small-col-2 {
  width: 160px;
  float: left;
}
.e-m-windows .e-m-tile-group .e-m-tile-column,
.e-m-flat .e-m-tile-group .e-m-tile-column {
  float: left;
  overflow: hidden;
  padding-top: 5px;
  position: relative;
}
.e-m-windows .e-m-tile-group .e-m-tile-column .e-m-tile.e-m-tile-wide,
.e-m-windows .e-m-tile-group .e-m-tile-column .e-m-tile.e-m-tile-small,
.e-m-windows .e-m-tile-group .e-m-tile-column .e-m-tile.e-m-tile-medium,
.e-m-flat .e-m-tile-group .e-m-tile-column .e-m-tile.e-m-tile-wide,
.e-m-flat .e-m-tile-group .e-m-tile-column .e-m-tile.e-m-tile-small,
.e-m-flat .e-m-tile-group .e-m-tile-column .e-m-tile.e-m-tile-medium {
  float: left;
}
.e-m-flat.e-m-tile-group {
  position: relative;
  margin: 0 auto;
}
.e-m-flat .e-m-tile-group .e-m-tile-column {
  max-height: 100%;
  max-width: 320px;
}
/*Radial Slider start*/

.e-m-radialslider .e-m-rs-svg {
  overflow: visible;
}
/*Radial Slider end*/

/*Grid*/

.e-m-grid-core {
  border-collapse: collapse;
  font-family: segoe UI;
  font-size: 15px;
  height: auto;
  outline: 0 none;
  border-width: 1px;
  border-style: solid;
  text-shadow: 0 0 0;
  overflow: hidden;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  text-align: left;
}
.e-m-grid-parent-container {
  position: relative;
  width: 100%;
  height: 100%;
}
.e-m-grid-core .e-m-grid-hcell .e-m-icon-grid-sort {
  font-size: 10px;
  height: 15px;
  text-indent: 0;
  line-height: 1;
  top: 20px;
  width: 15px;
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  font-weight: bold;
}
.e-m-grid-core .e-m-grid-hcell .e-m-title {
  position: relative;
  cursor: pointer;
}
.e-m-grid-core .e-m-grid-ascending .e-m-icon-grid-sort:before {
  content: "\ea95";
}
.e-m-grid-core .e-m-grid-descending .e-m-icon-grid-sort:before {
  content: "\ea93";
}
.e-m-grid-core .e-hide {
  display: none !important;
}
.e-m-grid-core.e-m-pagertype-scrollable .e-m-grid-inrpgr {
  width: 100%;
}
.e-m-grid-core.e-m-pagertype-scrollable .e-m-page-num:first-child {
  margin-left: 0;
}
.e-m-grid-core .e-m-grid-content .e-m-grid-table {
  border-collapse: separate;
  border-style: solid;
  border-width: 1px 0;
}
.e-m-grid-core .e-m-grid-table {
  width: 100%;
  table-layout: fixed;
  border: 0 none;
  border-collapse: separate;
  border-spacing: 0;
}
.e-m-grid-core.e-m-grid-pager {
  height: 30px;
  border: 0;
  padding: 0;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
}
.e-m-grid-core.e-m-grid-pager.e-m-pager-normal {
  bottom: auto;
  position: static;
  display: block;
}
.e-m-grid-core.e-m-pager-normal .e-m-grid-pwrapper {
  margin-top: 0;
}
.e-m-grid-core .e-m-grid-content {
  overflow: hidden;
  border-width: 0;
  border-style: none;
}
.e-m-grid-core .e-m-grid-hdrsettings {
  overflow-x: hidden;
}
.e-m-grid-core .e-m-grid-header {
  overflow: hidden;
  background-repeat: repeat-x;
  background-attachment: scroll;
  background-position: 0 0;
  line-height: 3em;
  outline: none;
}
.e-m-grid-core .e-m-caption-row {
  height: 30px;
  border-collapse: collapse;
  border-width: 1px 0 0;
  border-style: solid;
  text-indent: 15px;
  padding-top: 10px;
  padding-bottom: 5px;
  font-size: 17px;
  text-align: left;
  position: relative;
}
.e-m-grid-core .e-m-grid-hdrcell {
  border-width: 0 1px 0 0;
  border-style: solid;
  border-collapse: collapse;
  padding: 0;
  text-align: left;
  vertical-align: middle;
  overflow: hidden;
  height: 50px;
  text-overflow: ellipsis;
}
.e-m-grid-core .e-m-grid-hdrcell.e-m-grid-lastth {
  border-right-width: 0;
}
.e-m-grid-core .e-m-grid-hcelldiv {
  white-space: nowrap;
  height: 50px;
  line-height: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 10px 0 10px;
  font-weight: normal;
  font-size: 17px;
}
.e-m-grid-core .e-m-grid-hcelldiv.e-m-filter {
  padding: 0 20px 0 10px;
}
.e-m-grid-core .e-m-grid-sel-rowhdr {
  overflow: hidden;
  padding: 0;
  border-style: none solid hidden none;
  border-width: 1px;
}
.e-m-grid-core .e-m-grid-rowcell,
.e-m-grid-core .e-m-grid-emptycell {
  border-collapse: collapse;
  border-style: solid;
  border-width: 0 1px 0 0;
  display: table-cell;
  overflow: hidden;
  padding: 0.7em;
  line-height: 1.3em;
  white-space: nowrap;
  width: auto;
  text-overflow: ellipsis;
  font-size: 17px;
  cursor: default;
}
.e-m-grid-core .e-m-grid-rowcell:last-child,
.e-m-grid-core .e-m-grid-emptycell:last-child {
  border-width: 0;
}
.e-m-grid-core .e-m-grid-asc,
.e-m-grid-core .e-m-grid-des,
.e-m-grid-core .e-m-grid-cle {
  display: inline-block;
  background-repeat: no-repeat;
  width: 25px;
  height: 16px;
}
.e-m-grid-core .e-m-grid-first,
.e-m-grid-core .e-m-grid-last,
.e-m-grid-core .e-m-grid-prev,
.e-m-grid-core .e-m-grid-next,
.e-m-grid-core .e-m-grid-dfirst,
.e-m-grid-core .e-m-grid-dlast,
.e-m-grid-core .e-m-grid-dprev,
.e-m-grid-core .e-m-grid-dnext,
.e-m-grid-core .e-m-grid-hfirst,
.e-m-grid-core .e-m-grid-hlast,
.e-m-grid-core .e-m-grid-hprev,
.e-m-grid-core .e-m-grid-hnext {
  font-family: 'Segoe UI';
}
.e-m-grid-core .e-m-grid-first,
.e-m-grid-core .e-m-grid-last,
.e-m-grid-core .e-m-grid-prev,
.e-m-grid-core .e-m-grid-next {
  position: relative;
}
.e-m-grid-core .e-m-grid-first:before,
.e-m-grid-core .e-m-grid-dfirst:before,
.e-m-grid-core .e-m-grid-hfirst:before {
  content: "\e696";
  position: absolute;
  left: 4px;
  top: 12px;
}
.e-m-grid-core .e-m-grid-next:before,
.e-m-grid-core .e-m-grid-dnext:before,
.e-m-grid-core .e-m-grid-hnext:before {
  content: "\e695";
  position: absolute;
  left: 5px;
  top: 12px;
}
.e-m-grid-core .e-m-grid-last:before,
.e-m-grid-core .e-m-grid-dlast:before,
.e-m-grid-core .e-m-grid-hlast:before {
  content: "\e6dc";
  position: absolute;
  left: 5px;
  top: 12px;
}
.e-m-grid-core .e-m-grid-prev:before,
.e-m-grid-core .e-m-grid-dprev:before,
.e-m-grid-core .e-m-grid-hprev:before {
  content: "\e6db";
  position: absolute;
  left: 4px;
  top: 12px;
}
.e-m-grid-core .e-m-grid-container {
  position: relative;
  margin-top: 0px;
}
.e-m-grid-core .e-m-grid-container.e-m-filter {
  margin-top: -41px;
}
.e-m-grid-header .e-m-grid-hdrsettings,
.e-m-grid-hdrcell .e-m-grid-col-elem,
.e-m-grid-core .e-m-grid-container,
.e-m-grid-core .e-m-grid-pwrapper {
  transition-property: margin-top;
  -moz-transition-property: margin-top;
  -webkit-transition-property: margin-top;
  -o-transition-property: margin-top;
  -ms-transition-property: margin-top;
  transition-duration: 0.5s;
  -moz-transition-duration: 0.5s;
  -webkit-transition-duration: 0.5s;
  -o-transition-duration: 0.5s;
  -ms-transition-duration: 0.5s;
}
.e-m-grid-core .e-m-grid-fbardiv {
  height: 40px;
  position: relative;
  border-width: 1px 0 0;
  border-style: solid;
  display: flexbox;
  display: flex;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -o-box;
  visibility: hidden;
}
.e-m-grid-core .e-m-grid-fbardiv .e-m-grid-finput {
  height: 40px;
  flex: 1;
  -moz-box-flex: 1;
  -ms-flex: 1;
  -o-box-flex: 1;
  -webkit-box-flex: 1;
}
.e-m-grid-hdrcell .e-m-grid-col-elem {
  height: 50px;
  width: 100%;
}
.e-m-grid-hdrcell .e-m-grid-col-elem.e-m-grid-coldesc {
  height: 50px;
  width: 100%;
  position: relative;
}
.e-m-grid-hdrcell .e-m-grid-col-elem .e-m-icon-grid-fltr {
  height: 50px;
  background-repeat: no-repeat;
  display: block;
  width: 25px;
  line-height: 52px;
  position: absolute;
  top: -2px;
  right: -8px;
  font-size: 13px;
}
.e-m-grid-core .e-m-grid-fbardiv .e-m-grid-finput form {
  height: 40px;
  margin: 0;
  width: 100%;
  padding: 0;
  position: relative;
}
.e-m-grid-core .e-m-grid-fbardiv input {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  background: inherit;
  border-style: solid;
  border-width: 0;
  padding: 4px 0;
  position: absolute;
  text-indent: 30px;
  height: 30px;
  width: 100%;
  top: 5px;
}
.e-m-grid-core .e-m-grid-fbardiv input::-webkit-input-placeholder {
  text-indent: 30px;
}
.e-m-android .e-m-grid-core .e-m-grid-fbardiv .e-m-placeholder-css {
  padding: 0px;
  padding-left: 4px;
}
.e-m-grid-core .e-m-grid-fbardiv .e-m-icon-grid-search {
  font-size: 13px;
  height: 20px;
  left: 10px;
  line-height: 25px;
  position: absolute;
  top: 9px;
  width: 20px;
}
.e-m-grid-core .e-m-grid-fbardiv .e-m-icon-grid-search:before {
  content: "\e9bc";
}
.e-m-grid-core .e-m-grid-fbardiv .e-m-icon-grid-clear {
  font-size: 13px;
  height: 20px;
  line-height: 24px;
  position: absolute;
  right: 3px;
  text-indent: -2px;
  top: 10px;
  width: 20px;
}
.e-m-grid-core .e-m-grid-fbardiv .e-m-icon-grid-clear:before {
  content: "\eafb";
}
.e-m-grid-header .e-m-grid-inrhdr {
  height: 50px;
  overflow: hidden;
}
.e-m-grid-core .e-m-grid-col-elem .e-m-icon-grid-fltr:before {
  content: "\eaf5";
}
.e-m-grid-core .e-m-grid-pwrapper {
  margin-top: 46px;
  height: 45px;
}
.e-m-grid-core .e-m-grid-inrpgr {
  margin: 0;
  border: 0;
  background: none;
  height: 45px;
  padding: 2px;
  border: 1px;
  display: inline-block;
  padding: 0;
}
.e-m-grid-core .e-m-pagertype-normal .e-m-grid-inrpgr {
  padding: 10px 0;
  height: 25px;
}
.e-m-grid-core.e-m-grid-pager {
  height: 47px;
  background: none;
}
.e-m-grid-core.e-m-pagertype-scrollable .e-m-grid-inrpgr {
  width: 100%;
}
.e-m-grid-pwrapper .e-m-grid-inrpgr .e-m-grid-ptxt {
  height: 25px;
  line-height: 25px;
  position: relative;
  top: 0px;
  width: 30px;
  display: inline-block;
  margin-left: 15px;
}
.e-m-grid-core.e-m-pagertype-scrollable .e-m-grid-ptxt {
  width: 40%;
  overflow: hidden;
  top: 10px;
  margin: 0;
  vertical-align: top;
  padding: 0;
}
.e-m-grid-core .e-m-grid-inrpgr .e-m-fontimage {
  display: inline-block;
  margin-left: 15px;
}
.e-m-grid-core.e-m-pagertype-scrollable .e-m-pscroll {
  margin-left: 0;
  height: 25px;
  white-space: nowrap;
  left: 0;
}
.e-m-grid-core.e-m-pagertype-scrollable .e-m-grid-ptxt .e-m-page-num,
.e-m-grid-core .e-m-grid-pwrapper .e-m-fontimage {
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: inline-block;
  border-width: 0px;
  border-radius: 15px;
  margin-left: 15px;
  line-height: 28px;
  font-size: 16px;
  vertical-align: top;
}
.e-m-grid-core.e-m-pagertype-scrollable .e-m-grid-ptxt .e-m-page-num.e-m-page-large {
  width: 30px;
  border-radius: 10px;
}
.e-m-grid-core.e-m-pagertype-scrollable .e-m-grid-ptxt .e-m-page-num {
  line-height: 23px;
  font-size: 15px;
}
.e-m-grid-core.e-m-pagertype-scrollable .e-m-page-num:first-child {
  margin-left: 0;
}
.e-m-grid-core .e-m-grid-inrpgr .e-m-grid-hprev,
.e-m-grid-core .e-m-grid-inrpgr .e-m-grid-hnext,
.e-m-grid-core .e-m-grid-inrpgr .e-m-grid-hfirst,
.e-m-grid-core .e-m-grid-inrpgr .e-m-grid-hlast {
  display: none;
  height: 24px;
  margin-left: 0px;
  margin-top: 0px;
  position: absolute;
  width: 24px;
  top: 0;
}
.e-m-grid-core.e-m-pagertype-scrollable .e-m-grid-ptxt {
  width: 531px;
}
.e-m-grid-core.e-m-pager-fixed .e-m-grid-pwrapper {
  margin-top: 0;
}
.e-m-grid-core .e-m-grid-inrpgr .e-m-grid-last,
.e-m-grid-core .e-m-grid-inrpgr .e-m-grid-hlast {
  margin-right: 15px;
}
.e-m-grid-container .e-m-grid-core {
  display: inline-block;
}
.e-m-grid-core .e-m-grid-colnav {
  font-size: 24px;
  height: 30px;
  position: absolute;
  right: 5px;
  text-align: center;
  top: 7px;
  width: 30px;
  text-indent: 0;
}
.e-m-grid-core .e-m-grid-colnav:before {
  content: "\e691";
}
.e-m-grid-core.e-m-grid-colnavcontainer {
  width: 100%;
  border: none;
}
.e-m-mobile .e-m-grid-core.e-m-grid-colnavcontainer {
  height: 100%;
}
/*Grid End*/

/* Radial Menu */

.e-m-radial {
  height: 50px;
  width: 50px;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
  border-radius: 50%;
}
.e-m-radialright {
  float: right;
}
.e-m-radiallefttop,
.e-m-radialrighttop {
  top: 0px;
}
.e-m-radialleftbottom,
.e-m-radialrightbottom {
  bottom: 0px;
}
.e-m-radialmenu.e-m-overlow {
  overflow: hidden;
  width: 100%;
}
.e-m-radialmenu .e-m-abs {
  position: absolute;
}
.e-m-radialmenu .e-m-rel {
  position: relative;
}
.e-m-radialmenu.e-m-displaynone,
.e-m-radialmenu .e-m-displaynone {
  display: none;
}
.e-m-radialmenu .e-m-itemdisabled {
  opacity: 0.4;
  pointer-events: none;
}
.e-m-radialmenu .e-m-pathdisabled {
  pointer-events: none;
}
.e-m-radialmenu .e-m-badgetext {
  text-anchor: middle;
  font-size: 10px;
  font-family: 'Segoe UI';
}
.e-m-radialmenu .e-m-radialshow,
.e-m-radialslider.e-m-slider-show {
  -moz-animation: radialshow 300ms;
  -webkit-animation: radialshow 300ms;
  -o-animation: radialshow 300ms;
  -ms-animation: radialshow 300ms;
  animation: radialshow 300ms;
}
@-moz-keyframes radialshow {
  from {
    -moz-transform: rotate(-70deg) scale(0.7);
  }
  to {
    -moz-transform: rotate(0deg) scale(1);
  }
}
@-webkit-keyframes radialshow {
  from {
    -webkit-transform: rotate(-70deg) scale(0.7);
  }
  to {
    -webkit-transform: rotate(0deg) scale(1);
  }
}
@-o-keyframes radialshow {
  from {
    -o-transform: rotate(-70deg) scale(0.7);
  }
  to {
    -o-transform: rotate(0deg) scale(1);
  }
}
@-ms-keyframes radialshow {
  from {
    -ms-transform: rotate(-70deg) scale(0.7);
  }
  to {
    -ms-transform: rotate(0deg) scale(1);
  }
}
@keyframes radialshow {
  from {
    transform: rotate(-70deg) scale(0.7);
  }
  to {
    transform: rotate(0deg) scale(1);
  }
}
.e-m-radialmenu .e-m-radialhide,
.e-m-radialslider.e-m-slider-hide {
  -moz-animation: radialhide 300ms;
  -webkit-animation: radialhide 300ms;
  -o-animation: radialhide 300ms;
  -ms-animation: radialhide 300ms;
  animation: radialhide 300ms;
}
@-moz-keyframes radialhide {
  from {
    -moz-transform: rotate(0deg) scale(1);
  }
  to {
    -moz-transform: rotate(-70deg) scale(0.7);
  }
}
@-webkit-keyframes radialhide {
  from {
    -webkit-transform: rotate(0deg) scale(1);
  }
  to {
    -webkit-transform: rotate(-70deg) scale(0.7);
  }
}
@-o-keyframes radialhide {
  from {
    -o-transform: rotate(0deg) scale(1);
  }
  to {
    -o-transform: rotate(-70deg) scale(0.7);
  }
}
@-ms-keyframes radialhide {
  from {
    -ms-transform: rotate(0deg) scale(1);
  }
  to {
    -ms-transform: rotate(-70deg) scale(0.7);
  }
}
@keyframes radialhide {
  from {
    transform: rotate(0deg) scale(1);
  }
  to {
    transform: rotate(-70deg) scale(0.7);
  }
}
.e-m-radialmenu .e-m-scalehide {
  -moz-animation: scalehide 300ms;
  -webkit-animation: scalehide 300ms;
  -o-animation: scalehide 300ms;
  -ms-animation: scalehide 300ms;
  animation: scalehide 300ms;
}
@-moz-keyframes scalehide {
  from {
    -moz-transform: scale(1);
  }
  to {
    -moz-transform: scale(0.4);
  }
}
@-webkit-keyframes scalehide {
  from {
    -webkit-transform: scale(1);
  }
  to {
    -webkit-transform: scale(0.4);
  }
}
@-o-keyframes scalehide {
  from {
    -o-transform: scale(1);
  }
  to {
    -o-transform: scale(0.4);
  }
}
@-ms-keyframes scalehide {
  from {
    -ms-transform: scale(1);
  }
  to {
    -ms-transform: scale(0.4);
  }
}
@keyframes scalehide {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0.4);
  }
}
.e-m-radialmenu .e-m-scaleshow {
  -moz-animation: scaleshow 300ms;
  -webkit-animation: scaleshow 300ms;
  -o-animation: scaleshow 300ms;
  -ms-animation: scaleshow 300ms;
  animation: scaleshow 300ms;
}
@-moz-keyframes scaleshow {
  from {
    -moz-transform: scale(0.4);
  }
  to {
    -moz-transform: scale(1);
  }
}
@-webkit-keyframes scaleshow {
  from {
    -webkit-transform: scale(0.4);
  }
  to {
    -webkit-transform: scale(1);
  }
}
@-o-keyframes scaleshow {
  from {
    -o-transform: scale(0.4);
  }
  to {
    -o-transform: scale(1);
  }
}
@-ms-keyframes scaleshow {
  from {
    -ms-transform: scale(0.4);
  }
  to {
    -ms-transform: scale(1);
  }
}
@keyframes scaleshow {
  from {
    transform: scale(0.4);
  }
  to {
    transform: scale(1);
  }
}
/* Radial Menu End */

/* Revamp */

/* SVG Icons */

@font-face {
  font-family: 'ejmmobilefonts';
  src: url('images/ejmmobilefonts.eot?5sqe2e');
  src: url('images/ejmmobilefonts.eot?5sqe2e#iefix') format('embedded-opentype'), url('images/ejmmobilefonts.ttf?5sqe2e') format('truetype'), url('images/ejmmobilefonts.woff?5sqe2e') format('woff'), url('images/ejmmobilefonts.svg?5sqe2e#ejmmobilefonts') format('svg');
  font-weight: normal;
  font-style: normal;
}
[class^="e-m-icon-"]::before,
[class*=" e-m-icon-"]::before {
  font-family: 'ejmmobilefonts' !important;
}
[class^="e-m-icon-"],
[class*=" e-m-icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'ejmmobilefonts';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* better font rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.e-m-icon-plus:before {
  content: "\ea81";
}
.e-m-icon-cut:before {
  content: "\eaa4";
}
.e-m-icon-copy:before {
  content: "\e955";
}
.e-m-icon-save:before {
  content: "\eacb";
}
.e-m-icon-search:before {
  content: "\e9bc";
}
.e-m-icon-check:before,
.e-m-lv-checked .e-m-lv-content:before {
  content: "\ea9c";
}
.e-m-icon-eye:before {
  content: "\ead1";
}
.e-m-icon-up:before {
  content: "\ea95";
}
.e-m-icon-down:before {
  content: "\ea93";
}
.e-m-icon-back:before,
.e-m-btn-back:before {
  content: "\e947";
}
.e-m-icon-mail:before {
  content: "\ea69";
}
.e-m-icon-compose:before {
  content: "\eac1";
}
.e-m-icon-reply:before {
  content: "\e9c3";
}
.e-m-icon-delete:before {
  content: "\e90e";
}
.e-m-icon-settings:before {
  content: "\e9bd";
}
.e-m-icon-arrow:before {
  content: "\eaf8";
}
.e-m-icon-overflow:before {
  content: "\eaf9";
}
.e-m-icon-filter:before {
  content: "\eaf5";
}
.e-m-icon-grid-filter:before {
  content: "\eaf6";
}
.e-m-icon-close:before,
.e-m-icon-clear:before {
  content: "\eafb";
}
/* Svg Icons ends */

/*Appview CSS*/

.appview,
.subview {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
}
.page {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
}
.appview.rendering,
.subview.rendering,
.appview.rendering *,
.subview.rendering * {
  visibility: hidden;
}
* {
  font-family: sans-serif;
  margin: 0;
  padding: 0;
}
*,
*::before,
*::after {
  box-sizing: border-box;
}
.appview.e-m-ios7 {
  cursor: pointer;
}
.appview.e-m-ios * {
  font-family: Helvetica, sans-serif;
}
.appview.e-m-android * {
  font-family: roboto, Sans-Serif;
}
.e-m-ios7,
.e-m-ios7 [class^="e-"]:not(text),
.e-m-ios7 [class*=" e-"]:not(text) {
  font-family: "Helvetica Neue", "Roboto", "Segoe UI", sans-serif;
  font-weight: normal;
}
.e-m-android,
.e-m-android [class^="e-"]:not(text),
.e-m-android [class*=" e-"]:not(text) {
  font-family: Roboto;
  font-size: 18px;
  font-weight: normal;
}
.e-m-windows,
.e-m-windows [class^="e-"]:not(text),
.e-m-windows [class*=" e-"]:not(text),
.e-m-flat,
.e-m-flat [class^="e-"]:not(text),
.e-m-flat [class*=" e-"]:not(text) {
  font-family: Segoe UI;
  font-weight: normal;
}
.e-m-waitingpopup {
  height: 100%;
  opacity: 0.4;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 100008;
  background: black;
}
.e-m-waitingpopup .e-m-image {
  background-repeat: no-repeat;
  color: White;
  height: 32px;
  margin: 0 auto;
  padding-top: 6px;
  position: relative;
  text-indent: 37px;
  width: 115px;
  top: calc(100% / 2 - 16px);
}
.e-m-ios7.e-m-waitingpopup .e-m-image {
  background: url("images/waitingpopup/waitingpopup_ios7.gif") no-repeat left center;
}
.e-m-android.e-m-waitingpopup .e-m-image {
  background: url("images/waitingpopup/waitingpopup_android.GIF") no-repeat left center;
}
.e-m-windows.e-m-waitingpopup .e-m-image {
  background: url("images/waitingpopup/waitingpopup_windows.gif") no-repeat left center;
  padding-top: 20px;
}
.e-m-windows.e-m-tablet.e-m-waitingpopup .e-m-image {
  background: url("images/waitingpopup/waitingpopup_winrt.gif") no-repeat left center;
  padding-top: 4px;
}
/*Appview CSS ends*/

/*Common Styles*/

html {
  height: 100%;
  -ms-touch-action: none;
  min-height: 100%;
  touch-action: none;
  position: relative;
}
body.e-m-viewport {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
  line-height: normal;
}
body {
  -webkit-touch-callout: none;
  -webkit-text-size-adjust: none;
  -webkit-user-select: none;
  -ms-content-zooming: none;
  content-zooming: none;
  -ms-user-select: none;
  overflow: hidden;
  width: 100%;
  height: 100%;
  background-size: 100%;
  margin: 0px;
}
.e-js {
  display: block;
}
.e-m-overlay {
  position: fixed;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  width: 100%;
  background: none repeat scroll 0 0 black;
  z-index: 100004;
  opacity: 0.2;
}
a.e-m-link-inherit {
  text-decoration: none;
}
.e-m-skew-bottomleft,
.e-m-skew-bottomright,
.e-m-skew-topright,
.e-m-skew-topleft,
.e-m-skew-top,
.e-m-skew-bottom,
.e-m-skew-left,
.e-m-skew-right,
.e-m-skew-center {
  transition: transform 250ms;
}
.e-m-skew-top {
  transform: perspective(1000px) rotateX(10deg);
  -webkit-transform: perspective(1000) rotate3d(1, 0, 0, 10deg);
  -moz-transform: perspective(1000) rotate3d(1, 0, 0, 10deg);
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-skew-bottom {
  transform: perspective(1000px) rotateX(-10deg);
  -webkit-transform: perspective(1000) rotate3d(1, 0, 0, -10deg);
  -moz-transform: perspective(1000) rotate3d(1, 0, 0, -10deg);
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-skew-left {
  transform: perspective(1000px) rotateY(-10deg);
  -webkit-transform: perspective(1000) rotateY(-10deg);
  -moz-transform: perspective(1000) rotateY(-10deg);
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-skew-right {
  transform: perspective(1000px) rotateY(10deg);
  -webkit-transform: perspective(1000) rotateY(10deg);
  -moz-transform: perspective(1000) rotateY(10deg);
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-skew-center {
  transform: perspective(1000px) scale(0.95);
  -webkit-transform: perspective(1000) scale(0.95);
  -moz-transform: perspective(1000) scale(0.95);
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-tile-backface {
  backface-visibility: hidden;
  -ms-backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  position: absolute;
}
.e-m-skew-topleft {
  transform: perspective(1000px) rotateX(10deg) rotateY(-10deg);
}
.e-m-skew-topright {
  transform: perspective(1000px) rotateX(10deg) rotateY(10deg);
}
.e-m-skew-bottomright {
  transform: perspective(1000px) rotateX(-10deg) rotateY(10deg);
}
.e-m-skew-bottomleft {
  transform: perspective(1000px) rotateX(-10deg) rotateY(-10deg);
}
.e-m-font-small {
  font-size: 14px;
}
.e-m-font-normal {
  font-size: 17px;
}
.e-m-font-large {
  font-size: 21px;
}
.e-m-state-disabled {
  opacity: 0.3;
  filter: alpha(opacity=40);
  pointer-events: none;
}
ul {
  padding: 0;
  margin: 0;
}
.e-m-state-disabled,
.e-m-state-disabled a {
  cursor: default;
}
.e-m-shadow {
  -moz-box-shadow: 0 0 12px rgba(0, 0, 0, 0.6);
  -webkit-box-shadow: 0 0 12px rgba(0, 0, 0, 0.6);
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.6);
}
.e-m-corner-br {
  -moz-border-radius-bottomright: 0.6em;
  -webkit-border-bottom-right-radius: 0.6em;
  border-bottom-right-radius: 0.6em;
}
.e-m-corner-bl {
  -moz-border-radius-bottomleft: 0.6em;
  -webkit-border-bottom-left-radius: 0.6em;
  border-bottom-left-radius: 0.6em;
}
.e-m-corner-tl {
  -moz-border-radius-topleft: 0.6em;
  -webkit-border-top-left-radius: 0.6em;
  border-top-left-radius: 0.6em;
}
.e-m-corner-tr {
  -moz-border-radius-topright: 0.6em;
  -webkit-border-top-right-radius: 0.6em;
  border-top-right-radius: 0.6em;
}
.e-m-corner-top {
  -moz-border-radius-topleft: 0.6em;
  -webkit-border-top-left-radius: 0.6em;
  border-top-left-radius: 0.6em;
  -moz-border-radius-topright: 0.6em;
  -webkit-border-top-right-radius: 0.6em;
  border-top-right-radius: 0.6em;
}
.e-m-corner-bottom {
  -moz-border-radius-bottomleft: 0.6em;
  -webkit-border-bottom-left-radius: 0.6em;
  border-bottom-left-radius: 0.6em;
  -moz-border-radius-bottomright: 0.6em;
  -webkit-border-bottom-right-radius: 0.6em;
  border-bottom-right-radius: 0.6em;
}
.e-m-corner-left {
  -moz-border-radius-bottomleft: 0.6em;
  -webkit-border-bottom-left-radius: 0.6em;
  border-bottom-left-radius: 0.6em;
  -moz-border-radius-topleft: 0.6em;
  -webkit-border-top-left-radius: 0.6em;
  border-top-left-radius: 0.6em;
}
.e-m-corner-right {
  -moz-border-radius-bottomright: 0.6em;
  -webkit-border-bottom-right-radius: 0.6em;
  border-bottom-right-radius: 0.6em;
  -moz-border-radius-topright: 0.6em;
  -webkit-border-top-right-radius: 0.6em;
  border-top-right-radius: 0.6em;
}
.e-m-corner-all {
  -moz-border-radius: 0.6em;
  -webkit-border-radius: 0.6em;
  border-radius: 0.6em;
}
.e-m-border-left {
  border-left: 1px solid;
}
.e-m-border-right {
  border-right: 1px solid;
}
.e-m-border-bottom {
  border-bottom: 1px solid;
}
.e-m-border-top {
  border-top: 1px solid;
}
.e-m-clearall {
  padding: 0;
  margin: 0;
}
.e-m-auto-hw {
  height: auto;
  width: auto;
}
.e-m-margin {
  margin: 0;
}
.e-m-padding {
  padding: 0;
}
.e-m-border-none {
  border: 0;
}
.e-m-rel {
  position: relative;
}
.e-m-abs {
  position: absolute;
}
.e-m-fixed {
  position: fixed;
}
.e-m-display-none {
  display: none;
}
.e-m-overflow-visible {
  overflow: visible;
}
.e-m-image {
  border: 0 none transparent;
}
.e-m-remove-shadow {
  text-decoration: none;
  text-shadow: 0 0 0;
}
.e-m-state-hide,
.e-m-hide {
  display: none;
}
.e-m-state-block {
  display: block;
}
*[class^="e-m-"] input,
*[class^="e-m-"] button {
  box-shadow: none;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  text-shadow: none;
  text-decoration: none;
  box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0.2);
  moz-box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0.2);
  webkit-box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0.2);
  -webkit-appearance: none;
  outline: none;
}
input[type="button"]::-moz-focus-inner,
button::-moz-focus-inner {
  border: 0;
}
*[class^="e-m-"] a,
*[class^="e-m-"] a:hover {
  text-decoration: none;
  -webkit-appearance: none;
}
.e-m-inline {
  display: inline-block;
}
.e-m-show {
  display: block;
}
.e-m-hidden {
  visibility: hidden;
}
.e-m-visible {
  visibility: visible;
}
.e-m-text-center {
  text-align: center;
}
.e-m-user-select {
  -webkit-user-select: none;
  /* Chrome all / Safari all */
  -moz-user-select: none;
  /* Firefox all */
  -ms-user-select: none;
  /* IE 10+ */
  /* No support for these yet, use at own risk */
  -o-user-select: none;
  user-select: none;
}
/*Common style Ends*/

/*Accordion*/

.e-m-acc .e-m-acc-item,
.e-m-acc .e-m-acc-content-wrapper {
  list-style: none;
  overflow: hidden;
}
.e-m-acc-item.e-ripple {
  position: relative;
  overflow: hidden;
}
.e-m-acc .e-m-acc-content-wrapper {
  padding: 14px 16px;
}
.e-m-acc {
  outline: none;
  border-top: 1px solid;
}
.e-m-acc .e-m-acc-item,
.e-m-acc .e-m-acc-content-wrapper {
  border-bottom: 1px solid;
  border-left: 1px solid;
  border-right: 1px solid;
}
.e-m-acc .e-m-acc-item.e-m-state-active {
  border: none;
}
.e-m-acc .e-m-acc-item {
  position: relative;
}
.e-m-acc .e-m-acc-item:before {
  position: absolute;
  font-weight: 600;
  transition: transform 300ms linear 0s;
  -webkit-transition: -webkit-transform 300ms linear 0s;
}
/*Accordion Ends*/

/*Button*/

.e-m-btn {
  white-space: nowrap;
}
.e-m-btn.e-ripple {
  overflow: hidden;
  position: relative;
}
.e-m-btn-image {
  cursor: pointer;
  outline: 0 none;
}
.e-m-btn-large {
  width: 100%;
  text-align: center;
}
.e-m-btn-back.e-m-btn-image:before {
  font-size: 22px;
  position: relative;
  top: 4px;
  right: 4px;
}
.e-m-actionlink.e-m-btn {
  text-decoration: none;
}
.e-m-btn-image.e-m-btn-image {
  background-repeat: no-repeat;
  background-size: 24px 24px;
  background-position: 5px 3px;
}
.e-m-btn-image.e-m-btn-imageonly {
  min-width: 0px;
  width: 34px;
  height: 30px;
  overflow: hidden;
}
.e-m-btn-image.e-m-image-left.e-m-btn-image {
  background-position: left center;
  padding-left: 34px;
}
.e-m-btn-image.e-m-image-right.e-m-btn-image {
  background-position: right center;
  padding-right: 34px;
}
.e-m-btn-image.e-m-btn-imageonly.e-m-btn-image {
  height: 36px;
  width: 36px;
}
.e-m-btn.e-m-btn-image.e-m-image-right:before,
.e-m-actionlink.e-m-ios7.e-m-btn.e-m-btn-image.e-m-image-right:before {
  float: right;
  left: 21px;
  position: relative;
  top: 16px;
}
.e-m-btn.e-m-btn-image.e-m-image-left:before,
.e-m-actionlink.e-m-ios7.e-m-btn.e-m-btn-image.e-m-image-left:before {
  float: left;
  position: relative;
  right: 24px;
}
/*Button Ends*/

/*NavBar*/

.e-m-navbar {
  width: 100%;
  z-index: 2;
  overflow: hidden;
}
.e-m-navbar.e-m-navbar-top.e-m-navbar-header.e-m-navbar-template,
.e-m-navbar.e-m-navbar-bottom.e-m-navbar-header.e-m-navbar-template,
.e-m-navbar.e-m-navbar-top.e-m-navbar-toolbar.e-m-navbar-template,
.e-m-navbar.e-m-navbar-bottom.e-m-navbar-toolbar.e-m-navbar-template {
  height: auto;
  padding: 0px;
}
.e-m-navbar.e-m-navbar-top {
  top: 0;
}
.e-m-navbar.e-m-navbar-bottom {
  bottom: 0;
}
.e-m-navbar .e-m-navbar-text {
  left: 0;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.e-m-navbar .e-m-navbar-text.e-m-title-left {
  text-align: left;
}
.e-m-navbar .e-m-navbar-text.e-m-title-center {
  text-align: center;
}
.e-m-navbar .e-m-navbar-text.e-m-title-right {
  text-align: right;
}
.e-m-navbar.e-m-navbar-hide {
  display: none;
}
/*Button Related Styles*/

.e-m-navbar .e-m-navbar-right {
  float: right;
}
.e-m-navbar .e-m-navbar-icon,
.e-m-navbar .e-m-navbar-ellipsis {
  box-shadow: none;
}
/*Toolbar Related Styles*/

.e-m-navbar.e-m-navbar-toolbar.e-m-navbar-bottom {
  padding: 0;
}
.e-m-navbar.e-m-navbar-toolbar.e-m-navbar-bottom .e-m-navbar-container {
  width: 100%;
  display: table;
  table-layout: fixed;
}
.e-m-navbar.e-m-navbar-toolbar.e-m-navbar-bottom .e-m-navbar-icon {
  display: table-cell;
}
.e-m-navbar.e-m-navbar-toolbar.e-m-navbar-bottom .e-m-navbar-icon.e-m-hide {
  display: none;
}
.e-m-navbar.e-m-navbar-toolbar.e-m-navbar-top .e-m-navbar-icon {
  float: left;
}
.e-m-navbar.e-m-navbar-top .e-m-navbar-container {
  position: absolute;
  right: 0;
  top: 0;
}
.e-m-navbar.e-m-navbar-top .e-m-navbar-container.e-m-margin-right {
  right: 56px;
}
.e-m-navbar.e-m-navbar-top .e-m-navbar-text.e-m-margin-right-1 {
  margin-right: 56px;
}
.e-m-navbar.e-m-navbar-top .e-m-navbar-text.e-m-margin-right-2 {
  margin-right: 112px;
}
.e-m-navbar.e-m-navbar-top .e-m-navbar-text.e-m-margin-right-3 {
  margin-right: 168px;
}
.e-m-navbar .e-m-overflow-container {
  display: none;
}
.e-m-navbar .e-m-navbar-icon {
  text-align: center;
  list-style: none;
  overflow: hidden;
  position: relative;
}
.e-m-navbar .e-m-navbar-icon.e-m-nav-badge::after {
  border-radius: 50%;
  content: attr(badgeValue);
  font-size: 10px;
  line-height: 18px;
  height: 18px;
  min-width: 18px;
  position: absolute;
}
.e-m-navbar .e-m-navbar-ellipsis {
  width: 56px;
  text-align: center;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
}
.e-m-navbar.e-m-navbar-toolbar.e-m-navbar-bottom .e-m-group-icons {
  width: auto;
  margin: auto;
}
.e-m-navbar.e-m-navbar-toolbar.e-m-navbar-bottom .e-m-group-icons.e-m-more {
  padding-right: 56px;
}
.e-m-navbar.e-m-navbar-toolbar.e-m-navbar-bottom .e-m-group-icons .e-m-navbar-icon.e-m-nav-badge::after,
.e-m-navbar.e-m-navbar-top .e-m-navbar-icon.e-m-nav-badge::after {
  left: 50%;
}
@media (max-width: 640px) {
  .e-m-navbar.e-m-navbar-bottom .e-m-split-icons .e-m-navbar-icon.e-m-nav-badge::after {
    left: 50%;
  }
}
@media (min-width: 640px) {
  .e-m-navbar.e-m-navbar-bottom .e-m-split-icons .e-m-navbar-icon.e-m-nav-badge::after {
    left: 75%;
  }
}
/*NavBar Ends*/

/* Rotator starts*/

.e-m-rotator {
  overflow: hidden;
  position: relative;
}
.e-m-rotator .e-m-sv-container {
  position: relative;
  font-size: 0;
}
.e-m-rotator .e-m-horizontal {
  white-space: nowrap;
}
.e-m-rotator .e-m-sv-container .e-m-item {
  display: inline-block;
  margin: 0;
  height: 100%;
  width: 100%;
  position: relative;
  vertical-align: top;
}
.e-m-rotator-spanleft {
  display: inline-block;
  width: 100%;
  text-align: center;
}
.e-m-rotator-image {
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  width: 100%;
  margin: auto;
  height: 80%;
}
.e-m-rotator-p {
  text-align: center;
  margin-top: 20px;
}
.e-m-rotator .e-m-abs .e-m-sv-pager {
  border-radius: 8px 8px 8px 8px;
  display: inline-block;
  height: 8px;
  margin: 2px;
  width: 8px;
  border-width: 1px;
  border-style: solid;
}
.e-m-rotator .e-m-abs.e-m-sv-pagerbottom {
  bottom: 5px;
}
.e-m-rotator .e-m-abs.e-m-sv-pagertop {
  top: 5px;
}
.e-m-rotator .e-m-abs.e-m-sv-pagerright,
.e-m-rotator .e-m-abs.e-m-sv-pagerleft {
  width: 15px;
  line-height: 2px;
}
.e-m-rotator .e-m-abs.e-m-sv-pagerright {
  right: 0;
}
.e-m-rotator .e-m-abs.e-m-sv-pagerleft {
  left: 0;
}
.e-m-rotator .e-m-abs .e-m-active {
  border: 0 none;
  height: 8px;
  width: 8px;
}
/*Rotator Ends*/

/*Dialog Core starts*/

.e-m-dialog {
  bottom: 0px;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100005;
  overflow: hidden;
}
.e-m-dialog .e-m-dlg-container {
  margin: auto;
  top: 50%;
  left: 50%;
  -ms-transform: translateX(-50%) translateY(-50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.e-m-dialog.e-m-custom .e-m-dlg-content {
  margin: 16px;
}
.e-m-dialog .e-m-dlg-content {
  overflow: hidden;
}
.e-m-dialog .e-m-dlg-container {
  z-index: 100005;
}
.e-m-dialog .e-m-dlg-container.e-m-dlg-maxwidth {
  max-width: 320px;
}
.e-m-dialog .e-m-dlg-hdr {
  font-size: 18px;
}
.e-m-dialog .e-m-dlg-btn {
  cursor: pointer;
  border: medium none;
}
.e-m-dialog.e-m-hide .e-m-overlay {
  opacity: 0;
}
.e-m-windows.e-m-dialog .e-m-dlg-hideanimate,
.e-m-flat.e-m-dialog .e-m-dlg-hideanimate {
  -moz-animation: winflat_dlg_hide 0.2s;
  -webkit-animation: winflat_dlg_hide 0.2s;
  -o-animation: winflat_dlg_hide 0.2s;
  -ms-animation: winflat_dlg_hide 0.2s;
  animation: winflat_dlg_hide 0.2s;
}
@-webkit-keyframes winflat_dlg_hide {
  from {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  to {
    transform: translateX(-50%) translateY(-50%) translateZ(-100px) scale(1.1);
    opacity: 0;
  }
}
@-moz-keyframes winflat_dlg_hide {
  from {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  to {
    transform: translateX(-50%) translateY(-50%) translateZ(-100px) scale(1.1);
    opacity: 0;
  }
}
@-o-keyframes winflat_dlg_hide {
  from {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  to {
    transform: translateX(-50%) translateY(-50%) translateZ(-100px) scale(1.1);
    opacity: 0;
  }
}
@-ms-keyframes winflat_dlg_hide {
  from {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  to {
    transform: translateX(-50%) translateY(-50%) translateZ(-100px) scale(1.1);
    opacity: 0;
  }
}
@keyframes winflat_dlg_hide {
  from {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  to {
    transform: translateX(-50%) translateY(-50%) translateZ(-100px) scale(1.1);
    opacity: 0;
  }
}
.e-m-windows.e-m-dialog .e-m-dlg-showanimate,
.e-m-flat.e-m-dialog .e-m-dlg-showanimate {
  -moz-animation: winflat_dlg_show 0.2s;
  -webkit-animation: winflat_dlg_show 0.2s;
  -o-animation: winflat_dlg_show 0.2s;
  -ms-animation: winflat_dlg_show 0.2s;
  animation: winflat_dlg_show 0.2s;
}
@-webkit-keyframes winflat_dlg_show {
  from {
    transform: translateX(-50%) translateY(-50%) translateZ(-100em) scale(1.1);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
}
@-moz-keyframes winflat_dlg_show {
  from {
    transform: translateX(-50%) translateY(-50%) translateZ(-100em) scale(1.1);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
}
@-o-keyframes winflat_dlg_show {
  from {
    transform: translateX(-50%) translateY(-50%) translateZ(-100em) scale(1.1);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
}
@-ms-keyframes winflat_dlg_show {
  from {
    transform: translateX(-50%) translateY(-50%) translateZ(-100em) scale(1.1);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
}
@keyframes winflat_dlg_show {
  from {
    transform: translateX(-50%) translateY(-50%) translateZ(-100em) scale(1.1);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
}
/*Dialog Core end*/

/*Tab core starts*/

/*Media query for responsive*/

@media all and (max-width: 480px) and (orientation: portrait), only screen and (min-width: 480px) and (max-width: 640px) and (orientation: portrait), all and (max-width: 800px) and (orientation: landscape) {
  .e-m-tab.e-js.e-m-ios7 {
    table-layout: fixed;
    display: table;
  }
  .e-m-tab.e-js.e-m-ios7 .e-m-tab-item {
    display: table-cell;
    width: 100%;
  }
}
.e-m-tab-top .e-m-tab-state-hide,
.e-m-tab-bottom .e-m-tab-state-hide {
  display: none;
}
.e-m-tab.e-m-tab-top {
  top: 0px;
}
.e-m-tab.e-m-tab-bottom {
  bottom: 0px;
}
.e-m-tab {
  left: 0px;
  right: 0px;
  color: white;
  width: 100%;
  text-align: center;
  padding: 0px;
  margin: 0px;
  z-index: 10;
  table-layout: fixed;
  display: table;
  box-sizing: border-box;
  position: absolute;
}
.e-m-tab .e-m-tab-item {
  list-style: outside none none;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  display: table-cell;
  white-space: nowrap;
}
.e-m-tab .e-m-tab-item:before {
  line-height: normal;
  display: block;
}
.e-m-tab-content .e-m-content {
  overflow: hidden;
  width: 100%;
  height: 100%;
}
.e-m-abs.e-m-tab-content-wrapper .e-m-tab-content {
  position: absolute;
}
.e-m-rel.e-m-tab-content-wrapper .e-m-tab-content {
  position: relative;
}
.e-m-tab-content {
  height: 100%;
  bottom: 0;
  overflow: hidden;
  width: 100%;
}
.e-m-tab-top .e-m-tab-content {
  height: auto;
  top: 0px;
}
.e-m-tab-content-wrapper {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  overflow: hidden;
}
.e-m-tab .e-m-tab-item.e-m-state-disabled {
  opacity: 0.3;
  border-top: 4px solid groove;
}
.e-m-tab-badge::after {
  border-radius: 50%;
  content: attr(badgeValue);
  font-size: 10px;
  line-height: 18px;
  min-height: 18px;
  min-width: 18px;
  position: absolute;
  text-align: center;
}
.e-m-tab-content-image .e-m-tab-item:before,
.e-m-tab-content-bothblock .e-m-tab-item:before {
  font-size: 20px;
  position: relative;
  font-family: ejmmobilefonts;
}
/*Tab core Ends*/

/*Menu core starts*/

.e-m-menu {
  z-index: 100006;
}
.e-ripple.e-m-menuitem {
  overflow: hidden;
  position: relative;
}
.e-m-menu li {
  list-style-type: none;
}
.e-m-menu .e-m-menu-btn {
  outline: 0 none;
}
.e-m-menu .e-m-menu-title {
  width: 100%;
  display: block;
  text-align: center;
}
.e-m-menu-overlay.e-m-menu-popover {
  transition-duration: 250ms;
  -webkit-transition-duration: 250ms;
  pointer-events: none;
}
.e-m-menu-overlay.e-m-menu-actionsheet.e-m-overlay-hide,
.e-m-menu-overlay.e-m-menu-popup.e-m-overlay-hide,
.e-m-menu-overlay.e-m-menu-popover.e-m-overlay-hide {
  display: none;
}
.e-m-menu-overlay {
  opacity: 0.2;
}
.e-m-menu.e-m-menu-actionsheet {
  width: auto;
  margin: auto;
  padding-bottom: 0px;
  margin-bottom: 0px;
  transition-duration: 350ms;
  transition-property: bottom;
  -webkit-transition-duration: 350ms;
  -webkit-transition-property: bottom;
  left: 0px;
  right: 0px;
}
.e-m-menu-overlay.e-m-menu-popover {
  transition-duration: 250ms;
  -webkit-transition-duration: 250ms;
  pointer-events: none;
}
.e-m-menu.e-m-menu-popover {
  width: 285px;
}
.e-m-menu.e-m-menu-popover .e-m-menu-title {
  font-size: 20px;
  font-weight: bold;
  padding: 10px 0;
}
.e-m-menu .e-m-menuitem {
  border-bottom: 1px solid;
}
.e-m-menu .e-m-menuitem .e-m-menu-btn {
  border-radius: 0;
  -webkit-border-radius: 0;
}
.e-m-menu.e-m-menu-actionsheet .e-m-menu-title {
  border-bottom: 1px solid;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  -webkit-border-top-left-radius: 5px;
  -webkit-border-top-right-radius: 5px;
  padding: 10px;
  border-bottom-color: #cfcfd2;
}
.e-m-menu .e-m-menu-btn {
  border-style: none;
  border-width: 0;
  display: block;
  position: relative;
  box-shadow: none;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 40px;
  line-height: 40px;
  color: #007aff;
  font-size: 14px;
  padding: 0 16px 14px;
  text-decoration: none;
  cursor: pointer;
  white-space: nowrap;
}
.e-m-menu.e-m-menu-popover div.e-m-icon-arrow:before {
  font-size: 18px;
}
.e-m-menu.e-m-menu-popover div.e-m-icon-arrow {
  position: absolute;
  margin-top: -14px;
}
.e-m-menu.e-m-menu-popover div.e-m-icon-arrow.e-m-bottom {
  margin-top: -6px;
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
}
.e-m-menu.e-m-menu-popup .e-m-menuitem {
  min-width: 200px;
}
.e-m-menu-cancelbtn {
  margin-top: 10px;
}
.e-m-menulist {
  margin-bottom: 0px;
}
.e-m-menu.e-m-menu-popup .e-m-menutemplate {
  min-width: 200px;
}
/*ListView core starts*/

.e-m-lv {
  list-style: none;
  font-size: 20px;
  padding: 0px;
  margin: 0px;
}
.e-m-lv-item {
  position: relative;
  display: block;
  overflow: hidden;
}
.e-m-lv-group {
  font-weight: bold !important;
  height: 30px;
  line-height: 30px;
  text-indent: 5px;
  display: block;
}
.e-m-lv-content {
  height: 40px;
  line-height: 40px;
  font-size: 20px;
  display: block;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  padding-left: 10px;
  color: inherit;
  text-decoration: none;
  /*z-index: 2;*/
  margin-bottom: 1px;
}
/*Swipe Features*/

.e-m-lv-slideitem {
  position: absolute;
  top: 0;
  height: 100%;
  right: 0;
  -webkit-transform: translateX(100%);
  transform: translateX(100%);
}
.e-m-lv-slideitem .e-m-lv-delete {
  padding: 0 30px;
  align-items: center;
  position: relative;
  left: 0;
  display: inline-block;
  line-height: 41px;
}
.e-m-lv-swipeout {
  -webkit-transition-duration: 300ms;
  -moz-transition-duration: 300ms;
  -ms-transition-duration: 300ms;
  transition-duration: 300ms;
  -webkit-transition-property: -webkit-transform;
  -moz-transition-property: -moz-transform;
  -ms-transition-property: -moz-transform;
  transition-property: transform;
}
.e-m-lv-swipe {
  width: 100%;
}
/*Swipe Features*/

.e-m-lv-image {
  background-position: 10px center;
  background-size: 30px 30px;
  text-indent: 40px;
  background-color: transparent;
  background-repeat: no-repeat;
  margin-bottom: 1px;
}
.e-m-lv-content::after {
  border-radius: 50%;
  content: attr(badgeValue);
  display: none;
  float: right;
  font-size: 12px;
  height: 27px;
  line-height: 26px;
  margin: 6px 6px 0 0;
  text-align: center;
  text-indent: 0;
  width: 27px;
}
.e-m-lv-badge::after {
  display: inline;
}
.e-m-lv-content::before {
  display: none;
  height: 30px;
  position: absolute;
  right: 0px;
  width: 30px;
  font-family: ejmmobilefonts;
}
.e-m-lv-item.e-m-lv-checked .e-m-lv-content::before {
  display: inline;
}
.e-m-lv-image.e-m-lv-content::before {
  right: 35px;
}
.e-m-lv-badge.e-m-lv-content::before {
  right: 30px;
}
.e-m-lv-image.e-m-lv-badge.e-m-lv-content::before {
  right: 65px;
}
/*ListView core ends*/

/*Date Picker start*/

.e-m-datepickerwrapper {
  width: 100%;
}
.e-m-datepickerwrapper .e-m-datepicker {
  cursor: pointer;
  background-image: none;
  display: inline-block;
  outline: 0 none;
  width: 100%;
}
.e-m-dp .e-m-dpwrap .e-m-dp {
  display: table-cell;
}
/*Date Picker end*/

/*Time Picker start*/

.e-m-timepicker {
  cursor: pointer;
  background-image: none;
  display: inline-block;
  outline: 0 none;
  width: 100%;
}
.e-m-tp-dialog.e-m-dialog.e-m-hide {
  display: block;
  visibility: hidden;
}
.e-m-tp-dialog.e-m-confirm.e-m-dialog.e-m-twelve .e-m-dlg-container {
  min-width: 280px;
  max-width: 310px;
}
.e-m-tp-dialog.e-m-confirm.e-m-dialog.e-m-twentyfour .e-m-dlg-container {
  min-width: 190px;
  max-width: 220px;
}
.e-m-tp-dialog.e-m-dialog .e-m-dlg-container .e-m-dlg-content {
  margin: 0;
}
.e-m-tp-dialog.e-m-dialog .e-m-tpinner {
  padding: 0 20px;
}
.e-m-tp-dialog.e-m-dialog .e-m-tp-header .e-m-tp-time {
  float: left;
}
.e-m-tp {
  position: relative;
  display: table;
  margin: auto;
}
.e-m-tp .e-m-tpwrap .e-m-tp {
  width: 90px;
  display: table-cell;
}
.e-m-tp .e-m-tpwrap .e-m-tp.e-m-display-none {
  display: none;
}
.e-m-tp .e-m-tpwrap .e-m-tp .e-m-timewrapper {
  width: 45px;
}
.e-m-tp .e-m-tpwrap .e-m-tp.e-m-tphours .e-m-timewrapper {
  margin-left: 0;
}
.e-m-tp .e-m-tpwrap .e-m-tp.e-m-tpmins .e-m-timewrapper {
  margin: auto;
}
.e-m-tp-dialog.e-m-twentyfour .e-m-tp .e-m-tpwrap .e-m-tp.e-m-tpmins .e-m-timewrapper {
  margin: 0;
  margin-left: auto;
}
.e-m-tp .e-m-tpwrap .e-m-tp.e-m-tpmeridians .e-m-timewrapper {
  margin-left: auto;
}
.e-m-tp .e-m-tpwrap .e-m-col-fill.e-m-high {
  height: 90px;
}
.e-m-tp .e-m-tpwrap .e-m-col-fill.e-m-medium {
  height: 45px;
}
.e-m-tp .e-m-tpwrap .e-m-col-fill.e-m-low {
  height: 0;
}
.e-m-tp .e-m-tpwrap .e-m-text {
  height: 45px;
  text-align: center;
  line-height: 45px;
}
/*Time Picker end*/

/*Scroll Panel Core*/

.e-m-scrollpanel {
  height: auto;
  width: auto;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow: hidden;
}
.e-m-scrollpanel.e-m-scroll-native {
  overflow: auto;
}
.e-m-scrollpanel .e-m-scrollcontent {
  -ms-transform-origin: left top 0;
  /* IE 9 */
  -webkit-transform-origin: left top 0;
  /* Chrome, Safari, Opera */
  transform-origin: left top 0;
}
.e-m-scroll-refresher {
  top: -45px;
  position: absolute;
  left: 0;
  right: 0;
  height: 45px;
  width: auto;
}
.e-m-scroll-refreshcontent {
  display: table;
  margin: 0 auto;
  text-align: center;
}
.e-m-scroll-refreshcontent .e-m-left {
  display: table-cell;
  vertical-align: middle;
}
.e-m-scroll-refreshcontent .e-m-right {
  display: table-cell;
  height: 45px;
  padding-left: 5px;
  vertical-align: middle;
}
.e-m-scroll-refreshcontent .e-m-icon {
  height: 32px;
  width: 32px;
  display: block;
  transition-duration: 300ms;
}
.e-m-scroll-refreshcontent .e-m-pull {
  background-image: url("images/scrollpanel/pullarrow.png");
  background-size: 100% 100%;
}
.e-m-scroll-refreshcontent .e-m-refresh {
  background-image: url("images/scrollpanel/refreshing-ios-light.gif");
  background-size: 100% 100%;
}
.e-m-scroll-refreshcontent .e-m-rotate {
  -moz-transform: rotate(180deg);
  /* Mozila */
  -webkit-transform: rotate(180deg);
  /* Ch <36, Saf 5.1+, iOS, An =<4.4.4 */
  -ms-transform: rotate(180deg);
  /* IE 9 */
  transform: rotate(180deg);
  /* IE 10, Fx 16+, Op 12.1+ */

}
/*Scroll Panel Native Customize*/

.e-m-scroll-native.e-m-scroll-wrapper {
  overflow: hidden;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  -webkit-overflow-scrolling: touch;
  -webkit-transform: translate3d(0, 0, 0);
  -ms-scroll-chaining: none;
}
.e-m-scroll-native.e-m-scroll-wrapper.e-m-vertical > div {
  -ms-touch-action: pan-y;
  touch-action: pan-y;
}
.e-m-scroll-native.e-m-scroll-wrapper.e-m-horizontal > div {
  -ms-touch-action: pan-x;
  touch-action: pan-x;
}
.e-m-scroll-native.e-m-scroll-wrapper,
.e-m-scroll-native .e-m-scroll-content {
  -webkit-box-orient: vertical;
  -webkit-box-align: stretch;
  -webkit-box-flex: 1;
}
.e-m-scroll-native .e-m-scroll-content {
  margin-bottom: -2px;
}
.e-m-scroll-native.e-m-scroll-box {
  display: -webkit-box;
}
.e-m-scroll-native.e-m-scroll-wrapper.e-m-hidescrollbar {
  -ms-overflow-style: none;
}
.e-m-hidescrollbar::-webkit-scrollbar {
  display: none;
}
.e-m-scroll-native.e-m-scroll-wrapper.e-m-zoom {
  -ms-content-zooming: none;
}
/* END Scroll Panel Native Customize*/

/* ScrollPanel Scrollbar */

.e-m-sbw {
  position: absolute;
  z-index: 100007;
}
.e-m-sbw .e-m-sb {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  position: absolute;
}
.e-m-sbw.e-m-hr {
  left: 2px;
  right: 2px;
  bottom: 0;
}
.e-m-sbw.e-m-ver {
  bottom: 2px;
  top: 2px;
  right: 1px;
}
.e-m-sbw.e-m-ver .e-m-sb {
  width: 100%;
}
.e-m-sbw.e-m-hr .e-m-sb {
  height: 100%;
}
/* END ScrollPanel Scrollbar  */

/*Scrollpanel end*/

/*SplitPane core starts*/

.e-m-splitpane {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
}
.e-m-pane {
  top: 0;
  bottom: 0;
  width: auto;
  height: auto;
  -moz-transition: transform 250ms ease 0s;
  -o-transition: transform 250ms ease 0s;
  -webkit-transition: transform 250ms ease 0s;
  transition: transform 250ms ease 0s;
  -moz-backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -ms-transform-origin: left center 0;
  -webkit-transform-origin: left center 0;
  transform-origin: left center 0;
}
.e-m-splitpane .e-m-sp-left {
  left: 0;
  border-right: 1px solid;
}
.e-m-splitpane .e-m-sp-right {
  right: 0;
  border-left: 1px solid;
}
.e-m-splitpane.e-m-ios7 .e-m-sp-content.e-m-pane {
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.5);
}
.e-m-sp-content {
  left: 0;
  right: 0;
  z-index: 10006;
  overflow: hidden;
}
.e-m-opening.e-m-pane,
.e-m-opened.e-m-pane {
  z-index: 10004;
}
.e-m-sp-left,
.e-m-sp-right {
  z-index: 10000;
}
.e-m-type-overlay.e-m-sp-left,
.e-m-type-overlay.e-m-sp-right,
.e-m-exposed.e-m-pane {
  z-index: 10007;
}
/*SplitPane core ends*/

/*GroupButton core starts*/

.e-m-grpbtn {
  display: table;
  outline: none;
}
.e-m-grpbtn .e-m-btn {
  display: table-cell;
  text-align: center;
  border: none;
  margin: 0px;
}
.e-m-grpbtn .e-m-btn > input {
  display: none;
}
.e-m-grpbtn button.e-m-btn {
  display: inline-block;
}
.e-m-grpbtn .grpimage {
  width: 20px;
  height: 20px;
  float: left;
  background-size: 100% 100%;
  margin-right: 10px;
}
.e-m-grpbtn .e-m-btn.e-m-imageonly {
  background-repeat: no-repeat;
  background-size: 20px 20px;
}
/*GorupButton core ends*/

/*Dropdown starts*/

.e-m-dropdownlist {
  width: 100%;
  height: 40px;
  border-style: solid;
  font-size: 17px;
  text-indent: 10px;
  box-sizing: border-box;
  padding-right: 37px;
}
.e-m-dropdown-wrapper {
  position: relative;
  width: 100%;
}
.e-m-dropdown-wrapper .e-m-icon-down {
  cursor: pointer;
  font-size: 22px;
  height: 31px;
  padding: 3px;
  position: absolute;
  right: 2px;
  top: 7px;
  width: 37px;
}
.e-m-dropdown-wrapper.e-m-state-disabled {
  opacity: 0.3;
  pointer-events: none;
  cursor: default;
}
.e-m-dropdown-wrapper .e-m-target-wrapper {
  position: absolute;
  width: 100%;
  top: 40px;
  margin-top: 5px;
  z-index: 1;
}
/*Dropdown ends*/

/*Tile Starts*/

.e-m-tile .e-m-tile-selected {
  border: 2px solid;
}
.e-m-tile {
  margin: 5px;
}
.e-m-tile .e-m-tile-selected::before {
  border-left: 2px solid;
  border-bottom: 2px solid;
}
.e-m-tab .e-m-tab-item {
  box-sizing: content-box;
}
.e-m-tile .e-m-tile-overlay {
  height: 100%;
  width: 100%;
  display: block;
  position: absolute;
  top: 0px;
  right: 0px;
  left: 0px;
  bottom: 0px;
}
/*Badge*/

.e-m-tile-small.e-m-tile-badge.e-m-badge-position-topright::after {
  top: 5px;
  right: 5px;
}
.e-m-tile-small.e-m-tile-badge.e-m-badge-position-bottomright::after {
  bottom: 5px;
  right: 5px;
}
.e-m-tile.e-m-badge-position-bottomright::after {
  bottom: 6px;
}
.e-m-tile.e-m-caption-outer.e-m-badge-position-bottomright::after {
  bottom: 55px;
}
.e-m-tile.e-m-badge-position-topright::after {
  top: 10px;
}
.e-m-tile.e-m-tile-badge.e-m-tile-badge-value::after {
  content: attr(badgeValue);
  font-size: 14px;
  text-align: center;
}
.e-m-tile.e-m-tile-badge::after {
  font-size: 18px;
  line-height: 18px;
  right: 10px;
  min-height: 18px;
  min-width: 18px;
  position: absolute;
  z-index: 10;
}
/*Caption position*/

.e-m-tile-caption.e-m-tile-caption-icon.e-m-caption-innertop::before {
  top: 10px;
}
.e-m-tile-caption.e-m-tile-caption-icon.e-m-caption-innerbottom::before {
  bottom: 10px;
}
.e-m-tile-caption-text.e-m-caption-innertop::before {
  top: 0px;
  margin-top: 10px;
}
.e-m-tile-caption-text.e-m-caption-innerbottom::before {
  bottom: 0px;
  margin-bottom: 6px;
}
.e-m-tile-caption-text.e-m-caption-innerbottom::before,
.e-m-tile-caption-text.e-m-caption-innertop::before {
  max-height: 38px;
  height: auto;
  left: 0px;
}
.e-m-tile-caption-text.e-m-caption-outer::before {
  bottom: 0px;
  height: 45px;
}
.e-m-tile-caption-text::before {
  content: attr(text);
  width: 100%;
}
.e-m-tile-caption.e-m-tile-caption-icon::before {
  width: 30px;
  height: 30px;
  font-size: 30px;
}
.e-m-tile-caption.e-m-caption-outer.e-m-tile-caption-icon::before {
  bottom: 15px;
}
.e-m-tile-caption::before {
  font-size: 14px;
  position: absolute;
  display: block;
  float: left;
  overflow: hidden;
  z-index: 10;
}
/*Text alignment*/

.e-m-caption-align-right.e-m-caption-innertop.e-m-badge-position-topright.e-m-tile-caption-icon::before,
.e-m-caption-align-right.e-m-caption-innerbottom.e-m-badge-position-bottomright.e-m-tile-caption-icon::before {
  right: 35px;
}
.e-m-caption-align-normal.e-m-tile-caption-icon::before,
.e-m-caption-align-left.e-m-tile-caption-icon::before {
  left: 10px;
}
.e-m-tile-caption-icon.e-m-caption-align-center::before {
  margin-left: -10px;
  left: 50%;
}
.e-m-tile-caption-icon.e-m-caption-align-right::before {
  right: 10px;
}
.e-m-tile-caption-text.e-m-caption-align-right.e-m-caption-innertop.e-m-badge-position-topright::before,
.e-m-tile-caption-text.e-m-caption-align-right.e-m-caption-innerbottom.e-m-badge-position-bottomright::before {
  padding-right: 34px;
}
.e-m-tile-caption-text.e-m-caption-align-normal::before,
.e-m-tile-caption-text.e-m-caption-align-left::before {
  text-align: left;
}
.e-m-tile-caption-text::before {
  left: 0px;
  right: 0px;
  padding-left: 10px;
  padding-right: 10px;
}
.e-m-tile-caption-text.e-m-caption-align-center::before {
  text-align: center;
}
.e-m-tile-caption-text.e-m-caption-align-right::before {
  direction: rtl;
}
.e-m-tile .e-m-tile-image .e-m-tile-image {
  background-repeat: no-repeat;
  overflow: hidden;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagecenter {
  background-position: center center;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagefill {
  background-size: 100% 100%;
  backface-visibility: hidden;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagetopcenter {
  background-position: top center;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagebottomcenter {
  background-position: bottom center;
}
.e-m-tile .e-m-tile-image .e-m-tile-imageleftcenter {
  background-position: left center;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagerightcenter {
  background-position: right center;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagetopleft {
  background-position: top left;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagetopright {
  background-position: top right;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagebottomright {
  background-position: bottom right;
}
.e-m-tile .e-m-tile-image .e-m-tile-imagebottomleft {
  background-position: bottom left;
}
.e-m-tile.e-m-tile-caption.e-m-caption-outer {
  padding: 0px;
}
.e-m-tile:not(.e-m-tile-imagefill):not(.e-m-windows) {
  padding: 10px;
}
.e-m-tile .e-m-tile-template {
  background-size: 100% 100%;
  width: inherit;
  height: inherit;
}
.e-m-tile .e-m-tile-image {
  width: 100%;
  height: 100%;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  position: absolute;
}
.e-m-tile .e-m-tile-image.e-m-tile-flip,
.e-m-tile .e-m-tile-image.e-m-tile-flipback {
  height: auto;
  backface-visibility: hidden;
}
/*Rounded corner*/

.e-m-tile-round-corner.e-m-caption-outer,
.e-m-tile-round-corner .e-m-tile-image {
  border-radius: 10px;
}
.e-m-tile .e-m-tile-selected::before {
  position: absolute;
  display: block;
  content: "";
  height: 5px;
  width: 10px;
  right: 4px;
  z-index: 102;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
/*Tile Animation Starts*/

.e-m-tile.e-m-livetile-enable .e-m-tile-slideback {
  position: absolute;
  transform: translateY(0px);
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -ms-transform: translateY(0px);
  transition-duration: 350ms;
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-tile.e-m-livetile-enable .e-m-tile-slide {
  position: absolute;
  transform: translateY(118%);
  -webkit-transform: translateY(118%);
  -moz-transform: translateY(118%);
  -ms-transform: translateY(118%);
  transition-delay: 350ms;
  -webkit-transition-delay: 350ms;
  -ms-transition-delay: 350ms;
  transition-duration: 0ms;
  -webkit-transition-duration: 0ms;
  -moz-transition-duration: 0ms;
  -ms-transition-duration: 0ms;
}
.e-m-tile.e-m-livetile-enable .e-m-tile-slideup {
  position: absolute;
  transform: translateY(-100%);
  -webkit-transform: translateY(-100%);
  -moz-transform: translateY(-100%);
  -ms-transform: translateY(-100%);
  transition-duration: 350ms;
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-tile.e-m-livetile-enable .e-m-tile-carouselback {
  position: absolute;
  transform: translateX(0px);
  -webkit-transform: translateX(0px);
  -moz-transform: translateX(0px);
  -ms-transform: translateX(0px);
  transition-duration: 350ms;
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-tile.e-m-livetile-enable .e-m-tile-carousel {
  position: absolute;
  transform: translateX(100%);
  -webkit-transform: translateX(100%);
  -moz-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transition-delay: 350ms;
  -webkit-transition-delay: 350ms;
  -ms-transition-delay: 350ms;
  transition-duration: 0ms;
  -webkit-transition-duration: 0ms;
  -moz-transition-duration: 0ms;
  -ms-transition-duration: 0ms;
  z-index: -1;
}
.e-m-tile.e-m-livetile-enable .e-m-tile-carouselup {
  position: absolute;
  transform: translateX(-100%);
  -webkit-transform: translateX(-100%);
  -moz-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  transition-duration: 350ms;
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-tile.e-m-livetile-enable .e-m-tile-image.e-m-tile-flip {
  position: absolute;
  transform: perspective(1000px) rotateX(180deg);
  -webkit-transform: perspective(1000) rot ateX(180deg);
  -moz-transform: perspective(1000) rotateX(180deg);
  transition-duration: 350ms;
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-tile.e-m-livetile-enable .e-m-tile-image.e-m-tile-flipback {
  position: absolute;
  transform: perspective(1000px) rotateX(0deg);
  -webkit-transform: perspective(1000) rotateX(0deg);
  -moz-transform: perspective(1000) rotateX(0deg);
  transition-duration: 350ms;
  -webkit-transition-duration: 350ms;
  -moz-transition-duration: 350ms;
}
.e-m-tile.e-m-state-active .e-m-tile-overlay {
  opacity: 0.5;
}
.e-m-tile .e-m-tile-overlay {
  height: 100%;
  width: 100%;
  display: block;
  position: absolute;
  top: 0px;
  right: 0px;
  left: 0px;
  bottom: 0px;
}
/*Tile Ends*/

/*ToggleButton Starts*/

.e-m-tbutton > input {
  display: none;
}
/*ToggleButton Ends*/

/*input Controls Starts*/

/* Core Common*/

::-ms-reveal {
  display: none;
}
::-ms-clear {
  display: none;
}
.e-m-editor * {
  box-sizing: border-box;
}
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.e-m-editor.e-m-input-wrapper {
  border-style: solid;
  margin-bottom: 8px;
  position: relative;
  outline-width: 0;
  height: 48px;
  width: 100%;
  padding: 11px 6px 11px 0px;
}
.e-m-editor.e-m-input-wrapper .e-m-textbox {
  border: none;
  position: absolute;
  height: 100%;
  text-indent: 12px;
  left: 0;
  top: 0;
  width: 100%;
  padding: 11px 6px 11px 0px;
}
textarea.e-m-editor.e-m-input-wrapper {
  resize: none;
  height: 75px;
  padding-left: 12px;
  padding-bottom: 6px;
  padding-right: 6px;
  padding-top: 6px;
  -webkit-appearance: none;
}
.e-m-editor.e-m-input-wrapper.e-m-mask {
  text-indent: 12px;
  border-radius: 0px;
}
.e-m-ios7.e-m-editor.e-m-input-wrapper.e-m-mask {
  border-radius: 3px;
}
/* Right Icons */

.e-m-editor.e-m-input-wrapper span {
  position: absolute;
  right: 0;
  width: 46px;
  height: auto;
  top: 0;
  bottom: 0;
  text-align: center;
  cursor: default;
}
.e-m-editor.e-m-input-wrapper span.right-clear-btn.e-m-icon-clear {
  display: block;
}
.e-m-editor.e-m-input-wrapper span.down-btn {
  right: 40px;
}
.e-m-editor.e-m-input-wrapper span[class^="e-m-icon-"]::before,
.e-m-editor.e-m-input-wrapper span[class*="e-m-icon-"]::before {
  position: absolute;
  vertical-align: middle;
  line-height: 46px;
  padding: 0 17px;
  right: 0px;
  font-size: 12px;
}
/* End Right Icons */

/* Border Styles */

.e-m-editor.e-m-input-wrapper.e-m-box {
  border-width: 1px;
}
.e-m-editor.e-m-input-wrapper.e-m-none {
  border-width: 0;
}
.e-m-editor.e-m-input-wrapper.e-m-line {
  border-width: 0 0 1px;
}
/* End Border Styles*/

input.e-m-textbox::-webkit-input-placeholder,
input.e-m-editor::-webkit-input-placeholder,
textarea.e-m-editor::-webkit-input-placeholder,
input.e-m-textbox:-moz-placeholder,
input.e-m-editor:-moz-placeholder,
textarea.e-m-editor:-moz-placeholder,
.e-m-editor.e-m-input-wrapper .e-m-textbox:-ms-input-placeholder,
input.e-m-editor:-ms-input-placeholder,
textarea.e-m-editor:-ms-input-placeholder {
  color: #aaaaaa;
}
/*input Controls Ends*/

/**Radial Slider Core Style**/

/*Radial Slider start*/

.e-m-radialslider .e-m-rs-svg {
  overflow: visible;
}
.e-m-radialslider .e-m-rs-marker {
  border-radius: 50%;
  border-style: solid;
  border-width: 1px;
  cursor: move;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  height: 30px;
  width: 30px;
  position: absolute;
}
.e-m-radialmenu .e-m-radialshow,
.e-m-radialslider.e-m-slider-show {
  -moz-animation: radialshow 300ms;
  -webkit-animation: radialshow 300ms;
  -o-animation: radialshow 300ms;
  -ms-animation: radialshow 300ms;
  animation: radialshow 300ms;
}
@-moz-keyframes radialshow {
  from {
    -moz-transform: rotate(-70deg) scale(0.7);
  }
  to {
    -moz-transform: rotate(0deg) scale(1);
  }
}
@-webkit-keyframes radialshow {
  from {
    -webkit-transform: rotate(-70deg) scale(0.7);
  }
  to {
    -webkit-transform: rotate(0deg) scale(1);
  }
}
@-o-keyframes radialshow {
  from {
    -o-transform: rotate(-70deg) scale(0.7);
  }
  to {
    -o-transform: rotate(0deg) scale(1);
  }
}
@-ms-keyframes radialshow {
  from {
    -ms-transform: rotate(-70deg) scale(0.7);
  }
  to {
    -ms-transform: rotate(0deg) scale(1);
  }
}
@keyframes radialshow {
  from {
    transform: rotate(-70deg) scale(0.7);
  }
  to {
    transform: rotate(0deg) scale(1);
  }
}
.e-m-radialmenu .e-m-radialhide,
.e-m-radialslider.e-m-slider-hide {
  -moz-animation: radialhide 300ms;
  -webkit-animation: radialhide 300ms;
  -o-animation: radialhide 300ms;
  -ms-animation: radialhide 300ms;
  animation: radialhide 300ms;
}
@-moz-keyframes radialhide {
  from {
    -moz-transform: rotate(0deg) scale(1);
  }
  to {
    -moz-transform: rotate(-70deg) scale(0.7);
  }
}
@-webkit-keyframes radialhide {
  from {
    -webkit-transform: rotate(0deg) scale(1);
  }
  to {
    -webkit-transform: rotate(-70deg) scale(0.7);
  }
}
@-o-keyframes radialhide {
  from {
    -o-transform: rotate(0deg) scale(1);
  }
  to {
    -o-transform: rotate(-70deg) scale(0.7);
  }
}
@-ms-keyframes radialhide {
  from {
    -ms-transform: rotate(0deg) scale(1);
  }
  to {
    -ms-transform: rotate(-70deg) scale(0.7);
  }
}
@keyframes radialhide {
  from {
    transform: rotate(0deg) scale(1);
  }
  to {
    transform: rotate(-70deg) scale(0.7);
  }
}
.e-m-radialmenu .e-m-scalehide {
  -moz-animation: scalehide 300ms;
  -webkit-animation: scalehide 300ms;
  -o-animation: scalehide 300ms;
  -ms-animation: scalehide 300ms;
  animation: scalehide 300ms;
}
@-moz-keyframes scalehide {
  from {
    -moz-transform: scale(1);
  }
  to {
    -moz-transform: scale(0.4);
  }
}
@-webkit-keyframes scalehide {
  from {
    -webkit-transform: scale(1);
  }
  to {
    -webkit-transform: scale(0.4);
  }
}
@-o-keyframes scalehide {
  from {
    -o-transform: scale(1);
  }
  to {
    -o-transform: scale(0.4);
  }
}
@-ms-keyframes scalehide {
  from {
    -ms-transform: scale(1);
  }
  to {
    -ms-transform: scale(0.4);
  }
}
@keyframes scalehide {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0.4);
  }
}
.e-m-radialmenu .e-m-scaleshow {
  -moz-animation: scaleshow 300ms;
  -webkit-animation: scaleshow 300ms;
  -o-animation: scaleshow 300ms;
  -ms-animation: scaleshow 300ms;
  animation: scaleshow 300ms;
}
@-moz-keyframes scaleshow {
  from {
    -moz-transform: scale(0.4);
  }
  to {
    -moz-transform: scale(1);
  }
}
@-webkit-keyframes scaleshow {
  from {
    -webkit-transform: scale(0.4);
  }
  to {
    -webkit-transform: scale(1);
  }
}
@-o-keyframes scaleshow {
  from {
    -o-transform: scale(0.4);
  }
  to {
    -o-transform: scale(1);
  }
}
@-ms-keyframes scaleshow {
  from {
    -ms-transform: scale(0.4);
  }
  to {
    -ms-transform: scale(1);
  }
}
@keyframes scaleshow {
  from {
    transform: scale(0.4);
  }
  to {
    transform: scale(1);
  }
}
/*Radial Slider end*/

/** END Radial Slider Core Style**/

/* CheckBox and RadioButton starts*/

.e-m-input-checkbox {
  border: 2px solid;
  height: 22px;
  width: 22px;
  position: relative;
  margin: 5px;
  top: 6px;
  display: inline-block;
}
.e-m-input-radiobutton.e-ripple,
.e-m-input-checkbox.e-ripple {
  overflow: hidden;
}
.e-m-input-radiobutton {
  border: 2px solid;
  position: relative;
  height: 24px;
  width: 24px;
  margin: 5px;
  top: 6px;
  border-radius: 50%;
  display: inline-block;
}
.e-m-input-label {
  bottom: 5px;
  position: relative;
  -moz-user-select: none;
}
.e-m-input-radiobutton:after {
  content: "";
  position: absolute;
  height: 12px;
  width: 12px;
  line-height: 18px;
  border-radius: 50%;
  top: 4px;
  left: 4px;
}
.e-m-input-checkbox.e-m-state-active:before {
  content: "\ea9c";
  font-size: 16px;
  font-weight: bold;
  font-family: ejmmobilefonts;
  line-height: 18px;
  text-align: center;
  position: absolute;
}
.e-m-input-checkbox.e-m-state-glow:after {
  content: "";
  height: 20px;
  width: 20px;
  border-radius: 50%;
  position: absolute;
  opacity: 0.1;
}
/* CheckBox and RadioButton ends*/

/* Autocomplete starts*/

.e-m-ac-wrapper {
  width: 100%;
  position: relative;
}
.e-m-ac-wrapper .e-m-ac {
  height: 40px;
  width: 100%;
  text-indent: 10px;
  -webkit-appearance: none;
  -moz-appearance: none;
  outline: none;
  box-sizing: border-box;
}
.e-m-ac-wrapper.e-m-ac-search .e-m-ac {
  padding-left: 20px;
  padding-right: 30px;
}
.e-m-ac-wrapper.e-m-state-disabled {
  opacity: 0.3;
  pointer-events: none;
  cursor: default;
}
.e-m-ac-wrapper .e-m-list-wrapper {
  position: absolute;
  width: 100%;
  top: 40px;
  margin-top: 5px;
  z-index: 1;
}
.e-m-ac-wrapper.e-m-ac-search .e-m-icon-search:before,
.e-m-ac-wrapper.e-m-ac-search .e-m-icon-close:before {
  display: inline;
  font-size: 16px;
  height: 20px;
  margin: 0;
  position: absolute;
  top: 12px;
  width: 20px;
}
.e-m-ac-wrapper.e-m-ac-search .e-m-icon-search:before {
  left: 8px;
}
.e-m-ac-wrapper.e-m-ac-search .e-m-icon-close:before {
  right: 3px;
}
/* CheckBox and RadioButton ends*/

/*Slider*/

.e-m-slider {
  margin-left: 20px;
  margin-right: 20px;
}
.e-m-slider.e-m-vertical {
  width: 25px;
  height: auto;
}
.e-m-slider.e-m-horizontal {
  height: 25px;
  width: auto;
}
.e-m-slider .e-m-slider-outer {
  position: relative;
}
.e-m-slider .e-m-slider-inner,
.e-m-slider .e-m-slider-handlein,
.e-m-slider .e-m-slider-handleout {
  position: absolute;
}

.e-m-icon-endless:before {
  content: "\e900";
}
.e-m-icon-agreement:before {
  content: "\e901";
}
.e-m-icon-archive:before {
  content: "\e902";
}
.e-m-icon-audiomixer:before {
  content: "\e903";
}
.e-m-icon-battery:before {
  content: "\e904";
}
.e-m-icon-bell:before {
  content: "\e905";
}
.e-m-icon-biometricreader:before {
  content: "\e906";
}
.e-m-icon-blog:before {
  content: "\e907";
}
.e-m-icon-light:before {
  content: "\e908";
}
.e-m-icon-cd:before {
  content: "\e909";
}
.e-m-icon-certificate:before {
  content: "\e90a";
}
.e-m-icon-repository:before {
  content: "\e90b";
}
.e-m-icon-connector:before {
  content: "\e90c";
}
.e-m-icon-cpu:before {
  content: "\e90d";
}
.e-m-icon-delete:before {
  content: "\e90e";
}
.e-m-icon-devicebell:before {
  content: "\e90f";
}
.e-m-icon-walkietalkie:before {
  content: "\e910";
}
.e-m-icon-directionnorth:before {
  content: "\e911";
}
.e-m-icon-directionsouth:before {
  content: "\e912";
}
.e-m-icon-edit:before {
  content: "\e913";
}
.e-m-icon-firehydrant:before {
  content: "\e914";
}
.e-m-icon-fireplug:before {
  content: "\e915";
}
.e-m-icon-flag:before {
  content: "\e916";
}
.e-m-icon-flashon:before {
  content: "\e917";
}
.e-m-icon-stretchglobe:before {
  content: "\e918";
}
.e-m-icon-horizontalalignleft:before {
  content: "\e919";
}
.e-m-icon-horizontalalignright:before {
  content: "\e91a";
}
.e-m-icon-hourglass:before {
  content: "\e91b";
}
.e-m-icon-funnelglass:before {
  content: "\e91c";
}
.e-m-icon-sandglass:before {
  content: "\e91d";
}
.e-m-icon-hydroelectricpower:before {
  content: "\e91e";
}
.e-m-icon-incomingcalls:before {
  content: "\e91f";
}
.e-m-icon-instrumentscissors:before {
  content: "\e920";
}
.e-m-icon-internet:before {
  content: "\e921";
}
.e-m-icon-keyhash:before {
  content: "\e922";
}
.e-m-icon-map:before {
  content: "\e923";
}
.e-m-icon-lockopen:before {
  content: "\e924";
}
.e-m-icon-lock:before {
  content: "\e925";
}
.e-m-icon-memorycard:before {
  content: "\e926";
}
.e-m-icon-mobile:before {
  content: "\e927";
}
.e-m-icon-phone:before {
  content: "\e928";
}
.e-m-icon-handdevice:before {
  content: "\e929";
}
.e-m-icon-msetting:before {
  content: "\e92a";
}
.e-m-icon-next:before {
  content: "\e92b";
}
.e-m-icon-noise:before {
  content: "\e92c";
}
.e-m-icon-sound:before {
  content: "\e92d";
}
.e-m-icon-paragraph:before {
  content: "\e92e";
}
.e-m-icon-pencil:before {
  content: "\e92f";
}
.e-m-icon-productbox:before {
  content: "\e930";
}
.e-m-icon-reminderring:before {
  content: "\e931";
}
.e-m-icon-remote:before {
  content: "\e932";
}
.e-m-icon-setflag:before {
  content: "\e933";
}
.e-m-icon-signature:before {
  content: "\e934";
}
.e-m-icon-spycamera:before {
  content: "\e935";
}
.e-m-icon-stationary:before {
  content: "\e936";
}
.e-m-icon-thunder:before {
  content: "\e937";
}
.e-m-icon-stopwatch:before {
  content: "\e938";
}
.e-m-icon-usbplug:before {
  content: "\e939";
}
.e-m-icon-volumehigh:before {
  content: "\e93a";
}
.e-m-icon-volumelow:before {
  content: "\e93b";
}
.e-m-icon-addreminder:before {
  content: "\e93c";
}
.e-m-icon-aligncenter:before {
  content: "\e93d";
}
.e-m-icon-alignjustify:before {
  content: "\e93e";
}
.e-m-icon-alignleft:before {
  content: "\e93f";
}
.e-m-icon-alignright:before {
  content: "\e940";
}
.e-m-icon-antennanew:before {
  content: "\e941";
}
.e-m-icon-antenna:before {
  content: "\e942";
}
.e-m-icon-arrowheadleft:before {
  content: "\e943";
}
.e-m-icon-arrowheadright:before {
  content: "\e944";
}
.e-m-icon-attach:before {
  content: "\e945";
}
.e-m-icon-audit:before {
  content: "\e946";
}
.e-m-icon-backward:before {
  content: "\e947";
}
.e-m-icon-bin:before {
  content: "\e948";
}
.e-m-icon-bluetooth:before {
  content: "\e949";
}
.e-m-icon-bookmark:before {
  content: "\e94a";
}
.e-m-icon-banner:before {
  content: "\e94b";
}
.e-m-icon-broadband:before {
  content: "\e94c";
}
.e-m-icon-burndisc:before {
  content: "\e94d";
}
.e-m-icon-calculator:before {
  content: "\e94e";
}
.e-m-icon-calender:before {
  content: "\e94f";
}
.e-m-icon-clipboardnext:before {
  content: "\e950";
}
.e-m-icon-clipboard:before {
  content: "\e951";
}
.e-m-icon-closedbook:before {
  content: "\e952";
}
.e-m-icon-compass:before {
  content: "\e953";
}
.e-m-icon-contacts:before {
  content: "\e954";
}
.e-m-icon-copy:before {
  content: "\e955";
}
.e-m-icon-cursor:before {
  content: "\e956";
}
.e-m-icon-database:before {
  content: "\e957";
}
.e-m-icon-datafiles:before {
  content: "\e958";
}
.e-m-icon-splitdata:before {
  content: "\e959";
}
.e-m-icon-mergedata:before {
  content: "\e95a";
}
.e-m-icon-devicetablet:before {
  content: "\e95b";
}
.e-m-icon-device:before {
  content: "\e95c";
}
.e-m-icon-documentedit:before {
  content: "\e95d";
}
.e-m-icon-document:before {
  content: "\e95e";
}
.e-m-icon-downarrow:before {
  content: "\e95f";
}
.e-m-icon-ear:before {
  content: "\e960";
}
.e-m-icon-customize:before {
  content: "\e961";
}
.e-m-icon-euro-tag:before {
  content: "\e962";
}
.e-m-icon-splitflag:before {
  content: "\e963";
}
.e-m-icon-flashdrive:before {
  content: "\e964";
}
.e-m-icon-forward:before {
  content: "\e965";
}
.e-m-icon-gift:before {
  content: "\e966";
}
.e-m-icon-globe:before {
  content: "\e967";
}
.e-m-icon-harddrive:before {
  content: "\e968";
}
.e-m-icon-hook:before {
  content: "\e969";
}
.e-m-icon-keyaccess:before {
  content: "\e96a";
}
.e-m-icon-key:before {
  content: "\e96b";
}
.e-m-icon-attachment:before {
  content: "\e96c";
}
.e-m-icon-androidlink:before {
  content: "\e96d";
}
.e-m-icon-browserlink:before {
  content: "\e96e";
}
.e-m-icon-sparelink:before {
  content: "\e96f";
}
.e-m-icon-link:before {
  content: "\e970";
}
.e-m-icon-locationpin:before {
  content: "\e971";
}
.e-m-icon-location:before {
  content: "\e972";
}
.e-m-icon-login:before {
  content: "\e973";
}
.e-m-icon-microphone:before {
  content: "\e974";
}
.e-m-icon-moneygold:before {
  content: "\e975";
}
.e-m-icon-mouse:before {
  content: "\e976";
}
.e-m-icon-move:before {
  content: "\e977";
}
.e-m-icon-mssyssetting:before {
  content: "\e978";
}
.e-m-icon-mssystemsetting:before {
  content: "\e979";
}
.e-m-icon-newspaper:before {
  content: "\e97a";
}
.e-m-icon-noreminder:before {
  content: "\e97b";
}
.e-m-icon-openedbook:before {
  content: "\e97c";
}
.e-m-icon-pause:before {
  content: "\e97d";
}
.e-m-icon-phonebook:before {
  content: "\e97e";
}
.e-m-icon-play:before {
  content: "\e97f";
}
.e-m-icon-guage:before {
  content: "\e980";
}
.e-m-icon-qrcode:before {
  content: "\e981";
}
.e-m-icon-reminderok:before {
  content: "\e982";
}
.e-m-icon-reminder:before {
  content: "\e983";
}
.e-m-icon-removereminder:before {
  content: "\e984";
}
.e-m-icon-salesman:before {
  content: "\e985";
}
.e-m-icon-searchadd:before {
  content: "\e986";
}
.e-m-icon-searchremove:before {
  content: "\e987";
}
.e-m-icon-androidsettings:before {
  content: "\e988";
}
.e-m-icon-settings:before {
  content: "\e989";
}
.e-m-icon-windowsshare:before {
  content: "\e98a";
}
.e-m-icon-distribute:before {
  content: "\e98b";
}
.e-m-icon-shoppingbag:before {
  content: "\e98c";
}
.e-m-icon-singlecurlyquotationmark:before {
  content: "\e98d";
}
.e-m-icon-socks:before {
  content: "\e98e";
}
.e-m-icon-sortascending:before {
  content: "\e98f";
}
.e-m-icon-sortdescending:before {
  content: "\e990";
}
.e-m-icon-tag:before {
  content: "\e991";
}
.e-m-icon-timer:before {
  content: "\e992";
}
.e-m-icon-todolist:before {
  content: "\e993";
}
.e-m-icon-top:before {
  content: "\e994";
}
.e-m-icon-trashcan:before {
  content: "\e995";
}
.e-m-icon-uparrow:before {
  content: "\e996";
}
.e-m-icon-user:before {
  content: "\e997";
}
.e-m-icon-volumeicon:before {
  content: "\e998";
}
.e-m-icon-warningshield:before {
  content: "\e999";
}
.e-m-icon-wayboard:before {
  content: "\e99a";
}
.e-m-icon-routeboard:before {
  content: "\e99b";
}
.e-m-icon-navigateboard:before {
  content: "\e99c";
}
.e-m-icon-windowslogin:before {
  content: "\e99d";
}
.e-m-icon-mouse:before {
  content: "\e99e";
}
.e-m-icon-zoomvertical:before {
  content: "\e99f";
}
.e-m-icon-announcement:before {
  content: "\e9a0";
}
.e-m-icon-equalizer:before {
  content: "\e9a1";
}
.e-m-icon-audiowave:before {
  content: "\e9a2";
}
.e-m-icon-billboard:before {
  content: "\e9a3";
}
.e-m-icon-biohazard:before {
  content: "\e9a4";
}
.e-m-icon-bullet:before {
  content: "\e9a5";
}
.e-m-icon-schedule:before {
  content: "\e9a6";
}
.e-m-icon-cassette:before {
  content: "\e9a7";
}
.e-m-icon-cctvcamera:before {
  content: "\e9a8";
}
.e-m-icon-checklist:before {
  content: "\e9a9";
}
.e-m-icon-checktask:before {
  content: "\e9aa";
}
.e-m-icon-cloudcomputing:before {
  content: "\e9ab";
}
.e-m-icon-decreaseindent:before {
  content: "\e9ac";
}
.e-m-icon-discount:before {
  content: "\e9ad";
}
.e-m-icon-easeofaccess:before {
  content: "\e9ae";
}
.e-m-icon-edit1:before {
  content: "\e9af";
}
.e-m-icon-firstaidbox:before {
  content: "\e9b0";
}
.e-m-icon-icons:before {
  content: "\e9b1";
}
.e-m-icon-increaseindent:before {
  content: "\e9b2";
}
.e-m-icon-internetnetwork:before {
  content: "\e9b3";
}
.e-m-icon-menu:before {
  content: "\e9b4";
}
.e-m-icon-network:before {
  content: "\e9b5";
}
.e-m-icon-printer:before {
  content: "\e9b6";
}
.e-m-icon-options:before {
  content: "\e9b7";
}
.e-m-icon-qrcode:before {
  content: "\e9b8";
}
.e-m-icon-redo:before {
  content: "\e9b9";
}
.e-m-icon-rotationlock:before {
  content: "\e9ba";
}
.e-m-icon-router:before {
  content: "\e9bb";
}
.e-m-icon-search:before {
  content: "\e9bc";
}
.e-m-icon-sparesettings:before {
  content: "\e9bd";
}
.e-m-icon-shop:before {
  content: "\e9be";
}
.e-m-icon-slideshow:before {
  content: "\e9bf";
}
.e-m-icon-system:before {
  content: "\e9c0";
}
.e-m-icon-taskview:before {
  content: "\e9c1";
}
.e-m-icon-time:before {
  content: "\e9c2";
}
.e-m-icon-undo:before {
  content: "\e9c3";
}
.e-m-icon-unreadmail:before {
  content: "\e9c4";
}
.e-m-icon-usershield:before {
  content: "\e9c5";
}
.e-m-icon-verticalalignbottom:before {
  content: "\e9c6";
}
.e-m-icon-verticalaligntop:before {
  content: "\e9c7";
}
.e-m-icon-3dglass:before {
  content: "\e9c8";
}
.e-m-icon-aeroplane:before {
  content: "\e9c9";
}
.e-m-icon-plane:before {
  content: "\e9ca";
}
.e-m-icon-flight:before {
  content: "\e9cb";
}
.e-m-icon-alarm:before {
  content: "\e9cc";
}
.e-m-icon-androidaudiomixer:before {
  content: "\e9cd";
}
.e-m-icon-androidequalizer:before {
  content: "\e9ce";
}
.e-m-icon-soundwave:before {
  content: "\e9cf";
}
.e-m-icon-barometer:before {
  content: "\e9d0";
}
.e-m-icon-batterycharging:before {
  content: "\e9d1";
}
.e-m-icon-batterylow:before {
  content: "\e9d2";
}
.e-m-icon-batterysaver:before {
  content: "\e9d3";
}
.e-m-icon-beepsignal:before {
  content: "\e9d4";
}
.e-m-icon-biometricaccess:before {
  content: "\e9d5";
}
.e-m-icon-book:before {
  content: "\e9d6";
}
.e-m-icon-bow:before {
  content: "\e9d7";
}
.e-m-icon-brightness:before {
  content: "\e9d8";
}
.e-m-icon-bullhorn:before {
  content: "\e9d9";
}
.e-m-icon-calculation:before {
  content: "\e9da";
}
.e-m-icon-digicamera:before {
  content: "\e9db";
}
.e-m-icon-lasercamera:before {
  content: "\e9dc";
}
.e-m-icon-mountedcctv:before {
  content: "\e9dd";
}
.e-m-icon-rotatablecctv:before {
  content: "\e9de";
}
.e-m-icon-cd:before {
  content: "\e9df";
}
.e-m-icon-clock:before {
  content: "\e9e0";
}
.e-m-icon-microprocessor:before {
  content: "\e9e1";
}
.e-m-icon-delete1:before {
  content: "\e9e2";
}
.e-m-icon-directioneast:before {
  content: "\e9e3";
}
.e-m-icon-directionwest:before {
  content: "\e9e4";
}
.e-m-icon-eservice:before {
  content: "\e9e5";
}
.e-m-icon-eject:before {
  content: "\e9e6";
}
.e-m-icon-dismiss:before {
  content: "\e9e7";
}
.e-m-icon-remove:before {
  content: "\e9e8";
}
.e-m-icon-film:before {
  content: "\e9e9";
}
.e-m-icon-filmreel:before {
  content: "\e9ea";
}
.e-m-icon-filmroll:before {
  content: "\e9eb";
}
.e-m-icon-filmshooting:before {
  content: "\e9ec";
}
.e-m-icon-freeflag:before {
  content: "\e9ed";
}
.e-m-icon-flashoff:before {
  content: "\e9ee";
}
.e-m-icon-files:before {
  content: "\e9ef";
}
.e-m-icon-folders:before {
  content: "\e9f0";
}
.e-m-icon-gamecontrol:before {
  content: "\e9f1";
}
.e-m-icon-gpsoff:before {
  content: "\e9f2";
}
.e-m-icon-gpson:before {
  content: "\e9f3";
}
.e-m-icon-headphone:before {
  content: "\e9f4";
}
.e-m-icon-hub:before {
  content: "\e9f5";
}
.e-m-icon-id:before {
  content: "\e9f6";
}
.e-m-icon-inbox:before {
  content: "\e9f7";
}
.e-m-icon-internetfacilities:before {
  content: "\e9f8";
}
.e-m-icon-lambda:before {
  content: "\e9f9";
}
.e-m-icon-laptop:before {
  content: "\e9fa";
}
.e-m-icon-lap:before {
  content: "\e9fb";
}
.e-m-icon-lapmachine:before {
  content: "\e9fc";
}
.e-m-icon-letteropen:before {
  content: "\e9fd";
}
.e-m-icon-library:before {
  content: "\e9fe";
}
.e-m-icon-margin:before {
  content: "\e9ff";
}
.e-m-icon-mediafastbackward:before {
  content: "\ea00";
}
.e-m-icon-mediafastforward:before {
  content: "\ea01";
}
.e-m-icon-networkdrives:before {
  content: "\ea02";
}
.e-m-icon-newtag:before {
  content: "\ea03";
}
.e-m-icon-noentry:before {
  content: "\ea04";
}
.e-m-icon-numbering:before {
  content: "\ea05";
}
.e-m-icon-openletter:before {
  content: "\ea06";
}
.e-m-icon-paintbrush:before {
  content: "\ea07";
}
.e-m-icon-pin:before {
  content: "\ea08";
}
.e-m-icon-polaroid:before {
  content: "\ea09";
}
.e-m-icon-dotprinter:before {
  content: "\ea0a";
}
.e-m-icon-matrixprinter:before {
  content: "\ea0b";
}
.e-m-icon-printers:before {
  content: "\ea0c";
}
.e-m-icon-productboxes:before {
  content: "\ea0d";
}
.e-m-icon-radio:before {
  content: "\ea0e";
}
.e-m-icon-walkman:before {
  content: "\ea0f";
}
.e-m-icon-rain:before {
  content: "\ea10";
}
.e-m-icon-ram:before {
  content: "\ea11";
}
.e-m-icon-star:before {
  content: "\ea12";
}
.e-m-icon-readmode:before {
  content: "\ea13";
}
.e-m-icon-saving:before {
  content: "\ea14";
}
.e-m-icon-shirt:before {
  content: "\ea15";
}
.e-m-icon-shoe:before {
  content: "\ea16";
}
.e-m-icon-shoppingadd:before {
  content: "\ea17";
}
.e-m-icon-addtocart:before {
  content: "\ea18";
}
.e-m-icon-shoppingremove:before {
  content: "\ea19";
}
.e-m-icon-shuffle:before {
  content: "\ea1a";
}
.e-m-icon-speakeraudible:before {
  content: "\ea1b";
}
.e-m-icon-teacher:before {
  content: "\ea1c";
}
.e-m-icon-telephone:before {
  content: "\ea1d";
}
.e-m-icon-telephone1:before {
  content: "\ea1e";
}
.e-m-icon-television:before {
  content: "\ea1f";
}
.e-m-icon-television1:before {
  content: "\ea20";
}
.e-m-icon-top2:before {
  content: "\ea21";
}
.e-m-icon-usb:before {
  content: "\ea22";
}
.e-m-icon-useredit:before {
  content: "\ea23";
}
.e-m-icon-novolume:before {
  content: "\ea24";
}
.e-m-icon-walkman:before {
  content: "\ea25";
}
.e-m-icon-warning:before {
  content: "\ea26";
}
.e-m-icon-webpage:before {
  content: "\ea27";
}
.e-m-icon-withinquotes:before {
  content: "\ea28";
}
.e-m-icon-withinquotes1:before {
  content: "\ea29";
}
.e-m-icon-zipfile:before {
  content: "\ea2a";
}
.e-m-icon-zoom:before {
  content: "\ea2b";
}
.e-m-icon-alarmclock:before {
  content: "\ea2c";
}
.e-m-icon-draw:before {
  content: "\ea2d";
}
.e-m-icon-audiodisk:before {
  content: "\ea2e";
}
.e-m-icon-barcode:before {
  content: "\ea2f";
}
.e-m-icon-briefcase:before {
  content: "\ea30";
}
.e-m-icon-camera:before {
  content: "\ea31";
}
.e-m-icon-car:before {
  content: "\ea32";
}
.e-m-icon-chat:before {
  content: "\ea33";
}
.e-m-icon-closed:before {
  content: "\ea34";
}
.e-m-icon-cloud:before {
  content: "\ea35";
}
.e-m-icon-cloudsun:before {
  content: "\ea36";
}
.e-m-icon-contactslist:before {
  content: "\ea37";
}
.e-m-icon-contacts:before {
  content: "\ea38";
}
.e-m-icon-creditcard:before {
  content: "\ea39";
}
.e-m-icon-dataerase:before {
  content: "\ea3a";
}
.e-m-icon-dataexport:before {
  content: "\ea3b";
}
.e-m-icon-datasync:before {
  content: "\ea3c";
}
.e-m-icon-databaseconnection:before {
  content: "\ea3d";
}
.e-m-icon-dislike:before {
  content: "\ea3e";
}
.e-m-icon-documents:before {
  content: "\ea3f";
}
.e-m-icon-download:before {
  content: "\ea40";
}
.e-m-icon-drag:before {
  content: "\ea41";
}
.e-m-icon-earphone:before {
  content: "\ea42";
}
.e-m-icon-editdocument:before {
  content: "\ea43";
}
.e-m-icon-emptyarchive:before {
  content: "\ea44";
}
.e-m-icon-expansion:before {
  content: "\ea45";
}
.e-m-icon-filedelete:before {
  content: "\ea46";
}
.e-m-icon-fullscreen:before {
  content: "\ea47";
}
.e-m-icon-viewscreen:before {
  content: "\ea48";
}
.e-m-icon-garbagefull:before {
  content: "\ea49";
}
.e-m-icon-garbageremove:before {
  content: "\ea4a";
}
.e-m-icon-groupadd:before {
  content: "\ea4b";
}
.e-m-icon-groupdelete:before {
  content: "\ea4c";
}
.e-m-icon-groupmodify:before {
  content: "\ea4d";
}
.e-m-icon-group:before {
  content: "\ea4e";
}
.e-m-icon-headphone1:before {
  content: "\ea4f";
}
.e-m-icon-heart:before {
  content: "\ea50";
}
.e-m-icon-hide:before {
  content: "\ea51";
}
.e-m-icon-information:before {
  content: "\ea52";
}
.e-m-icon-infrared:before {
  content: "\ea53";
}
.e-m-icon-infrared1:before {
  content: "\ea54";
}
.e-m-icon-lcdtv:before {
  content: "\ea55";
}
.e-m-icon-like:before {
  content: "\ea56";
}
.e-m-icon-link5:before {
  content: "\ea57";
}
.e-m-icon-link6:before {
  content: "\ea58";
}
.e-m-icon-link7:before {
  content: "\ea59";
}
.e-m-icon-link8:before {
  content: "\ea5a";
}
.e-m-icon-link9:before {
  content: "\ea5b";
}
.e-m-icon-listen:before {
  content: "\ea5c";
}
.e-m-icon-loading:before {
  content: "\ea5d";
}
.e-m-icon-loading1:before {
  content: "\ea5e";
}
.e-m-icon-loading2:before {
  content: "\ea5f";
}
.e-m-icon-loading3:before {
  content: "\ea60";
}
.e-m-icon-loading4:before {
  content: "\ea61";
}
.e-m-icon-loading5:before {
  content: "\ea62";
}
.e-m-icon-logindoor:before {
  content: "\ea63";
}
.e-m-icon-login1:before {
  content: "\ea64";
}
.e-m-icon-login2:before {
  content: "\ea65";
}
.e-m-icon-login3:before {
  content: "\ea66";
}
.e-m-icon-magnetictape:before {
  content: "\ea67";
}
.e-m-icon-mailbox1:before {
  content: "\ea68";
}
.e-m-icon-mail:before {
  content: "\ea69";
}
.e-m-icon-mail1:before {
  content: "\ea6a";
}
.e-m-icon-mailbox:before {
  content: "\ea6b";
}
.e-m-icon-maximize1:before {
  content: "\ea6c";
}
.e-m-icon-maximize2:before {
  content: "\ea6d";
}
.e-m-icon-maximize3:before {
  content: "\ea6e";
}
.e-m-icon-maximize4:before {
  content: "\ea6f";
}
.e-m-icon-maximize-24:before {
  content: "\ea70";
}
.e-m-icon-maximize-35:before {
  content: "\ea71";
}
.e-m-icon-measurement:before {
  content: "\ea72";
}
.e-m-icon-mediafastbackward2:before {
  content: "\ea73";
}
.e-m-icon-mediafastforward:before {
  content: "\ea74";
}
.e-m-icon-mediaforward:before {
  content: "\ea75";
}
.e-m-icon-mediarewind-back:before {
  content: "\ea76";
}
.e-m-icon-media:before {
  content: "\ea77";
}
.e-m-icon-meidafind:before {
  content: "\ea78";
}
.e-m-icon-messagesent:before {
  content: "\ea79";
}
.e-m-icon-message:before {
  content: "\ea7a";
}
.e-m-icon-messages:before {
  content: "\ea7b";
}
.e-m-icon-minus:before {
  content: "\ea7c";
}
.e-m-icon-movie:before {
  content: "\ea7d";
}
.e-m-icon-openfolder:before {
  content: "\ea7e";
}
.e-m-icon-openmessage:before {
  content: "\ea7f";
}
.e-m-icon-pictures:before {
  content: "\ea80";
}
.e-m-icon-plus:before {
  content: "\ea81";
}
.e-m-icon-maxprinter:before {
  content: "\ea82";
}
.e-m-icon-properties:before {
  content: "\ea83";
}
.e-m-icon-vintageradio:before {
  content: "\ea84";
}
.e-m-icon-windowssettings:before {
  content: "\ea85";
}
.e-m-icon-share:before {
  content: "\ea86";
}
.e-m-icon-shoppingbasket:before {
  content: "\ea87";
}
.e-m-icon-systemsetting:before {
  content: "\ea88";
}
.e-m-icon-systemsettings:before {
  content: "\ea89";
}
.e-m-icon-tags:before {
  content: "\ea8a";
}
.e-m-icon-upload:before {
  content: "\ea8b";
}
.e-m-icon-userdownload:before {
  content: "\ea8c";
}
.e-m-icon-usericon:before {
  content: "\ea8d";
}
.e-m-icon-videocam:before {
  content: "\ea8e";
}
.e-m-icon-videochat:before {
  content: "\ea8f";
}
.e-m-icon-activedirectory:before {
  content: "\ea90";
}
.e-m-icon-addnew:before {
  content: "\ea91";
}
.e-m-icon-arrowheaddown:before {
  content: "\ea92";
}
.e-m-icon-down:before {
  content: "\ea93";
}
.e-m-icon-arrowheadup:before {
  content: "\ea94";
}
.e-m-icon-up:before {
  content: "\ea95";
}
.e-m-icon-assign:before {
  content: "\ea96";
}
.e-m-icon-bringtofront:before {
  content: "\ea97";
}
.e-m-icon-calendar:before {
  content: "\ea98";
}
.e-m-icon-datepick:before {
  content: "\ea99";
}
.e-m-icon-cdcatalog:before {
  content: "\ea9a";
}
.e-m-icon-reward:before {
  content: "\ea9b";
}
.e-m-icon-check:before {
  content: "\ea9c";
}
.e-m-icon-cloud:before {
  content: "\ea9d";
}
.e-m-icon-commandredo:before {
  content: "\ea9e";
}
.e-m-icon-commandreset:before {
  content: "\ea9f";
}
.e-m-icon-commandundo:before {
  content: "\eaa0";
}
.e-m-icon-computerdesktop:before {
  content: "\eaa1";
}
.e-m-icon-computer:before {
  content: "\eaa2";
}
.e-m-icon-connectivityerror:before {
  content: "\eaa3";
}
.e-m-icon-cut:before {
  content: "\eaa4";
}
.e-m-icon-datamerge:before {
  content: "\eaa5";
}
.e-m-icon-datasplit:before {
  content: "\eaa6";
}
.e-m-icon-datasynchronize:before {
  content: "\eaa7";
}
.e-m-icon-displaybrightness:before {
  content: "\eaa8";
}
.e-m-icon-favourite:before {
  content: "\eaa9";
}
.e-m-icon-findreplace:before {
  content: "\eaaa";
}
.e-m-icon-folder:before {
  content: "\eaab";
}
.e-m-icon-medianext:before {
  content: "\eaac";
}
.e-m-icon-mediapause:before {
  content: "\eaad";
}
.e-m-icon-mediaplay:before {
  content: "\eaae";
}
.e-m-icon-playmusic:before {
  content: "\eaaf";
}
.e-m-icon-mediaprevious:before {
  content: "\eab0";
}
.e-m-icon-memberofsecuritygroup:before {
  content: "\eab1";
}
.e-m-icon-messagevoicemail:before {
  content: "\eab2";
}
.e-m-icon-messagewarning:before {
  content: "\eab3";
}
.e-m-icon-minimize:before {
  content: "\eab4";
}
.e-m-icon-mobilephonemessage:before {
  content: "\eab5";
}
.e-m-icon-moneycoin:before {
  content: "\eab6";
}
.e-m-icon-mousedrag:before {
  content: "\eab7";
}
.e-m-icon-coffeemug:before {
  content: "\eab8";
}
.e-m-icon-mug:before {
  content: "\eab9";
}
.e-m-icon-musicicon:before {
  content: "\eaba";
}
.e-m-icon-navigationdownright:before {
  content: "\eabb";
}
.e-m-icon-navigationright:before {
  content: "\eabc";
}
.e-m-icon-navigationupleft:before {
  content: "\eabd";
}
.e-m-icon-orientationlandscape:before {
  content: "\eabe";
}
.e-m-icon-orientationportrait:before {
  content: "\eabf";
}
.e-m-icon-password:before {
  content: "\eac0";
}
.e-m-icon-pen:before {
  content: "\eac1";
}
.e-m-icon-playonce:before {
  content: "\eac2";
}
.e-m-icon-poweroff:before {
  content: "\eac3";
}
.e-m-icon-pressure:before {
  content: "\eac4";
}
.e-m-icon-previous:before {
  content: "\eac5";
}
.e-m-icon-productboxwithdisc:before {
  content: "\eac6";
}
.e-m-icon-boxproduct:before {
  content: "\eac7";
}
.e-m-icon-rating:before {
  content: "\eac8";
}
.e-m-icon-refresh:before {
  content: "\eac9";
}
.e-m-icon-rssfeed:before {
  content: "\eaca";
}
.e-m-icon-save:before {
  content: "\eacb";
}
.e-m-icon-scaletofit:before {
  content: "\eacc";
}
.e-m-icon-sizetofit:before {
  content: "\eacd";
}
.e-m-icon-androidshare:before {
  content: "\eace";
}
.e-m-icon-iosshare:before {
  content: "\eacf";
}
.e-m-icon-shoppingcart:before {
  content: "\ead0";
}
.e-m-icon-eye:before {
  content: "\ead1";
}
.e-m-icon-shrink:before {
  content: "\ead2";
}
.e-m-icon-reduce:before {
  content: "\ead3";
}
.e-m-icon-skew:before {
  content: "\ead4";
}
.e-m-icon-softwaresupdatedincomputer:before {
  content: "\ead5";
}
.e-m-icon-spellcheck:before {
  content: "\ead6";
}
.e-m-icon-stopmedia:before {
  content: "\ead7";
}
.e-m-icon-stopmedia1:before {
  content: "\ead8";
}
.e-m-icon-submit:before {
  content: "\ead9";
}
.e-m-icon-sync:before {
  content: "\eada";
}
.e-m-icon-tabhistory:before {
  content: "\eadb";
}
.e-m-icon-task:before {
  content: "\eadc";
}
.e-m-icon-temporaryfolder:before {
  content: "\eadd";
}
.e-m-icon-textdecoration:before {
  content: "\eade";
}
.e-m-icon-typewriter:before {
  content: "\eadf";
}
.e-m-icon-uparrow:before {
  content: "\eae0";
}
.e-m-icon-uploadfile:before {
  content: "\eae1";
}
.e-m-icon-verticlealigncenter:before {
  content: "\eae2";
}
.e-m-icon-viewdetails:before {
  content: "\eae3";
}
.e-m-icon-detailsview:before {
  content: "\eae4";
}
.e-m-icon-viewlist:before {
  content: "\eae5";
}
.e-m-icon-viewmediumicon:before {
  content: "\eae6";
}
.e-m-icon-volumedown:before {
  content: "\eae7";
}
.e-m-icon-volumedecrease:before {
  content: "\eae8";
}
.e-m-icon-volumemute:before {
  content: "\eae9";
}
.e-m-icon-mute:before {
  content: "\eaea";
}
.e-m-icon-volumespeaker:before {
  content: "\eaeb";
}
.e-m-icon-volumeup:before {
  content: "\eaec";
}
.e-m-icon-volumeincrease:before {
  content: "\eaed";
}
.e-m-icon-waterrecycling:before {
  content: "\eaee";
}
.e-m-icon-wifi:before {
  content: "\eaef";
}
.e-m-icon-wirelessmouse:before {
  content: "\eaf0";
}
.e-m-icon-zoomcorner:before {
  content: "\eaf1";
}
.e-m-icon-cornerzoom:before {
  content: "\eaf2";
}
.e-m-icon-zoomhorizontal:before {
  content: "\eaf3";
}

/*For Mobile both portrait and landscape and small tablets portait mode*/

@media all and (max-width: 480px) and (orientation: portrait), only screen and (min-width: 480px) and (max-width: 640px) and (orientation: portrait), all and (max-width: 800px) and (orientation: landscape) {
  /*Common*/
  
  /*ListView */
  
  .e-m-windows.e-m-lv .e-m-grouped .e-m-list .e-m-list-img {
    left: 0px;
  }
  .e-m-windows.e-m-lv .e-m-list .e-m-list-img {
    left: 20px;
  }
  .e-m-lv.e-m-windows.e-m-light .e-m-list-hdr .e-m-list.e-m-state-active {
    background: transparent;
    border-bottom-color: white;
  }
  .e-m-lv.e-m-windows.e-m-light .e-m-list.e-m-state-active .e-m-list-text {
    color: #111111;
  }
  .e-m-windows.e-m-lv .e-m-list .e-m-list-text {
    float: none;
  }
  .e-m-lv.e-m-windows .e-m-list .e-m-list-anchor {
    padding-top: 8px;
    padding-bottom: 8px;
  }
  .e-m-windows.e-m-lv .e-m-lv-checkdiv.e-m-chkbox.e-m-windows {
    float: left;
    left: 15px;
    top: 4px;
    margin-left: 10px;
  }
  .e-m-windows.e-m-lv .e-m-grouped .e-m-lv-checkdiv.e-m-chkbox.e-m-windows {
    left: 0;
    margin-left: 0;
  }
  .e-m-windows.e-m-lv .e-m-list-text {
    font-size: 20px;
    font-weight: 200;
    left: 17px;
    font-weight: 400;
  }
  .e-m-windows.e-m-lv .e-m-grouped .e-m-list-text.e-m-text {
    padding-left: 0px;
  }
  /*ListView*/
  
  /* Tab Start*/
  
  .e-m-tab.e-m-ios7 .e-m-tab-hdr {
    display: table;
    margin: auto;
    width: 100%;
  }
  .e-m-tab.e-m-ios7 .e-m-tabitem.e-m-user-select {
    width: auto;
  }
  .e-m-tab.e-m-windows .e-m-tab-container .e-m-tab-text {
    font-size: 41px;
    font-weight: normal;
  }
  .e-m-windows.e-m-tab-content .e-m-content-container {
    position: relative;
  }
  /* Tab End*/
  
  /* Toolbar Start*/
  
  .e-m-tb.e-m-windows.e-m-fixed,
  .e-m-tb.e-m-windows.e-m-normal {
    height: 50px;
    transition: height 100ms;
    -webkit-transition: height 100ms;
    -moz-transition: height 100ms;
    -o-transition: height 100ms;
    -transition: height 100ms;
  }
  .e-m-tb.e-m-windows.e-m-dark.e-m-fixed .e-m-tbcontainer,
  .e-m-tb.e-m-windows.e-m-dark.e-m-normal .e-m-ellipsiscontainer {
    background-color: #1e1e1e;
  }
  .e-m-tb.e-m-windows.e-m-fixed .e-m-tbulcontainer,
  .e-m-tb.e-m-windows.e-m-normal .e-m-tbulcontainer {
    padding-top: 10px;
  }
  .e-m-tb.e-m-windows.e-m-fixed .e-m-tbcontainer .e-m-tbulcontainer .e-m-toolbaritem,
  .e-m-tb.e-m-windows.e-m-normal .e-m-tbcontainer .e-m-tbulcontainer .e-m-toolbaritem {
    display: table-cell;
    text-align: center;
    vertical-align: middle;
    width: 48px;
  }
  .e-m-tb.e-m-windows .e-m-tbcontainer .e-m-tbulcontainer .e-m-toolbaritem .e-m-tbicon {
    border-radius: 50%;
    height: 28px;
    width: 28px;
    display: inline-block;
  }
  .e-m-ellipsiscontainer.e-m-scrollpanel.e-m-menuitemcontainer-show {
    display: inline;
  }
  .e-m-tb.e-m-windows .e-m-tbcontainer .e-m-tbulcontainer .e-m-toolbaritem .e-m-tbiconblock {
    font-size: 10px;
    padding-top: 10px;
  }
  .e-m-tb.e-m-windows .e-m-tbcontainer .e-m-toolbaritem-ellipsis .e-m-ellipsis {
    background-repeat: no-repeat;
    right: 6px;
    float: right;
    position: absolute;
    height: 24px;
    width: 24px;
  }
  .e-m-tb.e-m-windows .e-m-tbcontainer .e-m-tbulcontainer .e-m-toolbaritem-ellipsis {
    display: table-cell;
    list-style: none outside none;
  }
  .e-m-tb.e-m-windows .e-m-tbcontainer .e-m-toolbaritem-ellipsis .e-m-ellipsis:before {
    content: "\e6c6";
    font-size: 30px;
    position: absolute;
    right: 5px;
  }
  .e-m-tb.e-m-windows .e-m-tbcontainer .e-m-ellipsiscontainer {
    position: absolute;
    width: 100%;
    height: 120px;
    display: inline;
  }
  .e-m-tb.e-m-windows .e-m-ellipsiscontainer .e-m-menuitem {
    list-style: none outside none;
    transition-property: transform;
    -webkit-transition-property: transform;
  }
  .e-m-tb.e-m-windows .e-m-ellipsiscontainer .e-m-menuitem * {
    font-size: 20px;
  }
  .e-m-tb.e-m-windows .e-m-ellipsiscontainer .e-m-ver .e-m-menuitem.e-m-menumode-portrait {
    padding-left: 20px;
    padding-top: -20px;
  }
  .e-m-tb.e-m-windows .e-m-ellipsiscontainer .e-m-ver .e-m-menuitem.e-m-menumode-landscape {
    padding-left: -20px;
    padding-top: 20px;
  }
  .e-m-tb.e-m-windows .e-m-tbcontainer .e-m-tbulcontainer .e-m-toolbaritem .e-m-fontimage.e-m-tbicon:before {
    font-size: 30px;
    position: relative;
    top: 10px;
    right: 1px;
    left: auto;
  }
  .e-m-tb.e-m-android .e-m-tbcontainer .e-m-splitview.e-m-ellipsis:before {
    left: 50%;
  }
  .e-m-tb.e-m-android .e-m-tbcontainer .e-m-splitviewcontainer {
    bottom: 0;
    position: fixed;
    width: 100%;
    height: 45px;
  }
  .e-m-tb.e-m-android .e-m-tbcontainer .e-m-splitview.e-m-ellipsis.e-m-showellipsis-true {
    bottom: 0;
    position: fixed;
    right: 0;
    z-index: 1;
  }
  .e-m-tb.e-m-android .e-m-splitview.e-m-tbulcontainer {
    bottom: 0;
    display: table;
    float: right;
    height: 45px;
    position: fixed;
    width: 100%;
  }
  .e-m-tb.e-m-android .e-m-splitview.e-m-tbulcontainer.e-m-splitellipsis {
    width: 80%;
  }
  .e-m-tb.e-m-android .e-m-splitview .e-m-tbulcontainer .e-m-toolbaritem {
    display: table-cell;
  }
  .e-m-tb.e-m-android .e-m-tbcontainer .e-m-splitview.e-m-ellipsis.e-m-showellipsis-true {
    bottom: 0;
    height: 45px;
    position: fixed;
    right: 0;
    width: 20%;
    z-index: 1;
  }
  .e-m-tb.e-m-android .e-m-splitview.e-m-ellipsiscontainer {
    bottom: 0;
    position: fixed;
  }
  .e-m-tb.e-m-android .e-m-splitview.e-m-tbulcontainer .e-m-toolbaritem {
    display: table-cell;
    text-align: center;
    vertical-align: middle;
  }
  .e-m-tb.e-m-android .e-m-tbcontainer .e-m-templatecontainer.e-m-templatewidth {
    width: 100%;
  }
  .e-m-tb.e-m-ios7.e-m-fixed {
    bottom: 0;
    height: 45px;
    position: fixed;
    left: 0px;
    width: 100%;
    top: auto;
    border-bottom: none;
    border-top: 1px solid;
  }
  .e-m-tb.e-m-ios7 .e-m-templatecontainer {
    display: none;
  }
  .e-m-tb.e-m-ios7.e-m-fixed .e-m-tbulcontainer,
  .e-m-tb.e-m-ios7.e-m-normal .e-m-tbulcontainer {
    display: table;
    padding-left: 0;
    margin: 0px;
    height: 45px;
    width: 100%;
  }
  .e-m-tb.e-m-ios7.e-m-fixed .e-m-tbulcontainer .e-m-toolbaritem,
  .e-m-tb.e-m-ios7.e-m-normal .e-m-tbulcontainer .e-m-toolbaritem {
    display: table-cell;
    text-align: center;
    vertical-align: middle;
    position: relative;
  }
  .e-m-windows .e-m-tb.e-m-tp-tb {
    -webkit-transition: bottom 225ms, height 100ms !important;
    -moz-transition: bottom 225ms, height 100ms !important;
    -ms-transition: bottom 225ms, height 100ms !important;
  }
  .e-m-windows.e-m-tp.viewport-flip {
    position: absolute;
  }
  /*splitpane start*/
  
  .e-m-splitpane.e-m-overlaypane .e-m-sp-left,
  .e-m-splitpane.e-m-tablet .e-m-sp-left {
    width: 250px;
  }
  .e-m-splitpane.e-m-overlaypane .e-m-sp-right,
  .e-m-splitpane.e-m-tablet .e-m-sp-right {
    left: 0;
  }
  .e-m-splitpane.e-m-overlaypane .e-m-sp-overlayleft {
    position: absolute;
    left: 0px;
  }
  .e-m-splitpane .e-m-sp-left.e-m-sp-overlayleft {
    -moz-transform: translate(-110%, 0px);
    -ms-transform: translate(-110%, 0px);
    -webkit-transform: translate(-110%, 0px);
    transform: translate(-110%, 0px);
  }
  .e-m-splitpane .e-m-sp-left {
    -webkit-transition-property: transform;
    -webkit-transition-duration: 350ms;
    -moz-transition: transform 350ms;
    -webkit-transition: transform 350ms;
    -ms-transition: transform 350ms;
    transition: transform 350ms;
  }
  .e-m-splitpane .e-m-sp-left.e-m-sp-overlayleft.e-m-sp-show,
  .e-m-splitpane .e-m-sp-left.e-m-sp-overlayright.e-m-sp-show {
    -moz-transform: translate(0px, 0px);
    -ms-transform: translate(0px, 0px);
    -webkit-transform: translate(0px, 0px);
    transform: translate(0px, 0px);
  }
  .e-m-splitpane .e-m-sp-left.e-m-sp-overlayright {
    -moz-transform: translate(110%, 0px);
    -ms-transform: translate(110%, 0px);
    -webkit-transform: translate(110%, 0px);
    transform: translate(110%, 0px);
    right: 0px;
    position: absolute;
  }
  .e-m-splitpane.e-m-tablet .e-m-sp-left {
    width: 250px;
  }
  .e-m-splitpane.e-m-tablet .e-m-sp-right {
    left: 251px;
  }
  /*splitpane end*/
  
  /* DatePicker*/
  
  .e-m-windows.e-m-dp.viewport-flip {
    position: absolute;
  }
  .e-m-windows.e-m-dp .e-m-dp-header.flipY {
    margin-left: 20px;
    width: 100%;
    position: absolute;
    z-index: 100004;
  }
  .e-m-windows.e-m-dp .e-m-dpinner .e-m-dpwrap {
    border-spacing: 10px;
  }
}
/*small tablets portait mode*/

@media only screen and (min-width: 480px) and (max-width: 603px) and (orientation: portrait) {
  /*splitpane start*/
  
  .e-m-splitpane.e-m-overlaypane .e-m-sp-left {
    width: 320px;
  }
}
/*For Mobile landscape mode*/

@media all and (max-width: 800px) and (orientation: landscape), only screen and (orientation: landscape), only screen and (orientation: landscape) {
  /*Dialog*/
  
  .e-m-dlg.e-m-dlg-normal {
    width: 75%;
  }
  .e-m-grid-core.e-m-pagertype-scrollable .e-m-grid-ptxt {
    width: 455px;
  }
}
/*For Mobile portrait mode*/

@media all and (max-width: 480px) and (orientation: portrait), only screen and (min-width: 480px) and (max-width: 603px) and (orientation: portrait), only screen and (orientation: portrait), only screen and (orientation: portrait) {
  /*Grid*/
  
  .e-m-grid-core.e-m-pagertype-scrollable .e-m-grid-ptxt {
    width: 258px;
  }
  /*Dialog*/
  
  .e-m-dlg.e-m-dlg-normal {
    width: 100%;
  }
  /*Tile*/
  
  .e-m-windows .group {
    padding-top: 0px;
  }
}
/*Larger Screens*/

@media only screen and (min-width: 1280px) {
  /*Splitpane*/
  
  .e-m-splitpane.e-m-windows.e-m-overlaypane .e-m-sp-left,
  .e-m-splitpane.e-m-windows.e-m-tablet .e-m-sp-left {
    width: 450px;
  }
  .e-m-splitpane.e-m-windows.e-m-overlaypane .e-m-sp-right,
  .e-m-splitpane.e-m-windows.e-m-tablet .e-m-sp-right {
    left: 451px;
  }
}
@media only screen and (min-width: 768px) {
  /*ListView*/
  
  .e-m-lv.e-m-windows .e-m-lv-checkdiv.e-m-chkbox.e-m-windows .e-m-rootel {
    background: none;
    border: 0;
  }
  .e-m-lv.e-m-windows .e-m-lv-inputdiv {
    padding: 10px 15px;
  }
  .e-m-windows.e-m-lv .e-m-grouped .e-m-list .e-m-list-img {
    left: 0px;
  }
  .e-m-windows.e-m-lv .e-m-lv-checkdiv.e-m-chkbox.e-m-windows {
    float: right;
    right: 10px;
  }
  .e-m-light.e-m-windows.e-m-lv .e-m-list-hdr .e-m-state-active.e-m-list .e-m-list-text,
  .e-m-light.e-m-windows.e-m-lv .e-m-state-active.e-m-list .e-m-chkbox.e-m-lv-checkdiv .e-m-active.e-m-chk-innerel::before {
    color: white;
  }
  .e-m-windows.e-m-lv .e-m-list.e-m-skew-center {
    transform: perspective(1000px) scale(0.98);
  }
  /*ListView*/
}

@-webkit-keyframes slideLeftOut {
  from {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  to {
    -webkit-transform: translate(-100%, 0);
    transform: translate(-100%, 0);
  }
}
@keyframes slideLeftOut {
  from {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  to {
    -webkit-transform: translate(-100%, 0);
    transform: translate(-100%, 0);
  }
}
@-webkit-keyframes slideLeftIn {
  from {
    -webkit-transform: translate(-100%, 0);
    transform: translate(-100%, 0);
  }
  to {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}
@keyframes slideLeftIn {
  from {
    -webkit-transform: translate(-100%, 0);
    transform: translate(-100%, 0);
  }
  to {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}
@-webkit-keyframes slideRightIn {
  from {
    -webkit-transform: translate(100%, 0);
    transform: translate(100%, 0);
  }
  to {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}
@keyframes slideRightIn {
  from {
    -webkit-transform: translate(100%, 0);
    transform: translate(100%, 0);
  }
  to {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}
@-webkit-keyframes slideRightOut {
  from {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  to {
    -webkit-transform: translate(100%, 0);
    transform: translate(100%, 0);
  }
}
@keyframes slideRightOut {
  from {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  to {
    -webkit-transform: translate(100%, 0);
    transform: translate(100%, 0);
  }
}
@-webkit-keyframes slideBottomIn {
  from {
    -webkit-transform: translate(0, 100%);
    transform: translate(0, 100%);
  }
  to {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}
@keyframes slideBottomIn {
  from {
    -webkit-transform: translate(0, 100%);
    transform: translate(0, 100%);
  }
  to {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}
@-webkit-keyframes slideBottomOut {
  from {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  to {
    -webkit-transform: translate(0, 100%);
    transform: translate(0, 100%);
  }
}
@keyframes slideBottomOut {
  from {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  to {
    -webkit-transform: translate(0, 100%);
    transform: translate(0, 100%);
  }
}
@-webkit-keyframes slideTopIn {
  from {
    -webkit-transform: translate(0, -100%);
    transform: translate(0, -100%);
  }
  to {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}
@keyframes slideTopIn {
  from {
    -webkit-transform: translate(0, -100%);
    transform: translate(0, -100%);
  }
  to {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}
@-webkit-keyframes slideTopOut {
  from {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  to {
    -webkit-transform: translate(0, -100%);
    transform: translate(0, -100%);
  }
}
@keyframes slideTopOut {
  from {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
  }
  to {
    -webkit-transform: translate(0, -100%);
    transform: translate(0, -100%);
  }
}
@-webkit-keyframes slideRight {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}
@keyframes slideRight {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}
@-webkit-keyframes slideLeft {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
@keyframes slideLeft {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
@-webkit-keyframes slideDown {
  from {
    height: 0%;
  }
  to {
    height: 100%;
  }
}
@keyframes slideDown {
  from {
    height: 0%;
  }
  to {
    height: 100%;
  }
}
@-webkit-keyframes slideUp {
  from {
    height: 100%;
  }
  to {
    height: 0%;
  }
}
@keyframes slideUp {
  from {
    height: 100%;
  }
  to {
    height: 0%;
  }
}
@-webkit-keyframes fadeIn {
  0% {
    opacity: 0;
    filter: alpha(opacity=0);
  }
  100% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}
@keyframes fadeIn {
  0% {
    opacity: 0;
    filter: alpha(opacity=0);
  }
  100% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}
@-webkit-keyframes fadeOut {
  from {
    opacity: 1;
    filter: alpha(opacity=100);
  }
  to {
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
    filter: alpha(opacity=100);
  }
  to {
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@-webkit-keyframes zoomIn {
  from {
    -webkit-transform: translate(0, 0) scale(0);
    transform: translate(0, 0) scale(0);
  }
  to {
    -webkit-transform: translate(0, 0) scale(1);
    transform: translate(0, 0) scale(1);
  }
}
@keyframes zoomIn {
  from {
    -webkit-transform: translate(0, 0) scale(0);
    transform: translate(0, 0) scale(0);
  }
  to {
    -webkit-transform: translate(0, 0) scale(1);
    transform: translate(0, 0) scale(1);
  }
}
@-webkit-keyframes zoomOut {
  from {
    -webkit-transform: translate(0, 0) scale(1);
    transform: translate(0, 0) scale(1);
  }
  to {
    -webkit-transform: translate(0, 0) scale(0);
    transform: translate(0, 0) scale(0);
  }
}
@keyframes zoomOut {
  from {
    -webkit-transform: translate(0, 0) scale(1);
    transform: translate(0, 0) scale(1);
  }
  to {
    -webkit-transform: translate(0, 0) scale(0);
    transform: translate(0, 0) scale(0);
  }
}
@-webkit-keyframes fadeZoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
    filter: alpha(opacity=0);
  }
  to {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
    filter: alpha(opacity=100);
  }
}
@keyframes fadeZoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
    filter: alpha(opacity=0);
  }
  to {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
    filter: alpha(opacity=100);
  }
}
@-webkit-keyframes fadeZoomOut {
  from {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  to {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: scale(0);
    transform: scale(0);
  }
}
@keyframes fadeZoomOut {
  from {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  to {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: scale(0);
    transform: scale(0);
  }
}
@-webkit-keyframes flipRightDownIn {
  from {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: perspective(400px) rotateY(-180deg);
    transform: perspective(400px) rotateY(-180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
@keyframes flipRightDownIn {
  from {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: perspective(400px) rotateY(-180deg);
    transform: perspective(400px) rotateY(-180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
@-webkit-keyframes flipRightDownOut {
  from {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: perspective(400px) rotateY(0deg);
    transform: perspective(400px) rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: rotateY(-180deg);
    transform: rotateY(-180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
@keyframes flipRightDownOut {
  from {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: perspective(400px) rotateY(0deg);
    transform: perspective(400px) rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: rotateY(-180deg);
    transform: rotateY(-180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
@-webkit-keyframes flipRightUpIn {
  from {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: perspective(400px) rotateY(135deg);
    transform: perspective(400px) rotateY(135deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
@keyframes flipRightUpIn {
  from {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: perspective(400px) rotateY(135deg);
    transform: perspective(400px) rotateY(135deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
@-webkit-keyframes flipRightUpOut {
  from {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: perspective(400px) rotateY(0deg);
    transform: perspective(400px) rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: rotateY(135deg);
    transform: rotateY(135deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
@keyframes flipRightUpOut {
  from {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: perspective(400px) rotateY(0deg);
    transform: perspective(400px) rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: right center;
    transform-origin: right center;
    -webkit-transform: rotateY(135deg);
    transform: rotateY(135deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
/*Left Flip*/

@-webkit-keyframes flipLeftDownIn {
  from {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: perspective(400px) rotateY(-180deg);
    transform: perspective(400px) rotateY(-180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
@keyframes flipLeftDownIn {
  from {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: perspective(400px) rotateY(135deg);
    transform: perspective(400px) rotateY(135deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
@-webkit-keyframes flipLeftDownOut {
  from {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: perspective(400px) rotateY(0deg);
    transform: perspective(400px) rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: rotateY(135deg);
    transform: rotateY(135deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
@keyframes flipLeftDownOut {
  from {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: perspective(400px) rotateY(0deg);
    transform: perspective(400px) rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: rotateY(135deg);
    transform: rotateY(135deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
@-webkit-keyframes flipLeftUpIn {
  from {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: perspective(400px) rotateY(-135deg);
    transform: perspective(400px) rotateY(-135deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
@keyframes flipLeftUpIn {
  from {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: perspective(400px) rotateY(-135deg);
    transform: perspective(400px) rotateY(-135deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
@-webkit-keyframes flipLeftUpOut {
  from {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: perspective(400px) rotateY(0deg);
    transform: perspective(400px) rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: rotateY(-135deg) scale;
    transform: rotateY(-135deg) scale;
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
@keyframes flipLeftUpOut {
  from {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: perspective(400px) rotateY(0deg);
    transform: perspective(400px) rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
  to {
    -webkit-transform-origin: left center;
    transform-origin: left center;
    -webkit-transform: rotateY(-135deg);
    transform: rotateY(-135deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
  }
}
/*check*/

@-webkit-keyframes flipYLeftIn {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateY(180deg);
    transform: perspective(400px) rotateY(180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
  50% {
    -webkit-transform: perspective(700px) rotateY(90deg);
    transform: perspective(700px) rotateY(90deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}
@keyframes flipYLeftIn {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateY(180deg);
    transform: perspective(400px) rotateY(180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
  50% {
    -webkit-transform: perspective(700px) rotateY(90deg);
    transform: perspective(700px) rotateY(90deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}
@-webkit-keyframes flipYLeftOut {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateY(0deg);
    transform: perspective(400px) rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  50% {
    -webkit-transform: perspective(700px) rotateY(90deg);
    transform: perspective(700px) rotateY(90deg);
  }
  75% {
    -webkit-transform: perspective(850px) rotateY(125deg);
    transform: perspective(850px) rotateY(125deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateY(180deg);
    transform: rotateY(180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@keyframes flipYLeftOut {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateY(0deg);
    transform: perspective(400px) rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  50% {
    -webkit-transform: perspective(700px) rotateY(90deg);
    transform: perspective(700px) rotateY(90deg);
  }
  75% {
    -webkit-transform: perspective(850px) rotateY(125deg);
    transform: perspective(850px) rotateY(125deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateY(180deg);
    transform: rotateY(180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@-webkit-keyframes flipYRightIn {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateY(-180deg);
    transform: perspective(400px) rotateY(-180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
  50% {
    -webkit-transform: perspective(700px) rotateY(-90deg);
    transform: perspective(700px) rotateY(-90deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}
@keyframes flipYRightIn {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateY(-180deg);
    transform: perspective(400px) rotateY(-180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
  50% {
    -webkit-transform: perspective(700px) rotateY(-90deg);
    transform: perspective(700px) rotateY(-90deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}
@-webkit-keyframes flipYRightOut {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateY(0deg);
    transform: perspective(400px) rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  50% {
    -webkit-transform: perspective(700px) rotateY(-90deg);
    transform: perspective(700px) rotateY(-90deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateY(-180deg);
    transform: rotateY(-180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@keyframes flipYRightOut {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateY(0deg);
    transform: perspective(400px) rotateY(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  50% {
    -webkit-transform: perspective(700px) rotateY(-90deg);
    transform: perspective(700px) rotateY(-90deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateY(-180deg);
    transform: rotateY(-180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@-webkit-keyframes flipXDownIn {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateX(180deg);
    transform: perspective(400px) rotateX(180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
  50% {
    -webkit-transform: perspective(700px) rotateX(90deg);
    transform: perspective(700px) rotateX(90deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateX(0deg);
    transform: rotateX(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}
@keyframes flipXDownIn {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateX(180deg);
    transform: perspective(400px) rotateX(180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
  50% {
    -webkit-transform: perspective(700px) rotateX(90deg);
    transform: perspective(700px) rotateX(90deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateX(0deg);
    transform: rotateX(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}
@-webkit-keyframes flipXDownOut {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateX(0deg);
    transform: perspective(400px) rotateX(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  50% {
    -webkit-transform: perspective(700px) rotateX(90deg);
    transform: perspective(700px) rotateX(90deg);
  }
  75% {
    -webkit-transform: perspective(850px) rotateX(125deg);
    transform: perspective(850px) rotateX(125deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateX(180deg);
    transform: rotateX(180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@keyframes flipXDownOut {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateX(0deg);
    transform: perspective(400px) rotateX(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  50% {
    -webkit-transform: perspective(700px) rotateX(90deg);
    transform: perspective(700px) rotateX(90deg);
  }
  75% {
    -webkit-transform: perspective(850px) rotateX(125deg);
    transform: perspective(850px) rotateX(125deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateX(180deg);
    transform: rotateX(180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@-webkit-keyframes flipXUpIn {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateX(-180deg);
    transform: perspective(400px) rotateX(-180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
  50% {
    -webkit-transform: perspective(700px) rotateX(-90deg);
    transform: perspective(700px) rotateX(-90deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateX(0deg);
    transform: rotateX(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}
@keyframes flipXUpIn {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateX(-180deg);
    transform: perspective(400px) rotateX(-180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
  50% {
    -webkit-transform: perspective(700px) rotateX(-90deg);
    transform: perspective(700px) rotateX(-90deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateX(0deg);
    transform: rotateX(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}
@-webkit-keyframes flipXUpOut {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateX(0deg);
    transform: perspective(400px) rotateX(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  50% {
    -webkit-transform: perspective(700px) rotateX(-90deg);
    transform: perspective(700px) rotateX(-90deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateX(-180deg);
    transform: rotateX(-180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@keyframes flipXUpOut {
  from {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: perspective(400px) rotateX(0deg);
    transform: perspective(400px) rotateX(0deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 1;
    filter: alpha(opacity=100);
  }
  50% {
    -webkit-transform: perspective(700px) rotateX(-90deg);
    transform: perspective(700px) rotateX(-90deg);
  }
  to {
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transform: rotateX(-180deg);
    transform: rotateX(-180deg);
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
/*Ripple Effect*/

.ripple {
  position: absolute;
  background: rgba(0, 0, 0, 0.25);
  border-radius: 100%;
  -webkit-transform: scale(0) translateZ(0px) translateY(0px) translateX(0px);
  -ms-transform: scale(0) translateZ(0px) translateY(0px) translateX(0px);
  transform: scale(0) translateZ(0px) translateY(0px) translateX(0px);
  pointer-events: none;
  -moz-backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform-origin: center center 0;
  -ms-transform-origin: center center 0;
  transform-origin: center center 0;
}
@-webkit-keyframes ripple {
  to {
    -webkit-transform: scale(1.5) translateZ(0px) translateY(0px) translateX(0px);
    -ms-transform: scale(1.5) translateZ(0px) translateY(0px) translateX(0px);
    transform: scale(1.5) translateZ(0px) translateY(0px) translateX(0px);
    -webkit-transform-origin: center center 0;
    -ms-transform-origin: center center 0;
    transform-origin: center center 0;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@keyframes ripple {
  to {
    -webkit-transform: scale(1.5) translateZ(0px) translateY(0px) translateX(0px);
    -ms-transform: scale(1.5) translateZ(0px) translateY(0px) translateX(0px);
    transform: scale(1.5) translateZ(0px) translateY(0px) translateX(0px);
    -webkit-transform-origin: center center 0;
    -ms-transform-origin: center center 0;
    transform-origin: center center 0;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

/*Radial Slider start*/

.e-m-radialslider.e-m-ios7 .e-m-rs-marker {
  border-radius: 50%;
  border-style: solid;
  border-width: 1px;
  cursor: move;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  height: 30px;
  width: 30px;
  position: absolute;
}
.e-m-radialslider.e-m-ios7 .e-m-ticks-text,
.e-m-radialslider.e-m-ios7 .e-m-dynamic-text {
  font-size: 14px;
}
/*Radial Slider end*/

/* Radial Menu */

.e-m-ios7 .e-m-radialimage {
  background-image: url(images/radialmenu/ios7radial.png);
}
.e-m-ios7 .e-m-backimage {
  background-image: url(images/radialmenu/ios7back.png);
}
.e-m-radialmenu.e-m-ios7 .e-m-rm-slider {
  top: 35px;
  left: 35px;
}
.e-m-radialmenu.e-m-ios7 .e-m-rm-slider text {
  font-size: 12px;
}
/* Tile Start*/

.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-state-active .e-m-child-tile .e-m-layer,
.e-m-ios7.e-m-state-active.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-medium.e-m-text-outer .e-m-child-tile .e-m-fontimage .e-m-layer,
.e-m-ios7.e-m-state-active.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-wide .e-m-child-tile .e-m-fontimage .e-m-layer {
  background-color: rgba(0, 0, 0, 0.25);
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-state-active .e-m-child-tile .e-m-tile-badge:after {
  background-color: rgba(0, 0, 0, 0.25);
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-wide.e-m-enabled-text.e-m-ios7 .e-m-child-tile .e-m-tile-image.e-m-fontimage,
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-large.e-m-enabled-text.e-m-ios7 .e-m-child-tile .e-m-tile-image.e-m-fontimage {
  margin-left: 0px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-wide.e-m-enabled-text.e-m-ios7.e-m-text-outer .e-m-child-tile .e-m-tile-image.e-m-fontimage,
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-large.e-m-enabled-text.e-m-ios7.e-m-text-outer .e-m-child-tile .e-m-tile-image.e-m-fontimage {
  margin: 0 12px;
}
.e-m-ios7.e-m-tile .e-m-tile-template {
  border-radius: 12px;
}
.e-m-tile-wrapper.e-m-tile-wrapper-medium.e-m-text-outer.e-m-ios7 .e-m-child-tile .e-m-tile-image.e-m-fontimage {
  margin-left: 12px;
}
.e-m-tile-wrapper.e-m-tile-wrapper-small.e-m-ios7 {
  height: 70px;
  position: relative;
  width: 74px;
  margin-bottom: 8px;
  margin-left: 0px;
  margin-top: 8px;
}
.e-m-tile-wrapper.e-m-tile-wrapper-medium.e-m-ios7 {
  width: 120px;
  height: 120px;
  position: relative;
}
.e-m-tile-wrapper.e-m-tile-wrapper-wide.e-m-ios7 {
  width: 250px;
  height: 120px;
  position: relative;
}
.e-m-tile-wrapper.e-m-tile-wrapper-large.e-m-ios7 {
  width: 250px;
  height: 250px;
  position: relative;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7 .e-m-tile-badge {
  border: medium none;
  border-radius: 15px;
  box-shadow: none;
  color: white;
  font-size: inherit;
  font-weight: inherit;
  line-height: 15px;
  margin: auto;
  min-height: 15px;
  min-width: 15px;
  padding: 3px;
  position: absolute;
  right: -8px;
  text-align: center;
  top: -8px;
  background-color: red;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-tile-wrapper-wide .e-m-tile-badge {
  right: -9px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-tile-wrapper-large .e-m-tile-badge {
  right: 10px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-tile-wrapper-wide.e-m-text-outer .e-m-tile-badge {
  right: 1px;
  top: -8px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-tile-wrapper-large.e-m-text-outer .e-m-tile-badge {
  right: 18px;
  top: -8px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7 .e-m-tile-badge:after {
  content: ' ';
  position: absolute;
  top: 0;
  border-radius: 50%;
  left: 0;
  bottom: 0;
  right: 0;
}
.e-m-tile.e-m-tile-wrapper-small.e-m-ios7.e-m-disabled-text .e-m-tile-badge {
  right: -3px;
  top: -6px;
}
.e-m-tile.e-m-tile-wrapper-small.e-m-ios7.e-m-tile-wrapper .e-m-tile-badge {
  right: -3px;
  top: -8px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7 .e-m-child-tile .e-m-fontimage.e-m-tile-imagefill {
  border-radius: 20px;
}
.e-m-tile.e-m-ios7.e-m-tile-wrapper.e-m-tile-wrapper-medium.e-m-text-outer .e-m-child-tile .e-m-fontimage,
.e-m-tile.e-m-ios7.e-m-tile-wrapper.e-m-tile-wrapper-wide.e-m-text-outer .e-m-child-tile .e-m-fontimage {
  border-radius: 20px;
}
.e-m-tile.e-m-ios7.e-m-tile-wrapper.e-m-text-outer .e-m-child-tile .e-m-fontimage {
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}
.e-m-tile.e-m-ios7.e-m-tile-wrapper.e-m-text-outer.e-m-tile-wrapper-large .e-m-child-tile .e-m-fontimage {
  border-radius: 50px;
}
.e-m-tile.e-m-ios7.e-m-tile-wrapper .e-m-child-tile .e-m-fontimage.e-m-tile-imagefill {
  border-radius: 12px;
}
.e-m-tile.e-m-ios7.e-m-tile-wrapper.e-m-tile-wrapper-large .e-m-child-tile .e-m-fontimage.e-m-tile-imagefill {
  border-radius: 50px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-small.e-m-ios7 .e-m-child-tile .e-m-tile-image.e-m-tile-imagefill {
  background-size: 100% 100%;
  position: relative;
  width: 60px;
  height: 60px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-small.e-m-ios7 .e-m-child-tile .e-m-tile-image {
  border-radius: 12px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-medium.e-m-ios7.e-js .e-m-child-tile.e-m-tile-imagefill .e-m-fontimage.e-m-tile-image.e-m-tile-imagefill {
  background-size: 100% 100%;
  height: 120px;
  position: absolute;
  width: 120px;
  margin: 0px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-medium.e-m-ios7.e-m-text-outer .e-m-child-tile.e-m-tile-imagefill .e-m-fontimage.e-m-tile-image.e-m-tile-imagefill {
  background-size: 100% 100%;
  height: 96px;
  position: absolute;
  width: 96px;
  margin-left: 12px;
}
.e-js.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-wide.e-m-ios7.e-m-text-inner .e-m-child-tile.e-m-tile-imagefill .e-m-tile-image.e-m-tile-imagefill {
  background-size: 100% 100%;
  width: 250px;
  height: 120px;
  position: absolute;
  border-radius: 20px;
  margin: 0px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-large.e-m-ios7.e-m-text-inner.e-js .e-m-child-tile.e-m-tile-imagefill .e-m-tile-image.e-m-tile-imagefill {
  background-size: 100% 100%;
  width: 250px;
  height: 250px;
  position: absolute;
  margin: 0px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7 {
  border-radius: 20px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-tile-wrapper-large {
  border-radius: 50px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-text-outer.e-m-tile-wrapper-medium .e-m-child-tile .e-m-tile-image,
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-text-outer.e-m-tile-wrapper-wide .e-m-child-tile .e-m-tile-image {
  border-radius: 20px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-medium.e-m-ios7.e-m-enabled-text.e-m-text-outer .e-m-child-tile .e-m-tile-image.e-m-fontimage {
  height: 96px;
  position: absolute;
  width: 96px;
  background-size: 40px 40px;
  margin-top: 0px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-medium.e-m-ios7.e-m-enabled-text.e-m-text-inner .e-m-child-tile .e-m-tile-image.e-m-fontimage {
  height: 80px;
  width: 105px;
  position: absolute;
  margin: 7px;
  background-size: 40px 40px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-wide.e-m-ios7.e-m-enabled-text.e-m-text-inner .e-m-child-tile .e-m-tile-image.e-m-fontimage {
  width: 236px;
  height: 80px;
  margin: 7px;
  position: absolute;
  background-size: 40px 40px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-large.e-m-ios7.e-m-enabled-text.e-m-text-inner .e-m-child-tile .e-m-tile-image.e-m-fontimage {
  width: 236px;
  height: 212px;
  position: absolute;
  margin: 7px;
  background-size: 40px 40px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-medium.e-m-ios7 .e-m-tile-text {
  bottom: 5px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-medium.e-m-ios7.e-m-text-outer .e-m-tile-text {
  bottom: -5px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-medium.e-m-text-outer.e-m-ios7 .e-m-tile-text {
  width: 96px;
  margin-left: 12px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-small.e-m-ios7 .e-m-tile-text {
  bottom: 0px;
  position: relative;
  white-space: pre-line;
  width: 100%;
  word-wrap: break-word;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-wide.e-m-ios7 .e-m-tile-text,
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-large.e-m-ios7 .e-m-tile-text {
  bottom: 5px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-wide.e-m-text-outer.e-m-ios7 .e-m-tile-text,
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-large.e-m-text-outer.e-m-ios7 .e-m-tile-text {
  bottom: -5px;
}
.e-m-tile-wrapper.e-m-tile-wrapper-small.e-m-ios7 .e-m-child-tile .e-m-fontimage.e-m-tile-image {
  width: 60px;
  height: 60px;
  position: relative;
  background-size: 60px 60px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7 .e-m-tile-text.e-m-textalign-normal {
  text-align: center;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-tile-wrapper-small .e-m-child-tile .e-m-tile-text {
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-text-outer .e-m-child-tile .e-m-tile-text {
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-state-active.e-m-tile-wrapper-small .e-m-child-tile .e-m-layer {
  height: 100%;
  width: 100%;
  position: absolute;
  border-radius: 12px;
  z-index: 1;
  top: 0px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-state-active .e-m-child-tile .e-m-layer {
  height: 100%;
  width: 100%;
  position: absolute;
  border-radius: 20px;
  z-index: 1;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-state-active.e-m-tile-wrapper-large .e-m-child-tile .e-m-layer {
  border-radius: 50px;
}
.e-m-ios7.e-m-state-active.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-medium.e-m-text-outer .e-m-child-tile .e-m-fontimage .e-m-layer {
  height: 100%;
  width: 100%;
  position: absolute;
  border-radius: 20px;
  z-index: 1;
}
.e-m-ios7.e-m-state-active.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-wide.e-m-text-outer .e-m-child-tile .e-m-fontimage .e-m-layer {
  height: 100%;
  width: 100%;
  position: absolute;
  border-radius: 20px;
}
.e-m-ios7.e-m-state-active.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-large.e-m-text-outer .e-m-child-tile .e-m-fontimage .e-m-layer {
  height: 100%;
  width: 100%;
  position: absolute;
  border-radius: 50px;
}
.e-m-ios7.e-m-state-active.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-wide.e-m-text-outer .e-m-child-tile .e-m-fontimage.e-m-tile-imagefill .e-m-layer {
  height: 100%;
  width: 100%;
  position: absolute;
  border-radius: 20px;
}
.e-m-ios7.e-m-state-active.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-large.e-m-text-outer .e-m-child-tile .e-m-fontimage.e-m-tile-imagefill .e-m-layer {
  height: 100%;
  width: 100%;
  position: absolute;
  border-radius: 50px;
}
.e-m-ios7.e-m-state-active.e-m-tile.e-m-tile-wrapper .e-m-child-tile.e-m-template-caption .e-m-layer {
  z-index: 1;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-wide.e-m-ios7 .e-m-child-tile.e-m-disabled-text .e-m-live-tile.e-m-tile-image {
  height: 250px;
  width: 120px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-wide.e-m-text-outer.e-m-ios7 .e-m-child-tile .e-m-tile-image.e-m-fontimage {
  height: 96px;
  position: absolute;
  width: 226px;
  background-size: 40px 40px;
  margin-top: 0px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-tile-wrapper-wide.e-m-text-outer .e-m-child-tile .e-m-tile-text,
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-tile-wrapper-large.e-m-text-outer .e-m-child-tile .e-m-tile-text {
  margin-left: 12px;
  padding-left: 0px;
  width: 226px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-large.e-m-text-outer.e-m-ios7.e-m-enabled-text .e-m-child-tile .e-m-tile-image.e-m-fontimage {
  height: 226px;
  position: absolute;
  width: 226px;
  margin-top: 0px;
  background-size: 40px 40px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-text-outer {
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-wide.e-m-text-outer.e-m-ios7 .e-m-child-tile.e-m-tile-imagefill .e-m-tile-image.e-m-fontimage,
.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-large.e-m-text-outer.e-m-ios7 .e-m-child-tile.e-m-tile-imagefill .e-m-tile-image.e-m-fontimage {
  background-size: 100% 100%;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-tile-wrapper-medium.e-m-text-outer .e-m-tile-badge {
  right: 5px;
  top: -6px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-text-outer.e-m-tile-wrapper-medium .e-m-tile-badge {
  right: 4px;
  top: -7px;
}
.e-m-tile.e-m-ios7.e-m-tile-wrapper.e-m-tile-wrapper-medium .e-m-child-tile.e-m-disabled-text .e-m-fontimage {
  height: 120px;
  position: absolute;
  border-radius: 12px;
  width: 120px;
}
.e-m-tile.e-m-ios7.e-m-tile-wrapper.e-m-tile-wrapper-wide .e-m-child-tile.e-m-disabled-text .e-m-fontimage {
  height: 120px;
  position: absolute;
  width: 250px;
}
.e-m-tile-wrapper.e-m-tile-wrapper-wide.e-m-ios7 .e-m-child-tile .e-m-tile-image.e-m-fontimage {
  background-size: 40px 40px;
  height: 106px;
  margin: 7px;
  position: absolute;
  width: 236px;
}
.e-m-tile-wrapper.e-m-tile-wrapper-large.e-m-ios7 .e-m-child-tile .e-m-tile-image.e-m-fontimage {
  height: 236px;
  margin: 7px;
  position: absolute;
  width: 236px;
  background-size: 40px 40px;
}
.e-m-tile-wrapper.e-m-tile-wrapper-wide.e-m-ios7 .e-m-child-tile.e-m-disabled-text .e-m-tile-image.e-m-fontimage,
.e-m-tile-wrapper.e-m-tile-wrapper-large.e-m-ios7 .e-m-child-tile.e-m-disabled-text .e-m-tile-image.e-m-fontimage {
  border-radius: 12px;
}
.e-m-tile-wrapper.e-m-tile-wrapper-medium.e-m-ios7 .e-m-child-tile .e-m-tile-image.e-m-fontimage {
  background-size: 40px 40px;
  height: 106px;
  margin: 7px;
  position: absolute;
  width: 106px;
}
.e-m-tile-wrapper.e-m-tile-wrapper-small.e-m-ios7 .e-m-child-tile .e-m-tile-image.e-m-fontimage {
  margin: 0 auto;
}
.e-m-tile-wrapper.e-m-tile-wrapper-small.e-m-ios7 .e-m-child-tile.e-m-disabled-text .e-m-tile-image.e-m-fontimage {
  margin-left: 7px;
  margin-top: 3px;
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-state-active .e-m-child-tile .e-m-layer,
.e-m-ios7.e-m-state-active.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-medium.e-m-text-outer .e-m-child-tile .e-m-fontimage .e-m-layer,
.e-m-ios7.e-m-state-active.e-m-tile.e-m-tile-wrapper.e-m-tile-wrapper-wide .e-m-child-tile .e-m-fontimage .e-m-layer {
  background-color: rgba(0, 0, 0, 0.25);
}
.e-m-tile.e-m-tile-wrapper.e-m-ios7.e-m-state-active .e-m-child-tile .e-m-tile-badge:after {
  background-color: rgba(0, 0, 0, 0.25);
}
/* Tile End*/

/* Revamp */

/*Accordion IOS style*/

.e-m-ios7.e-m-acc .e-m-acc-item {
  height: 56px;
  padding: 20px 16px;
  font-size: 16px;
  line-height: 16px;
}
.e-m-ios7.e-m-acc .e-m-acc-item:before {
  line-height: 16px;
  right: 16px;
}
/*Accordion IOS style End*/

/*Button IOS Style*/

.e-m-ios7.e-m-btn {
  height: 36px;
  font-size: 14px;
  padding: 0px 16px;
  border: 1px solid;
  border-radius: 4px;
  display: inline-block;
  line-height: 0px;
}
.e-m-actionlink.e-m-btn {
  outline: 0 none;
  display: inline-block;
  line-height: 34px;
  height: 34px;
}
.e-m-ios7.e-m-btn.e-m-noborder {
  border: none;
  box-shadow: none;
}
.e-m-ios7.e-m-actionlink.e-m-btn-imageonly {
  height: 34px;
}
.e-m-ios7.e-m-btn-image.e-m-btn-imageonly {
  background-position: center center;
  padding: 0px;
}
.e-m-actionlink.e-m-btn.e-m-btn-large {
  padding: 0px;
}
.e-m-ios7.e-m-state-disabled {
  background-color: white;
  color: #d4e8fe;
  border-color: #e5f1fe;
}
.e-m-ios7.e-m-btn-header.e-m-state-disabled {
  background-color: transparent;
  color: #d4e8fe;
  border-color: transparent;
}
/*Button IOS Style Ends*/

/*NavBar IOS style*/

.e-m-ios7.e-m-navbar {
  height: 45px;
  padding: 4px 10px;
  border-style: solid;
}
.e-m-ios7.e-m-navbar.e-m-navbar-top {
  border-width: 0 0 1px 0;
}
.e-m-ios7.e-m-navbar.e-m-navbar-bottom {
  border-width: 1px 0 0 0;
}
.e-m-ios7.e-m-navbar .e-m-navbar-text {
  line-height: 45px;
  font-size: 20px;
}
/*Toolbar*/

.e-m-ios7.e-m-navbar .e-m-navbar-icon {
  font-size: 23px;
}
.e-m-ios7.e-m-navbar .e-m-navbar-icon:before {
  line-height: 37px;
}
.e-m-ios7.e-m-navbar.e-m-navbar-top .e-m-navbar-icon,
.e-m-ios7.e-m-navbar.e-m-navbar-bottom .e-m-group-icons .e-m-navbar-icon {
  padding: 4px 10px;
  width: 57px;
}
.e-m-ios7.e-m-navbar.e-m-navbar-bottom .e-m-navbar-icon {
  padding: 4px 0;
}
.e-m-ios7.e-m-navbar.e-m-navbar .e-m-navbar-ellipsis {
  line-height: 45px;
  font-size: 23px;
}
@media (min-width: 640px) {
  .e-m-ios7.e-m-navbar.e-m-navbar-bottom .e-m-split-icons .e-m-navbar-icon.e-m-nav-badge::after {
    top: 13px;
  }
}
/*NavBar IOS style Ends*/

/*Dialog IOS Style Starts*/

.e-m-ios7.e-m-dialog .e-m-dlgbtnwrapper {
  border-top: 1px solid;
}
.e-m-ios7.e-m-dialog .e-m-dlg-container {
  border-radius: 12px;
  min-width: 300px;
  width: 20%;
}
.e-m-ios7.e-m-dialog .e-m-dlg-content {
  text-align: center;
  margin: 7px 14px 21px;
}
.e-m-ios7.e-m-dialog .e-m-dlg-btn {
  margin-bottom: 0;
  padding: 19px 0;
  width: 50%;
  font-size: 17px;
}
.e-m-ios7.e-m-dialog .e-m-alertbtn {
  width: 100%;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}
.e-m-ios7.e-m-dialog .e-m-dlg-btn.e-m-dlg-leftbtn {
  border-bottom-left-radius: 12px;
}
.e-m-ios7.e-m-dialog .e-m-dlg-btn.e-m-dlg-rightbtn {
  border-bottom-right-radius: 12px;
  border-left: 1px solid;
}
.e-m-ios7.e-m-dialog .e-m-dlg-hdr {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  font-weight: 600;
  padding: 15px 0 10px;
  text-align: center;
}
.e-m-ios7.e-m-dialog.e-m-show .e-m-overlay {
  opacity: 0.2;
}
.e-m-ios7.e-m-dialog .e-m-dlg-showanimate {
  -moz-animation: ios_dlg_show 300ms;
  -webkit-animation: ios_dlg_show 300ms;
  -o-animation: ios_dlg_show 300ms;
  -ms-animation: ios_dlg_show 300ms;
  animation: ios_dlg_show 300ms;
}
@-webkit-keyframes ios_dlg_show {
  from {
    -webkit-transform: translateX(-50%) translateY(-50%) scale(1.5);
  }
  to {
    -webkit-transform: translateX(-50%) translateY(-50%) scale(1);
  }
}
@-moz-keyframes ios_dlg_show {
  from {
    -moz-transform: translateX(-50%) translateY(-50%) scale(1.5);
  }
  to {
    -moz-transform: translateX(-50%) translateY(-50%) scale(1);
  }
}
@-o-keyframes ios_dlg_show {
  from {
    -o-transform: translateX(-50%) translateY(-50%) scale(1.5);
  }
  to {
    -o-transform: translateX(-50%) translateY(-50%) scale(1);
  }
}
@-ms-keyframes ios_dlg_show {
  from {
    -ms-transform: translateX(-50%) translateY(-50%) scale(1.5);
  }
  to {
    -ms-transform: translateX(-50%) translateY(-50%) scale(1);
  }
}
@keyframes ios_dlg_show {
  from {
    transform: translateX(-50%) translateY(-50%) scale(1.5);
  }
  to {
    transform: translateX(-50%) translateY(-50%) scale(1);
  }
}
.e-m-ios7.e-m-dialog .e-m-dlg-hideanimate {
  -moz-animation: ios_dlg_hide 30ms;
  -webkit-animation: ios_dlg_hide 30ms;
  -o-animation: ios_dlg_hide 30ms;
  -ms-animation: ios_dlg_hide 30ms;
  animation: ios_dlg_hide 30ms;
}
@-webkit-keyframes ios_dlg_hide {
  from {
    -webkit-transform: translateX(-50%) translateY(-50%) scale(1);
  }
  to {
    -webkit-transform: translateX(-50%) translateY(-50%) scale(0.8);
  }
}
@-moz-keyframes ios_dlg_hide {
  from {
    -moz-transform: translateX(-50%) translateY(-50%) scale(1);
  }
  to {
    -moz-transform: translateX(-50%) translateY(-50%) scale(0.8);
  }
}
@-o-keyframes ios_dlg_hide {
  from {
    -o-transform: translateX(-50%) translateY(-50%) scale(1);
  }
  to {
    -o-transform: translateX(-50%) translateY(-50%) scale(0.8);
  }
}
@-ms-keyframes ios_dlg_hide {
  from {
    -ms-transform: translateX(-50%) translateY(-50%) scale(1);
  }
  to {
    -ms-transform: translateX(-50%) translateY(-50%) scale(0.8);
  }
}
@keyframes ios_dlg_hide {
  from {
    transform: translateX(-50%) translateY(-50%) scale(1);
  }
  to {
    transform: translateX(-50%) translateY(-50%) scale(0.8);
  }
}
/*Dialog IOS Style Ends*/

/*Tab IOS style starts*/

.e-m-tab.e-m-ios7.e-m-tab-scroll {
  display: table;
}
.e-m-tab.e-m-ios7.e-m-tab-scroll .e-m-tab-item {
  display: table-cell;
}
.e-m-tab-content-wrapper.e-m-tab-bottom.e-m-ios7.e-m-tab-content-bothblock {
  bottom: 72px;
}
.e-m-tab-content-wrapper.e-m-tab-bottom.e-m-ios7 {
  bottom: 58px;
}
.e-m-tab.e-m-ios7.e-m-tab-content-bothblock {
  height: 72px;
}
.e-m-tab.e-m-ios7:not(.e-m-tab-content-bothblock) {
  height: 58px;
}
.e-m-tab-content-wrapper.e-m-tab-top.e-m-ios7.e-m-tab-content-bothblock {
  top: 72px;
}
.e-m-tab-content-wrapper.e-m-tab-top.e-m-ios7 {
  top: 58px;
}
.e-m-tab.e-m-ios7.e-m-tab-content-bothblock .e-m-tab-item:before {
  padding-bottom: 5px;
}
.e-m-tab.e-m-ios7 {
  display: inline-block;
}
.e-m-tab.e-m-ios7 [class*=" e-m-icon-"]::before {
  font-size: 24px;
}
.e-m-tab.e-m-ios7 {
  overflow: visible;
}
.e-m-tab.e-m-ios7 .e-m-tab-item {
  display: inline-block;
  width: 100px;
}
.e-m-tab.e-m-ios7.e-m-tab-content-bothinline .e-m-tab-badge::after,
.e-m-tab.e-m-ios7.e-m-tab-content-text .e-m-tab-item.e-m-tab-badge::after {
  top: 20px;
  right: 5px;
}
.e-m-tab.e-m-tab-top .e-m-tab-item {
  border-bottom: 2px solid transparent;
}
.e-m-tab.e-m-tab-bottom .e-m-tab-item {
  border-top: 2px solid transparent;
}
.e-m-tab.e-m-ios7.e-m-tab-content-bothblock .e-m-tab-item {
  padding: 15px;
}
.e-m-tab.e-m-ios7.e-m-tab-content-bothblock.e-m-tab-top .e-m-tab-item {
  padding: 13px;
}
.e-m-tab.e-m-ios7.e-m-tab-content-image {
  font-size: 0px;
}
.e-m-tab.e-m-ios7.e-m-tab-content-text .e-m-tab-item::before {
  content: none;
}
.e-m-tab.e-m-ios7.e-m-tab-bottom.e-m-tab-content-bothinline .e-m-tab-item {
  padding: 0px 8px;
  line-height: 54px;
}
.e-m-tab.e-m-ios7.e-m-tab-content-image .e-m-tab-item {
  padding: 15px 8px;
}
.e-m-tab.e-m-ios7.e-m-tab-top.e-m-tab-content-bothinline .e-m-tab-item {
  padding: 18px 8px;
}
.e-m-tab.e-m-ios7.e-m-tab-content-text .e-m-tab-item {
  padding: 0px 8px;
  line-height: 54px;
}
.e-m-tab.e-m-ios7.e-m-tab-content-bothinline .e-m-tab-item::before {
  display: inline;
  white-space: nowrap;
  vertical-align: middle;
  padding-right: 5px;
}
.e-m-tab.e-m-ios7.e-m-tab-content-bothblock .e-m-tab-badge::after {
  left: 50%;
  top: 8px;
}
.e-m-tab.e-m-ios7.e-m-tab-content-image .e-m-tab-badge::after {
  top: 10px;
}
/*Tab IOS style ends*/

/*Menu IOS style Starts*/

.e-m-menu.e-m-ios7 .e-m-state-disabled .e-m-menu-btn {
  color: #c7c7c7;
}
.e-m-ios7.e-m-menu.e-m-menu-actionsheet {
  padding-bottom: 10px;
  left: 10px;
  right: 10px;
}
.e-m-ios7.e-m-menu.e-m-menu-popover .e-m-menu-title {
  border-radius: 5px;
}
.e-m-ios7.e-m-menu .e-m-menu-cancelbtn {
  border-radius: 5px;
}
.e-m-ios7.e-m-menu.e-m-menu-actionsheet .e-m-scrollpanel,
.e-m-ios7.e-m-menu.e-m-menu-popover .e-m-scrollpanel {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
.e-m-menu.e-m-ios7 .e-m-state-disabled .e-m-menu-btn {
  color: #c7c7c7;
}
/*Menu IOS style Ends*/

/*Datepicker IOS7 styles start*/

.e-m-ios7.e-m-datepickerwrapper .e-m-datepicker {
  border: 1px solid;
  border-radius: 5px;
  height: 46px;
  font-size: 18px;
  text-indent: 8px;
}
.e-m-ios7.e-m-dp-menu.e-m-menu.e-m-menu-actionsheet {
  width: 100%;
  padding-bottom: 0;
  left: 0;
  right: 0;
}
.e-m-ios7.e-m-dp-menu.e-m-menu.e-m-menu-actionsheet .e-m-dp {
  border-radius: 0;
}
.e-m-ios7.e-m-dp {
  overflow: hidden;
  border-radius: 5px;
}
.e-m-ios7.e-m-dp .e-m-dpinner {
  text-align: center;
  position: relative;
  z-index: 10000;
}
.e-m-ios7.e-m-dp .e-m-dpinner .e-m-ios7-overlay {
  border-bottom: 1px solid;
  border-top: 1px solid;
  height: 40px;
  margin-top: -20px;
  pointer-events: none;
  position: absolute;
  top: 50%;
  width: 100%;
  cursor: pointer;
  opacity: 0.5;
}
.e-m-ios7.e-m-dp .e-m-dpwrap {
  display: table;
  margin: 0 auto;
  padding: 0;
}
.e-m-ios7.e-m-dp .e-m-dpwrap .e-m-dp {
  margin: 4px 0;
  position: relative;
}
.e-m-ios7.e-m-dp .e-m-dpwrap .e-m-dp .col-fill {
  height: 80px;
}
.e-m-ios7.e-m-dp .e-m-dpwrap .e-m-text {
  font-size: 25px;
  height: 40px;
  line-height: 40px;
  padding: 0 7px;
  text-align: right;
  text-shadow: none;
}
/*Datepicker IOS7 styles end*/

/*Timepicker IOS7 styles start*/

.e-m-ios7.e-m-timepicker {
  border: 1px solid;
  border-radius: 5px;
  height: 46px;
  font-size: 18px;
  text-indent: 8px;
}
.e-m-ios7.e-m-tp-dialog.e-m-dialog .e-m-dlg-container {
  border-radius: 6px;
}
.e-m-ios7.e-m-tp-dialog.e-m-dialog .e-m-tp-header {
  height: 45px;
  padding: 0 16px;
  border-bottom: 1px solid;
  border-top-right-radius: 6px;
  border-top-left-radius: 6px;
}
.e-m-ios7.e-m-tp-dialog.e-m-dialog .e-m-dlg-btn.e-m-dlg-leftbtn {
  border-bottom-left-radius: 6px;
}
.e-m-ios7.e-m-tp-dialog.e-m-dialog .e-m-dlg-btn.e-m-dlg-rightbtn {
  border-bottom-right-radius: 6px;
}
.e-m-ios7.e-m-tp-dialog.e-m-dialog .e-m-tp-header .e-m-tp-time,
.e-m-ios7.e-m-tp-dialog.e-m-dialog .e-m-tp-header .e-m-tp-mer {
  font-size: 25px;
  line-height: 45px;
}
.e-m-ios7.e-m-tp-dialog.e-m-dialog .e-m-tp-header .e-m-tp-time {
  width: 65px;
  text-align: left;
}
.e-m-ios7.e-m-tp-dialog.e-m-dialog .e-m-tp-header .e-m-tp-mer {
  float: right;
  width: 90px;
}
.e-m-ios7.e-m-tp-dialog.e-m-dialog .e-m-tp-header .e-m-tp-mer .pm {
  margin-left: 16px;
}
.e-m-ios7.e-m-tp-dialog.e-m-dialog .e-m-dlg-btn {
  padding: 13px 0;
}
.e-m-ios7.e-m-tp-dialog.e-m-dialog .e-m-tpwrap .e-m-text.e-m-text-active {
  border-radius: 50%;
}
/*Timepicker IOS7 styles end*/

/*Scrollpanel IOS7 styles start*/

.e-m-ios7.e-m-adjheader-1 {
  top: 45px;
}
.e-m-ios7.e-m-adjfooter-1 {
  bottom: 45px;
}
.e-m-ios7.e-m-adjheader-2 {
  top: 90px;
}
.e-m-ios7.e-m-adjfooter-2 {
  bottom: 90px;
}
.e-m-ios7.e-m-sbw.e-m-hr {
  height: 2px;
}
.e-m-ios7.e-m-sbw.e-m-ver {
  width: 3px;
}
/*Scrollpanel IOS7 styles end*/

/*GroupButton IOS7 style starts*/

.e-m-ios7.e-m-grpbtn .e-m-btn {
  text-shadow: 0 0 0;
  padding: 5px 10px;
  border: 1px solid;
  border-left: none;
}
.e-m-ios7.e-m-grpbtn .e-m-btn:first-child {
  border-radius: 5px 0 0 5px;
  border-left: 1px solid;
}
.e-m-ios7.e-m-grpbtn .e-m-btn:last-child {
  border-radius: 0 5px 5px 0;
}
.e-m-grpbtn.e-m-ios7 .e-m-btn.e-m-imageonly {
  background-position: 10px 50%;
  padding: 15px 20px;
  line-height: 0px;
}
/*Groupbutton IOS7 style ends*/

/*Dropdown starts*/

.e-m-ios7.e-m-dropdownlist {
  border-width: 1px;
  border-radius: 8px;
}
/*Dropdown ends*/

/*Tile Starts*/

.e-m-tile-caption-text.e-m-ios7.e-m-caption-align-normal::before {
  text-align: center;
}
.e-m-ios7.e-m-tile-round-corner .e-m-tile-overlay {
  border-radius: 14px;
}
.e-m-ios7.e-m-caption-outer.e-m-tile-caption-text::before {
  bottom: 2px;
}
.e-m-tile.e-m-ios7.e-m-caption-outer .e-m-tile-image:not(.e-m-tile-imagefill) {
  margin: 0px;
}
.e-m-tile.e-m-caption-outer.e-m-ios7.e-m-badge-position-bottomright::after {
  bottom: 30px;
}
/*Selection style*/

.e-m-tile.e-m-ios7 .e-m-tile-selected::before {
  bottom: 14px;
  right: 10px;
}
.e-m-tile.e-m-ios7 .e-m-tile-selected::after {
  border-radius: 50%;
  border-style: solid;
  border-width: 1.5px;
  bottom: 5px;
  content: "";
  display: block;
  height: 20px;
  position: absolute;
  right: 5px;
  width: 20px;
  z-index: 101;
}
/*small tile badge*/

.e-m-ios7.e-m-tile.e-m-tile-badge.e-m-tile-small.e-m-caption-outer.e-m-badge-position-bottomright::after,
.e-m-ios7.e-m-tile.e-m-tile-badge.e-m-tile-small.e-m-caption-outer.e-m-badge-position-topright::after {
  border-radius: 50%;
  font-weight: inherit;
  padding: 3px;
  line-height: 14px;
  right: 0px;
  top: 0px;
  bottom: initial;
}
/*Rounded corner*/

.e-m-ios7.e-m-tile-round-corner:not(.e-m-caption-outer),
.e-m-ios7.e-m-tile-round-corner .e-m-tile-image {
  border-radius: 14px;
}
.e-m-tile.e-m-ios7 .e-m-tile-image .e-m-tile-image:not(.e-m-tile-imagefill) {
  background-size: 32px 32px;
  font-size: 32px;
}
.e-m-tile-caption.e-m-ios7.e-m-tile-caption-icon::before {
  width: 20px;
  height: 20px;
  font-size: 20px;
}
.e-m-tile.e-m-ios7 .e-m-tile-imagefill {
  backface-visibility: visible;
}
.e-m-tile.e-m-ios7 {
  overflow: hidden;
  position: relative;
  min-width: 74px;
  min-height: 74px;
  margin: 2px;
}
.e-m-tile-wide.e-m-ios7 {
  width: 200px;
  height: 100px;
  float: left;
}
.e-m-tile-small.e-m-ios7.e-m-caption-outer .e-m-tile-image,
.e-m-tile-small.e-m-ios7.e-m-caption-outer .e-m-tile-overlay {
  height: 60px;
  width: 60px;
  margin: 7px;
}
.e-m-tile-small.e-m-ios7 {
  float: left;
  height: 74px;
  width: 74px;
}
.e-m-tile-medium.e-m-ios7 {
  float: left;
  width: 100px;
  height: 100px;
}
.e-m-tile-large.e-m-ios7 {
  float: left;
  width: 200px;
  height: 200px;
}
.e-m-tile-wide.e-m-ios7.e-m-caption-outer {
  width: 200px;
  height: 126px;
}
.e-m-tile-medium.e-m-ios7.e-m-caption-outer {
  width: 100px;
  height: 126px;
}
.e-m-tile-large.e-m-ios7.e-m-caption-outer {
  width: 200px;
  height: 226px;
}
.e-m-tile-wide.e-m-ios7.e-m-caption-outer .e-m-tile-image,
.e-m-tile-wide.e-m-ios7.e-m-caption-outer .e-m-tile-overlay {
  width: 200px;
  height: 100px;
}
.e-m-tile-medium.e-m-ios7.e-m-caption-outer .e-m-tile-image,
.e-m-tile-medium.e-m-ios7.e-m-caption-outer .e-m-tile-overlay {
  height: 100px;
  width: 100px;
}
.e-m-tile-large.e-m-ios7.e-m-caption-outer .e-m-tile-image,
.e-m-tile-large.e-m-ios7.e-m-caption-outer .e-m-tile-overlay {
  height: 200px;
  width: 200px;
}
/* Small tile caption position*/

.e-m-ios7.e-m-tile-small.e-m-tile-caption-text.e-m-caption-outer::before {
  bottom: 0px;
}
.e-m-ios7.e-m-tile-caption-text.e-m-caption-outer::before {
  height: 20px;
}
.e-m-tile.e-m-tile-small.e-m-ios7.e-m-caption-outer .e-m-tile-image:not(.e-m-tile-imagefill) {
  background-size: auto;
}
.e-m-tile.e-m-tile-small.e-m-ios7.e-m-caption-outer .e-m-tile-image:not(.e-m-tile-imagefill) {
  width: 60px;
  height: 60px;
  position: absolute;
  margin: 7px;
}
.e-m-ios7.e-m-caption-outer.e-m-tile-caption-text::before {
  padding-left: 0px;
  padding-right: 0px;
}
.e-m-tile.e-m-ios7.e-m-tile-small.e-m-caption-outer {
  overflow: hidden;
  position: relative;
  min-width: 74px;
  min-height: 90px;
  margin: 2px;
}
.e-m-tile-small.e-m-ios7 {
  float: left;
  height: 74px;
  width: 74px;
}
.e-m-ios7.e-m-tile-caption::before {
  text-overflow: ellipsis;
  white-space: nowrap;
}
/*Tile Ends*/

/*Togglebutton Starts*/

.e-m-tbutton.e-m-ios7 {
  border-radius: 15px;
  display: inline-block;
  border: 1px solid;
  min-width: 50px;
  height: 29px;
  position: relative;
  box-sizing: border-box;
}
.e-m-tbutton.e-m-ios7 .e-m-tslider {
  border-radius: 15px;
  width: 26px;
  height: 26px;
  position: relative;
  margin-right: 0.5px;
  top: 0.5px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}
.e-m-tbutton.e-m-ios7.e-m-state-active {
  border: none;
}
.e-m-tbutton.e-m-ios7 .e-m-tslider.e-m-state-active {
  left: -22px;
  float: right;
  top: 1px;
  box-shadow: none;
}
.e-m-tbutton.e-m-ios7 .e-m-tslider.e-m-touchactive {
  width: 33px;
}
.e-m-tbutton .e-m-ios7 .e-m-state-disabled {
  background-color: transparent;
}
/*Togglebutton Starts*/

/* AutoComplete ios core starts*/

.e-m-ios7.e-m-ac-wrapper .e-m-ac {
  border-width: 1px;
  border-radius: 5px;
  font-size: 17px;
}
/* AutoComplete ios core ends*/

/*Slider Control*/

.e-m-slider.e-m-ios7.e-m-vertical .e-m-slider-outer {
  width: 3px;
  height: 100%;
  min-height: 200px;
  left: 12px;
}
.e-m-slider.e-m-ios7.e-m-vertical .e-m-slider-inner {
  width: 3px;
  bottom: 0px;
}
.e-m-slider.e-m-ios7.e-m-vertical .e-m-slider-handlein {
  height: 5px;
  width: 5px;
  display: none;
}
.e-m-slider.e-m-ios7.e-m-vertical .e-m-slider-handlein {
  margin: 0px 0px 0px 0px;
}
.e-m-slider.e-m-ios7.e-m-vertical .e-m-slider-handleout {
  height: 30px;
  width: 30px;
}
.e-m-slider.e-m-ios7.e-m-vertical .e-m-slider-handleout.e-m-small-handle {
  height: 16px;
  width: 16px;
}
.e-m-slider.e-m-ios7.e-m-vertical .e-m-slider-handleout.e-m-slider-left {
  margin: 0px 0 -12px -14px;
}
.e-m-slider.e-m-ios7.e-m-vertical .e-m-slider-handleout.e-m-slider-right {
  margin: 0px 0 -12px -14px;
}
.e-m-slider.e-m-ios7.e-m-vertical .e-m-slider-handleout.e-m-small-handle.e-m-slider-left {
  margin: 0px 0 -12px -7px;
}
.e-m-slider.e-m-ios7.e-m-vertical .e-m-slider-handleout.e-m-small-handle.e-m-slider-right {
  margin: 0px 0 -12px -7px;
}
.e-m-slider.e-m-ios7.e-m-vertical .e-m-state-active .e-m-slider-handlein {
  margin: 0px;
}
.e-m-slider.e-m-ios7.e-m-horizontal .e-m-slider-outer {
  width: 100%;
  height: 3px;
  top: 12px;
}
.e-m-slider.e-m-ios7.e-m-horizontal .e-m-slider-inner {
  width: 0px;
  height: 3px;
}
.e-m-slider.e-m-ios7.e-m-horizontal .e-m-slider-handlein {
  height: 5px;
  width: 5px;
  display: none;
}
.e-m-slider.e-m-ios7.e-m-horizontal .e-m-slider-handlein {
  margin: 0px 0px 0px 0px;
}
.e-m-slider.e-m-ios7.e-m-horizontal .e-m-slider-handleout {
  height: 30px;
  width: 30px;
}
.e-m-slider.e-m-ios7.e-m-horizontal .e-m-slider-handleout.e-m-small-handle {
  height: 16px;
  width: 16px;
}
.e-m-slider.e-m-ios7.e-m-horizontal .e-m-slider-handleout.e-m-slider-left {
  margin: -14px 0 0 -12px;
}
.e-m-slider.e-m-ios7.e-m-horizontal .e-m-slider-handleout.e-m-slider-right {
  margin: -14px 0 0 -12px;
}
.e-m-slider.e-m-ios7.e-m-horizontal .e-m-slider-handleout.e-m-small-handle.e-m-slider-left {
  margin: -7px 0 0 -9px;
}
.e-m-slider.e-m-ios7.e-m-horizontal .e-m-slider-handleout.e-m-small-handle.e-m-slider-right {
  margin: -7px 0 0 -9px;
}
.e-m-slider.e-m-ios7.e-m-horizontal .e-m-state-active .e-m-slider-handlein {
  margin: 0px;
}
/*Slider Control End*/

