﻿<%@ Page Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" EnableEventValidation="false" ValidateRequest="false" CodeBehind="EmailTemplate.aspx.cs" Inherits="WebUI.EmailTemplate" Culture="auto" meta:resourcekey="Page" UICulture="auto" %>


<asp:Content ID="mainHeadContent" ContentPlaceHolderID="mainHead" runat="server">

    <script type="text/javascript">
        function Initialization() {
            tinymce.init({
                selector: '#bodyTinyMce',
                language: 'el',
                init_instance_callback: (editor) => {
                    //body = tinymce.activeEditor.getContent({ format: 'html' });
                    //alert(body);
                    editor.setContent($("#<%= bodyHiddenFld.ClientID %>").val(), { format: 'html' });
                    //alert('init_instance_callback ended');
                },
               <%-- onchange_callback: (editor) => {
                    alert("Some one modified something");
                    alert("The HTML is now:" + inst.getBody().innerHTML);
                    var x = document.getElementById('<%= bodyHiddenFld.ClientID %>');
                    x.val(inst.getBody().innerHTML);
                },--%>
                min_height: 600,
                plugins: [
                    'a11ychecker', 'advlist', 'table', 'advcode', 'autolink', 'checklist', 'export',
                    'lists', 'link', 'charmap', 'preview',
                    'powerpaste', 'formatpainter', 'wordcount'
                ],
                toolbar: 'undo redo | formatpainter casechange blocks | bold italic backcolor | ' +
                    'alignleft aligncenter alignright alignjustify | ' +
                    'bullist numlist checklist outdent indent | removeformat',
                setup: function (ed) {
                    ed.on("change", function (e) {
                        //alert("OnChange:" + tinymce.activeEditor.getContent());
                        var x = document.getElementById('<%= bodyHiddenFld.ClientID %>');
                        ReplaceCharactersInBody();
                    });
                    ed.on("keyup", function () {
                        //alert("OnKeyUp:" + tinymce.activeEditor.getContent());
                        var x = document.getElementById('<%= bodyHiddenFld.ClientID %>');
                        ReplaceCharactersInBody();
                    });
                }
            });

        }

        function onContentChange(inst) {

        }

        function Save() {
            //ReplaceCharactersInBody();
        }

        function Cancel() {
            //ReplaceCharactersInBody();
        }

        function ReplaceCharactersInBody() {
            body = tinymce.activeEditor.getContent({ format: 'html' });
            var x = document.getElementById('<%= bodyHiddenFld.ClientID %>');

            x.value = body.replace(/</g, '&lt;');
            //alert(body.replace(/</g, '&lt;'));
        }

        function OpenSelectFile() {
            document.getElementById('<%= fileUpload.ClientID %>').click();
            return true;
        }

    </script>
    <%--<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>--%>
    <script src="Content/tinymce/tinymce.min.js"></script>
</asp:Content>
<asp:Content ID="mainBodyContent" ContentPlaceHolderID="mainBody" runat="server">
    <%--<asp:UpdatePanel ID="UpdatePanel1" runat="server" ChildrenAsTriggers="true" >
        <ContentTemplate>--%>

    <div class="row margin-bottom">
        <div class="col-xs-12">
            <div class="btn-group">
                <asp:LinkButton ID="saveBtn" runat="server" CssClass="btn btn-primary btn-flat margin-bottom margin-r-5" OnClientClick="Save();" OnClick="saveBtn_Click"><span class="hidden-md hidden-lg"><i class="fa fa-save"></i></span><asp:Label Text="<%$ Resources:GlobalResources, Save %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
                <%-- <button type="button" class="btn btn-primary btn-flat dropdown-toggle" data-toggle="dropdown">
                    <span class="caret"></span>
                    <span class="sr-only">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu" role="menu">
                    <li>
                        <asp:Button ID="saveCloseBtn" runat="server" Text="<%$ Resources:GlobalResources, SaveClose %>" OnClick="saveCloseBtn_Click"></asp:Button>
                    </li>
                </ul>--%>
            </div>
            <asp:LinkButton ID="deleteBtn" runat="server" DisableValidation="True" CssClass="btn btn-danger btn-flat margin-bottom margin-r-5" OnClientClick="Cancel();" OnClick="deleteBtn_Click" CausesValidation="False"><span class="hidden-md hidden-lg"><i class="fa fa-trash"></i></span><asp:Label Text="<%$ Resources:GlobalResources, Delete %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
            <asp:LinkButton ID="cancelBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat margin-bottom margin-r-5" OnClick="cancelBtn_Click" CausesValidation="False" PostBackUrl="~/EmailTemplates.aspx"><span class="hidden-md hidden-lg"><i class="fa fa-remove"></i></span><asp:Label Text="<%$ Resources:GlobalResources, Cancel %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
        </div>
    </div>

    <%-- </ContentTemplate>
    </asp:UpdatePanel>--%>
    <div class="row">
        <div class="col-sm-12">
            <div class="form-horizontal">
                <%--<div class="form-group">
                    <asp:Label for="emailTemplateIdTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="emailTemplateIdLbl"></asp:Label>
                    <div class="col-sm-10">
                        <input type="text" runat="server" class="form-control" id="emailTemplateIdTxtBox" readonly="readonly" placeholder="">
                    </div>
                </div>--%>
                <div class="form-group">
                    <asp:Label for="titleTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="titleLbl"></asp:Label>
                    <div class="col-sm-10">
                        <div class="input-group">
                            <input type="text" runat="server" class="form-control " id="titleTxtBox" maxlength="200" placeholder="" required="required">
                            <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12">
            <div class="form-horizontal">
                <div class="form-group">
                    <asp:Label for="subjectTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="subjectLbl"></asp:Label>
                    <div class="col-sm-10">
                        <div class="input-group">
                            <input type="text" runat="server" class="form-control " id="subjectTxtBox" maxlength="300" placeholder="" required="required">
                            <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12">
            <div class="form-horizontal">
                <div class="form-group">
                    <asp:Label for="emailTemplateCategoryIdDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="emailTemplateCategoryIdLbl"></asp:Label>
                    <div class="col-sm-10">
                        <select runat="server" id="emailTemplateCategoryIdDDL" datavaluefield="EmailTemplateCategoryId" datatextfield="CategoryName" class="form-control no-search-select"></select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12">
            <div class="form-horizontal">
                <%--<ej:UploadBox ID="importExcelUploadBox" runat="server" SaveUrl="saveFiles.ashx" RemoveUrl="removeFiles.ashx" OnComplete="importExcelUploadBox_Complete" AutoUpload="false"></ej:UploadBox>--%>
                <%--<asp:Button ID="selectFileBtn" runat="server" OnClientClick="OpenSelectFile();" DisableValidation="True" CssClass="btn btn-default btn-flat margin-bottom margin-r-5" meta:resourceKey="selectFileBtn" />--%>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12">
            <div class="form-horizontal">
                <div class="form-group">
                    <asp:Label for="bodyRichTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="bodyLbl"></asp:Label>
                    <div class="col-sm-10">
                        <textarea id="bodyTinyMce"></textarea>

                        <br />
                        <div class="box">
                            <div class="box-header with-border">
                                <h3 class="box-title">
                                    <asp:Label runat="server" meta:resourcekey="importExcelTitleLbl"></asp:Label>
                                </h3>
                            </div>
                            <!-- /.box-header -->
                            <div class="box-body">
                                <asp:Label runat="server" meta:resourcekey="importExcelInoLbl" Style="display: block;" CssClass="margin-bottom"></asp:Label>
                                <div style="display: inline-block;">
                                    <asp:FileUpload ID="fileUpload" runat="server" AllowMultiple="false" accept=".xls,.xlsx" CssClass="" Style="float: left;" />
                                    <asp:Button ID="importExcelFileBtn" runat="server" OnClientClick="Save();" OnClick="importExcelFileBtn_Click" DisableValidation="True" CssClass="btn btn-default btn-flat margin-bottom margin-r-5" Style="float: left;" meta:resourceKey="importExcelFileBtn" />
                                </div>
                            </div>
                            <!-- /.box-body -->

                        </div>
                        <!-- /.box -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <asp:HiddenField ID="bodyHiddenFld" runat="server" ClientIDMode="Static" />

</asp:Content>
