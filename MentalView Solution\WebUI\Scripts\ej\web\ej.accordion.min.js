/*!
*  filename: ej.accordion.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.scroller.min"],n):n()})(function(){(function(n,t){t.widget("ejAccordion","ej.Accordion",{_rootCSS:"e-acrdn",element:null,model:null,validTags:["div","span"],_addToPersist:["selectedItemIndex","selectedItems"],_setFirst:!1,angular:{terminal:!1},defaults:{collapsible:!1,ajaxSettings:{type:"GET",cache:!1,data:{},dataType:"html",contentType:"html",async:!0},events:"click",customIcon:{header:"e-plus",selectedHeader:"e-minus"},heightAdjustMode:"content",height:null,width:null,headerSize:null,enableAnimation:!0,selectedItemIndex:0,cssClass:"",enableRTL:!1,showCloseButton:!1,showRoundedCorner:!1,allowKeyboardNavigation:!0,enableMultipleOpen:!1,expandSpeed:"300",collapseSpeed:"300",selectedItems:[],disabledItems:[],enabledItems:[],enablePersistence:!1,enabled:!0,htmlAttributes:{},ajaxLoad:null,ajaxBeforeLoad:null,activate:null,beforeActivate:null,inActivate:null,beforeInactivate:null,ajaxSuccess:null,ajaxError:null,create:null,destroy:null},dataTypes:{cssClass:"string",collapsible:"boolean",enabled:"boolean",events:"string",heightAdjustMode:"enum",ajaxSettings:"data",customIcon:"data",selectedItems:"array",disabledItems:"array",enabledItems:"array",enableAnimation:"boolean",htmlAttributes:"data"},_setModel:function(t){var i,r,e,s,u,o,f;for(i in t)switch(i){case"events":this._off(this._headers,this.model.events);this._on(this._headers,t[i],this._headerEventHandler);break;case"disabledItems":this.disableItems(t[i]);break;case"enabledItems":this.enableItems(t[i]);break;case"enabled":this._enabledAction(t[i]);break;case"selectedItemIndex":n(this._contentPanels[t[i]]).hasClass("e-disable")||t[i]>this._headers.length||this.model.enableMultipleOpen?t[i]=this.model.selectedItemIndex:(this._isInteraction=!0,this.model.enableMultipleOpen||this._activeTab(t[i],!1,!0),this._isInteraction=!1);break;case"heightAdjustMode":this._setHeightStyle(t[i]);break;case"cssClass":this._changeSkin(t[i]);break;case"showRoundedCorner":this._roundedCorner(t[i]);break;case"customIcon":r=this.model.customIcon;e=t[i];this._headers.find("span."+r.header).removeClass(r.header).addClass(e.header);this._headers.find("span."+r.selectedHeader).removeClass(r.selectedHeader).addClass(e.selectedHeader);break;case"height":this.element.height(t[i]);this.model.height=t[i];this.model.heightAdjustMode!="fill"?this._getHeight()>this.element.height()&&(this._scrollerObj?this._scrollerObj.option({height:this.element.height()}):this._setScoller()):this._setHeight();break;case"width":if(this.element.width(t[i]),this._scrollerObj&&this._scrollerObj.option("width",t[i]),t[i].toString().indexOf("%"))n(window).on("resize",n.proxy(this._scrollerRefresh,this));else n(window).off("resize",n.proxy(this._scrollerRefresh,this));this._scrollerObj&&this._scrollerObj.refresh();break;case"headerSize":this.model.headerSize=t[i];this._setHeaderSize();break;case"showCloseButton":this.model.showCloseButton=t[i];this._addDeleteIcon();break;case"allowKeyboardNavigation":t[i]?(this._off(this.element,"keydown"),this._on(this.element,"keydown",this._keyPress)):this._off(this.element,"keydown");break;case"enableRTL":t[i]?this.element.addClass("e-rtl"):this.element.removeClass("e-rtl");this._scrollerObj&&this._scrollerObj.option("enableRTL",t[i]);break;case"selectedItems":this._showHideSelectedItems(t[i]);t[i]=this.model.selectedItems;break;case"expandSpeed":this.model.expandSpeed=t[i];break;case"collapseSpeed":this.model.collapseSpeed=t[i];break;case"htmlAttributes":this._addAttr(t[i]);break;case"enableMultipleOpen":if(this.model.enableMultipleOpen=t[i],this.model.selectedItems.length<=0)s=n.extend(!0,[],this.model.enabledItems),this._activeTab(this.model.enabledItems[0]);else if(this.model.selectedItemIndex>0&&this.model.selectedItemIndex<=this._headers.length)for(u=n.extend(!0,[],this.model.selectedItems),u.sort(function(n,t){return n-t}),this.model.selectedItemIndex=u[0],o=u,f=1;f<o.length;f++)this._hideTab(o[f])}},_destroy:function(){this._removeBaseClass();this.element.children(".e-disable").removeClass("e-disable");this.element.removeClass("e-acrdn e-corner e-widget");this._headers.removeClass("e-active  e-state-disabled");this._contentPanels.removeClass("e-content-active e-disable e-acrdn-content-active");n(this._headers.find(".e-icon.e-acrdn-icon")).remove();this.model.height&&this._contentPanels.height("auto");this.model.width&&this._contentPanels.width("auto");this._scrollerObj&&this._scrollerObj.destroy();this._unWireEvents()},_init:function(){this.beforeRender=!1;this._renderControl();this._prevSize=this._getDimension(n(this.element).parent(),"height")},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.element.addClass(n):t=="disabled"&&(n=="disabled"||n=="true")?i.disable():i.element.attr(t,n)})},_renderControl:function(){this._headers=this.element.find("> :even");this.element.attr("tabindex",0).attr("role","tablist");this._contentPanels=this._headers.next();this._nagativeIndexCheck(this.model.selectedItemIndex);this._addBaseClass();this._addAttr(this.model.htmlAttributes);t.buildTag("span.e-icon e-acrdn-icon "+this.model.customIcon.header).prependTo(this._headers);this.model.showCloseButton&&this._addDeleteIcon();this._setHeightStyle(this.model.heightAdjustMode);this.model.height&&this.element.css("height",this.model.height);this.model.width&&this.element.css("width",this.model.width);this.model.enableMultipleOpen||this._renderHeaderIcon();t.isNullOrUndefined(this.model.headerSize)||this._setHeaderSize();this._wireEvents();this._roundedCorner(this.model.showRoundedCorner);this.model.enabled||this._enabledAction(this.model.enabled);this._selectedItemsAction(this.model.enableMultipleOpen?this.model.selectedItems:this.model.selectedItemIndex);this._disableItems(this.model.disabledItems);this._setEnabledItems()},_setHeight:function(){var t=this,r=0,u=0,f,e,i;this.element.css("height",this.model.height);f=this.element.height();this._contentPanels.css("height","auto");this._scrollerObj&&this._scrollerObj.option("height",this.element.height());this._headers.each(function(i){f-=n(this).outerHeight();r+=n(this).outerHeight();n(t._contentPanels[i]).hasClass("e-acrdn-content-active")&&(u+=n(t._contentPanels[i]).outerHeight())});e=0;i=this.element.height()-(u+r);i>0?this._contentPanels.each(function(){n(this).height(n(this).height()+i/t.element.find(".e-select.e-active").length)}):this._setScoller();this._scrollerObj&&this._scrollerObj.refresh()},_setScoller:function(){if(this.element.parent(".e-acrdn-scroller.e-scroller").length<1){this.scroller=t.buildTag("div.e-acrdn-scroller");this.element.wrap(this.scroller);var n=this.element.css("width");this.element.parent(".e-acrdn-scroller").ejScroller({height:this.element.height(),width:this.model.width});this.element.css("width",n);this._scrollerObj=this.element.parent(".e-js").data("ejScroller");this.model.showRoundedCorner&&this.element.parent(".e-acrdn-scroller").addClass("e-corner");this.model.enableRTL&&this._scrollerObj.option("enableRTL",!0)}},_setHeaderSize:function(){var t=this;this._headers.each(function(){n(this).css({"padding-top":0,"padding-bottom":0});n(this).css("height",t.model.headerSize);var i=typeof t.model.headerSize=="string"&&t.model.headerSize.indexOf("px")!=-1?t.model.headerSize:t.model.headerSize+"px";n(n(this).find("a[href]")).css("line-height",i);n(this).find(".e-icon.e-acrdn-icon").length>0&&n(this).find(".e-icon.e-acrdn-icon").css("margin-top",n(this).height()/2-5)});this.model.height&&(this.model.heightAdjustMode!="fill"?this._getHeight()>this.element.height()&&(this._scrollerObj?this._scrollerObj.refresh():this._setScoller()):this._setHeight(),this._scrollerObj&&this._scrollerObj.refresh())},_addDeleteIcon:function(){this.model.showCloseButton?(this.getItemsCount()>0&&this._headers.append(this._createDeleteIcon()),this._on(this._headers.children("div.e-close.e-acrdn-icon"),"click",this._panelDeleteClick)):(this._headers.children("div.e-close.e-acrdn-icon").remove(),this._off(this._headers.children("div.e-close.e-acrdn-icon"),"click",this._panelDeleteClick))},_createDeleteIcon:function(){return t.buildTag("div.e-icon e-close e-acrdn-icon")},_getHeight:function(){var t=0,i=0,r=this;return this._headers.each(function(u){t+=n(this).outerHeight();n(r._contentPanels[u]).hasClass("e-acrdn-content-active")&&(i+=n(r._contentPanels[u]).outerHeight())}),t+i},_setEnabledItems:function(){for(var t=0;t<this.getItemsCount();t++)n.inArray(t,this.model.disabledItems)<0&&n.inArray(t,this.model.enabledItems)<0&&this.model.enabledItems.push(t)},_changeSkin:function(n){this.model.cssClass!=n&&this.element.removeClass(this.model.cssClass).addClass(n)},_panelDeleteClick:function(t){var i,r;this.model.enabled&&(i=n(t.target),i.hasClass("e-close")&&(r=n(this._headers).index(n(t.target).parent())),this.removeItem(r))},_showHideSelectedItems:function(n){var r,i,t;if(this.model.enableMultipleOpen&&n instanceof Array){for(t=0;t<n.length;t++)n[t]>=0&&n[t]<this._headers.length&&this.model.selectedItems.indexOf(n[t])==-1&&this._activeTab(n[t],!0);for(r=this.model.selectedItems.length,i=JSON.parse(JSON.stringify(this.model.selectedItems)),t=0;t<r;t++)n.indexOf(i[t])==-1&&this._hideTab(i[t])}},_selectedItemsAction:function(n){if(this.model.enableMultipleOpen&&n instanceof Array&&n.length>0)for(var t=0;t<n.length;t++)n[t]>=0&&this._activeTab(n[t],!0);else!(this.collapseALL&&this.model.collapsible)&&this.model.selectedItemIndex>=0&&this._activeTab(this.model.selectedItemIndex,!0)},_enabledAction:function(n){n?this.enable():this.disable()},_addBaseClass:function(){this.element.addClass(this.model.cssClass+" e-widget");this.model.enableRTL&&this.element.addClass("e-rtl");this._headers.addClass(" e-select").attr("role","tab").attr("tabindex",0);this._contentPanels.addClass("e-content").attr("role","tabpanel").attr("aria-hidden",!0).hide();this.model.enableMultipleOpen||n(this._contentPanels[this.model.selectedItemIndex]).show()},_removeBaseClass:function(){this._headers.removeClass(" e-select");this._contentPanels.removeClass("e-content").show()},_roundedCorner:function(n){n?this.element.addClass("e-corner"):this.element.hasClass("e-corner")&&this.element.removeClass("e-corner")},_nagativeIndexCheck:function(n){var i,t;if(n<0&&this.model.collapsible)for(this.collapseALL=!0,i=this.getItemsCount(),t=0;t<i;t++)this._hideTab(t);else(n<0||n>=this._headers.length)&&(this.model.selectedItemIndex=0)},collapsePanel:function(n){this._expandcollapsepanel(!1,n)},expandPanel:function(n){this._expandcollapsepanel(!0,n)},_expandcollapsepanel:function(t,i){var r=this;i instanceof Array?n.each(i,function(n,i){r._activehideIndex(t,i)}):typeof i=="number"&&r._activehideIndex(t,i)},_activehideIndex:function(n,t){var i=this;t>=0&&t<=i._headers.length&&(n?this._activeTab(t,!1,!0):(this._hideTab(t,!1,!0),this._deleteSelectedItems(t),this.model.selectedItemIndex==t&&(this.model.selectedItemIndex=null)))},_activeTab:function(i,r,u){var e,o,f;if(n.inArray(i,this.model.disabledItems)<0){if(e={activeIndex:i,isInteraction:!u},!0===(!r&&this._trigger("beforeActivate",e)))return!1;!this.model.enableMultipleOpen&&this.beforeRender&&this._hideTab(this.model.selectedItemIndex,r,u);this.model.selectedItemIndex=i;t.isNullOrUndefined(this.model.selectedItemIndex)||(o=this.model.customIcon,this._headers.attr("tabindex",0).removeAttr("aria-expanded"),this._activeHeader=this._headers.eq(i).addClass("e-active ").attr("aria-selected",!0).attr("tabindex",0).attr("aria-expanded",!0),this._activeHeader.find("span.e-icon.e-acrdn-icon").removeClass(o.header).addClass(o.selectedHeader),this._addSelectedItems(i),this._ajaxLoad(),this._roundedCorner(this.model.showRoundedCorner),e={activeHeader:this._activeHeader,activeIndex:this.model.selectedItemIndex,isInteraction:!u},f=this,this._activeHeader.next().addClass("e-acrdn-content-active").removeAttr("aria-hidden",!1),this._paneAdjustSize(),this._activeContent=this._activeHeader.next().slideDown(this.model.enableAnimation?this._validateSpeed(this.model.expandSpeed):0,function(){r||f._trigger("activate",e);f.model.height&&(f.model.heightAdjustMode!="fill"?f._getHeight()>f.element.height()&&(f._scrollerObj?f._scrollerObj.refresh():f._setScoller()):f._setHeight(),f._scrollerObj&&f._scrollerObj.refresh())}));this.beforeRender=!0}},_paneAdjustSize:function(){var r=this,t,i;this.model.heightAdjustMode==="fill"&&this.model.enableMultipleOpen&&(t=this._getDimension(n(this.element).parent(),"height"),n(this.element).parent().css({overflow:"auto"}),this._headers.each(function(){t-=r._getDimension(n(this),"outerHeight")}),i=0,i=Math.max(i,r._getDimension(n(this._activeHeader.next()),"outerHeight")-r._getDimension(n(this._activeHeader.next()),"height")),t=t/this.model.selectedItems.length-i,this._headers.next().animate({height:t},300))},_validateSpeed:function(t){return t&&(t=n.trim(t.toString().toLowerCase()))&&(t=="fast"||t=="slow")?t:Number(t)},_hideTab:function(n,t,i){var f=this.model.customIcon,u,r;if(n>=0){if(u={inActiveIndex:n,isInteraction:!i},!0===(!t&&this._trigger("beforeInactivate",u)))return!1;this._activeHeader=this._headers.eq(n).removeClass("e-active ").removeAttr("aria-selected").attr("tabindex",0).attr("aria-expanded",!1);this._activeHeader.find("span.e-icon.e-acrdn-icon").removeClass(f.selectedHeader).addClass(f.header);u={inActiveHeader:this._activeHeader,inActiveIndex:n,isInteraction:!i};r=this;r.raiseEvent=t;this._activeHeader.next().removeClass("e-acrdn-content-active").attr("aria-hidden",!0).slideUp(this.model.enableAnimation?this._validateSpeed(this.model.collapseSpeed):0,function(){r.raiseEvent||r._trigger("inActivate",u);r.model.height&&(r.model.heightAdjustMode!="fill"?r._getHeight()>r.element.height()&&(r._scrollerObj?r._scrollerObj.refresh():r._setScoller()):r._setHeight());r._scrollerObj&&r._scrollerObj.refresh();r._paneAdjustSize()});this._deleteSelectedItems(n)}},_renderHeaderIcon:function(){if(this._headers.find(".e-icon.e-acrdn-icon").length<=0){var n=this.model.customIcon;n&&(this.collapseALL&&this.model.collapsible||this._headers.eq(this.model.selectedItemIndex).find("span.e-icon.e-acrdn-icon").removeClass(n.header).addClass(n.selectedHeader))}},_ajaxLoad:function(){var t=this._activeHeader.find("a[href]"),r,i;t.length>0&&(r=n(this._contentPanels[this.model.selectedItemIndex]),i=n(t).attr("href"),i&&i!=="#"&&this._sendAjaxOptions(r,t[0]))},_sendAjaxOptions:function(t,i){if(!0===this._trigger("ajaxBeforeLoad",{url:i}))return!1;t.addClass("e-load");var r=this,u=n(i).html(),f=i.href.replace("#",""),e={type:this.model.ajaxSettings.type,cache:this.model.ajaxSettings.cache,url:f,data:this.model.ajaxSettings.data,dataType:this.model.ajaxSettings.dataType,contentType:this.model.ajaxSettings.contentType,async:this.model.ajaxSettings.async,success:function(n){try{r._ajaxSuccessHandler(n,t,i,u)}catch(f){}},error:function(){try{r._ajaxErrorHandler(data,i,r.model.selectedItemIndex,u)}catch(n){}}};this._sendAjaxRequest(e)},_sendAjaxRequest:function(t){n.ajax({type:t.type,cache:t.cache,url:t.url,dataType:t.dataType,data:t.data,contentType:t.contentType,async:t.async,success:t.success,error:t.error,beforeSend:t.beforeSend,complete:t.complete})},_ajaxSuccessHandler:function(t,i,r,u){u!=null&&n(r).html(u);i.removeClass("e-load");i.html(t).addClass("e-acrdn-loaded");var f={data:t,url:r,content:i};this._trigger("ajaxSuccess",f);this._trigger("ajaxLoad",{url:r})},_ajaxErrorHandler:function(n,t){this._trigger("ajaxError",{data:n,url:t});this._trigger("ajaxLoad",{url:t})},_setHeightStyle:function(i){var u=this,r,f;t.Accordion.HeightAdjustMode.Fill==i?this.model.height!=null?this._setHeight():(r=this._getDimension(n(this.element).parent(),"height"),n(this.element).parent().css({overflow:"auto"}),u._headers.each(function(){r-=u._getDimension(n(this),"outerHeight")}),f=0,u._headers.next().each(function(){f=Math.max(f,u._getDimension(n(this),"outerHeight")-u._getDimension(n(this),"height"))}).height(r-f).css({overflow:"auto"})):t.Accordion.HeightAdjustMode.Auto==i&&(r=0,u._headers.next().each(function(){r=Math.max(r,u._getDimension(n(this),"outerHeight"))}).height(r),this.maxAutoHeight=r);t.Accordion.HeightAdjustMode.Fill!=i&&this.model.height&&this._getHeight()>this.element.height()&&this._setScoller()},_getDimension:function(t,i){var u,f=n(t).parents().addBack().filter(":hidden"),r={visibility:"hidden",display:"block"},e=[];return f.each(function(){var t={};for(var n in r)t[n]=this.style[n],this.style[n]=r[n];e.push(t)}),u=/(outer)/g.test(i)?n(t)[i](!0):n(t)[i](),f.each(function(n){var i=e[n];for(var t in r)this.style[t]=i[t]}),u},_headerEventHandler:function(t){if(this.model.enabled){t.preventDefault();var r=n(t.currentTarget),i=this._headers.index(r);this.model.enableMultipleOpen&&this._headers.eq(i).hasClass("e-active")?this._hideTab(i):this.model.enableMultipleOpen&&this.model.collapsible?this._activeTab(i):this.model.selectedItemIndex==i&&this.model.collapsible?(this._hideTab(this.model.selectedItemIndex),this.model.selectedItemIndex=-1):this._headers.eq(i).hasClass("e-active")||this._activeTab(i)}},_addSelectedItems:function(t){var i=this.model.selectedItems;this._removeNullInArray(i);n.inArray(t,i)==-1&&this.model.selectedItems.push(t)},_deleteSelectedItems:function(t){var i=this.model.selectedItems;n.inArray(t,i)>-1&&(this.model.selectedItems.splice(n.inArray(t,i),1),i.length==0&&i.push(-1))},_removeNullInArray:function(n){var t=n.indexOf(-1);t!=-1&&n.splice(t,1)},_deleteDisabledItems:function(t){var i;n.inArray(t,this.model.disabledItems)>-1&&(i=n.inArray(t,this.model.disabledItems),this.model.disabledItems.splice(i,1))},_addEnabledItems:function(t){n.inArray(t,this.model.enabledItems)<0&&this.model.enabledItems.push(t)},_deleteEnabledItems:function(t){var i;n.inArray(t,this.model.enabledItems)>-1&&(i=n.inArray(t,this.model.enabledItems),this.model.enabledItems.splice(i,1))},_keyPress:function(i){var r,u,f,e;if(this.model.enabled){if(f=n(i.target),(f.hasClass("e-select")||f.hasClass("e-acrdn"))&&(r=this.model.selectedItemIndex),e=i.keyCode?i.keyCode:i.which?i.which:i.charCode,f.hasClass("e-select")||f.hasClass("e-acrdn"))switch(e){case 39:case 40:i.preventDefault();r=r==this.getItemsCount()-1?0:r+1;this._activeTab(r);u=n(this._headers[r]);break;case 38:case 37:i.preventDefault();r=r==0?this.getItemsCount()-1:r-1;this._activeTab(r);u=n(this._headers[r]);break;case 35:i.preventDefault();u=n(this._headers[this.getItemsCount()-1]);this._activeTab(this.getItemsCount()-1);break;case 36:i.preventDefault();u=n(this._headers[0]);this._activeTab(0);break;case 32:case 13:i.preventDefault();n(this._headers[r]).hasClass("e-active")&&(this.model.enableMultipleOpen||this.model.collapsible)?this._hideTab(r):this._activeTab(r)}else if(i.ctrlKey&&!f.hasClass("e-acrdn"))switch(e){case 38:i.preventDefault();r=this._contentPanels.index(f.parent(".e-content"));u=n(this._headers[r]);this._activeTab(r);break;case 33:i.preventDefault();u=n(this._headers[0]);this._activeTab(0);break;case 34:i.preventDefault();u=n(this._headers[this.getItemsCount()-1]);this._activeTab(this.getItemsCount()-1)}t.isNullOrUndefined(u)||(u.addClass("e-focus"),u.focus())}},_wireEvents:function(){this._on(this._headers,this.model.events,this._headerEventHandler);this.model.allowKeyboardNavigation&&this._on(this.element,"keydown",this._keyPress);n(window).on("resize",n.proxy(this._contentPaneSize,this));if(this.model.height&&this.model.width&&this.model.width.toString().indexOf("%")!=-1)n(window).on("resize",n.proxy(this._scrollerRefresh,this))},_scrollerRefresh:function(){this.model.height&&(this.model.heightAdjustMode!="fill"?this._getHeight()>this.model.height&&(this._scrollerObj?this._scrollerObj.refresh():this._setScoller()):this._setHeight())},_unWireEvents:function(){this._off(this._headers,this.model.events);n(window).off("resize",n.proxy(this._contentPaneSize,this));n(window).off("resize",n.proxy(this._scrollerRefresh,this));this.model.allowKeyboardNavigation&&this._off(this.element,"keydown")},_contentPaneSize:function(){var i=this._getDimension(n(this.element).parent(),"height");this._prevSize!=i&&(t.Accordion.HeightAdjustMode.Fill==this.model.heightAdjustMode&&(this.model.enableMultipleOpen?this._paneAdjustSize():this._setHeightStyle(this.model.heightAdjustMode)),this._prevSize=i)},_disableItems:function(t){var i,r,u;if(n.isArray(t))for(i=0;i<t.length;i++)n(this._headers[t[i]]).addClass("e-disable"),n(this._contentPanels[t[i]]).addClass("e-disable"),n(this._headers[t[i]]).off(this.model.events),this.model.disabledItems.indexOf(t[i])==-1&&this.model.disabledItems.push(t[i]),this._hideTab(t[i],!0),this._deleteEnabledItems(t[i]);else n(this._headers[t]).addClass("e-disable"),n(this._contentPanels[t]).addClass("e-disable"),n(this._headers[t]).off(this.model.events),n(this.model.disabledItems).index(t[i])==-1&&this.model.disabledItems!=t&&this.model.disabledItems.push(t),this._hideTab(t,!0),this._deleteEnabledItems(t[i]);if(!this.model.enableMultipleOpen){for(u=this.model.selectedItemIndex,i=0;i<this.model.disabledItems.length;i++)r=this.model.selectedItemIndex,n.inArray(r,this.model.disabledItems)>=0&&(r++,this.model.selectedItemIndex=r);if(r==this._headers.length)for(r=r-1,i=0;i<this.model.disabledItems.length;i++)n.inArray(r,this.model.disabledItems)>=0&&(r--,this.model.selectedItemIndex=r);u!==this.model.selectedItemIndex&&this._selectedItemsAction(r)}},enableItems:function(t){if(!this.model.enabled)return!1;if(n.isArray(t))for(var i=0;i<t.length;i++)n(this._headers[t[i]]).removeClass("e-disable"),n(this._contentPanels[t[i]]).removeClass("e-disable"),this._on(n(this._headers[t[i]]),this.model.events,this._headerEventHandler),this.model.enabledItems.indexOf(t[i])==-1&&this.model.enabledItems.push(t[i]),this._deleteDisabledItems(t[i]);else n(this._headers[t]).removeClass("e-disable"),n(this._contentPanels[t]).removeClass("e-disable"),this._on(n(this._headers[t]),this.model.events,this._headerEventHandler),this.model.enabledItems.indexOf(t[i])==-1&&this.model.enabledItems.push(t[i]),this._deleteDisabledItems(t)},disableItems:function(n){if(!this.model.enabled)return!1;this._disableItems(n)},addItem:function(i,r,u,f){var h,c,o,e,a,l,s;if(t.isNullOrUndefined(f)&&(f=!1),t.isNullOrUndefined(u)||u>this._headers.length)this._addItemIndex=this._headers.length;else if(u>=0&&u<=this._headers.length)this._addItemIndex=u;else return!1;for(c=this.model.enableAnimation,c&&(this.model.enableAnimation=!1),this.model.heightAdjustMode=="fill"&&this._headers.next().height("auto"),o=0;o<this.model.disabledItems.length;o++)this.model.disabledItems[o]>=this._addItemIndex&&this.model.disabledItems[o]++;if(e=t.buildTag("h3").addClass("e-select").attr("role","tab").attr("tabindex",0),this._addItemIndex==0?e.insertBefore(n(this._headers[this._addItemIndex])):e.insertAfter(n(this._headers[this._addItemIndex-1]).next()),this._addItemIndex<=this.model.selectedItemIndex&&(this.model.selectedItemIndex+=1),this._headers.length||(this._headers=e,this.element.append(this._headers),this.model.selectedItemIndex=0),a=t.buildTag("a").attr("href",f?r:"#").text(i),a.appendTo(e),f&&(r=""),h=n(r).length>0?n(r):t.buildTag("div").text(r),h.addClass("e-content").attr("role","tabpanel").attr("aria-hidden",!0).hide(),h.insertAfter(e),this._headers=this.element.find("> :even"),this._contentPanels=this._headers.next(),t.buildTag("span.e-icon e-acrdn-icon "+this.model.customIcon.header).prependTo(e),this.model.showCloseButton&&(l=this._createDeleteIcon(),e.append(l),this._on(l,"click",this._panelDeleteClick)),this.model.enableMultipleOpen){for(this.model.selectedItems=[],s=0;s<this._headers.length;s++)n(this._headers[s]).hasClass("e-active")&&this.model.selectedItems.push(s);this._selectedItemsAction(this.model.selectedItems)}else this.model.selectedItems=[],this.model.selectedItems.push(this.model.selectedItemIndex),this._selectedItemsAction(this.model.selectedItems);this.model.width=this.model.width||this.element.css("width");this.model.height&&this.element.css("height",this.model.height);this.model.enableRTL&&(e.addClass("e-rtl"),h.addClass("e-rtl"));this.model.enableMultipleOpen||this._renderHeaderIcon();t.isNullOrUndefined(this.model.headerSize)||this._setHeaderSize();this._on(e,this.model.events,this._headerEventHandler);this._roundedCorner(this.model.showRoundedCorner);this.model.heightAdjustMode!="fill"||this.model.enableMultipleOpen||this._setHeightStyle(this.model.heightAdjustMode);this.disableItems(this.model.disabledItems);this._setEnabledItems();c&&(this.model.enableAnimation=!0)},removeItem:function(t){var u,f,i,r;if(!this.model.enabled)return!1;if(t!=null&&t>-1&&t<this._headers.length){for(u=this.model.enableAnimation,u&&(this.model.enableAnimation=!1),f=n(this._headers[t]).remove(),i=0;i<n(this.model.disabledItems).length;i++)this.model.disabledItems[i]>t?this.model.disabledItems[i]--:this.model.disabledItems[i]==t&&this.model.disabledItems.splice(i,1);if(n(this.element.find(">div.e-content")[t]).remove(),this._contentPanels.splice(t,1),this._headers.splice(t,1),this.model.enableMultipleOpen&&this.model.selectedItemIndex==t?this.model.selectedItemIndex=null:this.model.enableMultipleOpen||(t==this.model.selectedItemIndex&&this.model.collapsible?this.model.selectedItemIndex=null:t==0&&this.model.selectedItemIndex==0?this.model.selectedItemIndex=0:t<=this.model.selectedItemIndex&&(this.model.selectedItemIndex-=1)),this._unWireEvents(),this._wireEvents(),this.model.heightAdjustMode!="fill"||this.model.enableMultipleOpen||this._setHeightStyle(this.model.heightAdjustMode),this.model.enableMultipleOpen){for(this.model.selectedItems=[],r=0;r<this._headers.length;r++)n(this._headers[r]).hasClass("e-active")&&this.model.selectedItems.push(r);this._selectedItemsAction(this.model.selectedItems)}else this.model.selectedItems=[],this.model.selectedItems.push(this.model.selectedItemIndex),this._selectedItemsAction(this.model.selectedItems);this.disableItems(this.model.disabledItems);this._setEnabledItems();u&&(this.model.enableAnimation=!0)}},collapseAll:function(){if(this.model.enableMultipleOpen||this.model.collapsible){this.model.selectedItemIndex=null;for(var n=0;n<this.getItemsCount();n++)this._hideTab(n,!1,!0)}},expandAll:function(){if(this.model.enableMultipleOpen)for(var n=0;n<this.getItemsCount();n++)this._activeTab(n,!1,!0),this.model.selectedItemIndex=n},disable:function(){var t,i;for(this.model.enabled=!1,t=0;t<this._headers.length;t++)this._disableItems([t]);n(this.element.parent(".e-scroller")).addClass(".e-disable");this._scrollerObj&&(i=n(this.element.parent(".e-scroller")).find(".e-vscrollbar"),i.length>0&&this._scrollerObj.disable());this._unWireEvents()},enable:function(){this.model.enabled=!0;for(var n=0;n<this._headers.length;n++)this.enableItems([n]);this._scrollerObj&&this._scrollerObj.enable();this._unWireEvents();this._wireEvents()},refresh:function(){this._contentPaneSize()},_selected:function(n){if(!this.model.enabled)return!1;n!=null&&this.model.selectedItemIndex!=n&&this._activeTab(n)},show:function(){if(!this.model.enabled)return!1;this.element.css("visibility","visible")},hide:function(){if(!this.model.enabled)return!1;this.element.css("visibility","hidden")},getItemsCount:function(){if(this._headers)return this._headers.length}});t.Accordion.HeightAdjustMode={Content:"content",Auto:"auto",Fill:"fill"}})(jQuery,Syncfusion)});
