//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class GlobalResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal GlobalResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.GlobalResources", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ο λογαριασμός δεν υπάρχει. Συνδεθείτε στο &lt;a href=&quot;https://mentalview.gr&quot; target=&quot;_blank&quot;&gt;mentalview.gr&lt;/a&gt; για να δημιουργήσετε ένα νέο λογαριασμό..
        /// </summary>
        internal static string AccountNotExists {
            get {
                return ResourceManager.GetString("AccountNotExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Προσθήκη.
        /// </summary>
        internal static string AddText {
            get {
                return ResourceManager.GetString("AddText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MentalView.
        /// </summary>
        internal static string ApplicationTitle {
            get {
                return ResourceManager.GetString("ApplicationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Συνεδρία.
        /// </summary>
        internal static string AppointmentReportText {
            get {
                return ResourceManager.GetString("AppointmentReportText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Μεγάλο.
        /// </summary>
        internal static string BigText {
            get {
                return ResourceManager.GetString("BigText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ακύρωση.
        /// </summary>
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Άκυρο.
        /// </summary>
        internal static string CancelText {
            get {
                return ResourceManager.GetString("CancelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Κλείσιμο.
        /// </summary>
        internal static string Close {
            get {
                return ResourceManager.GetString("Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Σχόλια.
        /// </summary>
        internal static string CommentsText {
            get {
                return ResourceManager.GetString("CommentsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ο πελάτης δεν έχει email..
        /// </summary>
        internal static string ContactHasNoEmailMessage {
            get {
                return ResourceManager.GetString("ContactHasNoEmailMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Πελάτης.
        /// </summary>
        internal static string ContactReportText {
            get {
                return ResourceManager.GetString("ContactReportText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Σύμβαση.
        /// </summary>
        internal static string ContractText {
            get {
                return ResourceManager.GetString("ContractText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Διαγραφή.
        /// </summary>
        internal static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Είστε σίγουρος ότι θέλετε να γίνει η διαγραφή;.
        /// </summary>
        internal static string DeleteConfirmationMessage {
            get {
                return ResourceManager.GetString("DeleteConfirmationMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Δεν επιτρέπεται η διαγραφή..
        /// </summary>
        internal static string DeleteProhibitedMessage {
            get {
                return ResourceManager.GetString("DeleteProhibitedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Έχετε επιλέξει να διαγράψετε ένα επαναλαμβανόμενο συμβάν. Θέλετε να διαγράψετε και όλες τις επόμενες επαναλήψεις;&lt;BR/&gt;
        ///- Για να διαγράψετε την συγκεκριμένη επανάληψη και όλες τις επόμενες επαναλήψεις, επιλέξετε &lt;b&gt;Ναι&lt;/b&gt;.&lt;BR/&gt;
        ///- Για να διαγράψετε μόνο την συγκεκριμένη επανάληψη, επιλέξετε &lt;b&gt;Όχι&lt;/b&gt;.&lt;BR/&gt;
        ///- Για να ακυρώσετε τη διαγραφή επιλέξτε &lt;b&gt;Άκυρο&lt;/b&gt;..
        /// </summary>
        internal static string DeleteTaskWithUpcomingRecurrencesConfirmationMessage {
            get {
                return ResourceManager.GetString("DeleteTaskWithUpcomingRecurrencesConfirmationMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Έχετε επιλέξει να διαγράψετε μια επαναλαμβανόμενη συνεδρία. Θέλετε να διαγράψετε και όλες τις επόμενες επαναλήψεις;&lt;BR/&gt;
        ///- Για να διαγράψετε την συγκεκριμένη επανάληψη και όλες τις επόμενες επαναλήψεις, επιλέξετε &lt;b&gt;Ναι&lt;/b&gt;.&lt;BR/&gt;
        ///- Για να διαγράψετε μόνο την συγκεκριμένη επανάληψη, επιλέξετε &lt;b&gt;Όχι&lt;/b&gt;.&lt;BR/&gt;
        ///- Για να ακυρώσετε τη διαγραφή επιλέξτε &lt;b&gt;Άκυρο&lt;/b&gt;..
        /// </summary>
        internal static string DeleteWithUpcomingRecurrencesConfirmationMessage {
            get {
                return ResourceManager.GetString("DeleteWithUpcomingRecurrencesConfirmationMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Κλείσιμο.
        /// </summary>
        internal static string DoneText {
            get {
                return ResourceManager.GetString("DoneText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        internal static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Συνέβη ένα απρόσμενο σφάλμα..
        /// </summary>
        internal static string ExceptionOccuredMessage {
            get {
                return ResourceManager.GetString("ExceptionOccuredMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Εξαγωγή σε Excel.
        /// </summary>
        internal static string ExportToExcel {
            get {
                return ResourceManager.GetString("ExportToExcel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Εξαγωγή σε PDF.
        /// </summary>
        internal static string ExportToPdf {
            get {
                return ResourceManager.GetString("ExportToPdf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Εξαγωγή σε Word.
        /// </summary>
        internal static string ExportToWord {
            get {
                return ResourceManager.GetString("ExportToWord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Σε εξέλιξη....
        /// </summary>
        internal static string InProgress {
            get {
                return ResourceManager.GetString("InProgress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Οι ημερομηνίες δεν είναι σωστά συμπληρωμένες..
        /// </summary>
        internal static string InvalidDateValuesMessage {
            get {
                return ResourceManager.GetString("InvalidDateValuesMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Οι επιλογές δεν είναι σωστές..
        /// </summary>
        internal static string InvalidInputDataMessage {
            get {
                return ResourceManager.GetString("InvalidInputDataMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Οι ημερομηνίες δεν είναι σωστές..
        /// </summary>
        internal static string InvalidStartEndTimeMessage {
            get {
                return ResourceManager.GetString("InvalidStartEndTimeMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Μεσαίο.
        /// </summary>
        internal static string MediumText {
            get {
                return ResourceManager.GetString("MediumText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Δεν επιτρέπεται να δείτε τον πελάτη..
        /// </summary>
        internal static string NoPermissionToViewContactMessage {
            get {
                return ResourceManager.GetString("NoPermissionToViewContactMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Όχι.
        /// </summary>
        internal static string NoText {
            get {
                return ResourceManager.GetString("NoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Προεπισκόπηση.
        /// </summary>
        internal static string Preview {
            get {
                return ResourceManager.GetString("Preview", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Εκτύπωση.
        /// </summary>
        internal static string Print {
            get {
                return ResourceManager.GetString("Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Αποθήκευση.
        /// </summary>
        internal static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Αποθήκευση &amp; Κλείσιμο.
        /// </summary>
        internal static string SaveClose {
            get {
                return ResourceManager.GetString("SaveClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Η διαδικασία δεν μπορεί να ολοκληρωθεί γιατί το session έχει λήξει..
        /// </summary>
        internal static string SessionExpiredMessage {
            get {
                return ResourceManager.GetString("SessionExpiredMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Μικρό.
        /// </summary>
        internal static string SmallText {
            get {
                return ResourceManager.GetString("SmallText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Η συνδρομή σας έχει λήξει. Συνδεθείτε στο &lt;a href=&quot;https://mentalview.gr&quot; target=&quot;_blank&quot;&gt;mentalview.gr&lt;/a&gt; για να αποκτήσετε νέα συνδρομή..
        /// </summary>
        internal static string SubscriptionExpired {
            get {
                return ResourceManager.GetString("SubscriptionExpired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Σύνολο.
        /// </summary>
        internal static string SummaryText {
            get {
                return ResourceManager.GetString("SummaryText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Σχόλια Επόπτη.
        /// </summary>
        internal static string SupervisorCommentsText {
            get {
                return ResourceManager.GetString("SupervisorCommentsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Εργασία.
        /// </summary>
        internal static string TaskText {
            get {
                return ResourceManager.GetString("TaskText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Σχόλια Θεραπευτή.
        /// </summary>
        internal static string TherapistCommentsText {
            get {
                return ResourceManager.GetString("TherapistCommentsText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Τώρα.
        /// </summary>
        internal static string TimeNowText {
            get {
                return ResourceManager.GetString("TimeNowText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ώρα.
        /// </summary>
        internal static string TimeText {
            get {
                return ResourceManager.GetString("TimeText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Σήμερα.
        /// </summary>
        internal static string TodayText {
            get {
                return ResourceManager.GetString("TodayText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Τελικό Σύνολο.
        /// </summary>
        internal static string TotalSummaryText {
            get {
                return ResourceManager.GetString("TotalSummaryText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ο χρήστης δεν έχει email..
        /// </summary>
        internal static string UserHasNoEmailMessage {
            get {
                return ResourceManager.GetString("UserHasNoEmailMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Πολύ Μεγάλο.
        /// </summary>
        internal static string VeryBigText {
            get {
                return ResourceManager.GetString("VeryBigText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Τα στοιχεία πρόσβασης δεν είναι σωστά..
        /// </summary>
        internal static string WrongCredentialsMessage {
            get {
                return ResourceManager.GetString("WrongCredentialsMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ναι.
        /// </summary>
        internal static string YesText {
            get {
                return ResourceManager.GetString("YesText", resourceCulture);
            }
        }
    }
}
