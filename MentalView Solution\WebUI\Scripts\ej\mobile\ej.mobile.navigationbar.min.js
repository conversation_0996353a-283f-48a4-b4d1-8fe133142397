/*!
*  filename: ej.mobile.navigationbar.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function(n,t,i){t.widget({ejmNavigationBar:"ej.mobile.NavigationBar",ejmHeader:"ej.mobile.Header",ejmFooter:"ej.mobile.Footer",ejmToolbar:"ej.mobile.Toolbar"},{_setFirst:!0,_rootCSS:"e-m-navbar",_tags:[{tag:"items",attr:["iconName","badgeValue"],content:"template"}],defaults:{title:null,titleAlignment:"left",isRelative:!1,renderMode:"auto",templateID:null,enablePersistence:!1,cssClass:"",mode:"header",position:"auto",iconAlignment:"auto",locale:"en-US",badge:{maxValue:99},android:{position:"auto"},ios7:{position:"auto"},flat:{position:"auto"},windows:{position:"auto"},touchStart:null,touchEnd:null,ellipsisTouchStart:null,ellipsisTouchEnd:null,enableRippleEffect:t.isAndroid()?!0:!1,items:[]},dataTypes:{title:"string",titleAlignment:"enum",items:"data",isRelative:"boolean",renderMode:"enum",templateID:"string",cssClass:"string",enableRippleEffect:"boolean",mode:"enum",position:"enum",iconAlignment:"enum",android:{position:"enum"},ios7:{position:"enum"},flat:{position:"enum"},windows:{position:"enum"},badge:{maxValue:"number"},enablePersistence:"boolean"},_init:function(){t.setRenderMode(this);this._getLocalizedLabels();this.model.title=t.isNullOrUndefined(this.model.title)?this._localizedLabels.title:this.model.title;this._renderControl();this._wireEvents(!1)},_getLocalizedLabels:function(){this._localizedLabels=t.getLocalizedConstants(this.sfType,this.model.locale)},_renderControl:function(){this._orgEle=n(this.element).clone();this._angularContent=this.element.children();this._position=this.model[this.model.renderMode].position!="auto"?this.model[this.model.renderMode].position:this.model.position!="auto"?this.model.position:this.model.mode=="header"?"top":this.model.renderMode=="android"?"top":"bottom";this.model.iconAlignment=!t.isNullOrUndefined(this.model.iconAlignment)&&this.model.iconAlignment!="auto"?this.model.iconAlignment:this.model.renderMode=="windows"?"group":"split";this.element.addClass("e-m-user-select e-m-"+this.model.renderMode+" "+this.model.cssClass+" e-m-navbar-"+this._position+" e-m-navbar-"+this.model.mode+" e-m-"+(this.model.isRelative?"rel":"abs")+(this.model.templateID?" e-m-navbar-template":""));this.model.templateID?this._renderNavigationBarTemplate():this._renderDefaultNavigationBar()},_renderNavigationBarTemplate:function(){var n=t.getCurrentPage().find("#"+this.model.templateID);n.length&&(this.element.empty(),t.destroyWidgets(n),this._template=n[0].nodeName&&n[0].nodeName.toLowerCase()=="script"?t.getClearString(n[0].innerHTML):n,this.element.append(this._template),t.widget.init(this.element),this._compileAngularElement(this.element.children()))},_renderDefaultNavigationBar:function(){this._compileAngularElement(this._angularContent);(this.model.mode=="header"||this.model.mode=="toolbar"&&this._position=="top")&&(this._titleSpan=t.buildTag("span.e-m-navbar-text",this.model.title,null,null).appendTo(this.element),this._titleSpan.addClass("e-m-title-"+this.model.titleAlignment).css({"margin-left":this.element.find(".e-m-navbar-left").length?t.getDimension(this.element.find(".e-m-navbar-left"),"outerWidth")+32:16,"margin-right":this.model.mode=="header"?this.element.find(".e-m-navbar-right").length?t.getDimension(this.element.find(".e-m-navbar-right"),"outerWidth")+32:16:""}));this.model.mode=="toolbar"&&this._toolbarRendering()},_compileAngularElement:function(i){(!t.isAppNullOrUndefined()&&App.angularAppName||t.angular.defaultAppName)&&t.angular.compile(n(i))},_toolbarRendering:function(){var i=this,u=this.model.items.length?!0:!1,r,f;if(this._items=[],u)this._items=this.model.items;else for(this._navIcons=this.element.find("li"),r=0,f=this._navIcons.length;r<f;r++)this._items.push(this._insertItemValue(n(this._navIcons[r])));this._navContainer=u?t.buildTag("ul.e-m-clearall").appendTo(this.element):this.element.find("ul").addClass("e-m-clearall");n.each(this._items,function(r){var e=(u?t.buildTag("li").attr("data-ej-badgevalue",!t.isNullOrUndefined(this.badgeValue)&&this.badgeValue>0?this.badgeValue:null):n(i._navIcons[r])).attr("id","nav-item"+r).addClass("e-m-navbar-icon e-m-icon-"+this.iconName).appendTo(i._navContainer),f;parseInt(e.attr("data-ej-badgevalue"))>0&&(f=parseInt(this.badgeValue),e.addClass("e-m-nav-badge").attr("badgeValue",f<=i.model.badge.maxValue?f:i.model.badge.maxValue.toString()+"+"))});this._allIconsLength=this._navContainer.find("li").length;this._maxIconsLength=this._position=="top"?this._allIconsLength>3?2:this._allIconsLength:this._allIconsLength>5?5:this._allIconsLength;this._ellipsis=null;this._allIconsLength>this._maxIconsLength&&(this._ellipsis=t.buildTag("span.e-m-navbar-ellipsis e-m-icon-overflow").appendTo(this.element),this._overflowContainer=this._navContainer.clone(),this.element.append(this._overflowContainer),this._navContainer.find("li").slice(this._maxIconsLength,this._allIconsLength).remove(),this._overflowContainer.addClass("e-m-overflow-container").find("li").slice(0,this._maxIconsLength).remove(),this._overflowIcons=this._overflowContainer.find("li").addClass("e-m-overflow-icon"));this._navbarIcons=this._navContainer.addClass("e-m-navbar-container"+(this._position=="bottom"?" e-m-"+this.model.iconAlignment+"-icons"+(t.isNullOrUndefined(this._ellipsis)?"":" e-m-more"):"")).find("li");this._position=="top"&&(this._navContainer.addClass(this._allIconsLength>this._maxIconsLength?"e-m-margin-right":""),this._titleSpan.addClass("e-m-margin-right-"+(this._allIconsLength>this._maxIconsLength?3:this._allIconsLength)));this._iconWidth();this._setEnableRippleEffect()},_setEnableRippleEffect:function(){this.element.find("li")[this.model.enableRippleEffect?"addClass":"removeClass"]("e-ripple")},_insertItemValue:function(n){var r={};return r.iconName=t.getAttrVal(n,"data-ej-iconname",i),r.badgeValue=t.getAttrVal(n,"data-ej-badgevalue",i),r},_iconWidth:function(){this.model.iconAlignment!="group"?this.model.mode=="toolbar"&&this._position=="bottom"&&(t.isNullOrUndefined(this._ellipsis)?this._navContainer.width("100%"):this._navContainer.width(window.innerWidth-56)):this._navContainer.width("auto")},_createDelegates:function(){this._ellipsisTouchStartHndlr=n.proxy(this._ellipsisTouchStart,this);this._ellipsisTouchEndHndlr=n.proxy(this._ellipsisTouchEnd,this);this._touchStartHndlr=n.proxy(this._touchStart,this);this._touchEndHndlr=n.proxy(this._touchEnd,this);this._resizeHndlr=n.proxy(this._resize,this)},_wireEvents:function(i){this._createDelegates();t.listenEvents([n(this._ellipsis),n(this._ellipsis),this._navbarIcons,this._navbarIcons],[t.startEvent(),t.endEvent(),t.startEvent(),t.endEvent()],[this._ellipsisTouchStartHndlr,this._ellipsisTouchEndHndlr,this._touchStartHndlr,this._touchEndHndlr],i);t.listenTouchEvent(n(document),t.tapEvent(),this._docClickHndlr,i);t.listenTouchEvent(n(window),"onorientationchange"in window?"orientationchange":"resize",this._resizeHndlr,i)},_touchStart:function(n){this._touchEvents(n,!0,"touchStart")},_touchEnd:function(n){this._touchEvents(n,!0,"touchEnd")},_ellipsisTouchStart:function(n){this._touchEvents(n,!1,"ellipsisTouchStart")},_ellipsisTouchEnd:function(n){this._touchEvents(n,!1,"ellipsisTouchEnd")},_touchEvents:function(i,r,u){var f=r?{element:n(i.target),iconname:i.target.getAttribute("data-ej-iconname"),index:i.target.id.split("item")[1]}:{element:n(i.target)},e=i.type==t.startEvent()?"add":"remove";n(i.target)[e+"Class"]("e-m-state-active");this.model[u]&&this._trigger(u,f)},_resize:function(){var n=this;setTimeout(function(){n._iconWidth()},t.isAndroid()?200:0)},_setModel:function(n){var r=!1,t,i;for(t in n)i="_set"+t.charAt(0).toUpperCase()+t.slice(1),this[i]?this[i](n[t]):r=!0;r&&this._refresh()},_setRenderMode:function(){this.element.removeClass("e-m-android e-m-ios7 e-m-windows e-m-flat").addClass("e-m-"+this.model.renderMode)},_setIsRelative:function(n){this.element.removeClass("e-m-abs e-m-rel").addClass("e-m-"+(n?"rel":"abs"))},_setTitle:function(){this._titleSpan.text(this.model.title)},_setTemplateID:function(){this._renderNavigationBarTemplate()},_setTitleAlignment:function(){this._titleSpan.removeClass("e-m-title-left e-m-title-center e-m-title-right").addClass("e-m-title-"+this.model.titleAlignment)},_setIconAlignment:function(){this._navContainer.removeClass("e-m-split-icons e-m-group-icons").addClass("e-m-"+this.model.iconAlignment+"-icons");this._iconWidth()},_setLocale:function(){this._getLocalizedLabels();this.model.title=this._localizedLabels.title;this._setTitle(this.model.title)},_refresh:function(){this._clearElement();this.element.addClass("e-m-navbar");this._renderControl()},_clearElement:function(){this.element.removeAttr("class");this.element.html(this._orgEle.html())},_destroy:function(){this._clearElement()},_addRemoveRefresh:function(){this.element.empty();this._renderControl();this._wireEvents();this._items=this.model.items},removeItem:function(n){this.model.items=this._items;n>=0&&this.model.items.splice(n,1);this._addRemoveRefresh()},addItem:function(t,i){this.model.items=this._items;i>=0?this.model.items.splice(i,0,this._insertItemValue(n(t))):this.model.items.push(this._insertItemValue(n(t)));this._addRemoveRefresh()},disableItem:function(t){t>=0&&n(this._navbarIcons[t]).addClass("e-m-state-disabled")},enableItem:function(t){t>=0&&n(this._navbarIcons[t]).removeClass("e-m-state-disabled")},hideItem:function(t){t>=0&&n(this._navbarIcons[t]).addClass("e-m-hide")},showItem:function(t){t>=0&&n(this._navbarIcons[t]).removeClass("e-m-hide")},getTitle:function(){return this._titleSpan.text()},hide:function(){this.element.addClass("e-m-hide")},show:function(){this.element.removeClass("e-m-hide")}});t.mobile.NavigationBar.Position={Top:"top",Bottom:"bottom"};t.mobile.NavigationBar.Mode={Header:"header",Toolbar:"toolbar"};t.mobile.NavigationBar.TitleAlignment={Left:"left",Center:"center",Right:"right"};t.mobile.NavigationBar.IconAlignment={Split:"split",Group:"group"};t.mobile.NavigationBar.Locale=t.mobile.NavigationBar.Locale||{};t.mobile.Header.Locale=t.mobile.Header.Locale||{};t.mobile.Footer.Locale=t.mobile.Footer.Locale||{};t.mobile.Toolbar.Locale=t.mobile.Toolbar.Locale||{};t.mobile.NavigationBar.Locale["default"]=t.mobile.NavigationBar.Locale["en-US"]=t.mobile.Header.Locale["default"]=t.mobile.NavigationBar.Locale["en-US"]=t.mobile.Footer.Locale["default"]=t.mobile.NavigationBar.Locale["en-US"]=t.mobile.Toolbar.Locale["default"]=t.mobile.NavigationBar.Locale["en-US"]={title:null}})(jQuery,Syncfusion)});
