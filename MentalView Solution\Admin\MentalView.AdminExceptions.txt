Product: MentalView.Admin
DATE: 3/12/2025 1:44:17 PM
STACK TRACE:    at Microsoft.Data.SqlClient.SqlConnection.PermissionDemand()
   at Microsoft.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at Microsoft.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry, SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open(SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open()
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at Admin.Reporting.Reports.GetTenantsStatisticsExcel() in D:\Users\peter\Documents\Projects\MentalView\MentalView Solution\Admin\Reporting\Reports.cs:line 51
   at Admin.Components.Pages.Reports.ExportTenantsStatisticsExcelBtnClick(MouseEventArgs e) in D:\Users\peter\Documents\Projects\MentalView\MentalView Solution\Admin\Components\Pages\Reports.razor.cs:line 33
ERROR: The ConnectionString property has not been initialized.
(METHOD: Void PermissionDemand())
(Source: Microsoft.Data.SqlClient)
ErrorID: 7BA1417C-1DAF-43D1-87AD-8072C65C15B4

Product: MentalView.Admin
DATE: 3/13/2025 3:01:32 PM
STACK TRACE:    at C1.Util.Licensing.SafeLicenseContext.GetSavedLicenseKey(LicenseContext context, Type type, Assembly resAsm)
   at C1.Util.Licensing.ProviderInfo.ValidateRuntime(Type type, LicenseContext context)
   at C1.Util.Licensing.ProviderInfo.Validate(Type type, Object instance, Boolean showNagDialog)
   at C1.Util.Licensing.ProviderInfo.Validate(Type type, Object instance)
   at C1.C1Report.C1Report..ctor()
   at Admin.Reporting.ReportsFactory.PrepareAppointmentsCountPerYearReport() in D:\Users\peter\Documents\Projects\MentalView\MentalView Solution\Admin\Reporting\ReportsFactory.cs:line 115
   at Admin.Components.Pages.Reports.PreviewAppointmentsCountPerYearBtnClick(MouseEventArgs e) in D:\Users\peter\Documents\Projects\MentalView\MentalView Solution\Admin\Components\Pages\Reports.razor.cs:line 107
ERROR: Method not found: 'System.String System.AppDomainSetup.get_LicenseFile()'.
(METHOD: System.String GetSavedLicenseKey(System.ComponentModel.LicenseContext, System.Type, System.Reflection.Assembly))
(Source: C1.C1Report.2)
ErrorID: 7BC42EB6-D264-4445-A4BB-35B6EC9A9D3E

Product: MentalView.Admin
DATE: 3/13/2025 3:08:23 PM
STACK TRACE:    at C1.Util.Licensing.SafeLicenseContext.GetSavedLicenseKey(LicenseContext context, Type type, Assembly resAsm)
   at C1.Util.Licensing.ProviderInfo.ValidateRuntime(Type type, LicenseContext context)
   at C1.Util.Licensing.ProviderInfo.Validate(Type type, Object instance, Boolean showNagDialog)
   at C1.Util.Licensing.ProviderInfo.Validate(Type type, Object instance)
   at C1.C1Report.C1Report..ctor()
   at Admin.Reporting.ReportsFactory.PrepareAppointmentsCountPerYearReport() in D:\Users\peter\Documents\Projects\MentalView\MentalView Solution\Admin\Reporting\ReportsFactory.cs:line 115
   at Admin.Components.Pages.Reports.PreviewAppointmentsCountPerYearBtnClick(MouseEventArgs e) in D:\Users\peter\Documents\Projects\MentalView\MentalView Solution\Admin\Components\Pages\Reports.razor.cs:line 79
ERROR: Method not found: 'System.String System.AppDomainSetup.get_LicenseFile()'.
(METHOD: System.String GetSavedLicenseKey(System.ComponentModel.LicenseContext, System.Type, System.Reflection.Assembly))
(Source: C1.C1Report.2)
ErrorID: AB864536-63BC-41ED-8346-6E70D495C5B1

Product: MentalView.Admin
DATE: 3/13/2025 3:08:35 PM
STACK TRACE:    at C1.Util.Licensing.SafeLicenseContext.GetSavedLicenseKey(LicenseContext context, Type type, Assembly resAsm)
   at C1.Util.Licensing.ProviderInfo.ValidateRuntime(Type type, LicenseContext context)
   at C1.Util.Licensing.ProviderInfo.Validate(Type type, Object instance, Boolean showNagDialog)
   at C1.Util.Licensing.ProviderInfo.Validate(Type type, Object instance)
   at C1.C1Report.C1Report..ctor()
   at Admin.Reporting.ReportsFactory.PrepareAppointmentsCountPerYearReport() in D:\Users\peter\Documents\Projects\MentalView\MentalView Solution\Admin\Reporting\ReportsFactory.cs:line 115
   at Admin.Components.Pages.Reports.PreviewAppointmentsCountPerYearBtnClick(MouseEventArgs e) in D:\Users\peter\Documents\Projects\MentalView\MentalView Solution\Admin\Components\Pages\Reports.razor.cs:line 79
ERROR: Method not found: 'System.String System.AppDomainSetup.get_LicenseFile()'.
(METHOD: System.String GetSavedLicenseKey(System.ComponentModel.LicenseContext, System.Type, System.Reflection.Assembly))
(Source: C1.C1Report.2)
ErrorID: 31C37B03-7CFA-4691-B5B2-1EDC61A6AFBC

Product: MentalView.Admin
DATE: 3/13/2025 3:22:52 PM
STACK TRACE:    at C1.Util.Licensing.SafeLicenseContext.GetSavedLicenseKey(LicenseContext context, Type type, Assembly resAsm)
   at C1.Util.Licensing.ProviderInfo.ValidateRuntime(Type type, LicenseContext context)
   at C1.Util.Licensing.ProviderInfo.Validate(Type type, Object instance, Boolean showNagDialog)
   at C1.Util.Licensing.ProviderInfo.Validate(Type type, Object instance)
   at C1.C1Report.C1Report..ctor()
   at Admin.Reporting.ReportsFactory.PrepareAppointmentsCountPerYearReport() in D:\Users\peter\Documents\Projects\MentalView\MentalView Solution\Admin\Reporting\ReportsFactory.cs:line 115
   at Admin.Components.Pages.Reports.PreviewAppointmentsCountPerYearBtnClick(MouseEventArgs e) in D:\Users\peter\Documents\Projects\MentalView\MentalView Solution\Admin\Components\Pages\Reports.razor.cs:line 79
ERROR: Method not found: 'System.String System.AppDomainSetup.get_LicenseFile()'.
(METHOD: System.String GetSavedLicenseKey(System.ComponentModel.LicenseContext, System.Type, System.Reflection.Assembly))
(Source: C1.C1Report.2)
ErrorID: 815B73E8-A85C-4FCC-877B-4835452CE233

Product: MentalView.Admin
DATE: 3/13/2025 3:38:15 PM
STACK TRACE:    at C1.Util.Licensing.SafeLicenseContext.GetSavedLicenseKey(LicenseContext context, Type type, Assembly resAsm)
   at C1.Util.Licensing.ProviderInfo.ValidateRuntime(Type type, LicenseContext context)
   at C1.Util.Licensing.ProviderInfo.Validate(Type type, Object instance, Boolean showNagDialog)
   at C1.Util.Licensing.ProviderInfo.Validate(Type type, Object instance)
   at C1.C1Report.C1Report..ctor()
   at Admin.Reporting.ReportsFactory.PrepareAppointmentsCountPerYearReport() in D:\Users\peter\Documents\Projects\MentalView\MentalView Solution\Admin\Reporting\ReportsFactory.cs:line 115
   at Admin.Components.Pages.Reports.PreviewAppointmentsCountPerYearBtnClick(MouseEventArgs e) in D:\Users\peter\Documents\Projects\MentalView\MentalView Solution\Admin\Components\Pages\Reports.razor.cs:line 79
ERROR: Method not found: 'System.String System.AppDomainSetup.get_LicenseFile()'.
(METHOD: System.String GetSavedLicenseKey(System.ComponentModel.LicenseContext, System.Type, System.Reflection.Assembly))
(Source: C1.C1Report.2)
ErrorID: 7F74C1EB-D800-4A8E-846E-4AE07C5C6410

