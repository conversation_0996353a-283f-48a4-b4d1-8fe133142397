/*!
*  filename: ej.mobile.inputbutton.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function(n,t){t.widget({ejmRadioButton:"ej.mobile.RadioButton",ejmCheckBox:"ej.mobile.CheckBox"},{_setFirst:!0,_rootCSS:"e-m-editor",validTags:["input"],defaults:{renderMode:"auto",cssClass:"",enablePersistence:!1,enabled:!0,checked:!1,touchStart:null,enableRippleEffect:t.isAndroid()?!0:!1,touchEnd:null,beforeChange:null,change:null},dataTypes:{renderMode:"enum",enablePersistence:"boolean",enabled:"boolean",enableRippleEffect:"boolean",checked:"boolean"},_init:function(){this._cloneElement=this.element.clone();this._renderControl();this._wireEvents()},_getControlName:function(){return this.pluginName.replace("ejm","")},_renderControl:function(){t.setRenderMode(this);var n=this.model;this._radioCheckCoreProperties(this._getControlName().toLowerCase())},_radioCheckCoreProperties:function(i){this.model.type=i=="checkbox"?"checkbox":"radio";this.element.attr("type")||this.element.attr("type",this.model.type);this.element.css("display","none");this._wrapper=this.element.parent();this._elementName=this.element.attr("name");this._inputWrapper=t.buildTag("div.e-m-"+this.model.renderMode).addClass("e-m-input-"+i+" "+this.model.cssClass+" "+(this._elementName?this._elementName:""));this.element.addClass(this.model.cssClass);this._inputWrapper.insertBefore(this.element);this._setEnableRippleEffect();this.element.appendTo(this._inputWrapper);this._label=n("label[for='"+this._id+"']");this._setCommonProperties()},_setEnableRippleEffect:function(){this._inputWrapper[this.model.enableRippleEffect?"addClass":"removeClass"]("e-ripple")},_setCommonProperties:function(){var n=this.model;n.checked&&this._setChecked(!0);n.enabled||this._setEnabled(!1)},_setChecked:function(n){this.element.prop("disabled")||(this.element.prop("checked",n),this._inputWrapper[n?"addClass":"removeClass"]("e-m-state-active"))},_setEnabled:function(n){this.element.attr("disabled",!n);this._inputWrapper[n?"removeClass":"addClass"]("e-m-state-disabled")},_setRenderMode:function(){this.element.prop("disabled")||this._inputWrapper.removeClass("e-m-ios7 e-m-android e-m-windows e-m-flat").addClass("e-m-"+this.model.renderMode)},_createDelegate:function(){this._touchStartProxy=n.proxy(this._touchStart,this);this._touchEndProxy=n.proxy(this._touchEnd,this)},_wireEvents:function(n){var i=this.model,r=this.element,u=this._getControlName();this._createDelegate();t.listenEvents([this._inputWrapper,this._inputWrapper,this._label,this._label],[t.startEvent(),t.endEvent(),t.startEvent(),t.endEvent()],[this._touchStartProxy,this._touchEndProxy,this._touchStartProxy,this._touchEndProxy],n)},_touchStart:function(n){if(this.model.enabled){this._inputWrapper.addClass("e-m-state-glow");var t={isChecked:this.model.checked,value:this.element.val(),controlType:this.model.type,element:this.element,events:n};this.model.touchStart&&this._trigger("touchStart",t);this.model.beforeChange&&this._trigger("beforeChange",t)}},_touchEnd:function(t){var r,i;this.model.enabled&&(this.model.type=="checkbox"&&(this.element.attr("checked")&&this.element.attr("checked").length?(this.element.attr("checked",!1),this.model.checked=!1,this._inputWrapper.removeClass("e-m-state-active")):(this.element.attr("checked",!0),this.model.checked=!0,this._inputWrapper.addClass("e-m-state-active"))),this.model.type=="radio"&&(r=n(".e-m-input-radiobutton."+this._elementName),n.each(r,function(t,i){n(i).removeClass("e-m-state-active");n(i).children().attr("checked",!1)}),this.element.attr("checked",!0),this.model.checked=!0,this.element.parent().addClass("e-m-state-active")),i={isChecked:this.model.checked,value:this.element.val(),controlType:this.model.type,element:this.element,events:t},this._inputWrapper.removeClass("e-m-state-glow"),this.model.touchEnd&&this._trigger("touchEnd",i),this.model.change&&this._trigger("change",i))},enable:function(){this.model.enabled=!0;this._inputWrapper.removeClass("e-m-state-disabled")},disable:function(){this.model.enabled=!1;this._inputWrapper.addClass("e-m-state-disabled")},isChecked:function(){return this.model.checked},_setModel:function(n){for(var t in n)this["_set"+t.charAt(0).toUpperCase()+t.slice(1)](n[t])},_clearElement:function(){return this._cloneElement.insertBefore(this.element.parent()),this.element.parent().remove(),this.element.remove(),this._wireEvents(!0),this},_destroy:function(){this._clearElement()},_refresh:function(){this._clearElement()._init();this._wireEvents()}})})(jQuery,Syncfusion)});
