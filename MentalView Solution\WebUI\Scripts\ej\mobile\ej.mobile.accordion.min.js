/*!
*  filename: ej.mobile.accordion.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function(n,t){t.mobile.AjaxLoader={_renderItems:function(i,r,u){var i=n(i),e,f,o;this._control=this._rootCSS.split("e-m-")[1];i.attr("id",this.elementId+"-"+this._control+"-item"+u).addClass("e-m-"+this._control+"-item");e=!t.isNullOrUndefined(this._items[u].enableAjax)&&this._items[u].enableAjax?this._items[u].enableAjax:this.model.enableAjax;this._storedContent[u]||e||(this._items[u].href?(f=t.getCurrentPage().find(this._items[u].href),this._storedContent[u]=f[0]&&f[0].nodeName&&f[0].nodeName.toLowerCase()=="script"?t.getClearString(f[0].innerHTML):f):this._storedContent[u]=this._items[u].template?this._items[u].template:i.html());e||(o=t.buildTag("div.e-m-"+this._control+"-content e-m-"+this._control+"-static-content#"+this.elementId+"-"+this._control+"-item"+u+"-content",this._storedContent[u]).appendTo(r));e&&this.model.showAjaxPopup&&t.mobile.WaitingPopup.show();e&&this.model.prefetchAjaxContent&&this._loadAjaxContent(i,r,u)},_loadAjaxContent:function(i,r,u){var o=n(i),f=this,s=this,e=this._items[u].href,h,c;(!n.support.pushstate||t.isWindowsWebView())&&(e=this._makeUrlAbsolute(e));h={item:i,index:u,text:o.text(),url:this.model.ajaxSettings.url?this.model.ajaxSettings.url:e};this.model.ajaxBeforeLoad&&this._trigger("ajaxBeforeLoad",h);c={cache:this.model.ajaxSettings.cache,async:this.model.ajaxSettings.async,type:this.model.ajaxSettings.type,contentType:this.model.ajaxSettings.contentType,url:t.isWindowsWebView()?e:this.model.ajaxSettings.url?this.model.ajaxSettings.url:e,dataType:this.model.ajaxSettings.dataType,data:this.model.ajaxSettings.data,successHandler:function(h){var l={item:i,index:u,text:o.text(),url:f.model.ajaxSettings.url?f.model.ajaxSettings.url:e},a=t.getCurrentPage().find("#"+f.elementId+"-"+s._control+"-item"+u+"-content"),c;f.model.enableCache&&a.length!=0||(c=t.buildTag("div.e-m-"+s._control+"-content e-m-"+s._control+"-ajax-content#"+f.elementId+"-"+s._control+"-item"+u+"-content",/<\/?body[^>]*>/gmi.test(h)?h.split(/<\/?body[^>]*>/gmi)[1]:h||"").appendTo(r),f._content=this._control=="acc"?c.children():c,(!t.isAppNullOrUndefined()&&App.angularAppName||t.angular.defaultAppName)&&t.angular.compile(n(c)));t.widget.init(f._content);f.model.ajaxSuccess&&f._trigger("ajaxSuccess",l);f._ajaxSuccessHandler&&f._ajaxSuccessHandler(l)},errorHandler:function(n,t,r){var u={xhr:n,textStatus:t,errorThrown:r,item:i,index:o.index(),text:o.text(),url:f.model.ajaxSettings.url?f.model.ajaxSettings.url:e};f.model.ajaxError&&f._trigger("ajaxError",u)},completeHandler:function(){f.model.showAjaxPopup&&t.mobile.WaitingPopup.hide();var n={content:f._content,item:i,index:o.index(),text:o.text(),url:f.model.ajaxSettings.url?f.model.ajaxSettings.url:e};f.model.ajaxComplete&&f._trigger("ajaxComplete",n)}};t.sendAjaxRequest(c)},_showHideItems:function(i,r,u){var f=t.getCurrentPage(),e=t.isNullOrUndefined(this._items[u].enableAjax)?this.model.enableAjax:this._items[u].enableAjax,o=f.find("#"+i.id+"-content").length;e&&o==0&&!this.model.prefetchAjaxContent&&this._loadAjaxContent(i,r,u);n(this["_"+this._control+"Items"]).removeClass("e-m-state-active");n(i).addClass("e-m-state-active");f.find(".e-m-"+this._control+"-content").addClass("e-m-"+this._control+"-state-hide");f.find("#"+i.id+"-content").removeClass("e-m-"+this._control+"-state-hide");this.model.prefetchAjaxContent||this.model.enableCache||f.find(".e-m-"+this._control+"-ajax-content.e-m-"+this._control+"-state-hide").remove();this.model.select&&this._trigger("select",{index:u,item:n(i),text:n(i).text(),isInteraction:this._isInteraction?!0:!1})},ajaxReload:function(){var n=this.element.find(".e-m-"+this._control+"-item.e-m-state-active"),t=this.element.find(".e-m-"+this._control+"-content:not(.e-m-"+this._control+"-state-hide)"),i=t.parent(),r=n[0].id.split("item")[1];t.remove();this._loadAjaxContent(n,i,r)},_makeUrlAbsolute:function(i){var r=n("<a href ='"+i+"'><\/a>")[0],u;n("body").append(r);u=r.pathname.indexOf("/")!=0?"/"+r.pathname:r.pathname;var f=t.browserInfo(),e=f.name=="msie"&&f.version=="9.0",o=r.protocol+"//"+(e&&r.port=="80"?r.host.replace(":80",""):r.host)+u+r.search+r.hash;return n(r).remove(),o}}})(jQuery,Syncfusion),function($,ej,undefined){ej.widget("ejmAccordion","ej.mobile.Accordion",{_setFirst:!0,_requiresID:!0,_rootCSS:"e-m-acc",validTags:["ul"],_tags:[{tag:"items",attr:["text","href","enableAjax"],content:"template"}],defaults:{renderMode:"auto",cssClass:"",selectedItems:[0],disabledItems:[],enableMultipleOpen:!1,expandAll:!1,collapseAll:!1,heightAdjustMode:"content",enableRippleEffect:ej.isAndroid()?!0:!1,showHeaderIcon:!0,headerIcon:{normal:"e-m-icon-down",active:"e-m-icon-up"},spinnerText:null,enablePersistence:!1,enableAjax:!1,enableCache:!1,prefetchAjaxContent:!1,showAjaxPopup:!1,items:[],itemTouchStart:null,itemTouchEnd:null,select:null,expand:null,collapse:null,ajaxBeforeLoad:null,ajaxSuccess:null,ajaxError:null,ajaxComplete:null,ajaxSettings:{type:"GET",cache:!1,async:!0,dataType:"html",contentType:"html",url:"",data:{}},locale:"en-US"},dataTypes:{renderMode:"enum",cssClass:"string",selectedItems:"array",disabledItems:"array",enableMultipleOpen:"boolean",expandAll:"boolean",enableRippleEffect:"boolean",collapseAll:"boolean",heightAdjustMode:"enum",showHeaderIcon:"boolean",headerIcon:{normal:"string",active:"string"},spinnerText:"string",enablePersistence:"boolean",enableAjax:"boolean",enableCache:"boolean",prefetchAjaxContent:"boolean",showAjaxPopup:"boolean",locale:"string"},observableArray:["selectedItems"],selectedItems:ej.util.valueFunction("selectedItems"),_init:function(){this._getLocalizedLabels();this.model.spinnerText=ej.isNullOrUndefined(this.model.spinnerText)?this._localizedLabels.spinnerText:this.model.spinnerText;this._renderControl();this._wireEvents(!1)},_renderControl:function(){var i,li,itemLength,content;if(this._orgEle=$(this.element).clone(),ej.setRenderMode(this),this.element.addClass("e-m-user-select e-m-clearall e-m-"+this.model.renderMode+" "+this.model.cssClass),this.elementId=this.element[0].id,this._headerIcon=jQuery.extend({},this.model.headerIcon),this.model.prefetchAjaxContent=this.model.expandAll&&this.model.enableAjax?!0:this.model.prefetchAjaxContent,this.model.enableMultipleOpen=this.model.expandAll?!0:this.model.enableMultipleOpen,typeof this.model.items=="string"&&(this.model.items=eval(this.model.items)),this._storedContent=[],this._accItems=[],this._items=[],this._isAngular=this.model.items.length>0?!0:!1,this._isAngular)this._items=this.model.items;else for(this._accItems=this.element.find("li"),i=0,itemLength=this._accItems.length;i<itemLength;i++)this._items.push(this._insertItem($(this._accItems[i])));for(li=0,itemLength=this._items.length;li<itemLength;li++)this._isAngular&&this._accItems.push(ej.buildTag("li").appendTo(this.element)[0]),content=ej.buildTag("li#"+this.elementId+"-acc-item"+li+"-content-wrapper.e-m-acc-content-wrapper e-m-acc-state-hide"),$(this._accItems[li]).after(content),this._renderItems(this._accItems[li],content,li),$(this._accItems[li]).text(this._items[li].text?this._items[li].text:$(this._accItems[li]).attr("data-ej-text")).addClass((li==0?"e-m-acc-item-first ":li==itemLength-1?"e-m-acc-item-last ":"")+(this.model.showHeaderIcon?this.model.headerIcon.normal:""));this._setEnableRippleEffect();this._postRendering()},_setEnableRippleEffect:function(){$(this._accItems)[this.model.enableRippleEffect?"addClass":"removeClass"]("e-ripple")},_insertItem:function(n){var t={};return t.text=ej.getAttrVal(n,"data-ej-text")==undefined?$(n).html():ej.getAttrVal(n,"data-ej-text"),t.href=ej.getAttrVal(n,"data-ej-href",undefined),t.enableAjax=ej.getBooleanVal(n,"data-ej-enableAjax",undefined),t.template=$(n).html(),t},_postRendering:function(){this._setHeightAdjustMode();this.model.expandAll?this._setExpandAll(!0):this.model.collapseAll?this._setCollapseAll(!0):this._loadItem(this.selectedItems());this.model.disabledItems.length&&this._enableDisable(this.model.disabledItems,!0)},_ajaxSuccessHandler:function(n){if($(n.item).text()==this.model.spinnerText&&($(n.item).text(this._items[n.index].text).next(".e-m-acc-content-wrapper").slideDown(300,"linear"),this.model.expand&&!this.model.enableMultipleOpen)){var t=this,i={item:n.item,index:n.index};setTimeout(function(){t._trigger("expand",i)},300)}},_loadItem:function(n,t){var e=this,i,s,h,r,f,o;if(this.model.enableMultipleOpen){for(i=0,s=n.length;i<s;i++){var o={item:u,index:n[i]},u=$(this._accItems[n[i]]),r=this.element.find("#"+this._accItems[n[i]].id+"-content-wrapper"),f=r.find(".e-m-acc-content").length>0;u.hasClass("e-m-state-active")?t?u.removeClass("e-m-state-active "+this.model.headerIcon.active).addClass(this.model.headerIcon.normal):"":u.addClass("e-m-state-active "+(this.model.showHeaderIcon?this.model.headerIcon.active:"")).removeClass(this.model.headerIcon.normal);u.hasClass("e-m-state-active")?t?this.selectedItems().push(parseInt(n[i])):"":this.selectedItems().splice(n[i],1);!(ej.isNullOrUndefined(this._items[n[i]].enableAjax)?this.model.enableAjax:this._items[n[i]].enableAjax)||this.model.prefetchAjaxContent||f||(u.text(this.model.spinnerText),this._loadAjaxContent(u,r,n[i]));r.hasClass("e-m-acc-state-hide")?((!this.model.enableAjax||this.model.prefetchAjaxContent||f)&&$(r).slideDown(300,"linear"),this.model.expand&&setTimeout(function(){e._trigger("expand",o)},300),r.removeClass("e-m-acc-state-hide")):t&&(r.slideUp(300,"linear").addClass("e-m-acc-state-hide"),this.model.collapse&&setTimeout(function(){e._trigger("collapse",o)},300))}this.model.enableCache||this.model.prefetchAjaxContent||this.element.find(".e-m-acc-content-wrapper.e-m-acc-state-hide > .e-m-acc-ajax-content").remove()}else this.selectedItems(n),t&&!this.model.collapseAll&&(h={item:this.element.find(".e-m-acc-item.e-m-state-active"),index:this.element.find(".e-m-acc-item.e-m-state-active")[0].id.split("item")[1]}),this.element.find(".e-m-acc-item.e-m-state-active").removeClass(this.model.headerIcon.active).addClass(this.model.showHeaderIcon?this.model.headerIcon.normal:""),r=this.element.find("#"+this._accItems[n[0]].id+"-content-wrapper"),f=r.find(".e-m-acc-content").length>0,!(ej.isNullOrUndefined(this._items[n[0]].enableAjax)?this.model.enableAjax:this._items[n[0]].enableAjax)||this.model.prefetchAjaxContent||f||$(this._accItems[n[0]]).text(this.model.spinnerText),(!this.model.enableAjax||this.model.prefetchAjaxContent||f)&&($(r).slideDown(300,"linear"),o={item:this._accItems[n[0]],index:n[0]},this.model.expand&&setTimeout(function(){e._trigger("expand",o)},300)),r.removeClass("e-m-acc-state-hide"),this._showHideItems(this._accItems[n[0]],r,n[0]),this.model.collapse&&t&&setTimeout(function(){e._trigger("collapse",h)},300);this.element.find(".e-m-acc-item.e-m-state-active").removeClass(this.model.headerIcon.normal).addClass(this.model.showHeaderIcon?this.model.headerIcon.active:"");this.element.find(".e-m-acc-item:not(.e-m-state-active)").next(".e-m-acc-content-wrapper").slideUp(t?300:0,"linear").addClass("e-m-acc-state-hide")},_getLocalizedLabels:function(){this._localizedLabels=ej.getLocalizedConstants(this.sfType,this.model.locale)},_setLocale:function(){this._getLocalizedLabels();this.model.spinnerText=this._localizedLabels.spinnerText},_createDelegates:function(){this._itemTouchStartHndlr=$.proxy(this._itemTouchStart,this);this._itemTouchEndHndlr=$.proxy(this._itemTouchEnd,this);this._touchMoveHndlr=$.proxy(this._onTouchMove,this);this._resizeHndlr=$.proxy(this._resize,this)},_wireEvents:function(n){this._createDelegates();ej.listenEvents([this._accItems],[ej.startEvent()],[this._itemTouchStartHndlr],n);ej.listenTouchEvent($(window),"onorientationchange"in window?"orientationchange":"resize",this._resizeHndlr,n)},_itemTouchStart:function(n){var i=n.touches?n.touches[0]?n.touches[0]:n.changedTouches?n.changedTouches[0]:n:n,t;this._clickX=i.clientX;this._isMouseMove=!1;t={item:n.target,index:n.target.id.split("item")[1]};this._target=n.target;ej.listenEvents([this._accItems,this._accItems],[ej.endEvent(),ej.moveEvent()],[this._itemTouchEndHndlr,this._touchMoveHndlr],!1);this.model.itemTouchStart&&this._trigger("itemTouchStart",t)},_onTouchMove:function(n){var t=n.touches?n.touches[0]?n.touches[0]:n.changedTouches?n.changedTouches[0]:n:n;this._clickX!=t.clientX&&(this._isMouseMove=!0)},_itemTouchEnd:function(n){if(ej.listenEvents([this._accItems,this._accItems],[ej.moveEvent(),ej.endEvent()],[this._touchMoveHndlr,this._itemTouchEndHndlr],!0),n.target===this._target&&!this._isMouseMove){var t={item:n.target,index:n.target.id.split("item")[1]};!this.model.enableMultipleOpen&&$(n.target).hasClass("e-m-state-active")||$(n.target).hasClass("e-m-state-disabled")||this._loadItem([parseInt(n.target.id.split("item")[1])],!0);this.element.find(".e-m-state-active").length==this._items.length?this.model.expandAll=!0:this.model.collapseAll=this.element.find(".e-m-state-active").length==0?!0:!1;this.model.expandAll=!1;this.model.itemTouchEnd&&this._trigger("itemTouchEnd",t)}},_resize:function(){this._setHeightAdjustMode()},_setModel:function(n){var r=!1,t,i;for(t in n)i="_set"+t.charAt(0).toUpperCase()+t.slice(1),this[i]?this[i](n[t]):r=!0;r&&this._refresh()},_refresh:function(){this._destroy();this.element.addClass("e-m-acc");this._renderControl();this._wireEvents()},_destroy:function(){this._wireEvents(!0);this._clearElement()},_clearElement:function(){this.element.removeAttr("class");this.element.html(this._orgEle.html())},_setExpandAll:function(n){n&&(this.model.enableAjax&&!this.model.prefetchAjaxContent?this._refresh():this._addRemoveClass(!0),this.model.collapseAll&&(this.model.collapseAll=!1))},_setCollapseAll:function(n){n&&(this._addRemoveClass(!1),this.model.expandAll&&(this.model.expandAll=!1))},_addRemoveClass:function(n,t){$(t>=0?this._accItems[t]:this._accItems)[(n?"add":"remove")+"Class"]("e-m-state-active "+this.model.headerIcon.active)[(n?"remove":"add")+"Class"](this.model.headerIcon.normal);this.element.find(t>=0?"#"+this._accItems[t].id+"-content-wrapper":".e-m-acc-content-wrapper")["slide"+(n?"Down":"Up")](0);this.element.find(t>=0?"#"+this._accItems[t].id+"-content-wrapper":".e-m-acc-content-wrapper")[(n?"remove":"add")+"Class"]("e-m-acc-state-hide")},_setSelectedItems:function(n){n.length&&(this.model.enableMultipleOpen?this._selectDeselect(this.selectedItems(),!0):($(this._accItems[n[0]]).hasClass("e-m-state-active")||$(this._accItems[n[0]]).hasClass("e-m-state-disabled"))?"":this._loadItem(n))},_setSpinnerText:function(){},_selectDeselect:function(n,t){for(var i=0,r=n.length;i<r;i++)this.element.find("#"+this._accItems[n[i]].id+"-content").length==0&&this._loadAjaxContent(this._accItems[n[i]],this.element.find("#"+this._accItems[n[i]].id+"-content-wrapper"),n[i]),this._addRemoveClass(t,n[i])},_setDisabledItems:function(){this._enableDisable(this.model.disabledItems,!0)},_enableDisable:function(n,t){addremove=t?"add":"remove";for(var i=0,r=n.length;i<r;i++)$(this._accItems[n[i]])[addremove+"Class"]("e-m-state-disabled"),this.element.find("#"+this._accItems[n[i]].id+"-content-wrapper")[[addremove+"Class"]]("e-m-state-disabled")},_setHeightAdjustMode:function(){var n=this.model.heightAdjustMode=="fill"?this._adjustFillHeight():this.model.heightAdjustMode=="fixed"?this._adjustFixedHeight():"auto";$(this.element.find(".e-m-acc-content-wrapper")).height(n)},_adjustFixedHeight:function(){if(!this.model.enableAjax)return maxHeight=Math.max.apply(null,$(".e-m-acc-content-wrapper").map(function(){return $(this).height()}).get())},_adjustFillHeight:function(){return ej.getDimension(this.element.parent(),"height")-ej.getDimension(this._accItems[0],"outerHeight")*this._accItems.length},_setHeaderIcon:function(){this.element.find(".e-m-acc-item.e-m-state-active").removeClass(this._headerIcon.active).addClass(this.model.headerIcon.active);this.element.find(".e-m-acc-item:not(.e-m-state-active)").removeClass(this._headerIcon.normal).addClass(this.model.headerIcon.normal);this._headerIcon=jQuery.extend({},this.model.headerIcon)},selectItems:function(n){this.model.enableMultipleOpen?this._selectDeselect(n,!0):this._loadItem(n)},deselectItems:function(n){this.model.enableMultipleOpen&&this._selectDeselect(n,!1)},enableItems:function(n){this._enableDisable(n,!1)},disableItems:function(n){this._enableDisable(n,!0)},addItem:function(n,t){this.model.items=this._items;t>=0?this.model.items.splice(t,0,this._insertItem($(n))):this.model.items.push(this._insertItem($(n)));this.element.empty();this._renderControl();this._wireEvents();this._items=this.model.items},removeItem:function(n){this.model.items=this._items;n>=0&&(this.model.items.splice(n,1),this.element.empty(),this._renderControl(),this._wireEvents());this._items=this.model.items},getItemsCount:function(){return this._items.length}});$.extend(!0,ej.mobile.Accordion.prototype,ej.mobile.AjaxLoader);ej.mobile.Accordion.HeightAdjustMode={Fixed:"fixed",Content:"content",Fill:"fill"};ej.mobile.Accordion.Locale=ej.mobile.Accordion.Locale||{};ej.mobile.Accordion.Locale["default"]=ej.mobile.Accordion.Locale["en-US"]={spinnerText:"Loading..."}}(jQuery,Syncfusion)});
