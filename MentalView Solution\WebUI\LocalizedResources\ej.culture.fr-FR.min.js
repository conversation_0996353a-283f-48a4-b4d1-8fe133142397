/*!
*  filename: ej.culture.fr-FR.min.js
*  version : 18.1.0.42
*  Copyright Syncfusion Inc. 2001 - 2020. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/

ej.addCulture("fr-FR", {
    name: "fr-FR",
    englishName: "French (France)",
    nativeName: "français (France)",
    language: "fr",
    numberFormat: {
        pattern: ["-n"], ",": " ", ".": ",", groupSizes: [3], NaN: "Non numérique", negativeInfinity: "-Infini",
        positiveInfinity: "+Infini",
        percent: { pattern: ["-n%", "n%"], groupSizes: [3], ",": " ", ".": ",", symbol: "%" },
        currency: { pattern: ["-n $", "n $"], groupSizes: [3], ",": " ", ".": ",", symbol: "€" }
    },
    calendars: {
        standard: {
            "/": "/", ":": ":",
            firstDay: 1,
            days: {
                names: ["dimanche", "lundi", "mardi", "mercredi", "jeudi", "vendredi", "samedi"],
                namesAbbr: ["dim.", "lun.", "mar.", "mer.", "jeu.", "ven.", "sam."], namesShort: ["di", "lu", "ma", "me", "je", "ve", "sa"]
            }, months: { names: ["janvier", "février", "mars", "avril", "mai", "juin", "juillet", "août", "septembre", "octobre", "novembre", "décembre", ""], namesAbbr: ["janv.", "févr.", "mars", "avr.", "mai", "juin", "juil.", "août", "sept.", "oct.", "nov.", "déc.", ""] }, AM: null, PM: null, eras: [{ name: "ap. J.-C.", start: null, offset: 0 }], patterns: { d: "dd/MM/yyyy", D: "dddd d MMMM yyyy", t: "HH:mm", T: "HH:mm:ss", f: "dddd d MMMM yyyy HH:mm", F: "dddd d MMMM yyyy HH:mm:ss", M: "d MMMM" }
        }
    }
});;