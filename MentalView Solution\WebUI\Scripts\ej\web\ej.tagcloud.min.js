/*!
*  filename: ej.tagcloud.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min"],n):n()})(function(){(function(n,t){t.widget("ejTagCloud","ej.TagCloud",{element:null,model:null,validTags:["div","span"],_rootCSS:"e-tagcloud",_setFirst:!1,defaults:{cssClass:"",htmlAttributes:{},dataSource:null,query:null,fields:{text:"text",url:"url",frequency:"frequency",htmlAttributes:"htmlAttributes"},showTitle:!0,titleText:"Title",titleImage:null,format:"cloud",enableRTL:!1,minFontSize:"10px",maxFontSize:"40px",mouseover:null,mouseout:null,click:null,create:null,destroy:null},dataTypes:{cssClass:"string",showTitle:"boolean",titleText:"string",titleImage:"string",format:"enum",enableRTL:"boolean",dataSource:"data",query:"data",fields:"data",htmlAttributes:"data"},_init:function(){this._initialize();this._render()},_setModel:function(n){for(var t in n)switch(t){case"fields":case"query":case"dataSource":case"minFontSize":case"maxFontSize":this._refreshTagItems(t,n[t]);break;case"showTitle":this._showTitle(n[t]);break;case"titleText":this._title(n[t]);break;case"titleImage":this._titleImage(n[t]);break;case"cssClass":this._changeSkin(n[t]);break;case"format":this._format(n[t]);break;case"enableRTL":this._rtl(n[t]);break;case"htmlAttributes":this._addAttr(n[t])}},_refreshTagItems:function(n,t){this.model[n]=t;this.ul.empty();this._checkDataBinding()},_showTitle:function(n){n?(this._generateTitle(),this.ul.removeClass("e-notitle")):(this.titleText.remove(),this.ul.addClass("e-notitle"),this.titleText=null)},_title:function(n){this.titleText&&(n?this.text?this.text.html(n):this._generateTextTag(n):this.text&&(this.text.remove(),this.text=null))},_titleImage:function(n){this.titleText&&(n?this.image?this.image.attr("src",n):this._generateImageTag(n):this.image&&(this.image.remove(),this.image=null))},_changeSkin:function(n){this.model.cssClass!=n&&this.element.removeClass(this.model.cssClass).addClass(n)},insert:function(t){n.trim(t.text)&&this.ul.append(this._generateLi(t,this._getMapper()))},insertAt:function(t,i){n.trim(t.text)&&n(this.ul.children()[i-1]).before(this._generateLi(t,this._getMapper()))},remove:function(t){for(var u=this.ul.children(),r,i=0;i<u.length;i++)r=n(u[i]),r.children()[0].innerHTML==t&&r.remove()},removeAt:function(t){var i=this.ul.children();n(i[t-1]).remove()},_format:function(n){n=="cloud"?(this.ul.removeClass("e-list"),this.ul.addClass("e-cloud")):n=="list"&&(this.ul.removeClass("e-cloud"),this.ul.addClass("e-list"))},_destroy:function(){this.element.removeClass("e-widget "+this.model.cssClass);this.element.empty()},_initialize:function(){this.minFreq=0;this.maxFreq=30;this.ul=null;this.titleText=null;this.image=null;this.text=null},_render:function(){this.element.addClass("e-widget "+this.model.cssClass);this.model.showTitle&&this._generateTitle();this._renderWrapper();this._checkDataBinding();this._addAttr(this.model.htmlAttributes);this._checkProperties()},_generateTitle:function(){this.titleText=t.buildTag("div.e-header e-title");this.model.titleImage&&this._generateImageTag(this.model.titleImage);this.model.titleText&&this._generateTextTag(this.model.titleText);this.ul?this.titleText.insertBefore(this.ul):this.element.append(this.titleText)},_generateImageTag:function(t){this.image||(this.image=n('<img class="e-title-img" src="'+t+'" />'));this.text&&!this.model.titleImage?this.image.insertBefore(this.text):this.titleText.append(this.image)},_generateTextTag:function(n){this.text||(this.text=t.buildTag("span",n));this.titleText.append(this.text)},_renderWrapper:function(){var n;n=this.model.format=="list"?"list":"cloud";this.ul=t.buildTag("ul.e-ul e-box e-"+n);this.element.append(this.ul);this.model.showTitle||this.ul.addClass("e-notitle")},_renderItems:function(n){this._generateTagItems(n);this.ul.removeClass("e-load")},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.element.addClass(n):i.element.attr(t,n)})},_checkProperties:function(){this.model.enableRTL&&this._rtl(this.model.enableRTL)},_rtl:function(n){n?this.element.addClass("e-rtl"):this.element.removeClass("e-rtl")},_checkDataBinding:function(){var n=this.model.dataSource;n!=null&&(this.ul.addClass("e-load"),t.DataManager&&n instanceof t.DataManager?this._initDataSource(n):this._renderItems(n))},_initDataSource:function(n){var t=this,i=n.executeQuery(this._getQuery());i.done(function(n){t._renderItems(n.result)}).fail(function(){t.ul.removeClass("e-load")})},_setAttributes:function(n,t){if(n)for(var i in n)i=="class"?t.addClass(n[i]):t.attr(i,(i=="style"?t.attr("style")+";":"")+n[i])},_getQuery:function(){var r;if(t.isNullOrUndefined(this.model.query)){var u=[],i=t.Query(),n=this.model.fields;for(r in n)r!=="tableName"&&n[r]&&u.push(n[r]);u.length>0&&i.select(u);this.model.dataSource.dataSource.url.match(n.tableName+"$")||t.isNullOrUndefined(n.tableName)||i.from(n.tableName)}else i=this.model.query;return i},_generateTagItems:function(t){var i,r=this._getMapper(),u=function(n){return n[r._freq]},f=t.map?t.map(u):n.map(t,u);for(this.minFreq=Math.min.apply(Math,f),this.maxFreq=Math.max.apply(Math,f),i=0;i<t.length;i++)this.ul.append(this._generateLi(t[i],r))},_getMapper:function(){var n=this.model.fields,t={_text:null,_freq:null,_url:null};return t._text=n&&n.text?n.text:"text",t._freq=n&&n.frequency?n.frequency:"frequency",t._url=n&&n.url?n.url:"url",t._attr=n&&n.htmlAttributes?n.htmlAttributes:"htmlAttributes",t},_generateLi:function(n,i){var u=t.buildTag("li.e-tagitems"),r;return r=t.buildTag("a.e-txt",n[i._text]||n[i._url],{"font-size":this._calculateFontSize(n[i._freq])},{role:"link"}),n[i._url]&&r.attr({href:n[i._url],target:"blank"}),this._setAttributes(n[i._attr],r),u.append(r),this._on(r,"mouseenter",this._mouseEnter),this._on(r,"mouseleave",this._mouseLeave),this._on(r,"click",this._mouseClick),u},_mouseEnter:function(t){n(t.target).addClass("hover");this._raiseEvent(t,"mouseover")},_mouseLeave:function(t){n(t.target).removeClass("hover");this._raiseEvent(t,"mouseout")},_mouseClick:function(t){n(t.target).removeClass("hover");this._raiseEvent(t,"click")},_raiseEvent:function(t,i){this._trigger(i,{value:n(t.target).html(),url:n(t.target).attr("href"),eventType:i,target:n(t.target)})},_calculateFontSize:function(n){if(n){var t=2,i,r,u=parseInt(this.model.minFontSize,10),f=parseInt(this.model.maxFontSize,10);return i=(n-this.minFreq)/(this.maxFreq-this.minFreq),r=f-u,u+t*Math.floor(i*(r/t))}return this.model.minFontSize}});t.Format={Cloud:"cloud",List:"list"}})(jQuery,Syncfusion)});
