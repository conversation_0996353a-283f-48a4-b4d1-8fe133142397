﻿using C1.C1Report;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebUI.Reporting
{
    public partial class ExportC1Report : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            C1Report report = Session["Report"] as C1Report;
            bool inline = Convert.ToBoolean(Request.QueryString["Inline"]);

            if (report != null)
            {
                Export(report, report.ReportName + " " + DateTime.Now.ToString(), inline);
            }
        }

        public void Export(C1Report report, string fileName, bool inline)
        {
            try
            {
                MemoryStream stream = new MemoryStream();
                string fileType = "pdf";
                //string filePath = Server.MapPath("~") + @"\Temp\" + fileName;
                Response.Clear();

                //if (fileType == "xls")
                //    report.(stream);
                if (fileType == "pdf")
                    report.RenderToStream(stream, C1.C1Report.FileFormatEnum.PDF);
                //if (fileType == "rtf")
                //    report.ExportToRtf(stream);
                //if (fileType == "csv")
                //    report.ExportToCsv(stream);

                Response.ClearHeaders();
                Response.ContentType = "application/" + fileType;
                Response.AddHeader("Accept-Header", stream.Length.ToString());
                Response.AddHeader("Content-Disposition", (inline ? "Inline" : "Attachment") + "; filename=" + fileName + "." + fileType);
                Response.AddHeader("Content-Length", stream.Length.ToString());
                //Response.ContentEncoding = System.Text.Encoding.Default;

                Response.BinaryWrite(stream.ToArray());
                Response.Flush();
                Response.End();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }
    }
}