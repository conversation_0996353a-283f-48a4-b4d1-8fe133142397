@page "/test-auth"
@using Admin.Services
@inject IAuthenticationService AuthService

<h3>Authentication Test</h3>

<div class="card">
    <div class="card-body">
        <h5>Test Authentication Service</h5>
        
        <div class="mb-3">
            <label>Username:</label>
            <input @bind="testUsername" class="form-control" />
        </div>
        
        <div class="mb-3">
            <label>Password:</label>
            <input @bind="testPassword" type="password" class="form-control" />
        </div>
        
        <button @onclick="TestLogin" class="btn btn-primary">Test Login</button>
        
        <div class="mt-3">
            <strong>Result:</strong> @testResult
        </div>
        
        <div class="mt-3">
            <h6>Valid Credentials:</h6>
            <ul>
                <li>admin / admin123</li>
                <li>superadmin / super123</li>
            </ul>
        </div>
    </div>
</div>

@code {
    private string testUsername = "";
    private string testPassword = "";
    private string testResult = "";

    private async Task TestLogin()
    {
        try
        {
            testResult = "Testing...";
            StateHasChanged();
            
            var result = await AuthService.LoginAsync(testUsername, testPassword);
            testResult = $"Login result: {result}";
            
            if (result)
            {
                var user = await AuthService.GetUserAsync();
                testResult += $" | User: {user.Identity?.Name} | Authenticated: {user.Identity?.IsAuthenticated}";
            }
        }
        catch (Exception ex)
        {
            testResult = $"Error: {ex.Message}";
        }
    }
}
