﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Admin.Data.Model;

public partial class User
{
    [Key]
    public long UserId { get; set; }

    public long RoleId { get; set; }

    public long TenantId { get; set; }

    [Required]
    [StringLength(20)]
    public string Username { get; set; }

    [Required]
    [StringLength(20)]
    public string Password { get; set; }

    [Required]
    [StringLength(30)]
    public string FullName { get; set; }

    [Required]
    [StringLength(50)]
    public string Email { get; set; }

    public bool IsDoctor { get; set; }

    [Required]
    [StringLength(50)]
    public string JobTitle { get; set; }

    [Required]
    [StringLength(50)]
    public string AcademicTitle { get; set; }

    [Required]
    [StringLength(50)]
    public string Specialisation { get; set; }

    [Required]
    [StringLength(20)]
    public string AppointmentsListSort { get; set; }

    [Required]
    [StringLength(20)]
    public string AppointmentsScheduleView { get; set; }

    [Required]
    public string AppointmentsScheduleVisibleResources { get; set; }

    [InverseProperty("User")]
    public virtual ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();
}