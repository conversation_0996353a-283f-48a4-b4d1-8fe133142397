/*!
*  filename: ej.mobile.radialmenu.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function(n,t){t.widget("ejRadialMenuBase","ej.RadialMenuBase",{_tags:[{tag:"items",attr:["imageName","imagePath","imageUrl","prependTo","windows.text","flat.text","text","click","touch","enabled","badge.enabled","badge.value","type","sliderSettings.ticks","sliderSettings.strokeWidth","sliderSettings.labelSpace",[{tag:"items",attr:["imageName","imagePath","imageUrl","windows.text","flat.text","text","prependTo","click","touch","enabled"]}]]}],defaults:{radius:150,cssClass:"",enableAnimation:!0,open:null,close:null,items:[]},dataTypes:{radius:"number",enableAnimation:"boolean",items:"array"},_initialization:function(){var t,i;if(this.element.addClass(this.model.cssClass),this._svgLink="http://www.w3.org/2000/svg",this.model.items.length<1)for(this._radialItems=n(this.element.find(">ul").children()),t=0;t<this._radialItems.length;t++)if(this.model.items.push(this._itemsObjectCollection(n(this._radialItems[t]))),n(this._radialItems[t]).find("ul").children().length>0)for(this._nestedRadialItems=n(this._radialItems[t]).find("ul").children(),this.model.items[t].items=[],i=0;i<this._nestedRadialItems.length;i++)this.model.items[t].items.push(this._itemsObjectCollection(n(this._nestedRadialItems[i])));this._itemCount=this.model.items.length;n(this.element.children()).remove();this._startXY=this.model.radius;this._diameter=2*this.model.radius},_itemsObjectCollection:function(t){var i={},r={};return n(this._tags[0].attr).each(function(n,u){var f,e,o;typeof u=="string"&&(f=u.split("."),f.length==1?i[u]=t.attr("data-ej-"+u):(e=f[0],o=f[1],i[e]||(r={}),r[o]=t.attr("data-ej-"+u.replace(".","-")),i[e]=r))}),i},_createSVGElement:function(){var t=document.createElementNS(this._svgLink,"svg");return this.model.radius!=null&&(t.setAttribute("width",this._diameter),t.setAttribute("height",this._diameter)),n(t)},_createGroupElement:function(t){var i=document.createElementNS(this._svgLink,"g");return t&&i.setAttribute("id",t),n(i)},_createImageElement:function(i,r,u,f,e){var s,o;if(t.isNullOrUndefined(u.imageUrl)&&t.isNullOrUndefined(u.imagePath)){if(!t.isNullOrUndefined(u.prependTo))return n(n.find(u.prependTo)[0].children).clone()}else return s=u.imageUrl?u.imageUrl:this.model.renderMode.toLowerCase()!="flat"?u.imagePath+"/"+this.model.renderMode.toLowerCase()+"/"+u.imageName:u.imagePath+"/"+this.model.renderMode.toLowerCase()+"/"+u.imageName,o=document.createElementNS(this._svgLink,"image"),o.setAttribute("width",i),o.setAttribute("height",r),o.setAttributeNS("http://www.w3.org/1999/xlink","href",s),o.setAttribute("x",f),o.setAttribute("y",e),o},_createTextElement:function(n,t,i,r){var u=document.createElementNS(this._svgLink,"text");return u.setAttribute("x",n),u.setAttribute("y",t),u.setAttribute("class",i),u.textContent=r,u},_createPathElement:function(n,t,i){var r=document.createElementNS(this._svgLink,"path");return r.setAttribute("data-ej-index",n),r.setAttribute("d",t),r.setAttribute("class",i),r},_createPolygonElement:function(n,t){var i=document.createElementNS(this._svgLink,"polygon");return i.setAttribute("points","10,10 0,10 5,5"),i.setAttribute("data-ej-index",n),i.setAttribute("fill","#FFFFFF"),i.setAttribute("transform",t),i},_createCircleElement:function(n,t,i,r){var u=document.createElementNS(this._svgLink,"circle");return u.setAttribute("cx",n),u.setAttribute("cy",t),u.setAttribute("r",i),u.setAttribute("class",r),u},_calculateRadialArc:function(n,t,i,r,u){for(var a,l,h=0,e=2*Math.PI*h,o=2*Math.PI*(h+1/n),s=.5*t*Math.min(this._diameter,this._diameter),v=i*t*Math.min(this._diameter,this._diameter),c=r*s,y=u*s,p=360/n,w=p/2,b=[],k=[],d=[],g=[],nt=[],tt=[],it=[],rt=[],ut=[],ft=[],et=[],ot=[],st=[],ht=[],f=0;f<n;f++)a=-.5*Math.PI,e=e+a,o=o+a-1e-6,ht[f]=o-e<Math.PI?0:1,l=(e+o)/2,b[f]=this._startXY+s*Math.cos(e),k[f]=this._startXY+s*Math.sin(e),d[f]=this._startXY+s*Math.cos(o),g[f]=this._startXY+s*Math.sin(o),nt[f]=this._startXY+v*Math.cos(l),tt[f]=this._startXY+v*Math.sin(l),it[f]=this._startXY+c*Math.cos(e),rt[f]=this._startXY+c*Math.sin(e),ut[f]=this._startXY+c*Math.cos(o),ft[f]=this._startXY+c*Math.sin(o),et[f]=this._startXY+y*Math.cos(l),ot[f]=this._startXY+y*Math.sin(l),st[f]=w,h+=1/n,e=2*Math.PI*h,o=2*Math.PI*(h+1/n),w+=p;return{x1:b,y1:k,x2:d,y2:g,midx:nt,midy:tt,dStartX:it,dStartY:rt,dEndX:ut,dEndY:ft,dmidx:et,dmidy:ot,deg:st,longArc:ht,radius:s,dradius:c}},_renderRadialMenu:function(){var n,l,a;this._radialSVG=this._createSVGElement();this._radialChildSVG=this._createSVGElement();this._childArcGroup=this._createGroupElement("childarcgroup");this._polygonGroup=this._createGroupElement("polygongroup");this._polygonGroup.attr("transform","translate(-10,-10)");this._radialArcGroup=this._createGroupElement("outerarcpathgroup");this._menuItemsArcGroup=this._createGroupElement("arcpathgroup");this._menuItemsGroup=this._createGroupElement("menuitemsgroup");this._menuItemsGroup.attr("transform","translate(-10,-5)");this._menuItemsArcPaths=this._createGroupElement("menuitemsarcpaths");this._menuItemsPaths=this._createGroupElement("itemspathgroup");this._radialSVGDiv=t.buildTag("div#"+this._id+"_svgdiv."+this._prefix+"abs").css("height",this._diameter);this._radialCircleSVGDiv=t.buildTag("div#"+this._id+"_circlesvgdiv."+this._prefix+"abs "+this._prefix+"displaynone");this._radialChildSVGDiv=t.buildTag("div#"+this._id+"_childsvgdiv."+this._prefix+"abs "+this._prefix+"displaynone");var i=this._calculateRadialArc(this._itemCount,1,.28,.8,.95),r=this._calculateRadialArc(this._itemCount,.79,null,.97,null),u=this._calculateRadialArc(this._itemCount,.8,null,.3,null),f=this._calculateRadialArc(1,1,.28,.8,null),v=this._pathDirection(f.x1[0],f.y1[0],f.radius,f.longArc[0],f.x2[0],f.y2[0],f.dEndX[0],f.dEndY[0],f.dradius,f.dStartX[0],f.dStartY[0]),h=20;for(n=0;n<this._itemCount;n++){var c=this._pathDirection(i.x1[n],i.y1[n],i.radius,i.longArc[n],i.x2[n],i.y2[n],i.dEndX[n],i.dEndY[n],i.dradius,i.dStartX[n],i.dStartY[n]),y=this._pathDirection(r.x1[n],r.y1[n],r.radius,r.longArc[n],r.x2[n],r.y2[n],r.dEndX[n],r.dEndY[n],r.dradius,r.dStartX[n],r.dStartY[n]),p=this._pathDirection(u.x1[n],u.y1[n],u.radius,u.longArc[n],u.x2[n],u.y2[n],u.dEndX[n],u.dEndY[n],u.dradius,u.dStartX[n],u.dStartY[n]),e=this.model.items[n],o=this._createGroupElement(),s=this._enableItem(e,o);this._menuItemsArcPaths.append(this._createPathElement(n,y,""+this._prefix+"arcbgcolor"));this._menuItemsPaths.append(this._createPathElement(n,p,s.itemPathClass));this._menuItemsArcGroup.append(this._createPathElement(n,c,s.arcPathClass));o.attr({cursor:"context-menu","data-ej-index":n});o.append(this._createImageElement(h,h,e,i.midx[n],i.midy[n]-h/2));o.append(this._createTextElement(i.midx[n]+10,i.midy[n]+25,s.textClass,s.dataText));e.badge&&(e.badge.enabled=="true"||e.badge.enabled==!0)&&o.append(this._createCircleElement(i.midx[n]+25,i.midy[n]-10,10,""+this._prefix+"badgecircle")).append(this._createTextElement(i.midx[n]+25,i.midy[n]-7,""+this._prefix+"badgetext",e.badge.value));this._menuItemsGroup.append(o);(e.items||e.type&&e.type.toLowerCase()==(this.model.renderMode?t.mobile:t).RadialMenu.ItemType.Slider)&&(l=i.deg[n]>270?"translate("+(i.dmidx[n]+5)+","+(i.dmidy[n]+15)+")rotate("+i.deg[n]+")":"translate("+(i.dmidx[n]+10)+","+(i.dmidy[n]+10)+")rotate("+i.deg[n]+")",this._childArcGroup.append(this._createPathElement(n,c,""+this._prefix+"childdefault")),this._polygonGroup.append(this._createPolygonElement(n,l)))}a=this._createCircleElement(this._startXY,this._startXY,this._startXY/3.5,""+this._prefix+"circlebgcolor");this._radialArcGroup.append(this._createPathElement(1,v,""+this._prefix+"outerdefault"));this._radialChildSVG.append(this._menuItemsPaths).append(this._menuItemsArcGroup).append(this._childArcGroup).append(this._polygonGroup).append(this._menuItemsArcPaths).append(this._menuItemsGroup).append(a);this._radialContainer.append(this._radialSVGDiv.append(this._radialCircleSVGDiv.append(this._radialSVG.append(this._radialArcGroup))).append(this._radialChildSVGDiv.append(this._radialChildSVG)))},_ejMenuBaseItemsRemove:function(){this._menuChildSVGDiv=t.buildTag("div#"+this._id+"_menuchildsvgdiv."+this._prefix+"abs");this.model.enableAnimation?(this._radialChildSVGDiv.removeClass(""+this._prefix+"radialshow").addClass(""+this._prefix+"scalehide"),this._radialChildAnimate(),this._menuChildSVGDiv.addClass(""+this._prefix+"scaleshow")):this._radialChildSVGDiv.addClass(""+this._prefix+"displaynone");this._radial.removeClass(this.model.imageClass).addClass(""+this._prefix+"backarrow "+this.model.backImageClass+"")},_renderRadialChildMenu:function(n){var t,c;this._menuChildSVG=this._createSVGElement();this._childItemsGroup=this._createGroupElement("childitemsgroup");this._childItemsGroup.attr("transform","translate(-10,-10)");this._childItemArcGroup=this._createGroupElement();this._childItemsArcPaths=this._createGroupElement("childitemsarcpaths");this._childItemsPaths=this._createGroupElement();var e=n.items.length,i=this._calculateRadialArc(e,1,.28,.8,null),r=this._calculateRadialArc(e,.79,null,.97,null),u=this._calculateRadialArc(e,.8,null,.3,null),s=20;for(t=0;t<e;t++){var l=this._pathDirection(i.x1[t],i.y1[t],i.radius,i.longArc[t],i.x2[t],i.y2[t],i.dEndX[t],i.dEndY[t],i.dradius,i.dStartX[t],i.dStartY[t]),a=this._pathDirection(r.x1[t],r.y1[t],r.radius,r.longArc[t],r.x2[t],r.y2[t],r.dEndX[t],r.dEndY[t],r.dradius,r.dStartX[t],r.dStartY[t]),v=this._pathDirection(u.x1[t],u.y1[t],u.radius,u.longArc[t],u.x2[t],u.y2[t],u.dEndX[t],u.dEndY[t],u.dradius,u.dStartX[t],u.dStartY[t]),f=this._createGroupElement(),h=n.items[t],o=this._enableItem(h,f);this._childItemArcGroup.append(this._createPathElement(t,l,o.arcPathClass));this._childItemsArcPaths.append(this._createPathElement(t,a,""+this._prefix+"arcbgcolor"));this._childItemsPaths.append(this._createPathElement(t,v,o.itemPathClass));f.attr({cursor:"context-menu","data-ej-index":t});f.append(this._createImageElement(s,s,h,i.midx[t],i.midy[t]-s/2));f.append(this._createTextElement(i.midx[t]+10,i.midy[t]+25,o.textClass,o.dataText));this._childItemsGroup.append(f)}c=this._createCircleElement(this._startXY,this._startXY,this._startXY/3.5,""+this._prefix+"circlebgcolor");this._radialSVGDiv.append(this._menuChildSVGDiv.append(this._menuChildSVG.append(this._childItemsPaths).append(this._childItemArcGroup).append(this._childItemsArcPaths).append(this._childItemsGroup).append(c)))},_radialSliderHandler:function(n){this.updateBadgeValue(this._index,n.value);this._data={value:n.value,oldValue:n.oldValue,index:this._index};this._eventsTrigger(this._data,this.model.renderMode?"touch":"click")},_isRSText:function(n){return n==this._prefix+"ticks-text"||n==this._prefix+"dynamic-text"?!0:!1},_pathDirection:function(n,t,i,r,u,f,e,o,s,h,c){return"M "+n+" "+t+" A "+i+" "+i+" 0 "+r+" 1 "+u+" "+f+" L "+e+" "+o+" A "+s+" "+s+" 1 "+r+" 0 "+h+" "+c+" z"},_enableItem:function(n,t){var u=this._prefix+"itembgcolor",f=this._prefix+"default",i=""+this._prefix+"textcolor",r=this.model.renderMode?n[this.model.renderMode].text:n.text;return n.enabled=="false"||n.enabled==!1?(t.attr("class",this._prefix+"itemdisabled"),{itemPathClass:this._prefix+"itembgcolor "+this._prefix+"pathdisabled",arcPathClass:this._prefix+"default "+this._prefix+"pathdisabled",dataText:r,textClass:i}):{itemPathClass:u,arcPathClass:f,dataText:r,textClass:i}},_windowsMenuShow:function(){this.model.enableAnimation?(this._radialCircleSVGDiv.removeClass(this._prefix+"displaynone "+this._prefix+"radialhide").addClass(this._prefix+"radialshow"),this._radialChildSVGDiv.removeClass(this._prefix+"displaynone "+this._prefix+"radialhide").addClass(this._prefix+"radialshow")):(this._radialCircleSVGDiv.removeClass(this._prefix+"displaynone"),this._radialChildSVGDiv.removeClass(this._prefix+"displaynone"));this._openCloseTrigger("open")},_windowsMenuHide:function(n){this.model.enableAnimation?(this._radialCircleSVGDiv.removeClass(this._prefix+"radialshow").addClass(this._prefix+"radialhide"),this._radialChildSVGDiv.removeClass(this._prefix+"radialshow "+this._prefix+"scaleshow").addClass(this._prefix+"radialhide"),setTimeout(function(){n._radialCircleSVGDiv.addClass(n._prefix+"displaynone");n._radialChildSVGDiv.addClass(n._prefix+"displaynone")},160)):(this._radialCircleSVGDiv.addClass(this._prefix+"displaynone"),this._radialChildSVGDiv.addClass(this._prefix+"displaynone"));this._openCloseTrigger("close")},_windowsInnerMenuHide:function(n){this._radial.removeClass(this._prefix+"backarrow "+this.model.backImageClass+"").addClass(this.model.imageClass);this.model.enableAnimation?(this._menuChildSVGDiv.removeClass(this._prefix+"scaleshow").addClass(this._prefix+"scalehide"),setTimeout(function(){n._menuChildSVGDiv.remove()},150),setTimeout(function(){n._radialChildSVGDiv.removeClass(n._prefix+"scalehide "+n._prefix+"displaynone").addClass(n._prefix+"scaleshow")},200)):(this._menuChildSVGDiv.remove(),this._radialChildSVGDiv.removeClass(this._prefix+"displaynone"))},_eventsTrigger:function(n,t){var i=this.model[t];this.model[t]=this._childTarget&&this.model.items[n.index].items[n.childIndex][t]?this.model.items[n.index].items[n.childIndex][t]:!this._childTarget&&this.model.items[n.index][t]?this.model.items[n.index][t]:this.model[t];this.model[t]&&this._trigger(t,n);this.model[t]=i},_openCloseTrigger:function(n){this.model[n]&&this._trigger(n)},_menuEnabledDisabled:function(t,i){var r=this;Array.isArray(t)&&n.each(t,function(t,u){Array.isArray(u)?n.each(u,function(t,f){Array.isArray(f)?n.each(f,function(n,t){r.model.items[u].items[t].enabled=i=="disabled"?!1:!0}):((!r.model.renderMode||r.model.renderMode&&(r.model.renderMode=="windows"||r.model.renderMode=="flat"))&&r._menuItems(r,f,i),u=f)}):r._menuItems(r,u,i)})},_ejMenuItem:function(i,r,u){var f=n(this._menuItemsGroup.find("g")[r]),e=n(this._menuItemsPaths.find("path")[r]),o=n(this._radialChildSVGDiv.find("#arcpathgroup path")[r]);u=="disabled"?(e.attr("class",""+this._prefix+"itembgcolor "+this._prefix+"pathdisabled"),o.attr("class",""+this._prefix+"default "+this._prefix+"pathdisabled"),f.attr("class",""+this._prefix+"itemdisabled"),!t.isNullOrUndefined(this._radialChildSVGDiv)&&n(this._radialChildSVGDiv.find("#polygongroup polygon[index='"+r+"']")).length>0&&(n(this._radialChildSVGDiv.find("#polygongroup polygon[index='"+r+"']")).attr("class",this._prefix+"itemdisabled"),n(this._radialChildSVGDiv.find("#childarcgroup path[index='"+r+"']")).attr("class",this._prefix+"itemdisabled"))):(e.attr("class",""+this._prefix+"itembgcolor"),o.attr("class",""+this._prefix+"default"),f.removeAttr("class"),!t.isNullOrUndefined(this._radialChildSVGDiv)&&n(this._radialChildSVGDiv.find("#polygongroup polygon[index='"+r+"']")).length>0&&n(this._radialChildSVGDiv.find("#polygongroup polygon[index='"+r+"']")).removeAttr("class"),n(this._radialChildSVGDiv.find("#childarcgroup path[index='"+r+"']")).attr("class",this._prefix+"childdefault"))},_ejMenuHide:function(n){this.model.enableAnimation?(this._radialCircleSVGDiv.removeClass(this._prefix+"radialshow").addClass(this._prefix+"radialhide"),this._radialChildSVGDiv.removeClass(this._prefix+"radialshow "+this._prefix+"scaleshow").addClass(this._prefix+"radialhide"),setTimeout(function(){n._radialCircleSVGDiv.addClass(n._prefix+"displaynone").removeClass(n._prefix+"radialshow "+n._prefix+"radialhide "+n._prefix+"scaleshow "+n._prefix+"scalehide");n._radialChildSVGDiv.addClass(n._prefix+"displaynone").removeClass(n._prefix+"radialshow "+n._prefix+"radialhide "+n._prefix+"scaleshow "+n._prefix+"scalehide")},160)):(this._radialCircleSVGDiv.addClass(this._prefix+"displaynone"),this._radialChildSVGDiv.addClass(this._prefix+"displaynone"));this._openCloseTrigger("close")},_ejInnerMenuHide:function(n){this._radial.removeClass(this._prefix+"backarrow "+this.model.backImageClass+"").addClass(this.model.imageClass);this.model.enableAnimation?(this._radialCircleSVGDiv.removeClass(this._prefix+"radialshow "+this._prefix+"radialhide "+this._prefix+"scaleshow "+this._prefix+"scalehide").addClass(this._prefix+"displaynone"),this._radialChildSVGDiv.removeClass(this._prefix+"radialshow "+this._prefix+"radialhide "+this._prefix+"scaleshow "+this._prefix+"scalehide").addClass(this._prefix+"displaynone"),this._menuChildSVGDiv.removeClass(this._prefix+"scaleshow").addClass(this._prefix+"radialhide"),setTimeout(function(){n._menuChildSVGDiv.removeClass(n._prefix+"radialhide").remove()},150)):(this._radialCircleSVGDiv.addClass(this._prefix+"displaynone"),this._radialChildSVGDiv.addClass(this._prefix+"displaynone"),this._menuChildSVGDiv.remove());this._openCloseTrigger("close")},_currentBadgeEle:function(t){return this.model.renderMode=="android"?n(this._imageBadgeGroup.find("g")[t]):n(this._menuItemsGroup.find("g")[t])},_badge:function(n){return this._currentBadgeEle(n).find("."+this._prefix+"badgetext,."+this._prefix+"badgecircle")},show:function(){this.element.removeClass(this._prefix+"displaynone")},hide:function(){this.element.addClass(this._prefix+"displaynone")},showMenu:function(){this._showMenu()},hideMenu:function(){this._hideMenu()},enableItemByIndex:function(n){this._menuItems(this,n,"enabled")},enableItemsByIndices:function(n){this._menuEnabledDisabled(n,"enabled")},disableItemByIndex:function(n){this._menuItems(this,n,"disabled")},disableItemsByIndices:function(n){this._menuEnabledDisabled(n,"disabled")},updateBadgeValue:function(n,t){this.model.items[n].badge.value=t;this._currentBadgeEle(n).children("text."+this._prefix+"badgetext").html(t)},showBadge:function(n){this._badge(n).show()},hideBadge:function(n){this._badge(n).hide()},_setModel:function(n){n.items&&(this.model.items=n.items);this._refresh()},_refresh:function(){this._destroy();this._init()},_destroy:function(){t.listenEvents([document,document],[t.endEvent(),t.startEvent()],[this._targetClick,this._targetDown],!0);this.element.removeAttr("class style").children().remove()}})})(jQuery,Syncfusion)});
