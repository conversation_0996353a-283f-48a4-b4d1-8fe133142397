<?xml version="1.0"?>
<Reports>
  <!--Report *** CompanyData Subreport ***-->
  <Report version="2.6.20093.53207">
    <Name>CompanyData Subreport</Name>
    <ReportInfo>
      <Author>Peter</Author>
    </ReportInfo>
    <DataSource />
    <Layout>
      <Width>10110</Width>
      <MarginLeft>0</MarginLeft>
      <MarginTop>0</MarginTop>
      <MarginRight>0</MarginRight>
      <MarginBottom>0</MarginBottom>
      <Orientation>1</Orientation>
    </Layout>
    <Font>
      <Name>Verdana</Name>
      <Size>9</Size>
    </Font>
    <Groups />
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>285</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>5</Height>
        <Visible>0</Visible>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>LogoField</Name>
        <Section>1</Section>
        <Width>10110</Width>
        <Height>285</Height>
        <ZOrder>-4</ZOrder>
        <Align>0</Align>
        <Picture>Logo</Picture>
        <CanGrow>-1</CanGrow>
        <CanShrink>-1</CanShrink>
        <Font>
          <Name>Verdana</Name>
          <Size>8.25</Size>
        </Font>
      </Field>
      <Field>
        <Name>HotelDataField</Name>
        <Section>1</Section>
        <Text>HotelData</Text>
        <Calculated>-1</Calculated>
        <RTF>-1</RTF>
        <Width>10110</Width>
        <Height>285</Height>
        <ZOrder>-3</ZOrder>
        <Align>0</Align>
        <CanGrow>-1</CanGrow>
        <CanShrink>-1</CanShrink>
        <Font>
          <Name>Verdana</Name>
          <Size>8.25</Size>
        </Font>
      </Field>
    </Fields>
  </Report>
  <!--Report *** ServicesIncome ***-->
  <Report version="2.6.20093.53207">
    <Name>ServicesIncome</Name>
    <ReportInfo>
      <Author>Peter</Author>
      <Title>Services Income</Title>
    </ReportInfo>
    <DataSource>
      <RecordSource>Receipts</RecordSource>
    </DataSource>
    <Layout>
      <Width>15105</Width>
      <MarginLeft>590.4</MarginLeft>
      <MarginTop>590.4</MarginTop>
      <MarginRight>590.4</MarginRight>
      <MarginBottom>590.4</MarginBottom>
      <Orientation>2</Orientation>
    </Layout>
    <Font>
      <Name>Verdana</Name>
      <Size>9</Size>
    </Font>
    <Groups>
      <Group>
        <Name>TotalGroup</Name>
      </Group>
      <Group>
        <Name>Year Group</Name>
        <GroupBy>Year(StartDate)</GroupBy>
        <Sort>2</Sort>
      </Group>
      <Group>
        <Name>Month Group</Name>
        <GroupBy>Month(StartDate)</GroupBy>
        <Sort>2</Sort>
      </Group>
      <Group>
        <Name>Driver Group</Name>
        <GroupBy>DriverId</GroupBy>
        <Sort>1</Sort>
      </Group>
    </Groups>
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Height>285</Height>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>1530</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>300</Height>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>500</Height>
      </Section>
      <Section>
        <Name>TotalGroupHeader</Name>
        <Type>5</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>TotalGroupFooter</Name>
        <Type>6</Type>
        <Height>420</Height>
      </Section>
      <Section>
        <Name>YearGroupHeader</Name>
        <Type>7</Type>
        <Height>390</Height>
      </Section>
      <Section>
        <Name>YearGroupFooter</Name>
        <Type>8</Type>
        <Height>555</Height>
      </Section>
      <Section>
        <Name>MonthGroupHeader</Name>
        <Type>9</Type>
        <Height>420</Height>
      </Section>
      <Section>
        <Name>MonthGroupFooter</Name>
        <Type>10</Type>
        <Height>540</Height>
      </Section>
      <Section>
        <Name>DriverGroupHeader</Name>
        <Type>11</Type>
        <Height>420</Height>
      </Section>
      <Section>
        <Name>DriverGroupFooter</Name>
        <Type>12</Type>
        <Height>540</Height>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>AmountCtl</Name>
        <Section>0</Section>
        <Text>Price</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13500</Left>
        <Width>1605</Width>
        <Height>285</Height>
        <Align>8</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>CustomerIDCtl</Name>
        <Section>0</Section>
        <Text>StartDate</Text>
        <Calculated>-1</Calculated>
        <Format>Date</Format>
        <Left>3135</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>6</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrLeft1</Name>
        <Section>4</Section>
        <Text>Now()</Text>
        <Calculated>-1</Calculated>
        <Top>75</Top>
        <Width>4515</Width>
        <Height>300</Height>
        <Align>0</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrRight1</Name>
        <Section>4</Section>
        <Text>"Page " &amp; [Page] &amp; " of " &amp; [Pages]</Text>
        <Calculated>-1</Calculated>
        <Left>10545</Left>
        <Top>60</Top>
        <Width>4560</Width>
        <Height>300</Height>
        <Align>2</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>3</Section>
        <Text>Service ID</Text>
        <Left>1995</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>Field2</Name>
        <Section>3</Section>
        <Top>255</Top>
        <Width>15105</Width>
        <Height>30</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <ForeColor />
        <BorderStyle>1</BorderStyle>
        <Font>
          <Name>Arial</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field3</Name>
        <Section>3</Section>
        <Text>Start Date</Text>
        <Format>Date</Format>
        <Left>3135</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>Field5</Name>
        <Section>3</Section>
        <Text>Driver</Text>
        <Left>4410</Left>
        <Width>2145</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>AmountHeaderCtl</Name>
        <Section>3</Section>
        <Text>Price</Text>
        <Left>13500</Left>
        <Width>1605</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>titleLbl</Name>
        <Section>1</Section>
        <Text>Service Income</Text>
        <Top>420</Top>
        <Width>15105</Width>
        <Height>600</Height>
        <Align>6</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>18</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field7</Name>
        <Section>1</Section>
        <Top>945</Top>
        <Width>15105</Width>
        <Height>45</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <ForeColor />
        <LineWidth>1</LineWidth>
        <Font>
          <Name>Arial</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>DatesField</Name>
        <Section>1</Section>
        <Text>Dates from {0} to {1}</Text>
        <Top>990</Top>
        <Width>10275</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8.25</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field8</Name>
        <Section>10</Section>
        <Text>"Total of month "&amp;Format(StartDate, "MMMM yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>7695</Left>
        <Top>30</Top>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field9</Name>
        <Section>10</Section>
        <Left>11190</Left>
        <Width>3885</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>Field10</Name>
        <Section>10</Section>
        <Left>11190</Left>
        <Top>15</Top>
        <Width>3885</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>MonthGroupFooterSumAmountCtl</Name>
        <Section>10</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13470</Left>
        <Top>45</Top>
        <Width>1605</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>1</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field12</Name>
        <Section>9</Section>
        <Text>Format(StartDate, "MMMM yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>600</Left>
        <Top>75</Top>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field13</Name>
        <Section>9</Section>
        <Left>600</Left>
        <Top>360</Top>
        <Width>2850</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>CustomerIDCtl2</Name>
        <Section>0</Section>
        <Text>ServiceId</Text>
        <Calculated>-1</Calculated>
        <Left>1995</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field4</Name>
        <Section>0</Section>
        <Text>DriverFullName</Text>
        <Calculated>-1</Calculated>
        <Format>Percent</Format>
        <Left>4410</Left>
        <Width>2145</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field14</Name>
        <Section>7</Section>
        <Text>Format(StartDate, "yyyy")</Text>
        <Calculated>-1</Calculated>
        <Top>75</Top>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field15</Name>
        <Section>7</Section>
        <Top>360</Top>
        <Width>2850</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>Field16</Name>
        <Section>8</Section>
        <Text>"Total of year "&amp;Format(StartDate, "yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>7695</Left>
        <Top>30</Top>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field17</Name>
        <Section>8</Section>
        <Left>11190</Left>
        <Width>3885</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>Field18</Name>
        <Section>8</Section>
        <Left>11190</Left>
        <Top>15</Top>
        <Width>3885</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>YearGroupFooterSumAmountCtl</Name>
        <Section>8</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13470</Left>
        <Top>45</Top>
        <Width>1605</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>1</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field20</Name>
        <Section>6</Section>
        <Text>"Total:"</Text>
        <Calculated>-1</Calculated>
        <Left>11835</Left>
        <Top>30</Top>
        <Width>1560</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field24</Name>
        <Section>6</Section>
        <Left>11190</Left>
        <Width>3855</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>Field25</Name>
        <Section>6</Section>
        <Left>11190</Left>
        <Top>15</Top>
        <Width>3855</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>TotalGroupFooterSumAmountCtl</Name>
        <Section>6</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13470</Left>
        <Top>45</Top>
        <Width>1605</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>2</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field22</Name>
        <Section>3</Section>
        <Text>Mission</Text>
        <Left>10830</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>Field23</Name>
        <Section>0</Section>
        <Text>Mission</Text>
        <Calculated>-1</Calculated>
        <Format>Percent</Format>
        <Left>10830</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field26</Name>
        <Section>11</Section>
        <Text>DriverFullName</Text>
        <Calculated>-1</Calculated>
        <Left>1140</Left>
        <Top>75</Top>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field27</Name>
        <Section>12</Section>
        <Text>"Total of driver "&amp; DriverFullName &amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>7695</Left>
        <Top>30</Top>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>DriverFooterSumAmountCtl</Name>
        <Section>12</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13470</Left>
        <Top>30</Top>
        <Width>1605</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>1</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field29</Name>
        <Section>3</Section>
        <Text>Payment Type</Text>
        <Left>11970</Left>
        <Width>1425</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>Field30</Name>
        <Section>0</Section>
        <Text>PaymentType</Text>
        <Calculated>-1</Calculated>
        <Format>Percent</Format>
        <Left>11970</Left>
        <Width>1425</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field31</Name>
        <Section>3</Section>
        <Text>Customer</Text>
        <Left>6690</Left>
        <Width>3855</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>Field32</Name>
        <Section>0</Section>
        <Text>CustomerFullName</Text>
        <Calculated>-1</Calculated>
        <Format>Percent</Format>
        <Left>6690</Left>
        <Width>3855</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field33</Name>
        <Section>12</Section>
        <Left>11190</Left>
        <Width>3885</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>Field34</Name>
        <Section>12</Section>
        <Left>11190</Left>
        <Top>15</Top>
        <Width>3885</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
    </Fields>
  </Report>
  <!--Report *** Tasks ***-->
  <Report version="2.6.20093.53207">
    <Name>Tasks</Name>
    <ReportInfo>
      <Author>Peter</Author>
      <Title>Επισκέψεις</Title>
    </ReportInfo>
    <DataSource>
      <ConnectionString>Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;Initial Catalog=Anres;Data Source=MAIN\SQLEXPRESS</ConnectionString>
    </DataSource>
    <Layout>
      <Width>10695</Width>
      <MarginLeft>590.4</MarginLeft>
      <MarginTop>590.4</MarginTop>
      <MarginRight>590.4</MarginRight>
      <MarginBottom>590.4</MarginBottom>
      <Orientation>1</Orientation>
    </Layout>
    <Font>
      <Name>Verdana</Name>
      <Size>9</Size>
    </Font>
    <Groups>
      <Group>
        <Name>Total Group</Name>
      </Group>
      <Group>
        <Name>Year Group</Name>
        <GroupBy>Year(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Month Group</Name>
        <GroupBy>Month(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Contact Group</Name>
        <GroupBy>ContactId</GroupBy>
        <Sort>1</Sort>
      </Group>
    </Groups>
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Height>285</Height>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>1245</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>375</Height>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>500</Height>
      </Section>
      <Section>
        <Name>TotalGroupHeader</Name>
        <Type>5</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>TotalGroupFooter</Name>
        <Type>6</Type>
        <Height>360</Height>
      </Section>
      <Section>
        <Name>YearGroupHeader</Name>
        <Type>7</Type>
        <Height>420</Height>
      </Section>
      <Section>
        <Name>YearGroupFooter</Name>
        <Type>8</Type>
        <Height>360</Height>
      </Section>
      <Section>
        <Name>MonthGroupHeader</Name>
        <Type>9</Type>
        <Height>420</Height>
      </Section>
      <Section>
        <Name>MonthGroupFooter</Name>
        <Type>10</Type>
        <Height>360</Height>
      </Section>
      <Section>
        <Name>ContactGroupHeader</Name>
        <Type>11</Type>
        <Height>420</Height>
      </Section>
      <Section>
        <Name>ContactGroupFooter</Name>
        <Type>12</Type>
        <Height>360</Height>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>AmountCtl</Name>
        <Section>0</Section>
        <Text>TotalCost</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9120</Left>
        <Width>1545</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>CustomerIDCtl</Name>
        <Section>0</Section>
        <Text>ContactFullName</Text>
        <Calculated>-1</Calculated>
        <Left>3390</Left>
        <Width>2310</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>PaymentDateCtl</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>dd/MM/yyyy</Format>
        <Left>570</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrLeft1</Name>
        <Section>4</Section>
        <Text>Now()</Text>
        <Calculated>-1</Calculated>
        <Format>dd/MM/yyyy HH:mm</Format>
        <Top>75</Top>
        <Width>4515</Width>
        <Height>300</Height>
        <Align>0</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrRight1</Name>
        <Section>4</Section>
        <Text>"Σελίδα " &amp; [Page] &amp; " από " &amp; [Pages]</Text>
        <Calculated>-1</Calculated>
        <Left>6135</Left>
        <Top>75</Top>
        <Width>4560</Width>
        <Height>300</Height>
        <Align>2</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field2</Name>
        <Section>3</Section>
        <Top>255</Top>
        <Width>10680</Width>
        <Height>20</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <ForeColor />
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
        <Font>
          <Name>Arial</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field3</Name>
        <Section>3</Section>
        <Text>Ημ/νία</Text>
        <Calculated>-1</Calculated>
        <Left>570</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>Field4</Name>
        <Section>3</Section>
        <Text>Πελάτης</Text>
        <Calculated>-1</Calculated>
        <Left>3390</Left>
        <Width>2310</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>Field6</Name>
        <Section>3</Section>
        <Text>Ποσό</Text>
        <Calculated>-1</Calculated>
        <Left>9120</Left>
        <Width>1545</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>CompanyDataSubreportField</Name>
        <Section>1</Section>
        <Width>10680</Width>
        <Height>135</Height>
        <Visible>0</Visible>
        <CanGrow>-1</CanGrow>
        <Subreport>CompanyData Subreport</Subreport>
      </Field>
      <Field>
        <Name>titleLbl1</Name>
        <Section>1</Section>
        <Text>ΕΠΙΣΚΕΨΕΙΣ</Text>
        <Top>135</Top>
        <Width>10680</Width>
        <Height>600</Height>
        <Align>6</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>18</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field7</Name>
        <Section>1</Section>
        <Top>705</Top>
        <Width>10680</Width>
        <Height>45</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <ForeColor />
        <LineWidth>1</LineWidth>
        <Font>
          <Name>Arial</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>FilterField</Name>
        <Section>1</Section>
        <Text>Αφίξεις από {0} εως {1}</Text>
        <Top>780</Top>
        <Width>10680</Width>
        <Height>285</Height>
        <Align>6</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8.25</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field8</Name>
        <Section>10</Section>
        <Text>"Σύνολο "&amp;Format(StartTime, "MMMM yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>3270</Left>
        <Top>75</Top>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field9</Name>
        <Section>10</Section>
        <Left>5070</Left>
        <Top>90</Top>
        <Width>5595</Width>
        <Height>15</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
      </Field>
      <Field>
        <Name>Field11</Name>
        <Section>10</Section>
        <Text>SUM(TotalCost)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9120</Left>
        <Top>75</Top>
        <Width>1545</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>1</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field12</Name>
        <Section>9</Section>
        <Text>Format(StartTime, "MMMM yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>285</Left>
        <Top>60</Top>
        <Width>4125</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field13</Name>
        <Section>9</Section>
        <Left>285</Left>
        <Top>332</Top>
        <Width>2850</Width>
        <Height>15</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
      </Field>
      <Field>
        <Name>Field14</Name>
        <Section>7</Section>
        <Text>Format(StartTime, "yyyy")</Text>
        <Calculated>-1</Calculated>
        <Top>75</Top>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field15</Name>
        <Section>7</Section>
        <Top>332</Top>
        <Width>2850</Width>
        <Height>20</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
      </Field>
      <Field>
        <Name>Field16</Name>
        <Section>8</Section>
        <Text>"Σύνολο έτους "&amp;Format(StartTime, "yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>3270</Left>
        <Top>75</Top>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field17</Name>
        <Section>8</Section>
        <Left>5085</Left>
        <Top>90</Top>
        <Width>5595</Width>
        <Height>15</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
      </Field>
      <Field>
        <Name>Field19</Name>
        <Section>8</Section>
        <Text>SUM(TotalCost)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9105</Left>
        <Top>75</Top>
        <Width>1545</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>1</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field20</Name>
        <Section>6</Section>
        <Text>"Σύνολο:"</Text>
        <Calculated>-1</Calculated>
        <Left>7215</Left>
        <Top>75</Top>
        <Width>1755</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field21</Name>
        <Section>6</Section>
        <Left>5085</Left>
        <Top>90</Top>
        <Width>5595</Width>
        <Height>15</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
      </Field>
      <Field>
        <Name>Field23</Name>
        <Section>6</Section>
        <Text>SUM(TotalCost)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9120</Left>
        <Top>75</Top>
        <Width>1545</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>2</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>3</Section>
        <Text>Τεχνικός</Text>
        <Calculated>-1</Calculated>
        <Left>5835</Left>
        <Width>1425</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>CustomerIDCtl2</Name>
        <Section>0</Section>
        <Text>UserFullName</Text>
        <Calculated>-1</Calculated>
        <Left>5835</Left>
        <Width>1425</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field25</Name>
        <Section>3</Section>
        <Text>Έναρξη</Text>
        <Calculated>-1</Calculated>
        <Left>1710</Left>
        <Width>690</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>PaymentDateCtl1</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>1710</Left>
        <Width>690</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field5</Name>
        <Section>3</Section>
        <Text>Λήξη</Text>
        <Calculated>-1</Calculated>
        <Left>2535</Left>
        <Width>720</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>PaymentDateCtl2</Name>
        <Section>0</Section>
        <Text>EndTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>2535</Left>
        <Width>720</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field10</Name>
        <Section>3</Section>
        <Text>Κατηγορία</Text>
        <Calculated>-1</Calculated>
        <Left>7410</Left>
        <Width>1560</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>CustomerIDCtl1</Name>
        <Section>0</Section>
        <Text>TaskCategoryName</Text>
        <Calculated>-1</Calculated>
        <Left>7410</Left>
        <Width>1560</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field18</Name>
        <Section>11</Section>
        <Text>ContactFullName</Text>
        <Calculated>-1</Calculated>
        <Left>585</Left>
        <Top>60</Top>
        <Width>4125</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field22</Name>
        <Section>11</Section>
        <Left>554.75205880608257</Left>
        <Top>332</Top>
        <Width>2850</Width>
        <Height>20</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
      </Field>
      <Field>
        <Name>Field24</Name>
        <Section>12</Section>
        <Text>"Σύνολο "&amp;ContactFullName&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>3270</Left>
        <Top>75</Top>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field26</Name>
        <Section>12</Section>
        <Left>5070</Left>
        <Top>90</Top>
        <Width>5595</Width>
        <Height>15</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
      </Field>
      <Field>
        <Name>Field27</Name>
        <Section>12</Section>
        <Text>SUM(TotalCost)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9120</Left>
        <Top>75</Top>
        <Width>1545</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>1</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field28</Name>
        <Section>9</Section>
        <Text>Month(StartTime)</Text>
        <Calculated>-1</Calculated>
        <Left>4845</Left>
        <Top>60</Top>
        <Width>4125</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field29</Name>
        <Section>11</Section>
        <Text>ContactId</Text>
        <Calculated>-1</Calculated>
        <Left>4845</Left>
        <Top>60</Top>
        <Width>4125</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
    </Fields>
  </Report>
  <!--Report *** Contacts ***-->
  <Report version="2.6.20093.53207">
    <Name>Contacts</Name>
    <ReportInfo>
      <Author>peter</Author>
    </ReportInfo>
    <DataSource>
      <ConnectionString>Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;Initial Catalog=MentalView;Data Source=MAIN\SQLEXPRESS</ConnectionString>
      <RecordSource>Contacts</RecordSource>
    </DataSource>
    <Layout>
      <Width>14685</Width>
      <MarginLeft>561.6</MarginLeft>
      <MarginTop>561.6</MarginTop>
      <MarginRight>561.6</MarginRight>
      <MarginBottom>561.6</MarginBottom>
      <Orientation>2</Orientation>
    </Layout>
    <Font>
      <Name>Segoe UI</Name>
      <Size>9</Size>
    </Font>
    <OnOpen>' -- style script start
_styleCtr = 0
' -- style script end
</OnOpen>
    <Groups />
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Height>375</Height>
        <OnPrint>' -- style script start
detail.BackColor = iif(_styleCtr mod 2 = 0, rgb(255,255,255),rgb(240,240,240))
_styleCtr = _styleCtr + 1
' -- style script end
</OnPrint>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>1070</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>390</Height>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>500</Height>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>TitleField</Name>
        <Section>1</Section>
        <Text>Πελάτες</Text>
        <Top>135</Top>
        <Width>14670</Width>
        <Height>570</Height>
        <Align>6</Align>
        <Font>
          <Name>Segoe UI</Name>
          <Size>20</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>0</Section>
        <Text>FullName</Text>
        <Calculated>-1</Calculated>
        <Width>2700</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field2</Name>
        <Section>3</Section>
        <Text>Ονοματεπώνυμο</Text>
        <Width>2700</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>ftrRight1</Name>
        <Section>4</Section>
        <Text>"Σελίδα " &amp; [Page] &amp; " από " &amp; [Pages]</Text>
        <Calculated>-1</Calculated>
        <Left>9975</Left>
        <Top>135</Top>
        <Width>4560</Width>
        <Height>300</Height>
        <Align>2</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrLeft1</Name>
        <Section>4</Section>
        <Text>Now()</Text>
        <Calculated>-1</Calculated>
        <Format>dd/MM/yyyy HH:mm</Format>
        <Top>135</Top>
        <Width>4515</Width>
        <Height>300</Height>
        <Align>0</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ReportInfoField</Name>
        <Section>1</Section>
        <Text>&lt;ReportInfo&gt;</Text>
        <Top>705</Top>
        <Width>14670</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field3</Name>
        <Section>3</Section>
        <Text>Τηλέφωνο</Text>
        <Left>5130</Left>
        <Width>1290</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field4</Name>
        <Section>0</Section>
        <Text>Phone1</Text>
        <Calculated>-1</Calculated>
        <Left>5130</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>6</Align>
      </Field>
      <Field>
        <Name>Field5</Name>
        <Section>3</Section>
        <Text>Κινητό</Text>
        <Left>6555</Left>
        <Width>1290</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field6</Name>
        <Section>0</Section>
        <Text>Mobile1</Text>
        <Calculated>-1</Calculated>
        <Left>6555</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>6</Align>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field7</Name>
        <Section>3</Section>
        <Top>285</Top>
        <Width>14670</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field8</Name>
        <Section>3</Section>
        <Text>Θεραπευτής</Text>
        <Left>7980</Left>
        <Width>2145</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field9</Name>
        <Section>0</Section>
        <Text>TherapistFullName</Text>
        <Calculated>-1</Calculated>
        <Left>7980</Left>
        <Width>2145</Width>
        <Height>285</Height>
        <Align>6</Align>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field10</Name>
        <Section>3</Section>
        <Text>Διαγνωστικά</Text>
        <Left>10260</Left>
        <Width>2145</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field11</Name>
        <Section>0</Section>
        <Text>DiagnosticianFullName</Text>
        <Calculated>-1</Calculated>
        <Left>10260</Left>
        <Width>2145</Width>
        <Height>285</Height>
        <Align>6</Align>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field12</Name>
        <Section>3</Section>
        <Text>Κλινική Εποπτεία</Text>
        <Left>12540</Left>
        <Width>2145</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field13</Name>
        <Section>0</Section>
        <Text>ClinicSupervisorFullName</Text>
        <Calculated>-1</Calculated>
        <Left>12540</Left>
        <Width>2145</Width>
        <Height>285</Height>
        <Align>6</Align>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field14</Name>
        <Section>3</Section>
        <Text>Ενεργός</Text>
        <Left>2850</Left>
        <Width>855</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field17</Name>
        <Section>3</Section>
        <Text>Σε αναμονή</Text>
        <Left>3840</Left>
        <Width>1005</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field15</Name>
        <Section>0</Section>
        <Text>Iif(Active,"Ναι","Όχι")</Text>
        <Calculated>-1</Calculated>
        <Left>2850</Left>
        <Width>855</Width>
        <Height>285</Height>
        <Align>7</Align>
      </Field>
      <Field>
        <Name>Field16</Name>
        <Section>0</Section>
        <Text>Iif(Waiting,"Ναι","Όχι")</Text>
        <Calculated>-1</Calculated>
        <Left>3840</Left>
        <Width>855</Width>
        <Height>285</Height>
        <Align>7</Align>
      </Field>
    </Fields>
  </Report>
  <!--Report *** Appointments ***-->
  <Report version="2.6.20093.53207">
    <Name>Appointments</Name>
    <ReportInfo>
      <Author>peter</Author>
    </ReportInfo>
    <DataSource>
      <ConnectionString>Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;Initial Catalog=MentalView;Data Source=MAIN\SQLEXPRESS</ConnectionString>
      <RecordSource>Appointments</RecordSource>
    </DataSource>
    <Layout>
      <Width>14955</Width>
      <MarginLeft>561.6</MarginLeft>
      <MarginTop>561.6</MarginTop>
      <MarginRight>561.6</MarginRight>
      <MarginBottom>561.6</MarginBottom>
      <Orientation>2</Orientation>
    </Layout>
    <Font>
      <Name>Segoe UI</Name>
      <Size>9</Size>
    </Font>
    <OnOpen>' -- style script start
_styleCtr = 0
' -- style script end
</OnOpen>
    <Groups>
      <Group>
        <Name>UserFullName</Name>
        <GroupBy>UserFullName</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Year Group</Name>
        <GroupBy>Year(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Month Group</Name>
        <GroupBy>Month(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
    </Groups>
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Height>285</Height>
        <OnFormat>RoomField.BackColor = Iif(LEN(RoomBackgroundColor)&gt;0,RoomBackgroundColor,"#fff") </OnFormat>
        <OnPrint>' -- style script start
detail.BackColor = iif(_styleCtr mod 2 = 0, rgb(255,255,255),rgb(240,240,240))
_styleCtr = _styleCtr + 1
' -- style script end
</OnPrint>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>1070</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>405</Height>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>500</Height>
      </Section>
      <Section>
        <Name>UserFullName_Header</Name>
        <Type>5</Type>
        <Height>435</Height>
      </Section>
      <Section>
        <Name>UserFullName_Footer</Name>
        <Type>6</Type>
        <Height>285</Height>
      </Section>
      <Section>
        <Name>Year Group_Header</Name>
        <Type>7</Type>
        <Height>420</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Year Group_Footer</Name>
        <Type>8</Type>
        <Height>300</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Month Group_Header</Name>
        <Type>9</Type>
        <Height>390</Height>
      </Section>
      <Section>
        <Name>Month Group_Footer</Name>
        <Type>10</Type>
        <Height>285</Height>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>Field6</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Short Date</Format>
        <Left>420</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field8</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>1695</Left>
        <Width>570</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field15</Name>
        <Section>0</Section>
        <Text>EndTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>2415</Left>
        <Width>720</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>ContactFullNameField</Name>
        <Section>0</Section>
        <Text>ContactFullName</Text>
        <Calculated>-1</Calculated>
        <Left>3270</Left>
        <Width>2280</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field17</Name>
        <Section>0</Section>
        <Text>Price</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>11535</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>TitleField</Name>
        <Section>1</Section>
        <Text>Συνεδρίες</Text>
        <Top>135</Top>
        <Width>11115</Width>
        <Height>570</Height>
        <Align>6</Align>
        <Font>
          <Name>Segoe UI</Name>
          <Size>20</Size>
        </Font>
      </Field>
      <Field>
        <Name>ReportInfoField</Name>
        <Section>1</Section>
        <Text>&lt;ReportInfo&gt;</Text>
        <Top>705</Top>
        <Width>11115</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field5</Name>
        <Section>3</Section>
        <Text>Ημερομηνία</Text>
        <Left>420</Left>
        <Width>1140</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field9</Name>
        <Section>3</Section>
        <Text>Έναρξη</Text>
        <Left>1695</Left>
        <Width>720</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field14</Name>
        <Section>3</Section>
        <Text>Λήξη</Text>
        <Left>2415</Left>
        <Width>720</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field2</Name>
        <Section>3</Section>
        <Text>Πελάτης</Text>
        <Left>3270</Left>
        <Width>2280</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field16</Name>
        <Section>3</Section>
        <Text>Κόστος</Text>
        <Left>11535</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>2</Align>
      </Field>
      <Field>
        <Name>Field7</Name>
        <Section>3</Section>
        <Top>285</Top>
        <Width>14955</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>ftrLeft1</Name>
        <Section>4</Section>
        <Text>Now()</Text>
        <Calculated>-1</Calculated>
        <Format>dd/MM/yyyy HH:mm</Format>
        <Top>135</Top>
        <Width>4515</Width>
        <Height>300</Height>
        <Align>0</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrRight1</Name>
        <Section>4</Section>
        <Text>"Σελίδα " &amp; [Page] &amp; " από " &amp; [Pages]</Text>
        <Calculated>-1</Calculated>
        <Left>10545</Left>
        <Top>135</Top>
        <Width>4410</Width>
        <Height>300</Height>
        <Align>2</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field3</Name>
        <Section>5</Section>
        <Text>UserFullName</Text>
        <Calculated>-1</Calculated>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field4</Name>
        <Section>5</Section>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field23</Name>
        <Section>6</Section>
        <Text>"Σύνολο "&amp; UserFullName &amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>4095</Left>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field20</Name>
        <Section>6</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>11535</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field12</Name>
        <Section>7</Section>
        <Text>Format(StartTime, "yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>285</Left>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field11</Name>
        <Section>7</Section>
        <Left>285</Left>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field21</Name>
        <Section>8</Section>
        <Text>"Σύνολο έτους "&amp;Format(StartTime, "yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>4095</Left>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field19</Name>
        <Section>8</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>11535</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field10</Name>
        <Section>9</Section>
        <Text>Format(StartTime, "MMMM yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>420</Left>
        <Width>3990</Width>
        <Height>285</Height>
        <ZOrder>-1</ZOrder>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field13</Name>
        <Section>9</Section>
        <Left>420</Left>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field22</Name>
        <Section>10</Section>
        <Text>"Σύνολο μήνα "&amp;Format(StartTime, "MMMM yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>4095</Left>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field18</Name>
        <Section>10</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>11535</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field24</Name>
        <Section>0</Section>
        <Text>Iif(PaymentType="Card","Κάρτα",Iif(PaymentType="Cash","Μετρητά", Iif(PaymentType="Deposit","Μεταφ./Κατάθ. σε λογαρ.", Iif(PaymentType="Pending","Εκκρεμεί", ""))))</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9690</Left>
        <Width>1695</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field25</Name>
        <Section>3</Section>
        <Text>Τρόπος Πληρωμής</Text>
        <Left>9690</Left>
        <Width>1695</Width>
        <Height>285</Height>
        <Align>2</Align>
      </Field>
      <Field>
        <Name>Field28</Name>
        <Section>3</Section>
        <Text>Κρατήσεις</Text>
        <Left>12825</Left>
        <Width>990</Width>
        <Height>270</Height>
        <Align>2</Align>
      </Field>
      <Field>
        <Name>Field29</Name>
        <Section>0</Section>
        <Text>Deductions</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>12825</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field30</Name>
        <Section>10</Section>
        <Text>SUM(Deductions)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>12825</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field31</Name>
        <Section>8</Section>
        <Text>SUM(Deductions)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>12825</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field32</Name>
        <Section>6</Section>
        <Text>SUM(Deductions)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>12825</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field33</Name>
        <Section>3</Section>
        <Text>Υπόλοιπο</Text>
        <Left>13965</Left>
        <Width>990</Width>
        <Height>270</Height>
        <Align>2</Align>
      </Field>
      <Field>
        <Name>Field34</Name>
        <Section>0</Section>
        <Text>Price-Deductions</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13965</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field35</Name>
        <Section>10</Section>
        <Text>SUM(Price-Deductions)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13965</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field36</Name>
        <Section>8</Section>
        <Text>SUM(Price-Deductions)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13965</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field37</Name>
        <Section>6</Section>
        <Text>SUM(Price-Deductions)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13965</Left>
        <Width>960</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field38</Name>
        <Section>3</Section>
        <Text>Ακυρωμένη</Text>
        <Left>7695</Left>
        <Width>1860</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field39</Name>
        <Section>0</Section>
        <Text>IIF(Canceled, "Ακυρ."+IIF(ChargeableCancellation, " με χρεώση", " χωρίς χρέωση"), "")</Text>
        <Calculated>-1</Calculated>
        <Left>7695</Left>
        <Width>1860</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>3</Section>
        <Text>Τόπος Συνεδρίας</Text>
        <Left>5700</Left>
        <Width>1710</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>RoomField</Name>
        <Section>0</Section>
        <Calculated>-1</Calculated>
        <Left>5700</Left>
        <Top>30</Top>
        <Width>225</Width>
        <Height>225</Height>
        <Align>6</Align>
      </Field>
    </Fields>
  </Report>
  <!--Report *** AppointmentsCountPerYearReport ***-->
  <Report version="2.6.20093.53207">
    <Name>AppointmentsCountPerYearReport</Name>
    <ReportInfo>
      <Author>peter</Author>
    </ReportInfo>
    <DataSource>
      <ConnectionString>Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;Initial Catalog=MentalView;Data Source=MAIN\SQLEXPRESS</ConnectionString>
      <RecordSource>WITH YearlyAppointments AS (
    SELECT 
        YEAR(StartTime) AS AppointmentYear,
        COUNT(*) AS YearlyTotal
    FROM 
        Appointments
    GROUP BY 
        YEAR(StartTime)
)
SELECT 
    ya.AppointmentYear,
    ya.YearlyTotal AS CurrentYearAppointments,
    SUM(ya2.YearlyTotal) AS CumulativeAppointments
FROM 
    YearlyAppointments ya
JOIN 
    YearlyAppointments ya2 ON ya2.AppointmentYear &lt;= ya.AppointmentYear
GROUP BY 
    ya.AppointmentYear, ya.YearlyTotal
ORDER BY 
    ya.AppointmentYear;</RecordSource>
    </DataSource>
    <Layout>
      <Width>14955</Width>
      <Orientation>2</Orientation>
    </Layout>
    <Font>
      <Name>Segoe UI</Name>
      <Size>9</Size>
    </Font>
    <OnOpen>' -- style script start
_styleCtr = 0
' -- style script end
</OnOpen>
    <Groups />
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Height>4540</Height>
        <OnPrint>' -- style script start
detail.BackColor = iif(_styleCtr mod 2 = 0, rgb(255,255,255),rgb(240,240,240))
_styleCtr = _styleCtr + 1
' -- style script end
</OnPrint>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>800</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>400</Height>
        <Visible>0</Visible>
        <BackColor>16768194</BackColor>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>500</Height>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>titleLbl</Name>
        <Section>1</Section>
        <Text>Σύνολο Συνεδριών ανά Έτος</Text>
        <Left>50</Left>
        <Top>200</Top>
        <Width>13953.599609375</Width>
        <Height>600</Height>
        <Align>6</Align>
        <ForeColor>0</ForeColor>
        <Font>
          <Name>Segoe UI</Name>
          <Size>20</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrLeft</Name>
        <Section>4</Section>
        <Text>Now()</Text>
        <Calculated>-1</Calculated>
        <Left>45</Left>
        <Top>105</Top>
        <Width>3375</Width>
        <Height>300</Height>
        <Align>0</Align>
        <ForeColor>0</ForeColor>
        <Font>
          <Name>Segoe UI</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrRight1</Name>
        <Section>4</Section>
        <Text>"Σελίδα " &amp; [Page] &amp; " από " &amp; [Pages]</Text>
        <Calculated>-1</Calculated>
        <Left>10545</Left>
        <Top>75</Top>
        <Width>4410</Width>
        <Height>300</Height>
        <Align>2</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>0</Section>
        <Top>135</Top>
        <Width>14955</Width>
        <Height>4140</Height>
        <PictureScale>1</PictureScale>
        <CustomField asm="C1.C1Report.CustomFields.2" class="C1.C1Report.CustomFields.Chart">
          <ChartType>5</ChartType>
          <DataX>AppointmentYear</DataX>
          <DataY>CumulativeAppointments</DataY>
          <DataColor>8388608</DataColor>
          <GridLines>-1</GridLines>
        </CustomField>
      </Field>
    </Fields>
  </Report>
</Reports>