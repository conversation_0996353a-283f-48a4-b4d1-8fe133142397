/*!
*  filename: ej.mobile.autocomplete.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min","./ej.mobile.listview.min","./ej.mobile.scrollpanel.min"],n):n()})(function(){(function($,ej){ej.widget("ejmAutocomplete","ej.mobile.Autocomplete",{_setFirst:!0,_rootCSS:"e-m-ac",defaults:{renderMode:"auto",cssClass:"",watermarkText:null,minCharacter:1,enableMultiSelect:!1,caseSensitiveSearch:!1,enableDistinct:!1,enablePersistence:!1,enableAutoFill:!1,allowSorting:!0,templateId:"",popupHeight:"164px",popupWidth:"auto",itemsCount:0,sortOrder:"ascending",delimiterChar:",",emptyResultText:null,showEmptyResultText:!0,filterType:"startswith",mode:"default",value:"",enabled:!0,dataSource:[],fields:{text:null,key:null,image:null},locale:"en-US",touchEnd:null,keyPress:null,select:null,change:null,focusIn:null,focusOut:null},dataTypes:{renderMode:"enum",filterType:"enum",sortOrder:"enum",minCharacter:"number",dataSource:"data",enableMultiSelect:"boolean",caseSensitiveSearch:"boolean",enableDistinct:"boolean",enablePersistence:"boolean",enableAutoFill:"boolean",allowSorting:"boolean",showEmptyResultText:"boolean",enabled:"boolean",itemsCount:"number",locale:"string"},observables:["value"],value:ej.util.valueFunction("value"),_init:function(){this._orgEle=this.element.clone();ej.setRenderMode(this);this._getLocalizedLabels();this.model.emptyResultText=ej.isNullOrUndefined(this.model.emptyResultText)?this._localizedLabels.emptyResultText:this.model.emptyResultText;this.model.watermarkText=ej.isNullOrUndefined(this.model.watermarkText)?this._localizedLabels.watermarkText:this.model.watermarkText;this._initialize();this._renderControl();this._wireEvents()},_initialize:function(){this._id=this.element.attr("id");this._noResultEle=ej.buildTag("li.e-m-lv-item e-m-emptyresult",ej.buildTag("a.e-m-lv-content",this.model.emptyResultText,{},{"data-ej-appajax":!1}));this._selectedData=[];this._hiddenVal=[];this.model.dataSource=eval(this.model.dataSource);this.model.value!=""&&(this._selectedData.push(this.model.value),this.element.val(this.model.value))},_getLocalizedLabels:function(){this._localizedLabels=ej.getLocalizedConstants(this.sfType,this.model.locale)},_renderControl:function(){this.element.addClass(this.model.cssClass);this._wrapper=ej.buildTag("div#"+this._id+"_wrapper.e-m-ac-wrapper").addClass("e-m-"+this.model.renderMode);this._wrapper=this.element.wrap(this._wrapper).parent();this._setWatermarkText(this.model.watermarkText);this._controlState(this.model.enabled);this._hiddenElement=ej.buildTag("input#"+this._id+"_hidden",{},{display:"none"},{name:this._id});this._accList=ej.buildTag("ul#"+this._id+"list.e-m-ac-list").appendTo(this._wrapper.append(this._hiddenElement));this._listWrapper=ej.buildTag("div.e-m-list-wrapper");this.model.mode==ej.mobile.Autocomplete.Mode.Search&&(this._search=ej.buildTag("div.e-m-icon-search"),this._clear=ej.buildTag("div.e-m-icon-close"),this._search.appendTo(this._wrapper.addClass("e-m-ac-search")),this._clear.appendTo(this._wrapper).hide());this._listWrapper.appendTo(this._wrapper).append(this._accList).hide()},_createDelegates:function(){this._focus=$.proxy(this._focusHandler,this);this._blur=$.proxy(this._blurHandler,this);this._keyPress=$.proxy(this._keyPressHandler,this);this._docTouchStart=$.proxy(this._docTouchStartHandler,this);this._docTouchEnd=$.proxy(this._docTouchEndHandler,this);this._clearClick=$.proxy(this._clearClickHandler,this)},_wireEvents:function(n){var t=$(document);this._createDelegates();ej.listenEvents([this.element,this.element,this.element,t,t],["focus","blur","keyup",ej.startEvent(),ej.endEvent()],[this._focus,this._blur,this._keyPress,this._docTouchStart,this._docTouchEnd],n);this.model.mode==ej.mobile.Autocomplete.Mode.Search&&ej.listenTouchEvent(this._clear,ej.endEvent(),this._clearClick,n)},_focusHandler:function(n){this.element.addClass("e-m-focus");var t=this.element.val().substring(this.element.val().length-2,this.element.val().length);t!=this.model.delimiterChar+" "&&this.model.enableMultiSelect&&this.element.val().length&&this.element.val(this.element.val()+this.model.delimiterChar+" ");this.model.focusIn&&this._trigger("focusIn",{event:n,isInteraction:!0})},_blurHandler:function(n){this.element.removeClass("e-m-focus");this.model.focusOut&&this._trigger("focusOut",{event:n,isInteraction:!0})},_keyPressHandler:function(n){var t=this.element.val().lastIndexOf(this.model.delimiterChar),i=t<0?this.element.val():this.element.val().substring(t+2);i.length>=this.model.minCharacter&&(dataQuery=ej.Query().where(this.model.fields.text,this.model.filterType,i,!this.model.caseSensitiveSearch),this._addQuery(dataQuery),this._renderSuggestionsList(dataQuery),this.model.enableAutoFill&&n.keyCode!=8&&this.model.filterType==ej.mobile.Autocomplete.FilterType.StartsWith&&!this._selectedData.length&&this._autoFill(),this.model.dataSource instanceof ej.DataManager||(this._listWrapper.show(),this._accList.children().length?this._checkItems():this.model.showEmptyResultText&&this._accList.append(this._noResultEle)));this.element.val()||this.clearText();this.model.keyPress&&this._trigger("keyPress",{event:n,isInteraction:!0})},_listTouchHandler:function(n){if(!$(n.event.target).closest(this._noResultEle).length){var t=this.model.templateId!=""?n.data.text:n.text,r=n.item.attr("data-value"),i=r?r:t;this.model.enableMultiSelect?(n.isChecked?$.inArray(t,this._selectedData)==-1&&(this._selectedData.push(t),this._hiddenVal.push(i)):(this._selectedData=jQuery.grep(this._selectedData,function(n){return n!=t}),this._hiddenVal=jQuery.grep(this._hiddenVal,function(n){return n!=i})),this.element.val(this._setDelimiterChar(this._selectedData)),this._hiddenElement.attr("value",this._hiddenVal.toString()),this.value(this._setDelimiterChar(this._selectedData))):(this.element.val(t),this.value(t),this._hiddenElement.attr("value",i),this._selectedData=[],this._hiddenVal=[],this._selectedData.push(t),this._listWrapper.hide());this.element.blur();this.model.select&&this._trigger("select",n);this.model.touchEnd&&this._trigger("touchEnd",n);this.model.change&&this._trigger("change",n)}this._accList.find(".e-m-emptyresult").removeClass("e-m-lv-selected e-m-lv-checked")},_onComplete:function(){this.model.enableDistinct&&this._distinctList();this.element.val()&&this._listWrapper.show();this._accList.children().length?this._checkItems():this.model.showEmptyResultText&&this._accList.append(this._noResultEle);this._renderScroll()},_clearClickHandler:function(){this.clearText()},_docTouchStartHandler:function(n){this._isTargetDropDown(n.target)||(this._hide=!0)},_docTouchEndHandler:function(evt){!this._isTargetDropDown(evt.target)&&this._hide&&(this._listWrapper.hide(),this.element.removeClass("e-m-focus"),this.model.enableMultiSelect&&this.element.val(this.element.val().replace(eval("/"+this.model.delimiterChar+"\\s*$/"),"")),this._hide=!1)},_isTargetDropDown:function(n){return $(n).closest(this._wrapper).length},_addQuery:function(n){if(this.model.allowSorting){var t=this.model.sortOrder==ej.mobile.Autocomplete.SortOrder.Descending?!0:!1;n.sortBy(this.model.fields.text?this.model.fields.text:"",t)}this.model.itemsCount>0&&n.take(this.model.itemsCount)},_renderSuggestionsList:function(n){var t={renderMode:this.model.renderMode,dataSource:this.model.dataSource,query:n,templateId:this.model.templateId,touchEnd:$.proxy(this._listTouchHandler,this),enableChecklist:this.model.enableMultiSelect,fields:{text:this.model.fields.text,image:this.model.fields.image,value:this.model.fields.key},actionSuccess:$.proxy(this._onComplete,this)};this._accList.ejmListView(t);this._listWrapper.hide();this.model.enableDistinct&&this._distinctList();this._renderScroll();this.model.mode==ej.mobile.Autocomplete.Mode.Search&&this._clear.show()},_renderScroll:function(){this._scrollDestroy();var t=this._listWrapper.height(),r=window.innerHeight,n=parseInt(this.model.popupHeight),i=t>n?n:t,u=this.element.offset().top+this.element.height();n<t&&this._listWrapper.ejmScrollPanel({renderMode:this.model.renderMode,targetHeight:n});u+i>r&&this._listWrapper.css("top",-i);this.model.popupWidth!="auto"&&this._listWrapper.css("width",this.model.popupWidth)},_scrollDestroy:function(){var n=this._wrapper.find(".e-m-scroll");n.length&&(n.ejmScrollPanel("destroy"),this._listWrapper.addClass("e-m-list-wrapper"))},_autoFill:function(){var n=this._accList.ejmListView("getTextByIndex",0);n.length&&typeof n=="string"?this._setInputText(n):this.model.showEmptyResultText&&this._accList.append(this._noResultEle)},_distinctList:function(){var n=this._accList.data("ejmListView"),t=ej.dataUtil.distinct(n.model.dataSource,this.model.fields.text,!0);this._accList.ejmListView({dataSource:t});this._listWrapper.hide()},_setDelimiterChar:function(n){var t=n.toString();return t.replace(/,/g,this.model.delimiterChar+" ")},_getDelimiterIndex:function(){return this.element.val().lastIndexOf(this.model.delimiterChar)},_setInputText:function(n){var i=this._getDelimiterIndex()+1,r=this.element.val(),u=this.element[0].selectionStart,t;this.element.val(r.substring(0,i)+n);this.element[0].selectionStart=u;t=this.element[0].selectionEnd;this.element[0].selectionEnd=t},_setWatermarkText:function(n){n?this.element.attr("placeholder",n):this.element.removeAttr("placeholder")},_controlState:function(n){this._wrapper[n?"removeClass":"addClass"]("e-m-state-disabled")},_checkItems:function(){var n,t,r;if(this.model.enableMultiSelect){for(n=this._accList.data("ejmListView"),t=[],i=0;i<this._selectedData.length;i++)for(j=0;j<n.model.dataSource.length;j++)r=this.model.fields.text?n.model.dataSource[j][this.model.fields.text]:n.model.dataSource[j],this._selectedData[i]==r&&t.push(j);n.checkItemsByIndices(t)}},enable:function(){this.element.removeClass("e-m-state-disabled")},disable:function(){this.element.addClass("e-m-state-disabled")},getValue:function(){return this.element.val()},clearText:function(){this.element.val("");this.value("");this._selectedData=[];this._hiddenVal=[];this.model.mode==ej.mobile.Autocomplete.Mode.Search&&this._clear.hide();this._listWrapper.hide()},getSelectedItems:function(){return this._selectedData},_setModel:function(n){var t,i;if(!this.model.enabled&&ej.isNullOrUndefined(n.enabled))return!1;for(i in n)switch(i){case"renderMode":this._setRenderMode(n.renderMode);break;case"delimiterChar":this.model.enableMultiSelect&&this._setDelimiterChar(this._text);break;case"enabled":this._controlState(this.model.enabled);break;case"watermarkText":this._setWatermarkText(this.model.watermarkText);break;case"value":this._setValue(this.model.value);break;case"emptyResultText":this._setEmptyRestultText();break;case"locale":this._setLocale(n.locale);break;default:t=!0}t&&this._refresh();t=!1},_setValue:function(n){this.element.prop("disabled")||(this.element.val(n),this.value(n))},_setEmptyRestultText:function(){this._noResultEle.find("a.e-m-lv-content").html(this.model.emptyResultText)},_setLocale:function(){this._getLocalizedLabels();this.model.emptyResultText=this._localizedLabels.emptyResultText;this.model.watermarkText=this._localizedLabels.watermarkText;this._setWatermarkText();this._setEmptyRestultText()},_refresh:function(){this._destroy();this.element.addClass(this._rootCSS);this._renderControl();this._wireEvents()},_clearElement:function(){this.element.removeAttr("class").removeAttr("style");this._orgEle.insertBefore(this._wrapper);this._wrapper.remove()},_destroy:function(){this._wireEvents(!0);this._clearElement()}});ej.mobile.Autocomplete.FilterType={StartsWith:"startswith",Contains:"contains"};ej.mobile.Autocomplete.Mode={Search:"search",Default:"default"};ej.mobile.Autocomplete.SortOrder={Ascending:"ascending",Descending:"descending"};ej.mobile.Autocomplete.Locale=ej.mobile.Autocomplete.Locale||{};ej.mobile.Autocomplete.Locale["default"]=ej.mobile.Autocomplete.Locale["en-US"]={watermarkText:null,emptyResultText:"No suggestions"}})(jQuery,Syncfusion)});
