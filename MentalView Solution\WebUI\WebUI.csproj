﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{0C760E1B-5D59-4BB3-A77B-0376F8CF2D6A}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>WebUI</RootNamespace>
    <AssemblyName>WebUI</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44331</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AspNet.ScriptManager.bootstrap, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AspNet.ScriptManager.bootstrap.4.6.0\lib\net45\AspNet.ScriptManager.bootstrap.dll</HintPath>
    </Reference>
    <Reference Include="AspNet.ScriptManager.jQuery, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AspNet.ScriptManager.jQuery.3.6.0\lib\net45\AspNet.ScriptManager.jQuery.dll</HintPath>
    </Reference>
    <Reference Include="C1.C1Report.2, Version=2.6.20093.53207, Culture=neutral, PublicKeyToken=594a0605db190bb9, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Compression.Base, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>..\packages\Syncfusion.AspNet.20.3.0.59\lib\net46\Syncfusion.Compression.Base.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.DocIO.Base, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>..\packages\Syncfusion.DocIO.AspNet.20.3.0.59\lib\net46\Syncfusion.DocIO.Base.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.DocToPdfConverter.Base, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>..\packages\Syncfusion.DocToPDFConverter.AspNet.20.3.0.59\lib\net46\Syncfusion.DocToPdfConverter.Base.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.EJ, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>..\packages\Syncfusion.Web.Base.20.3.0.59\lib\net46\Syncfusion.EJ.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.EJ.Export, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>..\packages\Syncfusion.Web.Base.20.3.0.59\lib\net46\Syncfusion.EJ.Export.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.EJ.PdfViewer, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>..\packages\Syncfusion.AspNet.PdfViewer.20.3.0.59\lib\net46\Syncfusion.EJ.PdfViewer.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.EJ.Web, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>..\packages\Syncfusion.AspNet.20.3.0.59\lib\net46\Syncfusion.EJ.Web.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Licensing, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=632609b4d040f6b4, processorArchitecture=MSIL">
      <HintPath>..\packages\Syncfusion.Licensing.20.3.0.59\lib\net46\Syncfusion.Licensing.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Linq.Base, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>..\packages\Syncfusion.Web.Base.20.3.0.59\lib\net46\Syncfusion.Linq.Base.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.OfficeChart.Base, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>..\packages\Syncfusion.AspNet.20.3.0.59\lib\net46\Syncfusion.OfficeChart.Base.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Pdf.Base, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>..\packages\Syncfusion.Pdf.AspNet.20.3.0.59\lib\net46\Syncfusion.Pdf.Base.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.Presentation.Base, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>..\packages\Syncfusion.Presentation.AspNet.20.3.0.59\lib\net46\Syncfusion.Presentation.Base.dll</HintPath>
    </Reference>
    <Reference Include="Syncfusion.XlsIO.Base, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89, processorArchitecture=MSIL">
      <HintPath>..\packages\Syncfusion.XlsIO.AspNet.20.3.0.59\lib\net46\Syncfusion.XlsIO.Base.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.2.0.1\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ScriptManager.MSAjax">
      <HintPath>..\packages\Microsoft.AspNet.ScriptManager.MSAjax.5.0.0\lib\net45\Microsoft.ScriptManager.MSAjax.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ScriptManager.WebForms">
      <HintPath>..\packages\Microsoft.AspNet.ScriptManager.WebForms.5.0.0\lib\net45\Microsoft.ScriptManager.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.*******\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Web.Optimization.WebForms">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.WebForms.1.1.3\lib\net45\Microsoft.AspNet.Web.Optimization.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.FriendlyUrls">
      <HintPath>..\packages\Microsoft.AspNet.FriendlyUrls.Core.1.0.2\lib\net45\Microsoft.AspNet.FriendlyUrls.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Scripts\speech-to-text.js" />
    <Content Include="Links.aspx" />
    <Content Include="Partners.aspx" />
    <Content Include="Content\images\loading.gif" />
    <Content Include="Content\tinymce\icons\default\icons.min.js" />
    <Content Include="Content\tinymce\license.txt" />
    <Content Include="Content\tinymce\models\dom\model.min.js" />
    <Content Include="Content\tinymce\plugins\accordion\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\advlist\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\anchor\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\autolink\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\autoresize\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\autosave\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\charmap\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\codesample\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\code\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\directionality\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\emoticons\js\emojiimages.js" />
    <Content Include="Content\tinymce\plugins\emoticons\js\emojiimages.min.js" />
    <Content Include="Content\tinymce\plugins\emoticons\js\emojis.js" />
    <Content Include="Content\tinymce\plugins\emoticons\js\emojis.min.js" />
    <Content Include="Content\tinymce\plugins\emoticons\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\fullscreen\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\ar.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\bg_BG.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\ca.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\cs.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\da.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\de.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\el.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\en.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\es.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\eu.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\fa.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\fi.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\fr_FR.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\he_IL.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\hi.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\hr.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\hu_HU.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\id.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\it.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\ja.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\kk.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\ko_KR.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\ms.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\nb_NO.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\nl.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\pl.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\pt_BR.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\pt_PT.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\ro.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\ru.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\sk.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\sl_SI.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\sv_SE.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\th_TH.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\tr.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\uk.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\vi.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\zh_CN.js" />
    <Content Include="Content\tinymce\plugins\help\js\i18n\keynav\zh_TW.js" />
    <Content Include="Content\tinymce\plugins\help\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\image\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\importcss\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\insertdatetime\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\link\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\lists\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\media\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\nonbreaking\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\pagebreak\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\preview\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\quickbars\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\save\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\searchreplace\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\table\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\template\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\visualblocks\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\visualchars\plugin.min.js" />
    <Content Include="Content\tinymce\plugins\wordcount\plugin.min.js" />
    <Content Include="Content\tinymce\skins\content\dark\content.min.css" />
    <Content Include="Content\tinymce\skins\content\default\content.min.css" />
    <Content Include="Content\tinymce\skins\content\document\content.min.css" />
    <Content Include="Content\tinymce\skins\content\tinymce-5-dark\content.min.css" />
    <Content Include="Content\tinymce\skins\content\tinymce-5\content.min.css" />
    <Content Include="Content\tinymce\skins\content\writer\content.min.css" />
    <Content Include="Content\tinymce\skins\ui\oxide-dark\content.inline.min.css" />
    <Content Include="Content\tinymce\skins\ui\oxide-dark\content.min.css" />
    <Content Include="Content\tinymce\skins\ui\oxide-dark\skin.min.css" />
    <Content Include="Content\tinymce\skins\ui\oxide-dark\skin.shadowdom.min.css" />
    <Content Include="Content\tinymce\skins\ui\oxide\content.inline.min.css" />
    <Content Include="Content\tinymce\skins\ui\oxide\content.min.css" />
    <Content Include="Content\tinymce\skins\ui\oxide\skin.min.css" />
    <Content Include="Content\tinymce\skins\ui\oxide\skin.shadowdom.min.css" />
    <Content Include="Content\tinymce\skins\ui\tinymce-5-dark\content.inline.min.css" />
    <Content Include="Content\tinymce\skins\ui\tinymce-5-dark\content.min.css" />
    <Content Include="Content\tinymce\skins\ui\tinymce-5-dark\skin.min.css" />
    <Content Include="Content\tinymce\skins\ui\tinymce-5-dark\skin.shadowdom.min.css" />
    <Content Include="Content\tinymce\skins\ui\tinymce-5\content.inline.min.css" />
    <Content Include="Content\tinymce\skins\ui\tinymce-5\content.min.css" />
    <Content Include="Content\tinymce\skins\ui\tinymce-5\skin.min.css" />
    <Content Include="Content\tinymce\skins\ui\tinymce-5\skin.shadowdom.min.css" />
    <Content Include="Content\tinymce\themes\silver\theme.min.js" />
    <Content Include="Content\tinymce\tinymce.min.js" />
    <Content Include="HtmlPage1.html" />
    <Content Include="Notifications.aspx" />
    <Content Include="Scripts\ej\ej.globalize.min.js" />
    <Content Include="Scripts\ej\ej.unobtrusive.min.js" />
    <Content Include="Scripts\ej\ej.web.all.min.js" />
    <Content Include="Scripts\ej\ej.webform.min.js" />
    <Content Include="Scripts\ej\ej.widget.angular.min.js" />
    <Content Include="Scripts\ej\ej.widget.ko.min.js" />
    <Content Include="SubmitQuestionnaireForm.aspx" />
    <Content Include="Task.aspx" />
    <Content Include="Contact.aspx" />
    <Content Include="Contacts.aspx" />
    <Content Include="Content\AdminLTE\AdminLTE.min.css" />
    <Content Include="Content\AdminLTE\skins\skin-black-light.css" />
    <Content Include="Content\AdminLTE\skins\skin-black-light.min.css" />
    <Content Include="Content\AdminLTE\skins\skin-black.css" />
    <Content Include="Content\AdminLTE\skins\skin-black.min.css" />
    <Content Include="Content\AdminLTE\skins\skin-blue-light.css" />
    <Content Include="Content\AdminLTE\skins\skin-blue-light.min.css" />
    <Content Include="Content\AdminLTE\skins\skin-blue.css" />
    <Content Include="Content\AdminLTE\skins\skin-blue.min.css" />
    <Content Include="Content\AdminLTE\skins\skin-green-light.css" />
    <Content Include="Content\AdminLTE\skins\skin-green-light.min.css" />
    <Content Include="Content\AdminLTE\skins\skin-green.css" />
    <Content Include="Content\AdminLTE\skins\skin-green.min.css" />
    <Content Include="Content\AdminLTE\skins\skin-purple-light.css" />
    <Content Include="Content\AdminLTE\skins\skin-purple-light.min.css" />
    <Content Include="Content\AdminLTE\skins\skin-purple.css" />
    <Content Include="Content\AdminLTE\skins\skin-purple.min.css" />
    <Content Include="Content\AdminLTE\skins\skin-red-light.css" />
    <Content Include="Content\AdminLTE\skins\skin-red-light.min.css" />
    <Content Include="Content\AdminLTE\skins\skin-red.css" />
    <Content Include="Content\AdminLTE\skins\skin-red.min.css" />
    <Content Include="Content\AdminLTE\skins\skin-yellow-light.css" />
    <Content Include="Content\AdminLTE\skins\skin-yellow-light.min.css" />
    <Content Include="Content\AdminLTE\skins\skin-yellow.css" />
    <Content Include="Content\AdminLTE\skins\skin-yellow.min.css" />
    <Content Include="Content\AdminLTE\skins\_all-skins.css" />
    <Content Include="Content\AdminLTE\skins\_all-skins.min.css" />
    <Content Include="Content\bootstrap-grid.css" />
    <Content Include="Content\bootstrap-grid.min.css" />
    <Content Include="Content\bootstrap-reboot.css" />
    <Content Include="Content\bootstrap-reboot.min.css" />
    <Content Include="Content\bootstrap-switch.min.css" />
    <Content Include="Content\bootstrap-theme.css" />
    <Content Include="Content\bootstrap-theme.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Content\ej\mobile\android-theme\ej.mobile.android-core.css" />
    <Content Include="Content\ej\mobile\android-theme\ej.mobile.android-core.min.css" />
    <Content Include="Content\ej\mobile\android-theme\ej.mobile.android.css" />
    <Content Include="Content\ej\mobile\android-theme\ej.mobile.android.min.css" />
    <Content Include="Content\ej\mobile\ej.mobile.all.css" />
    <Content Include="Content\ej\mobile\ej.mobile.all.min.css" />
    <Content Include="Content\ej\mobile\flat-theme\ej.mobile.flat-core.css" />
    <Content Include="Content\ej\mobile\flat-theme\ej.mobile.flat-core.min.css" />
    <Content Include="Content\ej\mobile\flat-theme\ej.mobile.flat-theme.css" />
    <Content Include="Content\ej\mobile\flat-theme\ej.mobile.flat-theme.min.css" />
    <Content Include="Content\ej\mobile\images\ejmmobilefonts.svg" />
    <Content Include="Content\ej\mobile\images\PullToRefresh\PtR_android_dark.GIF" />
    <Content Include="Content\ej\mobile\images\PullToRefresh\PtR_android_light.GIF" />
    <Content Include="Content\ej\mobile\images\PullToRefresh\PtR_ios7_dark.gif" />
    <Content Include="Content\ej\mobile\images\PullToRefresh\PtR_ios7_light.gif" />
    <Content Include="Content\ej\mobile\images\PullToRefresh\PtR_windows.GIF" />
    <Content Include="Content\ej\mobile\images\PullToRefresh\PtR_winrt.GIF" />
    <Content Include="Content\ej\mobile\images\radialmenu\androidback.png" />
    <Content Include="Content\ej\mobile\images\radialmenu\androidradial.png" />
    <Content Include="Content\ej\mobile\images\radialmenu\flatback.png" />
    <Content Include="Content\ej\mobile\images\radialmenu\flatradial.png" />
    <Content Include="Content\ej\mobile\images\radialmenu\ios7back.png" />
    <Content Include="Content\ej\mobile\images\radialmenu\ios7radial.png" />
    <Content Include="Content\ej\mobile\images\radialmenu\windowsback.png" />
    <Content Include="Content\ej\mobile\images\radialmenu\windowsradial.png" />
    <Content Include="Content\ej\mobile\images\scrollpanel\pullarrow.png" />
    <Content Include="Content\ej\mobile\images\scrollpanel\refreshing-ios-light.gif" />
    <Content Include="Content\ej\mobile\images\waitingpopup\waitingpopup_android.GIF" />
    <Content Include="Content\ej\mobile\images\waitingpopup\waitingpopup_ios7.gif" />
    <Content Include="Content\ej\mobile\images\waitingpopup\waitingpopup_windows.GIF" />
    <Content Include="Content\ej\mobile\images\waitingpopup\waitingpopup_winrt.GIF" />
    <Content Include="Content\ej\mobile\ios7-theme\ej.mobile.ios7-core.css" />
    <Content Include="Content\ej\mobile\ios7-theme\ej.mobile.ios7-core.min.css" />
    <Content Include="Content\ej\mobile\ios7-theme\ej.mobile.ios7.css" />
    <Content Include="Content\ej\mobile\ios7-theme\ej.mobile.ios7.min.css" />
    <Content Include="Content\ej\mobile\windows-theme\ej.mobile.windows-core.css" />
    <Content Include="Content\ej\mobile\windows-theme\ej.mobile.windows-core.min.css" />
    <Content Include="Content\ej\mobile\windows-theme\ej.mobile.windows.css" />
    <Content Include="Content\ej\mobile\windows-theme\ej.mobile.windows.min.css" />
    <Content Include="Content\ej\web\bootstrap-theme\ej.theme.css" />
    <Content Include="Content\ej\web\bootstrap-theme\ej.theme.min.css" />
    <Content Include="Content\ej\web\bootstrap-theme\ej.web.all.min.css" />
    <Content Include="Content\ej\web\bootstrap-theme\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\bootstrap-theme\images\checkedtick.png" />
    <Content Include="Content\ej\web\bootstrap-theme\images\drop-sibling.png" />
    <Content Include="Content\ej\web\bootstrap-theme\images\rating-star.png" />
    <Content Include="Content\ej\web\bootstrap-theme\images\rotator-icon.png" />
    <Content Include="Content\ej\web\bootstrap-theme\images\slider-tick.png" />
    <Content Include="Content\ej\web\bootstrap-theme\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\common-images\darktheme\colorpicker_dark.png" />
    <Content Include="Content\ej\web\common-images\diagram\diagram_context.png" />
    <Content Include="Content\ej\web\common-images\diagram\diagram_context1.png" />
    <Content Include="Content\ej\web\common-images\diagram\Rotate.cur" />
    <Content Include="Content\ej\web\common-images\documenteditor\cellbottomalignment.svg" />
    <Content Include="Content\ej\web\common-images\documenteditor\cellcenteralignment.svg" />
    <Content Include="Content\ej\web\common-images\documenteditor\celltopalignment.svg" />
    <Content Include="Content\ej\web\common-images\documenteditor\tablecenteralignment.svg" />
    <Content Include="Content\ej\web\common-images\documenteditor\tableleftalignment.svg" />
    <Content Include="Content\ej\web\common-images\documenteditor\tablerightalignment.svg" />
    <Content Include="Content\ej\web\common-images\ejicons.svg" />
    <Content Include="Content\ej\web\common-images\fileexplorer\file_icons.png" />
    <Content Include="Content\ej\web\common-images\grid\dropPointer.cur" />
    <Content Include="Content\ej\web\common-images\kanban\nouser.svg" />
    <Content Include="Content\ej\web\common-images\lighttheme\colorpicker_light.png" />
    <Content Include="Content\ej\web\common-images\maps\Arrow_Bootom.png" />
    <Content Include="Content\ej\web\common-images\maps\Arrow_Bootom_hover.png" />
    <Content Include="Content\ej\web\common-images\maps\Arrow_Left.png" />
    <Content Include="Content\ej\web\common-images\maps\Arrow_Left_hover.png" />
    <Content Include="Content\ej\web\common-images\maps\Arrow_Right.png" />
    <Content Include="Content\ej\web\common-images\maps\Arrow_Right_hover.png" />
    <Content Include="Content\ej\web\common-images\maps\Arrow_Top.png" />
    <Content Include="Content\ej\web\common-images\maps\Arrow_Top_hover.png" />
    <Content Include="Content\ej\web\common-images\maps\Bar.png" />
    <Content Include="Content\ej\web\common-images\maps\Home.png" />
    <Content Include="Content\ej\web\common-images\maps\Home_hover.png" />
    <Content Include="Content\ej\web\common-images\maps\Home_hover_lime.png" />
    <Content Include="Content\ej\web\common-images\maps\Home_hover_saffron.png" />
    <Content Include="Content\ej\web\common-images\maps\Home_lime.png" />
    <Content Include="Content\ej\web\common-images\maps\Home_saffron.png" />
    <Content Include="Content\ej\web\common-images\maps\Navicatior.png" />
    <Content Include="Content\ej\web\common-images\maps\Zoom-In.png" />
    <Content Include="Content\ej\web\common-images\maps\Zoom-In_hover.png" />
    <Content Include="Content\ej\web\common-images\maps\Zoom-out.png" />
    <Content Include="Content\ej\web\common-images\maps\Zoom-out_hover.png" />
    <Content Include="Content\ej\web\common-images\materialtheme\colorpicker_light_material.png" />
    <Content Include="Content\ej\web\common-images\mediaplayer\basic_view_toolbar_bg.png" />
    <Content Include="Content\ej\web\common-images\mediaplayer\header_bg.png" />
    <Content Include="Content\ej\web\common-images\mediaplayer\thumbnail.png" />
    <Content Include="Content\ej\web\common-images\mediaplayer\waiting_popup.gif" />
    <Content Include="Content\ej\web\common-images\navigationdrawer\icons.svg" />
    <Content Include="Content\ej\web\common-images\pdfviewer\preloader-pdfviewer.gif" />
    <Content Include="Content\ej\web\common-images\pivotgrid\circle.png" />
    <Content Include="Content\ej\web\common-images\pivotgrid\diamond.png" />
    <Content Include="Content\ej\web\common-images\pivotgrid\down-arrow.png" />
    <Content Include="Content\ej\web\common-images\pivotgrid\green.png" />
    <Content Include="Content\ej\web\common-images\pivotgrid\red.png" />
    <Content Include="Content\ej\web\common-images\pivotgrid\right-arrow.png" />
    <Content Include="Content\ej\web\common-images\pivotgrid\three-color.png" />
    <Content Include="Content\ej\web\common-images\pivotgrid\triangle.png" />
    <Content Include="Content\ej\web\common-images\pivotgrid\up-arrow.png" />
    <Content Include="Content\ej\web\common-images\radialmenu\back.png" />
    <Content Include="Content\ej\web\common-images\radialmenu\radial.png" />
    <Content Include="Content\ej\web\common-images\ribbon\Blank.png" />
    <Content Include="Content\ej\web\common-images\ribbon\Browser.png" />
    <Content Include="Content\ej\web\common-images\ribbon\Check.png" />
    <Content Include="Content\ej\web\common-images\ribbon\Diet.png" />
    <Content Include="Content\ej\web\common-images\ribbon\ejrbnicons.svg" />
    <Content Include="Content\ej\web\common-images\ribbon\gallery.png" />
    <Content Include="Content\ej\web\common-images\ribbon\Gantt.png" />
    <Content Include="Content\ej\web\common-images\ribbon\homegallery.png" />
    <Content Include="Content\ej\web\common-images\ribbon\paste.png" />
    <Content Include="Content\ej\web\common-images\ribbon\Project.png" />
    <Content Include="Content\ej\web\common-images\ribbon\Protect.png" />
    <Content Include="Content\ej\web\common-images\ribbon\Sales.png" />
    <Content Include="Content\ej\web\common-images\ribbon\style.gallery.png" />
    <Content Include="Content\ej\web\common-images\ribbon\User.jpg" />
    <Content Include="Content\ej\web\common-images\ribbon\Version.png" />
    <Content Include="Content\ej\web\common-images\schedule\schedule_priority.png" />
    <Content Include="Content\ej\web\common-images\spellcheck\highlight.png" />
    <Content Include="Content\ej\web\common-images\spreadsheet\blank.png" />
    <Content Include="Content\ej\web\common-images\spreadsheet\bluedatabar.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\bwrcolorscale.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\chart-sprites.png" />
    <Content Include="Content\ej\web\common-images\spreadsheet\charttab-sprites.png" />
    <Content Include="Content\ej\web\common-images\spreadsheet\charttype-sprites.png" />
    <Content Include="Content\ej\web\common-images\spreadsheet\cross.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\downarrow.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\downtriangle.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\down_arrow_dark.cur" />
    <Content Include="Content\ej\web\common-images\spreadsheet\down_arrow_light.cur" />
    <Content Include="Content\ej\web\common-images\spreadsheet\draw_border_dark.cur" />
    <Content Include="Content\ej\web\common-images\spreadsheet\draw_border_grid_dark.cur" />
    <Content Include="Content\ej\web\common-images\spreadsheet\draw_border_grid_light.cur" />
    <Content Include="Content\ej\web\common-images\spreadsheet\draw_border_light.cur" />
    <Content Include="Content\ej\web\common-images\spreadsheet\drystar.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\ejxlicons.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\exclam.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\format_painter_dark.cur" />
    <Content Include="Content\ej\web\common-images\spreadsheet\format_painter_light.cur" />
    <Content Include="Content\ej\web\common-images\spreadsheet\fullstar.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\greenarrow.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\greencircle.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\greencircleindi.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\greendatabar.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\greenflag.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\gwcolorscale.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\gwrcolorscale.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\gycolorscale.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\gyrcolorscale.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\halfstar.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\lightbluedatabar.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\midarrow.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\midtriangle.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\orangedatabar.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\purpledatabar.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\redarrow.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\redcircle.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\redcircleindi.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\reddatabar.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\redflag.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\redsquare.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\right_arrow_dark.cur" />
    <Content Include="Content\ej\web\common-images\spreadsheet\right_arrow_light.cur" />
    <Content Include="Content\ej\web\common-images\spreadsheet\rimgreencircle.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\rimredcircle.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\rimyellowcircle.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\rwbcolorscale.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\rwcolorscale.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\rwgcolorscale.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\rygcolorscale.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\sprites.png" />
    <Content Include="Content\ej\web\common-images\spreadsheet\tick.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\uparrow.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\uptriangle.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\wgcolorscale.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\wrcolorscale.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\yellowarrow.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\yellowcircle.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\yellowcircleindi.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\yellowflag.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\yellowtri.svg" />
    <Content Include="Content\ej\web\common-images\spreadsheet\ygcolorscale.svg" />
    <Content Include="Content\ej\web\common-images\treeview\drop-sibling-rtl.png" />
    <Content Include="Content\ej\web\default-theme\ej.theme.css" />
    <Content Include="Content\ej\web\default-theme\ej.theme.min.css" />
    <Content Include="Content\ej\web\default-theme\ej.web.all.min.css" />
    <Content Include="Content\ej\web\default-theme\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\default-theme\images\checkedtick.png" />
    <Content Include="Content\ej\web\default-theme\images\drop-sibling.png" />
    <Content Include="Content\ej\web\default-theme\images\rating-star.png" />
    <Content Include="Content\ej\web\default-theme\images\rotator-icon.png" />
    <Content Include="Content\ej\web\default-theme\images\slider-tick.png" />
    <Content Include="Content\ej\web\default-theme\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\ej.widgets.core.bootstrap.min.css" />
    <Content Include="Content\ej\web\ej.widgets.core.material.min.css" />
    <Content Include="Content\ej\web\ej.widgets.core.min.css" />
    <Content Include="Content\ej\web\ej.widgets.core.office-365.min.css" />
    <Content Include="Content\ej\web\flat-azure-dark\ej.theme.css" />
    <Content Include="Content\ej\web\flat-azure-dark\ej.theme.min.css" />
    <Content Include="Content\ej\web\flat-azure-dark\ej.web.all.min.css" />
    <Content Include="Content\ej\web\flat-azure-dark\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\flat-azure-dark\images\checkedtick.png" />
    <Content Include="Content\ej\web\flat-azure-dark\images\drop-sibling.png" />
    <Content Include="Content\ej\web\flat-azure-dark\images\rating-star.png" />
    <Content Include="Content\ej\web\flat-azure-dark\images\rotator-icon.png" />
    <Content Include="Content\ej\web\flat-azure-dark\images\slider-tick.png" />
    <Content Include="Content\ej\web\flat-azure-dark\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\flat-lime-dark\ej.theme.css" />
    <Content Include="Content\ej\web\flat-lime-dark\ej.theme.min.css" />
    <Content Include="Content\ej\web\flat-lime-dark\ej.web.all.min.css" />
    <Content Include="Content\ej\web\flat-lime-dark\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\flat-lime-dark\images\checkedtick.png" />
    <Content Include="Content\ej\web\flat-lime-dark\images\drop-sibling.png" />
    <Content Include="Content\ej\web\flat-lime-dark\images\rating-star.png" />
    <Content Include="Content\ej\web\flat-lime-dark\images\rotator-icon.png" />
    <Content Include="Content\ej\web\flat-lime-dark\images\slider-tick.png" />
    <Content Include="Content\ej\web\flat-lime-dark\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\flat-lime\ej.theme.css" />
    <Content Include="Content\ej\web\flat-lime\ej.theme.min.css" />
    <Content Include="Content\ej\web\flat-lime\ej.web.all.min.css" />
    <Content Include="Content\ej\web\flat-lime\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\flat-lime\images\checkedtick.png" />
    <Content Include="Content\ej\web\flat-lime\images\drop-sibling.png" />
    <Content Include="Content\ej\web\flat-lime\images\rating-star.png" />
    <Content Include="Content\ej\web\flat-lime\images\rotator-icon.png" />
    <Content Include="Content\ej\web\flat-lime\images\slider-tick.png" />
    <Content Include="Content\ej\web\flat-lime\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\flat-saffron-dark\ej.theme.css" />
    <Content Include="Content\ej\web\flat-saffron-dark\ej.theme.min.css" />
    <Content Include="Content\ej\web\flat-saffron-dark\ej.web.all.min.css" />
    <Content Include="Content\ej\web\flat-saffron-dark\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\flat-saffron-dark\images\checkedtick.png" />
    <Content Include="Content\ej\web\flat-saffron-dark\images\drop-sibling.png" />
    <Content Include="Content\ej\web\flat-saffron-dark\images\rating-star.png" />
    <Content Include="Content\ej\web\flat-saffron-dark\images\rotator-icon.png" />
    <Content Include="Content\ej\web\flat-saffron-dark\images\slider-tick.png" />
    <Content Include="Content\ej\web\flat-saffron-dark\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\flat-saffron\ej.theme.css" />
    <Content Include="Content\ej\web\flat-saffron\ej.theme.min.css" />
    <Content Include="Content\ej\web\flat-saffron\ej.web.all.min.css" />
    <Content Include="Content\ej\web\flat-saffron\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\flat-saffron\images\checkedtick.png" />
    <Content Include="Content\ej\web\flat-saffron\images\drop-sibling.png" />
    <Content Include="Content\ej\web\flat-saffron\images\rating-star.png" />
    <Content Include="Content\ej\web\flat-saffron\images\rotator-icon.png" />
    <Content Include="Content\ej\web\flat-saffron\images\slider-tick.png" />
    <Content Include="Content\ej\web\flat-saffron\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\gradient-azure-dark\ej.theme.css" />
    <Content Include="Content\ej\web\gradient-azure-dark\ej.theme.min.css" />
    <Content Include="Content\ej\web\gradient-azure-dark\ej.web.all.min.css" />
    <Content Include="Content\ej\web\gradient-azure-dark\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\gradient-azure-dark\images\checkedtick.png" />
    <Content Include="Content\ej\web\gradient-azure-dark\images\drop-sibling.png" />
    <Content Include="Content\ej\web\gradient-azure-dark\images\rating-star.png" />
    <Content Include="Content\ej\web\gradient-azure-dark\images\rotator-icon.png" />
    <Content Include="Content\ej\web\gradient-azure-dark\images\slider-tick.png" />
    <Content Include="Content\ej\web\gradient-azure-dark\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\gradient-azure\ej.theme.css" />
    <Content Include="Content\ej\web\gradient-azure\ej.theme.min.css" />
    <Content Include="Content\ej\web\gradient-azure\ej.web.all.min.css" />
    <Content Include="Content\ej\web\gradient-azure\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\gradient-azure\images\checkedtick.png" />
    <Content Include="Content\ej\web\gradient-azure\images\drop-sibling.png" />
    <Content Include="Content\ej\web\gradient-azure\images\rating-star.png" />
    <Content Include="Content\ej\web\gradient-azure\images\rotator-icon.png" />
    <Content Include="Content\ej\web\gradient-azure\images\slider-tick.png" />
    <Content Include="Content\ej\web\gradient-azure\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\gradient-lime-dark\ej.theme.css" />
    <Content Include="Content\ej\web\gradient-lime-dark\ej.theme.min.css" />
    <Content Include="Content\ej\web\gradient-lime-dark\ej.web.all.min.css" />
    <Content Include="Content\ej\web\gradient-lime-dark\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\gradient-lime-dark\images\checkedtick.png" />
    <Content Include="Content\ej\web\gradient-lime-dark\images\drop-sibling.png" />
    <Content Include="Content\ej\web\gradient-lime-dark\images\rating-star.png" />
    <Content Include="Content\ej\web\gradient-lime-dark\images\rotator-icon.png" />
    <Content Include="Content\ej\web\gradient-lime-dark\images\slider-tick.png" />
    <Content Include="Content\ej\web\gradient-lime-dark\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\gradient-lime\ej.theme.css" />
    <Content Include="Content\ej\web\gradient-lime\ej.theme.min.css" />
    <Content Include="Content\ej\web\gradient-lime\ej.web.all.min.css" />
    <Content Include="Content\ej\web\gradient-lime\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\gradient-lime\images\checkedtick.png" />
    <Content Include="Content\ej\web\gradient-lime\images\drop-sibling.png" />
    <Content Include="Content\ej\web\gradient-lime\images\rating-star.png" />
    <Content Include="Content\ej\web\gradient-lime\images\rotator-icon.png" />
    <Content Include="Content\ej\web\gradient-lime\images\slider-tick.png" />
    <Content Include="Content\ej\web\gradient-lime\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\gradient-saffron-dark\ej.theme.css" />
    <Content Include="Content\ej\web\gradient-saffron-dark\ej.theme.min.css" />
    <Content Include="Content\ej\web\gradient-saffron-dark\ej.web.all.min.css" />
    <Content Include="Content\ej\web\gradient-saffron-dark\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\gradient-saffron-dark\images\checkedtick.png" />
    <Content Include="Content\ej\web\gradient-saffron-dark\images\drop-sibling.png" />
    <Content Include="Content\ej\web\gradient-saffron-dark\images\rating-star.png" />
    <Content Include="Content\ej\web\gradient-saffron-dark\images\rotator-icon.png" />
    <Content Include="Content\ej\web\gradient-saffron-dark\images\slider-tick.png" />
    <Content Include="Content\ej\web\gradient-saffron-dark\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\gradient-saffron\ej.theme.css" />
    <Content Include="Content\ej\web\gradient-saffron\ej.theme.min.css" />
    <Content Include="Content\ej\web\gradient-saffron\ej.web.all.min.css" />
    <Content Include="Content\ej\web\gradient-saffron\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\gradient-saffron\images\checkedtick.png" />
    <Content Include="Content\ej\web\gradient-saffron\images\drop-sibling.png" />
    <Content Include="Content\ej\web\gradient-saffron\images\rating-star.png" />
    <Content Include="Content\ej\web\gradient-saffron\images\rotator-icon.png" />
    <Content Include="Content\ej\web\gradient-saffron\images\slider-tick.png" />
    <Content Include="Content\ej\web\gradient-saffron\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\high-contrast-01\ej.theme.css" />
    <Content Include="Content\ej\web\high-contrast-01\ej.theme.min.css" />
    <Content Include="Content\ej\web\high-contrast-01\ej.web.all.min.css" />
    <Content Include="Content\ej\web\high-contrast-01\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\high-contrast-01\images\checkedtick.png" />
    <Content Include="Content\ej\web\high-contrast-01\images\drop-sibling.png" />
    <Content Include="Content\ej\web\high-contrast-01\images\rating-star.png" />
    <Content Include="Content\ej\web\high-contrast-01\images\rotator-icon.png" />
    <Content Include="Content\ej\web\high-contrast-01\images\slider-tick.png" />
    <Content Include="Content\ej\web\high-contrast-01\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\high-contrast-02\ej.theme.css" />
    <Content Include="Content\ej\web\high-contrast-02\ej.theme.min.css" />
    <Content Include="Content\ej\web\high-contrast-02\ej.web.all.min.css" />
    <Content Include="Content\ej\web\high-contrast-02\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\high-contrast-02\images\checkedtick.png" />
    <Content Include="Content\ej\web\high-contrast-02\images\drop-sibling.png" />
    <Content Include="Content\ej\web\high-contrast-02\images\rating-star.png" />
    <Content Include="Content\ej\web\high-contrast-02\images\rotator-icon.png" />
    <Content Include="Content\ej\web\high-contrast-02\images\slider-tick.png" />
    <Content Include="Content\ej\web\high-contrast-02\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\material\ej.theme.css" />
    <Content Include="Content\ej\web\material\ej.theme.min.css" />
    <Content Include="Content\ej\web\material\ej.web.all.min.css" />
    <Content Include="Content\ej\web\material\icons\ejicons.svg" />
    <Content Include="Content\ej\web\material\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\material\images\checkedtick.png" />
    <Content Include="Content\ej\web\material\images\drop-sibling.png" />
    <Content Include="Content\ej\web\material\images\rating-star.png" />
    <Content Include="Content\ej\web\material\images\rotator-icon.png" />
    <Content Include="Content\ej\web\material\images\slider-tick.png" />
    <Content Include="Content\ej\web\material\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\office-365\ej.theme.css" />
    <Content Include="Content\ej\web\office-365\ej.theme.min.css" />
    <Content Include="Content\ej\web\office-365\ej.web.all.min.css" />
    <Content Include="Content\ej\web\office-365\icons\ejicons.svg" />
    <Content Include="Content\ej\web\office-365\images\ajax-loader.gif" />
    <Content Include="Content\ej\web\office-365\images\checkedtick.png" />
    <Content Include="Content\ej\web\office-365\images\drop-sibling.png" />
    <Content Include="Content\ej\web\office-365\images\Rating_Active.png" />
    <Content Include="Content\ej\web\office-365\images\Rating_Clear.png" />
    <Content Include="Content\ej\web\office-365\images\Rating_Default.png" />
    <Content Include="Content\ej\web\office-365\images\rotator-icon.png" />
    <Content Include="Content\ej\web\office-365\images\slider-tick.png" />
    <Content Include="Content\ej\web\office-365\images\waitingpopup.gif" />
    <Content Include="Content\ej\web\responsive-css\ej.responsive.css" />
    <Content Include="Content\ej\web\responsive-css\ejgrid.responsive.css" />
    <Content Include="Content\ej\web\ribbon-css\ej.icons.css" />
    <Content Include="Content\font-awesome-template\css\font-awesome.min.css" />
    <Content Include="Content\font-awesome-template\fonts\fontawesome-webfont.svg" />
    <Content Include="Content\font-awesome\css\all.min.css" />
    <Content Include="Content\images\MentalView logo big - ORIGINAL.png" />
    <Content Include="Content\images\MentalView logo big.png" />
    <Content Include="Content\images\MentalView logo compact.png" />
    <Content Include="Content\images\MentalView logo.png" />
    <Content Include="Content\select2.min.css" />
    <Content Include="Content\Site.css" />
    <Content Include="Reports.aspx" />
    <Content Include="Default.aspx" />
    <Content Include="favicon.ico" />
    <Content Include="Global.asax" />
    <Content Include="LocalizedResources\ej.culture.el-GR.min.js" />
    <Content Include="LocalizedResources\ej.culture.en-US.min.js" />
    <Content Include="LocalizedResources\ej.culture.es-ES.min.js" />
    <Content Include="LocalizedResources\ej.culture.fr-FR.min.js" />
    <Content Include="Login.aspx" />
    <Content Include="Notes.txt" />
    <Content Include="Reporting\ExportWordReport.aspx" />
    <Content Include="Reporting\MentalViewReports.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Scripts\AdminLTE\adminlte.min.js" />
    <Content Include="Scripts\angular.min.js" />
    <Content Include="Scripts\bootstrap-datepicker.el.js" />
    <Content Include="Scripts\bootstrap-datepicker.js" />
    <Content Include="Scripts\bootstrap-switch.min.js" />
    <Content Include="Scripts\bootstrap.bundle.js" />
    <Content Include="Scripts\bootstrap.bundle.min.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Scripts\bootstrap.touchspin.js" />
    <Content Include="Scripts\custom.js" />
    <Content Include="Scripts\ej\common\ej.core.min.js" />
    <Content Include="Scripts\ej\common\ej.data.min.js" />
    <Content Include="Scripts\ej\common\ej.draggable.min.js" />
    <Content Include="Scripts\ej\common\ej.globalize.min.js" />
    <Content Include="Scripts\ej\common\ej.scroller.min.js" />
    <Content Include="Scripts\ej\common\ej.touch.min.js" />
    <Content Include="Scripts\ej\common\ej.unobtrusive.min.js" />
    <Content Include="Scripts\ej\common\ej.web.react.min.js" />
    <Content Include="Scripts\ej\common\ej.webform.min.js" />
    <Content Include="Scripts\ej\common\ej.widget.angular.min.js" />
    <Content Include="Scripts\ej\common\ej.widget.ko.min.js" />
    <Content Include="Scripts\ej\datavisualization\ej.barcode.min.js" />
    <Content Include="Scripts\ej\datavisualization\ej.bulletgraph.min.js" />
    <Content Include="Scripts\ej\datavisualization\ej.chart.min.js" />
    <Content Include="Scripts\ej\datavisualization\ej.circulargauge.min.js" />
    <Content Include="Scripts\ej\datavisualization\ej.diagram.min.js" />
    <Content Include="Scripts\ej\datavisualization\ej.digitalgauge.min.js" />
    <Content Include="Scripts\ej\datavisualization\ej.gauge.min.js" />
    <Content Include="Scripts\ej\datavisualization\ej.heatmap.min.js" />
    <Content Include="Scripts\ej\datavisualization\ej.lineargauge.min.js" />
    <Content Include="Scripts\ej\datavisualization\ej.map.min.js" />
    <Content Include="Scripts\ej\datavisualization\ej.rangenavigator.min.js" />
    <Content Include="Scripts\ej\datavisualization\ej.sparkline.min.js" />
    <Content Include="Scripts\ej\datavisualization\ej.sunburstchart.min.js" />
    <Content Include="Scripts\ej\datavisualization\ej.treemap.min.js" />
    <Content Include="Scripts\bootstrap.min.js.map" />
    <Content Include="Scripts\bootstrap.js.map" />
    <Content Include="Scripts\bootstrap.bundle.min.js.map" />
    <Content Include="Scripts\bootstrap.bundle.js.map" />
    <Content Include="Content\bootstrap.min.css.map" />
    <Content Include="Content\bootstrap.css.map" />
    <Content Include="Content\bootstrap-reboot.min.css.map" />
    <Content Include="Content\bootstrap-reboot.css.map" />
    <Content Include="Content\bootstrap-grid.min.css.map" />
    <Content Include="Content\bootstrap-grid.css.map" />
    <Content Include="IncludeFiles.Master" />
    <Content Include="Content\bootstrap-theme.css.map" />
    <Content Include="Content\bootstrap-theme.min.css.map" />
    <Content Include="AppointmentReport.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="ContactReport.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Content\ej\web\office-365\icons\ejicons.woff" />
    <Content Include="Content\ej\web\office-365\icons\ejicons.ttf" />
    <Content Include="Content\ej\web\office-365\icons\ejicons.eot" />
    <Content Include="Content\ej\web\office-365\ej.theme.less" />
    <Content Include="Content\ej\web\material\icons\ejicons.woff" />
    <Content Include="Content\ej\web\material\icons\ejicons.ttf" />
    <Content Include="Content\ej\web\material\icons\ejicons.eot" />
    <Content Include="Content\ej\web\material\ej.theme.less" />
    <Content Include="Content\ej\web\high-contrast-02\ej.theme.less" />
    <Content Include="Content\ej\web\high-contrast-01\ej.theme.less" />
    <Content Include="Content\ej\web\gradient-saffron\ej.theme.less" />
    <Content Include="Content\ej\web\gradient-saffron-dark\ej.theme.less" />
    <Content Include="Content\ej\web\gradient-lime\ej.theme.less" />
    <Content Include="Content\ej\web\gradient-lime-dark\ej.theme.less" />
    <Content Include="Content\ej\web\gradient-azure\ej.theme.less" />
    <Content Include="Content\ej\web\gradient-azure-dark\ej.theme.less" />
    <Content Include="Content\ej\web\flat-saffron\ej.theme.less" />
    <Content Include="Content\ej\web\flat-saffron-dark\ej.theme.less" />
    <Content Include="Content\ej\web\flat-lime\ej.theme.less" />
    <Content Include="Content\ej\web\flat-lime-dark\ej.theme.less" />
    <Content Include="Content\ej\web\flat-azure-dark\ej.theme.less" />
    <Content Include="Content\ej\web\ej.widgets.core.office-365.less" />
    <Content Include="Content\ej\web\ej.widgets.core.material.less" />
    <Content Include="Content\ej\web\ej.widgets.core.less" />
    <Content Include="Content\ej\web\ej.widgets.core.bootstrap.less" />
    <Content Include="Content\ej\web\default-theme\ej.theme.less" />
    <Content Include="Content\ej\web\common-images\spreadsheet\ejxlicons.woff" />
    <Content Include="Content\ej\web\common-images\spreadsheet\ejxlicons.ttf" />
    <Content Include="Content\ej\web\common-images\spreadsheet\ejxlicons.eot" />
    <Content Include="Content\ej\web\common-images\ribbon\ejrbnicons.woff" />
    <Content Include="Content\ej\web\common-images\ribbon\ejrbnicons.ttf" />
    <Content Include="Content\ej\web\common-images\ribbon\ejrbnicons.eot" />
    <Content Include="Content\ej\web\common-images\navigationdrawer\icons.woff" />
    <Content Include="Content\ej\web\common-images\navigationdrawer\icons.ttf" />
    <Content Include="Content\ej\web\common-images\navigationdrawer\icons.eot" />
    <Content Include="Content\ej\web\common-images\ejicons.woff" />
    <Content Include="Content\ej\web\common-images\ejicons.ttf" />
    <Content Include="Content\ej\web\common-images\ejicons.eot" />
    <Content Include="Content\ej\web\bootstrap-theme\ej.theme.less" />
    <Content Include="Content\ej\mobile\images\ejmmobilefonts.woff" />
    <Content Include="Content\ej\mobile\images\ejmmobilefonts.ttf" />
    <Content Include="Content\ej\mobile\images\ejmmobilefonts.eot" />
    <Content Include="Content\tinymce\langs\README.md" />
    <Content Include="ContactReportNew.docx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Content\font-awesome\webfonts\fa-brands-400.ttf" />
    <Content Include="Content\font-awesome\webfonts\fa-brands-400.woff2" />
    <Content Include="Content\font-awesome\webfonts\fa-regular-400.ttf" />
    <Content Include="Content\font-awesome\webfonts\fa-regular-400.woff2" />
    <Content Include="Content\font-awesome\webfonts\fa-solid-900.ttf" />
    <Content Include="Content\font-awesome\webfonts\fa-solid-900.woff2" />
    <Content Include="Content\font-awesome\webfonts\fa-v4compatibility.ttf" />
    <Content Include="Content\font-awesome\webfonts\fa-v4compatibility.woff2" />
    <None Include="Scripts\ej\ej.web.all.min.intellisense.js" />
    <Content Include="Scripts\ej\i18n\ej.culture.ar-AE.min.js" />
    <Content Include="Scripts\ej\i18n\ej.culture.de-DE.min.js" />
    <Content Include="Scripts\ej\i18n\ej.culture.el-GR.min.js" />
    <Content Include="Scripts\ej\i18n\ej.culture.en-GB.min.js" />
    <Content Include="Scripts\ej\i18n\ej.culture.en-US.min.js" />
    <Content Include="Scripts\ej\i18n\ej.culture.es-ES.min.js" />
    <Content Include="Scripts\ej\i18n\ej.culture.fr-FR.min.js" />
    <Content Include="Scripts\ej\i18n\ej.culture.vi-VN.min.js" />
    <Content Include="Scripts\ej\i18n\ej.culture.zh-CN.min.js" />
    <Content Include="Scripts\ej\l10n\ej.localetexts.ar-AE.min.js" />
    <Content Include="Scripts\ej\l10n\ej.localetexts.de-DE.min.js" />
    <Content Include="Scripts\ej\l10n\ej.localetexts.el-GR.min.js" />
    <Content Include="Scripts\ej\l10n\ej.localetexts.en-GB.min.js" />
    <Content Include="Scripts\ej\l10n\ej.localetexts.en-US.min.js" />
    <Content Include="Scripts\ej\l10n\ej.localetexts.es-ES.min.js" />
    <Content Include="Scripts\ej\l10n\ej.localetexts.fr-FR.min.js" />
    <Content Include="Scripts\ej\l10n\ej.localetexts.vi-VN.min.js" />
    <Content Include="Scripts\ej\l10n\ej.localetexts.zh-CN.min.js" />
    <Content Include="Scripts\ej\l10n\reportdesigner\ej.localetexts.de-DE.min.js" />
    <Content Include="Scripts\ej\l10n\reportdesigner\ej.localetexts.en-US.min.js" />
    <Content Include="Scripts\ej\l10n\reportdesigner\ej.localetexts.es-ES.min.js" />
    <Content Include="Scripts\ej\l10n\reportdesigner\ej.localetexts.fr-FR.min.js" />
    <Content Include="Scripts\ej\l10n\reportdesigner\ej.localetexts.zh-CN.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.accordion.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.all.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.application.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.autocomplete.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.button.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.core.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.datepicker.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.dialog.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.dropdownlist.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.editor.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.groupbutton.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.inputbutton.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.listview.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.menu.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.navigationbar.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.progressbar.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.radialmenu.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.radialslider.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.rating.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.rotator.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.scrollpanel.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.slider.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.splitpane.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.tab.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.tile.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.timepicker.min.js" />
    <Content Include="Scripts\ej\mobile\ej.mobile.togglebutton.min.js" />
    <Content Include="Scripts\ej\web\ej.accordion.min.js" />
    <Content Include="Scripts\ej\web\ej.autocomplete.min.js" />
    <Content Include="Scripts\ej\web\ej.button.min.js" />
    <Content Include="Scripts\ej\web\ej.calculate.min.js" />
    <Content Include="Scripts\ej\web\ej.captcha.min.js" />
    <Content Include="Scripts\ej\web\ej.checkbox.min.js" />
    <Content Include="Scripts\ej\web\ej.colorpicker.min.js" />
    <Content Include="Scripts\ej\web\ej.combobox.min.js" />
    <Content Include="Scripts\ej\web\ej.datepicker.min.js" />
    <Content Include="Scripts\ej\web\ej.daterangepicker.min.js" />
    <Content Include="Scripts\ej\web\ej.datetimepicker.min.js" />
    <Content Include="Scripts\ej\web\ej.dialog.min.js" />
    <Content Include="Scripts\ej\web\ej.documenteditor.min.js" />
    <Content Include="Scripts\ej\web\ej.dropdownlist.min.js" />
    <Content Include="Scripts\ej\web\ej.dropdowntree.min.js" />
    <Content Include="Scripts\ej\web\ej.editor.min.js" />
    <Content Include="Scripts\ej\web\ej.excelfilter.min.js" />
    <Content Include="Scripts\ej\web\ej.fileexplorer.min.js" />
    <Content Include="Scripts\ej\web\ej.gantt.min.js" />
    <Content Include="Scripts\ej\web\ej.grid.min.js" />
    <Content Include="Scripts\ej\web\ej.groupbutton.min.js" />
    <Content Include="Scripts\ej\web\ej.kanban.min.js" />
    <Content Include="Scripts\ej\web\ej.listbox.min.js" />
    <Content Include="Scripts\ej\web\ej.listview.min.js" />
    <Content Include="Scripts\ej\web\ej.maskedit.min.js" />
    <Content Include="Scripts\ej\web\ej.mediaplayer.min.js" />
    <Content Include="Scripts\ej\web\ej.menu.min.js" />
    <Content Include="Scripts\ej\web\ej.navigationdrawer.min.js" />
    <Content Include="Scripts\ej\web\ej.olap.base.min.js" />
    <Content Include="Scripts\ej\web\ej.pager.min.js" />
    <Content Include="Scripts\ej\web\ej.pdfviewer.min.js" />
    <Content Include="Scripts\ej\web\ej.pivot.common.min.js" />
    <Content Include="Scripts\ej\web\ej.pivotanalysis.base.min.js" />
    <Content Include="Scripts\ej\web\ej.pivotchart.min.js" />
    <Content Include="Scripts\ej\web\ej.pivotclient.min.js" />
    <Content Include="Scripts\ej\web\ej.pivotgauge.min.js" />
    <Content Include="Scripts\ej\web\ej.pivotgrid.min.js" />
    <Content Include="Scripts\ej\web\ej.pivotpager.min.js" />
    <Content Include="Scripts\ej\web\ej.pivotschemadesigner.min.js" />
    <Content Include="Scripts\ej\web\ej.pivottreemap.min.js" />
    <Content Include="Scripts\ej\web\ej.print.min.js" />
    <Content Include="Scripts\ej\web\ej.progressbar.min.js" />
    <Content Include="Scripts\ej\web\ej.radialmenu.min.js" />
    <Content Include="Scripts\ej\web\ej.radialslider.min.js" />
    <Content Include="Scripts\ej\web\ej.radiobutton.min.js" />
    <Content Include="Scripts\ej\web\ej.rating.min.js" />
    <Content Include="Scripts\ej\web\ej.recurrenceeditor.min.js" />
    <Content Include="Scripts\ej\web\ej.ribbon.min.js" />
    <Content Include="Scripts\ej\web\ej.rotator.min.js" />
    <Content Include="Scripts\ej\web\ej.rte.min.js" />
    <Content Include="Scripts\ej\web\ej.schedule.min.js" />
    <Content Include="Scripts\ej\web\ej.signature.min.js" />
    <Content Include="Scripts\ej\web\ej.slider.min.js" />
    <Content Include="Scripts\ej\web\ej.spellcheck.min.js" />
    <Content Include="Scripts\ej\web\ej.splitbutton.min.js" />
    <Content Include="Scripts\ej\web\ej.splitter.min.js" />
    <Content Include="Scripts\ej\web\ej.spreadsheet.min.js" />
    <Content Include="Scripts\ej\web\ej.tab.min.js" />
    <Content Include="Scripts\ej\web\ej.tagcloud.min.js" />
    <Content Include="Scripts\ej\web\ej.tile.min.js" />
    <Content Include="Scripts\ej\web\ej.timepicker.min.js" />
    <Content Include="Scripts\ej\web\ej.togglebutton.min.js" />
    <Content Include="Scripts\ej\web\ej.toolbar.min.js" />
    <Content Include="Scripts\ej\web\ej.tooltip.min.js" />
    <Content Include="Scripts\ej\web\ej.treegrid.min.js" />
    <Content Include="Scripts\ej\web\ej.treeview.min.js" />
    <Content Include="Scripts\ej\web\ej.uploadbox.min.js" />
    <Content Include="Scripts\ej\web\ej.waitingpopup.min.js" />
    <Content Include="Scripts\ej\web\ej.web.all.min.js" />
    <Content Include="Scripts\esm\popper-utils.js" />
    <Content Include="Scripts\esm\popper-utils.min.js" />
    <Content Include="Scripts\esm\popper.js" />
    <Content Include="Scripts\esm\popper.min.js" />
    <Content Include="Scripts\fastclick.js" />
    <Content Include="Scripts\index.js.flow" />
    <Content Include="Scripts\esm\popper.min.js.map" />
    <Content Include="Scripts\esm\popper.js.map" />
    <Content Include="Scripts\esm\popper-utils.min.js.map" />
    <Content Include="Scripts\esm\popper-utils.js.map" />
    <None Include="Scripts\jquery-1.7.1-vsdoc.js" />
    <Content Include="Scripts\jquery-1.7.1.js" />
    <Content Include="Scripts\jquery-1.7.1.min.js" />
    <Content Include="Scripts\jquery-3.2.1.min.js" />
    <None Include="Scripts\jquery-3.6.0.intellisense.js" />
    <Content Include="Scripts\jquery-3.6.0.js" />
    <Content Include="Scripts\jquery-3.6.0.min.js" />
    <Content Include="Scripts\jquery-3.6.0.slim.js" />
    <Content Include="Scripts\jquery-3.6.0.slim.min.js" />
    <Content Include="Scripts\jquery.cookie.min.js" />
    <Content Include="Scripts\jquery-3.6.0.slim.min.map" />
    <Content Include="Scripts\jquery-3.6.0.min.map" />
    <Content Include="Scripts\jquery.unobtrusive-ajax.js" />
    <Content Include="Scripts\jquery.unobtrusive-ajax.min.js" />
    <None Include="Scripts\jquery.validate-vsdoc.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.min.js" />
    <Content Include="Scripts\jsrender.min.js" />
    <Content Include="Scripts\knockout-min.js" />
    <Content Include="Scripts\modernizr-2.6.2.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="Scripts\moment.min.js" />
    <Content Include="Scripts\popper-utils.js" />
    <Content Include="Scripts\popper-utils.min.js" />
    <Content Include="Scripts\popper.js" />
    <Content Include="Scripts\popper.min.js" />
    <Content Include="Scripts\respond.js" />
    <Content Include="Scripts\respond.min.js" />
    <Content Include="Scripts\select2.min.js" />
    <Content Include="Scripts\src\index.js" />
    <Content Include="Scripts\src\methods\defaults.js" />
    <Content Include="Scripts\src\methods\destroy.js" />
    <Content Include="Scripts\src\methods\disableEventListeners.js" />
    <Content Include="Scripts\src\methods\enableEventListeners.js" />
    <Content Include="Scripts\src\methods\placements.js" />
    <Content Include="Scripts\src\methods\update.js" />
    <Content Include="Scripts\src\modifiers\applyStyle.js" />
    <Content Include="Scripts\src\modifiers\arrow.js" />
    <Content Include="Scripts\src\modifiers\computeStyle.js" />
    <Content Include="Scripts\src\modifiers\flip.js" />
    <Content Include="Scripts\src\modifiers\hide.js" />
    <Content Include="Scripts\src\modifiers\index.js" />
    <Content Include="Scripts\src\modifiers\inner.js" />
    <Content Include="Scripts\src\modifiers\keepTogether.js" />
    <Content Include="Scripts\src\modifiers\offset.js" />
    <Content Include="Scripts\src\modifiers\preventOverflow.js" />
    <Content Include="Scripts\src\modifiers\shift.js" />
    <Content Include="Scripts\src\utils\clockwise.js" />
    <Content Include="Scripts\src\utils\computeAutoPlacement.js" />
    <Content Include="Scripts\src\utils\debounce.js" />
    <Content Include="Scripts\src\utils\find.js" />
    <Content Include="Scripts\src\utils\findCommonOffsetParent.js" />
    <Content Include="Scripts\src\utils\findIndex.js" />
    <Content Include="Scripts\src\utils\getBordersSize.js" />
    <Content Include="Scripts\src\utils\getBoundaries.js" />
    <Content Include="Scripts\src\utils\getBoundingClientRect.js" />
    <Content Include="Scripts\src\utils\getClientRect.js" />
    <Content Include="Scripts\src\utils\getFixedPositionOffsetParent.js" />
    <Content Include="Scripts\src\utils\getOffsetParent.js" />
    <Content Include="Scripts\src\utils\getOffsetRect.js" />
    <Content Include="Scripts\src\utils\getOffsetRectRelativeToArbitraryNode.js" />
    <Content Include="Scripts\src\utils\getOppositePlacement.js" />
    <Content Include="Scripts\src\utils\getOppositeVariation.js" />
    <Content Include="Scripts\src\utils\getOuterSizes.js" />
    <Content Include="Scripts\src\utils\getParentNode.js" />
    <Content Include="Scripts\src\utils\getPopperOffsets.js" />
    <Content Include="Scripts\src\utils\getReferenceNode.js" />
    <Content Include="Scripts\src\utils\getReferenceOffsets.js" />
    <Content Include="Scripts\src\utils\getRoot.js" />
    <Content Include="Scripts\src\utils\getRoundedOffsets.js" />
    <Content Include="Scripts\src\utils\getScroll.js" />
    <Content Include="Scripts\src\utils\getScrollParent.js" />
    <Content Include="Scripts\src\utils\getStyleComputedProperty.js" />
    <Content Include="Scripts\src\utils\getSupportedPropertyName.js" />
    <Content Include="Scripts\src\utils\getViewportOffsetRectRelativeToArtbitraryNode.js" />
    <Content Include="Scripts\src\utils\getWindow.js" />
    <Content Include="Scripts\src\utils\getWindowSizes.js" />
    <Content Include="Scripts\src\utils\includeScroll.js" />
    <Content Include="Scripts\src\utils\index.js" />
    <Content Include="Scripts\src\utils\isBrowser.js" />
    <Content Include="Scripts\src\utils\isFixed.js" />
    <Content Include="Scripts\src\utils\isFunction.js" />
    <Content Include="Scripts\src\utils\isIE.js" />
    <Content Include="Scripts\src\utils\isModifierEnabled.js" />
    <Content Include="Scripts\src\utils\isModifierRequired.js" />
    <Content Include="Scripts\src\utils\isNumeric.js" />
    <Content Include="Scripts\src\utils\isOffsetContainer.js" />
    <Content Include="Scripts\src\utils\removeEventListeners.js" />
    <Content Include="Scripts\src\utils\runModifiers.js" />
    <Content Include="Scripts\src\utils\setAttributes.js" />
    <Content Include="Scripts\src\utils\setStyles.js" />
    <Content Include="Scripts\src\utils\setupEventListeners.js" />
    <Content Include="Scripts\umd\popper-utils.js" />
    <Content Include="Scripts\umd\popper-utils.min.js" />
    <Content Include="Scripts\umd\popper.js" />
    <Content Include="Scripts\umd\popper.min.js" />
    <Content Include="Scripts\WebForms\DetailsView.js" />
    <Content Include="Scripts\WebForms\Focus.js" />
    <Content Include="Scripts\WebForms\GridView.js" />
    <Content Include="Scripts\WebForms\Menu.js" />
    <Content Include="Scripts\WebForms\MenuStandards.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjax.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxApplicationServices.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxComponentModel.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxCore.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxGlobalization.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxHistory.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxNetwork.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxSerialization.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxTimer.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxWebForms.js" />
    <Content Include="Scripts\WebForms\MSAjax\MicrosoftAjaxWebServices.js" />
    <Content Include="Scripts\WebForms\SmartNav.js" />
    <Content Include="Scripts\WebForms\TreeView.js" />
    <Content Include="Scripts\WebForms\WebForms.js" />
    <Content Include="Scripts\WebForms\WebParts.js" />
    <Content Include="Scripts\WebForms\WebUIValidation.js" />
    <Content Include="Scripts\umd\popper.min.js.map" />
    <Content Include="Scripts\umd\popper.js.map" />
    <Content Include="Scripts\umd\popper.js.flow" />
    <Content Include="Scripts\umd\popper-utils.min.js.map" />
    <Content Include="Scripts\umd\popper-utils.js.map" />
    <Content Include="Scripts\README.md" />
    <Content Include="Scripts\popper.min.js.map" />
    <Content Include="Scripts\popper.js.map" />
    <Content Include="Scripts\popper-utils.min.js.map" />
    <Content Include="Scripts\popper-utils.js.map" />
    <None Include="Scripts\_references.js" />
    <Content Include="ServerMessage.ascx" />
    <Content Include="Appointments.aspx" />
    <Content Include="Appointment.aspx" />
    <Content Include="EmailTemplate.aspx" />
    <Content Include="User.aspx" />
    <Content Include="EmailTemplates.aspx" />
    <Content Include="Users.aspx" />
    <Content Include="Web.config" />
    <Content Include="Bundle.config" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Links.aspx.cs">
      <DependentUpon>Links.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Links.aspx.designer.cs">
      <DependentUpon>Links.aspx</DependentUpon>
    </Compile>
    <Compile Include="Partners.aspx.cs">
      <DependentUpon>Partners.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Partners.aspx.designer.cs">
      <DependentUpon>Partners.aspx</DependentUpon>
    </Compile>
    <Compile Include="EmailManager.cs" />
    <Compile Include="Notifications.aspx.cs">
      <DependentUpon>Notifications.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Notifications.aspx.designer.cs">
      <DependentUpon>Notifications.aspx</DependentUpon>
    </Compile>
    <Compile Include="SubmitQuestionnaireForm.aspx.cs">
      <DependentUpon>SubmitQuestionnaireForm.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SubmitQuestionnaireForm.aspx.designer.cs">
      <DependentUpon>SubmitQuestionnaireForm.aspx</DependentUpon>
    </Compile>
    <Compile Include="Task.aspx.cs">
      <DependentUpon>Task.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Task.aspx.designer.cs">
      <DependentUpon>Task.aspx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\GlobalResources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>GlobalResources.resx</DependentUpon>
    </Compile>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="Contact.aspx.cs">
      <DependentUpon>Contact.aspx</DependentUpon>
    </Compile>
    <Compile Include="Contact.aspx.designer.cs">
      <DependentUpon>Contact.aspx</DependentUpon>
    </Compile>
    <Compile Include="Contacts.aspx.cs">
      <DependentUpon>Contacts.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Contacts.aspx.designer.cs">
      <DependentUpon>Contacts.aspx</DependentUpon>
    </Compile>
    <Compile Include="CookieHandler.cs" />
    <Compile Include="DateFunctions.cs" />
    <Compile Include="Reports.aspx.cs">
      <DependentUpon>Reports.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reports.aspx.designer.cs">
      <DependentUpon>Reports.aspx</DependentUpon>
    </Compile>
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="IncludeFiles.Master.cs">
      <DependentUpon>IncludeFiles.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="IncludeFiles.Master.designer.cs">
      <DependentUpon>IncludeFiles.Master</DependentUpon>
    </Compile>
    <Compile Include="Login.aspx.cs">
      <DependentUpon>Login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Login.aspx.designer.cs">
      <DependentUpon>Login.aspx</DependentUpon>
    </Compile>
    <Compile Include="Main.master.cs">
      <DependentUpon>Main.master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Main.master.designer.cs">
      <DependentUpon>Main.master</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Reporting\ExportWordReport.aspx.cs">
      <DependentUpon>ExportWordReport.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Reporting\ExportWordReport.aspx.designer.cs">
      <DependentUpon>ExportWordReport.aspx</DependentUpon>
    </Compile>
    <Compile Include="Reporting\ReportsFactory.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ServerMessage.ascx.cs">
      <DependentUpon>ServerMessage.ascx</DependentUpon>
    </Compile>
    <Compile Include="ServerMessage.ascx.designer.cs">
      <DependentUpon>ServerMessage.ascx</DependentUpon>
    </Compile>
    <Compile Include="Appointments.aspx.cs">
      <DependentUpon>Appointments.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Appointments.aspx.designer.cs">
      <DependentUpon>Appointments.aspx</DependentUpon>
    </Compile>
    <Compile Include="Appointment.aspx.cs">
      <DependentUpon>Appointment.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Appointment.aspx.designer.cs">
      <DependentUpon>Appointment.aspx</DependentUpon>
    </Compile>
    <Compile Include="EmailTemplate.aspx.cs">
      <DependentUpon>EmailTemplate.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EmailTemplate.aspx.designer.cs">
      <DependentUpon>EmailTemplate.aspx</DependentUpon>
    </Compile>
    <Compile Include="User.aspx.cs">
      <DependentUpon>User.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="User.aspx.designer.cs">
      <DependentUpon>User.aspx</DependentUpon>
    </Compile>
    <Compile Include="EmailTemplates.aspx.cs">
      <DependentUpon>EmailTemplates.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="EmailTemplates.aspx.designer.cs">
      <DependentUpon>EmailTemplates.aspx</DependentUpon>
    </Compile>
    <Compile Include="Users.aspx.cs">
      <DependentUpon>Users.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Users.aspx.designer.cs">
      <DependentUpon>Users.aspx</DependentUpon>
    </Compile>
    <Compile Include="WorksheetToHtmlService.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\AdminLTE\adminlte.min.css.map" />
    <Content Include="Content\font-awesome-template\css\font-awesome.css.map" />
    <Content Include="Content\font-awesome-template\fonts\fontawesome-webfont.eot" />
    <Content Include="Content\font-awesome-template\fonts\fontawesome-webfont.ttf" />
    <Content Include="Content\font-awesome-template\fonts\fontawesome-webfont.woff" />
    <Content Include="Content\font-awesome-template\fonts\fontawesome-webfont.woff2" />
    <Content Include="Content\font-awesome-template\fonts\FontAwesome.otf" />
    <Content Include="Main.master" />
    <None Include="packages.config" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_GlobalResources\GlobalResources.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>GlobalResources.Designer.cs</LastGenOutput>
    </Content>
    <Content Include="App_LocalResources\Contact.aspx.resx">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="App_LocalResources\Contacts.aspx.resx" />
    <Content Include="App_LocalResources\Default.aspx.resx" />
    <Content Include="App_LocalResources\Login.aspx.resx">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="App_LocalResources\Main.Master.resx">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="App_LocalResources\User.aspx.resx" />
    <Content Include="App_LocalResources\Users.aspx.resx" />
    <Content Include="App_LocalResources\Appointment.aspx.resx">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="App_LocalResources\Appointments.aspx.resx">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="App_LocalResources\Reports.aspx.resx" />
    <Content Include="App_LocalResources\Task.aspx.resx">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="App_LocalResources\EmailTemplates.aspx.resx" />
    <Content Include="App_LocalResources\EmailTemplate.aspx.resx" />
    <Content Include="App_LocalResources\Partners.aspx.resx" />
    <Content Include="App_LocalResources\Links.aspx.resx" />
    <EmbeddedResource Include="LocalizedResources\ej.localetexts.el-GR.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="LocalizedResources\ej.localetexts.en-US.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Data\Data.csproj">
      <Project>{2f882db9-e62f-4e19-9d82-8e61f78c0dab}</Project>
      <Name>Data</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <TypeScriptCompile Include="Content\tinymce\tinymce.d.ts" />
    <TypeScriptCompile Include="Scripts\index.d.ts" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <SaveServerSettingsInUserFile>True</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>