/*!
*  filename: ej.combobox.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min","./../common/ej.globalize.min"],n):n()})(function(){var i=this&&this.__extends||function(n,t){function r(){this.constructor=n}for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)};(function(f){var o=function(o){function s(n,t){return o.call(this),this._rootCSS="e-combobox",this._setFirst=!1,this.selectFocus=!1,this.PluginName="ejComboBox",this.validTags=["input"],this.type="editor",this._requiresID=!0,this.angular={require:["?ngModel","^?form","^?ngModelOptions"]},this.boolean=!0,this.isPreventBlur=!1,this.isDocumentClick=!1,this.isPopupOpen=!1,this.popupObj=null,this.beforePopupOpen=!1,this.preventFocus=!1,this.isDropDownClick=!1,this.isInteracted=!1,this.isValidKey=!1,this.isSelectCustom=!1,this.preventAutoFill=!1,this.isTyped=!1,this.isSelected=!1,this.prevSelectPoints={},this.initial=!0,this.typedString="",this.actionCompleteData={isUpdated:!1},this.isEscapeKey=!1,this.isFilterFocus=!1,this.isRequested=!1,this.isNotSearchList=!1,this.initRemoteRender=!1,this.searchKeyEvent=null,this.preventAltUp=!1,this.isTabKey=!1,this.model=null,this.defaults={autofill:!1,allowCustom:!0,htmlAttributes:{},allowFiltering:!1,query:null,showClearButton:!0,valueTemplate:null,readonly:!1,text:null,value:null,index:null,headerTemplate:null,footerTemplate:null,fields:{text:null,value:null,iconCss:null,groupBy:null,tooltip:null},groupTemplate:null,itemTemplate:null,noRecordsTemplate:"No Records Found",actionFailureTemplate:"The Request Failed",sortOrder:"None",dataSource:[],popupHeight:"300px",placeholder:null,cssClass:null,enabled:!0,enableRtl:!1,width:"100%",popupWidth:"100%",locale:"en-US",customValueSpecifier:null,filtering:null,actionBegin:null,actionComplete:null,actionFailure:null,select:null,focus:null,change:null,blur:null,close:null,dataBound:null},this.dataTypes={autofill:"boolean",allowCustom:"boolean",htmlAttributes:"object",allowFiltering:"boolean",showClearButton:"boolean",valueTemplate:"string",readonly:"boolean",text:"string",index:"number",headerTemplate:"string",footerTemplate:"string",dataSource:"data",query:"data",fields:"data",groupTemplate:"string",itemTemplate:"string",noRecordsTemplate:"string",actionFailureTemplate:"string",sortOrder:"enum",placeholder:"string",cssClass:"string",enabled:"boolean",enableRtl:"boolean",locale:"string"},this.observables=["value"],this.value=ej.util.valueFunction("value"),n&&(n.jquery||(n=f("#"+n)),n.length)?f(n).ejComboBox(t).data(this.PluginName):void 0}return i(s,o),s.prototype._setModel=function(n){var t,i;for(t in n)switch(t){case"allowFiltering":this.model.allowFiltering=n[t];this.setSearchBox();break;case"allowCustom":this.model.allowCustom=n[t];break;case"htmlAttributes":this.model.htmlAttributes=n[t];this.setHTMLAttributes();break;case"width":this.model.width=n[t];f(this.inputWrapper.container).css({width:this.formatUnit(n[t])});break;case"placeholder":this.model.placeholder=n[t];this.setPlaceholder(n[t],this.inputElement);break;case"readonly":this.model.readonly=n[t];this.setReadonly(n[t],this.inputElement);break;case"cssClass":this.model.cssClass=n[t];this.setCssClass(n[t]);this.popupObj&&f(this.popupObj).addClass(n[t]);break;case"enableRtl":this.model.enableRtl=n[t];this.setEnableRtl();break;case"enabled":this.model.enabled=n[t];this.setEnable();break;case"text":case"value":case"index":if(n[t]===null){this.clear();return}this.list||(this.model.dataSource instanceof ej.DataManager&&(this.initRemoteRender=!0),this.renderList());this.initRemoteRender||(i=t=="text"?this.getElementByText(n[t]):t=="value"?this.getElementByValue(n[t]):this.liCollections[n[t]],this.isValidLI(i)?this.setSelection(i,null):t=="text"?this.setOldText(n[t]):t=="value"?this.setOldValue(this.value(n[t])):null);break;case"query":case"dataSource":case"fields":case"sortOrder":t=="sortOrder"&&(this.model.sortOrder=n[t]);t=="fields"&&(this.model.fields=n[t]);t=="query"&&(this.model.query=n[t]);t=="dataSource"&&(this.model.dataSource=n[t]);this.clear();this.resetList(this.model.dataSource);break;case"footerTemplate":this.model.footerTemplate=n[t];this.popupObj&&this.setFooterTemplate(this.popupObj)}},s.prototype.wireEvent=function(){this._on(f(this.inputWrapper.buttons[0]),"mousedown",this.preventBlur);this._on(f(this.inputWrapper.container),"blur",this.onBlur);this._on(f(this.inputElement),"focus",this.targetFocus);this._on(f(this.inputElement),"blur",this.onBlur);this.model.readonly||(this._on(f(this.inputElement),"input",this.onInput),this._on(f(this.inputElement),"keyup",this.onFilterUp),this._on(f(this.inputElement),"keydown",this.onFilterDown));ej.isNullOrUndefined(this.inputWrapper.buttons[0])||this._on(f(this.inputWrapper.buttons[0]),"mousedown",this.dropDownClick);ej.isDevice()||this._on(f(this.inputElement),"keydown",this.keyActionHandler);this.model.showClearButton&&this._on(f(this.inputWrapper.clearButton),"mousedown",this.resetHandler)},s.prototype.unWireEvent=function(){this._off(f(this.inputWrapper.buttons[0]),"mousedown",this.preventBlur);this._off(f(this.inputWrapper.container),"blur",this.onBlur);this._off(f(this.inputElement),"focus",this.targetFocus);this._off(f(this.inputElement),"blur",this.onBlur);ej.isNullOrUndefined(this.inputWrapper.buttons[0])||this._off(f(this.inputWrapper.buttons[0]),"mousedown",this.dropDownClick);this.model.readonly||(this._off(f(this.inputElement),"input",this.onInput),this._off(f(this.inputElement),"keyup",this.onFilterUp),this._off(f(this.inputElement),"keydown",this.onFilterDown));ej.isDevice()||this._off(f(this.inputElement),"keydown",this.keyActionHandler);this.model.showClearButton&&this._off(f(this.inputWrapper.clearButton),"mousedown",this.resetHandler)},s.prototype.preventBlur=function(t){(!this.model.allowFiltering&&document.activeElement!==this.inputElement&&(ej.isNullOrUndefined(document.activeElement)||document.activeElement.className&&document.activeElement.className.indexOf(n.input)==-1)&&ej.isDevice()||!ej.isDevice())&&t.preventDefault()},s.prototype.setOldText=function(){this.setInputValue(this.model.text,this.inputElement,this.model.showClearButton);this.customValue();this.removeSelection()},s.prototype.setOldValue=function(){this.valueMuteChange(this.model.allowCustom?this.value():null);this.removeSelection();this.setHiddenValue()},s.prototype.valueMuteChange=function(n){var r=ej.isNullOrUndefined(n)?null:n.toString(),i,t;this.setInputValue(r,this.inputElement,this.model.showClearButton);this.value(n);this.model.text=n?n.toString():null;this.model.index=null;this.activeIndex=this.model.index;i=this.getFields();t={};t[i.text]=ej.isNullOrUndefined(n)?null:n.toString();t[i.value]=ej.isNullOrUndefined(n)?null:n.toString();this.itemData=t;this.item=null;this.previousValue!==this.value()&&this.detachChangeEvent(null)},s.prototype.updateValues=function(){var n;ej.isNullOrUndefined(this.value())?this.model.text&&ej.isNullOrUndefined(this.value())?(n=this.getElementByText(this.model.text),n?this.setSelection(n,null):(this.setInputValue(this.model.text,this.inputElement,this.model.showClearButton),this.customValue())):this.setSelection(this.liCollections[this.activeIndex],null):(n=this.getElementByValue(this.value()),n?this.setSelection(n,null):this.model.allowCustom?this.valueMuteChange(this.value()):this.valueMuteChange(null));this.setHiddenValue();this.setInputValue(this.model.text,this.inputElement,this.model.showClearButton)},s.prototype.getAriaAttributes=function(){return{"aria-owns":this.element[0].id+"_options",role:"combobox","aria-autocomplete":"both","aria-hasPopup":"true","aria-expanded":"false","aria-readonly":this.model.readonly.toString(),autocomplete:"off",autocorrect:"off",autocapitalize:"off",spellcheck:"false"}},s.prototype.searchLists=function(t){this.isTyped=!0;this.model.allowFiltering?(this.listsearchLists(t),f.trim(this.filterInput.value)===""&&this.setHoverList(this.ulElement.querySelector("."+n.li))):(this.ulElement&&this.inputElement.value===""&&this.preventAutoFill&&this.setHoverList(this.ulElement.querySelector("."+n.li)),this.incrementalSearch(t))},s.prototype.setSearchBox=function(){return this.filterInput=this.inputElement,this.model.allowFiltering?this.inputWrapper:u},s.prototype.onActionComplete=function(n,t,i){this.listonActionComplete(n,t,i);this.isSelectCustom&&this.removeSelection();!this.preventAutoFill&&this.isTyped&&this.inlineSearch()},s.prototype.getFocusElement=function(){var o=this.isSelectCustom?{text:""}:this.getItemData(),r=this.list.querySelector("."+n.selected),s=o.text===this.inputElement.value&&!ej.isNullOrUndefined(r),u,i,e;if(s)return r;if((ej.isDevice()&&!this.isDropDownClick||!ej.isDevice())&&!ej.isNullOrUndefined(this.liCollections)&&this.liCollections.length>0){var h=this.inputElement.value,c=this.search(h,this.liCollections,"StartsWith",!0),t=c.item;return ej.isNullOrUndefined(t)?this.isSelectCustom&&f.trim(this.inputElement.value)!==""&&(this.removeFocus(),this.list.scrollTop=0):(u=this.getIndexByValue(t.getAttribute("data-value"))-1,i=parseInt(typeof getComputedStyle!="undefined"?getComputedStyle(this.liCollections[0],null).getPropertyValue("height"):this.liCollections[0].currentStyle.height,10),isNaN(i)||(e=this.model.fields.groupBy?this.liCollections[0].offsetHeight:0,this.list.scrollTop=u*i+e,f(t).addClass(n.focus))),t}return null},s.prototype.setValue=function(n){return n&&n.type==="keydown"&&n.KeyCode===13&&this.removeFillSelection(),this.model.autofill&&n&&n.type==="keydown"&&n.keyCode!==13?(this.preventAutoFill=!1,this.inlineSearch(n),!1):this.listsetValue(n)},s.prototype.setAutoFill=function(n,t){if(t||this.setHoverList(n),this.model.autofill&&!this.preventAutoFill){var i=this.getTextByValue(n.getAttribute("data-value")).toString(),r=this.getFormattedValue(n.getAttribute("data-value"));this.isSelected||this.previousValue===r?this.updateSelectedItem(n,null,!0):(this.updateSelectedItem(n,null),this.isSelected=!0,this.previousValue=this.getFormattedValue(n.getAttribute("data-value")));this.isAndroidAutoFill(i)||this.setAutoFillSelection(i)}},s.prototype.isAndroidAutoFill=function(n){if(ej.isMobile()){var r=this.getSelectionPoints(),t=this.prevSelectPoints.end,u=r.end,i=this.prevSelectPoints.start,f=r.start;return t!==0&&(t===n.length&&i===n.length||i>f&&t>u||t===u&&i===f)?!0:!1}return!1},s.prototype.inlineSearch=function(t){var i=t&&(t.keyCode===40||t.keyCode===38||t.keyCode===36||t.keyCode===35||t.keyCode===33||t.keyCode===34),r=i?this.liCollections[this.activeIndex]:this.getFocusElement(),u,f;ej.isNullOrUndefined(r)?this.inputElement.value===""?(this.activeIndex=null,this.list.scrollTop=0,f=this.list.querySelector("."+n.li),this.setHoverList(f)):(this.activeIndex=null,this.removeSelection(),this.removeFocus()):(i||(this.selectFocus=!0,u=this.getFormattedValue(r.getAttribute("data-value")),this.activeIndex=this.getIndexByValue(u),this.activeIndex=ej.isNullOrUndefined(this.activeIndex)?null:this.activeIndex),this.preventAutoFill=this.inputElement.value===""?!1:this.preventAutoFill,this.setAutoFill(r,i))},s.prototype.incrementalSearch=function(n){this.isWatermark||this.hiddenSpan.css("display","none");this.showPopup();ej.isNullOrUndefined(this.listData)||(this.inlineSearch(n),n.preventDefault())},s.prototype.setAutoFillSelection=function(n){var i=this.getSelectionPoints(),t=this.inputElement.value.substr(0,i.start),r;t&&t.toLowerCase()===n.substr(0,i.start).toLowerCase()?(r=t+n.substr(t.length,n.length),this.setInputValue(r,this.inputElement,this.model.showClearButton),this.inputElement.setSelectionRange(i.start,this.inputElement.value.length)):(this.setInputValue(n,this.inputElement,this.model.showClearButton),this.inputElement.setSelectionRange(0,this.inputElement.value.length))},s.prototype.setSelection=function(n,t){this.listsetSelection(n,t);ej.isNullOrUndefined(n)||this.model.autofill||this.isDropDownClick||this.removeFocus()},s.prototype.selectCurrentItem=function(t){var i;if(this.isPopupOpen&&(i=this.list.querySelector("."+n.focus),i&&(this.setSelection(i,t),this.isTyped=!1),this.isSelected&&(this.isSelectCustom=!1,!this.selectFocus)))this.onChangeEvent(t);this.isTyped&&!this.isSelected&&ej.isNullOrUndefined(i)&&this.customValue();this.selectFocus?this.focusOutAction():this.hidePopup()},s.prototype.setHoverList=function(t){this.removeSelection();this.isValidLI(t)&&t.className.indexOf(n.selected)==-1&&(this.removeFocus(),f(t).addClass(n.focus))},s.prototype.targetFocus=function(){f(this.inputWrapper.container).addClass(n.inputFocus);ej.isDevice()&&!this.model.allowFiltering&&(this.preventFocus=!1);this.isWatermark||this.hiddenSpan.css("display","none");this.onFocus()},s.prototype.dropDownClick=function(n){ej.isDevice()&&!this.model.allowFiltering&&(this.preventFocus=!0);this.listdropDownClick(n)},s.prototype.customValue=function(){var r=this.getValueByText(this.inputElement.value,!0),o,t,i;if(this.model.allowCustom||this.inputElement.value===""){if(f.trim(this.inputElement.value)!==""){if(o=this.value(),ej.isNullOrUndefined(r)){var u=this.inputElement.value===""?null:this.inputElement.value,n=this.getFields(),e=void 0;e={text:u,item:{}};this.initial||this._trigger("customValueSpecifier",e);t=e.item;i={};t&&t[n.text]&&t[n.value]?i=t:(i[n.text]=u,i[n.value]=u);this.itemData=i;this.model.text=this.itemData[n.text];this.value(this.itemData[n.value]);this.model.index=null;this.setSelection(null,null);this.isSelectCustom=!0}else this.isSelectCustom=!1,this.value(r);if(o!==this.value())this.onChangeEvent(null)}}else this._setModel({value:r}),this.value(r),ej.isNullOrUndefined(this.value())&&this.setInputValue("",this.inputElement,this.model.showClearButton)},s.prototype._init=function(){this.listrender();this.setSearchBox();this.model.allowFiltering&&ej.isNullOrUndefined(this.list)&&this.renderList()},s.prototype.hidePopup=function(){var i,r,u,t;if(!ej.isNullOrUndefined(this.listData)){if(i=this.isEscapeKey,this.isEscapeKey&&(this.setInputValue(this.typedString,this.inputElement,this.model.showClearButton),this.isEscapeKey=!1),this.model.autofill&&this.removeFillSelection(),r=this.isSelectCustom?{text:""}:this.getItemData(),u=this.list.querySelector("."+n.selected),r.text===this.inputElement.value&&!ej.isNullOrUndefined(u)){if(this.isSelected){if(!this.selectFocus)this.onChangeEvent(null);this.isSelectCustom=!1}this.listhidePopup();return}f.trim(this.inputElement.value)!==""&&(t=this.search(this.inputElement.value,this.liCollections,"Equal",!0),this.selectedLI=t.item,ej.isNullOrUndefined(t.index)&&(t.index=this.search(this.inputElement.value,this.liCollections,"StartsWith",!0).index),this.activeIndex=t.index,ej.isNullOrUndefined(this.selectedLI)?i&&(this.isSelectCustom=!0,this.removeSelection()):this.updateSelectedItem(this.selectedLI,null,!0));this.isEscapeKey||!this.isTyped||this.isInteracted||this.customValue()}this.listhidePopup()},s.prototype.focusIn=function(){ej.isDevice()&&!this.model.allowFiltering&&(this.preventFocus=!0);this.listfocusIn()},s.prototype.listhidePopup=function(){this.closePopup();var n=this.getItemData();f.trim(this.inputElement.value)!==""||this.isInteracted||!this.isSelectCustom&&(ej.isNullOrUndefined(this.selectedLI)||this.inputElement.value===n.text)||(this.isSelectCustom=!1,this.clear())},s.prototype.closePopup=function(){if(this.isTyped=!1,this.popupObj&&document.body.contains(this.popupObj))if(this._off(f(document),"mousedown",this.onDocumentClick),this.isActive=!1,this.isDropDownClick=!1,this.isDocumentClick=!1,this.preventAutoFill=!1,this._off(ej.getScrollableParents(f(this.inputWrapper.container)),"scroll",this.scrollHandler),f(this.inputElement).attr({"aria-expanded":"false","aria-activedescendant":null}),f(this.inputWrapper.container).removeClass(n.iconAnimation),this.model.allowFiltering&&(this.actionCompleteData.isUpdated=!1),this.beforePopupOpen=!1,this.isPopupOpen){var t=this;this.isPopupOpen=!1;f(this.popupObj).fadeOut(100,function(){f(t.popupObj).removeClass("e-popup-open").addClass("e-popup-close");t._trigger("close",{popup:t.popupObj});t.destroyPopup()})}else this.destroyPopup()},s.prototype.scrollHandler=function(){ej.isDevice()&&!this.model.allowFiltering&&this.isDropDownClick&&this.hidePopup()},s.prototype.destroyPopup=function(){this.isFilterFocus=!1;this.popupObj&&f("#"+this.popupObj.id+".e-ddl.e-popup.e-control").remove();this.popupObj=null},s.prototype.onDocumentClick=function(t){var i=t.target,r;!ej.isNullOrUndefined(this.popupObj)&&f(i).closest("#"+this.popupObj.id).length>0||this.inputWrapper.container.contains(t.target)?i===this.inputElement||this.model.allowFiltering&&i===this.filterInput||!this.model.allowFiltering&&ej.isDevice()&&i===this.inputWrapper.buttons[0]||(this.isPreventBlur=(ej.browserInfo().name=="msie"||ej.browserInfo().name==="edge")&&(document.activeElement===this.inputElement||document.activeElement===this.filterInput),t.preventDefault()):(this.inputWrapper.container.className.indexOf(n.inputFocus)>-1||this.isPopupOpen)&&(this.isDocumentClick=!0,r=this.isRequested,this.isInteracted=!1,this.hidePopup(),r||(this.onFocusOut(),f(this.inputWrapper.container).removeClass(n.inputFocus)))},s.prototype.onFocusOut=function(){if(this.isSelected&&(this.isSelectCustom=!1,!this.selectFocus))this.onChangeEvent(e);this.dispatchEvent(this.hiddenElement,"change");this.inputWrapper.clearButton&&f(this.inputWrapper.clearButton).addClass(n.clearIconHide);this._trigger("blur")},s.prototype.dispatchEvent=function(n,t){if(document.createEvent){var i=document.createEvent("HTMLEvents");i.initEvent(t,!1,!0);n.dispatchEvent(i);this.onChangeEvent(null)}},s.prototype.onBlur=function(t){var i=t.relatedTarget,r=t.target,u=this.isPreventBlur;if(this.isPreventBlur=!1,u&&!this.isDocumentClick&&this.isPopupOpen&&(!ej.isNullOrUndefined(r)||ej.isNullOrUndefined(i))){this.inputElement.focus();return}if(this.isDocumentClick||!ej.isNullOrUndefined(this.popupObj)&&document.body.contains(this.popupObj)&&this.popupObj.className.indexOf(n.mobileFilter)>-1){this.beforePopupOpen||(this.isDocumentClick=!1);return}(document.activeElement!==i||document.activeElement===i&&r.className.indexOf(n.inputFocus)>-1||!this.inputWrapper.container.contains(i)||this.isTabKey)&&(this.isDocumentClick=this.isPopupOpen?!0:!1,this.selectFocus||this.focusOutAction(),this.isTabKey=!1)},s.prototype.focusOutAction=function(){this.isInteracted=!1;this.focusOut();this.onFocusOut()},s.prototype.focusOut=function(){this.isTyped=!0;this.hidePopup();this.inputElement.blur();f(this.inputWrapper.container).removeClass(n.inputFocus)},s.prototype.listdropDownClick=function(t){if(!(this.inputElement.className.indexOf(n.disable)>-1)&&this.inputWrapper.clearButton!==t.target){var i=t.target;this.model.readonly||(this.isPopupOpen?this.hidePopup():(this.focusIn(),this.queryString=f.trim(this.inputElement.value)===""?null:this.inputElement.value,this.isDropDownClick=!0,this.showPopup()))}},s.prototype.showPopup=function(){if(this.beforePopupOpen){this.refreshPopup();return}if(this.beforePopupOpen=!0,this.model.allowFiltering&&!this.isActive&&this.actionCompleteData.list&&this.actionCompleteData.list[0]){this.isActive=!0;this.onActionComplete(this.actionCompleteData.ulElement,this.actionCompleteData.list,null,!0)}else(ej.isNullOrUndefined(this.list)||this.list!=undefined&&this.list.className.indexOf(n.noData)>-1)&&this.renderList();(!ej.isNullOrUndefined(this.list.children[0])||this.list.className.indexOf(n.noData)>-1)&&this.renderPopup();f(this.inputElement).attr({"aria-activedescendant":this.selectedLI?this.selectedLI.id:null});this.model.cssClass&&this.popupObj&&f(this.popupObj).addClass(this.model.cssClass)},s.prototype.listfocusIn=function(){if(!(this.inputElement.className.indexOf(n.disable)>-1)){var t=!1;this.preventFocus&&ej.isDevice()&&(this.inputWrapper.container.tabIndex=1,this.inputWrapper.container.focus(),this.preventFocus=!1,t=!0);t||this.inputElement.focus();f(this.inputWrapper.container).addClass(n.inputFocus);this.onFocus()}},s.prototype.onFocus=function(){this.isInteracted||(this.isInteracted=!0,this._trigger("focus"));this.updateIconState()},s.prototype.updateIconState=function(){this.model.showClearButton&&(this.inputElement.value!==""?f(this.inputWrapper.clearButton).removeClass(n.clearIconHide):f(this.inputWrapper.clearButton).addClass(n.clearIconHide))},s.prototype.refreshPopup=function(){!ej.isNullOrUndefined(this.popupObj)&&document.body.contains(this.popupObj)&&this.setPopupPosition()},s.prototype.renderPopup=function(){var t,u,h,r,o,c,e,s,i;if(this.popupObj&&document.body.contains(this.popupObj)){this.refreshPopup();return}t=ej.buildTag("div#"+this.element[0].id+"_popup.e-ddl e-popup e-widget ej1-combobox")[0];u=this.setSearchBox();this.listHeight=this.formatUnit(this.model.popupHeight);this.model.headerTemplate&&(h=void 0,this.header=document.createElement("div"),f(this.header).addClass(n.header),this.header.innerHTML=this.model.headerTemplate,f(t).append(this.header));f(t).append(this.list);this.model.footerTemplate&&this.setFooterTemplate(t);t.style.visibility="hidden";t.style.display="none";document.body.appendChild(t);this.model.popupHeight!=="auto"?(this.searchBoxHeight=0,ej.isNullOrUndefined(u.container)||(this.searchBoxHeight=u.container.parentElement.getBoundingClientRect().height,this.listHeight=(parseInt(this.listHeight,10)-this.searchBoxHeight).toString()+"px"),this.model.headerTemplate&&(r=Math.round(this.header.getBoundingClientRect().height),this.listHeight=(parseInt(this.listHeight,10)-(r+this.searchBoxHeight)).toString()+"px"),this.model.footerTemplate&&(r=Math.round(this.footer.getBoundingClientRect().height),this.listHeight=(parseInt(this.listHeight,10)-(r+this.searchBoxHeight)).toString()+"px"),this.list.style.maxHeight=(parseInt(this.listHeight,10)-2).toString()+"px",t.style.maxHeight=this.formatUnit(this.model.popupHeight)):t.style.height="auto";o=2;!ej.isNullOrUndefined(this.selectedLI)&&!ej.isNullOrUndefined(this.activeIndex)&&this.activeIndex>=0?this.setScrollPosition():this.list.scrollTop=0;ej.isDevice()&&!this.model.allowFiltering&&this.isDropDownClick&&(o=this.setPopupPosition(),e=this.isEmptyList()?this.list:this.liCollections[0],c=-(parseInt(typeof getComputedStyle!="undefined"?getComputedStyle(e).textIndent:e.currentStyle.textIndent,10)-parseInt(typeof getComputedStyle!="undefined"?getComputedStyle(this.inputElement).textIndent:this.inputElement.currentStyle.textIndent,10)-parseInt(typeof getComputedStyle!="undefined"?getComputedStyle(this.inputElement.parentElement).borderLeftWidth:this.inputElement.parentElement.currentStyle.textIndent),10));this.getFocusElement();this.popupObj=f(t).css("width",this.setWidth()).addClass("e-control e-popup e-ddl")[0];this.model.enableRtl?f(t).addClass(n.rtl):f(t).removeClass(n.rtl);ej.isDevice()&&f(this.popupObj).addClass(n.device);this.setListPosition();t.style.visibility="visible";this._on(ej.getScrollableParents(f(this.inputWrapper.container)),"scroll",this.scrollHandler);this._on(ej.getScrollableParents(f(this.inputWrapper.container)),"scroll",this.setListPosition);this._on(f(window),"resize",this.setListPosition);f(this.inputElement).attr({"aria-expanded":"true"});s=this.model.allowFiltering?this.filterInput.parentElement:this.inputWrapper.container;f(s).addClass(n.inputFocus);f(this.inputWrapper.container).addClass(n.iconAnimation);this.beforePopupOpen=!0;i=this;f(this.popupObj).fadeIn(100,function(){i.isPopupOpen=!0;i.activeStateChange();f(i.popupObj).addClass("e-popup-open");i._trigger("open",{popup:i.popupObj});i.model.allowFiltering&&i.filterInput.focus()});this._on(f(document),"mousedown",this.onDocumentClick);this.wireListEvents();this.model.fields.groupBy&&this._on(f(this.list),"scroll",this.setFloatingHeader)},s.prototype.setListPosition=function(){var n=this.inputWrapper.container,t=ej.getOffset(f(n)),u,h=f(document).scrollTop()+f(window).height()-(t.top+f(n).outerHeight()),c=t.top-f(document).scrollTop(),r=f(this.popupObj).outerHeight(),e=f(this.popupObj).outerWidth(),i=t.left,o=f(n).outerHeight(),l=(o-f(n).height())/2,a=ej.getZindexPartial(this.element,this.popupObj),s=3,v=(r<h||r>c?t.top+o+s:t.top-r-s)-l;u=f(document).scrollLeft()+f(window).width()-i;(this.model.enableRtl||e>u&&e<i+f(n).outerWidth())&&(i-=f(this.popupObj).outerWidth()-f(n).outerWidth());f(this.popupObj).css({left:i+"px",top:v+"px","z-index":a})},s.prototype.setFooterTemplate=function(t){this.footer?this.footer.innerHTML="":(this.footer=document.createElement("div"),f(this.footer).addClass(n.footer));this.footer.innerHTML=this.model.footerTemplate;f(t).append(this.footer)},s.prototype.setPopupPosition=function(){var t,e=2,r=this.list.querySelector("."+n.focus)||this.selectedLI,o=this.isEmptyList()?this.list:this.liCollections[0],c=this.isEmptyList()?this.list:this.liCollections[this.getItems().length-1],i=o.getBoundingClientRect().height,u=parseInt(this.listHeight,10)/2,f=ej.isNullOrUndefined(r)?o.offsetTop:r.offsetTop,l=c.offsetTop,s,h;return l-u<f&&!ej.isNullOrUndefined(this.liCollections)&&this.liCollections.length>0&&!ej.isNullOrUndefined(r)?(s=parseInt(this.model.popupHeight,10)/i,h=parseInt(typeof getComputedStyle!="undefined"?getComputedStyle(this.list).paddingBottom:this.list.currentStyle.paddingBottom,10),t=(s-(this.liCollections.length-this.activeIndex))*i-e+h,this.list.scrollTop=r.offsetTop):f>u?(t=u-i/2,this.list.scrollTop=f-u+i/2):t=f,t=t+i-e+(i-this.inputWrapper.container.offsetHeight)/2,-t},s.prototype.activeStateChange=function(){this.isDocumentClick&&(this.hidePopup(),this.onFocusOut(),f(this.inputWrapper.container).removeClass(n.inputFocus))},s.prototype.isEmptyList=function(){return!ej.isNullOrUndefined(this.liCollections)&&this.liCollections.length===0},s.prototype.formatUnit=function(n){var t=n+"";return t==="auto"||t.indexOf("%")!==-1||t.indexOf("px")!==-1?t:t+"px"},s.prototype.createInput=function(t){var i={container:null,buttons:[],clearButton:null},r;return i.container=ej.buildTag("span."+n.input+" e-widget ej1-combobox")[0],t.element.parentNode.insertBefore(i.container,t.element),i.container.appendChild(f(t.element).addClass("e-input")[0]),t.properties.showClearButton&&(i.clearButton=this.createClearButton(t.element,i.container)),r=ej.buildTag("span."+t.buttons[0])[0],ej.browserInfo().name==="msie"&&parseInt(ej.browserInfo().version)<=10&&(parseInt(ej.browserInfo().version)==9?f(r).addClass("e-comboie9"):parseInt(ej.browserInfo().version)==8&&f(r).addClass("e-comboie8"),f(r).addClass("e-comboie")),i.container.appendChild(r),i.buttons.push(r),i.container.className.indexOf(n.input)==-1&&f(i.container).addClass("e-input-group"),t.properties.readonly?f(this.inputElement).attr({readonly:""}):this.inputElement.removeAttribute("readonly"),i},s.prototype.createClearButton=function(t,i){var r=ej.buildTag("span."+n.clearIcon+(ej.browserInfo().name==="msie"&&parseInt(ej.browserInfo().version)<=10?" e-comboie":""))[0];return i.appendChild(r),t.value?f(r).removeClass(n.clearIconHide):f(r).addClass(n.clearIconHide),this._on(f(r),"click",function(i){t.className.indexOf(n.disable)>-1||t.readOnly||(i.preventDefault(),t!==document.activeElement&&t.focus(),t.value="",f(r).addClass(n.clearIconHide))}),r},s.prototype.setWidth=function(){var n=this.formatUnit(this.model.popupWidth),i,t;return n.indexOf("%")>-1&&(i=this.inputWrapper.container.offsetWidth*parseFloat(n)/100,n=i.toString()+"px"),ej.isDevice()&&!this.model.allowFiltering&&this.isDropDownClick&&(t=this.isEmptyList()?this.list:this.liCollections[0],n=parseInt(n,10)+(parseInt(typeof getComputedStyle!="undefined"?getComputedStyle(t).textIndent:t.currentStyle.textIndent,10)-parseInt(typeof getComputedStyle!="undefined"?getComputedStyle(this.inputElement).textIndent:this.inputElement.currentStyle.textIndent,10)+parseInt(typeof getComputedStyle!="undefined"?getComputedStyle(this.inputElement.parentElement).borderLeftWidth:this.inputElement.parentElement.currentStyle.borderLeftWidth,10))*2+"px"),n},s.prototype.clearText=function(){this.filterInput.value="";this.searchLists(null)},s.prototype.onInput=function(){this.isValidKey=!0;this.updateIconState()},s.prototype.onFilterUp=function(n){if(this.isValidKey=n.keyCode===40||n.keyCode===38||this.isValidKey,this.isValidKey){this.isValidKey=!1;switch(n.keyCode){case 38:case 40:this.preventAutoFill=!1;this.preventAltUp=!1;n.preventDefault();break;case 46:case 8:this.typedString=this.filterInput.value;!this.isPopupOpen&&this.typedString!==""||this.isPopupOpen&&this.queryString.length>0?(this.preventAutoFill=!0,this.searchLists(n)):this.typedString===""&&(this.resetFocusElement(),this.activeIndex=null);n.preventDefault();break;default:this.typedString=this.filterInput.value;this.preventAutoFill=!1;this.searchLists(n)}}},s.prototype.onFilterDown=function(n){switch(n.keyCode){case 13:break;case 40:case 38:this.queryString=this.filterInput.value;n.preventDefault();break;case 9:this.isPopupOpen&&n.preventDefault();break;default:this.prevSelectPoints=this.getSelectionPoints();this.queryString=this.filterInput.value}},s.prototype.resetHandler=function(n){n.preventDefault();this.clear()},s.prototype.clear=function(){if(this.list){if(this.model.allowFiltering)this.onActionComplete(this.actionCompleteData.ulElement.cloneNode(!0),this.actionCompleteData.list);this.resetFocusElement()}this.hiddenElement.innerHTML="";this.inputElement.value="";this.value(null);this.model.text=null;this.model.index=null;this.activeIndex=null;this.item=null;this.itemData=null;this.queryString="";this.setSelection(null,null);this.isSelectCustom=!1;this.onChangeEvent(null);this.updateIconState()},s.prototype.resetFocusElement=function(){this.removeHover();this.removeSelection();this.removeFocus();this.list.scrollTop=0;var t=this.ulElement.querySelector("."+n.li);t&&f(t).addClass(n.focus)},s.prototype.keyActionHandler=function(t){var o=t.keyCode===33||t.keyCode===34,s=t.keyCode===36||t.keyCode===35,i,r,u,f;if((this.isEscapeKey=t.keyCode===27,this.isTabKey=!this.isPopupOpen&&t.keyCode===9,i=t.keyCode===40||t.keyCode===38||t.keyCode===33||t.keyCode===34||t.keyCode===36||t.keyCode===35,!(o||s)||this.isPopupOpen)&&!this.model.readonly){if(r=t.keyCode===9||t.keyCode===9&&t.shiftKey,this.list!==undefined||this.isRequested||r||t.keyCode===27||(this.searchKeyEvent=t,this.renderList()),ej.isNullOrUndefined(this.list)||!ej.isNullOrUndefined(this.liCollections)&&i&&this.liCollections.length===0||this.isRequested)return;(r&&this.isPopupOpen||t.keyCode===27)&&t.preventDefault();this.isSelected=t.keyCode===27?!1:this.isSelected;this.isTyped=i||t.keyCode===27?!1:this.isTyped;switch(t.keyCode){case 40:case 38:if(t.altKey&&t.keyCode===40){this.showPopup();break}if(t.altKey&&t.keyCode===38){this.preventAltUp=this.isPopupOpen;this.hidePopup();break}if(u=this.list.querySelector("."+n.focus),ej.isNullOrUndefined(u)){var e=void 0,h=t.keyCode===40?this.activeIndex+1:this.activeIndex-1;e=ej.isNullOrUndefined(this.activeIndex)?this.liCollections[0]:this.liCollections[h];this.setSelection(e,t)}else this.setSelection(u,t);t.preventDefault();break;case 33:this.pageUpSelection(this.activeIndex-this.getPageCount(),t);t.preventDefault();break;case 34:this.pageDownSelection(this.activeIndex+this.getPageCount(),t);t.preventDefault();break;case 36:if(t.preventDefault(),this.activeIndex===0)return;this.setSelection(this.liCollections[0],t);break;case 35:if(t.preventDefault(),f=this.getItems().length-1,this.activeIndex===f)return;this.setSelection(this.liCollections[f],t);break;case 13:this.selectCurrentItem(t);break;case 27:case 9:this.isPopupOpen&&this.hidePopup()}}},s.prototype.getPageCount=function(){var t=this.list.className.indexOf(n.noData)>-1?null:typeof getComputedStyle!="undefined"?getComputedStyle(this.getItems()[0],null).getPropertyValue("height"):this.getItems()[0].currentStyle.height;return Math.round(this.list.getBoundingClientRect().height/parseInt(t,10))},s.prototype.pageUpSelection=function(n,t){var i=n>=0?this.liCollections[n+1]:this.liCollections[0];this.setSelection(i,t)},s.prototype.pageDownSelection=function(n,t){var i=this.getItems(),r=n<=i.length?this.liCollections[n-1]:this.liCollections[i.length-1];this.setSelection(r,t)},s.prototype.getValueByText=function(n,t){var u=null,i=this.listData,o=this.model.fields.text?this.model.fields.text:"text",c=this.model.fields.value?this.model.fields.value:o,f,s,e,h,r;if(t)if(typeof i[0]=="string"||typeof i[0]=="number"){for(f=0,s=i;f<s.length;f++)if(r=s[f],String(r).toLowerCase()===n.toString().toLowerCase()){u=typeof i[0]=="string"?String(r):this.getFormattedValue(String(r));break}}else i.filter(function(t){var i=t[o].toString();if(i.toLowerCase()===n.toLowerCase())return u=t[c],!0});else if(typeof i[0]=="string"||typeof i[0]=="number"){for(e=0,h=i;e<h.length;e++)if(r=h[e],String(r)===n.toString()){u=typeof i[0]=="string"?n:this.getFormattedValue(n);break}}else i.filter(function(t){if(t[o]===n)return u=t[c],!0});return u},s.prototype.removeSelection=function(){var t=this.list.querySelectorAll("."+n.selected);t.length&&(f(t).removeClass(n.selected),t[0].removeAttribute("aria-selected"))},s.prototype.listonActionComplete=function(n,t,i){if(this.isNotSearchList){this.isNotSearchList=!1;return}if(this.isActive){var r=this.selectedLI?this.selectedLI.cloneNode(!0):null;this.baseonActionComplete(n,t,i);this.isRequested&&!ej.isNullOrUndefined(this.searchKeyEvent)&&this.searchKeyEvent.type==="keydown"&&(this.isRequested=!1,this.keyActionHandler(this.searchKeyEvent),this.searchKeyEvent=null);this.isRequested&&!ej.isNullOrUndefined(this.searchKeyEvent)&&(this.incrementalSearch(this.searchKeyEvent),this.searchKeyEvent=null);this.list.scrollTop=0;ej.isNullOrUndefined(n)||f(n).attr({id:this.element[0].id+"_options",role:"listbox","aria-hidden":"false"});this.initRemoteRender&&(this.initial=!0,this.activeIndex=this.model.index,this.updateValues(),this.initRemoteRender=!1,this.initial=!1);this.model.allowFiltering&&!this.isTyped&&(this.actionCompleteData.isUpdated||(this.actionCompleteData={ulElement:n.cloneNode(!0),list:t,isUpdated:!0}),this.addNewItem(t,r),ej.isNullOrUndefined(this.itemData)||this.focusIndexItem());this.beforePopupOpen&&(this.renderPopup(),this.model.cssClass&&this.popupObj&&f(this.popupObj).addClass(this.model.cssClass))}},s.prototype.addNewItem=function(t,i){var s=this,u,e,r,o;ej.isNullOrUndefined(this.itemData)||ej.isNullOrUndefined(i)||(u=this.getItemData().value,e=t.some(function(n){return n[s.model.fields.value]===u}),e||(this.addItem(this.itemData),this.actionCompleteData.list.push(this.itemData),this.actionCompleteData.ulElement.appendChild(i)),r=void 0,o=this.actionCompleteData.ulElement,r=o.querySelectorAll("."+n.selected),r.length&&(f(r).removeClass(n.selected),r[0].removeAttribute("aria-selected")))},s.prototype.removeFocus=function(){var t=this.list.querySelectorAll("."+n.focus);t&&t.length&&f(t).removeClass(n.focus)},s.prototype.getItemData=function(){var t=this.model.fields.text?this.model.fields.text:"text",i=this.model.fields.value?this.model.fields.value:t,n={},r,u;return n=this.itemData,r=!ej.isNullOrUndefined(n)&&!ej.isNullOrUndefined(n[i])?n[i]:n,u=!ej.isNullOrUndefined(n)&&!ej.isNullOrUndefined(n[t])?n[t]:n,{value:r,text:u}},s.prototype.isValidLI=function(n){return n&&n.hasAttribute("role")&&n.getAttribute("role")==="option"},s.prototype.getSelectionPoints=function(){var n=this.inputElement;return{start:Math.abs(n.selectionStart),end:Math.abs(n.selectionEnd)}},s.prototype.updateSelectedItem=function(t,i,r){var u,o,e;this.removeSelection();f(t).addClass(n.selected);this.removeHover();u=this.getFormattedValue(t.getAttribute("data-value"));this.item=t;this.itemData=this.getDataByValue(u);this.initial||r||(this.isSelected=!0,o={e:i,item:this.item,itemData:this.itemData,isInteracted:i?!0:!1,text:this.itemData[this.model.fields.text],value:this.itemData[this.model.fields.value]},this._trigger("select",o));e=this.list.querySelector("."+n.focus);e&&f(e).removeClass(n.focus);t.setAttribute("aria-selected","true");this.activeIndex=this.getIndexByValue(u)},s.prototype.setHiddenValue=function(){if(ej.isNullOrUndefined(this.value()))this.hiddenElement.innerHTML="";else{var n=document.createElement("option");n.innerText=this.model.text;n.selected=!0;n.setAttribute("value",this.value());f(this.hiddenElement).append(n)}},s.prototype.detachChangeEvent=function(n){if(this.isSelected=!1,this.previousValue=this.value(),this.activeIndex=this.model.index,this.typedString=ej.isNullOrUndefined(this.model.text)?"":this.model.text,!this.initial){this.setHiddenValue();var t={e:n,item:this.item,itemData:this.itemData,isInteracted:n?!0:!1,value:this.value()};this._trigger("_change",{value:this.value()});this._trigger("change",t)}},s.prototype.getElementByValue=function(n){for(var i,r,f=this.getItems(),t=0,u=f;t<u.length;t++)if(i=u[t],this.getFormattedValue(i.getAttribute("data-value"))===n){r=i;break}return r},s.prototype.getElementByText=function(n){return this.getElementByValue(this.getValueByText(n))},s.prototype.listsearchLists=function(t){var i=this,r;this.isTyped=!0;this.activeIndex=null;this.filterInput.parentElement.querySelector("."+n.clearIcon)&&(r=this.filterInput.parentElement.querySelector("."+n.clearIcon),r.style.visibility=this.filterInput.value===""?"hidden":"visible");this.model.allowFiltering&&this._trigger("filtering",{text:this.filterInput.value,updateData:function(n,t,r){if(!ej.isNullOrUndefined(i.filterInput))if(i.beforePopupOpen=!0,f.trim(i.filterInput.value)===""){i.actionCompleteData.isUpdated=!1;i.isTyped=!1;i.onActionComplete(i.actionCompleteData.ulElement,i.actionCompleteData.list);i.isTyped=!0;i.isNotSearchList=!0}else i.isNotSearchList=!1,i.resetList(n,r,t)},event:t})},s.prototype.focusIndexItem=function(){var t=this.getItemData().value,n;this.activeIndex=this.getIndexByValue(t);n=this.list.querySelector('[data-value="'+t+'"]');this.selectedLI=n;this.activeItem(n);this.removeFocus()},s.prototype.activeItem=function(t){this.isValidLI(t)&&t.className.indexOf(n.selected)==-1&&(this.removeSelection(),f(t).addClass(n.selected),this.removeHover(),t.setAttribute("aria-selected","true"))},s.prototype.removeFillSelection=function(){if(this.isInteracted){var n=this.getSelectionPoints();this.inputElement.setSelectionRange(n.end,n.end)}},s.prototype.listsetValue=function(){var n=this.getItemData();return ej.isNullOrUndefined(n.value)?this.setInputValue(null,this.inputElement,this.model.showClearButton):this.setInputValue(n.text,this.inputElement,this.model.showClearButton),this.previousValue===n.value?(this.isSelected=!1,!0):(this.isSelected=this.initial?!1:!0,this.isSelectCustom=!1,!1)},s.prototype.listsetSelection=function(t,i){if(this.isValidLI(t)&&t.className.indexOf(n.selected)==-1&&this.updateSelectedItem(t,i,!1),this.list&&this.removeHover(),this.selectedLI=t,!this.setValue(i)){if(this.model.valueTemplate&&this.itemData!==null)this.valueTempElement||(this.valueTempElement=ej.buildTag("span."+n.value)[0],this.inputElement.parentElement.insertBefore(this.valueTempElement,this.inputElement),this.inputElement.style.display="none",this.inputWrapper.container.focus(),f(this.inputWrapper.container).addClass(n.inputFocus)),this.valueTempElement.innerHTML="",this.valueTempElement.innerHTML=this.model.valueTemplate;else this.inputElement.previousSibling===this.valueTempElement&&(f(this.valueTempElement).remove(),this.inputElement.style.display="block");if(this.isPopupOpen&&f(this.inputElement).attr({"aria-activedescendant":this.selectedLI?this.selectedLI.id:null}),!this.isPopupOpen&&!ej.isNullOrUndefined(t)||this.isPopupOpen&&!ej.isNullOrUndefined(i)&&(i.type!=="keydown"||i.type==="keydown"&&i.keyCode===13)){this.isSelectCustom=!1;this.onChangeEvent(i)}!this.isPopupOpen||ej.isNullOrUndefined(this.selectedLI)||this.itemData===null||i&&i.type==="click"||this.setScrollPosition(i)}},s.prototype.setScrollPosition=function(n){if(ej.isNullOrUndefined(n))this.scrollBottom(!0);else switch(n.keyCode){case 34:case 40:case 35:this.scrollBottom();break;default:this.scrollTop()}},s.prototype.scrollBottom=function(n){var r=this.list.offsetHeight,u=this.selectedLI.offsetTop+this.selectedLI.offsetHeight-this.list.scrollTop,i=this.list.scrollTop+u-r,t;i=n?i+parseInt(typeof getComputedStyle!="undefined"?getComputedStyle(this.list).paddingTop:this.list.currentStyle.paddingTop,10)*2:i;t=this.selectedLI.offsetTop+this.selectedLI.offsetHeight-this.list.scrollTop;t=this.model.fields.groupBy&&!ej.isNullOrUndefined(this.fixedHeaderElement)?t-this.fixedHeaderElement.offsetHeight:t;this.activeIndex===0?this.list.scrollTop=0:(u>r||!(t>0&&this.list.offsetHeight>t))&&(this.list.scrollTop=i)},s.prototype.scrollTop=function(){var n=this.selectedLI.offsetTop-this.list.scrollTop,i=this.selectedLI.offsetTop+this.selectedLI.offsetHeight-this.list.scrollTop,t;n=this.model.fields.groupBy&&!ej.isNullOrUndefined(this.fixedHeaderElement)?n-this.fixedHeaderElement.offsetHeight:n;t=this.selectedLI.offsetTop+this.selectedLI.offsetHeight-this.list.scrollTop;this.activeIndex===0?this.list.scrollTop=0:n<0?this.list.scrollTop=this.list.scrollTop+n:t>0&&this.list.offsetHeight>t||(this.list.scrollTop=this.selectedLI.offsetTop-(this.model.fields.groupBy&&!ej.isNullOrUndefined(this.fixedHeaderElement)?this.fixedHeaderElement.offsetHeight:0))},s.prototype.onChangeEvent=function(n){var t=this.getItemData(),i=this.isSelectCustom?null:this.activeIndex;this.value(t.value);this.model.index=i;this.model.text=t.text;this.isWatermark||(ej.isNullOrUndefined(this.model.text)?this.hiddenSpan.css("display","block"):this.hiddenSpan.css("display","none"));this.detachChangeEvent(n)},s.prototype.setPlaceholder=function(n,t){var i;i=f(t).parent()[0];this.isWatermark=f(document.createElement("input")).attr("placeholder","")[0].hasAttribute("placeholder");ej.isNullOrUndefined(n)||n===""?(t.removeAttribute("placeholder"),t.removeAttribute("aria-placeholder")):(f(t).attr({placeholder:n,"aria-placeholder":n}),this.isWatermark||(this.hiddenSpan=ej.buildTag("span.e-input e-placeholder ").insertAfter(t),this.hiddenSpan.text(n),this.hiddenSpan.css("display","block")))},s.prototype.setReadonly=function(n,t){n?f(t).attr({readonly:""}):t.removeAttribute("readonly")},s.prototype.setEnableRtl=function(){this.model.enableRtl?(f(this.inputElement.parentElement).addClass(n.rtl),this.popupObj&&f(this.popupObj).addClass(n.rtl)):(f(this.inputElement.parentElement).removeClass(n.rtl),this.popupObj&&f(this.popupObj).removeClass(n.rtl))},s.prototype.listrender=function(){var i,r,t;this.element[0].tagName==="INPUT"?this.inputElement=this.element[0]:(this.inputElement=ej.buildTag("input")[0],this.element[0].tagName!=="EJ-COMBOBOX"&&(this.element[0].style.display="none"),this.element[0].parentElement.insertBefore(this.inputElement,this.element[0]));this.inputWrapper=this.createInput({element:this.inputElement,buttons:[n.icon],properties:{readonly:this.model.readonly,showClearButton:this.model.showClearButton}});this.setPlaceholder(this.model.placeholder,this.inputElement);this.setCssClass(this.model.cssClass);this.setEnable();this.setEnableRtl();this.element[0].tagName==="EJ-COMBOBOX"?this.element[0].appendChild(this.inputWrapper.container):this.inputElement.parentElement.insertBefore(this.element[0],this.inputElement);i=this.element[0].getAttribute("name")?this.element[0].getAttribute("name"):this.element[0].getAttribute("id");this.hiddenElement=ej.buildTag("select",null,null,{name:i,"aria-hidden":"true","class":"e-ddl-hidden",tabindex:"-1"})[0];f(this.inputWrapper.container).prepend(this.hiddenElement);this.element[0].removeAttribute("name");this.inputWrapper.container.style.width=this.formatUnit(this.model.width);f(this.inputWrapper.container).addClass("e-ddl");this.wireEvent();this.tabIndex=this.element[0].hasAttribute("tabindex")?this.element[0].getAttribute("tabindex"):"0";this.element[0].removeAttribute("tabindex");r=this.element[0].getAttribute("id")?this.element[0].getAttribute("id"):"dropdownlist"+ej.getGuid("dropdownlist");this.element[0].id=r;this.inputElement.setAttribute("tabindex",this.tabIndex);f(this.inputElement).attr(this.getAriaAttributes());this.setHTMLAttributes();ej.isNullOrUndefined(this.model.index)||(this.activeIndex=this.model.index);ej.isNullOrUndefined(this.value())&&ej.isNullOrUndefined(this.activeIndex)&&ej.isNullOrUndefined(this.model.text)?this.element[0].tagName==="SELECT"&&this.element[0].options[0]&&(t=this.element[0],this.value(t.options[t.selectedIndex].value),this.model.text=ej.isNullOrUndefined(this.value())?null:t.options[t.selectedIndex].textContent,this.initValue()):this.initValue();this.model.enabled||(this.inputElement.tabIndex=-1);this.initial=!1;this.element[0].style.opacity=""},s.prototype.setHTMLAttributes=function(){var t,i,n,r,u;if(!ej.isNullOrUndefined(Object.keys)&&Object.keys(this.model.htmlAttributes).length)for(t=0,i=Object.keys(this.model.htmlAttributes);t<i.length;t++)n=i[t],n==="class"?this.setCssClass(this.model.htmlAttributes[n]):n==="disabled"&&this.model.htmlAttributes[n]==="disabled"?(this.model.enabled=!1,this.setEnable()):n==="readonly"&&this.model.htmlAttributes[n]==="readonly"?this._setModel({readonly:!0}):n==="style"?this.inputWrapper.container.setAttribute("style",this.model.htmlAttributes[n]):(r=["title","id","placeholder"],u=["name","required"],u.indexOf(n)>-1?this.hiddenElement.setAttribute(n,this.model.htmlAttributes[n]):r.indexOf(n)>-1?n==="placeholder"?this.setPlaceholder(this.model.htmlAttributes[n],this.inputElement):this.element[0].setAttribute(n,this.model.htmlAttributes[n]):this.inputWrapper.container.setAttribute(n,this.model.htmlAttributes[n]))},s.prototype.setCssClass=function(n){f(this.inputWrapper.container).addClass(n);this.popupObj&&f(this.popupObj).addClass(n)},s.prototype.setEnable=function(){this.model.enabled?(f(this.inputElement).removeAttr("disabled aria-disabled").removeClass(n.disable).attr("aria-disabled","false"),f(this.inputWrapper.container).removeClass(n.disable),this.inputElement.tabIndex=parseInt(this.tabIndex)):(f(this.inputElement).attr({disabled:"disabled","aria-disabled":"true"}).addClass(n.disable).attr("aria-disabled","true"),this.hidePopup(),f(this.inputWrapper.container).addClass(n.disable))},s.prototype.initValue=function(){this.renderList();this.model.dataSource instanceof ej.DataManager?this.initRemoteRender=!0:this.updateValues()},s.prototype.renderList=function(n){this.baserender(n);this.wireListEvents()},s.prototype.wireListEvents=function(){this._on(f(this.list),"click",this.onMouseClick);this._on(f(this.list),"mouseover",this.onMouseOver);this._on(f(this.list),"mouseout",this.removeHover)},s.prototype.onMouseClick=function(t){var u=t.target,i=f(u).closest("."+n.li)[0],r;this.isValidLI(i)&&(this.setSelection(i,t),r=100,this.closePopup(r),this.selectFocus=!1)},s.prototype.onMouseOver=function(t){var i=f(t.target).closest("."+n.li)[0];this.setHover(i)},s.prototype.setHover=function(t){this.model.enabled&&this.isValidLI(t)&&t.className.indexOf(n.hover)==-1&&(this.removeHover(),f(t).addClass(n.hover))},s.prototype.removeHover=function(){var t=this.list.querySelectorAll("."+n.hover);t&&t.length&&f(t).removeClass(n.hover)},s.prototype.baserender=function(t){this.list=ej.buildTag("div."+n.content,"",{},{tabindex:"0"})[0];f(this.list).addClass("e-dropdownbase");var i=this.element[0].querySelector("select>optgroup");(this.model.fields.groupBy||!ej.isNullOrUndefined(i))&&this._on(f(this.list),"scroll",this.setFloatingHeader);this.setEnableRtl();this.setEnable();t||this.initialize()},s.prototype.initialize=function(){var n,t;this.bindEvent=!0;this.element[0].tagName==="SELECT"?(n=void 0,this.model.dataSource instanceof Array&&(n=this.model.dataSource),t=this.model.dataSource instanceof Array?n.length>0?!0:!1:ej.isNullOrUndefined(this.model.dataSource)?!1:!0,t||this.renderItemsBySelect()):this.setListData(this.model.dataSource,this.model.fields,this.model.query)},s.prototype.renderItemsBySelect=function(){var u=this.element[0],n={value:"value",text:"text"},t=[],f=u.querySelectorAll("select>optgroup"),s=u.querySelectorAll("select>option"),i,e,r,o,h;if(this.getJSONfromOption(t,s,n),f.length){for(i=0;i<f.length;i++)e=f[i],r={},r[n.text]=e.label,r.isHeader=!0,o=e.querySelectorAll("option"),t.push(r),this.getJSONfromOption(t,o,n);h=u.querySelectorAll("select>option")}this.model.fields.text=n.text;this.model.fields.value=n.value;this.resetList(t,n)},s.prototype.getJSONfromOption=function(n,t,i){for(var r,f,u=0,e=t;u<e.length;u++)r=e[u],f={},f[i.text]=r.innerText,f[i.value]=r.getAttribute(i.value)?r.getAttribute(i.value):r.innerText,n.push(f)},s.prototype.setFloatingHeader=function(t){ej.isNullOrUndefined(this.fixedHeaderElement)&&(this.fixedHeaderElement=ej.buildTag("div."+n.fixedHead)[0],this.list.querySelector("li").className.indexOf(n.group)==-1&&(this.fixedHeaderElement.style.display="none"),f(this.list).prepend(this.fixedHeaderElement),this.setFixedHeader());this.scrollStop(t)},s.prototype.setFixedHeader=function(){var t,i;this.list.parentElement.style.display="block";t=this.liCollections[0].offsetWidth;this.fixedHeaderElement.style.width=t.toString()+"px";f(this.fixedHeaderElement).css({zIndex:10});i=this.ulElement.querySelector("."+n.group);this.fixedHeaderElement.innerHTML=i.innerHTML},s.prototype.scrollStop=function(t){for(var u,f=t.target,e=parseInt(typeof getComputedStyle!="undefined"?getComputedStyle(this.liCollections[0],null).getPropertyValue("height"):this.liCollections[0].currentStyle.height,10),o=Math.round(f.scrollTop/e),r=this.ulElement.querySelectorAll("li"),i=o;i>-1;i--)if(!ej.isNullOrUndefined(r[i])&&r[i].className.indexOf(n.group)>-1){u=r[i];this.fixedHeaderElement.innerHTML=u.innerHTML;this.fixedHeaderElement.style.display="block";break}else this.fixedHeaderElement.style.display="none"},s.prototype.getFormattedValue=function(n){return this.listData&&this.listData.length&&(typeof this.listData[0][this.model.fields.value?this.model.fields.value:"value"]=="number"||typeof this.listData[0]=="number")?parseInt(n,10):n},s.prototype.baseonActionComplete=function(t,i){this.listData=i;this.list.innerHTML="";this.list.appendChild(t);this.liCollections=this.list.querySelectorAll("."+n.li);this.ulElement=this.list.querySelector("ul");this.postRender(this.list,i,this.bindEvent)},s.prototype.getIndexByValue=function(n){for(var i,r=this.getItems(),t=0;t<r.length;t++)if(r[t].getAttribute("data-value")===n.toString()){i=t;break}return i},s.prototype.getItems=function(){return this.ulElement.querySelectorAll("."+n.li)},s.prototype.postRender=function(t,i){var r=t.querySelector("."+n.li),u=t.querySelector("."+n.selected);r&&!u&&f(r).addClass(n.focus);i.length<=0?(this.l10nUpdate(),f(t).addClass(n.noData)):f(t).removeClass(n.noData);this.model.groupTemplate&&this.renderGroupTemplate(t)},s.prototype.renderGroupTemplate=function(t){var i,u,r;if(this.model.fields.groupBy!==null&&this.model.dataSource||this.element[0].querySelector("."+n.group)){var e=this.model.dataSource,o={},s=t.querySelectorAll("."+n.group);for(e=this.listGroupDataSource(e,this.model.fields),i=0,u=t.querySelectorAll("."+n.group);i<u.length;i++)r=u[i],o[this.model.fields.groupBy]=r.textContent,r.innerHTML="",f(r).append(this.getTemplatedString(this.model.groupTemplate,o))}},s.prototype.getTemplatedString=function(n,t){for(var i=n,r=i.indexOf("${"),u=i.indexOf("}"),f,e;r!=-1&&u!=-1;)f=i.substring(r,u+1),e=f.replace("${","").replace("}",""),i=i.replace(f,ej.getObject(e,t).toString()),r=i.indexOf("${"),u=i.indexOf("}");return i},s.prototype.listGroupDataSource=function(n,t){var e=new ej.DataManager(n).executeLocal((new ej.Query).group(t.groupBy)),i,u,r,f;for(n=[],i=0;i<e.length;i++)for(u=e[i].items,r={},r[t.text]=e[i].key,r.isHeader=!0,r.items=u,n.push(r),f=0;f<u.length;f++)n.push(u[f]);return n},s.prototype.updateLocalConstant=function(){this.localizedLabels=ej.getLocalizedConstants("ej.ComboBox",this.model.locale)},s.prototype.getLocalizedLabels=function(n){return this.localizedLabels[n]===undefined?ej.ComboBox.Locale["en-US"][n]:this.localizedLabels[n]},s.prototype.l10nUpdate=function(n){if(this.updateLocalConstant(),this.model.noRecordsTemplate!=="No Records Found"||this.model.actionFailureTemplate!=="The Request Failed"){var t=n?this.model.actionFailureTemplate:this.model.noRecordsTemplate;this.list.innerHTML=t}else this.list.innerHTML=n?this.getLocalizedLabels("actionFailureTemplate"):this.getLocalizedLabels("noRecordsTemplate")},s.prototype.getTextByValue=function(n){var r,t=this.listData,u=this.getFields(),i,f,e;if(typeof t[0]=="string"||typeof t[0]=="number"){for(i=0,f=t;i<f.length;i++)if(e=f[i],e.toString()===n.toString()){r=e.toString();break}}else t.filter(function(t){if(!ej.isNullOrUndefined(t[u.value])&&t[u.value].toString()===n.toString())return r=t[u.text],!0});return r},s.prototype.getFields=function(){var n=this.model.fields.text?this.model.fields.text:"text",t=this.model.fields.value?this.model.fields.value:n,i=this.model.fields.tooltip?this.model.fields.tooltip:null;return{value:t.toString(),text:n,tooltip:i}},s.prototype.resetList=function(n,t,i){this.list&&this.setListData(n,t,i)},s.prototype.setListData=function(n,t,i){var r=this,u;if(t=t?t:this.model.fields,this.isActive=!0,n instanceof ej.DataManager)this.isRequested=!0,this._trigger("actionBegin"),n.executeQuery(i?i:this.model.query?this.model.query:new ej.Query).done(function(n){if(r._trigger("actionComplete",n),!n.cancel){var i=n.result;u=r.renderItems(i,t);r.onActionComplete(u,i,n);r.isRequested=!1;r._trigger("dataBound",{items:i,e:n})}}).fail(function(n){r.isRequested=!1;r.onActionFailure(n)});else{var o=new ej.DataManager(n),f=o.executeLocal(i?i:this.model.query?this.model.query:new ej.Query),e={cancel:!1,result:f};if(this._trigger("actionComplete",e),e.cancel)return;u=this.renderItems(f,t);this.onActionComplete(u,f);this._trigger("dataBound",{items:f})}},s.prototype.onActionFailure=function(t){this.liCollections=[];this._trigger("actionFailure",t);this.l10nUpdate(!0);f(this.list).addClass(n.noData);this.renderPopup()},s.prototype.renderItems=function(n,t){var r,i=n;return t.value=ej.isNullOrUndefined(t.value)?t.text:t.value,this.model.fields=t,this.model.itemTemplate&&n?(i&&t.groupBy?(i=this.listgroupDataSource(i,t),f(this.list).addClass("e-combobox-group")):i&&this.model.sortOrder!=="None"&&(i=new ej.DataManager(i).executeLocal(this.model.sortOrder.toLowerCase()=="descending"?(new ej.Query).sortByDesc(this.model.fields.text):(new ej.Query).sortBy(this.model.fields.text))),this.model.fields.value&&!t.text?t.text=t.value:!t.value&&t.text&&(t.value=t.text),r=this.listrenderContentTemplate(this.model.itemTemplate,i,t)):(i&&this.model.sortOrder!=="None"&&(n=new ej.DataManager(i).executeLocal(this.model.sortOrder.toLowerCase()=="descending"?(new ej.Query).sortByDesc(this.model.fields.text):(new ej.Query).sortBy(this.model.fields.text))),i&&t.groupBy&&(n=this.listGroupDataSource(i,t),f(this.list).addClass("e-combobox-group")),r=this.createListItems(n,t.text!==null||t.value!==null?{fields:t,showIcon:ej.isNullOrUndefined(t.iconCss)?!1:!0}:{fields:{value:"text"}})),r},s.prototype.listgroupDataSource=function(n,t){var e=new ej.DataManager(n).executeLocal((new ej.Query).group(t.groupBy)),i,u,r,f;for(n=[],i=0;i<e.length;i++)for(u=e[i].items,r={},r[t.text]=e[i].key,r.isHeader=!0,r.items=u,n.push(r),f=0;f<u.length;f++)n.push(u[f]);return n},s.prototype.listrenderContentTemplate=function(t,i,r){for(var a,s=ej.buildTag("ul."+n.ul,"",{},{role:"presentation"})[0],h=[],o=0,c=i;o<c.length;o++){var e=c[o],l=e.isHeader,u=ej.buildTag("li."+(l?n.group:n.li),"",{},{role:"presentation"})[0];l?u.innerText=e[r.text]:(f(u).append(this.getTemplatedString(t,e)),a=e[r.value],u.setAttribute("data-value",a),u.setAttribute("role","option"),r.tooltip&&u.setAttribute("title",e[r.tooltip]));h.push(u)}return f(s).append(h),s},s.prototype.createListItems=function(i,u){var c,e,o,l;if(typeof i[0]=="string"){for(c=[],e=0;e<i.length;e++)c.push(this.generateSingleLevelListItem(i[e],i[e]));return ej.buildTag("ul."+n.ul,"",{},{role:"presentation"}).append(c)[0]}var s=f.extend({},r,u),h=f.extend({},t,s.fields),a=[];for(e=0;e<i.length;e++)o=i[e],l=[],s.showIcon&&l.push(ej.buildTag("span.e-list-icon "+o[h.iconCss])[0]),a.push(this.generateSingleLevelListItem(o[h.text],o[h.value],o[h.tooltip],s.itemClass,l,o.hasOwnProperty("isHeader")&&o.isHeader?!0:!1));return ej.buildTag("ul."+n.ul+" "+s.listClass,"",{},{role:"presentation"}).append(a)[0]},s.prototype.generateSingleLevelListItem=function(t,i,r,u,e,o){var s=ej.buildTag("li."+(o===!0?n.group:n.li)+" "+u,"",{role:o===!0?"group":"presentation"})[0];return o?s.innerText=t:(s.setAttribute("data-value",i),s.setAttribute("role","option"),r&&s.setAttribute("title",r),e&&f(s).append(e),s.appendChild(document.createTextNode(t))),s},s.prototype.getDataByValue=function(n){var i,u,r,f,t;if(typeof this.listData[0]=="string"||typeof this.listData[0]=="number"){for(i=0,u=this.listData;i<u.length;i++)if(t=u[i],t===n)return t}else for(r=0,f=this.listData;r<f.length;r++)if(t=f[r],t[this.model.fields.value?this.model.fields.value:"value"]===n)return t;return null},s.prototype.search=function(n,t,i,r){var a=t,e,l,s,u,h,o,c;if(r=r!==undefined&&r!==null?r:!0,e={item:null,index:null},n.length){for(l=n.length,s=r?n.toLowerCase():n,u=0,h=a;u<h.length;u++)if(o=h[u],c=(r?f(o).text().toLowerCase():f(o).text()).replace(/^\s+|\s+$/g,""),i==="Equal"&&c===s||i==="StartsWith"&&c.substr(0,l)===s)return e.item=o,e.index=u,{item:o,index:u};return e}return e},s.prototype.setInputValue=function(t,i,r){if(ej.isNullOrUndefined(t)||(i.value=t),!ej.isNullOrUndefined(r)&&r){var u=f(i).parent()[0],e=f(u).find("."+n.clearIcon)[0];i.value&&u.className.indexOf("e-input-focus")>-1?f(e).removeClass(n.clearIconHide):f(e).addClass(n.clearIconHide)}},s.prototype.addItem=function(t,i){var c,y,v,l,u,s,e,h,o,r,a;for(this.list&&this.list.textContent!==this.model.noRecordsTemplate||this.renderList(),c=this.getItems().length,y=this.list.querySelector("."+n.selected),t=t instanceof Array?t:[t],v=t instanceof Array?t:[t],l=v.length,u=ej.isNullOrUndefined(i)||i<0||i>c-1?c:i,s=this.getFields(),e=[],r=0;r<l;r++)h=t[r],o=ej.buildTag("li#option-add-"+r+"."+n.li)[0],o.setAttribute("data-value",h[s.value]),o.setAttribute("role","option"),s.tooltip&&o.setAttribute("title",h[s.tooltip]),o.appendChild(document.createTextNode(h[s.text])),e.push(o),this.listData.push(h);if(c===0&&ej.isNullOrUndefined(this.list.querySelector("ul")))this.list.innerHTML="",this.list.appendChild(this.ulElement),f(this.ulElement).append(e);else for(r=0;r<l;r++)this.liCollections[u]?this.liCollections[u].parentNode.insertBefore(e[r],this.liCollections[u]):this.ulElement.appendChild(e[r]),a=[].slice.call(this.liCollections),a.splice(u,0,e[r]),this.liCollections=a,u+=1},s}(ej.WidgetBase);window.ej.widget("ejComboBox","ej.ComboBox",new o);window.ejComboBox=null})(jQuery);ej.ComboBox.Locale=ej.ComboBox.Locale||{};ej.ComboBox.Locale["default"]=ej.ComboBox.Locale["en-US"]={noRecordsTemplate:"No Records Found",actionFailureTemplate:"The Request Failed"};var t={id:"id",text:"text",value:"value",isChecked:"isChecked",enabled:"enabled",iconCss:"icon",tooltip:null,htmlAttributes:null,imageAttributes:null,imageUrl:"imageUrl",groupBy:null},r={showIcon:!1,fields:t,listClass:"",itemClass:"",sortOrder:"None",template:null,groupTemplate:null},n={input:"e-input-group",mobileFilter:"e-ddl-device-filter",disable:"e-disabled",hover:"e-hover",selected:"e-active",rtl:"e-rtl",li:"e-list-item",focus:"e-item-focus",inputFocus:"e-input-focus",icon:"e-input-group-icon e-ddl-icon",iconAnimation:"e-icon-anim",value:"e-input-value",device:"e-ddl-device",filterInput:"e-input-filter",group:"e-list-group-item",ul:"e-list-parent e-ul",noData:"e-nodata",fixedHead:"e-fixed-head",clearIcon:"e-clear-icon",clearIconHide:"e-clear-icon-hide",popupFullScreen:"e-popup-full-page",footer:"e-ddl-footer",header:"e-ddl-header",backIcon:"e-input-group-icon e-back-icon e-icons",filterBarClearIcon:"e-input-group-icon e-clear-icon e-icons",filterParent:"e-filter-parent",content:"e-content"},u={container:null,buttons:[]}});
