/*!
*  filename: ej.mobile.application.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./ej.mobile.core.min"],n):n()})(function(){(function(){ejPageAnimation={slideLeft:["slideLeftOut","slideRightIn","slideRightOut","slideLeftIn"],slideRight:["slideRightOut","slideLeftIn","slideLeftOut","slideRightIn"],slideUp:["slideTopOut","slideBottomIn","slideBottomOut","slideTopIn"],slideDown:["slideBottomOut","slideTopIn","slideTopOut","slideBottomIn"],flipIn:["flipYLeftOut","flipYRightIn","flipYRightOut","flipYLeftIn"],flipOut:["flipYRightOut","flipYLeftIn","flipYLeftOut","flipYRightIn"],pop:["fadeZoomOut","fadeZoomIn","fadeZoomOut","fadeZoomIn"]};App={window:$(window),document:$(document),html:$("html"),head:$("head"),container:$("body"),model:{},_isBack:!1,_initialHeap:!0,currentStateId:null,checkStateId:null,activeSubPage:null,activePage:null,renderEJMControlsByDef:!0,applyMobileStyles:!0,defaults:{enableBrowserHistory:!0,forceHash:!1,enableAjax:"auto",ajaxOptions:{async:!0,crossDomain:!0,data:null,dataType:"html",contentType:"text/plain",method:"GET"},hashValue:null,enableCache:!1,enableAnimation:!0,animateOptions:{transitionType:"slideLeft",transitionDuration:"400",easing:"ease"},toPageClass:"",isReverseAnimation:!1,customOption:null,viewTransfered:null,viewBeforeTransfer:null,ajaxSuccess:null,ajaxFailure:null,refreshed:null,onBack:null,onHistoryBack:null},_isUrl:function(n){return/[\.|\/|\#]/g.test(n)},_getModel:function(n){return n.data("historyModel")},_getEventArguments:function(n){return{heap:n.data("heap")?n.data("heap"):{},historyState:history.state?{target:history.state.target,templateUrl:history.state.targetUrl,templateContent:history.state.targetContent,userModel:history.state.userModel,uid:history.state.uid}:{},renderingPage:App._renderingPage}},transferPage:function(n,t,i){var n,r,h,e,f,u,o,s;if(ej.mobile.WaitingPopup.show(),n=$("#"+n),n.addClass("page-container").data({historyModel:$.extend(!1,App.defaults,i?i:{})}),r=App._getModel(n),h=App._isUrl(t),n.children().first().attr("data-url")){for(currentPageUrl=n.children().first().attr("id"),newpageurl=t,e=[],f=0;f<n.data("heap").length;f++)e.push(n.data("heap")[f].href);u=e.indexOf(newpageurl);u>-1?(o=n.data("heap")[u],n.data("heap").splice(u,1),n.data("heap").push(o),App._loadExistingView(n,t,r),r.enableBrowserHistory&&(this._trimmedContent=$.trim(n.children().html()),App._initialHeap&&(App._initialTargetId=$("#"+n[0].id),App._initialHeap=!1),o.href==t&&u==n.data("heap").length-1?history.replaceState({target:n[0].id,targetUrl:t,targetContent:r.enableCache?this._trimmedContent:"",userModel:i,uid:App.currentStateId},t,r.forceHash?"#"+(r.hashValue?r.hashValue:t):App._isUrl(t)?t:"#"+t):history.pushState({target:n[0].id,targetUrl:t,targetContent:r.enableCache?this._trimmedContent:"",userModel:i,uid:App.currentStateId},t,r.forceHash?"#"+(r.hashValue?r.hashValue:t):App._isUrl(t)?t:"#"+t))):App._getContent(n,t,r,!0)}else s=$("<div class='e-m-hidden' style='z-index:1'><\/div>"),s.attr({id:"page_"+Math.floor(Math.random()*1e3+1)+"_container"}),n.wrapInner(s),App._pushHistory(n,t,n.html(),i),App._getContent(n,t,r,!0);App.currentStateId=history.state?history.state.uid+1:1},_updateContent:function(n,t,i,r){var u=App._getModel(n),o=App._isUrl(t)?"template_"+Math.floor(Math.random()*1e3+1):t,f,e;App._trimmedContent=$.trim(r);n.css({overflow:"hidden"});f=$("<div id="+o+"_container class='e-m-hidden' data-url="+t+"><\/div>");f.html(App._trimmedContent);e=$("<div><\/div>");e.append(f.clone());App._pushHistory(n,t,e.html(),i);e.remove();App._renderingPage=f;n.append(f);App._animatePages(n,u.isReverseAnimation);u.enableBrowserHistory&&(App._initialHeap&&(App._initialTargetId=$("#"+n[0].id),App._initialHeap=!1),history.pushState({target:n[0].id,targetUrl:t,targetContent:u.enableCache?App._trimmedContent:"",userModel:i,uid:App.currentStateId},t,u.forceHash?"#"+(u.hashValue?u.hashValue:t):App._isUrl(t)?t:"#"+t));App._currentTarget=n},_animatePages:function(n,t){var i=App._getModel(n),r;typeof i.animateOptions.transitionType=="string"&&(i.animateOptions.transitionType=ejPageAnimation[i.animateOptions.transitionType]);typeof i.animateOptions.easing=="string"&&(i.animateOptions.easing=ejAnimation.Easing[i.animateOptions.easing]);App._page1=n.children().first().addClass("page");App._page2=n.children().last().addClass("page "+i.toPageClass);i.viewBeforeTransfer&&App._trigger("viewBeforeTransfer",App._getEventArguments(n),n);r=App;ej.widget.init(App._page2);i.enableAnimation?(n.addClass("e-m-intransition"),App._page1.ejAnimation("stop"),App._page2.ejAnimation("stop"),t?(r._page1.removeClass("e-m-hidden").ejAnimation(i.animateOptions.transitionType[2],i.animateOptions.transitionDuration,i.animateOptions.easing),r._page2.removeClass("e-m-hidden").ejAnimation(i.animateOptions.transitionType[3],i.animateOptions.transitionDuration,i.animateOptions.easing).done($.proxy(function(t){t.siblings().remove();ej.mobile.WaitingPopup.hide();n.removeClass("e-m-intransition");this._page2.css("z-index","").removeClass("page");i.viewTransfered&&App._trigger("viewTransfered",App._getEventArguments(n),n)},r))):(r._page1.removeClass("e-m-hidden").ejAnimation(i.animateOptions.transitionType[0],i.animateOptions.transitionDuration,i.animateOptions.easing),r._page2.removeClass("e-m-hidden").ejAnimation(i.animateOptions.transitionType[1],i.animateOptions.transitionDuration,i.animateOptions.easing).done($.proxy(function(t){t.siblings().remove();ej.mobile.WaitingPopup.hide();n.removeClass("e-m-intransition");this._page2.css("z-index","").removeClass("page");i.viewTransfered&&App._trigger("viewTransfered",App._getEventArguments(n),n)},r)))):(n.children().not(":last").remove(),App._page2.removeClass("e-m-hidden page").css("z-index",""),ej.mobile.WaitingPopup.hide(),i.viewTransfered&&App._trigger("viewTransfered",App._getEventArguments(n),n));App._isBack=!1},_pushHistory:function(n,t,i,r){var u=App._getModel(n),f=n.data("heap"),e=f?f.length:0;currentPageUrl=u.enableAjax?t:n.children().first().attr("id");e==0?n.data("heap",[{target:n,href:n.children().first().attr("id"),content:i,model:r}]):n.data("heap").push({target:n,href:t,content:u.enableCache?i:"",model:r})},_loadExistingView:function(n,t,i){var f=App._getModel(n),r=n.data("heap"),u=r.length;heapLastContent=r[u-1].content;heapLastUrl=r[u-1].href;heapModel=r[u-1].model;heapLastContent!=""?(App._renderingPage=$(heapLastContent),n.append(App._renderingPage),App._animatePages(n,f.isReverseAnimation)):App._getContent(n,heapLastUrl,heapModel,!1);(i.refreshed?i.refreshed:f.refreshed)&&App._trigger("refreshed",App._getEventArguments(n),n)},_getContent:function(n,t,i,r){var u=App._getModel(n),e,o,f;App._isUrl(t)?(proxy=App,$.ajax({url:t,dataType:i.dataType?i.dataType:u.ajaxOptions.dataType,crossDomain:i.crossDomain?i.crossDomain:u.ajaxOptions.crossDomain,contentType:i.contentType?i.contentType:u.ajaxOptions.contentType,async:!1,data:i.data?i.data:u.ajaxOptions.data,method:i.method?i.method:u.ajaxOptions.method,success:function(f){var o,s,e;u.ajaxSuccess&&App._trigger("ajaxSuccess",App._getEventArguments(n),n);o=/<body[^>]*>((.|[\n\r])*)<\/body>/im;templateContent=o.exec(f)?o.exec(f)[1]:f;r?proxy._updateContent(n,t,i,templateContent):(s=proxy._isUrl(t)?"template_"+Math.floor(Math.random()*1e3+1):t,e=$("<div id="+s+"_container class='e-m-hidden' data-url="+t+"><\/div>"),e.append($.trim(templateContent)),App._renderingPage=e,n.append(e),proxy._animatePages(n,App._isBack))},error:function(){ej.mobile.WaitingPopup.hide();u.ajaxFailure&&App._trigger("ajaxFailure",App._getEventArguments(n),n)}})):(e=$("#"+t),templateContent=e.html(),r?App._updateContent(n,t,i,templateContent):(o=App._isUrl(t)?"template_"+Math.floor(Math.random()*1e3+1):t,f=$("<div id="+o+"_container class='e-m-hidden' data-url="+t+"><\/div>"),f.append($.trim(templateContent)),App._renderingPage=f,n.append(f),App._animatePages(n,u.isReverseAnimation)))},renderEJMControls:function(n){var t=n?n:App.activePage;App.renderEJMControlsByDef&&(t.find("input[type='button']:not([data-role]):attrNotStartsWith('ejm-'),input[type='submit']:not([data-role]):attrNotStartsWith('ejm-'),input[type='reset']:not([data-role]):attrNotStartsWith('ejm-')").attr("data-role","ejmbutton"),t.find("a:not([data-role]):attrNotStartsWith('ejm-')").attr("data-role","ejmactionlink"),t.find("input:not([data-role]):not([type]):attrNotStartsWith('ejm-'),input[type='text']:not([data-role]):attrNotStartsWith('ejm-'),input[type='tel']:not([data-role]):attrNotStartsWith('ejm-'),input[type='email']:not([data-role]):attrNotStartsWith('ejm-'),input[type='password']:not([data-role]):attrNotStartsWith('ejm-')").attr("data-role","ejmtextbox"),t.find("input[type='checkbox']:not([data-role]):attrNotStartsWith('ejm-')").filter(function(){if(!$(this).closest("[data-role='ejmgroupbutton']").length)return this}).attr("data-role","ejmcheckbox"),t.find("input[type='radio']:not([data-role]):attrNotStartsWith('ejm-')").filter(function(){if(!$(this).closest("[data-role='ejmgroupbutton']").length)return this}).attr("data-role","ejmradiobutton"),t.find("input[type='date']:not([data-role]):attrNotStartsWith('ejm-')").attr("data-role","ejmdatepicker"),t.find("input[type='time']:not([data-role]):attrNotStartsWith('ejm-')").attr("data-role","ejmtimepicker"))},back:function(n){var i,e,r,t,u,o;if(typeof n=="undefined")if(i=$("<div class='e-m-hidden'><\/div>"),history.state){if(ej.isNullOrUndefined(history.state.target))return;App._currentTarget=n=$("#"+history.state.target);r=App._getModel(n);App._currentTarget.data({historyModel:$.extend(!1,App.defaults,history.state.userModel?history.state.userModel:{})});history.state.targetContent==""?App._getContent(n,history.state.targetUrl,r,!1):(e=App._isUrl(history.state.targetUrl)?"template_"+Math.floor(Math.random()*1e3+1)+"_container":history.state.targetUrl,i.attr({id:e,"page-url":history.state.targetUrl}),i.html(history.state.targetContent),App._renderingPage=i,n.append(i),App._animatePages(App._currentTarget,r.isReverseAnimation));App.model.onHistoryBack&&App._trigger("onHistoryBack",App._getEventArguments(n),n);App.checkStateId=history.state&&history.state.uid}else r=App._getModel(App._initialTargetId),i.attr("id",App._initialTargetId.data("heap")[0].href+"_container"),i.html(App._initialTargetId.data("heap")[0].content),App._renderingPage=i,App._initialTargetId.append(i),App._animatePages(App._initialTargetId,r.isReverseAnimation),App.model.onHistoryBack&&App._trigger("onHistoryBack",App._getEventArguments(App._initialTargetId),n);else if(App._isBack=!0,t=$("#"+n).data("heap"),t&&t.length){if(u=$("#"+n),o=t.pop(),t.length){var f=t[t.length-1].content,s=t[t.length-1].href,h=t[t.length-1].model;f!=""||t.length<=1?(App._renderingPage=f,u.append(f),App._animatePages($("#"+n),App._isBack)):App._getContent(u,s,h,!1)}App.model.onBack&&App._trigger("onBack",App._getEventArguments($("#"+n)),$("#"+n))}else return!1},_trigger:function(n,t,i){var f=App._getModel(i),r,e,u,o,s;if(f)return(r=null,o={},t=$.extend({target:i},t),$.extend(o,t),n in f&&(r=f[n]),r&&(typeof r=="string"&&(r=ej.util.getObject(r,window)),$.isFunction(r)&&(u=ej.event(n,f,t),e=r.call(App,u),t&&$.extend(t,u),u.cancel||!ej.isNullOrUndefined(e))))?e===!1||u.cancel:(s=Boolean(t),t=t||{},t.originalEventType=n,t.type=n,u=$.Event(t.type,ej.event(t.type,f,t)),s&&$.extend(t,u),u.cancel)}}})();$(function(){App.applyMobileStyles&&$("body").find("[data-role*='ejm']").length>0&&$("body").addClass("e-m-user-select e-m-viewport e-m-"+ej.getRenderMode());App._currentPage=App.activeSubPage=App.activePage=$("body");App.window.on("popstate",function(n){App._isBack=!0;App.checkStateId&&n.originalEvent.state&&n.originalEvent.state.uid>=App.checkStateId&&(App._isBack=!1);App.back()})})});
