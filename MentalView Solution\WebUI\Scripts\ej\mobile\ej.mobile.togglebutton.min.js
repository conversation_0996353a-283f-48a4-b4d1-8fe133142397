/*!
*  filename: ej.mobile.togglebutton.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function(n,t){t.widget("ejmToggleButton","ej.mobile.ToggleButton",{_setFirst:!0,_rootCSS:"e-m-tbutton",defaults:{cssClass:"",toggleState:!0,renderMode:"auto",touchStart:null,touchEnd:null,change:null,enabled:!0,enablePersistence:!1},dataTypes:{toggleState:"boolean",enabled:"boolean",enablePersistence:"boolean"},_init:function(){this._renderControl();this._createDelegates();this._wireEvents(!1);this.model.enabled?this.enable():this.disable()},_renderControl:function(){var r,i,u,f;t.setRenderMode(this);r=this.model;i=this.element;i.addClass("e-m-tbutton "+r.cssClass+" e-m-"+r.renderMode);this._input=t.buildTag("input",{},{},{name:n(i).attr("id"),type:"checkbox","data-role":"none"});u=this._input;this._slider=t.buildTag("div.e-m-tslider");f=this._slider;r.toggleState?(u.attr("checked","checked").attr("value",!0),f.addClass("e-m-state-active"),i.addClass("e-m-state-active"),this._onState()):(u.attr("value",!1),this._offState());i.append(u).append(f)},_createDelegates:function(){this._touchStartHandler=n.proxy(this._touchStart,this);this._touchEndHandler=n.proxy(this._touchEnd,this);this._touchMoveHandler=n.proxy(this._touchMove,this)},_wireEvents:function(n){this._createDelegates();t.listenTouchEvent(this.element,t.startEvent(),this._touchStartHandler,n)},_touchStart:function(n){var u,r,f,i,e;t.blockDefaultActions(n);u=n.touches?n.touches[0]:n;this._startPoint=u.pageY;r=this._slider;f=this.element;this.maxMove=f.width()-r.width();i=this.model;this._flag=!0;this._startstate=i.toggleState;this._startTime=n.timeStamp||Date.now();this._startPos=n.pageX;t.isTouchDevice()&&(n=u);(i.renderMode=="windows"||i.renderMode=="flat")&&(this.maxMove=this.maxMove-5);r.addClass("e-m-touchactive");this._position={x:n.pageX,y:n.pageY};this.startValue=this._getNormalValue(this._position);this._handleX=r.offset().left-f.offset().left;t.listenEvents([document,document],[t.endEvent(),t.moveEvent()],[this._touchEndHandler,this._touchMoveHandler],!1);e={state:i.toggleState};i.touchStart&&this._trigger("touchStart",e)},_touchMove:function(n){var r,u,i;t.blockDefaultActions(n);this._endPoint=(n.touches?n.touches[0]:n).pageY;this._startPoint!=this._endPoint&&(r=this.model,this._flag=!1,t.isTouchDevice()&&(n=n.touches?n.touches[0]:n),this._position={x:n.pageX,y:n.pageY},this._curValue=this._getNormalValue(this._position)-this.startValue,u=this._curValue,i=u+this._handleX,this._endTime=n.timeStamp||Date.now(),this._endPos=n.pageX,r.renderMode=="flat"||r.renderMode=="windows"?i>=0&&i<=this.maxMove&&this._translateContent(this._slider,i+2):i<this.maxMove/2?this._offState():this._onState())},_touchEnd:function(n){var r,u,i,e,f;t.blockDefaultActions(n);r=this._slider;u=this.element;r.removeClass("e-m-touchactive");i=this.model;t.listenEvents([document,document],[t.endEvent(),t.moveEvent()],[this._touchEndHandler,this._touchMoveHandler],!0);t.isTouchDevice()&&(n=n.changedTouches?n.changedTouches[0]:n);e=r.offset().left-u.offset().left;this._delTime=this._endTime-this._startTime;this._delPos=this._startstate?-(this._endPos-this._startPos):this._endPos-this._startPos;(i.renderMode==="ios7"||i.renderMode==="windows")&&this._delTime<300&&this._delPos>10?this._startstate?this._offState():this._onState():this._flag?r.hasClass("e-m-state-active")?this._offState():this._onState():e<this.maxMove/2?this._offState():this._onState();this._input.attr("checked")?(r.addClass("e-m-state-active"),u.addClass("e-m-state-active")):(r.removeClass("e-m-state-active"),u.removeClass("e-m-state-active"));f={state:i.toggleState};i.touchEnd&&this._trigger("touchEnd",f);i.change&&this._startstate!=i.toggleState&&this._trigger("change",f)},_getNormalValue:function(n){var t,i,r,u;return t=n.x-this.element.offset().left,i=t/this.element[0].offsetWidth,r=this.element.width(),u=i*r,this._trimValue(u)},_trimValue:function(n){var t=1,i=n%t,r=n-i;return Math.abs(i)*2>=t&&(r+=i>0?t:-t),parseFloat(r.toFixed(5))},_offState:function(){var t=this.model,n=this._slider;t.toggleState=!1;this._input.removeAttr("checked","checked").attr("value",!1);this._translateContent(n,0);n.removeClass("e-m-state-active");this.element.removeClass("e-m-state-active")},_onState:function(){var n=this.model,t=this._slider,i;n.toggleState=!0;this._input.attr("checked","checked").attr("value",!0);i=n.renderMode=="flat"||n.renderMode=="windows"?43:49;this._translateContent(t,i);t.addClass("e-m-state-active");this.element.addClass("e-m-state-active");(n.renderMode=="ios7"||n.renderMode=="android")&&this._translateContent(t,22)},_translateContent:function(n,i){n[0].style[t.transform]="translateX("+i+"px)"},_setModel:function(n){var i;for(var r in n)switch(r){case"renderMode":n.renderMode=="auto"?t.setRenderMode(this):this.model.renderMode=n.renderMode;i=!0;break;case"toggleState":this.model.toggleState?this._onState():this._offState();break;case"enabled":n.enabled?this.enable():this.disable()}i&&this._refresh()},_refresh:function(){this._clearElement();this._renderControl();this._wireEvents()},_clearElement:function(){this._wireEvents(!0);this.element.removeAttr("class");this.element.removeAttr("style");this.element.html("")},_destroy:function(){this._clearElement()},disable:function(){this.element.addClass("e-m-state-disabled");this._wireEvents(!0);this.model.enabled=!1},enable:function(){this.element.removeClass("e-m-state-disabled");this._wireEvents(!1);this.model.enabled=!0}})})(jQuery,Syncfusion)});
