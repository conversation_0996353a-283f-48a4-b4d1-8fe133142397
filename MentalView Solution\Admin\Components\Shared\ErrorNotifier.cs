﻿using Syncfusion.Blazor.Popups;

namespace Admin.Components.Shared
{
    public class ErrorNotifier
    {
        SfDialogService dialogService;

        public ErrorNotifier(SfDialogService dialogService)
        {
            this.dialogService = dialogService;
        }

        public async void ShowError(string message, string title)
        {
            await this.dialogService.AlertAsync(message, title);
        }

        public async void ShowInfo(string message, string title)
        {
            await this.dialogService.PromptAsync(message, title);
        }
    }
}
