/*!
*  filename: ej.dropdowntree.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./../common/ej.data.min","./../common/ej.scroller.min","./../common/ej.draggable.min","./../common/ej.treeview.min","./../common/ej.dropdownlist.min"],n):n()})(function(){"use strict";var n=this&&this.__extends||function(n,t){function r(){this.constructor=n}for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)};(function(t){var i=function(i){function r(n,r){return i.call(this),this._rootCSS="e-dropdowntree",this._setFirst=!1,this._requiresID=!0,this._setValuesCalled=!1,this.PluginName="ejDropDownTree",this.validTags=["input"],this.type="editor",this.angular={require:["?ngModel","^?form","^?ngModelOptions"]},this.model=null,this.defaults={cssClass:"",value:null,text:null,targetId:null,delimiter:",",textMode:"None",fullPathDelimiter:"/",locale:"en-US",height:"",htmlAttributes:{},headerTemplate:null,footerTemplate:null,showRoundedCorner:!1,enableRTL:!1,filterType:"contains",caseSensitiveSearch:!1,enablePersistence:!1,enableFilterSearch:!1,enableServerFiltering:!1,enableIncrementalSearch:!0,readOnly:!1,enableAnimation:!1,loadOnDemand:!1,validationRules:null,validationMessage:null,treeViewSettings:{},popupSettings:{height:"152px",width:"auto",showPopupOnLoad:!1},watermarkText:null,enabled:!0,width:"100%"},this.dataTypes={cssClass:"string",itemsCount:"number",watermarkText:"string",targetId:"string",enableIncrementalSearch:"boolean",cascadeTo:"string",delimiter:"string",showRoundedCorner:"boolean",enableRTL:"boolean",enablePersistence:"boolean",enabled:"boolean",readOnly:"boolean",query:"data",fields:"data",enableAnimation:"boolean",loadOnDemand:"boolean",enableSorting:"boolean",validationRules:"data",validationMessage:"data",htmlAttributes:"object",locale:"string"},this.value=ej.util.valueFunction("value"),this.observables=["value"],this.hiddenValue=null,this.activeItemIndex=-1,this.currentText=null,this.currentFullPath=null,this.listSize=0,this.isIE8=!1,this.textContent=null,this.initValue=!1,this.checkboxValue=!1,this.isFocused=!1,this.id=null,this.ddWidth=null,this.name=null,this.popUpShow=!1,this.loadPopup=!1,this.maxPopupHeight=null,this.minPopupHeight="20",this.maxPopupWidth=null,this.minPopupWidth="0",this.visibleNodes=[],this.hiddenNodes=[],this.filterData=null,this.normalData=null,this.addData=[],this.isupdateData=!1,n&&(n.jquery||(n=t("#"+n)[0]),!ej.isNullOrUndefined(n))?t(n).ejDropDownTree(r).data(this.PluginName):void 0}return n(r,i),r.prototype.enable=function(){if(t(this.visibleInput).hasClass("e-disable")){this.target.setAttribute("disabled","false");this.model.enabled=!0;t(this.container).removeClass("e-disable");t(this.visibleInput).removeClass("e-disable");t(this.dropdownbutton).removeClass("e-disable");ej.isNullOrUndefined(this.popupListWrapper)||t(this.popupListWrapper).removeClass("e-disable");this.isIE8&&t(this.drpbtnspan).removeClass("e-disable");t(this.container).on("mousedown",t.proxy(this.OnDropdownClick,this));this.wrapper.setAttribute("tabindex","0")}this.wireEvents()},r.prototype.disable=function(){t(this.visibleInput).hasClass("e-disable")||(this.target.setAttribute("disabled","true"),this.model.enabled=!1,t(this.container).addClass("e-disable"),t(this.visibleInput).addClass("e-disable"),ej.isNullOrUndefined(this.popupListWrapper)||t(this.popupListWrapper).addClass("e-disable"),t(this.dropdownbutton).addClass("e-disable"),this.isIE8&&t(this.drpbtnspan).addClass("e-disable"),t(this.container).off("mousedown",t.proxy(this.OnDropdownClick,this)),this.unwireEvents(),this.wrapper.removeAttribute("tabindex"),this.isPopupShown()&&this.hideResult())},r.prototype.enabled=function(n){n?this.enable():this.disable()},r.prototype.getValue=function(){return this.visibleInput.value},r.prototype.hidePopup=function(){this.model.enabled&&(ej.isNullOrUndefined(this.popupTree)||this.hideResult())},r.prototype.hideResult=function(n){var i,r,u;if(this.model&&this.isPopupShown()){if(!ej.isNullOrUndefined(n)&&!ej.isNullOrUndefined(this.inputSearch)&&jQuery(this.inputSearch).querySelector(":foucs")&&n.type==="scroll"&&ej.isTouchDevice())return!1;if(i=this,r={model:this.model,text:this.visibleInput.value,value:this.element.val(),cancel:!1,type:"close"},this._trigger("close",r))return;t(this.popupListWrapper).slideUp(this.model.enableAnimation?100:0,function(){t(document).off("mousedown",t.proxy(i.OnDocumentClick,i))});this.element!=null&&this.element.attr("aria-expanded","false");ej.isDevice()||(u=ej.getScrollableParents(this.wrapper),t(u).off("scroll",t.proxy(i.hideResult,i)));this.visibleInput!=null&&t(this.wrapper).removeClass("e-popactive");this.popUpShow=!1;setTimeout(function(){i.resetSearch()},100)}},r.prototype.resetSearch=function(){var n;(t(this.popupListWrapper).find(".e-atc.e-search .e-cross-circle").length===1&&(t(this.popupListWrapper).find(".e-atc.e-search .e-cross-circle").addClass("e-search").removeClass("e-cross-circle"),n=t(this.popupListWrapper).find(".e-atc.e-search .e-cross-circle"),this._off(t(this.popupListWrapper).find(".e-atc.e-search .e-cross-circle"),"mousedown",this.refreshSearch)),this.inputSearch&&this.model&&this.model.enableFilterSearch)&&this.inputSearch.value!==""&&(this.inputSearch.value="",n=this.popup.querySelector(".e-nosuggestion"),n&&(n.remove(),this.popupTree.style.display=this.treeStyle),this.showNodes(Array.prototype.slice.call(this.treeView.element[0].querySelectorAll("li"))),this.refreshPopup())},r.prototype.addItem=function(n,t){if(this.treeView.addNodes(n,t),this.isPopupShown()){var i=this.scrollerObj?this.scrollerObj.scrollTop():0;this.refreshScroller();this.scrollerObj&&this.scrollerObj.option("scrollTop",i)}},r.prototype._destroy=function(){this.element.insertAfter(this.wrapper);this.element.width(this.element.width()+t(this.dropdownbutton).outerWidth());t(this.visibleInput).removeClass("e-input");this.setAttr(this.element[0],{accesskey:t(this.wrapper).attr("accesskey"),type:"text"});this.isWatermark&&t(this.visibleInput).removeAttr("placeholder");this.element.value="";this.element.removeAttr("aria-expanded aria-autocomplete aria-haspopup aria-owns accesskey role").css({width:"",display:"block"});this.treeView.destroy();this.wrapper.remove();t(this.container).off("mousedown",t.proxy(this.OnDropdownClick,this));this.hideResult();t(this.popupPanelWrapper).remove();this.unwireEvents()},r.prototype.getSelectedValue=function(){return this.element.val()},r.prototype.removeSearch=function(){this.model.enableFilterSearch=!1;this.popupListWrapper.querySelector(".e-atc.e-search").remove();this.isPopupShown()&&this.hidePopup();this.inputSearch=null},r.prototype.isPopupShown=function(){return ej.isNullOrUndefined(this.popupListWrapper)?!1:t(this.popupListWrapper).css("display")==="block"},r.prototype.OnDropdownClick=function(n){if(this.preventDefaultAction(n),this.model.loadOnDemand&&ej.isNullOrUndefined(this.popupListWrapper)&&(this.popupCreation(),this.renderScroller(),this._skipOrderDataSource=this._trigger("orderingDataSource"),this.value()&&!ej.isNullOrUndefined(this.model.treeViewSettings.fields)&&(this._setValuesCalled=!0,this.setValueText(this.value(),"value"))),t(n.target).is("li")&&t(n.target).parent().hasClass("e-boxes")||t(n.target).parents("ul").hasClass("e-boxes")&&t(n.target).hasClass("e-icon e-close"))return!1;if(this.model.readOnly||this.readOnly)return this.model.loadOnDemand&&(this.loadPopup=!0),!1;(n.which&&n.which===1||n.button&&n.button===0)&&this.OnPopupHideShow()},r.prototype.OnPopupHideShow=function(){var n,t;this.isPopupShown()?this.hideResult():(this.selectedElements=this.model.treeViewSettings.showCheckbox?this.treeView.getCheckedNodes():this.treeView.getSelectedNodes(),this.showResult(),this.model.enableFilterSearch?(n=this.selectedElements,n.length===0?this.addHoverClass(this.popupTree.querySelector(".e-ul.e-box .e-text")):(t=n.length,this.addHoverClass(n[t-1].querySelector("a"))),this.inputSearch.focus()):this.wrapper.focus())},r.prototype.addHoverClass=function(n){t(n).hasClass("e-text")&&!t(n).hasClass("e-node-disable")&&t(n).addClass("e-node-hover")},r.prototype.showPopup=function(){this.model.enabled&&(ej.isNullOrUndefined(this.popupTree)||this.showResult())},r.prototype.showResult=function(){var f=this,n=this,r,i,u;(this.popUpShow=!0,r={model:this.model,text:this.visibleInput.value,value:this.element.val(),cancel:!1,type:"open",refreshPopup:!0},this._trigger("open",r))||(r.refreshPopup&&this.refreshPopup(),this.selectedElements=this.model.treeViewSettings.showCheckbox?this.treeView.getCheckedNodes():this.treeView.getSelectedNodes(),t(this.popupListWrapper).slideDown(this.model.enableAnimation?200:1,function(){t(document).on("mousedown",t.proxy(n.OnDocumentClick,n));if(!ej.isDevice()){var i=ej.getScrollableParents(f.wrapper);t(i).on("scroll",t.proxy(n.hideResult,n))}}),this.element[0].setAttribute("aria-expanded","true"),t(this.wrapper).addClass("e-popactive"),i=this.getSelectedElements(),i.length!==0?this.setScrollPosition(i[i.length-1]):(u=t(t(this.getLi()).find(".e-node-focus")).parents("li")[0],ej.isNullOrUndefined(u)||this.setScrollPosition(u)))},r.prototype.getSelectedElements=function(){return this.model.treeViewSettings.showCheckbox?this.treeView.getCheckedNodes():this.treeView.getSelectedNodes()},r.prototype.setScrollPosition=function(n){var i=t(this.popupTree).offset().top,r=t(n).offset().top,u=r-i;this.scrollerObj&&this.scrollerObj.option("scrollTop",u)},r.prototype.preventDefaultAction=function(n,t){n.preventDefault?n.preventDefault():n.returnValue=!1;t&&(n.stopPropagation?n.stopPropagation():n.cancelBubble=!0)},r.prototype.clearText=function(){this.clearTextboxValue();this.clearTree();this.isWatermark||this.setWatermark()},r.prototype.clearTextboxValue=function(){this.element.val("");t(this.visibleInput).val("");this.updateValue(null);this.selectedElements=[];this.hiddenValue="";this.updateText()},r.prototype.clearTree=function(){this.treeView.setModel({selectedNode:-1,selectedNodes:[],checkedNodes:[]})},r.prototype.addNode=function(){var n=this.popup.querySelector(".e-norecord");n&&(n.remove(),this.popupTree.style.display=this.treeStyle)},r.prototype.addNodes=function(){var n=this.popup.querySelector(".e-norecord");n&&(n.remove(),this.popupTree.style.display=this.treeStyle)},r.prototype.checkAll=function(){var n,t;if(this.model.treeViewSettings.showCheckbox)for(n=this.treeView.getCheckedNodes(),n.length!=this.getLi().length&&this.treeView.checkAll(),t=0;t<n.length;t++)this.selectText(n[t])},r.prototype.moveNode=function(n){var t=this.getElementText(n);this.checkContains(t)&&this.removeText(t);this.selectText(n)},r.prototype.removeAll=function(){this.addEmptyRecord();this.clearTextboxValue()},r.prototype.addEmptyRecord=function(){var i=this.popup.querySelector(".e-norecord"),n;i||this.popup.querySelector(".e-norecord")||(n=document.createElement("div"),t(n).addClass("e-norecord"),n.style.padding="14px 16px",n.style.textAlign="center",n.innerHTML=this.getLocalizedLabels("noRecordsTemplate"),this.popupScroller.appendChild(n),this.treeStyle=this.popupTree.style.display,this.popupTree.style.display="none")},r.prototype.selectAll=function(){var t,n;if(!this.model.treeViewSettings.showCheckbox)for(t=this.treeView.getSelectedNodes(),n=0;n<t.length;n++)this.selectText(t[n])},r.prototype.selectNode=function(n){if(!this.model.treeViewSettings.showCheckbox)for(var t=0;t<n.length;t++)this.selectText(n[t])},r.prototype.unCheckAll=function(){if(this.model.treeViewSettings.showCheckbox){var n=this.treeView.getCheckedNodes();n.length>0&&(this.clearTextboxValue(),this.treeView.unCheckAll(),this.valueContainer=[])}},r.prototype.uncheckNode=function(n){if(this.model.treeViewSettings.showCheckbox)for(var t=0;t<n.length;t++)this.unselectText(n[t])},r.prototype.unselectAll=function(){this.model.treeViewSettings.showCheckbox||this.clearTextboxValue()},r.prototype.unselectNode=function(n){if(!this.model.treeViewSettings.showCheckbox)for(var t=0;t<n.length;t++)this.unselectText(n[t])},r.prototype.unselectText=function(n){var t=this.getElementText(n);this.checkContains(t)&&this.removeText(t)},r.prototype.selectText=function(n){this.currentText=this.getElementText(n);this.checkContains(this.currentText)||(this.maintainHiddenValue(),this.addText(this.currentText))},r.prototype.validateDelimiter=function(n){if(this.trim(n).length===1)if(!/^[a-zA-Z0-9]+$/.test(n))return n;return","},r.prototype.changeWatermark=function(n){if(!this.model.enabled)return!1;this.isWatermark?t(this.visibleInput).attr("placeholder",n):t(this.hiddenSpan).text(n)},r.prototype.roundedCorner=function(n){n?(t(this.container).addClass("e-corner"),ej.isNullOrUndefined(this.popupListWrapper)||t(this.popupListWrapper).addClass("e-corner"),this.inputSearch&&t(this.inputSearch).parents().find(".e-in-wrap").addClass("e-corner")):(t(this.container).removeClass("e-corner"),ej.isNullOrUndefined(this.popupListWrapper)||t(this.popupListWrapper).removeClass("e-corner"),this.inputSearch&&t(this.inputSearch).parents().find(".e-in-wrap").removeClass("e-corner"))},r.prototype.setAttr=function(n,i){var u,r,f;if(typeof i=="string")u=i.replace(/['"]/g,"").split("="),u.length===2&&t(n).attr(u[0],u[1]);else for(r in i)if((r==="styles"||r==="style")&&typeof i[r]=="object")for(f in i[r])n.style[f]=i[r][f];else t(n).attr(r,i[r]);return this},r.prototype.updateText=function(){this.model.text=t(this.visibleInput).value===""?null:t(this.visibleInput).val()},r.prototype.updateValue=function(n){this.value(n===""?null:n)},r.prototype.setWatermark=function(){if(this.element.val()===""&&this.trim(t(this.visibleInput).val().toString())===""){var n=this.model.watermarkText;this.isWatermark?t(this.visibleInput).attr("placeholder",n):t(this.hiddenSpan).css("display","block").text(n)}},r.prototype.setDimentions=function(){this.model.height&&t(this.wrapper).height(this.model.height);this.model.width&&t(this.wrapper).width(this.model.width)},r.prototype.trim=function(n){return typeof n=="string"?t.trim(n):n},r.prototype.wireEvents=function(){t(this.wrapper).on("keydown",t.proxy(this.OnKeyDown,this));if(!ej.isNullOrUndefined(this.popupList))t(this.popupList).on("keydown",t.proxy(this.OnKeyDown,this));if(!ej.isNullOrUndefined(this.inputSearch))t(this.inputSearch).on("keydown",t.proxy(this.OnKeyDown,this));this._on(t(this.wrapper),"focus",this.targetFocus);this._on(t(this.wrapper),"blur",this.targetBlur)},r.prototype.unwireEvents=function(){this._off(t(this.wrapper),"focus",this.targetFocus);this._off(t(this.wrapper),"blur",this.targetBlur);t(window).off("resize",t.proxy(this.OnWindowResize,this))},r.prototype.updateLocalConstant=function(){this.localizedLabels=ej.getLocalizedConstants("ej.DropDownTree",this.model.locale)},r.prototype._setModel=function(n){var i,u,f,r,e;for(i in n)switch(i){case"delimiter":u=this.model.delimiter;n[i]=this.validateDelimiter(n[i]);this.model.delimiter=n[i];this.isSingleSelect()||(this.model.text&&(this.model.text=this.model.text.split(u).join(this.model.delimiter),t(this.visibleInput).val(this.model.text)),ej.isNullOrUndefined(this.value())||(this.value(this.value().split(u).join(this.model.delimiter)),this.element.val(this.value())));break;case"fullPathDelimiter":this.setFullPathDelimiter(n[i]);break;case"treeViewSettings":n[i].fields&&(delete this.model.treeViewSettings.fields.dataSource,this.model.loadOnDemand&&(f=t("#"+this.element[0].id+"_popup_wrapper")[0],t(f).remove(),this.popupTree=undefined,this.popupListWrapper=undefined));this.model.treeViewSettings=t.extend(!0,this.model.treeViewSettings,n[i]);ej.isNullOrUndefined(this.popupTree)||t(this.popupTree).ejTreeView(this.model.treeViewSettings);break;case"value":r=ej.util.getVal(n[i]);ej.isNullOrUndefined(r)?this.clearTextboxValue():ej.isNullOrUndefined(this.treeView)?this.model.loadOnDemand&&r!==""&&this.setInputItemValue(r):(this.setValueText(ej.util.getVal(n[i]),"value"),n[i]=this.model.value);break;case"text":ej.isNullOrUndefined(n[i])?this.clearTextboxValue():ej.isNullOrUndefined(this.treeView)||(this.setValueText(n[i],"text"),n[i]=this.model.text);break;case"enableRTL":this.setRTL(n[i]);break;case"enabled":this.enabled(n[i]);break;case"height":this.changeHeight(n[i]);break;case"width":this.changeWidth(n[i]);break;case"popupSettings":this.model.popupSettings=t.extend(!0,this.model.popupSettings,n[i]);this.setListWidth();this.setListHeight();this.setInitialPopup(this.model.popupSettings.showPopupOnLoad);break;case"cssClass":this.changeSkin(n[i]);ej.isNullOrUndefined(this.treeList)||(e=this.waitingCss(n[i]),this.treeView.setModel({cssClass:e}));break;case"watermarkText":this.changeWatermark(n[i]);break;case"validationRules":this.element.closest("form").length!=0&&(this.model.validationRules!=null&&(this.element.rules("remove"),this.model.validationMessage=null),this.model.validationRules=n[i],this.model.validationRules!=null&&(this.initValidator(),this.setValidation()));break;case"locale":this.model.locale=n[i];this.updateLocalConstant();break;case"validationMessage":this.element.closest("form").length!=0&&(this.model.validationMessage=n[i],this.model.validationRules!=null&&this.model.validationMessage!=null&&(this.initValidator(),this.setValidation()));break;case"showRoundedCorner":this.roundedCorner(n[i]);this.model.showRoundedCorner=n[i];break;case"targetId":this.model.targetId=n[i];this.treeList();break;case"htmlAttributes":this.addAttr(n[i]);break;case"enableFilterSearch":n[i]?(this.model.enableFilterSearch=!0,this.enableSearch()):this.removeSearch()}},r.prototype._init=function(){var n=ej.browserInfo();this.updateLocalConstant();this.isIE8=n.name==="msie"&&n.version==="8.0";this.textContent=this.isIE8?"innerText":"textContent";this.element.is("input")&&(this.element.is("input[type=text]")||!this.element.attr("type"))&&(this.isWatermark="placeholder"in t(document.createElement("input")).attr("placeholder","")[0],this.id=this.element[0].id,this.initialize(),this.render(),this.addAttr(this.model.htmlAttributes),this.enabled(this.model.enabled),this.initValue=!1,this.checkboxValue=!1,ej.DataManager&&!ej.isNullOrUndefined(this.model.treeViewSettings.fields)&&this.model.treeViewSettings.fields.dataSource instanceof ej.DataManager||this.model.loadOnDemand||this.finalize(),this.model.validationRules!=null&&(this.initValidator(),this.setValidation()),this.model.popupSettings.showPopupOnLoad&&!this.model.loadOnDemand&&this.setInitialPopup(!0),this.model.loadOnDemand&&!ej.isNullOrUndefined(this.value())&&this.value()!==""&&this.setInputItemValue(this.value()),this.roundedCorner(this.model.showRoundedCorner))},r.prototype.setInputItemValue=function(n){var i=this.model.treeViewSettings.fields,u=[],e,f,o,r;if(this.model.loadOnDemand&&(ej.isNullOrUndefined(this.ultag)||this.ultag.children().length==0)){if(i.dataSource.offline||i.dataSource.json&&i.dataSource.json.length>0||!ej.isNullOrUndefined(i.dataSource.dataSource))r=this,i.query=i.query?i.query:this._columnToSelect(i),i.dataSource.executeQuery(i.query).done(function(i){var c=ej.isNullOrUndefined(r.model.treeViewSettings.fields.value)?r.model.treeViewSettings.fields.text:r.model.treeViewSettings.fields.value,l=r.model.treeViewSettings.fields.text,o,a,f,s,e,h;if(r.model.treeViewSettings.allowMultiSelection)for(o=0;o<n.length;o++)a=new ej.DataManager(i.result).executeLocal((new ej.Query).where(c,ej.FilterOperators.equal,n[o],!1)),u.push(a[0][l]);else if(f=r._newDataSource=i.result,s=new ej.DataManager(f).executeLocal((new ej.Query).where(c,ej.FilterOperators.equal,n,!1)),s.length==0)for(e=0;e<f.length;e++)h=ej.getObject(r.model.treeViewSettings.fields.parentId,f[e]),(ej.isNullOrUndefined(h)||h==0)&&r.model.treeViewSettings.fields.child&&r.getChildItem(f[e],r.model.treeViewSettings.fields);else u.push(s[0][l]);t(r.visibleInput).val(u)});else if(e=i.value?this.treeMapping("value"):this.treeMapping("text"),this.model.treeViewSettings.allowMultiSelection){for(f=0;f<n.length;f++)o=new ej.DataManager(i.dataSource).executeLocal((new ej.Query).where(i.text,ej.FilterOperators.equal,n[f],!1)),u.push(o[0][e[0]]);t(this.visibleInput).val(u)}else this._setInitValue(i.dataSource,e[0],n);u=[]}},r.prototype._setInitValue=function(n,i,r){for(var f=new ej.DataManager(n).executeLocal((new ej.Query).where(i,ej.FilterOperators.equal,r,!1)),e,u,o,s;f.length==0&&n.length!=0;){for(e=[],u=0;u<n.length;u++)n[u].child&&t.merge(e,n[u].child);f=new ej.DataManager(e).executeLocal((new ej.Query).where(i,ej.FilterOperators.equal,r,!1));n=e}o=[];f.length!=0&&(s=this.model.treeViewSettings.fields.value?this.model.treeViewSettings.fields.text:i,o.push(f[0][s]),t(this.visibleInput).val(o))},r.prototype.getChildItem=function(n,t){var i=this,f,e,u,r,o;e=t.child&&t.child.parentId?t.child.parentId:i.model.treeViewSettings.fields.parentId;u;u=t.id?t.id:i.model.treeViewSettings.fields.id;o=ej.getObject(u,n);f=this._executeDataQuery(t.child,e,parseInt(o));f.done(function(n){var u,f;r=n.xhr&&n.xhr.responseJSON&&n.xhr.responseJSON.d?n.xhr.responseJSON.d:n.result?n.result:[];r&&r.length>0&&(u=r[0][t.child.parentId],i._updateRemoteData(i._newDataSource,u,r,i.model.treeViewSettings.fields));f=i.model.treeViewSettings.fields.child.text;i._setInitValue(i._newDataSource,f,i.value())})},r.prototype._executeDataQuery=function(n,i,r){var u,e,f;if(u=new ej.Query,u=this._columnToSelect(n),!ej.isNullOrUndefined(i)&&i!=""){for(e=t.extend(!0,[],u._params),u._params=[],u.addParams(i,r),f=0;f<e.length;f++)e[f].key!=i&&u.addParams(e[f].key,e[f].value);u.where(i,ej.FilterOperators.equal,r)}return n.dataSource.executeQuery(u)},r.prototype._columnToSelect=function(n){var r=[],i=new ej.Query,t;if(n.query||ej.isNullOrUndefined(n.tableName))i=n.query?n.query.clone():i;else{for(t in n)t!=="tableName"&&t!=="child"&&t!=="dataSource"&&n[t]&&r.push(n[t]);r.length>0&&i.select(r);n.dataSource.dataSource.url.match(n.tableName+"$")||ej.isNullOrUndefined(n.tableName)||i.from(n.tableName)}return i},r.prototype._updateRemoteData=function(n,t,i,r){var u,f,e;if(r.dataSource==null||!r.dataSource.dataSource.offline)for(u=0;u<n.length;u++){if(f=ej.getObject(r.id,n[u]),f&&f.toString()==t){n[u].child=i;e=n[u];n.splice(u,1,e);break}n[u].hasOwnProperty("child")&&this._updateRemoteData(n[u].child,t,i,r.child?r.child:r)}},r.prototype.finalize=function(){(ej.isNullOrUndefined(this.value())||this.value()!=="")&&this.value()===this.element.val()||((this.treeView.model.allowMultiSelection||this.treeView.model.showCheckbox)&&(this._setValuesCalled=!0),this.setValueText(this.value(),"value"));(ej.isNullOrUndefined(this.model.text)||this.model.text!=="")&&this.model.text===t(this.visibleInput).val()||this.setValueText(this.model.text,"text");this.initialSelection();this.updateText();ej.util.valueFunction(this.value)===this.element.val()||ej.util.valueFunction(this.value)===null&&this.element.val()===""||this.updateValue(this.element.val())},r.prototype.setValues=function(n){this._setValuesCalled=!0;this.treeNodesSelection([]);this.setValueText(n,"value");this._setValuesCalled=!1},r.prototype.setValueText=function(n,i){var v,l,c,r,h,e;if(ej.isNullOrUndefined(this.model.targetId)){var u=void 0,f=-1,o=[],s=this.treeMapping(i);if(this.treeView.dataSource()instanceof ej.DataManager){for(u=this._skipOrderDataSource?[]:this.treeView.getTreeData(),this.model.loadOnDemand&&t(this.visibleInput).val()!==""&&t(this.visibleInput).val(""),r=0;r<u.length;r++)if(f=f+1,typeof n=="string"||typeof n=="number"||n==null){if(ej.getObject(s[0],u[r])===n&&this.treeNodeSelection(f),!ej.isNullOrUndefined(u[r].child))for(e=0;e<u[r].child.length;e++)f=f+1,ej.getObject(s[1],u[r].child[e])===n&&this.treeNodeSelection(f)}else if(!ej.isNullOrUndefined(n)&&typeof n=="object"&&this._setValuesCalled)for(this.treeView.model.allowMultiSelection||this.treeView.model.showCheckbox||(n=n[0],this.setValueText(n,i)),h=0;h<n.length;h++)if(ej.getObject(s[0],u[r])===n[h]&&o.push(f),!ej.isNullOrUndefined(u[r].child))for(e=0;e<u[r].child.length;e++)f=f+1,ej.getObject(s[1],u[r].child[e])===n[h]&&o.push(f),e==u[r].child.length-1&&h!=n.length-1&&(f=f-u[r].child.length);o.length>0&&this.treeNodesSelection(o)}else{for(u=this.model.treeViewSettings.fields.dataSource,r=0,l=u.length;r<l;){if(this.model.loadOnDemand&&t(this.visibleInput).val()!==""&&this.model.value==""&&ej.isNullOrUndefined(this.activeItem)&&t(this.visibleInput).val(""),(ej.isNullOrUndefined(n)||typeof n!="string")&&typeof n!="number"){if(!ej.isNullOrUndefined(n)&&typeof n=="object"&&this._setValuesCalled)for(this.treeView.model.allowMultiSelection||this.treeView.model.showCheckbox||(n=n[0],this.setValueText(n,i)),c=0;c<n.length;c++)ej.getObject(s[0],u[r])===n[c]&&o.push(r)}else if(ej.getObject(s[0],u[r])===n){this.treeNodeSelection(r);break}r++}o.length>0&&this.treeNodesSelection(o)}}else for(var a=this.getLi(),r=0,l=a.length;r<l;)v=a[r].querySelector("a"),v.innerHTML.toString()===n&&this.treeNodeSelection(r),r++},r.prototype.treeMapping=function(n){var u,f,t=[],i=this.model.treeViewSettings.fields.text,r;return n==="value"?(u=ej.isNullOrUndefined(this.model.treeViewSettings.fields.value)?i:this.model.treeViewSettings.fields.value,ej.isNullOrUndefined(this.model.treeViewSettings.fields.child)?(r=i,f=u):(r=ej.isNullOrUndefined(this.model.treeViewSettings.fields.child.text)?i:this.model.treeViewSettings.fields.child.text,f=ej.isNullOrUndefined(this.model.treeViewSettings.fields.child.value)?r:this.model.treeViewSettings.fields.child.value),t[0]=u,t[1]=f):(t[0]=i,t[1]=r),t},r.prototype.treeNodeSelection=function(n){ej.isNullOrUndefined(this.treeView)||(this.model.treeViewSettings.showCheckbox?this.treeView.setModel({checkedNodes:[n]}):this.treeView.setModel({selectedNode:n}))},r.prototype.treeNodesSelection=function(n){ej.isNullOrUndefined(this.treeView)||(this.model.treeViewSettings.showCheckbox?this.treeView.setModel({checkedNodes:n}):this.treeView.setModel({selectedNodes:n}))},r.prototype.initialSelection=function(){var n=this.model.treeViewSettings.showCheckbox?this.treeView.getCheckedNodes():this.treeView.getSelectedNodes(),t;if(!ej.isNullOrUndefined(n))for(this.selectedElements=n,t=0;t<n.length;t++)this.currentText=this.getElementText(n[t]),this.maintainHiddenValue(),this.addText(this.currentText)},r.prototype.setInitialPopup=function(n){this.model.enabled&&!this.model.readOnly&&(n===!1?this.hideResult():this.showResult())},r.prototype.changeSkin=function(n){t(this.wrapper).removeClass(this.model.cssClass).addClass(n);ej.isNullOrUndefined(this.popupListWrapper)||t(this.popupListWrapper).removeClass(this.model.cssClass).addClass(n)},r.prototype.setRTL=function(n){this.model.enableRTL!==n&&(this.model.enableRTL=n,this.RightToLeft(),ej.isNullOrUndefined(this.popupListWrapper)||this.dropbtnRTL())},r.prototype.RightToLeft=function(){this.model.enableRTL?t(this.wrapper).addClass("e-rtl"):t(this.wrapper).removeClass("e-rtl")},r.prototype.dropbtnRTL=function(){this.model.enableRTL?(t(this.popupListWrapper).addClass("e-rtl").find(".e-resize-handle").addClass("e-rtl-resize"),t(this.popupList).addClass("e-rtl")):(t(this.popupListWrapper).removeClass("e-rtl").find(".e-resize-handle").removeClass("e-rtl-resize"),t(this.popupList).removeClass("e-rtl"))},r.prototype.formatUnit=function(n){var t=n+"";return t==="auto"||t.indexOf("%")!==-1||t.indexOf("px")!==-1?t:t+"px"},r.prototype.changeHeight=function(n){t(this.wrapper).height(this.formatUnit(n));this.setListHeight()},r.prototype.changeWidth=function(n){t(this.wrapper).width(this.formatUnit(n));this.setListWidth()},r.prototype.setListWidth=function(){if(!ej.isNullOrUndefined(this.popupListWrapper)){var n=this.model.popupSettings.width;n!=="auto"?t(this.popupListWrapper).css({width:n}):t(this.popupListWrapper).css({"min-width":this.validatePixelData(this.minPopupWidth)});t(this.popupListWrapper).css({"max-width":this.validatePixelData(this.maxPopupWidth)})}},r.prototype.setListHeight=function(){ej.isNullOrUndefined(this.popupListWrapper)||t(this.popupListWrapper).css({"max-height":this.validatePixelData(this.model.popupSettings.height),"min-height":this.validatePixelData(this.minPopupHeight)})},r.prototype.validatePixelData=function(n){return n&&!isNaN(n)?Number(n):n},r.prototype.addAttr=function(n){var i=this;t.map(n,function(n,r){var u=r.toLowerCase();u==="class"?t(i.wrapper).addClass(n):u==="disabled"&&n==="disabled"?i.disable():u==="readOnly"&&n==="readOnly"?i.model.readOnly=!0:u==="style"?t(i.wrapper).attr(r,n):ej.isValidAttr(i.visibleInput[0],r)?t(i.visibleInput).attr(r,n):t(i.wrapper).attr(r,n)})},r.prototype.initValidator=function(){this.element.closest("form").data("validator")||this.element.closest("form").validate()},r.prototype.setValidation=function(){var i,u,n,r,f;if(this.element.closest("form").length!==0){this.element.rules("add",this.model.validationRules);i=this.element.closest("form").data("validator");i||(i=this.element.closest("form").validate());u=this.element.attr("name");i.settings.messages[u]={};for(n in this.model.validationRules)if(!ej.isNullOrUndefined(this.model.validationRules[n])){if(r=null,ej.isNullOrUndefined(this.model.validationRules.messages&&this.model.validationRules.messages[n])){i.settings.messages[u][n]=t.validator.messages[n];for(f in this.model.validationMessage)n==f?r=this.model.validationMessage[n]:""}else r=this.model.validationRules.messages[n];i.settings.messages[u][n]=r!=null?r:t.validator.messages[n]}}},r.prototype.initialize=function(){this.target=this.element[0];this.popUpShow=!1;this.valueContainer=[]},r.prototype.render=function(){this.createInput();this.setWatermark();this.model.loadOnDemand||(this.popupCreation(),this.renderScroller())},r.prototype.createInput=function(){if(this.wrapper=ej.buildTag("span.e-ddl e-ddtree e-widget "+this.model.cssClass+"#"+this.id+"_wrapper","",{},{tabindex:"0",accesskey:this.element.attr("accesskey")})[0],this.container=ej.buildTag("span.e-in-wrap e-box #"+this.id+"_container")[0],this.element.removeAttr("accesskey"),this.model.value===null&&this.element.attr("value")!=null&&(this.model.value=this.element.attr("value")),this.element.attr("value","").val(""),this.isIE8?this.setAttr(this.element[0],{role:"combobox","aria-expanded":!1,"aria-autocomplete":"list","aria-haspopup":!0,"aria-owns":this.id+"_popup"}).element.hide():this.setAttr(this.element[0],{type:"hidden",role:"combobox","aria-expanded":!1,"aria-autocomplete":"list","aria-haspopup":!0,"aria-owns":this.id+"_popup"}).element.hide(),t(this.container).insertAfter(this.element),t(this.container).append(this.element),this.dropDownCreation(),t(this.container).append(this.dropdownbutton),t(this.wrapper).insertBefore(this.container),t(this.wrapper).append(this.container),this.visibleInput=ej.buildTag("input#"+this.id+"_hidden","",{}).insertAfter(this.element)[0],t(this.visibleInput).addClass("e-input"),this.setAttr(this.visibleInput[0],{readonly:"readonly",tabindex:-1,"data-role":"none"}),!this.isWatermark){var n=this.model.watermarkText;this.hiddenSpan=ej.buildTag("span.e-input e-placeholder")[0];t(this.hiddenSpan).insertAfter(this.element);t(this.hiddenSpan).text(n);t(this.hiddenSpan).css("display","none")}this.setDimentions();this.RightToLeft();this.ddWidth=t(this.dropdownbutton).outerWidth()>0?t(this.dropdownbutton).outerWidth():24;t(this.container).on("mousedown",t.proxy(this.OnDropdownClick,this))},r.prototype.dropDownCreation=function(){this.drpbtnspan=ej.buildTag("span.e-icon e-arrow-sans-down","",{},{"aria-label":"select",unselectable:"on"})[0];this.dropdownbutton=ej.buildTag("span.e-select#"+this.id+"_dropdown","",{},{role:"button",unselectable:"on"})[0];t(this.dropdownbutton).append(this.drpbtnspan)},r.prototype.popupCreation=function(){var n=t("#"+this.element[0].id+"_popup_wrapper")[0];n&&t(n).remove();this.popupPanelWrapper=ej.buildTag("div#"+this.id+"_popup_wrapper")[0];t("body").append(this.popupPanelWrapper);t(this.popupPanelWrapper).addClass("e-ddl-popupwrapper");this.popupListWrapper=ej.buildTag("div.e-ddl-popup e-ddtree-popup e-box e-widget  e-popup#"+this.id+"_popup_list_wrapper","",{display:"none",overflow:"hidden"})[0];this.popupList=ej.buildTag("div#"+this.id+"_popup","","",{tabIndex:"0"})[0];t(this.popupListWrapper).addClass(this.model.cssClass);this.popup=this.popupList;this.popupScroller=ej.buildTag("div")[0];t(this.popupList).append(this.popupScroller);this.model.headerTemplate&&(this.headerTemplate=t("<div>").append(this.model.headerTemplate)[0],t(this.popupListWrapper).append(this.headerTemplate));this.model.targetId!=null?this.targetElementBinding():this.popupTree=ej.buildTag("div#"+this.id+"_popup_treeview","","",{tabIndex:"0"})[0];t(this.popupScroller).append(this.popupTree);t(this.popupListWrapper).append(this.popupList);this.model.footerTemplate&&this.setFooterTemplate();t(this.popupPanelWrapper).append(this.popupListWrapper);t(window).on("resize",t.proxy(this.OnWindowResize,this));this.enableSearch();this.renderTreeView()},r.prototype.setFooterTemplate=function(){this.footerTemplate?this.footerTemplate.innerHTML="":this.footerTemplate=document.createElement("div");this.footerTemplate.innerHTML=this.model.footerTemplate;t(this.popupListWrapper).append(this.footerTemplate)},r.prototype.treeList=function(){this.model.targetId!=null&&this.targetElementBinding()},r.prototype.renderTreeView=function(){var n={cssClass:this.waitingCss(this.model.cssClass),enabled:this.model.enabled,enableRTL:this.model.enableRTL,showRoundedCorner:this.model.showRoundedCorner,allowKeyboardNavigation:!0,enablePersistence:this.model.enablePersistence},i;this.model.treeViewSettings=t.extend(!0,n,this.model.treeViewSettings);!ej.isNullOrUndefined(this.model.treeViewSettings.fields)&&this.model.treeViewSettings.fields.dataSource instanceof ej.DataManager&&this.addLoadingClass();i=this;t(this.popupTree).ejTreeView(this.model.treeViewSettings);this.treeView=t(this.popupTree).ejTreeView("instance");t(this.popupTree).ejTreeView({ready:t.proxy(this.onTreeReady,this),nodeDelete:t.proxy(this.nodeDelete,this),nodeCollapse:t.proxy(this.onNodeCollapseExpand,this),nodeExpand:t.proxy(this.onNodeCollapseExpand,this),nodeSelect:t.proxy(this.onNodeSelectUnselect,this),nodeUnselect:t.proxy(this.onNodeSelectUnselect,this),nodeCheck:t.proxy(this.onNodeCheckUncheck,this),nodeUncheck:t.proxy(this.onNodeCheckUncheck,this),keyPress:t.proxy(this.onTreeKeyPress,this)});this.treeMethods()},r.prototype.treeMethods=function(){var t=ej.TreeView.prototype.addNode,i=ej.TreeView.prototype.addNodes,r=ej.TreeView.prototype.checkAll,u=ej.TreeView.prototype.checkNode,f=ej.TreeView.prototype.moveNode,e=ej.TreeView.prototype.removeAll,o=ej.TreeView.prototype.removeNode,s=ej.TreeView.prototype.selectAll,h=ej.TreeView.prototype.selectNode,c=ej.TreeView.prototype.unCheckAll,l=ej.TreeView.prototype.uncheckNode,a=ej.TreeView.prototype.unselectAll,v=ej.TreeView.prototype.unselectNode,y=this.treeView,n=this;ej.TreeView.prototype.addNode=function(i,r){t.apply(this,[i,r]);n.addNode(i,r)};ej.TreeView.prototype.addNodes=function(t,r){i.apply(this,[t,r]);n.addNodes(t,r)};ej.TreeView.prototype.checkAll=function(){r.call(this);this.element.parents()[2].classList.contains("e-ddtree-popup")&&n.checkAll()};ej.TreeView.prototype.checkNode=function(n){u.apply(this,[n])};ej.TreeView.prototype.moveNode=function(t,i,r){f.apply(this,[t,i,r]);n.moveNode(t,i,r)};ej.TreeView.prototype.removeAll=function(){e.call(this);n.removeAll()};ej.TreeView.prototype.removeNode=function(n){o.apply(this,[n])};ej.TreeView.prototype.selectAll=function(){s.call(this);n.selectAll()};ej.TreeView.prototype.selectNode=function(t){h.apply(this,[t]);n.selectNode(t)};ej.TreeView.prototype.unCheckAll=function(){c.call(this);this.element.parents()[2].classList.contains("e-ddtree-popup")&&n.unCheckAll()};ej.TreeView.prototype.uncheckNode=function(t){l.apply(this,[t]);n.uncheckNode(t)};ej.TreeView.prototype.unselectAll=function(){a.call(this);n.unselectAll()};ej.TreeView.prototype.unselectNode=function(t){v.apply(this,[t]);n.unselectNode(t)}},r.prototype.nodeDelete=function(n){var i,f,r,e,u;for(this.model.treeViewSettings.nodeDelete&&(this.treeView.option({nodeDelete:this.model.treeViewSettings.nodeDelete}),this.treeView._trigger("nodeDelete",n),this.treeView.option({nodeDelete:t.proxy(this.nodeDelete,this)})),i=0;i<n.removedNodes.length;i++)for(this.currentText=this.getElementText(n.removedNodes[i]),this.checkContains(this.currentText)&&(this.maintainHiddenValue(),this.removeText(this.currentText)),f=t(n.removedNodes[i]).find("ul.e-treeview-ul"),r=0;r<f.length;r++)for(e=t(f[r]).find("li.e-item"),u=0;u<e.length;u++)this.currentText=this.getElementText(e[u]),this.checkContains(this.currentText)&&(this.maintainHiddenValue(),this.removeText(this.currentText));this.getLi().length===0&&this.addEmptyRecord()},r.prototype.onTreeKeyPress=function(n){this.onKeyPressTreeView(n);this.model.treeViewSettings.keyPress&&(this.treeView.option({keyPress:this.model.treeViewSettings.keyPress}),this.treeView._trigger("keyPress",n),this.treeView.option({keyPress:t.proxy(this.onTreeKeyPress,this)}))},r.prototype.onTreeReady=function(n){this.finalize();this.calcScrollTop();this.removeLoadingClass();this.model.treeViewSettings.ready&&(this.treeView.option({ready:this.model.treeViewSettings.ready}),this.treeView._trigger("ready",n),this.treeView.option({ready:t.proxy(this.onTreeReady,this)}));var i={model:this.model,cancel:!1,type:"actionComplete",treedetails:n};this._trigger("actionComplete",i)},r.prototype.onNodeCheckUncheck=function(n){this.OnMouseClick(n);this.model.treeViewSettings.nodeCheck?(this.treeView.option({nodeCheck:this.model.treeViewSettings.nodeCheck}),this.treeView._trigger("nodeCheck",n),this.treeView.option({nodeCheck:t.proxy(this.onNodeCheckUncheck,this)})):this.model.treeViewSettings.nodeUncheck&&(this.treeView.option({nodeUncheck:this.model.treeViewSettings.nodeUncheck}),this.treeView._trigger("nodeUncheck",n),this.treeView.option({nodeUncheck:t.proxy(this.onNodeCheckUncheck,this)}))},r.prototype.onNodeCollapseExpand=function(n){n.isInteraction&&this.calcScrollTop();this.model.treeViewSettings.nodeExpand?(this.treeView.option({nodeExpand:this.model.treeViewSettings.nodeExpand}),this.treeView._trigger("nodeExpand",n),this.treeView.option({nodeExpand:t.proxy(this.onNodeCollapseExpand,this)})):this.model.treeViewSettings.nodeCollapse&&(this.treeView.option({nodeCollapse:this.model.treeViewSettings.nodeCollapse}),this.treeView._trigger("nodeCollapse",n),this.treeView.option({nodeCollapse:t.proxy(this.onNodeCollapseExpand,this)}));n.isInteraction&&this.refreshPopup()},r.prototype.onNodeSelectUnselect=function(n){this.OnMouseClick(n);this.model.treeViewSettings.nodeSelect?(this.treeView.option({nodeSelect:this.model.treeViewSettings.nodeSelect}),this.treeView._trigger("nodeSelect",n),this.treeView.option({nodeSelect:t.proxy(this.onNodeSelectUnselect,this)})):this.model.treeViewSettings.nodeUnselect&&(this.treeView.option({nodeUnselect:this.model.treeViewSettings.nodeUnselect}),this.treeView._trigger("nodeUnselect",n),this.treeView.option({nodeUnselect:t.proxy(this.onNodeSelectUnselect,this)}))},r.prototype.waitingCss=function(n){var t="e-dropdowntreeview ";return this.model.treeViewSettings.cssClass=ej.isNullOrUndefined(this.model.treeViewSettings.cssClass)?"":this.model.treeViewSettings.cssClass,t=t.concat(this.model.treeViewSettings.cssClass),t+" "+n},r.prototype.targetElementBinding=function(){var n=this.element.parents().last()[0];this.model.targetId&&(this.popupTree=n.querySelector("#"+this.model.targetId))},r.prototype.calcScrollTop=function(){var n=this.scrollerObj?this.scrollerObj.scrollTop():0;this.refreshScroller();this.scrollerObj&&this.scrollerObj.option("scrollTop",n)},r.prototype.renderScroller=function(){var n=this;ej.isNullOrUndefined(this.popupListWrapper)||this.dropbtnRTL();ej.isNullOrUndefined(this.popupListWrapper)||(this.setListWidth(),this.setListHeight());ej.isNullOrUndefined(this.popupListWrapper)||(t(this.popupScroller).css({height:"",width:""}),t(this.popupList).ejScroller({height:this.getPopupHeight(),width:0,scrollerSize:20}),this.scrollerObj=t(this.popupList).ejScroller("instance"),t(this.popupList).find("div.e-scrollbar div").attr("unselectable","on"),this.setListPosition(),this.popUpShow||t(this.popupListWrapper).css({display:"none",visibility:"visible"}),this.changeSkin(this.model.cssClass))},r.prototype.getPopupHeight=function(){var n=t(this.popupListWrapper).height();if(this.model.headerTemplate&&this.headerTemplate&&(n-=t(this.headerTemplate).height()),this.model.footerTemplate&&this.footerTemplate&&(n-=t(this.footerTemplate).height()),this.model.enableFilterSearch&&this.inputSearch){var i=t(this.inputSearch).parent(".e-in-wrap")[0],r=t(i).css("height"),u=t(i).css("margin-top"),f=t(i).css("margin-bottom");n-=parseInt(r)+parseInt(u)+parseInt(f)}return n},r.prototype.isSingleSelect=function(){return this.model.treeViewSettings.showCheckbox||this.model.treeViewSettings.allowMultiSelection?!1:!0},r.prototype.isFilterInput=function(){return this.model.enableFilterSearch?t(this.inputSearch).val()!==""?!0:!0:!1},r.prototype.getParentData=function(n){var i=this;t.map(this.filterData,function(t){t[i.treeView.model.fields.id].toString()===n.toString()&&(i.addData.push(t),ej.isNullOrUndefined(t[i.treeView.model.fields.parentId])||i.getParentData(t[i.treeView.model.fields.parentId]))})},r.prototype.OnMouseClick=function(n){var u=this,i,r;if(this.model.treeViewSettings.showCheckbox&&n.type==="nodeSelect"||(i=n.currentElement,!this.model.enabled||this.model.readOnly||this.readOnly))return!1;this.model.enableFilterSearch&&this.filterData!==null&&this.filterData!==this.normalData&&(r=!1,this.addData=[],t.map(this.normalData,function(t){t[u.treeView.model.fields.id]===n.id&&(r=!0)}),r||this.getParentData(n.id));i.hasClass("e-disable")||(this.uiInteract=!0,this.activeItem=i[0],this.currentText=this.treeView.getNode(n.currentElement[0]).text,this.isSingleSelect()?this.enterTextBoxValue(n):this.model.treeViewSettings.showCheckbox?(this.activeItemIndex=n.selectedNodes,n.type==="nodeCheck"?this.enterTextBoxValue(n):this.removeTextBoxValue(n)):i.hasClass("e-li-active")?this.enterTextBoxValue(n):this.removeTextBoxValue(n))},r.prototype.checkElements=function(n){var u=this.model.treeViewSettings.showCheckbox?this.treeView.getCheckedNodes():this.treeView.getSelectedNodes(),t,i,r,f;if(this.selectedElements=u,this.currentText=this.getElementText(n.currentElement[0]),this.checkContains(this.currentText)||(this.maintainHiddenValue(),this.addText(this.currentText)),this.model.treeViewSettings.showCheckbox&&n.currentCheckedNodes.length>1)for(t=1;t<n.currentCheckedNodes.length;t++)this.currentText=n.currentCheckedNodes[t].text,this.maintainHiddenValue(),this.addText(this.currentText);for(t=0;t<this.valueContainer.length;t++){for(i=!0,r=0;r<u.length;r++)if(f=this.getElementText(u[r]),ej.isNullOrUndefined(this.model.treeViewSettings.fields))if(this.valueContainer.indexOf(f)>-1){i=!0;break}else i=!1;else if(this.getMappedField(this.valueContainer[t],"value","text")===f){i=!0;break}else this.treeView.dataSource()instanceof ej.DataManager||(i=!1);i||(this.removeText(this.valueContainer[t]),t=t-1)}this.isVisibleInViewport(n.currentElement[0])||n.currentElement[0].scrollIntoView()},r.prototype.getElementText=function(n){var i=this.treeView.getNode(n),t=i.text;return this.model.textMode==="fullPath"&&(t=this.getPath(n)),t},r.prototype.unCheckElements=function(n){var r=n.currentUncheckedNodes,u,i,f;if(this.model.treeViewSettings.showCheckbox)for(r=n.currentUncheckedNodes,i=0;i<r.length;i++)f=r[i].text,u=this.getPath(t(this.getLi()).find("#"+r[i].id)[0]),this.currentText=this.model.textMode==="None"?f:u,this.maintainHiddenValue(),this.removeText(this.currentText);else this.currentText=this.getElementText(n.currentElement),this.maintainHiddenValue(),this.removeText(this.currentText);this.model.treeViewSettings.autoCheck&&this.ensureRootCheck(n)},r.prototype.ensureRootCheck=function(n){for(var i,f,e,u=t(n.currentElement).parents("ul.e-treeview-ul"),r=0;r<u.length;r++)t(u[r]).parent("li").length&&(i=t(u[r]).siblings("[role=presentation]").text(),f=ej.isNullOrUndefined(this.model.treeViewSettings.fields)?i:(ej.isNullOrUndefined(this.model.treeViewSettings.fields.text)&&ej.isNullOrUndefined(this.model.treeViewSettings.fields.value))?i:this.getMappedField(i,"text","value"),this.checkContains(f)&&(e=this.getPath(t(u[r]).siblings("[role=presentation]")[0]),i=this.model.textMode==="None"?i:e,this.removeText(i)))},r.prototype.maintainHiddenValue=function(){var n,t,i;this.model.textMode==="fullPath"&&this.currentText.indexOf(this.model.fullPathDelimiter)!=-1?(t=this.currentText.split(this.model.fullPathDelimiter),n=t[t.length-1]):n=this.currentText;this.model.treeViewSettings.fields&&this.model.treeViewSettings.fields.text&&this.model.treeViewSettings.fields.value&&(i=this.getMappedField(n,"text","value"));this.hiddenValue=ej.isNullOrUndefined(i)?this.currentText:i},r.prototype.getMappedField=function(n,t,i){var u,e,o=this.treeMapping(t),s=this.treeMapping(i),r,f;if(this.treeView.dataSource()instanceof ej.DataManager){if(this.treeView.dataSource()instanceof ej.DataManager&&this.isSingleSelect())for(f=0;f<this.getSelectedElements().length;f++)e=this.getSelectedElements()[f].id}else for(u=this.model.treeViewSettings.fields.dataSource,r=0;r<u.length;r++)ej.getObject(o[0],u[r])==n&&(e=ej.getObject(s[0],u[r]));return e},r.prototype.getActiveItem=function(){var n=this.getLi();return n[this.activeItemIndex]},r.prototype.parseValue=function(n){var t=parseInt(n);return isNaN(t)?n:t},r.prototype.getLi=function(){return this.treeView._liList},r.prototype.getPath=function(n){var u,i,r;return ej.isNullOrUndefined(n)?null:(u=this.treeView._getPath(t(n)),i=u.split("/"),i.shift(),r=this.validateDelimiter(this.model.fullPathDelimiter),r=this.model.delimiter!==this.model.fullPathDelimiter?this.model.fullPathDelimiter:"/",i.join(r))},r.prototype.addText=function(n){var i,u,t,r,f;if(this.checkContains(this.hiddenValue))return!1;for(i=["element","visibleInput"],t=0;t<i.length;t++)u=i[t]==="element"?this.hiddenValue:n,r=i[t]==="element"?this[i[t]][0]:this[i[t]],r.value&&r.value!==""?(f=r.value.split(this.model.delimiter),f.push(u),i[t]==="element"&&this.updateValueContainer(u,"push"),r.value=f.join(this.model.delimiter)):(r.value=u,i[t]==="element"&&this.updateValueContainer(u,"push"))},r.prototype.updateValueContainer=function(n,t,i){t==="push"?this.valueContainer.push(n):t==="pop"&&(ej.isNullOrUndefined(i)?this.valueContainer.pop():this.valueContainer.splice(i,1))},r.prototype.removeText=function(n){var r=this.element[0].value.split(this.model.delimiter),f=this.visibleInput.value.split(this.model.delimiter),u,e,i;ej.isNullOrUndefined(this.model.treeViewSettings.fields)||(u=this.getMappedField(n,"text","value"));e=ej.isNullOrUndefined(u)?n.toString():u.toString();i=t.inArray(e,r);i>=0&&(r.splice(i,1),this.updateValueContainer(n,"pop",i),f.splice(i,1));this.element[0].value=r.join(this.model.delimiter);this.visibleInput.value=f.join(this.model.delimiter)},r.prototype.setFullPathDelimiter=function(n){var r=this.validateDelimiter(n),i=this.model.fullPathDelimiter;this.model.fullPathDelimiter=r!==this.model.delimiter?r:i;this.model.text&&(this.model.text=this.setPath(this.model.text,i),t(this.visibleInput).val(this.model.text));ej.isNullOrUndefined(this.value())||(this.value(this.setPath(this.value,i)),this.element.val(this.value()))},r.prototype.setPath=function(n,t){for(var i=[],i=n.split(this.model.delimiter),r=0;r<i.length;r++)i[r]=i[r].split(t).join(this.model.fullPathDelimiter);return i.join(this.model.delimiter)},r.prototype.checkContains=function(n){var u=t(this.element[0]).val().toString(),r=u.split(this.model.delimiter),i;for(this.contains=!1,i=0;i<r.length;i++)if(this.parseValue(r[i])===this.parseValue(n)){this.contains=!0;break}return this.contains},r.prototype.removeTextBoxValue=function(n){this.uiInteract=!0;this.removeID=!0;this.checkedStatus=!0;var t={text:this.currentText,selectedText:this.currentText,isChecked:this.checkedStatus};if(this._trigger("select",t)){this.setWatermark();return}this.unCheckElements(n);this.checkedStatus=!1;this.onValueChange();this.setWatermark()},r.prototype.enterTextBoxValue=function(n){var r=!0,u,i,f;if(this.isWatermark||(this.hiddenSpan.style.display="none"),u=n.currentElement,i=this.getPath(u),this.currentFullPath=ej.isNullOrUndefined(i)?this.currentFullPath:i,f={text:this.currentText,selectedText:this.currentText,value:this.currentText,isChecked:this.checkedStatus},this._trigger("select",f)){this.setWatermark();return}this.isSingleSelect()?this.isSingleSelect()?(this.maintainHiddenValue(),this.model.textMode==="fullPath"?(t(this.visibleInput).val(this.currentFullPath),this.element.val(this.currentFullPath)):(t(this.visibleInput).val(this.currentText),this.element.val(this.hiddenValue)),this.isVisibleInViewport(n.currentElement[0])||n.currentElement[0].scrollIntoView()):r=!1:this.checkElements(n);r&&(this.checkedStatus=!0,this.onValueChange());this.setWatermark();this.uiInteract=!1},r.prototype.onValueChange=function(){var n,t,i;this.updateText();ej.util.valueFunction(this.value)===this.element.val()||ej.util.valueFunction(this.value)===null&&this.element.val()===""||(this.updateValue(this.element.val()),n=this.model.treeViewSettings.showCheckbox?this.treeView.getCheckedNodes():this.treeView.getSelectedNodes(),t={text:this.visibleInput.value,value:this.element.val(),selectedText:this.currentText,isChecked:this.checkedStatus,isInteraction:!!this.uiInteract,selectedItems:n},this._trigger("change",t),this.model.treeViewSettings.showCheckbox&&(i={isChecked:this.checkedStatus,text:this.visibleInput.value,value:this.element.val(),model:this.model},this._trigger("checkChange",i)),this.uiInteract=!1)},r.prototype.selectItem=function(n){this.isSingleSelect()||this.clearTextboxValue();this.activeItemIndex=n;this.enterTextBoxValue()},r.prototype.addLoadingClass=function(){this.isPopupShown()?t(this.popupListWrapper).addClass("e-load"):(t(this.dropdownbutton).addClass("e-load"),t(this.drpbtnspan).removeClass("e-icon e-arrow-sans-down"));this.readOnly=!0},r.prototype.removeLoadingClass=function(){t(this.dropdownbutton).removeClass("e-load");t(this.drpbtnspan).addClass("e-icon e-arrow-sans-down");this.readOnly=!1;ej.isNullOrUndefined(this.popupListWrapper)||t(this.popupListWrapper).removeClass("e-load");this.loadPopup&&this.OnPopupHideShow()},r.prototype.refreshPopup=function(){this.model.popupSettings.width!=="auto"||this.validatePixelData(this.minPopupWidth)?this.validatePixelData(this.minPopupWidth)&&t(this.popupListWrapper).css({"min-width":this.validatePixelData(this.minPopupWidth)}):t(this.popupListWrapper).css({"min-width":t(this.wrapper).width()});this.scrollerObj!==undefined&&this.refreshScroller();this.setListPosition()},r.prototype.refreshScroller=function(){t(this.popupList).find(".e-content, .e-vhandle").removeAttr("style");t(this.popupList).find(".e-vhandle div").removeAttr("style");t(this.popupList).children(".e-content").removeClass("e-content");var n=this.isPopupShown();this.popupList.style.display="block";this.popupListWrapper.style.display="block";this.scrollerObj.model.height=Math.ceil(this.getPopupHeight());this.scrollerObj.refresh();this.model.enablePopupResize||(this.popupList.style.height="auto",this.popupListWrapper.style.height="auto");this.scrollerObj.option("scrollTop",0);n||(this.popupListWrapper.style.display="none")},r.prototype.setListPosition=function(){var n=this.wrapper,i=this.getOffset(n),f,c=t(document).scrollTop()+t(window).height()-(i.top+t(n).outerHeight()),l=i.top-t(document).scrollTop(),u=t(this.popupListWrapper).outerHeight(),e=t(this.popupListWrapper).outerWidth(),r=i.left,o=t(n).outerHeight(),a=(o-t(n).height())/2,v=this.getZindexPartial(),s=3,h;h=u<c||u>l?i.top+o+s:i.top-u-s-a;f=t(document).scrollLeft()+t(window).width()-r;(this.model.enableRTL||e>f&&e<r+t(n).outerWidth())&&(r-=t(this.popupListWrapper).outerWidth()-t(n).outerWidth());t(this.popupListWrapper).css({left:r+"px",top:h+"px","z-index":v})},r.prototype.getOffset=function(n){return ej.getOffset(t(n))},r.prototype.getZindexPartial=function(){return ej.getZindexPartial(this.element,this.popupListWrapper[0])},r.prototype.OnWindowResize=function(){!ej.isNullOrUndefined(this.model)&&this.isPopupShown()&&this.refreshPopup()},r.prototype.OnKeyPress=function(n){this.model.enableIncrementalSearch&&n.keyCode!==13;n.keyCode===32&&this.preventDefaultAction(n)},r.prototype.isVisibleInViewport=function(n){if(ej.isNullOrUndefined(n))return!1;var t=n.getBoundingClientRect(),i=this.popupListWrapper.getBoundingClientRect();return i.top<t.top&&i.bottom>t.bottom?!0:!1},r.prototype.onKeyPressTreeView=function(n){var u=n.keyCode,t,i,r;if(!ej.isNullOrUndefined(n.currentElement)){t=n.currentElement[0];i=this.isVisibleInViewport(t);switch(u){case 40:if(this.preventDefaultAction(n.event,!0),!i){t.scrollIntoView(!0);break}break;case 38:if(this.preventDefaultAction(n.event,!0),!i){t.scrollIntoView(!1);break}break;case 36:if(!i){t.scrollIntoView(!0);break}break;case 35:if(!i){t.scrollIntoView(!1);break}}r=this.scrollerObj?this.scrollerObj.scrollTop():0;this.scrollerObj&&this.scrollerObj.option("scrollTop",r)}},r.prototype.OnKeyDown=function(n){var i,r,u;if(this.uiInteract=!0,this.model.enabled&&!ej.isNullOrUndefined(this.popupListWrapper)){i=n.keyCode?n.keyCode:n.which?n.which:n.charCode;r=this.getLi();this.listSize=r.length;u=t(this.popupList).height();switch(i){case 38:n.altKey?ej.isNullOrUndefined(this.popupTree)||this.hideResult():t(this.popupTree).focus();break;case 40:n.altKey?ej.isNullOrUndefined(this.popupTree)||(this.showResult(),this.model.enableFilterSearch?this.inputSearch.focus():t(this.popupTree).focus(),this.preventDefaultAction(n)):t(this.popupTree).focus();break;case 9:case 27:this.isPopupShown()&&this.hideResult()}}},r.prototype.targetFocus=function(){if(this.model.enabled&&!this.isFocused){this.isWatermark||t(this.hiddenSpan).css("display","none");t(this.wrapper).addClass("e-focus e-popactive");this.isFocused=!0;var n={model:this.model,cancel:!1,type:"focus"};this._trigger("focus",n)}},r.prototype.targetBlur=function(){if(this.model.enabled){this.isFocused=!1;t(this.wrapper).removeClass("e-focus e-popactive");this.setWatermark();var n={model:this.model,cancel:!1,type:"blur"};this._trigger("blur",n)}},r.prototype.OnDocumentClick=function(n){this.model.enabled&&!this.model.readOnly&&(t(n.target).is(this.popupList)||t(n.target).parents(".e-ddl-popup").is(this.popupListWrapper)||t(n.target).is(this.visibleInput)||t(n.target).parents(".e-ddl").is(this.wrapper)?t(n.target).is(this.inputSearch)?this.inputSearch.focus():(t(n.target).is(this.popupList)||t(n.target).parents(".e-ddl-popup").is(this.popupListWrapper))&&this.preventDefaultAction(n):this.hideResult())},r.prototype.enableSearch=function(){var n=this;this.model.enableFilterSearch&&(ej.isNullOrUndefined(this.popupListWrapper)||this.inputSearch||(this.inputSearch=ej.buildTag("input#"+this.id+"_inputSearch.e-input","",{},{type:"text","data-role":"none"})[0],jQuery(this.popupListWrapper).prepend(jQuery("<span>").addClass("e-atc e-search").append(jQuery("<span>").addClass("e-in-wrap ").append(this.inputSearch).append(jQuery("<span>").addClass(" e-icon e-search")))),this._on(t(this.inputSearch),"keyup",this.onFilterKeyUp),this._on(t(this.inputSearch),"keydown",function(t){var i=t.keyCode||t.which;i===9&&(t.preventDefault(),n.wrapper.focus(),n.hideResult())})))},r.prototype.onFilterKeyUp=function(n){switch(n.keyCode){case 16:case 17:case 18:case 36:case 35:case 13:case 9:n.stopPropagation();n.preventDefault();break;case 37:case 39:case 38:case 40:case 33:case 34:case 27:break;default:this.debounce(n)}},r.prototype.debounce=function(){var t=this,n=this,i=this.inputSearch.value.toLocaleLowerCase();this.filterTimer&&clearTimeout(this.filterTimer);this.filterTimer=setTimeout(function(){if(n._trigger("filtering",{text:i,updateData:function(i){t.isupdateData=!0;t.inputSearch.value.trim()===""?n.treeView.option({fields:t.normalData}):(t.normalData=n.model.treeViewSettings.fields,n.treeView.option({fields:i}),t.filterData=n.treeView.model.fields.dataSource);n.treeView.model.ready=function(){n.addData.length>0&&(n.treeView.addNodes(n.addData.reverse(),null),n.addData=[]);n.refreshPopup()}}}),!t.isupdateData)if(i.length>0)n.filterNodes(i);else{var r=n.popup.querySelector(".e-nosuggestion");r&&(r.remove(),n.popupTree.style.display=n.treeStyle);n.showNodes(Array.prototype.slice.call(n.treeView.element[0].querySelectorAll("li")));n.refreshPopup()}t.isupdateData=!1},80)},r.prototype.refreshSearch=function(){this.resetSearch();this.refreshPopup()},r.prototype.filterNodes=function(n){var f=Array.prototype.slice.call(this.treeView.element[0].querySelectorAll("li")),o=f.length,u,e,r,i;for(t(this.popupListWrapper).find(".e-atc.e-search .e-search").addClass("e-cross-circle").removeClass("e-search"),this._on(t(this.popupListWrapper).find(".e-cross-circle"),"mousedown",this.refreshSearch),e=this.model.filterType==="startwith"?new RegExp("^\\s*"+n.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"),"im"):this.model.filterType==="endwith"?new RegExp("\\s*"+n.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&$")+"$","im"):new RegExp("\\s*"+n.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"),"im"),u=0;u<o;u++)r=f[u],this.matchNode(r,e)?(this.handleVisibleParents(r),this.visibleNodes.push(r)):this.handleHiddenNode(r);this.hideNodes(this.hiddenNodes);this.showNodes(this.visibleNodes);this.visibleNodes.length<=0?this.popup.querySelector(".e-nosuggestion")||(i=document.createElement("div"),t(i).addClass("e-nosuggestion"),i.style.padding="14px 16px",i.style.textAlign="center",i.innerHTML=this.getLocalizedLabels("noRecordsTemplate"),this.popupScroller.appendChild(i),this.treeStyle=this.popupTree.style.display,this.popupTree.style.display="none"):(i=this.popup.querySelector(".e-nosuggestion"),i&&(i.remove(),this.popupTree.style.display=this.treeStyle));this.visibleNodes=[];this.hiddenNodes=[];this.refreshPopup()},r.prototype.showNodes=function(n){var r,i,u;for(this.treeView.expandNode(n),i=0,u=n.length;i<u;i++)r=n[i],this.treeView.showNode(t(r)),r.style.display=""},r.prototype.hideNodes=function(n){for(var i,r=0,u=n.length;r<u;r++)i=n[r],this.treeView.hideNode(t(i)),i.style.display="none"},r.prototype.matchNode=function(n,t){var i=this.treeView.getText(n);return i.match(t)?!0:!1},r.prototype.handleVisibleParents=function(n){for(var i,u,f=this.getNodeParents(n),e=f.length,r=0;r<e;r++)i=f[r],t.inArray(i,this.visibleNodes)===-1&&this.visibleNodes.push(i),u=t.inArray(i,this.hiddenNodes),u!==-1&&this.hiddenNodes.splice(u,1)},r.prototype.handleHiddenNode=function(n){t.inArray(n,this.visibleNodes)===-1&&t.inArray(n,this.hiddenNodes)===-1&&this.hiddenNodes.push(n)},r.prototype.getNodeParents=function(n){for(var r=[],i=this.treeView.getParent(n);i.length>0;)if(t.inArray(i[0],this.visibleNodes)!==-1)break;else r.push(i[0]),i=this.treeView.getParent(i);return r},r.prototype.getLocalizedLabels=function(n){return this.localizedLabels[n]===undefined?ej.DropDownTree.Locale["en-US"][n]:this.localizedLabels[n]},r}(ej.WidgetBase);window.ej.widget("ejDropDownTree","ej.DropDownTree",new i)})(jQuery);ej.DropDownTree.TextMode={none:"none",fullPath:"fullPath"};ej.DropDownTree.Locale=ej.DropDownTree.Locale||{};window.ejDropDownTree=null;ej.DropDownTree.Locale["default"]=ej.DropDownTree.Locale["en-US"]={noRecordsTemplate:"No Records Found",actionFailureTemplate:"The Request Failed"}});
