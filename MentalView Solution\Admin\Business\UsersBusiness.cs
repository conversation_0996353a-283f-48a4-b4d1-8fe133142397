﻿using Admin;
using Admin.Business;
using Admin.Data.Model;
using Microsoft.EntityFrameworkCore;

namespace Admin.Business
{
    public class UsersBusiness : IUsersBusiness
    {
        private Admin.Data.Model.MentalViewContext dbContext;
        private IConfiguration configuration;
        private ILogger<UsersBusiness> logger;

        public UsersBusiness(Admin.Data.Model.MentalViewContext dbContext, IConfiguration configuration, ILogger<UsersBusiness> logger)
        {
            this.dbContext = dbContext;
            this.logger = logger;
            this.configuration = configuration;
        }

        public async Task<List<Admin.Data.Model.User>> GetAllUsers(Int64 tenantId)
        {
            try
            {
                //Validation
               
                //Query
                IQueryable<Admin.Data.Model.User> query = this.dbContext.Users.AsNoTracking().AsQueryable();
                query = query.Where(x => x.TenantId == tenantId).OrderBy(e => e.UserId);

                //Διαβάζει τα δεδομένα.
                List<Admin.Data.Model.User> roles = await query.ToListAsync();
                
                return roles;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

       
        public async Task<Admin.Data.Model.User?> GetUser(Int64 userId)
        {
            try
            {
                //Query
                IQueryable<Admin.Data.Model.User> query = this.dbContext.Users.AsNoTracking().Where(x => x.UserId == userId);
                Admin.Data.Model.User? role = await query.FirstOrDefaultAsync();

                //Result
                return role;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task CreateOrUpdateUser(User user)
        {
            try
            {
                //Validation
                if (user == null)
                {
                    throw new Exception(GlobalResources.InvalidDataMessage);
                }

                //UserValidator validator = new UserValidator();
                //FluentValidation.Results.ValidationResult result = validator.Validate(role);
                //string validationErrors = string.Empty;
                //if (!result.IsValid)
                //{
                //    foreach (var failure in result.Errors)
                //    {
                //        validationErrors += failure.ErrorMessage + ". ";
                //    }
                //    throw new ApplicationException(validationErrors);
                //}

                //List<ValidationResult> validationResults = new List<ValidationResult>();
                //ValidationContext validationContext = new ValidationContext(role);
                //if (Validator.TryValidateObject(role, validationContext, validationResults, true) == false)
                //{
                //    string validationErrors = string.Empty;
                //    foreach (CompositeValidationResult compValidationResult in validationResults)
                //    {
                //        foreach (ValidationResult validationResult in compValidationResult.Results)
                //        {
                //            validationErrors += validationResult.ErrorMessage + ". ";
                //        }
                //    }
                //    throw new ApplicationException(validationErrors);
                //}

                //Query
                this.dbContext.Attach(user);
                await this.dbContext.SaveChangesAsync();

                //Response
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { user.UserId });
                throw;
            }
        }

        //public async Task DeleteUser(Int64 roleId)
        //{
        //    //User? role = await this.dbContext.Users.Where(x => x.UserId == roleId).AsNoTracking().FirstAsync();
        //    //if (role != null)
        //    //{
        //    //    role.ObjectState = ObjectState.Deleted;
        //    //    this.dbContext.Attach(role);
        //    //    this.dbContext.SaveChanges();
        //    //}
        //}

        public async Task<User?> CheckUsernameAndPassword(Int64 tenantId, string username, string password)
        {
            User? role = await this.dbContext.Users.Where(x => x.TenantId != tenantId && x.Username == username && x.Password == password).FirstOrDefaultAsync();
            return role;
        }


    }
}
