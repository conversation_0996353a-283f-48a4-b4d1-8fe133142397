/*!
*  filename: ej.listview.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["jsrender","./../common/ej.globalize.min","./../common/ej.core.min","./../common/ej.data.min","./../common/ej.scroller.min","./../common/ej.touch.min","./ej.checkbox.min"],n):n()})(function(){(function($,ej,undefined){ej.widget("ejListViewBase","ej.ListViewBase",{_addToPersist:["selectedItemIndex","checkedIndices"],defaults:{height:null,width:null,selectedItemIndex:-1,enableGroupList:!1,enableAjax:!1,enableCache:!1,enablePersistence:!1,ready:null,load:null,itemRequestCount:5,totalItemsCount:5,loadComplete:null,ajaxBeforeLoad:null,ajaxSuccess:null,ajaxError:null,ajaxComplete:null,ajaxSettings:{type:"GET",cache:!1,async:!0,dataType:"html",contentType:"html",url:"",data:{}},renderTemplate:!1,templateId:null,persistSelection:!1,preventSelection:!1,dataSource:[],query:null,allowVirtualScrolling:!1,virtualScrollMode:"normal",showHeader:!1,showHeaderBackButton:!1,cssClass:"",headerTitle:"Title",headerBackButtonText:null,enableFiltering:!1,enableCheckMark:!1,checkedIndices:[],locale:"en-US"},observables:["selectedItemIndex","dataSource"],selectedItemIndex:ej.util.valueFunction("selectedItemIndex"),dataSource:ej.util.valueFunction("dataSource"),checkedIndices:ej.util.valueFunction("checkedIndices"),_updateModelItems:function(){var ang_attr,i,ang_li,ele,ul,groupid,ulindex,index,element,groupTitle,index1,element1,primaryKey,parentPrimaryKey;if(this.model.items=eval(this.model.items),ang_attr=this.model.items,ang_attr.length){for(ul=ej.buildTag("ul.e-m-clearall"),i=0;i<ang_attr.length;i++)ang_attr[i].items=[],ang_li=ej.buildTag("li"),this.model.items[i].template&&ang_li.html(this.model.items[i].template),!this.model.items[i].childId&&this.model.items[i].href&&(this.model.items[i].childId="page_"+parseInt(Math.random().toFixed(3)*1e3)),this.model.items[i].renderTemplate&&(this._storedTemplate[i]||(this.model.items[i].templateId?(ele=this._tempContent.find("#"+this.model.items[i].templateId).remove(),this._storedTemplate[i]=ele[0].nodeName&&ele[0].nodeName.toLowerCase()=="script"?ej.getClearString(ele[0].innerHTML):ele[0].outerHTML):this._storedTemplate[i]=this.model.items[i].template,this.model.items[i].templateId=this._storedTemplate[i])),this.model.fieldSettings&&(this.model.fieldSettings=$.extend(this.defaults.fieldSettings,this.model.fieldSettings)),ul.append(ang_li);this.element.append(ul)}else for(ul=this.element.find(">ul"),groupid=1,ulindex=0;ulindex<ul.length;ulindex++){for(this._listitems=$(ul[ulindex]).find(">li"),index=0;index<this._listitems.length;index++)for(element=this._listitems[index],ej.getAttrVal(element,"data-ej-primarykey")==null&&$(element).find("ul").length?(primaryKey=Math.round(Math.random()*100),$(element).attr("data-ej-primarykey",primaryKey)):primaryKey="",groupTitle=ej.getAttrVal($(ul[ulindex]),"data-ej-grouplistitle")?ej.getAttrVal($(ul[ulindex]),"data-ej-grouplistitle"):"GroupList"+groupid,this.model.items.push(this._itemsObjectCollection($(this._listitems[index]),primaryKey,null,groupTitle)),this._nestedListitems=$(this._listitems[index]).find("ul >li"),index1=0;index1<this._nestedListitems.length;index1++)element1=this._nestedListitems[index1],ej.getAttrVal(element1,"data-ej-primarykey")==null&&$(element1).find("ul").length?(primaryKey=Math.round(Math.random()*100),$(element1).attr("data-ej-primarykey",primaryKey)):primaryKey="",parentPrimaryKey=ej.getAttrVal($($(element1).parent()).closest("li"),"data-ej-primarykey"),this.model.items.push(this._itemsObjectCollection($(this._nestedListitems[index1]),primaryKey,parentPrimaryKey,groupTitle));groupid++}},_load:function(){this._orgEle=this.element.clone();this._index=0;this._items=[];this._dummyUl=[];this._virtualCount=0;this._liItemHeight=0;this._requestType=null;this._checkedValues=[];this._checkedValues=this.model.checkedIndices;this.model.id=this.element[0].id;this.model.allowVirtualScrolling&&this.model.query!=null&&(this._savedQueries=this.model.query.clone());this.model.load&&this._trigger("load");this.model.fieldSettings=eval(this.model.fieldSettings);this.model.fieldSettings&&(ej.DataManager&&this._dataUrl instanceof ej.DataManager?this._dataUrl.dataSource.offline||this._dataUrl.dataSource.json&&this._dataUrl.dataSource.json.length>0?(this.model.allowVirtualScrolling&&this.model.virtualScrollMode=="continuous"&&ej.isNullOrUndefined(this._totalitemscount)&&(this.model.totalItemsCount=this._dataUrl.dataSource.json.length),this._queryPromise(0,this,this.model.totalItemsCount,null)):this._queryPromise(0,this,this.model.totalItemsCount,null):ej.isNullOrUndefined(this._dataUrl)||this._dataUrl instanceof ej.DataManager||ej.isNullOrUndefined(this._totalitemscount)?this._renderControl():(this._dataUrl=ej.DataManager(this._dataUrl),this._rawList=ej.DataManager(this._dataUrl.dataSource.json).executeLocal(ej.Query().take(this.model.totalItemsCount).clone()),this.model.dataSource=this._rawList,this._renderControl()))},_loadVirtualData:function(n){var u,c,f,r,i,s,l,t,o,h,e;if(this._dummyUl=[],u=this._renderLists(),$.views.helpers({_checkAjaxUrls:this._checkAjaxUrl,_checkImgUrls:this._checkImgUrl,_checkIsChecked:this._checkIsCheck,Object:this,ej:ej}),this.jsRender=ej.buildTag("script#"+this.model.id+"_Template","",{},{type:"text/x-jsrender"}),this.jsRender[0].text=ej.browserInfo().name=="msie"&&ej.browserInfo().version<10?u[0].outerHTML.replace(/&gt;/g,">"):u[0].outerHTML.replace(/&gt;/g,">"),this.jsChildRender=ej.buildTag("script#"+this.model.id+"_ChildTemplate","",{},{type:"text/x-jsrender"}),this.model.allowVirtualScrolling==!0&&this.model.virtualScrollMode=="normal"&&(c=ej.buildTag("div")),f=ej.buildTag("ul."+this._prefixClass+"childcontainer "+this._prefixClass+"list-hdr "+this._prefixClass+"clearall"),u.addClass(this._prefixClass+"childli"),f[0].innerHTML="{{for items}}"+u[0].outerHTML+"{{/for}}",r=n,r)for(f.empty().html($(this.jsRender).render(r)),i=f.clone().find("li"),s="",this._initEJCheckBox&&this._initEJCheckBox(f),l=this,t=0;t<r.length;t++)r[t]&&r[t].attributes&&$.each(r[t].attributes,function(n,r){r&&r.name.toLowerCase()=="class"&&(s=$(i[t]).attr("class"))}),this.model.allowVirtualScrolling&&this.model.virtualScrollMode=="normal"?(o=this.realUllength,this.model.enableCheckMark&&($(this.element.find("li").eq(o).find(".e-chkbox-wrap")).length!=0?(h=$(this.element.find("li").eq(o).find(".e-chkbox-wrap"))[0],e=h.getAttribute("aria-checked")=="true"?!0:!1,$($(i[t]).find(".e-lv-check")).ejCheckBox({checked:e})):(e=!1,this._removeIndex!=t?$($(i[t]).find(".e-lv-check")).ejCheckBox({checked:e}):$(i[t]).find(".e-lv-check").remove())),$(i[t]).find(".e-lv-check").length!=0&&$(i[t]).find(".e-lv-check").parent().addClass("e-lv-checkdiv"),this._dummyUl.push(i[t]),this._removeEmptyElements(),this.scrollerObj.refresh()):this.model.allowVirtualScrolling&&this.model.virtualScrollMode=="continuous"&&(this.model.enableCheckMark&&$($(i[t]).find(".e-lv-check")).ejCheckBox(),$(i[t]).find(".e-lv-check").parent().addClass("e-lv-checkdiv"),this._lContainer.find("ul").append(i[t]),this._removeEmptyElements(),this._lContainer.find("ul li.e-lastitem").removeClass(this._prefixClass+"lastitem"),this._lContainer.find("ul li:last").addClass(this._prefixClass+"lastitem"),this.scrollerObj.refresh()),this.realUllength+=1},_removeEmptyElements:function(){this.eLi=this.element.find("li."+this._prefixClass+"list");this.eLi.removeEleEmptyAttrs();this.eLi.find("."+this._prefixClass+"chevron-right_01").removeEleEmptyAttrs()},_renderControl:function(){var proxy=this,ulContainer,hdr,innerDiv,group,length,title,ulItem,i,groupdiv,ul,j,eLi,totalHeight,ulItems;if(this.element.addClass(this._prefixClass+"parentlv "+this.model.cssClass),this._lbEle=ej.buildTag("div."+this._rootCSS),this._lbEle.addClass("subpage"),this.model.allowVirtualScrolling==!0&&this.model.virtualScrollMode=="normal"&&(ulContainer=ej.buildTag("div."+this._prefixClass+"sub-list-container")),this._lContainer=ej.buildTag("div."+this._prefixClass+"list-container#"+this.model.id+"_container"),ul=this._hasDataSource()?this.element.find(">ul"):this.element.find("ul:first"),this.model.showHeader&&(hdr=this._renderHeader(this.model.id,!1,this.model.headerTitle,this.model.headerBackButtonText),this._lbEle.prepend(hdr)),this._hasDataSource()&&!this.model.renderTemplate&&this.element.empty(),this.model.renderTemplate&&(this.model.templateId?this._tempContent.find("#"+this.model.templateId).length&&(this._template=this._tempContent.find("#"+this.model.templateId),ej.destroyWidgets(this._template),this._template[0].nodeName.toLowerCase()!="script"&&this._template.remove(),this._template=this._template[0].nodeName&&this._template[0].nodeName.toLowerCase()=="script"?ej.getClearString(this._template[0].innerHTML):this._template[0].outerHTML):(ej.destroyWidgets(this.element),this._template=this.element.html(),this.element.empty()),this._lContainer.addClass(this._prefixClass+"template-list")),!this.model.renderTemplate||this._hasDataSource()||this.model.enableGroupList){if(!this.model.renderTemplate||this.model.renderTemplate&&this._hasDataSource()||this.model.renderTemplate&&this.model.enableGroupList){if(this._model_index=0,this.model.enableGroupList){if(this._lContainer.addClass(this._prefixClass+"grouped"),innerDiv=ej.buildTag("div."+this._prefixClass+"grouplist"),ul=this.element.children(),ul.length||this.dataSource().length)for(group=this.dataSource().length?ej.DataManager(this.dataSource()).executeLocal(ej.Query().from(this.dataSource()).group(this.model.fieldSettings.groupID)):ej.DataManager(this.model.items).executeLocal(ej.Query().from(this.model.items).group(this.model.fieldSettings.groupID)),length=group.length,i=0;i<length;i++)this._items=group[i].items,this._hasDataSource()?(ulItem=ej.buildTag("ul","",{},{"data-ej-grouplisttitle":group[i].key}),this._lbEle.append(ulItem)):ulItem=ul[i],title=this._hasDataSource()?group[i].key:ej.getAttrVal($(ulItem),"data-ej-grouplisttitle","GroupList"+(i+1)),$(ulItem).attr("data-ej-grouplisttitle",title),groupdiv=ej.buildTag("div."+this._prefixClass+"groupdiv",ej.buildTag("div."+this._prefixClass+"grouptitle",title)),innerDiv.append(groupdiv.append(this._renderListItems($(ulItem).addClass(this._prefixClass+"grouped"))));else this._template&&(ej.destroyWidgets(this._template),innerDiv[0].innerHTML=this._template);this._lContainer.append(innerDiv);ej.widget.init&&ej.widget.init(innerDiv)}else{if(this._hasDataSource())for(ul=ej.buildTag("ul"),this._items=this.dataSource(),this._items=eval(this._items),j=0;j<this._items.length;j++)this._items[j].href&&!this._items[j].childId&&(this._items[j].childId="page_"+parseInt(Math.random().toFixed(3)*1e3));ul.length?(ej.destroyWidgets(ul),this.model.allowVirtualScrolling==!0&&this.model.virtualScrollMode=="normal"?(ulContainer.append(this._renderListItems(ul)),$(ul).find("li").attr("data-ej-page",0),this._lContainer.append(ulContainer)):this._lContainer.append(this._renderListItems(ul))):this._template&&(ej.destroyWidgets(this._template),this._lContainer[0].innerHTML=this._template)}this.element.prepend(this._lbEle);this.model.enableFiltering&&this._createFilterWrapper(this._lbEle);this._setHeightWidth();this._lContainer.ejScroller({height:this._lContainer.outerHeight(),width:0,scrollerSize:20,scroll:function(n){proxy._onScroll(n)}});this.scrollerObj=this._lContainer.ejScroller("instance");this._lbEle.append(this._lContainer);ej.widget.init&&ej.widget.init(this._lContainer);eLi=this.element.find("li."+this._prefixClass+"list");eLi.removeEleEmptyAttrs();eLi.find("."+this._prefixClass+"chevron-right_01").removeEleEmptyAttrs();this.model.allowVirtualScrolling==!0&&this.model.virtualScrollMode=="normal"&&(this._liItemHeight=$(this.element.find("li")[0]).outerHeight(),totalHeight=this._totalCount*this._liItemHeight,$(".e-sub-list-container ul").height(totalHeight));ej.widget.init&&ej.widget.init(this._lbEle);ulItems=this.element.find("ul");ulItems.find("li:first").addClass(this._prefixClass+"firstitem");ulItems.find("li:last").addClass(this._prefixClass+"lastitem");this._liEl=this.element.find("li");this.selectedItemIndex()>=0&&!this.model.preventSelection&&this.model.persistSelection&&!ej.getBooleanVal(ulItems[this.selectedItemIndex()],"data-preventselection",this.model.preventSelection)&&ej.getBooleanVal(ulItems[this.selectedItemIndex()],"data-persistselection",this.model.persistSelection)&&(this._currentItem=$(this._liEl[this.selectedItemIndex()]),this._prevItem=this._currentItem,this._currentItem.removeClass(this._prefixClass+"state-default").addClass(this._prefixClass+"state-active"));$(this.element.find("."+this._prefixClass+"lv-check").parent()).addClass(this._prefixClass+"lv-checkdiv");this.model.renderTemplate&&$(this.element.find("."+this._prefixClass+"lv-check").parent()).addClass(this._prefixClass+"template-checkmark");$(this.element.find("."+this._prefixClass+"lv-check").closest("li."+this._prefixClass+"list")).addClass(this._prefixClass+"list-check");$(this.element.find("."+this._prefixClass+"lv-input").closest("."+this._prefixClass+"lv-filter")).addClass(this._prefixClass+"lv-inputdiv");this._wireEvents()}}else this.element.append(this._lbEle.append(this._lContainer)),$(this.element.find("."+this._prefixClass+"lv-check").parent()).addClass(this._prefixClass+"template-checkmark"),this._template&&(this._lContainer.append(this._template),ej.widget.init&&ej.widget.init(this._lContainer));this._setHeightWidth();this.model.height!==null&&this._lContainer.height()>this.model.height&&this._refreshScroller(this._lContainer,!1);this.scrollerObj&&(this.scrollerObj.refresh(),$(this.scrollerObj.element).find(".e-vhandlespace").css("height",$(this.scrollerObj.element).find(".e-vhandlespace").height()-1));this.model.loadComplete&&this._trigger("loadComplete")},_createFilterWrapper:function(n){var t=this._createFilterDiv(),i=ej.buildTag("a."+this._prefixClass+"lv-anchor",ej.buildTag("span."+this._prefixClass+"input-btn"),{},{Title:"Clear text","data-role":"none"});$(t).append(i);n.append(t)},_refreshScroller:function(n,t){this.model.virtualScrollMode=="continuous"&&(this._lContainer.find(".e-content, .e-vhandle,.e-vhandle div").removeAttr("style"),this._lContainer.css({display:"block"}),this.scrollerObj&&(this.scrollerObj.model.height=this._lContainer.height(),this.scrollerObj.refresh(),this.scrollerObj.option("scrollTop",0)));n.find(".e-vhandle div").removeAttr("style");var i;i=t?this.model.showHeader&&this.model.enableFiltering?this.model.height-(ej.getDimension(this._lbEle.find("."+this._prefixClass+"header"),"outerHeight")+ej.getDimension($(id).find("."+this._prefixClass+"lv-filter"),"outerHeight"))-2:this.model.showHeader?this.model.height-ej.getDimension(this._lbEle.find("."+this._prefixClass+"header"),"outerHeight")-2:this.model.enableFiltering?this.model.height-ej.getDimension($(id).find("."+this._prefixClass+"lv-filter"),"outerHeight")-2:this.model.height-2:this.model.showHeader&&this.model.enableFiltering?this.model.height-(ej.getDimension(this._lbEle.find("."+this._prefixClass+"header"),"outerHeight")+$("."+this._prefixClass+"lv-filter").height())-2:this.model.showHeader?this.model.height-ej.getDimension(this._lbEle.find("."+this._prefixClass+"header"),"outerHeight")-2:this.model.enableFiltering?this.model.height-$("."+this._prefixClass+"lv-filter").height()-2:this.model.height-2;this.scrollerObj&&(this.scrollerObj.model.height=i)},_renderListItems:function(n){var u,o,e,t,r,f,i;if(n.addClass(this._prefixClass+"list-hdr "+this._prefixClass+"clearall"),this._liItems=n.find("li"),(this._liItems.length&&!this._hasDataSource()||this._hasDataSource())&&(this._renderParentChildTemplate(),this._hasDataSource()||this.model.enableGroupList||(this._items=this.model.items),u=this._items,o=this,this.model.allowVirtualScrolling?(e=[],t={parent:u,child:e}):t=this._filterParentChild(u),t.child.length&&this._childRendering(t.child),t.parent))for(n.empty().html($(this.jsRender).render(t.parent)),r=n.find(">li"),f="",i=0;i<r.length;i++)t.parent[i]&&t.parent[i].attributes&&($.each(t.parent[i].attributes,function(n,t){t&&t.name.toLowerCase()=="class"&&(f=$(r[i]).attr("class"))}),$(r[i]).addEleAttrs(t.parent[i].attributes),$(r[i]).addClass(f));return this._initEJCheckBox&&this._initEJCheckBox(n),n},_onScroll:function(n){var i,u;if(n.scrollTop){var r=n.scrollTop,t=this,u=this.dataSource();this.model.actionBegin&&this._trigger("actionBegin",{});this.realUllength=this.element.find("li.e-list").length;this.model.allowVirtualScrolling&&this.model.virtualScrollMode=="continuous"?(i=$(this.element.find("li")[0]).outerHeight(),r+this.model.height>=this.element.find("li").length*i&&(this._updateLoadingClass(!0),ej.DataManager&&this._dataUrl instanceof ej.DataManager&&!ej.isNullOrUndefined(this._dataUrl.dataSource.url)?this._queryPromise(this.realUllength,t,this.realUllength+this.model.itemRequestCount,n):(ej.DataManager&&this._dataUrl instanceof ej.DataManager&&this._dataUrl.dataSource.offline&&this._dataUrl.dataSource.json&&this._dataUrl.dataSource.json.length>0||!ej.isNullOrUndefined(this._dataUrl)&&!(this._dataUrl instanceof ej.DataManager))&&window.setTimeout(function(){t._updateLoadingClass(!1)},300))):this.model.allowVirtualScrolling&&this.model.virtualScrollMode=="normal"&&(u=this.dataSource(),ej.DataManager&&this._dataUrl instanceof ej.DataManager&&n.scrollTop!=n.scrollData.scrollOneStepBy+n.scrollData.scrollable&&window.setTimeout(function(){t._virtualCount==0&&t._loadList()},300))}},_localDataVirtualScroll:function(){var n=this._rawList.length;return ej.DataManager(this._dataUrl.dataSource.json).executeLocal(ej.Query().skip(n).take(this.model.totalItemsCount).clone())},_loadList:function(){var n,o,i,s,h,e;this._virtualCount++;var c=this.scrollerObj.scrollTop(),t=this,u=0,r,f=null;if(this._currentPageindex=Math.round(c/(this._liItemHeight*this._items.length)),$.inArray(this._currentPageindex,this._virtualPages.sort(function(n,t){return n-t}))!=-1)if(this._currentPageindex==0){if($.inArray(this._currentPageindex+1,this._virtualPages)!=-1)return this._virtualCount--,!1;this._currentPageindex=this._currentPageindex+1}else if($.inArray(this._currentPageindex-1,this._virtualPages)!=-1){if($.inArray(this._currentPageindex+1,this._virtualPages)!=-1)return this._virtualCount--,!1;this._currentPageindex=this._currentPageindex+1}else this._currentPageindex=this._currentPageindex-1;for(r=!($.inArray(this._currentPageindex-1,this._virtualPages)!=-1),this._updateLoadingClass(!0),n=this._virtualPages.length-1;n>=0;n--)if(this._virtualPages[n]<this._currentPageindex){u=this._virtualPages[n];n+1==this._virtualPages.length||(f=this._virtualPages[n+1]);break}if(o=r?(this._currentPageindex-1)*this._items.length:this._currentPageindex*this._items.length,i=ej.Query().range(o,this._currentPageindex*this._items.length+this._items.length),ej.DataManager&&this._dataUrl instanceof ej.DataManager&&!ej.isNullOrUndefined(this._dataUrl.dataSource.url)){if(e=r?(this._currentPageindex-1)*this._items.length:this._currentPageindex*this._items.length,i=this._dataUrl.dataSource.offline==!0?ej.Query().skip(e).take(this.model.itemRequestCount):this._getQuery().skip(e),r){for(n=0;n<i.queries.length;n++)if(i.queries[n].fn=="onTake"){i.queries.splice(n,1);break}i.take(2*this._items.length)}t._trigger("actionBegin")||(s=t._dataUrl.executeQuery(i),s.done(function(n){t._appendVirtualList(n.result,u,t._currentPageindex,f,r);t._trigger("actionSuccess",{e:n})}).fail(function(n){t._virtualCount--;t._trigger("actionFailure",{e:n})}).always(function(n){t._updateLoadingClass(!1);t._trigger("actionComplete",{e:n})}))}else ej.DataManager&&this._dataUrl instanceof ej.DataManager&&this._dataUrl.dataSource.offline&&this._dataUrl.dataSource.json&&this._dataUrl.dataSource.json.length>0?(this._appendVirtualList(this._localDataVirtualScroll(),u,this._currentPageindex,f,r),this._updateLoadingClass(!1)):(h=ej.DataManager(t.model.dataSource).executeLocal(i),this._appendVirtualList(h,u,this._currentPageindex,f,r),this._updateLoadingClass(!1))},_appendVirtualList:function(n,t,i,r,u){var f,s,c,e,o,l,h;if(this._virtualCount--,$.inArray(i,this._virtualPages.sort(function(n,t){return n-t}))!=-1)return!1;if(u&&$.inArray(i-1,this._virtualPages.sort())!=-1&&(n.splice(0,this._items.length),u=!1),f=this._items.length,s=$("<ul>"),c=u?(i-1)*f*this._liItemHeight-(t*f+f)*this._liItemHeight:i*f*this._liItemHeight-(t*f+f)*this._liItemHeight,c!=0&&s.append($("<span>").addClass("e-virtual").css({display:"block",height:c})),this._loadVirtualData(n),$(this._dummyUl).attr("data-ej-page",i),u&&$(this._dummyUl).slice(0,f).attr("data-ej-page",i-1),s.append(this._dummyUl),o=this.element.find("ul"),e=(i*f+f)*this._liItemHeight,e=r!=null?r*f*this._liItemHeight-e:o.height()-e,e!=0&&s.append($("<span>").addClass("e-virtual").css({display:"block",height:e})),l=o.find("li[data-ej-page="+t+"]").last(),l.next().remove(),s.children().insertAfter(l),this._removeEmptyElements(),this._virtualPages.push(i),u&&this._virtualPages.push(i-1),ej.DataManager&&this._dataUrl instanceof ej.DataManager&&this._dataUrl.dataSource.offline&&this._dataUrl.dataSource.json&&this._dataUrl.dataSource.json.length>0)for(h=0;h<n.length;h++)this._rawList.push(n[h]);this._virtualUl=o.clone(!0);o.find("li.e-lastitem").removeClass(this._prefixClass+"lastitem");o.find("li:last").addClass(this._prefixClass+"lastitem")},_updateLoadingClass:function(n){var t=this.element.attr("id");return n?($("#"+t+" .e-list-container.e-scroller .e-sub-list-container").addClass("e-load"),$(".e-lv .e-list.e-state-default").css("opacity","0.5")):($("#"+t+" .e-list-container.e-scroller .e-sub-list-container").removeClass("e-load"),$(".e-lv .e-list.e-state-default").css("opacity","")),this},_queryPromise:function(n,t,i,r){var f,u;ej.DataManager&&this._dataUrl instanceof ej.DataManager&&(r==null?(t._requestType="init",u=this._dataUrl.executeQuery(this._getQuery())):r!=null&&(t._requestType="request",this._trigger("itemRequest",{event:r,isInteraction:!0}),f=this._dataUrl.dataSource.offline==!0?ej.Query():this._savedQueries.clone(),u=this._dataUrl.executeQuery(f.range(n,i)),this._updateLoadingClass(!0)),u.done(function(i){t._requestType=="init"&&(t._totalCount=i.count,t.model.dataSource=i.result,t._renderControl(),t._virtualPages=[0]);t._requestType=="request"&&(t._trigger("actionBeforeSuccess",i),t.realUllength=n,t._loadVirtualData(i.result));t._trigger("actionSuccess",{e:i})}).fail(function(n){t._trigger("actionFailure",{e:n})}).always(function(n){t._updateLoadingClass(!1);t._requestType=null;t._trigger("actionComplete",{e:n})}))},_getQuery:function(){var n,t=this.model.fieldSettings,i=ej.Query(),r,u;if(ej.isNullOrUndefined(this.model.query)){r=[];for(u in t)u!=="tableName"&&t[u]&&r.push(t[u]);r.length>0&&i.select(r)}else this.model.query&&(i=this.model.query.clone());return this.model.allowVirtualScrolling&&(i.requiresCount(),i.take(this.model.totalItemsCount)),n=this.model.dataSource.dataSource,t&&(n&&n.url&&!n.url.match(t.tableName+"$")||n&&!n.url||!n)&&(ej.isNullOrUndefined(t.tableName)||i.from(t.tableName)),i},_filterParentChild:function(n){var t=ej.DataManager(n),i=t.executeLocal(ej.Query().from(n).where(ej.Predicate(this.model.fieldSettings.parentPrimaryKey,ej.FilterOperators.notEqual,null)).group(this.model.fieldSettings.parentPrimaryKey)),r=t.executeLocal(ej.Query().from(n).where(ej.Predicate(this.model.fieldSettings.parentPrimaryKey,ej.FilterOperators.equal,null)));return{parent:r,child:i}},_childRendering:function(n){var t=this;n.length&&(t.element.append($(t.jsChildRender).render(n)),$.each(n,function(i,r){var u;ej.widget.init&&ej.widget.init(t.element.find("#child"+r.key));var o=t.element.find("#child"+r.key).find("ul"),e=o.find("li"),f=0;for(u=0;u<e.length;u++)n[f].items[u]&&n[f].items[u].attributes&&$(e[u]).addEleAttrs(n[f].items[u].attributes);f++}))},_renderParentChildTemplate:function(){var t=this._renderLists(),u,i,n,r;$.views.helpers({_checkAjaxUrls:this._checkAjaxUrl,_checkImgUrls:this._checkImgUrl,_checkIsChecked:this._checkIsCheck,Object:this,ej:ej});this.jsRender=ej.buildTag("script#"+this.model.id+"_Template","",{},{type:"text/x-jsrender"});this.jsRender[0].text=ej.browserInfo().name=="msie"&&ej.browserInfo().version<10?t[0].outerHTML.replace(/&gt;/g,">"):t[0].outerHTML.replace(/&gt;/g,">");this.jsChildRender=ej.buildTag("script#"+this.model.id+"_ChildTemplate","",{},{type:"text/x-jsrender"});this.model.allowVirtualScrolling==!0&&this.model.virtualScrollMode=="normal"&&(u=ej.buildTag("div"));i=ej.buildTag("ul."+this._prefixClass+"childcontainer "+this._prefixClass+"list-hdr "+this._prefixClass+"clearall");t.addClass(this._prefixClass+"childli");i[0].innerHTML="{{for items}}"+t[0].outerHTML+"{{/for}}";n=ej.buildTag("div."+this._rootCSS+" subpage "+this._prefixClass+"childitem","",{},{id:"{{if key}}child{{>key}}{{else "+this.model.fieldSettings.childId+"}}{{>"+this.model.fieldSettings.childId+"}}{{else}}{{/if}}",style:"display:none"});r=ej.buildTag("div."+this._prefixClass+"list-container","",{},{id:"{{if key}}child{{>key}}{{else "+this.model.fieldSettings.childId+"}}{{>"+this.model.fieldSettings.childId+"}}{{else}}{{/if}}_container"});this.model.showHeader&&n.append(this._renderHeader("{{if key}}child{{>key}}{{else "+this.model.fieldSettings.childId+"}}{{>"+this.model.fieldSettings.childId+"}}{{else}}{{/if}}",!0,"Title"));this.model.enableFiltering&&this._createFilterWrapper(n);this.model.allowVirtualScrolling==!0&&this.model.virtualScrollMode=="normal"?n.append(r.append(u.append(i))):n.append(r.append(i));ej.browserInfo().name=="msie"&&ej.browserInfo().version<10?this.jsChildRender[0].text=n[0].outerHTML.replace(/&gt;/g,">"):this.jsChildRender[0].innerHTML=n[0].outerHTML.replace(/&gt;/g,">")},_renderChild:function(n){this._currentItem.attr("data-childid",n);this._currentItem.attr("data-childtitle")||this._currentItem.attr("data-childtitle",this._currentItem.text());this._createListDiv(n);this._div.hide();this._container=ej.buildTag("div."+this._prefixClass+"list-container#"+n+"_container");this.element.append(this._div)},_getText:function(n){return $(n).clone().children().remove().end().text()},_checkImgUrl:function(){var n=this.getRsc("helpers","Object"),t=this.data[n.model.fieldSettings.imageUrl];return $.support.pushstate||(t=typeof App=="object"?App.route.makeUrlAbsolute(t,!0):t),"<img src = "+t+" class='"+n._prefixClass+"list-img "+n._prefixClass+"rel "+n._prefixClass+"user-select'/>"},_checkAjaxUrl:function(){var t=this.data.href,u=this.data.childId,e=this.data.renderTemplate,o=this.data.templateId,n=this.getRsc("helpers","Object"),f=this.getRsc("helpers","ej"),r=n._currentPage(n),i;t&&t.indexOf("#")!=-1&&t!="#"?n._storedContent[u]||(r.find(t).show(),i=r.find(t).clone(),r.find(t).hide(),n._storedContent[u]=i[0].nodeName&&i[0].nodeName.toLowerCase()=="script"?f.getClearString(i[0].innerHTML):i[0].outerHTML):n._storedContent[this.index]&&(n._storedContent=f._pushValue(n._storedContent,"",this.index))},_currentPage:function(n){return n._prefixClass=="e-m-"?ej.getCurrentPage():$("body")},_checkIsCheck:function(){return this.data[this.getRsc("helpers","Object").model.fieldSettings.checked]?!0:!1},_onTouchStartHandler:function(n){if(this._mouseDown={x:ej.isNullOrUndefined(n.clientX)?n.touches[0].clientX:n.clientX,y:ej.isNullOrUndefined(n.clientY)?n.touches[0].clientY:n.clientY},$(n.target.parentElement).hasClass(this._prefixClass+"disable")||$(n.currentTarget).hasClass(this._prefixClass+"disable"))return!1;ej.isDevice()||ej._preventDefaultException(n.target,this._preventDefaultException)||n.preventDefault&&n.preventDefault();ej.isWindows&&ej.isWindows()&&ej._touchStartPoints(n,this);this._currentItem=$(n.currentTarget);this._scroll=!1;ej.getBooleanVal(this._currentItem,"data-preventSelection",this.model.preventSelection)||this._addSelection();this.model.renderMode!="windows"||this.model.windows.preventSkew||this._currentItem.addClass(this._prefixClass+"m-skew-center");ej.isNullOrUndefined(n)||(this._eventtrigger=$(n.target));this.model.mouseDown&&this._triggerStartEvent(this._returnData());ej.listenEvents([this._liEl,this._liEl],[ej.endEvent(),ej.moveEvent(),ej.cancelEvent()],[this._touchEndDelegate,this._touchMoveDelegate,this._touchMoveDelegate],!1,this);$(window).on(ej.endEvent()+" MSPointerUp pointerup",this._docClickDelegate)},_onTouchMoveHandler:function(n){(ej.browserInfo().name=="msie"&&ej.browserInfo().version>8||ej.browserInfo().name!="msie")&&((ej.isNullOrUndefined(n.clientX)?n.changedTouches[0].clientX!==this._mouseDown.x:n.clientX)||(ej.isNullOrUndefined(n.clientY)?n.changedTouches[0].clientY!==this._mouseDown.y:n.clientY))&&(this._isMoved=!0,ej.isDevice()&&(!ej.isWindows||ej.isWindows&&!ej.isWindows()||ej.isWindows&&ej.isWindows()&&ej._isTouchMoved(n,this))&&(this._scroll=!0,ej.getBooleanVal(this._currentItem,"data-persistSelection",this.model.persistSelection)?this._prevItem&&this._prevItem[0]!=this._currentItem[0]&&this._removeSelection():this._removeSelection(),this.model.renderMode!="windows"||this.model.windows.preventSkew?!ej.getBooleanVal(this._currentItem,"data-preventselection",this.model.preventSelection)&&this._prevItem&&ej.getBooleanVal(this._currentItem,"data-persistSelection",this.model.persistSelection)&&this._prevItem.removeClass(this._prefixClass+"state-default").addClass(this._prefixClass+"state-active"):ej._removeSkewClass(this._currentItem)))},_onTouchEndHandler:function(n){var u,r,t,i,f,e;if(ej.browserInfo().name=="msie"&&ej.browserInfo().version==8&&n.stopImmediatePropagation(),this._isFromAjax=!1,ej.getBooleanVal(this._currentItem,"data-persistSelection",this.model.persistSelection)||this._removeSelection(),this.model.renderMode!="windows"||this.model.windows.preventSkew||ej._removeSkewClass(this._currentItem),this._scroll)return this._setCurrent(),this._unbindEvents(n),!1;if(!this._scroll){if(this._currentItem.find("."+this._prefixClass+"lv-check").length&&(u=this._currentItem.find("."+this._prefixClass+"lv-check"),this._prefixClass!="e-"||ej.isNullOrUndefined(n)||$(n.target).closest(".e-lv-checkdiv").length||this._toggleCheckboxValue(u)),r=this._currentItem.attr("data-childheaderbackbuttontext")==undefined?this._currentItem.closest("."+this._rootCSS+"").find("."+this._prefixClass+"header").length&&!this.model.showHeaderBackButton?this._currentItem.closest("."+this._rootCSS+"").find("."+this._prefixClass+"header ."+this._prefixClass+"htitle").text():"Back":ej.getAttrVal(this._currentItem,"data-childheaderbackbuttontext"),t=this._currentItem.attr("data-href"),this._currentItem.attr("data-navigateUrl"))this._touchEndEventHandler();else{if(i=this._isInsideNavigation?this._tempContent.find("[data-ajaxurl='"+this._convertToRelativeUrl(t)+"']"):this.element.find("[data-ajaxurl='"+this._convertToRelativeUrl(t)+"']"),ej.getBooleanVal(this._currentItem,"data-loadajax",this.model.enableAjax&&typeof t!="undefined"||!typeof t)&&(!this.model.enableCache||i.length==0)){i.length&&i.remove();(this._prefixClass=="e-"&&this._currentItem.hasClass("e-arrow")||this._prefixClass!="e-")&&this.loadAjaxContent(t,r);this._unbindEvents(n);this._isInsideNavigation&&this._nearestND.model.contentId&&this._closeNavigation();return}t&&t.indexOf("#")!=-1&&this._currentPage(this).find("#"+t.replace("#","")).length&&(this._renderChild(ej.getAttrVal(this._currentItem,"data-childid","page_"+parseInt(Math.random().toFixed(3)*1e3))),f=ej.buildTag("div."+this._prefixClass+"content",this._storedContent[this._currentItem.attr("data-childid")]),this._div.append(this._container.append(f)),ej.widget.init&&ej.widget.init(this._div));this._updateContent(this._currentItem,r);this.model.ready&&this._trigger("ready");this._isInsideNavigation&&(e=this._nearestND.model.contentId?!this._currentItem.attr("data-childid")||this._currentItem.attr("data-href"):!(this._currentItem.attr("data-childid")||this._currentItem.attr("data-href")));this._isInsideNavigation&&e&&this._closeNavigation();ej.isNullOrUndefined(n)||(this._eventtrigger=$(n.target));this._touchEndEventHandler(n)}this._prevItem=this._currentItem}this._unbindEvents(n)},_hasValue:function(n,t){for(var i=0;i<n.length;i++)if(n[i]==t)return!0},_generateData:function(n,t){var i=ej.DataManager(n).executeLocal(ej.Query().from(this.model.dataSource).where(ej.Predicate(typeof ej.getAttrVal(this._currentItem,"data-id")=="undefined"?this.model.fieldSettings.text:this.model.fieldSettings.id,ej.FilterOperators.equal,t)).group(t))[0];return i?i.items[0]:[]},_closeNavigation:function(){this.element.closest("."+this._prefixClass+"nb").ejNavigationDrawer("close")},_setCurrent:function(){this._prevItem&&ej.getBooleanVal(this._currentItem,"data-persistSelection",this.model.persistSelection)&&(this._currentItem=this._prevItem,this._currentItem.removeClass(this._prefixClass+"state-default").addClass(this._prefixClass+"state-active"))},_unbindEvents:function(n){n&&n.target.nodeName&&n.target.nodeName.toLowerCase()!="a"&&(this._scroll=!1);ej.listenEvents([this._liEl,this._liEl],[ej.endEvent(),ej.moveEvent(),ej.cancelEvent()],[this._touchEndDelegate,this._touchMoveDelegate,this._touchMoveDelegate],!0,this)},_addSelection:function(){this._scroll||(this._currentItem.closest("."+this._prefixClass+"list-container").find("."+this._prefixClass+"state-active").removeClass(this._prefixClass+"state-active").addClass(this._prefixClass+"state-default"),this._currentItem.removeClass(this._prefixClass+"state-default").addClass(this._prefixClass+"state-active"))},_removeSelection:function(){var n=this;n._currentItem.removeClass(this._prefixClass+"state-active").addClass(this._prefixClass+"state-default")},_setHeightWidth:function(){if(this.model.autoAdjustHeight)this.element.height(window.innerHeight);else if(this.model.height)this.element.height(this.model.height);else{var n=this.element[0].scrollHeight?this.element[0].scrollHeight:ej.getDimension(this._lbEle.find("."+this._prefixClass+"list-container"),"outerHeight");this.element.height(n)}this.model.width&&this.element.width(this.model.width)},_touchEndEventHandler:function(n){this._triggerEndEvent(this._returnData(),n)},_docClick:function(){this._scroll&&(this._setCurrent(),$(document).off(ej.endEvent()+" MSPointerUp pointerup",this._docClickDelegate),ej.listenEvents([this._liEl,this._liEl],[ej.endEvent(),ej.moveEvent()],[this._touchEndDelegate,this._touchMoveDelegate],!0,this),$(window).off(ej.endEvent()+" MSPointerUp pointerup",this._docClickDelegate))},_popStateNavigation:function(n,t){t.pageUrl&&this.model.enableFiltering&&this._initializeFiltering($("div[data-url='"+t.pageUrl.replace("#","")+"']"))},_anchorClickHandler:function(n){if(this._scroll)return ej.blockDefaultActions(n),!1},_onResize:function(){var n=this;setTimeout(function(){n._setHeightWidth()},ej.isAndroid()?200:0)},_onScrollStop:function(n){ej.blockDefaultActions(n)},_createDelegates:function(){this._anchorClickDelegate=$.proxy(this._anchorClickHandler,this);this._keyup=$.proxy(this._onKeyUp,this);this._touchStartDelegate=$.proxy(this._onTouchStartHandler,this);this._touchEndDelegate=$.proxy(this._onTouchEndHandler,this);this._touchMoveDelegate=$.proxy(this._onTouchMoveHandler,this);this._resizeDelegate=$.proxy(this._onResize,this);this._popStateDelegate=$.proxy(this._popStateNavigation,this);this._docClickDelegate=$.proxy(this._docClick,this)},_wireEvents:function(n,t){var i=this._liEl||t,r,u;if(i){this._createDelegates();r=n?"unbind":"bind";this.model.autoAdjustHeight&&(u=!ej.isDevice()&&"onorientationchange"in window?"orientationchange":"resize",ej.listenEvents([window],[u],[this._resizeDelegate],n,this));ej.listenEvents([i.find("a"),this.element.find("."+this._prefixClass+"lv-input"),i,i],["click","keyup",ej.startEvent(),ej.cancelEvent()],[this._anchorClickDelegate,this._keyup,this._touchStartDelegate,this._touchMoveDelegate],n,this);this._lContainer.on("scrollstop",$.proxy(this._onScrollStop,this));$("body")[r]("viewpopstate",this._popStateDelegate);this.model.enableFiltering&&this._initializeFiltering(this._lbEle)}},_initializeFiltering:function(n){this._searchItems=$(n).find("."+this._prefixClass+"list");this._emptyFilterTextValue(n);n.find("."+this._prefixClass+'list[style*="display: none"]').show();this._elementText=[];for(var t=0;t<this._searchItems.length;t++)$(this._searchItems[t])?this._elementText.push($.trim($(this._searchItems[t]).html().replace(new RegExp("<[^<]+>","g"),"").toLowerCase())):this._elementText.push("")},_onKeyUp:function(n){for(var t=0;t<this._searchItems.length;t++)this._elementText[t].indexOf(n.target.value.toLowerCase())==-1?$(this._searchItems[t]).css("display","none"):$(this._searchItems[t]).css("display","")},_setModel:function(n){var r=!1,t,i;for(t in n)if(i="_set"+t.charAt(0).toUpperCase()+t.slice(1),this[i]||t=="locale")switch(t){case"locale":ej.ListView.Locale[n[t]]&&(this.model.locale=n[t],this._setCulture(),this._setHeaderVal(this.model.id,this.model.headerTitle,this.model.headerBackButtonText));break;default:this[i](n[t])}else r=!0;r&&this._refresh()},_setTheme:function(n){if(n&&(this.model.theme=n,this._lbEle.removeClass("e-m-dark e-m-light e-default").addClass("e-m-"+this.model.theme),this.model.enableFiltering&&this.element.find("."+this._prefixClass+"text-input").ejmTextBox("model.theme",this.model.theme),this.model.showHeader&&this._lbEle.find("#"+this.model.id+"_header").ejmHeader("model.theme",this.model.theme),this.element.find("."+this._prefixClass+"childitem").length)){$(this.element.find("."+this._prefixClass+"childitem")).removeClass("e-m-dark e-m-light e-default").addClass("e-m-"+this.model.theme);var t=this.element.find("."+this._prefixClass+"childitem ."+this._prefixClass+"header"),i=this;t.each(function(n,t){$(t).ejmHeader("model.theme",i.model.theme)})}},_setDataSource:function(n,t){this._hasDataSource()&&n&&(t&&(this.model.fieldSettings=t),this._refresh())},_hasDataSource:function(){return this.dataSource()&&this.dataSource().length},_getElement:function(n){return n?this.element.find("#"+n):this._lbEle},_isEnable:function(n){return n.hasClass(this._prefixClass+"state-disabled")?!1:!0},_refresh:function(){this._destroy();this.element.addClass(this._rootCSS);this._load()},_clearElement:function(){this.element.removeAttr("class style");this.element.empty().html(this._orgEle.html())},_destroy:function(){this._prefixClass=="e-"&&ej.listenEvents([window],["resize"],[this._resizeDelegate],!0,this);this._wireEvents(!0);this._clearElement()},loadAjaxContent:function(n,t){var i=this,r,u;this._isFromAjax=!0;this._renderChild(ej.getAttrVal(this._currentItem,"data-childid","page_"+parseInt(Math.random().toFixed(3)*1e3)));(!$.support.pushstate||ej.isWindowsWebView())&&(r={content:i._div,item:i._currentItem,index:$(i._currentItem).index(),text:$(i._currentItem).text(),url:i.model.ajaxSettings.url?i.model.ajaxSettings.url:n});this.model.ajaxBeforeLoad&&this._trigger("ajaxBeforeLoad",r);u={cache:i.model.ajaxSettings.cache,async:i.model.ajaxSettings.async,type:i.model.ajaxSettings.type,contentType:i.model.ajaxSettings.contentType,url:ej.isWindowsWebView()?n:i.model.ajaxSettings.url?i.model.ajaxSettings.url:n,dataType:i.model.ajaxSettings.dataType,data:i.model.ajaxSettings.data,successHandler:function(r){var u=ej.buildTag("div."+this._prefixClass+"content",/<\/?body[^>]*>/gmi.test(r)?r.split(/<\/?body[^>]*>/gmi)[1]:r||""),f,e;i._div.append(i._container.append(u));i._updateContent(i._currentItem,t);f=i._prefixClass=="e-m-"?App.angularAppName:!1;(f||ej.angular.defaultAppName)&&ej.angular.compile(u);e={content:i._div,item:i._currentItem,index:$(i._currentItem).index(),text:$(i._currentItem).text(),url:i.model.ajaxSettings.url?i.model.ajaxSettings.url:n};i.model.ajaxSuccess&&i._trigger("ajaxSuccess",e)},errorHandler:function(t,r,u){var f={xhr:t,textStatus:r,errorThrown:u,item:i._currentItem,index:$(i._currentItem).index(),text:$(i._currentItem).text(),url:i.model.ajaxSettings.url?i.model.ajaxSettings.url:n};i.model.ajaxError&&i._trigger("ajaxError",f)},completeHandler:function(){var t={content:i._div,item:i._currentItem,index:$(i._currentItem).index(),text:$(i._currentItem).text(),url:i.model.ajaxSettings.url?i.model.ajaxSettings.url:n};i.model.ajaxComplete&&i._trigger("ajaxComplete",t)}};ej.sendAjaxRequest(u)},selectItem:function(n,t){n>=0&&this._isEnable($(this._getElement(t).find("li."+this._prefixClass+"list")[n]))&&(this.setActive(n,t),this._currentItem=$(this._getElement(t).find("li."+this._prefixClass+"list")[n]),this._prevItem=this._currentItem,this._onTouchEndHandler())},setActive:function(n,t){if(n>=0){var i=this._getElement(t);this._isEnable($(i.find("li."+this._prefixClass+"list")[n]))&&ej.getBooleanVal($(i.find("li."+this._prefixClass+"list")[n]),"data-persistSelection",this.model.persistSelection)&&(i.find("li."+this._prefixClass+"list."+this._prefixClass+"state-active").removeClass(this._prefixClass+"state-active").addClass(this._prefixClass+"state-default"),this._currentItem=$(i.find("li."+this._prefixClass+"list")[n]),this._prevItem=this._currentItem,this._currentItem.removeClass(this._prefixClass+"state-default").addClass(this._prefixClass+"state-active"))}},deActive:function(n,t){n>=0&&this._isEnable($(this._getElement(t).find("li."+this._prefixClass+"list")[n]))&&$(this._getElement(t).find("li."+this._prefixClass+"list")[n]).removeClass(this._prefixClass+"state-active").addClass(this._prefixClass+"state-default")},enableItem:function(n,t){n>=0&&$(this._getElement(t).find("li."+this._prefixClass+"list")[n]).removeClass(this._prefixClass+"disable").addClass(this._prefixClass+"state-default").find("a").removeClass(this._prefixClass+"disable").find("."+this._prefixClass+"lv-check").ejCheckBox("enable")},disableItem:function(n,t){n>=0&&$(this._getElement(t).find("li."+this._prefixClass+"list")[n]).addClass(this._prefixClass+"disable").removeClass(this._prefixClass+"state-default").find("a").addClass(this._prefixClass+"disable").find("."+this._prefixClass+"lv-check").ejCheckBox("disable")},removeCheckMark:function(n,t){this._removeIndex=n;var i=this._getElement(t);n>=0&&this._isEnable($(i.find("li."+this._prefixClass+"list")[n]))?$(i.find("li."+this._prefixClass+"list")[n]).find("."+this._prefixClass+"lv-checkdiv").remove():i.find("."+this._prefixClass+"lv-checkdiv").remove()},checkItem:function(n,t){n>=0&&this._isEnable($(this._getElement(t).find("li."+this._prefixClass+"list")[n]))&&this._setCheckboxValue($(this._getElement(t).find("."+this._prefixClass+"lv-check")[n]),!0);this._checkedValues.push(n);this.checkedIndices(this._checkedValues)},unCheckItem:function(n,t){n>=0&&this._isEnable($(this._getElement(t).find("li."+this._prefixClass+"list")[n]))&&this._setCheckboxValue($(this._getElement(t).find("."+this._prefixClass+"lv-check")[n]),!1);this._checkedValues.splice(this._checkedValues.indexOf(n),1)},checkAllItem:function(n){var t=this;this._getElement(n).find("."+this._prefixClass+"lv-check").each(function(i,r){t._isEnable($(t._getElement(n).find("li."+t._prefixClass+"list")[i]))&&t._setCheckboxValue($(r),!0)});this._checkStatevalue()},unCheckAllItem:function(n){this.model.checkedIndices=[];this._checkedValues=[];var t=this;this._getElement(n).find("."+this._prefixClass+"lv-check").each(function(i,r){t._isEnable($(t._getElement(n).find("li."+t._prefixClass+"list")[i]))&&t._setCheckboxValue($(r),!1)})},_checkStatevalue:function(){var n,t;for(this._currentItem=$(this._liEl),n=0;n<this._currentItem.length;n++)t=this.element.find("li").index(this._currentItem[n]),this._hasValue(this._checkedValues,t)?this._checkedValues.splice(this._checkedValues.indexOf(t),1):this._checkedValues.push(t);this.checkedIndices(this._checkedValues);this.model.checkedIndices=this.checkedIndices()},getActiveItem:function(n){return this._getElement(n).find("li."+this._prefixClass+"list."+this._prefixClass+"state-active")},getActiveItemText:function(n){return this._getElement(n).find("li."+this._prefixClass+"list."+this._prefixClass+"state-active").text()},getItemText:function(n,t){if(n>=0)return $(this._getElement(t).find("li."+this._prefixClass+"list")[n]).text()},getCheckedItems:function(n){var i,t,r;if(n!=undefined)return this._getElement(n).find("input."+this._prefixClass+"lv-check:checked").closest("li."+this._prefixClass+"list");for(i=[],t=0;t<this.checkedIndices().length;t++)r=this.element.find("li")[this.checkedIndices()[t]],i.push(r);return i},getCheckedItemsText:function(n){return $(this.getCheckedItems(n)).map(function(){return $(this).text()}).get()},hasChild:function(n,t){if(n>=0)return this.element.find("#"+$(this._getElement(t).find("li."+this._prefixClass+"list")[n]).attr("data-childid")).length?!0:!1},isChecked:function(n,t){if(n>=0)return $(this._getElement(t).find("li."+this._prefixClass+"list")[n]).find("input."+this._prefixClass+"lv-check").prop("checked")},showItem:function(n,t){n>=0&&$(this._getElement(t).find("li."+this._prefixClass+"list")[n]).css("visibility","")},hideItem:function(n,t){n>=0&&$(this._getElement(t).find("li."+this._prefixClass+"list")[n]).css("visibility","hidden")},show:function(n){this._getElement(n).css("visibility","")},hide:function(n){this._getElement(n).css("visibility","hidden")},_objectSplice:function(n,t){for(var i=0;i<n.length;i++)this.model.items.splice(t,0,n[i])},addItem:function(n,t,i){var o,s,f,u,e,r;if(typeof n=="object"&&(n=$(n)),t>=0)if(this._hasDataSource()){if(typeof n=="object")for(r=0;r<n.length;r++)this.dataSource().splice(t,0,n[r]);else this.dataSource().splice(t,0,this._itemsObjectCollection($(n),null,null,i));f=this.dataSource()}else $(".e-list-container").find("ul").length==0&&(o=ej.buildTag("ul"),s=ej.buildTag("li"),$(".e-list-container").find("div:first").append(o),$(".e-list-container > div > ul").append(s)),typeof n=="object"?this._objectSplice(n,t):this.model.items.splice(t,0,this._getLiAttributes(n,null,null,i)),f=this.model.items;else this._orgEle.children().append(n);if(u=this.model.enableGroupList?$(this.element.find("ul[data-ej-grouplisttitle= "+i+"]")):$(this.element.find("ul:visible")),ej.isNullOrUndefined(this.jsRender))this._renderControl(),$(this.element).find(".subpage").length>1&&$(this.element).find(".subpage:nth-child(2)").remove();else{if(typeof n=="object")for(r=0;r<n.length;r++)e=$($(this.jsRender).render(f[t+r])).insertBefore(u.children()[t]);else e=$($(this.jsRender).render(f[t])).insertBefore(u.children()[t]);ej.widget.init&&ej.widget.init(e);$(n).attrNotStartsWith(/^data-ej-/).length>0&&$(e).addEleAttrs($($(n).attrNotStartsWith(/^data-ej-/)))}this._processing(u);this._liEl=this.element.find("li."+this._prefixClass+"list");this._initEJCheckBox&&(this._initEJCheckBox(u),this.model.renderTemplate&&$(this.element.find("."+this._prefixClass+"lv-check").parent()).addClass(this._prefixClass+"template-checkmark"));this._setHeightWidth();this._wireEvents()},_processing:function(n){n.find("li."+this._prefixClass+"firstitem").removeClass(this._prefixClass+"firstitem");n.find("li:first").addClass(this._prefixClass+"firstitem");n.find("li."+this._prefixClass+"lastitem").removeClass(this._prefixClass+"lastitem");n.find("li:last").addClass(this._prefixClass+"lastitem");var t=n.find("li."+this._prefixClass+"list");t.removeEleEmptyAttrs();t.find("."+this._prefixClass+"chevron-right_01").removeEleEmptyAttrs();n.find("."+this._prefixClass+"lv-check").parent().addClass(this._prefixClass+"lv-checkdiv");n.find("."+this._prefixClass+"lv-check").closest("li."+this._prefixClass+"list").addClass(this._prefixClass+"list-check");n.find("."+this._prefixClass+"lv-input").closest("."+this._prefixClass+"lv-filter").addClass(this._prefixClass+"lv-inputdiv")},removeItem:function(n,t){var r=[],i,n;n>=0&&(element=this._getElement(t),i=$(element.find("li."+this._prefixClass+"list")[n]).attr("data-childid"),this.element.find($("#"+i).length)&&this.element.find($("#"+i)).remove(),$(element.find("li."+this._prefixClass+"list")[n]).remove(),n=[parseInt(n)],this.dataSource(this.dataSource().filter(function(t,i){if(n.indexOf(i)!=-1)r.push(t);else return!0})))},clear:function(){this.element.empty().html();this._liEl=this.element.find("li."+this._prefixClass+"list")},getItemsCount:function(n){return this._getElement(n).find("li."+this._prefixClass+"list").length},getActiveItemData:function(){if(this.getActiveItem().attr("data-id"))return this._generateData(this.dataSource().length?typeof this.dataSource()=="string"?eval(this.dataSource()):this.dataSource():this.model.items,this.getActiveItem().attr("data-id"))}});ej.VirtualScrollMode={Normal:"normal",Continuous:"continuous"}})(jQuery,Syncfusion),function($,ej,undefined){ej.widget("ejListView","ej.ListView",{_rootCSS:"e-lv e-js",validTags:["div"],_prefixClass:"e-",defaults:{fieldSettings:{navigateUrl:"navigateUrl",href:"href",enableAjax:"enableAjax",preventSelection:"preventSelection",persistSelection:"persistSelection",text:"text",enableCheckMark:"enableCheckMark",checked:"checked",primaryKey:"primaryKey",parentPrimaryKey:"parentPrimaryKey",imageClass:"imageClass",imageUrl:"imageUrl",childHeaderTitle:"childHeaderTitle",childId:"childId",childHeaderBackButtonText:"childHeaderBackButtonText",renderTemplate:"renderTemplate",templateId:"templateId",attributes:"attributes",mouseUp:"mouseUp",mouseDown:"mouseDown",groupID:"groupID",id:"id"},mouseDown:null,mouseUp:null,items:[]},dataTypes:{dataSource:"data",query:"data",itemRequestCount:"number",totalItemsCount:"number",fieldSettings:"data",renderMode:"enum",theme:"enum",enablePersistence:"boolean"},observables:["selectedItemIndex","dataSource"],selectedItemIndex:ej.util.valueFunction("selectedItemIndex"),dataSource:ej.util.valueFunction("dataSource"),checkedIndices:ej.util.valueFunction("checkedIndices"),_tags:[{tag:"items",attr:["navigateUrl","href","text","checked","primaryKey","parentPrimaryKey","imageClass","imageUrl","childHeaderTitle","childId","childHeaderBackButtonText","mouseUP","mouseDown","attributes","renderTemplate","templateId","enableAjax","preventSelection","persistSelection","enableCheckMark","attributes"],content:"template"}],_init:function(n){this._options=n;this._totalitemscount=n.totalItemsCount;this._preventDefaultException={tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT)$/};this._storedContent=[];this._storedTemplate=[];this._tempContent=$("body");this._touchStart=this.model.mouseDown;this._touchEnd=this.model.mouseUp;this._oldEle=this.element.clone();this._oldEle.find("ul").length&&(this.model.items=[]);this._indexVal=0;this._setCulture();this._updateModelItems();this._dataUrl=this.model.dataSource;ej.isNullOrUndefined(this._dataUrl.dataSource)||(this._rawList=ej.DataManager(this._dataUrl.dataSource.json).executeLocal(ej.Query().take(this.model.totalItemsCount).clone()));this._load();var t=this.element.closest(".e-nb.e-js");this._isInsideNavigation=t.length;this._isInsideNavigation&&(this._nearestND=t.ejNavigationDrawer("instance"));this._responsive()},_responsive:function(){$(window).on("resize",$.proxy(this._resizeHandler,this))},_resizeHandler:function(){$(this.element).parent().width()==null&&this.model==null?$(this.element).width(this.width):$(this.element).parent().width()<=this.model.width?(this.width=$(this.element).parent().width(),$(this.element).width(this.width)):$(this.element).width(this.model.width)},_itemsObjectCollection:function(n,t,i,r){var f,u;return ej.getAttrVal(n,"data-ej-rendertemplate")&&(f=this._tempContent.find("#"+ej.getAttrVal(n,"data-ej-templateid")),f.length?(u=f.remove(),this._storedTemplate[this._indexVal]=u[0].nodeName&&u[0].nodeName.toLowerCase()=="script"?ej.getClearString(u[0].innerHTML):u[0].outerHTML):this._storedTemplate[this._indexVal]||($(n)[0].innerHTML?this._storedTemplate[this._indexVal]=$(n)[0].innerHTML:this._storedTemplate[this._indexVal]&&this._tempContent.find("#"+template).length&&(u=this._tempContent.find("#"+template).remove(),u=u[0].nodeName&&u[0].nodeName.toLowerCase()=="script"?ej.getClearString(u[0].innerHTML):u[0].outerHTML,this._storedTemplate=ej._pushValue(this._storedTemplate,u,this._indexVal))),this._indexVal++),this._getLiAttributes(n,t,i,r)},_getLiAttributes:function(n,t,i,r){var u={};return u.groupID=r?r:"",u.text=ej.getAttrVal(n,"data-ej-text")?ej.getAttrVal(n,"data-ej-text"):this._getText(n),u.preventSelection=ej.getAttrVal(n,"data-ej-preventselection"),u.persistSelection=ej.getAttrVal(n,"data-ej-persistselection"),u.navigateUrl=ej.getAttrVal(n,"data-ej-navigateurl"),u.href=ej.getAttrVal(n,"data-ej-href"),u.checked=ej.getAttrVal(n,"data-ej-checked"),u.primaryKey=ej.getAttrVal(n,"data-ej-primarykey",t),u.parentPrimaryKey=ej.getAttrVal(n,"data-ej-parentprimarykey",i),u.imageClass=ej.getAttrVal(n,"data-ej-imageclass"),u.imageUrl=ej.getAttrVal(n,"data-ej-imageurl"),u.childHeaderTitle=ej.getAttrVal(n,"data-ej-childheadertitle"),u.childId=u.href?ej.getAttrVal(n,"data-ej-childid")?ej.getAttrVal(n,"data-ej-childid"):"page_"+parseInt(Math.random().toFixed(3)*1e3):"",u.childHeaderBackButtonText=ej.getAttrVal(n,"data-ej-childheaderbackbuttontext"),u.mouseUp=ej.getAttrVal(n,"data-ej-mouseUp"),u.mouseDown=ej.getAttrVal(n,"data-ej-mouseDown"),this.element.find("li").length>0&&(u.attributes=typeof n=="object"?n.attrNotStartsWith(/^data-ej-/):$(n).attrNotStartsWith(/^data-ej-/)),u.renderTemplate=ej.getAttrVal(n,"data-ej-rendertemplate"),u.templateId=this._storedTemplate[this._indexVal-1],u.enableAjax=ej.getAttrVal(n,"data-ej-enableajax"),u.enableCheckMark=ej.getAttrVal(n,"data-ej-enablecheckmark"),u},_renderLists:function(){var t=ej.buildTag("li","",{},{"class":this._prefixClass+"user-select "+this._prefixClass+"list "+this._prefixClass+"state-default{{if "+this.model.fieldSettings.primaryKey+" || "+this.model.fieldSettings.enableAjax+" || "+this.model.enableAjax+"}} "+this._prefixClass+"arrow{{/if}}{{if "+this.model.fieldSettings.imageClass+"}} "+this._prefixClass+"margin{{else}}{{if "+this.model.fieldSettings.imageUrl+"}} "+this._prefixClass+"margin{{/if}}{{/if}}","data-childid":"{{if "+this.model.fieldSettings.primaryKey+"}}child{{>"+this.model.fieldSettings.primaryKey+"}}{{else "+this.model.fieldSettings.childId+"}}{{>"+this.model.fieldSettings.childId+"}}{{else}}{{/if}}","data-childheadertitle":"{{>"+this.model.fieldSettings.childHeaderTitle+"}}","data-childheaderbackbuttontext":"{{>"+this.model.fieldSettings.childHeaderBackButtonText+"}}","data-preventSelection":"{{>"+this.model.fieldSettings.preventSelection+"}}","data-persistSelection":"{{>"+this.model.fieldSettings.persistSelection+"}}","data-navigateUrl":"{{>"+this.model.fieldSettings.navigateUrl+"}}","data-loadajax":"{{>"+this.model.fieldSettings.enableAjax+"}}","data-href":"{{>"+this.model.fieldSettings.href+"}}{{:~_checkAjaxUrls()}}","data-checked":"{{>"+this.model.fieldSettings.checked+"}}","data-templateid":"{{>"+this.model.fieldSettings.renderTemplate+"}}","data-mouseup":"{{>"+this.model.fieldSettings.mouseUp+"}}","data-mousedown":"{{>"+this.model.fieldSettings.mouseDown+"}}","data-id":"{{>"+this.model.fieldSettings.id+"}}"}),n,i,r;if(this.model.renderTemplate)this._hasDataSource()&&this._template&&(n=this._createCheckBox(),t[0].innerHTML=this._template+"{{if "+this.model.fieldSettings.enableCheckMark+" !== undefined}}{{if "+this.model.fieldSettings.enableCheckMark+".toString() == 'false' ? "+this.model.fieldSettings.enableCheckMark+" : "+this.model.enableCheckMark+"}}"+n[0].outerHTML+"{{/if}}{{else}}{{if "+this.model.enableCheckMark+"}}"+n[0].outerHTML+"{{/if}}{{/if}}");else{i=ej.buildTag("a","",{},{"class":this._prefixClass+"chevron-right_01 "+this._prefixClass+"remove-shadow{{if "+this.model.fieldSettings.imageClass+"}} "+this._prefixClass+"margin{{else}}{{if "+this.model.fieldSettings.imageUrl+"}} "+this._prefixClass+"margin{{/if}}{{/if}}{{if "+this.model.fieldSettings.primaryKey+" || "+this.model.fieldSettings.enableAjax+" || "+this.model.enableAjax+"}} "+this._prefixClass+"fontimage e-icon{{/if}}",href:ej.browserInfo().name=="msie"&&ej.browserInfo().version<9?"":"{{if "+this.model.fieldSettings.navigateUrl+" == '' || "+this.model.fieldSettings.navigateUrl+" == undefined }}{{else}}{{:"+this.model.fieldSettings.navigateUrl+"}}{{/if}}"});ej.browserInfo().name=="msie"&&ej.browserInfo().version<9&&i.removeAttr("href");var u=ej.buildTag("span","{{>"+this.model.fieldSettings.text+"}}",{},{"class":this._prefixClass+"list-text "+this._prefixClass+"rel "+this._prefixClass+"user-select{{if "+this.model.fieldSettings.imageClass+"}} "+this._prefixClass+"text{{else}}{{if "+this.model.fieldSettings.imageUrl+"}} "+this._prefixClass+"text{{/if}}{{/if}}"}),f=ej.buildTag("div","",{},{"class":this._prefixClass+"list-img "+this._prefixClass+"rel "+this._prefixClass+"user-select {{>"+this.model.fieldSettings.imageClass+"}}"}),n=this._createCheckBox();i[0].innerHTML=this.model.renderMode=="windows"&&ej.isMobile()?"{{if !"+this.model.fieldSettings.primaryKey+"}}{{if "+this.model.fieldSettings.enableAjax+" == undefined || "+this.model.fieldSettings.enableAjax+".toString() == 'false'}}{{if !"+this.model.fieldSettings.navigateUrl+"}}{{if "+this.model.fieldSettings.enableCheckMark+" !== undefined}}{{if "+this.model.fieldSettings.enableCheckMark+".toString() == 'false' ? "+this.model.fieldSettings.enableCheckMark+" : "+this.model.enableCheckMark+"}}"+n[0].outerHTML+"{{/if}}{{else}}{{if "+this.model.enableCheckMark+"}}"+n[0].outerHTML+"{{/if}}{{/if}}{{/if}}{{/if}}{{/if}}{{if "+this.model.fieldSettings.imageClass+"}}"+f[0].outerHTML+"{{else}}{{if "+this.model.fieldSettings.imageUrl+"}}{{:~_checkImgUrls()}}{{/if}}{{/if}}"+u[0].outerHTML:"{{if "+this.model.fieldSettings.imageClass+"}}"+f[0].outerHTML+"{{else}}{{if "+this.model.fieldSettings.imageUrl+"}}{{:~_checkImgUrls()}}{{/if}}{{/if}}"+u[0].outerHTML+"{{if !"+this.model.fieldSettings.primaryKey+"}}{{if "+this.model.fieldSettings.enableAjax+" == undefined || "+this.model.fieldSettings.enableAjax+".toString() == 'false'}}{{if !"+this.model.fieldSettings.navigateUrl+"}}{{if "+this.model.fieldSettings.enableCheckMark+" !== undefined}}{{if "+this.model.fieldSettings.enableCheckMark+".toString() == 'false' ? "+this.model.fieldSettings.enableCheckMark+" : "+this.model.enableCheckMark+"}}"+n[0].outerHTML+"{{/if}}{{else}}{{if "+this.model.enableCheckMark+"}}"+n[0].outerHTML+"{{/if}}{{/if}}{{/if}}{{/if}}{{/if}}";t[0].innerHTML="{{if "+this.model.fieldSettings.renderTemplate+" == undefined || "+this.model.fieldSettings.renderTemplate+".toString() == 'false'}}"+i[0].outerHTML+"{{else}}{{:templateId}}{{/if}}"}return this.model.renderMode=="ios7"&&(r=ej.buildTag("div."+this._prefixClass+"list-div"),r[0].innerHTML=t[0].innerHTML,t.empty().append(r)),t},_updateContent:function(n,t){var o=this,r=$(n),u=r.attr("data-childid"),i=this._isInsideNavigation&&n.attr("data-href")?$("body").find($("#"+u)):this.element.find($("#"+u)),e,f;i.length&&(e=this.element.find("#"+u+"_header"),this.model.enableFiltering&&this._initializeFiltering($(i)),f=r.attr("data-childheadertitle")==undefined?n.text():r.attr("data-childheadertitle"),e.hasClass("e-header")&&this._setHeaderVal(u,f,t),i.attr("data-hdr-title",f).attr("data-hdr-bckbtn",t),this._initEJCheckBox(i),this._isInsideNavigation&&n.attr("data-href")&&this._nearestND.model.contentId?$("body").find(".e-lv.subpage.e-childitem")&&$("#"+this._nearestND.model.contentId).empty().append(i.show()):(r.closest(".subpage").hide(),i.show(),this._childContainer=i.find("."+this._prefixClass+"list-container"),this._childContainer.ejScroller({height:this._childContainer.height(),width:0,scrollerSize:20}),this.scrollerObj=this._childContainer.ejScroller("instance"),this._containerHeight=this.element.height()-ej.getDimension(this._lbEle.find("."+this._prefixClass+"lv-inputdiv"),"outerHeight")-ej.getDimension(this._lbEle.find("."+this._prefixClass+"header"),"outerHeight"),this.model.height!==null&&this._childContainer.height()>this._containerHeight&&this._refreshScroller(this._childContainer,!0),this.scrollerObj&&(this.scrollerObj.refresh(),$(this.scrollerObj.element).find(".e-vhandlespace").css("height",$(this.scrollerObj.element).find(".e-vhandlespace").height()-1)),$(this.element.children()[0]).removeClass("e-slideright"),i.addClass("e-slideleft")))},_renderHeader:function(n,t,i,r){var u=ej.buildTag("div","",{},{id:n+"_header","class":"e-header e-box"});return t?(u.append("<span class='e-hicon e-icon e-chevron-left_01'><\/span>"),u.append("<div class='e-btn-text'>"+(r?r:"Back")+"<\/div>")):u.append("<div class='e-htitle'>"+i+"<\/div>"),u},_setHeaderVal:function(n,t,i){this._onBackButtonDelegate=$.proxy(this._onBackButtonClick,this);ej.listenTouchEvent($("#"+n+"_header"),ej.endEvent(),this._onBackButtonDelegate,!1,this);var r=this.model.showHeaderBackButton?this.model.headerBackButtonText?this.model.headerBackButtonText:i:i;$("#"+n+"_header").find(".e-btn-text").text(i?r:"Back");$("#"+n+"_header").find(".e-htitle").text(t)},_onBackButtonClick:function(){this.element.find(".e-slideleft").removeClass("e-slideleft");this.element.children(":visible").hide();$(this.element.children()[0]).show();$(this.element.children()[0]).addClass("e-slideright");this.model.enableFiltering&&this._initializeFiltering($(this.element.children()[0]))},_returnData:function(){var checkedItem=this._currentItem.closest("ul.e-list-hdr").find('.e-chkbox-wrap[aria-checked="true"]').closest("li"),elementId=ej.getAttrVal(this._currentItem,"data-id",this._currentItem.text()),items=this.dataSource().length?typeof this.dataSource()=="string"?eval(this.dataSource()):this.dataSource():this.model.items;return{hasChild:this._currentItem.attr("data-childid")&&this._currentItem.attr("data-childid").length>0?this.element.find("#"+this._currentItem.attr("data-childid")).length?!0:!1:!1,item:this._currentItem,text:this._currentItem.text(),index:this._currentItem.index(),isChecked:ej.isNullOrUndefined(this._eventtrigger)?!1:this._eventtrigger.hasClass("e-chk-image e-icon")||this.model.mouseDown?this._currentItem.find("input."+this._prefixClass+"lv-check").prop("checked")==!0?!1:!0:this._currentItem.find("input."+this._prefixClass+"lv-check").prop("checked")==!0?!0:!1,checkedItems:checkedItem.length?checkedItem:null,checkedItemsText:$(checkedItem).map(function(){return $(this).text()}).get(),itemData:this._generateData(items,elementId),checkedValues:this.checkedIndices()}},_initEJCheckBox:function(n){for(var u,i,r=n.find("li"),f=r.length,t=0;t<f;t++)this.model.enablePersistence||this.model.checkedIndices.length>0?(u=this.model.checkedIndices,i=this._hasValue(u,t)):(i=ej.getAttrVal($(r[t]),"data-checked"),i=i=="true"?!0:!1),$($(r[t]).find(".e-lv-check")).ejCheckBox({checked:i});n.find(".e-lv-checkdiv").removeClass("e-lv-checkdiv");n.find(".e-lv-check").parent().addClass("e-lv-checkdiv")},_triggerStartEvent:function(n){this.model.mouseDown=ej.browserInfo().name=="msie"&&ej.browserInfo().version==8?this._touchStart:ej.getAttrVal(this._currentItem,"data-mousedown",this._touchStart);var t=this._currentItem.find("."+this._prefixClass+"lv-check");$(t).closest(".e-chkbox-wrap").attr("aria-checked",n.isChecked);t.ejCheckBox({checked:n.isChecked});this._trigger("mouseDown",n)},_triggerEndEvent:function(n,t){if(this.model.mouseUp=ej.browserInfo().name=="msie"&&ej.browserInfo().version==8?this._touchEnd:ej.getAttrVal(this._currentItem,"data-mouseup",this._touchEnd),this.selectedItemIndex(this._currentItem.index()),this.model.enablePersistence||this.model.enableCheckMark){var r=this._currentItem.find(".e-chkbox-wrap"),i=this._currentItem.index();r.attr("aria-checked")=="true"||i||$(t.target).parent().hasClass("e-chk-inact")?this._hasValue(this._checkedValues,i)?this._checkedValues.splice(this._checkedValues.indexOf(i),1):this._checkedValues.push(i):this._checkedValues.splice(this._checkedValues.indexOf(i),1)}this.checkedIndices(this._checkedValues);this.model.mouseUp&&this._trigger("mouseUp",n)},_createFilterDiv:function(){return ej.buildTag("div.e-lv-filter",ej.buildTag("input.e-lv-input","",{},{type:"text",placeholder:"search"}))},_emptyFilterTextValue:function(n){n.find(".e-lv-input").val("")},_createListDiv:function(n){this._div=ej.buildTag("div#"+n+"."+this._rootCSS+" subpage e-childitem e-ajaxchild",this.model.showHeader&&this._isInsideNavigation&&!this._nearestND.model.contentId?this._renderHeader(n,!0,this._currentItem.text()):"")},_createCheckBox:function(){return ej.buildTag("input.e-lv-check","",{},{type:"checkbox"})},_toggleCheckboxValue:function(n){n.ejCheckBox({checked:$(n).closest(".e-chkbox-wrap").attr("aria-checked")=="true"?!1:!0})},_setCheckboxValue:function(n,t){n.ejCheckBox({checked:t})},_convertToRelativeUrl:function(n){return n},_setCulture:function(){this._localizedLabels=this._getLocalizedLabels();ej.isNullOrUndefined(this._options)||(ej.isNullOrUndefined(this._options.headerTitle)||(this._localizedLabels.headerTitle=this._options.headerTitle),ej.isNullOrUndefined(this._options.headerBackButtonText)||(this._localizedLabels.headerBackButtonText=this._options.headerBackButtonText));this.model.headerTitle=this._localizedLabels.headerTitle;this.model.headerBackButtonText=this._localizedLabels.headerBackButtonText},_getLocalizedLabels:function(){return ej.getLocalizedConstants(this.sfType,this.model.locale)}});ej.ListView.Locale=ej.ListView.Locale||{};ej.ListView.Locale["default"]=ej.ListView.Locale["en-US"]={headerTitle:"Title",headerBackButtonText:""};$.extend(!0,ej.ListView.prototype,ej.ListViewBase.prototype)}(jQuery,Syncfusion)});
