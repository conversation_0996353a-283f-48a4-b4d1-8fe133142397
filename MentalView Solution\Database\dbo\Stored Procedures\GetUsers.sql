﻿

-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[GetUsers] 
	@TenantId bigint,
	@Filter nvarchar(50) = '', 
	@OrderBy nvarchar(50) = '',
	@SortType nvarchar(5) = '',
	@PageSize int = 10,
	@PageIndex int = 0
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;


    -- Insert statements for procedure here
	SELECT *
	FROM dbo.Users
	WHERE TenantId=@TenantId AND (FullName LIKE '%'+@Filter+'%' OR Username LIKE '%'+@Filter+'%') 
	ORDER BY 
		CASE WHEN @OrderBy='' AND @SortType='' THEN Users.FullName END ASC, 
		CASE WHEN @OrderBy='UserId' AND @SortType='ASC' THEN UserId END ASC,
		CASE WHEN @OrderBy='UserId' AND @SortType='DESC' THEN UserId END DESC,
		CASE WHEN @OrderBy='FullName' AND @SortType='ASC' THEN FullName END ASC,
		CASE WHEN @OrderBy='FullName' AND @SortType='DESC' THEN FullName END DESC,
		CASE WHEN @OrderBy='Username' AND @SortType='ASC' THEN Username END ASC,
		CASE WHEN @OrderBy='Username' AND @SortType='DESC' THEN Username END DESC

		
	OFFSET @PageSize*@PageIndex ROWS
	FETCH NEXT @PageSize ROWS ONLY;

	SELECT COUNT(*) AS TotalCount
	FROM dbo.Users
	WHERE TenantId=@TenantId AND (FullName LIKE '%'+@Filter+'%' OR Username LIKE '%'+@Filter+'%')
	
END