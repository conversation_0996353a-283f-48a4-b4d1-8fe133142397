/*!
*  filename: ej.gauge.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function(n,t,i){var r,f,u;t.widget({ejCircularGauge:"ej.datavisualization.CircularGauge"},{_rootCSS:"e-circulargauge",validTags:["div","span"],_savedPoints:[],_labelRadius:0,_customLblMaxSize:0,defaults:{exportSettings:{mode:"client",type:"png",fileName:"CircularGauge",action:""},locale:null,enableGroupSeparator:!1,value:0,minimum:0,maximum:100,radius:180,width:360,height:360,frame:{frameType:"fullcircle",backgroundImageUrl:null,halfCircleFrameStartAngle:180,halfCircleFrameEndAngle:360},backgroundColor:null,interiorGradient:null,readOnly:!0,enableAnimation:!0,animationSpeed:500,theme:"flatlight",isRadialGradient:!1,enableResize:!1,isResponsive:!1,tooltip:{showLabelTooltip:!1,showCustomLabelTooltip:!1,templateID:null},outerCustomLabelPosition:"bottom",gaugePosition:"center",distanceFromCorner:20,rangeZOrder:"rear",drawTicks:null,drawLabels:null,drawPointers:null,drawRange:null,drawCustomLabel:null,drawIndicators:null,drawPointerCap:null,load:null,doubleClick:"",rightClick:"",renderComplete:null,mouseClick:null,mouseClickMove:null,mouseClickUp:null,legendItemRender:null,legendItemClick:null,tooltipRendering:null,rangeMouseMove:null,scales:null,legend:{visible:!1,toggleVisibility:!0,border:{color:"transparent",width:1},itemPadding:20,shape:"circle",alignment:"center",position:"bottom",itemStyle:{height:9,width:9,border:{color:"transparent",width:1}},opacity:1,fill:null,font:{color:null,fontFamily:"Segoe UI",fontStyle:"Normal",fontWeight:"Regular",size:"12px"},size:{height:null,width:null}},themeProperties:{flatlight:{backgroundColor:"#FFFFFF",scales:{pointerCap:{borderColor:"#424242",backgroundColor:"#424242"},backgroundColor:"#777777",border:{color:"#777777"},pointers:{backgroundColor:"#424242",border:{color:"#424242"}},ticks:{color:"#777777"},labels:{color:"#282828"}}},flatdark:{backgroundColor:"#1F1F1F",scales:{pointerCap:{borderColor:"#686868",backgroundColor:"#686868"},backgroundColor:"#7a7a7a",border:{color:"#7a7a7a"},pointers:{backgroundColor:"#686868",border:{color:"#686868"}},ticks:{color:"#7a7a7a"},labels:{color:"#d3d3d3"}}}}},_defaultScaleValues:function(){return{size:6,pointerCap:{radius:7,borderWidth:3,interiorGradient:null,borderColor:null,backgroundColor:null},showScaleBar:!1,sweepAngle:310,radius:170,startAngle:115,majorIntervalValue:10,minorIntervalValue:2,maximum:null,minimum:null,border:{color:null,width:1.5},backgroundColor:null,direction:"clockwise",showPointers:!0,showRanges:!1,showTicks:!0,showLabels:!0,showIndicators:!1,opacity:1,shadowOffset:0,customLabels:[{value:null,color:null,textAngle:0,positionType:"inner",position:{x:0,y:0},font:{size:"11px",fontFamily:"arial",fontStyle:"bold"}}],pointers:[{distanceFromScale:0,showBackNeedle:!1,backNeedleLength:10,length:150,placement:"near",width:7,opacity:1,value:null,border:{color:null,width:1.5},backgroundColor:null,gradients:null,type:"needle",needleType:"triangle",markerType:"rectangle",imageUrl:"",pointerValueText:{showValue:!1,distance:20,font:{size:"11px",fontFamily:"Arial",fontStyle:"Bold"},color:"#8c8c8c",opacity:1,autoAngle:!1,angle:0}}],ranges:[{legendText:null,distanceFromScale:25,size:5,placement:"near",startValue:null,endValue:null,startWidth:null,endWidth:null,gradients:null,opacity:null,backgroundColor:"#32b3c6",border:{color:"#32b3c6",width:1.5}}],ticks:[{angle:0,distanceFromScale:0,color:null,type:"major",placement:"near",height:16,width:3},{angle:0,distanceFromScale:0,color:null,type:"minor",placement:"near",height:7,width:1}],labels:[{angle:0,autoAngle:!1,opacity:null,font:{size:"11px",fontFamily:"Arial",fontStyle:"Bold"},color:null,distanceFromScale:0,includeFirstValue:!0,placement:"near",type:"major",unitText:"",unitTextPosition:"back"}],indicators:[{height:15,width:15,type:"circle",imageUrl:null,position:{x:0,y:0},stateRanges:[{endValue:0,startValue:0,backgroundColor:null,borderColor:null,text:"",textColor:null,font:null}]}],subGauges:[{height:150,width:150,position:{x:0,y:0}}]}},dataTypes:{scales:"data",isResponsive:"boolean"},observables:["value","minimum","maximum"],_tags:[{tag:"scales",attr:["showScaleBar","pointerCap.radius","pointerCap.borderWidth","pointerCap.interiorGradient","pointerCap.borderColor","pointerCap.backgroundColor","sweepAngle","startAngle","showPointers","showTicks","backgroundColor","scaleRadius","majorIntervalValue","minorIntervalValue","shadowOffset","showRanges","showLabels","showCustomLabels","showIndicators","border.width","border.color",[{tag:"pointers",attr:["distanceFromScale","showBackNeedle","backNeedleLength","backgroundColor","needleType","markerType","border.width","border.color","imageUrl","pointerValueText.showValue","pointerValueText.distance","pointerValueText.font.size","pointerValueText.font.fontFamily","pointerValueText.font.fontStyle","pointerValueText.angle","pointerValueText.autoAngle","pointerValueText.color","pointerValueText.opacity"]},{tag:"labels",attr:["autoAngle","distanceFromScale","includeFirstValue","unitText","unitTextPosition","font.size","font.fontFamily","font.fontStyle"]},{tag:"ticks",attr:["distanceFromScale"]},{tag:"ranges",attr:["legendText","distanceFromScale","startValue","endValue","startWidth","endWidth","backgroundColor","border.color","border.width"]},{tag:"indicators",attr:["imageUrl","position.x","position.y",[{tag:"stateRanges",attr:["endValue","startValue","backgroundColor","borderColor","textColor"]}]]},{tag:"subGauges",attr:["controlID","position.x","position.y"]},{tag:"customLabels",attr:["positionType","textAngle","position.x","position.y","font.size","font.fontFamily","font.fontStyle"]}]]}],value:t.util.valueFunction("value"),minimum:t.util.valueFunction("minimum"),maximum:t.util.valueFunction("maximum"),_setModel:function(t){var i,r,u,f,e;for(i in t)switch(i){case"height":this.model.height=t[i];break;case"width":this.model.width=t[i];break;case"radius":this.model.radius=t[i];break;case"frame":n.extend(this.model.frame,t[i]);break;case"tooltip":n.extend(this.model.tooltip,t[i]);break;case"backgroundColor":this.model.backgroundColor=t[i];break;case"interiorGradient":this.model.interiorGradient=t[i];break;case"readOnly":this.model.readOnly=t[i];break;case"theme":this.model.theme=t[i];break;case"isRadialGradient":this.model.isRadialGradient=t[i];break;case"outerCustomLabelPosition":this.model.outerCustomLabelPosition=t[i];break;case"gaugePosition":this.model.gaugePosition=t[i];break;case"distanceFromCorner":this.model.distanceFromCorner=t[i];break;case"rangeZOrder ":this.model.rangeZOrder=t[i];break;case"value":for(this.value()==""&&this.value(0),r=0;this.model.scales[r]!=null;r++)for(u=0;this.model.scales[r].pointers[u]!=null;u++)this.model.scales[r].pointers[u].value=parseFloat(this.value());break;case"minimum":for(this.minimum()==""&&this.minimum(0),f=0;this.model.scales[f]!=null;f++)this.model.scales[f].minimum=parseInt(this.minimum());break;case"maximum":for(this.maximum()==""&&this.maximum(100),e=0;this.model.scales[e]!=null;e++)this.model.scales[e].maximum=parseInt(this.maximum());break;case"scales":n.extend(!0,this.model.scales,t[i])}this._init()},_destroy:function(){this._unWireEvents();this.element.empty().removeClass("e-circulargauge e-js e-widget")},_init:function(){r=n(".e-circulargauge").length;f=r;this._scaleRedrawn=!1;this._scalesInitialize();this._trigger("load");this._setTheme();this._initialize();this.model.frame.backgroundImageUrl&&this.initialize();this._wireEvents();this._onWindowResize()},_onWindowResize:function(){(this.model.enableResize||this.model.isResponsive)&&(t.isTouchDevice()?this._on(n(window),"orientationchange",this.resizeCanvas):this._on(n(window),"resize",this.resizeCanvas))},_initialize:function(){this._androidAnimation=this.isAndroid()&&t.datavisualization.CircularGauge.animationPolyfill;this._initObject(this);this._drawOuterLayer();this.model.legend.visible&&this.model.legend._legendItemHeight>0&&this.model.legend._legendItemWidth>0&&(this._drawLegend(),this._legendDrawn=!0);this._drawScales();this.model.renderComplete&&this._onRenderComplete()},_drawLegend:function(){var r=this.model.legend,n,p=this.GaugeEl,u=20,c=this.model.legend.position.toLowerCase(),l=this.model.legend.alignment.toLowerCase(),y,f=this.model.legend.border.width,e=this.model.width,o=this.model.height,s,a,h;for(n=this.model.legendActualBounds,c=="top"||c=="bottom"?(n.y=c=="top"?f+u:o-n.Height,n.x=l=="center"?e/2-n.Width/2:l=="near"?f+u:e-n.Width-u,n.x=n.x+n.Width>e?n.x-Math.abs(n.x+n.Width-e):n.x):(n.x=c=="left"?f:e-n.Width,n.y=l=="center"?o/2-n.Height/2:l=="near"?f+u:o-n.Height-u,n.y=n.y+n.Height>o?n.y-Math.abs(n.y+n.Height-o):n.y),this._legendBounds=n,this.contextEl.lineWidth=f,this.contextEl.save(),this.contextEl.strokeStyle=this.model.legend.border.color,this.contextEl.strokeRect(n.x,n.y,n.Width,n.Height),this.contextEl.restore(),this.model._legendCollection=[],s=0;s<this.model.scales.length;s++)for(a=this.model.scales[s],h=0;h<a.ranges.length;h++){var i=a.ranges[h],v;i._visible=t.util.isNullOrUndefined(i._visible)?!0:i._visible;t.util.isNullOrUndefined(i.legendText)||(v=this._getLegendSize(i,10),y={Bounds:{Height:v.Height,Width:v.Width},legendStyle:{font:r.font,opacity:r.opacity,BorderColor:r.itemStyle.border.color,BorderWidth:r.itemStyle.border.width},displayText:i.legendText,fill:t.util.isNullOrUndefined(r.fill)?i.backgroundColor:r.fill,ScaleIndex:s,RangeIndex:h,visible:i._visible},this.model._legendCollection.push(y))}this._drawLegendItem(n)},legendMouseClick:function(n){var i,t,u,f,e,o,s,h,r=this.calMousePosition(n);if(this.model.legend.toggleVisibility)for(i=0;i<this.legendRegion.length;i++)t=this.legendRegion[i],u=t.X,f=t.X+t.Width,e=t.Y,o=t.Y+t.Height,r.X>=u&&r.X<=f&&r.Y>=e&&r.Y<=o&&(s={type:"legendItemClick",cancel:!1,data:t,model:this.model},this._trigger("legendItemClick",s),h=this.model.scales[t.item.ScaleIndex].ranges[t.item.RangeIndex]._visible?!1:!0,this.setRangeVisible(t.item.ScaleIndex,t.item.RangeIndex,h))},calMousePosition:function(t){var e=jQuery.uaMatch(navigator.userAgent),r,u,f,o,i;return r=this.GaugeEl[0],i=r.querySelector("canvas"),o=e.browser.toLowerCase(),u=t.pageX-n(i).offset().left,f=t.pageY-n(i).offset().top,{X:u,Y:f}},rangesMouseMove:function(t){var ot=this,a,v,p,w,b,k,d,g,ft,f,r,nt,y,s,tt,h=this.calMousePosition(t),i,e,o,et,it,rt,ut,u,c,l;if(this.model.legend.toggleVisibility&&this._legendDrawn)for(v=0;v<this.legendRegion.length;v++)if(i=this.legendRegion[v],p=i.X,w=i.X+i.Width,b=i.Y,k=i.Y+i.Height,h.X>=p&&h.X<=w&&h.Y>=b&&h.Y<=k){n(this.GaugeEl[0]).css("cursor","pointer");break}else n(this.GaugeEl[0]).css("cursor","default");for(d=h.X,g=h.Y,ft=[],f=this.model.scales[0].startAngle,r=this.model.scales[0].sweepAngle,nt=f*(Math.PI/180),y=0;y<this._rangeCollectionRegions.length;y++)if(i=this._rangeCollectionRegions[y],e=d-i.centerRadius.centerX,o=g-i.centerRadius.centerY,et=i.Radius.endRadius?i.Radius.endRadius:0,f=f<0?f+360:f,r=r<0?r+360:r,it=r-f,rt=-.5*Math.PI+nt,it<0){if(r=r/360,ut=r?2*Math.PI*(r<0?1+r:r):0,u=(Math.atan2(o,e)-rt-ut)%(2*Math.PI),u<0&&(u=2*Math.PI+u),c=Math.PI*(i.Region.startAngle/180),l=Math.PI*(i.Region.endAngle/180),u<=c&&u>=l&&(s=Math.sqrt(Math.pow(Math.abs(e),2)+Math.pow(Math.abs(o),2)),s<=i.Radius.startRadius&&s>i.Radius.endRadius)){a={type:"rangeMouseMove",cancel:!1,data:i,model:this.model};this._trigger("rangeMouseMove",a);break}}else if(u=Math.atan2(o,e)%(2*Math.PI),u<0&&(u=2*Math.PI+u),i.Region.endAngle<i.Region.startAngle?(c=e>0&&o>0?0:Math.PI*(i.Region.startAngle/180),l=e>0&&o>0?Math.PI*(i.Region.endAngle/180):Math.PI*((360+i.Region.endAngle)/180)):(c=Math.PI*(i.Region.startAngle/180),l=Math.PI*(i.Region.endAngle/180)),u>=c&&u<=l&&(s=Math.sqrt(Math.pow(Math.abs(e),2)+Math.pow(Math.abs(o),2)),s<=i.Radius.startRadius&&s>i.Radius.endRadius)){tt=i;a={type:"rangeMouseMove",cancel:!1,data:i,model:this.model};this._trigger("rangeMouseMove",a);break}return tt},_cgDoubleClick:function(n){this.model.doubleClick!=""&&this._trigger("doubleClick",{data:{event:n}})},_cgRightClick:function(n){this.model.rightClick!=""&&this._trigger("rightClick",{data:{event:n}})},_drawLegendItem:function(i){var s=[],e=[],st,tt,r,nt,pt;for(this.legendRegion=[],this.model.legend._legendTextMaxHeight=0,this.model.legend._legendTextMaxWidth=0,tt=this.model.legend.position.toLowerCase(),r=0;r<this.model._legendCollection.length;r++){var c=this.model.legend.shape.toLowerCase(),y=10,p=10,w=3,a,v,it,rt,ut,u,b,ht,k,ft,ct,et,lt,at,o,vt,ot,f,d,h,l,g,yt=10;u=this.model._legendCollection[r];t.util.isNullOrUndefined(u.displayText)||(st={type:"legendItemRender",cancel:!1,data:u,model:this.model},this._trigger("legendItemRender",st),this.model.scales[u.ScaleIndex].ranges[u.RangeIndex]._visible=t.util.isNullOrUndefined(this.model.scales[u.ScaleIndex].ranges[u.RangeIndex]._visible)?!0:this.model.scales[u.ScaleIndex].ranges[u.RangeIndex]._visible,ht=this.model.scales[u.ScaleIndex].ranges[u.RangeIndex]._visible?u.fill:"#808080",lt=this.model.scales[u.ScaleIndex].ranges[u.RangeIndex]._visible?u.legendStyle.font.color:"#808080",at=this._getFontString(this,u.legendStyle.font),o=this.calcText(u.displayText,i.Width,u.legendStyle.font),this.model.legend._legendTextMaxHeight=Math.max(o.height,this.model.legend._legendTextMaxHeight),this.model.legend._legendTextMaxWidth=Math.max(o.width,this.model.legend._legendTextMaxWidth),vt=this.model.legend.itemStyle.height,ot=this.model.legend.itemStyle.width,f=(vt+ot)/2,d={angle:0,width:f,isFill:!0,isStroke:!0,height:f,lineWidth:this.model.legend.itemStyle.border.width,opacity:u.legendStyle.opacity,strokeStyle:u.legendStyle.BorderColor,fillStyle:ht},y=f>o.height/2?y+f/2:y+o.height/4,p=c=="circle"?p+f/2:p,r==0?(a=p+i.x,v=i.y+y):tt=="top"||tt=="bottom"?(nt=e[r-1].x+e[r-1].width+this.model.legend.itemPadding,nt+f+w+o.width>=i.x+i.Width?(a=p+i.x,v=this.model.legend._legendTextMaxHeight/2>f?e[r-1].y+s[r-1].actualBounds.height/2+f/2+yt:yt+s[r-1].actualBounds.y+s[r-1].actualBounds.height/2+f/2):(a=c.toLowerCase()=="circle"?nt+f/2:nt,v=s[r-1].actualBounds.y)):(ut=e[r-1].height>f?e[r-1].y+this.model.legend.itemPadding:s[r-1].actualBounds.y+f+this.model.legend.itemPadding,pt=e[r-1].height>f?o.height:f,ut+pt-2>i.y+i.Height+2?(v=i.y+y,a=s[r-1].actualBounds.x+s[r-1].actualBounds.width+(this.model.legend._legendTextMaxWidth+w)+10):(a=s[r-1].actualBounds.x,v=ut)),h={startX:a,startY:v},c=c.charAt(0).toUpperCase()+c.slice(1),l=n.extend({},h,!0),l.startX=c.toLowerCase()=="circle"?l.startX-f/2:l.startX,l.startY=l.startY-f/2,s.push({actualBounds:{x:h.startX,y:h.startY,height:f,width:f},item:u}),b={angle:0,width:o.width,isFill:!0,isStroke:!0,height:o.height,textValue:u.displayText,font:at,fillStyle:lt,opacity:u.legendStyle.opacity,strokeStyle:u.legendStyle.BorderColor},it=c.toLowerCase()=="circle"?h.startX+ot/2+w:h.startX+d.width+w,rt=h.startY+o.height/4,e.push({x:it,y:rt,height:o.height/2,width:o.width}),k=l.startX,ct=Math.abs(k+s[r].actualBounds.width-k)+w+Math.abs(e[r].x+e[r].width-e[r].x),f>e[r].height?(ft=l.startY,et=f):(ft=e[r].y-e[r].height,et=e[r].height),c.toLowerCase()=="circle"?(g=this._setPointerDimension(d,this),this._contextOpenPath(g,this),this._setContextRotation(g,this),this.contextEl.arc(h.startX,h.startY,f/2,0,2*Math.PI),this._contextClosePath(g,this)):this["_draw"+c](h,d,this),this._contextOpenPath(b,this),this.contextEl.font=b.font,this.contextEl.translate(it,rt),this.contextEl.fillText(b.textValue,0,0),this._contextClosePath(b,this),this.legendRegion.push({X:k,Y:ft,Width:ct,Height:et,item:u}))}},calcText:function(t,r,u){var h=n(document).find("#measureTex"),f,c=null,l=null,a=null,v=null,o,s,e,y;if(n("#measureTex").css("display","block"),h.length==0?(f=document.createElement("text"),n(f).attr({id:"measureTex"}),document.body.appendChild(f)):f=h[0],typeof t=="string"&&(t.indexOf("<")>-1||t.indexOf(">")>-1)){for(o=t.split(" "),s=0;s<o.length;s++)o[s].indexOf("<br/>")==-1&&(o[s]=o[s].replace(/[<>]/g,"&"));t=o.join(" ")}return f.innerHTML=t,u!=i&&u.size==i&&(e=u,e=e.split(" "),c=e[0],l=e[1],a=e[2],v=e[3]),u!=null&&(f.style.fontSize=u.size>0?u.size+"px":u.size?u.size:l,f.style.fontStyle&&(f.style.fontStyle=u.fontStyle?u.fontStyle:c),f.style.fontFamily=u.fontFamily?u.fontFamily:a,window.navigator.userAgent.indexOf("MSIE 8.0")==-1&&(f.style.fontWeight=u.fontWeight?u.fontWeight:v)),f.style.backgroundColor="white",f.style.position="absolute",f.style.top=-100,f.style.left=0,f.style.visibility="hidden",f.style.whiteSpace="nowrap",r&&(f.style.maxwidth=r+"px"),y={width:f.offsetWidth,height:f.offsetHeight},n("#measureTex").css("display","none"),y},_calculateLegendBounds:function(){var i=this,r=i.model.legend,f=0,a,e=i.model.legend.position.toLowerCase(),o,v,c,u=10,y=i.model.legend.border.width,p=i.model.legend.itemPadding,s,l,h;return s=i.findMaxHeightWidth(),this._gaugeResizeState||t.util.isNullOrUndefined(i.model.legend.size.height)&&t.util.isNullOrUndefined(i.model.legend.size.width)?(h=i.findLegendMax(),l={Height:h.Height,Width:h.Width},i.model.legend.size._height=i.model.legend.size._width=null):parseInt(i.model.legend.size.width)<s.width&&!t.util.isNullOrUndefined(i.model.legend.size.width)||parseInt(i.model.legend.size.height)<s.height&&!t.util.isNullOrUndefined(i.model.legend.size.height)?(h=i.findLegendMax(),l={Height:h.Height,Width:h.Width},i.model.legend.size._height=i.model.legend.size._width=null):(i.model.legend._legendItemHeight=0,i.model.legend._columnWidth=0,i.model.legend._legendItemWidth=0,i.model.legend._rowCount=1,i.model.legend._columnCount=1,i.model.legend._maxWidth=0,i.model.legend._maxHeight=0,i._columnIncreasing=0,i._columnDecreasing=0,n.each(i.model.scales,function(w,b){if(b.showRanges&&(i.scaleIndex=w,b.ranges!=null)){for(i.rangeEl=b.ranges,i.index=0,a=0;a<i.model.scales[i.scaleIndex].ranges.length;a++)t.util.isNullOrUndefined(i.model.scales[i.scaleIndex].ranges[a].legendText)||f++;n.each(b.ranges,function(n,a){i.rangeIndex=n;!t.util.isNullOrUndefined(a.legendText)&&a.legendText.replace(/\s/g,"").length>0&&i.index<f&&(o=i._getLegendSize(a),parseInt(i.model.legend.size.width)?(c=i.index==0?u+y:e=="top"||e=="bottom"?p:u,v=i.index==0?u+y:0,r._legendItemWidth=e=="top"||e=="bottom"?c+o.Width+r._legendItemWidth:c+s.width+r._legendItemWidth,r._legendItemHeight=v+Math.max(o.Height,r._legendItemHeight),i._columnIncreasing++,i._columnDecreasing=Math.max(i._columnDecreasing,i._columnIncreasing),parseInt(i.model.legend.size.width)<=r._legendItemWidth?(i.model.legend._rowCount++,i._columnIncreasing=0,r._legendItemHeight+=e=="top"||e=="bottom"?i.index==f-1?o.Height+u*2:o.Height+u:i.index==f-1?o.Height+u+p:o.Height+p,r._legendItemWidth=e=="top"||e=="bottom"?i.index==f-1?parseInt(i.model.legend.size.width):o.Width+u:s.width+u):i.index==f-1&&i.model.legend._rowCount>1&&(r._legendItemHeight+=u,i.model.legend.size._width=i.model.legend.size.width,parseInt(i.model.legend.size.width)>s.width*(i._columnDecreasing-1)+(i._columnDecreasing-1)*u+u&&(r._legendItemWidth=s.width*(i._columnDecreasing-1)+(i._columnDecreasing-1)*u+u,i.model.legend.size._width=null))):e=="left"||e=="right"?(c=i.index==0?u+y:i.index==f-1?u:0,v=i.index==0?u+y:p,r._legendItemWidth=c+Math.max(s.width,r._legendItemWidth),r._legendItemHeight=v+o.Height+r._legendItemHeight,i.model.legend.size._height=parseInt(i.model.legend.size.height),parseInt(i.model.legend.size.height)<r._legendItemHeight?(i.model.legend._rowCount++,r._legendItemWidth+=i.index==f-1?s.width+u*2:s.width+u,r._legendItemHeight=o.Height+u):f==1&&parseInt(i.model.legend.size.height)>r._legendItemHeight&&(i.model.legend.size._height=null,r._legendItemWidth+=u,r._legendItemHeight+=u)):(h=i.findLegendMax(),l={Height:h.Height,Width:h.Width},i.index=f-1));i.index++})}}),l={Height:r._legendItemHeight,Width:r._legendItemWidth}),l},findLegendMax:function(){var i=this,r;i.model.legend._legendItemHeight=0;i.model.legend._rowWidth=0;i.model.legend._legendItemWidth=0;i.model.legend._rowCount=1;i.model.legend._columnCount=1;i.model.legend._maxWidth=0;i.model.legend._maxHeight=0;var r=i.model.legend,f=0,a,v,y=i.model.legend.position.toLowerCase(),o,h,c,e=20,u=10,p,s=i.model.legend.border.width,l=i.model.legend.itemPadding;return i.model.legend.size._height=i.model.legend.size._width=null,n.each(i.model.scales,function(w,b){if(b.showRanges&&(i.scaleIndex=w,b.ranges!=null)){for(i.rangeEl=b.ranges,i.index=0,a=0;a<i.model.scales[i.scaleIndex].ranges.length;a++)t.util.isNullOrUndefined(i.model.scales[i.scaleIndex].ranges[a].legendText)||f++;n.each(b.ranges,function(n,a){i.rangeIndex=n;t.util.isNullOrUndefined(a.legendText)||(o=i._getLegendSize(a),y=="top"||y=="bottom"?(f!=1?(c=i.index==0?u*2+s:i.index==f-1?u+l:l,h=i.index==0?u:i.index==f-1?u:0):h=c=u*2,r._legendItemHeight=h+Math.max(o.Height,r._legendItemHeight),r._maxHeight=Math.max(o.Height,r._maxHeight),r._legendItemWidth=c+o.Width+r._legendItemWidth,p=i.model.legend._rowCount>1&&i.index==f-1?u:0,i.model.width-(e-s)<=r._legendItemWidth-p?(i.model.legend._rowCount++,r._rowWidth=Math.max(r._rowWidth,r._legendItemWidth-l-o.Width),r._legendItemWidth=i.index==f-1?r._rowWidth<i.model.width-e*2?r._rowWidth:i.model.width-e*2:i.index==0?e+u+s:o.Width+e+u+s,r._legendItemHeight+=r._maxHeight+(u+s)):i.index==f-1&&i.model.legend._rowCount>1&&(r._legendItemWidth=r._rowWidth<i.model.width-e*2?r._rowWidth:i.model.width-e*2)):(f!=1?(h=i.index==0?u+s:i.index==f-1?u+l:l,c=i.index==0?u:i.index==f-1?u*2:0):h=c=u*2,r._legendItemHeight=o.Height+h+r._legendItemHeight,r._legendItemWidth=c+Math.max(o.Width,r._legendItemWidth),r._maxWidth=Math.max(o.Width,r._maxWidth),i.model.height-e<r._legendItemHeight?(i.model.legend._columnCount++,v=i.model.legend._columnCount==2?e:0,r._legendItemHeight=i.index==f-1?i.model.height-e*2:o.Height+e+u+s+v,r._legendItemWidth+=r._maxWidth+u):i.index==f-1&&i.model.legend._columnCount>1&&(r._legendItemHeight=i.model.height-e*2)),i.index++)})}}),{Height:r._legendItemHeight,Width:r._legendItemWidth}},findMaxHeightWidth:function(){var u=this,t=0,i=0,r;return n.each(u.model.scales,function(f,e){n.each(e.ranges,function(n,f){r=u._getLegendSize(f);t=Math.max(r.Height,t);i=Math.max(r.Width,i)})}),{height:t,width:i}},_getLegendSize:function(n){var t,i,r,u,f=this.model.legend.font;return t=(this.model.legend.itemStyle.width+this.model.legend.itemStyle.height)/2,i=this.calcText(n.legendText,null,f),r=Math.max(t,i.height/2),u=t+3+i.width,{Height:r,Width:u}},_scalesInitialize:function(){var t=this;this.model.scales!=null?n.each(this.model.scales,function(i,r){r=t._checkArrayObject(r,i);var u=t._defaultScaleValues();n.extend(u,r);n.extend(r,u)}):this.model.scales=[this._defaultScaleValues()]},_checkArrayObject:function(t,i){var r=this,u;return n.each(t,function(n,t){if(u=typeof n,(u!="string"||u=="string"&&n.indexOf("_")==-1&&n.indexOf("__")==-1)&&typeof t!="function")if(t instanceof Array)r._checkArrayObject(t,n);else if(t!=null&&typeof t=="object"&&!t.setter&&!t.factory&&!t.key){var f=r._defaultScaleValues();r._LoadIndividualDefaultValues(t,f,typeof n=="number"?i:n)}}),t},_LoadIndividualDefaultValues:function(t,i,r){var u=null,e=this,f;return n.each(i,function(n,t){if(r==n){u=t;return}}),u instanceof Array&&(u=u[0]),f=typeof r,n.each(t,function(n,t){t instanceof Array?e._checkArrayObject(t,r):t!=null&&typeof t=="object"&&(f!="string"||f=="string"&&r.indexOf("_")==-1&&r.indexOf("__")==-1)&&e._LoadIndividualDefaultValues(t,t,typeof n=="number"?r:n)}),n.extend(u,t),n.extend(t,u),t},initialize:function(){this._initObject(this);this.model.frame.backgroundImageUrl?this._drawCustomImage(this,this.model.frame.backgroundImageUrl):this.model.scales!=null&&this._drawScales()},_initObject:function(i){var w,v,l,p,s,h,o,it,g,nt,a,e,c,tt,rt,y;for(this._savedPoints=[],this.element.addClass("e-widget"),i.GaugeEl=this.element,i.canvasEl?(i.canvasEl.remove(),i.GaugeEl.empty()):i.canvasEl=n("<canvas><\/canvas>"),w=0,e=0;this.model.scales[e]!=null;e++){for(this.model.scales[e].minimum==null&&(this.model.scales[e].minimum=this.minimum()),this.model.scales[e].maximum==null&&(this.model.scales[e].maximum=this.maximum()),v=0;this.model.scales[e].pointers[v]!=null;v++)this.model.scales[e].pointers[v].value==null&&(this.model.scales[e].pointers[v].value=this.value());for(l=0;this.model.scales[e].customLabels[l]!=null;l++)if(this.model.scales[e].customLabels[l].value!=null&&(w++,i.GaugeEl.find("div").length==0))if(this.model.scales[e].customLabels[l]!=null&&this.model.scales[e].customLabels[l].positionType!=null&&this.model.scales[e].customLabels[l].positionType=="outer")if(i.outerDiv=t.buildTag("div"),i.model.outerCustomLabelPosition=="bottom")i.GaugeEl.append(i.canvasEl),i.GaugeEl.append(i.outerDiv),i.outerDiv.css("text-align","center");else if(i.model.outerCustomLabelPosition!="top"){p=t.buildTag("TABLE");p.css("width","100%");var b=t.buildTag("TR"),k=t.buildTag("TD"),d=t.buildTag("td");i.model.outerCustomLabelPosition=="left"?(k.append(i.outerDiv),d.append(i.canvasEl)):(k.append(i.canvasEl),d.append(i.outerDiv));b.append(k);b.append(d);p.append(b);i.GaugeEl.append(p);i.outerDiv.css({width:this.element.width()-i.model.width})}else i.GaugeEl.append(i.outerDiv),i.GaugeEl.append(i.canvasEl),i.outerDiv.css("text-align","center");else i.GaugeEl.append(i.canvasEl);w==0&&i.GaugeEl.append(i.canvasEl)}if(i.canvasEl.attr("role","presentation"),r==f&&(u=window.innerWidth),i.canvasEl[0].setAttribute("width",i.model.width),i.canvasEl[0].setAttribute("height",i.model.height),i.centerX=i.canvasEl[0].width/2,i.centerY=i.canvasEl[0].height/2,s=20,a=this.model.gaugePosition.toLowerCase(),this.model.legend.visible){if(this.model.legend.font.color=this.model.theme=="flatdark"?t.util.isNullOrUndefined(this.model.legend.font.color)?"#8c8c8c":this.model.legend.font.color:t.util.isNullOrUndefined(this.model.legend.font.color)?"#282828":this.model.legend.font.color,it=this.model.legend.position.toLowerCase(),o=this._calculateLegendBounds(),o.Height=t.util.isNullOrUndefined(this.model.legend.size._height)?o.Height:parseInt(this.model.legend.size.height),o.Width=t.util.isNullOrUndefined(this.model.legend.size._width)?o.Width:parseInt(this.model.legend.size.width),this.model.legendActualBounds=o,o.Height>0&&o.Width>0){switch(it){case"top":g=i.model.height-(o.Height+s);i.centerY=o.Height+s+g/2;h=g/2-s;break;case"bottom":i.centerY=(i.model.height-(o.Height+s))/2;h=i.centerY-s;break;case"left":nt=i.model.width-(o.Width+s);i.centerX=o.Width+s+nt/2;h=nt/2-s*2;break;case"right":i.centerX=(i.model.width-(o.Width+s))/2;h=i.centerX-s*2}for(e=0;e<this.model.scales.length;e++)c=this.model.scales[e],tt=0,this._scaleRedrawn||(c._radius=c.radius),n.each(c.ranges,function(n,t){tt+=t.size}),rt=tt/c.ranges.length,h=c.radius=c._radius<h?c._radius:h,h=Math.abs((h-rt)/(e+1))}}else n.each(this.model.scales,function(n,i){i.radius=t.util.isNullOrUndefined(i._radius)?i.radius:i._radius});if(this._isHalfCircle&&this._isHalfCircle())if(i.model.frame.halfCircleFrameEndAngle-i.model.frame.halfCircleFrameStartAngle>=180){if(i.model.frame.halfCircleFrameStartAngle==0)switch(a){case"center":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.width/2;break;case"topleft":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"topright":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.width-(i.model.radius+i.model.distanceFromCorner);break;case"topcenter":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.width/2;break;case"middleleft":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"middleright":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.width-(i.model.radius+i.model.distanceFromCorner);break;case"bottomleft":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"bottomright":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.width-(i.model.radius+i.model.distanceFromCorner);break;case"bottomcenter":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.width/2}else if(i.model.frame.halfCircleFrameStartAngle==90)switch(a){case"center":i.centerY=i.model.height/2;i.centerX=i.model.width/2+i.model.radius/2;break;case"topleft":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.distanceFromCorner+i.model.radius;break;case"topright":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width-i.model.distanceFromCorner;break;case"topcenter":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width/2+i.model.radius/2;break;case"middleleft":i.centerY=i.model.height/2;i.centerX=i.model.distanceFromCorner+i.model.radius;break;case"middleright":i.centerY=i.model.height/2;i.centerX=i.model.width-i.model.distanceFromCorner;break;case"bottomleft":i.centerY=i.model.height-(i.model.distanceFromCorner+i.model.radius);i.centerX=i.model.distanceFromCorner+i.model.radius;break;case"bottomright":i.centerY=i.model.height-(i.model.distanceFromCorner+i.model.radius);i.centerX=i.model.width-i.model.distanceFromCorner;break;case"bottomcenter":i.centerY=i.model.height-(i.model.distanceFromCorner+i.model.radius);i.centerX=i.model.width/2+i.model.radius/2}else if(i.model.frame.halfCircleFrameStartAngle==180)switch(a){case"center":i.centerY=i.model.height/2+i.model.radius/2;i.centerX=i.model.width/2;break;case"topleft":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.distanceFromCorner+i.model.radius;break;case"topright":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"topcenter":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width/2;break;case"middleleft":i.centerY=i.model.height/2+i.model.radius/2;i.centerX=i.model.distanceFromCorner+i.model.radius;break;case"middleright":i.centerY=i.model.height/2+i.model.radius/2;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"bottomleft":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.distanceFromCorner+i.model.radius;break;case"bottomright":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"bottomcenter":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.width/2}}else if(i.model.frame.halfCircleFrameStartAngle==270&&i.model.frame.halfCircleFrameEndAngle==90)switch(a){case"center":i.centerY=i.model.height/2;i.centerX=i.model.width/2-i.model.radius/2;break;case"topleft":i.centerY=i.model.radius+i.model.distanceFromCorner;i.centerX=i.model.distanceFromCorner;break;case"topright":i.centerY=i.model.radius+i.model.distanceFromCorner;i.centerX=i.model.width-(i.model.radius+i.model.distanceFromCorner);break;case"topcenter":i.centerY=i.model.radius+i.model.distanceFromCorner;i.centerX=i.model.width/2-i.model.radius/2;break;case"middleleft":i.centerY=i.model.height/2;i.centerX=i.model.distanceFromCorner;break;case"middleright":i.centerY=i.model.height/2;i.centerX=i.model.width-(i.model.radius+i.model.distanceFromCorner);break;case"bottomleft":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.distanceFromCorner;break;case"bottomright":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.width-(i.model.radius+i.model.distanceFromCorner);break;case"bottomcenter":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.width/2-i.model.radius/2}else if(i.model.frame.halfCircleFrameEndAngle-i.model.frame.halfCircleFrameStartAngle<=90)if(i.model.frame.halfCircleFrameStartAngle==0)switch(i.model.gaugePosition){case"center":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.width/2-i.model.radius/2;break;case"topleft":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.distanceFromCorner;break;case"topright":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"topcenter":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.width/2-i.model.radius/2;break;case"middleleft":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.distanceFromCorner;break;case"middleright":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"bottomleft":i.centerY=i.model.height-(i.model.distanceFromCorner+i.model.radius);i.centerX=i.model.distanceFromCorner;break;case"bottomright":i.centerY=i.model.height-(i.model.distanceFromCorner+i.model.radius);i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"bottomcenter":i.centerY=i.model.height-(i.model.distanceFromCorner+i.model.radius);i.centerX=i.model.width/2-i.model.radius/2}else if(i.model.frame.halfCircleFrameStartAngle==90)switch(a){case"center":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.width/2+i.model.radius/2;break;case"topleft":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"topright":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.width-i.model.distanceFromCorner;break;case"topcenter":i.centerY=i.model.distanceFromCorner;i.centerX=i.model.width/2+i.model.radius/2;break;case"middleleft":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"middleright":i.centerY=i.model.height/2-i.model.radius/2;i.centerX=i.model.width-i.model.distanceFromCorner;break;case"bottomleft":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"bottomright":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.width-i.model.distanceFromCorner;break;case"bottomcenter":i.centerY=i.model.height-(i.model.radius+i.model.distanceFromCorner);i.centerX=i.model.width/2+i.model.radius/2}else if(i.model.frame.halfCircleFrameStartAngle==180)switch(i.model.gaugePosition){case"center":i.centerY=i.model.height/2+i.model.radius/2;i.centerX=i.model.width/2+i.model.radius/2;break;case"topleft":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"topright":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width-i.model.distanceFromCorner;break;case"topcenter":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width/2+i.model.radius/2;break;case"middleleft":i.centerY=i.model.height/2+i.model.radius/2;i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"middleright":i.centerY=i.model.height/2+i.model.radius/2;i.centerX=i.model.width-i.model.distanceFromCorner;break;case"bottomleft":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.radius+i.model.distanceFromCorner;break;case"bottomright":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.width-i.model.distanceFromCorner;break;case"bottomcenter":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.width/2+i.model.radius/2}else if(i.model.frame.halfCircleFrameStartAngle==270)switch(i.model.gaugePosition){case"center":i.centerY=i.model.radius/2+i.model.height/2;i.centerX=i.model.width/2-i.model.radius/2;break;case"topleft":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.distanceFromCorner;break;case"topright":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"topcenter":i.centerY=i.model.distanceFromCorner+i.model.radius;i.centerX=i.model.width/2-i.model.radius/2;break;case"middleleft":i.centerY=i.model.height/2+i.model.radius/2;i.centerX=i.model.distanceFromCorner;break;case"middleright":i.centerY=i.model.radius/2+i.model.height/2;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"bottomleft":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.distanceFromCorner;break;case"bottomright":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.width-(i.model.distanceFromCorner+i.model.radius);break;case"bottomcenter":i.centerY=i.model.height-i.model.distanceFromCorner;i.centerX=i.model.width/2-i.model.radius/2}(y=i.canvasEl[0],typeof G_vmlCanvasManager!="undefined"&&(y=window.G_vmlCanvasManager.initElement(y)),y&&y.getContext)&&(i.contextEl=i.canvasEl[0].getContext("2d"))},_browserInfo:function(){var n={},t=[],i={webkit:/(chrome)[ \/]([\w.]+)/i,safari:/(webkit)[ \/]([\w.]+)/i,msie:/(msie) ([\w.]+)/i,opera:/(opera)(?:.*version|)[ \/]([\w.]+)/i,mozilla:/(mozilla)(?:.*? rv:([\w.]+)|)/i};for(var r in i)if(i.hasOwnProperty(r)&&(t=navigator.userAgent.match(i[r]),t)){n.name=t[1].toLowerCase();n.version=t[2];!navigator.userAgent.match(/Trident\/7\./)||(n.name="msie");break}return n.isMSPointerEnabled=n.name=="msie"&&n.version>9&&window.navigator.msPointerEnabled,n.pointerEnabled=window.navigator.pointerEnabled,n},_wireEvents:function(){var f=jQuery.uaMatch(navigator.userAgent),r=this._browserInfo(),i=r.isMSPointerEnabled,t=r.pointerEnabled,u;this.startEv=i?t?"pointerdown":"MSPointerDown":"touchstart mousedown";this.endEv=i?t?"pointerup":"MSPointerUp":"touchend mouseup";this.moveEv=i?t?"pointermove":"MSPointerMove":"touchmove mousemove";this.leaveEv=i?t?"pointerleave":"MSPointerOut":"touchleave mouseleave";this.scrollEv=f.browser.toLowerCase()=="mozilla"?t?"mousewheel":"DOMMouseScroll":"mousewheel";this.model.browserInfo=r;u=this.model.readOnly?"pan-y pan-x":"none";n(this.element).css("touch-action",u);this.onMouseMoveHandler=n.proxy(this._onMouseMove,this);this.onMouseUpHandler=n.proxy(this._onMouseUp,this);this.onMouseDownHandler=n.proxy(this._onMouseDown,this);this.onHoverOCustomLabel=n.proxy(this._onHoverOCustomLabel,this);this.onLeaveOCustomLabel=n.proxy(this._onLeaveOCustomLabel,this);this.model.legend.visible&&this._on(n(this.canvasEl),"click",this.legendMouseClick);this._on(n(this.canvasEl),"mousemove",this.rangesMouseMove);this._on(n(this.canvasEl),this.startEv,this.rangeMouseDown);this._on(n(this.canvasEl),this.endEv,this.rangeMouseUp);this._on(n(this.canvasEl),"contextmenu",this._cgRightClick);(this.model.tooltip.showCustomLabelTooltip||this.model.tooltip.showLabelTooltip)&&(n(this.canvasEl).bind(this.moveEv,this.onMouseMoveHandler),n(this.canvasEl).bind(this.scrollEv,this.onMouseMoveHandler),n(this.canvasEl).bind(this.startEv,this.onMouseDownHandler),n(this.canvasEl).bind(this.endEv,this.onLeaveOCustomLabel),n(this.canvasEl).bind(this.leaveEv,this.onLeaveOCustomLabel));this.model.readOnly||n(this.canvasEl).bind(this.startEv,this.onMouseDownHandler);this.model.tooltip.showCustomLabelTooltip&&(n("."+this._id+"outercustomlbl").bind("mouseenter",this.onHoverOCustomLabel),n("."+this._id+"outercustomlbl").bind("this.leaveEv",this.onLeaveOCustomLabel))},_unWireEvents:function(){this._off(n(this.canvasEl),this.startEv,this.rangeMouseDown);this._off(n(this.canvasEl),this.endEv,this.rangeMouseUp);this._off(n(this.canvasEl),"contextmenu",this._cgRightClick);n(this.canvasEl).unbind(this.startEv,this.onMouseDownHandler)},rangeMouseDown:function(){t.isTouchDevice()&&this.model.rightClick!=""&&(this._longPressTimer=new Date)},rangeMouseUp:function(n){var i=new Date;this._doubleTapTimer!=null&&i-this._doubleTapTimer<300&&this._cgDoubleClick(n);this._doubleTapTimer=i;t.isTouchDevice()&&this.model.rightClick!=""&&i-this._longPressTimer>1500&&this._cgRightClick()},_onHoverOCustomLabel:function(n){(n.currentTarget.innerHTML!=null||n.currentTarget.innerHTML!="")&&this._showTooltip(n,n.currentTarget.innerHTML)},_onLeaveOCustomLabel:function(t){this.isTouch(t)?(this._performTooltip(t),window.clearTimeout(this.model.timer),this.model.timer=setTimeout(function(){n(".tooltipDiv").fadeOut(500)},1200)):this._hideTooltip()},isTouch:function(n){var t=n.originalEvent?n.originalEvent:n;return t.pointerType=="touch"||t.pointerType==2||t.type.indexOf("touch")>-1?!0:!1},_showTooltip:function(t,i){var s=this.model.locale,o=s&&this.model.enableGroupSeparator?i.toLocaleString(s):i.toString(),r=n(".tooltipDiv"),h,e;r.length==0&&(r=n("<div class='tooltipDiv' style='pointer-events:none; position: absolute; z-index: 105; display: block;'><\/div>"),n(document.body).append(r));this.model.tooltip.templateID!=""&&this.model.tooltip.templateID!=null?(h=n("#"+this.model.tooltip.templateID).clone(),n(".tooltipDiv")[0].innerHTML="",n(h).css("display","block").appendTo(r),n(r).css({"pointer-events":"none","background-color":this.model.backgroundColor,border:"1px solid #bbbcbb","border-radius":"3px",color:"#565656"}),r.html(r.html().replace("#label#",o))):(n(r).html(o),n(r).css({"pointer-events":"none","background-color":"white",border:"2px solid #bbbcbb",position:"absolute",padding:"10px 20px","margin-top":"5px","text-align":"left",font:"12px Segoe UI","font-stretch":"condensed",display:"inline-block","border-radius":"3px",color:"#565656",width:"auto"}));var c=10,u=t.pageX+c,f=t.pageY+c;u=u+n(r).width()<this.model.width?u:u-n(r).width();f=f+n(r).height()<this.model.height?f:f-n(r).height();n(r).css("left",u);n(r).css("top",f);n(".tooltipDiv").show();e={type:"tooltipRendering",cancel:!1,text:o,model:this.model,location:{x:u,y:f},target:r,event:t};this._trigger("tooltipRendering",e);e.cancel||e.text==o||(r[0].innerText=e.text)},_hideTooltip:function(){n(".tooltipDiv").remove()},_onMouseDown:function(t){this._blockDefaultActions(t);this._mouseDown=!0;var s=this.isTouch(t)?10:0,o=t.originalEvent.touches?t.originalEvent.touches[0]:t,u={x:this.centerX,y:this.centerY},f={x:o.pageX-n(this.canvasEl).offset().left,y:o.pageY-n(this.canvasEl).offset().top},h=180*this._getCirucumferenceAngle(u,f)/Math.PI,r,e,i=this;this.model.readOnly||n.each(this.model.scales,function(t,o){i.scaleIndex=t;o.pointers!=null&&(i.pointerEl=o.pointers,r=o.radius,e=o.ticks[0].height,n.each(o.pointers,function(t,c){var l;i._isHalfCircle()&&(o.showBackNeedle=!1);l=i._getAngle(c.value);l>360&&(l=l-360);var v=l+c.width,y=l-c.width,a=Math.sqrt((f.x-u.x)*(f.x-u.x)+(f.y-u.y)*(f.y-u.y)),p=a<(c.placement=="far"?r+c.width+c.distanceFromScale:c.placement=="center"?r-c.distanceFromScale:r-15-e-c.distanceFromScale)&&a>(c.placement=="far"?r+c.distanceFromScale:c.placement=="center"?r-c.width-c.distanceFromScale:r-c.width-15-e-c.distanceFromScale),w=c.type=="needle"?a<=c.length:p;i._isBetween(y,v,h,s)&&w&&(i._onMouseClick(l,o.value),i.activeElement=c,n(document).bind(i.moveEv,i.onMouseMoveHandler),n(document).bind(i.endEv,i.onMouseUpHandler))}))})},_onMouseUp:function(){this._mouseDown=!1;n(document).unbind(self.moveEv,self.onMouseMoveHandler);n(document).unbind(self.endEv,self.onMouseUpHandler);this.activeElement&&this._onMouseClickUp(this._getAngle(this.activeElement.value),this.activeElement.value);this.activeElement=null},_mousePosition:function(n){if(!t.util.isNullOrUndefined(n.pageX)&&n.pageX>0)return{x:n.pageX,y:n.pageY};if(n.originalEvent&&!t.util.isNullOrUndefined(n.originalEvent.pageX)&&n.originalEvent.pageX>0)return{x:n.originalEvent.pageX,y:n.originalEvent.pageY};if(n.originalEvent&&n.originalEvent.changedTouches!=i){if(!t.util.isNullOrUndefined(n.originalEvent.changedTouches[0].pageX)&&n.originalEvent.changedTouches[0].pageX>0)return{x:n.originalEvent.changedTouches[0].pageX,y:n.originalEvent.changedTouches[0].pageY}}else return{x:0,y:0}},_calTouchPosition:function(n){var i=jQuery.uaMatch(navigator.userAgent),t=this._mousePosition(n);n.pageX=t.x;n.pageY=t.y},getEvent:function(n){return n.targetTouches&&n.targetTouches[0]?n.targetTouches[0]:n},_onMouseMove:function(i){if(this._mouseDown&&!t.isNullOrUndefined(this.activeElement)){this._blockDefaultActions(i);var f={x:this.centerX,y:this.centerY},u=i.originalEvent.touches?i.originalEvent.touches[0]:i,e={x:u.pageX-n(this.canvasEl).offset().left,y:u.pageY-n(this.canvasEl).offset().top},r=180*this._getCirucumferenceAngle(f,e)/Math.PI;r<this.scaleEl[this.scaleIndex].startAngle&&!this._isHalfCircle()&&(r=r+360);this._onMouseClickMove(this._getAngle(this.activeElement.value),this.activeElement.value);this._getValue(r)<=this.scaleEl[this.scaleIndex].maximum&&(this.activeElement.value=this.scaleEl[this.scaleIndex].direction=="clockwise"?this._getValue(r):this.scaleEl[this.scaleIndex].maximum-this._getValue(r));this.contextEl.putImageData?this._reDrawPointer():this._init()}else(this.model.tooltip.showCustomLabelTooltip||this.model.tooltip.showLabelTooltip)&&!this.isTouch(i)&&this._performTooltip(i)},_performTooltip:function(t){for(var r,u=!1,f=10,o=this.isTouch(t),i=0;this._savedPoints[i]!=null;i++)if(o){var c=this._calTouchPosition(t),e=this.getEvent(t),s=e.pageX,h=e.pageY,r={X:s-n(this.canvasEl).offset().left,Y:h-n(this.canvasEl).offset().top};r.X>this._savedPoints[i].startX-f&&r.X<this._savedPoints[i].startX+this._savedPoints[i].width+f&&r.Y>this._savedPoints[i].startY-f&&r.Y<this._savedPoints[i].startY+this._savedPoints[i].height+f?(this._showTooltip(t,this._savedPoints[i].value),u=!0):u==!1&&this._hideTooltip()}else r={X:t.pageX-n(this.canvasEl).offset().left,Y:t.pageY-n(this.canvasEl).offset().top},r.X>this._savedPoints[i].startX&&r.X<this._savedPoints[i].startX+this._savedPoints[i].width&&r.Y>this._savedPoints[i].startY&&r.Y<this._savedPoints[i].startY+this._savedPoints[i].height?(this._showTooltip(t,this._savedPoints[i].value),u=!0):u==!1&&this._hideTooltip()},_isHalfCircle:function(){return this.model.frame.frameType.toLowerCase()=="halfcircle"?!0:!1},_calFontLength:function(t){var i=this.model.scales[0].minimum+this.model.scales[0].labels[0].unitText,r=this.model.scales[0].maximum+this.model.scales[0].labels[0].unitText,u=r.length>i.length?r:i,f=n('<span id="test"><\/span>').css({font:t,display:"none",whiteSpace:"nowrap"}).appendTo(n("body")).text(u).width();return n("#test").remove(),f/2},_getHalfCircleYPosition:function(){return this._getYCordinate(this.centerY,0,(this.model.frame.halfCircleFrameStartAngle+this.model.frame.halfCircleFrameEndAngle)/2)},_getHalfCircleXPosition:function(){return this._getXCordinate(this.centerX,0,(this.model.frame.halfCircleFrameStartAngle+this.model.frame.halfCircleFrameEndAngle)/2)},_getXCordinate:function(n,t,i){return n+t*Math.cos(Math.PI*(i/180))},_getYCordinate:function(n,t,i){return n+t*Math.sin(Math.PI*(i/180))},_getAngle:function(n){var t;return t=n>=this.scaleEl[this.scaleIndex].minimum&&n<=this.scaleEl[this.scaleIndex].maximum?this.scaleEl[this.scaleIndex].direction=="clockwise"?n-this.scaleEl[this.scaleIndex].minimum:this.scaleEl[this.scaleIndex].maximum-n:this.scaleEl[this.scaleIndex].direction=="clockwise"?n<=this.scaleEl[this.scaleIndex].minimum?this.scaleEl[this.scaleIndex].minimum:this.scaleEl[this.scaleIndex].maximum:n<=this.scaleEl[this.scaleIndex].minimum?this.scaleEl[this.scaleIndex].maximum:this.scaleEl[this.scaleIndex].minimum,t*(this.scaleEl[this.scaleIndex].sweepAngle/(this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum))+this.scaleEl[this.scaleIndex].startAngle},_subtractDecimal:function(n,t){var r=n.toString(),u=t.toString(),f,e,i,o;return f=r.indexOf(".")>-1?r.length-r.indexOf(".")-1:0,e=u.indexOf(".")>-1?u.length-u.indexOf(".")-1:0,i=f>e?f:e,o=(n*Math.pow(10,i)-t*Math.pow(10,i))/Math.pow(10,i),o},_getCirucumferenceAngle:function(n,t){return t.x>n.x?t.y>n.y?this._tangent(n,t):t.y==n.y?0:2*Math.PI+this._tangent(n,t):t.x==n.x?t.y==n.y?0:t.y>n.y?Math.PI/2:1.5*Math.PI:t.y==n.y?Math.PI:t.y>n.y?Math.PI+this._tangent(n,t):Math.PI+this._tangent(n,t)},_calcDistanceFactor:function(n,t,i){var r;return r=n>240&&n<=300||n>60&&n<=120?0:n>330&&n<=360||n>=0&&n<=30?-i*.5:n>30&&n<=60||n>300&&n<=330?-i/2:n>150&&n<=210?i*.5:i/2,t=="far"?-r:r},_tangent:function(n,t){var i=(t.y-n.y)/(t.x-n.x);return Math.atan(i)},_getValue:function(n){return this.scaleEl[this.scaleIndex].direction=="counterclockwise"?(n-this.scaleEl[this.scaleIndex].startAngle)/this.scaleEl[this.scaleIndex].sweepAngle*(this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum):(n-this.scaleEl[this.scaleIndex].startAngle)/this.scaleEl[this.scaleIndex].sweepAngle*(this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum)+this.scaleEl[this.scaleIndex].minimum},_drawScales:function(){var r=this,u,i,f,t;if(this.scaleEl=this.model.scales,n.each(this.model.scales,function(n,t){r.scaleIndex=n;t.showScaleBar&&r._setScaleCordinates(t)}),this.model.rangeZOrder=="rear"?(this._setRanges(),this._setTicks()):(this._setTicks(),this._setRanges()),this._setLabels(),this._subGauge(),this._setCustomLabel(),this._setPointers(),this.contextEl.putImageData||(this.model.enableAnimation=!1),this.model.animationSpeed!=null&&this.model.animationSpeed>0&&(u=this.model.animationSpeed/25,u>=0&&(i=navigator.userAgent.toLowerCase(),f=i.indexOf("msie")!=-1?parseInt(i.split("msie")[1]):0,this.model.enableAnimation&&f!=9))){for(t=0;t<this.model.scales[0].pointers.length;t++)this.pointerValue=[],this.currentValue=[],this.pointerValue[t]=null,this.currentValue[t]=null,this.updatePointerOnAnimation=!1;this.dt=(new Date).getTime();this._onAnimate(this)}this._setIndicators()},_setTicks:function(){var t=this;n.each(this.model.scales,function(i,r){r.showTicks&&(t.scaleIndex=i,r.ticks!=null&&(t.tickEl=r.ticks,n.each(r.ticks,function(n,i){t.tickIndex=n;t._setTicksCordinates(n,i)})))})},_setLabels:function(){var t=this;n.each(this.model.scales,function(i,r){r.showLabels&&(t.scaleIndex=i,r.labels!=null&&(t.labelEl=r.labels,n.each(r.labels,function(n,i){t.labelIndex=n;t._setLabelCoridnates(n,i)})))})},_setIndicators:function(){var t=this;n.each(this.model.scales,function(i,r){r.showIndicators&&(t.scaleIndex=i,r.indicators!=null&&(t.indicatorEl=r.indicators,n.each(r.indicators,function(n,i){t.indicatorIndex=n;t._drawIndicator(n,i)})))})},_setPointers:function(){var t=this;n.each(this.model.scales,function(i,r){r.showPointers&&(t.scaleIndex=i,r.pointers!=null&&(t.pointerEl=r.pointers,n.each(r.pointers,function(n,i){t._isHalfCircle()&&(r.showBackNeedle=!1);t.pointerIndex=n;t._drawPointers(n,i)})))})},_onAnimate:function(n){if(!this._androidAnimation&&!t.util.isNullOrUndefined(n.model)){for(var o=n,i=o,l=i.model.animationSpeed/25,u,f,s,h,c,a=n.model.scales[0].pointers.length,e=[],r=0;r<a;r++)e[r]=!0,i.model.scales[0].pointers[r].value!=i.model.scales[0].pointers[r]._value&&(i.model.scales[0].pointers[r]._value=i.model.scales[0].pointers[r].value),i.pointerValue[r]=t.util.isNullOrUndefined(i.pointerValue[r])?i.model.scales[0].pointers[r]._value:i.pointerValue[r],i.currentValue[r]=t.util.isNullOrUndefined(i.currentValue[r])?i.model.scales[0].minimum:i.currentValue[r],u=i.pointerValue[r],s=(new Date).getTime(),h=s-i.dt,h>l&&u>i.currentValue[r]?(i.currentValue[r]=i.currentValue[r]+(i.model.scales[0].maximum-i.model.scales[0].minimum)/100,i.updatePointerOnAnimation=!0,u>i.currentValue[r]?i.setPointerValue(0,r,i.currentValue[r]):i.setPointerValue(0,r,u),i.dt=s-h%l):i.currentValue>=u&&(e[r]=!1);for(f=0;f<e.length;f++)if(c=e[f],c)break;c&&requestAnimationFrame(function(){o._onAnimate(o)})}},_pointInterval:function(n,t,i,r){this.timer=setTimeout(function(){t>n?(n=n+(r.model.scales[0].maximum-r.model.scales[0].minimum)/100,t>n?r.setPointerValue(0,0,n):r.setPointerValue(0,0,t)):n>=t&&window.clearInterval(r.timer);r._pointInterval(n,t,i,r)},i)},_setRanges:function(){var t=this;this._rangeCollectionRegions=[];n.each(this.model.scales,function(i,r){r.showRanges&&(t.scaleIndex=i,r.ranges!=null&&(t.rangeEl=r.ranges,n.each(r.ranges,function(n,i){t.rangeIndex=n;(i._visible||!t.model.legend.visible)&&t._setRangeCordinates(n,i)})))})},_setCustomLabel:function(){var t=this,i;n.each(this.model.scales,function(r,u){t.scaleIndex=r;u.customLabels!=null&&(t.customLabelEl=u.customLabels,n.each(u.customLabels,function(n,r){t.customLabelIndex=n;i=t.model.scales[t.scaleIndex].customLabels[t.customLabelIndex];i.value!=null&&(i.positionType=="outer"?t._setOuterCustomLabels(n,r):t._setCustomLabelCordinates(n,r))}))})},_subGauge:function(){var t=this;n.each(this.model.scales,function(i,r){t.scaleIndex=i;r.subGauges!=null&&(t.subGaugeEl=r.subGauges,n.each(r.subGauges,function(n,r){t.subGaugeIndex=i;t._setSubGauge(n,r)}))})},_setOuterCustomLabels:function(n,i){var r,u;this._customLblMaxSize=this._customLblMaxSize<parseFloat(i.font.size.match(/\d+/)[0])?parseFloat(i.font.size.match(/\d+/)[0]):this._customLblMaxSize;r=t.buildTag("div."+this._id+"outercustomlbl");r.text(this.model.scales[this.scaleIndex].customLabels[n].value);u=this.model.outerCustomLabelPosition=="right"||this.model.outerCustomLabelPosition=="left"?"left":"center";this.outerDiv.append(r);this.outerDiv.append("<\/br>");u=="center"?r.css({display:"inline-block",margin:"0 auto","max-width":this.model.width}):r.css({display:"inline-block","max-width":this.element.width()-this.model.width>10?this.element.width()-this.model.width:10});r.css({color:i.color!=null?i.color:"black",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap","font-size":i.font!=null&&i.font.size!=null?i.font.size:"12px","font-family":i.font!=null&&i.font.fontFamily!=null?i.font.fontFamily:"Arial","font-weight":i.font!=null&&i.font.fontStyle!=null?i.font.fontStyle:"Normal","text-align":u})},_setScaleCordinates:function(n){if(this.region={centerX:this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,centerY:this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,startAngle:n.startAngle,endAngle:n.startAngle+n.sweepAngle,startRadius:this.scaleEl[this.scaleIndex].radius-this.scaleEl[this.scaleIndex].size/2,endRadius:this.scaleEl[this.scaleIndex].radius+this.scaleEl[this.scaleIndex].size/2},this.style={radius:n.radius-n.size/2,strokeStyle:n.border.color=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.border.color),lineWidth:n.border.width,size:n.size,isFill:!0,opacity:isNaN(n.opacity)?1:n.opacity,isStroke:!0,shadowOffset:n.shadowOffset,fillStyle:n.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.backgroundColor),counterClockwise:n.direction=="clockwise"?!1:!0},n.maximum<n.minimum){var i=n.maximum;n.maximum=n.minimum;n.minimum=i}n.maximum==n.minimum&&(n.maximum=n.maximum+1);this.maximum(n.maximum);this.minimum(n.minimum);this._notifyArrayChange&&(this._notifyArrayChange("scales["+this.scaleIndex+"]maximum",n.maximum),this._notifyArrayChange("scales["+this.scaleIndex+"]minimum",n.minimum));this._drawScaleBar(this.region,this.style);this.contextEl.getImageData&&(this.scaleImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height))},_drawOuterLayer:function(){var n,i,r;this._isHalfCircle()&&(i={x:this.centerX,y:this.centerY},r={x:this.centerX+this.model.radius,y:this.centerY/2});n=this.model.isRadialGradient?this.contextEl.createRadialGradient(this.canvasEl[0].width/2,this.canvasEl[0].height/2,0,this.canvasEl[0].width/2,this.canvasEl[0].height/2,this.model.radius):this.contextEl.createLinearGradient(0,0,0,this.canvasEl[0].height);t.isNullOrUndefined(this.model.interiorGradient)||this._setGradientColor(this,n,this.model.interiorGradient.colorInfo);this.frameOuterLocation={centerX:this.centerX,hcCenterX:this.centerX,hcCenterY:this.centerY,centerY:this.centerY,startAngle:this._isHalfCircle()?Math.PI*(this.model.frame.halfCircleFrameStartAngle/180):0,endAngle:this._isHalfCircle()?Math.PI*(this.model.frame.halfCircleFrameEndAngle/180):2*Math.PI};this._isHalfCircle()&&(this.frameInnerLocation={centerX:this.centerX,hcCenterX:this._getXCordinate(this.centerX,0,(this.model.frame.halfCircleFrameStartAngle+this.model.frame.halfCircleFrameEndAngle)/2),hcCenterY:this._getYCordinate(this.centerX,0,(this.model.frame.halfCircleFrameStartAngle+this.model.frame.halfCircleFrameEndAngle)/2),centerY:this.centerY,startAngle:Math.PI*(this.model.frame.halfCircleFrameStartAngle/180),endAngle:Math.PI*(this.model.frame.halfCircleFrameEndAngle/180)});this.frameInnerStyle={radius:this.model.radius,isStroke:!1,isFill:!0,fillStyle:this.model.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(null,this.model.backgroundColor),counterClockwise:!1};this.model.frame.frameType.toLowerCase()=="fullcircle"?this._drawCircleFrame(this.frameOuterLocation,this.frameInnerStyle):this.model.frame.frameType.toLowerCase()=="halfcircle"&&this._drawHalfCircle(this.frameInnerLocation,this.frameInnerStyle);this.contextEl.getImageData&&(this.outerImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height))},_setTicksCordinates:function(n,i){var o,s,h,c,e,u,r,f;for(i.type=="major"?(e=this.scaleEl[this.scaleIndex].majorIntervalValue,this.majorTickHeight=i.height):e=this.scaleEl[this.scaleIndex].minorIntervalValue,i.placement=="far"&&(u=this.scaleEl[this.scaleIndex].radius+this.scaleEl[this.scaleIndex].size/2+this.scaleEl[this.scaleIndex].border.width/2+i.distanceFromScale),i.placement=="center"&&(u=this.scaleEl[this.scaleIndex].radius-i.height/2-this.scaleEl[this.scaleIndex].border.width/2-i.distanceFromScale),i.placement=="near"&&(u=this.scaleEl[this.scaleIndex].radius-this.scaleEl[this.scaleIndex].size/2-this.scaleEl[this.scaleIndex].border.width/2-i.distanceFromScale),h=i.placement=="near"?-i.height:i.height,f=this.scaleEl[this.scaleIndex].maximum;f>=this.scaleEl[this.scaleIndex].minimum;f-=e)(e==this.scaleEl[this.scaleIndex].minorIntervalValue&&f%this.scaleEl[this.scaleIndex].majorIntervalValue!=0||e==this.scaleEl[this.scaleIndex].majorIntervalValue)&&(f==this.scaleEl[this.scaleIndex].minimum&&(c=!0),r=this._getAngle(f),r=r>360?r-360:r,o=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,u,r),s=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,u,r),this.region={startX:o,startY:s},this.style={angle:i.angle+r,isStroke:!0,isFill:!1,lineHeight:h,lineWidth:i.width,strokeStyle:i.color=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.color)},this.model.drawTicks&&this._onDrawTicks(r,f),this._drawTickMark(this.region,this.style));c||(r=this._getAngle(this.scaleEl[this.scaleIndex].minimum),r=r>360?r-360:r,o=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,u,r),s=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,u,r),this.region={startX:o,startY:s},this.style={angle:i.angle+r,isStroke:!0,isFill:!1,lineHeight:h,lineWidth:i.width,strokeStyle:i.color=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.color)});this.contextEl.getImageData&&(this.tickImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height))},_setLabelCoridnates:function(n,i){var h,e,o,c,l,s,f,u,r;for(s=i.type=="major"?this.scaleEl[this.scaleIndex].majorIntervalValue:this.scaleEl[this.scaleIndex].minorIntervalValue,l=i.type=="major"?this.majorIntervalAngle=this.scaleEl[this.scaleIndex].sweepAngle/((this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum)/s):this.scaleEl[this.scaleIndex].sweepAngle/((this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum)/s),i.placement=="far"&&(f=this.scaleEl[this.scaleIndex].radius+this.scaleEl[this.scaleIndex].size/2+this.majorTickHeight+i.distanceFromScale),i.placement=="center"&&(f=this.scaleEl[this.scaleIndex].radius-10-i.distanceFromScale),i.placement=="near"&&(f=this.scaleEl[this.scaleIndex].radius-this.scaleEl[this.scaleIndex].size/2-10-this.majorTickHeight-i.distanceFromScale),this._labelRadius=f,u=this.scaleEl[this.scaleIndex].maximum;u>=this.scaleEl[this.scaleIndex].minimum;u=this._subtractDecimal(u,s))if(u==this.scaleEl[this.scaleIndex].minimum&&(h=!0),s==this.scaleEl[this.scaleIndex].minorIntervalValue&&u%this.scaleEl[this.scaleIndex].majorIntervalValue!=0||s==this.scaleEl[this.scaleIndex].majorIntervalValue){if(u==this.scaleEl[this.scaleIndex].minimum&&!i.includeFirstValue)continue;r=this._getAngle(u);r=r>360?r-360:r;e=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,f,r);o=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,f,r);this.region={startX:e,startY:o};this.style={placement:i.placement,textPositionAngle:r,angle:this.scaleEl[this.scaleIndex].labels[this.labelIndex].autoAngle?r+i.angle:i.angle,isStroke:!1,isFill:!0,textValue:u,opacity:i.opacity?i.opacity:1,font:this._getFontString(i,i.font),fillStyle:i.color=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.color)};this.model.drawLabels&&this._onDrawLabels(this.scaleEl[this.scaleIndex].labels[this.labelIndex].autoAngle?r+i.angle:i.angle,u);this._drawLabel(this.region,this.style,!1);this.model.tooltip.showLabelTooltip&&this._savedPoints.push({startX:e-10,startY:o-5,width:20,height:parseInt(i.font.size.replace(/\D/g,"")),value:u})}h||(r=this._getAngle(this.scaleEl[this.scaleIndex].minimum),r=r>360?r-360:r,e=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,f,r),o=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,f,r),this.region={startX:e,startY:o},this.style={angle:this.scaleEl[this.scaleIndex].labels[this.labelIndex].autoAngle?r+i.angle:i.angle,isStroke:!1,isFill:!0,textValue:this.scaleEl[this.scaleIndex].minimum,opacity:i.opacity?i.opacity:1,font:this._getFontString(i,i.font),lineHeight:c,lineWidth:i.width,fillStyle:i.color=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.color)},this.model.drawLabels&&this._onDrawLabels(r,u),this._drawLabel(this.region,this.style,!1),this.model.tooltip.showLabelTooltip&&this._savedPoints.push({startX:e-10,startY:o-5,width:20,height:parseInt(i.font.size.replace(/\D/g,"")),value:u}),r=this._getAngle(u),r=r>360?r-360:r);this.contextEl.getImageData&&(this.labelImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height))},_setRangeCordinates:function(n,i){if(i.startValue<this.scaleEl[this.scaleIndex].maximum&&i.endValue<=this.scaleEl[this.scaleIndex].maximum){var o=i.startValue>=this.scaleEl[this.scaleIndex].minimum?i.startValue:this.scaleEl[this.scaleIndex].minimum,s=i.endValue>this.scaleEl[this.scaleIndex].maximum?this.scaleEl[this.scaleIndex].maximum:i.endValue,h,c,l,a,r,u,f,v,y,e,p;p=this._getAngle((o+s)/2);r=this._getAngle(o);f=this._getAngle(s);u=this.scaleEl[this.scaleIndex].radius-i.distanceFromScale-this.scaleEl[this.scaleIndex].size/2-i.size-(t.isNullOrUndefined(this.scaleEl[this.scaleIndex].ticks[0])?16:this.scaleEl[this.scaleIndex].ticks[0].height);v=this.scaleEl[this.scaleIndex].radius-i.distanceFromScale-this.scaleEl[this.scaleIndex].size/2-(t.isNullOrUndefined(this.scaleEl[this.scaleIndex].ticks[0])?16:this.scaleEl[this.scaleIndex].ticks[0].height);y=this.scaleEl[this.scaleIndex].radius-i.distanceFromScale-i.size-this.scaleEl[this.scaleIndex].size/2-(t.isNullOrUndefined(this.scaleEl[this.scaleIndex].ticks[0])?16:this.scaleEl[this.scaleIndex].ticks[0].height);h=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,u,r);c=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,u,r);l=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,u,f);a=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,u,f);r=180*this._getCirucumferenceAngle({x:this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,y:this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY},{x:h,y:c})/Math.PI;(i.startValue!=0||i.endValue!=this.scaleEl[this.scaleIndex].maximum)&&(f=180*this._getCirucumferenceAngle({x:this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,y:this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY},{x:l,y:a})/Math.PI);!t.isNullOrUndefined(i.gradients)&&i.gradients.colorInfo.length>0?(e=this.contextEl.createRadialGradient(this.centerX,this.centerY,y,this.centerX,this.centerY,v),this._setGradientColor(this,e,i.gradients.colorInfo)):e=i.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.backgroundColor);this.region={startX:h,startY:c,endX:l,endY:a,startAngle:r,endAngle:f};this.style={placement:i.placement,radius:i.placement=="near"?this.scaleEl[this.scaleIndex].radius-i.distanceFromScale:this.scaleEl[this.scaleIndex].radius+i.distanceFromScale,rangeStart:o,rangeEnd:s,startWidth:i.startWidth,isFill:!0,fillStyle:e,strokeStyle:i.border.color=="transparent"?"rgba(0,0,0,0)":i.border.color,opacity:isNaN(i.opacity)?1:i.opacity,counterClockwise:this.scaleEl[this.scaleIndex].direction=="clockwise"?!1:!0,startRadius:v,endRadius:y,endWidth:i.endWidth,lineWidth:i.border.width,isStroke:!0};this.model.drawRange&&this._onDrawRange(this.region,this.style);this._rangeCollectionRegions.push({Range:i,Region:this.region,scaleIndex:this.scaleIndex,rangeIndex:this.rangeIndex,Radius:{startRadius:this.style.startRadius,endRadius:this.style.endRadius},centerRadius:{centerX:this.centerX,centerY:this.centerY,radius:this.style.radius}});this._drawRange(this.region,this.style);this.contextEl.getImageData&&(this.rangeImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height))}},_setSubGauge:function(i,r){var u=n("div[id="+r.controlID+"]"),f;u.length>0&&u.find("canvas").length&&(f=u.find("canvas")[0].getContext("2d"),this.contextEl.drawImage(f.canvas,r.position.x,r.position.y,r.width,r.height),u.css("display","none"),this.contextEl.getImageData&&(this.subGaugeImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height)))},_setCustomLabelCordinates:function(n,i){this._customLblMaxSize=this._customLblMaxSize<parseFloat(i.font.size.match(/\d+/)[0])?parseFloat(i.font.size.match(/\d+/)[0]):this._customLblMaxSize;i.color=i.color?i.color:"#282828";this.region=i.position?{startX:i.position.x,startY:i.position.y}:{startX:0,startY:0};this.style={angle:i.textAngle,textValue:i.value,fillStyle:i.color=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.color),font:this._getFontString(i,i.font)};this.model.drawCustomLabel&&this._onDrawCustomLabel(this.region,this.style);this._drawLabel(this.region,this.style,!0);this.model.tooltip.showCustomLabelTooltip&&this._savedPoints.push({startX:this.region.startX-30,startY:this.region.startY-5,width:60,height:t.isNullOrUndefined(i.font)?10:parseInt(i.font.size.replace(/\D/g,"")),value:this.style.textValue});this.contextEl.getImageData&&(this.customLabelImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height))},_drawIndicator:function(i,r){var u=this,o=!1,f=r.type.toLowerCase(),e;this.region={centerX:r.position.x-r.width/2,startX:r.position.x,startY:r.position.y,textLocation:r.position,centerY:r.position.y-r.height/2,startAngle:0,endAngle:2*Math.PI};this.style={radius:r.width/2,strokeStyle:"#2BA104",cornerRadius:r.type=="roundedrectangle"?2:0,height:r.height,width:r.width,lineWidth:r.indicatorBorderWidth,fillStyle:"#2BA104",isStroke:!0,isFill:!0,indicatorText:r.indicatorText,textColor:null,font:null,counterClockwise:!1};this.model.drawIndicators&&this._onDrawIndicators(this.style,this.region);f==t.datavisualization.CircularGauge.IndicatorType.Image?(e=new Image,e.onload=function(){u.contextEl.drawImage(this,r.position.x,r.position.y)},e.src=r.imageUrl):r.stateRanges!=null&&n.each(r.stateRanges,function(n,i){u.pointerEl[u.pointerIndex].value>=i.startValue&&u.pointerEl[u.pointerIndex].value<=i.endValue?(o=!0,!t.isNullOrUndefined(i.text)&&i.text.length>0&&(u.style.indicatorText=i.text,u.style.textColor=i.textColor=="transparent"?"rgba(0,0,0,0)":i.textColor,u.style.font=r.font),u.style.strokeStyle=i.borderColor=="transparent"?"rgba(0,0,0,0)":i.borderColor,u.style.fillStyle=i.backgroundColor=="transparent"?"rgba(0,0,0,0)":i.backgroundColor,u._drawIndicatorFrame(f,u.region,u.style)):(u.style.strokeStyle=i.borderColor=="transparent"?"rgba(0,0,0,0)":i.borderColor,u.style.fillStyle=i.backgroundColor=="transparent"?"rgba(0,0,0,0)":i.backgroundColor)});o||f==t.datavisualization.CircularGauge.IndicatorType.Image||u._drawIndicatorFrame(f,u.region,u.style);this.contextEl.getImageData&&f!=t.datavisualization.CircularGauge.IndicatorType.Image&&(this.indicatorImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height))},_drawIndicatorFrame:function(n,t,i){switch(n){case"circle":this._drawIndicatorCircle(t,i);break;case"roundedrectangle":case"rectangle":this._drawRectangleFrame(t,i);break;case"text":this._drawText(t,i);break;case"triangle":this._drawIndicatorTriangle(t,i,this);break;case"diamond":this._drawDiamond(t,i,this);break;case"trapezoid":this._drawTrapezoid(t,i,this);break;case"pentagon":this._drawPentagon(t,i,this);break;case"wedge":this._drawWedge(t,i,this);break;case"star":this._drawIndicatorStar(t,i,this);break;case"ellipse":this._drawIndicatorEllipse(t,i,this);break;case"horizontalline":this._drawHorizontalLine(t,i,this);break;case"verticalline":this._drawVerticalLine(t,i,this);break;case"cross":this._drawCross(t,i,this);break;case"uparrow":this._drawUpArrow(t,i,this);break;case"downarrow":this._drawDownArrow(t,i,this);break;case"invertedtriangle":this._drawIndicatorInvertedTriangle(t,i,this);break;case"leftarrow":this._drawLeftArrow(t,i,this);break;case"rightarrow":this._drawRightArrow(t,i,this)}},_drawScaleBar:function(n,t){this.contextEl.shadowColor=t.strokeStyle=="transparent"?"rgba(0,0,0,0)":t.strokeStyle;t.shadowOffset&&(this.contextEl.shadowBlur=t.shadowOffset);this._contextOpenPath(t,this);n.endAngle-n.startAngle==0?this.contextEl.arc(n.centerX,n.centerY,n.startRadius,Math.PI*0,Math.PI*0,!1):(n.endAngle-n.startAngle)%360==0?(this.contextEl.arc(n.centerX,n.centerY,n.startRadius,Math.PI*0,Math.PI*2,!1),this.contextEl.arc(n.centerX,n.centerY,n.endRadius,Math.PI*2,Math.PI*0,!0)):(this.contextEl.arc(n.centerX,n.centerY,n.startRadius,Math.PI*(n.startAngle/180),Math.PI*(n.endAngle/180),!1),this.contextEl.arc(n.centerX,n.centerY,n.endRadius,Math.PI*(n.endAngle/180),Math.PI*(n.startAngle/180),!0));this._contextClosePath(t,this)},_drawTickMark:function(n,t){this._contextOpenPath(t,this);this.contextEl.translate(n.startX,n.startY);this.contextEl.lineTo(0,0);this.contextEl.rotate(Math.PI*(t.angle/180));this.contextEl.lineTo(t.lineHeight,0);this._contextClosePath(t,this)},_drawLabel:function(n,i,r){var f=0,e=this.model.locale,u;t.isNullOrUndefined(r)||r||(u=this.model.scales[this.scaleIndex].labels[this.labelIndex].unitTextPosition,i.textValue=i.textValue%1!=0&&typeof i.textValue!="string"?+parseFloat(i.textValue.toFixed(3)):i.textValue,i.textValue=e&&this.model.enableGroupSeparator?i.textValue.toLocaleString(e):i.textValue,t.isNullOrUndefined(u)||u.toString()!="back"?t.isNullOrUndefined(u)||u.toString()!="front"||(i.textValue=this.model.scales[this.scaleIndex].labels[this.labelIndex].unitText+i.textValue):i.textValue+=this.model.scales[this.scaleIndex].labels[this.labelIndex].unitText,f=this._calcDistanceFactor(i.textPositionAngle,i.placement,this._calFontLength(i.font)));this._contextOpenPath(i,this);this.contextEl.textAlign="center";this.contextEl.textBaseline="middle";this.contextEl.font=i.font;this.contextEl.translate(n.startX+f,n.startY);this.contextEl.lineTo(0,0);r?r&&i.angle!=0&&this.contextEl.rotate(Math.PI*(i.angle/180)):this.scaleEl[this.scaleIndex].labels[this.labelIndex].autoAngle?this.contextEl.rotate(Math.PI*((i.angle-270)/180)):this.contextEl.rotate(Math.PI*(i.angle/180));this.contextEl.fillText(i.textValue,0,0);this._contextClosePath(i,this)},_drawCircleFrame:function(n,t){this._contextOpenPath(t,this);this.contextEl.arc(n.centerX,n.centerY,t.radius,n.startAngle,n.endAngle,t.counterClockwise);this._contextClosePath(t,this);t.indicatorText&&this._drawText(n,t)},_drawIndicatorCircle:function(n,t){this._contextOpenPath(t,this);this.contextEl.arc(n.startX+t.width/2,n.startY,t.radius,n.startAngle,n.endAngle,t.counterClockwise);this._contextClosePath(t,this);t.indicatorText&&this._drawText(n,t)},_drawHalfCircle:function(n,t){this._contextOpenPath(t,this);this.contextEl.lineJoin="round";this.contextEl.arc(n.centerX,n.centerY,t.radius,n.startAngle,n.endAngle,!1);this.contextEl.lineTo(n.centerX,n.centerY);this._contextClosePath(t,this)},_drawRectangleFrame:function(n,t){this._contextOpenPath(t,this);this.contextEl.translate(n.startX,n.startY-t.height/2);this.contextEl.lineTo(t.cornerRadius,0);this.contextEl.lineTo(t.width-t.cornerRadius,0);this.contextEl.quadraticCurveTo(t.width,0,t.width,t.cornerRadius);this.contextEl.lineTo(t.width,t.height-t.cornerRadius);this.contextEl.quadraticCurveTo(t.width,t.height,t.width-t.cornerRadius,t.height);this.contextEl.lineTo(t.cornerRadius,t.height);this.contextEl.quadraticCurveTo(0,t.height,0,t.height-t.cornerRadius);this.contextEl.lineTo(0,t.cornerRadius);this.contextEl.quadraticCurveTo(0,0,t.cornerRadius,0);this._contextClosePath(t,this);t.indicatorText&&this._drawText(n,t)},_drawText:function(n,t){this.contextEl.beginPath();this.contextEl.textAlign="center";this.contextEl.fillStyle=t.textColor=="transparent"?"rgba(0,0,0,0)":t.textColor;this.contextEl.font=t.font;this.contextEl.fillText(t.indicatorText,n.textLocation.x,n.textLocation.y);this.contextEl.closePath()},_drawRange:function(n,t){var i;if(t.startWidth==null&&t.endWidth==null)this._contextOpenPath(t,this),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.startRadius,Math.PI*(n.startAngle/180),Math.PI*(n.endAngle/180),t.counterClockwise),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.endRadius,Math.PI*(n.endAngle/180),Math.PI*(n.startAngle/180),!t.counterClockwise),this._contextClosePath(t,this);else{var u=t.startWidth>t.endWidth?t.startWidth-t.endWidth:t.endWidth-t.startWidth,f=this.scaleEl[this.scaleIndex].direction=="clockwise"?n.startAngle<n.endAngle?n.endAngle-n.startAngle:360-(n.startAngle-n.endAngle):n.endAngle<n.startAngle?n.startAngle-n.endAngle:n.startAngle+(360-n.endAngle),r=f/(u*2);if(t.startWidth<t.endWidth)for(t.startWidth!=0&&(this._contextOpenPath(t,this),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.radius,Math.PI*(n.startAngle/180),Math.PI*(n.endAngle/180),t.counterClockwise),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.placement=="near"?t.radius-t.startWidth:t.radius+t.startWidth,Math.PI*(n.endAngle/180),Math.PI*(n.startAngle/180),!t.counterClockwise),this._contextClosePath(t,this)),t.radius=t.placement=="near"?t.radius-t.startWidth:t.radius+t.startWidth,t.endWidth-=t.startWidth,t.startWidth=0,i=t.startWidth;i<t.endWidth;i+=.5)this.contextEl.beginPath(),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.radius,Math.PI*(n.endAngle/180),Math.PI*(n.startAngle/180),!t.counterClockwise),this.contextEl.lineWidth=2,this.contextEl.strokeStyle=t.fillStyle,this.contextEl.stroke(),this.contextEl.restore(),n.startAngle=this.scaleEl[this.scaleIndex].direction=="clockwise"?n.startAngle+r:n.startAngle-r,t.radius=t.placement=="near"?t.radius-.5:t.radius+.5;else for(t.endWidth!=0&&(this._contextOpenPath(t,this),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.radius,Math.PI*(n.startAngle/180),Math.PI*(n.endAngle/180),t.counterClockwise),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.placement=="near"?t.radius-t.endWidth:t.radius+t.endWidth,Math.PI*(n.endAngle/180),Math.PI*(n.startAngle/180),!t.counterClockwise),this._contextClosePath(t,this)),t.radius=t.placement=="near"?t.radius-t.endWidth:t.radius+t.endWidth,t.startWidth-=t.endWidth,t.endWidth=0,i=t.endWidth;i<t.startWidth;i+=.5)this.contextEl.beginPath(),this.contextEl.arc(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,t.radius,Math.PI*(n.startAngle/180),Math.PI*(n.endAngle/180),t.counterClockwise),this.contextEl.lineWidth=2,this.contextEl.strokeStyle=t.fillStyle,this.contextEl.stroke(),this.contextEl.restore(),n.endAngle=this.scaleEl[this.scaleIndex].direction=="clockwise"?n.endAngle-r:n.endAngle+r,t.radius=t.placement=="near"?t.radius-.5:t.radius+.5}},_drawPointers:function(n,i){i.value=i.value>this.scaleEl[this.scaleIndex].maximum?this.scaleEl[this.scaleIndex].maximum:i.value;i.value=i.value<this.scaleEl[this.scaleIndex].minimum?this.scaleEl[this.scaleIndex].minimum:i.value;var e,u,r,f,o,s=this.model.locale;r=this._getAngle(i.value);r=r>360?r-360:r;i.type=="needle"?(i.placement=="far"&&(f=i.length+this.scaleEl[this.scaleIndex].size/2),i.placement=="center"&&(f=i.length),i.placement=="near"&&(f=i.length-this.scaleEl[this.scaleIndex].size/2)):(f=i.length,i.placement=="far"&&(e=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this.scaleEl[this.scaleIndex].radius+this.scaleEl[this.scaleIndex].size/2+i.distanceFromScale,r),u=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerX,this.scaleEl[this.scaleIndex].radius+this.scaleEl[this.scaleIndex].size/2+i.distanceFromScale,r)),i.placement=="center"&&(e=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this.scaleEl[this.scaleIndex].radius-this.scaleEl[this.scaleIndex].size/2-i.distanceFromScale,r),u=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerX,this.scaleEl[this.scaleIndex].radius-this.scaleEl[this.scaleIndex].size/2-i.distanceFromScale,r)),i.placement=="near"&&(e=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this.scaleEl[this.scaleIndex].radius-this.scaleEl[this.scaleIndex].size/2-i.distanceFromScale-this.majorTickHeight-15,r),u=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerX,this.scaleEl[this.scaleIndex].radius-this.scaleEl[this.scaleIndex].size/2-i.distanceFromScale-this.majorTickHeight-15,r)),this._isHalfCircle&&this._isHalfCircle()||(this.model.height>this.model.width?u+=(this.model.height-this.model.width)/2:this.model.height<this.model.width&&(u-=(this.model.width-this.model.height)/2)));f>this.model.radius&&(f=this.model.radius);this.region={startX:i.type=="needle"?this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX:e,startY:i.type=="needle"?this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY:u};!t.isNullOrUndefined(i.gradients)&&i.gradients.colorInfo.length>0?(o=this.contextEl.createLinearGradient(0,0,0,i.width),this._setGradientColor(this,o,i.gradients.colorInfo)):o=i.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.backgroundColor);this.style={width:i.width,isFill:!0,isStroke:!0,radius:0,showBackNeedle:i.showBackNeedle,backNeedleLength:i.backNeedleLength,angle:i.type=="needle"?r:i.placement=="far"?r:r+180,height:f,lineWidth:i.border.width,opacity:isNaN(i.opacity)?1:i.opacity,strokeStyle:i.border.color=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.border.color),fillStyle:o,imageUrl:i.imageUrl,type:i.type};this.model.drawPointers&&this._onDrawPointers(r,i.value);i.type=="needle"?(this._drawNeedlePointer(this.region,this.style,i),this._setPointerCap(i)):(i.markerType=="roundedrectangle"&&(this.style.radius=5),this._drawMarkerType(i.markerType,this.region,this.style));i.pointerValueText.showValue&&(e=this._getXCordinate(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._labelRadius+i.pointerValueText.distance,r),u=this._getYCordinate(this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,this._labelRadius+i.pointerValueText.distance,r),this.region={startX:e,startY:u},this.style={angle:i.pointerValueText.autoAngle?i.pointerValueText.angle+r:i.pointerValueText.angle,textValue:s&&this.model.enableGroupSeparator?i.value.toLocaleString(s):i.value,fillStyle:i.pointerValueText.color=="transparent"?"rgba(0,0,0,0)":this._getColor(i,i.pointerValueText.color),font:this._getFontString(i,i.pointerValueText.font),opacity:i.pointerValueText.opacity},this._drawPointerValueText(this.region,this.style));this.contextEl.getImageData&&(this.pointerImage=t.isNullOrUndefined(this.model.frame.backgroundImageUrl)?this.contextEl.getImageData(0,0,this.model.width,this.model.height):this.contextEl.drawImage(this.contextEl.canvas,this.model.width,this.model.height));this._notifyArrayChange&&this._notifyArrayChange("scales["+this.scaleIndex+"]pointers["+n+"]value",i.value);this.value(i.value)},_drawPointerValueText:function(n,t){this._contextOpenPath(t,this);this.contextEl.textAlign="center";this.contextEl.textBaseline="middle";this.contextEl.font=t.font;this.contextEl.translate(n.startX,n.startY);this.contextEl.lineTo(0,0);this.scaleEl[this.scaleIndex].pointers[this.pointerIndex].pointerValueText.autoAngle?this.contextEl.rotate(Math.PI*((t.angle-270)/180)):this.contextEl.rotate(Math.PI*(t.angle/180));this.contextEl.fillText(t.textValue,0,0);this._contextClosePath(t,this)},_drawMarkerType:function(n,i,r){switch(n){case"rectangle":this._drawRectangle(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"triangle":this._drawTriangle(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"ellipse":this._drawEllipse(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"diamond":this._drawDiamond(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"pentagon":this._drawPentagon(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"circle":this._drawCircle(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"slider":this._drawSlider(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"pointer":this._drawPointer(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"wedge":this._drawWedge(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"trapezoid":this._drawTrapezoid(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"roundedrectangle":this._drawRoundedRectangle(i,r,this);t.isNullOrUndefined(r.imageUrl)&&r.imageUrl==""||this._drawImagePointer(i,r,this);break;case"image":this._drawImagePointer(i,r,this)}},_drawNeedlePointer:function(n,t,i){this.pointerEl[this.pointerIndex].needleType=="image"?this._drawImagePointer(n,t,i):(this._contextOpenPath(t,this),this.contextEl.translate(n.startX,n.startY),this.contextEl.rotate(Math.PI*(t.angle/180)),this.contextEl.lineTo(0,-t.width/2),this.pointerEl[this.pointerIndex].needleType=="triangle"?this.contextEl.lineTo(t.height,0):this.pointerEl[this.pointerIndex].needleType=="rectangle"?(this.contextEl.lineTo(t.height,-t.width/2),this.contextEl.lineTo(t.height,t.width/2)):this.pointerEl[this.pointerIndex].needleType=="trapezoid"?(this.contextEl.lineTo(t.height,-t.width/4),this.contextEl.lineTo(t.height,t.width/4)):this.pointerEl[this.pointerIndex].needleType=="arrow"&&(this.contextEl.lineTo(t.height-t.height/4,-t.width/6),this.contextEl.lineTo(t.height-t.height/4,-t.width/2),this.contextEl.lineTo(t.height,0),this.contextEl.lineTo(t.height-t.height/4,t.width/2),this.contextEl.lineTo(t.height-t.height/4,t.width/6)),this.contextEl.lineTo(0,t.width/2),t.showBackNeedle&&(this.contextEl.lineTo(-(t.backNeedleLength+this.scaleEl[this.scaleIndex].pointerCap.radius/2),t.width/2),this.contextEl.lineTo(-(t.backNeedleLength+this.scaleEl[this.scaleIndex].pointerCap.radius/2),-t.width/2)),this._contextClosePath(t,this));this.canvasEl.attr("aria-label",this.model.scales[this.scaleIndex].pointers[this.pointerIndex].value)},_drawImagePointer:function(n,t){var i=this,r=new Image,f=t.angle,e=n.startX,o=n.startY,s=t.width,u=t.height,h=t.type;r.onload=function(){i.contextEl.save();i.contextEl.translate(e,o);i.contextEl.rotate(Math.PI*(f/180));i.contextEl.drawImage(this,0,-u/2,s,u);i.contextEl.restore()};r.src=t.imageUrl},_setPointerCap:function(n){var i;i=this.contextEl.createRadialGradient(this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.canvasEl[0].height/2,0,this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,this._isHalfCircle()?this._getHalfCircleYPosition():this.canvasEl[0].height/2,this.scaleEl[this.scaleIndex].pointerCap.radius);t.isNullOrUndefined(this.scaleEl[this.scaleIndex].pointerCap.interiorGradient)?i=this.scaleEl[this.scaleIndex].pointerCap.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(this,this._getColor(n,this.scaleEl[this.scaleIndex].pointerCap.backgroundColor)):this._setGradientColor(this,i,this.scaleEl[this.scaleIndex].pointerCap.interiorGradient.colorInfo);this.region={centerX:this._isHalfCircle()?this._getHalfCircleXPosition():this.centerX,centerY:this._isHalfCircle()?this._getHalfCircleYPosition():this.centerY,startAngle:0,endAngle:2*Math.PI};this.style={isStroke:!0,isFill:!0,strokeStyle:this.scaleEl[this.scaleIndex].pointerCap.borderColor=="transparent"?"rgba(0,0,0,0)":this._getColor(this,this._getColor(n,this.scaleEl[this.scaleIndex].pointerCap.borderColor)),radius:this.scaleEl[this.scaleIndex].pointerCap.radius,lineWidth:this.scaleEl[this.scaleIndex].pointerCap.borderWidth,fillStyle:i};this.model.drawPointerCap&&this._onDrawPointerCap();this._drawCircleFrame(this.region,this.style)},isAndroid:function(){return/android/i.test(navigator.userAgent.toLowerCase())},redraw:function(n){switch(n){case"scale":this._reDrawScale();break;case"pointer":this._reDrawPointer();break;case"range":this._reDrawRange();break;case"label":this._reDrawLabel();break;case"tickMark":this._reDrawTickMark();break;case"subGauges":this._reDrawSubGauge();break;case"CustomLabel":this._reDrawCustomLabel();break;default:this._init()}},_getIndicatorImage:function(){return this.pointerImage?this.pointerImage:this._getPointerImage()},_getPointerImage:function(){return this.customLabelImage?this.customLabelImage:this._getCustomLabelImage()},_getSubGaugeImage:function(){return this.labelImage?this.labelImage:this._getLabelImage()},_getCustomLabelImage:function(){return this.subGaugeImage?this.subGaugeImage:this._getSubGaugeImage()},_getRangeImage:function(){return this.model.rangeZOrder=="rear"?this.scaleImage?this.scaleImage:this.outerImage:this.tickImage?this.tickImage:this._getTickImage()},_getLabelImage:function(){return this.model.rangeZOrder=="rear"?this.tickImage?this.tickImage:this._getTickImage():this.tickImage?this.rangeImage:this._getRangeImage()},_getTickImage:function(){return this.model.rangeZOrder=="rear"?this.rangeImage?this.rangeImage:this._getRangeImage():this.scaleImage?this.scaleImage:this.outerImage},getPointerValue:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].value:null},setMarkerDistanceFromScale:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(this.scaleEl[n].pointers[t].distanceFromScale=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawPointer():this._initialize())},getMarkerDistanceFromScale:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].distanceFromScale:null},setPointerLength:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(this.scaleEl[n].pointers[t].length=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawPointer():this._initialize())},getPointerLength:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].length:null},setPointerWidth:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(this.scaleEl[n].pointers[t].width=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawPointer():this._initialize())},getPointerWidth:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].width:null},setBackNeedleLength:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(this.scaleEl[n].pointers[t].backNeedleLength=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawPointer():this._initialize())},getBackNeedleLength:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].backNeedleLength:null},setNeedleStyle:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(this.scaleEl[n].pointers[t].needleType=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawPointer():this._initialize())},getNeedleStyle:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].needleType:null},setPointerPlacement:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(this.scaleEl[n].pointers[t].placement=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawPointer():this._initialize())},getPointerPlacement:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].placement:null},setPointerNeedleType:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(this.scaleEl[n].pointers[t].type=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawCustomLabel():this._initialize())},getPointerNeedleType:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].type:null},setMarkerStyle:function(n,i,r){n<this.model.scales.length&&i<this.model.scales[n].pointers.length&&r!=null&&(r!="roundedrectangle"&&t.isNullOrUndefined(t.datavisualization.CircularGauge.MarkerType[r.replace(/\w\S*/g,function(n){return n.charAt(0).toUpperCase()+n.substr(1).toLowerCase()})])||(this.scaleEl[n].pointers[i].markerType=r,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawCustomLabel():this._initialize()))},getMarkerStyle:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].pointers.length?this.scaleEl[n].pointers[t].markerType:null},setCustomLabelValue:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].customLabels.length&&i!=null&&(this.scaleEl[n].customLabels[t].value=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawCustomLabel():this._initialize())},getCustomLabelValue:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].customLabels.length?this.scaleEl[n].customLabels[t].value:null},setSubGaugeLocation:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].subGauges.length&&i!=null&&(this.scaleEl[n].subGauges[t].position.x=i.x,this.scaleEl[n].subGauges[t].position.y=i.y,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawSubGauge():this._initialize())},getSubGaugeLocation:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].subGauges.length?this.scaleEl[n].subGauges[t].position:null},setCustomLabelAngle:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].customLabels.length&&i!=null&&(this.scaleEl[n].customLabels[t].textAngle=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawCustomLabel():this._initialize())},getCustomLabelAngle:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].customLabels.length?this.scaleEl[n].customLabels[t].textAngle:null},setRangeStartValue:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].startValue=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawRange():this._initialize())},getRangeStartValue:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ranges.length?this.scaleEl[n].ranges[t].startValue:null},setRangeEndValue:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].endValue=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawRange():this._initialize())},getRangeEndValue:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ranges.length?this.scaleEl[n].ranges[t].endValue:null},setRangeSize:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].size=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawRange():this._initialize())},setRangeVisible:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t]._visible=i,this.contextEl.putImageData&&!this.isAndroid()?(this._initialize(),this._wireEvents()):this._initialize())},getRangeSize:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ranges.length?this.scaleEl[n].ranges[t].size:null},setRangeDistanceFromScale:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].distanceFromScale=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawRange():this._initialize())},getRangeDistanceFromScale:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ranges.length?this.scaleEl[n].ranges[t].distanceFromScale:null},setRangePosition:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].placement=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawRange():this._initialize())},getRangePosition:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ranges.length?this.scaleEl[n].ranges[t].placement:null},setRangeBorderWidth:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].border.width=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawRange():this._initialize())},getRangeBorderWidth:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ranges.length?this.scaleEl[n].ranges[t].border.width:null},setPointerValue:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].pointers.length&&i!=null&&(i>=this.scaleEl[n].minimum&&i<=this.scaleEl[n].maximum&&(this.scaleEl[n].pointers[t].value=i),!this.updatePointerOnAnimation&&this.model.enableAnimation&&(this.pointerValue[t]=i),this.contextEl.putImageData&&!this._androidAnimation?(this._reDrawPointer(),this.updatePointerOnAnimation=!1):this._initialize())},setLabelAngle:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].angle=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawLabel():this._initialize())},getLabelAngle:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].labels.length?this.scaleEl[n].labels[t].angle:null},setLabelDistanceFromScale:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].distanceFromScale=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawLabel():this._initialize())},getLabelDistanceFromScale:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].labels.length?this.scaleEl[n].labels[t].distanceFromScale:null},setLabelStyle:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].type=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawLabel():this._initialize())},getLabelStyle:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].labels.length?this.scaleEl[n].labels[t].type:null},setLabelPlacement:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].placement=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawLabel():this._initialize())},getLabelPlacement:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].labels.length?this.scaleEl[n].labels[t].placement:null},setTickAngle:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].angle=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawTickMark():this._initialize())},getTickAngle:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ticks.length?this.scaleEl[n].ticks[t].angle:null},setTickStyle:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].type=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawTickMark():this._initialize())},getTickStyle:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ticks.length?this.scaleEl[n].ticks[t].type:null},setTickPlacement:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].placement=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawTickMark():this._initialize())},getTickPlacement:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ticks.length?this.scaleEl[n].ticks[t].placement:null},setTickWidth:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].width=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawTickMark():this._initialize())},getTickWidth:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ticks.length?this.scaleEl[n].ticks[t].width:null},setTickHeight:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].height=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawTickMark():this._initialize())},getTickHeight:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ticks.length?this.scaleEl[n].ticks[t].height:null},setTickDistanceFromScale:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].distanceFromScale=i,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawTickMark():this._initialize())},getTickDistanceFromScale:function(n,t){return n<this.model.scales.length&&t<this.model.scales[n].ticks.length?this.scaleEl[n].ticks[t].distanceFromScale:null},setStartAngle:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].startAngle=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getStartAngle:function(n){return n<this.model.scales.length?this.scaleEl[n].startAngle:null},setSweepAngle:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].sweepAngle=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getSweepAngle:function(n){return n<this.model.scales.length?this.scaleEl[n].sweepAngle:null},setMinimumValue:function(n,t){n<this.model.scales.length&&t!=null&&(t<this.scaleEl[n].maximum&&(this.scaleEl[n].minimum=t),this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getMinimumValue:function(n){return n<this.model.scales.length?this.scaleEl[n].minimum:null},setMaximumValue:function(n,t){n<this.model.scales.length&&t!=null&&(t>this.scaleEl[n].minimum&&(this.scaleEl[n].maximum=t),this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getMaximumValue:function(n){return n<this.model.scales.length?this.scaleEl[n].maximum:null},setScaleBarSize:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].size=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getScaleBarSize:function(n){return n<this.model.scales.length?this.scaleEl[n].size:null},setScaleRadius:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].radius=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getScaleRadius:function(n){return n<this.model.scales.length?this.scaleEl[n].radius:null},setMajorIntervalValue:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].majorIntervalValue=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getMajorIntervalValue:function(n){return n<this.model.scales.length?this.scaleEl[n].majorIntervalValue:null},setMinorIntervalValue:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].minorIntervalValue=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getMinorIntervalValue:function(n){return n<this.model.scales.length?this.scaleEl[n].minorIntervalValue:null},setPointerCapRadius:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].pointerCap.radius=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getPointerCapRadius:function(n){return n<this.model.scales.length?this.scaleEl[n].pointerCap.radius:null},setScaleBorderWidth:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].border.width=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getScaleBorderWidth:function(n){return n<this.model.scales.length?this.scaleEl[n].border.width:null},setPointerCapBorderWidth:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].pointerCap.borderWidth=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getPointerCapBorderWidth:function(n){return n<this.model.scales.length?this.scaleEl[n].pointerCap.borderWidth:null},setScaleDirection:function(n,t){n<this.model.scales.length&&t!=null&&(this.scaleEl[n].direction=t,this.contextEl.putImageData&&!this.isAndroid()?this._reDrawScale():this._initialize())},getScaleDirection:function(n){return n<this.model.scales.length?this.scaleEl[n].direction:null},includeFirstValue:function(n,t,i){n<this.model.scales.length&&t<this.model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].includeFirstValue=i,this.contextEl.putImageData?this._reDrawLabel():this.initialize())},_reDrawScale:function(){t.isNullOrUndefined(this.model.frame.backgroundImageUrl)&&(this.contextEl.putImageData(this.outerImage,0,0),this._drawScales())},_reDrawPointer:function(){t.isNullOrUndefined(this.model.frame.backgroundImageUrl)&&(this.contextEl.putImageData(this._getPointerImage(),0,0),this._setPointers(),this._setIndicators())},_reDrawCustomLabel:function(){t.isNullOrUndefined(this.model.frame.backgroundImageUrl)&&(this.contextEl.putImageData(this._getCustomLabelImage(),0,0),this._setCustomLabel(),this._setPointers(),this._setIndicators())},_reDrawRange:function(){t.isNullOrUndefined(this.model.frame.backgroundImageUrl)&&(this.contextEl.putImageData(this._getRangeImage(),0,0),this.model.rangeZOrder=="rear"?(this._setRanges(),this._setTicks()):this._setRanges(),this._setLabels(),this._subGauge(),this._setCustomLabel(),this._setPointers(),this._setIndicators())},_reDrawLabel:function(){t.isNullOrUndefined(this.model.frame.backgroundImageUrl)&&(this.contextEl.putImageData(this._getLabelImage(),0,0),this._setLabels(),this._subGauge(),this._setCustomLabel(),this._setPointers(),this._setIndicators())},_reDrawTickMark:function(){t.isNullOrUndefined(this.model.frame.backgroundImageUrl)&&(this.contextEl.putImageData(this._getTickImage(),0,0),this.model.rangeZOrder=="rear"?this._setTicks():(this._setTicks(),this._setRanges()),this._setLabels(),this._subGauge(),this._setCustomLabel(),this._setPointers(),this._setIndicators())},_reDrawSubGauge:function(){t.isNullOrUndefined(this.model.frame.backgroundImageUrl)&&(this.contextEl.putImageData(this._getSubGaugeImage(),0,0),this._subGauge(),this._setCustomLabel(),this._setPointers(),this._setIndicators())},refreshSubGauge:function(){this.contextEl.putImageData?this._reDrawSubGauge():this._initialize()},refresh:function(){this._scaleRedrawn=!0;this._initialize();this._wireEvents()},"export":function(){var i=this.model.exportSettings,u,f,e,r,o,s;i.mode.toLowerCase()==="client"?this.exportImage(i.fileName,i.fileType):(f=i.type.toLowerCase()==="jpg"?"image/jpeg":"image/png",u=this.canvasEl[0].toDataURL(f),e={action:i.action,method:"post"},r=t.buildTag("form","",null,e),o={name:"Data",type:"hidden",value:u},s=t.buildTag("input","",null,o),r.append(s).append(this),n("body").append(r),r.submit())},exportImage:function(n,i){var c,o,e,f,l,h,u,r,v,s,w,y;if(t.browserInfo().name==="msie"&&parseFloat(t.browserInfo().version)<10)return!1;if(c=document.getElementById(this._id),o=new Image,c!==null){for(e=document.createElement("canvas"),f=e.getContext("2d"),e.width=c.clientWidth,e.height=c.clientHeight,l=20,h=!0,this.model.outerCustomLabelPosition!="top"&&this.model.outerCustomLabelPosition!="left"&&f.drawImage(this.canvasEl[0],0,0),u=document.getElementsByClassName(this._id+"outercustomlbl"),r=0;r<u.length;r++)f.fillStyle=u[r].style.color,f.font=u[r].style.fontWeight+" "+u[r].style.fontSize+" "+u[r].style.fontFamily,this.model.outerCustomLabelPosition==="top"?(h==!0&&f.drawImage(this.canvasEl[0],0,e.height-this.model.height),f.fillText(u[r].innerHTML,u[r].getBoundingClientRect().left,u[r].getBoundingClientRect().top+u[r].getBoundingClientRect().height+l),h=!1):this.model.outerCustomLabelPosition==="bottom"?f.fillText(u[r].innerHTML,u[r].getBoundingClientRect().left,u[r].getBoundingClientRect().top+u[r].getBoundingClientRect().height-l):this.model.outerCustomLabelPosition==="right"?f.fillText(u[r].innerHTML,u[r].getBoundingClientRect().left,u[r].getBoundingClientRect().top+u[r].getBoundingClientRect().height):(h===!0&&f.drawImage(this.canvasEl[0],e.width/2,(e.height-this.model.height)/2),f.fillText(u[r].innerHTML,u[r].getBoundingClientRect().left,u[r].getBoundingClientRect().top+u[r].getBoundingClientRect().height),h=!1);o=e.toDataURL()}else o=this.canvasEl[0].toDataURL();o=o.replace(/^data:[a-z]*;,/,"");var b=o.split(","),a=atob(b[1]),p=new ArrayBuffer(a.length),k=new Uint8Array(p);for(r=0;r<a.length;r++)k[r]=a.charCodeAt(r);return v=new Blob([p],{type:"image/png"}),t.browserInfo().name==="msie"?window.navigator.msSaveOrOpenBlob(v,n+"."+i):(s=document.createElement("a"),w=URL.createObjectURL(v),s.href=w,s.setAttribute("download",n+"."+i),document.createEvent?(y=document.createEvent("MouseEvents"),y.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),s.dispatchEvent(y)):s.fireEvent&&s.fireEvent("onclick")),!0},resizeCanvas:function(){var s,h,i,o,f;if(r=r!=0?r-1:n(".e-circulargauge").length-1,h=!0,t.isNullOrUndefined(this.GaugeEl.parent().attr("style"))||(s=this.GaugeEl.parent().attr("style").split(";")),t.isNullOrUndefined(s)||n.each(s,function(n,t){while(t.indexOf("width")!=-1){h=t.indexOf("px")==-1?!0:!1;break}}),h){var e=window.innerWidth/u,c=this.model.width,l=this.model.height;for((c*e>window.innerWidth||l*e>window.innerHeight)&&(e=1),this.model.width*=e,this.model.height*=e,this.model.radius*=e,this._gaugeResizeState=!0,i=0;this.model.scales[i]!=null;i++){for(this.model.scales[i].radius*=e,this.model.scales[i].pointerCap.radius*=e,o=0;o<this.model.scales[i].customLabels.length;o++)t.isNullOrUndefined(this.model.scales[i].customLabels[o])||(this.model.scales[i].customLabels[o].positionType!="outer"&&(this.model.scales[i].customLabels[o].position.x*=e),this.model.scales[i].customLabels[o].position.y*=e,this.model.scales[i].customLabels[o].font.size=parseFloat(this.model.scales[i].customLabels[o].font.size.match(/\d+/)[0])*e<10?"10px":parseFloat(this.model.scales[i].customLabels[o].font.size.match(/\d+/)[0])*e>this._customLblMaxSize?this._customLblMaxSize.toString()+"px":(parseFloat(this.model.scales[i].customLabels[o].font.size.match(/\d+/)[0])*e).toString()+"px");for(f=0;this.model.scales[i].labels[f]!=null||this.model.scales[i].pointers[f]!=null||this.model.scales[i].ranges[f]!=null||this.model.scales[i].indicators[f]!=null||this.model.scales[i].subGauges[f]!=null||this.model.scales[i].ticks[f]!=null;f++)t.isNullOrUndefined(this.model.scales[i].ticks[f])||(this.model.scales[i].ticks[f].height*=e),t.isNullOrUndefined(this.model.scales[i].pointers[f])||(this.model.scales[i].pointers[f].length*=e,this.model.scales[i].pointers[f].width*=e,this.model.scales[i].pointers[f].backNeedleLength*=e),t.isNullOrUndefined(this.model.scales[i].ranges[f])||(this.model.scales[i].ranges[f].distanceFromScale*=e,this.model.scales[i].ranges[f].size*=e),!t.isNullOrUndefined(this.model.scales[i].indicators[f])&&this.model.scales[i].showIndicators&&(this.model.scales[i].indicators[f].height*=e,this.model.scales[i].indicators[f].width*=e,this.model.scales[i].indicators[f].position.x*=e,this.model.scales[i].indicators[f].position.y*=e),!t.isNullOrUndefined(this.model.scales[i].subGauges[f])&&t.isNullOrUndefined(this.model.scales[i].subGauges[f].controlID)&&(this.model.scales[i].subGauges[f].height*=e,this.model.scales[i].subGauges[f].width*=e,this.model.scales[i].subGauges[f].position.x*=e)}this.refresh();r==0&&(u=window.innerWidth)}},_onDrawTicks:function(n,t){var i={index:this.tickIndex,element:this.tickEl[this.tickIndex],angle:parseInt(n)},r={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,pointerValue:t,style:this.style,position:this.region,tick:i};this._trigger("drawTicks",r)},_onDrawLabels:function(n,t){var i={index:this.labelIndex,element:this.labelEl[this.labelIndex],angle:parseInt(n)},r={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,pointerValue:t,style:this.style,position:this.region,label:i};this._trigger("drawLabels",r)},_onDrawPointers:function(n,t){var i={index:this.pointerIndex,element:this.pointerEl[this.pointerIndex],angle:parseInt(n),pointerValue:t},r={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region,pointer:i};this._trigger("drawPointers",r)},_onDrawRange:function(){var n={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,rangeIndex:this.rangeIndex,rangeElement:this.rangeEl[this.rangeEl],context:this.contextEl,style:this.style,position:this.region};this._trigger("drawRange",n)},_onDrawCustomLabel:function(){var n={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,customLabelIndex:this.customLabelIndex,customLabelElement:this.customLabelEl[this.customLabelIndex],context:this.contextEl,style:this.style,position:this.region};this._trigger("drawCustomLabel",n)},_onDrawIndicators:function(){var n={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,indicatorIndex:this.indicatorIndex,indicatorEl:this.indicatorEl[this.indicatorIndex],context:this.contextEl,style:this.style,position:this.region};this._trigger("drawIndicators",n)},_onDrawPointerCap:function(){var n={object:this,scaleElement:this.model.scales,position:this.region,style:this.style,context:this.contextEl};this._trigger("drawPointerCap",n)},_onRenderComplete:function(){var n={object:this,scaleElement:this.model.scales,context:this.contextEl};this._trigger("renderComplete",n)},_onMouseClick:function(n,t){var i={index:this.pointerIndex,element:this.pointerEl[this.pointerIndex],value:t,angle:parseInt(n)},r={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.position,pointers:i};this._trigger("mouseClick",r)},_onMouseClickMove:function(n,t){var i={index:this.pointerIndex,element:this.pointerEl[this.pointerIndex],value:t,angle:parseInt(n)},r={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region,pointers:i};this._trigger("mouseClickMove",r)},_onMouseClickUp:function(n,t){var i={index:this.pointerIndex,element:this.pointerEl[this.pointerIndex],value:t,angle:parseInt(n)},r={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region,pointers:i};this._trigger("mouseClickUp",r)},_setTheme:function(){var n=this.model.theme.toLowerCase(),t=this.model.themeProperties[n];this._setThemeColors(t)},_setThemeColors:function(n){var r=[],f=this.model.themeProperties,s,i,t,u,e,o;for(s in f)r.push(s);for(i=0;i<r.length;i++)for(this.model.backgroundColor=!this.model.backgroundColor||this.model.backgroundColor==f[r[i]].backgroundColor?n.backgroundColor:this.model.backgroundColor,t=0;t<this.model.scales.length;t++){for(this.model.scales[t].backgroundColor=!this.model.scales[t].backgroundColor||this.model.scales[t].backgroundColor==f[r[i]].scales.backgroundColor?n.scales.backgroundColor:this.model.scales[t].backgroundColor,this.model.scales[t].border.color=!this.model.scales[t].border.color||this.model.scales[t].border.color==f[r[i]].scales.border.color?n.scales.border.color:this.model.scales[t].border.color,u=0;u<this.model.scales[t].pointers.length;u++)this.model.scales[t].pointers[u].backgroundColor=!this.model.scales[t].pointers[u].backgroundColor||this.model.scales[t].pointers[u].backgroundColor==f[r[i]].scales.pointers.backgroundColor?n.scales.pointers.backgroundColor:this.model.scales[t].pointers[u].backgroundColor,this.model.scales[t].pointers[u].border.color=!this.model.scales[t].pointers[u].border.color||this.model.scales[t].pointers[u].border.color==f[r[i]].scales.pointers.border.color?n.scales.pointers.border.color:this.model.scales[t].pointers[u].border.color,this.model.scales[t].pointerCap.backgroundColor=!this.model.scales[t].pointerCap.backgroundColor||this.model.scales[t].pointerCap.backgroundColor==f[r[i]].scales.pointerCap.backgroundColor?n.scales.pointerCap.backgroundColor:this.model.scales[t].pointerCap.backgroundColor,this.model.scales[t].pointerCap.borderColor=!this.model.scales[t].pointerCap.borderColor||this.model.scales[t].pointerCap.borderColor==f[r[i]].scales.pointerCap.borderColor?n.scales.pointerCap.borderColor:this.model.scales[t].pointerCap.borderColor;for(e=0;e<this.model.scales[t].ticks.length;e++)this.model.scales[t].ticks[e].color=!this.model.scales[t].ticks[e].color||this.model.scales[t].ticks[e].color==f[r[i]].scales.ticks.color?n.scales.ticks.color:this.model.scales[t].ticks[e].color;for(o=0;o<this.model.scales[t].labels.length;o++)this.model.scales[t].labels[o].color=!this.model.scales[t].labels[o].color||this.model.scales[t].labels[o].color==f[r[i]].scales.labels.color?n.scales.labels.color:this.model.scales[t].labels[o].color}},_getFontString:function(n,t){return t?(t.fontStyle?t.fontStyle:"")+" "+(t.size==null?"11px":t.size)+" "+t.fontFamily:""},_drawTriangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);this._contextClosePath(t,i)},_drawPointer:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(t.width,t.height/4);i.contextEl.lineTo(t.width,-t.height/4);i.contextEl.lineTo(t.width/2,-t.height/4);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/2,t.height/2);i.contextEl.lineTo(t.width/2,t.height/4);this._contextClosePath(t,i)},_drawWedge:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(3*t.width/4,0);i.contextEl.lineTo(t.width,t.height/2);this._contextClosePath(t,i)},_drawSlider:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/4,-t.height/2);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);i.contextEl.lineTo(t.width/4,t.height/2);this._contextClosePath(t,i)},_drawIndicatorStar:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);i.contextEl.lineTo(0,t.height/2);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);i.contextEl.lineTo(0,-t.height/4);i.contextEl.lineTo(t.width,-t.height/4);i.contextEl.lineTo(0,t.height/2);this._contextClosePath(t,i)},_drawStar:function(n,t,i){this._contextOpenPath(t,i);i.model.Orientation=="Horizontal"&&i.markerPlacement=="near"?(i.contextEl.lineTo(n.startX+t.width-t.width/6,n.startY),i.contextEl.lineTo(n.startX,n.startY+t.height-t.height/3),i.contextEl.lineTo(n.startX+t.width,n.startY+t.height-t.height/3),i.contextEl.lineTo(n.startX+t.width/6,n.startY),i.contextEl.lineTo(n.startX+t.width/2,n.startY+t.height)):(i.contextEl.lineTo(n.startX+t.width/6,n.startY+t.height),i.contextEl.lineTo(n.startX+t.width,n.startY+t.height/3),i.contextEl.lineTo(n.startX,n.startY+t.height/3),i.contextEl.lineTo(n.startX+t.width-t.width/6,n.startY+t.height),i.contextEl.lineTo(n.startX+t.width/2,n.startY));this._contextClosePath(t,i)},_drawPentagon:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/3,-t.height/2);i.contextEl.lineTo(t.width,-t.height/4);i.contextEl.lineTo(t.width,t.height/4);i.contextEl.lineTo(t.width/3,t.height/2);this._contextClosePath(t,i)},_drawDiamond:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.lineTo(t.width,0);i.contextEl.lineTo(t.width/2,t.height/2);i.contextEl.lineTo(0,0);this._contextClosePath(t,i)},_drawCircle:function(n,t,i){var r=Math.sqrt(t.height*t.height+t.width*t.width)/2;t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.arc(r/2,0,r/2,0,Math.PI*2,!0);this._contextClosePath(t,i)},_drawLine:function(n,t,i){i.contextEl.beginPath();i.contextEl.strokeStyle=t.fillStyle;i.contextEl.globalAlpha=t.opacity;i.contextEl.lineWidth=t.lineWidth;i.contextEl.moveTo(n.startX,n.startY);i.contextEl.lineTo(n.startX+t.width,n.startY);i.contextEl.closePath();i.contextEl.stroke()},_drawHorizontalLine:function(n,t,i){i.contextEl.beginPath();i.contextEl.strokeStyle=t.fillStyle;i.contextEl.globalAlpha=t.opacity;i.contextEl.lineWidth=t.lineWidth;i.contextEl.moveTo(n.startX,n.startY);i.contextEl.lineTo(n.startX+t.width,n.startY);i.contextEl.closePath();i.contextEl.stroke()},_drawVerticalLine:function(n,t,i){i.contextEl.beginPath();i.contextEl.strokeStyle=t.fillStyle;i.contextEl.globalAlpha=t.opacity;i.contextEl.lineWidth=t.lineWidth;i.contextEl.moveTo(n.startX,n.startY+t.height/2);i.contextEl.lineTo(n.startX,n.startY+-t.height/2);i.contextEl.closePath();i.contextEl.stroke()},_drawCross:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width,0);i.contextEl.moveTo(t.width/2,0);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.moveTo(t.width/2,0);i.contextEl.lineTo(t.width/2,t.height/2);this._contextClosePath(t,i)},_drawIndicatorTriangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,t.height/2);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);this._contextClosePath(t,i)},_drawIndicatorInvertedTriangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);i.contextEl.lineTo(0,-t.height/2);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width/2,t.height/2);this._contextClosePath(t,i)},_drawUpArrow:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.lineTo(t.width,0);i.contextEl.lineTo(t.width*(3/4),0);i.contextEl.lineTo(t.width*(3/4),t.height/2);i.contextEl.lineTo(t.width/4,t.height/2);i.contextEl.lineTo(t.width/4,0);this._contextClosePath(t,i)},_drawDownArrow:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/4,0);i.contextEl.lineTo(t.width/4,-t.height/2);i.contextEl.lineTo(t.width*(3/4),-t.height/2);i.contextEl.lineTo(t.width*(3/4),0);i.contextEl.lineTo(t.width,0);i.contextEl.lineTo(t.width/2,t.height/2);this._contextClosePath(t,i)},_drawLeftArrow:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.moveTo(n.startX,n.startY);i.contextEl.lineTo(n.startX+t.width/2,n.startY+-t.height/2);i.contextEl.lineTo(n.startX+t.width/2,n.startY+-t.height/4);i.contextEl.lineTo(n.startX+t.width,n.startY+-t.height/4);i.contextEl.lineTo(n.startX+t.width,n.startY+t.height/4);i.contextEl.lineTo(n.startX+t.width/2,n.startY+t.height/4);i.contextEl.lineTo(n.startX+t.width/2,n.startY+t.height/2);i.contextEl.lineTo(n.startX,n.startY);this._contextClosePath(t,i)},_drawRightArrow:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.lineTo(n.startX,n.startY);i.contextEl.lineTo(n.startX,n.startY+-t.height/4);i.contextEl.lineTo(n.startX+t.width/2,n.startY+-t.height/4);i.contextEl.lineTo(n.startX+t.width/2,n.startY+-t.height/2);i.contextEl.lineTo(n.startX+t.width,n.startY);i.contextEl.lineTo(n.startX+t.width/2,n.startY+t.height/2);i.contextEl.lineTo(n.startX+t.width/2,n.startY+t.height/4);i.contextEl.lineTo(n.startX,n.startY+t.height/4);i.contextEl.lineTo(n.startX,n.startY);this._contextClosePath(t,i)},_drawTrapezoid:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(0,-t.height/4);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);i.contextEl.lineTo(0,t.height/4);this._contextClosePath(t,i)},_drawRectangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(0,-t.height/2);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);i.contextEl.lineTo(0,t.height/2);this._contextClosePath(t,i)},_drawRoundedRectangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(t.radius,-t.height/2);i.contextEl.lineTo(t.width-t.radius,-t.height/2);i.contextEl.quadraticCurveTo(t.width,-t.height/2,t.width,-t.height/2+t.radius);i.contextEl.lineTo(t.width,t.height/2-t.radius);i.contextEl.quadraticCurveTo(t.width,t.height/2,t.width-t.radius,t.height/2);i.contextEl.lineTo(t.radius,t.height/2);i.contextEl.quadraticCurveTo(0,t.height/2,0,t.height/2-t.radius);i.contextEl.lineTo(0,-t.height/2+t.radius);i.contextEl.quadraticCurveTo(0,-t.height/2,t.radius,-t.height/2);this._contextClosePath(t,i)},_drawCustomImage:function(t,i){var r=new Image;n(r).on("load",function(){t.contextEl.drawImage(this,0,0,t.model.width,t.model.height);t.model.scales!=null&&t._drawScales();t.model.items!=null&&t._renderItems()}).attr("src",i)},_drawIndicatorEllipse:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.moveTo(n.startX,n.startY);i.contextEl.bezierCurveTo(n.startX,n.startY-t.height/2,n.startX+t.width,n.startY-t.height/2,n.startX+t.width,n.startY);i.contextEl.bezierCurveTo(n.startX+t.width,n.startY+t.height/2,n.startX,n.startY+t.height/2,n.startX,n.startY);this._contextClosePath(t,i)},_drawEllipse:function(n,t,i){var r=Math.sqrt(t.height*t.height+t.width*t.width)/2;t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.scale(2,1);i.contextEl.arc(r/2,0,r/2,0,Math.PI*2,!0);this._contextClosePath(t,i)},_setPointerDimension:function(n,i){if(!t.isNullOrUndefined(i.model.Orientation)&&i.model.Orientation=="Horizontal"){var r=n.width,u=n.height;n.height=r;n.width=u}return n},_setContextRotation:function(n,t){t.contextEl.rotate(Math.PI*(n.angle/180))},_contextOpenPath:function(n,t){t.contextEl.save();t.contextEl.beginPath();n.strokeStyle&&(t.contextEl.strokeStyle=n.strokeStyle);n.opacity!=i&&(t.contextEl.globalAlpha=n.opacity);n.lineWidth&&(t.contextEl.lineWidth=n.lineWidth);n.fillStyle&&(t.contextEl.fillStyle=n.fillStyle)},_contextClosePath:function(n,t){t.contextEl.closePath();n.isFill&&t.contextEl.fill();n.isStroke&&t.contextEl.stroke();t.contextEl.restore()},_blockDefaultActions:function(n){n.cancelBubble=!0;n.returnValue=!1;n.preventDefault&&n.preventDefault();n.stopPropagation&&n.stopPropagation()},_isBetween:function(n,t,i,r){return n<t?i>=n-r&&i<=t+r:i>=t-r&&i<=n+r},_getColor:function(n,t){return typeof t=="string"?t:"rgba("+t.r+", "+t.g+","+t.b+", "+t.a/255+")"},_setGradientColor:function(t,i,r){var u=t;r.Name||typeof r=="string"?(i.addColorStop(0,this._getColor(t,r)),i.addColorStop(1,this._getColor(t,r))):n.each(r,function(n,r){i.addColorStop(r.colorStop!=NaN?r.colorStop:0,typeof r.color=="string"?r.color:u._getColor(t,r.color))})}}),function(){for(var u=0,f=["ms","moz","webkit","o"],i,r,n=0;n<f.length&&!window.requestAnimationFrame;++n)window.requestAnimationFrame=window[f[n]+"RequestAnimationFrame"];window.requestAnimationFrame||(t.datavisualization.CircularGauge.animationPolyfill=!0,window.requestAnimationFrame=function(n,t){return i=(new Date).getTime(),r=Math.max(0,16-(i-u)),t=window.setTimeout(function(){n(i+r)},r),u=i+r,t})}();t.datavisualization.CircularGauge.Frame={FullCircle:"fullcircle",HalfCircle:"halfcircle"};t.datavisualization.CircularGauge.Directions={Clockwise:"clockwise",CounterClockwise:"counterclockwise"};t.datavisualization.CircularGauge.PointerPlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.CircularGauge.PointerType={Needle:"needle",Marker:"marker"};t.datavisualization.CircularGauge.NeedleType={Triangle:"triangle",Rectangle:"rectangle",Trapezoid:"trapezoid",Arrow:"arrow",Image:"image"};t.datavisualization.CircularGauge.MarkerType={Rectangle:"rectangle",Triangle:"triangle",Ellipse:"ellipse",Diamond:"diamond",Pentagon:"pentagon",Circle:"circle",Slider:"slider",Pointer:"pointer",Wedge:"wedge",Trapezoid:"trapezoid",RoundedRectangle:"roundedrectangle",Image:"image"};t.datavisualization.CircularGauge.RangePlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.CircularGauge.TickType={Major:"major",Minor:"minor"};t.datavisualization.CircularGauge.gaugePosition={TopLeft:"topleft",TopRight:"topright",TopCenter:"topcenter",MiddleLeft:"middleleft",MiddleRight:"middleright",Center:"center",BottomLeft:"bottomleft",BottomRight:"bottomright",BottomCenter:"bottomcenter"};t.datavisualization.CircularGauge.TickPlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.CircularGauge.CustomLabelPositionType={Inner:"inner",Outer:"outer"};t.datavisualization.CircularGauge.OuterCustomLabelPosition={Left:"left",Right:"right",Top:"top",Bottom:"bottom"};t.datavisualization.CircularGauge.LabelPlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.CircularGauge.LabelType={Major:"major",Minor:"minor"};t.datavisualization.CircularGauge.UnitTextPlacement={Back:"back",Front:"front"};t.datavisualization.CircularGauge.RangeZOrderPosition={Rear:"rear",Front:"front"};t.datavisualization.CircularGauge.IndicatorType={Rectangle:"rectangle",Circle:"circle",RoundedRectangle:"roundedrectangle",Text:"text",Image:"image",Cross:"cross",Diamond:"diamond",DownArrow:"downarrow",Ellipse:"ellipse",HorizontalLine:"horizontalLine",InvertedTriangle:"invertedtriangle",LeftArrow:"leftarrow",Pentagon:"pentagon",RightArrow:"rightarrow",Star:"star",Trapezoid:"trapezoid",Triangle:"triangle",UpArrow:"uparrow",VerticalLine:"verticalline",Wedge:"wedge"};t.datavisualization.CircularGauge.Themes={FlatLight:"flatlight",FlatDark:"flatdark"};t.datavisualization.CircularGauge.LegendPosition={Top:"top",Bottom:"bottom",Left:"left",Right:"right"};t.datavisualization.CircularGauge.LegendAlignment={Near:"near",Center:"center",Far:"far"};t.datavisualization.CircularGauge.LegendShape={Rectangle:"rectangle",Triangle:"triangle",Diamond:"diamond",Pentagon:"pentagon",Circle:"circle",Slider:"slider",Wedge:"wedge",Trapezoid:"trapezoid",Line:"line"}})(jQuery,Syncfusion),function(n,t,i){var u,r,f;t.widget({ejLinearGauge:"ej.datavisualization.LinearGauge"},{element:null,_rootCSS:"e-lineargauge",_animationFlag:!0,model:null,_customLblMaxSize:0,_savedPoints:[],validTags:["div","span"],defaults:{exportSettings:{mode:"client",type:"png",fileName:"CircularGauge",action:""},locale:null,enableGroupSeparator:!1,value:0,minimum:0,maximum:100,width:150,height:400,theme:"flatlight",orientation:"Vertical",pointerGradient1:null,pointerGradient2:null,backgroundColor:null,borderColor:null,labelColor:null,tickColor:null,readOnly:!0,enableResize:!1,isResponsive:!1,tooltip:{showLabelTooltip:!1,showCustomLabelTooltip:!1,templateID:null},outerCustomLabelPosition:"bottom",frame:{backgroundImageUrl:null,outerWidth:12,innerWidth:8},scales:null,enableAnimation:!0,enableMarkerPointerAnimation:!0,animationSpeed:500,drawTicks:null,drawLabels:null,drawBarPointers:null,drawMarkerPointers:null,drawRange:null,drawCustomLabel:null,drawIndicators:null,load:null,doubleClick:"",rightClick:"",init:null,renderComplete:null,mouseClick:null,mouseClickMove:null,mouseClickUp:null,themeProperties:{flatlight:{scales:{backgroundColor:"#FFFFFF",border:{color:"#1d1e1e"},barPointers:{backgroundColor:"#8abc3b",border:{color:"#8abc3b"}},markerPointers:{backgroundColor:"#212121",border:{color:"#212121"}},ticks:{color:"#1d1e1e"},labels:{labelColor:"#222222"}}},flatdark:{scales:{backgroundColor:"#808080",border:{color:"#808080"},barPointers:{backgroundColor:"#8abc3b",border:{color:"#8abc3b"}},markerPointers:{backgroundColor:"#CCCCCC",border:{color:"#CCCCCC"}},ticks:{color:"#808080"},labels:{labelColor:"#CCCCCC"}}}}},_defaultScaleValues:function(){return{minimum:null,maximum:null,majorIntervalValue:10,minorIntervalValue:2,direction:"counterclockwise",backgroundColor:null,border:{color:null,width:1.5},opacity:NaN,width:30,shadowOffset:0,length:290,type:"rectangle",position:{x:50,y:50},showRanges:!1,showIndicators:!1,showCustomLabels:!1,showLabels:!0,showTicks:!0,showBarPointers:!0,showMarkerPointers:!0,ticks:[{distanceFromScale:{x:0,y:0},angle:0,color:null,type:"majorinterval",placement:"near",opacity:0,height:10,width:3},{distanceFromScale:{x:0,y:0},angle:0,color:null,type:"minorinterval",placement:"near",opacity:0,height:5,width:2}],ranges:[{endWidth:10,placement:"center",startWidth:10,distanceFromScale:0,endValue:60,startValue:20,gradients:null,backgroundColor:null,border:{color:null,width:1.5},opacity:null}],labels:[{distanceFromScale:{x:-10,y:0},angle:0,font:{size:"11px",fontFamily:"Arial",fontStyle:"bold"},textColor:null,opacity:0,type:"major",placement:"near",includeFirstValue:!0,unitText:"",unitTextPlacement:"back"}],markerPointers:[{type:"triangle",length:30,placement:"far",gradients:null,distanceFromScale:0,width:30,value:null,backgroundColor:null,border:{color:null,width:1.5},opacity:1}],barPointers:[{gradients:null,distanceFromScale:0,width:30,value:null,backgroundColor:null,border:{color:null,width:1.5},opacity:1}],indicators:[{font:{size:"11px",fontFamily:"Arial",fontStyle:"bold"},height:30,type:"rectangle",width:30,position:{x:0,y:0},textLocation:{x:0,y:0},stateRanges:[{endValue:60,startValue:50,backgroundColor:null,borderColor:null,text:"",textColor:null}],backgroundColor:null,border:{color:null,width:1.5},opacity:NaN}],customLabels:[{font:{size:"11px",fontFamily:"Arial",fontStyle:"bold"},color:null,opacity:0,value:"",textAngle:0,position:{x:0,y:0},positionType:"inner"}]}},dataTypes:{scales:"data",isResponsive:"boolean"},observables:["value","minimum","maximum"],_tags:[{tag:"scales",attr:["majorIntervalValue","minorIntervalValue","backgroundColor","shadowOffset","showRanges","showIndicators","showCustomLabels","showLabels","showTicks","showBarPointers","showMarkerPointers","border.color","border.width","position.x","position.y",[{tag:"markerPointers",attr:["distanceFromScale","backgroundColor","border.width","border.color"]},{tag:"barPointers",attr:["distanceFromScale","backgroundColor","border.width","border.color"]},{tag:"ranges",attr:["distanceFromScale","startValue","endValue","startWidth","endWidth","backgroundColor","border.color","border.width"]},{tag:"ticks",attr:["distanceFromScale.x","distanceFromScale.y"]},{tag:"indicators",attr:["backgroundColor","textLocation","font.size","font.fontFamily","font.fontStyle","position.x","position.y","textLocation.x","textLocation.y","borderColor","textColor",[{tag:"stateRanges",attr:["endValue","startValue","backgroundColor","borderColor","textColor"]}]]},{tag:"labels",attr:["distanceFromScale.x","distanceFromScale.y","textColor","includeFirstValue","unitText","unitTextPlacement","font.size","font.fontFamily","font.fontStyle"]},{tag:"customLabels",attr:["textAngle","font.size","font.fontFamily","font.fontStyle","position.x","position.y"]}]]}],value:t.util.valueFunction("value"),minimum:t.util.valueFunction("minimum"),maximum:t.util.valueFunction("maximum"),_init:function(){r=n(".e-lineargauge").length;f=r;this._initialize();this._trigger("load");this._setTheme();this._render();this.wireEvents();this._onWindowResize()},_onWindowResize:function(){(this.model.enableResize||this.model.isResponsive)&&(t.isTouchDevice()?this._on(n(window),"orientationchange",this.resizeCanvas):this._on(n(window),"resize",this.resizeCanvas))},_setModel:function(t){var i,r,f,u,e,o,s;for(i in t)switch(i){case"theme":this.model.theme=t[i];this._init();break;case"height":this.model.height=t[i];break;case"width":this.model.width=t[i];break;case"orientation":this.model.orientation=t[i];break;case"pointerGradient1":this.model.pointerGradient1=t[i];break;case"pointerGradient2":this.model.pointerGradient2=t[i];break;case"labelColor":this.model.labelColor=t[i];break;case"tick":n.extend(this.model.tick,t[i]);break;case"backgroundColor":this.model.backgroundColor=t[i];break;case"borderColor":this.model.borderColor=t[i];break;case"frame":n.extend(this.model.frame,t[i]);case"outerCustomLabelPosition":this.model.outerCustomLabelPosition=t[i];break;case"tooltip":n.extend(this.model.tooltip,t[i]);break;case"readOnly":this.model.readOnly=t[i];break;case"value":for(this.value()==""&&this.value(0),r=0;this.model.scales[r]!=null;r++)for(f=0;this.model.scales[r].markerPointers[f]!=null;f++)this.model.scales[r].markerPointers[f].value=parseFloat(this.value());for(u=0;this.model.scales[u]!=null;u++)for(e=0;this.model.scales[u].barPointers[e]!=null;e++)this.model.scales[u].barPointers[e].value=parseFloat(this.value());break;case"minimum":for(this.minimum()==""&&this.minimum(0),o=0;this.model.scales[o]!=null;o++)this.model.scales[o].minimum=parseInt(this.minimum());break;case"maximum":for(this.maximum()==""&&this.maximum(0),s=0;this.model.scales[s]!=null;s++)this.model.scales[s].maximum=parseInt(this.maximum());break;case"scales":this.model.scales=t[i];this._itemInitialize()}this._render();this.wireEvents()},_destroy:function(){this.activeElement=null;this.canvasEl=null;this.contextEl=null;this.unWireEvents();this.element.empty().removeClass("e-lineargauge e-js e-widget")},_scales:function(){this._itemInitialize();this._render()},_scales_markerPointers:function(){this.refresh();this._trigger("refresh")},_scales_barPointers:function(){this.refresh();this._trigger("refresh")},_scales_ranges:function(){this.refresh();this._trigger("refresh")},_scales_ticks:function(){this.refresh();this._trigger("refresh")},_scales_indicators:function(){this.refresh();this._trigger("refresh")},_scales_indicators_stateRanges:function(){this.refresh();this._trigger("refresh")},_scales_labels:function(){this.refresh();this._trigger("refresh")},_scales_customLabels:function(){this.refresh();this._trigger("refresh")},_initialize:function(){this.GaugeEl=this.element;this.scaleStartX=[];this.scaleStartY=[];this.isScaleModified=!1;this.target=this.element[0];this._itemInitialize();this.Model=this.model},_render:function(){this.initialize();this.wireEvents()},_itemInitialize:function(){var t=this;this.model.scales!=null?n.each(this.model.scales,function(i,r){r=t._checkArrayObject(r,i);var u=t._defaultScaleValues();n.extend(u,r);n.extend(r,u)}):this.model.scales=[this._defaultScaleValues()]},_checkArrayObject:function(t,i){var r=this,u;return n.each(t,function(n,t){if(u=typeof n,(u!="string"||u=="string"&&n.indexOf("_")==-1&&n.indexOf("__")==-1)&&typeof t!="function")if(t instanceof Array)r._checkArrayObject(t,n);else if(t!=null&&typeof t=="object"&&!t.setter&&!t.factory&&!t.key){var f=r._defaultScaleValues();r._LoadIndividualDefaultValues(t,f,typeof n=="number"?i:n)}}),t},_LoadIndividualDefaultValues:function(t,i,r){var u=null,e=this,f;return n.each(i,function(n,t){if(r==n){u=t;return}}),u instanceof Array&&(u=u[0]),f=typeof r,n.each(t,function(n,t){t instanceof Array?e._checkArrayObject(t,r):t!=null&&typeof t=="object"&&(f!="string"||f=="string"&&r.indexOf("_")==-1&&r.indexOf("__")==-1)&&e._LoadIndividualDefaultValues(t,u,typeof n=="number"?r:n)}),n.extend(u,t),n.extend(t,u),t},initialize:function(){this._initObject(this);this.Model.frame.backgroundImageUrl?this._drawCustomImage(this,this.Model.frame.backgroundImageUrl):this.Model.scales!=null&&this._drawScales()},_initObject:function(i){var a,e,s,h,o,l,c;for(this._savedPoints=[],this.element.addClass("e-widget"),i.GaugeEl=i.element,i.canvasEl&&(i.canvasEl.parent().empty(),i.GaugeEl.empty()),i.canvasEl=n("<canvas><\/canvas>"),a=0,e=0;this.model.scales[e]!=null;e++){for(this.model.scales[e].minimum==null&&(this.model.scales[e].minimum=this.minimum()),this.model.scales[e].maximum==null&&(this.model.scales[e].maximum=this.maximum()),s=0;this.model.scales[e].markerPointers[s]!=null;s++)this.model.scales[e].markerPointers[s].value==null&&(this.model.scales[e].markerPointers[s].value=this.value());for(h=0;this.model.scales[e].barPointers[h]!=null;h++)this.model.scales[e].barPointers[h].value==null&&(this.model.scales[e].barPointers[h].value=this.value());for(o=0;this.model.scales[e].customLabels[o]!=null&&this.model.scales[e].showCustomLabels==!0;o++)if(a++,this.model.scales[e].customLabels[o].value!=null&&i.GaugeEl.find("div").length==0)if(this.model.scales[e].customLabels[o]!=null&&this.model.scales[e].customLabels[o].positionType!=null&&this.model.scales[e].customLabels[o].positionType=="outer")if(i.outerDiv=t.buildTag("div"),i.model.outerCustomLabelPosition=="bottom")i.GaugeEl.append(i.canvasEl),i.GaugeEl.append(i.outerDiv),i.outerDiv.css("text-align","center"),i.GaugeEl.css({width:i.model.width});else if(i.model.outerCustomLabelPosition!="top"){l=t.buildTag("TABLE");l.css("width","100%");var v=t.buildTag("TR"),y=t.buildTag("TD"),p=t.buildTag("td");i.model.outerCustomLabelPosition=="left"?(y.append(i.outerDiv),p.append(i.canvasEl)):(y.append(i.canvasEl),p.append(i.outerDiv));v.append(y);v.append(p);l.append(v);i.GaugeEl.append(l);i.outerDiv.css({width:this.element.width()-i.model.width})}else i.GaugeEl.append(i.outerDiv),i.GaugeEl.append(i.canvasEl),i.GaugeEl.css({width:i.model.width}),i.outerDiv.css("text-align","center");else i.GaugeEl.append(i.canvasEl);a==0&&i.GaugeEl.append(i.canvasEl)}(i.canvasEl.attr("role","presentation"),r==f&&(u=window.innerWidth),i.canvasEl[0].setAttribute("width",i.model.width),i.canvasEl[0].setAttribute("height",i.model.height),i.centerX=i.canvasEl[0].width/2,i.centerY=i.canvasEl[0].height/2,c=i.canvasEl[0],typeof G_vmlCanvasManager!="undefined"&&(c=window.G_vmlCanvasManager.initElement(c)),c&&c.getContext)&&(i.contextEl=i.canvasEl[0].getContext("2d"))},_drawFrameCircle:function(n,t,i){this._contextOpenPath(t,i);i.contextEl.arc(n.startX,n.startY,t.circleRadius,0,2*Math.PI,!0);this._contextClosePath(t,i);t.indicatorText&&i._drawText(n,t)},_drawFrameRectangle:function(n,t,i){this._contextOpenPath(t,i);i.contextEl.lineTo(n.startX+t.radius,n.startY);i.contextEl.lineTo(n.startX+t.width-t.radius,n.startY);i.contextEl.lineTo(n.startX+t.width,n.startY+t.height-t.radius);i.contextEl.lineTo(n.startX+t.radius,n.startY+t.height);this._contextClosePath(t,i);t.indicatorText&&i._drawText(n,t)},_drawFrameThermometer:function(n,t,i){var r=i.Model.orientation=="Vertical"?Math.sqrt(t.width*t.width+t.width*t.width)/2:Math.sqrt(t.height*t.height+t.height*t.height)/2;this._contextOpenPath(t,i);i.Model.orientation=="Vertical"?i.scaleEl[i.scaleIndex].direction=="counterclockwise"?(i.contextEl.arc(n.startX+Math.cos(Math.PI*(45/180))*r,n.startY+t.height-Math.sin(Math.PI*(45/180))*r,r,Math.PI*(-45/180),Math.PI*(225/180),!1),i.contextEl.lineTo(n.startX,n.startY+t.calDistance+t.width/2),t.topRounded?i.contextEl.arc(n.startX+t.width/2,n.startY+t.width/2,t.width/2,-Math.PI,0,!1):i.contextEl.lineTo(n.startX+t.width,n.startY+t.calDistance+t.width/2)):(i.contextEl.arc(n.startX+Math.cos(Math.PI*(45/180))*r,n.startY+Math.sin(Math.PI*(45/180))*r,r,Math.PI*(45/180),Math.PI*(-225/180),!0),i.contextEl.lineTo(n.startX,n.startY+t.height-t.width/2),t.topRounded?i.contextEl.arc(n.startX+t.width/2,n.startY+t.height-t.width/2,t.width/2,-Math.PI,0,!0):i.contextEl.lineTo(n.startX+t.width,n.startY+t.height-t.width/2)):i.scaleEl[i.scaleIndex].direction=="counterclockwise"?(i.contextEl.arc(n.startX+t.width-r/4-Math.cos(Math.PI*(45/180))*r,n.startY+Math.sin(Math.PI*(45/180))*r,r,Math.PI*(135/180),Math.PI*(225/180),!0),i.contextEl.lineTo(n.startX+t.height/2,n.startY),t.topRounded?i.contextEl.arc(n.startX+t.height/2,n.startY+t.height/2,t.height/2,Math.PI*(270/180),Math.PI*(90/180),!0):i.contextEl.lineTo(n.startX+t.height/2,n.startY+t.height)):(i.contextEl.arc(n.startX+r/4+Math.cos(Math.PI*(45/180))*r,n.startY+Math.sin(Math.PI*(45/180))*r,r,Math.PI*(45/180),Math.PI*(315/180),!1),i.contextEl.lineTo(n.startX+t.width-t.height/2,n.startY),t.topRounded?i.contextEl.arc(n.startX+t.width-t.height/2,n.startY+t.height/2,t.height/2,Math.PI*(270/180),Math.PI*(90/180),!1):i.contextEl.lineTo(n.startX+t.width-t.height/2,n.startY+t.height));this._contextClosePath(t,i)},_drawFrameRoundedRectangle:function(n,t,i){this._contextOpenPath(t,i);i.contextEl.lineTo(n.startX+t.radius,n.startY);i.contextEl.lineTo(n.startX+t.width-t.radius,n.startY);i.contextEl.quadraticCurveTo(n.startX+t.width,n.startY,n.startX+t.width,n.startY+t.radius);i.contextEl.lineTo(n.startX+t.width,n.startY+t.height-t.radius);i.contextEl.quadraticCurveTo(n.startX+t.width,n.startY+t.height,n.startX+t.width-t.radius,n.startY+t.height);i.contextEl.lineTo(n.startX+t.radius,n.startY+t.height);i.contextEl.quadraticCurveTo(n.startX,n.startY+t.height,n.startX,n.startY+t.height-t.radius);i.contextEl.lineTo(n.startX,n.startY+t.radius);i.contextEl.quadraticCurveTo(n.startX,n.startY,n.startX+t.radius,n.startY);this._contextClosePath(t,i);t.indicatorText&&this._drawText(n,t)},_contextOpenPath:function(n,t){t.contextEl.save();t.contextEl.beginPath();n.strokeStyle&&(t.contextEl.strokeStyle=n.strokeStyle);n.opacity!=i&&(t.contextEl.globalAlpha=n.opacity);n.lineWidth&&(t.contextEl.lineWidth=n.lineWidth);n.fillStyle&&(t.contextEl.fillStyle=n.fillStyle)},_contextClosePath:function(n,t){t.contextEl.closePath();n.isFill&&t.contextEl.fill();n.isStroke&&t.contextEl.stroke();t.contextEl.restore()},_drawScales:function(){var t=this,i;this.scaleEl=this.Model.scales;this.contextEl.save();this.contextEl.translate(this.Model.frame.outerWidth+this.Model.frame.innerWidth,this.Model.frame.outerWidth+this.Model.frame.innerWidth);n.each(this.Model.scales,function(n,i){t.scaleIndex=n;t._setScaleCordinates(i,i.type)});this._setTicks();this._setLabels();this._setRange();this._setCustomLabel();this._flagPointer=!1;this._tempOpacity=this.model.scales[0].barPointers[0].opacity;this._setBarPointers();this._setMarkerPointers();this._setIndicators();n.each(this.Model.scales,function(n,i){i.showBarPointers&&i.barPointers.length>1&&(t.model.enableAnimation=!1);i.showMarkerPointers&&i.markerPointers.length>1&&(t.model.enableAnimation=!1)});this.contextEl.putImageData||(this.model.enableAnimation=!1);this.model.animationSpeed!=null&&this.model.animationSpeed>0&&(i=this.model.animationSpeed/25,i>=0&&this.model.enableAnimation&&this._animationFlag&&this._onAnimate(i))},_setTicks:function(){var t=this;n.each(this.Model.scales,function(i,r){r.showTicks&&(t.scaleIndex=i,r.ticks!=null&&(t.tickEl=r.ticks,n.each(r.ticks,function(n,i){t.tickIndex=n;t._setTicksCordinates(i,n)})))})},_setLabels:function(){var t=this;n.each(this.Model.scales,function(i,r){r.showLabels&&(t.scaleIndex=i,r.labels!=null&&(t.labelEl=r.labels,n.each(r.labels,function(n,i){t.labelIndex=n;t._setLabelCordinates(i,n)})))})},_setIndicators:function(){var t=this;n.each(this.Model.scales,function(i,r){t.scaleIndex=i;r.indicators!=null&&r.showIndicators&&(t.indicatorEl=r.indicators,n.each(r.indicators,function(n,i){t.indicatorIndex=n;t._drawIndicator(n,i)}))})},_setBarPointers:function(){var t=this;n.each(this.Model.scales,function(i,r){r.showBarPointers&&(t.scaleIndex=i,r.barPointers!=null&&(t.barPointerEl=r.barPointers,n.each(r.barPointers,function(n,i){t.barPointerIndex=n;r.opacity=t.scaleIndex==0&&t.barPointerIndex==0&&t.model.enableAnimation==!0&&t._flagPointer==!1&&t._animationFlag==!0&&t.model.scales[0].type=="thermometer"?0:t._tempOpacity;t._drawScaleBarPointer(i,n);t._flagPointer=!0})))})},_setMarkerPointers:function(){var t=this;n.each(this.Model.scales,function(i,r){r.showMarkerPointers&&(t.scaleIndex=i,r.markerPointers!=null&&(t.markerPointerEl=r.markerPointers,n.each(r.markerPointers,function(n,i){t.markerPointerIndex=n;t._drawMarkerPointer(i,n);t.canvasEl.attr("aria-label",t.model.scales[t.scaleIndex].markerPointers[t.markerPointerIndex].value)})))})},_onAnimate:function(n){var t=this,f,e,i=t.model.scales[0].minimum,r=t.model.scales[0].barPointers[0].value,u=t.model.scales[0].markerPointers[0].value;f=setInterval(function(){t.model?r>i||i==t.model.scales[0].minimum?(i=i+(t.model.scales[0].maximum-t.model.scales[0].minimum)/100,t.scaleEl[0].type=="thermometer"?(t.model.scales[0].barPointers[0].value=r>i?i:r,t.contextEl.putImageData!="undefined"?t._setBarPointers():r>i?t.setBarPointerValue(0,0,i):t.setBarPointerValue(0,0,r)):r>i?t.setBarPointerValue(0,0,i):t.setBarPointerValue(0,0,r)):(t._animationFlag=!1,t.setBarPointerValue(0,0,r),window.clearInterval(f)):window.clearInterval(f)},n);e=setInterval(function(){t.model&&t.model.enableMarkerPointerAnimation&&t.model.enableAnimation?u>i||i==t.model.scales[0].minimum?(i=i+(t.model.scales[0].maximum-t.model.scales[0].minimum)/100,t.scaleEl[0].type=="thermometer"?(t.model.scales[0].markerPointers[0].value=u>i?i:u,t.contextEl.putImageData!="undefined"?t._setMarkerPointers():u>i?t.setPointerValue(0,0,i):t.setPointerValue(0,0,u)):u>i?t.setPointerValue(0,0,i):t.setPointerValue(0,0,u)):(t._animationFlag=!1,t.setPointerValue(0,0,u),window.clearInterval(e)):window.clearInterval(e)},n)},_setRange:function(){var t=this;n.each(this.Model.scales,function(i,r){t.scaleIndex=i;r.ranges!=null&&r.showRanges&&(t.rangeEl=r.ranges,n.each(r.ranges,function(n,i){t.rangeIndex=n;t._drawRange(i)}))})},_setCustomLabel:function(){var t=this;n.each(this.Model.scales,function(i,r){t.scaleIndex=i;r.customLabels!=null&&r.showCustomLabels&&(t.customLabelEl=r.customLabels,n.each(r.customLabels,function(n,i){t.customLabelIndex=n;t.model.scales[t.scaleIndex].customLabels[t.customLabelIndex]!=null&&t.model.scales[t.scaleIndex].customLabels[t.customLabelIndex].positionType!=null&&t.model.scales[t.scaleIndex].customLabels[t.customLabelIndex].positionType=="outer"?t._setOuterCustomLabelCordinates(n,i):t._setCustomLabelCordinates(n,i)}))})},_setOuterCustomLabelCordinates:function(n,i){var r,u;this._customLblMaxSize=this._customLblMaxSize<parseFloat(i.font.size.match(/\d+/)[0])?parseFloat(i.font.size.match(/\d+/)[0]):this._customLblMaxSize;r=t.buildTag("div."+this._id+"outercustomlbl");r.text(this.model.scales[this.scaleIndex].customLabels[n].value);u=this.model.outerCustomLabelPosition=="right"||this.model.outerCustomLabelPosition=="left"?"left":"center";this.outerDiv.append(r);this.outerDiv.append("<\/br>");u=="center"?r.css({display:"inline-block",margin:"0 auto","max-width":this.model.width}):r.css({display:"inline-block","max-width":this.element.width()-this.model.width>10?this.element.width()-this.model.width:10});r.css({color:i.color,overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap","font-size":i.font!=null&&i.font.size!=null?i.font.size:"12px","font-family":i.font!=null&&i.font.fontFamily!=null?i.font.fontFamily:"Arial","font-weight":i.font!=null&&i.font.fontStyle!=null?i.font.fontStyle:"Normal","text-align":u})},_setScaleCordinates:function(n,t){var r,i,u,f;this.opacity=1;this.bottomRadius=Math.sqrt(n.width*n.width+n.width*n.width)/2;this.bounds={height:this.canvasEl[0].height-2*(this.Model.frame.outerWidth+this.Model.frame.innerWidth),width:this.canvasEl[0].width-2*(this.Model.frame.outerWidth+this.Model.frame.innerWidth)};this.Model.orientation=="Vertical"?(this.scaleStartX[this.scaleIndex]=(this.bounds.width-n.width)*(n.position.x/100),this.scaleStartY[this.scaleIndex]=(this.bounds.height-n.length)*(n.position.y/100)):(this.scaleStartX[this.scaleIndex]=(this.bounds.width-n.length)*(n.position.x/100),this.scaleStartY[this.scaleIndex]=(this.bounds.height-n.width)*(n.position.y/100));u=t=="roundedrectangle"?5:0;r={startX:this.scaleStartX[this.scaleIndex],startY:this.scaleStartY[this.scaleIndex]};i={width:this.Model.orientation=="Vertical"?n.width:n.length,isStroke:!0,topRounded:!0,fillStyle:n.backgroundColor?n.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.backgroundColor):this.Model.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,this.Model.backgroundColor),lineWidth:n.border.width,radius:u,height:this.Model.orientation=="Vertical"?n.length:n.width,isFill:!0,strokeStyle:n.border.color?n.border.color=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.border.color):this.Model.borderColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,this.Model.borderColor)};n.maximum<n.minimum&&(f=n.maximum,n.maximum=n.minimum,n.minimum=f);n.maximum==n.minimum&&(n.maximum=n.maximum+1);this.minimum(n.minimum);this.maximum(n.maximum);this._notifyArrayChange&&(this._notifyArrayChange("scales["+this.scaleIndex+"]maximum",n.maximum),this._notifyArrayChange("scales["+this.scaleIndex+"]minimum",n.minimum));n.shadowOffset&&(this.contextEl.shadowBlur=n.shadowOffset,this.contextEl.shadowColor=i.fillStyle=="transparent"?"rgba(0,0,0,0)":i.fillStyle);this._drawFrame(t,r,i);this.scaleEl[this.scaleIndex].type!="thermometer"||this.isScaleModified||(this._modifyWidth(),this.isScaleModified=!0);this.contextEl.getImageData&&(this.scaleImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height))},_setTicksCordinates:function(n){var r,u,f,i,t;if(this.scaleEl[this.scaleIndex].majorIntervalValue>this.scaleEl[this.scaleIndex].minorIntervalValue){for(i=n.type.toLowerCase()=="majorinterval"?this.scaleEl[this.scaleIndex].majorIntervalValue:this.scaleEl[this.scaleIndex].minorIntervalValue,n.placement=="near"?r=this.Model.orientation=="Vertical"?this.scaleStartX[this.scaleIndex]:this.scaleStartY[this.scaleIndex]:n.placement=="far"?r=this.Model.orientation=="Vertical"?this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width:n.placement=="center"&&(r=this.Model.orientation=="Vertical"?this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2),u=n.placement=="near"?-n.height:n.height,t=this.scaleEl[this.scaleIndex].maximum;t>=this.scaleEl[this.scaleIndex].minimum&&i!="";t-=i)(i==this.scaleEl[this.scaleIndex].minorIntervalValue&&t%this.scaleEl[this.scaleIndex].majorIntervalValue!=0||i==this.scaleEl[this.scaleIndex].majorIntervalValue)&&(f=this._getClockwiseLinePosition(t),this.region={lineChangePosition:f+(this.Model.orientation=="horizontal"?n.distanceFromScale.x:n.distanceFromScale.y),lineStaticPosition:r+(this.Model.orientation=="horizontal"?n.distanceFromScale.y:n.distanceFromScale.x)},this.style={lineHeight:u,angle:this.Model.orientation=="Vertical"?n.angle:n.angle+270,tickShape:n.TickShape,strokeStyle:n.color?n.color=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.color):this.Model.tickColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,this.Model.tickColor),lineWidth:n.width},this.Model.drawTicks&&this._onDrawTicks(this.Model.orientation=="Vertical"?n.angle:n.angle+270,t),this._drawTickMark(this.region,this.style));this.contextEl.getImageData&&(this.tickImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height))}},_drawTickMark:function(n,t){this.contextEl.beginPath();this.contextEl.save();this.contextEl.lineWidth=t.lineWidth;this.contextEl.strokeStyle=t.strokeStyle;this.Model.orientation=="Vertical"?this.contextEl.translate(n.lineStaticPosition,n.lineChangePosition):this.contextEl.translate(n.lineChangePosition,n.lineStaticPosition);this.contextEl.lineTo(0,0);this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this.contextEl.rotate(Math.PI*(t.angle/180)):this.contextEl.rotate(-(Math.PI*(t.angle/180)));this.contextEl.lineTo(t.lineHeight,0);this.contextEl.stroke();this.contextEl.restore();this.contextEl.closePath()},_addDecimal:function(n,t){var r=n.toString(),u=t.toString(),f,e,i,o;return f=r.indexOf(".")>-1?r.length-r.indexOf(".")-1:0,e=u.indexOf(".")>-1?u.length-u.indexOf(".")-1:0,i=f>e?f:e,o=(n*Math.pow(10,i)+t*Math.pow(10,i))/Math.pow(10,i),o},_setLabelCordinates:function(n){var u,f,i,e,r,o=this.model.locale,t;if(this.scaleEl[this.scaleIndex].majorIntervalValue>this.scaleEl[this.scaleIndex].minorIntervalValue)for(this.Model.orientation=="Vertical"?(u=n.distanceFromScale.x,f=n.distanceFromScale.y):(u=n.distanceFromScale.y,f=n.distanceFromScale.x),r=n.type=="major"?this.scaleEl[this.scaleIndex].majorIntervalValue:this.scaleEl[this.scaleIndex].minorIntervalValue,n.placement=="near"?(i=this.Model.orientation=="Vertical"?this.scaleStartX[this.scaleIndex]-this.scaleEl[this.scaleIndex].border.width/2:this.scaleStartY[this.scaleIndex]-this.scaleEl[this.scaleIndex].border.width-5,this.contextEl.textAlign=this.Model.orientation=="Vertical"?"right":"center"):n.placement=="far"?(i=this.Model.orientation=="Vertical"?this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width+this.scaleEl[this.scaleIndex].border.width/2:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width+this.scaleEl[this.scaleIndex].border.width+5,this.contextEl.textAlign=this.Model.orientation=="Vertical"?"left":"center"):(this.contextEl.textAlign="center",i=this.Model.orientation=="Vertical"?this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2+this.scaleEl[this.scaleIndex].border.width/2:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2+this.scaleEl[this.scaleIndex].border.width/2),t=this.scaleEl[this.scaleIndex].minimum;t<=this.scaleEl[this.scaleIndex].maximum;t=this._addDecimal(t,r))(r==this.scaleEl[this.scaleIndex].minorIntervalValue&&t%this.scaleEl[this.scaleIndex].majorIntervalValue!=0||r==this.scaleEl[this.scaleIndex].majorIntervalValue)&&(e=this.scaleEl[this.scaleIndex].direction=="counterclockwise"?this._getCounterClockwiseLinePosition(t):this._getClockwiseLinePosition(t),this.labelValue=t,this.region={lineChangePosition:e+f,lineStaticPosition:i+u},this.style={angle:this.Model.orientation=="Vertical"?n.angle:n.angle+270,fillStyle:n.textColor?n.textColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.textColor):this.Model.labelColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,this.Model.labelColor),opacity:isNaN(n.opacity)?1:n.opacity,font:this._getFontString(this,n.font),textValue:this.labelValue},this.style.textValue=this.labelValue=o&&this.model.enableGroupSeparator?this.labelValue.toLocaleString(o):this.labelValue,this.Model.drawLabels&&this._onDrawLabels(this.Model.orientation=="Vertical"?n.angle:n.angle+270),this._drawLabel(this.region,this.style,!1));this.contextEl.getImageData&&(this.labelImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height))},_drawLabel:function(n,i,r){if(this.contextEl.beginPath(),this.contextEl.save(),this.contextEl.textBaseline="middle",this.contextEl.fillStyle=i.fillStyle,this.contextEl.font=i.font,i.opacity&&(this.contextEl.globalAlpha=i.opacity),this.Model.orientation=="Vertical"?(this.contextEl.translate(n.lineStaticPosition,n.lineChangePosition),this.model.tooltip.showLabelTooltip&&!r&&this._savedPoints.push({startX:n.lineStaticPosition+5,startY:n.lineChangePosition+10,width:15,height:15,value:i.textValue}),this.model.tooltip.showCustomLabelTooltip&&r&&this._savedPoints.push({startX:n.lineStaticPosition-35,startY:n.lineChangePosition+10,width:110,height:15,value:i.textValue})):(this.contextEl.translate(n.lineChangePosition,n.lineStaticPosition),this.model.tooltip.showLabelTooltip&&!r&&this._savedPoints.push({startX:n.lineChangePosition+10,startY:n.lineStaticPosition+10,width:15,height:15,value:i.textValue}),this.model.tooltip.showCustomLabelTooltip&&r&&this._savedPoints.push({startX:n.lineChangePosition-35,startY:n.lineStaticPosition+10,width:110,height:15,value:i.textValue})),this.contextEl.lineTo(0,0),this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this.contextEl.rotate(Math.PI*(i.angle/180)):this.contextEl.rotate(-(Math.PI*(i.angle/180))),!t.isNullOrUndefined(r)&&!r){var u=this.model.scales[this.scaleIndex].labels[this.labelIndex].unitTextPlacement;t.isNullOrUndefined(u)||u.toString()!="back"?t.isNullOrUndefined(u)||u.toString()!="front"||(i.textValue=this.model.scales[this.scaleIndex].labels[this.labelIndex].unitText+i.textValue):i.textValue=i.textValue+this.model.scales[this.scaleIndex].labels[this.labelIndex].unitText}this.contextEl.fillText(i.textValue,0,0);this.contextEl.fill();this.contextEl.restore()},_drawScaleBarPointer:function(n,t){n.value=n.value>this.scaleEl[this.scaleIndex].maximum?this.scaleEl[this.scaleIndex].maximum:n.value;n.value=n.value<this.scaleEl[this.scaleIndex].minimum?this.scaleEl[this.scaleIndex].minimum:n.value;var u,r,s,f,e,o,c,i,h;h=[{ColorStop:0,Color:this.Model.pointerGradient1=="transparent"?"rgba(0,0,0,0)":this.Model.pointerGradient1},{ColorStop:1,Color:this.Model.pointerGradient2=="transparent"?"rgba(0,0,0,0)":this.Model.pointerGradient2}];s=this.scaleEl[this.scaleIndex].type.toLowerCase()=="roundedrectangle"?5:0;this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?(r=this._getClockwiseLinePosition(n.value),this.scaleEl[this.scaleIndex].type=="thermometer"&&this.isScaleModified&&(this._restoreWidth(),this.isModify=!0),this.Model.orientation=="Vertical"?(i=this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2+n.distanceFromScale,u=this.contextEl.createLinearGradient(i,this.scaleStartY[this.scaleIndex],i+n.width,this.scaleStartY[this.scaleIndex])):(i=this.scaleStartX[this.scaleIndex],u=this.contextEl.createLinearGradient(i,this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2,i,this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2+n.width/2))):(r=this._getCounterClockwiseLinePosition(n.value),this.scaleEl[this.scaleIndex].type=="thermometer"&&this.isScaleModified&&(this._restoreWidth(),this.isModify=!0),this.Model.orientation=="Vertical"?(i=this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2+n.distanceFromScale,u=this.contextEl.createLinearGradient(i,this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].length-r,i+n.width,this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].length-r)):(i=this.scaleEl[this.scaleIndex].type=="thermometer"?r-this.scaleEl[this.scaleIndex].width/2-this.scaleEl[this.scaleIndex].border.width/2:r-this.scaleEl[this.scaleIndex].border.width/2,u=this.contextEl.createLinearGradient(i,this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2,i,this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2+n.width/2)));n.backgroundColor?c=n.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.backgroundColor):n.gradients?this._setGradientColor(this,u,n.gradients.colorInfo):this.Model.ScaleInterior?this._setGradientColor(this,u,this.Model.ScaleInterior.colorInfo):this._setGradientColor(this,u,h);this.Model.orientation=="Vertical"?(o=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].border.width/2:r,this.scaleEl[this.scaleIndex].direction=="counterclockwise"&&this.scaleEl[this.scaleIndex].type=="thermometer"&&(o=o-this.scaleEl[this.scaleIndex].width/2),f=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?r-this.scaleStartY[this.scaleIndex]:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].length-r-this.scaleEl[this.scaleIndex].border.width/2,e=n.width):(o=this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2+this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale,f=n.width,e=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?r-this.scaleStartX:this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].length-r);this.region={startX:i+this.scaleEl[this.scaleIndex].border.width/2,startY:o};this.style={width:this.scaleEl[this.scaleIndex].type=="thermometer"&&this.Model.orientation=="horizontal"?e+f/2-this.scaleEl[this.scaleIndex].border.width/2:e,lineWidth:n.border.width,radius:s,topRounded:!1,isStroke:!1,isFill:!0,height:this.scaleEl[this.scaleIndex].type=="thermometer"&&this.Model.orientation=="Vertical"?f+e/2:f,strokeStyle:n.border.color==null?this.Model.borderColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,this.Model.borderColor):n.border.color=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.border.color),fillStyle:n.backgroundColor?n.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.backgroundColor):u=="transparent"?"rgba(0,0,0,0)":this._getColor(n,u),opacity:isNaN(n.opacity)?.4:n.opacity,calDistance:this.scaleEl[this.scaleIndex].type=="thermometer"&&this.Model.orientation=="Vertical"?(this.scaleEl[this.scaleIndex].width-this.barPointerEl[this.barPointerIndex].width)/2:0};this.value(n.value);this._notifyArrayChange&&this._notifyArrayChange("scales["+this.scaleIndex+"]barpointers["+t+"]value",n.value);this.Model.drawBarPointers&&this._onDrawBarPointers(n.value);this._drawFrame(this.scaleEl[this.scaleIndex].type,this.region,this.style);this.contextEl.getImageData&&(this.barPointerImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height))},_drawMarkerPointer:function(n,t){n.value=n.value>this.scaleEl[this.scaleIndex].maximum?this.scaleEl[this.scaleIndex].maximum:n.value;n.value=n.value<this.scaleEl[this.scaleIndex].minimum?this.scaleEl[this.scaleIndex].minimum:n.value;var i,f,o,e,r,u,s,h=[{ColorStop:0,Color:this.Model.pointerGradient1=="transparent"?"rgba(0,0,0,0)":this.Model.pointerGradient1},{ColorStop:1,Color:this.Model.pointerGradient2=="transparent"?"rgba(0,0,0,0)":this.Model.pointerGradient2}];this.markerPlacement=n.placement;o=Math.sqrt(n.width*n.width+n.length*n.length)/2;this.scaleEl[this.scaleIndex].type=="thermometer"&&this.isModify&&this._modifyWidth();this.Model.orientation=="Vertical"?(this.markerPlacement=="far"&&(i=this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width+this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale,r=0),this.markerPlacement=="near"&&(i=n.type=="star"?this.scaleStartX[this.scaleIndex]+n.distanceFromScale-n.width:this.scaleStartX[this.scaleIndex]+n.distanceFromScale,r=180),this.markerPlacement=="center"&&(i=n.type=="circle"?this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2+n.distanceFromScale+n.border.width:this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2+n.distanceFromScale,r=0)):(this.markerPlacement=="far"&&(i=this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width+this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale,r=90),this.markerPlacement=="near"&&(i=n.type=="star"?this.scaleStartY[this.scaleIndex]-this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale-n.length:this.scaleStartY[this.scaleIndex]-this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale,r=270),this.markerPlacement=="center"&&(i=n.type=="circle"?this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.length/2+n.distanceFromScale+n.border.width:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.length/2+n.distanceFromScale,r=90));e=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this._getClockwiseLinePosition(n.value):this._getCounterClockwiseLinePosition(n.value);n.type=="star"?this.Model.orientation=="Vertical"?(u=this.contextEl.createLinearGradient(i,this.scaleStartY[this.scaleIndex],i+n.width,this.scaleStartY[this.scaleIndex]),f=e-n.length/3):(u=this.contextEl.createLinearGradient(e,i,e,i+n.length),f=e-n.width/2):(u=this.contextEl.createLinearGradient(0,0,n.width,0),f=e);n.type.toLowerCase()=="roundedrectangle"&&(this.Model.orientation=="Vertical"&&this.markerPlacement=="near"?f+=n.length:this.Model.orientation=="horizontal"&&(this.markerPlacement=="near"&&(f-=n.width),i+=n.width/2));n.backgroundColor?s=n.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.backgroundColor):n.gradients?this._setGradientColor(this,u,n.gradients.colorInfo):this.Model.PointerInterior?this._setGradientColor(this,u,this.Model.PointerInterior.colorInfo):this._setGradientColor(this,u,h);this.region={startX:this.Model.orientation=="Vertical"?i:f,startY:this.Model.orientation=="Vertical"?f:i};this.style={width:n.width,radius:n.type=="rectangle"?0:o,height:n.length,lineWidth:n.border.width,isFill:!0,isStroke:!0,angle:r,strokeStyle:n.border.color=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.border.color),markerPlacement:this.markerPlacement,opacity:isNaN(n.opacity)?.4:n.opacity,fillStyle:n.backgroundColor?n.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.backgroundColor):u=="transparent"?"rgba(0,0,0,0)":this._getColor(n,u)};this.value(n.value);this._notifyArrayChange&&this._notifyArrayChange("scales["+this.scaleIndex+"]markerpointers["+t+"]value",n.value);this.Model.drawMarkerPointers&&this._onDrawMarkerPointers(r,n.value);n.type.toLowerCase()=="roundedrectangle"&&(this.style.radius=5);this._drawMarkerType(n.type,this.region,this.style);this.scaleEl[this.scaleIndex].type=="thermometer"&&this.isModify&&(this._restoreWidth(),this.isScaleModified=!1);this.contextEl.getImageData&&(this.markerPointerImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height))},_drawMarkerType:function(n,t,i){switch(n){case"rectangle":this._drawRectangle(t,i,this);break;case"triangle":this._drawTriangle(t,i,this);break;case"ellipse":this._drawEllipse(t,i,this);break;case"diamond":this._drawDiamond(t,i,this);break;case"pentagon":this._drawPentagon(t,i,this);break;case"circle":this._drawCircle(t,i,this);break;case"slider":this._drawSlider(t,i,this);break;case"star":this._drawStar(t,i,this);break;case"pointer":this._drawPointer(t,i,this);break;case"wedge":this._drawWedge(t,i,this);break;case"trapezoid":this._drawTrapezoid(t,i,this);break;case"roundedrectangle":this._drawRoundedRectangle(t,i,this)}},_drawRange:function(n){var e,o,u,t,f,r,i,s,h;n.startValue<this.scaleEl[this.scaleIndex].maximum&&n.endValue>this.scaleEl[this.scaleIndex].minimum&&this.scaleEl[this.scaleIndex].minimum<this.scaleEl[this.scaleIndex].maximum&&n.endValue<=this.scaleEl[this.scaleIndex].maximum&&(e=n.startValue<this.scaleEl[this.scaleIndex].minimum?this.scaleEl[this.scaleIndex].minimum:n.startValue,o=n.endValue>this.scaleEl[this.scaleIndex].maximum?this.scaleEl[this.scaleIndex].maximum:n.endValue,this.rangePosition=n.placement,h=[{ColorStop:0,Color:this.Model.pointerGradient1=="transparent"?"rgba(0,0,0,0)":this.Model.pointerGradient1},{ColorStop:1,Color:this.Model.pointerGradient2=="transparent"?"rgba(0,0,0,0)":this.Model.pointerGradient2}],u=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this._getClockwiseLinePosition(e):this._getCounterClockwiseLinePosition(e),t=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this._getClockwiseLinePosition(o):this._getCounterClockwiseLinePosition(o),this.Model.orientation=="Vertical"?(n.placement=="far"&&(f=this.scaleStartX[this.scaleIndex]+n.distanceFromScale+this.scaleEl[this.scaleIndex].width+this.scaleEl[this.scaleIndex].border.width),n.placement=="near"&&(f=this.scaleStartX[this.scaleIndex]+n.distanceFromScale),n.placement=="center"&&(f=n.startWidth>n.endWidth?this.scaleStartX[this.scaleIndex]+n.distanceFromScale+this.scaleEl[this.scaleIndex].width/2-n.startWidth/2:this.scaleStartX[this.scaleIndex]+n.distanceFromScale+this.scaleEl[this.scaleIndex].width/2-n.endWidth/2),i=this.contextEl.createLinearGradient(t,t,t,u),this.region={startX:f,startY:u,endY:t}):(n.placement=="far"&&(r=this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width+n.distanceFromScale+this.scaleEl[this.scaleIndex].border.width),n.placement=="near"&&(r=this.scaleStartY[this.scaleIndex]+n.distanceFromScale),n.placement=="center"&&(r=n.startWidth>n.endWidth?this.scaleStartY[this.scaleIndex]+n.distanceFromScale+this.scaleEl[this.scaleIndex].width/2-n.startWidth/2:this.scaleStartY[this.scaleIndex]+n.distanceFromScale+this.scaleEl[this.scaleIndex].width/2-n.endWidth/2),i=this.contextEl.createLinearGradient(t,r,u,r),this.region={startX:u,startY:r,endX:t}),n.backgroundColor?s=n.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.backgroundColor):n.gradients?this._setGradientColor(this,i,n.gradients.colorInfo):this.Model.RangeInterior?this._setGradientColor(this,i,this.Model.RangeInterior.colorInfo):this._setGradientColor(this,i,h),this.style={startWidth:n.startWidth,lineWidth:n.border.width,isStroke:!0,isFill:!0,opacity:isNaN(n.opacity)?.4:n.opacity,endWidth:n.endWidth,fillStyle:n.backgroundColor?s=="transparent"?"rgba(0,0,0,0)":this._getColor(n,s):i=="transparent"?"rgba(0,0,0,0)":this._getColor(n,i),strokeStyle:n.border.color?n.border.color=="transparent"?"rgba(0,0,0,0)":this._getColor(n,n.border.color):this.Model.borderColor=="transparent"?"rgba(0,0,0,0)":this._getColor(n,this.Model.borderColor)},this.Model.drawRange&&this._onDrawRange(),this._drawRangeBar(this.region,this.style),this.contextEl.getImageData&&(this.rangeImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height)))},_drawRangeBar:function(n,t){this._contextOpenPath(t,this);this.Model.orientation=="Vertical"?(this.contextEl.lineTo(n.startX,n.startY),this.contextEl.lineTo(n.startX,n.endY),this.rangePosition=="near"?(this.contextEl.lineTo(n.startX-t.endWidth,n.endY),this.contextEl.lineTo(n.startX-t.startWidth,n.startY)):(this.contextEl.lineTo(n.startX+t.endWidth,n.endY),this.contextEl.lineTo(n.startX+t.startWidth,n.startY))):(this.contextEl.lineTo(n.startX,n.startY),this.contextEl.lineTo(n.endX,n.startY),this.rangePosition=="near"?(this.contextEl.lineTo(n.endX,n.startY-t.endWidth),this.contextEl.lineTo(n.startX,n.startY-t.startWidth)):(this.contextEl.lineTo(n.endX,n.startY+t.endWidth),this.contextEl.lineTo(n.startX,n.startY+t.startWidth)));this._contextClosePath(t,this)},_setCustomLabelCordinates:function(n,t){this._customLblMaxSize=this._customLblMaxSize<parseFloat(t.font.size.match(/\d+/)[0])?parseFloat(t.font.size.match(/\d+/)[0]):this._customLblMaxSize;var i,r;this.contextEl.textAlign="center";this.Model.orientation=="Vertical"?(i=this.bounds.width*(t.position.x/100),r=this.bounds.height*(t.position.y/100)):(i=this.bounds.width*(t.position.x/100),r=this.bounds.height*(t.position.y/100));this.region={lineStaticPosition:this.Model.orientation=="Vertical"?i:r,lineChangePosition:this.Model.orientation=="Vertical"?r:i};this.style={angle:t.textAngle,textValue:t.value,fillStyle:t.color?t.color=="transparent"?"rgba(0,0,0,0)":this._getColor(t,t.color):this.Model.labelColor=="transparent"?"rgba(0,0,0,0)":this._getColor(t,this.Model.labelColor),font:this._getFontString(this,t.font)};this.Model.drawCustomLabel&&this._onDrawCustomLabel();this._drawLabel(this.region,this.style,!0);this.contextEl.getImageData&&(this.customLabelImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height))},_drawIndicator:function(i,r){var u=this,f,e,o,s=!1;f=(this.bounds.width-2*r.width)*(r.position.x/100);e=(this.bounds.height-2*r.height)*(r.position.y/100);o={x:this.bounds.width*(r.textLocation.x/100),y:this.bounds.height*(r.textLocation.y/100)};u.region={startX:r.type=="circle"?f+r.width:f,textLocation:o,startY:r.type=="circle"?e+r.height:e,startAngle:0,endAngle:2*Math.PI};u.style={radius:r.type=="roundedrectangle"?2:0,strokeStyle:r.border.color?r.border.color=="transparent"?"rgba(0,0,0,0)":this._getColor(r,r.border.color):this._getColor(r,"#FFFFFF"),angle:0,circleRadius:(r.height+r.width)/2,height:r.height,width:r.width,lineWidth:r.border.width,fillStyle:r.backgroundColor?r.backgroundColor=="transparent"?"rgba(0,0,0,0)":this._getColor(r,r.backgroundColor):this._getColor(r,"#FFFFFF"),isStroke:!0,isFill:!0,indicatorText:null,textColor:null,font:null,counterClockwise:!1};this.Model.drawIndicators&&this._onDrawIndicators(u.style,u.region);r.stateRanges!=null&&n.each(r.stateRanges,function(n,i){u.markerPointerEl[u.markerPointerIndex].value>=i.startValue&&u.markerPointerEl[u.markerPointerIndex].value<=i.endValue&&(s=!0,!t.isNullOrUndefined(i.text)&&i.text.length>0&&(u.style.indicatorText=i.text,u.style.textColor=i.textColor=="transparent"?"rgba(0,0,0,0)":u._getColor(r,i.textColor),u.style.font=u._getFontString(u,r.font)),r.type!="text"?(u.style.strokeStyle=i.borderColor=="transparent"?"rgba(0,0,0,0)":u._getColor(r,i.borderColor),u.style.fillStyle=i.backgroundColor=="transparent"?"rgba(0,0,0,0)":u._getColor(r,i.backgroundColor),u._drawFrame(r.type,u.region,u.style,u)):r.type=="text"&&u._drawText(u.region,u.style))});s||r.type=="text"||this._drawFrame(r.type,u.region,u.style,u);this.contextEl.getImageData&&(this.indicatorImage=this.contextEl.getImageData(0,0,this.Model.width,this.Model.height))},_drawFrame:function(n,t,i){switch(n.toLowerCase()){case"circle":this._drawFrameCircle(t,i,this);break;case"rectangle":this._drawFrameRectangle(t,i,this);break;case"roundedrectangle":this._drawFrameRoundedRectangle(t,i,this);break;case"thermometer":this._drawFrameThermometer(t,i,this)}},_drawText:function(n,t){this.contextEl.beginPath();this.contextEl.textAlign="center";this.contextEl.fillStyle=t.textColor=="transparent"?"rgba(0,0,0,0)":t.textColor;this.contextEl.font=t.font;this.contextEl.fillText(t.indicatorText,n.textLocation.x,n.textLocation.y);this.contextEl.closePath()},_drawTriangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);this._contextClosePath(t,i)},_drawPointer:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(t.width,t.height/4);i.contextEl.lineTo(t.width,-t.height/4);i.contextEl.lineTo(t.width/2,-t.height/4);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/2,t.height/2);i.contextEl.lineTo(t.width/2,t.height/4);this._contextClosePath(t,i)},_drawWedge:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(3*t.width/4,0);i.contextEl.lineTo(t.width,t.height/2);this._contextClosePath(t,i)},_drawSlider:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/4,-t.height/2);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);i.contextEl.lineTo(t.width/4,t.height/2);this._contextClosePath(t,i)},_drawStar:function(n,t,i){this._contextOpenPath(t,i);i.Model.orientation=="horizontal"&&i.markerPlacement=="near"?(i.contextEl.lineTo(n.startX+t.width-t.width/6,n.startY),i.contextEl.lineTo(n.startX,n.startY+t.height-t.height/3),i.contextEl.lineTo(n.startX+t.width,n.startY+t.height-t.height/3),i.contextEl.lineTo(n.startX+t.width/6,n.startY),i.contextEl.lineTo(n.startX+t.width/2,n.startY+t.height)):(i.contextEl.lineTo(n.startX+t.width/6,n.startY+t.height),i.contextEl.lineTo(n.startX+t.width,n.startY+t.height/3),i.contextEl.lineTo(n.startX,n.startY+t.height/3),i.contextEl.lineTo(n.startX+t.width-t.width/6,n.startY+t.height),i.contextEl.lineTo(n.startX+t.width/2,n.startY));this._contextClosePath(t,i)},_drawPentagon:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/3,-t.height/2);i.contextEl.lineTo(t.width,-t.height/4);i.contextEl.lineTo(t.width,t.height/4);i.contextEl.lineTo(t.width/3,t.height/2);this._contextClosePath(t,i)},_drawDiamond:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(t.width/2,-t.height/2);i.contextEl.lineTo(t.width,0);i.contextEl.lineTo(t.width/2,t.height/2);i.contextEl.lineTo(0,0);this._contextClosePath(t,i)},_drawCircle:function(n,t,i){var r=Math.sqrt(t.height*t.height+t.width*t.width)/2;t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.arc(r/2,0,r/2,0,Math.PI*2,!0);this._contextClosePath(t,i)},_drawTrapezoid:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(0,-t.height/4);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);i.contextEl.lineTo(0,t.height/4);this._contextClosePath(t,i)},_drawRectangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.lineTo(0,0);i.contextEl.lineTo(0,-t.height/2);i.contextEl.lineTo(t.width,-t.height/2);i.contextEl.lineTo(t.width,t.height/2);i.contextEl.lineTo(0,t.height/2);this._contextClosePath(t,i)},_drawRoundedRectangle:function(n,t,i){t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY-t.height/2);this._setContextRotation(t,i);i.contextEl.lineTo(t.radius,0);i.contextEl.lineTo(t.width-t.radius,0);i.contextEl.quadraticCurveTo(t.width,0,t.width,t.radius);i.contextEl.lineTo(t.width,t.height-t.radius);i.contextEl.quadraticCurveTo(t.width,t.height,t.width-t.radius,t.height);i.contextEl.lineTo(t.radius,t.height);i.contextEl.quadraticCurveTo(0,t.height,0,t.height-t.radius);i.contextEl.lineTo(0,t.radius);i.contextEl.quadraticCurveTo(0,0,t.radius,0);this._contextClosePath(t,i)},_drawCustomImage:function(t,i){var r=new Image;n(r).on("load",function(){t.contextEl.drawImage(this,0,0,t.Model.width,t.Model.height);t.Model.scales!=null&&t._drawScales();t.Model.Items!=null&&t._renderItems()}).attr("src",i)},_drawEllipse:function(n,t,i){var r=Math.sqrt(t.height*t.height+t.width*t.width)/2;t=this._setPointerDimension(t,i);this._contextOpenPath(t,i);i.contextEl.translate(n.startX,n.startY);this._setContextRotation(t,i);i.contextEl.scale(2,1);i.contextEl.arc(r/2,0,r/2,0,Math.PI*2,!0);this._contextClosePath(t,i)},_getIndicatorImage:function(){return this.pointerImage?this.pointerImage:this._getMarkerPointerImage()},_getBarPointerImage:function(){return this.customLabelImage?this.customLabelImage:this._getCustomLabelImage()},_getMarkerPointerImage:function(){return this.barPointerImage?this.barPointerImage:this._getCustomLabelImage()},_getCustomLabelImage:function(){return this.rangeImage?this.rangeImage:this._getRangeImage()},_getRangeImage:function(){return this.labelImage?this.labelImage:this._getLabelImage()},_getLabelImage:function(){return this.tickImage?this.tickImage:this._getTickImage()},_getTickImage:function(){return this.scaleImage?this.scaleImage:this.outerImage},setPointerValue:function(n,i,r){n<this.Model.scales.length&&i<this.Model.scales[n].markerPointers.length&&r!=null&&(r<=this.scaleEl[n].maximum&&r>=this.scaleEl[n].minimum&&(this.scaleEl[n].markerPointers[i].value=r),this.contextEl.putImageData?(this.contextEl.putImageData(this._getMarkerPointerImage(),0,0),!t.isNullOrUndefined(this.outerDiv)&&this.model.scale[n].showCustomLabels&&this.outerDiv.empty(),this._setCustomLabel(),this._setMarkerPointers(),this._setIndicators()):this.initialize())},getPointerValue:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length?this.scaleEl[n].markerPointers[t].value:null},setPointerWidth:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length&&i!=null&&(this.scaleEl[n].markerPointers[t].width=i,this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():(this.contextEl.putImageData(this._getMarkerPointerImage(),0,0),this._setMarkerPointers()):this.initialize())},getPointerWidth:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length?this.scaleEl[n].markerPointers[t].width:null},setPointerHeight:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length&&i!=null&&(this.scaleEl[n].markerPointers[t].length=i,this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():(this.contextEl.putImageData(this._getMarkerPointerImage(),0,0),this._setMarkerPointers()):this.initialize())},getPointerHeight:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length?this.scaleEl[n].markerPointers[t].length:null},_getColor:function(n,t){return typeof t=="string"?t:"rgba("+t.r+", "+t.g+","+t.b+", "+t.a/255+")"},setPointerDistanceFromScale:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length&&i!=null&&(this.scaleEl[n].markerPointers[t].distanceFromScale=i,this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():(this.contextEl.putImageData(this._getMarkerPointerImage(),0,0),this._setMarkerPointers()):this.initialize())},getPointerDistanceFromScale:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length?this.scaleEl[n].markerPointers[t].distanceFromScale:null},setPointerPlacement:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length&&i!=null&&(this.scaleEl[n].markerPointers[t].placement=i,this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():(this.contextEl.putImageData(this._getMarkerPointerImage(),0,0),this._setMarkerPointers()):this.initialize())},getPointerPlacement:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length?this.scaleEl[n].markerPointers[t].placement:null},setMarkerStyle:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length&&i!=null&&(this.scaleEl[n].markerPointers[t].type=i,this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():(this.contextEl.putImageData(this._getMarkerPointerImage(),0,0),this._setMarkerPointers()):this.initialize())},getMarkerStyle:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].markerPointers.length?this.scaleEl[n].markerPointers[t].type:null},setBarPointerValue:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].barPointers.length&&i!=null&&(i<=this.scaleEl[n].maximum&&i>=this.scaleEl[n].minimum&&(this.scaleEl[n].barPointers[t].value=i),this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():this._reDrawBarPointer():this.initialize())},getBarPointerValue:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].barPointers.length?this.scaleEl[n].barPointers[t].value:null},setBarWidth:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].barPointers.length&&i!=null&&(this.scaleEl[n].barPointers[t].width=i,this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():this._reDrawBarPointer():this.initialize())},getBarWidth:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].barPointers.length?this.scaleEl[n].barPointers[t].width:null},setBarDistanceFromScale:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].barPointers.length&&i!=null&&(this.scaleEl[n].barPointers[t].distanceFromScale=i,this.contextEl.putImageData?this.scaleEl[this.scaleIndex].type=="thermometer"?this.initialize():this._reDrawBarPointer():this.initialize())},getBarDistanceFromScale:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].barPointers.length?this.scaleEl[n].barPointers[t].distanceFromScale:null},setCustomLabelValue:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].customLabels.length&&i!=null&&(this.scaleEl[n].customLabels[t].value=i,this.contextEl.putImageData?this._reDrawCustomLabel():this.initialize())},getCustomLabelValue:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].customLabels.length?this.scaleEl[n].customLabels[t].value:null},setCustomLabelAngle:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].customLabels.length&&i!=null&&(this.scaleEl[n].customLabels[t].textAngle=i,this.contextEl.putImageData?this._reDrawCustomLabel():this.initialize())},getCustomLabelAngle:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].customLabels.length?this.scaleEl[n].customLabels[t].textAngle:null},setRangeStartValue:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].startValue=i,this.contextEl.putImageData?this._reDrawRange():this.initialize())},getRangeStartValue:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length?this.scaleEl[n].ranges[t].startValue:null},setRangeEndValue:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].endValue=i,this.contextEl.putImageData?this._reDrawRange():this.initialize())},getRangeEndValue:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length?this.scaleEl[n].ranges[t].endValue:null},setRangeStartWidth:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].startWidth=i,this.contextEl.putImageData?this._reDrawRange():this.initialize())},getRangeStartWidth:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length?this.scaleEl[n].ranges[t].startWidth:null},setRangeEndWidth:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].endWidth=i,this.contextEl.putImageData?this._reDrawRange():this.initialize())},getRangeEndWidth:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length?this.scaleEl[n].ranges[t].endWidth:null},setRangeDistanceFromScale:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].distanceFromScale=i,this.contextEl.putImageData?this._reDrawRange():this.initialize())},getRangeDistanceFromScale:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length?this.scaleEl[n].ranges[t].distanceFromScale:null},setRangePosition:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].placement=i,this.contextEl.putImageData?this._reDrawRange():this.initialize())},getRangePosition:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length?this.scaleEl[n].ranges[t].placement:null},setRangeBorderWidth:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length&&i!=null&&(this.scaleEl[n].ranges[t].border.width=i,this.contextEl.putImageData?this._reDrawRange():this.initialize())},getRangeBorderWidth:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ranges.length?this.scaleEl[n].ranges[t].border.width:null},setLabelAngle:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].angle=i,this.contextEl.putImageData?this._reDrawLabel():this.initialize())},getLabelAngle:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].labels.length?this.scaleEl[n].labels[t].angle:null},setLabelStyle:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].type=i,this.contextEl.putImageData?this._reDrawLabel():this.initialize())},getLabelStyle:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].labels.length?this.scaleEl[n].labels[t].type:null},setLabelPlacement:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].placement=i,this.contextEl.putImageData?this._reDrawLabel():this.initialize())},getLabelPlacement:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].labels.length?this.scaleEl[n].labels[t].placement:null},setLabelXDistanceFromScale:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].distanceFromScale.x=i,this.contextEl.putImageData?this._reDrawLabel():this.initialize())},getLabelXDistanceFromScale:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].labels.length?this.scaleEl[n].labels[t].distanceFromScale.x:null},setLabelYDistanceFromScale:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].labels.length&&i!=null&&(this.scaleEl[n].labels[t].distanceFromScale.y=i,this.contextEl.putImageData?this._reDrawLabel():this.initialize())},getLabelYDistanceFromScale:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].labels.length?this.scaleEl[n].labels[t].distanceFromScale.y:null},setTickAngle:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].angle=i,this.contextEl.putImageData?this._reDrawTickMark():this.initialize())},getTickAngle:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length?this.scaleEl[n].ticks[t].angle:null},setTickWidth:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].width=i,this.contextEl.putImageData?this._reDrawTickMark():this.initialize())},getTickWidth:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length?this.scaleEl[n].ticks[t].width:null},setTickHeight:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].height=i,this.contextEl.putImageData?this._reDrawTickMark():this.initialize())},getTickHeight:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length?this.scaleEl[n].ticks[t].height:null},setTickStyle:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].type=i,this.contextEl.putImageData?this._reDrawTickMark():this.initialize())},getTickStyle:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length?this.scaleEl[n].ticks[t].type:null},setTickPlacement:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].placement=i,this.contextEl.putImageData?this._reDrawTickMark():this.initialize())},getTickPlacement:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length?this.scaleEl[n].ticks[t].placement:null},setTickXDistanceFromScale:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].distanceFromScale.x=i,this.contextEl.putImageData?this._reDrawTickMark():this.initialize())},getTickXDistanceFromScale:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length?this.scaleEl[n].ticks[t].distanceFromScale.x:null},setTickYDistanceFromScale:function(n,t,i){n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length&&i!=null&&(this.scaleEl[n].ticks[t].distanceFromScale.y=i,this.contextEl.putImageData?this._reDrawTickMark():this.initialize())},getTickYDistanceFromScale:function(n,t){return n<this.Model.scales.length&&t<this.Model.scales[n].ticks.length?this.scaleEl[n].ticks[t].distanceFromScale.y:null},setScaleLocation:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].position.x=t.x,this.scaleEl[n].position.y=t.y,this.initialize())},getScaleLocation:function(n){return n<this.Model.scales.length?{x:this.scaleEl[n].position.x,y:this.scaleEl[n].position.y}:null},setMaximumValue:function(n,t){n<this.Model.scales.length&&t!=null&&(t>this.scaleEl[n].minimum&&(this.scaleEl[n].maximum=t),this.initialize())},getMaximumValue:function(n){return n<this.Model.scales.length?this.scaleEl[this.scaleIndex].maximum:null},setMinimumValue:function(n,t){n<this.Model.scales.length&&t!=null&&(t<this.scaleEl[n].maximum&&(this.scaleEl[n].minimum=t),this.initialize())},getMinimumValue:function(n){return n<this.Model.scales.length?this.scaleEl[this.scaleIndex].minimum:null},setScaleBarSize:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].width=t,this.initialize())},getScaleBarSize:function(n){return n<this.Model.scales.length?this.scaleEl[n].width:null},setScaleBarLength:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].length=t,this.initialize())},setScaleStyle:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].type=t,this.initialize())},getScaleStyle:function(n){return n<this.Model.scales.length?this.scaleEl[n].type:null},getScaleBarLength:function(n){return n<this.Model.scales.length?this.scaleEl[n].length:null},setScaleBorderWidth:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].border.width=t,this.initialize())},setScaleDirection:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].direction=t,this.initialize())},getScaleDirection:function(n){return n<this.Model.scales.length?this.scaleEl[n].direction:null},getScaleBorderWidth:function(n){return n<this.Model.scales.length?this.scaleEl[n].border.width:null},setMajorIntervalValue:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].majorIntervalValue=t,this.initialize())},getMajorIntervalValue:function(n){return n<this.Model.scales.length?this.scaleEl[n].majorIntervalValue:null},setMinorIntervalValue:function(n,t){n<this.Model.scales.length&&t!=null&&(this.scaleEl[n].minorIntervalValue=t,this.initialize())},getMinorIntervalValue:function(n){return n<this.Model.scales.length?this.scaleEl[n].minorIntervalValue:null},_reDrawBarPointer:function(){if(this.Model.frame.backgroundImageUrl){var n=t.isNullOrUndefined(this.customLabelImage)?t.isNullOrUndefined(this.rangeImage)?t.isNullOrUndefined(this.labelImage)?t.isNullOrUndefined(this.tickImage)?t.isNullOrUndefined(this.scaleImage)?null:this.scaleImage:this.tickImage:this.labelImage:this.rangeImage:this.customLabelImage();this.contextEl.putImageData(n,0,0);this._setBarPointers();this._setMarkerPointers();this._setIndicators()}else this.contextEl.putImageData!="undefined"&&(this.contextEl.putImageData(this._getBarPointerImage(),0,0),this._setBarPointers(),this._setMarkerPointers(),this._setIndicators())},_reDrawMarkerPointer:function(){if(this.Model.frame.backgroundImageUrl){var n=t.isNullOrUndefined(this.customLabelImage)?t.isNullOrUndefined(this.rangeImage)?t.isNullOrUndefined(this.labelImage)?t.isNullOrUndefined(this.tickImage)?t.isNullOrUndefined(this.scaleImage)?null:this.scaleImage:this.tickImage:this.labelImage:this.rangeImage:this.customLabelImage();this.contextEl.putImageData(n,0,0);this._setMarkerPointers()}else this.contextEl.putImageData!="undefined"&&(this.contextEl.putImageData(this._getMarkerPointerImage(),0,0),this._setMarkerPointers())},_reDrawCustomLabel:function(){if(this.Model.frame.backgroundImageUrl){var n=t.isNullOrUndefined(this.rangeImage)?t.isNullOrUndefined(this.labelImage)?t.isNullOrUndefined(this.tickImage)?t.isNullOrUndefined(this.scaleImage)?null:this.scaleImage:this.tickImage:this.labelImage:this.rangeImage;this.contextEl.putImageData(n,0,0);this._setCustomLabel();this._setIndicators();this._setBarPointers();this._setMarkerPointers()}else this.contextEl.putImageData(this._getCustomLabelImage(),0,0),this._setCustomLabel(),this._setIndicators(),this._setBarPointers(),this._setMarkerPointers()},_reDrawRange:function(){if(this.Model.frame.backgroundImageUrl){var n=t.isNullOrUndefined(this.labelImage)?t.isNullOrUndefined(this.tickImage)?t.isNullOrUndefined(this.scaleImage)?null:this.scaleImage:this.tickImage:this.labelImage;this.contextEl.putImageData(n,0,0);this._setRange();this._setCustomLabel();this._setIndicators();this._setBarPointers();this._setMarkerPointers()}else this.contextEl.putImageData(this._getRangeImage(),0,0),this._setRange(),this._setCustomLabel(),this._setIndicators(),this._setBarPointers(),this._setMarkerPointers()},_reDrawLabel:function(){if(this.Model.frame.backgroundImageUrl){var n=t.isNullOrUndefined(this.tickImage)?t.isNullOrUndefined(this.scaleImage)?null:this.scaleImage:this.tickImage;this.contextEl.putImageData(n,0,0);this._setLabels();this._setRange();this._setCustomLabel();this._setIndicators();this._setBarPointers();this._setMarkerPointers()}else this.contextEl.putImageData(this._getLabelImage(),0,0),this._setLabels(),this._setRange(),this._setCustomLabel(),this._setIndicators(),this._setBarPointers(),this._setMarkerPointers()},_reDrawTickMark:function(){if(this.Model.frame.backgroundImageUrl){var n=t.isNullOrUndefined(this.scaleImage)?null:this.scaleImage;this.contextEl.putImageData(n,0,0);this._setTicks();this._setLabels();this._setRange();this._setCustomLabel();this._setIndicators();this._setBarPointers();this._setMarkerPointers()}else this.contextEl.putImageData(this._getTickImage(),0,0),this._setTicks(),this._setLabels(),this._setRange(),this._setCustomLabel(),this._setIndicators(),this._setBarPointers(),this._setMarkerPointers()},refresh:function(){this._init()},"export":function(){var i=this.model.exportSettings,u,f,e,r,o,s;i.mode.toLowerCase()==="client"?this.exportImage(i.fileName,i.fileType):(f=i.type.toLowerCase()==="jpg"?"image/jpeg":"image/png",u=this.canvasEl[0].toDataURL(f),e={action:i.action,method:"post"},r=t.buildTag("form","",null,e),o={name:"Data",type:"hidden",value:u},s=t.buildTag("input","",null,o),r.append(s).append(this),n("body").append(r),r.submit())},exportImage:function(n,i){var f,u,o,r,c,s;if(t.browserInfo().name==="msie"&&parseFloat(t.browserInfo().version)<10)return!1;f=this.canvasEl[0].toDataURL();f=f.replace(/^data:[a-z]*;,/,"");var l=f.split(","),e=atob(l[1]),h=new ArrayBuffer(e.length),a=new Uint8Array(h);for(u=0;u<e.length;u++)a[u]=e.charCodeAt(u);return o=new Blob([h],{type:"image/png"}),t.browserInfo().name==="msie"?window.navigator.msSaveOrOpenBlob(o,n+"."+i):(r=document.createElement("a"),c=URL.createObjectURL(o),r.href=c,r.setAttribute("download",n+"."+i),document.createEvent?(s=document.createEvent("MouseEvents"),s.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),r.dispatchEvent(s)):r.fireEvent&&r.fireEvent("onclick")),!0},resizeCanvas:function(){var o,s,e,i,f;if(r=r!=0?r-1:n(".e-lineargauge").length-1,s=!0,t.isNullOrUndefined(this.GaugeEl.parent().attr("style"))||(o=this.GaugeEl.parent().attr("style").split(";")),t.isNullOrUndefined(o)||n.each(o,function(n,t){while(t.indexOf("width")!=-1){s=t.indexOf("px")==-1?!0:!1;break}}),s){for(e=window.innerWidth/u,this.model.width*=e,i=0;this.model.scales[i]!=null;i++)for(this.model.scales[i].length*=e,f=0;this.model.scales[i].markerPointers[f]!=null||this.model.scales[i].barPointers[f]!=null||this.model.scales[i].indicators[f]!=null||this.model.scales[i].customLabels[f]!=null||this.model.scales[i].ranges[f]!=null||this.model.scales[i].labels[f]!=null||this.model.scales[i].ticks[f]!=null;f++)t.isNullOrUndefined(this.model.scales[i].markerPointers[f])||(this.model.scales[i].markerPointers[f].length*=e,this.model.scales[i].markerPointers[f].width*=e),t.isNullOrUndefined(this.model.scales[i].barPointers[f])||(this.model.scales[i].barPointers[f].distanceFromScale*=e,this.model.scales[i].barPointers[f].width*=e),!t.isNullOrUndefined(this.model.scales[i].indicators[f])&&this.model.scales[i].showIndicators&&(this.model.scales[i].indicators[f].height*=e,this.model.scales[i].indicators[f].width*=e,this.model.scales[i].indicators[f].position.x*=e,this.model.scales[i].indicators[f].textLocation.x*=e),t.isNullOrUndefined(this.model.scales[i].ticks[f])||(this.model.scales[i].ticks[f].length*=e,this.model.scales[i].ticks[f].width*=e),t.isNullOrUndefined(this.model.scales[i].ranges[f])||(this.model.scales[i].ranges[f].startWidth*=e,this.model.scales[i].ranges[f].endWidth*=e),t.isNullOrUndefined(this.model.scales[i].customLabels[f])||(this.model.scales[i].customLabels[f].positionType!="outer"&&(this.model.scales[i].customLabels[f].position.x*=e),this.model.scales[i].customLabels[f].font.size=parseFloat(this.model.scales[i].customLabels[f].font.size.match(/\d+/)[0])*e<10?"10px":parseFloat(this.model.scales[i].customLabels[f].font.size.match(/\d+/)[0])*e>this._customLblMaxSize?this._customLblMaxSize.toString()+"px":(parseFloat(this.model.scales[i].customLabels[f].font.size.match(/\d+/)[0])*e).toString()+"px");this._render();r==0&&(u=window.innerWidth)}},_onDrawTicks:function(n,t){var r={index:this.tickIndex,element:this.tickEl[this.tickIndex],angle:parseInt(n),value:t},i={Object:this,Model:this.Model,scaleElement:this.Model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region};this._trigger("drawTicks",i)},_onDrawLabels:function(n){var t={index:this.labelIndex,element:this.labelEl[this.labelIndex],angle:parseInt(n),value:this.labelValue},i={object:this,Model:this.Model,scaleElement:this.Model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region,label:t};this._trigger("drawLabels",i)},_onDrawBarPointers:function(n){var t={object:this,Model:this.Model,scaleElement:this.Model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,barPointerIndex:this.barPointerIndex,barElement:this.barPointerEl[this.barPointerIndex],context:this.contextEl,style:this.style,position:this.region,pointerValue:n};this._trigger("drawBarPointers",t)},_onDrawMarkerPointers:function(n,t){var i={object:this,Model:this.Model,scaleElement:this.Model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,markerPointerIndex:this.markerPointerIndex,markerElement:this.markerPointerEl[this.markerPointerIndex],context:this.contextEl,style:this.style,position:this.region,pointerValue:t,pointerAngle:parseInt(n)};this._trigger("drawMarkerPointers",i)},_onDrawRange:function(){var n={object:this,Model:this.Model,scaleElement:this.Model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,rangeIndex:this.rangeIndex,rangeElement:this.rangeEl[this.rangeIndex],context:this.contextEl,style:this.style,position:this.region};this._trigger("drawRange",n)},_onDrawCustomLabel:function(){var n={object:this,Model:this.Model,scaleElement:this.Model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,customLabelIndex:this.customLabelIndex,customLabelElement:this.customLabelEl[this.customLabelIndex],context:this.contextEl,style:this.style,position:this.region};this._trigger("drawCustomLabel",n)},_onDrawIndicators:function(){var n={object:this,Model:this.Model,scaleElement:this.Model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,indicatorIndex:this.indicatorIndex,indicatorEl:this.indicatorEl[this.indicatorIndex],context:this.contextEl,style:this.style,position:this.region};this._trigger("drawIndicators",n)},onLoad:function(){var n={object:this,Model:this.Model,scaleElement:this.Model.scales,context:this.contextEl};this._trigger("load",n)},_onInit:function(){var n={object:this,Model:this.Model,scaleElement:this.Model.scales,context:this.contextEl};this._trigger("init",n)},_onRenderComplete:function(){var n={object:this,Model:this.Model,scaleElement:this.Model.scales,context:this.contextEl};this._trigger("renderComplete",n)},_onMouseClick:function(n){var t={index:this.markerPointerIndex,element:this.markerPointerEl[this.markerPointerIndex],value:n},i={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region,markerPointer:t};this._trigger("mouseClick",i)},_onMouseClickMove:function(n){var t={index:this.markerPointerIndex,element:this.markerPointerEl[this.markerPointerIndex],value:n},i={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region,markerPointer:t};this._trigger("mouseClickMove",i)},_onMouseClickUp:function(n){var t={index:this.markerPointerIndex,element:this.markerPointerEl[this.markerPointerIndex],value:n},i={object:this,scaleElement:this.model.scales[this.scaleIndex],scaleIndex:this.scaleIndex,context:this.contextEl,style:this.style,position:this.region,markerPointer:t};this._trigger("mouseClickUp",i)},_restoreWidth:function(){this.scaleEl[this.scaleIndex].length=this.scaleEl[this.scaleIndex].length+this.bottomRadius+this.scaleEl[this.scaleIndex].width;this.Model.orientation=="Vertical"?this.scaleStartY[this.scaleIndex]=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this.scaleStartY[this.scaleIndex]-this.bottomRadius-this.scaleEl[this.scaleIndex].width/2:this.scaleStartY[this.scaleIndex]-this.scaleEl[this.scaleIndex].width/2:this.scaleStartX[this.scaleIndex]=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this.scaleStartX[this.scaleIndex]-this.bottomRadius-this.scaleEl[this.scaleIndex].width/2:this.scaleStartX[this.scaleIndex]-this.scaleEl[this.scaleIndex].width/2},_modifyWidth:function(){this.scaleEl[this.scaleIndex].length=this.scaleEl[this.scaleIndex].length-this.bottomRadius-this.scaleEl[this.scaleIndex].width;this.Model.orientation=="Vertical"?this.scaleStartY[this.scaleIndex]=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this.scaleStartY[this.scaleIndex]+this.bottomRadius+this.scaleEl[this.scaleIndex].width/2:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2:this.scaleStartX[this.scaleIndex]=this.scaleEl[this.scaleIndex].direction.toLowerCase()=="clockwise"?this.scaleStartX[this.scaleIndex]+this.bottomRadius+this.scaleEl[this.scaleIndex].width/2:this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2},_getClockwiseLinePosition:function(n){var t;return t=(n-this.scaleEl[this.scaleIndex].minimum)/(this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum)*100,this.Model.orientation=="Vertical"?this.scaleStartY[this.scaleIndex]+parseInt(t*this.scaleEl[this.scaleIndex].length/100):this.scaleStartX[this.scaleIndex]+parseInt(t*this.scaleEl[this.scaleIndex].length/100)},_getCounterClockwiseLinePosition:function(n){var t;return t=this.scaleEl[this.scaleIndex].maximum-n+this.scaleEl[this.scaleIndex].minimum,t=(t-this.scaleEl[this.scaleIndex].minimum)/(this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum)*100,this.Model.orientation=="Vertical"?this.scaleStartY[this.scaleIndex]+parseInt(t*this.scaleEl[this.scaleIndex].length/100):this.scaleStartX[this.scaleIndex]+parseInt(t*this.scaleEl[this.scaleIndex].length/100)},_getValue:function(n){var i,t;return i=this.Model.orientation=="Vertical"?(n.y-this.scaleStartY[this.scaleIndex])/this.scaleEl[this.scaleIndex].length*100:(n.x-this.scaleStartX[this.scaleIndex])/this.scaleEl[this.scaleIndex].length*100,t=(i*(this.scaleEl[this.scaleIndex].maximum-this.scaleEl[this.scaleIndex].minimum)+this.scaleEl[this.scaleIndex].minimum)/100,this.scaleEl[this.scaleIndex].direction=="counterclockwise"?this.scaleEl[this.scaleIndex].maximum-t:this.scaleEl[this.scaleIndex].minimum+t},_getPointerXPosition:function(n){var t,i;return this.Model.orientation=="Vertical"?(this.markerPlacement=="far"&&(t=this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width+this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale,i=0),this.markerPlacement=="near"&&(t=this.scaleStartX[this.scaleIndex]+n.distanceFromScale,i=180),this.markerPlacement=="center"&&(t=n.type=="circle"?this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-Math.sqrt(n.length*n.length+n.width*n.width)/2+n.distanceFromScale:this.scaleStartX[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.width/2+n.distanceFromScale,i=0)):(this.markerPlacement=="far"&&(t=this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width+this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale,i=90),this.markerPlacement=="near"&&(t=this.scaleStartY[this.scaleIndex]-this.scaleEl[this.scaleIndex].border.width/2+n.distanceFromScale,i=270),this.markerPlacement=="center"&&(t=n.type=="circle"?this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-Math.sqrt(n.length*n.length+n.width*n.width)/2+n.distanceFromScale:this.scaleStartY[this.scaleIndex]+this.scaleEl[this.scaleIndex].width/2-n.length/2+n.distanceFromScale,i=90)),{startx:t,angle:i}},_hexFromRGB:function(t,i,r){var u=[t.toString(16),i.toString(16),r.toString(16)];return n.each(u,function(n,t){t.length===1&&(u[n]="0"+t)}),u.join("").toUpperCase()},_setGradientColor:function(t,i,r){r.Name||typeof r=="string"?(i.addColorStop(0,r),i.addColorStop(1,r)):n.each(r,function(n,t){i.addColorStop(t.colorStop!=NaN?t.colorStop:0,typeof t.color=="string"?t.color:t.color)})},_getFontString:function(n,t){return t.fontStyle+" "+(t.size==null?"11px":t.size)+" "+t.fontFamily},_setPointerDimension:function(n,t){if(t.Model.orientation&&t.Model.orientation=="horizontal"){var i=n.width,r=n.height;n.height=i;n.width=r}return n},_setContextRotation:function(n,t){t.contextEl.rotate(Math.PI*(n.angle/180))},_browserInfo:function(){var n={},t=[],i={webkit:/(chrome)[ \/]([\w.]+)/i,safari:/(webkit)[ \/]([\w.]+)/i,msie:/(msie) ([\w.]+)/i,opera:/(opera)(?:.*version|)[ \/]([\w.]+)/i,mozilla:/(mozilla)(?:.*? rv:([\w.]+)|)/i};for(var r in i)if(i.hasOwnProperty(r)&&(t=navigator.userAgent.match(i[r]),t)){n.name=t[1].toLowerCase();n.version=t[2];!navigator.userAgent.match(/Trident\/7\./)||(n.name="msie");break}return n.isMSPointerEnabled=n.name=="msie"&&n.version>9&&window.navigator.msPointerEnabled,n.pointerEnabled=window.navigator.pointerEnabled,n},wireEvents:function(){var u;n(this.canvasEl).off();var f=jQuery.uaMatch(navigator.userAgent),r=this._browserInfo(),i=r.isMSPointerEnabled,t=r.pointerEnabled;this.startEv=i?t?"pointerdown":"MSPointerDown":"touchstart mousedown";this.endEv=i?t?"pointerup":"MSPointerUp":"touchend mouseup";this.moveEv=i?t?"pointermove":"MSPointerMove":"touchmove mousemove";this.leaveEv=i?t?"pointerleave":"MSPointerOut":"touchleave mouseleave";this.scrollEv=f.browser.toLowerCase()=="mozilla"?t?"mousewheel":"DOMMouseScroll":"mousewheel";this.model.browserInfo=r;u=this.model.readOnly?"pan-y pan-x":"none";n(this.element).css("touch-action",u);this.onMouseMoveHandler=n.proxy(this._onMouseMove,this);this.onMouseUpHandler=n.proxy(this._onMouseUp,this);this.onHoverOCustomLabel=n.proxy(this._onHoverOCustomLabel,this);this.onLeaveOCustomLabel=n.proxy(this._onLeaveOCustomLabel,this);this.model.readOnly||(this.onMouseDownHandler=n.proxy(this._onMouseDown,this),this._on(n(this.element),this.startEv,this._onMouseDown));this._on(n(this.canvasEl),this.startEv,this.lgMouseDown);this._on(n(this.canvasEl),this.endEv,this.lgMouseUp);(this.model.tooltip.showCustomLabelTooltip||this.model.tooltip.showLabelTooltip)&&(n(this.canvasEl).bind(this.moveEv,this.onMouseMoveHandler),n(this.canvasEl).bind(this.scrollEv,this.onMouseMoveHandler),n(this.canvasEl).bind(this.startEv,this.onMouseDownHandler),n(this.canvasEl).bind(this.endEv,this.onLeaveOCustomLabel),n(this.canvasEl).bind(this.leaveEv,this.onLeaveOCustomLabel));this.element.bind(this.startEv,this.onMouseDownHandler);this.model.tooltip.showCustomLabelTooltip&&(n("."+this._id+"outercustomlbl").bind("mouseenter",this.onHoverOCustomLabel),n("."+this._id+"outercustomlbl").bind(this.leaveEv,this.onLeaveOCustomLabel));this._on(n(this.canvasEl),"contextmenu",this._lgRightClick)},unWireEvents:function(){this.element.unbind(this.startEv,this.onMouseDownHandler);this._off(n(this.canvasEl),"contextmenu",this._lgRightClick);this._off(n(this.canvasEl),this.startEv,this.lgMouseDown);this._off(n(this.canvasEl),this.endEv,this.lgMouseUp)},lgMouseDown:function(){t.isTouchDevice()&&this.model.rightClick!=""&&(this._longPressTimer=new Date)},lgMouseUp:function(n){var i=new Date;this._doubleTapTimer!=null&&i-this._doubleTapTimer<300&&this._lgDoubleClick(n);this._doubleTapTimer=i;t.isTouchDevice()&&this.model.rightClick!=""&&new Date-this._longPressTimer>1500&&this._lgRightClick(n)},_onHoverOCustomLabel:function(n){(n.currentTarget.innerHTML!=null||n.currentTarget.innerHTML!="")&&this._showTooltip(n,n.currentTarget.innerHTML)},_onLeaveOCustomLabel:function(t){this.isTouch(t)?(this._performTooltip(t),window.clearTimeout(this.model.timer),this.model.timer=setTimeout(function(){n(".tooltipDiv").fadeOut(500)},1200)):this._hideTooltip()},isTouch:function(n){var t=n.originalEvent?n.originalEvent:n;return t.pointerType=="touch"||t.pointerType==2||t.type.indexOf("touch")>-1?!0:!1},_blockDefaultActions:function(n){n.cancelBubble=!0;n.returnValue=!1;n.preventDefault&&n.preventDefault();n.stopPropagation&&n.stopPropagation()},_onMouseDown:function(t){var r,u,e,o,h,f,s,i;this._blockDefaultActions(t);this._mouseDown=!0;f=this.isTouch(t)?10:0;s=t.originalEvent.touches?t.originalEvent.touches[0]:t;r={x:s.pageX-n(this.canvasEl).offset().left-(this.Model.frame.outerWidth+this.Model.frame.innerWidth),y:s.pageY-n(this.canvasEl).offset().top-(this.Model.frame.outerWidth+this.Model.frame.innerWidth)};i=this;this.model.readOnly||n.each(this.Model.scales,function(t,s){i.scaleIndex=t;s.markerPointers!=null&&(i.markerPointerEl=s.markerPointers,n.each(s.markerPointers,function(t,s){u=i.scaleEl[i.scaleIndex].direction.toLowerCase()=="clockwise"?i._getClockwiseLinePosition(s.value):i._getCounterClockwiseLinePosition(s.value);e=u+s.width;o=u-s.width;h=i._getPointerXPosition(s).startx;var c=i._isBetween((i.Model.orientation=="horizontal"?r.y:r.x)-s.width,(i.Model.orientation=="horizontal"?r.y:r.x)+s.width,h,f);(i.Model.orientation=="horizontal"?i._isBetween(o,e,r.x,f):i._isBetween(o,e,r.y,f))&&c&&(i.activeElement=s);i.Model.scales[i.scaleIndex].barPointers[t]!=null&&(i.activeBarElement=i.Model.scales[i.scaleIndex].barPointers[t]);i.model.mouseClick&&i._onMouseClick(s.value);i.onMouseMoveHandler=n.proxy(i._onMouseMove,i);i.onMouseUpHandler=n.proxy(i._onMouseUp,i);n(document).bind(i.moveEv,i.onMouseMoveHandler);n(document).bind(i.endEv,i.onMouseUpHandler)}))})},_isBetween:function(n,t,i,r){return n<t?i>=n-r&&i<=t+r:i>=t-r&&i<=n+r},_lgDoubleClick:function(n){this.model.doubleClick!=""&&this._trigger("doubleClick",{data:{event:n}})},_lgRightClick:function(n){this.model.rightClick!=""&&this._trigger("rightClick",{data:{event:n}})},_onMouseUp:function(){this._mouseDown=!1;this.mouseMove=!1;n(document).unbind(self.moveEv,self.onMouseMoveHandler);n(document).unbind(self.endEv,self.onMouseUpHandler);this.model.mouseClickUp&&this.activeElement&&this._onMouseClickUp(this.activeElement.value);this.activeElement=null},_mousePosition:function(n){if(!t.util.isNullOrUndefined(n.pageX)&&n.pageX>0)return{x:n.pageX,y:n.pageY};if(n.originalEvent&&!t.util.isNullOrUndefined(n.originalEvent.pageX)&&n.originalEvent.pageX>0)return{x:n.originalEvent.pageX,y:n.originalEvent.pageY};if(n.originalEvent&&n.originalEvent.changedTouches!=i){if(!t.util.isNullOrUndefined(n.originalEvent.changedTouches[0].pageX)&&n.originalEvent.changedTouches[0].pageX>0)return{x:n.originalEvent.changedTouches[0].pageX,y:n.originalEvent.changedTouches[0].pageY}}else return{x:0,y:0}},_calTouchPosition:function(n){var i=jQuery.uaMatch(navigator.userAgent),t=this._mousePosition(n);n.pageX=t.x;n.pageY=t.y},getEvent:function(n){return n.targetTouches&&n.targetTouches[0]?n.targetTouches[0]:n},_onMouseMove:function(i){if(this._mouseDown&&!t.isNullOrUndefined(this.activeElement)){this._blockDefaultActions(i);var r=i.originalEvent.touches?i.originalEvent.touches[0]:i,u={x:r.pageX-n(this.canvasEl).offset().left-(this.Model.frame.outerWidth+this.Model.frame.innerWidth),y:r.pageY-n(this.canvasEl).offset().top-(this.Model.frame.outerWidth+this.Model.frame.innerWidth)};this.activeElement.value=this._getValue(u);this.value(this.activeElement.value);this.model.mouseClickMove&&this._onMouseClickMove(this.activeElement.value);this.activeBarElement&&(this.activeBarElement.value=this._getValue(u));this.contextEl.putImageData?this._reDrawBarPointer():this._init()}else(this.model.tooltip.showCustomLabelTooltip||this.model.tooltip.showLabelTooltip)&&!this.isTouch(i)&&this._performTooltip(i)},_performTooltip:function(t){for(var r,u=!1,f=10,o=this.isTouch(t),i=0;this._savedPoints[i]!=null;i++)if(o){var c=this._calTouchPosition(t),e=this.getEvent(t),s=e.pageX,h=e.pageY,r={X:s-n(this.canvasEl).offset().left,Y:h-n(this.canvasEl).offset().top};r.X>this._savedPoints[i].startX-f&&r.X<this._savedPoints[i].startX+this._savedPoints[i].width+f&&r.Y>this._savedPoints[i].startY-f&&r.Y<this._savedPoints[i].startY+this._savedPoints[i].height+f?(this._showTooltip(t,this._savedPoints[i].value),u=!0):u==!1&&this._hideTooltip()}else r={X:t.pageX-n(this.canvasEl).offset().left,Y:t.pageY-n(this.canvasEl).offset().top},r.X>this._savedPoints[i].startX&&r.X<this._savedPoints[i].startX+this._savedPoints[i].width&&r.Y>this._savedPoints[i].startY&&r.Y<this._savedPoints[i].startY+this._savedPoints[i].height?(this._showTooltip(t,this._savedPoints[i].value),u=!0):u==!1&&this._hideTooltip()},_showTooltip:function(t,i){var e=i+"",r=n(".tooltipDiv"),o;r.length==0&&(r=n("<div class='tooltipDiv' style='position: absolute; z-index: 105; display: block;'><\/div>"),n(document.body).append(r));this.model.tooltip.templateID!=""&&this.model.tooltip.templateID!=null?(o=n("#"+this.model.tooltip.templateID).clone(),n(".tooltipDiv")[0].innerHTML="",n(o).css("display","block").appendTo(r),n(r).css({"background-color":this.model.backgroundColor,border:"1px solid #bbbcbb","border-radius":"3px",color:"#565656"}),r.html(r.html().replace("#label#",e))):(n(r).html(e),n(r).css({"background-color":"white",border:"2px solid #bbbcbb",position:"absolute",padding:"10px 20px","margin-top":"5px","text-align":"left",font:"12px Segoe UI","font-stretch":"condensed",display:"inline-block","border-radius":"3px",color:"#565656",width:"auto"}));var s=10,u=t.pageX+s,f=t.pageY+s;u=u+n(r).width()<n(window).width()?u:u-n(r).width();f=f+n(r).height()<n(window).height()?f:f-n(r).height();n(r).css("left",u);n(r).css("top",f);n(".tooltipDiv").show()},_hideTooltip:function(){n(".tooltipDiv").remove()},_setTheme:function(){var n=this.model.theme.toLowerCase(),t=this.model.themeProperties[n];this._setThemeColors(t)},_setThemeColors:function(n){var f=[],e=this.model.themeProperties,o,i,t,r,u;for(o in e)f.push(o);for(i=0;i<f.length;i++)for(this.model.backgroundColor=!this.model.backgroundColor||this.model.backgroundColor==e[f[i]].scales.backgroundColor?n.scales.backgroundColor:this.model.backgroundColor,this.model.borderColor=!this.model.borderColor||this.model.borderColor==e[f[i]].scales.border.color?n.scales.border.color:this.model.borderColor,this.model.labelColor=!this.model.labelColor||this.model.labelColor==e[f[i]].scales.labels.labelColor?n.scales.labels.labelColor:this.model.labelColor,this.model.tickColor=!this.model.tickColor||this.model.tickColor==e[f[i]].scales.ticks.color?n.scales.ticks.color:this.model.tickColor,t=0;t<this.model.scales.length;t++){for(r=0;r<this.model.scales[t].markerPointers.length;r++)this.model.scales[t].markerPointers[r].backgroundColor=!this.model.scales[t].markerPointers[r].backgroundColor||this.model.scales[t].markerPointers[r].backgroundColor==e[f[i]].scales.markerPointers.backgroundColor?n.scales.markerPointers.backgroundColor:this.model.scales[t].markerPointers[r].backgroundColor,this.model.scales[t].markerPointers[r].border.color=!this.model.scales[t].markerPointers[r].border.color||this.model.scales[t].markerPointers[r].border.color==e[f[i]].scales.markerPointers.border.color?n.scales.markerPointers.border.color:this.model.scales[t].markerPointers[r].border.color;for(u=0;u<this.model.scales[t].barPointers.length;u++)this.model.scales[t].barPointers[u].backgroundColor=!this.model.scales[t].barPointers[u].backgroundColor||this.model.scales[t].barPointers[u].backgroundColor==e[f[i]].scales.barPointers.backgroundColor?n.scales.barPointers.backgroundColor:this.model.scales[t].barPointers[u].backgroundColor,this.model.scales[t].barPointers[u].border.color=!this.model.scales[t].barPointers[u].border.color||this.model.scales[t].barPointers[u].border.color==e[f[i]].scales.barPointers.border.color?n.scales.barPointers.border.color:this.model.scales[t].barPointers[u].border.color}}});t.datavisualization.LinearGauge.TickType={MajorInterval:"majorinterval",MinorInterval:"minorinterval"};t.datavisualization.LinearGauge.LabelType={Major:"major",Minor:"minor"};t.datavisualization.LinearGauge.FontStyle={Bold:"bold",Italic:"italic",Regular:"regular",Strikeout:"strikeout",Underline:"underline"};t.datavisualization.LinearGauge.PointerPlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.LinearGauge.TickPlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.LinearGauge.LabelPlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.LinearGauge.RangePlacement={Near:"near",Far:"far",Center:"center"};t.datavisualization.LinearGauge.UnitTextPlacement={Front:"front",Back:"back"};t.datavisualization.LinearGauge.Directions={Clockwise:"clockwise",CounterClockwise:"counterclockwise"};t.datavisualization.LinearGauge.ScaleType={Rectangle:"rectangle",RoundedRectangle:"roundedrectangle",Thermometer:"thermometer"};t.datavisualization.LinearGauge.IndicatorType={Rectangle:"rectangle",Circle:"circle",RoundedRectangle:"roundedrectangle",Text:"text"};t.datavisualization.LinearGauge.MarkerType={Rectangle:"rectangle",Triangle:"triangle",Ellipse:"ellipse",Diamond:"diamond",Pentagon:"pentagon",Circle:"circle",Star:"star",Slider:"slider",Pointer:"pointer",Wedge:"wedge",Trapezoid:"trapezoid",RoundedRectangle:"roundedrectangle"};t.datavisualization.LinearGauge.CustomLabelPositionType={Inner:"inner",Outer:"outer"};t.datavisualization.LinearGauge.OuterCustomLabelPosition={Left:"left",Right:"right",Top:"top",Bottom:"bottom"};t.datavisualization.LinearGauge.Themes={FlatLight:"flatlight",FlatDark:"flatdark"}}(jQuery,Syncfusion),function(n,t,i){var u,r,f;t.widget({ejDigitalGauge:"ej.datavisualization.DigitalGauge"},{element:null,model:null,validTags:["div","span"],_rootCSS:"e-digitalgauge",defaults:{exportSettings:{mode:"client",type:"png",fileName:"DigitalGauge",action:""},segmentData:{"0":[0,1,2,3,4,5,14,15],"1":[1,2],"2":[0,14,1,6,8,4,3,15],"3":[0,1,2,3,6,8,14,15],"4":[1,2,5,6,8],"5":[0,2,3,5,6,8,14,15],"6":[0,2,3,4,5,6,8,14,15],"7":[0,1,2,14],"8":[0,1,2,3,4,5,6,8,14,15],"9":[0,1,2,3,5,6,8,14,15],A:[0,1,2,4,5,6,8,14],B:[0,1,2,3,7,9,8,14,15],C:[0,3,4,5,14,15],D:[0,1,2,3,7,9,14,15],E:[0,3,4,5,6,8,14,15],F:[0,4,5,6,8,14],G:[0,2,3,4,5,8,14,15],H:[1,2,4,5,6,8],I:[0,3,7,9,14,15],J:[1,2,3,4,15],K:[4,5,6,10,11],L:[3,4,5,15],M:[1,2,4,5,10,13],N:[1,2,4,5,11,13],O:[0,1,2,3,4,5,14,15],P:[0,1,4,5,6,8,14],Q:[0,1,2,3,4,5,11,14,15],R:[0,1,4,5,6,8,11,14],S:[0,2,3,5,6,8,14,15],T:[0,7,9,14],U:[1,2,3,4,5,15],V:[4,5,10,12],W:[1,2,4,5,11,12],X:[10,11,12,13],Y:[1,5,6,7,8],Z:[0,3,10,12,14,15]},matrixSegmentData:{"1":[0,3,0,4,1,1,1,2,1,3,1,4,2,3,2,4,3,3,3,4,4,3,4,4,5,3,5,4,6,1,6,2,6,3,6,4,6,5,6,6],"2":[0,1,0,2,0,3,0,4,0,5,1,5,1,6,2,5,2,6,3,4,3,5,4,3,4,2,5,2,5,1,6,1,6,2,6,3,6,4,6,5,6,6],"3":[0,1,0,2,0,3,0,4,0,5,1,5,1,6,2,5,2,6,3,2,3,3,3,4,3,5,3,6,4,5,4,6,5,5,5,6,6,1,6,2,6,3,6,4,6,5],"4":[0,3,0,4,0,5,1,2,1,3,1,4,1,5,2,1,2,2,2,4,2,5,3,0,3,1,3,4,3,5,4,0,4,1,4,2,4,3,4,4,4,5,4,6,5,4,5,5,6,4,6,5],"5":[0,1,0,2,0,3,0,4,0,5,0,6,1,1,1,2,2,1,2,2,3,1,3,2,3,3,3,4,3,5,3,6,4,5,4,6,5,5,5,6,6,1,6,2,6,3,6,4,6,5,6,6],"6":[0,3,0,4,0,5,0,6,1,2,1,3,2,1,2,2,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,4,3,4,6,4,7,5,2,5,3,5,6,5,7,6,3,6,4,6,5,6,6],"7":[0,1,0,2,0,3,0,4,0,5,0,6,0,7,1,6,1,7,2,5,2,6,3,4,3,5,4,3,4,4,5,2,5,3,6,1,6,2],"8":[0,2,0,3,0,4,0,5,0,6,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,2,3,3,3,4,3,5,3,6,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,2,6,3,6,4,6,5,6,6],"9":[0,2,0,3,0,4,0,5,1,1,1,2,1,5,1,6,2,1,2,2,2,4,2,5,2,6,3,2,3,3,3,4,3,5,3,6,4,5,4,6,5,5,5,6,6,2,6,3,6,4,6,4,6,5],"0":[0,2,0,3,0,4,0,5,0,6,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,2,6,3,6,4,6,5,6,6],a:[0,2,0,3,0,4,0,5,0,6,1,6,1,7,2,6,2,7,3,2,3,3,3,4,3,5,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,2,6,3,6,4,6,5,6,6,6,7],b:[0,1,0,2,1,1,1,2,2,1,2,2,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,4,3,4,6,4,7,5,1,5,2,5,6,5,7,6,1,6,2,6,3,6,4,6,5,6,6],c:[1,3,1,4,1,5,1,6,2,2,2,3,3,1,3,2,4,1,4,2,5,2,5,3,6,3,6,4,6,5,6,6],d:[0,6,0,7,1,6,1,7,2,6,2,7,3,2,3,3,3,4,3,5,3,6,3,7,4,1,4,2,4,5,4,6,4,7,5,1,5,2,5,6,5,7,6,2,6,3,6,4,6,5,6,6,6,7],e:[0,2,0,3,0,4,0,5,0,6,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,5,1,5,2,6,2,6,3,6,4,6,5,6,6,6,7],f:[0,4,0,5,0,6,0,7,1,3,1,4,2,3,2,4,3,1,3,2,3,3,3,4,3,5,3,6,3,7,4,3,4,4,5,3,5,4,6,3,6,4],g:[0,2,0,3,0,4,0,5,0,6,0,7,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,2,3,3,3,4,3,5,3,6,3,7,4,6,4,7,5,6,5,7,6,2,6,3,6,4,6,5,6,6],h:[0,1,0,2,1,1,1,2,2,1,2,2,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,4,3,4,6,4,7,5,1,5,2,5,6,5,7,6,1,6,2,6,6,6,7],i:[0,3,0,4,2,1,2,2,2,3,2,4,3,3,3,4,4,3,4,4,5,3,5,4,6,3,6,4],j:[1,5,1,6,2,5,2,6,3,5,3,6,4,1,4,2,4,5,4,6,5,1,5,2,5,5,5,6,6,2,6,3,6,4,6,5],k:[0,1,0,2,1,1,1,2,1,4,1,5,2,1,2,2,2,3,2,4,3,1,3,2,3,3,4,1,4,2,4,3,4,4,4,5,5,1,5,2,5,5,5,6,6,1,6,2,6,6,6,7],l:[0,2,0,3,0,4,0,5,0,6,1,5,1,6,2,5,2,6,3,5,3,6,4,5,4,6,5,5,5,6,6,5,6,6],m:[0,1,0,2,0,3,0,4,0,5,0,6,1,0,1,1,1,2,1,3,1,4,1,5,1,6,1,7,2,0,2,1,2,3,2,4,2,6,2,7,3,0,3,1,3,3,3,4,3,6,3,7,4,0,4,1,4,3,4,4,4,6,4,7,5,0,5,1,5,3,5,4,5,6,5,7,6,0,6,1,6,3,6,4,6,6,6,7],n:[1,1,1,2,1,3,1,4,1,5,1,6,2,0,2,1,2,2,2,6,2,7,3,0,3,1,3,6,3,7,4,0,4,1,4,6,4,7,5,0,5,1,5,6,5,7,6,0,6,1,6,6,6,7],o:[1,2,1,3,1,4,1,5,2,1,2,2,2,5,2,6,3,1,3,2,3,5,3,6,4,1,4,2,4,5,4,6,5,1,5,2,5,5,5,6,6,2,6,3,6,4,6,5],p:[1,1,1,2,1,3,1,4,1,5,1,6,2,1,2,2,2,3,2,6,2,7,3,1,3,2,3,6,3,7,4,1,4,2,4,3,4,4,4,5,4,6,5,1,5,2,6,1,6,2],q:[0,2,0,3,0,4,0,5,0,6,0,7,1,1,1,2,1,5,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,5,3,6,3,7,4,2,4,3,4,4,4,5,4,6,4,7,5,6,5,7,6,6,6,7],r:[0,1,0,3,0,4,0,5,1,1,1,2,1,3,1,4,1,5,1,6,2,1,2,2,2,6,3,1,3,2,4,1,4,2,5,1,5,2,6,1,6,2],s:[1,2,1,3,1,4,1,5,2,1,2,2,3,1,3,2,4,3,4,4,5,4,5,5,6,1,6,2,6,3,6,4],t:[0,3,0,4,1,3,1,4,2,1,2,2,2,3,2,4,2,5,2,6,3,3,3,4,4,3,4,4,5,3,5,4,6,4,6,5,6,6,6,7],u:[1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,5,5,6,5,7,6,2,6,3,6,4,6,5,6,6,6,7],v:[1,1,1,2,1,6,1,7,2,2,2,3,2,5,2,6,3,2,3,3,3,5,3,6,4,3,4,4,4,5,5,4,5,5,6,4],w:[0,0,0,1,0,6,0,7,1,0,1,1,1,6,1,7,2,0,2,1,2,3,2,4,2,6,2,7,3,0,3,1,3,3,3,4,3,6,3,7,4,0,4,1,4,3,4,4,4,6,4,7,5,0,5,1,5,2,5,3,5,4,5,5,5,6,5,7,6,1,6,2,6,3,6,4,6,5,6,6],x:[1,1,1,2,1,6,1,7,2,2,2,3,2,5,2,6,3,3,3,4,3,5,4,3,4,4,4,5,5,2,5,3,5,5,5,6,6,1,6,2,6,6,6,7],y:[1,1,1,2,1,5,1,6,2,1,2,2,2,5,2,6,3,2,3,3,3,4,3,5,4,3,4,4,5,2,5,3,6,1,6,2],z:[1,2,1,3,1,4,1,5,1,6,1,7,2,6,2,7,3,5,3,6,4,4,4,5,5,3,5,4,6,2,6,3,6,4,6,5,6,6,6,7],A:[0,3,0,4,1,2,1,3,1,4,1,5,2,2,2,3,2,4,2,5,3,1,3,2,3,5,3,6,4,1,4,2,4,3,4,4,4,5,4,6,5,1,5,2,5,5,5,6,6,0,6,1,6,6,6,7],B:[0,1,0,2,0,3,0,4,0,5,0,6,1,1,1,2,1,3,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,4,3,4,6,4,7,5,1,5,2,5,6,5,7,6,1,6,2,6,3,6,4,6,5,6,6],C:[0,2,0,3,0,4,0,5,0,6,1,1,1,2,2,0,2,1,3,0,3,1,4,0,4,1,5,1,5,2,6,2,6,3,6,4,6,5,6,6],D:[0,1,0,2,0,3,0,4,0,5,0,6,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,1,6,2,6,3,6,4,6,5,6,6],E:[0,1,0,2,0,3,0,4,0,5,0,6,1,1,1,2,2,1,2,2,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,5,1,5,2,6,1,6,2,6,3,6,4,6,5,6,6],F:[0,1,0,2,0,3,0,4,0,5,0,6,1,1,1,2,2,1,2,2,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,5,1,5,2,6,1,6,2],G:[0,2,0,3,0,4,0,5,0,6,1,1,1,2,2,0,2,1,3,0,3,1,3,4,3,5,3,6,4,0,4,1,4,6,5,1,5,2,5,6,6,2,6,3,6,4,6,5,6,6],H:[0,1,0,2,0,6,0,7,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,3,3,4,3,5,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,1,6,2,6,6,6,7],I:[0,2,0,3,0,4,0,5,0,6,1,4,2,4,3,4,4,4,5,4,6,1,6,2,6,3,6,4,6,5,6,6,6,7],J:[0,2,0,3,0,4,0,5,0,6,1,5,1,6,2,5,2,6,3,5,3,6,4,1,4,2,4,5,4,6,5,1,5,2,5,5,5,6,6,2,6,3,6,4,6,5],K:[0,1,0,2,0,5,0,6,1,1,1,2,1,4,1,5,2,1,2,2,2,3,2,4,3,1,3,2,3,3,4,1,4,2,4,3,4,4,5,1,5,2,5,4,5,5,6,1,6,2,6,5,6,6],L:[0,1,0,2,1,1,1,2,2,1,2,2,3,1,3,2,4,1,4,2,5,1,5,2,6,1,6,2,6,3,6,4,6,5,6,6],M:[0,1,0,2,0,6,0,7,1,1,1,2,1,3,1,5,1,6,1,7,2,1,2,2,2,4,2,6,2,7,3,1,3,2,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,1,6,2,6,6,6,7],N:[0,1,0,2,0,6,0,7,1,1,1,2,1,3,1,6,1,7,2,1,2,2,2,4,2,6,2,7,3,1,3,2,3,5,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,1,6,2,6,6,6,7],O:[0,2,0,3,0,4,0,5,1,1,1,2,1,5,1,6,2,0,2,1,2,6,2,7,3,0,3,1,3,6,3,7,4,0,4,1,4,6,4,7,5,1,5,2,5,5,5,6,6,2,6,3,6,4,6,5],P:[0,1,0,2,0,3,0,4,0,5,0,6,1,1,1,2,1,3,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,5,1,5,2,6,1,6,2],Q:[0,2,0,3,0,4,0,5,1,1,1,2,1,5,1,6,2,0,2,1,2,6,2,7,3,0,3,1,3,6,3,7,4,0,4,1,4,4,4,6,4,7,5,1,5,2,5,5,5,6,6,2,6,3,6,4,6,5,6,6,6,7],R:[0,1,0,2,0,3,0,4,0,5,0,6,1,1,1,2,1,3,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,4,5,5,1,5,5,6,2,6,1,6,2,6,6],S:[0,2,0,3,0,4,0,5,0,6,1,1,1,2,2,1,2,2,3,2,3,3,3,4,3,5,4,5,4,6,5,5,5,6,6,1,6,2,6,3,6,4,6,5,6,6],T:[0,1,0,2,0,3,0,4,0,5,0,6,0,7,1,4,2,4,3,4,4,4,5,4],U:[0,1,0,2,0,6,0,7,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,2,6,3,6,4,6,5,6,6],V:[0,0,0,1,0,6,0,7,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,2,3,3,3,5,3,6,4,3,4,4,4,5,4,6,5,4,5,5,6,4],W:[0,1,0,2,0,6,0,7,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,6,3,7,4,1,4,2,4,4,4,6,4,7,5,1,5,2,5,3,5,5,5,6,5,7,6,1,6,2,6,6,6,7],X:[0,0,0,1,0,6,0,7,1,1,1,2,1,5,1,6,2,1,2,2,2,5,2,6,3,2,3,3,3,4,3,5,4,2,4,3,4,4,4,5,5,1,5,2,5,5,5,6,6,0,6,1,6,6,6,7],Y:[0,0,0,1,0,6,0,7,1,0,1,1,1,6,1,7,2,1,2,2,2,5,2,6,3,2,3,3,3,4,3,5,4,3,4,4,5,3,5,4,6,3,6,4],Z:[0,1,0,2,0,3,0,4,0,5,0,6,0,7,1,6,1,7,2,5,2,6,3,4,3,5,4,2,4,3,5,1,5,2,6,1,6,2,6,3,6,4,6,5,6,6,6,7],",":[5,3,5,4,5,5,6,4,6,5,7,3,7,4],":":[1,3,1,4,1,5,2,3,2,4,2,5,4,3,4,4,4,5,5,3,5,4,5,5],"%":[0,6,0,7,1,1,1,2,1,5,1,6,2,1,2,2,2,4,2,5,3,3,3,4,4,2,4,3,5,1,5,2,5,4,5,5,6,0,6,1,6,4,6,5],"!":[0,3,0,4,0,5,1,3,1,4,1,5,2,3,2,4,2,5,3,3,3,4,3,5,4,3,4,4,4,5,5,3,5,4,5,5,7,4],"(":[0,2,0,3,1,1,1,2,2,1,2,2,3,1,3,2,4,1,4,2,5,1,5,2,6,1,6,2,7,2,7,3],")":[0,5,0,6,1,6,1,7,2,6,2,7,3,6,3,7,4,6,4,7,5,6,5,7,6,6,6,7,7,5,7,6],".":[5,3,5,4,5,5,6,3,6,4,6,5,7,3,7,4,7,5]},frame:{backgroundImageUrl:null,innerWidth:6,outerWidth:10},height:150,width:400,enableResize:!1,isResponsive:!1,themes:"flatlight",items:null,init:null,load:null,doubleClick:"",rightClick:"",click:"",renderComplete:null,itemRendering:null,value:"text",themeProperties:{flatlight:{items:{segmentSettings:{color:"#232323"},shadowColor:"#232323",textColor:"#232323"}},flatdark:{items:{segmentSettings:{color:"#b1b0b0"},shadowColor:"#b1b0b0",textColor:"#b1b0b0"}}}},dataTypes:{segmentData:"data",matrixSegmentData:"data",items:"array",isResponsive:"boolean"},_setValues:function(){this.gaugeEl=this.element;this.segmentCount=null;this.contextEl=null;this.style=null;this._value=null;this.region=null;this.canvasEl=null;this.segement16X=null;this.segment16Y=null;this.segmentHeight=null;this.segmentAngle=null;this.startX=5;this.startY=5;this.gradient=null;this.itemIndex=null;this.characterSpace=null;this.outerImage=null;this.radius=null;this.frameOuterLocation=null;this.frameInnerLocation=null;this.glassFrameLocation=null;this.glassFrameStyle=null;this.frameOuterStyle=null;this.character=null;this.frameInnerStyle=null;this._itemInitialize()},observables:["value"],_tags:[{tag:"items",attr:["textAlign","textColor","characterSettings.count","characterSettings.opacity","characterSettings.spacing","characterSettings.type","enableCustomFont","segmentSettings.color","segmentSettings.gradient","segmentSettings.length","segmentSettings.opacity","segmentSettings.spacing","segmentSettings.width","shadowBlur","shadowOffsetX","shadowOffsetY","textAlign","shadowColor","textColor","font.size","font.fontFamily","font.fontStyle","position.x","position.y"]}],value:t.util.valueFunction("value"),_destroy:function(){this._unwireEvents();this.element.empty().removeClass("e-digitalgauge e-js e-widget")},_setModel:function(t){var i,r;for(i in t)switch(i){case"height":this.model.height=t[i];break;case"width":this.model.width=t[i];break;case"items":this.model.items=t[i];this._itemInitialize();break;case"frame":n.extend(this.model.frame,t[i]);break;case"themes":this.model.themes=t[i];break;case"value":for(r=0;this.model.items[r]!=null;r++)this.model.items[r].value=this.value()}this.refresh()},_init:function(){r=n(".e-digitalgauge").length;f=r;this._setValues();this._trigger("load");this._setTheme();this._initialize();this._onWindowResize()},_onWindowResize:function(){(this.model.enableResize||this.model.isResponsive)&&(t.isTouchDevice()?this._on(n(window),"orientationchange",this.resizeCanvas):this._on(n(window),"resize",this.resizeCanvas))},_setTheme:function(){var n=this.model.themeProperties[this.model.themes];this._setThemeColors(n)},_setThemeColors:function(n){var u=[],f=this.model.themeProperties,e,r,i;for(e in f)u.push(e);for(r=0;r<u.length;r++)for(i=0;i<this.model.items.length;i++)this.model.items[i].segmentSettings.color=t.isNullOrUndefined(this.model.items[i].segmentSettings.color)||this.model.items[i].segmentSettings.color==f[u[r]].items.segmentSettings.color?n.items.segmentSettings.color:this.model.items[i].segmentSettings.color,this.model.items[i].shadowColor=!this.model.items[i].shadowColor||this.model.items[i].shadowColor==f[u[r]].items.shadowColor?n.items.shadowColor:this.model.items[i].shadowColor,this.model.items[i].textColor=!this.model.items[i].textColor||this.model.items[i].textColor==f[u[r]].items.textColor?n.items.textColor:this.model.items[i].textColor},_wireEvents:function(){this._on(n(this.canvasEl),"touchstart",this._dgStart);this._on(n(this.canvasEl),t.isTouchDevice()?"touchend":"mouseup",this._dgClick);this._on(n(this.canvasEl),"contextmenu",this._dgRightClick)},_unwireEvents:function(){this._off(n(this.canvasEl),"touchstart",this._dgStart);this._off(n(this.canvasEl),t.isTouchDevice()?"touchend":"mouseup",this._dgClick);this._off(n(this.canvasEl),"contextmenu",this._dgRightClick)},_initialize:function(){this.model.init&&this._clientSideOnInit();this._initObject(this);this.model.load&&this._clientSideOnLoad();this.model.frame.backgroundImageUrl!=null?this._drawCustomImage(this,this.model.frame.backgroundImageUrl):this._renderItems();this.model.renderComplete&&this._clientSideOnRenderComplete();this._wireEvents()},_dgStart:function(){t.isTouchDevice()&&this.model.rightClick!=""&&(this._longPressTimer=new Date)},_dgClick:function(n){var i=new Date;this.model.click!=""&&this._trigger("click",{data:{event:n}});this._doubleTapTimer!=null&&i-this._doubleTapTimer<300&&this._dgDoubleClick(n);this._doubleTapTimer=i;t.isTouchDevice()&&this.model.rightClick!=""&&new Date-this._longPressTimer>1500&&this._dgRightClick(n)},_dgDoubleClick:function(n){this.model.doubleClick!=""&&this._trigger("doubleClick",{data:{event:n}})},_dgRightClick:function(n){this.model.rightClick!=""&&this._trigger("rightClick",{data:{event:n}})},_itemInitialize:function(){var t=this;this.model.items!=null?n.each(this.model.items,function(i,r){var u=t._sendDefaultItem();n.extend(!0,u,r);n.extend(!0,r,u)}):this.model.items=[this._sendDefaultItem()]},_sendDefaultItem:function(){return{characterSettings:{count:4,opacity:1,spacing:2,type:t.datavisualization.DigitalGauge.CharacterType.EightCrossEightDotMatrix},enableCustomFont:!1,segmentSettings:{color:null,gradient:null,length:2,opacity:0,spacing:1,width:1},shadowBlur:0,shadowOffsetX:1,shadowOffsetY:1,textAlign:"left",font:{size:"11px",fontFamily:"Arial",fontStyle:"italic"},position:{x:0,y:0},shadowColor:null,textColor:null,value:null}},_initObject:function(t){var i,e;for(this.element.addClass("e-widget"),t.gaugeEl=this.element,i=0;this.model.items[i]!=null;i++)this.model.items[i].value==null&&(this.model.items[i].value=this.value());(t.canvasEl&&t.canvasEl.parent().empty(),t.canvasEl=n("<canvas><\/canvas>"),t.gaugeEl.append(t.canvasEl),t.canvasEl.attr("role","presentation"),r==f&&(u=window.innerWidth),t.canvasEl[0].setAttribute("width",t.model.width),t.canvasEl[0].setAttribute("height",t.model.height),t.centerX=t.canvasEl[0].width/2,t.centerY=t.canvasEl[0].height/2,e=t.canvasEl[0],typeof G_vmlCanvasManager!="undefined"&&(e=window.G_vmlCanvasManager.initElement(e)),e&&e.getContext)&&(t.contextEl=t.canvasEl[0].getContext("2d"))},_drawCustomImage:function(t,i){var r=new Image;n(r).on("load",function(){t.contextEl.drawImage(this,0,0,t.model.width,t.model.height);t.model.Scales!=null&&t._drawScales();t.model.items!=null&&t._renderItems()}).attr("src",i)},_setSegmentCount:function(n){switch(n){case"sevensegment":this._SegmentCount=7;break;case"fourteensegment":this._SegmentCount=14;break;case"sixteensegment":this._SegmentCount=16;break;case"eightcrosseightdotmatrix":this._SegmentCount=[8,8];break;case"eightcrosseightsquarematrix":this._SegmentCount=[8,8];break;default:this._SegmentCount=7}},_setInnerPosition:function(){this.contextEl.save();this.contextEl.translate(this.model.frame.outerWidth+this.model.frame.innerWidth,this.model.frame.outerWidth+this.model.frame.innerWidth);this.bounds={height:this.canvasEl[0].height-2*(this.model.frame.outerWidth+this.model.frame.innerWidth),width:this.canvasEl[0].width-2*(this.model.frame.outerWidth+this.model.frame.innerWidth)}},_setWidth:function(){var t=[];this.model.items!=null&&n.each(this.model.items,function(n,i){t.push(i.characterSettings.count)})},_renderItems:function(){if(this.model.items!=null){this._setInnerPosition();var t=this;n.each(this.model.items,function(n,i){var r=i.characterSettings.type.toLowerCase();t._setSegmentCount(r);t.itemIndex=n;t.canvasEl.attr("aria-label",i.value);t._setShadow(n,i);i.enableCustomFont?t._setCustomFont(n,i):r.indexOf("matrix")!=-1?t._drawMatrixSegments(n,i):t._drawSegments(n,i)})}},_setGradientColor:function(t,i,r){r.Name||typeof r=="string"?(i.addColorStop(0,this._getColor(t,r)),i.addColorStop(1,this._getColor(t,r))):n.each(r,function(n,r){i.addColorStop(r.colorStop!=NaN?r.colorStop:0,typeof r.color=="string"?r.color:this._getColor(t,r.color))})},_getColor:function(n,t){return typeof t=="string"?t:"rgba("+t.R+", "+t.G+","+t.B+", "+t.A/255+")"},_drawMatrixSegments:function(t,i){var s=[],u=[],o=i.characterSettings.type.toLowerCase(),h,c,f,r,e;for(i.value?(this._value=i.value.toString().split(""),i.characterSettings.count=this._value.length>4?this._value.length:4):this._value="",this.radius=o.indexOf("dot")!=-1?(i.segmentSettings.length+i.segmentSettings.width)/2:i.segmentSettings.width/2,h=this.startX=(this.bounds.width-i.characterSettings.count*(this._SegmentCount[0]*2*this.radius+i.characterSettings.spacing+this._SegmentCount[0]*i.segmentSettings.spacing))*(i.position.x/100),c=this.startY=(this.bounds.height-(this._SegmentCount[1]*(o.indexOf("dot")!=-1?2*this.radius:i.segmentSettings.length)+this._SegmentCount[1]*i.segmentSettings.spacing))*(i.position.y/100),f=0;f<i.characterSettings.count;f++)for(this._value&&(this.character=i.textAlign=="right"?this._value[this._value.length-i.characterSettings.count+f]:this._value[f],u=this.model.matrixSegmentData[this.character]),f!=0&&(h=this.startX=this.startX+i.characterSettings.spacing+i.segmentSettings.spacing+2*this.radius,this.startY=c),r=0;r<this._SegmentCount[1];r++){for(r!=0&&(this.startY=(o.indexOf("dot")!=-1?2*this.radius:i.segmentSettings.length)+this.startY+i.segmentSettings.spacing,this.startX=h),u&&n.each(u,function(n){if(n%2==0){if(u[n]>r)return!1;u[n]==r&&s.push(parseInt(u[n+1]))}}),e=0;e<this._SegmentCount[0];e++)e!=0&&(this.startX=this.startX+2*this.radius+i.segmentSettings.spacing),this.gradient=o.indexOf("dot")!=-1?this.contextEl.createRadialGradient(0,0,0,0,0,this.radius):this.contextEl.createLinearGradient(0,0,i.segmentSettings.width,0),i.segmentSettings.gradient&&this._setGradientColor(this,this.gradient,i.segmentSettings.gradient.colorInfo),this.region={startX:this.startX,startY:this.startY},this.style={opacity:s&&n.inArray(e,s)!=-1?i.characterSettings.opacity:i.segmentSettings.opacity,height:i.segmentSettings.length,width:i.segmentSettings.width,fillStyle:i.segmentSettings.color=="transparent"?"rgba(0,0,0,0)":this._getColor(this,i.segmentSettings.color),skewX:i.SkewAngleX,skewY:i.SkewAngleX},this.model.itemRendering&&this._clientSideOnItemRendering(!0,e,r),o.indexOf("dot")!=-1?this._drawDot(this.region,this.style):this._drawSquare(this.region,this.style);s=[]}},_drawSegments:function(t,i){var f=[],e=i.characterSettings.type.toLowerCase(),u,r;for(i.value&&(this._value=i.value.toUpperCase().toString().split(""),i.characterSettings.count=this._value.length>4?this._value.length:4),this.characterSpace=e=="sevensegment"?2*i.segmentSettings.width:4*i.segmentSettings.width,this._renderSegmentCalculation(i),this.gradient=this.contextEl.createLinearGradient(0,0,0,i.segmentSettings.width),i.segmentSettings.color?this._setGradientColor(this,this.gradient,i.segmentSettings.color):i.segmentSettings.gradient&&this._setGradientColor(this,this.gradient,i.segmentSettings.gradient.colorInfo),u=0;u<i.characterSettings.count;u++){for(i.value&&(f=this.model.segmentData[i.textAlign=="right"?this._value[this._value.length-i.characterSettings.count+u]:this._value[u]]),r=0;r<this._SegmentCount;r++)u!=0&&(this.segment16X[r]=this.segment16X[r]+i.segmentSettings.length+this.characterSpace+i.characterSettings.spacing),this._value&&(this.character=i.textAlign=="right"?this._value[this._value.length-i.characterSettings.count+u]:this._value[u]),this.region={startX:this.segment16X[r],startY:this.segment16Y[r]},this.style={angle:this.angle[r],fillStyle:this.gradient,isStroke:!1,isFill:!0,characterHeight:e=="sevensegment"?i.segmentSettings.length:this.segmentHeight[r],segmentWidth:i.segmentSettings.width,opacity:f&&n.inArray(r,f)!=-1?i.characterSettings.opacity:i.segmentSettings.opacity},this.model.itemRendering&&this._clientSideOnItemRendering(!1,r),this._drawSegmentLayers(this.region,this.style);this._notifyArrayChange&&this._notifyArrayChange("items["+t+"]value",i.value);this.value(i.value);f=[]}},_setCustomFont:function(n,t){this.startX=(this.bounds.width-this._measureText(t.value,0,this._getFontString(this,t.font)).width)*(t.position.x/100);this.startY=(this.bounds.height-this._measureText(t.value,0,this._getFontString(this,t.font)).height)*(t.position.y/100);this.region={startX:this.startX,startY:this.startY};this.style={font:this._getFontString(this,t.font),text:t.value,textColor:t.textColor?t.textColor=="transparent"?"rgba(0,0,0,0)":this._getColor(this,t.textColor):t.segmentSettings.color=="transparent"?"rgba(0,0,0,0)":this._getColor(this,t.segmentSettings.color)};this.model.itemRendering&&this._clientSideOnItemRendering(!1);this._drawText(this.region,this.style)},_getFontString:function(n,t){return(t.size==null?"11px":t.size)+" "+t.fontFamily+" "+(t.fontStyle?t.fontStyle:"")},_renderSegmentCalculation:function(n){var t=n.segmentSettings.length,i=n.segmentSettings.width,u=n.characterSettings.type.toLowerCase(),r;this.startX=(this.bounds.width-n.characterSettings.count*(t+this.characterSpace+n.characterSettings.spacing))*(n.position.x/100);this.startY=(this.bounds.height-2*t-i)*(n.position.y/100);r=u=="sevensegment"?t:t/2;this.segment16X=[this.startX+i/2,this.startX+t+4*i,this.startX+t+4*i,this.startX+i/2,this.startX,this.startX,this.startX+i/2,this.startX+r+2*i,this.startX+2.5*i+r,this.startX+r+2*i,this.startX+t+2.5*i,this.startX+r+2.5*i,this.startX+r+1.5*i,this.startX+1.5*i,this.startX+5*i/2+r,this.startX+2.5*i+r];this.segment16Y=[this.startY,this.startY,this.startY+t+i,this.startY+2*t+2*i,this.startY+t+i,this.startY,this.startY+t+i,this.startY+t+i,this.startY+t+i,this.startY,this.startY+i,this.startY+t+i,this.startY+t+i,this.startY+i,this.startY,this.startY+2*t+2*i];this.segmentHeight=[t/2,t,t,t/2,t,t,t/2,t,t/2,t,t,t,t,t,t/2,t/2];this.angle=[-90,0,0,-90,0,0,-90,0,-90,0,27,-27,27,-27,-90,-90];u=="sevensegment"&&(this.segment16X[2]=this.segment16X[1]=this.startX+t+2*i);u=="fourteensegment"&&(this.segmentHeight[3]=this.segmentHeight[0]=t+2*i)},_drawSegmentLayers:function(n,t){this._contextOpenPath(t,this);this.contextEl.translate(n.startX,n.startY);this.contextEl.rotate(Math.PI*(t.angle/180));this.contextEl.lineTo(0,0);this.contextEl.lineTo(-t.segmentWidth,t.segmentWidth);this.contextEl.lineTo(-t.segmentWidth,t.characterHeight);this.contextEl.lineTo(0,t.characterHeight+t.segmentWidth);this.contextEl.lineTo(t.segmentWidth,t.characterHeight);this.contextEl.lineTo(t.segmentWidth,t.segmentWidth);this._contextClosePath(t,this)},_drawDot:function(n,t){this.contextEl.beginPath();this.contextEl.save();this.contextEl.translate(n.startX,n.startY);this.contextEl.fillStyle=t.fillStyle;this.contextEl.globalAlpha=t.opacity;this.contextEl.arc(0,0,this.radius,0,2*Math.PI,!0);this.contextEl.fill();this.contextEl.closePath();this.contextEl.restore()},_setShadow:function(n,t){this.contextEl.save();this.contextEl.shadowColor=t.shadowColor=="transparent"?"rgba(0,0,0,0)":this._getColor(this,t.shadowColor);this.contextEl.shadowOffsetY=t.shadowOffsetY;this.contextEl.shadowOffsetX=t.shadowOffsetX;this.contextEl.shadowBlur=t.shadowBlur},_drawSquare:function(n,t){this.contextEl.beginPath();this.contextEl.save();this.contextEl.translate(n.startX,n.startY);this.contextEl.fillStyle=t.fillStyle;this.contextEl.globalAlpha=t.opacity;this.contextEl.rect(0,0,t.width,t.height);this.contextEl.fill();this.contextEl.closePath();this.contextEl.restore()},_drawText:function(n,t){this.contextEl.beginPath();this.contextEl.save();this.contextEl.font=t.font;this.contextEl.textBaseline="hanging";this.contextEl.fillStyle=t.textColor=="transparent"?"rgba(0,0,0,0)":t.textColor;this.contextEl.fillText(t.text,n.startX,n.startY);this.contextEl.closePath();this.contextEl.restore()},setValue:function(n,t){n<this.model.items.length&&t!=null&&(this.model.items[n].value=t,this._initialize())},getValue:function(n){return this.model.items[n].value},setPosition:function(n,t){n<this.model.items.length&&t!=null&&(this.model.items[n].position.x=t.x,this.model.items[n].position.y=t.y,this._initialize())},getPosition:function(n){return n<this.model.items.length?{x:this.model.items[n].position.x,y:this.model.items[n].position.y}:null},refresh:function(){this._setTheme();this._initialize()},"export":function(){var i=this.model.exportSettings,u,f,e,r,o,s;i.mode.toLowerCase()==="client"?this.exportImage(i.fileName,i.fileType):(f=i.type.toLowerCase()==="jpg"?"image/jpeg":"image/png",u=this.canvasEl[0].toDataURL(f),e={action:i.action,method:"post"},r=t.buildTag("form","",null,e),o={name:"Data",type:"hidden",value:u},s=t.buildTag("input","",null,o),r.append(s).append(this),n("body").append(r),r.submit())},exportImage:function(n,i){var f,u,o,r,c,s;if(t.browserInfo().name==="msie"&&parseFloat(t.browserInfo().version)<10)return!1;f=this.canvasEl[0].toDataURL();f=f.replace(/^data:[a-z]*;,/,"");var l=f.split(","),e=atob(l[1]),h=new ArrayBuffer(e.length),a=new Uint8Array(h);for(u=0;u<e.length;u++)a[u]=e.charCodeAt(u);return o=new Blob([h],{type:"image/png"}),t.browserInfo().name==="msie"?window.navigator.msSaveOrOpenBlob(o,n+"."+i):(r=document.createElement("a"),c=URL.createObjectURL(o),r.href=c,r.setAttribute("download",n+"."+i),document.createEvent?(s=document.createEvent("MouseEvents"),s.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),r.dispatchEvent(s)):r.fireEvent&&r.fireEvent("onclick")),!0},resizeCanvas:function(){var e,o,f,i;if(r=r!=0?r-1:n(".e-digitalgauge").length-1,o=!0,t.isNullOrUndefined(this.canvasEl.parent().attr("style"))||(e=this.GaugeEl.parent().attr("style").split(";")),t.isNullOrUndefined(e)||n.each(e,function(n,t){while(t.indexOf("width")!=-1){o=t.indexOf("px")==-1?!0:!1;break}}),o){for(f=window.innerWidth/u,this.model.width*=f,i=0;this.model.items[i]!=null;i++)this.model.items[i].segmentSettings.width*=f,this.model.items[i].segmentSettings.length*=f,this.model.items[i].segmentSettings.spacing*=f,this.model.items[i].characterSettings.spacing*=f;this.refresh();r==0&&(u=window.innerWidth)}},_clientSideOnLoad:function(){var n={object:this,items:this.model.items,context:this.contextEl};this._trigger("load",n)},_clientSideOnItemRendering:function(n,t,i){var r;r=n?{object:this,items:this.model.items,character:this.character,context:this.contextEl,position:this.region,style:this.style,row:t,column:i}:{object:this,model:this.model,id:this.model.ClientId,items:this.model.items,character:this.character,context:this.contextEl,position:this.region,style:this.style,segment:t};this._trigger("itemRendering",r)},_clientSideOnInit:function(){var n={object:this,items:this.model.items,context:this.contextEl};this._trigger("init",n)},_clientSideOnRenderComplete:function(){var n={object:this,items:this.model.items,context:this.contextEl};this._trigger("renderComplete",n)},_contextOpenPath:function(n,t){t.contextEl.save();t.contextEl.beginPath();n.strokeStyle&&(t.contextEl.strokeStyle=n.strokeStyle);n.opacity!=i&&(t.contextEl.globalAlpha=n.opacity);n.lineWidth&&(t.contextEl.lineWidth=n.lineWidth);n.fillStyle&&(t.contextEl.fillStyle=n.fillStyle)},_contextClosePath:function(n,t){t.contextEl.closePath();n.isFill&&t.contextEl.fill();n.isStroke&&t.contextEl.stroke();t.contextEl.restore()},_measureText:function(t,i,r){var u=document.createElement("DIV"),f;return u.innerHTML=t,r!=null&&(u.style.font=r),u.style.backgroundColor="white",u.style.position="absolute",u.style.top=-100,u.style.left=0,i&&(u.style.maxwidth=i+"px"),document.body.appendChild(u),f={width:u.offsetWidth,height:u.offsetHeight},n(u).remove(),f}});t.datavisualization.DigitalGauge.CharacterType={SevenSegment:"sevensegment",FourteenSegment:"fourteensegment",SixteenSegment:"sixteensegment",EightCrossEightDotMatrix:"eightcrosseightdotmatrix",EightCrossEightSquareMatrix:"eightcrosseightsquarematrix"};t.datavisualization.DigitalGauge.TextAlign={Left:"left",Right:"right"};t.datavisualization.DigitalGauge.FontStyle={Normal:"normal",Bold:"bold",Italic:"italic",Underline:"underline",Strikeout:"strikeout"};t.datavisualization.DigitalGauge.Themes={FlatLight:"flatlight",FlatDark:"flatdark"}}(jQuery,Syncfusion)});
