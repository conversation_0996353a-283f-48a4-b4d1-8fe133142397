﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Data
{
    public static class FieldValuesMappings
    {
        private static DataSet ds;

        public static DataSet DataSet
        {
            get
            {
                return ds;
            }
        }

        static FieldValuesMappings()
        {
            ds = new DataSet();
            DataTable table;

            #region Contacts
            #region  MaritalStatus 
            table = new DataTable();
            table.TableName = "Contacts-MaritalStatus";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Άγαμος/η", "A" }, true);
            table.LoadDataRow(new object[] { "Έγγαμος/η", "B" }, true);
            table.LoadDataRow(new object[] { "Διαζευμένος/η", "C" }, true);
            table.LoadDataRow(new object[] { "Σε Σύμφωνο", "D" }, true);
            table.LoadDataRow(new object[] { "Σε Σχέση", "E" }, true);
            table.LoadDataRow(new object[] { "Σε Διάσταση", "F" }, true);
            table.LoadDataRow(new object[] { "Μόνος/η", "G" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);

            ds.Tables.Add(table);
            #endregion

            #region  LivingStatus 
            table = new DataTable();
            table.TableName = "Contacts-LivingStatus";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Ζει Μόνος/η", "A" }, true);
            table.LoadDataRow(new object[] { "Ζει με Γονείς", "B" }, true);
            table.LoadDataRow(new object[] { "Ζει με τη Σχέση", "C" }, true);
            table.LoadDataRow(new object[] { "Ζει με Τέκνο", "D" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);

            ds.Tables.Add(table);
            #endregion

            #region SessionOrigin
            table = new DataTable();
            table.TableName = "Contacts-SessionOrigin";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Παραπομπή", "A" }, true);
            table.LoadDataRow(new object[] { "Internet/Site", "B" }, true);
            table.LoadDataRow(new object[] { "Προωθητική Ενέργεια", "C" }, true);
            table.LoadDataRow(new object[] { "Προωθητική Πλατφόρμα", "D" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);

            ds.Tables.Add(table);
            #endregion

            #region Sex
            table = new DataTable();
            table.TableName = "Contacts-Sex";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Άνδρας (CISGENDER)", "A" }, true);
            table.LoadDataRow(new object[] { "Γυναίκα (CISGENDER)", "B" }, true);
            table.LoadDataRow(new object[] { "Τρανς Άνδρας", "C" }, true);
            table.LoadDataRow(new object[] { "Τρανς Γυναίκα", "D" }, true);
            table.LoadDataRow(new object[] { "Non Binary", "E" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);

            ds.Tables.Add(table);
            #endregion

            #region EducationLevel
            table = new DataTable();
            table.TableName = "Contacts-EducationLevel";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Αναλφάβητος", "A" }, true);
            table.LoadDataRow(new object[] { "Δημοτικό", "B" }, true);
            table.LoadDataRow(new object[] { "Γυμνάσιο", "C" }, true);
            table.LoadDataRow(new object[] { "Λύκειο", "D" }, true);
            table.LoadDataRow(new object[] { "Μετεκπαίδευση", "E" }, true);
            table.LoadDataRow(new object[] { "Παν/μιο", "F" }, true);
            table.LoadDataRow(new object[] { "Διδακτορικό", "G" }, true);
            table.LoadDataRow(new object[] { "Μ.Διδακτορικό", "H" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Occupation
            table = new DataTable();
            table.TableName = "Contacts-Occupation";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Αυτοαπασχολούμενος", "A" }, true);
            table.LoadDataRow(new object[] { "Ελ. Επαγγελματίας", "B" }, true);
            table.LoadDataRow(new object[] { "Δημ. Υπάλληλος", "C" }, true);
            table.LoadDataRow(new object[] { "Ιδ. Υπάλληλος", "D" }, true);
            table.LoadDataRow(new object[] { "Εποχικός Υπάλληλος", "E" }, true);
            table.LoadDataRow(new object[] { "Άνεργος", "F" }, true);
            table.LoadDataRow(new object[] { "Εισοδηματίας", "G" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  EconomicStatus
            table = new DataTable();
            table.TableName = "Contacts-EconomicStatus";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Πολύ Καλή", "A" }, true);
            table.LoadDataRow(new object[] { "Καλή", "B" }, true);
            table.LoadDataRow(new object[] { "Μέτρια", "C" }, true);
            table.LoadDataRow(new object[] { "Κακή", "D" }, true);
            table.LoadDataRow(new object[] { "Συνταξιούχος", "E" }, true);
            table.LoadDataRow(new object[] { "Ειδ. Σύνταξη/Βοήθημα", "F" }, true);
            table.LoadDataRow(new object[] { "Χωρίς Πόρους", "G" }, true);
            table.LoadDataRow(new object[] { "Ταμείο Ανεργείας", "H" }, true);
            table.LoadDataRow(new object[] { "Υποστηρίζεται από Άλλους", "I" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  CommunicationMethod
            table = new DataTable();
            table.TableName = "Contacts-CommunicationMethod";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Viber", "A" }, true);
            table.LoadDataRow(new object[] { "SMS", "B" }, true);
            table.LoadDataRow(new object[] { "Whats App", "C" }, true);
            table.LoadDataRow(new object[] { "Skype", "D" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  SessionFrequency
            table = new DataTable();
            table.TableName = "Contacts-SessionFrequency";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Εβδομαδιαία", "A" }, true);
            table.LoadDataRow(new object[] { "Ανά δύο Εβδομάδες", "B" }, true);
            table.LoadDataRow(new object[] { "Μια το Μήνα", "C" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  ReferralToAnotherSpecialist
            table = new DataTable();
            table.TableName = "Contacts-ReferralToAnotherSpecialist";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Ψυχολόγο", "A" }, true);
            table.LoadDataRow(new object[] { "Ψυχίατρο", "B" }, true);
            table.LoadDataRow(new object[] { "Διατροφολόγο", "C" }, true);
            table.LoadDataRow(new object[] { "Ενδοκρινολόγο", "D" }, true);
            table.LoadDataRow(new object[] { "Παθολόγο", "E" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  EyeContact
            table = new DataTable();
            table.TableName = "Contacts-EyeContact";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Σταθερή/Συντονισμένη  ", "A" }, true);
            table.LoadDataRow(new object[] { "Αποφεκτική/Μειωμένη", "B" }, true);
            table.LoadDataRow(new object[] { "Απούσα", "C" }, true);
            table.LoadDataRow(new object[] { "Κυριαρχική", "D" }, true);
            table.LoadDataRow(new object[] { "Επιθετική", "E" }, true);
            table.LoadDataRow(new object[] { "Σαγηνευτική/Προκλητική", "F" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  BodyLanguage
            table = new DataTable();
            table.TableName = "Contacts-BodyLanguage";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Σταθερή/Συντονισμένη", "A" }, true);
            table.LoadDataRow(new object[] { "Αποφεκτική/Μαζεμένη", "B" }, true);
            table.LoadDataRow(new object[] { "Υποτονική", "C" }, true);
            table.LoadDataRow(new object[] { "Υπερκινητική/Νευρική", "D" }, true);
            table.LoadDataRow(new object[] { "Σαγηνευτική/Προκλητική", "Ε" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  VoiceTone
            table = new DataTable();
            table.TableName = "Contacts-VoiceTone";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Σταθερός & συντονισμένος", "A" }, true);
            table.LoadDataRow(new object[] { "Σιγανός", "B" }, true);
            table.LoadDataRow(new object[] { "Αγχώδεις", "C" }, true);
            table.LoadDataRow(new object[] { "Δυνατός", "D" }, true);
            table.LoadDataRow(new object[] { "Αργός", "Ε" }, true);
            table.LoadDataRow(new object[] { "Γρήγορος", "F" }, true);
            table.LoadDataRow(new object[] { "Βαθύς", "G" }, true);
            table.LoadDataRow(new object[] { "Παιδικότητα", "H" }, true);
            table.LoadDataRow(new object[] { "Σαγηνευτικός", "I" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Narration
            table = new DataTable();
            table.TableName = "Contacts-Narration";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Δομημένη", "A" }, true);
            table.LoadDataRow(new object[] { "Οργανωμένη", "B" }, true);
            table.LoadDataRow(new object[] { "Αποδιοργανωμένη", "C" }, true);
            table.LoadDataRow(new object[] { "Σιωπές", "D" }, true);
            table.LoadDataRow(new object[] { "Γλώσσα  άρνησης", "Ε" }, true);
            table.LoadDataRow(new object[] { "Γλώσσα αναθεώρησης", "F" }, true);
            table.LoadDataRow(new object[] { "Γλώσσα συγκάλυψης", "G" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  SexualOrientation
            table = new DataTable();
            table.TableName = "Contacts-SexualOrientation";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Str8", "A" }, true);
            table.LoadDataRow(new object[] { "Gay", "B" }, true);
            table.LoadDataRow(new object[] { "BISEX", "C" }, true);
            table.LoadDataRow(new object[] { "ASEX", "D" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  GeneralRequest
            table = new DataTable();
            table.TableName = "Contacts-GeneralRequest";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Προσωπικές Σχέσεις", "A" }, true);
            table.LoadDataRow(new object[] { "Αγχώδεις/Φοβίες", "B" }, true);
            table.LoadDataRow(new object[] { "Διαθέσεις", "C" }, true);
            table.LoadDataRow(new object[] { "Προσωπικότητα", "D" }, true);
            table.LoadDataRow(new object[] { "Εργασιακά", "E" }, true);
            table.LoadDataRow(new object[] { "Τραύμα", "F" }, true);
            table.LoadDataRow(new object[] { "Ζητήματα Σεξουαλικού Προσ./Φύλου", "G" }, true);
            table.LoadDataRow(new object[] { "Αυτοεκτίμησης", "H" }, true);
            table.LoadDataRow(new object[] { "Αυτοανάπτυξη/Αυτοεκτίμηση", "I" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  HealingExperience
            table = new DataTable();
            table.TableName = "Contacts-HealingExperience";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Έχει Θεραπευτική Εμπειρία", "A" }, true);
            table.LoadDataRow(new object[] { "Δεν Έχει Θεραπευτική Εμπειρία", "B" }, true);
            table.LoadDataRow(new object[] { "Ψυχανάλυση", "C" }, true);
            table.LoadDataRow(new object[] { "Γνωσιακή Συμπεριφορική", "D" }, true);
            table.LoadDataRow(new object[] { "Συνθετική", "E" }, true);
            table.LoadDataRow(new object[] { "Σωματική", "F" }, true);
            table.LoadDataRow(new object[] { "Διαπροσωπική", "G" }, true);
            table.LoadDataRow(new object[] { "Προσωποκεντρική", "H" }, true);
            table.LoadDataRow(new object[] { "Σχηματών", "I" }, true);
            table.LoadDataRow(new object[] { "Υπαρξιακή", "J" }, true);
            table.LoadDataRow(new object[] { "Γκεστάλτ", "K" }, true);
            table.LoadDataRow(new object[] { "Συστημική", "L" }, true);
            table.LoadDataRow(new object[] { "Σωματική", "M" }, true);
            table.LoadDataRow(new object[] { "Κλινική Ύπνωση", "N" }, true);
            table.LoadDataRow(new object[] { "Συμβουλευτική", "O" }, true);
            table.LoadDataRow(new object[] { "Ζεύγους", "P" }, true);
            table.LoadDataRow(new object[] { "Ομαδική", "Q" }, true);
            table.LoadDataRow(new object[] { "Μουσικοθεραπεία", "R" }, true);
            table.LoadDataRow(new object[] { "Χοροθεραπεία", "S" }, true);
            table.LoadDataRow(new object[] { "Εικαστική", "T" }, true);
            table.LoadDataRow(new object[] { "Παιγνιοθεραπεία", "U" }, true);
            table.LoadDataRow(new object[] { "Δραμματοθεραπεία", "V" }, true);
            table.LoadDataRow(new object[] { "Ψυχόδραμα", "W" }, true);
            table.LoadDataRow(new object[] { "Τραύματος", "X" }, true);
            table.LoadDataRow(new object[] { "Κινηματογραφοθεραπεία", "Y" }, true);
            table.LoadDataRow(new object[] { "Διαλεκτική Συμπεριφορική", "Z" }, true);
            table.LoadDataRow(new object[] { "Βιβλιοθεραπεία", "AA" }, true);
            table.LoadDataRow(new object[] { "Φαρμακοθεραπεία", "AB" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  AppointmentsStart
            table = new DataTable();
            table.TableName = "Contacts-AppointmentsStart";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Ναι", "A" }, true);
            table.LoadDataRow(new object[] { "Όχι", "B" }, true);
            table.LoadDataRow(new object[] { "Παραπομπή σε Άλλο Ειδικό", "C" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  AppointmentsFrequency
            table = new DataTable();
            table.TableName = "Contacts-AppointmentsFrequency";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Εβδομαδιαία", "A" }, true);
            table.LoadDataRow(new object[] { "Ανά 2 Εβδομάδες", "B" }, true);
            table.LoadDataRow(new object[] { "Μια το Μήνα", "C" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-InterventionModel
            table = new DataTable();
            table.TableName = "Contacts-InterventionModel";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Ψυχανάλυση", "Psychoanalysis" }, true);
            table.LoadDataRow(new object[] { "Γνωσιακή Συμπεριφορική", "CognitiveBehavioral" }, true);
            table.LoadDataRow(new object[] { "Σωματική", "Physical" }, true);
            table.LoadDataRow(new object[] { "Διαπροσωπική", "Interpersonal" }, true);
            table.LoadDataRow(new object[] { "Σχημάτων", "Shapes" }, true);
            table.LoadDataRow(new object[] { "Υπαρξιακή", "Existential" }, true);
            table.LoadDataRow(new object[] { "Γκεστάλτ", "Gestalt" }, true);
            table.LoadDataRow(new object[] { "Κλινική Ύπνωση", "ClinicalHypnosis" }, true);
            table.LoadDataRow(new object[] { "Συμβουλευτική", "Counseling" }, true);
            table.LoadDataRow(new object[] { "Ζεύγους", "Couple" }, true);
            table.LoadDataRow(new object[] { "Ομαδική", "group" }, true);
            table.LoadDataRow(new object[] { "Μουσικοθεραπεία", "MusicTherapy" }, true);
            table.LoadDataRow(new object[] { "Χοροθεραπεία", "DanceTherapy" }, true);
            table.LoadDataRow(new object[] { "Εικαστική", "Speculative" }, true);
            table.LoadDataRow(new object[] { "Παιγνιοθεραπεία", "PlayTherapy" }, true);
            table.LoadDataRow(new object[] { "Δραμματοθεραπεία", "DramaTherapy" }, true);
            table.LoadDataRow(new object[] { "Ψυχόδραμα", "psychodrama" }, true);
            table.LoadDataRow(new object[] { "Τραύματος", "Trauma" }, true);
            table.LoadDataRow(new object[] { "Κινηματογραφοθεραπεία", "FilmTherapy" }, true);
            table.LoadDataRow(new object[] { "Διαλεκτική Συμπεριφορική", "DialecticalBehavioral" }, true);
            table.LoadDataRow(new object[] { "Βιβλιοθεραπεία", "Bibliotherapy" }, true);
            table.LoadDataRow(new object[] { "Συστημική", "Systemic" }, true);
            table.LoadDataRow(new object[] { "Συνθετική", "Synthetic" }, true);
            table.LoadDataRow(new object[] { "Φαρμακοθεραπεία", "Pharmacotherapy" }, true);
            table.LoadDataRow(new object[] { "Άλλη Παρέμβαση", "Another" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-OtherImportant
            table = new DataTable();
            table.TableName = "Contacts-OtherImportant";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Σύντροφος", "A" }, true);
            table.LoadDataRow(new object[] { "Σύζυγος", "B" }, true);
            table.LoadDataRow(new object[] { "Φίλος", "C" }, true);
            table.LoadDataRow(new object[] { "Παππούς", "D" }, true);
            table.LoadDataRow(new object[] { "Γιαγια", "E" }, true);
            table.LoadDataRow(new object[] { "Θείος", "F" }, true);
            table.LoadDataRow(new object[] { "Θεία", "G" }, true);
            table.LoadDataRow(new object[] { "Ξάδελφος", "H" }, true);
            table.LoadDataRow(new object[] { "Ξαδέλφη", "I" }, true);
            table.LoadDataRow(new object[] { "Άλλος του περιβάλλοντος", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-MotherCharacteristics
            table = new DataTable();
            table.TableName = "Contacts-MotherCharacteristics";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Εσωστρεφής", "A" }, true);
            table.LoadDataRow(new object[] { "Δεσποτική", "B" }, true);
            table.LoadDataRow(new object[] { "Εξωστρεφής", "C" }, true);
            table.LoadDataRow(new object[] { "Δοτική", "D" }, true);
            table.LoadDataRow(new object[] { "Συναισθηματικά Απούσα", "E" }, true);
            table.LoadDataRow(new object[] { "Τιμωριτική", "F" }, true);
            table.LoadDataRow(new object[] { "Απούσα", "G" }, true);
            table.LoadDataRow(new object[] { "Υποτακτική", "H" }, true);
            table.LoadDataRow(new object[] { "Αδιάφορη", "I" }, true);
            table.LoadDataRow(new object[] { "Χαρούμενη", "J" }, true);
            table.LoadDataRow(new object[] { "Δυναμική", "K" }, true);
            table.LoadDataRow(new object[] { "Φοβική", "L" }, true);
            table.LoadDataRow(new object[] { "Θλιμμένη", "M" }, true);
            table.LoadDataRow(new object[] { "Νευρωτική", "N" }, true);
            table.LoadDataRow(new object[] { "Ψυχαναγκαστική", "O" }, true);
            table.LoadDataRow(new object[] { "Ανέκφραστη", "P" }, true);
            table.LoadDataRow(new object[] { "Κοινωνική", "Q" }, true);
            table.LoadDataRow(new object[] { "Αφοσιωμένη", "R" }, true);
            table.LoadDataRow(new object[] { "Ακοινώνητη", "S" }, true);
            table.LoadDataRow(new object[] { "Θρησκόληπτη", "T" }, true);
            table.LoadDataRow(new object[] { "Νοικοκυρά", "U" }, true);
            table.LoadDataRow(new object[] { "Καριερίστρια", "V" }, true);
            table.LoadDataRow(new object[] { "Πεισματάρα", "W" }, true);
            table.LoadDataRow(new object[] { "Υπερπροστατευτική", "X" }, true);
            table.LoadDataRow(new object[] { "Δημοκρατική", "Y" }, true);
            table.LoadDataRow(new object[] { "Εκφραστική Συναισθηματικά", "Z" }, true);
            table.LoadDataRow(new object[] { "Καταθλιπτική", "AA" }, true);
            table.LoadDataRow(new object[] { "Απρόβλεπτη", "AB" }, true);
            table.LoadDataRow(new object[] { "Αυτοκτονική", "AC" }, true);
            table.LoadDataRow(new object[] { "Βίαιη / Ασκούσε σωματική βία", "AD" }, true);
            table.LoadDataRow(new object[] { "Βίαιη / Ασκούσε ψυχολογική βία με τα λόγια", "AE" }, true);
            table.LoadDataRow(new object[] { "Βίαιη / Ασκούσε βία με την σιωπή της", "AF" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-FatherCharacteristics
            table = new DataTable();
            table.TableName = "Contacts-FatherCharacteristics";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Εσωστρεφής", "A" }, true);
            table.LoadDataRow(new object[] { "Δεσποτικός", "B" }, true);
            table.LoadDataRow(new object[] { "Εξωστρεφής", "C" }, true);
            table.LoadDataRow(new object[] { "Δοτικό", "D" }, true);
            table.LoadDataRow(new object[] { "Συναισθηματικά Απών", "E" }, true);
            table.LoadDataRow(new object[] { "Τιμωριτικός", "F" }, true);
            table.LoadDataRow(new object[] { "Απών", "G" }, true);
            table.LoadDataRow(new object[] { "Υποτακτικός", "H" }, true);
            table.LoadDataRow(new object[] { "Αδιάφορος", "I" }, true);
            table.LoadDataRow(new object[] { "Χαρούμενος", "J" }, true);
            table.LoadDataRow(new object[] { "Δυναμικός", "K" }, true);
            table.LoadDataRow(new object[] { "Φοβικός", "L" }, true);
            table.LoadDataRow(new object[] { "Θλιμμένός", "M" }, true);
            table.LoadDataRow(new object[] { "Νευρωτικός", "N" }, true);
            table.LoadDataRow(new object[] { "Ψυχαναγκαστικός", "O" }, true);
            table.LoadDataRow(new object[] { "Ανέκφραστος", "P" }, true);
            table.LoadDataRow(new object[] { "Κοινωνικός", "Q" }, true);
            table.LoadDataRow(new object[] { "Αφοσιωμένος", "R" }, true);
            table.LoadDataRow(new object[] { "Ακοινώνητος", "S" }, true);
            table.LoadDataRow(new object[] { "Θρησκόληπτος", "T" }, true);
            table.LoadDataRow(new object[] { "Νοικοκύρης", "U" }, true);
            table.LoadDataRow(new object[] { "Καριερίστας", "V" }, true);
            table.LoadDataRow(new object[] { "Πεισματάρης", "W" }, true);
            table.LoadDataRow(new object[] { "Υπερπροστατευτικός", "X" }, true);
            table.LoadDataRow(new object[] { "Δημοκρατικός", "Y" }, true);
            table.LoadDataRow(new object[] { "Εκφραστικός Συναισθηματικά", "Z" }, true);
            table.LoadDataRow(new object[] { "Καταθλιπτικός", "AA" }, true);
            table.LoadDataRow(new object[] { "Απρόβλεπτος", "AB" }, true);
            table.LoadDataRow(new object[] { "Αυτοκτονικός", "AC" }, true);
            table.LoadDataRow(new object[] { "Βίαιος / Ασκούσε σωματική βία", "AD" }, true);
            table.LoadDataRow(new object[] { "Βίαιος / Ασκούσε ψυχολογική βία με τα λόγια", "AE" }, true);
            table.LoadDataRow(new object[] { "Βίαιος / Ασκούσε βία με την σιωπή του", "AF" }, true);
            table.LoadDataRow(new object[] { "Διαγνωσμένος/η με κάποια ψυχική ασθένεια", "AG" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-OtherImportantFromMotherFamily
            table = new DataTable();
            table.TableName = "Contacts-OtherImportantFromMotherFamily";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Παππούς", "A" }, true);
            table.LoadDataRow(new object[] { "Γιαγιά", "B" }, true);
            table.LoadDataRow(new object[] { "Θείος", "C" }, true);
            table.LoadDataRow(new object[] { "Θεία", "D" }, true);
            table.LoadDataRow(new object[] { "Ξάδελφος", "E" }, true);
            table.LoadDataRow(new object[] { "Ξαδέλφη", "F" }, true);
            table.LoadDataRow(new object[] { "Άλλος του περιβάλλοντος", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-OtherImportantFromFatherFamily
            table = new DataTable();
            table.TableName = "Contacts-OtherImportantFromFatherFamily";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Παππούς", "A" }, true);
            table.LoadDataRow(new object[] { "Γιαγιά", "B" }, true);
            table.LoadDataRow(new object[] { "Θείος", "C" }, true);
            table.LoadDataRow(new object[] { "Θεία", "D" }, true);
            table.LoadDataRow(new object[] { "Ξάδελφος", "E" }, true);
            table.LoadDataRow(new object[] { "Ξαδέλφη", "F" }, true);
            table.LoadDataRow(new object[] { "Άλλος του περιβάλλοντος", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-MotherFamilyHistory
            table = new DataTable();
            table.TableName = "Contacts-MotherFamilyHistory";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Ιστορικό συγκρουσιακών συμφερόντων", "A" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό διαφωνιών για τον γάμο", "B" }, true);
            table.LoadDataRow(new object[] { "Ενδοοικογενειακή βία", "C" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό μετανάστευσης προς / από άλλη χώρα", "D" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό ptsd", "E" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό βιαιοπραγίας", "F" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό απώλειας ", "G" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό φτώχιας/δυσχερειών", "H" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό σεξουαλικής κακοποίησης / η παρενόχλησης", "I" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό ασθένειας / η ψυχικής νόσου", "J" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό εξωσυζυγικών σχέσεων", "K" }, true);
            table.LoadDataRow(new object[] { "Ιιστορικό προβλημάτων με το νόμο", "L" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό εγκατάλειψης", "M" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό διαζυγίου / χωρισμού", "N" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό υιοθεσίας", "O" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-FatherFamilyHistory
            table = new DataTable();
            table.TableName = "Contacts-FatherFamilyHistory";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Ιστορικό συγκρουσιακών συμφερόντων", "A" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό διαφωνιών για τον γάμο", "B" }, true);
            table.LoadDataRow(new object[] { "Ενδοοικογενειακή βία", "C" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό μετανάστευσης προς / από άλλη χώρα", "D" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό ptsd", "E" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό βιαιοπραγίας", "F" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό απώλειας ", "G" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό φτώχιας/δυσχερειών", "H" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό σεξουαλικής κακοποίησης / η παρενόχλησης", "I" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό ασθένειας / η ψυχικής νόσου", "J" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό εξωσυζυγικών σχέσεων", "K" }, true);
            table.LoadDataRow(new object[] { "Ιιστορικό προβλημάτων με το νόμο", "L" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό εγκατάλειψης", "M" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό διαζυγίου / χωρισμού", "N" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό υιοθεσίας", "O" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-MotherFeedbackInMySuccess
            table = new DataTable();
            table.TableName = "Contacts-MotherFeedbackInMySuccess";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Αδιαφορία", "A" }, true);
            table.LoadDataRow(new object[] { "Χαιρόταν με τη χαρά μου", "B" }, true);
            table.LoadDataRow(new object[] { "Μπορούσες και καλύτερα", "C" }, true);
            table.LoadDataRow(new object[] { "Τιμωρία Έλεγε ότι χαιρόταν αλλά δεν φαινόταν", "D" }, true);
            table.LoadDataRow(new object[] { "Ουδέτερα Φαινόταν αναμενόμενο", "E" }, true);
            table.LoadDataRow(new object[] { "Πως το παθες", "F" }, true);
            table.LoadDataRow(new object[] { "Μείωνε την επιτυχία μου", "G" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-MotherFeedbackInMyFailure
            table = new DataTable();
            table.TableName = "Contacts-MotherFeedbackInMyFailure";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Αδιαφορία", "A" }, true);
            table.LoadDataRow(new object[] { "Στεναχωριόταν το έπαιρνε κατάκαρδα", "B" }, true);
            table.LoadDataRow(new object[] { "Δεν το περίμενα από σένα", "C" }, true);
            table.LoadDataRow(new object[] { "Τιμωρία", "D" }, true);
            table.LoadDataRow(new object[] { "Δεν αναγνώριζε την αποτυχία μου", "E" }, true);
            table.LoadDataRow(new object[] { "Τα έριχνε στους άλλους", "F" }, true);
            table.LoadDataRow(new object[] { "Έκανε σαν να μην συμβαίνει τίποτα", "G" }, true);
            table.LoadDataRow(new object[] { "Μου μίλαγε με κατανόηση και με βοηθούσε να το ξεπεράσω", "H" }, true);
            table.LoadDataRow(new object[] { "Το περίμενα ότι δεν θα τα καταφέρεις", "I" }, true);
            table.LoadDataRow(new object[] { "Ουδέτερα / σαν να ήταν αναμενόμενο", "J" }, true);
            table.LoadDataRow(new object[] { "Μου έπαιρνε δώρα για να με γλυκάνει ", "K" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-FatherFeedbackInMySuccess
            table = new DataTable();
            table.TableName = "Contacts-FatherFeedbackInMySuccess";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Αδιαφορία", "A" }, true);
            table.LoadDataRow(new object[] { "Χαιρόταν με τη χαρά μου", "B" }, true);
            table.LoadDataRow(new object[] { "Μπορούσες και καλύτερα", "C" }, true);
            table.LoadDataRow(new object[] { "Τιμωρία Έλεγε ότι χαιρόταν αλλά δεν φαινόταν", "D" }, true);
            table.LoadDataRow(new object[] { "Ουδέτερα Φαινόταν αναμενόμενο", "E" }, true);
            table.LoadDataRow(new object[] { "Πως το παθες", "F" }, true);
            table.LoadDataRow(new object[] { "Μείωνε την επιτυχία μου", "G" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-FatherFeedbackInMyFailure
            table = new DataTable();
            table.TableName = "Contacts-FatherFeedbackInMyFailure";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Αδιαφορία", "A" }, true);
            table.LoadDataRow(new object[] { "Στεναχωριόταν το έπαιρνε κατάκαρδα", "B" }, true);
            table.LoadDataRow(new object[] { "Δεν το περίμενα από σένα", "C" }, true);
            table.LoadDataRow(new object[] { "Τιμωρία", "D" }, true);
            table.LoadDataRow(new object[] { "Δεν αναγνώριζε την αποτυχία μου", "E" }, true);
            table.LoadDataRow(new object[] { "Τα έριχνε στους άλλους", "F" }, true);
            table.LoadDataRow(new object[] { "Έκανε σαν να μην συμβαίνει τίποτα", "G" }, true);
            table.LoadDataRow(new object[] { "Μου μίλαγε με κατανόηση και με βοηθούσε να το ξεπεράσω", "H" }, true);
            table.LoadDataRow(new object[] { "Το περίμενα ότι δεν θα τα καταφέρεις", "I" }, true);
            table.LoadDataRow(new object[] { "Ουδέτερα / σαν να ήταν αναμενόμενο", "J" }, true);
            table.LoadDataRow(new object[] { "Μου έπαιρνε δώρα για να με γλυκάνει ", "K" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-ImportantFeedbackInMySuccess
            table = new DataTable();
            table.TableName = "Contacts-ImportantFeedbackInMySuccess";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Αδιαφορία", "A" }, true);
            table.LoadDataRow(new object[] { "Χαιρόταν με τη χαρά μου", "B" }, true);
            table.LoadDataRow(new object[] { "Μπορούσες και καλύτερα", "C" }, true);
            table.LoadDataRow(new object[] { "Τιμωρία Έλεγε ότι χαιρόταν αλλά δεν φαινόταν", "D" }, true);
            table.LoadDataRow(new object[] { "Ουδέτερα Φαινόταν αναμενόμενο", "E" }, true);
            table.LoadDataRow(new object[] { "Πως το παθες", "F" }, true);
            table.LoadDataRow(new object[] { "Μείωνε την επιτυχία μου", "G" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-ImportantFeedbackInMyFailure
            table = new DataTable();
            table.TableName = "Contacts-ImportantFeedbackInMyFailure";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Αδιαφορία", "A" }, true);
            table.LoadDataRow(new object[] { "Χαιρόταν με τη χαρά μου", "B" }, true);
            table.LoadDataRow(new object[] { "Μπορούσες και καλύτερα", "C" }, true);
            table.LoadDataRow(new object[] { "Τιμωρία Έλεγε ότι χαιρόταν αλλά δεν φαινόταν", "D" }, true);
            table.LoadDataRow(new object[] { "Ουδέτερα Φαινόταν αναμενόμενο", "E" }, true);
            table.LoadDataRow(new object[] { "Πως το παθες", "F" }, true);
            table.LoadDataRow(new object[] { "Μείωνε την επιτυχία μου", "G" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-AdhesionType
            table = new DataTable();
            table.TableName = "Contacts-AdhesionType";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Ασφαλής", "A" }, true);
            table.LoadDataRow(new object[] { "Αποφευτικός", "B" }, true);
            table.LoadDataRow(new object[] { "Αγχώδης/Συμβιωτικός", "C" }, true);
            table.LoadDataRow(new object[] { "Αποδιοργανομένος/Χαοτικός", "D" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-PreschoolExperiences
            table = new DataTable();
            table.TableName = "Contacts-PreschoolExperiences";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Έμενε πολλές ώρες μόνος στο σπίτι", "A" }, true);
            table.LoadDataRow(new object[] { "Σπίτι με τηλεόραση", "B" }, true);
            table.LoadDataRow(new object[] { "Σπίτι διάβασμα μελέτη", "C" }, true);
            table.LoadDataRow(new object[] { "Έξω με διάφορους φίλους", "D" }, true);
            table.LoadDataRow(new object[] { "Κυρίως με οικογένεια", "E" }, true);
            table.LoadDataRow(new object[] { "Με γιαγιά και παππού", "F" }, true);
            table.LoadDataRow(new object[] { "Γενικά πέρναγε βαρετά", "G" }, true);
            table.LoadDataRow(new object[] { "Έξω στις γειτονιές", "H" }, true);
            table.LoadDataRow(new object[] { "Με έναν ή ελάχιστους φίλους", "I" }, true);
            table.LoadDataRow(new object[] { "Με υπολογιστή", "J" }, true);
            table.LoadDataRow(new object[] { "Με πολλές υποχρεώσεις", "K" }, true);
            table.LoadDataRow(new object[] { "Με παιδικό σταθμό", "L" }, true);
            table.LoadDataRow(new object[] { "Με νταντά", "M" }, true);
            table.LoadDataRow(new object[] { "Δεν θυμάται", "N" }, true);
            table.LoadDataRow(new object[] { "Απομονωμένα", "O" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-SchoolExperiences
            table = new DataTable();
            table.TableName = "Contacts-SchoolExperiences";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Έμενε πολλές ώρες μόνος στο σπίτι", "A" }, true);
            table.LoadDataRow(new object[] { "Σπίτι με τηλεόραση", "B" }, true);
            table.LoadDataRow(new object[] { "Σπίτι διάβασμα μελέτη", "C" }, true);
            table.LoadDataRow(new object[] { "Έξω με διάφορους φίλους", "D" }, true);
            table.LoadDataRow(new object[] { "Κυρίως με οικογένεια", "E" }, true);
            table.LoadDataRow(new object[] { "Με γιαγιά και παππού", "F" }, true);
            table.LoadDataRow(new object[] { "Γενικά πέρναγε βαρετά", "G" }, true);
            table.LoadDataRow(new object[] { "Έξω στις γειτονιές", "H" }, true);
            table.LoadDataRow(new object[] { "Με έναν ή ελάχιστους φίλους", "I" }, true);
            table.LoadDataRow(new object[] { "Με υπολογιστή", "J" }, true);
            table.LoadDataRow(new object[] { "Με πολλές υποχρεώσεις", "K" }, true);
            table.LoadDataRow(new object[] { "Με παιδικό σταθμό", "L" }, true);
            table.LoadDataRow(new object[] { "Με νταντά", "M" }, true);
            table.LoadDataRow(new object[] { "Δεν θυμάται", "N" }, true);
            table.LoadDataRow(new object[] { "Απομονωμένα", "O" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-TeenageExperiences
            table = new DataTable();
            table.TableName = "Contacts-TeenageExperiences";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Έμενε πολλές ώρες μόνος στο σπίτι", "A" }, true);
            table.LoadDataRow(new object[] { "Σπίτι με τηλεόραση", "B" }, true);
            table.LoadDataRow(new object[] { "Σπίτι διάβασμα μελέτη", "C" }, true);
            table.LoadDataRow(new object[] { "Έξω με διάφορους φίλους", "D" }, true);
            table.LoadDataRow(new object[] { "Κυρίως με οικογένεια", "E" }, true);
            table.LoadDataRow(new object[] { "Με γιαγιά και παππού", "F" }, true);
            table.LoadDataRow(new object[] { "Γενικά πέρναγε βαρετά", "G" }, true);
            table.LoadDataRow(new object[] { "Έξω στις γειτονιές", "H" }, true);
            table.LoadDataRow(new object[] { "Με έναν ή ελάχιστους φίλους", "I" }, true);
            table.LoadDataRow(new object[] { "Με σχέση", "J" }, true);
            table.LoadDataRow(new object[] { "Με υπολογιστή", "K" }, true);
            table.LoadDataRow(new object[] { "Με πολλές υποχρεώσεις", "L" }, true);
            table.LoadDataRow(new object[] { "Με παιδικό σταθμό", "M" }, true);
            table.LoadDataRow(new object[] { "Με νταντά", "N" }, true);
            table.LoadDataRow(new object[] { "Δεν θυμάται", "O" }, true);
            table.LoadDataRow(new object[] { "Απομονωμένα", "P" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-AdultExperiences
            table = new DataTable();
            table.TableName = "Contacts-AdultExperiences";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Έμενε πολλές ώρες μόνος στο σπίτι", "A" }, true);
            table.LoadDataRow(new object[] { "Σπίτι με τηλεόραση", "B" }, true);
            table.LoadDataRow(new object[] { "Σπίτι διάβασμα μελέτη", "C" }, true);
            table.LoadDataRow(new object[] { "Έξω με διάφορους φίλους", "D" }, true);
            table.LoadDataRow(new object[] { "Κυρίως με οικογένεια", "E" }, true);
            table.LoadDataRow(new object[] { "Με γιαγιά και παππού", "F" }, true);
            table.LoadDataRow(new object[] { "Γενικά πέρναγε βαρετά", "G" }, true);
            table.LoadDataRow(new object[] { "Έξω στις γειτονιές", "H" }, true);
            table.LoadDataRow(new object[] { "Με έναν ή ελάχιστους φίλους", "I" }, true);
            table.LoadDataRow(new object[] { "Με σχέση", "J" }, true);
            table.LoadDataRow(new object[] { "Με υπολογιστή", "K" }, true);
            table.LoadDataRow(new object[] { "Με πολλές υποχρεώσεις", "L" }, true);
            table.LoadDataRow(new object[] { "Με παιδικό σταθμό", "M" }, true);
            table.LoadDataRow(new object[] { "Με νταντά", "N" }, true);
            table.LoadDataRow(new object[] { "Δεν θυμάται", "O" }, true);
            table.LoadDataRow(new object[] { "Απομονωμένα", "P" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-WorkExperiences
            table = new DataTable();
            table.TableName = "Contacts-WorkExperiences";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Δύσκολα αλλάζει δουλεία", "A" }, true);
            table.LoadDataRow(new object[] { "Γενικά την κάνει διεκπεραιωτικά δεν έχει αλλάξει ποτέ", "B" }, true);
            table.LoadDataRow(new object[] { "Πολλές ώρες στο γραφείο", "C" }, true);
            table.LoadDataRow(new object[] { "Δεν έχει πολλές επαφές με συνάδελφους", "D" }, true);
            table.LoadDataRow(new object[] { "Γενικά έχει καλές σχέσεις με συνάδελφους", "E" }, true);
            table.LoadDataRow(new object[] { "Συνήθως έχει ζητήματα με τα αφεντικά", "F" }, true);
            table.LoadDataRow(new object[] { "Ικανοποιημένος από την εργασία", "G" }, true);
            table.LoadDataRow(new object[] { "Αναλαμβάνει περισσότερα από όσα θα χρειαζόταν να αναλάβει", "H" }, true);
            table.LoadDataRow(new object[] { "Η δουλειά είναι το πιο σημαντικό στη ζωή του", "I" }, true);
            table.LoadDataRow(new object[] { "Δυσαρεστημένος με το μισθό", "J" }, true);
            table.LoadDataRow(new object[] { "Δεν θυμάται", "K" }, true);
            table.LoadDataRow(new object[] { "Βαριέται δεν περνά η ώρα", "L" }, true);
            table.LoadDataRow(new object[] { "Συχνά παθαίνει εξάντληση από την δουλειά", "M" }, true);
            table.LoadDataRow(new object[] { "Συνήθως του λένε ότι δουλεύει πολύ", "N" }, true);
            table.LoadDataRow(new object[] { "Απομονωμένα", "O" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-SpecificTraumaHistory
            table = new DataTable();
            table.TableName = "Contacts-SpecificTraumaHistory";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Καποιο σοβαρό ατύχημα", "A" }, true);
            table.LoadDataRow(new object[] { "Σωματική επίθεση, ξυλοδαρμός, ληστεία", "B" }, true);
            table.LoadDataRow(new object[] { "Πόλεμο", "C" }, true);
            table.LoadDataRow(new object[] { "Φυσική καταστροφή", "D" }, true);
            table.LoadDataRow(new object[] { "Σεξουαλική κακοποίηση(βιασμός), σεξουαλική παρενόχληση", "E" }, true);
            table.LoadDataRow(new object[] { "Απώλεια(θάνατος) ενός συγγενούς ή φίλου", "F" }, true);
            table.LoadDataRow(new object[] { "Ήταν μπροστά σε ένα σοβαρό ατύχημα ή σε μια φυσική καταστροφή", "G" }, true);
            table.LoadDataRow(new object[] { "Πληροφορήθηκε ότι ένας φίλος ή ένα μέλος της οικογένειας έχει βιώσει ένα απειλητικό γεγονός ή έχει πεθάνει ξαφνικά.", "H" }, true);
            table.LoadDataRow(new object[] { "Bullying", "I" }, true);
            table.LoadDataRow(new object[] { "Mobbing", "J" }, true);
            table.LoadDataRow(new object[] { "Ασθένεια", "K" }, true);
            table.LoadDataRow(new object[] { "Ασθένεια σημαντικού άλλου", "L" }, true);
            table.LoadDataRow(new object[] { "Οικονομικές δυσπραγίες", "M" }, true);
            table.LoadDataRow(new object[] { "Μετανάστευση", "N" }, true);
            table.LoadDataRow(new object[] { "Αλλαγή περιβάλλοντος", "O" }, true);
            table.LoadDataRow(new object[] { "Περιγεννητικά", "P" }, true);
            table.LoadDataRow(new object[] { "Οικογενειακή βία", "Q" }, true);
            table.LoadDataRow(new object[] { "Αντίληψη εαυτού", "R" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-Developmental
            table = new DataTable();
            table.TableName = "Contacts-Developmental";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Αναπτυξιακές - Aυτισμός(Autism)", "A" }, true);
            table.LoadDataRow(new object[] { "Αναπτυξιακές - Σύνδρομο Asperger(Asperger Syndrome)", "B" }, true);
            table.LoadDataRow(new object[] { "Αναπτυξιακές - Σύνδρομο Rett(Rett Syndrome)", "C" }, true);
            table.LoadDataRow(new object[] { "Αναπτυξιακές - Παιδική Αποδιοργανωτική Διαταραχή(Childhood Disintegrative Disorder)", "D" }, true);
            table.LoadDataRow(new object[] { "Αναπτυξιακές - Διάχυτη Αναπτυξιακή Διαταραχή - μη προσδιοριζόμενη αλλιώς(P.D.D.-N.O.S., Pervasive Developmental Disorder - not otherwise specified)", "E" }, true);
            table.LoadDataRow(new object[] { "Αναπτυξιακές - Άλλο", "DevOther" }, true);
            table.LoadDataRow(new object[] { "Νευροαναπτυξιακές - Διάσπαση προσοχής(ελλειμματική προσοχή)", "F" }, true);
            table.LoadDataRow(new object[] { "Νευροαναπτυξιακές - Υπερκινητικότητα", "G" }, true);
            table.LoadDataRow(new object[] { "Νευροαναπτυξιακές - Παρορμητικότητα", "H" }, true);
            table.LoadDataRow(new object[] { "Νευροαναπτυξιακές - Άλλο", "NeurOther" }, true);
            table.LoadDataRow(new object[] { "Μαθησιακές - Δυσλεξία", "I" }, true);
            table.LoadDataRow(new object[] { "Μαθησιακές - Δυσαριθμησία", "J" }, true);
            table.LoadDataRow(new object[] { "Μαθησιακές - Δυσγραφία", "K" }, true);
            table.LoadDataRow(new object[] { "Μαθησιακές - Άλλο", "LearnOther" }, true);
            ds.Tables.Add(table);
            #endregion

            //#region  Contacts-Neurodevelopmental
            //table = new DataTable();
            //table.TableName = "Contacts-Neurodevelopmental";
            //table.Columns.Add("Text");
            //table.Columns.Add("Value");

            //table.LoadDataRow(new object[] { "Διάσπαση προσοχής(ελλειμματική προσοχή)", "A" }, true);
            //table.LoadDataRow(new object[] { "Υπερκινητικότητα", "B" }, true);
            //table.LoadDataRow(new object[] { "Παρορμητικότητα", "C" }, true);
            //table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            //ds.Tables.Add(table);
            //#endregion

            //#region  Contacts-Learnings
            //table = new DataTable();
            //table.TableName = "Contacts-Learnings";
            //table.Columns.Add("Text");
            //table.Columns.Add("Value");

            //table.LoadDataRow(new object[] { "Δυσλεξία", "A" }, true);
            //table.LoadDataRow(new object[] { "Δυσαριθμησία", "B" }, true);
            //table.LoadDataRow(new object[] { "Δυσγραφία", "C" }, true);
            //table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            //ds.Tables.Add(table);
            //#endregion

            #region  Contacts-EmotionalDifficulties
            table = new DataTable();
            table.TableName = "Contacts-EmotionalDifficulties";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "test1", "A" }, true);
            table.LoadDataRow(new object[] { "test2", "B" }, true);
            table.LoadDataRow(new object[] { "test3", "C" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-EatingDisorder
            table = new DataTable();
            table.TableName = "Contacts-EatingDisorder";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Ψυχογενής ανορεξία - περιοριστικού τύπου", "A" }, true);
            table.LoadDataRow(new object[] { "Ψυχογενής ανορεξία - με συμπεριφορές υπερφαγίας/κάθαρσης", "B" }, true);
            table.LoadDataRow(new object[] { "Ψυχογενής ανορεξία - Άλλο", "AnorexiaOther" }, true);
            table.LoadDataRow(new object[] { "Ψυχογενής Βουλιμία", "C" }, true);
            table.LoadDataRow(new object[] { "Επεισοδιακή Υπερφαγία", "D" }, true);
            table.LoadDataRow(new object[] { "Διατροφ. Διαταρ. μη προσδιοριζόμενες (OSFED)", "E" }, true);
            table.LoadDataRow(new object[] { "Αλλοτριοφαγία (Pica)", "F" }, true);
            table.LoadDataRow(new object[] { "Διαταραχή Μηρυκασμού (Rumination disorder)", "G" }, true);
            table.LoadDataRow(new object[] { "Αποφευκτική/Περιοριστική διαταραχή πρόσληψης τροφής (Avoidant/Restrictive food intake disorder)", "H" }, true);
            table.LoadDataRow(new object[] { "Oρθορεξία", "I" }, true);
            ds.Tables.Add(table);
            #endregion

            //#region  Contacts-Anorexia
            //table = new DataTable();
            //table.TableName = "Contacts-Anorexia";
            //table.Columns.Add("Text");
            //table.Columns.Add("Value");

            //table.LoadDataRow(new object[] { "Ψυχογενής ανορεξία περιοριστικού τύπου", "A" }, true);
            //table.LoadDataRow(new object[] { "Ψυχογενής ανορεξία με συμπεριφορές υπερφαγίας/κάθαρσης", "B" }, true);
            //table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            //ds.Tables.Add(table);
            //#endregion

            #region  Contacts-Moods
            table = new DataTable();
            table.TableName = "Contacts-Moods";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Κατάθλιψη Μείζωνα (Καταθλιπτική διάθεση, μελαγχολία)", "A" }, true);
            table.LoadDataRow(new object[] { "Κατάθλιψη Μείζωνα (Απώλεια του ενδιαφέροντος ή μείωση της ευχαρίστησης από δραστηριότητες που τον ευχαριστούσαν στο παρελθόν, ανηδονία)", "B" }, true);
            table.LoadDataRow(new object[] { "Κατάθλιψη Μείζωνα (Σημαντική αλλαγή όρεξης ή και βάρους [συνήθως μείωση λιγότερο συχνά αύξηση])", "C" }, true);
            table.LoadDataRow(new object[] { "Κατάθλιψη Μείζωνα (Αϋπνία ή λιγότερο συχνά υπερυπνία)", "D" }, true);
            table.LoadDataRow(new object[] { "Κατάθλιψη Μείζωνα (Ψυχοκινητική ανησυχία / επιβράδυνση ή και άγχος)", "E" }, true);
            table.LoadDataRow(new object[] { "Κατάθλιψη Μείζωνα (Εύκολη κόπωση ή απώλεια της ενεργητικότητας),", "F" }, true);
            table.LoadDataRow(new object[] { "Κατάθλιψη Μείζωνα (Ιδέες αναξιότητας, υπέρμετρης ή απρόσφορης ενοχής)", "G" }, true);
            table.LoadDataRow(new object[] { "Κατάθλιψη Μείζωνα (Μειωμένη ικανότητα συγκέντρωσης, βραδύτητα στη σκέψη και δυσχέρεια στη λήψη των αποφάσεων)", "H" }, true);
            table.LoadDataRow(new object[] { "Κατάθλιψη Μείζωνα (Επαναλαμβανόμενες σκέψεις θανάτου ή ιδέες αυτοκαταστροφής(αυτοκτονικός ιδεασμός))", "I" }, true);
            table.LoadDataRow(new object[] { "Κατάθλιψη Μείζωνα (Άλλο)", "Other1" }, true);
            table.LoadDataRow(new object[] { "Κυκλοθυμία", "J" }, true);
            table.LoadDataRow(new object[] { "Διπολική", "K" }, true);
            
            table.LoadDataRow(new object[] { "Δυσθυμία (Καταθλιπτική διάθεση 2 ετών με πιθανά μικρότερη ένταση)", "L" }, true);
            table.LoadDataRow(new object[] { "Δυσθυμία (Βαριά κατάθλιψη)", "M" }, true);
            table.LoadDataRow(new object[] { "Δυσθυμία (Δεν αντιδρά στα εξωτερικά ερεθίσματα)", "N" }, true);
            table.LoadDataRow(new object[] { "Δυσθυμία (Δεν αντιδρά σε θεραπείες)", "O" }, true);
            table.LoadDataRow(new object[] { "Δυσθυμία (Άλλο)", "Other2" }, true);

            table.LoadDataRow(new object[] { "Μελαγχολική (έμφαση στη βαρύτητα)", "P" }, true);
            table.LoadDataRow(new object[] { "Κατατονική (έμφαση στη βαρύτητα)", "Q" }, true);
            table.LoadDataRow(new object[] { "Ανθεκτική στη Θεραπεία", "R" }, true);
            table.LoadDataRow(new object[] { "Υποτροπιάζουσα", "S" }, true);
            table.LoadDataRow(new object[] { "Χρόνια", "T" }, true);
            table.LoadDataRow(new object[] { "Εποχική", "U" }, true);
            table.LoadDataRow(new object[] { "Επιλόχεια", "V" }, true);
            table.LoadDataRow(new object[] { "Εγκυμοσύνης", "W" }, true);
            table.LoadDataRow(new object[] { "Στην Τρίτη Ηλικία (Άρνηση ή υποβάθμιση του αρνητικού συναισθήματος)", "X" }, true);
            table.LoadDataRow(new object[] { "Στην Τρίτη Ηλικία (Περισσότερο άγχος)", "Y" }, true);
            table.LoadDataRow(new object[] { "Στην Τρίτη Ηλικία (Πιο συχνά σωματικά συμπτώματα κατάθλιψης όπως για παράδειγμα πόνοι τύπου αρθρίτιδας, δερματολογικά, γαστρεντερικά κ.α.)", "Z" }, true);
            table.LoadDataRow(new object[] { "Στην Τρίτη Ηλικία (Συχνότερη εκδήλωση γνωσιακών διαταραχών όπως μνήμης, προσοχής, συγκέντρωσης, προσανατολισμού (καταθλιπτική ψευδοάνοια))", "AA" }, true);
            table.LoadDataRow(new object[] { "Στην Τρίτη Ηλικία (Συχνότερες διαταραχές συμπεριφοράς & ψυχοκινητικότητας όπως απάθεια, επιβράδυνση ή έντονη ψυχοκινητική ανησυχία)", "AB" }, true);
            table.LoadDataRow(new object[] { "Στην Τρίτη Ηλικία (διαταραχές ύπνου γενικά)", "AC" }, true);
            table.LoadDataRow(new object[] { "Στην Τρίτη Ηλικία (Άλλο)", "Other3" }, true);

            table.LoadDataRow(new object[] { "Άτυπη (Υπερυπνία)", "AD" }, true);
            table.LoadDataRow(new object[] { "Άτυπη (Ημερήσια υπνηλία)", "AE" }, true);
            table.LoadDataRow(new object[] { "Άτυπη (Αίσθημα βάρους στα άκρα)", "AF" }, true);
            table.LoadDataRow(new object[] { "Άτυπη (Άύξηση όρεξης)", "AG" }, true);
            table.LoadDataRow(new object[] { "Άτυπη (Υπερφαγία)", "AH" }, true);
            table.LoadDataRow(new object[] { "Άτυπη (Αύξηση βάρους)", "AI" }, true);
            table.LoadDataRow(new object[] { "Άτυπη (Υψηλή ευαισθησία στην απόριψη)", "AJ" }, true);
            table.LoadDataRow(new object[] { "Άτυπη (Μειωμένη/αυξημένη όρεξη)", "AK" }, true);
            table.LoadDataRow(new object[] { "Άτυπη (Αυπνία)", "AL" }, true);
            table.LoadDataRow(new object[] { "Άτυπη (Υπερυπνία)", "AM" }, true);
            table.LoadDataRow(new object[] { "Άτυπη (Κόπωση/μειωμένη ενεργητικότητα)", "AN" }, true);
            table.LoadDataRow(new object[] { "Άτυπη (Μειωμένη αυτοπεποίθηση)", "AO" }, true);
            table.LoadDataRow(new object[] { "Άτυπη (Μειωμένη συγκέντρωση/ικανότητα λήψης αποφάσεων)", "AP" }, true);
            table.LoadDataRow(new object[] { "Άτυπη (Αίσθημα απελπισίας", "AQ" }, true);
            table.LoadDataRow(new object[] { "Άτυπη (Άλλο)", "Other4" }, true);

            table.LoadDataRow(new object[] { "Ψυχωτική Κατάθλιψη (Συμπτώματα μείζονος)", "AR" }, true);
            table.LoadDataRow(new object[] { "Ψυχωτική Κατάθλιψη (Παραλήρημα αυτομομφής ή μηδενιστικό)", "AS" }, true);
            table.LoadDataRow(new object[] { "Ψυχωτική Κατάθλιψη (Ακουστικές Ψευδαισθήσεις ίσως σε σχέση με την ενοχή)", "AT" }, true);
            table.LoadDataRow(new object[] { "Ψυχωτική Κατάθλιψη (Πιο έντονη ψυχοκινητική διαταραχή με σφοδρό συναίσθημα ενοχής)", "AU" }, true);
            table.LoadDataRow(new object[] { "Ψυχωτική Κατάθλιψη (Καταθλιπτικά συμπτώματα μικρής έντασης και μικρής διάρκειας για τουλάχιστον 2 εβδομάδες)", "AV" }, true);
            table.LoadDataRow(new object[] { "Ψυχωτική Κατάθλιψη (Συμπτώματα όπως της μείζονος κατάθλιψης αλλά με μικρή διάρκεια 2 εβδομάδων το μέγιστο)", "AW" }, true);
            table.LoadDataRow(new object[] { "Ψυχωτική Κατάθλιψη (Καταθλιπτικά συμπτώματα μικρής έντασης σε γυναίκες με έναρξη λίγες μέρες πριν την έμμηνο ρύση ως λίγες μέρες μετά)", "AX" }, true);
            table.LoadDataRow(new object[] { "Ψυχωτική Κατάθλιψη (Σύνδρομο που μεταξύ των καταθλιπτικών συμπτωμάτων υπερτερεί η διαταραχή των γνωστικών λειτουργιών όπως η μνήμη, η προσοχή, η συγκέντρωση και προσομοιάζει με άνοια)", "AY" }, true);
            table.LoadDataRow(new object[] { "Ψυχωτική Κατάθλιψη (Άλλο)", "Other5" }, true);

            table.LoadDataRow(new object[] { "Ελάσσων Καταθλιπτική Διαταραχή", "AZ" }, true);
            table.LoadDataRow(new object[] { "Βραχεία Διαλείπουσα Μορφή", "BA" }, true);
            table.LoadDataRow(new object[] { "Προεμμηνορρυσιακή Δυσφορική Διαταραχή", "BB" }, true);
            table.LoadDataRow(new object[] { "Καταθλιπτική Ψευδοάνοια", "BC" }, true);
            ds.Tables.Add(table);
            #endregion

            //#region  Contacts-MajorDepression
            //table = new DataTable();
            //table.TableName = "Contacts-MajorDepression";
            //table.Columns.Add("Text");
            //table.Columns.Add("Value");

            //table.LoadDataRow(new object[] { "Καταθλιπτική διάθεση, μελαγχολία", "A" }, true);
            //table.LoadDataRow(new object[] { "Απώλεια του ενδιαφέροντος ή μείωση της ευχαρίστησης από δραστηριότητες που τον ευχαριστούσαν στο παρελθόν, ανηδονία", "B" }, true);
            //table.LoadDataRow(new object[] { "Σημαντική αλλαγή όρεξης ή και βάρους(συνήθως μείωση λιγότερο συχνά αύξηση)", "C" }, true);
            //table.LoadDataRow(new object[] { "Αϋπνία ή λιγότερο συχνά υπερυπνία", "D" }, true);
            //table.LoadDataRow(new object[] { "Ψυχοκινητική ανησυχία / επιβράδυνση ή και άγχος", "E" }, true);
            //table.LoadDataRow(new object[] { "Εύκολη κόπωση ή απώλεια της ενεργητικότητας,", "F" }, true);
            //table.LoadDataRow(new object[] { "Ιδέες αναξιότητας, υπέρμετρης ή απρόσφορης ενοχής,", "G" }, true);
            //table.LoadDataRow(new object[] { "Μειωμένη ικανότητα συγκέντρωσης, βραδύτητα στη σκέψη και δυσχέρεια στη λήψη των αποφάσεων,", "H" }, true);
            //table.LoadDataRow(new object[] { "Επαναλαμβανόμενες σκέψεις θανάτου ή ιδέες αυτοκαταστροφής(αυτοκτονικός ιδεασμός),", "I" }, true);
            //table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            //ds.Tables.Add(table);
            //#endregion

            //#region  Contacts-Cyclothymia
            //table = new DataTable();
            //table.TableName = "Contacts-Cyclothymia";
            //table.Columns.Add("Text");
            //table.Columns.Add("Value");

            //table.LoadDataRow(new object[] { "Test 1", "A" }, true);
            //table.LoadDataRow(new object[] { "Test 2", "B" }, true);
            //table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            //ds.Tables.Add(table);
            //#endregion

            //#region  Contacts-Dysthymia
            //table = new DataTable();
            //table.TableName = "Contacts-Dysthymia";
            //table.Columns.Add("Text");
            //table.Columns.Add("Value");

            //table.LoadDataRow(new object[] { "Καταθλιπτική διάθεση 2 ετών με πιθανά μικρότερη ένταση", "A" }, true);
            //table.LoadDataRow(new object[] { "Βαριά κατάθλιψη", "B" }, true);
            //table.LoadDataRow(new object[] { "Δεν αντιδρά στα εξωτερικά ερεθίσματα", "C" }, true);
            //table.LoadDataRow(new object[] { "Δεν αντιδρά σε θεραπείες", "D" }, true);
            //table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            //ds.Tables.Add(table);
            //#endregion

            //#region  Contacts-ThirdAge
            //table = new DataTable();
            //table.TableName = "Contacts-ThirdAge";
            //table.Columns.Add("Text");
            //table.Columns.Add("Value");

            //table.LoadDataRow(new object[] { "Άρνηση ή υποβάθμιση του αρνητικού συναισθήματος", "A" }, true);
            //table.LoadDataRow(new object[] { "Περισσότερο άγχος", "B" }, true);
            //table.LoadDataRow(new object[] { "Πιο συχνά σωματικά συμπτώματα κατάθλιψης όπως για παράδειγμα πόνοι τύπου αρθρίτιδας, δερματολογικά, γαστρεντερικά κ.α.", "C" }, true);
            //table.LoadDataRow(new object[] { "Συχνότερη εκδήλωση γνωσιακών διαταραχών όπως μνήμης, προσοχής, συγκέντρωσης, προσανατολισμού (καταθλιπτική ψευδοάνοια)", "D" }, true);
            //table.LoadDataRow(new object[] { "Συχνότερες διαταραχές συμπεριφοράς & ψυχοκινητικότητας όπως απάθεια, επιβράδυνση ή έντονη ψυχοκινητική ανησυχία", "E" }, true);
            //table.LoadDataRow(new object[] { "διαταραχές ύπνου γενικά", "F" }, true);
            //table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            //ds.Tables.Add(table);
            //#endregion

            //#region  Contacts-Informal
            //table = new DataTable();
            //table.TableName = "Contacts-Informal";
            //table.Columns.Add("Text");
            //table.Columns.Add("Value");

            //table.LoadDataRow(new object[] { "Υπερυπνία", "A" }, true);
            //table.LoadDataRow(new object[] { "Ημερήσια υπνηλία", "B" }, true);
            //table.LoadDataRow(new object[] { "Αίσθημα βάρους στα άκρα", "C" }, true);
            //table.LoadDataRow(new object[] { "Άύξηση όρεξης", "D" }, true);
            //table.LoadDataRow(new object[] { "Υπερφαγία", "E" }, true);
            //table.LoadDataRow(new object[] { "Αύξηση βάρους", "F" }, true);
            //table.LoadDataRow(new object[] { "Υψηλή ευαισθησία στην απόριψη", "G" }, true);
            //table.LoadDataRow(new object[] { "Μειωμένη/αυξημένη όρεξη", "H" }, true);
            //table.LoadDataRow(new object[] { "Αυπνία", "I" }, true);
            //table.LoadDataRow(new object[] { "Υπερυπνία", "J" }, true);
            //table.LoadDataRow(new object[] { "Κόπωση/μειωμένη ενεργητικότητα", "K" }, true);
            //table.LoadDataRow(new object[] { "Μειωμένη αυτοπεποίθηση", "L" }, true);
            //table.LoadDataRow(new object[] { "Μειωμένη συγκέντρωση/ικανότητα λήψης αποφάσεων", "M" }, true);
            //table.LoadDataRow(new object[] { "Αίσθημα απελπισίας", "N" }, true);
            //table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            //ds.Tables.Add(table);
            //#endregion

            //#region  Contacts-PsychoticDepression
            //table = new DataTable();
            //table.TableName = "Contacts-PsychoticDepression";
            //table.Columns.Add("Text");
            //table.Columns.Add("Value");

            //table.LoadDataRow(new object[] { "Συμπτώματα μείζονος", "A" }, true);
            //table.LoadDataRow(new object[] { "Παραλήρημα αφτομομφής ή μηδενιστικό", "B" }, true);
            //table.LoadDataRow(new object[] { "Ακουστικές Ψευδαισθήσεις ίσως σε σχέση με την ενοχή", "C" }, true);
            //table.LoadDataRow(new object[] { "Πιο έντονη ψυχοκινητική διαταραχή με σφοδρό συναίσθημα ενοχής", "D" }, true);
            //table.LoadDataRow(new object[] { "Καταθλιπτικά συμπτώματα μικρής έντασης και μικρής διάρκειας για τουλάχιστον 2 εβδομάδες", "E" }, true);
            //table.LoadDataRow(new object[] { "Συμπτώματα όπως της μείζονος κατάθλιψης αλλά με μικρή διάρκεια 2 εβδομάδων το μέγιστο", "F" }, true);
            //table.LoadDataRow(new object[] { "Καταθλιπτικά συμπτώματα μικρής έντασης σε γυναίκες με έναρξη λίγες μέρες πριν την έμμηνο ρύση ως λίγες μέρες μετά", "G" }, true);
            //table.LoadDataRow(new object[] { "Σύνδρομο που μεταξύ των καταθλιπτικών συμπτωμάτων υπερτερεί η διαταραχή των γνωστικών λειτουργιών όπως η μνήμη, η προσοχή, η συγκέντρωση και προσομοιάζει με άνοια", "H" }, true);
            //table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            //ds.Tables.Add(table);
            //#endregion

            #region  Contacts-Anxiety
            table = new DataTable();
            table.TableName = "Contacts-Anxiety";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Μυική Τάση [Σφιγμένοι μύες])", "A" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Πόνοι)", "B" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Κεφαλαλγία Τάσεως [αίσθημα πίεσης στο κεφάλι]/Ημικρανία)", "C" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Διαταραχές ύπνου : Αϋπνία, Πρώιμη/Ενδιάμεση αφύπνιση/Μείωση ποιότητας)", "D" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Αδυναμία/Άτονία)", "E" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Κόπωση)", "F" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Μυικά Τινάγματα/Μουδιάσματα/Μυρμηγκιάσματα)", "G" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Ινομυαλγία)", "H" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Απώλεια όρεξης)", "I" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Υπερφαγία [Binge eating])", "J" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Ναυτία/Έμετος)", "K" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Στομαχικές ενοχλήσεις/Δυσπεψία/Μετεωρισμός [Φούσκωμα])", "L" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Γαστρίτιδα)", "M" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Γαστροοισοφαγική Παλινδρόμηση [ΓΟΠ])", "N" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Έπειξη ούρησης/ Συχνοουρία/Δυσουρία)", "O" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Διαταραχές λίμπιντο/στύσης)", "P" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Διάρροια/Δυσκοιλιότητα)", "Q" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Ευερέθιστο Έντερο)", "R" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Συμφόρηση μύτης/φάρυγγα)", "S" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Ξηροστομία)", "T" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Δύσπνοια/Ταχύπνοια)", "U" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Αίσθημα πνιγμού)", "V" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Κόμπος/αίσθημα ξένου σώματος στο λαιμό)", "W" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Βάρος στο στήθος)", "X" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Πόνος στο στήθος)", "Y" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Ταχυκαρδία[ταχυπαλμία]/αίσθημα παλμών)", "Z" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Επιδείνωση αρρυθμιών)", "AA" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Υπέρταση [νευροπίεση, συστολική/διαστολική πίεση])", "AB" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Ζάλη/ίλιγγος [ψυχογενής])", "AC" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Τάση λιποθυμίας)", "AD" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Αστάθεια)", "AE" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Εξάψεις)", "AF" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Εφίδρωση)", "AG" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Ερυθρότητα)", "AH" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Τρόμος [τρέμουλο], μυϊκές συσπάσεις)", "AI" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Εξανθήματα στο σώμα [π.χ. νευροδερματίτιδα, λεύκη, ψωρίαση, αλωπεκία κ.α.])", "AJ" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Αυτοάνοσες διαταραχές [π.χ. λύκος])", "AK" }, true);
            table.LoadDataRow(new object[] { "Άγχος Σωματικά Συμπτώματα (Άλλο)", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            //#region  Contacts-AnxietyPhysicalSymptoms
            //table = new DataTable();
            //table.TableName = "Contacts-AnxietyPhysicalSymptoms";
            //table.Columns.Add("Text");
            //table.Columns.Add("Value");

            //table.LoadDataRow(new object[] { "Μυική Τάση (Σφιγμένοι μύες)", "A" }, true);
            //table.LoadDataRow(new object[] { "Πόνοι", "B" }, true);
            //table.LoadDataRow(new object[] { "Κεφαλαλγία Τάσεως (αίσθημα πίεσης στο κεφάλι)/Ημικρανία", "C" }, true);
            //table.LoadDataRow(new object[] { "Διαταραχές ύπνου : Αϋπνία, Πρώιμη/Ενδιάμεση αφύπνιση/Μείωση ποιότητας", "D" }, true);
            //table.LoadDataRow(new object[] { "Αδυναμία/Άτονία", "E" }, true);
            //table.LoadDataRow(new object[] { "Κόπωση", "F" }, true);
            //table.LoadDataRow(new object[] { "Μυικά Τινάγματα/Μουδιάσματα/Μυρμηγκιάσματα", "G" }, true);
            //table.LoadDataRow(new object[] { "Ινομυαλγία", "H" }, true);
            //table.LoadDataRow(new object[] { "Απώλεια όρεξης", "I" }, true);
            //table.LoadDataRow(new object[] { "Υπερφαγία (Binge eating)", "J" }, true);
            //table.LoadDataRow(new object[] { "Ναυτία/Έμετος", "K" }, true);
            //table.LoadDataRow(new object[] { "Στομαχικές ενοχλήσεις/Δυσπεψία/Μετεωρισμός (Φούσκωμα)", "L" }, true);
            //table.LoadDataRow(new object[] { "Γαστρίτιδα", "M" }, true);
            //table.LoadDataRow(new object[] { "Γαστροοισοφαγική Παλινδρόμηση (ΓΟΠ)", "N" }, true);
            //table.LoadDataRow(new object[] { "Έπειξη ούρησης/ Συχνοουρία/Δυσουρία", "O" }, true);
            //table.LoadDataRow(new object[] { "Διαταραχές λίμπιντο/στύσης", "P" }, true);
            //table.LoadDataRow(new object[] { "Διάρροια/Δυσκοιλιότητα", "Q" }, true);
            //table.LoadDataRow(new object[] { "Ευερέθιστο Έντερο", "R" }, true);
            //table.LoadDataRow(new object[] { "Συμφόρηση μύτης/φάρυγγα", "S" }, true);
            //table.LoadDataRow(new object[] { "Ξηροστομία", "T" }, true);
            //table.LoadDataRow(new object[] { "Δύσπνοια/Ταχύπνοια", "U" }, true);
            //table.LoadDataRow(new object[] { "Αίσθημα πνιγμού", "V" }, true);
            //table.LoadDataRow(new object[] { "Κόμπος/αίσθημα ξένου σώματος στο λαιμό", "W" }, true);
            //table.LoadDataRow(new object[] { "Βάρος στο στήθος", "X" }, true);
            //table.LoadDataRow(new object[] { "Πόνος στο στήθος", "Y" }, true);
            //table.LoadDataRow(new object[] { "Ταχυκαρδία(ταχυπαλμία)/αίσθημα παλμών", "Z" }, true);
            //table.LoadDataRow(new object[] { "Επιδείνωση αρρυθμιών", "AA" }, true);
            //table.LoadDataRow(new object[] { "Υπέρταση (νευροπίεση, συστολική/διαστολική πίεση)", "AB" }, true);
            //table.LoadDataRow(new object[] { "Ζάλη/ίλιγγος (ψυχογενής)", "AC" }, true);
            //table.LoadDataRow(new object[] { "Τάση λιποθυμίας", "AD" }, true);
            //table.LoadDataRow(new object[] { "Αστάθεια", "AE" }, true);
            //table.LoadDataRow(new object[] { "Εξάψεις", "AF" }, true);
            //table.LoadDataRow(new object[] { "Εφίδρωση", "AG" }, true);
            //table.LoadDataRow(new object[] { "Ερυθρότητα", "AH" }, true);
            //table.LoadDataRow(new object[] { "Τρόμος (τρέμουλο), μυϊκές συσπάσεις", "AI" }, true);
            //table.LoadDataRow(new object[] { "Εξανθήματα στο σώμα (π.χ. νευροδερματίτιδα, λεύκη, ψωρίαση, αλωπεκία κ.α.)", "AJ" }, true);
            //table.LoadDataRow(new object[] { "Αυτοάνοσες διαταραχές (π.χ. λύκος)", "AK" }, true);
            //table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            //ds.Tables.Add(table);
            //#endregion

            #region  Contacts-ObsessiveIdeas
            table = new DataTable();
            table.TableName = "Contacts-ObsessiveIdeas";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Μόλυνσης", "A" }, true);
            table.LoadDataRow(new object[] { "Ελέγχου", "B" }, true);
            table.LoadDataRow(new object[] { "Συμμετρίας και ακρίβειας", "C" }, true);
            table.LoadDataRow(new object[] { "Ταξινόμησης", "D" }, true);
            table.LoadDataRow(new object[] { "Επιθετικότητας, βλάβης προς τον εαυτό ή προς τρίτους συνήθως κοντινά πρόσωπα", "E" }, true);
            table.LoadDataRow(new object[] { "Κοινωνικά απαράδεκτης συμπεριφοράς (π.χ. σεξουαλικής φύσεως)", "F" }, true);
            table.LoadDataRow(new object[] { "Θρησκείας", "G" }, true);
            table.LoadDataRow(new object[] { "Παθολογικής αμφιβολίας", "H" }, true);
            table.LoadDataRow(new object[] { "Αποθησαύρισης", "I" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-IdeoPsychoComplusions
            table = new DataTable();
            table.TableName = "Contacts-IdeoPsychoComplusions";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Ελέγχου 28.8%", "A" }, true);
            table.LoadDataRow(new object[] { "Καθαριότητας 26.5%", "B" }, true);
            table.LoadDataRow(new object[] { "Επανάληψη πράξεων 11.1%", "C" }, true);
            table.LoadDataRow(new object[] { "Νοητική επανάληψη λέξεων, φράσεων κ.α. (π.χ. προσευχές) 10.9%", "D" }, true);
            table.LoadDataRow(new object[] { "Ταξινόμησης/συμμετρίας 5.9%", "E" }, true);
            table.LoadDataRow(new object[] { "Αποθησαύρισης 3.5%", "F" }, true);
            table.LoadDataRow(new object[] { "Μετρήματος 2.1%", "G" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-Dysmorphobia
            table = new DataTable();
            table.TableName = "Contacts-Dysmorphobia";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Σκέψεις Ελαττωματικότητας Ατέλειας Ασυμμετρίες", "A" }, true);
            table.LoadDataRow(new object[] { "Συμπεριφορές Πλέον Των 3 Ωρών Ελέγχου, Καθρέφτη, Κάλυψη Δέρματος, Περιποίηση, Σύγκρισης Με Άλλους", "B" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-Insomnia
            table = new DataTable();
            table.TableName = "Contacts-Insomnia";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Αϋπνία έναρξης : δυσκολία να μας πάρει ο ύπνος", "A" }, true);
            table.LoadDataRow(new object[] { "Ενδιάμεση αφύπνιση : ξύπνημα κατά τη διάρκεια της νύχτας και αδυναμία να ξανακοιμηθούμε", "B" }, true);
            table.LoadDataRow(new object[] { "Πρώιμη αφύπνιση : ξύπνημα νωρίτερα από το επιθυμητό και αδυναμία να ξανακοιμηθούμε", "C" }, true);
            table.LoadDataRow(new object[] { "Μη αναζωογονητικός νυχτερινός ύπνος : μειωμένη ποιότητα του ύπνου", "D" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-SleepDisorder
            table = new DataTable();
            table.TableName = "Contacts-SleepDisorder";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Ναρκοληψία (διαταραχή της φυσιολογικής εγρήγορσης)", "A" }, true);
            table.LoadDataRow(new object[] { "Ιδιοπαθής υπερυπνία", "B" }, true);
            table.LoadDataRow(new object[] { "Υποτροπιάζουσα υπερυπνία (σύνδρομο Kleine Levin)", "C" }, true);
            table.LoadDataRow(new object[] { "Υπνική άπνοια αποφρακτικού ή κεντρικού τύπου (διαταραχή της αναπνοής κατά τον ύπνο με επεισόδια άπνοιας)", "D" }, true);
            table.LoadDataRow(new object[] { "Διαταραχή περιοδικών κινήσεων των άκρων (νυχτερινός μυόκλονος)", "E" }, true);
            table.LoadDataRow(new object[] { "Διαταραχές του κιρκάδιου ρυθμού λόγω κυλιόμενου ωραρίου ή jet lag (ύπνος επιβραδυνόμενης φάσης ASPT & ύπνος πρώιμης φάσης DSPT)", "F" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-Paranoid
            table = new DataTable();
            table.TableName = "Contacts-Paranoid";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Υποψίες βλάβης - εκμετάλλευσης - εξαπάτησης", "A" }, true);
            table.LoadDataRow(new object[] { "Αβάσιμες υποψίες φερεγγυότητας φίλων - συνεργατών", "B" }, true);
            table.LoadDataRow(new object[] { "Αδυναμία εκμυστήρευσης λόγω φόβου επιβλαβούς χρήσης των πληροφοριών", "C" }, true);
            table.LoadDataRow(new object[] { "Παθολογική Ζήλια", "D" }, true);
            table.LoadDataRow(new object[] { "Αδυναμία συγχώρεσης", "E" }, true);
            table.LoadDataRow(new object[] { "Εσφαλμένες αντιλήψεις επιθέσεων κατά του χαρακτήρα - υπόληψής του", "F" }, true);
            table.LoadDataRow(new object[] { "Παρερμηνεία σχολίων - γεγονότων ως κρυφά μηνύματα", "G" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-Schizoid
            table = new DataTable();
            table.TableName = "Contacts-Schizoid";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Έλλειψη ενδιαφέροντος για κοινωνικές και οικογενειακές καταστάσεις", "A" }, true);
            table.LoadDataRow(new object[] { "Έλλειψη ευχαρίστησης σχεδόν από κάθε δραστηριότητα", "B" }, true);
            table.LoadDataRow(new object[] { "Έλλειψη ενδιαφέροντος για σεξ με άλλο άτομο", "C" }, true);
            table.LoadDataRow(new object[] { "Μοναχικές δραστηριότητες", "D" }, true);
            table.LoadDataRow(new object[] { "Αδιαφορία για την κριτική και τον έπαινο", "E" }, true);
            table.LoadDataRow(new object[] { "Μη ύπαρξη φίλων", "F" }, true);
            table.LoadDataRow(new object[] { "Συναισθηματική άμβλυνση", "G" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-Schizotype
            table = new DataTable();
            table.TableName = "Contacts-Schizotype";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Παράδοξες πεποιθήσεις – Μαγική σκέψη π.χ. Τηλεπάθεια – Δεισιδαιμονίες", "A" }, true);
            table.LoadDataRow(new object[] { "Ασυνήθιστες αντιληπτικές εμπειρίες π.χ. Σωματικές παραισθήσεις", "B" }, true);
            table.LoadDataRow(new object[] { "Παρανοειδής ιδεασμός – Καχυποψία", "C" }, true);
            table.LoadDataRow(new object[] { "Ιδέες αναφοράς", "D" }, true);
            table.LoadDataRow(new object[] { "Παράδοξη – εκκεντρική εμφάνιση, συμπεριφορά", "E" }, true);
            table.LoadDataRow(new object[] { "Παράδοξη σκέψη και λόγος π.χ. Ασάφεια – Περιφερικότητα – Μεταφορές", "F" }, true);
            table.LoadDataRow(new object[] { "Περισφιγμένο – απρόσφορο συναίσθημα", "G" }, true);
            table.LoadDataRow(new object[] { "Κοινωνικό άγχος λόγω παρανοϊκών φοβιών", "H" }, true);
            table.LoadDataRow(new object[] { "Έλλειψη στενών – έμπιστων προσώπων", "I" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-OnLimit
            table = new DataTable();
            table.TableName = "Contacts-OnLimit";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Ασταθείς σχέσεις: άκρα εξιδανίκευση - άκρα απαξίωση", "A" }, true);
            table.LoadDataRow(new object[] { "Απεγνωσμένες προσπάθειες αποφυγής εγκατάλειψης", "B" }, true);
            table.LoadDataRow(new object[] { "Παρορμητική συμπεριφορά: σεξ, διατροφή, οδήγηση, έξοδα", "C" }, true);
            table.LoadDataRow(new object[] { "Αίσθημα κενού", "D" }, true);
            table.LoadDataRow(new object[] { "Ασταθής εικόνα/αίσθηση εαυτού", "E" }, true);
            table.LoadDataRow(new object[] { "Υποτροπιάζουσα αυτοκτονική συμπεριφορά", "F" }, true);
            table.LoadDataRow(new object[] { "Συναισθηματική αστάθεια", "G" }, true);
            table.LoadDataRow(new object[] { "Έντονος απρόσφορος θυμός, αδυναμία ελέγχου του", "H" }, true);
            table.LoadDataRow(new object[] { "Παρανοϊκός ιδεασμός – Αποσυνδετικά συμπτώματα", "I" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-Antisocial
            table = new DataTable();
            table.TableName = "Contacts-Antisocial";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Αδυναμία συμμόρφωσης σε κοινωνικούς κανόνες", "A" }, true);
            table.LoadDataRow(new object[] { "Εκμετάλλευση των άλλων προς ίδιον όφελος", "B" }, true);
            table.LoadDataRow(new object[] { "Παρορμητικότητα", "C" }, true);
            table.LoadDataRow(new object[] { "Ανευθυνότητα", "D" }, true);
            table.LoadDataRow(new object[] { "Ευερεθιστότητα – Επιθετικότητα", "E" }, true);
            table.LoadDataRow(new object[] { "Απερισκεψία – Αδιαφορία για ασφάλεια", "F" }, true);
            table.LoadDataRow(new object[] { "Έλλειψη μετάνοιας, ενοχής", "G" }, true);
            table.LoadDataRow(new object[] { "Ιστορικό διαταραχών διαγωγής", "H" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-Histronic
            table = new DataTable();
            table.TableName = "Contacts-Histronic";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Θεατρική συμπεριφορά – Υπερβολική συναισθηματική έκφραση", "A" }, true);
            table.LoadDataRow(new object[] { "Χρήση σωματικής εμφάνισης για προσέλκυση προσοχής", "B" }, true);
            table.LoadDataRow(new object[] { "Προκλητική σεξουαλικά, σαγηνευτική συμπεριφορά", "C" }, true);
            table.LoadDataRow(new object[] { "Εντυπωσιακός λόγος", "D" }, true);
            table.LoadDataRow(new object[] { "Δυσαρέσκεια όταν δεν είναι το επίκεντρο της προσοχής", "E" }, true);
            table.LoadDataRow(new object[] { "Υποβολιμότητα – Εύκολος επηρεασμός", "F" }, true);
            table.LoadDataRow(new object[] { "Ταχεία συναισθηματική ευμεταβλητότητα & ρηχότητα", "G" }, true);
            table.LoadDataRow(new object[] { "Θεώρηση σχέσεων πιο στενών απ’ ότι πραγματικά είναι", "H" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-Narcissistic
            table = new DataTable();
            table.TableName = "Contacts-Narcissistic";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Αίσθημα προσωπικού μεγαλείου", "A" }, true);
            table.LoadDataRow(new object[] { "Φαντασιώσεις υπέρμετρης επιτυχίας, ευτυχίας", "B" }, true);
            table.LoadDataRow(new object[] { "Πεποίθηση ότι πρέπει να συναλλάσσεται μόνο με άτομα ανώτερου επιπέδου", "C" }, true);
            table.LoadDataRow(new object[] { "Ζηλοφθονία", "D" }, true);
            table.LoadDataRow(new object[] { "Εκμετάλλευση των άλλων προς ίδιον όφελος", "E" }, true);
            table.LoadDataRow(new object[] { "Έλλειψη ενσυναίσθησης, απροθυμία αναγνώρισης ταύτισης με άλλο", "F" }, true);
            table.LoadDataRow(new object[] { "Πεποίθηση ότι πρέπει να τυγχάνει ιδιαίτερης μεταχείρισης", "G" }, true);
            table.LoadDataRow(new object[] { "Αλαζονεία, Υπεροψία", "H" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-Ideopsychocompression
            table = new DataTable();
            table.TableName = "Contacts-Ideopsychocompression";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Υπεραπασχόληση με λεπτομέρεια – Απώλεια ουσίας", "A" }, true);
            table.LoadDataRow(new object[] { "Τελειοθηρία & Μη ολοκλήρωση εργασίας", "B" }, true);
            table.LoadDataRow(new object[] { "Υπερβολική αφοσίωση στην εργασία & Αποκλεισμός ψυχαγωγικών δραστηριοτήτων", "C" }, true);
            table.LoadDataRow(new object[] { "Υπερβολική ευσυνειδησία, σχολαστικότητα σε θέματα ηθικής, δεοντολογίας, αξιών", "D" }, true);
            table.LoadDataRow(new object[] { "Ακαμψία – Ισχυρογνωμοσύνη", "E" }, true);
            table.LoadDataRow(new object[] { "Αδυναμία ανάθεσης καθηκόντων σε άλλους που δεν είναι σαν και αυτόν", "F" }, true);
            table.LoadDataRow(new object[] { "Αδυναμία απαλλαγής από άχρηστα αντικείμενα", "G" }, true);
            table.LoadDataRow(new object[] { "Μίζερος τρόπος διαχείρισης χρημάτων", "H" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-Avoidable
            table = new DataTable();
            table.TableName = "Contacts-Avoidable";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Αποφυγή επαγγελματικών δραστηριοτήτων που περιλαμβάνουν σημαντική διαπροσωπική επαφή", "A" }, true);
            table.LoadDataRow(new object[] { "Απροθυμία σύναψης σχέσεων", "B" }, true);
            table.LoadDataRow(new object[] { "Περιορισμός στενών φιλικών σχέσεων", "C" }, true);
            table.LoadDataRow(new object[] { "Συστολή σε νέες γνωριμίες", "D" }, true);
            table.LoadDataRow(new object[] { "Αρνητική, ανεπαρκής θεώρηση εαυτού", "E" }, true);
            table.LoadDataRow(new object[] { "Έντονη ενασχόληση με το ενδεχόμενο κριτικής / απόρριψης", "F" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-Addictive
            table = new DataTable();
            table.TableName = "Contacts-Addictive";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Δυσκολία λήψης αποφάσεων χωρίς επιβεβαιώσεις από άλλους", "A" }, true);
            table.LoadDataRow(new object[] { "Ανάληψη δικών του/της ευθυνών από άλλους", "B" }, true);
            table.LoadDataRow(new object[] { "Δυσκολία έκφρασης διαφωνίας ώστε να μην χάσει υποστήριξη", "C" }, true);
            table.LoadDataRow(new object[] { "Υπερβολική συμπεριφορά προς εξασφάλιση υποστήριξης", "D" }, true);
            table.LoadDataRow(new object[] { "Εκτελεστική δυσλειτουργία", "E" }, true);
            table.LoadDataRow(new object[] { "Δυσαρέσκεια, αβοηθητότητα όταν είναι μόνη/ος", "F" }, true);
            table.LoadDataRow(new object[] { "Απεγνωσμένη αναζήτηση εξαρτητικής σχέσης όταν απολέσει την προηγούμενη", "G" }, true);
            table.LoadDataRow(new object[] { "Υπεραπασχόληση με φόβους εγκατάλειψη", "H" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Contacts-PassiveAggressive
            table = new DataTable();
            table.TableName = "Contacts-PassiveAggressive";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Παθητική αντίσταση στην εκπλήρωση συνηθισμένων καθηκόντων", "A" }, true);
            table.LoadDataRow(new object[] { "Αιτιάσεις μη κατανόησης", "B" }, true);
            table.LoadDataRow(new object[] { "Υπερβολικές αιτιάσεις προσωπικής κακοτυχίας", "C" }, true);
            table.LoadDataRow(new object[] { "Φθόνος & Μνησικακία για τους πιο τυχερούς", "D" }, true);
            table.LoadDataRow(new object[] { "Βλοσυρότητα, Φιλονικία", "E" }, true);
            table.LoadDataRow(new object[] { "Αβάσιμη κριτική & απέχθεια κατά της εξουσίας", "F" }, true);
            table.LoadDataRow(new object[] { "Εναλλαγή μεταξύ εχθρικής περιφρόνησης και μετάνοιας", "G" }, true);
            table.LoadDataRow(new object[] { "Μοναχικές δραστηριότητες,Αποφυγή επαγγελματικών δραστηριοτήτων που περιλαμβάνουν σημαντική διαπροσωπική επαφή", "H" }, true);
            table.LoadDataRow(new object[] { "Άλλο", "Other" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  InactiveReason 
            table = new DataTable();
            table.TableName = "Contacts-InactiveReason";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Drop out", "DropOut" }, true);
            table.LoadDataRow(new object[] { "Ολοκλήρωση θεραπείας", "TreatmentCompletion" }, true);
            table.LoadDataRow(new object[] { "Παραπομπή", "Reference" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  Questionnaries 
            table = new DataTable();
            table.TableName = "Contacts-Questionnaries";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Α1. Συνοπτικός δείκτης ποιότητας ζωής", "A1" }, true);
            table.LoadDataRow(new object[] { "Α2. Ανατροφοδότηση προ συνεδρίας", "A2" }, true);
            table.LoadDataRow(new object[] { "Α3 .Ανατροφοδότηση μετά συνεδρίας", "A3" }, true);
            ds.Tables.Add(table);
            #endregion

            #region  AccessLevel 
            table = new DataTable();
            table.TableName = "Contacts-AccessLevels";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Συνδρομή ερωτηματολόγια χωρίς χρέωση", "1" }, true);
            table.LoadDataRow(new object[] { "Συνδρομή Ερωτηματολόγια με χρέωση επίπεδο 1", "2" }, true);
            table.LoadDataRow(new object[] { "Συνδρομή Ερωτηματολόγια με χρέωση επίπεδο 2", "3" }, true);
            table.LoadDataRow(new object[] { "Συνδρομή Ερωτηματολόγια με χρέωση επίπεδο 3", "4" }, true);
            table.LoadDataRow(new object[] { "Συνδρομή Ερωτηματολόγια με χρέωση επίπεδο 4", "5" }, true);
            table.LoadDataRow(new object[] { "Συνδρομή Ερωτηματολόγια με χρέωση επίπεδο 5", "6" }, true);
            table.LoadDataRow(new object[] { "Συνδρομή πλατφόρμα χωρίς χρέωση", "7" }, true);
            table.LoadDataRow(new object[] { "Συνδρομή πλατφόρμα με χρέωση επίπεδο 1", "8" }, true);
            table.LoadDataRow(new object[] { "Συνδρομή πλατφόρμα με χρέωση επίπεδο 2", "9" }, true);
            table.LoadDataRow(new object[] { "Συνδρομή πλατφόρμα με χρέωση επίπεδο 3", "10" }, true);
            table.LoadDataRow(new object[] { "Συνδρομή πλατφόρμα  με χρέωση επίπεδο 4", "11" }, true);
            table.LoadDataRow(new object[] { "Συνδρομή πλατφόρμα  με χρέωση επίπεδο 5", "12" }, true);
    
            ds.Tables.Add(table);
            #endregion

            #region  AppointmentStates 
            table = new DataTable();
            table.TableName = "Contacts-AppointmentStates";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Σε Διαδικασία Έναρξης", "Starting" }, true);
            table.LoadDataRow(new object[] { "Ενεργή", "Active" }, true);
            table.LoadDataRow(new object[] { "Σε Παύση", "Paused" }, true);
            table.LoadDataRow(new object[] { "Αναμνηστική", "Memorial" }, true);
            table.LoadDataRow(new object[] { "Παραπομπή σε Άλλο Ειδικό", "ReferToΟtherExpert" }, true);
            table.LoadDataRow(new object[] { "Ανενεργή", "Inactive" }, true);
            table.LoadDataRow(new object[] { "Black List", "BlackList" }, true);
            ds.Tables.Add(table);
            #endregion
            #endregion Contacts

            #region  Appointments


            #region Appointments-IntervetionModels
            table = new DataTable();
            table.TableName = "Appointments-IntervetionModels";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Ψυχανάλυση", "Psychoanalysis" }, true);
            table.LoadDataRow(new object[] { "Γνωσιακή Συμπεριφορική", "CognitiveBehavioral" }, true);
            table.LoadDataRow(new object[] { "Σωματική", "Physical" }, true);
            table.LoadDataRow(new object[] { "Διαπροσωπική", "Interpersonal" }, true);
            table.LoadDataRow(new object[] { "Σχημάτων", "Shapes" }, true);
            table.LoadDataRow(new object[] { "Υπαρξιακή", "Existential" }, true);
            table.LoadDataRow(new object[] { "Γκεστάλτ", "Gestalt" }, true);
            table.LoadDataRow(new object[] { "Κλινική Ύπνωση", "ClinicalHypnosis" }, true);
            table.LoadDataRow(new object[] { "Συμβουλευτική", "Counseling" }, true);
            table.LoadDataRow(new object[] { "Ζεύγους", "Couple" }, true);
            table.LoadDataRow(new object[] { "Ομαδική", "group" }, true);
            table.LoadDataRow(new object[] { "Μουσικοθεραπεία", "MusicTherapy" }, true);
            table.LoadDataRow(new object[] { "Χοροθεραπεία", "DanceTherapy" }, true);
            table.LoadDataRow(new object[] { "Εικαστική", "Speculative" }, true);
            table.LoadDataRow(new object[] { "Παιγνιοθεραπεία", "PlayTherapy" }, true);
            table.LoadDataRow(new object[] { "Δραμματοθεραπεία", "DramaTherapy" }, true);
            table.LoadDataRow(new object[] { "Ψυχόδραμα", "psychodrama" }, true);
            table.LoadDataRow(new object[] { "Τραύματος", "Trauma" }, true);
            table.LoadDataRow(new object[] { "Κινηματογραφοθεραπεία", "FilmTherapy" }, true);
            table.LoadDataRow(new object[] { "Διαλεκτική Συμπεριφορική", "DialecticalBehavioral" }, true);
            table.LoadDataRow(new object[] { "Άλλη Παρέμβαση", "Another" }, true);

            ds.Tables.Add(table);
            #endregion

            #region Appointments-IntervetionTechniques
            table = new DataTable();
            table.TableName = "Appointments-IntervetionTechniques";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Γνωσιακές", "A" }, true);
            table.LoadDataRow(new object[] { "Συμβουλευτικές", "B" }, true);
            table.LoadDataRow(new object[] { "Τραυματοθεραπείας", "C" }, true);
            table.LoadDataRow(new object[] { "Φυσιολογίας", "D" }, true);
            table.LoadDataRow(new object[] { "Εικονικής Πραγματικότητας", "E" }, true);
            table.LoadDataRow(new object[] { "Φαντασιωσικές", "F" }, true);
            table.LoadDataRow(new object[] { "Ύπνωσης", "G" }, true);
            table.LoadDataRow(new object[] { "Χαλαρωσης", "H" }, true);
            table.LoadDataRow(new object[] { "Ενσυνειδητοτητας", "J" }, true);
            table.LoadDataRow(new object[] { "Φυσιολογίας", "I" }, true);


            ds.Tables.Add(table);
            #endregion

            #region  AppointmentRooms
            table = new DataTable();
            table.TableName = "Appointments-AppointmentRooms";
            table.Columns.Add("RoomId");
            table.Columns.Add("RoomName");
            table.Columns.Add("BackColor");
            table.Columns.Add("FontColor");
            table.Columns.Add("IsPhysical", typeof(bool));  //Καθορίζει αν είναι φυσικό χώρος

            table.LoadDataRow(new object[] { "10", "Προτεινόμενη/διαγνωστικά", "#a0a0a0", "#fff", false }, true);
            table.LoadDataRow(new object[] { "9", "Αποστολή/διαγνωστικά", "#ffe72b", "#fff", false }, true);
            table.LoadDataRow(new object[] { "3", "Ανατροφοδότηση/διαγνωστικά", "#75ab4c", "#000", false }, true);
            table.LoadDataRow(new object[] { "1", "Αίθουσα 1", "#E0CFE9", "#000", true }, true);
            table.LoadDataRow(new object[] { "2", "Αίθουσα 2", "#f8ec9d", "#000", true }, true);
            table.LoadDataRow(new object[] { "4", "Αίθουσα 4", "#A8D5FF", "#000", true }, true);
            table.LoadDataRow(new object[] { "5", "Αίθουσα 5", "#ee5b3e", "#000", true }, true);
            table.LoadDataRow(new object[] { "6", "Αίθουσα 6", "#ffb138", "#000", true }, true);
            table.LoadDataRow(new object[] { "7", "Συνεδρία εκτός έδρας", "#9e7d4a", "#000", false }, true);
            table.LoadDataRow(new object[] { "8", "Διαδικτυακά", "#196aa4", "#fff", false }, true);
            table.LoadDataRow(new object[] { "11", "Συνεδρία VR", "#be58a7", "#fff", false }, true);
            ds.Tables.Add(table);
            #endregion

            #region Appointments-PaymentTypes
            table = new DataTable();
            table.TableName = "Appointments-PaymentTypes";
            table.Columns.Add("Text");
            table.Columns.Add("Value");

            table.LoadDataRow(new object[] { "Μετρητά", "Cash" }, true);
            table.LoadDataRow(new object[] { "Κάρτα", "Card" }, true);
            table.LoadDataRow(new object[] { "Μεταφορά/Κατάθεση σε λογαριασμό", "Deposit" }, true);
            table.LoadDataRow(new object[] { "Εκκρεμεί", "Pending" }, true);

            ds.Tables.Add(table);
            #endregion

            #endregion Appointments
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="field">For example 'Contacts-Addictive'</param>
        /// <param name="value">For example 'A' ή 'B' ή 'Other' e.t.c.</param>
        /// <returns></returns>
        public static string GetDisplayOfSingleValue(string field, string value)
        {
            if (value != "")
            {
                return ds.Tables[field].AsEnumerable().AsQueryable().Where(s => s["Value"].ToString() == value).Select(s => s["Text"].ToString()).First();
            }
            else
            {
                return "";
            }
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="field">For example 'Contacts-Addictive'</param>
        /// <param name="value">For example 'A|B|Other' e.t.c.</param>
        /// <returns></returns>
        public static string GetDisplayOfMultipleValues(string field, string value)
        {
            try
            {
                if (value == null || value == "")
                {
                    return "";
                }
                string display = "";
                string[] values = value.Split('|');

                foreach (string singleValue in values)
                {
                    display = display + ds.Tables[field].AsEnumerable().AsQueryable().Where(s => s["Value"].ToString() == singleValue).Select(s => s["Text"].ToString()).First() + " | ";
                }
                if (display[display.Length - 2] == '|')
                {
                    display = display.Remove(display.Length - 3, 3);
                }
                return display;
            }
            catch (Exception exp)
            {
                int i = 0;
                return "";
            }
        }


    }
}
