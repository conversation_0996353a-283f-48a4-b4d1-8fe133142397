/*!
*  filename: ej.data.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./ej.core.min"],n):n()})(function(){window.ej=window.Syncfusion=window.Syncfusion||{},function(n,t,i,r){"use strict";var e,nt,tt,y,d,w,h,c,f;t.DataManager=function(i,u,f){if(!(this instanceof t.DataManager))return new t.DataManager(i,u,f);i||(i=[]);f=f||i.adaptor;typeof f=="string"&&(f=new t[f]);var e=[],o=this;return i instanceof Array?e={json:i,offline:!0}:typeof i=="object"?n.isPlainObject(i)?(i.json||(i.json=[]),i.table&&(i.json=this._getJsonFromElement(i.table,i.headerOption)),e={url:i.url,insertUrl:i.insertUrl,removeUrl:i.removeUrl,updateUrl:i.updateUrl,crudUrl:i.crudUrl,batchUrl:i.batchUrl,json:i.json,headers:i.headers,accept:i.accept,data:i.data,async:i.async,timeTillExpiration:i.timeTillExpiration,cachingPageSize:i.cachingPageSize,enableCaching:i.enableCaching,requestType:i.requestType,key:i.key,crossDomain:i.crossDomain,antiForgery:i.antiForgery,jsonp:i.jsonp,dataType:i.dataType,enableAjaxCache:i.enableAjaxCache,offline:i.offline!==r?i.offline:i.adaptor=="remoteSaveAdaptor"||i.adaptor instanceof t.remoteSaveAdaptor?!1:i.url?!1:!0,requiresFormat:i.requiresFormat}):(i.jquery||pt(i))&&(e={json:this._getJsonFromElement(i),offline:!0,table:i}):typeof i=="string"&&(e={url:i,offline:!1,dataType:"json",json:[]}),e.requiresFormat!==r||t.support.cors||(e.requiresFormat=c(e.crossDomain)?!0:e.crossDomain),e.antiForgery&&this.antiForgeryToken(),e.dataType===r&&(e.dataType="json"),this.dataSource=e,this.defaultQuery=u,e.url&&e.offline&&!e.json.length?(this.isDataAvailable=!1,this.adaptor=f||new t.ODataAdaptor,this.dataSource.offline=!1,this.ready=this.executeQuery(u||t.Query()).done(function(n){o.dataSource.offline=!0;o.isDataAvailable=!0;e.json=n.result;o.adaptor=new t.JsonAdaptor})):this.adaptor=e.offline?new t.JsonAdaptor:new t.ODataAdaptor,!e.jsonp&&this.adaptor instanceof t.ODataAdaptor&&(e.jsonp="callback"),this.adaptor=f||this.adaptor,e.enableCaching&&(this.adaptor=new t.CacheAdaptor(this.adaptor,e.timeTillExpiration,e.cachingPageSize)),this};t.DataManager.prototype={setDefaultQuery:function(n){this.defaultQuery=n},executeQuery:function(i,u,e,o){var s,h,c;return typeof i=="function"&&(o=e,e=u,u=i,i=null),i||(i=this.defaultQuery),i instanceof t.Query||f("DataManager - executeQuery() : A query is required to execute"),s=n.Deferred(),s.then(u,e,o),h={query:i},this.dataSource.offline||this.dataSource.url==r?t.isNullOrUndefined(this.dataSource.async)||this.dataSource.async!=!1?d(function(){this._localQueryProcess(i,h,s)},this):this._localQueryProcess(i,h,s):(c=this.adaptor.processQuery(this,i),t.isNullOrUndefined(c.url)?d(function(){h=this._getDeferedArgs(i,c,h);s.resolveWith(this,[h])},this):this._makeRequest(c,s,h,i)),s.promise()},_localQueryProcess:function(n,t,i){var r=this.executeLocal(n);t=this._getDeferedArgs(n,r,t);i.resolveWith(this,[t])},_getDeferedArgs:function(n,t,i){return n._requiresCount?(i.result=t.result,i.count=t.count):i.result=t,i.getTableModel=rt(n._fromTable,i.result,this),i.getKnockoutModel=ut(i.result),i},executeLocal:function(i){var e,r;if(this.defaultQuery||i instanceof t.Query||f("DataManager - executeLocal() : A query is required to execute"),this.dataSource.json||f("DataManager - executeLocal() : Json data is required to execute"),i=i||this.defaultQuery,e=this.adaptor.processQuery(this,i),i._subQuery){var o=i._subQuery._fromTable,s=i._subQuery._lookup,u=i._requiresCount?e.result:e;for(s&&s instanceof Array&&k(i._subQuery._fKey,o,u,s,i._subQuery._key),r=0;r<u.length;r++)u[r][o]instanceof Array&&(u[r]=n.extend({},u[r]),u[r][o]=this.adaptor.processResponse(i._subQuery.using(t.DataManager(u[r][o].slice(0))).executeLocal(),this,i))}return this.adaptor.processResponse(e,this,i)},_makeRequest:function(i,r,u,f){var o=!!f._subQuerySelector,c=h(function(n){u.error=n;r.rejectWith(this,[u])},this),l=h(function(n,t,i,e,s,h,c){o||(u.xhr=i,u.count=parseInt(t,10),u.result=n,u.request=e,u.aggregates=h,u.getTableModel=rt(f._fromTable,n,this),u.getKnockoutModel=ut(n),u.actual=s,u.virtualSelectRecords=c,r.resolveWith(this,[u]))},this),a=h(function(t,i){var r=n.Deferred(),h={parent:u},e,s;return f._subQuery._isChild=!0,e=this.adaptor.processQuery(this,f._subQuery,t?this.adaptor.processResponse(t):i),s=this._makeRequest(e,r,h,f._subQuery),o||r.then(function(n){t&&(k(f._subQuery._fKey,f._subQuery._fromTable,t,n,f._subQuery._key),l(t))},c),s},this),v=w(function(n,i,r,u){r.getResponseHeader("Content-Type").indexOf("xml")==-1&&t.dateParse&&(n=t.parseJSON(n));var e=this.adaptor.processResponse(n,this,f,r,u),s=0,h=null,c=n.virtualSelectRecords;if(f._requiresCount&&(s=e.count,h=e.aggregates,e=e.result),!f._subQuery){l(e,s,r,u,n,h,c);return}o||a(e)},this),e=n.extend({type:"GET",dataType:this.dataSource.dataType,crossDomain:this.dataSource.crossDomain,jsonp:this.dataSource.jsonp,cache:t.isNullOrUndefined(this.dataSource.enableAjaxCache)?!0:this.dataSource.enableAjaxCache,beforeSend:h(this._beforeSend,this),processData:!1,success:v,error:c},i),s;return"async"in this.dataSource&&(e.async=this.dataSource.async),e=n.ajax(e),o&&(s=f._subQuerySelector.call(this,{query:f._subQuery,parent:f}),s&&s.length?(e=n.when(e,a(null,s)),e.then(w(function(n,t,i){var r=this.adaptor.processResponse(n[0],this,f,n[2],i[0]),e=0,u;f._requiresCount&&(e=r.count,r=r.result);u=this.adaptor.processResponse(t[0],this,f._subQuery,t[2],i[1]);e=0;f._subQuery._requiresCount&&(e=u.count,u=u.result);k(f._subQuery._fKey,f._subQuery._fromTable,r,u,f._subQuery._key);o=!1;l(r,e,n[2])},this),c)):o=!1),e},_beforeSend:function(n,t){var i,f,r,u;for(this.adaptor.beforeSend(this,n,t),i=this.dataSource.headers,r=0;i&&r<i.length;r++){f=[];for(u in i[r])f.push(u),n.setRequestHeader(u,i[r][u])}},saveChanges:function(i,r,u,f){var s,o,e;return(u instanceof t.Query&&(f=u,u=null),s={url:u,key:r||this.dataSource.key},o=this.adaptor.batchRequest(this,i,s,f),this.dataSource.offline)?o:(e=n.Deferred(),n.ajax(n.extend({beforeSend:h(this._beforeSend,this),success:w(function(n,t,u,f){e.resolveWith(this,[this.adaptor.processResponse(n,this,null,u,f,i,r)])},this),error:function(n){e.rejectWith(this,[{error:n}])}},o)),e.promise())},insert:function(i,r,f){var o,e;return(i=u.replacer(i,!0),r instanceof t.Query&&(f=r,r=null),o=this.adaptor.insert(this,i,r,f),this.dataSource.offline)?o:(e=n.Deferred(),n.ajax(n.extend({type:"POST",contentType:"application/json; charset=utf-8",processData:!1,beforeSend:h(this._beforeSend,this),success:w(function(n,i,r,f){try{t.isNullOrUndefined(n)?n=[]:u.parseJson(n)}catch(o){n=[]}n=this.adaptor.processResponse(u.parseJson(n),this,null,r,f);e.resolveWith(this,[{record:n,dataManager:this}])},this),error:function(n){e.rejectWith(this,[{error:n,dataManager:this}])}},o)),e.promise())},antiForgeryToken:function(){var i;return t.isNullOrUndefined(n("input[name='_ejRequestVerifyToken']").val())?i=t.buildTag("input","","",{type:"hidden",name:"_ejRequestVerifyToken",value:t.getGuid()}).appendTo("body"):n("input[name='_ejRequestVerifyToken']").val(t.getGuid()),t.cookie.set("_ejRequestVerifyToken",n("input[name='_ejRequestVerifyToken']").val()),{name:"_ejRequestVerifyToken",value:n("input[name='_ejRequestVerifyToken']").val()}},remove:function(i,r,f,e){var s,o;return(typeof r=="object"&&(r=r[i]),f instanceof t.Query&&(e=f,f=null),s=this.adaptor.remove(this,i,r,f,e),this.dataSource.offline)?s:(o=n.Deferred(),n.ajax(n.extend({type:"POST",contentType:"application/json; charset=utf-8",beforeSend:h(this._beforeSend,this),success:w(function(n,i,r,f){try{t.isNullOrUndefined(n)?n=[]:u.parseJson(n)}catch(e){n=[]}n=this.adaptor.processResponse(u.parseJson(n),this,null,r,f);o.resolveWith(this,[{record:n,dataManager:this}])},this),error:function(n){o.rejectWith(this,[{error:n,dataManager:this}])}},s)),o.promise())},update:function(i,r,f,e){var s,o;return(r=u.replacer(r,!0),f instanceof t.Query&&(e=f,f=null),s=this.adaptor.update(this,i,r,f,e),this.dataSource.offline)?s:(o=n.Deferred(),n.ajax(n.extend({contentType:"application/json; charset=utf-8",beforeSend:h(this._beforeSend,this),success:w(function(n,i,r,f){try{t.isNullOrUndefined(n)?n=[]:u.parseJson(n)}catch(e){n=[]}n=this.adaptor.processResponse(u.parseJson(n),this,null,r,f);o.resolveWith(this,[{record:n,dataManager:this}])},this),error:function(n){o.rejectWith(this,[{error:n,dataManager:this}])}},s)),o.promise())},_getJsonFromElement:function(i){typeof i=="string"&&(i=n(n(i).html()));i=i.jquery?i[0]:i;var r=i.tagName.toLowerCase();return r!=="table"&&f("ej.DataManager : Unsupported htmlElement : "+r),t.parseTable(i)}};var k=function(n,i,r,u,e){var o,s={},c,h;for(u.result&&(u=u.result),u.GROUPGUID&&f("ej.DataManager: Do not have support Grouping in hierarchy"),o=0;o<u.length;o++)h=t.getObject(n,u[o]),c=s[h]||(s[h]=[]),c.push(u[o]);for(o=0;o<r.length;o++)r[o][i]=s[t.getObject(e||n,r[o])]},l={accept:"application/json;odata=light;q=1,application/json;odata=verbose;q=0.5",multipartAccept:"multipart/mixed",batch:"$batch",changeSet:"--changeset_",batchPre:"batch_",contentId:"Content-Id: ",batchContent:"Content-Type: multipart/mixed; boundary=",changeSetContent:"Content-Type: application/http\nContent-Transfer-Encoding: binary ",batchChangeSetContentType:"Content-Type: application/json; charset=utf-8 "},u={parseJson:function(n){var t=typeof n;return t==="string"?n=JSON.parse(n,u.jsonReviver):n instanceof Array?u.iterateAndReviveArray(n):t==="object"&&u.iterateAndReviveJson(n),n},iterateAndReviveArray:function(n){for(var t=0;t<n.length;t++)typeof n[t]=="object"?u.iterateAndReviveJson(n[t]):n[t]=typeof n[t]!="string"||/^[\s]*\[|^[\s]*\{|\"/g.test(n[t])?u.parseJson(n[t]):u.jsonReviver("",n[t])},iterateAndReviveJson:function(n){var t;for(var i in n)i.startsWith("__")||(t=n[i],typeof t=="object"?t instanceof Array?u.iterateAndReviveArray(t):u.iterateAndReviveJson(t):n[i]=u.jsonReviver(i,t))},jsonReviver:function(n,i){var o=i,e=/[\-,/,\,.,:,]+/,f,r;if(typeof i=="string"){if(f=/^\/Date\(([+-]?[0-9]+)([+-][0-9]{4})?\)\/$/.exec(i),f)return t.parseDateInUTC?u.isValidDate(f[0]):u.replacer(new Date(parseInt(f[1])));(t.dateParse?/^(?:(\d{4}\-\d\d\-\d\d)|(\d{4}\-\d\d\-\d\d([tT][\d:\.]*){1})([zZ]|([+\-])(\d\d):?(\d\d))?)$/.test(i):/^(\d{4}\-\d\d\-\d\d([tT][\d:\.]*){1})([zZ]|([+\-])(\d\d):?(\d\d))?$/.test(i))&&(r=o.split(/[^0-9]/),/^(\d{4}\-\d\d\-\d\d)$/.test(i)?i=new Date(r[t.dateFormat.split(e).indexOf("yyyy")],r[t.dateFormat.split(e).indexOf("MM")]-1,r[t.dateFormat.split(e).indexOf("dd")]):(i=t.parseDateInUTC?u.isValidDate(i):u.replacer(new Date(i)),isNaN(i)&&(i=u.replacer(new Date(r[0],r[1]-1,r[2],r[3],r[4],r[5])))))}return i},isValidDate:function(n){var t=n,i;return(typeof t=="string"&&t.indexOf("/Date(")==0&&(n=t.replace(/\d+/,function(n){var t=new Date(parseInt(n)).getTimezoneOffset()*6e4,i=parseInt(n)+t;return u.replacer(new Date(parseInt(i)))})),typeof n=="string")?(n=n.replace("/Date(",function(){return""}),n=n.replace(")/",function(){return""}),i=new Date(n)instanceof Date,i?new Date(n):n):n},isJson:function(n){return typeof n[0]=="string"?n:t.parseJSON(n)},isGuid:function(n){var t=/[A-Fa-f0-9]{8}(?:-[A-Fa-f0-9]{4}){3}-[A-Fa-f0-9]{12}/i.exec(n);return t!=null},replacer:function(n,i){return t.isPlainObject(n)?u.jsonReplacer(n,i):n instanceof Array?u.arrayReplacer(n):n instanceof Date?u.jsonReplacer({val:n},i).val:n},jsonReplacer:function(n,i){var r,u,f;for(u in n)(r=n[u],r instanceof Date)&&(f=t.serverTimezoneOffset*36e5*(t.isNullOrUndefined(i)||i===!1?1:-1),n[u]=new Date(+r+f));return n},arrayReplacer:function(n){for(var i=0;i<n.length;i++)t.isPlainObject(n[i])?n[i]=u.jsonReplacer(n[i]):n[i]instanceof Date&&(n[i]=u.jsonReplacer({date:n[i]}).date);return n}};t.isJSON=u.isJson;t.parseJSON=u.parseJson;t.dateParse=!0;t.dateFormat="yyyy-MM-dd";t.isGUID=u.isGuid;t.Query=function(n){return(this instanceof t.Query)?(this.queries=[],this._key="",this._fKey="",typeof n=="string"?this._fromTable=n||"":n&&n instanceof Array&&(this._lookup=n),this._expands=[],this._sortedColumns=[],this._groupedColumns=[],this._subQuery=null,this._isChild=!1,this._params=[],this):new t.Query(n)};t.Query.prototype={key:function(n){return typeof n=="string"&&(this._key=n),this},using:function(n){return n instanceof t.DataManager?(this.dataManagar=n,this):f("Query - using() : 'using' function should be called with parameter of instance ej.DataManager")},execute:function(n,i,r,u){return(n=n||this.dataManagar,n&&n instanceof t.DataManager)?n.executeQuery(this,i,r,u):f("Query - execute() : dataManager needs to be is set using 'using' function or should be passed as argument")},executeLocal:function(n){return(n=n||this.dataManagar,n&&n instanceof t.DataManager)?n.executeLocal(this):f("Query - executeLocal() : dataManager needs to be is set using 'using' function or should be passed as argument")},clone:function(){var n=new t.Query;return n.queries=this.queries.slice(0),n._key=this._key,n._isChild=this._isChild,n.dataManagar=this.dataManager,n._fromTable=this._fromTable,n._params=this._params.slice(0),n._expands=this._expands.slice(0),n._sortedColumns=this._sortedColumns.slice(0),n._groupedColumns=this._groupedColumns.slice(0),n._subQuerySelector=this._subQuerySelector,n._subQuery=this._subQuery,n._fKey=this._fKey,n._requiresCount=this._requiresCount,n},from:function(n){return typeof n=="string"&&(this._fromTable=n),this},addParams:function(n,i){return typeof i=="function"||t.isPlainObject(i)?typeof i=="function"&&this._params.push({key:n,fn:i}):this._params.push({key:n,value:i}),this},expand:function(n){return this._expands=typeof n=="string"?[].slice.call(arguments,0):n.slice(0),this},where:function(n,i,r,u,e){i=(i||t.FilterOperators.equal).toLowerCase();var o=null;return typeof n=="string"?o=new t.Predicate(n,i,r,u,e):n instanceof t.Predicate?o=n:f("Query - where : Invalid arguments"),this.queries.push({fn:"onWhere",e:o}),this},search:function(n,i,r,u,f){i&&typeof i!="boolean"?typeof i=="string"&&(i=[i]):(i=[],u=i);typeof r=="boolean"&&(u=r,r=null);r=r||t.FilterOperators.contains;r.length<3&&(r=t.data.operatorSymbols[r]);var e=t.data.fnOperators[r]||t.data.fnOperators.processSymbols(r);return this.queries.push({fn:"onSearch",e:{fieldNames:i,operator:r,searchKey:n,ignoreCase:u,ignoreAccent:f,comparer:e}}),this},sortBy:function(n,i,r){var o=t.sortOrder.Ascending,s,f,u,e;if(typeof n=="string"&&n.toLowerCase().endsWith(" desc")&&(n=n.replace(/ desc$/i,""),i=t.sortOrder.Descending),n instanceof Array){for(u=0;u<n.length;u++)this.sortBy(n[u],i,r);return this}if(typeof i=="boolean"?i=i?t.sortOrder.Descending:t.sortOrder.Ascending:typeof i=="function"&&(o="custom"),i&&typeof i!="string"||(o=i?i.toLowerCase():t.sortOrder.Ascending,i=t.pvt.fnSort(i)),r)for(s=a(this.queries,"onSortBy"),u=0;u<s.length;u++)if(f=s[u].e.fieldName,typeof f=="string"){if(f===n)return this}else if(f instanceof Array)for(e=0;e<f.length;e++)if(f[e]===n||n.toLowerCase()===f[e]+" desc")return this;return this.queries.push({fn:"onSortBy",e:{fieldName:n,comparer:i,direction:o}}),this},sortByDesc:function(n){return this.sortBy(n,t.sortOrder.Descending)},group:function(n,t){return this.sortBy(n,null,!0),this.queries.push({fn:"onGroup",e:{fieldName:n,fn:t}}),this},page:function(n,t){return this.queries.push({fn:"onPage",e:{pageIndex:n,pageSize:t}}),this},range:function(n,t){return(typeof n!="number"||typeof t!="number")&&f("Query() - range : Arguments type should be a number"),this.queries.push({fn:"onRange",e:{start:n,end:t}}),this},take:function(n){return typeof n!="number"&&f("Query() - Take : Argument type should be a number"),this.queries.push({fn:"onTake",e:{nos:n}}),this},skip:function(n){return typeof n!="number"&&f("Query() - Skip : Argument type should be a number"),this.queries.push({fn:"onSkip",e:{nos:n}}),this},select:function(n){return typeof n=="string"&&(n=[].slice.call(arguments,0)),n instanceof Array||f("Query() - Select : Argument type should be String or Array"),this.queries.push({fn:"onSelect",e:{fieldNames:n}}),this},hierarchy:function(n,i){return n&&n instanceof t.Query||f("Query() - hierarchy : query must be instance of ej.Query"),typeof i=="function"&&(this._subQuerySelector=i),this._subQuery=n,this},foreignKey:function(n){return typeof n=="string"&&(this._fKey=n),this},requiresCount:function(){return this._requiresCount=!0,this},aggregate:function(n,t){this.queries.push({fn:"onAggregates",e:{field:t,type:n}})}};t.Adaptor=function(n){this.dataSource=n;this.pvt={};this.init.apply(this,[].slice.call(arguments,1))};t.Adaptor.prototype={options:{from:"table",requestType:"json",sortBy:"sorted",select:"select",skip:"skip",group:"group",take:"take",search:"search",count:"requiresCounts",where:"where",aggregates:"aggregates",antiForgery:"antiForgery"},init:function(){},extend:function(t){var i=function(t){this.dataSource=t;this.options&&(this.options=n.extend({},this.options));this.init.apply(this,[].slice.call(arguments,0));this.pvt={}},u,r;i.prototype=new this.type;i.prototype.type=i;u=i.prototype.base={};for(r in t)i.prototype[r]&&(u[r]=i.prototype[r]);return n.extend(!0,i.prototype,t),i},processQuery:function(){},processResponse:function(n){return n.d?n.d:n},convertToQueryString:function(t){return n.param(t)},type:t.Adaptor};t.UrlAdaptor=(new t.Adaptor).extend({processQuery:function(n,t,i){var w=a(t.queries,"onSortBy"),d=a(t.queries,"onGroup"),nt=a(t.queries,"onWhere"),tt=a(t.queries,"onSearch"),it=a(t.queries,"onAggregates"),l=g(t.queries,["onSelect","onPage","onSkip","onTake","onRange"]),rt=t._params,b=n.dataSource.url,u,y,p=null,h=this.options,s={sorted:[],grouped:[],filters:[],searches:[],aggregates:[]},f,r,v,k;for(l.onPage?(u=l.onPage,y=e(u.pageIndex,t),p=e(u.pageSize,t),y=(y-1)*p):l.onRange&&(u=l.onRange,y=u.start,p=u.end-u.start),f=0;f<w.length;f++)u=e(w[f].e.fieldName,t),s.sorted.push(o(this,"onEachSort",{name:u,direction:w[f].e.direction},t));for(i&&(u=this.getFiltersFrom(i,t),u&&s.filters.push(o(this,"onEachWhere",u.toJSON(),t))),f=0;f<nt.length;f++){s.filters.push(o(this,"onEachWhere",nt[f].e.toJSON(),t));for(v in s.filters[f])c(s[v])&&delete s[v]}for(f=0;f<tt.length;f++)u=tt[f].e,s.searches.push(o(this,"onEachSearch",{fields:u.fieldNames,operator:u.operator,key:u.searchKey,ignoreCase:u.ignoreCase},t));for(f=0;f<d.length;f++)s.grouped.push(e(d[f].e.fieldName,t));for(f=0;f<it.length;f++)u=it[f].e,s.aggregates.push({type:u.type,field:e(u.field,t)});r={};r[h.from]=t._fromTable;h.expand&&(r[h.expand]=t._expands);r[h.select]=l.onSelect?o(this,"onSelect",e(l.onSelect.fieldNames,t),t):"";r[h.count]=t._requiresCount?o(this,"onCount",t._requiresCount,t):"";r[h.search]=s.searches.length?o(this,"onSearch",s.searches,t):"";r[h.skip]=l.onSkip?o(this,"onSkip",e(l.onSkip.nos,t),t):"";r[h.take]=l.onTake?o(this,"onTake",e(l.onTake.nos,t),t):"";r[h.antiForgery]=n.dataSource.antiForgery?n.antiForgeryToken().value:"";r[h.where]=s.filters.length||s.searches.length?o(this,"onWhere",s.filters,t):"";r[h.sortBy]=s.sorted.length?o(this,"onSortBy",s.sorted,t):"";r[h.group]=s.grouped.length?o(this,"onGroup",s.grouped,t):"";r[h.aggregates]=s.aggregates.length?o(this,"onAggregates",s.aggregates,t):"";r.param=[];o(this,"addParams",{dm:n,query:t,params:rt,reqParams:r});for(v in r)(c(r[v])||r[v]===""||r[v].length===0||v==="params")&&delete r[v];return(h.skip in r&&h.take in r||p===null||(r[h.skip]=o(this,"onSkip",y,t),r[h.take]=o(this,"onTake",p,t)),k=this.pvt,this.pvt={},this.options.requestType==="json")?{data:JSON.stringify(r),url:b,ejPvtData:k,type:"POST",contentType:"application/json; charset=utf-8"}:(u=this.convertToQueryString(r,t,n),u=(n.dataSource.url.indexOf("?")!==-1?"&":"/")+u,{type:"GET",url:u.length?b.replace(/\/*$/,u):b,ejPvtData:k})},convertToQueryString:function(t,i,r){return r.dataSource.url&&r.dataSource.url.indexOf("?")!==-1?n.param(t):"?"+n.param(t)},processResponse:function(n,i,u,f,e,o){var c=e.ejPvtData||{},v=n.groupDs,y,a,h,s,d,w;if(f&&f.getResponseHeader("Content-Type")&&f.getResponseHeader("Content-Type").indexOf("xml")!=-1&&n.nodeType==9)return u._requiresCount?{result:[],count:0}:[];if(y=JSON.parse(e.data),y&&y.action==="batch"&&n.added)return o.added=n.added,o;if(n.d&&(n=n.d),c&&c.aggregates&&c.aggregates.length){var l=c.aggregates,h={},p,k={};for(("count"in n)&&(h.count=n.count),n.result&&(h.result=n.result),n.aggregate&&(n=n.aggregate),s=0;s<l.length;s++)p=t.aggregates[l[s].type],p&&(k[l[s].field+" - "+l[s].type]=p(n,l[s].field));h.aggregates=k;n=h}if(c&&c.groups&&c.groups.length){for(a=c.groups,h={},("count"in n)&&(h.count=n.count),n.aggregates&&(h.aggregates=n.aggregates),n.result&&(n=n.result),s=0;s<a.length;s++)d=null,w=b(a[s],u.queries),t.isNullOrUndefined(v)||(v=t.group(v,a[s],null,w)),n=t.group(n,a[s],c.aggregates,w,d,v);return h.count!=r?h.result=n:h=n,h}return n},onGroup:function(n){this.pvt.groups=n},onAggregates:function(n){this.pvt.aggregates=n},batchRequest:function(n,t,i,r){var u={changed:t.changed,added:t.added,deleted:t.deleted,action:"batch",table:i.url,key:i.key,antiForgery:n.dataSource.antiForgery?n.antiForgeryToken().value:""};return r&&this.addParams({dm:n,query:r,params:r._params,reqParams:u}),{type:"POST",url:n.dataSource.batchUrl||n.dataSource.crudUrl||n.dataSource.removeUrl||n.dataSource.url,contentType:"application/json; charset=utf-8",dataType:"json",data:JSON.stringify(u)}},beforeSend:function(){},insert:function(n,t,i,r){var u={value:t,table:i,action:"insert",antiForgery:n.dataSource.antiForgery?n.antiForgeryToken().value:""};return r&&this.addParams({dm:n,query:r,params:r._params,reqParams:u}),{url:n.dataSource.insertUrl||n.dataSource.crudUrl||n.dataSource.url,data:JSON.stringify(u)}},remove:function(n,t,i,r,u){var f={key:i,keyColumn:t,table:r,action:"remove",antiForgery:n.dataSource.antiForgery?n.antiForgeryToken().value:""};return u&&this.addParams({dm:n,query:u,params:u._params,reqParams:f}),{type:"POST",url:n.dataSource.removeUrl||n.dataSource.crudUrl||n.dataSource.url,data:JSON.stringify(f)}},update:function(n,t,i,r,u){var f={value:i,action:"update",keyColumn:t,key:i[t],table:r,antiForgery:n.dataSource.antiForgery?n.antiForgeryToken().value:""};return u&&this.addParams({dm:n,query:u,params:u._params,reqParams:f}),{type:"POST",url:n.dataSource.updateUrl||n.dataSource.crudUrl||n.dataSource.url,data:JSON.stringify(f)}},getFiltersFrom:function(n,i){var r;n instanceof Array&&n.length||f("ej.SubQuery: Array of key values required");var u=i._fKey,e,o=u,h=i._key,s=[],c=typeof n[0]!="object";for(typeof n[0]!="object"&&(o=null),r=0;r<n.length;r++)e=c?n[r]:t.pvt.getObject(h||o,n[r]),s.push(new t.Predicate(u,"equal",e));return t.Predicate.or(s)},addParams:function(n){var e=n.dm,u=n.query,o=n.params,i=n.reqParams,r,t;for(i.params={},r=0;t=o[r];r++)i[t.key]&&f("ej.Query: Custom Param is conflicting other request arguments"),i[t.key]=t.value,t.fn&&(i[t.key]=t.fn.call(u,t.key,u,e)),i.params[t.key]=i[t.key]}});t.WebMethodAdaptor=(new t.UrlAdaptor).extend({processQuery:function(n,i,r){var u=t.UrlAdaptor.prototype.processQuery(n,i,r),e=t.parseJSON(u.data),f={};return f.value=e,o(this,"addParams",{dm:n,query:i,params:i._params,reqParams:f}),{data:JSON.stringify(f),url:u.url,ejPvtData:u.ejPvtData,type:"POST",contentType:"application/json; charset=utf-8"}},addParams:function(n){var s=n.dm,o=n.query,h=n.params,i=n.reqParams,u,t,r,e;for(i.params={},u=0;t=h[u];u++)i[t.key]&&f("ej.Query: Custom Param is conflicting other request arguments"),r=t.key,e=t.value,t.fn&&(e=t.fn.call(o,t.key,o,s)),i[r]=e,i.params[r]=i[r]}});t.CacheAdaptor=(new t.UrlAdaptor).extend({init:function(n,i,r){var f,u;t.isNullOrUndefined(n)||(this.cacheAdaptor=n);this.pageSize=r;this.guidId=t.getGuid("cacheAdaptor");f={keys:[],results:[]};window.localStorage&&window.localStorage.setItem(this.guidId,JSON.stringify(f));u=this.guidId;t.isNullOrUndefined(i)||setInterval(function(){for(var e,r=t.parseJSON(window.localStorage.getItem(u)),f=[],n=0;n<r.results.length;n++)r.results[n].timeStamp=new Date-new Date(r.results[n].timeStamp),new Date-new Date(r.results[n].timeStamp)>i&&f.push(n);for(e=f,n=0;n<f.length;n++)r.results.splice(f[n],1),r.keys.splice(f[n],1);window.localStorage.removeItem(u);window.localStorage.setItem(u,JSON.stringify(r))},i)},generateKey:function(n,t){var h=a(t.queries,"onSortBy"),c=a(t.queries,"onGroup"),o=a(t.queries,"onWhere"),l=a(t.queries,"onSearch"),v=a(t.queries,"onPage"),s=g(t.queries,["onSelect","onPage","onSkip","onTake","onRange"]),y=t._params,r=n,u,i,f,e;for(s.onPage&&(r+=s.onPage.pageIndex),h.forEach(function(n){r+=n.e.direction+n.e.fieldName}),c.forEach(function(n){r+=n.e.fieldName}),l.forEach(function(n){r+=n.e.searchKey}),u=0;u<o.length;u++)if(i=o[u],i.e.isComplex){for(f=t.clone(),f.queries=[],e=0;e<i.e.predicates.length;e++)f.queries.push({fn:"onWhere",e:i.e.predicates[e],filter:t.queries.filter});r+=i.e.condition+this.generateKey(n,f)}else r+=i.e.field+i.e.operator+i.e.value;return r},processQuery:function(n,i){var f=this.generateKey(n.dataSource.url,i),r,u;return(window.localStorage&&(r=t.parseJSON(window.localStorage.getItem(this.guidId))),u=r?r.results[r.keys.indexOf(f)]:null,u!=null&&!this._crudAction&&!this._insertAction)?u:(this._crudAction=null,this._insertAction=null,this.cacheAdaptor.processQuery.apply(this.cacheAdaptor,[].slice.call(arguments,0)))},processResponse:function(i,r,u,f,e,o){var h;if(this._insertAction||e&&this.cacheAdaptor.options.batch&&e.url.endsWith(this.cacheAdaptor.options.batch)&&e.type.toLowerCase()==="post")return this.cacheAdaptor.processResponse(i,r,u,f,e,o);var i=this.cacheAdaptor.processResponse.apply(this,[].slice.call(arguments,0)),c=this.generateKey(r.dataSource.url,u),s={};for(window.localStorage&&(s=t.parseJSON(window.localStorage.getItem(this.guidId))),h=n.inArray(c,s.keys),h!=-1&&(s.results.splice(h,1),s.keys.splice(h,1)),s.results[s.keys.push(c)-1]={keys:c,result:i.result,timeStamp:new Date,count:i.count};s.results.length>this.pageSize;)s.results.splice(0,1),s.keys.splice(0,1);return window.localStorage.setItem(this.guidId,JSON.stringify(s)),i},update:function(n,t,i,r){return this._crudAction=!0,this.cacheAdaptor.update(n,t,i,r)},insert:function(n,t,i){return this._insertAction=!0,this.cacheAdaptor.insert(n,t,i)},remove:function(n,t,i,r){return this._crudAction=!0,this.cacheAdaptor.remove(n,t,i,r)},batchRequest:function(n,t,i){return this.cacheAdaptor.batchRequest(n,t,i)}});var a=function(n,t){return n.filter(function(n){return n.fn===t})||[]},g=function(n,t){for(var r=n.filter(function(n){return t.indexOf(n.fn)!==-1}),u={},i=0;i<r.length;i++)u[r[i].fn]||(u[r[i].fn]=r[i].e);return u},o=function(n,t,i,r){if(n[t]){var u=n[t](i,r);c(u)||(i=u)}return i};t.ODataAdaptor=(new t.UrlAdaptor).extend({options:{requestType:"get",accept:"application/json;odata=light;q=1,application/json;odata=verbose;q=0.5",multipartAccept:"multipart/mixed",sortBy:"$orderby",select:"$select",skip:"$skip",take:"$top",count:"$inlinecount",where:"$filter",expand:"$expand",batch:"$batch",changeSet:"--changeset_",batchPre:"batch_",contentId:"Content-Id: ",batchContent:"Content-Type: multipart/mixed; boundary=",changeSetContent:"Content-Type: application/http\nContent-Transfer-Encoding: binary ",batchChangeSetContentType:"Content-Type: application/json; charset=utf-8 "},onEachWhere:function(n,t){return n.isComplex?this.onComplexPredicate(n,t):this.onPredicate(n,t)},_typeStringQuery:function(n,i,r,u,f){r.indexOf("'")!=-1&&(r=r.replace(new RegExp(/'/g),"''"));return/[ !@@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(r)&&(r=encodeURIComponent(r)),r="'"+r+"'",i&&(u="cast("+u+", 'Edm.String')"),t.isGUID(r)&&(f="guid"),n.ignoreCase&&(f?u:u="tolower("+u+")",r=r.toLowerCase()),{val:r,guid:f,field:u}},onPredicate:function(n,i,r){var f="",o,l,e=n.value,v=typeof e,h=this._p(n.field),s,c,y,a;if(e instanceof Date&&(e="datetime'"+u.replacer(e).toJSON()+"'"),v==="string"&&(c=this._typeStringQuery(n,r,e,h,l),e=c.val,h=c.field,l=c.guid),o=t.data.odBiOperator[n.operator],n.anyCondition!=""&&o)return f+=e.table,f+="/"+n.anyCondition,f+="(d:d/",f+=h,f+=o,f+=e.value,f+")";if(n.operator=="in"||n.operator=="notin"){for(f+="(",s=0;s<e.length;s++)e[s]instanceof Date&&(e[s]="datetime'"+u.replacer(e[s]).toJSON()+"'"),typeof e[s]=="string"&&(c=this._typeStringQuery(n,r,e[s],h,l),e[s]=c.val,h=c.field,l=c.guid),f+=h,f+=o,f+=e[s],s!=e.length-1&&(f+=n.operator=="in"?" or ":" and ");return f+")"}return o?this.onOperation(f,o,h,e,l):(o=t.data.odUniOperator[n.operator],!o||v!=="string")?"":(o==="substringof"&&(y=e,e=h,h=y),f+=o+"(",f+=h+",",l&&(f+=l),f+=e+")",n.operator=="notcontains"&&(f+=" eq false"),n.anyCondition!=""&&o)?(a+=e.table,a+="/"+n.anyCondition,a+="(d:d/",f+=f,a+")"):f},onOperation:function(n,t,i,r,u){return n+=i,n+=t,u&&(n+=u),n+r},onComplexPredicate:function(n,t){for(var r=[],i=0;i<n.predicates.length;i++)r.push("("+this.onEachWhere(n.predicates[i],t)+")");return r.join(" "+n.condition+" ")},onWhere:function(n){return this.pvt.searches&&n.push(this.onEachWhere(this.pvt.searches,null,!0)),n.join(" and ")},onEachSearch:function(n){var r,i;for(n.fields.length===0&&f("Query() - Search : oData search requires list of field names to search"),r=this.pvt.searches||[],i=0;i<n.fields.length;i++)r.push(new t.Predicate(n.fields[i],n.operator,n.key,n.ignoreCase));this.pvt.searches=r},onSearch:function(){return this.pvt.searches=t.Predicate.or(this.pvt.searches),""},onEachSort:function(n){var i=[],t;if(n.name instanceof Array)for(t=0;t<n.name.length;t++)i.push(this._p(n.name[t]));else i.push(this._p(n.name)+(n.direction==="descending"?" desc":""));return i.join(",")},onSortBy:function(n){return n.reverse().join(",")},onGroup:function(n){return this.pvt.groups=n,""},onSelect:function(n){for(var t=0;t<n.length;t++)n[t]=this._p(n[t]);return n.join(",")},onAggregates:function(n){return this.pvt.aggregates=n,""},onCount:function(n){return n===!0?"allpages":""},beforeSend:function(n,t,i){i.url.endsWith(this.options.batch)&&i.type.toLowerCase()==="post"&&(t.setRequestHeader("Accept",l.multipartAccept),t.setRequestHeader("DataServiceVersion","2.0"),t.overrideMimeType("text/plain; charset=x-user-defined"));n.dataSource.crossDomain||(t.setRequestHeader("DataServiceVersion","2.0"),t.setRequestHeader("MaxDataServiceVersion","2.0"))},processResponse:function(i,r,f,e,o,s){var a,l,v,d,tt,g,it,k,h,ft;if(!t.isNullOrUndefined(i.d)&&(a=f&&f._requiresCount?i.d.results:i.d,!t.isNullOrUndefined(a)))for(h=0;h<a.length;h++)t.isNullOrUndefined(a[h].__metadata)||delete a[h].__metadata;if(l=o&&o.ejPvtData,e&&e.getResponseHeader("Content-Type")&&e.getResponseHeader("Content-Type").indexOf("xml")!=-1&&i.nodeType==9)return f._requiresCount?{result:[],count:0}:[];if(o&&this.options.batch&&o.url.endsWith(this.options.batch)&&o.type.toLowerCase()==="post"){if(v=e.getResponseHeader("Content-Type"),v=v.substring(v.indexOf("=batchresponse")+1),i=i.split(v),i.length<2)return;for(i=i[1],g=/(?:\bContent-Type.+boundary=)(changesetresponse.+)/i.exec(i),i.replace(g[0],""),it=g[1],i=i.split(it),h=i.length;h>-1;h--)/\bContent-ID:/i.test(i[h])&&/\bHTTP.+201/.test(i[h])&&(d=parseInt(/\bContent-ID: (\d+)/i.exec(i[h])[1]),s.added[d]&&(tt=u.parseJson(/^\{.+\}/m.exec(i[h])[0]),n.extend(s.added[d],this.processResponse(tt))));return s}var y=e&&e.getResponseHeader("DataServiceVersion"),w=null,rt={};if(y=y&&parseInt(y,10)||2,f&&f._requiresCount&&((i.__count||i["odata.count"])&&(w=i.__count||i["odata.count"]),i.d&&(i=i.d),(i.__count||i["odata.count"])&&(w=i.__count||i["odata.count"])),y===3&&i.value&&(i=i.value),i.d&&(i=i.d),y<3&&i.results&&(i=i.results),l&&l.aggregates&&l.aggregates.length){var p=l.aggregates,nt,ut={};for(h=0;h<p.length;h++)nt=t.aggregates[p[h].type],nt&&(ut[p[h].field+" - "+p[h].type]=nt(i,p[h].field));rt=ut}if(l&&l.groups&&l.groups.length)for(k=l.groups,h=0;h<k.length;h++)ft=b(k[h],f.queries),i=t.group(i,k[h],l.aggregates,ft);return c(w)?i:{result:i,count:w,aggregates:rt}},convertToQueryString:function(n,t,i){var r=[],u=n.table||"",f;delete n.table;i.dataSource.requiresFormat&&(n.$format="json");for(f in n)r.push(f+"="+n[f]);return(r=r.join("&"),i.dataSource.url&&i.dataSource.url.indexOf("?")!==-1&&!u)?r:r.length?u+"?"+r:u||""},insert:function(n,t,i){return{url:n.dataSource.url.replace(/\/*$/,i?"/"+i:""),data:JSON.stringify(t)}},remove:function(n,i,r,u){return typeof r=="string"?{type:"DELETE",url:t.isGUID(r)?n.dataSource.url.replace(/\/*$/,u?"/"+u:"")+"("+r+")":n.dataSource.url.replace(/\/*$/,u?"/"+u:"")+"('"+r+"')"}:{type:"DELETE",url:n.dataSource.url.replace(/\/*$/,u?"/"+u:"")+"("+r+")"}},update:function(n,i,r,u){var f;return f=typeof r[i]=="string"?t.isGUID(r[i])?n.dataSource.url.replace(/\/*$/,u?"/"+u:"")+"("+r[i]+")":n.dataSource.url.replace(/\/*$/,u?"/"+u:"")+"('"+r[i]+"')":n.dataSource.url.replace(/\/*$/,u?"/"+u:"")+"("+r[i]+")",{type:"PUT",url:f,data:JSON.stringify(r),accept:this.options.accept}},batchRequest:function(n,i,r){var e=r.guid=t.getGuid(l.batchPre),o=n.dataSource.url.replace(/\/*$/,"/"+this.options.batch),f={url:r.url,key:r.key,cid:1,cSet:t.getGuid(l.changeSet)},u="--"+e+"\n";return u+="Content-Type: multipart/mixed; boundary="+f.cSet.replace("--","")+"\n",this.pvt.changeSet=0,u+=this.generateInsertRequest(i.added,f),u+=this.generateUpdateRequest(i.changed,f),u+=this.generateDeleteRequest(i.deleted,f),u+=f.cSet+"--\n",u+="--"+e+"--",{type:"POST",url:o,contentType:"multipart/mixed; charset=UTF-8;boundary="+e,data:u}},generateDeleteRequest:function(n,t){var i,u,r;if(!n)return"";for(i="",r=0;r<n.length;r++)i+="\n"+t.cSet+"\n",i+=l.changeSetContent+"\n\n",i+="DELETE ",u=typeof n[r][t.key]=="string"?"'"+n[r][t.key]+"'":n[r][t.key],i+=t.url+"("+u+") HTTP/1.1\n",i+="If-Match : * \n",i+="Accept: "+l.accept+"\n",i+="Content-Id: "+this.pvt.changeSet+++"\n",i+=l.batchChangeSetContentType+"\n";return i+"\n"},generateInsertRequest:function(n,t){var i,r;if(!n)return"";for(i="",r=0;r<n.length;r++)i+="\n"+t.cSet+"\n",i+=l.changeSetContent+"\n\n",i+="POST ",i+=t.url+" HTTP/1.1\n",i+="Accept: "+l.accept+"\n",i+="Content-Id: "+this.pvt.changeSet+++"\n",i+=l.batchChangeSetContentType+"\n\n",i+=JSON.stringify(n[r])+"\n";return i},generateUpdateRequest:function(n,t){var i,u,r;if(!n)return"";for(i="",r=0;r<n.length;r++)i+="\n"+t.cSet+"\n",i+=l.changeSetContent+"\n\n",i+="PUT ",u=typeof n[r][t.key]=="string"?"'"+n[r][t.key]+"'":n[r][t.key],i+=t.url+"("+u+") HTTP/1.1\n",i+="If-Match : * \n",i+="Accept: "+l.accept+"\n",i+="Content-Id: "+this.pvt.changeSet+++"\n",i+=l.batchChangeSetContentType+"\n\n",i+=JSON.stringify(n[r])+"\n\n";return i},_p:function(n){return n.replace(/\./g,"/")}});t.ODataV4Adaptor=(new t.ODataAdaptor).extend({options:{requestType:"get",accept:"application/json;odata=light;q=1,application/json;odata=verbose;q=0.5",multipartAccept:"multipart/mixed",sortBy:"$orderby",select:"$select",skip:"$skip",take:"$top",count:"$count",search:"$search",where:"$filter",expand:"$expand",batch:"$batch",changeSet:"--changeset_",batchPre:"batch_",contentId:"Content-Id: ",batchContent:"Content-Type: multipart/mixed; boundary=",changeSetContent:"Content-Type: application/http\nContent-Transfer-Encoding: binary ",batchChangeSetContentType:"Content-Type: application/json; charset=utf-8 "},onCount:function(n){return n===!0?"true":""},onPredicate:function(n,i,r){var u="",f=n.value,e=f instanceof Date;return t.data.odUniOperator.contains="contains",u=t.ODataAdaptor.prototype.onPredicate.call(this,n,i,r),t.data.odUniOperator.contains="substringof",e&&(u=u.replace(/datetime'(.*)'$/,"$1")),u},onOperation:function(n,t,i,r,u){return u?(n+="("+i,n+=t,n+=r.replace(/["']/g,"")+")"):(n+=i,n+=t,n+=r),n},onEachSearch:function(n){var t=this.pvt.search||[];t.push(n.key);this.pvt.search=t},onSearch:function(){return this.pvt.search.join(" OR ")},beforeSend:function(){},processQuery:function(n,i){for(var f,o,e=/\/[\d*\/]*/g,u="",r=i._expands.length-1;r>0;r--)if(u.indexOf(i._expands[r])>=0)i._expands.pop();else if(e.test(i._expands[r])){for(u=i._expands.pop(),f=u.replace(e,"($expand="),o=0;o<u.split(e).length-1;o++)f=f+")";i._expands.unshift(f);r++}return t.ODataAdaptor.prototype.processQuery.apply(this,[n,i])},processResponse:function(i,r,f,e,o,s){var l=o&&o.ejPvtData,a,w,nt,k,tt,y,d,p,h,rt;if(e&&e.getResponseHeader("Content-Type")&&e.getResponseHeader("Content-Type").indexOf("xml")!=-1&&i.nodeType==9)return f._requiresCount?{result:[],count:0}:[];if(o&&this.options.batch&&o.url.endsWith(this.options.batch)&&o.type.toLowerCase()==="post"){if(a=e.getResponseHeader("Content-Type"),a=a.substring(a.indexOf("=batchresponse")+1),i=i.split(a),i.length<2)return;for(i=i[1],k=/(?:\bContent-Type.+boundary=)(changesetresponse.+)/i.exec(i),i.replace(k[0],""),tt=k[1],i=i.split(tt),h=i.length;h>-1;h--)/\bContent-ID:/i.test(i[h])&&/\bHTTP.+201/.test(i[h])&&(w=parseInt(/\bContent-ID: (\d+)/i.exec(i[h])[1]),s.added[w]&&(nt=u.parseJson(/^\{.+\}/m.exec(i[h])[0]),n.extend(s.added[w],this.processResponse(nt))));return s}if(y=null,d={},f&&f._requiresCount&&"@odata.count"in i&&(y=i["@odata.count"]),i=t.isNullOrUndefined(i.value)?i:i.value,l&&l.aggregates&&l.aggregates.length){var v=l.aggregates,g,it={};for(h=0;h<v.length;h++)g=t.aggregates[v[h].type],g&&(it[v[h].field+" - "+v[h].type]=g(i,v[h].field));d=it}if(l&&l.groups&&l.groups.length)for(p=l.groups,h=0;h<p.length;h++)rt=b(p[h],f.queries),i=t.group(i,p[h],l.aggregates,rt);return c(y)?i:{result:i,count:y,aggregates:d}}});t.JsonAdaptor=(new t.Adaptor).extend({processQuery:function(n,t){for(var u=n.dataSource.json.slice(0),o=u.length,s=!0,f,i,h={},e=0;e<t.queries.length;e++)i=t.queries[e],f=this[i.fn].call(this,u,i.e,t),i.fn=="onAggregates"?h[i.e.field+" - "+i.e.type]=f:u=f!==r?f:u,(i.fn==="onPage"||i.fn==="onSkip"||i.fn==="onTake"||i.fn==="onRange")&&(s=!1),s&&(o=u.length);return t._requiresCount&&(u={result:u,count:o,aggregates:h}),u},batchRequest:function(n,t,i){for(var r=0;r<t.added.length;r++)this.insert(n,t.added[r]);for(r=0;r<t.changed.length;r++)this.update(n,i.key,t.changed[r]);for(r=0;r<t.deleted.length;r++)this.remove(n,i.key,t.deleted[r]);return t},onWhere:function(n,t){return n?n.filter(function(n){return t.validate(n)}):n},onAggregates:function(n,i){var r=t.aggregates[i.type];return!n||!r||n.length==0?null:r(n,i.field)},onSearch:function(n,i){return!n||!n.length?n:(i.fieldNames.length===0&&t.pvt.getFieldList(n[0],i.fieldNames),n.filter(function(n){for(var r=0;r<i.fieldNames.length;r++)if(i.comparer.call(n,t.pvt.getObject(i.fieldNames[r],n),i.searchKey,i.ignoreCase,i.ignoreAccent))return!0;return!1}))},onSortBy:function(n,i,r){var o,u,f;if(!n)return n;if(u=e(i.fieldName,r),!u)return n.sort(i.comparer);if(u instanceof Array){for(u=u.slice(0),f=u.length-1;f>=0;f--)u[f]&&(o=i.comparer,u[f].endsWith(" desc")&&(o=t.pvt.fnSort(t.sortOrder.Descending),u[f]=u[f].replace(" desc","")),n=it(n,u[f],o,[]));return n}return it(n,u,i.comparer,r?r.queries:[])},onGroup:function(n,i,r){var u,o,s,f,h;if(!n)return n;if(u=a(r.queries,"onAggregates"),o=[],u.length)for(f=0;f<u.length;f++)s=u[f].e,o.push({type:s.type,field:e(s.field,r)});return h=b(i.fieldName,r.queries),t.group(n,e(i.fieldName,r),o,h)},onPage:function(n,t,i){var r=e(t.pageSize,i),u=(e(t.pageIndex,i)-1)*r,f=u+r;return n?n.slice(u,f):n},onRange:function(n,t){return n?n.slice(e(t.start),e(t.end)):n},onTake:function(n,t){return n?n.slice(0,e(t.nos)):n},onSkip:function(n,t){return n?n.slice(e(t.nos)):n},onSelect:function(n,i){return n?t.select(n,e(i.fieldNames)):n},insert:function(n,t){return n.dataSource.json.push(t)},remove:function(n,i,r){var f=n.dataSource.json,u;for(typeof r=="object"&&(r=t.getObject(i,r)),u=0;u<f.length;u++)if(t.getObject(i,f[u])===r)break;return u!==f.length?f.splice(u,1):null},update:function(i,r,u){for(var e=i.dataSource.json,o=t.getObject(r,u),f=0;f<e.length;f++)if(t.getObject(r,e[f])===o)break;return f<e.length?n.extend(e[f],u):null}});t.ForeignKeyAdaptor=function(i,u){var f=(new t[u||"JsonAdaptor"]).extend({init:function(){var r,n;for(this.foreignData=[],this.key=[],this.adaptorType=u,this.value=[],this.fValue=[],this.keyField=[],r=i,n=0;n<r.length;n++)this.foreignData[n]=r[n].dataSource,this.key[n]=r[n].foreignKeyField,this.fValue[n]=t.isNullOrUndefined(r[n].field)?r[n].foreignKeyValue:r[n].field+"_"+r[n].foreignKeyValue,this.value[n]=r[n].foreignKeyValue,this.keyField[n]=r[n].field||r[n].foreignKeyField,this.initial=!0},processQuery:function(n,i){var e=n.dataSource.json,u,f,r;if(this.initial){for(u=0;u<e.length;u++)for(f=this,r=0;r<this.foreignData.length;r++)this.foreignData[r].filter(function(n){t.getObject(f.key[r],n)==t.getObject(f.keyField[r],e[u])&&(e[u][f.fValue[r]]=t.getObject(f.value[r],n))});this.initial=!1}return this.base.processQuery.apply(this,[n,i])},setValue:function(i){for(var o,f,e,u=0;u<this.foreignData.length;u++)o=this,f=i[this.fValue[u]],typeof f!="string"||isNaN(f)||(f=t.parseFloat(f)),e=n.grep(o.foreignData[u],function(n){return n[o.value[u]]==f})[0],t.isNullOrUndefined(e)&&(e=n.grep(o.foreignData[u],function(n){return n[o.key[u]]==f})[0],t.getObject(this.value[u],e)!=r&&t.createObject(o.value[u],t.getObject(this.value[u],e),i)),t.getObject(this.value[u],e)!=r&&t.createObject(this.keyField[u],t.getObject(this.key[u],e),i)},insert:function(n,t,i){return this.setValue(t),{url:n.dataSource.insertUrl||n.dataSource.crudUrl||n.dataSource.url,data:JSON.stringify({value:t,table:i,action:"insert",antiForgery:n.dataSource.antiForgery?n.antiForgeryToken().value:""})}},update:function(n,i,r,u){return this.setValue(r),t.JsonAdaptor.prototype.update(n,i,r,u),{type:"POST",url:n.dataSource.updateUrl||n.dataSource.crudUrl||n.dataSource.url,data:JSON.stringify({value:r,action:"update",keyColumn:i,key:r[i],table:u,antiForgery:n.dataSource.antiForgery?n.antiForgeryToken().value:""})}}});return n.extend(this,new f),this};t.remoteSaveAdaptor=(new t.JsonAdaptor).extend({beforeSend:t.UrlAdaptor.prototype.beforeSend,insert:t.UrlAdaptor.prototype.insert,update:t.UrlAdaptor.prototype.update,remove:t.UrlAdaptor.prototype.remove,addParams:t.UrlAdaptor.prototype.addParams,batchRequest:function(n,t,i,r){var u={changed:t.changed,added:t.added,deleted:t.deleted,action:"batch",table:i.url,key:i.key,antiForgery:n.dataSource.antiForgery?n.antiForgeryToken().value:""};return r&&this.addParams({dm:n,query:r,params:r._params,reqParams:u}),{type:"POST",url:n.dataSource.batchUrl||n.dataSource.crudUrl||n.dataSource.url,contentType:"application/json; charset=utf-8",dataType:"json",data:JSON.stringify(u)}},processResponse:function(n,i,r,u,f,e,o){if(t.isNullOrUndefined(e))return n.d?n.d:n;n.d&&(n=n.d);n.added&&(e.added=t.parseJSON(n.added));n.changed&&(e.changed=t.parseJSON(n.changed));n.deleted&&(e.deleted=t.parseJSON(n.deleted));for(var s=0;s<e.added.length;s++)t.JsonAdaptor.prototype.insert(i,e.added[s]);for(s=0;s<e.changed.length;s++)t.JsonAdaptor.prototype.update(i,o,e.changed[s]);for(s=0;s<e.deleted.length;s++)t.JsonAdaptor.prototype.remove(i,o,e.deleted[s]);return n}});t.WebApiAdaptor=(new t.ODataAdaptor).extend({insert:function(n,t){return{type:"POST",url:n.dataSource.url,data:JSON.stringify(t)}},remove:function(n,t,i){return{type:"DELETE",url:n.dataSource.url+"/"+i,data:JSON.stringify(i)}},update:function(n,t,i){return{type:"PUT",url:n.dataSource.url,data:JSON.stringify(i)}},batchRequest:function(i,r,u){var e=u.guid=t.getGuid(l.batchPre),f=[];return n.each(r.added,function(n,t){f.push("--"+e);f.push("Content-Type: application/http; msgtype=request","");f.push("POST "+i.dataSource.insertUrl+" HTTP/1.1");f.push("Content-Type: application/json; charset=utf-8");f.push("Host: "+location.host);f.push("",t?JSON.stringify(t):"")}),n.each(r.changed,function(n,t){f.push("--"+e);f.push("Content-Type: application/http; msgtype=request","");f.push("PUT "+i.dataSource.updateUrl+" HTTP/1.1");f.push("Content-Type: application/json; charset=utf-8");f.push("Host: "+location.host);f.push("",t?JSON.stringify(t):"")}),n.each(r.deleted,function(n,t){f.push("--"+e);f.push("Content-Type: application/http; msgtype=request","");f.push("DELETE "+i.dataSource.removeUrl+"/"+t[u.key]+" HTTP/1.1");f.push("Content-Type: application/json; charset=utf-8");f.push("Host: "+location.host);f.push("",t?JSON.stringify(t):"")}),f.push("--"+e+"--",""),{type:"POST",url:i.dataSource.batchUrl||i.dataSource.crudUrl||i.dataSource.url,data:f.join("\r\n"),contentType:'multipart/mixed; boundary="'+e+'"'}},processResponse:function(n,i,r,u,f){var o=f&&f.ejPvtData,l,e,w;if(f&&f.type.toLowerCase()!="post"){var h=u&&u.getResponseHeader("DataServiceVersion"),a=null,y={};if(h=h&&parseInt(h,10)||2,r&&r._requiresCount&&(c(n.Count)||(a=n.Count)),h<3&&n.Items&&(n=n.Items),o&&o.aggregates&&o.aggregates.length){var s=o.aggregates,v,p={};for(e=0;e<s.length;e++)v=t.aggregates[s[e].type],v&&(p[s[e].field+" - "+s[e].type]=v(n,s[e].field));y=p}if(o&&o.groups&&o.groups.length)for(l=o.groups,e=0;e<l.length;e++)w=b(l[e],r.queries),n=t.group(n,l[e],o.aggregates,w);return c(a)?n:{result:n,count:a,aggregates:y}}}});e=function(n,t){return typeof n=="function"?n.call(t||{}):n};t.TableModel=function(i,r,u,e){var c,o,l,s;if(!p(this,t.TableModel))return new t.TableModel(r);for(p(r,Array)||f("ej.TableModel - Json Array is required"),c=[],l=h(ht,this),s=0;s<r.length;s++){o=new t.Model(r[s],this);o.state="unchanged";o.on("stateChange",l);e&&o.computes(e);c.push(o)}return this.name=i||"table1",this.rows=t.NotifierArray(c),this._deleted=[],this._events=n({}),this.dataManager=u,this._isDirty=!1,this};t.TableModel.prototype={on:function(n,t){this._events.on(n,t)},off:function(n,t){this._events.off(n,t)},setDataManager:function(n){this.dataManagar=n},saveChanges:function(){if(this.dataManager&&p(this.dataManager,t.DataManager)||f("ej.TableModel - saveChanges : Set the dataManager using setDataManager function"),this.isDirty()){var n=this.dataManager.saveChanges(this.getChanges(),this.key,this.name);n.done(h(function(n){for(var t=this.toArray(),i=0;i<t.length;i++)t.state==="added"&&t.set(this.key,n.added.filter(function(n){return n[this.key]===t.get(this.key)})[0][this.key]),t[i].markCommit();this._events.triggerHandler({type:"save",table:this})},this));n.fail(h(function(n){this.rejectChanges();this._events.triggerHandler({type:"reject",table:this,error:n})},this));this._isDirty=!1}},rejectChanges:function(){for(var t=this.toArray(),n=0;n<t.length;n++)t[n].revert(!0);this._isDirty=!1;this._events.triggerHandler({type:"reject",table:this})},insert:function(n){var i=new t.Model(n);i._isDirty=this._isDirty=!0;this.rows.push(i);this._events.triggerHandler({type:"insert",model:i,table:this})},update:function(n){var r;this.key||f("TableModel - update : Primary key should be assigned to TableModel.key");var i=n,t,u=this.key,e=i[u];t=this.rows.array.filter(function(n){return n.get(u)===e});t=t[0];for(r in i)t.set(r,i[r]);this._isDirty=!0;this._events.triggerHandler({type:"update",model:t,table:this})},remove:function(n){var t,i,e,u;for(this.key||f("TableModel - update : Primary key should be assigned to TableModel.key"),t=this.key,i=-1,n&&typeof n=="object"&&(n=n[t]!==r?n[t]:n.get(t)),u=0;u<this.rows.length();u++)if(this.rows.array[u].get(t)===n){i=u;break}i>-1&&(e=this.rows.removeAt(i),e.markDelete(),this._deleted.push({model:e,position:i}),this._isDirty=!0,this._events.triggerHandler({type:"remove",model:e,table:this}))},isDirty:function(){return this._isDirty},getChanges:function(){for(var i={added:[],changed:[]},r=this.toArray(),n=0;n<r.length;n++)i[r[n].state]&&i[r[n].state].push(r[n].json);return i.deleted=t.select(this._deleted,["model"]),i},toArray:function(){return this.rows.toArray()},setDirty:function(n,t){this._isDirty!==!!n&&(this._isDirty=!!n,this._events.triggerHandler({type:"dirty",table:this,model:t}))},get:function(n){return this.rows.array[n]},length:function(){return this.rows.array.length},bindTo:function(t){var i=nt,e=n(t.html()),f=this.toArray(),u,r;for(n.inArray(t.prop("tagName").toLowerCase(),["table","tbody"])&&(i=tt),i.insertBefore(t),t.detach().empty(),r=0;r<f.length;r++)u=e.clone(),f[r].bindTo(u),t.append(u);t.insertAfter(i);i.remove()}};nt=i?n(document.createElement("div")):{};tt=i?n(document.createElement("tr")):{};t.Model=function(i,r,u){typeof r=="string"&&(u=r,r=null);this.$id=et("m");this.json=i;this.table=r instanceof t.TableModel?r:null;this.name=u||this.table&&this.table.name;this.dataManager=r instanceof t.DataManager?r:r.dataManagar;this.actual={};this._events=n({});this.isDirty=!1;this.state="added";this._props=[];this._computeEls={};this._fields={};this._attrEls={};this._updates={};this.computed={}};t.Model.prototype={computes:function(t){n.extend(this.computed,t)},on:function(n,t){this._events.on(n,t)},off:function(n,t){this._events.off(n,t)},set:function(n,t){var i=this.json,e=n,f,u;for(n=n.split("."),u=0;u<n.length-1;u++)n=n[0],i=i[n[0]];this.isDirty=!0;this.changeState("changed",{from:"set"});f=i[n];this.actual[n]!==r||n in this.actual||(this.actual[n]=t);i[n]=t;this._updateValues(n,t);this._events.triggerHandler({type:e,current:t,previous:f,model:this})},get:function(n){return t.pvt.getObject(n,this.json)},revert:function(n){for(var t in this.actual)this.json[t]=this.actual[t];this.isDirty=!1;n?this.state="unchanged":this.changeState("unchanged",{from:"revert"})},save:function(i,r){return(i=i||this.dataManagar,r=r||i.dataSource.key,i||f("ej.Model - DataManager is required to commit the changes"),this.state==="added")?i.insert(this.json,this.name).done(t.proxy(function(t){n.extend(this.json,t.record)},this)):this.state==="changed"?i.update(r,this.json,this.name):this.state==="deleted"?i.remove(r,this.json,this.name):void 0},markCommit:function(){this.isDirty=!1;this.changeState("unchanged",{from:"commit"})},markDelete:function(){this.changeState("deleted",{from:"delete"})},changeState:function(t,i){if(this.state!==t){if(this.state==="added")if(t==="deleted")t="unchanged";else return;var r=t;i=i||{};this.state=t;this._events.triggerHandler(n.extend({type:"stateChange",current:t,previous:r,model:this},i))}},properties:function(){if(this._props.length)return this._props;for(var n in this.json)this._props.push(n),this._updates[n]={read:[],input:[]};return this._props},bindTo:function(t){var e=n(t),r,i,o=e.find("[ej-observe], [ej-computed], [ej-prop]"),s=o.length,u,f;for(e.data("ejModel",this),u={fields:[],props:[],computes:[]},f=0;f<s;f++){if(r=o.eq(f),i=r.attr("ej-prop"),i&&this._processAttrib(i,r,u),i=r.attr("ej-observe"),i&&this._props.indexOf(i)!==-1){this._processField(r,i,u);continue}if(i=r.attr("ej-computed"),i){this._processComputed(i,r,u);continue}}e.data("ejModelBinding"+this.$id,u)},unbind:function(t){var u,r={props:this._attrEls,computes:this._computeEls},f=!1,i,c,e,s,h,o;t&&(r=n(t).removeData("ejModel").data("ejModelBinding"+this.$id)||r,f=!0);for(i in this.computed)u=r.computes[i],i=this.computed[i],u&&i.deps&&(this.off(i.deps.join(" "),u.handle),f&&delete this._computeEls[i]);f||(this._computeEls={});for(i in r.props)u=r.props[i],u&&(this.off(u.deps.join(" "),u.handle),delete r.props[i],f&&delete this._attrEls[i]);if(f||(this._attrEls={}),r.fields&&r.fields.length)for(c=r.fields.length,o=0;o<c;o++)e=r.fields[o],n(e).off("change",null,this._changeHandler),h=this.formElements.indexOf(e.tagName.toLowerCase())!==-1?"input":"read",s=this._updates[h].indexOf(e),s!==-1&&this._updates[h].splice(s,1)},_processComputed:function(n,t,i){if(n){var r,u,f=st(n),e=this.formElements.indexOf(t[0].tagName.toLowerCase())!==-1?"val":"html";this.computed[n]&&this.computed[f]||(this.computed[f]={value:new Function("var e = this; return "+n),deps:this._generateDeps(n)},n=f);r=this.computed[n];r.get||(r.get=function(){r.value.call(this.json)});u=r.deps;r=r.value;this._updateDeps(u);this._updateElement(t,e,r);r={el:t,handle:h(this._computeHandle,this,{value:n,type:e})};this._computeEls[n]=r;i.computes[n]=r;this.on(u.join(" "),r.handle)}},_computeHandle:function(n){var t=this._computeEls[n.value];t&&this.computed[n.value]&&this._updateElement(t.el,n.type,this.computed[n.value].value)},_updateElement:function(t,i,r){t[i](r.call(n.extend({},this.json,this.computed)))},_updateDeps:function(n){for(var i=0;i<n.length;i++)!(n[i]in this.json)&&n[i]in this.computed&&t.merge(n,this.computed[n[i]].deps)},_generateDeps:function(n){for(var r=n.replace(/(^e\.)|( e\.)/g,"#%^*##ej.#").split("#%^*#"),t,u=[],i=0;i<r.length;i++)r[i].startsWith("#ej.#")&&(t=r[i].replace("#ej.#","").split(" ")[0],t&&this._props.indexOf(t)!==-1&&u.push(t));return u},_processAttrib:function(n,t,i){var e,o,u={},r,f;for(n=n.replace(/^ +| +$/g,"").split(";"),r=0;r<n.length;r++)(n[r]=n[r].split(":"),n[r].length<2)||(e=n[r][0].replace(/^ +| +$/g,"").replace(/^'|^"|'$|"$/g,""),u[e]=n[r][1].replace(/^ +| +$/g,"").replace(/^'|^"|'$|"$/g,""));n=u;f=[];for(e in n)f.push(n[e]);this._updateDeps(f);this._updateProps(t,n);u=et("emak");o={el:t,handle:h(this._attrHandle,this,u),value:n,deps:f};t.prop("ejmodelattrkey",u);i.props[u]=o;this._attrEls[u]=o;this.on(f.join(" "),o.handle)},_attrHandle:function(n){var t=this._attrEls[n];t&&this._updateProps(t.el,t.value)},_updateProps:function(t,i){var f=this.json,r,u=this.computed;for(var e in i)r=i[e],r in f?r=f[r]:r in u&&(r=u[r],r&&(r=r.value.call(n.extend({},this.json,u)))),c(r)||t.prop(e,r)},_updateValues:function(n,t){var i=this._updates[n];i&&(i.read||i.input)&&(this._ensureItems(i.read,"html",t),this._ensureItems(i.input,"val",t))},_ensureItems:function(t,i,r){if(t)for(var u=t.length-1;u>-1;u--){if(!t[u].offsetParent){t.splice(u,1);continue}n(t[u])[i](r)}},_changeHandler:function(t){t.data.self.set(t.data.prop,n(this).val())},_processField:function(n,t,i){var u={self:this,prop:t},r=this.get(t);if(i.fields.push(n[0]),this.formElements.indexOf(n[0].tagName.toLowerCase())===-1)return n.html(r),this._updates[t].read.push(n[0]);n.val(r).off("change",null,this._changeHandler).on("change",null,u,this._changeHandler);return this._updates[t].input.push(n[0])},formElements:["input","select","textarea"]};var ot=/[^\w]+/g,st=function(n){return n.replace(ot,"_")},ht=function(n){this.setDirty(!0,n.model)};if(t.Predicate=function(n,i,r,u,f){if(!(this instanceof t.Predicate))return new t.Predicate(n,i,r,u,f);if(this.ignoreAccent=!1,typeof n=="string"){var e="";i.toLowerCase().indexOf(" any")!=-1?(i=i.replace(" any",""),e="any"):i.toLowerCase().indexOf(" all")!=-1&&(i=i.replace(" all",""),e="all");this.field=n;this.operator=i;this.value=r;this.ignoreCase=u;this.ignoreAccent=f;this.isComplex=!1;this.anyCondition=e;this._comparer=t.data.fnOperators.processOperator(e!=""?e:this.operator)}else(n instanceof t.Predicate&&r instanceof t.Predicate||r instanceof Array)&&(this.isComplex=!0,this.condition=i.toLowerCase(),this.predicates=[n],r instanceof Array?[].push.apply(this.predicates,r):this.predicates.push(r));return this},t.Predicate.and=function(){return y._combinePredicates([].slice.call(arguments,0),"and")},t.Predicate.or=function(){return y._combinePredicates([].slice.call(arguments,0),"or")},t.Predicate.fromJSON=function(n){var i,t,r;if(p(n,Array)){for(i=[],t=0,r=n.length;t<r;t++)i.push(y._fromJSON(n[t]));return i}return y._fromJSON(n)},y={_combinePredicates:function(n,i){if(!n.length)return r;if(n.length===1){if(!p(n[0],Array))return n[0];n=n[0]}return new t.Predicate(n[0],i,n.slice(1))},_combine:function(n,i,r,u,e,o,s){return i instanceof t.Predicate?t.Predicate[e](n,i):typeof i=="string"?t.Predicate[e](n,new t.Predicate(i,r,u,o,s)):f("Predicate - "+e+" : invalid arguments")},_fromJSON:function(n){var i;if(!n||p(n,t.Predicate))return n;var u=n.predicates||[],f=u.length,r=[];for(i=0;i<f;i++)r.push(y._fromJSON(u[i]));return n.isComplex?new t.Predicate(r[0],n.condition,r.slice(1)):new t.Predicate(n.field,n.operator,t.parseJSON({val:n.value}).val,n.ignoreCase,n.ignoreAccent)}},t.Predicate.prototype={and:function(n,t,i,r,u){return y._combine(this,n,t,i,"and",r,u)},or:function(n,t,i,r,u){return y._combine(this,n,t,i,"or",r,u)},validate:function(n){var f=this.predicates,r,u,i;if(!this.isComplex)return this._comparer.call(this,t.pvt.getObject(this.field,n),this.value,this.ignoreCase,this.ignoreAccent);for(r=this.condition==="and",i=0;i<f.length;i++)if(u=f[i].validate(n),r){if(!u)return!1}else if(u)return!0;return r},toJSON:function(){var t,i,n;if(this.isComplex)for(t=[],i=this.predicates,n=0;n<i.length;n++)t.push(i[n].toJSON());return{isComplex:this.isComplex,field:this.field,operator:this.operator,value:this.value,ignoreCase:this.ignoreCase,ignoreAccent:this.ignoreAccent,condition:this.condition,predicates:t,anyCondition:this.anyCondition}}},t.dataUtil={swap:function(n,t,i){if(t!=i){var r=n[t];n[t]=n[i];n[i]=r}},mergeSort:function(n,i,r){return r&&typeof r!="string"||(r=t.pvt.fnSort(r,!0)),typeof i=="function"&&(r=i,i=null),t.pvt.mergeSort(n,i,r)},max:function(n,i,r){return typeof i=="function"&&(r=i,i=null),t.pvt.getItemFromComparer(n,i,r||t.pvt.fnDescending)},min:function(n,i,r){return typeof i=="function"&&(r=i,i=null),t.pvt.getItemFromComparer(n,i,r||t.pvt.fnAscending)},distinct:function(n,t,i){for(var f=[],r,e={},u=0;u<n.length;u++)r=v(n,t,u),r in e||(f.push(i?n[u]:r),e[r]=1);return f},sum:function(n,t){for(var u=0,i,f=typeof v(n,t,0)!="number",r=0;r<n.length;r++)i=v(n,t,r),isNaN(i)||i===null||(f&&(i=+i),u+=i);return u},avg:function(n,i){return t.sum(n,i)/n.length},select:function(n,i){for(var u=[],r=0;r<n.length;r++)u.push(t.pvt.extractFields(n[r],i));return u},group:function(i,r,u,f,e,o){var p,a,c,h,k,l,b,y,s,w;if(e=e||1,i.GROUPGUID==t.pvt.consts.GROUPGUID){for(s=0;s<i.length;s++)t.isNullOrUndefined(o)?(i[s].items=t.group(i[s].items,r,u,f,i.level+1),i[s].count=i[s].items.length):(p=-1,w=n.grep(o,function(n){return n.key==i[s].key}),p=o.indexOf(w[0]),i[s].items=t.group(i[s].items,r,u,f,i.level+1,o[p].items),i[s].count=o[p].count);return i.childLevels+=1,i}for(a={},c=[],c.GROUPGUID=t.pvt.consts.GROUPGUID,c.level=e,c.childLevels=0,c.records=i,l=0;l<i.length;l++)h=v(i,r,l),t.isNullOrUndefined(f)||(h=f(h,r)),a[h]||(a[h]={key:h,count:0,items:[],aggregates:{},field:r},c.push(a[h]),t.isNullOrUndefined(o)||(k=n.grep(o,function(n){return n.key==a[h].key}),a[h].count=k[0].count)),a[h].count=t.isNullOrUndefined(o)?a[h].count+=1:a[h].count,a[h].items.push(i[l]);if(u&&u.length)for(l=0;l<c.length;l++){for(b={},s=0;s<u.length;s++)y=t.aggregates[u[s].type],t.isNullOrUndefined(o)?y&&(b[u[s].field+" - "+u[s].type]=y(c[l].items,u[s].field)):(w=n.grep(o,function(n){return n.key==c[l].key}),y&&(b[u[s].field+" - "+u[s].type]=y(w[0].items,u[s].field)));c[l].aggregates=b}return c},parseTable:function(i,r,u){var s=i.rows,h,v=[],y=[],f,c,l,a,e,o;if(!s.length)return[];u=u||0;switch((r||"").toLowerCase()){case t.headerOption.tHead:h=i.tHead.rows[u];break;case t.headerOption.row:default:h=i.rows[u]}for(c=h.cells,f=0;f<c.length;f++)v.push(n.trim(c[f].innerHTML));for(f=u+1;f<s.length;f++){for(l={},a=s[f].cells,e=0;e<a.length;e++)o=a[e].innerHTML,l[v[e]]=typeof o=="string"&&n.isNumeric(o)?Number(o):o;y.push(l)}return y}},t.headerOption={tHead:"thead",row:"row"},t.aggregates={sum:function(n,i){return t.sum(n,i)},average:function(n,i){return t.avg(n,i)},minimum:function(n,i){return t.getObject(i,t.min(n,i))},maximum:function(n,i){return t.getObject(i,t.max(n,i))},truecount:function(n,i){var r=t.Predicate(i,"equal",!0);return t.DataManager(n).executeLocal(t.Query().where(r)).length},falsecount:function(n,i){var r=t.Predicate(i,"equal",!1);return t.DataManager(n).executeLocal(t.Query().where(r)).length},count:function(n){return n.length}},t.pvt={filterQueries:a,mergeSort:function(n,i,r){if(n.length<=1)return n;var e=parseInt(n.length/2,10),u=n.slice(0,e),f=n.slice(e);return u=t.pvt.mergeSort(u,i,r),f=t.pvt.mergeSort(f,i,r),t.pvt.merge(u,f,i,r)},getItemFromComparer:function(n,i,r){var f,e,o,u=0,s=typeof v(n,i,0)=="string";if(n.length)while(t.isNullOrUndefined(f)&&u<n.length)f=v(n,i,u),o=n[u++];for(;u<n.length;u++)(e=v(n,i,u),t.isNullOrUndefined(e))||(s&&(f=+f,e=+e),r(f,e)>0&&(f=e,o=n[u]));return o},quickSelect:function(n,i,r,u,f,e){if(r==u)return n[r];var o=t.pvt.partition(n,i,r,u,e),s=o-r+1;return s==f?n[o]:f<s?t.pvt.quickSelect(n,i,r,o-1,f,e):t.pvt.quickSelect(n,i,o+1,u,f-s,e)},extractFields:function(n,i){var u={},r;if(i.length==1)return t.pvt.getObject(i[0],n);for(r=0;r<i.length;r++)u[i[r].replace(".",t.pvt.consts.complexPropertyMerge)]=t.pvt.getObject(i[r],n);return u},partition:function(n,i,r,u,f){var e=parseInt((r+u)/2,10),s=v(n,i,e),o;for(t.swap(n,e,u),e=r,o=r;o<u;o++)f(v(n,i,o),s)&&(t.swap(n,o,e),e++);return t.swap(n,e,u),e},fnSort:function(n){return(n=n?n.toLowerCase():t.sortOrder.Ascending,n==t.sortOrder.Ascending)?t.pvt.fnAscending:t.pvt.fnDescending},fnGetComparer:function(n,i){return function(r,u){return i(t.pvt.getObject(n,r),t.pvt.getObject(n,u))}},fnAscending:function(n,i){return t.isNullOrUndefined(i)&&t.isNullOrUndefined(n)?-1:i===null||i===r?-1:typeof n=="string"?n.localeCompare(i):n===null||n===r?1:n-i},fnDescending:function(n,i){return t.isNullOrUndefined(i)&&t.isNullOrUndefined(n)?-1:i===null||i===r?1:typeof n=="string"?n.localeCompare(i)*-1:n===null||n===r?-1:i-n},merge:function(n,t,i,r){for(var u=[],f;n.length>0||t.length>0;)f=n.length>0&&t.length>0?r?r(v(n,i,0),v(t,i,0))<=0?n:t:n[0][i]<n[0][i]?n:t:n.length>0?n:t,u.push(f.shift());return u},getObject:function(n,t){var i,f,u;if(!t)return r;if(!n)return t;if(n.indexOf(".")===-1)return t[n];for(i=t,f=n.split("."),u=0;u<f.length;u++){if(i==null)break;i=i[f[u]]}return i},createObject:function(n,t,i){for(var f=n.split("."),o=i||window,e=o,u=0;u<f.length;u++)u+1==f.length?e[f[u]]=t===r?{}:t:e[f[u]]==null&&(e[f[u]]={}),e=e[f[u]];return o},ignoreDiacritics:function(n){if(typeof n!="string")return n;var i=n.split(""),r=i.map(function(n){return n in t.data.diacritics?t.data.diacritics[n]:n});return r.join("")},getFieldList:function(n,i,u){if(u===r&&(u=""),i===r||i===null)return t.pvt.getFieldList(n,[],u);for(var f in n)typeof n[f]!="object"||n[f]instanceof Array?i.push(u+f):t.pvt.getFieldList(n[f],i,u+f+".");return i}},t.FilterOperators={lessThan:"lessthan",greaterThan:"greaterthan",lessThanOrEqual:"lessthanorequal",greaterThanOrEqual:"greaterthanorequal",equal:"equal",contains:"contains",startsWith:"startswith",endsWith:"endswith",notEqual:"notequal"},t.data={},t.data.operatorSymbols={"<":"lessthan",">":"greaterthan","<=":"lessthanorequal",">=":"greaterthanorequal","==":"equal","!=":"notequal","*=":"contains","$=":"endswith","^=":"startswith"},t.data.odBiOperator={"<":" lt ",">":" gt ","<=":" le ",">=":" ge ","==":" eq ","!=":" ne ",lessthan:" lt ",lessthanorequal:" le ",greaterthan:" gt ",greaterthanorequal:" ge ",equal:" eq ",notequal:" ne ","in":" eq ",notin:" ne "},t.data.odUniOperator={"$=":"endswith","^=":"startswith","*=":"substringof",endswith:"endswith",startswith:"startswith",contains:"substringof",notcontains:"substringof"},t.data.diacritics={"Ⓐ":"A","Ａ":"A","À":"A","Á":"A","Â":"A","Ầ":"A","Ấ":"A","Ẫ":"A","Ẩ":"A","Ã":"A","Ā":"A","Ă":"A","Ằ":"A","Ắ":"A","Ẵ":"A","Ẳ":"A","Ȧ":"A","Ǡ":"A","Ä":"A","Ǟ":"A","Ả":"A","Å":"A","Ǻ":"A","Ǎ":"A","Ȁ":"A","Ȃ":"A","Ạ":"A","Ậ":"A","Ặ":"A","Ḁ":"A","Ą":"A","Ⱥ":"A","Ɐ":"A","Ꜳ":"AA","Æ":"AE","Ǽ":"AE","Ǣ":"AE","Ꜵ":"AO","Ꜷ":"AU","Ꜹ":"AV","Ꜻ":"AV","Ꜽ":"AY","Ⓑ":"B","Ｂ":"B","Ḃ":"B","Ḅ":"B","Ḇ":"B","Ƀ":"B","Ƃ":"B","Ɓ":"B","Ⓒ":"C","Ｃ":"C","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","Ç":"C","Ḉ":"C","Ƈ":"C","Ȼ":"C","Ꜿ":"C","Ⓓ":"D","Ｄ":"D","Ḋ":"D","Ď":"D","Ḍ":"D","Ḑ":"D","Ḓ":"D","Ḏ":"D","Đ":"D","Ƌ":"D","Ɗ":"D","Ɖ":"D","Ꝺ":"D","Ǳ":"DZ","Ǆ":"DZ","ǲ":"Dz","ǅ":"Dz","Ⓔ":"E","Ｅ":"E","È":"E","É":"E","Ê":"E","Ề":"E","Ế":"E","Ễ":"E","Ể":"E","Ẽ":"E","Ē":"E","Ḕ":"E","Ḗ":"E","Ĕ":"E","Ė":"E","Ë":"E","Ẻ":"E","Ě":"E","Ȅ":"E","Ȇ":"E","Ẹ":"E","Ệ":"E","Ȩ":"E","Ḝ":"E","Ę":"E","Ḙ":"E","Ḛ":"E","Ɛ":"E","Ǝ":"E","Ⓕ":"F","Ｆ":"F","Ḟ":"F","Ƒ":"F","Ꝼ":"F","Ⓖ":"G","Ｇ":"G","Ǵ":"G","Ĝ":"G","Ḡ":"G","Ğ":"G","Ġ":"G","Ǧ":"G","Ģ":"G","Ǥ":"G","Ɠ":"G","Ꞡ":"G","Ᵹ":"G","Ꝿ":"G","Ⓗ":"H","Ｈ":"H","Ĥ":"H","Ḣ":"H","Ḧ":"H","Ȟ":"H","Ḥ":"H","Ḩ":"H","Ḫ":"H","Ħ":"H","Ⱨ":"H","Ⱶ":"H","Ɥ":"H","Ⓘ":"I","Ｉ":"I","Ì":"I","Í":"I","Î":"I","Ĩ":"I","Ī":"I","Ĭ":"I","İ":"I","Ï":"I","Ḯ":"I","Ỉ":"I","Ǐ":"I","Ȉ":"I","Ȋ":"I","Ị":"I","Į":"I","Ḭ":"I","Ɨ":"I","Ⓙ":"J","Ｊ":"J","Ĵ":"J","Ɉ":"J","Ⓚ":"K","Ｋ":"K","Ḱ":"K","Ǩ":"K","Ḳ":"K","Ķ":"K","Ḵ":"K","Ƙ":"K","Ⱪ":"K","Ꝁ":"K","Ꝃ":"K","Ꝅ":"K","Ꞣ":"K","Ⓛ":"L","Ｌ":"L","Ŀ":"L","Ĺ":"L","Ľ":"L","Ḷ":"L","Ḹ":"L","Ļ":"L","Ḽ":"L","Ḻ":"L","Ł":"L","Ƚ":"L","Ɫ":"L","Ⱡ":"L","Ꝉ":"L","Ꝇ":"L","Ꞁ":"L","Ǉ":"LJ","ǈ":"Lj","Ⓜ":"M","Ｍ":"M","Ḿ":"M","Ṁ":"M","Ṃ":"M","Ɱ":"M","Ɯ":"M","Ⓝ":"N","Ｎ":"N","Ǹ":"N","Ń":"N","Ñ":"N","Ṅ":"N","Ň":"N","Ṇ":"N","Ņ":"N","Ṋ":"N","Ṉ":"N","Ƞ":"N","Ɲ":"N","Ꞑ":"N","Ꞥ":"N","Ǌ":"NJ","ǋ":"Nj","Ⓞ":"O","Ｏ":"O","Ò":"O","Ó":"O","Ô":"O","Ồ":"O","Ố":"O","Ỗ":"O","Ổ":"O","Õ":"O","Ṍ":"O","Ȭ":"O","Ṏ":"O","Ō":"O","Ṑ":"O","Ṓ":"O","Ŏ":"O","Ȯ":"O","Ȱ":"O","Ö":"O","Ȫ":"O","Ỏ":"O","Ő":"O","Ǒ":"O","Ȍ":"O","Ȏ":"O","Ơ":"O","Ờ":"O","Ớ":"O","Ỡ":"O","Ở":"O","Ợ":"O","Ọ":"O","Ộ":"O","Ǫ":"O","Ǭ":"O","Ø":"O","Ǿ":"O","Ɔ":"O","Ɵ":"O","Ꝋ":"O","Ꝍ":"O","Ƣ":"OI","Ꝏ":"OO","Ȣ":"OU","Ⓟ":"P","Ｐ":"P","Ṕ":"P","Ṗ":"P","Ƥ":"P","Ᵽ":"P","Ꝑ":"P","Ꝓ":"P","Ꝕ":"P","Ⓠ":"Q","Ｑ":"Q","Ꝗ":"Q","Ꝙ":"Q","Ɋ":"Q","Ⓡ":"R","Ｒ":"R","Ŕ":"R","Ṙ":"R","Ř":"R","Ȑ":"R","Ȓ":"R","Ṛ":"R","Ṝ":"R","Ŗ":"R","Ṟ":"R","Ɍ":"R","Ɽ":"R","Ꝛ":"R","Ꞧ":"R","Ꞃ":"R","Ⓢ":"S","Ｓ":"S","ẞ":"S","Ś":"S","Ṥ":"S","Ŝ":"S","Ṡ":"S","Š":"S","Ṧ":"S","Ṣ":"S","Ṩ":"S","Ș":"S","Ş":"S","Ȿ":"S","Ꞩ":"S","Ꞅ":"S","Ⓣ":"T","Ｔ":"T","Ṫ":"T","Ť":"T","Ṭ":"T","Ț":"T","Ţ":"T","Ṱ":"T","Ṯ":"T","Ŧ":"T","Ƭ":"T","Ʈ":"T","Ⱦ":"T","Ꞇ":"T","Ꜩ":"TZ","Ⓤ":"U","Ｕ":"U","Ù":"U","Ú":"U","Û":"U","Ũ":"U","Ṹ":"U","Ū":"U","Ṻ":"U","Ŭ":"U","Ü":"U","Ǜ":"U","Ǘ":"U","Ǖ":"U","Ǚ":"U","Ủ":"U","Ů":"U","Ű":"U","Ǔ":"U","Ȕ":"U","Ȗ":"U","Ư":"U","Ừ":"U","Ứ":"U","Ữ":"U","Ử":"U","Ự":"U","Ụ":"U","Ṳ":"U","Ų":"U","Ṷ":"U","Ṵ":"U","Ʉ":"U","Ⓥ":"V","Ｖ":"V","Ṽ":"V","Ṿ":"V","Ʋ":"V","Ꝟ":"V","Ʌ":"V","Ꝡ":"VY","Ⓦ":"W","Ｗ":"W","Ẁ":"W","Ẃ":"W","Ŵ":"W","Ẇ":"W","Ẅ":"W","Ẉ":"W","Ⱳ":"W","Ⓧ":"X","Ｘ":"X","Ẋ":"X","Ẍ":"X","Ⓨ":"Y","Ｙ":"Y","Ỳ":"Y","Ý":"Y","Ŷ":"Y","Ỹ":"Y","Ȳ":"Y","Ẏ":"Y","Ÿ":"Y","Ỷ":"Y","Ỵ":"Y","Ƴ":"Y","Ɏ":"Y","Ỿ":"Y","Ⓩ":"Z","Ｚ":"Z","Ź":"Z","Ẑ":"Z","Ż":"Z","Ž":"Z","Ẓ":"Z","Ẕ":"Z","Ƶ":"Z","Ȥ":"Z","Ɀ":"Z","Ⱬ":"Z","Ꝣ":"Z","ⓐ":"a","ａ":"a","ẚ":"a","à":"a","á":"a","â":"a","ầ":"a","ấ":"a","ẫ":"a","ẩ":"a","ã":"a","ā":"a","ă":"a","ằ":"a","ắ":"a","ẵ":"a","ẳ":"a","ȧ":"a","ǡ":"a","ä":"a","ǟ":"a","ả":"a","å":"a","ǻ":"a","ǎ":"a","ȁ":"a","ȃ":"a","ạ":"a","ậ":"a","ặ":"a","ḁ":"a","ą":"a","ⱥ":"a","ɐ":"a","ꜳ":"aa","æ":"ae","ǽ":"ae","ǣ":"ae","ꜵ":"ao","ꜷ":"au","ꜹ":"av","ꜻ":"av","ꜽ":"ay","ⓑ":"b","ｂ":"b","ḃ":"b","ḅ":"b","ḇ":"b","ƀ":"b","ƃ":"b","ɓ":"b","ⓒ":"c","ｃ":"c","ć":"c","ĉ":"c","ċ":"c","č":"c","ç":"c","ḉ":"c","ƈ":"c","ȼ":"c","ꜿ":"c","ↄ":"c","ⓓ":"d","ｄ":"d","ḋ":"d","ď":"d","ḍ":"d","ḑ":"d","ḓ":"d","ḏ":"d","đ":"d","ƌ":"d","ɖ":"d","ɗ":"d","ꝺ":"d","ǳ":"dz","ǆ":"dz","ⓔ":"e","ｅ":"e","è":"e","é":"e","ê":"e","ề":"e","ế":"e","ễ":"e","ể":"e","ẽ":"e","ē":"e","ḕ":"e","ḗ":"e","ĕ":"e","ė":"e","ë":"e","ẻ":"e","ě":"e","ȅ":"e","ȇ":"e","ẹ":"e","ệ":"e","ȩ":"e","ḝ":"e","ę":"e","ḙ":"e","ḛ":"e","ɇ":"e","ɛ":"e","ǝ":"e","ⓕ":"f","ｆ":"f","ḟ":"f","ƒ":"f","ꝼ":"f","ⓖ":"g","ｇ":"g","ǵ":"g","ĝ":"g","ḡ":"g","ğ":"g","ġ":"g","ǧ":"g","ģ":"g","ǥ":"g","ɠ":"g","ꞡ":"g","ᵹ":"g","ꝿ":"g","ⓗ":"h","ｈ":"h","ĥ":"h","ḣ":"h","ḧ":"h","ȟ":"h","ḥ":"h","ḩ":"h","ḫ":"h","ẖ":"h","ħ":"h","ⱨ":"h","ⱶ":"h","ɥ":"h","ƕ":"hv","ⓘ":"i","ｉ":"i","ì":"i","í":"i","î":"i","ĩ":"i","ī":"i","ĭ":"i","ï":"i","ḯ":"i","ỉ":"i","ǐ":"i","ȉ":"i","ȋ":"i","ị":"i","į":"i","ḭ":"i","ɨ":"i","ı":"i","ⓙ":"j","ｊ":"j","ĵ":"j","ǰ":"j","ɉ":"j","ⓚ":"k","ｋ":"k","ḱ":"k","ǩ":"k","ḳ":"k","ķ":"k","ḵ":"k","ƙ":"k","ⱪ":"k","ꝁ":"k","ꝃ":"k","ꝅ":"k","ꞣ":"k","ⓛ":"l","ｌ":"l","ŀ":"l","ĺ":"l","ľ":"l","ḷ":"l","ḹ":"l","ļ":"l","ḽ":"l","ḻ":"l","ſ":"l","ł":"l","ƚ":"l","ɫ":"l","ⱡ":"l","ꝉ":"l","ꞁ":"l","ꝇ":"l","ǉ":"lj","ⓜ":"m","ｍ":"m","ḿ":"m","ṁ":"m","ṃ":"m","ɱ":"m","ɯ":"m","ⓝ":"n","ｎ":"n","ǹ":"n","ń":"n","ñ":"n","ṅ":"n","ň":"n","ṇ":"n","ņ":"n","ṋ":"n","ṉ":"n","ƞ":"n","ɲ":"n","ŉ":"n","ꞑ":"n","ꞥ":"n","ǌ":"nj","ⓞ":"o","ｏ":"o","ò":"o","ó":"o","ô":"o","ồ":"o","ố":"o","ỗ":"o","ổ":"o","õ":"o","ṍ":"o","ȭ":"o","ṏ":"o","ō":"o","ṑ":"o","ṓ":"o","ŏ":"o","ȯ":"o","ȱ":"o","ö":"o","ȫ":"o","ỏ":"o","ő":"o","ǒ":"o","ȍ":"o","ȏ":"o","ơ":"o","ờ":"o","ớ":"o","ỡ":"o","ở":"o","ợ":"o","ọ":"o","ộ":"o","ǫ":"o","ǭ":"o","ø":"o","ǿ":"o","ɔ":"o","ꝋ":"o","ꝍ":"o","ɵ":"o","ƣ":"oi","ȣ":"ou","ꝏ":"oo","ⓟ":"p","ｐ":"p","ṕ":"p","ṗ":"p","ƥ":"p","ᵽ":"p","ꝑ":"p","ꝓ":"p","ꝕ":"p","ⓠ":"q","ｑ":"q","ɋ":"q","ꝗ":"q","ꝙ":"q","ⓡ":"r","ｒ":"r","ŕ":"r","ṙ":"r","ř":"r","ȑ":"r","ȓ":"r","ṛ":"r","ṝ":"r","ŗ":"r","ṟ":"r","ɍ":"r","ɽ":"r","ꝛ":"r","ꞧ":"r","ꞃ":"r","ⓢ":"s","ｓ":"s","ß":"s","ś":"s","ṥ":"s","ŝ":"s","ṡ":"s","š":"s","ṧ":"s","ṣ":"s","ṩ":"s","ș":"s","ş":"s","ȿ":"s","ꞩ":"s","ꞅ":"s","ẛ":"s","ⓣ":"t","ｔ":"t","ṫ":"t","ẗ":"t","ť":"t","ṭ":"t","ț":"t","ţ":"t","ṱ":"t","ṯ":"t","ŧ":"t","ƭ":"t","ʈ":"t","ⱦ":"t","ꞇ":"t","ꜩ":"tz","ⓤ":"u","ｕ":"u","ù":"u","ú":"u","û":"u","ũ":"u","ṹ":"u","ū":"u","ṻ":"u","ŭ":"u","ü":"u","ǜ":"u","ǘ":"u","ǖ":"u","ǚ":"u","ủ":"u","ů":"u","ű":"u","ǔ":"u","ȕ":"u","ȗ":"u","ư":"u","ừ":"u","ứ":"u","ữ":"u","ử":"u","ự":"u","ụ":"u","ṳ":"u","ų":"u","ṷ":"u","ṵ":"u","ʉ":"u","ⓥ":"v","ｖ":"v","ṽ":"v","ṿ":"v","ʋ":"v","ꝟ":"v","ʌ":"v","ꝡ":"vy","ⓦ":"w","ｗ":"w","ẁ":"w","ẃ":"w","ŵ":"w","ẇ":"w","ẅ":"w","ẘ":"w","ẉ":"w","ⱳ":"w","ⓧ":"x","ｘ":"x","ẋ":"x","ẍ":"x","ⓨ":"y","ｙ":"y","ỳ":"y","ý":"y","ŷ":"y","ỹ":"y","ȳ":"y","ẏ":"y","ÿ":"y","ỷ":"y","ẙ":"y","ỵ":"y","ƴ":"y","ɏ":"y","ỿ":"y","ⓩ":"z","ｚ":"z","ź":"z","ẑ":"z","ż":"z","ž":"z","ẓ":"z","ẕ":"z","ƶ":"z","ȥ":"z","ɀ":"z","ⱬ":"z","ꝣ":"z","Ά":"Α","Έ":"Ε","Ή":"Η","Ί":"Ι","Ϊ":"Ι","Ό":"Ο","Ύ":"Υ","Ϋ":"Υ","Ώ":"Ω","ά":"α","έ":"ε","ή":"η","ί":"ι","ϊ":"ι","ΐ":"ι","ό":"ο","ύ":"υ","ϋ":"υ","ΰ":"υ","ω":"ω","ς":"σ"},t.data.fnOperators={equal:function(n,i,r,u){return(u&&(n=t.pvt.ignoreDiacritics(n),i=t.pvt.ignoreDiacritics(i)),r)?s(n)==s(i):n==i},notequal:function(n,i,r,u){return u&&(n=t.pvt.ignoreDiacritics(n),i=t.pvt.ignoreDiacritics(i)),!t.data.fnOperators.equal(n,i,r)},notin:function(n,i,r){for(var u=0;u<i.length;u++)if(t.data.fnOperators.notequal(n,i[u],r)==!1)return!1;return!0},lessthan:function(n,t,i){return i?s(n)<s(t):n<t},greaterthan:function(n,t,i){return i?s(n)>s(t):n>t},lessthanorequal:function(n,t,i){return i?s(n)<=s(t):n<=t},greaterthanorequal:function(n,t,i){return i?s(n)>=s(t):n>=t},contains:function(n,i,r,u){return(u&&(n=t.pvt.ignoreDiacritics(n),i=t.pvt.ignoreDiacritics(i)),r)?!c(n)&&!c(i)&&s(n).indexOf(s(i))!=-1:!c(n)&&!c(i)&&n.toString().indexOf(i)!=-1},notcontains:function(n,i,r,u){return u&&(n=t.pvt.ignoreDiacritics(n),i=t.pvt.ignoreDiacritics(i)),!t.data.fnOperators.contains(n,i,r)},notnull:function(n){return n!==null},isnull:function(n){return n===null},startswith:function(n,i,r,u){return(u&&(n=t.pvt.ignoreDiacritics(n),i=t.pvt.ignoreDiacritics(i)),r)?n&&i&&s(n).startsWith(s(i)):n&&i&&n.startsWith(i)},endswith:function(n,i,r,u){return(u&&(n=t.pvt.ignoreDiacritics(n),i=t.pvt.ignoreDiacritics(i)),r)?n&&i&&s(n).endsWith(s(i)):n&&i&&n.endsWith(i)},all:function(n,i,r){for(var u=0;u<i.length;u++)if(t.data.fnOperators[this.operator](n,i[u],r)==!1)return!1;return!0},any:function(n,i,r){for(var u=0;u<i.length;u++)if(t.data.fnOperators[this.operator](n,i[u],r)==!0)return!0;return!1},processSymbols:function(n){var r=t.data.operatorSymbols[n],i;return r&&(i=t.data.fnOperators[r],i)?i:f("Query - Process Operator : Invalid operator")},processOperator:function(n){var i=t.data.fnOperators[n];return i?i:t.data.fnOperators.processSymbols(n)}},t.data.fnOperators["in"]=function(n,i,r){for(var u=0;u<i.length;u++)if(t.data.fnOperators.equal(n,i[u],r)==!0)return!0;return!1},t.NotifierArray=function(i){return p(this,t.NotifierArray)?(this.array=i,this._events=n({}),this._isDirty=!1,this):new t.NotifierArray(i)},t.NotifierArray.prototype={on:function(n,t){this._events.on(n,t)},off:function(n,t){this._events.off(n,t)},push:function(n){var t;return t=p(n,Array)?[].push.apply(this.array,n):this.array.push(n),this._raise("add",{item:n,index:this.length()-1}),t},pop:function(){var n=this.array.pop();return this._raise("remove",{item:n,index:this.length()-1}),n},addAt:function(n,t){return this.array.splice(n,0,t),this._raise("add",{item:t,index:n}),t},removeAt:function(n){var t=this.array.splice(n,1)[0];return this._raise("remove",{item:t,index:n}),t},remove:function(n){var t=this.array.indexOf(n);return t>-1&&(this.array.splice(t,1),this._raise("remove",{item:n,index:t})),t},length:function(){return this.array.length},_raise:function(t,i){this._events.triggerHandler(n.extend({type:t},i));this._events.triggerHandler({type:"all",name:t,args:i})},toArray:function(){return this.array}},n.extend(t,t.dataUtil),Array.prototype.forEach=Array.prototype.forEach||function(n,t){for(var i=0,r=this.length;i<r;++i)n.call(t,this[i],i,this)},Array.prototype.indexOf=Array.prototype.indexOf||function(n){var i=this.length,t;if(i===0)return-1;for(t=0;t<i;t++)if(t in this&&this[t]===n)return t;return-1},Array.prototype.filter=Array.prototype.filter||function(n){var i,u,t,r;if(typeof n!="function")throw new TypeError;for(i=[],u=arguments[1]||this,t=0;t<this.length;t++)r=this[t],n.call(u,r,t,this)&&i.push(r);return i},String.prototype.endsWith=String.prototype.endsWith||function(n){return this.slice(-n.length)===n},String.prototype.startsWith=String.prototype.startsWith||function(n){return this.slice(0,n.length)===n},t.support||(t.support={}),t.support.stableSort=function(){for(var t=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16].sort(function(){return 0}),n=0;n<17;n++)if(n!==t[n])return!1;return!0}(),t.support.cors=n.support.cors,!n.support.cors&&window.XDomainRequest){var ct=/^https?:\/\//i,lt=/^get|post$/i,at=new RegExp("^"+location.protocol,"i"),vt=/\/xml/i;n.ajaxTransport("text html xml json",function(n,t){if(n.crossDomain&&n.async&&lt.test(n.type)&&ct.test(t.url)&&at.test(t.url)){var i=null,u=(t.dataType||"").toLowerCase();return{send:function(f,e){i=new XDomainRequest;/^\d+$/.test(t.timeout)&&(i.timeout=t.timeout);i.ontimeout=function(){e(500,"timeout")};i.onload=function(){var o="Content-Length: "+i.responseText.length+"\r\nContent-Type: "+i.contentType,t={code:200,message:"success"},f={text:i.responseText},n;try{if(u==="json")try{f.json=JSON.parse(i.responseText)}catch(h){t.code=500;t.message="parseerror"}else if(u==="xml"||u!=="text"&&vt.test(i.contentType)){n=new ActiveXObject("Microsoft.XMLDOM");n.async=!1;try{n.loadXML(i.responseText)}catch(h){n=r}if(!n||!n.documentElement||n.getElementsByTagName("parsererror").length){t.code=500;t.message="parseerror";throw"Invalid XML: "+i.responseText;}f.xml=n}}catch(s){throw s;}finally{e(t.code,t.message,f,o)}};i.onerror=function(){e(500,"error",{text:i.responseText})};navigator.userAgent.indexOf("MSIE 9.0")!=-1&&(i.onprogress=function(){});i.open(n.type,n.url);i.send(t.data)},abort:function(){i&&i.abort()}}}})}n.support.cors=!0;t.sortOrder={Ascending:"ascending",Descending:"descending"};t.pvt.consts={GROUPGUID:"{271bbba0-1ee7}",complexPropertyMerge:"_"};d=function(n,t){t&&(n=h(n,t));(window.setImmediate||window.setTimeout)(n,0)};t.support.enableLocalizedSort=!1;var it=function(n,i,r,u){return t.support.stableSort?!t.support.enableLocalizedSort&&typeof t.pvt.getObject(i,n[0]||{})=="string"&&(r===t.pvt.fnAscending||r===t.pvt.fnDescending)&&u.filter(function(n){return n.fn==="onSortBy"}).length===1?yt(n,i,r===t.pvt.fnDescending):n.sort(t.pvt.fnGetComparer(i,r)):t.mergeSort(n,i,r)},b=function(i,r){for(var f=n.grep(r,function(n){return n.fn=="onGroup"}),u=0;u<f.length;u++)if(t.getObject("fieldName",f[u].e)==i)return t.getObject("fn",f[u].e)},yt=function(n,i,r){var u=Object.prototype.toString;Object.prototype.toString=i.indexOf(".")===-1?function(){return this[i]}:function(){return t.pvt.getObject(i,this)};n=n.sort();Object.prototype.toString=u;r&&n.reverse()},s=function(n){return n?n.toLowerCase?n.toLowerCase():n.toString().toLowerCase():n===0||n===!1?n.toString():""},v=function(n,i,r){return i?t.pvt.getObject(i,n[r]):n[r]},pt=function(n){return typeof HTMLElement=="object"?n instanceof HTMLElement:n&&n.nodeType===1&&typeof n=="object"&&typeof n.nodeName=="string"},p=function(n,t){return n instanceof t},rt=function(n,i,r,u){return function(f){return typeof f=="object"&&(u=f,f=null),new t.TableModel(f||n,i,r,u)}},ut=function(t){return function(i,r){var o,h,e,u,s;for(r=r||window.ko,r||f("Knockout is undefined"),h=[],s=0;s<t.length;s++){o={};for(e in t[s])e.startsWith("_")||(o[e]=r.observable(t[s][e]));for(e in i)u=i[e],n.isPlainObject(u)?(u.owner||(u.owner=o),u=r.computed(u)):u=r.computed(u,o),o[e]=u;h.push(o)}return r.observableArray(h)}},ft=0,et=function(n){return ft+=1,n+ft};t.getGuid=function(n){var i="0123456789abcdef",t;return(n||"")+"00000000-0000-4000-0000-000000000000".replace(/0/g,function(n,r){if("crypto"in window&&"getRandomValues"in crypto){var u=new Uint8Array(1);window.crypto.getRandomValues(u);t=u[0]%16|0}else t=Math.random()*16|0;return i[r===19?t&3|8:t]})};w=function(n,t){return function(){var i=[].slice.call(arguments,0);return i.push(this),n.apply(t||this,i)}};h=function(n,t,i){return"bind"in n?i?n.bind(t,i):n.bind(t):function(){var r=i?[i]:[];return r.push.apply(r,arguments),n.apply(t||this,r)}};t.merge=function(n,t){n&&t&&Array.prototype.push.apply(n,t)};c=function(n){return n===r||n===null};f=function(n){try{throw new Error(n);}catch(t){throw t.message+"\n"+t.stack;}}}(window.jQuery,window.Syncfusion,window.document)});
