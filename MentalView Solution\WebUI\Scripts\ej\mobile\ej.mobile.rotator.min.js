/*!
*  filename: ej.mobile.rotator.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function($,ej,undefined){ej.widget("ejmRotator","ej.mobile.Rotator",{_requiresID:!0,_setFirst:!0,_rootCSS:"e-m-rotator",_tags:[{tag:"items",attr:["imageUrl","text"]}],defaults:{targetId:null,renderMode:"auto",targetHeight:"auto",targetWidth:"auto",currentItemIndex:0,showPager:!0,cssClass:"",orientation:"horizontal",enablePersistence:!1,pagerPosition:"bottom",items:[],swipeLeft:null,swipeRight:null,swipeUp:null,swipeDown:null,change:null,pagerSelect:null,dataSource:null},dataTypes:{renderMode:"enum",showPager:"boolean",dataSource:"data",enablePersistence:"boolean"},observables:["currentItemIndex"],currentItemIndex:ej.util.valueFunction("currentItemIndex"),_init:function(){this._renderControl();this._createDelegate();this._wireEvents()},_renderControl:function(){this._setValues();this.element.addClass("e-m-"+this.model.renderMode+" "+this.model.cssClass+" e-m-rotator e-m-user-select").append(this._wrapper);this.model.dataSource?this._renderDatabind():this._renderDefault()},_setValues:function(){var itemsLen,item;if(ej.setRenderMode(this),eval(this.model.items).length!=0)for(this.model.items=eval(this.model.items),itemsLen=this.model.items.length,this._wrapper=ej.buildTag("div#"+this._id+"_container"),i=0;i<itemsLen;i++)this._wrapper.append(ej.buildTag("div"));else{this._wrapper=this.model.targetId?ej.getCurrentPage().find("#"+this.model.targetId):$(this.element.children()[0]);var childEle=this._wrapper.children(),tempImgAttr=ej.getAttrVal($(childEle[0]),"data-ej-imageUrl"),tempTextAttr=ej.getAttrVal($(childEle[0]),"data-ej-text");if(tempImgAttr||tempTextAttr)for(i=0;i<childEle.length;i++)item={},item.imageUrl=ej.getAttrVal($(childEle[i]),"data-ej-imageUrl"),tempTextAttr&&(item.text=ej.getAttrVal($(childEle[i]),"data-ej-text")),this.model.items.push(item)}this._divCount=this._wrapper.children().length;this._orgEle=this._wrapper;this._autoHeightWidthValues();this._browser=ej.browser().toLowerCase();this._startValues()},_autoHeightWidthValues:function(){this._mHeight=this.model.targetHeight=="auto"?ej.getDimension(this.element.parent(),"height"):parseInt(this.model.targetHeight);this._mWidth=this.model.targetWidth=="auto"?ej.getDimension(this.element.parent(),"width"):parseInt(this.model.targetWidth)},_startValues:function(){this._moveEle=this._divCount>this.currentItemIndex()&&this.currentItemIndex()>=0?-(this.currentItemIndex()*(this.model.orientation=="horizontal"?this._mWidth:this._mHeight)):0;this._divEle=this._moveEle?this.currentItemIndex()+1:1;this._timeStart=0},_transform:function(n,t){var i="-"+this._browser+"-transform",r=this.model.orientation=="horizontal"?"translate3d("+n+"px,0px,0px) scale(1)":"translate3d(0px,"+n+"px,0px) scale(1)",u="-"+this._browser+"-transition-property",f="-"+this._browser+"-transition-duration",e=t+"ms";this._wrapper.css(i,r).css(u,"transform").css(f,e)},_renderDatabind:function(){var n=ej.buildTag("script#"+this._id+"_Template",{},{},{type:"text/x-jsrender"}),t=this._wrapper.find("div"),i;t.remove();i=ej.buildTag("div").append(t);n[0].text=i[0].innerHTML;ej.getCurrentPage().append(n);this.model.dataSource&&this.renderDatasource(this.model.dataSource)},_renderDefault:function(){this.model.items.length!=0&&(this._imgTag=this.model.items[0].imageUrl,this._textTag=this.model.items[0].text);(this._imgTag!=undefined||this._textTag!=undefined)&&this._tagCreate();this._scrollItem();this._transform(this._moveEle,350);this._renderPager()},_scrollItem:function(){this.element.css({width:""+this._mWidth+"px",height:""+this._mHeight+"px"});this._wrapper.addClass("e-m-sv-container e-m-"+this.model.orientation+"").css({height:""+this._mHeight+"",width:""+this._mWidth+""});this._wrapper.children().addClass("e-m-item")},_tagCreate:function(){var n=this._wrapper.find("div"),t,r;for(i=0;i<this._divCount;i++)this._textTag!=undefined&&(r=ej.buildTag("p").html(this.model.items[i].text).addClass("e-m-rotator-p"),$(n[i]).append(r)),this._imgTag!=undefined&&(t=ej.buildTag("div").css({"background-image":"url("+this.model.items[i].imageUrl+")"}).addClass("e-m-rotator-image"),$(n[i]).append(t))},_renderPager:function(){for(this._span=ej.buildTag("div.e-m-abs  e-m-pager e-m-"+this.model.renderMode+""),this._wrapper.after(this._span),i=0;i<this._divCount;i++)this._span.append(ej.buildTag("span.e-m-sv-pager"));this._span.find(":nth-child("+this._divEle+")").addClass("e-m-active");this._pagerPos();this._setShowPager()},_pagerPos:function(){this.model.orientation=="horizontal"?this.model.pagerPosition=this.model.pagerPosition=="top"?this._span.addClass("e-m-sv-pagertop"):this._span.addClass("e-m-sv-pagerbottom"):this.model.pagerPosition=="right"?this._span.addClass("e-m-sv-pagerright"):this._span.addClass("e-m-sv-pagerleft");this.model.orientation=="horizontal"?this._span.addClass("e-m-rotator-spanleft"):this._span.css("bottom",+(this._mHeight-$(".e-m-pager").height())/2+"px")},_createDelegate:function(){this._onMouseDownDelg=$.proxy(this._onMouseDownHandler,this);this._onMouseMoveDelg=$.proxy(this._onMouseMoveHandler,this);this._onMouseUpDelg=$.proxy(this._onMouseUpHandler,this);this._onTouchEndDelegate=$.proxy(this._onTouchEndHandler,this)},_wireEvents:function(n){ej.listenTouchEvent(this._wrapper,ej.startEvent(),this._onMouseDownDelg,n);ej.listenEvents([window],["onorientationchange"in window?"orientationchange":"resize"],[$.proxy(this._ResizeHandler,this)],n);this._pagerEvents(n)},_ResizeHandler:function(n){if(this.model.targetHeight=="auto"||this.model.targetWidth=="auto"){var t=this;window.setTimeout(function(){n.preventDefault();t._orientationAndResize()},ej.isAndroid()?200:0)}},_pagerEvents:function(n){this._spanEle=this.element.find("div.e-m-abs").children();ej.listenEvents([this._spanEle,this._spanEle],[ej.startEvent(),ej.endEvent()],[this._onTouchStartDelegate,this._onTouchEndDelegate],n)},_onTouchEndHandler:function(n){this._index=$(n.currentTarget).index();this.currentItemIndex(this._index);this._moveEle=-(this._index*(this.model.orientation=="horizontal"?this._mWidth:this._mHeight));this._transform(this._moveEle,350);this._span.find(":nth-child("+this._divEle+")").removeClass("e-m-active");this._divEle=this._index+1;this._span.find(":nth-child("+this._divEle+")").addClass("e-m-active");var t={targetElement:this._wrapper,element:this._divEle};this.model.pagerSelect&&this._trigger("pagerSelect",t)},_orientationAndResize:function(){this._autoHeightWidthValues();var n=this.model.orientation=="horizontal"?this._mWidth:this._mHeight;this._moveEle=-(this._divEle*n-n);this._transform(this._moveEle,0);this._scrollItem();this._spanTrans=(this._divEle-1)*this._andSpanVal;this._pagerPos()},_onMouseDownHandler:function(n){ej.isTouchDevice()&&(n=n.touches?n.touches[0]:n);this._startX=n.clientX;this._startY=n.clientY;this._timeStart=n.timeStamp||Date.now();ej.listenEvents([this._wrapper,this._wrapper],[ej.moveEvent(),ej.endEvent()],[this._onMouseMoveDelg,this._onMouseUpDelg],!1)},_onMouseMoveHandler:function(n){if(ej.isTouchDevice()&&(n=n.touches?n.touches[0]:n),this._relativeX=n.clientX-this._startX,this._relativeY=n.clientY-this._startY,this.model.orientation=="horizontal"?(this._start=this._startX,this._relative=this._relativeX,this._distValue=this._mWidth,this._swipeLeftUp="swipeLeft",this._swipeRightDown="swipeRight"):(this._start=this._startY,this._relative=this._relativeY,this._distValue=this._mHeight,this._swipeLeftUp="swipeUp",this._swipeRightDown="swipeDown"),this._start!=0&&Math.abs(this._relative)){this._relative<0&&this._divEle<this._divCount||this._relative>0&&this._divEle>1||Math.abs(this._relative)<this._distValue/2;var t=this._moveEle+this._relative;this._transform(t,0)}ej.listenTouchEvent(this._wrapper,"mouseleave",this._onMouseUpDelg,!1)},_onMouseUpHandler:function(n){if(this._timeStart!=0){this._data={targetElement:this._wrapper,element:this._divEle};var r=(n.timeStamp||Date.now())-this._timeStart,i={dist:0,time:0},u=this._relative>0?!0:!1,t=this._relative;r<300&&(i=this._relative?this._momentum(this._relative,r,u?this._relative:-this._relative,this._distValue+this._relative,this._distValue):i,t=this._relative+i.dist);t<0&&Math.abs(t)>this._distValue/2&&this._divEle<this._divCount?(this._moveEle+=-this._distValue,this._movePager(!0),(this.model.swipeLeft||this.model.swipeUp)&&this._trigger(this._swipeLeftUp,this._data)):t>0&&Math.abs(t)>this._distValue/2&&this._divEle>1&&(this._moveEle-=-this._distValue,this._movePager(!1),(this.model.swipeRight||this.model.swipeDown)&&this._trigger(this._swipeRightDown,this._data));this._transform(this._moveEle,350);this._timeStart=0;this._startX=0;this._relativeX=0;this._relative=0;this._start=0;ej.listenEvents([this._wrapper,this._wrapper],[ej.moveEvent(),ej.endEvent()],[this._onMouseMoveDelg,this._onMouseUpDelg],!0)}this.currentItemIndex(this._divEle-1)},_momentum:function(n,t,i,r,u){var o=.0006,e=Math.abs(n)/t,f=e*e/(2*o),h=0,s=0;return n>0&&f>i?(s=u/(6/(f/e*o)),i=i+s,e=e*i/f,f=i):n<0&&f>r&&(s=u/(6/(f/e*o)),r=r+s,e=e*r/f,f=r),f=f*(n<0?-1:1),h=e/o,{dist:f,time:Math.round(h)}},_movePager:function(n){this._span.find(":nth-child("+this._divEle+")").removeClass("e-m-active");this._divEle=n?this._divEle+1:this._divEle-1;this._span.find(":nth-child("+this._divEle+")").addClass("e-m-active");this._spanTrans=(this._divEle-1)*this._andSpanVal;this._data={targetElement:this._wrapper,element:this._divEle};this.model.change&&this._trigger("change",this._data)},_setModel:function(n){var r=!1,t,i;for(t in n)(t!="swipeLeft"||t!="swipeRight")&&(i="_set"+t.charAt(0).toUpperCase()+t.slice(1)),this[i]?this[i]():r=!0;r&&this._refresh()},_setRenderMode:function(){this.element.removeClass("e-m-ios7 e-m-android e-m-windows e-m-flat").addClass("e-m-"+this.model.renderMode+"");this._span.remove();this._renderPager();this._pagerEvents(!1)},_setShowPager:function(){this.model.showPager?this._span.css("display","block"):this._span.css("display","none")},_setOrientation:function(){this.model.orientation=="horizontal"?this._span.addClass("e-m-sv-pagerbottom"):this._span.addClass("e-m-sv-pagerright");this.currentItemIndex(this._divEle-1);this._refresh()},_setCurrentItemIndex:function(){var n=this._span.find(":nth-child("+this._divEle+")");n.removeClass("e-m-active");this._startValues();this._transform(this._moveEle,1e3);this._spanTrans=(this._divEle-1)*this._andSpanVal;n.addClass("e-m-active")},_refresh:function(){this._destroy();this._init()},_destroy:function(){this._wireEvents(!0);this._wrapper.children().find(".e-m-rotator-p,.e-m-rotator-image").remove().removeClass("e-m-item");this.element.removeAttr("class style").children().removeAttr("class style").remove();this._span.remove();ej.getCurrentPage().append(this._orgEle)},renderDatasource:function(n){var t=$.templates("#"+this._id+"_Template"),i=t.render(n);this._wrapper.html(i);this._divCount=this._wrapper.children().length;this._scrollItem();this._transform(this._moveEle,350);this._renderPager();this._wireEvents()}})})(jQuery,Syncfusion)});
