/*!
*  filename: ej.mobile.button.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function(n,t){t.widget({ejmButton:"ej.mobile.Button",ejmActionlink:"ej.mobile.Actionlink"},{_setFirst:!0,_rootCSS:"e-m-btn",defaults:{touchStart:null,touchEnd:null,enabled:!0,cssClass:"",renderMode:"auto",enableRippleEffect:t.isAndroid()?!0:!1,text:null,href:"",imageClass:null,imagePosition:"left",contentType:"text",enablePersistence:!1,showBorder:!0,style:"normal",color:{border:"",fill:"",text:""},locale:"en-US"},dataTypes:{enabled:"boolean",renderMode:"enum",imagePoisition:"enum",contentType:"enum",enablePersistence:"boolean",enableRippleEffect:"boolean"},_init:function(){this._getLocalizedLabels();this.model.text=t.isNullOrUndefined(this.model.text)?this._localizedLabels.text:this.model.text;this._renderControl();this._createDelegates();this._wireEvents()},_renderControl:function(){t.setRenderMode(this);this._isLinkButton=this.element[0].nodeName=="A";this._isLinkButton&&this.model.href&&this.element.attr("href",this.model.href);this.element.addClass("e-m-"+this.model.renderMode+" e-m-btn-"+this.model.style+" e-m-btn-image e-m-state-default e-m-user-select e-m-border"+(this._isLinkButton?" e-m-actionlink ":" ")+this.model.cssClass);!this.element.attr("name")&&this.element[0].id&&this.element.attr("name",this.element[0].id);this._setImagePosition();this.model.text=this.element.html()!=""?this.element.html():t.isNullOrUndefined(this.element.attr("value"))?this.model.text:this.element.attr("value");this._setText(this.model.text);this.model.style=="back"&&this.element.addClass("e-m-icon-back");this._controlStatus(this.model.enabled);this.model.showBorder||this._setShowBorder(!1);this._setColor();this._setEnableRippleEffect()},_setEnableRippleEffect:function(){this.element[this.model.enableRippleEffect?"addClass":"removeClass"]("e-ripple")},_getLocalizedLabels:function(){this._localizedLabels=t.getLocalizedConstants(this.sfType,this.model.locale)},_setColor:function(){this.element.css({color:this.model.color.text,"border-color":this.model.color.border,"background-color":this.model.color.fill})},_createDelegates:function(){this._touchStartHandler=n.proxy(this._touchStart,this);this._touchEndHandler=n.proxy(this._touchEnd,this);this._touchMoveHandler=n.proxy(this._touchMove,this)},_wireEvents:function(n){t.listenTouchEvent(this.element,t.startEvent(),this._touchStartHandler,n)},_touchStart:function(n){this._target=n.currentTarget;this.element.removeClass("e-m-state-default").addClass("e-m-state-active");this.model.touchStart&&this._trigger("touchStart",data={text:this.model.text,currentEvent:n});t.listenEvents([window,this.element],[t.moveEvent(),t.endEvent()],[this._touchMoveHandler,this._touchEndHandler],!1)},_touchMove:function(){this.element.removeClass("e-m-state-active").addClass("e-m-state-default");t.listenTouchEvent(window,t.moveEvent(),this._touchMoveHandler,!0)},_touchEnd:function(n){this._target==this.element[0]&&(this._target=[],this.element.removeClass("e-m-state-active").addClass("e-m-state-default"),this.element.hasClass("e-m-btn-large")&&n.preventDefault(),this.model.style!="back"||this.model.touchEnd?this.model.touchEnd&&this._trigger("touchEnd",data={text:this.model.text,currentEvent:n}):history.back())},_controlStatus:function(n){this.element[n?"removeClass":"addClass"]("e-m-state-disabled");this._wireEvents(!n)},_setImagePosition:function(){this.model.imageClass&&this.model.contentType!="text"&&(this._imgClass=this.model.imageClass,this.element.addClass("e-m-btn-image "+this.model.imageClass),this.model.contentType=="both"&&this.element.removeClass("e-m-image-left e-m-image-right").addClass("e-m-image-"+this.model.imagePosition),this.model.contentType=="image"&&(this.element.removeClass("e-m-image-"+this.model.imagePosition).addClass("e-m-btn-imageonly").attr("value","").html(""),this.model.text=""));this.model.contentType=="text"&&this.element.removeClass(this.model.imageClass+" e-m-image-"+this.model.imagePosition)},_setLocale:function(){this._getLocalizedLabels();this.model.text=this._localizedLabels.text;this._setText(this.model.text)},_setModel:function(n){for(var t in n)setprop="_set"+t.charAt(0).toUpperCase()+t.slice(1),this[setprop]&&this[setprop](n[t])},_setRenderMode:function(){this.element.removeClass("e-m-ios7 e-m-android e-m-windows e-m-flat").addClass("e-m-"+this.model.renderMode)},_setStyle:function(){this.element.removeClass("e-m-btn-normal e-m-btn-back e-m-btn-header e-m-btn-large").addClass("e-m-btn-"+this.model.style+" "+(this.model.style=="back"?"e-m-icon-back":""));this._setText(this.model.text)},_setShowBorder:function(n){n?this.element.removeClass("e-m-noborder").addClass("e-m-border"):this.element.addClass("e-m-noborder").removeClass("e-m-border")},_setEnabled:function(){this._controlStatus(this.model.enabled)},_setText:function(){this.element[0].nodeName=="INPUT"?this.element.attr("value",this.model.text):this.element.html(this.model.text)},_setContentType:function(){this._setImagePosition()},_setImageClass:function(){this.element.removeClass(this._imgClass);this._setImagePosition()},_clearElement:function(){this.element.removeAttr("class");this.element.addClass("e-m-btn")},_destroy:function(){this._wireEvents(!0);this._clearElement()},enable:function(){this.model.enabled=!0;this._controlStatus(!0)},disable:function(){this.model.enabled=!1;this._controlStatus(!1)}});t.mobile.Button.ImagePosition={Left:"left",Right:"right"};t.mobile.Button.ContentType={Text:"text",Image:"image",Both:"both"};t.mobile.Button.Style={Normal:"normal",Back:"back",Header:"header",Large:"large"};t.mobile.Button.Locale=t.mobile.Button.Locale||{};t.mobile.Actionlink.Locale=t.mobile.Actionlink.Locale||{};t.mobile.Button.Locale["default"]=t.mobile.Button.Locale["en-US"]={text:"Button"};t.mobile.Actionlink.Locale["default"]=t.mobile.Actionlink.Locale["en-US"]={text:"Actionlink"}})(jQuery,Syncfusion)});
