﻿using Data;
using Newtonsoft.Json;
using Syncfusion.JavaScript;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.Script.Serialization;
using System.Web.Script.Services;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using static Data.MentalViewDataSet;

namespace WebUI
{
    public partial class EmailTemplates : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                ((Main)this.Master).ServerMessage.ButtonClicked += new ButtonClickedHandler(this.ServerMessageButtonClicked);
                ((Main)this.Master).PageTitle = GetLocalResourceObject("Page.Title").ToString();
                //Καλούμε την javascript Initialization() λόγω του UpdatePanel.
                ScriptManager.RegisterStartupScript(this, this.GetType(), "temp", "<script language='javascript'>Initialization();</script>", false);

                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                Int64 loggedEmailTemplateId = Convert.ToInt64(userData["UserId"]);
                string roleName = userData["Role"].ToString();

                if (roleName == "Guest")
                {
                    Response.StatusCode = 404;
                    Response.End();
                }

                //this.emailTemplatesPager.PageSize = 10;

                if (this.IsPostBack == false)
                {
                    this.FillEmailTemplatesGrid();
                }
                else
                {
                    //if (Request.Form["__EVENTTARGET"] == this.searchBtn.ID)
                    //{
                    //    this.emailTemplatesPager.CurrentPage = 0;
                    //    this.FillEmailTemplatesGrid();
                    //}
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void FillEmailTemplatesGrid()
        {
            Data.SortDirection sortDirection = Data.SortDirection.Ascending;
            string sortExpression = "";
            int totalResults = 0;
            Dictionary<string, object> emailTemplateData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
            Int64 tenantId = Convert.ToInt64(emailTemplateData["TenantId"]);

            //ΣΗΜΕΙΩΣΗ: δεν βάζουμε το παρακάτω κώδικα να περάσει το sorting από το grid στο Stored Procedure γιατί το default sorting που κάνει το stored procedure είναι αυτό που κάνει και το grid.
            //if (this.emailTemplatesGrid.SortedColumns.Count > 0)
            //{
            //    sortExpression = this.emailTemplatesGrid.SortedColumns[0].Field;
            //    sortDirection = this.emailTemplatesGrid.SortedColumns[0].Direction == Syncfusion.JavaScript.SortOrder.Descending ? Data.SortDirection.Descending : Data.SortDirection.Ascending;
            //}
            MentalViewDataSet ds = Data.Business.EmailTemplatesBusiness.GetAllEmailTemplatesList(tenantId);

            this.emailTemplatesGrid.DataSource = ds.EmailTemplates;
            this.emailTemplatesGrid.DataBind();
            Session["EmailTemplates"] = ds;

            ////Ρυθμίζει το paging
            //this.emailTemplatesPager.TotalRecordsCount = totalResults;
            //if (this.emailTemplatesPager.CurrentPage == 0)
            //{
            //    this.emailTemplatesPager.CurrentPage = 1;
            //}
        }

        protected void emailTemplatesGrid_ServerCommandButtonClick(object sender, Syncfusion.JavaScript.Web.GridEventArgs e)
        {
            try
            {
                if (e.EventType == "commandButtonClick")
                {
                    if (e.Arguments["commandType"].ToString() == "Edit")
                    {
                        string emailTemplateId = ((Dictionary<string, object>)e.Arguments["data"])["EmailTemplateId"].ToString();
                        Response.Redirect("~/EmailTemplate.aspx?EmailTemplateId=" + emailTemplateId);
                    }
                    else if (e.Arguments["commandType"].ToString() == "Delete")
                    {
                        string emailTemplateId = ((Dictionary<string, object>)e.Arguments["data"])["EmailTemplateId"].ToString();
                        ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete", emailTemplateId);
                    }
                    else if (e.Arguments["commandType"].ToString() == "Up" || e.Arguments["commandType"].ToString() == "Down")
                    {
                        string emailTemplateId = ((Dictionary<string, object>)e.Arguments["data"])["EmailTemplateId"].ToString();
                        UpdateEmailTemplatesIndex(emailTemplateId, e.Arguments["commandType"].ToString());
                    }
                }


            }
            catch (ThreadAbortException exp)
            {
                //do nothing or handle as required
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        //protected void searchBtn_ServerClick(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        this.emailTemplatesPager.CurrentPage = 0;
        //        this.FillEmailTemplatesGrid();
        //    }
        //    catch (Exception exp)
        //    {
        //        Data.ExceptionLogger.LogException(exp);
        //        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
        //    }
        //}

        //protected void emailTemplatesPager_Change(object Sender, Syncfusion.JavaScript.Web.PagerChangeEventArgs e)
        //{
        //    try
        //    {
        //        this.FillEmailTemplatesGrid();
        //    }
        //    catch (Exception exp)
        //    {
        //        Data.ExceptionLogger.LogException(exp);
        //        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
        //    }
        //}

        //protected void clearSearchBtn_ServerClick(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        this.emailTemplatesPager.CurrentPage = 0;
        //        this.filterTxtBox.Value = "";
        //        this.FillEmailTemplatesGrid();
        //    }
        //    catch (Exception exp)
        //    {
        //        Data.ExceptionLogger.LogException(exp);
        //        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
        //    }
        //}

        private void ServerMessageButtonClicked(object sender, ButtonClickedArgs args)
        {
            try
            {
                if (args.Action == "Delete")
                {
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        Int64 emailTemplateId = Int64.Parse(args.Tag);
                        Data.Business.EmailTemplatesBusiness.DeleteEmailTemplate(emailTemplateId);
                    }
                }
                else if (args.Action == "SessionExpired")
                {
                    Response.Redirect("Default.aspx");
                }

                this.FillEmailTemplatesGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void emailTemplatesGrid_ServerRecordDoubleClick(object sender, Syncfusion.JavaScript.Web.GridEventArgs e)
        {
            try
            {
                if (e.EventType == "recordDoubleClick")
                {
                    string emailTemplateId = ((Dictionary<string, object>)e.Arguments["data"])["EmailTemplateId"].ToString();
                    Response.Redirect("~/EmailTemplate.aspx?EmailTemplateId=" + emailTemplateId, true);

                }

                this.FillEmailTemplatesGrid();
            }
            catch (ThreadAbortException exp)
            {
                //do nothing or handle as required
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void UpdateEmailTemplatesIndex(string emailTemplateId, string direction)
        {
            try
            {
                MentalViewDataSet emailTemplatesDS = (MentalViewDataSet)Session["EmailTemplates"];
                DataTable dt = new DataView(emailTemplatesDS.EmailTemplates, "", "RowIndex, EmailTemplateId", DataViewRowState.CurrentRows).ToTable();

                //αρχικοποιεί το SortIndex με ζυγό αριθμός σε όλα τα rows.
                int rowIndex = 0;
                foreach (DataRow dr in dt.Rows)
                {
                    rowIndex = rowIndex + 2;
                    dr["RowIndex"] = rowIndex;
                }

                if (direction == "Up")
                {
                    DataRow dr = dt.Select("EmailTemplateId=" + Convert.ToInt64(emailTemplateId))[0];
                    dr["RowIndex"] = (int)dr["RowIndex"] - 3;
                }
                else
                {
                    DataRow dr = dt.Select("EmailTemplateId=" + Convert.ToInt64(emailTemplateId))[0];
                    dr["RowIndex"] = (int)dr["RowIndex"] + 3;
                }

                dt = new DataView(dt, "", "RowIndex, EmailTemplateId", DataViewRowState.CurrentRows).ToTable();  //Σορτάρει ξανά με το RowIndex, EmailTemplateId.
                
                //Φτιάχνει τo SortIndex μία με μονό αριθμό και μετά με ζυγό για να διορθωθούν οι αριθμοί.
                //Φτιάχνει το SortIndex με μονό αριθμό σε όλα τα rows.
                rowIndex = 1;
                foreach (DataRow dr in dt.Rows)
                {
                    rowIndex = rowIndex + 2;
                    dr["RowIndex"] = rowIndex;
                }

                //Φτιάχνει το SortIndex με ζυγό αριθμό σε όλα τα rows.
                rowIndex = 0;
                foreach (DataRow dr in dt.Rows)
                {
                    rowIndex = rowIndex + 2;
                    dr["RowIndex"] = rowIndex;
                }

                emailTemplatesDS.Merge(dt, false, MissingSchemaAction.Ignore);
                Data.Business.SaveAllData(emailTemplatesDS);
                this.emailTemplatesGrid.DataSource = emailTemplatesDS.EmailTemplates;
            }
            catch (ThreadAbortException exp)
            {
                //do nothing or handle as required
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }
    }
}
