﻿using Admin.Data.Model;
using Admin.WebApi;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog.Core;
using System.Diagnostics;
using System.Text.Json;

namespace Admin.Business
{
    public class TenantsBusiness : ITenantsBusiness
    {
        private Data.Model.MentalViewContext dbContext;
        private IConfiguration configuration;
        private ILogger<TenantsBusiness> logger;

        public TenantsBusiness(Admin.Data.Model.MentalViewContext dbContext, IConfiguration configuration, ILogger<TenantsBusiness> logger)
        {
            this.dbContext = dbContext;
            this.logger = logger;
            this.configuration = configuration;
        }

        public async Task<List<Admin.Data.Model.Tenant>> GetAllTenants()
        {
            try
            {
                //Validation

                //Query
                IQueryable<Admin.Data.Model.Tenant> query = this.dbContext.Tenants.AsNoTrackingWithIdentityResolution().AsQueryable();
                query = query.OrderBy(x => x.FullName);

                //Διαβάζει τα δεδομένα.
                List<Admin.Data.Model.Tenant> tenants = await query.ToListAsync();

                return tenants;
            }
            catch (Exception ex)
            {
                //logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task<Data.Model.Tenant?> GetTenant(string username)
        {
            try
            {
                //Query
                IQueryable<User> query = dbContext.Users.Where(x => x.Username == username);
                User? user = await query.AsNoTrackingWithIdentityResolution().FirstOrDefaultAsync<User>();
                if (user != null)
                {
                    IQueryable<Tenant> query2 = dbContext.Tenants.Where(x => x.TenantId == user!.TenantId);
                    return await query2.FirstOrDefaultAsync<Tenant>();
                }

                return null;
            }
            catch (Exception ex)
            {
                //logger.LogError(ex, ex.Message, new object[] { "TenantId=" + tenantId.ToString() });
                throw;
            }
        }

        public async Task<Tenant> CreateOrUpdateTenant(Tenant tenant)
        {
            try
            {
                //Validation
                if (tenant == null)
                {
                    throw new Exception(GlobalResources.InvalidDataMessage);
                }

                // validator = new ContactValidator();
                //FluentValidation.Results.ValidationResult result = validator.Validate(contact);
                //string validationErrors = string.Empty;
                //if (!result.IsValid)
                //{
                //    foreach (var failure in result.Errors)
                //    {
                //        validationErrors += failure.ErrorMessage + ". ";
                //    }
                //    throw new ApplicationException(validationErrors);
                //}

                //Query
                this.dbContext.Attach(tenant);
                await this.dbContext.SaveChangesAsync();

                //Response
                return tenant;
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                //this.logger.LogError(ex, ex.Message, new object[] { contact.ContactId });
                throw;
            }
        }

        public async Task<int[]> CheckUsersActiveSubscriptions(int joomlaUserId)
        {
            try
            {
                HttpClient client = new HttpClient();

                //if (hostEnvironment.IsDevelopment())
                //{
                //    return new int[] { 1 };  //Επιστρέφει ως Active Subscriptions μόνο το Basic πακέτο.
                //}
                //else  //στο MembershipPro του Joomla.
                //{

                string? domain = this.configuration.GetValue<string>("JoomlaWebsiteDomain");
                string? membershipproApiKey = this.configuration.GetValue<string>("MembershipProApiKey");
                string requestUri = domain + @"index.php?option=com_osmembership&task=api.get_active_plan_ids&api_key="+ membershipproApiKey + "@A&user_id=" + joomlaUserId;

                HttpMessageHandler handler = new HttpClientHandler();
                var httpClient = new HttpClient(handler)
                {
                    Timeout = new TimeSpan(0, 2, 0)
                };

                //httpClient.DefaultRequestHeaders.Add("ContentType", "application/json");
                httpClient.DefaultRequestHeaders.Add("Accept", "*/*");

                //var plainTextBytes = System.Text.Encoding.UTF8.GetBytes("petermanesis:ljb33bcbg@A33");
                //string val = System.Convert.ToBase64String(plainTextBytes);
                //httpClient.DefaultRequestHeaders.Add("Authorization", "Basic " + val);

                HttpResponseMessage response = httpClient.GetAsync(requestUri).Result;
                string content = string.Empty;
                int subscriptionsCount = 0;

                using (StreamReader stream = new StreamReader(response.Content.ReadAsStreamAsync().Result))
                {
                    content = stream.ReadToEnd();
                }

                if (content.Contains("data"))
                {
                    var dynamicObject = System.Text.Json.JsonSerializer.Deserialize<JsonElement>(content);
                    subscriptionsCount = dynamicObject.GetProperty("data").GetArrayLength();

                    string tempValue = dynamicObject.GetProperty("data").ToString().Replace("[", "").Replace("]", "");
                    return tempValue.Split(',').Select(int.Parse).ToArray();
                }

                if (subscriptionsCount > 0)
                {
                    return new int[0];
                }
                //}

                return new int[0];
            }
            catch (Exception exp)
            {
                //new ExceptionHandler(this.hostEnvironment, "FileTransformer.API").LogException(exp);
                return new int[0];
            }
        }

        /// <summary>
        /// Επιστρέφει πληροφορίες για το 1ο active subscription του χρήστη που θα βρει. Πιθανόν ο χρήστης να έχει περισσότερα από ένα active subscriptions.
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<MembershipSubscription?> GetActiveSubscriptionDetails(int userId)
        {
            try
            {
                HttpClient client = new HttpClient();

                MembershipSubscription subscription = new MembershipSubscription();

                string? domain = this.configuration.GetValue<string>("JoomlaWebsiteDomain");
                string? membershipproApiKey = this.configuration.GetValue<string>("MembershipProApiKey");
                string requestUri = domain + @"index.php?option=com_osmembership&task=api.get_user_active_subscriptions&api_key="+ membershipproApiKey + "@A&user_id=" + userId;

                HttpMessageHandler handler = new HttpClientHandler();
                var httpClient = new HttpClient(handler)
                {
                    Timeout = new TimeSpan(0, 2, 0)
                };

                //httpClient.DefaultRequestHeaders.Add("ContentType", "application/json");
                httpClient.DefaultRequestHeaders.Add("Accept", "*/*");

                //var plainTextBytes = System.Text.Encoding.UTF8.GetBytes("petermanesis:ljb33bcbg@A33");
                //string val = System.Convert.ToBase64String(plainTextBytes);
                //httpClient.DefaultRequestHeaders.Add("Authorization", "Basic " + val);

                HttpResponseMessage response = httpClient.GetAsync(requestUri).Result;
                string content = string.Empty;
                string subscriptionsArray;

                using (StreamReader stream = new StreamReader(response.Content.ReadAsStreamAsync().Result))
                {
                    content = stream.ReadToEnd();
                }

                if (content.Contains("data"))
                {
                    var dynamicObject = System.Text.Json.JsonSerializer.Deserialize<JsonElement>(content);
                    //subscriptionsArray = dynamicObject.GetProperty("data").GetString()!;

                    //Αν υπάρχει τουλάχιστον ένα subscription στα data
                    if (dynamicObject.GetProperty("data").GetArrayLength() > 0)
                    {
                        subscription.Id = int.Parse(dynamicObject.GetProperty("data")[0].GetProperty("id").ToString());
                        subscription.PlanId = int.Parse(dynamicObject.GetProperty("data")[0].GetProperty("plan_id").ToString());
                        subscription.UserId = int.Parse(dynamicObject.GetProperty("data")[0].GetProperty("user_id").ToString());
                        string? from_date = dynamicObject.GetProperty("data")[0].GetProperty("from_date").ToString();
                        if (from_date != null && from_date != "")
                        {
                            subscription.FromDate = DateTime.Parse(from_date);
                        }
                        string? to_date = dynamicObject.GetProperty("data")[0].GetProperty("to_date").ToString();
                        if (to_date != null && to_date != "")
                        {
                            subscription.ToDate = DateTime.Parse(to_date);
                        }

                        return subscription;
                    }

                    return null;
                }

                return null;
            }
            catch (Exception exp)
            {
                //new ExceptionHandler(this.hostEnvironment, "FileTransformer.API").LogException(exp);
                return null;
            }
        }
    }
}
