﻿using Admin.Data.Model;
using Microsoft.Extensions.Logging;
using static Microsoft.Extensions.Logging.EventSource.LoggingEventSource;
using Syncfusion.Blazor.Popups;
using System.Net.Http;
using Azure;
using Microsoft.Extensions.Hosting;
using Admin.Components.Shared;

namespace Admin.Components.Pages
{
    public partial class Tenants
    {
        private List<Tenant> tenants = new List<Tenant>();

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            try
            {
                if (firstRender)
                {
                    await this.FillTenants();

                    StateHasChanged();
                }
                else
                {
                }
            }
            catch (ApplicationException ex)
            {
                new ExceptionHandler("MentalView.Admin").LogException(ex);
                new ErrorNotifier(dialogService).ShowError(ex.Message, "");
            }
            catch (Exception ex)
            {
                new ExceptionHandler("MentalView.Admin").LogException(ex);
                new ErrorNotifier(dialogService).ShowError(GlobalResources.ErrorOccured, "");
            }
            finally
            {

            }
        }

        protected async Task FillTenants()
        {
            this.tenants = await this.tenantsBusiness.GetAllTenants();
        }
    }
}
