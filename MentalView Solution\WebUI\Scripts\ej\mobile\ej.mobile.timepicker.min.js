/*!
*  filename: ej.mobile.timepicker.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min","./ej.mobile.navigationbar.min","./ej.mobile.dialog.min","./ej.mobile.menu.min","./ej.mobile.scrollpanel.min"],n):n()})(function(){(function($,ej){ej.widget("ejmTimePicker","ej.mobile.TimePicker",{_setFirst:!0,_requiresID:!0,_rootCSS:"e-m-timepicker",defaults:{renderMode:"auto",hourFormat:"twentyfour",value:null,timeFormat:null,locale:"en-US",enabled:!0,enablePersistence:!1,cssClass:"",select:null,load:null,focusIn:null,focusOut:null,open:null,close:null,change:null},dataTypes:{renderMode:"enum",hourFormat:"enum",value:"string",timeFormat:"enum",locale:"string",enabled:"boolean",enablePersistence:"boolean",cssClass:"string"},_init:function(){this._renderControl();this._createDelegates();this._wireEvents()},_timepickerInitialize:function(){this.model.timeFormat=this.model.timeFormat==null?this.model.hourFormat=="twelve"?"hh:mm tt":"HH:mm":this.model.timeFormat;this.model.value=new Date(ej.format(new Date,"MM/dd/yyyy")+" "+(typeof this.model.value=="string"?this.model.value:ej.format(new Date,this.model.timeFormat)));this._currentTime=this.model.value;this._twelvedata=[{value:"01"},{value:"02"},{value:"03"},{value:"04"},{value:"05"},{value:"06"},{value:"07"},{value:"08"},{value:"09"},{value:"10"},{value:"11"},{value:"12"}];this._twentyfourdata=[{value:"00"},{value:"01"},{value:"02"},{value:"03"},{value:"04"},{value:"05"},{value:"06"},{value:"07"},{value:"08"},{value:"09"},{value:"10"},{value:"11"},{value:"12"},{value:"13"},{value:"14"},{value:"15"},{value:"16"},{value:"17"},{value:"18"},{value:"19"},{value:"20"},{value:"21"},{value:"22"},{value:"23"}];this._minsdata=[{value:"00"},{value:"01"},{value:"02"},{value:"03"},{value:"04"},{value:"05"},{value:"06"},{value:"07"},{value:"08"},{value:"09"},{value:"10"},{value:"11"},{value:"12"},{value:"13"},{value:"14"},{value:"15"},{value:"16"},{value:"17"},{value:"18"},{value:"19"},{value:"20"},{value:"21"},{value:"22"},{value:"23"},{value:"24"},{value:"25"},{value:"26"},{value:"27"},{value:"28"},{value:"29"},{value:"30"},{value:"31"},{value:"32"},{value:"33"},{value:"34"},{value:"35"},{value:"36"},{value:"37"},{value:"38"},{value:"39"},{value:"40"},{value:"41"},{value:"42"},{value:"43"},{value:"44"},{value:"45"},{value:"46"},{value:"47"},{value:"48"},{value:"49"},{value:"50"},{value:"51"},{value:"52"},{value:"53"},{value:"54"},{value:"55"},{value:"56"},{value:"57"},{value:"58"},{value:"59"}];this._meridiansdata=[{value:this._localizedLabels.AM},{value:this._localizedLabels.PM}]},_renderControl:function(){this._orgEle=$(this.element).clone();ej.setRenderMode(this);this._localizedLabels=this._getLocalizedLabels();this._timepickerInitialize();this.element.addClass(this.model.cssClass+" e-m-"+this.model.renderMode+" e-m-user-select").attr({name:this._id,readonly:!0});this._outerdiv=ej.buildTag("div.e-m-"+this.model.renderMode+" e-m-tp e-m-user-select #"+this._id+"_tp","<div class='e-m-tp-header'><span class='e-m-tp-time'><\/span>"+(this.model.hourFormat=="twelve"?"<div class='e-m-tp-mer'><span class='mer am'>"+this._localizedLabels.AM+"<\/span><span class='mer pm'>"+this._localizedLabels.PM+"<\/span><\/div>":"")+"<\/div>");this._innerdiv=ej.buildTag("div.e-m-tpinner");this._wrapdiv=ej.buildTag("div.e-m-tpwrap");this._visibletexthtml="<div class='e-m-col-fill'><\/div>";this._hours=ej.buildTag("div.e-m-tp e-m-tphours e-m-left","<div class='e-m-timewrapper'><div id="+this._id+"_tphour><div class='e-m-tpouter'>"+this._visibletexthtml+"<\/div><\/div><\/div><\/div>");this._mins=ej.buildTag("div.e-m-tp e-m-tpmins e-m-center","<div class='e-m-timewrapper'><div id="+this._id+"_tpmin><div class='e-m-tpouter'>"+this._visibletexthtml+"<\/div><\/div><\/div><\/div>");this._meridians=ej.buildTag("div.e-m-tp e-m-tpmeridians e-m-right","<div class='e-m-timewrapper'><div id="+this._id+"_tpmeridian><div class='e-m-tpouter'>"+this._visibletexthtml+"<\/div><\/div><\/div><\/div>");this._dialogRendering();this._scrollInitialize();this._setCurrentPreviousTime();this._hourstext=this._outerdiv.find("#"+this._id+"_tphour .e-m-text");this._minstext=this._outerdiv.find("#"+this._id+"_tpmin .e-m-text");this._meridianstext=this._outerdiv.find("#"+this._id+"_tpmeridian .e-m-text");this.model.load&&this._trigger("load",{value:this._currentTime});this._controlStatus(this.model.enabled)},_setCurrentPreviousTime:function(){var n=ej.format(new Date(this._currentTime),this.model.timeFormat,this.model.locale);$(this.element).val(n);this._updateHeader();this._previousTime=n},_updateHeader:function(){this._outerdiv.find(".e-m-tp-header .e-m-tp-time").text(ej.format(new Date(this._currentTime),this.model.timeFormat,this.model.locale).slice(0,5));this.model.hourFormat=="twelve"&&(this._outerdiv.find(".e-m-state-active").removeClass("e-m-state-active"),this._outerdiv.find(".e-m-tp-header .e-m-tp-mer ."+ej.format(new Date(this._currentTime),"tt",this.model.locale).toLowerCase()).addClass("e-m-state-active"))},_scrollInitialize:function(){this._displacement=45;this._targetHeight=window.innerHeight<260?45:window.innerHeight<350?135:225;this._minHeight=window.innerHeight<260?"low":window.innerHeight<350?"medium":"high";this._outerdiv.find(".e-m-col-fill").addClass("e-m-"+this._minHeight);this._scrollPanel("hour");this._scrollPanel("min");this._scrollPanel("meridian")},_scrollPanel:function(n){this["_"+n]=ej.buildTag("div#"+this._id+"tp"+n+"_scrollpanel");this._outerdiv.find("#"+this._id+"_tp"+n).append(this["_"+n]);this["_"+n].ejmScrollPanel({enableMouseWheel:!1,enableNativeScrolling:!1,enableTransition:!1,enableTransform:!1,showScrollbars:!1,target:this._id+"_tp"+n,targetHeight:this._targetHeight,renderMode:this.model.renderMode,scrollStart:$.proxy(this._onScrollStart,this),scrollStop:$.proxy(this._onScrollStop,this),enableBounce:!1,enableDisplacement:!0,displacementValue:this._displacement,displacementTime:500,isRelative:!0})},_dialogRendering:function(){this._renderTimeContent();this._dialog=ej.buildTag("div.e-m-tp-dialog e-m-"+this.model.hourFormat+"#"+this._id+"_dialog");this._dialog.ejmDialog({allowScrolling:!1,mode:"confirm",showHeader:!1,leftButtonCaption:this._localizedLabels.cancelText,rightButtonCaption:this._localizedLabels.confirmText,renderMode:this.model.renderMode,enableAnimation:!1});this._dialog.find(".e-m-dlg-content").append(this._outerdiv);this._dialog.find(".e-m-dlgbtnwrapper").appendTo(this._outerdiv)},_renderTimeContent:function(){this.model.hourFormat=="twentyfour"&&this._meridians.addClass("e-m-display-none");this._renderTemplate();this._columnRendering("hours");this._columnRendering("mins");this._columnRendering("meridians");this._innerdiv.append(this._wrapdiv);this._outerdiv.append(this._innerdiv)},_renderTemplate:function(){var n={};n["default"]="<div class='e-m-text'>{{:value}}<\/div>";$.templates(n)},_columnRendering:function(n){var t=n=="hours"?this["_"+this.model.hourFormat+"data"]:this["_"+n+"data"],i=$.render["default"](t)+this._visibletexthtml;this["_"+n].find(".e-m-tpouter").html(this["_"+n].find(".e-m-tpouter").html()+i);this._wrapdiv.append(this["_"+n])},_createDelegates:function(){this._focusHndlr=$.proxy(this._showTimePicker,this);this._blurHndlr=$.proxy(this._blurEventHandler,this);this._dlgBtnClkHndlr=$.proxy(this._dialogButtonClick,this);this._resizeHndlr=$.proxy(this._resize,this)},_wireEvents:function(n){ej.listenTouchEvent($(this._dialog).find(".e-m-dlg-btn"),"mousedown",this._dlgBtnClkHndlr,n);ej.listenEvents([$(this.element),$(this.element)],["focus","blur"],[this._focusHndlr,this._blurHndlr],n);ej.listenTouchEvent($(window),"onorientationchange"in window?"orientationchange":"resize",this._resizeHndlr,n)},_showTimePicker:function(){this.model.focusIn&&this._trigger("focusIn",{value:this._currentTime});this._showHidePicker();this._setInitialTime();this._outerdiv.find(".e-m-timewrapper").removeClass("e-m-scrollstart");this.model.open&&this._trigger("open",{value:this._currentTime})},_setInitialTime:function(){this._setValue(this._currentTime);this._setProperTime()},_setValue:function(n){var n=new Date(n);this._resetScrollPos();this._hour.ejmScrollPanel("scrollTo",0,-this._displacement*(this.model.hourFormat=="twelve"?n.getHours()%12==0?11:n.getHours()%12-1:n.getHours()),0);this._min.ejmScrollPanel("scrollTo",0,-this._displacement*n.getMinutes(),0);this._meridian.ejmScrollPanel("scrollTo",0,-this._displacement*(n.getHours()>11?1:0),0)},_resetScrollPos:function(){this._hour.ejmScrollPanel("scrollTo",0,0,0);this._min.ejmScrollPanel("scrollTo",0,0,0);this._meridian.ejmScrollPanel("scrollTo",0,0,0)},_blurEventHandler:function(){this.model.focusOut&&this._trigger("focusOut",{value:this._currentTime})},_dialogButtonClick:function(n){$(n.target).text()==this._localizedLabels.confirmText?($(this.element).val(ej.format(this._currentTime,this.model.timeFormat)),this._previousTime!=$(this.element).val()&&this.model.select&&this._trigger("select",{value:this._currentTime}),this._previousTime=$(this.element).val()):$(n.target).text()==this._localizedLabels.cancelText&&(this._currentTime=new Date(ej.format(new Date,"MM/dd/yyyy")+" "+this._previousTime),this._setInitialTime());this._showHidePicker(!0);this.model.close&&this._trigger("close",{value:this._currentTime})},_showHidePicker:function(n){var t=this,i={currentTarget:this.element},r=n?"hidden":"visible";this.element.blur();this._dialog.ejmDialog(n?"close":"open")},_resize:function(){var n=this;setTimeout(function(){var t=window.innerHeight<350?window.innerHeight<260?"low":"medium":"high";n._minHeight!=t&&(n._targetHeight=t=="low"?45:t=="medium"?135:225,n._minHeight=t,n._hour.ejmScrollPanel({targetHeight:n._targetHeight}),n._min.ejmScrollPanel({targetHeight:n._targetHeight}),n._meridian.ejmScrollPanel({targetHeight:n._targetHeight}),n._outerdiv.find(".e-m-col-fill").removeClass("e-m-short e-m-low e-m-high").addClass("e-m-"+t),n._setInitialTime())},200)},_onScrollStart:function(n){$("#"+n.model.target).find(".e-m-text-active").removeClass("e-m-text-active")},_onScrollStop:function(n){var t=n.model.target.replace(this._id+"_tp","");this._setProperTime(t)},_setProperTime:function(n){var t=this._scrollCoordinates(),i=this.model.hourFormat=="twelve"?t.meridian>0?t.hours==12?12:t.hours==0?13:t.hours==11?12:t.hours+13:t.hours==0?1:t.hours==12?11:t.hours==11?0:t.hours+1:t.hours;this._currentTime=new Date((new Date).setHours(this.model.hourFormat=="twelve"?i:t.hours,t.mins));this._activeText(this.model.hourFormat=="twelve"?t.hours==12?t.hours:t.hours+1:t.hours+1,t.mins+1,t.meridian,n);this._updateHeader();this.model.change&&this._trigger("change",{value:this._currentTime})},_scrollCoordinates:function(){var n={};return n.Scrollhours=Math.round(this._hour.ejmScrollPanel("getScrollPosition").y/this._displacement),n.Scrollmins=Math.round(this._min.ejmScrollPanel("getScrollPosition").y/this._displacement),n.Scrollmeridian=Math.round(this._meridian.ejmScrollPanel("getScrollPosition").y/this._displacement),n.hours=-n.Scrollhours,n.mins=-n.Scrollmins,n.meridian=-n.Scrollmeridian,n},_activeText:function(hours,mins,meridians,target){target?($(this["_"+target+"stext"]).removeClass("e-m-text-active"),$(this["_"+target+"stext"][eval(target+"s")-(target=="meridian"?0:1)]).addClass("e-m-text-active")):($(this._hourstext[hours-1]).addClass("e-m-text-active"),$(this._minstext[mins-1]).addClass("e-m-text-active"),$(this._meridianstext[meridians]).addClass("e-m-text-active"))},_setModel:function(n){var r=!1,t,i;for(t in n)i="_set"+t.charAt(0).toUpperCase()+t.slice(1),this[i]?this[i](n[t]):r=!0;r&&this._refresh()},_setTimeFormat:function(){$(this.element).val(ej.format(this._currentTime,this.model.timeFormat))},_setEnabled:function(n){this._controlStatus(n)},_controlStatus:function(n){this.element[n?"removeClass":"addClass"]("e-m-state-disabled")},_refresh:function(){this._destroy();this.element.addClass("e-m-timepicker");this._renderControl();this._wireEvents()},_clearElement:function(){this.element.removeAttr("class");this.element.html(this._orgEle.html())},_destroy:function(){this._wireEvents(!0);this._clearElement()},_getLocalizedLabels:function(){return ej.getLocalizedConstants(this.sfType,this.model.locale)},show:function(){this._showHidePicker()},hide:function(){this._showHidePicker(!0)},enable:function(){this.model.enabled=!0;this._controlStatus(this.model.enabled);this._showHidePicker(!0)},disable:function(){this.model.enabled=!1;this._controlStatus(this.model.enabled);this._showHidePicker(!0)},getValue:function(){return $(this.element).val()},setCurrentTime:function(n){this._currentTime=new Date(ej.format(new Date,"MM/dd/yyyy")+" "+n);this._setCurrentPreviousTime()}});ej.mobile.TimePicker.HourFormat={TwentyFour:"twentyfour",Twelve:"twelve"};ej.mobile.TimePicker.Locale=ej.mobile.TimePicker.Locale||{};ej.mobile.TimePicker.Locale["default"]=ej.mobile.TimePicker.Locale["en-US"]={confirmText:"OK",cancelText:"CANCEL",AM:"AM",PM:"PM"}})(jQuery,Syncfusion)});
