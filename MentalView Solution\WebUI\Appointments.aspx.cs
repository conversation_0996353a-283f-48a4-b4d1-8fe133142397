﻿using Data;
using Newtonsoft.Json;
using Syncfusion.DocIO.DLS;
using Syncfusion.EJ.Export;
using Syncfusion.JavaScript.Mobile;
using Syncfusion.JavaScript.Models;
using Syncfusion.JavaScript.Web;
using Syncfusion.Linq;
using Syncfusion.Pdf;
using Syncfusion.Pdf.Parsing;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Printing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Script.Serialization;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebUI
{
    public partial class Appointments : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                ((Main)this.Master).ServerMessage.ButtonClicked += new ButtonClickedHandler(this.ServerMessageButtonClicked);
                ((Main)this.Master).PageTitle = GetLocalResourceObject("Page.Title").ToString();
                //((Main)this.Master).ScheduleResourcesSectionVisible = true;

                //Καλούμε την javascript Initialization() λόγω του UpdatePanel.
                ScriptManager.RegisterStartupScript(this, this.GetType(), "temp", "<script language='javascript'>Initialization();</script>", false);

                if (Session["PdfReport"] != null)
                {
                    //Response.Write("<script>window.open('/Reporting/ExportWordReport.aspx?Inline=true','_blank');</script>");
                    ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=true','_blank');", true);
                }

                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 userId = Convert.ToInt64(userData["UserId"]);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                string roleName = userData["Role"].ToString();

                if (roleName == "Guest")
                {
                    Response.StatusCode = 404;
                    Response.End();
                }

                if (ViewState["UserData"] == null)
                {
                    MentalViewDataSet userDS = Data.Business.UsersBusiness.GetUserById(userId);
                    ViewState["UserData"] = userDS;
                }

                MentalViewDataSet usersDS = (MentalViewDataSet)ViewState["UserData"];
                this.newTaskBtn.Disabled = usersDS.Users[0].UserId != userId;


                #region  Ρυθμίσεις για τα Schedule Resources
                usersDS = Data.Business.UsersBusiness.GetAllDoctorUsersList(tenantId);
                if (this.scheduleResourcesDDL.Items.Count == 0)
                {
                    foreach (Data.MentalViewDataSet.UsersRow usersRow in usersDS.Users.Rows)
                    {
                        DropDownListItem item = new DropDownListItem();
                        item.Value = usersRow.UserId.ToString();
                        item.Text = usersRow.FullName;

                        this.scheduleResourcesDDL.Items.Add(item);
                    }
                }
                #endregion

                #region  Setup schedule
                this.appointmentsSchedule.Locale = "el-GR";
                this.appointmentsSchedule.ShowLocationField = false;
                this.appointmentsSchedule.HighlightBusinessHours = false;
                this.appointmentsSchedule.ShowCurrentTimeIndicator = false;
                this.appointmentsSchedule.TimeMode = Syncfusion.JavaScript.TimeMode.Hour24;
                this.appointmentsSchedule.ViewStateMode = ViewStateMode.Disabled;  //ViewStateMode.Enabled;
                this.appointmentsSchedule.EnablePersistence = false;
                this.appointmentsSchedule.IsResponsive = true;
                this.appointmentsSchedule.EnableLoadOnDemand = false;
                this.appointmentsSchedule.AllowDelete = false;
                this.appointmentsSchedule.EnableAppointmentResize = true;
                this.appointmentsSchedule.FirstDayOfWeek = DayOfWeek.Monday;
                this.appointmentsSchedule.AgendaViewSettings.DaysInAgenda = 30;
                this.appointmentsSchedule.ReadOnly = false;
                this.appointmentsSchedule.AllowDragAndDrop = false;
                //this.schedule.AllowDragDrop = false;
                this.appointmentsSchedule.EnableAppointmentNavigation = false;
                this.appointmentsSchedule.ShowQuickWindow = false;
                this.appointmentsSchedule.EnableResize = true;
                this.appointmentsSchedule.AppointmentSettings.ApplyTimeOffset = false;
                this.appointmentsSchedule.EnableAppointmentResize = false;
                this.appointmentsSchedule.IsDST = false;

                Data.MentalViewDataSet ds = Data.Business.AdministrationBusiness.GetUserByUserId(userId);
                SetScheduleView(ds.Users[0].AppointmentsScheduleView);
                #endregion


                if (this.Request.Params["View"] != null)
                {
                    this.appointmentsScheduleDiv.Visible = this.Request.Params["View"] == "Schedule";
                    this.appointmentsSchedule.Visible = this.Request.Params["View"] == "Schedule";
                    this.appointmentsGrid.Visible = this.Request.Params["View"] == "Grid";
                    this.appointmentsPager.Visible = this.Request.Params["View"] == "Grid";
                    this.appointmentsGridDiv.Visible = this.Request.Params["View"] == "Grid";
                    this.filterDiv.Visible = this.Request.Params["View"] == "Grid";
                    this.gridSortDiv.Visible = this.Request.Params["View"] == "Grid";
                    this.gridDateFilters.Visible = this.Request.Params["View"] == "Grid";
                }
                else
                {
                    this.appointmentsScheduleDiv.Visible = true;
                    this.appointmentsSchedule.Visible = true;
                    this.appointmentsGridDiv.Visible = false;
                    this.appointmentsGrid.Visible = false;
                    this.appointmentsPager.Visible = false;
                    this.filterDiv.Visible = false;
                    this.gridSortDiv.Visible = false;
                    this.gridDateFilters.Visible = false;
                }
                this.appointmentsPager.PageSize = 10;

                if (this.IsPostBack == false)
                {
                    //Διαβάζει τα στοιχεία του User
                    Data.MentalViewDataSet userDS = Data.Business.UsersBusiness.GetUserById(userId);

                    #region  Εμφανίζει τα επιλεγμένα resources που επέλεξε τελευταία φορά ο user.
                    this.scheduleResourcesDDL.Value = userDS.Users[0].AppointmentsScheduleVisibleResources;

                    if (string.IsNullOrEmpty(this.scheduleResourcesDDL.Value) && userDS.Users[0].AppointmentsScheduleVisibleResources == "")
                    {
                        this.EnsureScheduleResourceSelected();  //Ελέγχει και εξασφαλίζει ότι έχει επιλεγεί κάποιο resource

                        //Αποθηκεύει τα επιλεγμένα resources στα στοιχεία του User.
                        userDS.Users[0].AppointmentsScheduleVisibleResources = this.scheduleResourcesDDL.Value;
                        Data.Business.SaveAllData(userDS);
                    }
                    #endregion

                    #region  Ελέγχει και ρυθμίζει το sorting του grid
                    if (Session["AppointmentsListSort"] == null || Session["AppointmentsListSort"].ToString() == "")
                    {
                        if (userDS.Users.Count > 0)
                        {
                            if (string.IsNullOrEmpty(userDS.Users[0].AppointmentsListSort))
                            {
                                userDS.Users[0].AppointmentsListSort = "StartTime DESC";
                                Data.Business.UsersBusiness.SaveUsers(ref userDS);
                            }
                            Session["AppointmentsListSort"] = userDS.Users[0].AppointmentsListSort;
                            this.sortDDL.Value = Session["AppointmentsListSort"].ToString();
                        }
                    }
                    else
                    {
                        this.sortDDL.Value = Session["AppointmentsListSort"].ToString();
                    }
                    #endregion

                    if (this.Request.Params["View"] == "Grid")
                    {
                        this.FillappointmentsGrid();
                    }
                    else
                    {
                        if (Session["AppointmentsScheduleDate"] != null)
                        {
                            this.appointmentsSchedule.CurrentDate = Session["AppointmentsScheduleDate"].ToString();
                            DateTime currentDate = Convert.ToDateTime(this.appointmentsSchedule.CurrentDate);
                            this.FillAppointmentsSchedule(currentDate);
                        }
                        else
                        {
                            this.FillAppointmentsSchedule(null);
                        }

                    }
                }
                else  //Αν είναι PostBack
                {
                    if (Request.Form["__EVENTTARGET"] == this.searchBtn.ID)
                    {
                        this.appointmentsPager.CurrentPage = 0;
                        this.FillappointmentsGrid();
                    }
                    else if (Request.Form["__EVENTTARGET"] == "DateNavigate")
                    {
                        DateTime dt = DateTime.ParseExact(Request.Form["__EVENTARGUMENT"], "yyyy/M/d", CultureInfo.InvariantCulture);
                        this.appointmentsSchedule.CurrentDate = dt.ToString("yyyy/M/d");
                        Session["AppointmentsScheduleDate"] = this.appointmentsSchedule.CurrentDate;
                        this.FillAppointmentsSchedule(dt);
                    }
                    else
                    {
                        this.FillAppointmentsSchedule(null);
                    }

                    this.SetGridSelectedPeriodControls();
                }

                this.SetGridSelectedPeriodControls();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }


        #region Grid
        private void FillappointmentsGrid()
        {
            DateTime? startTimeFilter = null;  //Χρησιμοποιείται για να φιλτράρουμε με εύρος ημερομηνιών
            DateTime? endTimeFilter = null; //Χρησιμοποιείται για να φιλτράρουμε με εύρος ημερομηνιών
            Data.SortDirection sortDirection = Data.SortDirection.Ascending;
            string sortExpression = "";
            int totalResults = 0;

            Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
            Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

            //ΣΗΜΕΙΩΣΗ: Εδώ υλοποιούμε διαφορετικό τρόπο για το Sorting από ότι στις άλλες λίστες.
            if (this.sortDDL.Value != "")
            {
                if (this.sortDDL.Value == "StartTime ASC")
                {
                    sortExpression = "StartTime";
                    sortDirection = Data.SortDirection.Ascending;
                }
                else if (this.sortDDL.Value == "StartTime DESC")
                {
                    sortExpression = "StartTime";
                    sortDirection = Data.SortDirection.Descending;
                }
                else if (this.sortDDL.Value == "EndTime ASC")
                {
                    sortExpression = "EndTime";
                    sortDirection = Data.SortDirection.Ascending;
                }
                else if (this.sortDDL.Value == "EndTime DESC")
                {
                    sortExpression = "EndTime";
                    sortDirection = Data.SortDirection.Descending;
                }
            }

            if (Session["GridSelectedPeriod"] == null || Session["GridSelectedPeriod"].ToString() == "")
            {

            }
            else if (Session["GridSelectedPeriod"].ToString() == "CurrentDay")
            {
                startTimeFilter = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 0, 0, 0);
                endTimeFilter = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day, 23, 59, 59);
            }
            else if (Session["GridSelectedPeriod"].ToString() == "CurrentWeek")
            {
                startTimeFilter = DateFunctions.GetFirstDayOfWeek(DateTime.Today);
                endTimeFilter = startTimeFilter.Value.AddDays(7).AddSeconds(-1);

            }
            else if (Session["GridSelectedPeriod"].ToString() == "CurrentMonth")
            {
                startTimeFilter = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1, 0, 0, 0);
                endTimeFilter = startTimeFilter.Value.AddMonths(1);
                endTimeFilter = endTimeFilter.Value.AddSeconds(-1);
                // endDateFilter = new DateTime(endDateFilter.Value.Year, endDateFilter.Value.Month, endDateFilter.Value.Day, 23, 59, 59);
            }

            DataTable dt = Data.Business.AppointmentsBusiness.GetAppointments(tenantId, this.filterTxtBox.Value, sortExpression, sortDirection, this.appointmentsPager.CurrentPage - 1 >= 0 ? this.appointmentsPager.CurrentPage - 1 : 0, this.appointmentsPager.PageSize, startTimeFilter, endTimeFilter, out totalResults);

            this.appointmentsGrid.DataSource = dt;
            this.appointmentsGrid.DataBind();


            //Ρυθμίζει το paging
            this.appointmentsPager.TotalRecordsCount = totalResults;
            if (this.appointmentsPager.CurrentPage == 0)
            {
                this.appointmentsPager.CurrentPage = 1;
            }
        }

        protected void appointmentsGrid_ServerCommandButtonClick(object sender, Syncfusion.JavaScript.Web.GridEventArgs e)
        {
            try
            {
                if (e.EventType == "commandButtonClick")
                {
                    if (e.Arguments["commandType"].ToString() == "Edit")
                    {
                        string appointmentId = ((Dictionary<string, object>)e.Arguments["data"])["AppointmentId"].ToString();
                        Response.Redirect("~/Appointment.aspx?AppointmentId=" + appointmentId);
                    }
                    else if (e.Arguments["commandType"].ToString() == "Delete")
                    {
                        string appointmentId = ((Dictionary<string, object>)e.Arguments["data"])["AppointmentId"].ToString();
                        ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete", appointmentId);
                    }
                }

                this.FillappointmentsGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void searchBtn_ServerClick(object sender, EventArgs e)
        {
            try
            {
                this.appointmentsPager.CurrentPage = 0;
                this.FillappointmentsGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void appointmentsPager_Change(object Sender, Syncfusion.JavaScript.Web.PagerChangeEventArgs e)
        {
            try
            {
                this.FillappointmentsGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void clearSearchBtn_ServerClick(object sender, EventArgs e)
        {
            try
            {
                this.appointmentsPager.CurrentPage = 0;
                this.filterTxtBox.Value = "";
                this.FillappointmentsGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void ServerMessageButtonClicked(object sender, ButtonClickedArgs args)
        {
            try
            {
                if (args.Action == "Delete")
                {
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        Int64 appointmentId = Int64.Parse(args.Tag);
                        Data.Business.AppointmentsBusiness.DeleteAppointment(appointmentId);
                    }
                }
                else if (args.Action == "SessionExpired")
                {
                    Response.Redirect("Default.aspx");
                }

                this.FillAppointmentsSchedule(null);
                //this.FillappointmentsGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void sortDDL_ValueSelect(object sender, Syncfusion.JavaScript.Web.DropdownListEventArgs e)
        {
            try
            {
                Session["TasksListSort"] = this.sortDDL.Value;
                Data.Business.AdministrationBusiness.SetUserAppointmentsSort(this.Page.User.Identity.Name, this.sortDDL.Value);
                this.FillappointmentsGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void appointmentsGrid_ServerRecordDoubleClick(object sender, Syncfusion.JavaScript.Web.GridEventArgs e)
        {
            try
            {
                if (e.EventType == "recordDoubleClick")
                {
                    string appointmentId = ((Dictionary<string, object>)e.Arguments["data"])["AppointmentId"].ToString();
                    Response.Redirect("~/Appointment.aspx?AppointmentId=" + appointmentId);
                }

                this.FillappointmentsGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void SetGridSelectedPeriodControls()
        {
            this.currentDayBtn.Attributes.Remove("class");
            this.currentWeekBtn.Attributes.Remove("class");
            this.currentMonthBtn.Attributes.Remove("class");
            this.currentDayBtn.Attributes.Add("class", " btn btn-default");
            this.currentWeekBtn.Attributes.Add("class", " btn btn-default");
            this.currentMonthBtn.Attributes.Add("class", " btn btn-default");

            if (Session["GridSelectedPeriod"] == null || Session["GridSelectedPeriod"].ToString() == "")
            {

            }
            else if (Session["GridSelectedPeriod"].ToString() == "CurrentDay")
            {
                this.currentDayBtn.Attributes.Add("class", " btn btn-default active");
            }
            else if (Session["GridSelectedPeriod"].ToString() == "CurrentWeek")
            {
                this.currentWeekBtn.Attributes.Add("class", "btn btn-default active");
            }
            else if (Session["GridSelectedPeriod"].ToString() == "CurrentMonth")
            {
                this.currentMonthBtn.Attributes.Add("class", "btn btn-default active");
            }
        }
        #endregion

        #region  Schedule
        private void FillAppointmentsSchedule(DateTime? currentDate)
        {
            Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
            Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
            List<Int64> selectedUsersIds = new List<Int64>();

            //Data.MentalViewDataSet.AppointmentCategoriesDataTable appointmentCategoriesDT = Data.Business.AppointmentCategoriesBusiness.GetAllAppointmentCategories(tenantId);
            //this.appointmentsSchedule.CategorizeSettings.DataSource = appointmentCategoriesDT;
            this.appointmentsSchedule.CategorizeSettings.DataSource = Data.FieldValuesMappings.DataSet.Tables["Appointments-AppointmentRooms"];


            if (currentDate.HasValue == false)
            {
                if (this.appointmentsSchedule.CurrentDate == "")
                {
                    this.appointmentsSchedule.CurrentDate = DateTime.Now.ToString("yyyy/MM/dd");
                }
                currentDate = DateTime.Parse(this.appointmentsSchedule.CurrentDate);
            }
            DateTime startDate = DateTime.Now;
            DateTime endDate = DateTime.Now.AddMonths(3);

            switch (this.appointmentsSchedule.CurrentView)
            {
                case Syncfusion.JavaScript.CurrentView.Day:
                    {
                        startDate = currentDate.Value.AddSeconds(-1);
                        endDate = currentDate.Value.AddDays(1);
                        break;
                    }
                case Syncfusion.JavaScript.CurrentView.Week:
                    {
                        startDate = FirstWeekDate(currentDate.Value.Date).Date;
                        endDate = startDate.AddDays(6);
                        break;
                    }
                case Syncfusion.JavaScript.CurrentView.Workweek:
                    {
                        startDate = FirstWeekDate(currentDate.Value.Date).Date;
                        endDate = startDate.AddDays(5);
                        break;
                    }
                case Syncfusion.JavaScript.CurrentView.Month:
                    {
                        startDate = currentDate.Value.Date.AddDays(-currentDate.Value.Day - 8);  //Αφαιρούμε επιπλέον 8 ημέρες επειδή το μηνιαίο ημερολόγιο μπορεί να εμφανίζει και μερικές ημέρες του προηγούμενου μήνα. 
                        endDate = startDate.AddDays(40);
                        break;
                    }
                case Syncfusion.JavaScript.CurrentView.Agenda:
                    {
                        startDate = currentDate.Value;
                        endDate = startDate.AddDays(this.appointmentsSchedule.AgendaViewSettings.DaysInAgenda);
                        break;
                    }
            }

            #region Databinding τα Resources
            Data.MentalViewDataSet usersDS = Data.Business.UsersBusiness.GetAllDoctorUsersList(tenantId);
            this.scheduleResourcesDDL.DataSource = usersDS.Users;

            List<object> resources = new List<object>();
            foreach (string selectedValue in this.scheduleResourcesDDL.SelectedItemsValue)
            {
                Data.MentalViewDataSet.UsersRow selectedUsersRow = usersDS.Users.FindByUserId(Convert.ToInt64(selectedValue));
                resources.Add(new { UserId = selectedUsersRow.UserId, FullName = selectedUsersRow.FullName, Color = Color.FromName("#f8a398") });

                selectedUsersIds.Add(Convert.ToInt64(selectedValue));
            }

            this.appointmentsSchedule.Resources[0].ResourceSettings.DataSource = resources;
            #endregion


            //Databinding τα κανονικά data
            DataTable dt = Data.Business.AppointmentsBusiness.GetAppointmentsByDates(tenantId, startDate, endDate, AppointmentsSearchByDateType.StartTime, null, selectedUsersIds.ToArray());
            this.appointmentsSchedule.DataSource = dt;

        }

        private void SetScheduleView(string view)
        {
            if (view.ToLower() == "day")
            {
                this.appointmentsSchedule.CurrentView = Syncfusion.JavaScript.CurrentView.Day;
            }
            else if (view.ToLower() == "week")
            {
                this.appointmentsSchedule.CurrentView = Syncfusion.JavaScript.CurrentView.Week;
            }
            else if (view.ToLower() == "workweek")
            {
                this.appointmentsSchedule.CurrentView = Syncfusion.JavaScript.CurrentView.Workweek;
            }
            else if (view.ToLower() == "month")
            {
                this.appointmentsSchedule.CurrentView = Syncfusion.JavaScript.CurrentView.Month;
            }
            else if (view.ToLower() == "agenda")
            {
                this.appointmentsSchedule.CurrentView = Syncfusion.JavaScript.CurrentView.Agenda;
            }
        }

        protected void appointmentsSchedule_ServerNavigation(object sender, Syncfusion.JavaScript.Web.ScheduleEventArgs e)
        {
            object value;
            DateTime startDate = DateTime.Now;
            DateTime endDate = DateTime.Now;

            Dictionary<string, object> dictionary = (Dictionary<string, object>)e.Arguments;

            // Ρυθμίζει την τρέχουσα ημερομηνία
            dictionary.TryGetValue("currentDate", out value);
            DateTime currentDate = Convert.ToDateTime(value);

            // Ρυθμίζει την προηγούμενη ημερομηνία
            dictionary.TryGetValue("previousDate", out value);
            DateTime previousDate = Convert.ToDateTime(value);

            // Ρυθμίζει το τρέχον view
            dictionary.TryGetValue("currentView", out value);
            string currentView = value.ToString();

            this.appointmentsSchedule.CurrentDate = currentDate.ToString("yyyy/MM/dd");
            this.SetScheduleView(currentView);



            //Αποθηκεύει τις ρυθμίσεις του User
            Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
            Int64 userId = Convert.ToInt64(userData["UserId"]);
            Data.Business.AdministrationBusiness.SetUserAppointmentsScheduleView(Page.User.Identity.Name, currentView);

            ////Αν η νέα currentDate που πρόκειται να γίνει τρέχουσα δεν περιέχει ώρα παρά μόνο ημερομηνία.
            //if (currentDate.TimeOfDay.Seconds == 0)
            //{
            //    ////Αν η currentDate αναφέρεται στην σημερινή ημέρα.
            //    //if (currentDate == DateTime.Today.Date)
            //    //{
            //    //    currentDate = currentDate + DateTime.Now.TimeOfDay;  //Προσθέτει και την τρέχουσα ώρα
            //    //}
            //    //else  //Αν η currentDate αναφέρεται σε κάποια άλλη ημέρα εκτός της σημερινής.
            //    //{
            //    //    currentDate = currentDate + new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 12, 0, 0).TimeOfDay;  //Προσθέτει σαν ώρα τη μεσημεριανή ώρα.
            //    //}

            //    currentDate = currentDate + new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 12, 0, 0).TimeOfDay;  //Προσθέτει σαν ώρα τη μεσημεριανή ώρα.
            //    currentDate = DateTime.SpecifyKind(currentDate, DateTimeKind.Utc);
            //}

            this.FillAppointmentsSchedule(currentDate);
        }


        protected void appointmentsSchedule_ServerMenuItemClick(object sender, Syncfusion.JavaScript.Web.ScheduleEventArgs e)
        {
            //Δεν χρησιμοποιείται αλλα μπορεί να δουλέψει.
            try
            {
                Dictionary<string, object> events = (Dictionary<string, object>)e.Arguments["events"];
                string eventID = events["ID"].ToString();

                if (eventID == "edit")
                {
                    Dictionary<string, object> targetInfo = (Dictionary<string, object>)e.Arguments["targetInfo"];
                    int serviceId = int.Parse(targetInfo["AppointmentId"].ToString());

                    Response.Redirect("/Appointment.aspx?AppointmentId=" + serviceId.ToString());

                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void appointmentsSchedule_ServerAppointmentClick(object sender, Syncfusion.JavaScript.Web.ScheduleEventArgs e)
        {
            try
            {
                Dictionary<string, object> args = (Dictionary<string, object>)e.Arguments["appointment"];

                if (e.EventType == "appointmentClick")
                {
                    string appointmentIdText = args["AppointmentId"].ToString();
                    //Dictionary<string, object> targetInfo = (Dictionary<string, object>)e.Arguments["targetInfo"];
                    Int64 appointmentId = Int64.Parse(appointmentIdText);

                    Response.Redirect("Appointment.aspx?AppointmentId=" + appointmentId.ToString());
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }
        #endregion

        internal static DateTime FirstWeekDate(DateTime CurrentDate)
        {
            try
            {
                DateTime FirstDayOfWeek = CurrentDate;
                DayOfWeek WeekDay = FirstDayOfWeek.DayOfWeek;
                switch (WeekDay)
                {

                    case DayOfWeek.Monday:
                        //FirstDayOfWeek = FirstDayOfWeek.AddDays(-1);
                        break;
                    case DayOfWeek.Tuesday:
                        FirstDayOfWeek = FirstDayOfWeek.AddDays(-1);
                        break;
                    case DayOfWeek.Wednesday:
                        FirstDayOfWeek = FirstDayOfWeek.AddDays(-2);
                        break;
                    case DayOfWeek.Thursday:
                        FirstDayOfWeek = FirstDayOfWeek.AddDays(-3);
                        break;
                    case DayOfWeek.Friday:
                        FirstDayOfWeek = FirstDayOfWeek.AddDays(-4);
                        break;
                    case DayOfWeek.Saturday:
                        FirstDayOfWeek = FirstDayOfWeek.AddDays(-5);
                        break;
                    case DayOfWeek.Sunday:
                        FirstDayOfWeek = FirstDayOfWeek.AddDays(-6);
                        break;
                }
                return (FirstDayOfWeek);
            }
            catch
            {
                return DateTime.Now;
            }
        }

        protected void appointmentsSchedule_ServerDragStop(object sender, Syncfusion.JavaScript.Web.ScheduleEventArgs e)
        {
            try
            {
                Int64 appointmentId = Convert.ToInt64(((Dictionary<string, object>)e.Arguments["appointment"])["AppointmentId"]);
                DateTime newStartTime = Convert.ToDateTime(((Dictionary<string, object>)e.Arguments["appointment"])["StartTime"]);
                DateTime newEndTime = Convert.ToDateTime(((Dictionary<string, object>)e.Arguments["appointment"])["EndTime"]);
                TimeSpan duration = newEndTime - newStartTime;

                Data.MentalViewDataSet taskDS = Data.Business.AppointmentsBusiness.GetAppointmentById(appointmentId);
                taskDS.Appointments[0].StartTime = newStartTime;
                taskDS.Appointments[0].EndTime = newStartTime + duration;

                Data.Business.SaveAllData(taskDS);

                this.FillAppointmentsSchedule(null);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void appointmentsSchedule_ServerResizeStop(object sender, Syncfusion.JavaScript.Web.ScheduleEventArgs e)
        {
            try
            {
                Int64 appointmentId = Convert.ToInt64(((Dictionary<string, object>)e.Arguments["appointment"])["AppointmentId"]);
                DateTime newStartTime = Convert.ToDateTime(((Dictionary<string, object>)e.Arguments["appointment"])["StartTime"]);
                DateTime newEndTime = Convert.ToDateTime(((Dictionary<string, object>)e.Arguments["appointment"])["EndTime"]);
                TimeSpan duration = newEndTime - newStartTime;

                Data.MentalViewDataSet taskDS = Data.Business.AppointmentsBusiness.GetAppointmentById(appointmentId);
                taskDS.Appointments[0].StartTime = newStartTime;
                taskDS.Appointments[0].EndTime = newStartTime + duration;

                Data.Business.SaveAllData(taskDS);

                this.FillAppointmentsSchedule(null);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void currentDayBtn_ServerClick(object sender, EventArgs e)
        {
            try
            {
                if (Session["GridSelectedPeriod"] == null || Session["GridSelectedPeriod"].ToString() != "CurrentDay")
                {
                    Session["GridSelectedPeriod"] = "CurrentDay";
                }
                else
                {
                    Session["GridSelectedPeriod"] = null;
                }
                this.SetGridSelectedPeriodControls();
                this.FillappointmentsGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }

        }

        protected void currentWeekBtn_ServerClick(object sender, EventArgs e)
        {
            try
            {
                if (Session["GridSelectedPeriod"] == null || Session["GridSelectedPeriod"].ToString() != "CurrentWeek")
                {
                    Session["GridSelectedPeriod"] = "CurrentWeek";
                }
                else
                {
                    Session["GridSelectedPeriod"] = null;
                }
                this.SetGridSelectedPeriodControls();
                this.FillappointmentsGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void currentMonthBtn_ServerClick(object sender, EventArgs e)
        {
            try
            {
                if (Session["GridSelectedPeriod"] == null || Session["GridSelectedPeriod"].ToString() != "CurrentMonth")
                {
                    Session["GridSelectedPeriod"] = "CurrentMonth";
                }
                else
                {
                    Session["GridSelectedPeriod"] = null;
                }
                this.SetGridSelectedPeriodControls();
                this.FillappointmentsGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void scheduleResourcesDDL_ValueSelect(object sender, Syncfusion.JavaScript.Web.DropdownListEventArgs e)
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 userId = Convert.ToInt64(userData["UserId"]);

                this.EnsureScheduleResourceSelected();  //Ελέγχει και εξασφαλίζει ότι έχει επιλεγεί κάποιο resource

                //Διαβάζει το entity του User.
                Data.MentalViewDataSet userDS = Data.Business.UsersBusiness.GetUserById(userId);

                //Αποθηκεύει τα επιλεγμένα resources που έκανε ο χρήστης.
                userDS.Users[0].AppointmentsScheduleVisibleResources = this.scheduleResourcesDDL.Value;
                Data.Business.SaveAllData(userDS);

                if (Session["AppointmentsScheduleDate"] != null)
                {
                    this.appointmentsSchedule.CurrentDate = Session["AppointmentsScheduleDate"].ToString();
                    DateTime currentDate = Convert.ToDateTime(Session["AppointmentsScheduleDate"].ToString());
                    this.FillAppointmentsSchedule(currentDate);
                }
                else
                {
                    //this.FillAppointmentsSchedule(null);
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        /// <summary>
        /// Checks if user has selected any resource and if not, then it selectes all the resources.
        /// </summary>
        private void EnsureScheduleResourceSelected()
        {
            //Αν ο χρήστης δεν έχει επιλέξει τίποτα στο dropdownlist τότε επιλέγει όλα τα items.
            if (this.scheduleResourcesDDL.Value == null || this.scheduleResourcesDDL.Value == "")
            {
                string resourcesValue = "";
                foreach (DropDownListItem dropDownListItem in this.scheduleResourcesDDL.Items)
                {
                    resourcesValue += dropDownListItem.Value + ",";
                }
                if (resourcesValue.Length > 0)
                {
                    resourcesValue = resourcesValue.TrimEnd(',');
                }

                this.scheduleResourcesDDL.Value = resourcesValue;
            }
        }


        protected void appointmentsSchedule_ServerExportPDF(object sender, ScheduleEventArgs e)
        {
            try
            {
                if (this.appointmentsSchedule.CurrentView != Syncfusion.JavaScript.CurrentView.Month)
                {
                    ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("ExportSchedulerOnlyInMonthMessage").ToString(), ServerMessageButtons.Ok);
                    return;
                }


                this.appointmentsSchedule.BlockoutSettings.Enable = false;
                ScheduleProperties scheduleObject = new SchedulePDFExport().ScheduleSerializeModel(e.Arguments["model"].ToString());
                //ScheduleProperties scheduleObject = new SchedulePDFExport().ScheduleSerializeModel("{\"views\":[\"day\",\"week\",\"workweek\",\"month\",\"agenda\"],\"agendaViewSettings\":{\"daysInAgenda\":30,\"dateColumnTemplateId\":null,\"timeColumnTemplateId\":null},\"currentView\":\"week\",\"timeMode\":\"24\",\"firstDayOfWeek\":\"Monday\",\"workWeek\":[\"monday\",\"tuesday\",\"wednesday\",\"thursday\",\"friday\"],\"dateFormat\":\"\",\"isDST\":false,\"timeZone\":null,\"timeScale\":{\"enable\":true,\"minorSlotCount\":2,\"majorSlot\":60,\"minorSlotTemplateId\":null,\"majorSlotTemplateId\":null},\"startHour\":0,\"endHour\":24,\"highlightBusinessHours\":false,\"enableLoadOnDemand\":false,\"enablePersistence\":false,\"showQuickWindow\":false,\"showRecurrenceAlert\":true,\"showLocationField\":false,\"showTimeZoneFields\":true,\"businessStartHour\":9,\"businessEndHour\":18,\"workHours\":{\"highlight\":true,\"start\":9,\"end\":18},\"width\":1771.2,\"height\":1120,\"cellHeight\":\"\",\"cellWidth\":\"\",\"currentDate\":\"2022-11-22 00:00:00\",\"minDate\":\"1900-01-31 22:25:08\",\"maxDate\":\"2100-01-30 22:00:00\",\"cssClass\":\"\",\"locale\":\"el-GR\",\"enableResize\":true,\"readOnly\":false,\"enableRTL\":false,\"enableAppointmentNavigation\":null,\"showAppointmentNavigator\":false,\"appointmentTemplateId\":null,\"allDayCellsTemplateId\":null,\"dateHeaderTemplateId\":null,\"workCellsTemplateId\":null,\"resourceHeaderTemplateId\":null,\"allowDragDrop\":null,\"allowDragAndDrop\":true,\"enableAppointmentResize\":false,\"showCurrentTimeIndicator\":false,\"showAllDayRow\":true,\"showHeaderBar\":true,\"isResponsive\":true,\"showWeekend\":true,\"enableRecurrenceValidation\":true,\"appointmentDragArea\":\"\",\"showOverflowButton\":true,\"allowInline\":false,\"allowDelete\":false,\"reminderSettings\":{\"enable\":false,\"alertBefore\":5},\"group\":{\"resources\":[\"Doctors\"]},\"resources\":[{\"field\":\"UserId\",\"title\":\"Γιατροί\",\"name\":\"Doctors\",\"allowMultiple\":true,\"resourceSettings\":{\"color\":\"Color\",\"dataSource\":[{\"UserId\":7,\"FullName\":\"Ψυχίατρος 2\",\"Color\":{\"R\":0,\"G\":0,\"B\":0,\"A\":0,\"IsKnownColor\":false,\"IsEmpty\":false,\"IsNamedColor\":true,\"IsSystemColor\":false,\"Name\":\"#f8a398\"}},{\"UserId\":6,\"FullName\":\"Ψυχίατρος 1\",\"Color\":{\"R\":0,\"G\":0,\"B\":0,\"A\":0,\"IsKnownColor\":false,\"IsEmpty\":false,\"IsNamedColor\":true,\"IsSystemColor\":false,\"Name\":\"#f8a398\"}}],\"id\":\"UserId\",\"text\":\"FullName\"}}],\"allowKeyboardNavigation\":true,\"blockoutSettings\":{\"enable\":false,\"templateId\":null,\"query\":null,\"tableName\":null,\"id\":\"Id\",\"subject\":\"Subject\",\"startTime\":\"StartTime\",\"endTime\":\"EndTime\",\"isBlockAppointment\":\"IsBlockAppointment\",\"isAllDay\":null,\"customStyle\":null,\"resourceId\":null,\"groupId\":null},\"appointmentSettings\":{\"applyTimeOffset\":false,\"editFutureEventsOnly\":false,\"query\":{\"queries\":[],\"_key\":\"\",\"_fKey\":\"\",\"_subQuery\":null,\"_isChild\":false},\"tableName\":null,\"id\":\"AppointmentId\",\"subject\":\"Summary\",\"description\":\"Summary\",\"startTime\":\"StartTime\",\"endTime\":\"EndTime\",\"recurrence\":\"Recurrence\",\"recurrenceRule\":\"RecurrenceRule\",\"allDay\":\"AllDay\",\"resourceFields\":\"UserId\",\"categorize\":\"Room\",\"recurrenceId\":\"RecurrenceId\",\"recurrenceExDate\":\"RecurrenceExDate\",\"location\":\"Location\",\"priority\":{},\"startTimeZone\":\"StartTimeZone\",\"endTimeZone\":\"EndTimeZone\"},\"renderDates\":null,\"orientation\":\"vertical\",\"categorizeSettings\":{\"title\":null,\"enable\":true,\"allowMultiple\":false,\"dataSource\":[{\"RoomId\":\"1\",\"RoomName\":\"Αίθουσα 1\",\"BackColor\":\"#E0CFE9\",\"FontColor\":\"#000\"},{\"RoomId\":\"2\",\"RoomName\":\"Αίθουσα 2\",\"BackColor\":\"#f8ec9d\",\"FontColor\":\"#000\"},{\"RoomId\":\"3\",\"RoomName\":\"Αίθουσα 3\",\"BackColor\":\"#75ab4c\",\"FontColor\":\"#000\"},{\"RoomId\":\"4\",\"RoomName\":\"Αίθουσα 4\",\"BackColor\":\"#A8D5FF\",\"FontColor\":\"#000\"},{\"RoomId\":\"5\",\"RoomName\":\"Αίθουσα 5\",\"BackColor\":\"#ee5b3e\",\"FontColor\":\"#000\"},{\"RoomId\":\"6\",\"RoomName\":\"Αίθουσα 6\",\"BackColor\":\"#ffb138\",\"FontColor\":\"#000\"},{\"RoomId\":\"7\",\"RoomName\":\"Αίθουσα 7\",\"BackColor\":\"#9e7d4a\",\"FontColor\":\"#000\"},{\"RoomId\":\"8\",\"RoomName\":\"Διαδικτυακά\",\"BackColor\":\"#196aa4\",\"FontColor\":\"#fff\"}],\"text\":\"RoomName\",\"id\":\"RoomId\",\"color\":\"BackColor\",\"fontColor\":\"FontColor\"},\"showTimeScale\":true,\"tooltipSettings\":{\"enable\":true,\"templateId\":null},\"showDeleteConfirmationDialog\":true,\"showNextPrevMonth\":true,\"cellClick\":\"onCellClick\",\"cellHover\":null,\"appointmentClick\":\"onAppointmentClick\",\"appointmentHover\":\"onAppointmentHover\",\"cellDoubleClick\":\"onCellDoubleClick\",\"appointmentWindowOpen\":\"onAppointmentWindowOpen\",\"appointmentSaved\":null,\"appointmentEdited\":null,\"appointmentDeleted\":null,\"beforeAppointmentCreate\":null,\"beforeAppointmentChange\":\"onAppointmentEdit\",\"beforeAppointmentRemove\":null,\"appointmentCreated\":null,\"appointmentChanged\":null,\"appointmentRemoved\":null,\"keyDown\":null,\"navigation\":\"onNavigation\",\"dragStart\":null,\"drag\":null,\"dragStop\":\"onDragStop\",\"resizeStart\":null,\"resize\":null,\"resizeStop\":null,\"menuItemClick\":\"onMenuItemClick\",\"beforeContextMenuOpen\":null,\"reminder\":null,\"actionBegin\":null,\"actionComplete\":\"ActionComplete\",\"overflowButtonClick\":null,\"overflowButtonHover\":null,\"create\":\"OnCreate\",\"load\":null,\"destroy\":null,\"queryCellInfo\":null,\"clientId\":\"appointmentsSchedule\",\"uniqueId\":\"ctl00$ctl00$includeFilesBody$mainBody$appointmentsSchedule\",\"serverEvents\":[\"appointmentClick\",\"menuItemClick\",\"navigation\",\"resizeStop\",\"dragStop\",\"exportPDF\"]}");
                //scheduleObject.BlockoutSettings.DataSource = (IEnumerable)new JavaScriptSerializer().Deserialize(e.Arguments["blockedApp"].ToString(), typeof(IEnumerable));
                IEnumerable scheduleAppointments = (IEnumerable)new JavaScriptSerializer().Deserialize(e.Arguments["processedApp"].ToString(), typeof(IEnumerable));
                //IEnumerable scheduleAppointments = (IEnumerable)new JavaScriptSerializer().Deserialize("[{\"AppointmentId\":50,\"TenantId\":1,\"ContactId\":7,\"UserId\":6,\"AppointmentCategoryId\":null,\"StartTime\":\"2022-11-21 14:18:00\",\"StartTimeZone\":\"\",\"EndTime\":\"2022-11-21 15:18:00\",\"EndTimeZone\":\"\",\"Subject\":\"\",\"Description\":\"\",\"AllDay\":false,\"Categorize\":\"\",\"ResourceFields\":\"\",\"Recurrence\":false,\"RecurrenceRule\":\"\",\"RecurrenceId\":null,\"RecurrenceExDate\":\"\",\"Request\":\"\",\"State\":\"\",\"Room\":\"2\",\"ContactFirstName\":\"3\",\"ContactLastName\":\"3\",\"UserFullName\":\"Ψυχίατρος 1\",\"ContactFullName\":\"3 3\",\"Summary\":\"3 3\",\"AppTaskId\":1,\"ParentId\":null,\"Guid\":\"39f66a89-c9e3-400a-7e67-4c5d7343da4c\"}]", typeof(IEnumerable));
                PdfPageSettings pageSettings = new PdfPageSettings(50f) { Orientation = PdfPageOrientation.Portrait };
                pageSettings.Size = PdfPageSize.A4;
                // Here Landscape value assigned to the orientation. You can set either Landscape/Portrait for this Orientation property
                pageSettings.Orientation = PdfPageOrientation.Landscape;
                // Here assigned different values for the margins 
                pageSettings.Margins.Left = 15;
                pageSettings.Margins.Right = 15;
                pageSettings.Margins.Top = 20;
                pageSettings.Margins.Bottom = 20;

                PdfDocument pdfDocument = new PdfExport().Export(scheduleObject, scheduleAppointments, ExportTheme.FlatAzure, "el-GR", pageSettings);

                Session["PdfReport"] = pdfDocument;
                Session["ReportName"] = this.GetLocalResourceObject("ScheduleReportName") + ".pdf";

                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=true','_blank');", true);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }



    }
}