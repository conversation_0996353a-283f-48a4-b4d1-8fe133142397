﻿- Μηχανισμός Recurrence
  Δεν χρησιμοποιώ το μηχανισμό της Syncfusion αλλά δικό μου με τα πεδία CustomRecurrenceRule και CustomRecurrenceId. To CustomRecurrenceId είναι ένα Guid που το έχουν όλα appointments που ανήκουν στην ίδια επανάληψη.
  Ένα appoιntment θεωρείται ότι επαναλαμβάνεται αν έχει συμπληρωμένο το πεδίο CustomRecurrenceId (αλλά θα πρέπει να έχει και το CustomRecurrenceRule συμπληρωμένο για να μη χαθεί η πληροφορία).

- Όταν κάνω update το AppointmentsDataTable στο DataSet πρέπει στο select query FillByAppointmentId να προσθέτω το:
	STUFF ((SELECT ',' + CONVERT(varchar, StartTime, 120) AS Expr1
                        FROM      Appointments AS A
                        WHERE   (CustomRecurrenceId = B.CustomRecurrenceId) AND (CustomRecurrenceId <> '') FOR XML PATH('')), 1, 1, '') AS RecurrencesStartDates
  δηλαδή το query θα είναι:
  SELECT AllDay, AppointmentCategoryId, AppointmentId, AppointmentType, Canceled, Categorize, ChargeableCancellation, ContactId, CustomRecurrence, CustomRecurrenceId, CustomRecurrenceRule, Description, EndTime, EndTimeZone, IntervetionModel, IntervetionTechniques, Notes, Recurrence, RecurrenceExDate, RecurrenceId, RecurrenceRule, Request, ResourceFields, Room, StartTime, StartTimeZone, State, Subject, SupervisionRequest, SupervisorCommentsAfter, SupervisorInstructionsBefore, TenantId, Therapist, TherapistComments, UserId, VisibleToAll, 
  STUFF ((SELECT ',' + CONVERT(varchar, StartTime, 120) AS Expr1
                        FROM      Appointments AS A
                        WHERE   (CustomRecurrenceId = B.CustomRecurrenceId) AND (CustomRecurrenceId <> '') FOR XML PATH('')), 1, 1, '') AS RecurrencesStartDates

   FROM Appointments AS B WHERE (AppointmentId = @AppointmentId)

- Για να επιτρέπονται τα αρχεία axd από τον server της TopHost πρέπει να προσθέσω στο web.config το παρακάτω:
  <system.webServer>
  <modules>
  <remove name="ModSecurity IIS (32bits)"/>

  <remove name="ModSecurity IIS (64bits)"/>
  </modules>
  </system.webServer>

