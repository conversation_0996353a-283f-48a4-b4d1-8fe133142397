/*!
*  filename: ej.slider.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./ej.button.min"],n):n()})(function(){(function(n,t,i){t.widget("ejSlider","ej.Slider",{element:null,model:null,validTags:["div","span"],_addToPersist:["value","values"],_rootCSS:"e-slider",_setFirst:!1,_requiresID:!0,defaults:{orientation:"horizontal",enableAnimation:!0,animationSpeed:500,showTooltip:!0,cssClass:"",showRoundedCorner:!1,readOnly:!1,enableRTL:!1,htmlAttributes:{},minValue:0,maxValue:100,sliderType:"default",value:null,values:null,incrementStep:1,height:null,width:null,enabled:!0,showScale:!1,largeStep:10,smallStep:1,showSmallTicks:!0,showButtons:!1,enablePersistence:!1,allowMouseWheel:!1,start:null,stop:null,slide:null,change:null,create:null,destroy:null,tooltipChange:null,renderingTicks:null},dataTypes:{orientation:"enum",enableAnimation:"boolean",animationSpeed:"number",cssClass:"string",showRoundedCorner:"boolean",readOnly:"boolean",enableRTL:"boolean",minValue:"number",maxValue:"number",sliderType:"enum",incrementStep:"number",enabled:"boolean",showButtons:"boolean",showScale:"boolean",largeStep:"number",smallStep:"number",showSmallTicks:"boolean",enablePersistence:"boolean",htmlAttributes:"data",allowMouseWheel:"boolean"},observables:["value","values"],value:t.util.valueFunction("value"),values:t.util.valueFunction("values"),enable:function(){this.model.enabled||(this.model.enabled=!0,this.wrapper&&this.wrapper.removeClass("e-disable"),this.element.removeClass("e-disable"),this.model.showButtons&&this.element.siblings(".e-sliderbtn").ejButton("model.enabled",this.model.enabled),this._wireEvents())},disable:function(){this.model.enabled&&(this.model.enabled=!1,this.wrapper&&this.wrapper.addClass("e-disable"),this.element.addClass("e-disable"),this.model.showButtons&&this.element.siblings(".e-sliderbtn").ejButton("model.enabled",this.model.enabled),this._unWireEvents())},_validateValue:function(n,t){t=typeof t=="undefined"?!1:t;n==null||n===""?n=this.model.minValue:typeof n=="string"&&(n=parseFloat(n));this._isNumber(n)?(this._hidden.val(n),this.value(n)):this._isNumber(this.value())||(this._hidden.val(this.model.minValue),this.value(this.model.minValue));this.model.sliderType!="range"&&this._setValue(t)},_validateRangeValue:function(n,t){if(t=typeof t=="undefined"?!1:t,n==null)n=[this.model.minValue,this.model.maxValue];else if(typeof n=="string"){var i=n.split(",");i.length>1&&(n=[parseFloat(i[0]),parseFloat(i[1])])}typeof n=="object"&&this._isNumber(n[0])&&this._isNumber(n[1])?(this._hidden.val([n[0],n[1]]),this.values([n[0],n[1]])):typeof this.values()=="object"&&this._isNumber(this.values()[0])&&this._isNumber(this.values()[1])||(this._hidden.val([this.model.minValue,this.model.maxValue]),this.values([this.model.minValue,this.model.maxValue]));this.model.sliderType=="range"&&this._setRangeValue(t)},_validateStartEnd:function(){isNaN(this.model.minValue)&&(this.model.minValue=0);isNaN(this.model.maxValue)&&(this.model.maxValue=100)},_isNumber:function(n){return typeof n=="number"&&!isNaN(n)},_outerCorner:function(n){n?this._roundedCorner():this._sharpedCorner()},_changeSkin:function(n){this.element.removeClass(this.model.cssClass).addClass(n);this.model.showScale&&this.ul.removeClass(this.model.cssClass).addClass(n)},getValue:function(){return this._getHandleValue()},setValue:function(n,t){this._isInteraction=!1;this.model.sliderType=="range"?this._validateRangeValue(n,t):this._validateValue(n,t);this._isInteraction=!0},_getTransition:function(){var t=document.body||document.documentElement,n=t.style;return n.transition!==i||n.WebkitTransition!==i||n.MozTransition!==i||n.MsTransition!==i||n.OTransition!==i},_init:function(){this._isInteraction=!0;this._isTransition=this._getTransition();this._initialize();this._render()},_setModel:function(n){var r,u,f,e;this._isInteraction=!1;t.isNullOrUndefined(n.minValue)&&t.isNullOrUndefined(n.maxValue)||(this._isNumber(n.minValue)?this.model.minValue=n.minValue:n.minValue=this.model.minValue,this._isNumber(n.maxValue)?this.model.maxValue=n.maxValue:n.maxValue=this.model.maxValue,this.model.sliderType=="range"&&n.values==i?this._setRangeValue():this.model.sliderType!="range"&&n.value==i&&this._setValue());for(r in n)switch(r){case"value":this._validateValue(t.util.getVal(n[r]));n[r]=this.model.value;break;case"values":if(u=typeof n.values=="function"?n.values():n.values,!t.isNullOrUndefined(u)&&!t.isNullOrUndefined(u.length)&&typeof u!="string"&&!isNaN(u[0])&&!isNaN(u[1])&&(f=typeof this.values().join=="function"?u.join():u,f==this._hidden.val()))break;this._validateRangeValue(t.util.getVal(n[r]));n[r]=this.model.values;break;case"height":this.model.height=n[r];this._setDimension();this.model.showScale&&this._scaleAlignment();break;case"width":this.model.width=n[r];this._setDimension();this.model.showScale&&this._scaleAlignment();break;case"enabled":this._disabled(!n[r]);break;case"showRoundedCorner":this._outerCorner(n[r]);break;case"enableRTL":this.model.enableRTL=n[r];this.model.showButtons&&(this._valueChanged=!0);this._checkRTL();n[r]=this.model.enableRTL;break;case"cssClass":this._changeSkin(n[r]);this.model.showButtons&&this.element.siblings(".e-sliderbtn").ejButton("model.cssClass",n[r]);break;case"showScale":this._renderScale(n[r]);this.model.enableRTL&&this._changeVerticalScaleDir(n[r]);break;case"orientation":e=this.model.height;this.model.height=this.model.width;this.model.width=e;case"sliderType":this._sliderOptions(r,n[r]);break;case"smallStep":case"largeStep":case"showSmallTicks":case"minValue":case"maxValue":this._scaleOptions(r,n[r]);break;case"htmlAttributes":this._addAttr(n[r]);break;case"tooltipChange":this.model.tooltipChange=n[r];break;case"allowMouseWheel":this.model.allowMouseWheel=n[r];break;case"renderingTicks":this.model.renderingTicks=n[r];break;case"showButtons":this.model.showButtons=n[r];this._renderButtons()}this._isInteraction=!0},_destroy:function(){this.model.showScale&&this._destroyScale();this.element.insertAfter(this.wrapper);this.wrapper.remove();this.element.removeClass("e-widget e-box e-corner "+this.model.cssClass).empty();this.model.showButtons&&this.element.removeAttr("style")},_initialize:function(){this.target=this.element[0];this.horDir="left";this.verDir="bottom";this._isFocused=!1},_render:function(){this.initialRender=!0;this._isIE8=t.browserInfo().name=="msie"&&t.browserInfo().version=="8.0"?!0:!1;this.wrapper=t.buildTag("div.e-slider-wrap e-widget"+this.model.cssClass+"#"+this.target.id+"_wrapper",{tabindex:"0",role:"slider"}).insertAfter(this.element);this.model.showButtons?this._showButtons():this.wrapper.append(this.element);this.element.addClass("e-widget e-box "+this.model.cssClass);this.model.sliderType!="default"&&(this.header=t.buildTag("div.e-range"),this.element.append(this.header),this.model.sliderType=="range"&&(this.secondHandle=this._createHandle()));this.firstHandle=this._createHandle();this._setOrientation();this._setDimension();this._insertHiddenField();this._checkProperties();this.model.showScale||this._alignButtons();this._addAttr(this.model.htmlAttributes);this._setSliderValue()},_showButtons:function(){var i=this,r=t.buildTag("button.e-decreasebtn e-sliderbtn e-animate"),u=t.buildTag("button.e-increasebtn e-sliderbtn e-animate");r.ejButton({contentType:"imageonly",prefixIcon:"e-icon e-minus",type:"button",repeatButton:!0,enabled:i.model.enabled,cssClass:i.model.cssClass,click:function(n){i._clickButtons(n)}});u.ejButton({contentType:"imageonly",prefixIcon:"e-icon e-plus",type:"button",repeatButton:!0,enabled:i.model.enabled,cssClass:i.model.cssClass,click:function(n){i._clickButtons(n)}});this.model.enableRTL&&this.model.orientation=="horizontal"||this.model.orientation=="vertical"&&!this.model.enableRTL?this.wrapper.append(n(u)).append(this.element).append(n(r)).addClass("e-slider-buttons"):this.wrapper.append(n(r)).append(this.element).append(n(u)).addClass("e-slider-buttons");this.wrapper.find(".e-sliderbtn").attr("tabindex",-1)},_renderButtons:function(){this.model.showButtons?(this._showButtons(),this.model.showScale||this._alignButtons()):(this.element.siblings(".e-sliderbtn").remove(),this.element.removeAttr("style"),this.wrapper.removeClass("e-slider-buttons"));this.wrapper.find("ul").hasClass("e-scale")&&(this._destroyScale(),this._renderScale(!0))},_alignButtons:function(){if(this.model.showButtons){var n=this.wrapper.find(".e-sliderbtn");this.model.orientation=="horizontal"?n.css("top",this.element.outerHeight()/2-parseFloat(n.outerHeight()/2)+parseFloat(this.wrapper.css("padding-top"))+"px"):n.css("left",this.element.outerWidth()/2-parseFloat(n.outerWidth()/2)+parseFloat(this.wrapper.css("padding-left"))+"px")}},_clickButtons:function(i){if(n(i.target).hasClass("e-animate")&&n(i.target).removeClass("e-animate"),!this.model.readOnly&&!t.isNullOrUndefined(i.target)){var u,r;this.model.sliderType=="range"?(n(this.element).find(".e-handle.e-focus").is(this.firstHandle)&&!this.model.enableRTL?(this.firstHandle.focus().addClass("e-no-tab"),r=this.handleVal):this.model.enableRTL||(this.secondHandle.focus().addClass("e-no-tab"),r=this.handleVal2),n(this.element).find(".e-handle.e-focus").is(this.secondHandle)&&this.model.enableRTL?(this.secondHandle.focus().addClass("e-no-tab"),r=this.handleVal2):this.model.enableRTL&&(this.firstHandle.focus().addClass("e-no-tab"),r=this.handleVal)):(this.firstHandle.focus().addClass("e-no-tab"),r=this.handleVal);u=n(i.target).hasClass("e-decreasebtn")?this._add(r,this.model.incrementStep,!1):this._add(r,this.model.incrementStep,!0);this._changeHandleValue(u,!1)}},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.wrapper.addClass(n):t=="disabled"&&n=="disabled"?i._disabled(!0):i.element.attr(t,n)})},_renderScale:function(n){var e,o,s,i,h,l,a,v;if(n){this.wrapper.addClass("e-scale-wrap");e="width";o="h";this.model.orientation=="vertical"&&(e="height",o="v");i=this.model.smallStep;this.model.showSmallTicks?i<=0&&(i=this.model.incrementStep):i=this.model.largeStep>0?this.model.largeStep:this.model.maxValue-this.model.minValue;h=Math.abs(this.model.maxValue-this.model.minValue)/i;this.ul=t.buildTag("ul.e-scale e-"+o+"-scale "+this.model.cssClass);this._isIE8&&this.ul.addClass("e-ie8");this.wrapper.append(this.ul);var u,f,r=this.model.minValue,y=0,c=100/h;for(o=="v"&&(r=this.model.maxValue),l=0;l<=h;l++)u=t.buildTag("li.e-tick","",{},{title:r}),a=r%this.model.largeStep==0?!0:!1,a&&u.addClass("e-large"),u.css(e,c+"%"),this.model.renderingTicks&&(f={value:r,valueType:"tooltip",tick:u[0]},this._trigger("renderingTicks",f),u.attr("title",f.value)),a&&(this.model.renderingTicks?(f.valueType="label",f.value=r,this._trigger("renderingTicks",f),s=f.value):s=r,v=t.buildTag("span.e-tick-value",""+s),u.append(v)),this.ul.append(u),o=="h"?r+=i:r-=i,y+=i;this.ul.children().first().addClass("e-first-tick").css(e,c/2+"%");this.ul.children().last().addClass("e-last-tick").css(e,c/2+"%");this._scaleAlignment()}else this._destroyScale();this._setWrapperHeight();this._alignButtons()},_destroyScale:function(){this.wrapper.removeClass("e-scale-wrap");this.ul.remove();this.ul=null},_tickValuePosition:function(){var n=this.model.orientation=="vertical"?"height":"width",t=this.model.orientation=="vertical"?"top":"left",i=this.ul.find(".e-tick.e-first-tick"),r=i.find(".e-tick-value"),u=this.ul.find(".e-tick.e-large:not(.e-first-tick)").find(".e-tick-value"),f=i[n]()*2;r.css(t,-r[n]()/2);u.css(t,(f-u[n]())/2)},_scaleAlignment:function(){var u;this._tickValuePosition();var f=12,t=20,e=t/2,n="height",i="top",r="h";this.model.orientation=="vertical"?(n="width",i="right",r="v",this.element.width()<=15?this.wrapper.addClass("e-small-size"):this.wrapper.removeClass("e-small-size")):this.element.height()<=15?this.wrapper.addClass("e-small-size"):this.wrapper.removeClass("e-small-size");this.ul.css(i,-(this.wrapper[n]()+e));r=="v"&&this.ul.css("top",-this.wrapper.height()).css(i,e);this.ul[n](this.wrapper[n]()+t);u=-(t-f)/2;this.model.largeStep==null&&r!="v"&&(u=-u);this.ul.find(".e-tick:not(.e-large)").css(n,this.wrapper[n]()+f).css(i,u);r=="v"&&this.ul.children(".e-large").find(".e-tick-value").css("right",this.wrapper.width()+t+4)},_setWrapperHeight:function(){var i,r,n,t;this.model.orientation=="horizontal"?(i=this.ul?this.firstHandle.outerHeight()>this.ul.height()?this.firstHandle.outerHeight():this.ul.height():this.firstHandle.outerHeight(),n=(i-this.element.outerHeight())/2,n<0&&(n=0),this.wrapper.css({padding:n+"px 0px"})):(r=this.ul?this.firstHandle.outerWidth()>this.ul.width()?this.firstHandle.outerWidth():this.ul.width():this.firstHandle.outerWidth(),t=(r-this.element.outerWidth())/2,t<0&&(t=0),this.wrapper.css({padding:"0px "+t+"px"}))},_createHandle:function(){var n=t.buildTag("a.e-handle e-select","",{},{"aria-label":"drag",tabindex:0});return n.attr({role:"slider","aria-valuemin":this.model.minValue,"aria-valuemax":this.model.maxValue}),t.browserInfo().name=="msie"&&n.addClass("e-pinch"),this.element.append(n),n},_setDimension:function(){this.model.height&&this.wrapper.height(this.model.height);this.model.width&&this.wrapper.width(this.model.width);this._setHandleSize();this._handleAlignment(this.model.enableRTL);this._alignButtons()},_insertHiddenField:function(){this._hidden=t.buildTag("input","",{},{type:"hidden",name:this.element[0].id});this._hidden.val(this._getHandleValue());this.element.append(this._hidden)},_checkProperties:function(){this.model.enabled?this._wireEvents():this.wrapper?this.wrapper.addClass("e-disable"):this.element.addClass("e-disable");this.model.showScale?this._renderScale(!0):this._setWrapperHeight();this.model.enableRTL&&this._checkRTL();this.model.showRoundedCorner&&this._roundedCorner()},_roundedCorner:function(){this.element.addClass("e-corner")},_sharpedCorner:function(){this.element.removeClass("e-corner")},_handleAlignment:function(n){var t=-(this.firstHandle.outerWidth()/2)+"px",i;i=this.model.orientation!="vertical"?n?"0 "+t+" 0 0":"0 0 0 "+t:n?t+" 0 0 0":"0 0 "+t+" 0";this.element.children(".e-handle").css("margin",i)},_checkRTL:function(){var i,n,t;this.model.showButtons&&this._valueChanged&&(this.element.siblings(".e-sliderbtn").remove(),this._renderButtons());i=this.model.enableRTL;n=this.model.orientation!="vertical"?this.horDir:this.verDir;i?(this.wrapper.addClass("e-rtl"),this.model.orientation=="vertical"&&this.wrapper.addClass("e-top-to-bottom"),this.horDir="right",this.verDir="top"):(this.wrapper.removeClass("e-rtl e-top-to-bottom"),this.horDir="left",this.verDir="bottom");(!this.model.showButtons||this.model.showButtons&&this.model.enableRTL)&&this._changeVerticalScaleDir(this.model.showScale);t=this.model.orientation!="vertical"?this.horDir:this.verDir;n!=t&&(this.firstHandle.css(t,this.firstHandle[0].style[n]).css(n,"auto"),this.model.sliderType!="default"&&(this.header.css(t,this.header[0].style[n]).css(n,"auto"),this.model.sliderType=="range"&&this.secondHandle.css(t,this.secondHandle[0].style[n]).css(n,"auto")));this._handleAlignment(i)},_setOrientation:function(){this.model.orientation!="vertical"?this.wrapper.addClass("e-horizontal"):(this.wrapper.addClass("e-vertical"),this.firstHandle.css(this.verDir,"0"))},_changeVerticalScaleDir:function(n){var t,i;n&&(t=this.wrapper.find(".e-v-scale li"),t.length>0&&(i=t.toArray().reverse(),t.remove(),this.wrapper.find(".e-v-scale").append(i)))},_setHandleSize:function(){if(this.model.height!=null&&this.model.orientation=="horizontal"||this.model.width!=null&&this.model.orientation=="vertical"){var n;n=this.model.orientation!="vertical"?this.wrapper.height()+2:this.wrapper.width()+2;this.element.find(".e-handle").height(n).width(n)}else this.wrapper.addClass("e-default-wrap"),this.element.find(".e-handle").addClass("e-default")},_disabled:function(n){n?this.disable():this.enable()},_sliderOptions:function(n,t){this._unWireEvents();this._destroy();this.model[n]=t;this._init()},_scaleOptions:function(n,t){this.model.showScale&&(this._destroyScale(),this.model[n]=t,this._renderScale(!0),this.model.enableRTL&&this._changeVerticalScaleDir(!0))},_showTooltip:function(){if(this.model.showTooltip){this._timeOut&&clearTimeout(this._timeOut);var i=this.tooltip?n("body .e-tooltipbox").text().replace(/\s+/g,"").replace("-",","):"";if(this.tooltip&&this.tooltip.length&&this.tooltip.css("display")!="none"&&this._getHandle()[0]==this._oldHandle&&i==this.preValue){this._getHandleValue().toString()!=this.preValue&&this._setTooltipPosition();return}this._oldHandle=this._getHandle()[0];n("body .e-tooltipbox").remove();this.tooltip=t.buildTag("div.e-tooltipbox "+this.model.cssClass+" e-corner",{role:"tooltip"}).css(this._getOffset(this._getHandle()));n("body").append(this.tooltip);this.model.orientation=="vertical"&&this.tooltip.addClass("e-vertical");this._setTooltipPosition()}},_hideTooltip:function(){if(this.model.showTooltip){var n=this;this._timeOut=setTimeout(function(){n.tooltip.fadeOut(800)},1500)}},_showhideTooltip:function(n){this.model.showTooltip&&n&&(this._showTooltip(),this._timeOut&&clearTimeout(this._timeOut),this._hideTooltip())},_setTooltipPosition:function(){var i,r,s,e,u,t,o,l,f,h,c,a;this.model.showTooltip&&(this._updateTooltipValue(),o=5,u=this._getHandle(),t=this._getOffset(u),l=this._getOffset(this.tooltip),f=n(u).outerHeight()-n(u).height(),this.model.orientation=="vertical"?(e=(this.tooltip.outerHeight()-u.outerHeight())/2,s=u.outerWidth()+o,i=t.top-e,r=t.left+s,h=n(window).height(),window.pageYOffset>0&&(h+=window.pageYOffset),i<0?i=0:h<i+this.tooltip.outerHeight()&&(i=h-this.tooltip.outerHeight()-f),n(window).width()<r+this.tooltip.outerWidth()&&(r=t.left-this.tooltip.outerWidth()-f)):(l.left+this.tooltip.outerWidth()>n(window).width()&&this.tooltip.css({left:"0px"}),s=(this.tooltip.outerWidth()-u.outerWidth())/2,e=this.tooltip.outerHeight()+o,i=t.top-e,r=t.left-s,c=n(window).width(),window.pageXOffset>0&&(c+=window.pageXOffset),r<0?r=0:c<r+this.tooltip.outerWidth()&&(r=c-this.tooltip.outerWidth()-f),(i<0||t.top<e)&&(t.top+u.outerHeight()+f+this.tooltip.outerHeight()>n(window).height()?(i=t.top,r=t.left>this.tooltip.outerWidth()+o+f?t.left-(this.tooltip.outerWidth()+f):t.left+(u.outerWidth()+o+f)):i=t.top+u.outerHeight()+f)),a=this._maxZindex(),this.tooltip.css({top:i,left:r,zIndex:a+1}))},_getOffset:function(n){return t.util.getOffset(n)},_maxZindex:function(){return t.util.getZindexPartial(this.element,this.popup)},_updateTooltipValue:function(){var t=0,i=1,n;this.model.enableRTL&&(t=1,i=0);n=this.model.tooltipChange?this._raiseEvent("tooltipChange"):this._getHandleValue();this.tooltip[0].innerHTML=this.model.sliderType!="range"?n:n[t]+" - "+n[i]},_increaseHeaderWidth:function(n){var u;if(this.model.sliderType!="default"){var i="width",r=this.horDir,t={};this.model.orientation=="vertical"&&(i="height",r=this.verDir);this.model.sliderType=="range"?(t[i]=this.handlePos-this.handlePos2+"%",t[r]=this.handlePos2+"%"):(t[i]=this.handlePos+"%",t[r]=0);u=this;n?this._isTransition?(this.header[0].style.transition="all "+this.model.animationSpeed+"ms",this.header[0].style["-webkit-transition"]="all "+this.model.animationSpeed+"ms",this.header.css(r,t[r]),this.header.css(i,t[i]),setTimeout(function(){u.header[0].style.transition="none";u.header[0].style["-webkit-transition"]="none"},this.model.animationSpeed)):this.header.animate(t,this.model.animationSpeed):this.header.css(t)}},_setSliderValue:function(){this._validateStartEnd();this.model.sliderType=="range"?this._validateRangeValue(this.values()):this._validateValue(this.value());this.preValue=this.getValue().toString()},_hoverOnHandle:function(t){n(t.currentTarget).addClass("e-hover")},_leaveFromHandle:function(t){n(t.currentTarget).removeClass("e-hover")},_firstHandleClick:function(i){if(i.preventDefault(),this.firstHandle.focus().addClass("e-no-tab"),this._raiseEvent("start"))return!1;this.mouseDownPos=this.handlePos;this.model.readOnly||this._on(n(document),t.eventType.mouseMove,this._firstHandleMove);this._on(n(document),t.eventType.mouseUp,this._firstHandleUp);this._on(n(document),"mouseleave",this._firstHandleUp);this._showTooltip()},_firstHandleMove:function(n){n.preventDefault();n=n.type=="touchmove"?n.originalEvent.changedTouches[0]:n;var t={x:n.pageX,y:n.pageY};this.handlePos=this._xyToPosition(t);this.model.sliderType=="range"&&this.handlePos<this.handlePos2&&(this.handlePos=this.handlePos2);this.handlePos!=this.preHandlePos&&(this.preHandlePos=this.handlePos,this.handleVal=this._positionToValue(this.handlePos),this._increaseHeaderWidth(!1),this._setHandlePosition(!1,!1,!1),this._setTooltipPosition(),this._updateModelValue(),this._raiseEvent("slide"))},_firstHandleUp:function(i){i.preventDefault();this._off(n(document),t.eventType.mouseMove,this._firstHandleMove);this._off(n(document),t.eventType.mouseUp,this._firstHandleUp);this._off(n(document),"mouseleave",this._firstHandleUp);this._timeOut&&clearTimeout(this._timeOut);this._hideTooltip();this.mouseDownPos!=this.handlePos&&this._raiseChangeEvent()},_secondHandleClick:function(i){if(i.preventDefault(),this.secondHandle.focus().addClass("e-no-tab"),this._raiseEvent("start"))return!1;this.mouseDownPos2=this.handlePos2;this.model.readOnly||this._on(n(document),t.eventType.mouseMove,this._secondHandleMove);this._on(n(document),t.eventType.mouseUp,this._secondHandleUp);this._on(n(document),"mouseleave",this._secondHandleUp);this._showTooltip()},_secondHandleMove:function(n){n.preventDefault();n=n.type=="touchmove"?n.originalEvent.changedTouches[0]:n;var t={x:n.pageX,y:n.pageY};this.handlePos2=this._xyToPosition(t);this.handlePos2>this.handlePos&&(this.handlePos2=this.handlePos);this.handlePos2!=this.preHandlePos2&&(this.preHandlePos2=this.handlePos2,this.handleVal2=this._positionToValue(this.handlePos2),this._increaseHeaderWidth(!1),this._setHandlePosition(!1,!1,!1),this._setTooltipPosition(),this._updateModelValue(),this._raiseEvent("slide"))},_secondHandleUp:function(i){i.preventDefault();this._off(n(document),t.eventType.mouseMove,this._secondHandleMove);this._off(n(document),t.eventType.mouseUp,this._secondHandleUp);this._off(n(document),"mouseleave",this._secondHandleUp);this._timeOut&&clearTimeout(this._timeOut);this._hideTooltip();this.mouseDownPos2!=this.handlePos2&&this._raiseChangeEvent()},_focusInHandle:function(t){this._isFocused||(this._isFocused=!0,n(t.currentTarget).addClass("e-focus"),this.model.readOnly||this._on(n(document),"keydown",this._moveHandle),this.model.allowMouseWheel&&!this.model.readOnly&&this._on(this.element,"mousewheel DOMMouseScroll",this._moveHandle),this.activeHandle=n(t.currentTarget).is(this.firstHandle)?1:2,this._setZindex())},_focusOutHandle:function(i){n(i.relatedTarget).is("button")&&n(i.target).parent().siblings().is(i.relatedTarget)||t.isNullOrUndefined(i.relatedTarget)&&!t.isNullOrUndefined(i.originalEvent)&&!t.isNullOrUndefined(i.originalEvent.toElement)&&n(i.originalEvent.toElement).is("button")&&n(i.target).parent().siblings().is(i.originalEvent.toElement)||(this._isFocused=!1,this.model.showTooltip&&this.tooltip&&this.tooltip.fadeOut(800),this.element.find(".e-no-tab").removeClass("e-no-tab"),n(i.currentTarget).removeClass("e-focus"),this._off(n(document),"keydown",this._moveHandle),this._off(this.element,"mousewheel DOMMouseScroll",this._moveHandle))},_moveHandle:function(n){var t,i,u,f,r,e,o;(n.type=="mousewheel"||n.type=="DOMMouseScroll")&&n.preventDefault();u=this._getHandleIndex(this.activeHandle)-1;(n.type=="mousewheel"||n.type=="DOMMouseScroll")&&(r=n.originalEvent,r.wheelDelta?f=r.wheelDelta/120:r.detail&&(f=-r.detail/3),t=f>0?"add":"sub");switch(n.keyCode||n.originalEvent.wheelDelta){case-120:case 37:case 40:this._getHandle().addClass("e-no-tab");n.preventDefault();t="sub";break;case 120:case 38:case 39:this._getHandle().addClass("e-no-tab");n.preventDefault();t="add";break;case 36:if(this._getHandle().addClass("e-no-tab"),n.preventDefault(),this._raiseEvent("start"))return!1;this.model.sliderType!="range"&&this.value()!=this.model.minValue?this._changeHandleValue(this.model.minValue,this.model.enableAnimation):this.model.sliderType=="range"&&(i=this.activeHandle==2?this.model.minValue:this.handleVal2,this.values()[u]!=i&&this._changeHandleValue(i,this.model.enableAnimation));break;case 35:if(this._getHandle().addClass("e-no-tab"),n.preventDefault(),this._raiseEvent("start"))return!1;this.model.sliderType!="range"&&this.value()!=this.model.maxValue?this._changeHandleValue(this.model.maxValue,this.model.enableAnimation):this.model.sliderType=="range"&&(i=this.activeHandle==1?this.model.maxValue:this.handleVal,this.values()[u]!=i&&this._changeHandleValue(i,this.model.enableAnimation));break;case 27:this._getHandle().addClass("e-no-tab");n.preventDefault();this._getHandle().focusout()}if(t=="add"||t=="sub"){if(this._raiseEvent("start"))return!1;e=this.activeHandle==1?this.handleVal:this.handleVal2;o=t=="add"?this._add(e,this.model.incrementStep,!0):this._add(e,this.model.incrementStep,!1);this._changeHandleValue(o,!1)}},_changeHandleValue:function(n,t){var i=null;this.activeHandle==1?(this.handleVal=this._checkHandleValue(n),this.handlePos=this._checkHandlePosition(this.handleVal),this.model.sliderType=="range"&&this.handlePos<this.handlePos2&&(this.handlePos=this.handlePos2,this.handleVal=this.handleVal2),this.handlePos!=this.preHandlePos&&(i=this.preHandlePos=this.handlePos)):(this.handleVal2=this._checkHandleValue(n),this.handlePos2=this._checkHandlePosition(this.handleVal2),this.model.sliderType=="range"&&this.handlePos<this.handlePos2&&(this.handlePos2=this.handlePos,this.handleVal2=this.handleVal),this.handlePos2!=this.preHandlePos2&&(i=this.preHandlePos2=this.handlePos2));i!=null&&(this._increaseHeaderWidth(t),this._setHandlePosition(t,!0,!0))},_sliderBarClick:function(t){if(this.model.readOnly)return!1;if(t.target==this.target||this.model.sliderType!="default"&&t.target==this.header[0]||n(t.target).hasClass("e-tick")||n(t.target).hasClass("e-scale")||t.target==this.wrapper[0]){if(t.preventDefault(),this._raiseEvent("start"))return!1;var u={x:t.pageX,y:t.pageY},i=this._xyToPosition(u),r=this._positionToValue(i);this.model.sliderType=="range"&&this.handlePos-i>i-this.handlePos2?(this.handlePos2=this.preHandlePos2=i,this.handleVal2=r,this.activeHandle=2):(this.handlePos=this.preHandlePos=i,this.handleVal=r,this.activeHandle=1);this._getHandle().focus().addClass("e-no-tab");this.model.sliderType!="default"&&this._increaseHeaderWidth(this.model.enableAnimation);this._setHandlePosition(this.model.enableAnimation,!0,!0)}},_setHandlePosition:function(n,t,i){var r=this._getHandle(),u=this,o={},f,e,s;f=this.activeHandle==1?this.handlePos:this.handlePos2;e=this.activeHandle==1?this.handleVal:this.handleVal2;r.attr("aria-label",e);r.attr("aria-valuenow",e);r.attr("aria-valuetext",e);s=this.model.orientation=="vertical"?this.verDir:this.horDir;o[s]=f+"%";f==0?this.model.sliderType!="range"&&this._getHandle().addClass("e-handle-start"):this._getHandle().removeClass("e-handle-start");n?this._isTransition?(r[0].style.transition="all "+this.model.animationSpeed+"ms",r[0].style["-webkit-transition"]="all "+this.model.animationSpeed+"ms",r.css(s,f+"%"),setTimeout(function(){r[0].style.transition="none";r[0].style["-webkit-transition"]="none";u._showhideTooltip(t);i&&u._raiseChangeEvent()},this.model.animationSpeed)):r.animate(o,this.model.animationSpeed,function(){u._showhideTooltip(t);i&&u._raiseChangeEvent()}):(r.css(o),this._showhideTooltip(t),i&&this._raiseChangeEvent())},_xyToPosition:function(n){if(this.model.minValue==this.model.maxValue)return 100;if(this.model.orientation!="vertical")var r=n.x-this.element.offset().left,i=this.element.width()/100,t=r/i;else var u=n.y-this.element.offset().top,i=this.element.height()/100,t=100-u/i;return(t=this._stepValueCalculation(t),t<0?t=0:t>100&&(t=100),this.model.enableRTL)?100-t:t},_updateValue:function(){this.handleVal=this._checkHandleValue(this.value());this.handlePos=this._checkHandlePosition(this.handleVal);this.preHandlePos=this.handlePos;this.activeHandle=1},_setValue:function(n){this._updateValue();this._increaseHeaderWidth(n);this._setHandlePosition(n,!1,!0)},_updateRangeValue:function(){var n=this.values();this.handleVal=this._checkHandleValue(n[1]);this.handleVal2=this._checkHandleValue(n[0]);this.handlePos=this._checkHandlePosition(this.handleVal);this.handlePos2=this._checkHandlePosition(this.handleVal2);this.handlePos<this.handlePos2&&(this.handlePos=this.handlePos2,this.handleVal=this.handleVal2);this.preHandlePos=this.handlePos;this.preHandlePos2=this.handlePos2},_setRangeValue:function(n){this._updateRangeValue();this._increaseHeaderWidth(n);this.activeHandle=1;this._setHandlePosition(n,!1,!1);this.activeHandle=2;this._setHandlePosition(n,!1,!0)},_checkHandlePosition:function(n){if(this.model.minValue==this.model.maxValue)return 100;var t=this._tempStartEnd();return n>=t.start&&n<=t.end?100*(n-this.model.minValue)/(this.model.maxValue-this.model.minValue):n<t.start?0:100},_checkHandleValue:function(n){if(this.model.minValue==this.model.maxValue)return this.model.minValue;var t=this._tempStartEnd();return n<t.start?n=t.start:n>t.end&&(n=t.end),n},_tempStartEnd:function(){return this.model.minValue>this.model.maxValue?{start:this.model.maxValue,end:this.model.minValue}:{start:this.model.minValue,end:this.model.maxValue}},_positionToValue:function(n){var t=this.model.maxValue-this.model.minValue,i=this._round(t*n/100);return this._add(i,this.model.minValue,!0)},_getHandle:function(){return this.activeHandle==1?this.firstHandle:this.secondHandle},_getHandleIndex:function(n){return this.model.sliderType=="range"&&n==1?2:1},_getHandleValue:function(){return this.model.sliderType=="range"?[this.handleVal2,this.handleVal]:this.handleVal},_updateModelValue:function(){var n=this._getHandleValue();this._hidden.val(n);this.model.sliderType=="range"?this.values(n):this.value(n)},_add:function(n,t,i,r){var u=Math.pow(10,r||3);return i?(Math.round(n*u)+Math.round(t*u))/u:(Math.round(n*u)-Math.round(t*u))/u},_round:function(n){var t=this.model.incrementStep.toString().split(".");return t[1]?parseFloat(n.toFixed(t[1].length)):Math.round(n)},_raiseChangeEvent:function(){this._updateModelValue();this.initialRender?this.initialRender=!1:this.getValue().toString()!=this.preValue.toString()&&(this._raiseEvent("change"),this._raiseEvent("stop"),this.preValue=this.getValue().toString())},_raiseEvent:function(n){var t={id:this.target.id,value:this._getHandleValue(),sliderIndex:this._getHandleIndex(this.activeHandle)},i;return(n=="change"&&(t={id:this.target.id,isInteraction:this._isInteraction,value:this._getHandleValue(),sliderIndex:this._getHandleIndex(this.activeHandle)}),n=="tooltipChange"&&(t={id:this.target.id,isInteraction:this._isInteraction,value:this._getHandleValue(),sliderIndex:this._getHandleIndex(this.activeHandle)}),i=this._trigger(n,t),n=="tooltipChange")?t.value:i},_setZindex:function(){this.model.sliderType=="range"&&(this.activeHandle==1?(this.firstHandle.css("z-index",2),this.secondHandle.css("z-index",1)):(this.firstHandle.css("z-index",1),this.secondHandle.css("z-index",2)))},_stepValueCalculation:function(n){this.model.incrementStep==0&&(this.model.incrementStep=1);var i=this.model.incrementStep/((this.model.maxValue-this.model.minValue)/100),t=n%Math.abs(i);return t!=0&&(i/2>t?n-=t:n+=Math.abs(i)-t),n},_wireEvents:function(){this._on(this.wrapper,"mousedown",this._sliderBarClick);this._on(this.firstHandle,t.eventType.mouseDown,this._firstHandleClick);this._on(this.firstHandle,"mouseenter",this._hoverOnHandle);this._on(this.firstHandle,"mouseleave",this._leaveFromHandle);this._on(this.firstHandle,"focusin",this._focusInHandle);this._on(this.firstHandle,"focusout",this._focusOutHandle);this.model.sliderType=="range"&&(this._on(this.secondHandle,t.eventType.mouseDown,this._secondHandleClick),this._on(this.secondHandle,"mouseenter",this._hoverOnHandle),this._on(this.secondHandle,"mouseleave",this._leaveFromHandle),this._on(this.secondHandle,"focusin",this._focusInHandle),this._on(this.secondHandle,"focusout",this._focusOutHandle))},_unWireEvents:function(){this._off(this.wrapper,"mousedown");this._off(this.firstHandle,t.eventType.mouseDown);this._off(this.firstHandle,"mouseenter");this._off(this.firstHandle,"mouseleave");this._off(this.firstHandle,"focusin");this._off(this.firstHandle,"focusout");this.model.sliderType=="range"&&(this._off(this.secondHandle,t.eventType.mouseDown),this._off(this.secondHandle,"mouseenter"),this._off(this.secondHandle,"mouseleave"),this._off(this.secondHandle,"focusin"),this._off(this.secondHandle,"focusout"))}});t.SliderType={Default:"default",MinRange:"minrange",Range:"range"}})(jQuery,Syncfusion)});
