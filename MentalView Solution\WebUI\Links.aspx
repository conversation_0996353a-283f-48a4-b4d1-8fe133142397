﻿<%@ Page Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Links.aspx.cs" Inherits="WebUI.Links" Culture="auto" meta:resourcekey="Page" UICulture="auto" %>

<asp:Content ID="mainHeadContent" ContentPlaceHolderID="mainHead" runat="server">
    <script src="LocalizedResources/ej.culture.en-US.min.js"></script>
    <script type="text/javascript">
        function Initialization() {
            SetLocalization();

            $('#filterTxtBox').keydown(function (e) {
                if (e.keyCode == 13) {
                    e.preventDefault();
                    javascript: __doPostBack('searchBtn', '');
                }
            });
        }
    </script>

    <script type="text/x-template" id="activeColumnTemplate">
        {{if Active}}
            <i class="fa fa-check"/>
        {{else}}
            
        {{/if}}
    </script>

    <script type="text/x-template" id="waitingColumnTemplate">
        {{if Waiting}}
            <i class="fa fa-check"/>
        {{else}}
            
        {{/if}}
    </script>
</asp:Content>
<asp:Content ID="mainBodyContent" ContentPlaceHolderID="mainBody" runat="server">
    <%--<div class="row">
        <div class="col-xs-12">
            <a id="newContactBtn" href="Contact.aspx" runat="server" type="button" class="btn btn-primary btn-flat margin-bottom margin-r-5">
                <asp:Literal meta:resourcekey="newContactBtn" runat="server"></asp:Literal></a>
        </div>
    </div>--%>
    <%--    <asp:UpdatePanel ID="contactsUpdatePanel" runat="server">
        <ContentTemplate>--%>
    <%-- <div class="row">
        <div class="col-xs-12 ">
            <div class="input-group input-group-sm margin-bottom pull-right" style="min-width: 200px; max-width: 250px;">
                <input type="text" runat="server" id="filterTxtBox" class="form-control" maxlength="30">
                <span class="input-group-btn">
                    <button type="button" id="clearSearchBtn" runat="server" class="btn btn-default btn-flat" style="border-radius: unset !important" onserverclick="clearSearchBtn_ServerClick">
                        <i class="fa fa-remove"></i>
                    </button>
                </span>
                <span class="input-group-btn">
                    <button type="button" id="searchBtn" runat="server" class="btn btn-info btn-flat" onserverclick="searchBtn_ServerClick">
                        <i class="fa fa-search  hidden-md hidden-lg"></i>
                        <asp:Label meta:resourcekey="searchBtn" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></button>
                </span>
            </div>
        </div>
    </div>--%>
    <div class="row">
        <div class="col-xs-12">
            <asp:UpdatePanel ID="UpdatePanel1" runat="server" ChildrenAsTriggers="true">
                <ContentTemplate>
                    <ej:Grid ID="linksGrid" runat="server" Selectiontype="Single" AllowFiltering="true" AllowSorting="true" EnablePersistence="false" Locale="el-GR" OnServerAddRow="linksGrid_ServerAddRow" OnServerEditRow="linksGrid_ServerEditRow" OnServerDeleteRow="linksGrid_ServerDeleteRow">
                        <EditSettings AllowEditing="true" AllowAdding="true" AllowDeleting="true" EditMode="Normal"></EditSettings>
                        <ToolbarSettings ShowToolbar="true" ToolbarItems="add,edit,delete,update,cancel"></ToolbarSettings>
                        <SortedColumns><ej:SortedColumn Field="CreateDate" Direction="Descending" /></SortedColumns>
                        <Columns>
                            <ej:Column Field="LinkId" IsPrimaryKey="true" Type="string" Visible="false" />
                            <ej:Column Field="CreateDate" HeaderText="Ημ/νία Δημιουργίας" Type="datetime" AllowFiltering="false" AllowEditing="false" EditType="DateTimePicker" Width="130"/>
                            <ej:Column Field="Description" HeaderText="Περιγραφή" Type="string" EditType="StringEdit" />
                            <ej:Column Field="Link" HeaderText="Σύνδεσμος" Type="string" EditType="StringEdit" />
                            <ej:Column HeaderText="" Width="80" TextAlign="Center">
                                <Command>
                                    <ej:Commands Type="">
                                        <ButtonOptions Text="..." Width="40" Click="linksGridOnClick"></ButtonOptions>
                                    </ej:Commands>
                                </Command>
                            </ej:Column>
                        </Columns>
                    </ej:Grid>
                </ContentTemplate>
            </asp:UpdatePanel>
        </div>
    </div>

    <script type="text/javascript">
        function linksGridOnClick(args) {
            var grid = $("#linksGrid").ejGrid("instance");
            var index = this.element.closest("tr").index();
            var record = grid.getCurrentViewData()[index];
            //alert("Record Details: " + JSON.stringify(record));
            
            // Open link in new window
            if (record && record.Link) {
                var url = record.Link;
                // Check if URL starts with http:// or https://, if not add https://
                if (!url.match(/^https?:\/\//i)) {
                    url = 'https://' + url;
                }
                window.open(url, '_blank', 'noopener,noreferrer');
            }
            return true;
        }
    </script>
</asp:Content>

