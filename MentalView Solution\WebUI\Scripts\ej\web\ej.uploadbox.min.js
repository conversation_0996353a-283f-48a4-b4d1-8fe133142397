/*!
*  filename: ej.uploadbox.min.js
*  version : *********
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./../common/ej.scroller.min","./ej.tooltip.min","./../common/ej.draggable.min","./ej.dialog.min"],n):n()})(function(){(function(n,t,i){t.widget("ejUploadbox","ej.Uploadbox",{element:null,_addToPersist:[""],model:null,validTags:["div","span"],_setFirst:!1,_rootCSS:"e-uploadbox",defaults:{buttonText:{browse:"Browse",upload:"Upload",cancel:"Cancel",close:"Close"},width:"100px",height:"35px",htmlAttributes:{},dialogPosition:{X:"",Y:""},dialogText:{title:"Upload Box",name:"Name",size:"Size",status:"Status"},customFileDetails:{title:!0,name:!0,size:!0,status:!0,action:!0},dialogAction:{modal:!1,closeOnComplete:!1,drag:!0,content:null},locale:"en-US",asyncUpload:!0,pushFile:null,enabled:!0,multipleFilesSelection:!0,autoUpload:!1,showFileDetails:!0,fileSize:31457280,extensionsAllow:"",extensionsDeny:"",saveUrl:"",removeUrl:"",uploadName:"",cssClass:"",dropAreaText:"Drop files or click to upload",allowDragAndDrop:!1,showBrowseButton:!0,showRoundedCorner:!0,dropAreaHeight:"100%",dropAreaWidth:"100%",enableRTL:!1,create:null,fileSelect:null,begin:null,beforeSend:null,beforeRemove:null,cancel:null,inProgress:null,success:null,complete:null,remove:null,error:null,destroy:null},dataTypes:{buttonText:"data",dialogText:"data",disbled:"boolean",customFileDetails:"data",pushFile:"data",dialogAction:"data",multipleFilesSelection:"boolean",autoUpload:"boolean",showFileDetails:"boolean",allowDragAndDrop:"boolean",showBrowseButton:"boolean",showRoundedCorner:"boolean",fileSize:"number",extensionsAllow:"string",extensionsDeny:"string",saveUrl:"string",removeUrl:"string",cssClass:"string",enableRTL:"boolean",htmlAttributes:"data"},disable:function(){this._changeState(!1)},enable:function(){this._changeState(!0)},_changeState:function(t){t?n(this.control).removeClass("e-disable"):n(this.control).addClass("e-disable");this.model.enabled=t;n(this._currentElement).find(".e-uploadinput")[0].disabled=!t;this.diaObj&&(this.diaObj.option("beforeClose",this._onBeforeClose),this.diaObj.setModel({enabled:t,allowDraggable:t}))},refresh:function(){this.diaObj&&(this.diaObj.isOpened()?this.diaObj.close():this._uploadFileListDelete())},_updateLocalConstant:function(){this._localizedLabels=t.getLocalizedConstants("ej.Uploadbox",this.model.locale)},_init:function(){this.s=t.browserInfo();this._selectedfiles=[];this._updateLocalConstant();this._initialize();this._wireEvents();this.model.asyncUpload||this._initObjectsSyncUpload();this._controlStatus(this.model.enabled);this.model.locale=="en-US"?this._buttonText(this.model.buttonText):this._setLocale();this._roundedCorner(this.model.showRoundedCorner)},_setModel:function(n){for(var t in n){switch(t){case"buttonText":this._buttonText(n[t]);break;case"htmlAttributes":this._addAttr(n[t]);break;case"dialogText":this._dialogText(n[t]);break;case"cssClass":this._setSkin(n[t]);break;case"enableRTL":this._setRTL(n[t]);break;case"enabled":this._controlStatus(n[t]);break;case"locale":this.model.locale=n[t];this._updateLocalConstant();this._setLocale();break;case"height":this.model.height=n[t];this._setHeight(this.model.height);break;case"width":this.model.width=n[t];this._setWidth(this.model.width);break;case"dialogPosition":this.model.dialogPosition=n[t];this._dialogPosition();break;case"allowDragAndDrop":this.model.allowDragAndDrop=n[t];this._dragAndDrop();break;case"dropAreaText":this.model.dropAreaText=n[t];this._dropAreaText(this.model.dropAreaText);break;case"showBrowseButton":this.model.showBrowseButton=n[t];this._hideBrowseButton();this._refreshUploadDialogParent();break;case"showRoundedCorner":this._roundedCorner(n[t]);break;case"dropAreaHeight":this.model.dropAreaHeight=n[t];this._setSize();break;case"dropAreaWidth":this.model.dropAreaWidth=n[t];this._setSize();break;case"pushFile":this.model.pushFile=n[t];this.model.pushFile!=null&&this.UploadType=="Xhr"&&this.model.asyncUpload&&(this._files=this.model.pushFile,this._selectedfiles=this._selectedfiles.concat(this.model.pushFile));this._onXhrSelect();break;case"multipleFilesSelection":this.model.multipleFilesSelection=n[t];(navigator.userAgent.indexOf("Safari")!=-1&&navigator.userAgent.indexOf("Chrome")==-1)==!1&&(this.model.multipleFilesSelection?this._currentElement.find(".e-uploadinput").attr("multiple","multiple"):this._currentElement.find(".e-uploadinput").removeAttr("multiple"));break;case"uploadName":this.model.uploadName=n[t];this.inputupload.attr("name",this.model.uploadName);this.refresh();break;case"autoUpload":this.model.autoUpload=n[t];break;case"showFileDetails":this.model.showFileDetails=n[t];this.refresh();break;case"fileSize":this.model.fileSize=n[t];break;case"extensionsAllow":this.model.extensionsAllow=n[t];this._currentElement.find(".e-uploadinput").attr("accept",n[t]);break;case"extensionsDeny":this.model.extensionsDeny=n[t];break;case"saveUrl":this.model.saveUrl=n[t];break;case"removeUrl":this.model.removeUrl=n[t]}this._currentElement=this.model.allowDragAndDrop&&!this.model.showBrowseButton?this.dragWrapper:this.element}},_controlStatus:function(n){n!=!0?this.disable():this.enable()},_setRTL:function(n){n?this._currentElement.addClass("e-rtl"):this._currentElement.removeClass("e-rtl");this.updialog&&this.updialog.ejDialog({enableRTL:n})},_getLocalizedLabels:function(n){var i,r="";return i=n=="browse"||n=="upload"||n=="cancel"||n=="close"?"buttonText":"dialogText",this._localizedLabels[n]?r=this._localizedLabels[n]:this._localizedLabels[i][n]?r=this._localizedLabels[i][n]:t.Uploadbox.Locale["en-US"][n]?r=t.Uploadbox.Locale["en-US"][n]:t.Uploadbox.Locale["en-US"][i][n]&&(r=t.Uploadbox.Locale["en-US"][i][n]),r},_setLocale:function(){this._buttonText(this._localizedLabels.buttonText);this._dialogText(this._localizedLabels.dialogText);this._dropAreaText(this._localizedLabels.dropAreaText)},_buttonText:function(t){n.extend(this.model.buttonText,t);this.buttondiv.val(this.model.buttonText.browse);this.updialog&&(this.updialog.find(".e-action-container .e-uploadbtn").html(this.model.buttonText.upload),this.updialog.find(".e-action-container .e-uploadclosebtn").html(this.model.buttonText.cancel))},_dialogText:function(t){n.extend(this.model.dialogText,t);this.diaObj==i||this.diaObj.option("title",this.model.dialogText.title);this.updialog&&(this.updialog.find(".e-head-name").html(this.model.dialogText.name),this.updialog.find(".e-head-size").html(this.model.dialogText.size),this.updialog.find(".e-head-status").html(this.model.dialogText.status))},_destroy:function(){this._currentElement.hasClass("e-uploadbox")&&(this._currentElement.removeClass("e-uploadbox e-widget"),this._currentElement.empty(),n(this.dragWrapper).after(this._currentElement),this.element.css({width:"",height:""}),this._bindResizeHandler(!1),this.dragWrapper.remove())},_setSkin:function(n){this._currentElement.removeClass(this.model.cssClass);this._currentElement.addClass(n);this.diaObj&&this.diaObj.setModel({cssClass:this.model.cssClass});this.model.allowDragAndDrop&&this.dragWrapper.addClass(n)},_initialize:function(){this.control=this.element[0];this.element.addClass("e-widget "+this.model.cssClass);this.innerdiv=t.buildTag("div.e-selectpart e-select e-box");this.element.append(this.innerdiv);this.buttondiv=t.buildTag("input.e-inputbtn e-btn#"+this.control.id+"_SelectButton","",{},{type:"button","data-role":"none",value:this._getLocalizedLabels("browse")});this.inputupload=t.buildTag("input.e-uploadinput","",{},{type:"file","data-role":"none",name:this.model.uploadName!=""?this.model.uploadName:this.control.id});this.model.extensionsAllow&&this.inputupload.attr("accept",this.model.extensionsAllow);this.model.multipleFilesSelection&&(navigator.userAgent.indexOf("Safari")!=-1&&navigator.userAgent.indexOf("Chrome")==-1)==!1&&this.inputupload.attr("multiple","multiple");this.innerdiv.append(this.inputupload);this.innerdiv.append(this.buttondiv);this.model.allowDragAndDrop&&this._dragAndDrop(!0);this._Selector=this.buttondiv[0];this._currentElement=this.model.allowDragAndDrop&&!this.model.showBrowseButton?this.dragWrapper:this.element;this._setRTL(this.model.enableRTL);this._addAttr(this.model.htmlAttributes);this.Uploadframes=[];this.UploadForms=[];this._successFiles=[];this._errorFiles=[];this.UploadType=this._isXhrSupported()?"Xhr":"IFrame";this.model.width!=""&&this._setWidth(this.model.width);this.model.height!=""&&this._setHeight(this.model.height);this._hideBrowseButton()},_refreshUploadDialogParent:function(){this.diaObj&&this.diaObj.setModel({target:this.model.dialogAction.content!=null&&this.model.dialogAction.content!=""?this.model.dialogAction.content:!this.model.showBrowseButton&&this.model.allowDragAndDrop?"#"+this.control.id+"_dragWrapper":"#"+this.control.id})},_hideBrowseButton:function(){!this.model.showBrowseButton&&this.model.allowDragAndDrop?(this.element.addClass("e-browse-hide"),this._dropAreaSize()):this.element.hasClass("e-browse-hide")&&(this.element.removeClass("e-browse-hide"),this.element.width(this.model.width),this.element.height(this.model.height))},_setSize:function(){this.model.allowDragAndDrop&&(this.model.dropAreaWidth!=""&&this.dragWrapper.outerWidth(this.model.dropAreaWidth),this.model.dropAreaHeight!=""&&this.dragWrapper.outerHeight(this.model.dropAreaHeight),this.model.showBrowseButton||this._dropAreaSize())},_dropAreaSize:function(){this.model.dropAreaWidth!=""&&this.element.width(this.model.dropAreaWidth);this.model.dropAreaHeight!=""&&this.element.height(this.model.dropAreaHeight)},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.element.addClass(n):t=="disabled"&&n=="disabled"?i.disable():i.element.attr(t,n)})},_dragAndDrop:function(){this.model.allowDragAndDrop?(this.dragWrapper=t.buildTag("div.e-drag-wrapper e-widget-wrapper e-upload-box "+this.model.cssClass+"#"+this.control.id+"_dragWrapper"),this.innerWrapper=t.buildTag("div.e-drag-container"),this._spanTag=t.buildTag("span.e-drag-text").html(this.model.locale=="en-US"?this.model.dropAreaText:t.Uploadbox.Locale[this.model.locale].dropAreaText),this.innerWrapper.append(this._spanTag),this.innerWrapper.insertBefore(this.element),this.dragWrapper.insertBefore(this.element),this.innerWrapper.append(this.element),this.dragWrapper.append(this.innerWrapper),this._bindDragEvents(),this._refreshUploadDialogParent(),this._setSize()):(this._refreshUploadDialogParent(),this.element.insertBefore(this.dragWrapper),this.dragWrapper.remove(),this._unBindDragEvents());this._hideBrowseButton()},_resizeHandler:function(){this.diaObj&&(n(window).width()>750?(n(this.updialog).closest(".e-dialog.e-uploadbox").removeClass("e-mobile-upload"),this.diaObj.setModel({width:this._getDialogContentWidth(),height:"auto"})):(n(this.updialog).closest(".e-dialog.e-uploadbox").addClass("e-mobile-upload"),this.diaObj.setModel({width:250,height:"auto"})),this.diaObj.scroller&&this.diaObj.scroller.setModel({height:"auto"}))},_getDialogContentWidth:function(){var n=0;return this.model.customFileDetails.name&&(n=n+200),this.model.customFileDetails.size&&(n=n+100),this.model.customFileDetails.status?n=n+200:this.model.customFileDetails.action&&(n=n+45),n+5},_dropAreaText:function(n){this.model.allowDragAndDrop&&this._spanTag.html(n)},_bindDragEvents:function(){this._on(this.dragWrapper,"dragover",this._onDragOverHandler);this._on(this.dragWrapper,"drop",this._onDropHandler)},_unBindDragEvents:function(){this._off(this.dragWrapper,"dragover",this._onDragOverHandler);this._off(this.dragWrapper,"drop",this._onDropHandler)},_setWidth:function(n){this.element.css("width",n)},_setHeight:function(n){this.element.height(n)},_dialogPosition:function(){var t,i;this.diaObj&&((this.diaObj.model.content=="#"+this.control.id||this.diaObj.model.content=="#"+this.control.id+"_dragWrapper")&&this.model.dialogPosition.X==""&&this.model.dialogPosition.Y==""&&(t=parseInt(this.diaObj.wrapper.css("left"))-(this.diaObj.wrapper.outerWidth()-n(this.diaObj.model.content).outerWidth())/2,this.model.dialogPosition.X=parseInt(t)>0?t:parseInt(this.diaObj.wrapper.css("left")),this.model.dialogPosition.Y=parseInt(this.diaObj.wrapper.css("top"))+n(this.diaObj.model.content).outerHeight()),this.model.dialogPosition.X!=""&&this.model.dialogPosition.Y!=""&&(this.model.allowDragAndDrop&&(i=this.model.showBrowseButton?n(this.diaObj.model.target).parent():n(this.diaObj.model.target).children(),this.model.dialogPosition.X=parseInt(this.model.dialogPosition.X)-i.offset().left,this.model.dialogPosition.Y=parseInt(this.model.dialogPosition.Y)-i.offset().top),this.diaObj.setModel({position:this.model.dialogPosition})))},_roundedCorner:function(n){n?this.innerdiv.addClass("e-corner"):this.innerdiv.hasClass("e-corner")&&this.innerdiv.removeClass("e-corner")},_wireEvents:function(){this._on(this.element,"click",this._disableclickselect);this._on(this.element,"keydown",this._keydownselect);this._bindInputChangeEvent();this._bindResizeHandler(!0)},_bindResizeHandler:function(t){t?n(window).on("resize",n.proxy(this._resizeHandler,this)):n(window).off("resize",n.proxy(this._resizeHandler,this))},_keydownselect:function(n){this.element.hasClass("e-disable")||n.keyCode!=13||this.diaObj&&this.diaObj.isOpened()||this.element.find(".e-uploadinput").click()},_onDropHandler:function(i){if(t.browserInfo().name==="msie"&&t.browserInfo().version==="8.0"||t.browserInfo().version==="9.0"||this._currentElement.hasClass("e-disable"))return!1;i.stopPropagation();i.preventDefault();this._files=this._getAllFileInfo(i.originalEvent.dataTransfer.files);this._selectedfiles=this._selectedfiles.concat(this._files);this.model.asyncUpload||(this._isDropped=!0,n("input[type='file']").prop("files",i.originalEvent.dataTransfer.files));this._fileSelect(i)},_onDragOverHandler:function(n){if(this._currentElement.hasClass("e-disable"))return!1;n.stopPropagation();n.preventDefault()},_disableclickselect:function(n){this._currentElement.hasClass("e-disable")&&n.preventDefault()},_bindInputChangeEvent:function(){this._on(this.inputupload,"change",this._inputValueChange)},_inputValueChange:function(t){if(!this.model.asyncUpload&&this._isDropped)t.stopPropagation(),t.preventDefault(),this._isDropped=!1;else return this._files=this._getInputFileInfo(n(t.target)),this._selectedfiles=this._selectedfiles.concat(this._files),this._fileSelect(t)},_fileSelect:function(n){if(this._trigger("fileSelect",{files:this._files}))return this._resetFileInput(this._currentElement.find(".e-uploadinput")),!1;this._fileUpload(n)},_fileUpload:function(n){var t=this._isAllowed(this._files);this._files=t.files;this.model.asyncUpload?t.files!=null&&t.files.length>0&&(this.UploadType=="Xhr"?this._onXhrSelect(n):this._onSelectIFrame(n),this._resetFileInput(this._currentElement.find(".e-uploadinput"))):t.status&&this._onSelectSyncUpload(n);this._renderTooltip()},upload:function(){this.UploadType=="Xhr"?this._xhrOnUploadButtonClick():this._onUploadButtonClickIFrame()},__uploadButtonClick:function(t){if(!this._currentElement.hasClass("e-disable")){var i=this._currentElement.find(".e-uploadbtn");this._currentElement.find(".e-uploadbtn").hasClass("e-disable")?t.preventDefault():this.upload();n(t.target).attr("disabled","disabled").addClass("e-disable")}},_actionClick:function(t){var r,i,u;this._currentElement.hasClass("e-disable")||(r=n(t.target),i=r.closest(".e-upload-file"),this._file=n(i).data("file"),r.hasClass("e-delete")?this.UploadType=="Xhr"?this._xhrOnRemove(t,i):this._onRemoveIFrame(t,i):r.hasClass("e-file-cancel")?(this._trigger("cancel",{fileStatus:this._file}),u=this.updialog.find(".e-file-cancel"),u.hasClass("e-disable")?t.preventDefault():this.model.asyncUpload?(this._removeFile(n(i).data("file")),this.UploadType=="Xhr"?this._xhrOnCancel(t,i):this._onCancelIFrame(t,i)):this._onCancelSyncUpload(t,i)):r.hasClass("e-reload")&&(this.UploadType=="Xhr"?this._xhrOnRetry(t,i):this._onRetryIFrame(t,i)))},_removeFileEntry:function(n){n.remove()},_removeFile:function(n){for(var i=[],r=[],u=[],f=[],t=0;t<this._files.length;t++)this._files[t]!=n&&i.push(this._files[t]);for(this._files=i,t=0;t<this._selectedfiles.length;t++)this._selectedfiles[t]!=n&&r.push(this._selectedfiles[t]);for(this._selectedfiles=r,t=0;t<this._successFiles.length;t++)this._successFiles[t]!=n&&u.push(this._successFiles[t]);for(this._successFiles=u,t=0;t<this._errorFiles.length;t++)this._errorFiles[t]!=n&&f.push(this._errorFiles[t]);this._errorFiles=f},_isFileUpload:function(t){if(this.model.customFileDetails.action){var i=n(t).find("div.e-icon");return i.is(".e-file-cancel")}return!0},_isXhrSupported:function(){return this.s.name=="msie"&&parseInt(this.s.version)<9||this.s.name=="safari"&&this.s.name=="chrome"&&this.s.version=="536"?!1:typeof FormData!="undefined"&&typeof File!="undefined"},_getFileName:function(t){return n.map(this._getAllFileInfo(t),function(n){return n.name}).join(", ")},_getFileSize:function(t){var i=this;return n.map(this._getAllFileInfo(t),function(n){return i._formatSize(n.size)}).join(", ")},_pushFile:function(i,r){var h,l,o,f,s,e,y,p,w,b,a,tt,c,v,u,k,d,g,nt;for(e=n("<div class='e-head-content'><\/div>"),p=n("<div class='e-file-head e-head-name'>"+this._getLocalizedLabels("name")+"<\/div>"),w=n("<div class='e-file-head e-head-size'>"+this._getLocalizedLabels("size")+"<\/div>"),b=n("<div class='e-file-head e-head-status'>"+this._getLocalizedLabels("status")+"<\/div>"),this.model.customFileDetails.name&&n(p).appendTo(e),this.model.customFileDetails.size&&n(w).appendTo(e),this.model.customFileDetails.status&&n(b).appendTo(e),v=this.updialog,v&&v.length!=0?this.model.showFileDetails&&(this.updialog&&this.updialog.find(".e-uploadbtn").removeAttr("disabled").removeClass("e-disable"),this.diaObj.open()):(this.updialog=t.buildTag("div.e-uploaddialog#"+this.element[0].id+"_dialog","",{},{title:this._getLocalizedLabels("title")}),this.model.allowDragAndDrop&&!this.model.showBrowseButton?(this.dragWrapper.append(this.updialog),a="#"+this.control.id+"_dragWrapper"):(this.element.append(this.updialog),a=this.control.id!=""?"#"+this.control.id:this.element)),h=this.updialog.find(".e-ul"),n(this.updialog.find(".e-head-content"))&&n(this.updialog.find(".e-head-content")).replaceWith(e),h.length==0&&(e.appendTo(this.updialog),h=t.buildTag("ul.e-ul").appendTo(this.updialog)),n(this.updialog).find(".e-file-upload").length>0&&n(this.updialog).find(".e-file-upload").remove(),tt=t.buildTag("div.e-file-upload").appendTo(this.updialog).append(this.model.autoUpload||!this.model.asyncUpload?n(t.buildTag("div.e-action-container")).addClass("sync"):t.buildTag("div.e-action-container")),c=t.buildTag("button.e-uploadclosebtn e-btn e-select",this._getLocalizedLabels("cancel"),{},{type:"button","data-role":"none"}).appendTo(n(this.updialog).find(".e-action-container")),this.model.showRoundedCorner&&c.addClass("e-corner"),this._on(c,"click",this._dialogclose),this._on(c,"keydown",this._keydownDialogClose),nt=this.model.dialogAction.content!=null&&this.model.dialogAction.content!=""?this.model.dialogAction.content:a,this.updialog.ejDialog({showOnInit:!1,closeIconTooltip:this._getLocalizedLabels("closeToolTip"),minWidth:240,width:n(window).width()<750?250:this._getDialogContentWidth(),height:"auto",cssClass:"e-uploadbox "+this.model.cssClass,close:n.proxy(this._uploadFileListDelete,this),enableRTL:this.model.enableRTL,target:nt,enableResize:!1,allowDraggable:this.model.dialogAction.drag,enableModal:this.model.asyncUpload?this.model.dialogAction.modal:!1,showHeader:this.model.customFileDetails.title,showRoundedCorner:this.model.showRoundedCorner}),n(window).width()<750?n(this.updialog).closest(".e-dialog.e-uploadbox").addClass("e-mobile-upload"):n(this.updialog).closest(".e-dialog.e-uploadbox").removeClass("e-mobile-upload"),this.diaObj=this.updialog.data("ejDialog"),this.diaObj.option("close",n.proxy(this._onClose,this)),this.model.cssClass!=""&&this.diaObj.setModel({cssClass:"e-uploadbox "+this.model.cssClass}),this._dialogPosition(),this.model.multipleFilesSelection||this.updialog.find(".e-ul>.e-upload-file").remove(),f=0;f<i.length;f++)l=n("<li class='e-upload-file'><\/li>").appendTo(h).data(r),u=n("<div class='e-file-list'><\/div>"),k=n("<div class='e-file-progress e-file-view'><div class='e-file-name e-file-view'><span class='e-file-name-txt'>"+i[f].name+"<\/span><\/div>"),y="<div class='e-file-size e-file-view'><span class='e-file-name-txt'>"+this._formatSize(i[f].size)+"<\/span><\/div>",d=n("<div class='e-file-percentage e-file-view'><div class='e-file-progress-bar'><div class='e-file-progress-status'><\/div><\/div><\/div>"),g=n("<div class='e-action-perform'><div class='e-icon e-file-view'><\/div><\/div>"),this.model.customFileDetails.name&&n(k).appendTo(u),this.model.customFileDetails.size&&(this._getFileSize(i[f]).toString().toLowerCase().match("0.0kb").length==0||i[f].size!=null?n(y).appendTo(u):n(this.updialog).find(".e-head-size").remove()),this.model.customFileDetails.status&&n(d).appendTo(u),this.model.customFileDetails.action&&(n(g).appendTo(u),s="cancel",u.find(".e-icon").remove().addClass(s.toString()),s=="cancel"?o=t.buildTag("div.e-icon e-file-cancel","",{},{"data-content":this._getLocalizedLabels("cancelToolTip")}):s=="remove"?o=t.buildTag("div.e-icon e-delete","",{},{"data-content":this._getLocalizedLabels("removeToolTip")}):s=="retry"&&(o=t.buildTag("div.e-icon e-reload","",{},{"data-content":this._getLocalizedLabels("retryToolTip")})),u.find(".e-action-perform").append(o),this._on(o,"click",this._actionClick)),n(u).appendTo(l);return this.model.showFileDetails&&this.diaObj.open(),this._buttonText(this.model.buttonText),this._dialogText(this.model.dialogText),l},_keydownDialogClose:function(n){this._currentElement.hasClass("e-disable")||n.keyCode==13&&this._dialogclose()},_pushFileDetails:function(i){var r,u,f,l,a,v,y,p,w,b,h,e,c,d,o,s,k;return f=n("<div class='e-head-content'><\/div>"),l=n("<div class='e-file-head e-head-name'>"+this._getLocalizedLabels("name")+"<\/div>"),a=n("<div class='e-file-head e-head-size'>"+this._getLocalizedLabels("size")+"<\/div>"),v=n("<\/div><div class='e-file-head e-head-status'>"+this._getLocalizedLabels("status")+"<\/div>"),this.model.customFileDetails.name&&n(l).appendTo(f),this.model.customFileDetails.size&&n(a).appendTo(f),this.model.customFileDetails.status&&n(v).appendTo(f),r=this,h=this.updialog,h&&h.length!=0?this.model.showFileDetails&&(this.control.id!=""?e:e=this.element,this.updialog&&this.updialog.find(".e-uploadbtn").removeAttr("disabled").removeClass("e-disable"),r.diaObj.open()):(this.updialog=t.buildTag("div.e-uploaddialog#"+this.element[0].id+"_dialog","",{},{title:this._getLocalizedLabels("title")}),this.model.allowDragAndDrop&&!this.model.showBrowseButton?(this.dragWrapper.append(this.updialog),e="#"+this.control.id+"_dragWrapper"):(this.element.append(this.updialog),e=this.control.id!=""?"#"+this.control.id:this.element)),o=this.updialog.find(".e-ul"),n(this.updialog.find(".e-head-content"))&&n(this.updialog.find(".e-head-content")).replaceWith(f),o.length==0&&(f.appendTo(this.updialog),o=t.buildTag("ul.e-ul").appendTo(this.updialog)),n(this.updialog).find(".e-file-upload").length>0&&n(this.updialog).find(".e-file-upload").remove(),d=t.buildTag("div.e-file-upload").appendTo(this.updialog).append(this.model.autoUpload||!this.model.asyncUpload?n(t.buildTag("div.e-action-container")).addClass("sync"):t.buildTag("div.e-action-container")),c=t.buildTag("button.e-uploadclosebtn e-btn e-select",this._getLocalizedLabels("cancel"),{},{type:"button","data-role":"none"}).appendTo(n(this.updialog).find(".e-action-container")),this.model.showRoundedCorner&&c.addClass("e-corner"),this._on(c,"click",this._dialogclose),this.model.multipleFilesSelection||this.updialog.find(".e-ul>.e-upload-file").remove(),s=n("<li class='e-upload-file'><\/li>").appendTo(o).data("file",i),u=n("<div class='e-file-list'><\/div>"),y=n("<div class='e-file-progress e-file-view'><div class='e-file-name e-file-view'><span class='e-file-name-txt'>"+i.name+"<\/span><\/div>"),w=n("<div class='e-file-size e-file-view'><span class='e-file-name-txt'>"+this._formatSize(0)+"\\"+this._formatSize(i.size)+"<\/span><\/div>"),p=n("<div class='e-file-percentage e-file-view'><div class='e-file-progress-bar'><div class='e-file-progress-status'><\/div><\/div><\/div>"),b=n("<div class='e-action-perform'><div class='e-icon e-file-view'><\/div><\/div><\/div>"),this.model.customFileDetails.name&&n(y).appendTo(u),this.model.customFileDetails.size&&n(w).appendTo(u),this.model.customFileDetails.status&&n(p).appendTo(u),this.model.customFileDetails.action&&n(b).appendTo(u),n(u).appendTo(s),k=this.model.dialogAction.content!=null&&this.model.dialogAction.content!=""?this.model.dialogAction.content:e,this.updialog.ejDialog({showOnInit:!1,closeIconTooltip:this._getLocalizedLabels("closeToolTip"),minWidth:240,width:n(window).width()<750?250:this._getDialogContentWidth(),height:"auto",cssClass:"e-uploadbox "+this.model.cssClass,close:n.proxy(this._uploadFileListDelete,this),enableRTL:this.model.enableRTL,target:k,enableResize:!1,allowDraggable:this.model.dialogAction.drag,enableModal:this.model.asyncUpload?this.model.dialogAction.modal:!1,showHeader:this.model.customFileDetails.title,showRoundedCorner:this.model.showRoundedCorner}),n(window).width()<750?n(this.updialog).closest(".e-dialog.e-uploadbox").addClass("e-mobile-upload"):n(this.updialog).closest(".e-dialog.e-uploadbox").removeClass("e-mobile-upload"),r.diaObj=this.updialog.data("ejDialog"),r.model.cssClass!=""&&r.diaObj.setModel({cssClass:"e-uploadbox "+this.model.cssClass}),r._dialogPosition(),this.model.showFileDetails&&r.diaObj.open(),r.diaObj.option("close",n.proxy(this._onClose,this)),this._buttonText(this.model.buttonText),this._dialogText(this.model.dialogText),s},_onClose:function(){this._successFiles=[];this._errorFiles=[];this._selectedfiles=[];this._uploadFileListDelete()},_setProgress:function(t,i,r){var u,f,e,o;this.model.customFileDetails.status&&(u=n(t).find(".e-file-progress-status"),u.width(i+"%"));this.model.customFileDetails.size&&(f=n(t).find(".e-file-size .e-file-name-txt"),e=this._formatSize(r.loaded),o=this._formatSize(r.total),f.html(e+"\\"+o))},_setAction:function(n,i){if(this.model.customFileDetails.action){var r;n.find(".e-action-perform .e-icon,.e-file-percentage .e-icon").remove().addClass(i.toString());i=="cancel"?r=t.buildTag("div.e-icon e-file-cancel","",{},{"data-content":this._getLocalizedLabels("cancelToolTip")}):i=="remove"?r=t.buildTag("div.e-icon e-delete","",{},{"data-content":this._getLocalizedLabels("removeToolTip")}):i=="retry"&&(r=t.buildTag("div.e-icon e-reload","",{},{"data-content":this._getLocalizedLabels("retryToolTip")}));n.find(".e-action-perform").append(r);this._on(r,"click",this._actionClick)}},_setStatus:function(n,i){if(this.model.customFileDetails.status){var u,r=t.buildTag("div");i=="success"&&(n.find(".file-status").addClass("e-file-status-success").html("Completed"),n.find(".e-file-percentage").html(""),r.addClass("e-icon e-checkmark").attr("data-content",this._getLocalizedLabels("completedToolTip")),n.find(".e-file-percentage").append(r));i=="failed"&&(n.find(".file-status").addClass("e-file-status-failed").html("Failed"),n.find(".e-file-percentage").html(""),r.addClass("e-icon e-file-percentage-failed").attr("data-content",this._getLocalizedLabels("failedToolTip")),n.find(".e-file-percentage").append(r));i=="progress"&&n.find(".file-status").addClass("file-status-inprogress").html("in progress");i=="uploading"&&(n.find(".file-status").addClass("file-status-inprogress").html("uploading"),u=n.find(".e-file-percentage"),u.html(""))}},_renderTooltip:function(){var i=this;t.isNullOrUndefined(this.updialog)||(this.upTooltip=n(this.updialog).ejTooltip({target:".e-file-cancel, .e-delete, .e-reload, .e-checkmark, .e-close, .e-file-percentage-failed",content:" ",isBalloon:!1,showRoundedCorner:this.model.showRoundedCorner,enableRTL:this.model.enableRTL,position:{target:{horizontal:"center",vertical:"bottom"},stem:{horizontal:"left",vertical:"top"}}}).data("ejTooltip"),n(this.upTooltip.tooltip).css({"min-width":"auto"}))},_createInputandBind:function(){var n=t.buildTag("input","",{},{type:"file","data-role":"none"});n.attr("name",this.model.uploadName!=""?this.model.uploadName:this.control.id).attr("autocomplete","off").attr("class","e-uploadinput").attr("accept",this.model.extensionsAllow);this.model.multipleFilesSelection&&(navigator.userAgent.indexOf("Safari")!=-1&&navigator.userAgent.indexOf("Chrome")==-1)==!1&&n.attr("multiple","multiple");n.appendTo(this.element.find(".e-selectpart"));this.inputupload=n;this._bindInputChangeEvent()},_showUploadButton:function(){var i=this.updialog.find(".e-uploadbtn");i.length==0&&(i=t.buildTag("button.e-uploadbtn e-btn e-select",this._getLocalizedLabels("upload"),{},{type:"button","data-role":"none"}),this.model.showRoundedCorner&&i.addClass("e-corner"),this.updialog.find(".e-action-container").append(i),this._on(i,"click",this.__uploadButtonClick),this._on(i,"keydown",this._keydownUpload),n(i).focus());this._buttonText(this.model.buttonText)},_keydownUpload:function(n){n.keyCode==13&&this.__uploadButtonClick(n)},_resetFileInput:function(n){var t=n.clone(!1,!1);this._on(t,"change",this._inputValueChange);n.replaceWith(t)},_isAllowed:function(t){var f,e,i,s,h,c,r=this,u=[],l=[],o=!0;return f=this.element.find(".e-uploadinput"),e=this,this.model.extensionsAllow!=""?(s=this.model.extensionsAllow.replace(/\s/g,"").toLowerCase().split(","),n(t).each(function(){n.inArray(this.extension.toLowerCase(),s)==-1?(i={action:"Files Processing",error:r._getLocalizedLabels("allowError").replace("#Extension",s.join(", ")),files:this},e._trigger("error",i),r._errorFiles.push(i.files),r._resetFileInput(f),o=!1):this.extension!=""&&u.push(this)})):this.model.extensionsDeny!=""?(c=this.model.extensionsDeny.replace(/\s/g,"").toLowerCase().split(","),n(t).each(function(){n.inArray(this.extension.toLowerCase(),c)!=-1?(i={action:"Files Processing",error:r._getLocalizedLabels("denyError").replace("#Extension",c.join(", ")),files:this},e._trigger("error",i),r._errorFiles.push(i.files),r._resetFileInput(f),o=!1):this.extension!=""&&u.push(this)})):n(t).each(function(){this.extension!=""&&u.push(this)}),this.model.fileSize!=""?(h=this.model.fileSize,n(u).each(function(){this.size>h?(i={action:"Files Processing",error:r._getLocalizedLabels("filedetail").replace("#fileSize",h),files:this},e._trigger("error",i),r._errorFiles.push(i.files),r._resetFileInput(f),o=!1):l.push(this)})):l=u,{files:l,status:o}},_fileListRemove:function(){var n=this.updialog.find(".e-upload-file .e-delete");n.length==0&&(this.updialog.find(".e-uploadbtn").attr("disabled","disabled").addClass("e-disable"),this.updialog.ejDialog("close"))},_uploadHide:function(){var n=this.updialog.find(".e-upload-file .e-file-cancel");n.length==0&&(this.updialog.find(".e-ul").empty(),this.updialog.find(".e-uploadbtn").attr("disabled","disabled").addClass("e-disable"),this.updialog.ejDialog("close"),this.upTooltip.hide())},_onBeforeClose:function(i){t.isNullOrUndefined(i.event)||(i.cancel=n(i.event.currentTarget).hasClass("e-disable")?!0:!1)},_dialogclose:function(n){if(this._successFiles=[],this._errorFiles=[],this._selectedfiles=[],!this._currentElement.hasClass("e-disable")){var t=this.updialog.find(".e-uploadclosebtn");t.hasClass("e-disable")?n.preventDefault():(this._uploadFileListDelete(),this.updialog.ejDialog("close"))}},_uploadFileListDelete:function(){var t,i;i=this.updialog.find(".e-ul").children().first();t=n(i).data("file");this._trigger("cancel",{fileStatus:t});this.updialog.find(".e-ul").empty();this._currentElement.find(".e-uploadinput").val("");this._resetFileInput(this._currentElement.find(".e-uploadinput"));this.model.asyncUpload||this._formResetSyncUpload()},_onXhrSelect:function(){var t,i;this._xhrBeforeUpload(this._files);t=this;n.each(t._files,function(r,u){i=n(u).data("filelist");t._setAction(i,"cancel");t.model.autoUpload?t._xhrPerformUpload(u):t._showUploadButton()})},_xhrBeforeUpload:function(t){var i,r,u,f;return i=t,r=this,n.each(i,function(t,i){u=r._createFormObjectXhr(i);n(i).data("formobject",u);f=r._pushFileDetails(i);n(i).data("filelist",f)}),i},_xhrPerformUpload:function(t){var o,r,e,u,f;if(u=new XMLHttpRequest,r=this,f={files:t},this._trigger("begin",f)){n(t).data("xhr",u);r._onRequestError(r,i,t);return}if(o=this.model.saveUrl,e=n(t).data("formobject"),e.append((this.model.uploadName&&this.model.uploadName!=""?this.model.uploadName:this.control.id)+"_data",JSON.stringify(f.data)),n(t).data("xhr",u),u.addEventListener("load",function(n){r._onRequestSuccess(r,n,t)},!1),u.addEventListener("error",function(n){r._onRequestError(r,n,t)},!1),u.upload.addEventListener("progress",function(n){r._onRequestProgress(r,n,t)},!1),u.open("POST",o),f={files:t,xhr:u},this._trigger("beforeSend",f)){n(t).data("xhr",u);r._onRequestError(r,i,t);return}u.send(e)},_xhrOnUploadButtonClick:function(){var r,f,u,i,t;for(r=this.model.showBrowseButton?this.diaObj.wrapper:this.dragWrapper,i=r.find(".e-ul li.e-upload-file"),t=0;t<i.length;t++)f=n(this),u=this._isFileUpload(i[t]),u&&this._xhrPerformUpload(n(i[t]).data("file"))},_xhrOnRemove:function(t,i){var u,f,o,e,s,r;if(u=new XMLHttpRequest,s=n(i).find(".e-file-name").text().toString().split(","),r=this,o=this.model.removeUrl,u.open("POST",o),f={files:r._file,xhr:u},this._trigger("beforeRemove",f)){n(r._file).data("xhr",u);return}e=n(r._file).data("formobject");e.append((this.model.uploadName&&this.model.uploadName!=""?this.model.uploadName:this.control.id)+"_data",JSON.stringify(f.data));u.send(e);n.ajax({url:this.model.removeUrl,type:"POST",data:"fileNames="+s,success:function(){n(i).remove();r._fileListRemove();r._trigger("remove",{fileStatus:r._file})}});this.upTooltip.hide()},_xhrOnCancel:function(t,i){var r,u;r=n(i).data("file");u=n(r).data("xhr");u&&n(r).data("xhr").abort();n(r).data("xhr",null);n(i).data("file",null);n(i).remove();this._uploadHide()},_xhrOnRetry:function(t,i){if(!this._currentElement.hasClass("e-disable")){var r=n(i).data("file");this._xhrPerformUpload(r)}},_onRequestSuccess:function(t,i,r){var u=n(r).data("xhr");u.status>=200&&u.status<=299?t._onXhrUploadSuccess(t,i,r):t._onRequestError(t,i,r);t._selectedfiles.length==this._successFiles.length+this._errorFiles.length&&(t._trigger("complete",{files:r,responseText:u.responseText,success:this._successFiles,error:this._errorFiles,xhr:u,e:i}),this._onClearCompletedFiles())},_onClearCompletedFiles:function(){this._successFiles=[];this._errorFiles=[];this._selectedfiles=[];this.model.dialogAction.closeOnComplete&&this._dialogclose()},_onXhrUploadSuccess:function(t,i,r){var u,f,o,s,e,h,c;u=n(r).data("filelist");f=n(r).data("xhr");t.model.removeUrl?(t._setAction(u,"remove"),t._setStatus(u,"success")):(u.find(".e-action-perform .e-icon,.e-file-percentage .e-icon").remove(),t._setStatus(u,"success"));n(r).length>0&&(o=n(u).find(".e-file-progress-status"),o.width("100%"),s=n(r)[0].size,e=this._formatSize(s),h=n(u).find(".e-file-size .e-file-name-txt"),h.html(e+"\\"+e));c={files:r,responseText:f.responseText,xhr:f,e:i};this.updialog.find(".e-file-upload .e-uploadclosebtn").html(this.model.buttonText.close).focus();t._trigger("success",c);this._successFiles.push(r)},_onRequestError:function(t,i,r){var f,u,e;f=n(r).data("filelist");u=n(r).data("xhr");t._setAction(f,"retry");t._setStatus(f,"failed");e={action:"File Processing",error:u.status?u.status+" "+u.statusText:"Unable to reach the server.",files:r,xhr:u,e:i};t._trigger("error",e);this._errorFiles.push(r)},_onRequestProgress:function(t,i,r){var u,f,e;u=Math.round(i.loaded*100/i.total);f=n(r).data("filelist");t._setProgress(f,u,i);t._setStatus(f,"progress");e={file:r,percentage:u,e:i};this._trigger("inProgress",e)},_createFormObjectXhr:function(n){var t=new FormData;return t.append(this.model.uploadName!=""?this.model.uploadName:this.control.id,n.rawFile),t},_getInputFileInfo:function(n){var t=n[0];return t.files?this._getAllFileInfo(t.files):[{name:this._GetName(t.value),extension:this._getFileExtensionType(t.value),size:this._getFileSizeinIE(t.value)}]},_getFileSizeinIE:function(n){var t,i;t=null;i=null;try{t=new ActiveXObject("Scripting.FileSystemObject")}catch(r){i=null}return t&&(i=t.getFile(n).size),i},_getFileExtensionType:function(n){return n.match?n.match(/\.([^\.]+)$/)?n.match(/\.([^\.]+)$/)[0]:"":""},_getAllFileInfo:function(t){var i=this;return n.map(t,function(n){return i._getFileInfo(n||t)})},_GetName:function(n){var t=n.lastIndexOf("\\");return t!=-1?n.substr(t+1):n},_getFileInfo:function(n){var t=n.name||n.fileName||n;return{name:t,extension:this._getFileExtensionType(t),size:n.size||n.fileSize,rawFile:n}},_formatSize:function(n){var t=-1;if(!n)return"0.0KB";do n=n/1024,t++;while(n>99);return Math.max(n,0).toFixed(1)+["KB","MB","GB","TB","PB","EB"][t]},_onSelectIFrame:function(t){var r,u,i,f;r=n(t.target);u=this._getInputFileInfo(r);i=this._beforeUploadIFrame(u);f=i.data("iframe");this.model.autoUpload?this._performUploadIFrame(i):this._showUploadButton();this._off(this.inputupload,"change");this._bindInputChangeEvent()},_onRemoveIFrame:function(t,i){var u,f,r,e;u=i.data("iframe");e=n(i).data("file");f=e[0].name;r=this;u?(this._removeFileEntry(i),this.model.removeUrl&&n.ajax({url:this.model.removeUrl,type:"POST",data:"fileNames="+f,success:function(){r._fileListRemove();r._trigger("remove",{fileStatus:r._file})}})):(this._removeFileEntry(i),this._trigger("remove",{fileStatus:this._file}))},_onCancelIFrame:function(n,t){var i;this._trigger("cancel",{Status:t});i=t.data("iframe");i&&(this._removeFileEntry(t),typeof i.stop!="undefined"?i.stop():i.document&&(i.document.execCommand("Stop"),i.contentWindow.location.href=i.contentWindow.location.href),this._processServerResponse(i,""));this._uploadHide()},_onRetryIFrame:function(n,t){this._performUploadIFrame(t)},_beforeUploadIFrame:function(n){var t,r,i;return t=this._createFrame(this.control.id+"_Iframe"+this.Uploadframes.length),this.Uploadframes.push(t),r=this._createForm(this.model.saveUrl,t[0].id),this._currentElement.find("input.e-uploadinput").removeClass("e-uploadinput").css("display","none").appendTo(r),this._createInputandBind(),i=this._pushFile(n,{iframe:t,form:r,file:n}),t.data({filelist:i}),this._setAction(i,"cancel"),i},_performUploadIFrame:function(t){var e,r,i,u,f;if(u={files:t},this._trigger("begin",u)){this._failureIframeUpload(t,"File upload has been denied");return}e=t.data("file");this._setStatus(t,"uploading");r=t.data("iframe");i=t.data("form");f=n("<input>").attr("name",(this.model.uploadName&&this.model.uploadName!=""?this.model.uploadName:this.control.id)+"_data").attr("type","hidden").val(JSON.stringify(u.data));i.append(f);r.appendTo(document.body);i.appendTo(document.body);this._on(r,"load",this._removeFramesIFrame);i.submit()},_onUploadButtonClickIFrame:function(){var t,i,r;t=this;n(".e-ul li.e-upload-file",t.updialog).each(function(){i=n(this);r=t._isFileUpload(i);r&&t._performUploadIFrame(i)})},_removeFramesIFrame:function(t){var u,i,r,f;u=n(t.target);f=this._files;r=u.data("filelist");try{i=n.trim(t.target.contentDocument.body.innerText.replace(/\n|\r/g," "))}catch(t){i="Server Error trying to get server response: "+t}i.substring(0,12)!="Server Error"&&i.indexOf("HTTP Error")!=0?(this._processServerResponse(u,i),this._setIframeProgress(r,100,t),this._setStatus(r,"progress"),this._successIframeUpload(r,i)):this._failureIframeUpload(r,i);r.length==this._successFiles.length+this._errorFiles.length&&(this._trigger("complete",{files:f,responseText:i,success:this._successFiles,error:this._errorFiles}),this._onClearCompletedFiles())},_setIframeProgress:function(t,i){var r;r=n(t).find(".e-file-progress-status");r.width(i+"%")},_successIframeUpload:function(n,t){var i=n.data("file"),r;this.model.removeUrl?(this._setAction(n,"remove"),this._setStatus(n,"success")):(n.find(".file-action").remove(),this._setStatus(n,"success"));r={files:i,responseText:t};this.updialog.find(".e-file-upload .e-uploadclosebtn").html(this.model.buttonText.close).focus();this._trigger("success",r);this._successFiles.push(i)},_failureIframeUpload:function(n,t){var i=n.data("file"),r;this.model.saveUrl&&t!="File upload has been denied"?(this._setAction(n,"retry"),this._setStatus(n,"failed")):(n.find(".file-action").remove(),this._setStatus(n,"failed"));r=t.indexOf("HTTP Error")==0?{files:i,responseText:t,status:t.match(/\d+/).toString()}:{files:i,responseText:t};this._trigger("error",r);this._errorFiles.push(i)},_processServerResponse:function(t){var i;i=n(document.body).find("form[target='"+n(t).attr("id")+"']");setTimeout(function(){i.remove();t.remove()},0)},_createDivBlock:function(n){return t.buildTag("div."+n)},_createForm:function(n,i){return t.buildTag("form","",{},{enctype:"multipart/form-data",method:"POST",action:n,target:i})},_createFrame:function(n){return t.buildTag("iframe#"+n,"",{display:"none"},{name:n})},_createInput:function(n){return t.buildTag("input","",{},{type:"file",name:n,"data-role":"none"})},_initObjectsSyncUpload:function(){this._currentElement.closest("form").attr("enctype","multipart/form-data").attr("encoding","multipart/form-data");this._wireEventsSyncUpload()},_wireEventsSyncUpload:function(){var t=this._currentElement.closest("form")[0];this._on(n(t),"submit",this._formSubmitSyncUpload);this._on(n(t),"reset",this._formResetSyncUpload)},_onSelectSyncUpload:function(t){var i,r,u;i=n(t.target);r=n(".e-selectpart",this.control);this._currentElement.find("input.e-uploadinput").removeClass("e-uploadinput").css("display","none").appendTo(r);this._createInputandBind();u=this._pushFile(this._files,{file:this._files,Input:i})},_onCancelSyncUpload:function(n,t){var i=t.data("Input");t.data("file",null);t.data("Input",null);t.remove();i.remove();this._uploadHide()},_formSubmitSyncUpload:function(){var t,i;t=n(".e-uploadinput",this.control);t.attr("name","");i=this.model.uploadName!=""?this.model.uploadName:this.control.id;setTimeout(function(){t.attr("name",i)},0)},_formResetSyncUpload:function(){n(".e-selectpart",this.control).children('input[type="file"]').each(function(){n(this).hasClass("e-uploadinput")||n(this).remove()})}});t.Uploadbox.Locale=t.Uploadbox.Locale||{};t.Uploadbox.Locale["default"]=t.Uploadbox.Locale["en-US"]={buttonText:{upload:"Upload",browse:"Browse",cancel:"Cancel",close:"Close"},dialogText:{title:"Upload Box",name:"Name",size:"Size",status:"Status"},dropAreaText:"Drop files or click to upload",filedetail:"The selected file size is too large.  Please select a file within #fileSize",denyError:"Files with #Extension extensions are not allowed.",allowError:"Only files with #Extension extensions are allowed.",cancelToolTip:"Cancel",removeToolTip:"Remove",retryToolTip:"Retry",completedToolTip:"Completed",failedToolTip:"Failed",closeToolTip:"Close"}})(jQuery,Syncfusion)});
