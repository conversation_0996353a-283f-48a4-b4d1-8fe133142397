/*!
*  filename: ej.mobile.radialslider.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function(n,t){t.widget("ejRadialSliderBase","ej.RadialSliderBase",{defaults:{radius:200,ticks:[0,10,20,30,40,50,60,70,80,90,100],enableRoundOff:!0,value:10,autoOpen:!0,enableAnimation:!0,cssClass:null,labelSpace:30,stop:null,slide:null,start:null,change:null,create:null,destroy:null},dataTypes:{radius:"number",enableRoundOff:"boolean",enableAnimation:"boolean",cssClass:"string"},observables:["value"],observableArray:["ticks"],value:t.util.valueFunction("value"),ticks:t.util.valueFunction("ticks"),_outerTextCalculation:function(n,t,i){var e=this._isMobile()?this.model.radius-this.model.labelSpace:this.model.radius+this.model.labelSpace,f,s,o,r,u;for(this._point=(i-t)/(n-1),i=t+(i-t)/(n-1),f=i,s=f,this._textPoints=[],o=0;o<n;o++)r={},u=t,t=t*Math.PI/180,i=i*Math.PI/180,r.X2=this._startXY+e*Math.cos(t),r.Y2=this._startXY+e*Math.sin(t),r.textAlignment=u<=270&&90<=u?"middle":"start",t=f,i=f+this._point,f+=this._point,this._textPoints.push(r);return r={},u=this._startValueAngle,this._startValueAngle=this._startValueAngle*Math.PI/180,r.X2=this._startXY+e*Math.cos(this._startValueAngle),r.Y2=this._startXY+e*Math.sin(this._startValueAngle),r.textAlignment=u<=270&&90<=u?"middle":"start",this._textPoints.push(r),this._textPoints},_polarToCartesian:function(n,t,i,r,u){var f=r*Math.PI/180;return{x:n+i*Math.cos(f),y:t+i*Math.sin(u?-f:f)}},_tapHandlerEvent:function(i){var f,s,h=n("#"+this._prefix+this._elementID+"-radial-slider-svg").offset(),c=i.clientY,l=i.clientX,a=h.top+this._radialWidth/2-n(window).scrollTop(),v=h.left+this._radialWidth/2,e,o,y,u,r,p;if(this._dynamicAngleCalculation(v,l,a,c),e=this._isMobile()&&this.model.renderMode=="ios7"?6.5:this._isMobile()&&this.model.renderMode=="windows"?2.5:5,!this._isMobile()&&this._angle>=360-this._endAngle&&this._angle<=360-this._startAngle||this._isMobile()&&this._angle>=360-(this._endAngle-e)&&this._angle<=360-(this._startAngle+e)||this._isMobile()&&this.model.position.charAt(0).toLowerCase()=="l"&&(this._angle>270+e||this._angle<90-e)){if(this._lineAngleCalculation(!0),this._previousAngle=this._angle,this._isMobile()||(n(this._overLine).remove(),this._pathBeforeAddlength=this._tickCount+1,this._pathAfterAddLength=this._directionLine.toString().replace(/[^M]/g,"").length,this._pathBeforeAddlength<this._pathAfterAddLength?(o=this._isTapSelected?2:1,this._directionLine.remove(this._tickCount,o,o)):this._directionLine.remove(this._tickCount,1),this._dynamicLineCalculation(this._angle,!1,!0,!0,this._tickCount,this._tickCount+1,!0),n(this._pathLineElement).attr("d",this._directionLine.toString())),n(this._textGroupElement).find("[id="+this._prefix+this._elementID+"-dynamic-text]").length>0&&this._textGroupElement.find("[id="+this._prefix+this._elementID+"-dynamic-text]").remove(),y=this._selectPart(),u=y.select,this._isTicksControl()){if(r=this._ticksCalculation(),this._isMobile()&&this.model.position.charAt(0).toLowerCase()=="l"?this.line.textAlignment="end":this._isMobile()&&this.model.position.charAt(0).toLowerCase()=="r"?this.line.textAlignment="start":this._isMobile()&&(this.model.position.charAt(0).toLowerCase()=="b"||this.model.position.charAt(0).toLowerCase()=="t")&&(this.line.textAlignment="middle"),(this._isMobile()&&this.ticks().indexOf(r)==0||this.ticks().indexOf(r)==this._tickCount-1)&&(this.line.textAlignment="middle"),this.ticks().indexOf(r)<0){f=r.toString().split(".");p=t.preferredCulture(this.model.locale).numberFormat["."];f=t.isNullOrUndefined(f[1])?f[0]:f[0]+p+f[1];s=f;this._outerTextElement=this._createSVGElements("text",{"stroke-width":.5,x:this.line.X2,y:this.line.Y2,"class":this._prefix+"dynamic-text",id:this._prefix+this._elementID+"-dynamic-text",textContent:s,"text-anchor":this.line.textAlignment});this._textGroupElement.append(this._outerTextElement);var w=document.getElementById(this._prefix+this._elementID+"-dynamic-text").getBoundingClientRect(),v=this._textPoints[u.toFixed()].X2,a=this._textPoints[u.toFixed()].Y2,l=this.line.X2,c=this.line.Y2,nt=Math.sqrt(Math.pow(l-v,2)+Math.pow(c-a,2));if(nt<w.width*72/96){this.line={};var b=r.toString().length,k=w.width*72/96-(this._isMobile()?b*2:b*4),tt=parseFloat(u.toFixed(3).substr(0,1))==u.toFixed()?this._degPoint[u.toFixed()]+k:this._degPoint[u.toFixed()]-k,d=(360-tt)*(Math.PI/180),g=this._isMobile()?this.model.radius-this.model.labelSpace:this.model.radius+this.model.labelSpace;this.line.X2=this._startXY+g*Math.cos(d);this.line.Y2=this._startXY+g*Math.sin(-d);n("#"+this._prefix+this._elementID+"-dynamic-text").attr({x:this.line.X2,y:this.line.Y2})}}}else r=this.ticks()[u.toFixed()];this._trigger("change",{value:r,oldValue:this.value()});this._needleStop&&this.model.stop&&this._trigger("stop",{value:r});this.value(r);this._needleStop=!0;this._needleMove=!1;this._isMobile()&&(this.model.renderMode=="windows"||this.model.renderMode=="flat"?this._dynamicWindowsRadial():this._dynamicIOSandAndroidRadial())}},_selectPart:function(){var t=this._dynamicAngle,i=this._startAngle,u=this._endAngle,n;this._isMobile()&&(n=this.model.renderMode=="ios7"?5:this.model.renderMode=="windows"?2.5:5,i=this._startAngle+n,u=this._endAngle-n,this.model.position.charAt(0).toLowerCase()=="l"&&this._dynamicAngle<180&&(t=this._dynamicAngle+360));var r=(t-i)/((u-i)/(this.ticks().length-1)),f=this.ticks()[parseInt(r)],e=this.ticks()[parseInt(r)+1],o=e-f;return{select:r,firstValue:f,space:n,difference:o,dynamicAngle:t}},_lineAngleCalculation:function(n){var t=this._selectPart(),i,f,r,u;this._degPoint.splice(this.ticks().length,this.ticks().length+1);this._degPoint.push(this._degPoint[this._degPoint.length-1]+this._point);i=(this._degPoint[parseInt(t.select)+1]-this._degPoint[parseInt(t.select)]-(this._degPoint[parseInt(t.select)+1]-t.dynamicAngle))/(this._point/t.difference);f=parseInt(t.select)!=0?this._degPoint[parseInt(t.select)]:this._degPoint[parseInt(t.select)];this.model.enableRoundOff&&n?(u=parseFloat(i.toFixed(2)),i=parseInt(i.toFixed()),r=t.difference==.5&&u>=.25?this._point*1:this._point/t.difference*i):r=this._point/t.difference*parseFloat(i.toFixed(2));this._angle=360-this._degPoint[parseInt(t.select)]-Math.abs(r)},_isTicksControl:function(){var t=this._selectPart(),i,r;return this._degPoint.splice(this.ticks().length,this.ticks().length+1),this._degPoint.push(this._degPoint[this._degPoint.length-1]+this._point),i=(this._degPoint[parseInt(t.select)+1]-this._degPoint[parseInt(t.select)]-(this._degPoint[parseInt(t.select)+1]-this._dynamicAngle))/(this._point/t.difference),r=this.model.enableRoundOff?parseInt(i.toFixed())==0||parseInt(i.toFixed())==t.difference?!1:!0:n.inArray(this._angle,this._degPoint)>-1?!1:!0,r},_ticksCalculation:function(){var t=this._selectPart(),r=t.select,s=t.dynamicAngle,h=t.difference,e=t.firstValue,l=t.space,e=this.ticks()[parseInt(r)],u,f,o,n,i,c;return this.line={},u=this._angle*(Math.PI/180),f=this._isMobile()?this.model.radius-this.model.labelSpace:this.model.radius+this.model.labelSpace,this.line.X2=this._startXY+f*Math.cos(u),this.line.Y2=this._startXY+f*Math.sin(-u),this.line.textAlignment=this._angle<=270&&90<=this._angle?"middle":"start",o=parseInt(r)!=0?this._degPoint[parseInt(r)]:this._degPoint[parseInt(r)],n=(s-o)/(this._point/h),this.model.enableRoundOff?(c=parseFloat(n.toFixed(1).substr(1,3)),n=parseInt(n.toFixed()),i=e+Math.abs(n)):(i=e+Math.abs(parseFloat(n.toFixed(2))),i=parseFloat(i.toFixed(2))),i},_dynamicAngleCalculation:function(n,t,i,r){var u=Math.atan2(r-i,t-n);this._angle=(360-u*180/Math.PI)%360;this._dynamicAngle=(360+u*180/Math.PI)%360},_createSVGElements:function(t,i){var r=document.createElementNS(this._svgLink,t);return n.each(i,function(n,t){n=="xlink:href"&&r.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",t);n!="textContent"?r.setAttribute(n,t):r.textContent=t}),r},show:function(){this.model.enableAnimation&&(this.element.removeClass(this._prefix+"slider-hide").addClass(this._prefix+"slider-show"),this._radialSVG.attr("class","").attr("class",this._prefix+"radialslider-svg-show "+this._prefix+"rs-svg"));this.element.css("display","block");this.model.autoOpen=!0},hide:function(){var n=this;this.model.enableAnimation?(this.element.removeClass(this._prefix+"slider-show").addClass(this._prefix+"slider-hide"),this._radialSVG.attr("class","").attr("class",this._prefix+"radialslider-svg-hide "+this._prefix+"rs-svg"),this.model.autoOpen?setTimeout(function(){n.element.css("display","none")},this._isMobile?150:400):n.element.css("display","none")):n.element.css("display","none");this.model.autoOpen=!1},_setModel:function(n){t.isNullOrUndefined(n.inline)||n.inline||(this.model.radius+=50);n.ticks&&(this.model.ticks=n.ticks);n.locale&&(this.model.locale=n.locale);n.enableRoundOff&&this.model.value(Math.round(t.util.getVal(this.model.value)));this._refresh()},_clearElement:function(){this.element.removeAttr("class");this.element.html(this._orgEle.html())},_destroy:function(){this._wireEvents(!0);this._clearElement()},_isMobile:function(){return this._prefix=="e-m-"?!0:!1}})})(jQuery,Syncfusion),function(n,t){t.widget("ejmRadialSlider","ej.mobile.RadialSlider",{_setFirst:!0,_rootCSS:"e-m-radialslider",defaults:{renderMode:"auto",theme:"auto",position:"rightcenter",strokeWidth:t.isWindows()||t.isFlat()?12:5},dataTypes:{ticks:"array"},_init:function(){this._docClick=!1;this._orgEle=this.element.clone();this._renderEJMControl()},_renderEJMControl:function(){var r,i,f,u;for(t.setRenderMode(this),t.setTheme(this),this._prefix="e-m-",this._initialization(),this.element.addClass("e-m-radialslider e-m-overlow e-m-abs e-m-user-select e-m-"+this.model.renderMode+" e-m-"+this.model.theme+" e-m-radial"+this.model.position+""),this._radialSVG=n(this._createSVGElements("svg",{id:this._prefix+this._elementID+"-radial-slider-svg","class":this._prefix+"rs-svg",width:this._diameter,height:this._diameter})),this._circleGroupElement=n(this._createSVGElements("g",{id:"circlegroup"})),this._radialCircleCalculation(50,40,this._tickCount,!0,!0,"path"),this._textGroupElement=n(this._createSVGElements("g",{id:"outerTextCircle",transform:"translate(0, 4)"})),r=this.model.renderMode=="ios7"?6.5:5,this._outerTextDirection=this._outerTextCalculation(this._tickCount,this._startAngle+r,this._endAngle-r),i=0;i<this._tickCount+1;i++){if(f=i==0&&this._startAngle==0&&this._endAngle==360?this._tickCount-1:i,u=i==0||i==this._tickCount-1||this.ticks()[i]==this.value()&&this.ticks()[0]==this.value()||this.ticks()[i]==this.value()&&this.ticks()[this._tickCount-1]==this.value(),this.model.position.charAt(0).toLowerCase()=="l"&&(this._outerTextDirection[i].textAlignment="end"),u&&(this._outerTextDirection[i].textAlignment="middle"),this.model.position.charAt(0).toLowerCase()!="r"||u||(this._outerTextDirection[i].textAlignment="start"),(this.model.position.charAt(0).toLowerCase()=="b"||this.model.position.charAt(0).toLowerCase()=="t")&&(this._outerTextDirection[i].textAlignment="middle"),this._outerTextElement,i==this._tickCount)if(this.ticks()[0]==this.value())continue;else this._outerTextElement=this._createSVGElements("text",{"stroke-width":.5,x:this._outerTextDirection[i].X2,y:this._outerTextDirection[i].Y2,id:this._prefix+this._elementID+"-dynamic-text","class":this._prefix+"dynamic-text",textContent:this.value(),"text-anchor":this._outerTextDirection[i].textAlignment});else this._outerTextElement=this._createSVGElements("text",{"stroke-width":.5,x:this._outerTextDirection[i].X2,y:this._outerTextDirection[i].Y2,"class":this._prefix+"ticks-text",textContent:this.ticks()[f],"text-anchor":this._outerTextDirection[i].textAlignment});this._textGroupElement.append(this._outerTextElement)}this._radialSliderWrapper.append(this._radialSVG.append(this._textGroupElement).append(this._circleGroupElement));this.model.autoOpen?this.element.css("display","block"):this.element.css("display","none");this._wireEvents(!1)},_initialization:function(){this.element.addClass(this.model.cssClass);this._svgLink="http://www.w3.org/2000/svg";this._startXY=this.model.radius;this._diameter=2*this.model.radius;this.model.radius=this._startXY;this._elementID=this.element.attr("id");this._tickCount=this.ticks().length;this._radialWidth=this._diameter;this.model.position.charAt(0).toLowerCase()=="r"&&(this._startAngle=90,this._endAngle=270);this.model.position.charAt(0).toLowerCase()=="l"&&(this._startAngle=270,this._endAngle=450);this.model.position.charAt(0).toLowerCase()=="t"&&(this._startAngle=0,this._endAngle=180);this.model.position.charAt(0).toLowerCase()=="b"&&(this._startAngle=180,this._endAngle=360);this._radialSliderWrapper=t.buildTag("div",{},{},{"class":this._prefix+"radail-slider-wrapper"});this.element.append(this._radialSliderWrapper);this._positionRadial()},resize:function(){var n=this;window.setTimeout(function(){n._positionRadial()},t.isAndroid()?200:0)},_documentClick:function(t){n(n(t.target).closest("svg.e-m-rs-svg")).length==0&&this._docClick&&(this.hide(),this._docClick=!1);this.element.hasClass("e-m-slider-show")&&(this._docClick=!0)},_positionRadial:function(){this.model.position.charAt(0).toLowerCase()=="r"&&this.element.addClass("e-m-radialright").css("right",-this._startXY+"px");(this.model.position=="rightcenter"||this.model.position=="leftcenter")&&this.element.css({top:window.innerHeight/2-this.model.radius+"px"});this.model.position.charAt(0).toLowerCase()=="b"&&(this.element.css({bottom:-this.model.radius+"px"}),this.model.position=="bottomcenter"&&this.element.css({left:window.innerWidth/2-this.model.radius+"px"}),this.model.position=="bottomright"&&this.element.css({right:"10px"}),this.model.position=="bottomleft"&&this.element.css({left:"10px"}));this.model.position.charAt(0).toLowerCase()=="t"&&(this.element.css({top:-this.model.radius+"px"}),this.model.position=="topcenter"?this.element.css({left:window.innerWidth/2-this.model.radius+"px"}):this.model.position=="topleft"?this.element.css({left:"10px"}):this.model.position=="topright"&&this.element.css("right","10px"));this.model.position.charAt(0).toLowerCase()=="l"&&this.element.css({left:-this.model.radius+"px"})},_pathDirection:function(n,t,i,r,u,f,e,o,s,h,c,l){return r=l?0:r,["M",n,t,"A",i,i,"0",r,"1",u,f,"L",e,o,"A",s,s,"1",r,"0",h,c,"z"].join(" ")},_radialCircleCalculation:function(n,t,i){var f=this.model.renderMode=="ios7"?6.5:this.model.renderMode=="windows"?2.5:5,e=this._startAngle+f,o=e+(this._endAngle-f-(this._startAngle+f))/(i-1),u,y,h,p,r,a,v;for(this._point=(this._endAngle-f-(this._startAngle+f))/(i-1),u=o,y=u,this._degPoint=[],this._degPoint.push(e),h=0;h<i;h++)p={},e=e*Math.PI/180,o=o*Math.PI/180,this._degPoint.push(u),e=u,o=u+this._point,u+=this._point;for(r=0;r<i;r++){var s=r+1,c=r==this.ticks().length-1?this.ticks()[this.ticks().length-1]+.5:this.ticks()[s],l=r==0?this.ticks()[r]-.5:this.ticks()[r],w=l<=0&&l>=this.value()?!0:!1,b=c<=0&&c<=this.value()?!0:!1,k=w&&b?!0:l<=this.value()&&this.value()<c;k&&(this.ticks()[r]!=this.value()?(a=this._point/(this.ticks()[s]-this.ticks()[r]),v=this.ticks()[s]-this.value(),this._startValueAngle=this._degPoint[s]-a*v,this._radialSliderCircle()):(this._startValueAngle=this._degPoint[r],this._radialSliderCircle()))}},_radialSliderCircle:function(){var r=this._calculateCircleDirection(this._startAngle,this.model.renderMode=="windows"||this.model.renderMode=="flat"?this._startValueAngle-2.5:this._startValueAngle),i,n;this._fillCircleElement=this._createSVGElements("path",{d:r,"class":this._prefix+"rs-fill-circle"});this._circleGroupElement.append(this._fillCircleElement);r=this._calculateCircleDirection(this.model.renderMode=="windows"||this.model.renderMode=="flat"?this._startValueAngle+2.5:this._startValueAngle,this._endAngle);this._radialCircleDefault=this._createSVGElements("path",{d:r,"class":this._prefix+"rs-circle-default"});this._circleGroupElement.append(this._radialCircleDefault);this.model.renderMode=="windows"||this.model.renderMode=="flat"?(r=this._calculateCircleDirection(this._startValueAngle-2.5,this._startValueAngle+2.5),this._markerElement=this._createSVGElements("path",{d:r,"class":this._prefix+"rs-marker","stroke-width":t.isFlat()?this.model.strokeWidth-5:"none"}),this._circleGroupElement.append(this._markerElement)):(i=.5*Math.min(this._diameter,this._diameter),i=i-(i-this._arcRadius)/2,this.model.renderMode=="android"?(n=this._polarToCartesian(this._startXY,this._startXY,i,this._startValueAngle),this._smallCircle=this._createSVGElements("circle",{"class":"e-m-rs-marker",cx:n.x,cy:n.y,r:6}),this._outerSmallCircel=this._createSVGElements("circle",{"class":"e-m-marker-large",cx:n.x,cy:n.y,r:15}),this._circleGroupElement.append(this._outerSmallCircel).append(this._smallCircle)):(this._iosMarkerElement=t.buildTag("div.e-m-rs-marker"),n=this._polarToCartesian(this._startXY,this._startXY,i,this._startValueAngle),this._radialSliderWrapper.append(this._iosMarkerElement),this._iosMarkerElement.css({left:n.x-15,top:n.y-15})))},_refresh:function(){this._destroy();this.element.removeAttr("style");this.element.addClass("e-m-radialslider e-js");this._renderEJMControl()},_createDelegates:function(){this._documentDelegate=n.proxy(this._documentClick,this);this._touchStartDelegate=n.proxy(this._touchStartHandler,this);this._markerMoveDelegate=n.proxy(this._markerMoveHandler,this);this._touchEndDelegate=n.proxy(this._touchEndHandler,this)},_wireEvents:function(i){this._createDelegates();t.listenEvents([this._radialSVG],[t.startEvent()],[this._touchStartDelegate],i);t.listenEvents([n(document)],["click"],[this._documentDelegate],i);this.model.renderMode=="ios7"&&t.listenEvents([this._iosMarkerElement],[t.startEvent()],[this._touchStartDelegate],i)},_touchEndHandler:function(n){n=n.touches?n.changedTouches[0]:n;t.listenEvents([this._radialSVG],[t.moveEvent()],[this._markerMoveDelegate],!0);this.model.renderMode=="ios7"&&t.listenEvents([this._iosMarkerElement],[t.moveEvent()],[this._markerMoveDelegate],!0);this._tapHandlerEvent(n);this._docClick=!1},_touchStartHandler:function(n){t.blockDefaultActions(n);t.isTouchDevice()&&(n=n.touches?n.touches[0]:n);this.model.start&&this._trigger("start",{value:this.value()});t.listenEvents([this._radialSVG,this._radialSVG],[t.moveEvent(),t.endEvent()],[this._markerMoveDelegate,this._touchEndDelegate],!1);this.model.renderMode=="ios7"&&t.listenEvents([this._iosMarkerElement,this._iosMarkerElement],[t.moveEvent(),t.endEvent()],[this._markerMoveDelegate,this._touchEndDelegate],!1)},_markerMoveHandler:function(i){var r,f;t.blockDefaultActions(i);t.isTouchDevice()&&(i=i.touches?i.touches[0]:i);var u=n("#"+this._prefix+this._elementID+"-radial-slider-svg").offset(),e=i.clientY,o=i.clientX,s=u.top+this._radialWidth/2,h=u.left+this._radialWidth/2;this._dynamicAngleCalculation(h,o,s,e);r=this.model.renderMode=="ios7"?6.5:this.model.renderMode=="windows"?2.5:5;(this.model.position.charAt(0).toLowerCase()=="l"&&(this._angle>270+r||this._angle<90-r)||this.model.position.charAt(0).toLowerCase()!="l"&&this._angle>360-(this._endAngle-r)&&this._angle<360-(this._startAngle+r))&&(this.model.renderMode=="windows"||this.model.renderMode=="flat"?this._dynamicWindowsRadial():this._dynamicIOSandAndroidRadial(),this.model.slide&&(f=this._ticksCalculation(),this._trigger("slide",{value:f,selectedValue:this.value()})))},_calculateCircleDirection:function(n,t,i){var r=.5*Math.min(this._diameter,this._diameter),u=r-this.model.strokeWidth,f,e,o,s,h,c,l,a,v,y;return this._arcRadius=u,n=n*Math.PI/180,t=t*Math.PI/180,v=t-n<Math.PI?0:1,y=(n+t)/2,f=this._startXY+r*Math.cos(n),e=this._startXY+r*Math.sin(i?-n:n),o=this._startXY+r*Math.cos(t),s=this._startXY+r*Math.sin(i?-t:t),h=this._startXY+u*Math.cos(n),c=this._startXY+u*Math.sin(i?-n:n),l=this._startXY+u*Math.cos(t),a=this._startXY+u*Math.sin(i?-t:t),leftPosition=!1,(this.model.position.charAt(0).toLowerCase()=="l"||this.model.position.charAt(0).toLowerCase()=="b")&&(leftPosition=!0),this._pathDirection(f,e,r,v,o,s,l,a,u,h,c,leftPosition)},_dynamicWindowsRadial:function(){var t=this._calculateCircleDirection(360-this._startAngle,this._angle+2.5,!0,!0);n(this._fillCircleElement).attr("d",t);t=this._calculateCircleDirection(this._angle+2.5,this._angle-2.5,!0,!1);n(this._markerElement).attr("d",t);t=this._calculateCircleDirection(this._angle-2.5,360-this._endAngle,!0,!1,!0);n(this._radialCircleDefault).attr("d",t)},_dynamicIOSandAndroidRadial:function(){var r=this._calculateCircleDirection(this._angle,360-this._endAngle,!0,!1,!0),i,t;n(this._radialCircleDefault).attr("d",r);r=this._calculateCircleDirection(360-this._startAngle,this._angle,!0,!0);n(this._fillCircleElement).attr("d",r);i=.5*Math.min(this._diameter,this._diameter);i=i-(i-this._arcRadius)/2;this.model.renderMode=="android"?(t=this._polarToCartesian(this._startXY,this._startXY,i,this._angle,!0),n(this._smallCircle).attr({cx:t.x,cy:t.y}),n(this._outerSmallCircel).attr({cx:t.x,cy:t.y})):(t=this._polarToCartesian(this._startXY,this._startXY,i,this._angle,!0),this._iosMarkerElement.css({left:t.x-15,top:t.y-15}))}});t.mobile.RadialSlider.Position={RightCenter:"rightcenter",RightTop:"righttop",RightBottom:"rightbottom",LeftCenter:"leftcenter",LeftTop:"lefttop",LeftBottom:"leftbottom",TopLeft:"topleft",TopRight:"topright",TopCenter:"topcenter",BottomLeft:"bottomleft",BottomRight:"bottomright",BottomCenter:"bottomcenter"};n.extend(!0,t.mobile.RadialSlider.prototype,t.RadialSliderBase.prototype)}(jQuery,Syncfusion)});
