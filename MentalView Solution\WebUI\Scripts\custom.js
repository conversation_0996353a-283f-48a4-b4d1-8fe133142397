﻿function SetLocalization() {
    ej.Grid.Locale["el-GR"] = {
        EmptyRecord: "Δεν βρέθηκαν αποτελέσματα",
        ConfirmDelete: "Είστε σίγουρος ότι θέλετε να γίνει η διαγραφή;",
        DeleteOperationAlert: "Δεν έχετε επιλέξει εγγραφή την οποία θέλετε να διαγράψετε.",
        EditOperationAlert: "Δεν έχετε επιλέξει εγγραφή την οποία θέλετε να επεξεργαστείτε.",
        SaveButton: "Αποθήκευση",
        CancelButton: "Ακύρωση",
        Add: "Δημιουργία",
        Edit: "Επεξεργασία",
        Delete: "Διαγραφή",
        Update: "Αποθήκευση",
        Cancel: "Ακύρωση",
        EditFormTitle: "Λεπτομέρειες του #",
        AddFormTitle: "Νέα εγγραφή"
    };
    $.extend($.validator.messages, {
        required: "Το πεδίο είναι υποχρεωτικό"

    });
    ej.Pager.Locale["el-GR"] = {
        pagerInfo: "{0} από {1} σελίδες ({2} εγγραφές)",
        firstPageTooltip: "Πρώτη σελίδα",
        lastPageTooltip: "Τελευταία σελίδα",
        nextPageTooltip: "Επόμενη σελίδα",
        previousPageTooltip: "Προηγούμενη σελίδα"
    };
    ej.DropDownList.Locale["el-GR"] = {
        emptyResultText: "Δεν βρέθηκαν αποτελέσματα" //replace with your text  
    };
    
    ej.Schedule.Locale["el-GR"] = {
        //ReminderWindowTitle: "Fenêtre de rappel",
        //CreateAppointmentTitle: "créer un rendez-",
        //RecurrenceEditTitle: "Modifier répétition nomination",
        //RecurrenceEditMessage: "Comment voulez-vous changer le cas dans la série?",
        //RecurrenceEditOnly: "Seulement cette nomination",
        //RecurrenceEditSeries: "La série entière",
        //PreviousAppointment: "Nomination précédente",
        //NextAppointment: "prochain rendez-vous",
        //AppointmentSubject: "sujet",
        //StartTime: "Heure de début",
        //EndTime: "Heure de fin",
        AllDay: "Ημέρα",
        Today: "Σήμερα",
        //Recurrence: "répétition",
        //Done: "Terminé",
        Cancel: "Άκυρο",
        Ok: "Ok",
        //RepeatBy: "Répétez par",
        //RepeatEvery: "répéter chaque",
        //RepeatOn: "répéter l'opération sur",
        //StartsOn: "démarre sur",
        //Ends: "extrémités",
        //Summary: "résumé",
        //Daily: "quotidien",
        //Weekly: "hebdomadaire",
        //Monthly: "mensuel",
        //Yearly: "annuel",
        Every: "κάθε",
        //EveryWeekDay: "chaque jour de la semaine",
        Never: "Ποτέ",
        //After: "après",
        //Occurrence: "apparition",
        //On: "sur",
        //Edit: "Modifier",
        //RecurrenceDay: "Jour (s)",
        //RecurrenceWeek: "Semaine (s)",
        //RecurrenceMonth: "Mois (s)",
        //RecurrenceYear: "Année (s)",
        //The: "la",
        //OfEvery: "de chaque",
        //First: "première",
        //Second: "deuxième",
        //Third: "troisième",
        //Fourth: "quatrième",
        //Last: "dernier",
        //WeekDay: "jour de la semaine",
        //WeekEndDay: "Jour de week-end",
        //Subject: "sujet",
        //Categorize: "Catégories",
        //DueIn: "En raison",
        //DismissAll: "rejeter tout",
        //Dismiss: "rejeter",
        //OpenItem: "Ouvrir l'élément",
        //Snooze: "répétition",
        Day: "Ημέρα",
        Week: "Εβδομάδα",
        WorkWeek: "Εργ. Εβδομ.",
        Month: "Μήνα",
        //AddEvent: "Ajouter événement",
        //CustomView: "Vue personnalisée",
        Agenda: "Ατζεντα",
        //Detailed: "détaillé",
        //EventBeginsin: "Nomination commence dans",
        //Editevent: "Modifier nomination",
        //Editseries: "Modifier série",
        //Times: "fois",
        //Until: "jusqu'à",
        //Eventwas: "rendez-vous était",
        //Hours: "hrs",
        //Minutes: "minutes",
        //Overdue: "en retard",
        //Days: "jour (s)",
        Event: "συμβάν",
        //Select: "sélectionner",
        Previous: "προηγούμενο",
        Next: "επόμενο",
        //Close: "proche",
        //Delete: "effacer",
        Date: "ημερομηνία",
        //Showin: "montrer en",
        //Gotodate: "Aller à la date",
        //Resources: "RESSOURCES",
        //RecurrenceDeleteTitle: "Supprimer répétition rendez-",
        //Location: "emplacement",
        //Priority: "priorité",
        //RecurrenceAlert: "alerte",
        //WrongPattern: "Le modèle de récurrence est pas valable",
        //CreateError: "La durée de la nomination doit être plus courte que la façon dont elle se produit fréquemment. Raccourcir la durée ou changer le modèle de récurrence dans la boîte de dialogue Récurrence de rendez.",
        //DragResizeError: "Impossible de replanifier une occurrence du rendez-vous récurrent. si elle saute sur une occurrence ultérieure du même rendez-vous.",
        //StartEndError: "L'heure de fin doit être supérieur à l'heure de début",
        //MouseOverDeleteTitle: "supprimer un rendez-",
        //DeleteConfirmation: "Êtes-vous sûr de vouloir supprimer ce rendez-vous?",
        Time: "ώρα",
        "": ""
    };
    
    ej.addCulture("el-GR", {
        name: "el-GR",
        englishName: "Greek (Greece)",
        nativeName: "Ελληνικά (Ελλάδα)",
        language: "el",
        numberFormat: {
            pattern: ["-n"], ",": " ", ".": ",", groupSizes: [3], NaN: "Non numérique",
            negativeInfinity: "-Άπειρο",
            positiveInfinity: "+Άπειρο",
            percent: { pattern: ["-n%", "n%"], groupSizes: [3], ",": " ", ".": ",", symbol: "%" },
            currency: { pattern: ["-n €", "n €"], groupSizes: [3], ",": " ", ".": ",", symbol: "€" }
        },
        calendars: {
            standard: {
                "/": "/", ":": ":",
                firstDay: 1,
                days: {
                    names: ["Κυριακή", "Δευτέρα", "Τρίτη", "Τετάρτη", "Πέμπτη", "Παρασκευή", "Σάββατο"],
                    namesAbbr: ["Κυρ.", "Δευ.", "Τρι.", "Τετ.", "Πεμ.", "Παρ.", "Σαβ."],
                    namesShort: ["Κυ", "Δε", "Τρ", "Τε", "Πε", "Πα", "Σα"]
                }, months: {
                    names: ["Ιανουάριος", "Φεβρουάριος", "Μάρτιος", "Απρίλιος", "Μάιος", "Ιούνιος", "Ιούλιος", "Αύγουστος", "Σεπτέμβριος", "Οκτώβριος", "Νοέμβριος", "Δεκέμβριος", ""],
                    namesAbbr: ["Ιαν.", "Φεβ.", "Μαρτ.", "Απρ.", "Μάι.", "Ιούν.", "Ιούλ.", "Αύγ.", "Σεπ.", "Οκτ.", "Νοεμ.", "Δεκ.", ""]
                }, AM: null, PM: null, eras: [{ name: "ap. J.-C.", start: null, offset: 0 }], patterns: { d: "dd/MM/yyyy", D: "dddd d MMMM yyyy", t: "HH:mm", T: "HH:mm:ss", f: "dddd d MMMM yyyy HH:mm", F: "dddd d MMMM yyyy HH:mm:ss", M: "d MMMM" }
            }
        }
    });;
    
}