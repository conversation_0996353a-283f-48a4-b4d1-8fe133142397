/*!
*  filename: ej.kanban.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.globalize.min","jquery-validation","jsrender","./../common/ej.core.min","./../common/ej.data.min","./../common/ej.touch.min","./../common/ej.draggable.min","./../common/ej.scroller.min","./ej.button.min","./ej.waitingpopup.min","./ej.datepicker.min","./ej.datetimepicker.min","./ej.dropdownlist.min","./ej.dialog.min","./ej.editor.min","./ej.toolbar.min","./ej.rte.min","./ej.menu.min"],n):n()})(function(){var c=function(){function n(n){this.kanbanObj=null;this.kanbanObj=n}return n.prototype._kbnTimeoutSearch=function(n,t){var i=n.data("ejKanban"),r;r=t.currentTarget;i._searchTout&&(i._searchTout=window.clearInterval(i._searchTout));i._searchTout=window.setInterval(function(){$(".e-kanbanwaitingpopup").removeClass("e-kanbanwaitingpopup").addClass("e-kbnsearchwaitingpopup");n.data("ejWaitingPopup").show();$(".e-kbnsearchwaitingpopup").css({top:"1px",left:"1px"});var u=setTimeout(function(){var f=$(r).find("input").val(),e,o;ej.isNullOrUndefined(f)&&(f=$(r).val());e={itemName:r.title,itemId:r.id,target:r,currentTarget:r,itemIndex:$(r).index(),toolbarData:t,itemText:f};i._trigger("toolbarClick",e);i.KanbanFilter.searchCards(f);i.element.find(".e-searchbar .e-search.e-tooltxt").addClass("e-highliht-kbnsearchbar");$(".e-kbnsearchwaitingpopup").addClass("e-kanbanwaitingpopup").removeClass("e-kbnsearchwaitingpopup").css({top:"0px",left:"0px"});ej.isNullOrUndefined(i.model.fields.swimlaneKey)||(o=(new ej.Query).where(ej.Predicate.or(i.keyPredicates)).select(i.model.fields.swimlaneKey),i._kbnAdaptDdlData=new ej.DataManager(i._currentJsonData).executeLocal(o),i._kbnAdaptDdlData=ej.dataUtil.mergeSort(ej.dataUtil.distinct(i._kbnAdaptDdlData)),i.KanbanAdaptive._adaptiveSwimlaneRefresh());n.data("ejWaitingPopup").hide();clearTimeout(u)},300);i._searchTout=window.clearInterval(i._searchTout)},1e3)},n.prototype._kbnRightSwipe=function(){var n=this.kanbanObj,h=n.element.width(),l=n.element.find(".e-rowcell:visible"),o,r=n.element.find(".e-stackedHeaderCell"),f=0,u,e,i,a=n,t,c,s;if(n._kbnSwipeCount>0){for(n._kbnSwipeCount==1?n._kbnSwipeWidth=0:(i=l.eq(n._kbnSwipeCount-1),i.length==0&&(i=n.element.find(".e-headercell:visible").not(".e-stackedHeaderCell").eq(n._kbnSwipeCount-1)),n._kbnSwipeWidth=n._kbnSwipeWidth-i.offset().left,window.matchMedia("(max-width: 600px)").matches||window.matchMedia("(max-device-width: 800px)").matches?o=(h-i.width())/2:(window.matchMedia("(max-width: 801px)").matches||window.matchMedia("(max-device-width: 1200px)").matches)&&(o=(h-i.width())/8),n._kbnSwipeWidth=n._kbnSwipeWidth+o),t=0;t<r.length;t++)if(c=n.headerContent.find("col:eq(0)").width(),u=f,e=n._kbnSwipeCount-1,f=f+parseInt($(r).eq(t).attr("colspan")),e>=u&&e<f)if(u==n._kbnSwipeCount-1){n.element.find(".e-adapt-stheader").removeClass("e-adapt-stheader");$(r).eq(t).find("div").css({position:"",left:""});s=$(r).eq(t).prev(".e-stackedHeaderCell");s.length>0&&s.addClass("e-adapt-stheader");break}else if(u<n._kbnSwipeCount-1){$(r).eq(t).find("div").css({position:"relative",left:c*(n._kbnSwipeCount-1-u)+8*(e-1)});break}n._kbnSwipeCount>1&&(n._kbnSwipeWidth=n._kbnSwipeWidth+n.element.offset().left);n.headerContent.find("table").css({transform:"translate3d("+n._kbnSwipeWidth+"px, 0px, 0px)","transition-duration":""});n.kanbanContent.find("table").eq(0).css({transform:"translate3d("+n._kbnSwipeWidth+"px, 0px, 0px)","transition-duration":""});--n._kbnSwipeCount}},n.prototype._kbnLeftSwipe=function(){var n=this.kanbanObj,e=n.element.width(),l=n.element.find(".e-rowcell:visible"),o,r=n.element.find(".e-stackedHeaderCell"),s=0,f,u,i,a=n,c,t,h;if((window.matchMedia("(max-width: 600px)").matches||window.matchMedia("((max-device-width: 800px)").matches)&&n._kbnSwipeCount<n.model.columns.length-1||(window.matchMedia("(max-width: 801px)").matches||window.matchMedia("(max-device-width: 1200px)").matches)&&n._kbnSwipeCount<n.model.columns.length-2){for(i=l.eq(n._kbnSwipeCount+1),i.length==0&&(i=n.element.find(".e-headercell:visible").not(".e-stackedHeaderCell").eq(n._kbnSwipeCount+1)),n._kbnSwipeWidth=n._kbnSwipeWidth-i.offset().left,window.matchMedia("(max-width: 600px)").matches||window.matchMedia("(max-device-width: 800px)").matches?o=n._kbnSwipeCount==n.model.columns.length-2?e-i.width():(e-i.width())/2:(window.matchMedia("(max-width: 801px)").matches||window.matchMedia("(max-device-width: 1200px)").matches)&&(n._kbnSwipeCount==n.model.columns.length-3?(c=parseInt(n.contentTable.css("border-spacing").split("px")[0]),o=e-(i.width()+c)*2):o=(e-i.width())/8),n._kbnSwipeWidth=n._kbnSwipeWidth+o,t=0;t<r.length;t++)if(h=n.headerContent.find("col:eq(0)").width(),f=s,u=n._kbnSwipeCount+1,s=s+parseInt($(r).eq(t).attr("colspan")),u>f&&u<s)if(t==0){$(r).eq(t).addClass("e-adapt-stheader");$(r).eq(t).find("div").css({position:"relative",left:h*u+8*(u-1)});break}else if(f==n._kbnSwipeCount+1){n.element.find(".e-adapt-stheader").removeClass("e-adapt-stheader");$(r).eq(t-1).find("div").css({position:"",left:""});$(r).eq(t).addClass("e-adapt-stheader");break}else if(f<n._kbnSwipeCount+1){$(r).eq(t).find("div").css({position:"relative",left:h*(n._kbnSwipeCount+1-f)+8*(u-1)});break}n._kbnSwipeWidth=n._kbnSwipeWidth+n.element.offset().left;n.headerContent.find("table").css({transform:"translate3d("+n._kbnSwipeWidth+"px, 0px, 0px)","transition-duration":""});n.kanbanContent.find("table").eq(0).css({transform:"translate3d("+n._kbnSwipeWidth+"px, 0px, 0px)","transition-duration":""});++n._kbnSwipeCount}},n.prototype._clearAdaptSearch=function(n){var u=this.kanbanObj,t,r=u.element,i=r.data("ejKanban");ej.isNullOrUndefined(n)||(t=$(n.target));ej.isNullOrUndefined(t)||t.hide();r.data("ejWaitingPopup").show();setTimeout(function(){var e,f,o;ej.isNullOrUndefined(t)||(f=t.prev(".e-input"));(ej.isNullOrUndefined(f)||f.length==0)&&(f=ej.isNullOrUndefined(t)?r.find(".e-kanbantoolbar .e-searchdiv > .e-input"):t.next(".e-search").find("input"));e=ej.isNullOrUndefined(t)?{itemText:f.val()}:{target:t,currentTarget:t,itemIndex:t.index(),toolbarData:n,itemText:f.val()};i._trigger("toolbarClick",e);u.KanbanFilter.searchCards("");u.element.find(".e-searchbar .e-search.e-tooltxt").removeClass("e-highliht-kbnsearchbar");ej.isNullOrUndefined(i.model.fields.swimlaneKey)||(o=(new ej.Query).where(ej.Predicate.or(i.keyPredicates)).select(i.model.fields.swimlaneKey),i._kbnAdaptDdlData=new ej.DataManager(i._currentJsonData).executeLocal(o),i._kbnAdaptDdlData=ej.dataUtil.mergeSort(ej.dataUtil.distinct(i._kbnAdaptDdlData)),i.KanbanAdaptive._adaptiveSwimlaneRefresh());r.data("ejWaitingPopup").hide()},300)},n.prototype._adaptiveKbnClick=function(n){var t=this.kanbanObj,i=$(n.target),r,s,e=t.element.find(".e-swimlane-window"),y,rt,ut,p,o,w,ft,c,l,h,et,u,g,nt,b,k,tt,a,v,ot,d,it,f;if(t.element.hasClass("e-responsive")){if(r=$(".e-kanbanfilter-window"),s=t.element.find(".e-kanbanfilter-icon"),i.hasClass("e-searchitem")&&(y=i.parents(".e-searchbar"),i.prev().show(),i.prev().children(":first").removeAttr("style"),y.siblings().hide(),y.find(".e-adapt-search").length==0&&(rt=ej.buildTag("div.e-icon e-adapt-search e-searchfind","",{}),ut=ej.buildTag("div.e-icon e-adapt-cancel e-cancel","",{}),i.siblings(".e-searchdiv").append(ut).prepend(rt)),i.parents(".e-search").css({border:""}),i.parents("body").addClass("e-kbnwindow-modal"),i.parents(".e-kanbantoolbar").addClass("e-adaptive-search"),y.find(".e-adapt-cancel").show(),i.hide(),n.type==="tap"&&n.preventDefault()),ej.browserInfo().name=="webkit"&&t.element.find(".e-adapt-cancel").addClass("e-webkitadapt"),ej.browserInfo().name=="msie"&&ej.browserInfo().version=="10.0"&&t.element.find(".e-adapt-cancel").addClass("e-msieadapt"),i.hasClass("e-adapt-cancel")&&(this._clearAdaptSearch(n),i.parents(".e-searchbar").siblings(".e-print,.e-kanbanfilter-icon,.e-customtoolbar").show(),p=i.parents(".e-search"),p.find(".e-searchdiv").hide(),p.find(".e-searchitem").show(),p.css({border:"none"}),i.parents("body").removeClass("e-kbnwindow-modal"),i.parents(".e-kanbantoolbar").removeClass("e-adaptive-search")),(i.hasClass("e-filter-done")||i.hasClass("e-clearfilter"))&&(h=i.text(),r.parents("body").removeClass("e-kbnwindow-modal"),i.hasClass("e-clearfilter")&&(h=i.val()),o={target:i,currentTarget:i,itemIndex:i.index(),toolbarData:n,itemText:h},t._trigger("toolbarClick",o),i.hasClass("e-clearfilter")?(t._kbnFilterCollection=[],t._kbnFilterObject=[],t._filterCollection=[],t._kbnAdaptFilterObject=[],i.hide(),s.removeClass("e-kbnclearfl-icon"),r.find(".e-kbnfilter-check").parents('[aria-checked~="true"]').click()):(s.hasClass("e-kbnclearfl-icon")||s.addClass("e-kbnclearfl-icon"),t._kbnFilterObject.length==0&&s.hasClass("e-kbnclearfl-icon")&&s.removeClass("e-kbnclearfl-icon")),o={},o.requestType=ej.Kanban.Actions.Filtering,o.currentFilterObject=[],o.filterCollection=t._kbnFilterCollection,o.currentFilterObject=t._kbnFilterObject,w=t._dataSource()instanceof ej.DataManager?t._dataSource().dataSource.json:t._dataSource(),w.length==0&&t._currentJsonData.length>0&&(w=t._currentJsonData),t._initialData=w,t.KanbanCommon._processBindings(o),ej.isNullOrUndefined(t.model.fields.swimlaneKey)||(t._currentJsonData.length==0?(t.element.find(".e-swimlane-ddl,.e-swimlane-window").remove(),t.kanbanContent.data("ejScroller").destroy()):(ft=(new ej.Query).where(ej.Predicate.or(t.keyPredicates)).select(t.model.fields.swimlaneKey),t._kbnAdaptDdlData=new ej.DataManager(t._currentJsonData).executeLocal(ft),t._kbnAdaptDdlData=ej.dataUtil.mergeSort(ej.dataUtil.distinct(t._kbnAdaptDdlData)),t.KanbanAdaptive._adaptiveSwimlaneRefresh())),r.hide(),t.kanbanWindowResize(),n.type==="tap"&&n.preventDefault()),i.hasClass("e-filterback-icon")){for(c=[],u=0;u<t.model.filterSettings.length;u++)c.push(t.model.filterSettings[u].text);for(u=0;u<t._kbnAdaptFilterObject.length;u++)h=t._kbnAdaptFilterObject[u].text,et=$.inArray(h,c),c.splice(et,1),l=r.find("label.e-filterlabel:contains("+h+")").prev(),l.attr("aria-checked")=="false"&&l.click();for(u=0;u<c.length;u++)l=r.find("label.e-filterlabel:contains("+c[u]+")").prev(),l.attr("aria-checked")=="true"&&l.click();r.hide();n.type==="tap"&&n.preventDefault()}i.hasClass("e-kanbanfilter-icon")&&(g=r.find(".e-clearfilter"),k=0,tt=r.find(".e-filter-scrollcontent"),e.is(":visible")&&e.hide(),r.show(),i.hasClass("e-kbnclearfl-icon")?g.show():g.hide(),nt=r.find(".e-kbnfilterwindow-head").height(),b=r.find(".e-clearfilter"),b.is(":visible")&&(tt.ejScroller({height:0,buttonSize:0,scrollerSize:9,enableTouchScroll:!0,autoHide:!0}),k=b.outerHeight()+parseInt(b.css("bottom"))),$(window).height()-k<r.find(".e-filter-content").height()+nt&&tt.ejScroller({height:$(window).height()-(nt+k+10),buttonSize:0,scrollerSize:9,enableTouchScroll:!0,autoHide:!0}),r.parents("body").addClass("e-kbnwindow-modal"),n.type==="tap"&&n.preventDefault());(i.attr("id")==this._id+"_Cancel"||i.parent().attr("id")==this._id+"_closebutton")&&t.KanbanEdit.cancelEdit();i.attr("id")==t._id+"_Save"&&t.KanbanEdit.endEdit()}a=t.element.find(".e-slwindow-scrollcontent");(i.hasClass("e-swimlane-item")||i.parents(".e-swimlane-item").length>0)&&(v=i,i.parents(".e-swimlane-item").length>0&&(v=i.parents(".e-swimlane-item")),v.parents(".e-swimlane-ul").find(".e-selected-item").removeClass("e-selected-item"),v.addClass("e-selected-item"),e=$("#"+t._id+"_slWindow"),t._kbnAdaptDdlIndex=t._freezeSlOrder=e.find(".e-swimlane-item").index(v),f=$("#"+t._id+"_toolbarItems"),f.find(".e-kbnhide").removeClass("e-kbnhide"),f.removeClass("e-kbntoolbar-body").prependTo(t.element),e.removeClass("e-kbnslwindow-body").appendTo(t.element).hide(),t.element.find(".e-swimlane-text").text(t._kbnAdaptDdlData[t._kbnAdaptDdlIndex]),ot=t.element[0].getElementsByClassName("e-columnrow"),t.kanbanWindowResize(),d=t.kanbanContent.data("ejScroller"),d.scrollY(0,!0),d.scrollY($(ot[t._kbnAdaptDdlIndex]).offset().top-t.kanbanContent.offset().top,!0),t._freezeScrollTop=d.scrollTop(),a.hasClass("e-scroller")&&a.data("ejScroller").refresh());(i.hasClass("e-swimlane-ddl")||i.parents(".e-swimlane-ddl").length>0)&&(it=36,e.is(":hidden")?(t.element.parents("body").addClass("e-kbnwindow-modal"),f=t.element.find(".e-kanbantoolbar"),f.children().not(".e-swimlane-ddl").addClass("e-kbnhide"),f.addClass("e-kbntoolbar-body").appendTo("body"),t.element.find(".e-swimlane-window").addClass("e-kbnslwindow-body").appendTo("body"),e.show(),$(window).height()<it+a.height()&&a.ejScroller({height:$(window).height()-it,buttonSize:0,scrollerSize:9,enableTouchScroll:!0,autoHide:!0})):(e.hide(),f=$("#"+t._id+"_toolbarItems"),f.find(".e-kbnhide").removeClass("e-kbnhide"),f.removeClass("e-kbntoolbar-body").prependTo(t.element),$("#"+t._id+"_slWindow").removeClass("e-kbnslwindow-body").appendTo(t.element).hide(),t.element.parents("body").removeClass("e-kbnwindow-modal")))},n.prototype._kbnFilterChange=function(n){var t=this,s=$(n.event.target).closest(".e-kanban"),l=s.data("ejKanban"),e=$(n.event.target).parents(".e-chkbox-wrap").next().text(),c,o,u,i,f,r,h;if(!t._kbnAutoFilterCheck){for(e==""&&(e=$(n.event.target).next().text()),c=s.find(".e-kanbanfilter-icon"),o=$(".e-kanbanfilter-window .e-filter-done"),o.is(":hidden")&&o.show(),i=0;i<t.model.filterSettings.length;i++)if(t.model.filterSettings[i].text==e)break;if(u=i==t.model.filterSettings.length?null:t.model.filterSettings[i],n.isChecked)for(t._kbnFilterObject.push(u),r=0;r<t.model.filterSettings.length;r++)t.model.filterSettings[r].text==u.text&&(h=t.model.filterSettings[r].query.queries[0].e,t._kbnFilterCollection.push(h));else f=$.inArray(u,t._kbnFilterObject),f>=0&&(t._kbnFilterObject.splice(f,1),t._kbnFilterCollection.splice(f,1))}},n.prototype._kbnAdaptSwimlaneData=function(){var n=this.kanbanObj,r=n,t,i;n.model.isResponsive&&(ej.isNullOrUndefined(n.model.minWidth)||n.model.minWidth==0)&&!ej.isNullOrUndefined(n.model.fields.swimlaneKey)&&(t=new ej.Query,ej.isNullOrUndefined(n.model.keyField)||n._addColumnFilters(),n.model.query._fromTable!=""&&t.from(n.model.query._fromTable),n._dataSource()instanceof ej.DataManager&&t.queries.length&&!n._dataManager.dataSource.offline?(i=n._dataSource().executeQuery(t),i.done(ej.proxy(function(i){this._kbnAdaptDdlData=new ej.DataManager(i.result);t=(new ej.Query).where(ej.Predicate.or(n.keyPredicates)).select(n.model.fields.swimlaneKey);n._kbnAdaptDdlData=n._kbnAdaptDdlData.executeLocal(t);n._kbnAdaptDdlData=ej.dataUtil.mergeSort(ej.dataUtil.distinct(n._kbnAdaptDdlData))}))):(t=(new ej.Query).where(ej.Predicate.or(n.keyPredicates)).select(n.model.fields.swimlaneKey),n._dataManager.dataSource.offline&&n._dataManager.dataSource.json.length&&(n._kbnAdaptDdlData=n._dataManager.executeLocal(t)),n._kbnAdaptDdlData=ej.dataUtil.mergeSort(ej.dataUtil.distinct(n._kbnAdaptDdlData))))},n.prototype._setResponsiveHeightWidth=function(){var n=this.kanbanObj;if(typeof n.model.scrollSettings.width=="string"?n._originalScrollWidth=n.element.width():n.model.scrollSettings.width>0&&(n._originalScrollWidth=n.model.scrollSettings.width),typeof n.model.scrollSettings.height=="string"?n._originalScrollHeight=n.getContent().height():n.model.scrollSettings.height>0&&(n._originalScrollHeight=n.model.scrollSettings.height),n.model.isResponsive){$(window).on("resize",$.proxy(n.kanbanWindowResize,n));n.kanbanWindowResize()}},n.prototype._renderResponsiveKanban=function(n,t,i,r,u){var f=this.kanbanObj,e;n?(f.model.scrollSettings.width=ej.isNullOrUndefined(f._originalScrollWidth)?Math.min(i,u):Math.min(f._originalScrollWidth,Math.min(i,u)),e=Math.min(r,t),e=ej.isNullOrUndefined(f._originalScrollHeight)?e:Math.min(f._originalScrollHeight,r),f.model.scrollSettings.height=e,f._originalScrollWidth<u&&(f.model.scrollSettings.width=Math.min(i,u)),f.KanbanScroll&&f.KanbanScroll._renderScroller()):(f.model.scrollSettings.width="100%",ej.isNullOrUndefined(f._originalScrollWidth)||(f.model.scrollSettings.width=Math.min(f._originalScrollWidth,i)),e=f.element.outerHeight(),ej.isNullOrUndefined(f._originalScrollHeight)||(e=Math.min(f._originalScrollHeight,r)),f.model.scrollSettings.height=e,f.KanbanScroll&&f.KanbanScroll._renderScroller())},n.prototype._columnTimeoutAdapt=function(){var t=this.kanbanObj,i,e,o,f,n,s,r,u;for(Math.floor(t.element.width())>$(window).width()&&(t.element.width($(window).width()-t.element.offset().left-5),t.headerContent.removeClass("e-scrollcss"),t.headerContent.find(".e-hscrollcss").removeClass("e-hscrollcss")),n=t,window.matchMedia("(max-width: 480px)").matches?i=t.element.width()*(80/100):window.matchMedia("(max-width: 600px)").matches||window.matchMedia("(max-device-width: 800px)").matches?i=t.element.width()*(60/100):(window.matchMedia("(max-width: 801px)").matches||window.matchMedia("(max-device-width: 1200px)").matches)&&(i=t.element.width()*(43/100)),e=t.element.find(".e-headercell"),o=t.element.find(".e-columnrow .e-rowcell"),t.kanbanContent.find("table:eq(0) > colgroup col").width(i),o.width(i),t.headerContent.find("table > colgroup col").width(i),e.not(".e-stackedHeaderCell").width(i),f=t.element.find(".e-stackedHeaderCell"),u=0;u<f.length;u++)$(f).eq(u).width(parseInt($(f).eq(u).attr("colspan"))*i);s=setTimeout(function(){var u,f,h;n._kbnSwipeCount>0&&(r=o.eq(n._kbnSwipeCount),r.length==0&&(r=e.not(".e-stackedHeaderCell").eq(t._kbnSwipeCount)),n._kbnSwipeWidth=n._kbnSwipeWidth-r.offset().left,u=n.element.width(),f=r.width(),window.matchMedia("(max-width: 600px)").matches||window.matchMedia("(max-device-width: 800px)").matches?i=n._kbnSwipeCount==n.model.columns.length-1?n.element.hasClass("e-swimlane-responsive")?u-(f+18):u-f:(u-f)/2:(window.matchMedia("(max-width: 801px)").matches||window.matchMedia("(max-device-width: 1200px)").matches)&&(n._kbnSwipeCount==n.model.columns.length-3?(h=parseInt(n.contentTable.css("border-spacing").split("px")[0]),eqWidth=u-(f+h)*2):i=(u-f)/8),n._kbnSwipeWidth=n._kbnSwipeWidth+i+t.element.offset().left,n.headerContent.find("table").css({transform:"translate3d("+n._kbnSwipeWidth+"px, 0px, 0px)","transition-duration":"0ms"}),n.kanbanContent.find("table").eq(0).css({transform:"translate3d("+n._kbnSwipeWidth+"px, 0px, 0px)","transition-duration":"0ms"}),clearTimeout(s))},2500)},n.prototype._addSwimlaneName=function(){for(var r,i,n,u,f=this.kanbanObj.element.find(".e-columnrow"),t=0;t<f.length;t++)for(r=f.eq(t).find(".e-rowcell"),i=0;i<r.length;i++)n=r.eq(i).find(".e-limits"),n.length==0&&(n=ej.buildTag("div.e-limits","",{},{})),n.find(".e-swimlane-name").length==0?(n.append(ej.buildTag("div.e-swimlane-name",this.kanbanObj._kbnAdaptDdlData[t],{},{})),r.eq(i).prepend(n)):(u=n.find(".e-swimlane-name"),u.text(this.kanbanObj._kbnAdaptDdlData[t]),u.show(),u.parents(".e-limits").show())},n.prototype._kbnAdaptSwimlaneDdl=function(){var n=this.kanbanObj,r=ej.buildTag("div.e-swimlane-ddl","",{}),o=n.model.fields.swimlaneKey,u,t,f,e=n.element.find(".e-kanbantoolbar"),i;for(n.element.addClass("e-swimlane-responsive"),o&&n.model.swimlaneSettings.headers.length>0&&(n._kbnAdaptDdlData=n._slText),ej.buildTag("div.e-swimlane-text",n._kbnAdaptDdlData[0]).appendTo(r),ej.buildTag("div.e-swimlane-arrow").appendTo(r),u=ej.buildTag("div#"+n._id+"_slWindow.e-swimlane-window","",{}),t=ej.buildTag("ul.e-swimlane-ul ","",{}),i=0;i<n._kbnAdaptDdlData.length;i++)ej.buildTag("li.e-swimlane-item ","<div>"+n._kbnAdaptDdlData[i]+"<\/div>",{}).appendTo(t);t.find(".e-swimlane-item:eq(0)").addClass("e-selected-item");this._addSwimlaneName();f=ej.buildTag("div#"+n._id+"_slScrollContent","<div><\/div>").addClass("e-slwindow-scrollcontent");f.children().append(t);f.appendTo(u);u.appendTo(n.element).hide();e.length==0&&(n._renderToolBar().insertBefore(n.element.find(".e-kanbanheader").first()),e=n.element.find(".e-kanbantoolbar").css("padding","0px").addClass("e-sladapt-bar"));e.prepend(r);n.element.find(".e-swimlanerow").hide()},n.prototype._kbnAdaptFilterWindow=function(){var n=this.kanbanObj,h=n.element.find(".e-kanbantoolbar"),i,e,u,o,c,r,s,t,f;for(h.find(".e-quickfilter").parent().hide(),r=ej.buildTag("div.e-kanbanfilter-window e-widget","",{}),i=ej.buildTag("div.e-kbnfilterwindow-head","",{}),ej.buildTag("div.e-filterback-icon","",{}).appendTo(i),ej.buildTag("div.e-text","FILTER",{}).appendTo(i),ej.buildTag("div.e-text e-filter-done","DONE",{}).appendTo(i).hide(),i.appendTo(r),$("body").append(r.hide()),e=ej.buildTag("div.e-kbnfilterwindow-body","",{}),o=ej.buildTag("div.e-filter-content"),t=0;t<n.model.filterSettings.length;t++)f=ej.buildTag("div","",{}),f.append("<input type='checkbox' class='e-kbnfilter-check' id='check"+t+"' /><label for='check"+t+"' class='e-filterlabel'>"+n.model.filterSettings[t].text+"<\/label><\/td>"),o.append(f),f.find("input").ejCheckBox({change:$.proxy(this._kbnFilterChange,n)});e.append(o);s=ej.buildTag("div#"+n._id+"_filterScrollContent","<div><\/div>").addClass("e-filter-scrollcontent");s.children().append(e);s.appendTo(r);u=ej.buildTag("input.e-clearfilter","",{},{type:"button",id:n._id+"_ClearFilter"});u.ejButton({text:"ClearFilter"});r.append(u);u.hide();c=ej.buildTag("div.e-kanbanfilter-icon","",{float:"right"});h.append(c)},n.prototype._setAdaptiveSwimlaneTop=function(){var n=this.kanbanObj,i,r,t;n.element.hasClass("e-responsive")&&!ej.isNullOrUndefined(n.model.fields.swimlaneKey)&&(i=n.kanbanContent.data("ejScroller"),r=n.element[0].getElementsByClassName("e-columnrow"),t=n._freezeScrollTop,i.scrollY(t,!0),n._freezeScrollTop=t)},n.prototype._adaptiveSwimlaneRefresh=function(){var n=this.kanbanObj,f,t,i,r,u;if(n.element.find(".e-swimlane-window .e-swimlane-ul").empty(),t=n.model.swimlaneSettings.headers,t.length>0){for(i=0;i<t.length;i++)r=n._kbnAdaptDdlData.indexOf(t[i].key),r==-1?ej.isNullOrUndefined(t[i].text)?typeof t[i].key=="string"&&t[i].key.length>0?n._kbnAdaptDdlData.push(t[i].key):!1:n._kbnAdaptDdlData.push(t[i].text):ej.isNullOrUndefined(t[i].text)||(n._kbnAdaptDdlData[r]=n._kbnAdaptDdlData.push(t[i].text));n._kbnAdaptDdlData=ej.dataUtil.mergeSort(n._kbnAdaptDdlData)}for(r=0;r<n._kbnAdaptDdlData.length;r++)ej.buildTag("li.e-swimlane-item ","<div>"+n._kbnAdaptDdlData[r]+"<\/div>",{}).appendTo(n.element.find(".e-swimlane-window .e-swimlane-ul"));if(n.KanbanAdaptive&&n.element.hasClass("e-responsive")&&!ej.isNullOrUndefined(n.model.fields.swimlaneKey)){for(n.KanbanAdaptive._addSwimlaneName(),f=n.kanbanContent.find(".e-columnrow"),u=0;u<f.length;u++)if(f.eq(u).offset().top>=0){n.element.find(".e-swimlane-text").text(n._kbnAdaptDdlData[u]);break}n.kanbanContent.hasClass("e-scroller")&&n.kanbanContent.data("ejScroller").refresh()}},n.prototype._removeKbnAdaptItems=function(){var n=this.kanbanObj,u,nt,l,a,f,v,tt,e,y,p,w,t,b,s,o,k;if(n.element.hasClass("e-responsive")){var i=n.element.find(".e-kanbantoolbar"),d=n.element.find(".e-stackedHeaderCell"),h,g=n.element.find(".e-swimlane-ddl"),c=$(".e-kanbanfilter-window"),r;if(g.length>0&&(g.remove(),n.element.find(".e-swimlane-window").remove()),i.hasClass("e-sladapt-bar")&&i.remove(),c.length>0){for(u=c.find(".e-filter-content").children(),t=0;t<u.length;t++)u.eq(t).find("span.e-chkbox-wrap").attr("aria-checked")=="true"&&(nt=u.index(u.eq(t)),n.element.find(".e-quickfilter").nextAll("li.e-tooltxt").eq(nt).addClass("e-select"));c.remove()}if(d.length>0&&d.width(""),n.element.find(".e-rowcell,.e-headercell").width(""),n.kanbanContent.find("table:eq(0) > colgroup col").width(""),n.headerContent.find("table > colgroup col").width(""),!n.element.hasClass("e-swimlane-responsive"))for(h=n.element.find(".e-rowcell .e-cell-scrollcontent:visible"),t=0;t<h.length;t++)l=$(h[t]).data("ejScroller"),ej.isNullOrUndefined(l)||l.destroy();for(i.length>0&&i.is(":visible")&&(i.css("display","block"),a=i.find(".e-quickfilter"),f=i.find(".e-searchbar"),i.find(".e-adapt-cancel").is(":visible")&&f.find(".e-searchitem").addClass("e-cancel").removeClass("e-searchfind"),i.find(".e-adapt-cancel,.e-adapt-search").hide(),a.length>0&&(i.find(".e-kanbanfilter-icon").remove(),a.parent(".e-ul.e-horizontal").show()),v=f.find(".e-searchdiv"),v.length>0&&(v.show(),f.find(".e-search").css("border",""),f.find(".e-searchitem").show(),i.parents("body").removeClass("e-kbnwindow-modal"),i.removeClass("e-adaptive-search"))),n.headerContent.find("table").css({transform:"","transition-duration":""}),n.kanbanContent.find("table").eq(0).css({transform:"","transition-duration":""}),r=n.element.find(".e-cell-scrollcontent"),t=0;t<r.length;t++)tt=r.eq(t).children(),tt.children().appendTo(r.eq(t).parents(".e-rowcell")),r.eq(t).remove();if(e=n.element.find(".e-kanbandialog"),e.is(":visible")&&(y=e.parents(".e-dialog-scroller"),p=e.parents(".e-dialog"),p.css({top:"",left:""}).removeClass("e-kbnadapt-editdlg"),y.css("height","auto"),y.ejScroller({height:0,buttonSize:0,scrollerSize:9,enableTouchScroll:!0,autoHide:!0}),e.parents("body").removeClass("e-kbnwindow-modal"),p.hide(),n.KanbanEdit._onKbnDialogOpen()),!ej.isNullOrUndefined(n.model.fields.swimlaneKey))for(n.kanbanContent.hasClass("e-scroller")&&n.kanbanContent.data("ejScroller").destroy(),n.element.parents("body").removeClass("e-kbnwindow-modal"),n.element.find(".e-swimlanerow").show(),w=n.element.find(".e-columnrow"),t=0;t<w.length;t++)for(b=w.eq(t).find(".e-rowcell"),s=0;s<b.length;s++)o=b.eq(s).find(".e-swimlane-name"),o.length>0&&(o.siblings().length==0?o.parents(".e-limits").hide():o.hide());k=n.element.find(".e-adapt-stheader");k.length>0&&k.find("div").css({position:"",left:""});n.element.removeClass("e-responsive");n.element.removeClass("e-swimlane-responsive")}},n.prototype._setAdaptEditWindowHeight=function(){var r=this.kanbanObj,n,t,i;n=$("#"+r._id+"_dialogEdit");t=n.parents(".e-dialog-scroller");i;n.parents(".e-dialog").css({top:"0",left:"0"}).addClass("e-kbnadapt-editdlg");n.parents("body").addClass("e-kbnwindow-modal");i=t.prev(".e-titlebar");t.ejScroller({height:$(window).height()-i.outerHeight(),buttonSize:0,scrollerSize:9,enableTouchScroll:!0,autoHide:!0});t.data("ejScroller").refresh();n.css("height",t.height())},n}(),n,t,r,u,f,e,o,s,h;window.ej.createObject("ej.KanbanFeatures.Adaptive",c,window);n=function(){function n(n){this.kanbanObj=null;this.kanbanObj=n}return n.prototype._initScrolling=function(){var n=this.kanbanObj;(n.model.width||n.model.height)&&(n.model.allowScrolling=!0,n.model.width&&(n.model.scrollSettings.width=n.model.width),n.model.height&&(n.model.scrollSettings.height=n.model.height));n._originalScrollWidth=n.model.scrollSettings.width},n.prototype._renderScroller=function(){var n=this.kanbanObj,r,u,f,t,i,e;n.model.scrollSettings||(n.model.scrollSettings={});typeof n._originalScrollWidth=="string"&&(n.element.css("width","auto"),t=n.element.width(),n.model.scrollSettings.width=="auto"||n._originalScrollWidth=="auto"||n.model.scrollSettings.width==="100%"?(n._originalScrollWidth="100%",n.model.scrollSettings.width="auto"):n.model.scrollSettings.width=t*(parseFloat(n._originalScrollWidth)/100));typeof n.model.scrollSettings.height=="string"&&(r=n.element.height(),n.model.scrollSettings.height=="auto"&&(n.model.scrollSettings.height="100%"),n.model.scrollSettings.height=r*(parseFloat(n.model.scrollSettings.height)/100));(n.model.scrollSettings.width||n.model.width)&&n.element.width(n.model.scrollSettings.width||n.model.width);u=n.model.scrollSettings.height!=0?n.model.scrollSettings.height-n.getHeaderTable().height()-(!ej.isNullOrUndefined(n._filterToolBar)&&n._filterToolBar.height()):n.model.scrollSettings.height;f=n.getContent().attr("tabindex","0");n.element.addClass("e-kanbanscroll");t=n.model.scrollSettings.width==="auto"?n.model.scrollSettings.width:parseInt(n.model.scrollSettings.width);f.ejScroller({enableRTL:n.model.enableRTL,height:u,width:t,thumbStart:$.proxy(n._kbnThumbStart,n),scroll:$.proxy(n._freezeSwimlane,n)});n.getContent().ejScroller("isVScroll")?(n.element.find(".e-kanbanheader").addClass("e-scrollcss"),n.getHeaderContent().find("div").first().addClass("e-headercontent")):n.element.find(".e-kanbanheader").removeClass("e-scrollcss");(n.model.scrollSettings.width||n.model.width)&&n.element.width(n.model.scrollSettings.width||n.model.width);i=n.getContent().data("ejScroller");e=i&&i.isVScroll()?"addClass":"removeClass";n.getHeaderContent().find(".e-headercontent")[e]("e-hscrollcss")},n.prototype._refreshScroller=function(n){var t=this.kanbanObj,i=t.getContent().first();ej.isNullOrUndefined(i.data("ejScroller"))||(n.requestType=="beginedit"&&t.getScrollObject().scrollY(0,!0),i.ejScroller("refresh"),i.ejScroller({enableRTL:t.model.enableRTL}),i.ejScroller("isVScroll")&&!t.getScrollObject().model.autoHide?(t.getHeaderContent().addClass("e-scrollcss"),t.getHeaderContent().find(".e-headercontent").hasClass("e-hscrollcss")||t.getHeaderContent().find(".e-headercontent").addClass("e-hscrollcss")):t.element.find(".e-kanbanheader").removeClass("e-scrollcss"))},n.prototype._refreshSwimlaneToggleScroller=function(){var n=this.kanbanObj,t=n.headerContent.find(".e-hscrollcss");n.KanbanScroll._refreshScroller({requestType:"refresh"});n.getContent().find(".e-vscrollbar").length>0?t.removeClass("e-vscroll-area"):t.addClass("e-vscroll-area")},n}();window.ej.createObject("ej.KanbanFeatures.Scroller",n,window);t=function(){function n(n){this.kanbanObj=null;this.kanbanObj=n}return n.prototype._cardSelection=function(n,t,i){var r=this.kanbanObj,l,c,f=null,o=null,w,s,d,v,y,h,p;if(l=t.parent("td.e-rowcell"),r._currentRowCellIndex.length>0&&(r._previousRowCellIndex=[[r._currentRowCellIndex[0][0],[r._currentRowCellIndex[0][1][0]],[r._currentRowCellIndex[0][2][0]]]]),ej.isNullOrUndefined(t.attr("id"))||(w=new ej.DataManager(r._currentJsonData),o=w.executeLocal((new ej.Query).where(r.model.fields.primaryKey,ej.FilterOperators.equal,t.attr("id")))),ej.isNullOrUndefined(r._previousRowCellIndex)||r._previousRowCellIndex.length==0||(f=r.KanbanCommon._getCardbyIndexes(r._previousRowCellIndex)),c={currentCell:l,target:t,cellIndex:n[0][1][0],cardIndex:n[0][2][0],data:o,previousRowCellIndex:r._previousRowCellIndex,previousCard:f,selectedCardsData:r._selectedCardData},!r._trigger("beforeCardSelect",c)){if($(t).hasClass("e-cardselection")||(r.model.selectionType!="multiple"||r._kTouchBar.find(".e-cardtouch").hasClass("e-spanclicked")||r._kTouchBar.hide(),i.pointerType!="touch"||r._kTouchBar.find(".e-cardtouch").hasClass("e-spanclicked")||r.model.selectionType!="multiple"||(s=$(t).offset(),r._kTouchBar.show(),r._kTouchBar.offset({top:s.top,left:s.left}))),i.pointerType=="touch"&&$(t).hasClass("e-cardselection")&&r._kTouchBar.is(":hidden")&&(s=$(t).offset(),r._kTouchBar.show(),r._kTouchBar.offset({top:s.top,left:s.left})),r.model.selectionType=="multiple"&&i.shiftKey){var a,e,u=0,b=0,g=t,k=[];for(ej.isNullOrUndefined(f)?e=t.parents(".e-columnrow").find(".e-kanbancard"):(a=f.parents(".e-columnrow"),a.find(".e-cardselection").not(f).removeClass("e-cardselection"),e=a.find(".e-kanbancard"),u=e.index(f)),r._selectedCards=[],r._selectedCardData=[],r.selectedRowCellIndexes=[];b<=0;)y=[],d=$(e[u]).parents(".e-columnrow"),v=$(e[u]).parents(".e-rowcell"),$(e[u]).addClass("e-cardselection"),k.push(e[u]),r._selectedCardData.push(r.KanbanCommon._getKanbanCardData(r._currentJsonData,e[u].id)[0]),y=[[r.element.find(".e-columnrow").index(d),[v.index()],[v.find($(e[u])).index()]]],e[u].id==g.attr("id")&&(b=1),this._pushIntoSelectedArray(y),ej.isNullOrUndefined(f)?++u:n[0][1][0]<r._previousRowCellIndex[0][1][0]?--u:n[0][1][0]==r._previousRowCellIndex[0][1][0]?n[0][2][0]<r._previousRowCellIndex[0][2][0]?--u:++u:++u;ej.isNullOrUndefined(f)&&(r._previousRowCellIndex=r._currentRowCellIndex=[[0,[0],[0]]],f=r.KanbanCommon._getCardbyIndexes(r._previousRowCellIndex));r._selectedCards=[];r._selectedCards[r._selectedCards.length]=k;!ej.isNullOrUndefined(f)||i.ctrlKey||i.shiftKey||r._enableMultiTouch||($(t).addClass("e-cardselection"),r._selectedCards.push(t[0]),r._selectedCardData.push(r.KanbanCommon._getKanbanCardData(r._currentJsonData,t.id)[0]),this._pushIntoSelectedArray(n))}if(r.model.selectionType=="multiple"&&(i.ctrlKey||r._enableMultiTouch))if($(t).hasClass("e-cardselection")){for($(t).removeClass("e-cardselection"),this._popFromSelectedArray(n[0][0],n[0][1],n[0][2],i),h=0;h<r._selectedCardData.length;h++)if(o[0][r.model.fields.primaryKey]==r._selectedCardData[0][r.model.fields.primaryKey])break;r._selectedCardData.splice(h,1);r._selectedCards.splice(h,1);r._currentRowCellIndex=n}else $(t).addClass("e-cardselection"),r._selectedCards.push(t[0]),this._pushIntoSelectedArray(n),r._selectedCardData.push(o[0]);else r.model.selectionType=="multiple"&&i.shiftKey||(p=n[0][2],r.element.find(".e-cardselection").removeClass("e-cardselection"),r._selectedCards=[],r.selectedRowCellIndexes=[],$(t).addClass("e-cardselection"),r._selectedCardData=[],r._selectedCardData.push(o[0]),r._selectedCards.push(t[0]),r.model.selectionType=="multiple"&&(p=[n[0][2]]),r.selectedRowCellIndexes.push({rowIndex:n[0][0],cellIndex:n[0][1],cardIndex:p}));(c={currentCell:l,target:t,cellIndex:n[0][1][0],cardIndex:n[0][2][0],data:o,selectedRowCellIndex:r.selectedRowCellIndexes,previousRowCellIndex:r._previousRowCellIndex,previousCard:f,selectedCardsData:r._selectedCardData},r.model.selectionType=="multiple"&&i.shiftKey&&$(t).hasClass("e-cardselection")?(r._previousRowCellIndex.length==0&&(r._currentRowCellIndex=[[t.parents(".e-columnrow").index(),[0],[0]]]),r._currentRowCellIndex.length==0&&(r._currentRowCellIndex=n)):$(t).hasClass("e-cardselection")&&(r._currentRowCellIndex=n),r._trigger("cardSelect",c))||r.element.focus()}},n.prototype.clear=function(){var n=this.kanbanObj,t;n.model.allowSelection&&(t=$(n._kanbanRows).not(".e-swimlanerow"),$(t).find(".e-rowcell .e-kanbancard").removeClass("e-cardselection"),n.selectedRowCellIndexes=[],n._selectedCardData=[],n._selectedCards=[],n._previousRowCellIndex=[],n._currentRowCellIndex=[])},n.prototype._pushIntoSelectedArray=function(n){for(var i=this.kanbanObj,r=0,f=!0,e=!0,u,o,t=0;t<i.selectedRowCellIndexes.length;t++)if(u=i.selectedRowCellIndexes[t],u.rowIndex==n[0][0]){for(f=!1,r=0;r<u.cellIndex.length;r++)if(o=u.cellIndex[r],o==n[0][1][0]){e=!1;break}break}f&&i.selectedRowCellIndexes.push({rowIndex:n[0][0],cellIndex:n[0][1],cardIndex:[n[0][2]]});e?f||(i.selectedRowCellIndexes[t].cellIndex.push(n[0][1][0]),i.selectedRowCellIndexes[t].cardIndex.push([n[0][2][0]])):i.selectedRowCellIndexes[t].cardIndex[r].push(n[0][2][0])},n.prototype._popFromSelectedArray=function(n,t,i,r){var e=this.kanbanObj,o,f,s,u,h,c;if(e.model.selectionType=="multiple"&&(r.ctrlKey||r.shiftKey||e._enableMultiTouch))for(o=0;o<e.selectedRowCellIndexes.length;o++)if(u=e.selectedRowCellIndexes[o],u.rowIndex==n)for(f=0;f<u.cellIndex.length;f++)if(h=u.cellIndex[f],h==t[0])if(r.shiftKey){u.cardIndex=[];break}else for(s=0;s<u.cardIndex[f].length;s++)if(c=u.cardIndex[f][s],c==i[0]){u.cardIndex[f].splice(s,1);u.cardIndex[f].length==0&&(u.cardIndex.splice(f,1),u.cellIndex.splice(f,1));u.cellIndex.length==0&&e.selectedRowCellIndexes.splice(o,1);break}},n.prototype._selectionOnRerender=function(){var s,e,t,o=!0,n=this.kanbanObj,r,u,i,f,c,h;if(n.model.allowSelection){if(n.model.selectionType=="single")n._selectedCards.length>0&&(u=$(n._selectedCards[0]),i=n.element.find("#"+u[0].id),i.hasClass("e-kanbancard")?(e=$(i).parents(".e-columnrow"),t=$(i).parents(".e-rowcell"),n._selectedCards=[],n._selectedCards=i.addClass("e-cardselection"),n._currentRowCellIndex=n._previousRowCellIndex=[],n._currentRowCellIndex.push([e.index(),[t.index()],[t.find(".e-kanbancard").index(t.find("#"+u[0].id))]]),n._currentRowCellIndex=n._previousRowCellIndex,n.selectedRowCellIndexes=[],n.selectedRowCellIndexes.push({rowIndex:n._currentRowCellIndex[0][0],cellIndex:n._currentRowCellIndex[0][1][0],cardIndex:n._currentRowCellIndex[0][2][0]})):(n._selectedCards=[],n._selectedCardData=[],n.selectedRowCellIndexes=[],n._currentRowCellIndex=n._previousRowCellIndex=[]));else{for(r=0;r<n._selectedCards.length;r++)if($(n._selectedCards[r]).length>1)for(f=0;f<n._selectedCards[r].length;f++)u=$(n._selectedCards[r][f]),i=n.element.find("#"+u[0].id),e=$(i).parents(".e-columnrow"),t=$(i).parents(".e-rowcell"),i.hasClass("e-kanbancard")?(o=!0,n._selectedCards[r].splice(f,0,i[0]),i.addClass("e-cardselection"),n._selectedCards[r].splice(f+1,1),r==0&&f==0&&(n._currentRowCellIndex=n._previousRowCellIndex=[],n._currentRowCellIndex.push([e.index(),[t.index()],[t.find(".e-kanbancard").index(t.find("#"+u[0].id))]]),n._currentRowCellIndex=n._previousRowCellIndex,n.selectedRowCellIndexes=[]),c=[e.index(),[t.index()],[t.find(".e-kanbancard").index(t.find("#"+u[0].id))]],this._pushIntoSelectedArray([c])):(o=!1,n._selectedCards[r].splice(f,1),h=n._selectedCardData.indexOf(n.KanbanCommon._getKanbanCardData(n._selectedCardData,u[0].id)[0]),n._selectedCardData.splice(h,1),--f);else u=$(n._selectedCards[r]),i=n.element.find("#"+u[0].id),e=$(i).parents(".e-columnrow"),t=$(i).parents(".e-rowcell"),i.hasClass("e-kanbancard")?(o=!0,i.addClass("e-cardselection"),n._selectedCards[r]=i[0],r==0&&(n._currentRowCellIndex=n._previousRowCellIndex=[],n._currentRowCellIndex.push([e.index(),[t.index()],[t.find(".e-kanbancard").index(t.find("#"+u[0].id))]]),n._currentRowCellIndex=n._previousRowCellIndex,n.selectedRowCellIndexes=[]),c=[e.index(),[t.index()],[t.find(".e-kanbancard").index(t.find("#"+u[0].id))]],this._pushIntoSelectedArray([c])):(o=!1,n._selectedCards.splice(r,1),h=n._selectedCardData.indexOf(n.KanbanCommon._getKanbanCardData(n._selectedCardData,u[0].id)[0]),n._selectedCardData.splice(h,1),--r);!o&&n._selectedCards.length<=0&&(n._currentRowCellIndex=n._previousRowCellIndex=n.selectedRowCellIndexes=[])}s=n.element.find(".e-columnrow").index(n.element.find(".e-cardselection").eq(0).parents(".e-columnrow"));s>=0&&(n.selectedRowCellIndexes[0].rowIndex=n._currentRowCellIndex[0][0]=s,n._previousRowCellIndex.length>0&&(n._previousRowCellIndex[0][0]=s))}},n.prototype._renderKanbanTouchBar=function(){var n=this.kanbanObj;n._kTouchBar=ej.buildTag("div.e-kanbantouchbar","",{display:"none"},{});var t=ej.buildTag("div.e-content","",{},{}),i=ej.buildTag("div.e-downtail e-tail","",{},{}),r=ej.buildTag("span.e-cardtouch e-icon","",{},{});t.append(r);n._kTouchBar.append(t);n._kTouchBar.append(i);n.element.append(n._kTouchBar)},n.prototype._updateSelectedCardIndexes=function(n){var e,f,t=this.kanbanObj,i,r,u;if(t.selectedRowCellIndexes.length>0)for($(n).hasClass("e-rowcell")?f=$(n).index():(n=t.element.find("#"+$(n).attr("id")),f=$(n).parents(".e-rowcell").index()),e=$(n).parents(".e-columnrow"),$.inArray(f,t.selectedRowCellIndexes[0].cellIndex)==-1&&t.selectedRowCellIndexes[0].cellIndex.push(f),i=0;i<t.selectedRowCellIndexes[0].cellIndex.length;i++)if(r=e.find(".e-rowcell").eq(t.selectedRowCellIndexes[0].cellIndex[i]).find(".e-cardselection"),r.length>0)for(t.selectedRowCellIndexes[0].cardIndex.splice(i,1),t.element.find(".e-targetclone").remove(),u=0;u<r.length;u++)u>0?t.selectedRowCellIndexes[0].cardIndex[i].push($(r[u]).index()):t.selectedRowCellIndexes[0].cardIndex.splice(i,0,[$(r[0]).index()]);else t.selectedRowCellIndexes[0].cellIndex.splice(i,1),t.selectedRowCellIndexes[0].cardIndex.splice(i,1),--i},n}();window.ej.createObject("ej.KanbanFeatures.Selection",t,window);r=function(){function n(n){this.kanbanObj=null;this.kanbanObj=n}return n.prototype._filterHandler=function(n,t){var i=this.kanbanObj,u={requestType:"filtering",currentFilterObject:[],filterCollection:i._filterCollection},f,o,r,s,e;if(u.currentFilterObject.push(n),i.model.isResponsive){for(f=[],i._kbnFilterObject=i._kbnFilterObject.concat(u.currentFilterObject),r=0;r<i._kbnFilterObject.length;r++)e=$.inArray(i._kbnFilterObject[r],f),e<0&&f.push(i._kbnFilterObject[r]);i._kbnFilterObject=f}for(o=i._dataSource()instanceof ej.DataManager?i._dataSource().dataSource.json:i._dataSource(),o.length==0&&i._currentJsonData.length>0&&(o=i._currentJsonData),i._initialData=o,r=0;r<i.model.filterSettings.length;r++)if(i.model.filterSettings[r].text==u.currentFilterObject[0].text){s=i.model.filterSettings[r].query.queries[0].e;ej.isNullOrUndefined(u.filterCollection[0])?(u.filterCollection.push(s),$(t).addClass("e-select")):$(t).hasClass("e-select")?($(t).removeClass("e-select"),e=$.inArray(s,u.filterCollection),u.filterCollection.splice(e,1)):($(t).addClass("e-select"),u.filterCollection.push(s));break}i.KanbanCommon._processBindings(u)},n.prototype.filterCards=function(n){var t={requestType:"filtering",filterCollection:this.kanbanObj._filterCollection},n=n.queries[0].e;t.filterCollection.push(n);this.kanbanObj.KanbanCommon._processBindings(t)},n.prototype.clearFilter=function(){var n=this.kanbanObj,t;n._filterCollection.length!=0&&(t={requestType:"filtering"},n._filterCollection=[],n.element.find(".e-kanbantoolbar .e-tooltxt").removeClass("e-select"),n.KanbanCommon._processBindings(t))},n.prototype._filterLimitCard=function(n){for(var o,i,e,h,t=this.kanbanObj,r,u,s,f=0;f<t.model.columns.length;f++)o=t.model.columns[f],i=t.getHeaderContent().find("span.e-totalcount"),t.model.enableTotalCount&&(i=$(i[f]),e=$(i).text().indexOf(t.localizedLabels.FilterOfText),!ej.isNullOrUndefined(n)&&(n.requestType=="drop"||n.requestType=="beginedit"||n.requestType=="save"||n.requestType=="cancel"||n.requestType=="refresh"||n.requestType=="add"||(n.requestType=="filtering"?n.filterCollection.length>0?!0:t.model.searchSettings.key==""?!1:!0:n.requestType=="search"?t.model.searchSettings.key!=""?!0:t.model.filterSettings.length>0?t.element.find(".e-kanbantoolbar .e-tooltxt").hasClass("e-select"):!1:!1))?(e!=-1&&t._dataManager instanceof ej.DataManager&&!t._dataManager.dataSource.offline?(s=t.localizedLabels.FilterOfText.length+e+1,r=$(i).text().slice(s),(n.requestType=="drop"||n.requestType=="save")&&(u=t.headerContent.find(".e-headercell").eq(f),u.hasClass("e-card-dragged")&&(r=--r,u.removeClass("e-card-dragged")),u.hasClass("e-card-dropped")&&(r=++r,u.removeClass("e-card-dropped"))),$(i).text(r)):t._dataManager instanceof ej.DataManager&&t._dataManager.dataSource.offline&&(e!=-1||t.model.filterSettings.length!=0||t.model.searchSettings.key.length!=0)&&(r=new ej.DataManager(t.model.dataSource).executeLocal((new ej.Query).where(t.model.keyField,ej.FilterOperators.equal,o.key)).length,$(i).text(r)),h=t.KanbanCommon._multikeySeparation(o.key),$(i).text($($(t.element.find(".e-columnrow")).find('td[data-ej-mappingkey="'+h+'"] > div.e-kanbancard')).length+" "+t.localizedLabels.FilterOfText+" "+$(i).text())):ej.isNullOrUndefined(o.constraints)?$(i).text($(i).text().slice(e+3)):t.KanbanCommon._renderLimit())},n.prototype.searchCards=function(n){var u,t=this.kanbanObj,i=$("#"+t._id+"_toolbarItems_search"),r;i.find("input").val()!=n&&i.find("input").val(n);t.element.hasClass("e-responsive")||(i.parent().addClass("e-highliht-kbnsearchbar"),i.find("input").val()==""&&i.parent().removeClass("e-highliht-kbnsearchbar"));u={requestType:"search",keyValue:n};(n!=""||t.model.searchSettings.key!="")&&(t.model.searchSettings.key=n,t.model.searchSettings.key.length>0&&(r=t._dataSource()instanceof ej.DataManager?t._dataSource().dataSource.json:t._dataSource(),r.length==0&&t._currentJsonData.length>0&&(r=t._currentJsonData),t._initialData=r),t.KanbanCommon._processBindings(u))},n.prototype.clearSearch=function(){var n=this.kanbanObj;n.element.find(".e-kanbantoolbar #"+n._id+"_toolbarItems_search").val("");this.searchCards("");$.extend(n.model.searchSettings,n.defaults.searchSettings)},n.prototype._onToolbarKeypress=function(n){var t=this.KanbanFilter.kanbanObj,i=t.element,r=i.data("ejKanban");t.model.isResponsive&&i.hasClass("e-responsive")&&($(t.itemsContainer).parent().children().not(".e-searchbar").hide(),t.KanbanAdaptive._kbnTimeoutSearch(i,n))},n}();window.ej.createObject("ej.KanbanFeatures.Filter",r,window);u=function(){function n(n){this.kanbanObj=null;this._dragEle=null;this._dropEle=null;this._dropTarget=null;this.kanbanObj=n}return n.prototype._addDragableClass=function(){var n=this.kanbanObj;n._dropped=!1;this._dragEle=n.getContent().find(".e-columnrow td.e-drag .e-kanbancard");this._dropEle=n.getContent().find(".e-columnrow .e-rowcell.e-drop");this._dropEle.addClass("e-droppable");this._dragEle.addClass("e-draggable e-droppable");this._enableDragDrop();n._on(n.element,"mouseup touchstart pointerdown MSPointerDown","",$.proxy(n.element.focus(),this))},n.prototype._enableDragDrop=function(){var n=this.kanbanObj;this._drag();n.model.allowExternalDragAndDrop||$(n.getContent()).ejDroppable({accept:$(n.getContent()).find("div.e-kanbancard"),drop:function(n,t){$(t.helper).hide()}})},n.prototype._selectedPrevCurrentCards=function(){var t,i,n=this.kanbanObj;n._previousRowCellIndex.length>0&&(t=n.KanbanCommon._getCardbyIndexes(n._previousRowCellIndex),n._pCardId=t.attr("id"));n._previousRowCellIndex=n._currentRowCellIndex;n._pCardId=n._cCardId;n._currentRowCellIndex.length>0&&(i=n.KanbanCommon._getCardbyIndexes(n._currentRowCellIndex),n._cCardId=i.attr("id"))},n.prototype._getPriorityIndex=function(n){for(var i=null,r=this.kanbanObj,t=0;t<r._priorityCollection.length;t++)if(r._priorityCollection[t].primaryKey==n.attr("id")){i=r._priorityCollection[t];break}return ej.isNullOrUndefined(i)?this._getPriorityKey(n.attr("id")):parseInt(i.dropKey)},n.prototype._removeFromPriorirtyCollec=function(n){for(var i=null,r=this.kanbanObj,t=0;t<r._priorityCollection.length;t++)if(r._priorityCollection[t].primaryKey==n){i=t;break}ej.isNullOrUndefined(i)||r._priorityCollection.splice(i,1)},n.prototype._columnDataOndrop=function(n,t){var r=this.kanbanObj,h=new ej.DataManager(r._initialData),u,f,e,o,s,i=new ej.Query;return f=r.model.keyField,e=r.model.fields.swimlaneKey,o=$(n).hasClass("e-rowcell")?$(n).attr("data-ej-mappingkey"):$(n).parents(".e-rowcell").attr("data-ej-mappingkey"),s=$(n).parents(".e-columnrow").prev(".e-swimlanerow").find(".e-slkey").text(),i=ej.isNullOrUndefined(e)?i.where(f,ej.FilterOperators.equal,o):i.where(f,ej.FilterOperators.equal,o).where(e,ej.FilterOperators.equal,s),u=h.executeLocal(i),u.sort(function(n,i){return n[t]-i[t]}),u},n.prototype._dropToColumn=function(n,t){var i=this.kanbanObj,h,u,f=[],c=!1,e=i.model.fields.priority,l,a,w=n.parent().has(t).length,o,v,s,r,y,p;for(ej.isNullOrUndefined(i._filterToolBar)||(l=i._filterToolBar.find("li.e-select")),n.hasClass("e-targetclone")&&(n=n.parent()),n.hasClass("e-columnkey")&&(n=n.parent().parent()),this._selectedPrevCurrentCards(),e&&(!ej.isNullOrUndefined(i._filterToolBar)&&l.length>0||i._searchBar!=null&&i._searchBar.find(".e-cancel").length>0)?(f=this._columnDataOndrop(n,e),c=f.length>0):c=$(n).children(".e-kanbancard").length>1,a=this._getSelectedCards(t),i.model.selectionType=="multiple"&&a.length>0&&$(t).hasClass("e-cardselection")&&(t=a),o=0;o<t.length;o++)for(v=t[o],s=0;s<$(v).length;s++)r=$(v)[s],$(r).is(":visible")&&(e&&c&&(h=!ej.isNullOrUndefined(i._filterToolBar)&&l.length>0||i._searchBar!=null&&i._searchBar.find(".e-cancel").length>0?f[f.length-1][e]:this._getPriorityIndex(n.find(".e-kanbancard").last()),u=this._getPriorityIndex($(r)),h>=u&&(u=++h),this._removeFromPriorirtyCollec($(r).attr("id")),i._priorityCollection.push({primaryKey:$(r).attr("id"),dropKey:u}),y=i.KanbanCommon._getKanbanCardData(i._currentJsonData,$(r)[0].id)[0],ej.isNullOrUndefined(u)||(y[e]=u,f.push(y))),$(n).find(".e-customaddbutton").length>0?$(r).insertBefore($(n).find(".e-customaddbutton")):i.element.hasClass("e-responsive")?(p=n.find(".e-cell-scrollcontent"),p.length>0?p.children().append(r):$(n).append(r)):$(n).append(r));this._updateDropAction(n,t)},n.prototype._updateDropAction=function(n,t){var a,i=this._externalDrop?this._externalObj:this.kanbanObj,f,r,e,o,s,c,u,l,h;for(a=$(n).hasClass("e-rowcell")?n:$(n).closest("td.e-rowcell"),i._selectedCardData=[],r=0;r<t.length;r++)if(ej.isNullOrUndefined(t[r].length))($(t[r]).is(":visible")||!ej.isNullOrUndefined(i.model.fields.collapsibleCards)||$(n).hasClass("e-hide")&&i.model.contextMenuSettings.enable)&&(e=$(t[r]).attr("id"),this._updateDropData(n,t[r],e),u=i.element.find("#"+e),f=u.parents(".e-rowcell"),u.parents(".e-rowcell").hasClass("e-shrink")&&u.addClass("e-hide"),s=parseInt(f.find(".e-shrinkcount").html()),f.find(".e-shrinkcount").html(++s));else for(o=0;o<t[r].length;o++)($(t[r][o]).is(":visible")||$(n).hasClass("e-hide")&&i.model.contextMenuSettings.enable)&&(e=t[r][o].id,this._updateDropData(n,t[r][o],e),u=i.element.find("#"+e),f=u.parents(".e-rowcell"),u.parents(".e-rowcell").hasClass("e-shrink")&&u.addClass("e-hide"),s=parseInt(f.find(".e-shrinkcount").html()),f.find(".e-shrinkcount").html(++s));if(i.model.fields.priority&&i._priorityCollection.length>0)while(i._priorityCollection.length>0)c=i._priorityCollection[0].primaryKey,u=i.element.find("#"+c),this._updateDropData(n,u,c);h=i._bulkUpdateData[0][i.model.fields.primaryKey];i._bulkUpdateData.length>1&&(h="bulk");l={data:i._bulkUpdateData,requestType:"drop",primaryKeyValue:h};i._saveArgs=l;i.updateCard(h,i._bulkUpdateData);!this._externalDrop&&(i.model.allowSelection&&!ej.isNullOrUndefined(t[0].length)&&t[0].length>1||t.length>1||t.length==1&&$(t).hasClass("e-cardselection"))&&(i._previousRowCellIndex=ej.isNullOrUndefined(i._pCardId)?[]:this._updateRowCellIndexes(i._pCardId,i._previousRowCellIndex),i._currentRowCellIndex=ej.isNullOrUndefined(i._cCardId)?[]:this._updateRowCellIndexes(i._cCardId,i._currentRowCellIndex),i.KanbanSelection._selectionOnRerender(),ej.isNullOrUndefined(i._pCardId)&&(i._previousRowCellIndex=i._currentRowCellIndex),i.model.selectionType=="single"?i.selectedRowCellIndexes=i._currentRowCellIndex:i.KanbanSelection._updateSelectedCardIndexes(n));i._priorityCollection=[]},n.prototype._getPriorityKey=function(n){var t=this._externalDrop?this._externalObj:this.kanbanObj,i;return i=t.KanbanCommon._getKanbanCardData(t._currentJsonData,n),i.length==0&&(i=t.KanbanCommon._getKanbanCardData(this._externalData,n)),i[0][t.model.fields.priority]},n.prototype._dropAsSibling=function(n,t,i){var r=this._externalDrop?this._externalObj:this.kanbanObj,s=0,nt=0,ut=0,w,ft,f,k,tt,d,et,u=[],at,c=r.model.fields.priority,b,p,ot,yt=n.parent().has(t).length,st,vt,l,ht,v,o,ct,lt,g,it,y,e,a,h,rt;for(ej.isNullOrUndefined(r._filterToolBar)||(b=r._filterToolBar.find("li.e-select")),p=r.model.fields.primaryKey,this._selectedPrevCurrentCards(),c&&(!ej.isNullOrUndefined(r._filterToolBar)&&b.length>0||r._searchBar!=null&&r._searchBar.find(".e-cancel").length>0)&&(u=this._columnDataOndrop(n,c)),($(n).hasClass("e-targetclone")||$(n).hasClass("e-shrinkheader"))&&(n.next().length==0&&$(n).addClass("e-targetappend"),d=$(n).prevAll(".e-kanbancard")[0],et=$(n).nextAll(".e-kanbancard")[0],!ej.isNullOrUndefined(r.model.fields.collapsibleCards)&&ej.isNullOrUndefined(d)&&(st=$(n).prevAll(".e-toggle-area").find(".e-kanbancard"),vt=$(st).length,d=$(st)[vt-1]),$(n).hasClass("e-targetappend")?(i=!0,n=d,ej.isNullOrUndefined(n)&&(n=et)):(i=!1,n=et,ej.isNullOrUndefined(n)&&(n=d))),tt=n,ot=this._getSelectedCards(t),r.model.selectionType=="multiple"&&ot.length>0&&$(t).hasClass("e-cardselection")&&(t=ot),l=0;l<t.length;l++)for(ht=t[l],v=0;v<$(ht).length;v++)if(o=$(ht)[v],$(o).is(":visible")&&(i&&(v>0&&t[l][v-1]!=tt?n=t[l][v-1]:v==0&&l>0&&t[l-1]!=tt&&(n=t[l-1])),o!=tt&&(i?$(o).insertAfter(n):$(o).insertBefore(n),c&&(!ej.isNullOrUndefined(r._filterToolBar)&&b.length>0||r._searchBar!=null&&r._searchBar.find(".e-cancel").length>0)&&(ct=r.KanbanCommon._getKanbanCardData(u,$(n).attr("id")),h=u.indexOf(ct[0]),g=r.KanbanCommon._getKanbanCardData(r._currentJsonData,$(o)[0].id),ct[0][r.model.keyField]==g[0][r.model.keyField]&&u.splice(u.indexOf(g[0]),1),i?u.splice(h+1,0,g[0]):u.splice(h,0,g[0]),at=$(o)[0].id)),c)){if(!ej.isNullOrUndefined(r._filterToolBar)&&b.length>0||r._searchBar!=null&&r._searchBar.find(".e-cancel").length>0)for(a=r.KanbanCommon._getKanbanCardData(u,$(n).attr("id")),s=a[0][c],a=r.KanbanCommon._getKanbanCardData(u,at),k=[],y=u.indexOf(a[0]);y<u.length;y++)k.push(u[y]);else s=this._getPriorityIndex($(n)),it=$(o).nextAll(".e-kanbancard"),k=ej.isNullOrUndefined(it.addBack)?it.andSelf():it.addBack();for(y=0;y<k.length;y++){if(e=k[y],!ej.isNullOrUndefined(r._filterToolBar)&&b.length>0||r._searchBar!=null&&r._searchBar.find(".e-cancel").length>0?(a=null,f=e[c],!i&&$(o)[0].id==e[p]&&s<f&&(f=s),a=r.KanbanCommon._getKanbanCardData(u,e[p]),h=lt=u.indexOf(a[0]),h-1>=0&&(nt=u[h-1][c]),a=r.KanbanCommon._getKanbanCardData(u,e[p]),h=lt=u.indexOf(a[0]),h+1<u.length?(ut=u[h+1][c],w=u[h+1]):w=[]):(f=this._getPriorityKey($(e).attr("id")),!i&&$(o)[0]==e&&s<f&&(f=s),ft=$(e).prevAll(".e-kanbancard"),ft.length>0&&(nt=this._getPriorityIndex(ft.eq(0))),w=$(e).nextAll(".e-kanbancard"),w.length>0&&(ut=this._getPriorityIndex(w.eq(0)))),!isNaN(nt)&&nt<f&&(f<ut||w.length==0))break;!ej.isNullOrUndefined(r._filterToolBar)&&b.length>0||r._searchBar!=null&&r._searchBar.find(".e-cancel").length>0?(!i&&$(o)[0].id==e[p]?!i&&$(o)[0].id==e[p]&&s<f||(f=s):f=++s,u[lt][c]=f,rt=e[p]):(!i&&$(o)[0]==e?!i&&$(o)[0]==e&&s<f||(f=s):f=++s,rt=$(e).attr("id"));this._removeFromPriorirtyCollec(rt);r._priorityCollection.push({primaryKey:rt,dropKey:f});s=f}}this._updateDropAction(n,t);this._dropTarget=null},n.prototype._updateDropData=function(n,t,i){var r=this._externalDrop?this._externalObj:this.kanbanObj,o,c,l,u,y,a,v,e,p,s,w,h,f;if(!ej.isNullOrUndefined(r.model.fields.primaryKey)){if(u=[],c=r.model.fields.priority,l=r.model.fields.primaryKey,ej.isNullOrUndefined(i)&&(i=$(n).attr("id")),o=$(n).hasClass("e-rowcell")?$(n):$(n).parents(".e-rowcell"),o.index()<0&&(o=r.element.find("#"+$(n).attr("id")).parents(".e-rowcell")),u=$.extend(!0,[],this.kanbanObj.KanbanCommon._getKanbanCardData(this.kanbanObj._currentJsonData,i)),u.length==0&&this._externalDrop&&(u=$.extend(!0,[],r.KanbanCommon._getKanbanCardData(r._currentJsonData,i))),y=r.model.columns[o.index()].key,e=typeof y=="object"?y:y.split(","),u.length>0&&(a=u[0][r.model.keyField],$(o).find(".e-targetclonemulti").length>0?(v=$(o).find(".e-columnkey.e-active .e-text").eq(0).text(),typeof a!=typeof v&&typeof a=="number"&&(v=parseInt(v)),u[0][r.model.keyField]=v):e.length==1&&(e=e[0],typeof a!=typeof e&&typeof a=="number"&&(e=parseInt(e)),u[0][r.model.keyField]=e),r.model.fields.swimlaneKey&&(p=$(n).parents("tr.e-columnrow").prev().find("div.e-slkey").attr("data-ej-slmappingkey"),u[0][r.model.fields.swimlaneKey]=ej.isNullOrUndefined(p)?r.element.find("#"+n.id).parents("tr.e-columnrow").prev().find("div.e-slkey").html():p)),r._dropped=!0,c&&r._priorityCollection.length>0){for(h=0;h<r._priorityCollection.length;h++)if(r._priorityCollection[h].primaryKey==i){s=r._priorityCollection[h];r._priorityCollection.splice(h,1);break}if(!ej.isNullOrUndefined(s)&&u.length>0&&(u[0][c]=s.dropKey),!ej.isNullOrUndefined(r._filterToolBar)&&r._filterToolBar.find("li.e-select").length>0&&!ej.isNullOrUndefined(s)||r._searchBar!=null&&r._searchBar.find(".e-cancel").length>0)for(f=0;f<r._initialData.length;f++)if(s.primaryKey==r._initialData[f][l]){u.length>0?r._initialData[f]=u[0]:r._initialData[f][c]=s.dropKey;w=r._initialData[f];break}}if(u.length>0){if($(t).hasClass("e-cardselection")){for(f=0;f<r._selectedCardData.length;f++)if(u[0][l]==r._selectedCardData[f][l])break;r._selectedCardData.splice(f,1);r._selectedCardData.push(u[0])}r._bulkUpdateData.push(u[0])}else c&&r.KanbanCommon._getKanbanCardData(r._bulkUpdateData,w[l]).length<=0&&r._bulkUpdateData.push(w)}},n.prototype._updateRowCellIndexes=function(n,t){var f,i,r=[],u;return ej.isNullOrUndefined(n)?r=t:(u=this.kanbanObj.element.find("#"+n),f=$(u).parents(".e-columnrow"),i=$(u).parents(".e-rowcell"),r.push([f.index(),[i.index()],[i.find(".e-kanbancard").index(i.find("#"+n))]])),r},n.prototype._dragStop=function(n,t,i){var o=this,y,ht,ct,lt,k,at,s,r=this.kanbanObj,a,vt,gt=!ej.isNullOrUndefined(r.model.fields.collapsibleCards),u,yt,w,it,h,rt,pt,wt,d,ut,ft,ni,ti,e,f,et,ot,p,c,kt,v,b,nt,tt,st,ui,dt;if(n.element.dropped||t&&t.remove(),n.target=this._getCursorElement(n),u=$(n.target).closest(".e-kanbancard").length>0?$(n.target).closest(".e-kanbancard")[0]:n.target,r.element.hasClass("e-responsive")&&!$(u).hasClass("e-targetclone")&&(p=$(u).parents(".e-rowcell"),!$(u).hasClass("e-kanbancard")&&$(u).parents(".e-kanbancard").length==0&&p.length>0&&$(u).parents(".e-kanbancard").is(":visible")&&(n.target=u=p[0])),k=$(n.element).parents(".e-columnrow"),yt=k.find(u).parents(".e-rowcell").height()-r._tdHeightDiff,r._dataManager instanceof ej.DataManager&&!r._dataManager.dataSource.offline&&(ht=r.element.find(".e-kanban-draggedcard").parents(".e-rowcell").index(),ct=r.element.find(".e-targetclone").parents(".e-rowcell").index(),header=r.headerContent.find(".e-headercell"),r.model.enableTotalCount&&(r._filterCollection.length>0||r.model.searchSettings.key.length>0)&&(header.eq(ht).addClass("e-card-dragged"),header.eq(ct).addClass("e-card-dropped"))),r.element.find(".e-kanban-draggedcard").removeClass("e-kanban-draggedcard"),r.element.find(".e-targetdragclone").remove(),o._dropEle.removeClass("e-dropping e-dragged e-dragpossible"),r.element.find(".e-droppableremove").removeClass("e-droppableremove e-droppable"),r.element.find(".e-dragpossible").removeClass("e-dragpossible e-drop"),r.model.allowExternalDragAndDrop&&(w=r.model.cardSettings.externalDropTarget,it=$(n.target).parents(w),this._externalObj=$(it).data("ejKanban"),$(w).find(".e-rowcell.e-drag").removeClass("e-dropping e-dragged e-dragpossible"),!ej.isNullOrUndefined(w)&&it.hasClass("e-kanban"))){if(this._externalDrop=!0,this._externalData=[],ut=0,h=this._externalObj.model.fields.primaryKey,rt=this._externalObj.getCurrentJsonData(),this._externalObj.element.find(".e-rowcell.e-drag").removeClass("e-dropping e-dragpossible"),r._dataSource()instanceof ej.DataManager){for(ft=[],c=0;c<n.dragData.length;c++)ft.push(n.dragData[c][0]);r._saveArgs=n;r.KanbanCommon._kbnBulkUpdate(ni,ft,ti)}for(e=0;e<n.dragData.length;e++)this._externalData.push(n.dragData[e][0]),new ej.DataManager(rt).executeLocal((new ej.Query).where(h,ej.FilterOperators.equal,n.dragData[e][0][h])).length>0&&(ut==0?(pt=(new ej.Query).sortByDesc(h).take(1),wt=ej.DataManager(rt).executeLocal(pt),d=n.dragData[e][0][h]=parseInt(wt[0][h])+1):d=n.dragData[e][0][h]=parseInt(n.dragData[e-1][0][h])+1,r._selectedCards.length>0?$(r._selectedCards[e]).attr({id:d}):$(n.element).attr({id:d}),ut++)}if(y={data:n.dragData,draggedElement:n.dragEle,dropTarget:$(u),event:n.event},r._trigger("cardDragStop",y))return!1;k=$(n.element).parents(".e-columnrow");lt=k.find(".e-cardselection");at=k.find(".e-rowcell");s=$(n.target).parents(".e-targetclonemulti").length>0?this._externalDrop?$(w).find(".e-targetclonemulti"):r.element.find(".e-targetclonemulti"):this._externalDrop?$(w).find(".e-targetclone"):r.element.find(".e-targetclone");a=$(n.element).parents(".e-rowcell");vt=r.element.find(".e-rowcell.e-drag").index(a);var bt=$(u).parent().children().hasClass("e-customaddbutton"),g=$(u).parent().children().hasClass("e-limits"),ii=$(u).parent().children().hasClass("e-shrinkheader"),l=$(u).parent().children(),ri=ii?bt?g?l.length==4:l.length==3:g?l.length==3:l.length==2:bt?g?l.length==3:l.length==2:g?l.length==2:l.length==1;if(s.is(":visible")&&document.body.style.cursor==""&&u.style.cursor==""&&($(u).parents(".e-targetclonemulti").length>0?o._dropToColumn($(u).parents(".e-rowcell"),$(n.element)):u.nodeName=="TD"&&$(u).parent().hasClass("e-columnrow")&&s.is(":visible")||s.siblings().length<=0||ri?(ot=$(u).find(".e-cell-scrollcontent"),f=ot.length>0?ot.children().eq(0).children().last():$(u).children().last(),f.hasClass("e-targetclone")&&(f=f.prev()),et=s.siblings().not(".e-shrinkheader").not(".e-customaddbutton").not(".e-limits"),($(u).children().length==0||et.length>=0||f.hasClass("e-customaddbutton")&&f.prev().hasClass("e-targetclone")||!f.hasClass("e-customaddbutton")&&$(f).offset().top+$(f).height()<$(s).offset().top)&&(f.hasClass("e-customaddbutton")||f.next().hasClass("e-targetclone")||et.length<=0?o._dropToColumn($(u),$(n.element)):(i=!1,o._dropAsSibling($(s.next()),$(n.element),i)))):u.nodeName=="DIV"&&($(u).hasClass("e-kanbancard")||$(u).hasClass("e-targetclone"))&&($(u).hasClass("e-targetclone")?(p=$(u).parents(".e-rowcell"),$(u).next().length==0&&$(p).find(".e-kanbancard").length==0?o._dropToColumn($(p),$(n.element)):o._dropAsSibling($(u),$(n.element),i)):(s.is(":visible")||lt.length>1)&&o._dropAsSibling($(u),$(n.element),i)),r._kbnBrowserContext="contextmenu",this._externalDrop)){if(this._externalDrop=!1,this._externalObj._bulkUpdateData=[],r._bulkUpdateData=n.dragData,!(r._dataSource()instanceof ej.DataManager))for(c=0;c<this._externalData.length;c++)kt=$.inArray(this._externalData[c],r._dataSource()),r._dataSource().splice(kt,1);r.refresh(!0)}return(o._removeKanbanCursor(),r.model.allowExternalDragAndDrop&&!ej.isNullOrUndefined(this._externalObj)&&this._externalObj.currentViewData.length==0&&this._externalObj.getContentTable().find("tbody").empty().first().append(this._externalObj.KanbanCommon._getEmptyTbody()),r.element.hasClass("e-responsive")&&r.element.parents("body").removeClass("e-kbnwindow-modal"),r._autoKbnSwipeLeft=!1,r._autoKbnSwipeRight=!1,this._externalDrop||r.element.find(".e-targetclone").remove(),v=r.element.find(".e-targetclonemulti"),v.length>0&&(v.parent().find(".e-kanbancard").show(),ej.isNullOrUndefined(v.parent().find(".e-toggle-area"))||v.parent().find(".e-toggle-area").show(),$(n.target).parents(".e-rowcell").css("border-style",""),r.element.find(".e-responsive")&&v.parent().find(".e-limits").show(),v.remove()),$(n.element)[0]!=u&&at.height(yt),r._on(r.element,"mouseenter mouseleave",".e-columnrow .e-kanbancard",r._cardHover),$(t).length>0&&$(t).remove(),r._eventBindings(),r.model.isResponsive&&r.kanbanWindowResize(),b=r.element.find("#"+n.element.attr("id")),$(b).hasClass("e-cardselection")&&(b=$(b).parents(".e-columnrow").find(".e-cardselection:visible")),r.KanbanAdaptive&&r.element.hasClass("e-responsive")&&!ej.isNullOrUndefined(r.model.fields.swimlaneKey)&&r.KanbanAdaptive._addSwimlaneName(),a=r.element.find(".e-rowcell.e-drag").eq(vt),y.draggedElement=b,y.draggedParent=a,y.data=r._bulkUpdateData,r.model.allowScrolling&&!r.element.hasClass("e-responsive")&&r.kanbanContent.data("ejScroller").refresh(),r._trigger("cardDrop",y))?!1:(r._bulkUpdateData=[],nt=a.parent().find(".e-rowcell"),tt=b.parents(".e-rowcell"),r.model.isResponsive&&r.element.hasClass("e-responsive")&&tt[0]!=a[0]&&nt.index(tt)!=r._kbnSwipeCount&&(nt.index(tt)>nt.index(a)?r.KanbanAdaptive._kbnLeftSwipe():r.KanbanAdaptive._kbnRightSwipe()),r.KanbanEdit&&r.element.find(".e-form-container .e-disable").attr("value")==n.dragEle[0].id&&r.element.find(".e-form-container").css("display")=="block"&&(r.model.editSettings.editMode=="externalform"||r.model.editSettings.editMode=="externalformtemplate")&&r.KanbanEdit.startEdit(n.dragEle),r.element.find(".e-togglevisible").removeClass("e-togglevisible"),r.element.hasClass("e-responsive")&&!ej.isNullOrUndefined(r.model.fields.swimlaneKey)&&r._kbnAdaptDdlIndex>0&&(st=r.kanbanContent.data("ejScroller"),dt=r._freezeScrollTop,ui=r.element[0].getElementsByClassName("e-columnrow"),st.scrollY(0,!0),r._freezeScrollTop=dt,st.scrollY(r._freezeScrollTop,!0)),gt==!0&&this.KanbanScroll&&this.kanbanObj.KanbanScroll._refreshScroller({requestType:"refresh"}),!0)},n.prototype._getCursorElement=function(n){var i=$(n.target).closest(".dragClone.e-kanbancard"),t=n.event;return i.length>0&&(i.hide(),n.target=t.type=="touchmove"||t.type=="touchstart"||t.type=="touchend"?document.elementFromPoint(t.originalEvent.changedTouches[0].pageX,t.originalEvent.changedTouches[0].pageY):document.elementFromPoint(t.clientX,t.clientY),i.show()),n.target},n.prototype._getSelectedCards=function(n){for(var r,t=this.kanbanObj,i=0;i<t._selectedCards.length;i++)if($(t._selectedCards[i]).length>1)for(r=0;r<t._selectedCards[i].length;r++)$(t._selectedCards[i][r])[0]==n[0]&&(t._selectedCards[i].splice(r,1),t._selectedCards[i].splice(0,0,n[0]),t._selectedCards.splice(0,0,t._selectedCards[i]),t._selectedCards.splice(i+1,1));else $(t._selectedCards[i])[0]==n[0]&&(t._selectedCards.splice(0,0,t._selectedCards[i]),t._selectedCards.splice(i+1,1));return t._selectedCards},n.prototype._kanbanAutoScroll=function(n){var nt=this,t=this.kanbanObj,r,u,e=null,o=null,s=null,i=null,f=t.element.find(".e-draggedcard"),g=t.element.find(".e-columnrow .e-rowcell"),b,a,k,v,y,c,d,p,h,w,l;v=ej.isNullOrUndefined(n.event.clientX)?n.event.originalEvent.changedTouches[0].clientX:n.event.clientX;y=ej.isNullOrUndefined(n.event.clientY)?n.event.originalEvent.changedTouches[0].clientY:n.event.clientY;b=t.element.width();r=t.element.hasClass("e-responsive")&&!t.element.hasClass("e-swimlane-responsive")?$(g).eq(t._kbnSwipeCount).find(".e-cell-scrollcontent"):t.getContent();u=r.data("ejScroller");a=t.element.offset().left;k=t.element.offset().top;p=r.find(".e-hup");d=r.find(".e-hdown");l=r.find(".e-vup");w=r.find(".e-vdown");c=r.find(".e-hhandle.e-box");h=r.find(".e-vhandle.e-box");!t.element.hasClass("e-responsive")&&t.model.allowScrolling&&(v>a+b-10?(t.element.removeClass("e-view-horizontal"),o=window.setInterval(function(){(f.length<=0||f.length>0&&f.is(":hidden"))&&o&&(o=window.clearInterval(o));c.length>0&&c.width()+c.offset().left<=d.offset().left&&!t.element.hasClass("e-view-horizontal")?u.scrollX(u.scrollLeft()+5,!0):o&&(o=window.clearInterval(o))},100)):v-10<a?(t.element.removeClass("e-view-horizontal"),u.scrollLeft()>0&&(e=window.setInterval(function(){(f.length<=0||f.length>0&&f.is(":hidden"))&&e&&(e=window.clearInterval(e));c.offset().left>=p.offset().left+p.width()&&!t.element.hasClass("e-view-horizontal")?u.scrollX(u.scrollLeft()-5,!0):e&&(e=window.clearInterval(e))},100))):(t.element.addClass("e-view-horizontal"),o&&(o=window.clearInterval(o)),e&&(e=window.clearInterval(e))));!ej.isNullOrUndefined(r)&&r.length>0&&r.hasClass("e-scroller")&&(y>k+t.getHeaderContent().height()+(r.height()-60)&&h.length>0&&w.length>0?(t.element.removeClass("e-view-vertical"),i=window.setInterval(function(){(f.length<=0||f.length>0&&f.is(":hidden"))&&i&&(i=window.clearInterval(i));h.height()+h.offset().top<=w.offset().top&&!t.element.hasClass("e-view-vertical")&&u.scrollTop()<t.kanbanContent.find(".e-table").height()-t.kanbanContent.height()?u.scrollY(u.scrollTop()+5,!0):i&&(i=window.clearInterval(i))},100)):y-30<t.element.find(".e-kanbancontent").offset().top&&h.length>0&&l.length>0?u.scrollTop()>0&&(t.element.removeClass("e-view-vertical"),s=window.setInterval(function(){i&&(i=window.clearInterval(i));(f.length<=0||f.length>0&&f.is(":hidden"))&&s&&(i=window.clearInterval(s));h.offset().top>=l.offset().top+l.height()&&!t.element.hasClass("e-view-vertical")?u.scrollY(u.scrollTop()-5,!0):s&&(s=window.clearInterval(s))},100)):(t.element.addClass("e-view-vertical"),i&&(i=window.clearInterval(i)),s&&(s=window.clearInterval(s))))},n.prototype._changeKanbanCursor=function(n,t,i){var f=$(n).parents(".e-kanbancard"),e=$(n).hasClass("e-kanbancard"),u,r;f.length>0||e?(r=e?$(n):f,r[0].style.cursor="not-allowed"):$(n).hasClass("e-shrinkheader")||$(n).parents(".e-shrinkheader").length>0||$(n).hasClass("e-slexpandcollapse")||$(n).parents(".e-slexpandcollapse").length>0?(r=$(n),$(n).parents(".e-shrinkheader").length>0?r=$(n).parents(".e-shrinkheader"):$(n).parents(".e-slexpandcollapse").length>0&&(r=$(n).parents(".e-slexpandcollapse")),r[0].style.cursor="not-allowed"):$(n).hasClass("e-shrink")?$(n)[0].style.cursor="not-allowed":$(n).parents(".e-columnrow").has(i.element).length==0||$(n).parents(".e-swimlanerow").length>0||$(n).hasClass("e-columnkey e-disable")||$(n).parents(".e-columnkey.e-disable").length>0?document.body.style.cursor="not-allowed":$(n).hasClass("e-rowcell")&&($(n)[0].style.cursor="not-allowed");u=$(t).find(".e-kanbancard");u.length>0&&(u[0].style.cursor="not-allowed")},n.prototype._removeKanbanCursor=function(){document.body.style.cursor="";this.kanbanObj.element.find(".e-kanbancard,.e-shrink,.e-shrinkheader,.e-slexpandcollapse,.e-rowcell").css("cursor","")},n.prototype._multicloneremove=function(n,t){if(n.hasClass("e-rowcell")){var i=this.kanbanObj;$(t).parents(".e-rowcell").css("border-style","");$(t).siblings(".e-kanbancard").show();$(t).siblings(".e-targetdragclone").show();(this._externalDrop?this._externalObj.element.find(".e-responsive"):i.element.find(".e-responsive"))&&$(t).siblings(".e-limits").show();$(t).remove();t.children().remove()}},n.prototype._multiKeyCardDrop=function(n,t,i,r,u){var h,f,c,e,s,l,o;for(this._externalDrop&&(h=this._externalObj),f=this.kanbanObj,$(i).height(n.height()),n.css("border-style","none"),n.children().hide(),(this._externalDrop?this._externalObj.element.find(".e-responsive"):f.element.find(".e-responsive"))&&n.find(".e-limits").hide(),n.append(i),r=typeof r=="object"?r:r.split(","),c=f.KanbanCommon._getKanbanCardData(f._currentJsonData,u.attr("id")),e=0;e<t;e++)s=n.find(".e-targetclonemulti"),s.append(ej.buildTag("div.e-columnkey")),l=this._externalDrop?h.model.keyField:f.model.keyField,o=s.find(".e-columnkey").eq(e).addClass(f.KanbanCommon._preventCardMove(c[0][l],r[e])?"":"e-disable"),o.append(ej.buildTag("div.e-text").text(r[e])),o.css({height:n.height()/t-(1+1/t)}),o.find(".e-text").css("top",o.height()/2-o.find(".e-text").height()/2)},n.prototype._createCardClone=function(n,t){var i=this.kanbanObj,r;return n=i.element.find(".e-draggedcard"),n.length>0&&n.remove(),!ej.isNullOrUndefined(t)&&$(t).hasClass("e-draggable")&&(n=ej.buildTag("div.e-draggedcard"),n.addClass(i.model.cssClass),r=$(t).clone().addClass("dragClone"),$(n).css({width:t.width()}),n.append(r),$(n).find("div:first").removeClass("e-hover"),n.appendTo(i.element)),n},n.prototype._drag=function(){var t=this,i,f=null,r=null,c=null,l=null,a=null,v,s,e,o=[],u,h,n=this.kanbanObj,y=!ej.isNullOrUndefined(n.model.fields.collapsibleCards);n.element==null||n.model.allowExternalDragAndDrop||(c=n.getContent().find("div:first"));$(n.getContent()).find(".e-rowcell.e-drag div.e-kanbancard").not(".e-js").ejDraggable({dragArea:c,clone:!0,autoScroll:!0,cursorAt:{left:-20,top:-20},dragStart:function(i){var s,c,u;if(n.model.enableTouch&&!ej.isNullOrUndefined(n._cardSelect)&&n._cardSelect=="touch")return!1;for(n.element.hasClass("e-responsive")&&n.element.parents("body").addClass("e-kbnwindow-modal"),n._off(n.element,"mouseenter mouseleave",".e-columnrow .e-kanbancard"),h=n.element.find(".e-draggedcard"),r=$(i.element).hasClass("e-cardselection")?$(i.element).parents(".e-columnrow").find(".e-cardselection:visible"):$(i.element),r.length>1&&(s=ej.buildTag("div.e-dragmultiple"),s.append(r.length+n.localizedLabels.Cards),h.children().remove(),h.append(s).css("width","90px")),u=0;u<r.length;u++)c=ej.buildTag("div.e-targetdragclone"),$(r).eq(u).after(c),$(c).css({width:$(r).eq(u).width(),height:$(r).eq(u).height()}),$(r).eq(u).addClass("e-kanban-draggedcard");for(o.length!=0&&(o=[]),u=0;u<r.length;u++)r[u].id!=""&&(v=new ej.DataManager(n._currentJsonData),o.push(v.executeLocal((new ej.Query).where(n.model.fields.primaryKey,ej.FilterOperators.equal,r[u].id))));if(t&&!ej.isNullOrUndefined(i.target)){if(e={draggedElement:r,dragTarget:i.target,data:o,event:i.event},n._trigger("cardDragStart",e))return i.cancel=!0,f&&f.remove(),!1}else return!1;return n._dropinside=typeof e.allowInternalDrop=="boolean"?e.allowInternalDrop:!1,!0},drag:function(h){var nt=null,it=null,ft=n.element.find(".e-draggedcard"),ot,hi,dt,gt,ni,ci,ti,yt,ht,c,v,p,ct,lt,ii,st,tt,pt,at,li,ai,vt,d,w,rt,ri,ui,et,wt,si,k,yi,g,b,ut,kt,bt;if(s=!1,n._cardSelect=null,n.element.find(".e-kanbantooltip").hide(),ft.addClass("e-left-rotatecard"),n._kbnMouseX=ej.isNullOrUndefined(h.event.clientX)?h.event.originalEvent.changedTouches[0].clientX:h.event.clientX,$(".e-kanban-context").length>0&&$(".e-kanban-context").css("visibility","hidden"),ej.isNullOrUndefined(n._kTouchBar)||n._kTouchBar.hide(),!n._autoKbnSwipeRight&&n.element.hasClass("e-responsive")&&n.element.offset().left+n.element.width()-50<n._kbnMouseX&&(nt=window.setInterval(function(){ft.is(":hidden")?nt&&(nt=window.clearInterval(nt)):n.element.offset().left+n.element.width()<n._kbnMouseX?nt=window.clearInterval(nt):n.element.offset().left+n.element.width()-50<n._kbnMouseX?n.KanbanAdaptive._kbnLeftSwipe():nt&&(nt=window.clearInterval(nt))},1500),n._autoKbnSwipeRight=!0,n._autoKbnSwipeLeft=!1),!n._autoKbnSwipeLeft&&n.element.hasClass("e-responsive")&&n.element.offset().left+20>n._kbnMouseX&&(it=window.setInterval(function(){ft.is(":hidden")?it&&(it=window.clearInterval(it)):n.element.offset().left+20>n._kbnMouseX?n.KanbanAdaptive._kbnRightSwipe():it&&(it=window.clearInterval(it))},1500),n._autoKbnSwipeLeft=!0,n._autoKbnSwipeRight=!1),(n.model.allowScrolling||n.model.isResponsive)&&t._kanbanAutoScroll(h),$(h.target).closest(".dragClone.e-kanbancard").length>0&&(h.target=l,clearTimeout(a),a=setTimeout(function(){h.target=t._getCursorElement(h)},10)),$(h.target).hasClass("e-emptycard")&&n.model.allowExternalDragAndDrop&&n.model.cardSettings.externalDropTarget=="#"+$(h.target).parents(".e-kanban").attr("id")){for(this._externalObj=$(h.target).parents(n.model.cardSettings.externalDropTarget).data("ejKanban"),ot=this._externalObj.model.fields.swimlaneKey,st=n.KanbanCommon._getKanbanCardData(n._currentJsonData,$(h.element).attr("id"))[0],ej.isNullOrUndefined(ot)||(hi=this._externalObj.KanbanCommon._removeIdSymbols(st[ot]),dt=ej.buildTag("tr.e-swimlanerow","",{},{id:hi,"data-role":"kanbanrow"}),gt=ej.buildTag("td.e-rowcell","",{},{"data-role":"kanbancell"}),ni=ej.buildTag("div.e-slexpandcollapse"),ci=ej.buildTag("div.e-icon e-slexpand"),ni.append(ci),ti=ej.buildTag("div.e-slkey"),ti.html(st[ot]),gt.append(ni).append(ti),dt.append(gt)),yt=ej.buildTag("tr.e-columnrow","",{},{"data-role":"kanbanrow"}),ht=this._externalObj.getContentTable(),tt=0;tt<ht.find("colgroup col").length;tt++)yt.append(ej.buildTag("td.e-rowcell e-drag e-drop e-droppable","",{},{"data-role":"kanbancell","data-ej-mappingkey":this._externalObj.model.columns[tt].key}));ej.isNullOrUndefined(ot)?ht.find("tbody").empty().append(yt):(ht.find("tbody").empty().append(dt),ht.find("tbody").append(yt))}if($(h.target).hasClass("e-targetclone")||$(h.target).hasClass("e-rowcell")||$(h.target).hasClass("e-customaddbutton")||$(u).remove(),c=$(h.target).closest(".e-kanbancard").length>0?$(h.target).closest(".e-kanbancard")[0]:h.target,lt=!ej.isNullOrUndefined(n.model.fields.swimlaneKey)&&n.model.swimlaneSettings.allowDragAndDrop,p=$(h.element).closest("td.e-rowcell").addClass("e-dragged"),ii=p.siblings().length,p.parent().find(".e-dragpossible").length==0){if(st=n.KanbanCommon._getKanbanCardData(n._currentJsonData,$(h.element).attr("id"))[0],ii>0)for(tt=0;tt<ii;tt++)n.KanbanCommon._preventCardMove(st[n.model.keyField],n.model.columns[p.parent().children().index(p.siblings()[tt])].key)&&p.siblings().eq(tt).addClass("e-dragpossible");if(lt)for(pt=n.element.find(".e-columnrow"),at=0;at<pt.length;at++)li=pt.eq(at).index(p.parent()),li==-1&&(ai=pt.eq(at).find(".e-rowcell"),ai.addClass("e-dragpossible"))}if(p.siblings(".e-drop.e-dragpossible").addClass("e-dropping"),n._dropinside?p.addClass("e-dragpossible e-drop e-droppable e-droppableremove"):p.addClass("e-dragpossible e-drop"),e={draggedElement:r,data:o,dragTarget:c,event:h.event},vt=n.model.allowExternalDragAndDrop,d=n.model.cardSettings.externalDropTarget,vt&&d=="#"+$(h.target).parents(".e-kanban").attr("id")&&$(h.target).hasClass("e-rowcell")&&$(d).find(".e-rowcell.e-drag").addClass("e-dragpossible e-dropping"),vt=n.model.allowExternalDragAndDrop,d=n.model.cardSettings.externalDropTarget,vt&&d=="#"+$(h.target).parents(".e-kanban").attr("id")&&($(h.target).hasClass("e-rowcell")||$(h.target).parents(".e-rowcell").length>0)&&($(d).find("td.e-rowcell.e-drag").addClass("e-dragpossible e-dragged e-dropping"),t._externalObj=$(h.target).parents(d).data("ejKanban"),t._externalDrop=!0),l=v=$(c).closest("td.e-rowcell"),ct=v.parents(".e-columnrow").find(".e-cardselection"),v&&v.hasClass("e-droppable")&&(lt?!0:$(v).parent().has(h.element[0]).length>0?!0:"#"+$(v).parents(".e-kanban").attr("id")==n.model.cardSettings.externalDropTarget?h.element.length>0:!1)&&(c!=h.element[0]||ct.length>1)){if(document.body.style.cursor="",$(u).parents(".e-rowcell").index()!=v.index()&&$(u).remove(),v.find(".e-kanbancard").removeClass("e-hover"),n.element.hasClass("e-responsive")&&(ri=$(c).parents(".e-rowcell"),!$(c).hasClass("e-kanbancard")&&$(c).parents(".e-kanbancard").length==0&&ri.length>0&&(c=ri,v=$(c).find(".e-cell-scrollcontent").children().eq(0))),w=v.children().last(),w.hasClass("e-shrinkheader")&&(w=w.prev()),($(c).hasClass("e-columnkey")||$(c).parents(".e-columnkey").length>0||$(c).find(".e-columnkey").length>0)&&(ui=$(c).hasClass("e-rowcell")?$(c):$(c).parents(".e-rowcell"),et=h.target,$(et).hasClass("e-columnkey")&&!$(et).hasClass("e-disable")||$(et).parents(".e-columnkey").length>0&&!$(et).parents(".e-disable").length>0?(t._removeKanbanCursor(),ui.find(".e-columnkey.e-active").removeClass("e-active"),ui.find(".e-columnkey.e-multiclonestyle").removeClass("e-multiclonestyle"),$(et).closest(".e-columnkey").not(".e-disable").addClass("e-active"),$(v).find(".e-active").prev().addClass("e-multiclonestyle")):t._changeKanbanCursor(c,f,h)),($(c).hasClass("e-rowcell")||$(c).hasClass("e-kanbancard")||$(c).hasClass("e-targetdragclone")||$(c).hasClass("e-swimlane-name")||$(c).hasClass("e-limits")||$(c).parents(".e-rowcell").not(".e-drop").length>0)&&$(i).is(":visible")&&($(c).index()!=i.parents(".e-rowcell").index()||t._externalDrop&&$(c).index()==i.parents(".e-rowcell").index()||!ej.isNullOrUndefined(d)&&$(d).find(".e-targetclonemulti").length>0||y&&$(c).parents(".e-rowcell").index()==p.index()))$(i).siblings(".e-kanbancard").not(".e-kanban-draggedcard").show(),ej.isNullOrUndefined($(i).siblings(".e-toggle-area"))||$(i).siblings(".e-toggle-area").show(),$(i).parents(".e-rowcell").css("border-style",""),$(i).siblings(".e-targetdragclone,.e-customaddbutton,.e-limits").show(),$(i).remove(),i.children().remove();else if($(c).hasClass("e-rowcell")&&(h.element[0]!=w[0]||ct.length>1)&&!$(c).hasClass("e-shrink")&&!$(w[0]).hasClass("e-targetclonemulti")){if(ut=$(c).hasClass("e-dragpossible"),wt=$(c).parent().children().index(c),g=t._externalDrop?t._externalObj.model.columns[wt].key:n.model.columns[wt].key,b=(typeof g=="object"?g:g.split(",")).length,w.length==0||v.children().length==0||w.offset().top+w.height()<$(f).offset().top||b>1)if(ut&&(b==1||wt==$(p).parent().children().index(p)&&(r.length==1||r.length>1&&n.selectedRowCellIndexes[0].cellIndex.length==1)||r.length>1&&!n.selectedRowCellIndexes[0].cellIndex.length>1&&$.inArray(v.index(),n.selectedRowCellIndexes[0].cellIndex)!=-1))if(b>1&&p.parents(".e-kanban").attr("id")!=$(h.target).parents(".e-kanban").attr("id"))!v.find(".e-columnkey").length>0&&t._multicloneremove(v,i),t._multiKeyCardDrop(v,b,i,g,r);else{rt=$(c).height();$(u).css("display")=="none"&&$(u).css("display","block");var vi=$(h.element[0]).height(),fi=$(c).width(),w=v.children().last(),ei=v,oi=v.find(".e-cell-scrollcontent");n.element.hasClass("e-responsive")&&oi.length>0&&(w=oi.children().last(),ei=oi.children());w.hasClass("e-customaddbutton")?w.before($(u).height(vi).width(fi-28)):y?ei.append($(u).height(ft.find(".e-kanbancard").outerHeight()).width(fi-28)):ei.append($(u).height(vi).width(fi-28));rt<$(c).height()&&(v.height(v.height()),n._tdHeightDiff=$(c).height()-rt)}else ut&&b>1?(!v.find(".e-columnkey").length>0&&t._multicloneremove(v,i),t._multiKeyCardDrop(v,b,i,g,r)):t._changeKanbanCursor(c,f,h)}else $(c).hasClass("e-rowcell")&&$(c).hasClass("e-shrink")&&$(c).hasClass("e-dragpossible")||($(c).hasClass("e-shrinkheader IE")||$(c).hasClass("e-shrinkcount")||$(c).hasClass("e-shrinklabel"))&&$(c).parents(".e-rowcell").hasClass("e-dragpossible")?(si=$(c).hasClass("e-rowcell")?$(c):$(c).parents(".e-rowcell"),bt=new ej.DataManager(n.model.columns).executeLocal((new ej.Query).where("key",ej.FilterOperators.equal,si.attr("data-ej-mappingkey"))),n.toggleColumn(bt[0].headerText),n.element.find(".e-draggedcard").width(n.element.find(".e-targetdragclone").width()),u.width(n.element.find(".e-targetdragclone").width()),si.addClass("e-togglevisible")):$(c).hasClass("e-shrink")&&!$(c).hasClass("e-dragpossible")?t._changeKanbanCursor(c,f,h):($(c).hasClass("e-kanbancard")||$(c).hasClass("e-targetclone"))&&(k=$(c).parents(".e-rowcell"),g=t._externalDrop?t._externalObj.model.columns[k.parent().children().index(k)].key:n.model.columns[k.parent().children().index(k)].key,b=(typeof g=="object"?g:g.split(",")).length,rt=k.height(),ut=$(c).parents(".e-rowcell").hasClass("e-dragpossible"),yi=ej.isNullOrUndefined(h.event.pageY)?h.event.originalEvent.changedTouches[0].pageY:h.event.pageY,$(c).offset().top+$(c).height()/2>=yi&&ut&&(r.length>1&&!n.selectedRowCellIndexes[0].cellIndex.length>1&&$.inArray(v.index(),n.selectedRowCellIndexes[0].cellIndex)!=-1||b==1||$(v).parent().children().index(v)==$(p).parent().children().index(p)&&p.parents("tr").index()==v.parents("tr").index())?$(c).prev()[0]!=h.element[0]||ct.length>1?!$(c).hasClass("e-targetclone")&&($(c).prev().length==0||$(c).prev().length>0&&!$(c).prev().hasClass("e-targetdragclone"))&&(t._removeKanbanCursor(),$(u).insertBefore($(c)).height(ft.find(".e-kanbancard").outerHeight()).width($(c).width()).removeClass("e-targetappend")):t._changeKanbanCursor(c,f,h):($(c).next()[0]!=h.element[0]||ct.length>1)&&ut&&(r.length>1&&!n.selectedRowCellIndexes[0].cellIndex.length>1&&$.inArray(v.index(),n.selectedRowCellIndexes[0].cellIndex)!=-1||b==1||$(v).parent().children().index(v)==$(p).parent().children().index(p)&&(p.parents("tr").index()==v.parents("tr").index()||lt))?(rt=$(c).parents(".e-rowcell").height(),!$(c).hasClass("e-targetclone")&&($(c).next().length==0||$(c).next().length>0&&!$(c).next().hasClass("e-targetdragclone"))&&(s=!0,t._removeKanbanCursor(),$(u).insertAfter($(c)).height(ft.find(".e-kanbancard").outerHeight()).width($(c).width()).addClass("e-targetappend"))):ut&&b>1&&!k.find(".e-columnkey").length>0&&!($(v).parent().children().index(v)==$(p).parent().children().index(p))?(t._multicloneremove(v,i),t._multiKeyCardDrop(k,b,i,g,r)):t._changeKanbanCursor(c,f,h),rt<k.height()&&(k.height(k.height()),n._tdHeightDiff=k.height()-rt));$(u).parent().hasClass("e-rowcell")&&p.parent().has($(u).parent()).length==0&&lt?(n.element.find(".e-columnrow .e-rowcell.e-drop").addClass("e-dropping"),p.removeClass("e-dropping").addClass("e-dragged")):(n.element.find(".e-columnrow .e-rowcell").removeClass("e-dropping"),p.addClass("e-dragged"),p.siblings(".e-drop.e-dragpossible ").addClass("e-dropping"))}else($(c).parents(".e-rowcell").not(".e-droppable").length>0||$(c).hasClass("e-swimlane-name")||$(c).hasClass("e-limits")||$(c).hasClass("e-swimlanerow")||$(c).parents(".e-swimlanerow").length>0)&&$(i).is(":visible")&&($(i).siblings(".e-kanbancard").not(".e-kanban-draggedcard").show(),ej.isNullOrUndefined($(i).siblings(".e-toggle-area"))||$(i).siblings(".e-toggle-area").show(),$(i).parents(".e-rowcell").css("border-style",""),$(i).siblings(".e-targetdragclone,.e-customaddbutton,.e-limits").show(),$(i).remove(),i.children().remove()),($(c).hasClass("e-swimlanerow")||$(c).parents(".e-swimlanerow").length>0)&&$(u).is(":visible")&&$(u).remove(),t._changeKanbanCursor(c,f,h);return(kt=n.element.find(".e-togglevisible"),kt.length>0&&!$(c).hasClass("e-togglevisible")&&$(c).parents(".e-togglevisible").length==0&&(bt=new ej.DataManager(n.model.columns).executeLocal((new ej.Query).where("key",ej.FilterOperators.equal,kt.attr("data-ej-mappingkey"))),n.toggleColumn(bt[0].headerText),n.element.find(".e-draggedcard").width(n.element.find(".e-targetdragclone").width()),u.width(n.element.find(".e-targetdragclone").width()),kt.removeClass("e-togglevisible")),vt&&$(h.target).parents(d).length!=0&&$(h.target).parents(".e-kanban").length==0&&(document.body.style.cursor="",$(d).css("cursor","")),t._externalDrop=!1,n._trigger("cardDrag",e))?!1:!0},dragStop:function(i){n._cardSelect=null;i.dragData=o;i.dragEle=r;t._dragStop(i,f,s)},helper:function(r){return n.model.enableTouch&&!ej.isNullOrUndefined(n._cardSelect)&&n._cardSelect=="touch"?!1:(u=ej.buildTag("div.e-targetclone"),i=ej.buildTag("div.e-targetclonemulti"),$(u).css({width:r.element.width(),height:r.element.height()}),f=t._createCardClone(f,r.element))}})},n}();window.ej.createObject("ej.KanbanFeatures.DragAndDrop",u,window);f=function(){function n(n){this.kanbanObj=null;this._dropDownManager=null;this._editForm=null;this._onKbnDialogBeforeClose=function(){var n=this.kanbanObj,t=n.element.find("#"+n._id+"_dialogEdit_wrapper");t.addClass("e-kbndialog-transitionclose")};this._onKbnDialogBeforeOpen=function(){var n=this.kanbanObj,t=n.element.find("#"+n._id+"_dialogEdit_wrapper");t.removeClass("e-kbndialog-transitionclose")};this.kanbanObj=n}return n.prototype._processEditing=function(){var n=this.kanbanObj,t=this._columnToSelect(),i=this,r;n.model.query._fromTable!=""&&t.from(n.model.query._fromTable);n._dataSource()instanceof ej.DataManager&&t.queries.length&&!n._dataManager.dataSource.offline?(r=n._dataSource().executeQuery(t),r.done(ej.proxy(function(n){i._dropDownManager=new ej.DataManager(n.result);i._addDialogEditingTemplate()}))):i._addDialogEditingTemplate()},n.prototype._renderDialog=function(){return ej.buildTag("div.e-dialog e-kanbandialog e-dialog-content e-shadow e-widget-content","",{display:"none"},{id:this.kanbanObj._id+"_dialogEdit"})},n.prototype.closeEdit=function(){var n=this.kanbanObj,t;(n.model.editSettings.allowEditing||n.model.editSettings.allowAdding)&&(n.model.editSettings.editMode=="dialog"||n.model.editSettings.editMode=="dialogtemplate"?$("#"+n._id+"_dialogEdit").ejDialog("close"):(n.model.editSettings.editMode=="externalform"||n.model.editSettings.editMode=="externalformtemplate")&&(n.model.editSettings.formPosition=="right"&&$(n.element).children().css("width","100%"),$("#"+n._id+"_externalEdit").css("display","none")),n.refreshTemplate(),t={requestType:"cancel"},n.KanbanCommon._processBindings(t));n.element.parents("body").removeClass("e-kbnwindow-modal");n.element.hasClass("e-responsive")&&n.kanbanWindowResize()},n.prototype._columnToSelect=function(){for(var i=[],t=this.kanbanObj,n=0;n<t.model.editSettings.editItems.length;n++)t.model.editSettings.editItems[n].editType=="dropdownedit"&&ej.isNullOrUndefined(t.model.editSettings.editItems[n].dataSource)&&i.push(t.model.editSettings.editItems[n].field);return i.length?(new ej.Query).select(i):new ej.Query},n.prototype.cancelEdit=function(){var n=this.kanbanObj,t;(n.model.editSettings.allowEditing||n.model.editSettings.allowAdding)&&(n.model.editSettings.editMode=="dialog"||n.model.editSettings.editMode=="dialogtemplate"?$("#"+n._id+"_dialogEdit").ejDialog("close"):(n.model.editSettings.editMode=="externalform"||n.model.editSettings.editMode=="externalformtemplate")&&(n.model.editSettings.formPosition=="right"&&$(n.element).children().css("width","100%"),$("#"+n._id+"_externalEdit").css("display","none")),n.refreshTemplate(),t={requestType:"cancel",dialogtype:"cancel"},n.KanbanCommon._processBindings(t));n.element.parents("body").removeClass("e-kbnwindow-modal");n.element.hasClass("e-responsive")&&n.kanbanWindowResize()},n.prototype._renderExternalForm=function(){var n=this.kanbanObj,f=ej.buildTag("div","",{display:"none"},{id:n._id+"_externalEdit","class":"e-form-container"}),t=ej.buildTag("div","","",{id:n._id+"_eFormHeader","class":"e-form-titlebar"}),e=ej.buildTag("span","","",{"class":"e-form-title"}),i=ej.buildTag("div","","",{id:n._id+"_closebutton","class":"e-externalform-icon"}),o=ej.buildTag("span","","",{"class":"e-icon e-externaledit e-cancel"});i.append(o);t.append(e).append(i);var r=ej.buildTag("div","","",{id:n._id+"_eFormContent","class":"e-form-content"}),u=ej.buildTag("div","","",{id:n._id+"_externalForm","class":"e-externalform"}),s=ej.buildTag("div","","",{"class":"e-externalformedit"});return u.append(s),r.append(u),f.append(t).append(r)},n.prototype._maxZindex=function(){var n=1;return n=Math.max.apply(null,$.map($("body *"),function(n){if($(n).css("position")=="absolute")return parseInt($(n).css("z-index"))||1})),(n==undefined||n==null)&&(n=1),n},n.prototype.addCard=function(n,t){var i=this.kanbanObj,s,e,f,u,o,r;if(i._currentJsonData.length==0){if(i.model.showColumnWhenEmpty&&i.model.editSettings.allowAdding){for(u=i.model.editSettings.editItems,e={requestType:"add"},i._currentData={},i._isAddNewClick&&i.model.keyField&&(i._currentData[i.model.keyField]=i.model.columns[$(i._newCard).index()].key),r=0;r<u.length;r++)i._currentData[u[r].field]=ej.isNullOrUndefined(u[r].defaultValue)?ej.isNullOrUndefined(i._currentData[u[r].field])?"":i._currentData[u[r].field]:u[r].defaultValue;u.length==0&&(i._currentData[i.model.fields.primaryKey]="");e.data=i._currentData;i.element.find(".e-kanbandialog").removeAttr("data-id");i.KanbanCommon._processBindings(e);return}s=[];s.push(t);i.element.ejKanban({dataSource:s});return}if(i.model.editSettings.allowAdding)if(f=i.model.fields.primaryKey,n!="bulk"&&$.type(i._currentJsonData[0][f])=="number"&&(n=parseInt(n)),e={data:t,requestType:"save",action:"add",primaryKeyValue:n},i._cAddedRecord=t,i._saveArgs=e,n&&t)i.updateCard(n,t),i.model.showColumnWhenEmpty&&i.model.initiallyEmptyDataSource&&(i._kbnSwimLaneData.push(t[i.model.fields.swimlaneKey]),i.KanbanContext._kanbanSubMenu());else{for(u=i.model.editSettings.editItems,e={requestType:"add"},i._currentData={},i._isAddNewClick&&(o=i.model.fields.swimlaneKey,!ej.isNullOrUndefined(o)&&i.model.swimlaneSettings.headers.length>0?i._currentData[o]=$(i._newCard).parent().prev(".e-swimlanerow").find(".e-slkey").attr("data-ej-slmappingkey"):o&&(i._currentData[o]=$(i._newCard).parent().prev(".e-swimlanerow").find(".e-slkey").text()),i.model.keyField&&(i._currentData[i.model.keyField]=i.model.columns[$(i._newCard).index()].key)),r=0;r<u.length;r++)i._currentData[u[r].field]=ej.isNullOrUndefined(u[r].defaultValue)?ej.isNullOrUndefined(i._currentData[u[r].field])?"":i._currentData[u[r].field]:u[r].defaultValue;i._currentData[f]=i._currentJsonData[i._currentJsonData.length-1][f];$.type(i._currentJsonData[0][f])=="string"&&(i._currentData[f]=parseInt(i._currentData[f]));i._currentData[f]=i._currentData[f]+1;$.type(i._currentJsonData[0][f])=="string"&&(i._currentData[f]=i._currentData[f].toString());e.data=i._currentData;i.element.find(".e-kanbandialog").removeAttr("data-id");i.KanbanCommon._processBindings(e)}},n.prototype.deleteCard=function(n){var u,t=this.kanbanObj,f,i,e,r,o=t.model.fields.primaryKey,t=this.kanbanObj;if(f=t.element.find("#"+n),e=new ej.DataManager(t._currentJsonData),r=new ej.Query,r=r.where(t.model.fields.primaryKey,ej.FilterOperators.equal,n),i=e.executeLocal(r),$.type(t._currentJsonData[0][o])=="number"&&(n=parseInt(n)),u={div:f,data:i[0],requestType:"delete",primaryKeyValue:n},t._saveArgs=u,t._trigger("actionBegin",u))return!0;t._cDeleteData=i;t.updateCard(n,i[0])},n.prototype.startEdit=function(n){var t=this.kanbanObj;if($.type(n)!="object"&&(n=t.element.find("#"+n)),t.model.editSettings.allowEditing&&n.hasClass("e-kanbancard")){var f,i,e=t.model.fields.primaryKey,r=n.attr("id"),s=n.hasClass("e-rowcell")?n.index():n.closest(".e-rowcell").index(),h=t.getIndexByRow(n.closest("tr")),o,u=new ej.Query;if(f=n.closest(".e-kanbancard"),t._cardEditClick==!0?t._currentData.data=t._dblArgs.data:(o=new ej.DataManager(t._currentJsonData),u=u.where(e,ej.FilterOperators.equal,n.attr("id")),t._currentData=o.executeLocal(u)),$.type(t._currentJsonData[0][e])=="number"&&(r=parseInt(r)),i={target:n,rowIndex:h,data:t._currentData[0],columnIndex:s,cardIndex:f.index(),primaryKeyValue:r},t._trigger("beginEdit",i),i.requestType="beginedit",i.cancel)return;t.KanbanCommon._processBindings(i)}},n.prototype._refreshEditForm=function(n){var t=this.kanbanObj,v,s,b,g,y,l,p,f,i,r,u,c,a,o,h,e,k,d,w;for(t.model.editSettings.editMode=="dialog"||t.model.editSettings.editMode=="dialogtemplate"?(b=t.element.find("#"+t._id+"_dialogEdit_wrapper"),y=95,b.show(),l=$("#"+t._id+"_dialogEdit"),v=l.find("tr").find(".e-rowcell")):(t.model.editSettings.editMode=="externalform"||t.model.editSettings.editMode=="externalformtemplate")&&(g=t.element.find("#"+t._id+"_externalEdit"),y=133,g.show(),l=$("#"+t._id+"_externalForm"),v=l.find("div").find(".e-rowcell")),e=0;e<v.length;e++)t._tdsOffsetWidth[e]=v.get(e).offsetWidth;for((t.model.editSettings.editMode=="dialog"||t.model.editSettings.editMode=="dialogtemplate")&&(s=ej.max(t._tdsOffsetWidth)*(y/100)),p=0;p<t.model.editSettings.editItems.length;p++){if(f=t.model.editSettings.editItems[p],i=l.find("#"+t._id+"_"+f.field),(t.model.editSettings.editMode=="externalform"||t.model.editSettings.editMode=="externalformtemplate")&&(s=ej.max(t._tdsOffsetWidth)*(y/100),$(i).parent().css("width",s+"px")),f.editType=="stringedit"&&i.width(s-4),c=f.editType=="textarea",a=t.model.editSettings.editMode=="externalform"||t.model.editSettings.editMode=="externalformtemplate",u=f.editParams,!ej.isNullOrUndefined(u)&&c?(ej.isNullOrUndefined(u.width)||(i.width(u.width),c&&a&&i.parent().width(u.width+8)),ej.isNullOrUndefined(u.height)||(i.height(u.height),c&&a&&i.parent().height(u.height+8))):c&&(t.model.editSettings.editMode=="dialog"||t.model.editSettings.editMode=="dialogtemplate")?i.width(250).height(95):c&&a&&t.model.editSettings.formPosition=="bottom"?(i.height(95),$(i).css({width:"400px"}),$(i).parent().css({width:"500px"})):c&&a&&t.model.editSettings.formPosition=="right"&&(i.css({width:s-8}),i.height(150),$(i).parent().css("width",s+8)),f.editType=="rteedit")r={width:"450px",height:"260px",minHeight:"240px",locale:t.model.locale,enableRTL:t.model.enableRTL},u=f.editParams,r.toolsList=["style","links"],r.tools={style:["bold","italic","underline"],casing:["upperCase","lowerCase"],links:["createLink"]},ej.isNullOrUndefined(u)||$.extend(r,u),l.find("#"+t._id+"_"+f.field).ejRTE(r);else if(f.editType=="dropdownedit"){if(r={width:s,enableIncrementalSearch:!0,enableRTL:t.model.enableRTL},o=i.val(),u=f.editParams,h=[],f.field==t.model.keyField&&n.requestType==="beginedit")for(e=0;e<i.children().length;e++)k=i.children().eq(e).val(),ej.isNullOrUndefined(t.model.workflows)||(t.KanbanCommon._preventCardMove(o,i.children().eq(e).val())?!0:h.push(e)),o!=k&&(d=t._getColumnKeyIndex(k),ej.isNullOrUndefined(d)||t.model.columns[d].allowDrop||$.inArray(e,h)===-1&&h.push(e),t.model.columns[t._getColumnKeyIndex(o)].allowDrag||$.inArray(e,h)===-1&&h.push(e));if(h.length>0)for(w=h.length-1;w>=0;w--)$(i.children()).eq(h[w]).remove();i.hasClass("e-disable")&&(r.enabled=!1);ej.isNullOrUndefined(u)||$.extend(r,u);t._kbnDdlWindowResize=!0;i.ejDropDownList(r);i.ejDropDownList("setSelectedValue",i.val());t._kbnDdlWindowResize=!1}else f.editType=="numericedit"?(r={width:s},o=i.val(),u=f.editParams,r.cssClass=t.model.cssClass,r.showSpinButton=!0,r.enableRTL=t.model.enableRTL,r.locale=t.model.locale,o.length&&(r.value=parseFloat(o)),i.hasClass("e-disable")&&(r.enabled=!1),ej.isNullOrUndefined(u)||$.extend(r,u),i.ejNumericTextbox(r)):f.editType=="datepicker"?(r={width:s},o=i.val(),u=f.editParams,r.cssClass=t.model.cssClass,r.displayDefaultDate=!0,r.enableRTL=t.model.enableRTL,r.locale=t.model.locale,o.length&&(r.value=new Date(o)),i.hasClass("e-disable")&&(r.enabled=!1),ej.isNullOrUndefined(u)||$.extend(r,u),i.ejDatePicker(r)):f.editType=="datetimepicker"&&(r={width:s,cssClass:t.model.cssClass,locale:t.model.locale,showPopupButton:!1,enableRTL:t.model.enableRTL},o=i.val(),u=f.editParams,o.length&&(r.value=new Date(o)),i.hasClass("e-disable")&&(r.enabled=!1),ej.isNullOrUndefined(u)||$.extend(r,u),i.ejDateTimePicker(r));f.editType!="textarea"&&$(i.outerWidth(s)).height(28);ej.browserInfo().name=="msie"&&parseInt(ej.browserInfo().version,10)==8&&i.css("line-height","26px")}a&&(t._editForm=t.element.find(".e-externalform"));(t.model.editSettings.editMode=="dialog"||t.model.editSettings.editMode=="dialogtemplate")&&(t._editForm=t.element.find(".kanbanform"),b.hide());this._formFocus()},n.prototype._setKanbanDdlValue=function(n){for(var e,i,r=n.find("select.e-field"),u,f=n.find("input.e-field.e-dropdownlist"),t=0;t<r.length;t++)e={},e[this.kanbanObj._id+"drpDownTempl"]="{{:"+r[t].name.replace(/[^a-z0-9\s]/gi,"")+"}}",$.templates(e),u=$.render[this.kanbanObj._id+"drpDownTempl"](this.kanbanObj._currentData),n.find("select").eq(t).val(u).attr("selected","selected"),r.eq(t).val(u);for(i=0;i<f.length;i++)f.eq(i).val(ej.getObject(f.eq(i).attr("name"),this.kanbanObj._currentData[0]))},n.prototype._editAdd=function(n){var f,t=this.kanbanObj,p=t.element.find(".e-kanbandialog"),e=$(n.target),a,h=new ej.Query,c=t.model.fields.primaryKey,o=document.createElement("div"),l=$(o),i,r,u,v,s,y;n.requestType=="add"?l.addClass("e-addedrow"):(t._currentData={},e.hasClass("e-kanbancard")?i=e.attr("id"):e.parents(".e-kanbancard").length>0&&(i=e.parents(".e-kanbancard").attr("id")),$.type(t._currentJsonData[0][c])=="number"&&(i=parseInt(i)),a=new ej.DataManager(t._currentJsonData),h=h.where(c,ej.FilterOperators.equal,i),t._currentData=a.executeLocal(h),l.addClass("e-editedrow"));o.innerHTML=$.render[t._id+"_dialogEditingTemplate"](t._currentData);this._setKanbanDdlValue(l);n.requestType=="add"?ej.isNullOrUndefined(t.model.fields.title)||$.isEmptyObject(t._newData)?u=t.localizedLabels.AddFormTitle:(r=t.model.fields.title,u=t.localizedLabels.EditFormTitle+t._newData[r]):(r=c,ej.isNullOrUndefined(t.model.fields.title)||(r=t.model.fields.title),u=t.localizedLabels.EditFormTitle+t._currentData[0][r]);t.model.editSettings.editMode=="dialog"||t.model.editSettings.editMode=="dialogtemplate"?(f=$("#"+t._id+"_dialogEdit"),f.html($(o)),v={cssClass:t.model.cssClass,enableRTL:t.model.enableRTL,width:"auto",content:"#"+t._id,close:$.proxy(this.closeEdit,this),beforeClose:$.proxy(this._onKbnDialogBeforeClose,this),beforeOpen:$.proxy(this._onKbnDialogBeforeOpen,this),open:$.proxy(this._onKbnDialogOpen,this),enableModal:!0,enableResize:!1,title:u},f.ejDialog(v),f.ejDialog("open"),p.attr("data-id",i),s=t.element.find("#"+t._id+"_dialogEdit_wrapper"),s.css("left","0"),y=self!=parent?ej.browserInfo().name=="chrome"?$(window).scrollTop():-$(window.frameElement).offset().top:$(window).scrollTop(),s.css("top",y),s.hide()):(t.model.editSettings.editMode=="externalform"||t.model.editSettings.editMode=="externalformtemplate")&&($("#"+t._id+"_externalEdit").css("display","block").css("z-index",this._maxZindex()+1),$("#"+t._id+"_externalForm").find(".e-externalformedit").html($(o)),$("#"+t._id+"_eFormHeader").find(".e-form-title").text(u),$("#"+t._id+"_externalForm").attr("data-id",i),this._externalFormPosition());$.isFunction($.validator)&&(this.initValidator(),this.setValidation())},n.prototype._onKbnDialogOpen=function(){var n=this.kanbanObj,t=n.element.find("#"+n._id+"_dialogEdit_wrapper"),i,r,o,u,f,s,h,l,a,y,v,e,c;for(t.css("position","absolute"),o=$(n.element)[0],u=o.offsetTop,f=o.offsetLeft,s=$(o).find(".e-kanbancontent")[0].offsetWidth,h=$(o).find(".e-kanbancontent")[0].offsetHeight+$(n.element).find(".e-kanbanheader")[0].offsetHeight,window.pageYOffset>u?(i=u+h>window.pageYOffset+window.innerHeight?Math.max(0,(window.innerHeight-t.outerHeight())/2):Math.max(0,(u+h-window.pageYOffset-t.outerHeight())/2),i=window.pageYOffset+i):(i=u+h>window.pageYOffset+window.innerHeight?Math.max(0,(window.innerHeight-(u-window.pageYOffset)-t.outerHeight())/2):Math.max(0,(h-t.outerHeight())/2),i=u+i),window.pageXOffset>f?(r=f+s>window.pageXOffset+window.innerWidth?Math.max(0,(window.innerWidth-t.outerWidth())/2):Math.max(0,(f+s-window.pageXOffset-t.outerWidth())/2),r=window.pageXOffset+r):(r=f+s>window.pageXOffset+window.innerWidth?Math.max(0,(window.innerWidth-(f-window.pageXOffset)-t.outerWidth())/2):Math.max(0,(s-t.outerWidth())/2),r=f+r),t.css("top",i+"px"),t.css("left",r+"px"),l=t.find(".e-rte").not(".e-rte-wrapper"),a=0;a<l.length;a++)y=l.eq(0).attr("id"),$("#"+y).ejRTE("refresh");t.show();this._formFocus();n._editForm.find(".e-rte").length>0&&n.element.find("#"+n._id+"_dialogEdit").data("ejDialog")._resetScroller();n.model.isResponsive&&n.element.hasClass("e-responsive")?(n._on($(".e-kanban-editdiv"),$.isFunction($.fn.tap)&&n.model.enableTouch?"tap":"click","",n._kbnAdaptEditClickHandler),n.KanbanAdaptive._setAdaptEditWindowHeight(),t.parents(".e-kanban").length>0&&t.appendTo("body")):$(window).scrollTop()+$(window).height()<t.height()+i&&(v=$("#"+n._id+"_dialogEdit"),e=v.parents(".e-dialog-scroller"),c,c=e.prev(".e-titlebar"),e.ejScroller({height:$(window).height()-(c.height()+n.element.find(".e-kanbandialog").offset().top)<0?e.css("height","auto"):$(window).height()-(c.height()+n.element.find(".e-kanbandialog").offset().top)}),e.data("ejScroller").refresh(),v.css("height",e.height()))},n.prototype._externalFormPosition=function(){var n=this.kanbanObj,t=$(n.element).offset(),r=$(n.element).width(),u=$(n.element).height(),i=$("#"+n._id+"_externalEdit");switch(n.model.editSettings.formPosition){case"right":$(i).find(".e-close").removeClass("e-bottomleft").addClass("e-topright");$("#"+n._id+"_eFormContent").height("auto");n.model.allowScrolling||n.model.isResponsive?($(i).css({left:t.left+r+2+"px",top:t.top+"px",position:"absolute",width:"25%"}),$(".e-externalrow").css("padding-right","0px")):($(n.element).css({width:"100%"}),$(n.element).children().not(".e-form-container").css("width","73%"),$(i).css({left:t.left+$(n.element).children().width()+2+"px",top:t.top+"px",position:"absolute",width:"25%"}));break;case"bottom":$(i).find(".e-close").removeClass("e-topright").addClass("e-bottomleft");$(i).css({left:t.left+"px",top:t.top+u+1+"px"});$("#"+n._id+"_eFormContent").width("100%");(n.element.find(".e-scrollbar").hasClass("e-hscrollbar")||n.model.isResponsive)&&$("#"+n._id+"_externalEdit").css({border:"none","border-top":"1px solid"})}},n.prototype._formFocus=function(){for(var n,i=$(this.kanbanObj._editForm).find("input,select,div.e-field,textarea.e-field"),r=!1,t=0;t<i.length;t++)n=i.eq(t),(n.is(":disabled")||r||n.is(":hidden")&&typeof(n.data("ejDropDownList")||n.data("ejNumericTextbox"))!="object")&&!n.hasClass("e-rte")||(this._focusKbnDialogEle(n),r=!0)},n.prototype._focusKbnDialogEle=function(n){if(n.length)if((n[0].tagName.toLowerCase()!="select"||n.hasClass("e-field e-dropdownlist"))&&n[0].tagName.toLowerCase()!="input"&&(n[0].tagName.toLowerCase()!="textarea"||n.hasClass("e-numerictextbox")))n.hasClass("e-field e-dropdownlist")?n.closest(".e-ddl").focus():n.hasClass("e-numerictextbox")?n.siblings("input:visible").first().select().focus():n.find("input:visible,select").first().select().focus();else if(n.hasClass("e-rte")){var t=n.data("ejRTE");$(t).focus();t.selectAll()}else n.focus().select(),n[0].focus()},n.prototype.endEdit=function(){var n=this.kanbanObj,i,h,r,a,y,t,f,l,u,o,e,c,p,s;if(n.model.editSettings.allowEditing||n.model.editSettings.allowAdding){if(i=n.model.fields.primaryKey,h=n.model.editSettings.editMode=="dialog"||n.model.editSettings.editMode=="dialogtemplate"?$("#"+n._id+"_dialogEdit"):n.element.find(".e-externalform"),f={},!this.editFormValidate())return!0;if(r=document.getElementById(n._id+"EditForm"),a=$(r),y=a.closest("div"),ej.isNullOrUndefined(h.attr("data-id")))if(n.model.showColumnWhenEmpty&&n._currentJsonData.length==0&&$.type(n._currentData[i])=="string"){var w=n.element.find(".kanbanform"),b=JSON.stringify(w.serializeArray()),v=JSON.parse(b);for(l=0;l<v.length;l++)if(v[l].name==i){t=v[l].value;f[i]=t;break}}else t=n._currentJsonData[n._currentJsonData.length-1][i],$.type(n._currentJsonData[0][i])=="string"&&(t=parseInt(t)),t=t+1,$.type(n._currentJsonData[0][i])=="string"&&(t=t.toString()),f[i]=t;else t=h.attr("data-id"),$.type(n._currentJsonData[0][i])=="number"&&(t=parseInt(t));for(u=0;u<r.length;u++)if(!y.hasClass("e-addedrow")||!$(r[u]).hasClass("e-identity")){if(o=r[u].name,e=$(r[u]),(e.hasClass("e-dropdownlist")||e.hasClass("e-input"))&&(ej.isNullOrUndefined(e.attr("id"))||e.attr("id").indexOf("_input")!=-1||e.attr("id").indexOf("_hidden")!=-1)){!ej.isNullOrUndefined(a[1])&&ej.isNullOrUndefined(r[u+1])&&(r=a[1],u=-1);continue}o!=undefined&&(o==""&&(r[u].id.indexOf("Save")!=-1||r[u].id.indexOf("Cancel")!=-1?o="":(o=r[u].id.replace(n._id+"_",""),o=o.replace("_hidden",""))),o!=""&&f[o]==null?(c=r[u].value,$(r[u]).hasClass("e-datepicker")?c=e.ejDatePicker("model.value"):$(r[u]).hasClass("e-datetimepicker")?c=e.ejDateTimePicker("model.value"):e.is(".e-numerictextbox")?c=e.ejNumericTextbox("getValue"):e.data("ejDropDownList")&&(c=e.ejDropDownList("getSelectedValue")),p=r[u].type!="checkbox"?c:$(r[u]).is(":checked"),f[o]=p):o==i&&(f[i]=r[u].value))}if(n._editForm=$("#"+n._id+"EditForm"),f[i]=isNaN(f[i])?t:n.model.showColumnWhenEmpty&&n._currentJsonData.length==0?t=parseInt(f[i]):$.type(n._currentJsonData[0][i])=="number"?t=parseInt(f[i]):t=f[i],s={data:f,requestType:"save"},n._trigger("actionBegin",s))return!0;$(n._editForm).hasClass("e-formdestroy")?n.refresh(!0):ej.isNullOrUndefined(h.attr("data-id"))?(s.action="add",s.primaryKeyValue=t,n._cAddedRecord=f):(s.action="edit",s.primaryKeyValue=t,n._cModifiedData=f);s.primaryKey=i;n._saveArgs=s;n.updateCard(t,f);n.model.showColumnWhenEmpty&&n.model.initiallyEmptyDataSource&&(n._kbnSwimLaneData.push(f[n.model.fields.swimlaneKey]),n.KanbanContext._kanbanSubMenu());n.model.editSettings.editMode=="dialog"||n.model.editSettings.editMode=="dialogtemplate"?(h.removeAttr("data-id"),$("#"+n._id+"_dialogEdit").ejDialog("close")):(n.model.editSettings.editMode=="externalform"||n.model.editSettings.editMode=="externalformtemplate")&&(n.model.editSettings.formPosition=="right"&&$(n.element).children().css("width","100%"),h.removeAttr("data-id"),$("#"+n._id+"_externalEdit").css("display","none"))}n.element.hasClass("e-responsive")&&(n.kanbanWindowResize(),n.element.find(".e-kbnadapt-editdlg").length==0&&$("#"+n._id+"_dialogEdit_wrapper").appendTo(n.element))},n.prototype.initValidator=function(){for(var n=this.kanbanObj,i=n.element.find(".kanbanform"),t=0;t<i.length;t++)i.eq(t).validate({ignore:".e-hide",errorClass:"e-field-validation-error",errorElement:"div",wrapper:"div",errorPlacement:function(t,i){i.is(":hidden")&&(i=i.siblings("input:visible"));var u=i.closest("td"),r=$(t).addClass("e-error"),f=ej.buildTag("div.e-errortail e-toparrow");n.model.editSettings.editMode!="dialog"&&u.find(".e-error").remove();i.parent().hasClass("e-in-wrap")?r.insertAfter(i.closest(".e-widget")):r.insertAfter(i);r.prepend(f);n.model.enableRTL?n.model.editSettings.editMode!="dialog"&&r.offset({left:i.offset().left,top:i.offset().top+i.height()}):n.model.editSettings.editMode!="dialog"&&r.offset({left:i.offset().left,top:i.offset().top+i.height()});r.fadeIn("slow")}})},n.prototype.setValidation=function(){for(var t=this.kanbanObj,n=0;n<t.model.editSettings.editItems.length;n++)ej.isNullOrUndefined(t.model.editSettings.editItems[n].validationRules)||this.setValidationToField(t.model.editSettings.editItems[n].field,t.model.editSettings.editItems[n].validationRules)},n.prototype.setValidationToField=function(n,t){var i=n,u,r,f=this.kanbanObj,e,o;ej.isNullOrUndefined(n)||(i=i.replace(/[^a-z0-9\s_]/gi,""));e=$("#"+f._id+"EditForm");u=e.find("#"+f._id+"_"+i).length>0?e.find("#"+f._id+"_"+i):e.find("#"+i);u.attr("name")||u.attr("name",n);u.rules("add",t);o=$("#"+f._id+"EditForm").validate();o.settings.messages[n]=o.settings.messages[n]||{};ej.isNullOrUndefined(t.required)||(r=ej.isNullOrUndefined(t.messages&&t.messages.required)?$.validator.messages.required:t.messages.required,r.indexOf("This field")==0&&(r=r.replace("This field",n)),o.settings.messages[n].required=r)},n.prototype.editFormValidate=function(){return $.isFunction($.validator)?$("#"+this.kanbanObj._id+"EditForm").validate().form():!0},n.prototype._addDialogEditingTemplate=function(){var n=this.kanbanObj,l=ej.buildTag("div"),f,p,o,w,r,b,k,s,d,a,h,i,t,c,tt,g,it,e,v,y,rt,u,nt;if(n.model.columns.length!=0){if(f=ej.buildTag("form.kanbanform","",{},{id:n._id+"EditForm"}),p=ej.buildTag("table","",{}),n.model.editSettings.editMode=="dialog"||n.model.editSettings.editMode=="externalform")for(h=0;h<n.model.editSettings.editItems.length;h++){i=n.model.editSettings.editItems[h];t=i.field;n.model.editSettings.editMode=="dialog"?(d="tr",a="td"):(d="div.e-externalrow",a="div");o=ej.buildTag(d);w=ej.buildTag(a,"",{}).addClass("e-label");r=ej.buildTag(a,"",{"text-align":"left"}).addClass("e-rowcell");h==n.model.editSettings.editItems.length-1&&r.addClass("e-last-rowcell");o.append(w.get(0)).append(r.get(0));w.append("<label style='text-transform:capitalize' for='"+t+"'>"+t+"<\/label>");n.model.editSettings.editMode=="externalform"&&n.model.editSettings.formPosition=="right"&&o.css({width:"300px","padding-right":"0px"});ej.isNullOrUndefined(i.editType)&&(i.editType="stringedit");ej.isNullOrUndefined(t)||(t=t.replace(/[^a-z0-9\s_]/gi,""));switch(i.editType){case"stringedit":n.model.fields.primaryKey!=t||n.model.showColumnWhenEmpty&&n._currentJsonData.length==0?r.html(ej.buildTag("input.e-field e-ejinputtext","",{},{value:"{{html:#data['"+t+"']}}",id:n._id+"_"+t,name:t})):r.html(ej.buildTag("input.e-field e-ejinputtext e-disable","",{},{value:"{{html:#data['"+t+"']}}",id:n._id+"_"+t,name:t,disabled:"disabled"}));break;case"numericedit":r.html(ej.buildTag("input.e-numerictextbox e-field","",{},{type:"text",value:"{{:#data['"+t+"']}}",id:n._id+"_"+t,name:t}));break;case"dropdownedit":if(c=[],ej.isNullOrUndefined(i.dataSource))if(!ej.isNullOrUndefined(n.model.fields.swimlaneKey)&&i.field==n.model.fields.swimlaneKey&&n.model.swimlaneSettings.headers.length>0)for(e=n._slKey,u=0;u<e.length;u++)c.push({text:n._slText[u],value:e[u]});else for(y=n._keyFiltering?(new ej.Query).where(ej.Predicate.or(n.keyPredicates)).select(i.field):(new ej.Query).select(i.field),ej.isNullOrUndefined(this._dropDownManager)||!n._dataManager.dataSource.offline&&n._dataManager.dataSource.json.length?e=n._dataManager.executeLocal(y):(n._dataManager.adaptor instanceof ej.JsonAdaptor&&i.field.indexOf(".")!=-1&&(rt=i.field.replace(/\./g,ej.pvt.consts.complexPropertyMerge),y=(new ej.Query).select(rt)),e=this._dropDownManager.executeLocal(y)),v=ej.dataUtil.mergeSort(ej.dataUtil.distinct(e)),u=0;u<v.length;u++)c.push({text:v[u],value:v[u]});else c=i.dataSource;g=ej.buildTag("select");it=ej.buildTag("option","{{:text}}",{},{value:"{{html:#data['value']}}"});g.append(it);tt=$.templates(g.html());r.get(0).innerHTML=ej.isNullOrUndefined(i.editParams)||ej.isNullOrUndefined(i.dataSource)?["<select>",tt.render(c),"<\/select>"].join(""):"<input>";r.find("select,input").prop({id:n._id+"_"+t,name:t}).addClass("e-field e-dropdownlist");break;case"rteedit":r.html(ej.buildTag("textarea.e-field e-rte","{{html:#data['"+t+"']}}",{},{id:n._id+"_"+t,name:t}));break;case"textarea":r.html(ej.buildTag("textarea.e-field e-kanbantextarea e-ejinputtext","{{html:#data['"+t+"']}}",{},{id:n._id+"_"+t,name:t}));break;case"datepicker":case"datetimepicker":r.html(ej.buildTag("input.e-"+i.editType+" e-field","",{},{type:"text",value:"{{:#data['"+t+"']}}",id:n._id+"_"+t,name:t}))}n.model.editSettings.editMode=="dialog"?(f.append(p),p.append(o)):f.append(o);f.appendTo(l)}else n.model.editSettings.editMode=="dialogtemplate"&&n.model.editSettings.dialogTemplate!=null&&(nt=n.model.editSettings.dialogTemplate),n.model.editSettings.editMode=="externalformtemplate"&&n.model.editSettings.externalFormTemplate!=null&&(nt=n.model.editSettings.externalFormTemplate),f.html($(nt).html()),f.appendTo(l);b=ej.buildTag("input.e-save","",{},{type:"button",id:n._id+"_Save"});b.ejButton({text:n.localizedLabels.SaveButton});k=n.model.editSettings.formPosition!="right"?ej.buildTag("input.e-cancel","",{},{type:"button",id:n._id+"_Cancel"}):ej.buildTag("input.e-cancel","",{},{type:"button",id:n._id+"_Cancel"});k.ejButton({text:n.localizedLabels.CancelButton});s=n.model.editSettings.editMode!="dialog"&&n.model.editSettings.editMode!="dialogtemplate"?ej.buildTag("div","","",{"class":"e-editform-btn"}):ej.buildTag("div#"+n._id+"_EditBtnDiv","",{},{"class":"e-kanban-editdiv"});s.append(b);s.append(k);n.model.editSettings.editMode!="dialog"&&n.model.editSettings.editMode!="dialogtemplate"?s.appendTo(l):f.append(s);$.templates(n._id+"_dialogEditingTemplate",l.html())}},n}();window.ej.createObject("ej.KanbanFeatures.Edit",f,window);e=function(){function n(n){this.kanbanObj=null;this.kanbanObj=n}return n.prototype._renderContext=function(){var n=this.kanbanObj,u,l,f,i,r,t,o,y,a,e,p,c,s,h,v;for(u=n.model.contextMenuSettings.menuItems,e=ej.buildTag("ul","",{},{id:n._id+"_Context"}),p=n.model.contextMenuSettings.disableDefaultItems,h=0;h<u.length;h++)(u[h]==""||u[h]==" ")&&u.splice(h,1);for(t=0;t<u.length;t++)i=u[t],$.inArray(i,p)==-1&&(r=this._items(i,"menuItem")),e.append(r);for(c=n.model.contextMenuSettings.customMenuItems,o=0;o<c.length;o++)y=c[o].text,a=this._items(y,"custom"),c[o].template&&a.append($(c[o].template)),e.append(a);for(s=ej.buildTag("ul","",{},{id:n._id+"_SubContext_VisibleColumns"}),t=0;t<n.model.columns.length;t++)i=n.model.columns[t].headerText,l=ej.buildTag("input","",{},{type:"checkbox",name:i}),r=this._items(i,"subMenuItem"),r.find("span").append(l),r.find("span").addClass("e-checkbox e-visiblecolumns"),s.append(r),l.ejCheckBox({checked:n.model.columns[t].visible});if($(e).find("li.e-column.e-visiblecolumn").append(s),n.model.fields.swimlaneKey){for(s=ej.buildTag("ul","",{},{id:n._id+"_SubContext_Swimlane"}),this.kanbanObj.model.swimlaneSettings.headers.length>0?f=n._slText:(v=(new ej.Query).select([n.model.fields.swimlaneKey]),f=n._dataManager.dataSource.offline?n._dataManager.executeLocal(v):n._contextSwimlane.executeLocal(v),f=ej.dataUtil.mergeSort(ej.dataUtil.distinct(f))),f.length==0&&(n.model.initiallyEmptyDataSource=!0),t=0;t<f.length;t++)i=f[t],r=this._items(i,"subMenuItem"),s.append(r);$(e).find("li.e-move.e-swimlane").append(s)}$(e).ejMenu({menuType:ej.MenuType.ContextMenu,openOnClick:!1,contextMenuTarget:"#"+n._id,click:$.proxy(n._clickevent,n),width:"auto",cssClass:"e-kanban-context",beforeOpen:$.proxy(n._menu,n),open:$.proxy(this._contextopen,n)});n._conmenu=e.data("ejMenu")},n.prototype._contextopen=function(){var n=this._conmenu.element;n.length>0&&(n.find("li").is(":visible")||n.css("visibility","hidden"))},n.prototype._kanbanContextClick=function(n,t){var u,i,r,s,e,o,c,f,l,h,v,a;if(f=n.parentText!=null?n.parentText:n.events.text,u=$(t._contexttarget),n.targetelement=t._contexttarget,i=u.closest(".e-kanbancard"),r=i.length,i.length>0&&(n.card=i,n.index=i.parent(".e-rowcell").find(".e-kanbancard").index(i),n.cellIndex=i.parent(".e-rowcell").index(),n.rowIndex=t.getIndexByRow(i.parents(".e-columnrow")),n.cardData=t.KanbanCommon._getKanbanCardData(t._currentJsonData,i.attr("id"))[0]),!t._trigger("contextClick",n)){switch(f){case t.localizedLabels.AddCard:t._isAddNewClick=!0;t._newCard=$(t._contexttarget);t.KanbanEdit&&t.KanbanEdit.addCard();break;case t.localizedLabels.EditCard:r>0&&!ej.isNullOrUndefined(t.model.fields.primaryKey)&&t.KanbanEdit&&t.KanbanEdit.startEdit(i);break;case t.localizedLabels.HideColumn:u.closest(".e-headercell").find(".e-headercelldiv").length>0&&t.hideColumns($.trim(u.closest(".e-headercell").find(".e-headercelldiv .e-headerdiv").text()).split("[")[0]);break;case t.localizedLabels.DeleteCard:!ej.isNullOrUndefined(t.model.fields.primaryKey)&&i.length>0&&t.KanbanEdit&&(t.KanbanEdit.deleteCard(i.attr("id")),t.KanbanCommon._showhide(t._hiddenColumns,"hide"),t.model.enableTotalCount&&t.KanbanCommon._totalCount());break;case t.localizedLabels.MoveRight:r>0&&t.KanbanDragAndDrop&&(t._bulkUpdateData=[],t.KanbanDragAndDrop._dropToColumn($(t._contexttarget).closest("td.e-rowcell").next(),i),t.model.enableTotalCount&&t.KanbanCommon._totalCount());break;case t.localizedLabels.MoveLeft:r>0&&t.KanbanDragAndDrop&&(t._bulkUpdateData=[],t.KanbanDragAndDrop._dropToColumn($(t._contexttarget).closest("td.e-rowcell").prev(),i),t.model.enableTotalCount&&t.KanbanCommon._totalCount());break;case t.localizedLabels.MoveUp:r>0&&t.KanbanDragAndDrop&&(t._bulkUpdateData=[],t.KanbanDragAndDrop._dropAsSibling(i.prev(),i,!1));break;case t.localizedLabels.MoveDown:r>0&&t.KanbanDragAndDrop&&(t._bulkUpdateData=[],t.KanbanDragAndDrop._dropAsSibling(i.next(),i,!0));break;case t.localizedLabels.TopofRow:r>0&&t.KanbanDragAndDrop&&(t._bulkUpdateData=[],t.KanbanDragAndDrop._dropAsSibling(u.closest("td.e-rowcell").find(".e-kanbancard").first(),i,!1));break;case t.localizedLabels.BottomofRow:r>0&&t.KanbanDragAndDrop&&(t._bulkUpdateData=[],t.KanbanDragAndDrop._dropAsSibling(u.closest("td.e-rowcell").find(".e-kanbancard").last(),i,!0));break;case t.localizedLabels.MovetoSwimlane:f!=n.text&&r>0&&(s=u.closest(".e-kanbancard").attr("id"),t._hiddenColumns=[],e=new ej.DataManager(t._currentJsonData).executeLocal((new ej.Query).where(t.model.fields.primaryKey,ej.FilterOperators.equal,s))[0],o=t.model.fields.swimlaneKey,!ej.isNullOrUndefined(o)&&t.model.swimlaneSettings.headers.length>0?(c=new ej.DataManager(t.currentViewData).executeLocal((new ej.Query).where("slHeader",ej.FilterOperators.equal,n.text))[0],e[o]=c.key):e[o]=n.text,f={data:e,requestType:"save",primaryKeyValue:e[t.model.fields.primaryKey]},t._saveArgs=f,t.updateCard(s,e),t.KanbanCommon._showhide(t._hiddenColumns,"hide"),t._dataManager.dataSource.offline&&(t._saveArgs=null));break;case t.localizedLabels.PrintCard:r>0&&t.print(i);break;case t.localizedLabels.VisibleColumns:f!=n.text&&(l=$(n.element.parentElement).find("li").index(n.element),t.model.columns[l].visible?(t.hideColumns(n.text),$($(n.element).find("input.e-checkbox")).ejCheckBox({checked:!1})):(t.showColumns(n.text),$($(n.element).find("input.e-checkbox")).ejCheckBox({checked:!0})))}t.model.isResponsive&&t.kanbanWindowResize();t.model.allowScrolling&&!t.element.hasClass("e-responsive")&&t.kanbanContent.data("ejScroller").refresh();t.element.hasClass("e-responsive")&&!ej.isNullOrUndefined(t.model.fields.swimlaneKey)&&t._kbnAdaptDdlIndex>0&&(h=t.kanbanContent.data("ejScroller"),a=t._freezeScrollTop,v=t.element[0].getElementsByClassName("e-columnrow"),h.scrollY(0,!0),t._freezeScrollTop=a,h.scrollY(t._freezeScrollTop,!0))}},n.prototype._kanbanSubMenu=function(){var n=this.kanbanObj,t;if(n.model.fields.swimlaneKey){for(ul=n._conmenu.element.get(0),subul=n._conmenu.element.find("#"+n._id+"_SubContext_Swimlane").get(0),$(subul).find("li").remove(),data=ej.dataUtil.mergeSort(ej.dataUtil.distinct(n._kbnSwimLaneData)),i=0;i<data.length;i++)t=data[i],menu=this._items(t,"subMenuItem").get(0),$(subul).append(menu);$(ul).find("li.e-move.e-swimlane").append(subul)}},n.prototype._kanbanMenu=function(n,t){var i,f,e,w,v,s,b,h,u,o,r,y,c,a,p,l;if(i=t._conmenu.element,t._contexttarget=n.target,f=$(n.target),e=$(t._contexttarget).closest("td.e-rowcell"),w=$(t._contexttarget).closest("tr.e-columnrow"),v=f.closest(".e-rowcell"),s=$(f).closest(".e-kanbancard"),a=t.KanbanCommon._getKanbanCardData(t._currentJsonData,s.attr("id"))[0],b=t.model.contextMenuSettings.menuItems,i.css("visibility","visible"),i.find("li").hide(),f.closest(".e-kanban").attr("id")!==t._id){i.css("visibility","hidden");return}if(i.find(".e-customitem").length>0)for(h=t.model.contextMenuSettings.customMenuItems,u=i.find(".e-customitem"),r=0;r<u.length;r++)if(h[r].target||(h[r].target="all"),h[r].target&&h[r].text==i.find(".e-customitem").children("a").eq(r).text())switch(h[r].target){case"content":t.getContentTable().find(f).length>0?($(u[r]).show(),$(u[r]).find("li").length>0&&$(u[r]).find("li").show()):$(u[r]).hide();break;case"header":t.getHeaderContent().find(f).length>0?($(u[r]).show(),$(u[r]).find("li").length>0&&$(u[r]).find("li").show()):$(u[r]).hide();break;case"card":t.element.find(".e-kanbancard").find(f).length>0?($(u[r]).show(),$(u[r]).find("li").length>0&&$(u[r]).find("li").show()):$(u[r]).hide();break;case"all":$(u[r]).show();$(u[r]).find("li").length>0&&$(u[r]).find("li").show()}if(t.getContentTable().find(f).length>0){if(v.length==0||f.filter(".e-targetclone,.e-targetdragclone").length>0){i.css("visibility","hidden");return}if($(f).closest(".e-swimlanerow").length>0){i.css("visibility","hidden");return}if(s.length>0){if(p=$(f).closest("td.e-rowcell").find(".e-kanbancard"),c=p.index(s),y=p.length,$(i.find("li").not(".e-customitem")).hide(),$(i.find(".e-customitem li")).show(),!ej.isNullOrUndefined(t.model.fields.primaryKey)&&t.model.editSettings.allowEditing&&i.find(".e-content.e-cardedit").show(),e.hasClass("e-drag")&&(c>1&&i.find(".e-move.e-up").show(),c!=y-1&&e.hasClass("e-drop")&&i.find(".e-row.e-bottom").show(),c!=0&&e.hasClass("e-drop")&&i.find(".e-row.e-top").show(),y-1-c>1&&i.find(".e-move.e-down").show(),e.next().length>0&&e.next().hasClass("e-drop")&&!t._checkMultikey(e.next())&&t.KanbanCommon._preventCardMove(a[t.model.keyField],t.model.columns[e.next().index()].key)&&i.find(".e-move.e-right").show(),e.prev().length>0&&e.prev().hasClass("e-drop")&&!t._checkMultikey(e.prev())&&t.KanbanCommon._preventCardMove(a[t.model.keyField],t.model.columns[e.prev().index()].key)&&i.find(".e-move.e-left").show(),t.model.fields.swimlaneKey&&t.model.fields.primaryKey&&i.find(".e-move.e-swimlane").length>0))if(i.find(".e-move.e-swimlane").show(),i.find(".e-move.e-swimlane").find("li").show(),t.model.showColumnWhenEmpty&&t.model.initiallyEmptyDataSource)for(data=ej.dataUtil.mergeSort(ej.dataUtil.distinct(t._kbnSwimLaneData)),o=0;o<data.length;o++)data[o]==a[t.model.fields.swimlaneKey]&&i.find(".e-move.e-swimlane").find("li").eq(o).hide();else i.find(".e-move.e-swimlane").find("li").eq(t._columnRows.index(w)).hide();ej.isNullOrUndefined(t.model.fields.primaryKey)||i.find(".e-content.delete").show();t.model.allowPrinting&&i.find(".e-print").show()}else v.length>0&&($(i.find("li").not(".e-customitem")).hide(),$(i.find(".e-customitem li")).show(),t.model.editSettings.allowAdding&&i.find(".e-add").show())}else if(t.getHeaderContent().find(f).length>0)if(l=i.find(".e-column"),$(f).closest(".e-headercell").not(".e-stackedHeaderCell").length>0){for($(i.find("li").not(".e-customitem")).hide(),$(i.find(".e-customitem li")).show(),$(i.find("li.e-haschild").find("li")).show(),l.show(),o=0;o<i.find(".e-column.e-visiblecolumn").find("li").length;o++)t.element.find(".e-headercell").not(".e-stackedHeaderCell").eq(o).hasClass("e-hide")?l.find("li input.e-checkbox").eq(o).ejCheckBox({checked:!1}):l.find("li input.e-checkbox").eq(o).ejCheckBox({checked:!0});l.find("li").show()}else{i.css("visibility","hidden");return}else{i.css("visibility","hidden");return}t.model.contextOpen&&(n.card=s,n.index=s.parent(".e-rowcell").find(".e-kanbancard").index(s),n.cellIndex=s.parent(".e-rowcell").index(),n.rowIndex=t.getIndexByRow(s.parents(".e-columnrow")),n.cardData=t.KanbanCommon._getKanbanCardData(t._currentJsonData,s.attr("id"))[0],t._trigger("contextOpen",n))},n.prototype._items=function(n,t){var i,r,u,f;return n==""||ej.isNullOrUndefined(n)?!1:(t=="menuItem"?n.indexOf("Card")!=-1?(i=ej.buildTag("li","",{},{"class":"e-content"}),i.css("display","none"),n.indexOf("Add")!=-1&&(i.addClass("e-add"),r=this.kanbanObj.localizedLabels.AddCard),n.indexOf("Edit")!=-1&&(i.addClass("e-cardedit"),r=this.kanbanObj.localizedLabels.EditCard),n.indexOf("Delete")!=-1&&(i.addClass("delete"),r=this.kanbanObj.localizedLabels.DeleteCard),n.indexOf("Print")!=-1&&(i.addClass("e-print"),r=this.kanbanObj.localizedLabels.PrintCard),i.css("display","none")):n.indexOf("Column")!=-1||n.indexOf("Columns")!=-1?(i=ej.buildTag("li","",{},{"class":"e-column"}),n.indexOf("Hide")!=-1&&(i.addClass("e-hidecolumn"),r=this.kanbanObj.localizedLabels.HideColumn),n.indexOf("Visible")!=-1&&(i.addClass("e-visiblecolumn"),r=this.kanbanObj.localizedLabels.VisibleColumns),i.css("display","none")):n.indexOf("Row")!=-1?(i=ej.buildTag("li","",{},{"class":"e-row"}),n.indexOf("Top")!=-1?(i.addClass("e-top"),r=this.kanbanObj.localizedLabels.TopofRow):(i.addClass("e-bottom"),r=this.kanbanObj.localizedLabels.BottomofRow),i.css("display","none")):n.indexOf("Move")!=-1&&(i=ej.buildTag("li","",{},{"class":"e-move"}),n.indexOf("Left")!=-1?(i.addClass("e-left"),r=this.kanbanObj.localizedLabels.MoveLeft):n.indexOf("Right")!=-1?(i.addClass("e-right"),r=this.kanbanObj.localizedLabels.MoveRight):n.indexOf("Up")!=-1?(i.addClass("e-up"),r=this.kanbanObj.localizedLabels.MoveUp):n.indexOf("Down")!=-1?(i.addClass("e-down"),r=this.kanbanObj.localizedLabels.MoveDown):n.indexOf("Swimlane")!=-1&&(i.addClass("e-swimlane"),r=this.kanbanObj.localizedLabels.MovetoSwimlane),i.css("display","none")):t=="subMenuItem"?(i=ej.buildTag("li","",{},{}),this.kanbanObj.model.showColumnWhenEmpty&&this.kanbanObj.model.initiallyEmptyDataSource&&($(i).addClass("e-list"),$(i).attr("role","menuitem"))):t=="custom"&&(i=ej.buildTag("li","",{},{"class":"e-customitem"}),i.css("display","block")),u=document.createElement("a"),f="",f=typeof n=="string"?n.indexOf("Move to")!=-1?n.split(" ")[2].toLowerCase():n.indexOf("Move")!=-1?n.split(" ")[1].toLowerCase():n.split(" ")[0].toLowerCase():n,ej.isNullOrUndefined(r)?$(u).html(n):$(u).html(r),this.kanbanObj.model.showColumnWhenEmpty&&this.kanbanObj.model.initiallyEmptyDataSource&&$(u).addClass("e-menulink"),$(u).append(ej.buildTag("span","",{},{"class":"e-kanbancontext e-icon e-context"+f})),i.append(u),i)},n.prototype._kbnBrowserContextMenu=function(n){this._kbnBrowserContext=="contextmenu"&&this.model.enableTouch&&n.preventDefault();this._kbnBrowserContext="null"},n}();window.ej.createObject("ej.KanbanFeatures.Context",e,window);o=function(){function n(n){this.kanbanObj=null;this._removeFreezeRow=function(n){var n=this.kanbanObj;(n.model.scrollSettings.allowFreezeSwimlane||$.isEmptyObject(n._freezeSwimlaneRow))&&(!n.element.hasClass("e-responsive")||ej.isNullOrUndefined(n.model.fields.swimlaneKey)||!n.model.scrollSettings.allowFreezeSwimlane)||this._removeFreezeslRow()};this._removeFreezeslRow=function(n){var n=this.kanbanObj,t;n._freezeSwimlaneRow.remove();n.headerContent.css({position:"",top:""});n.kanbanContent.css({position:"",top:""});t=n.element.find(".e-kanbantoolbar");t.length>0&&t.css({position:"",top:""})};this.kanbanObj=n}return n.prototype.expandAll=function(){var t=this.kanbanObj.element.find(".e-swimlanerow .e-slcollapse"),n;if(t.length!=0)for(n=0;n<t.length;n++)this.toggle($(t[n]))},n.prototype.collapseAll=function(){var t=this.kanbanObj.element.find(".e-swimlanerow .e-slexpand"),n;if(t.length!=0)for(n=0;n<t.length;n++)this.toggle($(t[n]))},n.prototype.toggle=function(n){var i,r,t=this.kanbanObj,u;if(typeof n=="string"||typeof n=="number")i=t.KanbanCommon._removeIdSymbols(n),r=t.element.find('tr[id="'+i+'"]'),this._toggleSwimlaneRow($(r).find(".e-rowcell .e-slexpandcollapse"));else if(typeof n=="object"&&n[0].nodeName!="DIV")for(u=0;u<n.length;u++)i=t.KanbanCommon._removeIdSymbols(n[u]),r=t.element.find('tr[id="'+i+'"]'),this._toggleSwimlaneRow($(r).find(".e-rowcell .e-slexpandcollapse"));else this._toggleSwimlaneRow(n);t.model.allowScrolling&&t.KanbanScroll._refreshSwimlaneToggleScroller()},n.prototype._toggleSwimlaneRow=function(n){var t=this.kanbanObj,h,a=t._swimlaneRows.index(n.parents(".e-swimlanerow")),e,v=t.currentViewData[a],p=n.parent().next(".e-slkey").html(),i,r,u,y,c,o,s,f,l;if(n=n.hasClass("e-slexpandcollapse")?n.find("div:first"):n,n.hasClass("e-slexpand")||n.hasClass("e-slcollapse")){if(i=n.closest("tr"),i.next(".e-collapsedrow").remove(),r=i.next(),u=t.KanbanCommon._removeIdSymbols(n.parent().next(".e-slkey").html()),e=n.hasClass("e-slexpand")?"collapse":"expand",i.find(".e-slexpandcollapse .e-icon").attr("aria-label",u+" "+e),i.find(".e-slexpandcollapse .e-icon").attr("tab-index","0"),y=e=="expand"?!0:!1,i.attr("aria-expanded",y),h={target:n,swimlaneRow:i,data:v.items,cards:r.find(".e-kanbancard"),id:p,key:v.key,rowIndex:a,action:e},t._trigger("swimlaneClick",h),h.cancel)return!1;if(n.hasClass("e-slexpand")){for(r.hide(),c=ej.buildTag("tr","",{},{"class":"e-collapsedrow"}),o=t.model.columns,f=0;f<o.length;f++)s=ej.buildTag("td","",{},{"class":"e-rowcell","data-role":"kanbancell"}),o[f].visible||s.addClass("e-hide"),o[f].isCollapsed&&s.addClass("e-shrink"),c.append(s);r.before(c);n.removeClass("e-slexpand").addClass("e-slcollapse");$.inArray(u,t._collapsedSwimlane)==-1&&t._collapsedSwimlane.push(u)}else r.show(),n.removeClass("e-collapsedrow").removeClass("e-slcollapse").addClass("e-slexpand"),l=$.inArray(u,t._collapsedSwimlane),l!=-1&&t._collapsedSwimlane.splice(l,1)}},n.prototype._freezeRow=function(n,t){var c=t.model.fields.swimlaneKey,k=t.element.find(".e-kanbantoolbar"),o,p,l,a,w,d;if(t._freezeSwimlaneRow=t.element.find(".e-freezeswimlanerow"),this._removeFreezeRow(),(!t.element.hasClass("e-responsive")||!ej.isNullOrUndefined(c))&&(t.element.hasClass("e-responsive")||t.model.scrollSettings.allowFreezeSwimlane&&!ej.isNullOrUndefined(c)&&(ej.isNullOrUndefined(n.scrollLeft)||t._freezeSwimlaneRow.length!=0))){if(t._freezeSwimlaneRow.length<=0&&t.model.scrollSettings.allowFreezeSwimlane&&!t.element.hasClass("e-responsive"))t._freezeSwimlaneRow=ej.buildTag("div.e-freezeswimlanerow e-swimlanerow","<div><\/div>",{},{}),o=t.headerContent.offset().top+t.headerContent.height()-(t.element.offset().top+1),d=t.element.height(),t._freezeSwimlaneRow.prependTo(t.element).css({top:o}),o=t._freezeSwimlaneRow.height(),t.headerContent.css({position:"relative",top:-o}),t.kanbanContent.css({position:"relative",top:-o}),k.length>0&&k.css({position:"relative",top:-o}),t.element.height(d),p=t._swimlaneRows.eq(0),t._freezeSwimlaneRow.children().append(p.find(".e-slkey,.e-slcount").clone()),t._freezeSwimlaneRow.width(t.headerContent.width()-1).height(p.height()),t._freezeSlOrder=0,l=ej.buildTag("table.e-table e-freeze-table","",{}),l.append(t.getContentTable().find("colgroup").clone()),a=ej.buildTag("tbody","",{},{}),l.append(a),a.append(t.getContentTable().find(".e-columnrow").eq(0).clone().removeClass("e-columnrow").addClass("e-collapsedrow")),w=a.find("td"),w.removeClass("e-droppable").removeAttr("data-ej-mappingkey").height(0),w.children().remove(),t._freezeSwimlaneRow.append(l);else if(t._freezeSwimlaneRow.length>0||!ej.isNullOrUndefined(c)&&t.element.hasClass("e-responsive")){var i,r=0,h=0,b=0,v,y=t.element.find(".e-swimlane-text"),u,s,e,f;!ej.isNullOrUndefined(c)&&t.element.hasClass("e-responsive")?(v=t.element.find(".e-columnrow"),i=t.getContent().offset().top,s=v.eq(t._freezeSlOrder),u=v.eq(t._freezeSlOrder+1),e=v.eq(t._freezeSlOrder-1),u.length>0&&(r=u.offset().top),s.length>0&&(h=s.offset().top),e.length>0&&(b=e.offset().top)):t._freezeSwimlaneRow.length>0&&(i=t.getContent().offset().top+t._freezeSwimlaneRow.height(),s=t._swimlaneRows.eq(t._freezeSlOrder),u=t._swimlaneRows.eq(t._freezeSlOrder+1),e=t._swimlaneRows.eq(t._freezeSlOrder-1),u.length>0&&(r=u.offset().top+u.height()),h=s.offset().top+s.height(),b=e.offset().top+e.height());f=t.element.find(".e-freezeswimlanerow >div");t._freezeScrollTop>n.scrollTop&&(h=h,i>=r&&(r=i+1));t._freezeScrollTop<n.scrollTop&&(r=r);(n.source=="wheel"||n.source=="button")&&(t._freezeScrollTop<n.scrollTop?r=r-n.model.scrollOneStepBy:t._freezeScrollTop>n.scrollTop&&(i=i-n.model.scrollOneStepBy));i>=r&&t._freezeSlOrder<t._swimlaneRows.length-1?(f.length>0&&(f.children().remove(),f.append(u.find(".e-slkey,.e-slcount").clone())),y.length>0&&(y.text(t._kbnAdaptDdlData[t._kbnAdaptDdlIndex+1]),++t._kbnAdaptDdlIndex),++t._freezeSlOrder):i<h&&i>b&&t._freezeSlOrder>0&&(f.length>0&&(f.children().remove(),f.append(e.find(".e-slkey,.e-slcount").clone())),y.length>0&&(y.text(t._kbnAdaptDdlData[t._kbnAdaptDdlIndex-1]),--t._kbnAdaptDdlIndex),--t._freezeSlOrder);n.scrollTop==0&&this._removeFreezeslRow();ej.isNullOrUndefined(n.scrollLeft)||(f.css({left:-n.scrollLeft}),t._freezeSwimlaneRow.find("table").css({left:-n.scrollLeft}))}t._freezeScrollTop=n.scrollTop}},n.prototype._swimlaneLimit=function(){for(var i,s,e,h,o,c,r,a,t,n=this.kanbanObj,u=0;u<n.model.columns.length;u++)if(i=n.model.columns[u].constraints,s=n.KanbanCommon._multikeySeparation(n.model.columns[u].key),!ej.isNullOrUndefined(i)){var l=!ej.isNullOrUndefined(i.min),v=!ej.isNullOrUndefined(i.max),f=$($(n.getContent().find(".e-columnrow")).find('td[data-ej-mappingkey="'+s+'"]'));if(i.type=="swimlane"&&!ej.isNullOrUndefined(n.model.fields.swimlaneKey))for(r=0;r<f.length;r++)$(f).eq(r).find(".e-limits").length==0&&(a=ej.buildTag("div.e-limits"),$(f).eq(r).prepend(a)),t=$(f).eq(r).find(".e-limits"),l&&(e=ej.buildTag("div.e-min",n.localizedLabels.Min,"",{}),h=ej.buildTag("span.e-minlimit"," "+i.min.toString(),"",{}),e.append(h),t.find(".e-min").length>0&&t.find(".e-min").remove(),t.append(e)),v&&(o=ej.buildTag("div.e-max",n.localizedLabels.Max,"",{}),c=ej.buildTag("span.e-maxlimit"," "+i.max.toString(),"",{}),o.append(c),t.find(".e-max").length>0&&t.find(".e-max").remove(),l&&t.append("/"),t.append(o)),n.getHeaderContent().find(".e-headercell").eq(u).hasClass("e-shrinkcol")&&$(f).eq(r).find(".e-limits").addClass("e-hide")}},n}();window.ej.createObject("ej.KanbanFeatures.Swimlane",o,window);s=function(){function n(n){this.kanbanObj=null;this._columnAutoWidth=function(n,t,i){var r=this.kanbanObj,v=r.getContentTable().children("colgroup").find("col"),y=r.getHeaderTable().children("colgroup").find("col"),o,s,h,c,u,l,a,f,e;o=r.element.find(".e-kanbantoolbar");f=r.model.scrollSettings.height;a=typeof f=="string"&&f.indexOf("%")!=-1?parseFloat(r._originalScrollHeight)/100*r.element.height():f;s=(f==0?r.element.height():a)-(o.length>0?o.outerHeight():0);h=r.getContentTable().height()+r.getHeaderTable().height();parseInt(s)<parseInt(h)?i=i+18:parseInt(s)<=parseInt(h)&&r.initialRender&&(i=i+18);c=t-i;e=r._expandedColumns.length-r._hiddenColumns.length;u=parseInt((c/e).toFixed(2).split(".")[0]);l=Math.ceil(parseFloat("0."+(c/e).toFixed(2).split(".")[1])*e);r.model.columns[n].isCollapsed||(r._extraWidth<l&&(u=u+1,r._extraWidth++),v.eq(n).css("width",u+"px"),y.eq(n).css("width",u+"px"),r.model.columns[n].width=u,r._columnsWidthCollection[n]=u)};this.kanbanObj=n}return n.prototype._updateGroup=function(n,t){var i=this.kanbanObj;ej.isNullOrUndefined(i._saveArgs)&&new ej.DataManager(i._currentJsonData).update(i.model.fields.primaryKey,t,"");$.type(t)&&!t.count&&this._renderSingleCard(n,t)},n.prototype._removeIdSymbols=function(n){return typeof n=="string"&&(n=n.replace(/[-\s']/g,"_")),n},n.prototype._enableKanbanRTL=function(){this.kanbanObj.model.enableRTL?this.kanbanObj.element.addClass("e-rtl"):this.kanbanObj.element.removeClass("e-rtl")},n.prototype._getEmptyTbody=function(){var n=ej.buildTag("td.e-emptycard",this.kanbanObj.localizedLabels.EmptyCard,{},{colSpan:this.kanbanObj.model.columns.length});return $(document.createElement("tr")).append(n)},n.prototype._renderSingleCard=function(n,t){var i=this.kanbanObj,u=i.element.find("div[id='"+n+"']"),a=i,f,r,e,o,c,s,h,l;i._dropped?(h=$.render[i._id+"_cardTemplate"](t),ej.browserInfo().name=="msie"&&ej.browserInfo().version=="8.0"?$(u).after(h):($(u).replaceWith(h),l=$.inArray(n,i._collapsedCards),l!=-1&&i.toggleCard(n))):(e=a.model.fields.swimlaneKey,ej.isNullOrUndefined(e)?f=i.element.find(".e-columnrow:first"):(o=i.model.swimlaneSettings.unassignedGroup,o.enable&&o.keys.length>0&&(t=i._checkKbnUnassigned(t)),c=this._removeIdSymbols(t[e]),s=i.element.find("tr[id='"+c+"']"),s.length>0&&(f=s.next())),r=$(f).find("td.e-rowcell").eq(i._getColumnKeyIndex(t[i.model.keyField])),$(u).remove(),r.length>0&&($(r).children().hasClass("e-customaddbutton")?$($.render[i._id+"_cardTemplate"](t)).insertBefore($(r).find(".e-customaddbutton")):$(r).append($.render[i._id+"_cardTemplate"](t))));i.KanbanDragAndDrop&&i.KanbanDragAndDrop._addDragableClass();i.KanbanSelection&&i.KanbanSelection._selectionOnRerender()},n.prototype._getKanbanCardData=function(n,t){return new ej.DataManager(n).executeLocal((new ej.Query).where(this.kanbanObj.model.fields.primaryKey,ej.FilterOperators.equal,t))},n.prototype._kbnHeaderAndCellEvents=function(n){var t=this.kanbanObj,i={},l=n.find(".e-kanbancard"),r=n,s=n.find(".e-cardselection"),h,tt=this,f=n.index(),a=n.parents(".e-columnrow"),v,e,u,c;if(v=t.element.find(".e-rowcell.e-droppable"),u=n.attr("data-ej-mappingkey"),r.parents(".e-headercell").length>0&&(r=n.parents(".e-headercell")),r.hasClass("e-headercell")&&(h=f=r.index(),e=v.eq(h),u=e.attr("data-ej-mappingkey"),t.model.fields.swimlaneKey&&(e=$("td[data-ej-mappingkey~='"+u+"']")),l=e.find(".e-kanbancard"),s=e.find(".e-cardselection"),a=e.parents(".e-columnrow")),n.hasClass("e-rowcell")||!r.hasClass("e-stackedHeaderCell")){var it=a.prev(".e-swimlanerow").find(".e-slkey").text(),y=[],o=[];if(ej.isNullOrUndefined(u))o.push(new ej.Predicate(t.model.keyField,ej.FilterOperators.equal,u,!0));else for(u=typeof u=="object"?u:u.split(","),c=0;c<u.length;c++)o.push(new ej.Predicate(t.model.keyField,ej.FilterOperators.equal,u[c],!0));i.cardsInfo=function(){var i={},r;return i.cards=l,i.cardsData=t.model.fields.swimlaneKey&&n.hasClass("e-rowcell")?new ej.DataManager(t._currentJsonData).executeLocal((new ej.Query).where(ej.Predicate.or(o)).where(t.model.fields.swimlaneKey,ej.FilterOperators.equal,it)):new ej.DataManager(t._currentJsonData).executeLocal((new ej.Query).where(ej.Predicate.or(o))),i.cardsCount=l.length,t._selectedCardData.length>0&&(r=$.inArray(f,t.selectedRowCellIndexes[0].cellIndex),!ej.isNullOrUndefined(r)&&r>=0&&y.push(t.selectedRowCellIndexes[0].cardIndex[r])),i.selectedCardsIndexes=y,i.selectedCardsData=new ej.DataManager(tt._selectedCardData).executeLocal((new ej.Query).where(ej.Predicate.or(o))),i.selectedCards=s,i}}if(n.hasClass("e-rowcell")&&(i.rowIndex=t.getIndexByRow(a),i.cellIndex=f,t._trigger("cellClick",i)),r.hasClass("e-stackedHeaderCell")){i.text=n.text();var p=[],s,w=[],b=[],k=[],d,g=[],nt=[];d=t.element.find(".e-stackedHeaderRow");i.data=function(){var i,h=0,e,y=t.model.stackedHeaderRows[d.index(r.parents(".e-stackedHeaderRow"))].stackedHeaderColumns,o,u,c,l,a,n;for(i=y[r.index()].column,i=i.split(","),o=0;o<i.length;o++)u=new ej.DataManager(t.model.columns).executeLocal((new ej.Query).where("headerText",ej.FilterOperators.equal,i[o])),c=$("td[data-ej-mappingkey~='"+u[0].key+"']"),l=c.find(".e-kanbancard"),p.push(l),g.push(new ej.DataManager(t._currentJsonData).executeLocal((new ej.Query).where(t.model.keyField,ej.FilterOperators.equal,u[0].key))),f=v.index($("td[data-ej-mappingkey~='"+u[0].key+"']")[0]),t._selectedCardData.length>0&&(e=$.inArray(f,t.selectedRowCellIndexes[0].cellIndex),!ej.isNullOrUndefined(e)&&e>=0&&(a=c.find(".e-cardselection"),s.push(a),nt.push(new ej.DataManager(t._selectedCardData).executeLocal((new ej.Query).where(t.model.keyField,ej.FilterOperators.equal,u[0].key))),a.length>0&&(b.push(f),k.push(t.selectedRowCellIndexes[0].cardIndex[e])))),h=h+l.length,w.push(f);return n={},n.count=h,n.cards=p,n.cardsData=g,n.cellIndexes=w,n.selectedCellCardIndexes={cellIndex:b,cardIndex:k},n.selectedCards=s,n.selectedCardsData=nt,n};t._trigger("headerClick",i)}else r.hasClass("e-headercell")&&(i["text "]=r.text(),i.cellIndex=h,i.target=r,i.columnData=t.model.columns[h],t._trigger("headerClick",i),t.model.allowScrolling&&t.model.allowToggleColumn&&t.KanbanCommon._setWidthToColumns())},n.prototype._toggleCardByTarget=function(n){var t=this.kanbanObj,s,e,o;if(n=n.hasClass("e-expandcollapse")?n.find("div:first"):n,n.hasClass("e-cardexpand")||n.hasClass("e-cardcollapse")){var i=n.closest("div.e-cardheader"),r=i.next(),h=i.find(".e-primarykey").text(),u=$(n).closest(".e-kanbancard"),f=u.attr("id");n.hasClass("e-cardexpand")?(n.removeClass("e-cardexpand").addClass("e-cardcollapse"),u.addClass("e-collapsedcard"),i.append(r.find(".e-text").clone()),r.hide(),$.inArray(f,t._collapsedCards)==-1&&t._collapsedCards.push(f),s=r.find(".e-bottom-triangle").clone(),i.append(s)):(n.removeClass("e-cardcollapse").addClass("e-cardexpand"),u.removeClass("e-collapsedcard"),i.find(".e-text,.e-bottom-triangle").remove(),r.show(),e=$.inArray(f,t._collapsedCards),e!=-1&&t._collapsedCards.splice(e,1));t.model.allowScrolling&&((t.getScrollObject().isVScroll()||t.getScrollObject().isHScroll())&&t.getScrollObject().refresh(),t.getScrollObject().isVScroll()?t.element.find(".e-kanbanheader").addClass("e-scrollcss").children().addClass("e-hscrollcss"):t.element.find(".e-kanbanheader").removeClass("e-scrollcss").children().removeClass("e-hscrollcss"));t.element.hasClass("e-responsive")&&(o=n.parents(".e-cell-scrollcontent"),o.length>0&&o.data("ejScroller").refresh())}},n.prototype._priorityColData=function(n,t){var u,f=this.kanbanObj.model.fields.primaryKey,i=this.kanbanObj.model.fields.priority,r;return u=$.map(n,function(n,i){if(n[f]==t[f])return i}),ej.isNullOrUndefined(u[0])||n.splice(u,1),r=t[i]-1,ej.isNullOrUndefined(n[r-1])||n[r-1][i]==t[i]&&--r,n.splice(r,0,t),n.sort(function(n,t){return n[i]-t[i]}),n},n.prototype._getPriorityData=function(n,t){var i,r=this.kanbanObj.model.fields.primaryKey;return i=$.map(n,function(n,i){if(n[r]==t[r])return i}),ej.isNullOrUndefined(i[0])?n.push(t):n[i][this.kanbanObj.model.fields.priority]=t[this.kanbanObj.model.fields.priority],n},n.prototype._preventCardMove=function(n,t){for(var s,i,r,u,e,h=this.kanbanObj,o=typeof t=="object"?t:t.split(","),f=0;f<o.length;f++){if(n==o[f])return!0;if(s=!0,i=h.model.workflows,!ej.isNullOrUndefined(i)&&i.length>0)for(r=0;r<i.length;r++)if(i[r].key==n&&!ej.isNullOrUndefined(i[r].allowedTransitions)){if(u=typeof i[r].allowedTransitions=="object"?i[r].allowedTransitions:i[r].allowedTransitions.split(","),u.length==1&&u[0].length==0)return!0;for(e=0;e<u.length;e++){if(o[f]==u[e])return!0;s=!1}}}return s?!0:!1},n.prototype._updateKbnPriority=function(n,t){var f=this.kanbanObj,o=f._currentJsonData,i,l,s=f.model.fields.primaryKey,u=f.model.fields.priority,h=f.model.fields.swimlaneKey,a,v,c,e,r;if((!ej.isNullOrUndefined(f._filterToolBar)&&f._filterToolBar.find("li.e-select").length>0||f._searchBar!=null&&f._searchBar.find(".e-cancel").length>0)&&(o=f._initialData),l=this._getKanbanCardData(o,t[s])[0],t[u]=parseInt(t[u]),i=ej.isNullOrUndefined(h)?new ej.DataManager(o).executeLocal((new ej.Query).where(f.model.keyField,ej.FilterOperators.equal,t[f.model.keyField])):new ej.DataManager(o).executeLocal((new ej.Query).where(f.model.keyField,ej.FilterOperators.equal,t[f.model.keyField]).where(h,ej.FilterOperators.equal,t[h])),i=i.slice(),a=f,v=t[s],i.sort(function(n,t){return n[u]-t[u]}),f._bulkPriorityData.length>0){for(r=0;r<i.length;r++)c=$.map(f._bulkPriorityData,function(n,t){if(n[s]==i[r][s])return t}),ej.isNullOrUndefined(c[0])||(i[r][f.model.fields.priority]=f._bulkPriorityData[c][f.model.fields.priority]);i.sort(function(n,t){return n[u]-t[u]})}for(i=ej.isNullOrUndefined(h)?this._priorityColData(i,t):this._priorityColData(i,t),e=t[u]-1,e!=$.inArray(t,i)&&(e=$.inArray(t,i)),t[u]-1>i.length&&(e=i.length-1),e<0&&(e=0),r=e;r<i.length;r++)ej.isNullOrUndefined(i[r])||(r<i.length-1&&!ej.isNullOrUndefined(i[r+1])&&i[r][u]>=i[r+1][u]?(i[r+1][u]=i[r][u]+1,n=this._getPriorityData(n,i[r+1])):r!=i.length-1||ej.isNullOrUndefined(i[r-1])||i[r][u]<i[r-1][u]&&(i[r][u]=i[r-1][u]+1,n=this._getPriorityData(n,i[r])));for(r=e;r>=0;r--)ej.isNullOrUndefined(i[r])||(ej.isNullOrUndefined(i[r-1])?r!=0||ej.isNullOrUndefined(i[r+1])||i[r][u]>i[r+1][u]&&(i[r][u]=i[r+1][u]-1,n=this._getPriorityData(n,i[r])):r>0&&i[r-1][u]>=i[r][u]&&(i[r-1][u]=i[r][u]-1,n=this._getPriorityData(n,i[r-1])));return n},n.prototype._kbnBulkUpdate=function(n,t,i){var r=this.kanbanObj,u,f;r._dataManager instanceof ej.DataManager&&!r._dataManager.dataSource.offline||r._dataSource().adaptor instanceof ej.remoteSaveAdaptor?(f={added:n,deleted:t,changed:i},$("#"+r._id).data("ejWaitingPopup").show(),u=r._dataManager.saveChanges(f,r.model.fields.primaryKey,r.model.query._fromTable),$.isFunction(u.promise)&&(u.done(function(){r.KanbanCommon._processBindings(r._saveArgs);r._cModifiedData=null;r._cAddedRecord=null;r._isAddNewClick=!1;r._currentData=null;r._newCard=null;r._saveArgs=null;r._cardEditClick=null;r._dblArgs=null;r._cDeleteData=null}),u.fail(function(n){var t;ej.isNullOrUndefined(r._saveArgs)?t=n:(r._saveArgs.error=n&&n.error?n.error:n,t=r._saveArgs);r._renderAllCard();r._enableDragandScroll();r._cModifiedData=null;r._cAddedRecord=null;r._isAddNewClick=!1;r._currentData=null;r._newCard=null;r._saveArgs=null;r._cardEditClick=null;r._dblArgs=null;r._cDeleteData=null;r._trigger("actionFailure",t);$("#"+r._id).data("ejWaitingPopup").hide()}))):r.KanbanCommon._processBindings(r._saveArgs);u!=undefined&&$.isFunction(u.promise)||(r._cModifiedData=null,r._cAddedRecord=null,r._isAddNewClick=!1,r._currentData=null,r._newCard=null,r._saveArgs=null,r._cardEditClick=null,r._dblArgs=null,r._cDeleteData=null)},n.prototype._checkSkipAction=function(n){switch(n.requestType){case"save":case"delete":return!0}return!1},n.prototype._processBindings=function(n){var t=this.kanbanObj,r,i;if(n.primaryKey=t.model.fields.primaryKey,!this._checkSkipAction(n)&&ej.isNullOrUndefined(n.dialogtype)&&t._trigger("actionBegin",n))return!0;t._ensureDataSource(n);t._editForm=t.element.find(".kanbanform");n.requestType=="beginedit"||n.requestType=="drop"||t._editForm.length==0||(t._editForm.length>1&&n.requestType=="save"&&n.action=="edit"&&(t._editForm=$(t._editForm[0])),$(t._editForm).find("select.e-dropdownlist").ejDropDownList("destroy"),$(t._editForm).find("textarea.e-rte").ejRTE("destroy"),$(t._editForm).find(".e-datepicker").ejDatePicker("destroy"),$(t._editForm).find(".e-datetimepicker").ejDateTimePicker("destroy"),$(t._editForm).find(".e-numerictextbox").ejNumericTextbox("destroy"),$(t._editForm).addClass("e-formdestroy"));n&&n.requestType=="delete"&&n.div.remove();(n.requestType=="drop"||t.model.showColumnWhenEmpty&&n.requestType=="add")&&(t._templateRefresh=!0);t._dataSource()instanceof ej.DataManager&&n.requestType!="beginedit"&&n.requestType!="cancel"&&n.requestType!="add"?(t.element.ejWaitingPopup("show"),r=t._queryPromise=t._dataSource().executeQuery(t.model.query),i=t,i._dataSource().ready?i._dataSource().ready.done(function(){i.KanbanCommon._processDataRequest(i,n,r)}):i.KanbanCommon._processDataRequest(i,n,r)):t.sendDataRenderingRequest(n)},n.prototype._processDataRequest=function(n,t,i){i.done(ej.proxy(function(i){n.element.ejWaitingPopup("hide");n._currentJsonData=n.currentViewData=i.result==null?[]:i.result;n.KanbanCommon._processData(i,t)}));i.fail(ej.proxy(function(i){n.element.ejWaitingPopup("hide");t.error=i.error;i=[];n.currentViewData=[];n.KanbanCommon._processData(i,t);n._trigger("actionFailure",t)}))},n.prototype._processData=function(n,t){var i=this.kanbanObj;ej.isNullOrUndefined(i.model.filterSettings)||(t.requestType=="filtering"||i.model.filterSettings.length>0&&t.requestType=="refresh")&&(i._filteredRecordsCount=n.count);i.sendDataRenderingRequest(t)},n.prototype._moveCurrentCard=function(n,t,i){var h,u,e,f,s,c,b,l,y,p,k,r=this.kanbanObj,a,v,w,o;if(u=r.selectedRowCellIndexes[0],a=u.cardIndex,h=u.cellIndex[u.cellIndex.length-1],t=="down"||t=="up"){if(k=$(r._columnRows[n]).find("td.e-rowcell").eq(h),s=k.find(".e-kanbancard"),f=$(s).index(k.find(".e-cardselection")),l=s.length,t=="down")if(ej.isNullOrUndefined(r.model.fields.swimlaneKey)?f=r.model.selectionType=="multiple"?a[u.cellIndex.length-1][a[u.cellIndex.length-1].length-1]:a[u.cellIndex.length-1]:(i.shiftKey||i.ctrlKey)&&r.model.selectionType=="multiple"&&(f=a[u.cellIndex.length-1][a[u.cellIndex.length-1].length-1]),f==l-1||l==0){if(n==r._columnRows.length-1)return;if((i.shiftKey||i.ctrlKey)&&r.model.selectionType!="single")return;this._moveCurrentCard(n+1,t,i)}else s.eq(f+1).hasClass("e-cardselection")?r.KanbanSelection._cardSelection([[n,[h],[f]]],$(s[f]),i):r.KanbanSelection._cardSelection([[n,[h],[f+1]]],$(s[f+1]),i);else if(t=="up")if(ej.isNullOrUndefined(r.model.fields.swimlaneKey)?f=r.model.selectionType=="multiple"?u.cardIndex[u.cellIndex.length-1][u.cardIndex[u.cellIndex.length-1].length-1]:u.cardIndex[u.cellIndex.length-1]:(i.shiftKey||i.ctrlKey)&&r.model.selectionType=="multiple"&&(f=u.cardIndex[u.cellIndex.length-1][u.cardIndex[u.cellIndex.length-1].length-1]),l==0||f==0){if(n==0)return;if((i.shiftKey||i.ctrlKey)&&r.model.selectionType!="single")return;this._moveCurrentCard(n-1,t,i)}else s.eq(f-1).hasClass("e-cardselection")?r.KanbanSelection._cardSelection([[n,[h],[f]]],$(s[f]),i):f!=-1?r.KanbanSelection._cardSelection([[n,[h],[f-1]]],$(s[f-1]),i):r.KanbanSelection._cardSelection([[n,[h],[l-1]]],$(s[l-1]),i);(i.shiftKey||i.ctrlKey)&&!1||r.element.find(".e-cardselection").closest("tr.e-columnrow:visible").length==0&&r.KanbanSwimlane.toggle($(r._columnRows[r.selectedRowCellIndexes[0].rowIndex]).prev().prev(".e-swimlanerow").find(".e-slcollapse"))}else if(b=$(r._columnRows[u.rowIndex]),c=b.find("td.e-rowcell"),s=c.eq(n).find(".e-kanbancard"),e=c.eq(n).find(".e-kanbancard"),y=c.eq(n+1),p=c.eq(n-1),v=r.element.find(".e-kanbancard"),w=r.element.find(".e-cardselection"),p.is(":visible")&&y.is(":visible")||ej.isNullOrUndefined(r.model.fields.swimlaneKey)||r.element.find(".e-cardselection").closest("tr.e-columnrow:visible").length==0&&r.KanbanSwimlane.toggle($(r._columnRows[r.selectedRowCellIndexes[0].rowIndex]).prev().prev(".e-swimlanerow").find(".e-slcollapse")),f=(i.shiftKey||i.ctrlKey)&&r.model.selectionType=="multiple"?u.cardIndex[u.cellIndex.length-1][u.cardIndex[u.cellIndex.length-1].length-1]:r.model.selectionType=="single"?u.cardIndex[0]:u.cardIndex[0][0],t=="right"){if(o=v.eq(v.index(w)+1),n==c.length-1){o.length>0&&(!i.shiftKey&&!i.ctrlKey||r.model.selectionType=="single")&&!ej.isNullOrUndefined(r.model.fields.swimlaneKey)&&(r.KanbanSelection._cardSelection([[r._columnRows.index(o.closest("tr.e-columnrow")),[o.closest("tr.e-columnrow").find("td.e-rowcell").index(o.closest("td.e-rowcell"))],[0]]],o,i),r.element.find(".e-cardselection").closest("tr:visible").length==0&&r.KanbanSwimlane.toggle($(r._columnRows[r.selectedRowCellIndexes[0].rowIndex]).prev().prev(".e-swimlanerow").find(".e-slcollapse")));return}y.find(".e-kanbancard:visible").length>0?(e=y.find(".e-kanbancard"),e.eq(f).length>0?r.KanbanSelection._cardSelection([[u.rowIndex,[n+1],[f]]],$(e[f]),i):r.KanbanSelection._cardSelection([[u.rowIndex,[n+1],[e.length-1]]],$(e[e.length-1]),i),(i.shiftKey||i.ctrlKey)&&r.model.selectionType!="single"||ej.isNullOrUndefined(r.model.fields.swimlaneKey)||r.element.find(".e-cardselection").closest("tr.e-columnrow:visible").length==0&&r.KanbanSwimlane.toggle($(r._columnRows[r.selectedRowCellIndexes[0].rowIndex]).prev().prev(".e-swimlanerow").find(".e-slcollapse"))):this._moveCurrentCard(n+1,t,i)}else if(t=="left"){if(v.index(w)!=0&&(o=v.eq(v.index(w)-1)),n==0){if(!i.shiftKey&&!i.ctrlKey&&!ej.isNullOrUndefined(r.model.fields.swimlaneKey)){if(r.KanbanSwimlane.toggle($(b).prev().find(".e-slexpand")),ej.isNullOrUndefined(o))return;(o.length>0&&!i.shiftKey&&!i.ctrlKey||r.model.selectionType=="single")&&(r.KanbanSelection._cardSelection([[r._columnRows.index(o.closest("tr.e-columnrow")),[o.closest("tr.e-columnrow").find("td.e-rowcell").index(o.closest("td.e-rowcell"))],[0]]],o,i),r.element.find(".e-cardselection").closest("tr.e-columnrow:visible").length==0&&r.KanbanSwimlane.toggle($(r._columnRows[r.selectedRowCellIndexes[0].rowIndex]).prev().prev(".e-swimlanerow").find(".e-slcollapse")))}return}p.find(".e-kanbancard:visible").length>0?(e=p.find(".e-kanbancard"),e.eq(f).length>0?r.KanbanSelection._cardSelection([[u.rowIndex,[n-1],[f]]],$(e[f]),i):r.KanbanSelection._cardSelection([[u.rowIndex,[n-1],[e.length-1]]],$(e[e.length-1]),i),(i.shiftKey||i.ctrlKey)&&r.model.selectionType!="single"||ej.isNullOrUndefined(r.model.fields.swimlaneKey)||r.element.find(".e-cardselection").closest("tr.e-columnrow:visible").length==0&&r.KanbanSwimlane.toggle($(r._columnRows[r.selectedRowCellIndexes[0].rowIndex]).prev().prev(".e-swimlanerow").find(".e-slexpand"))):this._moveCurrentCard(n-1,t,i)}},n.prototype._kanbanKeyPressed=function(n,t,i){var u,s,f,h,a=$(t),c=!0,r=this.kanbanObj,l,e,o;u=r.element.find(".e-cardselection");s=r.element.find(".e-kanbancard:visible");i.code==13&&t.tagName=="INPUT"&&a.closest("#"+r._id+"_toolbarItems_search").length&&(n="searchRequest");f=r.selectedRowCellIndexes[0];l=!ej.isNullOrUndefined(r.model.fields.swimlaneKey);switch(n){case"editCard":u.length>0&&r.model.editSettings.allowEditing&&(h=f.cellIndex,u=r._columnRows.eq(f.rowIndex).find("td.e-rowcell").eq(h[h.length-1]).find(".e-kanbancard").eq(f.cardIndex[h.length-1]),r.KanbanEdit.startEdit(u));break;case"insertCard":r.model.editSettings.allowAdding&&r.KanbanEdit.addCard();break;case"swimlaneExpandAll":r.KanbanSwimlane&&r.KanbanSwimlane.expandAll();break;case"swimlaneCollapseAll":r.KanbanSwimlane&&r.KanbanSwimlane.collapseAll();break;case"downArrow":u.length>0&&document.activeElement.id==r._id&&this._moveCurrentCard(f.rowIndex,"down",i);break;case"upArrow":u.length>0&&document.activeElement.id==r._id&&this._moveCurrentCard(f.rowIndex,"up",i);break;case"leftArrow":u.length>0&&document.activeElement.id==r._id&&this._moveCurrentCard(f.cellIndex[f.cellIndex.length-1],"left",i);r.model.isResponsive&&r.element.hasClass("e-responsive")&&r.KanbanAdaptive._kbnRightSwipe();break;case"rightArrow":u.length>0&&document.activeElement.id==r._id&&this._moveCurrentCard(f.cellIndex[f.cellIndex.length-1],"right",i);r.model.isResponsive&&r.element.hasClass("e-responsive")&&r.KanbanAdaptive._kbnLeftSwipe();break;case"multiSelectionByDownArrow":i.ctrlKey=!0;u.length>0&&this._moveCurrentCard(f.rowIndex,"down",i);break;case"multiSelectionByUpArrow":i.ctrlKey=!0;u.length>0&&this._moveCurrentCard(f.rowIndex,"up",i);break;case"multiSelectionByRightArrow":i.shiftKey=!0;u.length>0&&this._moveCurrentCard(f.cellIndex[f.cellIndex.length-1],"right",i);break;case"multiSelectionByLeftArrow":i.shiftKey=!0;u.length>0&&this._moveCurrentCard(f.cellIndex[f.cellIndex.length-1],"left",i);break;case"firstCardSelection":r.model.allowSelection&&document.activeElement.id==r._id&&(r.KanbanSelection.clear(),s.eq(0).addClass("e-cardselection"),u=r.element.find(".e-cardselection"),e=$(r._columnRows).find(".e-cardselection").index(),o=$(r._columnRows).find(".e-cardselection").parent().children().eq(0).hasClass("e-limits")?e-1:e,r.model.selectionType=="multiple"?r.selectedRowCellIndexes.push({cardIndex:[[o]],cellIndex:[u.parent().index()],rowIndex:$(r._columnRows).index(u.parents(".e-columnrow"))}):r.selectedRowCellIndexes.push({cardIndex:[o],cellIndex:[u.parent().index()],rowIndex:$(r._columnRows).index(u.parents(".e-columnrow"))}));break;case"lastCardSelection":r.model.allowSelection&&document.activeElement.id==r._id&&(r.KanbanSelection.clear(),s.eq(s.length-1).addClass("e-cardselection"),u=r.element.find(".e-cardselection"),u=r.element.find(".e-cardselection"),e=$(r._columnRows).find(".e-cardselection").index(),o=$(r._columnRows).find(".e-cardselection").parent().children().eq(0).hasClass("e-limits")?e-1:e,r.model.selectionType=="multiple"?r.selectedRowCellIndexes.push({cardIndex:[[o]],cellIndex:[u.parent().index()],rowIndex:$(r._columnRows).index(u.parents(".e-columnrow"))}):r.selectedRowCellIndexes.push({cardIndex:[o],cellIndex:[u.parent().index()],rowIndex:$(r._columnRows).index(u.parents(".e-columnrow"))}));break;case"cancelRequest":$("#"+r._id+"_dialogEdit:visible").length>0?r.KanbanEdit.cancelEdit():u.length>0&&r.KanbanSelection.clear();break;case"searchRequest":r.KanbanFilter.searchCards(a.val());c=!1;break;case"saveRequest":$("#"+r._id+"_dialogEdit:visible").length>0&&r.KanbanEdit.endEdit();break;case"deleteCard":u.length>0&&$("#"+r._id+"_dialogEdit:visible").length<=0&&!ej.isNullOrUndefined(r.model.fields.primaryKey)&&r.KanbanEdit.deleteCard(u.attr("id"));break;case"selectedSwimlaneExpand":u.length>0&&l&&(u.is(":visible")||r.KanbanSwimlane.toggle($(r._columnRows[r.selectedRowCellIndexes[0].rowIndex]).prev().prev(".e-swimlanerow").find(".e-slexpandcollapse")));break;case"selectedSwimlaneCollapse":u.length>0&&l&&u.is(":visible")&&r.KanbanSwimlane.toggle($(r._columnRows[r.selectedRowCellIndexes[0].rowIndex]).prev().find(".e-slexpandcollapse"));break;case"selectedColumnCollapse":u.length>0&&this._toggleField(r.model.columns[r.selectedRowCellIndexes[0].cellIndex[0]].headerText);break;case"selectedColumnExpand":r._collapsedColumns.length!=0&&this._toggleField(r._collapsedColumns[r._collapsedColumns.length-1]);break;case"focus":r.element.find(".e-cardselection").focus();break;default:c=!0}return c},n.prototype._createStackedRow=function(n){for(var c,l,o,f,r,a,i,s,e,u,p,w,v=ej.buildTag("tr.e-columnheader e-stackedHeaderRow","",{},{}),t=[],y=this.kanbanObj,h=0;h<y.model.columns.length;h++)if(c=y.model.columns[h],c.visible!=!1){for(l="",o=n.stackedHeaderColumns,f=0;f<o.length;f++)o[f].column.indexOf(c.headerText)!=-1&&(l=o[f].headerText);t.push(l)}for(r=[],a="",i=0;i<t.length;i++){for(s=1,e=i+1;e<t.length;e++)if(t[i]!=""&&t[e]!=""&&t[i]==t[e])s++;else break;r.push({sapnCount:s,headerText:t[i],css:t[i]!=""?a:"e-sheader"});i+=s-1;a=""}for(u=0;u<r.length;u++)p=ej.buildTag("div",r[u].headerText,{},{}),w=ej.buildTag("th.e-headercell e-stackedHeaderCell "+r[u].css+"","",{},{colspan:r[u].sapnCount}).append(p),v.append(w);return v},n.prototype._refreshStackedHeader=function(){for(var u,n=this.kanbanObj,i=n.model.stackedHeaderRows,r=n.element.find(".e-stackedHeaderRow"),t=0;t<i.length;t++)u=this._createStackedRow(i[t]),r.length>0&&r.remove(),$(n.getHeaderTable().find(".e-columnheader").before(u));n.model.allowScrolling&&n.KanbanScroll._refreshScroller({requestType:"refresh"})},n.prototype._stackedHeadervisible=function(){for(var t=this.kanbanObj.element.find(".e-columnheader .e-stackedHeaderCell "),n=0;n<t.length;n++)t[n].offsetWidth<t[n].scrollWidth&&t.eq(n).find("div").addClass("e-hide")},n.prototype._kanbanUpdateCard=function(n,t){var s,i=this.kanbanObj,f=i._saveArgs,w,g,v,nt=i.model.fields.primaryKey,tt=new ej.DataManager(i._currentJsonData).executeLocal((new ej.Query).where(nt,ej.FilterOperators.equal,t[nt])),it,b,a,u,l,k,c,h,y,o,et,e,p,d,r,ot;if(tt.length>0&&(t=$.extend(tt[0],t)),it=i.model.fields.swimlaneKey,b=i.model.swimlaneSettings.unassignedGroup,!ej.isNullOrUndefined(it)&&b.enable&&b.keys.length>0)if(ej.isNullOrUndefined(t.length))t=i._checkKbnUnassigned(t);else for(r=0;r<t.length;r++)t[r]=i._checkKbnUnassigned(t[r]);if(ej.isNullOrUndefined(f)&&(i._saveArgs=f={data:t,requestType:"save",action:"edit",primaryKeyValue:n},i._cModifiedData=t),s=i._dataSource()instanceof ej.DataManager?i._dataSource().dataSource.json:i._dataSource(),s.length==0&&i._currentJsonData.length>0&&(s=i._currentJsonData),i._dataManager instanceof ej.DataManager&&!i._dataManager.dataSource.offline&&!ej.isNullOrUndefined(f)&&f.requestType!="drop"&&i.model.enableTotalCount&&(i._filterCollection.length>0||i.model.searchSettings.key.length>0)&&(g=new ej.DataManager(s).executeLocal((new ej.Query).where(i.model.fields.primaryKey,ej.FilterOperators.equal,t[i.model.fields.primaryKey])),w=i.headerContent.find(".e-headercell"),ej.isNullOrUndefined(g)||f.action!="edit"||(v=i._getColumnKeyIndex(e[0][i.model.keyField]),w.eq(v).addClass("e-cardDraggedHeader")),ej.isNullOrUndefined(t)||(v=i._getColumnKeyIndex(t[i.model.keyField]),w.eq(v).addClass("e-cardDroppedHeader"))),(ej.isNullOrUndefined(f)||!ej.isNullOrUndefined(f)&&f.requestType!="drop")&&i.model.fields.priority)if(a=[],$.isPlainObject(t))a.push(t),ej.isNullOrUndefined(t[i.model.fields.priority])&&(e=new ej.DataManager(s).executeLocal((new ej.Query).where(i.model.fields.primaryKey,ej.FilterOperators.equal,t[i.model.fields.primaryKey])),e.length>0?t[i.model.fields.priority]=e[0][i.model.fields.priority]:(c=new ej.DataManager(s).executeLocal((new ej.Query).where(i.model.keyField,ej.FilterOperators.equal,t[i.model.keyField])),t[i.model.fields.priority]=c[c.length-1][i.model.fields.priority]+1)),t=i.KanbanCommon._updateKbnPriority(a,t);else{for(k=i.model.fields.primaryKey,r=0;r<t.length;r++)for(a=$.extend(!0,[],t),ej.isNullOrUndefined(t[r][i.model.fields.priority])&&(e=new ej.DataManager(s).executeLocal((new ej.Query).where(i.model.fields.primaryKey,ej.FilterOperators.equal,t[r][i.model.fields.primaryKey])),e.length>0?t[r][i.model.fields.priority]=e[0][i.model.fields.priority]:(c=new ej.DataManager(s).executeLocal((new ej.Query).where(i.model.keyField,ej.FilterOperators.equal,t[r][i.model.keyField])),t[r][i.model.fields.priority]=c[c.length-1][i.model.fields.priority]+1)),u=t[r],l=i.KanbanCommon._updateKbnPriority(a,u),h=0;h<l.length;h++)i._bulkPriorityData.length>0?(y=$.map(i._bulkPriorityData,function(n,t){if(n[k]==l[h][k])return t}),ej.isNullOrUndefined(y[0])?i._bulkPriorityData.push(l[h]):(i._bulkPriorityData.splice(y,1),i._bulkPriorityData.splice(y,0,l[h]))):i._bulkPriorityData.push(l[h]);t=i._bulkPriorityData;i._bulkPriorityData=[]}var u=[],rt=[],ut=[],ft=[];if(ej.isNullOrUndefined(t)&&n&&(t=new ej.DataManager(i._currentJsonData).executeLocal((new ej.Query).where(i.model.fields.primaryKey,ej.FilterOperators.equal,n))[0]),t=ej.isNullOrUndefined(t)?null:t,$.isPlainObject(t)?u.push(t):u=t,i._isLocalData){for(o=i.model.query,et=o.clone(),r=0;r<u.length;r++)o=o.where(i.model.fields.primaryKey,ej.FilterOperators.equal,u[r][i.model.fields.primaryKey]),e=i._dataManager.executeLocal(o),p=i._dataSource()instanceof ej.DataManager?$.inArray(e.result[0],i._dataSource().dataSource.json):$.inArray(e.result[0],i._dataSource()),p>=0?i._dataSource()instanceof ej.DataManager?$.extend(i._dataSource().dataSource.json[p],u[r]):$.extend(i._dataSource()[p],u[r]):(d=u[r],i._cAddedRecord=null,i._dataSource()instanceof ej.DataManager?i._dataSource().dataSource.json.push(d):i._dataSource(undefined,!0).splice(i._dataSource().length,0,d)),o.queries=o.queries.slice(o.queries.length);o.queries=et.queries}if(f.requestType!="drop"&&(f.data=u),i._saveArgs=f,i._dataManager instanceof ej.DataManager&&!i._dataManager.dataSource.offline||i._dataSource().adaptor instanceof ej.remoteSaveAdaptor){if(f.requestType=="delete")ut.push(f.data[0]);else for(r=0;r<u.length;r++)ot=this._getKanbanCardData(i._currentJsonData,u[r][i.model.fields.primaryKey]),ot.length>0?ft.push(u[r]):rt.push(u[r]);i.KanbanCommon._kbnBulkUpdate(rt,ut,ft)}else i.KanbanCommon._processBindings(i._saveArgs),i._cModifiedData=null,i._cAddedRecord=null,i._isAddNewClick=!1,i._currentData=null,i._newCard=null,i._saveArgs=null,i._cardEditClick=null,i._dblArgs=null},n.prototype._getMetaColGroup=function(){for(var i,r=ej.buildTag("colgroup","",{},{}),n=this.kanbanObj,t=0;t<n.model.columns.length;t++)i=$(document.createElement("col")),n.model.allowToggleColumn&&n.model.columns[t].isCollapsed===!0&&i.addClass("e-shrinkcol"),n.model.columns[t].visible===!1&&i.addClass("e-hide"),r.append(i);return r},n.prototype._kanbanToolbarClick=function(n,t){var i=n.currentTarget,u=n.target,e,f,o,r;if($(i).hasClass("e-quickfilter")||(t.KanbanFilter&&u.tagName=="INPUT"&&$(u).parent().next().hasClass("e-cancel")&&$(u).val().length==0&&t.KanbanFilter.searchCards(""),t.model.isResponsive&&t.element.hasClass("e-responsive")&&($(t.itemsContainer).parent().children().not(".e-searchbar").hide(),n.keyCode==8&&t.KanbanAdaptive._kbnTimeoutSearch(t.element,n)),n.event==undefined))return!1;for(e=$(i).text(),f=0;f<t.model.filterSettings.length;f++)if(t.model.filterSettings[f].text==e)break;return(o=f==t.model.filterSettings.length?null:t.model.filterSettings[f],r={itemName:$(i).attr("data-content"),itemId:i.id,target:u,currentTarget:i,itemIndex:$(i).index(),toolbarData:n,itemText:e,model:t.model,cancel:!1,type:"toolbarClick"},t._trigger("toolbarClick",r))?!1:(r.itemId==t._id+"_toolbarItems_search"?(r.target.nodeName=="A"&&t.model.allowSearching&&t._searchBar!=null&&$(r.currentTarget).find("input").val().length!=0&&($(u).is(t._searchBar.find(".e-searchfind"))?t.KanbanFilter.searchCards($(r.currentTarget).find("input").val()):$(u).is(t._searchBar.find(".e-cancel"))&&t.KanbanFilter.searchCards("")),t._isWatermark||(t._searchInput.blur(function(){t._searchInput.val()||t._hiddenSpan.css("display","block")}),t._hiddenSpan.css("display","none"))):$(r.currentTarget).hasClass("e-printlist")?t.print():t.KanbanFilter&&!$(i).parent().hasClass("e-customtoolbar")&&t.KanbanFilter._filterHandler(o,i),!1)},n.prototype._kanbanSetModel=function(n,t){var i,e,h,u,s,o,r,f,c;for(i in n)switch(i){case"columns":h=n.columns;t.model.columns=[];t._keyValue=[];t.columns(h,"add");break;case"allowDragAndDrop":t.model.allowDragAndDrop=n[i];t.model.allowDragAndDrop&&(t.KanbanDragAndDrop=new ej.KanbanFeatures.DragAndDrop(t));t._renderAllCard();t._enableDragandScroll();break;case"cssClass":t.element.removeClass(t.model.cssClass).addClass(n[i]);break;case"allowFiltering":case"filterSettings":i=="allowFiltering"?(t.model.allowFiltering=n[i],t.model.allowFiltering&&(t.KanbanFilter=new ej.KanbanFeatures.Filter(t)),t.model.allowSearching||t.model.filterSettings.length!=0||t.model.allowPrinting||t.model.customToolbarItems.length!=0||(t.KanbanFilter=null)):(i=="filterSettings"&&(t.model.filterSettings=n[i]),t.model.filterSettings.length>0?(t.element.find(".e-kanbantoolbar").remove(),t._filterCollection=[],t._renderToolBar().insertBefore(t.element.find(".e-kanbanheader").first()),t.model.filterSettings.length>0&&(t.KanbanFilter=new ej.KanbanFeatures.Filter(t))):(t.element.find(".e-kanbantoolbar").remove(),t.model.filterSettings=[],t._filterCollection=[],t._renderToolBar().insertBefore(t.element.find(".e-kanbanheader").first()),t.model.allowSearching||t.model.filterSettings.length!=0||t.model.allowPrinting||t.model.customToolbarItems.length!=0||t.element.find(".e-kanbantoolbar").remove(),$(t.element.find(".e-kanbanheader")).replaceWith(t._renderHeader()),t.refresh()));break;case"allowSearching":if(t.model.allowSearching=n[i],u=[],t.model.filterSettings.length!=0||t.model.allowFiltering)for(s=t.element.find(".e-kanbantoolbar .e-tooltxt.e-select"),o=0;o<s.length;o++)u.push(s.eq(o).index());if(t.model.allowSearching){for(t.element.find(".e-kanbantoolbar").remove(),t.model.searchSettings.key="",t._renderToolBar().insertBefore(t.element.find(".e-kanbanheader").first()),r=0;r<u.length;r++)t.element.find(".e-kanbantoolbar .e-tooltxt").eq(u[r]).addClass("e-select");t.model.allowSearching&&(t.KanbanFilter=new ej.KanbanFeatures.Filter(t))}else if(t.KanbanFilter.clearSearch(),t.element.find(".e-kanbantoolbar").remove(),t._renderToolBar().insertBefore(t.element.find(".e-kanbanheader").first()),t.model.filterSettings.length!=0||t.model.allowPrinting||t.model.customToolbarItems.length!=0||t.element.find(".e-kanbantoolbar").remove(),t.model.filterSettings.length!=0||t.model.allowFiltering)for(r=0;r<u.length;r++)t.element.find(".e-kanbantoolbar .e-tooltxt").eq(u[r]).addClass("e-select");t._on($("#"+t._id+"_searchbar"),"keyup","",t._onToolbarClick);break;case"enableTotalCount":t.model.enableTotalCount=n[i];$(t.element.find(".e-kanbanheader")).replaceWith(t._renderHeader());t.model.enableTotalCount&&this._totalCount();t.refresh();break;case"fields":i=="fields"&&($(t.element.find(".e-kanbanheader")).replaceWith(t._renderHeader()),t.model.allowSearching&&t.KanbanFilter.searchCards(""),!ej.isNullOrUndefined(t.model.fields.swimlaneKey)&&t.model.contextMenuSettings.enable&&t.KanbanContext._renderContext(),t.model.filterSettings.length>0&&t.KanbanFilter.clearFilter(),t.model.filterSettings.length!=0&&$("#Kanban").find(".e-kanbantoolbar .e-tooltxt").removeClass("e-select"),t.getHeaderContent().replaceWith(t._renderHeader()),this._refreshDataSource(t._dataSource()),this._renderLimit(),t.model.enableTotalCount&&this._totalCount(),t.element.find(".e-kanbantoolbar").remove(),t._renderToolBar().insertBefore(t.element.find(".e-kanbanheader").first()),t.model.allowSearching||t.model.filterSettings.length!=0||t.model.allowPrinting||t.model.customToolbarItems.length!=0||t.element.find(".e-kanbantoolbar").remove(),t.model.fields&&!ej.isNullOrUndefined(t.model.fields.swimlaneKey)&&(t.KanbanSwimlane=new ej.KanbanFeatures.Swimlane(t)),t._on($("#"+t._id+"_searchbar"),"keyup","",t._onToolbarClick));break;case"enableTouch":t.model.enableTouch=n[i];t.model.enableTouch?(t._off(t.element,"dblclick",".e-kanbancard"),t.model.editSettings.allowEditing&&t._on(t.element,$.isFunction($.fn.doubletap)&&t.model.enableTouch?"doubletap":"dblclick",".e-kanbancard",t._cardDblClickHandler),t._on(t.element,$.isFunction($.fn.tap)&&t.model.enableTouch?"tap":"click","",t._clickHandler),t._on($(".e-swimlane-window,.e-kanbanfilter-window,.e-kanbantoolbar"),$.isFunction($.fn.tap)&&t.model.enableTouch?"tap":"click","",t._kbnAdaptClickHandler),t._on($(".e-kanban-editdiv"),$.isFunction($.fn.tap)&&t.model.enableTouch?"tap":"click","",t._kbnAdaptEditClickHandler),t.KanbanAdaptive&&(t._on(t.element,"taphold","",t.KanbanAdaptive._kbnHoldHandler),t._on(t.element,"touchend","",t.KanbanAdaptive._kbnTouchEndHandler),t._on(t.element,"swipeleft swiperight",".e-kanbancontent",$.proxy(t._swipeKanban,t)))):(t._off(t.element,"doubletap",".e-kanbancard"),t.model.editSettings.allowEditing&&t._on(t.element,$.isFunction($.fn.doubletap)&&t.model.enableTouch?"doubletap":"dblclick",".e-kanbancard",t._cardDblClickHandler),t.KanbanAdaptive&&(t._off(t.element,"taphold","",t.KanbanAdaptive._kbnHoldHandler),t._off(t.element,"touchend","",t.KanbanAdaptive._kbnTouchEndHandler),t._off(t.element,"swipeleft swiperight",".e-kanbancontent",$.proxy(t._swipeKanban,t))),t._on(t.element,$.isFunction($.fn.tap)&&t.model.enableTouch?"tap":"click","",t._clickHandler),t._on($(".e-swimlane-window,.e-kanbanfilter-window,.e-kanbantoolbar"),$.isFunction($.fn.tap)&&t.model.enableTouch?"tap":"click","",t._kbnAdaptClickHandler),t._on($(".e-kanban-editdiv"),$.isFunction($.fn.tap)&&t.model.enableTouch?"tap":"click","",t._kbnAdaptEditClickHandler));break;case"allowHover":t.model.allowHover=n[i];t._enableCardHover();break;case"enableRTL":t.model.enableRTL=n[i];this._enableKanbanRTL();t.refresh(!0);this._renderLimit();break;case"tooltipSettings":$.extend(t.model.tooltipSettings,n[i]);!ej.isNullOrUndefined(t.model.tooltipSettings)&&t.model.tooltipSettings.enable?(t.element.find(".e-kanbantooltip").remove(),t.element.append($("<div class='e-kanbantooltip'><\/div>")),t.element.find(".e-kanbantooltip").hide(),t._on(t.element,"mouseover",".e-kanbancard",$.proxy(t._showToolTip,t)),t._on(t.element,"mouseout",".e-kanbancard",$.proxy(t._hideToolTip,t))):(t._off(t.element,"mouseover",".e-kanbancard"),t._off(t.element,"mouseout",".e-kanbancard"));break;case"allowScrolling":case"scrollSettings":if(e=t.getContent(),t.model.allowScrolling=n[i],i!="allowScrolling"){if(!ej.isNullOrUndefined(n.scrollSettings)){if($.isEmptyObject(n.scrollSettings))break;$.extend(t.model.scrollSettings,n.scrollSettings)}ej.isNullOrUndefined(n.allowScrolling)||(t.model.allowScrolling=n.allowScrolling);ej.isNullOrUndefined(e.data("ejScroller"))||e.ejScroller("destroy");t.model.allowScrolling?(t.KanbanScroll=new ej.KanbanFeatures.Scroller(t),t.getHeaderContent().find("div").first().addClass("e-headercontent"),t._originalScrollWidth=t.model.scrollSettings.width,t.KanbanScroll._renderScroller()):(t.element.children(".e-kanbanheader").removeClass("e-scrollcss"),t.element.css("width","auto"),t.element.removeClass("e-kanbanscroll"),t.element.find(".e-headercontent").removeClass("e-hscrollcss"));ej.isNullOrUndefined(t.model.fields.swimlaneKey)||(t._swimlaneRows=t.element.find(".e-swimlanerow"));!$.isEmptyObject(t._freezeSwimlaneRow)&&t.KanbanSwimlane&&t.KanbanSwimlane._removeFreezeRow()}break;case"dataSource":e=t.element.find(".e-kanbancontent").first();this._refreshDataSource(t._dataSource());this._addLastRow();break;case"swimlaneSettings":ej.isNullOrUndefined(t.model.fields.swimlaneKey)||($.extend(t.model.swimlaneSettings,n[i]),ej.isNullOrUndefined(n[i].showCount)||t.refresh(!0));break;case"editSettings":$.extend(t.model.editSettings,n[i]);t.KanbanEdit._processEditing();t._tdsOffsetWidth=[];(t.model.editSettings.allowEditing||t.model.editSettings.allowAdding)&&(t.model.editSettings.editMode=="dialog"||t.model.editSettings.editMode=="dialogtemplate"?($("#"+t._id+"_dialogEdit").data("ejDialog")&&$("#"+t._id+"_dialogEdit").ejDialog("destroy"),$("#"+t._id+"_dialogEdit_wrapper,#"+t._id+"_dialogEdit").remove(),ej.isNullOrUndefined($("#"+t._id+"_externalEdit"))||$("#"+t._id+"_externalEdit").remove(),t.element.append(t.KanbanEdit._renderDialog())):(t.model.editSettings.editMode=="externalform"||t.model.editSettings.editMode=="externalformtemplate")&&(ej.isNullOrUndefined(t.element.find(".e-kanbandialog"))||t.element.find(".e-kanbandialog").remove(),$("#"+t._id+"_externalEdit").remove(),t.element.append(t.KanbanEdit._renderExternalForm())),t._isEdit=!1);t._enableEditingEvents();break;case"allowSelection":n[i]&&(t._off(t.element,"click"),t._on(t.element,"click","",t._clickHandler));break;case"query":t.model.query=$.extend(!0,{},n[i]);break;case"stackedHeaderRows":i=="stackedHeaderRows"&&(t.model.stackedHeaderRows=n[i]);t.model.stackedHeaderRows.length>0?this._refreshStackedHeader():(t.element.find(".e-stackedHeaderRow").remove(),t.model.stackedHeaderRows=[]);break;case"cardSettings":ej.isNullOrUndefined(n.cardSettings)||$.extend(t.model.cardSettings,n.cardSettings);t.element.find(".e-kanbancard").remove();t.refreshTemplate();t._renderAllCard();t._enableDragandScroll();break;case"allowToggleColumn":t.model.allowToggleColumn=n[i];t.refreshTemplate();t.getHeaderContent().replaceWith(t._renderHeader());t.sendDataRenderingRequest({requestType:"refresh"});t.model.allowToggleColumn||t.element.find(".e-shrinkheader").not(".e-hide").addClass("e-hide");t.model.enableTotalCount&&this._totalCount();this._renderLimit();break;case"locale":t.model.locale=n[i];f=t.model;c=t.element;f.query.queries=f.query.queries.slice(0,f.query.queries.length-1);t.element.ejKanban("destroy").ejKanban(f);t.element=c;t.model=f;t._collapsedCards.length!=0&&t.toggleCard(t._collapsedCards)}},n.prototype._setWidthToColumns=function(){var n=this.kanbanObj,f,r,u,i,l,a,s,h=n.getContentTable().children("colgroup").find("col"),e=n.getHeaderTable().children("colgroup").find("col"),o=n._originalWidth-n._collapsedColumns.length*50,t,c,v;for(n.initialRender&&n.model.allowScrolling&&(typeof n._originalScrollWidth=="string"&&(n.element.css("width","auto"),o=n.element.width(),n.model.scrollSettings.width=="auto"||n._originalScrollWidth=="auto"||n.model.scrollSettings.width==="100%"?(n._originalScrollWidth="100%",n.model.scrollSettings.width="auto"):n.model.scrollSettings.width=o*(parseFloat(n._originalScrollWidth)/100)),(n.model.scrollSettings.width||n.model.width)&&n.element.width(n.model.scrollSettings.width||n.model.width)),n._extraWidth=0,r=n._initialKanbanModel,l=parseInt(n.element.width())-(n._visibleColumns.length+1)*Math.round(n.getHeaderTable().css("border-spacing").split("px")[0])-18,t=0;t<e.length;t++)u=r.columns[t],ej.isNullOrUndefined(u.visible)&&n.model.allowScrolling&&!ej.isNullOrUndefined(u.width)&&($.isNumeric(r.scrollSettings.width)||$.isNumeric(u.width)||(r.scrollSettings.width.indexOf("%")>0||r.scrollSettings.width=="auto")&&u.width.indexOf("%")>0)&&(s=typeof u.width=="string"&&u.width.indexOf("%")!=-1?parseInt(u.width)/100*l:u.width,f=ej.isNullOrUndefined(f)?s:f+s,n.model.columns[t].isCollapsed&&(f=f-s,f=f+50));for(t=0;t<e.length;t++)i=r.columns[t],ej.isNullOrUndefined(n._columnsWidthCollection[t])||n.model.allowToggleColumn?n.model.allowScrolling&&n.model.scrollSettings.width!="auto"&&n.model.scrollSettings.width!="100%"&&(o=parseInt(n.element.width())-n._collapsedColumns.length*50,c=Math.round(n.getHeaderTable().css("border-spacing").split("px")[0])*(n.model.columns.length+1),!ej.isNullOrUndefined(i.width)&&($.isNumeric(r.scrollSettings.width)||$.isNumeric(i.width)||(r.scrollSettings.width.indexOf("%")>0||r.scrollSettings.width=="auto")&&i.width.indexOf("%")>0)?(v=f+c,!n.model.columns[t].isCollapsed&&v<parseInt(n.element.width())?this._columnAutoWidth(t,o,c):v>n.element.width()&&(h.eq(t).width(i.width),e.eq(t).width(i.width))):ej.isNullOrUndefined(i.width)&&this._columnAutoWidth(t,o,c),n.model.columns[t].isCollapsed&&(h.eq(t).css("width","50px"),e.eq(t).css("width","50px"),n._columnsWidthCollection[t]=50)):!ej.isNullOrUndefined(i.width)&&typeof i.width=="string"&&i.width.indexOf("%")?(a=parseFloat(i.width)/100*l,h.eq(t).width(a),e.eq(t).width(a)):(h.eq(t).width(n._columnsWidthCollection[t]),e.eq(t).width(n._columnsWidthCollection[t]))},n.prototype._getCardbyIndexes=function(n){return $(this.kanbanObj.getRowByIndex(n[0][0]).find(".e-rowcell:eq("+n[0][1][0]+")").find("div.e-kanbancard:eq("+n[0][2][0]+")"))},n.prototype._addLastRow=function(){var n=this.kanbanObj,r=n.getContentTable().find("tr:last").find("td"),i=0,t;if(n.model.allowScrolling&&!ej.isNullOrUndefined(n.model.dataSource)&&!ej.isNullOrUndefined(n._kanbanRows)){for(t=0;t<n._kanbanRows.length;t++)i+=$(n._kanbanRows[t]).height();i<n.getContent().height()-1&&r.addClass("e-lastrowcell")}},n.prototype._refreshDataSource=function(n){var t=this.kanbanObj;t._dataManager=n instanceof ej.DataManager?n:new ej.DataManager(n);t._isLocalData=!(t._dataSource()instanceof ej.DataManager)||t._dataManager.dataSource.offline||t._isRemoteSaveAdaptor;t.refresh(!0)},n.prototype._cardClick=function(n,t){var i,r=null,u;ej.isNullOrUndefined(t.attr("id"))||(u=new ej.DataManager(this.kanbanObj._currentJsonData),r=u.executeLocal((new ej.Query).where(this.kanbanObj.model.fields.primaryKey,ej.FilterOperators.equal,t.attr("id"))));i={target:n,currentCard:t,data:r};this.kanbanObj._trigger("cardClick",i)},n.prototype._validateLimit=function(n,t,i){var e=this.kanbanObj,o=this._multikeySeparation(n.key),r=$($(e.element.find(".e-columnrow")).find('td[data-ej-mappingkey="'+o+'"]')),u,s=!ej.isNullOrUndefined(n.constraints.min),h=!ej.isNullOrUndefined(n.constraints.max),f=!ej.isNullOrUndefined(e.model.fields.swimlaneKey)&&i==0;n.constraints.type=="column"?u=r.find(".e-kanbancard").length:(u=$(r[i]).find(".e-kanbancard").length,r=$(r[i]));s&&(n.constraints.min>u?(r.addClass("e-deceed"),f&&$(t).addClass("e-deceed")):(r.removeClass("e-deceed"),f&&$(t).removeClass("e-deceed")));h&&(n.constraints.max<u?(r.addClass("e-exceed"),f&&$(t).addClass("e-exceed")):(r.removeClass("e-exceed"),f&&$(t).removeClass("e-exceed")));r.find(".e-limits").addClass("e-hide");n.isCollapsed||$(t).find(".e-limits").removeClass("e-hide")},n.prototype._totalCount=function(){for(var i,n=this.kanbanObj,t=0;t<n.model.columns.length;t++)if(i=n.model.columns[t],n.model.enableTotalCount){var r=n.getHeaderContent().find("span.e-totalcount")[t],u=this._multikeySeparation(i.key),f=$($(n.element.find(".e-columnrow")).find('td[data-ej-mappingkey="'+u+'"]')),e=f.find(".e-kanbancard").length;$(r).text(e)}},n.prototype._multikeySeparation=function(n){var i=typeof n=="object"?n:n.split(","),n="",r=i.length,t;if(r==1)n=i[0];else for(t=0;t<r;t++)n=n+i[t],t!=r-1&&(n+=",");return n},n.prototype._renderLimit=function(){for(var n,f,e,t=this.kanbanObj,u={},r=0;r<t.model.columns.length;r++)if(n=t.model.columns[r],!ej.isNullOrUndefined(n.constraints)){f=!ej.isNullOrUndefined(n.constraints.min);e=!ej.isNullOrUndefined(n.constraints.max);(f||e)&&(u=t.getHeaderContent().find(".e-columnheader").not(".e-stackedHeaderRow").find(".e-headercell")[r]);var s=typeof n.key=="object"?n.key:n.key.split(","),h=t.KanbanCommon._multikeySeparation(s),o=$($(t.element.find(".e-columnrow")).find('td[data-ej-mappingkey="'+h+'"]')),i=0;ej.isNullOrUndefined(n.constraints.type)&&(n.constraints.type="column");switch(n.constraints.type){case"column":this._validateLimit(n,u,i);break;case"swimlane":for(i=0;i<o.length;i++)this._validateLimit(n,u,i),t.getHeaderContent().find(".e-headercell").eq(r).hasClass("e-shrinkcol")||o.eq(i).find(".e-limits").removeClass("e-hide");ej.isNullOrUndefined(t.model.fields.swimlaneKey)||$(u).find(".e-limits").addClass("e-hide")}}},n.prototype._showhide=function(n,t){var i=this.kanbanObj,r,w=0,c,e=i.model.columns,o,u,h,p,s;c=t==="show"?"_visibleColumns":"_hiddenColumns";var b=i.getHeaderTable().find("thead"),l=b.find("tr").not(".e-stackedHeaderRow").find(".e-headercell"),a=i.getHeaderTable().find("colgroup").find("col"),v,y=i.getContentTable().find("colgroup").find("col"),f=i._columnRows;for(r=0;r<e.length;r++)$.inArray(e[r].headerText,i[c])!=-1&&(e[r].visible=t==="show"?!0:!1,w++),v=i.getColumnByHeaderText(n[r]),o=$.inArray(v,i.model.columns),o!=-1&&(t=="show"?(l.eq(o).removeClass("e-hide"),a.eq(o).css("display","")):(l.eq(o).addClass("e-hide"),a.eq(o).css("display","none")));for(r=0;r<e.length;r++)for(u=0;u<f.length;u++)ej.isNullOrUndefined(i.model.fields.swimlaneKey)||($(f[u]).is(":visible")?f.eq(u).prev().find(".e-rowcell").attr("colspan",i.getVisibleColumnNames().length):(f.eq(u).prev().prev(".e-swimlanerow").find(".e-rowcell").attr("colspan",i.getVisibleColumnNames().length),e[r].visible?f.eq(u).prev(".e-collapsedrow").find("td.e-rowcell").eq(r).removeClass("e-hide"):f.eq(u).prev(".e-collapsedrow").find("td.e-rowcell").eq(r).addClass("e-hide"))),h=f.eq(u).find("td.e-rowcell[data-ej-mappingkey='"+i.model.columns[r].key+"']"),e[r].visible?(h.removeClass("e-hide"),y.eq(r).removeClass("e-hide")):(h.addClass("e-hide"),y.eq(r).addClass("e-hide"));for(p=i.element.find(".e-kanbanheader"),i._columnsWidthCollection=[],s=0;s<i.model.columns.length;s++)ej.isNullOrUndefined(i.model.columns[s].width)||i._columnsWidthCollection.push(i.model.columns[s].width);i.element[0].replaceChild(i._renderHeader()[0],p[0]);i.getContentTable().find("colgroup").first().replaceWith(this._getMetaColGroup());this._setWidthToColumns();i.KanbanScroll&&i.KanbanScroll._refreshScroller({requestType:"refresh"})},n.prototype._showExpandColumns=function(n,t,i,r){var u=this.kanbanObj,f,o,e;if(!ej.isNullOrUndefined(n)){if(n!=null)if($.isArray(t))for(f=0;f<t.length;f++)o=u.getColumnByHeaderText(t[f]),t[f]=o!=null?o.headerText:t[f];else t=n.headerText;if($.isArray(t))for(f=0;f<t.length;f++)e=$.inArray(t[f],u[i]),e!=-1?(u[i].splice(e,1),u[r].push(t[f])):e!=-1||$.inArray(t[f],u[r])!=-1||ej.isNullOrUndefined(u.getColumnByHeaderText(t[f]))||(u[r].push(u.getColumnByHeaderText(t[f]).key)&&u[r].push(t[f]),u[i].splice($.inArray(u.getColumnByHeaderText(t[f]).key,u[i]),1)&&u[i].splice($.inArray(t[f],u[i]),1));else e=$.inArray(t,u[i]),e!=-1?(u[i].splice(e,1),u[r].push(t)):e!=-1||$.inArray(t,u[r])!=-1||ej.isNullOrUndefined(u.getColumnByHeaderText(t))||(u[r].push(u.getColumnByHeaderText(t).key)&&u[r].push(t),u[i].splice($.inArray(u.getColumnByHeaderText(t).key,u[i]),1)&&u[i].splice($.inArray(t,u[i]),1))}},n.prototype._expandColumns=function(n){var i,r="_expandedColumns",t=this.kanbanObj;i=typeof n=="string"?t.getColumnByHeaderText(n):t.getColumnByHeaderText(n[0]);this._showExpandColumns(i,n,"_collapsedColumns",r);this._expandCollapse(t[r],"expand");t.model.allowScrolling&&this._setWidthToColumns();t.model.stackedHeaderRows.length>0&&this._refreshStackedHeader()},n.prototype._toggleField=function(n){if(!ej.isNullOrUndefined(n)){var r,t,i=this.kanbanObj;if(i.model.allowToggleColumn){if($.isArray(n))for(t=0;t<n.length;t++)r=$.inArray(n[t],i._collapsedColumns),r!=-1?this._expandColumns(n[t]):this._collapseColumns(n[t]);else $.inArray(n,i._collapsedColumns)!=-1?this._expandColumns(n):this._collapseColumns(n);i.KanbanScroll&&i.KanbanScroll._refreshScroller({requestType:"refresh"})}}},n.prototype._hideCollapseColumns=function(n,t,i,r){var f,e,u=this.kanbanObj;if(!ej.isNullOrUndefined(n))if($.isArray(t))for(f=0;f<t.length;f++)e=$.inArray(t[f],u[r]),e!=-1?(u[i].push(t[f]),u[r].splice(e,1)):e!=-1||$.inArray(t[f],u[i])!=-1||ej.isNullOrUndefined(u.getColumnByHeaderText(t[f]))||(u[i].push(u.getColumnByHeaderText(t[f]).key)&&u[i].push(u.getColumnByHeaderText(t[f]).key),u[r].splice($.inArray(u.getColumnByHeaderText(t[f]).key,u[r]),1)&&u[r].splice($.inArray(t[f],u[r]),1));else e=$.inArray(t,u[r]),e!=-1?(u[i].push(t),u[r].splice(e,1)):e!=-1||r!=r||$.inArray(t,u[i])!=-1||ej.isNullOrUndefined(u.getColumnByHeaderText(t))||(u[i].push(u.getColumnByHeaderText(t).key)&&u[i].push(u.getColumnByHeaderText(t).key),u[r].splice($.inArray(u.getColumnByHeaderText(t).key,u[r]),1)&&u[r].splice($.inArray(t,u[r]),1))},n.prototype._expandCollapse=function(n,t){var i=this.kanbanObj,f,w=0,y,l=i.model.columns,u,h,v,e,c,k;y=t==="expand"?"_expandedColumns":"_collapsedColumns";var b=i.getHeaderTable().find("thead"),o=b.find("tr").not(".e-stackedHeaderRow").find(".e-headercell"),s=i.getHeaderTable().find("colgroup").find("col"),a,r,p=i.getContentTable().find("colgroup").find("col");for(f=0;f<l.length;f++)if($.inArray(l[f].headerText,i[y])!=-1&&(l[f].isCollapsed=t==="expand"?!1:!0,w++),a=i.getColumnByHeaderText(n[f]),u=$.inArray(a,i.model.columns),h=t=="expand"?!0:!1,u!=-1&&(h?s.eq(u).hasClass("e-shrinkcol")&&(o.eq(u).find(".e-headercelldiv,.e-totalcard,.e-limits").removeClass("e-hide"),o.eq(u).removeClass("e-shrinkcol"),s.eq(u).removeClass("e-shrinkcol"),p.eq(u).removeClass("e-shrinkcol"),o.eq(u).find(".e-clcollapse").addClass("e-clexpand").removeClass("e-clcollapse")):s.eq(u).hasClass("e-shrinkcol")||(o.eq(u).find(".e-headercelldiv,.e-totalcard,.e-limits").addClass("e-hide"),o.eq(u).addClass("e-shrinkcol"),s.eq(u).addClass("e-shrinkcol"),p.eq(u).addClass("e-shrinkcol"),o.eq(u).find(".e-clexpand").addClass("e-clcollapse").removeClass("e-clexpand")),o.eq(u).attr("aria-expanded",h),o.eq(u).find(".e-icon").attr("aria-label",a.headerText+" "+t),o.eq(u).find(".e-icon").attr("tab-index","0")),v=i.model.columns[f].isCollapsed,h){if(!v)for(e=0;e<i._columnRows.length;e++)if(r=i._columnRows.eq(e).find("td.e-rowcell").eq(f),r.hasClass("e-shrink"))r.removeClass("e-shrink").find(".e-shrinkheader").addClass("e-hide").parent().find(".e-kanbancard").removeClass("e-hide"),r.hasClass("e-toggle")&&(c=r.find(".e-toggle-area")),$(c).removeClass("e-hide").show(),i.element.hasClass("e-responsive")&&r.find(".e-cell-scrollcontent").length>0&&r.find(".e-cell-scrollcontent").removeClass("e-hide"),i.model.enableRTL&&r.find(".e-shrinkheader").css({position:"",top:""}),r.find(".e-customaddbutton").removeClass("e-hide"),r.find(".e-limits").removeClass("e-hide"),r.find(".e-shrinkcount").text(i._columnCardcount(i.currentViewData,i.model.columns[f].key,i.model.fields.swimlaneKey?e:null,i));else break}else if(v)for(e=0;e<i._columnRows.length;e++)if(r=i._columnRows.eq(e).find("td.e-rowcell").eq(f),r.length>0&&!r.hasClass("e-shrink"))r.addClass("e-shrink").find(".e-shrinkheader").removeClass("e-hide").parent().find(".e-kanbancard").addClass("e-hide"),r.hasClass("e-toggle")&&(c=r.find(".e-toggle-area")),$(c).addClass("e-hide").hide(),i._checkMultikey(r)&&r.addClass("e-shrink").find(".e-shrinkheader").show(),i.element.hasClass("e-responsive")&&r.find(".e-cell-scrollcontent").length>0&&r.find(".e-cell-scrollcontent").addClass("e-hide"),k=r.offset().top,r.find(".e-customaddbutton").addClass("e-hide"),r.find(".e-limits").addClass("e-hide"),r.find(".e-shrinkcount").text(i._columnCardcount(i.currentViewData,i.model.columns[f].key,i.model.fields.swimlaneKey?e:null,i));else break},n.prototype._collapseColumns=function(n){for(var t=this.kanbanObj,r,u,f="_collapsedColumns",e=0,i=0;i<n.length;i++)r=$.inArray(n[i],t._expandedColumns),r!=-1&&e++;t._expandedColumns.length==e||t.getVisibleColumnNames().length-t._collapsedColumns.length==1||t.model.columns.length-t._collapsedColumns.length==1?t._collapsedColumns.length>0&&(this._expandColumns(t._collapsedColumns[0]),this._collapseColumns(n)):(u=typeof n=="string"?t.getColumnByHeaderText(n):t.getColumnByHeaderText(n[0]),this._hideCollapseColumns(u,n,f,"_expandedColumns"),this._expandCollapse(t[f],"collapse"),t.model.allowScrolling&&this._setWidthToColumns(),t.model.stackedHeaderRows.length>0&&this._refreshStackedHeader())},n}();window.ej.createObject("ej.KanbanFeatures.Common",s,window);h=this&&this.__extends||function(n,t){function r(){this.constructor=n}for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)},function(n){var t=function(t){function i(i,r){return t.call(this),this.element=null,this.PluginName="ejKanban",this.id="null",this.validTags=["div"],this.observables=["dataSource"],this._tags=[{tag:"columns",attr:["headerTemplate","headerText","key","isCollapsed","showAddButton","visible","constraints.type","constraints.min","constraints.max","allowDrag","allowDrop","totalCount.text"]},{tag:"workflows",attr:["key","allowedTransitions"]},{tag:"stackedHeaderRows",attr:[[{tag:"stackedHeaderColumns",attr:["headerText","column"]}]]},{tag:"contextMenuSettings",attr:["customMenuItems","menuItems","disableDefaultItems"]},{tag:"filterSettings",attr:["text","query","description"]},{tag:"editSettings.editItems",attr:["field","editType","validationRules","editParams","defaultValue"]},{tag:"swimlaneSettings.headers",attr:["text","key"]}],this.localizedLabels=null,this.currentViewData=null,this.keyConfigs={focus:"e",insertCard:"45",deleteCard:"46",editCard:"113",saveRequest:"13",cancelRequest:"27",firstCardSelection:"36",lastCardSelection:"35",upArrow:"38",downArrow:"40",rightArrow:"39",leftArrow:"37",swimlaneExpandAll:"ctrl+40",swimlaneCollapseAll:"ctrl+38",selectedSwimlaneExpand:"alt+40",selectedSwimlaneCollapse:"alt+38",selectedColumnCollapse:"ctrl+37",selectedColumnExpand:"ctrl+39",multiSelectionByUpArrow:"shift+38",multiSelectionByDownArrow:"shift+40",multiSelectionByLeftArrow:"shift+37",multiSelectionByRightArrow:"shift+39"},this.dataTypes={dataSource:"data",query:"data",columns:"array",stackedHeaderRows:"array",contextMenuSettings:{disableDefaultItems:"array",menuItems:"array",customMenuItems:"array"},filterSettings:"array",editSettings:{editMode:"enum",editItems:"array"},searchSettings:{fields:"array"},swimlaneSettings:{headers:"array"}},this.defaults={dataSource:null,keyField:null,keySettings:null,allowTitle:!1,cssClass:"",allowSelection:!0,allowSearching:!1,allowToggleColumn:!1,enableTotalCount:!1,enableTouch:!0,selectionType:"single",allowKeyboardNavigation:!1,allowDragAndDrop:!0,allowExternalDragAndDrop:!1,allowHover:!0,allowScrolling:!1,allowPrinting:!1,enableRTL:!1,showColumnWhenEmpty:!1,stackedHeaderRows:[],filterSettings:[],scrollSettings:{width:"auto",height:0,allowFreezeSwimlane:!1},swimlaneSettings:{showCount:!0,allowDragAndDrop:!1,unassignedGroup:{enable:!0,keys:["null","undefined",""]},showEmptySwimlane:!1,headers:[]},fields:{content:null,tag:null,color:null,imageUrl:null,swimlaneKey:null,primaryKey:null,priority:null},cardSettings:{colorMapping:{},template:null,externalDropTarget:null},columns:[],contextMenuSettings:{enable:!1,menuItems:["Add Card","Edit Card","Delete Card","Top of Row","Bottom of Row","Move Up","Move Down","Move Left","Move Right","Move to Swimlane","Hide Column","Visible Columns","Print Card"],customMenuItems:[]},customToolbarItems:[],editSettings:{editItems:[],allowEditing:!1,allowAdding:!1,dialogTemplate:null,externalFormTemplate:null,formPosition:"bottom",editMode:"dialog"},searchSettings:{fields:[],key:"",operator:"contains",ignoreCase:!0},tooltipSettings:{enable:!1,template:null},minWidth:0,isResponsive:!1,locale:"en-US",query:null,create:null,actionBegin:null,actionComplete:null,actionFailure:null,load:null,destroy:null,beginEdit:null,endEdit:null,endAdd:null,endDelete:null,beforeCardSelect:null,cardSelect:null,toolbarClick:null,cardDoubleClick:null,cardDragStart:null,cardDrag:null,cardDragStop:null,cardDrop:null,contextClick:null,contextOpen:null,cardClick:null,beforePrint:null,cellClick:null,headerClick:null,dataBound:null,queryCellInfo:null},this._dataSource=ej.util.valueFunction("dataSource"),this._rootCSS="e-kanban",this._requiresID=!0,this._id=null,this.KanbanDragAndDrop=null,this.KanbanEdit=null,this.KanbanCommon=null,this.KanbanAdaptive=null,this.KanbanScroll=null,this.KanbanContext=null,this.KanbanSwimlane=null,this.KanbanSelection=null,this.KanbanFilter=null,this._currentJsonData=null,this._kanbanRows=null,this._columnRows=null,this._swimlaneRows=null,this._filterToolBar=null,this._filteredRecordsCount=0,this._filteredRecords=null,this._contexttarget=null,this._editForm=null,this._newData=null,this._isAddNew=!1,this._isRemoteSaveAdaptor=!1,this._queryPromise=null,this._kanbanWidth=null,this.keyPredicates=null,this._cloneQuery=null,this._isLocalData=!0,this._previousRowCellIndex=[],this.selectedRowCellIndexes=[],this._bulkUpdateData=[],this._bulkPriorityData=[],this._kbnFilterObject=[],this._kbnAdaptFilterObject=[],this._kbnFilterCollection=[],this._kbnAdaptDdlData=[],this._kbnSwimLaneData=[],this._headerWidth=[],this._kbnAdaptDdlIndex=0,this._kbnSwipeWidth=0,this._kbnSwipeCount=0,this._extraWidth=0,this._searchTout=null,this._cardSelect=null,this._kbnBrowserContext=null,this._kbnMouseX=null,this._kbnAutoFilterCheck=!1,this._autoKbnSwipeLeft=!1,this._autoKbnSwipeRight=!1,this._kbnDdlWindowResize=!1,this._conmenu=null,this._rowIndexesColl=[],this.templates={},this.initialRender=!1,this._columnsWidthCollection=[],this._slTemplate="",this._cardTemplate="",this._tdsOffsetWidth=[],this._scrollObject=null,this._templateRefresh=!1,this._action="",this._hiddenColumns=[],this._visibleColumns=[],this._filterCollection=[],this.collapsedColumns=null,this._expandedColumns=null,this._headerColumnNames=null,this._isWatermark=null,this._searchInput=null,this._hiddenSpan=null,this._currentRowCellIndex=[],this._recordsCount=0,this._dropinside=!1,this._selectedCards=[],this._selectedCardData=[],this._tableBEle=null,this._saveArgs=null,this._cModifiedData=null,this._dropped=null,this._cAddedRecord=null,this._externalObj=null,this._externalDrop=!1,this._externalData=[],this._cDeleteData=null,this._isAddNewClick=!1,this._isEdit=!1,this._currentData=null,this._newCard=null,this._cardEditClick=null,this._collapsedCards=[],this._collapsedSwimlane=[],this._keyValue=[],this._dblArgs=null,this._freezeSwimlaneRow={},this._freezeScrollTop=0,this._freezeSlOrder=0,this._originalWidth=null,this._originalScrollWidth=0,this._collapsedColumns=[],this._enableMultiTouch=!1,this._enableSwimlaneCount=!0,this._kTouchBar=null,this._contextSwimlane=null,this._slText=null,this._slKey=null,this._initialData=null,this._searchBar=null,this._originalScrollHeight=null,this._dataManager=null,this._priorityCollection=[],this._initialKanbanModel=[],this._kbnAdaptEditClickHandler=function(t){if(this.element.hasClass("e-responsive")){var i=n(t.target);(i.attr("id")==this._id+"_Cancel"||i.parent().attr("id")==this._id+"_closebutton")&&this.KanbanEdit.cancelEdit();i.attr("id")==this._id+"_Save"&&this.KanbanEdit.endEdit()}},this._keyPressed=function(n,t,i,r){var u;return t.classList.contains("e-ejinputtext")||(u=this.KanbanCommon._kanbanKeyPressed(n,t,i,r)),u},this._freezeSwimlane=function(n){ej.isNullOrUndefined(n.scrollLeft)||(this.getHeaderContent().find("div").first().scrollLeft(n.scrollLeft),this.getContent().find(".e-content").first().scrollLeft(n.scrollLeft));this.KanbanSwimlane&&this.KanbanSwimlane._freezeRow(n,this)},this._getToggleCard=function(t,i){var r=this.getRsc("helpers",i);return r._collapsedCards&&n.inArray(t[r.model.fields.primaryKey].toString(),r._collapsedCards)!==-1?!0:!1},this.getCurrentJsonData=function(){return this._currentJsonData},i&&(this._id=i[0].id,i.jquery||(i=n("#"+i)),i.length)?n(i).ejKanban(r).data(this.PluginName):void 0}return h(i,t),i.prototype.getHeaderTable=function(){return this.headerTable},i.prototype.setHeaderTable=function(n){this.headerTable=n},i.prototype.getColumnByHeaderText=function(n){var i,t;if(!ej.isNullOrUndefined(n)){for(i=this.model.columns,t=0;t<i.length;t++)if(i[t].headerText==n)break;return t==i.length?null:i[t]}},i.prototype._getColumnKeyIndex=function(n){for(var r,i=this.model.columns,u,t=0;t<i.length;t++)for(u=typeof i[t].key=="object"?i[t].key:i[t].key.split(","),r=0;r<u.length;r++)if(u[r]==n)return t;return t==i.length?null:i[t]},i.prototype._checkMultikey=function(n){var i=n.index(),t;return t=this.model.columns[i].key,t=typeof t=="object"?t:t.split(","),t.length>1?!0:!1},i.prototype._destroy=function(){var r,t,u,i;if(this.element.off(),this.element.find(".e-kanbanheader").find(".e-headercontent").add(this.getContent().find(".e-content")).off("scroll"),ej.isNullOrUndefined(this._filterToolBar)||this._filterToolBar.ejToolbar("destroy"),!ej.isNullOrUndefined(this._editForm)){for(r=this._editForm.find(".e-field"),i=0;i<r.length;i++)t=n(r[i]),t.hasClass("e-ejinputtext")&&this.element.find(".e-ejinputtext").remove(),t.hasClass("e-kanbantextarea")&&this.element.find(".e-kanbantextarea").remove(),t.hasClass("e-dropdownlist")&&t.ejDropDownList("destroy"),t.hasClass("e-numerictextbox")&&t.ejNumericTextbox("destroy"),u=t.parent("div").find("textarea.e-rte"),u.length&&(u.ejRTE("destroy"),t.parent().find("textarea").remove());n("#"+this._id+"_dialogEdit").ejDialog("destroy")}this.element.ejWaitingPopup("destroy");this.model.contextMenuSettings.enable&&(n("#"+this._id+"_Context").ejMenu("destroy"),n("#"+this._id+"_Context").remove());this.element.children().remove();this.element.removeClass("e-kanban e-widget e-onlycontent").removeAttr("accesskey");this.model.isResponsive&&n(window).off("resize",n.proxy(this.kanbanWindowResize,this))},i.prototype._menu=function(n){this.KanbanContext&&this.KanbanContext._kanbanMenu(n,this)},i.prototype._setModel=function(n){this.KanbanCommon._kanbanSetModel(n,this)},i.prototype._onToolbarClick=function(t){var r=n(this.itemsContainer).closest(".e-kanban"),i=t.type=="keyup"?this:r.data("ejKanban");i.KanbanCommon._kanbanToolbarClick(t,i)},i.prototype._showToolTip=function(t){var i=this,s,h,e,o;if(i.model.tooltipSettings.enable){if(n(t.target).hasClass("e-kanbantooltip"))return;if(e=i.element.find(".e-kanbantooltip"),o=ej.isNullOrUndefined(i.model.tooltipSettings.template),o&&!(n(t.target).hasClass("e-tag")||n(t.target).hasClass("e-text")||n(t.target).closest(".e-primarykey").length>0))return;o?e.html(n(t.target).text()).removeClass("e-tooltiptemplate"):(e.addClass("e-tooltiptemplate"),h=new ej.DataManager(i._currentJsonData),s=h.executeLocal((new ej.Query).where(i.model.fields.primaryKey,ej.FilterOperators.equal,n(t.currentTarget).attr("id"))),e.html(n(i.model.tooltipSettings.template).render(s[0])));var u=ej.isNullOrUndefined(t.originalEvent)?t.originalEvent.clientX:t.pageX,f=ej.isNullOrUndefined(t.originalEvent)?t.originalEvent.clientY:t.pageY,r=n(i.element).find(".e-kanbantooltip");u=u+r.width()<n(i.element).width()?u:u-r.width();f=f+r.height()<n(i.element).height()?f:f-r.height();r.css("left",u);r.css("top",f);i.model.enableRTL==!0&&r.addClass("e-rtl");n(i.element).find(".e-kanbantooltip").show()}},i.prototype._hideToolTip=function(){this.model.tooltipSettings.enable&&this.element.find(".e-kanbantooltip").hide()},i.prototype.showColumns=function(n){var t,i="_visibleColumns";t=typeof n=="string"?this.getColumnByHeaderText(n):this.getColumnByHeaderText(n[0]);this.KanbanCommon._showExpandColumns(t,n,"_hiddenColumns",i);this.KanbanCommon._showhide(this[i],"show");this.KanbanCommon._renderLimit();this.KanbanCommon._totalCount();this.model.stackedHeaderRows.length>0&&this.KanbanCommon._refreshStackedHeader()},i.prototype.print=function(n){var i={},t,o,s,r,n,u,f,e;i.requestType="print";this._trigger("actionBegin",i);ej.isNullOrUndefined(this.element.find("#"+this._id+"_externalEdit"))||this.element.find("#"+this._id+"_externalEdit").css("display","none");t=this.element.clone();t.find(".e-kanbantouchbar").remove();this.model.allowScrolling&&(o=this.model.scrollSettings.width,s=this.model.scrollSettings.height,(this.getScrollObject().isVScroll()||this.getScrollObject().isHScroll())&&(r=this.getContent().find(".e-content")[0],t.find(".e-kanbancontent").height(r.scrollHeight),t.find(".e-kanbancontent").ejScroller({width:r.scrollWidth,height:r.scrollHeight}),t.width(r.scrollWidth)));(!ej.isNullOrUndefined(this.model.filterSettings)||this.model.allowSearching||this.model.allowPrinting||!ej.isNullOrUndefined(this.model.customToolbarItems))&&t.find(".e-kanbantoolbar").remove();t.find(".e-kanbancontent div:first").nextAll().remove();n&&((typeof n=="string"||typeof n=="number")&&(n=this.element.find("div.e-kanbancard[id="+n+"]")),t.find(".e-kanbanheader").remove(),u=n.parent().clone(),f=n.clone(),u.children().remove(),t.find('table[data-role="kanban"]').remove(),t.find(".e-kanbancontent").children().append(u.append(f)),t.css("border-style","none"),this.model.allowScrolling?(t.css({width:"auto",height:"auto"}),t.find(".e-kanbancontent").css({width:"auto",height:"auto"})):t.find(".e-kanbancard").css("width","30%"));e=window.open("","print","height=452,width=1024,tabbar=no");i={requestType:"print",element:t};this._trigger("beforePrint",i);ej.isNullOrUndefined(i.element)||(t=i.element);ej.print(t,e);this._trigger("actionComplete",i)},i.prototype._kbnAdaptClickHandler=function(n){this.KanbanAdaptive&&this.KanbanAdaptive._adaptiveKbnClick(n)},i.prototype._kbnTouchEndHandler=function(){var n=this.element.find(".e-draggedcard");n.length>0&&n.remove();this._cardSelect="null"},i.prototype._kbnTouchClick=function(t){if(this.model.selectionType=="multiple"&&this._kTouchBar.is(":visible")&&!n(t.target).hasClass("e-cardselection")&&n(t.target).parents(".e-cardselection").length<=0&&!n(t.target).hasClass("e-cardtouch")&&n(t.target).parents(".e-cardtouch").length<=0&&!this._kTouchBar.find(".e-cardtouch").hasClass("e-spanclicked")&&this._kTouchBar.hide(),t.type=="touchstart"&&(this._cardSelect="touch"),this.model.allowSearching&&!this.element.hasClass("e-responsive")){var i=this.element.find(".e-searchbar.e-ul");n(t.target).hasClass("e-searchitem")&&n(t.target).hasClass("e-cancel")||n(t.target).parents(".e-searchdiv").length>0&&n(t.target).parents(".e-searchbar").find(".e-cancel").length>0?i.addClass("e-highliht-kbnsearchbar"):n(t.target).hasClass("e-searchitem")&&n(t.target).hasClass("e-searchfind")&&i.removeClass("e-highliht-kbnsearchbar")}},i.prototype._kbnHoldHandler=function(t){if(this.model.enableTouch&&(n(t.target).hasClass("e-kanbancard")||n(t.target).parents(".e-kanbancard").length>0)&&t.type=="taphold"&&t.pointerType=="touch"){this._cardSelect="hold";var r=n(t.target),i=null;n(t.target).parents(".e-kanbancard").length>0&&(r=n(t.target).parents(".e-kanbancard"));this.KanbanDragAndDrop&&(i=this.KanbanDragAndDrop._createCardClone(i,r),i.css({position:"absolute",top:t.originalEvent.changedTouches[0].pageY,left:t.originalEvent.changedTouches[0].pageX}),i.addClass("e-left-rotatecard"))}},i.prototype._swipeKanban=function(n){if(this.element.hasClass("e-responsive")&&this.element.find(".e-targetclone").length==0)switch(n.type){case"swipeleft":this.KanbanAdaptive._kbnLeftSwipe();break;case"swiperight":this.KanbanAdaptive._kbnRightSwipe()}},i.prototype._wireEvents=function(){if(this._on(this.element,n.isFunction(n.fn.tap)&&this.model.enableTouch?"tap":"click","",this._clickHandler),this._on(n("#"+this._id+"_searchbar"),"keyup","",this._onToolbarClick),this._on(n(document),"click touchstart","",this._kbnTouchClick),this._on(this.element,"taphold","",this._kbnHoldHandler),this._on(this.element,"touchend","",this._kbnTouchEndHandler),this._on(this.element,n.isFunction(n.fn.doubletap)&&this.model.enableTouch?"doubletap":"dblclick",".e-kanbancard",this._cardDblClickHandler),this.KanbanAdaptive&&this._on(this.element,"swipeleft swiperight",".e-kanbancontent",n.proxy(this._swipeKanban,this)),this.KanbanContext&&this._on(this.element,"contextmenu","",this.KanbanContext._kbnBrowserContextMenu),this.KanbanFilter&&this._on(n("#"+this._id+"_searchbar"),"keypress","",this.KanbanFilter._onToolbarKeypress),this.KanbanEdit){if(ej.browserInfo().name=="msie"&&parseInt(ej.browserInfo().version,10)==8)n(window).on("resize",n.proxy(this.kanbanWindowResize,this));this._enableEditingEvents()}this._enableCardHover();this.model.tooltipSettings.enable&&(this._on(this.element,"mouseover",".e-kanbancard",this._showToolTip),this._on(this.element,"mouseout",".e-kanbancard",this._hideToolTip));this.model.allowKeyboardNavigation&&(this.element[0].tabIndex=this.element[0].tabIndex==-1?0:this.element[0].tabIndex,this.element[0].accessKey=!ej.isNullOrUndefined(this.element[0].accessKey)&&this.element[0].accessKey!=""?this.element[0].accessKey:"e",this._on(this.element,"keyup","",undefined))},i.prototype._enableEditingEvents=function(){this.model.editSettings.allowAdding?this._on(this.element,"dblclick doubletap",".e-kanbancontent .e-columnrow .e-rowcell",this._cellDblClickHandler):this._off(this.element,"dblclick doubletap",".e-kanbancontent .e-columnrow .e-rowcell");this.model.editSettings.allowEditing||this.model.editSettings.allowAdding?this._on(n("#"+this._id+"_dialogEdit"),"click keypress","#EditDialog_"+this._id+"_Save ,#EditDialog_"+this._id+"_Cancel",this._clickHandler):this._off(n("#"+this._id+"_dialogEdit"),"click","#EditDialog_"+this._id+"_Save ,#EditDialog_"+this._id+"_Cancel")},i.prototype._cardDblClickHandler=function(t){this._dblArgs=t;var r,e=this.model.fields.primaryKey,u,f,i=new ej.Query;if(r=n(t.target).closest(".e-kanbancard"),u=r.attr("id"),f=new ej.DataManager(this._currentJsonData),i=i.where(e,ej.FilterOperators.equal,u),this._currentData=f.executeLocal(i),this._dblArgs.data=this._currentData[0],this._trigger("cardDoubleClick",this._dblArgs),!this._isEdit){if(this._cardEditClick=!0,!ej.isNullOrUndefined(this._cardEditClick)&&this._cardEditClick&&this._dblArgs.cancel){this._cardEditClick=null;return}this.model.editSettings.allowEditing&&this.KanbanEdit.startEdit(n(t.target).closest(".e-kanbancard"))}},i.prototype._cellDblClickHandler=function(t){n(t.target).hasClass("e-rowcell")&&(this._isAddNewClick=!0,this._newCard=n(t.target),this.KanbanEdit.addCard())},i.prototype._enableCardHover=function(){this.model.allowHover?this._on(this.element,"mouseenter mouseleave",".e-columnrow .e-kanbancard",this._cardHover):this._off(this.element,"mouseenter mouseleave",".e-columnrow .e-kanbancard")},i.prototype.refreshColumnConstraints=function(){this.KanbanCommon._renderLimit();this.KanbanSwimlane&&this._enableSwimlaneCount&&this.KanbanSwimlane._swimlaneLimit();this._enableSwimlaneCount=!1},i.prototype.hideColumns=function(t){for(var r,u=0,f,e="_hiddenColumns",i=0;i<this._visibleColumns.length;i++)r=n.inArray(this._collapsedColumns[i],this._visibleColumns),r!=-1&&u++;if(this._visibleColumns.length-1>u)f=typeof t=="string"?this.getColumnByHeaderText(t):this.getColumnByHeaderText(t[0]),this.KanbanCommon._hideCollapseColumns(f,t,e,"_visibleColumns"),this.KanbanCommon._showhide(this[e],"hide"),this.model.stackedHeaderRows.length>0&&this.KanbanCommon._refreshStackedHeader();else{if(this._visibleColumns.length==1)return;this._visibleColumns[0]==t?this.KanbanCommon._expandColumns(this._visibleColumns[1]):this.KanbanCommon._expandColumns(this._visibleColumns[0]);this.hideColumns(t)}this.KanbanCommon._renderLimit();this.KanbanCommon._totalCount();this.KanbanCommon._stackedHeadervisible()},i.prototype._cardHover=function(t){var u=n(t.target),r=n(this._kanbanRows),i=n(u).closest(".e-kanbancard");return i.length<=0?!1:(this.model.allowHover&&(t.type=="mouseenter"?ej.isNullOrUndefined(r)||i.index()!=-1&&i.addClass("e-hover"):r.find(".e-kanbancard").removeClass("e-hover")),!1)},i.prototype.toggleCard=function(t){var i,r;if(typeof t=="string"||typeof t=="number")i=this.element.find("div.e-kanbancard[id="+t+"]"),this.KanbanCommon._toggleCardByTarget(n(i).find(".e-cardheader .e-icon"));else if(typeof t=="object"&&t[0].nodeName!="DIV")for(r=0;r<t.length;r++)i=this.element.find("div.e-kanbancard[id="+t[r]+"]"),this.KanbanCommon._toggleCardByTarget(n(i).find(".e-cardheader .e-icon"));else this.KanbanCommon._toggleCardByTarget(t)},i.prototype.toggleColumn=function(t){var f,r,e,i,u,h,o,s;if(typeof t=="string"||"object"&&t[0].nodeName!="DIV"&&t[0].nodeName!="TD")this.KanbanCommon._toggleField(t);else if(t[0].nodeName=="TD")this.KanbanCommon._toggleField(t.closest(".e-shrink").find(".e-shrinkheader").text().split("[")[0]);else if(h=this.model.columns,this.model.stackedHeaderRows.length&&(u=this.model.stackedHeaderRows[0].stackedHeaderColumns),e=t.closest(".e-headercell").not(".e-stackedHeaderCell"),r=this.element.find(".e-columnheader").not(".e-stackedHeaderRow").find(".e-hide"),e.length>0)this.KanbanCommon._toggleField(h[e.index()].headerText);else if(t.closest(".e-shrink").find(".e-shrinkheader").length>0)for(i=0;i<r.length;i++)o=n(r[i]).find(".e-headerdiv").text().split("[")[0],o==t.closest(".e-shrink").find(".e-shrinkheader").text().split("[")[0]&&(n(r[i]).next().find(".e-clcollapse").addClass("e-clexpand"),n(r[i]).next().find(".e-clexpand").removeClass("e-clcollapse"),this.KanbanCommon._toggleField(o));else if(t.closest(".e-stackedHeaderRow").length>0)for(f=t.closest(".e-stackedHeaderCell"),i=0;i<u.length;i++)u[i].headerText==n(f).text()&&(f.hasClass("e-collapse")?this.element.find(".e-stackedHeaderCell").eq(i).removeClass("e-collapse"):this.element.find(".e-stackedHeaderCell").eq(i).addClass("e-collapse"),this.KanbanCommon._toggleField(u[i].column.split(",")));this.element.find(".e-targetdragclone").is(":visible")&&(s=this.element.find(".e-targetdragclone").next(".e-kanbancard").eq(0),s.length>0?this.element.find(".e-targetdragclone").width(s.width()):this.element.find(".e-targetdragclone").width(""));this.model.allowScrolling&&this.model.allowToggleColumn&&this.KanbanCommon._setWidthToColumns()},i.prototype._toggleKey=function(n){var i=n.parents(".e-rowcell"),f=i.find(".e-toggle-cards"),r=i.find(".e-toggle-icon").children(),e=i.find(".e-toggle-key"),u=i.find(".e-toggle-cards .e-togglekey"),o,t;if(r.hasClass("e-icon e-toggle-expand"))for(f.removeClass("e-hide"),e.html(this.localizedLabels.Hide),r.removeClass("e-toggle-expand").addClass("e-toggle-collapse"),o=u.length,t=0;t<o;t++)u.eq(t).children().hasClass("e-kanbancard")||u.eq(t).hide();else f.addClass("e-hide"),e.html(this.localizedLabels.Show),r.removeClass("e-toggle-collapse").addClass("e-toggle-expand");this.KanbanScroll&&this.KanbanScroll._refreshScroller({requestType:"refresh"})},i.prototype._clickevent=function(n){this.KanbanContext&&this.KanbanContext._kanbanContextClick(n,this)},i.prototype._clickHandler=function(t){var i=n(t.target),r,e=i.hasClass("e-rowcell")?i.index():i.closest(".e-rowcell").index(),u=this.getIndexByRow(i.closest("tr.e-columnrow")),f;r=i.closest(".e-kanbancard");(n(t.target).hasClass("e-customaddbutton")||n(t.target).parents(".e-customaddbutton").length>0)&&(this._isAddNewClick=!0,this._newCard=n(t.target).parents(".e-rowcell"),this.KanbanEdit&&this.KanbanEdit.addCard());(i.hasClass("e-cardexpand")||i.hasClass("e-cardcollapse")||i.hasClass("e-expandcollapse"))&&this.toggleCard(i);(i.closest(".e-clexpand").length||i.closest(".e-clcollapse").length||i.closest(".e-shrink").length||i.closest(".e-stackedHeaderRow").length)&&this.model.allowToggleColumn&&this.toggleColumn(i);!ej.isNullOrUndefined(this.model.fields.swimlaneKey)&&(i.hasClass("e-slexpandcollapse")||i.hasClass("e-slexpand")||i.hasClass("e-slcollapse"))&&this.KanbanSwimlane.toggle(i);(i.hasClass("e-toggle-header")||i.parents(".e-toggle-header").length>0)&&this._toggleKey(i);this.model.allowSelection&&this.KanbanSelection&&r.length>0&&i.closest(".e-expandcollapse").length<=0&&(f=n(r.parent()).children().eq(0).hasClass("e-limits")?r.index()-1:r.index(),this.model.selectionType=="single"?this.KanbanSelection._cardSelection([[u,[e],[f]]],r,t):this.model.selectionType=="multiple"&&(t.shiftKey||t.ctrlKey||this._enableMultiTouch?(this._currentRowCellIndex.length==0||u==this._currentRowCellIndex[0][0])&&this.KanbanSelection._cardSelection([[u,[e],[f]]],r,t):this.KanbanSelection._cardSelection([[u,[e],[f]]],r,t)));r.length>0&&this.KanbanCommon._cardClick(i,r);(i.attr("id")==this._id+"_Cancel"||i.parent().attr("id")==this._id+"_closebutton")&&this.KanbanEdit.cancelEdit();i.attr("id")==this._id+"_Save"&&this.KanbanEdit.endEdit();i.hasClass("e-cardtouch")&&(i.hasClass("e-spanclicked")?(i.removeClass("e-spanclicked"),this._enableMultiTouch=!1,this._kTouchBar.hide()):(i.addClass("e-spanclicked"),this._enableMultiTouch=!0));this.KanbanCommon._kbnHeaderAndCellEvents(i)},i.prototype.getRowByIndex=function(t){if(!ej.isNullOrUndefined(t))return n(this._columnRows[t])},i.prototype.getScrollObject=function(){return(this._scrollObject==null||ej.isNullOrUndefined(this._scrollObject.model))&&(this._scrollObject=this.getContent().ejScroller("instance")),this._scrollObject},i.prototype.getIndexByRow=function(t){return n(this._columnRows).index(t)},i.prototype._initialize=function(){},i.prototype._initPrivateProperties=function(){this.currentViewData=null;this._filterCollection=[];this._collapsedColumns=[];this._expandedColumns=[];this._hiddenColumns=[];this._visibleColumns=[];this._columnsWidthCollection=[];this._dataManager=this._dataSource()instanceof ej.DataManager?this._dataSource():this._dataSource()!=null?new ej.DataManager(this._dataSource()):null;this._originalWidth=this.element.width();this._recordsCount=this._dataSource()!==null?this._dataSource().length:0;ej.Kanban.Locale["default"]=ej.Kanban.Locale["en-US"]={EmptyCard:"No cards to display",SaveButton:"Save",CancelButton:"Cancel",EditFormTitle:"Details of ",AddFormTitle:"Add New Card",SwimlaneCaptionFormat:"- {{:count}}{{if count == 1 || count == 0 }} item {{else}} items {{/if}}",FilterSettings:"Filters:",FilterOfText:"of",Max:"Max",Min:"Min",Cards:"  Cards",ItemsCount:"Items Count :",Unassigned:"Unassigned",AddCard:"Add Card",EditCard:"Edit Card",DeleteCard:"Delete Card",TopofRow:"Top of Row",BottomofRow:"Bottom of Row",MoveUp:"Move Up",MoveDown:"Move Down",MoveLeft:"Move Left",MoveRight:"Move Right",MovetoSwimlane:"Move to Swimlane",HideColumn:"Hide Column",VisibleColumns:"Visible Columns",PrintCard:"Print Card",Show:"Show",Hide:"Hide",Search:"Search"};this.localizedLabels=this._getLocalizedLabels()},i.prototype._init=function(){this._initPrivateProperties();this._initialKanbanModel=n.extend(!0,{},this.model);var t={keyFiltering:!0};if(this._trigger("load",t),this._keyFiltering=t.keyFiltering,!ej.isNullOrUndefined(this.model.query)&&this.model.query instanceof ej.Query||(this.model.query=new ej.Query),this._initSubModules(),this._initialize(),this.model.columns.length==0)return!1;this.KanbanAdaptive&&this.KanbanAdaptive._kbnAdaptSwimlaneData();this.element.removeClass("e-kanbanscroll");this.KanbanScroll&&this.KanbanScroll._initScrolling();this._checkDataBinding()},i.prototype._initSubModules=function(){var n=this.model;this.KanbanCommon=new ej.KanbanFeatures.Common(this);n.allowDragAndDrop&&(this.KanbanDragAndDrop=new ej.KanbanFeatures.DragAndDrop(this));(n.editSettings.allowEditing||n.editSettings.allowAdding)&&(this.KanbanEdit=new ej.KanbanFeatures.Edit(this));n.isResponsive&&(this.KanbanAdaptive=new ej.KanbanFeatures.Adaptive(this));n.isResponsive&&n.minWidth>0&&(n.allowScrolling=!0);n.allowScrolling&&(this.KanbanScroll=new ej.KanbanFeatures.Scroller(this));n.contextMenuSettings.enable&&(this.KanbanContext=new ej.KanbanFeatures.Context(this));n.fields&&!ej.isNullOrUndefined(n.fields.swimlaneKey)&&(this.KanbanSwimlane=new ej.KanbanFeatures.Swimlane(this));n.allowSelection&&(this.KanbanSelection=new ej.KanbanFeatures.Selection(this));(n.filterSettings.length>0||n.allowFiltering||n.customToolbarItems.length>0||n.allowPrinting||n.allowSearching)&&(this.KanbanFilter=new ej.KanbanFeatures.Filter(this))},i.prototype.kanbanWindowResize=function(t){var o,c,f,nt,v,r,e,u,tt,it,s,rt,y,p,i,w,ut,b,l,ft,k,a,d,h,g,et;if(this.KanbanAdaptive)if(this._kbnDdlWindowResize&&ej.browserInfo().name=="msie"&&parseInt(ej.browserInfo().version,10)==8&&t.stopImmediatePropagation(),o=this.element.find(".e-kanbantoolbar"),c=this.model.fields.swimlaneKey,this.model.isResponsive&&(ej.isNullOrUndefined(this.model.minWidth)||this.model.minWidth==0)){if(nt=n(".e-kanbanfilter-window"),v=!0,ej.browserInfo().name=="msie"&&parseInt(ej.browserInfo().version,10)<=9&&(v=!1),v)if(window.matchMedia("(max-width: 801px)").matches||window.matchMedia("(max-device-width: 1200px)").matches){if(this.model.isResponsive&&this.kanbanContent.hasClass("e-scroller")&&this.kanbanContent.data("ejScroller").destroy(),this.element.hasClass("e-responsive")||(this.element.addClass("e-responsive"),o.css("display","table"),o.find(".e-searchdiv").hide(),o.find(".e-search").css({border:"none"}),ej.isNullOrUndefined(c)||this.KanbanAdaptive._kbnAdaptSwimlaneDdl(),this.model.filterSettings.length>0&&(this.KanbanAdaptive._kbnAdaptFilterWindow(),this._kbnFilterCollection=this._filterCollection.slice()),this._on(n(".e-swimlane-window,.e-kanbanfilter-window,.e-kanbantoolbar"),n.isFunction(n.fn.tap)&&this.model.enableTouch?"tap":"click","",this._kbnAdaptClickHandler)),this.KanbanAdaptive._columnTimeoutAdapt(),!this.element.hasClass("e-swimlane-responsive"))for(r=this.element.find(".e-rowcell:visible"),i=0;i<r.length;i++)r.eq(i).height()+r.eq(i).offset().top>n(window).height()&&(e=r.eq(i).find(".e-cell-scrollcontent"),e.length>0?(e.ejScroller({height:n(window).height()-(this.kanbanContent.offset().top+2),buttonSize:0,scrollerSize:9,enableTouchScroll:!0,autoHide:!0}),r.eq(i).find(".e-shrinkheader").prependTo(r.eq(i))):(e=ej.buildTag("div.e-cell-scrollcontent","<div><\/div>"),r.eq(i).append(e),e.children().append(r.eq(i).children(":not('.e-cell-scrollcontent')")),e.ejScroller({height:n(window).height()-(this.kanbanContent.offset().top+2),thumbStart:n.proxy(this._kbnThumbStart,this),buttonSize:0,scrollerSize:9,enableTouchScroll:!0,autoHide:!0}),r.eq(i).find(".e-shrinkheader").prependTo(r.eq(i))));if(f=this.element.find(".e-searchbar"),f.length>0&&f.find(".e-ejinputtext").val().length>0&&(u=f.find(".e-searchitem"),u.addClass("e-searchfind"),u.prev().show(),f.siblings().hide(),f.find(".e-adapt-search").length==0&&(tt=ej.buildTag("div.e-icon e-adapt-search e-searchfind","",{}),it=ej.buildTag("div.e-icon e-adapt-cancel e-cancel","",{}),u.siblings(".e-searchdiv").append(it).prepend(tt)),u.parents(".e-search").css({border:""}),u.parents("body").removeClass("e-kbnwindow-modal"),u.parents(".e-kanbantoolbar").addClass("e-adaptive-search"),f.find(".e-adapt-cancel").show(),u.hide()),this.model.filterSettings.length>0)for(s=this.element.find(".e-quickfilter").nextAll(".e-tooltxt"),i=0;i<s.length;i++)s.eq(i).hasClass("e-select")&&(rt=s.index(s.eq(i)),y=this.element.find(".e-kanbanfilter-icon"),this._kbnAutoFilterCheck=!0,nt.find(".e-filter-content span.e-chkbox-wrap").eq(rt).click(),this._kbnAutoFilterCheck=!1,y.hasClass("e-kbnclearfl-icon")||y.addClass("e-kbnclearfl-icon"),s.eq(i).removeClass("e-select"));if(ej.isNullOrUndefined(c)||this.kanbanContent.ejScroller({height:n(window).height()-(this.headerContent.offset().top+this.headerContent.height()+5),scroll:n.proxy(this._freezeSwimlane,this),thumbStart:n.proxy(this._kbnThumbStart,this),buttonSize:0,scrollerSize:9,enableTouchScroll:!0,autoHide:!0}),!this.element.hasClass("e-swimlane-responsive"))for(p=this.element.find(".e-rowcell .e-cell-scrollcontent:visible"),i=0;i<p.length;i++)w=n(p[i]).data("ejScroller"),ej.isNullOrUndefined(w)||w.refresh();ut=this.element.find(".e-kanbandialog");b=this.element.find(".e-swimlane-window");ut.is(":visible")&&this.KanbanAdaptive&&this.KanbanAdaptive._setAdaptEditWindowHeight();!ej.isNullOrUndefined(c)&&b.is(":visible")&&(l=this.element.find(".e-slwindow-scrollcontent"),ft=this.headerContent.offset().top,l.ejScroller({height:n(window).height()-l.offset().top,buttonSize:0,scrollerSize:9,enableTouchScroll:!0,autoHide:!0}),l.data("ejScroller").refresh(),b.css({height:n(window).height()-ft}));this.KanbanSwimlane&&this.KanbanSwimlane._removeFreezeRow();this.model.allowSearching&&ej.browserInfo().name=="webkit"&&this.element.find(".e-searchitem").addClass("e-webkitadapt-search")}else this.KanbanAdaptive&&(k=n("#"+this._id+"_toolbarItems"),k.parents(".e-kanban").length==0&&(k.removeClass("e-kbntoolbar-body").prependTo(this.element),n("#"+this._id+"_slWindow").removeClass("e-kbnslwindow-body").appendTo(this.element)),this.KanbanAdaptive._removeKbnAdaptItems(),this.element.find(".e-webkitadapt-search").removeClass("e-webkitadapt-search"),this.model.allowScrolling&&(this.KanbanCommon._setWidthToColumns(),this.KanbanScroll._renderScroller()));!ej.isNullOrUndefined(this.model.fields.swimlaneKey)&&this.kanbanContent.hasClass("e-scroller")&&this.kanbanContent.data("ejScroller").refresh();this.KanbanAdaptive._setAdaptiveSwimlaneTop()}else this.model.scrollSettings.width=this._originalScrollWidth,this.element.outerWidth("100%"),this.getContentTable().width("100%"),this.getHeaderTable().width("100%"),a=this.element.outerWidth(),d=n(window).width()-this.element.offset().left,h=n(window).height()-this.element.offset().top,o.length>0&&(h=h-(o.outerHeight()+25)),g=this.headerContent.outerHeight()+this.getContentTable().height(),et=this.model.minWidth>a||d<=a||h<=g,this.KanbanAdaptive&&this.KanbanAdaptive._renderResponsiveKanban(et,g,a,h,d)},i.prototype._initKanbanRender=function(){var t=this,i;this._addInitTemplate();this.model.keySettings&&n.extend(this.model.keyConfigs,this.model.keySettings);this._render();this._trigger("dataBound",{});this.model.contextMenuSettings.enable&&(this._dataManager.dataSource.offline?this.KanbanContext._renderContext():(i=t._dataSource().executeQuery(this.model.query),i.done(ej.proxy(function(n){t._contextSwimlane=new ej.DataManager(n.result);t.KanbanContext._renderContext()}))));this.KanbanEdit&&(this.KanbanEdit._processEditing(),this.model.editSettings.editMode=="dialog"||this.model.editSettings.editMode=="dialogtemplate"?this.element.append(this.KanbanEdit._renderDialog()):(this.model.editSettings.editMode=="externalform"||this.model.editSettings.editMode=="externalformtemplate")&&this.element.append(this.KanbanEdit._renderExternalForm()));this.KanbanAdaptive&&this.KanbanAdaptive._setResponsiveHeightWidth();this.KanbanSelection&&this.KanbanSelection._renderKanbanTouchBar();this._wireEvents();this.model.tooltipSettings.enable&&(this.element.append(n("<div class='e-kanbantooltip'><\/div>")),this.element.find(".e-kanbantooltip").hide());this.initialRender=!1},i.prototype._render=function(){this.element.addClass(this.model.cssClass+"e-widget");this._renderContent().insertAfter(this.element.children(".e-kanbanheader"));this.model.enableTotalCount&&this.KanbanCommon._totalCount();this.KanbanCommon._renderLimit();this.KanbanCommon._setWidthToColumns();this._enableDragandScroll();ej.isNullOrUndefined(this.model.fields.swimlaneKey)||(this._swimlaneRows=this.element.find(".e-swimlanerow"));this.initialRender&&this.KanbanCommon._addLastRow()},i.prototype._renderHeader=function(){var o=ej.buildTag("div.e-kanbanheader","",{},{}),v=ej.buildTag("div","",{},{}),it=n(document.createElement("colgroup")),h,ut,r,l,b,f,k,ot,d,st,g,ht,nt,tt,e,a;ej.isNullOrUndefined(this.model.fields.swimlaneKey)||o.addClass("e-slheader");this.model.allowScrolling&&v.addClass("e-headercontent");o.append(v);var s=ej.buildTag("table.e-table","",{},{"data-role":"kanban"}),y=ej.buildTag("thead","",{},{}),rt=ej.buildTag("tbody.e-hide","",{},{});for(h=0;h<this.model.stackedHeaderRows.length;h++)ut=this.KanbanCommon._createStackedRow(this.model.stackedHeaderRows[h]),y.append(ut);var p=ej.buildTag("tr.e-columnheader","",{},{}),ft=n(document.createElement("tr")),u=this.model.columns;for(this._visibleColumns=[],this._expandedColumns=[],this._headerColumnNames={},r=0;r<u.length;r++){var t=u[r],i=ej.buildTag("th.e-headercell","",{},{"data-role":"columnheader"}),c=document.createElement("col"),ct=document.createElement("td"),w=ej.buildTag("div.e-headercelldiv","",{},{}),et=ej.buildTag("div.e-headerdiv",t.headerText!=null?t.headerText:t.key,{},{});if(ej.isNullOrUndefined(t.headerTemplate)||et.html(n(t.headerTemplate).hide().html()),f=null,this.model.enableTotalCount&&(l=ej.buildTag("div.e-totalcard","",{},{}),b=ej.buildTag("span.e-totalcount","",{},{}),ej.isNullOrUndefined(t.totalCount)||ej.isNullOrUndefined(t.totalCount.text)?l.append(this.localizedLabels.ItemsCount).append(b):l.append(t.totalCount.text+" : ").append(b),i.append(l)),ej.isNullOrUndefined(t.constraints)||(k=!ej.isNullOrUndefined(t.constraints.min),ot=!ej.isNullOrUndefined(t.constraints.max),f=ej.buildTag("div.e-limits","",{},{}),k&&(d=ej.buildTag("div.e-min",this.localizedLabels.Min,"",{}),st=ej.buildTag("span.e-minlimit"," "+t.constraints.min.toString(),"",{}),d.append(st),f.append(d)),ot&&(g=ej.buildTag("div.e-max",this.localizedLabels.Max,"",{}),ht=ej.buildTag("span.e-maxlimit"," "+t.constraints.max.toString(),"",{}),g.append(ht),k&&f.append("/"),f.append(g)),this.model.enableTotalCount&&f.children().length>0&&f.prepend("|")),w.append(et),i.prepend(w),i.append(f),this.model.allowToggleColumn&&(u[r].isCollapsed===!0?i.append(ej.buildTag("div.e-icon e-clcollapse","",{},{})).addClass("e-shrinkcol"):i.append(ej.buildTag("div.e-icon e-clexpand","",{},{}))),u[r].isCollapsed===!0&&this.model.allowToggleColumn?(i.find(".e-headercelldiv,.e-totalcard,.e-limits").addClass("e-hide")&&n(c).addClass("e-shrinkcol"),n.inArray(u[r].headerText,this._collapsedColumns)==-1&&this._collapsedColumns.push(u[r].headerText)):(i.find(".e-headercelldiv,.e-totalcard").removeClass("e-hide")&&n(c).removeClass("e-shrinkcol"),ej.isNullOrUndefined(u[r].constraints)||u[r].constraints.type!="column"?i.find(".e-limits").addClass("e-hide"):i.find(".e-limits").removeClass("e-hide"),this._expandedColumns.push(u[r].headerText),u[r].isCollapsed=!1),it.append(c),p.append(i),ft.append(ct),t.visible===!1?(i.addClass("e-hide")&&n(c).css("display","none"),n.inArray(t.headerText,this._hiddenColumns)==-1&&this._hiddenColumns.push(t.headerText)):(this._visibleColumns.push(t.headerText),t.visible=!0),nt=i.find(".e-limits"),tt=i.find(".e-totalcard"),(i.find(".e-clexpand").length>0||u[r].isCollapsed)&&(tt.length==0&&nt.length==0?i.addClass("e-toggleonly"):tt.length==0&&nt.children().length==0&&i.addClass("e-toggleonly")),i.hasClass("e-toggleonly"))for(a=0;a<this.model.columns.length;a++)e=this.model.columns[a],ej.isNullOrUndefined(e.constraints)||ej.isNullOrUndefined(e.constraints.min)&&ej.isNullOrUndefined(e.constraints.max)||i.addClass("e-toggle-withoutcount");e=this.model.columns[r];!this.model.enableTotalCount&&(ej.isNullOrUndefined(e.constraints)||ej.isNullOrUndefined(e.constraints.min)&&ej.isNullOrUndefined(e.constraints.max))||(i.addClass("e-toggle-withcount"),p.addClass("e-header-withcount"));(ej.isNullOrUndefined(t.allowDrag)||t.allowDrag!=!1)&&(t.allowDrag=!0);(ej.isNullOrUndefined(t.allowDrop)||t.allowDrop!=!1)&&(t.allowDrop=!0);ej.isNullOrUndefined(t.headerTemplate)||w.parent().addClass("e-headertemplate");this.initialRender&&(typeof t.width=="string"&&t.width.indexOf("%")!=-1?this._columnsWidthCollection.push(parseInt(t.width)/100*this.element.width()):this._columnsWidthCollection.push(t.width))}return y.append(p),s.append(it).append(y),rt.append(ft),s.append(rt),v.append(s),this.setHeaderContent(o),this.setHeaderTable(s),o},i.prototype._renderContent=function(){var t=ej.buildTag("div.e-kanbancontent","",{},{}),i=ej.buildTag("div","",{},{}),u=ej.buildTag("tbody","",{},{}),n=ej.buildTag("table.e-table","",{},{"data-role":"kanban"}),r;return n.append(this.getHeaderTable().find("colgroup").clone()).append(u),i.append(n),t.append(i),this.setContent(t),this.setContentTable(n),n.attr("data-role","kanban"),r={requestType:"refresh"},this.sendDataRenderingRequest(r),t},i.prototype.refreshTemplate=function(){this._addInitTemplate();(this.model.editSettings.allowEditing||this.model.editSettings.allowAdding)&&this.KanbanEdit._addDialogEditingTemplate()},i.prototype.getContentTable=function(){return this.contentTable},i.prototype.setContentTable=function(n){this.contentTable=n},i.prototype.getVisibleColumnNames=function(){return this._visibleColumns},i.prototype.getHeaderContent=function(){return this.headerContent},i.prototype.setHeaderContent=function(n){this.headerContent=n},i.prototype.getContent=function(){return this.kanbanContent},i.prototype.setContent=function(n){this.kanbanContent=n},i.prototype._initDataSource=function(){var n=this,t;this._isLocalData=!(this._dataSource()instanceof ej.DataManager)||this._dataSource().dataSource.offline;this._ensureDataSource();this._trigger("actionBegin");t=this._dataSource().executeQuery(this.model.query);this.element.is(":visible")||this.element.ejWaitingPopup("hide");t.done(ej.proxy(function(t){n.element.ejWaitingPopup("hide");n._currentJsonData=n.currentViewData=t.result;n._recordsCount=t.count==0&&t.result.length?t.result.length:t.count;n._initKanbanRender()}))},i.prototype.columns=function(t,i,r){var f,u,e,s,o,h;if(!ej.isNullOrUndefined(t)){for(f=!1,typeof t=="string"?(t=[t],f=!0):t instanceof Array&&t.length&&typeof t[0]=="string"&&(f=!0),u=0;u<t.length;u++)e=n.inArray(this.getColumnByHeaderText(f?t[u]:t[u].headerText),this.model.columns),r=="add"||ej.isNullOrUndefined(r)?e==-1?(this.model.columns.push(f?{headerText:t[u],key:i}:t[u]),this._initialKanbanModel.columns.push(f?{headerText:t[u],key:i}:t[u])):(this.model.columns[e]=f?{headerText:t[u],key:i}:t[u],this._initialKanbanModel.columns[e]=f?{headerText:t[u],key:i}:t[u]):e!=-1&&(this.model.columns.splice(e,1),this._initialKanbanModel.columns.splice(e,1));for(s=this.element.find(".e-kanbanheader"),this._columnsWidthCollection=[],o=0;o<this.model.columns.length;o++)ej.isNullOrUndefined(this.model.columns[o].width)||this._columnsWidthCollection.push(this.model.columns[o].width);s.length==0&&this.model.columns.length!=0?this._checkDataBinding():s.length!=0&&this.model.columns.length!=0?(this.element[0].replaceChild(this._renderHeader()[0],s[0]),this._headerTextWidth(),f?(this._keyValue.push(i),this.keyPredicates.push(new ej.Predicate(this.model.keyField,ej.FilterOperators.equal,i,!0))):this._addColumnFilters(),this.model.searchSettings.key.length==0&&this._filterCollection.length==0&&(queryManagar=this.model.query,queryManagar.queries=[],queryManagar.where(ej.Predicate.or(this.keyPredicates))),h={requestType:"addcolumn"},this.refresh(!0),this.KanbanCommon._totalCount()):this.model.columns.length==0&&(this.model.query.queries=[],this.element.children().remove())}},i.prototype._headerTextWidth=function(){var n,i,t;if(this.model.enableRTL&&this.model.allowToggleColumn)for(this._headerWidth=[],n=0;n<this.model.columns.length;n++)i=this.element.find(".e-headerdiv").eq(n),t=this.element.find(".e-headercelldiv").eq(n),t.hasClass("e-hide")?(t.removeClass("e-hide"),this._headerWidth.push(i.width()),t.addClass("e-hide")):this._headerWidth.push(i.width())},i.prototype._checkDataBinding=function(){var t,i;(this.model.columns.length||(this._dataSource()!=null&&this._dataSource().length||this._dataSource()instanceof ej.DataManager)&&(!(this._dataSource()instanceof ej.DataManager)||this._dataManager.dataSource.url!=undefined||this._dataSource().dataSource.json.length))&&(ej.isNullOrUndefined(this.model.keyField)||this._addColumnFilters(),this.initialRender=!0,this.model.cssClass!=null&&this.element.addClass(this.model.cssClass),(this.model.filterSettings.length>0||this.model.allowSearching||this.model.customToolbarItems.length>0||this.model.allowPrinting)&&this.element.append(this._renderToolBar()),t=this.model.columns,t&&t.length&&(this.element.append(this._renderHeader()),this.KanbanCommon._stackedHeadervisible(),this._headerTextWidth()),n.isFunction(n.fn.ejWaitingPopup)&&(this.element.ejWaitingPopup({showOnInit:!1}),n("#"+this._id+"_WaitingPopup").addClass("e-kanbanwaitingpopup")),this._dataSource()instanceof ej.DataManager?(this.element.ejWaitingPopup("show"),this._dataSource().ready!=undefined?(i=this,this._dataSource().ready.done(function(n){i._initDataSource();i.model.dataSource=new ej.DataManager(n.result)})):(this.element.ejWaitingPopup("show"),this._initDataSource())):(this._trigger("actionBegin"),this._ensureDataSource(),this._initKanbanRender(),this.KanbanCommon._enableKanbanRTL()))},i.prototype._renderToolBar=function(){var n=ej.buildTag("div.e-kanbantoolbar","",{},{id:this._id+"_toolbarItems"}),o,s,f,r,u,t,v;if(this.model.allowSearching){this._isWatermark="placeholder"in document.createElement("input");var e=ej.buildTag("ul.e-searchbar","",{},{}),i=ej.buildTag("li.e-search","",{},{id:this._id+"_toolbarItems_search"}),c=ej.buildTag("a.e-searchitem e-toolbaricons e-disabletool e-icon e-searchfind","",{float:"right"},{});ej.browserInfo().name=="msie"&&c.css("position","absolute");o=ej.buildTag("input.e-ejinputtext e-input","",{},{type:"text",id:this._id+"_searchbar",placeholder:this.localizedLabels.Search});s=ej.buildTag("div.e-searchdiv","",{display:"inline-table",width:"83%"},{});s.append(o);i.append(s);i.append(c);e.append(i);this._searchInput=o;ej.isNullOrUndefined(this.model.searchSettings.key)||this._searchInput.val(this.model.searchSettings.key);this._searchBar=i;this._isWatermark||(this._hiddenSpan=ej.buildTag("span.e-input e-placeholder","Search",{display:"block"},{}).insertAfter(this._searchInput))}for(ej.isNullOrUndefined(this.model.customToolbarItems)||(f=ej.buildTag("ul.e-customtoolbar"),this.model.filterSettings.length>0&&f.addClass("e-customtoolbarseparator"),!ej.isNullOrUndefined(this.model.customToolbarItems)&&this.model.customToolbarItems.length&&this._renderCustomLi(f),n.append(f)),this.model.filterSettings.length>0&&(r=ej.buildTag("ul","",{},{}),u=ej.buildTag("li","",{},{"class":"e-quickfilter",tabindex:"0"}),u.append("<label class='e-toolbartext e-text'>"+this.localizedLabels.FilterSettings+"<\/label>"),r.append(u)),t=0;t<this.model.filterSettings.length;t++){var l=this.model.filterSettings[t].description,i=ej.buildTag("li","",{},{id:this._id+"_"+this.model.filterSettings[t].text.replace(/[-\s']/g,"_"),title:ej.isNullOrUndefined(l)?this.model.filterSettings[t].text:l,tabindex:"0","class":"e-kbnfilter-tbtn"}),a=ej.buildTag("a.e-toolbartext e-text",this.model.filterSettings[t].text,{},{});i.append(a);r.append(i)}if(this.model.allowPrinting){var h=ej.buildTag("ul.e-print","",{},{}),u=ej.buildTag("li","",{},{"class":"e-printlist",title:"Print",tabindex:"0"}),a=ej.buildTag("a.e-printicon e-icon","",{});u.append(a);h.append(u)}return ej.isNullOrUndefined(r)||n.append(r),ej.isNullOrUndefined(h)||n.append(h),ej.isNullOrUndefined(e)||n.append(e),v={click:this._onToolbarClick},n.ejToolbar(v),this._filterToolBar=n,n},i.prototype._renderCustomLi=function(t){for(var r,u,f,i=0;i<this.model.customToolbarItems.length;i++){u=this.model.customToolbarItems[i].template?this.model.customToolbarItems[i].template.replace("#",""):this.model.customToolbarItems[i];r=ej.buildTag("li","",{},{id:this._id+"_"+u,title:u});switch(typeof this.model.customToolbarItems[i]){case"string":f=ej.buildTag("a.e-toolbaricons e-icon","",{}).addClass(this.model.customToolbarItems[i].text);break;case"object":r.attr("title",this.model.customToolbarItems[i].template.replace("#",""));f=n(this.model.customToolbarItems[i].template).hide().html()}r.html(f);t.append(r)}},i.prototype.dataSource=function(n,t){t&&(this._templateRefresh=!0);this._dataSource(n);this.KanbanCommon._refreshDataSource(n);this.model.enableTotalCount&&this.KanbanCommon._totalCount();this.KanbanCommon._addLastRow()},i.prototype._ensureDataSource=function(t){var i,e,h,r,u,o,c,f,l,s,a;if(this._dataSource()==null&&!(this._dataSource()instanceof ej.DataManager)){if(ej.isNullOrUndefined(t)||t.requestType!="add"){ej.isNullOrUndefined(this._dataSource())&&(this._currentJsonData=this.currentViewData=[]);return}this.dataSource([],!1)}if(this.model.query.requiresCount(),i=this.model.query,f=i.clone(),this._dataSource()instanceof ej.DataManager||(this._currentJsonData=this.currentViewData=this._dataSource()),(this.model.filterSettings.length||this.model.allowFiltering)&&!ej.isNullOrUndefined(t)&&t.requestType=="filtering"||!ej.isNullOrUndefined(t)&&t.requestType=="search"||this.model.searchSettings.key.length){for(i.queries=i.queries.slice(i.queries.length),this.model.allowSearching&&(h=this._searchBar.find(".e-toolbaricons"),this.model.searchSettings.key.length!=0?(r=this.model.searchSettings,h.removeClass("e-searchfind").addClass("e-cancel"),r.fields.length==0&&(ej.isNullOrUndefined(this.model.fields.content)||r.fields.push(this.model.fields.content),ej.isNullOrUndefined(this.model.fields.swimlaneKey)||r.fields.push(this.model.fields.swimlaneKey),ej.isNullOrUndefined(this.model.fields.primaryKey)||r.fields.push(this.model.fields.primaryKey),ej.isNullOrUndefined(this.model.fields.title)||r.fields.push(this.model.fields.title),ej.isNullOrUndefined(this.model.fields.tag)||r.fields.push(this.model.fields.tag),ej.isNullOrUndefined(this.model.fields.imageUrl)||r.fields.push(this.model.fields.imageUrl),ej.isNullOrUndefined(this.model.fields.priority)||r.fields.push(this.model.fields.priority),ej.isNullOrUndefined(this.model.fields.color)||r.fields.push(this.model.fields.color)),i.search(r.key,r.fields,r.operator||"contains",r.ignoreCase)):h.removeClass("e-cancel").addClass("e-searchfind")),this.element.hasClass("e-responsive")&&(this._filterCollection=this._kbnFilterCollection.slice()),u=0;u<this._filterCollection.length;u++)e=e!=undefined?e.and(this._filterCollection[u]):this._filterCollection[u];if(this._keyFiltering)for(i.where(ej.Predicate.or(this.keyPredicates)),u=0;u<i.queries.length;u++)i.queries[u].fn=="onWhere"&&e&&(i.queries[u].e=i.queries[u].e.and(e));else this._filterCollection.length&&i.where(e);this._isLocalData&&(this._filteredRecords=this._dataManager.executeLocal(i).result,this._filteredRecordsCount=this._filteredRecords.length);this._isLocalData&&this.model.filterSettings.length==0&&(!ej.isNullOrUndefined(this._filteredRecordsCount)||this._filteredRecordsCount>0)&&(this._filteredRecordsCount=null,this._filteredRecords=[])}this._isLocalData&&(this.model.editSettings.allowEditing||this.model.editSettings.allowAdding)&&(!ej.isNullOrUndefined(this._cModifiedData)||!ej.isNullOrUndefined(this._cAddedRecord))&&(i.queries=f.queries);t&&t.requestType=="delete"&&!ej.isNullOrUndefined(this._cDeleteData)&&this._isLocalData&&(this._dataSource()instanceof ej.DataManager?(o=n.inArray(this._cDeleteData[0],this._dataSource().dataSource.json),this._dataSource().dataSource.json.splice(o,1)):(o=n.inArray(this._cDeleteData[0],this._dataSource()),this._dataSource(undefined,!0).splice(o,1)));this._cloneQuery=i.clone();this._isLocalData&&(!ej.isNullOrUndefined(t)&&t.requestType=="refresh"&&this._filterCollection.length<=0&&(ej.isNullOrUndefined(this._searchInput)||this._searchInput.val().length<=0)?(f=i.clone(),f.queries=[],f.where(ej.Predicate.or(this.keyPredicates)),c=f):c=i,l=this._dataManager.dataSource.json,s=this._dataSource().dataSource,!ej.isNullOrUndefined(s)&&this._dataSource()instanceof ej.DataManager&&(this._dataManager.dataSource.json=l!=s.json?s.json:l),a=this._dataManager.executeLocal(c),this.currentViewData=this._currentJsonData=a.result,this._recordsCount=a.count)},i.prototype._addColumnFilters=function(){for(var n,t,u=this.model.columns,i=[],f=this.model.query,r=0;r<u.length;r++)for(n=u[r].key,ej.isNullOrUndefined(n)||(n=typeof n=="object"?n:n.split(",")),t=0;t<n.length;t++)i.push(new ej.Predicate(this.model.keyField,ej.FilterOperators.equal,n[t],!1)),this._keyValue.push(n[t]);this._keyFiltering&&i.length>0&&f.where(ej.Predicate.or(i));this.keyPredicates=i},i.prototype.refresh=function(n){n&&this.refreshTemplate();this.KanbanCommon._processBindings({requestType:"refresh"})},i.prototype.sendDataRenderingRequest=function(t){var r,e,f,u;if(this._templateRefresh&&(this.refreshTemplate(),this._templateRefresh=!1),r=this.model.query,e=r.clone(),this.currentViewData!=null&&(this.currentViewData.length||this.model.showColumnWhenEmpty)){switch(t.requestType){case"save":case"drop":case"delete":case"refresh":case"search":case"filtering":this.element.hasClass("e-responsive")&&!ej.isNullOrUndefined(t.currentFilterObject)&&(this._kbnAdaptFilterObject=t.currentFilterObject.slice());case"cancel":if(this.element.find(".e-kbnadapt-editdlg").length==0&&this.element.hasClass("e-responsive")&&(this.model.editSettings.allowAdding||this.model.editSettings.allowEditing)&&n("#"+this._id+"_dialogEdit_wrapper").appendTo(this.element),this.getContentTable().find("colgroup").first().replaceWith(this.KanbanCommon._getMetaColGroup()),!ej.isNullOrUndefined(this.model.fields.swimlaneKey)){var i=this,s=this.model.fields.swimlaneKey,o=this.model.swimlaneSettings.unassignedGroup;if(r=r.group(s),!this.currentViewData.GROUPGUID&&(o.enable&&o.keys.length>0&&n.map(i.currentViewData,function(n,t){i.currentViewData[t]=i._checkKbnUnassigned(n)}),this.currentViewData.GROUPGUID||(this.currentViewData=new ej.DataManager(this.currentViewData).executeLocal(r).result),this.model.swimlaneSettings.headers.length>0)){for(n.map(i.currentViewData,function(t){var f=i.model.swimlaneSettings.headers,r=new ej.DataManager(f).executeLocal((new ej.Query).where("key",ej.FilterOperators.equal,t.key)),u;u=r.length==0?{slHeader:t.key}:ej.isNullOrUndefined(r[0].text)?{slHeader:r[0].key}:{slHeader:r[0].text};n.extend(t,u)}),this.model.swimlaneSettings.showEmptySwimlane&&n.map(i.model.swimlaneSettings.headers,function(n){var t=new ej.DataManager(i.currentViewData).executeLocal((new ej.Query).where("key",ej.FilterOperators.equal,n.key)),r;t.length==0&&i.model.swimlaneSettings.showEmptySwimlane&&typeof n.key=="string"&&n.key.length!=0&&(r=ej.isNullOrUndefined(n.text)?n.key:n.text,t=[{key:n.key,slHeader:r,count:0,items:[]}],i.currentViewData.push(t[0]))}),f=new ej.DataManager(this.currentViewData).executeLocal((new ej.Query).sortBy("slHeader")),this.currentViewData.splice(0,this.currentViewData.length),u=0;u<f.length;u++)this.currentViewData.push(f[u]);this._slText=new ej.DataManager(this.currentViewData).executeLocal((new ej.Query).select("slHeader"));this._slKey=new ej.DataManager(this.currentViewData).executeLocal((new ej.Query).select("key"))}}this._renderAllCard();this._keyFiltering||ej.isNullOrUndefined(this.model.fields.swimlaneKey)||!this.model.swimlaneSettings.showCount||this._getSwimlaneCount();this.element.hasClass("e-responsive")&&(ej.isNullOrUndefined(this.model.fields.swimlaneKey)||(this.element.find(".e-swimlanerow").hide(),this.KanbanAdaptive&&this.KanbanAdaptive._addSwimlaneName()));this._enableSwimlaneCount=!0;this._eventBindings();this._enableDragandScroll();break;case"beginedit":case"add":this.KanbanEdit&&(this.KanbanEdit._editAdd(t),this._enableSwimlaneCount=!1)}r.queries=e.queries}else this.getContentTable().find("tbody").empty().first().append(this.KanbanCommon._getEmptyTbody());(this.model.editSettings.editMode=="externalform"||this.model.editSettings.editMode=="externalformtemplate")&&(this._editForm=this.element.find(".e-externalform"),this.KanbanEdit._formFocus());("beginedit"==t.requestType||"add"==t.requestType)&&(this.model.editSettings.editMode=="dialog"||this.model.editSettings.editMode=="externalform")&&this.KanbanEdit._refreshEditForm(t);this._renderComplete(t);this.KanbanSwimlane&&this._enableSwimlaneCount&&(this.KanbanSwimlane._swimlaneLimit(),this._swimlaneRows=this.element.find(".e-swimlanerow"));this.KanbanCommon._renderLimit();this._filterCollection.length==0&&this.model.searchSettings.key.length==0&&this.KanbanCommon._totalCount();(this._filterCollection.length>0||this.model.searchSettings.key.length>0)&&this.KanbanFilter._filterLimitCard(t);this._newData=null;this.element.hasClass("e-responsive")&&(this.kanbanWindowResize(),ej.isNullOrUndefined(this.model.fields.swimlaneKey)||this.KanbanAdaptive._setAdaptiveSwimlaneTop())},i.prototype._checkKbnUnassigned=function(t){for(var f,r=this.model.swimlaneSettings.unassignedGroup,u=t[this.model.fields.swimlaneKey],e=this.model.fields.swimlaneKey,i=0;i<r.keys.length;i++)typeof r.keys[i]=="string"&&(r.keys[i].replace(/^\s+|\s+$/g,"")==""&&n.inArray("",r.keys)>=0&&i<n.inArray("",r.keys)?r.keys.splice(i,1):r.keys[i].replace(/^\s+|\s+$/g,"")==""&&(r.keys[i]=r.keys[i].replace(/^\s+|\s+$/g,"")));if(u!=undefined&&u!=null&&typeof u=="string"&&(u=u.replace(/^\s+|\s+$/g,"")),u==null||u==undefined)for(i=0;i<r.keys.length;i++)typeof r.keys[i]=="string"&&(r.keys[i].toLowerCase()=="null"||r.keys[i].toLowerCase()=="undefined")?t[e]=this.localizedLabels.Unassigned:(r.keys[i]==null||r.keys[i]==undefined)&&(t[e]=this.localizedLabels.Unassigned);else for(i=0;i<r.keys.length;i++)f=r.keys[i],typeof u=="number"&&f.startsWith("'")&&f.endsWith("'")&&(f=f.split("'")[1]),f==u&&(t[e]=this.localizedLabels.Unassigned);return t},i.prototype._renderAllCard=function(){var t=document.createElement("div");t.innerHTML=["<table>",n.render[this._id+"_JSONTemplate"]({columns:this.model.columns,dataSource:this.currentViewData}),"<\/table>"].join("");ej.isNullOrUndefined(t.firstChild)||ej.isNullOrUndefined(t.firstChild.lastChild)||this.getContentTable().get(0).replaceChild(t.firstChild.lastChild,this.getContentTable().get(0).lastChild);ej.isNullOrUndefined(this.model.fields.swimlaneKey)||(this._swimlaneRows=this.element.find(".e-swimlanerow"));this._swimlaneCollapse();this.KanbanSelection&&this.KanbanSelection._selectionOnRerender()},i.prototype._editEventTrigger=function(n){if(n.requestType=="save"||n.requestType=="delete"){var t={data:n.data,action:n.action!==undefined?n.action:n.requestType};this._trigger("end"+t.action.charAt(0).toUpperCase()+t.action.slice(1),t)}},i.prototype._renderComplete=function(t){t.requestType=="beginedit"||this.initialRender||this.KanbanCommon._setWidthToColumns();(t.requestType=="save"||t.requestType=="cancel")&&(this._isAddNew=!1,this._isEdit=!1);t.requestType=="beginedit"&&(this._isEdit=!0);("delete"==t.requestType||"save"==t.requestType)&&this._editEventTrigger(t);this._tableBEle=this.getContentTable().get(0);this._kanbanRows=this._tableBEle.rows;this._columnRows=n(this._kanbanRows).not(".e-swimlanerow");this.model.allowScrolling&&!this.initialRender&&this.getContentTable().find("tr:last").find("td").addClass("e-lastrowcell");ej.isNullOrUndefined(t.dialogtype)&&this._trigger("actionComplete",t);!this.initialRender&&this.model.allowScrolling&&(t.requestType=="add"||t.requestType=="cancel"||t.requestType=="save"||t.requestType=="delete"||t.requestType=="filtering"||t.requestType=="search"||t.requestType=="refresh"||t.requestType=="drop")&&this.KanbanScroll._refreshScroller(t);this.model.allowDragAndDrop&&this.KanbanDragAndDrop&&this.KanbanDragAndDrop._addDragableClass()},i.prototype._createTemplate=function(t,i){var r=document.createElement("script"),u;return r.id=this._id+i+"_Template",u=this.element.parents("body").find("#"+r.id),r.type="text/x-jsrender",r.text=t,u&&u.remove(),n("body").append(r),r},i.prototype._addInitTemplate=function(){var t=this,i={},e=!ej.isNullOrUndefined(t.model.keyField),o=ej.browserInfo().name=="msie"&&parseInt(ej.browserInfo().version,10)==8,s=this.model.allowToggleColumn,u=!ej.isNullOrUndefined(t.model.fields.swimlaneKey),r=!ej.isNullOrUndefined(t.model.fields.collapsibleCards),f=!ej.isNullOrUndefined(t.model.showColumnWhenEmpty);this._slTemplate="";i[t._id+"Object"]=this;i["_"+t._id+"columnKeys"]=this._getColumnKeyItems;i["_"+t._id+"tagItems"]=this._gettagItems;i["_"+t._id+"colorMaps"]=this._getColorMaps;i["_"+t._id+"getId"]=this.KanbanCommon._removeIdSymbols;i["_"+t._id+"getData"]=this._columnData;i["_"+t._id+"getWidth"]=this._getHeaderWidth;i["_"+t._id+"getCardCount"]=this._columnCardcount;i["_"+t._id+"getStatus"]=this._columnStatus;i["_"+t._id+"getKey"]=this._columnKey;i["_"+t._id+"getToggleCard"]=this._getToggleCard;n.views.helpers(i);u&&(!f||t.currentViewData.length!=0||t.model.swimlaneSettings.showEmptySwimlane)&&(this._slTemplate+="{{for dataSource ~columns=columns ~ds=dataSource}}",this._slTemplate+="<tr id='{{: ~_"+t._id+"getId(key)}}' class='e-swimlanerow' data-role='kanbanrow'><td class='e-rowcell' data-role='kanbancell'  colspan='"+t.getVisibleColumnNames().length+"'><div class='e-slexpandcollapse'><div class='e-icon e-slexpand'><\/div><\/div>{{if slHeader }}<div class='e-slkey e-slHeader' data-ej-slmappingkey='{{:key}}'>{{:slHeader}}<\/div>{{else}}<div class='e-slkey' data-ej-slmappingkey='{{:key}}'>{{:key}}<\/div>{{/if}}{{if "+this.model.swimlaneSettings.showCount+"}}<div class='e-slcount'>"+t.localizedLabels.SwimlaneCaptionFormat+"<\/div>{{/if}}<\/td><\/tr>");this._slTemplate+="<tr class='e-columnrow' data-role='kanbanrow'>";this._slTemplate+=u&&(!f||t.currentViewData.length!=0||t.model.swimlaneSettings.showEmptySwimlane)?"{{for ~columns ~items=#data.items}}":"{{for columns}}";this._slTemplate+="<td data-ej-mappingkey='{{:key}}' class='e-rowcell {{if !#data.visible}}  e-hide {{/if}}{{if #data.allowDrag}}  e-drag {{/if}}{{if #data.allowDrop}}  e-drop {{/if}}{{if (#data.isCollapsed && "+s+")}} e-shrink{{/if}}{{if (#data.key && "+r+")}} e-toggle{{/if}}'data-role='kanbancell'>{{if "+e+"}}{{for ~_"+t._id+"columnKeys('"+t._id+"Object')}}";this._slTemplate+=this._cardCustomization()+"{{/for}}{{/if}}{{if "+r+"}}{{if ~_"+t._id+"getKey(#parent.parent.data,'"+t._id+"Object')}}<div class = 'e-toggle-area{{if "+r+" && #data.isCollapsed}}  e-hide {{/if}}'><div class = 'e-toggle-header'><div class='e-toggle-icon'><div class='e-icon e-toggle-expand'><\/div><\/div><div class='e-toggle-key'>"+t.localizedLabels.Show+"<\/div><div class='e-toggle-count'>{{:~_"+t._id+"columnKeys('"+t._id+"Object',"+r+",true,false)}}<\/div><\/div><div class = 'e-toggle-cards e-hide'>{{for ~_"+t._id+"columnKeys('"+t._id+"Object',"+r+",false,false)}}<div class='e-togglekey'>{{if  ~_"+t._id+"getKey(#data,'"+t._id+"Object')}}{{:#data}}{{/if}}{{for ~_"+t._id+"columnKeys('"+t._id+"Object',"+r+",true,true)}}"+this._cardCustomization()+"{{/for}}<\/div>{{/for}}<\/div><\/div>{{/if}}{{/if}}{{if "+this.model.allowToggleColumn+"}}<div class='e-shrinkheader{{if "+o+"}} IE{{/if}}{{if !#data.isCollapsed}}  e-hide {{/if}}'{{if "+this.model.enableRTL+"}}style=transform:rotate(90deg)translate({{:~_"+t._id+"getWidth('"+t._id+"Object') }}px){{/if}}>{{:headerText}}<div class='e-shrinklabel'>[<div class='e-shrinkcount'>{{:~_"+t._id+"getCardCount(~root.dataSource,key,#parent.parent.getIndex(),'"+t._id+"Object') }}<\/div>]<\/div><\/div>{{/if}}{{if "+this.model.editSettings.allowAdding+"}}{{if #data.showAddButton}} <div class='e-customaddbutton{{if #data.isCollapsed}}  e-hide {{/if}}'><div class='e-columnadd e-icon'><\/div><\/div>{{/if}}{{/if}}<\/td>{{/for}}<\/tr>";u&&(!f||t.currentViewData.length!=0||t.model.swimlaneSettings.showEmptySwimlane)&&(this._slTemplate+="{{/for}}");this.templates[this._id+"_JSONTemplate"]=n.templates(this._createTemplate(this._slTemplate,"_swinlaneContent"));this.templates[this._id+"_cardTemplate"]=n.templates(this._createTemplate(this._cardTemplate,"_cardTemplate"));n.templates(this.templates)},i.prototype._cardCustomization=function(){var t=this,f=this.model.allowToggleColumn,u=this.model.cardSettings.template?!0:!1,r=this.model.fields.imageUrl?!0:!1,i=this.model.fields,s=!ej.isNullOrUndefined(i.title),e=i.title?i.title:i.primaryKey?i.primaryKey:null,o=!ej.isNullOrUndefined(i.tag)||!ej.isNullOrUndefined(i.content);return r||this.element.addClass("e-onlycontent"),this._cardTemplate="<div id='{{:"+t.model.fields.primaryKey+"}}' class='e-kanbancard {{if ~_"+t._id+"getData(#parent)  && "+f+" }}e-hide {{/if}} {{if  ~_"+t._id+"getToggleCard(#data,'"+t._id+"Object')}}e-collapsedcard {{/if}} {{if "+u+"}}e-templatecell{{/if}}'>{{if "+u+"}}"+n(t.model.cardSettings.template).html()+"{{else}}{{if "+t.model.allowTitle+"}}<div class='e-cardheader'><div class='e-primarykey'>{{:"+e+"}}<\/div><div class='e-expandcollapse'><div class='e-icon {{if  ~_"+t._id+"getToggleCard(#data,'"+t._id+"Object')}}e-cardcollapse{{else}}e-cardexpand{{/if}}'><\/div><\/div>{{if  ~_"+t._id+"getToggleCard(#data,'"+t._id+"Object')}}<div class='e-text'>{{:"+i.content+"}}<\/div><div class='e-bottom-triangle'{{if "+i.color+" }} style='border-bottom-color:{{for  ~_"+t._id+"colorMaps('"+t._id+"Object',"+i.color+")}}{{:#data}}{{/for}}'{{/if}}><\/div>{{/if}}<\/div>{{/if}}<div class='e-cardcontent' {{if  ~_"+t._id+"getToggleCard(#data,'"+t._id+"Object')}} style='display:none' {{/if}}'><table class='e-cardtable'><tbody><tr><td class='e-contentcell'>{{if "+o+"}}<div class='e-text'>{{:"+i.content+"}}<\/div><\/td>{{if "+!r+" }} <td><\/td>{{/if}}{{if "+r+" }} <td class='e-imagecell'><div class='e-card_image'>{{if "+i.imageUrl+"}}<img class='e-image' src='{{:"+i.imageUrl+"}}' alt=''><\/img>{{else}}<div class='e-image e-no-user'><\/div>{{/if}}<\/div><\/td>{{/if}}<\/tr><tr><td>{{if "+i.tag+"}}{{for ~_"+t._id+"tagItems("+i.tag+")}}{{:#data}}{{/for}}{{/if}}<\/div>{{/if}}<\/td><td class='e-trainglecell'><div class='e-bottom-triangle'{{if "+i.color+" }} style='border-bottom-color:{{for  ~_"+t._id+"colorMaps('"+t._id+"Object',"+i.color+")}}{{:#data}}{{/for}}'{{/if}}><\/div><\/td><\/tr><\/tbody><\/table><\/div>{{/if}}<\/div>",this._cardTemplate},i.prototype._getHeaderWidth=function(t){var i=this.getRsc("helpers",t),r=n.inArray(this.parent.data,i.model.columns);return i._headerWidth[r]},i.prototype._columnKey=function(n,t){var o=!1,e=this.getRsc("helpers",t),a=this.ctx.root.dataSource,s=e.model.fields.collapsibleCards,i=e.model.fields.collapsibleCards.key,c,r,f;if(typeof n=="string"){if(typeof i=="object"&&(i=i[0]),!ej.isNullOrUndefined(i)){var h=[],r,l=e.model.query,u=l.clone();if(u.queries=[],r=this.parent.parent.parent.parent.fkey,h=this.parent.parent.parent.parent.key,u=u.where(ej.Predicate.or(h)),c=new ej.DataManager(r).executeLocal(u).result,c.length>0)return i}}else if(!ej.isNullOrUndefined(s))for(r=this.parent.parent.fkey,typeof i=="object"&&(i=i[0]),f=0;f<r.length;f++)i.indexOf(r[f][s.field])!=-1&&(o=!0);return o},i.prototype._columnStatus=function(n){var t=this.getRsc("helpers",n),i=t.getBrowserDetails();return i.browser=="msie"?!0:!1},i.prototype._columnCardcount=function(n,t,i,r){var s=r,h,u,e,o,f;if(typeof r=="string"&&(s=this.getRsc("helpers",r)),h=0,t=typeof t=="object"?t:t.split(","),ej.isNullOrUndefined(i)){for(u=0;u<n.length;u++)for(e=0;e<t.length;e++)o=typeof n[u][s.model.keyField],f=t[e],o!=typeof f&&o=="number"&&(f=parseInt(f)),n[u][s.model.keyField]===f&&h++;return h}if(n.length>0&&!ej.isNullOrUndefined(n[i])){for(u=0;u<n[i].items.length;u++)for(e=0;e<t.length;e++)o=typeof n[i].items[u][s.model.keyField],f=t[e],o!=typeof f&&o=="number"&&(f=parseInt(f)),n[i].items[u][s.model.keyField]===f&&h++;return h}},i.prototype._columnData=function(n){return ej.isNullOrUndefined(n)?!1:n.parent.data.isCollapsed},i.prototype._getColorMaps=function(n,t){var e=this.getRsc("helpers",n),f,r=e.model.cardSettings.colorMapping,i,u;for(i in r){if(r[i].indexOf(",")==-1&&r[i]==t)return i;for(f=r[i].split(","),u=0;u<f.length;u++)if(f[u]==t)return i}},i.prototype._gettagItems=function(n){var i="<div class='e-tags'>",t;for(n=n.split(","),t=0;t<n.length;t++)i=i.concat("<div class='e-tag'>"+n[t]+"<\/div>");return i.concat("<\/div>")},i.prototype._getColumnKeyItems=function(n,t,i,r){var f=this.getRsc("helpers",n),o,s,w=this.ctx.root.dataSource,l,y=f.model.keyField,k=f.model.fields.collapsibleCards,d=f.model.query,u=d.clone(),e,a,i,v,p,h,c,b;if(f._filterCollection.length<=0&&f.model.allowSearching&&f._searchInput.val().length<=0?(u.queries=[],u.where(ej.Predicate.or(f.keyPredicates))):u.queries=u.queries.slice(0,u.queries.length-1),t||(o=typeof this.data.key=="object"?this.data.key:this.data.key.split(",")),e=[],t){if(i==!1&&r==!1)return o=f.model.fields.collapsibleCards.key,typeof o=="object"&&(o=o[0]),o;if(i==!0&&r==!1)return e=this.parent.parent.parent.key,s=this.parent.parent.parent.fkey,u.queries=[],u=u.where(ej.Predicate.or(e)),a=new ej.DataManager(s).executeLocal(u).result,this.parent.parent.parent.cards=a,ej.isNullOrUndefined(a)?0:a.length;if(i==!0&&r==!0)return this.parent.parent.parent.parent.cards}else for(v=0;v<o.length;v++)e.push(new ej.Predicate(y,ej.FilterOperators.equal,o[v],!1));return(u=u.where(ej.Predicate.or(e)),w.GROUPGUID?s=new ej.DataManager(this.ctx.items).executeLocal(u).result:e.length!=0&&(s=new ej.DataManager(w).executeLocal(u).result),l=f.model.fields.priority,l&&s.sort(function(n,t){return n[l]-t[l]}),!ej.isNullOrUndefined(k)&&ej.isNullOrUndefined(t)&&(p=[],u.queries=[],h=f.model.fields.collapsibleCards.field,typeof h=="object"&&(h=h[0]),c=f.model.fields.collapsibleCards.key,typeof c=="object"&&(c=c[0]),h!=y||h==y))?(e=[],e.push(new ej.Predicate(h,ej.FilterOperators.notEqual,c,!0)),p.push(new ej.Predicate(h,ej.FilterOperators.equal,c,!0)),u=u.where(ej.Predicate.or(e)),b=new ej.DataManager(s).executeLocal(u).result,this.parent.parent.fkey=s,this.parent.parent.key=p,b):s},i.prototype._kbnThumbStart=function(t){var i=n(t.originalEvent.target);return(i.hasClass("e-kanbancard")||i.parents(".e-kanbancard").hasClass("e-kanbancard"))&&!ej.isDevice()?!1:!0},i.prototype._enableDragandScroll=function(){this.model.allowDragAndDrop&&this.KanbanDragAndDrop&&this.KanbanDragAndDrop._addDragableClass();this.model.allowScrolling&&(this.initialRender&&this.element.find(".e-kanbancontent").length>0?(ej.isNullOrUndefined(this.model.fields.swimlaneKey)||(this._swimlaneRows=this.element.find(".e-swimlanerow")),this.KanbanScroll._renderScroller()):this.KanbanScroll._refreshScroller({requestType:"refresh"}))},i.prototype._cardCollapse=function(){this._collapsedCards.length>0&&this.toggleCard(this._collapsedCards)},i.prototype._getSwimlaneCount=function(){var i=this.getContentTable().find(".e-swimlanerow .e-slcount"),r=[],l=this,t,h,c;for(this.model.swimlaneSettings.headers.length>0?r=kObj._slText:n.map(l.currentViewData,function(n){r.push(n.key)}),t=0;t<i.length;t++){var a=this.getContentTable().find(".e-swimlanerow .e-slkey").eq(t).html(),u=r.indexOf(a),f=new ej.DataManager(this.currentViewData[u].items).executeLocal((new ej.Query).where(ej.Predicate.or(this.keyPredicates)));if(f.length!=this.currentViewData[u].count&&(i.eq(t).html(i.eq(t).html().replace(this.currentViewData[u].count,f.length)),f.length==1)){var e=this.localizedLabels.SwimlaneCaptionFormat,o=e.search("{{else}}"),s=e.search("count == 1");o!=-1&&s!=-1&&(h=e.slice(s+13,o),c=i.eq(t).html().slice(4),i.eq(t).html(i.eq(t).html().replace(c,h)))}}},i.prototype._swimlaneCollapse=function(){var i,t;if(this._collapsedSwimlane.length>0){for(i=this.element.find(".e-swimlanerow"),t=0;t<i.length;t++)n.inArray(i.eq(t).attr("id"),this._collapsedSwimlane)!=-1&&this.KanbanSwimlane._toggleSwimlaneRow(n(i.eq(t)).find(".e-rowcell .e-slexpandcollapse"));this.model.allowScrolling&&this.KanbanScroll._refreshSwimlaneToggleScroller()}},i.prototype.updateCard=function(n,t){this.KanbanCommon._kanbanUpdateCard(n,t)},i.prototype._eventBindings=function(){var r,i,f,u,t,e;if(this._kanbanRows=this.getContentTable().get(0).rows,r=n(this._kanbanRows).not(".e-swimlanerow"),this._currentJsonData.length!=0&&this.model.queryCellInfo!=null)for(i=0;i<r.length;i++)for(f=r[i],u=this.model.columns,t=0;t<u.length;t++)e=n(f).find(".e-rowcell")[t],this._cellEventTrigger(e,u[t])},i.prototype._cellEventTrigger=function(t,i){for(var u=n(t).find(".e-kanbancard"),r=0;r<u.length;r++){var f=u[r].id,e=this.KanbanCommon._getKanbanCardData(this._currentJsonData,f),o={card:u[r],cell:t,column:i,data:e[0]};this._trigger("queryCellInfo",o)}},i.prototype._getLocalizedLabels=function(){return ej.getLocalizedConstants(this.sfType,this.model.locale)},i}(ej.WidgetBase);window.ej.widget("ejKanban","ej.Kanban",new t)}(jQuery);ej.Kanban.Actions={Filtering:"filtering",BeginEdit:"beginedit",Edit:"edit",Save:"save",Add:"add",Delete:"delete",Cancel:"cancel",Refresh:"refresh",Search:"searching",Print:"print"};ej.Kanban.EditingType={String:"stringedit",Numeric:"numericedit",Dropdown:"dropdownedit",DatePicker:"datepicker",DateTimePicker:"datetimepicker",TextArea:"textarea",RTE:"rteedit"};ej.Kanban.EditMode={Dialog:"dialog",DialogTemplate:"dialogtemplate",ExternalForm:"externalform",ExternalFormTemplate:"externalformtemplate"};ej.Kanban.FormPosition={Bottom:"bottom",Right:"right"};ej.Kanban.Type={Column:"column",Swimlane:"swimlane"};ej.Kanban.SelectionType={Multiple:"multiple",Single:"single"};ej.Kanban.MenuItem={AddCard:"Add Card",EditCard:"Edit Card",DeleteCard:"Delete Card",TopofRow:"Top of Row",BottomofRow:"Bottom of Row",MoveUp:"Move Up",MoveDown:"Move Down",MoveLeft:"Move Left",MoveRight:"Move Right",MovetoSwimlane:"Move to Swimlane",HideColumn:"Hide Column",VisibleColumns:"Visible Columns",PrintCard:"Print Card"};ej.Kanban.Target={Header:"header",Content:"content",Card:"card",All:"all"};ej.Kanban.Locale=ej.Kanban.Locale||{}});
