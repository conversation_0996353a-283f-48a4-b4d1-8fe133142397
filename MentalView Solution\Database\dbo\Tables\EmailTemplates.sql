﻿CREATE TABLE [dbo].[EmailTemplates] (
    [EmailTemplateId]         BIGINT         IDENTITY (1, 1) NOT NULL,
    [TenantId]                BIGINT         NOT NULL,
    [EmailTemplateCategoryId] BIGINT         NULL,
    [Title]                   NVARCHAR (200) CONSTRAINT [DF_EmailTemplates_Title] DEFAULT ('') NOT NULL,
    [Subject]                 NVARCHAR (300) CONSTRAINT [DF_EmailTemplates_Subject] DEFAULT ('') NOT NULL,
    [Body]                    NTEXT          CONSTRAINT [DF_EmailTemplates_Body] DEFAULT ('') NOT NULL,
    [RowIndex]                INT            NULL,
    CONSTRAINT [PK_EmailTemplates] PRIMARY KEY CLUSTERED ([EmailTemplateId] ASC),
    CONSTRAINT [FK_EmailTemplates_EmailTemplateCategories] FOREIGN KEY ([EmailTemplateCategoryId]) REFERENCES [dbo].[EmailTemplateCategories] ([EmailTemplateCategoryId]) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT [FK_EmailTemplates_Tenants] FOREIGN KEY ([TenantId]) REFERENCES [dbo].[Tenants] ([TenantId]) ON DELETE CASCADE ON UPDATE CASCADE
);

