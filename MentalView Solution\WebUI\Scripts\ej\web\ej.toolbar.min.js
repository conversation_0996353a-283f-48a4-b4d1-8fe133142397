/*!
*  filename: ej.toolbar.min.js
*  version : *********
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min","./ej.tooltip.min"],n):n()})(function(){(function(n,t){t.widget("ejToolbar","ej.Toolbar",{element:null,model:null,validTags:["div","span"],_setFirst:!1,_rootCSS:"e-toolbar",angular:{terminal:!1},defaults:{height:"",width:"",enabled:!0,hide:!1,disabledItemIndices:[],enabledItemIndices:[],enableSeparator:!1,orientation:"horizontal",enableRTL:!1,isResponsive:!1,showRoundedCorner:!1,htmlAttributes:{},dataSource:null,Items:{id:"id",tooltipText:"tooltipText",imageUrl:"imageUrl",text:"text",imageAttributes:"imageAttributes",spriteCssClass:"spriteCssClass",htmlAttributes:"htmlAttributes",group:"group",template:"template",overflow:"overflow"},query:null,responsiveType:"popup",fields:{id:"id",tooltipText:"tooltipText",imageUrl:"imageUrl",text:"text",imageAttributes:"imageAttributes",spriteCssClass:"spriteCssClass",htmlAttributes:"htmlAttributes",group:"group",template:"template",overflow:"overflow"},tooltipSettings:{associate:"mouseenter",showShadow:!0,position:{stem:{horizontal:"left",vertical:"top"}},tip:{size:{width:5,height:5},adjust:{xValue:17,yValue:17}},isBalloon:!1},cssClass:"",targetID:null,create:null,click:null,itemHover:null,itemLeave:null,overflowOpen:null,overflowClose:null,destroy:null},dataTypes:{enabled:"boolean",hide:"boolean",enableSeparator:"boolean",disabledItemIndices:"data",enabledItemIndices:"data",orientation:"enum",enableRTL:"boolean",showRoundedCorner:"boolean",isResponsive:"boolean",dataSource:"data",query:"data",fields:"data",cssClass:"string",htmlAttributes:"data",targetID:"string",responsiveType:"enum"},hide:function(){if(!this.model.enabled&&this.element.css("display")=="none")return!1;this.element.css("display","none");this.model.hide=!0},show:function(){if(!this.model.enabled&&this.element.css("display")!="none")return!1;this.element.css("display","block");this.model.hide=!1},_init:function(){this.model.targetID!=null&&this.element.html(n("#"+this.model.targetID).find("ul,ol"));this._cloneElement=n(this.element).clone();this._localDataSource=null;this.model.dataSource!=null?this._generateTemplate():this.model.Items!=null&&this._generateTemplate();this.model.dataSource instanceof t.DataManager||this._initialize();this._renderTooltip(this.model.tooltipSettings)},_setModel:function(t){var i,r;for(i in t)switch(i){case"height":this._setHeight(t[i]);break;case"width":this.model.width=t[i];this._setWidth();this._bindUnbindWidth();break;case"enabled":this._controlStatus(t[i]);break;case"disabledItemIndices":this._disableItemByIndex(t[i]);t[i]=this.model.disabledItemIndices;break;case"enabledItemIndices":this._enableItemByIndex(t[i]);break;case"isResponsive":this.model.isResponsive=t[i];this.model.isResponsive?(this._responsiveLayout(),this._renderTooltip(this.model.tooltipSettings)):this._removeResponsive();break;case"hide":this._controlVisibleOptions(t[i]);break;case"orientation":this._setOrientation(t[i]);this.model.orientation=t[i];this._wireResizing();break;case"tooltipSettings":this.model.tooltipSettings=n.extend(!0,this.model.tooltipSettings,t[i]);this._renderTooltip(this.model.tooltipSettings);break;case"enableRTL":this._enableRTL(t[i]);break;case"showRoundedCorner":this._roundedCorner(t[i]);break;case"cssClass":this._setSkin(t[i]);break;case"htmlAttributes":this._addAttr(t[i]);break;case"enableSeparator":this.model.enableSeparator=t[i];this._renderToolbarSeparator();break;case"fields":n.extend(this.model.fields,t[i]);break;case"query":this.model.query=t[i];break;case"dataSource":for(r in t)r=="fields"&&n.extend(this.model.fields,t[r]),r=="query"&&(this.model.query=t[r]);this._refreshTagItems(i,t[i]);break;case"Items":n.extend(this.model.Items,t[i]);this._refreshTagItems(i,t[i]);break;case"targetID":this.model.targetID=t[i];this._setTargetID()}},_setTargetID:function(){this.element.removeAttr("role tabindex aria-disabled style");n(this._spantag).remove();n(this._liTemplte).remove();this._liTemplte=this._tipRes=null;this.element.removeClass("e-widget e-box e-toolbarspan e-rtl");this.element.find("ul,ol").removeClass("e-ul e-horizontal e-vertical e-separator e-comnrtl");this.element.find("li").removeClass("e-tooltxt e-comnrtl");this.element.find("li").removeAttr("aria-label");this._init()},_refreshTagItems:function(n,t){this.model[n]=t;this.element.empty();this._generateTemplate();this._initialize()},_setHeight:function(n){this.element.css("height",n)},_setWidth:function(){var t,u;if(this.model.width=="auto"){var r=parseFloat(this.element.css("border-left-width"))+parseFloat(this.element.css("border-right-width")),f=this.element.find("ul").children("li"),i=this.element.find("ul"),e;for(e=this.model.responsiveType=="inline"?this.element.find(".e-arrow-sans-down"):this.element.find(".e-toolbar-res-arrow"),t=0,u=i.length;t<u;t++)i[t]!=null&&n(i[t]).hasClass("e-separator")&&(r+=parseFloat(i.eq(t).css("border-right-width"))+parseFloat(i.eq(t).css("border-left-width")));for(e[0]!=null&&(r+=this.model.responsiveType=="inline"?Math.round(this.element.find(".e-arrow-sans-down").outerWidth())+8:Math.round(this.element.find(".e-toolbar-res-arrow").outerWidth())+8),t=0,u=f.length;t<u;t++)f[t]!=null&&(r+=Math.round(f.eq(t).outerWidth()));this.element.css("width",Math.ceil(r))}else this.element.css("width",this.model.width)},_bindUnbindWidth:function(){this.model.width!="auto"?this._off(n(window),"resize",this._widthHandler):this._on(n(window),"resize",this._widthHandler)},_widthHandler:function(){this._setWidth(this.model.width)},_setOrientation:function(n){n!=t.Orientation.Vertical?(this.element.removeClass(this.model.cssClass).addClass("e-toolbarspan "+this.model.cssClass),this.itemsContainer.removeClass("e-ul e-vertical").addClass("e-ul e-horizontal")):(this.element.removeClass("e-toolbarspan "+this.model.cssClass).addClass(this.model.cssClass),this.itemsContainer.removeClass("e-ul e-horizontal").addClass("e-ul e-vertical"));this.items.addClass("e-tooltxt")},_reSizeHandler:function(){var r,u,f,e,i,t;for(this._isResized&&this._liTemplte.children().length>0&&(r=this.element.children("ol.e-show, ul.e-show"),u=this._liTemplte.children("ol.e-show, ul.e-show"),r.length>0||u.length>0?n(this._liTemplte.children()).appendTo(this.element):n(this._liTemplte.children()).insertBefore(n(this.element).find("span.e-res-pos")),this._renderToolbarSeparator(),this.model.responsiveType!="inline"&&this._liTemplte.addClass("e-display-none"),this._contstatus=!1),this._elementWidth=typeof getComputedStyle=="function"?parseFloat(window.getComputedStyle(this.element[0]).width):this.element.width()-1,this._liWidth=this._spanWidth,f=this.element.children("ol.e-show:visible, ul.e-show:visible"),e=this.element.children("ol:visible:not(.e-show), ul:visible:not(.e-show)"),this._spantag.removeClass("e-display-block").addClass("e-display-none"),this._ensureOverflow(f),i=0,t=0;t<this.itemsContainer.length;t++)i+=n(this.itemsContainer[t]).outerWidth(!0);i>this.element.width()&&this._ensureOverflow(e);this.model.responsiveType=="inline"&&(this._liTemplte.removeClass("e-normal"),this._liTemplte.css("width",this.element.width()));this._isResized=!0},_ensureOverflow:function(t){for(var r,i=0;i<t.length;i++)this._liWidth+=n(t[i]).outerWidth(!0)+8,this._liWidth>=this.element.width()&&(this._spantag.hasClass("e-display-block")||this._spantag.removeClass("e-display-none").addClass("e-display-block"),this._spantag.addClass("e-res-pos"),this._liTemplte.addClass("e-normal"),this._spantag.outerWidth(!0)!=this._spanOuterWidth&&(r=this._spantag.outerWidth(!0)-this._spantag.outerWidth(),this._liWidth-(n(t[i]).outerWidth(!0)+8)+r>=this._elementWidth&&this._liTemplte.append(t[i-1])),this._liTemplte.append(t[i]))},_getZindexPartial:function(){return t.util.getZindexPartial(this.element,this.popup)},_getOffset:function(n){return t.util.getOffset(n)},_btnMouseClick:function(i){var u,f,r,h,o=1,a=this.element.outerWidth(),v=this.element.outerHeight(),s=n(this._liTemplte).outerWidth(),c=n(window).width(),e=n(window).scrollLeft(),l;n(i.currentTarget).hasClass("e-disable")||(l=this._getZindexPartial(),f=this._getOffset(this.element),r=this.model.enableRTL?f.left:f.left+a-s,h=f.top+v-o+2,r=r<e?e+o:r+s>c+e?c-s+e-o:r,this._contstatus?(this._liTemplte.addClass("e-display-none"),this.model.responsiveType=="inline"&&this._spantag.removeClass("e-active"),this._contstatus=!1,u={currentTarget:i.currentTarget,clientX:i.clientX,clientY:i.clientY,cancel:i.cancellable},this._trigger("overflowClose",u)):(this._liTemplte.removeClass("e-display-none"),this.model.responsiveType!="inline"&&this._liTemplte.css({top:h+"px",left:r+"px","z-index":l}),this.model.responsiveType=="inline"&&this._spantag.addClass("e-active"),this._contstatus=!0,u={currentTarget:i.currentTarget,clientX:i.clientX,clientY:i.clientY,cancel:i.cancellable},this._trigger("overflowOpen",u),this.model.responsiveType!="inline"&&this._on(t.getScrollableParents(this.element),"scroll",this._hidecontext),this.element.bind("click",n.proxy(this._hidecontext,this))),this._contstatus&&this.model.responsiveType!="inline"&&this._on(n(document),"mouseup",this._documentClick),this._contstatus||this._off(n(document),"mouseup",this._documentClick),this._removeListHover())},_hidecontext:function(i){n(i.target).is(n("#"+this.element[0].id+"_target"))||this.model.responsiveType=="inline"||(this._liTemplte.addClass("e-display-none"),this._contstatus=!1,this.element.unbind("click",n.proxy(this._hidecontext,this)),this._off(t.getScrollableParents(this.element),"scroll",this._hidecontext),this._off(n(document),"mouseup",this._documentClick))},_documentClick:function(t){if(!n(t.target).is(n("#"+this.element[0].id+"_target"))&&!(n(t.target).closest("div#"+this.element[0].id+"_hiddenlist").length!=0||n(t.target).parents().hasClass("e-ddl-popup"))){this._liTemplte.addClass("e-display-none");this._contstatus=!1;var i={currentTarget:t.currentTarget,clientX:t.clientX,clientY:t.clientY,cancel:t.cancellable};this._trigger("overflowClose",i);this._off(n(document),"mouseup",this._documentClick)}},_setSkin:function(n){this.element.removeClass(this.model.cssClass).addClass(n);var t="e-toolbarTooltip "+n;this._subControlsSetModel("cssClass",t)},_subControlsSetModel:function(i,r){t.isNullOrUndefined(this._tipToolbar)||n(this.target).ejTooltip("option",i,r);t.isNullOrUndefined(this._tipRes)||n(this._liTemplte).ejTooltip("option",i,r)},_destroy:function(){this.element.html("");t.isNullOrUndefined(this._tipToolbar)||n(this.target).ejTooltip("destroy");t.isNullOrUndefined(this._tipRes)||n(this._liTemplte).ejTooltip("destroy");this._cloneElement.removeClass("e-toolbar e-js");this.element.replaceWith(this._cloneElement);this._liTemplte&&this._liTemplte.remove();this._unWireResizing()},_initialize:function(){this.element.attr({role:"toolbar",tabindex:"0"});var n=this.element.children().find("li");this._focusEnable=!0;this._eleClick=!1;this._renderControl();this._responsiveLayout();this._wireEvents()},_responsiveLayout:function(){var r,i;if(this._roundedCorner(this.model.showRoundedCorner),this.model.isResponsive&&this.model.orientation=="horizontal"){for(t.isNullOrUndefined(this._liTemplte)||(t.isNullOrUndefined(this._tipRes)||this._tipRes.destroy(),n(this._liTemplte).remove()),this._spantag=n("<span id='"+this.element[0].id+"_target' class='e-icon e-toolbar-res-arrow e-rel-position e-display-block' unselectable='on'><\/span>"),this.model.responsiveType=="inline"&&this._spantag.removeClass("e-toolbar-res-arrow").addClass("e-inlinearrow e-arrow-sans-down"),r=this.element.children("ol.e-show, ul.e-show"),r.length>0?this.element[0].insertBefore(this._spantag[0],this.element[0].childNodes[0]):this._spantag.appendTo(this.element),this._liTemplte=n("<div id='"+this.element[0].id+"_hiddenlist' class='e-responsive-toolbar e-display-none e-abs-position "+n(this.element)[0].className+"'><\/div>"),this.model.responsiveType=="inline"&&this._liTemplte.removeClass("e-abs-position").addClass("e-inline"),this._spanWidth=this._spantag.outerWidth(!0)+8,this.model.enableRTL&&this._liTemplte.addClass("e-rtl"),this._isResized=!1,this._elementWidth=typeof getComputedStyle=="function"?parseFloat(window.getComputedStyle(this.element[0]).width):this.element.width()-1,this._liWidth=this._spanWidth,i=0;i<this.itemsContainer.length;i++)this._liWidth+=n(this.itemsContainer[i]).outerWidth(!0);this._liWidth>this._elementWidth?this._reSizeHandler():this._spantag.removeClass("e-display-block").addClass("e-display-none");this.model.responsiveType=="inline"?n(this.element[0]).append(this._liTemplte):n("body").append(this._liTemplte);this._renderTooltip(this.model.tooltipSettings)}this._on(n("#"+this.element[0].id+"_target"),"mousedown",this._btnMouseClick);this._wireResizing();this._controlVisibleOptions(this.model.hide);this._enableRTL(this.model.enableRTL);this._disabledItems=this.model.disabledItemIndices;this._controlStatus(this.model.enabled);this.model.disabledItemIndices=this._disabledItems;this.model.disabledItemIndices.length!=0&&this._disableItemByIndex(this.model.disabledItemIndices);this.model.enabledItemIndices.length!=0&&this._enableItemByIndex(this.model.enabledItemIndices);this._setWidth()},_removeResponsive:function(){n(this._spantag).remove();t.isNullOrUndefined(this._tipRes)||n(this._liTemplte).ejTooltip("destroy");var i=n(this._liTemplte).children("ul");n(this._liTemplte).detach();n(this.element).append(i);this.itemsContainer=this.element.children("ol,ul");this.items=this.itemsContainer.children("li");this._off(this.items,"mouseup");this._off(this.items,"mousedown");this._off(this.element,"mousedown");this._wireEvents();this._wireResizing();this._setWidth()},_controlVisibleOptions:function(n){n!=!1?this.hide():this.show()},_controlStatus:function(n){n!=!0?this.disable():this.enable()},_roundedCorner:function(n){n?(this.element.addClass("e-corner"),this._liTemplte&&this._liTemplte.addClass("e-corner")):(this.element.removeClass("e-corner"),this._liTemplte&&this._liTemplte.removeClass("e-corner"));this._subControlsSetModel("showRoundedCorner",n)},_generateTemplate:function(){var n=this,i;this.element.css("visibility","hidden");this.model.dataSource instanceof t.DataManager?(i=this.model.dataSource.executeQuery(this.model.query),i.done(function(t){n._generateGroup(t.result);n._initialize();n.element.css("visibility","")})):this.model.dataSource!=null?(n._generateGroup(n.model.dataSource),n.element.css("visibility","")):(n._generateGroup(n.model.Items),n.element.css("visibility",""))},_generateGroup:function(n){var r=this,f,u,t,e,i;for(this._localDataSource=n,f=-1,u=[],t=0;t<n.length;t++)if(this._isNewGroup(n[t][this.model.fields.group],u)){for(u[++f]=n[t][this.model.fields.group],e=-1,r.itemsSource=[],i=t;i<n.length;i++)n[t][this.model.fields.group]==n[i][this.model.fields.group]&&(r.itemsSource[++e]=n[i]);r.element.append(r._generateTagitems())}},_isNewGroup:function(n,t){if(!t)return!0;for(var i=0;i<t.length;i++)if(n==t[i])return!1;return!0},_renderControl:function(){this.element.addClass("e-widget e-box");this._renderToolbarItems();this._addAttr(this.model.htmlAttributes);this._setOrientation(this.model.orientation);this._renderToolbarSeparator();this._setHeight(this.model.height);this._bindUnbindWidth()},_renderToolbarItems:function(){this.target=this.element[0];this.itemsContainer=this.element.children("ol,ul");this.itemsContainer.children("ol,ul").remove();this.items=this.itemsContainer.children("li");for(var i=0;i<this.items.length;i++)t.isNullOrUndefined(n(this.items[i]).attr("title"))&&t.isNullOrUndefined(n(this.items[i]).attr("aria-label"))&&n(this.items[i]).attr("aria-label",this.items[i].id);this._liCount=this.items.length},_generateTagitems:function(){var i,n;for(i=this.itemsSource,this.ultag=t.buildTag("ul"),n=0;n<i.length;n++)i[n][this.model.fields.overflow]=="show"&&this.ultag.addClass("e-show"),this.ultag.append(this._generateLi(i[n]));return this.ultag},_generateLi:function(n){var i,u,r;return i=t.buildTag("li"),n[this.model.fields.id]?i.attr("id",n[this.model.fields.id]):n[this.model.fields.text]&&i.attr("id",n[this.model.fields.text]),n[this.model.fields.tooltipText]&&i.attr("title",n[this.model.fields.tooltipText]),n[this.model.fields.imageUrl]&&n[this.model.fields.imageUrl]!=""&&(u=t.buildTag("img.e-align","",{},{src:n[this.model.fields.imageUrl],alt:n[this.model.fields.text]}),n[this.model.fields.imageAttributes]&&this._setAttributes(n[this.model.fields.imageAttributes],u),i.append(u)),n[this.model.fields.spriteCssClass]&&n[this.model.fields.spriteCssClass]!=""&&(r=t.buildTag("div.e-align "+n[this.model.fields.spriteCssClass]+" e-spriteimg"),i.append(r)),n[this.model.fields.text]&&n[this.model.fields.text]!=""&&i.append(n[this.model.fields.text]),n[this.model.fields.htmlAttributes]&&this._setAttributes(n[this.model.fields.htmlAttributes],i),n[this.model.fields.template]&&(r=t.buildTag("div .e-align",n[this.model.fields.template]),i.append(r)),i},_setAttributes:function(t,i){for(var r in t)n(i).attr(r,t[r])},_renderToolbarSeparator:function(){var t,i;if(this.model.enableSeparator){for(t=0,i=this.itemsContainer.length-1;t<i;t++)n(this.itemsContainer[t]).addClass("e-separator");if(this.itemsContainer.length==1)for(t=0,i=this.itemsContainer[0].children.length;t<i;t++)t==i-1?n(this.itemsContainer[0].children[t]).removeClass("e-separator"):n(this.itemsContainer[0].children[t]).addClass("e-separator")}else this.itemsContainer.removeClass("e-separator").find(".e-separator").removeClass("e-separator");this._setWidth()},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.element.addClass(n):t=="disabled"&&n=="disabled"?i.disable():i.element.attr(t,n)})},_enableRTL:function(n){n?(this.element.addClass("e-rtl"),this.items.addClass("e-comnrtl"),this.model.orientation==t.Orientation.Horizontal&&this.itemsContainer.addClass("e-comnrtl"),this._liTemplte&&this._liTemplte.addClass("e-rtl")):(this.element.removeClass("e-rtl"),this.items.removeClass("e-comnrtl"),this.model.orientation==t.Orientation.Horizontal&&this.itemsContainer.removeClass("e-comnrtl"),this._liTemplte&&this._liTemplte.removeClass("e-rtl"));this.model.enableRTL=n;this._subControlsSetModel("enableRTL",n);this._renderToolbarSeparator()},_renderTooltip:function(i){var r=n.extend(!0,{},i);r.cssClass=t.isNullOrUndefined(r.cssClass)?"e-toolbarTooltip "+this.model.cssClass:r.cssClass+" e-toolbarTooltip "+this.model.cssClass;r.enableRTL=this.model.enableRTL;r.showRoundedCorner=this.model.showRoundedCorner;r.target="li[data-content], li[title]";r.beforeOpen=this._showTooltip;this._tipToolbar=n(this.target).ejTooltip(r).data("ejTooltip");this.model.isResponsive&&!t.isNullOrUndefined(this._liTemplte)&&(this._tipRes=n(this._liTemplte).ejTooltip(r).data("ejTooltip"))},_showTooltip:function(t){var i=n(t.event.currentTarget),r=t.event.target;i.hasClass("e-disable")&&(t.cancel=!0)},_hideTooltip:function(){t.isNullOrUndefined(this._tipToolbar)||(this._tipToolbar.hide(),this._tipToolbar._isHidden=!1);t.isNullOrUndefined(this._tipRes)||(this._tipRes.hide(),this._tipRes._isHidden=!1)},_addOverlay:function(i){for(var u,r=0;r<i.length;r++)n(i[r]).hasClass("e-disable")||(n(i[r]).addClass("e-disable"),u=t.buildTag("div.e-item-overlay"),n(i[r]).append(u))},_removeOverlay:function(t){for(var i=0;i<t.length;i++)n(t[i]).children(".e-item-overlay").remove()},disableItem:function(t){var i=n(t);i==null||i.length<=0||(i.hasClass("e-disable")||(i.attr("aria-disabled",!0).removeAttr("aria-label"),this._addOverlay(i)),i.removeClass("e-hover e-active").attr("data-aria-selected",!1))},enableItem:function(t){var i=n(t);i==null||i.length<=0||(i.removeClass("e-disable").attr("aria-disabled",!1).removeClass("e-disable"),this._removeOverlay(i))},disableItemByID:function(n){var t=this.itemsContainer.find("li#"+n);t==null||t.length<=0||this.disableItem(t)},enableItemByID:function(n){var t=this.itemsContainer.find("li#"+n);t==null||t.length<=0||this.enableItem(t)},_enableItemByIndex:function(t){var r,i;if(!this.model.enabled)return!1;if(t.length!=0)for(i=0;i<t.length;i++)n.inArray(t[i],this.model.disabledItemIndices)>-1&&(r=n.inArray(t[i],this.model.disabledItemIndices),this.enableItem(this.items[this.model.disabledItemIndices[r]]),this.model.disabledItemIndices.splice(r,1))},_disableItemByIndex:function(t){var i;if(!this.model.enabled)return!1;for(this._disabledItems=this.model.disabledItemIndices,i=0;i<t.length;i++)n.inArray(t[i],this.model.disabledItemIndices)<0&&this._disabledItems.push(parseInt(t[i]));for(i=0;i<this.items.length;i++)n.inArray(i,this.model.disabledItemIndices)>-1&&this.disableItem(this.items[i]);this.model.disabledItemIndices=this._disabledItems},disable:function(){if(this.element.attr("aria-disabled")=="true")return!1;this.element.attr("aria-disabled",!0).removeAttr("aria-label");this._addOverlay(this.items);this.model.isResponsive&&this.model.orientation=="horizontal"&&this._spantag.addClass("e-disable");this.model.enabled=!1;this._subControlsSetModel("enabled",!1)},enable:function(){if(this.model.disabledItemIndices=[],this.element.attr("aria-disabled")=="false")return!1;this.items.removeClass("e-disable");this._removeOverlay(this.items);this.element.attr("aria-disabled",!1);this.model.isResponsive&&this.model.orientation=="horizontal"&&this._spantag.removeClass("e-disable");this.model.enabled=!0;this._subControlsSetModel("enabled",!0)},refresh:function(){this._reSizeHandler()},selectItem:function(t){var i=n(t);i==null||i.length<=0||(i.addClass("e-active").attr("data-aria-selected",!0),this._activeItem=n(this.items).index(i))},deselectItem:function(t){var i=n(t);i==null||i.length<=0||i.removeClass("e-active").attr("data-aria-selected",!1)},selectItemByID:function(n){var t=this.itemsContainer.find("li#"+n);t==null||t.length<=0||this.selectItem(t)},deselectItemByID:function(n){var t=this.itemsContainer.find("li#"+n);t==null||t.length<=0||this.deselectItem(t)},removeItem:function(t){var r,u,i;if(this.model.disabledItemIndices=[],r=n(t),u=0,r!=null&&!(r.length<=0))for(r.remove(),this.items=this.itemsContainer.children("li"),i=0;i<this.items.length;i++)n(this.items[i]).hasClass("e-disable")&&(this.model.disabledItemIndices[u++]=i)},removeItemByID:function(n){var t=this.itemsContainer.find("li#"+n);t==null||t.length<=0||this.removeItem(t)},_wireResizing:function(){this.model.isResponsive&&this.model.orientation=="horizontal"?n(window).bind("resize",n.proxy(this._reSizeHandler,this)):this._unWireResizing()},_unWireResizing:function(){n(window).unbind("resize",n.proxy(this._reSizeHandler,this))},_wireEvents:function(){this._on(this.element,"mousedown",this._clickEventHandler);this._on(this.element,"focus",this._focusElement);this._on(this.element,"blur",this._targetBlur);this._on(this.items,"mouseenter",this._onItemHover);this._on(this.items,"mouseleave",this._onItemLeave);this._on(this.items,"mousedown",this._onItemClick);this._on(this.items,"mouseup",this._onItemClick);this._on(n(document),"click",this._onDocumentClick)},_onDocumentClick:function(t){!t.target.classList.contains("e-toolbar")&&!t.target.classList.contains("e-toolbar-res-arrow")&&!n(t.target).parents(".e-responsive-toolbar").length>0&&(this._eleClick=!1)},_onItemHover:function(t){var i=n(t.currentTarget),u=t.target,r;i.hasClass("e-disable")?clearTimeout(this._tipToolbar.mouseTimer):(this.items.removeClass("e-hover"),i.addClass("e-hover"),r={currentTarget:i,target:u,status:this.model.enabled},this._trigger("itemHover",r))},_onItemClick:function(i){var r,u,f;if(n(i.currentTarget).hasClass("e-disable")||i.which!=1)return!1;r=i.currentTarget;u=i.target;t.isDevice()||this._hideTooltip();i.type=="mousedown"?(this._focusEnable=!1,n(r).addClass("e-active"),this._focusedItem=this._currentItem=n(r)):i.type=="mouseup"&&this._eleClick&&(this._removeSelection(),n(r).hasClass("e-disable")||(f={currentTarget:r,text:n(r).attr("data-content"),target:u,status:this.model.enabled,event:i},this._activeItem=this.model.isResponsive&&this._contstatus&&n(r).closest(".e-responsive-toolbar").length>0?this._liTemplte.find(".e-tooltxt").index(r):n(this.items).index(r),this._trigger("click",f)))},_onItemLeave:function(t){var i=n(t.currentTarget),u=t.target,r;i.hasClass("e-disable")||(this._removeSelection(),i.removeClass("e-hover"),r={currentTarget:i,target:u,status:this.model.enabled},this._trigger("itemLeave",r))},_onKeyPress:function(i){var u,r,f,e,o;if(u=i.keyCode?i.keyCode:i.which?i.which:i.charCode,this.model.isResponsive&&this._liTemplte.find(".e-tooltxt").length>0?(this._keyPressed=!0,this._items=this._contstatus?this._liTemplte.find(".e-tooltxt"):this.element.find(".e-tooltxt"),r=this._items.filter(".e-tooltxt:visible:not(.e-hidden, .e-disable)")):(this._keyPressed=!1,r=this.items.filter(".e-tooltxt:visible:not(.e-hidden, .e-disable)")),this._focusedItem?(f=this._focusedItem,this._focusedItem=null):f=r.filter(".e-hover"),i.type=="keydown")u==9&&i.shiftKey&&n(r[r.index(f)-1]).length>0?(i.preventDefault(),e=n(r[r.index(f)-1])):u==9&&n(r[r.index(f)+1]).length>0&&!i.shiftKey?(i.preventDefault(),e=n(r[r.index(f)+1])):(u==38||u==39)&&this.model.orientation!=t.Orientation.Vertical||(u==39||u==40)&&this.model.orientation==t.Orientation.Vertical?(i.preventDefault(),e=n(r[r.index(f)+1]).length>0?n(r[r.index(f)+1]):r.first()):(u==37||u==40)&&this.model.orientation!=t.Orientation.Vertical||(u==37||u==38)&&this.model.orientation==t.Orientation.Vertical?(i.preventDefault(),e=n(r[r.index(f)-1]).length>0?n(r[r.index(f)-1]):r.last()):u==33||u==36?(i.preventDefault(),e=r.first()):(u==34||u==35)&&(i.preventDefault(),e=r.last()),e&&(this._removeListHover(),e.addClass("e-hover"));else switch(u){case 13:case 32:if(i.preventDefault(),!f[0])break;o={currentTarget:f[0],target:n(f)[0],status:this.model.enabled,event:i};this._trigger("click",o);break;case 27:i.preventDefault();this.element.blur()}},_removeListHover:function(){n(this.items).removeClass("e-hover")},_addListHover:function(){var n=this._getActiveItem();n.hasClass("e-disable")||n.addClass("e-hover").focus()},_getActiveItem:function(){return this.model.isResponsive&&this._keyPressed?n(this._items[this._activeItem]):n(this.items[this._activeItem])},_targetBlur:function(n){n.preventDefault();this.element.focusout().removeClass("e-focus");this._removeListHover();this._off(this.element,"keyup",this._onKeyPress)._off(this.element,"keydown",this._onKeyPress);this._trigger("focusOut")},_clickEventHandler:function(){this._clicked=!0;this._eleClick=!0},_removeSelection:function(){this._currentItem&&this._currentItem.attr("data-aria-selected")!="true"&&this._currentItem.removeClass("e-active")},_focusElement:function(){if(!this.element.hasClass("e-focus")&&(this.element.addClass("e-focus"),this._on(this.element,"keyup",this._onKeyPress)._on(this.element,"keydown",this._onKeyPress),!this._focusEnable)){this._focusEnable=!0;return}this._removeListHover();this._activeItem=this._clicked?-1:0;(this._getActiveItem().hasClass("e-disable")||this._getActiveItem().is(":hidden"))&&(this._activeItem=n(this.items).filter('li:not([class*="e-disable"])').first().index());this._addListHover()}});t.Toolbar.ResponsiveType={Popup:"popup",Inline:"inline"}})(jQuery,Syncfusion)});
