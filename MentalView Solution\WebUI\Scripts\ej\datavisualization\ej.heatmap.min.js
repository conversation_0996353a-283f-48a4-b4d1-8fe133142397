/*!
*  filename: ej.heatmap.min.js
*  version : *********
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min","./../common/ej.globalize.min","./../common/ej.touch.min","./../common/ej.scroller.min","./../web/ej.tooltip.min"],n):n()})(function(){(function(n,t){"use strict";var i=function(){function i(n,i){this._heatmap=i?i:null;i&&(i._grid=this);this._rootCSS="e-grid";this.element=null;this.validTags=["div"];this.model=null;this._requiresID=!0;this.keyConfigs={focus:"e",insertRecord:"45",deleteRecord:"46",editRecord:"113",saveRequest:"13",cancelRequest:"27",nextPage:"34",previousPage:"33",lastPage:"ctrl+alt+34",firstPage:"ctrl+alt+33",nextPager:"alt+34",previousPager:"alt+33",firstCellSelection:"36",lastCellSelection:"35",firstRowSelection:"ctrl+36",lastRowSelection:"ctrl+35",upArrow:"38",downArrow:"40",rightArrow:"39",leftArrow:"37",moveCellRight:"9",moveCellLeft:"shift+9",selectedGroupExpand:"alt+40",totalGroupExpand:"ctrl+40",selectedGroupCollapse:"alt+38",totalGroupCollapse:"ctrl+38",multiSelectionByUpArrow:"shift+38",multiSelectionByDownArrow:"shift+40"};this._ignoreOnPersist=["query","isEdit","toolbarClick","queryCellInfo","mergeCellInfo","currentViewData","enableAltRow","enableRTL","contextClick","contextOpen","rowDataBound","rowTemplate","detailsDataBound","detailsTemplate","childGrid","summaryRows","toolbarSettings","editSettings","allowMultiSorting","enableAutoSaveOnSelectionChange","locale","allowCellMerging","allowTextWrap","textWrapSettings","cssClass","dataSource","groupSettings.enableDropAreaAnimation","enableRowHover","showSummary","allowGrouping","enableHeaderHover","allowKeyboardNavigation","scrollSettings.frozenRows","scrollSettings.frozenColumns","enableTouch","contextMenuSettings.enableContextMenu","exportToExcelAction","exportToWordAction","exportToPdfAction"];this.ignoreOnExport=["isEdit","toolbarClick","query","queryCellInfo","selectionType","currentViewData","rowDataBound","rowTemplate","detailsDataBound","detailsTemplate","editSettings","pageSettings","enableAutoSaveOnSelectionChange","localization","allowScrolling","cssClass","dataSource","groupSettings.enableDropAreaAnimation","enableRowHover","allowSummary","enableHeaderHover","allowKeyboardNavigation"];this.observables=["dataSource","selectedRowIndex","pageSettings.currentPage"];this._tags=[{tag:"columns",attr:["allowEditing","allowFiltering","allowGrouping","allowResizing","allowSorting","cssClass","customAttributes","dataSource","defaultValue","disableHtmlEncode","editTemplate","editType","foreignKeyField","foreignKeyValue","headerTemplateID","headerText","isFrozen","isIdentity","isPrimaryKey","filterBarTemplate","textAlign","templateID","textAlign","headerTextAlign","tooltip","clipMode","validationRules.minlength","validationRules.maxlength","validationRules.range","validationRules.number","validationRules.required","editParams.decimalPlaces",[{tag:"commands",attr:["type","buttonOptions"]}]],content:"template"},{tag:"summaryRows",attr:["showCaptionSummary","showTotalSummary",[{tag:"summaryColumns",attr:["customSummaryValue","dataMember","displayColumn","summaryType","template"]}]]},{tag:"stackedHeaderRows",attr:[[{tag:"stackedHeaderColumns",attr:["headerText","column"]}]]},{tag:"filterSettings.filteredColumns",attr:[]},{tag:"sortSettings.sortedColumns",attr:[]}];this._dataSource=t.util.valueFunction("dataSource");this._selectedRow=t.util.valueFunction("selectedRowIndex");this._currentPage=t.util.valueFunction("pageSettings.currentPage");this.dataTypes={dataSource:"data",query:"data",columns:"array",childGrid:"parent",gridLines:"enum",summaryRows:"array",stackedHeaderRows:"array",toolbarSettings:{toolbarItems:"array",customToolbarItems:"array"},contextMenuSettings:{contextMenuItems:"array",customContextMenuItems:"array",subContextMenu:"array"},selectionSettings:{selectionMode:"array",selectedRecords:"array"},sortSettings:{sortedColumns:"array"},filterSettings:{filteredColumns:"array",filterType:"enum",filterBarMode:"enum"},groupSettings:{groupedColumns:"array"},editSettings:{editMode:"enum",formPosition:"enum",rowPosition:"enum"},searchSettings:{fields:"array"},textWrapSettings:{wrapMode:"enum"}};this.model={allowPaging:!1,showColumnChooser:!1,gridLines:"both",allowSorting:!1,showStackedHeader:!1,selectedRecords:[],stackedHeaderRows:[],allowFiltering:!1,allowMultipleExporting:!1,allowSelection:!0,allowGrouping:!1,showSummary:!1,allowResizing:!1,allowResizeToFit:!1,allowTextWrap:!1,allowCellMerging:!1,enableRowHover:!0,enablePersistence:!1,enableFocusout:!1,selectedRowIndex:-1,allowSearching:!1,enableToolbarItems:!1,enableHeaderHover:!1,allowReordering:!1,allowKeyboardNavigation:!0,allowRowDragAndDrop:!1,enableTouch:!0,columnLayout:"auto",selectionType:"single",dataSource:null,cssClass:"",allowScrolling:!1,locale:"en-US",enableAutoSaveOnSelectionChange:!0,allowMultiSorting:!1,exportToExcelAction:"",exportToWordAction:"",exportToPdfAction:"",_groupingCollapsed:[],editSettings:{allowEditing:!1,showAddNewRow:!1,allowAdding:!1,allowDeleting:!1,editMode:"normal",rowPosition:"top",dialogEditorTemplateID:null,allowEditOnDblClick:!0,externalFormTemplateID:null,inlineFormTemplateID:null,formPosition:"bottomleft",titleColumn:null,showConfirmDialog:!0,showDeleteConfirmDialog:!1},selectionSettings:{selectionMode:["row"],enableToggle:!1,cellSelectionMode:"flow"},pageSettings:{pageSize:12,pageCount:8,currentPage:1,totalPages:null,enableTemplates:!1,showDefaults:!1,template:null,totalRecordsCount:null,enableQueryString:!1,printMode:"allpages"},groupSettings:{showDropArea:!0,showToggleButton:!1,showGroupedColumn:!0,showUngroupButton:!0,enableDropAreaAutoSizing:!0,captionFormat:null,groupedColumns:[]},contextMenuSettings:{enableContextMenu:!1,contextMenuItems:["Add Record","Edit Record","Delete Record","Sort In Ascending Order","Sort In Descending Order","Next Page","Last Page","Previous Page","First Page","Save","Cancel","Grouping","Ungrouping"],customContextMenuItems:[],subContextMenu:[],disableDefaultItems:!1},filterSettings:{filterType:"filterbar",filterBarMode:"immediate",showFilterBarStatus:!0,statusBarWidth:450,showPredicate:!1,filteredColumns:[],enableInterDeterminateState:!0,maxFilterChoices:1e3,enableCaseSensitivity:!1,immediateModeDelay:1500,enableComplexBlankFilter:!0,blankValue:""},searchSettings:{fields:[],key:"",operator:"contains",ignoreCase:!0},sortSettings:{sortedColumns:[]},toolbarSettings:{showToolbar:!1,toolbarItems:[],customToolbarItems:[]},minWidth:0,currentIndex:0,rowDropSettings:{dropMapper:null,dragMapper:null,dropTargetID:null},scrollSettings:{width:"auto",height:0,enableTouchScroll:!0,allowVirtualScrolling:!1,virtualScrollMode:"normal",frozenRows:0,frozenColumns:0,buttonSize:18,autoHide:!1,scrollerSize:18,scrollOneStepBy:57,enableVirtualization:!1},textWrapSettings:{wrapMode:"both"},summaryRows:[],enableRTL:!1,enableAltRow:!0,currentViewData:null,detailsTemplate:null,childGrid:null,keySettings:null,rowTemplate:null,detailsDataBound:null,rowDataBound:null,queryCellInfo:null,mergeCellInfo:null,create:null,actionBegin:null,actionComplete:null,actionFailure:null,beginEdit:null,endEdit:null,endAdd:null,endDelete:null,beforeBatchAdd:null,beforeBatchSave:null,beforeBatchDelete:null,batchAdd:null,batchDelete:null,cellSave:null,cellEdit:null,resizeStart:null,resizeEnd:null,resized:null,load:null,destroy:null,rowSelecting:null,rowSelected:null,cellSelecting:null,cellSelected:null,columnSelecting:null,columnSelected:null,columnDragStart:null,columnDrag:null,columnDrop:null,dataBound:null,recordClick:null,recordDoubleClick:null,templateRefresh:null,rightClick:null,detailsCollapse:null,detailsExpand:null,toolbarClick:null,contextOpen:null,contextClick:null,columns:[],query:null,isEdit:!1,isResponsive:!1,enableResponsiveRow:!1,virtualLoading:null}}return i.prototype._dataSource=t.util.valueFunction("dataSource"),i.prototype._selectedRow=t.util.valueFunction("selectedRowIndex"),i.prototype._currentPage=t.util.valueFunction("pageSettings.currentPage"),i.prototype._mapValues=function(){},i.prototype._createElement=function(i,r){this.element=i.jquery?i:n(i);this.sfType="ej-grid";this.pluginName="ej.Grid";this.model=t.copyObject(!0,{},this.model,r);this.model.keyConfigs=t.copyObject(this.keyConfigs);this.element.addClass("e-grid e-js").data("ej.Grid",this)},i.prototype._init=function(n,i,r){var f,u,e;this._createElement(n,i);!t.isNullOrUndefined(this.model.query)&&this.model.query instanceof t.Query||(this.model.query=t.Query());t.isNullOrUndefined(this.model.parentDetails)||(f=this.model.queryString,u=this.model.foreignKeyField,this.model.query=this.model.query.clone(),e=this.model.parentDetails.parentKeyFieldValue===undefined?"undefined":this.model.parentDetails.parentKeyFieldValue,this.model.query.where(t.isNullOrUndefined(u)?f:u,"equal",e,!0));this._initPrivateProperties();this._initScrolling();this.model.enableResponsiveRow&&this.element.addClass("e-responsive");this._checkForeignKeyBinding();this._checkDataBinding(r);this._refreshScroller({})},i.prototype._checkDataBinding=function(n){var i,u,r,f;if(!this.model.columns.length&&((this._dataSource()==null||!this._dataSource().length)&&!(this._dataSource()instanceof t.DataManager)||this._dataSource()instanceof t.DataManager&&this._dataManager.dataSource.url==undefined&&!this._dataSource().dataSource.json.length)){this._alertDialog.find(".e-content").text(this.localizedLabels.EmptyDataSource);this._alertDialog.ejDialog("open");return}if(this._initialRenderings(),this.model.editSettings.allowDeleting&&this.model.selectionType=="multiple"&&(this.multiDeleteMode=!0),this.initialRender=!0,this.model.enableRTL&&this.element.addClass("e-rtl"),this.model.allowFiltering&&this._isExcelFilter&&this._renderExcelFilter(),this.model.cssClass!=null&&this.element.addClass(this.model.cssClass),this.model.allowGrouping&&this.element.append(this._renderGroupDropArea()),(this.model.toolbarSettings.showToolbar||(this.model.allowSorting||this.model.allowFiltering)&&this.model.enableResponsiveRow)&&this.element.append(this._renderToolBar()),i=this.model.columns,i&&i.length){if(u=this.model.query._expands,typeof i[0]=="string")for(r=0;r<i.length;r++)i[r]={field:i[r]};for(r=0;r<i.length;r++)i[r].field&&i[r].field.indexOf(".")!==-1&&this._getExpands(i[r].field,u);this.model.query.expand(u);this.commonQuery.expand(u);this._renderAfterColumnInitialize()}this.model.allowPaging&&this.element.append(this._renderGridPager());this.model.contextMenuSettings.enableContextMenu&&this.element.append(this._renderContext());this.model.scrollSettings.allowVirtualScrolling&&(this._loadedJsonData=[],this._prevPage=1);this._dataSource()instanceof t.DataManager?this._dataSource().ready!=undefined?(f=this,this._dataSource().ready.done(function(n){f._initDataSource();f.model.dataSource=t.DataManager(n.result)})):this._initDataSource():(this._ensureDataSource(),this._setForeignKeyData(),this._relationalColumns.length==0&&this._initGridRender(n),this._vRowHeight=Math.floor(this.getRowHeight()));this.model.showColumnChooser&&this._renderColumnChooser()},i.prototype._initialRenderings=function(){var r,i;if(this.model.groupSettings.groupedColumns.length){for(r=[],i=0;i<this.model.sortSettings.sortedColumns.length;i++)t.isNullOrUndefined(this.model.sortSettings.sortedColumns[i].direction)&&(this.model.sortSettings.sortedColumns[i].direction=t.sortOrder.Ascending),r.push(this.model.sortSettings.sortedColumns[i].field);if(this.model.allowGrouping)for(i=0;i<this.model.groupSettings.groupedColumns.length;i++)n.inArray(this.model.groupSettings.groupedColumns[i],r)==-1&&this.model.sortSettings.sortedColumns.push({field:this.model.groupSettings.groupedColumns[i],direction:t.sortOrder.Ascending})}},i.prototype._renderAfterColumnInitialize=function(){this.element.append(this._renderGridHeader())},i.prototype.setGridHeaderContent=function(n){this._gridHeaderContent=n},i.prototype._renderGridHeader=function(){var n=t.buildTag("div.e-gridheader"),r,u,i=t.buildTag("div");return this.model.allowScrolling&&i.addClass("e-headercontent"),this.setGridHeaderContent(n),this.initialRender&&(this.columnsWidthCollection=[],this._hiddenColumns=[],this._hiddenColumnsField=[]),this._visibleColumns=[],this._visibleColumnsField=[],this._disabledGroupableColumns=[],this._fieldColumnNames={},this._headerColumnNames={},this.model.scrollSettings.frozenColumns>0?(r=t.buildTag("div.e-frozenheaderdiv",this._renderGridHeaderInternalDesign(this.model.columns.slice(0,this.model.scrollSettings.frozenColumns),!0)),u=t.buildTag("div.e-movableheader",t.buildTag("div.e-movableheaderdiv",this._renderGridHeaderInternalDesign(this.model.columns.slice(this.model.scrollSettings.frozenColumns),!1))),i.append(r).append(u)):i.append(this._renderGridHeaderInternalDesign(this.model.columns)),n.html(i),this.model.isResponsive&&n.addClass("e-textover"),this.setGridHeaderTable(this.getHeaderContent().find(".e-table")),n},i.prototype.setGridHeaderTable=function(n){this._gridHeaderTable=n},i.prototype._renderGridHeaderInternalDesign=function(i,r){var y=t.buildTag("table.e-table","",{},{"data-role":"heatmap"}),c=t.buildTag("thead"),p=t.buildTag("tbody.e-hide"),l=t.buildTag("tr.e-columnheader"),a=n(document.createElement("colgroup")),w=n(document.createElement("tr")),b,u,v,h,s,o;if((this.model.childGrid||this.model.detailsTemplate)&&(l.append(t.buildTag("th.e-headercell e-detailheadercell","<div><\/div>")),a.append(this._getIndentCol())),this.model.showStackedHeader)for(o=0;o<this.model.stackedHeaderRows.length;o++)b=this._createStackedRow(this.model.stackedHeaderRows[o],r),c.append(b);for(u=0;u<i.length;u++){var e=t.buildTag("th.e-headercell e-default","",{},{"data-role":"columnheader"}),k=document.createElement("td"),f=t.buildTag("div.e-headercelldiv",i[u].headerText===undefined?i[u].headerText=i[u].field:i[u].headerText,{},{"data-ej-mappingname":i[u].field});if(i[u].disableHtmlEncode&&f.text(i[u].headerText),t.isNullOrUndefined(i[u].headerTooltip)||f.addClass("e-headertooltip"),t.isNullOrUndefined(i[u].tooltip)||f.addClass("e-gridtooltip"),(i[u].clipMode==t.HeatMapGrid.ClipMode.Ellipsis||i[u].clipMode==t.HeatMapGrid.ClipMode.EllipsisWithTooltip)&&f.addClass("e-gridellipsis"),e.append(f),this.model.allowFiltering&&(this.model.filterSettings.filterType=="menu"||this.model.filterSettings.filterType=="excel")&&(i[u].allowFiltering==undefined||i[u].allowFiltering===!0)&&(!t.isNullOrUndefined(i[u].field)||i[u].field=="")){if(v="e-filterset",!this.initialRender&&this.model.filterSettings.filteredColumns)for(h=0;h<this.model.filterSettings.filteredColumns.length;h++)this.model.filterSettings.filteredColumns[h].field==i[u].field&&(v="e-filterset e-filteredicon e-filternone");e.append(t.buildTag("div.e-filtericon e-icon "+v));e.addClass("e-headercellfilter");t.browserInfo().name=="msie"&&t.browserInfo().version=="8.0"&&this.model.enableRTL&&n(f).css("padding","0 0 0 2em")}s=document.createElement("col");i[u].priority&&n(k).addClass("e-table-priority-"+i[u].priority);w.append(k);l.append(e);a.append(s);i[u].visible===!1?(e.addClass("e-hide")&&n(s).css("display","none"),n.inArray(i[u].headerText,this._hiddenColumns)==-1&&n.inArray(i[u].field,this._hiddenColumnsField)==-1&&(this._hiddenColumns.push(i[u].headerText)&&i[u].field!=(""||undefined)?this._hiddenColumnsField.push(i[u].field):this._hiddenColumnsField.push(i[u].headerText)),n.inArray(i[u].field,this._visibleColumnsField)!=-1&&this._visibleColumnsField.splice(n.inArray(i[u].field,this._visibleColumnsField),1)&&this._visibleColumns.splice(n.inArray(i[u].headerText,this._visibleColumns),1)):(this._visibleColumns.push(i[u].headerText)&&i[u].field!=(""||undefined)?this._visibleColumnsField.push(i[u].field):this._visibleColumnsField.push(i[u].headerText),i[u].visible=!0,n.inArray(i[u].field==""?i[u].headerText:i[u].field,this._hiddenColumnsField)!=-1&&this._hiddenColumnsField.splice(n.inArray(i[u].field==""?i[u].headerText:i[u].field,this._hiddenColumnsField),1)&&this._hiddenColumns.splice(n.inArray(i[u].headerText,this._hiddenColumns),1));this.model.showColumnChooser&&i[u].showInColumnChooser!==!1&&(i[u].showInColumnChooser=!0);this.model.allowResizing&&i[u].allowResizing!==!1&&(i[u].allowResizing=!0);t.isNullOrUndefined(i[u].headerTextAlign)?i[u].textAlign!=undefined?f.css("text-align",i[u].textAlign):this.model.enableRTL&&f.css("text-align",i[u].textAlign="right"):f.css("text-align",i[u].headerTextAlign);i[u].allowResizing===!1&&this._disabledResizingColumns.push(i[u].field);i[u].allowSorting===!1&&this._disabledSortableColumns.push(i[u].field);i[u].allowGrouping===!1&&this._disabledGroupableColumns.push(i[u].field);i[u].allowEditing===!1&&this._disabledEditableColumns.push(i[u].field);t.isNullOrUndefined(i[u].cssClass)||(e.addClass(i[u].cssClass),n(s).addClass(i[u].cssClass));t.isNullOrUndefined(i[u].headerTemplateID)||(f.html(n(i[u].headerTemplateID).hide().html()).parent().addClass("e-headertemplate"),o=n.inArray(i[u].field,this._disabledGroupableColumns),o==-1&&t.isNullOrUndefined(i[u].field)&&this._disabledGroupableColumns.push(i[u].field));this.model.isResponsive&&e.attr("title",this._decode(i[u].headerText));i[u].priority&&(e.attr("data-priority",i[u].priority).addClass("e-table-priority-"+i[u].priority),n(s).addClass("e-table-priority-"+i[u].priority));this.initialRender&&(typeof i[u].width=="string"&&i[u].width.indexOf("%")!=-1?this.columnsWidthCollection.push(parseInt(i[u].width)/100*this.element.width()):this.columnsWidthCollection.push(i[u].width));i[u].width==undefined&&this.model.commonWidth!==undefined&&(this.columnsWidthCollection[u]=this.model.commonWidth);this._fieldColumnNames[i[u].headerText]=i[u].field;this._headerColumnNames[i[u].field]=i[u].headerText}return c.append(l),p.append(w),y.append(a).append(c).append(p),y},i.prototype._decode=function(t){return n("<div/>").html(t).text()},i.prototype._checkForeignKeyBinding=function(){if(this.model.columns.length){var i,r,u,n;for(i=0,r=this.model.columns,u=r.length;i<u;i++)n=r[i],n.hasOwnProperty("foreignKeyField")&&n.dataSource instanceof t.DataManager&&this._relationalColumns.push({field:n.field,key:n.foreignKeyField,value:n.foreignKeyValue,dataSource:n.dataSource});this._$fkColumn=!0}},i.prototype._initScrolling=function(){for(var u,r=[],f=[],e=0,i=0;i<this.model.columns.length;i++)this.model.columns[i].visible===!1&&i<this.model.scrollSettings.frozenColumns&&e++,this.model.columns[i].isFrozen===!0?r.push(this.model.columns[i]):f.push(this.model.columns[i]);(r.length>0&&(u=this.model.scrollSettings.frozenColumns,this.model.columns=n.merge(n.merge([],r),f),this.model.scrollSettings.frozenColumns=r.length,r.length!=u&&u!=0&&(this.model.scrollSettings.frozenColumns=u)),(this.model.scrollSettings.frozenColumns>0||this.model.scrollSettings.frozenRows>0)&&(this.model.allowGrouping||this.model.rowTemplate!=null||this.model.detailsTemplate!=null||this.model.childGrid!=null||this.model.scrollSettings.allowVirtualScrolling||this.model.editSettings.editMode=="batch"))||(this.model.scrollSettings.allowVirtualScrolling&&this.model.allowScrolling&&(this.model.scrollSettings.enableVirtualization?(this._vRowHeight=Math.floor(this.getRowHeight()+1),this._virtualRowCount=Math.round(this.model.scrollSettings.height/this._vRowHeight)+1,this.model.pageSettings.pageSize<this._virtualRowCount*5&&(this.model.pageSettings.pageSize=this._virtualRowCount*5)):(this.model.pageSettings.pageSize=this.model.pageSettings.pageSize==12?Math.round(this.model.scrollSettings.height/32)+1:this.model.pageSettings.pageSize,this.model.pageSettings.totalPages=Math.ceil(this._gridRecordsCount/this.model.pageSettings.pageSize))),(this.model.width||this.model.height)&&(this.model.allowScrolling=!0,this.model.width&&(this.model.scrollSettings.width=this.model.width),this.model.height&&(this.model.scrollSettings.height=this.model.height)),this._originalScrollWidth=t.isNullOrUndefined(this.model.scrollSettings.previousStateWidth)?this.model.scrollSettings.width:this.model.scrollSettings.previousStateWidth)},i.prototype._initPrivateProperties=function(){this._click=0;this._tabKey=!1;this._gridHeaderTable=null;this._gridWidth=this.element.width();this._id=this.element.attr("id");this._gridRows=null;this._unboundRow=null;this._gridContentTable=null;this._gridContent=null;this._remoteSummaryData=null;this._gridSort=null;this._gridHeaderContent=null;this._gridFooterContent=null;this._gridFooterTable=null;this._gridRecordsCount=this._dataSource()!==null?this.model.pageSettings.totalRecordsCount==null?this._dataSource().length:this.model.pageSettings.totalRecordsCount:0;this._links=null;this._gridPager=null;this._cSortedColumn=null;this._cSortedDirection=null;this._$curSElementTarget=null;this._gridFilterBar=null;this._$curFieldName=null;this._$prevFieldName=null;this._mediaStatus=!1;this._$fDlgIsOpen=!1;this._$menuDlgIsOpen=!1;this._$colType=null;this._$colFormat=null;this._$prevColType=null;this._$prevSElementTarget=null;this._currentFilterColumn=null;this._filteredRecordsCount=null;this._filteredRecords=[];this._validatedColumns=[];this.filterColumnCollection=[];this._previousFilterCount=null;this._excelFilter=null;this._isExcelFilter=this.model.filterSettings.filterType=="excel";this._$fkColumn=!1;this._fkParentTblData=[];this._primaryKeys=[];this._identityKeys=[];this._primaryKeyValues=[];this._modifiedRecords=[];this._addedRecords=[];this._tdsOffsetWidth=[];this._deletedRecords=[];this._disabledToolItems=n();this._validationRules={};this._groupedColumns=[];this._scolumns=[];this._currentJsonData=[];this._groupingColumnIndex=0;this._dataManager=this._dataSource()instanceof t.DataManager?this._dataSource():this._dataSource()!=null?t.DataManager(this._dataSource()):null;this._dataManager!=null&&this.model.allowScrolling&&this.model.scrollSettings.allowVirtualScrolling&&this.model.pageSettings.totalRecordsCount!=null&&this._dataManager.dataSource.json!=null&&this._dataManager.dataSource.json.splice(this.model.pageSettings.totalRecordsCount);this._isRemoteSaveAdaptor=this._dataSource()instanceof t.DataManager&&this._dataSource().adaptor instanceof t.remoteSaveAdaptor;this._isLocalData=!0;this._disabledResizingColumns=[];this._disabledSortableColumns=[];this._disabledGroupableColumns=[];this._disabledFilterableColumns=[];this._disabledEditableColumns=[];this._hiddenColumns=[];this._visibleColumns=[];this._visibleColumnsField=[];this._hiddenColumnsField=[];this._ccVisibleColumns=[];this._ccHiddenColumns=[];this._sortedColumns=[];this.multiSortRequest=!1;this.multiSelectCtrlRequest=!1;this.multiSelectShiftRequest=!1;this._enableSelectMultiTouch=!1;this._enableSortMultiTouch=!1;this._templateRefresh=!1;this.initialRender=!1;this._selectDrag=!1;this._isAddNew=!1;this._fieldColumnNames={};this._headerColumnNames={};this._virtualLoadedRecords={};this._virtualLoadedRows={};this._virtualPageRecords={};this._queryCellView=[];this._currentPageViews=[];this._virtualLoadedPages=[];this._currentLoadedIndexes=[];this._prevVirtualSort=[];this._prevVirtualFilter=[];this._prevVirtualIndex=0;this._currentVirtualIndex=1;this._virtualRowCount=0;this._virtualSelectedRecords={};this.selectedRowsIndexes=[];this._isReorder=!1;this._searchString="";this._searchCount=null;this.columnsWidthCollection=[];this._Indicator=null;this._resizer=null;this._bulkEditCellDetails={cellValue:null,rowIndex:-1,columnIndex:-1,fieldName:null,_data:null,cellEditType:"",cancelSave:!1,defaultData:null,insertedTrCollection:[],rowData:null};this.batchChanges={added:[],deleted:[],changed:[]};this._bulkEditTemplate=n();this._confirmDialog=null;this._confirmedValue=!1;this._lastRow=!1;this._isVirtualRecordsLoaded=!1;this._scrollValue=0;this._currentTopFrozenRow=this.model.scrollSettings.frozenRows;this._rowHeightCollection=[];this._scrollObject=null;this._customPop=null;this.selectedRowCellIndexes=[];this._rowIndexesColl=[];this.selectedColumnIndexes=[];this._allowrowSelection=this._allowcellSelection=this._allowcolumnSelection=!1;this.commonQuery=n.extend(!0,{},this.model.query);this.phoneMode=this.model.isResponsive&&document.documentElement.clientWidth<360?!0:!1;this.model.selectionSettings.selectionMode.length>0&&this.model.allowSelection&&this._initSelection();this._mediaQuery=!1;this._columnChooserList=null;this._$headerCols=null;this._$contentCols=null;this._detailsOuterWidth=null;this._editForm=null;this._cloneQuery=null;this.localizedLabels=this._getLocalizedLabels();this._searchBar=null;this._relationalColumns=[];this._dropDownManager={};this._isUngrouping=!1;this._columnChooser=!1},i.prototype._getLocalizedLabels=function(){return t.getLocalizedConstants("ej.HeatMapGrid",this.model.locale)},i.prototype._initSelection=function(){for(var t=this.model.selectionSettings.selectionMode,n=0;n<t.length;n++)this["_allow"+t[n]+"Selection"]=!0},i.prototype._getSelectedViewData=function(t,i,r){var o=t%this._virtualRowCount,u,f={},e;return u=i?parseInt(n(i).closest("tr").attr("name"),32):r?r:t>1?Math.ceil((t+1)/this._virtualRowCount):1,f.viewIndex=u,this._virtualLoadedRecords[u]&&(f.data=this._virtualLoadedRecords[u][o]),e=t%this._virtualRowCount,f.rowIndex=u*this._virtualRowCount-(this._virtualRowCount-e),f},i.prototype._frozenCell=function(t,i){var r=i,u=0,f=this.getRowByIndex(t);return i>=this.model.scrollSettings.frozenColumns&&(u=1,r=r-this.model.scrollSettings.frozenColumns),n(f.eq(u).find(".e-rowcell:eq("+r+")"))},i.prototype.selectCells=function(i){var r,e,o,c,s,u,h,f;if(!this._allowcellSelection)return!1;r=null;c=this._excludeDetailRows();r=this.model.scrollSettings.frozenColumns?this._frozenCell(i[0][0],i[0][1][0]):c.eq(i[0][0]).find(".e-rowcell:eq("+i[0][1]+")");t.isNullOrUndefined(this._previousRowCellIndex)||this._previousRowCellIndex.length==0||(this.model.scrollSettings.enableVirtualization?(e=this._prevRowCell,o=this._preVirRowCellIndex):(e=n(this.getRowByIndex(this._previousRowCellIndex[0][0]).find(".e-rowcell:eq("+this._previousRowCellIndex[0][1]+")")),o=this._previousRowCellIndex));s=this._currentJsonData[i[0][0]];u=i[0][0];this.model.scrollSettings.allowVirtualScrolling&&this.model.scrollSettings.enableVirtualization&&(h=this._getSelectedViewData(i[0][0],r),s=h.data,u=h.rowIndex);f={currentCell:r,cellIndex:i[0][1],data:s,previousRowCellIndex:o,previousRowCell:e};this.model.selectionType=="multiple"&&(f.isCtrlPressed=this.multiSelectCtrlRequest,f.isShiftPressed=this.multiSelectShiftRequest);switch(this.model.selectionType){case t.HeatMapGrid.SelectionType.Single:this.clearCellSelection();this.clearColumnSelection();this.selectedRowCellIndexes=[];this._virtualRowCellSelIndex=[];n.inArray(u,this._rowIndexesColl)==-1&&this._rowIndexesColl.push(u);this.selectedRowCellIndexes.push({rowIndex:u,cellIndex:i[0][1]});this.model.scrollSettings.frozenColumns?this._frozenCell(i[0][0],i[0][1][0]).addClass("e-cellselectionbackground e-activecell"):n(this.getRowByIndex(i[0][0]).find(".e-rowcell:eq("+i[0][1]+")")).addClass("e-cellselectionbackground e-activecell")}f={currentCell:r,cellIndex:i[0][1],data:s,selectedRowCellIndex:this.selectedRowCellIndexes,previousRowCellIndex:o,previousRowCell:e};(!this.multiSelectShiftRequest||t.isNullOrUndefined(this._previousRowCellIndex))&&(this._previousRowCellIndex=i,this.model.scrollSettings.allowVirtualScrolling&&this.model.scrollSettings.enableVirtualization&&(this._preVirRowCellIndex=n.extend(!0,[],i),this._preVirRowCellIndex[0][0]=u),this._prevRowCell=r);this._heatmap&&this._heatmap._cellSelected(f);return},i.prototype.clearSelection=function(i){var r,i,u,f,e;return this._selectedRow()>=-1&&(r=this.model.scrollSettings.frozenColumns?this._excludeDetailRows():n(this.element.find("tr[aria-selected='true']")),t.isNullOrUndefined(i)?(this.model.scrollSettings.frozenColumns>0&&(r=n(r[0]).add(r[1])),r.removeAttr("aria-selected").find(".e-rowcell, .e-detailrowcollapse, .e-detailrowexpand").removeClass("e-selectionbackground").removeClass("e-active"),this._clearVirtualSelection||(this.selectedRowsIndexes=[],this.model.selectedRecords=[])):(this.getRowByIndex(i).removeAttr("aria-selected").find(".e-selectionbackground").removeClass("e-selectionbackground").removeClass("e-active"),u=this.getRowByIndex(i),this.model.scrollSettings.enableVirtualization&&this.multiSelectCtrlRequest&&n.inArray(i,this.selectedRowsIndexes)==-1&&(f=parseInt(u.attr("name"),32)*this._virtualRowCount,e=this._virtualRowCount-u.index()%this._virtualRowCount,i=f-e),i=n.inArray(i,this.selectedRowsIndexes),i!=-1&&this.selectedRowsIndexes.splice(i,1)),this.selectedRowsIndexes.length||this._selectedRow(-1)),!0},i.prototype._excludeDetailRows=function(){return t.isNullOrUndefined(this.model.detailsTemplate||this.model.childGrid||this.model.showSummary)?n(this.getRows()):n(this.getRows()).not(".e-detailrow,.e-gridSummaryRows")},i.prototype.clearCellSelection=function(i,r){var f,e,u;if(this._allowcellSelection)if(f=this.model.scrollSettings.frozenColumns||!t.isNullOrUndefined(i)?this._excludeDetailRows():n(this.element.find(".e-cellselectionbackground")).parent(),t.isNullOrUndefined(i))this.model.scrollSettings.frozenColumns&&(f=n(f[0]).add(f[1])),f.find(".e-rowcell, .e-detailrowcollapse, .e-detailrowexpand").removeClass("e-cellselectionbackground").removeClass("e-activecell"),this.selectedRowCellIndexes=[],this._rowIndexesColl=[];else{for(u=0;u<this.selectedRowCellIndexes.length;u++)if(this.selectedRowCellIndexes[u].rowIndex==i){e=n.inArray(r,this.selectedRowCellIndexes[u].cellIndex);this.model.scrollSettings.frozenColumns?this._frozenCell(i,r).removeClass("e-cellselectionbackground").removeClass("e-activecell"):f.eq(i).find(".e-rowcell").eq(r).removeClass("e-cellselectionbackground").removeClass("e-activecell");break}u!=this.selectedRowCellIndexes.length&&(this.selectedRowCellIndexes[u].cellIndex.splice(e,1),this.selectedRowCellIndexes[u].cellIndex.length==0&&(this.selectedRowCellIndexes.splice(u,1),this._rowIndexesColl.splice(n.inArray(i,this._rowIndexesColl),1)))}return!0},i.prototype.clearColumnSelection=function(i){var r,o,u,s,f,e;if(this._allowcolumnSelection)if(r=n(this._excludeDetailRows()),t.isNullOrUndefined(i))this.model.scrollSettings.frozenColumns&&(r=n(r[0]).add(r[1])),r.find(".e-rowcell").removeClass("e-columnselection"),n(this.getHeaderTable().find("th.e-headercell:not(.e-stackedHeaderCell)")).removeClass("e-columnselection"),this.selectedColumnIndexes=[];else{if(o=0,(this.model.detailsTemplate!=null||this.model.childGrid!=null)&&(++i,o=1),this.model.scrollSettings.frozenColumns)for(u=0,s=i,i>=this.model.scrollSettings.frozenColumns&&(u=1,s=i-this.model.scrollSettings.frozenColumns),f=0;f<r[u].length;f++)n(r[u][f].cells[s]).removeClass("e-columnselection");else for(e=0;e<r.length;e++)n(r[e].cells[i]).removeClass("e-columnselection");n(this.getHeaderTable().find("th.e-headercell:not(.e-stackedHeaderCell)")[i]).removeClass("e-columnselection");this.selectedColumnIndexes.splice(0,i-o)}return!0},i.prototype.getSelectedRecords=function(){var t=[],n;if(this._virtualScrollingSelection)return this._virtualSelRecords;for(n=0;n<this.selectedRowsIndexes.length;n++)this.selectedRowsIndexes[n]!=-1&&(this.model.scrollSettings.allowVirtualScrolling?t.push(this._virtualSelectedRecords[this.selectedRowsIndexes[n]]):t.push(this._currentJsonData[this.selectedRowsIndexes[n]]));return t},i.prototype._setCurrentRow=function(n){(n==t.HeatMapGrid.Actions.Refresh||n==t.HeatMapGrid.Actions.Ungrouping||n==t.HeatMapGrid.Actions.Grouping||n==t.HeatMapGrid.Actions.Filtering||n==t.HeatMapGrid.Actions.Sorting||n==t.HeatMapGrid.Actions.Delete||n==t.HeatMapGrid.Actions.Save||n==t.HeatMapGrid.Actions.Cancel||n==t.HeatMapGrid.Actions.Paging)&&(this._selectedRow(-1),this._virtualDataRefresh||(this.selectedRowsIndexes=[]))},i.prototype.refreshContent=function(){var n={};n.requestType=t.HeatMapGrid.Actions.Refresh;this._processBindings(n)},i.prototype.rowHeightRefresh=function(){var i,f;if(this.model.scrollSettings.frozenColumns>0&&!t.isNullOrUndefined(this.model.currentViewData)&&this.model.currentViewData.length){var u=this.getContentTable().get(0).rows,e=this.getContentTable().get(1).rows,r=0;if(this.getContent().find(".e-frozencontentdiv").is(":visible"))for(i=0;i<u.length;i++)n(u[i]).css("display")!="none"&&(r=t.max([u[i].getClientRects()[0].height,e[i].getClientRects()[0].height]),n(u[i]).height(r),n(e[i]).height(r),i&&(i==this.model.scrollSettings.frozenRows-1||i==u.length-1)&&(r=r+1),this.model.allowTextWrap||i&&i!=this.model.scrollSettings.frozenRows-1||(r=r-1),this.model.isEdit&&n(u[i]).find("#"+this._id+"EditForm").length&&i&&n(u[i]).find("#"+this._id+"EditForm td").css("height",r),n(e[i]).find("#"+this._id+"EditForm td").css("height",r));this._getRowHeights();!t.isNullOrUndefined(this.getContent().data("ejScroller"))&&this.getScrollObject().isVScroll()&&(f=this.getScrollObject()._vScrollbar,f&&f.value()!=f.model.maximum&&this._scrollObject.refresh(this.model.scrollSettings.frozenColumns>0))}},i.prototype._refreshDataSource=function(n){this._dataManager=n instanceof t.DataManager?n:t.DataManager(n);this._isLocalData=!(this._dataSource()instanceof t.DataManager)||this._dataManager.dataSource.offline||this._isRemoteSaveAdaptor;this.model.scrollSettings.allowVirtualScrolling&&this.model.scrollSettings.enableVirtualization&&(this._refreshVirtualViewData(),this._virtualDataRefresh=!0);this.refreshContent(!0);this._refreshScroller({requestType:"refresh"})},i.prototype.dataSource=function(n,t){t&&(this._templateRefresh=!0);this._dataSource(n);this.model.scrollSettings.enableVirtualization||(n.length>0?this._currentPage(1):this._currentPage(0));this._refreshDataSource(n);var i=this._refreshVirtualPagerInfo();(this.model.allowPaging||this.model.scrollSettings.allowVirtualScrolling)&&this._showPagerInformation(i);this.model.scrollSettings.allowVirtualScrolling&&(this.model.scrollSettings.enableVirtualization&&this._isLocalData?this._refreshVirtualView():this._refreshVirtualContent(),this.getContent().ejScroller("isHScroll")&&this.getContent().ejScroller("scrollX",0,!0),this.getContent().ejScroller("isVScroll")?(this.model.scrollSettings.enableVirtualization||this.getContent().ejScroller("scrollY",0,!0),this.element.find(".e-gridheader").addClass("e-scrollcss")):this.element.find(".e-gridheader").removeClass("e-scrollcss"));(!this.model.scrollSettings.enableVirtualization||this._gridRows.length<this._virtualRowCount)&&this._addLastRow();this._trigger("dataBound",{})},i.prototype._trigger=function(){},i.prototype.getFooterContent=function(){return this._gridFooterContent},i.prototype.getScrollObject=function(){return(this._scrollObject==null||t.isNullOrUndefined(this._scrollObject.model))&&(this._scrollObject=this.getContent().ejScroller("instance")),this._scrollObject},i.prototype.getRowHeight=function(){var i=-1,n,r,t;return this.getContentTable()!=null&&(n=this.getContentTable().find("tr:not(.e-virtualrow)"),r=n.length>2?1:0,n.length&&(t=n[r].getBoundingClientRect()),n.length>1&&(i=t&&t.height?t.height:n[r].offsetHeight)),i==-1?32:i},i.prototype.getCurrentIndex=function(){return(this._currentPage()-1)*this.model.pageSettings.pageSize},i.prototype.getColumnByIndex=function(n){return n<this.model.columns.length?this.model.columns[n]:null},i.prototype.set_currentPageIndex=function(n){var i=this.model.pageSettings,u=this.model.filterSettings.filteredColumns.length==0?this._searchCount==null?this._gridRecordsCount:this._searchCount:this._filteredRecordsCount,r;return(i.totalPages==null&&(i.totalPages=Math.ceil(u/i.pageSize)),n>i.totalPages||n<1||n==this._currentPage())?!1:(t.isNullOrUndefined(this._prevPageNo)&&(this._prevPageNo=this._currentPage()),this._currentPage(n),this._currentPage()!=this._prevPageNo?(r={},r.requestType="paging",this.gotoPage(this._currentPage(),r),!0):!1)},i.prototype.set_currentVirtualIndex=function(n){var t,i;return n<1||n!=1&&n!=this._totalVirtualViews&&n==this._currentVirtualIndex&&this._checkCurrentVirtualView(this._virtualLoadedRows,n)?!1:(this._prevVirtualIndex=this._currentVirtualIndex,this._currentVirtualIndex=n,t=this._calculateCurrentViewPage(),t<=this.model.pageSettings.totalPages&&!this._checkCurrentVirtualView(this._virtualLoadedRecords,this._currentVirtualIndex)?this._prevVirtualIndex<n&&n!=1?(i=this._isThumbScroll&&t!=1?t:t+1,!this._virtualPageRecords[i]&&i<=this.model.pageSettings.totalPages?this._setCurrentViewPage(i):this._renderVirtulViewContent(t)):this._prevVirtualIndex>n?(i=this._isThumbScroll?t:t-1,this._virtualPageRecords[i]&&!this._virtualLoadedRecords[n-1]&&(i=t-1),!this._virtualPageRecords[i]&&i>=1&&this._setCurrentViewPage(i)):this._renderVirtulViewContent(t):this._renderVirtulViewContent(t),!0)},i.prototype._setCurrentViewPage=function(n){this._needPaging=!0;this._prevPageNo=this._currentPage();this.gotoPage(n)},i.prototype._renderVirtulViewContent=function(){this._needPaging=!1;this._refreshVirtualView(this._currentVirtualIndex)},i.prototype._checkCurrentVirtualView=function(t,i){var f=this._virtualRowCount,u=i-1,r=i+1,e;if(t instanceof Array){if(t.length&&((u==0||r==this._totalVirtualViews+1)&&n.inArray(i,t)!=-1||n.inArray(u,t)!=-1&&n.inArray(i,t)!=-1&&n.inArray(r,t)!=-1))return!0}else if(e=r==this._totalVirtualViews?this._lastViewData:f,!this.initialRender&&i==1&&this._virtualLoadedRows[i]||i==this._totalVirtualViews&&t==this._virtualLoadedRows&&t[i]||u==0&&t[i]&&t[i].length==f||r==this._totalVirtualViews+1&&t[i]&&t[i].length==this._lastViewData||t[u]&&t[u].length==f&&t[i]&&t[i].length==f&&t[r]&&t[r].length==e)return!0;return!1},i.prototype._refreshStackedHeader=function(){var i,r,u,t;if(this.model.showStackedHeader){for(i=this.model.stackedHeaderRows,t=0;t<i.length;t++)if(this.model.scrollSettings.frozenColumns!=0){var f=n(this.getHeaderContent().find(".e-frozenheaderdiv")),e=n(this.getHeaderContent().find(".e-movableheader")),o=this._createStackedRow(i[t],!0),s=this._createStackedRow(i[t],!1);n(f.find("tr.e-stackedHeaderRow")[t]).replaceWith(o);n(e.find("tr.e-stackedHeaderRow")[t]).replaceWith(s)}else r=this._createStackedRow(i[t],!1),this.getHeaderTable().find("tr.e-stackedHeaderRow")[t]?n(this.getHeaderTable().find("tr.e-stackedHeaderRow")[t]).replaceWith(r):r.insertBefore(this.getHeaderTable().find("tr.e-columnheader:last"));if(u={},u.requestType="refresh",this.model.allowGrouping&&this.model.groupSettings.groupedColumns.length>0)for(t=0;t<this.model.groupSettings.groupedColumns.length;t++)this.getHeaderTable().find(".e-stackedHeaderRow").prepend(this._getGroupTopLeftCell());this.model.allowScrolling&&this._refreshScroller(u)}},i.prototype._getStackedColumnByTarget=function(n){var r=(n.get(0)||{}).className,i=/e-row([0-9])-column([0-9])/.exec(r),u=i[1],f=i[2],e=[u,"stackedHeaderColumns",f].join(".");return t.getObject(e,this.model.stackedHeaderRows)},i.prototype._checkSkipAction=function(n){switch(n.requestType){case t.HeatMapGrid.Actions.Save:case t.HeatMapGrid.Actions.Delete:return!0}return!1},i.prototype._processBindings=function(i){var r,u,f;if(this._requestType=i.requestType,this.model.query=this.commonQuery.clone(),this.model.editSettings.editMode=="batch"&&i.requestType!="batchsave"&&i.requestType!="cancel"&&!this._confirmedValue&&this._bulkChangesAcquired())return this._requestArgs=i,!1;!t.isNullOrUndefined(this.model.dataSource)&&i.requestType=="refresh"&&this.model.scrollSettings.allowVirtualScrolling&&(this._currentPage(1),this._scrollValue=0,this._loadedJsonData=[],this._prevPage=this._currentPage());this._ensureDataSource(i);this.model.scrollSettings.allowVirtualScrolling&&(i.requestType=="virtualscroll"?(this._loadedJsonData.push({pageIndex:this._prevPage,data:this._currentJsonData}),this._prevPage=this._currentPage()):this.model.scrollSettings.enableVirtualization||(this._virtualLoadedRecords[this._currentPage()]=this.model.currentViewData),i.requestType=="filtering"&&(this._loadedJsonData=[],this._prevPage=this._currentPage()));this.model.scrollSettings.allowVirtualScrolling&&i.requestType=="filtering"&&this.model.filterSettings.filteredColumns.length>0&&this.getScrollObject().scrollY(0);this.model.enableRTL?this.element.hasClass("e-rtl")||this.element.addClass("e-rtl"):this.element.hasClass("e-rtl")&&this.element.removeClass("e-rtl");i.requestType==t.HeatMapGrid.Actions.Delete&&this.model.groupSettings.groupedColumns.length==0&&(this.model.editSettings.showAddNewRow&&this.getContentTable().find(".e-addedrow").remove(),i.tr.remove());this._editForm=this.model.scrollSettings.frozenColumns>0||this.model.editSettings.showAddNewRow?this.element.find(".gridform"):n("#"+this._id+"EditForm");this.model.editSettings.showAddNewRow&&i.requestType=="beginedit"||this._editForm.length==0||this._editForm.length>1&&(i.requestType=="save"&&i.action=="edit"||i.requestType=="cancel")&&(this._editForm=this.model.editSettings.rowPosition=="top"?this._editForm[1]:this._editForm[0]);this._dataSource()instanceof t.DataManager&&!this._isRemoteSaveAdaptor&&i.requestType!=t.HeatMapGrid.Actions.BeginEdit&&i.requestType!=t.HeatMapGrid.Actions.Cancel&&i.requestType!=t.HeatMapGrid.Actions.Add?(this.model.scrollSettings.allowVirtualScrolling&&!this.model.scrollSettings.enableVirtualization&&this.model.pageSettings.totalPages==this.model.pageSettings.currentPage&&(r=t.pvt.filterQueries(this.model.query.queries,"onPage"),this.model.query.queries.splice(n.inArray(r[0],this.model.query.queries),1),this.model.query.page(this._currentPage()-1,this.model.pageSettings.pageSize),u=this._dataSource().executeQuery(this.model.query),this.model.query.queries.splice(n.inArray(r[0],this.model.query.queries),1),this.model.query.page(this._currentPage(),this.model.pageSettings.pageSize)),this._virtualSelectedRows&&this._virtualSelectedRows.length>0&&this.model.query.addParams("virtualSelectRecords",this._virtualSelectedRows),f=this._queryPromise=this._dataSource().executeQuery(this.model.query),proxy._dataSource().ready?proxy._dataSource().ready.done(function(){proxy._processDataRequest(proxy,i,f,u)}):proxy._processDataRequest(proxy,i,f,u)):this._isRelationalRendering(i)?this._setForeignKeyData(i):this.sendDataRenderingRequest(i)},i.prototype._processDataRequest=function(n,i,r,u){r.done(t.proxy(function(r){var e,f,o;if(n._relationalColumns.length==0,u&&!n._previousPageRendered)n.model.previousViewData&&n.model.previousViewData.length!=0&&(n.model.previousViewData.splice(0,r.result.length),n._previousPageLength=r.result.length,n._currentPageData=r.result,t.merge(n.model.previousViewData,r.result),n.model.currentViewData=n.model.previousViewData,n._remoteLastPageRendered=!0);else if(n._remoteLastPageRendered&&n.model.pageSettings.currentPage==n.model.pageSettings.totalPages-1&&!n.model.scrollSettings.enableVirtualization){for(e=n.model.pageSettings.pageSize-n._previousPageLength,f=0;f<e;f++)o=n.getRows()[n.getRows().length-(n.model.pageSettings.pageSize-f)],o.remove();n._tempPageRendered=!0;n.model.currentViewData=r.result}else n.model.pageSettings.currentPage!=n.model.pageSettings.totalPages-1||n._remoteLastPageRendered||(n._previousPageRendered=!0),n.model.currentViewData=r.result==null?[]:r.result,n._$fkColumn&&n.model.filterSettings.filterType=="excel"&&n.model.filterSettings.filteredColumns.length>0&&(n._fkParentTblData=r.result);n.model.allowScrolling&&n.model.scrollSettings.allowVirtualScrolling&&n.model.scrollSettings.enableVirtualization&&(i.requestType=="filtering"&&(n._gridRecordsCount=n._filteredRecordsCount=r.count,n._refreshVirtualViewDetails()),r.result.length?(n._isInitNextPage||n._isLastVirtualpage?(n._setInitialCurrentIndexRecords(r.result,n._currentPage()),n._isInitNextPage=n._isLastVirtualpage=!1):n._setVirtualLoadedRecords(r.result,n._currentPage()),n._isThumbScroll&&!n._checkCurrentVirtualView(n._virtualLoadedRecords,n._currentVirtualIndex)&&n._checkPrevNextViews(),n._remoteRefresh=!0):n.getContent().find(".e-virtualtop, .e-virtualbottom").remove());t.isNullOrUndefined(r.aggregates)||(n._remoteSummaryData=r.aggregates);t.isNullOrUndefined(n._unboundRow)||i.selectedRow==n._unboundRow||i.requestType!="save"||(n._unboundRow.find(".e-editbutton").trigger("click"),n._unboundRow=null)}));r.fail(t.proxy(function(t){i.error=t.error;t=[];n.model.currentViewData=[];n._trigger("actionFailure",i)}))},i.prototype._createUnboundElement=function(i){var f=document.createElement("div"),e,u,r,o;for(i.headerText=t.isNullOrUndefined(i.headerText)?i.field:i.headerText,t.isNullOrUndefined(i.headerText)||(f.id=this._id+i.headerText.replace(/[^a-z0-9|s_]/gi,"")+"_UnboundTemplate"),e=t.buildTag("div.e-unboundcelldiv"),u=i.commands,r=0;r<u.length;r++)o=t.buildTag("button.e-"+u[r].type.replace(/\s+/g,"")+"button","",{},{type:"button"}),o.val(u[r].type),e.append(o);return n("body").append(n(f).html(e).hide()),f},i.prototype._gridTemplate=function(n,t,i){var r=n.model.columns[i];return n._isGrouping&&(this.index=n._currentJsonData.indexOf(this.data)),n._renderEjTemplate("#"+t,this.data,this.index,r)},i.prototype._renderEjTemplate=function(i,r,u,f){var e=null;return((typeof i=="object"||i.startsWith("#")||i.startsWith("."))&&(e=n(i).attr("type")),e&&(e=e.toLowerCase(),t.template[e]))?t.template[e](this,i,r,u,f):t.template.render(this,i,r,u,f)},i.prototype._createTemplateElement=function(i,r,u){var e=i["templateID"in i?"templateID":"template"],s=/^#([\w-]*)/.exec(e),r=r||n("body"),f,h=u?"Pager":i.headerText+n.inArray(i,this.model.columns)+"_Template",o={name:"SCRIPT",type:"text/x-template",text:e,id:(this._id+h).replace(/[^0-9A-z-_]/g,"")};return f=s&&s[1]?document.getElementById(s[1]):/^<script/i.test(e)?n(e).get(0):t.buildTag(o.name,o.text).get(0),f.id=f.id||o.id,f.type=f.type||o.type,r.append(u?n(f).html():f),f},i.prototype._page=function(n){var t;n.events.text==this.localizedLabels.NextPage?(t=this.model.pageSettings.currentPage,++t,this.gotoPage(t)):n.events.text==this.localizedLabels.PreviousPage?(t=this.model.pageSettings.currentPage,t>1?(--t,this.gotoPage(t)):this.gotoPage(t)):n.events.text==this.localizedLabels.LastPage?(t=this.model.pageSettings.totalPages,this.gotoPage(t)):this.gotoPage(1)},i.prototype.gotoPage=function(n){var i,r,u;(this.model.allowPaging||this.model.allowScrolling||this.model.scrollSettings.allowVirtualScrolling)&&(i={},i.previousPage=this._currentPage(),this._currentPage(n),i.endIndex=this._currentPage()*this.model.pageSettings.pageSize>this._gridRecordsCount?this._gridRecordsCount:this._currentPage()*this.model.pageSettings.pageSize,i.startIndex=this._currentPage()*this.model.pageSettings.pageSize-this.model.pageSettings.pageSize,i.currentPage=n,this.model.allowPaging&&(i.requestType=t.HeatMapGrid.Actions.Paging),this.model.scrollSettings.allowVirtualScrolling&&this.model.allowScrolling&&(this._isVirtualRecordsLoaded=!1,u=this._refreshVirtualPagerInfo(),this._showPagerInformation(u),i.requestType=t.HeatMapGrid.Actions.VirtualScroll),r=this._processBindings(i),r&&this._currentPage(i.previousPage),this._primaryKeyValues=[])},i.prototype._checkScrollActions=function(n){return!this.model.scrollSettings.allowVirtualScrolling&&(n==t.HeatMapGrid.Actions.Sorting||n==t.HeatMapGrid.Actions.Reorder)||n==t.HeatMapGrid.Actions.Grouping||n==t.HeatMapGrid.Actions.Ungrouping||n==t.HeatMapGrid.Actions.Add||n==t.HeatMapGrid.Actions.Cancel||n==t.HeatMapGrid.Actions.Save||n==t.HeatMapGrid.Actions.BatchSave||n==t.HeatMapGrid.Actions.Delete||n==t.HeatMapGrid.Actions.Filtering||n==t.HeatMapGrid.Actions.Paging||n==t.HeatMapGrid.Actions.Refresh||n==t.HeatMapGrid.Actions.Search?!0:!1},i.prototype._frozenAlign=function(){var t=this.getContent().first(),i=this.getBrowserDetails(),n;n=this.model.enableRTL?"margin-right":"margin-left";t.find(".e-movablecontent").css(n,i.browser==="safari"?"auto":t.find(".e-frozencontentdiv").width()+"px");this.getHeaderContent().find(".e-movableheader").removeAttr("style").css(n,i.browser==="safari"?"auto":this.getHeaderContent().find(".e-frozenheaderdiv").width()+"px")},i.prototype._refreshScroller=function(n){var r=this.getContent().first(),i,u,f,e,o,s;t.isNullOrUndefined(r.data("ejScroller"))||(this.model.scrollSettings.frozenColumns>0&&(this._frozenAlign(),this.refreshScrollerEvent(),r.find(".e-movablecontent").scrollLeft(this.getHeaderContent().find(".e-movableheader").scrollLeft()),!t.isNullOrUndefined(this.getScrollObject()._vScrollbar)&&this.getScrollObject()._vScrollbar.value()>this.getScrollObject()._vScrollbar.model.maximum&&(i=this.getScrollObject()._vScrollbar.model.maximum)),this.model.scrollSettings.frozenRows>0&&(this._initFrozenRows(),i=this.getScrollObject().model.scrollTop,!t.isNullOrUndefined(this.getScrollObject()._vScrollbar)&&i>this.getScrollObject()._vScrollbar.model.maximum&&(i=this.getScrollObject()._vScrollbar.model.maximum),(n.requestType=="cancel"||n.requestType=="save")&&i>this._editFormHeight&&this.model.editSettings.editMode.indexOf("inlineform")!=-1&&(i=i-this._editFormHeight),n.requestType==t.Grid.Actions.Add&&this.getScrollObject().scrollY(0,!0),t.isNullOrUndefined(this.getScrollObject()._vScrollbar)||t.isNullOrUndefined(this.getScrollObject()._vScrollbar._scrollData)||(this.getScrollObject()._vScrollbar._scrollData.skipChange=!0)),n.requestType=="beginedit"&&(i=this.getScrollObject().model.scrollTop,this.getScrollObject().scrollY(0,!0)),!t.isNullOrUndefined(this.model.dataSource)&&(n.requestType=="refresh"||n.requestType=="searching")&&this.model.scrollSettings.allowVirtualScrolling&&(this.model.scrollSettings.enableVirtualization&&this._isLocalData&&this._gridRecordsCount>0?this._refreshVirtualView(this._currentVirtualIndex):this._refreshVirtualContent(1),this._currentVirtualIndex==1&&this.getScrollObject().scrollY(0)),this.model.scrollSettings.frozenColumns>0&&n.requestType!="filtering"?this.rowHeightRefresh():this.getScrollObject().refresh(),r.ejScroller("model.enableRTL",this.model.enableRTL),this.model.isResponsive&&(n.requestType=="searching"||n.requestType=="filtering")&&(u=this.getScrollObject(),f=u.isHScroll()?this.getContentTable().height()+u.model.buttonSize:this.getContentTable().height(),f>this.model.scrollSettings.height&&(f=this.model.scrollSettings.height),e=typeof this.model.scrollSettings.width=="string"?this.element.width()-u.model.buttonSize:this.model.scrollSettings.width,o=e,this.getContent().ejScroller({height:f,width:o})),r.ejScroller("isVScroll")&&!this.getScrollObject().model.autoHide?(this.getHeaderContent().addClass("e-scrollcss"),this.getHeaderContent().find(".e-headercontent").hasClass("e-hscrollcss")||this.getHeaderContent().find(".e-headercontent").addClass("e-hscrollcss")):this._showHideScroller(),this._getRowHeights(),i&&!t.isNullOrUndefined(this.getScrollObject()._vScrollbar)&&n.requestType!=t.Grid.Actions.Add&&(this._currentTopFrozenRow=0,i>this.getScrollObject()._vScrollbar.model.maximum&&(i=this.getScrollObject()._vScrollbar.model.maximum),this.getScrollObject()._vScrollbar.scroll(i)),n.requestType=="virtualscroll"&&(s=this.getScrollObject().model.scrollTop+this.getScrollObject().model.height-this.getScrollObject().model.height*.3,this.getScrollObject().scrollY(s,!0)))},i.prototype._isFrozenColumnVisible=function(){for(var n=0;n<this.model.scrollSettings.frozenColumns;n++)if(this.model.columns[n].visible)return!0;return!1},i.prototype._frozenPaneRefresh=function(){this.getContent().find(".e-frozencontentdiv").css("display","none");this.getHeaderContent().find(".e-frozenheaderdiv").css("display","none");this.getHeaderContent().find(".e-movableheader")[0].style["margin-left"]="";this.getContent().find(".e-movablecontent")[0].style["margin-left"]="";var i=t.isNullOrUndefined(this._scrollObject._vScrollbar)?0:this._scrollObject._vScrollbar["e-vscroll"].width(),n=this.model.scrollSettings.width-i-1;this.model.scrollSettings.width>this.getContent().find(".e-movablecontentdiv").width()&&(this.getContent().find(".e-movablecontentdiv").width(n),this.getHeaderContent().find(".e-movableheaderdiv").width(n));this._scrollObject.option("scrollLeft",0)},i.prototype._renderScroller=function(){var e,o,u,f,r,l,s,i,h,c;if(this.model.scrollSettings||(this.model.scrollSettings={}),this.model.enablePersistence&&(t.isNullOrUndefined(this.model.scrollSettings.previousStateWidth)||!this.model.scrollSettings.previousStateWidth)&&this.model.isResponsive&&(this.model.scrollSettings.previousStateWidth=this.model.scrollSettings.width),typeof this._originalScrollWidth!="string"||this.model.isResponsive||(this.element.css("width","auto"),e=this.element.width(),(this.model.scrollSettings.width=="auto"||this._originalScrollWidth=="auto")&&(this._originalScrollWidth="100%"),this.model.scrollSettings.width=e*(parseFloat(this._originalScrollWidth)/100)),typeof this.model.scrollSettings.height!="string"||this.model.isResponsive||(o=this.element.height(),this.model.scrollSettings.height=="auto"&&(this.model.scrollSettings.height="100%"),this.model.scrollSettings.height=o*(parseFloat(this.model.scrollSettings.height)/100)),(this.model.scrollSettings.width||this.model.width)&&!this._mediaQuery&&this.element.width(this.model.scrollSettings.width||this.model.width),u=this.getContent().attr("tabindex","0"),l=this.getRows(),this.model.scrollSettings.frozenColumns>0){if(s=this.getContent().find(".e-frozencontentdiv").width()+20,s>this.model.scrollSettings.width){this.getContent().remove();this.getHeaderTable().eq(1).remove();return}f=this.getContent().find(".e-frozencontentdiv").width();r=this.model.enableRTL?"margin-right":"margin-left";this.getContent().find(".e-movablecontent").css(r,f+"px");this.getHeaderContent().find(".e-movableheader").css(r,f+"px");this.model.scrollSettings.targetPane=".e-movablecontent"}this._initFrozenRows();this.model.scrollSettings.autoHide&&(this.model.scrollSettings.show=n.proxy(this._showHideScroller,this));i=this;this.model.scrollSettings.frozenRows||(this.model.scrollSettings.scroll=function(n){t.isNullOrUndefined(n.scrollData)||n.scrollData.handler!="e-hhandle"?(i._scrollValue=n.scrollTop,i.model.currentIndex=n.scrollTop==0?n.scrollTop:Math.floor(n.scrollTop/i._vRowHeight)):(i.model.allowFiltering&&(i.model.filterSettings.filterType=="menu"||i._isExcelFilter)&&(i._isExcelFilter?i._excelFilter.closeXFDialog():i._closeFilterDlg()),i._checkScroller(n,this))});!this.model.scrollSettings.allowVirtualScrolling&&this.model.currentIndex>0&&!this.model.scrollSettings.scrollTop&&(h=this.model.currentIndex*this.getRowHeight(),this.model.scrollSettings.scrollTop=h);this.model.scrollSettings&&!this.model.scrollSettings.height&&(this.model.scrollSettings.height=0);u.ejScroller(this.model.scrollSettings);this.model.rowTemplate!=null&&(this.getBrowserDetails().browser=="msie"||this.getBrowserDetails().browser=="safari")&&this.getScrollObject().refresh();this.model.scrollSettings.frozenColumns>0&&this.model.scrollSettings.frozenRows==0&&this.getScrollObject()._vScrollbar&&this.getScrollObject()._hScrollbar&&(this.getScrollObject()._vScrollbar._scrollData.skipChange=this.getScrollObject()._hScrollbar._scrollData.skipChange=!0);this.model.scrollSettings.autoHide||this._showHideScroller();this.getBrowserDetails().browser=="safari"&&this.model.scrollSettings.frozenColumns>0&&this.getHeaderContent().find(".e-movableheader").add(this.getContent().find(".e-movablecontent")).css(r,"auto");this.refreshScrollerEvent();this.model.scrollSettings.frozenColumns>0&&!this._isFrozenColumnVisible()&&this._frozenPaneRefresh();i.model.scrollSettings.allowVirtualScrolling&&(c=this._refreshVirtualPagerInfo(),this._showPagerInformation(c),u.ejScroller({scroll:function(r){if(i.model.scrollSettings.enableVirtualization&&r.scrollData!=null&&r.scrollData.handler!="e-hhandle"){if(r.reachedEnd=r.scrollData.scrollable-r.scrollTop==0,r.source=="thumb"){var f=Object.keys(i._virtualLoadedRows),u=(i._currentVirtualIndex+2).toString();i.model.scrollSettings.virtualScrollMode=="continuous"&&n.inArray(u,f)==-1&&u<i._totalVirtualViews?i._isContinuous=!0:(r.model.scrollTop=r.scrollTop,i._isContinuous=!1,r.cancel=!0)}(r.source=="button"||r.source=="key"||r.source=="wheel")&&(i._isThumbScroll=!1,i._virtualViewScroll(r),i.model.scrollSettings.virtualScrollMode=="continuous"&&r.reachedEnd&&this.refresh());i.model.currentIndex=r.scrollTop==0?r.scrollTop:Math.floor(r.scrollTop/i._vRowHeight)}else!t.isNullOrUndefined(r.scrollData)&&r.scrollData.handler=="e-hhandle"&&i.model.allowFiltering&&(i.model.filterSettings.filterType=="menu"||i._isExcelFilter)&&(i._isExcelFilter?i._excelFilter.closeXFDialog():i._closeFilterDlg()),r.reachedEnd=this.content()[0].scrollHeight-r.scrollTop==this.content()[0].clientHeight,(r.source=="button"||r.source=="key"||r.source=="wheel")&&i.model!=null&&i._virtualScroll(r),r.source=="wheel"&&r.scrollTop!=i._scrollValue&&(r.scrollTop=i._scrollValue),i._checkScroller(r,this)},thumbEnd:function(t){(i.model.scrollSettings.enableVirtualization&&i.model.scrollSettings.virtualScrollMode=="continuous"?t.reachedEnd=t.scrollData.scrollable-t.model.scrollTop==0:t.originalEvent&&!n(t.originalEvent.target).hasClass("e-rowcell")&&(t.reachedEnd=this.content()[0].scrollHeight-t.scrollData.sTop==this.content()[0].clientHeight),t.scrollData.handler!="e-hhandle")&&i.model!=null&&t.originalEvent&&(i.model.scrollSettings.enableVirtualization?(i._isThumbScroll=!0,i._virtualViewScroll(t),i.model.scrollSettings.virtualScrollMode=="continuous"&&t.reachedEnd&&this.refresh()):i._virtualScroll(t))},scrollEnd:function(t){if(t.scrollData.type!="mousewheel"&&(t.scrollData.model==null||t.scrollData.model.orientation!="horizontal")&&i.model.scrollSettings.enableVirtualization&&!i._isContinuous){var r=i._calculateCurrentViewPage(t.model),u=n.inArray(r,i._virtualLoadedPages)!=-1;u?(i._isThumbScroll=!0,i._virtualViewScroll(t),i._totalVirtualViews<=i._maxViews*3&&(this._content[0].scrollTop=t.scrollData.scrollTop)):t.cancel=!0}}}))},i.prototype._checkScroller=function(n,t){var i=n.scrollLeft>0?n.scrollLeft:Math.abs(n.scrollLeft),r;n.source=="thumb"&&(t.content()[0].scrollWidth-i==t.content()[0].clientWidth||i==0)&&(this.model.enableRTL&&(r=i==0?n.scrollData.scrollable:0,n.scrollData.sTop=n.model.scrollLeft=r,t.content().scrollLeft(r)),t.refresh())},i.prototype._showHideScroller=function(){this.getContent().ejScroller("isVScroll")?(this.getHeaderContent().find("div").first().addClass("e-headercontent"),this.model.scrollSettings.autoHide||this.getHeaderContent().addClass("e-scrollcss")):this.element.find(".e-gridheader").removeClass("e-scrollcss");this.getBrowserDetails().browser=="msie"||this.model.scrollSettings.frozenColumns!=0||this._mediaQuery||(!this.element.find(".e-gridheader").hasClass("e-scrollcss")&&(this.model.filterSettings.filteredColumns.length||this._hiddenColumns.length)?(this.getHeaderTable().removeAttr("style"),this.getContentTable().removeAttr("style")):(this.getHeaderContent().find("div table").first().width(this.getContentTable().width()),this.getContent().find("div table").first().width(this.getContentTable().width()),this.getHeaderTable().width(this.getContentTable().width())));this.getBrowserDetails().browser=="msie"&&this.model.scrollSettings.frozenColumns==0&&(this.getContent().ejScroller("isVScroll")?this.getContent().width(this.getHeaderContent().width()+18):this.getContent().width(this.getHeaderContent().width()));this._isHscrollcss()},i.prototype._isHscrollcss=function(){var n=this.getContent().data("ejScroller"),t=n&&(n.isHScroll()||n.isVScroll())?"addClass":"removeClass";this.getHeaderContent().find(".e-headercontent")[t]("e-hscrollcss")},i.prototype._initFrozenRows=function(){var i=this.getRows();this.model.currentViewData&&this.model.currentViewData.length!=0&&(this.model.scrollSettings.frozenRows>0&&i!=null?(this.model.scrollSettings.scroll=n.proxy(this._scroll,this),this.getContent().find(".e-frozeny").removeClass("e-frozeny").end().find(".e-frozenrow").removeClass("e-frozenrow"),!t.isNullOrUndefined(i[0][this.model.scrollSettings.frozenRows-1])&&!t.isNullOrUndefined(i[1][this.model.scrollSettings.frozenRows-1])&&this.model.scrollSettings.frozenColumns>0?n(i[0][this.model.scrollSettings.frozenRows-1].cells).add(i[1][this.model.scrollSettings.frozenRows-1].cells).addClass("e-frozeny").parent().addClass("e-frozenrow"):t.isNullOrUndefined(this.getRowByIndex(this.model.scrollSettings.frozenRows-1)[0])||n(i[this.model.scrollSettings.frozenRows-1].cells).addClass("e-frozeny").parent().addClass("e-frozenrow"),this.model.scrollSettings.height=this._rowHeightCollection[Math.floor(this.model.scrollSettings.height/this._rowHeightCollection[1])]+18):delete this.model.scrollSettings.scroll)},i.prototype.refreshScrollerEvent=function(){var i=this;this.getContent().find(".e-content:first,.e-movablecontent").scroll(t.proxy(function(t){if(this.model.scrollSettings.targetPane?this.getHeaderContent().find(".e-movableheader").scrollLeft(n(t.currentTarget).scrollLeft()):this.getHeaderContent().find("div").first().scrollLeft(n(t.currentTarget).scrollLeft()),this.model.scrollSettings.frozenRows>0&&this.model.editSettings.editMode.indexOf("inlineform")!=-1&&this.model.isEdit){var i=t.target.scrollTop;this.getContent().find(".e-content").scrollTop(0);this.getScrollObject().scrollY(this.getScrollObject().model.scrollTop+i,!0)}},this));this.element.find(".e-gridheader").find(".e-headercontent,.e-movableheader").scroll(t.proxy(function(t){var i=n(t.currentTarget);this.model.scrollSettings.targetPane?(this.getContent().find(".e-movablecontent").scrollLeft(i.scrollLeft()),this.model.showSummary&&this.getFooterContent().find(".e-movablefooter").scrollLeft(i.scrollLeft())):(this.model.showSummary&&this.getFooterContent().scrollLeft(i.scrollLeft()),this.getContent().find(".e-content").first().scrollLeft(i.scrollLeft()))},this))},i.prototype._renderByFrozenDesign=function(){var r=n(document.createElement("div")),u=this._getMetaColGroup().find("col"),i={};i.colgroup1=r.append(t.buildTag("colgroup").append(u.splice(0,this.model.scrollSettings.frozenColumns))).html();i.colgroup2=r.html(t.buildTag("colgroup").append(u)).html();this.getContent().find("div").first().get(0).innerHTML=n.render[this._id+"_FrozenTemplate"]({datas:this.model.currentViewData},i);this.setGridContentTable(this.getContent().find(".e-table").attr("data-role","heatmap"))},i.prototype.addFrozenTemplate=function(){var i="<div class='e-frozencontentdiv'><table class='e-table'>{{:~colgroup1}}<tbody>{{for datas tmpl='"+this._id+"_JSONFrozenTemplate'/}}<\/tbody><\/table><\/div><div class='e-movablecontent'><div class='e-movablecontentdiv'><table class='e-table'>{{:~colgroup2}}<tbody>{{for datas tmpl='"+this._id+"_JSONTemplate'/}}<\/tbody><\/table><\/div><\/div>",t={};t[this._id+"_FrozenTemplate"]=i;n.templates(t)},i.prototype._getTopRow=function(n){var i=this.model.scrollSettings.frozenRows,t=0;if(n>10)for(t=0;t<this._rowHeightCollection.length;t++)if(this._rowHeightCollection[t]>n){i=this.model.scrollSettings.frozenRows+t-1;break}return{imaginaryIndex:i,actualIndex:t}},i.prototype._showHideRow=function(t,i,r,u){var f=this.getRows();this.model.scrollSettings.frozenColumns>0?n(f[0]).slice(t,i).add(n(f[1]).slice(t,i).toArray())[r]():n(f).slice(t,i)[r]();this._currentTopFrozenRow=r=="show"?t:i;this.getScrollObject()._changevHandlerPosition(u)},i.prototype._scroll=function(n){var i,f,e,o,u;if(n.scrollData!=null&&n.scrollData.dimension!="width"){n.cancel=!0;var h=this.getRows(),s=this._getTopRow(n.scrollTop),r=s.imaginaryIndex;if(r>this._currentTopFrozenRow?this._showHideRow(this.model.scrollSettings.frozenRows,r,"hide",n.scrollTop):r<this._currentTopFrozenRow&&this._showHideRow(r,this._currentTopFrozenRow+1,"show",n.scrollTop),i=this.getContentTable().last().find("tr"),f=parseInt(i.last().find("td:first").css("border-top-width"))*2+1,n.scrollTop==this.getScrollObject()._vScrollbar.model.maximum&&i.last()[0].offsetTop+i.last().height()-f>this.element.find(".e-content").height()){for(e=i.last().prev()[0].offsetTop+i.last().prev().height(),o=1,u=i.length-2;e-f>this.element.find(".e-content").height();u++){e=i[u].offsetTop+i.eq(u).height();o++;break}this._showHideRow(this.model.scrollSettings.frozenRows,r+o,"hide",n.scrollTop)}n.model.scrollTop=n.scrollTop}else t.isNullOrUndefined(this.getScrollObject()._vScrollbar)||t.isNullOrUndefined(this.getScrollObject()._vScrollbar._scrollData)||(this.getScrollObject()._vScrollbar._scrollData.skipChange=!0)},i.prototype._virtualScroll=function(t){var i,f;if(t!=null){var u=0,e=this.model.filterSettings.filteredColumns.length==0?this._searchCount==null?this._gridRecordsCount:this._searchCount:this._filteredRecordsCount,r=this.model.pageSettings,o=this.getContentTable()[0].tBodies[0],s=n(o).find("tr.e-virtualrow");if(r.totalPages=Math.ceil(e/r.pageSize),t.scrollTop!==undefined&&(t.model.scrollTop=t.scrollTop),t.reachedEnd!=undefined&&(t.model.reachedEnd=t.reachedEnd),i=this._calculateCurrenPage(s,this.getContentTable(),t.model),i>r.totalPages&&(i=r.totalPages),r.currentPage!=i&&n.inArray((i-1)*r.pageSize,this.virtualLoadedPages)==-1&&(this._isVirtualRecordsLoaded=!1),this._isVirtualRecordsLoaded)r.currentPage=i;else{if(n.inArray((i-1)*r.pageSize,this.virtualLoadedPages)==-1){if(this.model.scrollSettings.virtualScrollMode=="continuous"&&!t.reachedEnd)return;i==r.totalPages&&n.inArray((i-2)*r.pageSize,this.virtualLoadedPages)==-1&&(u++,this.set_currentPageIndex(i));u==1&&(this._lastRow=!0);this.set_currentPageIndex(i)}r.currentPage=i}f=this._refreshVirtualPagerInfo();this._showPagerInformation(f)}},i.prototype._virtualViewScroll=function(t){if(t!=null){t.scrollTop!==undefined&&(t.model.scrollTop=t.scrollTop);t.reachedEnd!=undefined&&(t.model.reachedEnd=t.reachedEnd);var i=this._calculateCurrentVirtualIndex(t);n.inArray(i,this._currentLoadedIndexes)==-1&&(this._isVirtualRecordsLoaded=!1);this._isVirtualRecordsLoaded||this.set_currentVirtualIndex(i)}},i.prototype._refreshVirtualContent=function(t){var f=this.getRowHeight(),e=this.model.filterSettings.filteredColumns.length==0?this._searchCount==null?this._gridRecordsCount:this._searchCount:this._filteredRecordsCount,o,i,u,s,h,c,r;t!=null&&(this._currentPage(t),o=this._refreshVirtualPagerInfo(),this._showPagerInformation(o));i=this.getCurrentIndex();u=this.getContentTable()[0].tBodies[0];i>0&&(s=document.createElement("tr"),n(s).addClass("e-virtualrow").css("height",f*i).prependTo(u));i+this.model.pageSettings.pageSize<=e&&this.getContentTable().find("tr").last().hasClass("e-virtualrow")!=!0&&this.model.scrollSettings.frozenColumns==0&&(h=document.createElement("tr"),c=this.model.scrollSettings.virtualScrollMode=="normal"?f*(e-(i+this.model.pageSettings.pageSize)):1,n(h).addClass("e-virtualrow").css("height",c).appendTo(n(u)));this.virtualLoadedPages=[];this.orderedVirtualLoadedPage=[];this.virtualLoadedPages.push(i>=0?i:0);this.orderedVirtualLoadedPage.push(i>=0?i:0);r=n(u).find("tr:not(.e-virtualrow)").attr("name",i>=0?i:0)[0];r&&r.previousSibling&&(n(r.previousSibling).hasClass("e-virtualrow")||r.previousSibling.offsetTop>i*this.getContent().height())&&(this.getContent().children("div").first().scrollTop(this.getContent().find(".content").scrollTop()-(this.getContent().find(".content").scrollTop()-r.offsetTop)),this._isVirtualRecordsLoaded=!0)},i.prototype._refreshVirtualView=function(t){var u,e,r,o,h,f,i;if(this._singleView)this._singleView=!1,this._addLastRow(),this.getContent().find(".e-virtualtop, .e-virtualbottom").remove(),f=this._currentVirtualIndex.toString(32),n(this._gridRows).attr("name",f),this._virtualLoadedRows[this._currentVirtualIndex]=this._gridRows,this._eventBindings();else{if(u=this._virtualRowCount,t)t>this._totalVirtualViews&&(t=1,e=!0),this._currentVirtualIndex=t,this._virtualLoadedRecords[t]?r=Math.ceil(this.model.currentIndex/this.model.pageSettings.pageSize):(this._virtualDataRefresh||this._currentVirtualIndex==this._totalVirtualViews||(e=!0),r=Math.ceil(t*this._virtualRowCount/this.model.pageSettings.pageSize)),this._refreshVirtualViewScroller(e),r>this.model.pageSettings.totalPages&&(r=this.model.pageSettings.totalPages),r<=0&&(r=1),n.inArray(r,this._virtualLoadedPages)==-1?this.gotoPage(r):(this._currentPage(r),this._needPaging=this._checkCurrentVirtualView(this._virtualLoadedRecords,t)?!1:!0,this._getVirtualLoadedRecords(this.model.query),this._replacingVirtualContent());else{if(this._refreshVirtualViewDetails(),o=n(this.getContentTable()[0].rows),this._setVirtualTopBottom(),this.initialRender){for(i=0;i<this._currentLoadedIndexes.length;i++){var s=this._currentLoadedIndexes[i],l=(i+1)*u,c=i*u;n(o[l-1]).addClass("e-virtualview"+s);f=s.toString(32);h=o.slice(c,c+u).attr("name",f).detach();this._virtualLoadedRows[s]=h;h.appendTo(this.getContentTable())}this._currentVirtualIndex>1&&this._refreshVirtualViewScroller()}this._eventBindings()}n.inArray(this._currentPage(),this._virtualLoadedPages)==-1&&this._virtualLoadedPages.push(this._currentPage())}if(!t&&(this.model.queryCellInfo||this.model.rowDataBound))for(i=0;i<this._currentLoadedIndexes.length;i++)n.inArray(this._currentLoadedIndexes[i],this._queryCellView)==-1&&this._queryCellView.push(this._currentLoadedIndexes[i]);this._isThumbScroll=!1;this._virtualDataRefresh=!1},i.prototype._refreshVirtualViewData=function(){this._virtualLoadedRecords={};this._virtualLoadedRows={};this._virtualLoadedPages=[];this._virtualPageRecords={};this._queryCellView=[];this.model.pageSettings.totalPages!=null&&this._currentPage()>this.model.pageSettings.totalPages&&(this._currentPage(1),this._currentVirtualIndex=1)},i.prototype.setCurrentPageData=function(n){this.model.scrollSettings.allowVirtualScrolling&&this.model.scrollSettings.enableVirtualization&&(this._refreshVirtualViewData(),this._refreshVirtualViewDetails(),this._setVirtualLoadedRecords(n,this._currentPage()),this._refreshVirtualView(this._currentVirtualIndex))},i.prototype._refreshVirtualViewScroller=function(n){var t,i;this.initialRender&&!this.model.scrollSettings.scrollTop||n?(i=this._vRowHeight,t=this.model.currentIndex*this._vRowHeight):t=this._scrollObject.model.scrollTop;this.getContent().ejScroller("model.scrollTop",t);this._scrollValue=t},i.prototype._calculateCurrentViewPage=function(n){n||(n=this._scrollObject.model);var i=this.model.pageSettings.pageSize,t=Math.ceil((n.scrollTop+this.model.scrollSettings.height)/this._vRowHeight/i);return this.model.pageSettings.totalPages==null&&(this.model.pageSettings.totalPages=Math.ceil(this._getVirtualTotalRecord()/i)),t>this.model.pageSettings.totalPages&&(t=this.model.pageSettings.totalPages),t},i.prototype._calculateCurrentVirtualIndex=function(t){var h=t.model,p=this._getVirtualTotalRecord(),i,c,l,u=[],e,o,a,r=h.scrollTop,y=r+this.model.scrollSettings.height,s,v,f;if(i=y/this._vRowHeight/this._virtualRowCount,i=this._prevVirtualIndex>this._currentVirtualIndex&&r<=this._scrollValue?Math.floor(i):Math.ceil(i),r>=this._scrollValue&&h.virtualScrollMode=="continuous"&&h.reachedEnd&&(i=i+1),i>this._totalVirtualViews&&(i=this._totalVirtualViews),i<=0&&(i=1),n.inArray(i,this._currentLoadedIndexes)!==-1&&this._virtualLoadedRows[i]&&r!=t.scrollData.scrollable){for(s=this.getContentTable()[0].rows,a=s.length,v=this.getContent().find(".e-virtualtop").height(),l=r>=this._scrollValue,f=0;f<a;f++){if(e=s[f],o=e.offsetHeight+e.offsetTop+v,o>r+this.model.scrollSettings.height){u.length===0&&f!==0&&(u=[s[e.offsetTop<=r+this.model.scrollSettings.height?f:f-1]]);break}if(o>=r&&o<=r+this.model.scrollSettings.height&&(u.push(e),l===!1&&u.length>1))break}c=n(r>=this._scrollValue?u[u.length-1]:u[0]);c.length&&(i=parseInt(c.attr("name"),32))}return this._scrollValue=r,i},i.prototype._calculateCurrenPage=function(t,i,r){var o=this.model.pageSettings.pageSize,u,a=this,v,w,f=[],s,h,b,k,y,g=this.getRowHeight(),c,e,l,p,d;if(u=(r.scrollTop+this.model.scrollSettings.height)/g/o,k=this.model.pageSettings.pageSize*(this.model.pageSettings.currentPage-1),y=this.getContentTable().find("tr[name="+k+"]").eq(0),u=y.length&&y.offset().top>0&&u>=1&&r.scrollTop<this._scrollValue&&this.virtualLoadedPages.indexOf(Math.ceil(u-1)*o)!==-1?Math.floor(u):Math.ceil(u),r.scrollTop>=this._scrollValue&&r.virtualScrollMode=="continuous"&&r.reachedEnd&&(u=this.virtualLoadedPages[this.virtualLoadedPages.length-1]/o+2),n.inArray((u-1)*o,this.virtualLoadedPages)!==-1){for(c=this.getContentTable().children("tbody").children("tr"),b=c.length,w=r.scrollTop>=this._scrollValue,e=0;e<b;e++){if(s=c[e],h=s.offsetHeight+s.offsetTop,h>r.scrollTop+a.model.scrollSettings.height){f.length===0&&e!==0&&(f=[c[s.offsetTop<=r.scrollTop+a.model.scrollSettings.height?e:e-1]]);break}if(h>=r.scrollTop&&h<=r.scrollTop+a.model.scrollSettings.height&&(f.push(s),w===!1&&f.length>1))break}v=n(r.scrollTop>=this._scrollValue?f[f.length-1]:f[0]);v.hasClass("e-virtualrow")?f.length===1&&u++:u=parseInt(v.attr("name"),10)/o+1}for(this._scrollValue=r.scrollTop,l=0;l<t.length;l++)if(p=t[l],p.offsetTop+p.offsetHeight>=r.scrollTop)return d=this._calculatePrevPage(t,i,r),this._prevPageNo=d,u==0&&(u=1),u>this.model.pageSettings.totalPages?this.model.pageSettings.totalPages:u;return u},i.prototype._calculatePrevPage=function(t,i,r){for(var f,e,u=0;u<t.length;u++)if(f=t[u],f.offsetTop+f.offsetHeight>=r.scrollTop&&(e=n(f).prevAll("tr[name]")[0],e!=null))return Math.ceil(parseInt(n(e).attr("name"),10)/this.model.pageSettings.pageSize)+1;return-1},i.prototype._refreshVirtualPagerInfo=function(){var n={};return n.pageSize=this.model.pageSettings.pageSize,n.currentPage=this._currentPage(),n.totalRecordsCount=this.model.filterSettings.filteredColumns.length==0?this._searchCount==null?this._gridRecordsCount:this._searchCount:this._filteredRecordsCount,n.totalPages=Math.ceil(n.totalRecordsCount/n.pageSize),n},i.prototype._showPagerInformation=function(t){var i=(t.currentPage-1)*t.pageSize;n(this.$pagerStatusBarDiv).find("div:first").html(String.format("{0} of {1} pages ({2} items)",t.currentPage,t.totalPages,t.totalRecordsCount),i,i+t.pageSize);n(this.$pagerStatusBarDiv).css("display","block")},i.prototype._replacingContent=function(){var yt=document.createElement("div"),u=this.getCurrentIndex(),o=this.getContentTable()[0],rt=n(o).find("colgroup").first(),l=this.getRowHeight(),r,a,ut,f,h,b,e,y,ft,k,et,d,ot,st,nt,i,s,at,tt,kt,dt,it,vt;rt.replaceWith(this._getMetaColGroup());(this.model.detailsTemplate!=null||this.model.childGrid!=null)&&rt.prepend(this._getIndentCol());r=o.tBodies[0];a=this.model.currentViewData;t.isNullOrUndefined(this._currentPageData)?this._virtualLoadedRecords[this._currentPage()]=a:(this._virtualLoadedRecords[this._currentPage()]=this._currentPageData,this._currentPageData=null);var pt=n("<tbody><\/tbody>").append(n.render[this._id+"_JSONTemplate"](a)),w=this,v=pt.children("tr");if(this._allowcolumnSelection&&this.selectedColumnIndexes.length>0)for(s=0;s<this.selectedColumnIndexes.length;s++)ut=this.selectedColumnIndexes[s]+1,v.find("td:nth-of-type("+ut+")").addClass("e-columnselection");for(this.virtualLoadedPages.push(u>=0?u:0),f=t.dataUtil.mergeSort(t.distinct(this.virtualLoadedPages)),n(v).attr("name",u),h=t.dataUtil.min(f),b=t.dataUtil.max(f),n(r).children(".e-virtualrow").remove(),i=0;i<f.length;i++)e=f[i],y=f[i-1],(e!=this.orderedVirtualLoadedPage[i]||this.orderedVirtualLoadedPage[i]==undefined)&&(y!=undefined?v.insertAfter(n(r).children("[name="+y+"]:last")):v.insertBefore(n(r).children("[name="+this.orderedVirtualLoadedPage[i]+"]:first")),this.orderedVirtualLoadedPage=f),e!=0&&(ft=e==h?h:y,k=e-ft-w.model.pageSettings.pageSize,k>0&&(et=document.createElement("tr"),n(et).addClass("e-virtualrow").css("height",l*k).insertBefore(n(r).children("[name="+e+"]:first")))),e==b&&(d=w._gridRecordsCount-b-w.model.pageSettings.pageSize,d>0&&(ot=document.createElement("tr"),n(ot).addClass("e-virtualrow").css("height",l*d).appendTo(r)));h>0&&(st=document.createElement("tr"),n(st).addClass("e-virtualrow").css("height",l*h).prependTo(r));var ht=this.getContent(),p=n(r).children("tr[name="+u+"]")[0],g=p.previousSibling,ct=ht.height(),lt=p.offsetTop;if(this._virtaulUnSel)for(nt=n.extend(!0,[],this._virtaulUnSel),i=0;i<nt.length;i++){var c=nt[i],wt=this.model.pageSettings.currentPage,bt=c%this.model.pageSettings.pageSize==0?parseInt(c/this.model.pageSettings.pageSize):parseInt(c/this.model.pageSettings.pageSize)+1;bt==wt&&(s=c%this.model.pageSettings.pageSize,at=n(r).find("tr[name="+u+"]").eq(s),at.attr("aria-selected","true").find(".e-rowcell, .e-detailrowcollapse, .e-detailrowexpand").addClass("e-selectionbackground e-active"),tt=this._virtaulUnSel.indexOf(c),tt!=-1&&this._virtaulUnSel.splice(tt,1))}(p&&g&&(this._virIndex||n(g).hasClass("e-virtualrow")||g.offsetTop>u*ct)&&(this._gridRecordsCount-u>=this.model.pageSettings.pageSize||p.offsetParent.offsetHeight-lt<ct)||this._lastRow)&&(this._lastRow&&(this._lastRow=!1),this._virIndex&&(this._virIndex=!1),this._isVirtualRecordsLoaded=!0,ht.find(".e-content").scrollTop(lt),this._scrollValue=this.getContent()[0].firstChild.scrollTop);kt=n(o).get(0);dt=yt.firstChild;this._currentJsonData=a;this._gridRows=n(o).get(0).rows;it=n(o).find(".e-virtualrow").last();vt=this.model.scrollSettings.virtualScrollMode=="normal"?it.height()-(n(o).height()-this._gridRecordsCount*l):1;it.css("height",vt);this._eventBindings()},i.prototype._replacingVirtualContent=function(){var f=this.getContentTable()[0],e=this._currentLoadedIndexes,r=n("<tbody><\/tbody>"),s,l,i,u,a,h,c,t;if(this._checkCurrentVirtualView(this._virtualLoadedRows,this._currentVirtualIndex)){for(s=[],t=0;t<e.length;t++)n.merge(s,this._virtualLoadedRows[e[t]]);n(r).append(s)}else for(l=n("<tbody><\/tbody>"),t=0;t<e.length;t++)if(i=e[t],u=this._virtualLoadedRows[i],u)i<this._currentVirtualIndex?(c=r.find(".e-virtualview"+i),c.length?n(u).insertBefore(c):r.prepend(u)):n(u).insertAfter(r.find(".e-virtualview"+(i-1)));else{var l=n("<tbody><\/tbody>").append(n.render[this._id+"_JSONTemplate"](this._virtualLoadedRecords[i])),o=l[0].rows,v=o.length-1;n(o[v]).addClass("e-virtualview"+i);a=i.toString(32);h=n(o).attr("name",a);(h.length==this._virtualRowCount||i==this._totalVirtualViews)&&(this._virtualLoadedRows[i]=h,r.append(o))}if(f.replaceChild(r[0],f.lastChild),n(f.rows).removeClass("e-hover"),this._setVirtualTopBottom(),(this._isThumbScroll||this._remoteRefresh)&&(this._scrollObject._content[0].scrollTop=this._scrollObject.scrollTop(),this._isThumbScroll=this._remoteRefresh=!1),this.model.allowSelection&&this._checkVirtualSelection(),this._gridRows=f.rows,this._checkCurrentVirtualView(this._queryCellView,this._currentVirtualIndex)||this._eventBindings(),this.model.queryCellInfo||this.model.rowDataBound)for(t=0;t<this._currentLoadedIndexes.length;t++)n.inArray(this._currentLoadedIndexes[t],this._queryCellView)==-1&&this._queryCellView.push(this._currentLoadedIndexes[t])},i.prototype._setVirtualTopBottom=function(){var f=this.getContentTable()[0],c=this._vRowHeight,v=t.dataUtil.mergeSort(t.distinct(this._currentLoadedIndexes)),y=t.dataUtil.min(v),e=t.dataUtil.max(v),p=this._getVirtualTotalRecord(),a,l,w,i,u,h,o,r,s;if(this.model.scrollSettings.virtualScrollMode=="continuous"&&this._virtualLoadedRows[e+1]&&(w=Object.keys(this._virtualLoadedRows),l=parseInt(t.dataUtil.max(w),10),e=l-e),a=e*this._virtualRowCount*c,n.inArray(this._totalVirtualViews,this._currentLoadedIndexes)!=-1&&this._currentVirtualIndex!=this._totalVirtualViews&&(a=(p-(this._virtualRowCount-this._lastViewData))*c),i=p*c-a,this.model.scrollSettings.virtualScrollMode!="continuous"||this._virtualLoadedRows[e+1]||(i=l&&l<=e+1?i:1),this.getContent().find(".e-virtualtop, .e-virtualbottom").remove(),u=1e6,i>0&&this._getVirtualTotalRecord()>this._virtualRowCount*2)if(Math.round(i).toString().length<7)t.buildTag("div.e-virtualbottom","",{height:i}).insertAfter(f);else for(t.buildTag("div.e-virtualbottom").insertAfter(f),o=Math.ceil(i/u),r=0;r<o;r++)s=u,r==o-1&&(s=i%u),n(f).next().append(t.buildTag("div","",{height:s}));if(y>1)if(h=(y-1)*this._virtualRowCount*c,Math.round(h).toString().length<7)t.buildTag("div.e-virtualtop","",{height:h}).insertBefore(f);else for(t.buildTag("div.e-virtualtop").insertBefore(f),o=Math.ceil(h/u),r=0;r<o;r++)s=u,r==o-1&&(s=h%u),n(f).prev().append(t.buildTag("div","",{height:s}));this._scrollObject.model.scrollTop!=this._scrollValue&&this.getContent().ejScroller("model.scrollTop",this._scrollValue)},i.prototype._checkVirtualSelection=function(){for(var e,i,r,s,a,f,o,l,u=this.getContentTable()[0],t=0;t<this.selectedRowsIndexes.length;t++)i=this.selectedRowsIndexes[t],r=this._getSelectedViewData(i).viewIndex,n.inArray(r,this._currentLoadedIndexes)!=-1&&(e=i%this._virtualRowCount+this._currentLoadedIndexes.indexOf(r)*this._virtualRowCount,n(u.rows[e].cells).hasClass("e-selectionbackground")||(n(n(u.rows[e]).attr("aria-selected","true")[0].cells).addClass("e-selectionbackground e-active"),this.model.selectedRecords[t]=this._virtualLoadedRecords[r][e%this._virtualRowCount]));for(t=0;t<this._rowIndexesColl.length;t++)i=this._rowIndexesColl[t],r=this._getSelectedViewData(i).viewIndex,(n.inArray(r,this._currentLoadedIndexes)!=-1&&n.inArray(i,this._virtualRowCellSelIndex)==-1||this._virtualDataRefresh)&&(s=n.inArray(i,this._rowIndexesColl),a=this.selectedRowCellIndexes[s].cellIndex);for(f=n(u.rows).find(".e-active, .e-cellselectionbackground").closest("tr"),t=0;t<f.length;t++){var v=parseInt(n(f[t]).attr("name"),32)*this._virtualRowCount,y=this._virtualRowCount-n(f[t]).index()%this._virtualRowCount,h=v-y,c=n(f[t]).index();this.selectedRowsIndexes.length&&n.inArray(h,this.selectedRowsIndexes)==-1&&(this._clearVirtualSelection=!0,this.clearSelection(c));this._rowIndexesColl.length&&n.inArray(h,this._rowIndexesColl)==-1&&n(this.getRowByIndex(c)[0].cells).removeClass("e-cellselectionbackground e-activecell")}for(n(u.rows).find(".e-columnselection").removeClass("e-columnselection"),o=0;o<this.selectedColumnIndexes.length;o++)l=this.selectedColumnIndexes[o]+1,n(u.rows).find("td:nth-of-type("+l+")").addClass("e-columnselection");this._clearVirtualSelection=!1},i.prototype._calculateWidth=function(){for(var t=this.getHeaderTable().find(".e-columnheader").last().find("th:visible"),i=0,n=0;n<t.length;n++)i+=t.eq(n).outerWidth();return i},i.prototype.columns=function(i,r){var f,u,e,o,s;if(!t.isNullOrUndefined(i)){for(f=!1,typeof i=="string"?(i=[i],f=!0):i instanceof Array&&i.length&&typeof i[0]=="string"&&(f=!0),u=0;u<i.length;u++)e=n.inArray(this.getColumnByField(f?i[u]:i[u].field),this.model.columns),r=="add"||t.isNullOrUndefined(r)?e==-1?this.model.columns.push(f?{field:i[u]}:i[u]):this.model.columns[e]=f?{field:i[u]}:i[u]:e!=-1&&this.model.columns.splice(e,1);for(this.columnsWidthCollection=[],o=!1,s=0;s<this.model.columns.length;s++)this.columnsWidthCollection.push(this.model.columns[s].width),t.isNullOrUndefined(o)||(o=!0);this._enableRowHover(o);(this.model.editSettings.allowEditing||this.model.editSettings.allowAdding)&&this._processEditing();this.refreshContent(!0);this.model.allowScrolling&&(this.refreshScrollerEvent(),this.model.allowResizeToFit&&this.getContent().ejScroller("isVScroll")&&this._showHideScroller())}},i.prototype._enableRowHover=function(i,r){var f=!0,u;if(t.isNullOrUndefined(i)){for(u=0;u<this.model.columns.length;u++)if(!t.isNullOrUndefined(this.model.columns[u].tooltip)){f=!0;break}}else f=i;this.model.enableRowHover||f?r._on(this.element,"mouseenter mouseleave",".e-gridcontent tr td",n.proxy(this._rowHover,this)):r._off(this.element,"mouseenter mouseleave",".e-gridcontent tr td")},i.prototype._rowHover=function(i){var u=n(i.target),r,f;if(r=this.model.scrollSettings.frozenColumns?n(this.getRows()):this.element.find(".e-row.e-hover,.e-alt_row.e-hover"),(!u.closest("#"+this._id+"EditForm").length||!u.hasClass("e-rowcell"))&&u.hasClass("e-rowcell"))return i.type=="mouseenter"&&u.hasClass("e-gridtooltip")&&this._showTooltip(u),this.model.enableRowHover&&(i.type!="mouseenter"||this._dragActive?(this.model.scrollSettings.frozenColumns>0&&!t.isNullOrUndefined(r[0])&&!t.isNullOrUndefined(r[1])&&(r=n(r[0]).add(r[1])),r.removeClass("e-hover")):this.model.scrollSettings.frozenColumns>0&&!t.isNullOrUndefined(r[0])&&!t.isNullOrUndefined(r[1])?(r=n(r[0]).add(r[1]),r.removeClass("e-hover"),f=this.getIndexByRow(u.parent()),f!=-1&&this.getRowByIndex(f).addClass("e-hover")):(r.removeClass("e-hover"),(u.parent().hasClass("e-row")||u.parent().hasClass("e-alt_row"))&&u.parent().addClass("e-hover"))),!1},i.prototype._showTooltip=function(i,r){var e=i.index(),l=i.hasClass("e-stackedHeaderCell"),u,o,c,a,f,h,s,v;if(i.hasClass("e-headercelldiv")&&(e=i.parent(".e-headercell").index()-this.model.groupSettings.groupedColumns.length),!l&&(this.model.childGrid||this.model.detailsTemplate)&&e--,this.model.scrollSettings.frozenColumns>0&&(i.closest(".e-movableheaderdiv").length||i.closest(".e-movablecontentdiv").length)&&(e=e+this.model.scrollSettings.frozenColumns),u=l?this._getStackedColumnByTarget(i):this.getColumnByIndex(e),u.clipMode!=t.HeatMapGrid.ClipMode.Ellipsis){if(u.clipMode==t.HeatMapGrid.ClipMode.EllipsisWithTooltip&&(o=i,i.find("span").hasClass("e-tooltip")||(c=t.buildTag("span.e-tooltip",{},{}),c.html(i.html()),o.html(c)),o.find("span.e-tooltip").css("display","inline-block"),a=o.find("span:first")[0].getBoundingClientRect().width,o.find("span.e-tooltip").css("display","inline"),i.width()>a)){i.removeAttr("title");return}if(f=document.createElement("script"),t.isNullOrUndefined(u.tooltip)&&t.isNullOrUndefined(u.headerTooltip))return;f.id=(this._id+u.headerText+n.inArray(u,this.model.columns)+"_TemplateToolTip").split(" ").join("");f.type="text/x-template";s=r?"headerTooltip":"tooltip";t.isNullOrUndefined(u[s])||u[s].slice(0,1)==="#"?h=n(u[s]):f.text=u[s];h&&(f.text=h.html(),f.type=h.attr("type")||f.type);n("body").append(f);v=n(f).render({value:i.text()});i.attr("title",v)}else i.removeAttr("title")},i.prototype._colgroupRefresh=function(){var t,i,r,u;(this.model.allowResizing||this.model.allowResizeToFit)&&this.model.scrollSettings.frozenColumns>0?(t=n(this.getHeaderTable()).find("colgroup"),i=n(this.getContentTable()).find("colgroup")):(t=n(this.getHeaderTable()).find("colgroup")[0],i=n(this.getContentTable()).find("colgroup")[0]);r=n(t).clone();u=n(i).clone();n(i).remove();n(t).remove();(this.model.allowResizing||this.model.allowResizeToFit)&&this.model.scrollSettings.frozenColumns>0?(n(r[0]).prependTo(this.getHeaderTable()[0]),n(r[1]).prependTo(this.getHeaderTable()[1]),n(u[0]).prependTo(this.getContentTable()[0]),n(u[1]).prependTo(this.getContentTable()[1])):(n(r).prependTo(this.getHeaderTable()),n(u).prependTo(this.getContentTable()))},i.prototype._detailColsRefresh=function(){this._$headerCols=this.getHeaderTable().children("colgroup").find("col");this._$contentCols=this.getContentTable().children("colgroup").find("col");var n=this.model.columns.length;this._$headerCols.length>n&&this._$headerCols.splice(0,this._$headerCols.length-n);this._$contentCols.length>n&&this._$contentCols.splice(0,this._$contentCols.length-n)},i.prototype._htmlEscape=function(n){var t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&#34;","'":"&#39;"};return n.replace(/[&<>"']/g,function(n){return t[n]})},i.prototype._getForeignKeyData=function(n){for(var u,e,r=this,f={},i=0;i<this.model.columns.length;i++)this.model.columns[i].foreignKeyValue&&this.model.columns[i].dataSource&&(u=t.isNullOrUndefined(r.model.columns[i].foreignKeyField)?r.model.columns[i].field:r.model.columns[i].foreignKeyField,e=this.model.columns[i].dataSource instanceof t.DataManager?this.model.columns[i].foreignKeyData:this.model.columns[i].dataSource,e.filter(function(t){var e=n[r.model.columns[i].field],o=!isNaN(parseFloat(e))&&isFinite(e)?parseFloat(e):e;t[u]==o&&(f[u]=t)}));return f},i.prototype._foreignKeyBinding=function(i,r,u){var f,e,o=n("#"+u).ejGrid("instance"),s;return i=o.model.columns[i],s=i.dataSource instanceof t.DataManager?i.foreignKeyData:i.dataSource,s.filter(function(n){if(t.getObject(i.foreignKeyField,n)==r)return e=t.getObject(i.foreignKeyValue,n),f=i.type=="date"?new Date(e):e}),i.format&&(f=o.formatting(i.format,f,o.model.locale)),f},i.prototype._setForeignKeyData=function(){var f;if(this._relationalColumns.length){var c=this._relationalColumns,l=this._relationalColumns.length,a=[],u=this.model.currentViewData,i={},r,e,o,s,v=this,h=t.proxy(function(n){this._trigger("actionFailure",{requestType:"fetchingforeigndata",error:n.error})},this);for(f=0;f<l;f++)if(0 in u){if(r=c[f],i.field=r.field,i.keyField=r.key,i.valueField=r.value,i.dataSource=r.dataSource,i.query=(new t.Query).select([i.valueField,i.keyField]).foreignKey(i.keyField),o=t.distinct(u.level?u.records:u,i.keyField,!0),e=t.UrlAdaptor.prototype.getFiltersFrom(o,i.query),i.query.where(e),this._trigger("actionBegin",n.extend(i,{requestType:"fetchingforeigndata",column:this.getColumnByField(i.field)})))return;s=i.dataSource.ready===undefined?i.dataSource.executeQuery(i.query,null,h):i.dataSource.ready.fail(h);a.push(s)}}},i.prototype._isRelationalRendering=function(n){return 0 in this._relationalColumns&&["add","beginedit","cancel"].indexOf(n.requestType)==-1},i.prototype._columns=function(){var n=this.element.find(".e-gridheader");n.find("div").first().empty().append(this._renderGridHeader().find("table"));this._headerCellgDragDrop();this.refreshContent(!0);this._trigger("refresh")},i.prototype._summaryRows=function(n,i,r){if(i=="showTotalSummary"||i=="showCaptionSummary"){var u=n.summaryRows,f=r.toLowerCase()=="true"||r.toLowerCase()=="false"?t.parseJSON(r):!1;this.option("summaryRows")[u][i]=f}this.element.find(".e-gridfooter").remove();(i=="showCaptionSummary"||i=="title")&&(this._isCaptionSummary=this.option("summaryRows")[u].showCaptionSummary,this.model.showSummary=this._isCaptionSummary,this.model.groupSettings.groupedColumns.length!=0&&this._refreshCaptionSummary())},i.prototype._summaryRows_summaryColumns=function(n,i,r){(i=="displayColumn"||i=="dataMember")&&t.isNullOrUndefined(this.getColumnByField(r))||this.element.find(".e-groupcaptionsummary").length!=0&&this._refreshCaptionSummary()},i.prototype._stackedHeaderRows_stackedHeaderColumns=function(){this._refreshStackedHeader()},i.prototype._sortSettings_sortedColumns=function(n,t,i){var u,f,r=this.model.sortSettings.sortedColumns[n["sortSettings.sortedColumns"]];t=="field"?(u=this.getColumnByField(i)!=null?i:null,f=r.direction=="ascending"||r.direction=="descending"?r.direction:null):t=="direction"&&(u=this.getColumnByField(r.field)!=null?r.field:null,f=i=="ascending"||i=="descending"?i:null);u!=null&&f!=null&&this.sortColumn(u,f)},i.prototype._filterSettings_filteredColumns=function(n,i,r){var f,e,o,s,h,u=this.model.filterSettings.filteredColumns[n["filterSettings.filteredColumns"]];switch(i){case"field":f=this.getColumnByField(r)!=null?r:null;e=this._map(t.FilterOperators,u.operator);h=u.value;s=u.predicate=="and"||u.predicate=="or"?u.predicate:null;o=u.matchcase;break;case"matchcase":f=this.getColumnByField(u.field)!=null?u.field:null;e=this._map(t.FilterOperators,u.operator);h=u.value;s=u.predicate=="and"||u.predicate=="or"?u.predicate:null;o=r.toLowerCase()=="true"||r.toLowerCase()=="false"?t.parseJSON(r):!1;break;case"operator":f=this.getColumnByField(u.field)!=null?u.field:null;e=this._map(t.FilterOperators,r);h=u.value;s=u.predicate=="and"||u.predicate=="or"?u.predicate:null;o=u.matchcase;break;case"predicate":f=this.getColumnByField(u.field)!=null?u.field:null;e=this._map(t.FilterOperators,u.operator);h=u.value;s=r=="and"||r=="or"?r:null;o=u.matchcase;break;case"value":f=this.getColumnByField(u.field)!=null?u.field:null;e=this._map(t.FilterOperators,u.operator);h=r;s=u.predicate=="and"||u.predicate=="or"?u.predicate:null;o=u.matchcase}f!=null&&e!=null&&h!=null&&s!=null&&o!=null&&this.filterColumn(f,e,h,s,o)},i.prototype._map=function(t,i){var r=n.map(t,function(n){if(n===i)return n});return r.length!=0?r[0]:null},i.prototype._refreshCaptionSummary=function(){var t=document.createElement("div");t.innerHTML=["<table>",n.render[this._id+"_GroupingTemplate"](this.model.currentViewData,{groupedColumns:this.model.groupSettings.groupedColumns}),"<\/table>"].join("");this.getContentTable().get(0).replaceChild(t.firstChild.firstChild,this.getContentTable().get(0).lastChild);this.refreshContent()},i.prototype.getContentTable=function(){return this._gridContentTable},i.prototype.setGridContentTable=function(n){this._gridContentTable=n},i.prototype.getContent=function(){return this._gridContent},i.prototype.setGridContent=function(n){this._gridContent=n},i.prototype.getHeaderContent=function(){return this._gridHeaderContent},i.prototype.getHeaderTable=function(){return this._gridHeaderTable},i.prototype.getRows=function(){return this._gridRows},i.prototype.getFilteredRecords=function(){return this._filteredRecords},i.prototype.getRowByIndex=function(i,r){var e;try{var f=this.getRows(),o=this._excludeDetailRows(),u=n();if(Array.isArray(i)){for(e=0;e<i.length;e++)this.model.scrollSettings.frozenColumns>0?(u.push(f[0][i[e]]),u.push(f[1][i[e]])):u.push(f[i[e]]);return u}return t.isNullOrUndefined(r)?this.model.scrollSettings.frozenColumns>0?(u.push(f[0][i]),u.push(f[1][i]),u):n(o.not(".e-virtualrow")[i]):this.model.scrollSettings.frozenColumns>0?(u.push(n(f[0]).slice(i,r)),u.push(n(f[1]).slice(i,r)),u):n(o.not(".e-virtualrow").slice(i,r))}catch(s){return n()}},i.prototype.getColumnIndexByField=function(n){for(var t=0,i=this.model.columns,r=i.length;t<r;t++)if(i[t].field===n)return t;return-1},i.prototype.getColumnIndexByHeaderText=function(n,i){for(var r=0;r<this.model.columns.length;r++)if(this.model.columns[r].headerText==n)if(i){if(t.isNullOrUndefined(this.model.columns[r].field)||this.model.columns[r].field=="")break}else break;return r},i.prototype.getIndexByRow=function(t){var r=this.getRows(),u=this._excludeDetailRows(),i;return this.model.scrollSettings.frozenColumns>0?(i=n(r[0]).index(t),i==-1&&(i=n(r[1]).index(t)),i):u.not(".e-virtualrow").index(t)},i.prototype.getPrimaryKeyFieldNames=function(){if(this._primaryKeys.length!=0)return this._primaryKeys;for(var n=0,t=this.model.columns,i=t.length;n<i;n++)t[n].isPrimaryKey&&this._primaryKeys.push(t[n].field);return this._primaryKeys},i.prototype.getVisibleColumnNames=function(){return this._visibleColumns},i.prototype.getHiddenColumnNames=function(){return this._hiddenColumns},i.prototype.getColumnByField=function(n){for(var t=0;t<this.model.columns.length;t++)if(this.model.columns[t].field==n)break;return t==this.model.columns.length?null:this.model.columns[t]},i.prototype.getsortColumnByField=function(n){for(var t=0;t<this.model.sortSettings.sortedColumns.length;t++)if(this.model.sortSettings.sortedColumns[t].field==n)break;return t==this.model.sortSettings.sortedColumns.length?null:this.model.sortSettings.sortedColumns[t]},i.prototype.getColumnByHeaderText=function(n,i){for(var r=0;r<this.model.columns.length;r++)if(this.model.columns[r].headerText==n)if(i){if(t.isNullOrUndefined(this.model.columns[r].field)||this.model.columns[r].field=="")break}else break;return r==this.model.columns.length?null:this.model.columns[r]},i.prototype.getCurrentViewData=function(){return this._currentJsonData},i.prototype.getColumnFieldNames=function(){for(var t=[],n=0;n<this.model.columns.length;n++)this.model.columns[n].field&&t.push(this.model.columns[n].field);return t},i.prototype.getBrowserDetails=function(){var t=navigator.userAgent.match(/(firefox|mozilla|chrome|opera|msie|safari)\s?\/?(\d+(.\d+)*)/i);return!navigator.userAgent.match(/Trident\/7\./)?{browser:t[1].toLowerCase(),version:t[2]}:{browser:"msie",version:n.uaMatch(navigator.userAgent).version}},i.prototype._initComplexColumn=function(n,i,r){var e=r||i,u,o,f;for(u in n)typeof n[u]!="object"||t.isNullOrUndefined(n[u])?(o=e.concat(".").concat(u),f=n[u],this.model.columns.push({field:o,type:f!=null?f.getDay?f.getHours()>0||f.getMinutes()>0||f.getSeconds()>0||f.getMilliseconds()>0?"datetime":"date":typeof f:null})):(e=e.concat(".").concat(u),this._initComplexColumn(n[u],u,e))},i.prototype._initColumns=function(i){for(var f,e,r,u,o;i.items!=undefined;)i=i.items[0];if(this.model.columns.length==0&&i){for(f in i)i.hasOwnProperty(f)&&(typeof i[f]!="object"||i[f]instanceof Date||i[f]==null)?(e=i[f],this.model.columns.push({field:f,type:e!=null?e.getDay?e.getHours()>0||e.getMinutes()>0||e.getSeconds()>0||e.getMilliseconds()>0?"datetime":"date":typeof e:null})):typeof i[f]=="object"&&this._initComplexColumn(i[f],f);this.model.columns.length&&this._renderAfterColumnInitialize()}else for(r=0;r<this.model.columns.length;r++)this.model.columns[r].field=t.isNullOrUndefined(this.model.columns[r].field)?"":this.model.columns[r].field,t.isNullOrUndefined(this.model.columns[r].validationRules)||this._validatedColumns.push(this.model.columns[r].field),t.isNullOrUndefined(this.model.columns[r].type)?(u=t.isNullOrUndefined(this.model.columns[r].field)?null:t.getObject(this.model.columns[r].field,i),o=this.model.columns[r].dataSource,!!o&&this.model.columns[r].foreignKeyValue&&(this.model.columns[r].originalType=u!=null?u.getDay?u.getHours()>0||u.getMinutes()>0||u.getSeconds()>0||u.getMilliseconds()>0?"datetime":"date":typeof u:null,u=(o instanceof t.DataManager)?t.getObject("0."+this.model.columns[r].foreignKeyValue,this.model.columns[r].foreignKeyData):t.getObject("0."+this.model.columns[r].foreignKeyValue,o)),this.model.columns[r].type=u!=null?u.getDay?u.getHours()>0||u.getMinutes()>0||u.getSeconds()>0||u.getMilliseconds()>0?"datetime":"date":typeof u:null):this.model.columns[r].type!="date"||this.model.columns[r].format!=undefined||this._isReorder==!0||this.model.allowGrouping==!0||this._showHideColumns?this.model.columns[r].type!="datetime"||this.model.columns[r].format!=undefined||this._isReorder==!0||this.model.allowGrouping==!0||this._showHideColumns||(t.isNullOrUndefined(t.globalize)?n.extend(this.model.columns[r],{format:"{0:"+t.preferredCulture().calendars.standard.patterns.d+" "+t.preferredCulture().calendars.standard.patterns.t+"}"}):n.extend(this.model.columns[r],{format:"{0:M/d/yyyy h:mm tt}"})):t.isNullOrUndefined(t.globalize)?n.extend(this.model.columns[r],{format:"{0:"+t.preferredCulture().calendars.standard.patterns.d+"}"}):n.extend(this.model.columns[r],{format:"{0:M/d/yyyy}"})},i.prototype._getExpands=function(n,t){var r=n.split("."),i="",u,f;for(r.splice(r.length-1,1),u=0;u<r.length;u++,i=""){for(f=0;f<u;f++)i+=r[f]+"/";i=i+r[u];t.indexOf(i)===-1&&t.push(i)}},i.prototype._ensureDataSource=function(i){var r,k,v,y,s,h,e,u,o,d,p,g,nt,w,b,l,f,tt,c,it,a,rt;if(this._dataSource()==null&&!(this._dataSource()instanceof t.DataManager)){if(t.isNullOrUndefined(i)||i.requestType!="add")return;this.dataSource([])}if(this.model.query.requiresCount(),r=this.model.query,k=r.clone(),this._dataSource()instanceof t.DataManager||(this.model.currentViewData=this._dataSource()),this._isLocalData&&(this.model.editSettings.allowEditing||this.model.editSettings.allowAdding)&&(!t.isNullOrUndefined(this._cModifiedData)||!t.isNullOrUndefined(this._cAddedRecord))){if(t.isNullOrUndefined(this._cAddedRecord)){for(s=0;s<this._primaryKeys.length;s++)r=r.where(this._primaryKeys[s],t.FilterOperators.equal,this._primaryKeyValues[s]);v=this._dataManager.executeLocal(r);this._dataSource()instanceof t.DataManager?n.extend(this._dataSource().dataSource.json[n.inArray(v.result[0],this._dataSource().dataSource.json)],this._cModifiedData):n.extend(this._dataSource()[n.inArray(v.result[0],this._dataSource())],this._cModifiedData);this._cModifiedData=null}else y=this._cAddedRecord,this._cAddedRecord=null,this._dataSource()instanceof t.DataManager?this._dataSource().dataSource.json.unshift(y):this._dataSource(undefined,!0).splice(0,0,y);r.queries=k.queries;this.model.editSettings.showAddNewRow||(this.model.isEdit=!1)}if(i&&this.model.editSettings.allowDeleting&&i.requestType=="delete"&&(this._excludeDetailRows().length==1||this.multiDeleteMode=="multiple"&&this.selectedRowsIndexes.length==this._excludeDetailRows().length)&&this.model.pageSettings.currentPage!=1&&this._currentPage(this.model.pageSettings.totalPages-1),i&&this.model.editSettings.allowDeleting&&i.requestType=="delete"&&!t.isNullOrUndefined(this._cDeleteData)&&this._isLocalData&&(this._dataSource()instanceof t.DataManager?(s=n.inArray(this._cDeleteData[0],this._dataSource().dataSource.json),this._dataSource().dataSource.json.splice(s,1)):(s=n.inArray(this._cDeleteData[0],this._dataSource()),this._dataSource(undefined,!0).splice(s,1))),this.model.sortSettings.sortedColumns.length){for(h=[],e=this.model.sortSettings.sortedColumns,u=e.length-1;u>=0;u--)if(this.model.groupSettings.groupedColumns.indexOf(e[u].field)==-1){if(r.sortBy(e[u].field,e[u].direction),this.model.scrollSettings.allowVirtualScrolling&&this.model.scrollSettings.enableVirtualization&&n.inArray(e[u],this._prevVirtualSort)==-1){for(o=0;o<this._prevVirtualSort.length;o++)e[u].field==this._prevVirtualSort[o].field&&this._prevVirtualSort.splice(o,1);this._needVPaging=this._currentVirtualIndex*this._virtualRowCount%this.model.pageSettings.pageSize<=this._virtualRowCount;this._prevVirtualSort.push(e[u]);this._virtualDataRefresh=!0;this._refreshVirtualViewData()}}else h.push({field:e[u].field,direction:e[u].direction});for(o=0;o<h.length;o++)r.sortBy(h[o].field,h[o].direction)}if((this.model.allowPaging||this.model.scrollSettings.allowVirtualScrolling&&this.model.allowScrolling&&!this.model.scrollSettings.enableVirtualization)&&(this._isLocalData?(d=this._dataManager.executeLocal(r),this._recordsCount=d.count,p=this._recordsCount%this.model.pageSettings.pageSize==0?this._recordsCount/this.model.pageSettings.pageSize:parseInt(this._recordsCount/this.model.pageSettings.pageSize,10)+1,this._currentPage()>p&&this._currentPage(p)):t.isNullOrUndefined(i)&&this._currentPage(1),this._currentPage()==0&&(this._prevPageNo==0||this._prevPageNo==null?this._currentPage(1):this._currentPage(this._prevPageNo)),r.page(this._currentPage(),this.model.pageSettings.pageSize)),this.model.allowScrolling&&this.model.scrollSettings.allowVirtualScrolling&&this.model.scrollSettings.enableVirtualization&&(this._needPaging=!0,this.initialRender&&this.model.currentIndex>1&&(this.model.currentIndex<=this._getVirtualTotalRecord()||!this._isLocalData)&&(this.model.scrollSettings.virtualScrollMode=="continuous"&&(this.model.currentIndex=1),this._currentVirtualIndex=Math.ceil(this.model.currentIndex/this._virtualRowCount),this._isThumbScroll=!0,this._currentPage(Math.ceil(this.model.currentIndex/this.model.pageSettings.pageSize)),this._virtualLoadedPages.push(this._currentPage())),this._virtualDataRefresh&&(this._isThumbScroll=!0,this._refreshVirtualViewData(!0),this._gridRecordsCount=this._dataSource()!==null?this.model.pageSettings.totalRecordsCount==null?this._dataSource().length:this.model.pageSettings.totalRecordsCount:0,this._currentPage(Math.ceil(this._currentVirtualIndex*this._virtualRowCount/this.model.pageSettings.pageSize)),this._virtualLoadedPages.push(this._currentPage())),this.model.virtualLoading!=null&&(this._gridRecordsCount=this.model.pageSettings.totalRecordsCount),this.model.filterSettings.filteredColumns==0&&this._prevVirtualFilter.length&&(this._refreshVirtualViewData(),this._prevVirtualFilter=[]),this._isLocalData&&this.initialRender&&this._refreshVirtualViewDetails(),this._getVirtualLoadedRecords(r)),i!=undefined&&i.requestType=="add"&&this._isLocalData&&this.model.groupSettings.groupedColumns.length==0&&this.model.scrollSettings.frozenColumns==0&&this.model.scrollSettings.frozenRows==0&&((this._dataSource()instanceof t.DataManager)?this._dataSource().dataSource.json.unshift(i.data):this._dataSource().unshift(i.data)),t.isNullOrUndefined(i)||i.action!="add"||t.isNullOrUndefined(this.model.parentDetails)||(g=this.getColumnByField(this.model.parentDetails.parentKeyField),nt=n.inArray(g,this.model.columns),nt==-1&&(w={},w[this.model.parentDetails.parentKeyField]=this.model.parentDetails.parentKeyFieldValue,n.extend(!0,this.model.currentViewData[0],w))),this._cloneQuery=r.clone(),this._isLocalData&&(!this.model.scrollSettings.enableVirtualization||this._virtualDataRefresh)){if(b=this._dataManager.dataSource.json,l=this._dataSource().dataSource,!t.isNullOrUndefined(l)&&this._dataSource()instanceof t.DataManager&&(this._dataManager.dataSource.json=b!=l.json?l.json:b),f=this._dataManager.executeLocal(r),this.model.scrollSettings.allowVirtualScrolling&&this.model.pageSettings.currentPage==this.model.pageSettings.totalPages-1&&(this._prevPageRendered=!0),this.model.scrollSettings.allowVirtualScrolling&&!this._prevPageRendered&&f.result.length!=this.model.pageSettings.pageSize&&this.model.pageSettings.totalPages==this.model.pageSettings.currentPage)tt=t.pvt.filterQueries(r.queries,"onPage"),r.queries.splice(n.inArray(tt[0],r.queries),1),r.page(this._currentPage()-1,this.model.pageSettings.pageSize),c=this._dataManager.executeLocal(r),c.result.splice(0,f.result.length),this._previousPageRecords=n.extend(!0,[],c.result),this._previousPageLength=f.result.length,this._currentPageData=f.result,t.merge(c.result,f.result),this.model.currentViewData=c.result,this._lastPageRendered=!0;else if(this._lastPageRendered&&this.model.pageSettings.currentPage==this.model.pageSettings.totalPages-1&&!this.model.scrollSettings.enableVirtualization){for(it=this.model.pageSettings.pageSize-this._previousPageLength,a=0;a<it;a++)rt=this.getRows()[this.getRows().length-(this.model.pageSettings.pageSize-a)],rt.remove();this._tempPageRendered=!0;this.model.currentViewData=f.result}else this.model.currentViewData=f.result;this._gridRecordsCount=f.count;this._remoteSummaryData=f.aggregates;this._searchCount=this._searchString.length?f.count:null;this.model.groupSettings.groupedColumns.length&&this._setAggregates()}},i.prototype._refreshViewPageDetails=function(){this._currentPage(1);this.model.currentIndex=0;this._currentVirtualIndex=1;this.getContent().ejScroller("model.scrollTop",0)},i.prototype._refreshVirtualViewDetails=function(n){n&&(this._gridRecordsCount=this._dataSource()!==null?this._dataSource().length:this.model.pageSettings.totalRecordsCount);this._totalVirtualViews=Math.ceil(this._getVirtualTotalRecord()/this._virtualRowCount);this._maxViews=Math.ceil(this.model.pageSettings.pageSize/this._virtualRowCount);this.model.pageSettings.totalPages=Math.ceil(this._gridRecordsCount/this.model.pageSettings.pageSize);this.model.pageSettings.totalRecordsCount=this._gridRecordsCount;this._lastViewData=this._virtualRowCount-(this._totalVirtualViews*this._virtualRowCount-this._getVirtualTotalRecord())},i.prototype._getVirtualLoadedRecords=function(t){var i=this._currentPage(),r,u,f;if(this._needPaging&&(this._isLastVirtualpage=r=this._isThumbScroll&&i==this.model.pageSettings.totalPages&&!this._virtualPageRecords[i],(this.initialRender||this._virtualDataRefresh)&&(r=!0),this.model.virtualLoading&&this._isLocalData&&(this.model.currentIndex!=0||i!=1)&&this.model.currentIndex<this.model.pageSettings.totalRecordsCount?this._getVirtualOnLoadingData(i,!r):this._setVirtualPaging(t,i,!r),!this.initialRender&&this._isThumbScroll&&this._virtualPageRecords[i]&&!this._virtualDataRefresh&&this._checkPrevNextViews(i,t)),this._needPaging=!1,this._setVirtualLoadedIndexes(this._currentVirtualIndex),this.initialRender&&this._isLocalData)for(this.model.currentViewData=[],u=0;u<this._currentLoadedIndexes.length;u++)f=this._currentLoadedIndexes[u],this._virtualLoadedRecords[f]&&n.merge(this.model.currentViewData,this._virtualLoadedRecords[f])},i.prototype._setVirtualPaging=function(i,r,u){var e=t.pvt.filterQueries(i.queries,"onPage"),f;e.length&&i.queries.splice(n.inArray(e[0],i.queries),1);(!u||this._needVPaging)&&this.model.currentIndex>this._virtualRowCount?(this._initCurrentIndex(i,r),this._needVPaging=!1):i.page(r,this.model.pageSettings.pageSize);this._isLocalData&&!this._virtualPageRecords[r]&&(f=this._dataManager.executeLocal(i),this.initialRender||(this.model.currentViewData=f.result),f.result.length?(this._setVirtualLoadedRecords(f.result,r),n.inArray(r,this._virtualLoadedPages)==-1&&this._virtualLoadedPages.push(r)):this.getContent().find(".e-virtualtop, .e-virtualbottom").remove())},i.prototype._checkPrevNextViews=function(){var n=this._currentVirtualIndex,u=this._virtualLoadedRecords[n-1],f=this._virtualLoadedRecords[n+1],r=this._maxViews==3?1:2,t,i;n!=1&&n!=this._totalVirtualViews&&(u&&u.length==this._virtualRowCount?f&&f.length==this._virtualRowCount||this._totalVirtualViews==n-1||(i=n-r,this._currentVirtualIndex=this._virtualLoadedRecords[n]?i:i-1,t=this._scrollValue-r*this._virtualRowCount*this._vRowHeight):(i=n+r,this._currentVirtualIndex=this._virtualLoadedRecords[n]?i:i+1,t=this._scrollValue+r*this._virtualRowCount*this._vRowHeight),t&&(this._scrollValue=t,this._setVirtualLoadedIndexes(this._currentVirtualIndex),this.model.currentIndex=t==0?t:Math.floor(t/this._vRowHeight)))},i.prototype._initCurrentIndex=function(n,t){var u=t*this.model.pageSettings.pageSize,o=this._currentVirtualIndex*this._virtualRowCount+this._virtualRowCount,s=this._currentVirtualIndex*this._virtualRowCount-this._virtualRowCount*2,c=o>u||s<u-this.model.pageSettings.pageSize,r,f,i,h,e;c||this._isLastVirtualpage?(o>u?(r=(t-1)*this.model.pageSettings.pageSize,f=this.model.pageSettings.pageSize*2,this._isInitNextPage=!0):(s<u-this.model.pageSettings.pageSize||this._isLastVirtualpage)&&(r=(t-2)*this.model.pageSettings.pageSize,f=this.model.pageSettings.pageSize*2,this._isInitNextPage=!1,this._remoteRefresh=!0),this.model.virtualLoading&&this._isLocalData?(i={},i.endIndex=r+f,i.endIndex=i.endIndex>this._getVirtualTotalRecord()?this._getVirtualTotalRecord():i.endIndex,i.startIndex=r,i.currentPage=this._currentPage(),i.result=null,this._trigger("virtualLoading",i),e=i.result,this._setInitialCurrentIndexRecords(e,t)):(n.skip(r).take(f),this._isLocalData&&(h=this._dataManager.executeLocal(n),e=h.result,this._isLastVirtualpage=!1,this._setInitialCurrentIndexRecords(e,t)))):(this._needVPaging=!1,this.model.virtualLoading&&this._isLocalData&&(this.model.currentIndex!=0||t!=1)?this._getVirtualOnLoadingData(t,!0):this._setVirtualPaging(n,t,!0))},i.prototype._setInitialCurrentIndexRecords=function(n,t){for(var i=0;i<2;i++){var r=i*this.model.pageSettings.pageSize,f=r+this.model.pageSettings.pageSize,e=n.slice(r,f),u;u=this._isInitNextPage?i==0?t:t+1:i==0?t-1:t;this._setVirtualLoadedRecords(e,u)}},i.prototype._getVirtualOnLoadingData=function(n,t){var i,r;n>0&&(this.model.currentIndex>this._virtualRowCount&&(!t||this._needVPaging)&&this.model.currentIndex<this.model.pageSettings.totalRecordsCount?this._initCurrentIndex(undefined,n):(i={},i.endIndex=n*this.model.pageSettings.pageSize>this._gridRecordsCount?this._gridRecordsCount:n*this.model.pageSettings.pageSize,i.startIndex=n*this.model.pageSettings.pageSize-this.model.pageSettings.pageSize,i.currentPage=this._currentPage(),i.result=null,this._trigger("virtualLoading",i),r=i.result,this._setVirtualLoadedRecords(r,n)))},i.prototype._setVirtualLoadedRecords=function(t,i){var u=this._virtualRowCount,h=this.model.pageSettings.pageSize,v=h/u,o,y=Math.ceil(i*h/u),p=i==this.model.pageSettings.totalPages,l,e,f,s,r,w,c,a,b;for(this._virtualPageRecords[i]||(this._virtualPageRecords[i]=t),p&&(l=this._getVirtualTotalRecord()%h,y=(!this._virtualLoadedRecords[this._totalVirtualViews]||this._virtualLoadedRecords[this._totalVirtualViews].length!=this._lastViewData)&&l<this._lastViewData&&l!=0?this._totalVirtualViews+1:this._totalVirtualViews,this._getVirtualTotalRecord()<u&&(this._singleView=!0)),e=0;e<v;e++)r=Math.ceil((i-1)*v+(e+1)),(r<=this._totalVirtualViews||p)&&r<=y&&(this._virtualLoadedRecords[r-1]&&this._virtualLoadedRecords[r-1].length!=u?(w=this._virtualLoadedRecords[r-1].length+e*u,f=u-w+e*u,n.merge(this._virtualLoadedRecords[r-1],t.slice(0,f)),o=s=f+u,r<=this._totalVirtualViews&&(this._virtualLoadedRecords[r]=t.slice(f,o))):r==1||this._virtualLoadedRecords[r-1]?(f=o?o:e*u%h,o=s=f+u):(c=s=(r-1)*u%h,c!=0&&(this._virtualLoadedRecords[r-1]=t.slice(0,c)),f=c,s=o=c+u),this._virtualLoadedRecords[r]&&this._virtualLoadedRecords[r].length!=u?(a=t.slice(f,s),a.length+this._virtualLoadedRecords[r].length<=u&&(b=n.merge(a,this._virtualLoadedRecords[r]),this._virtualLoadedRecords[r]=b)):!this._virtualLoadedRecords[r]&&r<=this._totalVirtualViews&&(this._virtualLoadedRecords[r]=t.slice(f,s)));n.inArray(i,this._virtualLoadedPages)==-1&&this._virtualLoadedPages.push(i)},i.prototype._setVirtualLoadedIndexes=function(n){var i,t;for(this._currentLoadedIndexes=[],i=n==this._totalVirtualViews?n:n+1,n!=1&&(n=n-1),t=n;t<=i;t++)this._currentLoadedIndexes.push(t)},i.prototype._getVirtualTotalRecord=function(){return this.model.filterSettings.filteredColumns.length==0?this._searchCount==null?this._gridRecordsCount:this._searchCount:this._filteredRecordsCount},i.prototype._initGridRender=function(t){var r,i;this.addInitTemplate();this.model.scrollSettings.frozenColumns>0&&this.addFrozenTemplate();this.model.allowGrouping&&this.addGroupingTemplate();this.model.showSummary&&this.addSummaryTemplate();this.model.keySettings&&n.extend(this.model.keyConfigs,this.model.keySettings);this.render();this._setTextWrap();this._wireEvents(t);this.initialRender=!1;this.model.width&&!this.model.allowScrolling&&this.element.width(this.model.width);(this.model.editSettings.allowEditing||this.model.editSettings.allowAdding)&&this._processEditing();this.model.parentDetails&&(r=this.model.parentDetails.parentID,i=n("#"+r).data("ejGrid"),i.model.allowScrolling&&i._refreshScroller({requestType:"refresh"}));this.element.closest("tr").hasClass("e-detailrow")&&!this.model.parentDetails&&(i=this.element.closest("tr.e-detailrow").closest(".e-grid").data("ejGrid"),i.model.allowScrolling&&i.getScrollObject().refresh())},i.prototype._setTextWrap=function(){if(this.model.allowTextWrap==!0)switch(this.model.textWrapSettings.wrapMode){case"content":this.element.find(".e-columnheader").removeClass("e-wrap");this.element.removeClass("e-wrap");this.getContent().addClass("e-wrap");break;case"header":this.element.removeClass("e-wrap");this.getContent().removeClass("e-wrap");this.element.find(".e-columnheader").addClass("e-wrap");break;default:this.getContent().removeClass("e-wrap");this.element.find(".e-columnheader").removeClass("e-wrap");this.element.addClass("e-wrap")}else this.getContent().removeClass("e-wrap"),this.element.find(".e-columnheader").removeClass("e-wrap"),this.element.removeClass("e-wrap")},i.prototype._getMetaColGroup=function(){for(var r,u=t.buildTag("colgroup"),i=0;i<this.model.columns.length;i++)r=n(document.createElement("col")),this.model.columns[i].visible===!1&&r.css("display","none"),this.model.rowTemplate==null||t.isNullOrUndefined(this.model.columns[i].cssClass)||r.addClass(this.model.columns[i].cssClass),this.model.allowGrouping&&!this.model.groupSettings.showGroupedColumn&&n.inArray(this.model.columns[i].field,this.model.groupSettings.groupedColumns)!=-1&&r.css("display","none"),u.append(r);return u},i.prototype._alternateRow=function(){return this.getIndex()%2==0?"e-row":"e-alt_row"},i.prototype.formatting=function(i,r,u){var f,l,h,c,a,v,s,e,y;if(i=i.replace(/%280/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">"),u=t.preferredCulture(u)?u:"en-US",f=i,l=i.split("{0:"),a=i.split("}"),h=l[0],c=a[1],typeof r=="string"&&n.isNumeric(r)&&(r=Number(r)),i.indexOf("{0:")!=-1)return v=new RegExp("\\{0(:([^\\}]+))?\\}","gm"),s=v.exec(i),s!=null&&r!=null?h!=null&&c!=null?h+t.format(r,s[2],u)+c:t.format(r,s[2],u):r!=null?r:"";if(f.startsWith("{")&&!f.startsWith("{0:")){var o=f.split(""),r=(r||"")+"",p=r.split(""),w=/[0aA\*CN<>\?]/gm;for(e=0,y=0;e<o.length;e++)o[e]=w.test(o[e])?"{"+y+++"}":o[e];return String.format.apply(String,[o.join("")].concat(p)).replace("{","").replace("}","")}return this.data!=null&&this.data.Value==null?(n.each(this.data,function(n,t){f=f.replace(new RegExp("\\{"+n+"\\}","gm"),t)}),f):this.data.Value},i.prototype.addInitTemplate=function(){var p=this.element.find(".e-headercelldiv:not(.e-emptyCell)"),l={},w=!0,a=document.createElement("tbody"),v=n(a),c,b,u,tt,o,k,g,f,nt;if(this.model.rowTemplate==null){var y=document.createElement("tr"),e=n(y),r=this.model.columns,i;for(this._gridRecordsCount&&!this._virtualDataRefresh?this._initColumns(this.model.currentViewData[0]!=undefined?this.model.currentViewData[0]:this.model.currentViewData.value):this._isLocalData&&(this._dataSource()!=null&&this._dataSource().length||this._dataManager&&this._dataManager.dataSource.json.length)&&this._initColumns(this._dataSource()[0]!=undefined?this._dataSource()[0]:this._dataManager.dataSource.json[0]),c={_gridFormatting:this.formatting},n.views.helpers(c),f={},f._foreignKey=this._foreignKeyBinding,n.views.helpers(f),(this.model.childGrid||this.model.detailsTemplate)&&(b=t.buildTag("td.e-detailrowcollapse","<div class='e-icon e-gnextforward'><\/div>"),e.append(b)),i=0;i<this.model.columns.length;i++){if(u=t.buildTag("td.e-rowcell"),t.isNullOrUndefined(r[i].tooltip)&&r[i].clipMode!=t.HeatMapGrid.ClipMode.EllipsisWithTooltip||u.addClass("e-gridtooltip"),(r[i].clipMode==t.HeatMapGrid.ClipMode.Ellipsis||r[i].clipMode==t.HeatMapGrid.ClipMode.EllipsisWithTooltip)&&u.addClass("e-gridellipsis"),this.model.isResponsive&&u.attr("data-cell",this._decode(this.model.columns[i].headerText)),r[i].visible==!1?u.addClass("e-hide"):(w&&(this.model.detailsTemplate!=null||this.model.childGrid!=null)&&u.addClass("e-detailrowvisible"),w=!1),this.model.groupSettings.showGroupedColumn||u.addClass("{{for ~groupedColumns}} {{if #data == '"+this.model.columns[i].field+"'}}e-hide{{/if}}{{/for}}"),t.isNullOrUndefined(r[i].templateID||r[i].template)){for(var s=(r[i].field||"").split("."),d=s.length-1,h="";d;)h+="(",d--;g=r[i].type||r[i].editType;switch(g){default:r[i].disableHtmlEncode?u.html("{{html:"+h+"#data['"+s.join("'] || {})['")+"']}}"):u.html("{{:"+h+"#data['"+s.join("'] || {})['")+"']}}")}r[i].format==undefined||r[i].foreignKeyValue||u.html("{{:~_gridFormatting('"+r[i].format+"',"+h+"#data['"+s.join("'] || {})['")+"'],'"+this.model.locale+"')}}");r[i].foreignKeyValue&&r[i].dataSource&&u.html("{{:~_foreignKey("+i+","+h+"#data['"+s.join("'] || {})['")+"'],'"+this._id+"')}}");r[i].commands&&(f={},n.views.helpers(f),(t.isNullOrUndefined(r[i].field)||r[i].field=="")&&(this.model.columns[i].allowGrouping=this.model.columns[i].allowFiltering=this.model.columns[i].allowSorting=!1),t.isNullOrUndefined(r[i].headerText)||n("#"+this._id+r[i].headerText.replace(/[^a-z0-9|s_]/gi,"")+"_UnboundTemplate").remove(),nt=this._createUnboundElement(r[i]),t.isNullOrUndefined(r[i].headerText)||u.addClass("e-unboundcell").addClass("e-"+r[i].headerText.replace(/[^a-z0-9|s_]/gi,"")+i).html("{{:~_"+this._id+"UnboundTemplate('"+nt.id+"')}}"),this.model.scrollSettings.frozenColumns>0&&u.addClass("e-frozenunbound"),this._isUnboundColumn=!0)}else f={},o=r[i].headerText,f["_"+this._id+"ColumnTemplating"]=t.proxy(this._gridTemplate,null,this,tt),n.views.helpers(f),t.isNullOrUndefined(o)||t.isNullOrUndefined(o.match(/[^0-9\s\w]/g))||(o=o.replace(/[^0-9\s\w]/g,"_")),n("#"+this._id+o+i+"_Template").remove(),k=this._createTemplateElement(r[i]),(r[i].field==""||t.isNullOrUndefined(r[i].field))&&(this.model.columns[i].allowGrouping=this.model.columns[i].allowFiltering=this.model.columns[i].allowSorting=!1),r[i].template!=!1&&u.addClass("e-templatecell").html("{{:~_"+this._id+"ColumnTemplating('"+k.id+"','"+i+"')}}");r[i].textAlign==undefined&&(r[i].textAlign="left");r[i].isPrimaryKey===!0&&(this._primaryKeys.push(n.trim(r[i].field)),this._primaryKeys=n.unique(this._primaryKeys));this.phoneMode&&this.model.enableResponsiveRow||r[i].textAlign==undefined||(u.css("text-align",r[i].textAlign),n(p[i]).css("text-align",r[i].textAlign));this.phoneMode||t.isNullOrUndefined(r[i].headerTextAlign)||n(p[i]).css("text-align",r[i].headerTextAlign);t.isNullOrUndefined(r[i].cssClass)||u.addClass(r[i].cssClass);t.isNullOrUndefined(r[i].priority)||u.addClass("e-table-priority-"+r[i].priority);t.isNullOrUndefined(r[i].customAttributes)||u.attr(r[i].customAttributes);u.addClass("sf-ht-heatmapcell");u.attr("data-role","heatmapcell");e.append(u);this.model.enableAltRow?(c["_"+this._id+"AlternateRow"]=this._alternateRow,n.views.helpers(c),e.addClass("{{:~_"+this._id+"AlternateRow()}}")):e.addClass("e-row");e.attr("data-role","row");this.model.scrollSettings.frozenColumns>0&&this.model.scrollSettings.frozenColumns==i+1&&(a.appendChild(y),l[this._id+"_JSONFrozenTemplate"]=v.html(),e.empty(),v.empty())}a.appendChild(y)}l[this._id+"_JSONTemplate"]=this.model.rowTemplate!=null?n(this.model.rowTemplate).html():v.html();n.templates(l)},i.prototype.render=function(){this.model.showSummary=this.model.summaryRows.length>0||this.model.showSummary;this._renderGridContent().insertAfter(this.element.children(".e-gridheader"));this.model.allowResizeToFit&&this.setWidthToColumns();this._initialEndRendering()},i.prototype._createStackedRow=function(i,r){for(var v,ft,et,u,y,p,w,f,e,st,k,a=t.buildTag("tr.e-columnheader e-stackedHeaderRow"),l=[],d=[],g=[],nt=[],o=0;o<this.model.columns.length;o++)if(v=this.model.columns[o],v.visible!=!1){if(this.model.allowGrouping&&!this.model.groupSettings.showGroupedColumn&&this.model.groupSettings.groupedColumns.length>0&&n.inArray(v.field,this.model.groupSettings.groupedColumns)!=-1)continue;var tt="",it="",rt="",ut="",h=i.stackedHeaderColumns;for(e=0;e<h.length;e++)ft=Array.isArray(h[e].column)?h[e].column:n.map(h[e].column.split(","),n.trim),et="e-row"+n.inArray(i,this.model.stackedHeaderRows)+"-column"+e,n.inArray(v.field,ft)!=-1&&(tt=h[e].headerText,it=h[e].cssClass,rt=h[e].textAlign,ut=h[e].tooltip?" e-gridtooltip "+et:"");l.push(tt);d.push(it);g.push(rt);nt.push(ut)}for(u=[],f=0;f<l.length;f++){for(y=1,p=f+1;p<l.length;p++)if(l[f]==l[p])y++;else break;u.push({sapnCount:y,headerText:l[f],cssClass:d[f],txtAlign:g[f],tooltip:nt[f]});f+=y-1}if(a=t.buildTag("tr.e-columnheader e-stackedHeaderRow"),w=this.model.scrollSettings.frozenColumns,this.model.allowScrolling&&w>0){var b=[],c=0,s=0,ot=0;for(f=0;f<this.model.columns.length;f++)e=this.model.columns[f],f<w&&e.visible==!1&&ot++;for(c=w-ot;c>0;)st=u[s].sapnCount,u[s].sapnCount<c?(b.push(u[s]),r?s++:u.splice(s,1)):u[s].sapnCount>c?(u[s].sapnCount=u[s].sapnCount-c,r&&b.push({sapnCount:c,headerText:u[s].headerText})):(b.push(u[s]),r||u.splice(s,1)),c-=st;r&&(u=b)}for((this.model.detailsTemplate||this.model.childGrid)&&a.append(t.buildTag("th.e-headercell e-detailheadercell","<div><\/div>")),o=0;o<u.length;o++)k=t.buildTag("th.e-headercell e-stackedHeaderCell e-default"+u[o].tooltip,u[o].headerText,{},{colspan:u[o].sapnCount}),k.css("textAlign",u[o].txtAlign),a.append(k),u[o].cssClass!=undefined&&k.addClass(u[o].cssClass);return a},i.prototype._renderGridContent=function(){var o=t.buildTag("div.e-gridcontent"),h=t.buildTag("div"),f=t.buildTag("table.e-table",""),c=n(document.createElement("tbody")),r,i,e,l,u,s,a;if(f.append(this.getHeaderTable().find("colgroup").clone()).append(c),h.html(f),o.html(h),this.setGridContentTable(f),this.setGridContent(o),f.attr("data-role","heatmap"),r={},this.model.allowGrouping&&this.model.groupSettings.groupedColumns.length){if(this.initialRender&&(r.columnName=this.model.groupSettings.groupedColumns[this.model.groupSettings.groupedColumns.length-1],!this.model.groupSettings.showGroupedColumn))for(i=0;i<this.model.groupSettings.groupedColumns.length;i++)e=this.model.groupSettings.groupedColumns[i],n.inArray(e,this._hiddenColumnsField)==-1&&(this._hiddenColumnsField.push(e),this.getColumnByField(e).visible=!1);r.requestType=t.HeatMapGrid.Actions.Grouping}else r.requestType=t.HeatMapGrid.Actions.Refresh;if(this._dataSource()==null||this._dataSource().length==0||this.model.currentViewData.length==0?(l=t.buildTag("td.emptyrecord",this.localizedLabels.EmptyRecord,{},{colSpan:this.model.columns.length}),c.append(n(document.createElement("tr")).append(l)),this.setWidthToColumns(),this.initialRender&&this.sendDataRenderingRequest(r)):this.sendDataRenderingRequest(r),this._isCaptionSummary&&r.requestType=="grouping"&&this.model.groupSettings.groupedColumns.length>1)for(u=this.getContentTable().find(".e-table").not(".e-recordtable").children("colgroup"),s=n(this.getContentTable().find(".e-recordtable")[0]).children("colgroup").find("col"),i=0;i<u.length;i++)a=n(u[i]).find("col").length,n(u[i]).find("col:gt("+(a-s.length-1)+")").remove(),n(u[i]).append(s.clone());return o},i.prototype.sendDataRenderingRequest=function(i){var v,rt,ut,s,ft,et,tt,a,k,ot,c,y,o,d,it,st,g,ht;if(this._templateRefresh&&(this.refreshTemplate(),this._templateRefresh=!1),this.setFormat(),this.model.scrollSettings.enableVirtualization||(this._previousColumnIndex=null,this._previousRowCellIndex=null,this._previousIndex=null),i.requestType=="add"||i.requestType=="grouping"||this.model.currentViewData!=null&&this.model.currentViewData.length)switch(i.requestType){case t.HeatMapGrid.Actions.Refresh:case t.HeatMapGrid.Actions.Paging:case t.HeatMapGrid.Actions.Sorting:case t.HeatMapGrid.Actions.Filtering:case t.HeatMapGrid.Actions.Save:case t.HeatMapGrid.Actions.Cancel:case t.HeatMapGrid.Actions.Delete:case t.HeatMapGrid.Actions.Search:case t.HeatMapGrid.Actions.Reorder:case t.HeatMapGrid.Actions.BatchSave:if(v=this.model.groupSettings.groupedColumns,this.model.allowGrouping&&i.requestType==t.HeatMapGrid.Actions.Refresh&&v.length==0&&this.element.find(".e-grouptopleftcell").length>0&&(rt=this.element.children(".e-gridheader"),rt.find("div").first().empty().append(this._renderGridHeader().find("table"))),this.model.allowGrouping||(v=[]),(this.model.editSettings.editMode=="externalform"||this.model.editSettings.editMode=="externalformtemplate")&&n("#"+this._id+"_externalEdit").css("display","none"),v.length==0){var u=document.createElement("div"),p,h=-1,nt=!1;if(this.phoneMode||this.getContentTable().find("colgroup").first().replaceWith(this._getMetaColGroup()),(this.model.childGrid!=null||this.model.detailsTemplate!=null)&&this.getContentTable().find("colgroup").first().prepend(this._getIndentCol()),ut=this._currentPage(),(this.model.editSettings.editMode=="inlineform"||this.model.editSettings.editMode=="inlineformtemplate"||this.model.editSettings.editMode=="normal")&&(i.requestType=="cancel"||i.requestType=="save")&&(this._editFormHeight=this.element.find(".gridform").closest("tr").height()),this.model.scrollSettings.frozenColumns>0)u.innerHTML=this._renderByFrozenDesign();else{i.data&&(p=document.createElement("div"),p.innerHTML=["<table>",n.render[this._id+"_JSONTemplate"](i.data),"<\/table>"].join(""),this._dataSource()instanceof t.DataManager&&i.requestType==t.HeatMapGrid.Actions.Save&&(h=this._getDataIndex(this.model.currentViewData,i.data),nt=this._dataSource().adaptor instanceof t.remoteSaveAdaptor));u.innerHTML=["<table>",n.render[this._id+"_JSONTemplate"](this.model.currentViewData),"<\/table>"].join("");var w=this.getContentTable().get(0),r=w.lastChild,ct=this.getContentTable().first().find("tbody").first();if((i.requestType=="save"||i.requestType=="cancel")&&this.model.editSettings.editMode!="batch"){if(s=this.model.editSettings.editMode.indexOf("inlineform")!=-1?t.isNullOrUndefined(i.selectedRow)?this._selectedRow():i.selectedRow:this.getContentTable().find(".e-"+i.action+"edrow").index(),ft=this._currentTrIndex,s==-1&&(s=ft),this.model.detailsTemplate!=null||this.model.childGrid!=null)for(et=this.model.editSettings.editMode=="inlineform"?n(n(r.childNodes).not(".e-detailrow")[s]):n(r.childNodes[s]),tt=n(r.childNodes).not(".e-detailrow"),c=0;c<tt.length;c++)et.is(tt[c])&&(s=c);if(this.model.editSettings.editMode=="normal"||this.model.editSettings.editMode=="inlineform"||this.model.editSettings.editMode=="inlineformtemplate"){if(i.action=="add"&&!this.getContentTable().find(".e-addedrow").length)break;var f=this.getContentTable().find(".e-addedrow").get(0),l=this.getContentTable().find(".e-editedrow"),e=l.length||i.requestType=="cancel"?u.firstChild.firstChild.firstChild:p.firstChild.firstChild.lastChild;l.length?(this.model.editSettings.showAddNewRow&&this.model.editSettings.rowPosition=="top"&&(s=s-1),e=u.firstChild.firstChild.childNodes[s],this.model.editSettings.editMode=="inlineform"||this.model.editSettings.editMode=="inlineformtemplate"?(f=l.prev("tr").get(0),l.remove()):f=l.get(0),a=n(e),o=n(f),(this.model.detailsTemplate!=null||this.model.childGrid!=null)&&o.next("tr.e-detailrow:visible").length&&(y=a.find(".e-detailrowcollapse"),y.removeClass("e-detailrowcollapse").addClass("e-detailrowexpand").find("div").removeClass("e-gnextforward").addClass("e-gdiagonalnext")),i.requestType=="cancel"?(this.model.editSettings.showAddNewRow&&this.getContentTable().find(".e-addedrow").addClass("e-showaddrow"),o.replaceWith(a)):!t.isNullOrUndefined(this._filteredRecordsCount)&&this._filteredRecordsCount<this._previousFilterCount?(this.model.detailsTemplate!=null&&o.next("tr.e-detailrow").length&&r.removeChild(o.next("tr.e-detailrow").get(0)),o.remove(),this.model.allowPaging&&this.model.pageSettings.pageSize<=this.model.currentViewData.length&&v.length==0&&r.appendChild(u.firstChild.firstChild.lastChild)):this.model.sortSettings.sortedColumns.length&&i.requestType=="save"&&this._currentJsonData.length>0||!t.isNullOrUndefined(this._searchCount)?this.getContentTable().get(0).replaceChild(this.model.rowTemplate!=null?u.firstChild.lastChild:u.firstChild.firstChild,this.getContentTable().get(0).lastChild):r.replaceChild(e,f)):(a=n(e),o=n(f),i.action=="add"&&i.requestType=="save"&&this.model.editSettings.showAddNewRow&&this.model.allowPaging&&this.model.pageSettings.pageSize<=this._currentJsonData.length&&(this.model.editSettings.rowPosition=="bottom"?r.lastChild.previousSibling.remove():r.lastChild.remove()),i.requestType=="cancel"||this._dataSource()instanceof t.DataManager||this._currentPage()!=1||i.requestType=="save"&&!t.isNullOrUndefined(this._filteredRecordsCount)&&this._filteredRecordsCount==this._previousFilterCount?(t.isNullOrUndefined(f)||(o.remove(),this._dataSource()instanceof t.DataManager&&h!=-1?h==0?r.insertBefore(e,r.children[h]):a.insertAfter(r.children[h-1]):this._dataSource()instanceof t.DataManager&&!nt||this._currentPage()==1||i.requestType!="save"||n(r).prepend(n(u.firstChild.firstChild.firstChild)),this.model.allowPaging&&this.model.pageSettings.pageSize<=this.model.currentViewData.length&&v.length==0&&(this._dataSource()instanceof t.DataManager&&h==-1&&!nt&&i.requestType!="save"||i.requestType!="save"&&!(this._dataSource()instanceof t.DataManager))&&i.requestType=="cancel"&&!this.model.editSettings.showAddNewRow&&w.lastChild.appendChild(u.firstChild.firstChild.lastChild)),i.requestType=="cancel"&&this._selectedRow()!=-1&&this.clearSelection()):this.model.currentViewData.length==1?(n(r).empty(),r.appendChild(e)):this.model.sortSettings.sortedColumns.length&&i.requestType=="save"&&this._currentJsonData.length>0||!t.isNullOrUndefined(this._searchCount)?this.getContentTable().get(0).replaceChild(this.model.rowTemplate!=null?u.firstChild.lastChild:u.firstChild.firstChild,this.getContentTable().get(0).lastChild):this.model.editSettings.rowPosition=="bottom"?(ct.prepend(f),r.replaceChild(e,f)):r.replaceChild(e,f))}else if(this.model.editSettings.editMode=="dialog"||this.model.editSettings.editMode=="dialogtemplate"||this.model.editSettings.editMode=="externalform"||this.model.editSettings.editMode=="externalformtemplate"){if(i.action=="add"&&!this.element.find(".e-addedrow").length)break;if(l=this.element.find(".e-editedrow"),i.requestType!="cancel"&&(l.length||t.isNullOrUndefined(this._filteredRecordsCount)||this._filteredRecordsCount!=this._previousFilterCount))if(l.length){if(e=u.firstChild.firstChild.childNodes[s],f=this._excludeDetailRows(r.childNodes)[s],this.model.allowCellMerging!=null&&(o=n(f),n(f.childNodes).hasClass("e-merged")))for(k=o.children(".e-merged").index(),ot=f.children[k].colSpan,c=0;c<ot;c++)e.childNodes[k+c].className+=" e-merged e-hide",e.childNodes[k].colSpan=c+1;this.model.detailsTemplate!=null&&(f=n(r.childNodes).not(".e-detailrow").eq(s).get(0));(this.model.detailsTemplate!=null||this.model.childGrid!=null)&&o.next("tr.e-detailrow:visible").length&&(y=n(e).find(".e-detailrowcollapse"),y.removeClass("e-detailrowcollapse").addClass("e-detailrowexpand").find("div").removeClass("e-gnextforward").addClass("e-gdiagonalnext"));!t.isNullOrUndefined(this._filteredRecordsCount)&&this._filteredRecordsCount<this._previousFilterCount?(o=n(f),this.model.detailsTemplate!=null&&o.next("tr.e-detailrow").length&&r.removeChild(o.next("tr.e-detailrow").get(0)),o.remove(),this.model.allowPaging&&this.model.pageSettings.pageSize<=this.model.currentViewData.length&&r.appendChild(u.firstChild.firstChild.lastChild)):this.model.sortSettings.sortedColumns.length&&i.requestType=="save"&&this._currentJsonData.length>0||!t.isNullOrUndefined(this._searchCount)?this.getContentTable().get(0).replaceChild(this.model.rowTemplate!=null?u.firstChild.lastChild:u.firstChild.firstChild,this.getContentTable().get(0).lastChild):r.replaceChild(e,f)}else this.model.currentViewData.length==1&&this.getContentTable().find("td.e-rowcell").length==0?(e=u.firstChild.firstChild.firstChild,n(r).empty(),r.appendChild(e)):(d=l.length||i.requestType=="cancel"?u.firstChild.firstChild.firstChild:p.firstChild.firstChild.lastChild,this._dataSource()instanceof t.DataManager?h!=-1&&(h==0?r.insertBefore(d,r.children[h]):n(d).insertAfter(r.children[h-1]),this.model.allowPaging&&(this.model.pageSettings.pageSize<=this.model.currentViewData.length||h==this.model.pageSettings.pageSize-1)&&r.removeChild(r.lastChild)):(this.model.sortSettings.sortedColumns.length&&i.requestType=="save"&&this._currentJsonData.length>0||!t.isNullOrUndefined(this._searchCount)?this.getContentTable().get(0).replaceChild(this.model.rowTemplate!=null?u.firstChild.lastChild:u.firstChild.firstChild,this.getContentTable().get(0).lastChild):this._currentPage()==1?this.getContentTable().find("tbody").first().prepend(n(d)):this.getContentTable().find("tbody").first().prepend(n(u.firstChild.firstChild.firstChild)),this.model.allowPaging&&this.model.pageSettings.pageSize<=this.model.currentViewData.length&&r.removeChild(r.lastChild)),this.model.detailsTemplate!=null&&n(w.lastChild.lastChild).children(".e-detailrowexpand").length&&r.removeChild(r.lastChild));else e=u.firstChild.firstChild.childNodes[s],f=r.childNodes[s],a=n(e),o=n(f),(this.model.detailsTemplate!=null||this.model.childGrid!=null)&&o.next(".e-detailrow:visible").length&&(y=a.find(".e-detailrowcollapse"),y.removeClass("e-detailrowcollapse").addClass("e-detailrowexpand").find("div").removeClass("e-gnextforward").addClass("e-gdiagonalnext")),o.replaceWith(a),this.clearSelection()}this._gridRows=this.model.editSettings.showAddNewRow?this.getContentTable().first().find(".e-rowcell").closest("tr.e-row, tr.e-alt_row").toArray():w.rows;this.model.enableAltRow&&this._refreshAltRow()}else if(i.requestType=="delete"){if(this._isUnboundColumn){var b=this.element.find(".e-editedrow"),f=this.getContentTable().find(".e-editedrow").get(0),e=b.length?u.firstChild.firstChild.firstChild:p.firstChild.firstChild.lastChild;b.length!=0&&(this.model.editSettings.editMode=="normal"||this.model.editSettings.editMode=="externalform"||this.model.editSettings.editMode=="externalformtemplate")?n(f).replaceWith(n(e)):this.model.editSettings.editMode=="inlineform"||this.model.editSettings.editMode=="inlineformtemplate"?(f=b.prev("tr").get(0),b.remove()):f=b.get(0)}this.model.allowPaging&&this.model.pageSettings.pageSize<=this.model.currentViewData.length&&this.getContentTable()[0].rows.length!=this.model.currentViewData.length&&(this.getContentTable().find("tr").length&&this._excludeDetailRows().length?this.multiDeleteMode?(it=u.firstChild.firstChild.rows.length,st=n(u.firstChild.firstChild.rows).slice(it-this.selectedRowsIndexes.length,it),n(r).append(st)):r.appendChild(u.firstChild.firstChild.lastChild):n(r).prepend(u.firstChild.firstChild.rows));(this.model.detailsTemplate!=null||this.model.childGrid!=null)&&(g=this.getContentTable().find(".e-detailrow:visible"),n.each(g,function(n){g.eq(n).closest("tr").prev().children(".e-detailrowexpand").length==0&&g.eq(n).remove()}));this._gridRows=w.rows;this.model.enableAltRow&&this._refreshAltRow()}else this.getContentTable().get(0).replaceChild(this.model.rowTemplate!=null?u.firstChild.lastChild:u.firstChild.firstChild,this.getContentTable().get(0).lastChild)}this._currentJsonData=this.model.currentViewData;this._gridRows=this.model.editSettings.showAddNewRow?this.getContentTable().first().find(".e-rowcell").closest("tr.e-row, tr.e-alt_row").toArray():this.getContentTable().get(0).rows;this.model.scrollSettings.frozenColumns>0&&(this._gridRows=[this._gridRows,this.getContentTable().get(1).rows]);ht={};(i.requestType=="sorting"||i.requestType=="filtering")&&this.model.scrollSettings.allowVirtualScrolling&&(i.requestType=="filtering"&&(this.getContent().first().ejScroller("refresh").ejScroller("isVScroll")?this.element.find(".gridheader").addClass("e-scrollcss"):this.element.find(".gridheader").removeClass("e-scrollcss"),ht=this._refreshVirtualPagerInfo()),this.model.scrollSettings.enableVirtualization?this._refreshVirtualView(this._currentVirtualIndex):this._refreshVirtualContent(ut),i.requestType=="filtering"&&this.getContent().first().ejScroller("refresh"));this.model.scrollSettings.enableVirtualization||this._eventBindings();break}case t.HeatMapGrid.Actions.Grouping:this._group(i);this._refreshStackedHeader();break;case t.HeatMapGrid.Actions.BeginEdit:this._edit(i);break;case t.HeatMapGrid.Actions.Add:this._add(i);break;case t.HeatMapGrid.Actions.Ungrouping:this._ungroup(i);break;case t.HeatMapGrid.Actions.VirtualScroll:this._isVirtualRecordsLoaded||(this.model.scrollSettings.enableVirtualization?this._replacingVirtualContent():this._replacingContent())}else i.requestType!="refresh"||this.model.currentViewData!=0||this.phoneMode||this.getContentTable().find("colgroup").first().replaceWith(this._getMetaColGroup()),this._newungroup(i);this._showGridLines();this._completeAction(i)},i.prototype._showGridLines=function(){var n=this.model.gridLines;n!="both"&&this.getContent().addClass(n!="none"?"e-"+n+"lines":"e-hidelines")},i.prototype._newungroup=function(n){n.requestType=="ungrouping"?this._ungroup(n):this.getContentTable().find("tbody").empty().first().append(this._getEmptyTbody())},i.prototype.setFormat=function(){for(var i,r,t=[],n=0;n<this.model.columns.length;n++)this.model.columns[n].type=="date"&&t.push(this.model.columns[n]);if(t.length>0)for(n=0;n<this.model.currentViewData.length;n++)for(i=0;i<t.length;i++)r=this.model.currentViewData[n][t[i].field],/^(\d{4}\-\d\d\-\d\d([tT][\d:\.]*)?)([zZ]|([+\-])(\d\d):?(\d\d))?$/.test(r)&&(this.model.currentViewData[n][t[i].field]=new Date(this.model.currentViewData[n][t[i].field]))},i.prototype._completeAction=function(i){var u,r;if(this.model.editSettings.showAddNewRow||(this.model.isEdit=!1),this._confirmedValue=!1,t.HeatMapGrid.Actions.Grouping!=i.requestType||!t.isNullOrUndefined(i.columnName)){if(i.columnSortDirection!="ascending"&&i.columnSortDirection!="descending"||t.isNullOrUndefined(i.columnName)||(u=this.getColumnByField(i.columnName),this.model.allowSorting&&this.model.allowMultiSorting?this._scolumns.push(u.field):this._gridSort=u.field),i.requestType!="beginedit"&&i.requestType!="add"&&this.setWidthToColumns(),(i.requestType=="save"||i.requestType=="cancel"||i.requestType=="delete")&&(this._isAddNew=!1,this.model.isResponsive&&this.model.minWidth&&this.windowonresize()),this.initialRender||!(t.HeatMapGrid.Actions.UnGrouping==i.requestType||this.model.groupSettings.groupedColumns.length>0)||n("#"+this._id+"EditForm").length?t.HeatMapGrid.Actions.Sorting==i.requestType&&this.model.allowSorting||t.HeatMapGrid.Actions.Refresh==i.requestType||t.HeatMapGrid.Actions.Cancel==i.requestType?!this.initialRender&&(this.model.scrollSettings.frozenRows>0||this.model.scrollSettings.frozenColumns>0)&&this._refreshScroller(i):t.HeatMapGrid.Actions.Delete==i.requestType||t.HeatMapGrid.Actions.Save==i.requestType||t.HeatMapGrid.Actions.Search==i.requestType?(this._editEventTrigger(i),!this.initialRender&&(this.model.scrollSettings.frozenRows>0||this.model.scrollSettings.frozenColumns>0)&&this._refreshScroller(i)):t.HeatMapGrid.Actions.Filtering==i.requestType?this._filterCompleteAction():t.HeatMapGrid.Actions.BeginEdit==i.requestType||t.HeatMapGrid.Actions.Add==i.requestType?this._editCompleteAction(i):(t.HeatMapGrid.Actions.Grouping==i.requestType||t.HeatMapGrid.Actions.Ungrouping==i.requestType)&&this["_"+i.requestType+"CompleteAction"](i):this._recalculateIndentWidth(),!this.initialRender&&this.model.showSummary&&this.model.summaryRows.length>0&&(this.model.currentViewData.length||this.element.children(".e-gridfooter").remove()),this.initialRender||(this.getContent().find("td.e-selectionbackground").length||this._setCurrentRow(i.requestType),i.requestType!="virtualscroll"&&this.clearColumnSelection()&&n(this.getHeaderTable().find("th.e-headercell")).removeClass("e-columnselection")),this.model.editSettings.editMode=="batch"&&this.refreshBatchEditMode(),(!this.initialRender&&(this.model.allowScrolling||this.model.isResponsive)&&(this._checkScrollActions(i.requestType)||this.model.editSettings.editMode.indexOf("inline")!=-1&&i.requestType=="beginedit")||this.model.scrollSettings.virtualScrollMode=="continuous"&&i.requestType=="virtualscroll")&&(this.model.isResponsive&&this.model.minWidth?this.windowonresize():this._refreshScroller(i)),this.model.scrollSettings.virtualScrollMode=="normal"&&i.requestType=="virtualscroll"&&this.getContent().find("div:first").scrollLeft(this.getScrollObject().scrollLeft()),this._customPop!=null&&i.requestType!="sorting"&&this._customPop.hide(),!this.model.allowScrolling||this.initialRender||this.model.scrollSettings.enableVirtualization||this.getContentTable().find("tr:last").find("td").addClass("e-lastrowcell"),t.HeatMapGrid.Actions.Refresh==i.requestType&&!this.initialRender&&this.model.allowGrouping&&this.model.groupSettings.groupedColumns.length>0&&this._groupingCompleteAction(i),t.HeatMapGrid.Actions.Refresh==i.requestType&&!this.initialRender&&this.model.allowGrouping&&this.model.groupSettings.groupedColumns.length<1&&this._ungroupingCompleteAction(i),this.model.textWrapSettings&&this._setTextWrap(),(this._isUngrouping||this._columnChooser)&&i.requestType=="refresh"||(this._isUngrouping=!1,this._columnChooser=!1),this.model.editSettings.showAddNewRow&&this.model.editSettings.editMode=="normal"&&(this.initialRender||this.getContentTable().find("tr.e-addedrow").length!=0||this.element.find(".e-gridcontent").find("tr").length==0||this._startAdd()),(t.HeatMapGrid.Actions.BeginEdit==i.requestType||t.HeatMapGrid.Actions.Add==i.requestType)&&n.isFunction(n.validator)&&this.setValidation(),this.initialRender||(this.model._groupingCollapsed=[]),!this.initialRender&&i.requestType=="refresh")for(r=0;r<this.model.filterSettings.filteredColumns.length;r++)this.getHeaderTable().find(".e-headercelldiv").eq(this.getColumnIndexByField(this.model.filterSettings.filteredColumns[r].field)).parent().find(".e-filtericon").addClass("e-filteredicon e-filternone");this.model.columnLayout!="fixed"||this.model.isEdit||this.setWidthToColumns()}},i.prototype._getDataIndex=function(n,t){for(var i,u,f=0,r=0,e=n.length;r<e;r++){for(i=0,u=this._primaryKeys.length;i<u;i++)if(this._checkPrimaryValue(n[r][this._primaryKeys[i]],t[this._primaryKeys[i]],this._primaryKeys[i]))continue;else i==u-1&&(f=1);if(f)return r}return-1},i.prototype._checkPrimaryValue=function(n,t,i){return this.getColumnByField(i).type=="string"&&(n=n.trim()),n!=t?!0:!1},i.prototype._eventBindings=function(){var a=this.model.scrollSettings.frozenColumns>0?this._gridRows[0].length:this._gridRows.length,e=0,h,c=this.model.pageSettings.pageSize,i,r,u,o,f,s,l;if(this._gridRecordsCount!=0&&(this.model.queryCellInfo!=null||this.model.rowDataBound!=null||this.model.mergeCellInfo!=null||this.model.templateRefresh!=null))for(i=0;i<a;i++){if(r=null,e=i,this.model.scrollSettings.allowVirtualScrolling&&i<c)if(this.model.scrollSettings.enableVirtualization){if(r=n(this._gridRows).eq(i),u=parseInt(n(r).attr("name"),32),n.inArray(u,this._queryCellView)!=-1)continue;this._virtualLoadedRecords[u]&&(o=this._virtualLoadedRecords[u][i%this._virtualRowCount]);e=u*this._virtualRowCount+i%this._virtualRowCount}else{for(f=0;f<this._cloneQuery.queries.length;f++)h=this._cloneQuery.queries[f].fn=="onPage"&&this._cloneQuery.queries[f].e.pageIndex-1;s=c*h;s!=0&&(r=this.getContentTable().find("tr[name="+s+"]").eq(i),e=r.index())}else this.model.scrollSettings.enableVirtualization&&(r=n(this._gridRows).eq(i));if(r=r||this.getRowByIndex(e),r.hasClass("e-virtualrow")||t.isNullOrUndefined(this._currentJsonData[i]||o))break;l=this.model.scrollSettings.enableVirtualization?o:this._currentJsonData[i];this._rowEventTrigger(r,l)}},i.prototype._rowEventTrigger=function(i,r){for(var o,f={row:i,data:r},s=i.cells,e=n(i).find(".e-rowcell"),u=0;u<e.length;u++)f={cell:e[u],data:r,text:e[u].innerHTML},o=this._getForeignKeyData(f.data),n(e[u]).hasClass("e-rowcell")&&(f.column=this.model.columns[u]),t.isNullOrUndefined(o)||(f.foreignKeyData=o),this._heatmap._setBinding(f),n(e[u]).hasClass("e-templatecell")&&(f={cell:e[u],column:this.model.columns[u],data:r,rowIndex:n(i).index()})},i.prototype.setWidthToColumns=function(){var r=this.getContentTable().children("colgroup").find("col"),u=this.getHeaderTable().children("colgroup").find("col"),f=this.element.width(),o=0,s=0,v=0,y,w,b,l,a,p,e,rt,h,i;for(this.model.groupSettings.groupedColumns.length&&!this.model.allowScrolling&&this.model.groupSettings.showGroupedColumn&&(y=this.getBrowserDetails(),y.browser=="msie"&&parseInt(y.version,10)>8&&r.first().css("width",3e3/f+"%")),t.isNullOrUndefined(this.model.detailsTemplate)||(w=this.model.groupSettings.groupedColumns.length,b=this.model.groupSettings.groupedColumns.length!=0?1:0,r.eq(b).css("width",this._detailsOuterWidth),u.eq(w).css("width",this._detailsOuterWidth)),this._detailColsRefresh(),r=this._$headerCols,u=this._$contentCols,i=0;i<u.length;i++){if(this.model.allowResizeToFit&&this.model.columns[i].width===undefined&&this.columnsWidthCollection[i]===undefined){var ut=this.model.groupSettings.groupedColumns.length?i+this.model.groupSettings.groupedColumns.length:i,k=this._resizer._getContentWidth(i),d=this.getHeaderTable().find(".e-headercelldiv").eq(ut),g=this._resizer._getHeaderContentWidth(d);v=this.model.editSettings.editMode=="normal"&&(this.model.isEdit||this._isAddNew)?r.eq(i).width():(k>g?k:g)+parseInt(d.css("padding-left"),10);this.columnsWidthCollection[i]=v;s+=this.model.columns[i].visible?v:0}else s+=this.model.columns[i].visible?parseInt(this.model.columns[i].width,10):0;if(t.isNullOrUndefined(this.columnsWidthCollection[i])){if(this.model.allowScrolling){var ft=(f/this.model.columns.length).toFixed(2),et=(f/(this.model.scrollSettings.buttonSize||18)/100).toFixed(2),c=ft-et;r.eq(i).css("width",c+"px");u.eq(i).css("width",c+"px");this.model.columns[i].width=c;this.columnsWidthCollection[i]=parseFloat(c)}}else r.eq(i).width(this.columnsWidthCollection[i]),u.eq(i).width(this.columnsWidthCollection[i]),this.model.columns[i].priority&&u.eq(i).addClass("e-table-priority-"+this.model.columns[i].priority)}if(this.model.columnLayout=="fixed"){this.model.scrollSettings&&this.model.scrollSettings.frozenColumns==0&&(this.getHeaderTable().width(s),this.getContentTable().width(s));var ot=this.model.scrollSettings.frozenColumns>0?this.getHeaderTable().eq(0).width()+this.getHeaderTable().eq(1).width():this.getHeaderTable().width(),nt=this.getHeaderContent().width()>ot?"addClass":"removeClass",tt=this.getHeaderTable(),it=this.getContentTable();this.model.scrollSettings.frozenColumns>0&&(tt=this.getVisibleColumnNames().length<=this.model.scrollSettings.frozenColumns?this.getHeaderTable().eq(1):this.getHeaderTable().eq(0),it=this.getVisibleColumnNames().length<=this.model.scrollSettings.frozenColumns?this.getContentTable().eq(1):this.getContentTable().eq(0));tt[nt]("e-tableLastCell");it[nt]("e-tableLastCell")}if(!this.model.allowScrolling&&this.model.allowResizeToFit&&s>f&&(this.model.allowScrolling=!0,this.model.scrollSettings.width=f,this.getHeaderTable().parent().addClass("e-headercontent"),!this.model.scrollSettings.frozenColumns>0&&this.getHeaderTable().width(f)),this.model.isEdit&&(e=r.clone(),this.model.editSettings.showAddNewRow&&(l=this.getContentTable().find(".e-editedrow")),a=this.model.scrollSettings.frozenColumns>0?this.getContent().find(".gridform").find("colgroup"):!t.isNullOrUndefined(l)&&l.length==1?l.find("colgroup"):n("#"+this._id+"EditForm").find("colgroup"),this.model.scrollSettings.frozenColumns>0&&a.first().empty().append(e.splice(0,this.model.scrollSettings.frozenColumns)),a.last().empty().append(e),(this.model.detailsTemplate!=null||this.model.childGrid!=null)&&a.prepend(this._getIndentCol())),this.model.groupSettings.groupedColumns.length)for(p=this.getContentTable().find(".e-recordtable").children("colgroup"),i=0;i<p.length;i++)e=r.clone(),rt=this._detailsOuterWidth!=null?this._detailsOuterWidth:"30px",(this.model.detailsTemplate!=null||this.model.childGrid!=null)&&e.splice(0,0,n(this._getIndentCol()).width(rt)[0]),p.eq(i).empty().append(e);if(this.model.scrollSettings.frozenColumns>0){for(h=0,i=0;i<this.columnsWidthCollection.length;i++)h+=parseInt(this.columnsWidthCollection[i],10),this.model.scrollSettings.frozenColumns-1==i&&(o=Math.ceil(h));this.getContent().find(".e-frozencontentdiv").outerWidth(o).end().find(".e-movablecontentdiv").outerWidth(h-o);this.getHeaderContent().find(".e-frozenheaderdiv").outerWidth(o).end().find(".e-movableheaderdiv").outerWidth(h-o)}},i.prototype._initialEndRendering=function(){if(this.model.scrollSettings.frozenColumns>0&&!this.model.allowScrolling){this.getContent().remove();this.getHeaderTable().eq(1).remove();return}if(this.model.scrollSettings.allowVirtualScrolling&&!this.model.scrollSettings.enableVirtualization,this._getRowHeights(),this.element.width()!=0)this.model.allowScrolling&&this._renderScroller();else if(this.model.allowScrolling&&this.element.width()==0){var n=this,i=setInterval(function(){n.element.width()==0||t.isNullOrUndefined(n.element.width())||(n._renderScroller(),n._endRendering(),clearInterval(i))},100);return}this._endRendering()},i.prototype._endRendering=function(){var n,i;!t.isNullOrUndefined(this.getContent().data("ejScroller"))&&this.model.allowScrolling&&(n=this.getScrollObject());i=this.model.enableRTL?"e-summaryscroll e-rtl":"e-summaryscroll";this.model.allowScrolling&&this.model.showSummary&&n._vScroll&&this.element.find(".e-summaryrow.e-scroller").addClass(i);this._addMedia();this.model.allowScrolling&&this.model.allowTextWrap&&!this.model.scrollSettings.allowVirtualScrolling&&this.getContent().first().ejScroller("refresh");this.model.scrollSettings.allowVirtualScrolling&&(this._currentPage()!=1||this.model.scrollSettings.enableVirtualization||(this._virtualLoadedRecords[this._currentPage()]=this._currentJsonData),this.model.scrollSettings.enableVirtualization?this._refreshVirtualView():this._refreshVirtualContent(),this.getContent().first().ejScroller("refresh"),this.getContent().ejScroller("isVScroll")?(this.element.find(".e-gridheader").addClass("e-scrollcss"),this.getHeaderTable().first().width(this.getContentTable().width())):this.element.find(".e-gridheader").removeClass("e-scrollcss"));this._selectedRow()!=-1&&(this.model.currentIndex=this._selectedRow());this.rowHeightRefresh();this.initialRender&&(!this.model.scrollSettings.enableVirtualization||this._gridRows.length<this._virtualRowCount)&&this._addLastRow()},i.prototype._addLastRow=function(){var r=this.getContentTable().find("tr:last").find("td"),u=0,i;if(this.model.allowScrolling&&!this.model.scrollSettings.allowVirtualScrolling&&!t.isNullOrUndefined(this.model.dataSource)&&!t.isNullOrUndefined(this.getRows())){for(i=0;i<this.getRows().length;i++)u+=n(this.getRows()[i]).height();u<this.getContent().height()-1&&r.addClass("e-lastrowcell")}this.model.scrollSettings.allowVirtualScrolling&&this.getContentTable().height()<this.getContent().height()&&r.addClass("e-lastrowcell")},i.prototype._addMedia=function(){if(typeof this.model.scrollSettings.width!="string"&&this.model.scrollSettings.width>0?this._responsiveScrollWidth=this._originalScrollWidth=this.model.scrollSettings.width:this._originalScrollWidth=this.element.width(),typeof this.model.scrollSettings.height!="string"&&this.model.scrollSettings.height>0&&(this._responsiveScrollHiehgt=this.model.scrollSettings.height),this.model.minWidth&&this.model.isResponsive){if(this._$onresize=n.proxy(this.windowonresize,this),n(window).bind("resize",this._$onresize),n.isFunction(window.matchMedia)){var t=window.matchMedia("(max-width: 768px)");this._mediaStatus=t.matches}this.windowonresize()}},i.prototype._getNoncontentHeight=function(){var n=this.getHeaderContent().outerHeight();return this.model.allowGrouping&&this.model.groupSettings.showDropArea&&(n+=this.element.find(".e-groupdroparea").outerHeight()),n},i.prototype._mediaQueryUpdate=function(n,i,r,u){var e,o,s,f;if(window.innerWidth<=320&&this.model.enableResponsiveRow){e=this.getContentTable()[0].style;e.removeAttribute?e.removeAttribute("min-width"):e.removeProperty("min-width");o=this.getContent().data("ejScroller");o&&this.getContent().ejScroller("destroy");return}n?(this.model.scrollSettings.width=t.isNullOrUndefined(this._responsiveScrollWidth)?r:Math.min(this._responsiveScrollWidth,r),f=Math.min(u,i)-this._getNoncontentHeight(),f=t.isNullOrUndefined(this._responsiveScrollHiehgt)?f:Math.min(this._responsiveScrollHiehgt,f),f=this.model.scrollSettings.height!="auto"?f-(parseInt(this.element.parent().css("margin-bottom"))+1):this.model.scrollSettings.height,this.model.minWidth>r&&i>u&&(f=f!="auto"?f+this.model.scrollSettings.buttonSize:f),t.isNullOrUndefined(this.getRows())&&(f="100%"),this.model.scrollSettings.height=f,this.element.find(".e-gridheader").first().find("div").first().addClass("e-headercontent"),this._renderScroller()):(this.model.scrollSettings.width="100%",t.isNullOrUndefined(this._responsiveScrollWidth)||(this.model.scrollSettings.width=Math.min(this._responsiveScrollWidth,r)),s=Math.min(u,i),f=s-this._getNoncontentHeight(),t.isNullOrUndefined(this._responsiveScrollHiehgt)||(f=Math.min(this._responsiveScrollHiehgt,u)),f=this.model.scrollSettings.height!="auto"?f-parseInt(this.element.parent().css("margin-bottom")):this.model.scrollSettings.height,t.isNullOrUndefined(this.getRows())&&(f="100%"),this.model.scrollSettings.height=f,this.element.find(".e-gridheader").first().find("div").first().addClass("e-headercontent"),this._renderScroller())},i.prototype.windowonresize=function(){var i,f;this.model.scrollSettings.width=this._responsiveScrollWidth;this.element.width("100%");this.getContentTable().width("100%");this.getHeaderTable().width("100%");this.getContentTable().css("minWidth",this.model.minWidth);i=this.element.width();var e=n(window).height()-this.element.offset().top,s=t.isNullOrUndefined(this.getRows())?1:this.getRows().length,o=this.element.parent().is(n("body"))||this.element.parent().height()==n("body").height()||this.element.parent()[0].style.height=="",r=this.getContentTable()[0].scrollHeight+this._getNoncontentHeight(),u=o?e:this.element.parent().height();r+=parseInt(this.element.parent().css("margin-top"));f=this.model.minWidth>i||u<=r;this._mediaQueryUpdate(f,u,i,r);this._refreshScroller({})},i.prototype._getRowHeights=function(){var n=this.getRows(),i,r,t;if(n!==null)if(this._rowHeightCollection=[],n[1]!==undefined&&n[1].length&&(this.model.scrollSettings.frozenColumns>0&&n[0]!==undefined||n[0]!==undefined&&typeof n[0].item!="undefined"&&typeof n[0].length=="number"&&typeof n[1].item!="undefined"&&typeof n[1].length=="number"))for(i=n[0],r=n[1],t=0;t<i.length;t++)this._rowHeightCollection[t]=i[t].offsetTop>=r[t].offsetTop?i[t].offsetTop:r[t].offsetTop;else for(t=0;t<n.length;t++)this._rowHeightCollection[t]=n[t].offsetTop;return this._rowHeightCollection},i.prototype._getEmptyTbody=function(){var i=t.buildTag("td.emptyrecord",this.localizedLabels.EmptyRecord,{},{colSpan:this.model.columns.length});return n(document.createElement("tr")).append(i)},i.prototype._getIndentCol=function(){return t.buildTag("col","",{width:"30px"})},i.prototype._wireEvents=function(t){t._on(this.element,n.isFunction(n.fn.tap)&&this.model.enableTouch?"tap":"click",n.proxy(this._clickHandler,this));this._enableRowHover(undefined,t);this.model.allowGrouping&&(this._enableGroupingEvents(),this._on(this.element,"mouseenter mouseleave",".e-groupdroparea,.e-groupheadercell",n.proxy(this._dropAreaHover,this)))},i.prototype._clickHandler=function(t){var i=n(t.target),e=n("[id$='ccDiv'].e-grid.e-columnChooser"),o,a=n("#"+this._id+"EditForm"),s,r,u,v,f,y,h,c,l,p,w;if(e.length)for(v=!0,f=0;f<e.length;f++)(i.parents(".e-ccButton").length||i.hasClass("e-ccButton"))&&(v=n(t.target).closest(".e-grid").attr("id")+"ccDiv"!=e[f].id);if(i.closest(".e-grid").attr("id")===this._id&&!i.closest("#"+this._id+"EditForm").length){if(i.hasClass("e-rowcell")||i.closest("td").is(".e-rowcell")||i.hasClass("e-headercell")&&t.clientY-i.offset().top<i.height()/4){if(this._bulkEditCellDetails.cancelSave){this._bulkEditCellDetails.cancelSave=!1;return}if(this.model.editSettings.editMode=="batch"&&n.isFunction(n.validator)&&a.length&&a.validate().errorList.length>0)return;this.model.editSettings.editMode=="batch"&&this.element.focus();s=i.closest("tr").hasClass("e-insertedrow")?this.model.groupSettings.groupedColumns.length:0;y=i.closest(".e-rowcell").index()!=-1?i.closest(".e-rowcell").index():i.closest(".e-headercell").index()-this.model.groupSettings.groupedColumns.length;r=i.hasClass("e-rowcell")?i.index()-s:y-s;r=this.model.detailsTemplate!=null||this.model.childGrid!=null?r-1:r;this.model.scrollSettings.frozenColumns&&(i.closest(".e-movableheaderdiv").length||i.closest(".e-movablecontentdiv").length)&&(r=r+this.model.scrollSettings.frozenColumns);u=this.getIndexByRow(i.closest("tr"));this._bulkEditCellDetails.columnIndex=r;this._bulkEditCellDetails.rowIndex=u;this.model.allowSelection&&(this.model.selectionType=="multiple"&&((t.ctrlKey||this._enableSelectMultiTouch)&&(this.multiSelectCtrlRequest=!0),t.shiftKey&&(this.multiSelectShiftRequest=!0,this._allowcellSelection&&u>-1&&this.selectCells([[u,[r]]]),this._selectedRow(this.getIndexByRow(i.closest("tr")))),t.pointerType=="touch"&&this._customPop!=null&&!this._customPop.is(":visible")&&this._customPop.find(".e-rowselect").hasClass("e-spanclicked")&&this.model.selectionSettings.selectionMode=="row"&&this._customPop.show(),t.pointerType=="touch"&&this._customPop!=null&&(this._customPop.find(".e-sortdirect").is(":visible")||!this._customPop.find(".e-rowselect").hasClass("e-spanclicked"))&&this.model.selectionType=="multiple"&&(this._customPop.removeAttr("style"),h=i.offset(),this._customPop.offset({top:0,left:0}).offset({left:h.left,top:h.top-this.getRowHeight()}).find(".e-sortdirect").hide().end().find(".e-rowselect").show().end().show())),this.multiSelectShiftRequest||(this._allowcellSelection&&u>-1&&(c=this._checkCellSelectionByRow(u,r),(this.model.selectionSettings.enableToggle&&this.selectedRowCellIndexes.length==1&&this.selectedRowCellIndexes[0].cellIndex.length==1||t.ctrlKey&&this.model.selectionType=="multiple")&&c!=-1&&this.selectedRowCellIndexes.length>0&&this.selectedRowCellIndexes[0].cellIndex.length>0?this.clearCellSelection(c.rowIndex,r):this.selectCells([[u,[r]]])),this._allowrowSelection&&u>-1&&(l=this.getIndexByRow(i.closest("tr")),this.model.scrollSettings.enableVirtualization&&(p=u%this._virtualRowCount,w=parseInt(n(i).closest("tr").attr("name"),32),l=w*this._virtualRowCount-(this._virtualRowCount-p)),this.model.selectionSettings.enableToggle&&this.getSelectedRecords().length==1&&n.inArray(this.getIndexByRow(i.closest("tr")),this.selectedRowsIndexes)!=-1&&this.clearSelection(l)),this._allowcolumnSelection&&i.hasClass("e-headercell")&&!i.hasClass("e-stackedHeaderCell")&&t.clientY-i.offset().top<i.height()/4&&this.model.selectionSettings.enableToggle&&this.selectedColumnIndexes.length==1&&n.inArray(r,this.selectedColumnIndexes)!=-1&&this.clearColumnSelection(r),this.multiSelectCtrlRequest=!1),this.multiSelectShiftRequest=!1);o=this.model.columns[this._bulkEditCellDetails.columnIndex].field;i.closest(".e-rowcell").length&&o&&(this._tabKey=!1,this.model.editSettings.allowEditing&&this.model.editSettings.editMode=="batch"&&this.editCell(n.inArray(i.closest("tr").get(0),this.getRows()),o))}(i.hasClass("e-rowselect")||i.hasClass("e-sortdirect"))&&(i.hasClass("e-spanclicked")?(i.removeClass("e-spanclicked"),i.hasClass("e-rowselect")&&(this._enableSelectMultiTouch=!1),i.hasClass("e-sortdirect")&&(this._enableSortMultiTouch=!1),this._customPop.hide()):(i.addClass("e-spanclicked"),i.hasClass("e-rowselect")&&(this._enableSelectMultiTouch=!0),i.hasClass("e-sortdirect")&&(this._enableSortMultiTouch=!0)));i.is(".e-filtericon")&&i.closest(".e-detailrow").length!=0&&t.preventDefault();this.model.allowSearching&&this._searchBar!=null&&(i.is(this._searchBar.find(".e-cancel"))?this._searchBar.find("input").val(""):t.target.id==this._id+"_searchbar"?this._searchBar.find(".e-cancel").removeClass("e-hide"):this._searchBar.find(".e-cancel").hasClass("e-hide")||this._searchBar.find(".e-cancel").addClass("e-hide"))}},i.prototype._checkCellSelectionByRow=function(t,i){for(var r=0;r<this.selectedRowCellIndexes.length;r++)if(this.selectedRowCellIndexes[r].rowIndex==t)break;return r!=this.selectedRowCellIndexes.length&&n.inArray(i,this.selectedRowCellIndexes[r].cellIndex)!=-1?this.selectedRowCellIndexes[r]:-1},i.prototype._destroy=function(){var t,r,u,i;if(this.element.off(),this.element.find(".e-gridheader").find(".e-headercontent,.e-movableheader").add(this.getContent().find(".e-content,.e-movablecontent")).unbind("scroll"),t=n("#"+this._id+"EditForm"),t.length){for(r=t.find(".e-field"),i=0;i<r.length;i++)u=n(r[i]);t.remove()}this.model.showColumnChooser&&(n("#"+this._id+"ccDiv").remove(),n("#"+this._id+"_ccTail").remove(),n("#"+this._id+"_ccTailAlt").remove());this.model.allowFiltering&&this.model.filterSettings.filterType=="excel"&&this._excelFilter.resetExcelFilter();this.model.allowReordering&&n(".e-columndropindicator").remove();this._$onresize&&n(window).unbind("resize",this._$onresize);this.element.empty().removeClass("e-grid "+this.model.cssClass)},i}();t.HeatMapGrid=function(n,t){return new i(n,t)};t.HeatMapGrid.Locale=t.HeatMapGrid.Locale||{};t.HeatMapGrid.Actions={Paging:"paging",Sorting:"sorting",Filtering:"filtering",BeginEdit:"beginedit",Save:"save",Add:"add",Delete:"delete",Cancel:"cancel",Grouping:"grouping",Ungrouping:"ungrouping",Refresh:"refresh",Reorder:"reorder",Search:"searching",BatchSave:"batchsave",VirtualScroll:"virtualscroll"};t.HeatMapGrid.ClipMode={Ellipsis:"ellipsis",Clip:"clip",EllipsisWithTooltip:"ellipsiswithtooltip"};t.HeatMapGrid.SelectionType={Single:"single",Multiple:"multiple"};t.HeatMapGrid.SelectionMode={Row:"row",Cell:"cell",Column:"column"};t.HeatMapGrid.Locale["default"]=t.HeatMapGrid.Locale["en-US"]={EmptyRecord:"No records to display",GroupDropArea:"Drag a column header here to group its column",DeleteOperationAlert:"No records selected for delete operation",EditOperationAlert:"No records selected for edit operation",SaveButton:"Save",OkButton:"OK",CancelButton:"Cancel",EditFormTitle:"Details of ",AddFormTitle:"Add New Record",Notactionkeyalert:"This Key-Combination is not available",Keyconfigalerttext:"This Key-Combination has already been assigned to ",GroupCaptionFormat:"{{:headerText}}: {{:key}} - {{:count}} {{if count == 1 }} item {{else}} items {{/if}} ",BatchSaveConfirm:"Are you sure you want to save changes?",BatchSaveLostChanges:"Unsaved changes will be lost. Are you sure you want to continue?",ConfirmDelete:"Are you sure you want to Delete Record?",CancelEdit:"Are you sure you want to Cancel the changes?",PagerInfo:"{0} of {1} pages ({2} items)",FrozenColumnsViewAlert:"Frozen columns should be in grid view area",FrozenColumnsScrollAlert:"Enable allowScrolling while using frozen Columns",FrozenNotSupportedException:"Frozen Columns and Rows are not supported for Grouping, Row Template, Detail Template, Hierarchy Grid and Batch Editing",Add:"Add",Edit:"Edit",Delete:"Delete",Update:"Update",Cancel:"Cancel",Done:"Done",Columns:"Columns",SelectAll:"(Select All)",PrintGrid:"Print",ExcelExport:"Excel Export",WordExport:"Word Export",PdfExport:"PDF Export",StringMenuOptions:[{text:"StartsWith",value:"StartsWith"},{text:"EndsWith",value:"EndsWith"},{text:"Contains",value:"Contains"},{text:"Equal",value:"Equal"},{text:"NotEqual",value:"NotEqual"}],NumberMenuOptions:[{text:"LessThan",value:"LessThan"},{text:"GreaterThan",value:"GreaterThan"},{text:"LessThanOrEqual",value:"LessThanOrEqual"},{text:"GreaterThanOrEqual",value:"GreaterThanOrEqual"},{text:"Equal",value:"Equal"},{text:"NotEqual",value:"NotEqual"}],PredicateAnd:"AND",PredicateOr:"OR",Filter:"Filter",FilterMenuCaption:"Filter Value",FilterbarTitle:"'s filter bar cell",MatchCase:"Match Case",Clear:"Clear",ResponsiveFilter:"Filter",ResponsiveSorting:"Sort",Search:"Search",DatePickerWaterMark:"Select date",EmptyDataSource:"DataSource must not be empty at initial load since columns are generated from dataSource in AutoGenerate Column Grid",ForeignKeyAlert:"The updated value should be a valid foreign key value",True:"true",False:"false",UnGroup:"Click here to ungroup",AddRecord:"Add Record",EditRecord:"Edit Record",DeleteRecord:"Delete Record",Save:"Save",Grouping:"Group",Ungrouping:"Ungroup",SortInAscendingOrder:"Sort In Ascending Order",SortInDescendingOrder:"Sort In Descending Order",NextPage:"Next Page",PreviousPage:"Previous Page",FirstPage:"First Page",LastPage:"Last Page",EmptyRowValidationMessage:"Atleast one field must be updated",NoResult:"No Matches Found"}})(jQuery,Syncfusion);"use strict";var n=this&&this.__extends||function(n,t){function r(){this.constructor=n}for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)};(function(t){var r=function(i){function r(n,r){return i.call(this),this._rootCSS="e-heatmap",this._grid=null,this.PluginName="ejHeatMap",this._setFirst=!1,this.id="null",this.model=null,this.validTags=["div"],this.defaults={itemsSource:null,itemsMapping:{itemsSource:null,columnStyle:{headerTemplateID:"",templateID:"",textAlign:"",width:null},headerMapping:{columnStyle:{headerTemplateID:"",templateID:"",textAlign:"",width:null},displayName:"",propertyName:""},column:{displayName:"",propertyName:""},columnMapping:null,row:{displayName:"",propertyName:""},value:{displayName:"",propertyName:""}},colorMappingCollection:null,selectedItem:null,legendCollection:[],heatMapCell:{showContent:"visible",showColor:!0},cellMouseOver:null,cellMouseEnter:null,cellMouseLeave:null,cellSelected:null,create:null,destroy:null,isResponsive:!1,enableVirtualization:!1,enableRTL:!1,defaultColumnStyle:{textAlign:"center",headerTemplateID:"",templateID:""},enableTooltip:!1,tooltipSettings:{templateId:null,position:{stem:{horizontal:"left",vertical:"center"},target:{horizontal:"right",vertical:"center"}},isBalloon:!0,animation:{effect:"none",speed:0},associate:"mouseFollow",trigger:"hover"},width:null,height:null},this.observables=["heatMapCell.showContent","heatMapCell.showColor",],this.dataTypes={colorMappingCollection:"data",itemsMapping:{columnMapping:"data"},itemsSource:"data",legendCollection:"data"},this._tags=[{tag:"colorMappingCollection",attr:["value","color","label.bold","label.italic","label.text","label.textDecoration","label.fontSize","label.fontFamily","label.fontColor",],singular:"colorMapping"}],this._heatmapShowContent=ej.util.valueFunction("heatMapCell.showContent"),this._heatmapShowColor=ej.util.valueFunction("heatMapCell.showColor"),n&&(n.jquery||(n=t("#"+n)),n.length)?t(n).ejHeatMap(r).data(this.PluginName):void 0}return n(r,i),r.prototype.setModel=function(n,t){this.setModel(n,t)},r.prototype._destroy=function(){this.element.removeClass("e-heatmap e-js e-grid").empty()},r.prototype._setModel=function(n){var i=!1,r=this._grid,f,u;if(r){for(f in n)switch(f){case"colorMappingCollection":n.colorMappingCollection&&n.colorMappingCollection.length>0&&(this.model.colorMappingCollection=n.colorMappingCollection,i=!0);break;case"itemsSource":n.itemsSource&&(this.model.itemsSource=n.itemsSource,u=void 0,u=this.model.itemsMapping&&this.model.itemsMapping.column&&this.model.itemsMapping.row?this._bindCellMapValues():this._bindTableMapValues(),r.dataSource(u.dataTableMapping,!1),i=!0);break;case"legendCollection":n.legendCollection&&n.legendCollection>0&&(this.model.legendCollection=n.legendCollection);break;case"heatMapCell":case"heatmapCell":n.heatmapCell&&(n.heatmapCell.showContent!=undefined&&(n.heatMapCell.showContent=n.heatmapCell.showContent),n.heatmapCell.showColor!=undefined&&(n.heatMapCell.showColor=n.heatmapCell.showColor));n.heatMapCell&&(n.heatMapCell.showColor&&(typeof n.heatMapCell.showColor()=="boolean"?this._heatmapShowColor(n.heatMapCell.showColor()):this.model.heatMapCell.showColor=n.heatMapCell.showColor||n.heatMapCell.showColor===!1?n.heatMapCell.showColor:this.model.heatMapCell.showColor),n.heatMapCell.showContent&&(typeof n.heatMapCell.showContent()=="function"?this._heatmapShowContent(n.heatMapCell.showContent()):this.model.heatMapCell.showContent=n.heatMapCell.showContent||n.heatMapCell.showContent===!1?n.heatMapCell.showContent:this.model.heatMapCell.showContent),i=!0);break;case"tooltipSettings":case"enableTooltip":n.enableTooltip!=undefined&&(this.model.enableTooltip=n.enableTooltip);n.tooltipSettings&&(n.tooltipSettings.position&&(this.model.tooltipSettings.position=n.tooltipSettings.position),n.tooltipSettings.isBalloon!=undefined&&(this.model.tooltipSettings.isBalloon=n.tooltipSettings.isBalloon),n.tooltipSettings.animation&&(this.model.tooltipSettings.animation=n.tooltipSettings.animation),n.tooltipSettings.associate&&(this.model.tooltipSettings.associate=n.tooltipSettings.associate),n.tooltipSettings.trigger&&(this.model.tooltipSettings.trigger=n.tooltipSettings.trigger),n.tooltipSettings.templateId!==undefined&&n.tooltipSettings.templateId!==null&&(this.model.tooltipSettings.templateId=n.tooltipSettings.templateId));this._hasClass(this.element[0],"e-tooltip")&&(t("#"+this.element[0].id).data("ejTooltip").destroy(),this.element.addClass("e-js"));this._renderTooltip()}i&&(r.refreshContent(),this._updateResponsiveSize(r))}},r.prototype._hasClass=function(n,t){for(var r=n.classList,i=0;i<r.length;i++)if(r[i]==t)return!0},r.prototype._init=function(){var n=this._getSpace();this._initData();this._updateDataSource();this._wireEvents();this._renderMapGrid();this._renderTooltip()},r.prototype._wireEvents=function(){this._on(t(window),"resize",this._updateResponsiveSize);this._on(t(window),"load",this._updateResponsiveSize);t(window).bind("mousewheel DOMMouseScroll",t.proxy(this._hideLegendMarker,this))},r.prototype._hideLegendMarker=function(){for(var r,n=t(".gradient_scale_marker"),i=0;n.length>0&&i<n.length;i++)n[i]&&(r=n[i],r.style.visibility="hidden")},r.prototype._getBoundingClientRect=function(n){var i=n.getBoundingClientRect();return ej.browserInfo().name==="msie"&&ej.browserInfo().version==="8.0"&&(i={left:i.left,top:i.top,right:i.right,bottom:i.bottom,width:t(n).width(),height:t(n).height()}),i},r.prototype._getSpace=function(){var n=this._getBoundingClientRect(this.element[0])},r.prototype._initData=function(){typeof this.model.legendCollection=="string"&&(this.model.legendCollection=JSON.parse(this.model.legendCollection));this._updateHeatMapCellData(this.model);this.model.enableTooltip&&this.model.tooltipSettings&&!this.model.tooltipSettings.templateId&&(this.model.tooltipSettings.templateId=this.element[0].id+"_DefaultTooltipDiv")},r.prototype._updateHeatMapCellData=function(n){n&&n.heatmapCell&&(n.heatmapCell.showContent!=undefined&&(this.model.heatMapCell.showContent=n.heatmapCell.showContent),n.heatmapCell.showColor!=undefined&&(this.model.heatMapCell.showColor=n.heatmapCell.showColor))},r.prototype._updateDataSource=function(){},r.prototype._bindTableMapValues=function(){var o=[],f=[],e,r,n,u,i,t;if(this.model.itemsSource&&this.model.itemsSource.length>0)for(e=this.model.itemsSource,t=0;t<e.length;t++)o.push(e[t]);for(r=this.model.itemsMapping.headerMapping,n=r.columnStyle,n=this._mergeDefaultSettings(n,!0),u=!0,u=this.model.enableVirtualization?!1:u,r&&f.push({field:r.propertyName,headerText:r.displayName,isFrozen:u,width:n&&n.width?n.width:null,textAlign:n&&n.textAlign?n.textAlign:"center",headerTemplateID:n&&n.headerTemplateID?n.headerTemplateID:"",templateID:n&&n.templateID?n.templateID:""}),i=this.model.itemsMapping.columnMapping,t=0;i&&t<i.length;t++)n=i[t].columnStyle,n=this._mergeDefaultSettings(n,!1),f.push({field:i[t].propertyName,headerText:i[t].displayName,width:n&&n.width?n.width:null,textAlign:n&&n.textAlign?n.textAlign:"center",headerTemplateID:n&&n.headerTemplateID?n.headerTemplateID:"",templateID:n&&n.templateID?n.templateID:""});return{dataTableMapping:o,columns:f}},r.prototype._renderMapGrid=function(){var n=null,f,i,r,u;n=this.model.itemsMapping&&this.model.itemsMapping.column&&this.model.itemsMapping.column.propertyName!=""&&this.model.itemsMapping.row&&this.model.itemsMapping.row.propertyName!=""?this._bindCellMapValues():this._bindTableMapValues();f=null;this.model.height===null&&delete this.model.height;this.model.width===null&&delete this.model.width;n&&t("#"+this.element[0].id)[0]&&(i=ej.HeatMapGrid({},this),r={height:this.model.height!==undefined?this.model.height-40:"auto",width:this.model.width!==undefined?Number(this.model.width):"100%",allowVirtualScrolling:this.model.enableVirtualization?!0:!1,virtualScrollMode:"normal",enableVirtualization:this.model.enableVirtualization?!0:!1},this.model.width===undefined&&delete r.width,u={dataSource:n.dataTableMapping?n.dataTableMapping:[],columns:n.columns?n.columns:[],enableRowHover:!1,allowScrolling:!0,selectionSettings:{selectionMode:[ej.HeatMapGrid.SelectionMode.Cell]},queryCellInfo:t.proxy(this._setBinding,this),cellSelected:t.proxy(this._cellSelected,this),isResponsive:this.model.isResponsive?this.model.isResponsive:!1,enableResponsiveRow:!1,scrollSettings:r,minWidth:10,enableRTL:this.model.enableRTL?this.model.enableRTL:!1},i._init(t("#"+this.element[0].id),u,this),this._updateResponsiveSize(i))},r.prototype._updateResponsiveSize=function(n){var o,s,f,e,i,r,u,h;if(this.model.isResponsive){if(o=t(".e-movableheaderdiv"),s=t(".e-movablecontentdiv"),o.css("width","100%"),s.css("width","100%"),o[0]&&(f=o[0].getElementsByTagName("colgroup")[0],f&&f.childNodes&&f.childNodes.length>0))for(i=0;this.model.itemsMapping.columnMapping&&i<this.model.itemsMapping.columnMapping.length;i++)r=this.model.itemsMapping.columnMapping[i],!r||r.columnStyle&&r.columnStyle.width||(u=t(f.childNodes[i]),u&&u[0]&&(u[0].style.width=""));if(s[0]&&(e=s[0].getElementsByTagName("colgroup")[0],e&&e.childNodes&&e.childNodes.length>0))for(i=0;this.model.itemsMapping.columnMapping&&i<this.model.itemsMapping.columnMapping.length;i++)r=this.model.itemsMapping.columnMapping[i],!r||r.columnStyle&&r.columnStyle.width||(u=t(e.childNodes[i]),u&&u[0]&&(u[0].style.width=""));n&&n._refreshScroller&&(h=t(this.element).find(".e-scrollbar")[0],h&&(this.model.enableVirtualization||h.parentNode.removeChild(h),n._initScrolling()))}},r.prototype._cellSelected=function(n){this.model.selectedItem={cellValue:n.currentCell[0].ejHeatMapData.cellValue,source:n.currentCell[0].ejHeatMapData.data,cell:n.currentCell[0]};this._raiseEvent("cellSelected",{cellValue:n.currentCell[0].ejHeatMapData.cellValue,source:this._getTableData(n.currentCell[0].ejHeatMapData),cell:n.currentCell[0]})},r.prototype._componentToHex=function(n){var t=n.toString(16);return t.length===1?"0"+t:t},r.prototype._rgbToHex=function(n,t,i){return isNaN(n)&&isNaN(t)&&isNaN(i)?"":"#"+this._componentToHex(n)+this._componentToHex(t)+this._componentToHex(i)},r.prototype._setBinding=function(n){var i=t(n.cell),u=this._convertToRBG(ej.parseFloat(ej.format(n.text,"c")),this.model.colorMappingCollection),e=this._rgbToHex(u.R,u.G,u.B),f=this.model.itemsMapping.columnStyle&&this.model.itemsMapping.columnStyle[n.column.field]?this.model.itemsMapping.columnStyle[n.column.field]:null,r;this._heatmapShowColor()!=undefined&&(n.column.field&&f&&f.fillColor?(r=this._getForeGroundColor(f.fillColor),i.css("background-color",f.fillColor).css("color",r?r:"black")):(this._heatmapShowColor()===!1?i.css("background-color","white"):(r=this._getForeGroundColor(u),i.css("background-color",e).css("color",r?r:"")),this._setClassOnForzen(n.cell)&&i.index()===0&&i.css("font-weight","normal").css("font-size",12)));this._canEnableContent(n)&&(i[0].innerHTML="&nbsp;");this._bindHoverOutEvents(n,e);this.model.itemsMapping.headerMapping&&n.column.field&&this.model.itemsMapping.headerMapping.propertyName===n.column.field||i.addClass("sf-ht-enabletooltip")},r.prototype._canEnableContent=function(n){if(this._heatmapShowContent()===ej.datavisualization.HeatMap.CellVisibility.Hidden)return this.model.itemsMapping.headerMapping&&n.column&&this.model.itemsMapping.headerMapping.propertyName===n.column.field?!1:!0},r.prototype._setClassOnForzen=function(n){do if(n)if(n=n.parentNode,n){if(n.className==="e-movablecontent")return!0}else break;while(n&&n.className!=="e-gridcontent");return!1},r.prototype._getForeGroundColor=function(n){if(n.R!==undefined&&n.G!==undefined&&n.B!==undefined){var t=(n.R+n.G+n.B)/3;return t<255*.5?"white":isNaN(n.R)&&isNaN(n.G)&&isNaN(n.B)?"":"black"}return null},r.prototype._getTableData=function(n){var f=n.data,i,r,u,t;if(this.model.itemsMapping&&this.model.itemsMapping.column&&this.model.itemsMapping.row){if(i=this.model.itemsMapping.row,r=this.model.itemsMapping.column,n.column.field)return u=n.column.field,t={},t[i.propertyName]=f[i.propertyName],t.value=n.cellValue,t[r.propertyName]=u,t}else return n.data},r.prototype._bindHoverOutEvents=function(n,i){t(n.cell).mouseenter(t.proxy(this._cellMouseEnter,this)).mousemove(t.proxy(this._cellMouseHover,this)).mouseleave(t.proxy(this._cellMouseOut,this));n.cell.ejHeatMapData={column:n.column,cellValue:n.text,data:n.data,rgb:i}},r.prototype._cellMouseOut=function(n){var i,r,u;for(this._raiseEvent("cellMouseLeave",{cellValue:n.currentTarget.ejHeatMapData.cellValue,source:this._getTableData(n.currentTarget.ejHeatMapData),cell:n.currentTarget}),i=t(".gradient_scale_marker"),r=0;i.length>0&&r<i.length;r++)i[r]&&(u=i[r],u.style.visibility="hidden")},r.prototype._cellMouseHover=function(n){this._raiseEvent("cellMouseOver",{cellValue:n.currentTarget.ejHeatMapData.cellValue,source:this._getTableData(n.currentTarget.ejHeatMapData),cell:n.currentTarget})},r.prototype._cellMouseEnter=function(n){var r,i,u,f;if(this.model.legendCollection.length>0)for(r=0,i=null;r<this.model.legendCollection.length;r++)u=this.model.legendCollection[r],u&&t("#"+u)[0]&&(f=t("#"+u).data("ejHeatMapLegend"),f&&(n.currentTarget.ejHeatMapData&&(i=n.currentTarget.ejHeatMapData.cellValue),i&&f.model.legendMode===ej.datavisualization.HeatMap.LegendMode.Gradient.toString()&&(i=Number(i),this.model.itemsMapping.headerMapping&&n.currentTarget.ejHeatMapData&&n.currentTarget.ejHeatMapData.column.field&&this.model.itemsMapping.headerMapping.propertyName===n.currentTarget.ejHeatMapData.column.field?this._hideLegendMarker():this._drawLegendMarker(f,i))));this._raiseEvent("cellMouseEnter",{cellValue:n.currentTarget.ejHeatMapData.cellValue,source:this._getTableData(n.currentTarget.ejHeatMapData),cell:n.currentTarget})},r.prototype._raiseEvent=function(n,t){if(this.model[n])return this._trigger(n,t)},r.prototype._drawDefaultTooltip=function(n){var t=document.getElementById(this.element[0].id+"_DefaultTooltipDiv");return t||(t=this._createDefaultTooltip(n)),t},r.prototype._createDefaultTooltip=function(n){var r=document.createElement("div"),f,t,i,u;return f={id:this.element[0].id+"_DefaultTooltipDiv","class":"e-heatmap-tooltip-default",style:"padding-top:3px; height: 24px;pointer-events:none;position: absolute"},t=this.model.itemsMapping.headerMapping&&this.model.itemsMapping.headerMapping.displayName?this.model.itemsMapping.headerMapping.displayName:"",t==""&&(t=this.model.itemsMapping.row&&this.model.itemsMapping.row.displayName?this.model.itemsMapping.headerMapping.displayName:""),n.event.currentTarget&&n.event.currentTarget.ejHeatMapData&&this.model.itemsMapping.headerMapping&&this.model.itemsMapping.headerMapping.propertyName&&(i=n.event.currentTarget&&n.event.currentTarget.ejHeatMapData,u=i.data[this.model.itemsMapping.headerMapping.propertyName]),r.innerHTML="<table><tr><td style='min-width:50px;padding-right: 10px;'>"+t+"<\/td><td>"+u+"<\/td><\/tr><tr><td>Value<\/td><td>"+i.cellValue+"<\/td><\/tr><\/table>",r},r.prototype._renderTooltip=function(){var n=this;n.model.enableTooltip&&t("#"+this.element[0].id).ejTooltip({target:".sf-ht-enabletooltip",position:this.model.tooltipSettings.position,isBalloon:this.model.tooltipSettings.isBalloon,animation:this.model.tooltipSettings.animation,associate:this.model.tooltipSettings.associate,trigger:this.model.tooltipSettings.trigger,beforeOpen:function(i){if(n.model.enableTooltip)if(t("#"+n.element[0].id).ejTooltip("instance").hide(),i.event.currentTarget&&i.event.currentTarget.ejHeatMapData&&n.model.tooltipSettings.templateId!=this.element[0].id+"_DefaultTooltipDiv")t("#"+n.element[0].id).ejTooltip({content:t.templates("#"+n.model.tooltipSettings.templateId).render(i.event.currentTarget.ejHeatMapData)});else{var r=n._drawDefaultTooltip(i);t("#"+n.element[0].id).ejTooltip({content:t.templates(r).render(i.event.currentTarget.ejHeatMapData)})}}})},r.prototype._drawLegendMarker=function(n,i){var s,f,u;if(n&&i!==undefined&&!isNaN(i)){if(s=t("#"+n._id+"_gradient_scale")[0],s){f="position:absolute;visibility:visible;box-sizing:border-box;";u=t("#"+n._id+"_gradient_scale_marker")[0];u||(u=document.createElement("div"),u.setAttribute("id",n._id+"_gradient_scale_marker"),u.setAttribute("class","gradient_scale_marker"),s.appendChild(u));var r=this._getBoundingClientRect(s),h=this._getBoundingClientRect(u),e=this._getScroll(),o=this.model.colorMappingCollection,c=o[o.length-1].value;f+=n.model.orientation==="horizontal"?"width: 0;height: 0;border-left:"+r.height/2+"px solid transparent;border-right: "+r.height/2+"px solid transparent;border-bottom: "+r.height+"px solid green;":"border-left: none;border-right: "+r.width+"px solid green;border-bottom: "+r.width/2+"px solid transparent;border-top: "+r.width/2+"px solid transparent;";var a=o[0].value,l=o[o.length-1].value,v=l-a;n.model.orientation==="horizontal"?(f+=this.model.enableRTL?"left:"+(Math.round(r.left+e.x-h.width/2+1)+(Number((l-i)*100/c)/100*r.width-3))+"px;":"left:"+(Math.round(r.left+e.x-h.width/2+1)+(Number(i*100/c)/100*r.width-3))+"px;",f+="top:"+(r.top+e.y+2)+"px;"):(f+="left:calc("+(r.left+e.x)+"px + 0px);",f+=this.model.enableRTL?"top: calc( "+Math.round(r.top+e.y-h.height/2+1)+"px + "+(Number((l-i)*100/c)/100*r.height-3)+"px);":"top: calc( "+Math.round(r.top+e.y-h.height/2+1)+"px + "+(Number(i*100/c)/100*r.height-3)+"px);");u.setAttribute("style",f)}}else this._hideLegendMarker()},r.prototype._getScroll=function(){if(window.pageYOffset!==undefined)return{x:pageXOffset?pageXOffset:0,y:pageYOffset?pageYOffset:0};var n=void 0,t=void 0,i=document,r=i.documentElement,u=i.body;return n=r.scrollLeft||u.scrollLeft||0,t=r.scrollTop||u.scrollTop||0,{x:n?n:0,y:t?t:0}},r.prototype._convertToRBG=function(n,t){var u=0,o,e,i,s,h,c;typeof n=="number"?u=Number(n):typeof n=="string"||n==null;var r=0,f=0;for(t=this._orderbyOffset(t),o=0;o<t.length;o++)if(e=Number(t[o].value),e!==undefined)if(u<=e){f=e;break}else f=e,r=e;return u<r||u>f,i=0,r!==undefined?(s=f-r,i=(u-r)/s,i=isNaN(i)&&u===0?0:i):r=f,i<0||i>1,h=this._getEqualColor(t,r),c=this._getEqualColor(t,f),this._getPercentageColor(i,h,c)},r.prototype._hashCode=function(n){for(var t=0,i=0;i<n.length;i++)t=n.charCodeAt(i)+((t<<5)-t);return t},r.prototype._intToRGB=function(n){var t=(n&16777215).toString(16).toUpperCase();return"00000".substring(0,6-t.length)+t},r.prototype._getPercentageColor=function(n,t,i){var e=i,u,o=t,r,f;e.split("#").length>1?u=e.split("#")[1]:(f=this._intToRGB(this._hashCode(e)),u=f);r=null;o.split("#").length>1?r=o.split("#")[1]:(f=this._intToRGB(this._hashCode(o)),r=f);var s=this._getPercentage(n,parseInt(r.substr(0,2),16),parseInt(u.substr(0,2),16)),h=this._getPercentage(n,parseInt(r.substr(2,2),16),parseInt(u.substr(2,2),16)),c=this._getPercentage(n,parseInt(r.substr(4,2),16),parseInt(u.substr(4,2),16));return{R:s,G:h,B:c}},r.prototype._getPercentage=function(n,t,i){var r=i-t;return Math.round(t+r*n)},r.prototype._getEqualColor=function(n,t){for(var r,i=0;i<n.length;i++)if(r=n[i],r&&Number(r.value)===t)return r.color;return"#00000"},r.prototype._orderbyOffset=function(n){for(var u,t,i,r=0;r<n.length-1;r++)t=n[r],i=n[r+1],t&&i&&t.value&&i.value&&t.value>i.value&&(u=t,t=i,i=u);return n},r.prototype._mergeDefaultSettings=function(n,i){var u,r;return n&&(u=n.width===undefined?!0:!1),r=t.extend(!0,{},this.model.defaultColumnStyle,n),i&&this.model.enableRTL&&(r.textAlign=!n||n&&!n.textAlign?"right":n.textAlign),u&&delete r.width,r},r.prototype._bindCellMapValues=function(){var f=[],e={prLeft:this.model.itemsMapping.row.propertyName,prTop:this.model.itemsMapping.column.propertyName,prValue:this.model.itemsMapping.value.propertyName},i=this._convertToCellBindingData(this.model.itemsSource,e),r=this.model.itemsMapping.row,o=this.model.itemsMapping.column,n=this._getCellBingingColumnData(r.propertyName,!0),u,t;for(n=this._mergeDefaultSettings(n,!1),r&&(u=!0,u=this.model.enableVirtualization?!1:u,f.push({field:r.propertyName,headerText:r.displayName,isFrozen:u,width:n&&n.width?n.width:null,textAlign:n&&n.textAlign?n.textAlign:"center",headerTemplateID:n&&n.headerTemplateID?n.headerTemplateID:"",templateID:n&&n.templateID?n.templateID:""})),t=0;t<i.items.length;t++)n=this._getCellBingingColumnData(i.items[t],!1),n=this._mergeDefaultSettings(n,!1),f.push({field:i.items[t],headerText:i.items[t],width:n&&n.width?n.width:null,textAlign:n&&n.textAlign?n.textAlign:"center",headerTemplateID:n&&n.headerTemplateID?n.headerTemplateID:"",templateID:n&&n.templateID?n.templateID:""});return{dataTableMapping:i.dataTableMapping,columns:f}},r.prototype._getCellBingingColumnData=function(n,t){var u,i,r,f;if(t){if(u=this.model.itemsMapping&&this.model.itemsMapping.headerMapping,u.propertyName===n)return this._mergeColumnStyle(u.columnStyle,!0)}else if(i=this.model.itemsMapping&&this.model.itemsMapping.columnMapping,i&&i.length>0)for(r=0;r<i.length;r++)if(f=this._mergeDefaultSettings(i[r],!1),f.propertyName===n)return this._mergeColumnStyle(f.columnStyle,!1);return null},r.prototype._mergeColumnStyle=function(n,i){return n=this._mergeDefaultSettings(n,i),t.extend(!0,{},{textAlign:n&&n.textAlign?n.textAlign:"center"},n)},r.prototype._convertToCellBindingData=function(n,t){for(var i,r,f,o,s,c,u=[],e=[],h=0;h<n.length;h++)if(i=n[h],i&&(r={},i[t.prLeft]))if(u.length===0)f=!1,r.hasOwnProperty(i[t.prLeft])||(r[t.prLeft]=i[t.prLeft],u.push(r),f=!0),f&&(r[i[t.prTop].toString()]=i[t.prValue]);else{for(f=!1,o=0;o<u.length;o++)s=u[o],s&&s[t.prLeft]===i[t.prLeft]&&(s[i[t.prTop]]=i[t.prValue],e.indexOf(i[t.prTop])===-1&&e.push(i[t.prTop].toString()),f=!0);f||(c=!1,r.hasOwnProperty(i[t.prLeft])||(r[t.prLeft]=i[t.prLeft],u.push(r),c=!0),c&&(r[i[t.prTop].toString()]=i[t.prValue],e.indexOf(i[t.prTop])===-1&&e.push(i[t.prTop].toString())))}return{dataTableMapping:u,items:e}},r}(ej.WidgetBase),i;window.ej.widget("ejHeatMap","ej.datavisualization.HeatMap",new r);ej.datavisualization.HeatMap.CellVisibility={Visible:"visible",Hidden:"hidden"};ej.datavisualization.HeatMap.Effect={Slide:"slide",Fade:"fade",None:"none"};ej.datavisualization.HeatMap.Trigger={Hover:"hover",Click:"click"};ej.datavisualization.HeatMap.Associate={MouseFollow:"mouseFollow",MouseEnter:"mouseEnter",Target:"target"};ej.datavisualization.HeatMap.Horizontal={Left:"left",Center:"center",Right:"right"};ej.datavisualization.HeatMap.Vertical={Top:"top",Center:"center",Bottom:"bottom"};ej.datavisualization.HeatMap.TextAlign={Right:"right",Left:"left",Center:"center"};ej.datavisualization.HeatMap.LegendMode={Gradient:"gradient",List:"list"};ej.datavisualization.HeatMap.LegendOrientation={Horizontal:"horizontal",Vertical:"vertical"};ej.datavisualization.HeatMap.TextDecorations={Underline:"underline",Overline:"overline",LineThrough:"line-through",None:"none"};i=function(i){function r(n,r){return i.call(this),this.defaults={colorMappingCollection:null,orientation:ej.datavisualization.HeatMap.LegendOrientation.Horizontal,showLabel:!0,legendMode:"gradient",height:null,width:null,isResponsive:!1,enableRTL:!1,create:null,destroy:null},this.validTags=["div"],this._rootCSS="e-heatmaplegend",this._id="",this.setFirst=!1,this.PluginName="ejHeatMapLegend",this.id="null",this.model=null,this.dataTypes={colorMappingCollection:"data"},this._tags=[{tag:"colorMappingCollection",attr:["value","color","label.bold","label.italic","label.text","label.textDecoration","label.fontSize","label.fontFamily","label.fontColor",],singular:"colorMapping"}],n&&(n.jquery||(n=t("#"+n)),n.length)?t(n).ejHeatMapLegend(r).data(this.PluginName):void 0}return n(r,i),r.prototype._init=function(){this._initLegendData();this._wireEvents();this._renderLegend()},r.prototype._setLabel=function(n){var t=n?n:{};return t.bold=t&&t.bold!==undefined?t.bold:!1,t.italic=t&&t.italic!==undefined?t.italic:!1,t.text=t&&t.text?t.text:"",t.textDecoration=t&&t.textDecoration?t.textDecoration:ej.datavisualization.HeatMap.TextDecorations.None,t.fontSize=t&&t.fontSize?t.fontSize:10,t.fontFamily=t&&t.fontFamily?t.fontFamily:"Arial",t.fontColor=t&&t.fontColor?t.fontColor:"black",t},r.prototype._initLegendData=function(){this.model.colorMappingCollection=this._getSortedMappingList(this.model.colorMappingCollection);for(var n=0;n<this.model.colorMappingCollection.length;n++)this.model.colorMappingCollection[n].label=this._setLabel(this.model.colorMappingCollection[n].label)},r.prototype._wireEvents=function(){this._on(t(window),"resize",this._updateLegendSize);t(this.element[0].parentNode).scroll(t.proxy(this._hideLegendMarker,this))},r.prototype._hideLegendMarker=function(){for(var i=t(".gradient_scale_marker"),n=0;n<i.length;n++)i[n]&&i[n].parentNode&&i[n].parentNode.removeChild(i[n])},r.prototype._scrollElement=function(){var n=t("#"+this._id+"_gradient_scale_marker")[0];n&&n.parentNode.removeChild(n)},r.prototype._getLargerGradientLabel=function(){for(var u,n,f,t={left:0,top:0,width:0,height:0},i=this.model.colorMappingCollection,r=0;i&&r<i.length;r++)u=i[r],n=document.createElement("span"),this.element[0].appendChild(n),this._mergeLabelProperties(n,u),u.label&&(f=this._getBoundingClientRect(n),t=this._union(t,f)),n.parentNode.removeChild(n);return t},r.prototype._getBoundingClientRect=function(n){var i=n.getBoundingClientRect();return ej.browserInfo().name==="msie"&&ej.browserInfo().version==="8.0"&&(i={left:i.left<0?0:i.left,top:i.top<0?0:i.top,right:i.right<0?0:i.right,bottom:i.bottom<0?0:i.bottom,width:t(n).outerWidth(),height:t(n).outerHeight()}),{left:i.left<0?0:i.left,top:i.top<0?0:i.top,right:i.right<0?0:i.right,bottom:i.bottom<0?0:i.bottom,width:i.width,height:i.height}},r.prototype._getSpace=function(){var t=this._getLargerGradientLabel(),n=this._getBoundingClientRect(this.element[0]);return n={bottom:n.bottom,height:n.height-(t.height/2+2),left:n.left,right:n.right,top:n.top,width:this.model.orientation===ej.datavisualization.HeatMap.LegendOrientation.Horizontal?n.width-10:n.width},this.model.legendMode===ej.datavisualization.HeatMap.LegendMode.Gradient&&(this.model.orientation===ej.datavisualization.HeatMap.LegendOrientation.Horizontal?n.width=n.width-t.width/2:n.height=n.height-t.height/2),n},r.prototype._renderLegend=function(){var n=t("#"+this.element[0].id)[0],r,u,i;n&&(t("div."+this.element[0].id).find("*").removeAttr("style"),t("#"+this.element[0].id).empty());r=this.model.height?this.model.height:"100%";u=this.model.width?this.model.width:"100%";t("#"+this.element[0].id).attr("data-role","heatmap-legend").css("height",r).css("width",u).css("overflow","hidden");i=this._getSpace(null);this.model.legendMode===ej.datavisualization.HeatMap.LegendMode.Gradient?this._renderGradient(i,n,null):this._renderList(i,n,null)},r.prototype._mergeLabelProperties=function(n,t){if(n&&t&&t.label){var i=t.label;i&&(n.innerHTML=this.model.legendMode!=="gradient"&&i.text?i.text:t.value,n.style.fontFamily=i.fontFamily,n.style.fontSize=i.fontSize+"px",n.style.fontColor=i.fontColor,n.style.textDecoration=i.textDecoration,n.style.fontWeight=i.bold?"bold":"",n.style.fontStyle=i.italic?"italic":"")}},r.prototype._union=function(n,t){var i=Math.min(n.left,t.left),r=Math.min(n.top,t.top),u=Math.max(n.width,t.width),f=Math.max(n.height,t.height);return{left:i,top:r,width:u-i,height:f-r}},r.prototype._updateLegendSize=function(n){if(this.model.isResponsive){var u=this.model.height?this.model.height:"100%",f=this.model.width?this.model.width:"100%",i=t("#"+this.element[0].id)[0],r=this._getSpace(!0);this.model.legendMode===ej.datavisualization.HeatMap.LegendMode.Gradient?this._renderGradient(r,i,n):this._renderList(r,i,n)}},r.prototype._renderListBox=function(n,i,r,u,f,e){var o=t("#"+i)[0],c,h,l,s,a;o||(o=document.createElement("div"),o.setAttribute("id",i),this.model.enableRTL?t(n).prepend(o):n.appendChild(o),o.setAttribute("style","height:auto;height:auto;float:left;vertical-align: middle;"));o.setAttribute("data-role","list");c="";h=t("#"+o.id+"_colordiv_")[0];h||(h=document.createElement("div"),h.setAttribute("id",o.id+"_colordiv_"),o.appendChild(h));c="background-color:"+r.color+";";h.setAttribute("style",c+"height:"+f+"px;width:"+u+"px;float:left");this.model.showLabel&&(l=this._getBoundingClientRect(o),s=t("#"+o.id+"_labelSpan_")[0],s||(s=document.createElement("span"),s.setAttribute("id",o.id+"_labelSpan_"),s.setAttribute("aria-label","label"),s.setAttribute("class","sf-ht-label"),s.innerHTML=r.label&&r.label.text?r.label.text:r.value,o.appendChild(s)),s.setAttribute("style","float:left;margin-left:3px;margin-right:3px;margin-right:3px;margin-bottom:3px;"),this._mergeLabelProperties(s,r),a=this._getBoundingClientRect(s),s.style.marginTop=l.height/2-a.height/2+"px");this.model.orientation===ej.datavisualization.HeatMap.LegendOrientation.Horizontal?o.style.margin="10px":(!e||this.model.enableRTL)&&(t("#"+o.id+"_colordiv_br")[0]||t(o).after("<div id="+o.id+"_colordiv_br> <\/br><\/br><\/div> "))},r.prototype._renderList=function(n,i,r){var u=t("#"+i.id+"_list")[0],e,f,o;for(u?r&&(u.innerHTML=""):(u=document.createElement("div"),u.setAttribute("id",i.id+"_list"),i.appendChild(u),u.setAttribute("style","height:auto;height:auto;float:left;")),e=this.model.colorMappingCollection,f=0;f<e.length;f++)this._renderListBox(u,i.id+"_listBox_"+f,e[f],15,15,f===e.length-1?!0:!1);var s=this._getBoundingClientRect(i),h=this._getBoundingClientRect(u),c=s.width/2-h.width/2;u.style.marginLeft=c<=0?"0px":c+"px";o=s.height/2-h.height/2;u.style.marginTop=o<=0?"0px":o+"px"},r.prototype._setAttribute=function(n,t){if(n)for(var i in t)n.setAttribute(i,t[i])},r.prototype._renderGradient=function(n,i,r){var u=t("#"+this.element[0].id+"_gradient_scale_line")[0],e,s,f,h,o;u||(u=document.createElement("div"),i.appendChild(u));e=this.model.orientation==="horizontal"?"":"float:left;";e+=this.model.orientation===ej.datavisualization.HeatMap.LegendOrientation.Horizontal?"height:"+(40/100*n.height+20/100*n.height)+"px;width:"+n.width+"px; ":"height:"+n.height+"px;width:"+(40/100*n.width+20/100*n.width)+"px;";s=this._getStartlabelOffset();e+=this.model.orientation==="horizontal"?"padding-left:"+s.width+"px;":"padding-top:"+(s.height/2-1)+"px;";u.setAttribute("style",e);this._setAttribute(u,{id:"#"+this.element[0].id+"_gradient_scale_line"});u.setAttribute("style",e);f=t("#"+this.element[0].id+"_gradient")[0];f?r&&(f.innerHTML=""):(f=document.createElement("div"),u.appendChild(f));this._setAttribute(f,{id:this.element[0].id+"_gradient","data-role":"gradient"});h="to right , ";o="float:left; ";this.model.orientation===ej.datavisualization.HeatMap.LegendOrientation.Horizontal?o+="height:"+(40/100*n.height-2)+"px; width:"+n.width+"px; ":(h="to bottom , ",o+="height:"+n.height+"px;width:"+(40/100*n.width-2)+"px;");f.setAttribute("style",o);this._renderGradientElements(f);this._renderGradientScale(n,u,r);this._renderGradientScaleValue(n,i,r)},r.prototype._renderGradientElements=function(n){var r=this.model.colorMappingCollection,w=Number(r[0].value),e=r.length,h=Number(r[r.length-1].value),i,a,s,v,y;if(e>0){var c=0,l=0,o=this._getBoundingClientRect(n);for(i=1;r&&i<e;i++){var u="float:left;border:none;",f=t("#"+this.element[0].id+"_inner_gradient"+i)[0],p=this._childElementsBounds(n).width?this._childElementsBounds(n).width:0;f||(f=document.createElement("div"),this.model.enableRTL?t(n).prepend(f):n.appendChild(f));this.model.orientation==="horizontal"?(a=r[i].value*100/h,s=o.width*(a/100)-c,i===e-1&&(s=o.width-p-1),u+="height:100%;width:"+s+"px;",u+=this.model.enableRTL?ej.browserInfo().name==="msie"&&ej.browserInfo().version==="8.0"||ej.browserInfo().version==="9.0"?"filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="+r[i].color+", endColorstr="+r[i-1].color+",GradientType=1 );":"background:linear-gradient(to right ,"+r[i].color+", "+r[i-1].color+");":ej.browserInfo().name==="msie"&&ej.browserInfo().version==="8.0"||ej.browserInfo().version==="9.0"?"filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="+r[i-1].color+", endColorstr="+r[i].color+",GradientType=1 );":"background:linear-gradient(to right ,"+r[i-1].color+", "+r[i].color+");"):(v=r[i].value*100/h,y=o.height*(v/100)-l,u+="width:100%;height:"+y+"px;",u+=this.model.enableRTL?ej.browserInfo().name==="msie"&&ej.browserInfo().version==="8.0"||ej.browserInfo().version==="9.0"?"filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="+r[i].color+", endColorstr="+r[i-1].color+",GradientType=0 );":"background:linear-gradient(to bottom ,"+r[i].color+", "+r[i-1].color+");":ej.browserInfo().name==="msie"&&ej.browserInfo().version==="8.0"||ej.browserInfo().version==="9.0"?"filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="+r[i-1].color+", endColorstr="+r[i].color+",GradientType=0 );":"background:linear-gradient(to bottom ,"+r[i-1].color+", "+r[i].color+");");this._setAttribute(f,{id:this.element[0].id+"_inner_gradient"+i,style:u});c=this._childElementsBounds(n).width;l=this._childElementsBounds(n).height}}},r.prototype._getStartlabelOffset=function(){var n=document.createElement("span"),t,i;return this.element[0].appendChild(n),t=this.model.colorMappingCollection[0],this._mergeLabelProperties(n,t),i=null,t.label&&(i=this._getBoundingClientRect(n)),n.parentNode.removeChild(n),i},r.prototype._renderGradientScaleValue=function(n,i,r){var f=t("#"+this.element[0].id+"_gradient_scale_value")[0],s,u;f?r&&(f.innerHTML=""):(f=document.createElement("div"),i.appendChild(f));this._setAttribute(f,{id:this.element[0].id+"_gradient_scale_value"});s="float:left;";s+=this.model.orientation===ej.datavisualization.HeatMap.LegendOrientation.Horizontal?"width: 100%;":"height:100%;width:12px;margin-left:2px;";f.setAttribute("style",s);var e=this.model.colorMappingCollection,c=this._getBoundingClientRect(f),o=null,h=this._getStartlabelOffset();if(this.model.enableRTL)for(u=e.length-1;u>=0;u--)o=this._createGradientLabel(n,e[u],f,this.model.orientation===ej.datavisualization.HeatMap.LegendOrientation.Horizontal?!0:!1,u,o,u===0?h.width/2:0,0);else for(u=0;u<e.length;u++)o=this._createGradientLabel(n,e[u],f,this.model.orientation===ej.datavisualization.HeatMap.LegendOrientation.Horizontal?!0:!1,u,o,u===0?h.width/2:0,0)},r.prototype._createGradientLabel=function(n,i,r,u,f,e,o,s){var c,h,l,v;if(this.model.showLabel){c=this._getBoundingClientRect(r);h=t("#"+this.element[0].id+"_gradient_scale_value"+i.value)[0];h||(h=document.createElement("span"),r.appendChild(h));h.setAttribute("id",this.element[0].id+"_gradient_scale_value"+i.value);l="float:left;";l+="height:auto;";l+="width:auto;";h.setAttribute("style",l);h.innerHTML=i.value;this._mergeLabelProperties(h,i);var d=this._getBoundingClientRect(h),a=this.model.colorMappingCollection,nt=a[a.length-1].value,tt=this._getScroll(),g=t("#"+this.element[0].id+"_gradient")[0],k=a[a.length-1].value-a[0].value,y=+k,p=a[0].value,w=0,b=0;return g&&(c=this._getBoundingClientRect(g)),u?(w=this.model.enableRTL?Math.round(u?(k-(i.value-p))/y*c.width:0):Math.round(u?(i.value-p)/y*c.width:0),l+="position:absolute;left:"+w+"px;margin-left:"+(c.left-d.width/2)+"px;"):(b=this.model.enableRTL?Math.round((k-(i.value-p))/y*c.height):Math.round((i.value-p)/y*c.height),l+="position:absolute;top:"+b+"px;margin-top:"+(c.top-d.height/2)+"px;"),u?b=0:w=0,h.setAttribute("style",l),h.setAttribute("aria-label","label"),h.setAttribute("class","sf-ht-label"),this._mergeLabelProperties(h,i),v=this._getBoundingClientRect(h),{x:w+v.width+(o?v.width/2:0),y:b+v.height-(f===0?0:0)+(s?v.height/2:0)}}},r.prototype._renderGradientScale=function(n,i,r){var f=t("#"+this.element[0].id+"_gradient_scale")[0],s,o,u;if(f?r&&(f.innerHTML=""):(f=document.createElement("div"),i.appendChild(f)),this._setAttribute(f,{id:this.element[0].id+"_gradient_scale"}),s="float:left;",s+=this.model.orientation===ej.datavisualization.HeatMap.LegendOrientation.Horizontal?"height:"+20/100*n.height+"px; width:"+n.width+"px; margin-top:2px;":"height:"+n.height+"px;width:"+20/100*n.width+"px; margin-left:2px",f.setAttribute("style",s),this.model.legendMode===ej.datavisualization.HeatMap.LegendMode.Gradient){var e=this.model.colorMappingCollection,h=this._getBoundingClientRect(f),c=e[e.length-1].value;if(this.model.orientation===ej.datavisualization.HeatMap.LegendOrientation.Horizontal)for(o=this._createValueLine(this.element[0].id+"_gradient_scale_first",!0,n,"",f),u=1;u<e.length;u++)o+=this._createSpaceLine(this.element[0].id+u+"space",!0,n,"",f,o,e[u],u,e[u-1]),o+=this._createValueLine(this.element[0].id+u+"value",!0,n,"",f);else for(o=this._createValueLine(this.element[0].id+"_gradient_scale_first",!1,n,"",f),u=1;u<e.length;u++)o+=this._createSpaceLine(this.element[0].id+u+"space",!1,n,"",f,o,e[u],u,e[u-1]),o+=this._createValueLine(this.element[0].id+u+"value",!1,n,"",f)}},r.prototype._childElementsBounds=function(n){var i=n.childNodes,u={left:0,top:0,right:0,bottom:0,width:0,height:0},r,t;if(i.length>0)for(t=0;t<i.length;t++)i[t]&&(r=this._getBoundingClientRect(i[t]),r&&(u.width+=r.width,u.height+=r.height));return u},r.prototype._createSpaceLine=function(n,i,r,u,f,e,o,s,h){var p=this._getBoundingClientRect(f),d=this._childElementsBounds(f),c=t("#"+n)[0],a,v,y;c||(c=document.createElement("div"),c.setAttribute("id",n),this.model.enableRTL?t(f).prepend(c):f.appendChild(c));a="float:left;border: 1px solid gray; ";var l=this.model.colorMappingCollection,k=l[l.length-1].value-l[0].value,w=+k,b=i?(o.value-h.value)/w*p.width:0;return b-=s===1||s===l.length-1?6:4,v=Math.round(i?0:(o.value-h.value)/w*p.height),v-=s===1||s===l.length-1?6:4,a+="height:"+Math.floor(v)+"px;",a+="width:"+Math.floor(b)+"px;",c.setAttribute("style",a),y=this._getBoundingClientRect(c),i?y.width:y.height},r.prototype._createValueLine=function(n,i,r,u,f){var h=this._getBoundingClientRect(f),e=t("#"+n)[0],s;e||(e=document.createElement("div"),e.setAttribute("id",n),this.model.enableRTL?t(f).prepend(e):f.appendChild(e));var o="float:left;",c=i?0:h.width,l=i?h.height:0;return o+="height:"+l+"px;",o+="width:"+c+"px;",o+="border: 1px solid gray; ",u&&(o+=u),e.setAttribute("style",o),s=this._getBoundingClientRect(e),i?s.width:s.height},r.prototype._getScroll=function(){if(window.pageYOffset!==undefined)return{x:pageXOffset?pageXOffset:0,y:pageYOffset?pageYOffset:0};var n=void 0,t=void 0,i=document,r=i.documentElement,u=i.body;return n=r.scrollLeft||u.scrollLeft||0,t=r.scrollTop||u.scrollTop||0,{x:n?n:0,y:t?t:0}},r.prototype._getSortedMappingList=function(n){var t,i,r;if(n){for(t=0;t<n.length;t++)for(i=t+1;i<n.length;i++)Number(n[t].value)>Number(n[i].value)&&(r=n[t],n[t]=n[i],n[i]=r);return n}},r.prototype._setModel=function(n){var i,t;for(i in n)switch(i){case"colorMappingCollection":for(this.model.colorMappingCollection=n.colorMappingCollection?n.colorMappingCollection:this.model.colorMappingCollection,this.model.colorMappingCollection=this._getSortedMappingList(this.model.colorMappingCollection),t=0;t<this.model.colorMappingCollection.length;t++)this.model.colorMappingCollection[t].label=this._setLabel(this.model.colorMappingCollection[t].label);this._updateLegendSize(!0);break;case"legendMode":this._renderLegend()}},r.prototype._destroy=function(){this.element.removeClass("e-heatmaplegend e-js").empty()},r}(ej.WidgetBase);window.ej.widget("ejHeatMapLegend","ej.datavisualization.HeatMapLegend",new i)})(jQuery)});
