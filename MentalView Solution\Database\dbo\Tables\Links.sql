﻿CREATE TABLE [dbo].[Links] (
    [LinkId]      BIGINT         IDENTITY (1, 1) NOT NULL,
    [TenantId]    BIGINT         NOT NULL,
    [Description] NVARCHAR (250) CONSTRAINT [DF_Links_Description] DEFAULT ('') NOT NULL,
    [CreateDate]  DATETIME       CONSTRAINT [DF_Links_CreateDate] DEFAULT (getdate()) NOT NULL,
    [Link]        NVARCHAR (MAX) CONSTRAINT [DF_Links_Link] DEFAULT ('') NOT NULL,
    CONSTRAINT [PK_Links] PRIMARY KEY CLUSTERED ([LinkId] ASC),
    CONSTRAINT [FK_Links_Tenants] FOREIGN KEY ([TenantId]) REFERENCES [dbo].[Tenants] ([TenantId]) ON DELETE CASCADE ON UPDATE CASCADE
);

