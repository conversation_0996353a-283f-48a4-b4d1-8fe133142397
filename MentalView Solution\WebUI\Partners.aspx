﻿<%@ Page Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Partners.aspx.cs" Inherits="WebUI.Partners" Culture="auto" meta:resourcekey="Page" UICulture="auto" %>

<asp:Content ID="mainHeadContent" ContentPlaceHolderID="mainHead" runat="server">
    <script src="LocalizedResources/ej.culture.en-US.min.js"></script>
    <script type="text/javascript">
        function Initialization() {
            SetLocalization();

            $('#filterTxtBox').keydown(function (e) {
                if (e.keyCode == 13) {
                    e.preventDefault();
                    javascript: __doPostBack('searchBtn', '');
                }
            });
        }
    </script>

    <script type="text/x-template" id="activeColumnTemplate">
        {{if Active}}
            <i class="fa fa-check"/>
        {{else}}
            
        {{/if}}
    </script>

    <script type="text/x-template" id="waitingColumnTemplate">
        {{if Waiting}}
            <i class="fa fa-check"/>
        {{else}}
            
        {{/if}}
    </script>
</asp:Content>
<asp:Content ID="mainBodyContent" ContentPlaceHolderID="mainBody" runat="server">
    <%--<div class="row">
        <div class="col-xs-12">
            <a id="newContactBtn" href="Contact.aspx" runat="server" type="button" class="btn btn-primary btn-flat margin-bottom margin-r-5">
                <asp:Literal meta:resourcekey="newContactBtn" runat="server"></asp:Literal></a>
        </div>
    </div>--%>
    <%--    <asp:UpdatePanel ID="contactsUpdatePanel" runat="server">
        <ContentTemplate>--%>
    <%-- <div class="row">
        <div class="col-xs-12 ">
            <div class="input-group input-group-sm margin-bottom pull-right" style="min-width: 200px; max-width: 250px;">
                <input type="text" runat="server" id="filterTxtBox" class="form-control" maxlength="30">
                <span class="input-group-btn">
                    <button type="button" id="clearSearchBtn" runat="server" class="btn btn-default btn-flat" style="border-radius: unset !important" onserverclick="clearSearchBtn_ServerClick">
                        <i class="fa fa-remove"></i>
                    </button>
                </span>
                <span class="input-group-btn">
                    <button type="button" id="searchBtn" runat="server" class="btn btn-info btn-flat" onserverclick="searchBtn_ServerClick">
                        <i class="fa fa-search  hidden-md hidden-lg"></i>
                        <asp:Label meta:resourcekey="searchBtn" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></button>
                </span>
            </div>
        </div>
    </div>--%>
    <div class="row">
        <div class="col-xs-12">
            <asp:UpdatePanel ID="UpdatePanel1" runat="server" ChildrenAsTriggers="true">
                <ContentTemplate>
                    <ej:Grid ID="partnersGrid" runat="server" Selectiontype="Single" AllowFiltering="true" EnablePersistence="false" AllowPaging="true" Locale="el-GR" AllowSorting="true" OnServerAddRow="partnersGrid_ServerAddRow" OnServerEditRow="partnersGrid_ServerEditRow" OnServerDeleteRow="partnersGrid_ServerDeleteRow">
                        <EditSettings AllowEditing="true" AllowAdding="true" AllowDeleting="true" EditMode="Normal"></EditSettings>
                        <ToolbarSettings ShowToolbar="true" ToolbarItems="add,edit,delete,update,cancel"></ToolbarSettings>
                        <Columns>
                            <ej:Column Field="PartnerId" IsPrimaryKey="true" Type="string" Visible="false" />
                            <ej:Column Field="FirstName" HeaderText="Όνομα" Type="string" EditType="StringEdit">
                                
                            </ej:Column>
                            <ej:Column Field="LastName" HeaderText="Επώνυμο" Type="string" EditType="StringEdit" />
                            <ej:Column Field="Email1" HeaderText="Email" Type="string" EditType="StringEdit" />
                            <ej:Column Field="Phone1" HeaderText="Τηλέφωνο" Type="string" EditType="StringEdit" />
                            <ej:Column Field="Mobile1" HeaderText="Κινητό" Type="string" EditType="StringEdit" />
                            <ej:Column Field="Address" HeaderText="Διεύθυνση" Type="string" EditType="StringEdit" />
                            <ej:Column Field="City" HeaderText="Πόλη" Type="string" EditType="StringEdit" />
                            <ej:Column Field="PostCode" HeaderText="Ταχ. Κωδικός" Type="string" EditType="StringEdit" />
                            <ej:Column Field="CompanyName" HeaderText="Εταιρία" Type="string" EditType="StringEdit" />
                        </Columns>
                    </ej:Grid>
                </ContentTemplate>
            </asp:UpdatePanel>
        </div>


    </div>
    <%-- </ContentTemplate>
    </asp:UpdatePanel>--%>
</asp:Content>
