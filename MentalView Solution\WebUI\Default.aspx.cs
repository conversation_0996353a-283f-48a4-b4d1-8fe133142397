﻿using Data;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebUI
{
    public partial class Default : System.Web.UI.Page
    {
        protected async void Page_Load(object sender, EventArgs e)
        {
            try
            {
                ((Main)this.Master).PageTitle = GetLocalResourceObject("Page.Title").ToString();

                //await ChatGPT();

                if (this.Page.User.Identity.IsAuthenticated == true)
                {
                    Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                    if (userData != null)
                    {
                        Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                        Int64 loggedUserId = Convert.ToInt64(userData["UserId"]);
                        string roleName = userData["Role"].ToString();
                        if (roleName == "Guest")
                        {
                            this.calendarDiv.Visible = false;
                        }
                        DashboardInfo info = Data.Business.GeneralBusiness.GetDashboardInfo(tenantId, loggedUserId);

                        this.contactsCountLbl.Text = info.ContactsCount.ToString();
                        this.futureTasksCountLbl.Text = info.FutureUserAppointmentsCount.ToString();
                        this.waitingContactsCountLbl.Text = info.WaitingContactsCount.ToString();

                        FillappointmentsGrid();
                    }
                    else
                    {
                        FormsAuthentication.SignOut();
                        Response.Redirect("~/Login.aspx");
                    }
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }



        private void Button2_Click(object sender, EventArgs e)
        {
            try
            {
                //RegisterAsyncTask(new PageAsyncTask(ChatGPT));
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void FillappointmentsGrid()
        {
            Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
            Int64 tenantId = Convert.ToInt64(userData["TenantId"]);


            MentalViewDataSet ds = Data.Business.AppointmentsBusiness.GetAppointmentsWithImportantNotes(tenantId);

            this.importanNotesGrid.DataSource = ds.Appointments;
            this.importanNotesGrid.DataBind();


        }


        protected void importanNotesGrid_ServerRecordDoubleClick(object sender, Syncfusion.JavaScript.Web.GridEventArgs e)
        {
            try
            {
                if (e.EventType == "recordDoubleClick")
                {
                    string appointmentId = ((Dictionary<string, object>)e.Arguments["data"])["AppointmentId"].ToString();
                    Response.Redirect("~/Appointment.aspx?AppointmentId=" + appointmentId, true);

                }

               // this.FillEmailTemplatesGrid();
            }
            catch (ThreadAbortException exp)
            {
                //do nothing or handle as required
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void importanNotesGrid_ServerCommandButtonClick(object sender, Syncfusion.JavaScript.Web.GridEventArgs e)
        {
            try
            {
                if (e.EventType == "commandButtonClick")
                {
                    if (e.Arguments["commandType"].ToString() == "Edit")
                    {
                        string appointmentId = ((Dictionary<string, object>)e.Arguments["data"])["AppointmentId"].ToString();
                        Response.Redirect("~/Appointment.aspx?AppointmentId=" + appointmentId);
                    }
                    else if (e.Arguments["commandType"].ToString() == "Delete")
                    {
                        string appointmentId = ((Dictionary<string, object>)e.Arguments["data"])["AppointmentId"].ToString();
                        ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete", appointmentId);
                    }
                }
            }
            catch (ThreadAbortException exp)
            {
                //do nothing or handle as required
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }
    }
}
