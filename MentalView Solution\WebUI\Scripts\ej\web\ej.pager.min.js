/*!
*  filename: ej.pager.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function(n,t,i){t.widget("ejPager","ej.Pager",{_rootCSS:"e-pager",validTags:["div"],defaults:{pageSize:12,pageSizeList:null,pageCount:10,currentPage:1,enableExternalMessage:!1,externalMessage:"",pageSizeMessage:"",enableQueryString:!1,locale:"en-US",masterObject:null,pageSizeSelected:null,enableRTL:!1,totalRecordsCount:null,totalPages:null,customText:"",showPageInfo:!0,cssClass:"",enabled:!0,showGoToPage:!1,isResponsive:!1,change:null,click:null,template:""},_init:function(){this._index=-1;this.element.attr("role","navigation");this.element.attr("tabindex","0");this.element.attr("aria-label","pager");this._initPrivateProperties();this.model.enableQueryString&&this._queryStringValue();this.renderPager();this.model.isResponsive&&this._reSizeHandler();this._wireResizing();this._wireEvents();this.refreshPager()},_initPrivateProperties:function(){typeof this.model.pageSizeList=="string"&&(this.model.pageSizeList=JSON.parse(this.model.pageSizeList));this._pageSize=this.model.pageSize;this._links=[];this._$prev=null;this._$first=null;this._$PP=null;this._$NP=null;this._lastNP=!1;this._lastpageCount=null;this._$last=null;this._$next=null;this._prevPageNo=1;this.localizedLabels=this._getLocalizedLabels();this._intervalWid=0;this._msgWidth=0;this._gotoWid=0;this._temp=this.model.template},_wireEvents:function(){var t=this;this._on(this.element,"click",this._pagerClickHandler);n(document).on("click",n.proxy(t._hidedrop,t));if(this._pagerContainer){this._touchPrev=!1;this._pagerContainer.on("touchstart",this._touchHandler);this._pagerContainer.on("keydown",this._pagerClickHandler.bind(this));this._pagerContainer.on("mouseover",this._mouseOverHandler);this._pagerContainer.on("mouseout",this._mouseOutHandler)}},_touchHandler:function(){this._touchPrev=!0},_mouseOverHandler:function(t){if(this._touchPrev==!1){var i=n(t.target);(i.hasClass("e-icon")||i.hasClass("e-link"))&&!i.hasClass("e-hover")&&i.addClass("e-hover")}this._touchPrev=!1},_mouseOutHandler:function(t){var i=n(t.target);(i.hasClass("e-icon")||i.hasClass("e-link"))&&i.hasClass("e-hover")&&i.removeClass("e-hover")},_hidedrop:function(t){n(t.target.parentElement).hasClass("e-drpdwndiv")||n(t.target).hasClass("e-drpdwndiv")||this.$dropItem&&this.$dropItem.css("display")!="none"&&this.$dropItem.hide()},_wireResizing:function(){this._refreshDropandTextItems();n(window).bind("resize",this.model.isResponsive?n.proxy(this._reSizeHandler,this):n.proxy(this._unWireResizing,this))},_unWireResizing:function(){this._refreshDropandTextItems();this.$dropItem&&this.$dropItem.css("display")!="none"&&this.$dropItem.hide();n(window).unbind("resize",n.proxy(this._reSizeHandler,this))},_reSizeHandler:function(){this.$dropItem&&this.$dropItem.css("display")!="none"&&this.$dropItem.hide();var t=this._intervalWid+this._gotoWid+this.element.find(".e-pagercontainer").outerWidth()+this._msgWidth;t>this.element.outerWidth()-20?this._msgWidth>0&&this.element.find(".e-parentmsgbar").addClass("e-msg-res"):this._msgWidth>0&&this.element.find(".e-parentmsgbar").removeClass("e-msg-res");this.element.outerWidth()-n(this.element.find(".e-pagercontainer")).outerWidth()<40&&(this._flag=!0,this._fillScreen());this.element.outerWidth()-n(this.element.find(".e-pagercontainer")).outerWidth()>40&&(!n(this._templateElement).find(".e-textbox-paging").length>0&&(this._maxPageCount||(this._maxPageCount=this.model.pageCount)),this.option("pageCount",this._maxPageCount),this.element.outerWidth()-n(this.element.find(".e-pagercontainer")).outerWidth()<40&&(this._flag=!0,this._fillScreen()));this._refreshDropandTextItems()},_refreshDropandTextItems:function(){this.numTextbox&&!this.model.template&&(this.element.find(".e-pagercontainer").position().top!=this.element.find(".e-parentmsgbar").position().top?this.numTextbox.addClass("e-pager-goto-res"):this.numTextbox.removeClass("e-pager-goto-res"));this.pageInterval_wrap&&!this.model.template&&(this.element.find(".e-pagercontainer").position().top!=this.element.find(".e-pager-itemsinterval").position().top?this.element.find(".e-pager-itemsinterval").addClass("e-pager-goto-res"):this.element.find(".e-pager-itemsinterval").removeClass("e-pager-goto-res"))},renderPager:function(){var e,r,i,u,o;if(this.model.template){if(this._templateElement=t.buildTag("div.e-template","",{}),e=this.model.template.startsWith(".")||this.model.template.startsWith("#")?n(n(this.model.template).html()):n(this.model.template),e.appendTo(this._templateElement),n(this._templateElement).find(".e-default-paging").length>0&&(r=t.buildTag("div.e-pagercontainer","",{}),this._renderPagerContainer(r),r.appendTo(n(this._templateElement).find(".e-default-paging"))),n(this._templateElement).find(".e-textbox-paging").length>0){i=this;u=n(this._templateElement).find(".e-textbox-paging");u.addClass("e-pagercontainer");var s=t.buildTag("div.e-firstpage e-icon e-mediaback  e-firstpagedisabled e-disable","",{},{title:i.localizedLabels.firstPageTooltip,"aria-label":i.localizedLabels.firstPageTooltip,role:"link"}),h=t.buildTag("div.e-prevpage e-icon e-arrowheadleft-2x  e-prevpagedisabled e-disable","",{},{title:i.localizedLabels.previousPageTooltip,"aria-label":i.localizedLabels.previousPageTooltip,role:"link"}),f=t.buildTag("input.e-gototextbox",{},{type:"textbox"}),c=t.buildTag("div.e-nextpage e-icon e-arrowheadright-2x  e-default","",{},{title:i.localizedLabels.nextPageTooltip,"aria-label":i.localizedLabels.nextPageTooltip,role:"link"}),l=t.buildTag("div.e-lastpage e-icon e-mediaforward  e-default","",{},{title:i.localizedLabels.lastPageTooltip,"aria-label":i.localizedLabels.lastPageTooltip,role:"link"}),a=t.buildTag("div.e-newrecord e-icon e-plus e-default","",{},{title:"Add New Record","aria-label":"Add New Record",role:"button"});u.append(s);u.append(h);u.append(f);u.append(c);u.append(l);u.append(a);f.focus(function(){f.val(i.model.currentPage)})}o=this.element;n(this._templateElement).addClass("e-template");this._pagerContainer=r=n(this._templateElement).find(".e-pagercontainer")?n(this._templateElement).find(".e-pagercontainer"):n(this._templateElement);o.append(n(this._templateElement));n(this._templateElement).find(".e-prevpage").length>0&&(this._$prev=n(this._templateElement).find(".e-prevpage"));n(this._templateElement).find(".e-nextpage").length>0&&(this._$next=n(this._templateElement).find(".e-nextpage"));n(this._templateElement).find(".e-firstpage").length>0&&(this._$first=n(this._templateElement).find(".e-firstpage"));n(this._templateElement).find(".e-lastpage").length>0&&(this._$last=n(this._templateElement).find(".e-lastpage"));n(this._templateElement).find(".e-gototextbox").length>0&&(this.numTextbox=n(this._templateElement).find(".e-gototextbox").addClass("e-pager-goto-res e-textbox").attr("type","textbox"),!n(this._templateElement).find(".e-textbox-paging").length>0&&this.numTextbox.width(35),this._on(n(this.numTextbox),"keydown",this._mouseScroll),this._on(this.numTextbox,"focusout",this._onTextboxBlur));this._$prev&&this._$prev.hasClass("e-prevpagedisabled")&&this._$prev.removeClass("e-prevpage");this._$last&&this._$last.hasClass("e-lastpagedisabled")&&this._$prev.removeClass("e-lastpage");this._$first&&this._$first.hasClass("e-firstpagedisabled")&&this._$prev.removeClass("e-firstpage");this._$next&&this._$next.hasClass("e-nextpagedisabled")&&this._$prev.removeClass("e-nextpage");n(this._templateElement).find(".e-drpdwndiv").length>0&&this.model.pageSizeList&&this._renderDropdownlist();this._templatePageCount=n(this._templateElement).find(".e-pagenumbers").length;this._templatePageCount>1&&(i=this,n(this._templateElement).find(".e-previouspager").length>0&&(this._$PP=n(this._templateElement).find(".e-previouspager").addClass("e-nextprevitemdisabled e-disable e-spacing e-PP")),n(this._templateElement).find(".e-nextpager").length>0&&(this._$NP=n(this._templateElement).find(".e-nextpager").addClass("e-NP e-spacing e-nextprevitemdisabled e-disable")),n(this._templateElement).find(".e-pagenumbers").each(function(t){n(this).attr("role","link").addClass("e-numericitem e-spacing e-default").data("index",t)}),i._links=n(this._templateElement).find(".e-numericitem[role=link]"))}else r=t.buildTag("div.e-pagercontainer","",{}),this._pagerContainer=r,this._renderPagerContainer(r),this.element[0].appendChild(r[0]);this.model.enabled||this._disable();this.model.pageSizeList&&this.model.pageSizeList.length>0&&this._renderDropdownlist();this._pageInfo();this.model.enableExternalMessage&&this._renderPagerMessage();this.model.showGoToPage&&this._renderTextboxItem();this.model.enableRTL&&this.element.addClass("e-rtl");this._cssClass=this.model.cssClass;this.element.addClass(this.model.cssClass)},_onTextboxBlur:function(t){var i=this,r,u;if(t.currentTarget.value=parseInt(t.currentTarget.value),r=/^[0-9]*$/,u=r.test(parseInt(t.currentTarget.value)),!u)return i.numTextbox.val(i.model.currentPage),!1;i.model.currentPage!=parseInt(t.currentTarget.value)&&(parseInt(t.currentTarget.value)>=1&&parseInt(t.currentTarget.value)<=i.model.totalPages?(i.model.currentPage=parseInt(t.currentTarget.value),i.refreshPager(),i._prevPageNo!=i.model.currentPage&&i._trigger("change",{currentPage:i.model.currentPage,isInteraction:!0,event:t})):i.numTextbox.val(this.model.currentPage));n(this._templateElement).find(".e-textbox-paging").length>0&&this.numTextbox.val(this.model.currentPage+" of "+this.model.totalPages)},_removeDropdownlist:function(){this.pageInterval_wrap.remove();this.pageInterval_wrap=null;this.$textspan=null;this.$dropItem=null},_renderDropdownlist:function(){var i=this,r,u;this.pageInterval_wrap=t.buildTag("div.e-pager-itemsinterval","",{},{tabindex:"0","aria-label":"PageSizeList Dropdown",role:"listbox","aria-expanded":!1,"aria-haspopup":!0,"aria-owns":i.element[0].id+"_pagelist"});r=this.model.template&&n(this._templateElement).find(".e-drpdwndiv").length>0?n(this._templateElement).find(".e-drpdwndiv").addClass("e-icon e-arrow-sans-down"):t.buildTag("div.e-drpdwndiv e-icon e-arrow-sans-down","",{});(i.model.totalRecordsCount==null||i.model.totalRecordsCount==0)&&r.addClass("e-disable");this.$textspan=t.buildTag("span.e-text");this.sizeIntervals=this.model.pageSizeList;i.model.template&&(u=n(i._templateElement).find(".e-drpdwndiv").prev().length>0?n(i._templateElement).find(".e-drpdwndiv").prev():n(i._templateElement));r.appendTo(this.pageInterval_wrap);i.model.template?u.after(this.pageInterval_wrap):this.pageInterval_wrap.appendTo(this.element);this.pageInterval_wrap.insertBefore(this._parentMsgBar);this.$textspan.appendTo(r);i.$textspan.text(this.model.pageSize||this.model.pageSizeList[0]);this.$dropItem=t.buildTag("ul.e-drpdwn e-ul","",{},{role:"list",id:i.element[0].id+"_pagelist",tabindex:"-1"});this._renderItems();this.$dropItem.appendTo(r);this._renderPageSizeMessage();r.on("click",function(){i._showPageDropdown(i,r)});i.pageInterval_wrap.on("keydown",function(t){t.altKey&&t.keyCode==40?i._showPageDropdown(i,r):t.altKey||t.keyCode!=40||i.$dropItem.css("display")=="none"?t.altKey||t.keyCode!=38||i.$dropItem.css("display")=="none"?t.altKey||t.keyCode!=13||i.$dropItem.css("display")=="none"||(n(i.$dropItem).find("li.e-active").removeClass("e-active"),n(i.$dropItem.find("li")[i._index]).addClass("e-active").removeClass("e-hover"),i._pageSize=parseInt(n(i.$dropItem.find("li")[i._index]).text()),i.$textspan.text(n(i.$dropItem.find("li")[i._index]).text()),i._updatedFromDropDown(i,t),i.$dropItem.hide(),i.pageInterval_wrap.focus()):(i._index=i._index-1,i._index<0&&(i._index=0),n(i.$dropItem).find("li.e-hover").removeClass("e-hover"),n(i.$dropItem.find("li")[i._index]).addClass("e-hover"),n(i.$dropItem.find("li")[i._index]).focus()):(i._index=i._index+1,i._index>=n(i.$dropItem.find("li")).length&&(i._index=n(i.$dropItem.find("li")).length-1),n(i.$dropItem).find("li.e-hover").removeClass("e-hover"),n(i.$dropItem.find("li")[i._index]).addClass("e-hover"),n(i.$dropItem.find("li")[i._index]).focus())});this.$dropItem.hide();this._intervalWid=this.element.find(".e-pager-itemsinterval").outerWidth()},_getOffset:function(n){return t.util.getOffset(n)},_showPageDropdown:function(n,t){n.$dropItem.css("display")!="none"?(n.$dropItem.hide(),this.pageInterval_wrap.attr("aria-expanded",!1)):n.model.enabled&&!t.hasClass("e-disable")&&(this.pageInterval_wrap.attr("aria-expanded",!0),n.$dropItem.show(),n._setListPosition())},_getZindexPartial:function(){return t.util.getZindexPartial(this.pageInterval_wrap,this.$dropItem)},_setListPosition:function(){var t=this.pageInterval_wrap,r=this._getOffset(t),u=n(document).scrollTop()+n(window).height()-(r.top+n(t).outerHeight()),f=r.top-n(document).scrollTop(),i=this.$dropItem.outerHeight(),c=this.$dropItem.outerWidth(),e=t.outerHeight(),o=e-t.height(),s=this._getZindexPartial(),h=i<u||i>f?this.$dropItem.position().top:-(i+o+3);this.$dropItem.css({top:h+"px","z-index":s})},_renderPageSizeMessage:function(){this.pageInterval_wrap.children().hasClass("e-interval-msg")||(this._msgwrapper=t.buildTag("div.e-interval-msg"));this._msgwrapper.appendTo(this.pageInterval_wrap);this.model.pageSizeMessage.toString().length?(this._msgwrapper.html(this.model.pageSizeMessage),this._msgwrapper.css("display")=="none"&&this._msgwrapper.show()):this._msgwrapper.remove()},_renderItems:function(){var i=this;n(this.model.pageSizeList).each(function(){var n=t.buildTag("li");n.text(this);n.attr("aria-label",this.toString());n.attr("role","listitem");n.attr("tabindex","-1");i.$dropItem.append(n);i.model.pageSize==this&&n.addClass("e-active")});this.$dropItem.find("li").on("click",function(t){i.$dropItem.find("li.e-active").removeClass("e-active");n(this).addClass("e-active");i._pageSize=parseInt(n(this).text());i.$textspan.text(n(this).text());i._updatedFromDropDown(i,t)})},_updatedFromDropDown:function(n,t){n.refreshPager();n._trigger("pageSizeSelected",{pageSize:n._pageSize});n._prevPageNo!=n.model.currentPage&&n._trigger("change",{currentPage:n.model.currentPage,isInteraction:!0,event:t})},_queryStringValue:function(){var n=new RegExp("[\\?&]page=([^&#]*)").exec(window.location.href);this.model.currentPage=n?parseInt(n[1]||1):1},_renderPagerMessage:function(){this.element.find("e-pagermessage").length<=1&&(this._messageDiv=t.buildTag("div.e-pagermessage"));this.model.externalMessage.toString().length?(this._messageDiv.html(this.model.externalMessage),this._messageDiv.css("display")=="none"&&this._messageDiv.show()):this._messageDiv.hide();this.element.append(this._messageDiv)},_removePagerMessage:function(){this.element.find("div.e-pagermessage").remove()},_mouseScroll:function(n){if(this.model.enabled)n.keyCode=="38"?this._updateField("increment"):n.keyCode=="40"?this._updateField("decrement"):n.keyCode=="13"&&this._onTextboxBlur(n)},_updateField:function(n){var u=this.model.currentPage,r=1,i;this.numTextbox.val()==""?this.numTextbox.val(1):this.numTextbox.val().indexOf(" ")>=1&&this.numTextbox.val(this.numTextbox.val().replace(" ",""));this.model.currentPage=t.parseInt(this.numTextbox.val(),this.model.locale);isNaN(this.model.currentPage)&&!this.model.currentPage&&(this.model.currentPage=1);this.model.currentPage>=1&&this.model.currentPage>this.model.totalPages?(this.model.currentPage=this.model.totalPages,this.numTextbox.val(this.model.currentPage)):this.model.currentPage<1&&this.model.currentPage<=this.model.totalPages?(this.model.currentPage=1,this.numTextbox.val(this.model.currentPage)):this.model.currentPage>=1&&this.model.currentPage<=this.model.totalPages&&(i=n=="increment"?this.model.currentPage+r:this.model.currentPage-r,i>=1&&i<=this.model.totalPages&&(this.numTextbox.val(i),this.model.currentPage=i));this.refreshPager();u!=this.model.currentPage&&this._trigger("change",{currentPage:this.model.currentPage,isInteraction:!0,event:event})},_renderTextboxItem:function(){var r=this,i=t.buildTag("input#"+this.element[0].id+"_numtext","",{},{type:"textbox",role:"textbox","aria-label":"Go to Textbox"});n(i).appendTo(this._parentMsgBar).insertBefore(n(this._parentMsgBar).find("span"));this.numTextbox=i;this.numTextbox.height(this.element.find(".e-pagercontainer .e-numericcontainer a").outerHeight());this.numTextbox.width(35);this.numTextbox.addClass("e-gototextbox e-textbox");this.model.showGoToPage||this.numTextbox.hide();this._gotoWid=n(this.numTextbox).outerWidth();this._on(n(this.numTextbox),"keydown",this._mouseScroll);this._on(this.numTextbox,"focusout",this._onTextboxBlur)},_renderPagerContainer:function(n){this._renderBackwardButton(n);this._renderpreviousPager(n);this._renderNumericItem(n);this._renderForwardPager(n);this._renderForwardButton(n)},_renderMsgBar:function(){var n=t.buildTag("span.e-pagermsg",String.format(this.localizedLabels.pagerInfo,this.model.currentPage,this.model.totalPages||0,this.model.totalRecordsCount||0));this._parentMsgBar.appendChild(n[0]);this._parentMsgBar.style.textAlign=t.TextAlign.Right},_renderpreviousPager:function(n){this._$PP=t.buildTag("a.e-link e-nextprevitemdisabled e-disable e-spacing e-PP","...",{},{title:this.localizedLabels.previousPagerTooltip,"aria-label":this.localizedLabels.previousPagerTooltip});this._$PP.attr("tabindex","0");n.append(this._$PP)},_renderForwardPager:function(n){this._$NP=t.buildTag("a.e-link e-NP e-numericitem e-spacing e-default","...",{},{title:this.localizedLabels.nextPagerTooltip,"aria-label":this.localizedLabels.nextPagerTooltip});this._$NP.attr("tabindex","0");n.append(this._$NP)},_renderBackwardButton:function(n){this._$first=t.buildTag("div.e-firstpage e-icon e-mediaback  e-firstpagedisabled e-disable","",{},{title:this.localizedLabels.firstPageTooltip,"aria-label":this.localizedLabels.firstPageTooltip,role:"link"});this._$prev=t.buildTag("div.e-prevpage e-icon e-arrowheadleft-2x  e-prevpagedisabled e-disable","",{},{title:this.localizedLabels.previousPageTooltip,"aria-label":this.localizedLabels.previousPageTooltip,role:"link"});this._$first.attr("tabindex","0");this._$prev.attr("tabindex","0");n.append(this._$first);n.append(this._$prev)},_renderNumericItem:function(n){var i=t.buildTag("div.e-numericcontainer e-default","",{});this._renderNumericLinks(i,this.model.pageCount);n.append(i);this._maxPageCount=this.model.pageCount},_renderNumericLinks:function(n){var i,r;for(n.empty(),this.model.pageCount=Math.round(this.model.pageCount),this.model.customText!=""?n.addClass("e-customtext"):n.removeClass("e-customtext"),i=1;i<=this.model.pageCount;i++)r=t.buildTag("a.e-link",this.model.customText+i,{},{role:"link","aria-label":"Go To page "+i.toString()}).addClass("e-numericitem e-spacing e-default").data("index",i),r.attr("tabindex","0"),i==this.model.currentPage&&r.removeClass("e-default").addClass("e-currentitem e-active"),n.append(r);this._links=n.children()},_renderForwardButton:function(n){this._$next=t.buildTag("div.e-nextpage e-icon e-arrowheadright-2x  e-default","",{},{title:this.localizedLabels.nextPageTooltip,role:"link","aria-label":this.localizedLabels.nextPageTooltip});this._$last=t.buildTag("div.e-lastpage e-icon e-mediaforward  e-default","",{},{title:this.localizedLabels.lastPageTooltip,role:"link","aria-label":this.localizedLabels.lastPageTooltip});this._$next.attr("tabindex","0");this._$last.attr("tabindex","0");n.append(this._$next);n.append(this._$last)},_setLocale:function(){this.localizedLabels=this._getLocalizedLabels();this._$first.attr("title",this.localizedLabels.firstPageTooltip);this._$prev.attr("title",this.localizedLabels.previousPageTooltip);this._$next.attr("title",this.localizedLabels.nextPageTooltip);this._$last.attr("title",this.localizedLabels.lastPageTooltip);this._$NP.attr("title",this.localizedLabels.nextPagerTooltip);this._$NP.attr("title",this.localizedLabels.previousPagerTooltip);this._$first.attr("aria-label",this.localizedLabels.firstPageTooltip);this._$prev.attr("aria-label",this.localizedLabels.previousPageTooltip);this._$next.attr("aria-label",this.localizedLabels.nextPageTooltip);this._$last.attr("aria-label",this.localizedLabels.lastPageTooltip);this._$NP.attr("aria-label",this.localizedLabels.nextPagerTooltip);this._$NP.attr("aria-label",this.localizedLabels.previousPagerTooltip)},_applyCss:function(){if(this.model.totalRecordsCount==null){this._$prev&&this._$prev.addClass("e-prevpagedisabled e-disable").removeClass("e-prevpage").removeClass("e-default");this._$first&&this._$first.addClass("e-firstpagedisabled e-disable").removeClass("e-firstpage").removeClass("e-default");this._$last&&this._$last.addClass("e-lastpagedisabled e-disable").removeClass("e-lastpage").removeClass("e-default");this._$next&&this._$next.addClass("e-nextpagedisabled e-disable").removeClass("e-nextpage").removeClass("e-default");this._$NP&&this._$NP.addClass("e-nextprevitemdisabled e-disable").removeClass("e-numericitem").removeClass("e-default");this.model.pageSizeList&&this.element.find(".e-drpdwndiv").addClass("e-disable");return}this.model.totalRecordsCount==0&&this.model.pageSizeList?this.element.find(".e-drpdwndiv").addClass("e-disable"):this.model.totalRecordsCount!=0&&this.model.pageSizeList&&this.element.find(".e-drpdwndiv").hasClass("e-disable")&&this.element.find(".e-drpdwndiv").removeClass("e-disable");this.model.currentPage>1&&this._$prev&&this._$first?(this._$prev.removeClass("e-prevpagedisabled").removeClass("e-disable").addClass("e-prevpage e-default"),this._$first.removeClass("e-firstpagedisabled").removeClass("e-disable").addClass("e-firstpage e-default")):(this._$prev&&this._$prev.addClass("e-prevpagedisabled e-disable").removeClass("e-prevpage").removeClass("e-default"),this._$first&&this._$first.addClass("e-firstpagedisabled e-disable").removeClass("e-firstpage").removeClass("e-default"));this._$PP&&(this.model.currentPage>this.model.pageCount?this._$PP.removeClass("e-nextprevitemdisabled").removeClass("e-disable").addClass("e-numericitem e-default"):this._$PP.addClass("e-nextprevitemdisabled e-disable").removeClass("e-numericitem").removeClass("e-default"));this._lastNP=this._links.length&&parseInt(this._links[0].innerHTML.replace(this.model.customText,""),10)+this.model.pageCount>this.model.totalPages?!0:!1;this._lastNP==!1&&(this._$NP?this._$NP.removeClass("e-nextprevitemdisabled").removeClass("e-disable").addClass("e-numericitem e-default"):this._$NP&&this._$NP.addClass("e-nextprevitemdisabled e-disable").removeClass("e-numericitem").removeClass("e-default"));this._lastpageCount=this.model.totalPages%this.model.pageCount;this._lastpageCount==0&&(this._lastpageCount=this.model.pageCount);this.model.currentPage>this.model.totalPages-this._LastpageCount&&(this._$PP&&this._$PP.removeClass("e-nextprevitemdisabled").removeClass("e-disable").addClass("e-numericitem e-default"),this._$NP&&this._$NP.addClass("e-nextprevitemdisabled e-disable").removeClass("e-numericitem").removeClass("e-default"));this.model.currentPage==this.model.totalPages||this.model.totalRecordsCount==0?(this._$last&&this._$last.addClass("e-lastpagedisabled e-disable").removeClass("e-lastpage").removeClass("e-default"),this._$next&&this._$next.addClass("e-nextpagedisabled e-disable").removeClass("e-nextpage").removeClass("e-default"),this._$NP&&this._$NP.addClass("e-nextprevitemdisabled e-disable").removeClass("e-numericitem").removeClass("e-default")):(this._$last&&this._$last.addClass("e-lastpage e-default").removeClass("e-lastpagedisabled").removeClass("e-disable"),this._$next&&this._$next.addClass("e-nextpage e-default").removeClass("e-nextpagedisabled").removeClass("e-disable"));this._links.length&&(this._links.removeClass("e-currentitem").removeClass("e-active").addClass("e-default"),n(this._links[(this.model.currentPage-1)%this.model.pageCount]).removeClass("e-default").addClass("e-currentitem e-active"),n(this._links[(this._prevPageNo-1)%this.model.pageCount]).removeClass("e-default").addClass("e-numericitem"));this._pageSize>=this.model.totalRecordsCount/this.model.pageCount&&this._$PP!=null&&this._$PP.length!=0&&(this._$PP.addClass("e-nextprevitemdisabled e-disable").removeClass("e-numericitem").removeClass("e-default"),this._$NP.addClass("e-nextprevitemdisabled e-disable").removeClass("e-numericitem").removeClass("e-default"))},_SetTotalPages:function(){this.model.totalPages=this.model.totalRecordsCount%this._pageSize==0?this.model.totalRecordsCount/this._pageSize:parseInt(this.model.totalRecordsCount/this._pageSize,10)+1},_refreshNumericItem:function(){var i,t,r;if(this._links.length!=0&&this._links!=null)for(this.model.currentPage=this.model.totalPages==1?1:this.model.currentPage,this.model.currentPage>this.model.totalPages&&this.model.totalPages!=0&&(this.model.currentPage=this.model.totalPages),i=parseInt(this.model.currentPage/this.model.pageCount,10),this.model.currentPage%this.model.pageCount==0&&i>0&&(i=i-1),this._links.css("display","none"),t=0;t<this.model.pageCount;t++)r=i*this.model.pageCount+1+t,r<=this.model.totalPages&&(this._links[t].style.display="",n(this._links[t]).data("index",r),n(this._links[t]).html(this.model.customText+r),n(this._links[t]).attr("aria-label","Go to page "+r.toString()))},_refreshPagerInfo:function(){this.model.totalRecordsCount==0&&(this.model.currentPage=0);this.element.find(".e-pagermsg").text(String.format(this.localizedLabels.pagerInfo,this.model.currentPage,this.model.totalPages||0,this.model.totalRecordsCount||0))},_refreshExternalMessage:function(){this.model.externalMessage.toString().length?this.element.find(".e-pagermessage").empty().html(this.model.externalMessage).show():this.element.find(".e-pagermessage").hide()},refreshPager:function(){this._templateElement&&(this._links=n(this._templateElement).find(".e-numericitem[role=link]"));this._SetTotalPages();this._refreshNumericItem();this._refreshPagerInfo();this._applyCss();this.model.enableExternalMessage&&this._refreshExternalMessage();this.model.enableRTL?this.element.addClass("e-rtl"):this.element.removeClass("e-rtl");this.numTextbox&&(n(this._templateElement).find(".e-textbox-paging").length>0?(this.model.currentPage>this.model.totalPages&&this.model.totalPages!=0&&(this.model.currentPage=this.model.totalPages),this.numTextbox.is(":focus")||this.numTextbox.val(String.format("{0} of {1}",this.model.currentPage,this.model.totalPages))):this.numTextbox.val(this.model.currentPage));this._temp===""||t.isNullOrUndefined(this._temp)||(this._temp.startsWith(".")||this._temp.startsWith("#"))&&n(this._temp)[0]!=null&&n(this._temp)[0]!=i&&this.option("template",n(this._temp).render([this.model]))},_kDownHandler:function(n){var t;if(t=n.keyCode?n.keyCode:n.which?n.which:n.charCode,n.target=null,this.model.masterObject.checkKey("firstPage",t,n))n.target=this._$first;else if(this.model.masterObject.checkKey("previousPager",t,n))n.target=this._$PP;else if(this.model.masterObject.checkKey("previousPage",t,n))n.target=this._$prev;else if(this.model.masterObject.checkKey("lastPage",t,n))n.target=this._$last;else if(this.model.masterObject.checkKey("nextPager",t,n))n.target=this._$NP;else if(this.model.masterObject.checkKey("nextPage",t,n))n.target=this._$next;else return!1;this._pagerClickHandler(n)},_pageInfo:function(){(this.model.showPageInfo||this.model.showGoToPage)&&!this._parentMsgBar?(this._parentMsgBar=document.createElement("div"),this._parentMsgBar.className+="e-parentmsgbar",this.model.showPageInfo&&this._renderMsgBar(),this.element[0].appendChild(this._parentMsgBar),this.element[0].className+=this.model.enableRTL?" e-pager e-rtl":" e-pager"):this.model.showPageInfo||this.model.showGoToPage?n(this._parentMsgBar).find(".e-pagermsg").remove():(this._parentMsgBar&&this._parentMsgBar.remove(),this._parentMsgBar=null);this._msgWidth=n(this._parentMsgBar).outerWidth()},_doClickAnimation:function(t){var r=n(t.target);r!=i&&t.type!=i&&r.addClass("e-animate")},_pagerClickHandler:function(t){if(!this.model.enabled)return!1;if(t.type!="keydown"||t.keyCode==13){this._prevPageNo=this.model.currentPage;var r=n(t.target);(this.element.find(".e-animate").removeClass("e-animate"),this._doClickAnimation(t),n.inArray(t.target,this._links)!=-1?this.model.currentPage=parseInt(n(t.target).data("index"),10):r.hasClass("e-nextpage")&&r.hasClass("e-nextpagedisabled")!=!0?this.model.currentPage%this.model.pageCount==0?(this.model.currentPage++,this._links!=i&&this._links.length!=0&&(this.model.currentPage=parseInt(n(this._links[0]).data("index"),10)+this.model.pageCount),this.model.currentPage+this.model.pageCount>=this.model.totalPages&&(this._lastNP=!0)):this.model.currentPage++:r.hasClass("e-prevpage")&&r.hasClass("e-prevpagedisabled")!=!0?(this.model.currentPage%this.model.pageCount==1&&(this._lastNP=!1),this.model.currentPage--,this.model.currentPage<0&&(this.model.currentPage=0)):r.hasClass("e-lastpage")&&r.hasClass("e-lastpagedisabled")!=!0?(this._LastpageCount=this.model.totalPages%this.model.pageCount,this._LastpageCount==0?this._LastpageCount=this.model.pageCount:null,this.model.currentPage=this.model.totalPages,this._lastNP=!0):r.hasClass("e-firstpage")&&r.hasClass("e-firstpagedisabled")!=!0?(this.model.currentPage=1,this._lastNP=!1):r.hasClass("e-NP")&&r.hasClass("e-nextprevitemdisabled")!=!0?(this._links!=i&&(this.model.currentPage=parseInt(n(this._links[0]).data("index"),10)+this.model.pageCount),parseInt(this._links[this.model.pageCount-1].innerHTML.replace(this.model.customText,""),10)+this.model.pageCount>=this.model.totalPages&&(this._lastNP=!0,this._LastpageCount=this.model.totalRecordsCount-this._pageSize<this._pageSize?this.model.totalRecordsCount-this._pageSize:this.model.totalRecordsCount/this._pageSize%this.model.pageCount,this._LastpageCount==0?this._LastpageCount=this.model.pageCount:null,this._links!=i&&(this.model.currentPage=parseInt(n(this._links[this.model.pageCount-1]).data("index"),10)+1))):r.hasClass("e-PP")&&r.hasClass("e-nextprevitemdisabled")!=!0&&(this._links!=i&&(this.model.currentPage=parseInt(n(this._links[0]).data("index"),10)-this.model.pageCount),this._lastNP=!1),this._trigger("click",{currentPage:this.model.currentPage,event:t}))||(r.hasClass("e-newrecord")&&this._trigger("addRecord",{currentPage:this.model.currentPage,event:t}),this.goToPage(this.model.currentPage,t))}},goToPage:function(n,t){n!=this.model.currentPage&&(this._prevPageNo=this.model.currentPage);this._prevPageNo!==n&&n>=1&&n<=this.model.totalPages&&(this.model.currentPage=n,this.model.enableQueryString&&this._updateQueryString(this.model.currentPage));this._prevPageNo!=this.model.currentPage&&(this.refreshPager(),this._trigger("change",{currentPage:this.model.currentPage,isInteraction:!0,event:t}))},goToNextPage:function(){this.model.totalRecordsCount!=0&&this.model.currentPage>=1&&this.model.currentPage<this.model.totalPages&&(this.model.currentPage=this.model.currentPage+1,this.refreshPager(),this._trigger("change",{currentPage:this.model.currentPage,isInteraction:!1}))},goToLastPage:function(){this.model.totalRecordsCount!=0&&this.model.currentPage>=1&&this.model.currentPage<this.model.totalPages&&(this.model.currentPage=this.model.totalPages,this.refreshPager(),this._trigger("change",{currentPage:this.model.currentPage,isInteraction:!1}))},goToFirstPage:function(){this.model.totalRecordsCount!=0&&this.model.currentPage>1&&(this.model.currentPage=1,this.refreshPager(),this._trigger("change",{currentPage:this.model.currentPage,isInteraction:!1}))},goToPrevPage:function(){this.model.totalRecordsCount!=0&&this.model.currentPage>1&&(this.model.currentPage=this.model.currentPage-1,this.refreshPager(),this._trigger("change",{currentPage:this.model.currentPage,isInteraction:!1}))},_updateQueryString:function(n){var t=this._getUpdatedURL(window.location.href,"page",n);history.pushState?window.history.pushState({path:t},"",t):window.location.href=t},_getUpdatedURL:function(n,t,i){var u=new RegExp("([?|&])"+t+"=.*?(&|#|$)","i"),r,f;return n.match(u)?n.replace(u,"$1"+t+"="+i+"$2"):(r="",f=n.indexOf("?")!==-1?"&":"?",n.indexOf("#")!==-1&&(r=n.replace(/.*#/,"#"),n=n.replace(/#.*/,"")),n+f+t+"="+i+r)},_getLocalizedLabels:function(){return t.getLocalizedConstants(this.sfType,this.model.locale)},_fillScreen:function(){if(!n(this._templateElement).find(".e-textbox-paging").length>0){var t=this.element.find(".e-pagercontainer").outerWidth()-(this._$first.outerWidth(!0)+this._$last.outerWidth(!0)+this._$next.outerWidth(!0)+this._$prev.outerWidth(!0));t-=!this._$NP||this._$NP.hasClass("e-disable")?0:this._$NP.outerWidth(!0);t-=!this._$PP||this._$PP.hasClass("e-disable")?0:this._$PP.outerWidth(!0);this.model.template?this.element.outerWidth()-n(this.element.find(".e-pagercontainer")).outerWidth()<40&&(Math.floor(t/this.element.find(".e-numericitem.e-spacing.e-default").outerWidth(!0))-1<1?this._flag?this.option("pageCount",1):this.model.pageCount=1:this._flag?this.option("pageCount",Math.floor(t/this.element.find(".e-numericitem.e-spacing.e-default").outerWidth(!0))-1):this.model.pageCount=Math.floor(t/this.element.find(".e-numericitem.e-spacing.e-default").outerWidth(!0))-1):this.element.outerWidth()-n(this.element.contents()[0]).outerWidth()<40&&(Math.floor(t/this.element.find(".e-numericcontainer .e-numericitem.e-spacing.e-default").outerWidth(!0))-1<1?this._flag?this.option("pageCount",1):this.model.pageCount=1:this._flag?this.option("pageCount",Math.floor(t/this.element.find(".e-numericcontainer .e-numericitem.e-spacing.e-default").outerWidth(!0))-1):this.model.pageCount=Math.floor(t/this.element.find(".e-numericcontainer .e-numericitem.e-spacing.e-default").outerWidth(!0))-1)}},_enable:function(){this.element.removeClass("e-disable").attr({"aria-disabled":!1});this.element.prop("disabled",!1);this.numTextbox&&this.numTextbox.removeAttr("disabled")},_disable:function(){this.element.addClass("e-disable").attr({"aria-disabled":!0});this.element.prop("disabled","disabled");this.numTextbox&&this.numTextbox.attr("disabled","disabled");this.$dropItem&&this.$dropItem.css("display")!="none"&&this.$dropItem.hide();this.model.template&&n(this._templateElement).addClass(".e-disable")},_setFirst:!0,_setModel:function(t){var i,r;for(i in t){this._preval=this.model.currentPage;switch(i){case"pageCount":this._renderNumericLinks(this.element.find(".e-numericcontainer"));this.model.isResponsive&&!this._flag&&(this._maxPageCount=this.model.pageCount,this._fillScreen());this._flag=!1;break;case"template":this.element.children().remove();this.renderPager();break;case"enableExternalMessage":t[i]?this._renderPagerMessage():this._removePagerMessage();break;case"showPageInfo":this._pageInfo();break;case"pageSizeMessage":t[i]!=null&&this._renderPageSizeMessage();break;case"pageSize":this._pageSize=this.model.pageSize;this.model.pageSizeList&&this.model.pageSizeList.length>0&&(r=this,this.$textspan.text(this.model.pageSize),this.$dropItem.find(".e-active").removeClass("e-active"),this.$dropItem.find("li").each(function(){var t=n(this);t.text()==r.model.pageSize&&n(this).addClass("e-active")}));break;case"pageSizeList":this.model.pageSizeList=t[i];t[i].length>0?(this._pageSize=this.model.pageSize,this.$dropItem?(this.$dropItem.empty(),this._renderItems()):this._renderDropdownlist()):this.$dropItem&&this._removeDropdownlist();break;case"enabled":t[i]?this._enable():this._disable();break;case"cssClass":this.element.removeClass(this._cssClass).addClass(t[i]);this._cssClass=this.model.cssClass;case"isResponsive":this._wireResizing();t[i]?this.model.isResponsive&&!this._flag&&(this._maxPageCount=this.model.pageCount,this._fillScreen()):this.option("pageCount",this._maxPageCount);this._flag=!1;break;case"showGoToPage":t[i]?this._renderTextboxItem():this.numTextbox&&(this.numTextbox.remove(),this.numTextbox=null);break;case"locale":this._setLocale();break;case"currentPage":this.refreshPager();this._trigger("change",{currentPage:this.model.currentPage,isInteraction:!1})}}i!="currentPage"&&(this.refreshPager(),this._preval!=this.model.currentPage&&this._trigger("change",{currentPage:this.model.currentPage,isInteraction:!1}))},_destroy:function(){this.element.empty().removeClass("e-pager");this._unWireResizing()}});t.Pager.Locale=t.Pager.Locale||{};t.Pager.Locale["default"]=t.Pager.Locale["en-US"]={pagerInfo:"{0} of {1} pages ({2} items)",firstPageTooltip:"Go to first page",lastPageTooltip:"Go to last page",nextPageTooltip:"Go to next page",previousPageTooltip:"Go to previous page",nextPagerTooltip:"Go to next pager",previousPagerTooltip:"Go to previous pager"}})(jQuery,Syncfusion)});
