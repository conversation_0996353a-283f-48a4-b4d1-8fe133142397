/*!
*  filename: ej.splitbutton.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./ej.menu.min"],n):n()})(function(){(function(n,t){t.widget("ejSplitButton","ej.SplitButton",{element:null,model:null,validTags:["button"],_setFirst:!1,_rootCSS:"e-splitbutton",defaults:{size:"normal",width:"",height:"",enabled:!0,htmlAttributes:{},text:null,contentType:"textonly",imagePosition:"imageleft",buttonMode:"split",arrowPosition:"right",targetID:null,target:null,showRoundedCorner:!1,prefixIcon:null,suffixIcon:null,cssClass:"",enableRTL:!1,create:null,beforeOpen:null,click:null,itemMouseOver:null,itemMouseOut:null,itemSelected:null,open:null,close:null,destroy:null,popupPosition:"down"},dataTypes:{size:"string",enabled:"boolean",showRoundedCorner:"boolean",text:"string",contentType:"enum",imagePosition:"enum",buttonMode:"enum",arrowPosition:"enum",target:"string",targetID:"string",prefixIcon:"string",suffixIcon:"string",cssClass:"string",enableRTL:"boolean",htmlAttributes:"data"},disable:function(){this.element.addClass("e-disable");this.wrapper.addClass("e-disable");this.contstatus&&this._hidePopup();this.model.buttonMode==t.ButtonMode.Split&&this.dropbutton.addClass("e-disable").attr("aria-disabled",!0);this.model.buttonMode==t.ButtonMode.Dropdown&&this.btnimgwrap.addClass("e-disable").attr("aria-disabled",!0);this.model.enabled=!1},visible:function(n){n?(this.wrapper.removeClass("e-split-btn-hide"),this.wrapper.find(".e-icon").css("visibility","")):(this.wrapper.addClass("e-split-btn-hide"),this.wrapper.find(".e-icon").css("visibility","hidden"))},enable:function(){this.element.removeClass("e-disable");this.wrapper.removeClass("e-disable");this.model.buttonMode==t.ButtonMode.Split&&this.dropbutton.removeClass("e-disable").attr("aria-disabled",!1);this.model.buttonMode==t.ButtonMode.Dropdown&&this.btnimgwrap.removeClass("e-disable").attr("aria-disabled",!1);this.model.enabled=!0},hide:function(){this.contstatus&&this._hidePopup()},show:function(){this.contstatus||(this.model.buttonMode==t.ButtonMode.Dropdown?this.element.click():this.model.buttonMode==t.ButtonMode.Split&&this.dropbutton.click())},setPopupPosition:function(n){this._setPosition=!0;this._val=n},_init:function(){this._cloneElement=this.element.clone();this._setPosition=!1;this._initialize();this._controlStatus(this.model.enabled);this._documentClickHandler=n.proxy(this._documentClick,this);this._wireEvents()},_createElement:function(t,i){var r=document.createElement(t);return this._setAttributes(r,i),n(r)},_setAttributes:function(n,t){for(var i in t)n.setAttribute(i,t[i])},_destroy:function(){this.contstatus&&this._hide();this.splitwrap.removeClass("e-drop");this.splitwrap.removeClass("e-btn-"+this.model.size);this.innerWrap.removeClass("e-splitarrowright e-splitarrowleft e-splitarrowbottom e-splitarrowtop");this.element.removeClass(this.model.cssClass+"e-ntouch e-select e-corner e-btn e-disable e-split-btn e-droparrowright e-droparrowleft e-droparrowbottom e-droparrowtop e-left-btn e-txt").empty();this.element.append(this._cloneElement.text());this.element.insertAfter(this.wrapper);this.wrapper.remove();this._contextObj&&this._contextObj.model&&this._contextObj.destroy();this._contextObj&&(this._contextObj=null);n(this.model.target).show();n(this.model.target).insertAfter(this.element);this._off(this.element,"click",this._btnMouseClick)},_setModel:function(n){for(var t in n)switch(t){case"size":this._setSize(n[t]);break;case"width":this._splitbtnWidth(n[t]);break;case"height":this._splitbtnHeight(n[t]);break;case"contentType":this._setContentType(n[t]);break;case"imagePosition":this._setImagePosition(n[t]);break;case"buttonMode":this._setButtonMode(n[t]);break;case"arrowPosition":this._setArrowPosition(n[t]);break;case"text":this._setText(n[t]);break;case"prefixIcon":this._setMajorIcon(n[t]);break;case"suffixIcon":this._setMinorIcon(n[t]);break;case"enabled":this._controlStatus(n[t]);break;case"targetID":case"target":this._setTarget(n[t]);break;case"showRoundedCorner":this._roundedCorner(n[t]);break;case"cssClass":this._setSkin(n[t]);break;case"enableRTL":this._setRTL(n[t]);break;case"htmlAttributes":this._addAttr(n[t]);break;case"popupPosition":this._setPopupPosition(n[t])}},_setText:function(n){this.model.contentType==t.ContentType.TextOnly?this.model.buttonMode==t.ButtonMode.Split?this.element.html(n):(this.element.empty(),this.imgtxtwrap=n,this.model.arrowPosition==t.ArrowPosition.Left||this.model.arrowPosition==t.ArrowPosition.Top?this.element.append(this.btnimgwrap,this.imgtxtwrap):this.element.append(this.imgtxtwrap,this.btnimgwrap)):this.textspan.html(n)},_setMajorIcon:function(n){this.majorimgtag.removeClass(this.model.prefixIcon);this.majorimgtag.addClass(n)},_setMinorIcon:function(n){this.minorimgtag.removeClass(this.model.suffixIcon);this.minorimgtag.addClass(n)},_setTarget:function(n){n.substring(0,1)=="."||n.substring(0,1)=="#"?this.model.target=n:(this.model.targetID=n,this.model.target="#"+n);this._renderContxtMenu()},_setContentType:function(n){n!=this.model.contentType&&(this.element.empty(),this.model.contentType=n,this._renderButtonContent())},_setImagePosition:function(n){(n==t.ImagePosition.ImageRight||n==t.ImagePosition.ImageLeft||n==t.ImagePosition.ImageBottom||n==t.ImagePosition.ImageTop)&&this.model.contentType==t.ContentType.TextAndImage&&n!=this.model.imagePosition&&(this.element.empty(),this.model.imagePosition=n,this._renderButtonContent())},_setButtonMode:function(n){(n==t.ButtonMode.Split||n==t.ButtonMode.Dropdown)&&n!=this.model.buttonMode&&(this._destroy(),this.model.buttonMode=n,this._init())},_setArrowPosition:function(n){(n==t.ArrowPosition.Right||n==t.ArrowPosition.Left||n==t.ArrowPosition.Bottom||n==t.ArrowPosition.Top)&&(this.model.buttonMode==t.ButtonMode.Dropdown&&n!=this.model.arrowPosition?(this.model.arrowPosition=n,this.element.empty(),this._setSize(this.model.size),this.element.removeClass("e-droparrowright e-droparrowleft e-droparrowbottom e-droparrowtop"),this._renderButtonContent()):this.model.buttonMode==t.ButtonMode.Split&&n!=this.model.arrowPosition&&(this.model.arrowPosition=n,this._setSize(this.model.size),this.innerWrap.removeClass("e-splitarrowright e-splitarrowleft e-splitarrowbottom e-splitarrowtop"),this._setRTL(this.model.enableRTL)))},_setPopupPosition:function(n){this.model.popupPosition=n;this.model.popupPosition=="down"&&this.dropdownimg.addClass("e-arrow-sans-down").removeClass("e-arrow-sans-up");this.model.popupPosition=="up"&&this.dropdownimg.addClass("e-arrow-sans-up").removeClass("e-arrow-sans-down")},_setRTL:function(n){if(this.model.buttonMode==t.ButtonMode.Split){this.dropdownimg.removeClass("e-arrow-sans-up").addClass("e-arrow-sans-down");switch(this.model.arrowPosition){case t.ArrowPosition.Right:this.innerWrap.addClass("e-splitarrowright");break;case t.ArrowPosition.Left:this.innerWrap.addClass("e-splitarrowleft");break;case t.ArrowPosition.Bottom:this.innerWrap.addClass("e-splitarrowbottom");break;case t.ArrowPosition.Top:this.innerWrap.addClass("e-splitarrowtop");this.dropdownimg.addClass("e-arrow-sans-up").removeClass("e-arrow-sans-down")}n==!0?(this.innerWrap.hasClass("e-splitarrowleft")?"":this.splitwrap.addClass("e-rtl e-btnrtl"))&&this._contextObj&&this._contextObj.model&&this._contextObj.element.addClass("e-rtl"):this.splitwrap.removeClass("e-rtl e-btnrtl")&&this._contextObj&&this._contextObj.model&&this._contextObj.element.removeClass("e-rtl")}else n==!0?this.splitwrap.addClass("e-rtl")&&this._contextObj&&this._contextObj.model&&this._contextObj.element.addClass("e-rtl"):this.splitwrap.removeClass("e-rtl")&&this._contextObj&&this._contextObj.model&&this._contextObj.element.removeClass("e-rtl");this.model.enableRTL=n;this._roundedCorner(this.model.showRoundedCorner)},_roundedCorner:function(t){t?(this.element.addClass("e-corner"),n(this.model.target).length>0&&n(this.model.target).addClass("e-corner")):(this.element.removeClass("e-corner"),n(this.model.target).length>0&&n(this.model.target).removeClass("e-corner"))},_controlStatus:function(n){n?this.enable():this.disable()},_setSkin:function(n){this.element.removeClass(this.model.cssClass);this.model.buttonMode==t.ButtonMode.Split&&(this.dropbutton.removeClass(this.model.cssClass),this.dropbutton.addClass(n));this.element.addClass(n);this._contextObj&&this._contextObj.model&&this._contextObj.option("cssClass",n)},_initialize:function(){this.element.is("button")||this.element.is("ej-splitbutton")?this._render():this.element.removeClass("e-splitbutton");this._timeout=null},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.wrapper.addClass(n):t=="disabled"&&n=="disabled"?i._controlStatus(!1):i.wrapper.attr(t,n)})},_render:function(){this.element.addClass(this.model.cssClass+" e-btn e-select e-split-btn "+(t.isTouchDevice()?"":"e-ntouch"));this._setAttributes(this.element[0]);this.model.text==null||this.model.text==""?this.model.text=this.element.text():this._setAttributes(this.element[0],{"aria-describedby":this.model.text});this.model.buttonMode!=t.ButtonMode.Split&&this.model.buttonMode!=t.ButtonMode.Dropdown&&(this.model.buttonMode=t.ButtonMode.Split);this.model.arrowPosition!=t.ArrowPosition.Right&&this.model.arrowPosition!=t.ArrowPosition.Left&&this.model.arrowPosition!=t.ArrowPosition.Bottom&&this.model.arrowPosition!=t.ArrowPosition.Top&&(this.model.arrowPosition=t.ArrowPosition.Right);this.element.empty();this.splitwrap=this.model.buttonMode==t.ButtonMode.Split?this._createElement("span",{"class":"e-split e-widget"}):this._createElement("span",{"class":"e-split e-drop e-widget"});this.splitwrap.insertBefore(this.element);this.innerWrap=this._createElement("span",{"class":"e-in-wrap e-box e-padding"});this.splitwrap[0].appendChild(this.innerWrap[0]);this.wrapper=this.splitwrap;this.dropdownimg=this._createElement("span",{"class":"e-icon e-arrow-sans-down"});this.dropdownimage=this._createElement("span",{"class":"e-icon e-arrow-sans-up"});this.btnimgwrap=this._createElement("span",{"class":"e-split-btn-div e-btn-span"});this.model.popupPosition=="up"?this.btnimgwrap[0].appendChild(this.dropdownimage[0]):this.btnimgwrap[0].appendChild(this.dropdownimg[0]);this.model.buttonMode==t.ButtonMode.Split&&(this.dropbutton=t.buildTag("button.e-split-btn e-btn e-select "+this.model.cssClass+" e-drp-btn"+(t.isTouchDevice()?"":" e-ntouch"),"",{},{type:"button","data-role":"none",id:this.element[0].id+"drpbtn"}),this.dropbutton[0].appendChild(this.btnimgwrap[0]),this.dropbutton.insertAfter(this.element),this.model.contentType==t.ContentType.TextOnly?this.dropbutton.addClass("e-btn-txt"):this.dropbutton.addClass("e-rht-btn"));this._setSize(this.model.size);this.element.addClass("e-left-btn");this._renderButtonContent();this.model.buttonMode==t.ButtonMode.Dropdown?this.innerWrap[0].appendChild(this.element[0]):(this.innerWrap[0].appendChild(this.element[0]),this.innerWrap[0].appendChild(this.dropbutton[0]));this.model.target==null&&this.model.targetID!=null&&(this.model.target="#"+this.model.targetID);n(this.model.target).hide();this._roundedCorner(this.model.showRoundedCorner);this._setRTL(this.model.enableRTL);this._addAttr(this.model.htmlAttributes)},_renderButtonContent:function(){if(this.textspan=t.buildTag("span.e-btntxt",this.model.text),this.majorimgtag=t.buildTag("span").addClass(this.model.prefixIcon),this.minorimgtag=t.buildTag("span").addClass(this.model.suffixIcon),this.imgtxtwrap=t.buildTag("span").addClass("e-btn-span"),this.model.contentType==t.ContentType.TextAndImage)switch(this.model.imagePosition){case t.ImagePosition.ImageRight:this.imgtxtwrap.append(this.textspan,this.majorimgtag);break;case t.ImagePosition.ImageLeft:this.imgtxtwrap.append(this.majorimgtag,this.textspan);break;case t.ImagePosition.ImageBottom:this.majorimgtag.css("display","inline-table");this.imgtxtwrap.append(this.textspan,this.majorimgtag);break;case t.ImagePosition.ImageTop:this.majorimgtag.css("display","inline-table");this.imgtxtwrap.append(this.majorimgtag,this.textspan)}else this.model.contentType==t.ContentType.ImageTextImage?this.imgtxtwrap.append(this.majorimgtag,this.textspan,this.minorimgtag):this.model.contentType==t.ContentType.ImageBoth?this.imgtxtwrap.append(this.majorimgtag,this.minorimgtag):this.model.contentType==t.ContentType.ImageOnly?this.imgtxtwrap.append(this.majorimgtag):(this.element.addClass("e-txt"),this.imgtxtwrap=this.model.text);this.model.buttonMode==t.ButtonMode.Dropdown?this._renderDropdownArrow():this.element.append(this.imgtxtwrap)},_renderDropdownArrow:function(){this.btnimgwrap.css("position","absolute");this.dropdownimg.removeClass("e-arrow-sans-up").addClass("e-arrow-sans-down");switch(this.model.arrowPosition){case t.ArrowPosition.Right:this.element.addClass("e-droparrowright");this.element.append(this.imgtxtwrap,this.btnimgwrap);break;case t.ArrowPosition.Left:this.element.addClass("e-droparrowleft");this.element.append(this.btnimgwrap,this.imgtxtwrap);break;case t.ArrowPosition.Bottom:this.element.addClass("e-droparrowbottom");this.element.append(this.imgtxtwrap,this.btnimgwrap);break;case t.ArrowPosition.Top:this.element.addClass("e-droparrowtop");this.dropdownimg.addClass("e-arrow-sans-up").removeClass("e-arrow-sans-down");this.element.append(this.btnimgwrap,this.imgtxtwrap)}},_setSize:function(n){this.wrapper.css({height:"",width:""});switch(n){case"mini":this._splitbtnSize(n);break;case"small":this._splitbtnSize(n);break;case"medium":this._splitbtnSize(n);break;case"large":this._splitbtnSize(n);break;default:this._splitbtnSize(n)}(this.model.arrowPosition==t.ArrowPosition.Bottom||this.model.arrowPosition==t.ArrowPosition.Top)&&this.model.height==""&&this.splitwrap.addClass("e-btn-arrowsplit-"+n);var i,r=this.model.height===""?this.wrapper.outerHeight()+"px":this.model.height;this._splitbtnHeight(r);this.model.size!=="normal"?(i=this.model.width===""?this.wrapper.outerWidth()+"px":this.model.width,this._splitbtnWidth(i)):this.model.width!==""&&(i=this.model.width,this._splitbtnWidth(i))},_splitbtnSize:function(n){this.splitwrap.removeClass("e-btn-mini e-btn-medium e-btn-small e-btn-large e-btn-normal e-btn-arrowsplit-large e-btn-arrowsplit-small e-btn-arrowsplit-mini e-btn-arrowsplit-medium e-btn-arrowsplit-normal");(this.model.arrowPosition==t.ArrowPosition.Left||this.model.arrowPosition==t.ArrowPosition.Right)&&this.splitwrap.addClass("e-btn-"+n)},_splitbtnHeight:function(n){(n==""||n==null)&&(n="30px");this.splitwrap.css("height",n)},_splitbtnWidth:function(n){this.splitwrap.css("width",n)},_renderContxtMenu:function(){this.model.target!=null&&n(this.model.target).attr("id")==null&&this.model.target.substring(0,1)=="."&&n(this.model.target).attr("id",this.element.attr("id")+"_"+this.model.target.replace(".",""));n(this.model.target).ejMenu({menuType:t.MenuType.ContextMenu,openOnClick:!1,contextMenuTarget:"",fields:this.model.fields,showArrow:!0,cssClass:"e-split "+this.model.cssClass,enableRTL:this.model.enableRTL}).on("ejMenuclose",n.proxy(this._onKeyDown,this));this._contextObj=n(this.model.target).ejMenu("instance")},_onKeyDown:function(n){n.keyCode==27&&this._hide()},_itemClick:function(t){t={status:this.model.enabled,ID:t.ID,text:t.text};this._trigger("itemSelected",t);n(t.element).hasClass("e-haschild")||this._hide()},_itemMouseOver:function(n){this._trigger("itemMouseOver",n)},_itemMouseOut:function(n){this._trigger("itemMouseOut",n)},_wireEvents:function(){this._on(this.element,"click",this._btnMouseClick);this._on(this.element,"mousedown",this._btnMouseDown);this.model.buttonMode==t.ButtonMode.Split&&this._on(this.dropbutton,"click",this._btnMouseClick);this._on(n(document),"mousedown",this._docrhtclk)},_btnMouseClick:function(i){this._contextObj?!this._contextObj.model&&this._renderContxtMenu():this._renderContxtMenu();var r;if(!this.model.enabled)return!1;if(!n(i.currentTarget).hasClass("e-disable"))if(i.currentTarget.id!=this.element[0].id+"drpbtn"&&this.model.buttonMode==t.ButtonMode.Split)r={status:this.model.enabled},this._trigger("click",r);else if(this.contstatus||this._trigger("beforeOpen"),this.wrapper.addClass("e-active"),this.contstatus)this._hidecontext(i);else{this._contextPosition(i);this._trigger("open");this._on(n(window),"resize",this._OnWindowResize);this.contstatus=!0;this.element.on("click",n.proxy(this._hidecontext,this));t.listenTouchEvent(n(document),t.startEvent(),this._documentClickHandler,!1,this);this._on(t.getScrollableParents(this.wrapper),"scroll",this._hidePopup)}},_OnWindowResize:function(n){this._contextPosition(n)},_contextPosition:function(i,r,u,f){var e,o,h,s;this._contextObj.model&&(e=this._setPosition?this._val:this._getXYpos(i),h=this.model.buttonMode==t.ButtonMode.Split?this.dropbutton:this.element,s=this._contextObj,o=e.x-(n(this.model.target).outerWidth()-(this.model.buttonMode==t.ButtonMode.Split?this.dropbutton.outerWidth():this.element.outerWidth())),e.x=this.model.enableRTL?this.model.popupPosition=="left"||o<n(this.model.target).outerWidth()?e.x:o:this.model.popupPosition=="left"||e.x+n(this.model.target).outerWidth()<n(window).width()?e.x:o,this._posright<0&&this.model.popupPosition=="left"&&(e.x=n(window).outerWidth()),s.option({click:n.proxy(this._itemClick,this),mouseover:n.proxy(this._itemMouseOver,this),mouseout:n.proxy(this._itemMouseOut,this)}),r?s.show(u,f,h,i):s.show(e.x,e.y,h,i))},_getXYpos:function(){var i,e,o,r,u=1,f;return i=this._getOffset(this.model.buttonMode==t.ButtonMode.Split?this.dropbutton:this.element),r=this.model.popupPosition=="right"&&!this.model.enableRTL&&this.model.arrowPosition!=t.ArrowPosition.Left&&this.model.arrowPosition!=t.ArrowPosition.Bottom&&this.model.arrowPosition!=t.ArrowPosition.Top?this.model.buttonMode==t.ButtonMode.Split?i.left+this.dropbutton.outerWidth():i.left+this.element.outerWidth():this.model.popupPosition=="left"&&(this.model.enableRTL||this.model.arrowPosition!=t.ArrowPosition.Right)&&this.model.arrowPosition!=t.ArrowPosition.Bottom&&this.model.arrowPosition!=t.ArrowPosition.Top?i.left-n(this.model.target).outerWidth():i.left,this._posright=n(window).width()-r-n(this.model.target).outerWidth(),this._posleft=r-n(this.model.target).outerHeight(),this._posrightht=n(window).height()-i.top-n(this.model.target).outerHeight(),e=this.model.popupPosition=="right"&&this._posright<0||r<0?i.left:r,f=this.model.arrowPosition==t.ArrowPosition.Top||this.model.popupPosition=="up"?i.top-n(this.model.target).outerHeight()+1:this.model.arrowPosition!=t.ArrowPosition.Top&&this.model.arrowPosition!=t.ArrowPosition.Bottom&&((this.model.arrowPosition==t.ArrowPosition.Right!=(this.model.popupPosition=="left")||this.model.arrowPosition==t.ArrowPosition.Right&&this.model.enableRTL&&this.model.popupPosition=="left")&&this.model.arrowPosition==t.ArrowPosition.Left!=(this.model.popupPosition=="right")||this.model.arrowPosition==t.ArrowPosition.Right&&this.model.enableRTL&&this.model.popupPosition=="left")&&(!this.model.enableRTL&&this.model.popupPosition=="right"||this.model.popupPosition=="left")?i.top:(this.model.buttonMode==t.ButtonMode.Split?i.top+this.dropbutton.outerHeight():i.top+this.element.outerHeight())-u,o=this.model.popupPosition=="left"&&this._posrightht<0&&this._posright>0&&this._posleft<0?i.top-n(this.model.target).outerHeight():this.model.popupPosition=="left"&&this._posrightht<0&&this._posright>0?i.top+this.element.outerHeight()-n(this.model.target).outerHeight():(this.model.popupPosition=="right"||this.model.popupPosition=="up"||this.model.popupPosition)&&this._posrightht<0&&this._posright<0?i.top-n(this.model.target).outerHeight():(this.model.popupPosition=="right"||this.model.popupPosition=="left")&&this._posrightht<0?i.top+this.element.outerHeight()-n(this.model.target).outerHeight():this.model.popupPosition=="right"&&this._posright<0&&this._posrightht>0?i.top+this.element.outerHeight():this.model.popupPosition=="down"&&this._posrightht<0&&this._posright>0?i.top-n(this.model.target).outerHeight():r<0||this._posright<0?i.top+this.element.outerHeight():f<0?this.model.buttonMode==t.ButtonMode.Split&&this.model.popupPosition=="up"&&this.model.arrowPosition!=t.ArrowPosition.Top?i.top+this.element.outerHeight()-u:i.top+(this.model.buttonMode==t.ButtonMode.Split?this.dropbutton.outerHeight()+this.element.outerHeight():this.element.outerHeight())-u:f,{x:e,y:o}},_getOffset:function(t){var i=t.offset(),r;return n("body").css("position")!="static"&&(r=n("body").offset(),i.left-=r.left,i.top-=r.top),i},_btnMouseDown:function(t){n(t.currentTarget).hasClass("e-disable")||this._docrhtclk(t)},_hidePopup:function(n){this._contextObj&&this._contextObj.hide(n);this._hide();this._off(t.getScrollableParents(this.wrapper),"scroll",this._hidePopup)},_hide:function(){this.contstatus=!1;this.wrapper.removeClass("e-active");this.element.off("click",n.proxy(this._hidecontext,this));t.listenTouchEvent(n(document),t.startEvent(),this._documentClickHandler,!0,this);this._off(n(window),"resize",this._OnWindowResize);this._off(t.getScrollableParents(this.wrapper),"scroll",this._hide);this._closeEvent()},_closeEvent:function(){this._trigger("close")},_hidecontext:function(t){((n(t.target).is(this.element)||n(t.target).is(this.dropbutton)||n(t.target).is(this.textspan)||n(t.target).is(this.dropdownimg)||n(t.target).is(this.btnimgwrap)||!n(t.target).is(this.majorimgtag)||!n(t.target).is(this.minorimgtag))&&!n(t.target).is(this.splitwrap)&&!n(t.target).parents().is(n(this.model.target))||this.element.hasClass("e-txt")||n(t.target).is(this.imgtxtwrap))&&this._hidePopup(t)},_documentClick:function(t){n(t.target).is(this.element)||n(t.target).is(this.dropbutton)||n(t.target).is(this.textspan)||n(t.target).is(this.dropdownimg)||n(t.target).is(this.btnimgwrap)||n(t.target).is(this.majorimgtag)||n(t.target).is(this.minorimgtag)||n(t.target).is(this.splitwrap)||n(t.target).parents().is(n(this.model.target))||!this.element.hasClass("e-txt")&&n(t.target).is(this.imgtxtwrap)||this._hidePopup(t)},_docrhtclk:function(n){var t,i;t=!1;n.button?t=n.button==2:n.which&&(t=n.which==3);i=n.target;t&&n.preventDefault()}});t.ContentType={TextOnly:"textonly",ImageOnly:"imageonly",ImageBoth:"imageboth",TextAndImage:"textandimage",ImageTextImage:"imagetextimage"};t.ImagePosition={ImageRight:"imageright",ImageLeft:"imageleft",ImageTop:"imagetop",ImageBottom:"imagebottom"};t.ButtonSize={Mini:"mini",Small:"small",Medium:"medium",Large:"large"};t.ButtonMode={Split:"split",Dropdown:"dropdown"};t.ArrowPosition={Right:"right",Left:"left",Top:"top",Bottom:"bottom"}})(jQuery,Syncfusion)});
