/*!
*  filename: ej.sparkline.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min"],n):n()})(function(){var t=this&&this.__extends||function(n,t){function r(){this.constructor=n}for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)},n;(function(n){var i,r;(function(n){n[n.Line="line"]="Line";n[n.Column="column"]="Column";n[n.Area="area"]="Area";n[n.WinLoss="winloss"]="WinLoss";n[n.Pie="pie"]="Pie"})(n.Type||(n.Type={}));i=n.Type,function(n){n[n.flatlight="flatlight"]="flatlight";n[n.azurelight="azurelight"]="azurelight";n[n.limelight="limelight"]="limelight";n[n.saffronlight="saffronlight"]="saffronlight";n[n.gradientlight="gradientlight"]="gradientlight";n[n.flatdark="flatdark"]="flatdark";n[n.azuredark="azuredark"]="azuredark";n[n.limedark="limedark"]="limedark";n[n.saffrondark="saffrondark"]="saffrondark";n[n.gradientdark="gradientdark"]="gradientdark"}(n.Themes||(n.Themes={}));r=n.Themes,function(){var u=function(u){function f(t,f){u.call(this);this.defaults={locale:null,enableGroupSeparator:!1,enableCanvasRendering:!1,padding:8,palette:["#8A2BE2","#ff1a75","#99cc00","#4d4dff","#660066","#FFA500","#FFD700","#FF00FF","#808000","#990000"],isResponsive:!0,dataSource:null,xName:"",yName:"",type:i.Line,width:1,stroke:null,opacity:1,fill:"#33ccff",border:{color:"transparent",width:1},rangeBandSettings:{startRange:null,endRange:null,opacity:.4,color:"transparent"},highPointColor:null,lowPointColor:null,negativePointColor:null,startPointColor:null,endPointColor:null,tooltip:{visible:!1,template:null,fill:"white",border:{width:1,color:null},font:{fontFamily:"Segoe UI",fontStyle:"Normal",fontWeight:"Regular",color:"#111111",opacity:1,size:"8px"}},markerSettings:{visible:!1,fill:null,width:2,opacity:1,border:{color:"white",width:1}},background:"transparent",size:{height:"",width:""},axisLineSettings:{visible:!1,color:"#111111",width:1,dashArray:""},theme:r.flatlight,load:null,loaded:null,doubleClick:null,rightClick:null,click:null,sparklineMouseMove:null,sparklineMouseLeave:null,seriesRendering:null,pointRegionMouseMove:null,pointRegionMouseClick:null,tooltipInitialize:null};this.dataTypes={dataSource:"data",palette:"array",type:"enum",theme:"enum"};this.model=null;this.svgLink="http://www.w3.org/2000/svg";this._id=null;this.negativePointIndexes=[];this.validTags=["div"];this._id=t;!f||(this.model=n.compareExtend({},f,this.defaults))}return t(f,u),f.prototype.isTouch=function(n){var t=n.originalEvent?n.originalEvent:n;return t.pointerType=="touch"||t.pointerType==2||t.type.indexOf("touch")>-1?!0:!1},f.prototype.browserInfo=function(){var n={},t=[],i={webkit:/(chrome)[ \/]([\w.]+)/i,safari:/(webkit)[ \/]([\w.]+)/i,msie:/(msie) ([\w.]+)/i,opera:/(opera)(?:.*version|)[ \/]([\w.]+)/i,mozilla:/(mozilla)(?:.*? rv:([\w.]+)|)/i};for(var r in i)if(i.hasOwnProperty(r)&&(t=navigator.userAgent.match(i[r]),t)){n.name=t[1].toLowerCase();n.version=t[2];!navigator.userAgent.match(/Trident\/7\./)||(n.name="msie");break}return n.isMSPointerEnabled=n.name=="msie"&&n.version>9&&window.navigator.msPointerEnabled,n.pointerEnabled=window.navigator.pointerEnabled,n},f.prototype.fadeOut=function(n){var t=1,i=setInterval(function(){t<=.1&&(clearInterval(i),n.parentNode?n.parentNode.removeChild(n):n);n.style.opacity=t;n.style.filter="alpha(opacity="+t*100+")";t-=t*.1},50)},f.prototype._setModel=function(t){var i;for(i in t)switch(i){default:n.deepExtend(!0,this.model,{},t[i])}this.redraw()},f.prototype.unBindEvents=function(){var i=document.getElementById(this.rootId),n="",r=this.browserInfo(),u=r.isMSPointerEnabled,f=r.pointerEnabled,e=u?f?"pointerup":"MSPointerUp":"touchend mouseup",o=u?f?"pointermove":"MSPointerMove":"touchmove mousemove",t;for(n=e+" "+o,n=n.split(" "),t=0;t<n.length;t++)i.removeEventListener(n[t],this.sparkMouseMove);i.removeEventListener("mouseout",this.sparkMouseLeave)},f.prototype.bindClickEvents=function(n){this.sparkClick=this.sparkClick.bind(this);ej.isTouchDevice()?(this.sparkTouchStart=this.sparkTouchStart.bind(this),n.addEventListener("touchend",this.sparkClick),n.addEventListener("touchstart",this.sparkTouchStart)):(this.sparkDoubleClick=this.sparkDoubleClick.bind(this),this.sparkRightClick=this.sparkRightClick.bind(this),n.addEventListener("click",this.sparkClick),n.addEventListener("dblclick",this.sparkDoubleClick),n.addEventListener("contextmenu",this.sparkRightClick))},f.prototype.unBindClickEvents=function(n){ej.isTouchDevice()?(n.removeEventListener("touchend",this.sparkClick),n.removeEventListener("touchstart",this.sparkTouchStart)):(n.removeEventListener("click",this.sparkClick),n.removeEventListener("dblclick",this.sparkDoubleClick),n.removeEventListener("contextmenu",this.sparkRightClick))},f.prototype.bindEvents=function(n){var i;this.sparkMouseMove=this.sparkMouseMove.bind(this);this.sparkMouseLeave=this.sparkMouseLeave.bind(this);var t="",r=this.browserInfo(),u=r.isMSPointerEnabled,f=r.pointerEnabled,e=u?f?"pointerup":"MSPointerUp":"touchend mouseup",o=u?f?"pointermove":"MSPointerMove":"touchmove mousemove";for(t=e+" "+o,t=t.split(" "),i=0;i<t.length;i++)n.addEventListener(t[i],this.sparkMouseMove);n.addEventListener("mouseout",this.sparkMouseLeave)},f.prototype.sparkTouchStart=function(){this._longPressTimer=new Date},f.prototype.sparkClick=function(n){var t=new Date;this.model.click!=null&&this._trigger("click",{data:{event:n}});ej.isTouchDevice()&&n.type!="click"&&(this._doubleTapTimer!=null&&t-this._doubleTapTimer<300&&this.sparkDoubleClick(n),this._doubleTapTimer=t,this._longPressTimer!=null&&t-this._longPressTimer>1e3&&this.sparkRightClick(n))},f.prototype.sparkDoubleClick=function(n){this.model.doubleClick!=null&&this._trigger("doubleClick",{data:{event:n}})},f.prototype.sparkRightClick=function(n){this.model.rightClick!=null&&this._trigger("rightClick",{data:{event:n}})},f.prototype.sparkMouseMove=function(n){var hi=this.isTouch(n),et,oi,ki,wt,si,ni,ti,a,bt,v,ot;if(!(hi&&n.type.toString().toLowerCase().indexOf("move")>-1)){var t=this.model,st=this,ii=t.locale,ci=ii&&t.enableGroupSeparator,li,ai,ri=this.visiblePoints,vi=this.container.id+"_markerExplode",y,it,h,rt,l,b,lt=t.enableCanvasRendering?0:3,at="",s,u,k,d,g,vt=parseInt(t.size.height),ut=parseInt(t.size.width),e=t.tooltip.font,p=this.container.id+"_tooltip",gi=document.getElementById(p),yi,nt=t.tooltip.border.color,yt=document.getElementById(this.container.id),fr=yt.clientHeight,nr=yt.parentNode,ft=yt.getClientRects()[0],ht=ft.top,ct=ft.left,ui=n.clientX||(n.changedTouches?n.changedTouches[0].clientX:n.touches?n.touches[0].clientX:0),pi=n.clientY||(n.changedTouches?n.changedTouches[0].clientY:n.touches?n.touches[0].clientY:0),fi=ri.map(function(n){return n.location.X+ft.left}),er=ri.map(function(n){return n.location.markerPos+ft.top}),i=Infinity,ei=this.measureText,wi,o=this.model.markerSettings,pt,tt,w,bi,r,f,tr=document.getElementById(this.container.id+"_canvasTracker");for(et=0,ki=fi.length;et<ki;et++)oi=Math.abs(ui-fi[et]),i>oi&&(i=oi,pt=fi[et],this.pointIndex=et);if(ui>ct&&ui<ct+ft.width&&pi>ht&&pi<ht+ft.height)if(i=ri[this.pointIndex].location,wt=function(n){wi=n.length/2;var o=ei(n,e);s=o.height+4;u=o.width+wi;r=i.X;f=i.markerPos;f-s/2<0?f+=s/2:f+s/2>vt&&(f-=s/2);r+u+u/10>ut-t.padding?(r=i.X-Number(t.markerSettings.width)-Number(t.markerSettings.border.width)-4,at="M "+r+" "+i.markerPos+" L "+(r-u/10)+" "+(f-4)+" L "+(r-u/10)+" "+(f-s/2)+" L "+(r-u-u/10)+" "+(f-s/2)+" L "+(r-u-u/10)+" "+(f+s/2)+" L "+(r-u/10)+" "+(f+s/2)+" L "+(r-u/10)+" "+(f+4)+" Z"):(r=i.X+Number(t.markerSettings.width)+Number(t.markerSettings.border.width)+4,at="M "+r+" "+i.markerPos+" L "+(r+u/10)+" "+(f-4)+" L "+(r+u/10)+" "+(f-s/2)+" L "+(r+u+u/10)+" "+(f-s/2)+" L "+(r+u+u/10)+" "+(f+s/2)+" L "+(r+u/10)+" "+(f+s/2)+" L "+(r+u/10)+" "+(f+4)+" Z")},(t.tooltip.template==null||t.tooltip.template=="")&&(li=ci&&i.Xval?i.Xval.toLocaleString(ii):i.Xval,ai=ci&&i.Yval?i.Yval.toLocaleString(ii):i.Yval,h=" X : "+li+" Y : "+ai+" ",wt(h)),y=this.pointIndex,this.prevMousePos==undefined&&(this.prevMousePos=pt),si=function(n){var i=st.highPointIndex,r=st.lowPointIndex,u=st.negativePointIndexes,f=st.startPointIndex,e=st.endPointIndex;return n==i?t.highPointColor:n==r?t.lowPointColor:n==f&&t.startPointColor!=null?t.startPointColor:n==e&&t.endPointColor!=null?t.endPointColor:u.indexOf(n)>=0&&t.negativePointColor!=null?t.negativePointColor:o.fill?o.fill:t.fill},gi||tr){if(this.prevMousePos!=pt){if(k={data:{pointIndex:y,currentText:h,location:i}},this.model.tooltipInitialize!=null&&this._trigger("tooltipInitialize",k),yi=i.X-t.width/2,h!=k.data.currentText&&(h=k.data.currentText,wt(h)),this.prevMousePos=pt,g=si(y),rt={id:vi,cx:i.X,cy:i.markerPos,r:Number(o.width),fill:o.border.color,stroke:g,"stroke-width":Number(o.border.width)},t.tooltip.template==null||t.tooltip.template=="")l={id:p,fill:t.tooltip.fill,stroke:nt!=null?nt:g,"stroke-width":t.tooltip.border.width,d:at},b={x:i.X+u+u/10>ut-t.padding?r+lt-u-u/10:r+lt+u/10,y:f+s/4,fill:e.color,"font-size":e.size,"font-family":e.fontFamily,"font-weight":e.fontWeight,"font-style":e.fontStyle};else{a=document.getElementById(t.tooltip.template);bt=a.innerHTML;typeof t.dataSource[y]=="object"?(v=t.dataSource[y],v.x=i.Xval,v.y=i.Yval):v={x:i.Xval,y:i.Yval};var kt={point:v},bt=this.parseTemplate(a,kt),di=a.innerText,dt=Number(t.markerSettings.width),di=this.parseTemplate(a,kt),c=ei(di,e),gt=i.X+Number(o.width)+Number(o.border.width)+dt;f=ht+i.markerPos-c.height/2;r=ct+gt;i.markerPos-c.height/2<t.padding?f+=c.height/2:i.markerPos+c.height/2>vt-t.padding&&(f-=c.height/2);gt+c.width>ut-t.padding&&(r=r-c.width-(Number(o.width)+Number(o.border.width)+dt)*2);l={background:t.tooltip.fill,border:t.tooltip.border.width+"px solid "+(nt!=null?nt:g),left:r+"px",top:f+"px"};d=document.getElementById(p);d.innerHTML=bt;this.setStyles(l,d)}t.enableCanvasRendering?(tt=document.getElementById(this.container.id+"_canvasTracker"),w=tt.getContext("2d"),w.clearRect(0,0,ut,vt),this.canvasDrawCircle(rt,w),(t.tooltip.template==null||t.tooltip.template=="")&&(this.canvasDrawPath(l,w),b.font=e.size+" "+e.fontFamily,this.canvasDrawText(b,h,w))):(ni=document.getElementById(p+"_g"),this.drawCircle(rt,ni),t.tooltip.template==null&&(this.drawPath(l,ni),it=yt.getElementsByTagName("text")[0],it.textContent=h,this.setAttributes(b,it),ni.appendChild(it)))}}else{if(k={data:{pointIndex:y,currentText:h,location:i}},this.model.tooltipInitialize!=null&&this._trigger("tooltipInitialize",k),yi=i.X-t.width/2,h!=k.data.currentText&&(h=k.data.currentText,wt(h)),ti=document.getElementsByClassName("ej-sparkline-tooltip"),ti[0]&&ti[0].parentNode.removeChild(ti[0]),g=si(y),rt={id:vi,cx:i.X,cy:i.markerPos,r:Number(o.width),fill:o.border.color,stroke:g,"stroke-width":Number(o.border.width)},t.tooltip.template==null||t.tooltip.template=="")l={id:p,fill:t.tooltip.fill,stroke:nt!=null?nt:g,"stroke-width":t.tooltip.border.width,d:at},b={x:i.X+u+u/10>ut-t.padding?r+lt-u-u/10:r+lt+u/10,y:f+s/4,fill:e.color,opacity:e.opacity,"font-size":e.size,"font-family":e.fontFamily,"font-weight":e.fontWeight,"font-style":e.fontStyle};else{a=document.getElementById(t.tooltip.template);bt=a.innerHTML;typeof t.dataSource[y]=="object"?(v=t.dataSource[y],v.x=i.Xval,v.y=i.Yval):v={x:i.Xval,y:i.Yval};var kt={point:v},bt=this.parseTemplate(a,kt),di=a.innerText,dt=Number(t.markerSettings.width),di=this.parseTemplate(a,kt),c=ei(di,e),gt=i.X+Number(o.width)+Number(o.border.width)+dt;f=ht+i.markerPos-c.height/2;r=ct+gt;i.markerPos-c.height/2<t.padding?f+=c.height/2:i.markerPos+c.height/2>vt-t.padding&&(f-=c.height/2);gt+c.width>ut-t.padding&&(r=r-c.width-(Number(o.width)+Number(o.border.width)+dt)*2);l={background:t.tooltip.fill,border:t.tooltip.border.width+"px solid "+(nt!=null?nt:g),left:r+"px",top:f+"px",position:"fixed",height:"auto",width:"auto",display:"block",opacity:e.opacity,font:e.size+" "+e.fontFamily};d=document.createElement("div");this.setAttributes({id:p,"class":"ej-sparkline-tooltip"},d);this.setStyles(l,d);d.innerHTML=bt;nr.appendChild(d)}t.enableCanvasRendering?(tt=document.createElement("canvas"),bi={id:this.container.id+"_canvasTracker",fill:"transparent","class":"ej-sparkline-tooltip",height:t.size.height,width:t.size.width},this.setAttributes(bi,tt),this.setStyles({left:ct+"px",top:ht+"px",position:"fixed"},tt),w=tt.getContext("2d"),this.canvasDrawCircle(rt,w),(t.tooltip.template==null||t.tooltip.template=="")&&(this.canvasDrawPath(l,w),b.font=e.size+" "+e.fontFamily,this.canvasDrawText(b,h,w)),this.container.parentNode.appendChild(tt)):(ot=document.createElementNS(this.svgLink,"g"),this.setAttributes({id:p+"_g","class":"ej-sparkline-tooltip"},ot),this.container.appendChild(ot),this.drawCircle(rt,ot),t.tooltip.template==null&&(this.drawPath(l,ot),it=this.createText(b,h),ot.appendChild(it)))}if(hi){var ir=t.tooltip.template==null||t.tooltip.template==""?t.enableCanvasRendering?this.container.id+"_canvasTracker":p+"_g":p,rr=document.getElementById(ir),ur=this;setTimeout(function(){ur.fadeOut(rr)},1e3)}}},f.prototype.sparkMouseLeave=function(n){var f=n.clientX||(n.touches[0]?n.touches[0].clientX:n.changedTouches[0].clientX),e=n.clientY||(n.touches[0]?n.touches[0].clientY:n.changedTouches[0].clientY),o=this.container,v=o.clientHeight,r=o.getClientRects()[0],s=r.top,h=r.left,c=this.model,l=this.container.id+"_tooltip",t,a,u,i;(f<h||f>h+r.width||e<s||e>s+r.height||this.touchEnd)&&(c.tooltip.template!=null&&c.tooltip.template!=""&&(t=document.getElementById(l),t?t.parentNode.removeChild(t):t),this.model.enableCanvasRendering?(i=document.getElementById(this.container.id+"_canvasTracker"),i?i.parentNode.removeChild(i):i):(a=l+"_g",u=document.getElementById(a),u?this.container.removeChild(u):u))},f.prototype.bindPieEvents=function(n){var i;this.pieTooltip=this.pieTooltip.bind(this);this.pieTooltipHide=this.pieTooltipHide.bind(this);var t="",r=this.browserInfo(),u=r.isMSPointerEnabled,f=r.pointerEnabled,e=u?f?"pointerup":"MSPointerUp":"touchend mouseup",o=u?f?"pointermove":"MSPointerMove":"touchmove mousemove";for(t=e+" "+o,t=t.split(" "),i=0;i<t.length;i++)n.addEventListener(t[i],this.pieTooltip);n.addEventListener("mouseout",this.pieTooltipHide);this.model.pointRegionMouseClick!=null&&(n.addEventListener("click",this.pieTooltip),n.addEventListener("touchstart",this.pieTooltip))},f.prototype.unbindPieEvents=function(){var n="",i=this.browserInfo(),r=i.isMSPointerEnabled,u=i.pointerEnabled,e=r?u?"pointerup":"MSPointerUp":"touchend mouseup",o=r?u?"pointermove":"MSPointerMove":"touchmove mousemove",f=document.getElementById(this.rootId),t;for(n=e+" "+o,n=n.split(" "),t=0;t<n.length;t++)f.removeEventListener(n[t],this.pieTooltip);f.removeEventListener("mouseout",this.pieTooltipHide)},f.prototype.pieTooltip=function(n){var y=this.isTouch(n),e,ot,i,s,st,ht;if(y&&n.stopImmediatePropagation(),!(y&&n.type.toString().toLowerCase().indexOf("move")>-1)){this.model.sparklineMouseMove!=null&&this._trigger("sparklineMouseMove");var t=this.model,h=n.clientX||(n.changedTouches?n.changedTouches[0].clientX:n.touches?n.touches[0].clientX:0),yt=parseInt(t.size.height),pt=parseInt(t.size.width),u=t.tooltip.font,c=n.clientY||(n.changedTouches?n.changedTouches[0].clientY:n.touches?n.touches[0].clientY:0),d=document.getElementById(this.container.id),wt=d.parentNode,g=d.getClientRects()[0],ct=g.top,lt=g.left,bt=this.visiblePoints.map(function(n){return n.coordinates}),nt=this.visiblePoints.map(function(n){return n.location}),at=this.visiblePoints.radius,tt=this.visiblePoints.centerPos,p=this.visiblePoints,it,rt,f,w=h-(tt.X+lt),b=c-(tt.Y+ct),o=Math.atan2(b,w),ut=this.container.id+"_pieTooltip",r=document.getElementById(ut),l,a=t.tooltip.border.color,vt=Math.sqrt(w*w+b*b),k=t.pointRegionMouseClick!=null?"pointRegionMouseClick":t.pointRegionMouseMove!=null?"pointRegionMouseMove":null,v=t.palette,ft=t.locale,et=t.enableGroupSeparator;if(Math.abs(vt)<=at)for(e=0,ot=p.length;e<ot;e++)it=p[e].stAng,rt=p[e].endAng,o=o<0?6.283+o:o,o<=rt&&o>=it&&(f=e,(k=="pointRegionMouseClick"&&(n.type=="click"||n.type=="touchstart")||k=="pointRegionMouseMove")&&this._trigger(k,{data:{pointIndex:e,seriesType:"Pie",locationX:h,locationY:c}}));else r&&r.parentNode.removeChild(r);r&&f!=null?(i=nt[f].Percent.toFixed(2),i=et?"&nbsp"+parseFloat(i).toLocaleString(ft):i+"&nbsp%&nbsp",s=this.measureText(i,u),l={left:h+12+"px",top:c+"px",border:t.tooltip.border.width+"px solid "+(a!=null?a:v[f%v.length]),"background-color":t.tooltip.fill,height:s.height+"px",width:s.width+"px"},this.setStyles(l,r),r.innerHTML=i):f!=null&&(r=document.createElement("div"),i=nt[f].Percent.toFixed(2),i=et?"&nbsp"+parseFloat(i).toLocaleString(ft):i+"&nbsp%&nbsp",s=this.measureText(i,u),l={left:h+12+"px",top:c+"px","background-color":t.tooltip.fill,color:u.color,border:t.tooltip.border.width+"px solid "+(a!=null?a:v[f%v.length]),height:s.height+"px",width:s.width+"px","font-size":u.size,opacity:u.opacity,"font-weight":u.fontWeight,"font-family":u.fontFamily,"font-style":u.fontStyle,"z-index":"100000",position:"fixed"},r.setAttribute("id",ut),this.setStyles(l,r),r.innerHTML=i,document.body.appendChild(r));y&&(this.touchEnd=!0,st=document.getElementById(this.container.id+"_pieTooltip"),ht=this,setTimeout(function(){ht.fadeOut(st)},500))}},f.prototype.pieTooltipHide=function(){if(!this.touchEnd){this.model.sparklineMouseLeave!=null&&this._trigger("sparklineMouseLeave");var t=this.container.id+"_pieTooltip",n=document.getElementById(t);n?document.body.removeChild(n):n}},f.prototype.bindRegionEvents=function(n){this.sparklineEvent=this.sparklineEvent.bind(this);var t=this.findBrowser();(t=="IE"||t=="firefox")&&(n.style["touch-action"]="none");this.model.pointRegionMouseMove!=null&&(n.addEventListener("mousemove",this.sparklineEvent,!0),n.addEventListener("touchmove",this.sparklineEvent,!0));this.model.pointRegionMouseClick!=null&&(n.addEventListener("click",this.sparklineEvent,!0),n.addEventListener("touchstart",this.sparklineEvent,!0));this.model.sparklineMouseMove!=null&&(n.addEventListener("mousemove",this.sparklineEvent,!0),n.addEventListener("touchmove",this.sparklineEvent,!0));this.model.sparklineMouseLeave!=null&&(this.sparklineLeave=this.sparklineLeave.bind(this),n.addEventListener("mouseout",this.sparklineLeave,!0),t=="firefox"&&n.addEventListener("mouseup",this.sparklineLeave,!0),n.addEventListener("touchend",this.sparklineLeave,!0))},f.prototype.sparklineLeave=function(){this.model.sparklineMouseLeave!=null&&this._trigger("sparklineMouseLeave")},f.prototype.sparklineEvent=function(n){var t,p;this.model.sparklineMouseMove!=null&&this._trigger("sparklineMouseMove");var s=this.model,l=n.clientX||n.touches[0].clientX,a=n.clientY||n.touches[0].clientY,f=s.type,r=this.visiblePoints,e,o,v,y,u,h,w=document.getElementById(this.container.id),g=w.parentNode,b=w.getClientRects()[0],k=b.top,d=b.left,c;if(c=s.pointRegionMouseClick!=null?"pointRegionMouseClick":s.pointRegionMouseMove!=null?"pointRegionMouseMove":null,f==i.Line||f==i.Area)for(u=2*(s.markerSettings.width+s.markerSettings.border.width),h=u,t=0,p=r.length;t<p;t++)e=d+r[t].location.X-u/2,o=k+r[t].location.Y-h/2,v=e+u,y=o+h,l>=e&&l<=v&&a>=o&&a<=y&&c!=null&&this._trigger(c,{data:{pointIndex:t,seriesType:f,locationX:r[t].location.X,locationY:r[t].location.Y}});else if(f==i.Column||f==i.WinLoss)for(u=r[0].location.width,t=0,p=r.length;t<p;t++)h=r[t].location.height,e=d+r[t].location.X-u/2,o=k+r[t].location.Y,v=e+u,y=o+h,l>=e&&l<=v&&a>=o&&a<=y&&c!=null&&this._trigger(c,{data:{pointIndex:t,seriesType:f,locationX:r[t].location.X,locationY:r[t].location.Y}})},f.prototype.findBrowser=function(){return navigator.userAgent.indexOf("Firefox")!=-1?"firefox":navigator.userAgent.indexOf("MSIE")!=-1||!!document.documentMode==!0?"IE":null},f.prototype.resize=function(){var t=this,n=document.getElementById(this.rootId);n!=null||n!=undefined?(window.removeEventListener("resize",this.resize),this.isResized=!0,this._destroy(),this.createSvg(),this.renderSparkline(),this.isResized=!1):window.removeEventListener("resize",this.resize)},f.prototype.supportsSvg=function(){return!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg")},f.prototype._init=function(){this.supportsSvg()&&(this.rootId=this.rootId!=null?this.rootId:this._id,this.parentElement=this.parentElement!=null?this.parentElement:document.getElementById(this.rootId),this.model.load!=null&&this._trigger("load"),this.setTheme(this.model),this.height=parseInt(this.model.size.height),this.width=parseInt(this.model.size.width),this.createSvg(),this.renderSparkline(),this.model.loaded!=null&&this._trigger("loaded"))},f.prototype.redraw=function(){this.emptyContainer();this.renderSparkline()},f.prototype.touchCheck=function(n){this.touchEnd=!0;this.model.type.toString().toLocaleLowerCase()!="pie"?this.sparkMouseLeave(n):this.pieTooltipHide(n)},f.prototype.emptyContainer=function(){var t=this.model,n;if(t.enableCanvasRendering)this.ctx.clearRect(0,0,this.height,this.width);else for(n=this.container;n.firstChild;)n.removeChild(n.firstChild)},f.prototype._destroy=function(){var n=document.getElementById(this.container.id);n!=null?(this.unBindEvents(),this.unbindPieEvents(),window.removeEventListener("resize",this.resize),n.parentNode.removeChild(n)):window.removeEventListener("resize",this.resize)},f.prototype.renderSparkline=function(){this.setStyles({background:this.model.background},this.container);this.calculatePoints();this.seriesRender();this.resize=this.resize.bind(this);this.model.isResponsive?window.addEventListener("resize",this.resize):window.removeEventListener("resize",this.resize);this.model.type!=i.WinLoss&&this.model.type!=i.Pie&&this.model.tooltip.visible?this.bindEvents(this.parentElement):this.unBindEvents();this.model.type==i.Pie&&this.model.tooltip.visible?this.bindPieEvents(this.parentElement):this.unbindPieEvents();this.bindRegionEvents(this.parentElement);this.unBindClickEvents(this.parentElement);this.bindClickEvents(this.parentElement)},f.prototype.animateSparkline=function(){function ht(){n<=a?r.setAttribute("width",n.toString()):(clearInterval(it),h._trigger("animationComplete"));n=n+t}function ct(){f=u-n;n>=0?(r.setAttribute("y",n.toString()),r.setAttribute("height",f)):(clearInterval(ut),h._trigger("animationComplete"));n=n-t}function lt(){n<=u?r.setAttribute("height",n.toString()):(clearInterval(rt),h._trigger("animationComplete"));n=n+t}function at(){f=(l-n)*2;n>=-t?(r.setAttribute("y",n<0?0:n.toString()),r.setAttribute("height",f)):(clearInterval(ft),h._trigger("animationComplete"));n=n-t}function vt(){n<=v.length-1?(d=90,et=v[n].location.Degree,ot+=et+(n==0?d:0),b=(d-90)*Math.PI/180,p=(ot-90)*Math.PI/180,n==v.length-1&&(p-=.0001),f={sX:o.X+s*Math.cos(b),sY:o.Y+s*Math.sin(b),eX:o.X+s*Math.cos(p),eY:o.Y+s*Math.sin(p)},k="M "+o.X+" "+o.Y+" L "+f.eX+" "+f.eY+" A "+s+" "+s+" 0 1,0 "+f.sX+" "+f.sY+" Z",r.setAttribute("d",k)):(clearInterval(st),h._trigger("animationComplete"));n+=1}var y=this.model,h=this,c=y.type,g=y.padding,l=this.axisHeight,a=parseInt(y.size.width),u=parseInt(y.size.height),n,f,t,nt=document.createElementNS(this.svgLink,"defs"),w=document.createElementNS(this.svgLink,"clipPath"),e,tt,r,it,rt,ut,ft,st;if(r=c==i.Pie?document.createElementNS(this.svgLink,"path"):document.createElementNS(this.svgLink,"rect"),c==i.Line||c==i.Area)e={id:"clipRectSparkline",x:0,y:0,width:0,height:u},t=a/10,it=setInterval(ht,120),n=t;else if(c==i.Column||c==i.WinLoss)l==g?(e={id:"clipRectSparkline",x:0,y:0,width:a,height:0},t=u/10,n=t,rt=setInterval(lt,120)):l==u-g?(e={id:"clipRectSparkline",x:0,y:u,width:a,height:0},t=u/10,n=u,ut=setInterval(ct,120)):(e={id:"clipRectSparkline",x:0,y:l,width:a,height:0},t=u/10,n=l,ft=setInterval(at,120));else{var v=this.visiblePoints,o=v.centerPos,s=v.radius,et,b,p,k="",d=90,ot=0;n=0;e={id:"clipRectSparkline",d:k};st=setInterval(vt,120)}tt={id:this.container.id+"_sparklineRect"};this.setAttributes(e,r);this.setAttributes(tt,w);w.appendChild(r);nt.appendChild(w);this.container.appendChild(nt)},f.prototype.setTheme=function(n){var i=n.theme,t=this.defaults;i==r.flatdark?(n.background=n.background==t.background?"#111111":n.background,n.fill=n.fill==t.fill?"#B5B5B5":n.fill,n.stroke=n.stroke==t.stroke?"#F6D321":n.stroke,n.axisLineSettings.color=n.axisLineSettings.color==t.axisLineSettings.color?"#AAAAAA":n.axisLineSettings.color):i==r.gradientlight?(n.background=n.background==t.background?"transparent":n.background,n.fill=n.fill==t.fill?"#F34649":n.fill,n.stroke=n.stroke==t.stroke?"#597B15":n.stroke,n.axisLineSettings.color=n.axisLineSettings.color==t.axisLineSettings.color?"#8E8E8E":n.axisLineSettings.color):i==r.gradientdark?(n.background=n.background==t.background?"#111111":n.background,n.fill=n.fill==t.fill?"#005378":n.fill,n.stroke=n.stroke==t.stroke?"#6A9319":n.stroke,n.axisLineSettings.color=n.axisLineSettings.color==t.axisLineSettings.color?"#AAAAAA":n.axisLineSettings.color):i==r.azuredark?(n.background=n.background==t.background?"#111111":n.background,n.fill=n.fill==t.fill?"#007fff":n.fill,n.stroke=n.stroke==t.stroke?"#f0ffff":n.stroke,n.axisLineSettings.color=n.axisLineSettings.color==t.axisLineSettings.color?"#336699":n.axisLineSettings.color):i==r.azurelight?(n.background=n.background==t.background?"transparent":n.background,n.fill=n.fill==t.fill?"#336699":n.fill,n.stroke=n.stroke==t.stroke?"#007fff":n.stroke,n.axisLineSettings.color=n.axisLineSettings.color==t.axisLineSettings.color?"#336699":n.axisLineSettings.color):i==r.limedark?(n.background=n.background==t.background?"#111111":n.background,n.fill=n.fill==t.fill?"#238f23":n.fill,n.stroke=n.stroke==t.stroke?"#32CD32":n.stroke,n.axisLineSettings.color=n.axisLineSettings.color==t.axisLineSettings.color?"#43da21":n.axisLineSettings.color):i==r.limelight?(n.background=n.background==t.background?"transparent":n.background,n.fill=n.fill==t.fill?"#238f23":n.fill,n.stroke=n.stroke==t.stroke?"#32CD32":n.stroke,n.axisLineSettings.color=n.axisLineSettings.color==t.axisLineSettings.color?"#43da21":n.axisLineSettings.color):i==r.saffrondark?(n.background=n.background==t.background?"#111111":n.background,n.fill=n.fill==t.fill?"#ffaa33":n.fill,n.stroke=n.stroke==t.stroke?"#ffdba9":n.stroke,n.axisLineSettings.color=n.axisLineSettings.color==t.axisLineSettings.color?"#ffc26e":n.axisLineSettings.color):i==r.saffronlight?(n.background=n.background==t.background?"transparent":n.background,n.fill=n.fill==t.fill?"#ffaa33":n.fill,n.stroke=n.stroke==t.stroke?"#ffdba9":n.stroke,n.axisLineSettings.color=n.axisLineSettings.color==t.axisLineSettings.color?"#ffc26e":n.axisLineSettings.color):(i==null||i==r.flatlight)&&(n.background=n.background==t.background?"transparent":n.background,n.fill=n.fill==t.fill?"#33ccff":n.fill,n.stroke=n.stroke==t.stroke?"#33ccff":n.stroke,n.axisLineSettings.color=n.axisLineSettings.color==t.axisLineSettings.color?"#FF0000":n.axisLineSettings.color)},f.prototype.createSvg=function(){var i=this.model,t=this.parentElement.clientWidth>0?this.parentElement:this.parentElement.parentElement,e,u=parseInt(t.style.height)>t.clientHeight?parseInt(t.style.height):t.clientHeight,f=parseInt(t.style.width)>t.clientWidth?parseInt(t.style.width):t.clientWidth,n=i.size.width==""?f:parseInt(i.size.width),r=i.size.height==""?u:parseInt(i.size.height);r=r>0?r:u>0?u:30;n=this.model.isResponsive&&this.isResized?f>0?f:n:n;n=n<=0?50:n;i.size.height=r.toString();i.size.width=n.toString();this.height=r;this.width=n;e={id:this.rootId+"_sparkline_svg",width:n,height:r};i.enableCanvasRendering?(this.container=document.createElement("canvas"),this.ctx=this.container.getContext("2d")):this.container=document.createElementNS(this.svgLink,"svg");this.setAttributes(e,this.container);this.parentElement.appendChild(this.container)},f.prototype.setStyles=function(n,t){for(var r=Object.keys(n),u,f=r.map(function(t){return n[t]}),i=0,e=r.length;i<e;i++)u=r[i],t.style[u]=f[i]},f.prototype.setAttributes=function(n,t){for(var r=Object.keys(n),u=r.map(function(t){return n[t]}),i=0,f=r.length;i<f;i++)t.setAttribute(r[i],u[i])},f.prototype.getDefaultPoints=function(n){for(var t=[],i=1;i<=n;i++)Math.random()*10>5&&t.push(-Math.round(Math.random()*100)),t.push(Math.round(Math.random()*100));return t},f.prototype.calculatePoints=function(){var n=this.model,a=n.type,r=n.dataSource!=null?n.dataSource:this.getDefaultPoints(12),f,v,e,w,k,ft=[],h=r.length,c,d=0,y,et,l,t,ut,ot,st,ht;if(Array.isArray(r)&&typeof r[0]!="object")if(n.type==i.Pie)for(t=0;t<h;t++)d+=Math.abs(r[t]);else v=Math.max.apply(null,r),e=Math.min.apply(null,r),w=0,k=h-1;else if(n.type==i.Pie)for(t=0;t<h;t++)d+=Math.abs(r[t][n.yName]);else r[0][n.xName]?(c=r,c=c.sort(function(t,i){return t[n.yName]-i[n.yName]}),v=c[c.length-1][n.yName],e=c[0][n.yName]):(y=r.map(function(t){return t[n.yName]}),v=Math.max.apply(null,y),e=Math.min.apply(null,y)),r[0][n.xName]?(c=c.sort(function(t,i){return t[n.xName]-i[n.xName]}),k=c[c.length-1][n.xName],w=c[0][n.xName]):(w=0,k=h-1);if(n.type!=i.Pie){this.maxLength=h;var l,b=Number(n.padding),u=parseInt(n.size.height)-b*2,tt=parseInt(n.size.width)-b*2,g=k-w,o=v-e,g=g==0?1:g;o=o==0?1:o;this.min=e;this.unitX=g;this.minX=w;this.unitY=o;this.max=v;this.maxX=k;var it=0,p=u-u/o*-e,s,p=e<0&&v<=0?0:e<0&&v>0?p:u;this.axisHeight=p+b;a!=i.WinLoss&&n.axisLineSettings.visible&&this.drawAxis()}for(t=0;t<h;t++){if(r[t][n.xName]||r[t][n.yName]||r[t][n.yName]==0?r[t][n.xName]?(y=r[t][n.xName],f=r[t][n.yName]):(y=t,f=r[t][n.yName]):(y=t,f=r[t]),a==i.Line||a==i.Area)s=h!=1?u-Math.round(u*((f-e)/o)):0,l={X:h!=1?Math.round(tt*((y-w)/g)):tt/2,Y:s,markerPos:s};else if(a==i.Column||a==i.WinLoss){var nt=tt/h,rt=.5*2;nt-=rt;it=t*(nt+rt)+rt/2;a==i.WinLoss?(ut=.5,ot=40,s=f>0?u/4:f<0?u*ut:u*ut-u/ot,l={X:it,Y:s,height:f!=0?u/4:u/20,width:nt}):(st=u/o*(f-e),ht=f==e&&f>0?h!=1&&o!=1?u/o*(e/2):u/o:f==v&&f<0&&h!=1&&o!=1?u/o*(-v/2):st,s=Math.abs(u-ht),l={X:it,Y:s>p?p:s,height:Math.abs(s-p),width:nt,markerPos:s>p?p+Math.abs(s-p):s})}else a==i.Pie&&(et=Math.abs(f)/d*100,l={Percent:et,Degree:Math.abs(f)/d*360});a!=i.Pie&&(l.X+=b,l.Y+=b);a!=i.WinLoss&&(l.markerPos+=b);l.Xval=y;l.Yval=f;ft.push({location:l})}this.visiblePoints=ft},f.prototype.seriesRender=function(){var t=this.model,r=this.visiblePoints,f=r.length,u,n=t.type;u={data:{minX:this.minX,minY:this.min,maxX:this.maxX,maxY:this.max,xName:t.xName,yName:t.yName,pointsCount:f,seriesType:n,visiblePoints:this.visiblePoints}};this.model.seriesRendering!=null&&this._trigger("seriesRendering",u);n==i.Line?this.drawLineSeries(r):n==i.Area?this.drawAreaSeries(r):n==i.Column?this.drawColumnSeries(r):n==i.WinLoss?this.drawWinlossSeries(r):n==i.Pie&&this.drawPieSeries();t.markerSettings.visible&&n!=i.WinLoss&&n!=i.Pie&&this.drawMarker(r);t.rangeBandSettings.startRange!=null&&t.rangeBandSettings.endRange!=null&&n!=i.WinLoss&&n!=i.Pie&&this.drawRangeBand()},f.prototype.drawPieSeries=function(){var n=this.model,r=this.visiblePoints,i,c,b,y;n.padding=n.padding==this.defaults.padding?2:n.padding;var nt=r.length,l=parseInt(n.size.height)-n.padding*2,a=parseInt(n.size.width)-n.padding*2,u=l<=a?l/2:a/2,f,e,t={X:a/2,Y:l/2},o,p,v,w=n.palette,s=0,k=n.border.color,d=n.opacity,g=n.border.width,h;for(r.centerPos=t,r.radius=u,i=0,c=90;i<r.length;i++)c+=s,s=r[i].location.Degree,b=c+s,f=(c-90)*Math.PI/180,e=(b-90)*Math.PI/180,r[i].stAng=f,r[i].endAng=e,y=s<180?"0":"1",o=r[i].coordinates={sX:t.X+u*Math.cos(f),sY:t.Y+u*Math.sin(f),eX:t.X+u*Math.cos(e),eY:t.Y+u*Math.sin(e)},p="M "+t.X+" "+t.Y+" L "+o.eX+" "+o.eY+" A "+u+" "+u+" 0 "+y+",0 "+o.sX+" "+o.sY+" Z",v={id:this.container.id+"_pieBase"+i,fill:w[i%w.length],stroke:k,opacity:d,"stroke-width":g,d:p,start:e,end:f,x:t.X,y:t.Y,counterClockWise:y,radius:u},n.enableCanvasRendering?this.canvasDrawPath(v):(h=this.createGroup({id:"sparkpieSeries"}),this.setStyles({"clip-path":"url(#"+this.container.id+"_sparklineRect)"},h),this.container.appendChild(h),this.drawPath(v,h))},f.prototype.drawRangeBand=function(){var n=this.model,t=parseInt(n.size.height)-n.padding*2,f=parseInt(n.size.width)-n.padding*2,o=n.rangeBandSettings.startRange,s=n.rangeBandSettings.endRange,i=Number(t-t/this.unitY*(o-this.min))+n.padding,r=Number(t-t/this.unitY*(s-this.min))+n.padding,u,e;r=r>Number(t+n.padding)?Number(t+n.padding):r<0+n.padding?0+n.padding:r;i=i>Number(t+n.padding)?Number(t+n.padding):i<0+n.padding?0+n.padding:i;e="M "+n.padding+" "+i+" L "+(f+Number(n.padding))+" "+i+" L "+(f+Number(n.padding))+" "+r+" L "+n.padding+" "+r+" Z";u={id:this.container.id+"_rangeBand",fill:n.rangeBandSettings.color,opacity:n.rangeBandSettings.opacity,stroke:"transparent","stroke-width":n.border.width,d:e};n.enableCanvasRendering?this.canvasDrawPath(u):this.drawPath(u)},f.prototype.drawAxis=function(){var n=this.model,r=this.axisHeight,u=n.axisLineSettings.color,f=n.type,t;f!=i.WinLoss&&f!=i.Pie&&(n.axisLineSettings.visible||(u="transparent"),t={id:this.container.id+"_Sparkline_XAxis",x1:0+n.padding,y1:r,x2:parseInt(n.size.width)-n.padding,y2:r,stroke:u,"stroke-dasharray":n.axisLineSettings.dashArray,"stroke-width":n.axisLineSettings.width},n.enableCanvasRendering?this.canvasDrawLine(t):this.drawLine(t))},f.prototype.drawColumnSeries=function(t){var f,u,r=this.model,c=t.length,w=n.compareExtend({},[],t),b=r.border.width,l,k=r.opacity,a,d=r.fill,g=r.border.color,o=r.highPointColor,s=r.lowPointColor,v=r.startPointColor,y=r.endPointColor,p=r.negativePointColor,e,h,i;for((o||s)&&(h=w.map(function(n){return n.location.markerPos}),a=Math.min.apply(null,h),l=Math.max.apply(null,h)),r.enableCanvasRendering==!1&&(e=this.createGroup({id:this.container.id+"sparkcolumnSeries"}),this.setStyles({"clip-path":"url(#"+this.container.id+"_sparklineRect)"},e),this.container.appendChild(e)),i=0;i<c;i++)u=t[i].location,f={id:this.container.id+"_column_series_"+i,x:u.X,y:u.Y,height:u.height,width:u.width,fill:d,stroke:g,opacity:k,"stroke-width":b},u.markerPos==a&&o?(f.fill=o,this.highPointIndex=i):u.markerPos==l&&s?(f.fill=s,this.lowPointIndex=i):i==0&&v?(f.fill=v,this.startPointIndex=i):i==c-1&&y?(f.fill=y,this.endPointIndex=i):u.markerPos>=this.axisHeight&&p&&(f.fill=p,this.negativePointIndexes.push(i)),r.enableCanvasRendering?this.canvasDrawRectangle(f):this.drawRect(f,e),u.X+=u.width/2},f.prototype.drawWinlossSeries=function(n){var f,i,t=this.model,s=t.border.width,e=t.padding,h=t.border.color,c=t.opacity,l=n.length,o=parseInt(t.size.height)-e*2,u,r;for(t.enableCanvasRendering==!1&&(u=this.createGroup({id:this.container.id+"sparkwinlossSeries"}),this.setStyles({"clip-path":"url(#"+this.container.id+"_sparklineRect)"},u),this.container.appendChild(u)),r=0;r<l;r++)i=n[r].location,f={id:this.container.id+"_winloss_series_"+r,x:i.X,y:i.Y,height:i.height,width:i.width,fill:i.Y<o/2+e?i.Y>o/4+e?"#EE82EE":t.fill:t.negativePointColor?t.negativePointColor:"#FF0000",stroke:h,opacity:c,"stroke-width":s},t.enableCanvasRendering?this.canvasDrawRectangle(f):this.drawRect(f,u)},f.prototype.drawAreaSeries=function(n){for(var r="",e=this.axisHeight,t=this.model,f,u,i=0,o=n.length;i<o;i++)i==0&&(r="M "+n[0].location.X+" "+e+" "),r+="L "+n[i].location.X+" "+n[i].location.Y+" ",i==o-1&&(r+="L "+n[i].location.X+" "+e+" Z");f={id:this.container.id+"_area_series_fill",fill:t.fill,stroke:t.stroke?t.stroke:t.fill,"fill-opacity":t.opacity,"stroke-width":t.width,d:r};t.enableCanvasRendering?this.canvasDrawPath(f):(u=this.createGroup({id:"sparkAreaSeries"}),this.container.appendChild(u),this.drawPath(f,u),this.setStyles({"clip-path":"url(#"+this.container.id+"_sparklineRect)"},u))},f.prototype.drawLineSeries=function(n){for(var f,u="",t=this.model,r,i=0,e=n.length;i<e;i++)i==0&&(u="M "+n[0].location.X+" "+n[i].location.Y+" "),u+="L "+n[i].location.X+" "+n[i].location.Y+" ";f={id:this.container.id+"_Line_series",fill:"transparent",stroke:t.stroke?t.stroke:t.fill,opacity:t.opacity,"stroke-width":t.width,d:u};t.enableCanvasRendering?this.canvasDrawPath(f):(r=this.createGroup({id:"sparklineSeries"}),this.container.appendChild(r),this.drawPath(f,r),this.setStyles({"clip-path":"url(#"+this.container.id+"_sparklineRect)"},r))},f.prototype.drawMarker=function(t){var a=t.length,f,o=this.model.markerSettings,u=this.model,g=n.compareExtend({},[],t),nt=o.fill?o.fill:u.fill,tt=o.border.color,v=o.width,it=o.opacity,y=o.border.width,e,p,w,h=u.highPointColor,c=u.lowPointColor,b=u.startPointColor,k=u.endPointColor,d=u.negativePointColor,s,l,r,rt;for(u.enableCanvasRendering==!1&&(s=this.createGroup({id:this.container.id+"sparkmarkers"}),this.setStyles({"clip-path":"url(#"+this.container.id+"_sparklineRect)"},s),this.container.appendChild(s)),(o.visible==!1||u.type==i.Pie||u.type==i.WinLoss)&&(v=0,y=0),(h||c)&&(l=g.map(function(n){return n.location.markerPos}),w=Math.min.apply(null,l),p=Math.max.apply(null,l)),r=0;r<a;r++)e=t[r].location,rt=e.width!=undefined?e.width/2:0,f={id:this.container.id+"_marker_"+r,cx:e.X,cy:e.markerPos,r:v,fill:nt,stroke:tt,opacity:it,"stroke-width":y},e.markerPos==w&&h?(f.fill=h,this.highPointIndex=r):e.markerPos==p&&c?(f.fill=c,this.lowPointIndex=r):r==0&&b?(f.fill=b,this.startPointIndex=r):r==a-1&&k?(f.fill=k,this.endPointIndex=r):e.markerPos>this.axisHeight&&d&&(f.fill=d,this.negativePointIndexes.push(r)),this.model.enableCanvasRendering?this.canvasDrawCircle(f):this.drawCircle(f,s)},f.prototype.drawLine=function(n){var t=document.getElementById(n.id);t||(t=document.createElementNS(this.svgLink,"line"));this.setAttributes(n,t);this.container.appendChild(t)},f.prototype.drawCircle=function(n,t){t=!t?this.container:t;var i=document.getElementById(n.id);i||(i=document.createElementNS(this.svgLink,"circle"));this.setAttributes(n,i);t.appendChild(i)},f.prototype.drawPolyLine=function(n){var t=document.getElementById(n.id);t||(t=document.createElementNS(this.svgLink,"polyline"));this.setAttributes(n,t);this.container.appendChild(t)},f.prototype.drawPath=function(n,t){t=!t?this.container:t;var i=document.getElementById(n.id);i||(i=document.createElementNS(this.svgLink,"path"));this.setAttributes(n,i);t.appendChild(i)},f.prototype.drawRect=function(n,t){t=!t?this.container:t;var i=document.getElementById(n.id);i||(i=document.createElementNS(this.svgLink,"rect"));this.setAttributes(n,i);t.appendChild(i)},f.prototype.createGroup=function(n){var t=document.createElementNS(this.svgLink,"g");return this.setAttributes(n,t),t},f.prototype.createText=function(n,t){var i=document.createElementNS(this.svgLink,"text");return this.setAttributes(n,i),t&&(i.textContent=t),i},f.prototype.canvasDrawLine=function(n,t){var i=t?t:this.ctx;i.save();i.beginPath();i.lineWidth=n["stroke-width"];i.strokeStyle=n.stroke;i.moveTo(n.x1,n.y1);i.lineTo(n.x2,n.y2);i.stroke();i.restore();this.dataURL=this.container.toDataURL()},f.prototype.canvasDrawRectangle=function(n,t){var i=t?t:this.ctx,u=t?t:this.ctx,r;i.save();i.beginPath();i.globalAlpha=n.opacity;i.lineWidth=n["stroke-width"];r=n["stroke-dasharray"]?n["stroke-dasharray"].split(","):!1;r&&this.ctx.setLineDash(r);i.strokeStyle=n.stroke;i.rect(n.x,n.y,n.width,n.height);n.fill=="none"&&(n.fill="transparent");i.fillStyle=n.fill;i.fillRect(n.x,n.y,n.width,n.height);i.stroke();i.restore();i=u;this.dataURL=this.container.toDataURL()},f.prototype.canvasDrawPath=function(n,t){var c=n.d,u=c.split(" "),h=n["stroke-width"],i=t?t:this.ctx,l=t?t:this.ctx,o,s,r,f,e;for(i.save(),i.beginPath(),i.globalAlpha=n.opacity?n.opacity:n["fill-opacity"],o=!0,i.lineWidth=h,s=n["stroke-dasharray"]?n["stroke-dasharray"].split(","):!1,s&&i.setLineDash(s),i.strokeStyle=n.stroke,r=0;r<u.length;r=r+3){f=parseFloat(u[r+1]);e=parseFloat(u[r+2]);switch(u[r]){case"M":n.innerR||n.cx||i.moveTo(f,e);break;case"L":n.innerR||i.lineTo(f,e);break;case"C":i.bezierCurveTo(f,e,parseFloat(u[r+3]),parseFloat(u[r+4]),parseFloat(u[r+5]),parseFloat(u[r+6]));r=r+4;break;case"A":n.innerR?o&&(i.arc(n.x,n.y,n.radius,n.start,n.end,n.counterClockWise),i.arc(n.x,n.y,n.innerR,n.end,n.start,!n.counterClockWise),o=!1):n.cx?i.arc(n.cx,n.cy,n.radius,0,2*Math.PI,n.counterClockWise):(i.moveTo(n.x,n.y),i.arc(n.x,n.y,n.radius,n.start,n.end,n.counterClockWise),i.lineTo(n.x,n.y));r=r+5;break;case"Z":i.closePath()}}n.fill!="none"&&n.fill!=undefined&&(i.fillStyle=n.fill,i.fill());h>0&&i.stroke();i.restore();i=l;this.dataURL=this.container.toDataURL()},f.prototype.canvasDrawCircle=function(n,t){var i=t?t:this.ctx,u=t?t:this.ctx,r;i.save();i.beginPath();i.arc(n.cx,n.cy,n.r,0,2*Math.PI);i.fillStyle=n.fill;i.globalAlpha=n.opacity;i.fill();i.lineWidth=n["stroke-width"];r=n["stroke-dasharray"]?n["stroke-dasharray"].split(","):!1;r&&i.setLineDash(r);i.strokeStyle=n.stroke;i.stroke();i.restore();i=u;this.dataURL=this.container.toDataURL()},f.prototype.canvasDrawPolyline=function(n,t){var i=t?t:this.ctx,u,r;for(i.save(),i.beginPath(),u=n.points.split(" "),r=0;r<u.length-1;r++){var f=u[r].split(","),e=f[0],o=f[1];r==0?i.moveTo(e,o):i.lineTo(e,o)}i.lineWidth=n["stroke-width"];i.strokeStyle=n.stroke;i.stroke();i.restore();this.dataURL=this.container.toDataURL()},f.prototype.canvasDrawText=function(n,t,i){var e=n.font,u=n["text-anchor"],o=n.opacity!==undefined?n.opacity:1,r,f;u=="middle"&&(u="center");r=i?i:this.ctx;r.save();r.fillStyle=n.fill;r.font=e;r.textAlign=u;r.globalAlpha=o;n.baseline&&(r.textBaseline=n.baseline);f=0;n.labelRotation==90&&n.id.indexOf("XLabel")!=-1&&(f=r.measureText(t).width);r.translate(n.x+f/2,n.y);r.rotate(n.labelRotation*Math.PI/180);r.fillText(t,0,0);r.restore();this.dataURL=this.container.toDataURL()},f.prototype.parseTemplate=function(n,t){var i,e,o,u,r,f;for(i=n.innerHTML,e=Object.values(t),o=e[0],r=0,f=Object.entries(o);r<f.length;r++){var s=f[r],h=s[0],c=s[1];u="#point."+h+"#";i.search(u)!=-1&&(i=i.replace(u,c))}return i},f.prototype.measureText=function(n,t){var e=document.getElementById("measureTex"),i,u,f,r;e&&e.clientHeight!=0?i=e:(i=document.createElement("text"),i.setAttribute("id","measureTex"),document.body.appendChild(i));var o=null,s=null,h=null,c=null;if(typeof n=="string"&&(n.indexOf("<")>-1||n.indexOf(">")>-1)){for(u=n.split(" "),f=0;f<u.length;f++)u[f].indexOf("<br/>")==-1&&(u[f]=u[f].replace(/[<>]/g,"&"));n=u.join(" ")}return i.innerHTML=n,t!=undefined&&t.size==undefined&&(r=t,r=r.split(" "),o=r[0],s=r[1],h=r[2],c=r[3]),t!=null&&(i.style.fontSize=t.size>0?t.size+"px":t.size?t.size:s,i.style.fontStyle&&(i.style.fontStyle=t.fontStyle?t.fontStyle:o),i.style.fontFamily=t.fontFamily?t.fontFamily:h,window.navigator.userAgent.indexOf("MSIE 8.0")==-1&&(i.style.fontWeight=t.fontWeight?t.fontWeight:c)),i.style.backgroundColor="white",i.style.position="absolute",i.style.top="-100px",i.style.left="0px",i.style.visibility="hidden",i.style.whiteSpace="nowrap",{width:i.offsetWidth,height:i.offsetHeight}},f}(ej.WidgetBase);n.Sparkline=u;ej.widget("ejSparkline","ej.Sparkline",new u)}(jQuery);n.deepExtend=function(t){var u,i,r;for(t=t||{},u=1;u<arguments.length;u++)if(i=arguments[u],i)for(r in i)i.hasOwnProperty(r)&&(t[r]=typeof i[r]=="object"?n.deepExtend(t[r],i[r]):i[r]);return t};n.compareExtend=function(t,i,r){var e,o,u,f;if(typeof r=="object"&&r!==null)for(e=Object.keys(r),o=e.length,f=0;f<o;f++)u=e[f],i.hasOwnProperty(u)&&i[u]!=null?(Array.isArray(i[u])||typeof i[u]=="object"&&i[u]!==null)&&n.compareExtend({},i[u],r[u]):i[u]=r[u];return i}})(n||(n={}))});
