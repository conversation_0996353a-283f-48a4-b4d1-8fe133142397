﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="ServerMessage.ascx.cs" Inherits="WebUI.ServerMessage" ValidateRequestMode="Disabled" ClientIDMode="Static" %>

<script type="text/javascript">
    function ShowMessage() {
        $("#<%=this.ejServerMessage.ClientID%>").show();
    }

    function HideMessage() {
        $("#ejServerMessage").hide();
    }
</script>


<ej:Dialog ID="ejServerMessage" Title="" runat="server"  IsResponsive="True" EnableResize="false" CloseOnEscape="false">
    <DialogContent>
        <div class="cnt">
            <div style="display: block;">
                <p>
                    <asp:Label ID="message" runat="server"></asp:Label>
                </p>
            </div>
            <div style="display: block;">
                <asp:Button type="button" class="btn btn-default" ID="okBtn" runat="server" Text="Ok" OnClick="okBtn_Click" CausesValidation="false" DisableValidation="True" ValidateRequestMode="Disabled" UseSubmitBehavior="false" />
                <asp:Button type="button" class="btn btn-default" ID="yesBtn" Text="<%$ Resources:GlobalResources, YesText %>" runat="server" OnClick="yesBtn_Click" CausesValidation="false" ValidateRequestMode="Disabled" DisableValidation="True" UseSubmitBehavior="false" />
                <asp:Button type="button" class="btn btn-default" ID="noBtn" Text="<%$ Resources:GlobalResources, NoText %>" runat="server" OnClick="noBtn_Click" CausesValidation="false" ValidateRequestMode="Disabled" DisableValidation="True" UseSubmitBehavior="false" />
                <asp:Button type="button" class="btn btn-default" ID="cancelBtn" Text="<%$ Resources:GlobalResources, CancelText %>" runat="server" OnClick="cancelBtn_Click" CausesValidation="false" ValidateRequestMode="Disabled" DisableValidation="True" UseSubmitBehavior="false" />
            </div>
        </div>
    </DialogContent>
</ej:Dialog>

<asp:HiddenField ID="tagField" runat="server" />
<asp:HiddenField ID="actionField" runat="server" />

