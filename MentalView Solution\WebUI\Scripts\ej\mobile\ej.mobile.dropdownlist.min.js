/*!
*  filename: ej.mobile.dropdownlist.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min","./ej.mobile.listview.min","./ej.mobile.scrollpanel.min"],n):n()})(function(){(function(n,t){t.widget("ejmDropDownList","ej.mobile.DropDownList",{_setFirst:!0,_rootCSS:"e-m-dropdownlist",validTags:["input"],defaults:{renderMode:"auto",cssClass:"",targetId:null,templateId:"",popupHeight:"164px",popupWidth:"auto",dataSource:[],fields:{text:null,value:null,groupBy:null,image:null,checkBy:null},enableMultiSelect:!1,enablePersistence:!1,readOnly:!1,enabled:!0,query:null,watermarkText:null,locale:"en-US",selectedItemIndex:-1,delimiterChar:",",itemsCount:0,allowVirtualScrolling:!1,focusIn:null,focusOut:null,select:null,change:null,actionSuccess:null,actionFailure:null,actionComplete:null},dataTypes:{renderMode:"enum",dataSource:"data",itemsCount:"number",enableMultiSelect:"boolean",enablePersistence:"boolean",readOnly:"boolean",enabled:"boolean",allowVirtualScrolling:"boolean",locale:"string"},observables:["selectedItemIndex"],selectedItemIndex:t.util.valueFunction("selectedItemIndex"),_init:function(){t.setRenderMode(this);this._getLocalizedLabels();this._initialization();this._renderControl();this.model.readOnly||this._wireEvents();this.model.watermarkText=t.isNullOrUndefined(this.model.watermarkText)?this._localizedLabels.watermarkText:this.model.watermarkText},_renderControl:function(){this._renderList()},_wireEvents:function(i){i||(this._touchStart=n.proxy(this._touchStartHandler,this),this._docTouchStart=n.proxy(this._docTouchStartHandler,this),this._docTouchEnd=n.proxy(this._docTouchEndHandler,this));var r=n(document);t.listenEvents([this._eleWarpper,this._dropDownIcon,r,r],["focus",t.startEvent(),t.startEvent(),t.endEvent()],[this._touchStart,this._touchStart,this._docTouchStart,this._docTouchEnd],i)},_getLocalizedLabels:function(){this._localizedLabels=t.getLocalizedConstants(this.sfType,this.model.locale)},_initialization:function(){this._index=0;var i=this.element.attr("id"),r=this.element.is("select"),u=r&&this._renderSelectOption(i);this._orgEle=this.element.clone();this._text=[];this._hiddenVal=[];this._targetEle=this.model.targetId?n("#"+this.model.targetId):r?u:t.buildTag("ul");this._eleWarpper=t.buildTag("div#"+i+"_wrapper.e-m-dropdown-wrapper "+this.model.cssClass+"  e-m-"+this.model.renderMode+" e-m-user-select");this._hiddenElement=t.buildTag("input#"+this._id+"_hidden",{},{display:"none"},{name:this._id});this.element.addClass("e-m-user-select e-m-"+this.model.renderMode).attr({name:i,readonly:!0}).wrap(this._eleWarpper);this._eleWarpper=this.element.parent();this._dropDownIcon=t.buildTag("div.e-m-icon-down").appendTo(this._eleWarpper.append(this._hiddenElement));this._setWatermarkText(this.model.watermarkText=t.isNullOrUndefined(this.model.watermarkText)?this._localizedLabels.watermarkText:this.model.watermarkText);this._controlState(this.model.enabled)},_renderList:function(){t.isNullOrUndefined(this._targetWrapper)&&(this._targetWrapper=t.buildTag("div.e-m-target-wrapper"));this._targetWrapper.append(this._targetEle.ejmListView({renderMode:this.model.renderMode,dataSource:this.model.dataSource,query:this.model.query,enableChecklist:this.model.enableMultiSelect,itemsCount:this.model.itemsCount,fields:{text:this.model.fields.text,value:this.model.fields.value,groupBy:this.model.fields.groupBy,image:this.model.fields.image,checkBy:this.model.fields.checkBy},templateId:this.model.templateId,touchEnd:n.proxy(this._listItemClickHandler,this),actionSuccess:n.proxy(this._onComplete,this),actionFailure:this.model.actionFailure,actionComplete:this.model.actionComplete})).appendTo(this._eleWarpper);this.model.dataSource instanceof t.DataManager||this._renderScroll();this.hidePopup();this._setSelectedItemIndex(this.selectedItemIndex(),!0)},_renderScroll:function(){var u=this.model.allowVirtualScrolling?n.proxy(this._virtualScroll,this):null,i=this._targetWrapper.height(),f=window.innerHeight,t=parseInt(this.model.popupHeight),r=i>t?t:i,e=this.element.offset().top+this.element.height();t<i&&this._targetWrapper.ejmScrollPanel({renderMode:this.model.renderMode,targetHeight:t,scrollEnd:u});e+r>f&&this._targetWrapper.css("top",-r);this.model.popupWidth!="auto"&&this._targetWrapper.css("width",this.model.popupWidth)},_onComplete:function(n){this._renderScroll(n);this.model.actionSuccess&&this._trigger("actionSuccess")},_touchStartHandler:function(){this.model.readOnly||(this._targetWrapper.css("display")=="none"?(this.element.addClass("e-m-focus"),this.model.focusIn&&this._trigger("focusIn"),this.showPopup()):(this.element.removeClass("e-m-focus"),this.model.focusOut&&this._trigger("focusOut"),this.hidePopup()),this.element.blur())},_virtualScroll:function(n){if(n.position=="bottom"){var t=this._targetEle.data("ejmListView"),i=this._targetWrapper.data("ejmScrollPanel");t.append(this.model.dataSource);i.refresh()}},_docTouchStartHandler:function(n){this._isTargetDropDown(n.target)||(this._hide=!0)},_docTouchEndHandler:function(n){!this._isTargetDropDown(n.target)&&this._hide&&(this.element.removeClass("e-m-focus"),this.model.focusOut&&this._trigger("focusOut"),this.hidePopup(),this._hide=!1)},_isTargetDropDown:function(t){return n(t).closest(this._eleWarpper).hasClass("e-m-dropdown-wrapper")},_listItemClickHandler:function(n){var t=this.model.templateId!=""?n.data[this.model.fields.text]:n.text;this._updateSelectedItem(n.item,t,n.item.attr("data-value"),n.isChecked);this.element.blur();n.event.preventDefault()},_renderSelectOption:function(i){var u=t.buildTag("input#"+i+".e-m-dropdownlist",null,null,{"data-role":"ejmdropdownlist"}),r=t.buildTag("ul");return n(this.element.children()).each(function(n,i){var u=t.buildTag("li",null,null,{"data-ej-text":i.text});r.append(u)}),this.element.hide(),this.element.before(u.after(r)),this.element=u,r},_setWatermarkText:function(n){n?this.element.attr("placeholder",n):this.element.removeAttr("placeholder")},_controlState:function(n){this._eleWarpper[n?"removeClass":"addClass"]("e-m-state-disabled")},_setSelectedItemIndex:function(t,r){var u=this._targetEle.data("ejmListView"),f,e;if(this.selectedItemIndex()>=0)f=u.getTextByIndex(this.selectedItemIndex()),e=u.getItemByIndex(this.selectedItemIndex()),r?this.model.enableMultiSelect?u.checkItemsByIndex(this.selectedItemIndex()):u.selectItemByIndex(this.selectedItemIndex()):this.model.enableMultiSelect?u.uncheckItemsByIndex(this.selectedItemIndex()):u.deselectItem(),this._updateSelectedItem(e,f,e.attr("data-value"),r?!0:!1);else if(this.model.fields.checkBy)for(t=this._targetWrapper.find(".e-m-lv-checked").length,i=0;i<t;i++)f=n(this._targetWrapper.find(".e-m-lv-checked")[i]).find(".e-m-lv-content").text(),this._updateSelectedItem([],f,null,r?!0:!1)},_updateSelectedItem:function(t,i,r,u){var f=r?r:i;this.model.enableMultiSelect?(u?n.inArray(i,this._text)==-1&&(this._text.push(i),this._hiddenVal.push(f)):(this._text=jQuery.grep(this._text,function(n){return n!=i}),this._hiddenVal=jQuery.grep(this._hiddenVal,function(n){return n!=f})),this.model.select&&this._trigger("select",this._getArgs(t,i,r,u)),this.element.val(this._setDelimiterChar(this._text)),this._hiddenElement.attr("value",this._hiddenVal.toString()),this.model.change&&this._trigger("change",this._getArgs(t,i,r,u))):(this._hiddenVal=[],this._hiddenVal.push(f),this.model.select&&this._trigger("select",this._getArgs(t,i,r,u)),this.element.val()!=i&&(this.selectedItemIndex(this._targetEle.data("ejmListView").getIndexByText(i)),this.element.val(i),this._hiddenElement.attr("value",f),this.model.change&&this._trigger("change",this._getArgs(t,i,r,u))),this.hidePopup())},_getArgs:function(n,t,i,r){return data={selectedItem:n,selectedText:t,selectedValue:i,isChecked:r}},_setDelimiterChar:function(n){var t=n.toString();return t.replace(/,/g,this.model.delimiterChar+" ")},showPopup:function(){this._targetWrapper.show()},hidePopup:function(){this._targetWrapper.hide()},getValue:function(){return this.element.val()},selectItemByIndex:function(n){this.model.selectedItemIndex=n;this._setSelectedItemIndex(this.selectedItemIndex(),!0)},selectItemByIndices:function(t){if(this.model.enableMultiSelect){var i=this;n(t).each(function(n,t){i.model.selectedItemIndex=t;i._setSelectedItemIndex(i.selectedItemIndex(),!0)})}},unselectItemByIndex:function(n){this.model.selectedItemIndex=n;this._setSelectedItemIndex(this.selectedItemIndex(),!1)},unselectItemByIndices:function(t){if(this.model.enableMultiSelect){var i=this;n(t).each(function(n,t){i.model.selectedItemIndex=t;i._setSelectedItemIndex(i.selectedItemIndex(),!1)})}},getSelectedItemValue:function(){return this.model.enableMultiSelect||t.isNullOrUndefined(this._hiddenVal[0])?this._hiddenVal:this._hiddenVal[0]},_setModel:function(n){var t;for(var i in n)switch(i){case"renderMode":this._targetWrapper.removeClass("e-m-ios7 e-m-android e-m-windows e-m-flat").addClass("e-m-"+this.model.renderMode);this.element.removeClass("e-m-ios7 e-m-android e-m-windows e-m-flat").addClass("e-m-"+this.model.renderMode);this._targetEle.ejmListView({renderMode:this.model.renderMode});this._targetWrapper.ejmScrollPanel({renderMode:this.model.renderMode});break;case"delimiterChar":this.model.enableMultiSelect&&this._setDelimiterChar(this._text);break;case"enabled":this._controlState(this.model.enabled);break;case"watermarkText":this._setWatermarkText(this.model.watermarkText);break;case"selectedItemIndex":this._setSelectedItemIndex(this.selectedItemIndex(),!0);break;case"locale":this._setLocale();break;default:t=!0}t&&this._refresh();t=!1},_setLocale:function(){this._getLocalizedLabels();this.model.watermarkText=this._localizedLabels.watermarkText;this._setWatermarkText(this.model.watermarkText)},_refresh:function(){this._destroy();this.element.addClass(this._rootCSS);this._initialization();this._renderControl();this._wireEvents()},_clearElement:function(){this.element.removeAttr("class").removeAttr("style");this.element.empty().html(this._orgEle.html());this._targetEle.remove();this.element.unwrap();this._eleWarpper.remove();this._hiddenElement.remove();this._dropDownIcon.remove();this._targetWrapper.remove();this._targetWrapper=null},_destroy:function(){this._wireEvents(!0);this._clearElement()}});t.mobile.DropDownList.Locale=t.mobile.DropDownList.Locale||{};t.mobile.DropDownList.Locale["default"]=t.mobile.DropDownList.Locale["en-US"]={watermarkText:null}})(jQuery,Syncfusion)});
