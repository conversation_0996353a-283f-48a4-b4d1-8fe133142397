﻿using Data;
using Newtonsoft.Json;
using Syncfusion.JavaScript.Web;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using static Data.MentalViewDataSet;

namespace WebUI
{
    public partial class Links : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                ((Main)this.Master).ServerMessage.ButtonClicked += new ButtonClickedHandler(this.ServerMessageButtonClicked);
                ((Main)this.Master).PageTitle = GetLocalResourceObject("Page.Title").ToString();
                //Καλούμε την javascript Initialization() λόγω του UpdatePanel.
                ScriptManager.RegisterStartupScript(this, this.GetType(), "temp", "<script language='javascript'>Initialization();</script>", false);

                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                Int64 loggedEmailTemplateId = Convert.ToInt64(userData["UserId"]);
                string roleName = userData["Role"].ToString();

                if (roleName == "Guest")
                {
                    Response.StatusCode = 404;
                    Response.End();
                }

                if (this.IsPostBack == false)
                {
                    this.FillLinksGrid();
                }
                else
                {
                    //if (Request.Form["__EVENTTARGET"] == this.searchBtn.ID)
                    //{
                    //    this.FillLinksGrid();
                    //}
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void FillLinksGrid()
        {
            try
            {
                Data.SortDirection sortDirection = Data.SortDirection.Ascending;
                string sortExpression = "";
                int totalResults = 0;
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                string roleName = userData["Role"].ToString();
                Int64 userId = Convert.ToInt64(userData["UserId"]);

                MentalViewDataSet ds = null;

                ds = Data.Business.LinksBusiness.GetAllLinksOfTenant(tenantId);
                ViewState["Links"] = ds;

                this.linksGrid.DataSource = ds.Links;
                this.linksGrid.DataBind();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        //protected void contactsGrid_ServerCommandButtonClick(object sender, Syncfusion.JavaScript.Web.GridEventArgs e)
        //{
        //    try
        //    {
        //        if (e.EventType == "commandButtonClick")
        //        {
        //            if (e.Arguments["commandType"].ToString() == "Edit")
        //            {
        //                string contactId = ((Dictionary<string, object>)e.Arguments["data"])["ContactId"].ToString();
        //                Response.Redirect("~/Contact.aspx?ContactId=" + contactId);
        //            }
        //            else if (e.Arguments["commandType"].ToString() == "Delete")
        //            {
        //                string contactId = ((Dictionary<string, object>)e.Arguments["data"])["ContactId"].ToString();
        //                ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete", contactId);
        //            }
        //        }

        //        this.FillLinksGrid();
        //    }
        //    catch (Exception exp)
        //    {
        //        if (exp.GetType() != typeof(ThreadAbortException))
        //        {
        //            Data.ExceptionLogger.LogException(exp);
        //            ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
        //        }
        //    }
        //}

        //protected void searchBtn_ServerClick(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        this.contactsPager.CurrentPage = 0;
        //        this.FillLinksGrid();
        //    }
        //    catch (Exception exp)
        //    {
        //        Data.ExceptionLogger.LogException(exp);
        //        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
        //    }
        //}


        //protected void clearSearchBtn_ServerClick(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        this.contactsPager.CurrentPage = 0;
        //        this.filterTxtBox.Value = "";
        //        this.FillLinksGrid();
        //    }
        //    catch (Exception exp)
        //    {
        //        Data.ExceptionLogger.LogException(exp);
        //        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
        //    }
        //}

        private void ServerMessageButtonClicked(object sender, ButtonClickedArgs args)
        {
            try
            {
                if (args.Action == "Delete")
                {
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        Int64 linkId = Int64.Parse(args.Tag);
                        Data.Business.LinksBusiness.DeleteLink(linkId);
                    }
                }
                else if (args.Action == "SessionExpired")
                {
                    Response.Redirect("Default.aspx");
                }

                this.FillLinksGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void linksGrid_ServerAddRow(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Links"];

                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                MentalViewDataSet.LinksRow linksRow = ds.Links.NewLinksRow();
                linksRow.TenantId = tenantId;
                linksRow.Description = ((Dictionary<string, object>)e.Arguments["data"])["Description"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Description"].ToString();
                if (((Dictionary<string, object>)e.Arguments["data"])["CreateDate"] != null)
                {
                    linksRow.CreateDate = DateTime.Parse(((Dictionary<string, object>)e.Arguments["data"])["CreateDate"].ToString());
                }
                linksRow.Link = ((Dictionary<string, object>)e.Arguments["data"])["Link"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Link"].ToString();
                linksRow.CreateDate = DateTime.Now;
                ds.Links.AddLinksRow(linksRow);

                Data.Business.LinksBusiness.SaveLinks(ref ds);

                ViewState["Links"] = ds;
                this.linksGrid.DataSource = ds.Links;
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void linksGrid_ServerEditRow(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Links"];
                ViewState["Links"] = ds;

                Int64 linkId = Convert.ToInt64(((Dictionary<string, object>)e.Arguments["previousData"])["LinkId"]);
                MentalViewDataSet.LinksRow linksRow = ds.Links.FindByLinkId(linkId);

                linksRow.Description = ((Dictionary<string, object>)e.Arguments["data"])["Description"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Description"].ToString();
                if (((Dictionary<string, object>)e.Arguments["data"])["CreateDate"] != null)
                {
                    linksRow.CreateDate = DateTime.Parse(((Dictionary<string, object>)e.Arguments["data"])["CreateDate"].ToString());
                }
                linksRow.Link = ((Dictionary<string, object>)e.Arguments["data"])["Link"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Link"].ToString();

                Data.Business.LinksBusiness.SaveLinks(ref ds);
                this.linksGrid.DataSource = ds.Links;
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void linksGrid_ServerDeleteRow(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Links"];
                this.linksGrid.DataSource = ds.Links;

                string linkId = ((Dictionary<string, object>)e.Arguments["data"])["LinkId"].ToString();
                ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete", linkId);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }
    }
}