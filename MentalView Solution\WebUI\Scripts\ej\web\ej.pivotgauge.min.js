/*!
*  filename: ej.pivotgauge.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.globalize.min","./../common/ej.core.min","./../common/ej.data.min","./ej.waitingpopup.min","./ej.pivotgauge.min","./../datavisualization/ej.circulargauge.min","./ej.pivotanalysis.base.min","./ej.olap.base.min","./ej.pivot.common.min"],n):n()})(function(){(function(n,t,r){t.widget("ejPivotGauge","ej.PivotGauge",{_rootCSS:"e-pivotgauge",element:null,model:null,validTags:["div","span"],defaults:n.extend({},t.datavisualization.CircularGauge.prototype.defaults,{url:"",analysisMode:"pivot",operationalMode:"clientmode",cssClass:"",rowsCount:0,columnsCount:0,enableTooltip:!1,enableAnimation:!1,isResponsive:!1,enableRTL:!1,enableXHRCredentials:!1,labelFormatSettings:{numberFormat:"default",decimalPlaces:5,prefixText:"",suffixText:""},showHeaderLabel:!0,scales:t.datavisualization.CircularGauge.prototype._defaultScaleValues(),customObject:{},dataSource:{data:null,sourceInfo:"",providerName:"ssas",isFormattedValues:!1,columns:[],cube:"",catalog:"",rows:[],values:[],filters:[]},locale:"en-US",serviceMethodSettings:{initialize:"InitializeGauge"},renderSuccess:null,renderComplete:null,renderFailure:null,beforeServiceInvoke:null,afterServiceInvoke:null,load:null,beforePivotEnginePopulate:null}),dataTypes:{dataSource:{data:"data",columns:"array",rows:"array",values:"array",filters:"array"},serviceMethodSettings:{initialize:"enum"},customObject:"data",scales:"data"},observables:["rowsCount","columnsCount","showHeaderLabel","locale","radius","frameType"],rowsCount:t.util.valueFunction("rowsCount"),columnsCount:t.util.valueFunction("columnsCount"),showHeaderLabel:t.util.valueFunction("showHeaderLabel"),locale:t.util.valueFunction("locale"),radius:t.util.valueFunction("radius"),frameType:t.util.valueFunction("frameType"),getOlapReport:function(){return this._olapReport},setOlapReport:function(n){this._olapReport=n},getJSONRecords:function(){return this._JSONRecords},setJSONRecords:function(n){this._JSONRecords=JSON.parse(n)},_init:function(){this.model=n.extend(t.datavisualization.CircularGauge.prototype.defaults,this.model);this._scalesInitialize();this._initPrivateProperties();this._load()},_scalesInitialize:function(){this.model.scales!=null?n.each(this.model.scales,t.proxy(function(i,r){var u=t.datavisualization.CircularGauge.prototype._defaultScaleValues();n.extend(u,r);n.extend(r,u)},this)):this.model.scales=[t.datavisualization.CircularGauge.prototype._defaultScaleValues()];this.model.enableRTL&&n.each(this.model.scales,function(n,t){t.direction="counterClockwise"})},_destroy:function(){this._unWireEvents();this.element.empty().removeClass("e-pivotgauge"+this.model.cssClass).removeAttr("tabindex");this._ogaugeWaitingPopup!=r&&this._ogaugeWaitingPopup.destroy();this.element.attr("class")==""&&this.element.removeAttr("class")},_initPrivateProperties:function(){this._id=this.element.attr("id");this._olapReport="";this._JSONRecords=null;this._maximum=[];this._maxValue=0;this._pointerValue_0=0;this._pointerValue_1=0;this._currentLayout="";this._defaultArea=0;this._defaultWidth=0;this._gaugeObj={};this._ogaugeWaitingPopup=null;this._ogaugeProgressBar=null;this._ogaugeProgressText=null;this._notationStr=null;this._ogaugeTimer=null},_load:function(){var i,u;this.element.addClass(this.model.cssClass);n("#"+this._id).parent()[0]&&(n("#"+this._id).parent()[0].style.position="relative",n("#"+this._id).ejWaitingPopup({showOnInit:!0,appendTo:n("#"+this._id).parent()[0]}));this._ogaugeWaitingPopup=n("#"+this._id).data("ejWaitingPopup");this.model.load!=null&&this._trigger("load",{action:"initialize",model:this.model,element:this.element,customObject:this.model.customObject});i=JSON.stringify(this.model.customObject);this.model.dataSource&&typeof this.model.dataSource.data=="string"?(this._trigger("beforePivotEnginePopulate",{gaugeObject:this}),this.model.operationalMode=t.PivotGauge.OperationalMode.ClientMode,t.olap.base.getJSONData({action:"initialLoad"},this.model.dataSource,this)):this.model.dataSource&&typeof this.model.dataSource.data=="object"&&this.model.dataSource.data!=null?(this._trigger("beforePivotEnginePopulate",{gaugeObject:this}),u=t.PivotAnalysis.pivotEnginePopulate(this.model),this._dataModel="Pivot",this.generateJSON({baseObj:this},u.pivotEngine)):this.model.customObject!=null&&this.model.customObject!=r&&this.model.customObject!={}&&(this.model.operationalMode=t.PivotGauge.OperationalMode.ServerMode,this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{element:this.element,customObject:this.model.customObject}),this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.initialize,JSON.stringify({action:"initializeGauge",customObject:i}),this._renderControlSuccess))},_setFirst:!1,_setModel:function(i){for(var r in i)switch(r){case"OlapReport":this.setOlapReport(i[r]);break;case"JsonData":this.setOlapReport(i[r]);break;case"RefreshPivotGauge":this.element.renderControlFromJSON(i[r]);break;case"customObject":this.model.customObject=i[r];break;case"locale":this.locale(t.util.getVal(i[r]));this._load();break;case"rowsCount":this.rowsCount(t.util.getVal(i[r]));break;case"columnsCount":this.columnsCount(t.util.getVal(i[r]));break;case"showHeaderLabel":this.showHeaderLabel(t.util.getVal(i[r]));break;case"radius":this.radius(t.util.getVal(i[r]));break;case"frameType":this.frameType(t.util.getVal(i[r]));break;case"enableRTL":this.model.enableRTL=i[r];this._load();break;case"enableAnimation":this.model.enableAnimation=i[r];this._load();break;case"operationalMode":this.model.operationalMode=i[r];break;case"isResponsive":this.model.isResponsive=i[r];this._load();break;case"labelFormatSettings":this.model.labelFormatSettings=n.extend({},this.model.labelFormatSettings,i[r]);this._load();break;case"showHeaderLabel":this.showHeaderLabel(t.util.getVal(i[r]));this._load();break;case"dataSource":this.model.dataSource=n.extend({},this.model.dataSource,i[r]);this._load()}this._gaugeObj.obj0&&this._renderControlSuccess({PivotRecords:JSON.stringify(this.getJSONRecords()),OlapReport:this.getOlapReport()})},_wireEvents:function(){this.element.find(".e-circulargauge").mouseenter(t.proxy(function(n){this.model.enableTooltip&&this._showTooltip(n)},this)).mouseout(t.proxy(function(){n(this._tooltip).fadeOut("slow")},this)).mousemove(t.proxy(function(n){this.model.enableTooltip&&(this._showTooltip(n),this._hideTooltip())},this))},_getLocalizedLabels:function(n){return t.isNullOrUndefined(t.PivotGauge.Locale[this.locale()])||t.PivotGauge.Locale[this.locale()][n]==r?t.PivotGauge.Locale["en-US"][n]:t.PivotGauge.Locale[this.locale()][n]},_unWireEvents:function(){},_showTooltip:function(i){var e,o,f,l;if(t.browserInfo().name!="msie"||!(t.browserInfo().version<=8)){n(this._tooltip).remove();var c=i.target.id.split("_")==""?i.target.parentNode.id.split("_"):i.target.id.split("_"),u=c[c.length-1],s=this._numberFormatConversion(this._JSONRecords[u].GoalValue||0,this.model.labelFormatSettings.numberFormat,this.model.labelFormatSettings.decimalPlaces),h=this._numberFormatConversion(this._JSONRecords[u].MeasureValue||this._JSONRecords[u].Value,this.model.labelFormatSettings.numberFormat,this.model.labelFormatSettings.decimalPlaces),a=this._setMaxScaleValue(this._JSONRecords[u].GoalValue||0,this._JSONRecords[u].MeasureValue||this._JSONRecords[u].Value,u);if(this.model.labelFormatSettings.numberFormat==t.PivotGauge.NumberFormat.Notation){for(e=this._JSONRecords[u].GoalValue||0,o=this._JSONRecords[u].MeasureValue||this._JSONRecords[u].Value;e>=10;)e=parseInt(e)/10;while(o>=10)o=parseInt(o)/10;s=e.toFixed(1)+s;h=o.toFixed(1)+h}f=this._JSONRecords[u].MemberName||this._JSONRecords[u].Measure;(f==r||f=="undefined")&&(f=this._JSONRecords[u].MeasureCaption);l=this.model.enableRTL||this._gaugeObj["obj"+u].model.scales[0].direction=="counterClockwise"?f.split(" - ").reverse().join(" - ")+"<br/>"+s+" :"+this._getLocalizedLabels("RevenueGoal")+"<br/>"+h+" :"+this._getLocalizedLabels("RevenueValue"):f+"<br/>"+this._getLocalizedLabels("RevenueGoal")+": "+s+"<br/>"+this._getLocalizedLabels("RevenueValue")+": "+h;this._tooltip=t.buildTag("div.e-pivotgauge-tooltip e-js",l);n(this._tooltip).appendTo("body");n(this._tooltip).addClass(".e-pivotgauge-active");this._changeTooltipPos(i);this.model.enableRTL||this._gaugeObj["obj"+u].model.scales[0].direction=="counterClockwise"?n(this._tooltip).css("text-align","right"):n(this._tooltip).css("text-align","left")}},_changeTooltipPos:function(t){var i=t.pageX-8,r=t.pageY+8;n(this._tooltip).css({top:r,left:i})},_hideTooltip:function(){window.setTimeout(t.proxy(function(){n(this._tooltip).fadeOut(1500,"linear")},this),3e3)},_renderControlSuccess:function(n){var u,f,c,l,e;try{this.model.analysisMode=n.DataModel=="Olap"?t.PivotGauge.AnalysisMode.Olap:t.PivotGauge.AnalysisMode.Pivot;var i="",o="",s="",a="",h=0;if(n[0]!=r&&n.length>0?(this.setJSONRecords(n[0].Value),this.setOlapReport(n[1].Value),n[2]!=null&&n[2]!=r&&(this.model.customObject=n[2].Value)):n.d!=r&&n.d.length>0?(this.setJSONRecords(n.d[0].Value),this.setOlapReport(n.d[1].Value),n.d[2]!=null&&n.d[2]!=r&&(this.model.customObject=n.d[2].Value)):n.PivotRecords&&n.OlapReport&&(this.setJSONRecords(n.PivotRecords),this.setOlapReport(n.OlapReport),n.customObject!=null&&n.customObject!=r&&(this.model.customObject=n.customObject)),this.model.afterServiceInvoke!=null&&this.model.operationalMode==t.PivotGauge.OperationalMode.ServerMode&&this._trigger("afterServiceInvoke",{element:this.element,customObject:this.model.customObject}),u=this.getJSONRecords(),u!=null&&u.length>0){if(this.rowsCount()<1&&this.columnsCount()<1){for(this._currentLayout="WrapPanel",f=0;f<u.length;f++)o=t.buildTag("img#"+this._id+"_statusIndicator_"+f+".kpiiconvalue")[0].outerHTML,s=t.buildTag("img#"+this._id+"_trendIndicator_"+f+".kpiiconvalue")[0].outerHTML,a+=t.buildTag("li",t.buildTag("div#"+this._id+"_"+f,o+s)[0].outerHTML)[0].outerHTML;i=t.buildTag("ul.e-wrapLayout",a)[0].outerHTML}else{for(this._currentLayout="Table",(this.columnsCount()==r||this.columnsCount()<1)&&this.columnsCount((u.length/this.rowsCount()).toFixed()),(this.rowsCount()==r||this.rowsCount()<1)&&this.rowsCount((u.length/this.columnsCount()).toFixed()),i="<table><tbody>",c=0;c<this.rowsCount();c++){for(i+="<tr>",l=0;l<this.columnsCount();l++)o=t.buildTag("img#"+this._id+"_statusIndicator_"+h+".kpiiconvalue")[0].outerHTML,s=t.buildTag("img#"+this._id+"_trendIndicator_"+h+".kpiiconvalue")[0].outerHTML,i+="<td>"+t.buildTag("div#"+this._id+"_"+h,o+s)[0].outerHTML+"<\/td>",h++;i+="<\/tr>"}i+="<\/tbody><\/table>"}this.element.html(i);e=this;setTimeout(function(){e.renderControlFromJSON(u);var n={element:e.element,customObject:e.model.customObject};e._trigger("renderSuccess",n)},0);this._defaultWidth=this.element.find("canvas").width();this._defaultArea=this.element.find("table").height()*this.element.find("table").width()}}catch(v){}t.isNullOrUndefined(n.Exception)||t.Pivot._createErrorDialog(n,"Exception",this)},doAjaxPost:function(i,u,f,e,o,s){var h,c,l,y=!0,v=this.model.enableXHRCredentials,a;f.XMLA==r?(h="application/json; charset=utf-8",c="json",l=n.proxy(e,this)):(h="text/xml",c="xml",f=f.XMLA,l=n.proxy(e,t.olap.base,s),y=!t.isNullOrUndefined(s)&&s.action!="loadFieldElements"?!0:!1);a={type:i,url:u,contentType:h,async:!1,dataType:c,data:f,success:l,xhrFields:{withCredentials:v},complete:t.proxy(function(){var n={element:this.element,customObject:this.model.customObject};this._trigger("renderComplete",n)},this),error:t.proxy(function(n){this._ogaugeWaitingPopup.hide();var t={element:this.element,Message:n,customObject:this.model.customObject};this._trigger("renderFailure",t)},this)};v||delete a.xhrFields;n.ajax(a)},_getCalculatedValue:function(n){var t=n.toString(),r=1,f,i,u;for(f=t.indexOf(".")>-1?t.split(".")[0].length:t.length,i=parseInt(t),u=0;u<f;u++)i>this._maxValue&&(i=i/10,r=r*10);return r},_findNearestUpperLimit:function(n,t){var r=n.toString(),i=parseInt(r),u=i%t;return i+(t-u)},_setMaxScaleValue:function(n,t,i){var r;return t>n?(r=this._getCalculatedValue(t),this._maximum[i]=this._findNearestUpperLimit(t/r,10)):(r=this._getCalculatedValue(n),this._maximum[i]=this._findNearestUpperLimit(n/r*4/3,10)),r},_numberFormatConversion:function(n,i,r){function h(n,t){for(var i=n,r=t;;){if(!(i%=r))return r;if(!(r%=i))return i}}var o,n,r,f,s,u,e;switch(i){case t.PivotGauge.NumberFormat.Default:return parseFloat(n).toFixed(r);case t.PivotGauge.NumberFormat.Text:return n.toString();case t.PivotGauge.NumberFormat.Currency:return t.format(parseInt(n),"c",this.model.locale);case t.PivotGauge.NumberFormat.Percentage:return n*=100,parseFloat(n).toFixed(2)+"%";case t.PivotGauge.NumberFormat.Fraction:for(n||(n=this),o=String(n).split(".")[0],n=parseFloat("."+String(n).split(".")[1]),n=n.toFixed(4),r="1",f=0;f<String(n).length-2;f++)r+="0";for(n=n*r,r=parseInt(r),f=2;f<n+1;f++)n%f==0&&r%f==0&&(n=n/f,r=r/f,f=2);return n.toString().length==2&&r.toString().length==3?(n=Math.round(Math.round(n)/10),r=Math.round(Math.round(r)/10)):n.toString().length==2&&r.toString().length==2&&(n=Math.round(n/10),r=Math.round(r/10)),s=h(n,r),(o==0?"":o+" ")+n/s+"/"+r/s;case t.PivotGauge.NumberFormat.Scientific:return parseFloat(n).toExponential();case t.PivotGauge.NumberFormat.Notation:for(u=0,e=parseInt(n);e>=1;)e=parseInt(e)/10,u++;return this._notationStr=u==1?"da":u==2?"h":u==3?"k":u==4?"k*da":u==5?"k*h":u==6?"M":u==7?"M*da":u==8?"M*h":u==9?"G":u==10?"G*da":u==11?"G*h":u==12?"T":""}},removeImg:function(){this.element.find("img").remove()},renderControlFromJSON:function(i){var a,k,u,c,st,l,ht,ct,lt,f;for(this._maxValue=99,a=0,k=this.radius()/22,this._currentLayout=="Table"&&this.rowsCount()*this.columnsCount()<=i.length?a=this.rowsCount()*this.columnsCount():(this._currentLayout=="WrapPanel"||this.rowsCount()*this.columnsCount()>i.length)&&(a=i.length),u=0;u<a;u++){var v=0,y=0,tt=0,h=0,it=0,p=0,w=0,d=0,e="",o,rt=[],ut=[],g=[],ft=[],et=[],ot=[],nt=[],s="",b="";if(i[u].MeasureValue!=null&&i[u].MeasureValue!=r?(y=parseFloat(i[u].MeasureValue)||0,v=parseFloat(i[u].GoalValue)||0,e=this.model.labelFormatSettings.prefixText+" "+i[u].MeasureCaption+(n.trim(i[u].MemberName)?" - ":"")+i[u].MemberName+" "+this.model.labelFormatSettings.suffixText):(y=parseFloat(i[u].Value),v=0,e=i[u].Measure,this.model.scales[0].showIndicators=!1),e=this.model.enableRTL||this.model.scales[0].direction=="counterClockwise"?e.split(" - ").reverse().join(" - "):e,p=this._setMaxScaleValue(v,y,u),it=y,w=v/p,d=it/p,this._pointerValue_0=h+d,this._pointerValue_1=w+h,tt=h,h=h+d,i[u].StatusGraphic=="Traffic Signals"?i[u].StatusValue==-1?s="kpiredroad":i[u].StatusValue==0?s="kpiallcolor":i[u].StatusValue==1&&(s="kpigreenroad"):i[u].StatusValue==-1?s="kpidiamond":i[u].StatusValue==0?s="kpitriangle":i[u].StatusValue==1&&(s="kpicircle"),o=this.element.find("#"+this._id+"_statusIndicator_"+[u]).addClass(s),o!=null&&o!=r&&(c=n(o).css("background-image"),st={height:15,width:15,type:"image",imageUrl:!t.isNullOrUndefined(c)&&c!="none"&&c!=""?c.split("(")[1].split(")")[0].replace('"',"").replace('"',""):"",position:{x:this.radius()-20,y:260},location:{x:this.radius()-20,y:260}}),i[u].TrendGraphic!=null&&i[u].TrendGraphic!=r&&i[u].TrendGraphic!=""&&i[u].TrendGraphic.toLowerCase()=="standard arrow"&&(i[u].TrendValue==-1?b="kpidownarrow":i[u].TrendValue==0?b="kpirightarrow":i[u].TrendValue==1&&(b="kpiuparrow"),o=this.element.find("#"+this._id+"_trendIndicator_"+[u]).toggleClass(b)),t.isNullOrUndefined(o)||(l=n(o).css("background-image"),ht={height:15,width:15,type:"image",imageUrl:!t.isNullOrUndefined(l)&&l!="none"&&l!=""?l.split("(")[1].split(")")[0].replace('"',"").replace('"',""):"",position:{x:this.radius(),y:260},location:{x:this.radius(),y:260}}),!t.isNullOrUndefined(this.model.scales[0])){for(this.frameType()=="fullcircle"?(this.model.scales[0].startAngle=122,this.model.scales[0].sweepAngle=296):this.frameType()=="halfcircle"&&(this.model.scales[0].startAngle=180,this.model.scales[0].sweepAngle=180),this.model.scales[0].indicators=this.model.scales[0].showIndicators?[st,ht]:this.model.scales[0].indicators,t.isNullOrUndefined(this.model.scales[0].pointers[0])||(ct=t.datavisualization.CircularGauge.prototype._defaultScaleValues().pointerCap,this.model.scales[0].pointers[0].value=this._pointerValue_0,this.model.scales[0].pointers[0].pointerCap=ct,this.model.scales[0].pointers[0].pointerCap.radius=k),t.isNullOrUndefined(this.model.scales[0].pointers[1])||(this.model.scales[0].pointers[1].value=this._pointerValue_1,this.model.scales[0].pointers[1].type=this.model.scales[0].pointers[1].type||"marker",this.model.scales[0].pointers[1].markerType=this.model.scales[0].pointers[1].markerType||"diamond",this.model.scales[0].pointers[1].placement=this.model.scales[0].pointers[1].placement||"center"),t.isNullOrUndefined(this.model.scales[0].ranges[0])||(this.model.scales[0].ranges[0].startValue=tt,this.model.scales[0].ranges[0].endValue=w),t.isNullOrUndefined(this.model.scales[0].ranges[1])||(this.model.scales[0].ranges[1].startValue=w,this.model.scales[0].ranges[1].endValue=this._maximum[u]),t.isNullOrUndefined(this.model.scales[0].customLabels[0])||(this.model.scales[0].customLabels[0].value=this.showHeaderLabel()?"X "+p:""),t.isNullOrUndefined(this.model.scales[0].customLabels[1])||(this.model.scales[0].customLabels[1].value=this.showHeaderLabel()?e:""),t.isNullOrUndefined(this.model.scales[0].customLabels[2])||(this.model.scales[0].customLabels[2].value=this.showHeaderLabel()?e:""),f=0;f<this.model.scales[0].ranges.length;f++)rt.push(n.extend({},t.datavisualization.CircularGauge.prototype._defaultScaleValues().ranges[0],this.model.scales[0].ranges[f]));for(f=0;f<this.model.scales[0].pointers.length;f++)ut.push(n.extend({},t.datavisualization.CircularGauge.prototype._defaultScaleValues().pointers[0],this.model.scales[0].pointers[f]));for(f=0;f<this.model.scales[0].labels.length;f++)this.model.labelFormatSettings.numberFormat!=t.PivotGauge.NumberFormat.Notation||t.isNullOrUndefined(t.datavisualization.CircularGauge.prototype._defaultScaleValues().labels[0])||(this._numberFormatConversion(this._JSONRecords[u].MeasureValue||this._JSONRecords[u].Value,this.model.labelFormatSettings.numberFormat,this.model.labelFormatSettings.decimalPlaces),this.model.scales[0].labels[f].unitText=this._notationStr),g.push(n.extend({},t.datavisualization.CircularGauge.prototype._defaultScaleValues().labels[0],this.model.scales[0].labels[f]));for(f=0;f<this.model.scales[0].labels.length;f++)g.push(n.extend({},t.datavisualization.CircularGauge.prototype._defaultScaleValues().labels[0],this.model.scales[0].labels[f]));for(f=0;f<this.model.scales[0].customLabels.length;f++)ft.push(n.extend({},t.datavisualization.CircularGauge.prototype._defaultScaleValues().customLabels[0],this.model.scales[0].customLabels[f]));for(f=0;f<this.model.scales[0].ticks.length;f++)et.push(n.extend({},t.datavisualization.CircularGauge.prototype._defaultScaleValues().ticks[0],this.model.scales[0].ticks[f]));if(this.model.scales[0].indicators)for(f=0;f<this.model.scales[0].indicators.length;f++)ot.push(n.extend({},t.datavisualization.CircularGauge.prototype._defaultScaleValues().indicators[0],this.model.scales[0].indicators[f]));for(lt={pointerCap:{radius:k||this.model.scales[0].pointerCap.radius,borderWidth:this.model.scales[0].pointerCap.borderWidth,interiorGradient:this.model.scales[0].pointerCap.interiorGradient,borderColor:this.model.scales[0].pointerCap.borderColor,backgroundColor:this.model.scales[0].pointerCap.backgroundColor},border:{color:this.model.scales[0].border.color,width:this.model.scales[0].border.width},majorIntervalValue:this.model.scales[0].majorIntervalValue,minorIntervalValue:this.model.scales[0].minorIntervalValue,minimum:this.model.scales[0].minimum,backgroundColor:this.model.scales[0].backgroundColor,direction:this.model.scales[0].direction,showPointers:this.model.scales[0].showPointers,showTicks:this.model.scales[0].showTicks,showLabels:this.model.scales[0].showLabels,showIndicators:this.model.scales[0].showIndicators,showRanges:this.model.scales[0].showRanges,showScaleBar:this.model.scales[0].showScaleBar,startAngle:this.model.scales[0].startAngle,sweepAngle:this.model.scales[0].sweepAngle,radius:this.model.scales[0].radius,size:this.model.scales[0].size,maximum:this.model.scales[0].maximum||this._maximum[u],pointers:ut,ticks:et,labels:g,ranges:rt,customLabels:ft,indicators:ot},f=0;f<this.model.scales.length;f++)f==0?nt.push(n.extend({},t.datavisualization.CircularGauge.prototype._defaultScaleValues(),lt)):nt.push(n.extend({},t.datavisualization.CircularGauge.prototype._defaultScaleValues(),this.model.scales[f]))}n("#"+this._id+"_"+u).ejCircularGauge({backgroundColor:this.model.backgroundColor,frame:{frameType:this.model.frame.frameType,halfCircleFrameStartAngle:this.model.halfCircleFrameStartAngle,halfCircleFrameEndAngle:this.model.halfCircleFrameEndAngle},radius:this.radius(),width:this.model.width,height:this.model.height,interiorGradient:this.model.interiorGradient,readOnly:this.model.readOnly,enableResize:this.model.isResponsive,enableAnimation:t.isMobile()?!1:this.model.enableAnimation,animationSpeed:this.model.animationSpeed,theme:this.model.theme,isRadialGradient:this.model.isRadialGradient,load:this.model.load,drawTicks:this.model.drawTicks,drawLabels:this.model.drawLabels,drawPointers:this.model.drawPointers,drawRange:this.model.drawRange,drawCustomLabel:this.model.drawCustomLabel,drawIndicators:this.model.drawIndicators,drawPointerCap:this.model.drawPointerCap,renderComplete:this.model.renderComplete,mouseClick:this.model.mouseClick,mouseClickMove:this.model.mouseClickMove,mouseClickUp:this.model.mouseClickUp,scales:nt});this._gaugeObj["obj"+u]=n("#"+this._id+"_"+u).data("ejCircularGauge")}this.removeImg();this._wireEvents();t.isNullOrUndefined(this._ogaugeWaitingPopup)||this._ogaugeWaitingPopup.hide()},refresh:function(){var t,i;if(this._gaugeObj==null||this._gaugeObj==r)for(t=0;t<this._JSONRecords.length;t++)this._gaugeObj["obj"+t]=n("#"+this._id+"_"+t).data("ejCircularGauge"),this._gaugeObj["obj"+t].refresh();else for(i in this._gaugeObj)this._gaugeObj[i].refresh()},getJSONData:function(n,i){t.olap.base.getJSONData({action:n.action},i,n.activeObject)},generateJSON:function(n,t){var i,r,f="",u;return this.baseObj=n.baseObj,this.pivotEngine=t,this.kpiInfo=[],this.baseObj._measureDt&&this.baseObj._measureDt.isKpiExist?(u=this.baseObj._measureDt.axis,t&&(u=="colheader"?(i=t.length,r=i?t[0].length:0):(r=t.length,i=r?t[0].length:0)),this._getKpiHeadersCollection(i,r,u)):this._getSummaryInfo(),f=JSON.stringify(this.kpiInfo),this._renderControlSuccess({PivotRecords:f,OlapReport:"null"}),this.kpiInfo},_getKpiHeadersCollection:function(t,i,r){var k=this.pivotEngine,o={},v,y,p,w,h,s,l,b,a,u,f,e,c;for(r=="rowheader"?(v=t,y=i,p=i,w=t,h="rowheader",s="colheader"):r=="colheader"&&(v=i,y=t,s="rowheader",h="colheader",p=t,w=i),u=0;u<y;u++){for(f=0;f<v;f++)if(e=null,e=r=="rowheader"?this.pivotEngine[f][u]:this.pivotEngine[u][f],e.CSS==h)o=this._fillKpiInfo_kpiAxis(e,h,u,o);else break;c=0;(c=this._isKpiExist(o))?this._copyKpi(parseInt(c),o):n.isEmptyObject(o)||this.kpiInfo.push(o);o={}}for(l={},b=this.kpiInfo,this.kpiInfo=[],u=0;u<w;u++){for(f=0;f<p;f++)if(e=null,r=="rowheader"?(e=this.pivotEngine[u][f],this.pivotEngine[f][u-1]&&(a=this.pivotEngine[f][u-1])):(e=this.pivotEngine[f][u],this.pivotEngine[f-1]&&(a=this.pivotEngine[f-1][u])),e&&e.CSS==s&&e.Value!="")l=this._fillKpiInfo_kpiAxis(e,s,u,o);else break;c=0;a&&a.CSS==s&&e.CSS.indexOf("summary")==-1&&this._mergeValues(l,b,r,u,f);l={}}},_mergeValues:function(t,i,r,u,f){for(var s={},h=i.length,e={},o=0;o<h;o++)e=i[o],t.MemberRowIndex?e.MemberRowIndex=t.MemberRowIndex:t.MemberColIndex&&(e.MemberColIndex=t.MemberColIndex),e.MemberName?t.MemberName&&(e.MemberName+="-"+t.MemberName):e.MemberName=t.MemberName,r=="rowheader"?(e.ValueIndex>0&&(e.MeasureValue=this.pivotEngine[f][e.ValueIndex].ActualValue,e.ActualMeasureValue=this.pivotEngine[f][e.ValueIndex].Value),e.TrendIndex>0&&(e.TrendValue=this.pivotEngine[f][e.TrendIndex].Value),e.StatusIndex>0&&(e.StatusValue=this.pivotEngine[f][e.StatusIndex].Value),e.GoalIndex>0):r=="colheader"&&(e.ValueIndex>0&&(e.MeasureValue=this.pivotEngine[e.ValueIndex][u].ActualValue,e.ActualMeasureValue=this.pivotEngine[e.ValueIndex][u].Value),e.TrendIndex>0&&(e.TrendValue=this.pivotEngine[e.TrendIndex][u].Value),e.StatusIndex>0&&(e.StatusValue=this.pivotEngine[e.StatusIndex][u].Value),e.GoalIndex>0&&(e.GoalValue=this.pivotEngine[e.GoalIndex][u].Value,e.ActualGoalValue=this.pivotEngine[e.GoalIndex][u].ActualValue)),e.IsValidKpi=!0,s={ActualMeasureValue:0,GoalCaption:"",Kpi_Name:"",MeasureCaption:"",GoalValue:"",ActualGoalValue:0,MeasureValue:"",MemberName:"",StatusValue:-2,TrendValue:-2,TrendGraphic:null,StatusGraphic:null,ValueIndex:"",TrendIndex:-1,GoalIndex:"",StatusIndex:-1,MemberRowIndex:null,MemberColIndex:null,IsValidKpi:!0},this.kpiInfo.push(n.extend(s,e))},_isKpiExist:function(n){for(var t=0;t<this.kpiInfo.length;t++)if(this.kpiInfo[t].MemberName==n.MemberName&&this.kpiInfo[t].Kpi_Name==n.Kpi_Name)return t+"";return!1},_copyKpi:function(t,i){this.kpiInfo[t]=n.extend(this.kpiInfo[t],i)},_fillKpiInfo_kpiAxis:function(n,t,i,r){var u=n.Info.split("::")[0],f;if(!n.kpiInfo&&n.CSS==t&&u.indexOf("[Measures]")>-1&&!n.kpiInfo)r.Kpi_Name=this._getKpiName(u),r.MeasureCaption=n.Value,r.ValueIndex=i;else if(n.CSS==t){n.kpiInfo?r.Kpi_Name=n.kpiInfo.Value:(!r.MemberName&&n.Value?r.MemberName=n.Value:n.Value&&(r.MemberName+=" - "+n.Value),t=="colheader"?r.MemberColIndex=i:r.MemberRowIndex=i);f=n.kpi?n.kpi:"none";switch(f){case"status":r.StatusGraphic=n.kpiInfo.Graphic;r.StatusIndex=i;break;case"goal":r.GoalCaption=n.Value;r.GoalIndex=i;break;case"trend":r.TrendGraphic=n.kpiInfo.Graphic;r.TrendIndex=i}}return r},_getSummaryInfo:function(){for(var o,r={},u=this,e=n.map(this.pivotEngine,function(f){var o=!1,s,e;if(r.Measure="",r.Value="",r.DoubleValue=0,t.browserInfo().name=="msie"&&t.browserInfo().version<=8){for(e=0;e<f.length;e++)(f[e].CSS.indexOf("summary col")>-1||f[e].CSS.indexOf("summary cgtot calc")>-1)&&(o=!0),(o&&f[e].Info.indexOf("[Measures]")>-1||f[e].CSS.indexOf("summary cgtot calc")>-1)&&(r.Measure=f[e].Value);if(!o||u._dataModel=="Pivot"){if(o&&u._dataModel=="Pivot")return r.Value=f[e-1].Value.toString(),r.DoubleValue=f[e-1].Value,n.extend({},r)}else return r.Value=f[e-1].ActualValue,r.DoubleValue=parseFloat(f[e-1].ActualValue),n.extend({},r)}else{for(s=0,i=0;i<f.length;i++)s=i,(f[i].CSS.indexOf("summary col")>-1||f[i].CSS.indexOf("summary cgtot calc")>-1)&&(o=!0),(o&&f[i].Info.indexOf("[Measures]")>-1||f[i].CSS.indexOf("summary cgtot calc")>-1)&&(r.Measure=f[i].Value);if(!o||u._dataModel=="Pivot"){if(o&&u._dataModel=="Pivot")return r.Value=f[parseInt(s)].Value.toString(),r.DoubleValue=f[parseInt(s)].Value,n.extend({},r)}else return r.Value=f[parseInt(s)].ActualValue,r.DoubleValue=parseFloat(f[parseInt(s)].ActualValue),n.extend({},r)}}),f=0;f<e.length;f++)o={Caption:null,Object:null,CellIndex:-1,Type:-1,Measure:"",ClassName:"",DoubleValue:0,ExpandState:0,HasChildren:!1,Level:-1,RowIndex:3,Range:null,Span:null,Tag:null,UniqueName:"",Value:""},this.kpiInfo.push(n.extend(o,e[f]))},_getKpiName:function(t){var r=this.baseObj._kpi,i="";return(i=n(r).find("row:contains('"+t+"')"),i)?n(i).find("KPI_NAME").text():""}});t.PivotGauge.Locale=t.PivotGauge.Locale||{};t.PivotGauge.Locale["en-US"]={RevenueGoal:"Revenue Goal",RevenueValue:"Revenue Value",Exception:"Exception"};t.PivotGauge.NumberFormat={Default:"default",Currency:"currency",Percentage:"percentage",Fraction:"fraction",Scientific:"scientific",Text:"text",Notation:"notation"};t.PivotGauge.AxisName={Rows:"rows",Columns:"columns"};t.PivotGauge.OperationalMode={ClientMode:"clientmode",ServerMode:"servermode"};t.PivotGauge.AnalysisMode={Olap:"olap",Pivot:"pivot"}})(jQuery,Syncfusion)});
