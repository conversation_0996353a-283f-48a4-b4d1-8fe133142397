/*!
*  filename: ej.culture.vi-VN.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/

ej.addCulture("vi-VN", { name: "vi-VN", englishName: "Vietnamese (Vietnam)", nativeName: "Tiếng Việt (Việt Nam)", language: "vi", numberFormat: { ",": ".", ".": ",", percent: { ",": ".", ".": "," }, currency: { pattern: ["-n $", "n $"], ",": ".", ".": ",", symbol: "₫" } }, calendars: { standard: { firstDay: 1, days: { names: ["Chủ Nhật", "<PERSON>h<PERSON><PERSON> Hai", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>h<PERSON><PERSON> Bảy"], namesAbbr: ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>ă<PERSON>", "Sáu", "Bảy"], namesShort: ["C", "H", "B", "T", "N", "S", "B"] }, months: { names: ["Tháng Giêng", "Tháng Hai", "Tháng Ba", "Tháng Tư", "Tháng Năm", "Tháng Sáu", "Tháng Bảy", "Tháng Tám", "Tháng Chín", "Tháng Mười", "Tháng Mười Một", "Tháng Mười Hai", ""], namesAbbr: ["Thg1", "Thg2", "Thg3", "Thg4", "Thg5", "Thg6", "Thg7", "Thg8", "Thg9", "Thg10", "Thg11", "Thg12", ""] }, AM: ["SA", "sa", "SA"], PM: ["CH", "ch", "CH"], patterns: { d: "dd/MM/yyyy", D: "dd MMMM yyyy", f: "dd MMMM yyyy h:mm tt", F: "dd MMMM yyyy h:mm:ss tt", M: "dd MMMM" } } } });;