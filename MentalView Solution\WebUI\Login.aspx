﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Login.aspx.cs" Inherits="WebUI.Login" Culture="auto" meta:resourcekey="Page" UICulture="auto" %>

<!DOCTYPE html>
<%@ Import Namespace="System.Web.Optimization" %>
<%@ Register Src="~/ServerMessage.ascx" TagPrefix="uc1" TagName="ServerMessage" %>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>

    <asp:PlaceHolder runat="server">
        <%:Scripts.Render("~/bundles/modernizr") %>
    </asp:PlaceHolder>
    <webopt:BundleReference runat="server" Path="~/Content/css" />
    <link href="~/favicon.ico" rel="shortcut icon" type="image/x-icon" />
    <%:Styles.Render("~/Content/bootstrap.min.css") %>
    <%:Styles.Render("~/Content/Site.css") %>
    <%:Styles.Render("~/Content/ej/web/ej.widgets.core.min.css") %>
    <%:Styles.Render("~/Content/ej/web/default-theme/ej.theme.min.css") %>
    <%:Scripts.Render("~/Scripts/jquery-3.2.1.min.js")%>
    <%:Scripts.Render("~/Scripts/jquery.cookie.min.js")%>
    <%:Scripts.Render("~/Scripts/bootstrap.min.js")%>
    <%:Scripts.Render("~/Scripts/jsrender.min.js")%>
    <%:Scripts.Render("~/Scripts/ej/ej.web.all.min.js")%>
    <%:Scripts.Render("~/Scripts/ej/ej.webform.min.js")%>
    <%:Scripts.Render("~/Scripts/AdminLTE/adminlte.min.js")%>
    <%:Scripts.Render("~/Scripts/jquery.validate.min.js")%>
    <%:Scripts.Render("~/Scripts/custom.js")%>



    <link href="Content/AdminLTE/AdminLTE.min.css" rel="stylesheet" />
    <link href="Content/AdminLTE/skins/skin-blue-light.min.css" rel="stylesheet" />
    <link href="Content/AdminLTE/skins/adminlte.min.css.map" />
    <link href="Content/font-awesome-template/css/font-awesome.min.css" rel="stylesheet" />
    <link href="Content/font-awesome/css/all.min.css" rel="stylesheet" />
</head>
<body class="hold-transition login-page">
    <form id="form1" runat="server">
        <div class="login-box">
            <div class="login-logo">
                <img src="Content/images/MentalView%20logo%20big.png" />
            </div>
            <!-- /.login-logo -->
            <div class="login-box-body">
                <p runat="server" id="loginToLbl" class="login-box-msg">Σύνδεση στο {0}</p>
                <div class="form-horizontal">

                    <div class="form-group">
                        <asp:Label for="usernameTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="usernameLbl"></asp:Label>
                        <div class="col-sm-8">
                            <input type="text" runat="server" class="form-control" id="usernameTxtBox" maxlength="50" placeholder="">
                        </div>
                    </div>
                    <div class="form-group">
                        <asp:Label for="passwordTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="passwordLbl"></asp:Label>
                        <div class="col-sm-8">
                            <input type="password" runat="server" class="form-control" id="passwordTxtBox" maxlength="50" placeholder="">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <asp:Label ID="messageLbl" runat="server" CssClass="text-danger" Style="min-height: 16px; display: block; margin-bottom:5px;"></asp:Label>
                    </div>
                    <!-- /.col -->
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <%--<button type="button" id="loginBtn" runat="server" class="btn btn-info btn-flat pull-right" onserverclick="loginBtn_ServerClick">
                            <asp:Label meta:resourcekey="loginBtn" runat="server"></asp:Label></button>--%>
                        <button runat="server" onserverclick="loginBtn_ServerClick" class="btn btn-info btn-flat pull-right " type="submit"><asp:Label meta:resourcekey="loginBtn" runat="server"></asp:Label></button>
                    </div>
                    <!-- /.col -->
                </div>
            </div>
            <!-- /.login-box-body -->
        </div>
        <!-- /.login-box -->

        <uc1:servermessage runat="server" id="serverMessage" />
    </form>
</body>
</html>
