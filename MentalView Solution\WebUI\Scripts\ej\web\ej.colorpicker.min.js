/*!
*  filename: ej.colorpicker.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./ej.button.min","./ej.slider.min","./ej.splitbutton.min","./ej.menu.min"],n):n()})(function(){(function(n,t,i){t.widget("ejColorPicker","ej.ColorPicker",{_rootCSS:"e-colorpicker",element:null,model:null,validTags:["input","div"],_addToPersist:["value","opacityValue"],_setFirst:!1,angular:{require:["?ngModel","^?form","^?ngModelOptions"]},defaults:{enableOpacity:!0,opacityValue:100,columns:10,palette:"basicpalette",htmlAttributes:{},buttonMode:"split",custom:[],presetType:"basic",modelType:"picker",locale:"en-US",showPreview:!0,showTooltip:!1,showClearButton:!1,showSwitcher:!0,value:null,displayInline:!1,buttonText:{apply:"Apply",cancel:"Cancel",swatches:"Swatches"},tooltipText:{switcher:"Switcher",addButton:"Add Color",basic:"Basic",monoChrome:"Mono Chrome",flatColors:"Flat Colors",seaWolf:"Sea Wolf",webColors:"Web Colors",sandy:"Sandy",pinkShades:"Pink Shades",misty:"Misty",citrus:"Citrus",vintage:"Vintage",moonLight:"Moon Light",candyCrush:"Candy Crush",currentColor:"Current Color",selectedColor:"Selected Color"},showApplyCancel:!0,showRecentColors:!1,toolIcon:null,cssClass:"",enabled:!0,change:null,select:null,open:null,close:null,create:null,destroy:null},dataTypes:{modelType:"enum",palette:"enum",presetType:"enum",cssClass:"string",displayInline:"boolean",locale:"string",showSwitcher:"boolean",showRecentColors:"boolean",enabled:"boolean",showPreview:"boolean",enableOpacity:"boolean",buttonText:"data",custom:"array",htmlAttributs:"data"},observables:["value","opacityValue"],value:t.util.valueFunction("value"),opacityValue:t.util.valueFunction("opacityValue"),_setModel:function(n){for(var i in n)switch(i){case"enableOpacity":this.model.enableOpacity=n[i];this.popupContainer&&(this._previewSlider(this.model.enableOpacity),this._valueOperation());break;case"opacityValue":if(this.model.enableOpacity){this.model.opacityValue=n[i];this._tempOpacity=parseFloat(t.util.getVal(n[i]));this.popupContainer&&(this._tempOpacity=parseFloat(t.util.getVal(n[i])),this._opacity.option("value",this._tempOpacity),this._switch||this._changeOpacity(),this._updateValue(),this.opacityValue(this._tempOpacity),typeof n[i]=="function"?n[i](this.opacityValue()):n[i]=this.opacityValue());break}else return!1;case"custom":this.model.custom=n[i];this._reInitialize();break;case"palette":this.model.palette=n[i];this._reInitialize();break;case"columns":this.model.columns=parseFloat(n[i]);this._reInitialize();n[i]=this.model.columns;break;case"presetType":if(this.model.presetType=n[i],t.isNullOrUndefined(r[this.model.presetType]))return!1;this._reInitialize();break;case"buttonMode":this._unBindIconClick();this._buttonElement=t.ColorPicker.ButtonMode.Split==n[i]?this.dropdownbutton:this.wrapper;t.ColorPicker.ButtonMode.Split==n[i]?this.wrapper.addClass("e-split"):this.wrapper.removeClass("e-split");this._bindIconClick();break;case"showTooltip":this.model.showTooltip=n[i];this.popupContainer&&(this._colorSlider.option("showTooltip",n[i]),this._opacity.option("showTooltip",n[i]));break;case"value":this._setValue(t.util.getVal(n[i]),!0);typeof n[i]=="function"?n[i](this.value()):n[i]=this.value();break;case"modelType":this.model.modelType=n[i];this._reInitialize();break;case"showSwitcher":this.model.showSwitcher=n[i];this.popupContainer&&this._showSwitcher();break;case"tooltipText":this.model.tooltipText=n[i];this.popupContainer&&this._toolTipText(n[i]);break;case"locale":this.model.locale=n[i];this.popupContainer&&this._localize(n[i]);break;case"showPreview":this.model.showPreview=n[i];this.popupContainer&&this._previewPane(this.model.showPreview);break;case"buttonText":this._setButtonText=!0;this.model.buttonText=n[i];this.popupContainer&&this._buttonText(this.model.buttonText);break;case"displayInline":this.model.displayInline=n[i];this._setDisplayInline(n[i]);break;case"cssClass":this._setSkin(n[i]);this.model.cssClass=n[i];break;case"enabled":this._enabled(n[i]);break;case"showRecentColors":this.model.showRecentColors=n[i];this.popupContainer&&this._previewColor(this.model.showRecentColors);break;case"htmlAttributes":this.model.htmlAttributes=n[i];this._addAttr(this.model.htmlAttributes);break;case"showClearButton":this.model.showClearButton=n[i];this.popupContainer&&this._showClearIcon(n[i]);break;case"showApplyCancel":this.model.showApplyCancel=n[i];this.popupContainer&&this._buttonContainer()}},_setSkin:function(n){this.wrapper?this.wrapper.removeClass(this.model.cssClass).addClass(n):this.element.removeClass(this.model.cssClass).addClass(n)},_showSwitcher:function(){this.model.showSwitcher?(this._changeTag.removeClass("e-hide"),this.model.modelType=="picker"?this._switcher.addClass("e-paletteModel").removeClass("e-pickerModel"):this._switcher.addClass("e-pickerModel").removeClass("e-paletteModel")):this._changeTag.addClass("e-hide")},_pickerType:function(){this._modelType="picker";this.PaletteWrapper.removeAttr("style");this.PaletteWrapper.addClass("e-hide");this._gradient.removeClass("e-hide");this._gradient.fadeIn(200);this._presetTag.parents(".e-split.e-widget").addClass("e-hide");this._switcher.removeAttr("class");this._switcher.addClass("e-color-image e-paletteModel");this._switch=!0;this._rgbValue();this._hueGradient();this._updateUI();this._alphaGradient(this.RGBToHEX(this.rgb));this._hsva.ejButton("enable");this._switchEvents();this._unSwitchEvents();this._hideUnBindEvents();this.isPopupOpen&&this._showBindEvents();this.model.modelType=="default"?this._changeTag.removeClass("e-hide"):this._changeTag.addClass("e-hide");this.popupList.prepend(this._gradient);this._showSwitcher()},_paletteType:function(){this._gradient.removeAttr("style");this._presetTag.parents(".e-split.e-widget").removeClass("e-hide");this.PaletteWrapper.removeClass("e-hide");this.PaletteWrapper.fadeIn(200);this._switch=!1;this._disableHSVButton();this._cellSelect();this._switchEvents();this._unSwitchEvents();this._splitObj.option("prefixIcon","e-icon e-color-image e-"+this.model.presetType);this.popupList.prepend(this.PaletteWrapper);this._showSwitcher()},_reInitialize:function(){this._destroyPalette(!1)},_destroyPalette:function(n){this.PaletteWrapper.remove();(n||this._columns!=this.model.columns&&this.model.palette!=="custompalette")&&(this.PaletteWrapper=this._presetType(this._presetsId));this._temp!==this.model.presetType&&(this.PaletteWrapper=this._layoutType(this.model.palette));this.model.modelType=="palette"&&(this._modelType="palette",this.PaletteWrapper=this._layoutType(this.model.palette),this._hideUnBindEvents(),this.isPopupOpen&&this._showBindEvents(),this._gradient.addClass("e-hide"),this._paletteType(),n||this.model.palette=="custompalette"?"":this._splitObj.option("prefixIcon","e-icon e-color-image e-"+this.model.presetType));this.model.modelType=="picker"&&(this._pickerType(),this.model.displayInline&&!this.element.is("input")&&this._footer.addClass("e-hide"));this._temp=this.model.presetType;this._columns=this.model.columns;this._modelType=="picker"?this._presetTag.parents(".e-split.e-widget").addClass("e-hide"):this._presetTag.parents(".e-split.e-widget").removeClass("e-hide");this.refresh()},_previewColor:function(n){n?(this._swatchesArea.css("display","block"),this._bindRecentEvent()):(this._swatchesArea.css("display","none"),this._unBindRecentEvent())},_buttonText:function(i){this._setButtonText?this._setButtonText=!1:n.extend(this.model.buttonText,i);t.isNullOrUndefined(this._buttonTag)||this._buttonTag.html(this.model.buttonText.apply);t.isNullOrUndefined(this._cancelTag)||this._cancelTag.html(this.model.buttonText.cancel);this._spnTag.html(this.model.buttonText.swatches)},_toolTipText:function(t){n.extend(this.model.tooltipText,t);this._addTitleText()},_previewPane:function(n){n?this._previewTag.removeClass("e-hide"):this._previewTag.addClass("e-hide")},_previewSlider:function(n){n?this._opacity.enable():this._opacity.disable()},_getLocalizedLabels:function(){return t.getLocalizedConstants(this.sfType,this.model.locale)},_localize:function(){this._localizedLabels=this._getLocalizedLabels();(this._options.locale!="en-US"||t.isNullOrUndefined(this._options.buttonText))&&t.isNullOrUndefined(this._options.tooltipText)?t.isNullOrUndefined(this._localizedLabels)||(t.isNullOrUndefined(this._localizedLabels.buttonText)||this._buttonText(this._localizedLabels.buttonText),t.isNullOrUndefined(this._localizedLabels.tooltipText)||this._toolTipText(this._localizedLabels.tooltipText)):(t.isNullOrUndefined(this._options.buttonText)||this._buttonText(this._options.buttonText),t.isNullOrUndefined(this._options.tooltipText)||this._toolTipText(this._options.tooltipText))},_destroy:function(){this.model.displayInline&&n(window).off("resize",n.proxy(this._OnWindowResize,this));this.isPopupOpen&&this.hide();this.popupContainer&&this.popupContainer.remove();this.wrapper&&(this.element.insertAfter(this.wrapper),this.wrapper.remove(),this._presetContainer&&this._presetContainer.parent(".e-menu-wrap").remove());this.element.removeClass("e-colorpicker e-input e-widget").removeAttr("style name").val(this.element.attr("value"))},_init:function(i){this._options=i;this._browser=t.browserInfo();this._isFocused=!1;this.isPopupOpen=!1;this._dataBind=!1;this._modelType="picker";this._id&&n("#"+this._id+"_popup").remove();"#"+this._id+"_Presets"&&n("#"+this._id+"_Presets").parent(".e-menu-wrap").remove();this.model.palette==="basicpalette"?this._presetsId="e-presets30":"";this._tempOpacity=this.opacityValue();this._renderControl();this._tempValue=t.isNullOrUndefined(this.value())&&this.element[0].value!==""?this.element[0].value:this.value();this._previousValue=this._previousColor=this._tempValue;this._previousOpacity=this._tempOpacity;this._tempValue&&this._setValue(this._tempValue);this._columns=this.model.columns;this._temp=this.model.presetType;this._tempValue||(this._previousValue="");this.model.enabled||this._enabled(this.model.enabled);this._cancelTag=t.buildTag("button.e-cancelButton","",{},{type:"button"})},_renderPopup:function(){this._renderPopupPanelWrapper();this._selectedButton=this._groupTag.find(".e-click");this._buttonContainer();this._renderPopupElement();this._previewPane(this.model.showPreview);this._previewColor(this.model.showRecentColors);this._localize();this.popupContainer.css("display","none");n("body").append(this.popupContainer);this._isOpen=!1;this._switch&&this._previewSlider(this.model.enableOpacity);this.element.is("input")&&(this.popupContainer.find("button.e-applyButton").length==0&&this._buttonContainer(),this._footer.css({display:"block"}),this.wrapper.removeClass("e-focus"),this._off(n(document),"mousedown",this._onDocumentClick));this._isFocused=this.isPopupOpen=!1;this._wirePopupEvents();this._switchEvents();this._tempOpacity=this.opacityValue();this.model.palette==="custompalette"&&this._presetTag.parents(".e-split.e-widget").addClass("e-hide");this.popupContainer.find("button.e-presets").ejSplitButton({targetID:this._presetContainer.attr("id")});this._tempValue&&(this._setValue(this._tempValue),this._switch&&this._rgbValue());this._hsvValue();this._hueGradient();this._addTitleText();this._showClearIcon(this.model.showClearButton);this._tempValue||(this._colorSlider.option("value",parseInt(this._hsv.h)),this._opacity.option("value",this._tempOpacity),this._alphaGradient("#fff"));this._addAttr(this.model.htmlAttributes)},_renderControl:function(){this._createWrapper();this._buttonElement=t.ColorPicker.ButtonMode.Split==this.model.buttonMode?this.dropdownbutton:this.wrapper;this.model.buttonMode==t.ColorPicker.ButtonMode.Split&&this.element.is("input")&&this.wrapper.addClass("e-split");this._addAttr(this.model.htmlAttributes);this._setDisplayInline(this.model.displayInline);this._wireEvents()},_createWrapper:function(){this.element.is("input")&&(this.element.addClass("e-input e-widget"),this.element.attr("aria-label","colorpicker"),this.spanElement=t.buildTag("span.e-selected-color"),this.wrapper=t.buildTag("span.e-colorwidget e-picker e-widget "+this.model.cssClass).attr({tabindex:"0","aria-expanded":!1,"aria-haspopup":!0,"aria-owns":"popup"}),this._id&&this.wrapper.attr("id",this._id+"Wrapper"),this.container=t.buildTag("span.e-in-wrap e-box e-splitarrowright"),this.drpbtnspan=t.buildTag("span.e-icon e-arrow-sans-down","",{},{"aria-label":"select"}),this.dropdownbutton=t.buildTag("span.e-select","",{},{role:"button"}).append(this.drpbtnspan),this.iconWrapper=t.buildTag("span.e-tool-icon "+this.model.toolIcon),this.colorContainer=t.buildTag("span.e-color-container"),this.colorContainer.append(this.spanElement),this.container.insertAfter(this.element),t.isNullOrUndefined(this.model.toolIcon)?this.container.append(this.colorContainer):(this.colorContainer.prepend(this.iconWrapper),this.container.addClass("e-tool"),this.container.append(this.colorContainer)),this.container.append(this.element,this.dropdownbutton),this.wrapper.insertBefore(this.container),this.wrapper.append(this.container),this.element.css("display","none").val(this.value()));this._checkNameAttr()},_addTitleText:function(){this._switcher.attr("title",this.model.tooltipText.switcher);this._spanTag.attr("title",this.model.tooltipText.addButton);this._presetLi.find("#e-presets00").attr("title",this.model.tooltipText.webColors);this._presetLi.find("#e-presets01").attr("title",this.model.tooltipText.vintage);this._presetLi.find("#e-presets02").attr("title",this.model.tooltipText.seaWolf);this._presetLi.find("#e-presets10").attr("title",this.model.tooltipText.sandy);this._presetLi.find("#e-presets11").attr("title",this.model.tooltipText.pinkShades);this._presetLi.find("#e-presets12").attr("title",this.model.tooltipText.moonLight);this._presetLi.find("#e-presets20").attr("title",this.model.tooltipText.monoChrome);this._presetLi.find("#e-presets21").attr("title",this.model.tooltipText.misty);this._presetLi.find("#e-presets22").attr("title",this.model.tooltipText.flatColors);this._presetLi.find("#e-presets30").attr("title",this.model.tooltipText.basic);this._presetLi.find("#e-presets31").attr("title",this.model.tooltipText.candyCrush);this._presetLi.find("#e-presets32").attr("title",this.model.tooltipText.citrus);this._currentTag.attr("title",this.model.tooltipText.currentColor);this._previousTag.attr("title",this.model.tooltipText.selectedColor)},_renderPopupPanelWrapper:function(){var u,r,f,i;for(this.popupContainer=t.buildTag("div.e-colorpicker e-box e-popup e-widget "+this.model.cssClass,"",{},{role:"grid","aria-readonly":"true",tabindex:"0",style:"visibility:hidden"}),this._id&&this.popupContainer.attr("id",this._id+"_popup"),n("body").append(this.popupContainer),this.popupList=t.buildTag("div.e-popupWrapper"),this._gradient=t.buildTag("div.e-container"),this._colorArea=t.buildTag("div.e-hsv-color"),this._gradientArea=t.buildTag("div.e-hsv-gradient"),this._handleArea=t.buildTag("div.e-draghandle e-color-image"),this._browser=="msie"&&this._handleArea.addClass("e-pinch"),this._colorArea.append(this._gradientArea,this._handleArea),this._picker=t.buildTag("div.e-gradient"),this._hueSlider=t.buildTag("div.e-widget e-hue e-state-default"),this._alphaSlider=t.buildTag("div.e-widget e-opacity e-state-default"),this._picker.append(this._hueSlider,this._alphaSlider),this._gradient.append(this._colorArea,this._picker),this.popupList.append(this._gradient),this._footerBlock=t.buildTag("div.e-footerContainer"),this._templateWrapper=t.buildTag("div.e-buttons"),this._groupTag=t.buildTag("div.e-grpbtn"),this._formEle=t.buildTag("div.e-form"),this._rgb=t.buildTag("button.e-rgbButton e-click","",{},{type:"button"}),this._hexCode=t.buildTag("button.e-hexButton","",{},{type:"button"}),this._hsva=t.buildTag("button.e-hsvButton","",{},{type:"button"}),this._groupTag.append(this._rgb,this._hexCode,this._hsva),this._codeEditor=t.buildTag("div.e-codeeditor"),this._inputTag=t.buildTag("input.e-color-code","",{},{type:"text",tabindex:"0",maxLength:"22"}),this._codeEditor.append(this._inputTag),this._inputTag.attr("aria-label","color-code"),this._formEle.append(this._groupTag,this._codeEditor),this._previewTag=t.buildTag("div.e-preview").attr({tabindex:"0"}),this._currentTag=t.buildTag("div.e-current"),this._previousTag=t.buildTag("div.e-previous"),this._previewTag.append(this._currentTag,this._previousTag),this._templateWrapper.append(this._formEle,this._previewTag),this._swatchesArea=t.buildTag("div.e-color-labels"),u=11,this._divTag=t.buildTag("div.e-recent-color"),this._addTag=t.buildTag("div.e-colorblock e-block"),this._spanTag=t.buildTag("div.e-color e-color-image e-add"),this._addTag.append(this._spanTag),this._divTag.append(this._addTag),r=0;r<u;r++)this._liTag=t.buildTag("div.e-colorblock e-block"),f=t.buildTag("div.e-color e-color-image e-empty"),this._liTag.append(f),this._divTag.prepend(this._liTag);this._swatchesArea.append(this._divTag);this._footer=t.buildTag("div.e-footer");this._swatches=t.buildTag("div.e-element");this._changeTag=t.buildTag("div.e-switcher").attr("tabindex","0");this._switcher=t.buildTag("div.e-color-image");this._presetTag=t.buildTag("button.e-presets e-colorSplit");this._presetTag.attr("id",this.element[0].id+"_split");this._presets=t.buildTag("div");this._changeTag.append(this._switcher);this._presetTag.append(this._presets);this._swatches.append(this._changeTag,this._presetTag);this._footer.append(this._swatches);this._footerBlock.append(this._templateWrapper,this._swatchesArea,this._footer);this.PaletteWrapper=this._layoutType(this.model.palette);this.popupList.append(this.PaletteWrapper,this._footerBlock);i=n("#"+this._id+"_Presets").get(0);i&&(n(i).parent().hasClass("e-menu-wrap")?n(i).parent().remove():n(i).remove());this._presetContainer=n("<ul id='"+this._id+"_Presets' class='e-presetWrapper' style= top:87px ><\/ul>");this._presetLi=t.buildTag("li.e-item");this._presetLi.append(this._renderPresets());this._presetContainer.append(this._presetLi);this.popupList.append(this._presetContainer);this.popupContainer.append(this.popupList);(this._browser.name="msie"&&(this._browser.version=="9.0"||this._browser.version=="8.0"))?this._hueSlider.addClass("e-color-image e-filter"):this._hueSlider.addClass("e-common");this._width=this._gradientArea.width();this._height=this._gradientArea.height()},_buttonContainer:function(){this.model.showApplyCancel?(this._buttonTag=t.buildTag("button.e-applyButton","",{},{type:"button"}),this._cancelTag=t.buildTag("button.e-cancelButton","",{},{type:"button"}),this._footer.append(this._buttonTag,this._cancelTag),this._applyObj=this.popupContainer.find("button.e-applyButton").ejButton({text:this.model.buttonText.apply,type:"button",cssClass:"e-flat"}).data("ejButton"),this._cancelObj=this.popupContainer.find("button.e-cancelButton").ejButton({text:this.model.buttonText.cancel,type:"button",cssClass:"e-flat"}).data("ejButton"),this._on(this._cancelTag,"click",this._hidePopup),this._on(this._buttonTag,"click",this._buttonClick)):this._buttonTag!==i&&this._cancelTag!==i&&(this._buttonTag.remove(),this._cancelTag.remove(),this._off(this._cancelTag,"click",this._hidePopup),this._off(this._buttonTag,"click",this._buttonClick))},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.wrapper.addClass(n):t=="required"?i.element.attr(t,n):t=="disabled"&&n=="disabled"?i._enabled(!1):i.wrapper.attr(t,n)})},_showClearIcon:function(n){n?(this._clearIcon=t.buildTag("div",{},{},{"class":"e-icon e-close_01"}).hide(),this._codeEditor.append(this._clearIcon),this._on(this._clearIcon,"mousedown",this._clearColor),this._on(this._clearIcon,"click",this._clearColor)):(this._clearIcon&&this._clearIcon.remove(),this._off(this._clearIcon,"mousedown",this._clearColor),this._off(this._clearIcon,"click",this._clearColor))},_colorPresetsClick:function(t){this._presetsId=t.currentTarget.id;this._presetsId==="e-presets00"?this.model.presetType="webcolors":this._presetsId==="e-presets01"?this.model.presetType="vintage":this._presetsId==="e-presets02"?this.model.presetType="seawolf":this._presetsId==="e-presets10"?this.model.presetType="sandy":this._presetsId==="e-presets11"?this.model.presetType="pinkshades":this._presetsId==="e-presets12"?this.model.presetType="moonlight":this._presetsId==="e-presets20"?this.model.presetType="monochrome":this._presetsId==="e-presets21"?this.model.presetType="misty":this._presetsId==="e-presets22"?this.model.presetType="flatcolors":this._presetsId==="e-presets30"?this.model.presetType="basic":this._presetsId==="e-presets31"?this.model.presetType="candycrush":this._presetsId==="e-presets32"&&(this.model.presetType="citrus");this._splitObj.option("prefixIcon","e-icon e-color-image e-"+this.model.presetType);n("#"+this._id+"_Presets").find("li.e-preset-row").removeClass("e-presetsactive");n("#"+this._presetsId).addClass("e-presetsactive");this.PaletteWrapper.remove();this._modelType=="palette"&&(this.PaletteWrapper=this._layoutType(this.model.palette),this._gradient.addClass("e-hide"),this._paletteType(),this._switcher.addClass("e-pickerModel").removeClass("e-paletteModel"))},_renderPresets:function(){var r=t.buildTag("div.e-presets-table"),e,u,n,i,f;for(this._spnTag=t.buildTag("span.e-presetHeader"),this._spnTag.html(this.model.buttonText.swatches),r.append(this._spnTag),e=0,n=0;n<4;n++){for(u=t.buildTag("ul.e-tablerow"),i=0;i<3;i++)f=t.buildTag("li.e-color-image e-preset-row#e-presets"+[n]+[i]),f.appendTo(u);u.appendTo(r)}return r},_renderPopupElement:function(){var i=this;this._hsv={h:360,s:0,v:100};this._rgb.ejButton({text:"RGBA",type:"button"});this._hexCode.ejButton({text:"HEX",type:"button"});this._hsva.ejButton({text:"HSVA",type:"button"});this._splitObj=this._presetTag.ejSplitButton({size:"normal",showRoundedCorner:!0,contentType:"imageonly"}).data("ejSplitButton");this._splitObj.element.attr("aria-label","Presets");this._splitObj.dropbutton.attr("aria-label","Select");this._splitObj.option("beforeOpen",function(n){i._bindClickOperation(n)});this._presetTag.parents(".e-split.e-widget").css({height:"27px"});this.model.custom.length==0?this._splitObj.option("prefixIcon","e-icon e-color-image e-"+this.model.presetType):"";n("#"+this._presetsId).addClass("e-presetsactive");this._splitObj._getXYpos=function(){n("#"+this.model.targetID).ejMenu({animationType:"none"});var t,i,r=this.dropbutton.offset();return t=r.left-this.dropbutton.prev().outerWidth()-1,i=r.top-n("#"+this.model.targetID).outerHeight()-1,{x:t,y:i}};this._colorSlider=this._hueSlider.ejSlider({orientation:"Vertical",showTooltip:this.model.showTooltip,minValue:0,maxValue:360,change:function(n){i._changeHue(n)},slide:function(n){i._changeHue(n)}}).data("ejSlider");this._opacity=this._alphaSlider.ejSlider({value:this.opacityValue(),showTooltip:this.model.showTooltip,orientation:"Vertical",incrementStep:5,value:100,change:function(n){i._changeAlpha(n)},slide:function(n){i._changeAlpha(n)}}).data("ejSlider");(this._browser.name="msie"&&this._browser.version=="8.0")&&(this._handleTag=t.buildTag("div.e-handle-wrapper"),this._handleTag.appendTo(this._opacity.element.find("a.e-handle")));this._colorSlider.firstHandle.css({height:"13px",width:"13px"});this._opacity.firstHandle.css({height:"13px",width:"13px"});this.popupContainer.css({visibility:"visible",display:"none"});this.model.modelType=="picker"?(this._modelType="picker",this._gradient.removeClass("e-hide"),this.PaletteWrapper.addClass("e-hide"),this._presetTag.parents(".e-split.e-widget").addClass("e-hide"),this._showSwitcher(),this._switch=!0):this.model.modelType=="palette"&&(this._modelType="palette",this._presetTag.parents(".e-split.e-widget").removeClass("e-hide"),this.PaletteWrapper.removeClass("e-hide"),this._gradient.addClass("e-hide"),this._hsva.ejButton("disable"),this._showSwitcher(),this._switch=!1)},_layoutType:function(n){return typeof n=="string"&&n=="basicpalette"?this._collection=this._paletteGenerate(r[this.model.presetType],this.model.columns):typeof n=="string"&&n=="custompalette"&&this.model.modelType=="palette"&&(this._collection=this._paletteGenerate(this.model.custom,this.model.columns)),n=="custompalette"?this._collection.addClass("e-custom"):"",this._collection},_presetType:function(n){return n==="e-presets00"?this._collection=this._paletteGenerate(r.webcolors,this.model.columns):n==="e-presets01"?this._collection=this._paletteGenerate(r.vintage,this.model.columns):n==="e-presets02"?this._collection=this._paletteGenerate(r.seawolf,this.model.columns):n==="e-presets10"?this._collection=this._paletteGenerate(r.sandy,this.model.columns):n==="e-presets11"?this._collection=this._paletteGenerate(r.pinkshades,this.model.columns):n==="e-presets12"?this._collection=this._paletteGenerate(r.moonlight,this.model.columns):n==="e-presets20"?this._collection=this._paletteGenerate(r.monochrome,this.model.columns):n==="e-presets21"?this._collection=this._paletteGenerate(r.misty,this.model.columns):n==="e-presets22"?this._collection=this._paletteGenerate(r.flatcolors,this.model.columns):n==="e-presets30"?this._collection=this._paletteGenerate(r.basic,this.model.columns):n==="e-presets31"?this._collection=this._paletteGenerate(r.candycrush,this.model.columns):n==="e-presets32"&&(this._collection=this._paletteGenerate(r.citrus,this.model.columns)),this._collection},_paletteGenerate:function(i,r){var u;for(this._PresetTable=t.buildTag("div.e-palette-color").attr({role:"presentation"}),this._tag=t.buildTag("div.e-row").attr({role:"row"}),u=0;u<i.length;u++)u&&u%r==0&&(this._tag=t.buildTag("div.e-row").attr({role:"row"})),this._td=t.buildTag("div.e-item").attr({role:"gridcell","aria-label":"#"+n.trim(i[u]),"data-value":"#"+n.trim(i[u]),style:"background-color:#"+n.trim(i[u])}),this._tag.append(this._td),this._PresetTable.append(this._tag);return this._PresetTable},_checkNameAttr:function(){this.element.attr("name")||this.element.attr({name:this.element[0].id})},_enabled:function(n){n?this.enable():(this.model.enabled=!0,this.disable())},_setDisplayInline:function(i){!this.popupContainer&&this.model.displayInline&&this._renderPopup();this.model.displayInline=i;i&&this.element.is("input")?(this.popupContainer.insertAfter(this.wrapper),this._footer.css({display:"none"}),this._setPopupPosition()):i?(this.element.append(this.popupContainer),this.popupContainer.find("button.e-applyButton").css({display:"none"}),this.popupContainer.find("button.e-cancelButton").css({display:"none"}),this._footer.css({display:"none"})):(this.popupContainer&&(this.popupContainer.css("display","none"),n("body").append(this.popupContainer),this._isOpen=!1),this.element.is("input")&&(this._bindIconClick(),this.wrapper.removeClass("e-focus"),this.popupContainer&&(this.popupContainer.find("button.e-applyButton").length==0&&this._buttonContainer(),this._footer.css({display:"block"}),this._off(n(document),"mousedown",this._onDocumentClick))),this._isFocused=this.isPopupOpen=!1);i&&(this.show(),this.element.is("input")&&this._off(n(this._buttonElement),"mousedown",this._iconClick),this._off(t.getScrollableParents(this.wrapper),"scroll",this.hide))},_bindIconClick:function(){var i=n._data(n(this._buttonElement)[0],"events");t.isNullOrUndefined(i)||t.isNullOrUndefined(i.mousedown)?this._on(this._buttonElement,"mousedown",this._iconClick):i.mousedown.length==0&&this._on(this._buttonElement,"mousedown",this._iconClick)},_unBindIconClick:function(){this._off(this._buttonElement,"mousedown",this._iconClick)},_setPopupPosition:function(){var t=this.element.is("input")?this.wrapper:this.element,i=this._getOffset(t),e,h=n(document).scrollTop()+n(window).height()-(i.top+n(t).outerHeight()),c=i.top-n(document).scrollTop(),f=this.popupContainer.outerHeight(),u=this.popupContainer.outerWidth(),r=i.left,o=t.outerHeight(),l=(o-t.height())/2,a=this._getZindexPartial(),s=3,v=(f<h||f>c?i.top+o+s:i.top-f-s)-l;e=n(document).scrollLeft()+n(window).width()-r;u>e&&u<r+t.outerWidth()&&(r-=this.popupContainer.outerWidth()-t.outerWidth());u+t.offset().left>n(window).width()&&(r=Math.abs(u-n(window).width()));this.popupContainer.css({left:r+"px",position:"absolute",top:v+"px","z-index":a})},_getOffset:function(n){return t.util.getOffset(n)},_getZindexPartial:function(){return t.util.getZindexPartial(this.element,this.popupContainer)},_setValue:function(n,r){(typeof n=="object"||typeof n=="number"||t.isNullOrUndefined(n.match("^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3}|[A-Fa-f0-9]{8})$")))&&(n=null);this.value(n);this._tempValue=n;this.rgb!=i&&this._selectedButton!=i&&(this._switch&&this._rgbValue(),n!=null&&this._HexToRGB(this._toHEX(n)),this._inputTagValue(this._selectedButton),this._updateUI(),this._updatePreviewColor());t.isNullOrUndefined(n)?this._setEmptyValue():this._renderModelValue(n);this._changeEvent(!1,r);this._selectEvent();this.element.is("input")&&this._updateValue()},_renderModelValue:function(n){var t;n&&typeof n=="string"&&(t=this._HexToHSV(n),this._oldValue=this.rgb,this.isPopupOpen?(this._switch&&(this._valueOperation(),this._colorSlider.option("value",parseInt(this._hsv.h))),this._inputTagValue(this._selectedButton),this._switch||(this._updateUI(),this.element.val(this.value())),this._hueGradient()):this.element.val(this.value()))},setValue:function(n){this._setValue(n)},enable:function(){if(this.model.enabled)return!1;if(this.wrapper&&this.wrapper.hasClass("e-disable")?(this.wrapper.removeClass("e-disable"),this.element.prop("disabled",!1),this.container.hasClass("e-disable")&&this.container.removeClass("e-disable"),this.popupContainer&&this.popupList.removeClass("e-disable"),this.dropdownbutton.removeClass("e-disable")):this.model.displayInline&&this.element.removeClass("e-disable"),this.popupContainer){var t=this._switch;this._switch=!0;this._cancelObj.enable();this._colorSlider.enable();this.model.enableOpacity?this._opacity.enable():this._opacity.disable();this._splitObj.enable();this._applyObj.enable();this._switchEvents();this._switch=t;n(this._inputTag).prop("readonly",!1)}this._wireEvents();this._buttonElement&&this._on(this._buttonElement,"mousedown",this._iconClick);this.model.enabled=!0},disable:function(){if(!this.model.enabled)return!1;if(this.wrapper&&!this.wrapper.hasClass("e-disable")?(this.wrapper.addClass("e-disable"),this.element.attr("disabled","disabled"),this.container.hasClass("e-disable")||this.container.addClass("e-disable"),this.popupContainer&&this.popupList.addClass("e-disable"),this.dropdownbutton.addClass("e-disable")):this.model.displayInline&&this.element.addClass("e-disable"),this.popupContainer){var t=this._switch;this._switch=!1;this._cancelObj.disable();this._colorSlider.disable();this._opacity.disable();this._splitObj.disable();this._applyObj.disable();this._unSwitchEvents();this._switch=t;n(this._inputTag).attr("readonly","readonly")}this._unWireEvents();this._unBindIconClick();this.isPopupOpen&&!this.model.displayInline&&this.hide();this.model.enabled=!1},getColor:function(){return this.rgb},getValue:function(){return this.value()},_alphaGradient:function(n){var i=t.browserInfo(),n=t.isNullOrUndefined(n)?"#000000":n;i.name=="mozilla"?this._alphaSlider.attr({style:"background:-moz-linear-gradient(center top,"+n+",#fff) repeat scroll 0 0 rgba(0, 0, 0, 0);"}):i.name=="msie"||i.name=="edge"?this._alphaSlider.attr({style:"background:-ms-linear-gradient(top,"+n+",#fff) repeat scroll 0 0 rgba(0, 0, 0, 0);"}):i.name=="opera"&&i.version<="11.61"?this._alphaSlider.attr({style:"background:-o-linear-gradient(top,"+n+",#fff) repeat scroll 0 0 rgba(0, 0, 0, 0);"}):(i.name=="chrome"||i.name=="safari"||i.name=="opera")&&this._alphaSlider.attr({style:"background:-webkit-linear-gradient(top,"+n+",#fff) repeat scroll 0 0 rgba(0, 0, 0, 0);"});i.name=="msie"&&(i.version=="8.0"||i.version=="9.0")&&this._alphaSlider.attr({style:"progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr="+n+", endColorstr=#ffffff)"});i.name=="msie"&&i.version=="8.0"&&this._handleTag.css({background:this._formRGB(this.rgb),filter:"alpha(opacity="+this.rgb.a*100+")"})},_hueGradient:function(){var n=this._hsv,t={h:this._hsv.h,s:100,v:100};this._hueSlider.children(".e-handle").css({background:this._formRGB(this.HSVToRGB(t))});this._hsv=n},_updateColor:function(){(this.model.displayInline||!this.model.showApplyCancel)&&(this.value(this._tempValue),this.element.is("input")&&this._updateValue(),this._trigger("select",{value:this.value(),RGBA:this.rgb}),this._previousColor=this._tempValue)},_changeEvent:function(n,t){this._change&&this._previousValue!==this._tempValue?(this.popupContainer&&this._alphaGradient(this._tempValue),this._previousValue=this._tempValue,this._trigger("change",{value:this._tempValue,RGBA:this.rgb,changeFrom:n?"slider":"picker",isInteraction:!t}),this._updateColor(n)):this.popupContainer&&this._alphaGradient(this._tempValue)},_selectEvent:function(){(this._previousColor!==this._tempValue||this._tempOpacity!==this._previousOpacity||this._isApplyBtnClick)&&(this.value(this._tempValue),this.element.is("input")&&this._updateValue(),this.element.val(this.value()),this._trigger("select",{value:this.value(),RGBA:this.rgb}),this._previousColor=this._tempValue,this._isApplyBtnClick=!1)},_changeHue:function(n){this._handleArea.css("visibility","visible");parseInt(this._hsv.h)!==parseInt(n.value)&&this._switch&&(this._hsv.h=Math.round(n.value),this._hueGradient(),this._hsvValue(),this._tempValue=this.RGBToHEX(this.rgb),this._changeEvent(!0))},_changeAlpha:function(n){this._handleArea.css("visibility","visible");this._switch&&(this.rgb.a=n.value/100,this._tempOpacity=parseInt(this.rgb.a*100),this._changeOpacity(n))},_changeOpacity:function(n){this.rgb.a=this._tempOpacity/100;(this._browser.name="msie"&&this._browser.version=="8.0")?(this._currentTag.css({"background-color":this._formRGB(this.rgb),filter:"alpha(opacity="+this.rgb.a*100+")"}),this._handleTag.css({background:this._formRGB(this.rgb),filter:"alpha(opacity="+this.rgb.a*100+")"})):(this._currentTag.css("background-color",this._formRGBA(this.rgb)),this._alphaSlider.children(".e-handle").css({background:this._formRGBA(this.rgb)}));this._inputTagValue(this._selectedButton);this.model.displayInline?(this._trigger("change",{value:this._tempValue,RGBA:this.rgb,changeFrom:"slider",isInteraction:t.isNullOrUndefined(n)?!1:n.isInteraction}),this._trigger("select",{value:this.value(),RGBA:this.rgb})):this._trigger("change",{value:this._tempValue,RGBA:this.rgb,changeFrom:"slider",isInteraction:t.isNullOrUndefined(n)?!1:n.isInteraction})},_updateValue:function(){this.value()?(this._browser.name="msie"&&this._browser.version=="8.0")?this.spanElement.css({"background-color":this._formRGB(this._HexToRGB(this.value())),filter:"alpha(opacity="+this.rgb.a*100+")"}):this.spanElement.css({"background-color":this._formRGBA(this._HexToRGB(this.value()))}):this.spanElement.removeAttr("style")},_bindClickOperation:function(){var t=this,i;t._on(n("#"+this._id+"_Presets").find("li.e-preset-row"),"mousedown",t._colorPresetsClick);i=n("#"+t._presetContainer.attr("id")).ejMenu("instance");i.model.close=function(){t._splitObj.contstatus=!1;t._off(n("#"+this._id+"_Presets").find("li.e-preset-row"),"mousedown",t._colorPresetsClick)}},_wirePopupEvents:function(){this._on(this._changeTag,"click",this._switchModel);this._on(this._groupTag,"click",this._groupButton);this._on(this._addTag,"click",this._addColor);this._on(this._codeEditor,"mouseenter",this._inputEvent);this._on(this._codeEditor,"mouseleave",this._inputEvent);this._on(this._inputTag,"blur",this._inputEvent);this._on(this._inputTag,"focus",this._inputEvent);this._on(this._inputTag,"keyup",this._inputEvent);this._on(this.popupContainer,"focus",this._targetFocus);this._on(this.popupContainer,"blur",this._targetBlur)},_wireEvents:function(){this.element.is("input")&&(this._on(this.wrapper,"blur",this._targetBlur),this._on(this.wrapper,"focus",this._targetFocus),this._on(this.wrapper,"keydown",this._popupShown),this._on(this.colorContainer,"click",this._containerClick))},_unWireEvents:function(){!this.model.displayInline&&this.element.is("input")&&(this._off(this.wrapper,"blur",this._targetBlur),this._off(this.wrapper,"focus",this._targetFocus),this._off(this.wrapper,"keydown",this._popupShown),this._off(this.colorContainer,"click",this._containerClick));this._off(this._changeTag,"click",this._switchModel);this._off(this._groupTag,"click",this._groupButton);this._off(this._addTag,"click",this._addColor);this._off(this._codeEditor,"mouseenter",this._inputEvent);this._off(this._codeEditor,"mouseleave",this._inputEvent);this._off(this._inputTag,"blur",this._inputEvent);this._off(this._inputTag,"focus",this._inputEvent);this._off(this._inputTag,"keyup",this._inputEvent);this._off(this.popupContainer,"focus",this._targetFocus);this._off(this.popupContainer,"blur",this._targetBlur)},_inputEvent:function(n){(n.type==="focus"&&n.target.className.indexOf("e-color-code")>-1&&this._codeEditor.addClass("e-focus"),n.type==="blur"&&n.target.className.indexOf("e-color-code")>-1&&this._codeEditor.removeClass("e-focus"),this._clearIcon)&&(n.type==="focus"&&this._off(this._codeEditor,"mouseleave",this._inputEvent),n.type==="blur"&&this._on(this._codeEditor,"mouseleave",this._inputEvent),n.type==="keyup"&&(this._inputTag.val()!==""?(this._clearIcon.show(),this._handleArea.css("visibility","visible")):(this._clearIcon.hide(),this._handleArea.css("visibility","hidden"))),n.type==="mouseleave"||n.type==="blur"||this._inputTag.val()==""?this._clearIcon.hide():this._clearIcon.show())},_clearColor:function(n){this._tempValue="";this._inputTag.val("");this._setEmptyValue();n.type=="click"&&(this._clearIcon.hide(),this._inputTag.focus())},_containerClick:function(){this.model.buttonMode=="split"&&this._trigger("select",{value:this.value(),RGBA:this.rgb})},_popupShown:function(n){if(n.keyCode==13)return this._showHidePopup(),this.isPopupOpen||this._buttonClick(n),!1},_recentColor:function(i){var f,e,r,u;if(this._divTag.find(".e-select").removeClass("e-select").addClass("e-block"),f=i.target.attributes.getNamedItem("data-value"),r=i.target.attributes.style,t.isNullOrUndefined(f)||t.isNullOrUndefined(r))return this._change=!1,!1;n(i.target.parentNode).addClass("e-select").removeClass("e-block");u=r.value.replace(/^(background-color:rgb|background-color:rgba)\(/,"").replace(/\)$/,"").replace(/\s/g,"").split(",");t.isNullOrUndefined(u[3])?(this._browser.name="msie"&&this._browser.version=="8.0")?(e=parseInt(r.nodeValue.replace(/^(FILTER: alpha)\(/,"").split("=")[1].split(")")[0]),this._opacity.option("value",e),this.rgb.a=e/100):(this._opacity.option("value",100),this.rgb.a=1):(this._opacity.option("value",parseInt((parseFloat(u[3])*100).toFixed(2))),this.rgb.a=parseFloat(parseFloat(u[3]).toFixed(2)));this._HexToHSV(f.value);this._inputTagValue(this._selectedButton);this._tempValue=this.RGBToHEX(this.rgb);this._valueOperation();this._colorSlider.option("value",parseInt(this._hsv.h));this._hueGradient();this._changeEvent(!1);(!this.model.displayInline||this.element.is("input"))&&this.wrapper.focus()},_handleClick:function(t){t.preventDefault();this._width=this._gradientArea.width();this._height=this._gradientArea.height();this.model.displayInline&&(this._isFocused=!0);this._handleArea.css("visibility","visible");this.mouseDownPos=this._handlePos;n(document).on("mousemove touchmove",n.proxy(this._handleMovement,this));n(document).on("mouseup touchend",n.proxy(this._handleUp,this))},_handleMove:function(n){this._handleArea.css("visibility","visible");this._handleMovement(n);this._focusWrapper(n)},_handleMovement:function(n){if(!this.model.enabled)return!1;var t=n.pageX,i=n.pageY;this.element.is("input")&&this.wrapper.focus();(n.type=="touchstart"||n.type=="touchmove")&&(t=n.originalEvent.changedTouches[0].pageX,i=n.originalEvent.changedTouches[0].pageY);this._hsv.v=parseInt(100*(this._gradientArea.height()-Math.max(0,Math.min(this._gradientArea.height(),i-this._gradientArea.offset().top)))/this._gradientArea.height(),10);this._hsv.s=parseInt(100*Math.max(0,Math.min(this._gradientArea.width(),t-this._gradientArea.offset().left))/this._gradientArea.width(),10);this._hsvValue();this._tempValue=this.RGBToHEX(this.rgb);this._change=!0;this._changeEvent(!1)},_handleUp:function(t){return n(document).off("mouseup touchend",this._handleUp),n(document).off("mousemove touchmove",this._handleMovement),this._focusWrapper(t),!1},_handlePosition:function(){this._handlePos=this._handleArea?{left:parseInt(parseInt(this._width)*this._hsv.s/100,10)+"px",top:parseInt(parseInt(this._height)*(100-this._hsv.v)/100,10)+"px"}:"";this._handleArea.css({left:this._handlePos.left,top:this._handlePos.top})},_addColor:function(){var i,t=this._divTag.find("> div");i=this._selectedButton.html()!="HSVA"?this._inputTag.val():this._formRGBA(this.HSVToRGB(this._hsv));i!==""&&this._change&&this.model.showRecentColors&&t.length<=12&&(n(n(t)[t.length-2]).remove(),this._generateLi());(!this.model.displayInline||this.element.is("input"))&&this.wrapper.focus()},_buttonClick:function(){this._change=!0;var n=this._inputTag.val(),t=this._divTag.find("div");if(this._opacity.option("value",this._tempOpacity),this._tempValue=this.RGBToHEX(this.rgb),this._updatePreviewColor(),this._inputTag.val()==="")if(this._tempValue="",this.model.showClearButton)this._setValue("");else return this._inputTag.addClass("e-error"),!1;this._isApplyBtnClick=!0;this._selectEvent();this.element.is("input")&&(this._updateValue(),this.wrapper.focus());this.model.displayInline||this.hide();this._tempOpacity!==this.opacityValue()&&this.opacityValue(this._tempOpacity)},_generateLi:function(){this._liTag=t.buildTag("div.e-colorblock e-block e-colorset").attr({"data-value":this.RGBToHEX(this.rgb),tabindex:"0"});var n=t.buildTag("div.e-color e-set").attr({"data-value":this.RGBToHEX(this.rgb),title:this.RGBToHEX(this.rgb)});(this._browser.name="msie"&&this._browser.version=="8.0")?n.css({"background-color":this._formRGB(this.rgb),filter:"alpha(opacity="+this.rgb.a*100+")"}):n.css({"background-color":this._formRGBA(this.rgb)});this._liTag.append(n);this._divTag.prepend(this._liTag)},_colorCodeValue:function(i){var e="",c=this._inputTag.val(),u,r,f,o,s,h;if(u=n.trim(c),u.length==5?this._inputTag.removeClass("e-error"):"",this._keyPressFlag=i.shiftKey&&i.keyCode>=35&&i.keyCode<=40||i.keyCode>=65&&i.keyCode<71||i.keyCode==51||i.ctrlKey&&(i.keyCode==88||i.keyCode==86)||i.keyCode==190?1:i.crtlKey||i.shiftKey||!(i.keyCode>=65&&i.keyCode<71||i.keyCode>=35&&i.keyCode<=40||i.keyCode>=96&&i.keyCode<=105||i.keyCode>=48&&i.keyCode<=57||i.keyCode==13||i.keyCode==8||i.keyCode==46||i.type==="blur")?i.key=="#"||i.key=="("||i.key==")"||i.key==","?1:0:1,(this.model.enableOpacity&&(i.keyCode==188||i.keyCode==71||i.keyCode==72||i.keyCode==82||i.keyCode==83||i.keyCode==86)||i.shiftKey&&(i.keyCode==57||i.keyCode==48))&&(this._keyPressFlag=1),this._keyPressFlag==1){if(this._inputTag.removeClass("e-error"),i.keyCode===13||i.type==="blur"){if(u===""&&(this.model.showClearButton?this._setEmptyValue():this._inputTag.addClass("e-error")),o=/^\#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/,r=u.match(o),t.isNullOrUndefined(r))if(s=/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/,r=u.match(s),t.isNullOrUndefined(r)){if(h=/^hsva?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/,r=u.match(h),t.isNullOrUndefined(r))return u!=""&&this._inputTag.addClass("e-error"),this._change=!1,!1;this._hsvColor(r)}else this._rgbaColor(r);else{if(r[1].length===3)for(f=0;f<r[1].length;f++)e+=r[1][f]+r[1][f];else r[1].length===6&&(e=r[1]);u=this.hexCodeToRGB("#"+e);this._inputTag.val("#"+e);this.rgb=this.HSVToRGB(this.RGBToHSV(u));this._tempValue=this.RGBToHEX(this.rgb);this._change=!0}this._change&&(this._valueOperation(),this._colorSlider.option("value",parseInt(this._hsv.h)),this._hueGradient(),this._changeEvent(!1),this.element.is("input")||this._selectEvent(),this._inputTag.val()!==""&&this._inputTag.removeClass("e-error"))}}else i.keyCode!=9&&i.preventDefault(),i.shiftKey||i.ctrlKey||i.keyCode===27||i.keyCode==20||this._inputTag.addClass("e-error")},_setEmptyValue:function(){this.popupContainer&&(this._handleArea.css("visibility","hidden"),this._currentTag.css({"background-color":""}),this._removeClass(),this._inputTag.val(null));this._tempValue=null;this._previousValue!==this._tempValue&&(this._trigger("change",{value:null,RGBA:null}),this._previousValue=this._tempValue)},_rgbaColor:function(n){var i={};if(n[0].split("(")[0]!="rgba"||t.isNullOrUndefined(n[4]))if(n[0].split("(")[0]=="rgb"&&t.isNullOrUndefined(n[4]))i.r=n[1],i.g=n[2],i.b=n[3],i.a=this.rgb.a,this._inputTag.val("rgba("+i.r+","+i.g+","+i.b+","+i.a+")");else return this._inputTag.addClass("e-error"),this._change=!1,!1;else i.r=n[1],i.g=n[2],i.b=n[3],i.a=n[4];this.rgb.a=parseFloat(i.a);this._tempOpacity=this.rgb.a*100;this._opacity.option("value",this._tempOpacity);this._previousOpacity=this.opacityValue();this.opacityValue(this._tempOpacity);this._tempValue=this.RGBToHEX(i);this._HexToHSV(this._tempValue);this._inputTag.removeClass("e-error");this._change=!0},_hsvColor:function(n){var i={};if(t.isNullOrUndefined(n[4]))return this._inputTag.addClass("e-error"),this._change=!1,!1;i.h=n[1];i.s=n[2];i.v=n[3];i.a=n[4];this.rgb.a=parseFloat(i.a);this._tempOpacity=this.rgb.a*100;this._opacity.option("value",this._tempOpacity);this.opacityValue(this._tempOpacity);this.rgb=this.HSVToRGB(i);this._tempValue=this.RGBToHEX(this.rgb);this._inputTag.removeClass("e-error");this._change=!0},_iconClick:function(n){n.preventDefault();this._showHidePopup();this.wrapper.focus()},_showHidePopup:function(){if(this.model.displayInline)return!1;this.isPopupOpen?(this.hide(),this.wrapper.focus()):this.show()},hide:function(){var i=this;if(!this.isPopupOpen||this.model.displayInline)return!1;this.isPopupOpen=this._dataBind=!1;this.element.is("input")&&(this.wrapper.focus(),this.wrapper.removeClass("e-active"));this._width=this._gradientArea.width();this._height=this._gradientArea.height();this.popupContainer.slideUp(200,function(){i.model&&(i._tempOpacity=i.opacityValue(),i.rgb.a=i._tempOpacity/100,i._tempValue=i.value(),i._renderModelValue(i.value()),i._opacity.option("value",parseInt(i.opacityValue())),i.model.displayInline||i._off(n(document),"mousedown",i._onDocumentClick),i._trigger("close"))});this.model.displayInline||this._off(n(document),"mousedown",this._onDocumentClick);this._off(this._inputTag,"keydown",this._colorCodeValue);this._off(this._inputTag,"blur",this._colorCodeValue);this._modelType=="palette"?this._off(n(document),"keydown",this._keyDown):this._off(n(document),"keydown",this._onKeyDown);this._off(t.getScrollableParents(this.wrapper),"scroll",this.hide);n(window).off("resize",n.proxy(this._OnWindowResize,this))},_hidePopup:function(){this.model.displayInline||this.hide()},show:function(){if(this.popupContainer||this._renderPopup(),this.element.is("input")&&(this.wrapper.focus(),this.wrapper.addClass("e-active")),this.isPopupOpen||!this.model.enabled)return!1;this.isPopupOpen=!0;this.model.modelType=="palette"&&this._cellSelect();!this.model.displayInline&&(this.value()===""||t.isNullOrUndefined(this.value()))?this._setEmptyValue():this._handleArea.css("visibility","visible");this._previousColor=this._previousValue=this.value();this.popupContainer.children().find(".e-focus").removeClass("e-focus");this.model.displayInline||this._setPopupPosition();var i=this;this.popupContainer.slideDown(200,function(){i.isFocused=!0;i._on(n(document),"mousedown",i._onDocumentClick);i._trigger("open")});this._dataBind||(this._modelType=="palette"?this._on(n(document),"keydown",this._keyDown):this._on(n(document),"keydown",this._onKeyDown));this._on(this._inputTag,"keydown",this._colorCodeValue);this._on(this._inputTag,"blur",this._colorCodeValue);this._dataBind=!0;n(window).on("resize",n.proxy(this._OnWindowResize,this));this.model.displayInline||this._on(t.getScrollableParents(this.wrapper),"scroll",this.hide);this._prevSize!==n(window).width()&&this.refresh()},_showBindEvents:function(){this._modelType=="palette"?this._on(n(document),"keydown",this._keyDown):this._on(n(document),"keydown",this._onKeyDown)},_hideUnBindEvents:function(){this._modelType=="palette"?this._off(n(document),"keydown",this._onKeyDown):this._off(n(document),"keydown",this._keyDown)},_switchEvents:function(){this._switch?(this._on(this._gradientArea,"mousedown touchstart",this._handleMove),this._on(this._handleArea,"mousedown touchstart",this._handleClick),this._on(this._gradientArea,"mousedown touchstart",this._handleClick)):this._on(this._collection,"mousedown",this._onSelect)},_unSwitchEvents:function(){this._switch?this._off(this._collection,"mousedown",this._onSelect):(this._off(this._gradientArea,"mousedown touchstart",this._handleMove),this._off(this._handleArea,"mousedown touchstart",this._handleClick),this._off(this._gradientArea,"mousedown touchstart",this._handleClick))},_groupButton:function(t){if(n(t.target).hasClass("e-disable"))return!1;if(n(t.target).hasClass("e-button")){var i=this._groupTag.find(".e-btn.e-select");this._inputTag.val()!==""?this._inputTagValue(n(t.target)):this._selectedButton=n(t.target);this._selectedButton.html()!=="HEX"?this._inputTag.attr("maxLength","22"):this._inputTag.attr("maxLength","7");this._groupTag.find(".e-click").removeClass("e-click");n(t.target).addClass("e-click");this._inputTag.removeClass("e-error")}},_inputTagValue:function(n){n.html()=="RGBA"?this._inputTag.val(this._formRGBA(this.rgb)):n.html()=="HEX"?this._inputTag.val(this.RGBToHEX(this.rgb)):n.html()=="HSVA"&&this._modelType!="palette"&&this._inputTag.val("hsva("+Math.round(this._hsv.h)+","+Math.round(this._hsv.s)+","+Math.round(this._hsv.v)+","+this.rgb.a+")");this._selectedButton=n},_bindRecentEvent:function(){this._on(this._divTag,"click",this._recentColor)},_unBindRecentEvent:function(){this._off(this._divTag,"click",this._recentColor)},_handlePlacement:function(n,t,i){this._handleArea.css("visibility","visible");var r=this._hsv;r[n]+=t*(i?1:3);r[n]<0&&(r[n]=0);n==="s"?this._hsv.s=r[n]:this._hsv.v=r[n];this._hsvValue();this._tempValue=this.RGBToHEX(this.rgb);this._changeEvent(!1)},_onKeyDown:function(t){var i=t.keyCode,r;if(this.model.enabled&&(t.shiftKey&&i==9&&n(this._hueSlider).find(".e-handle").hasClass("e-focus")&&this._focusWrapper(t),this._isFocused||i!=9||i===27)&&(this._change=!0,!t.altKey&&!t.shiftKey))switch(i){case 39:this.element.is("input")&&n(t.target).is(this.wrapper)&&(t.preventDefault(),this._handlePlacement("s",1,t.ctrlKey));break;case 38:this.element.is("input")&&n(t.target).is(this.wrapper)&&(t.preventDefault(),this._handlePlacement("v",1,t.ctrlKey));break;case 37:this.element.is("input")&&n(t.target).is(this.wrapper)&&(t.preventDefault(),this._handlePlacement("s",-1,t.ctrlKey));break;case 40:this.element.is("input")&&n(t.target).is(this.wrapper)&&(t.preventDefault(),this._handlePlacement("v",-1,t.ctrlKey));break;case 13:t.preventDefault();n(t.target).hasClass("e-switcher")?(this._switchModel(),n(t.target).focus()):n(t.target).hasClass("e-applyButton")&&this._buttonClick(t);break;case 27:t.preventDefault();this.model.displayInline||this.hide();this._tempValue=this.value();break;case 9:r=document.activeElement;n(r).is(this.wrapper)&&this._focusPopup(t)}},_focusPopup:function(t){n(this._hueSlider).find(".e-handle").focus();t.preventDefault()},_focusWrapper:function(t){this.element.is("input")&&n(this.wrapper).focus();t.preventDefault()},_onDocumentClick:function(t){n(t.target).is(this.popupContainer)||n(t.target).parents(".e-colorpicker").is(this.popupContainer)||n(t.target).is(this.wrapper)||n(t.target).parents(".e-colorpickerwidget").is(this.wrapper)||n(t.target).parents(".e-presetWrapper").is("#"+this._id+"_Presets")||(this.model.displayInline||(this.hide(),this.element.is("input")&&this.wrapper.removeClass("e-focus")),this._isFocused=!1)},_OnWindowResize:function(){this.element.is("input")&&this._setPopupPosition();this.refresh()},refresh:function(){var t=n(this._PresetTable.find(".e-item")[1]),i=10,u,r;this.isPopupOpen?this._modelType=="picker"&&this.PaletteWrapper.css({display:"block",visibility:"hidden"}):(this.popupContainer.css({display:"block",visibility:"hidden"}),this._modelType=="palette"&&this.PaletteWrapper.css({display:"block",visibility:"hidden"}));(parseFloat(this._tag.outerHeight())>t.outerHeight(!0)||n(this._tag).outerWidth()-n(t).outerWidth(!0)*i>t.outerWidth()||n(this._tag).outerWidth()==0)&&(u=parseFloat(n(this._tag).outerWidth())-36,r=u/i-(t.outerWidth()-t.width()),this._PresetTable.find(".e-item").css("width",r),t.outerWidth(!0)*i>this._tag.outerWidth()&&this._PresetTable.find(".e-item").css("width",r-1));this.isPopupOpen?this._modelType=="picker"&&this.PaletteWrapper.css({display:"none",visibility:"visible"}):(this.popupContainer.css({display:"none",visibility:"visible"}),this._modelType=="palette"&&this.PaletteWrapper.css({visibility:"visible"}));this._prevSize=n(window).width();this._width=this._gradientArea.width();this._height=this._gradientArea.height()},_range:function(n,t){return t===""?0:t>n?n:t},_hsvValue:function(){this._change=!0;this._hsv.v=this._hsv.v>=100?100:this._hsv.v;this._hsv.s=this._hsv.s>=100?100:this._hsv.s;this.hsv=this._hsv;this.rgb=this.HSVToRGB(this.hsv);this._valueOperation();this._inputTagValue(this._selectedButton)},_formRGB:function(n){if(!t.isNullOrUndefined(n))return"rgb("+n.r+","+n.g+","+n.b+")"},_formRGBA:function(n){if(!t.isNullOrUndefined(n))return"rgba("+n.r+","+n.g+","+n.b+","+n.a+")"},_rgbValue:function(){var n;n=this._HexToRGB(this._tempValue);t.isNullOrUndefined(n)||(this.rgb=n,this._change=!0,this.HSVToRGB(this.RGBToHSV(this.rgb)),this._colorSlider.option("value",parseInt(this._hsv.h)),this._opacity.option("value",this.rgb.a*100));this._valueOperation();this._inputTagValue(this._selectedButton)},_valueOperation:function(){this._handlePosition();this._alphaGradient(this._tempValue);this._inputTag.removeClass("e-error");this._updateUI()},_HexToHSV:function(n){return this.HSVToRGB(this.RGBToHSV(this._HexToRGB(n)))},_HexToRGB:function(n){var r,n,i,u;if(!t.isNullOrUndefined(n))return(r="^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3}|[A-Fa-f0-9]{8})$",i=n.match(r),t.isNullOrUndefined(i))?(this._change=!1,!1):(u=n.length==9?Number((parseInt(n.slice(-2),16)/255).toFixed(2)):parseFloat(this._tempOpacity)/100,n=n.slice(1,7),i[1].length==3&&(n="#"+i[1][0]+i[1][0]+i[1][1]+i[1][1]+i[1][2]+i[1][2]),this._change=!0,n=parseInt(n.indexOf("#")>-1?n.substring(1):n,16),this.rgb={r:n>>16,g:(n&65280)>>8,b:n&255,a:u})},RGBToHSV:function(n){var t={h:0,s:0,v:0},u=Math.min(n.r,n.g,n.b),i=Math.max(n.r,n.g,n.b),r=i-u;return(t.v=i,t.v*=100/255,r===0)?(this._hsv=t,t):(t.s=i!=0?255*r/i:0,t.h=t.s!=0?n.r==i?(n.g-n.b)/r:n.g==i?2+(n.b-n.r)/r:4+(n.r-n.g)/r:-1,t.h*=60,t.h<0&&(t.h+=360),t.s*=100/255,t.a=n.a,this._hsv=t,t)},HSVToRGB:function(n){var t={},u=parseFloat(n.h),o=parseFloat(n.s*255/100),e=parseFloat(n.v*255/100),s;if(o==0)t.r=t.g=t.b=e;else{var i=e,r=(255-o)*e/255,f=(i-r)*(u%60)/60;u==360&&(u=0);u<60?(t.r=i,t.b=r,t.g=r+f):u<120?(t.g=i,t.b=r,t.r=i-f):u<180?(t.g=i,t.r=r,t.b=r+f):u<240?(t.b=i,t.r=r,t.g=i-f):u<300?(t.b=i,t.g=r,t.r=r+f):u<360?(t.r=i,t.g=r,t.b=i-f):(t.r=0,t.g=0,t.b=0)}return this._hsv=n,s=n.a,{r:Math.round(t.r),g:Math.round(t.g),b:Math.round(t.b),a:s}},_HSVToHex:function(n){return this.RGBToHEX(this.HSVToRGB(n))},_toHEX:function(n){return n.indexOf("#")!==-1?n:(n=n.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/),"#"+this._hex(n[1])+this._hex(n[2])+this._hex(n[3]))},RGBToHEX:function(n){if(!t.isNullOrUndefined(n))return"#"+this._hex(n.r)+this._hex(n.g)+this._hex(n.b)},_hex:function(n){return("0"+parseInt(n).toString(16)).slice(-2)},_colorValue:function(n){return this._color=n.indexOf("#")!=-1?this.hexCodeToRGB(n):"","rgb("+this._color.r+", "+this._color.g+", "+this._color.b+")"},hexCodeToRGB:function(n){var t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(n);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16),a:this.rgb.a}:null},_updatePreviewColor:function(){(this._browser.name="msie"&&this._browser.version=="8.0")?this._previousTag.css({"background-color":this._formRGB(this.rgb),filter:"alpha(opacity="+this.rgb.a*100+")"}):this._tempValue==null?this._previousTag.css({"background-color":""}):this._previousTag.css({"background-color":this._formRGBA(this.rgb)});this._oldValue=this.rgb},_updateUI:function(){var t=this._hsv,n;this._switch?(n={h:this._hsv.h,s:100,v:100},this._gradientArea.css({"background-color":this._colorValue(this._HSVToHex(n))})):this._cellSelect();(this._browser.name="msie"&&this._browser.version=="8.0")?(this._currentTag.css({"background-color":this._formRGB(this.rgb),filter:"alpha(opacity="+this.rgb.a*100+")"}),this._previousTag.css({"background-color":this._formRGB(this._oldValue),filter:"alpha(opacity="+this.rgb.a*100+")"})):(this._currentTag.css({"background-color":this._formRGBA(this.rgb)}),this._previousTag.css({"background-color":this._formRGBA(this._oldValue)}),this._alphaSlider.children(".e-handle").css({background:this._formRGBA(this.rgb)}));this._hsv=t},_targetFocus:function(n){n.preventDefault();this._isFocused||(this._isFocused=!0,this.element.is("input")&&this.wrapper.addClass("e-focus"))},_targetBlur:function(){this._isFocused=!1;!this.isPopupOpen&&this.element.is("input")&&this.wrapper.removeClass("e-focus")},_switchModel:function(){this._tempValue=this.RGBToHEX(this.rgb);this.refresh();var n=this;this._off(this._changeTag,"click",this._switchModel);this._modelType=="palette"?(this._modelType="picker",this._switcher.removeClass("e-pickerModel").addClass("e-paletteModel"),this.PaletteWrapper.fadeOut(300,function(){n._presetTag.parents(".e-split.e-widget").addClass("e-hide");n._gradient.fadeIn(300);n._on(n._changeTag,"click",n._switchModel)}),this._switch=!0,this._rgbValue(),this._hueGradient(),this._hsva.ejButton("enable")):(this.PaletteWrapper.remove(),this._modelType="palette",this.PaletteWrapper=this._layoutType(this.model.palette),this._gradient.addClass("e-hide"),this._paletteType(),this._switcher.removeClass("e-paletteModel").addClass("e-pickerModel"),this._gradient.fadeOut(300,function(){n._presetTag.parents(".e-split.e-widget").removeClass("e-hide");t.isNullOrUndefined(n.PaletteWrapper)&&(n.PaletteWrapper=n._layoutType(n.model.palette),n._splitObj.option("prefixIcon","e-color-image e-"+n.model.presetType),n.popupList.prepend(n.PaletteWrapper));n.PaletteWrapper.fadeIn(300);n._on(n._changeTag,"click",n._switchModel)}),this.value()!==""&&this._cellSelect(),this._disableHSVButton());this.model.palette==="custompalette"&&this._presetTag.parents(".e-split.e-widget").addClass("e-hide");(!this.model.displayInline||this.element.is(":input"))&&this.wrapper.focus();this._switchEvents();this._unSwitchEvents();this.isPopupOpen&&(this._hideUnBindEvents(),this._showBindEvents())},_disableHSVButton:function(){n(this._groupTag.find(".e-click")).hasClass("e-hsvButton")&&(this._inputTagValue(this._rgb),this._rgb.addClass("e-click"),this._hsva.removeClass("e-click"));this._hsva.ejButton("disable")},_cellSelect:function(){var i,r,t=this;this._removeClass();this._collection.find(".e-item").each(function(){i=n(this).css("background-color");(t._browser.name="msie"&&t._browser.version=="8.0")?i&&i.replace(/ /g,"")===t.RGBToHEX(t.rgb)&&(r=this,n(r).addClass("e-filter")):i&&i.replace(/ /g,"")===t._formRGB(t.rgb)&&(r=this)});n(r).addClass("e-state-selected").attr("aria-selected",!0)},_removeClass:function(){this._collection.find(".e-item").removeClass("e-state-selected").removeClass("e-filter").removeAttr("aria-selected")},_position:function(n,t,i){n=Array.prototype.slice.call(n);var u=n.length,r=n.indexOf(t);return r<0?i<0?n[u-1]:n[0]:(r+=i,n[r<0?r+=u:r%=u])},_onSelect:function(n){if(!this.model.enabled)return!1;this._isFocused=!0;this._handleArea.css("visibility","visible");n.target.style.backgroundColor!=""&&(this._collection.find(".e-item").removeClass("e-state-selected").removeAttr("aria-selected"),this._HexToRGB(this._toHEX(n.target.style.backgroundColor)),this._updateUI(),this._inputTagValue(this._selectedButton),(!this.model.displayInline||this.element.is("input"))&&this.wrapper.focus(),this._tempValue=this.RGBToHEX(this.rgb),this._changeEvent(!1));n.preventDefault()},_keyDown:function(t){var o;if(this._isFocused){if(this._change=!0,!this.model.enabled)return!1;var i="",r=t.keyCode,u=this._collection.find(".e-item"),f=u.filter(".e-state-selected").get(0),e=this.model.columns;t.altKey||r!=37&&r!=38&&r!=39&&r!=40||t.target.className==="e-color-code"||this._removeClass();switch(!t.altKey&&r){case 40:t.target.className!=="e-color-code"&&(t.preventDefault(),i=this._position(u,f,e));break;case 37:t.target.className!=="e-color-code"&&(t.preventDefault(),i=this._position(u,f,-1));break;case 38:t.target.className!=="e-color-code"&&(t.preventDefault(),i=this._position(u,f,-e));break;case 39:t.target.className!=="e-color-code"&&(t.preventDefault(),i=this._position(u,f,1));break;case 13:this._collection.find(".e-item").removeClass("e-state-selected").removeAttr("aria-selected");n(t.target).hasClass("e-switcher")?(this._switchModel(),n(t.target).focus()):n(t.target).hasClass("e-applyButton")&&(this._buttonClick(t),this._updateUI(),this.model.displayInline||this.hide(),this.element.is("input")&&this.wrapper.focus());break;case 27:this.model.displayInline||this.hide();n(this._presetContainer).hide();this.element.is("input")&&this.wrapper.focus();break;case 9:o=document.activeElement;n(o).is(this.wrapper)&&this._focusPalettePopup(t,!0)}i&&(n(i).addClass("e-state-selected").attr("aria-selected",!0),this._currentTag.css({"background-color":this._formRGB(this._HexToRGB(n(i).attr("data-value")))}),this._inputTagValue(this._selectedButton),this._tempValue=this.RGBToHEX(this.rgb),this._changeEvent(!1))}else t.keyCode==27&&(this.hide(),n(this._presetContainer).hide())},_focusPalettePopup:function(t){n(this.popupContainer).focus();t.preventDefault()}});t.ColorPicker.Locale=t.ColorPicker.Locale||{};t.ColorPicker.Locale["default"]=t.ColorPicker.Locale["en-US"]={buttonText:{apply:"Apply",cancel:"Cancel",swatches:"Swatches"},tooltipText:{switcher:"Switcher",addButton:"Add Color",basic:"Basic",monoChrome:"Mono Chrome",flatColors:"Flat Colors",seaWolf:"Sea Wolf",webColors:"Web Colors",sandy:"Sandy",pinkShades:"Pink Shades",misty:"Misty",citrus:"Citrus",vintage:"Vintage",moonLight:"Moon Light",candyCrush:"Candy Crush",currentColor:"Current Color",selectedColor:"Selected Color"}};t.ColorPicker.Palette={BasicPalette:"basicpalette",CustomPalette:"custompalette"};t.ColorPicker.ModelType={Palette:"palette",Picker:"picker"};t.ColorPicker.ButtonMode={Default:"dropdown",Split:"split"};t.ColorPicker.PresetType={Basic:"basic",MonoChrome:"monochrome",FlatColors:"flatcolors",SeaWolf:"seawolf",WebColors:"webcolors",Sandy:"sandy",PinkShades:"pinkshades",Misty:"misty",Citrus:"citrus",Vintage:"vintage",MoonLight:"moonlight",CandyCrush:"candycrush"};var r={basic:["ffffff","facbcb","fccb98","faf39a","fbf8cd","a6d38b","aadee8","d1ecf2","cdcae5","eecde1","cccbcb","f16667","f69668","f8ee6b","f7ec37","89c987","75cddd","8bd3e1","7f7fcc","9494c8","b3b2b3","ec2024","f7971d","ffcb67","f5ea14","74bf44","69c8c9","46c7f4","6666ad","b76cab","676767","971b1e","ca6828","ca9732","979937","0d9948","339898","4857a6","62449a","973794","000000","2f1110","973620","663433","343416","183319","023334","22205f","3b2f8d","310e31"],monochrome:["ffffff","e3e3e3","c6c6c6","aaaaaa","8e8e8e","717171","555555","393939","1c1c1c","000000","f9e6e7","f4d0d2","efbabc","e9a4a7","e48e92","df787c","da6267","d44c52","cf363c","ca2027","fff4ca","ffeb9e","fff0b4","ffefb1","ffe788","ffe272","ffd947","ffd531","ffd01b","ffcc05","e4f4eb","ccead9","b4e0c7","9cd6b5","84cca3","6dc190","55b77e","3dad6c","25a35a","0d9948","e8f4f4","d6e3eb","c4d1e3","b3c0da","a1aed1","8f9dc9","7d8bc0","6c7ab7","5a68af","4857a6"],flatcolors:["7477b8","488bca","18b1d4","1db369","78c162","acc063","ffe84e","f6b757","f79853","ed6346","E87F3D","E4C45D","B7A575","999999","67809F","002228","00A578","F9A41F","F3770B","D7401B","FFCB36","82CC2C","36B595","6370AD","D4264E","004D8E","22A04B","F3A414","C77405","F3420B","1ABC9C","3498DB","9B59B6","E67E22","E74C3C","3A738A","EBD9A7","89AD78","FF766D","C76160","BF3542","CDC5BA","EBE3D6","3C3C3C","2E2E2E","77A7FB","E57368","FBCB43","34B67A","FFFFFF"],seawolf:["0EEAFF","15A9FA","1B76FF","1C3FFD","2C1DFF","0B3C4C","0E5066","13647F","127899","1A8BB2","74B8E8","659EBB","3C9FFF","26466F","2472FF","0069A4","009BF2","004165","49A0B4","274C5F","000000","7A5848","E0A088","F9DEC6","3A2A22","DC3522","D9CB9E","374140","2A2C2B","1E1E20","CB3937","FE6B2C","654E44","6DD16F","70FE2C","275673","4681A6","FDDEC9","F22816","400101","071C2F","388494","E6A934","F3DB5F","534329","206956","47683B","E1BFA6","BF7950","903932"],webcolors:["0066aa","00bbdd","338800","77bb00","ffcc99","990c0c","0303c9","336699","669933","cccccc","EEEEEE","E7C36F","F7B230","E35B20","000033","7D7A74","BD524A","FCB200","8CFCC2","2ACD6B","666666","666553","FFFEEC","B2B2A4","AAA4B2","9CA5E3","5A668C","BBA469","CFC295","FFFFFF","DBBF56","2E94B3","808080","E96656","14A168","DE185B","D8806F","DBE186","D8CC63","DCC527","4E6C89","E2BDAD","EC6053","81BBAD","DFCDA5","453394","66398A","313E7D","336694","788E91"],sandy:["c0a986","ecd9c3","dfc79b","f6d58e","ecdaad","fff3e0","7f6b4a","ffd694","7f7a70","ccac76","E6E2AF","A79A71","EFECCA","806F4C","2F2F2E","997F1A","CCB65F","FFD291","6B674A","635F3A","7F693A","FFEBC1","FFD275","7F7561","CCA85E","D29854","4A4034","C9AD8D","4A351D","968169","E6E39F","9A9757","FFFDC9","94909A","E2E0E6","960010","EB1517","CD7C29","9A571C","1F7A94","7F6826","7F724C","FFE499","FFD04C","CCA63D","FFA669","92FFB6","FFF352","E8C269","D7E8CB"],pinkshades:["F6B1C3","F0788C","DE264C","BC0D35","A20D1E","E12256","BB1C48","7B132F","3B0917","FA2660","FFB7B5","9A423F","FF6D68","BB5855","CC5753","E88161","D66C60","C2646E","996072","705262","FFA1BD","FF8FB7","FF82AE","E9719B","CC6882","F250C7","BF1774","BF2696","AC60AA","BB90C5","BF1553","F20775","F2F2F2","e5566d","f2afc1","f43fa5","fc8c99","FF6887","7F3443","CC536C","D06AA9","E65F41","650017","BC1620","FA427E","3B1132","84476E","B83D65","E6E0E8","FF6EE8"],misty:["5C7A84","3D5372","7C9196","50748A","ADBFBF","010735","052159","194073","376B8C","FFFFFF","985999","C811CC","892EFF","FF6852","DBA211","0A0D0C","85A67C","46593E","BBD9AD","202616","BF8E63","734327","A66C4B","593A2F","BFBFBF","8DB0B6","1B778A","F46C1B","881801","192129","81808C","ABAABF","0C0E09","6A7366","37402F","5D6663","84867B","A4A66A","BABBB1","20211C","6B9695","646E8C","6B8196","61787F","648C80","8E9FBA","89A8C8","799ED1","7FAEE7","849EBD"],citrus:["FAEA41","E7F03E","E3C647","FAC541","F0AB3E","CCCA1F","FFF300","FFCB0D","FF9500","804A00","6A692A","FFFCA0","FFFFFF","CF664E","EFAC66","EFF299","F2DC6D","F29727","F2600C","592202","214001","4F7302","1A2601","BCD97E","C0D904","AAFEFF","359D6D","E5FF45","65FCCF","ABDC4B","42B200","C6FF00","F2E304","FFB200","FF8600","52EC04","04E206","94D507","ECE404","E2C904","DA321C","FF7913","FBD028","C0D725","9FC131","547312","ADBF26","DEB329","F1DB47","E08214"],vintage:["684132","fdbe30","eaac21","87783c","3e4028","ffc706","cd5648","5bafa9","828282","363636","424862","fb9a63","bfc4d5","f6fbf4","febc98","657050","FCF082","D8D98B","A2AB80","4D584A","5ADED4","4DAAAB","26596A","163342","6C98A1","010A26","28403B","557359","AEBF8A","C7D9AD","AFFCCB","CB4243","D2997E","36857E","4AC6BB","28394B","191313","AF0A18","DC373D","122438","43734A","A6A26D","D9B448","BF8C2C","734002","26010F","866F53","ACBD91","7BAB87","546859"],moonlight:["241D37","2A233D","322B45","362F49","D4BA73","261225","592040","8C3063","A64985","73345D","A3C8FF","85B6FF","000040","213190","050859","FFFFFF","6AAED9","4184BF","224573","2e4154","bcad7e","955351","c36a57","9a8556","7e6029","dbd78e","beae3b","c3a04c","58504d","967644","CFC496","B3B391","889486","61797B","366577","123340","436E73","7B8C61","D7D996","F0EBB4","341F36","D9B5E0","9889AB","4D4E66","1B2129","5CBBE3","FCF1BC","5C8182","383A47","B4F257"],candycrush:["0779f4","30da00","fb8006","f9d802","a71df7","f70200","fd49ae","682e07","9b2424","5e7693","F9AB3B","EF5627","FF0000","00A398","803C2C","DE5711","FFF026","FF0048","14A0CC","00B229","FFFFBE","F7CD99","FF77A1","9886E8","97CACB","EAEDE5","FFD127","FF870C","EC4610","9A1900","993460","CC1464","C300FF","FFFFBC","CCB914","FFFEE2","B24C5F","FF274F","0A94CC","679DB2","C2FFE6","16B271","5FFFBC","B2442F","FFA190","E89359","FFFB75","F36EFF","5999E8","73EB86"]}})(jQuery,Syncfusion)});
