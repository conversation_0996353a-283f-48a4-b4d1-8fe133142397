﻿using Admin.Data.Model;

namespace Admin.Business
{
    public interface IAppointmentCategoriesBusiness
    {
        //Task<Role?> GetRole(Int64 roleId);
        Task<List<AppointmentCategory>> GetAllAppointmentCategories(Int64 tenantId);
        //Task CreateOrUpdateRole(Role role);
        //Task DeleteRole(Int64 roleId);
        //Task<Role?> CheckRoleExists(Int64 tenantId, Int64 roleId, string name);
        Task CreateAppointmentCategoriesForTenant(Int64 tenantId);
    }
}