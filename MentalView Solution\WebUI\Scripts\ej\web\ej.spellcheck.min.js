/*!
*  filename: ej.spellcheck.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min","./../common/ej.globalize.min","./../common/ej.scroller.min","./../common/ej.draggable.min","./ej.dialog.min","./ej.button.min","./ej.listbox.min","./ej.menu.min"],n):n()})(function(){"use strict";var n=this&&this.__extends||function(n,t){function r(){this.constructor=n}for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)};(function(t){var i=function(i){function r(){i.apply(this,arguments);this.rootCSS="e-spellcheck";this.validTags=["div","span","textarea"];this.PluginName="ejSpellCheck";this._id="null";this.defaults={locale:"en-US",misspellWordCss:"e-errorword",ignoreSettings:{ignoreAlphaNumericWords:!0,ignoreHtmlTags:!0,ignoreEmailAddress:!0,ignoreMixedCaseWords:!0,ignoreUpperCase:!0,ignoreUrl:!0,ignoreFileNames:!0},dictionarySettings:{dictionaryUrl:"",customDictionaryUrl:""},maxSuggestionCount:6,ajaxDataType:"jsonp",ajaxRequestType:"GET",ignoreWords:[],contextMenuSettings:{enable:!0,menuItems:[{id:"IgnoreAll",text:"Ignore All"},{id:"AddToDictionary",text:"Add to Dictionary"}]},isResponsive:!0,enableValidateOnType:!1,controlsToValidate:null,enableAsync:!0,actionSuccess:null,actionBegin:null,actionFailure:null,start:null,complete:null,contextOpen:null,contextClick:null,dialogBeforeOpen:null,dialogOpen:null,dialogClose:null,validating:null,targetUpdating:null};this.dataTypes={locale:"string",misspellWordCss:"string",ignoreSettings:{ignoreAlphaNumericWords:"boolean",ignoreHtmlTags:"boolean",ignoreEmailAddress:"boolean",ignoreMixedCaseWords:"boolean",ignoreUpperCase:"boolean",ignoreUrl:"boolean",ignoreFileNames:"boolean"},dictionarySettings:{dictionaryUrl:"string",customDictionaryUrl:"string",customDictionaryPath:"string"},maxSuggestionCount:"number",ajaxDataType:"string",ajaxRequestType:"string",ignoreWords:"array",contextMenuSettings:{enable:"boolean",menuItems:"array"}};this._tags=[{tag:"ignoreSettings",attr:[]},{tag:"dictionarySettings",attr:[]},{tag:"contextMenuSettings.menuItems",attr:["id","text"]},];this._localizedLabels=null;this._statusFlag=!0;this._words=[];this._inputWords=[];this._controlIds=[];this._control=[];this._targetStatus=!0;this._statusMultiTarget=!1;this._changeAllWords=[];this._subElements=[];this._iframeStatus=!1;this._elementStatus=!0;this._suggestedWordCollection=[];this._ignoreStatus=!0;this._suggestedWords=[];this.webMethod=!1;this.model=this.defaults}return n(r,i),r.prototype._init=function(){ej.isNullOrUndefined(this.element)||this._renderSpellCheck()},r.prototype._renderSpellCheck=function(){this._initLocalize();this._renderControls()},r.prototype._initLocalize=function(){this._localizedLabels=this._getLocalizedLabels()},r.prototype._renderControls=function(){var u,i,n,r,f;if(ej.isNullOrUndefined(this.model.controlsToValidate))this._addAttributes(this,this.element);else{for(t(this.element).attr("style","display:none"),this._controlIds=this.model.controlsToValidate.split(","),u=!1,i=0;i<this._controlIds.length;i++)if(n=t(this._controlIds[i]),n.length>0)if(u=!0,n.length>1)for(r=0;r<n.length;r++)f=t(n[r]),this._addAttributes(this,f);else this._addAttributes(this,n);this._elementStatus=this._statusFlag=u}this.model.isResponsive&&this._on(t(window),"resize",t.proxy(this._resizeSpellCheck,this))},r.prototype._addAttributes=function(n,i){t(i).addClass("e-spellcheck");i[0].spellcheck=!1;n._addEventListeners(n,i)},r.prototype._addEventListeners=function(n,i){n._isIframe(i)?(t(i)[0].contentWindow.document.addEventListener("input",function(){n._changeStatus(n)},!1),n.model.contextMenuSettings.enable&&(t(i)[0].contentWindow.document.addEventListener("mousedown",function(){n._elementRightClick(n)},!1),t(document)[0].addEventListener("mousedown",function(){n._elementRightClick(n)},!1),t(i)[0].contentWindow.document.addEventListener("keydown",function(t){n._spellValidateOnType(t)},!1))):(i[0].addEventListener("input",function(){n._changeStatus(n)},!1),n.model.contextMenuSettings.enable&&(n._on(t(document),"mousedown",t.proxy(n._elementRightClick,n)),n._on(t(i[0]),"keydown","",this._spellValidateOnType)))},r.prototype._changeStatus=function(n){n._statusFlag=!0;ej.isNullOrUndefined(n.model.controlsToValidate)||(n._controlIds=n.model.controlsToValidate.split(","),n._targetStatus=!0)},r.prototype._isIframe=function(n){return t(n)[0].tagName==="IFRAME"},r.prototype._resizeSpellCheck=function(){var n=!ej.isNullOrUndefined(this._spellCheckWindow)&&this._spellCheckWindow.parents().find(".e-spellcheck.e-dialog-wrap"),t=!ej.isNullOrUndefined(this._spellCheckWindow)&&n.length>0&&this._spellCheckWindow.data("ejDialog"),i,r,u;this.model.isResponsive?!ej.isNullOrUndefined(this._spellCheckWindow)&&n.length>0&&this._spellCheckWindow.ejDialog("isOpen")&&(t._dialogPosition(),i=this._spellCheckWindow.find(".e-suggesteditems").data("ejListBox"),i.refresh(!0),r=this._spellCheckWindow.find(".e-sentence .e-sentencescroller").data("ejScroller"),setTimeout(function(){n.find(".e-dialog-scroller").width(n.width()-2);n.find(".e-suggestionlist .e-content").width(n.find(".e-suggestionlist .e-content").width()-2);r.refresh()},4)):t._dialogPosition();!ej.isNullOrUndefined(this._alertWindow)&&this._alertWindow.data("ejDialog")&&(u=!ej.isNullOrUndefined(this._alertWindow)&&this._alertWindow.data("ejDialog"),u._dialogPosition())},r.prototype.showInDialog=function(){this._statusFlag?this._renderDialogWindow():this._alertWindowRender("show")},r.prototype.validate=function(){var i,r,n,u,f,e,s,o,h;if(this._statusFlag){if(i=[],this.model.contextMenuSettings.enable&&!ej.isNullOrUndefined(this.model.dictionarySettings.dictionaryUrl)){if(n="",this._controlIds.length>0&&!this._currentActiveElement&&this.model.enableValidateOnType||this._controlIds.length>0&&(!this.model.enableValidateOnType||!this._statusMultiTarget)){for(u=0;u<this._controlIds.length;u++)if(f=t(this._controlIds[u]),f.length>0)for(e=0;e<f.length;e++)s=t(f[e]),o="",o=this._elementTextProcess(this,s),n=n===""?n.concat(o):n.concat(" "+o)}else this.model.enableValidateOnType&&this._currentActiveElement?n=this._elementTextProcess(this,t(this._currentActiveElement)):(this._isIframe(this.element)&&t(this.element).contents().find("body").addClass("e-spellcheck"),n=this._elementTextProcess(this,this.element));if(i=this._filteringDiffWords(this,n),this._splitWords(n,this),r={targetSentence:n,requestType:"validate",additionalParams:null,webMethod:!1},this._trigger("actionBegin",r))return!1;i.length>0?this._ajaxRequest(this,i.join(" "),"validateOnType",r):i.length!==0||this._ignoreStatus?ej.isNullOrUndefined(this._errorWordDetails)?this.model.enableValidateOnType||this._alertWindowRender("show"):(h=this._filterErrorData(this,this._errorWordDetails),this._validateOnTypeOperations(this,h,n,"validateOnType")):(this._splitInputWords(n,this),i=ej.dataUtil.distinct(this._inputWords),this._ajaxRequest(this,i.join(" "),"validateOnType",r))}}else this._alertWindowRender("show")},r.prototype._filteringDiffWords=function(n,t){var f=n._inputWords,r=[],i,e,u;for(n._splitInputWords(t,n),i=n._inputWords,u=0;u<i.length;u++)e=f.indexOf(i[u]),e===-1&&r.push(i[u]);return i.length!==f.length&&r.length!==0&&i.length===r.length&&(r=i),r},r.prototype._elementTextProcess=function(n,i){return this.model.contextMenuSettings.enable&&this.model.enableValidateOnType&&(this._controlIds.length>0?i[0].nodeType==9&&i[0].nodeName=="#document"&&(i=t(this._controlIds[0])):i=t(this.element[0])),n._isIframe(i)?t(i).contents().find("body").text():ej.isNullOrUndefined(t(i)[0].value)?(t(i)[0].innerText||t(i)[0].textContent).trim():t(i)[0].value.trim()},r.prototype._splitWords=function(n,t){var i=n.split(/[^0-9a-zA-Z\'_]/);i=i.filter(function(n){return/\S/.test(n)});t._words=i},r.prototype._splitInputWords=function(n,t){var i=n.split(" ");t._inputWords=i},r.prototype.spellCheck=function(n,t){var i={targetSentence:n,misspelledWordCss:t,requestType:"spellCheck",webMethod:!1};if(this._trigger("actionBegin",i))return!1;this._misspelledWordCss=t;this._ajaxRequest(this,n,"spellCheck",i)},r.prototype.ignoreAll=function(n,t){var i;return ej.isNullOrUndefined(n)||n===""||ej.isNullOrUndefined(t)||t===""?!1:(i={ignoreWord:n,targetContent:t,requestType:"ignoreAll"},this._trigger("validating",i))?!1:(this.model.ignoreWords.push(n),this._updateErrorContent(n,t,null,"ignoreAll",null))},r.prototype.ignore=function(n,t,i){var r;return ej.isNullOrUndefined(n)||n===""||ej.isNullOrUndefined(t)||t===""?!1:(r={ignoreWord:n,targetContent:t,requestType:"ignore"},this._trigger("validating",r))?!1:this._updateErrorContent(n,t,null,"ignore",i)},r.prototype.change=function(n,t,i,r){var u;return ej.isNullOrUndefined(n)||n===""||ej.isNullOrUndefined(t)||t===""||ej.isNullOrUndefined(i)||i===""?!1:(u={changableWord:n,targetContent:t,changeWord:i,requestType:"changeWord"},this._trigger("validating",u))?!1:this._updateErrorContent(n,t,i,"changeWord",r)},r.prototype.changeAll=function(n,t,i){var u,r;return ej.isNullOrUndefined(n)||n===""||ej.isNullOrUndefined(t)||t===""||ej.isNullOrUndefined(i)||i===""?!1:(u={changableWord:n,targetContent:t,changeWord:i,requestType:"changeAll"},this._trigger("validating",u))?!1:(r={},r.ErrorWord=n,r.ReplaceWord=i,this._changeAllWords.push(r),this._updateErrorContent(n,t,i,"changeAll",null))},r.prototype.addToDictionary=function(n){if(ej.isNullOrUndefined(n)||n==="")return!1;var t={customWord:n,requestType:"addToDictionary",additionalParams:null};if(this._trigger("validating",t))return!1;this._customWord=n;this._ajaxRequest(this,null,"addToDictionary",t)},r.prototype._updateErrorContent=function(n,t,i,r,u){var s,f,o,h,e,c;if(t.indexOf(n)!==-1){if(f='<span class="errorspan '+this.model.misspellWordCss+'">'+n+"<\/span>",o=r==="ignoreAll"||r==="addToDictionary"||r==="ignore"?n:i,r==="ignoreAll"||r==="addToDictionary"||r==="changeAll")t=t.replace(new RegExp(f,"g"),o);else if(r==="ignore"||r==="changeWord")if(ej.isNullOrUndefined(u))t=t.replace(f,o);else{for(h=[],e=t.indexOf(f);e!==-1;)h.push(e),e=t.indexOf(f,++e);c=h[u];t=t.substr(0,c)+o+t.substr(c+f.length)}s={resultHTML:t}}else s=!1;return s},r.prototype._renderDialogWindow=function(){this._dialogWindowRendering();this._showDialog()},r.prototype._dialogWindowRendering=function(){var n=this,f,i;this._spellCheckWindow=ej.buildTag("div.e-spellcheckdialog#"+this._id+"ErrorCorrectionWindow");var e=ej.buildTag("div.e-dialogdiv"),o=ej.buildTag("div.e-row e-labelrow").append(ej.buildTag("div.e-labelcell").append(ej.buildTag("label.e-dictionarylabel",this._localizedLabels.NotInDictionary))),r=ej.buildTag("div.e-row e-sentencerow"),s=ej.buildTag("div.e-cell e-sentencecell").append(ej.buildTag("div.e-sentence","",{},{id:this._id+"_Sentences",name:"sentences",contenteditable:"false"}));r.append(s);var h=ej.buildTag("div.e-buttoncell"),c=ej.buildTag("button.e-btnignoreonce",this._localizedLabels.IgnoreOnceButtonText,{},{id:this._id+"_IgnoreOnce"}).attr("type","button"),l=ej.buildTag("button.e-btnignoreall",this._localizedLabels.IgnoreAllButtonText,{},{id:this._id+"_IgnoreAll"}).attr("type","button"),a=ej.buildTag("button.e-btnaddtodictionary",this._localizedLabels.AddToDictionary,{},{id:this._id+"_AddToDictionary"}).attr("type","button");r.append(h.append(c).append(l).append(a));var v=ej.buildTag("div.e-row e-labelrow").append(ej.buildTag("div.e-labelcell").append(ej.buildTag("label.e-lablesuggestions",this._localizedLabels.SuggestionLabel))),u=ej.buildTag("div.e-row e-suggestionsrow"),y=ej.buildTag("div.e-cell e-suggestioncell").append(ej.buildTag("ul.e-suggesteditems","",{},{id:this._id+"_Suggestions"}));u.append(y);var p=ej.buildTag("div.e-buttoncell"),w=ej.buildTag("button.e-btnchange",this._localizedLabels.ChangeButtonText,{},{id:this._id+"_Change"}).attr("type","button"),b=ej.buildTag("button.e-btnchangeall",this._localizedLabels.ChangeAllButtonText,{},{id:this._id+"_ChangeAll"}).attr("type","button"),k=ej.buildTag("button.e-btnclose",this._localizedLabels.CloseButtonText,{},{id:this._id+"_Close"}).attr("type","button");for(u.append(p.append(w).append(b).append(k)),e.append(o).append(r).append(v).append(u),this._spellCheckWindow.append(e),this._spellCheckWindow.ejDialog({width:462,minHeight:305,enableModal:!0,enableResize:!1,showOnInit:!1,allowKeyboardNavigation:!1,target:t("body"),title:this._localizedLabels.SpellCheckButtonText,close:function(){n._close()},cssClass:"e-spellcheck",isResponsive:this.model.isResponsive}),f=[".e-btnignoreonce",".e-btnignoreall",".e-btnaddtodictionary",".e-btnchange",".e-btnchangeall",".e-btnclose"],i=0;i<f.length;i++)this._spellCheckWindow.find(f[i]).ejButton({width:this.model.isResponsive?"100%":140,click:function(t){t.model.text===n._localizedLabels.CloseButtonText?n._close():n._changeErrorWord(t)},cssClass:"e-spellbuttons"});this._spellCheckWindow.find(".e-sentence").append(ej.buildTag("div.e-sentencescroller").append(ej.buildTag("div").append(ej.buildTag("div.e-sentencecontent","",{},{id:this._id+"_SentenceContent"}))));this._spellCheckWindow.find(".e-sentence .e-sentencescroller").ejScroller({height:"100%",scrollerSize:20});this._spellCheckWindow.find(".e-suggesteditems").ejListBox({width:"100%",height:"100%",dataSource:null,selectedIndex:0,cssClass:"e-suggestionlist"})},r.prototype._alertWindowRender=function(n){this._renderAlertWindow(n);this._elementStatus||this._alertWindow.find(".e-alerttext").html(this._localizedLabels.NotValidElement);var t={spellCheckDialog:this._renderAlertWindow,requestType:"alertBeforeOpen"};if(this._trigger("dialogBeforeOpen",t))return!ej.isNullOrUndefined(this._spellCheckWindow)&&this._spellCheckWindow.parents().find(".e-spellcheck.e-dialog-wrap").length>0&&this._close(),!1;this._alertWindow.ejDialog("open")},r.prototype._renderAlertWindow=function(n){var i=this;this._alertWindow=ej.buildTag("div.e-alertdialog#"+this._id+"alertWindow");this._elementStatus||this._alertWindow.addClass("e-missingalert");var u=ej.buildTag("div.e-alertbtn","",{"text-align":"center"}).append(ej.buildTag("button.e-alertbutton e-alertspellok",this._localizedLabels.Ok,{},{id:this._id+"alertok"}).attr("type","button")),r=ej.buildTag("div.e-alerttextdiv"),f=ej.buildTag("div.e-alertnotifydiv").append(ej.buildTag("div.e-alertnotification e-icon e-notification")),e=ej.buildTag("div.e-alerttext",this._localizedLabels.CompletionPopupMessage,{"text-align":"left",padding:"5px"});r.append(f).append(e);this._alertWindow.append(r).append(u);this.element.append(this._alertWindow);this._alertWindow.find(".e-alertbutton").ejButton({showRoundedCorner:!0,width:this._elementStatus?"70px":"100px",click:function(){i._alertClose()},cssClass:"e-flat"});this._alertWindow.ejDialog({width:this._elementStatus?240:420,minHeight:140,showOnInit:!1,enableModal:!0,title:this._localizedLabels.CompletionPopupTitle,enableResize:!1,allowKeyboardNavigation:!1,target:n==="validating"?".e-spellcheckdialog":t("body"),cssClass:this._elementStatus?"e-spellalert":"e-spellalert e-elementmissing",close:function(){i._alertClose()},isResponsive:this.model.isResponsive})},r.prototype._renderContextMenu=function(){var n=this,u,r,i,f;if(this._contextMenu=ej.buildTag("ul#"+n._id+"contextMenu"),ej.isNullOrUndefined(n.model.controlsToValidate))u=n._isIframe(this.element)?n.element.contents()[0]:"."+n.model.misspellWordCss;else{for(r=!1,i=0;i<n._controlIds.length;i++)f=n._isIframe(t(n._controlIds[i])),f&&(r=!0);u=r?t(n._controlIds[0]).contents()[0]:"."+n.model.misspellWordCss}this._contextMenu.ejMenu({fields:{id:"id",text:"text",parentId:"parentId"},menuType:ej.MenuType.ContextMenu,openOnClick:!1,width:"auto",cssClass:"e-spellmenu",click:function(t){n._onMenuSelect(t)}})},r.prototype._contextMenuPosition=function(n,i){var u,r,e,f,o,s;return!ej.isNullOrUndefined(i._activeElement)&&i._isIframe(t(i.element))?(e=ej.isNullOrUndefined(i.model.controlsToValidate)?t(i.element):t(i._control[0].controlId),u=(n.clientX==undefined?0:n.clientX)+e.offset().left,r=(n.clientY==undefined?0:n.clientY)+e.offset().top,f=t(i._contextMenu).attr("style","visibility: visible;display:block;").height(),o=t(i._contextMenu).width(),r=r+f<t(document).scrollTop()+t(window).height()?r:r-f<0?r:r-f,u=u+o<t(document).scrollLeft()+t(window).width()?u:u-o):(u=n.clientX+i._contextMenu.width()<t(window).width()?n.pageX:n.pageX-i._contextMenu.width(),r=n.clientY+i._contextMenu.height()<t(window).height()?n.pageY:n.clientY>i._contextMenu.height()?n.pageY-i._contextMenu.height():t(window).height()-i._contextMenu.outerHeight(),s=t("body").css("position")!=="static"?t("body").offset():{left:0,top:0},u-=s.left,r-=s.top),{X:u,Y:r}},r.prototype._showDialog=function(){var v={spellCheckDialog:this._spellCheckWindow,requestType:"dialogBeforeOpen"},i,n,f,e,o,u,s,r,c,l,h,a;if(this._trigger("dialogBeforeOpen",v))return!1;if(this._spellCheckWindow.ejDialog("open"),i="",this._subElements=[],this._controlIds.length>0){for(f=0;f<this._controlIds.length;f++)if(e=t(this._controlIds[f]),e.length>0)for(o=0;o<e.length;o++)u=t(e[o]),this._activeElement=this._isIframe(u)?t(u).contents().find("body")[0]:t(u)[0],this._removeSpan(this),this._subElements.push(u[0]);i=this._inputTextProcess(this,t(this._subElements[0]),i);this._proElements=this._subElements.length>0&&t(this._subElements[0]);this._currentTargetElement=n=t(this._subElements[0]);this._subElements=this._subElements.slice(1)}else n=this.element,this._activeElement=this._isIframe(n)?this._getIframeElement(n):t(n)[0],this._removeSpan(this),i=this._inputTextProcess(this,n,i);if(s="",this.element=ej.isNullOrUndefined(this.model.controlsToValidate)?this.element:n,this.element.length>0&&(s=this._isIframe(this.element)?t(this.element).contents().find("body").html():t(n)[0].tagName==="TEXTAREA"||t(n)[0].tagName==="INPUT"?t(n)[0].value:t(n)[0].innerHTML),r=this._filteringDiffWords(this,i),this._splitWords(i,this),!ej.isNullOrUndefined(this.model.controlsToValidate)&&(c={previousElement:null,currentElement:n,targetHtml:s},this._trigger("targetUpdating",c)))return this._close(),!1;if((this._spellCheckWindow.find(".e-sentence .e-sentencecontent")[0].innerHTML=s,l={targetText:i,requestType:"dialogOpen"},this._trigger("dialogOpen",l))||(h={targetSentence:i,requestType:"spellCheck",additionalParams:null,webMethod:!1},this._trigger("actionBegin",h)))return!1;r.length>0?this._ajaxRequest(this,r.join(" "),"spellCheckDialog",h):r.length!==0||this._ignoreStatus?ej.isNullOrUndefined(this._errorWordDetails)?this._alertWindowRender("show"):(a=this._filterErrorData(this,this._errorWordDetails),this._dialogModeOperations(this,a,i,"spellCheckDialog")):(this._splitInputWords(i,this),r=ej.dataUtil.distinct(this._inputWords),this._ajaxRequest(this,r.join(" "),"spellCheckDialog",h))},r.prototype._getIframeElement=function(n){return t(n).contents().find("body")[0]},r.prototype._inputTextProcess=function(n,i,r){var u,f;return n._isIframe(i)?(u=t(i).contents().find("body").text(),r=r===""?u:r+u):(f=ej.isNullOrUndefined(t(i)[0].value)?(t(i)[0].innerText||t(i)[0].textContent).trim():t(i)[0].value.trim(),r=r===""?f:r+" "+f),r},r.prototype._ajaxRequest=function(n,i,r,u){var f=r==="addToDictionary"?JSON.stringify({customWord:n._customWord,additionalParams:u.additionalParams}):this._getModelValues(this,i,u);this.webMethod=this.webMethod?this.webMethod:u.webMethod;t.ajax({type:this.model.ajaxRequestType,async:this.model.enableAsync,url:r==="addToDictionary"?this.model.dictionarySettings.customDictionaryUrl:this.model.dictionarySettings.dictionaryUrl,data:this.model.ajaxDataType==="json"&&this.model.ajaxRequestType==="POST"?this.webMethod?JSON.stringify({data:f}):JSON.stringify(f):{data:f},contentType:"application/json; charset=utf-8",dataType:this.model.ajaxDataType,crossDomain:!0,success:function(u){var f,c,b,o,s,e,k,h,d,it,g,p,l,nt,w,v,y,a,tt;if(u=u&&u.d&&typeof u.d=="object"?u.d:u,c=typeof u=="string"&&r!=="addToDictionary"?JSON.parse(u):u,r==="addToDictionary"?ej.isNullOrUndefined(n._errorWordDetails)||ej.isNullOrUndefined(n._currentElement)?f=[]:(f=n._errorWordDetails,ej.isNullOrUndefined(c)||(n._filterData(c.toString(),n),n._errorWordDetails=n._errorWordsData)):f=n._updateErrorDetails(n,c),o=i,f.length>0){if(r==="spellCheckDialog"||r==="validateOnType"||r==="validateOnRender")e=n._filterErrorData(n,f),e.length>0?r==="spellCheckDialog"?n._dialogModeOperations(n,e,o,r):(r==="validateOnType"||r==="validateOnRender")&&n._validateOnTypeOperations(n,e,o,r):(r==="spellCheckDialog"&&n._spellCheckWindow.ejDialog("isOpen")&&n._spellCheckWindow.ejDialog("close"),n._alertWindowRender("validating"));else if(r==="spellCheck"){if(f.length>0){for(k=n._getFilterData(f,n),e=ej.dataUtil.distinct(k),h=0;h<e.length;h++)d=(new ej.Query).where("ErrorWord",ej.FilterOperators.equal,e[h]),it=new ej.DataManager(f).executeLocal(d),e.length>0&&(b='<span class="errorspan '+(!ej.isNullOrUndefined(n._misspelledWordCss)&&n._misspelledWordCss!==""?n._misspelledWordCss:n.model.misspellWordCss)+'">'+e[h]+"<\/span>",g=new RegExp(e[h],"gi"),o=o.replace(g,b));s={resultHTML:o,errorWordDetails:f,requestType:"spellCheck"};n._misspelledWordCss=null}else s={resultHTML:o,errorWordDetails:f,requestType:"spellCheck"};n._trigger("actionSuccess",s)}else if(r==="addToDictionary"&&(ej.isNullOrUndefined(n._currentElement)||(p=t(n._currentElement)[0].tagName==="IFRAME"?t(n._currentElement).contents().find("body").html():t(n._currentElement).html().trim()),l=n._updateErrorContent(n._customWord,p,null,"addToDictionary",null),!ej.isNullOrUndefined(p))){if(!ej.isNullOrUndefined(n._spellCheckWindow)&&n._spellCheckWindow.find(".e-btnaddtodictionary").hasClass("e-select"))nt=n._spellCheckWindow.find(".e-suggesteditems"),w=n._spellCheckWindow.find(".e-sentence .e-sentencecontent"),n._errorWordsData.length>0?(w[0].innerHTML=l.resultHTML,n._replaceErrorText(w,n._customWord.toString()),n._listBoxDataUpdate(n)):(nt.ejListBox({dataSource:null}),n._statusFlag=!1,n._alertWindowRender("validating"));else if(!ej.isNullOrUndefined(n._contextMenu)){if(n._isIframe(n.element)?t(n.element).contents().find("body").html(l.resultHTML):t(n._currentElement)[0].innerHTML=l.resultHTML,n._controlIds.length>0)for(v=0;v<n._controlIds.length;v++)for(y=t(n._controlIds[v]),a=0;a<y.length;a++)t(n._currentElement)[0]!==t(y[a])[0]&&(tt=t(y[a]),n._replaceErrorText(tt,n._customWord.toString()));n._renderMenu(n)}s={resultHTML:l.resultHTML,errorWordDetails:c,requestType:"addToDictionary"};n._trigger("actionSuccess",s)}}else n._subElements.length>0?n._updateTargetText(n):(r==="spellCheckDialog"&&n._spellCheckWindow.ejDialog("isOpen")&&n._spellCheckWindow.ejDialog("close"),r==="spellCheck"&&(s={resultHTML:i,errorWordDetails:f,requestType:"spellCheck"},n._trigger("actionSuccess",s)),r==="validateOnType"&&n._removeSpan(n),r!=="spellCheck"&&r!=="addToDictionary"&&n._alertWindowRender("load"))},error:function(t,i,u){var f={errorMessage:u,requestType:r};n._trigger("actionFailure",f)}})},r.prototype.getSuggestionWords=function(n){this._selectedValue=n;this._suggestionsRequest(this,null,n,"getSuggestions")},r.prototype._suggestionsRequest=function(n,i,r,u){var f;f=u==="validateByMenu"||u==="suggestionsUpdate"||u==="getSuggestions"?JSON.stringify({requestType:"getSuggestions",errorWord:r}):n._getModelValues(n,r,null);t.ajax({type:this.model.ajaxRequestType,async:n.model.enableAsync,url:n.model.dictionarySettings.dictionaryUrl,data:this.model.ajaxDataType==="json"&&this.model.ajaxRequestType==="POST"?this.webMethod?JSON.stringify({data:f}):JSON.stringify(f):{data:f},contentType:"application/json; charset=utf-8",dataType:n.model.ajaxDataType,crossDomain:!0,success:function(t){var e={},f,s,o,h,c;t=t&&t.d&&typeof t.d=="object"?t.d:t;f=typeof t=="string"?JSON.parse(t):t;e.ErrorWord=r;e.SuggestedWords=f[r];n._suggestedWordCollection.push(e);u==="getSuggestions"?n._suggestedWords=f[n._selectedValue]:u==="validateByMenu"?(s=f[r],n._contextMenuDisplay(n,s)):u==="validateByDialog"?(o=n._updateErrorDetails(n,f),o.length>0?(h=n._filterErrorData(n,o),n._splitWords(i[0].innerText,n),n._processNode(n,i[0],h,"spellCheckDialog"),n._activeElement=i[0],n._changeAllErrors(n),n._listBoxDataUpdate(n)):n._subElements.length>0?n._updateTargetText(n):n._completionCheck(n)):u==="suggestionsUpdate"&&(c=f[i[0].innerText],n._dialogSuggestionsUpdate(n,c))}})},r.prototype._filterErrorData=function(n,t){var i=n._getFilterData(t,n);return ej.dataUtil.distinct(i)},r.prototype._updateErrorDetails=function(n,t){var i=[],r;if(ej.isNullOrUndefined(n._errorWordDetails))i=n._errorWordDetails=t;else if(t.length>0)if(n._ignoreStatus)for(r=0;r<t.length;r++)n._errorWordDetails.push(t[r]),i=n._errorWordDetails;else i=n._errorWordDetails=t,n._ignoreStatus=!0;else i=n._errorWordDetails;return i},r.prototype._contextMenuDisplay=function(n,i){var r,o,u,c,a,v,s,f,h,e,l;if(ej.isNullOrUndefined(n._contextMenu)&&n._renderContextMenu(),r=n._contextMenu.data("ejMenu"),o=n.model.contextMenuSettings.menuItems,i.length>0&&this.model.maxSuggestionCount>0){for(u=[],c=n.model.maxSuggestionCount<i.length?n.model.maxSuggestionCount:i.length,u=n._convertData(i.slice(0,c),"menuData"),a=u.length,v=u[c-1].id,s=0;s<o.length;s++)u.push(o[s]);for(r.option("fields.dataSource",u),f=r.element.find(".e-list"),h=0;h<a;h++)t(f[h]).addClass("e-errorsuggestions");for(e=0;e<f.length;e++)f[e].attributes.id.value===v&&t(f[e]).addClass("e-separator")}else r.option("fields.dataSource",o);l=n._contextMenuPosition(n._menuEvents,n);t(r.element).css({left:l.X,top:l.Y});t(r.element).css("display","block")},r.prototype._dialogSuggestionsUpdate=function(n,i){var f=n._spellCheckWindow.find(".e-suggesteditems"),e=t("#"+n._id+"_Suggestions").data("ejListBox"),r,o,u;i.length>0?(n._spellCheckWindow.find(".e-btnchange").hasClass("e-disable")&&n._spellCheckWindow.find(".e-btnchangeall").hasClass("e-disable")&&(n._spellCheckWindow.find(".e-btnchange").removeClass("e-disable"),n._spellCheckWindow.find(".e-btnchangeall").removeClass("e-disable")),o=n.model.maxSuggestionCount<i.length?n.model.maxSuggestionCount:i.length,r=i.slice(0,o)):(n._spellCheckWindow.find(".e-btnchange").addClass("e-disable"),n._spellCheckWindow.find(".e-btnchangeall").addClass("e-disable"),r=[n._localizedLabels.NoSuggestionMessage]);f.ejListBox({selectedIndex:null});f.ejListBox({dataSource:n._convertData(r,"dictionaryData"),selectedIndex:0});ej.isNullOrUndefined(e)||e.refresh();u=n._spellCheckWindow.find(".e-sentence .e-sentencescroller").data("ejScroller");!ej.isNullOrUndefined(u)&&u.isVScroll()&&t(n._spellCheckWindow.find("."+n.model.misspellWordCss)).get(0).scrollIntoView(!1)},r.prototype._replaceErrorText=function(n,i){for(var f,u=t(n).find(".errorspan"),r=0;r<u.length;r++)f=u[r].innerText||u[r].textContent,f===i&&t(u[r]).replaceWith(f)},r.prototype._dialogModeOperations=function(n,t,i,r){var o={errorWords:n._errorWordDetails,targetText:i,requestType:r},u=n._spellCheckWindow.find(".e-sentence .e-sentencecontent"),f,e;if(t.length>0){if(n._removeSpan(n),n._processNode(n,u[0],t,r),this._trigger("start",o))return!1;f=n._spellCheckWindow.find(".e-sentence .e-sentencescroller").data("ejScroller");f.refresh();n._listBoxDataUpdate(n)}else e=n._spellCheckWindow.find(".e-suggesteditems"),r==="spellCheckDialog"&&(u[0].innerHTML=i),e.ejListBox({dataSource:null}),n._statusFlag=!1,this._alertWindowRender("load")},r.prototype._validateOnTypeOperations=function(n,i,r,u){var o,e,h,c,s,l,f;if(i.length>0){if(this._controlIds.length>0&&!this._currentActiveElement&&this.model.enableValidateOnType||this._controlIds.length>0&&(!this.model.enableValidateOnType||!this._statusMultiTarget))for(f=0;f<n._controlIds.length;f++)for(o=t(this._controlIds[f]),e=0;e<o.length;e++)h=n._isIframe(t(o[e]))?n._getIframeElement(t(o[e])):t(o[e])[0],n._activeElement=h,n._removeSpan(n),n._processNode(n,h,i,u),c={},c.controlId=n._controlIds[f],c.errorHtml=h.innerHTML,n._control.push(c);else this.model.enableValidateOnType&&this._currentActiveElement?(n._removeSpan(n),n._processNode(n,this._currentActiveElement,i,u),this._statusMultiTarget=!1,n._isIframe(n.element)&&(s=n._getIframeElement(n.element),n._activeElement=s)):n._isIframe(n.element)?(s=n._getIframeElement(n.element),n._activeElement=s,n._removeSpan(n),n._processNode(n,s,i,u)):(n._removeSpan(n),n._processNode(n,t(n.element)[0],i,u));if(n._statusFlag=!0,l=this._controlIds.length>0?{errorWords:n._errorWordDetails,targetControls:this._control,requestType:u}:{errorWords:n._errorWordDetails,targetText:t(n.element)[0].innerText,requestType:u},this._trigger("start",l))return!1;if(n._isIframe(this.element))n._bindBeforeOpen(n,t(this.element).contents().find("body"));else if(n._controlIds.length>0)for(f=0;f<n._controlIds.length;f++)n._bindBeforeOpen(n,t(n._controlIds[f]));else n._bindBeforeOpen(n,t(this.element))}else n._removeSpan(n),n._statusFlag=!1,n._alertWindowRender("show");this.model.enableValidateOnType&&n.setCursorPosition(n._currentCursorTarget)},r.prototype._bindBeforeOpen=function(n,i){n._on(t(i).find("."+this.model.misspellWordCss),"contextmenu",t.proxy(n._contextOpen,n))},r.prototype._contextOpen=function(n){var u=t(n.target),i,r,e,f;if(u.hasClass("errorspan")){if(n.preventDefault(),i=this,r=i._selectedValue=u[0].innerText,i._selectedTarget=u[0],i._menuEvents=n,e={selectedErrorWord:r,requestType:"contextOpen"},i._trigger("contextOpen",e))return!1;f=i._filterSuggestions(i,r);f.length>0?i._contextMenuDisplay(i,f[0].SuggestedWords):i._suggestionsRequest(i,null,r,"validateByMenu")}else this._elementRightClick(n)},r.prototype._processNode=function(n,t,i,r){for(var b,k,c,u,p,s=n._filterTextNodes(n,t),e=0;e<s.length;e++){var o=s[e],w=[s[e]],h=s[e].data,l=!1,a=!1,v=!1;if(n.model.ignoreSettings.ignoreUrl&&(b=/^((http|ftp|https)?:\/\/)?(www\.)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/,l=b.test(o.wholeText),l&&(v=l)),n.model.ignoreSettings.ignoreEmailAddress&&(k=/^[-a-z0-9~!$%^&*_=+}{\'?]+(\.[-a-z0-9~!$%^&*_=+}{\'?]+)*@([a-z0-9_][-a-z0-9_]*(\.[-a-z0-9_]+)*\.(aero|arpa|biz|com|coop|edu|gov|info|int|mil|museum|name|net|org|pro|travel|mobi|[a-z][a-z])|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})?$/i,a=k.test(o.wholeText),a&&(v=a)),!v)for(c=0;c<n._words.length;c++)for(u=0;u<i.length;u++)if(n._words[c]===i[u]&&!ej.isNullOrUndefined(h.match(new RegExp("\\b"+i[u]+"\\b","gi")))&&h.indexOf(i[u])!==-1){var d=h.indexOf(i[u]),g=i[u].length,f=o.splitText(d),y=document.createElement("span");y.className=r==="validateOnType"?"errorspan "+this.model.misspellWordCss:"errorspan";p=document.createTextNode(i[u]);y.appendChild(p);o.parentNode.insertBefore(y,f);f.data=f.data.substr(g);o=f;w.push(p);w.push(f);h=f.data}}},r.prototype._findRepeatWords=function(n,t,i,r){for(var o,f,s,e,h=/([{^}:[\\.,;><?|@!~`#$%&*()_=+'"])/g,u=n;u<=t.length;u++)if(e=!1,f=t.charAt(u),s=f.charCodeAt(f),e=h.test(f),f==" "||s==160||f==""||e)if(o=t.slice(n,u),o===i[r])break;else n=u+1;return n},r.prototype._spellValidateOnType=function(n){var i,t;if(this.model.enableValidateOnType&&this.model.contextMenuSettings.enable&&(i={events:n,requestType:"validate"},this._trigger("validating",i),this._statusMultiTarget=!1,this._currentActiveElement=i.events.currentTarget,i.events.cancelable===!0)){if(t=n.keyCode,t>=16&&t<=31)return;if(t>=37&&t<=40)return;(t===32||t===13)&&(this._statusMultiTarget=!0,this._triggerSpelling())}},r.prototype._triggerSpelling=function(){var n=this;setTimeout(function(){n.getCursorPosition();n.validate()},2)},r.prototype.getCursorPosition=function(){var n,f,r,i,e,o,u,s,h,c,l;if(this.model.enableValidateOnType&&this.model.contextMenuSettings.enable){if(n=this,f=String.fromCharCode(7),this._controlIds.length>0)for(e=0;e<this._controlIds.length;e++)o=t(this._controlIds[e]),this._isIframe(o)?(r=o[0].contentWindow.getSelection(),i=o[0].contentDocument.createRange()):(r=document.getSelection(),i=document.createRange());else this._isIframe(this.element)?(r=this.element[0].contentWindow.getSelection(),i=this.element[0].contentDocument.createRange()):(r=document.getSelection(),i=document.createRange());if(u=r.getRangeAt(0),u.deleteContents(),this._isIframe(this.element)&&ej.browserInfo().name==="msie"){for(s=this.element[0].contentDocument.createElement("div"),s.innerHTML=f,h=t(this.element[0]).contents()[0].createDocumentFragment();c=s.firstChild;)l=h.appendChild(c);u.insertNode(h)}else u.insertNode(document.createTextNode(f));return t(u.startContainer.parentElement).hasClass("errorspan")&&(this.model.controlsToValidate?n._normalizeTextNodes(this._currentActiveElement):n._normalizeTextNodes(t(n.element)[0])),n._currentCursorTarget=n._getActiveTarget(n,f),i.collapse(!0),i.setStart(n._currentCursorTarget.node,n._currentCursorTarget.offset),i.setEnd(n._currentCursorTarget.node,n._currentCursorTarget.offset),r.removeAllRanges(),r.addRange(i),n._currentCursorTarget}},r.prototype._getActiveTarget=function(n,i){var u,f,e,r;for(u=this.model.enableValidateOnType?n._filterTextNodes(n,this._currentActiveElement):n._filterTextNodes(n,t(n.element)[0]),f=null,e=null,r=0;r<u.length;r++)if(u[r].data.indexOf(i)>-1)return e=u[r],f=u[r].data.indexOf(i),u[r].data=u[r].data.replace(i,""),{node:e,offset:f}},r.prototype.setCursorPosition=function(n){var s,h,v,r,d,o,c,f,u,p,e,b,i,k;if(this._controlIds.length>0)for(i=0;i<this._controlIds.length;i++)v=t(this._controlIds[i]),this._isIframe(v)?(s=v[0].contentDocument.getSelection(),h=v[0].contentDocument.createRange()):(s=document.getSelection(),h=document.createRange());else this._isIframe(this.element)?(s=this.element[0].contentDocument.getSelection(),h=this.element[0].contentDocument.createRange()):(s=document.getSelection(),h=document.createRange());if(s.getRangeAt&&s.rangeCount&&(d=String.fromCharCode(7),n)){for(r=this.model.controlsToValidate?this._filterTextNodes(this,this._currentActiveElement):this._filterTextNodes(this,t(this.element)[0]),o=n.node,c=n.offset,i=0;i<r.length;i++)r[i]===o&&(f=i);if(u="",f===undefined){var a="",l="",w="",u="",g="",nt=!1,tt=!1,y=!1;for(p=0;p<o.length;p++)e=o.data.charAt(p),e.charCodeAt(0)!=160?e.charCodeAt(0)!=32?(!y&&this.model.enableValidateOnType&&(this._isAlphabet(e)||(u="",y=!0)),y&&u.length==1&&!this._isAlphabet(u)&&(u=""),a===""?u=u+e:e!=d&&(l=l+e)):(a=u+e,l=e):(a=u+e,w=" "+w+e);for(u=u+l,g=a.trim(),i=0;i<r.length;i++){if(r[i].data===u&&(f=i),r[i].data===a&&(f=i),r[i].data===l&&l!=""&&(f=i,nt=!0),r[i].data===u&&u!=""){f=i;tt=!0;break}if(r[i].data===g&&(f=i),r[i].data===u&&r[i+1]!==undefined&&r[i+1].data.charCodeAt(0)===160){f=i;break}if(r[i].data===u&&r[i+1]!==undefined&&r[i+1].data.charCodeAt(1)===160&&w.length>=1){f=i;break}if((r[i].data===a||r[i].data===u)&&r[i+1]==undefined){b=document.createTextNode("");r.push(b);this._currentActiveElement.appendChild(b);f=i+1;c=r[f].data.length;o=r[f];break}}}for(i=f;i<r.length-1;i++){if(c<=r[i].data.length){o=r[i];break}if(nt===!1||l===undefined||l==="")c-=r[i].data.length,o=r[i+1];else{c=1;o=r[i];break}if(y&&this.model.enableValidateOnType){c=r[i].data.length;o=r[i];break}}k=o;h.collapse(!0);h.setStart(k,c);h.setEnd(k,c);s.removeAllRanges();s.addRange(h)}},r.prototype._isAlphabet=function(n){return n.charCodeAt(0)>=65&&n.charCodeAt(0)<=90||n.charCodeAt(0)>=97&&n.charCodeAt(0)<=122?!0:!1},r.prototype._normalizeTextNodes=function(n){n.normalize();return},r.prototype._filterTextNodes=function(n,t){function r(n){for(var t,u=0;u<n.childNodes.length;u++)t=n.childNodes[u],t.nodeType===3?i.push(t):t.childNodes&&r(t)}var i=[];return r(t),i},r.prototype._removeSpan=function(n){var u,r,i,f;for(n.model.enableValidateOnType&&n._statusMultiTarget?n._currentActiveElement&&(u=n._currentActiveElement):u=!ej.isNullOrUndefined(n.model.controlsToValidate)||n._isIframe(n.element)?n._activeElement:n.element[0],r=t(u).find("span.errorspan"),i=0;i<r.length;i++)f=r[i].innerText||r[i].textContent,t(r[i]).replaceWith(f)},r.prototype._getFilterData=function(n,t){var u=[],i,r;for(t._errorWordsData=t._errorWordDetails=n,i=0;i<t.model.ignoreWords.length;i++)t._filterData(t.model.ignoreWords[i],t);for(r=0;r<t._errorWordsData.length;r++)u.push(t._errorWordsData[r].ErrorWord);return u},r.prototype._filterData=function(n,t){var i=(new ej.Query).where("ErrorWord",ej.FilterOperators.notEqual,n);t._errorWordsData=new ej.DataManager(t._errorWordsData).executeLocal(i)},r.prototype._formHtml=function(n,t){for(var r,u,i=0;i<n.length;i++)r='<span class="errorspan">'+n[i]+"<\/span>",u=new RegExp("\\b"+n[i]+"\\b","gi"),t=t.replace(u,r);return t},r.prototype._listBoxDataUpdate=function(n){var i=n._spellCheckWindow.find(".e-sentence .e-sentencecontent").find(".errorspan"),r,u;t(i[0]).addClass(this.model.misspellWordCss);i.length>0?(r=n._filterSuggestions(n,i[0].innerText),r.length>0?n._dialogSuggestionsUpdate(n,r[0].SuggestedWords):n._suggestionsRequest(n,i,i[0].innerText,"suggestionsUpdate")):!ej.isNullOrUndefined(this.model.controlsToValidate)&&n._targetStatus?n._updateTargetText(n):(u=n._spellCheckWindow.find(".e-sentence .e-sentencecontent")[0].innerHTML,n._validationComplete(n,u))},r.prototype._filterSuggestions=function(n,t){var i=[],r,u;return n._suggestedWordCollection.length>0&&(r=(new ej.Query).where("ErrorWord",ej.FilterOperators.equal,t),u=new ej.DataManager(n._suggestedWordCollection).executeLocal(r),i=u),i},r.prototype._validationComplete=function(n,t){n._updateTargetString(n);var i=ej.isNullOrUndefined(n._activeElement)?n.element:n._activeElement,r={targetElement:i,targetText:t,requestType:"changeErrorWord"};if(this._trigger("complete",r))return!1;n._statusFlag=!1;n._alertWindowRender("validating")},r.prototype._onMenuSelect=function(n){var s=n.events.ID.split("_"),e=!1,l,i,u,a,v,h,c,r,o,f,y,p,w,b;if(ej.isNullOrUndefined(this.model.controlsToValidate))e=this._isIframe(this.element);else for(r=0;r<this._controlIds.length;r++)l=this._isIframe(t(this._controlIds[r])),l&&(e=!0),e&&(this.element=t(this._controlIds[0]));if(i=e?this.element:this._selectedTarget.parentElement,this._activeElement=i,u="",u=this._isIframe(t(i))?t(i).contents().find("body").html():t(i).html().trim(),a={selectedOption:s[0],requestType:"menuSelect",targetContent:u,selectedValue:this._selectedValue},this._trigger("contextClick",a))return!1;switch(s[0]){case"AddToDictionary":v=(this._selectedTarget.innerText||this._selectedTarget.textContent).trim();this._currentElement=t(i);this.addToDictionary(v);break;case"IgnoreAll":if(h=(this._selectedTarget.innerText||this._selectedTarget.textContent).trim(),c=this.ignoreAll(h,u),u=c.resultHTML,t(i).html(c.resultHTML),this._controlIds.length>0)for(r=0;r<this._controlIds.length;r++)for(o=t(this._controlIds[r]),f=0;f<o.length;f++)t(i)[0]!==t(o[f])[0]&&(y=t(o[f]),this._replaceErrorText(y,h));this._renderMenu(this);break;default:p=s[0];w=t(n.element).hasClass("e-errorsuggestions");w&&(this._selectedTarget.innerHTML=p,b=document.createTextNode(this._selectedTarget.innerText||this._selectedTarget.textContent),this._selectedTarget.parentNode.insertBefore(b,this._selectedTarget),t(this._selectedTarget).remove(),u=t(i).html());this._renderMenu(this)}},r.prototype._renderMenu=function(n){var i,r=ej.isNullOrUndefined(n._activeElement)?n.element:n._activeElement,u,e,f;if(n._controlIds.length>0){for(u=0;u<n._controlIds.length;u++)if(i=n._getErrorLength(n,t(n._controlIds[u])),i>0)break}else i=n._getErrorLength(n,t(n.element));if(i===0&&(e={targetElement:r,requestType:"validate"},n._trigger("complete",e)))return!1;n._statusFlag=i>0?!0:!1;f=n._contextMenu.data("ejMenu");t(f.element).is(":visible")&&f.hide();n._isIframe(t(r))?n._bindBeforeOpen(n,t(r).contents().find("body")):n._bindBeforeOpen(n,t(r))},r.prototype._getErrorLength=function(n,i){var r=n._isIframe(i)?t(i).contents().find("body")[0]:t(i);return t(r).find(".errorspan").length},r.prototype._getElement=function(){for(var t=document.getElementsByTagName("span"),r=this._selectedValue,i=[],n=0;n<t.length;n++)t[n].textContent===r&&i.push(t[n]);return i},r.prototype._alertClose=function(){!ej.isNullOrUndefined(this._alertWindow)&&this._alertWindow.parents().find(".e-alertdialog").length>0&&(this._alertWindow.ejDialog("close"),this._alertWindow.parents().find(".e-alertdialog").remove(),this._close())},r.prototype._close=function(){var r,i,n,f,u,e;if(!ej.isNullOrUndefined(this._spellCheckWindow)&&this._spellCheckWindow.parents().find(".e-spellcheck.e-dialog-wrap").length>0){for(r=this._spellCheckWindow.find(".e-sentence .e-sentencecontent"),i=t(r[0]).find("span.errorspan"),n=0;n<i.length;n++)f=i[n].innerText||i[n].textContent,t(i[n]).replaceWith(f);if(this._updateTargetString(this),u=r.html(),e=ej.isNullOrUndefined(this.model.controlsToValidate)?{updatedText:u,requestType:"dialogClose"}:{updatedText:u,targetElement:this._currentTargetElement,requestType:"dialogClose"},this._trigger("dialogClose",e))return!1;this._spellCheckWindow.ejDialog("isOpen")&&this._spellCheckWindow.ejDialog("close");this._spellCheckWindow.parents().find(".e-spellcheck.e-dialog-wrap").remove();this._changeAllWords=[];ej.isNullOrUndefined(this.model.controlsToValidate)||(this._controlIds=this.model.controlsToValidate.split(","),this._subElements=[])}},r.prototype._changeErrorWord=function(n){var u=t("#"+this._id+"_Suggestions").ejListBox("option","value"),e=this._spellCheckWindow.find(".e-sentence .e-sentencecontent"),f=this._spellCheckWindow.find(".e-sentence .e-sentencecontent")[0].innerHTML,r=t(this._spellCheckWindow.find(".e-sentence .e-sentencecontent").find("."+this.model.misspellWordCss)[0]).text().trim(),i;u=u===this._localizedLabels.NoSuggestionMessage?r:u;n.model.text===this._localizedLabels.AddToDictionary?(this._currentElement=t(e),this.addToDictionary(r)):n.model.text===this._localizedLabels.IgnoreOnceButtonText?(i=this.ignore(r,f,null),i!==!1&&this._updateErrorWord(this,i,n,r,null,"ignore")):n.model.text===this._localizedLabels.IgnoreAllButtonText?(i=this.ignoreAll(r,f),i!==!1&&this._updateErrorWord(this,i,n,r,null,"ignoreAll")):n.model.text===this._localizedLabels.ChangeButtonText?(i=this.change(r,f,u,null),i!==!1&&this._updateErrorWord(this,i,n,r,u,"change")):n.model.text===this._localizedLabels.ChangeAllButtonText&&(i=this.changeAll(r,f,u),i!==!1&&this._updateErrorWord(this,i,n,r,u,"changeAll"))},r.prototype._convertData=function(n,t){for(var r=[],i=0;i<n.length;i++)t==="dictionaryData"?r.push({field:n[i]}):t==="menuData"&&r.push({id:n[i],text:n[i]});return r},r.prototype._updateErrorWord=function(n,i,r,u,f,e){var s=this._spellCheckWindow.find(".e-suggesteditems"),o,h,c;n._spellCheckWindow.find(".e-sentence .e-sentencecontent")[0].innerHTML=i.resultHTML;o=this._spellCheckWindow.find(".e-sentence .e-sentencecontent").find(".errorspan");o.length>0?n._targetUpdate(n,o,u,e,f):!ej.isNullOrUndefined(this.model.controlsToValidate)&&n._targetStatus?n._updateTargetText(n):(ej.isNullOrUndefined(this.model.controlsToValidate)||t(this._proElements).html(n._spellCheckWindow.find(".e-sentence .e-sentencecontent")[0].innerHTML),c=[this._localizedLabels.NoSuggestionMessage],s.ejListBox({selectedItemIndex:null}),s.ejListBox({dataSource:this._convertData(c,"dictionaryData"),selectedItemIndex:0}),h=t("#"+this._id+"_Suggestions").data("ejListBox"),h.refresh(),n._validationComplete(n,i.resultHTML))},r.prototype._targetUpdate=function(n,i,r,u,f){var e,c,s,o,h;if(u==="changeAll")for(e=0;e<i.length;e++)c=i[e].innerText||i[e].textContent,c===r&&t(i[e]).replaceWith(f);for(s=0;s<this.model.ignoreWords.length;s++)for(o=0;o<i.length;o++)h=i[o].innerText||i[o].textContent,h===n.model.ignoreWords[s]&&t(i[o]).replaceWith(h);n._listBoxDataUpdate(n)},r.prototype._updateTargetText=function(n){var e,i,r,o,u,s,f,h;if(n._updateTargetString(n),e=ej.isNullOrUndefined(n.model.controlsToValidate)?t("#"+n._id):t(n._proElements),n._proElements=t(n._subElements[0]),n._proElements.length>0||n._subElements.length>0){if(i=t(n._subElements[0]),n._currentTargetElement=i,r=t(i)[0].tagName==="TEXTAREA"||t(i)[0].tagName==="INPUT"?t(i)[0].value:t(i)[0].innerHTML,o={previousElement:e,currentElement:i,targetHtml:r,requestType:"updateText"},n._trigger("targetUpdating",o))return n._close(),!1;n._spellCheckWindow.find(".e-sentence .e-sentencecontent")[0].innerHTML=r;n._subElements=n._subElements.slice(1);u=n._spellCheckWindow.find(".e-sentence .e-sentencecontent");s=n._filteringDiffWords(n,u[0].innerText);n._suggestionsRequest(n,u,s.toString(),"validateByDialog")}else f=n._spellCheckWindow.find(".e-sentence .e-sentencecontent").find(".errorspan"),f.length===0&&n._subElements.length>0?n._updateTargetText(n):f.length>0?(h=n._spellCheckWindow.find(".e-sentence .e-sentencescroller").data("ejScroller"),h.refresh(),n._listBoxDataUpdate(n)):n._completionCheck(n)},r.prototype._updateTargetString=function(n){var i=ej.isNullOrUndefined(n.model.controlsToValidate)?t("#"+n._id):t(n._proElements),r;i.length>0&&(r=n._spellCheckWindow.find(".e-sentence .e-sentencecontent")[0].innerHTML,n._isIframe(i)?i.contents().find("body").html(r):ej.isNullOrUndefined(i[0].value)?i.html(r):i.val(r))},r.prototype._completionCheck=function(n){n._subElements=n._subElements.slice(1);n._subElements.length===0&&(n._targetStatus=!1);n._validationComplete(n,"")},r.prototype._changeAllErrors=function(n){for(var f,i,r=t(n._activeElement).find(".errorspan"),u=0;u<r.length;u++)for(f=r[u].innerText||r[u].textContent,i=0;i<n._changeAllWords.length;i++)f===n._changeAllWords[i].ErrorWord&&t(r[i]).replaceWith(n._changeAllWords[i].ReplaceWord)},r.prototype._setModel=function(n){var e=this,i,r,u,f;for(i in n)if(n.hasOwnProperty(i))switch(i){case"locale":this.model.locale=n[i];this._localizedLabels=ej.getLocalizedConstants("ej.SpellCheck",this.model.locale);break;case"misspellWordCss":if(this.model.misspellWordCss=n[i],this.model.contextMenuSettings.enable)if(ej.isNullOrUndefined(this.model.controlsToValidate))this._changeMisspellWordCss(this.element[0]);else for(r=0;r<this._controlIds.length;r++)this._changeMisspellWordCss(this._controlIds[r]);break;case"contextMenuSettings":t.extend(this.model.contextMenuSettings,n[i]);this.model.contextMenuSettings.enable?(this.validate(),this._renderControls()):(ej.isNullOrUndefined(this._contextMenu)||this._contextMenu.parent().remove(),this._removeSpan(this));break;case"ignoreSettings":t.extend(this.model.ignoreSettings,n[i]);this._ignoreStatus=!1;this._statusFlag=!0;this.model.contextMenuSettings.enable&&(this.validate(),this._renderControls());break;case"dictionarySettings":t.extend(this.model.dictionarySettings,n[i]);break;case"maxSuggestionCount":this.model.maxSuggestionCount=n[i];break;case"ignoreWords":this.model.ignoreWords=n[i];this.model.contextMenuSettings.enable&&this.validate();break;case"controlsToValidate":if(this.model.controlsToValidate=n[i],ej.isNullOrUndefined(this.model.controlsToValidate))for(t(this.element).attr("style","display:block"),u=0;u<this._controlIds.length;u++)f=t(this._controlIds[u]),f.removeClass("e-spellcheck"),f[0].spellcheck=!0,f[0].addEventListener("input",function(){e._statusFlag=!1},!1);this._renderControls();break;case"isResponsive":this.model.isResponsive=n[i];this._renderControls();break;case"enableValidateOnType":this.model.enableValidateOnType=n[i];this._renderControls()}},r.prototype._changeMisspellWordCss=function(n){var i=t(n).find("span.errorspan").attr("class").toString().split(" ")[1];t(n).find("span.errorspan").removeClass(i).addClass(this.model.misspellWordCss)},r.prototype._getModelValues=function(n,t,i){var r={ignoreAlphaNumericWords:n.model.ignoreSettings.ignoreAlphaNumericWords,ignoreEmailAddress:n.model.ignoreSettings.ignoreEmailAddress,ignoreHtmlTags:n.model.ignoreSettings.ignoreHtmlTags,ignoreMixedCaseWords:n.model.ignoreSettings.ignoreMixedCaseWords,ignoreUpperCase:n.model.ignoreSettings.ignoreUpperCase,ignoreUrl:n.model.ignoreSettings.ignoreUrl,ignoreFileNames:n.model.ignoreSettings.ignoreFileNames};return JSON.stringify({requestType:"checkWords",model:r,text:t,additionalParams:ej.isNullOrUndefined(i)?null:i.additionalParams})},r.prototype._getLocalizedLabels=function(){return ej.getLocalizedConstants(this.sfType,this.model.locale)},r.prototype._elementRightClick=function(n){if(!ej.isNullOrUndefined(this._contextMenu)&&!t(n.target).hasClass("e-menulink")){var i=this._contextMenu.data("ejMenu");ej.isNullOrUndefined(i)||t(i.element).is(":visible")&&i.hide()}},r}(ej.WidgetBase);ej.widget("ejSpellCheck","ej.SpellCheck",new i)})(jQuery);ej.SpellCheck.Locale=ej.SpellCheck.Locale||{};ej.SpellCheck.Locale["default"]=ej.SpellCheck.Locale["en-US"]={SpellCheckButtonText:"Spelling:",NotInDictionary:"Not in Dictionary:",SuggestionLabel:"Suggestions:",IgnoreOnceButtonText:"Ignore Once",IgnoreAllButtonText:"Ignore All",AddToDictionary:"Add to Dictionary",ChangeButtonText:"Change",ChangeAllButtonText:"Change All",CloseButtonText:"Close",CompletionPopupMessage:"Spell check is complete",CompletionPopupTitle:"Spell check",Ok:"OK",NoSuggestionMessage:"No suggestions available",NotValidElement:"Specify the valid control id or class name to spell check"}});
