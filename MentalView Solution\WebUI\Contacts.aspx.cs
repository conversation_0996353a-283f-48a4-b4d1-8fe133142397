﻿using Data;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebUI
{
    public partial class Contacts : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                ((Main)this.Master).ServerMessage.ButtonClicked += new ButtonClickedHandler(this.ServerMessageButtonClicked);
                ((Main)this.Master).PageTitle = GetLocalResourceObject("Page.Title").ToString();
                //Καλούμε την javascript Initialization() λόγω του UpdatePanel.
                ScriptManager.RegisterStartupScript(this, this.GetType(), "temp", "<script language='javascript'>Initialization();</script>", false);

                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                Int64 loggedEmailTemplateId = Convert.ToInt64(userData["UserId"]);
                string roleName = userData["Role"].ToString();

                if (roleName == "Guest")
                {
                    this.newContactBtn.Visible = false;
                }

                this.contactsPager.PageSize = 10;

                if (this.IsPostBack == false)
                {
                    this.FillContactsGrid();
                }
                else
                {
                    if (Request.Form["__EVENTTARGET"] == this.searchBtn.ID)
                    {
                        this.contactsPager.CurrentPage = 0;
                        this.FillContactsGrid();
                    }
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void FillContactsGrid()
        {
            try
            {
                Data.SortDirection sortDirection = Data.SortDirection.Ascending;
                string sortExpression = "";
                int totalResults = 0;
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                string roleName= userData["Role"].ToString();
                Int64 userId = Convert.ToInt64(userData["UserId"]);

                //ΣΗΜΕΙΩΣΗ: δεν βάζουμε το παρακάτω κώδικα να περάσει το sorting από το grid στο Stored Procedure γιατί το default sorting που κάνει το stored procedure είναι αυτό που κάνει και το grid.
                //if (this.contactsGrid.SortedColumns.Count > 0)
                //{
                //    sortExpression = this.contactsGrid.SortedColumns[0].Field;
                //    sortDirection = this.contactsGrid.SortedColumns[0].Direction == Syncfusion.JavaScript.SortOrder.Descending ? Data.SortDirection.Descending : Data.SortDirection.Ascending;
                //}

                MentalViewDataSet ds = null;
                //Αν ο χρήστης είναι διαχειριστής τότε βλέπει όλους τα Contacts
                if (roleName == "Admin")
                {
                    ds = Data.Business.ContactsBusiness.GetContacts(tenantId, null, null, this.filterTxtBox.Value, sortExpression, null, this.contactsPager.CurrentPage - 1 >= 0 ? this.contactsPager.CurrentPage - 1 : 0, this.contactsPager.PageSize, out totalResults);
                }
                else if (roleName == "User")
                {
                    ds = Data.Business.ContactsBusiness.GetContacts(tenantId, userId, null, this.filterTxtBox.Value, sortExpression, null, this.contactsPager.CurrentPage - 1 >= 0 ? this.contactsPager.CurrentPage - 1 : 0, this.contactsPager.PageSize, out totalResults);
                }
                else if (roleName == "Guest")
                {
                    //Εδώ περνάμε τη μεταβλητή userId στη παράμετρο guestId.
                    ds = Data.Business.ContactsBusiness.GetContacts(tenantId, null, userId, this.filterTxtBox.Value, sortExpression, null, this.contactsPager.CurrentPage - 1 >= 0 ? this.contactsPager.CurrentPage - 1 : 0, this.contactsPager.PageSize, out totalResults);
                }

                this.contactsGrid.DataSource = ds.Contacts;
                this.contactsGrid.DataBind();

                //Ρυθμίζει το paging
                this.contactsPager.TotalRecordsCount = totalResults;
                if (this.contactsPager.CurrentPage == 0)
                {
                    this.contactsPager.CurrentPage = 1;
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        protected void contactsGrid_ServerCommandButtonClick(object sender, Syncfusion.JavaScript.Web.GridEventArgs e)
        {
            try
            {
                if (e.EventType == "commandButtonClick")
                {
                    if (e.Arguments["commandType"].ToString() == "Edit")
                    {
                        string contactId = ((Dictionary<string, object>)e.Arguments["data"])["ContactId"].ToString();
                        Response.Redirect("~/Contact.aspx?ContactId=" + contactId);
                    }
                    else if (e.Arguments["commandType"].ToString() == "Delete")
                    {
                        Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                        string roleName = userData["Role"].ToString();
                        if (roleName == "Guest")
                        {
                            ((Main)this.Master).ServerMessage.ShowDialog(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteProhibitedMessage, ServerMessageButtons.Ok, true);
                        }
                        else
                        {
                            string contactId = ((Dictionary<string, object>)e.Arguments["data"])["ContactId"].ToString();
                            ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete", contactId);
                        }
                    }
                }

                this.FillContactsGrid();
            }
            catch (Exception exp)
            {
                if (exp.GetType() != typeof(ThreadAbortException))
                {
                    Data.ExceptionLogger.LogException(exp);
                    ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
                }
            }
        }

        protected void searchBtn_ServerClick(object sender, EventArgs e)
        {
            try
            {
                this.contactsPager.CurrentPage = 0;
                this.FillContactsGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void contactsPager_Change(object Sender, Syncfusion.JavaScript.Web.PagerChangeEventArgs e)
        {
            try
            {
                this.FillContactsGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void clearSearchBtn_ServerClick(object sender, EventArgs e)
        {
            try
            {
                this.contactsPager.CurrentPage = 0;
                this.filterTxtBox.Value = "";
                this.FillContactsGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void ServerMessageButtonClicked(object sender, ButtonClickedArgs args)
        {
            try
            {
                if (args.Action == "Delete")
                {
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        Int64 contactId = Int64.Parse(args.Tag);
                        Data.Business.ContactsBusiness.DeleteContact(contactId);
                    }
                }
                else if (args.Action == "SessionExpired")
                {
                    Response.Redirect("Default.aspx");
                }

                this.FillContactsGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void contactsGrid_ServerRecordDoubleClick(object sender, Syncfusion.JavaScript.Web.GridEventArgs e)
        {
            try
            {
                if (e.EventType == "recordDoubleClick")
                {
                    string contactId = ((Dictionary<string, object>)e.Arguments["data"])["ContactId"].ToString();
                    Response.Redirect("~/Contact.aspx?ContactId=" + contactId);
                }

                this.FillContactsGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }
    }
}