﻿using Data;
using Newtonsoft.Json;
using Syncfusion.JavaScript.Web;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebUI
{
    public partial class Partners : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                ((Main)this.Master).ServerMessage.ButtonClicked += new ButtonClickedHandler(this.ServerMessageButtonClicked);
                ((Main)this.Master).PageTitle = GetLocalResourceObject("Page.Title").ToString();
                //Καλούμε την javascript Initialization() λόγω του UpdatePanel.
                ScriptManager.RegisterStartupScript(this, this.GetType(), "temp", "<script language='javascript'>Initialization();</script>", false);

                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                Int64 loggedEmailTemplateId = Convert.ToInt64(userData["UserId"]);
                string roleName = userData["Role"].ToString();

                if (roleName == "Guest")
                {
                    Response.StatusCode = 404;
                    Response.End();
                }

                if (this.IsPostBack == false)
                {
                    this.FillPartnersGrid();
                }
                else
                {
                    //if (Request.Form["__EVENTTARGET"] == this.searchBtn.ID)
                    //{
                    //    this.FillPartnersGrid();
                    //}
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void FillPartnersGrid()
        {
            try
            {
                Data.SortDirection sortDirection = Data.SortDirection.Ascending;
                string sortExpression = "";
                int totalResults = 0;
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                string roleName = userData["Role"].ToString();
                Int64 userId = Convert.ToInt64(userData["UserId"]);

                MentalViewDataSet ds = null;

                ds = Data.Business.PartnersBusiness.GetAllPartnersOfTenant(tenantId);
                ViewState["Partners"] = ds;

                this.partnersGrid.DataSource = ds.Partners;
                this.partnersGrid.DataBind();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        //protected void contactsGrid_ServerCommandButtonClick(object sender, Syncfusion.JavaScript.Web.GridEventArgs e)
        //{
        //    try
        //    {
        //        if (e.EventType == "commandButtonClick")
        //        {
        //            if (e.Arguments["commandType"].ToString() == "Edit")
        //            {
        //                string contactId = ((Dictionary<string, object>)e.Arguments["data"])["ContactId"].ToString();
        //                Response.Redirect("~/Contact.aspx?ContactId=" + contactId);
        //            }
        //            else if (e.Arguments["commandType"].ToString() == "Delete")
        //            {
        //                string contactId = ((Dictionary<string, object>)e.Arguments["data"])["ContactId"].ToString();
        //                ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete", contactId);
        //            }
        //        }

        //        this.FillPartnersGrid();
        //    }
        //    catch (Exception exp)
        //    {
        //        if (exp.GetType() != typeof(ThreadAbortException))
        //        {
        //            Data.ExceptionLogger.LogException(exp);
        //            ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
        //        }
        //    }
        //}

        //protected void searchBtn_ServerClick(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        this.contactsPager.CurrentPage = 0;
        //        this.FillPartnersGrid();
        //    }
        //    catch (Exception exp)
        //    {
        //        Data.ExceptionLogger.LogException(exp);
        //        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
        //    }
        //}


        //protected void clearSearchBtn_ServerClick(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        this.contactsPager.CurrentPage = 0;
        //        this.filterTxtBox.Value = "";
        //        this.FillPartnersGrid();
        //    }
        //    catch (Exception exp)
        //    {
        //        Data.ExceptionLogger.LogException(exp);
        //        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
        //    }
        //}

        private void ServerMessageButtonClicked(object sender, ButtonClickedArgs args)
        {
            try
            {
                if (args.Action == "Delete")
                {
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        Int64 partnerId = Int64.Parse(args.Tag);
                        Data.Business.PartnersBusiness.DeletePartner(partnerId);
                    }
                }
                else if (args.Action == "SessionExpired")
                {
                    Response.Redirect("Default.aspx");
                }

                this.FillPartnersGrid();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void partnersGrid_ServerAddRow(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Partners"];

                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                MentalViewDataSet.PartnersRow partnersRow = ds.Partners.NewPartnersRow();
                partnersRow.TenantId = tenantId;
                partnersRow.FirstName = ((Dictionary<string, object>)e.Arguments["data"])["FirstName"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["FirstName"].ToString();
                partnersRow.LastName = ((Dictionary<string, object>)e.Arguments["data"])["LastName"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["LastName"].ToString();
                partnersRow.Mobile1 = ((Dictionary<string, object>)e.Arguments["data"])["Mobile1"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Mobile1"].ToString();
                partnersRow.Phone1 = ((Dictionary<string, object>)e.Arguments["data"])["Phone1"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Phone1"].ToString();
                partnersRow.Email1 = ((Dictionary<string, object>)e.Arguments["data"])["Email1"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Email1"].ToString();
                partnersRow.Address = ((Dictionary<string, object>)e.Arguments["data"])["Address"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Address"].ToString();
                partnersRow.City = ((Dictionary<string, object>)e.Arguments["data"])["City"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["City"].ToString();
                partnersRow.PostCode = ((Dictionary<string, object>)e.Arguments["data"])["PostCode"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["PostCode"].ToString();
                partnersRow.CompanyName = ((Dictionary<string, object>)e.Arguments["data"])["CompanyName"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["CompanyName"].ToString();
                ds.Partners.AddPartnersRow(partnersRow);

                Data.Business.PartnersBusiness.SavePartners(ref ds);

                ViewState["Partners"] = ds;
                this.partnersGrid.DataSource = ds.Partners;
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void partnersGrid_ServerEditRow(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Partners"];
                ViewState["Partners"] = ds;

                Int64 partnerId = Convert.ToInt64(((Dictionary<string, object>)e.Arguments["previousData"])["PartnerId"]);
                MentalViewDataSet.PartnersRow partnersRow = ds.Partners.FindByPartnerId(partnerId);

                partnersRow.FirstName = ((Dictionary<string, object>)e.Arguments["data"])["FirstName"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["FirstName"].ToString();
                partnersRow.LastName = ((Dictionary<string, object>)e.Arguments["data"])["LastName"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["LastName"].ToString();
                partnersRow.Mobile1 = ((Dictionary<string, object>)e.Arguments["data"])["Mobile1"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Mobile1"].ToString();
                partnersRow.Phone1 = ((Dictionary<string, object>)e.Arguments["data"])["Phone1"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Phone1"].ToString();
                partnersRow.Email1 = ((Dictionary<string, object>)e.Arguments["data"])["Email1"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Email1"].ToString();
                partnersRow.Address = ((Dictionary<string, object>)e.Arguments["data"])["Address"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Address"].ToString();
                partnersRow.City = ((Dictionary<string, object>)e.Arguments["data"])["City"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["City"].ToString();
                partnersRow.PostCode = ((Dictionary<string, object>)e.Arguments["data"])["PostCode"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["PostCode"].ToString();
                partnersRow.CompanyName = ((Dictionary<string, object>)e.Arguments["data"])["CompanyName"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["CompanyName"].ToString();

                Data.Business.PartnersBusiness.SavePartners(ref ds);
                this.partnersGrid.DataSource = ds.Partners;
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void partnersGrid_ServerDeleteRow(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Partners"];
                this.partnersGrid.DataSource = ds.Partners;

                string partnerId = ((Dictionary<string, object>)e.Arguments["data"])["PartnerId"].ToString();
                ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete", partnerId);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }
    }
}