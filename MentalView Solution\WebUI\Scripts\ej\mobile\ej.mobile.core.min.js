/*!
*  filename: ej.mobile.core.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){ej.mobUtil={cssUA:ej.userAgent?"-"+ej.userAgent().toString().toLowerCase()+"-":"",transform:ej.addPrefix("transform"),transition:ej.addPrefix("transition"),transitionProperty:ej.addPrefix("transitionProperty"),transformStyle:ej.addPrefix("transformStyle"),transitionDuration:ej.addPrefix("transitionDuration"),transformOrigin:ej.addPrefix("transformOrigin"),transitionTimingFunction:ej.addPrefix("transitionTimingFunction"),transitionDelay:ej.addPrefix("transitionDelay"),ease:{quadratic:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(n){return n*(2-n)}},circular:{style:"cubic-bezier(0.1, 0.57, 0.1, 1)",fn:function(n){return Math.sqrt(1- --n*n)}},back:{style:"cubic-bezier(0.175, 0.885, 0.32, 1.275)",fn:function(n){var t=4;return(n=n-1)*n*((t+1)*n+t)+1}},bounce:{style:"",fn:function(n){return(n/=1)<1/2.75?7.5625*n*n:n<2/2.75?7.5625*(n-=1.5/2.75)*n+.75:n<2.5/2.75?7.5625*(n-=2.25/2.75)*n+.9375:7.5625*(n-=2.625/2.75)*n+.984375}},elastic:{style:"",fn:function(n){var t=.22;return n===0?0:n==1?1:.4*Math.pow(2,-10*n)*Math.sin((n-t/4)*2*Math.PI/t)+1}}},angular:{defaultAppName:$("[ng-app]").attr("ng-app"),compile:function(n){var t=angular.element(document.querySelector("[ng-app],[data-ng-app]")||document).injector();t.invoke(["$compile","$rootScope",function(t,i){var r=angular.element(n.closest(".ng-scope")||n).scope();(r||i)&&t(n)(r||i);(r||i).$$phase||(r||i).$digest()}])}},angularMobileSettings:{enableAnimation:!0,animationTime:300},isAppNullOrUndefined:function(){return typeof App=="undefined"},device:{isIOS:function(){return/(ipad|iphone|ipod touch)/i.test(navigator.userAgent.toLowerCase())&&!this.isWindows()},isIOS7:function(){return/(ipad|iphone|ipod touch);.*os 7_\d|(ipad|iphone|ipod touch);.*os 8_\d/i.test(navigator.userAgent.toLowerCase())&&!this.isWindows()},isAndroid:function(){return/android/i.test(navigator.userAgent.toLowerCase())&&!this.isWindows()},isWindows:function(){return/trident|windows phone/i.test(navigator.userAgent.toLowerCase())},isFlat:function(){return ej.getBooleanVal($("head"),"data-ej-flat",!1)===!0}},isIOS:function(){if(!ej.getBooleanVal($("head"),"data-ej-windows")&&!ej.getBooleanVal($("head"),"data-ej-android")&&!ej.getBooleanVal($("head"),"data-ej-flat"))return this._ios()},isIOS7:function(){if(!ej.getBooleanVal($("head"),"data-ej-windows")&&!ej.getBooleanVal($("head"),"data-ej-android")&&!ej.getBooleanVal($("head"),"data-ej-flat"))return this._ios7()},isAndroid:function(){if(!ej.getBooleanVal($("head"),"data-ej-windows")&&!ej.getBooleanVal($("head"),"data-ej-ios")&&!ej.getBooleanVal($("head"),"data-ej-ios7")&&!ej.getBooleanVal($("head"),"data-ej-flat"))return this._android()},setTransition:function(){return ej.isAndroid()?"pop":ej.isWindows()?ej.isMobile()?"slide":"slide":"slide"},isLowerAndroid:function(){return/Android /.test(window.navigator.appVersion)&&!/Chrome\/\d/.test(window.navigator.appVersion)},getAndroidVersion:function(){return this._android()&&navigator.userAgent.toLowerCase().match(/android\s+([\d\.]+)/)?parseFloat(navigator.userAgent.toLowerCase().match(/android\s+([\d\.]+)/)[1]):!1},isWindows:function(){if(!ej.getBooleanVal($("head"),"data-ej-android")&&!ej.getBooleanVal($("head"),"data-ej-ios")&&!ej.getBooleanVal($("head"),"data-ej-ios7")&&!ej.getBooleanVal($("head"),"data-ej-flat"))return this._windows()},isFlat:function(){return ej.getBooleanVal($("head"),"data-ej-flat",!1)===!0},hasTheme:function(n){return n.hasClass("e-m-dark")||n.hasClass("e-m-light")||n.hasClass("e-m-default")},setTheme:function(n){n.model.renderMode=="windows"||ej.isMobile()||n.model.theme!="default"||(n.model.theme="auto");var t=ej.getAttrVal($("head"),"data-ej-theme","auto"),i=ej.getBooleanVal($("head"),"data-ej-windows-renderdefault",n.model.windows?n.model.windows.renderDefault:!1);n.model.windows&&(n.model.windows.renderDefault=i);n.model.theme=n.model.theme=="auto"?t=="auto"?n.model.renderMode=="ios7"||(n.model.renderMode=="android"||n.model.renderMode=="windows")&&!ej.isMobile()?"light":"dark":t:n.model.theme},getTheme:function(){return objTheme={model:{theme:"auto"}},this.setTheme(objTheme),objTheme.model.theme},hasRenderMode:function(n){return n.hasClass("e-m-ios7")||n.hasClass("e-m-android")||n.hasClass("e-m-windows")||n.hasClass("e-m-flat")},setRenderMode:function(n){n.model.renderMode=ej.getAttrVal(n.element,"data-ej-rendermode",n.model.renderMode);n.model.renderMode=="auto"&&(n.model.renderMode=ej.isAndroid()?"android":ej.isIOS()?"ios7":ej.isWindows()?"windows":ej.isFlat()?"flat":"ios7")},getRenderMode:function(){return this.isAndroid()?"android":this.isIOS()?"ios7":this.isWindows()?"windows":this.isFlat()?"flat":"ios7"},getCurrentPage:function(){return ej.isAppNullOrUndefined()?$("body"):App._renderingPage?App._renderingPage:App.activePage},browser:function(){return/webkit/i.test(navigator.appVersion)?"webkit":/firefox/i.test(navigator.userAgent)?"Moz":/trident/i.test(navigator.userAgent)?"ms":"opera"in window?"O":""},round:function(n,t,i){return t*(i?Math.ceil(n/t):Math.floor(n/t))},logBase:function(n,t){return Math.log(n)/Math.log(t)},correctRect:function(n,t,i,r){return{X:Math.min(n,i),Y:Math.min(t,r),Width:Math.abs(i-n),Height:Math.abs(r-t)}},measureText:function(n,t,i){var r=document.createElement("DIV"),u;return r.innerHTML=n,i!=null&&(r.style.font=i),r.style.backgroundColor="white",r.style.position="absolute",r.style.top=-100,r.style.left=0,t&&(r.style.maxwidth=t+"px"),document.body.appendChild(r),u={width:r.offsetWidth,height:r.offsetHeight},$(r).remove(),u},getTime:Date.now||(new Date).getTime(),getFontString:function(n){return n==null&&(n={}),n.FontFamily||(n.FontFamily="Arial"),n.FontStyle||(n.FontStyle=0),n.Size||(n.Size="12px"),ej.GetFontStyle(n.FontStyle)+" "+n.Size+" "+n.FontFamily},getFontStyle:function(n){switch(n){case 0:return"Regular";case 1:return"Bold";case 2:return"Italic";case 4:return"Underline";case 8:return"StrikeOut"}},hexFromRGB:function(n){var i=n.R,r=n.G,u=n.B,t=[i.toString(16),r.toString(16),u.toString(16)];return $.each(t,function(n,i){i.length===1&&(t[n]="0"+i)}),t.join("").toUpperCase()},adjustFixedElement:function(n){n.removeClass("e-m-adjheader e-m-adjfooter");var t=n.siblings(".e-m-navbar-top").length,i=n.siblings(".e-m-navbar-bottom").length;t&&n.addClass("e-m-adjheader-"+t);i&&n.addClass("e-m-adjfooter-"+i)},setCaretToPos:function(n,t,i){if(n.setSelectionRange&&n.type!="number")n.focus(),n.setSelectionRange(t,i);else if(n.createTextRange){var r=n.createTextRange();r.collapse(!0);r.moveStart("character",t);r.moveEnd("character",i);r.select()}},isCssCalc:function(){$("body").append('<div id="css3-calc" style=" width: 10px; width: calc(10px + 10px);display: none;"><\/div>');var n=$("#css3-calc").width();return $("#css3-calc").remove(),n==20?!0:!1},getLocation:function(n){var t=n?this.route.splitUrl(n):location,i=this.route.splitUrl(n||location.href).hash;return i=i==="#"?"":i,t.protocol+"//"+t.host+t.pathname+t.search+i},route:{urlSplitReg:/^\s*(((([^:\/#\?]+:)?(?:(\/\/)((?:(([^:@\/#\?]+)(?:\:([^:@\/#\?]+))?)@)?(([^:\/#\?\]\[]+|\[[^\/\]@#?]+\])(?:\:([0-9]+))?))?)?)?((\/?(?:[^\/\?#]+\/+)*)([^\?#]*)))?(\?[^#]+)?)(#.*)?/,splitUrl:function(n){if($.type(n)==="object")return n;var t=this.urlSplitReg.exec(n||"")||[];return{href:t[0]||"",protocol:t[4]||"",host:t[10]||"",pathname:t[13]||"",search:t[16]||"",hash:t[17]||""}}},resize:function(){$(window).bind("onorientationchange"in window?"orientationchange":"resize",function(n){(ej.getRenderMode()=="android"||ej.getRenderMode()=="ios7")&&$(document.activeElement).blur();ej._currentResolution!=ej.isLowerResolution()?(ej._currentResolution=ej.isLowerResolution(),$.extend(!0,n,{resolutionChanged:!0})):$.extend(!0,n,{resolutionChanged:!1});for(var t=0;t<ej.widget.registeredInstances.length;t++)$(ej.widget.registeredInstances[t].element).is(":visible")&&$.isFunction(ej.widget.registeredInstances[t].proto.resize)&&$(ej.widget.registeredInstances[t].element).data(ej.widget.registeredInstances[t].pluginName).resize(n);n.type=="orientationchange"&&window.setTimeout(function(){ej.getRenderMode()=="ios7"&&window.scrollTo(0,0)})})},initPage:function(){if(/(ipad|iphone|ipod touch);.*os 7_\d/i.test(navigator.userAgent.toLowerCase()))$(document).on("blur","input, select, textarea",function(){window.setTimeout(function(){document.activeElement.nodeName&&document.activeElement.nodeName.toLowerCase()!="input"&&window.scrollTo(0,0)})});$(window).bind("touchmove",function(n){$(n.target).closest(".e-m-scroll-native").length==0&&ej.isTouchDevice()&&ej.blockDefaultActions(n)})},_momentum:function(n,t,i,r,u){var o=.0006,e=Math.abs(n)/t,f=e*e/(2*o),h=0,s=0;return n>0&&f>i?(s=u/(6/(f/e*o)),i=i+s,e=e*i/f,f=i):n<0&&f>r&&(s=u/(6/(f/e*o)),r=r+s,e=e*r/f,f=r),f=f*(n<0?-1:1),h=e/o,{dist:f,time:Math.round(h)}},_nativeMomentum:function(n,t,i,r,u,f,e){var h=n-t,s=Math.abs(h)/i,o,c;return f=f==null?.0006:f,o=n+s*s/(2*(f/e))*(h<0?-1:1),c=s/f,o<r?(o=u?r-u/2.5*(s/8):r,h=Math.abs(o-n),c=h/s):o>0&&(o=u?u/2.5*(s/8):0,h=Math.abs(n)+o,c=h/s),{dest:Math.round(o),duration:c}},_setHidden:function(){var n=this;this.hidden.each(function(t){var r=n.tmp[t];for(var i in prop)this.style[i]=r[i]})},_removeHidden:function(n){var t=this;this.hidden=$(n).parents().andSelf().filter(":hidden");prop={visibility:"hidden",display:"block"};this.tmp=[];this.hidden.each(function(){var i={};for(var n in prop)i[n]=this.style[n],this.style[n]=prop[n];t.tmp.push(i)})},_transitionTime:function(n,t){n+="ms";t.style[ej.transitionDuration]=n},_slideOutWithoutDuration:function(n,t){ej._transitionTime(0,n);n.style[ej.transform]=t?"translateX(-100%)":"translateX(100%)"},_slideInWithoutDuration:function(n,t){ej._transitionTime(0,n);n.style[ej.transform]=t?"translateX(100%)":"translateX(-100%)"},_slideOutWithDuration:function(n,t){ej._transitionTime(300,n);n.style[ej.transform]=t?"translateX(-100%)":"translateX(100%)"},_slideInWithDuration:function(n,t){ej._transitionTime(100,n);n.style[ej.transform]=t?"translateX(100%)":"translateX(-100%)"},_slide:function(n){ej._transitionTime(100,n);n.style[ej.transform]="translateX(0)"},_device:function(){return/mobile|tablet|android|kindle/i.test(navigator.userAgent.toLowerCase())},_ios:function(){return/(ipad|iphone|ipod touch)/i.test(navigator.userAgent.toLowerCase())&&!this._windows()||ej.getBooleanVal($("head"),"data-ej-ios7",!1)===!0},_ios7:function(){return/(ipad|iphone|ipod touch);.*os 7_\d|(ipad|iphone|ipod touch);.*os 8_\d/i.test(navigator.userAgent.toLowerCase())&&!this._windows()||ej.getBooleanVal($("head"),"data-ej-ios7",!1)===!0},_android:function(){return/android/i.test(navigator.userAgent.toLowerCase())&&!this._windows()||ej.getBooleanVal($("head"),"data-ej-android",!1)===!0},_windows:function(){return/trident|windows phone|edge/i.test(navigator.userAgent.toLowerCase())||ej.getBooleanVal($("head"),"data-ej-windows",!1)===!0},_getFontString:function(n,t){return n.model.fontStyle[t.FontStyle]+" "+(t.Size==null?"11px":t.Size)+" "+t.FontFamily},_setScroller:function(n){n._wrpWd=n.element[0].clientWidth;n._scrollerWidth=n.element[0].scrollWidth;n._maxScrollX=n._wrpWd-n._scrollerWidth},_inputTouchStart:function(n,t){var i=n.touches?n.touches[0]:n;t._moved=!1;t._distX=0;t._x=t._x==undefined?t.element[0].scrollLeft:t._x;t._cloned=!1;t._startX=0;t._pointX=i.pageX;t._startTime=ej.getTime()},_inputTouchMove:function(n,t){var u=n.touches?n.touches[0]:n,i=u.pageX-t._pointX,r,f;(i>0&&(t._reverse=!0),t.element[0].scrollLeft==0&&i>0||i>0&&t.element[0].scrollLeft<i||i<0&&t.element[0].scrollLeftMax<=t.element[0].scrollLeft)||(i>0&&t.element[0].scrollLeft+i>t.element[0].scrollLeftMax&&(t._x<0&&(t._x=-t._x),i=-i),r=t._x+i,f=ej.getTime(),t._distX+=i,ej._translateText(t.element,r))},_inputTouchEnd:function(n,t){var r=n.touches?n.changedTouches[0]:n,i=r.pageX-t._pointX,u=i,f=ej.getTime();t._distX+=i;ej._translateText(t.element,t.element[0].scrollLeft);t._x=-t.element[0].scrollLeft},_translateText:function(n,t){n[0].scrollLeft=Math.abs(t)},_setGradientColor:function(n,t,i){var r=n;i.Name||typeof i=="string"?t.addColorStop(0,ej._getColor(i)):$.each(i,function(n,i){t.addColorStop(i.ColorStop!=NaN?i.ColorStop:0,typeof i.Color=="string"?i.Color:ej._getColor(i.Color))})},_pushValue:function(n,t,r){var u=[],r;for(i=0;i<r;i++)u[i]=n[i];for(u[r]=t,r=u.length,i=r;i<=n.length;i++)u[i]=n[i-1];return u}};$.extend(ej,ej.mobUtil);ej.mobile={};ej.mobile.enableRippleEffect=!0;ej.mobile.WaitingPopup={_init:function(n){this.model={text:"Loading...",target:$("body"),showOnInit:!1};$.extend(!0,this.model,n);this.createWaitingPopup()},createWaitingPopup:function(){if(!this.waitingPopupDiv){var n=ej.isMobile()?"mobile":"tablet";this.waitingPopupDiv=ej.buildTag("div.e-m-waitingpopup e-m-"+ej.getRenderMode()+" e-m-"+n,"<div class='e-m-image' style='top: "+(ej.isCssCalc()?"":window.innerHeight/2-16)+"px'><\/div>");this.waitingPopupDiv.find(".e-m-image").text(this.model.text);this.model.target.append(this.waitingPopupDiv);this.model.showOnInit||this.hide()}},setText:function(n){this.model.text=n;this.waitingPopupDiv.find(".e-m-image").text(n)},changeTarget:function(n){this.model.target=n;this.model.target.append(this.waitingPopupDiv)},show:function(){this.model.showOnInit=!0;this.waitingPopupDiv.addClass("e-m-show").removeClass("e-m-hide")},hide:function(){this.model.showOnInit=!1;this.waitingPopupDiv.removeClass("e-m-show").addClass("e-m-hide")}};$(function(){ej.initPage();ej._currentResolution=ej.isLowerResolution();ej.mobile.WaitingPopup._init();ej.mobile.enableRippleEffect&&!ej.isNullOrUndefined(window.ejAnimation)&&$("body").ejAnimation("rippleEffect");ej.resize()});ej.mobile.RenderMode={Auto:"auto",IOS7:"ios7",Android:"android",Windows:"windows"};ej.mobile.Theme={Auto:"auto",Dark:"dark",Light:"light"}});
