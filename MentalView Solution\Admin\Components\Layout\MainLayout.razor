﻿@inherits LayoutComponentBase
@using Microsoft.AspNetCore.Components.Authorization
@using Admin.Services

<div class="page">
    <div class="sidebar">
        <NavMenu />
    </div>

    <main>
        <div class="top-row px-4">
            <LoginLogout />
        </div>

        <article class="content px-4">
            @Body
        </article>
    </main>
</div>

<div id="blazor-error-ui" data-nosnippet>
    An unhandled error has occurred.
    <a href="." class="reload">Reload</a>
    <span class="dismiss">🗙</span>
</div>

<Syncfusion.Blazor.Popups.SfDialogProvider />