﻿CREATE TABLE [dbo].[Partners] (
    [PartnerId]   BIGINT        IDENTITY (1, 1) NOT NULL,
    [TenantId]    BIGINT        NOT NULL,
    [FirstName]   NVARCHAR (50) CONSTRAINT [DF_Partners_FirstName] DEFAULT ('') NOT NULL,
    [LastName]    NVARCHAR (50) CONSTRAINT [DF_Table_1_FirstName1] DEFAULT ('') NOT NULL,
    [Email1]      NVARCHAR (50) CONSTRAINT [DF_Table_1_FirstName1_1] DEFAULT ('') NOT NULL,
    [Phone1]      NVARCHAR (20) CONSTRAINT [DF_Table_1_FirstName1_2] DEFAULT ('') NOT NULL,
    [Mobile1]     NVARCHAR (20) CONSTRAINT [DF_Table_1_FirstName1_3] DEFAULT ('') NOT NULL,
    [Address]     NVARCHAR (50) CONSTRAINT [DF_Partners_Mobile11] DEFAULT ('') NOT NULL,
    [City]        NVARCHAR (30) CONSTRAINT [DF_Partners_Address1] DEFAULT ('') NOT NULL,
    [PostCode]    NVARCHAR (6)  CONSTRAINT [DF_Partners_Address1_1] DEFAULT ('') NOT NULL,
    [CompanyName] NVARCHAR (50) CONSTRAINT [DF_Partners_LastName1] DEFAULT ('') NOT NULL,
    CONSTRAINT [PK_Partners] PRIMARY KEY CLUSTERED ([PartnerId] ASC),
    CONSTRAINT [FK_Partners_Tenants] FOREIGN KEY ([TenantId]) REFERENCES [dbo].[Tenants] ([TenantId]) ON DELETE CASCADE ON UPDATE CASCADE
);

