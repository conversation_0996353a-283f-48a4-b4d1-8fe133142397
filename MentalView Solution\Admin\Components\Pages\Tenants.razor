﻿@page "/tenants"
@attribute [Authorize]

@using Admin.Business
@using Syncfusion.Blazor.Grids
@using Admin.Data.Model;
@using Syncfusion.Blazor.Popups

@inject SfDialogService dialogService
@inject ITenantsBusiness tenantsBusiness

<h3>@Resources.TenantsResources.Title</h3>

<SfGrid TValue="Tenant" DataSource="@this.tenants" AllowPaging="true" AllowSorting="true" >
    <GridColumns>
        <GridColumn Field=@nameof(Tenant.TenantId) HeaderText="ID" IsIdentity="true" IsPrimaryKey="true" Width="120">
        </GridColumn>
        <GridColumn Field=@nameof(Tenant.FullName) HeaderText="@Resources.TenantsResources.FullName" MinWidth="150"></GridColumn>
        <GridColumn Field=@nameof(Tenant.SubscriptionPlan) HeaderText="@Resources.TenantsResources.SubscriptionPlan" Width="150"></GridColumn>
        <GridColumn Field=@nameof(Tenant.LastLoggedUserFullName) HeaderText="@Resources.TenantsResources.LastLoggedUserFullName" Width="150"></GridColumn>
        <GridColumn Field=@nameof(Tenant.LastLoggedUserDate) HeaderText="@Resources.TenantsResources.LastLoggedUserDate" Type="ColumnType.DateTime" Format="dd/MM/yyyy HH:mm" Width="150"></GridColumn>
    </GridColumns>
</SfGrid>
