/*!
*  filename: ej.mobile.datepicker.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min","./ej.mobile.dialog.min","./ej.mobile.navigationbar.min","./ej.mobile.menu.min","./ej.mobile.scrollpanel.min"],n):n()})(function(){(function(n,t){t.widget("ejmDatePicker","ej.mobile.DatePicker",{_setFirst:!0,_requiresID:!0,_rootCSS:"e-m-datepicker",_isOpen:!1,defaults:{dateFormat:null,renderMode:"auto",minDate:null,maxDate:null,locale:"en-US",value:null,enabled:!0,enablePersistence:!1,cssClass:"",select:null,load:null,focusIn:null,focusOut:null,open:null,close:null,change:null},dataTypes:{renderMode:"enum",enabled:"boolean",enablePersistence:"boolean"},_init:function(){this._createDelegates();this._renderControl();this._wireEvents()},_datepickerInitialize:function(){this._winrt=this.model.renderMode=="windows"&&!t.isLowerResolution();this._menuTargetId=this._id;this._setLocale(this.model.locale);this.model.dateFormat=this.model.dateFormat==null?Date.format:this.model.dateFormat;this.model.minDate=this.model.minDate?this.model.minDate:"01/01/2000";this.model.maxDate=this.model.maxDate?this.model.maxDate:"12/31/2030";this.model.value||(date=new Date,this.model.value=date.getMonth()+1+"/"+date.getDate()+"/"+date.getFullYear());this._currentDate=new Date(this.model.minDate)>new Date(this.model.value)?this.model.minDate:new Date(this.model.maxDate)<new Date(this.model.value)?this.model.maxDate:this.model.value;this._prevResolution=t.isLowerResolution()},_setLocale:function(n){this.culture=t.preferredCulture(n);this._localizedLabels=this._getLocalizedLabels();this.culture&&(Date.dayNames=this.culture.calendar.days.names,Date.abbrDayNames=this.culture.calendar.days.namesAbbr,Date.monthNames=this.culture.calendar.months.names,Date.abbrMonthNames=this.culture.calendar.months.namesAbbr,Date.format=this.culture.calendar.patterns.d)},_renderControl:function(){t.setRenderMode(this);this._datepickerInitialize();this._datepickerwrapper=t.buildTag("div.e-m-datepickerwrapper "+this.model.cssClass+" e-m-"+this.model.renderMode+" e-m-user-select#"+this._id+"_Wrapper","",{},{});this.element.attr({name:this._id}).wrap(this._datepickerwrapper);this._datepickerwrapper=this.element.parent();this.element.attr({readonly:!0});this._outerdiv=t.buildTag("div.e-m-"+this.model.renderMode+" e-m-dp e-m-user-select #"+this._id+"_dp","",{},{});this._innerdiv=t.buildTag("div.e-m-dpinner","",{},{});this._wrapdiv=t.buildTag("div.e-m-dpwrap","",{},{});this._visibletexthtml=this._winrt?"":"<div class='col-fill'><\/div>";this._months=t.buildTag("div.e-m-dp e-m-dpmonth e-m-left","<div class='e-m-datewrapper'><div id="+this._id+"_dpmonth><div class='e-m-dpouter'>"+this._visibletexthtml+"<\/div><\/div><\/div><div class='e-m-dp-overlay'><\/div><\/div>",{},{});this._days=t.buildTag("div.e-m-dp e-m-dpdate e-m-center","<div class='e-m-datewrapper'><div id="+this._id+"_dpdate><div class='e-m-dpouter'>"+this._visibletexthtml+"<\/div><\/div><\/div><div class='e-m-dp-overlay'><\/div><\/div>",{},{});this._years=t.buildTag("div.e-m-dp e-m-dpyear e-m-right","<div class='e-m-datewrapper'><div id="+this._id+"_dpyear><div class='e-m-dpouter'>"+this._visibletexthtml+"<\/div><\/div><\/div><div class='e-m-dp-overlay'><\/div><\/div>",{},{});this["_"+this.model.renderMode+"Rendering"]();this._scrollInitialize();this._domElements();this._minDateInitialize();this._maxDateInitialize();this.model.load&&this._trigger("load",this._argsValue());this._controlStatus(this.model.enabled);this._initscroll=!1;this._setcurrent=!1;this._setformat=!1;this.element.addClass("e-m-user-select");this._setCurrentPreviousDate()},_setCurrentPreviousDate:function(){this._updateValue(this.model.dateFormat);this._previousDate=n(this.element).val()},_scrollPanel:function(i){this["_"+i]=t.buildTag("div#"+this._id+"dp"+i+"_scrollpanel");this._outerdiv.find("#"+this._id+"_dp"+i).append(this["_"+i]);this["_"+i].ejmScrollPanel({enableMouseWheel:!1,enableNativeScrolling:!1,enableTransition:!1,enableTransform:!1,showScrollbars:!1,target:this._id+"_dp"+i,targetHeight:this._targetHeight,renderMode:this.model.renderMode,scrollStart:n.proxy(this._onScrollStart,this),scrollStop:n.proxy(this._onScrollStop,this),enableBounce:!1,enableDisplacement:!0,displacementValue:this._displacement,displacementTime:t.isWindows()?800:350,isRelative:!0})},_windowsTargetHeight:function(){return this._closestAppview.innerHeight()-(t.getDimension(this._toolbar,"outerHeight")+t.getDimension(n(this._outerdiv).find(".e-m-dp-header"),"outerHeight"))},_scrollInitialize:function(){this._displacement=this.model.renderMode=="ios7"?40:this.model.renderMode=="android"?45:90;this._targetHeight=this.model.renderMode=="ios7"?200:this.model.renderMode=="android"?135:this._winrt?0:this._windowsTargetHeight();this._winrt||(this._scrollPanel("date"),this._scrollPanel("month"),this._scrollPanel("year"))},_minDateInitialize:function(){if(!this._winrt){this._minDate=new Date(this.model.minDate);var n=this._minDate.getDate(),t=this._minDate.getMonth();this._minMonths=this._monthtext.slice(0,t);this._minDates=this._datetext.slice(0,n-1)}},_maxDateInitialize:function(){if(!this._winrt){this._maxDate=new Date(this.model.maxDate);var n=this._maxDate.getDate(),t=this._maxDate.getMonth();this._maxMonths=this._monthtext.slice(t+1,12);this._maxDates=this._datetext.slice(n,31)}},_renderTemplate:function(){var t={};t["default"]="<div class='e-m-text {{:class}}'>{{:value}}<\/div>";t.monthtemplate="<div class='e-m-text'>{{:secondvalue}}<\/div>";t.windows="<div class='e-m-text {{:class}}'><div class='e-m-text-inner'>{{:value}}<\/div><div class='e-m-text-val e-m-dp-dayweek'>{{:secondvalue}}<\/div><\/div>";t.winrt="<option value='{{:value}}'>{{:value}}<\/option>";t.winrtmonths="<option value='{{:value}}'>{{:secondvalue}}<\/option>";n.templates(t)},_renderDateContent:function(){this._renderTemplate();this._monthsRendering();this._daysRendering();this._yearsRendering();this._innerdiv.append(this._wrapdiv);this._outerdiv.append(this._innerdiv)},_ios7Rendering:function(){this._renderDateContent();this._innerdiv.append("<div class='e-m-ios7-overlay'><\/div>");this._menu=t.buildTag("div#"+this._id+"_menu.e-m-dp-menu");this._menu.append(this._outerdiv);t.getCurrentPage().append(this._menu);this._menu.ejmMenu({allowScrolling:!1,type:t.isLowerResolution()?"actionsheet":"popover",target:this._menuTargetId,height:205,renderMode:this.model.renderMode,show:n.proxy(this._showDatePicker,this),showTitle:!1,cancelButton:{show:!1},hide:n.proxy(this._hideDatePicker,this)})},_androidRendering:function(){this._renderDateContent();this._dialog=t.buildTag("div.e-m-dp-dialog#"+this._id+"_dialog");this._dialog.ejmDialog({allowScrolling:!1,title:this._localizedLabels.headerText,mode:"confirm",leftButtonCaption:this._localizedLabels.confirmText,rightButtonCaption:this._localizedLabels.cancelText,renderMode:this.model.renderMode,enableAnimation:!1});this._dialog.find(".e-m-dlg-content").append(this._outerdiv)},_windowsRendering:function(){this.model.renderMode!="windows"||t.isLowerResolution()?(this._outerdiv.append("<div class='e-m-dp-header' style='visibility:hidden;'>"+this._localizedLabels.headerText+"<\/div>"),this._renderDateContent(),this._toolbar=t.buildTag("div#"+this._id+"_toolbar.e-m-dp-tb","<ul><li data-ej-iconname='check'><\/li><li data-ej-iconname='close'><\/li><\/ul>"),this._toolbar.ejmNavigationBar({mode:"toolbar",position:"bottom",renderMode:this.model.renderMode,touchEnd:n.proxy(this._winToolbarItem,this)}),this._closestAppview=this._outerdiv.closest(".appview").length?this._outerdiv.closest(".appview"):t.getCurrentPage(),t.getCurrentPage().append(this._outerdiv).append(this._toolbar),this._outerdiv.find(".e-m-dpouter").find(".col-fill").css("height",(this._windowsTargetHeight()-90)/2),this._outerdiv.css("top",-this._closestAppview.innerHeight()+"px"),this._toolbar.css({visibility:"hidden"})):(this._renderDateContent(),this._dialog=t.buildTag("div.e-m-dp-dialog#"+this._id+"_dialog"),this._dialog.ejmDialog({enableModal:!0,allowScrolling:!1,width:"100%",enableAnimation:!1,title:this._localizedLabels.headerText,leftButtonCaption:this._localizedLabels.confirmText,rightButtonCaption:this._localizedLabels.cancelText,mode:"confirm",renderMode:this.model.renderMode}),this._dialog.find(".e-m-dlg-content").append(this._outerdiv))},_flatRendering:function(){this._windowsRendering()},_daysRendering:function(){var t=[],n,i;for(this._daysofmonth=this._getDaysInMonth(new Date(this.model.value)),n=1;n<=31;n++)i=Date.dayNames[new Date(new Date(this.model.value).getMonth()+1+"/"+n+"/"+new Date(this.model.value).getFullYear()).getDay()],n<=this._daysofmonth?t.push({value:n,secondvalue:i,"class":""}):t.push({value:n,secondvalue:i,"class":"e-m-state-disabled"});this._columnRendering("days",t)},_winrtContent:function(i,r){this._select=t.buildTag("select");r=n.render[i=="months"?"winrtmonths":"winrt"](r);this["_"+i].find(".e-m-dpouter").html(this._select.append(r));this._wrapdiv.append(this["_"+i])},_monthsRendering:function(){for(var t=[],i=this.model.renderMode=="android"?"abbrMonthNames":"monthNames",n=0;n<12;n++)t.push({secondvalue:Date[i][n],value:n+1});this._columnRendering("months",t)},_yearsRendering:function(){for(var t=[],n=new Date(this.model.minDate).getFullYear();n<=new Date(this.model.maxDate).getFullYear();n++)t.push({value:n});this._columnRendering("years",t)},_columnRendering:function(t,i){if(this._winrt)this._winrtContent(t,i);else{var r=this.model.renderMode=="windows"||this.model.renderMode=="flat"?"windows":t=="years"?"default":t=="months"?"monthtemplate":"default";i=n.render[r](i)+this._visibletexthtml;this["_"+t].find(".e-m-dpouter").html(this["_"+t].find(".e-m-dpouter").html()+i);this._wrapdiv.append(this["_"+t])}},_createDelegates:function(){this._winScrlHndlr=n.proxy(this._outerScroll,this);this._focusHndlr=n.proxy(this._showDatePicker,this);this._blurHndlr=n.proxy(this._blurEventHandler,this);this._andBtnClkHndlr=n.proxy(this._androidDialogButtonClick,this);this._winrtBtnClkHndlr=n.proxy(this._winrtDialogButtonClick,this);this._OrientationChangeHndlr=n.proxy(this._OrientationChange,this);this._resizeHndlr=n.proxy(this._resize,this)},_wireEvents:function(i){this.model.renderMode=="android"?t.listenTouchEvent(n(this._dialog).find(".e-m-dlg-btn"),t.tapEvent(),this._andBtnClkHndlr,i):this._winrt?t.listenTouchEvent(n(this._dialog).find(".e-m-dlg-btn"),t.tapEvent(),this._winrtBtnClkHndlr,i):(this.model.renderMode=="windows"||this.model.renderMode=="flat")&&t.listenTouchEvent(n(this._outerdiv).find(".e-m-dpwrap .e-m-dp"),t.startEvent(),this._winScrlHndlr,i);t.listenEvents([n(this.element),n(this.element)],["focus","blur"],[this._focusHndlr,this._blurHndlr],i);t.listenTouchEvent(n(window),"onorientationchange"in window?"orientationchange":"resize",this._resizeHndlr,i)},_resize:function(){this._currentResolution=t.isLowerResolution();(this.model.renderMode=="ios7"||this.model.renderMode=="windows")&&this._prevResolution!=this._currentResolution?this._refresh():this._prevResolution=this._currentResolution;(this.model.renderMode=="windows"&&t.isLowerResolution()||this.model.renderMode=="flat")&&(this._outerdiv.find(".e-m-dpouter").find(".col-fill").css("height",(this._windowsTargetHeight()-90)/2),this._date.ejmScrollPanel({targetHeight:this._windowsTargetHeight()}),this._month.ejmScrollPanel({targetHeight:this._windowsTargetHeight()}),this._year.ejmScrollPanel({targetHeight:this._windowsTargetHeight()}),this._setValue(this._currentDate),this._outerdiv.offset().top!=0&&this._outerdiv.css("top",-this._closestAppview.innerHeight()))},_scrollCoordinates:function(){var n={};return n.Scrolldate=Math.round(this._date.ejmScrollPanel("getScrollPosition").y/this._displacement),n.Scrollmonth=Math.round(this._month.ejmScrollPanel("getScrollPosition").y/this._displacement),n.Scrollyear=Math.round(this._year.ejmScrollPanel("getScrollPosition").y/this._displacement),n.date=-n.Scrolldate+1,n.month=-n.Scrollmonth+1,n.year=-n.Scrollyear+new Date(this.model.minDate).getFullYear(),n},_domElements:function(){this._windate=this._days.find("#"+this._id+"_dpdate");this._winmonth=this._months.find("#"+this._id+"_dpmonth");this._winyear=this._years.find("#"+this._id+"_dpyear");this._datetext=this._windate.find(".e-m-text");this._monthtext=this._winmonth.find(".e-m-text");this._yeartext=this._winyear.find(".e-m-text")},_activeText:function(t,i,r,u){(u&&u=="dpdate"||u==null)&&(n(this._datetext).removeClass("e-m-text-active"),n(this._datetext[t-1]).addClass("e-m-text-active"));(u&&u=="dpmonth"||u==null)&&(n(this._monthtext).removeClass("e-m-text-active"),n(this._monthtext[i-1]).addClass("e-m-text-active"));(u&&u=="dpyear"||u==null)&&(n(this._yeartext).removeClass("e-m-text-active"),n(this._yeartext[r-new Date(this.model.minDate).getFullYear()]).addClass("e-m-text-active"))},_setDateInput:function(){this.model.renderMode!="ios7"&&this._updateValue(this.model.dateFormat);this._dateSelect()},_winrtInitialDate:function(n){var n=new Date(this._currentDate);this._windate.find("select option[value='"+n.getDate()+"']").attr("selected",!0);this._winmonth.find("select option[value='"+(n.getMonth()+1)+"']").attr("selected",!0);this._winyear.find("select option[value='"+n.getFullYear()+"']").attr("selected",!0)},_setInitialDate:function(){if(this._winrt)this._winrtInitialDate(this._currentDate);else{this.model.renderMode=="ios7"&&(this._initscroll=!0);this._setValue(this._currentDate);var n=this;setTimeout(function(){n._setProperDate(100)},200);this.model.renderMode=="ios7"&&(this._initscroll=!1)}},_setValue:function(n){var n=new Date(n);this._resetScrollPos();this._time=t.isIOS7()?!this._initscroll&&!this._setcurrent?200:0:0;this._date.ejmScrollPanel("scrollTo",0,-this._displacement*(n.getDate()-1),this._time);this._month.ejmScrollPanel("scrollTo",0,-this._displacement*n.getMonth(),this._time);this._year.ejmScrollPanel("scrollTo",0,-this._displacement*(n.getFullYear()-new Date(this.model.minDate).getFullYear()),this._time)},_resetScrollPos:function(){this._date.ejmScrollPanel("scrollTo",0,0,0);this._month.ejmScrollPanel("scrollTo",0,0,0);this._year.ejmScrollPanel("scrollTo",0,0,0)},_formatter:function(n,i){var r=this._checkFormat(i);return t.format(n,r,this.model.locale)},_parseDate:function(n){this._setformat||(this._oldFormat=this.model.dateFormat);var i=this._checkFormat(this._oldFormat);return t.parseDate(n,i,this.model.locale)},_checkFormat:function(n){var t=this,i=this._regExp();return n.replace(i,function(n){return n==="/"?t.culture.calendars.standard["/"]!=="/"?"'/'":n:n})},_changeFormat:function(n){var i=this,r=this._regExp();return n.replace(r,function(n){return n==="/"?i.culture.calendars.standard["/"]!=="/"?"'/'":n:n=="d"?new Date(i._currentDate).getDate():n=="M"?new Date(i._currentDate).getMonth()+1:t.format(new Date(i._currentDate),n)})},_regExp:function(){return/dddd|\/|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yy|HH|H|hh|h|mm|m|fff|ff|f|tt|ss|s|zzz|zz|z|gg|g|"[^"]*"|'[^']*'|[/]/g},_checkMinMax:function(n){if(this._outerdiv.find(".e-m-state-disabled").removeClass("e-m-state-disabled"),n.year==this._minDate.getFullYear()||n.year==this._maxDate.getFullYear()){var t=n.year==this._minDate.getFullYear()?"min":"max";this["_"+t+"Months"].addClass("e-m-state-disabled");(t=="min"?n.month<this._minDate.getMonth()+1:n.month>this._maxDate.getMonth()+1)?(n.month=this["_"+t+"Date"].getMonth()+1,this._month.ejmScrollPanel("scrollTo",0,-this._displacement*this["_"+t+"Date"].getMonth(),this._time)):(t=="min"?n.month==this._minDate.getMonth()+1:n.month==this._maxDate.getMonth()+1)&&(this["_"+t+"Dates"].addClass("e-m-state-disabled"),(t=="min"?n.date<this._minDate.getDate():n.date>this._maxDate.getDate())&&(n.date=this["_"+t+"Date"].getDate(),this._date.ejmScrollPanel("scrollTo",0,-this._displacement*(this["_"+t+"Date"].getDate()-1),this._time)))}return n},_setProperDate:function(t,i){var r=this._scrollCoordinates(),f,u;for(r=this._checkMinMax(r),f=this,(i=="dpmonth"||i=="dpyear")&&(this._daysofmonth=this._getDaysInMonth(new Date(r.month+"/1/"+r.year))),u=27;u<=30;u++)u+1>this._daysofmonth&&n(this._datetext[u]).addClass("e-m-state-disabled");this._daysofmonth<r.date&&(r.date=this._daysofmonth,this._date.ejmScrollPanel("scrollTo",0,-this._displacement*(this._daysofmonth-1),this._time));this.model.renderMode!="ios7"||this._initscroll||setTimeout(function(){n(f.element).val(f._formatter(new Date(r.month+"/"+r.date+"/"+r.year),f.model.dateFormat))},t);this._currentDate=r.month+"/"+r.date+"/"+r.year;this._activeText(r.date,r.month,r.year,i);this.model.value=this._currentDate;this.model.change&&this._trigger("change",this._argsValue())},_argsValue:function(){return{value:this._currentDate}},_onScrollStop:function(n){var t=n.model.target.replace(this._id+"_","");this._setProperDate(this.model.renderMode=="windows"?700:200,t);this["_"+this.model.renderMode+"ScrollStop"]&&this["_"+this.model.renderMode+"ScrollStop"](n.model.target,t,n)},_windowsScrollStop:function(t,i){for(var f,e,r=this._scrollCoordinates(),u=0;u<this._getDaysInMonth(new Date(r.month+"/"+r.date+"/"+r.year));u++)f=Date.dayNames[new Date(r.month+"/"+(u+1)+"/"+r.year).getDay()],n(this._datetext[u]).find(".e-m-dp-dayweek").html(f);e=this._id+"_"+this._target;this._hideWrapper(i)},_flatScrollStop:function(n,t,i){this._windowsScrollStop(n,t,i)},_onScrollStart:function(n){this._target=n.model.target.replace(this._id+"_","");this["_"+this.model.renderMode+"ScrollStart"]&&this["_"+this.model.renderMode+"ScrollStart"](n.model.target,this._target)},_windowsScrollStart:function(t,i){n("#"+t+"").find(".e-m-text.e-m-text-active").removeClass("e-m-text-active");this._hideWrapper(i)},_flatScrollStart:function(n,t){this._windowsScrollStart(n,t)},_hideWrapper:function(n){n&&n=="dpdate"&&(this._year.ejmScrollPanel("instance")._moved||this._winyear.closest(".e-m-datewrapper").removeClass("e-m-scrollstart"),this._month.ejmScrollPanel("instance")._moved||this._winmonth.closest(".e-m-datewrapper").removeClass("e-m-scrollstart"));n&&n=="dpmonth"&&(this._year.ejmScrollPanel("instance")._moved||this._winyear.closest(".e-m-datewrapper").removeClass("e-m-scrollstart"),this._date.ejmScrollPanel("instance")._moved||this._windate.closest(".e-m-datewrapper").removeClass("e-m-scrollstart"));n&&n=="dpyear"&&(this._month.ejmScrollPanel("instance")._moved||this._winmonth.closest(".e-m-datewrapper").removeClass("e-m-scrollstart"),this._date.ejmScrollPanel("instance")._moved||this._windate.closest(".e-m-datewrapper").removeClass("e-m-scrollstart"))},_blurEventHandler:function(){this.model.focusOut&&this._trigger("focusOut",this._argsValue());this.model.renderMode=="ios7"&&(this.model.select&&this._previousDate!=n(this.element).val()&&this._trigger("select",this._argsValue()),this._isOpen&&this._trigger("close",this._argsValue()))},_dateSelect:function(){this._previousDate!=n(this.element).val()&&this.model.select&&this._trigger("select",this._argsValue());this._previousDate=n(this.element).val();this._hideDatePicker()},_winToolbarItem:function(n){n.iconname=="check"?this._setDateInput():n.iconname=="close"&&this._hideDatePicker()},_winrtDialogButtonClick:function(t){n(t.target).text()==this._localizedLabels.confirmText?(this._currentDate=this._formatter(new Date(this._winmonth.find("select option:selected").val()+"/"+this._windate.find("select option:selected").val()+"/"+this._winyear.find("select option:selected").val()),this._setformat?this._oldFormat:this.model.dateFormat),this._setDateInput()):this._showHidePicker(!0)},_androidDialogButtonClick:function(t){n(t.target).text()==this._localizedLabels.confirmText?this._setDateInput():this._showHidePicker(!0)},_outerScroll:function(t){this._hideWrapper(n(t.target).parents(".e-m-scrollpanel")[0].id.replace(this._id+"_",""));n(t.target).closest(".e-m-dp").find(".e-m-datewrapper").addClass("e-m-scrollstart")},_showHidePicker:function(i){var u=this,f={currentTarget:this._datepickerwrapper},r=i?"hidden":"visible",e=t.getMaxZindex();this.element.blur();this._winrt?this._dialog.ejmDialog(i?"close":"open"):this.model.renderMode=="android"?this._dialog.ejmDialog(i?"close":"open"):(this.model.renderMode=="windows"||this.model.renderMode=="flat")&&(this._outerdiv.css({top:i?-this._closestAppview.innerHeight():"0px","visibility ":r}),n(this._outerdiv).find(".e-m-dp-header").css({visibility:r}),this._toolbar.css({visibility:r}))},_showDatePicker:function(){this._setInitialDate();this.model.focusIn&&this._trigger("focusIn",this._argsValue());this._showHidePicker();this._outerdiv.find(".e-m-datewrapper").removeClass("e-m-scrollstart");this._isOpen||(this._trigger("open",this._argsValue()),this._isOpen=!0)},_hideDatePicker:function(){this._showHidePicker(!0);this._isOpen&&(this._trigger("close",this._argsValue()),this._isOpen=!1)},_getDaysInMonth:function(n){return[31,this._isLeapYear(n.getFullYear())?29:28,31,30,31,30,31,31,30,31,30,31][n.getMonth()]},_isLeapYear:function(n){return n%4==0&&n%100!=0||n%400==0},_refreshScroller:function(){this._date.ejmScrollPanel("refresh");this._month.ejmScrollPanel("refresh");this._year.ejmScrollPanel("refresh")},_setModel:function(n){var i;for(var t in n)t=="renderMode"||t=="minDate"||t=="maxDate"||t=="locale"?i=!0:(setprop="_set"+t.charAt(0).toUpperCase()+t.slice(1),this[setprop]&&this[setprop](n[t]));i&&this._refresh()},_setEnabled:function(n){this._controlStatus(n)},_setDateFormat:function(n){this._setformat=!0;this._updateValue(n)},_updateValue:function(t){var i=this._changeFormat(t);n(this.element).val(i)},_controlStatus:function(n){this._datepickerwrapper[n?"removeClass":"addClass"]("e-m-state-disabled")},_refresh:function(){this._destroy();this.element.addClass("e-m-datepicker");this._renderControl();this._wireEvents()},_clearElement:function(){this.element.removeAttr("class").removeAttr("style");this.element.insertAfter(this._datepickerwrapper);this._datepickerwrapper.remove();this._dialog?(this._dialog.ejmDialog("instance")._destroy(),this._dialog.remove(),this._dialog=null):this._toolbar?(this._toolbar.ejmNavigationBar("instance")._destroy(),this._toolbar.remove(),this._toolbar=null):this._menu&&(this._menu.ejmMenu("instance")._destroy(),this._menu.remove(),this._menu=null);this.element=n(this.element);this._outerdiv.remove()},_destroy:function(){this._wireEvents(!0);this._clearElement()},_getLocalizedLabels:function(){return t.getLocalizedConstants(this.sfType,this.model.locale)},show:function(n){var n={currentTarget:this._datepickerwrapper};this.model.renderMode=="ios7"?this._menu.ejmMenu("show",n):this._showHidePicker()},hide:function(n){var n={currentTarget:this._datepickerwrapper};this.model.renderMode=="ios7"?this._menu.ejmMenu("hide",n):this._showHidePicker(!0)},enable:function(){this.model.enabled=!0;this._controlStatus(this.model.enabled);this._showHidePicker(!0)},disable:function(){this.model.enabled=!1;this._controlStatus(this.model.enabled);this._showHidePicker(!0)},getValue:function(){return n(this.element).val()},setCurrentDate:function(n){this._setcurrent=!0;this._currentDate=n;this._daysofmonth=this._getDaysInMonth(new Date(n));this._setCurrentPreviousDate();this._setcurrent=!1}});t.mobile.DatePicker.Locale=t.mobile.DatePicker.Locale||{};t.mobile.DatePicker.Locale["default"]=t.mobile.DatePicker.Locale["en-US"]={confirmText:"Done",cancelText:"Cancel",headerText:"CHOOSE DATE"}})(jQuery,Syncfusion)});
