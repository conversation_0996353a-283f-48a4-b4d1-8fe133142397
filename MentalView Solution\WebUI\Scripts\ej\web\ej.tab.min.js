/*!
*  filename: ej.tab.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function(n,t,i){t.widget("ejTab","ej.Tab",{_rootCSS:"e-tab",element:null,model:null,validTags:["div","span"],_addToPersist:["selectedItemIndex"],_setFirst:!1,angular:{terminal:!1},defaults:{collapsible:!1,enableAnimation:!0,ajaxSettings:{type:"GET",cache:!1,data:{},dataType:"html",contentType:"html",async:!0},disabledItemIndex:[],enabledItemIndex:[],hiddenItemIndex:[],events:"click",idPrefix:"ej-tab-",heightAdjustMode:"content",selectedItemIndex:0,cssClass:"",showCloseButton:!1,htmlAttributes:{},enableTabScroll:!1,showReloadIcon:!1,headerPosition:"top",width:null,height:null,headerSize:null,enableRTL:!1,allowKeyboardNavigation:!0,showRoundedCorner:!1,enablePersistence:!1,enabled:!0,ajaxLoad:null,ajaxBeforeLoad:null,ajaxSuccess:null,ajaxError:null,itemActive:null,beforeActive:null,itemAdd:null,itemRemove:null,beforeItemRemove:null,create:null,destroy:null},dataTypes:{cssClass:"string",collapsible:"boolean",events:"string",heightAdjustMode:"enum",enabled:"boolean",ajaxSettings:"data",disabledItemIndex:"data",enabledItemIndex:"data",enableAnimation:"boolean",htmlAttributes:"data"},observables:["selectedItemIndex"],selectedItemIndex:t.util.valueFunction("selectedItemIndex"),_destroy:function(){this._unWireEvents();this._removeBaseClass()},_setModel:function(i){for(var r in i)switch(r){case"events":this._off(this.items,this.model.events);this._on(this.items,i[r],this._tabItemClick);break;case"disabledItemIndex":this._disableItems(i[r]);i[r]=this.model.disabledItemIndex;break;case"enabledItemIndex":this._enableItems(i[r]);break;case"enabled":this._enabledAction(i[r]);break;case"selectedItemIndex":this._isInteraction=!1;this.showItem(t.util.getVal(i[r]));i[r]=this.model.selectedItemIndex;break;case"heightAdjustMode":this.model.heightAdjustMode=i[r];this._setTabsHeightStyle(i[r]);this._resizeEvents(i[r]);break;case"cssClass":this._changeSkin(i[r]);break;case"showRoundedCorner":this._roundedCorner(i[r]);break;case"height":this.model.height=i[r];this._setTabsHeightStyle(this.model.heightAdjustMode);break;case"width":this.element.width(i[r]);n(this.contentPanels).width(Number(i[r]));this.refreshTabScroll();break;case"headerSize":this._setHeaderSize(i[r]);break;case"allowKeyboardNavigation":i[r]?this._on(this.element,"keydown",this._keyPress):this._off(this.element,"keydown");break;case"headerPosition":this.model.headerPosition=i[r];this.model.headerPosition==t.Tab.Position.Top?(this._removeVerticalClass(),this._removeScroll(),this.itemsContainer.remove(),this.itemsContainer.insertBefore(this.element.find(">div").first()),this.element.find("div.e-active-content").removeClass("e-activebottom"),n(this.contentPanels).css("margin-top","0")):this.model.headerPosition==t.Tab.Position.Bottom?(this._removeVerticalClass(),this._removeScroll(),this.element.find("div.e-active-content").removeClass("e-activetop"),this.model.enableTabScroll?n(this.contentPanels).css("margin-top","0"):n(this.contentPanels).css("position","relative")):(this.model.headerPosition==t.Tab.Position.Left||this.model.headerPosition==t.Tab.Position.Right)&&(this._removeHeaderClass(),n(this.items).css("display",""),this._removeScroll());this._refresh();this.model.headerPosition==t.Tab.Position.Right&&this.element.css("position","");(this.model.headerPosition==t.Tab.Position.Left||this.model.headerPosition==t.Tab.Position.Right)&&this._on(this.element.find("div.e-chevron-circle-right"),"click",this._tabScrollClick);this.scrollstep=30;break;case"showCloseButton":i[r]?(this._addDeleteIcon(),this._on(this.element.find("div.e-close"),"click",this._tabDeleteClick)):this.element.find("div.e-close").remove();break;case"enableTabScroll":this.model.enableTabScroll=i[r];i[r]?(this._removeScroll(),this._addScroll(),this.model.headerPosition=="left"&&(this._refresh(),this._on(this.element.find("div.e-chevron-circle-right"),"click",this._tabScrollClick))):(this._removeScroll(),this.itemsContainer.removeAttr("style"),n(this.contentPanels).css("margin-top","0"));(this.model.headerPosition==t.Tab.Position.Left||this.model.headerPosition==t.Tab.Position.Right)&&(this._refresh(),this._on(this.element.find("div.e-chevron-circle-right"),"click",this._tabScrollClick),this.model.headerPosition!=t.Tab.Position.Right||this.model.enableTabScroll||this.element.css("margin-left",""));break;case"showReloadIcon":i[r]?this._addReloadIcon():this.element.find("div.e-reload").remove();break;case"enableRTL":this.model.enableRTL=i[r];this._removeScroll();this.itemsContainer.removeAttr("style");n(this.contentPanels).css("margin-top","0");this.element.find("ul").removeAttr("style");i[r]?this.element.addClass("e-rtl"):this.element.removeClass("e-rtl");this.model.enableTabScroll&&this._addScroll();this._refresh();this._on(this.element.find("div.e-chevron-circle-right"),"click",this._tabScrollClick);this.model.headerPosition==t.Tab.Position.Right&&this.model.enableRTL&&this.element.css("margin-left","");break;case"htmlAttributes":this._addAttr(i[r]);break;case"hiddenItemIndex":this.model.headerPosition==t.Tab.Position.Top||this.model.headerPosition==t.Tab.Position.Bottom?n(this.items).css("display","inline-block"):n(this.items).css("display","");this.model.hiddenItemIndex=i[r];this.model.hiddenItemIndex.length>0&&this._hiddenIndexItem(this.model.hiddenItemIndex)}},_removeScroll:function(){this.element.find("div.e-chevron-circle-right").remove();this.element.find("div.e-chevron-circle-left").remove()},_addScroll:function(){(this.model.headerPosition=="left"||this.model.headerPosition=="right"&&this._tabContentsHeight()>(this.element.width()||Number(this.model.height))||this.model.headerPosition=="top"||this.model.headerPosition=="bottom")&&this._checkScroll();this._addScrollIcon();this.refreshTabScroll()},_init:function(){this._addItemIndex=null;this.tabId=0;this._hiddenIndex=this.model.hiddenItemIndex;this._initialize();this._prevSize=this._getDimension(n(this.element).parent(),"height")},_changeSkin:function(n){this.model.cssClass!=n&&this.element.removeClass(this.model.cssClass).addClass(n)},_tabContentsWidth:function(){for(var r=this.itemsContainer.find("li").length,t=0,i=0;i<r;i++)t=t+n(this.itemsContainer.find("li")[i]).width();return t},_tabContentsHeight:function(){for(var r=this.itemsContainer.find("li").length,t=0,i=0;i<r;i++)t=t+n(this.itemsContainer.find("li")[i]).height();return t},_initialize:function(){this.initialRender=!0;this.element.attr("tabindex",0).attr("role","tablist");this._itemsRefreshing();n(this.anchors).addClass("e-link");this._preTabSelectedIndex=this._preTabIndex=-1;t.isNullOrUndefined(this.model.width)||this.element.width(this.model.width);t.isNullOrUndefined(this.model.height)||this.element.height(this.model.height);this._setTabPosition(this.model.headerPosition);this.model.showCloseButton&&this._addDeleteIcon();this.model.showReloadIcon&&this._addReloadIcon();this.model.showRoundedCorner&&this._roundedCorner(this.model.showRoundedCorner);this._enabledAction(this.model.enabled);this.contentPanels=[];this._addAttr(this.model.htmlAttributes);this._reinitialize();this._addBaseClass();t.isNullOrUndefined(this.model.headerSize)||this._setHeaderSize(this.model.headerSize);this._disableTabs();this._roundedCorner(this.model.showRoundedCorner);this.model.enableTabScroll&&(this.model.headerPosition=="top"||this.model.headerPosition=="bottom"?this.itemsContainer.width()>this.element.width()&&this._addScrollIcon():this.element.height()<this.items.height()*this.items.length&&this._addScrollIcon());this._wireEvents(this.model.events);this.showItem(this.selectedItemIndex());this._setTabsHeightStyle(this.model.heightAdjustMode);this._enabledAction(this.model.enabled);this._resizeEvents(this.model.heightAdjustMode);this.selectedItemIndex()>-1&&this.contentPanels!=0&&this.model.enableRTL&&n(this.element).height(this.itemsContainer.height()+parseInt(n(this.contentPanels).css("height")));this.model.hiddenItemIndex.length>0&&this._hiddenIndexItem(this.model.hiddenItemIndex)},_reinitialize:function(t){for(var f,r,e,o,u=this._addItemIndex!=null?this._addItemIndex:0;u<this.anchors.length;u++)if(f=this.anchors[u],r=this.divId==i?n(f).attr("href"):this.divId,this.divId=i,e=r.split("#")[0],e&&e===location.toString().split("#")[0]&&(r=o.hash,f.href=r),r&&r!=="#"?(this._addContentTag(r,u),t&&this._addContentBaseClass(n(this.contentPanels[u]))):this.model.enablePersistence||this.model.disabledItemIndex.push(u),this._addItemIndex!=null){this._unWireEvents();this._wireEvents(this.model.events);this.items.length==1&&this.showItem(this.selectedItemIndex());break}},_itemsRefreshing:function(){if(this.initialRender)this.itemsContainer=this.element.find("ol,ul").eq(0);else for(var n=0;n<this.element.find("ol,ul").length;){if(this.element.find("ol,ul")[n].classList.contains("e-header")){this.itemsContainer=this.element.find("ol,ul").eq(n);break}n++}this.items=this.itemsContainer.find(" > li:has(a[href])");this.anchors=this.items.find("a[href]")},_setHeaderSize:function(n){this.element.find(">ul li.e-item").css("height","auto");this.element.find(">ul li.e-item").children("a.e-link").css("margin-top","0px");this.model.headerPosition=="left"?this.element.find(">ul.e-left").css({width:n,"text-align":"center"}):this.model.headerPosition=="right"?this.element.find(">ul.e-right").css({width:n,"text-align":"center"}):(this.element.find(">ul.e-header li.e-item").css("height",n),this.element.find(">ul.e-header li.e-item a.e-link").css("margin-top",(this.element.find(">ul.e-header").outerHeight()/2-this.element.find(">.e-header li.e-item a.e-link").outerHeight()).toString()+"px"),this.element.find(">ul.e-header li.e-item .e-icon.e-tabdelete").css("margin-top",(this.element.find(">ul.e-header").outerHeight()/2-this.element.find(">.e-header li.e-item .e-icon.e-tabdelete").outerHeight()-5).toString()+"px"),this.element.find(">ul.e-header li.e-item .e-icon.e-reload").css("margin-top",(this.element.find(">ul.e-header").outerHeight()/2-this.element.find(">.e-header li.e-item .e-icon.e-reload").outerHeight()-5).toString()+"px"))},_enabledAction:function(n){n?this.element.removeClass("e-disable"):this.element.addClass("e-disable")},_hiddenIndexItem:function(t){for(var r,i=0;i<t.length;i++)!n.inArray(parseInt(t[i]),this._hiddenIndex)>-1&&(r=n(this.items[parseInt(t[i])]).children("a").attr("href"),this._hidePanel(r));this._hideContentPanel(this.selectedItemIndex(),this.model.hiddenItemIndex);this._hiddenIndex=this.model.hiddenItemIndex},_hidePanel:function(t){for(var r,i=0;i<this.contentPanels.length;i++)if("#"+n(this.contentPanels[i]).attr("id")==t){n(this.contentPanels[i]).css("display","none");break}for(r=0;r<this.items.length;r++)if(n(this.items.children("a")[r]).attr("href")==t){n(this.items[r]).css("display","none");break}},_hideContentPanel:function(t,i){n.inArray(t,i)>-1?(t+=1,t<=this.items.length-1?this._hideContentPanel(t,i):t>this.items.length-1&&i.length!=this.items.length&&this._hideContentPanel(0,i)):this.showItem(t)},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.element.addClass(n):t=="disabled"&&n=="disabled"?(i.model.enabled=!1,i._enabledAction(!1)):i.element.attr(t,n)})},_setTabPosition:function(i){i==t.Tab.Position.Bottom?(this.itemsContainer.appendTo(this.element),this.items.removeClass("e-bottom-line"),this.items.addClass("e-top-line")):i==t.Tab.Position.Top?(this.items.removeClass("e-top-line"),this.itemsContainer.prependTo(this.element),this.model.enableRTL?this.items.addClass("e-rtl-top-line e-top-hover"):this.items.addClass("e-bottom-line")):(i==t.Tab.Position.Left||i==t.Tab.Position.Right)&&this.items.length>=0&&(this.model.height?this.itemsContainer.css("height",this.model.height):!1&&n(this.itemsContainer).css("height",""),this.element.addClass("e-vertical"))},_addDeleteIcon:function(){var n,i;this.element.find("div.e-close.e-tabdelete").length<=0&&this.items.length>0&&(n=t.buildTag("div.e-icon e-close e-tabdelete","",{},{role:"presentation"}).css("visibility","hidden"),this.model.headerPosition=="left"||this.model.headerPosition=="right"?(i=this.items.find("a"),n.insertBefore(i)):this.items.append(n))},_tabScrollIconCalc:function(){this.padding={left:Number(this.element.css("padding-left").split("px")[0]),right:Number(this.element.css("padding-right").split("px")[0]),top:Number(this.element.css("padding-top").split("px")[0]),bottom:Number(this.element.css("padding-bottom").split("px")[0])};this.scrollPosition=this.itemsContainer.width()/2-this._rightScrollIcon.width();this.rightScroll=t.getDimension(this.element,"width")-this.itemsContainer.outerWidth()/2},_addScrollIcon:function(){if(this.element.addClass("e-tabscroll").css({position:"relative",overflow:"hidden"}),this.element.find("div.e-chevron-circle-right").length<=0&&this.items.length>0){this._rightScrollIcon=t.buildTag("div.e-icon e-chevron-circle-right","",{},{role:"presentation"}).css("visibility","hidden");this.itemsContainer.after(this._rightScrollIcon);this.scrollstep=30;this._tabScrollIconCalc();this._rightScrollIcon.css("position","absolute");this.model.enableRTL||(this.model.headerPosition=="left"?(this._rightScrollIcon.css("top",this.padding.top+20+"px"),this._rightScrollIcon.css("left",this.scrollPosition+this.padding.left+"px")):this.model.headerPosition=="right"?(this._rightScrollIcon.css({"margin-left":this.itemsContainer.width()/2-this.scrollstep+"px","z-index":"15",top:this.padding.top+20+"px",left:this.rightScroll+"px"}),this.element.css({position:"relative"})):this.model.enablePersistence==!0&&this._beforeWidth!=0&&this._beforeWidth>this.scrollPanelWidth?this._rightScrollIcon.css("margin-right",this.itemsContainer.width()-this.scrollPanelWidth+20-(this.items[this.selectedItemIndex()].offsetLeft-this.scrollPanelWidth)*2+"px"):this._rightScrollIcon.css("left",this.scrollPanelWidth-(this.scrollstep+20)+this.padding.left+"px"));this.model.headerPosition=="left"||this.model.headerPosition=="right"?(this._rightScrollIcon.css("transform","rotate(270deg)"),t.browserInfo().name=="msie"&&parseInt(t.browserInfo().version)<11?this.model.enableRTL?this._rightScrollIcon.css("top",this.padding.top+20+"px"):this._rightScrollIcon.css("top",this.padding.top+20+"px"):this.model.enablePersistence==!0&&this._beforeWidth!=0&&this._beforeWidth>this.scrollPanelHeight?this._rightScrollIcon.css("margin-top","-"+(this.items[this.selectedItemIndex()].offsetTop*3-this.scrollPanelHeight*2)+"px"):this.model.enableRTL&&(this.model.headerPosition=="left"?this._rightScrollIcon.css({left:"0px","margin-left":this.scrollPosition+this.padding.left+"px","z-index":"15"}):this._rightScrollIcon.css({right:this.itemsContainer.outerWidth()/2+this.padding.right+"px","z-index":"15"}))):this.model.enableRTL&&this._rightScrollIcon.css("margin-left",this.itemsContainer.width()-14+"px");t.browserInfo().name=="msie"&&parseInt(t.browserInfo().version)<11&&this.model.headerPosition=="bottom"&&(this.model.enableRTL?this._rightScrollIcon.css("top",this.itemsContainer.height()/2+20+"px"):this._rightScrollIcon.css("top",this.itemsContainer.height()/2+27+"px"));this.element.attr("unselectable","on");this.element.css("user-select","none");this.element.on("selectstart",!1);this._on(this.element.find("div.e-chevron-circle-right"),"click",this._tabScrollClick);this._on(this.element.find("div.e-chevron-circle-right"),"mouseover",this._hoverHandler);this._on(this.element.find("div.e-chevron-circle-right"),"mouseout",this._hoverHandler)}},_addScrollBackIcon:function(){if(this.element.find("div.e-chevron-circle-left").length<=0){this._leftScrollIcon=t.buildTag("div.e-icon e-chevron-circle-left","",{},{role:"presentation"}).css("visibility","hidden");this.itemsContainer.before(this._leftScrollIcon);this.rightscrollstep=30;this.element.attr("unselectable","on");this.element.css("user-select","none");this.element.on("selectstart",!1);this._leftScrollIcon.css("position","absolute").css("z-index","10");(this.model.headerPosition=="top"||this.model.headerPosition=="bottom")&&this.model.enableRTL&&this._leftScrollIcon.css("left",this.padding.left+this.rightscrollstep+"px");(this.model.headerPosition=="left"||this.model.headerPosition=="right")&&(this.model.enableRTL?(this._leftScrollIcon.css("top",this.scrollPanelHeight+"px"),this.model.headerPosition=="right"?this._leftScrollIcon.css({right:this.itemsContainer.outerWidth()/2+this.padding.right+"px","margin-left":this.scrollPosition+this.padding.left+"px","z-index":"15"}):this._leftScrollIcon.css({left:"0px","margin-left":this.scrollPosition+this.padding.left+"px"})):this.model.headerPosition=="right"?(this._leftScrollIcon.css({"margin-left":this.itemsContainer.width()/2-30+"px","z-index":"15",left:this.rightScroll+"px"}),this._leftScrollIcon.css("top",this.scrollPanelHeight+30+"px")):this.model.headerPosition=="left"&&(this._leftScrollIcon.css("left",this.scrollPosition+this.padding.left+"px"),this._leftScrollIcon.css("top",this.scrollPanelHeight-20+this.padding.top+"px")),this._leftScrollIcon.css("transform","rotate(270deg)"));t.browserInfo().name=="msie"&&parseInt(t.browserInfo().version)<11&&this.model.headerPosition=="bottom"&&this._leftScrollIcon.css("top",this.itemsContainer.height()/2+27+"px");this._on(this._leftScrollIcon,"click",this._tabScrollBackClick);this._on(this.element.find("div.e-chevron-circle-left"),"mouseout",this._hoverHandler);this._on(this.element.find("div.e-chevron-circle-left"),"mouseover",this._hoverHandler)}},_addReloadIcon:function(){var n,i;this.element.find("div.e-reload").length<=0&&this.items.length>0&&(n=t.buildTag("div.e-icon e-reload","",{},{role:"presentation"}).css("visibility","hidden"),this.model.headerPosition=="left"||this.model.headerPosition=="right"?(i=this.items.find("a"),n.insertBefore(i)):this.items.append(n))},_addBaseClass:function(){this.element.addClass("e-widget "+this.model.cssClass);this.itemsContainer.addClass("e-box");this.model.enableRTL&&this.element.addClass("e-rtl");this.model.headerPosition=="top"&&(n(this.contentPanels).addClass("e-hidebottom e-addborderbottom"),n(this.itemsContainer).addClass("e-addborderbottom"),n(this.contentPanels).removeClass("e-hidetop e-addbordertop e-hideright e-addborderright e-hideleft e-addborderleft"),n(this.itemsContainer).removeClass("e-addbordertop e-addborderright e-addborderleft"),this.items.length>0&&this.itemsContainer.addClass("e-header"),this.model.enableRTL&&(this.items.addClass("e-rtl-top-line"),this.items.removeClass("e-rtl-bottom-line")));this.model.headerPosition=="bottom"&&(n(this.contentPanels).removeClass("e-hidebottom e-addborderbottom e-hideright e-addborderright e-hideleft e-addborderleft"),n(this.itemsContainer).removeClass("e-addborderbottom e-addborderright e-addborderleft"),n(this.contentPanels).addClass("e-hidetop e-addbordertop"),n(this.itemsContainer).addClass("e-addbordertop"),this.items.length>0&&this.itemsContainer.addClass("e-header"),this.model.enableRTL&&(this.items.addClass("e-rtl-bottom-line"),this.items.removeClass("e-rtl-top-line e-top-line")));this.model.headerPosition=="left"&&(this.items.length>0&&this.itemsContainer.addClass("e-left"),n(this.contentPanels).removeClass("e-hidetop e-addbordertop e-hidebottom e-addborderbottom e-hideright e-addborderright"),n(this.itemsContainer).removeClass("e-addbordertop e-addborderbottom e-addborderright"),n(this.contentPanels).addClass("e-hideleft e-addborderleft"),n(this.itemsContainer).addClass("e-addborderleft"),n(this.items).removeClass("e-rtl-bottom-line e-rtl-top-line"));this.model.headerPosition=="right"&&(this.items.length>0&&this.itemsContainer.addClass("e-right"),n(this.contentPanels).removeClass("e-hidetop e-addbordertop e-hidebottom e-addborderbottom e-hideleft e-addborderleft"),n(this.itemsContainer).removeClass("e-addbordertop e-addborderbottom e-addborderleft"),n(this.contentPanels).addClass("e-hideright e-addborderright"),n(this.itemsContainer).addClass("e-addborderright"),this.model.enableTabScroll&&this._tabContentsHeight()>(this.element.height||Number(this.model.height))&&n(this.itemsContainer).css("z-index","12").css("margin-left","-"+this.itemsContainer.find("li").width()+"px"),n(this.items).removeClass("e-rtl-bottom-line e-rtl-top-line"));this.items.addClass("e-select e-item").attr("role","tab").attr("tabindex",-1).attr("aria-expanded",!0).attr("aria-selected",!1);n(this.contentPanels).addClass("e-content  e-content-item e-box").attr("role","tabpanel").attr("aria-hidden",!0);((this.model.headerPosition=="left"||this.model.headerPosition=="right")&&this._tabContentsHeight()>(this.element.height()||Number(this.model.height))||(this.model.headerPosition=="top"||this.model.headerPosition=="bottom")&&this._tabContentsWidth()>(t.getDimension(this.element,"width")||this.model.width))&&this._checkScroll()},_addContentBaseClass:function(n){this.model.headerPosition=="top"&&(n.addClass("e-hidebottom e-addborderbottom"),n.removeClass("e-hidetop e-addbordertop e-hideright e-addborderright e-hideleft e-addborderleft"));this.model.headerPosition=="bottom"&&(n.removeClass("e-hidebottom e-addborderbottom e-hideright e-addborderright e-hideleft e-addborderleft"),n.addClass("e-hidetop e-addbordertop"));this.model.headerPosition=="left"&&(n.removeClass("e-hidetop e-addbordertop e-hidebottom e-addborderbottom e-hideright e-addborderright"),n.addClass("e-hideleft e-addborderleft"));this.model.headerPosition=="right"&&(n.removeClass("e-hidetop e-addbordertop e-hidebottom e-addborderbottom e-hideleft e-addborderleft"),n.addClass("e-hideright e-addborderright"));n.addClass("e-content  e-content-item e-box").attr("role","tabpanel").attr("aria-hidden",!0)},_checkScroll:function(){var i,r,u,f;if(this.scrollPanelWidth=t.getDimension(this.element,"width"),this.scrollPanelHeight=t.getDimension(this.element,"height"),this.model.enableTabScroll==!0&&this._tabContentsHeight()>this.items.height()){if(this.scrollstep=0,this.model.enableTabScroll=!0,(this.model.headerPosition=="top"||this.model.headerPosition=="bottom")&&this.itemsContainer.css({width:t.getDimension(this.element,"width")+parseInt(this.items.css("width"))*this.items.length+"px",position:"absolute"}),this._beforeWidth=0,this.model.enablePersistence==!0)if(this.model.headerPosition=="top"||this.model.headerPosition=="bottom")for(i=0;i<this.selectedItemIndex();i++)this._beforeWidth+=this.items[i].offsetWidth;else for(i=0;i<this.selectedItemIndex();i++)this._beforeWidth+=this.items[i].offsetHeight;r=parseInt(this.itemsContainer.css("width"));this.model.headerPosition=="top"&&(u=n(this.contentPanels),u.css("padding-top",this.itemsContainer.outerHeight()+(u.hasClass("e-activetop")?0:this.model.enableRTL?4:3)+"px"),n(this.contentPanels).css({"border-top":"none",width:t.getDimension(this.element,"width")-1+"px"}),this.itemsContainer.css("border-bottom","1px solid #bbbcbb"));this.model.headerPosition=="bottom"&&(n(this.contentPanels).css({position:"relative",width:t.getDimension(this.element,"width")-1+"px","border-bottom":"none"}),this.itemsContainer.css("border-top","1px solid #bbbcbb"),n(this.contentPanels).css({"border-top":""}));f=parseInt(r-(this.scrollPanelWidth+this.scrollstep-1));this.model.enableRTL&&(this.model.headerPosition=="top"||this.model.headerPosition=="bottom")?this.itemsContainer.css("clip","rect(0px,"+r+"px,100px,"+f+"px)"):this.model.headerPosition=="left"?this._tabContentsHeight()>Number(this.model.height.split("px")[0])&&(this.itemsContainer.css({height:parseInt(this.itemsContainer.css("height"))+parseInt(this.items.css("height"))*this.items.length+30+"px",position:"absolute","border-right":"1px solid #bbbcbb",background:"white"}),n(this.contentPanels).css("padding-left",this.itemsContainer.width()+5+"px"),this.model.enableRTL&&this.itemsContainer.css("margin-right",t.getDimension(this.element,"width")-this.itemsContainer.width()),this._beforeWidth==0||this._beforeWidth<this.scrollPanelHeight?this.itemsContainer.css("clip","rect(0px,"+(this.itemsContainer.width()+4)+"px,"+this.scrollPanelHeight+"px,"+this.scrollstep+"px)"):this._beforeWidth>this.scrollPanelHeight&&this.itemsContainer.css({clip:"rect("+(this.items[this.selectedItemIndex()].offsetTop-this.scrollPanelHeight)*2+"px,"+(this.itemsContainer.width()+2)+"px,"+(this.items[this.selectedItemIndex()].offsetTop*2-this.scrollPanelHeight)+"px,0px)","margin-top":"-"+(this.items[this.selectedItemIndex()].offsetTop-this.scrollPanelHeight)*2+"px"}),this.element.removeClass("e-scrolltab")):this.model.headerPosition=="right"?(this._tabContentsHeight()>Number(this.model.height.split("px")[0])&&(this.itemsContainer.css({height:parseInt(this.itemsContainer.css("height"))+parseInt(this.items.css("height"))*this.items.length+30+"px",position:"absolute","margin-left":"-1px",right:0+Number(this.element.css("padding-left").split("px")[0])+"px"}).css("z-index","12"),n(this.contentPanels).css({position:"absolute",width:t.getDimension(this.element,"width")-this.itemsContainer.outerWidth()+"px",height:t.getDimension(this.element,"height")+"px","border-right":"none"}),this.itemsContainer.css("border-left","1px solid #bbbcbb"),this.model.enableRTL&&this.itemsContainer.css("margin-right","-"+(this.itemsContainer.width()+1)+"px"),this._beforeWidth==0||this._beforeWidth<this.scrollPanelHeight?this.itemsContainer.css("clip","rect(0px,"+this.itemsContainer.width()+2+"px,"+this.scrollPanelHeight+"px,"+this.scrollstep+"px)"):this._beforeWidth>this.scrollPanelHeight&&this.itemsContainer.css({clip:"rect("+(this.items[this.selectedItemIndex()].offsetTop-this.scrollPanelHeight)*2+"px,"+(this.itemsContainer.width()+2)+"px,"+(this.items[this.selectedItemIndex()].offsetTop*2-this.scrollPanelHeight)+"px,0px)","margin-top":"-"+(this.items[this.selectedItemIndex()].offsetTop-this.scrollPanelHeight)*2+"px"}),this.element.removeClass("e-scrolltab")),this.model.enableRTL&&(this.itemsContainer.css({height:parseInt(this.itemsContainer.css("height"))+parseInt(this.items.css("height"))*this.items.length+30+"px",position:"absolute","margin-left":"-1px",right:this.itemsContainer.width()+Number(this.element.css("padding-right").split("px")[0])+"px"}).css("z-index","12"),n(this.contentPanels).css({position:"absolute",width:t.getDimension(this.element,"width")-this.itemsContainer.outerWidth()+"px",left:this.element.css("padding-left"),height:t.getDimension(this.element,"height")+"px","border-right":"none",left:0+Number(this.element.css("padding-right").split("px")[0])+"px"}))):this._beforeWidth==0||this._beforeWidth<this.scrollPanelWidth?this.itemsContainer.css({clip:"rect(0px,"+(this.scrollPanelWidth+this.scrollstep)+"px,100px,"+this.scrollstep+"px)","margin-left":"-"+this.scrollstep+"px"}):this._beforeWidth>this.scrollPanelWidth&&this.itemsContainer.css({clip:"rect(0px,"+(this.items[this.selectedItemIndex()].offsetLeft*2-this.scrollPanelWidth)+"px,100px,"+(this.items[this.selectedItemIndex()].offsetLeft-this.scrollPanelWidth)*2+"px)","margin-left":"-"+(this.items[this.selectedItemIndex()].offsetLeft-this.scrollPanelWidth)*2+"px"});this.element.find(".e-icon.e-chevron-circle-left").length&&this.element.find(".e-icon.e-chevron-circle-left").css("display","none");this._initialClip=this.itemsContainer.css("clip")}},_executeForwardScrolling:function(n,i){var r=i?i:n.type=="swiperight"||n.type=="swipeleft"?50:30;if(this.model.headerPosition=="top"||this.model.headerPosition=="bottom")if(this.model.enableRTL){var f=Number(this.itemsContainer.css("clip").split("px")[1].replace(",","")),u=Number(this.itemsContainer.css("clip").split("px")[3].replace(",","")),o=this.itemsContainer.css("margin-right")?Number(this.itemsContainer.css("margin-right").split("px")[0].replace(",","")):0,e=Number(this._rightScrollIcon.css("margin-left").split("px")[0]),s=this._leftScrollIcon&&this._leftScrollIcon.css("margin-right")?Number(this._leftScrollIcon.css("margin-right").split("px")[0]):"";this.itemsContainer.css({clip:"rect(0px,"+(f-r)+"px,100px,"+(u-r)+"px)","margin-right":"-"+(-o+r)+"px"});this._rightScrollIcon.css("margin-left",e-r+"px");this._leftScrollIcon?this._leftScrollIcon.css("margin-right",s+r+"px"):"";n.type=="swipeleft"&&Math.abs(Number(this.itemsContainer.css("margin-right").split("px")[0]))>=this._tabContentsWidth()-t.getDimension(this.element,"width")&&this._off(this.items,"swipeleft",this._tabSwipe);this._leftScrollIcon&&this._leftScrollIcon.css("margin-right")&&Number(this.itemsContainer.css("margin-right").split("px")[0].replace("-",""))>=this._tabContentsWidth()-t.getDimension(this.element,"width")&&this._rightScrollIcon.css("display","none")}else{var f=Number(this.itemsContainer.css("clip").split("px")[1].replace(",","")),u=Number(this.itemsContainer.css("clip").split("px")[3].replace(",","")),e=Number(this._rightScrollIcon.css("margin-right").split("px")[0]);this.itemsContainer.css({clip:"rect(0px,"+(f+r)+"px,100px,"+(u+r)+"px)","margin-left":"-"+(u+r)+"px"});n.type=="swipeleft"&&Math.abs(Number(this.itemsContainer.css("margin-left").split("px")[0]))>=this._tabContentsWidth()-t.getDimension(this.element,"width")&&this._off(this.items,"swipeleft",this._tabSwipe);this._leftScrollIcon&&this._leftScrollIcon.css("margin-left")&&Number(this.itemsContainer.css("margin-left").split("px")[0].replace("-",""))>=this._tabContentsWidth()-t.getDimension(this.element,"width")&&(this._rightScrollIcon.css("display","none"),this._off(this.items,"swipeleft",this._tabSwipe))}else if(this.model.headerPosition=="left"||this.model.headerPosition=="right"){var f=Number(this.itemsContainer.css("clip").split("px")[0].replace(",","").split("(")[1]),u=Number(this.itemsContainer.css("clip").split("px")[2].replace(",","")),e=Number(this._rightScrollIcon.css("margin-top").split("px")[0].replace(",","")),c=this._leftScrollIcon&&this._leftScrollIcon.css("margin-top")?Number(this._leftScrollIcon.css("margin-top").split("px")[0]):0,h=this.itemsContainer.css("margin-top")?Number(this.itemsContainer.css("margin-top").split("px")[0]):0;this.itemsContainer.css({clip:"rect("+(f+r)+"px, "+(this.itemsContainer.width()+4)+"px,"+(u+r)+"px, 0px)","margin-top":"-"+(-h+r)+"px"});u>this._tabContentsHeight()-20&&(this.itemsContainer.css({clip:"rect("+(this._tabContentsHeight()-Number(this.model.height)+2)+"px, "+(this.itemsContainer.width()+4)+"px, "+(this._tabContentsHeight()+2)+"px, 0px","margin-top":"-"+(this._tabContentsHeight()-Number(this.model.height)+2)+"px"}),this._rightScrollIcon.css("display","none"))}},_executeBackwardScrolling:function(n,i){var r;if(this._rightScrollIcon!=""&&this._rightScrollIcon.css("display","block"),r=i?i:n.type=="swiperight"||n.type=="swipeleft"?50:30,this.model.headerPosition=="top"||this.model.headerPosition=="bottom")if(this.model.enableRTL){var f=Number(this.itemsContainer.css("clip").split("px")[1].replace(",","")),u=Number(this.itemsContainer.css("clip").split("px")[3].replace(",","")),e=Number(this.itemsContainer.css("margin-right").split("px")[0])?Number(this.itemsContainer.css("margin-right").split("px")[0]):0,s=this._leftScrollIcon&&this._leftScrollIcon.css("margin-right")?Number(this._leftScrollIcon.css("margin-right").split("px")[0]):"",o=Number(this._rightScrollIcon.css("margin-left").split("px")[0]);this.itemsContainer.css({clip:"rect(0px,"+(f+r)+"px,100px,"+(u+r)+"px)","margin-right":"-"+(-e-r)+"px"});this._rightScrollIcon.css("margin-left",o+r+"px");this._leftScrollIcon?this._leftScrollIcon.css("margin-right",s-r+"px"):"";n.type=="swiperight"&&Math.abs(Number(this.itemsContainer.css("margin-right").split("px")[0]))<=this._tabContentsWidth()-t.getDimension(this.element,"width")&&this._on(this.items,"swipeleft",this._tabSwipe);e>=-r&&this._leftScrollIcon&&this._leftScrollIcon.css("display","none")}else{var f=Number(this.itemsContainer.css("clip").split("px")[1].replace(",","")),u=Number(this.itemsContainer.css("clip").split("px")[3].replace(",","")),o=Number(this._rightScrollIcon.css("margin-right").split("px")[0]);this.itemsContainer.css({clip:"rect(0px,"+(f-r)+"px,100px,"+(u-r)+"px)","margin-left":"-"+(u-r)+"px"});u-r<0&&(this.itemsContainer.css({clip:"rect(0px,"+t.getDimension(this.element,"width")+"px,100px, 0px","margin-left":"0px"}),this._rightScrollIcon.css("margin-right",this.itemsContainer.width()-t.getDimension(this.element,"width")+20+"px"));n.type=="swiperight"&&Math.abs(Number(this.itemsContainer.css("margin-left").split("px")[0]))<=this._tabContentsWidth()-t.getDimension(this.element,"width")&&this._on(this.items,"swipeleft",this._tabSwipe);u<=r&&this._leftScrollIcon&&this._leftScrollIcon.css("display","none")}else if(this.model.headerPosition=="left"||this.model.headerPosition=="right"){var f=Number(this.itemsContainer.css("clip").split("px")[0].replace(",","").split("(")[1]),u=Number(this.itemsContainer.css("clip").split("px")[2].replace(",","")),o=Number(this._rightScrollIcon.css("margin-top").split("px")[0]),c=this._leftScrollIcon.css("margin-top")?Number(this._leftScrollIcon.css("margin-top").split("px")[0]):0,h=this.itemsContainer.css("margin-top")?Number(this.itemsContainer.css("margin-top").split("px")[0]):0;this.itemsContainer.css({clip:"rect("+(f-r)+"px, "+this.itemsContainer.width()+"px,"+(u-r)+"px, 0px)","margin-top":"-"+(-h-r)+"px"});Number(this.itemsContainer.css("clip").split("px")[0].split("(")[1])<=0&&(this.itemsContainer.css({clip:"rect( 0px, "+this.itemsContainer.width()+"px,"+Number(this.model.height)+"px, 0px)","margin-top":"0px"}),this._leftScrollIcon.css("display","none"))}},_removeHeaderClass:function(){this.itemsContainer.remove();this.itemsContainer.insertBefore(this.element.find(">div").first());this.items.removeClass("e-bottom-line e-top-line");n(this.contentPanels).removeClass("e-content-bottom e-activetop e-activebottom");this.itemsContainer.removeClass("e-header e-left e-right")},_removeVerticalClass:function(){this.element.removeClass("e-vertical");this.itemsContainer.removeClass("e-left e-right").removeAttr("style")},_removeBaseClass:function(){this.element.removeClass("e-tab e-widget e-corner e-js e-tabscroll").removeAttr("role tabindex unselectable");(this.model.headerPosition=="left"||this.model.headerPosition=="right")&&this._removeVerticalClass();this.itemsContainer.removeClass("e-header e-box e-clearall e-select e-addborderbottom e-addbordertop e-addborderleft e-addborderright");this.anchors.removeClass("e-link");this.items.removeClass("e-select e-item e-active e-bottom-line e-top-line e-margine-top e-margine-bottom e-rtl-top-line e-top-hover e-rtl-bottom-line e-disable").removeAttr("role tabindex aria-selected aria-expanded");n(this.contentPanels).removeClass("e-content  e-content-item e-box e-content-bottom e-activetop e-activebottom e-active-content e-hidebottom e-addborderbottom e-hidetop e-addbordertop e-hideleft e-addborderleft e-hideright e-addborderright e-disable").removeAttr("role aria-hidden").css("display","");this.element.find("div.e-close.e-tabdelete,div.e-icon.e-chevron-circle-right,div.e-icon.e-chevron-circle-left,div.e-icon.e-reload").remove()},_addContentTag:function(n,i){var u=this._getTabId(n),r=this.element.find("#"+u);r.length||(r=t.buildTag("div.e-content  e-content-item e-box e-content-bottom #"+u).insertAfter(this.contentPanels[i-1]||this.itemsContainer));this.contentPanels.splice(i,0,r[0])},_roundedCorner:function(n){n?this.element.addClass("e-corner"):this.element.hasClass("e-corner")&&this.element.removeClass("e-corner")},_setTabsHeightStyle:function(i){var r,u;if(t.Tab.HeightAdjustMode.Content!=i&&n(this.contentPanels).height(""),t.Tab.HeightAdjustMode.Fill==i)(t.Tab.Position.Left===this.model.headerPosition||t.Tab.Position.Right===this.model.headerPosition)&&n(this.contentPanels).css("height","100vh"),this._contentPaneSize();else if(t.Tab.HeightAdjustMode.Auto==i){for(r=0,n(this.contentPanels).css({display:"none"}).addClass("e-active-content"),u=0;u<this.contentPanels.length;u++)r=Math.max(r,this._getDimension(n(this.contentPanels[u]),"outerHeight"));n(this.contentPanels).removeClass("e-active-content");n(this.contentPanels).height(r);this.maxAutoHeight=r;this.showItem(this.selectedItemIndex())}else t.Tab.HeightAdjustMode.None==i&&this.model.height!=null&&this._contentPaneSize();t.Tab.HeightAdjustMode.Fill!==i&&n(this.itemsContainer).height("");t.Tab.HeightAdjustMode.Content==i&&n(this.contentPanels).height("auto");this.model.enableTabScroll&&(this.model.headerPosition=="left"||this.model.headerPosition=="right")&&n(this.contentPanels).css("height",this.model.height+"px")},_getDimension:function(t,i){var u,f=n(t).parents().addBack().filter(":hidden"),r={visibility:"hidden",display:"block"},e=[];return f.each(function(){var t={};for(var n in r)t[n]=this.style[n],this.style[n]=r[n];e.push(t)}),u=/(outer)/g.test(i)?n(t)[i](!0):n(t)[i](),f.each(function(n){var i=e[n];for(var t in r)this.style[t]=i[t]}),u},showItem:function(i){var f,e,r,u;if(n.inArray(i,this.model.disabledItemIndex)<0){if(f=this,this._isInteraction!=!1&&(this._isInteraction=!0),this._preTabSelectedIndex=this.selectedItemIndex(),this.selectedItemIndex(i),this.selectedItemIndex()>=this.contentPanels.length&&(this.selectedItemIndex(0),i=this.selectedItemIndex()),i>=0&&!this.initialRender&&!0===this._onBeforeActive(i))return this.selectedItemIndex(this._preTabSelectedIndex),!1;if(this._preTabIndex=this._preTabSelectedIndex,n(this.items[this.selectedItemIndex()]).attr("aria-expanded",!0).attr("aria-selected",!0).attr("tabindex",0),this.selectedItemIndex()!=null&&this.selectedItemIndex()<this.contentPanels.length){for(this._ajaxLoad(),this.hideItem(this._preTabIndex),n(this.contentPanels[this.selectedItemIndex()]).fadeIn(this.model.enableAnimation?20:0,function(){if(!f.initialRender&&f._onActive())return!0;f.initialRender=!1}),this.model.headerPosition=="left"||this.model.headerPosition=="right"?n(this.contentPanels[this.selectedItemIndex()]).addClass("e-active-content "):(e=this.model.headerPosition==t.Tab.Position.Top?"e-activetop":"e-activebottom",n(this.contentPanels[this.selectedItemIndex()]).addClass(e)),n(this.items[this.selectedItemIndex()]).addClass("e-active").removeClass("e-select"),n(this.items[this.selectedItemIndex()]).removeClass("e-margine-top e-margine-bottom"),r=0;r<=n(this.items).length;r++)n(this.items[r]).hasClass("e-select")&&(this.model.headerPosition=="right"&&n(this.items[r]).removeClass("e-margine-top e-margine-bottom"),this.model.headerPosition=="left"&&n(this.items[r]).removeClass("e-margine-top e-margine-bottom"),this.model.headerPosition=="top"&&(this.element.hasClass("e-tab-collapsed")?n(this.items[r]).removeClass("e-margine-top"):n(this.items[r]).addClass("e-margine-top"),n(this.items[r]).removeClass("e-margine-bottom")),this.model.headerPosition=="bottom"&&(n(this.items[r]).removeClass("e-margine-top"),n(this.items[r]).addClass("e-margine-bottom")));n(this.contentPanels[this.selectedItemIndex()]).addClass("e-active-content").removeAttr("aria-hidden",!1)}}this.model.enableTabScroll&&this._tabContentsWidth()>(this.model.width||this.element.width())&&this.itemsContainer.find("li").length&&(this.model.headerPosition=="top"||this.model.headerPosition=="bottom")?this.model.enableRTL||(u=Number(this.itemsContainer.find("li.e-active").position().left.toFixed(0))+this.itemsContainer.find("li.e-active").width()-this._getItemsWidth(this.itemsContainer),u>0&&(Number(this.itemsContainer.find("li.e-active").offset().left.toFixed(0))+this.itemsContainer.find("li.e-active").width()>this._getItemsWidth(this.itemsContainer)||Number(this.itemsContainer.find("li.e-active").offset().left.toFixed(0))-this.itemsContainer.find("li.e-active").width()<0)&&(this.itemsContainer.css("clip","rect(0 ,"+(this._getItemsWidth(this.itemsContainer)+(u+10))+"px, 100px,"+(u+10)+"px)").css("margin-left","-"+(u+10)+"px"),this._rightScrollIcon?this._rightScrollIcon.css("margin-right",this.itemsContainer.width()-this._getItemsWidth(this.itemsContainer)+10-u+"px").css("display","block"):"",this._addScrollBackIcon(),this._leftScrollIcon&&this._leftScrollIcon.css("margin-left")&&Number(this.itemsContainer.css("margin-left").split("px")[0].replace("-",""))>=this._tabContentsWidth()-this._getItemsWidth(this.itemsContainer)&&Number(this.itemsContainer.find("li.e-active").offset().left.toFixed(0))-this.itemsContainer.find("li.e-active").width()<0?(this._rightScrollIcon.css("display","none"),this._off(this.items,"swipeleft",this._tabSwipe)):this._leftScrollIcon?this._leftScrollIcon.css("display","block"):"",this._rightScrollIcon?this._rightScrollIcon.css("display","none"):"")):this.model.enableTabScroll&&this.model.height&&(this.model.headerPosition=="left"||this.model.headerPosition=="right")&&!this.model.enableRTL&&(u=Number(this.itemsContainer.find("li.e-active").position().top.toFixed(0))+this.itemsContainer.find("li.e-active").height()-Number(this.model.height),u>0&&(Number(this.itemsContainer.find("li.e-active").offset().top.toFixed(0))+this.itemsContainer.find("li.e-active").height()>Number(this.model.height)||Number(this.itemsContainer.find("li.e-active").offset().top.toFixed(0))-this.itemsContainer.find("li.e-active").height()<0)&&(this.itemsContainer.css("clip","rect("+u+"px,"+(this.itemsContainer.outerWidth()+2)+"px, "+(Number(this.model.height)+u)+"px, 0px").css("margin-top","-"+u+"px"),this._addScrollBackIcon()))},_getItemsWidth:function(n){for(var i=0,t=0;t<n[0].children.length;t++)i+=n[0].children[t].offsetWidth;return i},hideItem:function(i){if(n(this.contentPanels[i]).fadeOut(0),!(this.model.headerPosition=="left"||this.model.headerPosition=="right"))var r=this.model.headerPosition==t.Tab.Position.Top?"e-activetop":"e-activebottom";n(this.items[i]).removeClass("e-active").addClass("e-select");n(this.contentPanels[i]).removeClass("e-active-content "+r).attr("aria-hidden",!0)},_ajaxLoad:function(){var t=n(this.contentPanels[this.selectedItemIndex()]),i=this.anchors[this.selectedItemIndex()],r=n(i).attr("href");t.is(":empty")&&r.indexOf("#")!==0&&this._sendAjaxOptions(t,i)},_getTabId:function(n){return n.indexOf("#")?this.model.idPrefix+this._getNextTabId():n.replace("#","")},_getNextTabId:function(){return++this.tabId},_disableTabs:function(){for(var t=0,i;i=this.items[t];t++)n.inArray(t,this.model.disabledItemIndex)>-1&&(n(i).find("a").off(this.model.events),n(i).find("div.e-close").off("click")),n(i)[n.inArray(t,this.model.disabledItemIndex)!=-1&&!n(i).hasClass("e-tab-selected")?"addClass":"removeClass"]("e-disable"),n(this.contentPanels[t])[n.inArray(t,this.model.disabledItemIndex)!=-1&&!n(this.contentPanels[t]).hasClass("e-tab-selected")?"addClass":"removeClass"]("e-disable")},_tabItemClick:function(t){if(this.model.enabled){t.preventDefault();var i;this.selectedItemIndex()==n(this.items).index(n(t.currentTarget))&&this.model.collapsible?(i=-1,n(this.element).addClass("e-tab-collapsed")):i=n(this.items).index(n(t.currentTarget));i!=this.selectedItemIndex()&&this.showItem(i);n(this.element).removeClass("e-tab-collapsed");this.selectedItemIndex()>-1&&this.contentPanels!=0&&this.model.enableRTL&&n(this.element).height(this.itemsContainer.height()+parseInt(n(this.contentPanels[this.selectedItemIndex()]).css("height")))}},_tabDeleteClick:function(t){var u,r,i,f;this.model.enabled&&(u=n(t.target),r=n(t.target).parent().width(),u.hasClass("e-close")&&(i=n(this.items).index(n(t.target).parent())),i==this.selectedItemIndex()&&this.items.length>i&&this.selectedItemIndex(this.selectedItemIndex()+1),f=Number(this.itemsContainer.css("margin-right").split("px")[0])?Number(this.itemsContainer.css("margin-right").split("px")[0]):0,this.model.enableTabScroll&&(this.model.headerPosition=="top"||this.model.headerPosition=="bottom")&&(this._leftScrollIcon||this._rightScrollIcon)&&this._isSizeExceeded()&&(!this.model.enableRTL||this.model.enableRTL&&f<-r?this._executeBackwardScrolling(t,r):(this.itemsContainer.css({"margin-right":"0px",clip:this._initialClip}),this._leftScrollIcon&&this._leftScrollIcon.css("display","none"),this._rightScrollIcon&&this._rightScrollIcon.css("display","none"))),this.removeItem(i),this.model.enableTabScroll&&(this.model.headerPosition=="top"||this.model.headerPosition=="bottom")&&(this._leftScrollIcon||this._rightScrollIcon)&&!this.model.enableRTL&&(this._on(this.element.find("div.e-chevron-circle-right"),"mouseover",this._hoverHandler),this._on(this.element.find("div.e-chevron-circle-right"),"mouseout",this._hoverHandler)))},_tabScrollClick:function(n){this.model.enabled&&this._rightScrollIcon[0]==n.target&&(this._executeForwardScrolling(n),this._addScrollBackIcon(),this._leftScrollIcon.css("display","block"))},_tabSwipe:function(n){n.type=="swipeleft"?this._executeForwardScrolling(n):this._executeBackwardScrolling(n)},_tabScrollBackClick:function(n){this.model.enabled&&this._leftScrollIcon[0]==n.target&&this._executeBackwardScrolling(n)},_tabReloadClick:function(t){var i;if(this.model.enabled&&(i=n(t.target),i.hasClass("e-reload"))){var r=this.anchors[this.selectedItemIndex()],u=n(r).attr("href"),f=n(this.contentPanels[this.selectedItemIndex()]);u.indexOf("#")!==0?this._sendAjaxOptions(f,r):this.showItem(this.selectedItemIndex())}},_sendAjaxOptions:function(t,i){if(this._onBeforeLoad(i))return!0;t.addClass("e-load");var r=this,u=n(i).html(),f=i.href.replace("#",""),e={type:this.model.ajaxSettings.type,cache:this.model.ajaxSettings.cache,url:f,data:this.model.ajaxSettings.data,dataType:this.model.ajaxSettings.dataType,contentType:this.model.ajaxSettings.contentType,async:this.model.ajaxSettings.async,success:function(n){try{r._ajaxSuccessHandler(n,t,i,u)}catch(f){}},error:function(){try{r._ajaxErrorHandler(i,r.selectedItemIndex(),u)}catch(n){}}};this._sendAjaxRequest(e)},_sendAjaxRequest:function(t){n.ajax({type:t.type,cache:t.cache,url:t.url,dataType:t.dataType,data:t.data,contentType:t.contentType,async:t.async,success:t.success,error:t.error,beforeSend:t.beforeSend,complete:t.complete})},_ajaxSuccessHandler:function(t,i,r,u){u!=null&&n(r).html(u);i.removeClass("e-load");i.html(t).addClass("e-tab-loaded");var f={data:t,url:r,content:i};return this._trigger("ajaxSuccess",f),this._onLoad(r)?!0:void 0},_ajaxErrorHandler:function(n,t){this._trigger("ajaxError",{data:n,url:t});this._onLoad(t)},_createContentPanel:function(t){return n("<div><\/div>").attr("id",t).addClass("e-content  e-content-item e-content-bottom e-box")},_refresh:function(){this._unWireEvents();this.itemsContainer.removeAttr("style class");n(this.contentPanels).removeAttr("style class");this.element.css("margin-left","");this._removeVerticalClass();this._removeHeaderClass();this._initialize()},_keyPress:function(i){var f,o,s,r,u,e;if(this.model.enabled){if(o=n(i.target),s=i.keyCode?i.keyCode:i.which?i.which:i.charCode,o.hasClass("e-link")||o.hasClass("e-item"))switch(s){case 39:case 40:for(i.preventDefault(),r=[],u=0;u<this.getItemsCount();u++)n.inArray(u,this.model.hiddenItemIndex)<0&&r.push(u);e=n.inArray(this.selectedItemIndex(),r);e==r.length-1?this.showItem(r[0]):this.showItem(r[e+1]);break;case 37:case 38:for(i.preventDefault(),r=[],u=0;u<this.getItemsCount();u++)n.inArray(u,this.model.hiddenItemIndex)<0&&r.push(u);e=n.inArray(this.selectedItemIndex(),r);e==0?this.showItem(r[r.length-1]):this.showItem(r[e-1]);break;case 35:i.preventDefault();this.showItem(this.getItemsCount()-1);break;case 36:i.preventDefault();this.showItem(0);break;case 13:i.preventDefault();this.showItem(this.selectedItemIndex())}else if(i.ctrlKey&&!o.hasClass("e-tab"))switch(s){case 38:i.preventDefault();r=n(this.contentPanels).index(o.parent(".e-content"));f=n(this.items[r]);break;case 33:i.preventDefault();f=n(this.items[0]);this.showItem(0);break;case 34:i.preventDefault();f=n(this._headers[this.getItemsCount()-1]);this.showItem(this.getItemsCount()-1)}t.isNullOrUndefined(f)||(f.addClass("e-focus"),f.focus())}},_hoverHandler:function(t){if(t.preventDefault(),this.model.enabled){var i=n(this.items).index(n(t.target).parent());i==-1&&(i=n(this.items).index(n(t.target)));(this.model.showCloseButton||this.model.showReloadIcon)&&!n(this.items[i]).hasClass("e-disable")&&(t.type==="mouseout"?n(this.element.find("div.e-tabdelete")[i]).css("visibility","hidden"):n(this.element.find("div.e-tabdelete")[i]).css("visibility","visible"),t.type==="mouseout"?n(this.element.find("div.e-reload")[i]).css("visibility","hidden"):n(this.element.find("div.e-reload")[i]).css("visibility","visible"));t.type==="mouseout"?n(this.element.find("div.e-chevron-circle-right")).css("visibility","hidden"):n(this.element.find("div.e-chevron-circle-right")).css("visibility","visible");t.type==="mouseout"?n(this.element.find("div.e-chevron-circle-left")).css("visibility","hidden"):n(this.element.find("div.e-chevron-circle-left")).css("visibility","visible")}},_wireEvents:function(t){this._on(this.items,t,this._tabItemClick);this._on(this.itemsContainer,"mouseover",this._hoverHandler);this._on(this.itemsContainer,"mouseout",this._hoverHandler);this._on(this.element.find(">ul").eq(0).find(">li div.e-close"),"click",this._tabDeleteClick);this._on(this.element.find("div.e-chevron-circle-left"),"click",this._tabScrollBackClick);this.model.enableTabScroll&&this._on(this.items,"swipeleft swiperight",this._tabSwipe);this._on(this.itemsContainer,"focusin",this._focusIn);this._on(this.itemsContainer,"focusout",this._focusOut);n(window).on("resize",n.proxy(this._resize,this));this._on(this.element.find(">ul").eq(0).find(">li div.e-reload"),"click",this._tabReloadClick);this._resizeEvents(this.model.heightAdjustMode)},_resize:function(){this.model&&this.model.width==null&&this.model.enableTabScroll&&(this.model.headerPosition=="top"||this.model.headerPosition=="bottom")&&(this._removeScroll(),this._addScroll())},_unWireEvents:function(){this._off(this.items,this.model.events);this._off(this.element.find(">ul").eq(0).find(">li div.e-close"),"click");this._off(this.element.find("div.e-chevron-circle-right"),"click");this._off(this.element.find("div.e-chevron-circle-left"),"click");this.model.enableTabScroll&&this._off(this.items,"swipeleft swiperight",this._tabSwipe);this._off(this.itemsContainer,"mouseover",this._hoverHandler);this._off(this.itemsContainer,"mouseout",this._hoverHandler);this._off(this.element.find("div.e-chevron-circle-left"),"mouseover",this._hoverHandler);this._off(this.element.find("div.e-chevron-circle-right"),"mouseover",this._hoverHandler);this._off(this.element.find("div.e-chevron-circle-left"),"mouseout",this._hoverHandler);this._off(this.element.find("div.e-chevron-circle-right"),"mouseout",this._hoverHandler);this._off(this.itemsContainer,"focusin",this._focusIn);this._off(this.itemsContainer,"focusout",this._focusOut);this._off(this.element.find(">ul").eq(0).find(">li div.e-reload"),"click");this._resizeEvents()},_resizeEvents:function(t){if(t==="fill")n(window).on("resize",n.proxy(this._windowResized,this));else n(window).off("resize",n.proxy(this._windowResized,this))},_windowResized:function(){var t=this._getDimension(n(this.element).parent(),"height");this._prevSize!=t&&(this._contentPaneSize(),this._prevSize=t,!this.model.width&&this.model.enableTabScroll&&this._addScroll())},_contentPaneSize:function(){var i,r,u,t;for(this.model.height!=null&&this.model.heightAdjustMode=="none"?(n(this.element).height(this.model.height),i=this._getDimension(n(this.element),"height")):i=this._getDimension(n(this.element).parent(),"height"),n(this.contentPanels).height(""),n(this.element).parent().css({overflow:"auto"}),(this.model.headerPosition==="top"||this.model.headerPosition==="bottom")&&(i-=this._getDimension(n(this.itemsContainer),"outerHeight")),r=0,t=0;t<this.contentPanels.length;t++)n(this.contentPanels[t]).hasClass("e-active-content")?(u=Math.max(r,this._getDimension(n(this.contentPanels[t]),"outerHeight")-this._getDimension(n(this.contentPanels[t]),"height")),this.model.height!=null&&this.model.heightAdjustMode=="none"?n(this.contentPanels[t]).outerHeight(i).css({overflow:"auto"}):n(this.contentPanels[t]).height(i-u).css({overflow:"auto"})):(r=Math.max(r,this._getDimension(n(this.contentPanels[t]),"outerHeight")-this._getDimension(n(this.contentPanels[t]),"height")),this.model.height!=null&&this.model.heightAdjustMode=="none"?n(this.contentPanels[t]).outerHeight(i).css({overflow:"auto"}):n(this.contentPanels[t]).height(i-r).css({overflow:"auto"}))},_disableItems:function(t){if(!this.model.enabled)return!1;if(t!=null){for(var i=0;i<t.length;i++)n.inArray(t[i],this.model.disabledItemIndex)==-1&&this.model.disabledItemIndex.push(t[i]);this.model.disabledItemIndex.sort();this._disableTabs()}},_enableItems:function(t){var i,r;if(!this.model.enabled)return!1;for(i=0;i<t.length;i++)r=t[i],this.model.disabledItemIndex=n.grep(this.model.disabledItemIndex,function(n){return n!=r});this._disableTabs()},disable:function(){for(var t=[],n=0;n<this.getItemsCount();n++)t.push(n);this._disableItems(t);this.model.enabledItemIndex=[];this._unWireEvents()},enable:function(){var i=[],t;for(this.model.disabledItemIndex=[],t=0;t<this.getItemsCount();t++)n.inArray(t,this.model.enabledItemIndex)<0&&(this.model.enabledItemIndex.push(t),i.push(t)),this._enableItems(t)},getItemsCount:function(){if(this.items)return this.items.length},addItem:function(r,u,f,e,o){var h,s,a,c,l,v,y,p;for(this._addItemIndex=f>=0&&f<this.items.length?f:this.items.length,h=0;h<this.model.disabledItemIndex.length;h++)this.model.disabledItemIndex[h]>=f&&this.model.disabledItemIndex[h]++;this.model.headerPosition=="left"?this.items.length>=0&&this.itemsContainer.addClass("e-left"):this.model.headerPosition=="right"?this.items.length>=0&&this.itemsContainer.addClass("e-right"):this.items.length==0&&this.itemsContainer.addClass("e-header");s=t.buildTag("li.e-select e-item");this.model.headerPosition=="top"&&(this.model.enableRTL?n(s).addClass("e-rtl-top-line e-top-hover"):n(s).addClass("e-bottom-line"));t.isNullOrUndefined(e)||(a=t.buildTag("span").addClass(e),s.append(a));f===i&&u===i&&r!=null&&(u="Item");f===i&&u===i&&o===i&&(r="#Item"+this.items.length,u="Item");o!=i?(o.indexOf("#")!=0&&(o="#"+o),this.divId=o):r!=i&&(o=r);c=t.buildTag("a",u,{},{href:r});c.addClass("e-link");(this.model.headerPosition=="top"||this.model.headerPosition=="bottom")&&c.appendTo(s);this.model.showCloseButton&&(l=t.buildTag("div.e-icon e-close e-tabdelete","",{},{}).css("visibility","hidden"),s.append(l),this._on(l,"click",this._tabDeleteClick));(this.model.headerPosition=="left"||this.model.headerPosition=="right")&&c.appendTo(s);f===i&&(f=this.anchors.length);v=f>=this.items.length;v?s.appendTo(this.itemsContainer):s.insertBefore(this.items[f]);t.isNullOrUndefined(this.model.headerSize)||this._setHeaderSize(this.model.headerSize);this.selectedItemIndex()==f?(this.hideItem[f],this.selectedItemIndex(this.selectedItemIndex()+1)):(this.hideItem[f],f<this.selectedItemIndex()&&this.selectedItemIndex(this.selectedItemIndex()+1));this._itemsRefreshing();this._reinitialize(!0);this.model.headerPosition=="top"&&n(this.contentPanels[f]).addClass("e-hidebottom");this.model.headerPosition=="bottom"&&(n(this.contentPanels[f]).addClass("e-hidetop"),s.addClass("e-top-line e-item e-select e-margine-bottom"));this.model.headerPosition=="left"&&n(this.contentPanels[f]).addClass("e-hideleft");this.model.headerPosition=="right"&&n(this.contentPanels[f]).addClass("e-hideright");y={tabHeader:this.anchors[f],tabContent:this.contentPanels[f]};this.refreshTabScroll();this._addItemIndex=null;this._onAdd(y);this.model.showReloadIcon&&(p=t.buildTag("div.e-icon e-reload","",{},{role:"presentation"}).css("visibility","hidden"),n(this.element.find("li")[f]).append(p));this.model.enableTabScroll&&this.model.headerPosition=="right"&&n(this.contentPanels).css("height",this.model.height+"px");this._setTabsHeightStyle(this.model.heightAdjustMode)},_isSizeExceeded:function(){for(var r,u=this.element.width(),i=0,f=this.items.length,t=0;t<f;t++)r=n(this.items[t]).width(),i+=r;return i>u?!0:!1},refreshTabScroll:function(){this._isSizeExceeded()?(this.element.find("div.e-chevron-circle-right").length>=1&&this.element.find("div.e-chevron-circle-right").remove(),this.model.enableTabScroll&&(this._checkScroll(),this._addScrollIcon())):((this.model.headerPosition=="left"||this.model.headerPosition=="right")&&this._tabContentsHeight()>(this.element.width()||Number(this.model.height))||(this.model.headerPosition=="top"||this.model.headerPosition=="bottom")&&this.itemsContainer.width()>this.element.outerWidth())&&this._checkScroll()},removeItem:function(t){var u,r,i,f;if(!this.model.enabled)return!1;if(t!=null&&t>-1&&t<this.items.length){if(this._onBrforeRemove({index:t})===!0)return!1;for(u=n(this.items[t]).width(),r=n(this.items[t]).remove(),this.model.enableTabScroll&&this.itemsContainer.css("width",this.itemsContainer.width()-u+"px"),this.model.disabledItemIndex=[],r.hasClass("e-active")&&(t==0?this.selectedItemIndex(t+1):this.selectedItemIndex(t-1),this.showItem(this.selectedItemIndex())),n(this.element.find(">div.e-content")[t]).remove(),this.contentPanels.splice(t,1),t<this.selectedItemIndex()?this.selectedItemIndex(this.selectedItemIndex()-1):this.selectedItemIndex(),(t<0||t>=this.anchors.length)&&this.selectedItemIndex(0),(this.model.headerPosition=="left"||this.model.headerPosition=="right")&&this.items.length==1?this._removeVerticalClass():this.items.length==1&&this.itemsContainer.removeClass("e-header"),this._unWireEvents(),this._itemsRefreshing(),this._wireEvents(this.model.events),this.model.enableTabScroll&&this._on(this.element.find("div.e-chevron-circle-right"),"click",this._tabScrollClick),i=0;i<this.items.length;i++)n(this.items[i]).hasClass("e-disable")&&this.model.disabledItemIndex.push(i);this._disableTabs();f={removedTab:r};this._onRemove(f)}this.getItemsCount()==0&&(this.itemsContainer.removeAttr("style"),this.itemsContainer.find("div").remove());this._tabContentsHeight()<Number(this.model.height)&&this.itemsContainer.css("clip").split("px").length&&this.model.enableTabScroll&&(this.model.headerPosition=="left"||this.model.headerPosition=="right")?(this._refresh(),this.itemsContainer.removeAttr("style"),this._leftScrollIcon?this._leftScrollIcon.remove():"",this._rightScrollIcon?this._rightScrollIcon.remove():""):this.model.enableTabScroll&&(this.model.headerPosition=="left"||this.model.headerPosition=="right")&&(this._leftScrollIcon&&Number(this.itemsContainer.css("clip").split("px")[0].replace(",","").split("(")[1])!=-Number(this._leftScrollIcon.css("margin-top").split("px")[0])?(this._removeScroll(),this._addScroll()):this.refreshTabScroll(),this.showItem(this.selectedItemIndex()))},show:function(){if(!this.model.enabled)return!1;this.element.css("visibility","visible")},hide:function(){if(!this.model.enabled)return!1;this.element.css("visibility","hidden")},_onBeforeLoad:function(n){var t;return t=this.selectedItemIndex()==-1&&this.model.collapsible?{prevActiveHeader:this.items[this._preTabIndex],prevActiveIndex:this._preTabIndex,activeHeader:null,activeIndex:null,url:n,isInteraction:this._isInteraction}:{prevActiveHeader:this.items[this._preTabIndex],prevActiveIndex:this._preTabIndex,activeHeader:this.items[this.selectedItemIndex()],activeIndex:this.selectedItemIndex(),url:n,isInteraction:this._isInteraction},this._trigger("ajaxBeforeLoad",t)},_focusIn:function(){if(!this.model.readOnly&&this.model.allowKeyboardNavigation)n(this.element).on("keydown",n.proxy(this._keyPress,this))},_focusOut:function(){n(this.element).off("keydown",n.proxy(this._keyPress,this))},_onLoad:function(n){var t;return t=this.selectedItemIndex()==-1&&this.model.collapsible?{prevActiveHeader:this.items[this._preTabIndex],prevActiveIndex:this._preTabIndex,activeHeader:null,activeIndex:null,url:n,isInteraction:this._isInteraction}:{prevActiveHeader:this.items[this._preTabIndex],prevActiveIndex:this._preTabIndex,activeHeader:this.items[this.selectedItemIndex()],activeIndex:this.selectedItemIndex(),url:n,isInteraction:this._isInteraction},this._trigger("ajaxLoad",t)},_onActive:function(){var n;return n=this.selectedItemIndex()==-1&&this.model.collapsible?{prevActiveHeader:this.items[this._preTabIndex],prevActiveIndex:this._preTabIndex,activeHeader:null,activeIndex:null,isInteraction:this._isInteraction}:{prevActiveHeader:this.items[this._preTabIndex],prevActiveIndex:this._preTabIndex,activeHeader:this.items[this.selectedItemIndex()],activeIndex:this.selectedItemIndex(),isInteraction:this._isInteraction},this._isInteraction=!0,this._trigger("itemActive",n)},_onBeforeActive:function(n){if(this.model.beforeActive!=null){var t;return t=this.selectedItemIndex()==-1&&this.model.collapsible?{prevActiveHeader:this.items[this._preTabSelectedIndex],prevActiveIndex:this._preTabSelectedIndex,activeHeader:null,activeIndex:null,isInteraction:this._isInteraction}:{prevActiveHeader:this.items[this._preTabSelectedIndex],prevActiveIndex:this._preTabSelectedIndex,activeHeader:this.items[n],activeIndex:n,isInteraction:this._isInteraction},this._trigger("beforeActive",t)}},_onAdd:function(n){return this._trigger("itemAdd",n)},_onRemove:function(n){return this._trigger("itemRemove",n)},_onBrforeRemove:function(n){return this._trigger("beforeItemRemove",n)}});t.Tab.HeightAdjustMode={Content:"content",Auto:"auto",Fill:"fill",None:"none"};t.Tab.Position={Top:"top",Bottom:"bottom",Left:"left",Right:"right"}})(jQuery,Syncfusion)});
