/*!
*  filename: ej.pivotclient.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.globalize.min","./../common/ej.core.min","./../common/ej.data.min","./../common/ej.touch.min","./../common/ej.draggable.min","./../common/ej.scroller.min","./ej.button.min","./ej.checkbox.min","./ej.dropdownlist.min","./ej.dialog.min","./ej.toolbar.min","./ej.maskedit.min","./ej.waitingpopup.min","./ej.treeview.min","./ej.tab.min","./ej.pivotchart.min","./ej.pivotgrid.min","./ej.pivotschemadesigner.min","./../datavisualization/ej.chart.min","./ej.pivotanalysis.base.min","./ej.olap.base.min","./ej.pivot.common.min"],n):n()})(function(){(function(n,t,i){t.widget("ejPivotClient","ej.PivotClient",{_rootCSS:"e-pivotclient",element:null,model:null,validTags:["div","span"],defaults:{url:"",cssClass:"",title:"",gridLayout:"normal",chartType:"column",clientExportMode:"chartandgrid",enableDeferUpdate:!1,enablePivotTreeMap:!1,enableRTL:!1,enableDefaultValue:!1,enableAdvancedFilter:!1,enablePaging:!1,enableSplitter:!1,enableToolBar:!1,enableCellSelection:!1,enableLocalStorage:!1,enableVirtualScrolling:!1,enableMemberEditorPaging:!1,showUniqueNameOnPivotButton:!1,isResponsive:!1,collapseCubeBrowserByDefault:!1,enableKPI:!1,showReportCollection:!1,enableValueCellHyperlink:!1,enableRowHeaderHyperlink:!1,enableColumnHeaderHyperlink:!1,enableSummaryCellHyperlink:!1,enableCellContext:!1,enableDrillThrough:!1,enableCellEditing:!1,enableCellDoubleClick:!1,enableCellClick:!1,enableXHRCredentials:!1,enableCompleteDataExport:!1,enableMemberEditorSorting:!1,enableCheckStateOnSearch:!0,dataSource:{data:null,sourceInfo:"",providerName:"ssas",enableAdvancedFilter:!1,isFormattedValues:!1,reportName:"Default",columns:[],cube:"",catalog:"",rows:[],values:[],filters:[],pagerOptions:{categoricalPageSize:0,seriesPageSize:0,categoricalCurrentPage:0,seriesCurrentPage:0}},displaySettings:{mode:"chartandgrid",defaultView:"grid",controlPlacement:"tab",enableTogglePanel:!1,enableFullScreen:!1},serviceMethodSettings:{initialize:"InitializeClient",removeSplitButton:"RemoveSplitButton",filterElement:"FilterElement",nodeDropped:"NodeDropped",toggleAxis:"ToggleAxis",fetchMemberTreeNodes:"FetchMemberTreeNodes",cubeChanged:"CubeChanged",measureGroupChanged:"MeasureGroupChanged",toolbarServices:"ToolbarOperations",memberExpand:"MemberExpanded",saveReport:"SaveReportToDB",fetchReportList:"FetchReportListFromDB",loadReport:"LoadReportFromDB",updateReport:"UpdateReport",exportPivotClient:"Export",mdxQuery:"GetMDXQuery",drillThroughHierarchies:"DrillThroughHierarchies",drillThroughDataTable:"DrillThroughDataTable",paging:"Paging",removeDBReport:"RemoveReportFromDB",renameDBReport:"RenameReportInDB",calculatedMember:"CalculatedMember",valueSorting:"ValueSorting",changeSummaryType:"ChangeSummaryType"},size:{height:"685px",width:"1000px"},sortCubeMeasures:t.Pivot.SortOrder.Ascending,toolbarIconSettings:{enableAddReport:!0,enableNewReport:!0,enableRenameReport:!0,enableDBManipulation:!0,enableWordExport:!0,enableExcelExport:!0,enablePdfExport:!0,enableMDXQuery:!0,enableDeferUpdate:!1,enableFullScreen:!1,enableSortOrFilterColumn:!0,enableSortOrFilterRow:!0,enableToggleAxis:!0,enableChartTypes:!0,enableRemoveReport:!0,enableCalculatedMember:!1},valueSortSettings:{headerText:"",headerDelimiters:"##",sortOrder:"none"},customObject:{},calculatedMembers:[],enableMeasureGroups:!1,locale:"en-US",analysisMode:"olap",operationalMode:"clientmode",renderSuccess:null,renderFailure:null,renderComplete:null,load:null,chartLoad:null,treeMapLoad:null,drillThrough:null,beforeExport:null,beforeServiceInvoke:null,afterServiceInvoke:null,saveReport:null,loadReport:null,fetchReport:null,cellSelection:null,gridDrillSuccess:null,chartDrillSuccess:null,treeMapDrillSuccess:null,axesLabelRendering:null,pointRegionClick:null,valueCellHyperlinkClick:null,rowHeaderHyperlinkClick:null,columnHeaderHyperlinkClick:null,summaryCellHyperlinkClick:null,cellContext:null,cellEdit:null,cellDoubleClick:null,cellClick:null,drillThrough:null,memberEditorPageSize:100,maxNodeLimitInMemberEditor:1e3},dataTypes:{dataSource:{data:"data",columns:"array",rows:"array",values:"array",filters:"array"},serviceMethodSettings:{initialize:"enum",removeSplitButton:"enum",filterElement:"enum",nodeDropped:"enum",toggleAxis:"enum",fetchMemberTreeNodes:"enum",cubeChanged:"enum",measureGroupChanged:"enum",toolbarServices:"enum",memberExpand:"enum",saveReport:"enum",fetchReportList:"enum",loadReport:"enum",updateReport:"enum",exportPivotClient:"enum",mdxQuery:"enum",drillThroughHierarchies:"enum",drillThroughDataTable:"enum",paging:"enum",removeDBReport:"enum",renameDBReport:"enum",calculatedMember:"enum",valueSorting:"enum"},displaySettings:"data",customObject:"data"},observables:["title","gridLayout","displaySettings.mode","displaySettings.defaultView","displaySettings.controlPlacement","displaySettings.enableTogglePanel","locale"],title:t.util.valueFunction("title"),gridLayout:t.util.valueFunction("gridLayout"),displayMode:t.util.valueFunction("displaySettings.mode"),defaultView:t.util.valueFunction("displaySettings.defaultView"),controlPlacement:t.util.valueFunction("displaySettings.controlPlacement"),enableTogglePanel:t.util.valueFunction("displaySettings.enableTogglePanel"),locale:t.util.valueFunction("locale"),_init:function(){this._initPrivateProperties();this._load()},_initPrivateProperties:function(){this._isCollapseCB=!1;this._id=this.element.attr("id");this.oclientProxy=this;this.currentReport="";this.currentCubeName="";this.reportsCount=0;this._pivotGrid=null;this._pivotChart=null;this._pivotSchemaDesigner=null;this._deferReport="";this._keypress=!1;this._memberTreeObj=null;this._chartHeight=0;this._chartWidth=0;this._gridHeight=0;this._gridWidth=0;this._maxInitialChartHeight=0;this._maxInitialChartWidth=0;this._maxInitialGridWidth=0;this._initStyles=[];this._toggleStyles=[];this._initToggle=!0;this._toggleExpand=!1;this._treemapRender=!1;this._dimensionName="";this._dllSortMeasure=null;this._selectedFieldName="";this._axis="";this._isSorted=!1;this._isFiltered=!1;this._sortOrFilterTab="";this._currentAxis="";this._parentElwidth=0;this.pNode="";this.progressPos=null;this._selectedReport="";this._isMembersFiltered=!1;this._pagerObj=null;this._dialogTitle="";this._dataModel=" ";this._clientReportCollection=[];this._prevDrillElements=[];this._drillParams=[];this._drillInfo=[];this.draggedSplitBtn=null;this.isDragging=!1;this.isDropped=!1;this.measureGroupInfo="";this._currentTab=this.defaultView();this._currentItem=null;this._currentReportItems=[];this._savedReportItems=[];this._treeViewData={};this._slicerBtnTextInfo={};this._memberPageSettings={currentMemeberPage:1,startPage:0,endPage:0};this._memberSearchPageSettings={currentMemberSearchPage:1,startSearchPage:0,endSearchPage:0};this._memberDrillPageSettings={currentMemberDrillPage:1,startDrillPage:0,endDrillPage:0};this._isNodeOrButtonDropped=!1;this._ischartTypesChanged=!1;this._isrenderTreeMap=!1;this._isRemoved=!1;this._waitingPopup=null;this._isGridDrillAction=!1;this._isChartDrillAction=!1;this._olapReport="";this._jsonRecords=null;this._excelFilterInfo=[];this._index={tab:0,icon:1,chartimg:0,button:1,dialog:0,editor:0,tree:0};this._curFocus={tab:null,icon:null,chartimg:null,button:null,dialog:null,editor:null,tree:null};this._seriesCurrentPage=1;this._categCurrentPage=1;this._isReportListAction=!0;this._currentRecordName="";this._pagingSavedObjects={drillEngine:[],savedHdrEngine:[],curDrilledItem:{}};this._fieldMembers={};this._fieldSelectedMembers={};this._allMember="";this._editorTreeData=[];this._editorSearchTreeData=[];this._editorDrillTreeData={};this._editorDrillTreePageSettings={};this._lastSavedTree=[];this._isSearchApplied=!1;this._isTimeOut=!1;this._isOptionSearch=!1;this._isEditorDrillPaging=!1;this._isEditorCollapseDrillPaging=!1;this._isSelectSearchFilter=!0;this._calcMemberTreeObj=null;this._cubeTreeView=null;this._calcMemberDialog=null;this._calcMembers=[];this._selectedCalcMember=null;this._args_className=null;this._args_innerHTML=null;this._hierarchyCaption=null;this._currentCollection="";this._currentFilterList={};this._repCol=[];this._reportIndex=0;this._isExporting=!1;this._fullExportedData={};this._parentNodeCollection={};this._parentSearchNodeCollection={};this._initialSlicerName=""},getOlapReport:function(){return JSON.parse(this._olapReport)},setOlapReport:function(n){this._olapReport=JSON.stringify(n)},getJSONRecords:function(){return JSON.parse(this._jsonRecords)},setJSONRecords:function(n){this._jsonRecords=JSON.stringify(n)},getActiveTab:function(){return this._currentTab},_destroy:function(){this.element.empty().removeClass("e-pivotclient"+this.model.cssClass).removeAttr("tabindex");this._waitingPopup!=i&&this._waitingPopup.destroy();this.element.attr("class")==""&&this.element.removeAttr("class");delete this.oclientProxy},_load:function(){var h={element:this.element,customObject:this.model.customObject},s,u;if((this.model.enableAdvancedFilter||this.model.dataSource.enableAdvancedFilter)&&(this.model.dataSource.enableAdvancedFilter=this.model.enableAdvancedFilter=!0),this.model.toolbarIconSettings.enableFullScreen=this.model.displaySettings.enableFullScreen=this.model.toolbarIconSettings.enableFullScreen||this.model.displaySettings.enableFullScreen?!0:!1,this.model.toolbarIconSettings.enableDeferUpdate=this.model.enableDeferUpdate=this.model.enableDeferUpdate?!0:!1,this._trigger("load",h),this.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.dataSource.cube!=""){var f=this.model.dataSource.values.length>0?this.model.dataSource.values[0].measures:[],e=this.model.dataSource.rows,o=this.model.dataSource.columns,r=[];this.model.dataSource.rows=this._reArrangeCalcFields(e).reportItem;r=this._reArrangeCalcFields(e).calcMems;this.model.dataSource.columns=this._reArrangeCalcFields(o).reportItem;r=n.merge(r,this._reArrangeCalcFields(o).calcMems);f=n.grep(f,function(u){return u.expression!=i&&(r.push({caption:u.fieldName,expression:u.expression,hierarchyUniqueName:u.hierarchyUniqueName,formatString:u.formatString?u.formatString:null,format:u.format?u.format:null,memberType:t.PivotClient.MemberType.Measure}),u.caption=t.isNullOrUndefined(u.fieldCaption)?u.fieldName:u.fieldCaption,u.fieldName=u.fieldName.toLowerCase().indexOf("measure")>-1?u.fieldName:"[Measures].["+n.trim(u.fieldName)+"]"),u});this.model.calculatedMembers.length>0?(n.grep(this.model.calculatedMembers,function(t){var t=t;r=n.grep(r,function(n){if(t.caption!=n.caption&&!(n.caption.toLowerCase().indexOf("measure")>-1&&"[Measures].["+t.caption+"]"!=n.caption))return t})}),this.model.calculatedMembers=n.merge(r,this.model.calculatedMembers)):(this.model.calculatedMembers=[],this.model.calculatedMembers=n.merge(r,this.model.calculatedMembers))}this.element.addClass(this.model.cssClass);n("#"+this._id).parent()[0]&&(n("#"+this._id).parent()[0].style.position="relative",this.element.ejWaitingPopup({showOnInit:!0,appendTo:n("#"+this._id).parent()[0]}));this._waitingPopup=this.element.data("ejWaitingPopup");this.model.dataSource.data==null&&this.model.url!=""||this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?(this.model.operationalMode=t.Pivot.OperationalMode.ServerMode,t.isNullOrUndefined(this.model.beforeServiceInvoke)||this._trigger("beforeServiceInvoke",{action:"initializeClient",element:this.element,customObject:this.model.customObject}),s=JSON.stringify(this.model.customObject),this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.initialize,JSON.stringify({action:this.model.enableDrillThrough&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.analysisMode==t.Pivot.AnalysisMode.Pivot?"initializeClient:getDataSet":"initializeClient",customObject:s,clientParams:this.model.enableMeasureGroups+"-"+this.model.chartType+"-"+this.model.enableKPI+"-"+this.model.sortCubeMeasures+"-##-"+JSON.stringify(this.model.valueSortSettings)}),this._renderControlSuccess)):(this.model.operationalMode=t.Pivot.OperationalMode.ClientMode,this.model.analysisMode=this.model.dataSource.cube!=""?t.Pivot.AnalysisMode.Olap:t.Pivot.AnalysisMode.Pivot,u=this,setTimeout(function(){u._renderLayout();u._createControl();u.model.enableSplitter&&u._createSplitter()},0))},_reArrangeCalcFields:function(r){var u=[],f=[],r=n.grep(r,function(n){if(n.expression!=i)f.push({caption:n.fieldName,expression:n.expression,hierarchyUniqueName:n.hierarchyUniqueName,format:n.format?n.format:null,formatString:n.formatString?n.formatString:null,memberType:t.PivotClient.MemberType.Dimension}),u.push(n);else return n});return{reportItem:n.merge(u,r),calcMems:f}},_renderClientControls:function(t){this._renderLayout();this.setOlapReport(t.PivotReport);var i=n.parseJSON(t.PivotReport);this.currentReport=this.getOlapReport()!=""?JSON.parse(this.getOlapReport()).Report:"";this.setJSONRecords({GridJSON:t.GridJSON,ChartJSON:t.ChartJSON});this._createControl();this.model.enableSplitter&&this._createSplitter();this.model.enableAdvancedFilter&&this._pivotGrid&&this._pivotGrid._removeCells(t)},_createControl:function(){var e="",o,s,f,c,r,u;if(this.element.find("#"+this._id+"_PivotSchemaDesigner").ejPivotSchemaDesigner({pivotControl:this,_waitingPopup:this._waitingPopup,enableRTL:this.model.enableRTL,enableMemberEditorSorting:this.model.enableMemberEditorSorting,enableCheckStateOnSearch:this.model.enableCheckStateOnSearch,layout:t.PivotSchemaDesigner.Layouts.OneByOne,olap:{showKPI:!1,showNamedSets:!1},serviceMethods:{nodeDropped:this.model.serviceMethodSettings.nodeDropped,memberExpand:this.model.serviceMethodSettings.memberExpand},locale:this.model.locale,width:this.model.enableSplitter?"100%":this.element.width()/3,height:this.element.find(".e-controlPanel").height()}),this.model.enablePaging&&(this.element.find("#"+this._id+"_Pager").ejPivotPager({locale:this.model.locale,mode:t.PivotPager.Mode.Both,targetControlID:this._id}),this.element.find(".e-controlPanel").height(this.element.find(".e-controlPanel").height()-(this.element.find("#"+this._id+"_Pager").height()+5)),this.element.find(".e-gridContainer").height(this.element.find(".e-gridContainer").height()-(this.element.find("#"+this._id+"_Pager").height()+5)),this.element.find(".e-chartContainer").height(this.element.find(".e-chartContainer").height()-(this.element.find("#"+this._id+"_Pager").height()+10))),o=t.buildTag("div.searchDiv",t.buildTag("input#"+this._id+"_SearchTreeView.searchTreeView").attr("type","text")[0].outerHTML,{margin:"5px 5px 0px 5px"})[0].outerHTML,this.element.find(".parentSchemaFieldTree").prepend(o),this.element.find("#"+this._id+"_SearchTreeView").ejMaskEdit({name:"inputbox",width:"100%",height:"25px",inputMode:t.InputMode.Text,watermarkText:this._getLocalizedLabels("Search"),maskFormat:"",textAlign:this.model.enableRTL?"right":"left",change:t.proxy(t.Pivot._searchTreeNodes,this)}),this.model.enableMeasureGroups&&this._createMeasureGroup(),s=this._clientReportCollection.length>0?n.map(this._clientReportCollection,function(n){return n.reportName!=i?{name:n.reportName}:n.name!=i?{name:n.name}:void 0}):[{name:this.model.dataSource.reportName}],this.element.find(".reportlist").ejDropDownList({dataSource:s,enableRTL:this.model.enableRTL,fields:{text:"name",value:"name"},height:"26px",create:function(){n(this.wrapper.find(".e-input")).focus(function(){n(this).blur()})}}),this.element.find(".reportlist").attr("tabindex",0),this.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&(this._clientReportCollection=[this.model.dataSource]),this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode!=t.Pivot.OperationalMode.ClientMode){var h=this.element.find(".e-csHeader").width()-this.element.find(".cubeText").width()-this.element.find(".e-toggleExpandButton").width()-(this.model.enableSplitter?50:20),l=this.enableTogglePanel()?h-25:h,a=e==""?"":n.parseJSON(e);this.element.find(".cubeSelector").ejDropDownList({dataSource:a,enableRTL:this.model.enableRTL,fields:{text:"name",value:"name"},width:this.model.enableSplitter&&!this.model.isResponsive?"100%":""+l+"px",create:function(){n(this.wrapper.find(".e-input")).focus(function(){n(this).blur()})}});this.ddlTarget=this.element.find(".cubeSelector").data("ejDropDownList");this.ddlTarget.selectItemByText(this.currentCubeName)}this.reportDropTarget=this.element.find("#"+this._id+"_reportList").data("ejDropDownList");this._isReportListAction=!1;this.reportDropTarget.model.dataSource.length&&this.reportDropTarget.selectItemByText(this.reportDropTarget.model.dataSource[0].name);this._isReportListAction=!0;this._selectedReport=this.reportDropTarget._currentText;this.element.find(".cubeSelector").ejDropDownList("option","change",t.proxy(this._cubeChanged,this));this.element.find(".cubeSelector").attr("tabindex",0);this.element.find(".reportlist").ejDropDownList("option","change",t.proxy(this._reportChanged,this));this.model.enableMeasureGroups&&this.element.find(".measureGroupSelector").ejDropDownList("option","change",t.proxy(this._measureGroupChanged,this));this.element.find("#"+this._id+"_clientTab").ejTab({enableRTL:this.model.enableRTL,itemActive:t.proxy(this._onTabClick,this)});this.model.enableSplitter||this.element.find(".e-controlPanel").width(this.element.width()-this.element.find("#"+this._id+"_PivotSchemaDesigner").width()-(this.model.displaySettings.enableTogglePanel?30:10));f=0;this.controlPlacement()=="tile"&&this.displayMode()=="chartandgrid"&&(f=this.element.find("#"+this._id+"_PivotChart").parent().height());this.displayMode()!="chartonly"&&(c=this.element.find("#"+this._id+"_PivotGrid").parent().height(),this.element.find("#"+this._id+"_PivotGrid").ejPivotGrid({locale:this.model.locale,customObject:this.model.customObject,enableRTL:this.model.enableRTL,enableDefaultValue:this.model.enableDefaultValue,enableCellSelection:this.model.enableCellSelection,cellSelection:this.model.cellSelection,hyperlinkSettings:{enableValueCellHyperlink:this.model.enableValueCellHyperlink,enableRowHeaderHyperlink:this.model.enableRowHeaderHyperlink,enableColumnHeaderHyperlink:this.model.enableColumnHeaderHyperlink,enableSummaryCellHyperlink:this.model.enableSummaryCellHyperlink},enableCellContext:this.model.enableCellContext,enableDrillThrough:this.model.enableDrillThrough,enableCellEditing:this.model.enableCellEditing,enableCellDoubleClick:this.model.enableCellDoubleClick,enableCellClick:this.model.enableCellClick,valueCellHyperlinkClick:this.model.valueCellHyperlinkClick,rowHeaderHyperlinkClick:this.model.rowHeaderHyperlinkClick,columnHeaderHyperlinkClick:this.model.columnHeaderHyperlinkClick,summaryCellHyperlinkClick:this.model.summaryCellHyperlinkClick,cellContext:this.model.cellContext,cellEdit:this.model.cellEdit,cellDoubleClick:this.model.cellDoubleClick,enableCellClick:this.model.enableCellClick,drillThrough:this.model.drillThrough,isResponsive:this.model.isResponsive,drillSuccess:t.proxy(this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this._gridDrillSuccess:this._clientGridDrillSuccess,this),enableCollapseByDefault:!0}),this._pivotGrid=this.element.find("#"+this._id+"_PivotGrid").data("ejPivotGrid"),this._pivotGrid.model.operationalMode=this.model.operationalMode,this._pivotGrid.model.analysisMode=this.model.analysisMode,this._pivotGrid._waitingPopup=this._waitingPopup,this._pivotGrid.model.url=this.model.url,this._pivotGrid.model.dataSource=this.model.dataSource,this._pivotGrid.model.valueSortSettings=this.model.valueSortSettings,this.model.enableVirtualScrolling&&this.displayMode()=="chartandgrid"&&this.element.find("#"+this._id+"_PivotGrid").parent().height(c));this.displayMode()!="gridonly"&&(this.element.find("#"+this._id+"_PivotChart").ejPivotChart({locale:this.model.locale,customObject:this.model.customObject,enableRTL:this.model.enableRTL,enableDefaultValue:this.model.enableDefaultValue,axesLabelRendering:this.model.axesLabelRendering,pointRegionClick:this.model.pointRegionClick,canResize:this.model.isResponsive,drillSuccess:t.proxy(this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this._chartDrillSuccess:this._clientChartDrillSuccess,this),size:{height:this._chartHeight,width:this._chartWidth},commonSeriesOptions:{type:this.model.chartType,tooltip:{visible:!0}}}),this._pivotChart=this.element.find("#"+this._id+"_PivotChart").data("ejPivotChart"),this._pivotChart.model.operationalMode=this.model.operationalMode,this._pivotChart.model.analysisMode=this.model.analysisMode,this._pivotChart._waitingPopup=this._waitingPopup,this._pivotChart.model.url=this.model.url,this._pivotChart.model.dataSource=this.model.dataSource,this.model.enableVirtualScrolling&&f>0&&this.element.find("#"+this._id+"_PivotChart").parent().height(f));this._pivotSchemaDesigner=this.element.find("#"+this._id+"_PivotSchemaDesigner").data("ejPivotSchemaDesigner");r={};this._pivotChart!=null&&(this.element.find("#"+this._id+"_PivotChart").width(this._pivotChart.model.size.width),r.chartModelWidth=this._pivotChart.model.size.width);r.controlPanelWidth=this.element.find(".e-controlPanel").width();r.chartOuterWidth=this._chartWidth;r.gridOuterWidth=this._gridWidth;this._initStyles.push(r);this.model.isResponsive&&(this._enableResponsive(),this._parentElwidth=n("#"+this._id).parent().width(),this._parentElwidth<850?this._rwdToggleCollapse():this._parentElwidth>850&&this._rwdToggleExpand(),this.model.enableRTL?this._pivotSchemaDesigner.element.width((this._pivotSchemaDesigner.element.width()-37)/this._pivotSchemaDesigner.element.width()*100+"%"):this._pivotSchemaDesigner.element.width((this._pivotSchemaDesigner.element.width()-27)/this._pivotSchemaDesigner.element.width()*100+"%"),this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()=="chartandgrid"&&this._currentTab=="chart"&&this.element.find(".e-chartContainer").css({width:"99.6%"}));this.enableTogglePanel()&&this.element.find(".pivotFieldList").length>0&&(this.element.find(".e-togglePanel").height(this.element.find("#"+this._id+"_PivotSchemaDesigner").height()).width(14),this.element.find(".e-toggleExpandButton,.e-toggleCollapseButton").css("margin-top",(this.element.find("#"+this._id+"_PivotSchemaDesigner").parents("td:eq(0)").height()-9)/2),this.element.find(".e-togglePanel").children().addClass("e-toggleButtons"));this._trigger("renderSuccess",this);this.model.analysisMode==t.Pivot.AnalysisMode.Olap?(t.olap.base.getJSONData({action:"initialLoad"},this.model.dataSource,this),this.element.find(".e-fieldTable").css("border","none"),this.element.find(".parentSchemaFieldTree").addClass("e-olapFieldList")):this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?(this.displayMode()!=t.PivotClient.DisplayMode.ChartOnly&&(this._pivotGrid.setJSONRecords(this.getJSONRecords().GridJSON),this._pivotGrid.setOlapReport(this.getOlapReport()),this.model.gridLayout=="excellikelayout"?this._pivotGrid.excelLikeLayout(this._pivotGrid.getJSONRecords()):this._pivotGrid.renderControlFromJSON()),this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this._pivotChart.model.enableMultiLevelLabels?this._pivotChart._generateData({JsonRecords:this.getJSONRecords(),PivotReport:this.getOlapReport()}):this._pivotChart.renderControlSuccess({JsonRecords:this.getJSONRecords().ChartJSON,OlapReport:this.getOlapReport()})),this.currentReport=this.getOlapReport()!=""?JSON.parse(this.getOlapReport()).Report:""):(u=t.PivotAnalysis.pivotEnginePopulate(this.model),this.displayMode()!=t.PivotClient.DisplayMode.ChartOnly&&(this._pivotGrid.setJSONRecords(JSON.stringify(u.json)),this.model.gridLayout=="excellikelayout"?this._pivotGrid.excelLikeLayout(u.json):this._pivotGrid.renderControlFromJSON()),this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this._pivotChart.setPivotEngine(u.pivotEngine),this._pivotChart._generateData(u.pivotEngine)));this._unWireEvents();this._wireEvents();!this.model.enableSplitter&&this.model.collapseCubeBrowserByDefault&&(this._collapseCubeBrowser(),this._isCollapseCB=!0)},_createMeasureGroup:function(){var r,i;this.element.find(".measureGroupselector").remove();r="<div class ='measureGroupselector' style='margin:5px 5px 0px 5px'><input type='text' id='"+this._id+"_measureGroupSelector' class='measureGroupSelector' /><\/div>";n(this.element).find(".e-cubeBrowser").prepend(r);this.element.find(".measureGroupSelector").ejDropDownList({dataSource:this.measureGroupInfo,enableRTL:this.model.enableRTL,fields:{text:"name",value:"name"},height:"25px",width:"100%",create:function(){n(this.wrapper.find(".e-input")).focus(function(){n(this).blur()})}});this.element.find(".measureGroupSelector").attr("tabindex",0);i=this.element.find(".measureGroupSelector").data("ejDropDownList");i.selectItemByText(i.model.dataSource[0].name);this.element.find(".measureGroupSelector").ejDropDownList("option","change",t.proxy(this._measureGroupChanged,this))},refreshControl:function(r){var s,o;if(t.isNullOrUndefined(this._pivotSchemaDesigner)||this._pivotSchemaDesigner._refreshPivotButtons(),this.element.find(".e-chartTypesDialog").length>0&&this.element.find(".e-chartTypesDialog").remove(),this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this._pivotChart._labelCurrentTags={}),this.model.operationalMode==t.Pivot.OperationalMode.ServerMode){var u=null,f=null,e=null;r[0]!=i?(u=r[0].Value,f=r[1].Value,e=r[2].Value):t.isNullOrUndefined(r.d)?(u=r.PivotReport,f=r.GridJSON,e=r.ChartJSON):(u=r.d[0].Value,f=r.d[1].Value,e=r.d[2].Value);s=this;this._clientReportCollection=n.map(this._clientReportCollection,function(n){return n.report=n.name==s._currentReportName?JSON.parse(u).Report:n.report,n});this.setJSONRecords({GridJSON:f,ChartJSON:e});this.setOlapReport(u);t.isNullOrUndefined(this._pivotGrid)||(this._pivotGrid.setJSONRecords(f),this._pivotGrid.renderControlFromJSON(),this._pivotGrid._removeCells(r));t.isNullOrUndefined(this._pivotChart)||(this._pivotChart._drillAction&&(this._pivotChart._drillAction=""),this._pivotChart.model.enableMultiLevelLabels&&this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot?(this._pivotChart._drillParams=this._drillParams=[],this._pivotChart._generateData({JsonRecords:r,PivotReport:u})):this._pivotChart.renderControlSuccess({OlapReport:u,JsonRecords:e}))}else this.model.analysisMode==t.PivotChart.AnalysisMode.Pivot?(o=t.PivotAnalysis.pivotEnginePopulate(this.model),this.generateJSON({tranposeEngine:o.pivotEngine,jsonObj:o.json})):t.olap.base.getJSONData({action:"initialLoad"},this.model.dataSource,this);this.model.isResponsive&&this.defaultView()=="chartonly"&&(this.chartObj=this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(this.chartObj)||this.element.find("#"+this.chartObj._id).ejChart("option",{model:{size:{height:this._chartHeight}}}));this.model.enableSplitter&&!t.isNullOrUndefined(this.element.find(".e-childsplit").data("ejSplitter"))&&this.element.find(".e-childsplit").data("ejSplitter").refresh();this._unWireEvents();this._wireEvents();this._isTimeOut=!1},_setFirst:!1,_setModel:function(i){for(var r in i)switch(r){case"title":this.title(t.util.getVal(i[r]));this.element.find(".e-titleText").text(this.title());break;case"currentCubeName":this.currentCubeName=i[r];break;case"gridLayout":this.gridLayout(t.util.getVal(i[r]));this._renderPivotGrid();break;case"olapReport":this.currentReport=i[r];break;case"clientReports":this.reports=i[r];break;case"customObject":this.model.customObject=i[r];break;case"locale":this.locale(t.util.getVal(i[r]));(this._pivotGrid||this._pivotChart)&&this._load();break;case"displaySettings":this._setDisplaySettings(i[r]);(this._pivotGrid||this._pivotChart)&&this._load();break;case"operationalMode":this.model.operationalMode=i[r];break;case"analysisMode":this.model.analysisMode=i[r];break;case"enableRTL":this.model.enableRTL=i[r];this._load();break;case"enableAdvancedFilter":this.model.enableAdvancedFilter=i[r];break;case"enableDefaultValue":this.model.enableDefaultValue=i[r];break;case"enablePaging":this.model.enablePaging=i[r];this._load();break;case"enableSplitter":this.model.enableSplitter=i[r];this._load();break;case"enableToolBar":this.model.enableToolBar=i[r];this._load();break;case"enableLocalStorage":this.model.enableLocalStorage=i[r];break;case"enableVirtualScrolling":this.model.enableVirtualScrolling=i[r];this._load();break;case"enableMemberEditorPaging":this.model.enableMemberEditorPaging=i[r];break;case"enableDrillThrough":this.model.enableDrillThrough=i[r];this._load();break;case"showUniqueNameOnPivotButton":this.model.showUniqueNameOnPivotButton=i[r];this._load();break;case"isResponsive":this.model.isResponsive=i[r];this._load();break;case"collapseCubeBrowserByDefault":this.model.collapseCubeBrowserByDefault=i[r];this._load();break;case"dataSource":this.model.dataSource=n.extend({},this.model.dataSource,i[r]);this._load();break;case"enableMeasureGroups":this.model.enableMeasureGroups=i[r];this._load();break;case"size":this.model.size=n.extend({},this.model.size,i[r]);this._load();break;case"toolbarIconSettings":this.model.toolbarIconSettings=n.extend({},this.model.toolbarIconSettings,i[r]);this._load();break;case"sortCubeMeasures":this.model.sortCubeMeasures=i[r]}},_setDisplaySettings:function(i){!t.isNullOrUndefined(t.util.getVal(i).displayMode)&&n.isFunction(t.util.getVal(i).displayMode)?this.displayMode(i.displayMode()):t.isNullOrUndefined(i.displayMode)||this.displayMode(i.displayMode);!t.isNullOrUndefined(t.util.getVal(i).defaultView)&&n.isFunction(t.util.getVal(i).defaultView)?this.defaultView(i.defaultView()):t.isNullOrUndefined(i.defaultView)||this.defaultView(i.defaultView);!t.isNullOrUndefined(t.util.getVal(i).controlPlacement)&&n.isFunction(t.util.getVal(i).controlPlacement)?this.controlPlacement(i.controlPlacement()):t.isNullOrUndefined(i.controlPlacement)||this.controlPlacement(i.controlPlacement);!t.isNullOrUndefined(t.util.getVal(i).enableTogglePanel)&&n.isFunction(t.util.getVal(i).enableTogglePanel)?this.enableTogglePanel(i.enableTogglePanel()):t.isNullOrUndefined(i.enableTogglePanel)||this.enableTogglePanel(i.enableTogglePanel)},_getMeasuresList:function(){var t="";return this.element.find(".e-memberEditorDiv").find("div").each(function(){t+=n(this)[0].id+","}),t},_getUnSelectedNodes:function(){var i="",n;if(this._currentItem.indexOf("Measures")<0)for(n=0;n<this._memberTreeObj.dataSource().length;n++)this._memberTreeObj.dataSource()[n].checkedStatus==!1&&(i+="::"+this._memberTreeObj.dataSource()[n].id+"||"+this._memberTreeObj.dataSource()[n].tag+(this._currentItem.indexOf(this._getLocalizedLabels("KPIs"))==0?!t.isNullOrUndefined(this._memberTreeObj.dataSource()[n].pid)||!t.isNullOrUndefined(this._memberTreeObj.dataSource()[n].parentId)?"~&"+(this._memberTreeObj.dataSource()[n].pid||this._memberTreeObj.dataSource()[n].parentId):"~&":""));return i},_getSelectedNodes:function(i){var u,o,s,e,f,r;if(i){var c=this.element.find(".e-editorTreeView")[0].childNodes[0],f=[],h=n(c).children();for(r=0;r<h.length;r++){if(u=h[r],o={caption:n(u.firstChild).find("a").text(),parentId:u.parentElement.parentElement.className.indexOf("e-editorTreeView")>-1?"None":n(u).parents()[1].id,id:u.id,checked:n(u).find(":input.nodecheckbox")[0].checked||n(u).find("span:nth-child(1)").attr("class").indexOf("e-chk-indeter")>-1,expanded:n(u.firstChild).find(".e-minus").length>0?!0:!1,childNodes:[],tag:n(u).attr("data-tag")},n(u).find("ul:first").children().length>0)for(s=n(u).find("ul:first").children(),e=0;e<s.length;e++)o.childNodes.push(this._getNodeInfo(s[e]));f.push(o)}return JSON.stringify(f)}if(f="",this._currentItem.indexOf(this._getLocalizedLabels("Measures"))<0)for(r=0;r<this._memberTreeObj.dataSource().length;r++)this._memberTreeObj.dataSource()[r].checkedStatus==!0&&(f+="::"+this._memberTreeObj.dataSource()[r].id+"||"+this._memberTreeObj.dataSource()[r].tag+(this._currentItem.indexOf(this._getLocalizedLabels("KPIs"))==0?!t.isNullOrUndefined(this._memberTreeObj.dataSource()[r].pid)||!t.isNullOrUndefined(this._memberTreeObj.dataSource()[r].parentId)?"~&"+(this._memberTreeObj.dataSource()[r].pid||this._memberTreeObj.dataSource()[r].parentId):"":""));return f},_getNodeInfo:function(t){var u={caption:n(t.firstChild).find("a").text(),parentId:t.parentElement.parentElement.className.indexOf("e-editorTreeView")>-1?"None":n(t).parents()[1].id,id:t.id,checked:n(t).find(":input.nodecheckbox")[0].checked||n(t).find("span:nth-child(1)").attr("class").indexOf("e-chk-indeter")>-1,expanded:n(t.firstChild).find(".e-minus").length>0?!0:!1,childNodes:[],tag:n(t).attr("data-tag")},r,i;if(n(t).find("ul:first").children().length>0)for(r=n(t).find("ul:first").children(),i=0;i<r.length;i++)u.childNodes.push(this._getNodeInfo(r[i]));return u},_removeSplitBtn:function(){var i=n(document).find(".dragClone"),u,f=this,o=this.element.find(".e-splitBtn"),r,e;jQuery.each(o,function(t,r){n(n(r).children()[0]).attr("title")==n(i).attr("title")&&(u=n(r).attr("data-tag"),n(r).remove())});this._currentReportItems.length!=0&&this._treeViewData.hasOwnProperty(n(i).attr("title"))&&(delete this._treeViewData[n(i).attr("title")],this._currentReportItems.splice(n.inArray(n(i).attr("title"),this._currentReportItems),1));this.model.enableAdvancedFilter&&this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&(r=u.split(":")[1].split("."),t.isNullOrUndefined(r)||r.length!=2||(this._setUniqueNameFrmBtnTag(r),this._removeFilterTag(this._selectedFieldName)));n(".dragClone").remove();this._isTimeOut=!0;setTimeout(function(){f._isTimeOut&&f._waitingPopup.show()},800);this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:"removeSplitButton",element:this.element,customObject:this.model.customObject});e=JSON.stringify(this.model.customObject);this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.removeSplitButton,JSON.stringify({action:"removeSplitButton",clientParams:u,olapReport:this.currentReport,clientReports:this.reports,customObject:e}),this._removeSplitButtonSuccess);this._isNodeOrButtonDropped=!0},_removeDialog:function(t){t.target.className.indexOf("e-chartTypesImg")==-1&&n(".e-chartTypesDialog").remove();t.target.className.indexOf("e-reportDBImg")==-1&&n(".e-reportDBDialog").remove();t.target.className.indexOf("e-exportImg")==-1&&n(".e-exportTypesDialog").remove();t.target.className.indexOf("e-chart3DImg")==-1&&n(".e-chart3DTypesDialog").remove();t.target.className.indexOf("smartLabels")==-1&&n(".e-smartLabelsDialog").remove();t.target.className.indexOf("e-interaction")==-1&&n(".e-interactionsDialog").remove()},_wireEvents:function(){this._wireDialogEvent();this._wireEditorRemoveEvent();this._wireMeasureRemoveEvent();this._on(n(document),"click",this._removeDialog);this._on(n(document),"keydown",this._keyPressDown);this._on(n(document),"keyup",this._keyPressUp);this._on(this.element.find("a.e-linkPanel"),"click",t.Pivot._editorLinkPanelClick);this.element.find("#"+this._id+"_sep1").hover(function(){n(this).removeClass("e-mhover")});this.element.find(".e-categoricalAxis, .e-rowAxis, .e-slicerAxis").find("button").ejDraggable({handle:"button",clone:!0,cursorAt:{top:-10,left:-10},dragStart:function(i){var r=this.element.parents(".e-pivotclient").data("ejPivotClient");i.event.preventDefault();n(this.element.find(".e-txt")).off("touchstart");n(this.element.find(".e-txt")).off(t.eventType.click);r.isDragging=!0;this.element.find(".e-dialog").hide();r.draggedSplitBtn=i.event.target},dragStop:function(t){var u=n(this.element).parents(".e-pivotclient").data("ejPivotClient"),r,e,f;this.element.find(".targetAxis").removeClass("targetAxis");u.isDragging=!1;r=null;e=t.event.type=="touchend"?u._setSplitBtnTargetPos(t):u._setSplitBtnTargetPos(t.event);u._dropAxisClassName!=i&&u._dropAxisClassName!=""?(u._dropAxisClassName=="outOfAxis"?u._removeSplitBtn():r=u._dropAxisClassName,u._dropAxisClassName=""):t.target!=i?n(t.target).hasClass("e-btn")||n(t.target).hasClass("e-removeSplitBtn")?r=n(t.target).parents("div:eq(1)").attr("class"):jQuery.type(t.target.className)!="string"?u._removeSplitBtn():r=t.target.className.indexOf("e-splitBtn")>-1?n(t.target).parents("div:eq(0)").attr("class"):t.target.className:r=n(t.event.originalEvent.srcElement).hasClass("e-btn")||n(t.event.originalEvent.srcElement).hasClass("e-removeSplitBtn")?n(t.event.originalEvent.srcElement).parents("div:eq(1)").attr("class"):t.event.originalEvent.srcElement.className.indexOf("e-splitBtn")>-1?n(t.event.originalEvent.srcElement).parents("div:eq(0)").attr("class"):t.event.originalEvent.srcElement.className;r!=i&&r!=null&&(f=r.indexOf("e-categoricalAxis")>r.indexOf("e-rowAxis")?r.indexOf("e-categoricalAxis")>r.indexOf("e-slicerAxis")?"Categorical":"Slicer":r.indexOf("e-rowAxis")>r.indexOf("e-slicerAxis")?"Series":r.indexOf("e-slicerAxis")>=0?"Slicer":null,f!=null?u._splitButtonDropped(f,e):u._removeSplitBtn(),u._setSplitBtnTitle())},helper:function(t){var i=n(t.sender.target).parents(".e-pivotclient").data("ejPivotClient");return t.sender.target.className.indexOf("e-btn")>-1?n(t.sender.target).clone().addClass("dragClone").appendTo(i.element):!1}});this.element.find(".e-nextPage, .e-prevPage, .e-firstPage, .e-lastPage").click(function(i){var r=n(this).parents(".e-pivotclient").data("ejPivotClient");t.Pivot.editorTreeNavigatee(i,r)});this.element.find(".e-categoricalAxis, .e-rowAxis, .e-slicerAxis").mouseover(function(){var t=n(this).parents(".e-pivotclient").data("ejPivotClient");t.isDragging&&n(this).addClass("targetAxis")});this.element.find(".e-categoricalAxis, .e-rowAxis, .e-slicerAxis").mouseleave(function(){n(this).removeClass("targetAxis")});this.element.find(".e-btn").mouseover(function(t){n(t.target.parentNode).find("span").css("display","inline")});!t.isNullOrUndefined(this._pivotGrid)&&this.model.enableDrillThrough&&(this._pivotGrid.model.enableDrillThrough=!0,n.proxy(this._pivotGrid._addHyperlink,this));this.element.find(".e-splitBtn").mouseover(function(){var t=n(this).parents(".e-pivotclient").data("ejPivotClient");t.isDragging&&n(this).addClass("e-dropIndicator")});this.element.find(".e-splitBtn").mouseleave(function(){n(this).removeClass("e-dropIndicator")});this._on(this.element,"click","#preventDiv",t.proxy(function(){this.element.find(".e-dialog.e-advancedFilterDlg:visible").length>0&&(this.element.find(".e-dialog.e-advancedFilterDlg").hide(),this.element.find("#preventDiv").remove())},this));this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode||this._on(this.element,"mouseover",".e-pvtBtn",t.proxy(function(t){var i=n(t.currentTarget).parents(".e-pivotclient").data("ejPivotClient");(i.isDragging||i._pivotSchemaDesigner._isDragging)&&n(t.target).siblings(".e-dropIndicator").addClass("e-dropIndicatorActive")},this));this._on(this.element,"mouseleave",".e-pvtBtn",t.proxy(function(t){n(t.target).siblings(".e-dropIndicator").removeClass("e-dropIndicatorActive")},this));this.element.find(".e-sortDisable, .e-sortEnable, .e-filterDisable, .e-filterEnable ").on(t.eventType.click,function(t){var i=n(this).parents(".e-pivotclient").data("ejPivotClient");t.target.className=="e-sortDisable"?(n(".measuresList_wrapper,.e-radioBtnAsc, .e-radioBtnDesc, .e-preserveHrchy").attr("disabled","disabled"),n(".e-measureListLbl, .e-orderLbl, .e-radioBtnAscLbl, .e-radioBtnDescLbl, .e-preserveHrchyLbl").addClass("e-sortFilterDisable"),i._dllSortMeasure.disable()):t.target.className=="e-sortEnable"?(n(".measuresList_wrapper,.e-radioBtnAsc, .e-radioBtnDesc, .e-preserveHrchy").removeAttr("disabled"),n(".e-measureListLbl, .e-orderLbl, .e-radioBtnAscLbl, .e-radioBtnDescLbl, .e-preserveHrchyLbl").removeClass("e-sortFilterDisable"),i._dllSortMeasure.enable()):t.target.className=="e-filterDisable"?(n(".filterFrom, .filterTo").attr("disabled","disabled"),n(".filterMeasureListLbl, .e-conditionLbl, .e-filterValueLbl, .e-filterBtw").addClass("e-sortFilterDisable"),i._dllFilterCondition.disable(),i._dllfMeasuresList.disable()):t.target.className=="e-filterEnable"&&(n(".filterFrom, .filterTo").removeAttr("disabled"),n(".filterMeasureListLbl, .e-conditionLbl, .e-filterValueLbl, .e-filterBtw").removeClass("e-sortFilterDisable"),i._dllFilterCondition.enable(),i._dllfMeasuresList.enable())});this.element.find(".filterFrom , .filterTo").keypress(function(t){return t.which==8||t.which==0?!0:t.which<46||t.which>58?!1:t.which==46&&n(this).val().indexOf(".")!=-1||t.which==47?!1:void 0});this.element.find(".e-toggleExpandButton").click(function(){var t=n(this).parents(".e-pivotclient").data("ejPivotClient");t._isCollapseCB=!0;t._collapseCubeBrowser()});this.element.find(".e-toggleCollapseButton").click(function(){var i=n(this).parents(".e-pivotclient").data("ejPivotClient"),r;i._isCollapseCB=!1;i.model.isResponsive?(i._rwdToggleExpand(),i.model.enableSplitter&&(i.model.analysisMode==t.Pivot.AnalysisMode.Olap&&i.model.operationalMode==t.Pivot.OperationalMode.ServerMode?i.element.find(".e-serversplitresponsive").data("ejSplitter").refresh():i.element.find(".e-splitresponsive").data("ejSplitter").refresh())):(r=i.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&i.model.analysisMode==t.Pivot.AnalysisMode.Olap,i._toggleExpand=!1,i._performToggleAction(i._initStyles),i.element.find(".e-csHeader, .e-cubeTable,.e-toggleExpandButton, .pivotFieldList").show(),i.element.find(".e-toggleCollapseButton").hide(),i.element.find(".e-toggleText").hide(),i.displayMode()!=t.PivotClient.DisplayMode.GridOnly||r?i.displayMode()==t.PivotClient.DisplayMode.GridOnly&&r?i.element.find(".e-gridContainer").width(i.element.find(".e-controlPanel").width()-2):i.displayMode()==t.PivotClient.DisplayMode.ChartOnly?i.element.find(".e-chartContainer").width(i.element.find(".e-controlPanel").width()-6):i.controlPlacement()==t.PivotClient.ControlPlacement.Tile?i.defaultView()==t.PivotClient.DefaultView.Grid?(i.element.find(".e-gridContainer").width(i.element.find(".e-controlPanel").width()-3),i.element.find(".e-chartContainer").width(i.element.find(".e-controlPanel").width()-8)):(i.element.find(".e-gridContainer").width(i.element.find(".e-controlPanel").width()-3),i.element.find(".e-chartContainer").width(i.element.find(".e-controlPanel").width()-8)):i.controlPlacement()==t.PivotClient.ControlPlacement.Tab&&(i.defaultView()==t.PivotClient.DefaultView.Grid?(i.element.find(".e-gridContainer").width(i.element.find(".e-controlPanel").width()-2),i.element.find(".e-chartContainer").width(i.element.find(".e-controlPanel").width()-7)):(i.element.find(".e-gridContainer").width(i.element.find(".e-controlPanel").width()-2),i.element.find(".e-chartContainer").width(i.element.find(".e-controlPanel").width()-7))):i.element.find(".e-gridContainer").width(i.element.find(".e-controlPanel").width()),i.model.enableToolBar&&(i.element.find("#"+this._id+"_PivotCharttoolBar").width(i.element.find(".e-chartContainer").width()),i.element.find("#"+this._id+"_PivotGridToolbar").width(i.element.find(".e-gridContainer").width()-10),i.element.find(".e-chartToolBar").width(i.element.find(".e-chartContainer").width()),i.element.find(".e-toolBar").width(i.element.find(".e-gridContainer").width())),i.model.enablePivotTreeMap&&i.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(i.chartObj=null,i.chartObj=i.element.find("#"+i._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(i.chartObj)&&!t.isNullOrUndefined(i.otreemapObj)&&(i.chartObj=i.element.find("#"+i.otreemapObj._id+"TreeMapContainer").data("ejTreeMap")),t.isNullOrUndefined(i.chartObj)||(i.chartObj.sfType.split(".").pop().toLowerCase()=="treemap"?i.otreemapObj._treeMap.refresh():i.chartObj.redraw())))});this._on(this.element,"click",".e-memberAscendingIcon, .e-memberDescendingIcon",t.proxy(t.Pivot._memberSortBtnClick,this));this.element.find(".maximizedView").click(function(){var t=n(this).parents(".e-pivotclient").data("ejPivotClient");t._maxViewBtnClick()});n(document).on("click",".e-winCloseBtn",function(t){var i=n("#"+n(t.target).attr("clientID")).data("ejPivotClient");i._maxViewClsBtnClick()});if(this.model.isResponsive)n(window).on("resize",n.proxy(this._reSizeHandler,this));if(this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&this.model.enableSplitter){var r=this;this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?r.element.find(".e-shadowbar").parent(".e-serverchildsplit").length>0||r.element.find(".e-serverparentsplit").mouseup(function(){r._splitterChartResizing(r)}):r.element.find(".e-shadowbar").parent(".e-childsplit").length>0||r.element.find(".e-parentsplit").mouseup(function(){r._splitterChartResizing(r)})}},_collapseCubeBrowser:function(){var n,u;if(this.model.isResponsive)this._rwdToggleCollapse();else{if(n=this.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.analysisMode==t.Pivot.AnalysisMode.Olap,this._toggleExpand=!0,this._initToggle){this._initToggle=!1;var i={},f=n||this.controlPlacement()==t.PivotClient.ControlPlacement.Tile?0:23,r=(this.element.find(".e-cubeTable").width()||this.element.find(".pivotFieldList").width())+this.element.find(".e-controlPanel").width()-17;i.controlPanelWidth=(this.displayMode()==t.PivotClient.DisplayMode.ChartAndGrid)?r-(this.model.collapseCubeBrowserByDefault&&n&&this.controlPlacement()==t.PivotClient.ControlPlacement.Tab?0:5)+f:r-(this.model.collapseCubeBrowserByDefault&&n&&this.controlPlacement()==t.PivotClient.ControlPlacement.Tab?0:3)+f;i.chartOuterWidth=i.chartModelWidth=i.gridOuterWidth=r-(this.defaultView()==t.PivotClient.DefaultView.Grid?24:-3);this._toggleStyles.push(i)}this.displayMode()!=t.PivotClient.DisplayMode.GridOnly||n?this.displayMode()==t.PivotClient.DisplayMode.GridOnly&&n?this.element.find(".e-gridContainer").width(this.element.width()-39):this.displayMode()!=t.PivotClient.DisplayMode.ChartOnly||n?this.displayMode()==t.PivotClient.DisplayMode.ChartOnly&&n?this.element.find(".e-chartContainer").width(this.element.width()-50):this.controlPlacement()==t.PivotClient.ControlPlacement.Tile?this.defaultView()==t.PivotClient.DefaultView.Grid?(u=n?7:0,this.element.find(".e-gridContainer").width(this.element.width()-(33+u)),this.element.find(".e-chartContainer").width(this.element.width()-(38+u))):(this.element.find(".e-gridContainer").width(this.element.width()-33),this.element.find(".e-chartContainer").width(this.element.width()-38)):this.controlPlacement()==t.PivotClient.ControlPlacement.Tab&&(n?(this.element.find(".e-gridContainer").width(this.element.width()-42),this.element.find(".e-chartContainer").width(this.element.width()-47)):this.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&this.model.analysisMode==t.Pivot.AnalysisMode.Olap?(this.element.find(".e-gridContainer").width(this.element.width()-30),this.element.find(".e-chartContainer").width(this.element.width()-35)):(this.element.find(".e-gridContainer").width(this.element.width()-32),this.element.find(".e-chartContainer").width(this.element.width()-37))):this.element.find(".e-chartContainer").width(this.element.width()-36):this.element.find(".e-gridContainer").width(this.element.width()-30);this._performToggleAction(this._toggleStyles);this.element.find(".e-csHeader, .e-cubeTable,.e-toggleExpandButton, .pivotFieldList").hide();this.element.find(".e-toggleCollapseButton").show();this.element.find(".e-toggleText").show();this.model.enablePivotTreeMap&&this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this.chartObj=null,this.chartObj=this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(this.chartObj)&&!t.isNullOrUndefined(this.otreemapObj)&&(this.chartObj=this.element.find("#"+this.otreemapObj._id+"TreeMapContainer").data("ejTreeMap")),t.isNullOrUndefined(this.chartObj)||(this.chartObj.sfType.split(".").pop().toLowerCase()=="treemap"?this.otreemapObj._treeMap.refresh():this.chartObj.redraw()))}},_keyPressUp:function(n){if(n.keyCode===93&&!this.element.find(".e-dialog:visible").length>0&&this.element.find(".e-hoverCell:visible").length>0&&(n.preventDefault(),!t.isNullOrUndefined(this._curFocus.tab)&&this._curFocus.tab.hasClass("e-text"))){var i={x:this.element.find(".e-hoverCell").offset().left+this.element.find(".e-hoverCell").outerWidth(),y:this.element.find(".e-hoverCell").offset().top+this.element.find(".e-hoverCell").outerHeight()};this.element.find(".e-hoverCell").trigger({type:"mouseup",which:3,clientX:i.x,clientY:i.y,pageX:i.x,pageY:i.y})}},_splitterChartResizing:function(t){var i,r;t.element.find(".e-shadowbar").length>0&&(i=t.element.find(".e-controlPanel").width(),t.element.find(".e-shadowbar").parents(".e-parentsplit")?(t.element.find(".e-shadowbar").parent(".e-parentsplit").length>0||t.element.find(".e-shadowbar").parent(".e-serverparentsplit").length>0)&&(r=t.model.enableRTL?i+(t.element.find(".e-shadowbar").offset().left-t.element.find(".e-split-divider").offset().left):i+(t.element.find(".e-controlPanel").offset().left-t.element.find(".e-shadowbar").offset().left)):r=i,n("#"+t._pivotChart._id+"Container").width(r-15),t.chartObj=null,t.chartObj=t.element.find("#"+t._pivotChart._id+"Container").data("ejChart"),t.chartObj.redraw())},_createSplitter:function(){this.model.isResponsive?this.model.isResponsive&&this.element.find(".e-splitresponsive, .e-serversplitresponsive").ejSplitter({cssClass:"customCSS",isResponsive:!0,height:this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this.element.height()-((this.element.find("div.e-titleText").length>0?50:0)+this.element.find("#"+this._id+"_reportToolbar").height()+this.element.find(".e-csHeader").height()+25):"",properties:[{expandable:!1,collapsible:!1,paneSize:"48%"},{enableAutoResize:!0,collapsible:!1}]}):(this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.enableVirtualScrolling||this.element.find(".e-parentsplit, .e-serverparentsplit").ejSplitter({cssClass:"customCSS",isResponsive:!0,enableRTL:this.model.enableRTL,height:this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this.element.height()-((this.element.find("div.e-titleText").length>0?50:0)+this.element.find("#"+this._id+"_reportToolbar").height()+5):this.element.find(".e-outerTable").height(),properties:[{expandable:!1,collapsible:!1,paneSize:"50%",maxSize:this.model.enablePaging?"400%":""},{enableAutoResize:!0,collapsible:!1,paneSize:"50%",minSize:this.model.enableRTL?225:""}],resize:function(){var i=this.element.find(".e-childsplit, .e-serverchildsplit").data("ejSplitter");pivotClientObj=n(this.element).parents(".e-pivotclient").data("ejPivotClient");i.option("properties",[{expandable:!1,collapsible:!1,enableAutoResize:!0,paneSize:"50%",enableRTL:this.model.enableRTL},{paneSize:"50%",collapsible:!1}]);pivotClientObj.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&pivotClientObj.element.find(".e-fieldTable").css("height",pivotClientObj.element.find(".outerTable").height()-13+"px");pivotClientObj.model.enableRTL?(pivotClientObj.element.find(".e-childsplit.e-rtl > span.e-splitbar.e-h-bar").css("left","-5.5px"),pivotClientObj.element.find(".e-serverchildsplit.e-rtl > span.e-splitbar.e-h-bar").css("left","-2.5px")):(pivotClientObj.element.find(".e-childsplit > span.e-splitbar.e-h-bar").css("left","5.5px"),pivotClientObj.element.find(".e-splitresponsive > span.e-splitbar.e-h-bar").css("left","5.5px"))}}),this.element.find(".e-childsplit, .e-serverchildsplit").ejSplitter({cssClass:"customCSS",height:this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this.element.height()-((this.element.find("div.e-titleText").length>0?50:0)+this.element.find("#"+this._id+"_reportToolbar").height()+this.element.find(".e-csHeader").height()+(this.model.enableVirtualScrolling?25:15)):"",isResponsive:!0,enableRTL:this.model.enableRTL,properties:[{expandable:!1,collapsible:!1,enableAutoResize:!0,paneSize:"50%",enableRTL:this.model.enableRTL},{paneSize:"50%",collapsible:!1}]}),this.model.enableRTL&&(this.element.find(".e-serverparentsplit.e-rtl > span.e-splitbar.e-h-bar").css("left","-3.5px"),this.element.find(".e-serverchildsplit.e-rtl > span.e-splitbar.e-h-bar").css("left","-3.5px")));t.browserInfo().name=="msie"&&(t.browserInfo().version=="8.0"||t.browserInfo().version=="9.0")&&this.element.find(".e-splitbar.e-h-bar").css("height","inherit");this.model.enableRTL?this.element.find(".e-childsplit.e-rtl > span.e-splitbar.e-h-bar").css("left","-5.5px"):(this.element.find(".e-childsplit>span.e-splitbar.e-h-bar").css("left","5.5px"),this.element.find(".e-splitresponsive>span.e-splitbar.e-h-bar").css("left","5.5px"))},_keyPressDown:function(i){var u,o,f,e,r;if(t.isNullOrUndefined(this._curFocus.button)?t.isNullOrUndefined(this._curFocus.tab)||(u=this._curFocus.tab):u=this._curFocus.button,i.keyCode===93&&!t.isNullOrUndefined(u)&&!this.element.find(".e-dialog:visible").length>0&&this.element.find(".e-hoverCell:visible").length>0&&(i.preventDefault(),o={x:n(u).offset().left+n(u).outerWidth(),y:n(u).offset().top+n(u).outerHeight()},u.trigger({type:"mouseup",which:3,clientX:o.x,clientY:o.y,pageX:o.x,pageY:o.y})),(i.which===46||i.which===82&&i.ctrlKey)&&!this.element.find(".e-dialog:visible").length>0&&!t.isNullOrUndefined(u)?(i.preventDefault(),u.parent().find(".e-removeSplitBtn:visible").click()):(i.which===46||i.which===82&&i.ctrlKey&&(!t.isNullOrUndefined(this._curFocus.dialog)&&this._curFocus.dialog.hasClass("e-measureEditor")||!t.isNullOrUndefined(this._curFocus.editor)&&this._curFocus.editor.hasClass("e-measureEditor")))&&(t.isNullOrUndefined(this._curFocus.editor)?t.isNullOrUndefined(this._curFocus.dialog)||this._curFocus.dialog.find(".e-removeMeasure:visible").click():this._curFocus.editor.find(".e-removeMeasure:visible").click()),i.keyCode==79&&this.element.find(".e-dialog:visible").length>0&&i.ctrlKey&&(i.preventDefault(),!t.isNullOrUndefined(this._curFocus.icon)&&this._curFocus.icon.hasClass("e-mdxImg")?this.element.find(".e-dialogCancelBtn:visible").click():this.element.find(".e-dialogOKBtn:visible").click(),this._curFocus.dialog=null),i.which===70&&i.ctrlKey&&!t.isNullOrUndefined(u)&&u.click(),i.keyCode==67&&this.element.find(".e-dialog:visible").length>0&&this.element.find(".e-dialog .e-titlebar").attr("data-tag")!="MDX Query"&&i.ctrlKey&&(i.preventDefault(),this.element.find(".e-dialogCancelBtn:visible").click(),this._curFocus.dialog=null),this.element.find(".e-dialog:visible").length>0&&i.keyCode==27?(t.isNullOrUndefined(this._curFocus.icon)?t.isNullOrUndefined(this._curFocus.editor)?t.isNullOrUndefined(this._curFocus.dialog)||this._curFocus.dialog.attr("tabindex","-1").addClass("e-hoverCell").focus().mouseover():this._curFocus.editor.attr("tabindex","-1").addClass("e-hoverCell").focus().mouseover():this._curFocus.icon.attr("tabindex","-1").addClass("e-hoverCell").focus().mouseover(),this._curFocus.dialog=null,this._index.editor=0):this.element.find(".e-dialog:visible").length==0&&i.keyCode==27&&n(".e-fullScreenView").length!=0?(this._maxViewClsBtnClick(),t.isNullOrUndefined(this._pivotGrid._curFocus.cell)?this._curFocus.tab.hasClass("e-icon")||this._curFocus.tab.focus().mouseover():this._pivotGrid._curFocus.cell.focus().mouseover()):this.element.find(".e-chartTypesDialog:visible").length>0&&i.which===27?(this.element.find(".e-chartTypesDialog").remove(),this._curFocus.chartimg=null):n(".pivotTree:visible,.pivotTreeContext:visible").length>0&&i.which===27&&(t.isNullOrUndefined(this._curFocus.tree)?t.isNullOrUndefined(u)||u.attr("tabindex","-1").focus().addClass("e-hoverCell"):this._curFocus.tree.attr("tabindex","-1").focus().addClass("e-hoverCell")),(i.which===9&&i.shiftKey||i.which===9)&&this.element.find(".e-dialog:visible").length>0?(i.preventDefault(),this._curFocus.editor=null,this.element.find(".e-dialog .e-hoverCell:visible").removeClass("e-hoverCell"),f=!t.isNullOrUndefined(this._dllSortMeasure)&&this._dllSortMeasure.model.enabled&&this.element.find("#"+this._id+"_measuresList_container:visible").length>0||!t.isNullOrUndefined(this._dllfMeasuresList)&&this._dllfMeasuresList.model.enabled&&this.element.find("#"+this._id+"_fMeasuresList_container:visible").length>0?this.element.find(".e-dialog .e-filterEnable:visible,#"+this._id+"_measuresList_container:visible,#"+this._id+"_fMeasuresList_container:visible,#"+this._id+"_filterCondition_container:visible,.e-dialog .filterFrom:visible:not([disabled='disabled']),.e-dialog .filterTo:visible:not([disabled='disabled']),.e-dialog .e-radioBtnAsc:visible:not([disabled='disabled']),.e-dialog .e-preserveHrchy:visible:not([disabled='disabled']),.e-dialog .e-dialogOKBtn:visible:not([aria-disabled='true']),.e-dialog .e-dialogCancelBtn:visible,.e-dialog .e-close:visible,.e-dialog .e-checkAll:visible,.e-dialog .e-unCheckAll:visible,.e-dialog .e-sortfiltTab .e-active:visible,.e-dialog .e-measureEditor:visible:first,.e-dialog .e-text:visible:first,.e-dialog .e-sortEnable:visible,.e-dialog .reportName:visible,#"+this._id+"_reportNameList_container:visible"):this.element.find(".e-dialog .e-filterEnable:visible,.e-dialog .filterFrom:visible:not([disabled='disabled']),.e-dialog .filterTo:visible:not([disabled='disabled']),.e-dialog .e-radioBtnAsc:visible:not([disabled='disabled']),.e-dialog .e-preserveHrchy:visible:not([disabled='disabled']),.e-dialog .e-dialogOKBtn:visible:not([aria-disabled='true']),.e-dialog .e-dialogCancelBtn:visible,.e-dialog .e-close:visible,.e-dialog .e-checkAll:visible,.e-dialog .e-unCheckAll:visible,.e-dialog .e-sortfiltTab .e-active:visible,.e-dialog .e-measureEditor:visible:first,.e-dialog .e-text:visible:first,.e-dialog .e-sortEnable:visible,.e-dialog .reportName:visible,#"+this._id+"_reportNameList_container:visible"),t.isNullOrUndefined(this._curFocus.dialog)?(this._index.dialog=f.length>4?4:2,this._curFocus.dialog=f.eq(this._index.dialog)):(this._curFocus.dialog.attr("tabindex","0").removeClass("e-hoverCell").mouseleave(),i.which===9&&i.shiftKey?this._index.dialog=this._index.dialog-1<0?f.length-1:this._index.dialog-1:i.which===9&&(this._index.dialog=this._index.dialog+1>f.length-1?0:this._index.dialog+1),this._curFocus.dialog=f.eq(this._index.dialog)),this._curFocus.dialog.hasClass("e-input")||this._curFocus.dialog.attr("type")=="radio"?this._curFocus.dialog.attr("tabindex","-1").focus():this._curFocus.dialog.attr("tabindex","-1").focus().addClass("e-hoverCell").mouseover):(i.which===9&&i.shiftKey||i.which===9)&&!n(".pivotTree:visible,.pivotTreeContext:visible").length>0&&(i.preventDefault(),this.element.find(".e-hoverCell").removeClass("e-hoverCell").mouseleave(),this.element.find(".e-node-focus").removeClass("e-node-focus"),this._index.button=1,this._index.icon=1,this._index.chartimg=0,this._index.tree=0,this._index.dialog=0,this._curFocus.button=null,this._curFocus.icon=null,this._curFocus.chartimg=null,this._curFocus.tree=null,this._curFocus.editor=null,this._curFocus.dialog=null,n(".e-fullScreenView").length!=0?n("[role='columnheader']:visible:not([p='0,0'])").first().length>0&&(f=n("[role='columnheader']:visible:not([p='0,0']):first")):f=this.element.find("[role='columnheader']:visible:not([p='0,0']):first,.e-schemaFieldTree .e-text:visible:first,.e-reportToolbar li:visible:first,#"+this._id+"_reportList_wrapper:visible,#"+this._id+"_cubeList_wrapper:visible,#"+this._id+"_cubeSelector_wrapper:visible, .e-cubeTreeView .e-text:visible:first,.e-categoricalAxis button:visible:first,.e-rowAxis button:visible:first,.e-slicerAxis button:visible:first,.e-schemaColumn button:visible:first,.schemaRow button:visible:first,.schemaFilter button:visible:first,.e-schemaValue button:visible:first,.e-toggleExpandButton:visible:first,.e-toggleCollapseButton:visible:first,#"+this._id+"_clientTab .e-active:visible"),t.isNullOrUndefined(this._curFocus.tab)?(this._index.tab=0,this._curFocus.tab=f.eq(this._index.tab).attr("tabindex","-1").focus().addClass("e-hoverCell").mouseover(),this._curFocus.tab.hasClass("e-button")&&this._curFocus.tab.parent().find(".e-removeSplitBtn:visible").addClass("e-hoverCell")):(this._curFocus.tab.attr("tabindex","0").removeClass("e-hoverCell").mouseleave(),i.which===9&&i.shiftKey?this._index.tab=this._index.tab-1<0?f.length-1:this._index.tab-1:i.which===9&&(this._index.tab=this._index.tab+1>f.length-1?0:this._index.tab+1),this._curFocus.tab=f.eq(this._index.tab).attr("tabindex","-1").focus().addClass("e-hoverCell").mouseover(),this._curFocus.tab.hasClass("e-button")&&this._curFocus.tab.parent().find(".e-removeSplitBtn:visible").addClass("e-hoverCell"))),this.element.find(".e-dialog:visible").length>0&&i.keyCode==13){if(n(i.target).parents(".e-pivotschemadesigner").length==0&&(n(i.target).hasClass("e-memberCurrentPage")||n(i.target).hasClass("e-memberCurrentSearchPage")||n(i.target).hasClass("e-memberCurrentDrillPage"))){t.Pivot.editorTreeNavigatee(i,this);return}if(n(i.target).hasClass("searchEditorTreeView")&&n(i.target).parents(".e-pivotschemadesigner").length==0){t.Pivot._searchEditorTreeNodes(i,this);return}i.preventDefault();t.isNullOrUndefined(this._curFocus.editor)?t.isNullOrUndefined(this._curFocus.dialog)?t.isNullOrUndefined(this._curFocus.icon)?t.isNullOrUndefined(this._curFocus.tab)||this._curFocus.tab.attr("tabindex","-1").focus().mouseover():this._curFocus.icon.attr("tabindex","-1").focus().mouseover():this._curFocus.dialog.hasClass("e-text")?this.element.find(".e-dialog .e-hoverCell").parent().find(".e-chkbox-small").click():(this._curFocus.dialog.click(),(this._curFocus.dialog.hasClass("e-dialogOKBtn")||this._curFocus.dialog.hasClass("e-dialogCancelBtn")||this._curFocus.dialog.hasClass("e-close"))&&(this._curFocus.dialog=null),this._index.editor=0):this._curFocus.editor.hasClass("e-text")&&this.element.find(".e-dialog .e-hoverCell").parent().find(".e-chkbox-small").click()}else i.keyCode!=13||t.isNullOrUndefined(this._curFocus.tab)||this._curFocus.tab.attr("role")=="columnheader"||(i.preventDefault(),(!t.isNullOrUndefined(this._curFocus.icon)||!t.isNullOrUndefined(this._curFocus.tab)&&!n(".pivotTree:visible,.pivotTreeContext:visible").length>0&&this.element.find(".e-hoverCell:visible").length>0)&&this.element.find(".e-hoverCell:visible")[0].click(),t.isNullOrUndefined(this.element.find(".e-chartTypesDialog"))||t.isNullOrUndefined(this._curFocus.chartimg)||(this._curFocus.chartimg.click(),t.isNullOrUndefined(this._curFocus.icon)||this._curFocus.icon.attr("tabindex","-1").focus().mouseover(),this._index.chartimg=0,this._curFocus.chartimg=null));(i.which===39||i.which===37)&&this.element.find(".e-dialog:visible").length>0&&this.element.find(".e-dialog .e-text:visible").hasClass("e-hoverCell")?this.element.find(".e-dialog .e-hoverCell").parent().find(".e-plus,.e-minus").click():(i.which===39||i.which===37)&&this.element.find(".e-cubeTreeView .e-text:visible").hasClass("e-hoverCell")&&!this.element.find(".e-editorTreeView:visible").length>0?this.element.find(".e-cubeTreeView .e-hoverCell").parent().find(".e-plus,.e-minus").click():(i.which===37||i.which===39||i.which===38||i.which===40)&&(this.element.find(".e-chartTypesDialog").length>0||this.element.find(".e-reportDBDialog").length>0)?(i.preventDefault(),e=this.element.find(".e-chartTypesDialog").length>0?this.element.find(".e-chartTypesDialog"):this.element.find(".e-reportDBDialog"),e.tabindex=-1,e.focus(),r=e.find(".e-chartTypesIcon").length>0?e.find(".e-chartTypesIcon"):this.element.find(".e-reportDBIcon"),t.isNullOrUndefined(this._curFocus.chartimg)?(this._index.chartimg=i.which==39?0:i.which==37?r.length-1:0,this._curFocus.chartimg=r.eq(this._index.chartimg).addClass("e-hoverCell").mouseover()):(this._curFocus.chartimg.removeClass("e-hoverCell").mouseleave(),i.which===39?this._index.chartimg=this._index.chartimg+1>r.length-1?0:this._index.chartimg+1:i.which===37?this._index.chartimg=this._index.chartimg-1<0?r.length-1:this._index.chartimg-1:i.which===40&&e.find(".e-chartTypesIcon").length>0?this._index.chartimg=this._index.chartimg+5>r.length-1?(this._index.chartimg+5)%10:this._index.chartimg+5:i.which===38&&e.find(".e-chartTypesIcon").length>0&&(this._index.chartimg=this._index.chartimg-5<0?r.length-1:this._index.chartimg-5),this._curFocus.chartimg=r.eq(this._index.chartimg).addClass("e-hoverCell").mouseover())):(i.which===37||i.which===39)&&!t.isNullOrUndefined(this._curFocus.tab)&&this._curFocus.tab.hasClass("e-icon")&&!this._curFocus.tab.hasClass("e-toggleCollapseButton")&&!this._curFocus.tab.hasClass("e-toggleExpandButton")&&!this.element.find(".e-dialog:visible").length>0&&(i.preventDefault(),this._curFocus.tab.removeClass("e-hoverCell").mouseleave(),r=this.element.find(".e-reportToolbar .e-icon:visible:not(.e-reportCol)"),t.isNullOrUndefined(this._curFocus.icon)?(this._index.icon=i.which==39?1:i.which==37?r.length-2:0,this._curFocus.icon=r.eq(this._index.icon).addClass("e-hoverCell").mouseover()):(this._curFocus.icon.removeClass("e-hoverCell").mouseleave(),i.which===39?this._index.icon=this._index.icon+1>r.length-2?0:this._index.icon+1:i.which===37&&(this._index.icon=this._index.icon-1<0?r.length-2:this._index.icon-1),this._curFocus.icon=r.eq(this._index.icon).addClass("e-hoverCell").mouseover()));(i.which===40||i.which===38)&&this.element.find(".e-dialog:visible").length>0&&!n(".pivotTree:visible,.pivotTreeContext:visible").length>0&&(this.element.find(".e-dialog .e-text").hasClass("e-hoverCell")||this.element.find(".e-dialog .e-measureEditor").hasClass("e-hoverCell"))?(i.preventDefault(),this.element.find(".e-dialog .e-hoverCell").removeClass("e-hoverCell"),this.element.find(".e-dialog .e-node-focus").removeClass("e-node-focus"),t.isNullOrUndefined(this._curFocus.dialog)||this._curFocus.dialog.mouseleave(),r=this.element.find(".e-dialog .e-measureEditor:visible,.e-dialog .e-text:visible"),t.isNullOrUndefined(this._curFocus.editor)?(this._index.editor=r.length>1?i.which==40?1:i.which==38?r.length-1:0:0,this._curFocus.editor=r.eq(this._index.editor).attr("tabindex","0").focus().addClass("e-hoverCell")):(this._curFocus.editor.attr("tabindex","0").removeClass("e-hoverCell"),i.which===40?this._index.editor=this._index.editor+1>r.length-1?0:this._index.editor+1:i.which===38&&(this._index.editor=this._index.editor-1<0?r.length-1:this._index.editor-1),this._curFocus.editor=r.eq(this._index.editor).attr("tabindex","0").focus().addClass("e-hoverCell"))):(i.which===40||i.which===38)&&!this.element.find(".e-dialog:visible").length>0&&!t.isNullOrUndefined(this._curFocus.tab)&&this._curFocus.tab.hasClass("e-text")&&!n(".pivotTree:visible,.pivotTreeContext:visible").length>0&&t.isNullOrUndefined(this._schemaData)?(this.element.find(".e-hoverCell").removeClass("e-hoverCell"),this._curFocus.tab.mouseleave(),i.preventDefault(),r=this.element.find(".e-cubeTreeView .e-text:visible"),t.isNullOrUndefined(this._curFocus.tree)?(this._index.tree=i.which==40?1:i.which==38?r.length-1:0,this._curFocus.tree=r.eq(this._index.tree).attr("tabindex","-1").focus().addClass("e-hoverCell").mouseover(),this.element.find(".e-node-focus").removeClass("e-node-focus")):(this._curFocus.tree.attr("tabindex","0").removeClass("e-hoverCell").mouseleave(),i.which===40?this._index.tree=this._index.tree+1>r.length-1?0:this._index.tree+1:i.which===38&&(this._index.tree=this._index.tree-1<0?r.length-1:this._index.tree-1),this._curFocus.tree=r.eq(this._index.tree).attr("tabindex","-1").focus().addClass("e-hoverCell").mouseover(),this.element.find(".e-node-focus").removeClass("e-node-focus"))):(i.which===40||i.which===38)&&!t.isNullOrUndefined(this._curFocus.tab)&&this._curFocus.tab.hasClass("e-button")&&!n(".pivotTree:visible,.pivotTreeContext:visible").length>0&&!this.element.find(".e-dialog:visible").length>0&&t.isNullOrUndefined(this._schemaData)&&(i.preventDefault(),r=null,(this._curFocus.tab.parent().parent().hasClass("e-categoricalAxis")||this._curFocus.tab.parent().parent().hasClass("e-rowAxis")||this._curFocus.tab.parent().parent().hasClass("e-slicerAxis"))&&(this._curFocus.tab.parent().parent().hasClass("e-categoricalAxis")?r=this.element.find(".e-categoricalAxis button"):this._curFocus.tab.parent().parent().hasClass("e-rowAxis")?r=this.element.find(".e-rowAxis button"):this._curFocus.tab.parent().parent().hasClass("e-slicerAxis")&&(r=this.element.find(".e-slicerAxis button")),t.isNullOrUndefined(this._curFocus.tab)||(this._curFocus.tab.removeClass("e-hoverCell").attr("tabindex","0").mouseleave(),this._curFocus.tab.parent().find(".e-removeSplitBtn:visible").focus().removeClass("e-hoverCell")),t.isNullOrUndefined(this._curFocus.button)?(this._index.button=i.which==40?r.length>1?1:0:i.which==38?r.length-1:0,this._curFocus.button=r.eq(this._index.button).attr("tabindex","0").focus().addClass("e-hoverCell"),this._curFocus.button.parent().find(".e-removeSplitBtn:visible").addClass("e-hoverCell")):(this._curFocus.button.attr("tabindex","0").removeClass("e-hoverCell"),this._curFocus.button.parent().find(".e-removeSplitBtn:visible").removeClass("e-hoverCell"),i.which===40?this._index.button=this._index.button+1>r.length-1?0:this._index.button+1:i.which===38&&(this._index.button=this._index.button-1<0?r.length-1:this._index.button-1),this._curFocus.button=r.eq(this._index.button).attr("tabindex","0").focus().addClass("e-hoverCell"),this._curFocus.button.parent().find(".e-removeSplitBtn:visible").addClass("e-hoverCell"))))},_wireMeasureRemoveEvent:function(){this.element.find(".e-removeMeasure").click(function(t){var i=n(this).parents(".e-pivotclient").data("ejPivotClient");i._isMembersFiltered=!0;n(n(t.target).parent()).remove()})},_wireEditorRemoveEvent:function(){this.element.find(".e-removeSplitBtn").click(function(i){var r=n(i.target).parents(".e-pivotclient").data("ejPivotClient"),u,f;this._isTimeOut=!0;setTimeout(function(){r._isTimeOut&&r._waitingPopup.show()},800);n(n(i.target).parent()).remove();r._currentReportItems.length!=0&&r._treeViewData.hasOwnProperty(n(i.target).parent().find("button").attr("title"))&&(delete r._treeViewData[n(i.target).parent().find("button").attr("title")],r._currentReportItems.splice(n.inArray(n(i.target).parent().find("button").attr("title"),r._currentReportItems),1));r._off(this.element,"click",".e-removeSplitBtn");r.model.enableAdvancedFilter&&r.model.analysisMode==t.Pivot.AnalysisMode.Olap&&r.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&(u=n(n(i.target)).parent("div:eq(0)").attr("data-tag").split(":")[1].split("."),t.isNullOrUndefined(u)||u.length!=2||(r._setUniqueNameFrmBtnTag(u),r._removeFilterTag(r._selectedFieldName)));r.model.beforeServiceInvoke!=null&&r._trigger("beforeServiceInvoke",{action:"removeSplitButton",element:this.element,customObject:r.model.customObject});f=JSON.stringify(r.model.customObject);delete r._fieldSelectedMembers[n(i.target).parent().find("button").attr("title")];r.doAjaxPost("POST",r.model.url+"/"+r.model.serviceMethodSettings.removeSplitButton,JSON.stringify({action:"removeSplitButton",clientParams:n(i.target).parent().attr("data-tag"),olapReport:r.currentReport,clientReports:r.reports,customObject:f}),r._removeSplitButtonSuccess);r._isNodeOrButtonDropped=!0})},_setUniqueNameFrmBtnTag:function(t){var i,r;if(t.length>0)if(i=this.element.find(".e-cubeTreeView li[data-tag^='["+t[0]+"'][data-tag$='"+t[1]+"]']"),i.length>0){for(r=0;r<i.length;r++)if(n(i[r]).attr("data-tag").split("].").length==2){this._selectedFieldName=n(i[r]).attr("data-tag");break}}else this._selectedFieldName=i.attr("data-tag")},_getExportModel:function(){var i=this,f,e="clientMode",s=t.isNullOrUndefined(i._pivotGrid)?0:i._pivotGrid._excelLikeJSONRecords!=null?i._pivotGrid._excelRowCount:i._pivotGrid._rowCount,h=t.isNullOrUndefined(i._pivotGrid)?0:i._pivotGrid._excelLikeJSONRecords!=null?Math.floor(i._pivotGrid._excelLikeJSONRecords.length/i._pivotGrid._excelRowCount):i._pivotGrid.getJSONRecords()!=null?Math.floor(i._pivotGrid.getJSONRecords().length/i._pivotGrid._rowCount):0,w=i.element.find(".reportlist").data("ejDropDownList").getSelectedValue(),r={},c={},r={url:"",fileName:"PivotClient",exportMode:t.PivotClient.ExportMode.JSON,title:"",description:"",exportChartAsImage:!0,fileFormat:"xls",exportWithStyle:!0,exportValueAsNumber:!1},l,o,a,v,y,p,u;if(i.model.displaySettings.mode!=t.PivotClient.DisplayMode.GridOnly&&i.model.clientExportMode!=t.PivotClient.ClientExportMode.GridOnly&&(i.chartObj=null,l=[],o=!1&&i.model.analysisMode==t.Pivot.AnalysisMode.Olap,i.chartObj=i.element.find("#"+i._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(i.chartObj)&&!t.isNullOrUndefined(i.otreemapObj)&&(i.chartObj=i.element.find("#"+i.otreemapObj._id+"TreeMapContainer").data("ejTreeMap")),i.chartObj.sfType.split(".").pop().toLowerCase()!="treemap"&&(a=i.chartObj.model.primaryXAxis.zoomFactor,i.chartObj.model.primaryXAxis.zoomFactor=1,i.chartObj.model.enableCanvasRendering=!0,v=n.extend(!0,[],i.chartObj.model.series),o&&(i.chartObj.model.series=l),i.chartObj.redraw(),y=i.chartObj["export"](),chartString=y.toDataURL("image/png"),i.chartObj.model.primaryXAxis.zoomFactor=a,i.chartObj.model.enableCanvasRendering=!1,o&&(i.chartObj.model.series=v),i.chartObj.redraw())),i._isExporting=!1,i.model.displaySettings.mode!=t.PivotClient.DisplayMode.ChartOnly&&i.model.clientExportMode!=t.PivotClient.ClientExportMode.ChartOnly&&(p={valueCellColor:i._pivotGrid.element.find(".value").css("color"),valueCellBGColor:i._pivotGrid.element.find(".value").css("background-color"),summaryCellColor:i._pivotGrid.element.find(".summary").css("color"),summaryCellBGColor:i._pivotGrid.element.find(".summary").css("background-color")}),i.model.displaySettings.mode==t.PivotClient.DisplayMode.GridOnly||i.model.clientExportMode==t.PivotClient.ClientExportMode.GridOnly)mode=t.PivotClient.ClientExportMode.GridOnly,(i.model.operationalMode==t.Pivot.OperationalMode.ClientMode||i._pivotGrid!=null&&i._pivotGrid._excelLikeJSONRecords!=null&&r.exportMode==t.PivotClient.ExportMode.JSON||r.exportMode==t.PivotClient.ExportMode.JSON)&&(f={args:JSON.stringify({exportMode:mode,exportOption:e,fileFormat:r.fileFormat+(r.exportMode==t.PivotClient.ExportMode.JSON&&(r.fileFormat==".xls"||r.fileFormat==".xlsx")?"~"+r.exportValueAsNumber:""),pGridData:i._pivotGrid.exportRecords!=null&&i._pivotGrid.exportRecords!=""?i._pivotGrid.exportRecords:null,rowCount:s,columnCount:h,fileName:r.fileName,customObject:JSON.stringify(i._pivotGrid.model.customObject),title:r.title,description:r.description,completeDataExport:i.model.enableCompleteDataExport,exportWithStyle:r.exportWithStyle,multiControlExport:!0})});else if(i.model.displaySettings.mode==t.PivotClient.DisplayMode.ChartOnly||i.model.clientExportMode==t.PivotClient.ClientExportMode.ChartOnly)mode=t.PivotClient.ClientExportMode.ChartOnly,(i.model.operationalMode==t.Pivot.OperationalMode.ClientMode||i._pivotGrid!=null&&i._pivotGrid._excelLikeJSONRecords!=null&&r.exportMode==t.PivotClient.ExportMode.JSON||r.exportMode==t.PivotClient.ExportMode.JSON)&&(u={exportMode:mode,exportOption:e,fileFormat:r.fileFormat,fileName:r.fileName,chartdata:chartString.split(",")[1],bgColor:n(i._pivotChart.element).css("background-color"),title:r.title,description:r.description},t.isNullOrUndefined(r.exportChartAsImage)||r.exportChartAsImage||(u.chartModel=c),f={args:JSON.stringify(u)});else return mode=t.PivotClient.ClientExportMode.ChartAndGrid,(i.model.operationalMode==t.Pivot.OperationalMode.ClientMode||i._pivotGrid._excelLikeJSONRecords!=null&&r.exportMode==t.PivotClient.ExportMode.JSON||r.exportMode==t.PivotClient.ExportMode.JSON)&&(u={exportMode:mode,exportOption:e,fileFormat:r.fileFormat+(r.exportMode==t.PivotClient.ExportMode.JSON&&(r.fileFormat==".xls"||r.fileFormat==".xlsx")?"~"+r.exportValueAsNumber:""),pGridData:i._pivotGrid.exportRecords!=null&&i._pivotGrid.exportRecords!=""?i._pivotGrid.exportRecords:null,rowCount:s,columnCount:h,fileName:r.fileName,customObject:JSON.stringify(i._pivotGrid.model.customObject),chartdata:chartString.split(",")[1],bgColor:n(i._pivotChart.element).css("background-color"),title:r.title,description:r.description,completeDataExport:i.model.enableCompleteDataExport,exportWithStyle:r.exportWithStyle,multiControlExport:!0},t.isNullOrUndefined(r.exportChartAsImage)||r.exportChartAsImage||(u.chartModel=c),f={args:JSON.stringify(u)}),f},_wireDialogEvent:function(){this.element.find(".e-newReportImg, .e-addReportImg, .e-removeReportImg, .e-renameReportImg, .e-pvtBtn, .e-mdxImg,.e-colSortFilterImg, .e-rowSortFilterImg, .e-autoExecuteImg").on(t.eventType.click,function(i){var r=n(this).parents(".e-pivotclient").data("ejPivotClient");if(i.currentTarget.parentElement.className.indexOf("e-splitBtn")>-1){if(n(n(i.currentTarget).parents(".e-splitBtn")[0]).attr("data-tag").indexOf("NAMEDSET")>-1)return!1;r._currentAxis=n(n(i.currentTarget).parents(".e-splitBtn")[0]).attr("data-tag").split(":")[0]}i.preventDefault();i.target.innerHTML!=r._getLocalizedLabels("Cancel")&&i.target.innerHTML!=r._getLocalizedLabels("OK")&&i.target.parentElement.innerHTML!=r._getLocalizedLabels("Cancel")&&i.target.parentElement.innerHTML!=r._getLocalizedLabels("OK")&&(r._off(r.element,"click",".e-newReportImg, .e-addReportImg, .e-removeReportImg, .e-renameReportImg, .e-mdxImg, .e-txt,.e-colSortFilterImg, .e-rowSortFilterImg, .e-chartTypesImg, .e-autoExecuteImg"),i.target.className.indexOf("e-colSortFilterImg")>=0||i.target.className.indexOf("e-rowSortFilterImg")>=0?(r._sortOrFilterTab=i.target.className.indexOf("SortImg")>=0?"sort":"filter",i.target.className.indexOf("e-colSortFilterImg")>=0||i.target.className.indexOf("e-rowSortFilterImg")>=0||r.element.find(".e-rowAxis").html()!=""||r.element.find(".e-categoricalAxis").html()!=""||(r._isTimeOut=!0,setTimeout(function(){r._isTimeOut&&r._waitingPopup.show()},800)),r._isSorted=!1,r._isFiltered=!1,r.doAjaxPost("POST",r.model.url+"/"+r.model.serviceMethodSettings.toolbarServices,JSON.stringify({action:"FetchSortState",toolbarOperation:null,clientInfo:r._axis=i.target.className.indexOf("e-colSortFilterImg")>=0?"Column":"Row",olapReport:r.currentReport,clientReports:"",customObject:JSON.stringify(r.model.customObject)}),r._fetchSortState)):i.target.className.indexOf("mdx")>=0?r.model.analysisMode==t.Pivot.AnalysisMode.Olap&&r.model.operationalMode==t.Pivot.OperationalMode.ClientMode?r._mdxQuery(t.olap.base._getParsedMDX(r.model.dataSource,r.model.dataSource.cube)):r.doAjaxPost("POST",r.model.url+"/"+r.model.serviceMethodSettings.mdxQuery,JSON.stringify({olapReport:r.currentReport,customObject:JSON.stringify(r.model.customObject)}),r._mdxQuery):i.target.className.indexOf("autoExecute")>=0?(r.model.enableDeferUpdate=!1,r._renderControls(),r._deferReport=r.currentReport,r.model.enableDeferUpdate=!0):(r.model.operationalMode!=t.Pivot.OperationalMode.ServerMode||r.model.analysisMode!=t.Pivot.AnalysisMode.Olap)&&n(i.currentTarget).hasClass("e-pvtBtn")||r._createDialogRequest(i),r._dimensionName=n(i.currentTarget).parent().attr("data-tag"));r.isDropped=!1});this.element.find(".e-excelExportImg, .e-wordExportImg, .e-pdfExportImg").click(function(i){var r=n(this).parents(".e-pivotclient").data("ejPivotClient"),w,b,h,l,o,v,y,g,nt,tt,k,f,it,rt;if(r.chartObj=r.element.find("#"+r._pivotChart._id+"Container").data("ejChart"),r.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&!t.isNullOrUndefined(r.chartObj)&&r._currentTab==="grid"&&r.model.isResponsive&&(r.element.find("#"+r._pivotChart._id+"Container").css("width",r.element.find("#"+r._pivotGrid._id).width()),r.chartObj.redraw()),r.model.displaySettings.mode==r.model.clientExportMode||r.model.displaySettings.mode==t.PivotClient.DisplayMode.ChartAndGrid||r.model.clientExportMode==t.PivotClient.ClientExportMode.ChartAndGrid){t.browserInfo().name=="msie"&&t.browserInfo().version<=8&&(r.model.clientExportMode=t.PivotClient.ClientExportMode.GridOnly);var e=i.target.className.indexOf("e-excel")>=0?"Excel":i.target.className.indexOf("e-word")>=0?"Word":"Pdf",d=e=="Excel"?r.model.operationalMode==t.Pivot.OperationalMode.ServerMode?".xls":".xlsx":e=="Word"?".docx":".pdf",a=t.isNullOrUndefined(r._pivotGrid)?0:r._pivotGrid._excelLikeJSONRecords!=null?r._pivotGrid._excelRowCount:t.isNullOrUndefined(r._pivotGrid._rowLength)?r._pivotGrid._rowCount:r._pivotGrid._rowLength,p=t.isNullOrUndefined(r._pivotGrid)?0:(r._pivotGrid._excelLikeJSONRecords!=null?Math.floor(r._pivotGrid._excelLikeJSONRecords.length/r._pivotGrid._excelRowCount):r._pivotGrid.exportRecords!=null&&!t.isNullOrUndefined(r._pivotGrid._rowLength)?Math.floor(r._pivotGrid.exportRecords.length/r._pivotGrid._rowLength):!t.isNullOrUndefined(r._pivotGrid.getJSONRecords()))?Math.floor(r._pivotGrid.getJSONRecords().length/r._pivotGrid._rowCount):0,c=r.element.find(".reportlist").data("ejDropDownList").getSelectedValue(),u={},s={};u=e=="Excel"?{url:"",fileName:"PivotClient",exportMode:t.PivotClient.ExportMode.JSON,title:c!=null&&c!=""?c:"",description:"",exportChartAsImage:!0,fileFormat:d,exportWithStyle:!0,exportValueAsNumber:!1,titleAlignment:"center"}:{url:"",fileName:"PivotClient",exportMode:t.PivotClient.ExportMode.JSON,title:c!=null&&c!=""?c:"",description:"",fileFormat:d,exportWithStyle:!0,titleAlignment:"center"};r._trigger("beforeExport",u);w=r.model.enableCompleteDataExport&&(r.model.enablePaging||r.model.enableVirtualScrolling);r._isExporting=!0;w&&r.model.analysisMode==t.Pivot.AnalysisMode.Olap&&r.model.displaySettings.mode!=t.PivotClient.DisplayMode.ChartOnly&&r.model.clientExportMode!=t.PivotClient.ClientExportMode.ChartOnly&&u.exportMode==t.PivotClient.ExportMode.JSON&&(r.model.operationalMode==t.Pivot.OperationalMode.ClientMode?(b={paging:r.model.enablePaging,virualScrolling:r.model.enableVirtualScrolling},r.model.enablePaging=r.model.enableVirtualScrolling=!1,t.olap.base.getJSONData({action:"loadFieldElements"},r.model.dataSource,r),r.model.enablePaging=b.paging,r.model.enableVirtualScrolling=b.virualScrolling):r.doAjaxPost("POST",r.model.url+"/"+r._pivotGrid.model.serviceMethodSettings.initialize,JSON.stringify({action:"export",currentReport:r._pivotGrid.model.currentReport}),function(n){r._fullExportedData.jsonObj=t.isNullOrUndefined(n[0])?t.isNullOrUndefined(n.d)?JSON.parse(n.JsonRecords):JSON.parse(n.d[0]):JSON.parse(n[0]);r._fullExportedData.rowCount=t.isNullOrUndefined(n[1])?t.isNullOrUndefined(n.d)?JSON.parse(n.RowCount):JSON.parse(n.d[1]):JSON.parse(n[1])}),r._pivotGrid.exportRecords=r._fullExportedData.jsonObj,a=r._fullExportedData.rowCount,p=Math.floor(r._fullExportedData.jsonObj.length/a));r.model.displaySettings.mode!=t.PivotClient.DisplayMode.GridOnly&&r.model.clientExportMode!=t.PivotClient.ClientExportMode.GridOnly&&(r.chartObj=null,v=[],y=w&&r.model.analysisMode==t.Pivot.AnalysisMode.Olap,r.chartObj=r.element.find("#"+r._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(r.chartObj)&&!t.isNullOrUndefined(r.otreemapObj)&&(r.chartObj=r.element.find("#"+r.otreemapObj._id+"TreeMapContainer").data("ejTreeMap")),r.chartObj.sfType.split(".").pop().toLowerCase()!="treemap"&&(y&&(r.model.operationalMode==t.Pivot.OperationalMode.ServerMode?r.doAjaxPost("POST",r.model.url+"/"+r.chartObj.model.serviceMethodSettings.initialize,JSON.stringify({action:"export",currentReport:r._pivotChart.model.currentReport}),function(n){v=r._pivotChart._getChartSeries(t.isNullOrUndefined(n[0])?t.isNullOrUndefined(n.d)?JSON.parse(n.JsonRecords):JSON.parse(n.d):JSON.parse(n[0]))}):v=r._pivotChart._generateData(r._fullExportedData.tranposeEngine)),g=r.chartObj.model.primaryXAxis.zoomFactor,r.chartObj.model.primaryXAxis.zoomFactor=1,r.chartObj.model.enableCanvasRendering=!0,nt=n.extend(!0,[],r.chartObj.model.series),y&&(r.chartObj.model.series=v),r.chartObj.redraw(),tt=r.chartObj["export"](),l=tt.toDataURL("image/png"),r.chartObj.model.primaryXAxis.zoomFactor=g,r.chartObj.model.enableCanvasRendering=!1,y&&(r.chartObj.model.series=nt),r.chartObj.redraw()));e!="Excel"||u.exportChartAsImage||r.model.displaySettings.mode==t.PivotClient.DisplayMode.GridOnly||r.model.clientExportMode==t.PivotClient.ClientExportMode.GridOnly||(s=r._pivotChart._excelExport(r.chartObj),s=s.replace(new RegExp("<br/>","g"),""));r._isExporting=!1;r.model.displaySettings.mode!=t.PivotClient.DisplayMode.ChartOnly&&r.model.clientExportMode!=t.PivotClient.ClientExportMode.ChartOnly&&(k={valueCellColor:r._pivotGrid.element.find(".value").css("color"),valueCellBGColor:r._pivotGrid.element.find(".value").css("background-color"),summaryCellColor:r._pivotGrid.element.find(".summary").css("color"),summaryCellBGColor:r._pivotGrid.element.find(".summary").css("background-color")});r.model.displaySettings.mode==t.PivotClient.DisplayMode.GridOnly||r.model.clientExportMode==t.PivotClient.ClientExportMode.GridOnly?(o=t.PivotClient.ClientExportMode.GridOnly,h=r.model.operationalMode==t.Pivot.OperationalMode.ClientMode||r._pivotGrid!=null&&r._pivotGrid._excelLikeJSONRecords!=null&&u.exportMode==t.PivotClient.ExportMode.JSON||n.trim(u.url)!=""&&u.exportMode==t.PivotClient.ExportMode.JSON?{args:JSON.stringify({exportMode:o,exportOption:e,fileFormat:u.fileFormat+(u.exportMode==t.PivotClient.ExportMode.JSON&&(u.fileFormat==".xls"||u.fileFormat==".xlsx")?"~"+u.exportValueAsNumber:""),pGridData:r._pivotGrid.exportRecords!=null&&r._pivotGrid.exportRecords!=""?r._pivotGrid.exportRecords:null,rowCount:a,columnCount:p,fileName:u.fileName,customObject:JSON.stringify(r._pivotGrid.model.customObject),title:u.title,description:u.description,completeDataExport:r.model.enableCompleteDataExport,exportWithStyle:u.exportWithStyle,formatting:!t.isNullOrUndefined(r._pivotGrid)&&r._pivotGrid.model.enableConditionalFormatting&&u.exportMode==t.PivotClient.ExportMode.JSON?r._pivotGrid._cFormat:u.exportMode==t.PivotClient.ExportMode.JSON?[]:"",titleAlignment:u.titleAlignment})}:{args:JSON.stringify({exportMode:o,exportOption:e,fileFormat:u.fileFormat,currentReport:r.model.enableDeferUpdate?r._deferReport:r.currentReport,layout:r.gridLayout(),colorSettings:JSON.stringify(k),title:u.title,description:u.description,completeDataExport:r.model.enableCompleteDataExport,exportWithStyle:u.exportWithStyle,formatting:!t.isNullOrUndefined(r._pivotGrid)&&r._pivotGrid.model.enableConditionalFormatting&&u.exportMode==t.PivotClient.ExportMode.JSON?r._pivotGrid._cFormat:u.exportMode==t.PivotClient.ExportMode.JSON?[]:"",titleAlignment:u.titleAlignment})}):r.model.displaySettings.mode==t.PivotClient.DisplayMode.ChartOnly||r.model.clientExportMode==t.PivotClient.ClientExportMode.ChartOnly?(o=t.PivotClient.ClientExportMode.ChartOnly,r.model.operationalMode==t.Pivot.OperationalMode.ClientMode||r._pivotGrid!=null&&r._pivotGrid._excelLikeJSONRecords!=null&&u.exportMode==t.PivotClient.ExportMode.JSON||n.trim(u.url)!=""&&u.exportMode==t.PivotClient.ExportMode.JSON?(f={exportMode:o,exportOption:e,fileFormat:u.fileFormat,fileName:u.fileName,chartdata:l.split(",")[1],bgColor:n(r._pivotChart.element).css("background-color"),title:u.title,description:u.description,titleAlignment:u.titleAlignment},t.isNullOrUndefined(u.exportChartAsImage)||u.exportChartAsImage||(f.chartModel=s),h={args:JSON.stringify(f)}):(f={exportMode:o,exportOption:e,fileFormat:u.fileFormat,currentReport:r.model.enableDeferUpdate?r._deferReport:r.currentReport,chartdata:l.split(",")[1],legenddata:"",bgColor:n(r._pivotChart.element).css("background-color"),title:u.title,description:u.description,titleAlignment:u.titleAlignment},t.isNullOrUndefined(u.exportChartAsImage)||u.exportChartAsImage||(f.chartModel=s),h={args:JSON.stringify(f)})):(o=t.PivotClient.ClientExportMode.ChartAndGrid,r.model.operationalMode==t.Pivot.OperationalMode.ClientMode||r._pivotGrid._excelLikeJSONRecords!=null&&u.exportMode==t.PivotClient.ExportMode.JSON||n.trim(u.url)!=""&&u.exportMode==t.PivotClient.ExportMode.JSON?(f={exportMode:o,exportOption:e,fileFormat:u.fileFormat+(u.exportMode==t.PivotClient.ExportMode.JSON&&(u.fileFormat==".xls"||u.fileFormat==".xlsx")?"~"+u.exportValueAsNumber:""),pGridData:r._pivotGrid.exportRecords!=null&&r._pivotGrid.exportRecords!=""?r._pivotGrid.exportRecords:null,rowCount:a,columnCount:p,fileName:u.fileName,customObject:JSON.stringify(r._pivotGrid.model.customObject),chartdata:l.split(",")[1],bgColor:n(r._pivotChart.element).css("background-color"),title:u.title,description:u.description,completeDataExport:r.model.enableCompleteDataExport,exportWithStyle:u.exportWithStyle,formatting:!t.isNullOrUndefined(r._pivotGrid)&&r._pivotGrid.model.enableConditionalFormatting&&u.exportMode==t.PivotClient.ExportMode.JSON?r._pivotGrid._cFormat:u.exportMode==t.PivotClient.ExportMode.JSON?[]:"",titleAlignment:u.titleAlignment},t.isNullOrUndefined(u.exportChartAsImage)||u.exportChartAsImage||(f.chartModel=s),h={args:JSON.stringify(f)}):(f={exportMode:o,exportOption:e,fileFormat:u.fileFormat,currentReport:r.model.enableDeferUpdate?r._deferReport:r.currentReport,layout:r.gridLayout(),colorSettings:JSON.stringify(k),chartdata:l.split(",")[1],legenddata:"",bgColor:n(r._pivotChart.element).css("background-color"),title:u.title,description:u.description,completeDataExport:r.model.enableCompleteDataExport,exportWithStyle:u.exportWithStyle,formatting:!t.isNullOrUndefined(r._pivotGrid)&&r._pivotGrid.model.enableConditionalFormatting&&u.exportMode==t.PivotClient.ExportMode.JSON?r._pivotGrid._cFormat:u.exportMode==t.PivotClient.ExportMode.JSON?[]:"",titleAlignment:u.titleAlignment},t.isNullOrUndefined(u.exportChartAsImage)||u.exportChartAsImage||(f.chartModel=s),h={args:JSON.stringify(f)}));t.raiseWebFormsServerEvents&&n.trim(u.url)!=""&&u.url=="pivotClientExport"&&u.exportMode==t.PivotClient.ExportMode.JSON?(it={model:r.model,originalEventType:u.url},rt=h,t.raiseWebFormsServerEvents(u.url,it,rt),setTimeout(function(){t.isOnWebForms=!0},1e3)):r.doPostBack(n.trim(u.url)!=""?u.url:r.model.url+"/"+r.model.serviceMethodSettings.exportPivotClient,h)}});this.element.find(".e-toggleaxisImg").click(function(){var i=n(this).parents(".e-pivotclient").data("ejPivotClient"),r;i._isTimeOut=!0;i.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(i.chartObj=null,i.chartObj=i.element.find("#"+i._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(i.chartObj)&&!t.isNullOrUndefined(i.otreemapObj)&&(i.chartObj=i.element.find("#"+i.otreemapObj._id+"TreeMapContainer").data("ejTreeMap")));i.model.operationalMode==t.Pivot.OperationalMode.ClientMode?(setTimeout(function(){i._isTimeOut&&i._waitingPopup.show()},800),r=i.model.dataSource.rows,i.model.dataSource.rows=i.model.dataSource.columns,i.model.dataSource.columns=r,i.model.analysisMode==t.Pivot.AnalysisMode.Olap&&i.model.dataSource.values.length>0&&(i.model.dataSource.values[0].axis=i.model.dataSource.values[0].axis=="rows"?"columns":i.model.dataSource.values[0].axis=="columns"?"rows":"columns"),i.model.analysisMode==t.Pivot.AnalysisMode.Olap&&t.olap.base.clearDrilledItems(i.model.dataSource,{action:"nodeDropped"},i),i.refreshControl()):i.model.analysisMode==t.Pivot.AnalysisMode.Pivot?(i._waitingPopup&&i._waitingPopup.show(),i.doAjaxPost("POST",i.model.url+"/"+i.model.serviceMethodSettings.toolbarServices,JSON.stringify({action:"toggleAxis",args:JSON.stringify({currentReport:JSON.parse(i.getOlapReport()).Report,sortedHeaders:t.isNullOrUndefined(i._ascdes)?"":i._ascdes}),customObject:JSON.stringify(i.model.customObject)}),i._toggleAxisSuccess)):(i.element.find(".e-rowAxis").html()!=""||i.element.find(".e-categoricalAxis").html()!="")&&(i._waitingPopup&&i._waitingPopup.show(),i.doAjaxPost("POST",i.model.url+"/"+i.model.serviceMethodSettings.toggleAxis,JSON.stringify({action:"toggleAxis",currentReport:i.currentReport,clientReports:i.reports,customObject:JSON.stringify(i.model.customObject)}),i._toggleAxisSuccess));t.isNullOrUndefined(i.chartObj)||i.chartObj.redraw()});this.element.find(".e-reportDBImg").click(function(){var i=n(this).parents(".e-pivotclient").data("ejPivotClient"),r=t.buildTag("div.e-reportDBDialog#"+this._id+"_reportDBDialog",t.buildTag("table",t.buildTag("tbody",t.buildTag("tr",t.buildTag("td",t.buildTag("div.e-saveReportImg e-reportDBIcon").attr({title:i._getLocalizedLabels("Save"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-saveAsReportImg e-reportDBIcon").attr({title:i._getLocalizedLabels("SaveAs"),tabindex:0})[0].outerHTML)[0].outerHTML+(i.model.showReportCollection?"":t.buildTag("td",t.buildTag("div.e-loadReportImg e-reportDBIcon").attr({title:i._getLocalizedLabels("Load"),tabindex:0})[0].outerHTML)[0].outerHTML)+t.buildTag("td",t.buildTag("div.e-removeDBReportImg e-reportDBIcon").attr({title:i._getLocalizedLabels("Remove"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-renameDBReportImg e-reportDBIcon").attr({title:i._getLocalizedLabels("Rename"),tabindex:0})[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML),u=i.element.find("div.e-reportDBDialog");u.length==0&&(n(r).appendTo(i.element),n(r).css("left",this.offsetLeft+20+"px").css("top",this.offsetTop+20+"px"));i.element.find(".e-reportDBIcon").click(function(u){var o,s,e,h,f;if(n(r).remove(),i=i,i._off(i.element,"click",".e-reportDBIcon"),u.target.className.indexOf("e-loadReportImg")>=0||u.target.className.indexOf("e-removeDBReportImg")>=0||u.target.className.indexOf("e-renameDBReportImg")>=0)o=u.target.className.indexOf("e-loadReportImg")>=0?"LoadReport":u.target.className.indexOf("e-removeDBReportImg")>=0?"RemoveDBReport":"RenameDBReport",i._isTimeOut=!0,setTimeout(function(){i._isTimeOut&&i._waitingPopup.show()},800),i.model.operationalMode=="servermode"?(i.model.beforeServiceInvoke!=null&&i._trigger("beforeServiceInvoke",{action:"FetchingReportList",element:i.element,customObject:i.model.customObject}),f=JSON.stringify(i.model.customObject),i.doAjaxPost("POST",i.model.url+"/"+i.model.serviceMethodSettings.fetchReportList,JSON.stringify({customObject:f,action:o,operationalMode:i.model.operationalMode,analysisMode:i.model.analysisMode}),i._fetchReportListSuccess)):(s={url:"",reportCollection:i._clientReportCollection,reportList:"",mode:i.model.analysisMode},i._trigger("fetchReport",{targetControl:i,fetchReportSetting:s}),i.model.enableLocalStorage?i._fetchReportListSuccess({d:[{Name:"ReportNameList",Value:s.reportList},{Name:"Action",Value:o}]}):(f=JSON.stringify(i.model.customObject),i.doAjaxPost("POST",s.url+"/"+i.model.serviceMethodSettings.fetchReportList,JSON.stringify({customObject:f,action:o,operationalMode:i.model.operationalMode,analysisMode:i.model.analysisMode}),i._fetchReportListSuccess)));else if(u.target.className.indexOf("e-saveReportImg")>=0){if(i._currentRecordName=="")return i._createDialogRequest(u),!1;i._isTimeOut=!0;setTimeout(function(){i._isTimeOut&&i._waitingPopup.show()},800);i.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(e=i._updateReportCollection(!0));i.model.operationalMode==t.Pivot.OperationalMode.ServerMode?(i.model.beforeServiceInvoke!=null&&i._trigger("beforeServiceInvoke",{action:"saveReport",element:this.element,customObject:i.model.customObject}),f=JSON.stringify(i.model.customObject),i.doAjaxPost("POST",i.model.url+"/"+i.model.serviceMethodSettings.saveReport,JSON.stringify({reportName:i._currentRecordName,operationalMode:i.model.operationalMode,analysisMode:i.model.analysisMode,olapReport:i.currentReport,clientReports:i.model.analysisMode=="pivot"?JSON.stringify(i._clientReportCollection):t.isNullOrUndefined(e)?i.reports:e,customObject:f}),i._toolbarOperationSuccess)):(h={url:"",reportName:i._currentRecordName,reportCollection:i._clientReportCollection,mode:i.model.analysisMode,filterCollection:i._currentReportItems},i._trigger("saveReport",{targetControl:i,saveReportSetting:h}),i.model.enableLocalStorage||(f=JSON.stringify(i.model.customObject),i.doAjaxPost("POST",h.url+"/"+i.model.serviceMethodSettings.saveReport,JSON.stringify({reportName:h.reportName,operationalMode:i.model.operationalMode,analysisMode:i.model.analysisMode,olapReport:JSON.stringify(i.model.dataSource),clientReports:(t.isNullOrUndefined(e)?JSON.stringify(i._clientReportCollection):e)+":>>:"+JSON.stringify(i._currentReportItems),customObject:f}),i._toolbarOperationSuccess)))}else(i.model.operationalMode!=t.Pivot.OperationalMode.ServerMode||i.model.analysisMode!=t.Pivot.AnalysisMode.Olap)&&n(u.currentTarget).hasClass("e-pvtBtn")||i._createDialogRequest(u)})});this.element.find(".e-chartTypesImg").click(function(i){var r=n(this).parents(".e-pivotclient").data("ejPivotClient"),u=t.buildTag("div.e-chartTypesDialog#"+r._id+"_chartTypesDialog",t.buildTag("table",t.buildTag("tbody",t.buildTag("tr",t.buildTag("td",t.buildTag("div.e-line e-chartTypesIcon").attr({title:r._getLocalizedLabels("Line"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-spline e-chartTypesIcon").attr({title:r._getLocalizedLabels("Spline"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-column e-chartTypesIcon").attr({title:r._getLocalizedLabels("Column"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-area e-chartTypesIcon").attr({title:r._getLocalizedLabels("Area"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-splinearea e-chartTypesIcon").attr({title:r._getLocalizedLabels("SplineArea"),tabindex:0})[0].outerHTML)[0].outerHTML)[0].outerHTML+t.buildTag("tr",t.buildTag("td",t.buildTag("div.e-stepline e-chartTypesIcon").attr({title:r._getLocalizedLabels("StepLine"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-steparea e-chartTypesIcon").attr({title:r._getLocalizedLabels("StepArea"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-pie e-chartTypesIcon").attr({title:r._getLocalizedLabels("Pie"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-bar e-chartTypesIcon").attr({title:r._getLocalizedLabels("Bar"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-stackingarea e-chartTypesIcon").attr({title:r._getLocalizedLabels("StackingArea"),tabindex:0})[0].outerHTML)[0].outerHTML)[0].outerHTML+t.buildTag("tr",t.buildTag("td",t.buildTag("div.e-stackingcolumn e-chartTypesIcon").attr({title:r._getLocalizedLabels("StackingColumn"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-stackingbar e-chartTypesIcon").attr({title:r._getLocalizedLabels("StackingBar"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-funnel e-chartTypesIcon").attr({title:r._getLocalizedLabels("Funnel"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-pyramid e-chartTypesIcon").attr({title:r._getLocalizedLabels("Pyramid"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-doughnut e-chartTypesIcon").attr({title:r._getLocalizedLabels("Doughnut"),tabindex:0})[0].outerHTML)[0].outerHTML)[0].outerHTML+t.buildTag("tr",t.buildTag("td",t.buildTag("div.e-scatter e-chartTypesIcon").attr({title:r._getLocalizedLabels("Scatter"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.e-bubble e-chartTypesIcon").attr({title:r._getLocalizedLabels("Bubble"),tabindex:0})[0].outerHTML)[0].outerHTML+t.buildTag("td",t.buildTag("div.waterfall e-chartTypesIcon").attr({title:r._getLocalizedLabels("WaterFall"),tabindex:0})[0].outerHTML)[0].outerHTML+(r.model.enablePivotTreeMap?t.buildTag("td",t.buildTag("div.treemap e-chartTypesIcon").attr({title:r._getLocalizedLabels("TreeMap"),tabindex:0})[0].outerHTML)[0].outerHTML:""))[0].outerHTML)[0].outerHTML)[0].outerHTML),f=r.element.find("div.e-chartTypesDialog");f.length==0&&(n(u).appendTo(r.element),r.model.enableToolBar?n(u).css("left",n(i.target).offset().left+10+"px").css("top",n(i.target).offset().top+15+"px"):n(u).css("left",this.offsetLeft+20+"px").css("top",this.offsetTop+20+"px"),r._pivotChart.model.enable3D||r.element.find(".e-"+r._pivotChart.seriesType()).addClass("e-activeChartType"));n(".e-chartTypesIcon").click(function(i){var r=n(this).parents(".e-pivotclient").data("ejPivotClient"),f,e;r.element.find(".e-chartTypesIcon").removeClass("e-activeChartType");n(u).remove();f=i.target.className.split(" ")[0].replace("e-","");r.model.chartType=f;r._pivotChart.model.enable3D=!1;r._pivotChart.model.type=f;r._pivotChart.model.commonSeriesOptions.type=f;r._pivotChart.model.commonSeriesOptions.marker=f=="funnel"||f=="pyramid"?{dataLabel:{visible:!0,shape:"none",font:{color:r.element.css("color"),size:"12px",fontWeight:"lighter"}}}:{dataLabel:{visible:!1}};jQuery.inArray(f,["line","spline","area","splinearea","stepline","steparea","stackingarea","scatter"])>-1&&!r._pivotChart.model.commonSeriesOptions.marker.visible&&(r._pivotChart.model.commonSeriesOptions.marker={shape:t.PivotChart.SymbolShapes.Circle,size:{height:12,width:12},visible:!0,connectorLine:{height:30,type:"line"},dataLabel:{visible:!1},border:{width:3,color:"white"}});r._pivotChart.getJSONRecords()!=null?(r.model.operationalMode!=t.Pivot.OperationalMode.ClientMode?(r.chartObj=null,r.chartObj=r.element.find("#"+r._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(r.chartObj)&&!t.isNullOrUndefined(r.otreemapObj)&&(r.chartObj=r.element.find("#"+r.otreemapObj._id+"TreeMapContainer").data("ejTreeMap")),t.isNullOrUndefined(r.chartObj)||(r.model.enableDeferUpdate?(r._ischartTypesChanged=!0,r.model.enablePivotTreeMap&&this.title.toLocaleLowerCase()=="treemap"?r.model.enablePivotTreeMap&&(r.element.find("#"+r._id+"_PivotChartContainer").remove(),r.element.find("#"+r._id+"_PivotChart").removeClass("e-pivotchart"),r.element.find("#"+r._id+"_PivotChart").hasClass("e-pivottreemap")?r.otreemapObj.renderTreeMapFromJSON(r._pivotChart.getJSONRecords()):(r.element.find("#"+r._id+"_PivotChart").ejPivotTreeMap({url:r.model.url,customObject:this.model.customObject,canResize:r.model.isResponsive,currentReport:r.currentReport,locale:r.locale(),size:{height:r._chartHeight,width:r._chartWidth},drillSuccess:t.proxy(r._treemapDrillSuccess,r),beforeServiceInvoke:r.model.treeMapLoad}),r.otreemapObj=r.element.find("#"+r._id+"_PivotChart").data("ejPivotTreeMap"),r._isrenderTreeMap=!0,r.chartObj=null)):(r.element.find("#"+r._id+"_PivotChartTreeMapContainer").remove(),r.element.find("#"+r._id+"_PivotChart").removeClass("e-pivottreemap"),r.element.find("#"+r._id+"_PivotChart").hasClass("e-pivotchart")?r._pivotChart.renderControlSuccess({JsonRecords:JSON.stringify(r._pivotChart.getJSONRecords()),OlapReport:r._pivotChart.getOlapReport()}):(r.element.find("#"+r._id+"_PivotChart").ejPivotChart({url:r.model.url,customObject:this.model.customObject,enableRTL:r.model.enableRTL,enableDefaultValue:this.model.enableDefaultValue,axesLabelRendering:r.model.axesLabelRendering,pointRegionClick:r.model.pointRegionClick,canResize:r.model.isResponsive,currentReport:r.currentReport,customObject:r.model.customObject,locale:r.locale(),showTooltip:!0,size:{height:r._chartHeight,width:r._chartWidth},commonSeriesOptions:{type:r.model.chartType,tooltip:{visible:!0}},beforeServiceInvoke:r.model.chartLoad,drillSuccess:t.proxy(r._chartDrillSuccess,r)}),r._pivotChart=r.element.find("#"+r._id+"_PivotChart").data("ejPivotChart"),r.chartObj=null))):r.model.enablePivotTreeMap&&this.title.toLocaleLowerCase()=="treemap"?(r._isrenderTreeMap=!0,r.model.enablePivotTreeMap&&(r.element.find("#"+r._id+"_PivotChartContainer").remove(),r.element.find("#"+r._id+"_PivotChart").removeClass("e-pivotchart"),r.element.find("#"+r._id+"_PivotChart").hasClass("e-pivottreemap")?r.otreemapObj.renderControlSuccess({JsonRecords:JSON.stringify(r._pivotChart.getJSONRecords()),OlapReport:r._pivotChart.getOlapReport()}):(r.element.find("#"+r._id+"_PivotChart").ejPivotTreeMap({url:r.model.url,customObject:r.model.customObject,canResize:r.model.isResponsive,currentReport:r.currentReport,locale:r.locale(),size:{height:r._chartHeight,width:r._chartWidth},drillSuccess:t.proxy(r._treemapDrillSuccess,r),beforeServiceInvoke:r.model.treeMapLoad}),r.otreemapObj=r.element.find("#"+r._id+"_PivotChart").data("ejPivotTreeMap"),r.chartObj=null))):(r.element.find("#"+r._id+"_PivotChartTreeMapContainer").remove(),r.element.find("#"+r._id+"_PivotChart").removeClass("e-pivottreemap"),r.element.find("#"+r._id+"_PivotChart").hasClass("e-pivotchart")?r._pivotChart.renderControlSuccess({JsonRecords:JSON.stringify(r._pivotChart.getJSONRecords()),OlapReport:r._pivotChart.getOlapReport()}):(r.element.find("#"+r._id+"_PivotChart").ejPivotChart({url:r.model.url,customObject:r.model.customObject,enableRTL:r.model.enableRTL,enableDefaultValue:this.model.enableDefaultValue,axesLabelRendering:r.model.axesLabelRendering,pointRegionClick:r.model.pointRegionClick,canResize:r.model.isResponsive,currentReport:r.currentReport,customObject:r.model.customObject,locale:r.locale(),showTooltip:!0,size:{height:r._chartHeight,width:r._chartWidth},commonSeriesOptions:{type:r.model.chartType,tooltip:{visible:!0}},beforeServiceInvoke:r.model.chartLoad,drillSuccess:t.proxy(r._chartDrillSuccess,r)}),r._pivotChart=r.element.find("#"+r._id+"_PivotChart").data("ejPivotChart"),r.chartObj=null)),r.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(e=JSON.stringify(r.model.customObject),r.doAjaxPost("POST",r.model.url+"/"+r.model.serviceMethodSettings.updateReport,JSON.stringify({action:"chartTypeChanged",clientParams:f,olapReport:r.currentReport,clientReports:r.reports,customObject:e}),r._chartTypeChangedSuccess)))):r._pivotChart.renderControlSuccess({JsonRecords:JSON.stringify(r._pivotChart.getJSONRecords()),OlapReport:r._pivotChart.getOlapReport()}),r.model.isResponsive&&(r.chartObj=r.element.find("#"+r._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(r.chartObj)||r.element.find("#"+r.chartObj._id).ejChart("option",{model:{size:{height:r._chartHeight}}}))):(r._isTimeOut=!1,r._waitingPopup.hide())})});this.element.find(".e-dialogCancelBtn").on(t.eventType.click,function(i){var r=n(this).parents(".e-pivotclient").data("ejPivotClient");i.preventDefault();r.element.find(".e-dialog").hide();r.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog), .e-clientDialog").remove();t.Pivot.closePreventPanel(r)});this.element.find(".e-dialogOKBtn").on(t.eventType.click,function(r){var u=n(this).parents(".e-pivotclient").data("ejPivotClient"),l,g,w,s,k,tt,it,y,st,b,rt,ht,d,ut,ct,lt,ft,h,c,et,f,p,v,a,ot;if(r.preventDefault(),u.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(u.chartObj=u.element.find("#"+u._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(u.chartObj)&&u.model.enablePivotTreeMap&&!t.isNullOrUndefined(u.otreemapObj)&&(u.chartObj=u.element.find("#"+u.otreemapObj._id+"TreeMapContainer").data("ejTreeMap"))),l=t.browserInfo().name=="msie"&&t.browserInfo().version<=8?u._dialogTitle:u.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog) .e-titlebar")[0].textContent==i?u.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog) .e-titlebar")[0].innerText:u.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog) .e-titlebar")[0].textContent,l==u._getLocalizedLabels("Load"))if(u._currentRecordName=u.element.find(".reportNameList")[0].value,s=u.element.find(".reportNameList")[0].value,s!="")u._isTimeOut=!0,u.model.operationalMode==t.Pivot.OperationalMode.ServerMode?u._waitingPopup.show():setTimeout(function(){u._isTimeOut&&u._waitingPopup.show()},800),u._fieldMembers={},u._fieldSelectedMembers={},u._measureSortOrder=u.model.sortCubeMeasures,u.model.operationalMode==t.Pivot.OperationalMode.ServerMode?(u.model.beforeServiceInvoke!=null&&u._trigger("beforeServiceInvoke",{action:"loadReport",element:this.element,customObject:u.model.customObject}),h=JSON.stringify(u.model.customObject),u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.loadReport,JSON.stringify({reportName:s,operationalMode:u.model.operationalMode,analysisMode:u.model.analysisMode,olapReport:u.currentReport,clientReports:u.reports,customObject:h,clientParams:JSON.stringify(u.model.enableMeasureGroups+"-"+u.model.sortCubeMeasures)}),u.model.analysisMode==t.Pivot.AnalysisMode.Pivot?u._renderControlSuccess:u._toolbarOperationSuccess)):(g={url:"",reportCollection:u._clientReportCollection,selectedReport:s,mode:u.model.analysisMode},u._trigger("loadReport",{targetControl:u,loadReportSetting:g}),u.model.enableLocalStorage?(u.model.dataSource=g.reportCollection[0],u._clientReportCollection=g.reportCollection,u.refreshControl(),u._refreshReportList(),u._pivotSchemaDesigner&&u._pivotSchemaDesigner._refreshPivotButtons()):(h=JSON.stringify(u.model.customObject),u.doAjaxPost("POST",g.url+"/"+u.model.serviceMethodSettings.loadReport,JSON.stringify({reportName:s,operationalMode:u.model.operationalMode,analysisMode:u.model.analysisMode,olapReport:u.currentReport,clientReports:u.reports,customObject:h,clientParams:JSON.stringify(u.model.enableMeasureGroups+"-"+u.model.sortCubeMeasures)}),u._clientToolbarOperationSuccess)));else return t.Pivot._createErrorDialog(u._getLocalizedLabels("SelectRecordAlertMsg"),u._getLocalizedLabels("Warning"),u),!1;else if(l==u._getLocalizedLabels("Remove")){if(s=u.element.find(".reportNameList")[0].value,s==""||t.isNullOrUndefined(s))return t.Pivot._createErrorDialog(u._getLocalizedLabels("SelectRecordAlertMsg"),u._getLocalizedLabels("Warning"),u),!1;u._currentRecordName=s==u._currentRecordName?"":u._currentRecordName;u._isTimeOut=!0;setTimeout(function(){u._isTimeOut&&u._waitingPopup.show()},800);u.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&(k={url:"",selectedReport:s,mode:u.model.analysisMode},u._trigger("loadReport",{targetControl:u,fetchReportSetting:k}));u.model.beforeServiceInvoke!=null&&u._trigger("beforeServiceInvoke",{action:"removeDBReport",element:this.element,customObject:u.model.customObject,currentReportName:s});h=JSON.stringify(u.model.customObject);u._fieldMembers={};u._fieldSelectedMembers={};u.model.enableLocalStorage||u.doAjaxPost("POST",(u.model.operationalMode==t.Pivot.OperationalMode.ClientMode?k.url:u.model.url)+"/"+u.model.serviceMethodSettings.removeDBReport,JSON.stringify({reportName:s,operationalMode:u.model.operationalMode,analysisMode:u.model.analysisMode,customObject:h}),u._toolbarOperationSuccess)}else if(l==u._getLocalizedLabels("Rename")){if(w=u.element.find(".renameReport").val(),s=u.element.find(".reportNameList")[0].value,s==""||t.isNullOrUndefined(s)||w==""||t.isNullOrUndefined(w))return s==""||t.isNullOrUndefined(s)?t.Pivot._createErrorDialog(u._getLocalizedLabels("SelectRecordAlertMsg"),u._getLocalizedLabels("Warning"),u):(w==""||t.isNullOrUndefined(w))&&t.Pivot._createErrorDialog(u._getLocalizedLabels("SetReportNameAlertMsg"),u._getLocalizedLabels("Warning"),u),!1;u._isTimeOut=!0;setTimeout(function(){u._isTimeOut&&u._waitingPopup.show()},800);u.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&(k={url:"",selectedReport:s,mode:u.model.analysisMode},u._trigger("loadReport",{targetControl:u,fetchReportSetting:k}));u.model.beforeServiceInvoke!=null&&u._trigger("beforeServiceInvoke",{action:"renameDBReport",element:this.element,customObject:u.model.customObject,currentReportName:s,renameReportName:w});h=JSON.stringify(u.model.customObject);u.model.enableLocalStorage||u.doAjaxPost("POST",(u.model.operationalMode==t.Pivot.OperationalMode.ClientMode?k.url:u.model.url)+"/"+u.model.serviceMethodSettings.renameDBReport,JSON.stringify({selectedReport:s,renameReport:w,operationalMode:u.model.operationalMode,analysisMode:u.model.analysisMode,customObject:h}),u._toolbarOperationSuccess)}else if(u.element.find(".e-sortingDlg").length>0){if(u.element.find(".e-sortEnable")[0].checked==!0&&u.element.find(".e-measuresList")[0].value=="")return t.Pivot._createErrorDialog(u._getLocalizedLabels("FilterMeasureSelectionAlertMsg"),u._getLocalizedLabels("Warning"),u),!1;if(u.element.find(".e-filterEnable")[0].checked==!0){if(u.element.find(".fMeasuresList")[0].value=="")return t.Pivot._createErrorDialog(u._getLocalizedLabels("FilterMeasureSelectionAlertMsg"),u._getLocalizedLabels("Warning"),u),!1;if(u.element.find(".e-filterCondition")[0].value=="")return t.Pivot._createErrorDialog(u._getLocalizedLabels("FilterConditionAlertMsg"),u._getLocalizedLabels("Warning"),u),!1;if(u.element.find(".filterFrom")[0].value=="")return t.Pivot._createErrorDialog(u._getLocalizedLabels("FilterStartValueAlertMsg"),u._getLocalizedLabels("Warning"),u),!1;if((u.element.find(".e-filterCondition")[0].value=="Between"||u.element.find(".e-filterCondition")[0].value=="NotBetween")&&u.element.find(".filterTo")[0].value=="")return t.Pivot._createErrorDialog(u._getLocalizedLabels("FilterEndValueAlertMsg"),u._getLocalizedLabels("Warning"),u),!1;u._excelFilterInfo=[]}if(tt=null,it=null,(u._isSorted==!1||u._isSorted==!0)&&u.element.find(".e-sortEnable")[0].checked==!0?(y=u.element.find("li[data-tag*='"+u.element.find(".e-measuresList")[0].value+"']"),y.length>1&&(y=u.element.find("li[data-tag^='[Measures].["+u.element.find(".e-measuresList")[0].value+"']li[data-tag*='"+u.element.find(".e-measuresList")[0].value+"']")||u.element.find("li[data-tag^='[MEASURES].["+u.element.find(".e-measuresList")[0].value+"']li[data-tag*='"+u.element.find(".e-measuresList")[0].value+"']")),tt=n(y).attr("data-tag")+"::"+(u.element.find(".e-radioBtnAsc")[0].checked==!0?"ASC":"DESC")+"::"+u._axis+"::"+(u.element.find(".e-preserveHrchy")[0].checked==!0?"PHT":"PHF")):tt=u._isSorted==!0&&u.element.find(".e-sortDisable")[0].checked==!0?"Disable Sorting:: ::"+u._axis+":: ":" :: ::"+u._axis+":: ",(u._isFiltered==!1||u._isFiltered==!0)&&u.element.find(".e-filterEnable")[0].checked==!0?(y=u.element.find("li[data-tag*='"+u.element.find(".fMeasuresList")[0].value+"']"),y.length>1&&(y=u.element.find("li[data-tag^='[Measures].["+u.element.find(".fMeasuresList")[0].value+"']li[data-tag*='"+u.element.find(".fMeasuresList")[0].value+"']")||u.element.find("li[data-tag^='[MEASURES].["+u.element.find(".fMeasuresList")[0].value+"']li[data-tag*='"+u.element.find(".fMeasuresList")[0].value+"']")),it=n(y).attr("data-tag")+"::"+u.element.find(".e-filterCondition")[0].value+"::"+u.element.find(".filterFrom")[0].value+"::"+u.element.find(".filterTo")[0].value):it=u._isFiltered==!0&&u.element.find(".e-filterDisable")[0].checked==!0?"Disable Filtering":"",u._SortFilterDetails=tt+"||"+it,u._isTimeOut=!0,setTimeout(function(){u._isTimeOut&&u._waitingPopup.show()},800),u.element.find(".e-sortEnable")[0].checked==!0||u.element.find(".e-filterEnable")[0].checked==!0)u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.toolbarServices,JSON.stringify({action:"toolbarOperation",toolbarOperation:"SortOrFilter",clientInfo:u._SortFilterDetails,olapReport:u.currentReport,clientReports:u.reports,customObject:JSON.stringify(u.model.customObject)}),u._toolbarOperationSuccess);else if(u.element.find(".e-sortDisable")[0].checked==!0||u.element.find(".e-filterDisable")[0].checked==!0)if(u._isSorted==!0||u._isFiltered==!0)u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.toolbarServices,JSON.stringify({action:"toolbarOperation",toolbarOperation:"SortOrFilter",clientInfo:u._SortFilterDetails,olapReport:u.currentReport,clientReports:u.reports,customObject:JSON.stringify(u.model.customObject)}),u._toolbarOperationSuccess),u._isSorted=!1;else return t.Pivot._createErrorDialog(u._getLocalizedLabels("FilterInvalidAlertMsg"),u._getLocalizedLabels("Warning"),u),u._isTimeOut=!1,u._waitingPopup.hide(),!1}else{if(n(".e-memberEditorDiv").length>0&&r.target.id=="OKBtn"){if(u.element.find(".e-editorTreeView").find("li span.e-searchfilterselection").length>0&&u.element.find(".e-editorTreeView").ejTreeView("removeNode",u.element.find(".e-editorTreeView").find("li span.e-searchfilterselection").closest("li")),u.model.enableAdvancedFilter&&u._excelFilterInfo.length>0&&(u._removeFilterTag(u._selectedFieldName),u._selectedLevelUniqueName&&u._removeFilterTag(u._selectedLevelUniqueName)),u._currentItem.indexOf(u._getLocalizedLabels("Measures"))<0&&t.Pivot.updateTreeView(u),!u.model.enableMemberEditorPaging&&t.Pivot._getSelectedTreeState(u._currentAxis=="Slicers"?!0:!1,u)==""&&u._args_innerHTML!=u._getLocalizedLabels("Measures")&&u._args_innerHTML!="ToolbarButtons")return!1;if(u._currentItem.indexOf(u._getLocalizedLabels("Measures"))<0){if(u._currentReportItems.length!=0)for(st=!1,c=0;c<u._currentReportItems.length;c++)if(u._currentReportItems[c]==u._currentItem){st=!0;break}st&&u._currentReportItems.length!=0||u._currentReportItems.push(u._currentItem);u._treeViewData[u._currentItem]=u._memberTreeObj.dataSource()}}if(b=null,u._args_innerHTML!="ToolbarButtons"){if(u._isMembersFiltered||u.model.enableMemberEditorPaging)u._args_innerHTML==u._getLocalizedLabels("Measures")?b="Measures:"+u._getMeasuresList():u._args_innerHTML!="ToolbarButtons"&&u._args_innerHTML!=i&&(t.Pivot.updateTreeView(u),b=(u._args_innerHTML=="KPIs"?u._getUnSelectedNodes():t.Pivot._getUnSelectedTreeState(u))+"CHECKED"+(u._args_innerHTML=="KPIs"?u._getSelectedNodes(u._currentAxis=="Slicers"?!0:!1):t.Pivot._getSelectedTreeState(u._currentAxis=="Slicers"?!0:!1,u)));else{u.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog), .e-clientDialog").remove();t.Pivot.closePreventPanel(u);return}u._isTimeOut=!0;setTimeout(function(){u._isTimeOut&&u._waitingPopup.show()},800);u._args_innerHTML==u._getLocalizedLabels("Measures")&&b!=null&&b.split(":")[1]==""&&n(u.element).find(".e-splitBtn").each(function(t,i){i.firstChild.innerHTML==u._getLocalizedLabels("Measures")&&n(i).remove()});n(u).find(".e-dialog").hide();u.model.enableMemberEditorPaging&&u._args_innerHTML!=u._getLocalizedLabels("Measures")&&u._args_innerHTML!=u._getLocalizedLabels("KPIs")&&u.element.find(".e-nextPageDiv").length>0&&(b=t.Pivot._getUnSelectedTreeState(u)+"CHECKED"+t.Pivot._getSelectedTreeState(u._currentAxis=="Slicers"?!0:!1,u));u.model.beforeServiceInvoke!=null&&u._trigger("beforeServiceInvoke",{action:"filtering",element:this.element,customObject:u.model.customObject});rt=t.Pivot._getEditorMember(u._dimensionName.split(":").length>1?u._dimensionName.split(":")[1]:u._dialogTitle,u,!1);u._fieldSelectedMembers[u._dimensionName.split(":").length>1?u._dimensionName.split(":")[1]:u._dialogTitle]=n.map(u._fieldMembers[u._dimensionName.split(":").length>1?u._dimensionName.split(":")[1]:u._dialogTitle],function(n){if(!n.checked)return n}).length==0?"All":rt!="All"&&rt!="multiple"?rt:u._getLocalizedLabels("MultipleItems");u.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&u.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(ht=u.element.find("#"+u._id+"_reportList").data("ejDropDownList"),u._slicerBtnTextInfo[ht.selectedIndexValue]=u._fieldSelectedMembers);u._setSplitBtnTitle();h=JSON.stringify(u.model.customObject);u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.filterElement,JSON.stringify({action:"filtering",clientParams:b,olapReport:u.currentReport,clientReports:u.reports,customObject:h}),u._filterElementSuccess)}else if(u.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog) .e-titlebar").first().text()==u._getLocalizedLabels("RemoveReport")&&u.reportsCount<2)u.element.find(".e-dialog").hide();else{if(n.trim(u.element.find(".reportName").val())===""&&u.element.find(".reportName").val()!=i)return u.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog) .e-titlebar").first().text()==u._getLocalizedLabels("NewReport")||u.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog) .e-titlebar").first().text()==u._getLocalizedLabels("AddReport")||u.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog) .e-titlebar").first().text()==u._getLocalizedLabels("RenameReport")?t.Pivot._createErrorDialog(u._getLocalizedLabels("SetReportNameAlertMsg"),u._getLocalizedLabels("Warning"),u):t.Pivot._createErrorDialog(u._getLocalizedLabels("SetReportNameAlertMsg"),u._getLocalizedLabels("Warning"),u),!1;this.reportDropTarget=u.element.find("#"+u._id+"_reportList").data("ejDropDownList");var e=n.trim(u.element.find(".reportName").val())||this.reportDropTarget.selectedTextValue,o=u.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog) .e-titlebar").first().attr("data-tag"),nt=o=="New Report"?"":u.currentReport,at=o=="New Report"?"":u.reports;if(u._currentRecordName=o=="New Report"?"":u._currentRecordName,u.element.find(".e-dialog").hide(),u._isTimeOut=!0,u.model.operationalMode==t.Pivot.OperationalMode.ServerMode?u._waitingPopup.show():setTimeout(function(){u._isTimeOut&&u._waitingPopup.show()},800),o=="SaveAs Report"&&u.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(d=u._updateReportCollection(!0)),(o=="Add Report"||o=="New Report"||o=="Remove Report")&&u.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&(u.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&u._pivotSchemaDesigner&&(u._pivotSchemaDesigner._tempFilterData=[]),o=="New Report"?u._repCol=n.grep(u._repCol,function(n){n.CubeName==u.currentCubeName&&(n.slicerBtnTextInfo={})}):o=="Add Report"&&(u._slicerBtnTextInfo[this.reportDropTarget.selectedIndexValue]=u._fieldSelectedMembers,d=u._updateReportCollection(!1)),o=="Remove Report"?(delete u._slicerBtnTextInfo[this.reportDropTarget.selectedIndexValue],u._fieldSelectedMembers=u._slicerBtnTextInfo[this.reportDropTarget.selectedIndexValue-1]):u._fieldSelectedMembers={}),u.model.operationalMode==t.Pivot.OperationalMode.ClientMode)ut={data:u.model.dataSource.data,reportName:e,enableAdvancedFilter:u.model.dataSource.enableAdvancedFilter,columns:[],cube:u.model.dataSource.cube,catalog:u.model.dataSource.catalog,reportName:e,rows:[],values:[],filters:[],pagerOptions:u.model.dataSource.pagerOptions},f=u.element.find(".reportlist").data("ejDropDownList"),u.element.find(".reportlist").ejDropDownList("option","change",t.proxy(u._reportChanged,u)),(o=="New Report"||o=="Add Report")&&u.model.enableVirtualScrolling&&(u._pivotGrid._categPageCount=null,u._pivotGrid._seriesPageCount=null),o=="New Report"?(u.model.dataSource=ut,u._clientReportCollection=[ut],u.element.find(".reportlist").ejDropDownList("option","dataSource",[{name:e}]),f.selectItemByText(e),u._fieldMembers={},u._fieldSelectedMembers={}):o=="Add Report"?(u.model.dataSource=ut,u.model.dataSource.reportName=e,ct=n.map(u._clientReportCollection,function(n){if(n.reportName==u.model.dataSource.reportName)return n}),u._clientReportCollection=n.map(u._clientReportCollection,function(n){if(n.reportName!=u.model.dataSource.reportName)return n}),u._clientReportCollection.push(u.model.dataSource),u._fieldMembers={},u._fieldSelectedMembers={},ct.length==0?(f=u.element.find(".reportlist").data("ejDropDownList"),f.model.dataSource.push({name:e}),p=JSON.stringify(f.model.dataSource),u.element.find(".reportlist").ejDropDownList("option","dataSource",JSON.parse(p)),f.selectItemByText(e)):u._clientReportCollection.length>0&&u._reportChanged()):o=="Rename Report"?(u._clientReportCollection=n.map(u._clientReportCollection,function(n){return n.reportName==u.model.dataSource.reportName&&(n.reportName=e),n}),p=JSON.stringify(n.map(f.model.dataSource,function(n){return n.name!=f.getValue()?n:{name:e}})),u.model.dataSource.reportName=e,u.element.find(".reportlist").ejDropDownList("option","dataSource",JSON.parse(p)),f.selectItemByText(e),u._currentItem=""):o=="Remove Report"?(u._clientReportCollection=n.map(u._clientReportCollection,function(n){if(n.reportName!=e)return n}),lt=n(f.getSelectedItem()).index()>=u._clientReportCollection.length?u._clientReportCollection.length-1:n(f.getSelectedItem()).index(),u.model.dataSource=u._clientReportCollection[lt],p=JSON.stringify(n.map(f.model.dataSource,function(n){if(n.name!=f.getValue())return n})),u.element.find(".reportlist").ejDropDownList("option","dataSource",JSON.parse(p)),u._fieldMembers={},u._fieldSelectedMembers={},f.model.dataSource.length>0&&f.selectItemByText(u.model.dataSource.reportName)):o=="SaveAs Report"?(u._currentRecordName=e,ft={url:"",reportName:e,reportCollection:u._clientReportCollection,mode:u.model.analysisMode,filterCollection:u._currentReportItems},u._trigger("saveReport",{targetControl:u,saveReportSetting:ft}),u.model.enableLocalStorage||(h=JSON.stringify(u.model.customObject),u.doAjaxPost("POST",ft.url+"/"+u.model.serviceMethodSettings.saveReport,JSON.stringify({reportName:ft.reportName,operationalMode:u.model.operationalMode,analysisMode:u.model.analysisMode,olapReport:JSON.stringify(u.model.dataSource),clientReports:(t.isNullOrUndefined(d)?JSON.stringify(u._clientReportCollection):d)+":>>:"+JSON.stringify(u._currentReportItems),customObject:h}),u._toolbarOperationSuccess))):o!="Rename Report"&&(u.refreshControl(),u._pivotSchemaDesigner&&u._pivotSchemaDesigner._refreshPivotButtons()),u._isTimeOut=!1,u._waitingPopup.hide();else if(f=u.element.find(".reportlist").data("ejDropDownList"),u._clientReportCollection=n.map(u._clientReportCollection,function(n){return n.name==f.getValue()&&(n.report=nt),n}),o=="SaveAs Report")u._currentRecordName=e,u.model.beforeServiceInvoke!=null&&u._trigger("beforeServiceInvoke",{action:"saveReport",element:this.element,customObject:u.model.customObject}),h=JSON.stringify(u.model.customObject),u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.saveReport,JSON.stringify({reportName:e,operationalMode:u.model.operationalMode,analysisMode:u.model.analysisMode,olapReport:nt,clientReports:u.model.analysisMode=="pivot"?JSON.stringify(u._clientReportCollection):t.isNullOrUndefined(d)?JSON.stringify(u._clientReportCollection):d,customObject:h}),u._toolbarOperationSuccess);else if(u.model.beforeServiceInvoke!=null&&u._trigger("beforeServiceInvoke",{action:"toolbarOperation",element:this.element,customObject:u.model.customObject}),h=JSON.stringify(u.model.customObject),u.model.analysisMode==t.Pivot.AnalysisMode.Olap)u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.toolbarServices,JSON.stringify({action:"toolbarOperation",toolbarOperation:o,clientInfo:e,olapReport:nt,clientReports:at,customObject:h}),u._toolbarOperationSuccess);else{if(o==u._getLocalizedLabels("NewReport"))u._currentReportName=e,u._fieldMembers={},u._fieldSelectedMembers={};else if(o==u._getLocalizedLabels("AddReport"))u._fieldMembers={},u._fieldSelectedMembers={},f.model.dataSource.push({name:e}),f.selectItemByText(e),u._currentReportName=e;else if(o==u._getLocalizedLabels("RemoveReport")){for(c=0;c<u._clientReportCollection.length;c++)u._clientReportCollection[c].name==u._currentReportName&&u._clientReportCollection.splice(c,1);for(f=u.element.find(".reportlist").data("ejDropDownList"),c=0;c<f.model.dataSource.length;c++)f.model.dataSource[c].name==u._currentReportName&&f.model.dataSource.splice(c,1);f.selectItemByText(f.model.dataSource[0].name);nt=u._clientReportCollection[0].report;e=u._clientReportCollection[0].name;u._currentReport="";u._currentReportName=e;u._fieldMembers={};u._fieldSelectedMembers={}}else if(o==u._getLocalizedLabels("RenameReport")){et="";try{et=JSON.parse(u.getOlapReport()).Report}catch(vt){et=u.getOlapReport()}u._clientReportCollection=n.map(u._clientReportCollection,function(n){return n.name==u._currentReportName&&(n.name=e),n});f=u.element.find(".reportlist").data("ejDropDownList");p=JSON.stringify(n.map(f.model.dataSource,function(n){return n.name!=f.getValue()?n:{name:e,report:et}}));u.element.find(".reportlist").ejDropDownList("option","dataSource",JSON.parse(p));u._isReportListAction=!1;f.selectItemByText(e);u._isReportListAction=!0;u._currentReportName=e;u._isTimeOut=!1;u._waitingPopup.hide();t.Pivot.closePreventPanel(u);return}u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.toolbarServices,JSON.stringify({action:o,args:JSON.stringify({clientInfo:e,currentReport:nt}),customObject:h}),u._toolbarOperationSuccess)}}}u._isTimeOut&&(u._isTimeOut=!1);u.model.showReportCollection&&l!=u._getLocalizedLabels("NewReport")&&l!=u._getLocalizedLabels("AddReport")&&l!=u._getLocalizedLabels("RenameReport")&&l!=u._getLocalizedLabels("RemoveReport")&&l!=u._getLocalizedLabels("MemberEditor")&&l!=u._getLocalizedLabels("MeasureEditor")&&(v=u.element.find(".e-collectionlist").data("ejDropDownList").model.dataSource.slice(),a=t.isNullOrUndefined(u.element.find(".e-collectionlist").data("ejDropDownList").selectedTextValue)?"":u.element.find(".e-collectionlist").data("ejDropDownList").selectedTextValue,l==u._getLocalizedLabels("Rename")?(ot=v.indexOf(u.element.find(".reportNameList")[0].value),v[ot]=u.element.find(".renameReport").val(),a=u.element.find(".reportNameList")[0].value==a?u.element.find(".renameReport").val():a):l==u._getLocalizedLabels("Remove")?(ot=v.indexOf(u.element.find(".reportNameList")[0].value),v.splice(ot,1)):(a=u.element.find(".reportName")[0].value,v.push(u.element.find(".reportName")[0].value)),u.element.find(".e-collectionlist").data("ejDropDownList").option("dataSource",v),a=n.inArray(a,v)==-1?"":a,v.length>0&&a!=""&&(u.element.find(".e-collectionlist").data("ejDropDownList").option("change",""),u.element.find(".e-collectionlist").data("ejDropDownList").selectItemByText(a),u.element.find(".e-collectionlist").ejDropDownList("option","change",t.proxy(u._collectionChange,u))));u.model.showReportCollection&&l==u._getLocalizedLabels("NewReport")&&u.element.find(".e-collectionlist").length>0&&u.element.find(".e-collectionlist").data("ejDropDownList").selectItemByText();u.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog), .e-clientDialog").remove();u.element.find("#preventDiv").remove()});this.element.find(".e-searchEditorTree").click(function(i){var r=n(this).parents(".e-pivotclient").data("ejPivotClient");t.Pivot._searchEditorTreeNodes(i,r)});this.element.find(".e-calcMemberImg").click(function(){var i=n(this).parents(".e-pivotclient").data("ejPivotClient"),f,u,r,e;i.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&i.element.find(".e-calcMemberDialog").remove();i.element.find(".e-calcMemberDialog").length>0?(i._calcMemberTreeObj.collapseAll(),t.Pivot.openPreventPanel(i),i._calcMemberDialog.open(),i.element.find("#"+i._id+"_captionFieldCM").val(""),i.element.find("#"+i._id+"_expressionFieldCM").val(""),i.element.find("#"+i._id+"_memberTypeFieldCM").data("ejDropDownList").selectItemsByIndices(0),i.element.find("#"+i._id+"_dimensionFieldCM").data("ejDropDownList").selectItemsByIndices(0),i.element.find("#"+i._id+"_formatFieldCM").data("ejDropDownList").selectItemsByIndices(0),i.element.find("#"+i._id+"_customFormatFieldCM").val("")):(i._waitingPopup.show(),i.model.operationalMode==t.Pivot.OperationalMode.ServerMode?(i.model.beforeServiceInvoke!=null&&i._trigger("beforeServiceInvoke",{action:"fetchCalcMemberTreeView",element:i.element,customObject:i.model.customObject}),f=JSON.stringify(i.model.customObject),i.doAjaxPost("POST",i.model.url+"/"+i.model.serviceMethodSettings.fetchMemberTreeNodes,JSON.stringify({action:"fetchCalcMemberTreeView",dimensionName:"calcMember>#>"+i.model.sortCubeMeasures,olapReport:i.currentReport,customObject:f}),t.proxy(t.Pivot._createCalcMemberDialog,i))):(u=[],r=i.element.find(".e-schemaFieldTree").data("ejTreeView").model.fields.dataSource,r=n.grep(r,function(n){return n.id&&(n.id=n.id.replace(/\]/g,"_").replace(/\[/g,"_").replace(/\./g,"_").replace(/ /g,"_")),n.pid&&(n.pid=n.pid.replace(/\]/g,"_").replace(/\[/g,"_").replace(/\./g,"_").replace(/ /g,"_")),n.spriteCssClass.indexOf("e-level")>-1&&u.push({id:n.id+"_1",pid:n.id,name:"(Blank)",hasChildren:!1,spriteCssClass:""}),n}),r=n.merge(u,r),i._selectedCalcMember=null,e={CubeTreeInfo:JSON.stringify(r)},t.Pivot._createCalcMemberDialog(e,i)))});this.element.find(".e-unCheckAll,.e-checkAll").click(function(t){var i=n(this).parents(".e-pivotclient").data("ejPivotClient"),r=i.element.find(".e-editorTreeView"),u=null;t.target.className.indexOf("e-checkAll")>-1?(i._isOptionSearch?(u=n(r).find("li[data-isItemSearch='true']"),u.length>0&&n(u).each(function(t,i){n(r).ejTreeView("checkNode",n(i))})):n(r).ejTreeView("checkAll"),n(r).find("li").length>0&&n(r).find("li").find("span.e-checkmark").length>0&&(i.element.find(".e-dialogOKBtn").data("ejButton").enable(),i.element.find(".e-dialogOKBtn").removeAttr("disabled"),i._isMembersFiltered=!0),(i.model.enableMemberEditorPaging||i._editorTreeData.length>0)&&setTimeout(function(){if(i._waitingPopup.show(),i._editorSearchTreeData.length>0)for(var t=0;t<i._editorSearchTreeData.length;t++)n(i._editorTreeData).each(function(n,r){if(i._editorSearchTreeData[t].id==r.id)return i._editorSearchTreeData[t].checkedStatus=!0,r.checkedStatus=!0,!1});else n(i._editorTreeData).each(function(n,t){t.checkedStatus=!0});i._waitingPopup.hide()},0)):(i._isOptionSearch?(u=n(r).find("li[data-isItemSearch='true']"),u.length>0&&n(u).each(function(t,i){n(r).ejTreeView("uncheckNode",n(i))})):i.model.enableMemberEditorPaging&&n(r).find("li span.e-searchfilterselection").length>0?(n(r).ejTreeView("unCheckAll"),i._isSelectSearchFilter?n(r).find("li span.e-searchfilterselection").closest("li").find("span.e-chk-image").removeClass("e-stop").addClass("e-checkmark"):n(r).find("li span.e-searchfilterselection").closest("li").find("span.e-chk-image").removeClass("e-checkmark").removeClass("e-stop")):n(r).ejTreeView("unCheckAll"),(n(r).find("li").length>0&&!n(r).find("li").find("span.e-checkmark").length>0||n(r).find("li").length>0&&n(r).find("li").find("span.e-checkmark").length==1&&n(r).find("li span.e-searchfilterselection").closest("li").find("span.e-checkmark").length>0)&&(i.element.find(".e-dialogOKBtn").data("ejButton").disable(),i.element.find(".e-dialogOKBtn").attr("disabled","disabled")),(i.model.enableMemberEditorPaging||i._editorTreeData.length>0)&&setTimeout(function(){if(i._waitingPopup.show(),i._editorSearchTreeData.length>0)for(var t=0;t<i._editorSearchTreeData.length;t++)n(i._editorTreeData).each(function(n,r){if(i._editorSearchTreeData[t].id==r.id)return i._editorSearchTreeData[t].checkedStatus=!1,r.checkedStatus=!1,!1});else n(i._editorTreeData).each(function(n,t){t.checkedStatus=!1});n(i._editorTreeData).each(function(n,t){if(t.checkedStatus)return i.element.find(".e-dialogOKBtn").data("ejButton").enable(),i.element.find(".e-dialogOKBtn").removeAttr("disabled"),i._isMembersFiltered=!0,!1});i._waitingPopup.hide()},0))})},_generateCalculatedMember:function(t,i){for(var o,c,u,f,l,e=n(i).find("Axis:eq(0) Tuple"),s=[],h={},r=0;r<e.length;r++)o=n(n(i).find("Axis:eq(0) Tuple:eq("+r+")").children().children()[0]).text(),c=n(n(i).find("Axis:eq(0) Tuple:eq("+r+")").children().children()[1]).text()==""?"(Blank)":n(n(i).find("Axis:eq(0) Tuple:eq("+r+")").children().children()[1]).text(),h={hasChildren:n(e[r]).find("CHILDREN_CARDINALITY").text()!="0",checkedStatus:!0,id:o.replace(/\]*\]/g,"-").replace(/\[*\[/g,"-").replace(/ /g,"_").replace(/&/g,"_"),name:c,tag:o,level:parseInt(n(e[r]).find("LNum").text())},s.push(h);for(u=this.element.find(".e-cubeTreeViewCalcMember").data("ejTreeView"),f=s,u.addNodes(f,this._calcExpanded.id),r=0;r<f.length;r++)r==0&&u.element.find("#"+this._calcExpanded.id).find("li:eq(0)").remove(),l=f[r].tag,u.element.find("#"+this._calcExpanded.id).find("li:eq("+r+")").attr("data-tag",l);this._waitingPopup.hide()},_updateReportCollection:function(i){var r=-1,u,c,e,o,s,h,f;return this.model.operationalMode==t.Pivot.OperationalMode.ClientMode?(f=this.model.dataSource.cube,s=jQuery.extend(!0,{},this.model.dataSource),h=JSON.parse(JSON.stringify(this._clientReportCollection))):(f=this.currentCubeName,s=this.currentReport,h=this.reports),u=this.element.find("#"+this._id+"_reportList").data("ejDropDownList"),!t.isNullOrUndefined(this._pivotSchemaDesigner)&&this._pivotSchemaDesigner.element.find(".cubeList").length>0?(c=this._pivotSchemaDesigner.element.find(".cubeList").data("ejDropDownList"),this._repCol=this._pivotSchemaDesigner._repCollection):c=this.element.find("#"+this._id+"_cubeSelector").data("ejDropDownList"),this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&(this._slicerBtnTextInfo[u.selectedIndexValue]=this._fieldSelectedMembers),e={CubeName:f,CurrentReport:s,Reports:h,ReportIndex:u.selectedIndexValue,ReportList:JSON.parse(JSON.stringify(u.model.dataSource)),_fieldSelectedMembers:this._fieldSelectedMembers,slicerBtnTextInfo:this._slicerBtnTextInfo,calculatedMembers:this.model.calculatedMembers,measureSortOrder:this.model.sortCubeMeasures},n.map(this._repCol,function(n,t){n.CubeName==f&&(r=t)}),r!=-1?(this._repCol[r]=e,o=r):(o=this._repCol.length,this._repCol.push(e)),i&&this._repCol.push({cubeIndex:o}),JSON.stringify(this._repCol)},_getLocalizedLabels:function(n){return t.isNullOrUndefined(t.PivotClient.Locale[this.locale()])||t.PivotClient.Locale[this.locale()][n]==i?t.PivotClient.Locale["en-US"][n]:t.PivotClient.Locale[this.locale()][n]},_unWireEvents:function(){n(this.element.find(".e-dialogCancelBtn, .e-dialogOKBtn, .e-newReportImg, .e-addReportImg, .e-removeReportImg, .e-renameReportImg, .e-pvtBtn, .e-removeSplitBtn, .e-unCheckAll, .e-checkAll, .e-removeMeasure, .e-toggleCollapseButton, .e-toggleExpandButton, .e-reportDBImg, .e-saveAsReportImg, .e-saveReportImg, .e-loadReportImg, .e-removeDBReportImg, .e-renameDBReportImg, .e-mdxImg,.maximizedView,.e-colSortFilterImg, .e-rowSortFilterImg, .e-chartTypesImg, .e-toggleaxisImg, .e-autoExecuteImg, .e-nextPage, .e-prevPage, .e-firstPage, .e-lastPage, .e-searchEditorTree, .e-calcMemberImg, .e-excelExportImg, .e-wordExportImg, .e-pdfExportImg")).off(t.eventType.click);n(this.element.find(".e-dialogCancelBtn, .e-dialogOKBtn, .e-newReportImg, .e-addReportImg, .e-removeReportImg, .e-renameReportImg, .e-pvtBtn, .e-removeSplitBtn, .e-unCheckAll, .e-checkAll, .e-removeMeasure, .e-toggleCollapseButton, .e-toggleExpandButton, .e-reportDBImg, .e-saveAsReportImg, .e-saveReportImg, .e-loadReportImg, .e-removeDBReportImg, .e-renameDBReportImg, .e-mdxImg,.maximizedView,.e-colSortFilterImg, .e-rowSortFilterImg, .e-chartTypesImg, .e-toggleaxisImg,.e-autoExecuteImg, .e-nextPage, .e-prevPage, .e-firstPage, .e-lastPage, .e-searchEditorTree,.e-calcMemberImg,  .e-excelExportImg, .e-wordExportImg, .e-pdfExportImg")).off("click");n(document).find(".e-winCloseBtn").off(t.eventType.click);n(this._off(this.element,"mouseup",".e-parentsplit"));this._off(this.element,"click","#preventDiv");n(document).find(".e-winCloseBtn").off("click");this._off(n(document),"click",this._removeDialog);this._off(n(document),"keydown",this._keyPressDown);this._off(n(document),"keyup",this._keyPressUp);this._off(this.element.find("a.e-linkPanel"),"click",t.Pivot._editorLinkPanelClick);this._off(this.element,"click",".e-memberAscendingIcon, .e-memberDescendingIcon",t.proxy(t.Pivot._memberSortBtnClick,this));this.model.isResponsive&&n(window).off("resize",n.proxy(this._reSizeHandler,this))},_calculateHeight:function(){var i,u,r;n(this.element).height(n(this.element).height()<450?450:n(this.element).height());this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?(i=this.element.height()-((this.element.find("div.e-titleText").length>0?50:0)+this.element.find("#"+this._id+"_reportToolbar").height()+5),this.element.find(".oClientTbl").css("height",i+"px"),this.element.find(".e-outerTable").css("height",i+"px"),this.model.enableVirtualScrolling?(this.element.find(".e-controlPanel").css("height",i-25+"px"),this._pivotGrid._applyVScrolling()):this.model.enablePaging?this.model.displaySettings.controlPlacement==t.PivotClient.ControlPlacement.Tab?(this.element.find(".e-controlPanel").height(i-this.element.find("#"+this._id+"_Pager").height()),this.model.isResponsive&&this.element.find("#"+this._id+"_Pager").css({"margin-top":"-1px"})):this.element.find(".e-controlPanel").height(i-this.element.find("#"+this._id+"_Pager").height()-5):this.element.find(".e-controlPanel").css("height",i+"px"),this.element.find(".e-cubeTable").css("height",this.element.height()-((this.element.find("div.e-titleText").length>0?50:0)+this.element.find("#"+this._id+"_reportToolbar").height()+this.element.find(".e-csHeader").height()+38)+"px"),cdbHeight=this.element.height()-((this.element.find("div.e-titleText").length>0?50:0)+this.element.find("#"+this._id+"_reportToolbar").height()+this.element.find(".e-csHeader").height()+38),this.element.find(".e-cubeBrowser").css("height",cdbHeight+7-this.element.find(".e-cdbHeader").height()),this.element.find(".e-cubeTreeView").height(this.element.find(".e-cubeBrowser").height()-this.element.find(".e-cubeName").height()-(7+(this.model.enableMeasureGroups?this.element.find(".measureGroupselector").height()+13:0))),aebHeight=cdbHeight-(this.element.find(".e-axisHeader").height()*3+(this.model.enableSplitter?8:5)),this.element.find(".e-categoricalAxis, .e-rowAxis, .e-slicerAxis").css("height",aebHeight/3+"px"),this.model.enableSplitter&&this.element.find(".e-serversplitresponsive").height(this.element.height()-((this.element.find("div.e-titleText").length>0?50:0)+this.element.find("#"+this._id+"_reportToolbar").height()+this.element.find(".e-csHeader").height()+30))):(i=this.element.height()-((this.element.find("div.e-titleText").length>0?50:0)+this.element.find("#"+this._id+"_reportToolbar").height()+5),this.element.find(".e-outerTable, .controlPanelTD").css("height",i+"px"),this.model.enableVirtualScrolling?(this.element.find(".e-controlPanel").height(i-15),this.model.displaySettings.mode!=t.PivotClient.DisplayMode.ChartOnly&&this._pivotGrid._applyVScrolling()):this.model.enablePaging?this.model.displaySettings.controlPlacement==t.PivotClient.ControlPlacement.Tab?this.element.find(".e-controlPanel").height(i-this.element.find("#"+this._id+"_Pager").height()):this.element.find(".e-controlPanel").height(i-this.element.find("#"+this._id+"_Pager").height()-5):this.element.find(".e-controlPanel").height(i),this.element.find("#"+this._id+"_PivotSchemaDesigner").height(i),this._pivotSchemaDesigner._reSizeHandler(),this.enableTogglePanel()&&this.element.find(".pivotFieldList").length>0&&(this.element.find("div.e-togglePanel").remove(),this.model.enableRTL&&this.element.find(".e-pivotschemadesigner").css({"margin-left":"0px","margin-right":"0px"}),this.element.find(".e-controlPanel").width(this.element.width()-(this.element.width()/3+(this.model.enableVirtualScrolling?39:20))),n(t.buildTag("div.e-togglePanel",t.buildTag("div.e-toggleExpandButton e-icon","",{}).attr("aria-label","toggle expanded")[0].outerHTML+t.buildTag("div.e-toggleCollapseButton e-icon",{},{display:"none"}).attr("aria-label","toggle collapsed")[0].outerHTML)[0].outerHTML).appendTo(this.element.find(".pivotFieldList").parent()),this.element.find(".e-togglePanel").height(this.element.height()-((this.element.find("div.e-titleText").length>0?50:0)+this.element.find("#"+this._id+"_reportToolbar").height()+5)+"px").width(14),this.element.find(".e-toggleExpandButton,.e-toggleCollapseButton").css("margin-top",(this.element.find("#"+this._id+"_PivotSchemaDesigner").parents("td:eq(0)").height()-9)/2),this.element.find(".e-togglePanel").children().addClass("e-toggleButtons"),this.model.enableRTL&&this.element.find(".e-pivotschemadesigner").css({"margin-left":"2px","margin-right":"4px"}),this._unWireEvents(),this._wireEvents()));this.displayMode()!=t.PivotClient.DisplayMode.ChartOnly&&(this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()==t.PivotClient.DisplayMode.ChartAndGrid?(this.model.enableVirtualScrolling&&this.element.find(".e-controlPanel").height(this.element.find(".e-controlPanel").height()-5),this.defaultView()==t.PivotClient.DefaultView.Grid?this.element.find(".e-gridContainer").height(this.element.find(".e-controlPanel").height()/2-10):this.element.find(".e-gridContainer").height(this.element.find(".e-controlPanel").height()/2-10)):this.displayMode()==t.PivotClient.DisplayMode.GridOnly?this.model.enablePaging?this.element.find(".e-gridContainer").height(this.element.find(".e-controlPanel").height()-10):this.element.find(".e-gridContainer").height(this.element.find(".e-controlPanel").height()-20):this.model.enableVirtualScrolling?this.element.find(".e-gridContainer").height(this.element.find(".e-controlPanel").height()-(this.element.find("ul.clientTab").height()+30)):this.model.isResponsive?this.element.find(".e-gridContainer").height(this.element.find(".e-controlPanel").height()-(this.element.find("ul.clientTab").height()+19)):this.element.find(".e-gridContainer").height(this.element.find(".e-controlPanel").height()-(this.element.find("ul.clientTab").height()+15)));this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()==t.PivotClient.DisplayMode.ChartAndGrid?this.defaultView()==t.PivotClient.DefaultView.Grid?this.model.isResponsive?this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this.element.find(".e-chartContainer").height(this.element.find(".e-controlPanel").height()/2-10):this.element.find(".e-chartContainer").height(this.element.find(".e-controlPanel").height()/2-15):this.element.find(".e-chartContainer").height(this.element.find(".e-controlPanel").height()/2-10):this.element.find(".e-chartContainer").height(this.element.find(".e-controlPanel").height()/2-15):this.displayMode()==t.PivotClient.DisplayMode.ChartOnly?this.model.isResponsive?this.element.find(".e-chartContainer").height(this.element.find(".e-controlPanel").height()-25):this.element.find(".e-chartContainer").height(this.element.find(".e-controlPanel").height()-17):(u=this.element.find(".e-controlPanel").height()-(this.element.find("ul.clientTab").height()+20),this.model.enableVirtualScrolling?this.element.find(".e-chartContainer").height(u-5):this.model.isResponsive?this.element.find(".e-chartContainer").height(u-4):this.element.find(".e-chartContainer").height(u-2)));r=this.element.find(".e-chartContainer").height();this._chartHeight=this.model.enableToolBar?r-68+"px":this.model.isResponsive&&this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()==t.PivotClient.DisplayMode.ChartAndGrid||this.displayMode()==t.PivotClient.DisplayMode.ChartOnly?this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?r-4+"px":r-5+"px":r-3+"px";this._gridHeight=this.model.enableToolBar?this.element.find(".e-gridContainer").height()-46+"px":this.element.find(".e-gridContainer").height()-10+"px";this.model.enableToolBar&&(this.element.find("#"+this._id+"_PivotGridToolbar").height(this._gridHeight),this.element.find("#"+this._id+"_PivotCharttoolBar").height(this._chartHeight))},_performToggleAction:function(n){this.element.find(".e-controlPanel").width(n[0].controlPanelWidth);(this.controlPlacement()!="horizontal"||this.controlPlacement()=="horizontal"&&this.displayMode()=="chartOnly"||this.controlPlacement()=="horizontal"&&this.displayMode()=="gridOnly")&&(this.element.find(".e-pivotchart").width(n[0].chartOuterWidth),this.element.find(".e-pivotgrid").width(n[0].gridOuterWidth),this.element.find("#"+this._id+"_PivotChart").css({width:n[0].chartOuterWidth,overflow:""}),this.element.find("#"+this._id+"_PivotGrid").css({width:n[0].gridOuterWidth}),this.model.enableVirtualScrolling&&this._pivotGrid._applyVScrolling(),t.isNullOrUndefined(this._pivotChart)&&t.isNullOrUndefined(this.otreemapObj)||(this.element.find("#"+this._pivotChart._id).hasClass("e-pivotchart")?(this.element.find("#"+this._pivotChart._id).ejPivotChart("option","width",n[0].chartOuterWidth+"px"),this.chartObj=this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(this.chartObj)||(this.element.find("#"+this.chartObj._id).ejChart("option",{model:{size:{width:n[0].chartOuterWidth+"px"}}}),this.element.find("#"+this.chartObj._id+"_svg").width(n[0].chartOuterWidth),this.chartObj.redraw())):this.element.find("#"+this._pivotChart._id).hasClass("e-pivottreemap")&&this.model.enablePivotTreeMap&&!t.isNullOrUndefined(this.otreemapObj)&&this.otreemapObj._treeMap.refresh()),this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode&&this._toggleExpand&&!t.isNullOrUndefined(this.chartObj)&&(this.element.find("#"+this._id+"_PivotChartContainer").css("width",this.chartObj.model.size.width),this.chartObj.redraw()),this.model.enableToolBar&&(this.element.find("#"+this._id+"_PivotCharttoolBar").width(this.element.find(".e-chartContainer").width()),this.element.find("#"+this._id+"_PivotGridToolbar").width(this.element.find(".e-gridContainer").width()-10),this.element.find(".e-chartToolBar").width(this.element.find(".e-chartContainer").width()),this.element.find(".e-toolBar").width(this.element.find(".e-gridContainer").width())))},_rwdToggleExpand:function(){this.element.find(".e-csHeader, .e-cubeTable,.e-pivotschemadesigner,.e-toggleExpandButton").show();this.element.find(".e-toggleCollapseButton").hide();this.element.find(".e-toggleText").hide();this._calculateHeight();this.model.displaySettings.enableFullScreen&&this.element.find("#"+this._id+"_PivotGrid").css("width","");this._parentElwidth<850&&(this.element.find(".e-outerTable").css({position:"absolute",float:this.model.enableRTL?"right":"left",tableLayout:"auto","z-index":"10000"}),this.element.find(".pivotFieldList").width("91%"),this.element.find("table.e-cubeTable").css({width:"71%"}),this.element.find(".e-csHeader").css({width:"100%"}),this.element.find("#"+this._id+"_cubeSelector_wrapper").css({width:"100%"}),this.element.find(".controlPanelTD").css({width:this.element.find(".oClientTbl").width()-this.element.find("table.e-cubeTable").width(),float:this.model.enableRTL?"left":"right",left:this.model.enableRTL?"0px":"15px",right:this.model.enableRTL?"15px":"0px",position:"relative"}),this.element.find(".e-controlPanel").css({width:this.element.find(".controlPanelTD").width()-20}));this._parentElwidth>850&&(this.element.find(".controlPanelTD").css({width:"56%",left:"inherit",float:"left","margin-left":"5px"}),this.element.find("table.e-cubeTable").css({width:"98%"}),this.element.find(".e-outerTable,.controlPanelTD").css({position:"inherit"}),this._currentTab=="grid"||this._currentTab=="chart"?n(".e-controlPanel").width(n("#"+this._id).width()*55/100+"px"):n(".e-controlPanel").width("100%"),window.navigator.userAgent.indexOf("Trident")>0?(this.element.find(".e-outerTable").width(n("#"+this._id).width()*43/100),this.element.find(".e-csHeader").width(n("#"+this._id).width()*41/100+"px"),this.element.find("#"+this._id+"_cubeSelector_wrapper").width(n("#"+this._id).width()*29/100-60+"px"),this.model.enableSplitter||this.element.find(".cubeTableTD, .e-cubeBrowser, .e-cdbHeader").css({width:n(".e-cubeTable").width()/2-4+"px"}),this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()=="chartandgrid"&&this.element.find(".e-gridPanel").width(n("#"+this._id).width()*56/100+"px")):(this.element.find(".e-outerTable").css({width:n("#"+this._id).width()*(this.model.enableVirtualScrolling?41:42)/100+"px",tableLayout:"auto"}),this.element.find(".e-csHeader").css({width:n("#"+this._id).width()*42/100+"px"}),this.element.find("#"+this._id+"_cubeSelector_wrapper").css({width:n("#"+this._id).width()*32/100-60+"px"}),this.model.enableSplitter||this.element.find(".cubeTableTD, .e-cubeBrowser, .e-cdbHeader").css({width:n(".e-cubeTable").width()/2-4+"px"}),this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()=="chartandgrid"&&this.element.find(".e-gridPanel").css({width:n("#"+this._id).width()*55/100+"px"})));this.model.isResponsive&&this.displayMode()=="gridonly"&&n(".e-controlPanel").width(n("#"+this._id).width()*55/100+15+"px");this.model.isResponsive&&this.displayMode()=="gridonly"&&this.element.find(".e-controlPanel").width(n("#"+this._id).width()*55/100+15+"px");this.model.enableVirtualScrolling&&this._pivotGrid._applyVScrolling();this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.enableVirtualScrolling&&this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()=="chartandgrid"&&(this.element.find(".e-gridContainer").height(this.element.find(".e-controlPanel").height()/2-5),this.element.find(".e-chartContainer").height(this.element.find(".e-controlPanel").height()/2-10).css("overflow",""));this.displayMode()!="gridonly"&&(this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()=="chartandgrid"?(this.element.find(".e-gridPanel").width(n(".e-controlPanel").width()+8),this.model.enableToolBar?n("#"+this._pivotChart._id).width(n(".e-controlPanel").width()-30):n("#"+this._pivotChart._id).width(n(".e-controlPanel").width()-7),this.model.isResponsive&&this._currentTab=="chart"&&this.element.find(".e-chartContainer").css({width:"98.8%"})):n("#"+this._pivotChart._id).width(n(".e-controlPanel").width()-2),this.chartObj=null,this.chartObj=this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),!t.isNullOrUndefined(this.chartObj)&&this.model.displaySettings.enableFullScreen&&(this.chartObj.model.size.width=this.element.find(".e-chartContainer").width()-7,this.chartObj.model.size.height=this.element.find(".e-chartContainer").height(),this.element.find("#"+this._pivotChart._id+"Container").width(this.chartObj.model.size.width)),t.isNullOrUndefined(this.chartObj)&&this.model.enablePivotTreeMap&&!t.isNullOrUndefined(this.otreemapObj)&&(this.chartObj=this.element.find("#"+this.otreemapObj._id+"TreeMapContainer").data("ejTreeMap")),t.isNullOrUndefined(this.chartObj)||(this.chartObj.sfType.split(".").pop().toLowerCase()=="treemap"?this.otreemapObj._treeMap.refresh():this.chartObj.redraw()));this._overflow();this.displayMode()!=t.PivotClient.DisplayMode.ChartOnly&&this._pivotGrid.element.length>0&&this._pivotGrid._applyFrozenHeaderWidth(this._pivotGrid._JSONRecords)},_overflow:function(){this.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(this.element.find(".e-ellipse").remove(),this.element.find(".e-cdbHeader")[0].scrollHeight>this.element.find(".e-cdbHeader")[0].offsetHeight?(this.element.find(".e-cdbHeader").attr("title",this.element.find(".e-cdbHeader").text()),this.element.find(".e-cdbHeader").after("<span class='e-ellipse' style='margin-top:-"+this.element.find(".e-cdbHeader").height()*78/100+"px'>....<\/span>")):this.element.find(".e-cdbHeader").removeAttr("title"))},_rwdToggleCollapse:function(){if(this._parentElwidth<850&&(this.element.find(".e-outerTable").css({position:"absolute"}),this.element.find(".controlPanelTD").css({left:this.model.enableRTL?"0px":"35px",right:this.model.enableRTL?"35px":"0px",position:"relative"})),this._currentTab=="grid"?n(".e-controlPanel").css({width:n("#"+this._id).width()*91/100+"px"}):this.displayMode()==t.PivotClient.DisplayMode.ChartOnly?this.element.find(".e-controlPanel").css({width:n("#"+this._id).width()*91/100+"px"}):this.element.find(".e-controlPanel").css({width:"100%"}),this.model.displaySettings.enableFullScreen&&this.element.find("#"+this._id+"_PivotGrid").css("width",""),this.displayMode()=="chartandgrid"&&(this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.element.find(".e-gridPanel").css({width:n("#"+this._id).width()*92.65/100+"px"}),n(".e-controlPanel").css({width:n("#"+this._id).width()*91/100+"px"})),this.element.find(".e-csHeader, .e-cubeTable, .e-pivotschemadesigner,.e-toggleExpandButton").hide(),this.element.find(".controlPanelTD").css({left:this.model.enableRTL?"0":"1%",right:this.model.enableRTL?"2%":"0px",position:"relative",width:"95%",tableLayout:"auto",float:""}),this.element.find(".e-gridContainer").length>0&&this.element.find(".e-gridContainer").css({width:this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this._currentTab=="chart"?"100%":"99.8%"}),this.displayMode()=="gridonly"&&this.model.isResponsive&&(this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?this.element.find(".e-controlPanel").css({width:n("#"+this._id).width()*91/100+76+"px"}):this.element.find(".e-controlPanel").css({width:n("#"+this._id).width()*91/100+76+"px"})),this.displayMode()=="chartonly"&&this.model.isResponsive&&(this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?this.element.find(".e-controlPanel").css({width:n("#"+this._id).width()*91/100+87+"px"}):this.element.find(".e-controlPanel").css({width:n("#"+this._id).width()*91/100+86+"px"})),n("#"+this._id).width("100%"),this.element.find(".e-outerTable").css({position:"absolute",width:"0px",tableLayout:"fixed"}),this.element.find(".e-toggleCollapseButton").show(),this.element.find(".e-toggleText").show(),this.model.enableVirtualScrolling&&this._pivotGrid._applyVScrolling(),this.displayMode()!="gridonly"){if(this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()=="chartandgrid"){if(this.model.enableToolBar)n("#"+this._pivotChart._id).width(n(".e-controlPanel").width()-45);else{var i=this.element.find(".e-controlPanel").width()-(this._currentTab=="grid"?0:17);n("#"+this._pivotChart._id).width(i);n("#"+this._id+"_PivotChartContainer").width("99.6%")}this.element.find(".e-chartContainer").css({width:this._currentTab=="grid"?"99.2%":"100%"})}else n("#"+this._pivotChart._id).width(n(".e-controlPanel").width()-26),this.displayMode()==t.PivotClient.DisplayMode.ChartOnly&&this.element.find(".e-chartContainer").css({width:"100%"});this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this.chartObj=null,this.chartObj=this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),!t.isNullOrUndefined(this.chartObj)&&this.model.displaySettings.enableFullScreen&&(this.chartObj.model.size.width=this.element.find(".e-chartContainer").width()+"px"!="0px"?this.element.find(".e-chartContainer").width()+"px":this.element.find(".e-pivotchart").width()+"px",this.chartObj.model.size.height=this.element.find(".e-chartContainer").height(),this.element.find("#"+this._pivotChart._id+"Container").width(this.chartObj.model.size.width)),t.isNullOrUndefined(this.chartObj)&&this.model.enablePivotTreeMap&&!t.isNullOrUndefined(this.otreemapObj)&&(this.chartObj=this.element.find("#"+this.otreemapObj._id+"TreeMapContainer").data("ejTreeMap")));t.isNullOrUndefined(this.chartObj)||(this.chartObj.sfType.split(".").pop().toLowerCase()=="treemap"?this.otreemapObj._treeMap.refresh():this.chartObj.redraw())}this.displayMode()!=t.PivotClient.DisplayMode.ChartOnly&&this._pivotGrid.element.length>0&&this._pivotGrid._applyFrozenHeaderWidth(this._pivotGrid._JSONRecords)},_removeFilterTag:function(i){var u=[],f,r;if(i&&i.indexOf("].")>=0){if(f=i.split("].").length>2?"levelUniqueName":"hierarchyUniqueName",this._excelFilterInfo.length>0){var e=this.element.find(".cubeSelector").data("ejDropDownList").model.value,r=this.element.find("#"+this._id+"_reportList").data("ejDropDownList").model.value,o=this._selectedLevelUniqueName,s=this._selectedFieldName;this._excelFilterInfo=n.grep(this._excelFilterInfo,function(n){if(n.cubeName!=e||n.report!=r||n[f]==i)u=n.sortOrder;else return n})}return u}i&&this._excelFilterInfo.length>0&&this.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&(r=this.element.find("#"+this._id+"_reportList").data("ejDropDownList").model.value,this._excelFilterInfo=n.grep(this._excelFilterInfo,function(n){if(n.report==r&&!(n.levelUniqueName==i))return n}))},_groupLabelChange:function(i){var r,o,e,u,f;if(this._selectedLevelUniqueName=i.selectedValue,r=[],o=[],this._excelFilterInfo.length>0){var s=this.element.find(".cubeSelector").data("ejDropDownList").model.value,h=this.element.find("#"+this._id+"_reportList").data("ejDropDownList").model.value,c=this._selectedLevelUniqueName,l=this._selectedFieldName;r=n.map(this._excelFilterInfo,function(n){if(n.cubeName==s&&n.report==h&&n.hierarchyUniqueName==l&&n.levelUniqueName==c)return{action:n.action,operator:n.operator,value1:n.value1,sortInfo:n.sortOrder}})}e=this.element.find("#"+this._id+"_clearAllFilters a").children().clone();this.element.find("#"+this._id+"_clearAllFilters a").text(this._getLocalizedLabels("ClearFilter")+' from "'+this.element.find(".groupLabelDrop").data("ejDropDownList").model.text+'"').append(e);this.element.find(".e-filterElementTag .e-activeFilter,.e-filterIndicator").remove();r.length>0&&(t.isNullOrUndefined(r[0].sortInfo)||!t.isNullOrUndefined(r[0].sortInfo)&&r[0].sortInfo.length==0)||r.length==0?this.element.find(".e-clrSort").parents("li:eq(0)").css("opacity","0.5").attr("disable",!0):r[0].sortInfo.length>0&&this.element.find("#"+this._id+r[0].sortInfo+" span:eq(0)").addClass("e-selectedSort");this.element.find(".e-clrFilter").parents("li:eq(0)").css("opacity","0.5").attr("disable",!0);this.element.find("#"+this._id+"_labelClearFilter").css("opacity","0.5").attr("disable",!0);this.element.find("#"+this._id+"_valueClearFilter").css("opacity","0.5").attr("disable",!0);r.length>0&&!t.isNullOrUndefined(r[0].operator)?(u="",f="",r[0].action=="valuefiltering"?(u="valueFilterBtn",f=f=r[0].operator=="equals"||r[0].operator=="not equals"||r[0].operator=="less than or equal to"||r[0].operator=="greater than or equal to"||r[0].operator=="greater than"||r[0].operator=="less than"?"_valueFilter":""):(u="labelFilterBtn",f=r[0].operator=="equals"||r[0].operator=="not equals"||r[0].operator=="less than or equal to"||r[0].operator=="greater than or equal to"||r[0].operator=="greater than"||r[0].operator=="less than"?"_labelFilter":""),this.element.find("#"+u+" a:eq(0)").append("<span class='e-filterState e-icon'/>"),this.element.find(".e-clrFilter").parents("li:eq(0)").removeAttr("style disable"),this.element.find("#"+u+"#"+this._id+"_labelClearFilter").removeAttr("style disable"),this.element.find("#"+u+"#"+this._id+"_valueClearFilter").removeAttr("style disable"),r[0].operator.replace(/ /g,"")=="BottomCount"?this.element.find("#"+u+" li#"+r[0].operator.replace(/ /g,"").replace("Bottom","top")+" a").append(n(t.buildTag("span.e-activeFilter e-icon")[0].outerHTML)):this.element.find("#"+u+" li#"+r[0].operator.replace(/ /g,"")+f+" a").append(n(t.buildTag("span.e-activeFilter e-icon")[0].outerHTML))):this._getUnSelectedNodes()!=""&&this.element.find(".e-clientDialog").prepend("<div class='e-filterIndicator e-icon' style='margin-top:120px' />")},_filterElementClick:function(i){var h,r,u,c;if(n(i.element).hasClass("clearFilter")&&n(i.element).css("opacity")=="0.5")return i.Cancel;this._selectedLevelUniqueName=this.element.find(".groupLabelDrop").data("ejDropDownList").model.value||this.element.find(".groupLabelDrop").data("ejDropDownList").model.dataSource[0].value;var f=this.element.find(".cubeSelector").length>0?this.element.find(".cubeSelector").data("ejDropDownList").model.value:"",e=this.element.find("#"+this._id+"_reportList").data("ejDropDownList").model.value,o=this._selectedLevelUniqueName,s=this._selectedFieldName;if(!(n(i.element).attr("id")=="labelClearFilter"||n(i.element).attr("id")=="valueClearFilter")&&!n(i.element).parent(".e-filterElementTag").length>0)h=n(i.element).parents("li:eq(0)#"+this._id+"_valueFilterBtn").length>0?"valueFilterDlg":"labelFilterDlg",r=[],this._excelFilterInfo.length>0&&n(i.element).siblings("li:eq(0)").attr("disable")!="true"&&(r=n.map(this._excelFilterInfo,function(n){if(n.cubeName==f&&n.report==e&&n.hierarchyUniqueName==s&&n.levelUniqueName==o)return{value1:n.value1,value2:n.value2,operator:n.operator,measure:n.measure}}),n(i.element).attr("id").replace("_labelFilter","").replace("_valueFilter","")!=r[0].operator.replace(/ /g,"")&&r.length>0&&(r=[])),this._schemaData&&(this._schemaData._selectedLevelUniqueName=this._selectedLevelUniqueName,this._schemaData.selectedFieldName=this._selectedFieldName),t.Pivot.createAdvanceFilterTag({action:h,selectedArgs:i,filterInfo:r},this);else if(n(i.element).attr("id")=="descOrder"||n(i.element).attr("id")=="ascOrder"||n(i.element).find(".e-clrSort").length>0){if(u=!1,this._excelFilterInfo.length>0){var f=this.element.find(".cubeSelector").data("ejDropDownList").model.value,e=this.element.find("#"+this._id+"_reportList").data("ejDropDownList").model.value,o=this._selectedLevelUniqueName,s=this._selectedFieldName;this._excelFilterInfo=n.grep(this._excelFilterInfo,function(t){return t.cubeName==f&&t.report==e&&t.hierarchyUniqueName==s&&t.levelUniqueName==o&&(u=!0,n(i.element).find(".e-clrSort").length==0?t.sortOrder=n(i.element).attr("id"):delete t.sortOrder),t})}u||this._excelFilterInfo.push({cubeName:this.element.find(".cubeSelector").length>0?this.element.find(".cubeSelector").data("ejDropDownList").model.value:"",report:this.element.find("#"+this._id+"_reportList").data("ejDropDownList").model.value,hierarchyUniqueName:this._selectedFieldName,levelUniqueName:this._selectedLevelUniqueName,sortOrder:n(i.element).attr("id")});this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.filterElement,JSON.stringify({action:"labelSorting",clientParams:this._selectedFieldName+"--"+n(i.element).attr("id"),olapReport:this.currentReport,clientReports:this.reports,customObject:JSON.stringify(this.model.customObject)}),this._filterElementSuccess)}else c=this._removeFilterTag(this._selectedLevelUniqueName),this._excelFilterInfo.push({cubeName:this.element.find(".cubeSelector").length>0?this.element.find(".cubeSelector").data("ejDropDownList").model.value:"",report:this.element.find("#"+this._id+"_reportList").data("ejDropDownList").model.value,hierarchyUniqueName:this._selectedFieldName,levelUniqueName:this._selectedLevelUniqueName,sortOrder:c}),this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.filterElement,JSON.stringify({action:"labelfiltering",clientParams:this._selectedLevelUniqueName+"--Clear Filter",olapReport:this.currentReport,clientReports:this.reports,customObject:JSON.stringify(this.model.customObject)}),this._filterElementSuccess)},_filterOptionChanged:function(n){var i=this.element.find("#"+this._id+"_filterDialog_wrapper .e-titlebar").text().indexOf("Value")>=0?"valueFilterDlg":"labelFilterDlg";i=="valueFilterDlg"&&t.Pivot.createAdvanceFilterTag({action:i,selectedArgs:n,filterInfo:[]},this)},_filterElementOkBtnClick:function(){var i=this,s;t.isNullOrUndefined(this._waitingPopup)||(i._isTimeOut=!0,setTimeout(function(){i._isTimeOut&&i._waitingPopup.show()},800));var f=this.element.find("#"+this._id+"_filterOptions").data("ejDropDownList").model.value||this.element.find("#"+this._id+"_filterOptions")[0].value,e=this.element.find("#"+this._id+"_filterValue1")[0].value,o=this.element.find("#"+this._id+"_filterValue2").length>0?this.element.find("#"+this._id+"_filterValue2")[0].value:"",r,n,u="labelfiltering";n=this._selectedLevelUniqueName+"--"+f+"--"+e;this.element.find(".filterMeasures").length>0&&(this.element.find("#"+this._id+"_filterValue2").length>0&&(n=n+","+o),r=this.element.find(".filterMeasures").data("ejDropDownList").model.value,n=n+"--"+r,u="valuefiltering");s=this._removeFilterTag(this._selectedLevelUniqueName);this._excelFilterInfo.push({cubeName:this.element.find(".cubeSelector").length>0?this.element.find(".cubeSelector").data("ejDropDownList").model.value:"",report:this.element.find("#"+this._id+"_reportList").data("ejDropDownList").model.value,action:u,hierarchyUniqueName:this._selectedFieldName,levelUniqueName:this._selectedLevelUniqueName,operator:f,measure:r,value1:e,value2:o,sortOrder:s});this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.filterElement,JSON.stringify({action:u,clientParams:n,olapReport:this.currentReport,clientReports:this.reports,customObject:JSON.stringify(this.model.customObject)}),this._filterElementSuccess)},_splitButtonDropped:function(i,r){var o,s,h,c;this.isDropped=!0;var e=this,u=i=="Series"?"row":i.toLowerCase(),f=this.element.find("button.dragClone").attr("title");if(n.trim(r)==""&&this.element.find(".e-"+u+"Axis").find("button:last").attr("title")==f||n.trim(r)!=""&&this.element.find(n(".e-"+u+"Axis").find(".e-splitBtn")[r]).attr("data-tag").split(":")[1].replace("."," - ")==f||u=="slicer"&&n.trim(r)!=""&&this.element.find(".e-"+u+"Axis").find("button[title="+JSON.stringify(f).replace(/"/g,"'")+"]").length>0)return this.element.find("button.dragClone").remove(),!1;if(u=="slicer"&&this.draggedSplitBtn!=null&&(n(this.draggedSplitBtn).parent("div:eq(0)").attr("data-tag").indexOf("Measure")>-1||n(this.draggedSplitBtn).parent("div:eq(0)").attr("data-tag").indexOf("KPIs")>-1)&&(this.element.find(".e-splitBtn[tag*=Measures]").length>1||this.element.find(".e-splitBtn[data-tag*=KPIs]").length>=1))return o=(n(this.draggedSplitBtn).parent("div:eq(0)").attr("data-tag").indexOf("KPIs")>-1||n(this.draggedSplitBtn).parent("div:eq(0)").attr("data-tag").indexOf("Measure")>-1)&&this.element.find(".e-splitBtn[data-tag*=KPIs]").length>=1?this._getLocalizedLabels("KpiAlertMsg"):this._getLocalizedLabels("MultipleMeasure"),this.element.find("button.dragClone").remove(),t.Pivot._createErrorDialog(o,this._getLocalizedLabels("Warning"),this),!1;(n.trim(r)==""||this.draggedSplitBtn!=null&&this.element.find(n(".e-"+u+"Axis").find(".e-splitBtn")[r]).attr("data-tag").split(":")[1].replace("."," - ")!=f)&&(s=this.element.find(".e-cubeName").html()+"--"+n(this.draggedSplitBtn).parent("div:eq(0)").attr("data-tag")+"--"+i+"--"+r,this.model.enableAdvancedFilter&&i=="Slicer"&&(h=n(this.draggedSplitBtn).parent("div:eq(0)").attr("data-tag").split(":")[1].split("."),this._setUniqueNameFrmBtnTag(h),this._removeFilterTag(this._selectedFieldName)),this.element.find("button.dragClone").remove(),this.draggedSplitBtn=null,this._isTimeOut=!0,setTimeout(function(){e._isTimeOut&&e._waitingPopup.show()},800),this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:"nodeDropped",element:this.element,customObject:this.model.customObject}),c=JSON.stringify(this.model.customObject),this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.nodeDropped,JSON.stringify({action:"nodeDropped",dropType:"SplitButton",nodeInfo:s,olapReport:this.currentReport,clientReports:this.reports,customObject:c}),this._nodeDroppedSuccess));this._isNodeOrButtonDropped=!0},_filterElementSuccess:function(n){this._isSearchApplied=!1;t.Pivot.closePreventPanel(this);n[0]!=i?(this.currentReport=n[0].Value,this.reports=n[1].Value,n[2]!=null&&n[2]!=i&&(this.model.customObject=n[2].Value)):n.d!=i?(this.currentReport=n.d[0].Value,this.reports=n.d[1].Value,n.d[2]!=null&&n.d[2]!=i&&(this.model.customObject=n.d[2].Value)):(this.currentReport=n.UpdatedReport,this.reports=n.ClientReports,n.customObject!=null&&n.customObject!=i&&(this.model.customObject=n.customObject));this.model.afterServiceInvoke!=null&&this._trigger("afterServiceInvoke",{action:"filtering",element:this.element,customObject:this.model.customObject});this._renderControls();this._unWireEvents();this._wireEvents();this._successAction="Filter";this._trigger("renderSuccess",this)},_maxViewBtnClick:function(){var r=n(window).width()-150,u=n(window).height()-100,e;t.isNullOrUndefined(this._pivotChart)||(this._maxInitialChartHeight=this.element.find("#"+this._pivotChart._id+"Container").height(),this._maxInitialChartWidth=this.element.find("#"+this._pivotChart._id+"Container").width()-7);this._maxInitialGridWidth=this.element.find("#"+this._id+"_PivotGrid").width();var f=t.buildTag("div.e-fullScreenView","",{width:n(document).width(),height:n(document).height()}),i=t.buildTag("div#"+this._id+"_maxView.e-maximumView","",{width:r,height:u}),o=t.buildTag("div#"+this._id+"_Winclose.e-winCloseBtn e-icon","").attr("role","button").attr("aria-label","close").attr("clientID",this._id);this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this.chartObj=null,this.chartObj=this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(this.chartObj)&&this.model.enablePivotTreeMap&&!t.isNullOrUndefined(this.otreemapObj)&&(this.chartObj=this.element.find("#"+this.otreemapObj._id+"TreeMapContainer").data("ejTreeMap")));this.displayMode()==t.PivotClient.DisplayMode.ChartOnly&&(n("#"+this._pivotChart._id+"Container_svg")[0]||n("#"+this.otreemapObj._id+"TreeMapContainer").children().length>0)&&(this._fullScreenView(100,160),t.isNullOrUndefined(this.chartObj)||this.chartObj.sfType.split(".").pop().toLowerCase()=="treemap"&&(n("#"+this.otreemapObj._id).css({width:"99%"}),n("#"+this.otreemapObj._id+"TreeMapContainer").css({width:"100%"})),i.append(this.element.find("#"+this._id+"_PivotChart")));this.displayMode()==t.PivotClient.DisplayMode.GridOnly&&i.append(this.element.find("#"+this._id+"_PivotGrid").css({"max-height":u,width:r,overflow:"auto"}));this.controlPlacement()==t.PivotClient.ControlPlacement.Tab&&this.displayMode()=="chartandgrid"?this._currentTab=="grid"?i.append(this.element.find("#"+this._id+"_PivotGrid").css({"max-height":u,width:r,overflow:"auto"})):this._currentTab=="chart"&&(n("#"+this._pivotChart._id+"Container_svg")[0]||n("#"+this.otreemapObj._id+"TreeMapContainer").children().length>0)&&(this._fullScreenView(100,160),t.isNullOrUndefined(this.chartObj)||this.chartObj.sfType.split(".").pop().toLowerCase()=="treemap"&&(n("#"+this.otreemapObj._id).css({width:"99%"}),n("#"+this.otreemapObj._id+"TreeMapContainer").css({width:"100%"})),i.append(this.element.find("#"+this._id+"_PivotChart"))):this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()=="chartandgrid"&&(e=n(window).height()/2-50,this.element.find("#"+this._id+"_PivotGrid").css({"max-height":e,"margin-left":"0px",overflow:"auto"}),t.isNullOrUndefined(this.chartObj)||this.chartObj.sfType.split(".").pop().toLowerCase()=="treemap"&&(n("#"+this.otreemapObj._id).css({width:"99%"}),n("#"+this.otreemapObj._id+"TreeMapContainer").css({width:"100%"})),this._fullScreenView(50,160),(n("#"+this._pivotChart._id+"Container_svg")[0]||n("#"+this.otreemapObj._id+"TreeMapContainer").children().length>0)&&(this.defaultView()=="chart"?i.append(this.element.find("#"+this._id+"_PivotChart")).append(this.element.find("#"+this._id+"_PivotGrid").width(n(window).width()-160)):i.append(this.element.find("#"+this._id+"_PivotGrid").width(n(window).width()-160)).append(this.element.find("#"+this._id+"_PivotChart"))),i.append(this.element.find("#"+this._id+"_PivotGrid").width(n(window).width()-160)));i.append(o);f.append(i);n("body").append(f);this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(t.isNullOrUndefined(this.chartObj)||(this.model.enablePivotTreeMap&&this.chartObj.sfType.split(".").pop().toLowerCase()=="treemap"?this.otreemapObj._treeMap.refresh():this.chartObj.redraw()))},_maxViewClsBtnClick:function(){var i=n(".e-fullScreenView"),r=this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?0:80;i.find("#"+this._id+"_PivotGrid").css("margin-left","7px");i.find("#"+this._id+"_PivotChart").appendTo(this.element.find(".e-chartContainer"));this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this.chartObj=null,this.chartObj=this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(this.chartObj)&&this.model.enablePivotTreeMap&&!t.isNullOrUndefined(this.otreemapObj)&&(this.chartObj=this.element.find("#"+this.otreemapObj._id+"TreeMapContainer").data("ejTreeMap")));this.displayMode()==t.PivotClient.DisplayMode.ChartOnly&&n("#"+this._pivotChart._id+"Container_svg")[0]&&this._fullScreenViewCls(this._maxInitialChartHeight,this._maxInitialChartWidth+(this.model.isResponsive?7:0));this.displayMode()==t.PivotClient.DisplayMode.GridOnly&&i.find("#"+this._id+"_PivotGrid").css({width:"inherit","max-height":"",overflow:"initial"});this.controlPlacement()==t.PivotClient.ControlPlacement.Tab&&this.displayMode()=="chartandgrid"&&(this._currentTab=="chart"&&n("#"+this._pivotChart._id+"Container_svg")[0]?this._fullScreenViewCls(this._maxInitialChartHeight,this._maxInitialChartWidth-(this.model.isResponsive?7:0)):i.find("#"+this._id+"_PivotGrid").css({"max-height":"",width:this._maxInitialGridWidth+"px"}));this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()=="chartandgrid"&&(n("#"+this._pivotChart._id+"Container_svg")[0]?(this._fullScreenViewCls(this._maxInitialChartHeight,this._maxInitialChartWidth-(this.model.isResponsive?7:0)),i.find("#"+this._id+"_PivotGrid").css({width:this._maxInitialGridWidth+"px","max-height":"",overflow:"auto"})):i.find("#"+this._id+"_PivotGrid").css({"max-height":"",width:this._maxInitialGridWidth+"px"}));this.enableTogglePanel()&&(this.element.find(".e-cubeTable").is(":visible")==!1||this.element.find(".e-outerTable").is(":visible")==!1)&&i.find("#"+this._id+"_PivotGrid").css("width",this._maxInitialGridWidth+"px");i.find("#"+this._id+"_PivotGrid").appendTo(this.element.find(".e-gridContainer"));n(".e-maximumView").remove();n(".e-fullScreenView").remove();this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(t.isNullOrUndefined(this.chartObj)||(this.model.enablePivotTreeMap&&this.chartObj.sfType.split(".").pop().toLowerCase()=="treemap"?this.otreemapObj._treeMap.refresh():this.chartObj.redraw()))},_enableResponsive:function(){this.element.find(".outerPanel").css({width:"100%"});n(".e-pivotclient").css({width:"100%"});this.element.find(".e-outerTable").css({float:this.model.enableRTL?"right":"left"});this.model.enableSplitter&&this.element.find(".e-cdbHeader, .e-cubeBrowser").css("width","99%");this.element.find(".e-rowAxis, .e-slicerAxis, .e-categoricalAxis").width("99%");this.element.find(".controlPanelTD").width("56%");this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()=="chartandgrid"&&this.element.find(".controlPanelTD").css("display","inline-block");this.model.operationalMode==t.Pivot.OperationalMode.ClientMode?(this.element.find(".e-cubeTable").width("100%"),this.displayMode()==t.PivotClient.DisplayMode.ChartOnly?this.element.find(".e-pivotschemadesigner").width("95%"):this.element.find(".e-pivotschemadesigner").width("100%")):this.element.find(".e-cubeTable").width("98%");window.navigator.userAgent.indexOf("Trident")>0?(this._currentTab=="grid"?n(".e-controlPanel").css({width:n("#"+this._id).width()*55/100+"px"}):n(".e-controlPanel").css({width:"99%"}),this.element.find(".e-outerTable").width(n("#"+this._id).width()*39/100),this.element.find(".e-csHeader").width(n("#"+this._id).width()*38/100),this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()=="chartandgrid"&&this.element.find(".e-gridPanel").width(n("#"+this._id).width()*55/100)):(this.element.find(".e-outerTable").width(n("#"+this._id).width()*38/100),this.element.find(".e-csHeader").width(n("#"+this._id).width()*38/100))},_fullScreenView:function(r,u){var f,e;this._pivotChart!=null&&this._pivotChart!=i&&n("#"+this._pivotChart._id+"Container_svg")[0]&&(this.chartObj=this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),f=this.chartObj.model.size.width=this._pivotChart.model.size.width=n(window).width()-u+"px",e=this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()=="chartandgrid"?this._pivotChart.model.size.height=this.chartObj.model.size.height=n(window).height()/2-r+"px":this._pivotChart.model.size.height=this.chartObj.model.size.height=n(window).height()-r+"px",this.element.find("#"+this._id+"_PivotChart").css({"min-height":e,width:f}),this.element.find("#"+this._id+"_PivotChartContainer").css({width:f,height:e}),this.chartObj.redraw())},_fullScreenViewCls:function(n,r){var u,f;this._pivotChart!=null&&this._pivotChart!=i&&(this.chartObj=this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),u=this.chartObj.model.size.height=this._pivotChart.model.size.height=n+"px",f=this.enableTogglePanel()&&this.element.find(".e-cubeTable").is(":visible")==!1&&this.element.find(".e-outerTable").is(":visible")==!1?this.chartObj.model.size.width=this._pivotChart.model.size.width="950px":this.chartObj.model.size.width=this._pivotChart.model.size.width=r+"px",this.element.find("#"+this._id+"_PivotChart").css({"min-height":u,width:f}),this.element.find("#"+this._pivotChart._id+"Container").width(f),this.displayMode()==t.PivotClient.DisplayMode.ChartOnly&&this.element.find("#"+this._pivotChart._id+"Container").height(u),this.chartObj.redraw())},_calcMemberDroppedSuccess:function(n){var r,t;if(n[0]!=i){for(t=0;t<n.length;t++)if(r=n[t].Key,r=="calcMemberTreeData"){this._calcMembers=JSON.parse(n[t].Value);n.splice(t,1);break}}else if(n.d!=i){for(t=0;t<n.d.length;t++)if(r=n.d[t].Key,r=="calcMemberTreeData"){this._calcMembers=JSON.parse(n.d[t].Value);n.d.splice(t,1);break}}else this._calcMembers=JSON.parse(n.calcMemberTreeData),delete n.calcMemberTreeData;this._calcMemberTreeViewUpdate();this._nodeDroppedSuccess(n)},_nodeDroppedSuccess:function(r){var o,s,h,e,l,c,u,f;if(r[0]!=i?(o=r[0].Value,s=r[1].Value,h=r[2].Value,this.currentReport=r[3].Value,this.reports=r[4].Value,r[5]!=null&&r[5]!=i&&(this.model.customObject=r[5].Value)):r.d!=i?(o=r.d[0].Value,s=r.d[1].Value,h=r.d[2].Value,this.currentReport=r.d[3].Value,this.reports=r.d[4].Value,r.d[5]!=null&&r.d[5]!=i&&(this.model.customObject=r.d[5].Value)):(o=r.Columns,s=r.Rows,h=r.Slicers,this.currentReport=r.UpdatedReport,this.reports=r.ClientReports,r.customObject!=null&&r.customObject!=i&&(this.model.customObject=r.customObject)),this._isRemoved&&(this._isRemoved=!1,e=[],o.length>0&&e.push(o),s.length>0&&e.push(s),h.length>0&&e.push(h),e.length>0))for(l=0;l<e.length;l++)if(u=e[l],!t.isNullOrUndefined(u))for(f=0;f<u.split("#").length;f++)c=u.split("#")[f]==""||u.split("#")[f]=="Measures"?null:u.split("#")[f].split(".").length>0?u.split("#")[f].replace("."," - "):u.split("#")[f],t.isNullOrUndefined(c)||this._currentReportItems.length!=0&&this._treeViewData.hasOwnProperty(c)&&(delete this._treeViewData[c],this._currentReportItems.splice(n.inArray(c,this._currentReportItems),1));this.model.afterServiceInvoke!=null&&this._trigger("afterServiceInvoke",{action:"nodeDropped",element:this.element,customObject:this.model.customObject});this.element.find(".e-categoricalAxis, .e-rowAxis, .e-slicerAxis").find(".e-splitBtn, .e-btn").remove();n(this._createSplitButtons(o,"Columns")).appendTo(".e-categoricalAxis");n(this._createSplitButtons(s,"Rows")).appendTo(".e-rowAxis");n(this._createSplitButtons(h,"Slicers")).appendTo(".e-slicerAxis");this.element.find(".e-categoricalAxis, .e-rowAxis, .e-slicerAxis").find("button").ejButton({height:"20px",type:t.ButtonType.Button});this._setSplitBtnTitle();this.model.showUniqueNameOnPivotButton&&(n(".e-pvtBtn").addClass("e-splitBtnUnique"),this._addSplitButtonHeight());this._renderControls();this._unWireEvents();this._wireEvents();this._successAction="NodeDrop";this._trigger("renderSuccess",this);this._buttonContextMenu()},_buttonContextMenu:function(){var i=t.buildTag("ul.pivotTree#"+this._id+"_pivotTree",t.buildTag("li",t.buildTag("a",this._getLocalizedLabels("AddToColumn"))[0].outerHTML)[0].outerHTML+t.buildTag("li",t.buildTag("a",this._getLocalizedLabels("AddToRow"))[0].outerHTML)[0].outerHTML+t.buildTag("li",t.buildTag("a",this._getLocalizedLabels("AddToSlicer"))[0].outerHTML)[0].outerHTML)[0].outerHTML;this.element.append(i);n("#"+this._id+"_pivotTree").ejMenu({menuType:t.MenuType.ContextMenu,openOnClick:!1,contextMenuTarget:this.element.find(".e-button"),click:t.proxy(this._contextClick,this),beforeOpen:t.proxy(this._contextOpen,this),close:t.proxy(t.Pivot.closePreventPanel,this)})},_editorTreeInfoSuccess:function(n){n[0]!=i?n[2]!=null&&n[2]!=i&&(this.model.customObject=n[2].Value):n.d!=i?n.d[2]!=null&&n.d[2]!=i&&(this.model.customObject=n.d[2].Value):n.customObject!=null&&n.customObject!=i&&(this.model.customObject=n.customObject);this.model.afterServiceInvoke!=null&&this._trigger("afterServiceInvoke",{action:"fetchMemberTreeNodes",element:this.element,customObject:this.model.customObject});this._createDialog(this._args_className,this._args_innerHTML,n);this._isTimeOut=!1;this._waitingPopup.hide();this._successAction="EditorTreeInfo";this._trigger("renderSuccess",this);this.element.find(".e-dialog .e-text:visible").first().length>0?this._curFocus.editor=this.element.find(".e-dialog .e-text:visible").first().attr("tabindex","-1").focus().addClass("e-hoverCell"):this.element.find(".e-dialog .e-measureEditor:visible").first().length>0&&(this._curFocus.editor=this.element.find(".e-dialog .e-measureEditor:visible").first().attr("tabindex","-1").focus().addClass("e-hoverCell"))},_generateAllMember:function(t,i){this.olapCtrlObj._allMember=n(n(i).find("Axis:eq(0) Tuple:eq(0)").children().children()[1]).text()},generateJSON:function(i){this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this._pivotChart.model.analysisMode=this.model.analysisMode,this._pivotChart.model.operationalMode=this.model.operationalMode,this._pivotChart.model.dataSource=this.model.dataSource,this._pivotChart.setPivotEngine(i.tranposeEngine),this._pivotChart._drillAction="",this.displayMode()!=t.PivotClient.DisplayMode.ChartOnly&&this._pivotGrid._drillAction!=""?(this._setChartDrillMembers(i.tranposeEngine,this._pivotGrid._drillAction),this._drillInfo=[]):this._pivotChart.generateJSON(this,i.tranposeEngine),this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(this._pivotChart._drilledCellSet=this._drilledCellSet.length>0?this._drilledCellSet:[],this._pivotChart.XMLACellSet=this.XMLACellSet.length>0?this.XMLACellSet:[]),this._pivotChart._drillAction="");this.displayMode()!=t.PivotClient.DisplayMode.ChartOnly&&(this._pivotGrid.model.analysisMode=this.model.analysisMode,this._pivotGrid.model.operationalMode=this.model.operationalMode,this._pivotGrid.model.dataSource=this.model.dataSource,this._pivotGrid.setJSONRecords(JSON.stringify(i.jsonObj)),this._pivotGrid.model.collapsedMembers=null,this.model.gridLayout=="excellikelayout"?this._pivotGrid.excelLikeLayout(i.jsonObj):this._pivotGrid.renderControlFromJSON(),this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(this._pivotGrid._drilledCellSet=this._drilledCellSet.length>0?this._drilledCellSet:[],this._pivotGrid.XMLACellSet=this.XMLACellSet.length>0?this.XMLACellSet:[]),this._pivotGrid._drillAction="");this._pivotSchemaDesigner&&this._pivotSchemaDesigner._refreshPivotButtons();this.model.showUniqueNameOnPivotButton&&(n(".e-pivotButton").addClass("e-pvtBtnUnique"),n(".e-pvtBtn").addClass("e-schemaBtnUnique"),n(".e-removeClientPivotBtn").addClass("e-schemaRemoveBtnUnqiue"));this.model.isResponsive&&this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this.chartObj=this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(this.chartObj)||this.element.find("#"+this.chartObj._id).ejChart("option",{model:{size:{height:this._chartHeight}}}))},_renderLayout:function(){var r,i,u,f;(this.model.isResponsive||this.model.collapseCubeBrowserByDefault)&&(this.model.displaySettings.enableTogglePanel=!0);r=t.buildTag("div.e-reportToolbar#"+this._id+"_reportToolbar",this._reportToolbar(),{width:"410px",height:"29px"})[0].outerHTML;this.model.enableSplitter?this.model.isResponsive?this.model.isResponsive&&(i=t.buildTag("div#"+this._id+"_PivotSchemaDesigner.pivotFieldList","",{}).addClass("e-splitresponsive")[0].outerHTML,u=t.buildTag("div.outerPanel","").append(t.buildTag("div.e-titleText",t.buildTag("span",this.title(),{"padding-left":"10px"})[0].outerHTML)[0].outerHTML+r+t.buildTag("table.oClientTbl",t.buildTag("tr",t.buildTag("td",t.buildTag("table.e-outerTable",t.buildTag("tr",t.buildTag("td",i).attr("width","100%")[0].outerHTML).attr("width","100%")[0].outerHTML)[0].outerHTML+t.buildTag("table.controlPanelTD",t.buildTag("tr",t.buildTag("td",this._controlPanel())[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML,{width:"100%"})[0].outerHTML)):this.model.enableRTL&&!this.model.enableVirtualScrolling?(i=t.buildTag("div#"+this._id+"_PivotSchemaDesigner.pivotFieldList","",{}).attr("class","e-childsplit")[0].outerHTML,u=t.buildTag("div.outerPanel","",{}).append("<div class=\"e-titleText\"><span style='padding-left:10px'>"+this.model.title+"<\/span><\/div>"+r+"<table class=\"e-outerTable\" style='width:100%'><tr class='e-parentsplit' style='width:100%'><td class=\"controlPanelTD\" style='width:50%'>"+this._controlPanel()+"<\/td><td style='width:50%'>"+i+"<\/td><\/tr><\/table>")):this.model.enableVirtualScrolling||this.model.enableRTL&&this.model.enableVirtualScrolling?(i=t.buildTag("div#"+this._id+"_PivotSchemaDesigner.pivotFieldList","",{}).attr("class","e-childsplit")[0].outerHTML,u=t.buildTag("div.outerPanel","",{}).append("<div class=\"e-titleText\"><span style='padding-left:10px'>"+this.model.title+"<\/span><\/div>"+r+"<table class=\"e-outerTable\" style='width:100%'><tr style='width:98%'><td style='width:49%'>"+i+"<\/td><td class=\"controlPanelTD\" style='width:49%'>"+this._controlPanel()+"<\/td>"+(this.model.enableVirtualScrolling?t.buildTag("td.virtualScrolling",t.buildTag("div.e-vScrollPanel")[0].outerHTML)[0].outerHTML:"")+"<\/tr><\/table>")):(i=t.buildTag("div#"+this._id+"_PivotSchemaDesigner.pivotFieldList","",{}).attr("class","e-childsplit")[0].outerHTML,u=t.buildTag("div.outerPanel","",{}).append("<div class=\"e-titleText\"><span style='padding-left:10px'>"+this.model.title+"<\/span><\/div>"+r+"<table class=\"e-outerTable\" style='width:100%'><tr class='e-parentsplit' style='width:100%'><td style='width:50%'>"+i+"<\/td><td class=\"controlPanelTD\" style='width:50%'>"+this._controlPanel()+"<\/td><\/tr><\/table>")):this.model.isResponsive?this.model.isResponsive&&(i=t.buildTag("div#"+this._id+"_PivotSchemaDesigner.pivotFieldList","",{})[0].outerHTML,u=t.buildTag("div.outerPanel","").append((n.trim(this.model.title)!=""?t.buildTag("div.e-titleText",t.buildTag("span",this.title(),{"padding-left":"10px"})[0].outerHTML)[0].outerHTML:"")+r+t.buildTag("table.oClientTbl",t.buildTag("tr",t.buildTag("td",t.buildTag("table.e-outerTable",t.buildTag("tr",t.buildTag("td",i)[0].outerHTML)[0].outerHTML)[0].outerHTML+t.buildTag("table.controlPanelTD",t.buildTag("tr",t.buildTag("td",this._controlPanel())[0].outerHTML+(this.model.enableVirtualScrolling?t.buildTag("td.virtualScrolling",t.buildTag("div.e-vScrollPanel")[0].outerHTML)[0].outerHTML:""))[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML,{width:"100%"})[0].outerHTML)):(i=t.buildTag("div#"+this._id+"_PivotSchemaDesigner.pivotFieldList","",{})[0].outerHTML,u=t.buildTag("div.outerPanel","",{}).append((n.trim(this.model.title)!=""?"<div class=\"e-titleText\"><span style='padding-left:10px'>"+this.model.title+"<\/span><\/div>":"")+r+"<table class=\"e-outerTable\" style='display:block'><tr><td>"+i+"<\/td><td class=\"controlPanelTD\"style='display:block'>"+this._controlPanel()+"<\/td>"+(this.model.enableVirtualScrolling?t.buildTag("td.virtualScrolling",t.buildTag("div.e-vScrollPanel")[0].outerHTML)[0].outerHTML:"")+"<\/tr><\/table>"));this.element.html(u);this.element.find(".e-reportToolbar").ejToolbar({enableRTL:this.model.enableRTL,height:"35px"});this._calculateSize();this.model.enableRTL&&this.element.addClass("e-rtl");(this.enableTogglePanel()&&!this.model.enableSplitter||this.model.isResponsive)&&(this.element.find(".e-controlPanel").width(this.element.width()-(this.element.width()/3+(this.model.enableVirtualScrolling?39:20))),n(t.buildTag("div.e-togglePanel",t.buildTag("div.e-toggleExpandButton e-icon","",{}).attr("aria-label","toggle expanded")[0].outerHTML+t.buildTag("div.e-toggleCollapseButton e-icon",{},{display:"none"}).attr("aria-label","toggle collapsed")[0].outerHTML)[0].outerHTML).appendTo(this.element.find(".pivotFieldList").parent()));(this.controlPlacement()==t.PivotClient.ControlPlacement.Tab&&this.defaultView()==t.PivotClient.DefaultView.Grid||this.displayMode()==t.PivotClient.DisplayMode.GridOnly)&&this.element.find(".e-chartTypesImg").addClass("e-chartTypesOnGridView");this.model.enableSplitter&&this.model.enableRTL?this.displayMode()==t.PivotClient.DisplayMode.ChartOnly?this.element.find(".e-controlPanel").css("right",-3):this.element.find(".e-controlPanel").css("right",7):this.element.find(".e-controlPanel").css("right",3);f="<div class ='reportList' ><input type='text' id='"+this._id+"_reportList' class='reportlist' title='"+this._getLocalizedLabels("ReportList")+"'/><\/div>";n(this.element).find(".e-reportCol").append(f);this.model.showReportCollection&&this._fetchCollectionList()},_calculateSize:function(){var i,f,r,u;n(this.element).height(this.model.size.height).width(this.model.size.width);this.model.isResponsive||n(this.element).height(n(this.element).height()<500?500:n(this.element).height());n(this.element).width(n(this.element).width()<600?600:n(this.element).width());i=this.element.height()-((this.element.find("div.e-titleText").length>0?50:0)+this.element.find("#"+this._id+"_reportToolbar").height()+5);this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?(this.model.isResponsive||this.model.enableSplitter||(r=this.element.width()/2.5,this.element.find(".e-csHeader").width(r+15),this.element.find(".axisBuilderTD").width(r/2),this.element.find(".e-cdbHeader, .e-cubeBrowser").width(r/2),this.element.find(".e-controlPanel").width(this.element.width()-(r+25))),this.model.enableVirtualScrolling?this.element.find(".e-controlPanel").height(i-15):this.model.enablePaging?this.element.find(".e-controlPanel").height(i-5):this.element.find(".e-controlPanel").height(i),this.element.find(".e-cubeTable").height(this.element.height()-((this.element.find("div.e-titleText").length>0?50:0)+this.element.find("#"+this._id+"_reportToolbar").height()+this.element.find(".e-csHeader").height()+38)),this.model.isResponsive?(this.element.find(".e-cubeBrowser").height(this.element.find(".e-cubeTable").height()-this.element.find(".e-cdbHeader").height()),this.element.find(".e-cubeTreeView").height(this.element.find(".e-cubeBrowser").height()-(56+(this.model.enableMeasureGroups?35:0))),f=this.element.find(".e-cubeTable").height()-(this.element.find(".e-axisHeader").height()*3+4)):(this.element.find(".cdbTD, .cubeTableTD").height(this.element.find(".e-cubeTable").height()),this.element.find(".e-cubeBrowser").height(this.element.find(".cdbTD, .cubeTableTD").height()+9-this.element.find(".e-cdbHeader").height()),this.element.find(".e-cubeTreeView").height(this.element.find(".e-cubeBrowser").height()-(61+(this.model.enableMeasureGroups?30:0))),f=this.element.find(".e-cubeTable").height()-(this.element.find(".e-axisHeader").height()*3+(this.model.enableSplitter?14:3))),this.element.find(".e-categoricalAxis, .e-rowAxis, .e-slicerAxis").height(f/3)):(this.model.isResponsive||this.model.enableSplitter||(this.element.find(".e-outerTable").width(this.element.width()),this.element.find(".e-controlPanel").width(this.element.width()*(2/3)),this.enableTogglePanel()&&this.element.find(".pivotFieldList").length>0&&this.element.find(".e-controlPanel").width(this.element.width()-(this.element.width()/3+(this.model.enableVirtualScrolling?39:20)))),this.element.find(".controlPanelTD, .e-outerTable").height(i),this.model.enableVirtualScrolling?this.element.find(".e-controlPanel").height(i-20):this.element.find(".e-controlPanel").height(i));this.displayMode()!=t.PivotClient.DisplayMode.ChartOnly&&(this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()==t.PivotClient.DisplayMode.ChartAndGrid?(this.model.enableVirtualScrolling&&this.element.find(".e-controlPanel").width(this.element.find(".e-controlPanel").width()-5),this.defaultView()==t.PivotClient.DefaultView.Grid?(this.model.enablePaging?this.element.find(".e-gridContainer").height(this.element.find(".e-controlPanel").height()/2+5):this.element.find(".e-gridContainer").height(this.element.find(".e-controlPanel").height()/2-10),this.model.isResponsive||this.model.enableSplitter||this.element.find(".e-gridContainer").width(this.element.find(".e-controlPanel").width()-15)):(this.element.find(".e-gridContainer").height(this.element.find(".e-controlPanel").height()/2-10),this.model.isResponsive||this.model.enableSplitter||this.element.find(".e-gridContainer").width(this.element.find(".e-controlPanel").width()-(this.enableTogglePanel()&&this._currentTab=="chart"?15:16)))):this.displayMode()==t.PivotClient.DisplayMode.GridOnly?(this.element.find(".e-gridContainer").height(this.element.find(".e-controlPanel").height()-(this.model.operationalMode==t.Pivot.OperationalMode.ClientMode?12:14)),this.model.isResponsive||this.model.enableSplitter||this.element.find(".e-gridContainer").width(this.element.find(".e-controlPanel").width()-15)):this.model.enableVirtualScrolling?this.element.find(".e-gridContainer").height(this.element.find(".e-controlPanel").height()-(this.element.find("ul.clientTab").height()+30)):this.element.find(".e-gridContainer").height(this.element.find(".e-controlPanel").height()-(this.element.find("ul.clientTab").height()+17)));this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()==t.PivotClient.DisplayMode.ChartAndGrid?this.defaultView()==t.PivotClient.DefaultView.Grid?(this.model.enablePaging?this.element.find(".e-chartContainer").height(this.element.find(".e-controlPanel").height()/2+15):this.element.find(".e-chartContainer").height(this.element.find(".e-controlPanel").height()/2-10),this.model.isResponsive||this.model.enableSplitter||this.element.find(".e-chartContainer").width(this.element.find(".e-controlPanel").width()-21)):(this.element.find(".e-chartContainer").height(this.element.find(".e-controlPanel").height()/2-15),this.model.isResponsive||this.model.enableSplitter||this.element.find(".e-chartContainer").width(this.element.find(".e-controlPanel").width()-20)):this.displayMode()==t.PivotClient.DisplayMode.ChartOnly?(this.element.find(".e-chartContainer").height(this.element.find(".e-controlPanel").height()-22),this.model.isResponsive||this.model.enableSplitter||this.element.find(".e-chartContainer").width(this.element.find(".e-controlPanel").width()-20)):(u=this.element.find(".e-controlPanel").height()-(this.element.find("ul.clientTab").height()+20),this.model.enableVirtualScrolling?this.element.find(".e-chartContainer").height(u-5):this.model.enablePaging?this.element.find(".e-chartContainer").height(u):this.element.find(".e-chartContainer").height(u-2)));this._chartHeight=this.model.enableToolBar?(this.model.enablePaging?this.element.find(".e-chartContainer").height()-105:this.element.find(".e-chartContainer").height()-68)+"px":(this.model.enablePaging?this.element.find(".e-chartContainer").height()-40:this.defaultView()=="chart"?this.element.find(".e-chartContainer").height()-3:this.element.find(".e-chartContainer").height()-2)+"px";this._chartWidth=this.model.isResponsive||this.model.enableSplitter?"99%":this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this.element.find(".e-chartContainer").width()-7+"px":this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()==t.PivotClient.DisplayMode.ChartAndGrid||this.displayMode()==t.PivotClient.DisplayMode.ChartOnly?this.element.find(".e-chartContainer").width()-8+"px":this.element.find(".e-chartContainer").width()-15+"px";this._gridHeight=this.model.enableToolBar?this.model.enablePaging?this.element.find(".e-gridContainer").height()-78+"px":this.element.find(".e-gridContainer").height()-46+"px":this.element.find(".e-gridContainer").height()-10+"px";this._gridWidth=this.model.isResponsive||this.model.enableSplitter?"98%":this.model.enableToolBar&&!(this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode)?this.element.find(".e-gridContainer").width()-16+"px":this.element.find(".e-gridContainer").width()-6+"px"},_reportToolbar:function(){return t.buildTag("ul",(this.model.toolbarIconSettings.enableNewReport?t.buildTag("li.e-newReportImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("NewReport")).attr({title:this._getLocalizedLabels("NewReport"),tabindex:0})[0].outerHTML:"")+(this.model.toolbarIconSettings.enableAddReport?t.buildTag("li.e-addReportImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("AddReport")).attr({title:this._getLocalizedLabels("AddReport"),tabindex:0})[0].outerHTML:"")+(this.model.toolbarIconSettings.enableRemoveReport?t.buildTag("li.e-removeReportImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("RemoveReport")).attr({title:this._getLocalizedLabels("RemoveReport"),tabindex:0})[0].outerHTML:"")+(this.model.toolbarIconSettings.enableRenameReport?t.buildTag("li.e-renameReportImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("RenameReport")).attr({title:this._getLocalizedLabels("RenameReport"),tabindex:0})[0].outerHTML:"")+(this.model.toolbarIconSettings.enableDBManipulation?t.buildTag("li.e-reportDBImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("DBReport")).attr({title:this._getLocalizedLabels("DBReport"),tabindex:0})[0].outerHTML:"")+(this.model.toolbarIconSettings.enableMDXQuery&&this.model.analysisMode==t.Pivot.AnalysisMode.Olap?t.buildTag("li.e-mdxImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("MDXQuery")).attr({title:this._getLocalizedLabels("MDXQuery"),tabindex:0})[0].outerHTML:"")+(this.model.toolbarIconSettings.enableDeferUpdate&&this.model.enableDeferUpdate&&this.model.analysisMode!=t.Pivot.AnalysisMode.Pivot&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?t.buildTag("li.e-autoExecuteImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("DeferUpdate")).attr({title:this._getLocalizedLabels("DeferUpdate"),tabindex:0})[0].outerHTML:"")+(this.model.toolbarIconSettings.enableCalculatedMember?t.buildTag("li.e-calcMemberImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("CalculatedMember")).attr({title:this._getLocalizedLabels("CalculatedMember"),tabindex:0})[0].outerHTML:"")+(this.model.toolbarIconSettings.enableExcelExport?t.buildTag("li.e-excelExportImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("ExportToExcel")).attr({title:this._getLocalizedLabels("ExportToExcel"),tabindex:0})[0].outerHTML:"")+(this.model.toolbarIconSettings.enableWordExport?t.buildTag("li.e-wordExportImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("ExportToWord")).attr({title:this._getLocalizedLabels("ExportToWord"),tabindex:0})[0].outerHTML:"")+(this.model.toolbarIconSettings.enablePdfExport?t.buildTag("li.e-pdfExportImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("ExportToPdf")).attr({title:this._getLocalizedLabels("ExportToPdf"),tabindex:0})[0].outerHTML:"")+(this.model.toolbarIconSettings.enableFullScreen&&this.model.displaySettings.enableFullScreen?t.buildTag("li.maximizedView e-icon","",{}).attr("aria-label",this._getLocalizedLabels("FullScreen")).attr({title:this._getLocalizedLabels("FullScreen"),tabindex:0})[0].outerHTML:"")+(this.model.operationalMode!=t.Pivot.OperationalMode.ClientMode&&this.model.analysisMode==t.Pivot.AnalysisMode.Olap?(this.model.toolbarIconSettings.enableSortOrFilterColumn?t.buildTag("li.e-colSortFilterImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("SortOrFilterColumn")).attr({title:this._getLocalizedLabels("SortOrFilterColumn"),tabindex:0})[0].outerHTML:"")+(this.model.toolbarIconSettings.enableSortOrFilterRow?t.buildTag("li.e-rowSortFilterImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("SortOrFilterRow")).attr({title:this._getLocalizedLabels("SortOrFilterRow"),tabindex:0})[0].outerHTML:""):"")+(this.model.toolbarIconSettings.enableToggleAxis?t.buildTag("li.e-toggleaxisImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("ToggleAxis")).attr({title:this._getLocalizedLabels("ToggleAxis"),tabindex:0})[0].outerHTML:"")+(this.model.toolbarIconSettings.enableChartTypes?t.buildTag("li.e-chartTypesImg e-icon","",{}).attr("aria-label",this._getLocalizedLabels("ChartTypes")).attr({title:this._getLocalizedLabels("ChartTypes"),tabindex:0})[0].outerHTML:"")+(this.model.showReportCollection?t.buildTag("li.e-collectionli",t.buildTag("label.e-collectionLbl",this._getLocalizedLabels("Collection")+": ",{})[0].outerHTML+t.buildTag("div.e-collectiondiv",t.buildTag("input.e-collectionlist","",{})[0].outerHTML)[0].outerHTML,{}).attr("aria-label",this._getLocalizedLabels("Collection")).attr({title:this._getLocalizedLabels("Collection"),tabindex:0})[0].outerHTML+t.buildTag("li.e-reportli",(this.model.showReportCollection?t.buildTag("label.e-reportLbl",this._getLocalizedLabels("Report")+": ",{})[0].outerHTML:"")+t.buildTag("li.e-reportCol e-icon","",{}).attr("aria-label",this._getLocalizedLabels("Report")).attr({title:this._getLocalizedLabels("ReportList"),tabindex:0})[0].outerHTML)[0].outerHTML:t.buildTag("li.e-reportCol e-icon","",{}).attr("aria-label",this._getLocalizedLabels("Report")).attr({title:this._getLocalizedLabels("ReportList"),tabindex:0})[0].outerHTML))[0].outerHTML},_clientGridDrillSuccess:function(i){if(this._isChartDrillAction)this._isChartDrillAction=!1;else if(i.axis=="rowheader"&&this.displayMode()!=t.PivotClient.DisplayMode.GridOnly){i.drillAction=="drilldown"?this._drillParams.push(i.drilledMember):this._drillParams=n.grep(this._drillParams,function(n){return n.indexOf(i.drilledMember)<0});this._pivotChart._drillParams=this._drillParams;var r=i.drilledMember.split(">#>");this._pivotChart._labelCurrentTags.expandedMembers=r;i.drillAction=="drillup"&&(this.model.analysisMode==t.Pivot.AnalysisMode.Pivot?(this._pivotChart._labelCurrentTags.expandedMembers=[],this._drillParams.length>0&&this._getDrilledMember(i.drillAction)):this._pivotChart._labelCurrentTags.expandedMembers.length==0&&this._drillParams.length>0&&this._getDrilledMember(i.drillAction));this._pivotChart._drillAction=i.drillAction;this._pivotChart.refreshControl()}},_getDrilledMember:function(){var o=this._drillParams[this._drillParams.length-1],r=n.grep(this._drillParams,function(n){return n.indexOf(o)>=0}),e,f,u,i;if(this.model.analysisMode==t.Pivot.AnalysisMode.Pivot)for(i=0;i<r.length;i++)r[i].split(">#>").length>this._pivotChart._labelCurrentTags.expandedMembers.length&&(this._pivotChart._labelCurrentTags.expandedMembers=r[i].split(">#>"));else{for(e=r[0].split(">#>").length,f=0,i=1;i<r.length;i++)if(r[i].split(">#>").length>e){f=i;break}for(this._drillInfo=r[f].split(">#>"),u=0,this._pivotChart._labelCurrentTags.expandedMembers=[],i=0;i<this._drillInfo.length;i++)t.isNullOrUndefined(this._pivotChart._labelCurrentTags.expandedMembers[u])&&(this._pivotChart._labelCurrentTags.expandedMembers[u]=[]),i+1<this._drillInfo.length?t.isNullOrUndefined(t.olap._mdxParser._splitCellInfo(this._drillInfo[i]))||t.isNullOrUndefined(t.olap._mdxParser._splitCellInfo(this._drillInfo[i+1]))||t.olap._mdxParser._splitCellInfo(this._drillInfo[i]).hierarchyUniqueName!=t.olap._mdxParser._splitCellInfo(this._drillInfo[i+1]).hierarchyUniqueName?u++:this._pivotChart._labelCurrentTags.expandedMembers[u].push(this._drillInfo[i]):(this._pivotChart._labelCurrentTags.expandedMembers[u].push(this._drillInfo[i]),this._isChartDrillAction=!0)}},_clearSorting:function(r){if(n(r.target).parent().attr("disabled")=="disabled")return!1;t.Pivot.closePreventPanel(this);var f=this,e=this.model.dataSource,o=this._schemaData._selectedFieldName.toLowerCase(),u=n.map(e.columns,function(n){if(n.fieldName!=i&&n.fieldName.toLowerCase()==o)return n});u.length==0&&(u=n.map(e.rows,function(n){if(n.fieldName!=i&&n.fieldName.toLowerCase()==o)return n}));u.length>0&&delete u[0].sortOrder;this.model.dataSource=e;this.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog)").remove();this._schemaData!=null&&this._schemaData.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog)").remove();f._isTimeOut=!0;setTimeout(function(){f._isTimeOut&&f._waitingPopup.show()},800);this.model.analysisMode==t.Pivot.AnalysisMode.Pivot?this.refreshControl():t.olap.base.getJSONData({action:"clearSorting"},this.model.dataSource,this)},_clientChartDrillSuccess:function(i){var f=[],e,o,r,s,u;if(i.drilledMember!=""&&(f=i.drilledMember.split(">#>"),this._isChartDrillAction=!0),i.drillAction!="drilldown"||t.isNullOrUndefined(this._pivotChart._labelCurrentTags.expandedMembers)||this._drillParams.indexOf(this._pivotChart._labelCurrentTags.expandedMembers.join(">#>"))!=-1||this._drillParams.push(this._pivotChart._labelCurrentTags.expandedMembers.join(">#>")),this.displayMode()!="chartonly")for(e=0,o=this._pivotGrid._rowCount,r=0;r<f.length;r++)s=r==f.length-1&&i.drillAction=="drilldown"?this._pivotGrid.element.find(".e-pivotGridTable th.summary[data-p^='"+r+"']"):this._pivotGrid.element.find(".e-pivotGridTable th.rowheader[data-p^='"+r+"']"),u=n.grep(s,function(t){return n(t).clone().children().remove().end().text()==f[r]}),u=n.grep(u,function(t){return parseInt(n(t).attr("data-p").split(",")[1])>=e&&parseInt(n(t).attr("data-p").split(",")[1])<=o}),r==f.length-1?n(u).find("."+(i.drillAction=="drilldown"?"e-expand":"e-collapse")).trigger("click"):(e=parseInt(n(u).attr("data-p").split(",")[1]),o=e+parseInt(n(u).find("span").attr("data-tag"))+u[0].rowSpan)},setChartDrillParams:function(i,r){var e,u,h;r=="drilldown"?this._drillParams.push(i):this._drillParams=n.grep(this._drillParams,function(n){return n.indexOf(i)<0});this._drillInfo=i.split(">#>");e=0;this._pivotChart._labelCurrentTags.expandedMembers=[];var o=this._drillInfo.length-1,s=r=="drillup"&&this._drillParams.length>0&&this._drillParams[this._drillParams.length-1]!=this._drillInfo.join(">#>")&&this._drillParams[this._drillParams.length-1].split(">#>").filter(function(n){return n<o}).join(">#>")==this._drillInfo.filter(function(n){return n<o}).join(">#>"),f=s?this._drillParams[this._drillParams.length-1].split(">#>"):this._drillInfo;for(u=0;u<f.length;u++)t.isNullOrUndefined(this._pivotChart._labelCurrentTags.expandedMembers[e])&&(this._pivotChart._labelCurrentTags.expandedMembers[e]=[]),u+1<f.length||s?(!t.isNullOrUndefined(t.olap._mdxParser._splitCellInfo(f[u]))&&!t.isNullOrUndefined(t.olap._mdxParser._splitCellInfo(f[u+1]))?t.olap._mdxParser._splitCellInfo(f[u]).hierarchyUniqueName==t.olap._mdxParser._splitCellInfo(f[u+1]).hierarchyUniqueName:u==f.length-1?!0:!1)?this._pivotChart._labelCurrentTags.expandedMembers[e].push(f[u]):e++:r=="drilldown"&&(this._pivotChart._labelCurrentTags.expandedMembers[e].push(f[u]),this._isChartDrillAction=!0);h=n.grep(this._pivotChart._labelCurrentTags.expandedMembers,function(n){return n.length>0});h.length==0&&this._drillParams.length>0&&(this._getDrilledMember(r),this._pivotGrid._drillAction="drilldown")},_setChartDrillMembers:function(n){var o,h,c,e,p,i,a,r;if(this._drillInfo.length>0&&!this._pivotChart.model.enableMultiLevelLabels){var f=0,u=this._pivotChart._cloneEngine(n),s=!1;for(i=0;i<this._pivotChart._labelCurrentTags.expandedMembers.length;i++)for(r=0;r<this._pivotChart._labelCurrentTags.expandedMembers[i].length;r++)s=!0;if(s){for(h=this._drillInfo[this._drillInfo.length-1],i=0;i<this.model.dataSource.rows.length;i++)c=t.olap._mdxParser._splitCellInfo(h).hierarchyUniqueName,this.model.dataSource.rows[i].fieldName==c&&(o=i);var v=this.model.dataSource.values[0].axis=="rows"?1:0,l=this._drillInfo.length-1,y=this._pivotGrid._drillAction=="drillup"&&this._drillParams.length>0&&this._drillParams[this._drillParams.length-1].split(">#>").filter(function(n){return n<l}).join(">#>")==this._drillInfo.filter(function(n){return n<l}).join(">#>");if(y)for(i=0;i<this._drillParams[this._drillParams.length-1].split(">#>").length;i++)t.isNullOrUndefined(t.olap._mdxParser._splitCellInfo(this._drillParams[this._drillParams.length-1].split(">#>")[i]))||t.olap._mdxParser._splitCellInfo(this._drillParams[this._drillParams.length-1].split(">#>")[i]).hierarchyUniqueName==""||(e=t.olap._mdxParser._splitCellInfo(this._drillParams[this._drillParams.length-1].split(">#>")[i]).leveName,this._pivotChart._cropData(u,e,i+f,!1));else{if(u[0+f][0].ColSpan>(this._drillInfo.length+this._pivotGrid._drillAction=="drilldown"?1:0))for(i=this._drillInfo.length+(this._pivotGrid._drillAction=="drilldown"?1:0)+(this.model.dataSource.rows.length-(o+1));i+v<u[0+f][0].ColSpan;i++)u.splice(i,1),u[0][0].ColSpan--;for(i=0;i<this._drillInfo.length-(this._pivotGrid._drillAction=="drillup"?1:0);i++)t.isNullOrUndefined(t.olap._mdxParser._splitCellInfo(this._drillInfo[i]))||t.olap._mdxParser._splitCellInfo(this._drillInfo[i]).hierarchyUniqueName==""||(e=t.olap._mdxParser._splitCellInfo(this._drillInfo[i]).leveName,this._pivotChart._cropData(u,e,i+f,!1))}if(p=0,!t.isNullOrUndefined(this._pivotChart._labelCurrentTags.expandedMembers))for(i=0;i<this._pivotChart._labelCurrentTags.expandedMembers.length;i++)if(!t.isNullOrUndefined(this._pivotChart._labelCurrentTags.expandedMembers[i]))if(i>o)for(r=0;r<this._pivotChart._labelCurrentTags.expandedMembers[i].length;r++)a=this._pivotChart._labelCurrentTags.expandedMembers[i][r].split("::")[2],this._pivotChart._cropData(u,a,i+f,!0),f++;else for(r=0;r<this._pivotChart._labelCurrentTags.expandedMembers[i].length;r++)f++}this._drillInfo=[];this._pivotChart._generateData(u)}else this._pivotChart.generateJSON(this,n)},refreshPagedPivotClient:function(n,i){var r,u=this;if(t.isNullOrUndefined(this._waitingPopup)||(u._isTimeOut=!0,setTimeout(function(){u._isTimeOut&&u._waitingPopup.show()},800)),n=n.indexOf("categ")!=-1?"categorical":"series",this.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&this.model.analysisMode==t.Pivot.AnalysisMode.Olap)n=="categorical"?this._categCurrentPage=parseInt(i):this._seriesCurrentPage=parseInt(i),t.olap.base.getJSONData({action:"navPaging"},this.model.dataSource,this);else if(this.currentReport!=""){try{r=JSON.parse(this.currentReport)}catch(f){r=this.currentReport}this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.paging,JSON.stringify({action:"paging",pagingInfo:n+":"+i,currentReport:r,layout:t.PivotGrid.Layout.NoSummaries,customObject:null}),this.refreshPagedPivotClientSuccess)}},refreshPagedPivotClientSuccess:function(r){var u=[];r.d!=i?(u=n.map(r.d,function(n){var t=n.Key;if(t=="JsonRecords")return n}),n.merge(u,n.map(r.d,function(n){var t=n.Key;if(t=="OlapReport")return n})),this.reports=n.map(r.d,function(n){var t=n.Key;if(t=="ClientReports")return n.Value}).toString()):(this.reports=r.ClientReports,delete r.ClientReports,u=r);this.model.displaySettings.mode==t.PivotClient.DisplayMode.GridOnly?this._pivotGrid._renderControlSuccess(r):this.model.displaySettings.mode==t.PivotClient.DisplayMode.ChartOnly?this._pivotChart.renderControlSuccess(u):(this.model.displaySettings.mode=t.PivotClient.DisplayMode.ChartAndGrid)&&(this._pivotGrid._renderControlSuccess(r),this._pivotChart.renderControlSuccess(u))},_hiddenCellInfo:function(t){var i=n.map(t,function(n){var t=n.Key;if(t=="FilteredColumnHeaders")return n.Value}),r=n.map(t,function(n){var t=n.Key;if(t=="FilteredRowHeaders")return n.Value});return{columnArea:i.length>0?i[0]:i,rowArea:r.length>0?r[0]:[]}},_loadCollectionList:function(r){var u="";u=r[0]!=i?t.isNullOrUndefined(r[0].Value)?"":r[0].Value:r.d!=i?t.isNullOrUndefined(r.d[0].Value)?"":r.d[0].Value:r.ReportNameList;u=u!=""?u.split("__"):[];this.element.find(".e-collectionlist").ejDropDownList({dataSource:u,enableRTL:this.model.enableRTL,fields:{text:"name",value:"name"},height:"26px",change:t.proxy(this._collectionChange,this),create:function(){n(this.wrapper.find(".e-input")).focus(function(){n(this).blur()})}});this._currentCollection!=""&&this.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&(this.element.find(".e-collectionlist").data("ejDropDownList").option("change",""),this.element.find(".e-collectionlist").data("ejDropDownList").selectItemByText(this._currentCollection),this.element.find(".e-collectionlist").ejDropDownList("option","change",t.proxy(this._collectionChange,this)))},_fetchCollectionList:function(){var i,n;this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?(n=JSON.stringify(this.model.customObject),this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.fetchReportList,JSON.stringify({customObject:n,action:"LoadReport",operationalMode:this.model.operationalMode,analysisMode:this.model.analysisMode}),function(n){this._loadCollectionList(n)})):(i={url:"",reportCollection:this._clientReportCollection,reportList:"",mode:this.model.analysisMode},this._trigger("fetchReport",{targetControl:this,fetchReportSetting:i}),this.model.enableLocalStorage?this._loadCollectionList({d:[{Key:"ReportNameList",Value:i.reportList}]}):(n=JSON.stringify(this.model.customObject),this.doAjaxPost("POST",i.url+"/"+this.model.serviceMethodSettings.fetchReportList,JSON.stringify({customObject:n,action:"LoadReport",operationalMode:this.model.operationalMode,analysisMode:this.model.analysisMode}),function(n){this._loadCollectionList(n)})))},_renderControlSuccess:function(r){var nt,l,y,tt,f,p,e,w,it,a,g,b,u,ut,ft,k;if(t.isNullOrUndefined(this.model))return!1;t.Pivot._updateValueSortingIndex(r,this);(this.model.isResponsive||this.model.collapseCubeBrowserByDefault)&&(this.model.displaySettings.enableTogglePanel=!0);nt={element:this.element,customObject:this.model.customObject};this._trigger("load",nt);var v="",s="",h="",c="",o="";if(r[0]!=i)v=r[0].Value,s=r[1].Value,h=r[2].Value,c=r[3].Value,o=r[4].Value,this.currentReport=this._deferReport=r[5].Value,this.reports=r[6].Value,this.reportsCount=r[7].Value,y=n.parseJSON(r[8].Value),this.model.enableMeasureGroups&&(this.measureGroupInfo=n.parseJSON(r[9].Value)),this.currentCubeName=r[10].Value,r[11]!=null&&r[11]!=i&&(this.model.customObject=r[11].Value);else if(r.d!=i){if(t.isNullOrUndefined(r.d[3])||r.d[3].Value=="Pivot"){this.model.analysisMode=t.Pivot.AnalysisMode.Pivot;this._currentReportName="Default";l=this._hiddenCellInfo(r.d);r.d.length>4&&r.d[4].Value=="LoadReport"?(this._clientReportCollection=JSON.parse(r.d[5].Value),this._renderClientControls({PivotReport:r.d[0].Value,GridJSON:r.d[1].Value,ChartJSON:r.d[2].Value,FilteredColumnHeaders:l.columnArea.length>0?l.columnArea:"[]",FilteredRowHeaders:l.rowArea.length>0?l.rowArea:"[]"}),y=n.map(this._clientReportCollection,function(n){if(n.name!=i)return{name:n.name}}),tt=this.element.find(".reportlist").data("ejDropDownList"),this.element.find(".reportlist").ejDropDownList("option","dataSource",y),tt.selectItemByText(y[0].name),this._unWireEvents(),this._wireEvents()):(this.model.enableDrillThrough&&!t.isNullOrUndefined(r.d[4].Value)&&(this.dataSet=r.d[4].Value),this._clientReportCollection=[{name:"Default",report:n.parseJSON(r.d[0].Value).Report}],this._renderClientControls({PivotReport:r.d[0].Value,GridJSON:r.d[1].Value,ChartJSON:r.d[2].Value,FilteredColumnHeaders:l.columnArea.length>0?l.columnArea[0]:"[]",FilteredRowHeaders:l.rowArea.length>0?l.rowArea[0]:"[]"}));return}v=r.d[0].Value;s=r.d[1].Value;h=r.d[2].Value;c=r.d[3].Value;o=r.d[4].Value;this.currentReport=this._deferReport=r.d[5].Value;this.reports=r.d[6].Value;this.reportsCount=r.d[7].Value;y=n.parseJSON(r.d[8].Value);this.model.enableMeasureGroups&&(this.measureGroupInfo=n.parseJSON(r.d[9].Value));this.currentCubeName=r.d[10].Value;r.d[11]!=null&&r.d[11]!=i&&(this.model.customObject=r.d[11].Value)}else if(r!="")if(t.isNullOrUndefined(r.DataModel)||r.DataModel=="Olap")this.model.analysisMode=t.Pivot.AnalysisMode.Olap,v=r.Cubes,s=r.Columns,h=r.Rows,c=r.Slicers,o=r.CubeTreeInfo,this.currentReport=r.CurrentReport,this.reports=r.ClientReports,y=n.parseJSON(r.ReportList),this.reportsCount=r.ReportsCount,this.model.enableMeasureGroups&&(this.measureGroupInfo=n.parseJSON(r.MeasureGroups)),this.currentCubeName=r.CurrentCubeName,r.customObject!=null&&r.customObject!=i&&(this.model.customObject=r.customObject);else{this.model.analysisMode=t.Pivot.AnalysisMode.Pivot;this._currentReportName=t.isNullOrUndefined(r.ReportCollection)?"Default":JSON.parse(r.ReportCollection)[0].name;this._clientReportCollection=t.isNullOrUndefined(r.ReportCollection)?[{name:"Default",report:n.parseJSON(r.PivotReport).Report,_fieldSelectedMembers:this._fieldSelectedMembers}]:JSON.parse(r.ReportCollection);t.isNullOrUndefined(r.DataSet)||(this.dataSet=r.DataSet);this._renderClientControls(r);return}this.model.afterServiceInvoke!=null&&this._trigger("afterServiceInvoke",{action:"initialize",element:this.element,customObject:this.model.customObject});f=null;r!=""&&(f=n.parseJSON(o));p=t.buildTag("div.e-reportToolbar#"+this._id+"_reportToolbar",this._reportToolbar(),{width:"410px",height:"29px"})[0].outerHTML;this.model.enableSplitter?this.model.isResponsive?(e=this._createCubeSelector()+t.buildTag("table.e-cubeTable",t.buildTag("tbody",t.buildTag("tr.e-serversplitresponsive",t.buildTag("td.cubeTableTD",this._createCubeBrowser(o)).attr({valign:"bottom"}).css("width","50%")[0].outerHTML+t.buildTag("td.cubeTableTD",this._createAxisElementBuilder(s,h,c)).attr({valign:"bottom"}).css("width","50%")[0].outerHTML).css("width","100%")[0].outerHTML)[0].outerHTML)[0].outerHTML,w=t.buildTag("div.outerPanel","").append((n.trim(this.model.title)!=""?t.buildTag("div.e-titleText",t.buildTag("span",this.title(),{"padding-left":"10px"})[0].outerHTML)[0].outerHTML:"")+p+t.buildTag("table.oClientTbl",t.buildTag("tr",t.buildTag("td",t.buildTag("table.e-outerTable",t.buildTag("tr",t.buildTag("td",e)[0].outerHTML)[0].outerHTML)[0].outerHTML+t.buildTag("table.controlPanelTD",t.buildTag("tr",t.buildTag("td",this._controlPanel())[0].outerHTML+(this.model.enableVirtualScrolling?t.buildTag("td.virtualScrolling",t.buildTag("div.e-vScrollPanel")[0].outerHTML)[0].outerHTML:""))[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML,{width:"100%"})[0].outerHTML)):this.model.enableRTL?this.model.enableVirtualScrolling?(e=this._createCubeSelector()+"<table class=e-cubeTable style='width:100%'><tr class='e-serverchildsplit' style='width:98%'><td class=axisBuilderTD valign=\"bottom\" style='width:47%'>"+this._createAxisElementBuilder(s,h,c)+"<\/td><td class=cdbTD valign=\"bottom\" style='width:47%;'>"+this._createCubeBrowser(o)+"<\/td><\/tr><\/table>",w=t.buildTag("div.outerPanel","",{}).append('<div class="e-titleText"><span>'+this.title()+"<\/span><\/div>"+p+"<table class=\"e-outerTable\" style='width:100%'><tr class='e-serverparentsplit' style='width:100%'><td style='width:49%'>"+e+"<\/td><td class=\"controlPanelTD\" style='width:49%;'>"+this._controlPanel()+"<\/td>"+(this.model.enableVirtualScrolling?t.buildTag("td.virtualScrolling",t.buildTag("div.e-vScrollPanel")[0].outerHTML)[0].outerHTML:"")+"<\/tr><\/table>")):(e=this._createCubeSelector()+"<table class=e-cubeTable style='width:98%'><tr class='e-serverchildsplit'><td class=axisBuilderTD valign=\"bottom\" style='"+(this.model.enableRTL?"padding-right:0px;":"padding-left:"+this.model.enableSplitter?"0px":"5px")+"'>"+this._createAxisElementBuilder(s,h,c)+'<\/td><td class=cdbTD valign="bottom"\'>'+this._createCubeBrowser(o)+"<\/td><\/tr><\/table>",w=t.buildTag("div.outerPanel","",{}).append('<div class="e-titleText"><span>'+this.title()+"<\/span><\/div>"+p+"<table class=\"e-outerTable\" style='width:100%'><tr class='e-serverparentsplit'><td class=\"controlPanelTD\">"+this._controlPanel()+"<\/td><td>"+e+"<\/td>"+(this.model.enableVirtualScrolling?t.buildTag("td.virtualScrolling",t.buildTag("div.e-vScrollPanel")[0].outerHTML)[0].outerHTML:"")+"<\/tr><\/table>")):(e=this._createCubeSelector()+"<table class=e-cubeTable style='width:100%'><tr class='e-serverchildsplit' style='width:100%'><td class=cdbTD valign=\"bottom\" style='width:47%;'>"+this._createCubeBrowser(o)+'<\/td><td class=axisBuilderTD valign="bottom" style=\''+(this.model.enableRTL?"padding-right:5px;":"padding-left:"+this.model.enableSplitter?"0px":"5px")+"width:47%'>"+this._createAxisElementBuilder(s,h,c)+"<\/td><\/tr><\/table>",w=t.buildTag("div.outerPanel","",{}).append('<div class="e-titleText"><span>'+this.title()+"<\/span><\/div>"+p+"<table class=\"e-outerTable\" style='width:100%'><tr class='e-serverparentsplit' style='width:100%'><td style='width:49%'>"+e+"<\/td><td class=\"controlPanelTD\" style='width:49%'>"+this._controlPanel()+"<\/td>"+(this.model.enableVirtualScrolling?t.buildTag("td.virtualScrolling",t.buildTag("div.e-vScrollPanel")[0].outerHTML)[0].outerHTML:"")+"<\/tr><\/table>")):this.model.isResponsive?this.model.isResponsive&&(e=this._createCubeSelector()+t.buildTag("table.e-cubeTable",t.buildTag("tbody",t.buildTag("tr",t.buildTag("td.cubeTableTD",this._createCubeBrowser(o)).attr({valign:"bottom",width:"50%",height:"100%"}).css({"table-layout":"fixed",display:"initial"})[0].outerHTML+t.buildTag("td.cubeTableTD",this._createAxisElementBuilder(s,h,c)).attr({valign:"bottom",width:"50%",height:"100%"})[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML,w=t.buildTag("div.outerPanel","").append((n.trim(this.model.title)!=""?t.buildTag("div.e-titleText",t.buildTag("span",this.title(),{"padding-left":"10px"})[0].outerHTML)[0].outerHTML:"")+p+t.buildTag("table.oClientTbl",t.buildTag("tr",t.buildTag("td",t.buildTag("table.e-outerTable",t.buildTag("tr",t.buildTag("td",e)[0].outerHTML)[0].outerHTML)[0].outerHTML+t.buildTag("table.controlPanelTD",t.buildTag("tr",t.buildTag("td",this._controlPanel())[0].outerHTML+(this.model.enableVirtualScrolling?t.buildTag("td.virtualScrolling",t.buildTag("div.e-vScrollPanel")[0].outerHTML)[0].outerHTML:""))[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML,{width:"100%","table-layout":"fixed"})[0].outerHTML)):(e=this._createCubeSelector()+"<table class=e-cubeTable><tr><td class=cdbTD valign=\"bottom\" style='display:block'>"+this._createCubeBrowser(o)+'<\/td><td class=axisBuilderTD valign="bottom" style=\''+(this.model.enableRTL?"padding-right:5px;":"padding-left:5px;")+"'>"+this._createAxisElementBuilder(s,h,c)+"<\/td><\/tr><\/table>",w=t.buildTag("div.outerPanel","",{}).append('<div class="e-titleText"><span>'+this.title()+"<\/span><\/div>"+p+'<table class="e-outerTable"><tr><td>'+e+"<\/td><td class=\"controlPanelTD\" style='display:block'>"+this._controlPanel()+"<\/td>"+(this.model.enableVirtualScrolling?t.buildTag("td.virtualScrolling",t.buildTag("div.e-vScrollPanel")[0].outerHTML)[0].outerHTML:"")+"<\/tr><\/table>"));this.element.html(w);this.element.find(".e-reportToolbar").ejToolbar({enableRTL:this.model.enableRTL,height:"35px"});this._calculateSize();this.model.enableRTL&&this.element.addClass("e-rtl");(this.controlPlacement()==t.PivotClient.ControlPlacement.Tab&&this.defaultView()==t.PivotClient.DefaultView.Grid||this.displayMode()==t.PivotClient.DisplayMode.GridOnly)&&this.element.find(".e-chartTypesImg").addClass("e-chartTypesOnGridView");it="<div class ='reportList' ><input type='text' id='"+this._id+"_reportList' class='reportlist' title='"+this._getLocalizedLabels("ReportList")+"'/><\/div>";this.model.enableMeasureGroups&&this._createMeasureGroup();n(this.element).find(".e-reportCol").append(it);this.element.find(".reportlist").ejDropDownList({dataSource:y,enableRTL:this.model.enableRTL,fields:{text:"name",value:"name"},height:"26px",create:function(){n(this.wrapper.find(".e-input")).focus(function(){n(this).blur()})}});this.model.showReportCollection&&this._fetchCollectionList();this.element.find(".reportlist").attr("tabindex",0);var d=this.element.find(".e-csHeader").width()-this.element.find(".cubeText").width()-(this.element.find(".e-toggleExpandButton").length>0?this.element.find(".e-toggleExpandButton").width():0)-(this.model.analysisMode=="olap"&&this.model.operationalMode=="servermode"||this.model.enableSplitter?20:50),rt=this.enableTogglePanel()?d-25:d,et=v==""?"":n.parseJSON(v);for(this.element.find(".cubeSelector").ejDropDownList({dataSource:et,enableRTL:this.model.enableRTL,fields:{text:"name",value:"name"},width:this.model.enableSplitter&&!this.model.isResponsive?"100%":""+rt+"px"}),this.ddlTarget=this.element.find(".cubeSelector").data("ejDropDownList"),this.reportDropTarget=this.element.find("#"+this._id+"_reportList").data("ejDropDownList"),r==""||t.isNullOrUndefined(this.ddlTarget)||(this.ddlTarget.selectItemByText(this.currentCubeName),this.reportDropTarget.model.dataSource.length&&this.reportDropTarget.selectItemByText(this.reportDropTarget.model.dataSource[0].name),this._selectedReport=this.reportDropTarget.currentValue),this.element.find(".cubeSelector").ejDropDownList("option","change",t.proxy(this._cubeChanged,this)),this.element.find(".cubeSelector").attr("tabindex",0),this.element.find(".reportlist").ejDropDownList("option","change",t.proxy(this.reportChanged,this)),this.model.enableMeasureGroups&&this.element.find(".measureGroupSelector").ejDropDownList("option","change",t.proxy(this._measureGroupChanged,this)),this.element.find(".e-cubeTreeView").ejTreeView({clientObj:this,fields:{id:"id",parentId:"pid",text:"name",spriteCssClass:"spriteCssClass",dataSource:f,parentUniqueName:"parentUniqueName"},allowDragAndDrop:!0,enableRTL:this.model.enableRTL,allowDropChild:!1,allowDropSibling:!1,dragAndDropAcrossControl:!0,nodeDragStart:function(){this.model.clientObj.isDragging=!0},beforeDelete:function(){return!1},cssClass:"pivotTreeViewDragedNode",nodeDropped:t.proxy(this._nodeDropped,this)}),this.element.find(".e-cubeTreeView").attr("tabindex",0),a=this.element.find(".e-cubeTreeView").find("li"),u=0;u<a.length;u++)!t.isNullOrUndefined(a[u].id)&&a[u].id.indexOf("'").length>-1&&a[u].setAttribute("id",a[u].id.replace(/'/g,"")),a[u].setAttribute("data-tag",f[u].tag),t.isNullOrUndefined(f[u].parentUniqueName)||f[u].parentUniqueName==""||a[u].setAttribute("data-parentUniqueName",t.isNullOrUndefined(f[u].parentUniqueName)?"":f[u].parentUniqueName.split(">>||>>")[1]);for(g=this.element.find(".e-cubeTreeView .e-folderCDB"),u=0;u<g.length;u++)n(g[u].parentElement).removeClass("e-draggable");if(this.element.find(".e-cubeTreeView .e-kpiRootCDB").length>0&&this.element.find(".e-cubeTreeView .e-kpiRootCDB").parent().removeClass("e-draggable"),this.element.find(".e-cubeTreeView .e-calcMemberGroupCDB").length>0&&(this.element.find(".e-cubeTreeView .e-calcMemberGroupCDB").parent().removeClass("e-draggable"),b=this.element.find(".e-cubeTreeView .e-calcMemberCDB").parents("li").find("li"),b.length>0))for(u=0;u<b.length;u++)this._calcMembers.push(f[u+1]),b[u].setAttribute("expression",f[u+1].expression),b[u].setAttribute("formatString",f[u+1].formatString),b[u].setAttribute("nodeType",f[u+1].nodeType);if(this._cubeTreeView=this.element.find(".e-cubeTreeView").data("ejTreeView"),ut=t.buildTag("div.searchDiv",t.buildTag("input#"+this._id+"_SearchTreeView.searchTreeView").attr("type","text")[0].outerHTML,{margin:"5px 5px 0px 0px"})[0].outerHTML,ft=t.buildTag("div.e-cubeName",this.currentCubeName+ut),n(this.element).find(".e-cubeBrowser").prepend(ft),this.element.find("#"+this._id+"_SearchTreeView").ejMaskEdit({name:"inputbox",width:"100%",inputMode:t.InputMode.Text,watermarkText:this._getLocalizedLabels("Search"),maskFormat:"",textAlign:this.model.enableRTL?"right":"left",change:t.proxy(t.Pivot._searchTreeNodes,this)}),this.element.find(".e-categoricalAxis, .e-rowAxis, .e-slicerAxis").ejDroppable({drop:t.proxy(this._onDropped,this)}),this.element.find(".e-categoricalAxis, .e-rowAxis, .e-slicerAxis").find("button").ejButton({height:"20px",type:t.ButtonType.Button}),this.enableTogglePanel()&&(n(t.buildTag("div.e-toggleExpandButton e-icon","",{}).attr("aria-label","toggle expanded")[0].outerHTML).appendTo(this.element.find(".e-csHeader")),n(this.element.find(".e-outerTable").find("td")[0]).append(t.buildTag("div.e-toggleCollapseButton e-icon","",{}).attr("aria-label","toggle collapsed")[0].outerHTML),n(t.buildTag("div.e-toggleText")[0].outerHTML).insertAfter(this.element.find(".e-toggleCollapseButton")),this.element.find(".e-toggleCollapseButton").hide(),this.element.find(".e-toggleText").hide()),this._overflow(),this._setSplitBtnTitle(),this.element.find("#"+this._id+"_clientTab").ejTab({enableRTL:this.model.enableRTL,itemActive:t.proxy(this._onTabClick,this)}),this.model.operationalMode!=t.Pivot.OperationalMode.ClientMode){if(this.defaultView()==t.PivotClient.DefaultView.Chart?(this.element.find("#"+this._id+"_PivotChart").ejPivotChart({url:this.model.url,customObject:this.model.customObject,enableRTL:this.model.enableRTL,enableDefaultValue:this.model.enableDefaultValue,axesLabelRendering:this.model.axesLabelRendering,pointRegionClick:this.model.pointRegionClick,canResize:this.model.isResponsive,currentReport:this.currentReport,locale:this.locale(),showTooltip:!0,size:{height:this._chartHeight,width:this._chartWidth},commonSeriesOptions:{type:this.model.chartType,tooltip:{visible:!0}},beforeServiceInvoke:this.model.chartLoad,drillSuccess:t.proxy(this._chartDrillSuccess,this)}),this.gridLayout()!=t.PivotGrid.Layout.Normal?this.element.find("#"+this._id+"_PivotGrid").ejPivotGrid({url:this.model.url,customObject:this.model.customObject,isResponsive:this.model.isResponsive,enableRTL:this.model.enableRTL,enableDefaultValue:this.model.enableDefaultValue,enableCellSelection:this.model.enableCellSelection,cellSelection:this.model.cellSelection,hyperlinkSettings:{enableValueCellHyperlink:this.model.enableValueCellHyperlink,enableRowHeaderHyperlink:this.model.enableRowHeaderHyperlink,enableColumnHeaderHyperlink:this.model.enableColumnHeaderHyperlink,enableSummaryCellHyperlink:this.model.enableSummaryCellHyperlink},enableCellContext:this.model.enableCellContext,enableDrillThrough:this.model.enableDrillThrough,enableCellEditing:this.model.enableCellEditing,enableCellDoubleClick:this.model.enableCellDoubleClick,enableCellClick:this.model.enableCellClick,valueCellHyperlinkClick:this.model.valueCellHyperlinkClick,rowHeaderHyperlinkClick:this.model.rowHeaderHyperlinkClick,columnHeaderHyperlinkClick:this.model.columnHeaderHyperlinkClick,summaryCellHyperlinkClick:this.model.summaryCellHyperlinkClick,cellContext:this.model.cellContext,cellEdit:this.model.cellEdit,cellDoubleClick:this.model.cellDoubleClick,enableCellClick:this.model.enableCellClick,drillThrough:this.model.drillThrough,currentReport:this.currentReport,layout:this.gridLayout(),locale:this.locale(),drillSuccess:t.proxy(this._gridDrillSuccess,this)}):this.element.find("#"+this._id+"_PivotGrid").ejPivotGrid({url:this.model.url,customObject:this.model.customObject,isResponsive:this.model.isResponsive,enableRTL:this.model.enableRTL,enableDefaultValue:this.model.enableDefaultValue,enableCellSelection:this.model.enableCellSelection,cellSelection:this.model.cellSelection,hyperlinkSettings:{enableValueCellHyperlink:this.model.enableValueCellHyperlink,enableRowHeaderHyperlink:this.model.enableRowHeaderHyperlink,enableColumnHeaderHyperlink:this.model.enableColumnHeaderHyperlink,enableSummaryCellHyperlink:this.model.enableSummaryCellHyperlink},enableCellContext:this.model.enableCellContext,enableDrillThrough:this.model.enableDrillThrough,enableCellEditing:this.model.enableCellEditing,enableCellDoubleClick:this.model.enableCellDoubleClick,enableCellClick:this.model.enableCellClick,valueCellHyperlinkClick:this.model.valueCellHyperlinkClick,rowHeaderHyperlinkClick:this.model.rowHeaderHyperlinkClick,columnHeaderHyperlinkClick:this.model.columnHeaderHyperlinkClick,summaryCellHyperlinkClick:this.model.summaryCellHyperlinkClick,cellContext:this.model.cellContext,cellEdit:this.model.cellEdit,cellDoubleClick:this.model.cellDoubleClick,enableCellClick:this.model.enableCellClick,drillThrough:this.model.drillThrough,currentReport:this.currentReport,locale:this.locale(),drillSuccess:t.proxy(this._gridDrillSuccess,this)})):(this.gridLayout()!=t.PivotGrid.Layout.Normal?this.element.find("#"+this._id+"_PivotGrid").ejPivotGrid({url:this.model.url,customObject:this.model.customObject,isResponsive:this.model.isResponsive,enableRTL:this.model.enableRTL,enableDefaultValue:this.model.enableDefaultValue,enableCellSelection:this.model.enableCellSelection,cellSelection:this.model.cellSelection,hyperlinkSettings:{enableValueCellHyperlink:this.model.enableValueCellHyperlink,enableRowHeaderHyperlink:this.model.enableRowHeaderHyperlink,enableColumnHeaderHyperlink:this.model.enableColumnHeaderHyperlink,enableSummaryCellHyperlink:this.model.enableSummaryCellHyperlink},enableCellContext:this.model.enableCellContext,enableDrillThrough:this.model.enableDrillThrough,enableCellEditing:this.model.enableCellEditing,enableCellDoubleClick:this.model.enableCellDoubleClick,enableCellClick:this.model.enableCellClick,valueCellHyperlinkClick:this.model.valueCellHyperlinkClick,rowHeaderHyperlinkClick:this.model.rowHeaderHyperlinkClick,columnHeaderHyperlinkClick:this.model.columnHeaderHyperlinkClick,summaryCellHyperlinkClick:this.model.summaryCellHyperlinkClick,cellContext:this.model.cellContext,cellEdit:this.model.cellEdit,cellDoubleClick:this.model.cellDoubleClick,enableCellClick:this.model.enableCellClick,drillThrough:this.model.drillThrough,currentReport:this.currentReport,locale:this.locale(),layout:this.gridLayout(),drillSuccess:t.proxy(this._gridDrillSuccess,this)}):this.element.find("#"+this._id+"_PivotGrid").ejPivotGrid({url:this.model.url,customObject:this.model.customObject,isResponsive:this.model.isResponsive,enableRTL:this.model.enableRTL,enableDefaultValue:this.model.enableDefaultValue,enableCellSelection:this.model.enableCellSelection,cellSelection:this.model.cellSelection,hyperlinkSettings:{enableValueCellHyperlink:this.model.enableValueCellHyperlink,enableRowHeaderHyperlink:this.model.enableRowHeaderHyperlink,enableColumnHeaderHyperlink:this.model.enableColumnHeaderHyperlink,enableSummaryCellHyperlink:this.model.enableSummaryCellHyperlink},enableCellContext:this.model.enableCellContext,enableDrillThrough:this.model.enableDrillThrough,enableCellEditing:this.model.enableCellEditing,enableCellDoubleClick:this.model.enableCellDoubleClick,enableCellClick:this.model.enableCellClick,valueCellHyperlinkClick:this.model.valueCellHyperlinkClick,rowHeaderHyperlinkClick:this.model.rowHeaderHyperlinkClick,columnHeaderHyperlinkClick:this.model.columnHeaderHyperlinkClick,summaryCellHyperlinkClick:this.model.summaryCellHyperlinkClick,cellContext:this.model.cellContext,cellEdit:this.model.cellEdit,cellDoubleClick:this.model.cellDoubleClick,enableCellClick:this.model.enableCellClick,drillThrough:this.model.drillThrough,currentReport:this.currentReport,locale:this.locale(),drillSuccess:t.proxy(this._gridDrillSuccess,this)}),this.element.find("#"+this._id+"_PivotChart").ejPivotChart({url:this.model.url,customObject:this.model.customObject,enableRTL:this.model.enableRTL,enableDefaultValue:this.model.enableDefaultValue,axesLabelRendering:this.model.axesLabelRendering,pointRegionClick:this.model.pointRegionClick,canResize:this.model.isResponsive,currentReport:this.currentReport,locale:this.locale(),showTooltip:!0,size:{height:this._chartHeight,width:this._chartWidth},commonSeriesOptions:{type:this.model.chartType,tooltip:{visible:!0}},drillSuccess:t.proxy(this._chartDrillSuccess,this),beforeServiceInvoke:this.model.chartLoad})),this.model.enablePaging&&(this.element.find("#"+this._id+"_Pager").ejPivotPager({mode:t.PivotPager.Mode.Both,targetControlID:this._id}),this.element.find("#"+this._id+"_Pager").css("margin-top","5px"),this.element.find(".e-controlPanel").height(this.element.find(".e-controlPanel").height()-(this.element.find("#"+this._id+"_Pager").height()+5)),this.element.find(".e-gridContainer").height(this.element.find(".e-gridContainer").height()-(this.element.find("#"+this._id+"_Pager").height()+5)),this.element.find(".e-chartContainer").height(this.element.find(".e-chartContainer").height()-(this.element.find("#"+this._id+"_Pager").height()+10))),this.displayMode()!="chartonly"&&(this._pivotGrid=this.element.find("#"+this._id+"_PivotGrid").data("ejPivotGrid")),this.displayMode()!="gridOnly"&&(this._pivotChart=this.element.find("#"+this._id+"_PivotChart").data("ejPivotChart")),k={},this._pivotChart!=null&&(this.element.find("#"+this._id+"_PivotChart").width(this._pivotChart.model.size.width),this.element.find("#"+this._id+"_PivotChartContainer").width(this._pivotChart.model.size.width),k.chartModelWidth=this._pivotChart.model.size.width),k.controlPanelWidth=this.element.find(".e-controlPanel").width(),k.chartOuterWidth=this._chartWidth,k.gridOuterWidth=this._gridWidth,this._initStyles.push(k),this._wireEvents(),this.model.isResponsive&&(this._enableResponsive(),this._parentElwidth=n("#"+this._id).parent().width(),this._parentElwidth<850?this._rwdToggleCollapse():this._parentElwidth>850&&this._rwdToggleExpand(),this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()=="chartandgrid"&&this._currentTab=="chart"&&this.element.find(".e-chartContainer").css({width:"99.6%"})),this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode!=t.Pivot.OperationalMode.ClientMode){var d=this.element.find(".e-csHeader").width()-this.element.find(".cubeText").width()-this.element.find(".e-toggleExpandButton").width()-(this.model.enableSplitter?50:20),rt=this.enableTogglePanel()?d-25:d,et=v==""?"":n.parseJSON(v);this.element.find(".cubeSelector").ejDropDownList({width:this.model.enableSplitter&&!this.model.isResponsive?"100%":""+rt+"px"})}this._trigger("renderSuccess",this);this._successAction="ClientRender";this.progressPos=n("#"+this._id).position();this._treeContextMenu();this._buttonContextMenu();this.model.analysisMode=="olap"&&this.model.operationalMode=="servermode"&&this.model.enableSplitter&&this._createSplitter();t.isNullOrUndefined(r.Exception)||t.Pivot._createErrorDialog(r,"Exception",this);this.model.showUniqueNameOnPivotButton&&(n(".e-pvtBtn").addClass("e-splitBtnUnique"),this._addSplitButtonHeight())}this.model.collapseCubeBrowserByDefault&&(this._collapseCubeBrowser(),this._isCollapseCB=!0)},_collectionChange:function(n){var i,r;this._currentCollection=n.selectedText;this._waitingPopup.show();this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?(this.chartObj=this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),r=JSON.stringify(this.model.customObject),this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.loadReport,JSON.stringify({reportName:n.selectedText,operationalMode:this.model.operationalMode,analysisMode:this.model.analysisMode,olapReport:this.currentReport,clientReports:this.reports,customObject:r,clientParams:JSON.stringify(this.model.enableMeasureGroups)}),this.model.analysisMode==t.Pivot.AnalysisMode.Pivot?this._renderControlSuccess:this._toolbarOperationSuccess)):(i={url:"",reportCollection:this._clientReportCollection,selectedReport:n.selectedText,mode:this.model.analysisMode},this._trigger("loadReport",{targetControl:this,loadReportSetting:i}),this.model.enableLocalStorage?(this.model.dataSource=i.reportCollection[0],this._clientReportCollection=i.reportCollection,this.refreshControl(),this._refreshReportList(),this._pivotSchemaDesigner&&this._pivotSchemaDesigner._refreshPivotButtons()):(r=JSON.stringify(this.model.customObject),this.doAjaxPost("POST",i.url+"/"+this.model.serviceMethodSettings.loadReport,JSON.stringify({reportName:n.selectedText,operationalMode:this.model.operationalMode,analysisMode:this.model.analysisMode,olapReport:this.currentReport,clientReports:this.reports,customObject:r,clientParams:JSON.stringify(this.model.enableMeasureGroups)}),this._clientToolbarOperationSuccess)))},_contextOpen:function(i){if(this.element.find(".dragClone").length>0)return 0;t.Pivot.openPreventPanel(this);var r=n("#"+this._id+"_pivotTree").data("ejMenu");this._selectedMember=n(i.target);r.enableItem(this._getLocalizedLabels("AddToColumn"));r.enableItem(this._getLocalizedLabels("AddToRow"));r.enableItem(this._getLocalizedLabels("AddToSlicer"))},_contextClick:function(n){var u,f;t.Pivot.closePreventPanel(this);var i=n.events.text,r=this;if(i=i==this._getLocalizedLabels("AddToSlicer")?"Slicer":i==this._getLocalizedLabels("AddToColumn")?"Categorical":i==this._getLocalizedLabels("AddToRow")?"Series":"",i=="Slicer"&&this._selectedMember!=null&&this._selectedMember.parent().attr("data-tag").indexOf("Measure")>-1&&this.element.find(".e-splitBtn[data-tag*=Measures]").length>1)return t.Pivot._createErrorDialog(this._getLocalizedLabels("MultipleMeasure"),this._getLocalizedLabels("Warning"),this),!1;r._isTimeOut=!0;setTimeout(function(){r._isTimeOut&&r._waitingPopup.show()},800);this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:"nodeDropped",element:this.element,customObject:this.model.customObject});u=JSON.stringify(this.model.customObject);f=this.currentCubeName+"--"+this._selectedMember.parent().attr("data-tag")+"--"+i+"--";this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.nodeDropped,JSON.stringify({action:"nodeDropped",dropType:"SplitButton",nodeInfo:f,olapReport:this.currentReport,clientReports:this.reports,customObject:u}),this._nodeDroppedSuccess)},_onContextOpen:function(i){if(n(i.target).find(".e-folderCDB").length>0||n(i.target).find(".e-calcMemberGroupCDB").length>0)return!1;t.Pivot.openPreventPanel(this);var r=n("#"+this._id+"_pivotTreeContext").data("ejMenu");this._selectedMember=n(i.target);r.enableItem(this._getLocalizedLabels("AddToColumn"));r.enableItem(this._getLocalizedLabels("AddToRow"));n(i.target).find(".e-calcMemberCDB").length>0?r.showItems(["#"+this._id+"_Remove"]):r.hideItems(["#"+this._id+"_Remove"]);n(i.target.parentElement).find(".e-namedSetCDB").length>0?r.disableItem(this._getLocalizedLabels("AddToSlicer")):r.enableItem(this._getLocalizedLabels("AddToSlicer"))},_onTreeContextClick:function(i){var o,f,e;t.isNullOrUndefined(this._curFocus.tree)?t.isNullOrUndefined(this._curFocus.tab)||this._curFocus.tab.attr("tabindex","-1").focus().addClass("e-hoverCell"):this._curFocus.tree.attr("tabindex","-1").focus().addClass("e-hoverCell");delete this._fieldMembers[this._dimensionName.split(":").length>1?this._dimensionName.split(":")[1]:this._dialogTitle];delete this._fieldSelectedMembers[this._dimensionName.split(":").length>1?this._dimensionName.split(":")[1]:this._dialogTitle];t.Pivot.closePreventPanel(this);var u=i.events.text,r=this;if(r._isTimeOut=!0,u==this._getLocalizedLabels("Remove"))setTimeout(function(){r._isTimeOut&&r._waitingPopup.show()},800),o=this.element.find(".e-splitBtn"),jQuery.each(o,function(t,i){n(o[t]).attr("data-tag").split(":")[1]==r._selectedMember.parent().parent().attr("data-tag").replace(/\[/g,"").replace(/\]/g,"")&&n(i).remove()}),r._cubeTreeView.model.beforeDelete=null,r._cubeTreeView.removeNode(r._selectedMember.parent().parent().attr("id").replace(/'/g,"")),r._cubeTreeView.model.beforeDelete=function(){return!1},t.isNullOrUndefined(r._calcMemberTreeObj)||r._calcMemberTreeObj.removeNode(r._selectedMember.parent().parent().attr("id")),r.element.find(".e-cubeTreeView .e-calcMemberCDB").length==0&&r.element.find(".e-cubeTreeView .e-calcMemberGroupCDB").parents("li").length>0&&(r._cubeTreeView.model.beforeDelete=null,r._cubeTreeView.removeNode(r.element.find(".e-cubeTreeView .e-calcMemberGroupCDB").parents("li").attr("id").replace(/'/g,"")),r._cubeTreeView.model.beforeDelete=function(){return!1}),r.element.find(".e-cubeTreeViewCalcMember .e-calcMemberCDB").length==0&&r.element.find(".e-cubeTreeViewCalcMember .e-calcMemberGroupCDB").parents("li").length>0&&r._calcMemberTreeObj.removeNode(r.element.find(".e-cubeTreeViewCalcMember .e-calcMemberGroupCDB").parents("li").attr("id")),this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:"removeCalculatedMember",element:this.element,customObject:this.model.customObject}),f=JSON.stringify(this.model.customObject),e=this._selectedMember.parent().parent().attr("data-tag"),this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.removeSplitButton,JSON.stringify({action:"removeCalculatedMember",clientParams:e,olapReport:r.currentReport,clientReports:r.reports,customObject:f}),r._removeSplitButtonSuccess);else{if(u=u==r._getLocalizedLabels("AddToSlicer")?"Slicer":u==r._getLocalizedLabels("AddToColumn")?"Categorical":u==r._getLocalizedLabels("AddToRow")?"Series":"",u=="Slicer"&&this._selectedMember.parent().parent().attr("data-tag")!=null&&this._selectedMember.parent().parent().attr("data-tag").indexOf("Measure")>-1&&this.element.find(".e-splitBtn[data-tag*=Measures]").length>1||u=="Slicer"&&this._selectedMember.parent().parent().attr("data-tag").indexOf("Measure")>-1&&this._selectedMember.parent().parent().find(".e-calcMemberCDB").length>0&&this.element.find(".e-splitBtn[data-tag*=CalculatedMember][data-tag*=Measure]").length==1&&this._selectedMember.parent().parent().attr("data-tag").replace(/\[/g,"").replace(/\]/g,"")!=this.element.find(".e-splitBtn[data-tag*=CalculatedMember][data-tag*=Measure]").attr("data-tag").split(":")[1].split("::")[0]||u=="Slicer"&&this._selectedMember.parent().parent().attr("data-tag").indexOf("Measure")>-1&&this._selectedMember.parent().parent().find(".e-calcMemberCDB").length==0&&this.element.find(".e-splitBtn[data-tag*=CalculatedMember][data-tag*=Measure]").length==1||u=="Slicer"&&this._selectedMember.parent().parent().attr("data-tag").indexOf("Measure")>-1&&this._selectedMember.parent().parent().find(".e-calcMemberCDB").length==1&&this.element.find(".e-splitBtn[data-tag*=CalculatedMember][data-tag*=Measure]").length==0&&this.element.find(".e-splitBtn[data-tag*=Measure]").length==1)return t.Pivot._createErrorDialog(this._getLocalizedLabels("MultipleMeasure"),this._getLocalizedLabels("Warning"),this),!1;setTimeout(function(){r._isTimeOut&&r._waitingPopup.show()},800);this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:"nodeDropped",element:this.element,customObject:this.model.customObject});f=JSON.stringify(this.model.customObject);e=r.currentCubeName+"--"+this._selectedMember.parent().parent().attr("data-tag")+"--"+u+"--";r.doAjaxPost("POST",r.model.url+"/"+r.model.serviceMethodSettings.nodeDropped,JSON.stringify({action:"nodeDropped",dropType:"TreeNode",nodeInfo:e,olapReport:r.currentReport,clientReports:r.reports,customObject:f}),r._nodeDroppedSuccess)}},_toggleAxisSuccess:function(r){var u,f,e,o;t.Pivot._updateValueSortingIndex(r,this);this.model.analysisMode==t.Pivot.AnalysisMode.Pivot?(t.isNullOrUndefined(r.d)?this.setOlapReport(r.PivotReport):this.setOlapReport(n.grep(r.d,function(n){var t=n.Key;return t=="PivotReport"})[0].Value),this.refreshControl(r)):(r[0]!=i?(u=r[0].Value,f=r[1].Value,e=r[2].Value,this.currentReport=r[3].Value,this.reports=r[4].Value):r.d!=i?(u=r.d[0].Value,f=r.d[1].Value,e=r.d[2].Value,this.currentReport=r.d[3].Value,this.reports=r.d[4].Value):(u=r.Columns,f=r.Rows,e=r.Slicers,this.currentReport=r.CurrentReport,this.reports=r.ClientReports),this.model.OperationalMode==t.Pivot.AnalysisMode.ServerMode&&(this.element.find(".e-cubeTable").find(".e-categoricalAxis").parent().html("").html(this._createAxisElementBuilder(u,f,e)),this.model.enableSplitter&&!t.isNullOrUndefined(this.element.find(".e-serverchildsplit").data("ejSplitter"))&&this.element.find(".e-serverchildsplit > .e-splitbar").css("height",this.element.find(".e-cubeTable").height()),this.element.find(".e-cubeTable").height(this.element.height()-((this.element.find("div.e-titleText").length>0?this.element.find("div.e-titleText").height():0)+this.element.find("#"+this._id+"_reportToolbar").height()+this.element.find(".e-csHeader").height()+38)),this.element.find(".cdbTD, .cubeTableTD").height(this.element.find(".e-cubeTable").height()),o=this.element.find(".cdbTD, .cubeTableTD").height()-(this.element.find(".e-axisHeader").height()*3+(this.model.enableSplitter?this.model.isResponsive?18:24:this.model.isResponsive?14:3)),this.element.find(".e-categoricalAxis, .e-rowAxis, .e-slicerAxis").height(o/3)),this.element.find(".e-categoricalAxis, .e-rowAxis, .e-slicerAxis").find("button").ejButton({height:"20px",type:t.ButtonType.Button}),this.element.find(".e-categoricalAxis, .e-rowAxis, .e-slicerAxis").addClass("e-droppable"),this._renderControls(),this._setSplitBtnTitle(),this.model.showUniqueNameOnPivotButton&&(n(".e-pvtBtn").addClass("e-splitBtnUnique"),this._addSplitButtonHeight()),this._unWireEvents(),this._wireEvents(),this._trigger("renderSuccess",this),this._buttonContextMenu())},_measureGroupChangedSuccess:function(r){var o,r,e,f,u;for(r[0]!=i&&(o=r[0].Value),o=r.d!=i?r.d[0].Value:r.CubeTreeInfo,this.element.find(".e-treeview, .e-cubeTreeView").remove(),r=t.buildTag("div#"+this._id+"_cubeTreeView.e-cubeTreeView")[0].outerHTML,this.element.find(".e-cubeBrowser").append(r),e=n.parseJSON(o),this.element.find(".e-cubeTreeView").ejTreeView({clientObj:this,fields:{id:"id",parentId:"pid",text:"name",spriteCssClass:"spriteCssClass",dataSource:e,parentUniqueName:"parentUniqueName"},allowDragAndDrop:!0,allowDropChild:!1,allowDropSibling:!1,enableRTL:this.model.enableRTL,dragAndDropAcrossControl:!0,nodeDragStart:function(){this.model.clientObj.isDragging=!0},nodeDropped:t.proxy(this._nodeDropped,this),beforeDelete:function(){return!1},cssClass:"pivotTreeViewDragedNode",height:"464px"}),f=this.element.find(".e-cubeTreeView").find("li"),u=0;u<f.length;u++)!t.isNullOrUndefined(f[u].id)&&f[u].id.indexOf("'").length>-1&&f[u].setAttribute("id",f[u].id.replace(/'/g,"")),f[u].setAttribute("data-tag",e[u].tag),t.isNullOrUndefined(e[u].parentUniqueName)||e[u].parentUniqueName==""||f[u].setAttribute("data-parentUniqueName",t.isNullOrUndefined(e[u].parentUniqueName)?"":e[u].parentUniqueName.split(">>||>>")[1]);this.element.find(".e-cubeTreeView").height(this.element.find(".e-cubeBrowser").height()-this.element.find(".e-cubeName").height()-(7+(this.model.enableMeasureGroups?this.element.find(".measureGroupselector").height()+13:0)));this._cubeTreeView=this.element.find(".e-cubeTreeView").data("ejTreeView");this._unWireEvents();this._wireEvents();this._isTimeOut=!1;this._waitingPopup.hide();this._successAction="MeasureGroupChange"},_cubeChangedSuccess:function(r){var h,c,o,s,a,l,e,f,u;r[0]!=i?(this.currentReport=r[0].Value,h=r[1].Value,this.reports=r[2].Value,this.reportsCount=r[3].Value,o=n.parseJSON(r[4].Value),this.model.enableMeasureGroups&&(this.measureGroupInfo=n.parseJSON(r[5].Value)),c=n.parseJSON(r[6].Value),r[7]!=null&&r[7]!=i&&(this.model.customObject=r[7].Value)):r.d!=i?(this.currentReport=r.d[0].Value,h=r.d[1].Value,this.reports=r.d[2].Value,this.reportsCount=r.d[3].Value,o=n.parseJSON(r.d[4].Value),this.model.enableMeasureGroups&&(this.measureGroupInfo=n.parseJSON(r.d[5].Value)),c=n.parseJSON(r.d[6].Value),r.d[7]!=null&&r.d[7]!=i&&(s=r.d[7].Key,s!="Columns"&&(this.model.customObject=r.d[7].Value),s=="Columns"&&(r.Columns=r.d[7].Value),s=="Rows"&&(r.Rows=r.d[8].Value),s=="Slicers"&&(r.Slicers=r.d[9].Value))):(this.currentReport=r.NewReport,h=r.CubeTreeInfo,this.reports=r.ClientReports,this.reportsCount=r.ReportsCount,o=n.parseJSON(r.ReportList),this.model.enableMeasureGroups&&(this.measureGroupInfo=n.parseJSON(r.MeasureGroups)),c=n.parseJSON(r.ControlSettings),r.customObject!=null&&r.customObject!=i&&(this.model.customObject=r.customObject));a=this.currentCubeName;l=n.map(this._repCol,function(n){if(n.CubeName==a)return n.slicerBtnTextInfo});this.slicerBtnTextInfo=l.length>0?l[0]:{};this.model.chartType=c.ChartType.toLowerCase();this.model.afterServiceInvoke!=null&&this._trigger("afterServiceInvoke",{action:"cubeChanged",element:this.element,customObject:this.model.customObject});this.model.enableDeferUpdate?this.element.find(".e-splitBtn, .e-treeview, .e-cubeTreeView, .e-cubeName, .searchDiv").remove():this.element.find(".e-splitBtn, .e-pivotgrid, .e-pivotchart, .e-treeview, .e-cubeTreeView, .e-cubeName, .searchDiv").remove();var v=t.buildTag("div#"+this._id+"_cubeTreeView.e-cubeTreeView")[0].outerHTML,y=t.buildTag("div.searchDiv",t.buildTag("input#"+this._id+"_SearchTreeView.searchTreeView").attr("type","text")[0].outerHTML,{margin:"5px 5px 0px 5px"})[0].outerHTML,p=t.buildTag("div.e-cubeName",this.currentCubeName+y)[0].outerHTML;for(this.model.enableMeasureGroups&&this._createMeasureGroup(),e=n.parseJSON(h),n(v).appendTo(".e-cubeBrowser"),this.element.find(".e-cubeTreeView").ejTreeView({fields:{id:"id",parentId:"pid",text:"name",spriteCssClass:"spriteCssClass",dataSource:e,parentUniqueName:"parentUniqueName"},allowDragAndDrop:!0,allowDropChild:!1,allowDropSibling:!1,enableRTL:this.model.enableRTL,dragAndDropAcrossControl:!0,nodeDragStart:function(){var n=this.element.parents(".e-pivotclient").data("ejPivotClient");n.isDragging=!0},nodeDropped:t.proxy(this._nodeDropped,this),beforeDelete:function(){return!1},cssClass:"pivotTreeViewDragedNode",height:this.model.enableMeasureGroups?"464px":""}),f=this.element.find(".e-cubeTreeView").find("li"),u=0;u<f.length;u++)!t.isNullOrUndefined(f[u].id)&&f[u].id.indexOf("'").length>-1&&f[u].setAttribute("id",f[u].id.replace(/'/g,"")),f[u].setAttribute("data-tag",e[u].tag),t.isNullOrUndefined(e[u].parentUniqueName)||e[u].parentUniqueName==""||f[u].setAttribute("data-parentUniqueName",t.isNullOrUndefined(e[u].parentUniqueName)?"":e[u].parentUniqueName.split(">>||>>")[1]);this._cubeTreeView=this.element.find(".e-cubeTreeView").data("ejTreeView");this.element.find(".reportlist").ejDropDownList("option","change",t.proxy(this._reportChanged,this));this.element.find(".reportlist").ejDropDownList("option","dataSource",o);this.element.find(".reportlist").ejDropDownList("option","value",o[this._reportIndex].name);n(this.element).find(".e-cubeBrowser").prepend(p);this.element.find("#"+this._id+"_SearchTreeView").ejMaskEdit({name:"inputbox",width:"100%",inputMode:t.InputMode.Text,watermarkText:this._getLocalizedLabels("Search"),maskFormat:"",textAlign:this.model.enableRTL?"right":"left",change:t.proxy(t.Pivot._searchTreeNodes,this)});this.model.enableSplitter&&!t.isNullOrUndefined(this.element.find(".e-serverchildsplit").data("ejSplitter"))&&(this.element.find(".e-serverchildsplit > .e-splitbar").css("height",this.element.find(".e-cubeTable").height()),this.element.find(".e-serverchildsplit").data("ejSplitter").refresh());this.element.find(".e-cubeTable").height(this.element.height()-(this.element.find("div.e-titleText").height()+this.element.find("#"+this._id+"_reportToolbar").height()+this.element.find(".e-csHeader").height()+38));this.element.find(".cdbTD, .cubeTableTD").height(this.element.height()-(this.element.find("div.e-titleText").height()+this.element.find("#"+this._id+"_reportToolbar").height()+this.element.find(".e-csHeader").height()+38));this.element.find(".e-cubeBrowser").height(this.element.find(".cdbTD, .cubeTableTD").height()-(this.element.find(".e-cdbHeader").height()+5)+(this.model.isResponsive?0:14));n(this._createSplitButtons(r.Columns,"Columns")).appendTo(".e-categoricalAxis");t.isNullOrUndefined(r.Rows)?n(this._createSplitButtons("","Rows")).appendTo(".e-rowAxis"):n(this._createSplitButtons(r.Rows,"Rows")).appendTo(".e-rowAxis");t.isNullOrUndefined(r.Slicers)?n(this._createSplitButtons("","Slicers")).appendTo(".e-slicerAxis"):n(this._createSplitButtons(r.Slicers,"Slicers")).appendTo(".e-slicerAxis");this.element.find(".e-categoricalAxis, .e-rowAxis, .e-slicerAxis").find("button").ejButton({height:"20px",type:t.ButtonType.Button});this._setSplitBtnTitle();this._renderControls();this._unWireEvents();this._wireEvents();this._isTimeOut=!1;this._waitingPopup.hide();this._successAction="CubeChange";this._trigger("renderSuccess",this);this._treeContextMenu();this._buttonContextMenu();this.model.showUniqueNameOnPivotButton&&(this.element.find(".e-pvtBtn").addClass("e-splitBtnUnique"),this._addSplitButtonHeight());this.element.find(".e-cubeTreeView").height(this.element.find(".e-cubeBrowser").height()-this.element.find(".e-cubeName").height()-(7+(this.model.enableMeasureGroups?this.element.find(".measureGroupselector").height()+13:0)))},_treeContextMenu:function(){var i=t.buildTag("ul.pivotTreeContext#"+this._id+"_pivotTreeContext",t.buildTag("li",t.buildTag("a",this._getLocalizedLabels("AddToColumn"))[0].outerHTML)[0].outerHTML+t.buildTag("li",t.buildTag("a",this._getLocalizedLabels("AddToRow"))[0].outerHTML)[0].outerHTML+t.buildTag("li",t.buildTag("a",this._getLocalizedLabels("AddToSlicer"))[0].outerHTML)[0].outerHTML+t.buildTag("li#"+this._id+"_Remove",t.buildTag("a",this._getLocalizedLabels("Remove"))[0].outerHTML)[0].outerHTML)[0].outerHTML;n(this.element).append(i);n("#"+this._id+"_pivotTreeContext").ejMenu({menuType:t.MenuType.ContextMenu,openOnClick:!1,contextMenuTarget:this.element.find(".e-cubeTreeView")[0],click:t.proxy(this._onTreeContextClick,this),beforeOpen:t.proxy(this._onContextOpen,this),close:t.proxy(t.Pivot.closePreventPanel,this)})},_removeSplitButtonSuccess:function(n){n[0]!=i?(this.currentReport=n[0].Value,this.reports=n[1].Value,n[2]!=null&&n[2]!=i&&(this.model.customObject=n[2].Value)):n.d!=i?(this.currentReport=n.d[0].Value,this.reports=n.d[1].Value,n.d[2]!=null&&n.d[2]!=i&&(this.model.customObject=n.d[2].Value)):(this.currentReport=n.UpdatedReport,this.reports=n.ClientReports,n.customObject!=null&&n.customObject!=i&&(this.model.customObject=n.customObject));this.model.afterServiceInvoke!=null&&this._trigger("afterServiceInvoke",{action:"removeSplitButton",element:this.element,customObject:this.model.customObject});this._renderControls();this._unWireEvents();this._wireEvents();this._successAction="ButtonRemove";this._trigger("renderSuccess",this)},_chartTypeChangedSuccess:function(n){n[0]!=i?(this.currentReport=n[0].Value,this.reports=n[1].Value):n.d!=i?(this.currentReport=n.d[0].Value,this.reports=n.d[1].Value):(this.currentReport=n.CurrentReport,this.reports=n.ClientReports)},_mdxQuery:function(n){this.element.find(".e-dialog").hide();this.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog), .e-clientDialog").remove();n.d!=i?this._createDialog("mdx",n.d,""):this._createDialog("mdx",n,"")},_refreshReportList:function(){var t=n.map(this._clientReportCollection,function(n){if(n.reportName!=i)return{name:n.reportName}}),r=this.element.find(".reportlist").data("ejDropDownList");this.element.find(".reportlist").ejDropDownList("option","dataSource",t);r.selectItemByText(this.model.dataSource.reportName);this._unWireEvents();this._wireEvents()},_clientToolbarOperationSuccess:function(n){var r="",u;n!=null&&(u="",n&&n.d?r=n.d[0].Value:n&&n.report&&(r=n.report),r.indexOf(":>>:")>-1&&(this._currentReportItems=JSON.parse(r.split(":>>:")[1])),r=u=this._pivotSchemaDesigner._repCollection=JSON.parse(r.split(":>>:")[0]),this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&r[r.length-1].cubeIndex!=i?(u=r[r[r.length-1].cubeIndex].Reports,this.model.calculatedMembers=r[r[r.length-1].cubeIndex].calculatedMembers,this.reportDropTarget.selectItemByIndex(r[r[r.length-1].cubeIndex].ReportIndex)):this.reportDropTarget.selectItemByIndex(0),u.length>0&&(this.model.dataSource=u[0]),this._clientReportCollection=u,this.model.dataSource?(!t.isNullOrUndefined(this._pivotSchemaDesigner)&&this._pivotSchemaDesigner.element.find(".cubeList").length>0&&!t.isNullOrUndefined(u[r[r[r.length-1].cubeIndex].ReportIndex])&&!t.isNullOrUndefined(u[r[r[r.length-1].cubeIndex].ReportIndex].cube)&&(this._pivotSchemaDesigner.element.find(".cubeList").data("ejDropDownList").selectItemByText(u[r[r[r.length-1].cubeIndex].ReportIndex].cube),this._pivotSchemaDesigner._repCollection.splice(r.length-1,1)),this.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.refreshControl(this.model.dataSource),this._refreshReportList()):this._waitingPopup&&(this._isTimeOut=!1,this._waitingPopup.hide()))},_successDialog:function(n){var i=n=="Save"?this._getLocalizedLabels("SaveMsg"):n=="Rename"?this._getLocalizedLabels("RenameMsg"):n=="Remove"?this._getLocalizedLabels("RemoveMsg"):"";i!=""?t.Pivot._createErrorDialog(i,this._getLocalizedLabels("Success"),this):"";this._isTimeOut=!1},_toolbarOperationSuccess:function(r){var l,a,v,u,b,g,k,y,w,d,h,nt,o,s,rt,c,p,f,tt,ut,e,it;if(r!=null&&(r.CurrentAction!=i&&r.CurrentAction!="Load Report"&&r.CurrentAction!="Add Report"&&r.CurrentAction!="Remove Report"&&r.CurrentAction!="New Report"&&r.CurrentAction!="Rename Report"&&r.CurrentAction!="Report Change"?this._successDialog(r.CurrentAction):r.d!=i&&r.d[0]!=i&&(w=r.d[0].Key,w=="CurrentAction"&&r.d[0].Value!=i&&r.d[0].Value!="Load Report"&&r.d[0].Value!="Add Report"&&r.d[0].Value!="Remove Report"&&r.d[0].Value!="New Report"&&r.d[0].Value!="Rename Report"&&r.d[0].Value!="Report Change"&&(this._successDialog(r.d[0].Value),r.d=null)),r.d!=i&&r.d||r[0]!=i&&r[0]||r.CurrentReport||r.PivotReport)){if(r.length>1&&r[0]!=i)this.currentReport=r[0].Value,this.reports=r[1].Value,this.reportsCount=r[2].Value,l=r[3].Value,a=r[4].Value,v=r[5].Value,u=r[6].Value,h=n.parseJSON(r[7].Value),b=r[8].Value,g=n.parseJSON(r[9].Value),k=r[10].Value,y=r[11].Value,this.model.enableMeasureGroups&&r[12]!=null&&r[12]!=i&&(this.measureGroupInfo=n.parseJSON(r[12].Value)),r[13]!=null&&r[13]!=i&&(w=r[13].Key,w!="Collection"?this.model.customObject=r[13].Value:w=="Collection"&&(this._repCol=JSON.parse(r[13].Value),t.isNullOrUndefined(this._repCol[this._repCol.length-1].cubeIndex)||(o=this._repCol[this._repCol[this._repCol.length-1].cubeIndex].slicerBtnTextInfo,s=this._repCol[this._repCol[this._repCol.length-1].cubeIndex]._fieldSelectedMembers,this._slicerBtnTextInfo=t.isNullOrUndefined(o)?{}:o,this._fieldSelectedMembers=t.isNullOrUndefined(s)?{}:s)));else if(r.d!=i){if(this.model.analysisMode==t.Pivot.AnalysisMode.Pivot){u=n.grep(r.d,function(n){var t=n.Key;return t=="CurrentAction"})[0].Value;this.setOlapReport(n.grep(r.d,function(n){var t=n.Key;return t=="PivotReport"})[0].Value);this.currentReport=n.parseJSON(this.getOlapReport()).Report;l=n.parseJSON(this.getOlapReport()).PivotColumns;a=n.parseJSON(this.getOlapReport()).PivotRows;v=n.parseJSON(this.getOlapReport()).Filters;u=="NewReport"?(this._clientReportCollection=[{name:this._currentReportName,report:this.currentReport}],this.refreshControl({GridJSON:JSON.stringify(""),ChartJSON:JSON.stringify(""),PivotReport:n.grep(r.d,function(n){var t=n.Key;return t=="PivotReport"})[0].Value})):u=="AddReport"?(this._clientReportCollection.push({name:this._currentReportName,report:this.currentReport}),this.refreshControl({GridJSON:JSON.stringify(""),ChartJSON:JSON.stringify(""),PivotReport:n.grep(r.d,function(n){var t=n.Key;return t=="PivotReport"})[0].Value})):(u=="RemoveReport"||u=="ChangeReport")&&(d=this._hiddenCellInfo(r.d),this.refreshControl({GridJSON:n.grep(r.d,function(n){var t=n.Key;return t=="GridJSON"})[0].Value,ChartJSON:n.grep(r.d,function(n){var t=n.Key;return t=="ChartJSON"})[0].Value,PivotReport:n.grep(r.d,function(n){var t=n.Key;return t=="PivotReport"})[0].Value,FilteredColumnHeaders:d.columnArea.length>0?d.columnArea:"[]",FilteredRowHeaders:d.rowArea.length>0?d.rowArea:"[]"}));h=n.map(this._clientReportCollection,function(n){return{name:n.name}});this.element.find(".reportlist").ejDropDownList("option","dataSource",h);nt=this.element.find(".reportlist").data("ejDropDownList");this._isReportListAction=!1;nt.selectItemByText(this._currentReportName);this._isReportListAction=!0;return}this.currentReport=r.d[0].Value;this.reports=r.d[1].Value;this.reportsCount=r.d[2].Value;l=r.d[3].Value;a=r.d[4].Value;v=r.d[5].Value;u=r.d[6].Value;h=n.parseJSON(r.d[7].Value);b=r.d[8].Value;g=n.parseJSON(r.d[9].Value);k=r.d[10].Value;y=r.d[11].Value;this.model.enableMeasureGroups&&r.d[12]!=null&&r.d[12]!=i&&(this.measureGroupInfo=n.parseJSON(r.d[12].Value));r.d[13]!=null&&r.d[13]!=i&&r.d[13].Key!="Collection"?this.model.customObject=r.d[13].Value:r.d[13]!=null&&r.d[13]!=i&&r.d[13].Key=="Collection"&&(this._repCol=JSON.parse(r.d[13].Value),t.isNullOrUndefined(this._repCol[this._repCol.length-1].cubeIndex)||(o=this._repCol[this._repCol[this._repCol.length-1].cubeIndex].slicerBtnTextInfo,this._slicerBtnTextInfo=t.isNullOrUndefined(o)?{}:o,s=this._repCol[this._repCol[this._repCol.length-1].cubeIndex]._fieldSelectedMembers,this._fieldSelectedMembers=t.isNullOrUndefined(s)?{}:s))}else{if(this.model.analysisMode==t.Pivot.AnalysisMode.Pivot){u=r.CurrentAction;this.setOlapReport(r.PivotReport);this.currentReport=n.parseJSON(this.getOlapReport()).Report;l=n.parseJSON(this.getOlapReport()).PivotColumns;a=n.parseJSON(this.getOlapReport()).PivotRows;v=n.parseJSON(this.getOlapReport()).Filters;u=="NewReport"?(this._clientReportCollection=[{name:this._currentReportName,report:this.currentReport}],this.refreshControl({GridJSON:JSON.stringify(""),ChartJSON:JSON.stringify(""),PivotReport:r.PivotReport})):u=="AddReport"?(this._clientReportCollection.push({name:this._currentReportName,report:this.currentReport}),this.refreshControl({GridJSON:JSON.stringify(""),ChartJSON:JSON.stringify(""),PivotReport:r.PivotReport})):(u=="RemoveReport"||u=="ChangeReport")&&this.refreshControl({GridJSON:r.GridJSON,ChartJSON:r.ChartJSON,PivotReport:r.PivotReport,FilteredColumnHeaders:t.isNullOrUndefined(r.FilteredColumnHeaders)?"[]":r.FilteredColumnHeaders,FilteredRowHeaders:t.isNullOrUndefined(r.FilteredRowHeaders)?"[]":r.FilteredRowHeaders});h=n.map(this._clientReportCollection,function(n){return{name:n.name}});this.element.find(".reportlist").ejDropDownList("option","dataSource",h);nt=this.element.find(".reportlist").data("ejDropDownList");this._isReportListAction=!1;nt.selectItemByText(this._currentReportName);this._isReportListAction=!0;return}this._repCol=t.isNullOrUndefined(r.Collection)?this._repCol:JSON.parse(r.Collection);this.currentReport=r.CurrentReport;this.reports=r.Reports;this.reportsCount=r.ReportsCount;l=r.Columns;a=r.Rows;v=r.Slicers;u=r.CurrentAction;h=n.parseJSON(r.ReportList);b=r.RenamedReport;g=n.parseJSON(r.ControlSettings);k=r.CubeTreeInfo;y=r.CubeSelector;this.model.enableMeasureGroups&&(this.measureGroupInfo=n.parseJSON(r.MeasureGroups));r.customObject!=null&&r.customObject!=i&&(this.model.customObject=r.customObject);this._repCol!=i&&this._repCol.length>0&&!t.isNullOrUndefined(this._repCol[this._repCol.length-1].cubeIndex)&&(o=this._repCol[this._repCol[this._repCol.length-1].cubeIndex].slicerBtnTextInfo,this._slicerBtnTextInfo=t.isNullOrUndefined(o)?{}:o,s=this._repCol[this._repCol[this._repCol.length-1].cubeIndex]._fieldSelectedMembers,this._fieldSelectedMembers=t.isNullOrUndefined(s)?{}:s)}if(this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(this.model.chartType=g.ChartType.toLowerCase()),this.model.afterServiceInvoke!=null&&this._trigger("afterServiceInvoke",{action:"toolbarOperation",element:this.element,customObject:this.model.customObject}),u!="Report Change"){if(this.element.find(".reportlist").ejDropDownList("option","change",""),this.element.find(".reportlist").ejDropDownList("option","dataSource",h),u=="Load Report"){for(this.ddlTarget._initValue=!0,this.ddlTarget.selectItemByText(y),this.currentCubeName=y,this.ddlTarget._initValue=!1,t.isNullOrUndefined(r.Collection)&&(r.d==i||r.d[13]==i||r.d[13].Key!="Collection")?this.reportDropTarget.selectItemByText(this.reportDropTarget.model.dataSource[0].name):this.reportDropTarget.selectItemByIndex(this._repCol[this._repCol[this._repCol.length-1].cubeIndex].ReportIndex),this._repCol.splice(this._repCol.length-1,1),this._selectedReport=this.reportDropTarget.currentValue,this.element.find(".searchDiv").remove(),this.model.enableMeasureGroups&&this._createMeasureGroup(),rt=t.buildTag("div.searchDiv",t.buildTag("input#"+this._id+"_SearchTreeView.searchTreeView").attr("type","text")[0].outerHTML,{margin:"5px 5px 0px 0px"})[0].outerHTML,tt=n(this.element).find(".e-cubeName").html(y+rt),n(this.element).find(".e-cubeBrowser").prepend(tt),this.element.find("#"+this._id+"_SearchTreeView").ejMaskEdit({name:"inputbox",width:"100%",inputMode:t.InputMode.Text,watermarkText:this._getLocalizedLabels("Search"),maskFormat:"",textAlign:this.model.enableRTL?"right":"left",change:t.proxy(t.Pivot._searchTreeNodes,this)}),e=n.parseJSON(k),this.element.find(".e-cubeTreeView").ejTreeView({clientObj:this,fields:{id:"id",parentId:"pid",text:"name",spriteCssClass:"spriteCssClass",dataSource:e,parentUniqueName:"parentUniqueName"},allowDragAndDrop:!0,allowDropChild:!1,allowDropSibling:!1,enableRTL:this.model.enableRTL,dragAndDropAcrossControl:!0,nodeDragStart:function(){this.model.clientObj.isDragging=!0},nodeDropped:t.proxy(this._nodeDropped,this),beforeDelete:function(){return!1},cssClass:"pivotTreeViewDragedNode",height:this.model.enableMeasureGroups?"464px":"495px"}),c=this.element.find(".e-cubeTreeView").find("li"),f=0;f<c.length;f++)!t.isNullOrUndefined(c[f].id)&&c[f].id.indexOf("'").length>-1&&c[f].setAttribute("id",c[f].id.replace(/'/g,"")),c[f].setAttribute("data-tag",e[f].tag),t.isNullOrUndefined(e[f].parentUniqueName)||e[f].parentUniqueName==""||c[f].setAttribute("data-parentUniqueName",t.isNullOrUndefined(e[f].parentUniqueName)?"":e[f].parentUniqueName.split(">>||>>")[1]);for(p=this.element.find(".e-cubeTreeView .e-folderCDB"),f=0;f<p.length;f++)n(p[f].parentElement).removeClass("e-draggable");for(p=this.element.find(".e-cubeTreeViewCalcMember .e-folderCDB"),f=0;f<p.length;f++)n(p[f].parentElement).removeClass("e-draggable");this._cubeTreeView=this.element.find(".e-cubeTreeView").data("ejTreeView");this.model.isResponsive?this.element.find(".e-cubeTreeView").height(this.element.find(".e-cubeBrowser").height()-(56+(this.model.enableMeasureGroups?35:0))):this.element.find(".e-cubeTreeView").height(this.element.find(".e-cubeBrowser").height()-(61+(this.model.enableMeasureGroups?30:0)))}else u!="Rename Report"?(this._isReportListAction=!1,this.reportDropTarget.selectItemByText(this.reportDropTarget.model.dataSource[h.length-1].name),this._isReportListAction=!0,this._selectedReport=this.reportDropTarget.currentValue):(this._excelFilterInfo.length>0&&(tt=this.element.find(".cubeSelector").data("ejDropDownList").model.value,ut=this.element.find("#"+this._id+"_reportList").data("ejDropDownList").model.value,this._excelFilterInfo=n.grep(this._excelFilterInfo,function(n){return n.cubeName==tt&&n.report==this.reportDropTarget.model.itemValue&&(n.report=b),n})),this._isReportListAction=!1,this.reportDropTarget.selectItemByText(b),this._isReportListAction=!0,this._selectedReport=this.reportDropTarget.currentValue);this.element.find(".reportlist").ejDropDownList("option","change",t.proxy(this._reportChanged,this))}u!="Rename Report"&&(this.model.enableDeferUpdate?this.element.find(".e-splitBtn").remove():this.element.find(".e-splitBtn, .e-pivotgrid, .e-pivotchart").remove());(u=="Add Report"||u=="New Report"||u=="Remove Report"||u=="Report Change"||u=="Load Report"||u=="SortOrFilter"||u=="Save Report")&&(e=n.parseJSON(k),it=t.DataManager(e).executeLocal(t.Query().where("spriteCssClass","contains","e-calcMemberGroupCDB")),this._cubeTreeView.model.beforeDelete=null,this._cubeTreeView.removeNode(this.element.find(".e-cubeTreeView .e-calcMemberGroupCDB").parents("li")),this._cubeTreeView.model.beforeDelete=function(){return!1},t.isNullOrUndefined(this._calcMemberTreeObj)||this._calcMemberTreeObj.removeNode(this.element.find(".e-cubeTreeViewCalcMember .e-calcMemberGroupCDB").parents("li")),it.length>0&&!t.isNullOrUndefined(this._cubeTreeView)&&(this._calcMembers=[],this._calcMembers.push(it),n.merge(this._calcMembers,t.DataManager(e).executeLocal(t.Query().where("spriteCssClass","contains","e-calcMemberCDB"))),this._calcMemberTreeViewUpdate()));(u=="Remove Report"||u=="Report Change"||u=="Load Report"||u=="SortOrFilter"||u=="Save Report")&&(n(this._createSplitButtons(l,"Columns")).appendTo(".e-categoricalAxis"),n(this._createSplitButtons(a,"Rows")).appendTo(".e-rowAxis"),n(this._createSplitButtons(v,"Slicers")).appendTo(".e-slicerAxis"),this.element.find(".e-categoricalAxis, .e-rowAxis, .e-slicerAxis").find("button").ejButton({height:"20px",type:t.ButtonType.Button}),this._renderControls());(u=="Add Report"||u=="New Report")&&this._renderControls()}this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&(this._treeContextMenu(),this._buttonContextMenu());this._setSplitBtnTitle();this.model.showUniqueNameOnPivotButton&&(n(".e-pvtBtn").addClass("e-splitBtnUnique"),this._addSplitButtonHeight());this._unWireEvents();this._wireEvents();this._isTimeOut=!1;this._waitingPopup.hide();this._successAction="ToolbarOperation";this._trigger("renderSuccess",this)},_calcMemberTreeViewUpdate:function(){var i,r,n;if(this._cubeTreeView.model.beforeDelete=null,this._cubeTreeView.removeNode(this.element.find(".e-cubeTreeView .e-calcMemberGroupCDB").parents("li")),this._cubeTreeView.model.beforeDelete=function(){return!1},t.isNullOrUndefined(this._calcMemberTreeObj)||this._calcMemberTreeObj.removeNode(this.element.find(".e-cubeTreeViewCalcMember .e-calcMemberGroupCDB").parents("li")),this.element.find(".e-cubeTreeView").find("li").first().length>0){for(this._cubeTreeView.insertBefore(this._calcMembers[0],this.element.find(".e-cubeTreeView").find("li").first()),n=0;n<this._calcMembers.length;n++)!t.isNullOrUndefined(this._calcMembers[n].id)&&this._calcMembers[n].id.indexOf("'")>-1&&(this._calcMembers[n].id=this._calcMembers[n].id.replace(/'/g,""));this._cubeTreeView.addNodes(t.DataManager(this._calcMembers).executeLocal(t.Query().skip(1)),this._calcMembers[0].id);this.element.find(".e-cubeTreeView").find("#"+this._calcMembers[0].id).find("> div > div:first").removeClass("e-process");this._cubeTreeView.collapseNode(this._calcMembers[0].id)}else this._calcMembers.length>0&&(this._cubeTreeView.addNodes(this._calcMembers[0]),this.element.find(".e-cubeTreeView").find("#"+this._calcMembers[0].id).find("> div > div:first").removeClass("e-process"),this._cubeTreeView.addNodes(t.DataManager(this._calcMembers).executeLocal(t.Query().skip(1)),this._calcMembers[0].id));if(!t.isNullOrUndefined(this._calcMemberTreeObj)&&this.element.find(".e-cubeTreeViewCalcMember").find("li").first().length>0){for(this._calcMemberTreeObj.insertBefore(this._calcMembers[0],this.element.find(".e-cubeTreeViewCalcMember").find("li").first()),n=0;n<this._calcMembers.length;n++)!t.isNullOrUndefined(this._calcMembers[n].id)&&this._calcMembers[n].id.indexOf("'")>-1&&(this._calcMembers[n].id=this._calcMembers[n].id.replace(/'/g,""));this._calcMemberTreeObj.addNodes(t.DataManager(this._calcMembers).executeLocal(t.Query().skip(1)),this._calcMembers[0].id);this.element.find(".e-cubeTreeViewCalcMember").find("#"+this._calcMembers[0].id).find("> div > div:first").removeClass("e-process");this._calcMemberTreeObj.collapseNode(this._calcMembers[0].id)}else!t.isNullOrUndefined(this._calcMemberTreeObj)&&this._calcMembers.length>0&&(this._calcMemberTreeObj.addNodes(this._calcMembers[0]),this.element.find(".e-cubeTreeViewCalcMember").find("#"+this._calcMembers[0].id).find("> div > div:first").removeClass("e-process"),this._calcMemberTreeObj.addNodes(t.DataManager(this._calcMembers).executeLocal(t.Query().skip(1)),this._calcMembers[0].id));if(this.element.find(".e-cubeTreeView .e-calcMemberGroupCDB").length>0&&(this.element.find(".e-cubeTreeView .e-calcMemberGroupCDB").parent().removeClass("e-draggable"),this.element.find(".e-cubeTreeViewCalcMember .e-calcMemberGroupCDB").parent().removeClass("e-draggable"),i=this.element.find(".e-cubeTreeView .e-calcMemberCDB").parents("li").find("li"),r=this.element.find(".e-cubeTreeViewCalcMember .e-calcMemberCDB").parents("li").find("li"),i.length>0))for(n=0;n<i.length;n++)i[n].setAttribute("data-tag",this._calcMembers[n+1].tag),i[n].setAttribute("expression",this._calcMembers[n+1].expression),i[n].setAttribute("formatString",this._calcMembers[n+1].formatString),i[n].setAttribute("nodeType",this._calcMembers[n+1].nodeType),r.length>0&&(r[n].setAttribute("data-tag",this._calcMembers[n+1].tag),r[n].setAttribute("expression",this._calcMembers[n+1].expression),r[n].setAttribute("formatString",this._calcMembers[n+1].formatString),r[n].setAttribute("nodeType",this._calcMembers[n+1].nodeType))},_fetchChildNodeSuccess:function(r){var u,f,h,e,o,s;u=r.length>1&&r[0]!=i?JSON.parse(r[0].Value):r.d!=i?JSON.parse(r.d[0].Value):JSON.parse(r.ChildNodes);r[0]!=i?r[2]!=null&&r[2]!=i&&(this.model.customObject=r[2].Value):r.d!=i?r.d[2]!=null&&r.d[2]!=i&&(this.model.customObject=r.d[2].Value):r.customObject!=null&&r.customObject!=i&&(this.model.customObject=r.customObject);this.model.afterServiceInvoke!=null&&this._trigger("afterServiceInvoke",{action:"memberExpanded",element:this.element,customObject:this.model.customObject});h={id:"id",parentId:"pid",hasChild:"hasChildren",text:"name",isChecked:"checkedStatus"};f=n(this.pNode).parents("li").length>1?n(this.pNode).parents("li").first():n(this.pNode).parents("li");n(n(f).find("input.nodecheckbox")[0]).ejCheckBox({checked:!1});this.model.enableMemberEditorSorting&&(u=t.DataManager(u).executeLocal(t.Query().sortBy("name",this._sortType)));!t.DataManager(this._editorTreeData).executeLocal(t.Query().where("pid","equal",u[0].pid)).length>0&&n.merge(this._editorTreeData,n.extend(!0,[],u));this.element.find(".nodeExpand").remove();this.model.enableMemberEditorPaging?(e=t.Pivot._generateChildWithAncestors(this,f,this.model.enableMemberEditorPaging,this.model.memberEditorPageSize),u.length>=this.model.memberEditorPageSize||e.lstChildren.length>=this.model.memberEditorPageSize?(this.element.find(".searchEditorTreeView").data("ejMaskEdit").clear(),this._lastSavedTree=[],t.Pivot._makeAncestorsExpandable(this,f[0].id),this._isEditorDrillPaging=!0,o=e.allLvlLst.length>1&&e.lstChildren.length>=this.model.memberEditorPageSize?t.Pivot._getParentsTreeList(this,e.lstParents[0].id,this._editorTreeData):n.grep(this._editorTreeData,function(n){return n.id==f["0"].id})[0],s={childNodes:e.allLvlLst.length>1&&e.lstChildren.length>=this.model.memberEditorPageSize?e.lstChildren:u,parentNode:o},t.Pivot._drillEditorTreeNode(s,this,this.model.memberEditorPageSize)):this._memberTreeObj.addNode(t.Pivot._showEditorLinkPanel(u,this,this),f)):this._memberTreeObj.addNode(t.Pivot._showEditorLinkPanel(u,this,this),f);n.each(n(f).children().find("li"),function(n,i){t.isNullOrUndefined(u[n])||i.setAttribute("data-tag",u[n].tag)});this._successAction="FetchChildNode";this._trigger("renderSuccess",this);this.element.find(".e-dialog #preventDiv").remove()},_createCubeSelector:function(){return t.buildTag("div.e-csHeader",t.buildTag("span.cubeText",this._getLocalizedLabels("CubeSelector"),{"padding-left":"6px",float:"left","margin-bottom":"10px",width:this.model.enableSplitter&&!this.model.isResponsive?"25%":"","text-overflow":"ellipsis"})[0].outerHTML+t.buildTag("div.cubeSelect","<input type='text' id='"+this._id+"_cubeSelector' class ='cubeSelector' />",{display:"inline",float:"left","padding-left":this.model.enableSplitter?"1%":"8px",position:"relative",top:"-3px",width:this.model.enableSplitter&&!this.model.isResponsive?"70.5%":""})[0].outerHTML,{width:this.model.isResponsive||this.model.enableSplitter?"100%":"",height:"20px"})[0].outerHTML},_createCubeBrowser:function(){return t.buildTag("div.e-cdbHeader",t.buildTag("span",this._getLocalizedLabels("CubeDimensionBrowser"))[0].outerHTML,{width:this.model.enableSplitter?"100%":"",height:"30px"})[0].outerHTML+t.buildTag("div.e-cubeBrowser",t.buildTag("div.e-cubeTreeView.visibleHide")[0].outerHTML,{width:this.model.enableSplitter?"100%":""})[0].outerHTML},_createAxisElementBuilder:function(n,i,r){var u;return this.model.enableSplitter?t.buildTag("div.e-axisHeader",t.buildTag("span",this._getLocalizedLabels("Column"))[0].outerHTML,{height:"30px",width:"99%"})[0].outerHTML+t.buildTag("div.e-categoricalAxis",this._createSplitButtons(n,"Columns"),{width:this.model.isResponsive||this.model.enableSplitter?"99%":""}).attr("aria-label","column")[0].outerHTML+t.buildTag("div.e-axisHeader",t.buildTag("span",this._getLocalizedLabels("Row"))[0].outerHTML,{height:"30px",width:"99%"})[0].outerHTML+t.buildTag("div.e-rowAxis",this._createSplitButtons(i,"Rows"),{width:this.model.isResponsive||this.model.enableSplitter?"99%":""}).attr("aria-label","row")[0].outerHTML+t.buildTag("div.e-axisHeader",t.buildTag("span",this._getLocalizedLabels("Slicer"))[0].outerHTML,{height:"30px",width:"99%"})[0].outerHTML+t.buildTag("div.e-slicerAxis",this._createSplitButtons(r,"Slicers"),{width:this.model.isResponsive||this.model.enableSplitter?"99%":""}).attr("aria-label","slicer")[0].outerHTML:t.buildTag("div.e-axisHeader",t.buildTag("span",this._getLocalizedLabels("Column"))[0].outerHTML,{height:"30px"})[0].outerHTML+t.buildTag("div.e-categoricalAxis",this._createSplitButtons(n,"Columns"),{width:this.model.isResponsive?"99%":""}).attr("aria-label","column")[0].outerHTML+t.buildTag("div.e-axisHeader",t.buildTag("span",this._getLocalizedLabels("Row"))[0].outerHTML,{height:"30px"})[0].outerHTML+t.buildTag("div.e-rowAxis",this._createSplitButtons(i,"Rows"),{width:this.model.isResponsive?"99%":""}).attr("aria-label","row")[0].outerHTML+t.buildTag("div.e-axisHeader",t.buildTag("span",this._getLocalizedLabels("Slicer"))[0].outerHTML,{height:"30px"})[0].outerHTML+t.buildTag("div.e-slicerAxis",this._createSplitButtons(r,"Slicers"),{width:this.model.isResponsive?"99%":""}).attr("aria-label","slicer")[0].outerHTML},_createSplitButtons:function(n,r){var s="",f="",u,o,e,c,h;if(n.indexOf("~")>-1)for(u=0;u<n.split("~")[0].split("#").length-1;u++)f=n.split("~")[0].split("#")[u+1].split(".")[0],f=="Measures"?f=this._getLocalizedLabels("Measures"):f=="KPIs"?f=this._getLocalizedLabels("KPIs"):n.startsWith("#Measure")?n.indexOf("#Measure")>-1&&(f=n.split("~")[u+1]):f=n.split("~")[u+2],f==i&&(f=n.split("~")[u+1]),s+=t.buildTag("div.e-splitBtn",t.buildTag("button.e-pvtBtn",f).attr({title:n.split("~")[0].split("#")[u+1].replace("."," - ").split("$")[0],fieldCaption:f,allMember:!t.isNullOrUndefined(n.split("~")[0].split("#")[u+1].replace("."," - ").split("$")[1])&&n.split("~")[0].split("#")[u+1].replace("."," - ").split("$")[1].toLowerCase().trim()=="multiple items"?this._getLocalizedLabels("MultipleItems"):n.split("~")[0].split("#")[u+1].replace("."," - ").split("$")[1]})[0].outerHTML+t.buildTag("span.e-removeSplitBtn e-icon").attr("role","button").attr("aria-label","remove")[0].outerHTML,"",{"data-tag":r+":"+n.split("~")[0].split("#")[u+1].split("$")[0]})[0].outerHTML;else for(u=0;u<n.split("#").length-1;u++)h=n.split("#")[u+1].split("$")[0],n.split("#")[u+1].split("::")[1]=="CalculatedMember"?(e=n.split("#")[u+1].split("::")[0],o=e.split(".")[e.split(".").length-1],e=e.replace("."," - ")):(o=n.split("#")[u+1].split(".")[0]=="Measures"?this._getLocalizedLabels("Measures"):n.split("#")[u+1].split(".")[0],o.split("::")[1]!=i&&o.split("::")[1]!=null&&o.split("::")[1].split(">>")[0]=="NAMEDSET"&&(c=n.split("#")[u+1].split("::")[1].split(">>")[1],o=o.split("::")[0],h=n.split("#")[u+1].split("$")[0].split(">>")[0]),e=n.split("#")[u+1].replace("."," - ").split("$")[0]=="Measures"?this._getLocalizedLabels("Measures"):n.split("#")[u+1].replace("."," - ").split("$")[0]),this.model.enableRTL&&e.indexOf("-")>0&&(e=e.split("-").reverse().join(" - ")),s+=t.buildTag("div.e-splitBtn",t.buildTag("button.e-pvtBtn",this.model.showUniqueNameOnPivotButton?e:o).attr({title:e,fieldCaption:o,allMember:!t.isNullOrUndefined(n.split("#")[u+1].split("$")[1])&&n.split("#")[u+1].split("$")[1].toLowerCase().trim()=="multiple items"?this._getLocalizedLabels("MultipleItems"):n.split("#")[u+1].split("$")[1]})[0].outerHTML+t.buildTag("span.e-removeSplitBtn e-icon").attr("role","button").attr("aria-label","remove")[0].outerHTML,"",{"data-parenthierarchy":c,"data-tag":r+":"+h})[0].outerHTML;return s},_setSplitBtnTitle:function(){for(var f,u,o,e,h,c,i=this.element.find(".e-splitBtn"),t=0;t<i.length;t++){var s=n(i[t]).attr("data-tag").split(":")[1].split("."),r="";for(f=0;f<s.length;f++)r+=s[0].toUpperCase()!="[Measures]"?"["+s[f]+"]"+(f%2==0?".":""):s[f]+(f%2==0?".":"");this.element.find(".e-cubeTreeView").length>0&&!(r.indexOf("Measures")>=0)&&(r=r.indexOf("<>")>0?r.replace("<>","."):r,r.indexOf("'")>0&&(r=r.replace(/'/g,"~@")),u="",o=n(i[t]).find("button").attr("allMember"),o=o!="All"?o:this._getLocalizedLabels("All"),e=this.element.find(".e-cubeTreeView").find("li[data-tag='"+r+"'] a:eq(0)").text(),e==""&&(e=this.element.find(".e-cubeTreeView").find("li[data-tag='["+s[1]+"]'] a:eq(0)").text()),e.indexOf(".")>0?n(i[t]).find("button").attr("title",this.model.enableRTL?e.split(".").reverse().join(" - "):e.replace("."," - ")):(h=this.element.find(".e-cubeTreeView").find("li[data-tag='"+r+"'] a:eq(0)").text()||this.element.find(".e-cubeTreeView").find("li[data-tag='"+r.toUpperCase()+"'] a:eq(0)").text()||e,u=n(i[t]).find("button").attr("fieldCaption")==this._getLocalizedLabels("KPIs")?this._getLocalizedLabels("KPIs"):n(i[t]).find("button").attr("fieldCaption")+(h!=""?" - "+h:""),u=this.model.enableRTL&&u.indexOf("-")>0?u.split("-").reverse().join(" - "):u,n(i[t]).find("button").attr("title",u)),n(i[t]).attr("data-tag").split(":")[0]=="Slicers"&&n(i[t]).attr("data-tag").indexOf("::CalculatedMember")<0&&(u=n(i[t]).attr("data-tag").split(":")[1],c=(this._fieldSelectedMembers[u]=="All"?o:this._fieldSelectedMembers[u])||o,n(i[t]).find("button").text((this.model.showUniqueNameOnPivotButton?n(i[t]).find("button").parent().attr("data-tag").split(":")[1].replace("."," - "):n(i[t]).find("button").attr("fieldCaption"))+" ("+c+")"),n(i[t]).find("button").attr("title",n(i[t]).find("button").attr("title")+" ("+c+")")))}},_addSplitButtonHeight:function(){for(var i=this.element.find(".e-splitBtn"),t=0;t<i.length;t++)n(i[t]).find(".e-removeSplitBtn").height(n(i[t]).height()-9),n(i[t]).find(".e-removeSplitBtn").css("line-height",n(i[t]).find(".e-removeSplitBtn").height()+"px")},_controlPanel:function(){var f,n,i;if(this.displayMode()!=t.PivotClient.DisplayMode.ChartOnly&&(n=this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()==t.PivotClient.DisplayMode.ChartAndGrid?this.defaultView()==t.PivotClient.DefaultView.Grid?t.buildTag("div#"+this._id+"_gridPanel.e-gridPanel",t.buildTag("div.e-gridContainer",t.buildTag("div#"+this._id+"_PivotGrid","",{margin:this.model.enableToolBar?"5px 0px 7px":"5px 7px 7px",padding:"1px"})[0].outerHTML,{position:"relative",overflow:this.model.enableToolBar?"":"auto",left:"5px",top:"5px",width:this.model.isResponsive?"100%":this.model.enableSplitter?"inherit":""})[0].outerHTML,{width:this.model.enableSplitter?"99%":"auto",height:"auto"})[0].outerHTML:t.buildTag("div#"+this._id+"_gridPanel.e-gridPanel",t.buildTag("div.e-gridContainer",t.buildTag("div#"+this._id+"_PivotGrid","",{margin:this.model.enableToolBar?"10px 0px 7px":"10px 7px 7px",padding:"1px",width:this.model.isResponsive&&!this.model.enableSplitter?"auto":this.model.enableSplitter?"inherit":""})[0].outerHTML,{position:"relative",left:"7px","border-top":"none","margin-top":"-7px",overflow:this.model.enableToolBar?"":"auto",height:this.model.enablePaging?"277px":"",width:this.model.isResponsive?"100%":""})[0].outerHTML,{width:"auto",height:"auto"})[0].outerHTML:this.displayMode()==t.PivotClient.DisplayMode.GridOnly?t.buildTag("div#"+this._id+"_gridPanel.e-gridPanel",t.buildTag("div.e-gridContainer",t.buildTag("div#"+this._id+"_PivotGrid","",{margin:this.model.enableToolBar?"5px 0px 7px":"5px 7px 7px",padding:"1px"})[0].outerHTML,{overflow:this.model.enableToolBar?"":"auto",position:"absolute",left:this.model.enableSplitter?this.model.enableRTL?"13px":"5px":"7px","padding-top":"2px",width:this.model.isResponsive?"100%":this.model.enableSplitter?"98%":"",top:"3px"})[0].outerHTML,{width:"auto",height:"auto"})[0].outerHTML:t.buildTag("div#"+this._id+"_gridPanel.e-gridPanel",t.buildTag("div.e-gridContainer",t.buildTag("div#"+this._id+"_PivotGrid","",{margin:this.model.enableToolBar?"5px 0px 7px":"5px 7px 7px",padding:"1px"})[0].outerHTML,{overflow:this.model.enableToolBar?"":"auto",position:"relative",width:this.model.isResponsive?"100%":"",left:window.navigator.userAgent.indexOf("Trident")>0?this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this.model.enableRTL?"15px":this.model.enableSplitter?"5px":"0px":this.model.enableRTL?"5px":this.model.enableSplitter?"4px":"7px":this.model.enableRTL?"5px":this.model.enableSplitter?this.model.enableVirtualScrolling?"8px":"5px":"7px","padding-top":"5px","border-top":"none"})[0].outerHTML,{width:"100%",height:"auto"})[0].outerHTML),this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(i=this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()==t.PivotClient.DisplayMode.ChartAndGrid?this.defaultView()==t.PivotClient.DefaultView.Grid?t.buildTag("div#"+this._id+"_chartPanel.e-chartPanel",t.buildTag("div.e-chartContainer",t.buildTag("div#"+this._id+"_PivotChart","",{overflow:this.model.enableToolBar||this.model.isResponsive?"":"auto",border:this.model.enableToolBar?"none":""})[0].outerHTML,{position:"relative",overflow:this.model.enableToolBar||this.model.enableSplitter?"":"auto",left:"-2px",padding:this.model.enableToolBar?"5px 0px 5px 0px":this.model.enableSplitter?"5px 3px 5px 5px":"5px 0px 5px 5px",width:this.model.isResponsive?"99.2%":this.model.enableSplitter?"inherit":"","border-top":"none","margin-right":"-11px"})[0].outerHTML,{width:"100%",height:"auto"})[0].outerHTML:t.buildTag("div#"+this._id+"_chartPanel.e-chartPanel",t.buildTag("div.e-chartContainer",t.buildTag("div#"+this._id+"_PivotChart","",{"min-height":this.model.enablePaging?"255px":"",border:this.model.enableToolBar?"none":""})[0].outerHTML,{position:"relative",overflow:this.model.enableToolBar||this.model.enableSplitter||this.model.isResponsive?"":"auto",padding:this.model.enableToolBar?"5px 0px 5px 0px":"5px 0px 5px 5px",width:this.model.isResponsive?"99.6%":this.model.enableSplitter?"inherit":""})[0].outerHTML,{width:"98%",height:"auto"})[0].outerHTML:this.displayMode()==t.PivotClient.DisplayMode.ChartOnly?t.buildTag("div#"+this._id+"_chartPanel.e-chartPanel",t.buildTag("div.e-chartContainer",t.buildTag("div#"+this._id+"_PivotChart","",{border:this.model.enableToolBar?"none":""})[0].outerHTML,{position:"relative",overflow:this.model.enableToolBar||this.model.enableSplitter||this.model.isResponsive?"":"auto",padding:this.model.enableRTL?this.model.enableSplitter&&!this.model.isResponsive?"5px 4px 2px 6px":"5px 25px 2px 6px":this.model.enableToolBar?"5px 0px 5px 0px":"5px 0px 5px 5px","margin-right":this.model.enableRTL?"1px":"-11px"})[0].outerHTML,{width:this.model.enableSplitter?"96%":this.model.isResponsive?"99%":"98%",margin:this.model.enableSplitter?this.model.enableRTL?(this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode)?"5px -5px 7px":"5px 2px 7px":"5px 5px 7px":"5px 7px 7px",height:"auto"})[0].outerHTML:t.buildTag("div#"+this._id+"_chartPanel.e-chartPanel",t.buildTag("div.e-chartContainer",t.buildTag("div#"+this._id+"_PivotChart","",{border:this.model.enableToolBar?"none":""})[0].outerHTML,{position:"relative",overflow:this.model.enableToolBar||this.model.enableSplitter||this.model.isResponsive?"":"auto",top:"-5px",left:window.navigator.userAgent.indexOf("Trident")>0?this.model.enableSplitter?"-1px":"-7px":this.model.enableRTL?"12px":"0px",padding:this.model.enableRTL?"5px 5px 0px 5px":this.model.enableToolBar?"5px 0px 5px 0px":"5px 0px 5px 5px","border-top":"none","margin-right":this.model.enableRTL?"0px":this.model.enableSplitter?"-9px":"-11px","margin-left":this.model.enableRTL?"-11px":"0px"})[0].outerHTML,{margin:this.model.enableSplitter?this.model.enableRTL?"5px -3px 7px":"5px 5px 7px":this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?"5px 5px 7px":"5px 7px 7px",width:this.model.isResponsive?this.model.enableSplitter?this.model.enableRTL?"96%":"99%":"98.5%":"98.5%",left:this.model.enableSplitter?"7px":"",height:"auto"})[0].outerHTML),this.controlPlacement()==t.PivotClient.ControlPlacement.Tab&&this.displayMode()==t.PivotClient.DisplayMode.ChartAndGrid){var r,e,s="<a style='font: bold 12px Segoe UI' href='#"+this._id+"_gridPanel' tabindex='0'>"+this._getLocalizedLabels("Grid")+"<\/a>",h="<a style='font: bold 12px Segoe UI' href='#"+this._id+"_chartPanel' tabindex='0'>"+this._getLocalizedLabels("Chart")+"<\/a>",u=window.navigator.userAgent.indexOf("Trident")>0?this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this.model.enableRTL?"15px":this.model.enableSplitter?"5px":"0px":this.model.enableRTL?"5px":this.model.enableSplitter?"4px":"7px":this.model.enableRTL?"5px":this.model.enableSplitter?this.model.enableVirtualScrolling?"8px":"5px":"7px",o=0;this.model.enableRTL&&(o="-"+u,u=0);this.defaultView()==t.PivotClient.DefaultView.Chart?(r=t.buildTag("ul.clientTab",t.buildTag("li",h)[0].outerHTML+t.buildTag("li",s)[0].outerHTML,{"margin-left":u,"margin-right":o})[0].outerHTML,e=t.buildTag("div#"+this._id+"_clientTab",r+i+n)[0].outerHTML):(r=t.buildTag("ul.clientTab",t.buildTag("li",s)[0].outerHTML+t.buildTag("li",h)[0].outerHTML,{"margin-left":u,"margin-right":o})[0].outerHTML,e=t.buildTag("div#"+this._id+"_clientTab",r+n+i,{height:"100%"})[0].outerHTML);f=e}else f=this._createControlContainer(this.controlPlacement(),i,n);return t.buildTag("div.e-controlPanel",f,{width:this.model.isResponsive?"100%":this.model.displaySettings.controlPlacement=="tab"?this.model.analysisMode=="olap"&&this.model.operationalMode=="servermode"||!this.model.enableSplitter?"":"99%":"auto",top:this.model.displaySettings.mode==t.PivotClient.DisplayMode.GridOnly?"3px":"none","margin-bottom":this.model.displaySettings.controlPlacement==t.PivotClient.ControlPlacement.Tile&&(this.model.enablePaging||this.model.enableVirtualScrolling)?"5px":"0"})[0].outerHTML+(this.model.enablePaging?t.buildTag("div#"+this._id+"_Pager")[0].outerHTML:this.model.enableVirtualScrolling?t.buildTag("div#"+this._id+"_hsVirtualScrolling.hsVirtualScrolling",t.buildTag("div.e-hScrollPanel")[0].outerHTML,{width:"10px"})[0].outerHTML:"")},_createControlContainer:function(n,i,r){return this.displayMode()==t.PivotClient.DisplayMode.ChartOnly?"<table style='width:100%'><tr><td>"+i+"<\/td><\/tr><\/table>":this.displayMode()==t.PivotClient.DisplayMode.GridOnly?"<table style='width:100%'><tr><td>"+r+"<\/td><\/tr><\/table>":this.controlPlacement()==t.PivotClient.ControlPlacement.Tile?this.defaultView()==t.PivotClient.DefaultView.Chart?"<table style='width:100%'><tr><td>"+i+"<\/td><\/tr><tr><td>"+r+"<\/td><\/tr><\/table>":"<table style='width:100%'><tr><td>"+r+"<\/td><\/tr><tr><td>"+i+"<\/td><\/tr><\/table>":this.defaultView()==t.PivotClient.DefaultView.Chart?"<table style='width:100%'><tr><td valign='top'>"+i+"<\/td><td valign='top'>"+r+"<\/td><\/tr><\/table>":"<table style='width:100%'><tr><td valign='top'>"+r+"<\/td><td valign='top'>"+i+"<\/td><\/tr><\/table>"},_renderPivotGrid:function(r){var e,u,f;if(r=t.isNullOrUndefined(r)?"":r,this.element.find(".e-dialog").hide(),this.displayMode()!=t.PivotClient.DisplayMode.ChartOnly)if(this._pivotChart!=i&&this._pivotChart._startDrilldown){try{u=JSON.parse(this._pivotGrid.getOlapReport()).Report}catch(o){u=this._pivotGrid.getOlapReport()}this._pivotGrid.doAjaxPost("POST",this.model.url+"/"+this._pivotGrid.model.serviceMethodSettings.drillDown,JSON.stringify({action:"drillDownGrid",cellPosition:"",currentReport:u,clientReports:this.reports,headerInfo:r,layout:this.gridLayout(),enableRTL:this.model.enableRTL}),this._pivotGrid._drillDownSuccess)}else this.model.enableDeferUpdate?(this._isTimeOut=!1,this._waitingPopup.hide()):(f=this._pivotGrid.model.enableConditionalFormatting?this._pivotGrid.model.conditionalFormatSettings:[],this.element.find(".e-pivotgrid, #"+this._id+"_PivotGrid").remove(),e=this.controlPlacement()==t.PivotClient.ControlPlacement.Tile&&this.displayMode()==t.PivotClient.DisplayMode.ChartAndGrid?t.buildTag("div#"+this._id+"_PivotGrid","",{margin:this.model.enableToolBar?"5px 0px 7px":"5px 7px 7px",width:this.model.isResponsive?"auto":" ",padding:"1px"})[0].outerHTML:t.buildTag("div#"+this._id+"_PivotGrid","",{margin:this.model.enableToolBar?"5px 0px 7px":"5px 7px 7px",padding:"1px"})[0].outerHTML,n(e).appendTo(this.element.find(".e-gridContainer")),this.gridLayout()!=t.PivotGrid.Layout.Normal?this.element.find("#"+this._id+"_PivotGrid").ejPivotGrid({url:this.model.url,customObject:this.model.customObject,enableRTL:this.model.enableRTL,enableDefaultValue:this.model.enableDefaultValue,enableCellSelection:this.model.enableCellSelection,cellSelection:this.model.cellSelection,hyperlinkSettings:{enableValueCellHyperlink:this.model.enableValueCellHyperlink,enableRowHeaderHyperlink:this.model.enableRowHeaderHyperlink,enableColumnHeaderHyperlink:this.model.enableColumnHeaderHyperlink,enableSummaryCellHyperlink:this.model.enableSummaryCellHyperlink},enableCellContext:this.model.enableCellContext,enableDrillThrough:this.model.enableDrillThrough,enableCellEditing:this.model.enableCellEditing,enableCellDoubleClick:this.model.enableCellDoubleClick,enableCellClick:this.model.enableCellClick,valueCellHyperlinkClick:this.model.valueCellHyperlinkClick,rowHeaderHyperlinkClick:this.model.rowHeaderHyperlinkClick,columnHeaderHyperlinkClick:this.model.columnHeaderHyperlinkClick,summaryCellHyperlinkClick:this.model.summaryCellHyperlinkClick,cellContext:this.model.cellContext,cellEdit:this.model.cellEdit,cellDoubleClick:this.model.cellDoubleClick,enableCellClick:this.model.enableCellClick,drillThrough:this.model.drillThrough,isResponsive:this.model.isResponsive,currentReport:this.currentReport,locale:this.locale(),layout:this.gridLayout(),drillSuccess:t.proxy(this._gridDrillSuccess,this),conditionalFormatSettings:f,enableConditionalFormatting:this._pivotGrid.model.enableConditionalFormatting}):this.element.find("#"+this._id+"_PivotGrid").ejPivotGrid({url:this.model.url,customObject:this.model.customObject,enableRTL:this.model.enableRTL,enableDefaultValue:this.model.enableDefaultValue,enableCellSelection:this.model.enableCellSelection,cellSelection:this.model.cellSelection,hyperlinkSettings:{enableValueCellHyperlink:this.model.enableValueCellHyperlink,enableRowHeaderHyperlink:this.model.enableRowHeaderHyperlink,enableColumnHeaderHyperlink:this.model.enableColumnHeaderHyperlink,enableSummaryCellHyperlink:this.model.enableSummaryCellHyperlink},enableCellContext:this.model.enableCellContext,enableDrillThrough:this.model.enableDrillThrough,enableCellEditing:this.model.enableCellEditing,enableCellDoubleClick:this.model.enableCellDoubleClick,enableCellClick:this.model.enableCellClick,valueCellHyperlinkClick:this.model.valueCellHyperlinkClick,rowHeaderHyperlinkClick:this.model.rowHeaderHyperlinkClick,columnHeaderHyperlinkClick:this.model.columnHeaderHyperlinkClick,summaryCellHyperlinkClick:this.model.summaryCellHyperlinkClick,cellContext:this.model.cellContext,cellEdit:this.model.cellEdit,cellDoubleClick:this.model.cellDoubleClick,enableCellClick:this.model.enableCellClick,drillThrough:this.model.drillThrough,isResponsive:this.model.isResponsive,currentReport:this.currentReport,locale:this.locale(),drillSuccess:t.proxy(this._gridDrillSuccess,this),conditionalFormatSettings:f,enableConditionalFormatting:this._pivotGrid.model.enableConditionalFormatting}),this._pivotGrid=this.element.find("#"+this._id+"_PivotGrid").data("ejPivotGrid"))},_renderPivotChart:function(r,u){var f,e,o,s;if(this.element.find(".e-dialog").hide(),this.displayMode()!=t.PivotClient.DisplayMode.GridOnly)if(this._pivotGrid!=i&&this._pivotGrid._startDrilldown){try{f=JSON.parse(this._pivotChart.getOlapReport()).Report}catch(h){f=this._pivotChart.getOlapReport()}this._pivotChart.doAjaxPost("POST",this.model.url+"/"+this._pivotChart.model.serviceMethodSettings.drillDown,JSON.stringify({action:this._pivotChart.model.enableMultiLevelLabels?u+"#fullchart":u,drilledSeries:r,olapReport:f,clientReports:this.reports}),this._pivotChart.renderControlSuccess)}else this.model.enableDeferUpdate?(this._isTimeOut=!1,this._waitingPopup.hide()):(e=null,this.element.find(".e-pivotchart").length>0&&(o=this.element.find(".e-pivotchart").data("ejPivotChart"),e=o.model.renderSuccess),this.element.find(".e-pivotchart").remove(),s=t.buildTag("div#"+this._id+"_PivotChart","",{height:"auto"})[0].outerHTML,this.element.find(".e-chartContainer").children().length==0&&n(s).appendTo(this.element.find(".e-chartContainer")),this.element.find("#"+this._id+"_PivotChart").ejPivotChart({url:this.model.url,customObject:this.model.customObject,enableRTL:this.model.enableRTL,enableDefaultValue:this.model.enableDefaultValue,axesLabelRendering:this.model.axesLabelRendering,pointRegionClick:this.model.pointRegionClick,canResize:this.model.isResponsive,currentReport:this.currentReport,customObject:this.model.customObject,locale:this.locale(),showTooltip:!0,size:{height:this._chartHeight,width:this._chartWidth},commonSeriesOptions:{type:this.model.chartType,tooltip:{visible:!0}},beforeServiceInvoke:this.model.chartLoad,drillSuccess:t.proxy(this._chartDrillSuccess,this),renderSuccess:e}),this._pivotChart=this.element.find("#"+this._id+"_PivotChart").data("ejPivotChart"),this.element.find("#"+this._id+"_PivotChart").width(this._pivotChart.model.size.width),this.model.enableToolBar&&this.element.find("#"+this._id+"_PivotChart").css("border","none"));this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&this._toggleExpand&&!this.model.isResponsive&&this.chartObj.sfType.split(".").pop().toLowerCase()!="treemap"&&this.element.find(".e-toggleExpandButton").click()},_renderPivotTreeMap:function(r,u){var f,e;this.element.find(".e-dialog").hide();this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this._pivotGrid!=i&&this._pivotGrid._startDrilldown?(f=this.otreemapObj.getOlapReport(),this.otreemapObj.doAjaxPost("POST",this.model.url+"/"+this.otreemapObj.model.serviceMethodSettings.drillDown,JSON.stringify({action:u,drillInfo:r,olapReport:f,clientReports:this.reports}),this.otreemapObj.renderControlSuccess),t.isNullOrUndefined(this.chartObj)||(this.model.displaySettings.enableFullScreen||this.enableTogglePanel()&&this.chartObj.sfType.split(".").pop().toLowerCase()=="treemap")&&(n("#"+this.otreemapObj._id+"TreeMapContainer").css({width:"100%"}),this.otreemapObj._treeMap.refresh())):this.model.enableDeferUpdate?(this._isTimeOut=!1,this._waitingPopup.hide()):(this.element.find(".e-pivottreemap").remove(),e=t.buildTag("div#"+this._id+"_PivotChart","",{height:"auto"})[0].outerHTML,this.element.find(".e-chartContainer").children().length==0&&n(e).appendTo(this.element.find(".e-chartContainer")),this.element.find("#"+this._id+"_PivotChart").ejPivotTreeMap({url:this.model.url,customObject:this.model.customObject,canResize:this.model.isResponsive,currentReport:this.currentReport,customObject:this.model.customObject,locale:this.locale(),size:{height:this._chartHeight,width:this._chartWidth},beforeServiceInvoke:this.model.treeMapLoad,drillSuccess:t.proxy(this._treemapDrillSuccess,this)}),this.otreemapObj=this.element.find("#"+this._id+"_PivotChart").data("ejPivotTreeMap"),this.element.find("#"+this._id+"_PivotChart").width(this.otreemapObj.model.size.width)));this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&this._toggleExpand&&!this.model.isResponsive&&this.chartObj.sfType.split(".").pop().toLowerCase()=="treemap"&&this.element.find(".e-toggleExpandButton").click()},_renderControls:function(){(this._isNodeOrButtonDropped&&t.isNullOrUndefined(this.chartObj)||this._isrenderTreeMap)&&this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this._isNodeOrButtonDropped=!1,this._isrenderTreeMap=!1,this.chartObj=null,this.chartObj=this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(this.chartObj)&&!t.isNullOrUndefined(this.otreemapObj)&&(this.chartObj=this.element.find("#"+this.otreemapObj._id+"TreeMapContainer").data("ejTreeMap")));this.displayMode()==t.PivotClient.DisplayMode.GridOnly?this.defaultView()==t.PivotClient.DefaultView.Chart?(this._renderPivotChart(),this._renderPivotGrid()):(this._renderPivotGrid(),this._renderPivotChart()):this.defaultView()==t.PivotClient.DefaultView.Chart?(t.isNullOrUndefined(this.chartObj)?this.element.find("#"+this._pivotChart._id+"Container").length>0?this._renderPivotChart():this.model.enablePivotTreeMap&&this._renderPivotTreeMap():this.chartObj.sfType.split(".").pop().toLowerCase()!="treemap"?this._renderPivotChart():this.model.enablePivotTreeMap&&this._renderPivotTreeMap(),this._renderPivotGrid()):(this._renderPivotGrid(),t.isNullOrUndefined(this.chartObj)?this.element.find("#"+this._pivotChart._id+"Container").length>0?this._renderPivotChart():this.model.enablePivotTreeMap&&this._renderPivotTreeMap():this.chartObj.sfType.split(".").pop().toLowerCase()!="treemap"?this._renderPivotChart():this.model.enablePivotTreeMap&&this._renderPivotTreeMap())},_createDialogRequest:function(r){var u,l,h,s,c,e,f,o;if(!(r.target.className.indexOf("e-dialogOKBtn")>=0)){if(r.target.type=="button"&&n(r.target).parent().attr("data-tag").indexOf("::CalculatedMember")>-1){if(this._selectedCalcMember=n(r.target).parent().text(),this.element.find(".calcMemberDialog").length>0){if(this._calcMemberTreeObj.collapseAll(),t.Pivot.closePreventPanel(this),this._calcMemberDialog.open(),u=t.DataManager(this._calcMembers).executeLocal(t.Query().where("name","equal",this._selectedCalcMember)),u.length>0){if(this.element.find("#"+this._id+"_captionFieldCM").val(u[0].name),this.element.find("#"+this._id+"_expressionFieldCM").val(u[0].expression),this.element.find("#"+this._id+"_memberTypeFieldCM").data("ejDropDownList").selectItemsByIndices(u[0].nodeType),u[0].nodeType==1)for(l=u[0].tag.split(".")[0].replace(/\[/g,"").replace(/\]/g,""),h=this.element.find("#"+this._id+"_dimensionFieldCM").data("ejDropDownList").model.dataSource,f=0;f<h.length;f++)h[f].value==l&&this.element.find("#"+this._id+"_dimensionFieldCM").data("ejDropDownList").selectItemsByIndices(f);t.isNullOrUndefined(u[0].formatString)||(u[0].formatString=="Currency"?this.element.find("#"+this._id+"_formatFieldCM").data("ejDropDownList").selectItemsByIndices(1):u[0].formatString=="Percent"?this.element.find("#"+this._id+"_formatFieldCM").data("ejDropDownList").selectItemsByIndices(2):(this.element.find("#"+this._id+"_formatFieldCM").data("ejDropDownList").selectItemsByIndices(2),this.element.find("#"+this._id+"_customFormatFieldCM").val(u[0].formatString)))}}else this._waitingPopup.show(),this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:"fetchCalcMemberTreeView",element:this.element,customObject:this.model.customObject}),o=JSON.stringify(this.model.customObject),this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.fetchMemberTreeNodes,JSON.stringify({action:"fetchCalcMemberTreeView",dimensionName:"calcMember>#>"+this.model.sortCubeMeasures,olapReport:this.currentReport,customObject:o}),t.proxy(t.Pivot._createCalcMemberDialog,this));return}if(this.model.enableMemberEditorPaging&&(this._memberPageSettings.startPage=0,this._memberPageSettings.currentMemeberPage=1,this._selectedFieldName=n(r.target).parent().attr("data-tag"),this._memberPageSettings.filterReportCollection={}),s=this,this._args_className=r.target.className,this._args_innerHTML=r.target.innerHTML==""?"ToolbarButtons":r.target.innerHTML,this._dialogTitle=this._hierarchyCaption=t.browserInfo().name=="msie"&&t.browserInfo().version<=8?n(r.currentTarget)[0].title:r.currentTarget.attributes==""?"":n(r.currentTarget).attr("title")||n(r.currentTarget).attr("aria-label"),this._currentItem=this._hierarchyCaption,this.element.find(".e-dialog:not(.calcMemberDialog, .calcMemberDialog .e-dialog), .e-clientDialog").remove(),this._args_innerHTML!="ToolbarButtons"&&this._args_innerHTML!=i&&this.model.analysisMode==t.Pivot.AnalysisMode.Olap){if(n(r.target).parent().attr("data-tag").indexOf(":")>=0&&n(r.target).parent().attr("data-tag").split(":")[1].indexOf(".")>=0)if(c=n(r.target).parent().attr("data-tag").split(":")[1].split("."),e=this.element.find(".e-cubeTreeView li[data-tag^='["+c[0]+"'][data-tag$='"+c[1]+"]']"),e.length>0){for(f=0;f<e.length;f++)if(n(e[f]).attr("data-tag").split("].").length==2){this._selectedFieldName=n(e[f]).attr("data-tag");break}}else this._selectedFieldName=e.attr("data-tag");else this._selectedFieldName="";s._isTimeOut=!0;setTimeout(function(){s._isTimeOut&&s._waitingPopup.show()},800);this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:"fetchMemberTreeNodes",element:this.element,customObject:this.model.customObject});o=JSON.stringify(this.model.customObject);this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.fetchMemberTreeNodes,JSON.stringify({action:"fetchMemberTreeNodes",dimensionName:n(r.target).parent().attr("data-tag"),olapReport:this.currentReport,customObject:o}),this._editorTreeInfoSuccess)}else this._createDialog(this._args_className,this._args_innerHTML,"")}},_fetchReportListSuccess:function(n){var r="";n[0]!=i?n[2]!=null&&n[2]!=i&&(this.model.customObject=n[2].Value):n.d!=i?n.d[2]!=null&&n.d[2]!=i&&(this.model.customObject=n.d[2].Value):n.customObject!=null&&n.customObject!=i&&(this.model.customObject=n.customObject);this.model.afterServiceInvoke!=null&&this._trigger("afterServiceInvoke",{action:"fetchReportList",element:this.element,customObject:this.model.customObject,reports:n});this.element.find(".e-dialog:not(.calcMemberDialog, .calcMemberDialog .e-dialog), .e-clientDialog").remove();n[0]!=i?(r=t.isNullOrUndefined(n[1].Value)?"":n[1].Value,n=t.isNullOrUndefined(n[0].Value)?"":n[0].Value):n.d!=i?(r=t.isNullOrUndefined(n.d[1].Value)?"":n.d[1].Value,n=t.isNullOrUndefined(n.d[0].Value)?"":n.d[0].Value):(r=n.action,n=n.ReportNameList);n==""?(t.Pivot._createErrorDialog(this._getLocalizedLabels("NoReports"),this._getLocalizedLabels("Warning"),this),this._isTimeOut=!1):this._createDialog(r,n,"")},_fetchSortState:function(n){this._isTimeOut=!1;this._waitingPopup.hide();n[0]!=i?this._createDialog("e-SortFilterDlg",n[0].Value,""):n.d!=i?this._createDialog("e-SortFilterDlg",n.d[0].Value,""):this._createDialog("e-SortFilterDlg",n.FetchSortState,"")},_createDialog:function(r,u,f){var p=!1,nt,w,pt,ut,ft,e,o,s,bt,et,ot,st,kt,dt,gt,ni,ti,l,k,ii,ri,ht,ct,lt,ui,fi,oi,vt,yt,tt,si,d,y,g,rt,li,a;if(t.Pivot.openPreventPanel(this),this.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&(r=="e-SortFilterDlg"||r=="mdx")){if(nt=this.element.find(".e-splitBtn"),w=jQuery.grep(nt,function(t){return n(t).attr("data-tag").split(":")[1]=="Measures"}).length,pt=jQuery.grep(nt,function(t){return n(t).attr("data-tag").split(":")[1]!="Measures"&&n(t).attr("data-tag").split(":")[0]=="Rows"}).length,ut=jQuery.grep(nt,function(t){return n(t).attr("data-tag").split(":")[1]!="Measures"&&n(t).attr("data-tag").split(":")[0]=="Columns"}).length,r=="mdx"&&w==0&&ut==0)return t.Pivot._createErrorDialog(this._getLocalizedLabels("MDXAlertMsg"),this._getLocalizedLabels("Warning"),this),!1;if(w>0&&this.element.find(".e-categoricalAxis ")[0].childElementCount!=0){if(!pt&&document.activeElement.className.indexOf("e-rowSortFilterImg")>-1)return t.Pivot._createErrorDialog(this._getLocalizedLabels("FilterSortRowAlertMsg"),this._getLocalizedLabels("Warning"),this),!1;if(!ut&&document.activeElement.className.indexOf("e-colSortFilterImg")>-1)return t.Pivot._createErrorDialog(this._getLocalizedLabels("FilterSortColumnAlertMsg"),this._getLocalizedLabels("Warning"),this),!1}else if(r!="mdx")return w||this._axis!="Column"?w||this._axis!="Row"?n(".e-categoricalAxis ")[0].childElementCount||t.Pivot._createErrorDialog(this._getLocalizedLabels("FilterSortElementAlertMsg"),this._getLocalizedLabels("Warning"),this):t.Pivot._createErrorDialog(this._getLocalizedLabels("FilterSortrowMeasureAlertMsg"),this._getLocalizedLabels("Warning"),this):t.Pivot._createErrorDialog(this._getLocalizedLabels("FilterSortcolMeasureAlertMsg"),this._getLocalizedLabels("Warning"),this),!1}this.model.analysisMode!=t.Pivot.AnalysisMode.Olap||t.isNullOrUndefined(this._selectedFieldName)||(p=this.element.find(".e-pvtBtn").parent("[data-tag='Slicers:"+this._selectedFieldName.replace(/\[/g,"").replace(/\]/g,"")+"']").length>0);var b="",v="",h="",wt=[],c;if(c=f[0]!=i?f[0].Value:f.d!=i?f.d[0].Value:f.EditorTreeInfo,ft=t.buildTag("div#"+(this.model.enableAdvancedFilter?this._id+"_clientDialog":this._id+"_ClientDialog")+".e-clientDialog",{opacity:"1"})[0].outerHTML,st="",r.split(" ")[0]=="e-newReportImg")o=this._getLocalizedLabels("NewReport"),s="New Report",kt="<p class='e-dialogPara'>"+this._getLocalizedLabels("ReportName")+"<\/p>",dt="<input type=text id="+this._id+"_reportName class='reportName'/><\/br>",e=t.buildTag("div#"+this._id+"_ReportDlg.e-reportDlg","<table><tr><td>"+kt+"<\/td><td>"+dt+"<\/td><\/tr><\/table>")[0].outerHTML;else if(r.split(" ")[0]=="e-addReportImg")o=this._getLocalizedLabels("AddReport"),s="Add Report",gt="<p class='e-dialogPara'>"+this._getLocalizedLabels("ReportName")+"<\/p>",ni="<input type=text id="+this._id+"_reportName class='reportName'/><\/br>",e=t.buildTag("div#"+this._id+"_ReportDlg.e-reportDlg","<table><tr><td>"+gt+"<\/td><td>"+ni+"<\/td><\/tr><\/table>")[0].outerHTML;else if(r.split(" ")[0]=="e-removeReportImg")o=this._getLocalizedLabels("RemoveReport"),s="Remove Report",(this.model.operationalMode==t.Pivot.OperationalMode.ClientMode||this.model.analysisMode==t.Pivot.AnalysisMode.Pivot)&&(this.reportsCount=this._clientReportCollection.length),e=this.reportsCount>1?"<p>"+this._getLocalizedLabels("AreYouSureToDeleteTheReport")+"?<\/p>":"<p>"+this._getLocalizedLabels("CannotRemoveSingleReport")+"!<\/p>";else if(r.split(" ")[0]=="e-SortFilterDlg"){b=u.split("||");b[1]!=""&&(v=b[1].split("<<"));b[2]!=""&&(h=b[2].split("<<"));this.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog), .e-clientDialog").remove();o=this._getLocalizedLabels("SortingAndFiltering");var ai=t.buildTag("label#"+this._id+"_sortingLbl.e-sortingLbl",this._getLocalizedLabels("Sorting"))[0].outerHTML,vi=t.buildTag("label#"+this._id+"_measureListLbl.e-measureListLbl",this._getLocalizedLabels("Measure"))[0].outerHTML,yi=t.buildTag("label#"+this._id+"_orderLbl.e-orderLbl",this._getLocalizedLabels("Order"))[0].outerHTML,pi=t.buildTag("input#"+this._id+"_measuresList.e-measuresList","",{},{type:"text",accesskey:"M"})[0].outerHTML,wi=t.buildTag("input#"+this._id+"_sortDisable.e-sortDisable","",{},{name:"sort",type:"radio",checked:"checked",tabindex:0,accesskey:"i"})[0].outerHTML+t.buildTag("label.e-sortDisableLbl",this._getLocalizedLabels("Disable"))[0].outerHTML,bi=t.buildTag("input#"+this._id+"_sortEnable.e-sortEnable","",{},{name:"sort",type:"radio",tabindex:1,accesskey:"n"})[0].outerHTML+t.buildTag("label.e-sortEnableLbl",this._getLocalizedLabels("Enable"))[0].outerHTML+"<br/>",ki=t.buildTag("input#"+this._id+"_radioBtnAsc.e-radioBtnAsc","",{},{name:"order",type:"radio",checked:"checked",tabindex:0,accesskey:"A"})[0].outerHTML+t.buildTag("label.e-radioBtnAscLbl",this._getLocalizedLabels("Ascending"))[0].outerHTML+"<br/>",di=t.buildTag("input#"+this._id+"_radioBtnDesc.e-radioBtnDesc","",{},{name:"order",type:"radio",tabindex:2,accesskey:"e"})[0].outerHTML+t.buildTag("label.e-radioBtnDescLbl",this._getLocalizedLabels("Descending"))[0].outerHTML,gi=t.buildTag("input#"+this._id+"_preserveHrchy.e-preserveHrchy","",{},{type:"checkbox",checked:"checked",tabindex:0,accesskey:"r"})[0].outerHTML+t.buildTag("label.e-preserveHrchyLbl",this._getLocalizedLabels("PreserveHierarchy"))[0].outerHTML,nr=t.buildTag("label#"+this._id+"_filterLbl.e-filterLbl",this._getLocalizedLabels("Filtering"))[0].outerHTML,tr=t.buildTag("input#"+this._id+"_filterDisable.e-filterDisable","",{},{name:"filter",type:"radio",checked:"checked",tabindex:0,accesskey:"i"})[0].outerHTML+t.buildTag("label.e-filterDisableLbl",this._getLocalizedLabels("Disable"))[0].outerHTML,ir=t.buildTag("input#"+this._id+"_filterEnable.e-filterEnable","",{},{name:"filter",type:"radio",tabindex:0,accesskey:"n"})[0].outerHTML+t.buildTag("label.e-filterDisableLbl",this._getLocalizedLabels("Enable"))[0].outerHTML+"<br/>",rr=t.buildTag("label#"+this._id+"_conditionLbl.e-conditionLbl",this._getLocalizedLabels("Condition"))[0].outerHTML,ur=t.buildTag("input#"+this._id+"_filterCondition.e-filterCondition","",{},{type:"text",tabindex:0,accesskey:"o"})[0].outerHTML,fr=t.buildTag("div#"+this._id+"_filterfrom.e-filterFrmDiv",t.buildTag("input#"+this._id+"_filterFrom.filterFrom"+(t.isMobile()?" inputConditionMbl":""),"",{},{name:"inputVal",type:t.isMobile()?"number":"text",inputmode:t.isMobile()?"numeric":"",pattern:t.isMobile()?"[0-9]*":"",tabindex:0})[0].outerHTML)[0].outerHTML,er=t.buildTag("div#"+this._id+"_filterbtw.e-filterBtw","<p>"+this._getLocalizedLabels("and")+"<\/p>")[0].outerHTML,or=t.buildTag("div#"+this._id+"_filterto.e-filterToDiv",t.buildTag("input#"+this._id+"_filterTo.filterTo"+(t.isMobile()?" inputConditionMbl":""),"",{},{name:"inputVal",type:t.isMobile()?"number":"text",inputmode:t.isMobile()?"numeric":"",pattern:t.isMobile()?"[0-9]*":"",tabindex:0,accesskey:"d"})[0].outerHTML)[0].outerHTML,sr=t.buildTag("label#"+this._id+"_filterMeasureListLbl.filterMeasureListLbl",this._getLocalizedLabels("Measure"))[0].outerHTML,hr=t.buildTag("input#"+this._id+"_fMeasuresList.fMeasuresList","",{},{type:"text",tabindex:0,accesskey:"M"})[0].outerHTML,cr=t.buildTag("label#"+this._id+"_filterValueLbl.e-filterValueLbl",this._getLocalizedLabels("Value"),{},{tabindex:0,accesskey:"a"})[0].outerHTML,lr=t.buildTag("div#"+this._id+"_sortingDlg.e-sortingDlg","<table class='e-sortReportTbl'><tr><td style='vertical-align:top;'>"+ai+"<\/td><td>"+bi+wi+"<\/td><\/tr><tr><td>"+vi+"<\/td><td>"+pi+"<\/td><\/tr> <tr><td>"+yi+"<\/td><td>"+ki+di+"<\/td><\/tr><tr><td><\/td><td>"+gi+"<\/td><\/tr><\/table>")[0].outerHTML,ar=t.buildTag("div#"+this._id+"_filteringDlg.e-filteringDlg","<table class='e-sortReportTbl'><tr><td>"+nr+"<\/td><td>"+ir+tr+"<\/td><\/tr><tr><td>"+sr+"<\/td><td>"+hr+"<\/td><\/tr><tr><td>"+rr+"<\/td><td>"+ur+"<\/td><\/tr><tr><td>"+cr+"<\/td><td>"+fr+er+or+"<\/td><\/tr><\/table>")[0].outerHTML,vr="<li><a style='font: bold 12px Segoe UI' href='#"+this._id+"_sortingDlg'>"+this._getLocalizedLabels("Sorting")+"<\/a><\/li>",yr="<li><a style='font: bold 12px Segoe UI' href='#"+this._id+"_filteringDlg'>"+this._getLocalizedLabels("Filtering")+"<\/a><\/li>",pr=t.buildTag("div#"+this._id+"_sortfilterTab.sortfilterTab","<ul class ='e-sortfiltTab'>"+(vr+yr+"<\/ul>"+lr+ar))[0].outerHTML;e=pr}else if(r.split(" ")[0]=="e-renameReportImg"){o=this._getLocalizedLabels("RenameReport");s="Rename Report";var wr=t.isNullOrUndefined(this._currentReportName)?this.element.find(".reportlist").val():this._currentReportName,br="<p class='e-dialogPara'>"+this._getLocalizedLabels("ReportName")+"<\/p>",kr="<input type=text id="+this._id+"_reportName class='reportName' value='"+wr+"' /><\/br>";e=t.buildTag("div#"+this._id+"_ReportDlg.e-reportDlg","<table><tr><td>"+br+"<\/td><td>"+kr+"<\/td><\/tr><\/table>")[0].outerHTML}else if(r.split(" ")[0]=="e-saveAsReportImg"||r.split(" ")[0]=="e-saveReportImg")o=r.split(" ")[0]=="e-saveAsReportImg"?this._getLocalizedLabels("SaveAs"):this._getLocalizedLabels("Save"),s="SaveAs Report",l="<p class='e-dialogPara'>"+this._getLocalizedLabels("ReportName")+"<\/p>",ti="<input type=text id="+this._id+"_reportName class='reportName'/><\/br>",e=t.buildTag("div#"+this._id+"_ReportDlg.e-reportDlg","<table><tr><td>"+l+"<\/td><td>"+ti+"<\/td><\/tr><\/table>")[0].outerHTML;else if(r.split(" ")[0]=="LoadReport")o=this._getLocalizedLabels("Load"),s="Load Report",l="<table class='e-loadReportTbl'><tr><td class='e-loadReportTd'>"+this._getLocalizedLabels("ReportName")+"<\/td>",k="<td><input type='text' id='"+this._id+"_reportNameList' class='reportNameList'/><\/td><\/tr><\/table>",e=l+k;else if(r.split(" ")[0]=="RemoveDBReport")o=this._getLocalizedLabels("Remove"),s="RemoveDB Report",l="<table class='e-removeDBReportTbl'><tr><td class='e-removeDBReportTd'>"+this._getLocalizedLabels("ReportName")+"<\/td>",k="<td><input type='text' id='"+this._id+"_reportNameList' class='reportNameList'/><\/td><\/tr><\/table>",e=l+k;else if(r.split(" ")[0]=="RenameDBReport"){o=this._getLocalizedLabels("Rename");s="RenameDB Report";var l="<table class='e-renameDBReportTbl'><tr><td class='e-renameDBReportTd'>"+this._getLocalizedLabels("SelectReport")+"<\/td>",k="<td><input type='text' id='"+this._id+"_reportNameList' class='reportNameList'/><\/td><\/tr>",dr="<tr><td class='e-renameDBReportTd'>"+this._getLocalizedLabels("RenameReport")+"<\/td>",gr="<td><input type='text' id='"+this._id+"_renameReport' class='renameReport' style='width:146px'/><\/td><\/tr><\/table>";e=l+k+dr+gr}else if(r.split(" ")[0]=="mdx")o=this._getLocalizedLabels("MDXQuery"),s="MDX Query",ii="<textarea readonly='readonly' style='width:460px; height:180px; resize:none; margin:3px'>"+u+"<\/textarea>",e=ii;else if(r.split(" ")[0]=="e-chartTypesImg")o=this._getLocalizedLabels("ChartTypes"),s="Chart Types",ri="<p class='e-dialogPara'>"+this._getLocalizedLabels("ChartTypes")+"<\/p>",it="<input type=text id="+this._id+"_reportName class='reportName'/><\/br>",e=ri+it;else if(r.indexOf("e-txt")>-1){u==this._getLocalizedLabels("Measures")?(o=this._getLocalizedLabels("MeasureEditor"),e="<p class='e-editorPara'>"+this._getLocalizedLabels("Measures")+"<\/p>"):(this._isOptionSearch=!1,this._isEditorDrillPaging=!1,this._currentFilterList={},this._editorSearchTreeData=[],this._editorDrillTreeData={},this._editorDrillTreePageSettings={},this._lastSavedTree=[],o=this._getLocalizedLabels("MemberEditor"),e=this._hierarchyCaption!=this._getLocalizedLabels("KPIs")&&this.model.enableAdvancedFilter&&!p?"":"<p class='e-editorPara'>"+this._hierarchyCaption+"<\/p>");ht="";lt="";n(vt).appendTo(yt);u!=this._getLocalizedLabels("Measures")&&(ui=t.buildTag("div.e-checkAll e-icon").attr("role","button").attr("aria-label","checkall")[0].outerHTML,fi=t.buildTag("div.e-unCheckAll e-icon").attr("role","button").attr("aria-label","uncheckall")[0].outerHTML,lt=t.buildTag("div.e-checkOptions",ui+fi,{height:"19px",margin:"10px 0px 0px 31px"})[0].outerHTML,ht=t.buildTag("div.e-memberSearchEditorDiv",t.buildTag("input#"+this._id+"_searchEditorTreeView.searchEditorTreeView").attr("type","text")[0].outerHTML+(!t.isNullOrUndefined(c)&&u!=this._getLocalizedLabels("Measures")&&this.model.enableMemberEditorPaging&&n.parseJSON(c).length>=this.model.memberEditorPageSize?t.buildTag("span.e-icon e-searchEditorTree",{})[0].outerHTML:""),{padding:this.model.enableAdvancedFilter?"5px 0px 0px 0px":0})[0].outerHTML);u==this._getLocalizedLabels("Measures")?(this._isMembersFiltered=!1,ct=this._createMeasureEditor(f)):ct=t.buildTag("div#"+this._id+"_editorTreeView.e-editorTreeView")[0].outerHTML;var nu=t.buildTag("div.e-memberEditorDiv",(this._selectedFieldName!=""||u==this._getLocalizedLabels("KPIs")?lt:"")+ct)[0].outerHTML,at="",ei="";this._selectedFieldName!=""&&!p&&this.model.enableAdvancedFilter?(ei=t.buildTag("div.e-ddlGroupWrap",this._getLocalizedLabels("SelectField")+":"+t.buildTag("input#"+this._id+"_GroupLabelDrop.groupLabelDrop").attr("type","text")[0].outerHTML,{})[0].outerHTML,at=t.buildTag("div.e-filterElementTag")[0].outerHTML,oi=t.Pivot.createAdvanceFilterTag({action:"filterTag"},this),wt=n.map(this.element.find(".e-treeview li[data-tag='"+this._selectedFieldName+"'] li"),function(t){return{text:n(t).text(),value:n(t).attr("data-tag")}})):delete this._selectedLevelUniqueName;vt=t.buildTag("div",(at!=""&&!p&&this.model.enableAdvancedFilter?ei+t.buildTag("div.e-seperator",{},{padding:"2px"},d)[0].outerHTML+at+t.buildTag("div.e-seperator",{},{padding:"2px"})[0].outerHTML:"")+ht+nu)[0].outerHTML;yt=t.buildTag("div#"+this._id+"_EditorDiv.e-editorDiv",vt,{"margin-left":this.model.enableAdvancedFilter?"5px":"0px"})[0].outerHTML;e+=yt;bt=t.buildTag("div.e-memberPager",t.buildTag("div#"+this._id+"_NextpageDiv.e-nextPageDiv",t.buildTag("div.e-icon e-media-backward_01 e-firstPage",{})[0].outerHTML+t.buildTag("div.e-icon e-arrowhead-left e-prevPage",{})[0].outerHTML+t.buildTag("input.e-memberCurrentPage#"+this._id+"_memberCurrentPage",{},{width:"20px",height:"10px"})[0].outerHTML+t.buildTag("span.e-memberPageCount#"+this._id+"_memberPageCount")[0].outerHTML+t.buildTag("div.e-icon e-arrowhead-right e-nextPage",{})[0].outerHTML+t.buildTag("div.e-icon e-media-forward_01 e-lastPage",{})[0].outerHTML)[0].outerHTML,{})[0].outerHTML;et=t.buildTag("div.e-memberSearchPager",t.buildTag("div#"+this._id+"_NextSearchpageDiv.e-nextPageDiv",t.buildTag("div.e-icon e-media-backward_01 e-firstPage",{})[0].outerHTML+t.buildTag("div.e-icon e-arrowhead-left e-prevPage",{})[0].outerHTML+t.buildTag("input.e-memberCurrentSearchPage#"+this._id+"_memberCurrentSearchPage",{},{width:"20px",height:"10px"})[0].outerHTML+t.buildTag("span.e-memberSearchPageCount#"+this._id+"_memberSearchPageCount")[0].outerHTML+t.buildTag("div.e-icon e-arrowhead-right e-nextPage",{})[0].outerHTML+t.buildTag("div.e-icon e-media-forward_01 e-lastPage",{})[0].outerHTML)[0].outerHTML,{}).css("display","none")[0].outerHTML;ot=t.buildTag("div.e-memberDrillPager",t.buildTag("div#"+this._id+"_NextDrillpageDiv.e-nextPageDiv",t.buildTag("div.e-icon e-media-backward_01 e-firstPage",{})[0].outerHTML+t.buildTag("div.e-icon e-arrowhead-left e-prevPage",{})[0].outerHTML+t.buildTag("input.e-memberCurrentDrillPage#"+this._id+"_memberCurrentDrillPage",{},{width:"20px",height:"10px"})[0].outerHTML+t.buildTag("span.e-memberDrillPageCount#"+this._id+"_memberDrillPageCount")[0].outerHTML+t.buildTag("div.e-icon e-arrowhead-right e-nextPage",{})[0].outerHTML+t.buildTag("div.e-icon e-media-forward_01 e-lastPage",{})[0].outerHTML)[0].outerHTML,{}).css("display","none")[0].outerHTML;st=t.buildTag("div.e-linkOuterPanel",t.buildTag("span.e-infoImg e-icon","",{}).css({float:"left","margin-top":"4px","font-size":"16px"})[0].outerHTML+t.buildTag("a.e-linkPanel",this._getLocalizedLabels("NotAllItemsShowing")).css({display:"inline-block","margin-left":"3px","margin-top":"4px"})[0].outerHTML,{}).css({display:"none","margin-left":"3px","margin-left":y?"6px":"0px"})[0].outerHTML}if(r.split(" ")[0]=="mdx"?(d="<button id="+this._id+"_CancelBtn class='e-dialogCancelBtn' title="+this._getLocalizedLabels("OK").replace(/(<([^>]+)>)/ig,"")+" >"+this._getLocalizedLabels("OK")+"<\/button>",tt=t.buildTag("div.e-dialogFooter",d)[0].outerHTML,n(ft).appendTo("#"+this._id),n(e+tt).appendTo(this.element.find(".e-clientDialog")),this.element.find(".e-clientDialog").ejDialog({enableRTL:this.model.enableRTL,width:500,target:"#"+this._id,enableResize:!1,close:t.proxy(t.Pivot.closePreventPanel,this)})):(si="<button id="+this._id+"_OKBtn class='e-dialogOKBtn' title="+(r.split(" ")[0]=="RemoveDBReport"?this._getLocalizedLabels("Remove"):this._getLocalizedLabels("OK").replace(/(<([^>]+)>)/ig,""))+">"+(r.split(" ")[0]=="RemoveDBReport"?this._getLocalizedLabels("Remove"):this._getLocalizedLabels("OK"))+"<\/button>",d="<button id="+this._id+"_CancelBtn class='e-dialogCancelBtn' title="+this._getLocalizedLabels("Cancel").replace(/(<([^>]+)>)/ig,"")+">"+this._getLocalizedLabels("Cancel")+"<\/button>",tt=t.buildTag("div.e-dialogFooter",si+(r.split(" ")[0]=="e-removeReportImg"&&this.reportsCount==1?"":d))[0].outerHTML,n(ft).appendTo("#"+this._id),n(e+(!t.isNullOrUndefined(c)&&u!=this._getLocalizedLabels("Measures")&&this.model.enableMemberEditorPaging?n.parseJSON(c).length>=this.model.memberEditorPageSize?ot+et+bt:ot+et:st)+tt).appendTo(this.element.find(".e-clientDialog")),r.split(" ")[0]=="e-SortFilterDlg"?this.element.find(".e-clientDialog").ejDialog({enableRTL:this.model.enableRTL,width:"auto",target:"#"+this._id,cssClass:"e-SortFilterDlg",enableResize:!1,close:t.proxy(t.Pivot.closePreventPanel,this)}):this.element.find(".e-clientDialog").ejDialog({enableRTL:this.model.enableRTL,width:"auto",target:"#"+this._id,enableResize:!1,close:t.proxy(t.Pivot.closePreventPanel,this)}),r.split(" ")[0]=="e-removeReportImg"&&(this.element.find(".e-clientDialog").css("min-height","60px"),this.element.find("#"+this._id+"_ClientDialog_wrapper").css("min-height","100px"))),this.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog) .e-titlebar").prepend(t.buildTag("div",o,{display:"inline"})[0].outerHTML)[0].setAttribute("data-tag",s),this.element.find(".reportName, .renameReport").ejMaskEdit({width:"149px",textAlign:this.model.enableRTL?"right":"left"}),this.element.find(".e-reportDlg .e-mask").css("padding-left","6px"),this.element.find(".e-memberSearchEditorDiv .e-mask").addClass("e-dialogInput"),this.element.find(".e-prevPage, .e-firstPage").addClass("e-pageDisabled"),this.element.find(".e-nextPage, .e-lastPage").addClass("e-pageEnabled"),this.element.find(".e-dialogOKBtn, .e-dialogCancelBtn").ejButton({type:t.ButtonType.Button}),n(".sortfilterTab").ejTab({enableRTL:this.model.enableRTL}),n(".reportNameList").ejDropDownList({dataSource:u.split("__"),enableRTL:this.model.enableRTL,width:"150px",height:"20px",create:function(){n(this.wrapper.find(".e-input")).focus(function(){n(this).blur()})}}),r.indexOf("e-txt")>-1&&this.element.find("#"+this._id+"_searchEditorTreeView").ejMaskEdit({name:"inputbox",width:"100%",inputMode:t.InputMode.Text,watermarkText:this._getLocalizedLabels("Search")+" "+this._hierarchyCaption,maskFormat:"",textAlign:this.model.enableRTL?"right":"left",change:t.proxy(function(n){t.Pivot._searchEditorTreeNodes(n,this)},this)}),this.element.find(".e-filterElementTag").ejMenu({fields:{dataSource:oi,id:"id",parentId:"parentId",text:"text",spriteCssClass:"spriteCssClass"},menuType:t.MenuType.NormalMenu,width:"100%",height:"25px",enableRTL:this.model.enableRTL,orientation:t.Orientation.Vertical,click:t.proxy(this._filterElementClick,this)}),this.element.find(".groupLabelDrop").ejDropDownList({width:"100%",height:"25px",enableRTL:this.model.enableRTL,dataSource:wt,fields:{id:"id",text:"text",value:"value"},selectedIndices:[0],change:t.proxy(this._groupLabelChange,this),create:function(){n(this.wrapper.find(".e-input")).focus(function(){n(this).blur()})}}),n(".filterFrom,.filterTo").ejMaskEdit({width:"80px",height:"20px",textAlign:this.model.enableRTL?"right":"left"}),n(".e-measuresList").ejDropDownList({dataSource:u.split("||")[0].split("__"),enableRTL:this.model.enableRTL,width:"80%",height:"20px",create:function(){n(this.wrapper.find(".e-input")).focus(function(){n(this).blur()})}}),n(".fMeasuresList").ejDropDownList({dataSource:u.split("||")[0].split("__"),width:"80%",enableRTL:this.model.enableRTL,height:"20px",create:function(){n(this.wrapper.find(".e-input")).focus(function(){n(this).blur()})}}),n(".e-filterCondition").ejDropDownList({change:"_onActiveConditionChange",enableRTL:this.model.enableRTL,dataSource:[{option:"EqualTo",value:this._getLocalizedLabels("EqualTo")},{option:"NotEquals",value:this._getLocalizedLabels("NotEquals")},{option:"GreaterThan",value:this._getLocalizedLabels("GreaterThan")},{option:"GreaterThanOrEqualTo",value:this._getLocalizedLabels("GreaterThanOrEqualTo")},{option:"LessThan",value:this._getLocalizedLabels("LessThan")},{option:"LessThanOrEqualTo",value:this._getLocalizedLabels("LessThanOrEqualTo")},{option:"Between",value:this._getLocalizedLabels("Between")},{option:"NotBetween",value:this._getLocalizedLabels("NotBetween")},],fields:{text:"value",value:"option"},width:"80%",height:"20px",create:function(){n(this.wrapper.find(".e-input")).focus(function(){n(this).blur()})}}),this.element.find(".e-filterCondition").ejDropDownList("option","change",t.proxy(this._onActiveConditionChange,this)),this._dllSortMeasure=n(".e-measuresList").data("ejDropDownList"),this._dllFilterCondition=n(".e-filterCondition").data("ejDropDownList"),this._dllfMeasuresList=n(".fMeasuresList").data("ejDropDownList"),v!=""&&(this.element.find(".e-radioBtnAsc")[0].checked=v[0]=="ASC"?!0:!1,this.element.find(".e-radioBtnDesc")[0].checked=v[0]=="DESC"?!0:!1,this.element.find(".e-sortDisable")[0].checked=!(this.element.find(".e-sortEnable")[0].checked=!0),this.element.find(".e-preserveHrchy")[0].checked=v[1]=="PH"?!0:!1,this._isSorted=!0,this._dllSortMeasure.setModel({value:v[2]})),h!=""&&(this.element.find(".e-filterEnable")[0].checked=!0,this._dllfMeasuresList.setModel({value:h[0]}),this._dllFilterCondition.setModel({value:h[1]}),this.element.find(".filterFrom").val(h[2]),h[3]!=i&&h[3]!=""&&(h[1]=="Between"||h[1]=="NotBetween")?this.element.find(".filterTo").val(h[3]):n(".filterTo").attr("disabled","disabled"),this._isFiltered=!0),this.element.find(".e-sortDisable")[0]!=i&&this.element.find(".e-sortDisable")[0].checked==!0&&(n(".measuresList_wrapper,.e-radioBtnAsc, .e-radioBtnDesc, .e-preserveHrchy").attr("disabled","disabled"),n(".e-measureListLbl, .e-orderLbl, .e-radioBtnAscLbl, .e-radioBtnDescLbl, .e-preserveHrchyLbl").addClass("e-sortFilterDisable"),this._dllSortMeasure.disable()),this.element.find(".e-filterDisable")[0]!=i&&this.element.find(".e-filterDisable")[0].checked==!0&&(n(".filterFrom, .filterTo").attr("disabled","disabled"),n(".filterMeasureListLbl, .e-conditionLbl, .e-filterValueLbl, .e-filterBtw").addClass("e-sortFilterDisable"),this._dllFilterCondition.disable(),this._dllfMeasuresList.disable()),this.element.find(".e-widget-content").height("auto"),y=!1,this._excelFilterInfo.length>0){var tu=this.element.find(".cubeSelector").length>0?this.element.find(".cubeSelector").data("ejDropDownList").model.value:"",it=this.element.find("#"+this._id+"_reportList").data("ejDropDownList").model.value,hi=this._selectedLevelUniqueName,ci=this._selectedFieldName;this.model.analysisMode==t.Pivot.AnalysisMode.Olap?n.map(this._excelFilterInfo,function(n){n.cubeName==tu&&n.report==it&&(n.hierarchyUniqueName==ci||n.levelUniqueName==hi)&&(y=!0)}):n.map(this._excelFilterInfo,function(n){n.report==it&&(n.hierarchyUniqueName==ci||n.levelUniqueName==hi)&&(y=!0)})}if(f!=""&&f!=i&&u!=this._getLocalizedLabels("Measures")){if(g=null,!y&&this._currentReportItems.length!=0)for(rt=0;rt<this._currentReportItems.length;rt++)if(this._currentReportItems[rt]==this._currentItem){g=JSON.parse(JSON.stringify(this._treeViewData[this._currentItem]));break}g!=null&&y||(g=n.parseJSON(c));this._editorTreeData=n.parseJSON(c);this.model.enableMemberEditorPaging&&u!=this._getLocalizedLabels("Measures")&&this.element.find(".e-nextPageDiv").length>0&&(li=this._editorTreeData.length,a=li/this.model.memberEditorPageSize,a!=Math.round(a)&&(a=parseInt(a)+1),this._memberPageSettings.currentMemeberPage==a&&this.element.find(".e-nextPage, .e-lastPage").addClass("disabled"),this.element.find(".e-memberPageCount").html("/ "+a),this.element.find(".e-memberCurrentPage").val(this._memberPageSettings.currentMemeberPage),this._memberPageSettings.currentMemeberPage>1?this.element.find(".e-prevPage, .e-firstPage").removeClass("e-pageDisabled").addClass("e-pageEnabled"):this.element.find(".e-prevPage, .e-firstPage").removeClass("e-pageEnabled").addClass("e-pageDisabled"),this._memberPageSettings.currentMemeberPage==parseInt(n.trim(this.element.find(".e-memberPageCount").text().split("/")[1]))?this.element.find(".e-nextPage, .e-lastPage").removeClass("e-pageEnabled").addClass("e-pageDisabled"):this.element.find(".e-nextPage, .e-lastPage").removeClass("e-pageDisabled").addClass("e-pageEnabled"));this._appendTreeViewData(this.model.enableMemberEditorPaging&&this.element.find(".e-nextPageDiv").length>0?t.DataManager(this._editorTreeData).executeLocal(t.Query().where("pid","equal",null||i).page(this._memberPageSettings.currentMemeberPage,this.model.memberEditorPageSize)):g,p);this.model.enableAdvancedFilter||this.element.find(".e-memberEditorDiv").css({height:"286px"});this.model.enableAdvancedFilter&&(this.element.find("#"+this._id+"_clientDialog_wrapper").addClass("e-advancedFilterDlg"),this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.element.find("#"+this._id+"_clientDialog_wrapper").addClass("e-advanceFltrElement"))}this.model.enableMemberEditorSorting&&(this._sortType="",t.Pivot._separateAllMember(this));u==="Measures"&&this.element.find(".e-memberEditorDiv").css({"max-height":"286px",overflow:"auto"});this._dialogOKBtnObj=this.element.find(".e-dialogOKBtn").data("ejButton");this.element.find("#"+this._id+"_ClientDialog_closebutton").attr("title",this._getLocalizedLabels("Close"));this._unWireEvents();this._wireEvents();this._isTimeOut=!1;this._waitingPopup.hide()},_appendTreeViewData:function(i,r){var e,f,u,o;if(!this._isSearchApplied&&this._editorSearchTreeData.length==0&&i.length>0&&(this._lastSavedTree=i),this.element.find(".e-editorTreeView").ejTreeView({showCheckbox:!0,loadOnDemand:!0,enableRTL:this.model.enableRTL,height:this.model.enableMemberEditorSorting?"235px":"initial",nodeUncheck:function(i){var r,f,u,e;t.isNullOrUndefined(i.event)||i.event==""||(r=n(this.element).parents(".e-pivotclient").data("ejPivotClient"),f=n(i.currentElement).parents(".e-treeview"),r._isMembersFiltered=!0,n(i.currentElement).attr("id")=="SearchFilterSelection"&&(u=n(f).find("li:not([data-isItemSearch='true'])"),r._isSelectSearchFilter=!1,r._isOptionSearch?u.length>0&&n(u).each(function(t,i){n(i).attr("id")!="SearchFilterSelection"&&n(f).ejTreeView("uncheckNode",n(i))}):u.length>0&&n(r._editorTreeData).each(function(t,i){for(var r=0;r<u.length;r++)if(u[r].id!=i.id)i.checkedStatus=!1;else{i.checkedStatus=n(u[r]).find(".checked").length>0?!0:!1;break}})),n(f).find(".e-checkbox:checked").length!=0||r.model.enableMemberEditorPaging||r.element.find(".e-dialogOKBtn").data("ejButton").disable(),r.model.operationalMode!=t.Pivot.OperationalMode.ClientMode&&(t.Pivot._memberPageNodeUncheck(i,r),e=!0,n(r._editorTreeData).each(function(n,t){if(t.checkedStatus)return e=!1,!1}),e&&r._editorTreeData.length>0?(r.element.find(".e-dialogOKBtn").data("ejButton").disable(),r.element.find(".e-dialogOKBtn").removeAttr("disabled")):(r.element.find(".e-dialogOKBtn").data("ejButton").enable(),r.element.find(".e-dialogOKBtn").removeAttr("disabled"))))},nodeCheck:function(i){var r,e,o,s,f,u,h;if(!t.isNullOrUndefined(i.event)&&i.event!=""){if(r=n(this.element).parents(".e-pivotclient").data("ejPivotClient"),e=n(i.currentElement).parents(".e-treeview"),r._isMembersFiltered=!0,r.element.find(".e-editorPara").text()==r._getLocalizedLabels("KPIs")){for(o=!0,s=r.element.find(".e-editorTreeView ul:eq(0)"),f=0;f<s.children("li").length;f++)if(s.children("li:eq("+f+")").find(".e-check-small").attr("aria-checked")=="false"){o=!1;break}o?(r.element.find(".e-dialogOKBtn").data("ejButton").enable(),r.element.find(".e-dialogOKBtn").removeAttr("disabled")):(r.element.find(".e-dialogOKBtn").data("ejButton").disable(),r.element.find(".e-dialogOKBtn").attr("disabled","disabled"))}n(i.currentElement).attr("id")=="SearchFilterSelection"&&(r._isSelectSearchFilter=!0,u=n(e).find("li:not([data-isItemSearch='true'])"),r._isOptionSearch?u.length>0&&n(u).each(function(i,u){n(u).attr("id")!="SearchFilterSelection"&&!t.isNullOrUndefined(r._currentFilterList[n(u).attr("id")])&&r._currentFilterList[n(u).attr("id")].checkedStatus&&n(e).ejTreeView("checkNode",n(u))}):u.length>0&&n(r._editorTreeData).each(function(i,f){for(var e=0;e<u.length;e++)if(u[e].id!=f.id)t.isNullOrUndefined(r._currentFilterList)||(f.checkedStatus=r._currentFilterList[n(f).attr("id")].checkedStatus);else{f.checkedStatus=n(u[e]).find(".checked").length>0?!0:!1;break}}));r.model.enableMemberEditorPaging&&r._editorTreeData.length>0?n(r._editorTreeData).each(function(n,t){if(t.checkedStatus)return r.element.find(".e-dialogOKBtn").data("ejButton").enable(),r.element.find(".e-dialogOKBtn").removeAttr("disabled"),!1}):(r.element.find(".e-dialogOKBtn").data("ejButton").enable(),r.element.find(".e-dialogOKBtn").removeAttr("disabled"));r.model.operationalMode!=t.Pivot.OperationalMode.ClientMode&&(t.Pivot._memberPageNodeCheck(i,r),h=!0,n(r._editorTreeData).each(function(n,t){if(t.checkedStatus)return h=!1,!1}),h&&r._editorTreeData.length>0?(r.element.find(".e-dialogOKBtn").data("ejButton").disable(),r.element.find(".e-dialogOKBtn").removeAttr("disabled")):(r.element.find(".e-dialogOKBtn").data("ejButton").enable(),r.element.find(".e-dialogOKBtn").removeAttr("disabled")))}},beforeDelete:function(n){if(!t.isNullOrUndefined(n.event)&&n.event.type=="keydown"&&n.event.originalEvent.key.toLowerCase()=="delete")return!1},cssClass:"pivotTreeViewDragedNode",height:n(".e-memberEditorDiv").height(),fields:{id:"id",hasChild:"hasChildren",parentId:"pid",text:"name",isChecked:"checkedStatus",expanded:"expanded",dataSource:t.Pivot._showEditorLinkPanel(i,this,this)},beforeExpand:t.proxy(this._onNodeExpand,this),beforeCollapse:t.proxy(t.Pivot._onNodeCollapse,this)}),e=this,this.model.enableMemberEditorPaging&&jQuery.isEmptyObject(this._currentFilterList)?this._currentFilterList=JSON.parse(JSON.stringify(this._editorTreeData)):!this.model.enableMemberEditorPaging&&jQuery.isEmptyObject(this._currentFilterList)&&n(i).each(function(n,t){e._currentFilterList[t.id]=t}),f=this.element.find(".e-editorTreeView").find("li"),this._isSearchApplied)for(u=0;u<f.length;u++)t.isNullOrUndefined(n(f[u]).attr("id"))||n(f[u]).attr("data-tag",t.DataManager(i).executeLocal(t.Query().where("id","equal",n(f[u]).attr("id")))[0].tag);else for(u=0;u<f.length;u++)n(f[u]).attr("data-tag",i[u].tag);this._memberTreeObj=this.element.find(".e-editorTreeView").data("ejTreeView");o=n.grep(i,function(n){if(n.hasChildren==!0)return n}).length>0;o||this.element.find(".e-memberEditorDiv").addClass("noChildNode");this.model.enableMemberEditorSorting?this.element.find(".e-editorTreeView").height(235):this.element.find(".e-editorTreeView").height(256);this.element.find(".e-editorPara").text()!=this._getLocalizedLabels("KPIs")&&!r&&this.model.enableAdvancedFilter&&!t.isNullOrUndefined(this._memberTreeObj)&&this.element.find(".groupLabelDrop").length>0?(t.isNullOrUndefined(this.element.find(".e-treeview li[data-tag='"+this._selectedLevelUniqueName+"']").attr("labelFiltering"))||this._memberTreeObj.element.find("."+this.element.find(".e-treeview li[data-tag='"+this._selectedLevelUniqueName+"']").attr("labelFiltering").split("--")[1].replace(" ","")+" a").append(t.buildTag("div.e-activeFilter").addClass("e-icon")[0].outerHTML),this.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog) .e-titlebar").hide(),this._groupLabelChange({selectedValue:this.element.find(".groupLabelDrop").data("ejDropDownList").model.value||this.element.find(".groupLabelDrop").data("ejDropDownList").model.dataSource[0].value}),this.element.find(".e-memberEditorDiv").height(286),this.model.enableMemberEditorSorting?this.element.find(".e-editorTreeView").height(235):this.element.find(".e-editorTreeView").height(256)):this.element.find(".e-memberEditorDiv").height(286)},_createMeasureEditor:function(t){var u,f,r;for(u=t[0]!=i?n.parseJSON(t[0].Value):t.d!=i?n.parseJSON(t.d[0].Value):n.parseJSON(t.EditorTreeInfo),f="",r=0;r<u.length;r++)f+='<div id="'+u[r].uniqueName+'"class=e-measureEditor>'+u[r].name+"<span class='e-removeMeasure e-icon' role='button' aria-label='remove'><\/span><\/div>";return f},_onNodeExpand:function(r){var f=r.targetElement,o,c,s,e,u,h;for(this.model.enableMemberEditorPaging||(this._isSearchApplied=!1),o=t.buildTag("span.nodeExpand e-load e-icon")[0].outerHTML,c=n(f).parents("li").first().children().find("li").length,jQuery.each(this._editorTreeData,function(n,t){if(t.id==r.id)return t.expanded=!0,t.isChildMerged=!0,!1}),u=0;u<this._memberTreeObj.dataSource().length;u++)if(this._memberTreeObj.dataSource()[u].parentId==r.id||this._memberTreeObj.dataSource()[u].pid==r.id){r.isChildLoaded=!0;t.Pivot.closePreventPanel(this);return}if(!t.isNullOrUndefined(this)&&t.DataManager(this._editorTreeData).executeLocal(t.Query().where("pid","equal",r.id)).length>0){r.isChildLoaded=!0;t.Pivot.closePreventPanel(this);this.pNode=f;this._fetchChildNodeSuccess({ChildNodes:JSON.stringify(t.DataManager(this._editorTreeData).executeLocal(t.Query().where("pid","equal",r.id))),customObject:this.model.customObject});return}if(!r.isChildLoaded){if(n(f).parents("li").first().prepend(o),s=n(f).parent().find("input.e-checkbox").prop("checked"),e=n(f).parents("li").first().attr("data-tag"),e==i)for(u=0;u<this._memberTreeObj.dataSource().length;u++)if(this._memberTreeObj.dataSource()[u].id==r.id){e=this._memberTreeObj.dataSource()[u].tag;n(r.currentElement[0]).attr("data-tag",this._memberTreeObj.dataSource()[u].tag);break}this.pNode=f;this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:"memberExpanded",element:this.element,customObject:this.model.customObject});h=JSON.stringify(this.model.customObject);this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.memberExpand,JSON.stringify({action:"memberExpanded",checkedStatus:s,parentNode:n(f).parents("li")[0].id,tag:e,dimensionName:this._dimensionName,cubeName:this.currentCubeName,olapReport:this.currentReport,clientReports:this.reports,customObject:h}),this._fetchChildNodeSuccess)}},_onActiveConditionChange:function(t){t.selectedValue=="Between"||t.selectedValue=="NotBetween"?(n(".filterTo").removeAttr("disabled"),n(".e-filterBtw").removeClass("e-sortFilterDisable")):(n(".filterTo").attr("disabled","disabled"),this.element.find(".filterTo").val(""),n(".e-filterBtw").addClass("e-sortFilterDisable"))},_reSizeHandler:function(){if(!t.isNullOrUndefined(this)){if(this._parentHeight=n("#"+this._id).parent().height(),this.element.height(this._parentHeight),this._calculateHeight(),this.model.isResponsive&&this._parentHeight!=n("#"+this._id).parent().height()){var i=n("#"+this._id).parent().height()-this._parentHeight;this.element.height(this._parentHeight-i);this._calculateHeight()}this._parentElwidth=n("#"+this._id).parent().width();(this._parentElwidth<850||this.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&this._parentElwidth<910)&&this._rwdToggleCollapse();(this._parentElwidth>850||this.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&this._parentElwidth>910)&&this._rwdToggleExpand();this.model.isResponsive&&this.model.enableSplitter&&(this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this.element.find(".e-serversplitresponsive").data("ejSplitter").refresh():this.element.find(".e-splitresponsive").data("ejSplitter").refresh());this._overflow();this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this.chartObj=null,this.chartObj=this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),n("#"+this._pivotChart._id+"Container").height(this._chartHeight),t.isNullOrUndefined(this.chartObj)||(this.chartObj.model.size.height=this._chartHeight),t.isNullOrUndefined(this.chartObj)&&this.model.enablePivotTreeMap&&!t.isNullOrUndefined(this.otreemapObj)&&(this.chartObj=this.element.find("#"+this.otreemapObj._id+"TreeMapContainer").data("ejTreeMap")),t.isNullOrUndefined(this.chartObj)||(this.chartObj.sfType.split(".").pop().toLowerCase()=="treemap"?this.otreemapObj._treeMap.refresh():this.chartObj.redraw()));this._isCollapseCB&&this._collapseCubeBrowser();this.displayMode()!=t.PivotClient.DisplayMode.ChartOnly&&this._pivotGrid.element.length>0&&this._pivotGrid._applyFrozenHeaderWidth(this._pivotGrid._JSONRecords)}},_chartDrillSuccess:function(r){var h,e,f,s,u;if(this.model.analysisMode==t.Pivot.AnalysisMode.Pivot)if(this._isGridDrillAction)this._isGridDrillAction=!1;else{this._isGridDrillAction=!1;this._isChartDrillAction=!0;h=this._pivotChart._selectedItem;e=this._pivotChart._drillAction;e=="drilldown"?t.isNullOrUndefined(this._pivotChart._labelCurrentTags.expandedMembers)||this._drillParams.indexOf(this._pivotChart._labelCurrentTags.expandedMembers.join(">#>"))!=-1||this._drillParams.push(this._pivotChart._labelCurrentTags.expandedMembers.join(">#>")):this._drillParams=n.grep(this._drillParams,function(n){return n.indexOf(h)<0});var o=this._pivotChart.model.enableMultiLevelLabels?r.drilledMember.split(">#>"):e=="drilldown"?this._pivotChart._labelCurrentTags.expandedMembers:this._pivotChart._currentDrillInfo,c=0,l=this._pivotGrid._rowCount;for(f=0;f<o.length;f++)s=f==o.length-1&&e=="drilldown"?this._pivotGrid.element.find(".e-pivotGridTable th.summary[data-p^='"+f+"']"):this._pivotGrid.element.find("th.rowheader").length>0?this._pivotGrid.element.find(".e-pivotGridTable th.rowheader[data-p^='"+f+"']"):n(this._pivotGrid._table).find("th.rowheader[data-p^='"+f+"']"),u=n.grep(s,function(t){return n(t).clone().children().remove().end().text()==o[f]}),u=n.grep(u,function(t){return parseInt(n(t).attr("data-p").split(",")[1])>=c&&parseInt(n(t).attr("data-p").split(",")[1])<=l}),f==o.length-1?n(u).find("."+(e=="drilldown"?"e-expand":"e-collapse")).trigger("click"):(c=parseInt(n(u).attr("data-p").split(",")[1]),l=c+(t.isNullOrUndefined(n(u).find("span").attr("data-tag"))?0:parseInt(n(u).find("span").attr("data-tag")))+u[0].rowSpan);o.length==0&&(s=this._pivotGrid.element.find(".e-pivotGridTable th.rowheader[data-p^='0']"),u=n.grep(s,function(t){return n(t).clone().children().remove().end().text()==h}),n(u).find(".e-collapse").trigger("click"))}else this._pivotChart._startDrilldown&&this._pivotGrid!=null&&this._pivotGrid!=i?this._renderPivotGrid(this._pivotChart._selectedTagInfo):this._pivotGrid!=i&&this._pivotGrid.element.find("table")=="block"&&(this._isTimeOut=!1,this._waitingPopup.hide())},_treemapDrillSuccess:function(){this.otreemapObj._startDrilldown&&this._pivotGrid!=null&&this._pivotGrid!=i?this._renderPivotGrid(this.otreemapObj._selectedTagInfo):this._pivotGrid!=i&&this._pivotGrid.element.find("table")=="block"&&(this._isTimeOut=!1,this._waitingPopup.hide());t.isNullOrUndefined(this.chartObj)||(this.model.displaySettings.enableFullScreen||this.enableTogglePanel()&&this.chartObj.sfType.split(".").pop().toLowerCase()=="treemap")&&(n("#"+this.otreemapObj._id+"TreeMapContainer").css({width:"100%"}),this.otreemapObj._treeMap.refresh())},_gridDrillSuccess:function(r){var s,c,l,a,f,o,e,u,h;if(this.model.analysisMode==t.Pivot.AnalysisMode.Pivot)if(this._isChartDrillAction)this._isChartDrillAction=!1;else{if(this._isGridDrillAction=!0,this._isChartDrillAction=!1,r.axis=="rowheader"&&this.displayMode()!=t.PivotClient.DisplayMode.GridOnly){r.drillAction=="drilldown"?this._drillParams.push(r.drilledMember):this._drillParams=n.grep(this._drillParams,function(n){return n.indexOf(r.drilledMember)<0});s=r.drilledMember.split(">#>");this._pivotChart._labelCurrentTags.expandedMembers=s;r.drillAction=="drillup"&&(this._pivotChart._labelCurrentTags.expandedMembers=[],this._drillParams.length>0&&this._getDrilledMember(r.drillAction),s=this._pivotChart._labelCurrentTags.expandedMembers);s=this._pivotChart.model.enableMultiLevelLabels?r.drillAction=="drillup"?r.drilledMember.split(">#>").slice(0,-1):r.drilledMember.split(">#>"):s;this._pivotChart._drillAction=r.drillAction;this._pivotChart._drillParams=this._drillParams;try{c=JSON.parse(this.currentReport).Report}catch(v){c=this.currentReport}this._pivotChart.model.enableMultiLevelLabels?this._pivotChart._generateData({JsonRecords:this.getJSONRecords(),PivotReport:this.getOlapReport()}):(l=JSON.stringify(this.model.customObject),a={action:r.drillAction,currentReport:c,drilledSeries:JSON.stringify(s)+"-##-"+JSON.stringify(this.model.valueSortSettings),clientReports:this.reports,customObject:l},this._pivotChart.doAjaxPost("POST",this.model.url+"/"+this._pivotChart.model.serviceMethodSettings.drillDown,JSON.stringify(a),this._pivotChart.renderControlSuccess))}else this._isGridDrillAction=!1;return}if(this._pivotGrid._startDrilldown&&!t.isNullOrUndefined(this._pivotChart)){if(o=this._pivotGrid._drillCaption,this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this.chartObj=null,this.chartObj=this.model.displaySettings.enableFullScreen&&n("#"+this._id+"_maxView").length>0?n("#"+this._id+"_maxView").find("#"+this._pivotChart._id+"Container").data("ejChart"):this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(this.chartObj)&&this.model.enablePivotTreeMap&&!t.isNullOrUndefined(this.otreemapObj)&&(this.chartObj=this.element.find("#"+this.otreemapObj._id+"TreeMapContainer").data("ejTreeMap"))),u=this,!t.isNullOrUndefined(this.chartObj))if(this.chartObj.sfType.split(".").pop().toLowerCase()!="treemap")jQuery.each(this._pivotChart._labelCurrentTags,function(n,t){t!=i&&t.name==o&&(e=t.tag,f=t)}),this._pivotChart._drillAction=this._pivotGrid._drillAction,this._pivotChart._selectedItem=o,this._pivotGrid._drillAction!="drilldown"||t.isNullOrUndefined(f)?t.isNullOrUndefined(f)||(jQuery.each(this.element.find(".e-rowAxis").find("button"),function(n,t){t.innerHTML==f.tag.split("[")[1].split("]")[0]&&(u._pivotChart._dimensionIndex=n)}),jQuery.each(this._pivotChart._tagCollection,function(n,t){if(t!=i&&t.name==o)return u._pivotChart._selectedIndex=n+u._pivotChart._dimensionIndex,u._pivotChart._tagCollection.splice(n,u._pivotChart._tagCollection.length),!1})):this._pivotChart._tagCollection.push(f),e!=null&&e!=i?this._renderPivotChart(e,this._pivotGrid._drillAction):(this._isTimeOut=!1,this._waitingPopup.hide());else{for(h=0;h<this.otreemapObj.getJsonRecords().labelTags.length;h++)if(this.otreemapObj.getJsonRecords().labelTags[h].split("::")[2]==o){e=this.otreemapObj.getJsonRecords().labelTags[h];break}this.otreemapObj_currentAction=this._pivotGrid._drillAction;this.otreemapObj._selectedItem=o;this._pivotGrid._drillAction!="drilldown"||t.isNullOrUndefined(f)?t.isNullOrUndefined(f)||(jQuery.each(this.element.find(".e-rowAxis").find("button"),function(n,t){t.innerHTML==f.tag.split("[")[1].split("]")[0]&&(u.otreemapObj._dimensionIndex=n)}),jQuery.each(this.otreemapObj._tagCollection,function(n,t){if(t!=i&&t.name==o)return u.otreemapObj._selectedIndex=n+u.otreemapObj._dimensionIndex,u.otreemapObj._tagCollection.splice(n,u.otreemapObj._tagCollection.length),!1})):this.otreemapObj._tagCollection.push(f);e!=null&&e!=i?this._renderPivotTreeMap(e,this._pivotGrid._drillAction):(this._isTimeOut=!1,this._waitingPopup.hide())}}else n("#"+this._waitingPopup._id+"_WaitingPopup")[0].style.display=="block"&&(this._isTimeOut=!1,this._waitingPopup.hide())},_nodeDropped:function(r){var o=n(r.dropTarget).hasClass("e-pvtBtn")&&n(r.dropTarget).parent("div.e-splitBtn").length>0?n(r.dropTarget).parent("div.e-splitBtn"):n(r.dropTarget),u=this,l,f,e,a,c,h,v,y,s,p,w,b,k;if(o.prevObject!=i?o.prevObject.removeClass("targetAxis"):n(o).removeClass("targetAxis"),u.isDragging=!1,l=this._setSplitBtnTargetPos(r.event),o.prevObject!=i?o.prevObject.parents().find(".e-dialog").hide():n(o).parents().find(".e-dialog").hide(),f=null,n(o).hasClass("e-splitBtn"))f=n(o).parents("div:eq(0)").attr("class");else if(r.event.type=="touchend")f=o[0].className;else if(r.event.target!=i){if(r.event.target.className==i||r.event.target.className==null||n(r.event.target).parents(".e-chartContainer").length>0)return!1;f=r.event.target.className.toLowerCase().indexOf("e-splitBtn")>-1||n(r.event.target).hasClass("e-button")?n(o).attr("class"):r.event.target.className}else f=r.event.originalEvent.srcElement.className.toLowerCase().indexOf("e-splitBtn")>-1?o[0].className:r.event.originalEvent.srcElement.className;if(t.isNullOrUndefined(this._dimensionName)||(delete u._fieldMembers[this._dimensionName.split(":").length>1?this._dimensionName.split(":")[1]:this._dialogTitle],delete u._fieldSelectedMembers[this._dimensionName.split(":").length>1?this._dimensionName.split(":")[1]:this._dialogTitle]),s=f.indexOf("e-categoricalAxis")>f.indexOf("e-rowAxis")?f.indexOf("e-categoricalAxis")>f.indexOf("e-slicerAxis")?"Categorical":"Slicer":f.indexOf("e-rowAxis")>f.indexOf("e-slicerAxis")?"Series":f.indexOf("e-slicerAxis")>=0?"Slicer":null,e=n(r.droppedElement).parents("[data-tag='KPI']").length>0?n(r.droppedElement).text()=="Value"||n(r.droppedElement).text()=="Goal"||n(r.droppedElement).text()=="Status"||n(r.droppedElement).text()=="Trend"?n(r.droppedElement).attr("data-tag")+":"+n(r.droppedElement).parent().closest("li").attr("data-tag")+":"+this._getLocalizedLabels("KPIs"):n(r.droppedElement).attr("data-tag")+":"+this._getLocalizedLabels("KPIs"):n(r.droppedElement).attr("data-tag"),s=="Slicer"&&e!=null&&(e.indexOf("Measure")>-1||e.indexOf("KPIs")>-1)&&(this.element.find(".e-splitBtn[tag*=Measures]").length>1||this.element.find(".e-splitBtn[data-tag*='KPIs']").length>=1)||s=="Slicer"&&e.indexOf("Measure")>-1&&n(r.droppedElement).find(".e-calcMemberCDB").length>0&&this.element.find(".e-splitBtn[tag*=CalculatedMember][tag*=Measure]").length==1&&n(r.droppedElement).attr("data-tag").replace(/\[/g,"").replace(/\]/g,"")!=this.element.find(".e-splitBtn[data-tag*=CalculatedMember][data-tag*=Measure]").attr("data-tag").split(":")[1].split("::")[0]||s=="Slicer"&&e.indexOf("Measure")>-1&&n(r.droppedElement).find(".e-calcMemberCDB").length==0&&this.element.find(".e-splitBtn[data-tag*=CalculatedMember][data-tag*=Measure]").length==1||s=="Slicer"&&e.indexOf("Measure")>-1&&n(r.droppedElement).find(".e-calcMemberCDB").length==1&&this.element.find(".e-splitBtn[data-tag*=CalculatedMember][data-tag*=Measure]").length==0&&this.element.find(".e-splitBtn[data-tag*=Measure]").length==1)return a=(e.indexOf("KPIs")>-1||e.indexOf("Measure")>-1)&&this.element.find(".e-splitBtn[data-tag*='KPIs']").length>=1?this._getLocalizedLabels("KpiAlertMsg"):this._getLocalizedLabels("MultipleMeasure"),t.Pivot._createErrorDialog(a,this._getLocalizedLabels("Warning"),this),!1;for(c=this.element.find(".e-splitBtn"),h=0;h<c.length;h++)if(!t.isNullOrUndefined(n(r.droppedElement).attr("data-parentuniquename"))&&!t.isNullOrUndefined(n(n(c)[h]).attr("data-parenthierarchy"))&&n(n(c)[h]).attr("data-parenthierarchy")==n(r.droppedElement).attr("data-parentuniquename")){v=this._getLocalizedLabels("NamedSetAlert").replace("<Set 1>",n(n(c)[h]).find(".e-btn").text()).replace("<Set 2>",n(r.droppedElement).find(".e-text").text());y=t.buildTag("div.data-namedSetDialog#"+this._id+"_NamedSetDialog",t.buildTag("div.data-namedSetDialogContent",v)[0].outerHTML+t.buildTag("div",t.buildTag("button#"+this._id+"_OKBtn.e-okBtn","OK",{margin:"20px 0 10px 120px"}).attr("title","OK")[0].outerHTML+t.buildTag("button#"+this._id+"_CancelBtn.e-cancelBtn","Cancel",{margin:"20px 0 10px 10px"}).attr("title","Cancel")[0].outerHTML)[0].outerHTML).attr("title","Warning")[0].outerHTML;u.element.append(y);s=f.indexOf("e-categoricalAxis")>f.indexOf("e-rowAxis")?f.indexOf("e-categoricalAxis")>f.indexOf("e-slicerAxis")?"Categorical":"Slicer":f.indexOf("e-rowAxis")>f.indexOf("e-slicerAxis")?"Series":f.indexOf("e-slicerAxis")>=0?"Slicer":null;p=n(n(".e-splitBtn")[h]);u.element.find(".data-namedSetDialog").ejDialog({target:"#"+u._id,enableResize:!1,enableRTL:!1,width:"400px"});w=this;u.element.find(".e-okBtn,.e-cancelBtn").ejButton({type:t.ButtonType.Button,width:"70px"});u.element.find(".e-cancelBtn").on(t.eventType.click,function(){w.element.find(".e-dialog").remove()});u.element.find(".e-okBtn").on(t.eventType.click,function(){u.model.beforeServiceInvoke!=null&&u._trigger("beforeServiceInvoke",{action:"nodeDropped",element:u.element,customObject:u.model.customObject});var t=JSON.stringify(u.model.customObject),i=u.currentCubeName+"--"+n(r.droppedElement).attr("data-tag")+"--"+s+"--"+l+"##"+n(p).attr("data-tag");u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.nodeDropped,JSON.stringify({action:"nodeDroppedNamedSet",dropType:"TreeNode",nodeInfo:i,olapReport:u.currentReport,clientReports:u.reports,customObject:t}),u._nodeDroppedSuccess)});return 0}s!=null&&e!="Measures"&&e.lastIndexOf(".DF")!=e.length-3&&(s=="Slicer"&&(e.indexOf("Measures")>=0||n(r.droppedElement[0].childNodes[0]).find(".e-namedSetCDB").length>0)||(this._isTimeOut=!0,setTimeout(function(){u._isTimeOut&&u._waitingPopup.show()},800),this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:"nodeDropped",element:this.element,customObject:this.model.customObject}),b=JSON.stringify(this.model.customObject),k=u.currentCubeName+"--"+e+"--"+s+"--"+l,u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.nodeDropped,JSON.stringify({action:"nodeDropped",dropType:"TreeNode",nodeInfo:k,olapReport:u.currentReport,clientReports:u.reports,customObject:b}),u._nodeDroppedSuccess)));u._isRemoved=!0;u._isNodeOrButtonDropped=!0},_setSplitBtnTargetPos:function(t){var o="",e,r,u,f;if(t.event!=i&&t.event.type=="touchend"?(r=t.event.originalEvent.target!=null?n(t.event.originalEvent.target).parents(".e-splitBtn"):n(t.event.originalEvent.srcElement).parents(".e-splitBtn"),u=t.event.originalEvent.target!=null?t.event.originalEvent.target.className:t.event.originalEvent.srcElement.className):(r=t.originalEvent.target!=null?n(t.originalEvent.target).parents(".e-splitBtn"):n(t.originalEvent.srcElement).parents(".e-splitBtn"),u=t.originalEvent.target!=null?t.originalEvent.target.className:t.originalEvent.srcElement.className),r[0]||u!=i&&u!=null&&jQuery.type(u)=="string"&&u.indexOf("e-splitBtn")>-1)if(r=r[0]?r[0]:t.originalEvent.target!=null?t.originalEvent.target:t.originalEvent.srcElement,e=t.event!=i&&t.event.type=="touchend"?t.target:this._dropAxisClassName!=null&&this._dropAxisClassName!=i&&this._dropAxisClassName!=""?this.element.find("."+this._dropAxisClassName)[0]:r.parentNode,e!=i)for(f=0;f<n(e).find(".e-splitBtn").length;f++)n(e).find(".e-splitBtn")[f]==r&&(o=f);else this._dropAxisClassName="";return o},_cubeChanged:function(i){var r=this,u=-1,s=this.element.find("#"+this._id+"_reportList").data("ejDropDownList"),l=r.element.find("#"+this._id+"_cubeSelector").data("ejDropDownList"),h=n.extend(!0,{},this._slicerBtnTextInfo),f,e,o,c;if(h[s.selectedIndexValue]=this._fieldSelectedMembers,f={CubeName:this.currentCubeName,CurrentReport:this.currentReport,Reports:this.reports,ReportIndex:s.selectedIndexValue,slicerBtnTextInfo:h},n.map(this._repCol,function(n,t){n.CubeName==r.currentCubeName&&(u=t)}),u!=-1?this._repCol[u]=f:this._repCol.push(f),this._currentRecordName="",this._fieldMembers={},this._fieldSelectedMembers={},this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this.chartObj=null,this.chartObj=this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(this.chartObj)&&this.model.enablePivotTreeMap&&!t.isNullOrUndefined(this.otreemapObj)&&(this.chartObj=this.element.find("#"+this.otreemapObj._id+"TreeMapContainer").data("ejTreeMap"))),this.currentCubeName==i.text)return!1;this.currentCubeName=i.text;e="";o="";n.map(this._repCol,function(n){n.CubeName==r.currentCubeName&&(e=n.CurrentReport,o=n.Reports,r._reportIndex=n.ReportIndex)});r._isTimeOut=!0;setTimeout(function(){r._isTimeOut&&r._waitingPopup.show()},800);this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:"cubeChanged",element:this.element,customObject:this.model.customObject});c=JSON.stringify(this.model.customObject);this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.cubeChanged,JSON.stringify({action:"cubeChanged",cubeName:this.currentCubeName,olapReport:e,clientReports:o,customObject:c,clientParams:JSON.stringify(this.model.enableMeasureGroups)+"-"+this.model.enableKPI+"-"+this.model.sortCubeMeasures}),this._cubeChangedSuccess)},_measureGroupChanged:function(n){var t,i;this._isTimeOut=!0;t=this;setTimeout(function(){t._isTimeOut&&t._waitingPopup.show()},800);this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:"cubeChanged",element:this.element,customObject:this.model.customObject});i=JSON.stringify(this.model.customObject);this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.measureGroupChanged,JSON.stringify({action:"measureGroupChanged",measureGroupName:n.text,olapReport:this.currentReport,clientReports:this.reports,customObject:i}),this._measureGroupChangedSuccess)},_updateSlicerBtnText:function(i){var r=this.element.find("#"+this._id+"_reportList").data("ejDropDownList").selectedIndexValue,u=n.map(this._repCol,function(n){if(n.CubeName==i&&!t.isNullOrUndefined(n.slicerBtnTextInfo[r]))return n.slicerBtnTextInfo[r]});this._fieldSelectedMembers=u.length>0?u[0]:this._fieldSelectedMembers},_reportChanged:function(i){var r,u,f;if(this._fieldMembers={},this._fieldSelectedMembers={},r=this,this.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?r._updateSlicerBtnText(r.currentCubeName):this._pivotSchemaDesigner&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&(this._pivotSchemaDesigner._tempFilterData=[]),this.reportDropTarget=this.element.find("#"+this._id+"_reportList").data("ejDropDownList"),this.model.operationalMode==t.Pivot.OperationalMode.ClientMode)u=n.map(this._clientReportCollection,function(n){if(n.reportName==r.reportDropTarget.getValue())return n}),this.model.dataSource=u[0],this._currentItem!="Rename Report"&&(this.refreshControl(),this._pivotSchemaDesigner._refreshPivotButtons()),this._isTimeOut=!1,this._waitingPopup.hide();else if(this.model.analysisMode==t.Pivot.AnalysisMode.Pivot){if(!this._isReportListAction)return;r._isTimeOut=!0;setTimeout(function(){r._isTimeOut&&r._waitingPopup.show()},800);u=n.map(this._clientReportCollection,function(n){if(n.name==r.reportDropTarget.getValue())return n});this._currentReportName=u[0].name;this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:"toolbarOperation",element:this.element,customObject:this.model.customObject});f=JSON.stringify(this.model.customObject);this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.toolbarServices,JSON.stringify({action:"Change Report",args:JSON.stringify({currentReport:u[0].report}),customObject:f}),this._toolbarOperationSuccess)}else i.selectedText!=this._selectedReport&&(r._isTimeOut=!0,setTimeout(function(){r._isTimeOut&&r._waitingPopup.show()},800),this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:"toolbarOperation",element:this.element,customObject:this.model.customObject}),f=JSON.stringify(this.model.customObject),this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.toolbarServices,JSON.stringify({action:"toolbarOperation",toolbarOperation:"Report Change",clientInfo:i.text,olapReport:null,clientReports:this.reports,customObject:f}),this._toolbarOperationSuccess));this._selectedReport=this.reportDropTarget._currentText},_onDropped:function(n){if(n.target.className.indexOf("e-button")>-1){var t=this._getAxisPosition(this.element.find(".e-categoricalAxis")),r=this._getAxisPosition(this.element.find(".e-rowAxis")),u=this._getAxisPosition(this.element.find(".e-slicerAxis"));this._dropAxisClassName;n.pageX!=i&&n.pageY!=i&&(this._dropAxisClassName=n.pageX>t.left&&n.pageX<t.right&&n.pageY>t.top&&n.pageY<t.bottom?"e-categoricalAxis":n.pageX>r.left&&n.pageX<r.right&&n.pageY>r.top&&n.pageY<r.bottom?"e-rowAxis":n.pageX>u.left&&n.pageX<u.right&&n.pageY>u.top&&n.pageY<u.bottom?"e-slicerAxis":"outOfAxis")}},_getAxisPosition:function(t){var r=t.position(),i={};return i.top=r.top,i.right=r.left+n(t).width(),i.bottom=r.top+n(t).height(),i.left=r.left,i},_onTabClick:function(i){this.model.enableSplitter&&!t.isNullOrUndefined(this.element.find(".e-childsplit").data("ejSplitter"))&&this.element.find(".e-childsplit").data("ejSplitter").refresh();this.displayMode()!=t.PivotClient.DisplayMode.GridOnly&&(this.chartObj=this.element.find("#"+this._pivotChart._id+"Container").data("ejChart"),t.isNullOrUndefined(this.chartObj)&&this.model.enablePivotTreeMap&&!t.isNullOrUndefined(this.otreemapObj)&&(this.chartObj=this.element.find("#"+this.otreemapObj._id+"TreeMapContainer").data("ejTreeMap")));this._currentTab="chart";n(i.activeHeader).find("a").text()==this._getLocalizedLabels("Grid")?(this._currentTab="grid",this.element.find(".e-chartTypesImg").addClass("e-chartTypesOnGridView"),this.element.find(".e-gridPanel").css("display","inline")):t.isNullOrUndefined(this.chartObj)||(this._currentTab="chart",this.model.enableRTL&&t.browserInfo().name=="msie"&&(this.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.analysisMode==t.Pivot.AnalysisMode.Olap?this.element.find(".e-chartPanel").addClass("e-serverChartTabRtl"):this.element.find(".e-chartPanel").addClass("e-clientChartTabRtl")),this.element.find(".e-chartTypesImg").removeClass("e-chartTypesOnGridView"),t.isNullOrUndefined(this.chartObj)||(this.chartObj.sfType.split(".").pop().toLowerCase()=="treemap"?this.otreemapObj._treeMap.refresh():this.chartObj.redraw()),this.model.enableSplitter&&this._splitterChartResizing(this),this.model.enableToolBar&&(this._unWireEvents(),this._wireEvents()))},doAjaxPost:function(r,u,f,e,o,s){var h,c,l,a=!0,y=this.model.enableXHRCredentials,v;f.XMLA==i?(h="application/json; charset=utf-8",c="json",l=n.proxy(e,this),a=t.browserInfo().name=="msie"&&t.browserInfo().version<=9||JSON.parse(f).action=="export"?!1:!0):(h="text/xml",c="xml",f=f.XMLA,l=n.proxy(e,t.olap.base,s),a=t.browserInfo().name=="msie"&&t.browserInfo().version<=9?!1:!t.isNullOrUndefined(s)&&s.action!="loadFieldElements"?!0:!1);v={type:r,url:u,contentType:h,dataType:c,async:a,data:f,success:l,xhrFields:{withCredentials:y},complete:t.proxy(function(){if(t.isNullOrUndefined(this.model))return!1;var n={action:!t.isNullOrUndefined(s)&&!t.isNullOrUndefined(s.action)?s.action:JSON.parse(f).action,customObject:this.model.customObject,element:this.element};this._trigger("renderComplete",n)},this),error:t.proxy(function(n){this._waitingPopup=this.element.data("ejWaitingPopup");this._isTimeOut=!1;this._waitingPopup.hide();var t={customObject:this.model.customObject,element:this.element,Message:n};this._trigger("renderFailure",t);this._renderControlSuccess("")},this)};y||delete v.xhrFields;n.ajax(v)},doPostBack:function(t,i){var r=n("<form>").attr({action:t,method:"POST",name:"export"}),f=function(t,i){var u=n('<input type="hidden" title="params">').attr({id:t,name:t,value:i}).appendTo(r)};for(var u in i)f(u,i[u]);r.appendTo(document.body).submit().remove()}});t.PivotClient.Locale=t.PivotClient.Locale||{};t.PivotClient.Locale["en-US"]={NoReports:"No Reports Found in DB",Sort:"Sort",Search:"Search",SelectField:"Select field",LabelFilterLabel:"Show the items for which the label",ValueFilterLabel:"Show the items for which",ClearSorting:"Clear Sorting",ClearFilterFrom:"Clear Filter From",SortAtoZ:"Sort A to Z",SortZtoA:"Sort Z to A",LabelFilters:"Label Filters  ",BeginsWith:"Begins With",DoesNotBeginsWith:"Does Not Begin With",EndsWith:"Ends With",NotEndsWith:"Not Ends With",DoesNotEndsWith:"Does Not End With",Contains:"Contains",DoesNotContains:"Does Not Contain",ValueFilters:"Value Filters",ClearFilter:"Clear Filter",Equals:"Equals",DoesNotEquals:"Does Not Equal",NotEquals:"Not Equals",GreaterThan:"Greater Than",GreaterThanOrEqualTo:"Greater Than Or Equal To",LessThan:"Less Than",LessThanOrEqualTo:"Less Than Or Equal To",Between:"Between",NotBetween:"Not Between",Top10:"Top Count",GreaterThan:"Greater Than",IsGreaterThan:"Is Greater Than",IsGreaterThanOrEqualTo:"Is Greater Than Or Equal To",IsLessThan:"Is Less Than",IsLessThanOrEqualTo:"Is Less Than Or Equal To",DeferUpdate:"Defer Update",MDXQuery:"MDX Query",Column:"Column",Row:"Row",Slicer:"Slicer",CubeSelector:"Cube Selector",ReportName:"Report Name",NewReport:"New Report",CubeDimensionBrowser:"Cube Dimension Browser",AddReport:"Add Report",RemoveReport:"Remove Report",CannotRemoveSingleReport:"Cannot remove single report",AreYouSureToDeleteTheReport:"Are you sure to delete the Report",RenameReport:"Rename Report",ChartTypes:"Chart Types",ToggleAxis:"Toggle Axis",Load:"Load",ExportToExcel:"Export To Excel",ExportToWord:"Export To Word",ExportToPdf:"Export To Pdf",FullScreen:"Full Screen",Grid:"Grid",Chart:"Chart",OK:"<u>O<\/u>K",Cancel:"<u>C<\/u>ancel",Close:"Close",AddToColumn:"Add to Column",AddToRow:"Add to Row",AddToSlicer:"Add to Slicer",MeasureEditor:"Measure Editor",MemberEditor:"Member Editor",Measures:"Measures",SortOrFilterColumn:"Sort/Filter (Column)",SortOrFilterRow:"Sort/Filter (Row)",SortingAndFiltering:"Sorting And Filtering",Sorting:"Sorting",Measure:"<u>M<\/u>easure",Order:"Order",Filtering:"Filtering",Condition:"C<u>o<\/u>ndition",Value:"Val<u>u<\/u>e",PreserveHierarchy:"P<u>r<\/u>eserve  Hierarchy",Ascending:"<u>A<\/u>scending",Descending:"D<u>e<\/u>scending",Enable:"E<u>n<\/u>able",Disable:"D<u>i<\/u>sable",and:"<u>a<\/u>nd",EqualTo:"EqualTo",NotEquals:"NotEquals",GreaterThan:"GreaterThan",GreaterThanOrEqualTo:"GreaterThanOrEqualTo",LessThan:"LessThan",LessThanOrEqualTo:"LessThanOrEqualTo",Between:"Between",NotBetween:"NotBetween",ReportList:"Report List",Line:"Line",Spline:"Spline",Column:"Column",Area:"Area",SplineArea:"Spline Area",StepLine:"Step Line",StepArea:"Step Area",Pie:"Pie",Bar:"Bar",StackingArea:"Stacking Area",StackingColumn:"Stacking Column",StackingBar:"Stacking Bar",Pyramid:"Pyramid",Funnel:"Funnel",Doughnut:"Doughnut",Scatter:"Scatter",Bubble:"Bubble",WaterFall:"WaterFall",TreeMap:"TreeMap",Alert:"Alert",MDXAlertMsg:"Please add a measure, dimension, or hierarchy in an appropriate axis to view the MDX Query.",FilterSortRowAlertMsg:"Dimension is not found in the row axis. Please add Dimension element in the row axis for sorting/filtering.",FilterSortColumnAlertMsg:"Dimension is not found in the column axis. Please add Dimension element in the column axis for sorting/filtering.",FilterSortcolMeasureAlertMsg:"Please add measure to the column axis",FilterSortrowMeasureAlertMsg:"Please add measure to the row axis",FilterSortElementAlertMsg:"Element is not found in the column axis. Please add an element in column axis for sorting/filtering.",SelectRecordAlertMsg:"Please select a valid report.",FilterMeasureSelectionAlertMsg:"Please select a valid measure.",FilterConditionAlertMsg:"Please set a valid condition.",FilterStartValueAlertMsg:"Please set a start value.",FilterEndValueAlertMsg:"Please set a end value.",FilterInvalidAlertMsg:"Invalid operation !",Remove:"Remove",Save:"Save",SaveAs:"Save As",SelectReport:"Select Report",DBReport:"Report Manipulation in DB",Rename:"Rename",Remove:"Remove",SetReportNameAlertMsg:"Please set report name.",MultipleItems:"Multiple items",All:"All",CalculatedMember:"Calculated Member",Caption:"Caption:",Expression:"Expression:",MemberType:"MemberType:",FormatString:"Format String:",MultipleMeasure:"More than one measure cannot be sliced.",DuplicateCalcMeasure:"Calculated Member with same name already exists.",EmptyField:"Calculated Member name or Expression should not be empty.",EmptyFormat:"Format String for Calculated Member is empty.",Warning:"Warning",Confirm:"Calculated Member with the same name already exists. Do you want to replace?",KPIs:"KPIs",Collection:"Collection",Report:"Report",AddCurrentSelectionToFilter:"Add current selection to filter",SaveMsg:"Report saved successfully!!!",RenameMsg:"Report renamed successfully!!!",RemoveMsg:"Report removed successfully!!!",Success:"Success",KpiAlertMsg:"The field you are moving cannot be placed in that area of the report",NotAllItemsShowing:"Not all child nodes are shown",EditorLinkPanelAlert:"The members have more than 1000 items under one or more parent. Only the first 1000 items are displayed under each parent.",NamedSetAlert:"Named sets of same field cannot be added to the PivotTable report at the same time. Click OK to remove ' <Set 1> ' named set and add ' <Set 2> ' named set.",Exception:"Exception"};t.PivotClient.ControlPlacement={Tab:"tab",Tile:"tile"};t.PivotClient.DisplayMode={ChartOnly:"chartonly",GridOnly:"gridonly",ChartAndGrid:"chartandgrid"};t.PivotClient.ClientExportMode={ChartAndGrid:"chartandgrid",ChartOnly:"chartonly",GridOnly:"gridonly"};t.PivotClient.ExportMode={JSON:"json",PivotEngine:"pivotengine"};t.PivotClient.DefaultView={Chart:"chart",Grid:"grid"};t.PivotClient.MemberType={Dimension:"dimension",Measure:"measure"}})(jQuery,Syncfusion)});
