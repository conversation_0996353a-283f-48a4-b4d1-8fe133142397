﻿

-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[GetContacts] 
	@TenantId bigint,
	@Filter nvarchar(50) = '', 
	@OrderBy nvarchar(50) = '',
	@SortType nvarchar(5) = '',
	@PageSize int = 10,
	@PageIndex int = 0,
	@UserId bigint = null,
	@GuestId bigint = null
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;


    -- Insert statements for procedure here
	SELECT *, 
		(SELECT FullName FROM Users WHERE Users.UserId = Contacts.TherapistId) AS TherapistFullName,
		(SELECT FullName FROM Users WHERE Users.UserId = Contacts.ClinicSupervisorId) AS ClinicSupervisorFullName,
		(SELECT FullName FROM Users WHERE Users.UserId = Contacts.DiagnosticianId) AS DiagnosticianFullName,
		ISNULL((SELECT FullName FROM Users WHERE Users.UserId = Contacts.GuestId), '') AS GuestFullName
	FROM dbo.Contacts
	WHERE TenantId=@TenantId 
		AND ((@Filter IS NULL OR @Filter = '') OR FirstName LIKE '%'+@Filter+'%' OR LastName LIKE '%'+@Filter+'%' OR Email LIKE '%'+@Filter+'%' OR Phone1 LIKE '%'+@Filter+'%' OR Mobile1 LIKE '%'+@Filter+'%' OR AFM LIKE '%'+@Filter+'%' OR ContactCode = @filter)   
		AND (
			(@UserId IS NULL OR @UserId = 0) OR Contacts.TherapistId = @UserId OR Contacts.CoTherapistId = @UserId
		) 
		AND (
			(@GuestId IS NULL OR @GuestId = 0) OR Contacts.GuestId = @GuestId 
		)
	ORDER BY 
		CASE WHEN @OrderBy='' AND @SortType='' THEN Contacts.LastName END ASC, 
		CASE WHEN @OrderBy='ContactId' AND @SortType='ASC' THEN ContactId END ASC,
		CASE WHEN @OrderBy='ContactId' AND @SortType='DESC' THEN ContactId END DESC,
		CASE WHEN @OrderBy='LastName' AND @SortType='ASC' THEN LastName END ASC,
		CASE WHEN @OrderBy='LastName' AND @SortType='DESC' THEN LastName END DESC,
		CASE WHEN @OrderBy='FirstName' AND @SortType='ASC' THEN FirstName END ASC,
		CASE WHEN @OrderBy='FirstName' AND @SortType='DESC' THEN FirstName END DESC

		
	OFFSET @PageSize*@PageIndex ROWS
	FETCH NEXT @PageSize ROWS ONLY;

	SELECT COUNT(*) AS TotalCount
	FROM dbo.Contacts
	WHERE TenantId=@TenantId 
		AND ((@Filter IS NULL OR @Filter = '') OR FirstName LIKE '%'+@Filter+'%' OR LastName LIKE '%'+@Filter+'%' OR Email LIKE '%'+@Filter+'%' OR Phone1 LIKE '%'+@Filter+'%' OR Mobile1 LIKE '%'+@Filter+'%' OR AFM LIKE '%'+@Filter+'%')   
		AND (
			(@UserId IS NULL OR @UserId = 0) OR Contacts.TherapistId = @UserId OR Contacts.CoTherapistId = @UserId
		) 
		AND (
			(@GuestId IS NULL OR @GuestId = 0) OR Contacts.GuestId = @GuestId 
		)
	
END