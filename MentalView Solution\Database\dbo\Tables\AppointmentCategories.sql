﻿CREATE TABLE [dbo].[AppointmentCategories] (
    [AppointmentCategoryId] BIGINT        IDENTITY (1, 1) NOT NULL,
    [TenantId]              BIGINT        NOT NULL,
    [CategoryName]          NVARCHAR (50) CONSTRAINT [DF_AppointmentCategories_CategoryName] DEFAULT ('') NOT NULL,
    [Color]                 NVARCHAR (20) CONSTRAINT [DF_AppointmentCategories_Color] DEFAULT ('') NOT NULL,
    [FontColor]             NVARCHAR (20) CONSTRAINT [DF_AppointmentCategories_FontColor] DEFAULT ('') NOT NULL,
    CONSTRAINT [PK_AppointmentCategories] PRIMARY KEY CLUSTERED ([AppointmentCategoryId] ASC),
    CONSTRAINT [FK_AppointmentCategories_Tenants] FOREIGN KEY ([TenantId]) REFERENCES [dbo].[Tenants] ([TenantId]) ON DELETE CASCADE ON UPDATE CASCADE
);



