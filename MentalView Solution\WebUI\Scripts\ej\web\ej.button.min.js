/*!
*  filename: ej.button.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function(n,t){t.widget("ejButton","ej.Button",{element:null,model:null,validTags:["button","input"],_setFirst:!1,_rootCSS:"e-button",_requiresID:!0,defaults:{size:"normal",type:"submit",height:"",width:"",enabled:!0,htmlAttributes:{},text:null,contentType:"textonly",imagePosition:"imageleft",showRoundedCorner:!1,cssClass:"",prefixIcon:null,suffixIcon:null,enableRTL:!1,repeatButton:!1,timeInterval:"150",create:null,click:null,destroy:null},dataTypes:{size:"enum",enabled:"boolean",type:"enum",showRoundedCorner:"boolean",text:"string",contentType:"enum",imagePosition:"enum",prefixIcon:"string",suffixIcon:"string",cssClass:"string",repeatButton:"boolean",enableRTL:"boolean",timeInterval:"string",htmlAttributes:"data"},disable:function(){this.element.addClass("e-disable").attr("aria-disabled",!0);this.model.enabled=!1},enable:function(){this.element.removeClass("e-disable").attr("aria-disabled",!1);this.model.enabled=!0},_init:function(){this._cloneElement=this.element.clone();this._initialize();this._render();this._controlStatus(this.model.enabled);this._wireEvents(this.model.repeatButton);this._addAttr(this.model.htmlAttributes)},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.element.addClass(n):i.element.attr(t,n);t=="disabled"&&n=="disabled"&&i.disable()})},_destroy:function(){this._off(this.element,"blur",this._btnBlur);this.element.removeClass(this.model.cssClass+"e-ntouch e-btn e-txt e-select e-disable e-corner e-widget").removeAttr("role aria-describedby aria-disabled");!this._cloneElement.attr("type")&&this.element.attr("type")&&this.element.removeAttr("type");this.element.removeClass("e-btn-"+this.model.size);this.model.contentType&&this.model.contentType!="textonly"?this.element.append(this._cloneElement.text())&&this.imgtxtwrap[0].remove():""},_setModel:function(n){for(var t in n)switch(t){case"size":this._setSize(n[t]);break;case"height":this._setHeight(n[t]);break;case"width":this._setWidth(n[t]);break;case"contentType":this._setContentType(n[t]);break;case"imagePosition":this._setImagePosition(n[t]);break;case"text":this._setText(n[t]);break;case"prefixIcon":this.element.is("input")||this._setMajorIcon(n[t]);break;case"suffixIcon":this.element.is("input")||this._setMinorIcon(n[t]);break;case"enabled":this._controlStatus(n[t]);break;case"showRoundedCorner":this._roundedCorner(n[t]);break;case"cssClass":this._setSkin(n[t]);break;case"enableRTL":this._setRTL(n[t]);break;case"timeInterval":this.model.timeInterval=n[t];break;case"htmlAttributes":this._addAttr(n[t])}},_setSize:function(n){this.element.removeClass("e-btn-mini e-btn-medium e-btn-small e-btn-large e-btn-normal");this.element.addClass("e-btn-"+n)},_setType:function(n){this.element.prop({type:n})},_setHeight:function(n){this.element.css("height",n)},_setWidth:function(n){this.element.css("width",n)},_setText:function(n){this.buttonType=="inputButton"?this.element.val(n):this.model.contentType==t.ContentType.TextOnly?this.element.html(n):this.textspan.html(n);this.model.text=n},_setMajorIcon:function(n){this.majorimgtag.removeClass(this.model.prefixIcon);this.majorimgtag.addClass(n);this.model.prefixIcon=n},_setMinorIcon:function(n){this.minorimgtag.removeClass(this.model.suffixIcon);this.minorimgtag.addClass(n);this.model.suffixIcon=n},_setContentType:function(n){n!=this.model.contentType&&(this.element.empty(),this.model.contentType=n,this.element.is("input")||this._renderButtonNormal())},_setImagePosition:function(n){this.model.contentType==t.ContentType.TextAndImage&&n!=this.model.imagePosition&&(this.element.empty(),this.model.imagePosition=n,this.element.is("input")||this._renderButtonNormal())},_setRTL:function(n){n?this.element.addClass("e-rtl"):this.element.removeClass("e-rtl")},_controlStatus:function(n){n?this.enable():this.disable()},_setSkin:function(n){this.model.cssClass!=n&&(this.element.removeClass(this.model.cssClass),this.element.addClass(n))},_initialize:function(){t.isTouchDevice()||this.element.addClass("e-ntouch");this.element.is("input")?this.buttonType="inputButton":this.element.is("a")||this.element.is("button")?this.buttonType="tagButton":this.element.removeClass("e-button");this.element.attr("type")?this.model.type=this.element.attr("type"):this._setType(this.model.type);this._timeout=null},_render:function(){this._setSize(this.model.size);this._setHeight(this.model.height);this._setWidth(this.model.width);this._setRTL(this.model.enableRTL);this.element.addClass(this.model.cssClass+" e-btn e-select e-widget").attr("role","button");this.buttonType=="inputButton"?(this.element.addClass("e-txt"),this.model.text!=null&&this.model.text!=""?this.element.val(this.model.text):this.model.text=this.element.val()):this._renderButtonNormal();this._roundedCorner(this.model.showRoundedCorner);this.element[0].id&&this.element.attr("aria-describedby",this.element[0].id)},_renderButtonNormal:function(){if((this.model.text==null||this.model.text=="")&&(this.model.text=this.element.html()),this.element.empty(),this.textspan=t.buildTag("span.e-btntxt",this.model.text),this.model.contentType.indexOf("image")>-1&&(this.majorimgtag=t.buildTag("span").addClass(this.model.prefixIcon),this.minorimgtag=t.buildTag("span").addClass(this.model.suffixIcon),this.imgtxtwrap=t.buildTag("span").addClass("e-btn-span")),this.model.contentType==t.ContentType.TextAndImage){switch(this.model.imagePosition){case t.ImagePosition.ImageRight:this.imgtxtwrap.append(this.textspan,this.majorimgtag);break;case t.ImagePosition.ImageLeft:this.imgtxtwrap.append(this.majorimgtag,this.textspan);break;case t.ImagePosition.ImageBottom:this.majorimgtag.attr("style","display:inherit");this.imgtxtwrap.append(this.textspan,this.majorimgtag);break;case t.ImagePosition.ImageTop:this.majorimgtag.attr("style","display:inherit");this.imgtxtwrap.append(this.majorimgtag,this.textspan)}this.element.append(this.imgtxtwrap)}else this.model.contentType==t.ContentType.ImageTextImage?(this.imgtxtwrap.append(this.majorimgtag,this.textspan,this.minorimgtag),this.element.append(this.imgtxtwrap)):this.model.contentType==t.ContentType.ImageBoth?(this.imgtxtwrap.append(this.majorimgtag,this.minorimgtag),this.element.append(this.imgtxtwrap)):this.model.contentType==t.ContentType.ImageOnly?(this.imgtxtwrap.append(this.majorimgtag),this.element.append(this.imgtxtwrap)):(this.element.addClass("e-txt"),this.element.html(this.model.text))},_roundedCorner:function(n){n==!0?this.element.addClass("e-corner"):this.element.removeClass("e-corner")},_wireEvents:function(t){t&&(this._on(this.element,"mousedown",this._btnRepatMouseClickEvent),this._on(n(document),"mouseup",this._mouseUpClick),this._on(this.element,"keyup",this._btnRepatKeyUpEvent),this._on(n(document),"keypress",this._btnRepatKeyDownEvent));this._on(this.element,"click",this._btnMouseClickEvent);this._on(this.element,"blur",this._btnBlur)},_btnBlur:function(){this.element.removeClass("e-animate")},_btnMouseClickEvent:function(n){var t=this,i;if(this.element.addClass("e-animate"),!t.model.enabled)return!1;t.element.hasClass("e-disable")||(i={target:n.currentTarget,e:n,status:t.model.enabled},t._trigger("_click",i),t._trigger("click",i))},_btnRepatMouseClickEvent:function(n){var t=this,i;if(!t.model.enabled)return!1;t.element.hasClass("e-disable")||(i={status:t.model.enabled},(n.button==0||n.which==1)&&(t._timeout=setInterval(function(){t._trigger("click",{target:n.currentTarget,status:t.model.enabled})},this.model.timeInterval)))},_mouseUpClick:function(){clearTimeout(this._timeout)},_btnRepatKeyDownEvent:function(n){var t=this,i;t.element.hasClass("e-disable")||(i={status:t.model.enabled},(n.keyCode==32||n.keyCode==13)&&t._trigger("click",i))},_btnRepatKeyUpEvent:function(n){(n.keyCode==32||n.keyCode==13)&&clearTimeout(this._timeout)}});t.ContentType={TextOnly:"textonly",ImageOnly:"imageonly",ImageBoth:"imageboth",TextAndImage:"textandimage",ImageTextImage:"imagetextimage"};t.ImagePosition={ImageRight:"imageright",ImageLeft:"imageleft",ImageTop:"imagetop",ImageBottom:"imagebottom"};t.ButtonSize={Normal:"normal",Mini:"mini",Small:"small",Medium:"medium",Large:"large"};t.ButtonType={Button:"button",Reset:"reset",Submit:"submit"}})(jQuery,Syncfusion)});
