/*!
*  filename: ej.datetimepicker.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.globalize.min","./../common/ej.core.min","./../common/ej.scroller.min","./ej.datepicker.min","./ej.timepicker.min"],n):n()})(function(){(function(n,t,i){t.widget("ejDateTimePicker","ej.DateTimePicker",{element:null,model:null,validTags:["input"],_addToPersist:["value"],_setFirst:!1,_rootCSS:"e-datetimepicker",type:"editor",angular:{require:["?ngModel","^?form","^?ngModelOptions"],requireFormatters:!0},_requiresID:!0,defaults:{cssClass:"",locale:"en-US",readOnly:!1,showRoundedCorner:!1,enableRTL:!1,htmlAttributes:{},allowEdit:!0,enabled:!0,value:"",name:null,minDateTime:new Date("1/1/1900 12:00:00 AM"),maxDateTime:new Date("12/31/2099 11:59:59 PM"),height:"",width:"",dateTimeFormat:"",showPopupButton:!0,enableStrictMode:!1,buttonText:{today:"Today",timeNow:"Time Now",done:"Done",timeTitle:"Time"},watermarkText:"Select datetime",enablePersistence:!1,interval:30,timeDisplayFormat:"",timePopupWidth:105,popupPosition:"bottom",dayHeaderFormat:"short",startLevel:"month",depthLevel:"",startDay:-1,stepMonths:1,showOtherMonths:!0,specialDates:null,highlightWeekend:!1,enableAnimation:!0,headerFormat:"MMMM yyyy",validationRules:null,validationMessage:null,validationMessages:null,timeDrillDown:{enabled:!1,interval:5,showMeridian:!1,autoClose:!0,showFooter:!0},beforeOpen:null,beforeClose:null,open:null,close:null,change:null,create:null,destroy:null,focusIn:null,focusOut:null,disableDateTimeRanges:null,timeZone:!0,disableTimeRanges:null},dataTypes:{allowEdit:"boolean",cssClass:"string",locale:"string",readOnly:"boolean",showRoundedCorner:"boolean",enableRTL:"boolean",enabled:"boolean",enableAnimation:"boolean",dateTimeFormat:"string",showPopupButton:"boolean",buttonText:"data",watermarkText:"string",enablePersistence:"boolean",enableStrictMode:"boolean",interval:"number",timeDrillDown:"data",timeDisplayFormat:"string",dayHeaderFormat:"string",startLevel:"string",depthLevel:"string",startDay:"number",stepMonths:"number",showOtherMonths:"boolean",highlightWeekend:"boolean",specialDates:"data",headerFormat:"string",validationRules:"data",validationMessage:"data",validationMessages:"data",htmlAttributes:"data",disableDateTimeRanges:"data",disableTimeRanges:"data"},_setModel:function(i){var r,e=!1,o,u,s,f;for(r in i){r!="allowEdit"&&r!="readOnly"&&r!="enabled"&&r!="validationRules"&&r!="validationMessages"&&r!="enableStrictMode"&&r!="height"&&r!="width"&&r!="showPopupButton"&&r!="dateTimeFormat"&&r!="watermarkText"&&r!="htmlAttributes"&&(this.popup||this._renderDropdown());switch(r){case"allowEdit":this._changeEditable(i[r]);break;case"cssClass":this._changeSkin(i[r]);break;case"locale":this._localize(i[r]);break;case"readOnly":this._readOnly(i[r]);break;case"showRoundedCorner":this._setRoundedCorner(i[r]);break;case"enableRTL":this._setRtl(i[r]);break;case"enabled":this._enabled(i[r]);break;case"validationRules":this.model.validationRules!=null&&(this.element.rules("remove"),this.model.validationMessages=null);this.model.validationRules=i[r];this.model.validationRules!=null&&(this._initValidator(),this._setValidation());break;case"validationMessage":this.model.validationMessages=i[r];this.model.validationRules!=null&&this.model.validationMessages!=null&&(this._initValidator(),this._setValidation());break;case"validationMessages":this.model.validationMessages=i[r];this.model.validationRules!=null&&this.model.validationMessages!=null&&(this._initValidator(),this._setValidation());break;case"value":i[r]=this._setValue(i[r]);this._specificFormat()&&(this._stopRefresh=!0);e=!0;this._prevDateTime=this._prevDateTimeVal||this._preVal;break;case"enableStrictMode":this.model.enableStrictMode=i[r];e=!0;break;case"minDateTime":u=this._stringToObject(i[r]);o=this._getFormat(u,this.timePicker.model.timeFormat);this._isValidDate(u)?(this.datePicker.option("minDate",u),this.datePicker.model.value&&this.datePicker.model.value.toDateString()==this.datePicker.model.minDate.toDateString()&&this.timePicker.option("minTime",o),i[r]=u,this.model.minDateTime=u):i[r]=this.model[r];e=!0;break;case"maxDateTime":u=this._stringToObject(i[r]);s=this._getFormat(i[r],this.timePicker.model.timeFormat);this._isValidDate(u)?(this.datePicker.option("maxDate",u),this.datePicker.model.value&&this.datePicker.model.value.toDateString()==this.datePicker.model.maxDate.toDateString()&&this.timePicker.option("maxTime",s),i[r]=u,this.model.maxDateTime=u):i[r]=this.model[r];e=!0;break;case"height":this.wrapper.height(i[r]);break;case"width":this.wrapper.width(i[r]);break;case"dateTimeFormat":this.model.dateTimeFormat=i[r];this.isValidState&&this._setValue(this.model.value);break;case"showPopupButton":this._showButton(i[r]);break;case"watermarkText":t.isNullOrUndefined(this._options)&&(this._options={});this._options[r]=this.model.watermarkText=i[r];this._localizedLabels.watermarkText=this.model.watermarkText;this._setWaterMark();break;case"buttonText":t.isNullOrUndefined(this._options)||(this._options.buttonText=this.model.buttonText=i[r]);this._localizedLabels.buttonText=this.model.buttonText;this._buttonText(i[r]);break;case"interval":this._updateTimeHeight();this.timePicker.option("interval",i[r]);break;case"timeDisplayFormat":this._updateTimeHeight();this.timePicker.option("timeFormat",i[r]);break;case"disableDateTimeRanges":this._disabledDate&&(this._disabledDates=!1);this.model.disableDateTimeRanges=i[r];this._setValue(this.model.value);break;case"disableTimeRanges":this.model.disableTimeRanges=i[r];this.timePicker.option("disableTimeRanges",this.model.disableTimeRanges);break;case"timePopupWidth":this._updateTimeHeight();f=i[r];i[r]=typeof f=="string"&&f.indexOf("%")!=-1||typeof f=="string"?parseInt(f)>0?f:105:f>0?f:105;this.timePicker.option("popupWidth",i[r]);break;case"dayHeaderFormat":this.datePicker.option("dayHeaderFormat",i[r]);break;case"startLevel":this.datePicker.option("startLevel",i[r]);break;case"depthLevel":this.datePicker.option("depthLevel",i[r]);break;case"startDay":this.datePicker.option("startDay",i[r]);this.model.startDay=this.datePicker.model.startDay;i[r]=this.model.startDay;break;case"stepMonths":this.datePicker.option("stepMonths",i[r]);break;case"showOtherMonths":this.datePicker.option("showOtherMonths",i[r]);break;case"highlightWeekend":this.datePicker.option("highlightWeekend",i[r]);break;case"specialDates":this.datePicker.option("specialDates",i[r]);this.model.specialDates=this.datePicker.model.specialDates;break;case"headerFormat":this.datePicker.option("headerFormat",i[r]);break;case"htmlAttributes":this._addAttr(i[r]);break;case"popupPosition":this.model.popupPosition=i[r];this._setListPosition();break;case"timeDrillDown":t.isNullOrUndefined(i[r].enabled)||(this.model.timeDrillDown.enabled=i[r].enabled,this._changeDesign());t.isNullOrUndefined(i[r].interval)||(this.model.timeDrillDown.interval=i[r].interval,this._generateMins(n.trim(t.format(this.timePicker._createObject(this._datetimeValue),"HH:00",this.model.locale))));t.isNullOrUndefined(i[r].showMeridian)||(this.model.timeDrillDown.showMeridian=i[r].showMeridian,this._sfTimeHour.empty(),this._renderHourTable());t.isNullOrUndefined(i[r].showFooter)||(this.model.timeDrillDown.showFooter=i[r].showFooter,this._changeDesign())}}e&&this._validateMinMax();this._valueChange(!0);r=="value"&&(i[r]=this.model.value);this.popup&&this._updateTimeHeight();this._checkErrorClass()},observables:["value"],_destroy:function(){this.isPopupOpen&&this._hideResult();this.wrapper&&(this.element.insertAfter(this.wrapper),this.wrapper.remove());this.element.removeClass("e-js e-input").removeClass(t.util.getNameSpace(this.sfType));this.element.removeAttr(" type aria-atomic aria-live tabindex aria-expanded aria-disabled placeholder role");this._cloneElement[0].hasAttribute("name")||this.element.removeAttr("name");t.isNullOrUndefined(this.datePicker)||this.datePicker.destroy();t.isNullOrUndefined(this.timePicker)||this.timePicker.destroy();this.popup&&this.popup.remove()},_initDisableTimeRanges:function(n){for(var e,o,h,c,f,u,r,s=this._getDateObj(n,this.datePicker.model.dateFormat),i=0;i<this.model.disableDateTimeRanges.length;i++)if(t.isNullOrUndefined(this.model.disableDateTimeRanges[i].endDateTime)&&(this.model.disableDateTimeRanges[i].endDateTime=this.model.disableDateTimeRanges[i].startDateTime),this.model.disableDateTimeRanges[i].startDateTime instanceof Date||this.model.disableDateTimeRanges[i].endDateTime instanceof Date?(e=this._getFormat(this.model.disableDateTimeRanges[i].startDateTime,this.datePicker.model.dateFormat),o=this._getFormat(this.model.disableDateTimeRanges[i].endDateTime,this.datePicker.model.dateFormat)):(e=this.model.disableDateTimeRanges[i].startDateTime.split(" ")[0],o=this.model.disableDateTimeRanges[i].endDateTime.split(" ")[0]),h=new Date(this.model.disableDateTimeRanges[i].startDateTime),c=new Date(this.model.disableDateTimeRanges[i].endDateTime),e==o){if(this._compare(s,this._setEmptyTime(this.model.disableDateTimeRanges[i].startDateTime))){var u=this._getFormat(h,this.timePicker.model.timeFormat),r=this._getFormat(c,this.timePicker.model.timeFormat),f=[];f.push({startTime:u,endTime:r});this.timePicker.option("disableTimeRanges",f);return}this.timePicker.option("disableTimeRanges",this._defaultMinVal());this._disabledDates||this.model.disableDateTimeRanges[i].endDateTime!=this.model.disableDateTimeRanges[i].startDateTime||this.model.disableDateTimeRanges[i].endDateTime instanceof Date==!1&&this._getFormat(this.model.disableDateTimeRanges[i].endDateTime,this.model.dateTimeFormat)==this._getFormat(this.model.disableDateTimeRanges[i].endDateTime,this.model.dateFormat)&&t.isNullOrUndefined(this.model.disableDateTimeRanges[i].endDateTime.split(" ")[1])&&this._between.push(new Date(this.model.disableDateTimeRanges[i].endDateTime))}else if(e!=o){if(this._compare(s,this._setEmptyTime(this.model.disableDateTimeRanges[i].startDateTime))||this._compare(s,this._setEmptyTime(this.model.disableDateTimeRanges[i].endDateTime))){u=this.timePicker.model.minTime;r=this.timePicker.model.maxTime;this.datePicker.model.value.toDateString()==new Date(this.model.disableDateTimeRanges[i].startDateTime).toDateString()&&(u=this._getFormat(new Date(this.model.disableDateTimeRanges[i].startDateTime),this.timePicker.model.timeFormat));this.datePicker.model.value.toDateString()==new Date(this.model.disableDateTimeRanges[i].endDateTime).toDateString()&&(r=this._getFormat(new Date(this.model.disableDateTimeRanges[i].endDateTime),this.timePicker.model.timeFormat));f=[];f.push({startTime:u,endTime:r});this.timePicker.option("disableTimeRanges",f);return}this._disabledDates||(u=this._getFormat(new Date(this.model.disableDateTimeRanges[i].startDateTime),this.timePicker.model.timeFormat),r=this._getFormat(new Date(this.model.disableDateTimeRanges[i].endDateTime),this.timePicker.model.timeFormat),u==this.timePicker.model.minTime&&this._between.push(new Date(this.model.disableDateTimeRanges[i].startDateTime)),this.model.disableDateTimeRanges[i].endDateTime instanceof Date==!1&&this._getFormat(this.model.disableDateTimeRanges[i].endDateTime,this.model.dateTimeFormat)==this._getFormat(new Date(this.model.disableDateTimeRanges[i].endDateTime),this.datePicker.model.dateFormat)&&(r=this.timePicker.model.maxTime),r==this.timePicker.model.maxTime&&this._between.push(new Date(this.model.disableDateTimeRanges[i].endDateTime)))}},_disableBetweenDates:function(){for(var n=0;n<this.model.disableDateTimeRanges.length;n++)for(var r=this.model.disableDateTimeRanges[n].startDateTime,t=this._setEmptyTime(r),f=this._getFormat(new Date(this.model.disableDateTimeRanges[n].startDateTime),this.timePicker.model.timeFormat),e=this._getFormat(new Date(this.model.disableDateTimeRanges[n].endDateTime),this.timePicker.model.timeFormat),u=this.model.disableDateTimeRanges[n].endDateTime,i=this._setEmptyTime(u);t<i;)t.setDate(t.getDate()+1),new Date(t)<i&&this._between.push(new Date(t));this._datesDisabled=!0},_init:function(n){if(this._cloneElement=this.element.clone(),!this.element.is("input")||this.element.attr("type")&&this.element.attr("type")!="text")return!1;this._options=n;this._ISORegex();this._isSupport=document.createElement("input").placeholder==i?!1:!0;this._validateMeridian();this._checkAttribute();this._initialize();this._initial=!0;this._interval=60;this._render();this._wireEvents();this._addAttr(this.model.htmlAttributes);t.isNullOrUndefined(n)||t.isNullOrUndefined(n.validationMessage)||(this.model.validationMessages=this.model.validationMessage);this.model.validationRules!=null&&(this._initValidator(),this._setValidation());this._removeWatermark();n&&n.value!=i&&n.value!=this.element.val()&&this._trigger("_change",{value:this.element.val()})},_ISORegex:function(){this._tokens=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g;this._extISORegex=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/;this._basicISORegex=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/;this._numberRegex={2:/\d\d?/,4:/^\d{4}/,z:/Z|[+-]\d\d(?::?\d\d)?/gi,t:/T/,"-":/\-/,":":/:/};this._zeroRegex=/Z|[+-]\d\d(?::?\d\d)?/;this._dates=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/]];this._times=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]]},_changeDesign:function(){if(this.model.timeDrillDown.enabled){var t=this.model.timeDrillDown.showFooter?"block":"none";this.popup.addClass("e-drill-down");this._timeContainer.css("display","none");this._buttonContainer.css("display","none");this.datePicker.option("showFooter",this.model.timeDrillDown.showFooter);n(".e-footer",this._sfTimeHour).css("display",t);n(".e-footer",this._sfTimeMins).css("display",t)}else this.popup.removeClass("e-drill-down"),this._sfTimeHour.hide(),this._sfTimeMins.hide(),this._updateTimeHeight(),this._dateContainer.show(),this._timeContainer.show(),this._buttonContainer.show(),this.datePicker.option("showFooter",!1),this.timePicker._refreshScroller(),this.timePicker._changeActiveEle()},_initValidator:function(){this.element.closest("form").data("validator")||this.element.closest("form").validate()},_checkAttribute:function(){this.model.value=this.model.value===""?this.element[0].value:this.model.value;this._options.minDateTime||(this.model.minDateTime=this.element[0].min);this._options.maxDateTime||(this.model.maxDateTime=this.element[0].max);t.isNullOrUndefined(this._options.readOnly)&&(this.model.readOnly=this.element.is("[readonly]"));t.isNullOrUndefined(this._options.enabled)&&(this.model.enabled=!this.element.is("[disabled]"))},_setValidation:function(){var r,f,i,u,e;this.element.rules("add",this.model.validationRules);r=this.element.closest("form").data("validator");r=r?r:this.element.closest("form").validate();f=this.element.attr("name");r.settings.messages[f]={};for(i in this.model.validationRules)if(u=null,!t.isNullOrUndefined(this.model.validationRules[i])){if(t.isNullOrUndefined(this.model.validationRules.messages&&this.model.validationRules.messages[i])){r.settings.messages[f][i]=n.validator.messages[i];for(e in this.model.validationMessages)i==e?u=this.model.validationMessages[i]:""}else u=this.model.validationRules.messages[i];r.settings.messages[f][i]=u!=null?u:n.validator.messages[i]}},_validateDisableRanges:function(){for(var t,n=0;n<this.model.disableDateTimeRanges.length;n++)t=this.model.value,new Date(this.model.value).getTime()>=new Date(this.model.disableDateTimeRanges[n].startDateTime).getTime()&&new Date(this.model.value).getTime()<=new Date(this.model.disableDateTimeRanges[n].endDateTime).getTime()&&(this.model.value=null),this.model.value==null&&t!=null&&this.model.enableStrictMode&&(this.isValidState=!1),this.isValidState&&this.element.val(this._getFormat(this.model.value,this.model.dateTimeFormat))},_addAttr:function(i){var r=this;n.map(i,function(n,i){var u=i.toLowerCase();u=="class"?r.wrapper.addClass(n):u=="disabled"?r._enabled(!1):u=="readOnly"?r._readOnly(!0):u=="style"?r.wrapper.attr(i,n):u=="id"?(r.wrapper.attr(i,n+"_wrapper"),r.element.attr(i,n)):t.isValidAttr(r.element[0],u)?r.element.attr(u,n):r.wrapper.attr(u,n)})},_validateMeridian:function(){var n=t.preferredCulture(this.model.locale);n&&(this.model.locale=n.name=="en"?"en-US":n.name);t.isNullOrUndefined(this._options)?this.model.timeDrillDown.showMeridian=t.isNullOrUndefined(t.preferredCulture(this.model.locale).calendars.standard.AM)?!1:!0:t.isNullOrUndefined(this._options.timeDrillDown)?this.model.timeDrillDown.showMeridian=t.isNullOrUndefined(t.preferredCulture(this.model.locale).calendars.standard.AM)?!1:!0:t.isNullOrUndefined(this._options.timeDrillDown.showMeridian)&&(this.model.timeDrillDown.showMeridian=t.isNullOrUndefined(t.preferredCulture(this.model.locale).calendars.standard.AM)?!1:!0)},_initialize:function(){var i,r,u,f;this.popup=null;this.isPopupOpen=!1;this.isValidState=!0;this._localizedLabels=this._getLocalizedLabels();t.isNullOrUndefined(this._options)||(t.isNullOrUndefined(this._options.buttonText)||n.extend(this._localizedLabels.buttonText,this._options.buttonText),t.isNullOrUndefined(this._options.watermarkText)||(this._localizedLabels.watermarkText=this._options.watermarkText));this._localizedLabelToModel();this.model.startDay==-1&&(this.model.startDay=t.preferredCulture(this.model.locale).calendar.firstDay);this._isIE8=t.browserInfo().name=="msie"&&t.browserInfo().version=="8.0"?!0:!1;this._isIE9=t.browserInfo().name=="msie"&&t.browserInfo().version=="9.0"?!0:!1;this.model.dateTimeFormat&&this.model.timeDisplayFormat||this._getDateTimeFormat();this.model.value&&(typeof JSON!="object"||JSON.stringify(this.model.value)!=="{}")?this.model.value instanceof Date?i=this.model.value:(r=t.parseDate(this.model.value,this.model.dateTimeFormat,this.model.locale),i=r?r:(r=this._checkJSONString(this.model.value))?r:null):i=null;i&&(this.model.value=i);u=this.model.minDateTime=this._stringToObject(this.model.minDateTime);u&&this._isValidDate(u)||(this.model.minDateTime=this.defaults.minDateTime);f=this.model.maxDateTime=this._stringToObject(this.model.maxDateTime);f&&this._isValidDate(f)||(this.model.maxDateTime=this.defaults.maxDateTime)},_checkJSONString:function(n){var i=new Date(n),r;if(isNaN(Date.parse(i))){if(this._extISORegex.exec(n)||this._basicISORegex.exec(n))return this._dateFromISO(n)}else if(i.toJSON()===this.model.value||i.toGMTString()===this.model.value||i.toISOString()===this.model.value||i.toLocaleString()===this.model.value||i.toString()===this.model.value||i.toUTCString()===this.model.value){if(this.model.timeZone)return new Date(new Date(i).getTime()+t.serverTimezoneOffset*36e5);if(n.match(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(\.\d+)?(([+-]\d\d:\d\d)|Z)?$/i)&&n.match(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(\.\d+)?(([+-]\d\d:\d\d)|Z)?$/i).length>0)return r=n.split("Z"),r=r[0],new Date(r)}else if(typeof n=="string")return this._dateFromISO(n)},_render:function(){this._renderWrapper();this._renderIcon();this._setDimentions();this._checkProperties()},_renderWrapper:function(){if(this.element.addClass("e-input").attr({"aria-atomic":"true","aria-live":"assertive",tabindex:"0",role:"combobox","aria-expanded":"false"}),this.wrapper=t.buildTag("span.e-datetime-wrap e-widget "+this.model.cssClass+"#"+this.element[0].id+"_wrapper").insertAfter(this.element),this._setValue(this.model.value),this.wrapper.attr("style",this.element.attr("style")),this.element.removeAttr("style"),t.isTouchDevice()||this.wrapper.addClass("e-ntouch"),this.container=t.buildTag("span.e-in-wrap e-box").append(this.element),this.wrapper.append(this.container),!this._isSupport){this._hiddenInput=t.buildTag("input.e-input e-placeholder ","",{},{type:"text"}).insertAfter(this.element);this._hiddenInput.val(this._localizedLabels.watermarkText);this._hiddenInput.css("display","block");var i=this;n(this._hiddenInput).focus(function(){i.element.focus()})}},_removeWatermark:function(){this.element.val()!=""&&!this._isSupport&&this._hiddenInput&&this._hiddenInput.css("display","none")},_renderIcon:function(){if(!this.model.showPopupButton)return!1;this.datetimeIcon=t.buildTag("span.e-select","",{}).attr(this._isIE8?{unselectable:"on"}:{});var n=t.buildTag("span.e-icon e-datetime","",{},{"aria-label":"select"}).attr(this._isIE8?{unselectable:"on"}:{});this.datetimeIcon.append(n);this.container.append(this.datetimeIcon).addClass("e-padding");this._on(this.datetimeIcon,"click",this._iconClick);this._on(this.datetimeIcon,"mousedown",function(n){n.preventDefault()})},_setDimentions:function(){this.model.height||(this.model.height=this.element.attr("height"));this.model.width||(this.model.width=this.element.attr("width"));this.wrapper.height(this.model.height);this.wrapper.width(this.model.width)},_renderDropdown:function(){var f=n("#"+this.element[0].id+"_popup").get(0),e,o,i,r,u;f&&n(f).remove();this.popup=t.buildTag("div.e-datetime-popup e-popup e-widget e-box "+this.model.cssClass+"#"+this.element[0].id+"_popup").css("visibility","hidden");t.isTouchDevice()||this.popup.addClass("e-ntouch");n("body").append(this.popup);this._renderControls();e=t.buildTag("div.e-header",this._localizedLabels.buttonText.timeTitle).attr(this._isIE8?{unselectable:"on"}:{});this._dateContainer=t.buildTag("div.e-datecontainer").append(this.datePicker.popup).attr(this._isIE8?{unselectable:"on"}:{});this._timeContainer=t.buildTag("div.e-timecontainer").append(e,this.timePicker.popup).attr(this._isIE8?{unselectable:"on"}:{});this._drillDownContainer=t.buildTag("div.e-drillDowncontainer").append().attr(this._isIE8?{unselectable:"on"}:{});o=t.buildTag("div.e-popup-container").append(this._dateContainer,this._timeContainer,this._drillDownContainer).attr(this._isIE8?{unselectable:"on"}:{});i=t.buildTag("div.e-dt-button e-dt-today e-btn e-select e-flat",this._localizedLabels.buttonText.today).attr(this._isIE8?{unselectable:"on"}:{});r=t.buildTag("div.e-dt-button e-dt-now e-btn e-select e-flat",this._localizedLabels.buttonText.timeNow).attr(this._isIE8?{unselectable:"on"}:{});u=t.buildTag("div.e-dt-button e-dt-done e-btn e-select e-flat",this._localizedLabels.buttonText.done).attr(this._isIE8?{unselectable:"on"}:{});this._buttonContainer=t.buildTag("div.e-button-container").append(i,r,u).attr(this._isIE8?{unselectable:"on"}:{});this._renderDrillDown();this.popup.append(o,this._buttonContainer);this._checkForResponsive();this._updateTimeHeight();this._bindOperations();this._updateValues();this.popup.css({visibility:"visible",display:"none"});this._on(i,"click",this._todayClick);this._on(r,"click",this._nowClick);this._on(u,"click",this._doneClick);this.popup.on("mouseenter touchstart",n.proxy(function(){this._popClose=!0},this));this.popup.on("mouseleave touchend",n.proxy(function(){this._popClose=!1},this));this._changeDesign()},_renderControls:function(){this._renderDateControl();this._renderTimeControl();var n=t.buildTag("span").append(this.datePicker.wrapper,this.timePicker.wrapper);n.find("span").css("display","none");this.popup.append(n)},_renderDrillDown:function(){this._renderHourPopup();this._renderMinsPopup()},_renderHourPopup:function(){this._sfTimeHour=t.buildTag("div.e-timepicker e-popup e-widget "+this.model.cssClass+" e-time-hours ","",{},{id:this._id?"e-hours-"+this._id:""}).attr({"aria-hidden":"true"}).attr(this._isIE8?{unselectable:"on"}:{});t.isTouchDevice()||this._sfTimeHour.addClass("e-ntouch");this._drillDownContainer.append(this._sfTimeHour);this._renderHourTable()},_renderHourTable:function(){var i,o,a,v,s,u,y,p,r,w,l,e;if(t.buildTag("div.e-header").attr(this._isIE8?{unselectable:"on"}:{}).append(t.buildTag("span.e-prev").append(t.buildTag("a.e-icon e-arrow-sans-left").attr({role:"button"}).attr(this._isIE8?{unselectable:"on"}:{}))).append(t.buildTag("span.e-text").append(t.buildTag("span.e-hours-headertext").text("October 2015").attr({"aria-atomic":"true","aria-live":"assertive",role:"heading"}).attr(this._isIE8?{unselectable:"on"}:{}))).append(t.buildTag("span.e-next").append(t.buildTag("a.e-icon e-arrow-sans-right").attr({role:"button"}).attr(this._isIE8?{unselectable:"on"}:{}))).appendTo(this._sfTimeHour),this._interval<1)return!1;y=this._interval*6e4;p=this.model.timeDrillDown.showMeridian?2:1;v=this.model.timeDrillDown.showMeridian?6:4;var b=this.model.timeDrillDown.showMeridian?"hh":"HH:00",h=["AM","PM"],c=0,f="";for(i=this.timePicker._createObject("12:00:00 AM"),o=this.model.timeDrillDown.showMeridian?this.timePicker._createObject("11:59:59 AM"):this.timePicker._createObject("11:59:59 PM"),r=0;r<p;r++){for(this.model.timeDrillDown.showMeridian&&(f=h[r].toLowerCase(),w=t.isNullOrUndefined(t.preferredCulture(this.model.locale).calendars.standard[h[r]])?"":t.preferredCulture(this.model.locale).calendars.standard[h[r]][0],t.buildTag("div.e-header-"+f).attr(this._isIE8?{unselectable:"on"}:{}).append(t.buildTag("span.e-text").append(t.buildTag("span.e-hours-meridiantxt-"+f).text(w).attr({"aria-atomic":"true","aria-live":"assertive",role:"heading"}).attr(this._isIE8?{unselectable:"on"}:{}))).appendTo(this._sfTimeHour)),s=t.buildTag("table.e-dp-viewhours","",{}).data("e-table","data").attr({role:"grid"}).attr(this._isIE8?{unselectable:"on"}:{}),this._sfTimeHour.append(s),l=t.buildTag("tbody.e-timepicker-hours").attr(this._isIE8?{unselectable:"on"}:{}),u=t.buildTag("tr","").attr(this._isIE8?{unselectable:"on"}:{});this.timePicker._compareTime(o,i,!0);)a=this._localizeTime(i,b),e=t.buildTag("td.e-hour e-state-default",a),this.model.timeDrillDown.showMeridian&&e.addClass("e-hour-"+f),this._isIE8&&e.attr("unselectable","on"),u.append(e),c++,c>=v&&(c=0,l.append(u),u=t.buildTag("tr","").attr(this._isIE8?{unselectable:"on"}:{})),i=this.timePicker._createObject(i).getTime()+y;s.append(l);this.model.timeDrillDown.showMeridian&&(i=this.timePicker._createObject("12:00:00 PM"),o=this.timePicker._createObject("11:59:59 PM"))}t.buildTag("div.e-footer").append(t.buildTag("span.e-footer-icon")).append(t.buildTag("span.e-footer-text")).appendTo(this._sfTimeHour);n(".e-footer-text",this._sfTimeHour).html(this._localizedLabels.buttonText.timeNow);n(".e-hours-headertext",this._sfTimeHour).text(t.format(this.datePicker.model.value,"dd MMM yyyy"));this._on(this._sfTimeHour.find(".e-hour"),"click",n.proxy(this._hourNavHandler,this));this._on(n(".e-next",this._sfTimeHour),"click",n.proxy(this._prevNextHourHandler,this));this._on(n(".e-prev",this._sfTimeHour),"click",n.proxy(this._prevNextHourHandler,this));this._on(n(".e-footer",this._sfTimeHour),"click",this._todayBtn);n(".e-hours-headertext",this._sfTimeHour).on("click",n.proxy(this._forwardNavHandler,this));this._sfTimeHour.hide()},_localizeTime:function(i,r){return n.trim(t.format(this.timePicker._createObject(i),r,this.model.locale))},_renderMinsPopup:function(){var r,e,i,s,h,c,u,f,o;if(this._sfTimeMins=t.buildTag("div.e-timepicker e-popup e-widget "+this.model.cssClass+" e-time-minitues ","",{},{id:this._id?"e-time-minitues-"+this._id:""}).attr({"aria-hidden":"true"}).attr(this._isIE8?{unselectable:"on"}:{}),t.isTouchDevice()||this._sfTimeMins.addClass("e-ntouch"),this._drillDownContainer.append(this._sfTimeMins),t.buildTag("div.e-header").attr(this._isIE8?{unselectable:"on"}:{}).append(t.buildTag("span.e-prev").append(t.buildTag("a.e-icon e-arrow-sans-left").attr({role:"button"}).attr(this._isIE8?{unselectable:"on"}:{}))).append(t.buildTag("span.e-text").append(t.buildTag("span.e-minitues-headertext").attr({"aria-atomic":"true","aria-live":"assertive",role:"heading"}).attr(this._isIE8?{unselectable:"on"}:{}))).append(t.buildTag("span.e-next").append(t.buildTag("a.e-icon e-arrow-sans-right").attr({role:"button"}).attr(this._isIE8?{unselectable:"on"}:{}))).appendTo(this._sfTimeMins),t.buildTag("div.e-mins-header").attr(this._isIE8?{unselectable:"on"}:{}).append(t.buildTag("span.e-text").append(t.buildTag("span.e-minitues-meridiantxt").text("AM").attr({"aria-atomic":"true","aria-live":"assertive",role:"heading"}).attr(this._isIE8?{unselectable:"on"}:{}))).appendTo(this._sfTimeMins),r=t.buildTag("table.e-dp-viewmins","",{}).data("e-table","data").attr({role:"grid"}).attr(this._isIE8?{unselectable:"on"}:{}),this._sfTimeMins.append(r),e=t.buildTag("tbody.e-timepicker-mins").attr(this._isIE8?{unselectable:"on"}:{}),this._intervall<1)return!1;for(c=this._interval*6e4,i=this.timePicker._createObject("12:00:00 AM"),s=this.timePicker._createObject("11:59:59 PM"),u=t.buildTag("tr","").attr(this._isIE8?{unselectable:"on"}:{}),f=0;this.timePicker._compareTime(s,i,!0);)h=this._localizeTime(i,"HH:00"),o=t.buildTag("td.e-mins e-state-default",h),this._isIE8&&o.attr("unselectable","on"),u.append(o),f++,f>=4&&(f=0,e.append(u),u=t.buildTag("tr","").attr(this._isIE8?{unselectable:"on"}:{})),i=this.timePicker._createObject(i).getTime()+c;r.append(e);n(".e-minitues-headertext",this._sfTimeMins).text(n(".e-hours-headertext",this._sfTimeHour).text());t.buildTag("div.e-footer").append(t.buildTag("span.e-footer-icon")).append(t.buildTag("span.e-footer-text")).appendTo(this._sfTimeMins);n(".e-footer-text",this._sfTimeMins).html(this._localizedLabels.buttonText.timeNow);n(".e-minitues-headertext",this._sfTimeMins).text(t.format(this.datePicker.model.value,"dd MMM yyyy"));this.model.timeDrillDown.showMeridian||n(".e-mins-header",this._sfTimeMins).css("display","none");this._on(r.find(".e-mins"),"click",n.proxy(this._minsNavHandler,this));this._on(n(".e-next",this._sfTimeMins),"click",n.proxy(this._prevNextMinsHandler,this));this._on(n(".e-prev",this._sfTimeMins),"click",n.proxy(this._prevNextMinsHandler,this));this._on(n(".e-footer",this._sfTimeMins),"click",this._todayBtn);n(".e-minitues-headertext",this._sfTimeMins).on("click",n.proxy(this._forwardNavHandler,this));this._sfTimeMins.hide()},_todayBtn:function(){this._nowClick();this._hideResult()},_hourNavHandler:function(i){var r,f;if(this.model.readOnly||!this.model.enabled||i&&n(i.target).hasClass("e-disable"))return!1;i&&i.type&&i.preventDefault();n("table",this._sfTimeHour).find("td").removeClass("e-active");n(i.target).addClass("e-active");this._sfTimeHour.hide();this._sfTimeMins.show();this._addFocus(this._sfTimeMins);this.model.timeDrillDown.showMeridian?(f=n(i.target).hasClass("e-hour-am")?"AM":"PM",r=n(i.target).text()+":00 "+f):r=n(i.target).text();this._generateMins(r);var e=new Date(this._datetimeValue.toString()).setMinutes(this.model.value.getMinutes()),o=n.trim(t.format(this.timePicker._createObject(e),"HH:mm",this.model.locale)),s=n.trim(t.format(this.timePicker._createObject(e),"HH:00",this.model.locale)),u=(this.timePicker._parse(o)-this.timePicker._parse(s))/(this.model.timeDrillDown.interval*6e4);u=Math.ceil(u);this._hoverMins=this._setFocusByIndex("mins",u,this._sfTimeMins)},_minsNavHandler:function(i){if(this.model.readOnly||!this.model.enabled||i&&n(i.target).hasClass("e-disable"))return!1;if(i&&i.type&&i.preventDefault(),n("table",this._sfTimeMins).find("td").removeClass("e-active").removeClass("e-state-hover"),n(i.target).addClass("e-active"),this.model.timeDrillDown.showMeridian){var r=n(i.target).text()+" "+t.format(this._datetimeValue,"tt","en-US");r=this.timePicker._localizeTime(r)}else r=n(i.target).text();this.timePicker.option("value",r);this.datePicker.option("value",this._datetimeValue);this._datetimeValue=new Date(this.model.value.toString());this._updateInput();this.model.timeDrillDown.autoClose&&this._hideResult(i)},_generateMins:function(i){var f=n("table",this._sfTimeMins),h,s;f.empty();this.model.timeDrillDown.showMeridian?n(".e-mins-header",this._sfTimeMins).show():n(".e-mins-header",this._sfTimeMins).hide();h=this.model.timeDrillDown.showMeridian?"hh:mm":"HH:mm";n(".e-minitues-headertext",this._sfTimeMins).text(n(".e-hours-headertext",this._sfTimeHour).text());var e=t.buildTag("tbody.e-timepicker-mins").attr(this._isIE8?{unselectable:"on"}:{}),r,u,c,l,o=0,a=this.model.timeDrillDown.interval*6e4;for(r=this.timePicker._createObject(i),this._datetimeValue.setHours(r.getHours()),c=this.timePicker._createObject(r).getTime()+354e4,u=t.buildTag("tr","").attr(this._isIE8?{unselectable:"on"}:{}),e.append(u);this.timePicker._compareTime(c,r,!0);)o>=4&&(o=0,u=t.buildTag("tr","").attr(this._isIE8?{unselectable:"on"}:{}),e.append(u)),l=this._localizeTime(r,h),s=t.buildTag("td.e-mins e-state-default",l),this._isIE8&&s.attr("unselectable","on"),u.append(s),o++,r=this.timePicker._createObject(r).getTime()+a;f.append(e);n(".e-mins-header",this._sfTimeMins).find(".e-minitues-meridiantxt").text(t.format(this._datetimeValue,"tt",this.model.locale));this._disableRange("mins");this._on(f.find(".e-mins"),"click",n.proxy(this._minsNavHandler,this))},_prevNextHourHandler:function(t){if(this.model.readOnly||!this.model.enabled)return!1;t.preventDefault();var i=n(t.target).is("a")?n(t.target.parentNode):n(t.target),r=i.hasClass("e-prev")?!0:!1;this._processNextPrev(r,this._sfTimeHour)},_prevNextMinsHandler:function(t){if(this.model.readOnly||!this.model.enabled)return!1;t.preventDefault();var i=n(t.target).is("a")?n(t.target.parentNode):n(t.target),r=i.hasClass("e-prev")?!0:!1;this._processNextPrev(r,this._sfTimeMins)},_processNextPrev:function(i,r){var e,o,u,s;if(i&&r.find(".e-arrow-sans-left").hasClass("e-disable")||!i&&r.find(".e-arrow-sans-right").hasClass("e-disable"))return!1;e=n("table",r);s=e.get(0).className;switch(s){case"e-dp-viewhours":u=i?-1:1;this._datetimeValue.setDate(this._datetimeValue.getDate()+u);this._disableRange("hour");this._hoverHour=this._setFocusByIndex("hour",this._hoverHour,this._sfTimeHour);n(".e-hours-headertext",this._sfTimeHour).text(t.format(this._datetimeValue,"dd MMM yyyy"));n(".e-minitues-headertext",this._sfTimeMins).text(t.format(this._datetimeValue,"dd MMM yyyy"));break;case"e-dp-viewmins":u=i?-1:1;this._datetimeValue.setHours(this._datetimeValue.getHours()+u);this._generateMins(n.trim(t.format(this.timePicker._createObject(this._datetimeValue),"HH:00",this.model.locale)));var o=new Date(this._datetimeValue.toString()).setMinutes(this.model.value.getMinutes()),h=n.trim(t.format(this.timePicker._createObject(o),"HH:mm",this.model.locale)),c=n.trim(t.format(this.timePicker._createObject(o),"HH:00",this.model.locale)),f=(this.timePicker._parse(h)-this.timePicker._parse(c))/(this.model.timeDrillDown.interval*6e4);f=Math.ceil(f);this._disableRange("mins");this._hoverMins=this._setFocusByIndex("mins",f,this._sfTimeMins);n(".e-hours-headertext",this._sfTimeHour).text(t.format(this._datetimeValue,"dd MMM yyyy"));n(".e-minitues-headertext",this._sfTimeMins).text(t.format(this._datetimeValue,"dd MMM yyyy"))}},_forwardNavHandler:function(i,r){var f,e;if(this.model.readOnly||!this.model.enabled)return!1;e=this;i&&i.preventDefault();f=i?n(i.currentTarget).get(0).className:r.find(".e-text>span").get(0).className;switch(f){case"e-hours-headertext":this._sfTimeHour.css("display","none");this._dateContainer.css("display","block");this._addFocus(this._dateContainer.find(".e-datepicker"));break;case"e-minitues-headertext":this._sfTimeMins.css("display","none");this._disableRange("hour");var o=this._localizeTime(this.timePicker._createObject("12:00:00 AM"),"HH:00"),s=n.trim(t.format(this.timePicker._createObject(this._datetimeValue),"HH:00",this.model.locale)),u=(this.timePicker._parse(s)-this.timePicker._parse(o))/(this._interval*6e4);u=Math.floor(u);this._hoverHour=this._setFocusByIndex("hour",u,this._sfTimeHour);n(".e-hours-headertext",this._sfTimeHour).text(t.format(this._datetimeValue,"dd MMM yyyy"));n(".e-minitues-headertext",this._sfTimeMins).text(t.format(this._datetimeValue,"dd MMM yyyy"));this._sfTimeHour.css("display","block");this._addFocus(this._sfTimeHour)}},_renderDateControl:function(){var n=t.buildTag("input#"+this.element[0].id+"_date","",{},{type:"text"});this.popup.append(n);n.ejDatePicker({height:"0px",width:"0px",displayInline:!0,showDateIcon:!1,showFooter:this.model.timeDrillDown.enabled?this.model.timeDrillDown.showFooter:!1,enableStrictMode:!0,buttonText:this._localizedLabels.buttonText.today,minDate:this._stringToObject(this.model.minDateTime),maxDate:this._stringToObject(this.model.maxDateTime),dayHeaderFormat:this.model.dayHeaderFormat,startLevel:this.model.startLevel,depthLevel:this.model.depthLevel,startDay:this.model.startDay,stepMonths:this.model.stepMonths,showOtherMonths:this.model.showOtherMonths,specialDates:this.model.specialDates,highlightWeekend:this.model.highlightWeekend,headerFormat:this.model.headerFormat,enabled:this.model.enabled,enableRTL:this.model.enableRTL,showRoundedCorner:this.model.showRoundedCorner,readOnly:this.model.readOnly,cssClass:this.model.cssClass,locale:this.model.locale});t.isNullOrUndefined(this.model.value)||(this._datetimeValue=new Date(this.model.value.toString()));this.datePicker=n.data("ejDatePicker");this._datetimeValue=new Date(this.datePicker._dateValue.toString());this.model.startDay=this.datePicker.model.startDay;this.datePicker._getInternalEvents=!0;this.datePicker._dt_drilldown=!0;this.datePicker.popup.css({position:"static",display:"block"})},_renderTimeControl:function(){var i=t.buildTag("input#"+this.element[0].id+"_time","",{},{type:"text"}),n,r,u,f;this.popup.append(i);n=this.model.timePopupWidth;r=typeof n=="string"&&n.indexOf("%")!=-1||typeof n=="string"?parseInt(n)>0?n:105&&(this.model.timePopupWidth=105):n>0?n:105&&(this.model.timePopupWidth=105);i.ejTimePicker({height:"0px",width:"0px",interval:this.model.interval,timeFormat:this.model.timeDisplayFormat,popupWidth:r,enabled:this.model.enabled,enableRTL:this.model.enableRTL,showRoundedCorner:this.model.showRoundedCorner,readOnly:this.model.readOnly,cssClass:this.model.cssClass,locale:this.model.locale,disableTimeRanges:this.model.disableTimeRanges});this.timePicker=i.data("ejTimePicker");this.timePicker._renderDropdown();this.timePicker.popup.css({position:"static",display:"block"});this.timePicker._getInternalEvents=!0;this.timePicker.showDropdown=!0;this.timePicker._dateTimeInternal=!0;u=this.model.minDateTime?this._stringToObject(this.model.minDateTime):this.defaults.minDateTime;f=this.model.maxDateTime?this._stringToObject(this.model.maxDateTime):this.defaults.maxDateTime},_updateTimeHeight:function(){var n=this.popup.find(".e-timecontainer .e-header").is(":visible")?this.datePicker.popup.height()-this.popup.find(".e-header").height():this.datePicker.popup.height();n=this.popup.hasClass("e-dt-responsive")?"98px":n;this.timePicker.option("popupHeight",n)},_bindOperations:function(){var t=this;this.datePicker.option("layoutChange",function(){t._updateTimeHeight()});this.datePicker.option("outOfRange",function(){t.isValidState=!1});this.timePicker.option("outOfRange",function(){t.isValidState=!1});this.datePicker.option("change",function(n){t._refreshTimes(n)});this.datePicker.option("select",function(n){t._updateInput(n)});this.datePicker.option("dt_drilldown",function(n){t.model.timeDrillDown.enabled&&(t._updateInput(n),t._switchToDrilDown(n))});this.timePicker.option("select",function(){t._updateInput()});this.datePicker.option("_setAriaAttribute",function(i){n(t.element).attr("aria-activedescendant",i.attr("id"))})},_switchToDrilDown:function(){var r;this._dateContainer.hide();this._sfTimeHour.show();this._addFocus(this._sfTimeHour);r=new Date(this.model.value.toString());this._datetimeValue=new Date(r.setHours(this._datetimeValue.getHours(),this._datetimeValue.getMinutes(),this._datetimeValue.getSeconds(),this._datetimeValue.getMilliseconds()));this._disableRange("hour");var u=this._localizeTime(this.timePicker._createObject("12:00:00 AM"),"HH:00"),f=n.trim(t.format(this.timePicker._createObject(this._datetimeValue),"HH:00",this.model.locale)),i=(this.timePicker._parse(f)-this.timePicker._parse(u))/(this._interval*6e4);i=Math.floor(i);this._hoverHour=this._setFocusByIndex("hour",i,this._sfTimeHour);n(".e-hours-headertext",this._sfTimeHour).text(t.format(this._datetimeValue,"dd MMM yyyy"));n(".e-minitues-headertext",this._sfTimeMins).text(t.format(this._datetimeValue,"dd MMM yyyy"))},_disableRange:function(i){var l=i=="hour"?this._interval:this.model.timeDrillDown.interval,u=i=="hour"?this._sfTimeHour:this._sfTimeMins,h=i=="hour"?"e-hide-hour e-disable":"e-hide-mins e-disable",c,e,r,a,f,o,s;if(u.find("tbody tr td.e-"+i).removeClass(h),u.find(".e-arrow-sans-left").removeClass("e-disable"),u.find(".e-arrow-sans-right").removeClass("e-disable"),c=i=="hour"?this._localizeTime(this.timePicker._createObject("12:00:00 AM"),"HH:00"):this._localizeTime(n.trim(t.format(this.timePicker._createObject(this._datetimeValue),"HH:00",this.model.locale)),"HH:00"),this._compareDate(this.model.minDateTime,this._datetimeValue)){if(i=="mins"&&!(this.model.minDateTime.getHours()===this._datetimeValue.getHours()))return!1;for(e=n.trim(t.format(this.timePicker._createObject(this.model.minDateTime),"HH:mm",this.model.locale)),r=(this.timePicker._parse(e)-this.timePicker._parse(c))/(l*6e4),r=i=="hour"?Math.floor(r):Math.ceil(r),f=0;f<r;f++)o=u.find("tbody tr td.e-"+i),s=o[f],n(s).addClass(h);u.find(".e-arrow-sans-left").addClass("e-disable")}if(this._compareDate(this.model.maxDateTime,this._datetimeValue)){if(i=="mins"&&!(this.model.maxDateTime.getHours()===this._datetimeValue.getHours()))return!1;for(e=n.trim(t.format(this.timePicker._createObject(this.model.maxDateTime),"HH:mm",this.model.locale)),r=(this.timePicker._parse(e)-this.timePicker._parse(c))/(l*6e4),r=Math.floor(r)+1,a=u.find("tbody tr td.e-"+i).length,f=r;f<a;f++)o=u.find("tbody tr td.e-"+i),s=o[f],n(s).addClass(h);u.find(".e-arrow-sans-right").addClass("e-disable")}},_setFocusByName:function(t,i,r){var f=r.find("tbody tr td.e-"+t),e,u;return n(f).each(function(n,t){if(t.innerHTML==i){e=n;return}}),u=f[e],u||(u=f.last()),r.find("table td").removeClass("e-state-hover").removeClass("e-active"),n(u).hasClass("e-hide-"+t)||n(u).addClass("e-state-hover"),this._setActiveState(t,r),e},_setFocusByIndex:function(t,i,r){var f=r.find("tbody tr td.e-"+t),u;return u=f[i],u||(u=f.last()),r.find("table td").removeClass("e-state-hover").removeClass("e-active"),n(u).hasClass("e-hide-"+t)||n(u).addClass("e-state-hover"),this._setActiveState(t,r),i},_setActiveState:function(i,r){var h=r.find("tbody tr td.e-"+i),f,l=this,u=-1,o,e;switch(i){case"hour":this._compareDate(this.model.value,this._datetimeValue)&&(o=this._localizeTime(this.timePicker._createObject("12:00:00 AM"),"HH:00"),e=n.trim(t.format(this.timePicker._createObject(this.model.value),"HH:00",this.model.locale)),u=(this.timePicker._parse(e)-this.timePicker._parse(o))/(this._interval*6e4),u=Math.floor(u));break;case"mins":if(this._compareDate(this.model.value,this._datetimeValue)&&this.model.value.getHours()===this._datetimeValue.getHours()){var s=new Date(this._datetimeValue.toString()).setMinutes(this.model.value.getMinutes()),e=n.trim(t.format(this.timePicker._createObject(s),"HH:mm",this.model.locale)),c=n.trim(t.format(this.timePicker._createObject(s),"HH:00",this.model.locale));u=(this.timePicker._parse(e)-this.timePicker._parse(c))/(this.model.timeDrillDown.interval*6e4);u=Math.ceil(u)}}f=h[u];f&&(r.find("table td").removeClass("e-active"),n(f).removeClass("e-state-hover").addClass("e-active"))},_compareDate:function(n,t){var i=new Date(n.toString()).setHours(0,0,0,0),r=new Date(t.toString()).setHours(0,0,0,0);return+i==+r?!0:!1},_updateInput:function(t){var u=+new Date<+this.model.minDateTime||+new Date>+this.model.maxDateTime?this.model.minDateTime:(new Date).setHours(0,0,0,0),i=this._getDate()||new Date(u),r=this._getTime()||this.timePicker._createObject(u);this.model.value=new Date(i.getFullYear(),i.getMonth(),i.getDate(),r.getHours(),r.getMinutes(),r.getSeconds());this._updateDateTime();this._raiseChangeEvent();this._updateModel(t,!0);n(this.element).attr("aria-activedescendant",n(this.datePicker.popup).find(".e-active").attr("id"));t&&(t.cancel=!0)},_updateDateTime:function(){this.isValidState=!0;var n=this._objectToString(this.model.value);this.element.val(n);this._removeWatermark()},_refreshTimes:function(n){var i=this._getDateObj(n.value,this.datePicker.model.dateFormat),r,f,u;if(!i)return!1;this.isValidState=!0;this._compare(i,this._setEmptyTime(this.model.minDateTime))?(r=this._getFormat(this.model.minDateTime,this.timePicker.model.timeFormat),u=this._getTime(),this.timePicker.option("minTime",r),this.model.enableStrictMode||this._updateInput()):this.timePicker.option("minTime",this._defaultMinVal());this._compare(i,this._setEmptyTime(this.model.maxDateTime))?(f=this._getFormat(this.model.maxDateTime,this.timePicker.model.timeFormat),u=this._getTime(),this.timePicker.option("maxTime",f),this.model.enableStrictMode||this._updateInput()):this.timePicker.option("maxTime",this._defaultMaxVal());this.timePicker._changeActiveEle();t.isNullOrUndefined(this.model.disableDateTimeRanges)||this._setDisabledTimeRanges(n)},_setDisabledTimeRanges:function(n){var t=n?n.value:this._setEmptyTime(this.model.value);this._between=[];this._initDisableTimeRanges(t);this._datesDisabled||(this._disableBetweenDates(),this.datePicker.option("blackoutDates",this._between))},_defaultMinVal:function(){var n=(new Date).setHours(0,0,0,0);return t.format(this.timePicker._createObject(n),this.timePicker.model.timeFormat,this.timePicker.model.locale)},_defaultMaxVal:function(){var n=(new Date).setHours(23,59,59,59);return t.format(this.timePicker._createObject(n),this.timePicker.model.timeFormat,this.timePicker.model.locale)},_updateValues:function(){var n=this.model.enableStrictMode&&this.model.value==null?this.element.val():this.model.value;this.model.value!=null&&(this.datePicker.option("value",this.model.value),this.timePicker.option("value",this.model.value));this._setValue(n);this._validateMinMax();this._preVal=this.element.val();this._checkErrorClass()},_specificFormat:function(){var i=t.globalize._getDateParseRegExp(t.globalize.findCulture(this.model.locale).calendar,this.model.dateTimeFormat);return n.inArray("dddd",i.groups)>-1||n.inArray("ddd",i.groups)>-1},_changeEditable:function(t){var i=t?"_on":"_off";if(this.element.is(":input")){if(t)this.model.readOnly||this.element.attr("readonly",!1),this.element.off("mousedown",n.proxy(this._showhidePopup,this));else{this.model.readOnly||this.element.attr("readonly","readonly");this.element.on("mousedown",n.proxy(this._showhidePopup,this))}this[i](this.element,"blur",this._targetBlur);this[i](this.element,"focus",this._targetFocus);this[i](this.element,"keydown",this._keyDownOnInput)}this._change("allowEdit",t)},_setValue:function(n){return n&&(typeof JSON!="object"||JSON.stringify(n)!=="{}")?typeof n=="string"?this._extISORegex.exec(n)||this._basicISORegex.exec(n)?this._checkObject(this._dateFromISO(n)):(this.element.val(n),this._updateModel(),this._validateMinMax(),this._checkStrictMode(),this.wrapper.addClass("e-valid")):n instanceof Date&&this._isValidDate(n)&&this._checkObject(n):(this.element.val(""),this.model.value=null,this.isValidState=!0,this.wrapper.removeClass("e-valid")),t.isNullOrUndefined(this.model.disableDateTimeRanges)||this._validateDisableRanges(),this._checkErrorClass(),this.model.value},_checkObject:function(n){n instanceof Date&&this._isValidDate(n)&&(this.model.value=n,this._updateDateTime(),this._validateMinMax(),this._checkStrictMode())},_dateFromISO:function(n){var i=this._extISORegex.exec(n)||this._basicISORegex.exec(n),c="",l="",a="",v,e,o,f,r,t,s,u,h,y;if(i){for(e=0;e<this._dates.length;e++)if(this._dates[e][1].exec(i[1])){c=this._dates[e][0];break}if(i[3])for(o=0;o<this._times.length;o++)if(this._times[o][1].exec(i[3])){l=(i[2]||" ")+this._times[o][0];break}for(i[4]&&this._zeroRegex.exec(i[4])&&(a="Z"),v=c+l+a,f=v.match(this._tokens),t=[],u=0;u<f.length;u++)h=f[u],s=this._checkLiteral(f[u]),y=this._numberRegex[s?f[u].toLowerCase():h.length]||new RegExp("^\\d{1,"+h.length+"}"),r=n.match(y),r&&(n.substr(0,n.indexOf(r))>=0&&!s&&(f[u].indexOf("M")>=0?t.push(parseInt(r[0])-1):t.push(parseInt(r[0]))),n=n.slice(n.indexOf(r[0])+r[0].length));return i[4]=="Z"?new Date(Date.UTC.apply(null,t)):new Date(t[0],t[1],t[2],t[3],t[4],t[5])}return new Date(n+"")},_checkLiteral:function(n){var t=n.toLowerCase();return t=="t"||t=="z"||t==":"||t=="-"?!0:!1},_validateValue:function(n){var r=t.parseDate(n,this.model.dateTimeFormat,this.model.locale),u=null;this.timePicker!=i&&(u=this._getFormat(r,this.timePicker.model.timeFormat));!r||r<this.model.minDateTime||r>this.model.maxDateTime||!t.isNullOrUndefined(this.timePicker)&&this.timePicker._ensureTimeRange(u)?(this.model.value=null,this._change("value",this.model.value),this.isValidState=!1):(this._change("value",this.model.value),this.isValidState=!0)},_validateMinMax:function(){var i,n,t;if(i=this.model.value?this._stringToObject(this.model.value):null,n=this.model.minDateTime?this._stringToObject(this.model.minDateTime):this.defaults.minDateTime,t=this.model.maxDateTime?this._stringToObject(this.model.maxDateTime):this.defaults.maxDateTime,!i||!n||!t)return!1;n>t&&(this.model.minDateTime=this.model.maxDateTime);i<n&&(this.model.enableStrictMode?this.model.enableStrictMode&&(this.popup&&(this.datePicker.option("minDate",this._getFormat(n,this.datePicker.model.dateFormat)),this.timePicker.option("minTime",this._getFormat(n,this.timePicker.model.timeFormat))),this.isValidState=!1):(this._setValue(n),this.isValidState=!0));i>t&&(this.model.enableStrictMode?this.model.enableStrictMode&&(this.popup&&(this.datePicker.option("maxDate",this._getFormat(t,this.datePicker.model.dateFormat)),this.timePicker.option("maxTime",this._getFormat(t,this.timePicker.model.timeFormat))),this.isValidState=!1):(this._setValue(t),this.isValidState=!0));i<n||i>t||(this.isValidState=!0)},_checkProperties:function(){this.model.readOnly&&this._readOnly(!0);this.model.showRoundedCorner&&this._setRoundedCorner(!0);this.model.enableRTL&&this._setRtl(!0);this.model.enabled&&this._enabled(!0);this.model.enabled?this.model.enabled&&this.element.hasClass("e-disable")&&this._enabled(!0):this._enabled(!1);this.model.name=this._options.name?this.model.name:this.element.attr("name")?this.element.attr("name"):this.element[0].id;this.element.attr("name",this.model.name);this._checkStrictMode();this._checkErrorClass();this._setWaterMark()},_checkStrictMode:function(){this.model.enableStrictMode?this.model.enableStrictMode&&(this.isValidState||(this.model.value=null,this.isValidState=!1)):this.isValidState||(this.model.value<this.model.minDateTime?(this.element.val(this._objectToString(this.model.minDateTime)),this.model.value=this.model.minDateTime,this.isValidState=!0):this.model.value>this.model.maxDateTime?(this.element.val(this._objectToString(this.model.maxDateTime)),this.model.value=this.model.maxDateTime,this.isValidState=!0):(this.model.value="",this.element.val(""),this.isValidState=!0))},_targetFocus:function(n){n.preventDefault();this.isFocused=!0;this.wrapper.addClass("e-focus");this.wrapper.removeClass("e-error");this._isSupport||this._hiddenInput.css("display","none");this._prevDateTimeVal=this.element.val();this.model.showPopupButton||this.model.readOnly||this._showResult();this.model.showPopupButton||this._on(this.element,"click",this._elementClick);!this.model.showPopupButton&&this.model.readOnly&&this._off(this.element,"click",this._elementClick);this._trigger("focusIn",{value:this.model.value});this.wrapper.addClass("e-valid")},_targetBlur:function(){var n,i;this.isFocused=!1;this.wrapper.removeClass("e-focus");this.model.showPopupButton||this._hideResult();n=t.parseDate(this.element.val(),this.model.dateTimeFormat,this.model.locale);n&&!this.model.enableStrictMode&&(n<this.model.minDateTime||n>this.model.maxDateTime)&&(n=n<this.model.minDateTime?this.model.minDateTime:this.model.maxDateTime,this.element.val(this._objectToString(n)));i=t.parseDate(this.element.val(),this.model.dateTimeFormat,this.model.locale);i!=null||this.model.enableStrictMode||(this._prevDateTimeVal==null||this.element.val()==""?this.element.val(""):this.element.val(this._preVal));this._valueChange();this.model.enableStrictMode?this.element.val()!=""&&this._validateValue(this.element.val()):this.isValidState?this._prevDateTimeVal=this.element.val():(this.element.val(this._prevDateTimeVal),this._preVal=this._prevDateTimeVal,this.model.value=this._stringToObject(this._prevDateTimeVal),this.isValidState=!0);this._isSupport||this.element.val()!=""||this._hiddenInput.css("display","block");this._checkErrorClass();this.model.showPopupButton||this._off(this.element,"click",this._elementClick);this._trigger("focusOut",{value:this.model.value});t.isNullOrUndefined(this.model.value)?this.wrapper.removeClass("e-valid"):this.wrapper.addClass("e-valid");this._previousDateUpdate()},_previousDateUpdate:function(){var n=t.parseDate(this._prevDateTime,this.model.dateTimeFormat),i=t.parseDate(this.element.val(),this.model.dateTimeFormat);return+n==+i||(this._preValString=this._prevDateTime,this._prevDateTime=this.element.val()),this._preValString},_elementClick:function(){this.isPopupOpen||this._showResult()},_keyDownOnInput:function(n){switch(n.keyCode){case 40:n.altKey&&this._showhidePopup();break;case 37:case 39:this.model.timeDrillDown.enabled||n.altKey&&this.isPopupOpen&&(n.preventDefault(),this._addPrevNextFocus(n.keyCode==37));break;case 27:n.preventDefault();case 9:this._hideResult();break;case 13:var i=t.parseDate(this.element.val(),this.model.dateTimeFormat);if(i!=null||this.model.enableStrictMode||(this._prevDateTimeVal==null||this.element.val()==""?this.element.val(""):this.element.val(this._preVal)),this._valueChange(),!this.model.timeDrillDown.enabled){this._valueChange();this.model.enableStrictMode&&this._checkErrorClass();break}}},_addFocus:function(t){if(!t.hasClass("e-focus"))if(this._removeFocus(),t.addClass("e-focus"),t.hasClass("e-datepicker e-popup"))n(document).on("keydown",n.proxy(this.datePicker._keyboardNavigation,this.datePicker));else if(t.hasClass("e-timecontainer"))n(document).on("keydown",n.proxy(this.timePicker._keyDownOnInput,this.timePicker));else if(t.hasClass("e-time-hours"))n(document).on("keydown",n.proxy(this._keyDownOnHours,this));else if(t.hasClass("e-time-minitues"))n(document).on("keydown",n.proxy(this._keyDownOnMinutes,this));else if(t.hasClass("e-dt-button"))n(document).on("keydown",n.proxy(this._buttonClick,this))},_removeFocus:function(){var t=this._getFocusedElement();t.length>0&&(t.removeClass("e-focus"),t.hasClass("e-datepicker e-popup")?n(document).off("keydown",n.proxy(this.datePicker._keyboardNavigation,this.datePicker)):t.hasClass("e-timecontainer")?n(document).off("keydown",n.proxy(this.timePicker._keyDownOnInput,this.timePicker)):t.hasClass("e-time-hours")?n(document).off("keydown",n.proxy(this._keyDownOnHours,this)):t.hasClass("e-time-minitues")?n(document).off("keydown",n.proxy(this._keyDownOnMinutes,this)):t.hasClass("e-dt-button")&&n(document).off("keydown",n.proxy(this._buttonClick,this)))},_addPrevNextFocus:function(n){var i=this._getFocusedElement(),t;i.length>0?i.hasClass("e-datepicker e-popup")?t=n?this.popup.find(".e-dt-done"):this.popup.find(".e-timecontainer"):i.hasClass("e-timecontainer")?t=n?this.popup.find(".e-datecontainer >.e-datepicker.e-popup"):this.popup.find(".e-dt-today"):i.hasClass("e-dt-today")?t=n?this.popup.find(".e-timecontainer"):this.popup.find(".e-dt-now"):i.hasClass("e-dt-now")?t=n?this.popup.find(".e-dt-today"):this.popup.find(".e-dt-done"):i.hasClass("e-dt-done")&&(t=n?this.popup.find(".e-dt-now"):this.popup.find(".e-datecontainer >.e-datepicker.e-popup")):t=n?this.popup.find(".e-dt-done"):this.popup.find(".e-datecontainer >.e-datepicker.e-popup");this._addFocus(t)},_getFocusedElement:function(){return this.popup.children("div").find("div.e-focus")},_keyDownOnHours:function(n){var t;if(n.keyCode==37||n.keyCode==38||n.keyCode==39||n.keyCode==40||n.keyCode==13||n.keyCode==36||n.keyCode==35){n.preventDefault&&n.preventDefault();t={row:null,col:null};t.col=this._sfTimeHour.find("tbody tr td.e-state-hover").index();t.row=this._sfTimeHour.find("tbody tr td.e-state-hover").parent().index();t.col=t.col!=-1?t.col+1:this._sfTimeHour.find("tbody tr td.e-active").index()+1;t.row!=-1?(t.row=t.row+1,this.model.timeDrillDown.showMeridian&&this._sfTimeHour.find("tbody tr td.e-state-hover").hasClass("e-hour-pm")&&(t.row=t.row+2)):(t.row=this._sfTimeHour.find("tbody tr td.e-active").parent().index()+1,this.model.timeDrillDown.showMeridian&&this._sfTimeHour.find("tbody tr td.e-active").hasClass("e-hour-pm")&&(t.row=t.row+2));var f=this._sfTimeHour.find("table")[0].className,i,r=this._sfTimeHour.find("tbody.e-timepicker-hours tr").length,u=this.model.timeDrillDown.showMeridian?6:4;i=this._changeRowCol(t,n.keyCode,r,u,"hours",n.ctrlKey);n.ctrlKey||(this._hoverHour=this._sfTimeHour.find("tbody.e-timepicker-hours tr td").index(i));n.ctrlKey||(this._sfTimeHour.find("table td").removeClass("e-state-hover"),i.addClass("e-state-hover"))}},_changeRowCol:function(t,i,r,u,f,e){var c,o,l,h={parent:null,child:null},s,y,a,v,p;switch(f){case"hours":c="tbody.e-timepicker-hours tr td.e-hour";h.parent=".e-timepicker-hours";h.child=".e-hour";l=".e-hide-hour";o=this._sfTimeHour;break;case"mins":c="tbody.e-timepicker-mins tr td.e-mins";h.parent=".e-timepicker-mins";h.child=".e-mins";l=".e-hide-mins";o=this._sfTimeMins;u=o.find("tbody"+h.parent+" tr:nth-child("+t.row+") td"+h.child).length}if(t.row<=0&&t.col<=0)return o.find(c+":not(.e-disable):first");y=this;switch(i){case 36:return o.find(c+":not(.e-disable):first");case 35:return o.find(c+":not(.e-disable):last");case 38:if(e)this._forwardNavHandler(null,o);else if(t.row>1)t.row-=1;else return this._processNextPrev(!0,o),o.find(c+":nth-child("+t.col+"):last");if(s=this._getCell(t,h,o).not(l),s.length<=0){if(s=this._findVisible(t,h,"up",o),s!==null)return s;this._processNextPrev(!0,o);s=o.find(c+":nth-child("+t.col+"):last")}return s;case 37:if(e)return this._processNextPrev(!0,o),o.find("tbody tr td.e-state-hover");if(t.col>1)t.col-=1;else if(t.row>1)t={row:t.row-1,col:u},f=="mins"&&(t.col=u=o.find("tbody"+h.parent+" tr:nth-child("+t.row+") td"+h.child).length);else return this._processNextPrev(!0,o),o.find(c+":not(.e-disable):last");if(s=this._getCell(t,h,o).not(l),s.length<=0){if(s=this._findVisible(t,h,"left",o),s!==null)return s;this._processNextPrev(!0,o);s=o.find(c+":not(.e-disable):last")}return s;case 39:if(e)return this._processNextPrev(!1,o),o.find("tbody tr td.e-state-hover");if(t.col<u)t.col+=1;else if(t.row<r)t={row:t.row+1,col:1};else return this._processNextPrev(!1,o),o.find(c+":not(.e-disable):first");if(s=this._getCell(t,h,o).not(l),s.length<=0){if(s=this._findVisible(t,h,"right",o),s!==null)return s;this._processNextPrev(!1,o);s=o.find(c+":not(.e-disable):first")}return s;case 40:if(!e){if(t.row<r)t.row+=1;else return this._processNextPrev(!1,o),o.find(c+":nth-child("+t.col+"):first");if(s=this._getCell(t,h,o).not(l),s.length<=0){if(s=this._findVisible(t,h,"down",o),s!==null)return s;this._processNextPrev(!1,o);s=o.find(c+":nth-child("+t.col+"):first")}return s}case 13:a=this._getCell(t,h,o);p=n(a)[0];v={type:null,target:a};f=="hours"&&this._hourNavHandler(v);f=="mins"&&this._minsNavHandler(v)}return this._getCell(t,h,o).not(l)},_getCell:function(t,i,r){var f=t.row,u;return this.model.timeDrillDown.showMeridian&&t.row>2&&r.hasClass("e-time-hours")&&(f=f-2),u=r.find("tbody"+i.parent+" tr:nth-child("+f+") td"+i.child+":nth-child("+t.col+")"),this.model.timeDrillDown.showMeridian&&u.length>0&&r.hasClass("e-time-hours")&&(u=t.row<=2?n(u[0]):n(u[1])),u},_findVisible:function(n,t,i,r){for(var e,f=n.col,u=n.row,s=t.child.slice(1,t.child.length),o=0;o>=0;o++){if(e=this._getCell({row:u,col:f},t,r),e.length<=0)return null;if(e.hasClass("e-disable")||!e.is(":visible")){if((i=="right"||i=="left"?i=="right"?f++:f--:i=="down"?u++:u--,u<=0||u>r.find("tbody"+t.parent+" tr").length)||(f>r.find("tbody"+t.parent+" tr:nth-child("+u+") td").length&&(u++,f=1),f<=0&&(u--,f=r.find("tbody"+t.parent+" tr:nth-child("+u+") td").length),u<=0||u>r.find("tbody"+t.parent+" tr").length))return null}else if(e.hasClass(s))return n.col=f,n.row=u,this._getCell(n,t,r)}},_keyDownOnMinutes:function(n){var t;if(n.keyCode==37||n.keyCode==38||n.keyCode==39||n.keyCode==40||n.keyCode==13||n.keyCode==36||n.keyCode==35){n.preventDefault&&n.preventDefault();t={row:null,col:null};t.col=this._sfTimeMins.find("tbody tr td.e-state-hover").index();t.row=this._sfTimeMins.find("tbody tr td.e-state-hover").parent().index();t.col=t.col!=-1?t.col+1:this._sfTimeMins.find("tbody tr td.e-active").index()+1;t.row=t.row!=-1?t.row+1:this._sfTimeMins.find("tbody tr td.e-active").parent().index()+1;var u=this._sfTimeMins.find("table")[0].className,i,r=this._sfTimeMins.find("tbody.e-timepicker-mins tr").length;i=this._changeRowCol(t,n.keyCode,r,4,"mins",n.ctrlKey);n.ctrlKey||(this._hoverHour=this._sfTimeMins.find("tbody.e-timepicker-mins tr td").index(i));n.ctrlKey||(this._sfTimeMins.find("table td").removeClass("e-state-hover"),i.addClass("e-state-hover"))}},_valueChange:function(n){this.model.enableStrictMode?this.model.enableStrictMode&&(this._preVal!=this.element.val()||this.model.value<this.model.minDateTime||this.model.value>this.model.maxDateTime)&&(this._updateModel(),this._raiseChangeEvent(n)):(this._preVal!=this.element.val()&&(this._preVal=this.element.val(),this._updateModel(),this._validateMinMax(),this._raiseChangeEvent(n)),this._setWaterMark())},_updateModel:function(n,r){var u,f;if(this._stopRefresh){this._stopRefresh=!1;return}u=this.element.val();u==""?(this.model.value=null,this._change("value",this.model.value),this.isValidState=!0):(f=n!=i&&n.type=="select"||this._prevDateTimeVal==this.element.val()?this.model.value:t.parseDate(u,this.model.dateTimeFormat,this.model.locale),f?(this.model.value=f,this.isValidState=!0,this._refreshPopup(r),this._specificFormat()&&this._prevDateTimeVal!=this.element.val()&&this.element.val(this._objectToString(this.model.value))):(this.model.value=null,this._change("value",this.model.value),this.isValidState=!1,this.model.enableStrictMode||this.element.val(this._objectToString(this.model.value))))},_refreshPopup:function(n){if(this.isValidState&&this.isPopupOpen){var t=this._setEmptyTime(this.model.value),i=this._setEmptyDate(this.model.value),r=this._getDate(),u=this._getTime();r&&this._compare(r,t)||this.datePicker.option("value",t);u&&this._compare(u,i)||n||this.timePicker.option("value",i)}},_buttonClick:function(n){if(n.keyCode==13){n.preventDefault();var t=this._getFocusedElement();t.hasClass("e-dt-today")?this._todayClick():t.hasClass("e-dt-now")?this._nowClick():t.hasClass("e-dt-done")&&this._doneClick()}},_todayClick:function(){if(!this.model.enabled||this.model.readOnly)return!1;this.datePicker.popup.find(".today").hasClass("e-active")&&this.datePicker.popup.children("table").hasClass("e-dp-viewdays")&&this.element.val()!=""&&this.isValidState||(this.datePicker._setCurrDate(),this._updateInput())},_nowClick:function(){var r;if(!this.model.enabled||this.model.readOnly)return!1;this.timePicker.setCurrentTime();var u=this.model.minDateTime,f=this.model.maxDateTime,n=this.datePicker.model.value,i=new Date;n=t.isNullOrUndefined(n)?new Date:n;r=new Date(n.getFullYear(),n.getMonth(),n.getDate(),i.getHours(),i.getMinutes(),i.getSeconds());r<u?this.timePicker.option("value",this.timePicker._localizeTime(u)):r>f&&this.timePicker.option("value",this.timePicker._localizeTime(f));this._updateInput()},_doneClick:function(){this._hideResult()},_iconClick:function(n){n.preventDefault();this.isFocused||t.isTouchDevice()||this.element.focus();this._showhidePopup();this.model.disableDateTimeRanges&&t.isNullOrUndefined(this.model.value)&&this._setDisabledTimeRanges(this.datePicker.model.value);this._isIE9&&this.popup.find(".e-popup-container").css("display","inline-block")},_setInitialSelection:function(){var r=this.timePicker.ul.find("li"),u,f,e,t,i,o;if(r.hasClass("e-hover")){this._calcScrollTop();return}u=this.timePicker._setEmptyDate(new Date);f=u;this.timePicker.minTime&&!this._compareTime(this._createObject(u),this.timePicker.minTime,!0)&&(f=this.timePicker.minTime);this.timePicker.maxTime&&!this._compareTime(this.timePicker.maxTime,this._createObject(u),!0)&&(f=this.timePicker.maxTime);e=r.first().html();t=(this.timePicker._parse(f)-this.timePicker._parse(e))/(this.timePicker.model.interval*6e4);t=Math.round(t);i=t==r.length?t:t+1;(i<0||i>r.length||isNaN(i))&&(i=1);o=n(this.timePicker.ul.find("li")[i-1]);o.addClass("e-hover");this._calcScrollTop()},_calcScrollTop:function(){var r=this.timePicker.ul.outerHeight(),n=this.timePicker.ul.find("li").outerHeight(),t,i;t=this.timePicker.ul.find("li.e-hover").index();i=n*t-(this.timePicker.popupList.outerHeight()-n)/2;this.timePicker.scrollerObj.setModel({scrollTop:i})},_showhidePopup:function(){if(this.model.readOnly)return!1;this.isPopupOpen?this._hideResult():this._showResult()},_showResult:function(){if((this.popup||this._renderDropdown(),this.isPopupOpen||!this.model.enabled)||(this._setRtl(this.model.enableRTL),this._setRoundedCorner(this.model.showRoundedCorner),this._trigger("beforeOpen",{element:this.popup})))return!1;this.isPopupOpen=!0;this.element.attr({"aria-expanded":"true"});this.model.value!=null?n(this.element).attr("aria-activedescendant",n(this.datePicker.popup.find(".e-active")).attr("id")):n(this.element).attr("aria-activedescendant",n(this.datePicker.popup).find(".today").attr("id"));this._setListPosition();this._checkForResponsive();var i=this;this.popup.slideDown(this.model.enableAnimation?200:0,function(){i._on(n(document),"mousedown",i._OnDocumentClick);i.model.timeDrillDown.enabled&&i._addFocus(i._dateContainer.find(".e-datepicker"));i.timePicker.model.value||i._setInitialSelection()});this._updateModel();this._updateTimeHeight();this._validateMinMax();this._on(n(window),"resize",this._OnWindowResize);this._on(t.getScrollableParents(this.wrapper),"scroll",this._hideResult);this._on(t.getScrollableParents(this.wrapper),"touchmove",this._hideResult);this._raiseEvent("open");this._initial&&(this.timePicker._refreshScroller(),this.timePicker._changeActiveEle(),this._initial=!1);this.wrapper.addClass("e-active");this._addFocus(n(this.datePicker.popup))},_hideResult:function(i){if(!i||i.type!="touchmove"&&i.type!="scroll"||!(n(i.target).parents("#"+this.popup[0].id).length>0)){var r=this;if(!this.isPopupOpen||this._trigger("beforeClose",{element:this.popup}))return!1;if(this.isPopupOpen=!1,this.element.attr({"aria-expanded":"false"}),this._removeFocus(),this._popClose&&i&&i.type!="click"){this.isPopupOpen=!0;return}this.popup.slideUp(this.model.enableAnimation?100:0,function(){r.model&&(r.model.timeDrillDown.enabled&&(r._sfTimeHour.hide(),r._sfTimeMins.hide(),r._dateContainer.show()),t.isNullOrUndefined(r.model.value)||(r._datetimeValue=new Date(r.model.value.toString())))});this._raiseEvent("close");this._off(n(document),"mousedown",this._OnDocumentClick);this._off(n(window),"resize",this._OnWindowResize);this._off(t.getScrollableParents(this.wrapper),"scroll",this._hideResult);this._off(t.getScrollableParents(this.wrapper),"touchmove",this._hideResult);this.wrapper.removeClass("e-active")}},_setListPosition:function(){var r=this.wrapper,i=this._getOffset(r),s,v=n(document).scrollTop()+n(window).height()-(i.top+n(r).outerHeight()),h=i.top-n(document).scrollTop(),u=this.popup.outerHeight(),c=this.popup.outerWidth(),f=i.left,o=r.outerHeight(),l=(o-r.height())/2,y=this._getZindexPartial(),e=3,p=this.model.popupPosition,a;a=this.model.popupPosition==t.PopupPosition.Bottom?(u<v||u>h?i.top+o+e:i.top-u-e)-l:(u>h?i.top+o+e:i.top-u-e)-l;s=n(document).scrollLeft()+n(window).width()-f;(this.model.enableRTL||c>s&&c<f+r.outerWidth())&&(f-=this.popup.outerWidth()-r.outerWidth());this.popup.css({left:f+"px",top:a+"px","z-index":y})},_getOffset:function(n){return t.util.getOffset(n)},_OnDocumentClick:function(t){this.model&&(n(t.target).is(this.popup)||n(t.target).parents(".e-datetime-popup").is(this.popup)||n(t.target).is(this.wrapper)||n(t.target).parents(".e-datetime-wrap").is(this.wrapper)?(n(t.target).is(this.popup)||n(t.target).parents(".e-datetime-popup").is(this.popup))&&(t.preventDefault(),n(t.target).parents(".e-datepicker").length>0?this._addFocus(n(t.target).parents(".e-datepicker")):n(t.target).parents(".e-timecontainer").length>0?this._addFocus(n(t.target).parents(".e-timecontainer")):n(t.target).hasClass("e-dt-button")?this._addFocus(n(t.target)):n(t.target).parents(".e-time-hours").length>0?this._addFocus(n(t.target).parents(".e-time-hours")):n(t.target).parents(".e-time-minitues").length>0?this._addFocus(n(t.target).parents(".e-time-minitues")):this._removeFocus()):this._hideResult())},_OnWindowResize:function(){this._setListPosition();this._checkForResponsive();this._updateTimeHeight()},_raiseChangeEvent:function(i){var e=t.parseDate(this._prevDateTimeVal,this.model.dateTimeFormat),u=t.parseDate(this.element.val(),this.model.dateTimeFormat),f,r;+e==+u?this._prevDateTimeVal!=this.element.val()&&(r={prevDateTime:this._prevDateTimeVal,value:this.element.val(),isValidState:this.isValidState},this._prevDateTimeVal=this.element.val(),this._trigger("_change",r)):(this._preVal=this.element.val(),r={prevDateTime:this._prevDateTimeVal,value:this.element.val(),isInteraction:!i,isValidState:this.isValidState},this.timePicker&&this.timePicker.model?(f=this._getFormat(u,this.timePicker.model.timeFormat),this.timePicker._ensureTimeRange(f)?(this._prevDateTimeVal="",this.model.value=null,this.isValidState=!1):this._prevDateTimeVal=this.element.val()):this._prevDateTimeVal=this.element.val(),this._trigger("_change",r),r.value=n.trim(this.element.val())==""?null:this.element.val(),r.value=this._formatter(this.model.value,this.model.dateTimeFormat),this._trigger("change",r))},_formatter:function(n,i){var r=this._checkFormat(i);return t.format(n,r,this.model.locale)},_raiseEvent:function(n){var i=this._previousDateUpdate();return this.element!=null&&this.model[n]?this._trigger(n,{prevDateTime:t.isNullOrUndefined(i||this._preValString)?"":i||this._preValString,value:this.element.val()}):!1},_getDateTimeFormat:function(){var n=t.preferredCulture(this.model.locale).calendar.patterns;this.model.dateTimeFormat||(this.model.dateTimeFormat=n.d+" "+n.t);this.model.timeDisplayFormat||(this.model.timeDisplayFormat=n.t)},_getZindexPartial:function(){return t.util.getZindexPartial(this.element,this.popup)},_checkErrorClass:function(){this.isValidState?this.wrapper.removeClass("e-error"):this.wrapper.addClass("e-error")},_getDate:function(){return this.datePicker.model.value},_getTime:function(){return this._getDateObj(this.timePicker.model.value,this.timePicker.model.timeFormat)},_setEmptyTime:function(n){var t=new Date(n);return t.setMilliseconds(0),t.setSeconds(0),t.setMinutes(0),t.setHours(0),t},_setEmptyDate:function(n){var t=new Date(n);return t.setDate(1),t.setMonth(0),t.setFullYear(2e3),t},_objectToString:function(n){return this._getFormat(n,this.model.dateTimeFormat)},_stringToObject:function(n){return this._getDateObj(n,this.model.dateTimeFormat)},_getFormat:function(n,i){if(n instanceof Date){var r=this._checkFormat(i);return t.format(n,r,this.model.locale)}return n},_checkFormat:function(n){var i=this,r=this._regExp();return n.replace(r,function(n){return n==="/"?t.preferredCulture(i.model.locale).calendars.standard["/"]!=="/"?"'/'":n:n})},_regExp:function(){return/\/dddd|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yy|HH|H|hh|h|mm|m|fff|ff|f|tt|ss|s|zzz|zz|z|gg|g|"[^"]*"|'[^']*'|[/]/g},_getDateObj:function(n,i){var o,f;if(typeof n=="string"){if(o=this._checkFormat(i),f=t.parseDate(n,o,this.model.locale),f!=null)return f;if(n!=""&&n!=null){var e,r=/^\s*(\d{4})-(\d\d)-(\d\d)T(\d\d):(\d\d):(\d\d).*Z\s*$/.exec(n),u=new Date;if(r&&(u=new Date,e=+r[2],u.setUTCFullYear(r[1],e-1,r[3]),u.setUTCHours(r[4],r[5],r[6]),e!=u.getUTCMonth()+1&&u.setTime()),this._isValidDate(u))return u}else return null}else return n},_compare:function(n,t){return n&&t&&n.getTime()==t.getTime()},_isValidDate:function(n){return n&&typeof n.getTime=="function"&&isFinite(n.getTime())},_change:function(n,t){this.popup&&(this.datePicker.option(n,t),this.timePicker.option(n,t))},_changeSkin:function(n){this.wrapper.removeClass(this.model.cssClass).addClass(n);this.popup.removeClass(this.model.cssClass).addClass(n);this._change("cssClass",n)},_localize:function(i){var u,r,f;if(this.model.locale=i,t.isNullOrUndefined(this._options.timeDisplayFormat)&&(this.model.timeDisplayFormat=""),t.isNullOrUndefined(this._options.dateTimeFormat)&&(this.model.dateTimeFormat=""),u=["AM","PM"],this._getDateTimeFormat(),this.timePicker.option("timeFormat",this.model.timeDisplayFormat),this._localizedLabels=this._getLocalizedLabels(),t.isNullOrUndefined(this._options)||(t.isNullOrUndefined(this._options.buttonText)||n.extend(this._localizedLabels.buttonText,this._options.buttonText),t.isNullOrUndefined(this._options.watermarkText)||(this._localizedLabels.watermarkText=this._options.watermarkText)),this._localizedLabelToModel(),this._buttonText(this._localizedLabels.buttonText),(this.isValidState||this.model.value instanceof Date&&this._isValidDate(this.model.value))&&this.element.val(this._objectToString(this.model.value)),this._preVal=this.element.val(),this._change("locale",i),this.model.startDay=this.datePicker.model.startDay,this._validateMeridian(),this._sfTimeHour.empty(),this._renderHourTable(),this.model.timeDrillDown.showMeridian)for(r=0;r<2;r++)f=t.isNullOrUndefined(t.preferredCulture(this.model.locale).calendars.standard[u[r]])?"":t.preferredCulture(this.model.locale).calendars.standard[u[r]][0],n("span.e-hours-meridiantxt-"+u[r].toLowerCase(),this._sfTimeHour).text(f)},_setWaterMark:function(){if(this.element!=null&&this.element.hasClass("e-input"))return this._localizedLabels.watermarkText&&this.element.val()==""&&(this.isValidState=!0,this._checkErrorClass()),this._isSupport||this.element.val()!=""?n(this.element).attr("placeholder",this._localizedLabels.watermarkText):this._hiddenInput.css("display","block").val(this._localizedLabels.watermarkText),!0},_localizedLabelToModel:function(){this.model.watermarkText=this._localizedLabels.watermarkText;this.model.buttonText=this._localizedLabels.buttonText},_readOnly:function(n){this.model.readOnly=n;n?this.element.attr("readonly","readonly"):this.element.prop("readonly",!1);this._change("readOnly",n)},_setRoundedCorner:function(n){n?(this.container.addClass("e-corner"),this.popup&&this.popup.addClass("e-corner")):(this.container.removeClass("e-corner"),this.popup&&this.popup.removeClass("e-corner"));t.isNullOrUndefined(this.datePicker)||this.datePicker.option("showRoundedCorner",n);t.isNullOrUndefined(this.timePicker)||this.timePicker.option("showRoundedCorner",n)},_setRtl:function(n){n?(this.wrapper.addClass("e-rtl"),this.popup&&this.popup.addClass("e-rtl")):(this.wrapper.removeClass("e-rtl"),this.popup&&this.popup.removeClass("e-rtl"));this._change("enableRTL",n)},_enabled:function(n){n?(this.model.enabled=!1,this.enable()):(this.model.enabled=!0,this.disable())},_showButton:function(n){this.model.showPopupButton=n;n?(this.container.addClass("e-padding"),this._renderIcon()):(this.container.removeClass("e-padding"),this.datetimeIcon.remove(),this.datetimeIcon=null)},_buttonText:function(t){n.extend(this.model.buttonText,t);this.popup.find(".e-dt-today").html(this.model.buttonText.today);this.popup.find(".e-dt-now").html(this.model.buttonText.timeNow);this.popup.find(".e-dt-done").html(this.model.buttonText.done);this.popup.find(".e-timecontainer").find(".e-header").html(this.model.buttonText.timeTitle)},_checkForResponsive:function(){if(n(window).outerWidth()>200&&n(window).outerWidth()<=500)this.popup.hasClass("e-dt-responsive")||(this.popup.addClass("e-dt-responsive"),this.timePicker.option("popupWidth",this.datePicker.popup.outerWidth()),this.timePicker.option("popupHeight",98),this.timePicker._refreshScroller(),this.timePicker._changeActiveEle());else if(this.popup.hasClass("e-dt-responsive")){this.popup.removeClass("e-dt-responsive");this.timePicker.option("popupWidth",this.model.timePopupWidth);var t=this.datePicker.popup.height()-this.popup.find(".e-header").height();this.timePicker.option("popupHeight",t);this.timePicker._refreshScroller();this.timePicker._changeActiveEle()}},enable:function(){this.model.enabled||(this.element[0].disabled=!1,this.model.enabled=!0,this.element.prop("disabled",!1),this.wrapper.removeClass("e-disable"),this.element.removeClass("e-disable").attr("aria-disabled",!1),this._isSupport||this._hiddenInput.prop("disabled",!1),this.datetimeIcon&&this.datetimeIcon.removeClass("e-disable").attr("aria-disabled",!1),this._isIE8&&this.datetimeIcon&&this.datetimeIcon.children().removeClass("e-disable"),this.popup&&(this.popup.children("div").removeClass("e-disable").attr("aria-disabled",!1),this._change("enabled",!0)))},disable:function(){this.model.enabled&&(this.element[0].disabled=!0,this.model.enabled=!1,this.wrapper.addClass("e-disable"),this.element.addClass("e-disable").attr("aria-disabled",!0),this.element.attr("disabled","disabled"),this._isSupport||this._hiddenInput.attr("disabled","disabled"),this.datetimeIcon&&this.datetimeIcon.addClass("e-disable").attr("aria-disabled",!0),this._isIE8&&this.datetimeIcon&&this.datetimeIcon.children().addClass("e-disable"),this._hideResult(),this._change("enabled",!1),this.popup&&(this.popup.children("div").addClass("e-disable").attr("aria-disabled",!0),this.datePicker.popup.removeClass("e-disable").attr("aria-disabled",!1),this.timePicker.popup.removeClass("e-disable").attr("aria-disabled",!1)))},getValue:function(){return this._objectToString(this.model.value)},setCurrentDateTime:function(){this.model.readOnly||this._setValue(new Date)},show:function(){this._showResult()},hide:function(){this._hideResult()},_wireEvents:function(){if(this.model.allowEdit&&(this._on(this.element,"focus",this._targetFocus),this._on(this.element,"blur",this._targetBlur),this._on(this.element,"keydown",this._keyDownOnInput)),!this.model.allowEdit){this.element.attr("readonly","readonly");this.element.on("mousedown",n.proxy(this._showhidePopup,this))}},_getLocalizedLabels:function(){return t.getLocalizedConstants(this.sfType,this.model.locale)}});t.DateTimePicker.Locale=t.DateTimePicker.Locale||{};t.DateTimePicker.Locale["default"]=t.DateTimePicker.Locale["en-US"]={watermarkText:"Select datetime",buttonText:{today:"Today",timeNow:"Time Now",done:"Done",timeTitle:"Time"}};t.PopupPosition={Bottom:"bottom",Top:"top"}})(jQuery,Syncfusion)});
