/*!
*  filename: ej.localetexts.de-DE.min.js
*  version : *********
*  Copyright Syncfusion Inc. 2001 - 2020. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
ej.ReportDesigner.Locale["de-DE"]={itemPanel:{waterMarkText:"Widgets suchen",noDataFound:"Keine Treffer gefunden...",customCategory:"Barcodes",customRptItemName:"1D Barcode",dataRequirements:"Datenanforderungen",customTooltip:{tooltip:{requirements:"Fügen Sie dem Designerbereich ein Berichtselement hinzu.",description:"Zeigt das benutzerdefinierte Berichtselement an.",title:"Benutzerdefiniertes Berichtselement"}},groupItems:{basicItems:{groupName:"Grundlegende Elemente",Items:{line:{displayText:"<PERSON><PERSON>",tooltip:{requirements:"Trennen einer Region durch eine Linie in Berichtsabschnitten.",description:"Grafisches Element zum Trennen der Berichtsregion.",title:"<PERSON><PERSON>"}},image:{displayText:"Bild",tooltip:{requirements:"Um ein Bild aus der Datenbank anzuzeigen, betten Sie das Bild ein.",description:"Zeigt die Bilder an.",title:"Bild"}},textBox:{displayText:"Textfeld",tooltip:{requirements:"Fügen Sie einen beliebigen Text hinzu.",description:"Zeigt den statischen und dynamischen Text an.",title:"Textfeld"}},rectangle:{displayText:"Rechteck",tooltip:{requirements:"Kombinieren Sie ein oder mehrere Berichtselemente darin.",description:"Grafisches Containerelement.",title:"Rechteck"}}}},comparison:{groupName:"Vergleich",Items:{column:{displayText:"Säule",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Vergleicht Werte für eine Gruppe ungeordneter Elemente in verschiedenen Kategorien mithilfe der horizontal angeordneten vertikalen Balken.",title:"Säule"}},bar:{displayText:"Bar",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Vergleicht Werte für eine Gruppe ungeordneter Elemente über verschiedene Kategorien hinweg mit den vertikal angeordneten horizontalen Balken.",title:"Bar"}},stackedColumn:{displayText:"Gestapelte Säule",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Vergleicht mehrere Messungen mit vertikal gestapelten Balken.",title:"Gestapelte Säule"}},stackedBar:{displayText:"Gestapelte Bar",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Vergleicht mehrere Takte mit horizontal gestapelten Leisten.",title:"Gestapelte Bar"}},stackedColumnPercent:{displayText:"Gestapelte Säule100%",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Vergleicht mehrere Messungen als Teile eines Ganzen mit vertikal gestapelten Balken.",title:"Gestapelte Säule100%"}},stackedBarPercent:{displayText:"Gestapelte Bar100%",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Vergleicht mehrere Takte als Teile eines Ganzen unter Verwendung der horizontal gestapelten Balken.",title:"Gestapelte Bar100%"}}}},proportion:{groupName:"Anteil",Items:{pie:{displayText:"Kuchen",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Zeigt die Anteile jedes Beitrags an die Gesamtmenge in Form von Kuchenscheiben an.",title:"Kuchen"}},explodedPie:{displayText:"Explodierter Kuchen",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Betont ein einzelnes Stück eines Kreisdiagramms.",title:"Explodierter Kuchen"}},doughnut:{displayText:"Krapfen",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Zeigt die Proportionen von jedem Beitrag zur Gesamtmenge in Form von Donut-Scheiben.",title:"Krapfen"}},pyramid:{displayText:"Pyramide",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Zeigt den proportionalen Vergleich zwischen Werten progressiv ansteigend an.",title:"Pyramide"}},funnel:{displayText:"Trichter",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Zeigt den proportionalen Vergleich zwischen den Werten schrittweise abnehmend an.",title:"Trichter"}}}},distribution:{groupName:"Verteilung",Items:{area:{displayText:"Bereich",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Vergleicht Werte für eine Gruppe ungeordneter Elemente über verschiedene Kategorien hinweg durch die vertikal gefüllten gefüllten Kurven.",title:"Bereich"}},smoothArea:{displayText:"Glatte Oberfläche",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Vergleicht Werte für einen Satz ungeordneter Elemente über verschiedene Kategorien hinweg durch die gefüllten Kurven, die vertikal mit glatter Oberfläche angeordnet sind.",title:"Glatte Oberfläche"}},stackedArea:{displayText:"Gestapelte Fläche",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Vergleicht mehrere Messungen durch die vertikal gestapelten gefüllten Kurven.",title:"Gestapelte Fläche"}},stackedAreaPercent:{displayText:"Gestapelte Fläche100%",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Vergleicht mehrere Messungen als Teile eines Ganzen durch die vertikal gestapelten gefüllten Kurven.",title:"Gestapelte Fläche100%"}},line:{displayText:"Linie",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Zeigt die Trends für die Analyse über einen Zeitraum mit Datenpunkten, die über die geraden Linien verbunden sind.",title:"Linie"}},smoothLine:{displayText:"Glatte Linie",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Vergleicht die Verteilung von Werten über einen Zeitraum, der mit den glatten Linien verbunden ist.",title:"Glatte Linie"}},steppedLine:{displayText:"Stufenlinie",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Vergleicht die Verteilung von Werten über einen Zeitraum, der mit den abgestuften Linien verbunden ist.",title:"Stufenlinie"}},lineWithMarkers:{displayText:"Linie mit Markern",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Vergleichen Sie Änderungen für mehrere Gruppen über denselben Zeitraum.",title:"Linie mit Markern"}},smoothLineWithMarkers:{displayText:"Glatte Linie mit Markern",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Geplottete Werte werden mit einem Markierungspunkt dargestellt und diese Punkte werden mit einer glatten Linie verbunden.",title:"Glatte Linie mit Markern"}},scatter:{displayText:"Streuen",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Zeigt eine Reihe als eine Menge von Punkten an und Werte werden durch die Position von Punkten auf dem Diagramm dargestellt.",title:"Streuen"}},bubble:{displayText:"Blase",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Zeigt den Unterschied zwischen zwei Werten eines Datenpunkts basierend auf der Größe der Blase an.",title:"Blase"}},polar:{displayText:"Polar",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Zeigt eine Reihe als eine Gruppe von Punkten an, die in einem 360-Grad - Kreis nach Kategorie gruppiert sind.",title:"Polar"}},radar:{displayText:"Radar",tooltip:{requirements:"1 oder mehr Werte und 1 oder mehr Spalten.",description:"Zeigt eine Reihe als kreisförmige Linie oder Fläche an.",title:"Radar"}}}},dataRegions:{groupName:"Datenregionene",Items:{tablix:{displayText:"Tabelle",tooltip:{requirements:"1 oder mehr Zeilen / Spalten.",description:"Zeigt paginierte Berichtsdaten in Zellen an.",title:"Tabelle"}},list:{displayText:"Liste",tooltip:{requirements:"1 oder mehr Zeilen / Spalten.",description:"Eine Liste zeigt Daten in einem freien Format an. Platzieren Sie Felder an beliebiger Stelle in der Liste.",title:"Liste"}}}},subReports:{groupName:"Unterberichte",Items:{subreport:{displayText:"Unterbericht",tooltip:{requirements:"Anzeigen / Einbetten des Berichts im Hauptbericht.",description:"Zeigt einen anderen Bericht im Hauptbericht an.",title:"Unterbericht"}}}}}},toolbar:{newReport:"Neu",open:"Öffnen",openMenu:{fromDevice:"Von Gerät",fromServer:"Vom Server"},save:"sparen",saveMenu:{saveLabel:"sparen",saveAs:"Speichern als",saveAsMenu:{saveToDevice:"Zum Gerät",saveToServer:"Zu Server"}},cut:"Schnitt",copy:"Kopieren",paste:"Einfügen",deleteItem:"Löschen",undo:"Rückgängig machen",redo:"Wiederholen",zoomIn:"hineinzoomen",zoomOut:"Rauszoomen",header:"Header",footer:"Fußzeile",order:"Auftrag",orderMenu:{sendBackward:"Rückwärts senden",bringForward:"Vorwärts bringen",sendToBack:"Nach hinten senden",bringToFront:"Nach vorne bringen"},left:"Links ausrichten",center:"Links ausrichten",right:"Richtig ausrichten",top:"Oben ausrichten",middle:"Mitte",bottom:"Unten ausrichten",distributeHorizontally:"Horizontal verteilen",distributeVertically:"Vertikal verteilen",centerHorizontally:"Horizontal zentrieren",centerVertically:"Vertikal zentrieren",sizing:"Dimensionierung",sizingMenu:{sameSize:"Gleiche Größe",sameWidth:"Gleiche Breite",sameHeight:"Selbe Größe"},sizeToGrid:"Größe zum Gitter",alignToGrid:"Am Raster ausrichten",gridLine:"Gitterlinien",snapToShape:"An Form ausrichten",fullScreen:"Vollbild",preview:"Vorschau",reportUpload:{alertLabel:"Hochladen",alertMessage:"Fehler beim Hochladen der Datei. Bitte erneut hochladen "},grouping:"Gruppierung",view:"Aussicht"},newReport:{title:"Neuer Bericht",fileName:"Dateiname",waterMark:"Berichtsname",create:"Erstellen",cancel:"Stornieren",close:"Schließen"},reportAction:{enableLink:"Link aktivieren",linkTo:"Link zu",report:"Bericht",url:"URL"},linkReport:{reportCaption:"Bericht",setParameter:"Setze Parameter"},imageProperty:{basicSettings:{categoryName:"Grundeinstellungen",source:"Quelle",sourceTypes:{external:"Extern",embedded:"Eingebettet",database:"Datenbank"},value:"Wert",mimeType:"Mime Typ",mimeTypes:{bmp:"image/bmp",jpeg:"image/jpeg",gif:"image/gif",png:"image/png",xPng:"image/x-png"}},categoryName:"Verknüpfung",linkReport:"Link-Bericht",appearance:{categoryName:"Aussehen",styleTooltip:"Stil",colorTooltip:"Farbe",sizeTooltip:"Größe",borderTypes:{border:"Rand",borderLeft:"Links",borderTop:"Oben",borderRight:"Recht",borderBottom:"Unterseite"},borderStyles:{solid:"Solide",none:"Keiner",double:"Doppelt",dashed:"Gestrichelt",dotted:"Gepunktet"}},size:{categoryName:"Größe",paddingTypes:{padding:"Polsterung",paddingLeft:"Links",paddingTop:"Oben",paddingRight:"Recht",paddingBottom:"Unterseite"},sizing:"Größenanpassung",sizeTypes:{auto:"Automatische skalierung",fit:"Passen",proportional:"Proportional anpassen",clip:"Clip"}},position:{categoryPosition:"Position",positionLabel:"Position",left:"Links",top:"Oben",sizeLabel:"Größe",width:"Breite",height:"Höhe"},visibility:{categoryName:"Sichtweite",visible:"Sichtbar",toggleItem:"Element umschalten"}},chartProperty:{commonProperties:{showBorder:"Grenze anzeigen",border:{border:"Rand",borderLeft:"Links",borderTop:"Oben",borderRight:"Recht",borderBottom:"Unterseite"},background:"Hintergrundfarbe",font:"Schriftart",fontStyle:"Schriftstil",labelRotation:"Etikettenrotation",categoryAxis:"Kategorie Achse",valueAxis:"Wertachse",defaultText:"Standard",auto:"Auto",borderStyles:{solid:"Solide",none:"Keiner",double:"Doppelt",dashed:"Gestrichelt",dotted:"Gepunktet",dashDot:"Strich Punkt",dashDotDot:"Strich Punkt Punkt"},horizontalAlignments:{near:"In der Nähe von",far:"Weit"},textAlignments:{right:"Recht",bottom:"Unterseite",center:"Center",topLeft:"Oben links",topCenter:"Oben in der Mitte",topRight:"Oben rechts",rightTop:"Rechts oben",rightCenter:"Rechtes Zentrum",rightBottom:"Rechts unten",bottomLeft:"Unten links",bottomCenter:"Unten in der Mitte",bottomRight:"Unten rechts",leftTop:"Links oben",leftCenter:"Linkes Zentrum",leftBottom:"Links unten"},fontStyleTypes:{normal:"Normal",italic:"Kursiv"},fontWeightTypes:{light:"Licht",bold:"Fett gedruckt"}},basicSettings:{categoryName:"Grundeinstellungen",showLegend:{showLegendText:"Legende zeigen",title:"Titel",titleFont:"Überschrift",titleFontStyle:"Titel Schriftart",titleAlignment:"Titelausrichtung",legendPosition:"Legendenposition",enableCustomBounds:"Benutzerdefinierte Begrenzungen aktivieren"},chooseSeries:"Wählen Sie die Serie",showMarker:{showMarkerText:"Markierung zeigen",color:"Farbe",markerType:"Markierungstyp",markerTypes:{square:"Platz",circle:"Kreis",diamond:"Diamant",triangle:"Dreieck",cross:"Kreuz",star5:"Stern5"},size:"Größe"},showDataLabel:{showDataLabelText:"Zeige Datenlabel",dataLabelFormat:"Format",dataLabelText:"Etikette",dataLabelValueAsText:"Wert als Label verwenden",dataLabelTypes:{valueX:"#VALX",valueY:"#VALY",valueY2:"#VALY2",valueY3:"#VALY3",valueY4:"#VALY4",valueY5:"#VALY5",valueY6:"#VALY6",index:"#INDEX",percent:"#PROZENT",total:"#GESAMT",axisLabel:"#AXISLABEL"}},enableSmartLabel:{smartLabelText:"SmartLabel aktivieren",labelStyle:"Etikettenstil",value:"Wert",smartLabelPositions:{outside:"Draußen",inside:"Innerhalb",outsideInColumn:"Außerhalb der Spalte"},smartLabelStyles:{pieLabelStyle:"Kuchen Etikettenstil",funnelLabelStyle:"Trichter-Label-Stil",pyramidLabelStyle:"Pyramide-Aufkleber-Art",barLabelStyle:"Bar Etikettenstil",smartLabelStyle:"Beschriftungsstil"}},seriesBorder:"Seriengrenze",seriesColor:"Serie Farbe"},categoryName:"Aussehen",customAttribute:{categoryName:"BenutzerdefinierteAttribute",userDefined:"Benutzerdefinierten",alertHeader:"Diagramm Berichtselement",alertMessage:"Ungültiges Format für benutzerdefinierte Attribute Korrektes Beispiel: 'AttrName1 = Wert1, AttrName2 = Wert2 '."},chartArea:{categoryName:"Diagrammbereich",colorPalette:"Farbpalette",colorPaletteTypes:{earthTones:"Erdfarben",excel:"Excel",grayScale:"Graustufen",pastel:"Pastell",semiTransparent:"Halbtransparent",berry:"Beere",chocolate:"Schokolade",fire:"Feuer",seaGreen:"Meeresgrün",brightPastel:"Helles Pastell",pacific:"Pazifik",pacificLight:"Pazifisches Licht",pacificSemiTransparent:"PacificSemiTransparent"}},title:{categoryName:"Titel",showChartTitle:"Diagrammtitel anzeigen",titleText:"Titeltext",titlePosition:"Titelposition"},axis:{enableAxis:"Achse aktivieren",axisTitle:"Achsentitel",alignment:"Ausrichtung",lineStyle:"Linienstil",labelOverflowMode:"Etikettenüberlaufmodus",overFlowModeTypes:{trim:"Trimmen",hide:"verbergen"},labelFont:"Etikettenschrift",labelFormat:"Etikettenformat",enableMajorTicks:"Aktivieren Sie wichtige zecken",enableMinorTicks:"Aktivieren Sie Minor zecken",tickProperties:{tickSize:"zecken ​​Größe",tickColor:"zecken ​​Farbe",tickWidth:"Breite",length:"Länge"},tickPosition:"Tick-Position"},gridLine:{categoryName:"Rasterlinie",gridLineStyle:{minorGridLine:"Minor Rasterlinie anzeigen",majorGridLineStyle:"Haupt-Rasterlinie-Stil",minorGridLineStyle:"Geringfügiger Rasterlinie-Stil"}},pageBreak:{categoryName:"Seitenumbruch",enablePageBreak:"Seitenumbruch aktivieren",breakLocation:"Ort brechen",breakLocationTypes:{none:"Keiner",start:"Anfang",end:"Anfang",startAndEnd:"Start und Ende",between:"Zwischen"},pageNumberReset:"Seitenzahl zurücksetzen",pageName:"Seitenname"},position:{categoryPosition:"Position",positionLabel:"Position",left:"Links",top:"Oben",sizeLabel:"Größe",width:"Breite",height:"Höhe"},visibility:{categoryName:"Sichtweite",visible:"Sichtbar",toggleItem:"Element umschalten"},fontStyleTooltip:"Stil",fontWeightTooltip:"Gewicht",fontSizeTooltip:"Größe",fontColorTooltip:"Farbe",fontFamilyTooltip:"Schriftfamilie",styleTooltip:"Stil",colorTooltip:"Farbe",sizeTooltip:"Größe"},lineProperty:{basicSettings:{categoryBasicSettings:"Grundeinstellungen",line:"Linie",lineTypes:{solid:"Solide",dashed:"Gestrichelt",dotted:"Gepunktet"}},position:{categoryPosition:"Position",positionLabel:"Position",left:"Links",top:"Oben",sizeLabel:"Größe",width:"Breite",height:"Höhe"},visibility:{categoryName:"Sichtweite",visible:"Sichtbar",toggleItem:"Element umschalten"},styleTooltip:"Stil",colorTooltip:"Farbe",sizeTooltip:"Größe"},subReportProperty:{basicSettings:{categoryBasicSettings:"Grundeinstellungen"},appearance:{categoryAppearance:"Aussehen",borderTypes:{border:"Rand",borderLeft:"Links",borderTop:"Oben",borderRight:"Recht",borderBottom:"Unterseite"},borderStyles:{solid:"Solide",none:"Keiner",double:"Doppelt",dashed:"Gestrichelt",dotted:"Gepunktet"}},noRows:{noRowsLabel:"Keine Zeilen",font:"Schriftart",fontStyle:{fontStyleLabel:"Schriftstil",fontItem:{defaultStyle:"Standard",fontNormal:"Normal",italic:"Kursiv"},fontWeight:{defaultElement:"Standard",normal:"Normal",thin:"Dünn",extraLight:"Extra-Licht",light:"Licht",medium:"Mittel",semiBold:"Semi Fett",bold:"Fett gedruckt",extraBold:"Extra fett",heavy:"Schwer"}},textDecoration:{textDecorationLabel:"Textdekoration",defaultDecoration:"Standard",none:"Keiner",underLine:"Unterstreichen",overLine:"Überstreichen",lineThrough:"Linie durch"},format:"Format",lineHeight:"Zeilenhöhe",message:"Botschaft",paddingTypes:{padding:"Polsterung",paddingLeft:"Links",paddingRight:"Recht",paddingTop:"Oben",paddingBottom:"Unterseite"},textAlign:{textAlignLabel:"Textausrichtung",textAlignDefault:"Standard",textAlignGeneral:"Allgemeines",textAlignLeft:"Links",textAlignRight:"Recht",textAlignCenter:"Center"},verticalAlign:{verticalAlignlabel:"Vertical Align",verticalAlignDefault:"Standard",verticalAlignTop:"Oben",verticalAlignMiddle:"Mitte",verticalAlignBottom:"Unterseite"},writingMode:{writingModeLabel:"Schreibmodus",writingModeDefault:"Standard",writingModeHorizontal:"Horizontal",writingModeVertical:"Vertikal",writingModeRotate:"Drehen270"}},visibility:{categoryName:"Sichtweite",visible:"Sichtbar",toggleItem:"Element umschalten"},position:{categoryPosition:"Position",positionLabel:"Position",left:"Links",top:"Oben",sizeLabel:"Größe",width:"Breite",height:"Höhe"},miscellaneous:{categoryMiscellaneous:"Sonstiges",keepTogether:"Zusammen halten"},fontStyleTooltip:"Stil",fontWeightTooltip:"Gewicht",fontSizeTooltip:"Größe",fontColorTooltip:"Farbe",fontFamilyTooltip:"Schriftfamilie",styleTooltip:"Stil",colorTooltip:"Farbe",sizeTooltip:"Größe"},rectangleProperty:{basicSettings:{categoryBasicSettings:"Grundeinstellungen",borderTypes:{border:"Rand",borderLeft:"Links",borderTop:"Oben",borderRight:"Recht",borderBottom:"Unterseite"},borderStyles:{solid:"Solide",none:"Keiner",double:"Doppelt",dashed:"Gestrichelt",dotted:"Gepunktet"},backGround:"Hintergrundfarbe",styleTooltip:"Stil",colorTooltip:"Farbe",sizeTooltip:"Größe"},pageBreak:{pageBreak:"Seitenumbruch",enablePageBreak:{enablePageBreak:"Seitenumbruch aktivieren",breakLocation:{breakLocationLabel:"Ort brechen",none:"Keiner",start:"Anfang",end:"Anfang",startAndEnd:"Start und Ende",between:"Zwischen"},pageNumberReset:"Seitenzahl zurücksetzen"}},position:{categoryPosition:"Position",positionLabel:"Position",left:"Links",top:"Oben",sizeLabel:"Größe",width:"Breite",height:"Höhe"},visibility:{categoryName:"Sichtweite",visible:"Sichtbar",toggleItem:"Element umschalten"},rectangleMiscellaneous:{categoryMiscellaneous:"Sonstiges",keepTogether:"Zusammen halten",pageName:"Seitenname"}},browseFile:{openFile:{selectReport:"Bericht auswählen",open:"Öffnen"},saveFile:{saveAsReport:"Speichern als Bericht",name:"Name",save:"Sparen"},close:"Schließen",cancel:"Stornieren",waterMark:"Berichtsname",emptyMessage:"Diese Kategorie ist leer",alertMessage:{reportServer:"Berichtsserver",selectCategory:"Bitte Kategorie auswählen"},warningMessage:{fileNameLabel:"Ein Gegenstand '",fileNameExist:".rdl ' existiert bereits. Möchten Sie den bestehenden Artikel ersetzen??",populateCategory:"Der Berichts-Designer konnte die Ressourcen nicht vom Berichtsserver abrufen"}},expressionMenu:{reset:"Zurücksetzen",expression:"Ausdruck",advanced:"Fortgeschritten"},propertyPanel:{property:"EIGENSCHAFTEN",data:"DATEN",name:"Name",toolTipStyle:"Stil",toolTipColor:"Farbe",toolTipWidth:"Breite",setSorts:"Sortierung",setFilters:"Filter setzen",advancedOptions:"Erweiterte Optionen",codemodules:"Code",expressionList:{top:"Oben",right:"Recht",bottom:"Unterseite",left:"Links",style:"Stil",color:"Farbe",size:"Größe",fontFamily:"Schriftfamilie",width:"Breite",height:"Höhe",weight:"Gewicht",image:"Bild"},alertMessage:{nameWarning:"Name darf nicht leer sein",nameAlert:"Name existiert bereits",nameValidation:"Name sollte keine Leerzeichen und Sonderzeichen enthalten"},unitType:{inchText:"im",centimeterText:"cm",pixelText:"pixel",pointText:"pt",millimeterText:"mm",picaText:"pc"},setGroups:"Gruppen festlegen",addDatasource:"Datenquelle hinzufügen",dataAlertMsg:"Keine Datenquelle hinzugefügt !"},dataSource:{newDatasource:"NEUE DATASOURCE",datasource:"DATENQUELLEN",datasourceList:{data:"DATEN",contextMenu:{editItem:"Bearbeiten",deleteItem:"Löschen",createDataSet:"Datensatz erstellen",cloneDatasource:"Klon"}},datasourceType:{existOption:"Bestehende",newOption:"Erstelle neu",selectDatasoure:"Wählen Sie die Datenquelle aus",connectDatasource:"Datenquelle verbinden",datasourceType:"Wählen Sie den Typ zum Verbinden",sqlLabel:"SQL",sqlCeLabel:"SQLCE",odbcLabel:"ODBC",oracleLabel:"ORACLE",oledbLabel:"OLEDB",xmlLabel:"XML",sharedLabel:"Geteilt"},datasourceConnection:{newConnection:"NEUE VERBINDUNG",editConnection:"VERBINDUNG BEARBEITEN",name:"Name",save:"Sparen",connect:"Verbinden",cancel:"Stornieren"},sqlDatasource:{authenticationType:"Authentifizierungsart",window:"Fenster",sqlServer:"SQL Server",userName:"Nutzername",password:"Passwort",switchLabel:"Datenquelle Voraus-Panel",switchAlert:"Wenn Sie zum visuellen Designer wechseln, werden die manuellen Änderungen an der Verbindungszeichenfolge verworfen. Möchten Sie den visuellen Designer trotzdem verwenden?",basicOption:{serverName:"Server Name",savePassword:"Passwort speichern",database:"Datenbank",advanceSwitch:"Erweiterte Option"},advanceOption:{connectionString:"Verbindungsstring",promptLabel:"Eingabetext",prompt:"Prompt",none:"Keiner",savePassword:"Passwort speichern",basicSwitch:"Grundlegende Option"},alertMessage:{alertConnectionString:"Geben Sie die Verbindungszeichenfolge an",alertPrompt:"Geben Sie den Eingabeaufforderungstext ein",alertUserName:"Geben Sie den Benutzernamen an",alertPassword:"Geben Sie das Passwort an",alertServerName:"Geben Sie den Servernamen an",alertDatabaseName:"Geben Sie den Datenbanknamen an"}},sqlceDatasource:{connectionString:"Verbindungsstring",authenticationType:"Authentifizierungsart",authentication:"Authentifizierung",none:"Keiner",password:"Passwort",savePassword:"Passwort speichern",alertMessage:{alertConnectionString:"Geben Sie die Verbindungszeichenfolge an",alertPassword:"Geben Sie das Passwort an"}},odbcDatasource:{connectionString:"Verbindungsstring",authenticationType:"Authentifizierungsart",authentication:"Authentifizierung",prompt:"Prompt",none:"Keiner",userName:"Nutzername",password:"Passwort",promptLabel:"Eingabetext",savePassword:"Passwort speichern",alertMessage:{alertConnectionString:"Geben Sie die Verbindungszeichenfolge an",alertPrompt:"Geben Sie den Eingabeaufforderungstext ein",alertUserName:"Geben Sie den Benutzernamen an",alertPassword:"Geben Sie das Passwort an"}},oracleDatasource:{connectionString:"Verbindungsstring",authenticationType:"Authentifizierungsart",authentication:"Authentifizierung",prompt:"Prompt",none:"Keiner",userName:"Nutzername",password:"Passwort",promptLabel:"Eingabetext",savePassword:"Passwort speichern",alertMessage:{alertConnectionString:"Geben Sie die Verbindungszeichenfolge an",alertPrompt:"Geben Sie den Eingabeaufforderungstext ein",alertUserName:"Geben Sie den Benutzernamen an",alertPassword:"Geben Sie das Passwort an"}},oledbDatasource:{connectionString:"Verbindungsstring",authenticationType:"Authentifizierungsart",authentication:"Authentifizierung",prompt:"Prompt",none:"Keiner",userName:"Nutzername",password:"Passwort",promptLabel:"Eingabetext",savePassword:"Passwort speichern",alertMessage:{alertConnectionString:"Geben Sie die Verbindungszeichenfolge an",alertPrompt:"Geben Sie den Eingabeaufforderungstext ein",alertUserName:"Geben Sie den Benutzernamen an",alertPassword:"Geben Sie das Passwort an"}},xmlDatasource:{connectionString:"Verbindungsstring"},sharedDatasource:{datasource:"Gemeinsame Datenquelle",alertMessage:"Wählen Sie eine gemeinsame Datenquelle"},alertMessage:{alertLabel:"Datenquelle",alertConnectionFailed:"Der Berichts-Designer konnte keine Verbindung zur Datenquelle herstellen",dataExtensionFailed:"Der ausgewählte Datenanbieter ist nicht verfügbar. Bitte überprüfen Sie die Datenerweiterung.",connectStringValidation:"Da die Verbindungszeichenfolge Ausdrücke in der Datenquelle enthält ",validationMessage:" Bitte aktualisieren Sie mit einer gültigen Verbindungszeichenfolge.",executionMessage:", Wir können den Datensatz für diese Verbindung nicht ausführen.",confirmMessage:" Möchten Sie die Datenquelle wirklich speichern?",deleteValue:"Löschen Sie die Datenquelle '",nameWarning:"Geben Sie den DataSource-Namen an",nameAlert:"Der angegebene Name existiert bereits in der Datenquelle-Liste",nameValidation:"Name sollte keine Leerzeichen und Sonderzeichen enthalten"}},imageManager:{headerText:"Image Manager",addImageButton:"Bild hinzufügen",deleteImage:"Gelöschtes Bild löschen",image:"Bild"},linkParameter:{title:"Parameter",headerTxt:"Verbindungsparameter",descriptionText:"Berichtsparameter",addText:"HINZUFÜGEN",ok:"OK",cancel:"Stornieren",nameWaterMark:"Parameter Name",valueWaterMark:"Wert",errorMessage:"Geben Sie einen Wert für diese Eigenschaft ein",closeToolTip:"Schließen"},filter:{title:"Filter",descriptionLable:"Zeilen einschließen, bei denen die folgenden Bedingungen erfüllt sind.",add:"HINZUFÜGEN",ok:"OK",cancel:"Stornieren",valueWaterMark:"Wert",fieldWaterMark:"Wähle Feld",closeToolTip:"Schließen",errorMessage:{booleanValidation:"Wert ist kein boolescher Wert.",intValidation:"Wert ist keine ganze Zahl.",floatValidation:"Wert ist kein Float.",dateTimeValidation:"Wert ist ein ungültiges Datums- / Uhrzeitformat.",topBottomFilter:"Die Filteroperatoren Top% und Bottom% benötigen einen Float- oder Integer-Datentyp.",expressionValidation:"Wert für Ausdrucksfeld wählen"},operatorTypes:{like:"Mögen",topN:"TopN",bottomN:"BottomN",topPercent:"Oben%",bottomPercent:"Unterseite%",between:"Zwischen",inFilter:"Im"}},dataField:{title:"Datenfelder",descriptionLable:"Schließen Sie die Datenfeldzeilen ein",add:"HINZUFÜGEN",ok:"OK",cancel:"Stornieren",fieldNameWaterMark:"Feldname",closeToolTip:"Schließen",errorMessages:{emptyField:"Geben Sie den Feldnamen an",invalidCharacters:"Der Feldname darf keine Leerzeichen und Sonderzeichen enthalten",sameCharacter:"Der Feldname ist bereits vorhanden"},dsNameLabel:"Name",dsNameWaterMark:"Datenname",dsNameValidation:{nameWarning:"Geben Sie den Datensatznamen an",nameAlert:"Der angegebene Name ist bereits in der Datensatzliste vorhanden",nameValidation:"Der Datensatz Name darf keine Leerzeichen und Sonderzeichen enthalten"}},dataPanel:{itemTooltip:{properties:"EIGENSCHAFTEN",data:"DATEN",parameters:"Parameter",imageManager:"Bild Manager",expand:"Erweitern",collapse:"Zusammenbruch"},dataSourceNewAlert:{title:"Datenquelle",contentMessage:"Möchten Sie die Erstellung der Datenquelle abbrechen?"},dataSourceEditAlert:{title:"Datenquelle",contentMessage:"Möchten Sie die Bearbeitung der Datenquelle abbrechen?"},dataSetNewAlert:{title:"Datensatz",contentMessage:"Möchten Sie die Datensatzerstellung abbrechen?"},dataSetEditAlert:{title:"Datensatz",contentMessage:"Möchten Sie die Datensatzbearbeitung abbrechen?"},parameterNewAlert:{title:"Parameter",contentMessage:"Möchten Sie die Parametererstellung abbrechen?"},parameterEditAlert:{title:"Parameter",contentMessage:"Möchten Sie die Bearbeitung der Parameter abbrechen?"}},dataSet:{headerText:"DATEN",newData:"DATASET HINZUFÜGEN",shareDataset:{headerText:"NEUES DATASET",editHeaderText:"DATASET BEARBEITEN",save:"Sparen",cancel:"Stornieren",nameLable:"Name",sharedDatasetLabel:"Gemeinsames DataSet",errorMessage:{nameValidation:"Geben Sie den Datensatznamen an",datasetValidation:"Wählen Sie eine gemeinsame Datenquelle",duplicateName:"Der angegebene Name ist bereits in der Datensatzliste vorhanden",specialCharacter:"Name sollte keine Leerzeichen und Sonderzeichen enthalten"}},contextMenu:{edit:"Bearbeiten",remove:"Löschen",cloneDataset:"Klon",filter:"Filter",setField:"Felder"},datasourceSwitcher:"DATENQUELLEN",deleteDataset:"Datensatz löschen",deleteField:"Feld löschen",newDataText:"Neue Daten",sharedDataText:"Freigegebene Daten",dataRestriction:{dsCreateRestriction:"Die Erstellung von Datenquellen wurde eingeschränkt",title:"Daten"},dataFieldSearch:{errorMessage:"Keine Treffer gefunden",searchText:"Suche"}},reportViewer:{toolbar:{print:"Drucken",exportText:"Export",pageFit:"Passend für Seite",exportformat:{Pdf:"PDF",Excel:"Excel",Word:"Wort",Html:"Html",PPT:"PowerPoint",CSV:"CSV"},pageSetup:"Seiteneinrichtung",gotoFirst:"Gehe zu Erster",gotoLast:"Gehe zu Letzter",gotoNext:"Gehe zu Weiter",gotoPrevious:"Gehe zurück",gotoParanet:"Gehe zum Elternteil",zoomIn:"hineinzoomen",zoomOut:"Rauszoomen",fittopage:{pageWidth:"Seitenbreite",pageHeight:"Ganze Seite"},printLayout:"Drucklayout",refresh:"Aktualisierung",documentMap:"Dokumentkarte",parameter:"Parameter",viewDesign:"Vorschau schließen"},pagesetupDialog:{close:"Schließen",paperSize:"Papier größe",height:"Höhe",width:"Breite",margins:"Margen",top:"Oben",bottom:"Unterseite",right:"Recht",left:"Links",unit:"in",orientation:"Orientierung",portrait:"Porträt",landscape:"Landscape",doneButton:"OK",cancelButton:"Stornieren"},credential:{userName:"Nutzername",password:"Passwort"},waterMark:{selectOption:"Wähle eine Option",selectValue:"Wähle einen Wert"},errorMessage:{startMessage:"Beim Laden dieses Berichts sind in der Berichtsanzeige einige Probleme aufgetreten. Bitte",middleMessage:" Klick hier",endMessage:"um die Fehlerdetails zu sehen",closeMessage:"Diese Nachricht schließen"},alertMessage:{close:"Schließen",title:"BerichtViewer",done:"OK",showDetails:"zeige Einzelheiten",hideDetails:"ausblenden Einzelheiten",reportLoad:"Bericht geladen:",RVERR0001:"Bericht Viewer konnte den Bericht nicht laden",RVERR0002:"Bericht Viewer konnte den Bericht nicht rendern",RVERR0003:"Im Ajax-Postback ist ein Fehler aufgetreten",RVERR0004:"Bitte wählen Sie einen Wert für den Parameter",RVERR0005:"Der Parameter {parametername} fehlt ein Wert",RVERR0006:"Bitte geben Sie den Datentyp schweben ein ",RVERR0007:"Bitte geben Sie die Ganzzahl-Datentyp Eingabe",RVERR0008:"Bericht Viewer konnte die Datenquellenanmeldeinformationen nicht überprüfen",RVERR0009:"Die Ränder sind überlappt oder sie liegen außerhalb des Papiers. Geben Sie eine andere Randgröße ein.",RVERR0010:"Bitte geben Sie einen Wert für den Parameter ein",RVERR0011:"Der Parameter darf nicht leer sein",RVERR0012:"Der für den Berichtsparameter {parameterprompt} angegebene Wert ist für seinen Typ nicht gültig."},selectAll:"Wählen Sie Alle",viewButton:"Zeige Bericht"},sortData:{sorting:"Sortierung",headerText:"Filter sortieren",add:"HINZUFÜGEN",changeSortingOptions:"Ändern Sie die Sortieroptionen.",sortBy:"Sortiere nach",thenBy:"Dann vorbei",direction:{ascending:"Aufsteigend",descending:"Absteigend"},chooseField:"Wählen Sie Feld",errorMessage:"Wert für Ausdrucksfeld wählen",ok:"OK",cancel:"Stornieren",close:"Schließen"},groupData:{grouping:"Gruppierung",headerTxt:"Gruppe",headerTxtLabel:"Gruppenbezeichnung",name:"Name",label:"Etikette",changeGroupingOptions:"Ändern Sie die Gruppierungsoptionen.",add:"HINZUFÜGEN",groupBy:"Gruppiere nach",andOn:"Und weiter",chooseField:"Wähle Feld",ok:"OK",cancel:"Stornieren",close:"Schließen",errorMessage:{nameErrorMessage:"Bitte geben Sie den gültigen Namen ein",expressionErrorMessage:"Wählen Sie einen Wert für ein Ausdrucksfeld"}},alertMessage:{yes:"Ja",no:"Nein",showDetails:"Zeige Einzelheiten",hideDetails:"ausblenden Einzelheiten",close:"Schließen"},parameter:{listPanel:{headerText:"Parameter",newParameter:"NEU PARAMETER",editMenu:{edit:"Bearbeiten",remove:"Löschen"},alertTitle:"Parameter"},configurationPanel:{newHeaderText:"NEU PARAMETER",editHeaderText:"BEARBEITEN PARAMETER",nameLabel:"Name",promptLable:"Prompt",dataTypeLable:"Datentyp",blankValueLable:"Leerwert zulassen('')",nullValueLable:"NULL-Wert zulassen",multipleValueLable:"Erlaube mehrere Werte",visibilityLable:"Sichtweite",assignValueLable:"Wert zuweisen >>",save:"Sparen",cancel:"Stornieren",visibility:{visible:"Sichtbar",hidden:"Versteckt",internal:"Intern"},dataType:{stringType:"Schnur",booleanType:"Boolesch",dateTimeType:"Terminzeit",integerType:"Ganze Zahl",floatType:"Schweben"}},errorMessage:{nameField:"Bitte geben Sie den Namen ein",promptField:"Bitte geben Sie den Wert ein",nameAlreadyExists:"Der Parametername existiert bereits"},warningMessage:{specialCharacter:"Name sollte keine Leerzeichen und Sonderzeichen enthalten",multipleValueAlert:"Mehrere Standardwerte wurden angegeben. Der Parameter lässt mehrere Werte nicht zu.",nullValueAlert:"Im Wertfeld wurde ein Nullwert angegeben. Der Parameter lässt keine Nullwerte zu."},alertMessage:{confirmNullCheck:"Verfügbare oder Standardwerte enthalten möglicherweise einen Nullwert, möchten Sie das Kontrollkästchen 'Nullwert zulassen' aktivieren?",confirmBlankValue:"Verfügbare oder Standardwerte enthalten möglicherweise einen leeren Wert, möchten Sie das Kontrollkästchen für den leeren Wert aktivieren?",dataTypeChange:"Wenn Sie den Datentyp ändern, werden Änderungen an den verfügbaren und Standardwerten verwerfen. Möchten Sie den Datentyp trotzdem ändern?",deleteAlert:"Löschen Sie den Berichtsparameter"},assignData:{title:"Parameter",availableValue:"Verfügbarer Wert",defaultValue:"Standardwert",none:"Keiner",specify:"Angeben",query:"Abfragewert",ok:"OK",cancel:"Stornieren",availableFields:{specifyDescriptionText:"Fügen Sie die verfügbaren Werte für die Parameter hinzu:",queryDescriptionText:"Wählen Sie den Datensatz und die Felder für die verfügbaren Werte aus:",nameFieldWaterMark:"Etikette",valueFieldWaterMark:"Wert"},defaultFields:{specifyDescriptionText:"Fügen Sie die Standardwerte für die Parameter hinzu:",queryDescriptionText:"Wählen Sie den Datensatz und die Felder für die Standardwerte aus:",defValueWaterMark:"Wählen Sie Standardwert"},datasetWaterMark:"Wählen Sie Datensatzwert",valueWaterMark:"Wählen Sie Wert",lableWaterMark:"Wählen Sie Bezeichnung",add:"HINZUFÜGEN",datasetLableText:"Datensatz",valueLableText:"Wertfeld",labelFieldText:"Beschriftungsfeld",errorMessage:{boolTypeCheck:"Wert ist kein boolescher Wert.",dateTypeCheck:"Wert ist ein ungültiges Datumsformat.",intTypeCheck:"Wert ist keine ganze Zahl.",floatTypeCheck:"Wert ist kein Float.",multipleValuesCheck:"Ein mehrwertiger Parameter darf keine Nullwerte enthalten",datasetFieldCheck:"Datensatzfeld ist erforderlich.",valueFieldCheck:"Wertfeld ist erforderlich.",syntaxLabelField:"Der eingegebene Wert im Beschriftungsfeld ist keine gültige Tokensyntax.",syntaxValueField:"Der eingegebene Wert im Wertfeld ist keine gültige Tokensyntax.",blankValueCheck:"Das Wertfeld ist leer. Der Parameter lässt keine leeren Werte zu.",nullValueCheck:"Im Wertfeld wurde ein Nullwert angegeben. Der Parameter lässt keine Nullwerte zu."},closeToolTip:"Schließen"}},formatData:{title:"Formatierungsdialog",typeSelect:"Art",typeFormat:{numberType:{numberType:"Nummer",decimalPlaces:"Nachkommastellen",negativeValues:"Negative Werte",showZeroAs:{showZeroAs:"Zeige Null als",none:"(keiner)"},representation:"Darstellung",repDropDwn:{thousands:"Tausende",millions:"Millionen",billions:"Milliarden"},useRegionFormating:"Verwenden Sie regionale Formatierung",use1000Separator:"Verwenden Sie 1000 Trennzeichen (,)"},currency:{currencyType:"Währung",decimalPlaces:"Nachkommastellen",negativeValues:"Negative Werte",cultureCurrency:"Währung Kultur",showZeroAs:{none:"(keiner)"},representation:"Darstellung",repDropDwn:{thousands:"Tausende",millions:"Millionen",billions:"Milliarden"},useRegionFormating:"Verwenden Sie regionale Formatierung",use1000Separator:"Verwenden Sie 1000 Trennzeichen (,)",includeSpace:"Ein Leerzeichen einfügen"},date:{dateType:"Datum",date:"Datum"},time:{timeType:"Zeit",time:"Zeit"},percentage:{percentageType:"Prozentsatz",decimalPlaces:"Nachkommastellen",includeSpace:"Ein Leerzeichen einfügen"},scientific:{scientificType:"Wissenschaftlich",decimalPlaces:"Nachkommastellen"},custom:{customType:"Brauch",customFormat:"Benutzerdefiniertes Format"}},preview:"Vorschau",ok:"OK",cancel:"Stornieren",close:"Schließen"},expression:{title:"Ausdruck",descriptionText:"Setze Ausdruck für: ",optionLabel:"Optionen",dataLabel:"DATEN",descritionLabel:"Beschreibung",exampleLabelText:"Beispiel",ok:"OK",cancel:"Stornieren",closeToolTip:"Schließen",textAreaWaterMark:"Ausdruck",category:{builtInFields:"Integrierte Felder",operators:"Betreiber",functions:"Funktionen"},parameters:"Parameter",optionWaterMark:"Wähle eine Option",dataWaterMark:"Wählen Sie eine Daten aus",reportData:"Keine Berichtdaten gefunden",description:{executionTime:"Datum und Uhrzeit der Ausführung der Berichte",overallPageNumber:"Die aktuelle Gesamtseitenzahl kann nur in der Kopf- oder Fußzeile der Seite verwendet werden.",overallTotalPages:"Die Gesamtzahl der Seiten im Bericht kann nur in der Kopf- und Fußzeile der Seite verwendet werden.",pageName:"Der Name der aktuellen Seite im Bericht kann nur in der Kopf- oder Fußzeile der Seite verwendet werden.",pageNumber:"Die aktuelle Seitenzahl, die durch Seitenumbrüche zurückgesetzt werden kann",isInteractive:"Ein boolescher Wert, der angibt, ob die aktuelle Rendering-Anfrage ein interaktives Format verwendet.",renderName:"Der Name des Renderers, der in der RSReportServer-Konfigurationsdatei registriert ist.",reportFolder:"Der vollständige Pfad zu dem Ordner mit dem Bericht enthält nicht die Berichtsserver-URL.",reportName:"Die URL des Berichtsservers, auf dem der Bericht ausgeführt wird.",reportServerUrl:"Die URL des Berichtsservers, auf dem der Bericht ausgeführt wird.",totalPages:"Die Gesamtzahl der Seiten in der aktuellen fortlaufenden Seitenfolge kann nur in der Kopf- und Fußzeile der Seite verwendet werden. Die Nummer kann mithilfe von Seitenumbrüchen zurückgesetzt werden.",language:"Die Sprachen-ID des Clients, der den Bericht ausführt.",userID:"Die ID des Benutzers, der den Bericht ausführt.",powerNumberType:"Erhebt eine Zahl an die Macht einer anderen Nummer.",multiply:"Multipliziert zwei Zahlen.",integerDivision:"Dividiert zwei Zahlen und gibt eine Ganzzahl zurück.",floatDivision:"Dividiert zwei Zahlen und gibt einen Fließkommawert zurück.",modulus:"Dividiert zwei Zahlen und gibt nur den Rest zurück.",add:"Fügt zwei Zahlen hinzu und kann zum Verketten von zwei Strings verwendet werden.",difference:"Gibt die Differenz zwischen zwei Zahlen aus oder gibt den negativen Wert eines numerischen Ausdrucks an.",lesser:"Weniger als.",lesserOrEqual:"Gleich oder kleiner als.",greater:"Größer als.",greaterOrEqual:"Größer als oder gleich wie.",equal:"Gleich.",notEqual:"Nicht gleichzusetzen mit.",like:"Vergleicht zwei Strings.",isOperator:"Vergleicht zwei Objektreferenzvariablen.",expression:"Erzeugt eine String-Verkettung zweier Ausdrücke.",stringType:"Fügt zwei Zahlen hinzu und kann zum Verketten von zwei Strings verwendet werden.",and:"Führt eine logische Konjunktion für zwei boolesche Ausdrücke oder eine bitweise Konjunktion für zwei aus ",not:"Führt eine logische Negation für einen booleschen Ausdruck oder eine bitweise Negation durch auf einem numerischen Ausdruck.",or:"Wird verwendet, um eine logische Disjunktion für zwei boolesche Ausdrücke oder eine bitweise Disjunktion für zwei auszuführen numerische Werte.",xor:"Führt eine logische Ausschlussoperation für zwei boolesche Ausdrücke oder bitweise aus Ausschluss für zwei numerische Ausdrücke.",andAlso:"Führt eine logische Verknüpfung für zwei Ausdrücke aus.",orElse:"Wird verwendet, um eine logische Disjunktion für zwei Ausdrücke kurzzuschließen.",left:"Führt eine arithmetische Linksverschiebung für ein Bitmuster durch.",right:"Führt eine arithmetische Rechtsverschiebung für ein Bitmuster durch.",asc:"Gibt einen ganzzahligen Wert zurück, der den einem Zeichen entsprechenden Zeichencode darstellt.",ascW:"Gibt einen ganzzahligen Wert zurück, der den einem Zeichen entsprechenden Zeichencode darstellt.",chr:"Gibt das Zeichen zurück, das dem angegebenen Zeichencode zugeordnet ist.",chrW:"Gibt das Zeichen zurück, das dem angegebenen Zeichencode zugeordnet ist.",filter:"Gibt ein nullbasiertes Array zurück, das basierend auf angegebenen Filterkriterien eine Teilmenge eines Zeichenfolgenarrays enthält.",formatStringType:"Gibt eine formatierte Zeichenfolge gemäß den Anweisungen in einem Formatzeichenfolgenausdruck zurück.",currency:"Gibt einen Ausdruck zurück, der als Währungswert formatiert wurde, indem das Währungssymbol verwendet wird. in der Systemsteuerung definiert.",dateTime:"Gibt einen Zeichenfolgenausdruck zurück, der einen Datums- / Uhrzeitwert darstellt.",numberType:"Gibt einen Ausdruck zurück, der als Zahl formatiert ist.",percent:"Gibt einen Ausdruck zurück, der als Prozentsatz formatiert ist (dh mit 100 multipliziert).",getChar:"Gibt einen char-Wert zurück, der das Zeichen aus dem angegebenen Index in der angegebenen Zeichenfolge darstellt.",inStr:"Gibt eine Ganzzahl zurück, die die Startposition des ersten Auftretens einer Zeichenfolge innerhalb einer anderen angibt.",inStrRev:"Gibt die Position des ersten Vorkommens einer Zeichenfolge in einer anderen Zeichenfolge ab der rechten Seite der Zeichenfolge zurück.",join:"Gibt eine Zeichenfolge zurück, die durch Verbinden einer Anzahl von Teilzeichenfolgen in einem Array erstellt wurde.",lCase:"Gibt eine Zeichenfolge oder ein Zeichen zurück, die in Kleinbuchstaben konvertiert wurden.",leftStringType:"Gibt eine Zeichenfolge zurück, die eine angegebene Anzahl von Zeichen von der linken Seite einer Zeichenfolge enthält.",stringLength:"Gibt eine Ganzzahl zurück, die entweder die Anzahl der Zeichen in einer Zeichenfolge oder die Zahl enthält.",lSet:"Gibt eine linksbündige Zeichenfolge zurück, die die angegebene Zeichenfolge enthält, die an die angegebene Länge angepasst wurde.",leftTrim:"Gibt die Zeichenfolge zurück, ohne Leerzeichen auf der linken Seite in der angegebenen Zeichenfolge.",middle:"Gibt eine Zeichenfolge mit einer angegebenen Anzahl von Zeichen aus einer Zeichenfolge zurück.",replace:"Gibt eine Zeichenfolge zurück, in der eine angegebene Teilzeichenfolge durch eine andere ersetzt wurde.",rightString:"Gibt eine Zeichenfolge zurück, die eine angegebene Anzahl von Zeichen von der rechten Seite einer Zeichenfolge enthält.",rightSet:"Gibt eine rechtsbündige Zeichenfolge zurück, die die angegebene Zeichenfolge enthält, die an die angegebene Länge angepasst wurde.",rightTrim:"Gibt die Zeichenfolge ohne rechte nachstehende Leerzeichen in der angegebenen Zeichenfolge zurück.",stringSpace:"Gibt eine Zeichenfolge zurück, die aus der angegebenen Anzahl von Leerzeichen besteht.",splitString:"Gibt ein nullbasiertes, eindimensionales Array zurück, das eine angegebene Anzahl von Teilzeichenfolgen enthält.",strComp:"Gibt -1, 0 oder 1 basierend auf dem Ergebnis eines Zeichenfolgenvergleichs zurück.",strConv:"Gibt eine Zeichenfolge zurück, die wie angegeben konvertiert wurde.",duplicateString:"Gibt eine Zeichenfolge oder ein Objekt zurück, das aus dem angegebenen Zeichen besteht und die angegebene Anzahl von Malen wiederholt wurde.",strReverse:"Gibt eine Zeichenfolge zurück, in der die Zeichenreihenfolge einer angegebenen Zeichenfolge umgekehrt ist.",trim:"Gibt die Zeichenfolge ohne nachgestellte Leerzeichen in der angegebenen Zeichenfolge zurück.",upperCase:"Gibt eine Zeichenfolge oder ein Zeichen zurück, die die angegebene Zeichenfolge in Großbuchstaben konvertiert.",cDate:"In Datum umrechnen.",dateAdd:"Gibt einen Datumswert zurück, der Datums- und Uhrzeitwerte enthält, denen ein angegebenes Zeitintervall hinzugefügt wurde.",dateDiff:"Gibt einen langen Wert zurück, der die Anzahl der Zeitintervalle zwischen zwei Datumswerten angibt.",datePart:"Gibt einen ganzzahligen Wert zurück, der die angegebene Komponente eines bestimmten Datums enthält.",dateSerial:"Gibt einen Datumswert zurück, der ein angegebenes Jahr, einen bestimmten Monat und einen angegebenen Tag darstellt, wobei die Zeitinformationen auf festgelegt sind Mitternacht (00:00:00).",dateString:"Gibt einen Zeichenfolgenwert zurück, der das aktuelle Datum gemäß Ihrem System darstellt.",dateValue:"Gibt einen Datumswert zurück, der die Datumsinformationen enthält, die durch eine Zeichenfolge mit der Zeitinformation dargestellt werden. ",day:"Gibt einen Ganzzahlwert von 1 bis 31 zurück, der den Tag des Monats darstellt.",format:"Gibt einen Zeichenfolgenausdruck zurück, der den Datums- / Uhrzeitwert darstellt.",hour:"Gibt einen Ganzzahlwert von 0 bis 23 zurück, der die Stunde des Tages darstellt.",minute:"Gibt einen Ganzzahlwert von 0 bis 59 zurück, der die Minute der Stunde darstellt.",month:"Gibt einen Ganzzahlwert von 1 bis 12 zurück, der den Monat des Jahres darstellt.",monthName:"Gibt einen Zeichenfolgenwert zurück, der den Namen des angegebenen Monats enthält.",now:"Gibt einen Datumswert zurück, der das aktuelle Datum und die Uhrzeit gemäß Ihrem System enthält.",second:"Gibt einen Ganzzahlwert von 0 bis 59 zurück, der die Sekunde der Minute darstellt.",timeOfDay:"Gibt einen Datumswert zurück, der die aktuelle Uhrzeit gemäß Ihrem System enthält, oder legt diesen fest.",timer:"Gibt einen doppelten Wert zurück, der die Anzahl der seit Mitternacht verstrichenen Sekunden darstellt.",timeSerial:"Gibt einen Datumswert zurück, der eine angegebene Stunde, Minute und Sekunde darstellt, wobei die Datumsinformationen relativ zum 1. Januar des Jahres 1 festgelegt werden.",timeString:"Gibt einen Zeichenfolgenwert zurück oder legt diesen fest, der die aktuelle Uhrzeit gemäß Ihrem System darstellt.",timeValue:" Gibt einen Datumswert zurück, der die Zeitinformationen enthält, die durch eine Zeichenfolge dargestellt werden, wobei die Datumsinformationen auf den 1. Januar des Jahres 1 festgelegt sind.",timeToday:"Gibt einen Datumswert zurück, der das aktuelle Datum gemäß Ihrem System enthält.",timeWeekday:"Gibt einen ganzzahligen Wert zurück, der eine Zahl enthält, die den Wochentag darstellt.",timeWeekdayName:"Gibt einen Zeichenfolgenwert zurück, der den Namen des angegebenen Wochentages enthält.",year:"Gibt einen ganzzahligen Wert zwischen 1 und 9999 für das Jahr zurück.",abs:"Gibt den absoluten Wert einer Gleitkommazahl mit einfacher Genauigkeit zurück.",acos:"Gibt den Winkel zurück, dessen Kosinus die angegebene Zahl ist.",asin:"Gibt den Winkel zurück, dessen Sinus die angegebene Zahl ist.",atan:"Gibt den Winkel zurück, dessen Tangente die angegebene Zahl ist.",atan2:"Gibt den Winkel zurück, dessen Tangente der Quotient zweier angegebener Zahlen ist.",bigMultiply:"Produziert das vollständige Produkt von zwei 32-Bit-Nummern.",ceiling:"Gibt die kleinste Ganzzahl zurück, die größer oder gleich der angegebenen Ganzzahl ist.",cos:"Gibt den Kosinus des angegebenen Winkels zurück.",cosh:"Gibt den Hyperbelkosinus des angegebenen Winkels zurück.",exponent:"Gibt e auf die angegebene Potenz zurück.",fixNumberType:"Gibt einen ganzzahligen Teil einer Zahl zurück.",floor:"Gibt die größte Ganzzahl zurück, die kleiner oder gleich der angegebenen ist.",integer:"Gibt einen ganzzahligen Teil einer Zahl zurück.",logrithm:"Gibt den natürlichen (Basis e) Logarithmus einer angegebenen Zahl zurück.",logrithm10:"Gibt den natürlichen (Basis e) Logarithmus einer angegebenen Anzahl zurück.",maximum:"Gibt den Maximalwert von allen Nicht-Null-Werten des angegebenen Ausdrucks zurück.",minimum:"Gibt den Minimalwert aus allen Nicht-Null-Werten des angegebenen Ausdrucks zurück.",power:"Gibt eine angegebene Zahl mit der angegebenen Potenz zurück.",random:"Gibt eine zufällige Zahl von einzelnen Typen zurück.",round:"Rundet einen Gleitkommawert mit doppelter Genauigkeit auf die nächste Ganzzahl.",sign:"Gibt einen Wert zurück, der das Vorzeichen einer 8-Bit-Ganzzahl mit Vorzeichen angibt.",sin:"Gibt den Sinus des angegebenen Winkels zurück.",sinh:"Gibt den Hyperbelsinus des angegebenen Winkels zurück.",squareRoot:"Gibt die Quadratwurzel einer angegebenen Zahl zurück.",tangent:"Gibt den Tangens des angegebenen Winkels zurück.",tangentH:"Gibt den Hyperbeltangens des angegebenen Winkels zurück.",isArray:"Gibt einen booleschen Wert zurück, der angibt, ob eine Variable auf ein Array verweist.",isDate:"Gibt einen booleschen Wert zurück, der angibt, ob ein Ausdruck einen gültigen Wert darstellt.",isNothing:"Gibt einen booleschen Wert zurück, der angibt, ob ein Ausdruck kein Objekt enthält.",isNumeric:"Gibt einen booleschen Wert zurück, der angibt, ob ein Ausdruck als Zahl ausgewertet werden kann",flowChoose:"Wählt einen Wert aus einer Liste von Argumenten aus und gibt ihn zurück.",flowIIf:"Gibt abhängig von der Auswertung eines Ausdrucks eines von zwei Objekten zurück.",switchFlow:"Evaluiert eine Liste von Ausdrücken und gibt einen entsprechenden Objektwert zurück bis zum ersten Ausdruck in der Liste, der wahr ist.",avg:"Gibt den Durchschnitt aller Nicht-Null-Werte aus dem angegebenen Ausdruck zurück.",count:"Gibt eine Anzahl der Werte aus dem angegebenen Ausdruck zurück.",countDistinct:"Gibt eine Anzahl aller eindeutigen Werte von der angegebenen zurück.",countRows:"Gibt eine Anzahl von Zeilen innerhalb des angegebenen Bereichs zurück.",first:"Gibt den ersten Wert aus dem angegebenen Ausdruck zurück.",last:"Gibt den letzten Wert aus dem angegebenen Ausdruck zurück.",standardDev:"Gibt die Standardabweichung aller Nicht-Null-Werte des angegebenen Werts zurück.",standardDevP:"Gibt die Populationsstandardabweichung aller Nicht-Null-Werte von der angegebene Ausdruck.",sum:"Gibt eine Summe der Werte des angegebenen Ausdrucks zurück.",variance:"Gibt die Varianz aller Nicht-Null-Werte des angegebenen Ausdrucks zurück.",varianceP:"Gibt die Populationsvarianz aller Nicht-Null-Werte des angegebenen Ausdrucks zurück.",runningValue:"Verwendet eine angegebene Funktion, um ein laufendes Aggregat des angegebenen Ausdrucks zurückzugeben.",aggregate:"Gibt ein benutzerdefiniertes Aggregat des angegebenen Ausdrucks zurück, wie vom Datenprovider definiert.",doubleDeclining:"Gibt einen doppelten Wert zurück, der die Abschreibung eines Vermögenswerts für einen bestimmten Zeitraum unter Verwendung der Methode mit degressivem Saldo oder einer anderen von Ihnen angegebenen Methode angibt.",futureValue:"Gibt einen doppelten Wert zurück, der den zukünftigen Wert einer Rente auf der Grundlage von regelmäßigen festen Zahlungen und einem festen Zinssatz angibt.",interestPayment:"Gibt einen doppelten Wert zurück, der die Zinszahlung für einen bestimmten Zeitraum einer Annuität auf der Grundlage periodischer fester Zahlungen und eines festen Zinssatzes angibt.",numberOfPeriods:"Gibt einen doppelten Wert zurück, der die Anzahl der Perioden für eine Annuität auf der Grundlage von regelmäßigen festen Zahlungen und einem festen Zinssatz angibt.",annuityPayment:"Gibt einen doppelten Wert zurück, der die Zahlung für eine Annuität festlegt, die auf periodischen festen Zahlungen und einem festen Zinssatz basiert.",principalPayment:"Gibt ein Double zurück, in dem die Kapitalzahlung für einen bestimmten Zeitraum einer Annuität basierend auf regelmäßigen festen Zahlungen und einem festen Zinssatz angegeben wird.",presentValue:"Gibt einen doppelten Wert zurück, der den Barwert einer Rente auf der Grundlage von regelmäßigen, festen Zahlungen, die in der Zukunft zu zahlen sind, und eines festen Zinssatzes angibt.",rateOfInterest:"Gibt einen doppelten Wert zurück, der den Zinssatz pro Periode für eine Annuität angibt.",straightLine:"Gibt einen doppelten Wert zurück, der die lineare Abschreibung eines Vermögenswertes für eine einzelne Periode angibt.",sumOfYearsDigits:"Gibt einen doppelten Wert zurück, der die Abwertung der Summe der Jahreszahlen eines Vermögenswerts für einen bestimmten Zeitraum angibt.",convertBool:"In Boolean umwandeln.",convertByte:"In Byte umwandeln.",convertChar:"In Char umwandeln",convertDate:"In Datum umrechnen.",convertDouble:"In Doppel konvertieren.",convertDecimal:"In Dezimal konvertieren.",convertInteger:"In Ganzzahl konvertieren.",convertLong:"Zu lang konvertieren.",convertObject:"In Objekt konvertieren.",convertShort:"Konvertieren zu kurz.",convertSingle:"In Einzelbild konvertieren.",convertString:"In Zeichenfolge konvertieren",fix:"Gibt einen ganzzahligen Teil einer Zahl zurück.",hexaDecimal:"Gibt eine Zeichenfolge zurück, die den Hexadezimalwert einer Zahl darstellt.",integerPortion:"Gibt einen ganzzahligen Teil einer Zahl zurück.",octal:"Gibt eine Zeichenfolge zurück, die den Oktalwert einer Zahl darstellt.",stringOfNumber:"Gibt eine Zeichenfolge zurück, die eine Zahl darstellt.",stringAsNumeric:"Gibt eine Zahlen in einer Zeichenfolge als numerischen Wert des entsprechenden Typs zurück.",inScope:"Gibt true zurück, wenn die aktuelle Instanz innerhalb des angegebenen Bereichs liegt.",depthLevel:"Gibt eine auf Null basierende ganze Zahl zurück, die die aktuelle Tiefe darstellt.",previous:"Gibt den Wert des Ausdrucks für die vorherige Datenzeile zurück.",rowNumber:"Gibt eine fortlaufende Anzahl aller Zeilen im angegebenen Bereich zurück."}},dataAssign:{measures:"Maße",dimensions:"Maße",addDatasource:"Fügen Sie Datenquelle hinzu",errorMessagePrefix:"Sie haben noch keine Datenquelle konfiguriert.",errorMessageSuffix:"Fügen Sie eine Datenquelle hinzu, um Daten an Berichtselemente in Ihrem Designer zu binden.",search:"Suche",dragOnDrop:"Ziehen und loslassen"},reportProperty:{header:"Header",body:"Karosserie",footer:"Fußzeile",report:"Report",basicSettings:{categoryName:"Grundeinstellungen",background:"Hintergrundfarbe",borderTypes:{border:"Rand",borderLeft:"Links",borderTop:"Oben",borderRight:"Recht",borderBottom:"Unterseite"},borderStyles:{solid:"Solide",none:"Keiner",double:"Doppelt",dashed:"Gestrichelt",dotted:"Gepunktet"}},generalSettings:{categoryName:"Allgemeines",printFirstPage:"Auf der ersten Seite drucken",printLastPage:"Drucken Sie auf der letzten Seite"},size:{sizeLabel:"Größe",paddingTypes:{padding:"Polsterung",paddingLeft:"Links",paddingTop:"Oben",paddingRight:"Recht",paddingBottom:"Unterseite"}},position:{categoryPosition:"Position",positionLabel:"Position",left:"Links",top:"Oben",sizeLabel:"Größe",width:"Breite",height:"Höhe"},codeModule:{code:"Code"},margin:{categoryName:"Spanne",categoryHeader:"Spanne",types:{left:"Links",right:"Recht",bottom:"Unterseite",top:"Oben"}},pageUnit:{header:"Seiteneinheiten",label:"Seiteneinheit",types:{inches:"Zoll",centimeters:"Zentimeter",pixels:"Pixel",points:"Punkte",millimeters:"Millimeter",picas:"Picas"}},columns:{header:"Seitenspalte",label:"Säulen",columnSpacing:"Spaltenabstand"},paperSize:{orientation:"Orientierung",header:"Papier größe",label:"Papier größe",orientationTypes:{landScape:"Landschaft",portrait:"Porträt"},types:{a3Size:"A3",a4Size:"A4",b4Size:"B4(JIS)",b5Size:"B5(JIS)",envelope:"Briefumschlag #10",envelopeMonarch:"Umschlag Monarch",executive:"Exekutive",legal:"Legal",letter:"Brief",tabloid:"Boulevardzeitung",custom:"Brauch"}},styleTooltip:"Stil",colorTooltip:"Farbe",sizeTooltip:"Größe"},textBoxProperty:{contents:{categoryName:"Inhalt",content:"Inhalt"},basicSettings:{categoryName:"Grundeinstellungen",font:{categoryName:"Schriftart",defaultStyle:"Standard",normal:"Normal",italic:"Kursiv"},fontStyle:{categoryName:"Schriftstil",defaultStyle:"Standard",normal:"Normal",thin:"Dünn",extraLight:"Extra-Licht",light:"Licht",medium:"Mittel",semiBold:"Semi Fett",bold:"Fett gedruckt",extraBold:"Extra fett",heavy:"Schwer"},textDecoration:{categoryName:"Textdekoration",defaultStyle:"Standard",none:"Keiner",underline:"Unterstreichen",lineThrough:"Linie durch",overline:"Überstreichen"},format:"Format"},alignment:{categoryName:"Ausrichtung",textAlignment:{categoryName:"Textausrichtung",defaultStyle:"Standard",left:"Links",center:"Center",right:"Recht"},verticalAlignment:{categoryName:"Vertikale Ausrichtung",defaultStyle:"Standard",top:"Oben",middle:"Mitte",bottom:"Unterseite"},lineSpacing:"Zeilenhöhe"},appearance:{categoryName:"Aussehen",borderTypes:{border:"Rand",borderLeft:"Links",borderTop:"Oben",borderRight:"Recht",borderBottom:"Unterseite"},borderStyles:{solid:"Solide",none:"Keiner",double:"Doppelt",dashed:"Gestrichelt",dotted:"Gepunktet"},background:"Hintergrundfarbe"},link:"Verknüpfung",linkReport:"Link-Bericht",position:{categoryPosition:"Position",positionLabel:"Position",sizeLabel:"Größe",left:"Links",top:"Oben",width:"Breite",height:"Höhe",direction:{categoryName:"Richtung",leftToRight:"Links nach rechts",rightToLeft:"Rechts nach links"}},visibility:{categoryName:"Sichtweite",visible:"Sichtbar",toggleItem:"Element umschalten",intialToggleState:"Anfänglicher Umschaltzustand"},miscellaneous:{categoryName:"Sonstiges",canGrow:"Kann wachsen",canShrink:"Kann schrumpfen"},paragraphSettings:{categoryName:"Absatzeinstellungen",textAlignment:{categoryName:"Textausrichtung",defaultStyle:"Standard",left:"Links",center:"Center",right:"Recht"},indent:{categoryName:"Einzug",leftIndent:"Links",rightIndent:"Recht"},space:{categoryName:"Raum",topSpace:"Oben",bottomSpace:"Unterseite"},listLevel:{categoryName:"Listenebene",zeroLevel:"",oneLevel:"",twoLevel:"",threeLevel:"",fourLevel:""},listStyle:{categoryName:"Listenstil",none:"Keiner",numbered:"Nummeriert",bulleted:"Bulleted"}},padding:{padding:"Polsterung",paddingLeft:"Links",paddingTop:"Oben",paddingRight:"Recht",paddingBottom:"Unterseite"},contextMenu:{cut:"Schnitt",copy:"Kopieren",paste:"Einfügen",expression:"Ausdruck",pasteAlert:"Ihr Browser unterstützt keinen direkten Zugriff auf die Zwischenablage. Verwenden Sie statt der Einfügeoperation die Tastenkombination Strg + V"},fontStyleTooltip:"Stil",fontWeightTooltip:"Gewicht",fontSizeTooltip:"Größe",fontColorTooltip:"Farbe",fontFamilyTooltip:"Schriftfamilie",styleTooltip:"Stil",colorTooltip:"Farbe",sizeTooltip:"Größe",selectedText:"Ausgewählter Text"},designPanel:{headerText:"Header",footerText:"Fußzeile",pasteAlert:"Im Kopf- und Fußbereich werden nur grundlegende Elemente unterstützt",pasteTitle:"Einfügen"},customProperty:{position:{categoryPosition:"Position",positionLabel:"Position",left:"Links",top:"oben",sizeLabel:"Größe",width:"Breite",height:"Höhe"},appearance:{categoryAppearance:"Aussehen",borderTypes:{border:"Rand",borderLeft:"Links",borderTop:"oben",borderRight:"Recht",borderBottom:"Unterseite"},borderStyles:{solid:"Solide",none:"Keiner",double:"Doppelt",dashed:"Gestrichelt",dotted:"Gepunktet"},backGround:"Hintergrundfarbe"},visibility:{categoryName:"Sichtweite",visible:"Sichtbar"},styleTooltip:"Stil",colorTooltip:"Farbe",sizeTooltip:"Größe"},tablixProperty:{data:{categoryName:"Daten",datasetName:"Datensatz",datasetNone:"Keiner"},appearance:{categoryName:"Aussehen",borderTypes:{border:"Rand",borderLeft:"Links",borderTop:"oben",borderRight:"Recht",borderBottom:"Unterseite"},borderStyles:{solid:"Solide",none:"Keiner",double:"Doppelt",dashed:"Gestrichelt",dotted:"Gepunktet"},backGround:"Hintergrundfarbe"},miscellaneous:{categoryName:"Verschiedenes",noRowsMessage:"Keine Zeilenmeldung",pageName:"Seitenname",keepTogether:"Zusammen halten",repeatColumnHeaders:"Wiederholen Sie die Spaltenüberschriften",repeatRowHeaders:"Wiederholen Sie die Zeilenüberschriften",fixedColumnHeaders:"Feste Spaltenüberschriften",fixedRowHeaders:"Feste Zeilenüberschriften"},font:{categoryName:"Schriftart",defaultStyle:"Standard",normal:"Normal",italic:"Kursiv"},fontStyle:{categoryName:"Schriftstil",defaultStyle:"Standard",normal:"Normal",thin:"Dünn",extraLight:"Extra-Licht",light:"Licht",medium:"Mittel",semiBold:"Semi Fett",bold:"Fett gedruckt",extraBold:"Extra fett",heavy:"Schwer"},textDecoration:{categoryName:"Textdekoration",defaultStyle:"Standard",none:"Keiner",underline:"Unterstreichen",lineThrough:"LineThrough",overline:"Überschrift"},alignment:{categoryName:"Ausrichtung",textAlignment:{categoryName:"Textausrichtung",defaultStyle:"Standard",left:"Links",center:"Center",right:"Recht"},verticalAlignment:{categoryName:"Vertikale Ausrichtung",defaultStyle:"Standard",top:"oben",middle:"Mitte",bottom:"Unterseite"}},padding:{padding:"Polsterung",paddingLeft:"Links",paddingTop:"Oben",paddingRight:"Recht",paddingBottom:"Unterseite"},position:{categoryPosition:"Position",positionLabel:"Position",left:"Links",top:"Oben",sizeLabel:"Größe",width:"Breite",height:"Höhe"},visibility:{categoryName:"Sichtweite",visible:"Sichtbar",toggleItem:"Umschalten"},staticGroupProp:{categoryName:"Grundeinstellungen",filters:"Filter",sorts:"Sortiert",fixedData:"Feste Daten",groupExp:"Gruppen",hideIfNoRows:"Verstecken, wenn keine Zeilen",keepWithGroup:"Bei Gruppe bleiben",repeatOnNewPage:"Wiederholen Sie auf neuer Seite",afterGroup:"Nach dem",beforeGroup:"Vor",pageBreak:{categoryName:"Seitenumbruch",enablePageBreak:"Seitenumbruch aktivieren",breakLocation:{categoryName:"Ort brechen",none:"Keiner",start:"Anfang",end:"Anfang",startAndEnd:"Start und Ende",between:"Zwischen"},pageNumberReset:"Seitenzahl zurücksetzen"}},fontStyleTooltip:"Stil",fontWeightTooltip:"Gewicht",fontSizeTooltip:"Größe",fontColorTooltip:"Farbe",fontFamilyTooltip:"Schriftfamilie",styleTooltip:"Stil",colorTooltip:"Farbe",sizeTooltip:"Größe",tablixMember:"Tablix-Mitglied"},rowColumnGroup:{rowGroupLable:"Zeilengruppen",columnGroupLable:"Spaltengruppen",tablixAlertHeader:"Tablix",alertMessage:"Aktivieren Sie die Erweiterungsoption, um das Tablix-Berichtselement auszuwählen",contextMenu:{addgroup:"Gruppe hinzufügen",advanced:"Fortgeschritten",deletegroup:"Gruppe löschen",addtotal:"Summe hinzufügen",groupproperties:"Gruppeneigenschaften",addColumnGroup:"Spaltengruppe hinzufügen",addRowGroup:"Zeilengruppe hinzufügen"},contextSubMenu:{adjacentafter:"Angrenzend nach",adjacentbefore:"Angrenzend vor",childgroup:"Kindergruppe",parentgroup:"Übergeordnete Gruppe",totalafter:"Nach",totalbefore:"Vor",childGroupAlert:"Gruppe kann nicht in Details eingefügt werden."}},tablixContextMenu:{rowMenu:{insertRow:"Zeile einfügen",above:"Above",below:"Unten"},columnMenu:{insertColumn:"Spalte einfügen",left:"Links",right:"Recht"},rowGroupMenu:{insideGroupAbove:"Innerhalb der Gruppe - oben",insideGroupBelow:"Innerhalb der Gruppe - Unten",outsideGroupAbove:"Außerhalb der Gruppe - oben",outsideGroupBelow:"Außerhalb der Gruppe - unten"},columnGroupMenu:{insideGroupLeft:"Innerhalb der Gruppe - Links",insideGroupRight:"Innerhalb der Gruppe - richtig",outsideGroupLeft:"Außerhalb Gruppe - Links",outsideGroupRight:"Außerhalb der Gruppe - richtig"},deleteRows:"Zeilen löschen",deleteColumns:"Spalten löschen",rowVisibility:"Zeilen-Sichtbarkeit",columnVisibility:"Sichtbarkeit der Spalten",tablixProperties:"Tablix-Eigenschaften",splitcells:"Zellen teilen",mergecells:"Zellen verbinden",groupMenu:{adjacentAbove:"Angrenzend darüber",adjacentBelow:"Angrenzend unten",adjacentleft:"Angrenzend links",adjacentright:"Angrenzendes Recht",childGroup:"Kindergruppe",parentGroup:"Übergeordnete Gruppe",deleteRowGroup:"Zeilengruppe löschen",deleteColGroup:"Spaltengruppe löschen",addRowGroup:"Zeilengruppe",addColGroup:"Spaltengruppe"},reportItemMenu:{insertItem:"Einfügen",chart:"Diagramm"},totalMenu:{total:"Summe hinzufügen",row:"Reihe",column:"Säule",before:"Vor",after:"Nach dem"},cellMenu:{addExpression:"Ausdruck hinzufügen",editExpression:"Ausdruck bearbeiten",datasource:"Datenquelle hinzufügen",noFields:"Keine Felder",addText:"Text hinzufügen",editText:"Text bearbeiten"},basicItems:{deleteItem:"Löschen",cut:"Schnitt",copy:"Kopieren",paste:"Einfügen"}},tablixAlertDialog:{ok:"OK",cancel:"Stornieren",closeToolTip:"Schließen",deleteRowTitle:"Zeilen löschen",deleteRow:"Nur Zeilen löschen",deleteRowGroup:"Zeilen und zugehörige Gruppen löschen",deleteRowContent:"Zeilenoptionen löschen",deleteBodyRow:"Der Tablix-Körper muss mindestens eine Zeile enthalten.",deleteColumnTitle:"Spalten löschen",deleteColumn:"Nur Spalten löschen",deleteColumnGroup:"Löschen Sie Spalten und verknüpfte Gruppen",deleteColumnContent:"Spaltenoptionen löschen",deleteBodyColumn:"Der Tablix-Körper muss mindestens eine Spalte enthalten.",deleteGroup:"Nur Gruppe löschen",deleteGroupRowColumn:"Gruppe und zugehörige Zeilen und Spalten löschen",deleteGroupTitle:"Gruppe löschen",deleteGroupContent:"Gruppenoptionen löschen",deleteStructure:"Gruppenstruktur nicht verfügbar",removeRowAlert:"Entfernen der Zeile im Tablix-Berichtselement fehlgeschlagen",removeRow:"Zeilen entfernen",removeColumn:"Spalten entfernen",addRow:"Zeile hinzufügen",addColumn:"Spalte hinzufügen",removeColumnAlert:"Entfernen der Spalte in Tablix-Berichtselement fehlgeschlagen",addRowAlert:"Zeile konnte nicht in Tablix-Berichtselement hinzugefügt werden",addColumnAlert:"Fehler beim Hinzufügen der Spalte im Tablix-Berichtselement"},cellMergingAlertInfo:{merge:"Zellen verbinden",mergeAlert:"Fehler beim Zusammenführen von Zellen im Tablix-Berichtselement",split:"Zellen teilen",splitAlert:"Das Teilen von Zellen im Tablix-Berichtselement ist fehlgeschlagen"},tablixAlertInfo:{addGroup:"Gruppe hinzufügen",removeGroup:"Gruppe entfernen",adjacentAfterAlert:"Fehler beim Hinzufügen einer benachbarten Gruppe in der Hierarchiestruktur",adjacentBeforeAlert:"Fehler beim Hinzufügen einer benachbarten Gruppe in der Hierarchiestruktur",childGroupALert:"Fehler beim Hinzufügen einer untergeordneten Gruppe in der Hierarchiestruktur",title:"Tablix-Berichtselement",parentGroupAlert:"Fehler beim Hinzufügen der übergeordneten Gruppe in der Hierarchiestruktur",removeGroupAlert:"Fehler beim Entfernen der Gruppe in der Hierarchiestruktur",selectedMemberAlert:"Das ausgewählte Mitglied ist kein Gruppenmitglied",pasteActionAlert:"Die Informationen können nicht hochgeladen werden, da der Kopierbereich und der Einfügebereich nicht die gleiche Größe und Form haben.",pasteTitle:"Einfügen"},tablixGroup:{title:"Tablix-Gruppe",headerTxt:"Gruppenbezeichnung",groupBy:"Gruppiere nach:",chooseField:"Wählen Sie Feld",showDetailData:"Zeige Detaildaten",addGroupHeader:"Header hinzufügen",addGroupFooter:"Fußzeile hinzufügen",ok:"OK",cancel:"Stornieren",closeToolTip:"Schließen"},tablixDataAssignMenu:{datasource:"Datenquelle hinzufügen",addExpression:"Ausdruck hinzufügen",editExpression:"Ausdruck bearbeiten",addText:"Text hinzufügen",editText:"Text bearbeiten",search:"Suche",noFieldsFound:"Keine Felder gefunden"},tablixTotalAlert:{totalHeader:"Gesamtheader hinzufügen",totalStatic:"Summe hinzufügen",headerMessage:"Fehler beim Hinzufügen der gesamten Zeile oder Spalte zum Gruppenkopf im Tablix-Berichtselement",staticMessage:"Fehler beim Hinzufügen der gesamten Zeile oder Spalte zum Tablix-Hauptteil im Tablix-Berichtselement"},tablixAddTextDialog:{save:"sparen",add:"Hinzufügen",cancel:"Stornieren",closeToolTip:"Schließen",addText:"Text hinzufügen",editText:"Text bearbeiten"},queryDesigner:{storeParameter:{title:"Parameter",ok:"OK",cancel:"Stornieren",parameterLable:"Parameter",nullLable:"Null",valueLable:"Wert",dataTypeLable:"Datentyp",closeToolTip:"Schließen"},parameter:{title:"Parameter",ok:"OK",cancel:"Stornieren",parameterLable:"Parameter",nullLable:"Null",valueLable:"Wert",dataTypeLable:"Datentyp",auto:"Auto",text:"Text",closeToolTip:"Schließen"},filter:{title:"Abfragefilter",descriptionLable:"Liste der Tabellenfilter",add:"HINZUFÜGEN",save:"OK",cancel:"Stornieren",nullLable:"Null",trueLable:"Wahr",falseLable:"Falsch",parameterTooltip:"Als Parameter einbeziehen",closeToolTip:"Schließen",intOperatorType:{equals:"Gleich",doesNotEqual:"Ist nicht gleich",greaterThan:"Größer als",greaterThanOrEqual:"Größer als oder gleich wie",lessThan:"Weniger als",lessThanOrEqual:"Gleich oder kleiner als",between:"Zwischen",notBetween:"Nicht zwischen"},stringOpertorType:{equals:"Gleich",startsWith:"Beginnt mit",endWith:"Endet mit",contains:"Enthält",notContains:"Nicht enthält"},errorMessage:{dateValidation:"Wert ist ein ungültiges Datumsformat.",commonContent:"Der Filter ist eingeschaltet ",booleanValidation:" hat keine Werte zum Filtern. Bitte geben Sie die Werte für den Filter an.",stringValidation:" hat keine richtigen Werte zum Filtern. "}},previewArea:{dataPreview:"Datenvorschau",noRecords:"Keine Datensätze zur Anzeige",generatePreview:"Vorschau generieren",executeRecords:"Ausführen, um Datensätze in der Vorschau anzuzeigen",record:"Aufzeichnung",records:"Aufzeichnungen",retrieved:"Abgerufen",loadRecord:"Mehr laden",update:"Aktualisieren"},schemaArea:{search:"Suche",matchesFound:"Keine Treffer gefunden",rename:"Umbenennen",aggregation:"Anhäufung",dialogHeader:"Datensatz",alertMessage:{datasourceAlert:"Wählen Sie eine Datenquelle zum Konfigurieren des Berichtsdatensatzes aus",removeTable:"Die folgenden zugehörigen Tabellen werden damit entfernt",duplicateName:"Der angegebene Spaltenname ist bereits vorhanden",duplicateDatasetName:"Der angegebene Name ist bereits in der Datensatzliste vorhanden",datasetSpecialCharacter:"Name sollte keine Leerzeichen und Sonderzeichen enthalten",specialCharacter:"Der Spaltenname darf keine Sonderzeichen enthalten.",switcherAlert:"Wenn Sie zum visuellen Designer wechseln, werden manuelle Änderungen an der Abfrage verworfen. Möchten Sie den visuellen Designer trotzdem verwenden?"},errorMessage:{specifyName:"Geben Sie den Spaltennamen an",specifyDatasetName:"Geben Sie den Datensatznamen an",previewFailed:"DataSet konnte die ausgewählte Tabelle nicht in der Vorschau anzeigen",specifyQuery:"Geben Sie die DataSet-Abfrage an",selectTable:"Wählen Sie die Tabelle aus, um den Datensatz zu speichern",queryFailed:"Datensatz konnte die Abfrage der ausgewählten Tabelle nicht speichern",tableProcedure:"Datensatz konnte die ausgewählte Tabellenprozedur nicht abrufen"}},toolBar:{datasourceLable:"Datenquelle",datasetName:"Name",run:"Lauf",join:"Beitreten",expression:"Ausdruck",filter:"Filter",code:"Code",finish:"Fertig",cancel:"Stornieren",parameter:"Parameter",datasourceWaterMark:"Wählen Sie eine DataSource aus",autoPreview:"Automatische Vorschau"},joiner:{title:"Abfrage Joiner",descriptionLable:"Liste der Tabellenbeziehungen",add:"HINZUFÜGEN",save:"OK",cancel:"Stornieren",closeToolTip:"Schließen",addField:"Feld hinzufügen",leftTableWaterMark:"Linke Tabelle",rightTableWaterMark:"Rechter Tisch",leftFieldWaterMark:"Linkes Feld",rightFieldWaterMark:"Rechtes Feld",operatorWaterMark:"Operator",joinTypeWaterMark:"Verbindungstyp",joinTypes:{inner:"Innere",outer:"Links außen",rightOuter:"Rechts außen",fullOuter:"Voller Äußerer"},errorMessage:{removeField:"Jede Beziehung muss eine Feldbedingung haben. Also erlaubt es nicht, dieses Feld zu löschen.",noRelationAlert:" hat keine Beziehung zu anderen Tabellen",selectLeftTable:"Wählen Sie den Wert für die linke Tabelle",selectRightTable:"Wählen Sie den richtigen Tabellenwert",selectRelation:"Wählen Sie die Beziehung für Tabellen aus",selectLeftColumn:"Wählen Sie den linken Spaltenwert der Feldzeile#",selectRightColumn:"Wählen Sie den rechten Spaltenwert der Feldzeile #",selectOperator:"Wählen Sie den Operator der Feldzeile aus #",relationExists:"Es besteht bereits eine Beziehung zwischen Tabellen"}},credentialDialog:{title:"Anmeldedialog",userName:"Nutzername",password:"Passwort",userNameWaterMark:"Nutzername",passwordWaterMark:"Passwort",userNameErrorMessage:"Bitte geben sie einen Benutzernamen ein",passwordErrorMessage:"Bitte Passwort eingeben",connect:"Verbinden",close:"Schließen"},queryExpression:{title:"Abfrage Ausdrücke",functionLabel:"Funktionen",columnLabel:"Spalteneinstellungen",expressionLabel:"Ausdruck",nameLabel:"Name",descriptionLabel:"Beschreibung ",exampleLabelText:"Beispiel",ok:"Sparen",cancel:"Stornieren",add:"HINZUFÜGEN",textAreaWaterMark:"Abfrageausdruck",nameFieldWaterMark:"Ausdrucksname",closeToolTip:"Schließen",errorMessage:{saveAlert:"Ausdruck wird nicht gespeichert. Willst du speichern und fortfahren? ",bracketSyntax:"Falsche Syntax in der Nähe von offenen / geschlossenen Klammern.",parseAlert:"Der Berichts-Designer konnte den angegebenen Ausdruck nicht analysieren.",nameAlert:"Name Feld sollte nicht leer sein.",emptyAlert:"Das Ausdrucksfeld darf nicht leer sein",duplicateName:"Der angegebene Ausdrucksname ist bereits vorhanden",specialCharacter:"Der Ausdrucksname darf keine Sonderzeichen enthalten.",referenceError:"Die Spalte kann nicht innerhalb ihres eigenen Ausdrucks angegeben werden!",invalidSyntax:"Ungültige Syntax in der Nähe von offenen / geschlossenen Klammer (n).",retrieveExpression:"Berichts Designer konnte den angegebenen Ausdruck nicht abrufen."},datasetTitle:"Datensatz",expressions:{all:"Alle",numbers:"Zahlen",logical:"Logisch",conditional:"Bedingt",date:"Datum",stringType:"Schnur",text:"Text",miscellenuous:"Sonstiges ",abs:"Gibt den absoluten Wert des angegebenen Ausdrucks zurück.",acos:"Gibt den inversen Kosinus (auch Arccosinus genannt) des angegebenen numerischen Ausdrucks zurück.",asin:"Gibt den inversen Sinus (auch als arcsine bezeichnet) des angegebenen numerischen Ausdrucks zurück.",atan:"Gibt den umgekehrten Tangens (auch Arkustangens genannt) des angegebenen numerischen Ausdrucks zurück.",cos:"Gibt den Kosinus des Winkels zurück, der im Bogenmaß des angegebenen Ausdrucks angegeben ist.",degree:"Gibt den Winkel in Grad für den Wert zurück, der im Bogenmaß den angegebenen numerischen Ausdruck angibt.",exponent:"Gibt den Exponentialwert des angegebenen Ausdrucks zurück.",logrithm:"Gibt den Logarithmus des angegebenen Ausdrucks an die angegebene Basis zurück.",pi:"Gibt den konstanten Wert von PI zurück.",power:"Gibt den Wert des angegebenen Ausdrucks (Ausdruck1) an die angegebene Stärke zurück (Ausdruck2).",radians:"Gibt den Winkel im Bogenmaß für den Winkel zurück, der im angegebenen numerischen Ausdruck in Grad angegeben wurde.",round:"Gibt einen gerundeten Wert zurück.",sign:"Gibt einen Wert zurück, der das positive (+1), Null (0) oder negative (-1) Vorzeichen des angegebenen numerischen Ausdrucks darstellt.",sin:"Gibt den Sinus des Winkels zurück, der im Bogenmaß des angegebenen Ausdrucks angegeben ist.",squareRoot:"Gibt die Quadratwurzel des angegebenen numerischen Ausdrucks zurück.",tan:"Gibt den Tangens des angegebenen numerischen Ausdrucks zurück.",ifCondition:"Gibt abhängig von der Auswertung des Ausdrucks entweder einen wahren Teil oder einen falschen Teil zurück.",ifNull:"Wenn der Ausdruck numerisch / Zeichenfolge / Datum ist, wird der erste Ausdruck zurückgegeben. Wenn der erste Ausdruck NULL ist, wird der zweite Ausdruck zurückgegeben.",isNotNull:"Wenn der Wert numeric / string / date_expression NULL ist, wird eine Zeichenfolge zurückgegeben; die false darstellt. Andernfalls wird true angegeben.",isNull:"Wenn der Wert numeric / string / date_expression NULL ist, wird eine Zeichenfolge zurückgegeben, die 'true' darstellt; andernfalls 'false'.",and:"Gibt true zurück, wenn beide Ausdrücke als wahr ausgewertet werden.",notOperation:"Gibt den logischen Umkehrwert des auszuwertenden Ausdrucks zurück.",orOperation:"Gibt true zurück, wenn einer der Ausdrücke als wahr ausgewertet wird.",addDate:"Fügt dem angegebenen Datum die Anzahl der Tage hinzu.",name:"Gibt eine Zeichenfolge zurück, die den angegebenen Datumsteil des angegebenen Datumsausdrucks darstellt.",part:"Gibt einen ganzzahligen Wert zurück, der den angegebenen Datumsteil des angegebenen Datumsausdrucks darstellt.",sub:"Gibt das vom angegebenen Datum abgezogene Datum zurück.",day:"Gibt einen numerischen Wert zurück, der den Tagteil des angegebenen Datums darstellt.",daydiff:"Gibt einen numerischen Wert zurück, der die Differenz zwischen zwei angegebenen Datumsangaben darstellt.",hour:"Gibt die Stunde des angegebenen Datums als Ganzzahl zurück.",minute:"Gibt einen numerischen Wert zurück, der den Minutenabschnitt des Datums darstellt, der sich aus dem angegebenen Datumsausdruck ergibt.",month:"Gibt einen numerischen Wert zurück, der den Monatsteil des Datums darstellt, der sich aus dem angegebenen Datumsausdruck ergibt.",now:"Gibt das aktuelle Datum und die Uhrzeit zurück.",today:"Gibt das aktuelle Datum zurück.",year:"Gibt einen numerischen Wert zurück, der den Jahresteil des Datums darstellt, der sich aus dem angegebenen Datumsausdruck ergibt.",char:"Konvertiert den angegebenen Ganzzahl-ASCII-Code in ein Zeichen.",concat:"Gibt einen Zeichenfolgenwert zurück, der aus der Verkettung von zwei oder mehr Zeichenfolgenwerten resultiert.",contains:"Gibt true zurück, wenn der angegebene Zeichenfolgenausdruck den angegebenen Ausdruck der Unterzeichenfolge enthält.",endsWith:"Gibt true zurück, wenn der angegebene Zeichenfolgenausdruck mit dem angegebenen substring-Ausdruck endet.",left:"Gibt die angegebene Anzahl von Zeichen ab dem Beginn des angegebenen Zeichenfolgenausdrucks zurück.",length:"Gibt den natürlichen Logarithmus des angegebenen Ausdrucks zurück.",lower:"Gibt einen konvertierten Kleinbuchstaben aus dem angegebenen Zeichenfolgenausdruck zurück.",leftTrim:"Gibt den Zeichenfolgenwert mit führenden Leerzeichen aus dem Zeichenfolgenausdruck zurück.",maximum:"Gibt den maximalen Wert im angegebenen Ausdruck zurück.",minimum:"Gibt den Mindestwert im angegebenen Ausdruck zurück.",right:"Gibt die angegebene Anzahl von Zeichen vom Ende des angegebenen Zeichenfolgenausdrucks zurück.",rightTrim:"Gibt die Zeichenfolge ohne rechte nachstehende Leerzeichen in der angegebenen Zeichenfolge zurück.",startswith:"Gibt 'true' zurück, wenn die angegebenen Zeichenfolgenausdrücke mit dem angegebenen Ausdruck der Unterzeichenfolge beginnen.",subString:"Gibt eine bestimmte Länge der Zeichenfolge zurück, beginnend mit dem spezifischen Index des angegebenen Zeichenfolgenausdrucks.",upper:"Gibt einen in Großbuchstaben umgewandelten Zeichenfolgenwert aus einem angegebenen Zeichenfolgenausdruck zurück."}},reportParameter:{title:"Parameter",descriptionText:"Berichtsparameter",addText:"HINZUFÜGEN",ok:"OK",cancel:"Stornieren",nameWaterMark:"Parametername",valueWaterMark:"Wert",closeToolTip:"Schließen"}},chartItem:{categoryItems:{yvalue:"Y Wert(s)",size:"Größe(s)",xvalue:"X Wert(s)",column:"Säule",row:"Reihe(s)"},categoryItemsMenu:{filter:"Filter",sort:"Sortiert",group:"Gruppen",expression:"Ausdruck",aggregate:"Aggregat"}},codeDialog:{title:"Code-Modul",ok:"OK",cancel:"Stornieren",add:"HINZUFÜGEN",closeToolTip:"Schließen",reference:{title:"Verweise",waterMark:"Referenz",errorMessage:"Das Feld ist leer",headerText:"Liste der Montagereferenzen",infoTipText:"Fügen Sie eine Assemblyreferenz hinzu, um Ihre Assemblyfunktionen im Bericht zu verwenden."},classes:{title:"Klassen",classWaterMark:"Klassenname",instanceWaterMark:"Instanzname",classErrorMessage:"Die Felder sind leer",instanceErrorMessage:"Das Feld ist leer",headerText:"Liste der Klasseninstanzen",infoTipText:"Fügen Sie Klasseninstanzen hinzu, um auf Ihre Objektfunktionen im Bericht zuzugreifen."},code:{title:"Code",headerText:"VB-Code-Funktion für den Bericht",infoTipText:"Die Syncfusion Reporting Engine unterstützt VB - Code - Funktionen zur Integration mit Berichtselementen und Daten."}},previewData:{title:"Datenvorschau",ok:"OK",cancel:"Stornieren",description:"Binden Sie JSON-Daten für die Vorschau",close:"Schließen",infoToolTip:"Der Bericht erfordert eine Vorschau der Daten im JSON-Format und enthält den Schlüssel und den Wert in der Liste des Array-Formats.",jsonHeader:"JSON-Daten:",errorMessage:"Geben Sie das gültige JSON-Format an",previewDataAlert:{title:"Datenvorschau",alertMessage:"Möchten Sie zum Report Designer wechseln ?"}},sampleDataSource:{sampleDSHeader:"BEISPIELDATEN IMPORTIEREN",addText:"Hinzufügen",searchText:"Suche",noDataFound:"Keine Daten gefunden.",welcomeContentPrefix:"Beginnen Sie mit dem Erstellen einer Datenquelle",welcomeContentSuffix:"Sie können eine Verbindung zu Ihren eigenen benutzerdefinierten Daten herstellen oder aus den vordefinierten freigegebenen Daten, die wir anbieten, importieren.",sampleDSText:"Beispieldaten importieren",exploreSampleText:"Beispieldaten untersuchen",accordionText:"Starten Sie den ersten Bericht und erkunden Sie die Anpassungsoptionen anhand der Beispieldaten.",errorMessage:"Netzwerkfehler",alertHeaderText:"Daten importieren",alertMessage:"Der Berichts-Designer konnte die Daten nicht vom Berichtsserver importieren"},field:{title:"Felder",nameWaterMark:"Feldname",sourceWaterMark:"Feldquelle",ok:"OK",cancel:"Stornieren",description:"Abfrage und berechnete Felder ändern",query:"Abfragefeld",calculated:"Berechnetes Feld",fieldError:"Feld ist leer",fieldsError:"Felder sind leer",add:"HINZUFÜGEN",closeToolTip:"Schließen",invalidFormat:"Der Feldname darf keine Leerzeichen und Sonderzeichen enthalten",sameFieldName:"Der Feldname existiert bereits"},commonProperty:{commonProperties:"Gemeinsame Eigenschaften",basicSettings:{categoryBasicSettings:"Grundeinstellungen",borderTypes:{border:"Rand",borderLeft:"Links",borderTop:"oben",borderRight:"Recht",borderBottom:"Unterseite"},borderStyles:{solid:"Solide",none:"Keiner",double:"Doppelt",dashed:"Gestrichelt",dotted:"Gepunktet"},backGround:"Hintergrundfarbe",styleTooltip:"Stil",colorTooltip:"Farbe",sizeTooltip:"Größe"},position:{categoryPosition:"Position",positionLabel:"Position",left:"Links",top:"Oben"},visibility:{categoryVisibility:"Sichtweite",visible:"Sichtbar"}}};
