﻿using Admin.Business;
using Admin.Data.Model;
using Admin.WebApi;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Admin.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TenantsController : ControllerBase
    {
        private ILogger<TenantsController> logger;
        private ITenantsBusiness tenantsBusiness;
        private IConfiguration configuration;
        private IRolesBusiness rolesBusiness;
        private IUsersBusiness usersBusiness;
        private IAppointmentCategoriesBusiness appointmentCategoriesBusiness;

        public TenantsController(ITenantsBusiness tenantsBusiness, IAppointmentCategoriesBusiness appointmentCategoriesBusiness, IRolesBusiness rolesBusiness, IUsersBusiness usersBusiness, ILogger<TenantsController> logger, IConfiguration configuration)
        {
            try
            {
                this.tenantsBusiness = tenantsBusiness;
                this.appointmentCategoriesBusiness = appointmentCategoriesBusiness;
                this.rolesBusiness = rolesBusiness;
                this.configuration = configuration;
                this.usersBusiness = usersBusiness;
                this.logger = logger; 
            }
            catch (Exception ex)
            {
                this.logger!.LogError(ex, ex.Message);
            }

        }

        /// <summary>
        /// Registers the Tenant which bought a subscription plan in Joomla website
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("register")]
        public async Task<ApiResponse> RegisterTenant([FromBody] RegisterTenantRequest registerTenantRequest)
        {
            try
            {
                //Validation
                if (registerTenantRequest == null)
                {
                    throw new Exception(GlobalResources.InvalidDataMessage);
                }

                if (ModelState.IsValid == false)
                {
                    throw new Exception(ModelState.ValidationState.ToString());
                }

              

                //Creates Tenant first.
                Tenant tenant = new Tenant();
                tenant.FullName = registerTenantRequest.FullName;
                tenant.JoomlaUserId = registerTenantRequest.JoomlaUserId;
                tenant.SubscriptionPlan = registerTenantRequest.SubscriptionPlan;
                tenant.SubscriptionPlanId = registerTenantRequest.SubscriptionPlanId;
                tenant.DateCreated = DateTime.Now;
                tenant.ObjectState = Data.ObjectState.Added;
                tenant = await this.tenantsBusiness.CreateOrUpdateTenant(tenant);

                //Create all Roles
                await this.rolesBusiness.CreateRolesForTenant(tenant.TenantId);

                //Create all AppointmentCategories
                await this.appointmentCategoriesBusiness.CreateAppointmentCategoriesForTenant(tenant.TenantId);

                //Creates the SuperAdmin user
                List<Role> roles = await this.rolesBusiness.GetAllRoles(tenant.TenantId);
                User user = new User();
                user.TenantId = tenant.TenantId;
                user.Username = registerTenantRequest.UserName;
                user.FullName = registerTenantRequest.FullName;
                //user.Email=registerTenantRequest.
                user.IsDoctor = true;  //Βάζουμε ότι είναι γιατρός με το που δημιουργείται ο Super Admin, γιατί αλλιώς δεν θα βλέπει τίποτα στο ημερολόγιο και θα μπερδευτεί.
                user.Password = "";
                user.RoleId = roles.Where(x => x.Name == "SuperAdmin").First().RoleId;
                await this.usersBusiness.CreateOrUpdateUser(user);

                //Διαβάζει τα δεδομένα.
                //ApiResponse<Tenant> createdTenantResponse = await this.GetTenant(tenant.TenantId.ToString(), "333");

                //Response
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Ok };
            }
            catch (Exception ex)
            {
                //logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        /// <summary>
        /// Creates the Tenant.
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("create")]
        public async Task<ApiResponse> CreateTenant([FromBody] Tenant tenant)
        {
            try
            {
                //Validation
                if (tenant == null)
                {
                    throw new Exception(GlobalResources.InvalidDataMessage);
                }

                if (ModelState.IsValid == false)
                {
                    throw new Exception(ModelState.ValidationState.ToString());
                }

                //Query
                //Tenant tenant = new Tenant();
                tenant.DateCreated = DateTime.Now;
                tenant.ObjectState = Data.ObjectState.Added;
                await this.tenantsBusiness.CreateOrUpdateTenant(tenant);

                //Διαβάζει τα δεδομένα.
                //ApiResponse<Tenant> createdTenantResponse = await this.GetTenant(tenant.TenantId.ToString(), "333");

                //Response
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Ok };
            }
            catch (Exception ex)
            {
                //logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }



        //[HttpGet]
        //[Route("{id}")]
        //public async Task<ApiResponse<Tenant>> GetTenant(string id, string pwd)
        //{
        //    try
        //    {
        //        //Query
        //        Tenant? tenant = await  this.tenantsBusiness.GetTenant(Guid.Parse(id));



        //        //Response
        //        return new ApiResponse<Tenant>() { ResultCode = ApiResponseResultCode.Ok, Data = tenantDto };
        //    }
        //    catch (Exception exp)
        //    {
        //        //ExceptionHandler.RecordException(exp);
        //        return new ApiResponse<Tenant>() { ResultCode = ApiResponseResultCode.Exception, Exception = exp };
        //    }
        //}

        //[HttpGet]
        //[Route("{username}")]
        //public async Task<ApiResponse<Tenant>> SetLastLoggedUserInfo(string username, DateTime loggedDateTime)
        //{
        //    try
        //    {
        //        //Query
        //        Tenant? tenant = await this.tenantsBusiness.GetTenant(username);

        //        tenant.LastLoggedUserDate = loggedDateTime;
        //        tenant.LastLoggedUser = 

        //        //Response
        //        return new ApiResponse<Tenant>() { ResultCode = ApiResponseResultCode.Ok, Data = tenantDto };
        //    }
        //    catch (Exception exp)
        //    {
        //        //ExceptionHandler.RecordException(exp);
        //        return new ApiResponse<Tenant>() { ResultCode = ApiResponseResultCode.Exception, Exception = exp };
        //    }
        //}

        private async Task<ApiResponse<Tenant>> GetTenantByUsername(string username)
        {
            try
            {
                //Query
                Tenant? tenant = await this.tenantsBusiness.GetTenant(username);


                //Response
                return new ApiResponse<Tenant>() { ResultCode = ApiResponseResultCode.Ok, Data = tenant };
            }
            catch (Exception exp)
            {
                //ExceptionHandler.RecordException(exp);
                return new ApiResponse<Tenant>() { ResultCode = ApiResponseResultCode.Exception, Exception = exp };
            }
        }


    }
}
