﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace Admin.Data.Model
{
    public partial class MentalViewContext
    {
        private string connectionString = string.Empty;

        public MentalViewContext(DbContextOptions<MentalViewContext> options) : base(options)
        {
            ChangeTracker.LazyLoadingEnabled = false;
        }

        public MentalViewContext(string connectionString) : base()
        {
            //base.OnConfiguring(new DbContextOptionsBuilder().UseSqlServer(connectionString));
            this.connectionString = connectionString;
            ChangeTracker.LazyLoadingEnabled = false;
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                if (string.IsNullOrEmpty(connectionString))
                {
                    //optionsBuilder.UseSqlServer("Server=MAIN\\SQLEXPRESS;Database=FluentBlue;Trusted_Connection=True;TrustServerCertificate=True");
                    optionsBuilder.UseSqlServer("Name=MentalViewConnectionString");
                }
                else
                {
                    optionsBuilder.UseSqlServer(connectionString);
                }
            }
        }
    }
}
