/*!
*  filename: ej.draggable.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./ej.core.min"],n):n()})(function(){(function(n,t,i){t.widget("ejDraggable","ej.Draggable",{element:null,model:null,validTags:["div","span","a"],defaults:{scope:"default",handle:null,dragArea:null,clone:!1,distance:1,dragOnTaphold:!1,cursorAt:{top:-1,left:-2},dragStart:null,drag:null,dragStop:null,create:null,destroy:null,autoScroll:!1,scrollSensitivity:20,scrollSpeed:20,helper:function(){return n('<div class="e-drag-helper" />').html("draggable").appendTo(document.body)}},_init:function(){this.handler=function(){};this.resizables={};this._wireEvents();this._browser=t.browserInfo();this._isIE8=this._browser.name=="msie"&&this._browser.version=="8.0";this._isIE9=this._browser.name=="msie"&&this._browser.version=="9.0";this._browser.name=="msie"&&this.element.addClass("e-pinch");this._browser.name=="edge"&&this.element.css("touch-action","none")},_setModel:function(n){for(var t in n)switch(t){case"dragArea":this.model.dragArea=n[t];break;case"dragOnTaphold":this.model.dragOnTaphold=n[t];break;case"autoScroll":this.model.autoScroll=n[t]}},_destroy:function(){n(document).off(t.eventType.mouseUp,this._destroyHandler).off(t.eventType.mouseUp,this._dragStopHandler).off(t.eventType.mouseMove,this._dragStartHandler).off(t.eventType.mouseMove,this._dragHandler).off("mouseleave",this._dragMouseOutHandler).off("selectstart",!1);t.widgetBase.droppables[this.scope]=null},_initialize:function(i){var r,u;if(i.target&&i.target.nodeName&&n(i.target).closest("input[type='text'], input[type='checkbox'], textarea, select, option").length)return!0;r=i;i.preventDefault();i=this._getCoordinate(i);this.target=n(r.currentTarget);this._initPosition={x:i.pageX,y:i.pageY};n(document).on(t.eventType.mouseMove,this._dragStartHandler).on(t.eventType.mouseUp,this._destroyHandler);this.model.clone||(u=this.element.offset(),this._relXposition=i.pageX-u.left,this._relYposition=i.pageY-u.top);n(document.documentElement).trigger(t.eventType.mouseDown,r)},_setDragArea:function(){var o=n(this.model.dragArea)[0],s,h,u,r,f,e,i;if(o){if(r=["left","right","bottom","top"],t.isNullOrUndefined(o.getBoundingClientRect)){for(s=n(this.model.dragArea).outerWidth(),h=n(this.model.dragArea).outerHeight(),i=0;i<r.length;i++)this["border-"+r[i]+"-width"]=0,this["padding-"+r[i]]=0;f=e=0}else{for(u=o.getBoundingClientRect(),s=u.width?u.width:u.right-u.left,h=u.height?u.height:u.bottom-u.top,i=0;i<r.length;i++)this["border-"+r[i]+"-width"]=isNaN(parseFloat(n(n(this.model.dragArea)[0]).css("border-"+r[i]+"-width")))?0:parseFloat(n(n(this.model.dragArea)[0]).css("border-"+r[i]+"-width")),this["padding-"+r[i]]=isNaN(parseFloat(n(n(this.model.dragArea)[0]).css("padding-"+r[i])))?0:parseFloat(n(n(this.model.dragArea)[0]).css("padding-"+r[i]));f=n(this.model.dragArea).offset().top;e=n(this.model.dragArea).offset().left}this._left=t.isNullOrUndefined(n(this.model.dragArea).offset())?0+this["border-left-width"]+this["padding-left"]:e+this["border-left-width"]+this["padding-left"];this._top=t.isNullOrUndefined(n(this.model.dragArea).offset())?0+this["border-top-width"]+this["padding-top"]:f+this["border-top-width"]+this["padding-top"];this._right=e+s-[this["border-right-width"]+this["padding-right"]];this._bottom=f+h-[this["border-bottom-width"]+this["padding-bottom"]]}},_dragStart:function(r){var a,u,o,e,f,s,h;if(r.type=="touchmove"||r.type=="mousemove"&&(r.buttons!==i?r.buttons:r.which)==1||this._isIE8||this._isIE9){u=r;r=this._getCoordinate(r);this.margins={left:parseInt(this.element.css("marginLeft"),10)||0,top:parseInt(this.element.css("marginTop"),10)||0,right:parseInt(this.element.css("marginRight"),10)||0,bottom:parseInt(this.element.css("marginBottom"),10)||0};this.offset=this.element.offset();this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-this.margins.left};this.position=this._getMousePosition(u);var c=this._initPosition.x-r.pageX,l=this._initPosition.y-r.pageY,v=Math.sqrt(c*c+l*l);if(v>=this.model.distance){if(o=this.model.helper({sender:u,element:this.target}),!o||t.isNullOrUndefined(o))return;if(e=this.model.handle=this.helper=o,this.model.dragStart&&(f=null,u.type=="touchmove"?(s=u.originalEvent.changedTouches[0],f=document.elementFromPoint(s.clientX,s.clientY)):f=u.originalEvent.target||u.target,this.model.cursorAt.top==0&&this.model.cursorAt.left==0&&(f=this._checkTargetElement(r)||f),this._trigger("dragStart",{event:u,element:this.element,target:f,currentTarget:this._getCurrTarget(r)})))return this._destroy(),!1;if(this.model.dragArea?this._setDragArea():(this._left=this._top=this._right=this._bottom=0,this["border-top-width"]=this["border-left-width"]=0),!t.isNullOrUndefined(e)&&e.length>0){h=e.offsetParent().offset();n(document).off(t.eventType.mouseMove,this._dragStartHandler).off(t.eventType.mouseUp,this._destroyHandler).on(t.eventType.mouseMove,this._dragHandler).on(t.eventType.mouseUp,this._dragStopHandler).on("mouseleave",this._dragMouseOutHandler).on("selectstart",!1);t.widgetBase.droppables[this.model.scope]={draggable:this.element,helper:e.css({position:"absolute",left:this.position.left-h.left,top:this.position.top-h.top}),destroy:this._destroyHandler}}}}(this.model.autoScroll&&r.type=="touchmove"||r.type=="mousemove"&&(r.buttons!==i?r.buttons:r.which)==1||this._isIE8||this._isIE9)&&(a=this._getScrollParent(u.target))},_drag:function(i){var f,e,s,h,r,l,v,u,a,o,c;i.preventDefault();this.position=this._getMousePosition(i);this.position.top<0&&(this.position.top=0);n(document).height()<this.position.top&&(this.position.top=n(document).height());n(document).width()<this.position.left&&(this.position.left=n(document).width());o=t.widgetBase.droppables[this.model.scope].helper;this.model.drag&&(l=null,i.type=="touchmove"?(v=i.originalEvent.changedTouches[0],l=document.elementFromPoint(v.clientX,v.clientY)):l=i.originalEvent.target||i.target,this.model.cursorAt.top==0&&this.model.cursorAt.left==0&&(l=this._checkTargetElement(i)||l),u={event:i,element:this.target,target:l,currentTarget:this._getCurrTarget(i),position:{left:null,top:null}},this._trigger("drag",u));a=this._checkTargetElement(i);t.isNullOrUndefined(a)?this._hoverTarget&&(i.target=i.toElement=this._hoverTarget,this._hoverTarget.object._out(i),this._hoverTarget=null):(i.target=i.toElement=a,a.object._over(i),this._hoverTarget=a);o=t.widgetBase.droppables[this.model.scope].helper;c=o.offsetParent().offset();s=t.isNullOrUndefined(i.pageX)||i.pageX===0&&i.type=="touchmove"?i.originalEvent.changedTouches[0].pageX:i.pageX;h=t.isNullOrUndefined(i.pageY)||i.pageY===0&&i.type=="touchmove"?i.originalEvent.changedTouches[0].pageY:i.pageY;this.model.dragArea?(this._pageX!=s&&(f=this._left>this.position.left?this._left:this._right<this.position.left+o.outerWidth(!0)?this._right-o.outerWidth(!0):this.position.left),this._pageY!=h&&(e=this._top>this.position.top?this._top:this._bottom<this.position.top+o.outerHeight(!0)?this._bottom-o.outerHeight(!0):this.position.top)):(f=this.position.left,e=this.position.top);(e<0||e-[c.top+this["border-top-width"]]<0)&&(e=[c.top+this["border-top-width"]]);(f<0||f-[c.left+this["border-left-width"]]<0)&&(f=[c.left+this["border-left-width"]]);f=u&&u.position&&u.position.left?u.position.left:f;e=u&&u.position&&u.position.top?u.position.top:e;o.css({left:u&&u.position&&u.position.left?f:f-[c.left+this["border-left-width"]],top:u&&u.position&&u.position.top?e:e-[c.top+this["border-top-width"]]});this.position.left=f;this.position.top=e;this._pageX=s;this._pageY=h;this.model.autoScroll&&(r&&r!=document&&r.tagName!="HTML"?(n(r).offset().top+r.clientHeight-h<this.model.scrollSensitivity?r.scrollTop=r.scrollTop+this.model.scrollSpeed:h-n(r).offset().top<this.model.scrollSensitivity&&(r.scrollTop=r.scrollTop-this.model.scrollSpeed),n(r).offset().left+r.clientWidth-s<this.model.scrollSensitivity?r.scrollLeft=r.scrollLeft+this.model.scrollSpeed:s-n(r).offset().left<this.model.scrollSensitivity&&(r.scrollLeft=r.scrollLeft+this.model.scrollSpeed)):(h-n(document).scrollTop()<this.model.scrollSensitivity?n(document).scrollTop(n(document).scrollTop()-this.model.scrollSpeed):n(window).height()-(h-n(document).scrollTop())<this.model.scrollSensitivity&&n(document).scrollTop(n(document).scrollTop()+this.model.scrollSpeed),s-n(document).scrollLeft()<this.model.scrollSensitivity?n(document).scrollLeft(n(document).scrollLeft()-this.model.scrollSpeed):n(window).width()-(s-n(document).scrollLeft())<this.model.scrollSensitivity&&n(document).scrollLeft(n(document).scrollLeft()+this.model.scrollSpeed)))},_dragStop:function(n){var t,i;(n.type=="mouseup"||n.type=="touchend")&&this._destroy(n);this.model.dragStop&&(t=null,n.type=="touchend"?this.model.cursorAt.top==0&&this.model.cursorAt.left==0?t=n.originalEvent.target||n.target:(i=n.originalEvent.changedTouches[0],t=document.elementFromPoint(i.clientX,i.clientY)):t=n.originalEvent.target||n.target,this.model.cursorAt.top==0&&this.model.cursorAt.left==0&&(t=this._checkTargetElement(n)||t),this._trigger("dragStop",{event:n,element:this.target,target:t,currentTarget:this._getCurrTarget(n)}));this._dragEnd(n)},_dragEnd:function(n){var i=this._checkTargetElement(n);t.isNullOrUndefined(i)||(n.target=n.toElement=i,i.object._drop(n,this.element))},_dragMouseEnter:function(t){n(document).off("mouseenter",this._dragMouseEnterHandler);this._isIE9?this._dragManualStop(t):this._isIE8?t.button==0&&this._dragManualStop(t):t.buttons==0&&this._dragManualStop(t)},_dragManualStop:function(n){this.model.dragStop!=null&&this._trigger("dragStop",{event:n,element:this.target,target:n.originalEvent.target||n.target,currentTarget:this._getCurrTarget(n)});this._destroy(n)},_dragMouseOut:function(){n(document).on("mouseenter",this._dragMouseEnterHandler)},_checkTargetElement:function(n){var t,i;return(n.type=="touchmove"||n.type=="touchstart"||n.type=="touchend"||n.type=="taphold"?(i=n.originalEvent.changedTouches[0],t=document.elementFromPoint(i.clientX,i.clientY)):t=n.target,this.helper&&this._contains(this.helper[0],t))?(this.helper.hide(),t=this._elementUnderCursor(n),this.helper.show(),this._withDropElement(t)):this._withDropElement(t)},_getCurrTarget:function(n){var i=n.originalEvent&&n.originalEvent.target||n.target,u,r;if(!t.isNullOrUndefined(i.style))return u=i.style.display,this.element.is(i)&&(i.style.display="none"),r=null,t.isNullOrUndefined(n.pageX)||t.isNullOrUndefined(n.pageY)||(r=document.elementFromPoint(n.pageX,n.pageY)),i.style.display=u,r},_withDropElement:function(i){if(i){var r=n(i).data("ejDroppable");if(t.isNullOrUndefined(r)&&(r=this._checkParentElement(n(i))),!t.isNullOrUndefined(r))return n.extend(i,{object:r})}},_checkParentElement:function(i){var u=n(i).closest(".e-droppable"),r;if(u.length>0&&(r=n(u).data("ejDroppable"),!t.isNullOrUndefined(r)))return r},_elementUnderCursor:function(n){return n.type=="touchmove"||n.type=="touchstart"||n.type=="touchend"||n.type=="taphold"?document.elementFromPoint(n.originalEvent.changedTouches[0].clientX,n.originalEvent.changedTouches[0].clientY):document.elementFromPoint(n.clientX,n.clientY)},_contains:function(t,i){try{return n.contains(t,i)||t==i}catch(r){return!1}},_wireEvents:function(){t.isDevice()==!0&&this.model.dragOnTaphold==!0?this._on(this.element,"taphold",this._initialize):this._on(this.element,t.eventType.mouseDown,this._initialize);this._dragStartHandler=n.proxy(this._dragStart,this);this._destroyHandler=n.proxy(this._destroy,this);this._dragStopHandler=n.proxy(this._dragStop,this);this._dragHandler=n.proxy(this._drag,this);this._dragMouseEnterHandler=n.proxy(this._dragMouseEnter,this);this._dragMouseOutHandler=n.proxy(this._dragMouseOut,this)},_getMousePosition:function(n){n=this._getCoordinate(n);var t=this.model.clone?n.pageX:n.pageX-this._relXposition,i=this.model.clone?n.pageY:n.pageY-this._relYposition;return{left:t-[this.margins.left+this.model.cursorAt.left],top:i-[this.margins.top+this.model.cursorAt.top]}},_getCoordinate:function(n){var i=n;return(n.type=="touchmove"||n.type=="touchstart"||n.type=="touchend"||n.type=="taphold"&&t.browserInfo().name!="msie")&&(i=n.originalEvent.changedTouches[0]),i},_getScrollParent:function(n){return n&&n.scrollHeight>n.clientHeight?n:n&&n.parentNode?this._getScrollParent(n.parentNode):void 0}})})(jQuery,Syncfusion),function(n,t){t.widget("ejDroppable","ej.Droppable",{element:null,model:null,validTags:["div","span","a"],dropElements:[],defaults:{accept:null,scope:"default",drop:null,over:null,out:null,create:null,destroy:null},_init:function(){this._mouseOver=!1;this.dropElements.push(this)},_setModel:function(){},_destroy:function(){n(this.element).off("mouseup",n.proxy(this._drop,this))},_over:function(n){this._mouseOver||(this._trigger("over",n),this._mouseOver=!0)},_out:function(n){this._mouseOver&&(this._trigger("out",n),this._mouseOver=!1)},_drop:function(t,i){var u=t.target,f=n(u).parents(".e-droppable"),r;for(n(u).hasClass("e-droppable")&&f.push(u),r=0;r<this.dropElements.length;r++)n(f).is(n(this.dropElements[r].element))&&this.dropElements[r]._dropEvent.call(this.dropElements[r],t,i)},_dropEvent:function(i,r){var u=t.widgetBase.droppables[this.model.scope],f=!t.isNullOrUndefined(u.helper)&&u.helper.is(":visible"),e;f&&i.type=="touchend"&&n(u.helper).hide();e=this._isDropArea(i);f&&i.type=="touchend"&&n(u.helper).show();u&&!t.isNullOrUndefined(this.model.drop)&&f&&e.canDrop&&this.model.drop(n.extend(i,{dropTarget:e.target,dragElement:r},!0),u)},_isDropArea:function(t){var i={canDrop:!0,target:n(t.target)},e,r,o,u,f;if(t.type=="touchend")for(e=t.originalEvent.changedTouches[0],r=document.elementFromPoint(e.clientX,e.clientY),i.canDrop=!1,o=n(r).parents(),u=0;u<this.element.length;u++){if(n(r).is(n(this.element[u])))i={canDrop:!0,target:n(r)};else for(f=0;f<o.length;f++)if(n(this.element[u]).is(n(o[f]))){i={canDrop:!0,target:n(r)};break}if(i.canDrop)break}return i}})}(jQuery,Syncfusion),function(n,t){t.widget("ejResizable","ej.resizable",{element:null,model:null,validTags:["div","span","a"],defaults:{scope:"default",handle:null,distance:1,maxHeight:null,maxWidth:null,minHeight:10,minWidth:10,cursorAt:{top:1,left:1},resizeStart:null,resize:null,resizeStop:null,create:null,destroy:null,helper:function(){return n('<div class="e-resize-helper" />').html("resizable").appendTo(document.body)}},_init:function(){if(this.target=this.element,this._browser=t.browserInfo(),this._isIE8=this._browser.name=="msie"&&this._browser.version=="8.0",this._isIE9=this._browser.name=="msie"&&this._browser.version=="9.0",this.handle!=null)n(this.target).delegate(this.handle,t.eventType.mouseDown,n.proxy(this._mousedown,this)).delegate(this.handle,"resizestart",this._blockDefaultActions);else n(this.target).on(t.eventType.mouseDown,n.proxy(this._mousedown,this));this._resizeStartHandler=n.proxy(this._resizeStart,this);this._destroyHandler=n.proxy(this._destroy,this);this._resizeStopHandler=n.proxy(this._resizeStop,this);this._resizeHandler=n.proxy(this._resize,this);this._resizeMouseEnterHandler=n.proxy(this._resizeMouseEnter,this)},_mouseover:function(i){if(n(i.target).hasClass("e-resizable")){n(i.target).css({cursor:"se-resize"});n(this.target).on(t.eventType.mouseDown,n.proxy(this._mousedown,this))}else n(this.target).off(t.eventType.mouseDown),n(this.target).css({cursor:""})},_blockDefaultActions:function(n){n.cancelBubble=!0;n.returnValue=!1;n.preventDefault&&n.preventDefault();n.stopPropagation&&n.stopPropagation()},_setModel:function(){},_mousedown:function(i){var r=i;i=this._getCoordinate(i);this.target=n(r.currentTarget);this._initPosition={x:i.pageX,y:i.pageY};this._pageX=i.pageX;this._pageY=i.pageY;n(document).on(t.eventType.mouseMove,this._resizeStartHandler).on(t.eventType.mouseUp,this._destroyHandler);return n(document.documentElement).trigger(t.eventType.mouseDown,r),!1},_resizeStart:function(i){var r,h;if(n(i.target).hasClass("e-resizable")){i=this._getCoordinate(i);var u=this._initPosition.x-i.pageX,f=this._initPosition.y-i.pageY,e,o,s=Math.sqrt(u*u+f*f);if(s>=this.model.distance){if(this.model.resizeStart!=null&&this._trigger("resizeStart",{event:i,element:this.target}))return;r=this.model.helper({element:this.target});e=i.pageX-this._pageX+r.outerWidth();o=i.pageY-this._pageY+r.outerHeight();this._pageX=i.pageX;this._pageY=i.pageY;h=this.getElementPosition(r);n(document).off(t.eventType.mouseMove,this._resizeStartHandler).off(t.eventType.mouseUp,this._destroyHandler).on(t.eventType.mouseMove,this._resizeHandler).on(t.eventType.mouseUp,this._resizeStopHandler).on("mouseenter",this._resizeMouseEnterHandler).on("selectstart",!1);t.widgetBase.resizables[this.scope]={resizable:this.target,helper:r.css({width:e,height:o}),destroy:this._destroyHandler}}}},_resize:function(n){var i,r,u,e,f;n=this._getCoordinate(n);e=this.getElementPosition(t.widgetBase.resizables[this.scope].helper);f=this.model.helper({element:this.target});i=n.pageX-this._pageX+f.outerWidth();r=n.pageY-this._pageY+f.outerHeight();this._pageX=n.pageX;this._pageY=n.pageY;i<this.model.minWidth&&(u=this.model.minWidth-i,i=this.model.minWidth,this._pageX=n.pageX+u);r<this.model.minHeight&&(u=this.model.minHeight-r,r=this.model.minHeight,this._pageY=n.pageY+u);this.model.maxHeight!=null&&r>this.model.maxHeight&&(u=r-this.model.maxHeight,r=this.model.maxHeight,this._pageY=n.pageY-u);this.model.maxWidth!=null&&i>this.model.maxWidth&&(u=i-this.model.maxWidth,i=this.model.maxWidth,this._pageX=n.pageX-u);t.widgetBase.resizables[this.scope].helper.css({width:i,height:r});this._trigger("resize",{element:this.target})},_resizeStop:function(n){this.model.resizeStop!=null&&this._trigger("resizeStop",{element:this.target});(n.type=="mouseup"||n.type=="touchend")&&this._destroy(n)},_resizeMouseEnter:function(n){this._isIE9?this._resizeManualStop(n):this._isIE8?n.button==0&&this._resizeManualStop(n):n.buttons==0&&this._resizeManualStop(n)},_resizeManualStop:function(n){this.model.resizeStop!=null&&this._trigger("resizeStop",{element:this.target});this._destroy(n)},_destroy:function(){n(document).off(t.eventType.mouseUp,this._destroyHandler).off(t.eventType.mouseUp,this._resizeStopHandler).off(t.eventType.mouseMove,this._resizeStartHandler).off(t.eventType.mouseMove,this._resizeHandler).off("mouseenter",this._resizeMouseEnterHandler).off("selectstart",!1);t.widgetBase.resizables[this.scope]=null},getElementPosition:function(n){return n!=null&&n.length>0?{left:n[0].offsetLeft,top:n[0].offsetTop}:null},_getCoordinate:function(n){var t=n;return(n.type=="touchmove"||n.type=="touchstart"||n.type=="touchend")&&(t=n.originalEvent.changedTouches[0]),t}})}(jQuery,Syncfusion)});
