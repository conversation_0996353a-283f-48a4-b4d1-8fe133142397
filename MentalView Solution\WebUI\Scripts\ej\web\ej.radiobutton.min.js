/*!
*  filename: ej.radiobutton.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function(n,t){t.widget("ejRadioButton","ej.RadioButton",{_rootCSS:"e-radiobtn",element:null,_requiresID:!0,model:null,validTags:["input"],_addToPersist:["checked"],_setFirst:!1,angular:{require:["?ngModel","^?form","^?ngModelOptions"]},defaults:{id:null,name:null,value:null,checked:!1,cssClass:"",text:"",enableRTL:!1,htmlAttributes:{},enablePersistence:!1,idPrefix:"ej",size:"small",enabled:!0,validationRules:null,validationMessage:null,validationMessages:null,beforeChange:null,change:null,create:null,destroy:null},dataTypes:{id:"string",name:"string",enablePersistence:"boolean",size:"enum",enabled:"boolean",idPrefix:"string",validationRules:"data",validationMessage:"data",validationMessages:"data",htmlAttributes:"data"},observables:["checked"],checked:t.util.valueFunction("checked"),_init:function(n){var i=t.browserInfo();this._cloneElement=this.element.clone();this._isIE8=i.name=="msie"&&i.version=="8.0"?!0:!1;this._setValue();this._renderControl();this.isChecked&&this._checkedHandler();t.isNullOrUndefined(this.radbtn.attr("disabled"))||(this.model.enabled=!1);this._setEnabled(this.model.enabled);this._addAttr(this.model.htmlAttributes);t.isNullOrUndefined(n)||t.isNullOrUndefined(n.validationMessage)||(this.model.validationMessages=this.model.validationMessage);this.model.validationRules!=null&&(this._initValidator(),this._setValidation());this._wireEvents();this.initialRender=!1},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.div.addClass(n):t=="name"?i.radbtn.attr(t,n):t=="required"?i.radbtn.attr(t,n):t=="disabled"&&n=="disabled"?i.disable():t=="checked"&&n=="checked"?i._checkedChange(!0,!0):i.div.attr(t,n)})},_initValidator:function(){this.element.closest("form").data("validator")||this.element.closest("form").validate()},_setValidation:function(){var r,f,i,u,e;this.element.find("input").rules("add",this.model.validationRules);r=this.element.closest("form").data("validator");r=r?r:this.element.closest("form").validate();f=this.element.find("input").attr("name");r.settings.messages[f]={};for(i in this.model.validationRules)if(u=null,!t.isNullOrUndefined(this.model.validationRules[i])){if(t.isNullOrUndefined(this.model.validationRules.messages&&this.model.validationRules.messages[i])){r.settings.messages[f][i]=n.validator.messages[i];for(e in this.model.validationMessages)i==e?u=this.model.validationMessages[i]:""}else u=this.model.validationRules.messages[i];r.settings.messages[f][i]=u!=null?u:n.validator.messages[i]}},_setModel:function(t){for(var i in t)switch(i){case"cssClass":this._changeSkin(t[i]);break;case"enableRTL":this.model.text?t[i]?this.div.addClass("e-rtl"):this.div.removeClass("e-rtl")&&this.element.closest(".e-radiobtn-wrap").hasClass("e-rtl")?this.element.closest(".e-radiobtn-wrap").removeClass("e-rtl"):"":t[i]?this.element.closest(".e-radiobtn-wrap").addClass("e-rtl"):this.element.closest(".e-radiobtn-wrap").removeClass("e-rtl");break;case"text":this._setText(t[i]);break;case"size":this._setSize(t[i]);break;case"validationRules":this.model.validationRules!=null&&(this.element.find("input").rules("remove"),this.model.validationMessages=null);this.model.validationRules=t[i];this.model.validationRules!=null&&(this._initValidator(),this._setValidation());break;case"validationMessage":this.model.validationMessages=t[i];this.model.validationRules!=null&&this.model.validationMessages!=null&&(this._initValidator(),this._setValidation());break;case"validationMessages":this.model.validationMessages=t[i];this.model.validationRules!=null&&this.model.validationMessages!=null&&(this._initValidator(),this._setValidation());break;case"checked":n(this.div).removeClass("e-material-animate");typeof this.checked()=="boolean"?(this.model.checked=t[i],this._checkedChange(this.model.checked)):t[i]()!=null&&t[i]()==this.element.find(".e-input").attr("value")&&this._checkedChange(t[i]());break;case"enabled":this._setEnabled(t[i]);break;case"id":this._setIdAttr(t[i]);break;case"name":this.radbtn.attr("name",t[i]);break;case"value":this.radbtn.attr("value",t[i]);break;case"htmlAttributes":this._addAttr(t[i])}},_destroy:function(){this.radbtn.remove();this._cloneElement.removeClass("e-js e-input e-radiobtn");this._cloneElement.insertBefore(this.element);this.element.remove()},_changeSkin:function(n){this.model.cssClass!=n&&(this.element.removeClass(this.model.cssClass).addClass(n),this.div.removeClass(this.model.cssClass).addClass(n))},_setValue:function(){t.isNullOrUndefined(this.element.attr("type"))&&this.element.attr("type","radio");t.isNullOrUndefined(this.element.attr("id"))||(this.model.id=this.element.attr("id"));t.isNullOrUndefined(this.element.attr("name"))||(this.model.name=this.element.attr("name"));t.isNullOrUndefined(this.element.attr("value"))||(this.model.value=this.element.attr("value"));this.element.attr({id:this.model.id,name:this.model.name,value:this.model.value});typeof this.checked()=="boolean"?this.model.checked=this.isChecked=this.model.checked||this.element.attr("checked")=="checked":this.isChecked=this.element.attr("value")==this.checked();this.isChecked&&this.element.attr("checked","checked");this._hiddenValue=this.element.attr("value");this._isUI=!1},_setIdAttr:function(t){n("#"+this.model.idPrefix+this.model.id+"_wrapper").attr("id",this.model.idPrefix+t+"_wrapper");n("#"+this.model.idPrefix+this.model.id).attr("id",this.model.idPrefix+t);this.radbtn.attr("id",t)},_setSize:function(n){n==t.RadioButtonSize.Medium?(this.span.removeClass("e-radsmaller").addClass("e-radmedium"),this.div.removeClass("e-radsmall").addClass("e-radmed")):(this.span.removeClass("e-radmedium").addClass("e-radsmaller"),this.div.removeClass("e-radmed").addClass("e-radsmall"))},_setEnabled:function(n){n?this.enable():this.disable()},_renderControl:function(){this.initialRender=!0;var i=t.browserInfo();this.div=i.name=="msie"&&i.version=="8.0"?n('<div class="e-radiobtn-wrap e-widget e-rad-outer e-ie8" ><\/div>'):n('<div class="e-radiobtn-wrap e-widget" ><\/div>');this.div.attr({id:this.model.idPrefix+this.model.id,role:"radio",tabindex:0,"aria-checked":!1});this.span=n("<span><\/span>");this.span.addClass("e-spanicon");this._setSize(this.model.size);this.spanImg=n('<span class="e-rad-icon e-icon e-rad-select"><\/span>',"",{},{role:"presentation"});this.element.addClass("e-input");this.div.addClass(this.model.cssClass);this.span.append(this.spanImg);this.div.insertBefore(this.element);this.div.append(this.element);this.div.append(this.span);this._setTextWrapper(this.model.text);this.radbtn=this.element;this.element=this.div},_setTextWrapper:function(n){n!=""?(this.txtSpan=t.buildTag("div.e-text",n),this.div.append(this.txtSpan),this.model.enableRTL&&this.div.addClass("e-rtl")):this.model.enableRTL&&this.element.closest(".e-radiobtn-wrap").addClass("e-rtl")},_setText:function(n){this.model.text==""&&n!=""?this._setTextWrapper(n):this.txtSpan.html(n)},_wireEvents:function(){this._on(this.element,"click",function(n){this._isUI=!0;this._checkedHandler(n)});this._on(this.element,"focus",this._focusIn);this._on(this.element,"focusout",this._focusOut)},_focusIn:function(){n(this.element).addClass("e-focus");n(this.element).on("keydown",n.proxy(this._checkUnCheck,this))},_focusOut:function(){n(this.element).removeClass("e-focus");n(this.element).off("keydown",n.proxy(this._checkUnCheck,this))},_checkUnCheck:function(n){(n.keyCode==32||n.keyCode==37||n.keyCode==38||n.keyCode==39||n.keyCode==40)&&(n.preventDefault(),this._checkedHandler())},_checkedHandler:function(t){t&&(this._interacted=!0);this.element.hasClass("e-disable")||(this.isChecked=typeof this.checked()=="boolean"?this.radbtn.attr("checked")=="checked"?!0:!1:this.checked()==this.radbtn.attr("value"),n(this.element).find(".e-rad-icon").hasClass("e-circle_01")||this._changeEvent(!0),t&&n(this.div).addClass("e-material-animate"))},_checkedChange:function(n,t){this.isChecked=n;this._changeEvent(t)},_changeEvent:function(i){var u={isChecked:this.isChecked,isInteraction:!!i};if(!this.initialRender&&!0==this._trigger("beforeChange",u))return!1;if(n(this.element).find(".e-rad-icon").hasClass("e-circle_01"))this.spanImg.removeClass("e-circle_01").addClass("e-rad-select"),this.span.removeClass("e-rad-active"),this.div.attr({tabindex:0,"aria-checked":!1}),this.radbtn.removeAttr("checked");else{var f=this.element.find(".e-input").attr("name"),e=n('input.e-radiobtn[name="'+f+'"]:radio'),o=this,s=this.element.find(".e-input"),r=n(s).data("ejRadioButton");u.isChecked&&(this.spanImg.addClass("e-circle_01").removeClass("e-rad-select"),this.span.addClass("e-rad-active"),this.div.attr({tabindex:0,"aria-checked":!0}),this.radbtn.attr("checked","checked"));n.each(e,function(i,r){o._interacted&&n(n(r).closest(".e-widget")).find("span.e-spanicon").hasClass("e-rad-active")&&n(r).closest(".e-radiobtn-wrap").addClass("e-material-animate");n(r).closest(".e-radiobtn-wrap").find(".e-rad-icon").removeClass("e-circle_01").addClass("e-rad-select");n(r).closest(".e-radiobtn-wrap").find(".e-spanicon").removeClass("e-rad-active");n(r).closest(".e-radiobtn-wrap").attr({tabindex:0,"aria-checked":!1});var u=n(r).data("ejRadioButton");t.isNullOrUndefined(u)||u.checked()==null||typeof u.checked()!="boolean"||(u.model.checked=!1,u.isChecked=!1,u.radbtn.removeAttr("checked"))});r!=null&&r.checked()!=null&&typeof r.checked()=="boolean"?r.model.checked=!0:r.checked(r.radbtn.attr("value"));this.element.find(".e-rad-icon").addClass("e-circle_01").removeClass("e-rad-select");this.span.addClass("e-rad-active");this.div.attr({tabindex:0,"aria-checked":!0});this._isUI&&this.element.find(".e-input").click();this.isChecked=!0;this.isChecked==!0?this.radbtn.attr("checked","checked"):this.radbtn.removeAttr("checked")}u={isChecked:this.isChecked,isInteraction:!!i};this.initialRender||this._trigger("change",u);i&&this._trigger("_change",{value:this._hiddenValue})},disable:function(){this.element.hasClass("e-disable")||(this.element.addClass("e-disable"),this.radbtn.attr("disabled","disabled"));this._isIE8&&this.span.addClass("e-disable");this.div.attr("aria-disabled",!0);this.model.enabled=!1},enable:function(){this.element.hasClass("e-disable")&&(this.element.removeClass("e-disable"),this.radbtn.prop("disabled",!1));this._isIE8&&this.span.removeClass("e-disable");this.div.attr("aria-disabled",!1);this.model.enabled=!0}});t.RadioButtonSize={Small:"small",Medium:"medium"}})(jQuery,Syncfusion)});
