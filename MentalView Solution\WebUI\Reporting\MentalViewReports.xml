<?xml version="1.0"?>
<Reports>
  <!--Report *** CompanyData Subreport ***-->
  <Report version="2.6.20093.53207">
    <Name>CompanyData Subreport</Name>
    <ReportInfo>
      <Author>Peter</Author>
    </ReportInfo>
    <DataSource />
    <Layout>
      <Width>10110</Width>
      <MarginLeft>0</MarginLeft>
      <MarginTop>0</MarginTop>
      <MarginRight>0</MarginRight>
      <MarginBottom>0</MarginBottom>
      <Orientation>1</Orientation>
    </Layout>
    <Font>
      <Name>Verdana</Name>
      <Size>9</Size>
    </Font>
    <Groups />
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>285</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>5</Height>
        <Visible>0</Visible>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>LogoField</Name>
        <Section>1</Section>
        <Width>10110</Width>
        <Height>285</Height>
        <ZOrder>-4</ZOrder>
        <Align>0</Align>
        <Picture>Logo</Picture>
        <CanGrow>-1</CanGrow>
        <CanShrink>-1</CanShrink>
        <Font>
          <Name>Verdana</Name>
          <Size>8.25</Size>
        </Font>
      </Field>
      <Field>
        <Name>HotelDataField</Name>
        <Section>1</Section>
        <Text>HotelData</Text>
        <Calculated>-1</Calculated>
        <RTF>-1</RTF>
        <Width>10110</Width>
        <Height>285</Height>
        <ZOrder>-3</ZOrder>
        <Align>0</Align>
        <CanGrow>-1</CanGrow>
        <CanShrink>-1</CanShrink>
        <Font>
          <Name>Verdana</Name>
          <Size>8.25</Size>
        </Font>
      </Field>
    </Fields>
  </Report>
  <!--Report *** ServicesIncome ***-->
  <Report version="2.6.20093.53207">
    <Name>ServicesIncome</Name>
    <ReportInfo>
      <Author>Peter</Author>
      <Title>Services Income</Title>
    </ReportInfo>
    <DataSource>
      <RecordSource>Receipts</RecordSource>
    </DataSource>
    <Layout>
      <Width>15105</Width>
      <MarginLeft>590.4</MarginLeft>
      <MarginTop>590.4</MarginTop>
      <MarginRight>590.4</MarginRight>
      <MarginBottom>590.4</MarginBottom>
      <Orientation>2</Orientation>
    </Layout>
    <Font>
      <Name>Verdana</Name>
      <Size>9</Size>
    </Font>
    <Groups>
      <Group>
        <Name>TotalGroup</Name>
      </Group>
      <Group>
        <Name>Year Group</Name>
        <GroupBy>Year(StartDate)</GroupBy>
        <Sort>2</Sort>
      </Group>
      <Group>
        <Name>Month Group</Name>
        <GroupBy>Month(StartDate)</GroupBy>
        <Sort>2</Sort>
      </Group>
      <Group>
        <Name>Driver Group</Name>
        <GroupBy>DriverId</GroupBy>
        <Sort>1</Sort>
      </Group>
    </Groups>
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Height>285</Height>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>1530</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>300</Height>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>500</Height>
      </Section>
      <Section>
        <Name>TotalGroupHeader</Name>
        <Type>5</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>TotalGroupFooter</Name>
        <Type>6</Type>
        <Height>420</Height>
      </Section>
      <Section>
        <Name>YearGroupHeader</Name>
        <Type>7</Type>
        <Height>390</Height>
      </Section>
      <Section>
        <Name>YearGroupFooter</Name>
        <Type>8</Type>
        <Height>555</Height>
      </Section>
      <Section>
        <Name>MonthGroupHeader</Name>
        <Type>9</Type>
        <Height>420</Height>
      </Section>
      <Section>
        <Name>MonthGroupFooter</Name>
        <Type>10</Type>
        <Height>540</Height>
      </Section>
      <Section>
        <Name>DriverGroupHeader</Name>
        <Type>11</Type>
        <Height>420</Height>
      </Section>
      <Section>
        <Name>DriverGroupFooter</Name>
        <Type>12</Type>
        <Height>540</Height>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>AmountCtl</Name>
        <Section>0</Section>
        <Text>Price</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13500</Left>
        <Width>1605</Width>
        <Height>285</Height>
        <Align>8</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>CustomerIDCtl</Name>
        <Section>0</Section>
        <Text>StartDate</Text>
        <Calculated>-1</Calculated>
        <Format>Date</Format>
        <Left>3135</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>6</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrLeft1</Name>
        <Section>4</Section>
        <Text>Now()</Text>
        <Calculated>-1</Calculated>
        <Top>75</Top>
        <Width>4515</Width>
        <Height>300</Height>
        <Align>0</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrRight1</Name>
        <Section>4</Section>
        <Text>"Page " &amp; [Page] &amp; " of " &amp; [Pages]</Text>
        <Calculated>-1</Calculated>
        <Left>10545</Left>
        <Top>60</Top>
        <Width>4560</Width>
        <Height>300</Height>
        <Align>2</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>3</Section>
        <Text>Service ID</Text>
        <Left>1995</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>Field2</Name>
        <Section>3</Section>
        <Top>255</Top>
        <Width>15105</Width>
        <Height>30</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <ForeColor />
        <BorderStyle>1</BorderStyle>
        <Font>
          <Name>Arial</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field3</Name>
        <Section>3</Section>
        <Text>Start Date</Text>
        <Format>Date</Format>
        <Left>3135</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>Field5</Name>
        <Section>3</Section>
        <Text>Driver</Text>
        <Left>4410</Left>
        <Width>2145</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>AmountHeaderCtl</Name>
        <Section>3</Section>
        <Text>Price</Text>
        <Left>13500</Left>
        <Width>1605</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>titleLbl</Name>
        <Section>1</Section>
        <Text>Service Income</Text>
        <Top>420</Top>
        <Width>15105</Width>
        <Height>600</Height>
        <Align>6</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>18</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field7</Name>
        <Section>1</Section>
        <Top>945</Top>
        <Width>15105</Width>
        <Height>45</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <ForeColor />
        <LineWidth>1</LineWidth>
        <Font>
          <Name>Arial</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>DatesField</Name>
        <Section>1</Section>
        <Text>Dates from {0} to {1}</Text>
        <Top>990</Top>
        <Width>10275</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8.25</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field8</Name>
        <Section>10</Section>
        <Text>"Total of month "&amp;Format(StartDate, "MMMM yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>7695</Left>
        <Top>30</Top>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field9</Name>
        <Section>10</Section>
        <Left>11190</Left>
        <Width>3885</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>Field10</Name>
        <Section>10</Section>
        <Left>11190</Left>
        <Top>15</Top>
        <Width>3885</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>MonthGroupFooterSumAmountCtl</Name>
        <Section>10</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13470</Left>
        <Top>45</Top>
        <Width>1605</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>1</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field12</Name>
        <Section>9</Section>
        <Text>Format(StartDate, "MMMM yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>600</Left>
        <Top>75</Top>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field13</Name>
        <Section>9</Section>
        <Left>600</Left>
        <Top>360</Top>
        <Width>2850</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>CustomerIDCtl2</Name>
        <Section>0</Section>
        <Text>ServiceId</Text>
        <Calculated>-1</Calculated>
        <Left>1995</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field4</Name>
        <Section>0</Section>
        <Text>DriverFullName</Text>
        <Calculated>-1</Calculated>
        <Format>Percent</Format>
        <Left>4410</Left>
        <Width>2145</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field14</Name>
        <Section>7</Section>
        <Text>Format(StartDate, "yyyy")</Text>
        <Calculated>-1</Calculated>
        <Top>75</Top>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field15</Name>
        <Section>7</Section>
        <Top>360</Top>
        <Width>2850</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>Field16</Name>
        <Section>8</Section>
        <Text>"Total of year "&amp;Format(StartDate, "yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>7695</Left>
        <Top>30</Top>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field17</Name>
        <Section>8</Section>
        <Left>11190</Left>
        <Width>3885</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>Field18</Name>
        <Section>8</Section>
        <Left>11190</Left>
        <Top>15</Top>
        <Width>3885</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>YearGroupFooterSumAmountCtl</Name>
        <Section>8</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13470</Left>
        <Top>45</Top>
        <Width>1605</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>1</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field20</Name>
        <Section>6</Section>
        <Text>"Total:"</Text>
        <Calculated>-1</Calculated>
        <Left>11835</Left>
        <Top>30</Top>
        <Width>1560</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field24</Name>
        <Section>6</Section>
        <Left>11190</Left>
        <Width>3855</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>Field25</Name>
        <Section>6</Section>
        <Left>11190</Left>
        <Top>15</Top>
        <Width>3855</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>TotalGroupFooterSumAmountCtl</Name>
        <Section>6</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13470</Left>
        <Top>45</Top>
        <Width>1605</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>2</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field22</Name>
        <Section>3</Section>
        <Text>Mission</Text>
        <Left>10830</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>Field23</Name>
        <Section>0</Section>
        <Text>Mission</Text>
        <Calculated>-1</Calculated>
        <Format>Percent</Format>
        <Left>10830</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field26</Name>
        <Section>11</Section>
        <Text>DriverFullName</Text>
        <Calculated>-1</Calculated>
        <Left>1140</Left>
        <Top>75</Top>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field27</Name>
        <Section>12</Section>
        <Text>"Total of driver "&amp; DriverFullName &amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>7695</Left>
        <Top>30</Top>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>DriverFooterSumAmountCtl</Name>
        <Section>12</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13470</Left>
        <Top>30</Top>
        <Width>1605</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>1</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field29</Name>
        <Section>3</Section>
        <Text>Payment Type</Text>
        <Left>11970</Left>
        <Width>1425</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>Field30</Name>
        <Section>0</Section>
        <Text>PaymentType</Text>
        <Calculated>-1</Calculated>
        <Format>Percent</Format>
        <Left>11970</Left>
        <Width>1425</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field31</Name>
        <Section>3</Section>
        <Text>Customer</Text>
        <Left>6690</Left>
        <Width>3855</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>Field32</Name>
        <Section>0</Section>
        <Text>CustomerFullName</Text>
        <Calculated>-1</Calculated>
        <Format>Percent</Format>
        <Left>6690</Left>
        <Width>3855</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field33</Name>
        <Section>12</Section>
        <Left>11190</Left>
        <Width>3885</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
      <Field>
        <Name>Field34</Name>
        <Section>12</Section>
        <Left>11190</Left>
        <Top>15</Top>
        <Width>3885</Width>
        <BorderStyle>1</BorderStyle>
        <BorderColor>6908265</BorderColor>
      </Field>
    </Fields>
  </Report>
  <!--Report *** Invoice ***-->
  <Report version="2.6.20093.53207">
    <Name>Invoice</Name>
    <ReportInfo>
      <Author>Peter</Author>
      <Title>Invoice</Title>
    </ReportInfo>
    <DataSource>
      <ConnectionString>Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\Users\<USER>\Documents\Visual Studio 2010\Projects\HotelDesk\HotelDesk Solution\HotelDesk\HotelDeskData.mdb;Persist Security Info=False;Jet OLEDB:Database Password=232533</ConnectionString>
      <RecordSource>Customers</RecordSource>
    </DataSource>
    <Layout>
      <Width>10680</Width>
      <MarginLeft>590.4</MarginLeft>
      <MarginTop>590.4</MarginTop>
      <MarginRight>590.4</MarginRight>
      <MarginBottom>590.4</MarginBottom>
      <Orientation>1</Orientation>
    </Layout>
    <Font>
      <Name>Verdana</Name>
      <Size>9</Size>
    </Font>
    <Groups />
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>14235</Height>
        <OnFormat>If ReceiptTypeId&lt;&gt;3 Then 
  OccupationLabel.Visible = false
  OccupationField.Visible = false
  DoyLabel.Visible = false
  DoyField.Visible = false
  AfmLabel.Visible = false
  AfmField.Visible = false
  OccupationLabel.Visible = false
  OccupationField.Visible = false
End If</OnFormat>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>25</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Visible>0</Visible>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>Field19</Name>
        <Section>1</Section>
        <Left>7410</Left>
        <Top>495</Top>
        <Width>3270</Width>
        <Height>720</Height>
        <ZOrder>-1</ZOrder>
        <BackColor>14474460</BackColor>
        <BackStyle>1</BackStyle>
        <Font>
          <Name>Segoe UI</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>1</Section>
        <Text>FACTURE</Text>
        <Calculated>-1</Calculated>
        <Top>495</Top>
        <Width>2415</Width>
        <Height>645</Height>
        <Align>1</Align>
        <Font>
          <Name>Segoe UI Semibold</Name>
          <Size>26</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field14</Name>
        <Section>1</Section>
        <Text>N°</Text>
        <Left>7410</Left>
        <Top>570</Top>
        <Width>1020</Width>
        <Height>285</Height>
        <Align>8</Align>
        <Font>
          <Name>Segoe UI</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field15</Name>
        <Section>1</Section>
        <Text>"CTP"&amp;SerialNumber</Text>
        <Calculated>-1</Calculated>
        <Left>8580</Left>
        <Top>570</Top>
        <Width>2100</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginRight>50</MarginRight>
        <Font>
          <Bold>-1</Bold>
          <Name>Microsoft Sans Serif</Name>
          <Size>12</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field33</Name>
        <Section>1</Section>
        <Text>en EURO</Text>
        <Calculated>-1</Calculated>
        <Left>2850</Left>
        <Top>705</Top>
        <Width>2415</Width>
        <Height>420</Height>
        <Align>6</Align>
        <Font>
          <Name>Segoe UI Semibold</Name>
          <Size>14</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field18</Name>
        <Section>1</Section>
        <Text>Date</Text>
        <Left>7410</Left>
        <Top>855</Top>
        <Width>1020</Width>
        <Height>285</Height>
        <Align>8</Align>
        <Font>
          <Name>Segoe UI</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field13</Name>
        <Section>1</Section>
        <Text>Format(CreateDate, "dd/MM/yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>8580</Left>
        <Top>855</Top>
        <Width>2100</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginRight>50</MarginRight>
        <Font>
          <Bold>-1</Bold>
          <Name>Microsoft Sans Serif</Name>
          <Size>12</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field32</Name>
        <Section>1</Section>
        <Text>INVOICE</Text>
        <Calculated>-1</Calculated>
        <Top>1065</Top>
        <Width>2415</Width>
        <Height>645</Height>
        <Align>7</Align>
        <Font>
          <Name>Segoe UI Semibold</Name>
          <Size>15</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field9</Name>
        <Section>1</Section>
        <Text>11 rue de Javel
75015 Paris
+33 149260315
<EMAIL>
www.ctparis.com</Text>
        <Top>2220</Top>
        <Width>2565</Width>
        <Height>1425</Height>
        <ZOrder>-5</ZOrder>
        <Align>0</Align>
        <BackColor />
        <BackStyle>1</BackStyle>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI Semibold</Name>
          <Size>9.75</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field8</Name>
        <Section>1</Section>
        <Text>Confort Transfert à Paris s.a.r.l.</Text>
        <Calculated>-1</Calculated>
        <Top>1800</Top>
        <Width>4275</Width>
        <Height>495</Height>
        <Align>0</Align>
        <Font>
          <Name>Segoe UI</Name>
          <Size>14.75</Size>
        </Font>
      </Field>
      <Field>
        <Name>LogoField</Name>
        <Section>1</Section>
        <Left>5130</Left>
        <Top>1845</Top>
        <Width>1995</Width>
        <Height>1845</Height>
        <Picture encoding="base64">iVBORw0KGgoAAAANSUhEUgAAAQQAAADgCAMAAADSdOgYAAAABGdBTUEAALGPC/xhBQAAAwBQTFRFAAAAAQEBAgICAwMDBAQEBQUFBgYGBwcHCAgICQkJCgoKCwsLDAwMDQ0NDg4ODw8PEBAQEREREhISExMTFBQUFRUVFhYWFxcXGBgYGRkZGhoaGxsbHBwcHR0dHh4eHx8fICAgISEhIiIiIyMjJCQkJSUlJiYmJycnKCgoKSkpKioqKysrLCwsLS0tLi4uLy8vMDAwMTExMjIyMzMzNDQ0NTU1NjY2Nzc3ODg4OTk5Ojo6Ozs7PDw8PT09Pj4+Pz8/QEBAQUFBQkJCQ0NDRERERUVFRkZGR0dHSEhISUlJSkpKS0tLTExMTU1NTk5OT09PUFBQUVFRUlJSU1NTVFRUVVVVVlZWV1dXWFhYWVlZWlpaW1tbXFxcXV1dXl5eX19fYGBgYWFhYmJiY2NjZGRkZWVlZmZmZ2dnaGhoaWlpampqa2trbGxsbW1tbm5ub29vcHBwcXFxcnJyc3NzdHR0dXV1dnZ2d3d3eHh4eXl5enp6e3t7fHx8fX19fn5+f39/gICAgYGBgoKCg4ODhISEhYWFhoaGh4eHiIiIiYmJioqKi4uLjIyMjY2Njo6Oj4+PkJCQkZGRkpKSk5OTlJSUlZWVlpaWl5eXmJiYmZmZmpqam5ubnJycnZ2dnp6en5+foKCgoaGhoqKio6OjpKSkpaWlpqamp6enqKioqampqqqqq6urrKysra2trq6ur6+vsLCwsbGxsrKys7OztLS0tbW1tra2t7e3uLi4ubm5urq6u7u7vLy8vb29vr6+v7+/wMDAwcHBwsLCw8PDxMTExcXFxsbGx8fHyMjIycnJysrKy8vLzMzMzc3Nzs7Oz8/P0NDQ0dHR0tLS09PT1NTU1dXV1tbW19fX2NjY2dnZ2tra29vb3Nzc3d3d3t7e39/f4ODg4eHh4uLi4+Pj5OTk5eXl5ubm5+fn6Ojo6enp6urq6+vr7Ozs7e3t7u7u7+/v8PDw8fHx8vLy8/Pz9PT09fX19vb29/f3+Pj4+fn5+vr6+/v7/Pz8/f39/v7+////4rBdfQAAAAlwSFlzAAAOwwAADsMBx2+oZAAAABh0RVh0U29mdHdhcmUAcGFpbnQubmV0IDQuMC45bDN+TgAAQBtJREFUeF7tfQd4FFXU9pTdFJIAgQChhN5L6L1XAamhgyKiNBEFQUFRQCkiHQSkKEVFEJDQe5Hee5FOCJDe++7M3Pmfc+7M7swkuymE8H3P/72PQtid3Jn7zrnnnnvOuecy8v9BZowf/P+I/yPh/0igyHMSCCHGj+yA7xx8r3xK1B9yE3lMgiQTiRAiKf+EDhGA8hdAIngN7Sx+IKmX4Z/w+3Yi1I9Vepxy7Ah5TIIAfxBJsr1v7CR0E/6WZSk5IT4+JiLkVWhYyKunjx4HhYS9ehkaHZ+QmJiYImAvaT+BSPynhKRiAwqRhltmAXlMgvqE9NHpOydEsqbERYQ9f/LgzrUj+/du+2PtyuUrVi5bOHXilFnLVv28bOWmwN179uz/98rtO7fvPX4SFh6bmIYk2CSEdp0S8r+ABPquiAD9FyWZWOKjXj25c+no9vXLZ00ePTigfYcOjevUrF6jZq1qlUsUKV6+Zp2a1Ws2adu+fbsO7/YZPGjgsC++XL56+/5z9x89D46KSkyzghypwwBaxn9lE3lMgigpA1gkQkp81KtHV3dv+GnC0N4dmtapVqaYJ88zLMuwCAZB/2Y5TvnZpXDRWnVad+r13oipM37788CZG89ehEYnWiRJlFTtkX3kMQnAgCQRISn65e0T21dO+7hX69p+nhx0EDvPMyaG5Xge+0y7rbCh8qH8zLmXLFmvQftu/UdPnLNu/5Vn4TFJaTBA/jcMB9kqSZbEiMfnVnwztEvTqkVclZ4BBZy2lwzDcSzLIWzyADwxHJUTlmdYIIhzK16306DPZ63ZfzEoKj7ZkoPR8MZJMLwXMSUh9Or2n0Z1rurNMxzPMLy239BphuFoj7VfaMAyOGRs/wSKGMatWPVWfb+Yt27ftRdxiSlCNpl4sySIspUqcVQFoiXx4cFlX/RtWNxV169cAcsxfLEarft9+sPqPbdDE9MEsDXgHdABSFWRA7xREohFlgWwj8ACEFJeHFsyoq2fu6N3/HoAkQBwnhWbDZy0fM/NCIsVBQJvrpgSxgdU8CZJIJJswQlREokUfm7Np238VP2mGwW5BKCB43EwmX0bDZ7265FnaSgCotp70fiECt4wCSLenojX133ZtYK78bFzFSxOKQrLLGfyKN5u5NxNp4PTQBDo81iNT6jgTZIgE6uFyCT16d6fBlTx4kGjw3vizKY3MyIUhclxMNPA/1zBmt3HLfjnarzVuQ3xJkkgIH6JtzdPbFHAzMGMZxu6rNn4/K8PjmGQY9tsCvMG7+JRvEHfKWsP3YuXifVtSAIR5LQbG8c28XFxoRMfvBxOGba5DVUxsjxnoj+yLP7Amd08Krcas+W56HB+eJMkyJZb60bV9XHjODBxUEaV50UrONehmg+21hWbk2NMbvXn/IcTdYbIPRLASUBXBrisleWEa79+UruwCxVMBdQWYjnl+eA/MAHhY2oKgtnMMOaCJf3yo8nIMoyJtqC8XviaqkAQ9yyzWWvOwzyZIoksweIQJiVJltPurB9Tr7gLC2KgMfCgQzAelC5xoCNZjuVdfMvWbNi6S49ewz4a8fHHYyZN/37SJyOGf/Tx0N6d29avU8U3H8syPNV7lEf6D72l7Qgcw9ZfEmR8XjtyjwRY4NNVLbg7gndNbV/ElZr9GklgeDO8cBBZHBwsy+T3q9eu2/vDv53184bAo6cvPHzy9MmTp8GvXgU/ffrkyZN754/u/vO3xd+NH961RV0/T54qPlxqMZwpi9qFY9ima0IdCkIukqA6eySRkIQzi3tX8qLrH+1CkOFw8uJNuFzmPYr7t+v76cx1e079dy84OCwqPjk1lfoFbMtBMSUpPjLi1bOn9w79tXL66F5NKxXAkYQClkUO4P6tfo/Mm+EgCrJsFYmUcn/LqNretKN2zwCCAxEAvcjnK1H33Q+/Wrbr3L2giLhkMGzR3WTzPqK/CH9Ao1eWkqLCgu79+/ucUZ3rl3AHcXJFnZIlsGz7LbHpVnM25B4JRARJECXh1aG5bUqaKQXKI9ifhofH5/KVb9530qpDNx+9jLFIhIiiBCYFetoUTyHtPrZLzX5cAUgpEc/vHP11Qs/6JfOxJlbRkJmDY9/ZkZBHs4MoEjn++ur3qudDcQUe6Mi3PQ3oQO/KLft+s+HfeyGJImgR6mQVUBaoWqW9V5hQBocoovULH6ZG3Dq8akL3at7mbIyHbvtTpDxZOxBBEsOPT21eDF44GIjpPSUmzq/JgGmbT98LT8PuU2cQCIOqBOzqQLVtkAtJooYvfikmhdw+tGBQA1+XrI0HlmF6H4flrP55bcgJCTrLCyYEUXk4Ij/5e2QNWCfBQoFOjeqQQLlwq9JyzKpj92PAeY5dRQG1jX+1cXUgKJ8on2sEhBAp5cXJn0e0LA1TJ20bhJ6aHhmA7X8aPNHKPYzINgn0ge3NgTajr1EShdsrepUy65+CDgsQClMh/y6Tt194kYIS8HqAydj64tLvn7cuCQ5Jqn6o4y0DEljG5b2z9lhHOuSEBIMwoMcAhm3szW9bFtLJP8xjqBhYhi/i33/R/usJErraX5sEWKMTKerqhtG1i/GoIKnsZWhCsozXxxcdMZATEhRR0PwTFLdAiCX04Jf+rtQGUgErGJbjeIYrWL3/gqNPQDlp5sHXAfAuESHy/PKB1T1MJpx0YBBmRALDFP70qv6xdcg+CeAs1KzNiQSeG0FKe7JpYHnWpH8VsLIHG9m1/Ltz9j4Fn7gsiKgIdS3mAAQmVVGUiDX0+Mw2xd1NMA5AG2cwGhiWKTHppkGXaZFNEhS1pBcFUZaI5cHKzr4sLnY0EyLD8DzL5SvZdNqOhwnQe1zOgn7XNpoDqIFHmDGE25s+rOHJgBw4sBtYptx394xNaJBNEtBxS/+zAZR82oNlbfObcGhqjQJY55iKNBq5+XoKhMtEKxEFx0o6W0ATC1qSrHL08Rlti5jBF69brWlQZfYDYwMaZJ8EmSSn6TtCSNrdRW3BpjeaLyzDupbpMe90qoWANhRwIEATmt/OGdB6gmZARYrCzdXDysMinC7L08N/wRMnqQ3ZIAE7Lgmp4befWagAKH9IwtMFTfKjj0AvkCzHeNYeveWJBAMGr5StaPE48fdlA9R6QieGHBL4WUUOHFea29vB1v85yIkAZpEEeHtouaa8Ojl3wt+xYBWg7Qav1vrsx8ZemlFgN1nY/B1/OBufG5NBpog7+Z2fK84ReGuDgmz4y3PHHGSVBOizLJPUmJOz+zWcdDsR7QK0eiXJGjS3jpvdpwVQ5mxzkTHrnqc6cm/mMlKv/VDPg44G/TwNHzRfH2K8XoOskSAJopAqS4nnF/WvWrL/VkkWBAI5BsCF8GxuXXQTqGA5xgWnBffqk/cmGZt6c5CufV8bAtyqmap5K1z7zRFOBmHWSBAlSSRC8LIh5cwF26xJJBDoQ+NDJOT+zDYczk8aYIDJo8Hsq0S2YpJNHoDI8s2fmuTL0FziOu+IVlenGSBrJMiiICUdn1jDjXFrMP+pnIZmMiycCHm6uiODS3uT7rZmLn+rRXfwV0UpTwYEBD6vzWlAbRO9TmDZbrtjjddrkEUS5OTrCwcX5Bi29vQrQDrMdDBFyJHberozjEkbS8BMC6Zk/9+fCwLY06hO3jgkK0ki5PyslnQNa0CPfTGvHZAVX+wYU9PE8Wz5jw9aiRV1AZIQtbN/IY5JH08ylXhve4ggQywyJ0lEOQORZOu1GQ3Up9EOi4DD8ZJjOzVrJNxe3r2oiWeYAn13x0EWnogLR1kUzo4uy3MudKVkA8szpUfveilgxh5MI3lBAxGR8eQL40vn08Rf6AMxA0+lqFZNBnBKAhr6kpx4bFpTLxzzbVY/FTB3jrp/pOtTq5mow4TezUxDw1yxT09EgVnk6K5vABD4FCUiRJ0cnh+XURoFyTLvX7TC8xp/SYFTEmDwi1Li78MreIDvmCk/6VIKuDPAlwhrwaCZDTztd8I1PVhtLkVHHo92JHpvEGC5ECFkc28TxCQ0KRAcO/y6s5WrcxIkQUx7uqZ1URNrMrEmdsCeSFzCgitJEsW4dU3z68YB/ZktPHhHvEWQxDyZFDSgoieELarrroRnbBh6yUokh34MpyRIhKTeX9oYfMccx5jqLH0ODnJUQYSQ5JO9Cmj9SCwPeUMsX7Lfmlj0OTi65xsCPBl4b9MeTyqF0T3bk3HMoDOpOV1ACVLq9dlNPGk2nUvBMbeScclCI46WR98X1+eemRie53nfbmtCiBWzhIztvVHQt4NJwjsH+epWkxwz8FSq8XoNnJJA4s9+Vy+fskzP33FjHC6aMPdClsK2tDaZNOayko7p02Xl4zQMGjkZhG8CtqiFTMJXNPHQLh9YZvCZVCcvxSkJKecm1/Hg6aLQpdI3zwRCdSKMvoTTn3gz6NSy34pnGM/WC+9aBCFnOcavC5o2Lgvk2qSKWguWZQefTsUgVsZwSsKlaQ09aPiEZYsN3ge+MVAvsGQQH8yrBR5F7QoaXIq1p55PwSWFw9XKmwL1uNGoXXxg3yLa4cAGHI534t/NkAS0hUT5vzlNwV2EDn23uvMj8XP8zypFbevuyfE6QQCBKT78YIyxtbwGvKfgZY1pqis1GZhue2Mdr58ckABj3/piYWtv2keOYUq8f1hNUYfFY8rZz8vbgh52mLr98dLhrfIMkkyE0yOKKrks6HXstjfO4dyQMQkQHxGF8HWtCtKhwLEM32BpqGRVNA+R5WeLmnmA9BtEoc6Ch6kOhS7PIMkSebWuDY8kYM5SDkgA74EQtfNd8NOA75JjuSJDDlsFWDnjHESS9w0oyhnTUllz4SmXEkFzvmWAsFqvTfJFNQVvKickgKGTcGZkIciloia4S5PFTyFRgjo3RfJgRk26ZtB52PN3OxAPDjdje3kNfICIbZ3MNDoJHcg+CZJELLe/r0EdhTj5Fxl2LBWMaEUxinu6F0LHrm40uNecHyNI4I59y0CryfLft8Wpmw0ENvskyKL4/Lc2JrCBaYIZ12TxQ3Ak0dlBksNnVTVxhkgLw/mNPCATyZLXK4Z0wM6KYvT2tjSxKWeSIJPoXYN8eDMOJ8iyKjjiBPgk6GgjRD7cqxC+e8zRVOHRbE0kIVbHeaN5BrRqxbR7E91taRJZJ8F2leXMhMqcK0ZXsYmGix6jkwxmYJGQxJ+qYIoIKB1FChiWq/DZKZQTmk9ib9AQxXYGxfxX/jd+mxls+0nhGSUix6/3o5mkHMP2OBDv5CE0JIAHCPMvpJfL6sOSkE6PHMv3OxQDsWeZms2WCwPy294/gIXwW/4OG2NAZ+IcmhTyKiQ8MioqISEhIS4xLTXZkpaWZgVj0xlwjyB2AQae1fh1hhAtaRaLxZKWmpQQGxcRGR7yMiQBsqckIqde6umJMSmW4fv9m5w1EpQOWGTrnt6FQKnCygD+8plwx4pKH7znkiVuYyWdUxHVL+s/9YJV2VlArDe+Hztu2rxFa7f+vW3rtgPnLl+6fPH8pdtPwyIjnCA8LDwyPAQWaTJJDQs3fp0xgq5duXTp8uULZ45s2/zbwp++Hf/ZrBMWupoS4if7wQKC5Rl+wJk0ddrIAFpJoH4CQX74dUUTz5oZsIohstxwTRiNXIAhKSXf/cRoIDAs59nzH7CXJfQjCKc6eTJeviUr12/cpFHDVl179e3bN6DfkBGfjv/cMSZOGD9hwrgvAuMkIqXeXTBu/GefGS/JACMH9A0I6NunV/cODRtW9ytV0JSvyZ+4wAMv4N+tPOmQ5QaezSIJkGoB6fmWje0KwJBXu8oOPZeEI53mZ0X9WTtd6pwLW3fGdZrPK4pEFI50gXRW0BpgW2cQDckYLMsXnw+JnUn/9jSDYs4c4NSEbGlq3HMs49lyMw4GMGsejCnGc3j7QWdTHDFgIAFzD6zkxqd+uOuOTg4sV2hOKO5ix8WxJDz63EVJTFafA27ed18cRt/xKuFwlwJ6njBCZWDOAAxd8EUXAgmJJ/sbv84CcFgWaLtVttLhL4lLqijJ0O9fTMsaCbA8gsSP9Y3zgS/JhKLAmVwbbUuErzGnVCLxh3vob41M+2E6DNUrkiCc7eDFsmYXqlrpfld6rRPgBSbfRQmSLMcdHWiiK5PMgDlRymPgriKvdv8o7kZJlv/pACqcY5iPblLHecbQ2wmSIKTd/MgDNpXZkvG9Rt61wMqa5ouRZwtrpRdTl+6bQ7EB6kYQjjX3Un7ddknG2UQasCaeZbjCS8CFl3C8j7pJNBtgWZ7nfPocQiHANOBbX5SnjYy64ywrQisJMOVIsb/6uyobqUBGed5vbpLiKUMebo3Jx+icKSgtY+9B8oyyA5FY97bw4Fhlexr4fdPTlgFQDxVZlkyImHS+X3YYsCUPmzim2JB/wdtKY38JK/ypVTvqDk7+DqCVBIg1p976TJONybKcd7s/lPArWiPRO3ugDrBdwvEsz9T/RetGIKm7WnpwkFFnxgAAxOppYn4GPaPqF3YM47+KLk6G3WMn+6FOgkUs7IlOB+iZiWFccAoEs1b5fY4t9t4ZkHp4YEkWD/QqCC8z/yRIWcoSCSBEkZsba27FMEzRYedVMw4ueTS3JsPrIp4sx5qGXUiCPH8V1p1N8uF2bxYINbvyBUo3aNKtb0Df3n3TISCgX/+B/btWoY2pJPwLJADbXnUHDezTx/hLgID+nZs1qlHCg75qnMxB4IoPvwiPoNipNyaUA7XhM/VRFkmA6c1ydxLsLtGgzPeRNmJlIp0b7sXwuuHKmblS80I1zitCrNv9cTs88GPKV7bD6O/m/7X19JXLN65cTo8rly9dOrGiI21MRwL6uHtsv3glg1+7dPnC5WOBWzfM/+KDXpXc8KXQMVdqzDV8CBjCIolY2RCoLDXzaRZJAJs1/O+OWgXGcu7116CzjRr0Uvy2TuB91qkE1qXj7jjtaBAtG8tRm5JnTI0+mLPnQWhUQlxKWkpqKpi4RqSlpYVuaUvb0ksCx7FefS4mGX8BkZZqSY6Lj40IvnZ02Uet/GALOs7CfuNuqe4vWZSsu94xMRxXaeHzLJIAonDjqzIaQQBPQsAuZY8bxhsezaxOtZ39Go7JN/ZJmpYEkra8CLbCcT49F50LSUQ/BC14kg7ovY7e3pZ6yA3DgWW9+l63YF6MEWjgws0sSRHX/5rQzFtJzCg94b7yvPj9qfcKsBxTe9VLTT/TQT9FRm/vDpu3bB1kmQoTL9F70Wn25HuFqP/SxgLLmnxnJ+tXiqmz8mMrfKGhO58mYzepEziDiRobjwlsQ2+bbjh4BdyEeHLGgIwpC0hezNVl73rTBys/5Sl9Xrol6/bXMEk2+T3UURsAvWK8Nb0ahlBs4OstDbUtUWWStrmt2bjlncvfeJP+OUnsxHw4SAv2/ieELgZV96TmKvViMMFiAlsp99NLAsd79bqWlpHXUt0ppKRIJt//tUthHA8VvwtWneLwVcjqphzDtNkemUEbNujMZsvBvh4665bz6vAX2KC0GJAsRC6qTQPwWr1RePAVq1YSiDV0pBt849V6VYSVvn5Y4Wdsq+D8G7O9RQbDAWZZz543LRn9ovJQSrIIkdIerWiM7o8K377ANQ5eI5GUvd04ln1nd7SxAS20ilF6ubyRMtEo4EoPP04j3si7dP+rCjgV6UgoOQWWv/YnJWlBA1xgt1rVKQ8wiK+OXvuttICvY/5p5XA43HDir1MmQmgi9db4YuBIqvD1c5pFgt/LwtWZPXp2n3AlEfNeHUA7RZKL48rSHbxq/9ga0+8qrGLLZ4YWA0HQ2S9udZbrS4GRlFu9XFiO9R2ww0qDVg7vDoALYgLb0BaNdgLj1eeaExI0IHG7W0NGa4VvgqjxCx9KRIq5e/bC+YuxgrKayBDa4ZDyTxcP3bqXZRqvCsepk046JLCDB3yuu8an137wtmh6mnSyiwvDudZbGEQJoGFsR8glEmTh1WeFTSxTYdoLlQS6Iwc8VVYBR6Wjl6ElIfinWnT1be9hhx0W1UcIKQmr66BZorMSyk68D2tXDQkJB5q7MGzRAQcE3Nnp+A0gcosEImxs4M6yFX8IVVWCsiUBBiSMSuMv2KEl4fTwkjonOsO49z1Gu08R8X0FjDZoL+FrLYlXIrUq4gL9TQzjP/sBZK9lpNV0yDUSxBsfFOa4Kj9G2aYzRW+qtf8cDksNCdZ1Le1pWBS+o89T1yKKA7k9qgT6i7Q8uLf6Cxcs9nZI1PoKHJOv+84kTHl1qBEV5B4Jsd+V4LhqCxPU/RTQMi2qg3tQHY9KDQlh35Q3+n+qfncTshHVAXGoRyFcEmiNpcJ9Yf2uW6tHLCjGcRUmXkZvW14pRmBhaWUzV3OlreiWKp8YP6Rp+RlDQ8KFIUXwOTQkNFr2FDY54PuUZHljIw9lOWwnoczYi8a3HT4nP8+3WA1Gi+Kk0HyZDrlFApHlLS28OP/fbM+iqnMcCJoakOkAJCh0bW7lpfWvAtptopYWJUGYX96EgWqFAfyr2oxHxlcdPs/L5Np7fwr9PLPdPrlGApGPdPfm623Q3A7lFx9CmS4yBgMiC5dYxfllzIbhwAUcSaX6FS4l0ZOK2b+jMyXLNlgVKxp82WFz83MlPr/qkHg9cosESZDP9vVxb7XF+EXmABJwPIsx42iAUdNLz2Fn6HYr/E98NsrH9pWqG1zab4VqTjpRi1hQtkiLZU/zlgRYnlz/qErpd7cav8kcQAI+rPVWL9zHpCWh+IQbOFAARBauD/TWkQB/eQ88AjvGdR2O2zP1y4Xn4iSNr8kJcosEIgsvf/tm4sILxm8yh6IYiZS6t1E+W9UaBRWn/wcyghcQWTjZxcv2lQKuzLhL6TKnSVpoUESac3VoRy6RgFVwI0NeRTmcCB0DSYBuxK0oYaIybteMNRc8tgWeJGLZ0zSfpv8ItubM+8YmUbaw+J7xiwyReySgGszUOMsADH1mWQie7IajQTscGq0Khpx2vEQilk017XUU1esaLw+mnbUPCCi+Z836LodcIgH2ZTmZBZ2CUQzblKsDIWKlpYBh2m0Jl8DSoBOEZSUGeW3Aa1tvjCCwtUPbJrKC/o6sIJdIAKBNkEXutWCU1M24w22U2niaFVS3PbE44KlvLfWngnqOgIaO2xPha50QQsk/By6UDJBLJNBFgpMIixOoOiFmi78xTMQxfU9D2SOMK8kySZigKagIbh/4qxessF4LOSaBwCJV808oU6OMQeUNKFWf1E3KDgmCKRJIiFhdVv+a4V9DL8Msp7iVLC9HoNPMdgGQ4DbotLHF7CJHJChDTe0VEa0CKAWUCLrxSrUQcQ8WLKqd6CgGTWIiv5qtT8EBEkxj7yg2AjSYfHuAWmCYXgDryUIjMN7zOsgRCSAHsE4XIfEU0xHsqplSAYwo+9VsouFYEnB2IM++0PuQ4QHyT36gjjFCSOKZri5aYUESSn5x3dhidpEjEiRcpGu3VFBtTP+zv3MqEPgV1Pi2fa6HQoL19oea/tFpgis166m6NpIkOeFIO/3+R3RwT3dWoSJLyBEJ9N1ImOGlKGHcloULPfyTFrBBIYF/06Q0B1AsxqSTffR+NSj2WXVxMB0JKGoJ+1un2wTK1V7wxNhidpEzEiCvKC40LMy++5eo6W5g2KREhYWGvAqJCIlIEjHxDsgQHM3ZCgmR/3TU6UWU9VorX1FrEi9J2N3cUEKJYbjGv7zQt5d95IgELPO276dpM35cumrj1l37Dx3af+BcihR+4eDBvZs3bNywfO4P30//fvoP02ctXvPHvuuvksAx5MSpgu/65a/N7F1T8zT9V1ESaPgobntDra2EOpJrsc5ZXYIsIUckoDpf3rmyX+myFapUr92kbadOPaaHCxe/6d6pfYPKVapWKFO6dKkypcqU8itXyf+dYTPWH3+a4ng8UJ1Agn+uo+kfDaoz/qtfUqcMkhC7pa52OOA6g231R7ixxewiZyTAcDg7rXddH0zmcMnn4VFi5DPhYL/iHvlcaF0jmsYJOTxehcvU6fvDP3dSJTnl5kUlSkdBhRzMZpmQ50tr2kUdf5XlmXq/hmgcMrGbamtnB1SdbPutTuNbWUGOSIA6U1L8/aNLP2yE1ddYji/48RNxf8+C9Nlo1UoaxOIYxmwqWKnDl/ujhKS/vl16/CG0CKJkUbqmSMKzBVX0OgEIbvw7JHGq943ZWN2oGFnunUBndQmyhJyRAL8lSSnPTy5u7Q6VSnnOTgIkgLn7VqxSrbF/1Up+BcwsFFTgPSsP+is4ac+Env1nHw+C8uoEti4gFBIezy6r7x6Q0BxrcKi3jV5b0ZDJyjBs551vhwR8LpgGk+4ua+7C8iaWsZHAcqyPf9dR02bP/3PJ7NlT3u/UtDQW/HXx7fjbs2ebB1WuEbDoWhL1wdqGA9Dx6Hu/9EuHln+HoyFFbxu1qoy+WAYMm6673hYJIm7QIVLq/Tl+WHrNRgLHuHWcu/fqwyfBsS+eBj24dPjP8a3LubMsa/Lo+ueLR2s6FyhQe+qJGKBQT8LDb/VbfrGLrbeGoxVCL4laXkJTgx1LnTFMzwMJxsfLLnJIAibEA1KPd/GCFFu7JDDlpt1Ipr5+LFIXcWPb2AYFIMOt8Hs7Xtxb2IDnq4/dHaIkj9hJ+O+rwjqdgCS0D0SHu5LzELHYR5vJA1vkGKbfcSjO8FrIIQlYvBZCjNbQib6QEqyRhNZ/RVALD5JjJEkQEk/NaFsEYunlxp2OPDfam+MrfbA1xDZjUmOJ3JlgWD8BOmMhEtockcLn0Z2iCmiEov+Jt0MCHc84dQlkcUWYtlQSWI4Z8G8CXKAEnmAJabmxoJ0nPHOzJQ9frm3FMlzZoTsTMQvZJgnkro4E6mtkux4An4qyPiPhP3nhY9mv4lmm71uSBDrR4ysi0qYmME1qpsixd0H7S7SaCdxBINLV7+pC1Kjw4P3R1yfkYzm+ypSb6oEPisV4d0IBbfforpl39sZQInHERCwqpCMB0zn6/Av7CF4LOSLBBvCCH+wKXbfrBPbLIFvdWxsST470hjYbLnwUvaqyiePyt/89UblGGQ53JyGFav9oF9sFRiv5UfCo0b/oXYy42n77JEgyOdWvsJYEzjQNjDztMhtSVoJ/a84xJt5v5KmEE51dOdZU8fPriqNFIeH+N0V1HUQ7q+XWSBpIRCqi15RN73Loc+Jtk0Bk+coHxTQ6geE8ZkdQR4LtItjmnXplJOwWdeu0I+XVJ1BeIV+zP7FUlI2ExzPhTBptD0GJbIqw6wQ5enUZvSkBc2TAsbejE+wgMrk1wlc3HArMi9STAPOIRF78VBSipw1WRactLAHzSem5yepwoI6l+RV0HQRwjX8PoyQgYn6tqD+jA0jocQA3hLwOXpMEWSYPxpZgGI2x5LMI07HtV1CbIuqvutBolWmPhO0NoPRM8a8j6PRAA7Lk+bKq2t7RB6q/DnZy4GWEyLGbIAlHB5bp+rbMZhXwwp9N8NMOB77Uz1F6EuhSI/7wO9Bomc9uigdgvuSKjPzPLgkSIS9X+xs6yDBsbfAn0OYkkcT9XT89CV12vHbNiNckQZLloEl6EsquirGtfSnQNEw63wdaLDXqsnCic36YVANOqooRQ/Oh6xsYe8gw1ZcH48iiWiFua30MViqAn7i3Lwnw+M++KKnVCXy5NUYScOtSyo0hMKsXH/qveKOPN8+yBTrvpIFzMJZEIoX8qfEsKceKcDWWQ244/D4wmXCgrtGzxHHt/orQ3CtHeE0SZFkOmliK0SygQBL0w4GO6KTLQ2F6K/beSevND2BSLdD9oF0SiCxHB7a3z39wc/i//LwnWIOVIulIY1e7ICi7HZuvpxvAXgN5QAItj5t0sh1UdvYbfUW88E4BE8cVG3RaJQFdsHH7O9v7p7xnxm8OZM2rrSUeb6k93I+OjMZrqNp4DeQFCTAihKhtbYGEshPuCCdaQ3HhEuOgIIJCAhGl+KPdNB2kksCVnvMMLsE5lJCksx1d9NcwDFdnqZNDdbKGPCEBFtYhSytAaecqM55a9rTwYFiu5LRYOwmSRJLP9NV3D6JwxWc8wdOPaTupN3unP+axwoz0ORrZRF6QALGH1HtfwZ48U8NfYlN/q2Rmedeqi20WI1rZaVfe13SNeidZr68eAgkKDdZnw3QBWUTxLyCr6bWQFyRAGmPIH7AdivV6d4cl8rsCHMcWCdipXKM4VcT7n2o9JrSyJf/JHfwOeZLEmPHGsw5Z1nvk2wrIapApCbB2EK9+UhTW0uXHX0y50seNY93qz32sfI8kSBJ5MUMpLKV0D3X/e5fgqEPlVHCS8h1u6lEuoD++rdC8DpmSAMHKhwsbmxiO4VuueRWztaYry/q9H2ixSwIGd8NXwgZztfeK6RxwFFbcOElKkpy2sKjhoEeW4XocxsOL7DfMNnKfhDJrYkCVCfTJoQciCfq1K1Ytrfz56YQ7k/LxrEerJY/VB1fTemP/aUnHg7abnXfGwHcY2SayZW0Fw/TAMcw7gUkG70V2kdsksOaya2KU04KUCK1kvb6imw+UWXDrtSvmxeomLOvZ6LsLaeqTK0tpknQ2QFkjalho+UcYjgR6iXVrXYNm5CBxK/J/GAmMqfSSp7A9D56L5tg8OPx1Cx+eNbHmZgsfxp4YUZD1bjPzbLxNgBUSJOujkUACdaSrqLcymJKA/RQOtcHNP3ZwDNtgcZBileaUilwngS/ywbI9F++/jIpNTIqPevnf2V3fd6qIu/hdm8+9EX1jVm2+YNcVNxLshxApjlYiRX2Xj5KgmSWqz3tEoy/YQfFcd4NLmmO4yl/fojU4/+eQwLqW9m/3/pdzf1m9bsP6VfMmDmhVCUIvDOveZNb1+DvzWxep0nf9o1QYKHbPEn38pBUloJyajoRy395Vjv4DiLcG2XObESzD+X50hhqU2qfKFnKdBHgwl8JlKtXyr12njn+VkvmhHB/LuZbqPP9K8r2lvRu/+93Bl7idlU58NhIISd1WF9ZHuuFQZNw1SgJeIT4fY89yR7AM69FjP46FnLPwBkiA+KvtZEn6h5tvw5HbHlhiNo3/eMbue0nK/gbt7ADj3nKqk664K8Bt2AXaO7pDNnFqCcMFDMO32IQHef8PIoH19POGaIzyrFBiwrts84+WnYokcsSO3/bcS8R5Q8RTDbEBdYoklkdDsLCQFly/f+np3HRHrrS0KgTndcLC1F4SSa0p46NlFdC2ExICrmXqzzYqxjL9AlrULFeiWCEPL08vn5Klq9QPmLz+7CuYEcWXLxJA02MtCNuboyTAPBI/0QejLpo5kuu8C0/qUOYbaWsrL1rASkNC5W/+U6ZQ/ZNlGZBbFrOzNW1NTwLLMl4BN1Iza9lIQoV5+wNXzfjskyHv9ujec+jnXy3ceuxeFKZ1GX9TgZrRKhPrnNK4/Vc7JpquDcXe0wakf/sVAeHSGVQlPlaGTE6BG8YD4ViGdCTgcLiRXUkwlV0XLcY+uX333NFjx45fuPUfzbdxkm3NKFFLWZY3NwUzgNX60KrPvA+mksKTeO9TP9tBwCryd9sHv5xzHlAnbG9CW0tHgme3S85qbyOMFmOZNRFEErBaHsQbINtRyXQ0/qYCSgKydP0DXxAETU4OW+rTS2oQH1Rj5KyKtLyhRlpcGq2F+ItjmjMHIdFb6jkgwaP9v9kkgeHLrotW5j8UcXrEMS6Ejb+pQMlyB8TOqowVyzXCXmDAEdxYio3JsrAWyieAsrBfg9vU9eekZReERKyvRlszksB6tjmW3eHAl1oNp9zAfyAHdM+K6KyArpLMia96S2vcNK8ZDy4dt0I1fGwS+Azs4EFT9+yXsMUGgvf+9UgIXQ6RAyMJYMF6tj6allnbRhLKrI3GfCYcaeAVUiXAUTs0NE9xeXQpKO2kHfMNVyh5ijiDiKc/gKifTjGynk02Wp2Mt6yAvJyr2KJGEhj3BrtSMmvaSELF1ZH0cZXFNJYBc8Yko2Y6EFkKW+DPsVAlSwXP1vjhIc2GpZni975SQpYaWeDKzkl0coPMgI0HTVecVsbhwLhU3piQWetGEsr/AikFaA1AXrf9Qls6ogH24QDS3sVTPzuwZT65AARAW5Isp4QsrgPLJigWYr/I5+MbsPXA0R0yAbAvPR6r2B7pSOC8F8FyzwkkOXhiaQjDjbApxpXR8MCO1GB6aEiQ5POflOH1Z6UU7H0QlaLiX7L+1QYWGLpJkivQfHvWtoFmCKjvYL05TBliehLgcA23aVFOOQBJGF8KwnB2ElZDGC4b0JAgkohVUFhGO+JNLf7GbA60JIgsn/kYlYK29hrn5jsvzaHOyRwSIbEHuzkggWHMYx47WzyAgD4ZB2Ud7CT4Lo+2bRDKEjQkEFk81Musmx1Y1n9ZDFUHMEMK5Nk8f3hc3ZFbbP5R/6W9jmaUglfXU+Qv/XAw9zgZ52SkwcM9GOOrIYE1e/4UYUtWzRK0JEjk5le6bBSWZUqPu6EGHuCShG0ddKsLuIZ1a/J3XI45gGXMw+m+ym2NswPLsf7rMaXUAaCvt4YVhUC7SgLP/xBBFWNWoZME+eWvrQyJrd7v7qZfYS+J9ezHBfQlFsAh5z0zRMj6LfUAc/zKaDWiYZQElmOKTA9yQgLgZC+YYG3DgWGmvJQyq9qgg252kJNPjSqkV3uudX9WdkvQlOrH82viEl1jMrIMN/iUtgxf9kDkyC2d1BJXBhLASHcZcsp5ShDZ3hwWPXYS+A+vwn7OrENLgiSJz1fW0ebzcyzr9yXN1FC0Y9TWTvC82qUmyzL+P+d8FxAhd6dVUlk1ksCxJrYJunsdQ1pTHaqqa0h4Z09MTnUCdDL1wlAPzZDnOcZn4BHtJdaLw9yMR2RynOfYq3jChzpsMofmEVOPDPRQTTQ9CTweKpN/5NkUSYB9+GphL2VlT70iUsxXvvA8BUc+s+7t6c3yLFsLdrAph1FAhrf2xhlBrxMIebmyvkICHvLBMPkarNb0i5DHs8oZz2NkWbYDbi1XrE97k46h7K+WLJJwZ1YdVp2SDFMkFhBnm//yFK012rJqAUPNIHAUpt3s6cVzLOv98VNhf29vkKkCn11OAU+YCFfYtnY4hpGElGvj3OlYpJ5Kli/2daJmAUYid7SnZa118JtyA+5F35O9SafAwIhIQta097K1l252AE+xz4Dt4RKUTYZDXNFsAyLwmHVCpOhV1V3ArCr40WNhX8/C8IK4jutfUHkBpjIVBP1wgMaj/6xqRl8taCrYTOQ56K7insdr0m594m5UCQzr2nlTOJabcLpQ0YLWfyJi9D/9fe2TrnF2wAUdX2rY/nhaykJxEOPmZ7pYjjo0JD9WFAedsK87bOw3M8U+3PocLrTQPWzGexth0AlEttx93xvOGaTudwhZNA60HwlMCAn7tTrkt2tYACLKjz9vhVhGVitZ0A5JaZGbB5SG3IkMSaC7uUysufLovWGwJKQFtLCouogHMgkvdowpzbHwwN6jH1v29SjAcpyZcSk3ZOMTOEZTEsTsDQdFmJOW+7vRN00dLGbf+RE2NUaInHzpPWMxapZj3NuuCYVMBsVVlylw7AhRl9Z29jVDhMwBCUAPx3LuFQb+/jgVzqNFRUL9RURKCbn626CyMDg5jvEe9cx6oLc3PjTPlwr4+dTtWKskSlk4xldvJ+CQODespBKgplS4jrymSWAm1tBFtPqKjgSmzCjwg9HNylkAzESvzm8Y2yA/B3OAyqqBBOrAgYmyWLtZhx4mQKUWbB9PPH+4Z/mnrYuzcA4Fx3GFP3wu7O1dCNUIy/JFGgZ8ueFEqFUSsycJOMoIifmliafyWNRoa7FBk6xIpOTDHbXbP5TNEfmaLn0Bx7hmUSmkBF098POIZiVMuFa0VfEyKkYa5IBNXO5Vus/advpeJBzfSvd/i392b1jF16dk6fJly5StWK3Zp8HCgVFN/f1rVSnrV7aMb+GStRqMvwSHMmT6QAYSMOR2+SM/XjkMFg9SKjIJTvpQ9T4RXkxQREXRB1R7+A45CifxZY0EcmvJiHdq+7rSrnKwZQ/3JpuKzackBNAZSK2WCqevuFdp0W9qIBTJRO0gC+u6dwgY8uFnk6dMmfz19HmrdsRK9wPX/rJq2dyvvvxmytj3B/RvNvYSVIbHQar4hTKEcTgAEta2LcBhdXV6lBTbIxCSEGgqH+il35rQAp0689lc48cn9GBVY3sZgGzpUMKFNXkU9CpcvESp8lWrVq5atXIV/waN2y+Hc+yTL49pUK9ejSpVq1YuX6p4CZ8C+fN7Qi1lT/8vbtA6apIgW28fOnjy/KWb9x/e/+/h0xeRCZJsTYiKjIoIuv/fg/u3Lp0+u+tQiAXsBNzv4HTDeAa4OqWWK6yX0U5gGKbSD/9haRLFwJEvj8SgJAY97Sx4d/87CavyGpvLAGRbx7p16jbu1LvH0NHjJs2Yv2jB0qWLFqz5J3Dn2RSRSNaw/du2b/1l8eLFC36YOG7cB/169+japn7t2rWajb9MpQ3cBXCohloZgjrMUV8qKx10MmK5ADXy4vD1ZEhC7M7BvlCjXXUsuw04nITbMRXfSvSqxu44d+j0I+/72Y00yWGRAh3IreW/bdkWeOjYsYs37jwICgsPjYiIjABjwEIrOApEtsaEhUeEB9+9eefSqX+PH9v39+a/N6zaFgRH19GBjid3K88EP1A+0JzCeYTqOJxSKUPZkQQiP1vZ2g2KJSjzA1N/6TN0XKpetvOjy+B40e8Ccamx5mVqFs/PJdFpMNPbzteEHWsg45IMx2mhANPcD3Dl0JcL7zUxToQRRzulvH7lZ0XY4Wf63mlBDbwKi69kEnwxglgvf1UZF4tYjJRjfYaftNIjvPGOUsyG9p7AkJ4EztR/d0wWPa5Y4UPpnfKG8O1ZcdMWvGOc2ShBNhnHm0M9bKXykaZijqYheiWGoPAIL6DNaVnIjEggkhSxa6AnHJxD1xAM3/LXCKWMAvXiXvmyqgn90toBwXK+k25mGjSjEGilE6VRfOXUp02392JsEPuvGEfUWlbCSHioiCoCdDrC5RuVAPwMQNMKgIKEuPi4BGvWXO4qiCA8X9EODzWhmVxs6bHnlYwVugqO3DG4GK7wNIIApDRb9NCRzOlBX7O6tqG1/alLTILxoO0lvlY6yWF3FV1PQ4xKuFjTMNWO9Gc42Rle6b6tW7YdfuRwKZUhCYIkW+7PKIf2LOQDM4xL819jcV2OI55I5NH8+lDa3rCoZtwDtjvzCNoB5bFoMIOKgfI/DZZIIkHDxNZ7KgeKTY7LNCorlAKcI9QHU9Mu8Qd8FCHsp44tGzYMWONwb7sjEuTUm58UAp8Gx7BgJJcZdVyg22goLKeHFzZ4XDlY85X68GASDeFmTTe8WcCB6NagXxq5MIxr898dCUKGJFDBjA/sjtvtWTwMiK+5GKvv2vB8XTeWgeLpGh6gLlH50cdSZFE5juqtQRlHYCo8XtDCAxxP3XerW6PTIWMS0HMVu7pVAVSMJp7jOO8u27QTrSQ9nlzc4GxkeDgusMY3V0karnXfHhFUg8ObF+8u6mAGm6/M6HMOp4eMSECNKxL5wZKGbnR2gFNy/b5+qRUFEr1/sOEsJGrmu9ef8xxme821eQ0qB+C+lO4taOECA5qpNk3ZBJkBMiYBp2OZ3JnfwMWVehRZxqPtdk2mACFSxOpmLpr5QTnKieM8m66MF+AI0belFNRJRZDl6/NaKPnYjVZFZEsnUBaIKAvXZjfEuY/lzCbW43O9ESC8+E63jx40owlOJivQ5c9EIls1Trk8hX3QStdm1neDCY5lTJ0CHctmxiSgahclyXJ2cl2eY3gTx5rMfP2lmspS4Mo+PaaoehSXYidg9Ws+/4AtofqKkW8BRLIGflnDzQzbEzim4ODj9m3fRjggQZ1uk89OqIF6BZaUbiOPx9mugK9TtvTWVBzAZTcdOx4D1gdnbU39pkAkMXbzsJIuJh58dzxb4curju3mjElQIUkJ576qb6bLRc5cYfJ1C1W66O+U5PCVrTxBX0AyMR0ZioooMGDDMyxphcUA8wJobMLf8JNI5LT7Kzr58BxwAA64BgsfOX4QpyTAYi722gx/D44xuYC3sfmC+yBU9DRAkcjivZlYkI3ltWfhwE2L9Fl1V5KlVFrmx9jwm4B6rAcUV5aEuPPfNynIcnDqJKx22TYbwhxykBkJhEgpDyc3KshzYDbxXgGbXoBFi8fIiLJgTT35aUUzY2I4s0Y3wLV8oR5rbqbIFljHZO7pzAXQZDV0yQuS/HL7qJruHK5yMYpk7rUP3HYO4JQEmvZlvTWnbSGO58wsy5QYtj9OtNBFDro0onYNL4sRO60kwDHELsXa/3hBEFEQ8kgS6EKDyFLy+V8CSrmBOsDwEcMyBT+CI0scPYdzEmCyJyTt/oq+ZXjeDIO/6vh/BUiSxwCILFutr3YMK2E4GIpjGVeWNxWoP3FPonJIdF4A1qSwALPc/3t0g0JmMx5PQ/9gyn19W1n/ZgSnJEiiFZwckjXon7G1XDCbyaPpT3cwJAgQZatgDdk9tAh6HmzgXBgohsa5le/79xPQCsZ23wSISNdtlpBj01qXhJqMNIpIXdYNl+LedwdwSgKkSYNnURIiTn3fvAAMe65wp18jqGMdFKQkSdawwH5F0ClrEwXYRwSRPJNPh0UXYh3Pz7kJ9P4RIerKqsGVoCo9LmrRQwhP1nU71MhxBKckgKKhR5+TxFur+1SAk5a5Yv12JdCtMxAAAZ9o2M7uPtpto5gBDtFyjvWs++WuIIfmam4Cp4WEu3+Pb1wUvT1QlxNjWBgceu90sm1BkR7OSaDOHnRzpQXv/6JBQRPLupQcfgBLVYE80LhY9B89fJU9R2g02UISLMNX6r/sQqjNjaiMDIePk2Xg4gAfgupvoCD5v13TupRxhXiN6gdXPMX5P7utO77OgExIUAEdTry6oncVT4Y1l/7kSCy6GkURz7OUSMiGXiXNuN40a7eEcAxr5oo0/vi3WwkoUtQJq7pNXwe0/9Riw9oHRIy7EzilaxUvXWq2Arb8zCc4mxmbUZA1EtCpT4Tg3V91KOvOMtU/ORIDAQYi0FC5LD7/Y3B5N1AMLtrMNighy7Guvi3H7wlKsmJ2DbjHwMVqvEMOoMQawIIVLVG3Az9vVdZDZ7TZn6MBLfjh6K5ZIwET9Igkx1/dNLF5SRNb87PDMRJ4b6mCJCJ5+c/o6i76SQJvzzAmnjGX7jrv5IskmCpx/DierLIMdKBSMSRyWsjVP8e3K2GGerXGJwCwXffBNpDXJAGB29BjrmwYV6eYW60JR+Mw0YSmwFuIFHF0cp18jFn3EGi3g6nJuFcbsuTowxhwM6CF4ehxsgqNI1pMeH5uzeiWJeEcawggZzQcPryW4uymWSOB3hCGsyjEX1s7qlGF5lP+jRMxhQg9MFDQ6srC5h5QLVV7e5gjaAHl/P4BU/++E5oKsQPHodGsggZV4CzykDt7fxjYyIdjwU4FQchAEty+DsIC+I6QNRJwnsRWrBbJGn/pj697Bvx0lWAsiAZHoMzdwzWtfczaLBZ4Ho6nLgeWc/NrO+aXC6/i0ghY968JuKWQEv7g6OIRnSqg/CnlP9SzyjXgSi6lyc6OaMgaCVQV27bTkJSHB5fP2QkHCKJwg8EA9mr4+j7l8tlDESiZykyN6XjmfPU/WLD75qvY1y/dJxFLbPD5Xz9/t4Y7Y+IgcwvOTtVHxFSYGm0Gi814cJkdWSNBGcYY/lOOEIl8EGbTb2oMiERvHVHDE+xF9f44Y9NyrjxYL4xrmTYfzfzjXGgcJH+mezPpPgCoHyp/gwykJUYHX/5jSt863ixr4hgzNk0HYnoSWHP3IwnKqM0YWSMhS4CbxB79sUsxN0xtQOFM/2ZYxq1E40ET155+ERufTH1wymjFc6fV0KNCLXxAzVMaZSaymJIU9+Ts1vljA5oWTi/5eqDFyJldht+DXbaOrZPcJUGS055tG1PXjTHpzGiNmqD2PO9er/vY2esP3wlPTKUJCZicqmpgJekHP8CSLWi3SkJaSnLci4ubfhwT0MgX84Ap0U4BSbnfhIF579BgzEUSRDj4iMhy3MVFA9zQn0HTz9KD5Xgzw7kXq9520IQft5z4LwrOoBcE4EGkyVEAOMwF6LEKVkGwpqUmBF0/8c+a2Z90q+FjYnhwfBrbTQ+03k2ll6UqSs0Bco8EakiD8RS76+tq7ixvoq9Jx4TymQKWdfOp886QCdPnrPrz4PEbj6JEgFUQRasVOBAEUUh9/uTptROBa36aPLxPx7qlC9KpBhxGNLNM254BUFIIokGN/1JrCTlALpJgm0Mk6cmivqVodiGm/Oh7zvM8z3HgsKNag+FMHmWqN2vVa8jYb6dP++G77xeuXr9u3e/rVs6d9t20byd/+P4HfTo2LOOtVPaBwAbm++L6LFNwHJO/51GswucYuUeCBEFY9LaIkpy4/8depaCPBjNW/YfyBuE0eewNjB0zz/EcZ+I5r5IVKlas5FfYhePNNImQCpPttdP0d+OOg3QwmcxmjuGKjXykHrzuALlHAljPOJQhC5wIwYdmd/dTqvFoJQFkAP5GHz666algUyidBZgUx6VS9AfX5yhTSt+dE2CDuVD96UmY9uSYhdwjAXYXqCt7SLdKe3no227FDLOY8u7sAwTNSbSsaWorHfD2HHtYeCjOUgU2eUD/mfpphuCYIvX7TVpxneCOFMfIPRL0gPk+JeTg1HYlsJynWUkRhr8zEeKsQ2FDpxxp+il6WPnCzYZ8ufJEcIJ9xnGAN0UCQkp+tmdq14oeIMiQxEwdLqDZjQen5AT0VBdqkKjpVcrZp65m1+JNh0xYf/llbIr+aNcM8UZJILKY+HDf/GEtoLg0rqzhkVnelAvCoI4EVKuofXEEYVJJYf+uH01bf+pVdApaoZnijZIgyxYiJL64vGVK31pekP2puPwydP9kDxptoPhS4QOeM+Uv16z/pCV7bryITqEOOHuOnEO8URLQKS+KaTFPTi35uG1FL/ArgBs6q5o9M2jtMBCz/BUavfvR9I3nn72Ms9qdKJnHPd4kCZhUjMawlPzixrZvB7Sv4+OqhkNeHzhpQMgVlK3Zs1yjd4dOXbP/elBEGt0phxaL87lRwZskQRFHTNclQtSjS7sXjuzRuIK3OXdIoL2HzUFFqjTr+v7UtceuPQ5LtGIhRk0+7FsnAXPmaGa4JBEh7vmto+umDW3vX0p7rkhOAU5crxLVG7TvN+aHDYevPgxNQbnHV09dPbafM8EbJcF2Ljf9E9bDKeGPLgQunTCoY8PqZX3SV//VA0eObWeJGtDBf/EexSrUbNyp/6c/rtl74daTiFR9Z9WwDIrBWyYhHeDVCAkvb184/sf8ycPebVG3QrGC7uAYU5UlVRnYUTWIhSe/0U9ZjjN5e5epVKtpp36jv138+/6zNx+HwUkVWZB5J8hjEqiyhMyN2OC7F4/vWvvtqCHd2zSpV7NSad9CHmbVRKaWs7KrhGVZd+8C3n7lKtSs16R1h/ffnzpnxdajZ649eBENuyvoy/7fRAIqa5qID8PDEnn/2oUT+7dv/Hnm5NHv9WjXrHHjJk3q16pWpWq1alWrVa9WrXr1arUaNGn2Tt/+QyZOnb50/bZ9Ry5feBQUlkT3uNGdQK/Tf8TbIAHVlRKRxP8tCWHPHly/cOLgnl27d+/evHbZoiU/L1uyZNmSxUuWLv116849R86cPX/3wePQWPBKUvVPW4MN4a/NQV6ToNKAR5rhvxUdTudS/MsSHxkeGRUZERkZER4RGR4vqJt4oHqatsCZagy9dmgzj0mgnbWpbuUznDlUR6tiVyg2P9g79Ep1awQVf+VX6JB4TQ7ynAS1t3CwrZr8gH2ilQewt6qiU/adYU8VAvA3VC2oNGX7OsfIYxLUaI3ydtXNKcYuqPaeUkMPP1J+S01wUH+DCsPrUJDnJPzPxP+R8H8kUPw/uaFY9qthttwAAAAASUVORK5CYII=</Picture>
        <PictureScale>2</PictureScale>
      </Field>
      <Field>
        <Name>Field2</Name>
        <Section>1</Section>
        <Top>4395</Top>
        <Width>10680</Width>
        <Height>60</Height>
        <ZOrder>-1</ZOrder>
        <BackColor>14474460</BackColor>
        <BackStyle>1</BackStyle>
        <Font>
          <Name>Segoe UI</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field4</Name>
        <Section>1</Section>
        <Text>Nom:</Text>
        <Left>4680</Left>
        <Top>4680</Top>
        <Width>1710</Width>
        <Height>285</Height>
        <Align>2</Align>
        <MarginRight>150</MarginRight>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>CustomerFullNameField</Name>
        <Section>1</Section>
        <Text>FirstLastName</Text>
        <Calculated>-1</Calculated>
        <Left>6390</Left>
        <Top>4680</Top>
        <Width>4230</Width>
        <Height>285</Height>
        <Align>0</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field20</Name>
        <Section>1</Section>
        <Text>Customer</Text>
        <Top>4065</Top>
        <Width>10680</Width>
        <Height>435</Height>
        <ZOrder>3</ZOrder>
        <Align>6</Align>
        <BackColor />
        <BackStyle>1</BackStyle>
        <Visible>0</Visible>
        <Font>
          <Bold>-1</Bold>
          <Name>Microsoft Sans Serif</Name>
          <Size>11.25</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field10</Name>
        <Section>1</Section>
        <Text>Addresse:</Text>
        <Left>4680</Left>
        <Top>4965</Top>
        <Width>1710</Width>
        <Height>285</Height>
        <Align>2</Align>
        <MarginRight>150</MarginRight>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>CustomerHomeAddressField</Name>
        <Section>1</Section>
        <Text>Address</Text>
        <Calculated>-1</Calculated>
        <Left>6390</Left>
        <Top>4965</Top>
        <Width>4230</Width>
        <Height>285</Height>
        <Align>0</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field22</Name>
        <Section>1</Section>
        <Text>Phone:</Text>
        <Left>4680</Left>
        <Top>5310</Top>
        <Width>1710</Width>
        <Height>285</Height>
        <Align>2</Align>
        <MarginRight>150</MarginRight>
        <Visible>0</Visible>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>9.75</Size>
        </Font>
      </Field>
      <Field>
        <Name>PhoneField</Name>
        <Section>1</Section>
        <Text>Phone</Text>
        <Calculated>-1</Calculated>
        <Left>6390</Left>
        <Top>5310</Top>
        <Width>4215</Width>
        <Height>285</Height>
        <Align>0</Align>
        <Visible>0</Visible>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>9.75</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field11</Name>
        <Section>1</Section>
        <Top>6540</Top>
        <Width>10680</Width>
        <Height>60</Height>
        <ZOrder>-1</ZOrder>
        <BackColor>14474460</BackColor>
        <BackStyle>1</BackStyle>
        <Font>
          <Name>Segoe UI</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field3</Name>
        <Section>1</Section>
        <Text>Invoice Data</Text>
        <Top>6210</Top>
        <Width>10680</Width>
        <Height>435</Height>
        <ZOrder>2</ZOrder>
        <Align>6</Align>
        <BackColor />
        <BackStyle>1</BackStyle>
        <Visible>0</Visible>
        <Font>
          <Bold>-1</Bold>
          <Name>Microsoft Sans Serif</Name>
          <Size>11.25</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field23</Name>
        <Section>1</Section>
        <Text>Format(InvoiceDate, "dd/MM/yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>1710</Left>
        <Top>6780</Top>
        <Width>8970</Width>
        <Height>285</Height>
        <Align>0</Align>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>DescriptionField</Name>
        <Section>1</Section>
        <Text>Description</Text>
        <Calculated>-1</Calculated>
        <Left>1710</Left>
        <Top>7065</Top>
        <Width>8970</Width>
        <Height>285</Height>
        <Align>0</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field12</Name>
        <Section>1</Section>
        <Text>Date de Service:</Text>
        <Top>6780</Top>
        <Width>1690</Width>
        <Height>285</Height>
        <ZOrder>1</ZOrder>
        <Align>2</Align>
        <MarginRight>150</MarginRight>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field26</Name>
        <Section>1</Section>
        <Text>Mission:</Text>
        <Top>7725</Top>
        <Width>1710</Width>
        <Height>285</Height>
        <Align>2</Align>
        <MarginRight>150</MarginRight>
        <Visible>0</Visible>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>MissionField</Name>
        <Section>1</Section>
        <Text>Mission</Text>
        <Calculated>-1</Calculated>
        <Left>1710</Left>
        <Top>7725</Top>
        <Width>8970</Width>
        <Height>285</Height>
        <Align>0</Align>
        <Visible>0</Visible>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field16</Name>
        <Section>1</Section>
        <Text>Service:</Text>
        <Top>7065</Top>
        <Width>1690</Width>
        <Height>285</Height>
        <ZOrder>1</ZOrder>
        <Align>2</Align>
        <MarginRight>150</MarginRight>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field24</Name>
        <Section>1</Section>
        <Text>Service ID:</Text>
        <Top>8010</Top>
        <Width>1710</Width>
        <Height>285</Height>
        <Align>2</Align>
        <MarginRight>150</MarginRight>
        <Visible>0</Visible>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>ServiceIdField</Name>
        <Section>1</Section>
        <Text>ServiceId</Text>
        <Calculated>-1</Calculated>
        <Left>1710</Left>
        <Top>8010</Top>
        <Width>8970</Width>
        <Height>285</Height>
        <Align>0</Align>
        <Visible>0</Visible>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field6</Name>
        <Section>1</Section>
        <Left>6405</Left>
        <Top>8640</Top>
        <Width>4275</Width>
        <Height>1185</Height>
        <ZOrder>-4</ZOrder>
        <BackColor>14474460</BackColor>
        <BackStyle>1</BackStyle>
        <Font>
          <Name>Segoe UI</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field30</Name>
        <Section>1</Section>
        <Text>Total ht:</Text>
        <Left>6690</Left>
        <Top>8775</Top>
        <Width>2280</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginRight>350</MarginRight>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10.75</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field31</Name>
        <Section>1</Section>
        <Text>Amount</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>8970</Left>
        <Top>8775</Top>
        <Width>1710</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginRight>50</MarginRight>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10.75</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field21</Name>
        <Section>1</Section>
        <Text>"tva ("&amp; Vat &amp;"%):"</Text>
        <Calculated>-1</Calculated>
        <Left>6690</Left>
        <Top>9060</Top>
        <Width>2280</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginRight>350</MarginRight>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10.75</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field29</Name>
        <Section>1</Section>
        <Text>VatAmount</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>8970</Left>
        <Top>9060</Top>
        <Width>1710</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginRight>50</MarginRight>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10.75</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field7</Name>
        <Section>1</Section>
        <Text>Total ttc:</Text>
        <Left>6690</Left>
        <Top>9435</Top>
        <Width>2280</Width>
        <Height>330</Height>
        <Align>8</Align>
        <MarginRight>350</MarginRight>
        <Font>
          <Bold>-1</Bold>
          <Name>Microsoft Sans Serif</Name>
          <Size>14</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field28</Name>
        <Section>1</Section>
        <Text>TotalAmount</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>8970</Left>
        <Top>9435</Top>
        <Width>1710</Width>
        <Height>330</Height>
        <Align>8</Align>
        <MarginRight>25</MarginRight>
        <Font>
          <Bold>-1</Bold>
          <Name>Microsoft Sans Serif</Name>
          <Size>14</Size>
        </Font>
      </Field>
      <Field>
        <Name>CustomerHomeAddressField1</Name>
        <Section>1</Section>
        <Text>"Notes:  " &amp; Notes</Text>
        <Calculated>-1</Calculated>
        <Top>10260</Top>
        <Width>10545</Width>
        <Height>1710</Height>
        <Align>0</Align>
        <MarginTop>30</MarginTop>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>9.75</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field27</Name>
        <Section>1</Section>
        <Text>COMFORT TRANSFER A PARIS</Text>
        <Top>12810</Top>
        <Width>3555</Width>
        <Height>285</Height>
        <Align>6</Align>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>9.75</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field25</Name>
        <Section>1</Section>
        <Text>SARL au Capital de 10 000 €
SIREN: 80859286900016
TVA n°: FR00808592869
IBAN: ***************************
BIC: SOGEFRPP</Text>
        <Top>13095</Top>
        <Width>4845</Width>
        <Height>1140</Height>
        <Align>0</Align>
      </Field>
      <Field>
        <Name>Field5</Name>
        <Section>1</Section>
        <Text>Payment Type:</Text>
        <Top>9450</Top>
        <Width>1695</Width>
        <Height>285</Height>
        <ZOrder>1</ZOrder>
        <Align>2</Align>
        <MarginRight>150</MarginRight>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10</Size>
        </Font>
      </Field>
      <Field>
        <Name>DescriptionField1</Name>
        <Section>1</Section>
        <Text>PaymentType</Text>
        <Calculated>-1</Calculated>
        <Left>1710</Left>
        <Top>9450</Top>
        <Width>3195</Width>
        <Height>285</Height>
        <Align>0</Align>
        <WordWrap>0</WordWrap>
        <CanGrow>-1</CanGrow>
        <Font>
          <Name>Microsoft Sans Serif</Name>
          <Size>10</Size>
        </Font>
      </Field>
    </Fields>
  </Report>
  <!--Report *** Tasks ***-->
  <Report version="2.6.20093.53207">
    <Name>Tasks</Name>
    <ReportInfo>
      <Author>Peter</Author>
      <Title>Επισκέψεις</Title>
    </ReportInfo>
    <DataSource>
      <ConnectionString>Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;Initial Catalog=Anres;Data Source=MAIN\SQLEXPRESS</ConnectionString>
    </DataSource>
    <Layout>
      <Width>10695</Width>
      <MarginLeft>590.4</MarginLeft>
      <MarginTop>590.4</MarginTop>
      <MarginRight>590.4</MarginRight>
      <MarginBottom>590.4</MarginBottom>
      <Orientation>1</Orientation>
    </Layout>
    <Font>
      <Name>Verdana</Name>
      <Size>9</Size>
    </Font>
    <Groups>
      <Group>
        <Name>Total Group</Name>
      </Group>
      <Group>
        <Name>Year Group</Name>
        <GroupBy>Year(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Month Group</Name>
        <GroupBy>Month(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Contact Group</Name>
        <GroupBy>ContactId</GroupBy>
        <Sort>1</Sort>
      </Group>
    </Groups>
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Height>285</Height>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>1245</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>375</Height>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>500</Height>
      </Section>
      <Section>
        <Name>TotalGroupHeader</Name>
        <Type>5</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>TotalGroupFooter</Name>
        <Type>6</Type>
        <Height>360</Height>
      </Section>
      <Section>
        <Name>YearGroupHeader</Name>
        <Type>7</Type>
        <Height>420</Height>
      </Section>
      <Section>
        <Name>YearGroupFooter</Name>
        <Type>8</Type>
        <Height>360</Height>
      </Section>
      <Section>
        <Name>MonthGroupHeader</Name>
        <Type>9</Type>
        <Height>420</Height>
      </Section>
      <Section>
        <Name>MonthGroupFooter</Name>
        <Type>10</Type>
        <Height>360</Height>
      </Section>
      <Section>
        <Name>ContactGroupHeader</Name>
        <Type>11</Type>
        <Height>420</Height>
      </Section>
      <Section>
        <Name>ContactGroupFooter</Name>
        <Type>12</Type>
        <Height>360</Height>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>AmountCtl</Name>
        <Section>0</Section>
        <Text>TotalCost</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9120</Left>
        <Width>1545</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>CustomerIDCtl</Name>
        <Section>0</Section>
        <Text>ContactFullName</Text>
        <Calculated>-1</Calculated>
        <Left>3390</Left>
        <Width>2310</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>PaymentDateCtl</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>dd/MM/yyyy</Format>
        <Left>570</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrLeft1</Name>
        <Section>4</Section>
        <Text>Now()</Text>
        <Calculated>-1</Calculated>
        <Format>dd/MM/yyyy HH:mm</Format>
        <Top>75</Top>
        <Width>4515</Width>
        <Height>300</Height>
        <Align>0</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrRight1</Name>
        <Section>4</Section>
        <Text>"Σελίδα " &amp; [Page] &amp; " από " &amp; [Pages]</Text>
        <Calculated>-1</Calculated>
        <Left>6135</Left>
        <Top>75</Top>
        <Width>4560</Width>
        <Height>300</Height>
        <Align>2</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field2</Name>
        <Section>3</Section>
        <Top>255</Top>
        <Width>10680</Width>
        <Height>20</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <ForeColor />
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
        <Font>
          <Name>Arial</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field3</Name>
        <Section>3</Section>
        <Text>Ημ/νία</Text>
        <Calculated>-1</Calculated>
        <Left>570</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>Field4</Name>
        <Section>3</Section>
        <Text>Πελάτης</Text>
        <Calculated>-1</Calculated>
        <Left>3390</Left>
        <Width>2310</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>Field6</Name>
        <Section>3</Section>
        <Text>Ποσό</Text>
        <Calculated>-1</Calculated>
        <Left>9120</Left>
        <Width>1545</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>CompanyDataSubreportField</Name>
        <Section>1</Section>
        <Width>10680</Width>
        <Height>135</Height>
        <Visible>0</Visible>
        <CanGrow>-1</CanGrow>
        <Subreport>CompanyData Subreport</Subreport>
      </Field>
      <Field>
        <Name>titleLbl1</Name>
        <Section>1</Section>
        <Text>ΕΠΙΣΚΕΨΕΙΣ</Text>
        <Top>135</Top>
        <Width>10680</Width>
        <Height>600</Height>
        <Align>6</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>18</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field7</Name>
        <Section>1</Section>
        <Top>705</Top>
        <Width>10680</Width>
        <Height>45</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <ForeColor />
        <LineWidth>1</LineWidth>
        <Font>
          <Name>Arial</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>FilterField</Name>
        <Section>1</Section>
        <Text>Αφίξεις από {0} εως {1}</Text>
        <Top>780</Top>
        <Width>10680</Width>
        <Height>285</Height>
        <Align>6</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8.25</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field8</Name>
        <Section>10</Section>
        <Text>"Σύνολο "&amp;Format(StartTime, "MMMM yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>3270</Left>
        <Top>75</Top>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field9</Name>
        <Section>10</Section>
        <Left>5070</Left>
        <Top>90</Top>
        <Width>5595</Width>
        <Height>15</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
      </Field>
      <Field>
        <Name>Field11</Name>
        <Section>10</Section>
        <Text>SUM(TotalCost)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9120</Left>
        <Top>75</Top>
        <Width>1545</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>1</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field12</Name>
        <Section>9</Section>
        <Text>Format(StartTime, "MMMM yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>285</Left>
        <Top>60</Top>
        <Width>4125</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field13</Name>
        <Section>9</Section>
        <Left>285</Left>
        <Top>332</Top>
        <Width>2850</Width>
        <Height>15</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
      </Field>
      <Field>
        <Name>Field14</Name>
        <Section>7</Section>
        <Text>Format(StartTime, "yyyy")</Text>
        <Calculated>-1</Calculated>
        <Top>75</Top>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field15</Name>
        <Section>7</Section>
        <Top>332</Top>
        <Width>2850</Width>
        <Height>20</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
      </Field>
      <Field>
        <Name>Field16</Name>
        <Section>8</Section>
        <Text>"Σύνολο έτους "&amp;Format(StartTime, "yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>3270</Left>
        <Top>75</Top>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field17</Name>
        <Section>8</Section>
        <Left>5085</Left>
        <Top>90</Top>
        <Width>5595</Width>
        <Height>15</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
      </Field>
      <Field>
        <Name>Field19</Name>
        <Section>8</Section>
        <Text>SUM(TotalCost)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9105</Left>
        <Top>75</Top>
        <Width>1545</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>1</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field20</Name>
        <Section>6</Section>
        <Text>"Σύνολο:"</Text>
        <Calculated>-1</Calculated>
        <Left>7215</Left>
        <Top>75</Top>
        <Width>1755</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field21</Name>
        <Section>6</Section>
        <Left>5085</Left>
        <Top>90</Top>
        <Width>5595</Width>
        <Height>15</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
      </Field>
      <Field>
        <Name>Field23</Name>
        <Section>6</Section>
        <Text>SUM(TotalCost)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9120</Left>
        <Top>75</Top>
        <Width>1545</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>2</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>3</Section>
        <Text>Τεχνικός</Text>
        <Calculated>-1</Calculated>
        <Left>5835</Left>
        <Width>1425</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>CustomerIDCtl2</Name>
        <Section>0</Section>
        <Text>UserFullName</Text>
        <Calculated>-1</Calculated>
        <Left>5835</Left>
        <Width>1425</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field25</Name>
        <Section>3</Section>
        <Text>Έναρξη</Text>
        <Calculated>-1</Calculated>
        <Left>1710</Left>
        <Width>690</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>PaymentDateCtl1</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>1710</Left>
        <Width>690</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field5</Name>
        <Section>3</Section>
        <Text>Λήξη</Text>
        <Calculated>-1</Calculated>
        <Left>2535</Left>
        <Width>720</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>PaymentDateCtl2</Name>
        <Section>0</Section>
        <Text>EndTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>2535</Left>
        <Width>720</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field10</Name>
        <Section>3</Section>
        <Text>Κατηγορία</Text>
        <Calculated>-1</Calculated>
        <Left>7410</Left>
        <Width>1560</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>CustomerIDCtl1</Name>
        <Section>0</Section>
        <Text>TaskCategoryName</Text>
        <Calculated>-1</Calculated>
        <Left>7410</Left>
        <Width>1560</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field18</Name>
        <Section>11</Section>
        <Text>ContactFullName</Text>
        <Calculated>-1</Calculated>
        <Left>585</Left>
        <Top>60</Top>
        <Width>4125</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field22</Name>
        <Section>11</Section>
        <Left>554.75205880608257</Left>
        <Top>332</Top>
        <Width>2850</Width>
        <Height>20</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
      </Field>
      <Field>
        <Name>Field24</Name>
        <Section>12</Section>
        <Text>"Σύνολο "&amp;ContactFullName&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>3270</Left>
        <Top>75</Top>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field26</Name>
        <Section>12</Section>
        <Left>5070</Left>
        <Top>90</Top>
        <Width>5595</Width>
        <Height>15</Height>
        <BackColor>6908265</BackColor>
        <BackStyle>1</BackStyle>
        <BorderStyle>1</BorderStyle>
        <BorderColor>Transparent</BorderColor>
      </Field>
      <Field>
        <Name>Field27</Name>
        <Section>12</Section>
        <Text>SUM(TotalCost)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9120</Left>
        <Top>75</Top>
        <Width>1545</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>1</RunningSum>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field28</Name>
        <Section>9</Section>
        <Text>Month(StartTime)</Text>
        <Calculated>-1</Calculated>
        <Left>4845</Left>
        <Top>60</Top>
        <Width>4125</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field29</Name>
        <Section>11</Section>
        <Text>ContactId</Text>
        <Calculated>-1</Calculated>
        <Left>4845</Left>
        <Top>60</Top>
        <Width>4125</Width>
        <Height>285</Height>
        <Align>6</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
    </Fields>
  </Report>
  <!--Report *** Contacts ***-->
  <Report version="2.6.20093.53207">
    <Name>Contacts</Name>
    <ReportInfo>
      <Author>peter</Author>
    </ReportInfo>
    <DataSource>
      <ConnectionString>Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;Initial Catalog=MentalView;Data Source=MAIN\SQLEXPRESS</ConnectionString>
      <RecordSource>Contacts</RecordSource>
    </DataSource>
    <Layout>
      <Width>14685</Width>
      <MarginLeft>561.6</MarginLeft>
      <MarginTop>561.6</MarginTop>
      <MarginRight>561.6</MarginRight>
      <MarginBottom>561.6</MarginBottom>
      <Orientation>2</Orientation>
    </Layout>
    <Font>
      <Name>Segoe UI</Name>
      <Size>9</Size>
    </Font>
    <OnOpen>' -- style script start
_styleCtr = 0
' -- style script end
</OnOpen>
    <Groups />
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Height>375</Height>
        <OnPrint>' -- style script start
detail.BackColor = iif(_styleCtr mod 2 = 0, rgb(255,255,255),rgb(240,240,240))
_styleCtr = _styleCtr + 1
' -- style script end
</OnPrint>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>1070</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>390</Height>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>500</Height>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>TitleField</Name>
        <Section>1</Section>
        <Text>Πελάτες</Text>
        <Top>135</Top>
        <Width>14670</Width>
        <Height>570</Height>
        <Align>6</Align>
        <Font>
          <Name>Segoe UI</Name>
          <Size>20</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>0</Section>
        <Text>FullName</Text>
        <Calculated>-1</Calculated>
        <Width>2700</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field2</Name>
        <Section>3</Section>
        <Text>Ονοματεπώνυμο</Text>
        <Width>2700</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>ftrRight1</Name>
        <Section>4</Section>
        <Text>"Σελίδα " &amp; [Page] &amp; " από " &amp; [Pages]</Text>
        <Calculated>-1</Calculated>
        <Left>9975</Left>
        <Top>135</Top>
        <Width>4560</Width>
        <Height>300</Height>
        <Align>2</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrLeft1</Name>
        <Section>4</Section>
        <Text>Now()</Text>
        <Calculated>-1</Calculated>
        <Format>dd/MM/yyyy HH:mm</Format>
        <Top>135</Top>
        <Width>4515</Width>
        <Height>300</Height>
        <Align>0</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ReportInfoField</Name>
        <Section>1</Section>
        <Text>&lt;ReportInfo&gt;</Text>
        <Top>705</Top>
        <Width>14670</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field3</Name>
        <Section>3</Section>
        <Text>Τηλέφωνο</Text>
        <Left>5130</Left>
        <Width>1290</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field4</Name>
        <Section>0</Section>
        <Text>Phone1</Text>
        <Calculated>-1</Calculated>
        <Left>5130</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>6</Align>
      </Field>
      <Field>
        <Name>Field5</Name>
        <Section>3</Section>
        <Text>Κινητό</Text>
        <Left>6555</Left>
        <Width>1290</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field6</Name>
        <Section>0</Section>
        <Text>Mobile1</Text>
        <Calculated>-1</Calculated>
        <Left>6555</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>6</Align>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field7</Name>
        <Section>3</Section>
        <Top>285</Top>
        <Width>14670</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field8</Name>
        <Section>3</Section>
        <Text>Θεραπευτής</Text>
        <Left>7980</Left>
        <Width>2145</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field9</Name>
        <Section>0</Section>
        <Text>TherapistFullName</Text>
        <Calculated>-1</Calculated>
        <Left>7980</Left>
        <Width>2145</Width>
        <Height>285</Height>
        <Align>6</Align>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field10</Name>
        <Section>3</Section>
        <Text>Διαγνωστικά</Text>
        <Left>10260</Left>
        <Width>2145</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field11</Name>
        <Section>0</Section>
        <Text>DiagnosticianFullName</Text>
        <Calculated>-1</Calculated>
        <Left>10260</Left>
        <Width>2145</Width>
        <Height>285</Height>
        <Align>6</Align>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field12</Name>
        <Section>3</Section>
        <Text>Κλινική Εποπτεία</Text>
        <Left>12540</Left>
        <Width>2145</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field13</Name>
        <Section>0</Section>
        <Text>ClinicSupervisorFullName</Text>
        <Calculated>-1</Calculated>
        <Left>12540</Left>
        <Width>2145</Width>
        <Height>285</Height>
        <Align>6</Align>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field14</Name>
        <Section>3</Section>
        <Text>Ενεργός</Text>
        <Left>2850</Left>
        <Width>855</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field17</Name>
        <Section>3</Section>
        <Text>Σε αναμονή</Text>
        <Left>3840</Left>
        <Width>1005</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field15</Name>
        <Section>0</Section>
        <Text>Iif(Active,"Ναι","Όχι")</Text>
        <Calculated>-1</Calculated>
        <Left>2850</Left>
        <Width>855</Width>
        <Height>285</Height>
        <Align>7</Align>
      </Field>
      <Field>
        <Name>Field16</Name>
        <Section>0</Section>
        <Text>Iif(Waiting,"Ναι","Όχι")</Text>
        <Calculated>-1</Calculated>
        <Left>3840</Left>
        <Width>855</Width>
        <Height>285</Height>
        <Align>7</Align>
      </Field>
    </Fields>
  </Report>
  <!--Report *** TherapistContacts ***-->
  <Report version="2.6.20093.53207">
    <Name>TherapistContacts</Name>
    <ReportInfo>
      <Author>peter</Author>
    </ReportInfo>
    <DataSource>
      <ConnectionString>Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;Initial Catalog=MentalView;Data Source=MAIN\SQLEXPRESS</ConnectionString>
      <RecordSource>Contacts</RecordSource>
    </DataSource>
    <Layout>
      <Width>14685</Width>
      <MarginLeft>561.6</MarginLeft>
      <MarginTop>561.6</MarginTop>
      <MarginRight>561.6</MarginRight>
      <MarginBottom>561.6</MarginBottom>
      <Orientation>2</Orientation>
      <PaperSize>9</PaperSize>
    </Layout>
    <Font>
      <Name>Segoe UI</Name>
      <Size>9</Size>
    </Font>
    <OnOpen>' -- style script start
_styleCtr = 0
' -- style script end
</OnOpen>
    <Groups />
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Height>285</Height>
        <OnPrint>' -- style script start
detail.BackColor = iif(_styleCtr mod 2 = 0, rgb(255,255,255),rgb(240,240,240))
_styleCtr = _styleCtr + 1
' -- style script end
</OnPrint>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>1190</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>390</Height>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>500</Height>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>TitleField</Name>
        <Section>1</Section>
        <Text>Πελάτες Θεραπευτή</Text>
        <Top>135</Top>
        <Width>14670</Width>
        <Height>570</Height>
        <Align>6</Align>
        <Font>
          <Name>Segoe UI</Name>
          <Size>20</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>0</Section>
        <Text>FullName</Text>
        <Calculated>-1</Calculated>
        <Width>2700</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field2</Name>
        <Section>3</Section>
        <Text>Ονοματεπώνυμο</Text>
        <Width>2700</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>ftrRight1</Name>
        <Section>4</Section>
        <Text>"Σελίδα " &amp; [Page] &amp; " από " &amp; [Pages]</Text>
        <Calculated>-1</Calculated>
        <Left>9975</Left>
        <Top>135</Top>
        <Width>4560</Width>
        <Height>300</Height>
        <Align>2</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrLeft1</Name>
        <Section>4</Section>
        <Text>Now()</Text>
        <Calculated>-1</Calculated>
        <Format>dd/MM/yyyy HH:mm</Format>
        <Top>135</Top>
        <Width>4515</Width>
        <Height>300</Height>
        <Align>0</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ReportInfoField</Name>
        <Section>1</Section>
        <Text>&lt;ReportInfo&gt;</Text>
        <Top>705</Top>
        <Width>14670</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field3</Name>
        <Section>3</Section>
        <Text>Τηλέφωνο</Text>
        <Left>5130</Left>
        <Width>1290</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field4</Name>
        <Section>0</Section>
        <Text>Phone1</Text>
        <Calculated>-1</Calculated>
        <Left>5130</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>6</Align>
      </Field>
      <Field>
        <Name>Field5</Name>
        <Section>3</Section>
        <Text>Κινητό</Text>
        <Left>6555</Left>
        <Width>1290</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field6</Name>
        <Section>0</Section>
        <Text>Mobile1</Text>
        <Calculated>-1</Calculated>
        <Left>6555</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>6</Align>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field7</Name>
        <Section>3</Section>
        <Top>285</Top>
        <Width>14670</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field8</Name>
        <Section>3</Section>
        <Text>Θεραπευτής</Text>
        <Left>7980</Left>
        <Width>2145</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field9</Name>
        <Section>0</Section>
        <Text>TherapistFullName</Text>
        <Calculated>-1</Calculated>
        <Left>7980</Left>
        <Width>2145</Width>
        <Height>285</Height>
        <Align>6</Align>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field10</Name>
        <Section>3</Section>
        <Text>Διαγνωστικά</Text>
        <Left>10260</Left>
        <Width>2145</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field11</Name>
        <Section>0</Section>
        <Text>DiagnosticianFullName</Text>
        <Calculated>-1</Calculated>
        <Left>10260</Left>
        <Width>2145</Width>
        <Height>285</Height>
        <Align>6</Align>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field12</Name>
        <Section>3</Section>
        <Text>Κλινική Εποπτεία</Text>
        <Left>12540</Left>
        <Width>2145</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field13</Name>
        <Section>0</Section>
        <Text>ClinicSupervisorFullName</Text>
        <Calculated>-1</Calculated>
        <Left>12540</Left>
        <Width>2145</Width>
        <Height>285</Height>
        <Align>6</Align>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field14</Name>
        <Section>3</Section>
        <Text>Ενεργός</Text>
        <Left>2850</Left>
        <Width>855</Width>
        <Height>285</Height>
        <Align>1</Align>
      </Field>
      <Field>
        <Name>Field17</Name>
        <Section>3</Section>
        <Text>Σε αναμονή</Text>
        <Left>3840</Left>
        <Width>1005</Width>
        <Height>285</Height>
        <Align>1</Align>
      </Field>
      <Field>
        <Name>Field15</Name>
        <Section>0</Section>
        <Text>Iif(Active,"Ναι","Όχι")</Text>
        <Calculated>-1</Calculated>
        <Left>2850</Left>
        <Width>855</Width>
        <Height>285</Height>
        <Align>7</Align>
      </Field>
      <Field>
        <Name>Field16</Name>
        <Section>0</Section>
        <Text>Iif(Waiting,"Ναι","Όχι")</Text>
        <Calculated>-1</Calculated>
        <Left>3840</Left>
        <Width>855</Width>
        <Height>285</Height>
        <Align>7</Align>
      </Field>
    </Fields>
  </Report>
  <!--Report *** Appointments ***-->
  <Report version="2.6.20093.53207">
    <Name>Appointments</Name>
    <ReportInfo>
      <Author>peter</Author>
    </ReportInfo>
    <DataSource>
      <ConnectionString>Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;Initial Catalog=MentalView;Data Source=MAIN\SQLEXPRESS</ConnectionString>
      <RecordSource>Appointments</RecordSource>
    </DataSource>
    <Layout>
      <Width>14955</Width>
      <MarginLeft>561.6</MarginLeft>
      <MarginTop>561.6</MarginTop>
      <MarginRight>561.6</MarginRight>
      <MarginBottom>561.6</MarginBottom>
      <Orientation>2</Orientation>
    </Layout>
    <Font>
      <Name>Segoe UI</Name>
      <Size>9</Size>
    </Font>
    <OnOpen>' -- style script start
_styleCtr = 0
' -- style script end
</OnOpen>
    <Groups>
      <Group>
        <Name>UserFullName</Name>
        <GroupBy>UserFullName</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Year Group</Name>
        <GroupBy>Year(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Month Group</Name>
        <GroupBy>Month(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
    </Groups>
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Height>285</Height>
        <OnFormat>RoomField.BackColor = Iif(LEN(RoomBackgroundColor)&gt;0,RoomBackgroundColor,"#fff") </OnFormat>
        <OnPrint>' -- style script start
detail.BackColor = iif(_styleCtr mod 2 = 0, rgb(255,255,255),rgb(240,240,240))
_styleCtr = _styleCtr + 1
' -- style script end
</OnPrint>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>1070</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>405</Height>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>500</Height>
      </Section>
      <Section>
        <Name>UserFullName_Header</Name>
        <Type>5</Type>
        <Height>435</Height>
      </Section>
      <Section>
        <Name>UserFullName_Footer</Name>
        <Type>6</Type>
        <Height>285</Height>
      </Section>
      <Section>
        <Name>Year Group_Header</Name>
        <Type>7</Type>
        <Height>420</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Year Group_Footer</Name>
        <Type>8</Type>
        <Height>300</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Month Group_Header</Name>
        <Type>9</Type>
        <Height>390</Height>
      </Section>
      <Section>
        <Name>Month Group_Footer</Name>
        <Type>10</Type>
        <Height>285</Height>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>Field6</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Short Date</Format>
        <Left>420</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field8</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>1695</Left>
        <Width>570</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field15</Name>
        <Section>0</Section>
        <Text>EndTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>2415</Left>
        <Width>720</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>ContactFullNameField</Name>
        <Section>0</Section>
        <Text>ContactFullName</Text>
        <Calculated>-1</Calculated>
        <Left>3270</Left>
        <Width>2280</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field17</Name>
        <Section>0</Section>
        <Text>Price</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>11535</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>TitleField</Name>
        <Section>1</Section>
        <Text>Συνεδρίες</Text>
        <Top>135</Top>
        <Width>11115</Width>
        <Height>570</Height>
        <Align>6</Align>
        <Font>
          <Name>Segoe UI</Name>
          <Size>20</Size>
        </Font>
      </Field>
      <Field>
        <Name>ReportInfoField</Name>
        <Section>1</Section>
        <Text>&lt;ReportInfo&gt;</Text>
        <Top>705</Top>
        <Width>11115</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field5</Name>
        <Section>3</Section>
        <Text>Ημερομηνία</Text>
        <Left>420</Left>
        <Width>1140</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field9</Name>
        <Section>3</Section>
        <Text>Έναρξη</Text>
        <Left>1695</Left>
        <Width>720</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field14</Name>
        <Section>3</Section>
        <Text>Λήξη</Text>
        <Left>2415</Left>
        <Width>720</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field2</Name>
        <Section>3</Section>
        <Text>Πελάτης</Text>
        <Left>3270</Left>
        <Width>2280</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field16</Name>
        <Section>3</Section>
        <Text>Κόστος</Text>
        <Left>11535</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>2</Align>
      </Field>
      <Field>
        <Name>Field7</Name>
        <Section>3</Section>
        <Top>285</Top>
        <Width>14955</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>ftrLeft1</Name>
        <Section>4</Section>
        <Text>Now()</Text>
        <Calculated>-1</Calculated>
        <Format>dd/MM/yyyy HH:mm</Format>
        <Top>135</Top>
        <Width>4515</Width>
        <Height>300</Height>
        <Align>0</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrRight1</Name>
        <Section>4</Section>
        <Text>"Σελίδα " &amp; [Page] &amp; " από " &amp; [Pages]</Text>
        <Calculated>-1</Calculated>
        <Left>10545</Left>
        <Top>135</Top>
        <Width>4410</Width>
        <Height>300</Height>
        <Align>2</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field3</Name>
        <Section>5</Section>
        <Text>UserFullName</Text>
        <Calculated>-1</Calculated>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field4</Name>
        <Section>5</Section>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field23</Name>
        <Section>6</Section>
        <Text>"Σύνολο "&amp; UserFullName &amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>4095</Left>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field20</Name>
        <Section>6</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>11535</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field12</Name>
        <Section>7</Section>
        <Text>Format(StartTime, "yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>285</Left>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field11</Name>
        <Section>7</Section>
        <Left>285</Left>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field21</Name>
        <Section>8</Section>
        <Text>"Σύνολο έτους "&amp;Format(StartTime, "yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>4095</Left>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field19</Name>
        <Section>8</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>11535</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field10</Name>
        <Section>9</Section>
        <Text>Format(StartTime, "MMMM yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>420</Left>
        <Width>3990</Width>
        <Height>285</Height>
        <ZOrder>-1</ZOrder>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field13</Name>
        <Section>9</Section>
        <Left>420</Left>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field22</Name>
        <Section>10</Section>
        <Text>"Σύνολο μήνα "&amp;Format(StartTime, "MMMM yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>4095</Left>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field18</Name>
        <Section>10</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>11535</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field24</Name>
        <Section>0</Section>
        <Text>Iif(PaymentType="Card","Κάρτα",Iif(PaymentType="Cash","Μετρητά", Iif(PaymentType="Deposit","Μεταφ./Κατάθ. σε λογαρ.", Iif(PaymentType="Pending","Εκκρεμεί", ""))))</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9690</Left>
        <Width>1695</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field25</Name>
        <Section>3</Section>
        <Text>Τρόπος Πληρωμής</Text>
        <Left>9690</Left>
        <Width>1695</Width>
        <Height>285</Height>
        <Align>2</Align>
      </Field>
      <Field>
        <Name>Field28</Name>
        <Section>3</Section>
        <Text>Κρατήσεις</Text>
        <Left>12825</Left>
        <Width>990</Width>
        <Height>270</Height>
        <Align>2</Align>
      </Field>
      <Field>
        <Name>Field29</Name>
        <Section>0</Section>
        <Text>Deductions</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>12825</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field30</Name>
        <Section>10</Section>
        <Text>SUM(Deductions)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>12825</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field31</Name>
        <Section>8</Section>
        <Text>SUM(Deductions)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>12825</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field32</Name>
        <Section>6</Section>
        <Text>SUM(Deductions)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>12825</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field33</Name>
        <Section>3</Section>
        <Text>Υπόλοιπο</Text>
        <Left>13965</Left>
        <Width>990</Width>
        <Height>270</Height>
        <Align>2</Align>
      </Field>
      <Field>
        <Name>Field34</Name>
        <Section>0</Section>
        <Text>Price-Deductions</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13965</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field35</Name>
        <Section>10</Section>
        <Text>SUM(Price-Deductions)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13965</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field36</Name>
        <Section>8</Section>
        <Text>SUM(Price-Deductions)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13965</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field37</Name>
        <Section>6</Section>
        <Text>SUM(Price-Deductions)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>13965</Left>
        <Width>960</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field38</Name>
        <Section>3</Section>
        <Text>Ακυρωμένη</Text>
        <Left>7695</Left>
        <Width>1860</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field39</Name>
        <Section>0</Section>
        <Text>IIF(Canceled, "Ακυρ."+IIF(ChargeableCancellation, " με χρεώση", " χωρίς χρέωση"), "")</Text>
        <Calculated>-1</Calculated>
        <Left>7695</Left>
        <Width>1860</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>3</Section>
        <Text>Τόπος Συνεδρίας</Text>
        <Left>5700</Left>
        <Width>1710</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>RoomField</Name>
        <Section>0</Section>
        <Calculated>-1</Calculated>
        <Left>5700</Left>
        <Top>30</Top>
        <Width>225</Width>
        <Height>225</Height>
        <Align>6</Align>
      </Field>
    </Fields>
  </Report>
  <!--Report *** AppointmentsInDebt ***-->
  <Report version="2.6.20093.53207">
    <Name>AppointmentsInDebt</Name>
    <ReportInfo>
      <Author>peter</Author>
    </ReportInfo>
    <DataSource>
      <ConnectionString>Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;Initial Catalog=MentalView;Data Source=MAIN\SQLEXPRESS</ConnectionString>
      <RecordSource>Appointments</RecordSource>
    </DataSource>
    <Layout>
      <Width>11130</Width>
      <MarginLeft>561.6</MarginLeft>
      <MarginTop>561.6</MarginTop>
      <MarginRight>561.6</MarginRight>
      <MarginBottom>561.6</MarginBottom>
      <Orientation>1</Orientation>
    </Layout>
    <Font>
      <Name>Segoe UI</Name>
      <Size>9</Size>
    </Font>
    <OnOpen>' -- style script start
_styleCtr = 0
' -- style script end
</OnOpen>
    <Groups>
      <Group>
        <Name>UserFullName</Name>
        <GroupBy>UserFullName</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Year Group</Name>
        <GroupBy>Year(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Month Group</Name>
        <GroupBy>Month(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
    </Groups>
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Height>285</Height>
        <OnPrint>' -- style script start
detail.BackColor = iif(_styleCtr mod 2 = 0, rgb(255,255,255),rgb(240,240,240))
_styleCtr = _styleCtr + 1
' -- style script end
</OnPrint>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>1070</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>465</Height>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>500</Height>
      </Section>
      <Section>
        <Name>UserFullName_Header</Name>
        <Type>5</Type>
        <Height>435</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>UserFullName_Footer</Name>
        <Type>6</Type>
        <Height>390</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Year Group_Header</Name>
        <Type>7</Type>
        <Height>420</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Year Group_Footer</Name>
        <Type>8</Type>
        <Height>315</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Month Group_Header</Name>
        <Type>9</Type>
        <Height>390</Height>
      </Section>
      <Section>
        <Name>Month Group_Footer</Name>
        <Type>10</Type>
        <Height>285</Height>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>Field6</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Short Date</Format>
        <Left>1710</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field8</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>2985</Left>
        <Width>720</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field15</Name>
        <Section>0</Section>
        <Text>EndTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>3840</Left>
        <Width>720</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>0</Section>
        <Text>ContactFullName</Text>
        <Calculated>-1</Calculated>
        <Left>4695</Left>
        <Width>2715</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field17</Name>
        <Section>0</Section>
        <Text>Price</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>7545</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>TitleField</Name>
        <Section>1</Section>
        <Text>Πληρωμές σε Εκκρεμότητα</Text>
        <Top>135</Top>
        <Width>11115</Width>
        <Height>570</Height>
        <Align>6</Align>
        <Font>
          <Name>Segoe UI</Name>
          <Size>20</Size>
        </Font>
      </Field>
      <Field>
        <Name>ReportInfoField</Name>
        <Section>1</Section>
        <Text>&lt;ReportInfo&gt;</Text>
        <Top>705</Top>
        <Width>11115</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field5</Name>
        <Section>3</Section>
        <Text>Ημερομηνία</Text>
        <Left>1710</Left>
        <Width>1140</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field9</Name>
        <Section>3</Section>
        <Text>Έναρξη</Text>
        <Left>2985</Left>
        <Width>720</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field14</Name>
        <Section>3</Section>
        <Text>Λήξη</Text>
        <Left>3840</Left>
        <Width>720</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field2</Name>
        <Section>3</Section>
        <Text>Πελάτης</Text>
        <Left>4695</Left>
        <Width>2715</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field16</Name>
        <Section>3</Section>
        <Text>Κόστος</Text>
        <Left>7545</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>2</Align>
      </Field>
      <Field>
        <Name>Field7</Name>
        <Section>3</Section>
        <Top>285</Top>
        <Width>11115</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>ftrLeft1</Name>
        <Section>4</Section>
        <Text>Now()</Text>
        <Calculated>-1</Calculated>
        <Format>dd/MM/yyyy HH:mm</Format>
        <Top>135</Top>
        <Width>4515</Width>
        <Height>300</Height>
        <Align>0</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrRight1</Name>
        <Section>4</Section>
        <Text>"Σελίδα " &amp; [Page] &amp; " από " &amp; [Pages]</Text>
        <Calculated>-1</Calculated>
        <Left>6540</Left>
        <Top>135</Top>
        <Width>4560</Width>
        <Height>300</Height>
        <Align>2</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field3</Name>
        <Section>5</Section>
        <Text>UserFullName</Text>
        <Calculated>-1</Calculated>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field4</Name>
        <Section>5</Section>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field23</Name>
        <Section>6</Section>
        <Text>"Σύνολο "&amp; UserFullName &amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>1995</Left>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field20</Name>
        <Section>6</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>7545</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field12</Name>
        <Section>7</Section>
        <Text>Format(StartTime, "yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>285</Left>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field11</Name>
        <Section>7</Section>
        <Left>285</Left>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field21</Name>
        <Section>8</Section>
        <Text>"Σύνολο έτους "&amp;Format(StartTime, "yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>1995</Left>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field19</Name>
        <Section>8</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>7545</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field10</Name>
        <Section>9</Section>
        <Text>Format(StartTime, "MMMM yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>570</Left>
        <Width>3990</Width>
        <Height>285</Height>
        <ZOrder>-1</ZOrder>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field13</Name>
        <Section>9</Section>
        <Left>570</Left>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field22</Name>
        <Section>10</Section>
        <Text>"Σύνολο μήνα "&amp;Format(StartTime, "MMMM yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>1995</Left>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field18</Name>
        <Section>10</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>7545</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
    </Fields>
  </Report>
  <!--Report *** CanceledAppointments ***-->
  <Report version="2.6.20093.53207">
    <Name>CanceledAppointments</Name>
    <ReportInfo>
      <Author>peter</Author>
    </ReportInfo>
    <DataSource>
      <ConnectionString>Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;Initial Catalog=MentalView;Data Source=MAIN\SQLEXPRESS</ConnectionString>
      <RecordSource>Appointments</RecordSource>
    </DataSource>
    <Layout>
      <Width>10860</Width>
      <MarginLeft>561.6</MarginLeft>
      <MarginTop>561.6</MarginTop>
      <MarginRight>561.6</MarginRight>
      <MarginBottom>561.6</MarginBottom>
      <Orientation>1</Orientation>
      <PaperSize>9</PaperSize>
    </Layout>
    <Font>
      <Name>Segoe UI</Name>
      <Size>9</Size>
    </Font>
    <OnOpen>' -- style script start
_styleCtr = 0
' -- style script end
</OnOpen>
    <Groups>
      <Group>
        <Name>ContactFullName</Name>
        <GroupBy>ContactFullName</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>UserFullName</Name>
        <GroupBy>UserFullName</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Year Group</Name>
        <GroupBy>Year(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Month Group</Name>
        <GroupBy>Month(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
    </Groups>
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Height>285</Height>
        <OnPrint>' -- style script start
detail.BackColor = iif(_styleCtr mod 2 = 0, rgb(255,255,255),rgb(240,240,240))
_styleCtr = _styleCtr + 1
' -- style script end
</OnPrint>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>1070</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>465</Height>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>500</Height>
      </Section>
      <Section>
        <Name>ContactFullName_Header</Name>
        <Type>5</Type>
        <Height>420</Height>
      </Section>
      <Section>
        <Name>ContactFullName_Footer</Name>
        <Type>6</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>UserFullName_Header</Name>
        <Type>7</Type>
        <Height>435</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>UserFullName_Footer</Name>
        <Type>8</Type>
        <Height>390</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Year Group_Header</Name>
        <Type>9</Type>
        <Height>420</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Year Group_Footer</Name>
        <Type>10</Type>
        <Height>315</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Month Group_Header</Name>
        <Type>11</Type>
        <Height>390</Height>
      </Section>
      <Section>
        <Name>Month Group_Footer</Name>
        <Type>12</Type>
        <Height>285</Height>
        <Visible>0</Visible>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>Field6</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Short Date</Format>
        <Left>1710</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field8</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>2985</Left>
        <Width>720</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field15</Name>
        <Section>0</Section>
        <Text>EndTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>3840</Left>
        <Width>720</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>0</Section>
        <Text>ContactFullName</Text>
        <Calculated>-1</Calculated>
        <Left>4695</Left>
        <Width>2715</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field17</Name>
        <Section>0</Section>
        <Text>Price</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9570</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <Visible>0</Visible>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>TitleField</Name>
        <Section>1</Section>
        <Text>Ακυρωμένες Συνεδρίες</Text>
        <Top>135</Top>
        <Width>10830</Width>
        <Height>570</Height>
        <Align>6</Align>
        <Font>
          <Name>Segoe UI</Name>
          <Size>20</Size>
        </Font>
      </Field>
      <Field>
        <Name>ReportInfoField</Name>
        <Section>1</Section>
        <Text>&lt;ReportInfo&gt;</Text>
        <Top>705</Top>
        <Width>10830</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field5</Name>
        <Section>3</Section>
        <Text>Ημερομηνία</Text>
        <Left>1710</Left>
        <Width>1140</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field9</Name>
        <Section>3</Section>
        <Text>Έναρξη</Text>
        <Left>2985</Left>
        <Width>720</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field14</Name>
        <Section>3</Section>
        <Text>Λήξη</Text>
        <Left>3840</Left>
        <Width>720</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field2</Name>
        <Section>3</Section>
        <Text>Πελάτης</Text>
        <Left>4695</Left>
        <Width>2715</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field16</Name>
        <Section>3</Section>
        <Text>Κόστος</Text>
        <Left>9570</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>2</Align>
        <Visible>0</Visible>
      </Field>
      <Field>
        <Name>Field7</Name>
        <Section>3</Section>
        <Top>285</Top>
        <Width>10830</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>ftrLeft1</Name>
        <Section>4</Section>
        <Text>Now()</Text>
        <Calculated>-1</Calculated>
        <Format>dd/MM/yyyy HH:mm</Format>
        <Top>135</Top>
        <Width>4515</Width>
        <Height>300</Height>
        <Align>0</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrRight1</Name>
        <Section>4</Section>
        <Text>"Σελίδα " &amp; [Page] &amp; " από " &amp; [Pages]</Text>
        <Calculated>-1</Calculated>
        <Left>6540</Left>
        <Top>135</Top>
        <Width>4290</Width>
        <Height>300</Height>
        <Align>2</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field3</Name>
        <Section>7</Section>
        <Text>UserFullName</Text>
        <Calculated>-1</Calculated>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field4</Name>
        <Section>7</Section>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field23</Name>
        <Section>8</Section>
        <Text>"Σύνολο "&amp; UserFullName &amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>1995</Left>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field20</Name>
        <Section>8</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9570</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field12</Name>
        <Section>9</Section>
        <Text>Format(StartTime, "yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>285</Left>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field11</Name>
        <Section>9</Section>
        <Left>285</Left>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field21</Name>
        <Section>10</Section>
        <Text>"Σύνολο έτους "&amp;Format(StartTime, "yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>1995</Left>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field19</Name>
        <Section>10</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9570</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field10</Name>
        <Section>11</Section>
        <Text>Format(StartTime, "MMMM yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>570</Left>
        <Width>3990</Width>
        <Height>285</Height>
        <ZOrder>-1</ZOrder>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field13</Name>
        <Section>11</Section>
        <Left>570</Left>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field22</Name>
        <Section>12</Section>
        <Text>"Σύνολο μήνα "&amp;Format(StartTime, "MMMM yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>1995</Left>
        <Width>5700</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field18</Name>
        <Section>12</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9570</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field24</Name>
        <Section>5</Section>
        <Text>ContactFullName</Text>
        <Calculated>-1</Calculated>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field25</Name>
        <Section>5</Section>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field26</Name>
        <Section>0</Section>
        <Text>IIF(ChargeableCancellation, "Ναι", "Όχι")</Text>
        <Calculated>-1</Calculated>
        <Left>7545</Left>
        <Width>1575</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field27</Name>
        <Section>3</Section>
        <Text>Χρεώσιμη</Text>
        <Left>7545</Left>
        <Width>1575</Width>
        <Height>285</Height>
      </Field>
    </Fields>
  </Report>
  <!--Report *** Events ***-->
  <Report version="2.6.20093.53207">
    <Name>Events</Name>
    <ReportInfo>
      <Author>peter</Author>
    </ReportInfo>
    <DataSource>
      <ConnectionString>Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;Initial Catalog=MentalView;Data Source=MAIN\SQLEXPRESS</ConnectionString>
      <RecordSource>Appointments</RecordSource>
    </DataSource>
    <Layout>
      <Width>10845</Width>
      <MarginLeft>561.6</MarginLeft>
      <MarginTop>561.6</MarginTop>
      <MarginRight>561.6</MarginRight>
      <MarginBottom>561.6</MarginBottom>
      <Orientation>1</Orientation>
      <PaperSize>9</PaperSize>
    </Layout>
    <Font>
      <Name>Segoe UI</Name>
      <Size>9</Size>
    </Font>
    <OnOpen>' -- style script start
_styleCtr = 0
' -- style script end
</OnOpen>
    <Groups>
      <Group>
        <Name>UserFullName</Name>
        <GroupBy>UserFullName</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Year Group</Name>
        <GroupBy>Year(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Month Group</Name>
        <GroupBy>Month(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
    </Groups>
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Height>375</Height>
        <OnPrint>' -- style script start
detail.BackColor = iif(_styleCtr mod 2 = 0, rgb(255,255,255),rgb(240,240,240))
_styleCtr = _styleCtr + 1
' -- style script end
</OnPrint>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>1070</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>465</Height>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>500</Height>
      </Section>
      <Section>
        <Name>UserFullName_Header</Name>
        <Type>5</Type>
        <Height>435</Height>
      </Section>
      <Section>
        <Name>UserFullName_Footer</Name>
        <Type>6</Type>
        <Height>390</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Year Group_Header</Name>
        <Type>7</Type>
        <Height>420</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Year Group_Footer</Name>
        <Type>8</Type>
        <Height>315</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Month Group_Header</Name>
        <Type>9</Type>
        <Height>390</Height>
      </Section>
      <Section>
        <Name>Month Group_Footer</Name>
        <Type>10</Type>
        <Height>285</Height>
        <Visible>0</Visible>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>Field6</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Short Date</Format>
        <Left>570</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field8</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>1845</Left>
        <Width>720</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field15</Name>
        <Section>0</Section>
        <Text>EndTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>2700</Left>
        <Width>720</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>0</Section>
        <Text>UserFullName</Text>
        <Calculated>-1</Calculated>
        <Left>3555</Left>
        <Width>2145</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>TitleField</Name>
        <Section>1</Section>
        <Text>Συμβάντα</Text>
        <Top>135</Top>
        <Width>10830</Width>
        <Height>570</Height>
        <Align>6</Align>
        <Font>
          <Name>Segoe UI</Name>
          <Size>20</Size>
        </Font>
      </Field>
      <Field>
        <Name>ReportInfoField</Name>
        <Section>1</Section>
        <Text>&lt;ReportInfo&gt;</Text>
        <Top>705</Top>
        <Width>10830</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field5</Name>
        <Section>3</Section>
        <Text>Ημερομηνία</Text>
        <Left>570</Left>
        <Width>1140</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field9</Name>
        <Section>3</Section>
        <Text>Έναρξη</Text>
        <Left>1845</Left>
        <Width>720</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field14</Name>
        <Section>3</Section>
        <Text>Λήξη</Text>
        <Left>2700</Left>
        <Width>720</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field2</Name>
        <Section>3</Section>
        <Text>Ιατρός</Text>
        <Left>3555</Left>
        <Width>2145</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field7</Name>
        <Section>3</Section>
        <Top>285</Top>
        <Width>10830</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>ftrLeft1</Name>
        <Section>4</Section>
        <Text>Now()</Text>
        <Calculated>-1</Calculated>
        <Format>dd/MM/yyyy HH:mm</Format>
        <Top>135</Top>
        <Width>4515</Width>
        <Height>300</Height>
        <Align>0</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrRight1</Name>
        <Section>4</Section>
        <Text>"Σελίδα " &amp; [Page] &amp; " από " &amp; [Pages]</Text>
        <Calculated>-1</Calculated>
        <Left>6540</Left>
        <Top>135</Top>
        <Width>4290</Width>
        <Height>300</Height>
        <Align>2</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field3</Name>
        <Section>5</Section>
        <Text>UserFullName</Text>
        <Calculated>-1</Calculated>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field4</Name>
        <Section>5</Section>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field12</Name>
        <Section>7</Section>
        <Text>Format(StartTime, "yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>285</Left>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field11</Name>
        <Section>7</Section>
        <Left>285</Left>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field10</Name>
        <Section>9</Section>
        <Text>Format(StartTime, "MMMM yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>570</Left>
        <Width>3990</Width>
        <Height>285</Height>
        <ZOrder>-1</ZOrder>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field13</Name>
        <Section>9</Section>
        <Left>570</Left>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field16</Name>
        <Section>0</Section>
        <Text>Subject</Text>
        <Calculated>-1</Calculated>
        <Left>5835</Left>
        <Width>3420</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field17</Name>
        <Section>3</Section>
        <Text>Περιγραφή</Text>
        <Left>5835</Left>
        <Width>3420</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field18</Name>
        <Section>3</Section>
        <Text>Ορατό σε Όλους</Text>
        <Left>9405</Left>
        <Width>1425</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field19</Name>
        <Section>0</Section>
        <Text>IIF(VisibleToAll, "Ναι", "Όχι")</Text>
        <Calculated>-1</Calculated>
        <Left>9405</Left>
        <Width>1425</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
    </Fields>
  </Report>
  <!--Report *** SupervisionEvents ***-->
  <Report version="2.6.20093.53207">
    <Name>SupervisionEvents</Name>
    <ReportInfo>
      <Author>peter</Author>
    </ReportInfo>
    <DataSource>
      <ConnectionString>Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;Initial Catalog=MentalView;Data Source=MAIN\SQLEXPRESS</ConnectionString>
      <RecordSource>Appointments</RecordSource>
    </DataSource>
    <Layout>
      <Width>14820</Width>
      <MarginLeft>561.6</MarginLeft>
      <MarginTop>561.6</MarginTop>
      <MarginRight>561.6</MarginRight>
      <MarginBottom>561.6</MarginBottom>
      <Orientation>2</Orientation>
      <PaperSize>9</PaperSize>
    </Layout>
    <Font>
      <Name>Segoe UI</Name>
      <Size>9</Size>
    </Font>
    <OnOpen>' -- style script start
_styleCtr = 0
' -- style script end
</OnOpen>
    <Groups>
      <Group>
        <Name>UserFullName</Name>
        <GroupBy>UserFullName</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Year Group</Name>
        <GroupBy>Year(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Month Group</Name>
        <GroupBy>Month(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
    </Groups>
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Height>285</Height>
        <OnPrint>' -- style script start
detail.BackColor = iif(_styleCtr mod 2 = 0, rgb(255,255,255),rgb(240,240,240))
_styleCtr = _styleCtr + 1
' -- style script end
</OnPrint>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>1070</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>465</Height>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>500</Height>
      </Section>
      <Section>
        <Name>UserFullName_Header</Name>
        <Type>5</Type>
        <Height>435</Height>
      </Section>
      <Section>
        <Name>UserFullName_Footer</Name>
        <Type>6</Type>
        <Height>390</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Year Group_Header</Name>
        <Type>7</Type>
        <Height>420</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Year Group_Footer</Name>
        <Type>8</Type>
        <Height>315</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Month Group_Header</Name>
        <Type>9</Type>
        <Height>390</Height>
      </Section>
      <Section>
        <Name>Month Group_Footer</Name>
        <Type>10</Type>
        <Height>285</Height>
        <Visible>0</Visible>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>Field6</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Short Date</Format>
        <Left>570</Left>
        <Width>1140</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field8</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>1845</Left>
        <Width>720</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field15</Name>
        <Section>0</Section>
        <Text>EndTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>2700</Left>
        <Width>720</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>0</Section>
        <Text>UserFullName</Text>
        <Calculated>-1</Calculated>
        <Left>3555</Left>
        <Width>2280</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>TitleField</Name>
        <Section>1</Section>
        <Text>Εποπτείες</Text>
        <Top>135</Top>
        <Width>14820</Width>
        <Height>570</Height>
        <Align>6</Align>
        <Font>
          <Name>Segoe UI</Name>
          <Size>20</Size>
        </Font>
      </Field>
      <Field>
        <Name>ReportInfoField</Name>
        <Section>1</Section>
        <Text>&lt;ReportInfo&gt;</Text>
        <Top>705</Top>
        <Width>14820</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field5</Name>
        <Section>3</Section>
        <Text>Ημερομηνία</Text>
        <Left>570</Left>
        <Width>1140</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field9</Name>
        <Section>3</Section>
        <Text>Έναρξη</Text>
        <Left>1845</Left>
        <Width>720</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field14</Name>
        <Section>3</Section>
        <Text>Λήξη</Text>
        <Left>2700</Left>
        <Width>720</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field2</Name>
        <Section>3</Section>
        <Text>Ιατρός</Text>
        <Left>3555</Left>
        <Width>2280</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field7</Name>
        <Section>3</Section>
        <Top>285</Top>
        <Width>14820</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>ftrLeft1</Name>
        <Section>4</Section>
        <Text>Now()</Text>
        <Calculated>-1</Calculated>
        <Format>dd/MM/yyyy HH:mm</Format>
        <Top>135</Top>
        <Width>4515</Width>
        <Height>300</Height>
        <Align>0</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrRight1</Name>
        <Section>4</Section>
        <Text>"Σελίδα " &amp; [Page] &amp; " από " &amp; [Pages]</Text>
        <Calculated>-1</Calculated>
        <Left>10545</Left>
        <Top>135</Top>
        <Width>4275</Width>
        <Height>300</Height>
        <Align>2</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field3</Name>
        <Section>5</Section>
        <Text>UserFullName</Text>
        <Calculated>-1</Calculated>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field4</Name>
        <Section>5</Section>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field12</Name>
        <Section>7</Section>
        <Text>Format(StartTime, "yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>285</Left>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field11</Name>
        <Section>7</Section>
        <Left>285</Left>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field10</Name>
        <Section>9</Section>
        <Text>Format(StartTime, "MMMM yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>570</Left>
        <Width>3990</Width>
        <Height>285</Height>
        <ZOrder>-1</ZOrder>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field13</Name>
        <Section>9</Section>
        <Left>570</Left>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field16</Name>
        <Section>0</Section>
        <Text>TaskSupervisionCustomers</Text>
        <Calculated>-1</Calculated>
        <Left>5985</Left>
        <Width>3840</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field17</Name>
        <Section>3</Section>
        <Text>Πελάτες</Text>
        <Left>5985</Left>
        <Width>3840</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field18</Name>
        <Section>3</Section>
        <Text>Θέμα</Text>
        <Left>9975</Left>
        <Width>4845</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field19</Name>
        <Section>0</Section>
        <Text>Subject</Text>
        <Calculated>-1</Calculated>
        <Left>9975</Left>
        <Width>4845</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
    </Fields>
  </Report>
  <!--Report *** IncomeClearance ***-->
  <Report version="2.6.20093.53207">
    <Name>IncomeClearance</Name>
    <ReportInfo>
      <Author>peter</Author>
    </ReportInfo>
    <DataSource>
      <ConnectionString>Provider=SQLOLEDB.1;Integrated Security=SSPI;Persist Security Info=False;Initial Catalog=MentalView;Data Source=MAIN\SQLEXPRESS</ConnectionString>
      <RecordSource>Appointments</RecordSource>
    </DataSource>
    <Layout>
      <Width>15540</Width>
      <MarginLeft>561.6</MarginLeft>
      <MarginTop>561.6</MarginTop>
      <MarginRight>561.6</MarginRight>
      <MarginBottom>561.6</MarginBottom>
      <Orientation>2</Orientation>
      <PaperSize>9</PaperSize>
    </Layout>
    <Font>
      <Name>Segoe UI</Name>
      <Size>9</Size>
    </Font>
    <OnOpen>' -- style script start
_styleCtr = 0
' -- style script end
</OnOpen>
    <Groups>
      <Group>
        <Name>UserFullName</Name>
        <GroupBy>UserFullName</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Year Group</Name>
        <GroupBy>Year(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
      <Group>
        <Name>Month Group</Name>
        <GroupBy>Month(StartTime)</GroupBy>
        <Sort>1</Sort>
      </Group>
    </Groups>
    <Sections>
      <Section>
        <Name>Detail</Name>
        <Type>0</Type>
        <Height>285</Height>
        <OnPrint>' -- style script start
detail.BackColor = iif(_styleCtr mod 2 = 0, rgb(255,255,255),rgb(240,240,240))
_styleCtr = _styleCtr + 1
' -- style script end
</OnPrint>
      </Section>
      <Section>
        <Name>Header</Name>
        <Type>1</Type>
        <Height>1070</Height>
      </Section>
      <Section>
        <Name>Footer</Name>
        <Type>2</Type>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>PageHeader</Name>
        <Type>3</Type>
        <Height>465</Height>
      </Section>
      <Section>
        <Name>PageFooter</Name>
        <Type>4</Type>
        <Height>500</Height>
      </Section>
      <Section>
        <Name>UserFullName_Header</Name>
        <Type>5</Type>
        <Height>435</Height>
      </Section>
      <Section>
        <Name>UserFullName_Footer</Name>
        <Type>6</Type>
        <Height>390</Height>
      </Section>
      <Section>
        <Name>Year Group_Header</Name>
        <Type>7</Type>
        <Height>420</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Year Group_Footer</Name>
        <Type>8</Type>
        <Height>315</Height>
        <Visible>0</Visible>
      </Section>
      <Section>
        <Name>Month Group_Header</Name>
        <Type>9</Type>
        <Height>390</Height>
      </Section>
      <Section>
        <Name>Month Group_Footer</Name>
        <Type>10</Type>
        <Height>285</Height>
      </Section>
    </Sections>
    <Fields>
      <Field>
        <Name>Field6</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Short Date</Format>
        <Left>570</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field8</Name>
        <Section>0</Section>
        <Text>StartTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>1710</Left>
        <Width>570</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field15</Name>
        <Section>0</Section>
        <Text>EndTime</Text>
        <Calculated>-1</Calculated>
        <Format>Time</Format>
        <Left>2415</Left>
        <Width>570</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field1</Name>
        <Section>0</Section>
        <Text>UserFullName</Text>
        <Calculated>-1</Calculated>
        <Left>3135</Left>
        <Width>1710</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>TitleField</Name>
        <Section>1</Section>
        <Text>Εκκαθάριση Εσόδων</Text>
        <Top>135</Top>
        <Width>15390</Width>
        <Height>570</Height>
        <Align>6</Align>
        <Font>
          <Name>Segoe UI</Name>
          <Size>20</Size>
        </Font>
      </Field>
      <Field>
        <Name>ReportInfoField</Name>
        <Section>1</Section>
        <Text>&lt;ReportInfo&gt;</Text>
        <Top>705</Top>
        <Width>15390</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field5</Name>
        <Section>3</Section>
        <Text>Ημερομηνία</Text>
        <Left>570</Left>
        <Width>990</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field9</Name>
        <Section>3</Section>
        <Text>Έναρξη</Text>
        <Left>1710</Left>
        <Width>705</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field14</Name>
        <Section>3</Section>
        <Text>Λήξη</Text>
        <Left>2415</Left>
        <Width>570</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field2</Name>
        <Section>3</Section>
        <Text>Θεραπευτής</Text>
        <Left>3135</Left>
        <Width>1710</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field7</Name>
        <Section>3</Section>
        <Top>285</Top>
        <Width>15525</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>ftrLeft1</Name>
        <Section>4</Section>
        <Text>Now()</Text>
        <Calculated>-1</Calculated>
        <Format>dd/MM/yyyy HH:mm</Format>
        <Top>135</Top>
        <Width>4515</Width>
        <Height>300</Height>
        <Align>0</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>ftrRight1</Name>
        <Section>4</Section>
        <Text>"Σελίδα " &amp; [Page] &amp; " από " &amp; [Pages]</Text>
        <Calculated>-1</Calculated>
        <Left>10890</Left>
        <Top>135</Top>
        <Width>4065</Width>
        <Height>300</Height>
        <Align>2</Align>
        <Font>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field3</Name>
        <Section>5</Section>
        <Text>UserFullName</Text>
        <Calculated>-1</Calculated>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field4</Name>
        <Section>5</Section>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field12</Name>
        <Section>7</Section>
        <Text>Format(StartTime, "yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>285</Left>
        <Width>3990</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field11</Name>
        <Section>7</Section>
        <Left>285</Left>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field10</Name>
        <Section>9</Section>
        <Text>Format(StartTime, "MMMM yyyy")</Text>
        <Calculated>-1</Calculated>
        <Left>570</Left>
        <Width>3990</Width>
        <Height>285</Height>
        <ZOrder>-1</ZOrder>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field13</Name>
        <Section>9</Section>
        <Left>570</Left>
        <Top>285</Top>
        <Width>3990</Width>
        <BorderStyle>1</BorderStyle>
        <LineSlant>1</LineSlant>
      </Field>
      <Field>
        <Name>Field16</Name>
        <Section>3</Section>
        <Text>Κόστος Συνεδρίας</Text>
        <Left>7125</Left>
        <Width>1005</Width>
        <Height>285</Height>
        <Align>2</Align>
      </Field>
      <Field>
        <Name>Field17</Name>
        <Section>0</Section>
        <Text>Price</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>7125</Left>
        <Width>1005</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field18</Name>
        <Section>3</Section>
        <Text>Κρατήσεις</Text>
        <Left>8265</Left>
        <Width>855</Width>
        <Height>285</Height>
        <Align>2</Align>
      </Field>
      <Field>
        <Name>Field19</Name>
        <Section>0</Section>
        <Text>Deductions</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>8265</Left>
        <Width>855</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field20</Name>
        <Section>3</Section>
        <Text>Υπόλοιπο</Text>
        <Left>9255</Left>
        <Width>870</Width>
        <Height>285</Height>
        <Align>2</Align>
      </Field>
      <Field>
        <Name>Field21</Name>
        <Section>0</Section>
        <Text>Balance</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9255</Left>
        <Width>870</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field22</Name>
        <Section>3</Section>
        <Text>Είσπραξη Μετρ.</Text>
        <Left>10260</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>2</Align>
        <WordWrap>0</WordWrap>
      </Field>
      <Field>
        <Name>Field23</Name>
        <Section>0</Section>
        <Text>CashReceived</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>10260</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>8</Align>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field24</Name>
        <Section>3</Section>
        <Text>Εκκαθάριση</Text>
        <Left>11550</Left>
        <Width>990</Width>
        <Height>285</Height>
        <Align>2</Align>
      </Field>
      <Field>
        <Name>Field25</Name>
        <Section>0</Section>
        <Text>Clearance</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>11550</Left>
        <Width>855</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field26</Name>
        <Section>10</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>7125</Left>
        <Width>1005</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field27</Name>
        <Section>10</Section>
        <Text>SUM(Deductions)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>8265</Left>
        <Width>855</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field28</Name>
        <Section>10</Section>
        <Text>SUM(Balance)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9255</Left>
        <Width>870</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field29</Name>
        <Section>10</Section>
        <Text>SUM(CashReceived)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>10260</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field30</Name>
        <Section>10</Section>
        <Text>SUM(Clearance)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>11550</Left>
        <Width>855</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field31</Name>
        <Section>10</Section>
        <Text>"Σύνολο μήνα "&amp;Format(StartTime, "MMMM yyyy")&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>285</Left>
        <Width>5550</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field32</Name>
        <Section>3</Section>
        <Text>Τρόπος Πληρ.</Text>
        <Left>12540</Left>
        <Width>1575</Width>
        <Height>285</Height>
        <Align>2</Align>
      </Field>
      <Field>
        <Name>Field33</Name>
        <Section>0</Section>
        <Text>IIF(PaymentType="Cash","Μετρητά",IIF(PaymentType="Card","Κάρτα",IIF(PaymentType="Deposit","Μεταφορά/Κατάθεση",IIF(PaymentType="Pending","Εκκρεμεί",""))))</Text>
        <Calculated>-1</Calculated>
        <Left>12540</Left>
        <Width>1575</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field34</Name>
        <Section>0</Section>
        <Text>Bank</Text>
        <Calculated>-1</Calculated>
        <Left>14250</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <WordWrap>0</WordWrap>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field35</Name>
        <Section>3</Section>
        <Text>Τράπεζα</Text>
        <Left>14250</Left>
        <Width>1275</Width>
        <Height>285</Height>
        <Align>0</Align>
      </Field>
      <Field>
        <Name>Field36</Name>
        <Section>6</Section>
        <Text>SUM(Price)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>7125</Left>
        <Width>1005</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field37</Name>
        <Section>6</Section>
        <Text>SUM(Deductions)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>8265</Left>
        <Width>855</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field38</Name>
        <Section>6</Section>
        <Text>SUM(Balance)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>9255</Left>
        <Width>870</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field39</Name>
        <Section>6</Section>
        <Text>SUM(CashReceived)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>10260</Left>
        <Width>1290</Width>
        <Height>285</Height>
        <Align>8</Align>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field40</Name>
        <Section>6</Section>
        <Text>SUM(Clearance)</Text>
        <Calculated>-1</Calculated>
        <Format>Currency</Format>
        <Left>11550</Left>
        <Width>855</Width>
        <Height>285</Height>
        <Align>8</Align>
        <MarginLeft>50</MarginLeft>
        <RunningSum>1</RunningSum>
        <CanGrow>-1</CanGrow>
        <Font>
          <Bold>-1</Bold>
          <Name>Segoe UI</Name>
          <Size>9</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field41</Name>
        <Section>6</Section>
        <Text>"Σύνολο θεραπευτή "&amp;UserFullName&amp;":"</Text>
        <Calculated>-1</Calculated>
        <Left>285</Left>
        <Width>5550</Width>
        <Height>285</Height>
        <Align>8</Align>
        <WordWrap>0</WordWrap>
        <Font>
          <Bold>-1</Bold>
          <Name>Verdana</Name>
          <Size>8</Size>
        </Font>
      </Field>
      <Field>
        <Name>Field42</Name>
        <Section>3</Section>
        <Text>Θεραπευόμενος</Text>
        <Left>4980</Left>
        <Width>1575</Width>
        <Height>285</Height>
      </Field>
      <Field>
        <Name>Field43</Name>
        <Section>0</Section>
        <Text>ContactFullName</Text>
        <Calculated>-1</Calculated>
        <Left>4980</Left>
        <Width>1575</Width>
        <Height>285</Height>
        <Align>6</Align>
        <MarginLeft>50</MarginLeft>
        <CanGrow>-1</CanGrow>
      </Field>
      <Field>
        <Name>Field44</Name>
        <Section>3</Section>
        <Text>Ακυρ.</Text>
        <Left>6690</Left>
        <Width>420</Width>
        <Height>285</Height>
        <Align>2</Align>
      </Field>
      <Field>
        <Name>Field45</Name>
        <Section>0</Section>
        <Text>IIF(Canceled, "Ακυρ.", "")</Text>
        <Calculated>-1</Calculated>
        <Left>6690</Left>
        <Width>435</Width>
        <Height>285</Height>
        <ForeColor>139</ForeColor>
      </Field>
    </Fields>
  </Report>
</Reports>