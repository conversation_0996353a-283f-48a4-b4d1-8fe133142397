/*!
*  filename: ej.navigationdrawer.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["jsrender","./../common/ej.core.min","./../common/ej.data.min","./../common/ej.touch.min","./../common/ej.scroller.min","./ej.listview.min"],n):n()})(function(){(function(n,t,i){t.widget("ejNavigationDrawerBase","ej.NavigationDrawerBase",{defaults:{contentId:null,cssClass:"",direction:"left",targetId:null,position:"normal",enableListView:!1,listViewSettings:{},type:"overlay",width:"auto",swipe:null,open:null,beforeClose:null,items:[],ajaxSettings:{type:"GET",cache:!1,data:{},dataType:"html",contentType:"html",async:!0},ajaxSuccess:null,ajaxError:null,ajaxComplete:null},dataTypes:{direction:"enum",type:"enum",ajaxSettings:"data"},loadContent:function(i,r){var f=this,u,e,o;r.indexOf("#")==0?(u=n(r)[0]&&n(r)[0].nodeName&&n(r)[0].nodeName.toLowerCase()=="script"?t.getClearString(n(r)[0].innerHTML):r,u!=n(r)&&(n(u).length!=0?this._scriptTemplate.push({id:r,data:n(u)}):(e=n.grep(this._scriptTemplate,function(n){if(n.id==u)return n.data}),u=e.length>0?e[0].data[0]:u)),n(i).html(n(u)),this._hide()):(o={url:r,success:function(t){try{if(n(i).html(t),f._trigger("ajaxSuccess",{data:t,url:r,content:i}))return;f._hide()}catch(u){}},error:function(n){try{if(f._trigger("ajaxError",{data:{status:n.status,responseText:n.responseText,statusText:n.statusText},content:i,url:r}))return;f._hide()}catch(n){}},complete:function(n){try{f._trigger("ajaxComplete",{data:n,content:i,url:r})}catch(n){}}},this._sendAjaxRequest(o))},_sendAjaxRequest:function(t){n.ajax({type:t.type,cache:t.cache,url:t.url,dataType:t.dataType,data:t.data,contentType:t.contentType,async:t.async,success:t.success,error:t.error,beforeSend:t.beforeSend,complete:t.complete})},_shadowWrapper:function(){this.model.isPaneOpen||(this._elementOverlay=t.buildTag("div#"+this._id+"_Overlay",{},{},{"class":this._rootCSS+" "+this._prefixClass+"nb-layout "+this._prefixClass+"nb-overlay"}),this._elementShadow=t.buildTag("div#"+this._id+"_shadow",{},{},{"class":this._rootCSS+" "+this._prefixClass+"nb-shadow "+this._prefixClass+"nb-type-"+(this.model.type=="slide"?"slide":"overlay")}),this._nbHome.append(this._elementOverlay),this._elementOverlay.hide(),this.element.hide(),this.element.prepend(this._elementShadow))},_renderControl:function(){this.model.position.toLowerCase()=="fixed"&&this.element.appendTo(this._nbHome);this._shadowWrapper();this.element.addClass(this._prefixClass+"user-select "+this.model.cssClass);this.element.addClass(this._prefixClass+"nb-layout "+(this.model.direction.toLowerCase()=="left"?this._prefixClass+"nb-left":this._prefixClass+"nb-right"));this.element.addClass(this._prefixClass+"nb-type-"+(this.model.type=="slide"?"slide":"overlay"));this._maxIndex=this._getMaxZindex();this._parentWidth=this._nbHome.width();this._setWidth();this._setLayout()},_getMaxZindex:function(){return Math.max.apply(null,n.map(n("body *"),function(t){return parseInt(n(t).css("z-index"))+1||1}))},_transform:function(t,i,r){var s="-"+this._browser+"-transform",u="translateX("+t+"px) translateZ(0px)",h="-"+this._browser+"-transition-property",f="transform",c="-"+this._browser+"-transition-duration",e=i+"ms",o;r?(o=this.model.position=="fixed"?"e-nb-fixed":"",this.model.position=="normal"&&this.model.type=="slide"&&n("#"+this._id+"_WrapContainer").addClass("e-nb-normal"),this.element.addClass(""+this._prefixClass+"nb-animate "+o),this.element.css(h,f).css(c,e).css(s,u),this.element.css("transition-property",f).css("transition-duration",e).css("transform",u)):(o=this.model.position=="fixed"?"e-nb-fixed":"",this.model.position=="normal"&&this.model.type=="slide"&&n("#"+this._id+"_WrapContainer").addClass("e-nb-normal"),this._nbHome.addClass(this._rootCSS+" "+this._prefixClass+"nb-animate "+o),this._nbHome.css(h,f).css(c,e).css(s,u),this._nbHome.css("transition-property",f).css("transition-duration",e).css("transform",u))},_show:function(){t.isNullOrUndefined(this.element.css("left").match(/\d/g))||Number(this.element.css("left").match(/\d/g)[0]<this.element.width())||this._setLayout();n("."+this._prefixClass+"nb-opened").length&&n("."+this._prefixClass+"nb-opened")[0]!=this.element[0]&&n("."+this._prefixClass+"nb-opened").ejNavigationDrawer("close");this.element.show();t.listenTouchEvent(this._isTransitionElement()?this.element:this._nbHome,t.transitionEndEvent(),this._transitionOpenHandler,!1,this);this.element.addClass(""+this._prefixClass+"nb-opened");this._elementOverlay.css("z-index",this._maxIndex*2);this.element.css("z-index",this._maxIndex*3);t.browserInfo().name=="msie"&&t.browserInfo().version<=9?n(this.element).show().animate({left:this.model.direction=="left"?"0px":this._parentWidth-n(this.element).width()},400,n.proxy(this._transitionOpenEnd,this)):this.model.direction=="left"?this._transform(n(this.element).width(),400,this._isTransitionElement()):this._transform(-n(this.element).width(),400,this._isTransitionElement());this._data={element:this.element};this.model.enableListView?n.extend(this._data,{listview:this._lb}):null;this._elementOverlay.show();this._trigger("open",this._data)},_hide:function(){this._elementOverlay.hide();t.listenTouchEvent(this._isTransitionElement()?this.element:this._nbHome,t.transitionEndEvent(),this._transitionCloseHandler,!1,this);this.element.removeClass(""+this._prefixClass+"nb-opened");t.browserInfo().name=="msie"&&t.browserInfo().version<=9?n(this.element).animate({left:this.model.direction=="left"?-n(this.element).width():this._nbHome.width()},400,n.proxy(this._transitionCloseEnd,this)):this._transform(0,400,this._isTransitionElement())},_clearHomeElement:function(){var n="",t;this._browser!=""&&(n="-"+this._browser+"-");var i=n+"transform",r=n+"transition-property",u=n+"transition-duration";this._nbHome.css(i,"").css(u,"").css(r,"");t=this.model.position=="fixed"?"e-nb-fixed":"";this._nbHome.removeClass(this._rootCSS+" "+this._prefixClass+"nb-animate "+t)},_isOpened:function(){return this.element.hasClass(""+this._prefixClass+"nb-opened")},_isOtherOpened:function(){return n("."+this._prefixClass+"nb-opened").length},_isTransitionElement:function(){return this.model.type=="overlay"?!0:!1},_isOrientationSupported:function(){return"orientation"in window&&"onorientationchange"in window},_setWidth:function(){n(this._nbHome).width()<500?this.element.css("maxwidth","80%"):this.element.css("maxwidth","40%");this.model.width!="auto"&&Number(this.model.width)!=0&&this.element.width(Number(this.model.width));this.element.height("100%")},_setLayout:function(){var i;this.element.show();this.element.find("."+this._prefixClass+"list-container").removeClass("e-scroller");this.model.position=="fixed"&&this.element.css("min-height",window.innerHeight);this.model.direction.toLowerCase()=="left"?(n("#"+this._id+"_WrapContainer").css({right:""}).removeClass("e-nb-right").addClass("e-nb-left"),this.element.removeClass("e-nb-right").addClass("e-nb-left"),this.model.isPaneOpen?(i=this.model.position=="fixed"?"e-nb-fixed":"",this.element.css({right:"",left:this.model.position=="fixed"?-t.getDimension(this.element,"width"):0}),this._nbHome.css({width:this._nbHome.parent().width()-t.getDimension(this.element,"width"),left:t.getDimension(this.element,"width"),position:"absolute"}).addClass(i)):(this.model.position=="normal"?this.element.css({right:"",left:this.model.type=="overlay"?-t.getDimension(this.element,"width"):"0px"}):this.element.css({right:"",left:-t.getDimension(this.element,"width")}),this.model.type=="slide"&&n("#"+this._id+"_WrapContainer").removeAttr("style").css({left:-t.getDimension(this.element,"width")}))):(n("#"+this._id+"_WrapContainer").css({left:""}).removeClass("e-nb-left").addClass("e-nb-right"),this.element.css({left:""}).removeClass("e-nb-left").addClass("e-nb-right"),this.model.isPaneOpen?(i=this.model.position=="fixed"?"e-nb-fixed":"",this._nbHome.css({left:"",position:"",width:t.getDimension(this._nbHome.parent(),"width")-t.getDimension(this.element,"width")}).addClass(i),this.element.css({left:"",right:"0px"})):(i=this.model.position=="fixed"?"e-nb-fixed":"",this._nbHome.removeClass("e-nb-fixed-slide "+i),n("#"+this._id+"_WrapContainer").removeClass("e-nb-left").addClass("e-nb-right"),this.model.type=="slide"&&n("#"+this._id+"_WrapContainer").removeAttr("style").css({right:"0px"}),this.model.position.toLowerCase()=="fixed"&&this.model.type=="slide"&&this._nbHome.addClass("e-nb-fixed-slide"),this.model.position.toLowerCase()=="fixed"&&this.model.type=="overlay"&&this.element.addClass(i),this.model.position.toLowerCase()=="normal"?this.element.css({left:t.getDimension(this.element,"width")}):this.element.css({right:-t.getDimension(this.element,"width")})));this._isOpened()||this.model.isPaneOpen||this.element.hide()},_destroy:function(n){this._wireEvents(!0);this._elementOverlay&&this._elementOverlay.remove();this._elementShadow&&this._elementShadow.remove();this._elementWrapper&&this.element.unwrap();this.element.removeAttr("style");this.element.css("left","");this._destroyEJMPlugin();n||this.element.removeClass("e-nb e-user-select e-nb-layout e-nb-left e-nb-type-overlay e-nb-animate e-nb-opened")},_refresh:function(){this._destroy(!0);this._load();this._renderControl();this._renderEJMControls();this._createDelegate();this._wireEvents()},_createDelegate:function(){this._swipeStartHandler=n.proxy(this._swipeStart,this);this._swipeEndHandler=n.proxy(this._swipeEnd,this);this._swipeMoveHandler=n.proxy(this._swipeMove,this);this._touchMoveHandler=n.proxy(this._touchMove,this);this._touchEndHandler=n.proxy(this._touchEnd,this);this._transitionOpenHandler=n.proxy(this._transitionOpenEnd,this);this._transitionCloseHandler=n.proxy(this._transitionCloseEnd,this);this._overlayTapHandler=n.proxy(this._elementOverlayTap,this);this._targetButtonTapHandler=n.proxy(this._targetButtonTap,this);this._onOrientationChangeHandler=n.proxy(this._onOrientationChangeEvent,this);this._onResizeEventHandler=n.proxy(this._onResizeEvent,this)},_wireEvents:function(i){var r;(!this.model.isPaneOpen||i)&&(this.model.targetId?(r=t.getCurrentPage?t.getCurrentPage():n("body"),t.listenTouchEvent(r.find("#"+this.model.targetId),this._prefixClass=="e-"?"click":t.endEvent(),this._targetButtonTapHandler,i,this)):t.browserInfo().name=="msie"&&t.browserInfo().version<=8||t.listenTouchEvent(this._nbHome,t.startEvent(),this._swipeStartHandler,i,this),t.listenTouchEvent(this._elementOverlay,t.endEvent(),this._overlayTapHandler,i,this));this.model.isPaneOpen&&(t.isTouchDevice()&&this._isOrientationSupported()?t.listenTouchEvent(window,"orientationchange",this._onOrientationChangeHandler,i,this):t.listenTouchEvent(window,"resize",this._onResizeEventHandler,i,this))},_onResizeEvent:function(){var n;this.element.show();n=this._isOpened()&&this.element.is(":visible")&&!this.model.isPaneOpen?"opened ":"closed";this._transform(0,0,this._isTransitionElement());this.element.hide();this.element.removeClass(""+this._prefixClass+"nb-opened");this._clearHomeElement();this._parentWidth=this._nbHome.width();this._setWidth();this._setLayout();n=="opened "&&this._show()},_onOrientationChangeEvent:function(){var t,n;this.element.show();t=this._isOpened()&&this.element.is(":visible")?"opened ":"closed";(window.orientation==0||window.orientation==180||window.orientation==90)&&window.scrollTo(0,0);this._transform(0,0,this._isTransitionElement());this.element.hide();this.element.removeClass(""+this._prefixClass+"nb-opened");this._parentWidth=this._nbHome.width();this._clearHomeElement();n=this;setTimeout(function(){n._setWidth();n._setLayout();t=="opened "&&n._show()},300)},_transitionCloseEnd:function(){t.isNullOrUndefined(this.element)||this.element.hide();this._clearHomeElement();t.listenTouchEvent(this._isTransitionElement()?this.element:this._nbHome,t.transitionEndEvent(),this._transitionCloseHandler,!0,this);this._lbObj&&(this.model.listViewSettings.selectedItemIndex=this._lbObj.model.selectedItemIndex)},_transitionOpenEnd:function(){t.isNullOrUndefined(this.element)||this.element.show();this._scrollbar&&this._scrollbar.ejmScrollPanel("refresh");this._lbscrollbar&&this._lbscrollbar.ejmScrollPanel("refresh");t.listenTouchEvent(this._isTransitionElement()?this.element:this._nbHome,t.transitionEndEvent(),this._transitionOpenHandler,!0,this)},_targetButtonTap:function(n){n.preventDefault();this._isOtherOpened()||this._show()},_elementOverlayTap:function(r){(t.blockDefaultActions(r),this._data={element:this.element},this.model.enableListView?n.extend(this._data,{listview:this._lb}):i,this._trigger("beforeClose",this._data))||this._hide()},_swipeStart:function(n){var i=n.changedTouches?n.changedTouches[0]:n;this._startSwipeOffsetX=i.clientX-this._nbHome.offset().left;this._startSwipeOffsetActualX=i.clientX;this._startSwipeOffsetY=i.clientY;this._containerX=this._isTransitionElement()?this.element.offset().left:this._nbHome.offset().left;t.listenTouchEvent(this._nbHome,t.moveEvent(),this._swipeMoveHandler,!1,this);t.listenTouchEvent(this._nbHome,t.endEvent(),this._swipeEndHandler,!1,this)},_swipeMove:function(i){var r=i.changedTouches?i.changedTouches[0]:i,u,f;!t.isNullOrUndefined(this.element.css("left").match(/\d/g))&&Number(this.element.css("left").match(/\d/g)[0])<this.element.width()&&this._setLayout();this._swipeDirection=this._startSwipeOffsetActualX&&this._startSwipeOffsetActualX>r.clientX?"right":"left";u=this._isOpened()?this._swipeDirection!=this.model.direction.toLowerCase()?!0:!1:!1;u?this._startSwipeOffsetX&&this._isOpened()&&(this._relativeX=r.clientX-this._startSwipeOffsetActualX,this._relativeY=r.clientY-this._startSwipeOffsetY,f=this.element.width()+this._relativeX-(this.model.type.toLowerCase()=="overlay"?this._nbHome.offset().left:0),n(r.target).hasClass(""+this._prefixClass+"nb-overlay")&&(this.model.direction=="left"?-this._relativeX<this.element.width():this._relativeX<this.element.width())&&this._transform(this.model.direction=="left"?f:-(this.element.width()-this._relativeX),0,this._isTransitionElement())):this._swipeDirection!=this.model.direction.toLowerCase()||this._isOtherOpened()?t.listenEvents([this._nbHome,this._nbHome,this._nbHome],[t.moveEvent(),t.endEvent()],[this._touchMoveHandler,this._touchEndHandler],!0,this):t.listenEvents([this._nbHome,this._nbHome,this._nbHome],[t.moveEvent(),t.endEvent()],[this._touchMoveHandler,this._touchEndHandler],!1,this)},_swipeEnd:function(n){var i=n.changedTouches?n.changedTouches[0]:n;t.listenTouchEvent(this._nbHome,t.moveEvent(),this._swipeMoveHandler,!0,this);t.listenTouchEvent(this._nbHome,t.endEvent(),this._swipeEndHandler,!0,this)},_touchMove:function(n){var u=n.changedTouches?n.changedTouches[0]:n,r;t.listenTouchEvent(this._nbHome,t.moveEvent(),this._swipeMoveHandler,!0,this);this._isTransitionElement()&&(this._containerX=0);this._startSwipeOffsetX&&!this._isOpened()&&(this._relativeX=u.clientX-this._startSwipeOffsetX,this._relativeY=u.clientY-this._startSwipeOffsetY,(this.model.direction=="left"?this._startSwipeOffsetX<50:this._startSwipeOffsetX>this._nbHome.width()-50)&&(this.element.show(),r=this.model.type.toLowerCase()=="slide"?0:this._nbHome.offset().left,(this.model.direction=="left"?this._relativeX-r<this.element.width():this._relativeX-r>-this.element.width())&&(!this._containerX||this.model.targetHome?this._transform(this._containerX+this._relativeX-r,0,this._isTransitionElement()):i)))},_touchEnd:function(r){var u=r.changedTouches?r.changedTouches[0]:r;(t.listenTouchEvent(this._nbHome,t.moveEvent(),this._touchMoveHandler,!0,this),this._isOpened()||(n.isFunction(t.isIOS7)?t.isIOS7():!1)||(this._containerX?this._hide():(this._relativeX=u.clientX-this._startSwipeOffsetX,this._relativeY=u.clientY-this._startSwipeOffsetY,(this.model.direction=="left"?this._startSwipeOffsetX<50:this._startSwipeOffsetX>this._nbHome.width()-50)&&(this._relativeX>30||this._relativeX<-30?this._show():this._hide()))),this._data={targetElement:this._nbHome,element:this.element,direction:this._swipeDirection},this.model.enableListView?n.extend(this._data,{listview:this._lb}):i,this._trigger("swipe",this._data))||(t.listenTouchEvent(this._nbHome,t.endEvent(),this._touchEndHandler,!0,this),t.listenTouchEvent(this._nbHome,"mouseleave",this._touchEndHandler,!0,this))},open:function(){this.model.isPaneOpen||this._show()},close:function(){if(!this.model.isPaneOpen){if(this._data={element:this.element},this.model.enableListView?n.extend(this._data,{listview:this._lb}):i,this._trigger("beforeClose",this._data))return;this._hide()}},toggle:function(){this.model.isPaneOpen||(this._isOpened()?this.close():this.open())}})})(jQuery,Syncfusion),function(n,t){t.widget("ejNavigationDrawer","ej.NavigationDrawer",{_setFirst:!0,validTags:["div"],_rootCSS:"e-nb e-js",_prefixClass:"e-",defaults:{isPaneOpen:!1},dataTypes:{isPaneOpen:"boolean"},_init:function(){this._load();this._renderControl();this._renderEJMControls();this._createDelegate();this._wireEvents()},_load:function(){this._browser=this._getBrowserAgent();this.model.position=="normal"?this.model.isPaneOpen?(this.element.siblings().wrapAll("<div id='"+this._id+"_PageContainer' class='e-nb-container e-nb-pageContainer'><\/div>"),this._pageContainer=this._nbHome=this.element.siblings()):(this._elementWrapper=t.buildTag("div#"+this._id+"_WrapContainer",{},{},{"class":this._rootCSS+" e-nb-container e-nb-"+this.model.direction.toLowerCase()}),this.element.wrapAll(this._elementWrapper),this._nbHome=n("#"+this._id+"_WrapContainer").parent()):(this._nbHome=n("body"),this.element.appendTo(this._nbHome));this._nbHomeCss=this._nbHome.clone(!0)[0].style},_renderEJMControls:function(){this.model.enableListView?(this._lb=t.buildTag("div#"+this._id+"_listview",{},{},{"class":"e-nb-listview"}),this.model.items.length||this.model.listViewSettings.dataSource.length?this._lb.appendTo(this.element):this.element.find(">ul").wrapAll(this._lb),this._lb=n("#"+this._id+"_listview"),this.model.listViewSettings.width||(this.model.listViewSettings.width=240),this.model.listViewSettings.items=this.model.items,this._lb.ejListView(n.extend({},this.model.listViewSettings,{loadComplete:n.proxy(this._setLayout,this)})),this._lbObj=this._lb.data("ejListView"),this._lb.ejListView("selectItem",this.model.listViewSettings.selectedItemIndex)):this.element.find(".e-nb-listview").empty()},_setModel:function(t){var r=!1;for(var i in t){switch(i){case"width":this._setWidth();this._setLayout();break;case"direction":this._setLayout();break;case"type":this._transform(0,0,!0);this._transform(0,0,!1);break;case"isPaneOpen":this.model[i]=t[i];this.element.hide();t[i]?(n(this._elementWrapper,this._elementShadow,this._elementOverlay).remove(),this.element.removeAttr("style").height("100%"),this.element.unwrap()):(this._pageContainer.children().unwrap(),this._pageContainer.remove());this._load();this._shadowWrapper();this.model.enableListView&&this._renderEJMControls();this._wireEvents();break;case"enableListView":this._renderEJMControls();break;default:r=!0}r&&this._refresh()}},_destroyEJMPlugin:function(){this._lb&&(this._lb.ejListView("destroy"),this._lb.children().unwrap());this.model.contentId&&n("#"+this.model.contentId).empty();this._id&&n("#"+this._id).hide()},_getBrowserAgent:function(){return/webkit/i.test(navigator.appVersion)?"webkit":/firefox/i.test(navigator.userAgent)?"Moz":/trident/i.test(navigator.userAgent)?"ms":"opera"in window?"O":""}});n.extend(!0,t.NavigationDrawer.prototype,t.NavigationDrawerBase.prototype);n.extend(!0,t.NavigationDrawer.prototype.defaults.listViewSettings,t.ListView.prototype.defaults);t.NavigationDrawer.prototype._tags=t.ListView.prototype._tags}(jQuery,Syncfusion)});
