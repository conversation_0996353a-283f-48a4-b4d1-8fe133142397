/*!
*  filename: ej.signature.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.touch.min"],n):n()})(function(){"use strict";var n=this&&this.__extends||function(n,t){function r(){this.constructor=n}for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)};(function(t){var i=function(i){function r(n,r){return i.call(this),this._rootCSS="e-signature",this.PluginName="ejSignature",this.id="null",this.validTags=["div"],this.model=null,this.defaults={strokeColor:"#000000",height:"100%",width:"100%",enabled:!0,strokeWidth:2,backgroundColor:"#ffffff",showRoundedCorner:!0,saveImageFormat:"png",isResponsive:!1,saveWithBackground:!1,backgroundImage:null,change:null,mouseDown:null,mouseMove:null,mouseUp:null},n&&(n.jquery||(n=t("#"+n)),n.length)?t(n).ejSignature(r).data(this.PluginName):void 0}return n(r,i),r.prototype.setModel=function(n,t){this.setModel(n,t)},r.prototype.option=function(n,t){this.option(n,t)},r.prototype.clear=function(){this.model.saveWithBackground?this._setBackgroundImage(this.model.backgroundImage):this._canvasContext.clearRect(0,0,this._canvasContext.canvas.width,this._canvasContext.canvas.height);this._refresh()},r.prototype.save=function(n){var i,t=navigator.userAgent,u=this._canvas[0].toDataURL("image/"+this.model.saveImageFormat+""),r;i=ej.isNullOrUndefined(n)?Math.random().toString(36).replace(/[^a-z]+/g,"").substr(0,6)+"."+this.model.saveImageFormat:n+"."+this.model.saveImageFormat;r=this._dataURLtoBlob(u);t.indexOf("Edge")==-1?t.indexOf("Firefox")!=-1||t.indexOf("Chrome")!=-1||t.indexOf("Safari")!=-1||t.indexOf("AppleWebKit")!=-1?this._download(r,i):window.navigator.msSaveOrOpenBlob(r,i):window.navigator.msSaveOrOpenBlob(r,i)},r.prototype._dataURLtoBlob=function(n){for(var i=n.split(","),f=i[0].match(/:(.*?);/)[1],r=atob(i[1]),t=r.length,u=new Uint8Array(t);t--;)u[t]=r.charCodeAt(t);return new Blob([u],{type:f})},r.prototype._download=function(n,t){var r=URL.createObjectURL(n),i=document.createElement("a");i.href=r;i.target="_parent";i.download=t;(document.body||document.documentElement).appendChild(i);i.click();i.parentNode.removeChild(i)},r.prototype.undo=function(){var t=this,n;this.incStep>0&&(this.incStep--,n=new Image,n.src=this.storeSnap[this.incStep],n.onload=function(){var i=document.getElementById(t.element[0].id).children[0],r=i.getContext("2d");r.clearRect(0,0,i.width,i.height);r.drawImage(n,0,0)})},r.prototype.redo=function(){var t=this,n;this.incStep<this.storeSnap.length-1&&(this.incStep++,n=new Image,n.src=this.storeSnap[this.incStep],n.onload=function(){var i=document.getElementById(t.element[0].id).children[0],r=i.getContext("2d");r.clearRect(0,0,i.width,i.height);r.drawImage(n,0,0)})},r.prototype.show=function(){this.element.css("display","block")},r.prototype.hide=function(){this.element.css("display","none")},r.prototype.enable=function(){this.element.removeClass("e-disable").attr({"aria-disabled":!1})},r.prototype.disable=function(){this.element.addClass("e-disable").attr({"aria-disabled":!0})},r.prototype.refresh=function(){this._resizeCanvas()},r.prototype._init=function(){this._initialize();this._render();this._wireEvents(!1)},r.prototype._initialize=function(){this._canvas=ej.buildTag("canvas","",{},{});this.element.append(this._canvas);this._canvas[0].getContext&&(this._canvasContext=this._canvas[0].getContext("2d"));this._setProperties(this.model.backgroundColor);this.model.backgroundImage&&this._setBackgroundImage(this.model.backgroundImage);this.model.saveWithBackground||this._toSaveData();this._resizeCanvas();this._setRoundedCorner(this.model.showRoundedCorner);this.model.enabled||this.disable();this.strokeMinimumWidth=1},r.prototype._setBackgroundImage=function(n){if(this.model.saveWithBackground){var i=this,t=new Image;t.src=n;t.onload=function(){var n=document.getElementById(i.element[0].id).children[0],r=n.getContext("2d");r.globalCompositeOperation="source-over";r.drawImage(t,0,0,n.width,n.height);i._toSaveData()}}else this._canvasContext.canvas.style.backgroundImage="url("+n+")"},r.prototype._setProperties=function(n){ej.isNullOrUndefined(this.model.height&&this.model.width)||this.element.css({height:this.model.height,width:this.model.width});this._canvasContext.canvas.style.backgroundColor=n;this._canvasContext.canvas.style.backgroundSize="100% 100%";this._canvasContext.canvas.style.msTouchAction="none";this._canvasContext.canvas.style.touchAction="none"},r.prototype._resizeCanvas=function(){this._canvasContext.canvas.width=this.element.innerWidth();this._canvasContext.canvas.height=this.element.innerHeight();this._canvasContext.scale(1,1);this.model.isResponsive&&this._restore()},r.prototype._restore=function(){var t=this,n=new Image;ej.isNullOrUndefined(this.incStep)||(n.src=this.storeSnap[this.incStep],n.onload=function(){var i=document.getElementById(t.element[0].id).children[0],r=i.getContext("2d");r.clearRect(0,0,i.width,i.height);r.drawImage(n,0,0,i.width,i.height)})},r.prototype._refresh=function(){this._canvasContext.fillStyle=this.model.strokeColor;this.points=[];this._lastVelocity=0;this._lastWidth=(this.strokeMinimumWidth+this.model.strokeWidth)/2},r.prototype._setModel=function(n){for(var t in n)switch(t){case"height":this._changeHeight(n[t]);break;case"width":this._changeWidth(n[t]);break;case"showRoundedCorner":this._setRoundedCorner(n[t]);break;case"backgroundColor":this._setProperties(n[t]);break;case"backgroundImage":this._setBackgroundImage(n[t]);break;case"enabled":this._disabled(!n[t]);break;case"isResponsive":this._resizeCanvas()}},r.prototype._changeHeight=function(n){this.element.css("height",n.toString());this._changeHeightWidth()},r.prototype._changeWidth=function(n){this.element.css("width",n.toString());this._changeHeightWidth()},r.prototype._changeHeightWidth=function(){this._resizeCanvas();this.model.isResponsive||this._restore()},r.prototype._setRoundedCorner=function(n){n==!0?this.element.addClass("e-corner"):this.element.removeClass("e-corner")},r.prototype._disabled=function(n){n==!0?this.disable():this.enable()},r.prototype._render=function(){this.element.addClass(" e-signature e-select e-widget").attr("role","form")},r.prototype._drawStartHandler=function(n){if(n.which===1||n.which===0){if(ej.blockDefaultActions(n),this._mouseButtonDown=!0,(ej.browserInfo().name=="chrome"||"webkit")&&n.type=="touchstart"){var i=n.originalEvent.targetTouches[0];this._beginStroke(i)}else this._beginStroke(n);this._on(this._canvas,ej.eventType.mouseMove,this._drawMove);this._on(t(document),ej.eventType.mouseUp,this._drawEnd);this._trigger("mouseDown",{value:n})}},r.prototype._drawMoveHandler=function(n){if(ej.blockDefaultActions(n),this._mouseButtonDown)if((ej.browserInfo().name=="chrome"||"webkit")&&n.type=="touchmove"){var t=n.originalEvent.targetTouches[0];this._updateStroke(t)}else this._updateStroke(n);this._trigger("mouseMove",{value:n})},r.prototype._drawEndHandler=function(n){ej.blockDefaultActions(n);this._mouseButtonDown&&(this._mouseButtonDown=!1,this._endStroke());this._off(this._canvas,ej.eventType.mouseMove,this._drawMove);this._toSaveData();this._off(t(document),ej.eventType.mouseUp,this._drawEnd);this._trigger("mouseUp",{value:n});this._trigger("change",{isInteraction:!0,lastImage:this.storeSnap[this.incStep]})},r.prototype._toSaveData=function(){if(ej.isNullOrUndefined(this.incStep)?(this.incStep=-1,this.incStep++,this.storeSnap=[]):this.incStep++,this.incStep<this.storeSnap.length&&(this.storeSnap.length=this.incStep),this.incStep>0){var n=ej.buildTag("canvas","",{},{}),t=n[0].getContext("2d");n[0].height=this._canvas.height();n[0].width=this._canvas.width();t.drawImage(this._canvas[0],0,0,n[0].width,n[0].height);this.storeSnap.push(n[0].toDataURL())}else this.storeSnap.push(this._canvas[0].toDataURL())},r.prototype._beginStroke=function(n){this._refresh();this._updateStroke(n)},r.prototype._updateStroke=function(n){var t=this._createPoint(n);this._addPoint(t)},r.prototype._drawStroke=function(n){var t=this._canvasContext,i=(this.strokeMinimumWidth+this.model.strokeWidth)/2;t.beginPath();this._pointDraw(n.x,n.y,i);t.closePath();t.fill()},r.prototype._endStroke=function(){var t=this.points.length>2,n=this.points[0];!t&&n&&this._drawStroke(n)},r.prototype._createDelegates=function(){this._drawStart=t.proxy(this._drawStartHandler,this);this._drawMove=t.proxy(this._drawMoveHandler,this);this._drawEnd=t.proxy(this._drawEndHandler,this)},r.prototype._wireEvents=function(n){var t=n?"off":"on";this._createDelegates();this._on(this._canvas,ej.eventType.mouseDown,this._drawStart);this._wireResizeEvents()},r.prototype._wireResizeEvents=function(){t(window).bind("resize",t.proxy(this._resizeCanvas,this))},r.prototype._destroy=function(){this.element.removeClass("e-signature e-js e-select e-widget").removeAttr("role style signature");this.model.showRoundedCorner&&this.element.removeClass("e-corner");this._off(this._canvas,(ej.eventType.mouseDown,ej.eventType.mouseMove,ej.eventType.mouseUp));t(window).unbind("resize",t.proxy(this._resizeCanvas,this));this.element.empty()},r.prototype._createPoint=function(n){var t=this._canvas[0].getBoundingClientRect();return this._point(n.clientX-t.left,n.clientY-t.top,undefined)},r.prototype._addPoint=function(n){var t=this.points,i,r,u;t.push(n);t.length>2&&(t.length===3&&t.unshift(t[0]),i=this._calculateCurveControlPoints(t[0],t[1],t[2]).cp2,r=this._calculateCurveControlPoints(t[1],t[2],t[3]).cp1,u=this._bezierCurve(t[1],i,r,t[2]),this._addCurve(u),t.shift())},r.prototype._calculateCurveControlPoints=function(n,t,i){var f=n.x-t.x,e=n.y-t.y,o=t.x-(i.x+1),s=t.y-(i.y+1),u={x:(n.x+t.x)/2,y:(n.y+t.y)/2},r={x:(t.x+i.x)/2,y:(t.y+i.y)/2},y=Math.sqrt(f*f+e*e),h=Math.sqrt(o*o+s*s),p=u.x-r.x,w=u.y-r.y,c=h/(y+h),l={x:r.x+p*c,y:r.y+w*c},a=t.x-l.x,v=t.y-l.y;return{cp1:this._point(u.x+a,u.y+v,0),cp2:this._point(r.x+a,r.y+v,0)}},r.prototype._addCurve=function(n){var r=this.startPoint,t,i;t=this._pointVelocityCalc(r);t=.7*t+(.7-1)*this._lastVelocity;i=Math.max(this.model.strokeWidth/(t+1),this.strokeMinimumWidth);this._curveDraw(n,this._lastWidth,i);this._lastVelocity=t;this._lastWidth=i},r.prototype._pointDraw=function(n,t,i){var r=this._canvasContext;r.moveTo(n,t);r.arc(n,t,i,0,2*Math.PI,!1)},r.prototype._curveDraw=function(n,t,i){var l=this._canvasContext,a,y,o,r,s,h,u,c,v,f,e,p=i-t;for(a=Math.floor(this._bezierLengthCalc()),l.beginPath(),o=0;o<a;o++)r=o/a,s=r*r,h=s*r,u=1-r,c=u*u,v=c*u,f=v*this.startPoint.x,f+=3*c*r*this.control1.x,f+=3*u*s*this.control2.x,f+=h*this.endPoint.x,e=v*this.startPoint.y,e+=3*c*r*this.control1.y,e+=3*u*s*this.control2.y,e+=h*this.endPoint.y,y=t+h*p,this._pointDraw(f,e,y);l.closePath();l.fill()},r.prototype._point=function(n,t,i){return this.x=n,this.y=t,this.time=i||(new Date).getTime(),{x:this.x,y:this.y,time:this.time}},r.prototype._pointVelocityCalc=function(n){return this.time!==n.time?Math.sqrt(Math.pow(this.x-n.x,2)+Math.pow(this.y-n.y,2))/(this.time-n.time):1},r.prototype._bezierCurve=function(n,t,i,r){this.startPoint=n;this.control1=t;this.control2=i;this.endPoint=r},r.prototype._bezierLengthCalc=function(){for(var e=10,o=0,t,i,r,s,h,u,f,n=0;n<=e;n++)t=n/e,i=this._bezierPoint(t,this.startPoint.x,this.control1.x,this.control2.x,this.endPoint.x),r=this._bezierPoint(t,this.startPoint.y,this.control1.y,this.control2.y,this.endPoint.y),n>0&&(u=i-s,f=r-h,o+=Math.sqrt(u*u+f*f)),s=i,h=r;return o},r.prototype._bezierPoint=function(n,t,i,r,u){return t*(1-n)*(1-n)*(1-n)+3*i*(1-n)*(1-n)*n+3*r*(1-n)*n*n+u*n*n*n},r}(ej.WidgetBase);ej.widget("ejSignature","ej.Signature",new i);window.ejSignature=null})(jQuery);ej.Signature.SaveImageFormat={PNG:"png",JPG:"jpg",BMP:"bmp",TIFF:"tiff"}});
