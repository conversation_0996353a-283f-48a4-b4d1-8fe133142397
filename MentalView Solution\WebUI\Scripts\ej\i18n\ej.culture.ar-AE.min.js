/*!
*  filename: ej.culture.ar-AE.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/

ej.addCulture("ar-AE", { name: "ar-AE", englishName: "Arabic (U.A.E.)", nativeName: "العربية (الإمارات العربية المتحدة)", language: "ar", isRTL: !0, numberFormat: { pattern: ["n-"], NaN: "ليس برقم", negativeInfinity: "-لا نهاية", positiveInfinity: "+لا نهاية", currency: { pattern: ["$n-", "$ n"], symbol: "د.إ.‏" } }, calendars: { standard: { firstDay: 6, days: { names: ["الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"], namesAbbr: ["الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"], namesShort: ["ح", "ن", "ث", "ر", "خ", "ج", "س"] }, months: { names: ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليه", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر", ""], namesAbbr: ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليه", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر", ""] }, AM: ["ص", "ص", "ص"], PM: ["م", "م", "م"], patterns: { d: "dd/MM/yyyy", D: "dd MMMM, yyyy", t: "hh:mm tt", T: "hh:mm:ss tt", f: "dd MMMM, yyyy hh:mm tt", F: "dd MMMM, yyyy hh:mm:ss tt", M: "dd MMMM", Y: "MMMM, yyyy" } }, UmAlQura: { name: "UmAlQura", firstDay: 6, days: { names: ["الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"], namesAbbr: ["الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"], namesShort: ["ح", "ن", "ث", "ر", "خ", "ج", "س"] }, months: { names: ["محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأولى", "جمادى الثانية", "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة", ""], namesAbbr: ["محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأولى", "جمادى الثانية", "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة", ""] }, AM: ["ص", "ص", "ص"], PM: ["م", "م", "م"], twoDigitYearMax: 1451, patterns: { d: "dd/MM/yy", D: "dd/MMMM/yyyy", t: "hh:mm tt", T: "hh:mm:ss tt", f: "dd/MMMM/yyyy hh:mm tt", F: "dd/MMMM/yyyy hh:mm:ss tt", M: "dd MMMM", Y: "MMMM, yyyy" }, convert: { _yearInfo: [[746, -21987072e5], [1769, -21681216e5], [3794, -21374496e5], [3748, -21067776e5], [3402, -2076192e6], [2710, -20456064e5], [1334, -20150208e5], [2741, -19844352e5], [3498, -19537632e5], [2980, -19230912e5], [2889, -18925056e5], [2707, -186192e7], [1323, -18313344e5], [2647, -18007488e5], [1206, -17700768e5], [2741, -17394912e5], [1450, -17088192e5], [3413, -16782336e5], [3370, -16475616e5], [2646, -1616976e6], [1198, -15863904e5], [2397, -15558048e5], [748, -15251328e5], [1749, -14945472e5], [1706, -14638752e5], [1365, -14332896e5], [1195, -1402704e6], [2395, -13721184e5], [698, -13414464e5], [1397, -13108608e5], [2994, -12801888e5], [1892, -12495168e5], [1865, -12189312e5], [1621, -11883456e5], [683, -115776e7], [1371, -11271744e5], [2778, -10965024e5], [1748, -10658304e5], [3785, -10352448e5], [3474, -10045728e5], [3365, -9739872e5], [2637, -9434016e5], [685, -912816e6], [1389, -8822304e5], [2922, -8515584e5], [2898, -8208864e5], [2725, -7903008e5], [2635, -7597152e5], [1175, -7291296e5], [2359, -698544e6], [694, -667872e6], [1397, -6372864e5], [3434, -6066144e5], [3410, -5759424e5], [2710, -5453568e5], [2349, -5147712e5], [605, -4841856e5], [1245, -4536e8], [2778, -422928e6], [1492, -392256e6], [3497, -3616704e5], [3410, -3309984e5], [2730, -3004128e5], [1238, -2698272e5], [2486, -2392416e5], [884, -2085696e5], [1897, -177984e6], [1874, -147312e6], [1701, -1167264e5], [1355, -861408e5], [2731, -555552e5], [1370, -248832e5], [2773, 57024e5], [3538, 363744e5], [3492, 670464e5], [3401, 97632e6], [2709, 1282176e5], [1325, 1588032e5], [2653, 1893888e5], [1370, 2200608e5], [2773, 2506464e5], [1706, 2813184e5], [1685, 311904e6], [1323, 3424896e5], [2647, 3730752e5], [1198, 4037472e5], [2422, 4343328e5], [1388, 4650048e5], [2901, 4955904e5], [2730, 5262624e5], [2645, 556848e6], [1197, 5874336e5], [2397, 6180192e5], [730, 6486912e5], [1497, 6792768e5], [3506, 7099488e5], [2980, 7406208e5], [2890, 7712064e5], [2645, 801792e6], [693, 8323776e5], [1397, 8629632e5], [2922, 8936352e5], [3026, 9243072e5], [3012, 9549792e5], [2953, 9855648e5], [2709, 10161504e5], [1325, 1046736e6], [1453, 10773216e5], [2922, 11079936e5], [1748, 11386656e5], [3529, 11692512e5], [3474, 11999232e5], [2726, 12305088e5], [2390, 12610944e5], [686, 129168e7], [1389, 13222656e5], [874, 13529376e5], [2901, 13835232e5], [2730, 14141952e5], [2381, 14447808e5], [1181, 14753664e5], [2397, 1505952e6], [698, 1536624e6], [1461, 15672096e5], [1450, 15978816e5], [3413, 16284672e5], [2714, 16591392e5], [2350, 16897248e5], [622, 17203104e5], [1373, 1750896e6], [2778, 1781568e6], [1748, 181224e7], [1701, 18428256e5], [0, 18734112e5]], minDate: -21987072e5, maxDate: 1873411199999, toGregorian: function (e, t, a) { var M = a - 1, r = e - 1318; if (0 > r || r >= this._yearInfo.length) return null; var y = this._yearInfo[r], n = new Date(y[1]), s = y[0]; n.setMinutes(n.getMinutes() + n.getTimezoneOffset()); for (var d = 0; t > d; d++) M += 29 + (1 & s), s >>= 1; return n.setDate(n.getDate() + M), n }, fromGregorian: function (e) { var t = e - 6e4 * e.getTimezoneOffset(); if (t < this.minDate || t > this.maxDate) return null; for (var a = 0, M = 1; t > this._yearInfo[++a][1];); t !== this._yearInfo[a][1] && a--; var r = this._yearInfo[a], y = Math.floor((t - r[1]) / 864e5), n = r[0]; a += 1318; for (var s = 29 + (1 & n) ; y >= s;) y -= s, n >>= 1, s = 29 + (1 & n), M++; return [a, M - 1, y + 1] } } }, Hijri: { name: "Hijri", firstDay: 6, days: { names: ["الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"], namesAbbr: ["الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"], namesShort: ["ح", "ن", "ث", "ر", "خ", "ج", "س"] }, months: { names: ["محرم", "صفر", "ربيع الأول", "ربيع الآخرة", "جمادى الأولى", "جمادى الآخرة", "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة", ""], namesAbbr: ["محرم", "صفر", "ربيع الأول", "ربيع الآخرة", "جمادى الأولى", "جمادى الآخرة", "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة", ""] }, AM: ["ص", "ص", "ص"], PM: ["م", "م", "م"], twoDigitYearMax: 1451, patterns: { d: "dd/MM/yyyy", D: "dd MMMM, yyyy", t: "hh:mm tt", T: "hh:mm:ss tt", f: "dd MMMM, yyyy hh:mm tt", F: "dd MMMM, yyyy hh:mm:ss tt", M: "dd MMMM", Y: "MMMM, yyyy" }, convert: { ticks1970: 621355968e5, monthDays: [0, 30, 59, 89, 118, 148, 177, 207, 236, 266, 295, 325, 355], minDate: -425216736e5, maxDate: 0xe677d21fdbff, hijriAdjustment: 0, toGregorian: function (e, t, a) { var M = this.daysToYear(e) + this.monthDays[t] + a - 1 - this.hijriAdjustment, r = new Date(864e5 * M - this.ticks1970); return r.setMinutes(r.getMinutes() + r.getTimezoneOffset()), r }, fromGregorian: function (e) { if (e < this.minDate || e > this.maxDate) return null; var t, a, M = this.ticks1970 + (e - 0) - 6e4 * e.getTimezoneOffset(), r = Math.floor(M / 864e5) + 1 + this.hijriAdjustment, y = Math.floor(30 * (r - 227013) / 10631) + 1, n = this.daysToYear(y), s = this.isLeapYear(y) ? 355 : 354; n > r ? (y--, n -= s) : r === n ? (y--, n = this.daysToYear(y)) : r > n + s && (n += s, y++), a = 0; for (var d = r - n; 11 >= a && d > this.monthDays[a];) a++; return a--, t = d - this.monthDays[a], [y, a, t] }, daysToYear: function (e) { for (var t = 30 * Math.floor((e - 1) / 30), a = e - t - 1, M = Math.floor(10631 * t / 30) + 227013; a > 0;) M += this.isLeapYear(a) ? 355 : 354, a--; return M }, isLeapYear: function (e) { return 11 > (11 * e + 14) % 30 } } }, Gregorian_MiddleEastFrench: { name: "Gregorian_MiddleEastFrench", firstDay: 6, days: { names: ["dimanche", "lundi", "mardi", "mercredi", "jeudi", "vendredi", "samedi"], namesAbbr: ["dim.", "lun.", "mar.", "mer.", "jeu.", "ven.", "sam."], namesShort: ["di", "lu", "ma", "me", "je", "ve", "sa"] }, months: { names: ["janvier", "février", "mars", "avril", "mai", "juin", "juillet", "août", "septembre", "octobre", "novembre", "décembre", ""], namesAbbr: ["janv.", "févr.", "mars", "avr.", "mai", "juin", "juil.", "août", "sept.", "oct.", "nov.", "déc.", ""] }, AM: ["ص", "ص", "ص"], PM: ["م", "م", "م"], patterns: { d: "MM/dd/yyyy", D: "dddd, MMMM dd, yyyy", t: "hh:mm tt", T: "hh:mm:ss tt", f: "dddd, MMMM dd, yyyy hh:mm tt", F: "dddd, MMMM dd, yyyy hh:mm:ss tt", M: "dd MMMM", Y: "MMMM, yyyy" } }, Gregorian_Arabic: { name: "Gregorian_Arabic", firstDay: 6, days: { names: ["الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"], namesAbbr: ["الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"], namesShort: ["ح", "ن", "ث", "ر", "خ", "ج", "س"] }, months: { names: ["كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران", "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول", ""], namesAbbr: ["كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران", "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول", ""] }, AM: ["ص", "ص", "ص"], PM: ["م", "م", "م"], patterns: { d: "MM/dd/yyyy", D: "dddd, MMMM dd, yyyy", t: "hh:mm tt", T: "hh:mm:ss tt", f: "dddd, MMMM dd, yyyy hh:mm tt", F: "dddd, MMMM dd, yyyy hh:mm:ss tt", M: "dd MMMM", Y: "MMMM, yyyy" } }, Gregorian_TransliteratedFrench: { name: "Gregorian_TransliteratedFrench", firstDay: 6, days: { names: ["الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"], namesAbbr: ["الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"], namesShort: ["ح", "ن", "ث", "ر", "خ", "ج", "س"] }, months: { names: ["جانفييه", "فيفرييه", "مارس", "أفريل", "مي", "جوان", "جوييه", "أوت", "سبتمبر", "اكتوبر", "نوفمبر", "ديسمبر", ""], namesAbbr: ["جانفييه", "فيفرييه", "مارس", "أفريل", "مي", "جوان", "جوييه", "أوت", "سبتمبر", "اكتوبر", "نوفمبر", "ديسمبر", ""] }, AM: ["ص", "ص", "ص"], PM: ["م", "م", "م"], patterns: { d: "MM/dd/yyyy", D: "dddd, MMMM dd, yyyy", t: "hh:mm tt", T: "hh:mm:ss tt", f: "dddd, MMMM dd, yyyy hh:mm tt", F: "dddd, MMMM dd, yyyy hh:mm:ss tt", M: "dd MMMM", Y: "MMMM, yyyy" } } } });;