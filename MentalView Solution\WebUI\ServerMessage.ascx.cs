﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebUI
{
    public enum ServerMessageButtons
    {
        Ok,
        YesNo,
        YesNoCancel
    }

    public enum ButtonClicked
    {
        Ok,
        Yes,
        No,
        Cancel,
        Close
    }

    public class ButtonClickedArgs
    {
        private ButtonClicked buttonClicked;
        private string action;
        private string tag;

        public ButtonClicked ButtonClicked
        {
            get
            {
                return buttonClicked;
            }
            private set
            {
                buttonClicked = value;
            }
        }

        public string Action
        {
            get
            {
                return action;
            }
        }

        public string Tag
        {
            get
            {
                return tag;
            }
        }

        public ButtonClickedArgs(ButtonClicked buttonClicked, string action, string tag)
        {
            this.buttonClicked = buttonClicked;
            this.action = action;
            this.tag = tag;
        }
    }

    public delegate void ButtonClickedHandler(object sender, ButtonClickedArgs e);


    public partial class ServerMessage : System.Web.UI.UserControl
    {
        public event ButtonClickedHandler ButtonClicked;
        private bool show = false;
        private bool isModal = true;

        protected void Page_Load(object sender, EventArgs e)
        {
            //this.Visible = true;
            //this.ejServerMessage.Visible = true;
            this.ejServerMessage.ShowOnInit = this.show;
            this.ejServerMessage.EnableModal = this.isModal;
            if (isModal)
            {
                this.ejServerMessage.ActionButtons.Clear();  //καθαρίζουμε τα ActionButtons για να μην φαίνεται το close.
            }
        }

        public void ShowModal(string header, string message, ServerMessageButtons buttons, string action = "", string tag = "")
        {
            this.ShowDialog(header, message, buttons, true, action, tag);
        }

        public void Show(string header, string message, ServerMessageButtons buttons, string action = "", string tag = "")
        {
            this.ShowDialog(header, message, buttons, false, action, tag);
        }

        public void ShowDialog(string header, string message, ServerMessageButtons buttons, bool isModal, string action = "", string tag = "")
        {
            this.show = true;
            this.isModal = isModal;
            this.ejServerMessage.EnableModal = isModal;
            if (isModal)
            {
                this.ejServerMessage.ActionButtons.Clear();  //καθαρίζουμε τα ActionButtons για να μην φαίνετα το close.
            }
            this.ejServerMessage.ShowOnInit = true;

            //this.header.Text = header;
            this.ejServerMessage.Title = header;
            this.message.Text = message;
            this.tagField.Value = tag;
            this.actionField.Value = action;

            this.okBtn.Visible = false;
            this.yesBtn.Visible = false;
            this.noBtn.Visible = false;
            this.cancelBtn.Visible = false;
            //this.closeBtn.Visible = false;

            switch (buttons)
            {
                case ServerMessageButtons.Ok:
                    {
                        this.okBtn.Visible = true;
                        //this.closeBtn.Visible = true;
                        break;
                    }
                case ServerMessageButtons.YesNo:
                    {
                        this.yesBtn.Visible = true;
                        this.noBtn.Visible = true;
                        this.noBtn.Focus();
                        break;
                    }
                case ServerMessageButtons.YesNoCancel:
                    {
                        this.yesBtn.Visible = true;
                        this.noBtn.Visible = true;
                        this.cancelBtn.Visible = true;
                        this.cancelBtn.Focus();
                        break;
                    }

            };

            Page.ClientScript.RegisterStartupScript(this.GetType(), "key", "ShowMessage();", true);
        }

        protected void okBtn_Click(object sender, EventArgs e)
        {
            ButtonClickedArgs args = new ButtonClickedArgs(WebUI.ButtonClicked.Ok, this.actionField.Value, this.tagField.Value);

            if (ButtonClicked != null)
                ButtonClicked(this, args);
            //this.Visible = false;
        }

        protected void yesBtn_Click(object sender, EventArgs e)
        {
            ButtonClickedArgs args = new ButtonClickedArgs(WebUI.ButtonClicked.Yes, this.actionField.Value, this.tagField.Value);

            if (ButtonClicked != null)
                ButtonClicked(this, args);
        }

        protected void noBtn_Click(object sender, EventArgs e)
        {
            ButtonClickedArgs args = new ButtonClickedArgs(WebUI.ButtonClicked.No, this.actionField.Value, this.tagField.Value);

            if (ButtonClicked != null)
                ButtonClicked(this, args);
        }

        protected void cancelBtn_Click(object sender, EventArgs e)
        {
            ButtonClickedArgs args = new ButtonClickedArgs(WebUI.ButtonClicked.Cancel, this.actionField.Value, this.tagField.Value);

            if (ButtonClicked != null)
                ButtonClicked(this, args);
        }
    }
}