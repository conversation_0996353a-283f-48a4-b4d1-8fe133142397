﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Admin.Data.Model;

[Index("ContactId", Name = "ContactIdIndex")]
[Index("CustomRecurrenceId", Name = "CustomRecurrenceIdIndex")]
[Index("CustomRecurrence", Name = "CustomRecurrenceIndex")]
[Index("Description", Name = "DescriptionIndex")]
[Index("StartTime", "EndTime", Name = "StartEndTimeIndex")]
[Index("Subject", Name = "SubjectIndex")]
[Index("UserId", Name = "UserIdIndex")]
public partial class Appointment
{
    [Key]
    public long AppointmentId { get; set; }

    public long TenantId { get; set; }

    public long? ContactId { get; set; }

    /// <summary>
    /// Ο γιατρός ο οποίος δέχεται το ραντεβού
    /// </summary>
    public long? UserId { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime StartTime { get; set; }

    [Required]
    [StringLength(50)]
    [Unicode(false)]
    public string StartTimeZone { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime EndTime { get; set; }

    [Required]
    [StringLength(50)]
    [Unicode(false)]
    public string EndTimeZone { get; set; }

    [Required]
    [StringLength(50)]
    public string Subject { get; set; }

    [Required]
    [StringLength(250)]
    public string Description { get; set; }

    public bool AllDay { get; set; }

    public bool Canceled { get; set; }

    public bool ChargeableCancellation { get; set; }

    [Required]
    [Unicode(false)]
    public string Categorize { get; set; }

    [Required]
    [Unicode(false)]
    public string ResourceFields { get; set; }

    public bool Recurrence { get; set; }

    [Required]
    [Unicode(false)]
    public string RecurrenceRule { get; set; }

    public long? RecurrenceId { get; set; }

    [Required]
    [Unicode(false)]
    public string RecurrenceExDate { get; set; }

    [Required]
    public string Request { get; set; }

    [Required]
    [StringLength(40)]
    public string State { get; set; }

    [Required]
    public string Therapist { get; set; }

    [Required]
    public string SupervisorInstructionsBefore { get; set; }

    /// <summary>
    /// n
    /// </summary>
    [Required]
    [StringLength(40)]
    public string IntervetionModel { get; set; }

    [Required]
    public string IntervetionTechniques { get; set; }

    [Required]
    public string TherapistComments { get; set; }

    public bool SupervisionRequest { get; set; }

    [Required]
    public string SupervisorCommentsAfter { get; set; }

    [Required]
    [Column(TypeName = "ntext")]
    public string Notes { get; set; }

    [Required]
    [StringLength(30)]
    public string Room { get; set; }

    public bool CustomRecurrence { get; set; }

    [Required]
    [StringLength(50)]
    public string CustomRecurrenceId { get; set; }

    [Required]
    public string CustomRecurrenceRule { get; set; }

    /// <summary>
    /// Appointment= Συνεδρία, Event=Συμβάν
    /// </summary>
    [Required]
    [StringLength(20)]
    public string AppointmentType { get; set; }

    public bool VisibleToAll { get; set; }

    public bool BlockOther { get; set; }

    [Column(TypeName = "money")]
    public decimal Price { get; set; }

    [Column(TypeName = "money")]
    public decimal Deductions { get; set; }

    [Required]
    [StringLength(40)]
    public string PaymentType { get; set; }

    [Required]
    [StringLength(40)]
    public string Bank { get; set; }

    /// <summary>
    /// Μόνο για Συμβάν. Καθορίζει αν είναι Εποπτεία 
    /// </summary>
    public bool TaskSupervision { get; set; }

    /// <summary>
    /// Μόνο για Συμβάν που είναι εποπτεία. Τα IDs των θεραπευτών που θα συμμετέχουν στην εποπτεία. 
    /// </summary>
    [Required]
    [Column("TaskSupervisionTherapistIDs")]
    [StringLength(50)]
    public string TaskSupervisionTherapistIds { get; set; }

    /// <summary>
    /// Μόνο για Συμβάν που είναι εποπτεία. Τα ονόματα των πελατών.
    /// </summary>
    [Required]
    [StringLength(100)]
    public string TaskSupervisionCustomers { get; set; }

    /// <summary>
    /// Μόνο για Συμβάν που είναι εποπτεία. Το Ζήτημα της εποπτείας.
    /// </summary>
    [Required]
    public string TaskSupervisionSubject { get; set; }

    /// <summary>
    /// Μόνο για Συμβάν που είναι εποπτεία. Οι απαντήσεις/προτάσεις στο Ζήτημα/Θεμα της εποπτείας.
    /// </summary>
    [Required]
    public string TaskSupervisionReplies { get; set; }

    public bool ContactEmailNotificationSent { get; set; }

    [ForeignKey("ContactId")]
    [InverseProperty("Appointments")]
    public virtual Contact Contact { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("Appointments")]
    public virtual User User { get; set; }
}