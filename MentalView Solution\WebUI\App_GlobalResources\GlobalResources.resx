﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AddText" xml:space="preserve">
    <value>Προσθήκη</value>
  </data>
  <data name="ApplicationTitle" xml:space="preserve">
    <value>MentalView</value>
  </data>
  <data name="AppointmentReportText" xml:space="preserve">
    <value>Συνεδρία</value>
  </data>
  <data name="BigText" xml:space="preserve">
    <value>Μεγάλο</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Ακύρωση</value>
  </data>
  <data name="CancelText" xml:space="preserve">
    <value>Άκυρο</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Κλείσιμο</value>
  </data>
  <data name="CommentsText" xml:space="preserve">
    <value>Σχόλια</value>
  </data>
  <data name="ContactHasNoEmailMessage" xml:space="preserve">
    <value>Ο πελάτης δεν έχει email.</value>
  </data>
  <data name="ContactReportText" xml:space="preserve">
    <value>Πελάτης</value>
  </data>
  <data name="ContractText" xml:space="preserve">
    <value>Σύμβαση</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Διαγραφή</value>
  </data>
  <data name="DeleteConfirmationMessage" xml:space="preserve">
    <value>Είστε σίγουρος ότι θέλετε να γίνει η διαγραφή;</value>
  </data>
  <data name="DeleteTaskWithUpcomingRecurrencesConfirmationMessage" xml:space="preserve">
    <value>Έχετε επιλέξει να διαγράψετε ένα επαναλαμβανόμενο συμβάν. Θέλετε να διαγράψετε και όλες τις επόμενες επαναλήψεις;&lt;BR/&gt;
- Για να διαγράψετε την συγκεκριμένη επανάληψη και όλες τις επόμενες επαναλήψεις, επιλέξετε &lt;b&gt;Ναι&lt;/b&gt;.&lt;BR/&gt;
- Για να διαγράψετε μόνο την συγκεκριμένη επανάληψη, επιλέξετε &lt;b&gt;Όχι&lt;/b&gt;.&lt;BR/&gt;
- Για να ακυρώσετε τη διαγραφή επιλέξτε &lt;b&gt;Άκυρο&lt;/b&gt;.</value>
  </data>
  <data name="DeleteWithUpcomingRecurrencesConfirmationMessage" xml:space="preserve">
    <value>Έχετε επιλέξει να διαγράψετε μια επαναλαμβανόμενη συνεδρία. Θέλετε να διαγράψετε και όλες τις επόμενες επαναλήψεις;&lt;BR/&gt;
- Για να διαγράψετε την συγκεκριμένη επανάληψη και όλες τις επόμενες επαναλήψεις, επιλέξετε &lt;b&gt;Ναι&lt;/b&gt;.&lt;BR/&gt;
- Για να διαγράψετε μόνο την συγκεκριμένη επανάληψη, επιλέξετε &lt;b&gt;Όχι&lt;/b&gt;.&lt;BR/&gt;
- Για να ακυρώσετε τη διαγραφή επιλέξτε &lt;b&gt;Άκυρο&lt;/b&gt;.</value>
  </data>
  <data name="DoneText" xml:space="preserve">
    <value>Κλείσιμο</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="ExceptionOccuredMessage" xml:space="preserve">
    <value>Συνέβη ένα απρόσμενο σφάλμα.</value>
  </data>
  <data name="ExportToExcel" xml:space="preserve">
    <value>Εξαγωγή σε Excel</value>
  </data>
  <data name="ExportToPdf" xml:space="preserve">
    <value>Εξαγωγή σε PDF</value>
  </data>
  <data name="ExportToWord" xml:space="preserve">
    <value>Εξαγωγή σε Word</value>
  </data>
  <data name="InvalidDateValuesMessage" xml:space="preserve">
    <value>Οι ημερομηνίες δεν είναι σωστά συμπληρωμένες.</value>
  </data>
  <data name="InvalidInputDataMessage" xml:space="preserve">
    <value>Οι επιλογές δεν είναι σωστές.</value>
  </data>
  <data name="InvalidStartEndTimeMessage" xml:space="preserve">
    <value>Οι ημερομηνίες δεν είναι σωστές.</value>
  </data>
  <data name="InProgress" xml:space="preserve">
    <value>Σε εξέλιξη...</value>
  </data>
  <data name="MediumText" xml:space="preserve">
    <value>Μεσαίο</value>
  </data>
  <data name="NoText" xml:space="preserve">
    <value>Όχι</value>
  </data>
  <data name="Preview" xml:space="preserve">
    <value>Προεπισκόπηση</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Εκτύπωση</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Αποθήκευση</value>
  </data>
  <data name="SaveClose" xml:space="preserve">
    <value>Αποθήκευση &amp; Κλείσιμο</value>
  </data>
  <data name="SessionExpiredMessage" xml:space="preserve">
    <value>Η διαδικασία δεν μπορεί να ολοκληρωθεί γιατί το session έχει λήξει.</value>
  </data>
  <data name="SmallText" xml:space="preserve">
    <value>Μικρό</value>
  </data>
  <data name="SummaryText" xml:space="preserve">
    <value>Σύνολο</value>
  </data>
  <data name="SupervisorCommentsText" xml:space="preserve">
    <value>Σχόλια Επόπτη</value>
  </data>
  <data name="TaskText" xml:space="preserve">
    <value>Εργασία</value>
  </data>
  <data name="TherapistCommentsText" xml:space="preserve">
    <value>Σχόλια Θεραπευτή</value>
  </data>
  <data name="TimeNowText" xml:space="preserve">
    <value>Τώρα</value>
  </data>
  <data name="TimeText" xml:space="preserve">
    <value>Ώρα</value>
  </data>
  <data name="TodayText" xml:space="preserve">
    <value>Σήμερα</value>
  </data>
  <data name="TotalSummaryText" xml:space="preserve">
    <value>Τελικό Σύνολο</value>
  </data>
  <data name="UserHasNoEmailMessage" xml:space="preserve">
    <value>Ο χρήστης δεν έχει email.</value>
  </data>
  <data name="VeryBigText" xml:space="preserve">
    <value>Πολύ Μεγάλο</value>
  </data>
  <data name="WrongCredentialsMessage" xml:space="preserve">
    <value>Τα στοιχεία πρόσβασης δεν είναι σωστά.</value>
  </data>
  <data name="YesText" xml:space="preserve">
    <value>Ναι</value>
  </data>
  <data name="AccountNotExists" xml:space="preserve">
    <value>Ο λογαριασμός δεν υπάρχει. Συνδεθείτε στο &lt;a href="https://mentalview.gr" target="_blank"&gt;mentalview.gr&lt;/a&gt; για να δημιουργήσετε ένα νέο λογαριασμό.</value>
  </data>
  <data name="SubscriptionExpired" xml:space="preserve">
    <value>Η συνδρομή σας έχει λήξει. Συνδεθείτε στο &lt;a href="https://mentalview.gr" target="_blank"&gt;mentalview.gr&lt;/a&gt; για να αποκτήσετε νέα συνδρομή.</value>
  </data>
  <data name="NoPermissionToViewContactMessage" xml:space="preserve">
    <value>Δεν επιτρέπεται να δείτε τον πελάτη.</value>
  </data>
  <data name="DeleteProhibitedMessage" xml:space="preserve">
    <value>Δεν επιτρέπεται η διαγραφή.</value>
  </data>
</root>