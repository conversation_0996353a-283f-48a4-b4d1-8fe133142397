﻿using Admin;
using Admin.Business;
using Admin.Data.Model;
using Microsoft.EntityFrameworkCore;

namespace Admin.Business
{
    public class RolesBusiness : IRolesBusiness
    {
        private Admin.Data.Model.MentalViewContext dbContext;
        private IConfiguration configuration;
        private ILogger<RolesBusiness> logger;

        public RolesBusiness(Admin.Data.Model.MentalViewContext dbContext, IConfiguration configuration, ILogger<RolesBusiness> logger)
        {
            this.dbContext = dbContext;
            this.logger = logger;
            this.configuration = configuration;
        }

        public async Task<List<Admin.Data.Model.Role>> GetAllRoles(Int64 tenantId)
        {
            try
            {
                //Validation
               
                //Query
                IQueryable<Admin.Data.Model.Role> query = this.dbContext.Roles.AsNoTracking().AsQueryable();
                query = query.Where(x => x.TenantId == tenantId).OrderBy(e => e.RoleId);

                //Διαβάζει τα δεδομένα.
                List<Admin.Data.Model.Role> roles = await query.ToListAsync();
                
                return roles;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

       
        public async Task<Admin.Data.Model.Role?> GetRole(Int64 roleId)
        {
            try
            {
                //Query
                IQueryable<Admin.Data.Model.Role> query = this.dbContext.Roles.AsNoTracking().Where(x => x.RoleId == roleId);
                Admin.Data.Model.Role? role = await query.FirstOrDefaultAsync();

                //Result
                return role;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task CreateOrUpdateRole(Role role)
        {
            try
            {
                //Validation
                if (role == null)
                {
                    throw new Exception(GlobalResources.InvalidDataMessage);
                }

                //RoleValidator validator = new RoleValidator();
                //FluentValidation.Results.ValidationResult result = validator.Validate(role);
                //string validationErrors = string.Empty;
                //if (!result.IsValid)
                //{
                //    foreach (var failure in result.Errors)
                //    {
                //        validationErrors += failure.ErrorMessage + ". ";
                //    }
                //    throw new ApplicationException(validationErrors);
                //}

                //List<ValidationResult> validationResults = new List<ValidationResult>();
                //ValidationContext validationContext = new ValidationContext(role);
                //if (Validator.TryValidateObject(role, validationContext, validationResults, true) == false)
                //{
                //    string validationErrors = string.Empty;
                //    foreach (CompositeValidationResult compValidationResult in validationResults)
                //    {
                //        foreach (ValidationResult validationResult in compValidationResult.Results)
                //        {
                //            validationErrors += validationResult.ErrorMessage + ". ";
                //        }
                //    }
                //    throw new ApplicationException(validationErrors);
                //}

                //Query
                this.dbContext.Attach(role);
                await this.dbContext.SaveChangesAsync();

                //Response
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { role.RoleId });
                throw;
            }
        }

        public async Task DeleteRole(Int64 roleId)
        {
            //Role? role = await this.dbContext.Roles.Where(x => x.RoleId == roleId).AsNoTracking().FirstAsync();
            //if (role != null)
            //{
            //    role.ObjectState = ObjectState.Deleted;
            //    this.dbContext.Attach(role);
            //    this.dbContext.SaveChanges();
            //}
        }

        public async Task<Role?> CheckRoleExists(Int64 tenantId, Int64 roleId, string name)
        {
            Role? role = await this.dbContext.Roles.Where(x => x.RoleId != roleId && x.Name == name && x.TenantId == tenantId).FirstOrDefaultAsync();
            return role;
        }

        public async Task CreateRolesForTenant(Int64 tenantId)
        {
            try
            {
                //Validation
                if (tenantId == null)
                {
                    throw new Exception(GlobalResources.InvalidDataMessage);
                }

                //Adds SuperAdmin
                Role superAdmin = new Role();
                superAdmin.TenantId = tenantId;
                superAdmin.Name = "SuperAdmin";
                this.dbContext.Attach(superAdmin);
                //Adds Admin
                Role admin = new Role();
                admin.TenantId = tenantId;
                admin.Name = "Admin";
                this.dbContext.Attach(admin);
                //Adds User
                Role user = new Role();
                user.TenantId = tenantId;
                user.Name = "User";
                this.dbContext.Attach(user);

                await this.dbContext.SaveChangesAsync();

                //Response
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { tenantId });
                throw;
            }
        }
    }
}
