/*!
*  filename: ej.mobile.splitpane.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function(n,i,r){i.widget("ejmSplitPane","ej.mobile.SplitPane",{_setFirst:!0,_rootCSS:"e-m-splitpane",_ignoreOnPersist:["create","destroy","beforeOpen","beforeClose","close","open","swipe"],angular:{terminal:!1},defaults:{renderMode:"auto",cssClass:"",enablePersistence:!1,height:"auto",width:"auto",isRelative:!1,edgeThreshold:50,enableSwipe:!0,stopEventPropagation:i.isWindows(),leftPane:{templateId:null,showOnTablet:!0,animationType:"overlay",width:"280px"},rightPane:{templateId:null,showOnTablet:!1,animationType:"overlay",width:"280px"},contentPane:{templateId:null},swipe:null,open:null,close:null,beforeClose:null,beforeOpen:null,create:null,destroy:null},dataTypes:{renderMode:"enum",enablePersistence:"boolean",isRelative:"boolean",edgeThreshold:"number",stopEventPropagation:"boolean",leftPane:"object",rightPane:"object",contentPane:"object"},_lModel:{overlayOpacity:.3,swipeThreshold:15,defaultTransitionTime:400,fastTransitionTime:200},_class:{opened:"e-m-opened",hide:"e-m-hide",show:"e-m-show",hidden:"e-m-hidden"},_init:function(){this._isNavPaneOpened=!1;this._orgEleStyle=n(this.element).attr("style");this._orgEleClass=n(this.element).attr("class");this._browser=i.browser().toLowerCase();this._cssClass=this.model.cssClass;i.setRenderMode(this);this._initPane();this._renderControl();this._createDelegates();this._wireEvents()},_initPane:function(){return this._panel={left:this.model.leftPane.templateId?this._getTemplate(this.model.leftPane.templateId,"_leftpane"):this.element.find("[data-ej-pane='left']").length?this.element.find("[data-ej-pane='left']"):this.element.find("[e-pane='left']"),content:this.model.contentPane.templateId?this._getTemplate(this.model.contentPane.templateId,"_contentpane"):this.element.find("[data-ej-pane='content']").length?this.element.find("[data-ej-pane='content']"):this.element.find("[e-pane='content']"),right:this.model.rightPane.templateId?this._getTemplate(this.model.rightPane.templateId,"_rightpane"):this.element.find("[data-ej-pane='right']").length?this.element.find("[data-ej-pane='right']"):this.element.find("[e-pane='right']")},this._panel.left.addClass("e-m-type-"+this.model.leftPane.animationType+" e-m-sp-left e-m-pane e-m-hidden e-m-abs"),this._panel.right.addClass("e-m-type-"+this.model.rightPane.animationType+" e-m-sp-right e-m-pane e-m-hidden e-m-abs"),this._panel.content.addClass("e-m-sp-content e-m-pane e-m-abs"),this._elementOverlay=i.buildTag("div#"+this._id+"_Overlay",{},{},{"class":"e-m-overlay e-m-splitpane-overlay e-m-hide e-m-abs"}),this._elementOverlay.appendTo(this._panel.content),this},_getTemplate:function(t,r){var u=null;return n("#"+t)[0].nodeName=="SCRIPT"?(u=i.buildTag("div#"+this._id+r),u.html(n("#"+t).text()).appendTo(this.element),i.widget.init(u),i.angular.defaultAppName&&angular&&i.angular.compile(u)):(u=n("#"+t),n("#"+t).parent()!=this.element&&u.appendTo(this.element)),u},_renderControl:function(){this._direction=["left","right"];this.element.addClass("e-m-splitpane e-m-"+this.model.renderMode+" e-m-"+(i.isMobile()?"mobile ":"tablet ")+this.model.cssClass).addClass(i.isLowerResolution()?"e-m-lower":"e-m-higher").addClass(this.model.isRelative?"e-m-rel":"e-m-abs");this._setPaneSize()},_createDelegates:function(){this._swipeStartHandler=n.proxy(this._swipeStart,this);this._swipeEndHandler=n.proxy(this._swipeEnd,this);this._swipeMoveHandler=n.proxy(this._swipeMove,this);this._overlayTapStartDelegate=n.proxy(this._overlayTapStart,this);this._overlayTapEndDelegate=n.proxy(this._overlayTapEnd,this);this._resizeDelegate=n.proxy(this._resize,this)},_wireEvents:function(n){var t=!n&&this.model.enableSwipe?"bind":"unbind";this._bindSwipeEvent(n);i.listenTouchEvent(this._elementOverlay,i.startEvent(),this._overlayTapStartDelegate,n);i.isTouchDevice()&&this._isOrientationSupported()?i.listenTouchEvent(window,"orientationchange",this._resizeDelegate,n,this):i.listenTouchEvent(window,"resize",this._resizeDelegate,n,this)},_bindSwipeEvent:function(){this.model.enableSwipe&&this._on(this._panel.content,"mousedown touchstart MSPointerDown pointerdown",this._swipeStartHandler)},_unbindSwipeEvent:function(){this.model.enableSwipe&&(this._off(this._panel.content,"mousedown touchstart MSPointerDown pointerdown",this._swipeStartHandler),this._off(t.element,"mousemove touchmove MSPointerMove pointermove",this._swipeMoveHandler),this._off(t.element,"mouseup touchend MSPointerUp pointerup",this._swipeEndHandler),this._off(t.element,"mousecancel touchcancel MSPointerCancel pointercancel",this._swipeEndHandler))},_overlayTapStart:function(){i.listenTouchEvent(this._elementOverlay,i.endEvent(),this._overlayTapEndDelegate)},_overlayTapEnd:function(){this._cancelClose||this._closeOpenedPane(this._lModel.defaultTransitionTime);i.listenTouchEvent(this._elementOverlay,i.endEvent(),this._overlayTapEndDelegate,!0)},_swipeStart:function(n){if(this._isNavPaneOpened!=!0){var r=n.originalEvent.changedTouches?n.originalEvent.changedTouches[0]:n,t=this;if(t._startX=r.clientX,t._cancelClose=null,t._startTime=new Date,t._ap=r.clientX<t.element.offset().left+t.model.edgeThreshold?"left":t._ap=r.clientX<t.element.offset().left+t.model.edgeThreshold?"left":r.clientX>t.element.width()-t.model.edgeThreshold?"right":"",t._isPaneOpen()&&(t._ap=t._isPaneOpen()),t._ap&&t._panel[t._ap].length)t._tp=t._getTransitionPane(t._ap),i.isLowerResolution()*t.model.rightPane.showOnTablet&&t._panel.right.hide(),i.isLowerResolution()*t.model.leftPane.showOnTablet&&t._panel.left.hide(),t._panel[t._ap].show(),t.model.stopEventPropagation&&i.browserInfo().name!="msie"&&i.blockDefaultActions(n);else return;this._on(t.element,"mousemove touchmove MSPointerMove pointermove",this._swipeMoveHandler);this._on(t.element,"mouseup touchend MSPointerUp pointerup",this._swipeEndHandler);this._on(t.element,"mousecancel touchcancel MSPointerCancel pointercancel",this._swipeEndHandler)}},_swipeMove:function(n){var u,t;if(this._isNavPaneOpened!=!0&&this._ap){u=n.originalEvent.changedTouches?n.originalEvent.changedTouches[0]:n;t=this;t.model.stopEventPropagation&&i.browserInfo().name=="msie"&&i.blockDefaultActions(n);t._swipeDirection=t._startX>u.clientX?"right":t._startX==u.clientX?"":"left";var r=u.clientX-t._startX,f=t._panel[t._ap].outerWidth(),e=t._ap=="left"?1:-1;t._panel[t._swipeDirection]&&t._panel[t._swipeDirection].length&&!t._isPaneOpen()&&t._panel[t._swipeDirection].removeClass(t._class.hidden).addClass("e-m-opening");t._tp&&!t._isPaneOpen()&&t._ap?(r=f<=Math.abs(r)?e*f:r,t._transform(r,0,t._tp)._elementOverlay.removeClass(t._class.hide),t.model[t._ap+"Pane"].animationType!="overlay"?t._elementOverlay.css("opacity",0):t._elementOverlay.css("opacity",Math.abs(r/t._panel[t._ap].width())*t._lModel.overlayOpacity)):t._tp&&t._ap&&t._isPaneOpen()!=t._swipeDirection&&t._swipeDirection!=""&&(r=f<=Math.abs(r)?0:e*r+f,t._transform(e*r,0,t._tp),t._cancelClose=!0,t.model[t._ap+"Pane"].animationType!="reveal"&&t._elementOverlay.css("opacity",Math.abs(r/t._panel[t._ap].width())*t._lModel.overlayOpacity));t._movedX=r}},_swipeEnd:function(n){if(this._isNavPaneOpened!=!0&&this._ap!=null&&this._ap!=""){var s=n.originalEvent.changedTouches?n.originalEvent.changedTouches[0]:n,t=this,r=Math.abs(t._movedX),i=new Date-t._startTime,o=!1;if(Math.abs(i)<100&&(o=!0),t._panel[t._swipeDirection]&&t._panel[t._swipeDirection].length&&!t._isPaneOpen()&&t._panel[t._swipeDirection].removeClass("e-m-opening"),i=t._getMomentumTime(t._movedX,Math.abs(i),t._panel[t._ap].outerWidth()),i=i>500?500:i,t._cancelClose&&t._swipeDirection||r&&!t._isPaneOpen()&&t._tp&&t._ap){var u=t._panel[t._ap].outerWidth(),f=t._ap=="left"?u:-u,e=t._panel[t._ap].outerWidth()/100*t._lModel.swipeThreshold;e<r&&!t._cancelClose||e>u-r&&t._cancelClose?(t._transform(f,!t._isPaneOpen()&&!t._cancelClose?i:t._lModel.defaultTransitionTime,t._tp),opacity=t.model[t._ap+"Pane"].animationType!="overlay"?0:Math.abs(f/t._panel[t._ap].width())*t._lModel.overlayOpacity,t._elementOverlay.css("opacity",opacity),t._panel[t._ap].addClass(t._class.opened)):t._cancelClose?t._closeOpenedPane(i<100?100:i):(t._transform(0,t._lModel.fastTransitionTime,t._tp)._elementOverlay.addClass(t._class.hide),t._panel[t._ap].addClass(t._class.hidden))}else t._closeOpenedPane(t._lModel.defaultTransitionTime);this._trigger("swipe",{direction:t._swipeDirection,event:n});t._lastClientX=t._swipeDirection=t._ap=t._tp=t._movedX=t._startTime=null;this._on(t.element,"mousemove touchmove MSPointerMove pointermove",this._swipeMoveHandler);this._on(t.element,"mouseup touchend MSPointerUp pointerup",this._swipeEndHandler);this._on(t.element,"mousecancel touchcancel MSPointerCancel pointercancel",this._swipeEndHandler)}},_getMomentumTime:function(n,t,i){return Math.abs(t-Math.abs(i/n*t))},_getTransitionPane:function(n){return this.model[n+"Pane"].animationType=="reveal"?this._panel.content:this._panel[n]},_isPaneOpen:function(){var n="";return this.element.find("."+this._class.opened).length&&(n=this.element.children("."+this._class.opened).hasClass("e-m-sp-left")?"left":"right"),n},_isOrientationSupported:function(){return"orientation"in window&&"onorientationchange"in window},_transform:function(n,t,i,r){if(n=="none")return this;var f="-"+this._browser+"-transform",e="translateX("+n+"px) translateZ(0px)",o="-"+this._browser+"-transition-property",s="-"+this._browser+"-transition-duration",u=t+"ms";return i.css(o,"transform").css(s,u).css(f,e).css("transition-duration",u),r&&i.addClass(r),this},_closeOpenedPane:function(n){if(n=n==r?0:n,this._isPaneOpen()){var u=this._isPaneOpen(),f=this._getTransitionPane(u),t=this;this._elementOverlay.addClass(this._class.hide);this._trigger("beforeClose",this._getArgData(u));f.bind(i.transitionEndEvent(),function(){t._trigger("close",t._getArgData(u));t._panel.left.length&&t._panel.left.removeClass(t._class.hidden);t._panel.right.length&&t._panel.right.removeClass(t._class.hidden);f.unbind(i.transitionEndEvent())});this._transform(0,n,f).element.children("."+this._class.opened).removeClass(this._class.opened)}return this._isNavPaneOpened=!1,this},_resize:function(){this._closeOpenedPane(0)._setPaneSize();this.element.removeClass("e-m-lower e-m-higher").addClass(i.isLowerResolution()?"e-m-lower":"e-m-higher")},_setPaneSize:function(){var n,t;return this._panel.content.css({right:"",left:""}),this._panel.left.length&&(this._panel.left.css({width:this.model.leftPane.width,left:""}),!i.isLowerResolution()*this.model.leftPane.showOnTablet?(this._panel.left.addClass("e-m-exposed").removeClass(this._class.hidden),this._panel.content.css("left",this._panel.left.outerWidth())):(n=this.model.leftPane.width!="auto"?"-"+this.model.leftPane.width:-i.getDimension(this._panel.left,"width"),this.model.leftPane.animationType=="overlay"&&this._panel.left.css("left",n).addClass(this._class.hidden),this._panel.left.removeClass("e-m-exposed"))),this._panel.right.length&&(this._panel.right.css({width:this.model.rightPane.width,right:""}),!i.isLowerResolution()*this.model.rightPane.showOnTablet?(this._panel.right.addClass("e-m-exposed").removeClass(this._class.hidden),this._panel.content.css("right",this._panel.right.outerWidth())):(t=this.model.rightPane.width!="auto"?"-"+this.model.rightPane.width:-i.getDimension(this._panel.right,"width"),this.model.rightPane.animationType=="overlay"&&this._panel.right.css("right",t).addClass(this._class.hidden),this._panel.right.removeClass("e-m-exposed"))),this.element.css({height:this.model.height,width:this.model.width}),this},_setModel:function(n){for(var t in n){switch(t){case"renderMode":this._setRenderMode(n.renderMode);break;case"cssClass":this.element.removeClass(this._cssClass).addClass(n.cssClass);this._cssClass=n.cssClass;break;case"isRelative":this.element.removeClass("e-m-rel e-m-abs").addClass(this.model.isRelative?"e-m-rel":"e-m-abs");break;case"height":case"width":this.element.css({height:this.model.height,width:this.model.width});break;case"leftPane":case"rightPane":case"contentPane":this["_set"+t.charAt(0).toUpperCase()+t.slice(1)](n[t])}!1&&this._refresh()}},_setRenderMode:function(n){n=="auto"&&i.setRenderMode(this);this.element.removeClass("e-m-ios7 e-m-android e-m-windows e-m-flat").addClass("e-m-"+this.model.renderMode);this._setPaneSize()},_setLeftPane:function(n){for(var t in n)switch(t){case"animationType":this._panel.left.removeClass("e-m-type-overlay");case"width":case"showOnTablet":this._setPaneSize();break;case"templateId":this._setTemplate(n[t],"left")}},_setRightPane:function(n){for(var t in n)switch(t){case"animationType":this._panel.right.removeClass("e-m-type-overlay");case"width":case"showOnTablet":this._setPaneSize();break;case"templateId":this._setTemplate(n[t],"right")}},_setTemplate:function(t,i){this._closeOpenedPane();n("#"+t).removeAttr("class style");this._panel[i].length&&this._panel[i].removeAttr("class style").addClass(this._class.hidden);this._initPane()._setPaneSize()},_setContentPane:function(n){for(var t in n)switch(t){case"templateId":this._bindSwipeEvent(!0);this._setTemplate(n[t],"content");this._bindSwipeEvent()}},_contentTransition:function(t){n(t.target).hasClass("new")&&n(t.target).contents().unwrap();n(t.target).hasClass("old")&&n(t.target).remove()},_getArgData:function(n){return{panel:n,element:this._panel[n],content:this._panel.content}},_isCanOpen:function(n){return this.model[n+"Pane"].showOnTablet==!0&&i.isLowerResolution()==!1?!1:!0},_refresh:function(){this._clearElements();this._renderControl();this.model.renderTemplate&&i.angular.defaultAppName&&i.angular.compile(this._layouts);this._wireEvents()},_clearElements:function(){this.element.removeAttr("class style");this._panel.left.length&&this._panel.left.removeAttr("class style");this._panel.content.length&&this._panel.content.removeAttr("class style");this._panel.right.length&&this._panel.right.removeAttr("class style");this._elementOverlay&&this._elementOverlay.remove();this.element.attr({style:this._orgEleStyle,"class":this._orgEleClass}).removeClass(this._rootCSS)},_destroy:function(){this._wireEvents(!0);this._unbindSwipeEvent();this._clearElements()},openLeftPane:function(n){if(!this._isPaneOpen()&&this._panel.left.length&&this._isCanOpen("left")){var t=this._getTransitionPane("left"),r=this;return this._panel.left.removeClass(this._class.hidden),this._trigger("beforeOpen",this._getArgData("left")),t.bind(i.transitionEndEvent(),function(){r._trigger("open",r._getArgData("left"));t.unbind(i.transitionEndEvent())}),this._transform(this._panel.left.outerWidth(),n?n:this._lModel.defaultTransitionTime,t),opacity=this.model.leftPane.animationType!="overlay"?0:this._lModel.overlayOpacity,this._elementOverlay.css("opacity",opacity).removeClass(this._class.hide),this._panel.left.addClass(this._class.opened),this._isNavPaneOpened=!0,this}},openRightPane:function(n){if(!this._isPaneOpen()&&this._panel.right.length&&this._isCanOpen("right")){var t=this._getTransitionPane("right"),r=this;return this._panel.right.removeClass(this._class.hidden),this._trigger("beforeOpen",this._getArgData("right")),t.bind(i.transitionEndEvent(),function(){r._trigger("open",r._getArgData("right"));t.unbind(i.transitionEndEvent())}),this._transform(-this._panel.right.outerWidth(),n?n:this._lModel.defaultTransitionTime,t),opacity=this.model.rightPane.animationType!="overlay"?0:this._lModel.overlayOpacity,this._elementOverlay.css("opacity",opacity).removeClass(this._class.hide),this._panel.right.addClass(this._class.opened),this}},closePane:function(n){this._closeOpenedPane(n?n:this._lModel.defaultTransitionTime)}})})(jQuery,Syncfusion)});
