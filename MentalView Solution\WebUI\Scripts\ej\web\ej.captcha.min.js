/*!
*  filename: ej.captcha.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function(n,t,i){t.widget("ejCaptcha","ej.Captcha",{element:null,model:null,validTags:["div"],_setFirst:!1,_rootCSS:"e-captcha",defaults:{enablePattern:!0,targetInput:"",targetButton:"",height:50,width:150,characterSet:"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",maximumLength:8,minimumLength:4,enableCaseSensitivity:!0,enableAutoValidation:!1,encryptedCode:"",customErrorMessage:"Invalid Captcha",showAudioButton:!1,showRefreshButton:!1,locale:"en-US",enableRTL:!1,requestMapper:"",refreshBegin:"",refreshSuccess:"",refreshFailure:"",refreshComplete:"",mapper:"",hatchStyle:"BackwardDiagonal"},dataTypes:{targetInput:"string",targetButton:"string",height:"number",width:"number",characterSet:"string",maximumLength:"number",minimumLength:"number",enableCaseSensitivity:"boolean",enableAutoValidation:"boolean",encryptedCode:"string",customErrorMessage:"string",requestMapper:"string",showAudioButton:"boolean",locale:"string",showRefreshButton:"boolean",enableRTL:"boolean",mapper:"string",hatchStyle:"enum",enablePattern:"boolean"},_updateLocalConstant:function(){this._localizedLabels=t.getLocalizedConstants("ej.Captcha",this.model.locale)},_getLocalizedLabels:function(n){return this._localizedLabels[n]===i?t.Captcha.Locale["en-US"][n]:this._localizedLabels[n]},_init:function(){this._initialize();this._wireEvents()},_initialize:function(){this._updateLocalConstant();n("#"+this._id+"_RefreshButton").ejButton({size:"normal",showRoundedCorner:!0,contentType:"imageonly",prefixIcon:"e-icon e-captcha e-reload",type:"button"});n("#"+this._id+"_PlayAudio").ejButton({size:"normal",showRoundedCorner:!0,contentType:"imageonly",prefixIcon:"e-icon e-captcha e-volume-up",type:"button"});n("#"+this._id+"_ValidText").attr("placeholder",this._getLocalizedLabels("placeHolderText"));this._control=n("#"+this._id).get(0);this.model.targetInput||(this.model.targetInput=this._id+"_ValidText");this._target=n("#"+this.model.targetInput).get(0);this._captchaImage=n("#"+this._id+"_CaptchaImage").get(0);this._message=n("#"+this._id+"_CaptchaMessage").get(0);this._refreshButton=n("#"+this._id+"_RefreshButton").get(0);this._form=n(this._target).parents("form")[0];this._audioObject=n("#"+this._id+"_AudioObject").get(0);this._audioButton=n("#"+this._id+"_PlayAudio").get(0);this._audioPluginObject=null;this._audioType="audio/wav";t.isNullOrUndefined(this.model.targetButton)||this.model.targetButton===""||(this._submitButton=n("#"+this.model.targetButton).get(0),this._submitButton||(this._submitButton=n("[id$='"+this.model.targetButton+"']").get(0)));this._hiddenField=n("#"+this._id+"_Hidden").get(0);this._audioObject&&(this._isSupportAudio(this._audioType)||this._appendAudioPlugin())},_wireEvents:function(){if(this._FormSubmitDelegate=n.proxy(this.formSubmit,this),this._CaptchaRefreshDelegate=n.proxy(this.refresh,this),this._PlayAudioDelegate=n.proxy(this.playAudio,this),this._audioButton)n(this._audioButton).on("click",this._PlayAudioDelegate);if(this._submitButton)n(this._submitButton).on("click",this._FormSubmitDelegate);if(this._refreshButton)n(this._refreshButton).on("click",this._CaptchaRefreshDelegate)},_unwireEvents:function(){this._audioButton&&n(this._audioButton).off("click",this._PlayAudioDelegate);this._submitButton&&n(this._submitButton).off("click",this._FormSubmitDelegate);this._refreshButton&&n(this._refreshButton).off("click",this._CaptchaRefreshDelegate)},playAudio:function(){try{this._audioPluginObject?this._audioPluginObject.Play():this._audioObject&&this._audioObject.play&&this._audioObject.play()}catch(n){}},_isSupportAudio:function(n){return document.createElement("audio").canPlayType?document.createElement("audio").canPlayType(n).match(/maybe|probably/i)?!0:!1:!1},_appendAudioPlugin:function(){n(this._audioPluginObject).remove();var t=this._createAudioPluginObject();this._audioObject.parentNode.appendChild(t);this._audioPluginObject=n("#"+this._id+"_AudioPlugin").get(0)},_createAudioPluginObject:function(){var n=document.createElement("embed");return n.setAttribute("id",this._id+"_AudioPlugin"),n.setAttribute("src",this.model.audioUrl),n.setAttribute("name","AudioPlugin"),n.setAttribute("enablejavascript","true"),n.setAttribute("type","audio/wav"),n.setAttribute("autostart","false"),n.setAttribute("pluginspage","http://www.apple.com/quicktime/download/"),n.style.top=0,n.style.left=0,n.style.width="0px",n.style.height="0px",n.style.position="absolute",n},_onSuccess:function(t){var i=t.d?JSON.parse(t.d):t;try{this._audioPluginObject?this._audioPluginObject.Pause():this._audioObject&&this._audioObject.pause&&this._audioObject.pause()}catch(r){}this._captchaImage.src=i.NewChallenge;this._audioObject&&(this._audioObject.src=i.AudioLink);this.model.audioURL=i.AudioLink;this._audioPluginObject&&this._appendAudioPlugin();this.model.isValid=i.Validation.toLocaleLowerCase()=="false"?!1:!0;this.model.enableAutoValidation&&this._displayMessages();n("#"+this._id+"_ValidText")&&n("#"+this._id+"_ValidText").val("");this.model.encryptedCode=i.Script;n(this._hiddenField).val(this.model.encryptedCode)},_displayMessages:function(){this.model.isValid?n("#"+this._id+"_CaptchaMessage").html(""):(n("#"+this._id+"_CaptchaMessage").html(this.model.locale=="en-US"?this.model.customErrorMessage:t.Captcha.Locale[this.model.locale].customErrorMessage),n("#"+this._id+"_ValidText").addClass("error"))},_onRefreshSuccess:function(t){var i=t.d?JSON.parse(t.d):t;try{this._audioPluginObject?this._audioPluginObject.Pause():this._audioObject&&this._audioObject.pause&&this._audioObject.pause()}catch(r){}this._captchaImage.src=i.NewChallenge;this._audioObject&&(this._audioObject.src=i.AudioLink);this.model.audioUrl=i.AudioLink;this._audioPluginObject&&this._appendAudioPlugin();this.model.encryptedCode=i.EncryptedText;n(this._hiddenField).val(i.EncryptedText)},formSubmit:function(t){var i=this,r={Height:this.model.height,Width:this.model.width,CharacterSet:this.model.characterSet,MaximumLength:this.model.maximumLength,MinimumLength:this.model.minimumLength,CaseSensitive:this.model.enableCaseSensitivity,ShowRefreshButton:this.model.showRefreshButton,ShowAudioButton:this.model.showAudioButton,EncryptedText:this.model.encryptedCode,ResponseText:n(this._target).val(),Id:this._id,HatchStyle:this.model.hatchStyle,EnablePattern:this.model.enablePattern,RequestType:"CaptchaRequest",ActionType:"Validation"};return n.ajax({type:"POST",url:this.model.requestMapper?this.model.requestMapper:this.model.pathName+"/"+this.model.mapper,data:JSON.stringify({captchaModel:r}),dataType:"json",contentType:"application/json; charset=utf-8",async:!1,success:function(n){n&&i._onSuccess(n);return},error:function(){i._trigger("refreshFailure");return}}),!this.model.isValid&&this.model.enableAutoValidation?(t.preventDefault(),t.stopPropagation(),!1):void 0},refresh:function(){var t=this,i={Height:this.model.height,Width:this.model.width,CharacterSet:this.model.characterSet,MaximumLength:this.model.maximumLength,MinimumLength:this.model.minimumLength,CaseSensitive:this.model.enableCaseSensitivity,EncryptedText:this.model.encryptedCode,ShowRefreshButton:this.model.showRefreshButton,ShowAudioButton:this.model.showAudioButton,Id:this._id,HatchStyle:this.model.hatchStyle,EnablePattern:this.model.enablePattern,RequestType:"CaptchaRequest",ActionType:"Refresh"};if(t._trigger("refreshBegin"))return!1;n.ajax({type:"POST",url:this.model.requestMapper?this.model.requestMapper:this.model.pathName+"/"+this.model.mapper,data:JSON.stringify({captchaModel:i}),dataType:"json",contentType:"application/json; charset=utf-8",async:!1,success:function(n){n&&(t._onRefreshSuccess(n),t._trigger("refreshSuccess"));return},error:function(){t._trigger("refreshFailure");return},complete:function(){t._trigger("refreshComplete");return}})}});t.HatchStyle={None:"none",BackwardDiagonal:"backwardDiagonal",Cross:"cross",DarkDownwardDiagonal:"darkDownwardDiagonal",DarkHorizontal:"darkHorizontal",DarkUpwardDiagonal:"darkUpwardDiagonal",DarkVertical:"darkVertical",DashedDownwardDiagonal:"dashedDownwardDiagonal",DashedHorizontal:"dashedHorizontal",DashedUpwardDiagonal:"dashedUpwardDiagonal",DashedVertical:"dashedVertical",DiagonalBrick:"diagonalBrick",DiagonalCross:"diagonalCross",Divot:"divot",DottedDiamond:"dottedDiamond",DottedGrid:"dottedGrid",ForwardDiagonal:"forwardDiagonal",Horizontal:"horizontal",HorizontalBrick:"horizontalBrick",LargeCheckerBoard:"largeCheckerBoard",LargeConfetti:"largeConfetti",LargeGrid:"largeGrid",LightDownwardDiagonal:"lightDownwardDiagonal",LightHorizontal:"lightHorizontal",LightUpwardDiagonal:"lightUpwardDiagonal",LightVertical:"lightVertical",Max:"max",Min:"min",NarrowHorizontal:"narrowHorizontal",NarrowVertical:"narrowVertical",OutlinedDiamond:"outlinedDiamond",Percent90:"percent90",Wave:"wave",Weave:"weave",WideDownwardDiagonal:"wideDownwardDiagonal",WideUpwardDiagonal:"wideUpwardDiagonal",ZigZag:"zigZag"};t.Captcha.Locale=t.Captcha.Locale||{};t.Captcha.Locale["default"]=t.Captcha.Locale["en-US"]={placeHolderText:"Type the code shown",CustomErrorMessage:"Invalid Captcha"}})(jQuery,Syncfusion)});
