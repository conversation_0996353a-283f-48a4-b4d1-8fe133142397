/*!
*  filename: ej.web.react.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["react","./ej.core.min"],n):n()})(function(){(function(n,t,i){"use strict";var f={},r={firstCap:function(n){return n.charAt(0).toUpperCase()+n.slice(1)},loadTags:function(n,t){var e=n,f,o,i,u;if(e)return e;for(e={},i=0;i<t.length;i++){for(f=t[i].attr||[],o={},u=0;u<f.length;u++)typeof f[u]=="object"?o["e-tag"]=r.loadTags(null,f[u]):o[f[u].toLowerCase().replace(/\./g,"-")]=f[u];e[t[i].tag.toLowerCase().replace(/\./g,"-")]={field:t[i].tag,attr:o,content:t[i].content,singular:(t[i].singular||t[i].tag.replace(/s$/,"")).replace(/\./g,"-")}}return e},getTagValues:function(i,u,f){var h=n(u.children),l={},e,s,v,y;if(h.length===0)return l;for(e=0;e<h.length;e++){var c,p=h[e].type.toLowerCase(),o=i[p],a=[];for(c=n(h[e].props.children),s=0;s<c.length;s++)v=r.getModel(c[s],f),!t.isNullOrUndefined(o)&&o.attr["e-tag"]&&(y=r.getTagValues(o.attr["e-tag"],c[s].props,f),n.extend(!0,v||{},y)),a.push(v);!t.isNullOrUndefined(o)&&a.length&&r.createAndAddAttr(o.field,a,l)}return l},createAndAddAttr:function(n,i,r){for(var o=n.split("."),s=r||window,u=s,e,h=o.length,f=0;f<h;f++)e=o[f],f+1==h?u[e]=t.isNullOrUndefined(i)?{}:i:t.isNullOrUndefined(u[e])&&(u[e]={}),u=u[e];return s},getModel:function(n,i){var u={},f,e,o=this.getProps(n);for(var r in o)(e=n.props[r],r!="children"&&(r!="id"||i))&&(r.indexOf("-")!==-1&&(r=r.replace("-",".")),f=r,t.createObject(f,e,u));return u},getProps:function(n){return t.isNullOrUndefined(n.key)||(n.props.key=n.key),n.props}},o=function(i,u){var e=i.replace("ej","");f[e]=createReactClass({observableObj:[],widgetName:i,componentWillReceiveProps:function(i){for(var u,r,e=n(ReactDOM.findDOMNode(this)).data(this.widgetName),f=0;f<this.observableObj.length;f++)u=r=this.observableObj[f].value,u.indexOf(".")!==-1&&(r=u.split(".")),r.length==2&&(t.isNullOrUndefined(this.props[r[0]])||this.props[r[0]][r[1]]===i[r[0]][r[1]]||e.option(u,i[r[0]][r[1]],i[r[0]][r[1]]instanceof Array)),this.props[r]!==i[r]&&e.option(u,i[r],i[r]instanceof Array)},componentDidMount:function(){var f={},i=0,e={},o=!1,s,h;if(!t.isNullOrUndefined(u.observables))for(i=0;i<u.observables.length;i++)t.createObject("value",u.observables[i],e),this.observableObj.push(e),e={};if(f=r.getModel(this),!t.isNullOrUndefined(this.props.children)){for(i=0;!t.isNullOrUndefined(u._tags)&&i<u._tags.length;i++)u._tags[i].tag==this.props.children.type&&(o=!0);o&&(s=u["ob.tags"]=r.loadTags(u["ob.tags"],u._tags),h=r.getTagValues(s,this.props,o),n.extend(f,h))}n(ReactDOM.findDOMNode(this))[this.widgetName](f)},componentWillUnmount:function(){n(ReactDOM.findDOMNode(this))[this.widgetName]("destroy")},render:function(){var r=[u.validTags[0],n.extend({id:this.props.id},this.props.ejHtmlAttributes)],f=!1,i;if(!t.isNullOrUndefined(this.props.children)){for(i=0;!t.isNullOrUndefined(u._tags)&&i<u._tags.length;i++)u._tags[i].tag==this.props.children.type&&(f=!0);f||r.push(this.props.children)}return React.createElement.apply(React,r)}})},u=i.registeredWidgets;for(var e in u)o(u[e].name,u[e].proto);window.EJ=f})(window.jQuery,window.Syncfusion,window.Syncfusion.widget)});
