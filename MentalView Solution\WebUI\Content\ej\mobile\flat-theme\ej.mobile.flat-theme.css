@charset "UTF-8";
/*!
*  filename: ej.mobile.flat-theme.css
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws.
*/

@charset "UTF-8";
/*!
*  filename: ej.mobile.flat-theme.css
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws.
*/

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-acc .e-m-acc-item {
  background-color: white;
  color: black;
}
.e-m-flat.e-m-acc,
.e-m-flat.e-m-acc .e-m-acc-item,
.e-m-flat.e-m-acc .e-m-acc-content-wrapper {
  border-bottom-color: #353535;
  border-top-color: #353535;
  border-left-color: #353535;
  border-right-color: #353535;
}
.e-m-flat.e-m-acc .e-m-acc-item.e-m-state-active {
  background-color: #dd7b10;
  color: white;
}
.e-m-flat.e-m-acc .e-m-acc-content-wrapper {
  background-color: white;
  color: black;
}
.e-m-flat.e-m-acc .e-m-acc-item:before {
  color: black;
}
.e-m-flat.e-m-acc .e-m-acc-item.e-m-state-active:before {
  color: white;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-ac-wrapper .e-m-ac {
  border-color: #353535;
  color: black;
}
.e-m-flat.e-m-ac-wrapper .e-m-ac.e-m-focus {
  border-color: #353535;
}
.e-m-flat.e-m-ac-wrapper.e-m-ac-search .e-m-icon-search:before,
.e-m-flat.e-m-ac-wrapper.e-m-ac-search .e-m-icon-close:before {
  color: black;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-btn.e-m-border.e-m-state-default {
  border-color: #353535;
  background-color: #f7941e;
  color: white;
}
.e-m-flat.e-m-btn.e-m-noborder.e-m-state-default {
  border-color: transparent;
  background-color: transparent;
  color: white;
}
.e-m-flat.e-m-btn.e-m-border.e-m-state-active,
.e-m-flat.e-m-btn.e-m-noborder.e-m-state-active {
  border-color: #353535;
  background-color: #dd7b10;
  color: white;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-dialog .e-m-dlg-container {
  background-color: white;
  color: black;
  border-color: #353535;
}
.e-m-flat.e-m-dialog .e-m-dlg-btn.e-m-state-default {
  background-color: white;
  color: black;
  border-color: #353535;
}
.e-m-flat.e-m-dialog .e-m-dlgbtnwrapper {
  border-top-color: #353535;
}
.e-m-flat.e-m-dialog .e-m-dlg-btn.e-m-dlg-rightbtn {
  border-left-color: #353535;
}
.e-m-flat.e-m-dialog .e-m-dlg-hdr,
.e-m-flat.e-m-dialog .e-m-dlg-content {
  color: black;
}
.e-m-flat.e-m-dialog .e-m-dlg-btn.e-m-state-active {
  background-color: #dd7b10;
  color: white;
  border-color: #353535;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-dropdownlist {
  color: black;
  border-color: #353535;
}
.e-m-flat.e-m-dropdownlist.e-m-focus {
  border-color: #dd7b10;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-input-checkbox,
.e-m-flat.e-m-input-radiobutton {
  border-color: #353535;
}
.e-m-flat.e-m-input-checkbox.e-m-state-active,
.e-m-flat.e-m-input-radiobutton.e-m-state-active:after {
  background-color: #dd7b10;
}
.e-m-flat.e-m-input-checkbox.e-m-state-active:before {
  color: white;
}
.e-m-flat.e-m-input-radiobutton.e-m-state-glow:after {
  box-shadow: 0 0 1px 13px rgba(221, 123, 16, 0.1);
}
.e-m-flat.e-m-input-checkbox.e-m-state-glow:after {
  box-shadow: -2px -1px 0px 13px #dd7b10;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-navbar {
  background-color: #f7941e;
  border-color: #353535;
}
.e-m-flat.e-m-navbar .e-m-navbar-text {
  color: white;
}
.e-m-flat.e-m-navbar .e-m-navbar-icon,
.e-m-flat.e-m-navbar .e-m-navbar-ellipsis {
  color: white;
}
.e-m-flat.e-m-navbar .e-m-navbar-icon.e-m-state-active,
.e-m-flat.e-m-navbar .e-m-navbar-ellipsis.e-m-state-active {
  color: white;
  background-color: #dd7b10;
}
.e-m-flat.e-m-navbar .e-m-navbar-icon.e-m-nav-badge::after {
  background-color: white;
  color: #f7941e;
}
.e-m-flat.e-m-navbar .e-m-navbar-icon.e-m-state-active.e-m-nav-badge::after {
  background-color: white;
  color: #dd7b10;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat .e-m-grid-header .e-m-grid-fbardiv {
  background: #f2f2f2;
}
.e-m-flat .e-m-caption-row,
.e-m-flat .e-m-grid-fbardiv,
.e-m-flat .e-m-grid-fbardiv input {
  color: #4d4d4d;
  background: #f2f2f2;
}
.e-m-flat .e-m-grid-header,
.e-m-flat .e-m-grid-pwrapper {
  border-width: 1px 0;
  border-color: #959595;
  border-style: solid;
}
.e-m-flat .e-m-caption-row,
.e-m-flat .e-m-grid-fbardiv,
.e-m-flat .e-m-grid-content .e-m-grid-table,
.e-m-flat .e-m-grid-content,
.e-m-flat.e-m-grid-core {
  border-color: #aaaaaa;
}
.e-m-flat .e-m-grid-header .e-m-state-active {
  background: #dd7b10;
  color: white;
}
.e-m-flat .e-m-grid-header.e-m-state-default,
.e-m-flat .e-m-grid-header.e-m-state-default .e-m-grid-inrhdr {
  background: #f2f2f2;
  color: #4d4d4d;
}
.e-m-flat .e-m-grid-rowcell.e-m-state-active,
.e-m-flat.e-m-pagertype-scrollable .e-m-grid-ptxt .e-m-state-active.e-m-page-num {
  background: #dd7b10;
  color: white;
}
.e-m-flat .e-m-grid-content .e-m-grid-table tr {
  color: #231f20;
  background: #f2f2f2;
}
.e-m-flat.e-m-pagertype-scrollable .e-m-grid-ptxt .e-m-page-num,
.e-m-flat .e-m-grid-ptxt {
  color: #231f20;
}
.e-m-flat .e-m-grid-header .e-m-grid-fbardiv input {
  border-color: #aaaaaa;
  color: #4d4d4d;
}
.e-m-flat .e-m-grid-table tr:nth-child(2n),
.e-m-flat.e-m-pagertype-scrollable .e-m-grid-ptxt .e-m-page-num {
  background: #dbdbdb;
}
.e-m-flat .e-m-grid-pwrapper {
  background: #f2f2f2;
}
.e-m-flat .e-m-grid-rowcell,
.e-m-flat .e-m-grid-emptycell,
.e-m-flat .e-m-grid-hdrcell,
.e-m-flat.e-m-pagertype-scrollable .e-m-grid-ptxt .e-m-page-num {
  border-color: #aaaaaa;
}
.e-m-flat.e-m-pagertype-scrollable .e-m-grid-ptxt .e-m-page-num,
.e-m-flat .e-m-grid-pwrapper .e-m-fontimage {
  box-shadow: 0 0 0 #5d5d5d;
  background-color: #cccccc;
}
.e-m-flat.e-m-grid-core .e-m-fontimage.e-m-state-active {
  background-color: #f48b22;
}
.e-m-flat .e-m-grid-inrpgr .e-m-grid-prev,
.e-m-flat .e-m-grid-inrpgr .e-m-grid-next,
.e-m-flat .e-m-grid-inrpgr .e-m-grid-first,
.e-m-flat .e-m-grid-inrpgr .e-m-grid-last,
.e-m-flat.e-m-grid-core .e-m-fontimage {
  color: black;
}
.e-m-flat .e-m-grid-inrpgr .e-m-grid-hprev,
.e-m-flat .e-m-grid-inrpgr .e-m-grid-hnext,
.e-m-flat .e-m-grid-inrpgr .e-m-grid-hfirst,
.e-m-flat .e-m-grid-inrpgr .e-m-grid-hlast,
.e-m-flat.e-m-grid-core .e-m-state-active .e-m-fontimage,
.e-m-flat.e-m-grid-core .e-m-state-active.e-m-fontimage {
  color: white;
}
.e-m-flat .e-m-grid-inrpgr .e-m-grid-dprev,
.e-m-flat .e-m-grid-inrpgr .e-m-grid-dnext,
.e-m-flat .e-m-grid-inrpgr .e-m-grid-dfirst,
.e-m-flat .e-m-grid-inrpgr .e-m-grid-dlast {
  color: darkgray;
}
.e-m-flat.e-m-grid-colnavcontainer .e-m-btnwrapper {
  left: 0;
  top: 0;
}
.e-m-flat.e-m-grid-colnavcontainer .e-m-btnwrapper:last-child {
  left: 0;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-lv .e-m-lv-group {
  background-color: #f7941e;
}
.e-m-flat.e-m-lv .e-m-lv-content {
  box-shadow: 0px 1px 0 rgba(0, 0, 0, 0.2);
  color: black;
}
.e-m-flat.e-m-lv .e-m-lv-image {
  box-shadow: 0px 1px 0 rgba(0, 0, 0, 0.2);
}
.e-m-flat.e-m-lv .e-m-lv-item {
  background-color: white;
}
.e-m-flat.e-m-lv .e-m-lv-active {
  background-color: #dd7b10;
}
.e-m-flat.e-m-lv .e-m-lv-active .e-m-lv-content {
  color: white;
}
.e-m-flat.e-m-lv .e-m-lv-slideitem .e-m-lv-delete {
  color: black;
  background-color: white;
}
.e-m-flat.e-m-lv .e-m-lv-content::after {
  background-color: white;
  color: black;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-menu {
  border-color: #353535;
  background-color: white;
}
.e-m-flat.e-m-menu.e-m-menu-actionsheet {
  border-color: transparent;
  background-color: transparent;
}
.e-m-flat.e-m-menu.e-m-menu-actionsheet .e-m-menuitem.e-m-state-default,
.e-m-flat.e-m-menu.e-m-menu-actionsheet .e-m-menu-btn.e-m-menu-cancelbtn,
.e-m-flat.e-m-menu.e-m-menu-actionsheet .e-m-menuscrollwrapper {
  background-color: white;
}
.e-m-flat.e-m-menu .e-m-menuitem.e-m-state-default {
  border-bottom-color: #353535;
}
.e-m-flat.e-m-menu.e-m-menu-popover div.e-m-icon-arrow:before {
  color: white;
}
.e-m-flat.e-m-menu .e-m-menu-title,
.e-m-flat.e-m-menu.e-m-menu-actionsheet .e-m-menu-title {
  color: black;
  background-color: white;
}
.e-m-flat.e-m-menu.e-m-menu-popover {
  background-color: white;
}
.e-m-flat.e-m-menu.e-m-menu-actionsheet .e-m-menu-btn,
.e-m-flat.e-m-menu .e-m-menu-btn {
  color: black;
}
.e-m-flat.e-m-menu .e-m-menuitem.e-m-state-active,
.e-m-flat .e-m-menu-btn.e-m-menu-cancelbtn.e-m-state-active,
.e-m-flat.e-m-menu.e-m-menu-actionsheet .e-m-menuitem.e-m-state-active,
.e-m-flat.e-m-menu.e-m-menu-actionsheet .e-m-menu-btn.e-m-menu-cancelbtn.e-m-state-active {
  background-color: #dd7b10;
}
.e-m-flat.e-m-menu .e-m-menuitem.e-m-state-active .e-m-menu-btn,
.e-m-flat.e-m-menu .e-m-menu-btn.e-m-menu-cancelbtn.e-m-state-active {
  color: white;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-sbw .e-m-sb {
  background-color: #353535;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat .e-m-pane.e-m-sp-right,
.e-m-flat .e-m-pane.e-m-sp-left {
  border-color: #353535;
}
.e-m-flat.e-m-splitpane .e-m-pane {
  background-color: white;
  color: black;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-radialmenu.e-m-flat .e-m-radialdefault {
  fill: #f17e2d;
}
.e-m-radialmenu.e-m-flat .e-m-badgecircle {
  fill: rgba(241, 126, 45, 0.9);
}
.e-m-radialmenu.e-m-flat .e-m-badgetext {
  fill: white;
}
.e-m-radialmenu.e-m-flat .e-m-textcolor {
  fill: #3f3f3f;
}
.e-m-radialmenu.e-m-flat .e-m-childdefault {
  fill: #f17e2d;
}
.e-m-radialmenu.e-m-flat .e-m-default,
.e-m-radialmenu.e-m-flat .e-m-outerdefault {
  fill: #fce6c5;
}
.e-m-radialmenu.e-m-flat .e-m-active {
  fill: rgba(241, 126, 45, 0.9);
}
.e-m-radialmenu.e-m-flat .e-m-arcbgcolor,
.e-m-radialmenu.e-m-flat .e-m-itembgcolor,
.e-m-radialmenu.e-m-flat .e-m-circlebgcolor {
  fill: white;
  stroke: white;
}
.e-m-radialmenu.e-m-flat .e-m-radial {
  border: 1px solid #999999;
  background-color: white;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-rotator .e-m-pager .e-m-sv-pager.e-m-active {
  background-color: #dd7b10;
}
.e-m-flat.e-m-rotator .e-m-pager .e-m-sv-pager {
  background-color: white;
  border-color: #353535;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-slider.e-m-flat.e-m-horizontal .e-m-slider-outer {
  background-color: #b3b3b3;
}
.e-m-slider.e-m-flat.e-m-vertical .e-m-slider-outer {
  background-color: #b3b3b3;
}
.e-m-slider.e-m-flat.e-m-horizontal .e-m-slider-inner {
  background-color: #f48b22;
}
.e-m-slider.e-m-flat.e-m-vertical .e-m-slider-inner {
  background-color: #f48b22;
}
.e-m-slider.e-m-flat .e-m-slider-handleout,
.e-m-slider.e-m-flat .e-m-slider-handleout.e-m-state-active {
  border-radius: 4px;
  background-color: #4d4d4d;
}
.e-m-slider.e-m-flat .e-m-slider-handlein {
  display: none;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-grpbtn {
  color: white;
  background-color: #f7941e;
}
.e-m-flat.e-m-grpbtn .e-m-btn {
  border-color: #353535;
}
.e-m-flat.e-m-grpbtn .e-m-btn.e-m-active {
  color: white;
  background-color: #dd7b10;
  border-color: #353535;
}
.e-m-flat.e-m-grpbtn .e-m-btn:first-child {
  border-left-color: #353535;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-tbutton {
  border-color: #353535;
  background-color: white;
}
.e-m-flat.e-m-tbutton .e-m-tslider {
  background-color: #353535;
  border-color: #353535;
}
.e-m-flat.e-m-tbutton.e-m-state-active {
  background-color: rgba(221, 123, 16, 0.2);
}
.e-m-flat.e-m-tbutton .e-m-tslider.e-m-state-active {
  background-color: #dd7b10;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-tab.e-m-flat .e-m-tab-badge.e-m-state-active::after {
  background-color: white;
  color: black;
}
.e-m-tab.e-m-flat .e-m-tab-badge::after {
  background-color: #dd7b10;
  color: black;
}
.e-m-tab.e-m-flat.e-m-tab-bottom {
  border-top: 2px solid #353535;
}
.e-m-tab.e-m-flat.e-m-tab-top {
  border-bottom: 2px solid #353535;
}
.e-m-tab.e-m-flat.e-m-tab-top .e-m-tab-item.e-m-state-active {
  border-bottom: 2px solid #353535;
}
.e-m-tab.e-m-flat.e-m-tab-bottom .e-m-tab-item.e-m-state-active {
  border-top: 2px solid #353535;
}
.e-m-tab.e-m-flat .e-m-tab-item {
  background-color: #f7941e;
  color: white;
}
.e-m-tab.e-m-flat .e-m-state-active {
  color: white;
  background-color: #dd7b10;
}
.e-m-tab.e-m-flat {
  background-color: #f7941e;
  color: white;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-timepicker {
  color: black;
  border-color: #353535;
}
.e-m-flat.e-m-tp-dialog.e-m-dialog .e-m-tp-header {
  background-color: #f7941e;
  border-color: #353535;
}
.e-m-flat.e-m-tp-dialog.e-m-dialog .e-m-tp-header .e-m-tp-time {
  color: white;
}
.e-m-flat.e-m-tp-dialog.e-m-dialog .e-m-tp-header .e-m-tp-mer .mer {
  color: #353535;
}
.e-m-flat.e-m-tp-dialog.e-m-dialog .e-m-tp-header .e-m-tp-mer .mer.e-m-state-active {
  color: white;
}
.e-m-flat.e-m-tp-dialog .e-m-tpwrap .e-m-text.e-m-text-active {
  color: white;
  background-color: #dd7b10;
}
.e-m-flat.e-m-tp-dialog.e-m-dialog .e-m-dlgbtnwrapper {
  border-top-color: #353535;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-editor.e-m-input-wrapper,
.e-m-flat.e-m-editor.e-m-input-wrapper .e-m-textbox {
  border-color: #353535;
}
.e-m-flat.e-m-editor.e-m-input-wrapper,
.e-m-flat.e-m-editor.e-m-input-wrapper .e-m-textbox,
.e-m-flat.e-m-editor.e-m-input-wrapper span {
  background-color: white;
}
.e-m-flat.e-m-editor.e-m-input-wrapper,
.e-m-flat.e-m-editor.e-m-input-wrapper .e-m-textbox {
  color: black;
}
.e-m-flat.e-m-editor.e-m-input-wrapper:focus,
.e-m-flat.e-m-editor.e-m-input-wrapper.focus {
  border-color: #353535;
}
.e-m-flat.e-m-editor.e-m-input-wrapper span[class^='e-m-icon-']:active,
.e-m-flat.e-m-editor.e-m-input-wrapper span[class*='e-m-icon-']:active {
  color: #dd7b10;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-flat.e-m-datepickerwrapper .e-m-datepicker,
.e-m-flat.e-m-dp .e-m-dp-header {
  color: black;
  border-color: #353535;
}
.e-m-flat.e-m-dp .e-m-dpinner .e-m-flat-overlay {
  border-color: #353535;
}
.e-m-flat.e-m-dp .e-m-dpinner .e-m-dp-overlay {
  border-color: #f7941e;
}
.e-m-flat.e-m-dp .e-m-dpinner .e-m-text {
  color: #353535;
}
.e-m-flat.e-m-dp .e-m-dpinner .e-m-text.e-m-text-active {
  color: white;
  background-color: #dd7b10;
  border-color: #dd7b10;
}
.e-m-flat.e-m-dp .e-m-dpinner .e-m-text.e-m-text-disabled {
  color: none;
}
.e-m-flat.e-m-dp,
.e-m-flat.e-m-dp-tb {
  background-color: #f2f2f2;
}
.e-m-flat.e-m-dp-tb.e-m-navbar .e-m-navbar-icon {
  color: black;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-tile.e-m-flat .e-m-tile-selected:after {
  border-color: #dd7b10;
  background-color: transparent;
  border-left-color: transparent;
}
.e-m-tile.e-m-flat .e-m-tile-selected {
  border-color: #353535;
}
.e-m-tile.e-m-flat .e-m-tile-selected::before {
  background-color: transparent;
  border-left-color: white;
  border-bottom-color: white;
}
.e-m-flat.e-m-tile.e-m-tile-badge.e-m-tile-small.e-m-caption-outer.e-m-badge-position-bottomright::after,
.e-m-flat.e-m-tile.e-m-tile-badge.e-m-tile-small.e-m-caption-outer.e-m-badge-position-topright::after {
  background-color: #dd7b10;
}
.e-m-flat.e-m-tile-caption-text.e-m-caption-innerbottom::before,
.e-m-flat.e-m-tile-caption-text.e-m-caption-innertop::before {
  background-color: transparent;
}
.e-m-flat.e-m-tile.e-m-state-active .e-m-tile-overlay {
  background-color: #dd7b10;
}
.e-m-flat.e-m-tile.e-m-tile-badge::after,
.e-m-flat.e-m-tile-caption::before {
  color: white;
}
.e-m-flat.e-m-tile-caption-text.e-m-caption-outer::before {
  color: black;
}

/* control */
/*Datepicker*/
/*Timepicker*/
/* Radial Menu */
/*Slider Control Start*/
/*Slider Control End*/
/*Tile*/
/*Radial Slider start*/
/*Radial Slider end*/
/*Grid*/
/*Grid End*/

.e-m-radialslider.e-m-flat .e-m-ticks-text,
.e-m-radialslider.e-m-flat .e-m-dynamic-text {
  fill: black;
}
.e-m-radialslider.e-m-flat .e-m-rs-circle-default {
  fill: #b3b3b3;
  stroke: #b3b3b3;
}
.e-m-radialslider.e-m-flat .e-m-rs-fill-circle {
  fill: #f48b22;
  stroke: #f48b22;
}
.e-m-radialslider.e-m-flat .e-m-rs-marker {
  fill: #4d4d4d;
  stroke: #4d4d4d;
}
