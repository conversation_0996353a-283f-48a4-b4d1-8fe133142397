/*!
*  filename: ej.core.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["jquery"],n):n()})(function(){window.ej=window.Syncfusion=window.Syncfusion||{},function(n,t,i){"use strict";var e,u,f,o;t.version="20.3.0.59";t.consts={NamespaceJoin:"-"};t.TextAlign={Center:"center",Justify:"justify",Left:"left",Right:"right"};t.Orientation={Horizontal:"horizontal",Vertical:"vertical"};t.serverTimezoneOffset=0;t.parseDateInUTC=!1;t.persistStateVersion=null;t.locales=t.locales||[];Object.prototype.hasOwnProperty||(Object.prototype.hasOwnProperty=function(n,t){return n[t]!==i});Date.prototype.toISOString||function(){function n(n){var t=String(n);return t.length===1&&(t="0"+t),t}Date.prototype.toISOString=function(){return this.getUTCFullYear()+"-"+n(this.getUTCMonth()+1)+"-"+n(this.getUTCDate())+"T"+n(this.getUTCHours())+":"+n(this.getUTCMinutes())+":"+n(this.getUTCSeconds())+"."+String((this.getUTCMilliseconds()/1e3).toFixed(3)).slice(2,5)+"Z"}}();String.format=function(){for(var t=arguments[0],n=0;n<arguments.length-1;n++)t=t.replace(new RegExp("\\{"+n+"\\}","gm"),arguments[n+1]);return t.replace(/\{[0-9]\}/g,"")};jQuery.uaMatch=function(n){n=n.toLowerCase();var t=/(chrome)[ \/]([\w.]+)/.exec(n)||/(webkit)[ \/]([\w.]+)/.exec(n)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(n)||/(msie) ([\w.]+)/.exec(n)||n.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(n)||[];return{browser:t[1]||"",version:t[2]||"0"}};t.defineClass=function(n,r,u,f){if(!n||!u)return i;for(var o=n.split("."),s=window,e=0;e<o.length-1;e++)t.isNullOrUndefined(s[o[e]])&&(s[o[e]]={}),s=s[o[e]];return(f||t.isNullOrUndefined(s[o[e]]))&&(r=typeof r=="function"?r:function(){},s[o[e]]=r,s[o[e]].prototype=u),s[o[e]]};t.util={getNameSpace:function(n){var i=n.toLowerCase().split(".");return i[0]==="ej"&&(i[0]="e"),i.join(t.consts.NamespaceJoin)},getObject:function(n,r){var u,e,f;if(!r||!n)return i;for(typeof n!="string"&&(n=JSON.stringify(n)),u=r,e=n.split("."),f=0;f<e.length;f++){if(t.util.isNullOrUndefined(u))break;u=u[e[f]]}return u},createObject:function(n,i,r){for(var o=n.split("."),s=r||window,u=s,e,h=o.length,f=0;f<h;f++)e=o[f],f+1==h?u[e]=i:t.isNullOrUndefined(u[e])&&(u[e]={}),u=u[e];return s},isNullOrUndefined:function(n){return n===i||n===null},exportAll:function(i,r){var h=[],o,u=[],c,l,a,f,v,p={action:i,method:"post","data-ajax":"false"},s=t.buildTag("form","",null,p),e,y;if(r.length!=0){for(e=0;e<r.length;e++)c=e,l=n("#"+r[e]),a=n("#"+r[e]).data(),o=a.ejWidgets,f=n(l).data(o[0]),u.push({id:f._id,locale:f.model.locale}),t.isNullOrUndefined(f)||(v=f._getExportModel(f.model),h.push({name:o[0],type:"hidden",value:f.stringify(v)}),y=t.buildTag("input","",null,h[c]),s.append(y));n("body").append(s);s.submit();setTimeout(function(){var r,f,i;if(u.length)for(i=0;i<u.length;i++)t.isNullOrUndefined(u[i].locale)||(r=n("#"+u[i].id).data(),o=r.ejWidgets,f=n("#"+u[i].id).data(o[0]),f.model.locale=u[i].locale)},0);s.remove()}return!0},print:function(i,r){var f=t.buildTag("div"),o=i.clone(),r,e,u;f.append(o);r||(r=window.open("","print","height=452,width=1024,tabbar=no"));r.document.write("<!DOCTYPE html>");e=n("head").find("link").add("style");t.browserInfo().name==="msie"?(u="",e.each(function(t,i){i.tagName=="LINK"&&n(i).attr("href",i.href);u+=i.outerHTML}),r.document.write("<html><head><\/head><body>"+u+f[0].innerHTML+"<\/body><\/html>")):(u="",r.document.write("<html><head>"),e.each(function(t,i){i.tagName=="LINK"&&n(i).attr("href",i.href);u+=i.outerHTML}),r.document.writeln(u+"<\/head><body>"),r.document.writeln(f[0].innerHTML+"<\/body><\/html>"));r.document.close();r.focus();setTimeout(function(){t.isNullOrUndefined(r.window)||(r.print(),setTimeout(function(){r.close()},1e3))},1e3)},ieClearRemover:function(t){var i=n(t).height();t.style.paddingTop=parseFloat(i/2)+"px";t.style.paddingBottom=parseFloat(i/2)+"px";t.style.height="1px";t.style.lineHeight="1px"},sendAjaxRequest:function(t){n.ajax({type:t.type,cache:t.cache,url:t.url,dataType:t.dataType,data:t.data,contentType:t.contentType,async:t.async,success:t.successHandler,error:t.errorHandler,beforeSend:t.beforeSendHandler,complete:t.completeHandler})},buildTag:function(t,r,u,f){var s=/^[a-z]*[0-9a-z]+/ig.exec(t)[0],e=/#([_a-z0-9-&@\/\\,+()$~%:*?<>{}\[\]]+\S)/ig.exec(t),o;return e=e?e[e.length-1].replace(/[&@\/\\,+()$~%.:*?<>{}\[\]]/g,""):i,o=/\.([a-z]+[-_0-9a-z ]+)/ig.exec(t),o=o?o[o.length-1]:i,n(document.createElement(s)).attr(e?{id:e}:{}).addClass(o||"").css(u||{}).attr(f||{}).html(r||"")},_preventDefaultException:function(n,t){if(n)for(var i in t)if(t[i].test(n[i]))return!0;return!1},getMaxZindex:function(){var t=1;return t=Math.max.apply(null,n.map(n("body *"),function(t){if(n(t).css("position")=="absolute"||n(t).css("position")=="fixed")return parseInt(n(t).css("z-index"))||1})),(t==i||t==null)&&(t=1),t},blockDefaultActions:function(n){n.cancelBubble=!0;n.returnValue=!1;n.preventDefault&&n.preventDefault();n.stopPropagation&&n.stopPropagation()},getDimension:function(t,i){var e,u=n(t).parents().andSelf().filter(":hidden"),r,f;return u&&(r={visibility:"hidden",display:"block"},f=[],u.each(function(){var t={};for(var n in r)t[n]=this.style[n],this.style[n]=r[n];f.push(t)}),e=/(outer)/g.test(i)?n(t)[i](!0):n(t)[i](),u.each(function(n){var i=f[n];for(var t in r)this.style[t]=i[t]})),e},transitionEndEvent:function(){return{"":"transitionend",webkit:"webkitTransitionEnd",Moz:"transitionend",O:"otransitionend",ms:"MSTransitionEnd"}[t.userAgent()]},animationEndEvent:function(){return{"":"animationend",webkit:"webkitAnimationEnd",Moz:"animationend",O:"webkitAnimationEnd",ms:"animationend"}[t.userAgent()]},startEvent:function(){return t.isTouchDevice()||n.support.hasPointer?"touchstart":"mousedown"},endEvent:function(){return t.isTouchDevice()||n.support.hasPointer?"touchend":"mouseup"},moveEvent:function(){return t.isTouchDevice()||n.support.hasPointer?n.support.hasPointer&&!t.isMobile()?"ejtouchmove":"touchmove":"mousemove"},cancelEvent:function(){return t.isTouchDevice()||n.support.hasPointer?"touchcancel":"mousecancel"},tapEvent:function(){return t.isTouchDevice()||n.support.hasPointer?"tap":"click"},tapHoldEvent:function(){return t.isTouchDevice()||n.support.hasPointer?"taphold":"click"},isDevice:function(){return t.getBooleanVal(n("head"),"data-ej-forceset",!1)?t.getBooleanVal(n("head"),"data-ej-device",this._device()):this._device()},isPortrait:function(){var n=document.documentElement;return n&&n.clientWidth/n.clientHeight<1.1},isLowerResolution:function(){return window.innerWidth<=640&&t.isPortrait()&&t.isDevice()||window.innerWidth<=800&&!t.isDevice()||window.innerWidth<=800&&!t.isPortrait()&&t.isWindows()&&t.isDevice()||t.isMobile()},isIOSWebView:function(){return/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(navigator.userAgent)},isAndroidWebView:function(){return!(typeof Android=="undefined")},isWindowsWebView:function(){return location.href.indexOf("x-wmapp")!=-1},_device:function(){return/Android|BlackBerry|iPhone|iPad|iPod|IEMobile|kindle|windows\sce|palm|smartphone|iemobile|mobile|pad|xoom|sch-i800|playbook/i.test(navigator.userAgent.toLowerCase())},isMobile:function(){return/iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(navigator.userAgent.toLowerCase())&&/mobile/i.test(navigator.userAgent.toLowerCase())||t.getBooleanVal(n("head"),"data-ej-mobile",!1)===!0},isTablet:function(){return/ipad|xoom|sch-i800|playbook|tablet|kindle/i.test(navigator.userAgent.toLowerCase())||t.getBooleanVal(n("head"),"data-ej-tablet",!1)===!0||!t.isMobile()&&t.isDevice()},isTouchDevice:function(){return("ontouchstart"in window||window.navigator.msPointerEnabled&&t.isMobile())&&this.isDevice()},getClearString:function(t){return n.trim(t.replace(/\s+/g," ").replace(/(\r\n|\n|\r)/gm,"").replace(new RegExp(">[\n\t ]+<","g"),"><"))},getBooleanVal:function(t,i,r){var u=n(t).attr(i);return u!=null?u.toLowerCase()=="true":r},_getSkewClass:function(n,t,i){var h=n.width(),c=n.height(),f=n.offset().left,e=n.offset().left+h,o=n.offset().top,s=n.offset().top+c,r=h*.3,u=c*.3;return t<f+r&&i<o+u?"e-m-skew-topleft":t>e-r&&i<o+u?"e-m-skew-topright":t>e-r&&i>s-u?"e-m-skew-bottomright":t<f+r&&i>s-u?"e-m-skew-bottomleft":t>f+r&&i<o+u&&t<e-r?"e-m-skew-top":t<f+r?"e-m-skew-left":t>e-r?"e-m-skew-right":i>s-u?"e-m-skew-bottom":"e-m-skew-center"},_removeSkewClass:function(t){n(t).removeClass("e-m-skew-top e-m-skew-bottom e-m-skew-left e-m-skew-right e-m-skew-topleft e-m-skew-topright e-m-skew-bottomleft e-m-skew-bottomright e-m-skew-center e-skew-top e-skew-bottom e-skew-left e-skew-right e-skew-topleft e-skew-topright e-skew-bottomleft e-skew-bottomright e-skew-center")},_getObjectKeys:function(n){var t,i=[];if(n=Object.prototype.toString.call(n)===Object.prototype.toString()?n:{},!Object.keys){for(t in n)n.hasOwnProperty(t)&&i.push(t);return i}if(Object.keys)return Object.keys(n)},_touchStartPoints:function(n,t){if(n){var i=n.touches?n.touches[0]:n;t._distX=0;t._distY=0;t._moved=!1;t._pointX=i.pageX;t._pointY=i.pageY}},_isTouchMoved:function(n,t){if(n){var i=n.touches?n.touches[0]:n,f=i.pageX-t._pointX,e=i.pageY-t._pointY,o=Date.now(),r,u;return t._pointX=i.pageX,t._pointY=i.pageY,t._distX+=f,t._distY+=e,r=Math.abs(t._distX),u=Math.abs(t._distY),!(r<5&&u<5)}},listenEvents:function(n,i,r,u,f,e){for(var o=0;o<n.length;o++)t.listenTouchEvent(n[o],i[o],r[o],u,f,e)},listenTouchEvent:function(i,r,u,f,e,o){for(var s,h=f?"removeEventListener":"addEventListener",a=f?"off":"on",l=n(i),c=0;c<l.length;c++){s=l[c];switch(r){case"touchstart":t._bindEvent(s,h,r,u,"mousedown","MSPointerDown","pointerdown",o);break;case"touchmove":t._bindEvent(s,h,r,u,"mousemove","MSPointerMove","pointermove",o);break;case"touchend":t._bindEvent(s,h,r,u,"mouseup","MSPointerUp","pointerup",o);break;case"touchcancel":t._bindEvent(s,h,r,u,"mousecancel","MSPointerCancel","pointercancel",o);break;case"tap":case"taphold":case"ejtouchmove":case"click":n(s)[a](r,u);break;default:t.browserInfo().name=="msie"&&t.browserInfo().version<9?e._on(n(s),r,u):s[h](r,u,!0)}}},_bindEvent:function(t,i,r,u,f,e,o){n.support.hasPointer?t[i](window.navigator.pointerEnabled?o:e,u,!0):t[i](r,u,!0)},_browser:function(){return/webkit/i.test(navigator.appVersion)?"webkit":/firefox/i.test(navigator.userAgent)?"Moz":/trident/i.test(navigator.userAgent)?"ms":"opera"in window?"O":""},styles:document.createElement("div").style,userAgent:function(){for(var i="webkitT,t,MozT,msT,OT".split(","),r,n=0,u=i.length;n<u;n++)if(r=i[n]+"ransform",r in t.styles)return i[n].substr(0,i[n].length-1);return!1},addPrefix:function(n){return t.userAgent()===""?n:(n=n.charAt(0).toUpperCase()+n.substr(1),t.userAgent()+n)},destroyWidgets:function(t){var i=n(t).find("[data-role *= ejm]");i.each(function(t,i){var r=n(i),u=r.data("ejWidgets");u&&r[u]("destroy")})},getAttrVal:function(t,i,r){var u=n(t).attr(i);return u!=null?u:r},getOffset:function(t){var i={},u=t.offset()||{left:0,top:0},r;return n.extend(!0,i,u),n("body").css("position")!="static"&&(r=n("body").offset(),i.left-=r.left,i.top-=r.top),i},getZindexPartial:function(i,r){var e,f,u;if(!t.isNullOrUndefined(i)&&i.length>0)return e=i.parents(),f=n("body").children(),!t.isNullOrUndefined(i)&&i.length>0&&f.splice(f.index(r),1),n(f).each(function(n,t){e.push(t)}),u=Math.max.apply(u,n.map(e,function(t){if(n(t).css("position")!="static")return parseInt(n(t).css("z-index"))||1})),!u||u<1e4?u=1e4:u+=1,u},isValidAttr:function(t,i){var t=n(t)[0],r;return typeof t[i]!="undefined"?!0:(r=!1,n.each(t,function(n){if(n.toLowerCase()==i.toLowerCase())return r=!0,!1}),r)}};n.extend(t,t.util);t.widgetBase={droppables:{"default":[]},resizables:{"default":[]},_renderEjTemplate:function(i,r,u,f,e){var o=null;if((typeof i=="object"||i.startsWith("#")||i.startsWith("."))&&(o=n(i).attr("type")),o){if(o=o.toLowerCase(),t.template[o])return t.template[o](this,i,r,u,f)}else if(!t.isNullOrUndefined(e))return t.template["text/x-"+e](this,i,r,u,f);return t.template.render(this,i,r,u,f)},destroy:function(){var u,r,f,i;if(!this._trigger("destroy")){this.model.enablePersistence&&(this.persistState(),n(window).off("unload",this._persistHandler));try{this._destroy()}catch(e){}for(u=this.element.data("ejWidgets")||[],i=0;i<u.length;i++)u[i]==this.pluginName&&u.splice(i,1);for(u.length||this.element.removeData("ejWidgets");this._events;){if(r=this._events.pop(),f=[],!r)break;for(i=0;i<r[1].length;i++)n.isPlainObject(r[1][i])||f.push(r[1][i]);n.fn.off.apply(r[0],f)}this._events=null;this.element.removeClass(t.util.getNameSpace(this.sfType)).removeClass("e-js").removeData(this.pluginName);this.element=null;this.model=null}},_on:function(i){this._events||(this._events=[]);for(var r=[].splice.call(arguments,1,arguments.length-1),u={},f=r.length;u&&typeof u!="function";)u=r[--f];return r[f]=t.proxy(r[f],this),this._events.push([i,r,u,r[f]]),n.fn.on.apply(i,r),this},_off:function(t,i,r,u){var e=this._events,s,h,o,f,c;if(!e||!e.length)return this;for(typeof r=="function"&&(s=u,u=r,r=s),h=i.match(/\S+/g)||[""],o=0;o<e.length;o++)if(f=e[o],c=f[0].length&&(!u||f[2]===u)&&(f[1][0]===i||h[0])&&(!r||f[1][1]===r)&&n.inArray(t[0],f[0])>-1,c){n.fn.off.apply(t,u?[i,r,f[3]]:[i,r]);e.splice(o,1);break}return this},_trigger:function(i,r){var f=null,e,u,o={},s;return(n.extend(o,r),i in this.model&&(f=this.model[i]),f&&(typeof f=="string"&&(f=t.util.getObject(f,window)),n.isFunction(f)&&(u=t.event(i,this.model,r),e=f.call(this,u),r&&n.extend(r,u),u.cancel||!t.isNullOrUndefined(e))))?e===!1||u.cancel:(s=Boolean(r),r=r||{},r.originalEventType=i,r.type=this.pluginName+i,u=n.Event(r.type,t.event(r.type,this.model,r)),this.element&&this.element.trigger(u),s&&n.extend(r,u),t.isOnWebForms&&u.cancel==!1&&this.model.serverEvents&&this.model.serverEvents.length&&t.raiseWebFormsServerEvents(i,r,o),u.cancel)},setModel:function(t,i){var r,f,o,u;if(!this._trigger("modelChange",{changes:t})){for(r in t){if(!i){if(this.model[r]===t[r]){delete t[r];continue}if(n.isPlainObject(t[r])&&(e(this.model[r],t[r]),n.isEmptyObject(t[r]))){delete t[r];continue}}if(this.dataTypes&&(f=this._isValidModelValue(r,this.dataTypes,t),f!==!0))throw"setModel - Invalid input for property :"+r+" - "+f;this.model.notifyOnEachPropertyChanges&&this.model[r]!==t[r]&&(o={oldValue:this.model[r],newValue:t[r]},t[r]=this._trigger(r+"Change",o)?this.model[r]:o.newValue)}n.isEmptyObject(t)||(this._setFirst?(u=t.dataSource,u&&delete t.dataSource,n.extend(!0,this.model,t),u&&(this.model.dataSource=u instanceof Array?u.slice():u,t.dataSource=this.model.dataSource),this._setModel&&this._setModel(t)):this._setModel&&this._setModel(t)===!1||n.extend(!0,this.model,t),"enablePersistence"in t&&this._setState(t.enablePersistence))}},option:function(r,u,f){if(!r)return this.model;if(n.isPlainObject(r))return this.setModel(r,f);if(typeof r=="string"){r=r.replace(/^model\./,"");var e=t.getObject(r,this.model);if(u===i&&!f)return e;if(r==="enablePersistence")return this._setState(u);if(f&&u===t.extensions.modelGUID)return this._setModel(t.createObject(r,t.getObject(r,this.model),{}));if(f||t.getObject(r,this.model)!==u)return this.setModel(t.createObject(r,u,{}),f)}return i},_isValidModelValue:function(n,t,i){var r=t[n],u=i[n],f,e,o;if(!r)return!0;if(typeof r=="string"){if(r=="enum"&&(i[n]=u?u.toString().toLowerCase():u,r="string"),r==="array"){if(Object.prototype.toString.call(u)==="[object Array]")return!0}else if(r==="data"||r==="parent"||typeof u===r)return!0;return"Expected type - "+r}if(u instanceof Array){for(e=0;e<u.length;e++)if(f=this._isValidModelValue(n,t,u[e]),f!==!0)return" ["+e+"] - "+f;return!0}for(o in u)if(f=this._isValidModelValue(o,r,u),f!==!0)return o+" : "+f;return!0},_returnFn:function(n,t){t.indexOf(".")!=-1?this._returnFn(n[t.split(".")[0]],t.split(".").slice(1).join(".")):n[t]=n[t].call(n.propName)},_removeCircularRef:function(n){function i(n,r,u){if(typeof n=="object"){if(Array.prototype.indexOf||(Array.prototype.indexOf=function(n){return jQuery.inArray(n,this)}),t.indexOf(n)>=0){delete u[r];return}t.push(n);for(var f in n)n.hasOwnProperty(f)&&i(n[f],f,n);t.pop();return}}var t=[];return i(n,"obj",null),n},stringify:function(n,i){for(var f,u=this.observables,r=0;r<u.length;r++)f=t.getObject(u[r],n),t.isNullOrUndefined(f)||typeof f!="function"||this._returnFn(n,u[r]);return i&&(n=this._removeCircularRef(n)),JSON.stringify(n)},_setState:function(i){if(i===!0){this._persistHandler=t.proxy(this.persistState,this);n(window).on("unload",this._persistHandler)}else this.deleteState(),n(window).off("unload",this._persistHandler)},_removeProp:function(n,i){t.isNullOrUndefined(n)||(i.indexOf(".")!=-1?this._removeProp(n[i.split(".")[0]],i.split(".").slice(1).join(".")):delete n[i])},persistState:function(){var n,i;if(this._ignoreOnPersist){for(n=r({},this.model),i=0;i<this._ignoreOnPersist.length;i++)this._removeProp(n,this._ignoreOnPersist[i]);n.ignoreOnPersist=this._ignoreOnPersist}else if(this._addToPersist){for(n={},i=0;i<this._addToPersist.length;i++)t.createObject(this._addToPersist[i],t.getObject(this._addToPersist[i],this.model),n);n.addToPersist=this._addToPersist}else n=r({},this.model);this._persistState&&(n.customPersists={},this._persistState(n.customPersists));window.localStorage?(t.isNullOrUndefined(t.persistStateVersion)||window.localStorage.getItem("persistKey")!=null||window.localStorage.setItem("persistKey",t.persistStateVersion),window.localStorage.setItem("$ej$"+this.pluginName+this._id,JSON.stringify(n))):document.cookie&&(t.isNullOrUndefined(t.persistStateVersion)||t.cookie.get("persistKey")!=null||t.cookie.set("persistKey",t.persistStateVersion),t.cookie.set("$ej$"+this.pluginName+this._id,n))},deleteState:function(){var n;window.localStorage?window.localStorage.removeItem("$ej$"+this.pluginName+this._id):document.cookie&&t.cookie.set("$ej$"+this.pluginName+this._id,n,new Date)},restoreState:function(i){var f=null,r,u,e;if(window.localStorage?f=window.localStorage.getItem("$ej$"+this.pluginName+this._id):document.cookie&&(f=t.cookie.get("$ej$"+this.pluginName+this._id)),f&&(r=JSON.parse(f),this._restoreState&&(this._restoreState(r.customPersists),delete r.customPersists),t.isNullOrUndefined(r)===!1&&(t.isNullOrUndefined(r.ignoreOnPersist)?t.isNullOrUndefined(r.addToPersist)||(this._addToPersist=r.addToPersist,delete r.addToPersist):(this._ignoreOnPersist=r.ignoreOnPersist,delete r.ignoreOnPersist))),t.isNullOrUndefined(r)||t.isNullOrUndefined(this._ignoreOnPersist))this.model=n.extend(!0,this.model,r);else{for(u=0,e=this._ignoreOnPersist.length;u<e;u++)this._ignoreOnPersist[u].indexOf(".")!==-1?t.createObject(this._ignoreOnPersist[u],t.getObject(this._ignoreOnPersist[u],this.model),r):r[this._ignoreOnPersist[u]]=this.model[this._ignoreOnPersist[u]];this.model.ngTemplateId&&this.model.ngTemplateId!=r.ngTemplateId&&(r.ngTemplateId=this.model.ngTemplateId);this.model=r}!i&&f&&this._setModel&&this._setModel(this.model)},ignoreOnPersist:function(n){var r=[],t,u;if(typeof n=="object"?r=n:typeof n=="string"&&r.push(n),this._addToPersist===i)for(this._ignoreOnPersist=this._ignoreOnPersist||[],t=0;t<r.length;t++)this._ignoreOnPersist.push(r[t]);else for(t=0;t<r.length;t++)u=this._addToPersist.indexOf(r[t]),this._addToPersist.splice(u,1)},addToPersist:function(t){var u=[],f,r;if(typeof t=="object"?u=t:typeof t=="string"&&u.push(t),this._addToPersist===i)for(this._ignoreOnPersist=this._ignoreOnPersist||[],r=0;r<u.length;r++)f=this._ignoreOnPersist.indexOf(u[r]),this._ignoreOnPersist.splice(f,1);else for(r=0;r<u.length;r++)n.inArray(u[r],this._addToPersist)===-1&&this._addToPersist.push(u[r])},formatting:function(i,r,u){var f,l,h,c,a,v,s,e,y;if(i=i.replace(/%280/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">"),u=t.preferredCulture(u)?u:"en-US",f=i,l=i.split("{0:"),a=i.split("}"),h=l[0],c=a[1],typeof r=="string"&&n.isNumeric(r)&&(r=Number(r)),i.indexOf("{0:")!=-1)return v=new RegExp("\\{0(:([^\\}]+))?\\}","gm"),s=v.exec(i),s!=null&&r!=null?h!=null&&c!=null?h+t.format(r,s[2],u)+c:t.format(r,s[2],u):r!=null?r:"";if(f.startsWith("{")&&!f.startsWith("{0:")){var o=f.split(""),r=(r||"")+"",p=r.split(""),w=/[0aA\*CN<>\?]/gm;for(e=0,y=0;e<o.length;e++)o[e]=w.test(o[e])?"{"+y+++"}":o[e];return String.format.apply(String,[o.join("")].concat(p)).replace("{","").replace("}","")}return this.data!=null&&this.data.Value==null?(n.each(this.data,function(n,t){f=f.replace(new RegExp("\\{"+n+"\\}","gm"),t)}),f):this.data.Value}};t.WidgetBase=function(){};e=function(t,i){var u,f,r;if(t instanceof Array)for(u=0,f=t.length;u<f;u++)r=t[u],r===i[r]&&delete i[r],n.isPlainObject(i[r])&&n.isPlainObject(r)&&e(r,i[r]);else for(r in t)t[r]===i[r]&&delete i[r],n.isPlainObject(i[r])&&n.isPlainObject(t[r])&&e(t[r],i[r])};t.widget=function(f,e,h){var a,l,v;if(typeof f=="object"){h=e;for(a in f)l=f[a],l instanceof Array&&(h._rootCSS=l[1],l=l[0]),t.widget(a,l,h),f[a]instanceof Array&&(h._rootCSS="");return}v=h._rootCSS||t.getNameSpace(e);h=t.defineClass(e,function(i,o){var y,p,w,b,c,tt,g,k,d,nt,l,a;if(this.sfType=e,this.pluginName=f,this.instance=s,t.isNullOrUndefined(this._setFirst)&&(this._setFirst=!0),this["ob.values"]={},n.extend(this,t.widgetBase),this.dataTypes)for(y in o)if(p=this._isValidModelValue(y,this.dataTypes,o),p!==!0)throw"setModel - Invalid input for property :"+y+" - "+p;for(w=i.data("ejWidgets")||[],w.push(f),i.data("ejWidgets",w),c=0;t.widget.observables&&this.observables&&c<this.observables.length;c++)b=t.getObject(this.observables[c],o),b&&t.createObject(this.observables[c],t.widget.observables.register(b,this.observables[c],this,i),o);if(this.element=i.jquery?i:n(i),this.model=r(!0,{},h.prototype.defaults,o),this.model.keyConfigs=r(this.keyConfigs),this.element.addClass(v+" e-js").data(f,this),this._id=i[0].id,this.model.enablePersistence){if(window.localStorage&&!t.isNullOrUndefined(t.persistStateVersion)&&window.localStorage.getItem("persistKey")!=t.persistStateVersion)for(c in window.localStorage)c.indexOf("$ej$")!=-1&&(window.localStorage.removeItem(c),window.localStorage.setItem("persistKey",t.persistStateVersion));else if(document.cookie&&!t.isNullOrUndefined(t.persistStateVersion)&&t.cookie.get("persistKey")!=t.persistStateVersion){g=document.cookie.split(/; */);for(k in g)k.indexOf("$ej$")!=-1&&(t.cookie.set(k.split("=")[0],tt,new Date),t.cookie.set("persistKey",t.persistStateVersion))}this._persistHandler=t.proxy(this.persistState,this);n(window).on("unload",this._persistHandler);this.restoreState(!0)}if(this._init(o),typeof this.model.keyConfigs=="object"&&!(this.model.keyConfigs instanceof Array)){d=!1;this.model.keyConfigs.focus&&this.element.attr("accesskey",this.model.keyConfigs.focus);for(nt in this.model.keyConfigs)if(nt!=="focus"){d=!0;break}d&&this._keyPressed&&(l=i,a="keydown",this.keySettings&&(l=this.keySettings.getElement?this.keySettings.getElement()||l:l,a=this.keySettings.event||a),this._on(l,a,function(n){if(this.model.keyConfigs){var t=u.getActionFromCode(this.model.keyConfigs,n.which,n.ctrlKey,n.shiftKey,n.altKey),i={code:n.which,ctrl:n.ctrlKey,alt:n.altKey,shift:n.shiftKey};t&&this._keyPressed(t,n.target,i,n)===!1&&n.preventDefault()}}))}this._trigger("create")},h);n.fn[f]=function(r){for(var w,p=r,u,y=0;y<this.length;y++){var s=n(this[y]),l=s.data(f),b=l&&s.hasClass(v),a=null;if(this.length>0&&n.isPlainObject(p)&&(r=t.copyObject({},p)),!b){h.prototype._requiresID!==!0||n(this[y]).attr("id")||s.attr("id",c("ejControl_"));r&&typeof r!="object"?o(f+": methods/properties can be accessed only after plugin creation"):(h.prototype.defaults&&!t.isNullOrUndefined(t.setCulture)&&"locale"in h.prototype.defaults&&f!="ejChart"&&(!r||"locale"in r?t.isNullOrUndefined(r)&&(r={},r.locale=t.setCulture().name):r.locale=t.setCulture().name),new h(s,r));continue}if(r)if(u=[].slice.call(arguments,1),this.length>0&&u[0]&&p==="option"&&n.isPlainObject(u[0])&&(u[0]=t.copyObject({},u[0])),n.isPlainObject(r))l.setModel(r);else if((r.indexOf("_")===0||t.isNullOrUndefined(a=t.getObject(r,l)))&&r.indexOf("model.")!==0)o(e+": function/property - "+r+" does not exist");else{if(!a||!n.isFunction(a)){if(arguments.length==1)return a;l.option(r,arguments[1]);continue}if(w=a.apply(l,u),w!==i)return w}}return f.indexOf("ejm")!=-1&&t.widget.registerInstance(s,f,e,h.prototype),this};t.widget.register(f,e,h.prototype);t.loadLocale(f)};t.loadLocale=function(i){for(var f=t.locales,r=0,u=f.length;r<u;r++)n.fn["Locale_"+f[r]](i)};n.extend(t.widget,function(){var n={},i=[],r=function(i,r,u){t.isNullOrUndefined(n[i])||o("ej.widget : The widget named "+i+" is trying to register twice.");n[i]={name:i,className:r,proto:u};t.widget.extensions&&t.widget.extensions.registerWidget(i)},u=function(n,t,r,u){i.push({element:n,pluginName:t,className:r,proto:u})};return{register:r,registerInstance:u,registeredWidgets:n,registeredInstances:i}}());t.widget.destroyAll=function(n){var u,r,t,i;if(n&&n.length)for(u=0;u<n.length;u++)if(r=n.eq(u).data(),t=r.ejWidgets,t&&t.length)for(i=0;i<t.length;i++)r[t[i]]&&r[t[i]].destroy&&r[t[i]].destroy()};t.cookie={get:function(n){var t=RegExp(n+"=([^;]+)").exec(document.cookie);return t&&t.length>1?t[1]:i},set:function(n,t,i){typeof t=="object"&&(t=JSON.stringify(t));t=escape(t)+(i==null?"":"; expires="+i.toUTCString());document.cookie=n+"="+t}};u={getActionFromCode:function(n,t,i,r,f){var s,o,e;i=i||!1;r=r||!1;f=f||!1;for(s in n)if(s!=="focus")for(o=u.getKeyObject(n[s]),e=0;e<o.length;e++)if(t===o[e].code&&i==o[e].isCtrl&&r==o[e].isShift&&f==o[e].isAlt)return s;return null},getKeyObject:function(t){for(var f,o,e,s={isCtrl:!1,isShift:!1,isAlt:!1},c=n.extend(!0,{},s),r=t.split(","),h=[],i=0;i<r.length;i++){if(f=null,r[i].indexOf("+")!=-1)for(o=r[i].split("+"),e=0;e<o.length;e++)f=u.getResult(n.trim(o[e]),s);else f=u.getResult(n.trim(r[i]),n.extend(!0,{},c));h.push(f)}return h},getResult:function(n,t){return n==="ctrl"?t.isCtrl=!0:n==="shift"?t.isShift=!0:n==="alt"?t.isAlt=!0:t.code=parseInt(n,10),t}};t.getScrollableParents=function(t){return n(t).parentsUntil("html").filter(function(){return n(this).css("overflow")!="visible"}).add(n(window))};t.browserInfo=function(){var i={},r=[],e={opera:/(opera|opr)(?:.*version|)[ \/]([\w.]+)/i,edge:/(edge)(?:.*version|)[ \/]([\w.]+)/i,webkit:/(chrome)[ \/]([\w.]+)/i,safari:/(webkit)[ \/]([\w.]+)/i,msie:/(msie|trident) ([\w.]+)/i,mozilla:/(mozilla)(?:.*? rv:([\w.]+)|)/i},o,s,f,u;for(o in e)if(e.hasOwnProperty(o)&&(r=navigator.userAgent.match(e[o]),r)){if(i.name=r[1].toLowerCase()=="opr"?"opera":r[1].toLowerCase(),i.version=r[2],i.culture={},i.culture.name=i.culture.language=navigator.language||navigator.userLanguage,typeof t.globalize!="undefined"){for(s=t.preferredCulture().name,f=navigator.language||navigator.userLanguage?t.preferredCulture(navigator.language||navigator.userLanguage):t.preferredCulture("en-US"),u=0;navigator.languages&&u<navigator.languages.length;u++)if(f=t.preferredCulture(navigator.languages[u]),f.language==navigator.languages[u])break;t.preferredCulture(s);n.extend(!0,i.culture,f)}!navigator.userAgent.match(/Trident\/7\./)||(i.name="msie");break}return i.isMSPointerEnabled=i.name=="msie"&&i.version>9&&window.navigator.msPointerEnabled,i.pointerEnabled=window.navigator.pointerEnabled,i};t.eventType={mouseDown:"mousedown touchstart",mouseMove:"mousemove touchmove",mouseUp:"mouseup touchend",mouseLeave:"mouseleave touchcancel",click:"click touchend"};t.event=function(t,i,r){return n.extend(r||{},{type:t,model:i,cancel:!1})};t.proxy=function(n,t,i){return!n||typeof n!="function"?null:"on"in n&&t?i?n.on(t,i):n.on(t):function(){var r=i?[i]:[];return r.push.apply(r,arguments),n.apply(t||this,r)}};t.hasStyle=function(n){var r=document.documentElement.style,i,t;if(n in r)return!0;for(i=["ms","Moz","Webkit","O","Khtml"],n=n[0].toUpperCase()+n.slice(1),t=0;t<i.length;t++)if(i[t]+n in r)return!0;return!1};Array.prototype.indexOf=Array.prototype.indexOf||function(n){var i=this.length,t;if(i===0)return-1;for(t=0;t<i;t++)if(t in this&&this[t]===n)return t;return-1};String.prototype.startsWith=String.prototype.startsWith||function(n){return this.slice(0,n.length)===n};var r=t.copyObject=function(n,u){var h=2,c,f,s,o,e,l;for(typeof n!="boolean"&&(h=1),s=[].slice.call(arguments,h),h===1&&(u=n,n=i),o=0;o<s.length;o++)for(e in s[o])if(c=u[e],f=s[o][e],f!==i&&c!==f&&s[o]!==f&&u!==f)if(f instanceof Array)if(o===0&&n)if(e==="dataSource"||e==="data"||e==="predicates")u[e]=f.slice();else for(u[e]=[],l=0;l<f.length;l++)r(!0,u[e],f);else u[e]=f.slice();else t.isPlainObject(f)?(u[e]=c||{},n?r(n,u[e],f):r(u[e],f)):u[e]=f;return u},s=function(){return this},h=0,c=function(n){return n+h++};t.template={};t.template.render=t.template["text/x-jsrender"]=function(t,i,r,u,f){i.slice(0,1)!=="#"&&(i=["<div>",i,"<\/div>"].join(""));var e={prop:f,index:u};return n(i).render(r,e)};t.isPlainObject=function(n){if(!n||t.DataManager!==i&&n instanceof t.DataManager||typeof n!="object"||n.nodeType||jQuery.isWindow(n))return!1;try{if(n.constructor&&!n.constructor.prototype.hasOwnProperty("isPrototypeOf"))return!1}catch(f){return!1}var r,u=t.support.isOwnLast;for(r in n)if(u)break;return r===i||n.hasOwnProperty(r)};f=!1;t.util.valueFunction=function(n){return function(r,u){var e=t.getObject(n,this.model);if(f===!1&&(f=t.getObject("observables.getValue",t.widget)),r===i)return t.isNullOrUndefined(f)?typeof e=="function"?e.call(this):e:f(e,u);typeof e=="function"?(this["ob.values"][n]=r,e.call(this,r)):t.createObject(n,r,this.model)}};t.util.getVal=function(n){return typeof n=="function"?n():n};t.support={isOwnLast:function(){var n=function(){this.a=1},t;n.prototype.b=1;for(t in new n)return t==="b"}(),outerHTML:function(){return document.createElement("div").outerHTML!==i}()};o=t.throwError=function(n){try{throw new Error(n);}catch(t){throw t.message+"\n"+t.stack;}};t.getRandomValue=function(n,r){var u,f;return n===i||r===i?t.throwError("Min and Max values are required for generating a random number"):("crypto"in window&&"getRandomValues"in crypto?(f=new Uint16Array(1),window.crypto.getRandomValues(f),u=f[0]%(r-n)+n):u=Math.random()*(r-n)+n,u|0)};t.extensions={};t.extensions.modelGUID="{0B1051BA-1CCB-42C2-A3B5-635389B92A50}"}(window.jQuery,window.Syncfusion),function(){$.fn.addEleAttrs=function(n){var t=$(this);$.each(n,function(n,i){i&&i.specified&&t.attr(i.name,i.value)})};$.fn.removeEleAttrs=function(n){return this.each(function(){var t=$(this),i=$(this.attributes).clone();$.each(i,function(i,r){r&&r.specified&&n.test(r.name)&&t.removeAttr(r.name)})})};$.fn.attrNotStartsWith=function(n){var u=this,r=[],i,t;for(this.each(function(){i=$(this.attributes).clone()}),t=0;t<i.length;t++)if(i[t]&&i[t].specified&&n.test(i[t].name))continue;else r.push(i[t]);return r};$.fn.removeEleEmptyAttrs=function(){return this.each(function(){var n=$(this),t=$(this.attributes).clone();$.each(t,function(t,i){i&&i.specified&&i.value===""&&n.removeAttr(i.name)})})};$.extend($.support,{has3d:ej.addPrefix("perspective")in ej.styles,hasTouch:"ontouchstart"in window,hasPointer:navigator.msPointerEnabled,hasTransform:ej.userAgent()!==!1,pushstate:"pushState"in history&&"replaceState"in history,hasTransition:ej.addPrefix("transition")in ej.styles});$.extend($.expr[":"],{attrNotStartsWith:function(n,t,i){for(var u=n.attributes,r=0;r<u.length;r++)if(u[r].nodeName.indexOf(i[3])===0)return!1;return!0}});var n=$.fn.andSelf||$.fn.addBack;$.fn.andSelf=$.fn.addBack=function(){return n.apply(this,arguments)}}()});
