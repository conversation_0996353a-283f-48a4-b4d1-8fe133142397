/*!
*  filename: ej.pivot.common.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){ej.Pivot=ej.Pivot||{},function(n,t,r){t.Pivot={addReportItem:function(n,i){var r,u;if(i.isMeasuresDropped)i.droppedClass!=""?n.values[0].axis=i.droppedClass=="row"?"rows":i.droppedClass=="column"?"columns":n.values[0].axis:n.values[0].measures=[];else{r=this.removeReportItem(n,i.droppedFieldName,i.isMeasuresDropped);t.isNullOrUndefined(r)&&(r={fieldName:i.droppedFieldName,fieldCaption:i.droppedFieldCaption,format:i.droppedFieldFormat,formatString:i.droppedFieldFormatString,showSubTotal:i.droppedFieldShowSubTotal,expression:i.droppedExpression,hierarchyUniqueName:i.droppedHierarchyUniqueName,summaryType:i.summaryType});for(u in r)(u=="format"||u=="formatString")&&(t.isNullOrUndefined(r[u])||r[u]=="")&&delete r[u];switch(i.droppedClass){case"row":i.droppedPosition.toString()!=""?n.rows.splice(i.droppedPosition,0,r):n.rows.push(r);break;case"column":i.droppedPosition.toString()!=""?n.columns.splice(i.droppedPosition,0,r):n.columns.push(r);break;case"value":n.cube==""?i.droppedPosition.toString()!=""?n.values.splice(i.droppedPosition,0,r):n.values.push(r):i.droppedPosition.toString()!=""?n.values[0].measures.splice(i.droppedPosition,0,r):n.values[0].measures.push(r);break;case"filter":i.droppedPosition.toString()!=""?n.filters.splice(i.droppedPosition,0,r):n.filters.push(r)}}},removeReportItem:function(i,r,u){var o=i.cube==""?t.Pivot.AnalysisMode.Pivot:t.Pivot.AnalysisMode.Olap,f=n.grep(i.columns,function(n){return n.fieldName==r}),e;return u?i.values[0].measures=[]:f.length>0?i.columns=n.grep(i.columns,function(n){return n.fieldName!=r}):(f=n.grep(i.rows,function(n){return n.fieldName==r}),f.length>0?i.rows=n.grep(i.rows,function(n){return n.fieldName!=r}):(e=o==t.Pivot.AnalysisMode.Olap?i.values[0].measures:i.values,f=n.grep(e,function(n){return n.fieldName==r}),f.length>0?i.values=o==t.Pivot.AnalysisMode.Olap?[{measures:n.grep(e,function(n){return n.fieldName!=r}),axis:i.values[0].axis}]:n.grep(e,function(n){return n.fieldName!=r}):(f=n.grep(i.filters,function(n){return n.fieldName==r}),f.length>0&&(i.filters=n.grep(i.filters,function(n){return n.fieldName!=r}))))),f[0]},getReportItemByFieldName:function(i,r){var f="columns",u=n.grep(r.columns,function(n){return n.fieldName.toLowerCase()==i.toLowerCase()}),e;return u.length==0&&(u=n.grep(r.rows,function(n){return n.fieldName.toLowerCase()==i.toLowerCase()}),f="rows"),u.length==0&&(e=r.cube==""?r.values:r.values.length>0&&!t.isNullOrUndefined(r.values[0].measures)?r.values[0].measures:[],u=n.grep(e,function(n){return n.fieldName.toLowerCase()==i.toLowerCase()}),f="values"),u.length==0&&(u=n.grep(r.filters,function(n){return n.fieldName.toLowerCase()==i.toLowerCase()}),f="filters"),{item:u[0],axis:f}},getReportItemByFieldCaption:function(i,r){var f="columns",u=n.grep(r.columns,function(n){return n.fieldCaption.toLowerCase()==i.toLowerCase()}),e;return u.length==0&&(u=n.grep(r.rows,function(n){return n.fieldCaption.toLowerCase()==i.toLowerCase()}),f="rows"),u.length==0&&(e=r.cube==""?r.values:r.values.length>0&&!t.isNullOrUndefined(r.values[0].measures)?r.values[0].measures:[],u=n.grep(e,function(n){return n.fieldCaption.toLowerCase()==i.toLowerCase()}),f="values"),u.length==0&&(u=n.grep(r.filters,function(n){return n.fieldCaption.toLowerCase()==i.toLowerCase()}),f="filters"),{item:u[0],axis:f}},closePreventPanel:function(i){var r=i.type!="close"?i:this;r.element.find("[id^='preventDiv']").remove();t.isNullOrUndefined(r.model.pivotControl)||t.isNullOrUndefined(r.model.pivotControl.element)?r._currentReportItems=n.extend(!0,[],r._savedReportItems):(r.model.pivotControl.element.find("[id^='preventDiv']").remove(),r.model.pivotControl._currentReportItems=n.extend(!0,[],r.model.pivotControl._savedReportItems));t.isNullOrUndefined(r._waitingPopup)||r._waitingPopup.hide();t.isNullOrUndefined(r.model.pivotControl)||t.isNullOrUndefined(r.model.pivotControl._waitingPopup)||r.model.pivotControl._waitingPopup.hide()},openPreventPanel:function(i){var r=t.buildTag("div#preventDiv.errorDlg").css({width:n("body").width()+"px",height:n("body").height()+"px",position:"absolute",top:n("body").offset().top+"px",left:n("body").offset().left+"px","z-index":10})[0].outerHTML;n("#"+i._id).append(r)},_createErrorDialog:function(i,r,u){var e,o;if(t.Pivot.closePreventPanel(u),u._errorDialog=r,t.Pivot.openPreventPanel(u),u.element.find(".e-errorDialog:visible").length==0){e=t.isNullOrUndefined(i.Exception)?t.buildTag("div.e-errorDialog#"+this._id+"_ErrorDialog",t.buildTag("div.warningImg")[0].outerHTML+t.buildTag("div.warningContent action:",i)[0].outerHTML+t.buildTag("div",t.buildTag("button"+this._id+"_ErrOKBtn.errOKBtn",u._getLocalizedLabels("OK"),{margin:"20px 0 10px 165px"}).attr("title",u._getLocalizedLabels("OK").replace(/(<([^>]+)>)/ig,""))[0].outerHTML)[0].outerHTML).attr("title",r)[0].outerHTML:t.buildTag("div.e-errorDialog#"+this._id+"_ErrorDialog",t.buildTag("div.warningImg")[0].outerHTML+t.buildTag("div.warningContent action:",i.Exception.Message)[0].outerHTML+t.buildTag("br")[0].outerHTML+t.buildTag("div."+u._id+"stackTraceContent","Stack Trace :"+i.Exception.StackTraceString)[0].outerHTML+t.buildTag("div",t.buildTag("button#"+this._id+"_ErrOKBtn.errOKBtn",u._getLocalizedLabels("OK"),{margin:"20px 0 10px 165px"}).attr("title",u._getLocalizedLabels("OK").replace(/(<([^>]+)>)/ig,""))[0].outerHTML)[0].outerHTML).attr("title",r)[0].outerHTML;u.element.append(e);u.element.find(".e-errorDialog").ejDialog({target:"#"+u._id,enableResize:!1,enableRTL:u.model.enableRTL,width:"400px",close:t.proxy(t.Pivot.closePreventPanel,u)});o=u.element.find(".e-errorDialog").data("ejDialog");n("#"+o._id+"_wrapper").css({left:"50%",top:"50%"});u.element.find(".errOKBtn").ejButton({type:t.ButtonType.Button,width:"50px"}).on(t.eventType.click,function(){u._errorDialog!="nodeCheck"||t.isNullOrUndefined(u._schemaData)||t.isNullOrUndefined(u._schemaData._selectedTreeNode)||u._schemaData._tableTreeObj.uncheckNode(u._schemaData._selectedTreeNode);u.element.find("#preventDiv").remove();(u._errorDialog==u._getLocalizedLabels("Success")||u._errorDialog==u._getLocalizedLabels("Warning")||u._errorDialog==u._getLocalizedLabels("Exception")||u._errorDialog==u._getLocalizedLabels("Error"))&&u.element.children("#"+this._id+"_ErrorDialog_wrapper").remove()});if(u.element.find(".e-dialog .e-close").attr("title","Close"),t.isNullOrUndefined(n("#"+u._id).data("ejWaitingPopup"))||n("#"+u._id).data("ejWaitingPopup").hide(),typeof oclientWaitingPopup!="undefined"&&oclientWaitingPopup!=null&&oclientWaitingPopup.hide(),!t.isNullOrUndefined(i.Exception)){var f=50,h="...",s="Show more",c="Show less";n("."+u._id+"stackTraceContent").each(function(){var i=n(this).html();if(i.length>f){u._id;var r=i.substr(0,f),e=i.substr(f,i.length-f),o=r+t.buildTag("span."+u._id+"moreellipses",h)[0].outerHTML+t.buildTag("span."+u._id+"morecontent",t.buildTag("span",e).css("display","none")[0].outerHTML+t.buildTag("a."+u._id+"morelink",s).css("display","block")[0].outerHTML)[0].outerHTML;n(this).html(o)}});n("."+u._id+"morelink").click(function(){return n(this).hasClass("less")?(n(this).removeClass("less"),n(this).html(s)):(n(this).addClass("less"),n(this).html(c)),n(this).parent().prev().toggle("slow",function(){}),n(this).prev().toggle("slow",function(){}),!1})}}},_updateValueSortingIndex:function(i,u){if(i.SortColIndex!=r)t.PivotAnalysis._valueSorting=JSON.parse(i.SortColIndex)[0],t.PivotAnalysis._sort=u.model.valueSortSettings.sortOrder;else{i=i[0]!=r?i:i.d!=r?i.d:i;var f=n.map(i,function(n){if(n!=null)var t=n.Key;if(t!=null&&t!=r&&t=="SortColIndex")return n});f.length>0?(i.splice(n.map(i,function(n,t){var i=n.Key;if(i=="SortColIndex")return t}),1),t.PivotAnalysis._valueSorting=JSON.parse(f[0].Value),t.PivotAnalysis._sort=u.model.valueSortSettings.sortOrder):t.PivotAnalysis._valueSorting=null}},_memberSortBtnClick:function(i){var a=this.element.hasClass("e-pivotclient")&&this.model.operationalMode=="servermode"&&this.model.analysisMode=="olap",v=this.pluginName=="ejPivotSchemaDesigner"?this.model.pivotControl:this,w=this.pluginName=="ejPivotGrid"&&v.model.analysisMode==t.Pivot.AnalysisMode.Olap&&v.model.operationalMode==t.Pivot.OperationalMode.ServerMode,o=n(i.target).parents(".e-memberEditorDiv").find(".e-editorTreeView"),b=this._sortType=n(i.target).hasClass("e-memberAscendingIcon")?"ascending":"descending",c=[],y=jQuery.extend(!0,[],this._memberTreeObj._dataSource),p,f,s,e,u,l,h;if(this.element.find(".e-memberDrillPager").is(":visible")&&(p=this.model.memberEditorPageSize==r?this.model.pivotControl.model.memberEditorPageSize:this.model.memberEditorPageSize,c=t.Pivot._getParentsTreeList(this,this._drilledNode,this._editorTreeData).concat(t.DataManager(this._editorDrillTreeData[this._drilledNode]).executeLocal(t.Query().sortBy("name",this._sortType).page(this._memberDrillPageSettings.currentMemberDrillPage,p)))),!t.isNullOrUndefined(o)&&o.length>0){if(t.Pivot.updateTreeView(this),f=(this.model.enableMemberEditorPaging||!t.isNullOrUndefined(this.model.pivotControl)&&this.model.pivotControl.model.enableMemberEditorPaging)&&!this._isSearchApplied?this._editorTreeData:o.data("ejTreeView").model.fields.dataSource,s=!a&&(f[0].id=="All"||f[0].id=="(All)_0")?f.slice(1):f,c.length>0&&(s=c),s.length>1&&s[0].id=="SearchFilterSelection"&&(s=s.slice(1)),e=a?[]:f.length>1&&(f[0].id=="All"||f[0].id=="(All)_0")?f.splice(0,1):[],u=t.DataManager(s).executeLocal(t.Query().sortBy("name",b)),!t.isNullOrUndefined(e)&&e.length>0&&(u.splice(0,0,e[0]),f.splice(0,0,e[0])),(this.model.enableMemberEditorPaging||!t.isNullOrUndefined(this.model.pivotControl)&&this.model.pivotControl.model.enableMemberEditorPaging)&&c.length==0&&(u=t.DataManager(u).executeLocal(t.Query().page(this._memberPageSettings.currentMemeberPage,(this.model.memberEditorPageSize||this.model.pivotControl.model.memberEditorPageSize)+(this._memberPageSettings.currentMemeberPage==1?1:0)))),v.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&u.length>0&&u[0].id!="All"&&u[0].id!="(All)_0"&&!t.isNullOrUndefined(e)&&e.length>0&&u.splice(0,0,e[0]),e={},o.data("ejTreeView").model.fields.dataSource.length>1&&o.data("ejTreeView").model.fields.dataSource[1].id=="SearchFilterSelection"&&u[1].id!="SearchFilterSelection"&&u.splice(1,0,{id:"SearchFilterSelection",name:this._getLocalizedLabels("AddCurrentSelectionToFilter"),checkedStatus:this._isSelectSearchFilter,tag:"SearchFilterSelection",spriteCssClass:"e-searchfilterselection",uniqueName:"Add current selection to filter"}),a&&this._isSearchApplied&&u.splice(0,0,{id:"SearchFilterSelection",name:this._getLocalizedLabels("AddCurrentSelectionToFilter"),checkedStatus:this._isSelectSearchFilter,tag:"SearchFilterSelection",spriteCssClass:"e-searchfilterselection",uniqueName:"Add current selection to filter"}),t.isNullOrUndefined(o)||o.ejTreeView({fields:{dataSource:jQuery.extend(!0,[],u)}}),c.length>0&&!t.isNullOrUndefined(y)&&(this._memberTreeObj._dataSource=y),w)for(l=this.element.find(".e-editorTreeView").find("li"),h=0;h<l.length;h++)t.isNullOrUndefined(n(l[h]).attr("id"))||n(l[h]).attr("data-tag",t.DataManager(o.data("ejTreeView").model.fields.dataSource).executeLocal(t.Query().where("id","equal",n(l[h]).attr("id")))[0].tag);t.Pivot._separateAllMember(this)}},_contextMenuOpen:function(i,r){var f,e,u,o;if(t.Pivot.openPreventPanel(r),r._selectedMember=n(i.target),r.pluginName=="ejPivotGrid"?(f=r.model.analysisMode==t.Pivot.AnalysisMode.Olap?"olap":"pivot",u=n("#"+this._id+"_pivotTree").data("ejMenu")):(t.isNullOrUndefined(r.model.pivotControl)||(f=r.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap?"olap":"pivot"),u=n("#"+r._id+"_pivotTreeContextMenu").data("ejMenu")),f==t.Pivot.AnalysisMode.Olap){if(t.isNullOrUndefined(n(i.target).parent().attr("data-tag"))&&n(i.target).hasClass("e-pivotButton")&&n(i.target).children(".e-pvtBtn:eq(0)").length>0)return u.disable(),r._selectedMember=n(i.target).children(".e-pvtBtn:eq(0)"),!1;!t.isNullOrUndefined(n(i.target).parent().attr("data-tag"))&&n(i.target).parent().attr("data-tag").split(":")[1].toLowerCase().startsWith("[measures]")?u.disable():t.isNullOrUndefined(r._selectedMember.parent().attr("data-tag"))||r._selectedMember.parent().attr("data-tag").split(":")[1].toLowerCase()!="measures"?t.isNullOrUndefined(n(i.target).parent().attr("data-tag"))?u.disable():(u.disableItem(r._getLocalizedLabels("AddToValues")),n(i.target.parentElement).find(".e-namedSetCDB").length>0?u.disableItem(r._getLocalizedLabels("AddToFilter")):u.enableItem(r._getLocalizedLabels("AddToFilter")),u.enableItem(r._getLocalizedLabels("AddToRow")),u.enableItem(r._getLocalizedLabels("AddToColumn"))):(u.disableItem(r._getLocalizedLabels("AddToValues")),u.disableItem(r._getLocalizedLabels("AddToFilter")),u.enableItem(r._getLocalizedLabels("AddToRow")),u.enableItem(r._getLocalizedLabels("AddToColumn")))}else f==t.Pivot.AnalysisMode.Pivot&&(e=i.target.textContent,n(i.target).hasClass("e-btn")?(u.enable(),r.pluginName=="ejPivotGrid"&&(u.disableItem(r._getLocalizedLabels("CalculatedField")),n(i.target).parents(".e-pivotButton").attr("data-tag").split(":")[0].toLowerCase()=="values"&&(r.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode&&n.grep(r.model.dataSource.values,function(n){return n.fieldCaption==e&&n.isCalculatedField==!0}).length>0?u.disable():r.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&n.grep(JSON.parse(r.getOlapReport()).PivotCalculations,function(n){return n.FieldHeader==e&&n.CalculationType==8}).length>0&&u.disable(),u.enableItem(r._getLocalizedLabels("CalculatedField"))))):n("#"+this._id+"_pivotTree").length>0?(u=n("#"+this._id+"_pivotTree").data("ejMenu"),u.disable()):u.disable());o=r.model.pivotControl;t.isNullOrUndefined(o)||o.model.analysisMode!=t.Pivot.AnalysisMode.Pivot||n(i.target).parents(".e-schemaValue").length==0&&u.disableItem(r._getLocalizedLabels("SummarizeValueBy"))},_searchTreeNodes:function(i){var w=n(i.target).hasClass("searchTreeView")?this.element.find(".searchTreeView").data("ejMaskEdit"):this.element.find(".searchEditorTreeView").data("ejMaskEdit"),f=n(i.target).hasClass("searchTreeView")?this.model.operationalMode==t.Pivot.OperationalMode.ClientMode||this.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this.element.find(".e-schemaFieldTree").data("ejTreeView"):this.element.find(".e-cubeTreeView").data("ejTreeView"):this.element.find(".e-editorTreeView").data("ejTreeView"),a={id:"SearchFilterSelection",name:this._getLocalizedLabels("AddCurrentSelectionToFilter"),hasChildren:!1,checkedStatus:!0,tag:"SearchFilterSelection",spriteCssClass:"e-searchfilterselection",uniqueName:"Add current selection to filter"},r=f.element,b=r.find("li[data-isItemSearch='true']"),k=t.isNullOrUndefined(w.get_StrippedValue())?"":w.get_StrippedValue().toLowerCase(),e,o,c,h,v,u;if(b.length>0&&n(b).each(function(n,t){t.removeAttribute("data-isItemSearch")}),k.length>0){var d=[],g=[],s=r.find("ul>li>div>a");for(e=0;e<s.length;e++)n(s[e]).text().toLowerCase().indexOf(k)!=-1||n(n(s[e]).closest("li")).attr("id")=="(All)_0"||n(n(s[e]).closest("li")).attr("id")=="All"||n(n(s[e]).closest("li")).attr("id")=="SearchFilterSelection"?d.push(s[e]):g.push(s[e]);for(o=r.find(d).closest("li").css("display","block"),o.length>0&&(this._isOptionSearch=!0,n(o).each(function(t,i){n(i).attr("id")!="SearchFilterSelection"&&i.setAttribute("data-isItemSearch",!0)}),r.hasClass("e-editorTreeView")&&(!r.find("li span.e-searchfilterselection").length>0?(r.find("li:first").attr("id")=="(All)_0"||r.find("li:first").attr("id")=="All"?(o.length>1&&f.insertAfter(a,r.find("li:first")),(this.element.hasClass("e-pivotschemadesigner")?this.model.pivotControl.model.analysisMode:this.model.analysisMode)==t.Pivot.AnalysisMode.Pivot&&r.find("li span.e-searchfilterselection").closest("li").css("padding","0")):f.insertBefore(a,r.find("li:first")),r.find("li span.e-searchfilterselection").closest("li").attr("data-tag",a.tag)):r.find("li span.e-searchfilterselection").length>0&&!this._isSelectSearchFilter&&f.uncheckNode(r.find("li span.e-searchfilterselection").closest("li")))),r.find(g).closest("li").css("display","none"),u=0;u<o.length;u++)c=n(o[u]),h=c.parents("ul").closest("li").css("display","block"),h.length>0&&(f.expandNode(h),f.model.expandedNodes.indexOf(n(f._liList).index(h))!=-1||t.isNullOrUndefined(window._temp)||window._temp.push(h)),v=c.children("ul"),v.length>0&&v.children("li:visible").length==0&&(c.children("ul").children("li").css("display","block"),f.expandNode(o[u]),f.model.expandedNodes.indexOf(n(f._liList).index(o[u]))!=-1||t.isNullOrUndefined(window._temp)||window._temp.push(o[u]))}else{r.find("ul>li").css("display","block");r.find("li span.e-searchfilterselection").closest("li").length>0&&f.removeNode(r.find("#"+this._id+"_SearchFilterSelection"));n(r.find("ul>li")).each(function(n,t){t.removeAttribute("data-isItemSearch")});this._isOptionSearch=!1;var l=n(r).find("ul>li"),y=0,p=0;if(n(n(l)[0]).attr("id")=="(All)_0"||n(n(l)[0]).attr("id")=="All"){for(u=1;u<n(l).length;u++)n(n(l)[u]).find("span.e-checkmark").length>0?y++:p++;y>0&&p>0?n(r.find("ul>li")[0]).find("div .e-chkbox-small > span > span:eq(0)").removeClass("e-checkmark").addClass("e-stop"):y>0&&p==0?n(r.find("ul>li")[0]).find("div .e-chkbox-small > span > span:eq(0)").removeClass("e-stop").addClass("e-checkmark"):n(r.find("ul>li")[0]).find("div .e-chkbox-small > span > span:eq(0)").removeClass("e-checkmark").removeClass("e-stop")}if(!t.isNullOrUndefined(window._temp))for(u=0;u<window._temp.length;u++)f._collpaseNode(window._temp[u]);window._temp=[];n(i.target).hasClass("searchTreeView")&&f.collapseAll()}},_updateSearchFilterSelection:function(i,r,u){var s={},o={},f,e;i.id=="SearchFilterSelection"&&(f=n(r).find("li:not([data-isItemSearch='true'])"),i.type=="nodeCheck"?(u._isSelectSearchFilter=!0,u._isOptionSearch?f.length>0&&n(f).each(function(i,f){n(f).attr("id")!="SearchFilterSelection"&&!t.isNullOrUndefined(u._currentFilterList[n(f).attr("id")])&&u._currentFilterList[n(f).attr("id")].checkedStatus&&n(r).ejTreeView("checkNode",n(f))}):f.length>0&&(u.model.enableMemberEditorPaging&&(s=u._currentFilterList,n(s).each(function(n,t){o[t.id]=t})),n(u._editorTreeData).each(function(i,r){for(var e=0;e<f.length;e++)if(f[e].id!=r.id)t.isNullOrUndefined(u._currentFilterList)||(u.model.enableMemberEditorPaging&&!t.isNullOrUndefined(o)&&o[n(r).attr("id")]?r.checkedStatus=o[n(r).attr("id")].checkedStatus:u._currentFilterList[n(r).attr("id")]&&(r.checkedStatus=u._currentFilterList[n(r).attr("id")].checkedStatus));else{r.checkedStatus=n(f[e]).find(".checked").length>0?!0:!1;break}}),e=!0,n(u._editorTreeData).each(function(n,t){if(t.checkedStatus)return e=!1,!1}),e&&u._editorTreeData.length>0?(u._dialogOKBtnObj.disable(),u.element.find(".e-dialogOKBtn").attr("disabled","disabled")):(u._dialogOKBtnObj.enable(),u.element.find(".e-dialogOKBtn").removeAttr("disabled")))):i.type=="nodeUncheck"&&(u._isSelectSearchFilter=!1,u._isOptionSearch?f.length>0&&n(f).each(function(t,i){n(i).attr("id")!="SearchFilterSelection"&&n(r).ejTreeView("uncheckNode",n(i))}):f.length>0&&(n(u._editorTreeData).each(function(t,i){for(var r=0;r<f.length;r++)if(f[r].id!=i.id)i.checkedStatus=!1;else{i.checkedStatus=n(f[r]).find(".checked").length>0?!0:!1;break}}),e=!0,n(u._editorTreeData).each(function(n,t){if(t.checkedStatus)return e=!1,!1}),e&&u._editorTreeData.length>0?(u._dialogOKBtnObj.disable(),u.element.find(".e-dialogOKBtn").attr("disabled","disabled")):(u._dialogOKBtnObj.enable(),u.element.find(".e-dialogOKBtn").removeAttr("disabled")))))},editorTreeNavigatee:function(i,r){var e,f,u;if(n(i.target).parents(".e-memberPager").length>0){if(u=r._memberPageSettings.currentMemeberPage,!n(i.target).hasClass("e-pageDisabled")){if(n(i.target).hasClass("e-nextPage"))r._memberPageSettings.startPage+=r.model.memberEditorPageSize,r._memberPageSettings.currentMemeberPage+=1,r._memberPageSettings.endPage+=r.model.memberEditorPageSize;else if(n(i.target).hasClass("e-prevPage"))r._memberPageSettings.currentMemeberPage-=1,r._memberPageSettings.startPage=Math.abs(r._memberPageSettings.startPage-(r._memberPageSettings.currentMemeberPage==1?r.model.memberEditorPageSize:r.model.memberEditorPageSize)),r._memberPageSettings.endPage-=r.model.memberEditorPageSize;else if(n(i.target).hasClass("e-firstPage"))r._memberPageSettings.currentMemeberPage=1,r._memberPageSettings.endPage=r.model.memberEditorPageSize,r._memberPageSettings.startPage=0;else if(n(i.target).hasClass("e-lastPage"))r._memberPageSettings.currentMemeberPage=parseInt(n.trim(r.element.find(".e-memberPageCount").text().split("/")[1])),r._memberPageSettings.endPage=r._memberPageSettings.currentMemeberPage*r.model.memberEditorPageSize,r._memberPageSettings.startPage=r._memberPageSettings.endPage-r.model.memberEditorPageSize;else{if(parseInt(n(i.target).val())>n.trim(r.element.find(".e-memberPageCount").text().split("/")[1])||parseInt(n(i.target).val())==0)return r.element.find(".e-memberCurrentPage").val(u),!1;r._memberPageSettings.currentMemeberPage=parseInt(n(i.target).val());r._memberPageSettings.endPage=r._memberPageSettings.currentMemeberPage*r.model.memberEditorPageSize;r._memberPageSettings.startPage=r._memberPageSettings.currentMemeberPage==1||r._memberPageSettings.currentMemeberPage==0?0:r._memberPageSettings.endPage-r.model.memberEditorPageSize}r._waitingPopup.show();r.element.hasClass("e-pivotclient")&&r.model.analysisMode==t.Pivot.AnalysisMode.Olap&&r.model.operationalMode==t.Pivot.OperationalMode.ServerMode?(r.model.enableMemberEditorSorting&&(r._editorTreeData=t.DataManager(r._editorTreeData).executeLocal(t.Query().sortBy("name",r._sortType))),this.editorTreePageInfoSuccess(r._editorTreeData,r)):(r.pluginName!="ejPivotSchemaDesigner"?r._isMemberPageFilter=!0:r.model.pivotControl._isMemberPageFilter=!0,e=t.Pivot.updateTreeView(r),f=t.DataManager(r._editorTreeData).executeLocal(t.Query().sortBy("name",t.isNullOrUndefined(r._sortType)?"":r._sortType).where(t.Predicate("pid","equal",null).and("id","notequal","All").and("id","notequal","(All)_0")).page(r._memberPageSettings.currentMemeberPage,r.pluginName=="ejPivotSchemaDesigner"?r.model.pivotControl.model.memberEditorPageSize:r.model.memberEditorPageSize)),r._memberPageSettings.currentMemeberPage>1?r.element.find(".e-prevPage, .e-firstPage").removeClass("e-pageDisabled").addClass("e-pageEnabled"):r.element.find(".e-prevPage, .e-firstPage").removeClass("e-pageEnabled").addClass("e-pageDisabled"),r._memberPageSettings.currentMemeberPage==parseInt(n.trim(r.element.find(".e-memberPageCount").text().split("/")[1]))?r.element.find(".e-nextPage, .e-lastPage").removeClass("e-pageEnabled").addClass("e-pageDisabled"):r.element.find(".e-nextPage, .e-lastPage").removeClass("e-pageDisabled").addClass("e-pageEnabled"),r.element.find(".e-memberCurrentPage").val(r._memberPageSettings.currentMemeberPage),r.element.find(".e-nextPageDiv .e-pageDisabled").css("opacity","0.5"),r.element.find(".e-nextPageDiv .e-pageEnabled").css("opacity","1"),t.Pivot._createSearchTreeview(f,r),r._waitingPopup.hide())}}else if(n(i.target).parents(".e-memberSearchPager").length>0){if(u=r._memberSearchPageSettings.currentMemberSearchPage,!n(i.target).hasClass("e-pageDisabled")){if(n(i.target).hasClass("e-nextPage"))r._memberSearchPageSettings.startSearchPage+=r.model.memberEditorPageSize,r._memberSearchPageSettings.currentMemberSearchPage+=1,r._memberSearchPageSettings.endSearchPage+=r.model.memberEditorPageSize;else if(n(i.target).hasClass("e-prevPage"))r._memberSearchPageSettings.currentMemberSearchPage-=1,r._memberSearchPageSettings.startSearchPage=Math.abs(r._memberSearchPageSettings.startSearchPage-(r._memberSearchPageSettings.currentMemberSearchPage==1?r.model.memberEditorPageSize:r.model.memberEditorPageSize)),r._memberSearchPageSettings.endSearchPage-=r.model.memberEditorPageSize;else if(n(i.target).hasClass("e-firstPage"))r._memberSearchPageSettings.currentMemberSearchPage=1,r._memberSearchPageSettings.endSearchPage=r.model.memberEditorPageSize,r._memberSearchPageSettings.startSearchPage=0;else if(n(i.target).hasClass("e-lastPage"))r._memberSearchPageSettings.currentMemberSearchPage=parseInt(n.trim(r.element.find(".e-memberSearchPageCount").text().split("/")[1])),r._memberSearchPageSettings.endSearchPage=r._memberSearchPageSettings.currentMemberSearchPage*r.model.memberEditorPageSize,r._memberSearchPageSettings.startSearchPage=r._memberSearchPageSettings.endSearchPage-r.model.memberEditorPageSize;else{if(parseInt(n(i.target).val())>n.trim(r.element.find(".e-memberSearchPageCount").text().split("/")[1])||parseInt(n(i.target).val())==0)return r.element.find(".e-memberCurrentSearchPage").val(u),!1;r._memberSearchPageSettings.currentMemberSearchPage=parseInt(n(i.target).val());r._memberSearchPageSettings.endSearchPage=r._memberSearchPageSettings.currentMemberSearchPage*r.model.memberEditorPageSize;r._memberSearchPageSettings.startSearchPage=r._memberSearchPageSettings.currentMemberSearchPage==1||r._memberSearchPageSettings.currentMemberSearchPage==0?0:r._memberSearchPageSettings.endSearchPage-r.model.memberEditorPageSize}r._waitingPopup.show();r.element.hasClass("e-pivotclient")&&r.model.analysisMode==t.Pivot.AnalysisMode.Olap&&r.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this._editorTreeSearchPageInfoSuccess(r._editorSearchTreeData,r):(r.pluginName!="ejPivotSchemaDesigner"?r._isMemberPageFilter=!0:r.model.pivotControl._isMemberPageFilter=!0,this._editorTreeSearchPageInfoSuccess(r._editorSearchTreeData,r))}}else if(n(i.target).parents(".e-memberDrillPager").length>0&&(u=r._memberDrillPageSettings.currentMemberDrillPage,!n(i.target).hasClass("e-pageDisabled"))){if(n(i.target).hasClass("e-nextPage"))r._memberDrillPageSettings.startDrillPage+=r.model.memberEditorPageSize,r._memberDrillPageSettings.currentMemberDrillPage+=1,r._memberDrillPageSettings.endDrillPage+=r.model.memberEditorPageSize;else if(n(i.target).hasClass("e-prevPage"))r._memberDrillPageSettings.currentMemberDrillPage-=1,r._memberDrillPageSettings.startDrillPage=Math.abs(r._memberDrillPageSettings.startDrillPage-(r._memberDrillPageSettings.currentMemberDrillPage==1?r.model.memberEditorPageSize:r.model.memberEditorPageSize)),r._memberDrillPageSettings.endDrillPage-=r.model.memberEditorPageSize;else if(n(i.target).hasClass("e-firstPage"))r._memberDrillPageSettings.currentMemberDrillPage=1,r._memberDrillPageSettings.endDrillPage=r.model.memberEditorPageSize,r._memberDrillPageSettings.startDrillPage=0;else if(n(i.target).hasClass("e-lastPage"))r._memberDrillPageSettings.currentMemberDrillPage=parseInt(n.trim(r.element.find(".e-memberDrillPageCount").text().split("/")[1])),r._memberDrillPageSettings.endDrillPage=r._memberDrillPageSettings.currentMemberDrillPage*r.model.memberEditorPageSize,r._memberDrillPageSettings.startDrillPage=r._memberDrillPageSettings.endDrillPage-r.model.memberEditorPageSize;else{if(parseInt(n(i.target).val())>n.trim(r.element.find(".e-memberDrillPageCount").text().split("/")[1])||parseInt(n(i.target).val())==0)return r.element.find(".e-memberCurrentDrillPage").val(u),!1;r._memberDrillPageSettings.currentMemberDrillPage=parseInt(n(i.target).val());r._memberDrillPageSettings.endDrillPage=r._memberDrillPageSettings.currentMemberDrillPage*r.model.memberEditorPageSize;r._memberDrillPageSettings.startDrillPage=r._memberDrillPageSettings.currentMemberDrillPage==1||r._memberDrillPageSettings.currentMemberDrillPage==0?0:r._memberDrillPageSettings.endDrillPage-r.model.memberEditorPageSize}r._waitingPopup.show();this._editorTreeDrillPageInfoSuccess(r._memberTreeObj._dataSource.slice(),r)}},editorTreePageInfoSuccess:function(i,u){setTimeout(function(){u._memberPageSettings.currentMemeberPage>1?u.element.find(".e-prevPage, .e-firstPage").removeClass("e-pageDisabled").addClass("e-pageEnabled"):u.element.find(".e-prevPage, .e-firstPage").removeClass("e-pageEnabled").addClass("e-pageDisabled");u._memberPageSettings.currentMemeberPage==parseInt(n.trim(u.element.find(".e-memberPageCount").text().split("/")[1]))?u.element.find(".e-nextPage, .e-lastPage").removeClass("e-pageEnabled").addClass("e-pageDisabled"):u.element.find(".e-nextPage, .e-lastPage").removeClass("e-pageDisabled").addClass("e-pageEnabled");var e,f;u.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&(e=i[0]!=r?i[0].Value:i.d!=r?i.d[0].Value:i.EditorTreeInfo,f=n.parseJSON(e),f.splice(-1,1));u.element.find(".e-memberCurrentPage").val(u._memberPageSettings.currentMemeberPage);u._appendTreeViewData(u.model.operationalMode==t.Pivot.OperationalMode.ClientMode?f:t.DataManager(u._editorTreeData).executeLocal(t.Query().where("pid","equal",null||r).page(u._memberPageSettings.currentMemeberPage,u.pluginName=="ejPivotSchemaDesigner"?u.model.pivotControl.model.memberEditorPageSize:u.model.memberEditorPageSize)),u.element.find(".pvtBtn").parent("[data-tag='Slicers:"+u._selectedFieldName.replace(/\[/g,"").replace(/\]/g,"")+"']").length>0);u._waitingPopup.hide()},0)},_editorTreeDrillPageInfoSuccess:function(i,u){setTimeout(function(){var o,f,e;u._memberDrillPageSettings.currentMemberDrillPage>1?u.element.find(".e-memberDrillPager").find(".e-prevPage, .e-firstPage").removeClass("e-pageDisabled").addClass("e-pageEnabled"):u.element.find(".e-memberDrillPager").find(".e-prevPage, .e-firstPage").removeClass("e-pageEnabled").addClass("e-pageDisabled");u._memberDrillPageSettings.currentMemberDrillPage==parseInt(n.trim(u.element.find(".e-memberDrillPageCount").text().split("/")[1]))?u.element.find(".e-memberDrillPager").find(".e-nextPage, .e-lastPage").removeClass("e-pageEnabled").addClass("e-pageDisabled"):u.element.find(".e-memberDrillPager").find(".e-nextPage, .e-lastPage").removeClass("e-pageDisabled").addClass("e-pageEnabled");u.element.find(".e-memberCurrentDrillPage").val(u._memberDrillPageSettings.currentMemberDrillPage);f=0;i.length>0&&i[0].length>0&&i[0][0].id=="All"&&i[0].splice(0,1);n.each(i,function(n,t){if(t.length==1&&t[0].expanded)f=n;else return!1});e=[];e=u.model.enableMemberEditorSorting&&u._sortType?t.DataManager(t.DataManager(u._editorTreeData).executeLocal(t.Query().where("pid","equal",i[f][0].id).sortBy("name",u._sortType))).executeLocal(t.Query().where("pid","notequal",null||r).page(u._memberDrillPageSettings.currentMemberDrillPage,u.pluginName=="ejPivotSchemaDesigner"?u.model.pivotControl.model.memberEditorPageSize:u.model.memberEditorPageSize)):t.DataManager(t.DataManager(u._editorTreeData).executeLocal(t.Query().where("pid","equal",i[f][0].id))).executeLocal(t.Query().where("pid","notequal",null||r).page(u._memberDrillPageSettings.currentMemberDrillPage,u.pluginName=="ejPivotSchemaDesigner"?u.model.pivotControl.model.memberEditorPageSize:u.model.memberEditorPageSize));u._editorDrillTreePageSettings[i[f][0].id]=n.extend(!0,{},u._memberDrillPageSettings);u.element.find(".e-memberDrillPager").find(".e-nextPageDiv").length>0?(o=t.Pivot._getParentsTreeList(u,i[f][0].id,u._editorTreeData).concat(e),u.pluginName!="ejPivotClient"?t.Pivot._createSearchTreeview(o,u):u._appendTreeViewData(o,u.element.find(".pvtBtn").parent("[data-tag='Slicers:"+u._selectedFieldName.replace(/\[/g,"").replace(/\]/g,"")+"']").length>0)):u.pluginName!="ejPivotClient"?t.Pivot._createSearchTreeview(e,u):u._appendTreeViewData(e,u.element.find(".pvtBtn").parent("[data-tag='Slicers:"+u._selectedFieldName.replace(/\[/g,"").replace(/\]/g,"")+"']").length>0);u._waitingPopup.hide()},0)},_editorTreeSearchPageInfoSuccess:function(i,r){setTimeout(function(){var f=r.pluginName=="ejPivotSchemaDesigner"?r.model.pivotControl.model.memberEditorPageSize:r.model.memberEditorPageSize,u;for(r._memberSearchPageSettings.currentMemberSearchPage>1?r.element.find(".e-memberSearchPager").find(".e-prevPage, .e-firstPage").removeClass("e-pageDisabled").addClass("e-pageEnabled"):r.element.find(".e-memberSearchPager").find(".e-prevPage, .e-firstPage").removeClass("e-pageEnabled").addClass("e-pageDisabled"),r._memberSearchPageSettings.currentMemberSearchPage==parseInt(n.trim(r.element.find(".e-memberSearchPageCount").text().split("/")[1]))?r.element.find(".e-memberSearchPager").find(".e-nextPage, .e-lastPage").removeClass("e-pageEnabled").addClass("e-pageDisabled"):r.element.find(".e-memberSearchPager").find(".e-nextPage, .e-lastPage").removeClass("e-pageDisabled").addClass("e-pageEnabled"),i=t.DataManager(i).executeLocal(t.Query().where(t.Predicate("id","notequal","(All)_0").and("id","notequal","All"))).slice((r._memberSearchPageSettings.currentMemberSearchPage-1)*f,(r._memberSearchPageSettings.currentMemberSearchPage-1)*f+f),u=0;u<i.length;u++)t.isNullOrUndefined(i[u].pid)||(i[u].parentId=i[u].pid);for(u=0;u<i.length;u++)t.Pivot._appendParentNodes(i,i[u],r);r.element.find(".e-memberCurrentSearchPage").val(r._memberSearchPageSettings.currentMemberSearchPage);i.splice(0,0,{id:"SearchFilterSelection",name:r._getLocalizedLabels("AddCurrentSelectionToFilter"),checkedStatus:r._isSelectSearchFilter,tag:"SearchFilterSelection",spriteCssClass:"e-searchfilterselection",uniqueName:"Add current selection to filter"});r.pluginName=="ejPivotClient"&&r.model.analysisMode==t.Pivot.AnalysisMode.Olap&&r.model.operationalMode==t.Pivot.OperationalMode.ServerMode?r._appendTreeViewData(i,r.element.find(".pvtBtn").parent("[data-tag='Slicers:"+r._selectedFieldName.replace(/\[/g,"").replace(/\]/g,"")+"']").length>0):t.Pivot._createSearchTreeview(i,r);r._waitingPopup.hide();r._parentNodeCollection={};r._parentSearchNodeCollection={}},0)},_drillEditorTreeNode:function(i,u,f){var o,e;u._waitingPopup.show();o=i.childNodes;u._isEditorDrillPaging&&!u._isEditorCollapseDrillPaging?u.element.find(".e-memberDrillPager").find(".e-nextPageDiv").length>0&&(u._memberDrillPageSettings={currentMemberDrillPage:1,startDrillPage:0,endDrillPage:0},t.isNullOrUndefined(i.parentNode.id)||(u._editorDrillTreeData[i.parentNode.id]=o.slice(),u._editorDrillTreePageSettings[i.parentNode.id]=n.extend(!0,{},u._memberDrillPageSettings)),this._refreshDrillEditorPager(i,u,f)):u._isEditorCollapseDrillPaging&&(u.element.find(".e-memberDrillPager").find(".e-nextPageDiv").length>0&&(u._isEditorCollapseDrillPaging=!1,u._memberDrillPageSettings=t.isNullOrUndefined(i.parentNode.id)?i.drillPageSettings:i.drillPageSettings[i.parentNode.id]),this._refreshDrillEditorPager(i,u,f));e=[];u._drilledNode=i.parentNode.id;e=t.isNullOrUndefined(i.parentNode.id)?i.parentNode.concat(i.childNodes):this._getParentsTreeList(u,i.parentNode.id,u._editorTreeData).concat(t.DataManager(o).executeLocal(t.Query().where("pid","notequal",null||r).page(u._memberDrillPageSettings.currentMemberDrillPage,f)));u.pluginName=="ejPivotClient"&&u.model.analysisMode==t.Pivot.AnalysisMode.Olap&&u.model.operationalMode==t.Pivot.OperationalMode.ServerMode?u._appendTreeViewData(e,u.element.find(".pvtBtn").parent("[data-tag='Slicers:"+u._selectedFieldName.replace(/\[/g,"").replace(/\]/g,"")+"']").length>0):t.Pivot._createSearchTreeview(e,u);u._waitingPopup.hide()},_refreshDrillEditorPager:function(t,i,r){i.element.find(".e-memberPager,.e-memberSearchPager").css("display","none");i.element.find(".e-memberDrillPager").css("display","block");var f=t.childNodes.length,u=f/r;u!=Math.round(u)&&(u=parseInt(u)+1);i._memberDrillPageSettings.currentMemberDrillPage==u&&i.element.find(".e-memberDrillPager").find(".e-nextPage, .e-lastPage").addClass("disabled");i.element.find(".e-memberDrillPageCount").html("/ "+u);i.element.find(".e-memberCurrentDrillPage").val(i._memberDrillPageSettings.currentMemberDrillPage);i._memberDrillPageSettings.currentMemberDrillPage>1?i.element.find(".e-memberDrillPager").find(".e-prevPage, .e-firstPage").removeClass("e-pageDisabled").addClass("e-pageEnabled"):i.element.find(".e-memberDrillPager").find(".e-prevPage, .e-firstPage").removeClass("e-pageEnabled").addClass("e-pageDisabled");i._memberDrillPageSettings.currentMemberDrillPage==parseInt(n.trim(i.element.find(".e-memberDrillPageCount").text().split("/")[1]))?i.element.find(".e-memberDrillPager").find(".e-nextPage, .e-lastPage").removeClass("e-pageEnabled").addClass("e-pageDisabled"):i.element.find(".e-memberDrillPager").find(".e-nextPage, .e-lastPage").removeClass("e-pageDisabled").addClass("e-pageEnabled");t.childNodes=t.childNodes.slice((i._memberDrillPageSettings.currentMemberDrillPage-1)*r,i._memberDrillPageSettings.currentMemberDrillPage*r)},_getParentsTreeList:function(n,i,r){var u=[],f;do parentNodes=t.DataManager(r).executeLocal(t.Query().where("id","equal",i)),f=!t.isNullOrUndefined(parentNodes)&&parentNodes.length>0&&!t.isNullOrUndefined(parentNodes[0].pid),u.push(new t.Predicate("id","equal",i)),i=f?parentNodes[0].pid:null;while(!t.isNullOrUndefined(i));return t.DataManager(r).executeLocal(t.Query().where(t.Predicate.or(u)))},_onNodeCollapse:function(i){var u=t.isNullOrUndefined(i.olapCtrlObj)?this:i.olapCtrlObj,l=!1,o={memberEditorPageSize:u.pluginName=="ejPivotSchemaDesigner"?u.model.pivotControl.model.memberEditorPageSize:u.model.memberEditorPageSize,enableMemberEditorPaging:u.pluginName=="ejPivotSchemaDesigner"?u.model.pivotControl.model.enableMemberEditorPaging:u.model.enableMemberEditorPaging},c;if(jQuery.each(u._editorTreeData,function(n,r){r.id==i.id?(t.isNullOrUndefined(r.pid)||(i.parentId=r.pid),r.expanded=!1):r.pid==i.id&&(t.isNullOrUndefined(u._editorDrillTreeData[r.id])||(l=!0))}),o.enableMemberEditorPaging&&(l||!t.isNullOrUndefined(u._editorDrillTreeData[i.id])||i.parentId=="")){u.element.find(".searchEditorTreeView").data("ejMaskEdit").clear();u._lastSavedTree=[];u._isEditorDrillPaging=!1;u.element.find(".e-memberPager").length>0?(u.element.find(".e-memberPager").css("display","block"),u.element.find(".e-memberDrillPager,.e-memberSearchPager").css("display","none")):u.element.find(".e-memberDrillPager, .e-memberSearchPager").css("display","none");var f=[],e=i.parentId,s=!1,a=!1,h="";if(!t.isNullOrUndefined(e)&&e!=""){do parentNodes=t.DataManager(u._editorTreeData).executeLocal(t.Query().where("id","equal",e)),s=s||o.memberEditorPageSize<=t.DataManager(u._editorTreeData).executeLocal(t.Query().where("pid","equal",e)).length?!0:!1,f.push(new t.Predicate(s?"id":"pid","equal",e)),!a&&s&&o.memberEditorPageSize<t.DataManager(u._editorTreeData).executeLocal(t.Query().where("pid","equal",e)).length&&(a=!0,h=e),e=parentNodes[0].pid;while(!t.isNullOrUndefined(e));s?f.push(new t.Predicate("id","equal",i.parentId)):f.push(new t.Predicate("pid","equal",null||r))}s?(t.Pivot._makeAncestorsExpandable(u,i.parentId),u._isEditorCollapseDrillPaging=!0,c={drillPageSettings:t.isNullOrUndefined(u._editorDrillTreePageSettings[h])&&u.pluginName=="ejPivotSchemaDesigner"?u.model.pivotControl._editorDrillTreePageSettings[h]:u._editorDrillTreePageSettings[h],parentNode:t.DataManager(n.extend(!0,[],u._editorTreeData)).executeLocal(t.Query().where(t.Predicate.or(f))),childNodes:t.DataManager(n.extend(!0,[],u._editorTreeData)).executeLocal(t.Query().where("pid","equal",h))},c.parentNode=n.grep(c.parentNode,function(t){var i=!1;return n.each(c.childNodes,function(n,r){r.id==t.id&&(r.expanded=!0,i=!0)}),!i}),t.Pivot._drillEditorTreeNode(c,u,o.memberEditorPageSize)):(u.model.enableMemberEditorSorting&&(u._editorTreeData=t.DataManager(u._editorTreeData).executeLocal(t.Query().sortBy("name",u._sortType))),f.length==0&&f.push(new t.Predicate("pid","equal",null||r)),u.element.find(".e-memberPager").length>0?u.pluginName!="ejPivotClient"?t.Pivot._createSearchTreeview(t.DataManager(u._editorTreeData).executeLocal(t.Query().where(t.Predicate.or(f)).page(u._memberPageSettings.currentMemeberPage,o.memberEditorPageSize)),u):u._appendTreeViewData(t.DataManager(u._editorTreeData).executeLocal(t.Query().where(t.Predicate.or(f)).page(u._memberPageSettings.currentMemeberPage,o.memberEditorPageSize)),u.element.find(".pvtBtn").parent("[data-tag='Slicers:"+u._selectedFieldName.replace(/\[/g,"").replace(/\]/g,"")+"']").length>0):u.pluginName!="ejPivotClient"?t.Pivot._createSearchTreeview(t.DataManager(u._editorTreeData).executeLocal(t.Query().where(t.Predicate.or(f))),u):u._appendTreeViewData(t.DataManager(u._editorTreeData).executeLocal(t.Query().where(t.Predicate.or(f))),u.element.find(".pvtBtn").parent("[data-tag='Slicers:"+u._selectedFieldName.replace(/\[/g,"").replace(/\]/g,"")+"']").length>0))}},_searchEditorTreeNodes:function(i,u){if(t.isNullOrUndefined(window.event)?i.which!=0&&i.which!=13:window.event.which!=0&&window.event.which!=13){t.isNullOrUndefined(u._waitingPopup.element)||u._waitingPopup.show();var f=this;setTimeout(function(){var o={memberEditorPageSize:u.pluginName=="ejPivotSchemaDesigner"?u.model.pivotControl.model.memberEditorPageSize:u.model.memberEditorPageSize,enableMemberEditorPaging:u.pluginName=="ejPivotSchemaDesigner"?u.model.pivotControl.model.enableMemberEditorPaging:u.model.enableMemberEditorPaging},c="",e,a,s,l,v,y;if(u.pluginName!="ejPivotClient"||t.isNullOrUndefined(u._pivotSchemaDesigner)){if(t.isNullOrUndefined(u.element.find(".searchEditorTreeView"))||u.element.find(".searchEditorTreeView").length==0)return!1;c=t.isNullOrUndefined(n.trim(u.element.find(".searchEditorTreeView").val()))?"":n.trim(u.element.find(".searchEditorTreeView").val()).toLowerCase()}else{if(t.isNullOrUndefined(u._pivotSchemaDesigner.element.find(".searchEditorTreeView"))||u._pivotSchemaDesigner.element.find(".searchEditorTreeView").length==0)return!1;c=t.isNullOrUndefined(n.trim(u._pivotSchemaDesigner.element.find(".searchEditorTreeView").val()))?"":n.trim(u._pivotSchemaDesigner.element.find(".searchEditorTreeView").val()).toLowerCase()}if(c!=""){var h=jQuery.extend(!0,[],t.DataManager(u._editorTreeData).executeLocal(t.Query().where("name","contains",c,!0).sortBy("name",t.isNullOrUndefined(u._sortType)?"":u._sortType))),p=n.extend(!0,[],h.slice(0,o.memberEditorPageSize+1)),i=o.enableMemberEditorPaging?p:h;for(e=0;e<i.length;e++)t.isNullOrUndefined(i[e].pid)||(i[e].parentId=i[e].pid);for(e=0;e<i.length;e++)f._appendParentNodes(i,i[e],u);u._isSearchApplied=!0;h.length>0?(u._isSelectSearchFilter=u.model.enableCheckStateOnSearch,o.enableMemberEditorPaging&&(u.element.find(".e-memberSearchPager").find(".e-nextPageDiv").length>0||u.element.find(".e-memberDrillPager").find(".e-nextPageDiv").length>0)&&(u._memberSearchPageSettings.currentMemberSearchPage=1,u._memberSearchPageSettings.startSearchPage=0,u._memberSearchPageSettings.endSearchPage=0,u.element.find(".e-memberPager,.e-memberDrillPager").css("display","none"),u._editorSearchTreeData=h.slice(),a=t.DataManager(h).executeLocal(t.Query().where(t.Predicate("id","notequal","(All)_0").and("id","notequal","All"))).length,s=a/o.memberEditorPageSize,s!=Math.round(s)&&(s=parseInt(s)+1),s>1&&u.element.find(".e-memberSearchPager").css("display","block"),u._memberSearchPageSettings.currentMemberSearchPage==s&&u.element.find(".e-memberSearchPager").find(".e-nextPage, .e-lastPage").addClass("disabled"),u.element.find(".e-memberSearchPageCount").html("/ "+s),u.element.find(".e-memberCurrentSearchPage").val(u._memberSearchPageSettings.currentMemberSearchPage),u._memberSearchPageSettings.currentMemberSearchPage>1?u.element.find(".e-memberSearchPager").find(".e-prevPage, .e-firstPage").removeClass("e-pageDisabled").addClass("e-pageEnabled"):u.element.find(".e-memberSearchPager").find(".e-prevPage, .e-firstPage").removeClass("e-pageEnabled").addClass("e-pageDisabled"),u._memberSearchPageSettings.currentMemberSearchPage==parseInt(n.trim(u.element.find(".e-memberSearchPageCount").text().split("/")[1]))?u.element.find(".e-memberSearchPager").find(".e-nextPage, .e-lastPage").removeClass("e-pageEnabled").addClass("e-pageDisabled"):u.element.find(".e-memberSearchPager").find(".e-nextPage, .e-lastPage").removeClass("e-pageDisabled").addClass("e-pageEnabled"))):(u.element.find(".e-memberPager,.e-memberSearchPager,.e-memberDrillPager").css("display","none"),u.element.find(".e-dialogFooter").css("margin-top","5px"));i.length>0&&(i[0].id=="All"&&i.splice(0,1),i.splice(0,0,{id:"SearchFilterSelection",name:u._getLocalizedLabels("AddCurrentSelectionToFilter"),checkedStatus:u._isSelectSearchFilter,tag:"SearchFilterSelection",spriteCssClass:"e-searchfilterselection",uniqueName:"Add current selection to filter"}));u.pluginName=="ejPivotClient"&&u.model.analysisMode==t.Pivot.AnalysisMode.Olap&&u.model.operationalMode==t.Pivot.OperationalMode.ServerMode?u._appendTreeViewData(i,u.element.find(".pvtBtn").parent("[data-tag='Slicers:"+u._selectedFieldName.replace(/\[/g,"").replace(/\]/g,"")+"']").length>0):f._createSearchTreeview(i,u)}else u._isSearchApplied=!1,u._isSelectSearchFilter=u.model.enableCheckStateOnSearch,l=!t.isNullOrUndefined(u._lastSavedTree)&&n.grep(u._lastSavedTree,function(n){return n.pid}).length>0?!0:!1,o.enableMemberEditorPaging&&u.element.find(".e-memberSearchPager").find(".e-nextPageDiv").length>0&&(u._editorSearchTreeData=[],u.element.find(".e-memberPageCount").text()!=""&&u.element.find(".e-memberPager").css("display","block"),u.element.find(".e-memberSearchPager").css("display","none")),l&&u.element.find(".e-memberDrillPageCount").text()!=""&&(u.element.find(".e-memberDrillPager").css("display","block"),u.element.find(".e-memberPager").css("display","none")),u.pluginName=="ejPivotClient"&&u.model.analysisMode==t.Pivot.AnalysisMode.Olap&&u.model.operationalMode==t.Pivot.OperationalMode.ServerMode?u._appendTreeViewData(o.enableMemberEditorPaging?l?t.DataManager(u._lastSavedTree).executeLocal(t.Query().sortBy("name",t.isNullOrUndefined(u._sortType)?"":u._sortType)):t.DataManager(u._editorTreeData).executeLocal(t.Query().where("pid","equal",null||r).page(u._memberPageSettings.currentMemeberPage,o.memberEditorPageSize).sortBy("name",t.isNullOrUndefined(u._sortType)?"":u._sortType)):u._editorTreeData,u.element.find(".pvtBtn").parent("[data-tag='Slicers:"+u._selectedFieldName.replace(/\[/g,"").replace(/\]/g,"")+"']").length>0):f._createSearchTreeview(o.enableMemberEditorPaging?l?t.DataManager(u._lastSavedTree).executeLocal(t.Query().sortBy("name",t.isNullOrUndefined(u._sortType)?"":u._sortType)):t.DataManager(u._editorTreeData).executeLocal(t.Query().where(t.Predicate("pid","equal",null).and("id","notequal","All").and("id","notequal","(All)_0")).page(u._memberPageSettings.currentMemeberPage,o.memberEditorPageSize).sortBy("name",t.isNullOrUndefined(u._sortType)?"":u._sortType)):u._editorTreeData,u);v={id:"SearchFilterSelection",type:u._isSelectSearchFilter?"nodeCheck":"nodeUncheck"};y=u.element.find(".e-editorTreeView");t.Pivot._updateSearchFilterSelection(v,y,u);u._waitingPopup.hide();u._parentNodeCollection={};u._parentSearchNodeCollection={}},0)}},_createSearchTreeview:function(i,r){var c=r.pluginName=="ejPivotSchemaDesigner"?r.model.pivotControl.model.enableMemberEditorPaging:r.model.enableMemberEditorPaging,e=r.pluginName=="ejPivotSchemaDesigner"?r.model.pivotControl.model.enableAdvancedFilter:r.model.enableAdvancedFilter,o=r.pluginName=="ejPivotSchemaDesigner"?r.model.pivotControl.model.enableMemberEditorSorting:r.model.enableMemberEditorSorting,s=r.element.find(".e-pvtBtn").parent("[data-tag='Slicers:"+(r._selectedFieldName||r._dialogHead)+"']").length>0,h={analysisMode:r.pluginName=="ejPivotSchemaDesigner"?r.model.pivotControl.model.analysisMode:r.model.analysisMode,operationalMode:r.pluginName=="ejPivotSchemaDesigner"?r.model.pivotControl.model.operationalMode:r.model.operationalMode},f,u;for(!r._isSearchApplied&&r._editorSearchTreeData.length==0&&i.length>0&&(r._lastSavedTree=i),i.length>0&&(i=t.DataManager(i).executeLocal(t.Query().where(t.Predicate("id","notequal","All").and("id","notequal","(All)_0"))),i.splice(0,0,{id:"All",name:e&&!s?"(Select All)":h.operationalMode==t.Pivot.OperationalMode.ServerMode?"(All)":"All",checkedStatus:r._isAllMemberChecked,_isAllMemberChecked:r._isAllMemberChecked})),r.element.find(".e-editorTreeView").ejTreeView({showCheckbox:!0,loadOnDemand:!0,enableRTL:r.model.enableRTL,beforeDelete:function(n){if(!t.isNullOrUndefined(n.event)&&n.event.type=="keydown"&&n.event.originalEvent.key.toLowerCase()=="delete")return!1},height:o?"200px":"245px",fields:{id:"id",text:"name",isChecked:"checkedStatus",parentId:"pid",expanded:"expanded",hasChild:"hasChildren",dataSource:t.Pivot._showEditorLinkPanel(i,r,r.pluginName=="ejPivotSchemaDesigner"?r.model.pivotControl:r)}}),f=r.element.find(".e-editorTreeView").find("li"),u=0;u<f.length;u++)t.isNullOrUndefined(n(f[u]).attr("id"))||n(f[u]).attr("data-tag",t.DataManager(i).executeLocal(t.Query().where("id","equal",n(f[u]).attr("id")))[0].tag);r._memberTreeObj=r.element.find(".e-editorTreeView").data("ejTreeView");r._memberTreeObj.model.nodeCheck=t.proxy(r._nodeCheckChanges,r);r._memberTreeObj.model.nodeUncheck=t.proxy(r._nodeCheckChanges,r);r.pluginName=="ejPivotGrid"?r.model.operationalMode==t.Pivot.OperationalMode.ClientMode?r._memberTreeObj.model.beforeExpand=t.proxy(r._beforeNodeExpand,r):r._memberTreeObj.model.nodeClick=t.proxy(r._nodeExpand,r):(r.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?r._memberTreeObj.model.beforeExpand=t.proxy(r._beforeNodeExpand,r):r._memberTreeObj.model.nodeClick=t.proxy(r._nodeExpand,r),r._memberTreeObj.model.beforeCollapse=t.proxy(t.Pivot._onNodeCollapse,r));t.isNullOrUndefined(r.element.find(".e-dialog .e-text:visible").first())||(r.element.find(".e-dialog .e-text:visible").length>0?(r.element.find(".e-dialog .e-text:visible").first().attr("tabindex","-1").addClass("e-hoverCell"),r.element.find(".e-dialog button").removeClass("e-hoverCell")):r.element.find(".e-dialog button:visible").first().attr("tabindex","-1").addClass("e-hoverCell"));r._memberTreeObj.element.find(".e-plus").length==0&&r._memberTreeObj.element.find(".e-minus").length==0&&r._memberTreeObj.element.find(".e-item").css("padding","0px");r.model.enableMemberEditorSorting&&this._separateAllMember(r)},_separateAllMember:function(n){var u=n.element.hasClass("e-pivotclient")&&n.model.operationalMode=="servermode"&&n.model.analysisMode=="olap",f=n.pluginName=="ejPivotSchemaDesigner"?n.model.pivotControl.model.enableAdvancedFilter:n.model.enableAdvancedFilter,i=n.pluginName=="ejPivotSchemaDesigner"?n.model.pivotControl.model.enableRTL:n.model.enableRTL,r=n.pluginName=="ejPivotSchemaDesigner"?n.model.pivotControl.model.analysisMode:n.model.analysisMode,o=u?"":t.buildTag("div.e-checkAllBox",t.buildTag("input#"+n._id+"_allelement.allMember","",{float:i?"right":"left"},{name:"row",type:"checkbox",tabindex:0})[0].outerHTML+" "+t.buildTag("label.e-allElementLabel",f?"(Select All)":"All",{"margin-left":"5px"})[0].outerHTML,{width:"100px",float:i?"right":"left"})[0].outerHTML,e=t.buildTag("div.memberSortDiv",t.buildTag("div.e-icon e-memberAscendingIcon",{margin:"5px"})[0].outerHTML+t.buildTag("div.e-icon e-memberDescendingIcon")[0].outerHTML,{height:"35px","margin-left":"5px",float:i?"left":"right"})[0].outerHTML,s=t.buildTag("div.memberSortingDiv",o+e,{height:"20px",margin:n.pluginName=="ejPivotSchemaDesigner"?i?r==t.Pivot.AnalysisMode.Pivot?"10px 15px 0px 21px":"10px 31px 0px 12px":r==t.Pivot.AnalysisMode.Pivot?"10px 0px 0px 8px":"10px 0px 0px 20px":i?r==t.Pivot.AnalysisMode.Pivot?"10px 15px 0px 20px":"10px 28px 0px 20px":r==t.Pivot.AnalysisMode.Pivot?"10px 0px 0px 20px":"10px 0px 0px 29px"});f&&n.element.find(".e-editorTreeView").css("margin-top","10px");n.element.find(".memberSortingDiv, .e-separateDiv").remove();r==t.Pivot.AnalysisMode.Pivot&&n.element.find(".e-editorTreeView").addClass("e-treeViewUl");u||n.element.find(".e-editorTreeView").before(s);(n.element.find(".e-editorTreeView li:first").attr("id")=="All"||n.element.find(".e-editorTreeView li:first").attr("id")=="(All)_0")&&n.element.find(".e-editorTreeView li:first").css("display","none");u?(n.element.find(".memberSortDiv, .e-separateDiv").remove(),n.element.find(".e-checkOptions").after(t.buildTag("div.e-separateDiv",{margin:"0px 5px"})[0].outerHTML),n.element.find(".e-checkOptions").append(e),f&&n.element.find(".e-editorTreeView").height(235)):(n.element.find(".memberSortingDiv").after(t.buildTag("div.e-separateDiv",{})[0].outerHTML),n.element.find(".allMember").ejCheckBox({size:"small",width:"40px",checked:n.element.find(".e-dialogOKBtn").attr("disabled")=="disabled"?!1:!0,change:t.proxy(n._nodeCheckChanges,n)}));n._sortType=="ascending"?n.element.find(".e-memberAscendingIcon").addClass("e-selectedSort"):n._sortType=="descending"&&n.element.find(".e-memberDescendingIcon").addClass("e-selectedSort")},_checkChanges:function(n){var t=this.pluginName=="ejPivotSchemaDesigner"?this.model.pivotControl:this;n.isChecked?t._nodeCheckChanges(n):n.isChecked||t._nodeUnCheckChanges(n)},_fetchMemberPageSuccess:function(i){var u,f=this.pluginName=="ejPivotSchemaDesigner"?this.model.pivotControl:this;i[0]!=r&&i.length>0?(u=i[0].Value,i[1]!=null&&i[1]!=r&&(this.model.customObject=i[1].Value)):i.d!=r&&i.d.length>0?(u=i.d[0].Value,i.d[1]!=null&&i.d[1]!=r&&(this.model.customObject=i.d[1].Value)):i!=r&&i.length>0?(u=i.EditorTreeInfo,i!=null&&i.customObject!=r&&(this.model.customObject=i.customObject)):i!=r&&(u=i.EditorTreeInfo,i!=null&&i.customObject!=r&&(this.model.customObject=i.customObject));this._memberPageSettings.endPage=this.pluginName=="ejPivotSchemaDesigner"?this.model.pivotControl.model.memberEditorPageSize:this.model.memberEditorPageSize;this._memberPageSettings.startPage=0;this._memberPageSettings.currentMemeberPage=1;filteredData=JSON.parse(u);this._editorTreeData=n.extend(!0,[],filteredData);f.model.enableAdvancedFilter&&(this._editorTreeData=n.map(this._editorTreeData,function(n){if(t.isNullOrUndefined(n.levels))return n}));(this.pluginName=="ejPivotSchemaDesigner"&&this.model.pivotControl&&this.model.pivotControl.model.enableAdvancedFilter&&this.model.pivotControl.model.analysisMode==t.PivotGrid.AnalysisMode.Olap&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode||this.pluginName=="ejPivotGrid"&&this.model.enableAdvancedFilter&&this.model.analysisMode==t.PivotGrid.AnalysisMode.Olap&&this.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode)&&(this._memberPagingAvdData=filteredData.splice(filteredData.length-1,1));this._memberCount=t.DataManager(this._editorTreeData).executeLocal(t.Query().where(t.Predicate("pid","equal",null).and("id","notequal","All").and("id","notequal","(All)_0"))).length;filteredData=t.DataManager(this._editorTreeData).executeLocal(t.Query().where("pid","equal",null||r).page(this._memberPageSettings.currentMemeberPage,this.pluginName=="ejPivotSchemaDesigner"?this.model.pivotControl.model.memberEditorPageSize+1:this.model.memberEditorPageSize+1));this._fetchMemberSuccess({EditorTreeInfo:JSON.stringify(filteredData)})},_memberPageNodeUncheck:function(i,r){var f="",e=null,o=r.pluginName=="ejPivotSchemaDesigner"?r.model.pivotControl.model.enableMemberEditorPaging:r.model.enableMemberEditorPaging,u;n(i.currentElement).parent().parent().hasClass("e-editorTreeView")&&!r._isSearchApplied&&o?(f=t.DataManager(r._editorTreeData).executeLocal(t.Query().page(r._memberPageSettings.currentMemeberPage,r.pluginName=="ejPivotSchemaDesigner"?r.model.pivotControl.model.memberEditorPageSize+1:r.model.memberEditorPageSize+1).where("id","equal",i.currentElement.attr("id")))[0],t.isNullOrUndefined(r._editorSearchTreeData)||(e=t.DataManager(r._editorSearchTreeData).executeLocal(t.Query().page(r._memberPageSettings.currentMemeberPage,r.pluginName=="ejPivotSchemaDesigner"?r.model.pivotControl.model.memberEditorPageSize+1:r.model.memberEditorPageSize+1).where("id","equal",i.currentElement.attr("id")))[0])):(f=t.DataManager(r._editorTreeData).executeLocal(t.Query().where("id","equal",i.currentElement.attr("id")))[0],t.isNullOrUndefined(r._editorSearchTreeData)||(e=t.DataManager(r._editorSearchTreeData).executeLocal(t.Query().where("id","equal",i.currentElement.attr("id")))[0]));t.isNullOrUndefined(f)||(f.checkedStatus=!1,t.isNullOrUndefined(f.pid)||(u=t.DataManager(r._editorTreeData).executeLocal(t.Query().where("pid","equal",f.pid).where("checkedStatus","equal",!0)),t.isNullOrUndefined(u)||u.length!=0||t.Pivot._unSelectParentTreeNode(f,r)),u=t.DataManager(r._editorTreeData).executeLocal(t.Query().where("pid","equal",f.id)),!t.isNullOrUndefined(u)&&u.length>0&&t.Pivot._unSelectChildTreeNode(f,r));t.isNullOrUndefined(e)||(e.checkedStatus=!1,t.isNullOrUndefined(e.pid)||(u=t.DataManager(r._editorSearchTreeData).executeLocal(t.Query().where("pid","equal",e.pid).where("checkedStatus","equal",!0)),t.isNullOrUndefined(u)||u.length!=0||t.Pivot._unSelectParentTreeNode(e,r)),u=t.DataManager(r._editorSearchTreeData).executeLocal(t.Query().where("pid","equal",f.id)),!t.isNullOrUndefined(u)&&u.length>0&&t.Pivot._unSelectChildTreeNode(e,r))},_memberPageNodeCheck:function(i,r){var e="",o=null,s=r.pluginName=="ejPivotSchemaDesigner"?r.model.pivotControl.model.enableMemberEditorPaging:r.model.enableMemberEditorPaging,u,f;if(n(i.currentElement).parent().parent().hasClass("e-editorTreeView")&&!r._isSearchApplied&&s?(e=t.DataManager(r._editorTreeData).executeLocal(t.Query().page(r._memberPageSettings.currentMemeberPage,r.pluginName=="ejPivotSchemaDesigner"?r.model.pivotControl.model.memberEditorPageSize+1:r.model.memberEditorPageSize+1).where("id","equal",i.currentElement.attr("id")))[0],t.isNullOrUndefined(r._editorSearchTreeData)||(o=t.DataManager(r._editorSearchTreeData).executeLocal(t.Query().page(r._memberPageSettings.currentMemeberPage,r.pluginName=="ejPivotSchemaDesigner"?r.model.pivotControl.model.memberEditorPageSize+1:r.model.memberEditorPageSize+1).where("id","equal",i.currentElement.attr("id")))[0])):(e=t.DataManager(r._editorTreeData).executeLocal(t.Query().where("id","equal",i.currentElement.attr("id")))[0],t.isNullOrUndefined(r._editorSearchTreeData)||(o=t.DataManager(r._editorSearchTreeData).executeLocal(t.Query().where("id","equal",i.currentElement.attr("id")))[0])),!t.isNullOrUndefined(e)&&(e.checkedStatus=!0,t.isNullOrUndefined(e.pid)||t.Pivot._selectParentTreeNode(e,r),!t.isNullOrUndefined(e.id)&&(u=t.DataManager(r._editorTreeData).executeLocal(t.Query().where("pid","equal",e.id)),!t.isNullOrUndefined(u)&&u.length>0)))for(f=0;f<u.length;f++)u[f].checkedStatus=!0,(u[f].expanded||u[f].isChildMerged)&&t.Pivot._selectChildTreeNode(u[f],r);if(!t.isNullOrUndefined(o)&&(o.checkedStatus=!0,t.isNullOrUndefined(o.pid)||t.Pivot._selectParentTreeNode(o,r),!t.isNullOrUndefined(o.id)&&(u=t.DataManager(r._editorSearchTreeData).executeLocal(t.Query().where("pid","equal",o.id)),!t.isNullOrUndefined(u)&&u.length>0)))for(f=0;f<u.length;f++)u[f].checkedStatus=!0,t.Pivot._selectChildTreeNode(u[f],r)},_appendParentNodes:function(n,i,r){if(!t.isNullOrUndefined(i.pid)){var u,f;t.isNullOrUndefined(r._parentNodeCollection[i.pid])?(u=jQuery.extend(!0,[],t.DataManager(r._editorTreeData).executeLocal(t.Query().where("id","equal",i.pid))),r._parentNodeCollection[i.pid]=u):u=r._parentNodeCollection[i.pid];!t.isNullOrUndefined(u)&&u.length>0&&(t.isNullOrUndefined(r._parentSearchNodeCollection[u[0].id])?(f=jQuery.extend(!0,[],t.DataManager(n).executeLocal(t.Query().where("id","equal",u[0].id))),r._parentSearchNodeCollection[u[0].id]=u):f=r._parentSearchNodeCollection[u[0].id]);t.isNullOrUndefined(f)||(f.length==0?(u[0].expanded=!0,n.push(u[0]),t.isNullOrUndefined(u[0].pid)||this._appendParentNodes(n,u[0],r)):f[0].expanded=!0)}},_selectParentTreeNode:function(n,i){var r=t.DataManager(i._editorTreeData).executeLocal(t.Query().where("id","equal",n.pid));!t.isNullOrUndefined(r)&&r.length>0&&(r[0].checkedStatus=!0,this._selectParentTreeNode(r[0],i))},_selectChildTreeNode:function(n,i){var r=t.DataManager(i._editorTreeData).executeLocal(t.Query().where("pid","equal",n.id)),u;if(!t.isNullOrUndefined(r)&&r.length>0)for(u=0;u<r.length;u++)r[u].checkedStatus=!0,(r[u].expanded||r[u].isChildMerged)&&this._selectChildTreeNode(r[u],i)},_unSelectChildTreeNode:function(n,i){var r=t.DataManager(i._editorTreeData).executeLocal(t.Query().where("pid","equal",n.id)),u;if(!t.isNullOrUndefined(r)&&r.length>0)for(u=0;u<r.length;u++)r[u].checkedStatus=!1,(r[u].expanded||r[u].isChildMerged)&&this._unSelectChildTreeNode(r[u],i)},_unSelectParentTreeNode:function(n,i){var r=t.DataManager(i._editorTreeData).executeLocal(t.Query().where("id","equal",n.pid));!t.isNullOrUndefined(r)&&r.length>0&&(r[0].checkedStatus=!1,t.DataManager(i._editorTreeData).executeLocal(t.Query().where("pid","equal",r[0].pid).where("checkedStatus","equal",!0)).length==0&&this._unSelectParentTreeNode(r[0],i))},_getSelectedTreeState:function(n,i){var f,o,h,s,e,u;if(n){for(e=[],f=t.DataManager(i._editorTreeData).executeLocal(t.Query().where("pid","equal",null||r)),u=0;u<f.length;u++){if(o=t.DataManager(i._editorTreeData).executeLocal(t.Query().where("pid","equal",f[u].id)),h={caption:f[u].name,parentId:t.isNullOrUndefined(f[u].pid)?"None":f[u].pid,id:f[u].id,checked:f[u].checkedStatus,expanded:o.length>0?!0:!1,childNodes:[],tag:f[u].tag},o.length>0)for(s=0;s<o.length;s++)h.childNodes.push(this._getEditorSlicerInfo(o[s],i));e.push(h)}return JSON.stringify(e)}for(e="",u=0;u<i._editorTreeData.length;u++)i._editorTreeData[u].checkedStatus==!0&&(e+="::"+i._editorTreeData[u].id+"||"+i._editorTreeData[u].tag);return e},_getUnSelectedTreeState:function(n){for(var i="",t=0;t<n._editorTreeData.length;t++)n._editorTreeData[t].checkedStatus==!1&&(i+="::"+n._editorTreeData[t].id+"||"+n._editorTreeData[t].tag);return i},_getEditorSlicerInfo:function(n,i){var r=t.DataManager(i._editorTreeData).executeLocal(t.Query().where("pid","equal",n.id)),f={caption:n.name,parentId:t.isNullOrUndefined(n.pid)?"None":n.pid,id:n.id,checked:n.checkedStatus,expanded:r.length>0?!0:!1,childNodes:[],tag:n.tag},u;if(r.length>0)for(u=0;u<r.length;u++)f.childNodes.push(this._getEditorSlicerInfo(r[u],i));return f},_editorLinkPanelClick:function(){this.element.find(".e-dialog").hide();t.Pivot._createErrorDialog(this._getLocalizedLabels("EditorLinkPanelAlert").split("1000").join(this.model.maxNodeLimitInMemberEditor),this._getLocalizedLabels("Warning"),this)},_showEditorLinkPanel:function(i,r,u){var f,s;if(!u.model.enableMemberEditorPaging&&(u.model.analysisMode!=t.Pivot.AnalysisMode.Pivot||r._isSearchApplied||(r._editorTreeData=i),i.length>u.model.maxNodeLimitInMemberEditor+(r._isSearchApplied?1:0))){var h=[],e={},o={},c=!1;for(f=0;f<i.length;f++)t.isNullOrUndefined(e[i[f].pid])?(t.isNullOrUndefined(i[f].pid)||(s=n.grep(i,function(n){if(n.id==i[f].pid)return n}),s.length>0&&(o[i[f].pid]=s[0].expanded?!0:!1)),e[i[f].pid]=1):e[i[f].pid]++,e[i[f].pid]<=u.model.maxNodeLimitInMemberEditor+(r._isSearchApplied&&t.isNullOrUndefined(i[f].pid)?1:0)?h.push(i[f]):(t.isNullOrUndefined(o[i[f].pid])||o[i[f].pid])&&(c=!0);i=h;c&&r.element.find("div.e-linkOuterPanel").css("display","block")}return i},createErrorDialog:function(i){var r,u;t.Pivot.openPreventPanel(i);i.element.find(".e-errorDialog").length==0?(u=t.buildTag("div.e-errorDialog#"+this._id+"_ErrorDialog",t.buildTag("div.warningImg")[0].outerHTML+t.buildTag("div.warningContent",i._getLocalizedLabels("AlertMsg"))[0].outerHTML+t.buildTag("div",t.buildTag("button#"+this._id+"_ErrOKBtn.errOKBtn","OK")[0].outerHTML)[0].outerHTML).attr("title",i._getLocalizedLabels("Warning"))[0].outerHTML,i.element.append(u),i.element.find(".e-errorDialog").ejDialog({target:"#"+i._id,enableResize:!1,enableRTL:i.model.enableRTL,width:"400px"}),r=i.element.find(".e-errorDialog").data("ejDialog"),n("#"+r._id+"_wrapper").css({left:"50%",top:"50%"}),i.element.find(".errOKBtn").ejButton({type:t.ButtonType.Button,click:t.proxy(t.Pivot.errOKBtnClick,i)}),i.element.find(".e-dialog .e-close").attr("title",i._getLocalizedLabels("Close"))):(r=i.element.find(".e-errorDialog").data("ejDialog"),r.open())},errOKBtnClick:function(){this.element.find("#preventDiv").remove();var n=this.element.find(".e-errorDialog").data("ejDialog");n._ejDialog.find("div.e-dialog-icon").trigger("click")},doAjaxPost:function(i,u,f,e,o,s){var h,c,l,a=!0;f.XMLA==r?(h="application/json; charset=utf-8",c="json",l=n.proxy(e,this)):(h="text/xml",c="xml",f=f.XMLA,l=n.proxy(e,t.olap.base,s),a=t.browserInfo().name=="msie"&&t.browserInfo().version<=9?!1:!t.isNullOrUndefined(s)&&s.action!="loadFieldElements"?!0:!1);n.ajax({type:i,url:u,contentType:h,async:a,dataType:c,data:f,success:l,complete:t.proxy(function(t){n.proxy(t,this);var i={action:this._currentAction,customObject:"",element:this.element}},this),error:t.proxy(function(n){typeof this._ogridWaitingPopup!="undefined"&&this._ogridWaitingPopup!=null&&this._ogridWaitingPopup.hide();typeof oclientWaitingPopup!="undefined"&&oclientWaitingPopup!=null&&oclientWaitingPopup.hide();var t={action:this._drillAction!=""?this._drillAction:"initialize",customObject:"",element:this.element,Message:n};this._trigger("renderFailure",t);this.renderControlFromJSON("");this._dataModel=="XMLA"&&this._createErrorDialog(n.statusText,this._getLocalizedLabels("Error"));n.statusText},this)})},getCubeList:function(t,i){for(var f,u=[],r=0;r<n(i).find("row").length;r++)f=n(n(i).find("row")[r]),u.push({name:n(f).children("CUBE_NAME").text()});t.pvtCtrldObj.setCubeList(u)},generateTreeViewData:function(n){var t={catalog:n.model.pivotControl.model.dataSource.catalog,cube:n.model.pivotControl.model.dataSource.cube,url:n.model.pivotControl.model.dataSource.data,request:"MDSCHEMA_DIMENSIONS"};this._getTreeData(t,this.loadDimensionElements,{schemaData:n,action:"loadFieldElements"})},loadDimensionElements:function(i,u){var h,f=i.schemaData.model.pivotControl,c={},v=t.olap.base._getConnectionInfo(f.model.dataSource.data),o={},a=f.model.dataSource.providerName==t.olap.Providers.Mondrian,s;for(o={catalog:f.model.dataSource.catalog,cube:f.model.dataSource.cube,url:f.model.dataSource.data,request:"MDSCHEMA_DIMENSIONS"},f.schemaTreeView=[],f.reportItemNames=[],s=0;s<n(u).find("row").length;s++){var l=n(n(u).find("row")[s]),e=l.find("DIMENSION_UNIQUE_NAME").text(),h=l.find("DIMENSION_CAPTION").text();e.toLowerCase().indexOf("[measure")>=0?c={hasChildren:!0,isSelected:!1,id:e,name:h,spriteCssClass:e.toLowerCase()=="[measures]"?"e-folderCDB e-icon":"e-dimensionCDB e-icon",tag:e}:!n(n(u).find("row")[0]).find("HIERARCHY_CAPTION").length>0&&f.schemaTreeView.push({hasChildren:!0,isSelected:!1,id:a?e+"~#^Dim":e,name:h,spriteCssClass:"e-dimensionCDB e-icon",tag:e,defaultHierarchy:n(n(u).find("row")[s]).children("DEFAULT_HIERARCHY").text()})}f.schemaTreeView.splice(0,0,c);!f.model.enableDrillThrough||i.schemaData!=r&&i.schemaData.model.olap.showNamedSets?(o.request="MDSCHEMA_SETS",t.Pivot._getTreeData(o,t.Pivot.loadNamedSetElements,i)):(o.request="MDSCHEMA_HIERARCHIES",(t.isNullOrUndefined(f._fieldData)||!t.isNullOrUndefined(f._fieldData)&&t.isNullOrUndefined(f._fieldData.hierarchy))&&this._getFieldItemsInfo(f),f._fieldData.hierarchySuccess==r?t.Pivot._getTreeData(o,f.loadHierarchyElements,i):t.Pivot.loadHierarchyElements(i,f._fieldData.hierarchySuccess))},loadNamedSetElements:function(i,u){var e=i.schemaData.model.pivotControl,c={catalog:e.model.dataSource.catalog,cube:e.model.dataSource.cube,url:e.model.dataSource.data,request:"MDSCHEMA_HIERARCHIES"},u=e.model.dataSource,h=[],o,s,f;for(o=n.map(u.rows,function(n){if(n.fieldName!=r)return n.fieldName}),n.merge(o,n.map(u.columns,function(n){if(n.fieldName!=r)return n.fieldName})),n.merge(o,n.map(u.filters,function(n){if(n.fieldName!=r)return n.fieldName})),s=0;s<n(u).find("row").length;s++)f=n(n(u).find("row")[s]),n.inArray(f.find("DIMENSIONS").text().split(".")[0],h)>=0||(e.schemaTreeView.push({hasChildren:!0,isSelected:!1,pid:f.find("DIMENSIONS").text().split(".")[0],id:f.find("SET_DISPLAY_FOLDER").text()+"_"+f.find("DIMENSIONS").text().split(".")[0],name:f.find("SET_DISPLAY_FOLDER").text(),spriteCssClass:"e-folderCDB e-icon namedSets"}),h.push(f.find("DIMENSIONS").text().split(".")[0])),e.schemaTreeView.push({hasChildren:!0,isSelected:n.inArray("["+n.trim(f.children("SET_NAME").text())+"]",o)>=0,pid:f.find("SET_DISPLAY_FOLDER").text()+"_"+f.find("DIMENSIONS").text().split(".")[0],id:"["+n.trim(f.children("SET_NAME").text()).replace(/\&/g,"&amp;")+"]",name:f.children("SET_CAPTION").text(),spriteCssClass:"e-namedSetCDB e-icon",tag:f.find("EXPRESSION").text()});t.isNullOrUndefined(e._fieldData)||e._fieldData.hierarchySuccess==r?t.Pivot._getTreeData(c,t.Pivot.loadHierarchyElements,i):t.Pivot.loadHierarchyElements(i,e._fieldData.hierarchySuccess)},loadHierarchyElements:function(i,u){var f=i.schemaData.model.pivotControl,v={catalog:f.model.dataSource.catalog,cube:f.model.dataSource.cube,url:f.model.dataSource.data,request:"MDSCHEMA_LEVELS"},c=f.model.dataSource,o,a=f.model.dataSource.providerName==t.olap.Providers.Mondrian,s;for(o=n.map(c.rows,function(n){if(n.fieldName!=r)return n.fieldName}),n.merge(o,n.map(c.columns,function(n){if(n.fieldName!=r)return n.fieldName})),n.merge(o,n.map(c.filters,function(n){if(n.fieldName!=r)return n.fieldName})),s=0;s<n(u).find("row").length;s++){var e=n(n(u).find("row")[s]),l=e.find("DIMENSION_UNIQUE_NAME").text(),h=e.find("HIERARCHY_UNIQUE_NAME").text(),y=n(f.schemaTreeView).filter(function(n,t){return t.tag==l}).map(function(n,t){return t});y.length>0&&(l!=h||a)&&f.schemaTreeView.push({hasChildren:!0,isSelected:n.inArray(h,o)>=0,pid:l+(a?"~#^Dim":""),id:h,name:e.find("HIERARCHY_CAPTION").text(),spriteCssClass:e.find("HIERARCHY_ORIGIN").text()!="2"&&e.find("HIERARCHY_ORIGIN").text()!="6"?"e-hierarchyCDB e-icon":"e-attributeCDB e-icon",tag:h})}t.Pivot._getTreeData(v,t.Pivot.loadLevelElements,i)},loadLevelElements:function(i,r){var u=i.schemaData.model.pivotControl,e=n.map(n(r).find("row"),function(t){if(parseInt(n(t).children("LEVEL_TYPE").text())!="1"&&n(t).children("HIERARCHY_UNIQUE_NAME").text().toLowerCase()!="[measures]")return{hasChildren:!1,isChecked:!1,id:n(t).find("LEVEL_UNIQUE_NAME").text(),pid:n(t).find("HIERARCHY_UNIQUE_NAME").text(),name:n(t).find("LEVEL_CAPTION").text(),tag:n(t).find("LEVEL_UNIQUE_NAME").text(),spriteCssClass:"e-level"+parseInt(n(t).children("LEVEL_NUMBER").text())+" e-icon"}}),f;n.merge(u.schemaTreeView,e);(!u.model.enableDrillThrough||u._fieldData.measureSuccess)&&(t.isNullOrUndefined(u._fieldData)||!u._fieldData.measureSuccess?(f={catalog:u.model.dataSource.catalog,cube:u.model.dataSource.cube,url:u.model.dataSource.data,request:"MDSCHEMA_MEASURES"},t.Pivot._getTreeData(f,t.Pivot.loadMeasureElements,i)):t.Pivot.loadMeasureElements(i,u._fieldData.measureSuccess))},loadMeasureGroups:function(i,r){t.isNullOrUndefined(i.pivotControl._fieldData)&&(i.pivotControl._fieldData={});i.pivotControl._fieldData.measuresGroups=n(r).find("row")},loadMeasureElements:function(i,u){var f=i.schemaData.model.pivotControl,c={catalog:f.model.dataSource.catalog,cube:f.model.dataSource.cube,url:f.model.dataSource.data,request:"MDSCHEMA_DIMENSIONS"},p=[],w=[],a,e,y;p=n.map(f.model.dataSource.values,function(n){if(n.measures!=r)return n.measures});f.reportItemNames=n.map(p,function(n){if(n.fieldName!=r)return n.fieldName});f.model.locale!="en-US"&&(c={catalog:f.model.dataSource.catalog,cube:f.model.dataSource.cube,url:f.model.dataSource.data,request:"MDSCHEMA_MEASUREGROUPS"},t.Pivot._getTreeData(c,t.Pivot.loadMeasureGroups,{pivotControl:f,action:"loadFieldElements"}));var b,k,d=[],g=[],s=[],nt=[],tt=[],l,it=f.pluginName=="ejPivotClient"&&(f.model.sortCubeMeasures==t.PivotAnalysis.SortOrder.Ascending||f.model.sortCubeMeasures==t.PivotAnalysis.SortOrder.Descending)&&f.model.sortCubeMeasures!=t.PivotAnalysis.SortOrder.None;if(it){for(sortType=f.model.sortCubeMeasures==t.PivotAnalysis.SortOrder.Ascending?"ascending":"descending",e=0;e<n(u).find("row").length;e++)g.push({name:n(u).find("row:eq("+e+") MEASURE_CAPTION").text()});for(k=t.DataManager(g).executeLocal(t.Query().sortBy("name",sortType)),n.each(k,function(t,i){for(var r=0;r<n(u).find("row").length;r++)i.name==n(u).find("row:eq("+r+") MEASURE_CAPTION").text()&&s.push(n(u).find("row:eq("+r+")"))}),e=0;e<s.length;e++)n.inArray(n(s[e]).children("MEASUREGROUP_NAME").text(),d)>=0||(d.push(n(s[e]).children("MEASUREGROUP_NAME").text()),nt.push({name:n(s[e]).children("MEASUREGROUP_NAME").text()}));b=t.DataManager(nt).executeLocal(t.Query().sortBy("name",sortType));n.each(b,function(t,i){for(var r=0;r<s.length;r++)i.name==n(s[r]).children("MEASUREGROUP_NAME").text()&&tt.push(n(s[r]))});l=tt}else l=n(u).find("row");for(e=0;e<l.length;e++){var v=n(l[e]),o=v.children("MEASUREGROUP_NAME").text(),h=v.find("MEASURE_UNIQUE_NAME").text();n.inArray(o,w)>=0||(f.model.locale!="en-US"?(y=n.map(f._fieldData.measuresGroups,function(t){if(n(t).children("MEASUREGROUP_NAME").text()==o)return n(t).children("MEASUREGROUP_CAPTION").text()}),a=y.length>0?y[0]:o):a=o,o!=""&&f.schemaTreeView.push({hasChildren:!0,isChecked:!1,pid:"[Measures]",id:o,name:a,spriteCssClass:"e-measureGroupCDB e-icon",tag:o}),w.push(o));f.schemaTreeView.push({hasChildren:!0,isSelected:n.inArray(h,f.reportItemNames)>=0,id:h,pid:o==""?"[Measures]":o,name:v.children("MEASURE_CAPTION").text(),spriteCssClass:"measure",tag:h});n.inArray(h,f.reportItemNames)>=0&&f.reportItemNames.splice(f.reportItemNames.indexOf(h),1)}!t.isNullOrUndefined(i.schemaData)&&(i.schemaData.model.olap.showKpi||f.model.enableKPI)?(treeNodeElement={hasChildren:!0,isChecked:!1,id:"folderStruct",name:"KPI",spriteCssClass:"kpiCDB e-folderCDB e-icon",tag:""},f.schemaTreeView.splice(1,0,treeNodeElement),c.request="MDSCHEMA_KPIS",t.Pivot._getTreeData(c,t.Pivot.loadKPIElements,i)):t.isNullOrUndefined(i.schemaData)||i.schemaData._createTreeView(this,f.schemaTreeView)},loadKPIElements:function(t,i){for(var r=t.schemaData.model.pivotControl,e=this.reportItemNames,a=[],o=0;o<n(i).find("row").length;o++){var f=n(n(i).find("row")[o]),u=f.children("KPI_CAPTION").text(),s=f.children("KPI_goal").text(),h=f.children("KPI_STATUS").text(),c=f.children("KPI_TREND").text(),l=f.find("KPI_VALUE").text();n.inArray(f.children("KPI_NAME").text(),a)>=0||(treeNodeElement={hasChildren:!0,isChecked:!1,pid:"folderStruct",id:u,name:u,spriteCssClass:"e-measureGroupCDB e-icon",tag:u},r.schemaTreeView.push(treeNodeElement),a.push(u));treeNodeElement={hasChildren:!0,isSelected:n.inArray(s,e)>=0,id:s,pid:u,name:r.model.enableKPI?"Goal":r._getLocalizedLabels("Goal"),spriteCssClass:"kpiGoal e-icon",tag:s};r.schemaTreeView.push(treeNodeElement);treeNodeElement={hasChildren:!0,isSelected:n.inArray(h,e)>=0,id:h,pid:u,name:r.model.enableKPI?"Status":r._getLocalizedLabels("Status"),spriteCssClass:"kpiStatus e-icon",tag:h};r.schemaTreeView.push(treeNodeElement);treeNodeElement={hasChildren:!0,isSelected:n.inArray(c,e)>=0,id:c,pid:u,name:r.model.enableKPI?"Trend":r._getLocalizedLabels("Trend"),spriteCssClass:"kpiTrend e-icon",tag:c};r.schemaTreeView.push(treeNodeElement);treeNodeElement={hasChildren:!0,isSelected:n.inArray(l,e)>=0,id:l,pid:u,name:r.model.enableKPI?"Value":r._getLocalizedLabels("Value"),spriteCssClass:"kpiValue e-icon",tag:l};r.schemaTreeView.push(treeNodeElement)}t.schemaData._createTreeView(this,r.schemaTreeView);delete r.reportItemNames;delete r.schemaTreeView},_createCalcMemberDialog:function(i,u){var u=t.isNullOrUndefined(u.model)?this:u,s="",h,l,o,a,e,c,f;s=i.length>1&&i[0]!=r?JSON.parse(i[0].Value):i.d!=r?JSON.parse(i.d[0].Value):JSON.parse(i.CubeTreeInfo);u.model.afterServiceInvoke!=null&&u._trigger("afterServiceInvoke",{action:"fetchCalcMemberTreeView",element:u.element,customObject:u.model.customObject});t.Pivot.openPreventPanel(u);u.element.find(".e-calcMemberDialog",".e-clientDialog",".e-dialog").remove();var y=t.buildTag("div.e-cubeBrowserCalcMember",t.buildTag("div#"+u._id+"_cubeTreeViewCalcMember.e-cubeTreeViewCalcMember")[0].outerHTML,{})[0].outerHTML,p=t.buildTag("label.lblCaption",u._getLocalizedLabels("Caption"),{})[0].outerHTML+t.buildTag("input#"+u._id+"_captionFieldCM.captionFieldCM","",{}).attr("aria-label",u._getLocalizedLabels("Caption"))[0].outerHTML,w=t.buildTag("label.lblexpression",u._getLocalizedLabels("Expression"),{})[0].outerHTML+t.buildTag("textarea#"+u._id+"_expressionFieldCM.e-textarea e-droppable expressionFieldCM"+(u.model.enableRTL?" e-rtl":""),"",{}).attr("aria-label",u._getLocalizedLabels("Expression"))[0].outerHTML,b=t.buildTag("label.lblmemberType",u._getLocalizedLabels("MemberType"),{})[0].outerHTML+t.buildTag("input#"+u._id+"_memberTypeFieldCM.memberTypeFieldCM","",{}).attr("aria-label",u._getLocalizedLabels("MemberType"))[0].outerHTML+t.buildTag("input#"+u._id+"_dimensionFieldCM.dimensionFieldCM","",{}).attr("aria-label","dimension")[0].outerHTML,k=t.buildTag("label.lblformat",u._getLocalizedLabels("FormatString"),{})[0].outerHTML+t.buildTag("input#"+u._id+"_formatFieldCM.formatFieldCM","",{}).attr("aria-label",u._getLocalizedLabels("FormatString"))[0].outerHTML+t.buildTag("input#"+u._id+"_customFormatFieldCM.customFormatFieldCM","",{}).attr("aria-label","custom format")[0].outerHTML,d=t.buildTag("div.e-calcMemberFieldPanel",p+w+b+k,{})[0].outerHTML,g=t.buildTag("button#"+u._id+"_btnOk.e-btnCalcMemberOk",u._getLocalizedLabels("OK"),{},{name:u._getLocalizedLabels("OK")}).attr("aria-label",u._getLocalizedLabels("OK")).attr("title",u._getLocalizedLabels("OK").replace(/(<([^>]+)>)/ig,""))[0].outerHTML,nt=t.buildTag("button#"+u._id+"_btnCancel.btnCalcMemberCancel",u._getLocalizedLabels("Cancel"),{},{name:u._getLocalizedLabels("Cancel")}).attr("aria-label",u._getLocalizedLabels("Cancel")).attr("title",u._getLocalizedLabels("Cancel").replace(/(<([^>]+)>)/ig,""))[0].outerHTML,tt=t.buildTag("div.e-calcMemberFooter",g+nt,{})[0].outerHTML;for(u._calcMemberDialog=t.buildTag("div#"+u._id+"_calcMemberDialog",y+d+tt,{})[0].outerHTML,n(u._calcMemberDialog).appendTo("#"+u._id),n("#"+u._id+"_calcMemberDialog").ejDialog({width:"auto",title:u._getLocalizedLabels("CalculatedMember"),cssClass:u.model.cssClass+" e-calcMemberDialog",enableModal:!1,target:"#"+u._id,enableRTL:u.model.enableRTL,enableResize:!1,close:t.proxy(function(){t.Pivot.closePreventPanel(u)},u),beforeOpen:t.proxy(function(){u.element.find(".e-calcMemberDialog .e-dialog").css("display","block")},u)}),u._calcMemberDialog=u.element.find("#"+u._id+"_calcMemberDialog").data("ejDialog"),n("#"+u._id+"_btnCancel").ejButton({type:t.ButtonType.Button,width:"80px",enableRTL:u.model.enableRTL,click:t.proxy(function(){u._calcMemberDialog.close();u._selectedCalcMember=null;t.Pivot.closePreventPanel(u)},u)}),n("#"+u._id+"_btnOk").ejButton({type:t.ButtonType.Button,width:"80px",enableRTL:u.model.enableRTL,click:t.proxy(function(){var e,s,i,o,r,f,c;if(n.trim(u.element.find("#"+u._id+"_captionFieldCM").val())==""||n.trim(u.element.find("#"+u._id+"_expressionFieldCM").val())==""){t.Pivot._createErrorDialog(u._getLocalizedLabels("EmptyField"),u._getLocalizedLabels("Warning"),u);return}if(u.element.find("#"+u._id+"_formatFieldCM").val()=="Custom"&&n.trim(u.element.find("#"+u._id+"_customFormatFieldCM").val())==""){t.Pivot._createErrorDialog(u._getLocalizedLabels("EmptyFormat"),u._getLocalizedLabels("Warning"),u);return}if(t.isNullOrUndefined(u._selectedCalcMember))for(e=0;e<u._calcMembers.length;e++)if(n.trim(u.element.find("#"+u._id+"_captionFieldCM").val()).toLowerCase()==u._calcMembers[e].name.toLowerCase())if(confirm(u._getLocalizedLabels("Confirm")))u._selectedCalcMember=n.trim(u.element.find("#"+u._id+"_captionFieldCM").val());else return;if(u._calcMemberDialog.close(),t.Pivot.closePreventPanel(u),s="",t.isNullOrUndefined(u._selectedCalcMember)||(r=t.DataManager(u._calcMembers).executeLocal(t.Query().where("name","equal",u._selectedCalcMember,!0)),!t.isNullOrUndefined(r)&&r.length>0&&(s=r[0].tag)),u._waitingPopup.show(),i={action:"calculatedMember",olapReport:u.currentReport,clientReports:u.reports,caption:u.currentCubeName+"%"+n.trim(u.element.find("#"+u._id+"_captionFieldCM").val()),expression:n.trim(u.element.find("#"+u._id+"_expressionFieldCM").val()),memberType:u.element.find("#"+u._id+"_memberTypeFieldCM").val(),dimension:u.element.find("#"+u._id+"_dimensionFieldCM").val(),formatString:u.element.find("#"+u._id+"_formatFieldCM").val()=="Custom"?n.trim(u.element.find("#"+u._id+"_customFormatFieldCM").val()):u.element.find("#"+u._id+"_formatFieldCM").val(),uniqueName:s,customObject:u.model.customObject},u.model.operationalMode==t.Pivot.OperationalMode.ServerMode)u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.calculatedMember,JSON.stringify(i),u._calcMemberDroppedSuccess);else{i.caption=i.caption.replace("%","");o="";o=i.memberType.toLowerCase().indexOf("measure")>-1?"[Measures].["+n.trim(i.caption)+"]":n(jQuery("[id*='["+i.dimension+"]']")[0]).attr("data-defaulthierarchy")+".["+n.trim(i.caption)+"]";r={caption:i.caption,expression:i.expression,tag:o,hierarchyUniqueName:"["+n.trim(i.dimension)+"].["+n.trim(i.dimension)+"]",memberType:i.memberType,formatString:i.formatString?i.formatString:i.format?i.format:null};i.memberType.toLowerCase().indexOf("measure")==-1&&u._selectedCalcMember!=null&&(t.Pivot.getReportItemByFieldName(this._schemaData._selectedFieldName,this.model.dataSource).item.hierarchyUniqueName=r.hierarchyUniqueName);u._selectedCalcMember=null;var l={id:i.caption,pid:"_0",name:i.caption,hasChildren:!1,spriteCssClass:"e-calcMemberCDB e-icon",tag:o,expression:i.expression,formatString:null,nodeType:0,hierarchyUniqueName:i.memberType=="Measure"?"":r.hierarchyUniqueName},h=!1,a=r.caption;u.model.calculatedMembers=n.grep(u.model.calculatedMembers,function(n){return n.caption==a&&(h=!0,n.caption=r.caption,n.expression=r.expression,n.hierarchyUniqueName=r.hierarchyUniqueName,n.memberType=i.memberType,n.formatString=i.formatString),n});f=u.element.find(".e-schemaFieldTree").data("ejTreeView").model.fields;f.dataSource[0].id!="_0"&&f.dataSource.splice(0,0,[{id:"_0",name:"Calculated Members",hasChildren:!0,spriteCssClass:"e-calcMemberGroupCDB e-icon",tag:""}][0]);h?(c=this._schemaData._selectedFieldName,f.dataSource=n.grep(f.dataSource,function(n){return n.id==c&&(n.expression=r.expression,n.hierarchyUniqueName=r.hierarchyUniqueName,n.expression=r.expression,n.memberType=r.memberType,n.formatString=r.formatString),n})):u.model.calculatedMembers.push(r);h?u.refreshControl():f.dataSource.push(l);t.Pivot._refreshFieldList(u)}},u)}),u.element.find("#"+u._id+"_cubeTreeViewCalcMember").ejTreeView({fields:{id:"id",parentId:"pid",text:"name",spriteCssClass:"spriteCssClass",dataSource:s},allowDragAndDrop:!0,enableRTL:u.model.enableRTL,allowDropChild:!1,allowDropSibling:!1,dragAndDropAcrossControl:!0,cssClass:"calcMemberTreeViewDragedNode",nodeDropped:t.proxy(function(t){t.target!=null&&t.target.attr("id")==""+u._id+"_expressionFieldCM"&&u.element.find("#"+u._id+"_expressionFieldCM").val(u.element.find("#"+u._id+"_expressionFieldCM").val()+n(t.droppedElement).attr("data-tag"))},u),beforeExpand:t.proxy(t.Pivot._getMemberChildNodes,u)}),u.element.find("#"+u._id+"_captionFieldCM").ejMaskEdit({name:"inputbox",inputMode:t.InputMode.Text,watermarkText:"",maskFormat:"",textAlign:u.model.enableRTL?"right":"left",width:"100%"}),u.element.find("#"+u._id+"_customFormatFieldCM").ejMaskEdit({name:"inputbox",inputMode:t.InputMode.Text,watermarkText:"",maskFormat:"",textAlign:u.model.enableRTL?"right":"left",width:"100%",cssClass:"e-calcMemberCustomFormat"}),u.element.find(".e-calcMemberCustomFormat").css("visibility","hidden"),u.element.find("#"+u._id+"_memberTypeFieldCM").ejDropDownList({dataSource:[{text:"Measure",value:"Measure"},{text:"Dimension",value:"Dimension"}],enableRTL:u.model.enableRTL,width:"100%",selectedIndex:0,change:t.proxy(function(n){n.text=="Dimension"?u.element.find(".e-calcMemberDimensionField").css("visibility","visible"):u.element.find(".e-calcMemberDimensionField").css("visibility","hidden")},u)}),h=t.DataManager(s).executeLocal(t.Query().where("spriteCssClass","contains","e-dimensionCDB")),l=[],f=0;f<h.length;f++)l.push({text:h[f].name,value:h[f].name});for(u.element.find("#"+u._id+"_dimensionFieldCM").ejDropDownList({dataSource:l,selectedIndex:0,width:"100%",enableRTL:u.model.enableRTL,cssClass:"e-calcMemberDimensionField"}),u.element.find(".e-calcMemberDimensionField").css("visibility","hidden"),u.element.find("#"+u._id+"_formatFieldCM").ejDropDownList({dataSource:[{text:"Standard",value:"Standard"},{text:"Currency",value:"Currency"},{text:"Percent",value:"Percent"},{text:"Custom",value:"Custom"}],enableRTL:u.model.enableRTL,selectedIndex:0,width:"100%",enableRTL:u.model.enableRTL,change:t.proxy(function(n){n.text=="Custom"?u.element.find(".e-calcMemberCustomFormat").css("visibility","visible"):u.element.find(".e-calcMemberCustomFormat").css("visibility","hidden")},u)}),o=u.element.find(".e-cubeTreeViewCalcMember").find("li"),f=0;f<o.length;f++)t.isNullOrUndefined(n(o[f]).attr("id"))||n(o[f]).attr("data-tag",t.DataManager(s).executeLocal(t.Query().where("id","equal",n(o[f]).attr("id")))[0].tag);for(a=u.element.find(".e-cubeTreeViewCalcMember .e-folderCDB"),f=0;f<a.length;f++)n(a[f].parentElement).removeClass("e-draggable");if(u.element.find(".e-cubeTreeViewCalcMember .calcMemberGroupCDB").length>0&&u.element.find(".e-cubeTreeViewCalcMember .calcMemberGroupCDB").parent().removeClass("e-draggable"),u._calcMemberTreeObj=u.element.find(".e-cubeTreeViewCalcMember").data("ejTreeView"),!t.isNullOrUndefined(u._selectedCalcMember)&&(e={},e=u.model.operationalMode==t.Pivot.OperationalMode.ServerMode?t.DataManager(u._calcMembers).executeLocal(t.Query().where("name","equal",u._selectedCalcMember)):t.DataManager(u.model.calculatedMembers).executeLocal(t.Query().where("caption","equal",u._selectedCalcMember)),e.length>0)){if(c=0,c=u.model.operationalMode==t.Pivot.OperationalMode.ServerMode?e[0].nodeType:e.length>0&&e[0].memberType&&(e[0].memberType=="dimension"||e[0].memberType=="Dimension")?1:0,u.element.find("#"+u._id+"_captionFieldCM").val(e[0].caption||e[0].name),u.element.find("#"+u._id+"_expressionFieldCM").val(e[0].expression),u.element.find("#"+u._id+"_memberTypeFieldCM").data("ejDropDownList").selectItemsByIndices(c),c==1){var it=u.model.operationalMode==t.Pivot.OperationalMode.ServerMode?e[0].tag:e[0].hierarchyUniqueName,rt=it.split(".")[0].replace(/\[/g,"").replace(/\]/g,""),v=u.element.find("#"+u._id+"_dimensionFieldCM").data("ejDropDownList").model.dataSource;for(f=0;f<v.length;f++)v[f].value==rt&&u.element.find("#"+u._id+"_dimensionFieldCM").data("ejDropDownList").selectItemsByIndices(f)}t.isNullOrUndefined(e[0].formatString)||(e[0].formatString=="Standard"?u.element.find("#"+u._id+"_formatFieldCM").data("ejDropDownList").selectItemsByIndices(0):e[0].formatString=="Currency"?u.element.find("#"+u._id+"_formatFieldCM").data("ejDropDownList").selectItemsByIndices(1):e[0].formatString=="Percent"?u.element.find("#"+u._id+"_formatFieldCM").data("ejDropDownList").selectItemsByIndices(2):(u.element.find("#"+u._id+"_formatFieldCM").data("ejDropDownList").selectItemsByIndices(3),u.element.find("#"+u._id+"_customFormatFieldCM").val(e[0].formatString)))}u._waitingPopup.hide()},_refreshFieldList:function(i){var e=i.element.find(".e-schemaFieldTree").data("ejTreeView"),o,f,u,s,r;for(e.refresh(),o=e.model.fields.dataSource,f=i.element.find(".e-schemaFieldTree li"),u=0;u<n(f).length;u++)s=n(n(f)[u]).attr("id"),r=n.map(o,function(n){if(n.id==s)return{tag:n.tag,expression:n.expression,defaultHierarchy:n.defaultHierarchy}}),r.length>0&&r[0]!=""&&(n(n(f)[u]).attr("data-tag",r[0].tag),t.isNullOrUndefined(r[0].expression)||n(n(f)[u]).attr("expression",r[0].expression),t.isNullOrUndefined(r[0].defaultHierarchy)||n(n(f)[u]).attr("data-defaultHierarchy",r[0].defaultHierarchy))},_getMemberChildNodes:function(i){var r,f,o;if((n(i.currentElement).find("a > span")[0].className.indexOf("level")>-1||n(i.currentElement).find("a > span")[0].className.indexOf("member")>-1)&&(r=t.DataManager(this._calcMemberTreeObj.dataSource()).executeLocal(t.Query().where("pid","equal",n(i.currentElement).attr("id")).where("name","equal","(Blank)")),!t.isNullOrUndefined(r)&&r.length>0))if(this._calcMemberTreeObj.hideNode(r[0].id),this.pNode=i.currentElement,this._waitingPopup.show(),f=n(i.currentElement).find("a > span")[0].className.indexOf("level")>-1?"level":"member",this.model.operationalMode==t.Pivot.OperationalMode.ClientMode){var u=this.model.dataSource,s=this.element.find(".e-cubeTreeViewCalcMember").data("ejTreeView"),e=n.map(s.model.fields.dataSource,function(t){if(t.id==n(i.currentElement).attr("id"))return t});if(n(i.currentElement).children("ul").children().length>1)this._waitingPopup&&this._waitingPopup.hide();else{e.length>0&&(this._calcExpanded=e[0]);var h="select {"+n(i.currentElement).attr("data-tag")+".members}dimension properties CHILDREN_CARDINALITY, MEMBER_TYPE on 0 from ["+n.trim(u.cube)+"]",c=t.olap._mdxParser.getSoapMsg(h,u.data,u.catalog),l=t.olap.base._getConnectionInfo(u.data);this.doAjaxPost("POST",l.url,{XMLA:c},t.proxy(this._generateCalculatedMember,this),null,{action:"fetchMembers"})}}else this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:"fetchMemberChildNodes",element:this.element,customObject:this.model.customObject}),o=JSON.stringify(this.model.customObject),this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.fetchMemberTreeNodes,JSON.stringify({action:"fetchMemberChildNodes",dimensionName:n(i.currentElement).attr("data-tag")+":"+f+":"+n(i.currentElement).attr("id"),olapReport:this.currentReport,customObject:o}),t.proxy(t.Pivot._fetchMemberSuccess,this))},_fetchMemberSuccess:function(u){var f=[],e,o;for(f=u.length>1&&u[0]!=r?JSON.parse(u[0].Value):u.d!=r?JSON.parse(u.d[0].Value):JSON.parse(u.MemberChildNodes),this.model.afterServiceInvoke!=null&&this._trigger("afterServiceInvoke",{action:"fetchMemberChildNodes",element:this.element,customObject:this.model.customObject}),this._calcMemberTreeObj.model.beforeExpand=null,this._calcMemberTreeObj.addNode(f,n(this.pNode)),e=t.DataManager(this._calcMemberTreeObj.dataSource()).executeLocal(t.Query().where("pid","equal",n(this.pNode).attr("id")).where("name","equal","(Blank)")),!t.isNullOrUndefined(e)&&e.length>0&&this._calcMemberTreeObj.removeNode(e[0].id),n.each(n(this.pNode).children().find("li"),function(n,t){t.setAttribute("data-tag",f[n].tag)}),o=n(this.pNode).children().find("li"),i=0;i<o.length;i++)f[i].hasChildren==!0&&(this._calcMemberTreeObj.addNode({id:f[i].id+"_Blank_"+i,name:"(Blank)",parentId:f[i].id},o[i]),this.element.find("#"+f[i].id).find("> div > div:first").removeClass("e-process"),this._calcMemberTreeObj.collapseNode(this.element.find("#"+f[i].id)));this._calcMemberTreeObj.model.beforeExpand=t.proxy(t.Pivot._getMemberChildNodes,this);this._waitingPopup.hide()},_drillThroughCellClick:function(r,u){var h,e,f,c,o,l,s;if(u._waitingPopup.show(),f=n(r.currentTarget.parentElement).attr("data-p"),u.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode)e=n(u.element).parents(".e-pivotclient").length>0||n(u.element).parents(".e-maximumView").length>0?u._pivotClientObj:u,e.doAjaxPost("POST",e.model.url+"/"+e.model.serviceMethodSettings.drillThroughDataTable,JSON.stringify({currentReport:JSON.parse(u.getOlapReport()).Report,layout:u.model.layout,cellPos:f,selector:"",customObject:JSON.stringify(e.model.customObject)}),function(n){e._trigger("drillThrough",{element:u.element,data:n})});else{for(c=n("#"+u._id).find("tbody").find("tr:first").find("th").length,i=0;i<c;i++)o=u.getJSONRecords()[parseInt(i*u._rowCount+parseInt(f.split(",")[1]))].Info.indexOf("Measures")!=-1||u.getJSONRecords()[parseInt(i*u._rowCount+parseInt(f.split(",")[1]))].RowSpan<=1?u.getJSONRecords()[parseInt(i*u._rowCount+parseInt(f.split(",")[1]))].Info.split("::")[0]:"",o.indexOf("Measures")!=-1&&(h=o),o!=""&&(u._rowHeader[i]=o);for(l=n("#"+u._id).find("tbody").find('[data-p="'+parseInt(f.split(",")[0])+","+parseInt(f.split(",")[1])+'"]').closest("tbody").prev().children("tr").length,i=0;i<l;i++)s=u.getJSONRecords()[parseInt(parseInt(f.split(",")[0])*u._rowCount+i)].Info.indexOf("Measures")!=-1||u.getJSONRecords()[parseInt(parseInt(f.split(",")[0])*u._rowCount+i)].ColSpan<=1?u.getJSONRecords()[parseInt(parseInt(f.split(",")[0])*u._rowCount+i)].Info.split("::")[0]:"",s.indexOf("Measures")!=-1&&(h=s),s!=""&&(u._colHeader[i]=s);for((t.isNullOrUndefined(u._fieldData)||!t.isNullOrUndefined(u._fieldData)&&t.isNullOrUndefined(u._fieldData.hierarchy))&&u._getFieldItemsInfo(u),j=0;j<u._fieldData.measures.length;j++)h==u._fieldData.measures[j].id&&(u.measureGrp=u._fieldData.measures[j].pid);u._rowHeader=n.grep(u._rowHeader,function(n){return n==0||n});u._colHeader=n.grep(u._colHeader,function(n){return n==0||n});u._createDrillThroughQuery("",u)}},openHierarchySelector:function(n,i){var r,u,f;n.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this._createDrillThroughDialog(n,i):(n._waitingPopup.show(),r=n.target.className.indexOf("e-pivotclient")>=0?n._pivotGrid:n,u=r._getConnectionInfo(n.model.dataSource.data),f='<Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/"><Header/><Body><Discover xmlns="urn:schemas-microsoft-com:xml-analysis"><RequestType>MDSCHEMA_MEASUREGROUP_DIMENSIONS<\/RequestType><Restrictions><RestrictionList><CATALOG_NAME>'+n.model.dataSource.catalog+"<\/CATALOG_NAME><CUBE_NAME>"+n.model.dataSource.cube+"<\/CUBE_NAME><MEASUREGROUP_NAME>"+r.measureGrp+"<\/MEASUREGROUP_NAME><\/RestrictionList><\/Restrictions><Properties><PropertyList><Catalog>"+n.model.dataSource.catalog+"<\/Catalog> <LocaleIdentifier>"+u.LCID+"<\/LocaleIdentifier><\/PropertyList><\/Properties><\/Discover><\/Body><\/Envelope>",this.doAjaxPost("POST",u.url,{XMLA:f},r._loadDimensionElements,null,{pvtGridObj:r,action:"loadMeasureElements"}))},_createDrillThroughDialog:function(i,r){var e,u,o,f;i.model.operationalMode!=t.PivotGrid.OperationalMode.ServerMode&&r.shift();i.element.find(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog), .e-clientDialog").remove();var s=t.buildTag("label#"+this._id+"_",i._getLocalizedLabels("SelectHierarchy"))[0].outerHTML,h="<br><textarea id='"+this._id+"_hrSel' style='width:270px; height:300px; resize:none; margin:0px 5px 0 5px'><\/textarea><\/br><br>",c="<div class=e-cubeTable style='width:200px; overflow:auto'><div valign=\"bottom\">"+this._createHierarchyBrowser()+"<\/div><\/div>",l=t.buildTag("div#"+this._id+"_dropDlg.dropDlg",'<table class="e-outerTable"><tr><td>'+c+"<\/td><td>"+s+h+"<\/td><\/tr><\/table>")[0].outerHTML+"<\/br>",a=t.buildTag("div",t.buildTag("button#"+this._id+"_btnOK.dialogBtnOK",i._getLocalizedLabels("OK")).attr("title",i._getLocalizedLabels("OK").replace(/(<([^>]+)>)/ig,""))[0].outerHTML+t.buildTag("button#"+this._id+"_btnCancel.dialogBtnCancel",i._getLocalizedLabels("Cancel")).attr("title",i._getLocalizedLabels("OK").replace(/(<([^>]+)>)/ig,""))[0].outerHTML,{float:"right",margin:"-5px 0 6px"})[0].outerHTML,v=t.buildTag("div#"+this._id+"_clientDialog.e-clientDialog",l+a,{opacity:"1"}).attr("title","Hierarchy Selector")[0].outerHTML;for(n(v).appendTo("#"+i._id),n("#"+this._id+"_btnOK, #"+this._id+"_btnCancel").ejButton(),n("#"+this._id+"_btnOK, #"+this._id+"_btnCancel").css({margin:"0 20px 20px 0",width:"50px"}),i.element.find(".e-clientDialog").ejDialog({width:550,target:"#"+i._id,enableResize:!1,enableRTL:i.model.enableRTL,close:t.proxy(t.Pivot.closePreventPanel,i)}),i.element.find(".cubeTreeViewHierarchy").ejTreeView({showCheckbox:!0,fields:{id:"id",parentId:"pid",text:"name",isChecked:"isSelected",spriteCssClass:"spriteCssClass",dataSource:i.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?t.isNullOrUndefined(r.d)?n.parseJSON(r):n.parseJSON(r.d):r},allowDragAndDrop:!0,allowDropChild:!1,allowDropSibling:!1,enableRTL:i.model.enableRTL?!0:!1,beforeDelete:function(){return!1},dragAndDropAcrossControl:!0,nodeDropped:t.proxy(this._hierarchyNodeDropped,i)}),i._tableTreeObj=i.element.find(".cubeTreeViewHierarchy").data("ejTreeView"),i._tableTreeObj.element.find(".e-ul").css({width:"100%",height:"100%"}),i._tableTreeObj.element.find(".e-chkbox-wrap").remove(),e=i._tableTreeObj.element.find("li"),u=0;u<e.length;u++)o=i.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?t.isNullOrUndefined(r.d)?n.parseJSON(r)[u].tag:n.parseJSON(r.d)[u].tag:r[u].tag,e[u].setAttribute("data-tag",o);i._tableTreeObj&&i._tableTreeObj.element.find("li").mouseover(t.proxy(function(t){n(t.target).parent().find(".e-measureGroupCDB, .e-dimensionCDB, .e-folderCDB").length>0&&n(t.target).css("cursor","default")},i));f=i;n("#"+this._id+"_btnOK").click(function(){var i=n("#"+this._id+"_hrSel").val();n(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog), .e-clientDialog, .drilltableDialog").remove();f._waitingPopup.show();t.olap._mdxParser._createDrillThroughQuery(i,f);f._waitingPopup.hide()});n("#"+this._id+"_btnCancel").click(function(){n(".e-dialog:not(.e-calcMemberDialog, .e-calcMemberDialog .e-dialog), .e-clientDialog").remove();f._waitingPopup.hide()});i.model.enableRTL&&(n(".e-dialog").addClass("e-rtl"),n(".dialogBtnCancel").css("margin","0 -70px 0 0"))},_createHierarchyBrowser:function(){return t.buildTag("div.cubeBrowserHierarchy",t.buildTag("div.cubeTreeViewHierarchy")[0].outerHTML,{width:"200px",height:"300px",overflow:"auto"})[0].outerHTML},_hierarchyNodeDropped:function(i){var r,f,u;if(i.dropTarget[0].id=="hrSel"&&(r=n(n(i.droppedElement).children()[0]).find(".e-dimensionCDB").length>0?this.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?n(i.droppedElement).find("li:first").attr("data-tag"):n(i.droppedElement).find("li:first")[0].id:n(i.droppedElement).find("li:first").length==0?this.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?n(i.droppedElement.parent().parent()).attr("data-tag"):n(i.droppedElement.parent().parent())[0].id:this.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?n(i.droppedElement).attr("data-tag"):i.droppedElementData.id,!t.isNullOrUndefined(r))){for(r=r.replace("[","[$"),f=0;f<n("#"+this._id+"_hrSel").val().split(",").length;f++)if(r==n("#"+this._id+"_hrSel").val().split(",")[f])return!1;u=this.element.find('#" + this._id + "_hrSel').val();u.length!=0?(u=u+","+r,this.element.find('#" + this._id + "_hrSel').val(u)):this.element.find('#" + this._id + "_hrSel').val(r)}},_generateDrillData:function(t,i){for(var e=n(i).find("row").children(),r=n.map(e,function(n){var t=n.textContent,i=n.tagName.replace(/_x005B_/g,"[").replace(/_x0020_/g," ").replace(/_x005D_/g,"]").replace(/_x0024_/g,"$").replace("].[","]-[");return'"'+i+'":"'+t+'"'}),o=r[0],u="",f=0;f<r.length;f++){if(r[f]==o){u+=u==""?"[{"+r[f]:"}, {"+r[f];continue}u+=","+r[f]}u+="}]";n(t.pvtGridObj.element).parents(".e-pivotclient").length>0?n(t.pvtGridObj.element).parents(".e-pivotclient").data("ejPivotClient")._trigger("drillThrough",{element:t.pvtGridObj.element,data:u}):t.pvtGridObj._trigger("drillThrough",{element:t.pvtGridObj.element,data:u})},_getFilterStateMember:function(i,r,u,f,e){if(u.filterItems.filterType=="include")if(u.filterItems.values.length==1)if(t.isNullOrUndefined(f._fieldSelectedMembers[u.fieldName.toLowerCase()])&&i){var o="select {"+u.filterItems.values[0]+"}dimension properties CHILDREN_CARDINALITY on 0 from ["+n.trim(f.model.dataSource.cube)+"]",s=t.olap._mdxParser.getSoapMsg(o,f.model.dataSource.data,f.model.dataSource.catalog),h=t.olap.base._getConnectionInfo(f.model.dataSource.data);this.doAjaxPost("POST",h.url,{XMLA:s},t.proxy(this._getInitialSlicerName,this),null,{controlObject:f,action:"fetchMembers"});e=f._slicerName}else i||(e=u.filterItems.values[0]);else e=f._getLocalizedLabels("MultipleItems");else e=r.length-(u.filterItems.values.indexOf("All")==-1?u.filterItems.values.length:u.filterItems.values.length-1)==1?r.filter(function(n){if(u.filterItems.values.indexOf(n)==-1)return n}):f._getLocalizedLabels("MultipleItems");return e},_getFilterState:function(i,u,f,e){var o="",h,s;if(e.model.operationalMode==t.Pivot.OperationalMode.ClientMode)i?(t.isNullOrUndefined(t.olap.base.olapCtrlObj)&&(t.olap.base.olapCtrlObj=e),(e._fieldSelectedMembers[f.fieldName.toLowerCase()]=="All"||t.isNullOrUndefined(e._fieldSelectedMembers[f.fieldName.toLowerCase()]))&&t.olap._mdxParser.getAllMember(e.model.dataSource,f.fieldName,e),o=e._fieldSelectedMembers[f.fieldName.toLowerCase()]=="All"||t.isNullOrUndefined(e._fieldSelectedMembers[f.fieldName.toLowerCase()])?e._allMember:e._fieldSelectedMembers[f.fieldName.toLowerCase()],o=this._getFilterStateMember(i,u,f,e,o)):o=this._getFilterStateMember(i,u,f,e,o);else if(e.model.analysisMode==t.Pivot.AnalysisMode.Pivot)if(h=t.isNullOrUndefined(e._tempFilterData)?[]:n.map(e._tempFilterData,function(n){return n[f.DimensionName]}),h.length>0&&e._fieldMembers[f.DimensionName])o=e._fieldMembers[f.DimensionName].length-h.length==1?e._fieldMembers[f.DimensionName].filter(function(n){if(h.indexOf(n)==-1)return n}):e._getLocalizedLabels("MultipleItems");else if(t.isNullOrUndefined(e._clientReportCollection))o=e._getLocalizedLabels("All");else for(s=0;s<e._clientReportCollection.length;s++)if(t.isNullOrUndefined(e._clientReportCollection[s]._fieldSelectedMembers)||(e._currentRecordName==""&&e.element.find("#"+e._id+"_reportList")!=r&&e.element.find("#"+e._id+"_reportList")[0].value==e._clientReportCollection[s].name?!1:!0)&&(e._currentRecordName==""||e._currentReportName!=e._clientReportCollection[s].name))o=e._getLocalizedLabels("All");else{e._fieldSelectedMembers[f.Tag]=e._clientReportCollection[s]._fieldSelectedMembers[f.DimensionName];o=e._fieldSelectedMembers[f.Tag]=="All"||e._fieldSelectedMembers[f.Tag]==r?e._getLocalizedLabels("All"):e._fieldSelectedMembers[f.Tag];break}else o=(e._fieldSelectedMembers[f.Tag]=="All"?f.AllMember:e._fieldSelectedMembers[f.Tag])||f.AllMember;return o},_getInitialSlicerName:function(t,i){t.controlObject._slicerName=n(n(i).find("Axis:eq(0) Tuple:eq(0)").children().children()[1]).text()},_getTreeViewItems:function(t){for(var r,e=t.element.find(".e-editorTreeView"),u=[],f=n(e).find(":input.nodecheckbox"),i=0;i<f.length;i++)r=n(f[i]).parents("li:eq(0)"),u.push({Id:r[0].id,name:n(r[0]).find("a:eq(0)").text()});return u},_getEditorMember:function(i,r,u){var f=u?r._schemaData._editorTreeData:r._editorTreeData,e=[],l,o,h,s;if(r.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode){if(f.length==0){var a=this._getTreeViewItems(u?r._schemaData:r,f),v=n.extend(!0,[],u?r._schemaData._memberTreeObj.dataSource():r._memberTreeObj.dataSource()),c=n.map(a,function(i){if(i.Id!="All")return n.map(v,function(n){i.Id==n.id&&(i=n,i.name=t.isNullOrUndefined(i.parentId)?i.name+"_1":i.name)}),i}),o=n.map(c,function(t){return n.map(c,function(n){t.parentId==n.id&&(t.name=t.name+"_"+(Number(n.name.split("_")[n.name.split("_").length-1])+1))}),t});n.map(o,function(n){var t=n.name.split("_")[n.name.split("_").length-2],i=Number(n.name.split("_")[n.name.split("_").length-1]),r=n.checkedStatus;e.push({name:t,checked:r,level:Number(i)})})}else n.map(f,function(n){e.push({name:n.name,id:n.id,pid:n.pid,checked:n.checkedStatus,level:n.level})});return this._updateEditorMembers(i,e,r,f)}if(r.model.analysisMode==t.Pivot.AnalysisMode.Olap)return l=u?r._schemaData.element.find(".e-editorTreeView"):r.element.find(".e-editorTreeView"),o=f.length>0?f:n(l).find(":input.nodecheckbox"),f.length>0?n.map(o,function(n){e.push({name:n.name,id:n.id,pid:n.pid,checked:n.checkedStatus,level:Number(n.id.split("_")[n.id.split("_").length-1])})}):n.map(o,function(t){if(n(t).parents("li:eq(0)").attr("id")!="All"){var i=n(t).parent().siblings("a").text(),r=t.value.split("_")[t.value.split("_").length-1],u=t.checked||n(t.parentElement).attr("aria-checked")=="mixed"?!0:!1;e.push({name:i,checked:u,level:Number(r)})}}),this._updateEditorMembers(i,e,r,f);for(h=[],s=1;s<i.length;s++)h.push(n(i[s].parentElement).siblings("a").text());return h},_updateEditorMembers:function(i,r,u,f){var e,b,h,a,w,y;if(r.length>0&&r[0].name=="All"&&r.splice(0,1),!t.isNullOrUndefined(u._fieldMembers[i]))if(f.length==0){var p=[],o=0,l="",s=u._fieldMembers[i];for(e=0;e<r.length;e++)if(t.isNullOrUndefined(s[e+o])||r[e].level==s[e+o].level)p.push(r[e]);else if(t.isNullOrUndefined(s[e+o])||r[e].level<s[e+o].level){h=e;do r[e-1].checked&&p.push(s[h]),o++,h++;while(r[e].level<s[h].level);e--}else if(t.isNullOrUndefined(s[e+o])||r[e].level>s[e+o].level){b=s[e+o].level;h=e;do p.push(r[h]),o--,h++,e=h-1;while(r[h].level>b)}u._fieldMembers[i]=p}else a=n.extend(!0,[],u._fieldMembers[i]),w=[],n.each(r,function(n,t){for(var r=!1,i=0;i<a.length;i++)if(t.id==a[i].id){a.splice(i,1,t);r=!0;break}r||w.push(t)}),u._fieldMembers[i]=a.concat(w);u._fieldMembers[i]=t.isNullOrUndefined(u._fieldMembers[i])?r:u._fieldMembers[i];var v=1,k="",d="",c=[];do if(y=n.map(u._fieldMembers[i],function(n){if(n.level==v)return n}),c=n.map(y,function(n){if(n.checked)return n}),y.length==c.length)l=v==1?"All":k,y.length>1&&(d="All"),v++;else if(c.length==1)k=l=d=="All"?"multiple":c[0].name,v++;else if(c.length>1&&y.length>c.length){l="multiple";break}while(c.length>0&&l!="All"&&l!="multiple"&&n.map(u._fieldMembers[i],function(n){if(n.level==v)return n}).length>0);return l},_getTreeData:function(n,i,r){var u=t.olap.base._getConnectionInfo(n.url),f='<Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/"><Header/><Body><Discover xmlns="urn:schemas-microsoft-com:xml-analysis"><RequestType>'+n.request+"<\/RequestType><Restrictions><RestrictionList><CATALOG_NAME>"+n.catalog+"<\/CATALOG_NAME>"+(r.action=="loadcubelist"?"":"<CUBE_NAME>"+n.cube+"<\/CUBE_NAME>")+"<\/RestrictionList><\/Restrictions><Properties><PropertyList><Catalog>"+n.catalog+"<\/Catalog><LocaleIdentifier>"+u.LCID+"<\/LocaleIdentifier> <\/PropertyList><\/Properties><\/Discover><\/Body><\/Envelope>";r.action="loadFieldElements";this.doAjaxPost("POST",u.url,{XMLA:f},i,null,r)},updateTreeView:function(i){var o=i.element.find(".e-editorTreeView").find("li"),l=o.length,u=i._memberTreeObj.dataSource(),a=u.length,s,v,h,c,e,f;if(i.element.find(".e-editorTreeView").parents("#"+i._id+"_ClientDialog").find(".e-editorPara").text()==i._getLocalizedLabels("KPIs"))for(e=0;e<l;e++)for(s=n(o[e]).find("span:first").attr("aria-checked"),f=0;f<a;f++)o[e].id==u[f].id&&(s=="mixed"||s=="true")?t.isNullOrUndefined(u[f].parentId)?u[f].checkedStatus=!0:n(o[e].parentElement).parent().attr("id")==u[f].parentId&&(u[f].checkedStatus=!0):o[e].id==u[f].id&&s=="false"&&(t.isNullOrUndefined(u[f].parentId)?u[f].checkedStatus=!1:n(o[e].parentElement).parent().attr("id")==u[f].parentId&&(u[f].checkedStatus=!1));else for(e=0;e<l;e++)for(s=n(o[e]).find("span:first").attr("aria-checked"),f=0;f<a;f++)if(o[e].id==u[f].id&&(s=="mixed"||s=="true")){u[f].checkedStatus=!0;break}else if(o[e].id==u[f].id&&s=="false"){u[f].checkedStatus=!1;break}for(e=0;e<u.length;e++)if(v=!1,u[e].checkedStatus==!0){for(f=0;f<u.length;f++)if(u[f].hasOwnProperty("parentId")&&u[f].parentId==u[e].id&&u[f].checkedStatus==!0){v=!0;break}if(!v)for(h=0;h<u.length;h++)u[h].hasOwnProperty("parentId")&&u[h].parentId==u[e].id&&(u[h].checkedStatus=!0)}else if(u[e].checkedStatus==!1)for(c=0;c<u.length;c++)u[c].hasOwnProperty("parentId")&&u[c].parentId==u[e].id&&(u[c].checkedStatus=!1);for(e=0;e<l;e++)if(n(o[e]).attr("data-tag")==null||r)for(f=0;f<a;f++)if(n(o[e]).attr("id")==u[f].id){n(o[e]).attr("data-tag",u[f].tag);break}return i._memberTreeObj},getNodesState:function(n){for(var r="",u="",i=[],i=n.pluginName=="ejTreeView"?n.dataSource():n,t=0;t<i.length;t++)i[t].checkedStatus==!0?r+="::"+i[t].id+"||"+i[t].tag+"||"+(i[t].parentId||i[t].pid)+"||"+i[t].name:u+="::"+(i[t].parentId||i[t].pid)+"||"+i[t].tag+"||"+i[t].name;return{selectedNodes:r,unSelectedNodes:u}},removeParentSelectedNodes:function(t){for(var i,u=n.extend([],t),f=0;f<t.length;f++)for(i=0;i<u.length;i++)u[i].Id==t[f].parentId&&u.splice(i,1);return n.map(u,function(n){if(n.tag!=""&&n.tag!=r)return n.tag.replace(/\&/g,"&amp;")})},getChildNodes:function(i,u,f,e,o){var s=i.targetElement,b=e.cube,k=e.catalog,d=e.data,g=o._generateChildMembers,p=n(i.currentElement).find("li").length,c=!1,h={memberEditorPageSize:o.pluginName=="ejPivotSchemaDesigner"?o.model.pivotControl.model.memberEditorPageSize:o.model.memberEditorPageSize,enableMemberEditorPaging:o.pluginName=="ejPivotSchemaDesigner"?o.model.pivotControl.model.enableMemberEditorPaging:o.model.enableMemberEditorPaging},l,v,y;if(h.enableMemberEditorPaging){for(l=0;l<o._memberTreeObj.dataSource().length;l++)if(o._memberTreeObj.dataSource()[l].parentId==i.id||o._memberTreeObj.dataSource()[l].pid==i.id){i.isChildLoaded=!0;t.Pivot.closePreventPanel(o);return}}else o._isSearchApplied=!1;if(jQuery.each(o._editorTreeData,function(n,t){if(t.id==i.id)return t.expanded=!0,t.isChildMerged=!0,!1}),p==0){var w=t.buildTag("span.nodeExpand e-load e-icon")[0].outerHTML,a=n(s).parents("li:eq(0)").attr("id"),nt=n.map(f,function(i){var f,e,l,v;i.fieldName!=u||t.isNullOrUndefined(i.filterItems)||(f=i.filterItems,(t.isNullOrUndefined(o.model.pivotControl)?o.model.enableMemberEditorPaging:o.model.pivotControl.model.enableMemberEditorPaging)?(f=n.map(f,function(n){if(n.pid!=r&&n.pid==a.replace(/\]*\]/g,"-").replace(/\[*\[/g,"-"))return c=!0,n}),f.length>0&&c==!0&&(e=t.Pivot._generateChildWithAncestors(o,n(s).parents("li:eq(0)"),h.enableMemberEditorPaging,h.memberEditorPageSize),h.enableMemberEditorPaging&&(f.length>=h.memberEditorPageSize||e.lstChildren.length>=h.memberEditorPageSize)?(o._isEditorDrillPaging=!0,o.element.find(".searchEditorTreeView").data("ejMaskEdit").clear(),o._lastSavedTree=[],t.Pivot._makeAncestorsExpandable(o,n(s).parents("li:eq(0)")[0].id),l=e.allLvlLst.length>1&&e.lstChildren.length>=h.memberEditorPageSize?t.Pivot._getParentsTreeList(o,e.lstParents[0].id,o._editorTreeData):n.grep(o._editorTreeData,function(t){return t.id==n(s).parents("li:eq(0)")["0"].id})[0],v={childNodes:e.allLvlLst.length>1&&e.lstChildren.length>=h.memberEditorPageSize?e.lstChildren:f,parentNode:l},t.Pivot._drillEditorTreeNode(v,o,h.memberEditorPageSize)):(n(s).parents("li:eq(0)").find(".nodeExpand").remove(),n(s).parents("li:eq(0)").removeClass("e-load"),n.each(f,function(n,t){delete t.parentId}),o._onDemandNodeExpand=!1,o._memberTreeObj.addNode(t.Pivot._showEditorLinkPanel(f,o,o.pluginName=="ejPivotSchemaDesigner"?o.model.pivotControl:o),n(s).parents("li:eq(0)")),o._onDemandNodeExpand=!1,o._memberTreeObj.model.nodeCheck=t.proxy(o._nodeCheckChanges,o),o._memberTreeObj.model.nodeUncheck=t.proxy(o._nodeCheckChanges,o),n.each(n(s).parents("li:eq(0)").find("li"),function(n,i){t.isNullOrUndefined(f[n])||i.setAttribute("data-tag",f[n].tag)})))):n.map(f,function(n){(n.parentId!=r&&n.parentId==a.replace(/\]*\]/g,"-").replace(/\[*\[/g,"-")||n.pid!=r&&n.pid==a.replace(/\]*\]/g,"-").replace(/\[*\[/g,"-"))&&(c=!0)}))});i.isChildLoaded=c?!0:!1;c?jQuery.grep(o._editorTreeData,function(n){if(n.pid==i.id)return n}).length>10&&o.element.find("a.e-linkPanel").css("display","block"):(n(s).parents("li:eq(0)").prepend(w),n(s).parents("li:eq(0)").attr("data-tag")==r&&(v=n.map(f,function(n){if(n.fieldName==u)return n})[0],v&&n.map(v.filterItems,function(t){t.id==n(s).parents("li:eq(0)").attr("id")&&n(s).parents("li:eq(0)").attr("data-tag",t.tag)})),y=n(s).parents("li:eq(0)").attr("data-tag").replace(/\&/g,"&amp;"),t.olap._mdxParser.getChildren(e,y,o))}},_getFilterParams:function(n,i,r){var o="",f,u,e;if(n!="schemaValue"){if(f="",!t.isNullOrUndefined(i))for(u=0;u<i.length;u++)if(!t.isNullOrUndefined(i[u][r]))for(e=0;e<i[u][r].length;e++)f+="##"+i[u][r][e];f!=""&&(o=n+"::"+r+"::FILTERED"+f)}return o},generateChildMembers:function(i,r){var l=n(r).find("Axis:eq(0) Tuple"),f=[],a,u=this,s,y,h,p,w;t.isNullOrUndefined(u.olapCtrlObj)||(u=u.olapCtrlObj);var o={memberEditorPageSize:u.pluginName=="ejPivotSchemaDesigner"?u.model.pivotControl.model.memberEditorPageSize:u.model.memberEditorPageSize,enableMemberEditorPaging:u.pluginName=="ejPivotSchemaDesigner"?u.model.pivotControl.model.enableMemberEditorPaging:u.model.enableMemberEditorPaging},c={currentReportItems:u.pluginName=="ejPivotSchemaDesigner"?u.model.pivotControl._currentReportItems:u._currentReportItems,savedReportItems:u.pluginName=="ejPivotSchemaDesigner"?u.model.pivotControl._savedReportItems:u._savedReportItems},e=u.element.find("[data-tag='"+i.currentNode.replace(/&amp;/g,"&")+"']");for(s=0;s<l.length;s++){var v=n(n(r).find("Axis:eq(0) Tuple:eq("+s+")").children().children()[0]).text(),b=n(n(r).find("Axis:eq(0) Tuple:eq("+s+")").children()).find("Caption").text()==""?"(Blank)":n(n(r).find("Axis:eq(0) Tuple:eq("+s+")").children()).find("Caption").text(),k={hasChildren:n(l[s]).find("CHILDREN_CARDINALITY").text()!="0",checkedStatus:n(n(e).find("input.nodecheckbox")[0]).parent().attr("aria-checked")=="true"?!0:!1,id:v.replace(/\]*\]/g,"-").replace(/\[*\[/g,"-").replace(/ /g,"_"),name:b,tag:v,level:parseInt(n(l[s]).find("LNum").text())};f.push(k)}f.length>0&&u.model.enableMemberEditorSorting&&u._sortType!=null&&(f=t.DataManager(f).executeLocal(t.Query().sortBy("name",u._sortType)));a=n(e).parents("li").length>1?n(e).parents("li").first():n(e).parents("li");n(n(a).find("input.nodecheckbox")[0]).ejCheckBox({checked:!1});u._memberTreeObj=u.element.find(".e-editorTreeView").data("ejTreeView");f.length>0&&(y=u.pluginName=="ejPivotGrid"?u._selectedField:u._selectedFieldName,n.map(c.currentReportItems,function(i){i.fieldName!=y||t.isNullOrUndefined(i.filterItems)||(n.each(f,function(t,i){i.pid=n(e).attr("id")}),n.merge(i.filterItems,f),u._editorTreeData=i.filterItems)}),c.currentReportItems.length==0&&u._editorTreeData.concat(f));e.find(".nodeExpand").remove();e.find(".e-load").removeClass("e-load");jQuery.each(u._editorTreeData,function(n,t){if(t.id==e["0"].id)return t.expanded=!0,!1});o.enableMemberEditorPaging?(h=t.Pivot._generateChildWithAncestors(u,e,o.enableMemberEditorPaging,o.memberEditorPageSize),f.length>=o.memberEditorPageSize||h.lstChildren.length>=o.memberEditorPageSize?(u._isEditorDrillPaging=!0,u.element.find(".searchEditorTreeView").data("ejMaskEdit").clear(),u._lastSavedTree=[],t.Pivot._makeAncestorsExpandable(u,e[0].id),p=h.allLvlLst.length>1&&h.lstChildren.length>=o.memberEditorPageSize?t.Pivot._getParentsTreeList(u,h.lstParents[0].id,u._editorTreeData):n.grep(u._editorTreeData,function(n){return n.id==e["0"].id})[0],w={childNodes:h.allLvlLst.length>1&&h.lstChildren.length>=o.memberEditorPageSize?h.lstChildren:f,parentNode:p},t.Pivot._drillEditorTreeNode(w,u,o.memberEditorPageSize)):u._memberTreeObj.addNode(t.Pivot._showEditorLinkPanel(f,u,u.pluginName=="ejPivotSchemaDesigner"?u.model.pivotControl:u),n(e))):u._memberTreeObj.addNode(t.Pivot._showEditorLinkPanel(f,u,u.pluginName=="ejPivotSchemaDesigner"?u.model.pivotControl:u),n(e));n.each(e.find("li"),function(n,i){t.isNullOrUndefined(f[n])||i.setAttribute("data-tag",f[n].tag)});c.savedReportItems=n.extend(!0,[],c.currentReportItems)},_generateChildWithAncestors:function(i,r,u,f){var c=[],o=[n.grep(i._editorTreeData,function(n){return n.id==r["0"].id})[0]],e=[],s,h;if(u){jQuery.each(i._editorTreeData,function(n,t){if(t.id==r["0"].id)return t.expanded=!0,!1});c.push(o[0]);do{for(s=[],e=[],h=0;h<o.length;h++)if(n.each(i._editorTreeData,function(n,i){t.isNullOrUndefined(i.pid)||i.pid!=o[h].id||(i.expanded&&(s.push(i),c.push(i)),e.push(i))}),e.length>=f)break;else e=[];if(o=s.length>0&&e.length<f?s:o,e.length>=f)break}while(s.length>0)}return{allLvlLst:c,lstParents:o,lstChildren:e}},_makeAncestorsExpandable:function(n,i){var r=t.DataManager(n._editorTreeData).executeLocal(t.Query().where("id","equal",i));r.length>0&&(r[0].expanded=!0,i=r[0].pid);!t.isNullOrUndefined(i)&&r.length>0&&this._makeAncestorsExpandable(n,i)},_jsonToEngine:function(n){for(var u,f=n,i=[],r=0;r<f.length;r++)u=parseInt(f[r].Index.split(",")[0]),t.isNullOrUndefined(i[u])?(i[u]=[],i[u].push(f[r])):i[u].push(f[r]);return i},_cropJson:function(i,r){var e=0,h=0,c=1,u={},o,s,f,a,y,l,b,k;if(u=i.model.operationalMode==t.Pivot.OperationalMode.ServerMode?{columns:JSON.parse(i.getOlapReport()).PivotColumns,rows:JSON.parse(i.getOlapReport()).PivotRows,values:JSON.parse(i.getOlapReport()).PivotCalculations}:{columns:i.model.dataSource.columns,rows:i.model.dataSource.rows,values:i.model.dataSource.values},o=u.columns.length+(u.values.length>0&&u.values[0].axis!="rows"?1:0),s=u.rows.length+(u.values.length>0&&u.values[0].axis=="rows"?1:0),o=o==0?1:o,s=s==0?1:s,i._drillAction==""&&i.model.enableCollapseByDefault){if(f=n.extend(!0,[],t.Pivot._jsonToEngine(r)),o+s>2){while(h<f[0].length)if(u.rows.length>1&&f[0][h].CSS.indexOf("summary")==-1&&h+c>o||u.columns.length==0&&u.values.length==0&&f[0][h].CSS.indexOf("summary")==-1||h>0&&c<u.columns.length){for(a=0;a<f.length;a++)f[a].splice(h,1);c++}else h++;for(c=1;e<f.length;)u.columns.length>1&&f[e][0].CSS.indexOf("summary")==-1&&e+c>s||u.rows.length==0&&u.values.length==0&&f[e][0].CSS.indexOf("summary")==-1||e>0&&c<u.rows.length?(f.splice(e,1),c++):e+=f[e][0].CSS.indexOf("summary")>-1?f[e][0].ColSpan:1}for(f=n.map(f,function(t){return[n.map(t,function(n){return(n.CSS=="summary rstot"||n.CSS=="summary cstot"||n.CSS=="summary stot")&&(n.State=2,n.Value=n.Value.replace("Total","")),n})]}),f[0][0].ColSpan=f[0][0].CSS=="none"&&u.values.length>0&&u.values[0].axis=="rows"&&u.rows.length>0?2:1,f[0][0].RowSpan=f[0][0].CSS=="none"&&u.values.length>0&&u.values[0].axis!="rows"&&u.columns.length>0?2:1,o=u.values.length>0&&u.values[0].axis=="rows"?u.values.length:1,s=u.values.length>0&&u.values[0].axis!="rows"?u.values.length:1,e=0;e<f[0].length;e++)f[0][e].CSS.indexOf("summary")>-1&&(f[0][e].RowSpan=o,f[0][e].ColSpan=1);for(e=0;e<f.length;e++)f[e][0].CSS.indexOf("summary")>-1&&(f[e][0].ColSpan=s,f[e][0].RowSpan=1);return n.map(f,function(n){return n})}if(i.model.enableCollapseByDefault||i._drillAction!=""){var v=0,f=n.extend(!0,[],t.Pivot._jsonToEngine(i._croppedJson)),d=n.extend(!0,[],t.Pivot._jsonToEngine(i._compJson)),p=n.extend(!0,[],i._compJson),w=i._drillParams.currRow==""?"column":"row";for(y=0;y<i._compJson.length;y++)if(parseInt(i._compJson[y].Index.split(",")[0])==0)v++;else break;return l=t.Pivot._getDrillRange(u,w,i,v,p),b=t.Pivot._sliceJson(u,w,o,s,i,p,l.startPos,l.endPos,v,f),i._drillAction=="drilldown"&&(k=t.Pivot._mergeJson(u,i,w,o,s,p,d,f,b,l.startPos,l.endPos,v)),n.map(i._drillAction=="drilldown"?k:b,function(n){return n})}return r},_getEnginePos:function(i,r,u){var f=n.map(t.isNullOrUndefined(u[parseInt(i.split(",")[0])])?u[parseInt(r.split(",")[0])]:u[parseInt(i.split(",")[0])],function(n,t){if(n.Index==n.Index.split(",")[0]+","+r.split(",")[1])return t})[0],e=n.map(u,function(t,i){var u=!1;return n.each(t,function(n,t){t.Index==r&&(u=!0)}),u?i:void 0})[0];return{colPos:e,rowPos:f}},_mergeJson:function(i,r,u,f,e,o,s,h,c,l,a,v){var d=t.Pivot._getIndices(u,o,r,h,l,a,v),rt=d.columnIndices,ut=d.rowIndices,ft=d.rowSpan,et=d.colSpan,nt=t.Pivot._getEnginePos(l,a,h),ot=nt.colPos,tt=nt.rowPos,it=[],p,g,k,b,w,y;if(u=="row"){if(t.isNullOrUndefined(h[parseInt(l.split(",")[0])+1])){p=parseInt(l.split(",")[0])+1;do it=n.map(s[p],function(n){if(ut.indexOf(parseInt(n.Index.split(",")[1]))>-1)return n.Span="Block",n}),h[p]=it,p++;while(p<et&&!r.model.enableCollapseByDefault)}}else if(parseInt(h[0][parseInt(l.split(",")[1])+1].Index.split(",")[1])!=parseInt(l.split(",")[1])+1)for(y=0;y<s.length;y++){p=parseInt(l.split(",")[1])+1;do g=s[y][p],rt.indexOf(parseInt(g.Index.split(",")[0]))>-1&&(g.Span="Block",h[y].splice(p,0,g)),p++;while(p<ft&&!r.model.enableCollapseByDefault)}if(u=="row"){for(w=0,y=0;y<h.length;y++)if(!t.isNullOrUndefined(h[y])){k=tt;b=0;do k==tt&&h[y].splice(k,1,c[w][b]),k++,b++,h[y].splice(k,0,c[w][b]);while(t.isNullOrUndefined(c[w][b])||parseInt(c[w][b].Index.split(",")[1])<parseInt(a.split(",")[1]));w++}}else for(w=c.length-1,y=ot;y>=e;y--)t.isNullOrUndefined(c[w])||h.splice(y,1,c[w]),w--;return t.Pivot._drillSpanCalculation(h,r)},_getIndices:function(i,r,u,f,e,o,s){var c=n.map(f,function(n){if(!t.isNullOrUndefined(n))return parseInt(n[0].Index.split(",")[0])}),l=n.map(f[0],function(n){if(!t.isNullOrUndefined(n))return parseInt(n.Index.split(",")[1])}),h;if(u.model.enableCollapseByDefault)i=="row"?c.push(parseInt(e.split(",")[0])+1):l.push(parseInt(e.split(",")[1])+1);else{var a=r[parseInt(o.split(",")[0])*s+parseInt(o.split(",")[1])],p=parseInt(a.Index.split(",")[0]),w=parseInt(a.Index.split(",")[1]),v=a.ColSpan,y=a.RowSpan;if(i=="row")for(h=p+1;h<v;h++)c.indexOf(h)==-1&&c.push(h);else for(h=w+1;h<y;h++)l.indexOf(h)==-1&&l.push(h)}return{columnIndices:c,rowIndices:l,colSpan:v,rowSpan:y}},_sliceJson:function(i,r,u,f,e,o,s,h,c,l){var w=[],kt=i.values.length>0&&i.values[0].axis!="rows"?1:0,tt,rt,et,vt,yt,pt,b,g,p,d,it,k,ht,v,y,a;if(e._drillAction=="drilldown"){var nt=r=="row"?"0,"+parseInt(s.split(",")[1]):parseInt(s.split(",")[0])+",0",wt=r=="row"?"0,"+parseInt(h.split(",")[1]):parseInt(h.split(",")[0])+",0",bt=t.Pivot._getIndices(r,o,e,l,s,h,c),dt=bt.columnIndices,gt=bt.rowIndices,ct=parseInt(nt.split(",")[0])*c+c,rt=0;nt=r=="row"?nt:parseInt(nt.split(",")[0])*c+",0";do{for(tt=r=="row"?parseInt(nt.split(",")[1]):parseInt(nt.split(",")[0]),rt=r=="row"?0:rt;!t.isNullOrUndefined(o[tt])&&(r=="column"?ct>tt:!0);)(r=="row"?dt.indexOf(parseInt(o[tt].Index.split(",")[0]))>-1:gt.indexOf(parseInt(o[tt].Index.split(",")[1]))>-1)&&(t.isNullOrUndefined(w[rt])&&(w[rt]=[]),w[rt].push(o[tt]),r=="row"&&rt++),tt=tt+(r=="row"?c:1);r=="column"&&(ct=ct+c,rt++);nt=r=="row"?"0,"+(parseInt(nt.split(",")[1])+1):parseInt(nt.split(",")[0])+c+",0"}while(nt!=(r=="row"?"0,"+(parseInt(wt.split(",")[1])+1):(parseInt(wt.split(",")[0])+1)*c+",0"));if((r=="row"?f-1>parseInt(e._drillParams.currPos.split(",")[0])+1:u-kt-1>parseInt(e._drillParams.currPos.split(",")[1])+1)&&e.model.enableCollapseByDefault){y=r=="row"?0:parseInt(e._drillParams.currPos.split(",")[1])+1;v=r=="row"?parseInt(e._drillParams.currPos.split(",")[0])+1:0;do{if(et=!1,!t.isNullOrUndefined(w[v])&&!t.isNullOrUndefined(w[v][y])&&w[r=="row"?v-1:v][r=="row"?y:y-1].CSS.indexOf("summary")==-1&&(w[v][y].CSS=="rowheader"||w[v][y].CSS=="colheader"||w[v][y].CSS=="none"||w[v][y].Value==""))if(r=="row")for(a=0;a<w.length;a++)t.isNullOrUndefined(w[a])||(w[a].splice(y,1),et=!0);else delete w[v],et=!0;r=="row"?y=et?y:y+1:v=v+1}while(r=="row"?y<w[0].length:v<w.length)}var v=parseInt(e._drillParams.currPos.split(",")[0])+(r=="row"?1:0),ut="",lt=0,at=0,y=parseInt(e._drillParams.currPos.split(",")[1])+1;if(e.model.enableCollapseByDefault)do{if(r=="row"?v==parseInt(e._drillParams.currPos.split(",")[0])+1:y==parseInt(e._drillParams.currPos.split(",")[1])+1){for(a=r=="row"?w[v].length-1:w.length-1;a>=0;a--)if((r!="column"||!t.isNullOrUndefined(w[a]))&&(p=r=="row"?w[v][a]:w[a][y],p.CSS.indexOf("summary")>-1)){p.State=2;p.Expander=1;var ot=v,d=y,st=0;if(r=="row"){do st++,ot++;while(!t.isNullOrUndefined(l[ot])&&(l[ot][0].CSS=="none"||l[ot][0].Value==""));p.ColSpan=st}else{do st++,d++;while(!t.isNullOrUndefined(l[0][d])&&(l[0][d].CSS=="none"||l[0][d].Value==""));p.RowSpan=st}p.Value=p.Value.replace(" Total","")}}else for(vt="",r=="column"&&(vt=n.map(w,function(n,i){if(!t.isNullOrUndefined(n))return i})[0]),a=r=="row"?w[v].length-1:w.length-1;a>=0;a--)r=="column"&&t.isNullOrUndefined(w[a])||(p=r=="row"?w[v][a]:w[a][y],yt=p.Value.replace(" Total",""),ut=ut==""&&p.Value.replace(" Total","")!=""?p.Value.replace(" Total",""):p.Value.replace(" Total","")!=""&&ut!=p.Value.replace(" Total","")?p.Value.replace(" Total",""):ut,yt!=""&&yt!=ut||a==0||vt==a?(p.CSS=r=="row"?"rowheader":"colheader",p.State=1,p.Expander=1,p.Value=ut,r=="row"?p.RowSpan=v>parseInt(e._drillParams.currPos.split(",")[0])-1?lt:p.RowSpan:p.ColSpan=y>parseInt(e._drillParams.currPos.split(",")[1])-1?at:p.ColSpan,lt=1,at=1):(p.CSS.indexOf("summary")>-1&&(r=="row"?p.ColSpan=parseInt(e._drillParams.currPos.split(",")[0])+2-v:p.RowSpan=parseInt(e._drillParams.currPos.split(",")[1])+2-y),lt++,at++));r=="row"?v--:y--}while(r=="row"?v>=0:y>=0)}else{var ft=t.Pivot._getEnginePos(s,h,l),v=ft.colPos,y=ft.rowPos;if(r=="row"){for(pt=0,n.each(l[0],function(n,t){if(parseInt(t.Index.split(",")[1])==parseInt(s.split(",")[1]))return pt=n,!1}),a=parseInt(s.split(",")[1]);a<parseInt(h.split(",")[1]);a++)for(b=0;b<l.length;b++)if(!t.isNullOrUndefined(l[b])){for(g="",p="",d=pt;d<l[b].length;d++)if(parseInt(l[b][d].Index.split(",")[1])==a){p=l[b][d];g=d;l[b].splice(d,1);break}t.isNullOrUndefined(l[b][g])||l[b][g].CSS!="none"&&l[b][g].Value!=""&&(l[b][g].CSS!="rowheader"||l[b][g].Expander!=0)||p.CSS!="rowheader"||(l[b][g].CSS="rowheader",l[b][g].Value=p.Value,l[b][g].State=p.State,l[b][g].Expander=p.Expander)}}else for(it=[],a=parseInt(s.split(",")[0]);a<parseInt(h.split(",")[0]);a++)if(it=it.length==0&&!t.isNullOrUndefined(l[a])?l[a]:it,delete l[a],parseInt(h.split(",")[0])-a==1){k=0;do(l[a+1][k].CSS=="none"||l[a+1][k].Value==""||l[a+1][k].CSS=="colheader"&&l[a+1][k].Expander==0)&&(l[a+1][k].CSS=it[k].CSS,l[a+1][k].Value=it[k].Value,l[a+1][k].State=it[k].State,l[a+1][k].Expander=it[k].Expander),k++;while(k<=parseInt(h.split(",")[1]))}if(ht=r=="row"?n.map(l[parseInt(h.split(",")[0])+1],function(n){if(n.CSS=="rowheader"||n.CSS.indexOf("summary")>-1)return n}).length:n.map(l,function(n){if(!t.isNullOrUndefined(n)&&!t.isNullOrUndefined(n[y+1])&&(n[y+1].CSS=="colheader"||n[y+1].CSS.indexOf("summary")>-1))return n}).length,ht==0)if(r=="row"){v=parseInt(h.split(",")[0])+1;do delete l[v],v++;while(!t.isNullOrUndefined(l[v])&&(l[v][0].CSS=="none"||l[v][0].Value==""))}else{y=parseInt(h.split(",")[1])+1;do{for(a=0;a<l.length;a++)t.isNullOrUndefined(l[a])||(l[a]=n.map(l[a],function(n){if(parseInt(n.Index.split(",")[1])!=y)return n}));y++}while(y<l[0][0].RowSpan-(i.values.length>0&&i.values[0].axis!="rows"?1:0))}if(ft=t.Pivot._getEnginePos(s,h,l),v=ft.colPos,y=ft.rowPos,r=="row"){for(a=0;a<l[parseInt(h.split(",")[0])].length;a++)if(l[parseInt(h.split(",")[0])][a].Index==h){l[parseInt(h.split(",")[0])][a].Value=l[parseInt(h.split(",")[0])][a].Value.replace(" Total","");l[parseInt(h.split(",")[0])][a].State=2;l[parseInt(h.split(",")[0])][a].ColSpan=ht==0?l[parseInt(h.split(",")[0])][a].ColSpan-1:l[parseInt(h.split(",")[0])][a].ColSpan;break}}else l[v][y].Value=l[v][y].Value.replace(" Total",""),l[v][y].State=2,l[v][y].RowSpan=ht==0?l[v][y].RowSpan-1:l[v][y].RowSpan;w=t.Pivot._drillSpanCalculation(l,e)}return w},_getDrillRange:function(i,r,u,f,e){var o=0,v=0,l="",h="",y=f,a=r=="row"?u._drillParams.currRow.split(">#>"):u._drillParams.currCol.split(">#>"),c,s;do{if(c=r=="row"?parseInt((o-1)/f):v,c+1==a.length)if((e[o].CSS!="none"&&e[o].Value!=""||t.isNullOrUndefined(e[r=="row"?o-f:o-1])||typeof e[r=="row"?o-f:o-1].Value!="string"||e[r=="row"?o-f:o-1].Value.replace(" Total","")!=n.trim(a[c<1?0:c-1]))&&(typeof e[o].Value!="string"||e[o].Value==""||e[o].Value.replace(" Total","")==n.trim(a[c]))||l=="")typeof e[o].Value=="string"&&e[o].Value.replace(" Total","")==n.trim(a[c])&&(l=l==""?e[o].Index:l);else{for(s=r=="row"?o-1:o-f;!t.isNullOrUndefined(e[s])&&e[s].CSS!="summary rstot"&&e[s].CSS!="summary cstot"&&e[s].CSS!="summary stot";)s=r=="row"?s-1:s-f;t.isNullOrUndefined(e[s])||(h=e[s].Index);break}else e[o].Value.replace(" Total","")==n.trim(a[c])&&(o=r=="row"?y*(c+1)+(o-1):o-y+1);o=r=="row"?o+1:o+f;v=o%f;r=="column"&&o>e.length&&(v++,o=v)}while(e.length>o&&h=="");return u._drillAction=="drillup"&&u.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&(r=="column"&&i.values.length>0&&i.values[0].axis!="row"?h=parseInt(h.split(",")[0])-(i.values.length-1)+","+h.split(",")[1]:r=="row"&&i.values.length>0&&i.values[0].axis=="row"&&(h=h.split(",")[0]+","+(parseInt(h.split(",")[1])-(i.values.length-1)))),{startPos:l,endPos:h}},_drillSpanCalculation:function(i,r){for(var k,tt,h,f,s,e,o=r.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?JSON.parse(r.getOlapReport()).PivotCalculations:r.getOlapReport().values,w=r.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?JSON.parse(r.getOlapReport()).PivotColumns:r.getOlapReport().columns,b=r.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?JSON.parse(r.getOlapReport()).PivotRows:r.getOlapReport().rows,d=[],g=[],nt=n.map(i,function(n){if(!t.isNullOrUndefined(n))return n[0]}).length,u=0;u<i[0].length;u++){for(s=0,k=0,e=0;e<i.length;e++)t.isNullOrUndefined(i[e])||t.isNullOrUndefined(i[e][u])||(s++,i[e][u].ColSpan>nt-s+1&&(i[e][u].ColSpan=nt-s+1),i[e][u].CSS!="none"&&i[e][u].Value!=""&&k++);k==0&&d.push(u)}for(u=0;u<i.length;u++)t.isNullOrUndefined(i[u])||(tt=n.map(i[u],function(n,t){if(d.indexOf(t)==-1)return n.RowSpan>=i[u].length-t&&(n.RowSpan=i[u].length-t),n.CSS=="summary value"&&(n.RowSpan=1,n.ColSpan=1),n}),g[u]=tt);i=g;var it=w.length+(o.length>0&&o[0].axis!="rows"?o.length:0),rt=b.length+(o.length>0&&o[0].axis=="rows"?o.length:0),c=-1;do c++;while(!t.isNullOrUndefined(i[0])&&!t.isNullOrUndefined(i[0][c])&&(i[0][c].CSS=="none"||i[0][c].Value==""));i[0][0].RowSpan=c>it?it:c;c=o.length>0&&o[0].axis!="rows"?c-1:c;h=-1;f=0;do h++;while(!t.isNullOrUndefined(i[h])&&(i[h][0].CSS=="none"||i[h][0].Value==""));for(i[0][0].ColSpan=h>rt?rt:h,u=0;u<i[0].length;u++)for(s=o.length>0&&o[0].axis=="rows"?1:0,e=0;e<i[0][0].ColSpan;e++)t.isNullOrUndefined(i[e][u])||(s++,!(u==0&&e==0)&&i[e][u].ColSpan>i[0][0].ColSpan-s+1&&i[0][0].ColSpan-s+1>0&&(i[e][u].ColSpan=i[0][0].ColSpan-s+1));for(u=i[0][0].ColSpan-1;u<i.length;u++)if(s=o.length>0&&o[0].axis!="rows"?1:0,!t.isNullOrUndefined(i[u]))for(e=0;e<i[0][0].RowSpan;e++)s++,!(u==0&&e==0)&&i[u][e].RowSpan>i[0][0].RowSpan-s+1&&i[0][0].RowSpan-s+1>0&&(i[u][e].RowSpan=i[0][0].RowSpan-s+1);if(!t.isNullOrUndefined(b)&&b.length>1){h=o.length>0&&o[0].axis=="rows"?h-1:h;do{i[f]=n.map(i[f],function(n){return n.CSS.indexOf("summary")>-1&&(n.ColSpan=h-f),n});var a=0,v=!1,l="";for(u=i[f].length-1;u>=i[0][0].RowSpan;u--)i[f][u].CSS=="summary rstot"||i[f][u].CSS=="summary stot"?(t.isNullOrUndefined(i[f][u+1])||i[f][u+1].CSS!="rowheader"||(i[f][u+1].RowSpan=a),a=0,v=!0,l=""):i[0][0].RowSpan-u==0?i[f][u].RowSpan=v||l==i[f][u].Value?a+1:i[f][u].RowSpan:(l=i[f][u].Value,a++);f++}while(h>f+(o.length>0&&o[0].axis=="rows"?1:0))}if(!t.isNullOrUndefined(w)&&w.length>1){f=0;do{for(u=0;u<i.length;u++)!t.isNullOrUndefined(i[u])&&i[u][f].CSS.indexOf("summary")>-1&&(i[u][f].RowSpan=c-f);var y=0,v=!1,p="",l="",s=n.grep(i,function(n,i){if(!t.isNullOrUndefined(n)&&n[0].CSS!="none"&&n[0].Value!="")return p=p==""?i:p,n}).length;for(u=i.length-1;u>=p;u--)s-=!t.isNullOrUndefined(i[u])&&i[u][0].CSS!="none"&&i[u][0].Value!=""?1:0,t.isNullOrUndefined(i[u])||i[u][f].CSS!="summary cstot"&&i[u][f].CSS!="summary stot"?s!=0||t.isNullOrUndefined(i[u])?t.isNullOrUndefined(i[u])||(l=i[u][f].Value,y++):i[u][f].ColSpan=v||l==i[u][f].Value?y+1:i[u][f].ColSpan:(t.isNullOrUndefined(i[u+1])||t.isNullOrUndefined(i[u+1][f])||i[u+1][f].CSS!="rowheader"&&i[u+1][f].CSS!="colheader"||(i[u+1][f].ColSpan=y),y=0,v=!0,l="");f++}while(c>f+(o.length>0&&o[0].axis!="rows"?1:0))}return i},_calculatePagingSpan:function(i,r){var u,s,f;if(i!=null&&i!=""&&i.length>0){for(r._rowCount=0,u=jQuery.extend(!0,[],i),r._compJson=r._drillAction==""?jQuery.extend(!0,[],u):r._compJson,r._croppedJson=t.Pivot._cropJson(r,u),u=n.extend(!0,[],r._croppedJson),f=0;f<u.length;f++)if(parseInt(u[f].Index.split(",")[0])==0)r._rowCount++;else break;var e=r.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?JSON.parse(r.getOlapReport()).PivotRows.length:r.getOlapReport().rows.length,h=r.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?JSON.parse(r.getOlapReport()).PivotColumns.length:r.getOlapReport().columns.length,o=r.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?JSON.parse(r.getOlapReport()).PivotCalculations.length:r.getOlapReport().values.length,l=u.length/r._rowCount,c=u[0].RowSpan==0?0:u[0].RowSpan-1;for(f=c+r.model.dataSource.pagerOptions.seriesPageSize;f<u.length&&f<r._rowCount;f=f+r.model.dataSource.pagerOptions.seriesPageSize)if(u[f].CSS.indexOf("summary")>-1||u[f].CSS.indexOf("rowheader")>-1&&u[f].Expander==0&&u[f+r._rowCount]!=null&&u[f+r._rowCount].CSS.indexOf("value")>-1)continue;else e>1&&t.Pivot._calculateRowPagingSpan(f,u,1,e,r);for(s=u[0].ColSpan==0?0:u[0].ColSpan-1,f=(s+r.model.dataSource.pagerOptions.categoricalPageSize)*r._rowCount;f<u.length;f=f+r.model.dataSource.pagerOptions.categoricalPageSize*r._rowCount)if(u[f].CSS.indexOf("summary")>-1&&o<=1)continue;else if(r.model.dataSource.pagerOptions.categoricalPageSize==1&&(u[f].CSS.indexOf("stot")>-1&&u[f+r._rowCount]!=null&&u[f+r._rowCount].CSS.indexOf("gtot")>-1||u[f].CSS.indexOf("gtot")>-1&&u[f+r._rowCount]==null))continue;else t.Pivot._calculateColumnPagingSpan(f,u,1,h,o,r);r._relPagingRecords=jQuery.extend(!0,[],u);r._rowCount=0}},_generatePagingRecords:function(i){var e,nt,r,it,a,u,f;if(i._relPagingRecords!=null&&i._relPagingRecords.length>0){for(e=n.extend(!0,[],i._relPagingRecords),i._rowCount=0,r=0;r<e.length;r++)if(parseInt(e[r].Index.split(",")[0])==0)i._rowCount++;else break;var tt=e.length/i._rowCount,c=e[0].RowSpan==0?0:e[0].RowSpan-1,l=e[0].ColSpan==0?0:e[0].ColSpan-1,v=Math.ceil((tt-(l+1))/i.model.dataSource.pagerOptions.categoricalPageSize),y=Math.ceil((i._rowCount-(c+1))/i.model.dataSource.pagerOptions.seriesPageSize);i.model.dataSource.pagerOptions.categoricalCurrentPage=i.model.dataSource.pagerOptions.categoricalCurrentPage>v?v:i.model.dataSource.pagerOptions.categoricalCurrentPage;i.model.dataSource.pagerOptions.seriesCurrentPage=i.model.dataSource.pagerOptions.seriesCurrentPage>y?y:i.model.dataSource.pagerOptions.seriesCurrentPage;i._seriesPageCount=i.model.enablePaging?i._rowCount-(c+1):y;i._categPageCount=i.model.enablePaging?tt-(l+1):v;i._categCurrentPage=i.model.dataSource.pagerOptions.categoricalCurrentPage;i._seriesCurrentPage=i.model.dataSource.pagerOptions.seriesCurrentPage;var s=[],h=c+1+i.model.dataSource.pagerOptions.seriesPageSize,it=l+1+i.model.dataSource.pagerOptions.categoricalPageSize,rt,r,ut,o,p,w,b,k,d,g;for(rt=r=ut=o=p=w=b=k=d=g=0,u=0;u<c+1;u++){for(f=0;f<l+1;f++)e[r]!=null&&(s[o]=e[r],r=r+i._rowCount,o=o+h);r=++rt;o=++ut}for(p=r=c+(i.model.dataSource.pagerOptions.seriesCurrentPage==0||i.model.dataSource.pagerOptions.seriesCurrentPage==1?0:(i.model.dataSource.pagerOptions.seriesCurrentPage-1)*i.model.dataSource.pagerOptions.seriesPageSize)+1,w=o=c+1,u=0;u<i.model.dataSource.pagerOptions.seriesPageSize;u++){for(f=0;f<l+1;f++)e[r]!=null&&(s[o]=e[r],r=r+i._rowCount,o=o+h);if(r=++p,o=++w,r>i._rowCount-1)break}for(b=r=(l+1)*i._rowCount+(i.model.dataSource.pagerOptions.categoricalCurrentPage==0||i.model.dataSource.pagerOptions.categoricalCurrentPage==1?0:(i.model.dataSource.pagerOptions.categoricalCurrentPage-1)*i.model.dataSource.pagerOptions.categoricalPageSize*i._rowCount),k=o=(l+1)*h,u=0;u<c+1;u++){for(f=0;f<i.model.dataSource.pagerOptions.categoricalPageSize;f++)e[r]!=null&&(s[o]=e[r],r=r+i._rowCount,o=o+h);if(r=++b,o=++k,e[r]==null)break}for(d=r=(l+1)*i._rowCount+(i.model.dataSource.pagerOptions.categoricalCurrentPage==0||i.model.dataSource.pagerOptions.categoricalCurrentPage==1?0:(i.model.dataSource.pagerOptions.categoricalCurrentPage-1)*i.model.dataSource.pagerOptions.categoricalPageSize*i._rowCount)+c+(i.model.dataSource.pagerOptions.seriesCurrentPage==0||i.model.dataSource.pagerOptions.seriesCurrentPage==1?0:(i.model.dataSource.pagerOptions.seriesCurrentPage-1)*i.model.dataSource.pagerOptions.seriesPageSize)+1,g=o=(l+1)*h+c+1,nt=0,u=0;u<i.model.dataSource.pagerOptions.seriesPageSize;u++){for(f=0;f<i.model.dataSource.pagerOptions.categoricalPageSize;f++)e[r]!=null&&(s[o]=e[r],nt=parseInt(e[r].Index.split(",")[1]),r=r+i._rowCount,o=o+h);if(r=++d,o=++g,e[r]==null||nt>parseInt(e[r].Index.split(",")[1]))break}for(i._rowCount=0,s=s.filter(function(n){return n!=null}),h=0,r=0;r<s.length;r++)if(parseInt(s[r].Index.split(",")[0])==0)h++;else break;for(it=s.length/h,u=0;u<it;u++)for(f=0;f<h;f++)t.isNullOrUndefined(s[u*h+f])||(s[u*h+f].Index=""+u+","+f+"");for(a=t.Pivot._drillSpanCalculation(t.Pivot._jsonToEngine(s),i),u=0;u<a.length;u++)for(f=0;f<a[u].length;f++)a[u][f].Index=u+","+f;return n.map(a,function(n){return n})}},_calculateRowPagingSpan:function(n,i,r,u,f){var o,e;if(i[n].CSS.indexOf("summary")>-1||i[n].CSS.indexOf("rowheader")>-1&&i[n].Expander==0&&r<u&&i[n+f._rowCount]!=null&&i[n+f._rowCount].CSS.indexOf("value")>-1)return!1;if(i[n].Expander==1&&i[n].RowSpan>1)i[n+1].RowSpan=i[n].RowSpan-1,i[n].RowSpan=1,i[n+1].Expander=i[n].Expander,i[n+1].State=i[n].State,i[n+1].Value=i[n].Value,i[n+1].CSS=i[n].CSS,i[n+f._rowCount].CSS.indexOf("value")==-1&&r<u-1&&t.Pivot._calculateRowPagingSpan(n+f._rowCount,i,r+1,u,f);else if(i[n].Expander==0&&i[n+f._rowCount]!=null&&r<u&&i[n+f._rowCount].CSS.indexOf("value")==-1)for(o=1,e=n-1;e>=0;e--)if(o++,i[e].Expander==1){i[e].RowSpan!=o&&(i[n+1].RowSpan=i[e].RowSpan-o,i[e].RowSpan=o,i[n+1].Expander=i[e].Expander,i[n+1].State=i[e].State,i[n+1].Value=i[e].Value,i[n+1].CSS=i[e].CSS,i[n+f._rowCount].CSS.indexOf("value")==-1&&r<u-1&&t.Pivot._calculateRowPagingSpan(n+f._rowCount,i,r+1,u,f));break}},_calculateColumnPagingSpan:function(n,i,r,u,f,e){var s,o;if(i[n].CSS.indexOf("summary")>-1&&f<=1)return!1;if(i[n+e._rowCount]!=null&&(i[n].Expander==1&&i[n].ColSpan>1||i[n].CSS.indexOf("summary")>-1&&i[n].ColSpan>1||r==u&&f>1&&i[n].ColSpan>1))i[n+e._rowCount].ColSpan=i[n].ColSpan-1,i[n].ColSpan=1,i[n+e._rowCount].Expander=i[n].Expander,i[n+e._rowCount].RowSpan=i[n].RowSpan,i[n+e._rowCount].State=i[n].State,i[n+e._rowCount].Value=i[n].Value,i[n+e._rowCount].CSS=i[n].CSS,r<u&&i[n].CSS.indexOf("summary")==-1&&t.Pivot._calculateColumnPagingSpan(n+1,i,r+1,u,f,e);else if(i[n].Expander==0&&i[n+e._rowCount]!=null&&i[n+e._rowCount].Expander!=1)for(s=1,o=n-e._rowCount;o>=0;o=o-e._rowCount)if(s++,i[o].Expander==1||i[o].CSS.indexOf("summary")>-1&&i[o].ColSpan>1||r==u&&f>1&&i[o].ColSpan>1){i[o].ColSpan!=s&&(i[n+e._rowCount].ColSpan=i[o].ColSpan-s,i[o].ColSpan=s,i[n+e._rowCount].RowSpan=i[o].RowSpan,i[n+e._rowCount].Expander=i[o].Expander,i[n+e._rowCount].State=i[o].State,i[n+e._rowCount].Value=i[o].Value,i[n+e._rowCount].CSS=i[o].CSS,r<u&&i[o].CSS.indexOf("summary")==-1&&t.Pivot._calculateColumnPagingSpan(n+1,i,r+1,u,f,e));break}},createAdvanceFilterTag:function(i,r){var f="",k=t.buildTag("li.e-separator").css("margin-left","29px")[0].outerHTML,e=r.element.hasClass("e-pivotschemadesigner")?r.model.pivotControl.model.operationalMode:r.model.operationalMode,b=r.element.hasClass("e-pivotschemadesigner")?r.model.pivotControl.model.analysisMode:r.model.analysisMode,h,c,o,a,v,u,s,y,l,p,w;if(i.action=="filterTag")f=[{id:"ascOrder",text:r._getLocalizedLabels("SortAtoZ"),parentId:null,spriteCssClass:"e-ascImage e-icon"},{id:"descOrder",text:r._getLocalizedLabels("SortZtoA"),parentId:null,spriteCssClass:"e-descImage e-icon"},{id:"clearSorting",text:r._getLocalizedLabels("ClearSorting"),parentId:null,spriteCssClass:"e-clrSort e-icon"},{id:"sep1",parentId:null,text:"",spriteCssClass:"e-seperator"},{id:"clearAllFilters",text:r._getLocalizedLabels("ClearFilterFrom"),parentId:null,spriteCssClass:"e-clrFilter e-icon"},{id:"labelFilterBtn",text:r._getLocalizedLabels("LabelFilters"),parentId:null},{id:"valueFilterBtn",text:r._getLocalizedLabels("ValueFilters"),parentId:null},{id:"labelClearFilter",parentId:"labelFilterBtn",text:r._getLocalizedLabels("ClearFilter"),spriteCssClass:"e-clrFilter e-icon"},{id:"sep2",parentId:"labelFilterBtn",text:"",spriteCssClass:"e-seperator"},{id:"equals_labelFilter",parentId:"labelFilterBtn",text:r._getLocalizedLabels("Equals"),spriteCssClass:"sprite"},{id:"notequals_labelFilter",parentId:"labelFilterBtn",text:r._getLocalizedLabels("DoesNotEquals")+"...",spriteCssClass:"sprite"},{id:"sep3",parentId:"labelFilterBtn",text:"",spriteCssClass:"e-seperator"},{id:"beginswith",parentId:"labelFilterBtn",text:r._getLocalizedLabels("BeginsWith")+"...",spriteCssClass:"sprite"},{id:"notbeginswith",parentId:"labelFilterBtn",text:r._getLocalizedLabels("DoesNotBeginsWith")+"...",spriteCssClass:"sprite"},{id:"endswith",parentId:"labelFilterBtn",text:r._getLocalizedLabels("EndsWith")+"...",spriteCssClass:"sprite"},{id:"notendswith",parentId:"labelFilterBtn",text:r._getLocalizedLabels("DoesNotEndsWith")+"...",spriteCssClass:"sprite"},{id:"sep4",parentId:"labelFilterBtn",text:"",spriteCssClass:"e-seperator"},{id:"contains",parentId:"labelFilterBtn",text:r._getLocalizedLabels("Contains")+"...",spriteCssClass:"sprite"},{id:"notcontains",parentId:"labelFilterBtn",text:r._getLocalizedLabels("DoesNotContains")+"...",spriteCssClass:"sprite"},{id:"sep5",parentId:"labelFilterBtn",text:"",spriteCssClass:"e-seperator"},{id:"greaterthan_labelFilter",parentId:"labelFilterBtn",text:r._getLocalizedLabels("GreaterThan")+"...",spriteCssClass:"sprite"},{id:"greaterthanorequalto_labelFilter",parentId:"labelFilterBtn",text:r._getLocalizedLabels("GreaterThanOrEqualTo")+"...",spriteCssClass:"sprite"},{id:"lessthan_labelFilter",parentId:"labelFilterBtn",text:r._getLocalizedLabels("LessThan")+"...",spriteCssClass:"sprite"},{id:"lessthanorequalto_labelFilter",parentId:"labelFilterBtn",text:r._getLocalizedLabels("LessThanOrEqualTo")+"...",spriteCssClass:"sprite"},{id:"valueClearFilter",parentId:"valueFilterBtn",text:r._getLocalizedLabels("ClearFilter"),spriteCssClass:"e-clrFilter e-icon"},{id:"sep6",parentId:"valueFilterBtn",text:"",spriteCssClass:"e-seperator"},{id:"equals_valueFilter",parentId:"valueFilterBtn",text:r._getLocalizedLabels("Equals"),spriteCssClass:"equals"},{id:"notequals_valueFilter",parentId:"valueFilterBtn",text:r._getLocalizedLabels("DoesNotEquals")+"...",spriteCssClass:"sprite"},{id:"sep7",parentId:"valueFilterBtn",text:"",spriteCssClass:"e-seperator"},{id:"greaterthan_valueFilter",parentId:"valueFilterBtn",text:r._getLocalizedLabels("GreaterThan")+"...",spriteCssClass:"sprite"},{id:"greaterthanorequalto_valueFilter",parentId:"valueFilterBtn",text:r._getLocalizedLabels("GreaterThanOrEqualTo")+"...",spriteCssClass:"sprite"},{id:"lessthan_valueFilter",parentId:"valueFilterBtn",text:r._getLocalizedLabels("LessThan")+"...",spriteCssClass:"sprite"},{id:"lessthanorequalto_valueFilter",parentId:"valueFilterBtn",text:r._getLocalizedLabels("LessThanOrEqualTo")+"...",spriteCssClass:"sprite"},{id:"sep8",parentId:"valueFilterBtn",text:"",spriteCssClass:"e-seperator"},{id:"between",parentId:"valueFilterBtn",text:r._getLocalizedLabels("Between")+"...",spriteCssClass:"sprite"},{id:"notbetween",parentId:"valueFilterBtn",text:r._getLocalizedLabels("NotBetween")+"...",spriteCssClass:"sprite"},{id:"topCount",parentId:"valueFilterBtn",text:r._getLocalizedLabels("Top10")+"...",spriteCssClass:"sprite"}],(e==t.Pivot.OperationalMode.ClientMode||e==t.Pivot.OperationalMode.ServerMode&&b==t.Pivot.AnalysisMode.Pivot)&&f.splice(f.length-1,2);else if(i.action=="clearFilter")f=t.buildTag("div.clearSorting",t.buildTag("span.e-clrSort","",{padding:"0px 10px 0px 4px"}).addClass("e-icon").attr("aria-label","clear sort")[0].outerHTML+t.buildTag("span.clearSortText","Clear Sorting",{padding:"5px 0px"})[0].outerHTML)[0].outerHTML+t.buildTag("div.separator",{padding:"5px 0px"})[0].outerHTML+t.buildTag("div.clearAllFilters",t.buildTag("span.e-clrFilter","",{padding:"0px 10px 0px 4px"}).addClass("e-icon").attr("aria-label"," clear filter")[0].outerHTML+t.buildTag("span.clearFltrText",'Clear Filter From"'+i.selectedLevel.text+'"',{padding:"5px 0px"})[0].outerHTML)[0].outerHTML;else if(i.action=="sort")f=t.buildTag("div#"+this._id+"_sortDiv.sortDiv",t.buildTag("li#"+this._id+"_ascOrder.e-ascOrder",t.buildTag("span.e-ascImage").addClass("e-icon").attr("aria-label","ascending")[0].outerHTML+r._getLocalizedLabels("Sort")+" A to Z")[0].outerHTML+t.buildTag("li#"+this._id+"_descOrder.e-descOrder",t.buildTag("span.e-descImage").addClass("e-icon").attr("aria-label","descending")[0].outerHTML+r._getLocalizedLabels("Sort")+" Z to A")[0].outerHTML)[0].outerHTML;else if(i.action=="labelFilterDlg"||i.action=="valueFilterDlg"){if(h="",c=[],i.action=="labelFilterDlg")h=t.buildTag("table.labelfilter",t.buildTag("tr",t.buildTag("td",r._getLocalizedLabels("LabelFilterLabel")).attr("colspan","2")[0].outerHTML)[0].outerHTML+t.buildTag("tr",t.buildTag("td","<input type='text' id='"+this._id+"_filterOptions' class='filterOptions'  style='width:220px'/>")[0].outerHTML+t.buildTag("td.filterValuesTd","<input type='text' id='"+this._id+"_filterValue1'  value='"+(i.filterInfo.length>0?e==t.Pivot.OperationalMode.ClientMode?i.filterInfo[0].values[0]:i.filterInfo[0].value1:"")+"' style='display:inline; width:160px; height:19px; margin-left:7px;' class='e-filterValues'/><\/br>")[0].outerHTML)[0].outerHTML)[0].outerHTML,c=[{value:"equals",option:r._getLocalizedLabels("Equals").toLowerCase()},{value:"not equals",option:r._getLocalizedLabels("DoesNotEquals").toLowerCase()},{value:"begins with",option:r._getLocalizedLabels("BeginsWith").toLowerCase()},{value:"not begins with",option:r._getLocalizedLabels("DoesNotBeginsWith").toLowerCase()},{value:"ends with",option:r._getLocalizedLabels("EndsWith").toLowerCase()},{value:"not ends with",option:r._getLocalizedLabels("DoesNotEndsWith").toLowerCase()},{value:"contains",option:r._getLocalizedLabels("Contains").toLowerCase()},{value:"not contains",option:r._getLocalizedLabels("DoesNotContains").toLowerCase()},{value:"greater than",option:r._getLocalizedLabels("IsGreaterThan").toLowerCase()},{value:"greater than or equal to",option:r._getLocalizedLabels("IsGreaterThanOrEqualTo").toLowerCase()},{value:"less than",option:r._getLocalizedLabels("IsLessThan").toLowerCase()},{value:"less than or equal to",option:r._getLocalizedLabels("IsLessThanOrEqualTo").toLowerCase()},];else{if(o=[],v=n(i.selectedArgs.element).attr("id")=="between"||n(i.selectedArgs.element).attr("id")=="notbetween"||i.selectedArgs.value=="between"||i.selectedArgs.value=="not between"?"4":"3",r.element.find(".e-cubeTreeView ").length>0)o=n.map(r.element.find(".e-cubeTreeView [data-tag*='[Measures]'] "),function(t){return{option:n(t).text(),value:n(t).attr("data-tag")}});else for(u=r.element.hasClass("e-pivotschemadesigner")?r.element.find(".e-schemaValue .e-pivotButton"):r.element.find(".groupingBarPivot .values .e-pivotButton"),u=r._schemaData&&u.length==0?n(r._schemaData.element.find(".e-schemaValue .e-pivotButton")):u,s=0;s<u.length;s++)o.push({option:n(u[s]).text(),value:n(u[s]).attr("data-tag").split(":")[1]});a=n(i.selectedArgs.element).hasClass("topCount")?t.buildTag("td","<input type='text' id='"+this._id+"_filterOptions' class='filterOptions' />").attr("width","80px")[0].outerHTML+t.buildTag("td","<input type='text' id='"+this._id+"_filterValue1' class='e-filterValues' />").attr("width","50px")[0].outerHTML+t.buildTag("td","<input type='text' id='"+this._id+"_filterMeasures' class='filterMeasures' />").attr("width","180px")[0].outerHTML:t.buildTag("td","<input type='text' id='"+this._id+"_filterMeasures' class='filterMeasures' />").attr("width","180px")[0].outerHTML+t.buildTag("td","<input type='text' id='"+this._id+"_filterOptions' class='filterOptions'/>").attr("width","180px")[0].outerHTML+t.buildTag("td.filterValuesTd","<input type='text' id='"+this._id+"_filterValue1' value='"+(i.filterInfo.length>0?e==t.Pivot.OperationalMode.ClientMode?i.filterInfo[0].values[0]:i.filterInfo[0].value1:"")+"' style='display:inline; width:190px; height:19px;' class='e-filterValues'/>"+(v=="4"?"<span>"+r._getLocalizedLabels("and")+" <\/span><input type='text' id='"+this._id+"_filterValue2' value='"+(i.filterInfo.length>0?e==t.Pivot.OperationalMode.ClientMode&&!t.isNullOrUndefined(i.filterInfo[0].values[1])?i.filterInfo[0].values[1]:e==t.Pivot.OperationalMode.ServerMode&&!t.isNullOrUndefined(i.filterInfo[0].value2)?i.filterInfo[0].value2:"":"")+"' style='display:inline; width:190px; height:19px;' class='e-filterValues'/> <\/br>":"<\/br>"))[0].outerHTML;h=t.buildTag("table.valuefilter",t.buildTag("tr",t.buildTag("td",r._getLocalizedLabels("ValueFilterLabel")).attr("colspan",i.text=="Between"?"4":"3")[0].outerHTML)[0].outerHTML+t.buildTag("tr",a)[0].outerHTML)[0].outerHTML;c=[{value:"equals",option:r._getLocalizedLabels("Equals").toLowerCase()},{value:"not equals",option:r._getLocalizedLabels("DoesNotEquals").toLowerCase()},{value:"greater than",option:r._getLocalizedLabels("IsGreaterThan").toLowerCase()},{value:"greater than or equal to",option:r._getLocalizedLabels("IsGreaterThanOrEqualTo").toLowerCase()},{value:"less than",option:r._getLocalizedLabels("IsLessThan").toLowerCase()},{value:"less than or equal to",option:r._getLocalizedLabels("IsLessThanOrEqualTo").toLowerCase()},{value:"between",option:r._getLocalizedLabels("Between").toLowerCase()},{value:"not between",option:r._getLocalizedLabels("NotBetween").toLowerCase()}]}y=t.buildTag("div",t.buildTag("button#"+this._id+"_filterDlgOKBtn.e-dialogOKBtn",r._getLocalizedLabels("OK")).attr("title",r._getLocalizedLabels("OK").replace(/(<([^>]+)>)/ig,""))[0].outerHTML+t.buildTag("button#"+this._id+"_filterDlgCancelBtn.e-dialogCancelBtn",r._getLocalizedLabels("Cancel")).attr("title",r._getLocalizedLabels("Cancel").replace(/(<([^>]+)>)/ig,""))[0].outerHTML,{float:r.model.enableRTL?"left":"right",margin:"8px 0 6px"})[0].outerHTML;r.element.find(".e-dialog").remove();n(t.buildTag("div#"+this._id+"_filterDialog.filterDialog",h+y,{opacity:"1"})).appendTo("#"+r._id);r.element.find(".filterDialog").ejDialog({enableRTL:r.model.enableRTL,enableResize:!1,cssClass:"e-labelValueFilterDlg",width:"auto",content:"#"+r._id,close:t.proxy(t.Pivot.closePreventPanel,r)});i.action=="valueFilterDlg"&&(r.element.find(".filterMeasures").ejDropDownList({dataSource:o,width:"180px",height:"25px",fields:{text:"option",value:"value"},create:function(){n(this.wrapper.find(".e-input")).focus(function(){n(this).blur()})}}),r._measureDDL=r.element.find(".filterMeasures").data("ejDropDownList"),l=i.filterInfo.length>0&&!t.isNullOrUndefined(i.filterInfo[0].measure)?i.filterInfo[0].measure:o.length>0?o[0].value:"",l!=""&&r._measureDDL.selectItemByValue(l));n(i.selectedArgs.element).attr("id")=="topCount"?(r.element.find("#"+this._id+"_filterOptions").ejDropDownList({dataSource:[{option:"Top",value:"topCount"},{option:"Bottom",value:"BottomCount"}],fields:{text:"option",value:"value"},value:i.filterInfo.length>0?i.filterInfo[0].operator:"topCount",create:function(){n(this.wrapper.find(".e-input")).focus(function(){n(this).blur()})}}),r.element.find("#"+this._id+"_filterValue1").ejNumericTextbox({value:i.filterInfo.length>0?parseInt(i.filterInfo[0].value1):5,minValue:1})):(p=n(i.selectedArgs.element).parent().children('li:not([id^="sep"])').index(i.selectedArgs.element),r.element.find(".filterOptions").ejDropDownList({dataSource:c,width:"180px",height:"25px",fields:{value:"value",text:"option"},selectedIndices:[p-1],change:t.proxy(r._filterOptionChanged,r),create:function(){n(this.wrapper.find(".e-input")).focus(function(){n(this).blur()})}}),n(i.selectedArgs.element).length==0&&(w=r.element.find(".filterOptions").data("ejDropDownList"),w.selectItemByValue(i.selectedArgs.selectedValue)));r.element.find("#"+this._id+"_filterDlgOKBtn").ejButton({type:t.ButtonType.Button,click:t.proxy(r._filterElementOkBtnClick,r)});r.element.find("#"+this._id+"_filterDlgCancelBtn").ejButton({type:t.ButtonType.Button,click:function(){n(".e-dialog").hide();t.Pivot.closePreventPanel(r)}});r.element.find(".e-titlebar").prepend(t.buildTag("div",(i.action=="valueFilterDlg"?r._getLocalizedLabels("ValueFilters")+"(":r._getLocalizedLabels("LabelFilters")+"(")+(r._selectedLevelUniqueName.indexOf(".")==-1?r._selectedLevelUniqueName:r._selectedLevelUniqueName.indexOf(".")<0?r._selectedLevelUniqueName:r._selectedLevelUniqueName.split(".")[2].replace(/\[/g,"").replace(/\]/g,""))+")",{display:"inline"}))}return f}};t.Pivot.SortOrder={None:"none",Ascending:"ascending",Descending:"descending"};t.Pivot.AdvancedFilterType={LabelFilter:"label",ValueFilter:"value"};t.Pivot.ValueFilterOptions={None:"none",Equals:"equals",NotEquals:"notequals",GreaterThan:"greaterthan",GreaterThanOrEqualTo:"greaterthanorequalto",LessThan:"lessthan",LessThanOrEqualTo:"lessthanorequalto",Between:"between",NotBetween:"notbetween"};t.Pivot.LabelFilterOptions={None:"none",BeginsWith:"beginswith",NotBeginsWith:"notbeginswith",EndsWith:"endswith",NotEndsWith:"notendswith",Contains:"contains",NotContains:"notcontains",Equals:"equals",NotEquals:"notequals",GreaterThan:"greaterthan",GreaterThanOrEqualTo:"greaterthanorequalto",LessThan:"lessthan",LessThanOrEqualTo:"lessthanorequalto",Between:"between",NotBetween:"notbetween"};t.Pivot.AnalysisMode={Olap:"olap",Pivot:"pivot"};t.Pivot.OperationalMode={ClientMode:"clientmode",ServerMode:"servermode"}}(jQuery,Syncfusion)});
