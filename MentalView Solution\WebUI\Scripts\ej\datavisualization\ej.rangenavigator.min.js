/*!
*  filename: ej.rangenavigator.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min","./../common/ej.scroller.min","./ej.chart.min","./../common/ej.globalize.min"],n):n()})(function(){(function(n,t,i){t.widget("ejRangeNavigator","ej.datavisualization.RangeNavigator",{ejChart:"",validTags:["div"],defaults:{theme:"",padding:"0",enableAutoResizing:!1,isResponsive:!1,allowSnapping:!1,allowNextValue:!0,sizeSettings:{width:"",height:""},_size:{},locale:"en-US",valueType:"datetime",valueAxisSettings:{rangePadding:"none",range:{min:null,max:null,interval:null},axisLine:{visible:!1},font:{size:"0px"},majorTickLines:{width:0,size:0,visible:!0},majorGridLines:{visible:!1},visible:!1},rangePadding:"none",enableRTL:!1,enableScrollbar:!1,dataSource:"",xName:"x",yName:"y",tooltipSettings:{visible:!0,labelFormat:"MM/dd/yyyy",tooltipDisplayMode:"always",backgroundColor:"#303030",font:{color:"#FFFFFF",family:"Segoe UI",fontStyle:"Normal",size:"10px",opacity:1,weight:"regular"}},zoomPosition:"0",zoomFactor:"1",selectedRangeSettings:{start:"",end:""},selectedData:"",rangeSettings:{start:"",end:""},border:{width:1,color:null,opacity:1},scrollRangeSettings:{start:"",end:""},enableDeferredUpdate:!0,series:"",seriesSettings:"",labelSettings:{style:{font:{color:"#333333",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"},horizontalAlignment:"middle"},higherLevel:{intervalType:null,style:{font:{color:"black",fontFamily:"Segoe UI",fontStyle:"Normal",size:"12px",opacity:1,fontWeight:"regular"},horizontalAlignment:"middle"},gridLineStyle:{color:"#B5B5B5",width:1,dashArray:"20 5 0"},border:{color:"transparent",width:.5},fill:"transparent",position:"top",visible:!0,labelPlacement:"outside",labelIntersectAction:"none"},lowerLevel:{intervalType:null,style:{font:{color:"black",fontFamily:"Segoe UI",fontStyle:"Normal",size:"12px",opacity:1,fontWeight:"regular"},horizontalAlignment:"middle"},gridLineStyle:{color:"#B5B5B5",width:1,dashArray:""},border:{color:"transparent",width:.5},fill:"transparent",position:"bottom",visible:!0,labelPlacement:"outside",labelIntersectAction:"none"}},navigatorStyleSettings:{selectedRegionColor:"#EFEFEF",unselectedRegionColor:"#5EABDE",thumbColor:"#2382C3",thumbRadius:10,leftThumbTemplate:null,rightThumbTemplate:null,thumbStroke:"#303030",background:"#dddddd",border:{color:"transparent",width:.5,dashArray:""},opacity:1,selectedRegionOpacity:0,unselectedRegionOpacity:.3,highlightSettings:{enable:!1,opacity:.5,color:"#006fa0",border:{color:"",width:1}},selectionSettings:{enable:!1,opacity:.5,color:"#0e4a7c",border:{color:"",width:1}},majorGridLineStyle:{color:"#B5B5B5",visible:!0},minorGridLineStyle:{color:"#B5B5B5",visible:!0}},loaded:"",load:"",click:"",doubleClick:"",rightClick:"",rangeChanged:"",scrollEnd:"",scrollStart:"",scrollChanged:"",selectedRangeStart:"",selectedRangeEnd:""},dataTypes:{enableAutoResizing:"boolean",isResponsive:"boolean",allowSnapping:"boolean",allowNextValue:"boolean",dataSource:"data",tooltipSettings:{visible:"boolean",labelFormat:"string",tooltipDisplayMode:"string"},zoomPosition:"string",zoomFactor:"string",selectedData:"string",enableDeferredUpdate:"boolean",series:"array",labelSettings:{higherLevel:{intervalType:"string",position:"string",visible:"boolean",labelPlacement:"string",labelIntersectAction:"string"},lowerLevel:{intervalType:"string",position:"string",visible:"boolean",labelPlacement:"string",labelIntersectAction:"string"}},navigatorStyleSettings:{thumbRadius:"number"}},observables:["selectedRangeStart","selectedRangeEnd"],_tags:[{tag:"series",attr:["xName","yName","dataSource","enableAnimation",[{tag:"points",attr:["x","y","text","isEmpty","fill","visible"]}]],singular:"series"}],_selectedRangeStart:t.util.valueFunction("selectedRangeStart"),_selectedRangeEnd:t.util.valueFunction("selectedRangeEnd"),_series:function(){this.renderNavigator();this._trigger("refresh")},_series_points:function(){this.renderNavigator();this._trigger("refresh")},_themes:{flatlight:{tooltipSettings:{backgroundColor:"#303030",font:{color:"#FFFFFF",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#B5B5B5",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:"#e5e5e5",unselectedRegionColor:"#5EABDE",thumbColor:"#2382C3",thumbRadius:10,thumbStroke:"#303030",background:"#EFEFEF",border:{color:"#606262",width:0},opacity:1,unselectedRegionOpacity:.3,selectedRegionOpacity:0,majorGridLineStyle:{color:"#8c8c8c",visible:!0},minorGridLineStyle:{color:"#8c8c8c",visible:!0}}},azurelight:{tooltipSettings:{backgroundColor:"#303030",font:{color:"#FFFFFF",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#B5B5B5",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:"#e5e5e5",unselectedRegionColor:"#5EABDE",thumbColor:"#2382C3",thumbRadius:10,thumbStroke:"#303030",background:"#EFEFEF",border:{color:"#606262",width:0},opacity:1,unselectedRegionOpacity:.3,selectedRegionOpacity:0,majorGridLineStyle:{color:"#8c8c8c",visible:!0},minorGridLineStyle:{color:"#8c8c8c",visible:!0}}},limelight:{tooltipSettings:{backgroundColor:"#303030",font:{color:"#FFFFFF",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#B5B5B5",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:"#e5e5e5",unselectedRegionColor:"#A9CA44 ",thumbColor:"#AECF49",thumbRadius:10,thumbStroke:"#303030",background:"#EFEFEF",border:{color:"#606262",width:0},opacity:1,unselectedRegionOpacity:.3,selectedRegionOpacity:0,majorGridLineStyle:{color:"#8c8c8c",visible:!0},minorGridLineStyle:{color:"#8c8c8c",visible:!0}}},saffronlight:{tooltipSettings:{backgroundColor:"#303030",font:{color:"#FFFFFF",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#B5B5B5",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:"#e5e5e5",unselectedRegionColor:"#FAA113",thumbColor:"#F9920B",thumbRadius:10,thumbStroke:"#303030",background:"#EFEFEF",border:{color:"#606262",width:0},opacity:1,unselectedRegionOpacity:.3,selectedRegionOpacity:0,majorGridLineStyle:{color:"#8c8c8c",visible:!0},minorGridLineStyle:{color:"#8c8c8c",visible:!0}}},gradientlight:{tooltipSettings:{backgroundColor:"#303030",font:{color:"#FFFFFF",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#B5B5B5",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:[{color:"#bbbbbb",colorStop:"0%"},{color:"#efefef",colorStop:"15%"},{color:"#bbbbbb",colorStop:"85%"},{color:"#efefef",colorStop:"100%"}],unselectedRegionColor:[{color:"#67C1DC",colorStop:"50%"},{color:"#3D93AA",colorStop:"100%"}],thumbColor:[{color:"#67C1DC",colorStop:"50%"},{color:"#3D93AA",colorStop:"100%"}],thumbRadius:10,thumbStroke:"#303030",background:"#EFEFEF",border:{color:"#606262",width:0},opacity:1,unselectedRegionOpacity:.3,selectedRegionOpacity:0,majorGridLineStyle:{color:"#8c8c8c",visible:!0},minorGridLineStyle:{color:"#8c8c8c",visible:!0}}},gradientazure:{tooltipSettings:{backgroundColor:"#303030",font:{color:"#FFFFFF",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#B5B5B5",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:[{color:"#bbbbbb",colorStop:"0%"},{color:"#efefef",colorStop:"15%"},{color:"#bbbbbb",colorStop:"85%"},{color:"#efefef",colorStop:"100%"}],unselectedRegionColor:[{color:"#67C1DC",colorStop:"50%"},{color:"#3D93AA",colorStop:"100%"}],thumbColor:[{color:"#67C1DC",colorStop:"50%"},{color:"#3D93AA",colorStop:"100%"}],thumbRadius:10,thumbStroke:"#303030",background:"#EFEFEF",border:{color:"#606262",width:0},opacity:1,unselectedRegionOpacity:.3,selectedRegionOpacity:0,majorGridLineStyle:{color:"#8c8c8c",visible:!0},minorGridLineStyle:{color:"#8c8c8c",visible:!0}}},gradientlime:{tooltipSettings:{backgroundColor:"#303030",font:{color:"#FFFFFF",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#B5B5B5",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:[{color:"#bbbbbb",colorStop:"0%"},{color:"#efefef",colorStop:"15%"},{color:"#bbbbbb",colorStop:"85%"},{color:"#efefef",colorStop:"100%"}],unselectedRegionColor:[{color:"#A5C14A",colorStop:"50%"},{color:"#738B1F",colorStop:"100%"}],thumbColor:[{color:"#A5C14A",colorStop:"50%"},{color:"#738B1F",colorStop:"100%"}],thumbRadius:10,thumbStroke:"#303030",background:"#EFEFEF",border:{color:"#606262",width:0},opacity:1,unselectedRegionOpacity:.3,selectedRegionOpacity:0,majorGridLineStyle:{color:"#8c8c8c",visible:!0},minorGridLineStyle:{color:"#8c8c8c",visible:!0}}},gradientsaffron:{tooltipSettings:{backgroundColor:"#303030",font:{color:"#FFFFFF",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#B5B5B5",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:[{color:"#bbbbbb",colorStop:"0%"},{color:"#efefef",colorStop:"15%"},{color:"#bbbbbb",colorStop:"85%"},{color:"#efefef",colorStop:"100%"}],unselectedRegionColor:[{color:"#FEB75B",colorStop:"50%"},{color:"#ED7E16",colorStop:"100%"}],thumbColor:[{color:"#FEB75B",colorStop:"50%"},{color:"#ED7E16",colorStop:"100%"}],thumbRadius:10,thumbStroke:"#303030",background:"#EFEFEF",border:{color:"#606262",width:0},opacity:1,unselectedRegionOpacity:.3,selectedRegionOpacity:0,majorGridLineStyle:{color:"#8c8c8c",visible:!0},minorGridLineStyle:{color:"#8c8c8c",visible:!0}}},flatdark:{tooltipSettings:{backgroundColor:"#FFFFFF",font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#FFFFFF",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:"#353635",unselectedRegionColor:"#5EABDE",thumbColor:"#2382C3",thumbRadius:10,thumbStroke:"#FFFFFF",background:"#FFFFFF",opacity:.08,unselectedRegionOpacity:.3,selectedRegionOpacity:0,border:{color:"#FFFFFF",width:0},majorGridLineStyle:{color:"#FFFFFF",visible:!0},minorGridLineStyle:{color:"#FFFFFF",visible:!0}}},material:{tooltipSettings:{backgroundColor:"#303030",font:{color:"#FFFFFF",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#333333",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#333333",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#B5B5B5",width:1}},lowerLevel:{style:{font:{color:"#333333",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:"#f5f5f5",unselectedRegionColor:"#8d8afd",thumbColor:"#8d8afd",thumbRadius:10,thumbStroke:"#000000",background:"#EFEFEF",border:{color:"#606262",width:0},opacity:1,unselectedRegionOpacity:.3,selectedRegionOpacity:0,majorGridLineStyle:{color:"#8c8c8c",visible:!0},minorGridLineStyle:{color:"#8c8c8c",visible:!0}}},office:{tooltipSettings:{backgroundColor:"#303030",font:{color:"#FFFFFF",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#333333",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#333333",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#B5B5B5",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#333333",family:"Segoe UI",style:"Normal",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:"#eaeaea",unselectedRegionColor:"#0078d7",thumbColor:"#0078d7",thumbRadius:10,thumbStroke:"#000000",background:"#EFEFEF",border:{color:"#606262",width:0},opacity:1,unselectedRegionOpacity:.6,selectedRegionOpacity:0,majorGridLineStyle:{color:"#8c8c8c",visible:!0},minorGridLineStyle:{color:"#8c8c8c",visible:!0}}},highcontrast01:{tooltipSettings:{backgroundColor:"#FFFFFF",font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#FFFFFF",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:"#353635",unselectedRegionColor:"#008000",thumbColor:"#008000",thumbRadius:10,thumbStroke:"#FFFFFF",background:"#FFFFFF",opacity:.08,unselectedRegionOpacity:.6,selectedRegionOpacity:0,border:{color:"#FFFFFF",width:0},majorGridLineStyle:{color:"#FFFFFF",visible:!0},minorGridLineStyle:{color:"#FFFFFF",visible:!0}}},highcontrast02:{tooltipSettings:{backgroundColor:"#FFFFFF",font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#FFFFFF",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:"#353635",unselectedRegionColor:"#0000ff",thumbColor:"#0000ff",thumbRadius:10,thumbStroke:"#FFFFFF",background:"#FFFFFF",opacity:.08,unselectedRegionOpacity:.6,selectedRegionOpacity:0,border:{color:"#FFFFFF",width:0},majorGridLineStyle:{color:"#FFFFFF",visible:!0},minorGridLineStyle:{color:"#FFFFFF",visible:!0}}},azuredark:{tooltipSettings:{backgroundColor:"#FFFFFF",font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#FFFFFF",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:"#353635",unselectedRegionColor:"#5EABDE",thumbColor:"#2382C3",thumbRadius:10,thumbStroke:"#FFFFFF",background:"#FFFFFF",opacity:.08,unselectedRegionOpacity:.3,selectedRegionOpacity:0,border:{color:"#FFFFFF",width:0},majorGridLineStyle:{color:"#FFFFFF",visible:!0},minorGridLineStyle:{color:"#FFFFFF",visible:!0}}},limedark:{tooltipSettings:{backgroundColor:"#FFFFFF",font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#FFFFFF",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:"#353635",unselectedRegionColor:"#A9CA44",thumbColor:"#AECF49",thumbRadius:10,thumbStroke:"#FFFFFF",background:"#FFFFFF",opacity:.08,unselectedRegionOpacity:.3,selectedRegionOpacity:0,border:{color:"#FFFFFF",width:0},majorGridLineStyle:{color:"#FFFFFF",visible:!0},minorGridLineStyle:{color:"#FFFFFF",visible:!0}}},saffrondark:{tooltipSettings:{backgroundColor:"#FFFFFF",font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#FFFFFF",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:"#353635",unselectedRegionColor:"#FAA113",thumbColor:"#F9920B",thumbRadius:10,thumbStroke:"#FFFFFF",background:"#FFFFFF",opacity:.08,unselectedRegionOpacity:.3,selectedRegionOpacity:0,border:{color:"#FFFFFF",width:0},majorGridLineStyle:{color:"#FFFFFF",visible:!0},minorGridLineStyle:{color:"#FFFFFF",visible:!0}}},gradientdark:{tooltipSettings:{backgroundColor:"#FFFFFF",font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#FFFFFF",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:[{color:"#0a0a0a",colorStop:"0%"},{color:"#282828",colorStop:"15%"},{color:"#282828",colorStop:"85%"},{color:"#0a0a0a",colorStop:"100%"}],unselectedRegionColor:[{color:"#67C1DC",colorStop:"50%"},{color:"#3D93AA",colorStop:"100%"}],thumbColor:[{color:"#67C1DC",colorStop:"50%"},{color:"#3D93AA",colorStop:"100%"}],thumbRadius:10,thumbStroke:"#FFFFFF",background:"#FFFFFF",opacity:.08,unselectedRegionOpacity:.3,selectedRegionOpacity:0,border:{color:"#FFFFFF",width:0},majorGridLineStyle:{color:"#FFFFFF",visible:!0},minorGridLineStyle:{color:"#FFFFFF",visible:!0}}},gradientazuredark:{tooltipSettings:{backgroundColor:"#FFFFFF",font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#FFFFFF",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:[{color:"#0a0a0a",colorStop:"0%"},{color:"#282828",colorStop:"15%"},{color:"#282828",colorStop:"85%"},{color:"#0a0a0a",colorStop:"100%"}],unselectedRegionColor:[{color:"#67C1DC",colorStop:"50%"},{color:"#3D93AA",colorStop:"100%"}],thumbColor:[{color:"#67C1DC",colorStop:"50%"},{color:"#3D93AA",colorStop:"100%"}],thumbRadius:10,thumbStroke:"#FFFFFF",background:"#FFFFFF",opacity:.08,unselectedRegionOpacity:.3,selectedRegionOpacity:0,border:{color:"#FFFFFF",width:0},majorGridLineStyle:{color:"#FFFFFF",visible:!0},minorGridLineStyle:{color:"#FFFFFF",visible:!0}}},gradientlimedark:{tooltipSettings:{backgroundColor:"#FFFFFF",font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",fontStyle:"Regular",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#FFFFFF",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:[{color:"#0a0a0a",colorStop:"0%"},{color:"#282828",colorStop:"15%"},{color:"#282828",colorStop:"85%"},{color:"#0a0a0a",colorStop:"100%"}],unselectedRegionColor:[{color:"#A5C14A",colorStop:"50%"},{color:"#738B1F",colorStop:"100%"}],thumbColor:[{color:"#A5C14A",colorStop:"50%"},{color:"#738B1F",colorStop:"100%"}],thumbRadius:10,thumbStroke:"#FFFFFF",background:"#FFFFFF",opacity:.08,unselectedRegionOpacity:.3,selectedRegionOpacity:0,border:{color:"#FFFFFF",width:0},majorGridLineStyle:{color:"#FFFFFF",visible:!0},minorGridLineStyle:{color:"#FFFFFF",visible:!0}}},gradientsaffrondark:{tooltipSettings:{backgroundColor:"#FFFFFF",font:{color:"#1E1E1E",family:"Segoe UI",style:"Normal",size:"10px",opacity:1,weight:"regular"}},labelSettings:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},higherLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"},horizontalAlignment:"left"},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"#FFFFFF",width:1,dashArray:"15 5 0"}},lowerLevel:{style:{font:{color:"#FFFFFF",family:"Segoe UI",style:"Regular",size:"13px",opacity:1,weight:"regular"}},border:{color:"transparent",width:0},fill:"transparent",gridLineStyle:{color:"transparent",width:0}}},navigatorStyleSettings:{selectedRegionColor:[{color:"#0a0a0a",colorStop:"0%"},{color:"#282828",colorStop:"15%"},{color:"#282828",colorStop:"85%"},{color:"#0a0a0a",colorStop:"100%"}],unselectedRegionColor:[{color:"#FEB75B",colorStop:"50%"},{color:"#ED7E16",colorStop:"100%"}],thumbColor:[{color:"#FEB75B",colorStop:"50%"},{color:"#ED7E16",colorStop:"100%"}],thumbRadius:10,thumbStroke:"#FFFFFF",background:"#FFFFFF",opacity:.08,unselectedRegionOpacity:.3,selectedRegionOpacity:0,border:{color:"#FFFFFF",width:0},majorGridLineStyle:{color:"#FFFFFF",visible:!0},minorGridLineStyle:{color:"#FFFFFF",visible:!0}}}},_setModel:function(t){for(var i in t)switch(i){case"theme":this.model._themeChanged=!0;this.model.theme=t[i];this._setTheme(this._themes,this.model.theme);break;case"selectedRangeStart":this.model.selectedRangeSettings.start=this._selectedRangeStart();break;case"selectedRangeEnd":this.model.selectedRangeSettings.end=this._selectedRangeEnd();break;default:n.extend(!0,this.model,{},t[i])}this.bindTo();this.renderNavigator(this);this._bindevents()},_destroy:function(){this._unbindEvents();this.element.empty().removeClass(this.model.cssClass)},_createSvg:function(){this.svgSupport=window.SVGSVGElement?!0:!1;this.vmlRendering=!this.svgSupport;var n=jQuery.uaMatch(navigator.userAgent);this.brow=n.browser.toLowerCase();this.svgSupport?(this.renderer=new t.EjSvgRender(this.element),this.svgns=this.renderer.svgLink,this.svgDocument=this.renderer.svgObj):(this.renderer=new t.EjVmlRender(this.element),this.svgDocument=this.renderer.svgObj)},_setSVGSize:function(){var o=this.model.valueType,t,f=this.model,u=f.sizeSettings,r=parseFloat(f.labelSettings.higherLevel.style.font.size),e;o=="datetime"?(this.minHighHeight=r<20?20:r+2,r=parseFloat(this.model.labelSettings.lowerLevel.style.font.size)):this.minHighHeight=0;this.minLowHeight=r<20?20:r+2;this.scrollbarLoaded=this.model.scrollObj&&this.model.scrollObj[0].scrollbarLoaded?!0:!1;e=this.model.enableScrollbar&&this.scrollbarLoaded&&this.svgSupport?18:0;t=n(this.element).height()?n(this.element).height()>this.newHeight?n(this.element).height()-e:n(this.element).height():this.newHeight==i?o=="datetime"?120:100:this.newHeight;this.minCenterHeight=parseFloat(t)-parseFloat(this.minLowHeight)-parseFloat(this.minHighHeight);t=t<=0?120:t;this.minHeight=t;this.padding=parseFloat(f.padding);this.newWidth=u.width?u.width:n(this.element).width();this.newHeight=u.height?u.height:(n(this.element).height()&&this.newHeight==i)>this.minHeight?n(this.element).height()-e:this.minHeight;this.scrollWidth=this.newWidth-this.padding*2},_setTheme:function(i,r){var u=r.toLowerCase();u.indexOf("high")>=0&&u.indexOf("01")>=0?i[u]=n.extend(!0,{},i.highcontrast01,i[u]):u.indexOf("high")>=0&&u.indexOf("02")>=0?i[u]=n.extend(!0,{},i.highcontrast02,i[u]):u.indexOf("material")>=0?i[u]=n.extend(!0,{},i.material,i[u]):u.indexOf("office")>=0&&(i[u]=n.extend(!0,{},i.office,i[u]));this.model=t.copyObject({},this.model,i[u])},_setPositions:function(){var i=this.model.labelSettings.higherLevel,r=this.model.labelSettings.lowerLevel,n=i.position,t=r.position,u=i.visible,f=r.visible,e=i.labelPlacement,o=r.labelPlacement,s;n==="top"&&t==="bottom"?(this.centerPosition=this.minHighHeight+1,this.centerHeight=this.newHeight-this.minHighHeight-this.minLowHeight,this.bottomPosition=this.minHighHeight+this.centerHeight+1):n==="bottom"&&t==="bottom"?(this.centerPosition=0,this.centerHeight=this.newHeight-this.minHighHeight-this.minLowHeight,this.bottomPosition=this.minLowHeight+this.centerHeight+1):n==="bottom"&&t==="top"?(this.centerPosition=this.minLowHeight+1,s=0,this.centerHeight=this.newHeight-this.minHighHeight-this.minLowHeight,this.bottomPosition=this.minLowHeight+this.centerHeight+1):n==="top"&&t==="top"&&(this.centerPosition=this.minLowHeight+this.minHighHeight+1,this.centerHeight=this.newHeight-this.minHighHeight-this.minLowHeight,this.bottomPosition=this.newHeight);u===!1&&f===!1?(this.centerPosition=0,this.centerHeight=this.newHeight,this.bottomPostion=this.newHeight):u===!1?(this.centerPosition=0,this.centerHeight=this.newHeight-this.minLowHeight,this.bottomPosition=this.newHeight):f===!1&&(this.centerPosition=this.minHighHeight+1,this.centerHeight=this.newHeight-this.minHighHeight,this.bottomPosition=this.minHighHeight+this.centerHeight+1);e==="outside"&&o==="outside"?(this.sliderPosition=this.centerPosition,this.sliderHeight=this.centerHeight):e==="outside"?(this.sliderPosition=this.centerPosition,this.sliderHeight=this.newHeight):o==="outside"?(this.sliderPosition=0,this.sliderHeight=this.centerHeight+this.minHighHeight):(this.sliderPosition=0,this.sliderHeight=this.newHeight)},redraw:function(){var r=this.model.scrollRangeSettings,u=this.model.rangeSettings,i,f=r.start,e=r.end,o,s;this.loadScrollbar=!1;this.model.enableScrollbar&&(this.scrollUpdate=!0,i=this._scrollRange,this.model.valueType.toLowerCase()=="datetime"&&(f=Date.parse(r.start),e=Date.parse(r.end),o=Date.parse(u.start),s=Date.parse(u.end)),(!i||o<i.min||i.max<s||i.min!=f||i.max!=e||t.isNullOrUndefined(this.scrollsvgObj))&&(this.loadScrollbar=!0,this.scrollbarLoaded=!1,n(this.scrollsvgObj).remove()));this.renderNavigator(this)},renderNavigator:function(r){var l,k,b,u,g,nt,tt,it,pt,rt,ut,s,v,ft,et,ot,st,ht,c,ct,lt,at,y,f,e,o,p,h,w;n(this.svgDocument).empty();l=this.model.locale;this.culture=t.preferredCulture(l);this._localizedLabels=this._getLocalizedLabels();this._initializeVariables.call(this,this.model,this.model.series,this.model.seriesSettings);this._rootId=n(this.element).attr("id");this.leftPadding=parseFloat(n("#"+this._rootId).css("padding-left"));this._setSVGSize.call(this);k=this.model.valueType;this._higherTextNode=[];this._higherTotalValues=[];this._higherLineLeft=[];this._higherTextLeft=[];this._lowerTextNode=[];this._lowerTotalValues=[];this._lowerLineLeft=[];this._lowerTextLeft=[];this.labelRegions=[];var d=this.model.labelSettings,wt=d.higherLevel,bt=d.lowerLevel,kt=this.model.valueType;this.centerPosition=0;this.bottomPosition=0;this.centerHeight=0;this.sliderPosition=0;this.sliderHeight=0;this._setPositions();this.svgDocument.setAttribute("style","overflow:visible;position:relative;display:block");this.svgSupport?(this.svgDocument.setAttribute("height",this.newHeight),this.svgDocument.setAttribute("width",this.newWidth)):(n(this.svgDocument).width(this.newWidth),n(this.svgDocument).height(this.newHeight));this.model._size.width=this.newWidth;this.model._size.height=this.newHeight;b=n.extend({},t.EjSvgRender.commonChartEventArgs);b.data={model:this.model};this._trigger("loaded",b);this.model.theme!=""&&this._setTheme(this._themes,this.model.theme);u=this.model.navigatorStyleSettings;g=u.unselectedRegionColor;this.unselectgrad=this.renderer.createGradientElement("unselected",g,150,0,150,100,this.svgDocument);nt=u.background;this.naviback=this.renderer.createGradientElement("naviback",nt,150,0,150,100,this.svgDocument);tt=u.selectedRegionColor;this.selectedgrad=this.renderer.createGradientElement("selected",tt,150,0,150,100,this.svgDocument);it=u.thumbColor;this.tbClr=this.renderer.createGradientElement("thumbClr",it,150,0,150,100,this.svgDocument);var a=this.model.border,vt=a.color?a.color:this.model.enableScrollbar?"#B4B4B4":"transparent",yt={id:this._rootId+"_Layout",x:this.padding,width:this.newWidth>0?this.newWidth-this.padding*2:0,height:this.newHeight,fill:"transparent","stroke-width":a.width,stroke:vt,opacity:a.opacity};this.renderer.drawRect(yt,this.svgDocument);pt=k=="numeric"?this.newHeight-u.border.width-this.minLowHeight:this.newHeight-u.border.width-this.minLowHeight-this.minHighHeight;rt={id:this._rootId+"_Border",x:this.padding,y:this.sliderPosition,width:this.newWidth>0?this.newWidth-this.padding*2:0,height:this.sliderHeight,fill:this.naviback,"stroke-width":u.border.width,stroke:u.border.color,opacity:u.opacity,"stroke-dasharray":u.border.dashArray};this.renderer.drawRect(rt,this.svgDocument);n("#"+this._rootId+"higherLevel").length==0&&(this.higherLevel=this.renderer.createGroup({id:this._rootId+"higherLevel"}));n("#"+this._rootId+"lowerLevel").length==0&&(this.lowerLevel=this.renderer.createGroup({id:this._rootId+"lowerLevel"}));this.centerLevel=this.renderer.createGroup({id:this._rootId+"centerLelvel"});this.higherLevel.height=this.minHighHeight;this.lowerLevel.height=this.minLowHeight;this.model.tooltipSettings.visible&&this.svgSupport&&(this.leftTooltip=this.renderer.createGroup({id:this._rootId+"leftTooltip"}),this.rightTooltip=this.renderer.createGroup({id:this._rootId+"rightTooltip"}),ut={fill:this.model.tooltipSettings.backgroundColor,d:"M 0 0 L 0 21 L 64 21 L 64 6 L 70 0 Z"},this.renderer.drawPath(ut,this.leftTooltip),s=this.model.tooltipSettings.font,v={id:this._rootId+"_LeftToolText",x:5,fill:s.color,"font-size":s.size,"font-family":s.family,"font-style":s.style,"font-weight":s.weight,"text-anchor":"start",opacity:s.opacity,"dominant-baseline":"middle"},this.renderer.drawText(v,t.format(new Date(1/2e3),"MMMM, yyyy",l),this.leftTooltip),this.leftTxt=this.leftTooltip.childNodes[1],ft={fill:this.model.tooltipSettings.backgroundColor,d:"M 0 0 L 70 0 L 70 21 L 6 21 L 6 6 Z","horizontal-alignment":"stretch"},this.renderer.drawPath(ft,this.rightTooltip),v.id=this._rootId+"_RightToolText",this.renderer.drawText(v,t.format(new Date(1/2e3),"MMMM, yyyy",l),this.rightTooltip),this.rightTxt=this.rightTooltip.childNodes[1]);this.chartGView=this.renderer.createGroup({id:this._rootId+"charView"});this.renderer.append(this.chartGView,this.svgDocument);(this.model.navigatorStyleSettings.highlightSettings.enable||this.model.navigatorStyleSettings.selectionSettings.enable)&&(this.styleRect=this.renderer.createGroup({id:this._rootId+"highlightRect"}),this.renderer.append(this.styleRect,this.svgDocument));this.model.valueType=="datetime"&&this.renderer.append(this.higherLevel,this.svgDocument);this.renderer.append(this.lowerLevel,this.svgDocument);this.renderer.append(this.centerLevel,this.svgDocument);this.leftUnSelected=this.renderer.createGroup({id:"unselectleft"});this.gLeftSlider=this.renderer.createGroup({id:"leftslider"});this.gRightSlider=this.renderer.createGroup({id:"rightslider"});this.gCenterSlider=this.renderer.createGroup({id:"centerslider"});this.rightUnSelected=this.renderer.createGroup({id:"unselectright"});et={width:this.newWidth>0?this.newWidth-this.padding*2:0,height:this.sliderHeight,fill:this.unselectgrad,opacity:u.unselectedRegionOpacity,transform:"translate("+this.padding+","+this.sliderPosition+")"};this.renderer.drawRect(et,this.leftUnSelected);this.leftUnArea=this.leftUnSelected.firstChild;this.renderer.append(this.leftUnSelected,this.svgDocument);ot={width:this.newWidth>0?this.newWidth-4-this.padding*2:0,height:this.sliderHeight,fill:this.selectedgrad,opacity:u.selectedRegionOpacity,transform:"translate("+this.padding+","+this.sliderPosition+")"};this.renderer.drawRect(ot,this.gCenterSlider);this.centerSlider=this.gCenterSlider.firstChild;n(this.centerSlider).css("cursor","pointer");this.renderer.append(this.gCenterSlider,this.svgDocument);st={width:"2",height:this.sliderHeight,fill:u.thumbStroke,opacity:"1",y:this.sliderPosition,transform:"translate("+this.padding+")"};ht={id:"leftCircle"+this._id,cx:this.padding,cy:this.sliderPosition+this.sliderHeight/2,r:u.thumbRadius,"stroke-width":2,stroke:u.thumbStroke,fill:this.tbClr};this.renderer.drawRect(st,this.gLeftSlider);this.model.navigatorStyleSettings.leftThumbTemplate==null?(this.renderer.drawCircle(ht,this.gLeftSlider),this.leftCircle=this.gLeftSlider.lastChild,n(this.leftCircle).css("cursor","pointer")):(y=document.getElementById(this.model.navigatorStyleSettings.leftThumbTemplate)?!0:!1,n("#"+this._id+"thumbleft").length>0&&n("#"+this._id+"thumbleft").remove(),f=this._id,n("#"+f).css("transform")=="none"&&n("#"+f).css("transform","translate(0, 0)"),n("#"+f+"template_group_").length!=0?e=n("#"+f+"template_group_"):(e=n("<div><\/div>").attr("id",f+"template_group_"),n(e).css("position","relative")),o=n("<div><\/div>").attr("id",this._id+"thumbleft"),p=y==!0?n("#"+this.model.navigatorStyleSettings.leftThumbTemplate)[0].innerHTML:this.model.navigatorStyleSettings.leftThumbTemplate,h=n(p),n(o).css("position","absolute"),h.appendTo(n(o)),n(o).appendTo(e),this.svgSupport?n(e).appendTo("#"+f):n(e).appendTo(this.svgDocument),w=this.sliderPosition+this.sliderHeight/2-h.height()/2,n(o).css("top",w).css("cursor","pointer").css("z-index","1"));this.leftSlider=this.gLeftSlider.firstChild;n(this.leftSlider).css("cursor","w-resize");this.renderer.append(this.gLeftSlider,this.svgDocument);c=parseFloat(this.newWidth-this.padding*2);c=c<0?0:c;ct={width:c,height:this.sliderHeight,fill:this.unselectgrad,opacity:u.unselectedRegionOpacity,transform:"translate("+this.padding+","+this.sliderPosition+")"};this.renderer.drawRect(ct,this.rightUnSelected);this.rightUnArea=this.rightUnSelected.firstChild;this.renderer.append(this.rightUnSelected,this.svgDocument);lt={width:"2",height:this.sliderHeight,fill:this.model.navigatorStyleSettings.thumbStroke,opacity:"1",transform:"translate("+parseFloat(this.newWidth-4-this.padding)+", "+this.sliderPosition+" )"};at={id:"rightCircle"+this._id,cx:this.newWidth-4-this.padding,cy:this.sliderPosition+this.sliderHeight/2,r:this.model.navigatorStyleSettings.thumbRadius,"stroke-width":2,stroke:this.model.navigatorStyleSettings.thumbStroke,fill:this.tbClr};this.renderer.drawRect(lt,this.gRightSlider);this.model.navigatorStyleSettings.rightThumbTemplate==null?(this.renderer.drawCircle(at,this.gRightSlider),this.rightCircle=this.gRightSlider.lastChild,n(this.rightCircle).css("cursor","pointer")):(y=document.getElementById(this.model.navigatorStyleSettings.rightThumbTemplate)?!0:!1,n("#"+this._id+"thumbright").length>0&&n("#"+this._id+"thumbright").remove(),f=this._id,n("#"+f+"template_group_").length!=0?e=n("#"+f+"template_group_"):(e=n("<div><\/div>").attr("id",f+"template_group_"),n(e).css("position","absolute")),o=n("<div><\/div>").attr("id",this._id+"thumbright"),p=y==!0?n("#"+this.model.navigatorStyleSettings.rightThumbTemplate)[0].innerHTML:this.model.navigatorStyleSettings.rightThumbTemplate,h=n(p),n(o).css("position","absolute"),h.appendTo(n(o)),n(o).appendTo(e),this.svgSupport?n(e).appendTo("#"+f):n(e).appendTo(this.svgDocument),w=this.sliderPosition+this.sliderHeight/2-h.height()/2,n(o).css("top",w).css("cursor","pointer").css("z-index","1"));this.rightSlider=this.gRightSlider.firstChild;n(this.rightSlider).css("cursor","w-resize");this.renderer.append(this.gRightSlider,this.svgDocument);this.model.tooltipSettings.visible&&this.svgSupport&&(this.renderer.append(this.leftTooltip,this.svgDocument),this.renderer.append(this.rightTooltip,this.svgDocument));this.element.append(this.svgDocument);this.svgSupport&&(this.trueCoords=this.svgDocument.createSVGPoint(),this.grabPoint=this.svgDocument.createSVGPoint());this.backDrop=this.element;this.dragTarget=null;this.sliderHeight>0&&this._renderChart.call(this,this.model);r!=i&&r.target!=i&&(this.resize=!0);this.startDateTime!=i&&this.endDateTime!=i||this.startValue!=i&&this.endValue!=i?(this.calculateInterval.call(this,this.model.labelSettings.higherLevel,this.model.labelSettings.lowerLevel),this.setSliders()):(this.svgSupport?this.renderer._setAttr(n(this.leftSlider),{transform:"translate("+this.padding+",0)"}):this.renderer._setAttr(n(this.leftSlider),{transform:"translate("+this.padding+","+this.sliderPosition+")"}),this.leftTooltip.setAttribute("opacity",0),this.rightTooltip.setAttribute("opacity",0));this.model.tooltipSettings.visible&&this.svgSupport&&this.model.tooltipSettings.tooltipDisplayMode.toLowerCase()=="ondemand"&&(this.rightTooltip.setAttribute("opacity",0),this.leftTooltip.setAttribute("opacity",0));this._scrollChanged=this._scrollChanged?!1:this._scrollChanged;this.model.enableScrollbar?(this.loadScrollbar&&!this.scrollbarUpdate||this.model.scrollObj&&this.model.scrollObj[0].width!=this.scrollWidth?(this.model.scrollObj[0].width=this.scrollWidth,this.scrollbarContainer=new t.EjSvgScrollbarRender(this.element,this.model.scrollObj[0]),this.scrollbarContainer._initializeScrollbarVariables.call(this,this.model.scrollObj[0]),this.scrollbarContainer._renderScrollbar.call(this,this.model.scrollObj[0])):this.renderer.append(this.scrollsvgObj,this.element),this._bindevents()):(n(this.scrollsvgObj).remove(),this.scrollsvgObj=null,this.scrollZoomPos=null,this.scrollZoomFact=null)},eachInterval:"",_renderChart:function(t){var e=t.valueType.toLowerCase(),u,f=t.rangeSettings,r,o;(t.enableScrollbar||f.start!=""&&f.end!="")&&(u=e=="datetime"?{min:this.startDateTime,max:this.endDateTime}:{min:this.startValue,max:this.endValue});t.dataSource!=""&&t.series==""&&t.seriesSettings==""?n(this.chartGView).ejChart({locale:this.model.locale,border:{width:0,color:"transparent"},margin:{left:0,right:0,top:0,bottom:0},elementSpacing:0,chartArea:{background:"transparent",border:{width:0}},primaryXAxis:{axisLine:{visible:!1},font:{size:"0px"},range:u,majorTickLines:{lineWidth:0,size:0,visible:!0},majorGridLines:{visible:!1},rangePadding:this.model.rangePadding,visible:!1,isInversed:this.model.enableRTL},primaryYAxis:this.model.valueAxisSettings,series:[{dataSource:t.dataSource,xName:t.xName,yName:t.yName,type:"line",width:1.5,enableAnimation:!1},],size:{height:parseFloat(this.sliderHeight).toString(),width:parseFloat(this.newWidth-2*this.padding).toString(),padding:-10},legend:{visible:!1,itemSize:{height:0,width:0,borderColor:"transparent",borderWidth:0}}}):t.dataSource!=""&&t.series!=""&&t.series[0].dataSource==i||t.seriesSettings!=""&&t.seriesSettings.dataSource==i&&t.dataSource!=""?(t.seriesSettings.dataSource=t.dataSource,t.seriesSettings.xName=t.seriesSettings.xName?t.seriesSettings.xName:t.xName,t.seriesSettings.yName=t.seriesSettings.yName?t.seriesSettings.yName:t.yName,r=t.series,r==""&&(r=[{dataSource:t.dataSource,xName:t.seriesSettings.xName,yName:t.seriesSettings.yName,type:"line",width:1.5,enableAnimation:!1}]),n(this.chartGView).ejChart({locale:this.model.locale,border:{width:0,color:"transparent"},margin:{left:0,right:0,top:0,bottom:0},elementSpacing:0,chartArea:{background:"transparent",border:{width:0}},primaryXAxis:{axisLine:{visible:!1},font:{size:"0px"},range:u,majorTickLines:{lineWidth:0,size:0,visible:!0},majorGridLines:{visible:!1},rangePadding:this.model.rangePadding,visible:!1,isInversed:this.model.enableRTL},primaryYAxis:this.model.valueAxisSettings,series:r,commonSeriesOptions:t.seriesSettings,size:{height:parseFloat(this.sliderHeight).toString(),width:parseFloat(this.newWidth-2*this.padding).toString(),padding:-10},legend:{visible:!1,itemSize:{height:0,width:0,borderColor:"transparent",borderWidth:0}}})):(t.series!=""&&t.series[0].dataSource!=i||t.seriesSettings!=""&&t.seriesSettings.dataSource!=i)&&(t.seriesSettings==""&&(t.seriesSettings={type:"line"}),r=t.series,o=r.length,r==""&&(r=[{dataSource:t.seriesSettings.dataSource,xName:t.seriesSettings.xName,yName:t.seriesSettings.yName,type:"line",width:1.5,enableAnimation:!1}]),n(this.chartGView).ejChart({locale:this.model.locale,border:{width:0,color:"transparent"},margin:{left:0,right:0,top:0,bottom:0},elementSpacing:0,chartArea:{background:"transparent",border:{width:0}},primaryXAxis:{axisLine:{visible:!1},range:u,font:{size:"0px"},majorTickLines:{lineWidth:0,size:0,visible:!0},majorGridLines:{visible:!1},rangePadding:this.model.rangePadding,visible:!1,isInversed:this.model.enableRTL},primaryYAxis:this.model.valueAxisSettings,series:r,commonSeriesOptions:t.seriesSettings,size:{height:parseFloat(this.sliderHeight).toString(),width:parseFloat(this.newWidth-2*this.padding).toString(),padding:-10},legend:{visible:!1,itemSize:{height:0,width:0,borderColor:"transparent",borderWidth:0}}}));this.ejChart=n(this.chartGView).data("ejChart");this.renderer._setAttr(n(this.chartGView),{transform:"translate("+this.padding+","+this.sliderPosition+")"});n("#chartContainer_"+this.chartGView.id).remove()},_init:function(){var r,u,i;this._createSvg(this);r=n.extend({},t.EjSvgRender.commonChartEventArgs);r.data={model:this.model};this._trigger("load",r);u=this.model.scrollRangeSettings;this.renderNavigator(this);this.model.enableScrollbar&&(this.model.scrollObj=[],i=this.model.scrollObj,i[0]={orientation:"horizontal",index:0,width:this.scrollWidth,x:0,y:this.newHeight,isRTL:this.model.enableRTL,zoomPosition:this.scrollZoomPos,zoomFactor:this.scrollZoomFact,scrollRange:this._scrollRange,valueType:this.model.valueType,parent:this.element,enableResize:!0},i[0].valueType=="datetime"?(i[0].startDateTime=this.startDateTime,i[0].endDateTime=this.endDateTime):(i[0].startValue=this.startValue,i[0].endValue=this.endValue),this.scrollbarContainer=new t.EjSvgScrollbarRender(this.element,this.model.scrollObj[0]),this.scrollbarContainer._initializeScrollbarVariables.call(this,this.model.scrollObj[0]),this.scrollbarContainer._renderScrollbar.call(this,this.model.scrollObj[0]));this.model.enableAutoResizing||this.model.isResponsive?this.bindResizeEvents():this.removeResizeEvents();this._bindevents()},bindResizeEvents:function(){if(this.model._rangeResizeEventRegistered)return 0;(!this.model.enableScrollbar||this.model.enableScrollbar&&this.svgSupport)&&this._on(n(window),"resize",this.rangeChange);this.model._rangeResizeEventRegistered=!0},removeResizeEvents:function(){this.model._rangeResizeEventRegistered&&(this._off(n(window),"resize",this.rangeChange),this.model._rangeResizeEventRegistered=!1)},rangeChange:function(){var n,i=this;this.rangeResize=!0;clearTimeout(n);t.isNullOrUndefined(window.orientation)?this.renderNavigator():n=setTimeout(function(){i.renderNavigator()},500);this.model.enableScrollbar&&(this.model.scrollObj[0].width=this.scrollWidth,this.scrollbarContainer._renderScrollbar.call(this,this.model.scrollObj[0]))},isDevice:function(){return/mobile|android|kindle/i.test(navigator.userAgent.toLowerCase())},isWindows:function(){if(!t.getBooleanVal(n("head"),"data-ej-android")&&!t.getBooleanVal(n("head"),"data-ej-ios")&&!t.getBooleanVal(n("head"),"data-ej-ios7")&&!t.getBooleanVal(n("head"),"data-ej-flat"))return this._windows()},_windows:function(){return/trident|windows phone/i.test(navigator.userAgent.toLowerCase())||t.getBooleanVal(n("head"),"data-ej-windows",!1)===!0},_unbindEvents:function(){this._off(n(this.element),"contextmenu",this._rightClick);this._off(n(this.element),"click",this._rangeClick)},_bindevents:function(){var h=jQuery.uaMatch(navigator.userAgent),u=t.EjSvgRender.utils.browserInfo(),i=u.isMSPointerEnabled,r=u.pointerEnabled,e=i?r?"pointerdown":"MSPointerDown":"touchstart mousedown",f=i?r?"pointerup":"MSPointerUp":"touchend mouseup",o=i?r?"pointermove":"MSPointerMove":"touchmove mousemove",s=i?r?"pointerleave":"MSPointerOut":"touchleave mouseleave";this.model.browserInfo=u;this._on(this.element,o,this._drag);this._on(this.element,s,this._leave);this._on(n(document),f,this._drop);this._on(n(this.element),e,this._grab);this._on(n(this.element),"contextmenu",this._rightClick);this._on(n(this.element),"click",this._rangeClick);this._on(n("#"+this._id+"template_group_"),e,this._grab);this._on(n("#"+this._id+"template_group_"),o,this._drag);this._on(n("#"+this._id+"template_group_"),f,this._drop);this._on(n(window),f,this.mouseup);n(this.element).css("touch-action","none")},_initializeVariables:function(n,t,r){var a,v,f,u,o,s,p,h,c;this.ismouseup=!1;var e=this.model.rangeSettings,y=this.model.labelSettings.style,l=this.model.labelSettings.lowerLevel.style,w=this.model.labelSettings.higherLevel.style,b=this.model.scrollRangeSettings;if(w==""&&(this.model.labelSettings.higherLevel.style=y),l==""?this.model.labelSettings.lowerLevel.style=y:l!=i&&l.horizontalAlignment==i&&(this.model.labelSettings.lowerLevel.style.horizontalAlignment="middle"),this.model.valueType=="numeric"&&this.model.tooltipSettings.labelFormat=="MM/dd/yyyy"&&(this.model.tooltipSettings.labelFormat="d"),n.dataSource!=""&&n.xName!=""&&n.yName!="")if(f=t.length,f>0)for(u=0;u<f;u++)t[u].dataSource=n.dataSource,t[u].type=t[u].type?t[u].type:r.type?n.type:"line",t[u].xName=t[u].xName?t[u].xName:n.xName,t[u].yName=t[u].yName?t[u].yName:n.yName,this._processJsonData(t[u].dataSource,t[u]);else this._processJsonData(n.dataSource,this.model);else if(t!=""&&t!=i&&t[0].dataSource!=i&&t[0].xName!=i&&t[0].yName!=i)this._processJsonData(t[0].dataSource,t[0]);else if(r.dataSource!=i)if(f=t.length,f>0)for(u=0;u<f;u++)t[u].dataSource=r.dataSource,t[u].type=t[u].type?t[u].type:r.type?r.type:"line",t[u].xName=t[u].xName?t[u].xName:r.xName,t[u].yName=t[u].yName?t[u].yName:r.yName,this._processJsonData(t[u].dataSource,t[u]);else this._processJsonData(r.dataSource,r);else e.start!=""&&e.end!=""&&(o=a=e.start,s=v=e.end,this.model.enableScrollbar?(p=[o,s,new Date(o),new Date(s)],this.calculateZoomingRange.apply(this,p)):this.setRange.call(this,o,s,a,v));this.startDateTime!=i&&this.endDateTime!=i&&(this.model.valueType=="datetime"&&this.model.rangePadding=="round"&&(h=new Date(this.startValue),c=new Date(this.endValue),this.startDateTime=new Date(h.getFullYear(),h.getMonth(),h.getDate(),0,0,0),this.endDateTime=new Date(c.getFullYear(),c.getMonth(),c.getDate(),23,59,59)),this.startYear=this.startDateTime.getFullYear(),this.startMonth=this.startDateTime.getMonth(),this.endYear=this.endDateTime.getFullYear(),this.endMonth=this.endDateTime.getMonth())},setSliders:function(){var c=this.newWidth-this.padding*2,u=this.model.valueType,i=this.model.selectedRangeSettings.start,t=this.model.selectedRangeSettings.end,r=this.padding,e=this.startDateTime.getTime(),h=this.endDateTime.getTime(),f,o=this.startValue,s=this.endValue;this.eachInterval=u=="datetime"?c/parseFloat(h-e):c/parseFloat(s-o);f=this.eachInterval;this.model.enableScrollbar||(u=="datetime"?((i==""||new Date(i).getTime()<e)&&(i=this.startDateTime),(t==""||new Date(t).getTime()>h||new Date(t).getTime()<e)&&(t=this.endDateTime)):((i==""||i<o)&&(i=o),(t==""||t>s)&&(t=s)));this.model.enableRTL?u=="datetime"?(this.leftSliderPosition=(h-new Date(t).getTime())*f+r,this.rightSliderPosition=(h-new Date(i).getTime())*f+r,i=new Date(i),t=new Date(t)):(this.leftSliderPosition=(s-t)*f+r,this.rightSliderPosition=(s-i)*f+r):u=="datetime"?(this.leftSliderPosition=(new Date(i).getTime()-e)*f+r,this.rightSliderPosition=(new Date(t).getTime()-e)*f+r,i=new Date(i),t=new Date(t)):(this.leftSliderPosition=(i-o)*f+r,this.rightSliderPosition=(t-o)*f+r);this.setopacity=this.model.tooltipSettings.tooltipDisplayMode=="always"?1:0;this.model.enableScrollbar?this.leftSliderPosition>=r&&this.leftSliderPosition<this.newWidth-r?this.setSliderPositions(this.leftSliderPosition,null,null):this.leftSliderPosition>this.newWidth?(n(this.leftTooltip).hide(),n(this.gLeftSlider).hide()):(n(this.leftTooltip).hide(),n(this.gLeftSlider).hide(),n(this.leftUnSelected).hide()):this.setSliderPositions(this.leftSliderPosition,null,null);this.model.enableScrollbar?this.rightSliderPosition<=this.newWidth-r&&this.rightSliderPosition>r?this.setSliderPositions(null,null,this.rightSliderPosition):this.rightSliderPosition<0?(n(this.rightTooltip).hide(),n(this.gRightSlider).hide()):(n(this.rightTooltip).hide(),n(this.gRightSlider).hide(),n(this.rightUnSelected).hide()):this.setSliderPositions(null,null,this.rightSliderPosition);this.setopacity=1;this.model.enableDeferredUpdate&&(this._calculateSelectedData(),this._calculateSliderZoomFactPosition(),this.previousSelectedRangeStart=this.previousSelectedRangeStart?this.previousSelectedRangeStart:u=="datetime"?this.startDateTime.getTime():this.startValue,this.previousSelectedRangeEnd=this.previousSelectedRangeEnd?this.previousSelectedRangeEnd:u=="datetime"?this.endDateTime.getTime():this.endValue,this._scrollChanged||(u=="datetime"?this.previousSelectedRangeStart===i.getTime()&&(this.previousSelectedRangeEnd===t.getTime()||this.rangeResize)||this._trigger("rangeChanged",this.model):this.previousSelectedRangeStart===i&&(this.previousSelectedRangeEnd===t||this.rangeResize)||this._trigger("rangeChanged",this.model),this.previousSelectedRangeStart=u=="datetime"?i.getTime():i,this.previousSelectedRangeEnd=u=="datetime"?t.getTime():t),this.rangeResize=!1)},centerSliderWidth:"",zoomp:0,zoomf:1,setSliderPositions:function(r,u,f){var b=this.model.locale,v=this.model.valueType,c,ut=this.model.labelSettings.higherLevel.labelPlacement,k,d,a,rt,o,h,e,s;c=ut=="inside"?0:this.centerPosition;var y,w,it=20,p=this.model.tooltipSettings.labelFormat;if(v=="numeric"&&(p.indexOf("e")==0||p.indexOf("E")==0)&&(w=p.match(/(\d+)/g),w=w==null?6:w>it?it:w),r!=null&&r>=this.padding&&r<this.newWidth-this.padding){if(this.leftSliderPosition=r,y=this.model.enableRTL?this.newWidth-r:r,v=="datetime"?(o=y/this.eachInterval+this.startDateTime.getTime()-this.padding/this.eachInterval,o-=this.endDateTime.getTime()==o?0:this.model.enableRTL&&!this.model.allowNextValue?1:0):o=y/this.eachInterval+this.startValue-this.padding/this.eachInterval,this.model.tooltipSettings.visible&&this.svgSupport&&this.leftTxt){this.leftTooltip.setAttribute("opacity",this.setopacity);this.leftTxt.textContent=v=="datetime"?t.format(new Date(o),this.model.tooltipSettings.labelFormat,b):p.indexOf("e")==0||p.indexOf("E")==0?o.toExponential(w):t.format(o,this.model.tooltipSettings.labelFormat,b);var g=t.EjSvgRender.utils._measureText(this.leftTxt.textContent,null,this.model.tooltipSettings.font),e=g.width+15,s=g.height,nt=this.brow!="msie"?(s+6)/2:s/2+6;this.leftTxt.setAttribute("y",nt);this.leftd="M 0 0 L 0 "+(s+6)+" L "+(e-6)+" "+(s+6)+" L "+(e-6)+" 6 L "+e+" 0 Z";this.rightd="M 0 0 L "+e+" 0 L "+e+" "+(s+6)+" L 6 "+(s+6)+" L 6 6 Z";r-e>=0?(this.leftTooltip.firstChild.setAttribute("d",this.leftd),this.renderer._setAttr(n(this.leftTooltip),{transform:"translate("+(r-e)+","+c+")"}),this.leftTxt.setAttribute("x",5)):r+e<this.rightTooltip.getCTM().e?(this.renderer._setAttr(n(this.leftTooltip),{transform:"translate("+r+","+c+")"}),this.leftTooltip.firstChild.setAttribute("d",this.rightd),this.leftTxt.setAttribute("x",10)):this.rightTooltip.getCTM().f<30?(this.renderer._setAttr(n(this.leftTooltip),{transform:"translate("+r+","+(c+30)+")"}),this.leftTooltip.firstChild.setAttribute("d",this.rightd)):(this.renderer._setAttr(n(this.leftTooltip),{transform:"translate("+r+","+c+")"}),this.leftTooltip.firstChild.setAttribute("d",this.rightd));r>this.newWidth-this.rightTooltip.lastChild.getBBox().width-15&&this.rightTooltip.getCTM().f<30&&this.renderer._setAttr(n(this.leftTooltip),{transform:"translate("+(r-e)+","+(c+30)+")"});this.rightTooltip.getCTM().f>=30&&r<this.rightTooltip.getCTM().e&&this.renderer._setAttr(n(this.rightTooltip),{transform:"translate("+this.rightTooltip.getCTM().e+","+c+")"})}this.ismouseup==!1?(this.renderer._setAttr(n(this.leftUnArea),{width:r-this.padding,transform:"translate("+this.padding+","+this.sliderPosition+")"}),this.svgSupport?this.renderer._setAttr(n(this.leftSlider),{transform:"translate("+r+",0)"}):this.renderer._setAttr(n(this.leftSlider),{transform:"translate("+r+","+this.sliderPosition+")"}),this.leftCircle!=i?(this.svgSupport?this.renderer._setAttr(n(this.leftCircle),{cx:r}):this.renderer._setAttr(n(this.leftCircle),{transform:"translate("+parseFloat(r-10)+","+parseFloat(this.sliderPosition+this.sliderHeight/2)+")"}),this._leftValue={left:r-this.model.navigatorStyleSettings.thumbRadius,right:r+this.model.navigatorStyleSettings.thumbRadius}):(h=r-n("#"+this._id+"thumbleft").width()/2,n("#"+this._id+"thumbleft").css("left",h),this._leftValue={left:h,right:h+n("#"+this._id+"thumbleft").width()}),this.renderer._setAttr(n(this.centerSlider),{transform:"translate("+parseFloat(r)+","+this.sliderPosition+")"}),k=parseFloat(this.rightSlider.getBoundingClientRect().left)-parseFloat(this.leftSlider.getBoundingClientRect().left),k>0&&this.renderer._setAttr(n(this.centerSlider),{width:k})):(this.ismouseup=!1,this.renderer._setAttr(n(this.leftUnArea),{width:r-this.padding,transform:"translate("+this.padding+","+this.sliderPosition+")"}),this.svgSupport?this.renderer._setAttr(n(this.leftSlider),{transform:"translate("+r+",0)"}):this.renderer._setAttr(n(this.leftSlider),{transform:"translate("+r+","+this.sliderPosition+")"}),this.leftCircle!=i?(this.svgSupport?this.renderer._setAttr(n(this.leftCircle),{cx:r}):this.renderer._setAttr(n(this.leftCircle),{transform:"translate("+parseFloat(r-10)+","+parseFloat(this.sliderPosition+this.sliderHeight/2)+")"}),this._leftValue={left:r-this.model.navigatorStyleSettings.thumbRadius,right:r+this.model.navigatorStyleSettings.thumbRadius}):(h=r-n("#"+this._id+"thumbleft").width()/2,n("#"+this._id+"thumbleft").css("left",h),this._leftValue={left:h,right:h+n("#"+this._id+"thumbleft").width()}),this.renderer._setAttr(n(this.centerSlider),{transform:"translate("+parseFloat(r)+","+this.sliderPosition+")"}),k=parseFloat(this.rightSlider.getBoundingClientRect().left)-parseFloat(this.leftSlider.getBoundingClientRect().left),k>0&&this.renderer._setAttr(n(this.centerSlider),{width:k}));this.model.zoomPosition=(r-this.padding)/(this.newWidth-this.padding*2);this.model.zoomFactor=(this.rightSlider.getBoundingClientRect().left+parseFloat(this.renderer._getAttrVal(n(this.rightSlider),"width"))-this.leftSlider.getBoundingClientRect().left)/(this.newWidth-this.padding*2);this.model.enableRTL?this.model.selectedRangeSettings.end=v=="datetime"?new Date(o):o:this.model.selectedRangeSettings.start=v=="datetime"?new Date(o):o;n(this.leftTooltip).show();n(this.gLeftSlider).show();n(this.leftUnSelected).show()}else if(f!=null&&f>this.padding){if(f>this.newWidth-this.padding+1&&(f=this.newWidth-this.padding),this.rightSliderPosition=f,rt=new Date(this.model.rangeSettings.start),y=this.model.enableRTL?this.newWidth-f:f,o=y/this.eachInterval+this.startDateTime.getTime()-this.padding/this.eachInterval,o-=this.endDateTime.getTime()==o?0:!this.model.enableRTL&&!this.model.allowNextValue?1:0,o=v=="datetime"?new Date(o):y/this.eachInterval+this.startValue-this.padding/this.eachInterval,this.model.enableRTL?this.model.selectedRangeSettings.start=o:this.model.selectedRangeSettings.end=o,this.model.tooltipSettings.visible&&this.svgSupport&&this.rightTxt){this.rightTooltip.setAttribute("opacity",this.setopacity);this.rightTxt.textContent=v=="datetime"?t.format(new Date(o),this.model.tooltipSettings.labelFormat,b):p.indexOf("e")==0||p.indexOf("E")==0?o.toExponential(w):t.format(o,this.model.tooltipSettings.labelFormat,b);var g=t.EjSvgRender.utils._measureText(this.rightTxt.textContent,null,this.model.tooltipSettings.font),e=g.width+15,s=g.height,nt=this.brow!="msie"?(s+6)/2:s/2+6;this.rightTxt.setAttribute("y",nt);this.rightTxt.setAttribute("x",10);this.leftd="M 0 0 L 0 "+(s+6)+" L "+(e-6)+" "+(s+6)+" L "+(e-6)+" 6 L "+e+" 0 Z";this.rightd="M 0 0 L "+e+" 0 L "+e+" "+(s+6)+" L 6 "+(s+6)+" L 6 6 Z";f+e<=this.newWidth?(this.rightTooltip.firstChild.setAttribute("d",this.rightd),this.renderer._setAttr(n(this.rightTooltip),{transform:"translate("+f+","+c+")"})):f+e>=this.newWidth&&f-e>this.centerSlider.getCTM().e?(this.renderer._setAttr(n(this.rightTooltip),{transform:"translate("+(f-e)+","+c+")"}),this.rightTooltip.firstChild.setAttribute("d",this.leftd),this.rightTxt.setAttribute("x",5)):this.leftTooltip.getCTM().f<30?(this.renderer._setAttr(n(this.rightTooltip),{transform:"translate("+(f-e)+","+(c+30)+")"}),this.rightTooltip.firstChild.setAttribute("d",this.leftd)):(this.renderer._setAttr(n(this.rightTooltip),{transform:"translate("+(f-e)+","+c+")"}),this.rightTooltip.firstChild.setAttribute("d",this.leftd));f<this.leftTooltip.getCTM().e+e&&this.leftTooltip.getCTM().f<30&&this.leftSliderPosition>=this.padding&&this.renderer._setAttr(n(this.rightTooltip),{transform:"translate("+f+","+(c+30)+")"});this.leftTooltip.getCTM().f>=30&&f>this.leftTooltip.getCTM().e+e&&this.rightTooltip.getCTM().e>this.leftTooltip.getCTM().e+e&&(this.renderer._setAttr(n(this.leftTooltip),{transform:"translate("+this.leftTooltip.getCTM().e+","+c+")"}),this.rightTxt.setAttribute("x",5))}d=this.newWidth-f-this.padding-2;d=d<0?0:d;this.renderer._setAttr(n(this.rightUnArea),{width:d,transform:"translate("+parseFloat(f+2)+","+this.sliderPosition+")"});this.renderer._setAttr(n(this.rightSlider),{transform:"translate("+f+","+this.sliderPosition+")"});this.rightCircle!=i?(this.svgSupport?this.renderer._setAttr(n(this.rightCircle),{cx:f}):this.renderer._setAttr(n(this.rightCircle),{transform:"translate("+parseFloat(f-10)+","+parseFloat(this.sliderPosition+this.sliderHeight/2)+")"}),this._rightValue={left:f-this.model.navigatorStyleSettings.thumbRadius,right:f+this.model.navigatorStyleSettings.thumbRadius}):(h=f-n("#"+this._id+"thumbright").width()/2,n("#"+this._id+"thumbright").css("left",h),this._rightValue={left:h,right:h+n("#"+this._id+"thumbright").width()});this.centerSliderWidth=parseFloat(this.rightSlider.getBoundingClientRect().left)-parseFloat(this.leftSlider.getBoundingClientRect().left);this.renderer._setAttr(n(this.centerSlider),{width:Math.abs(this.centerSliderWidth)});this.model.zoomFactor=(this.rightSlider.getBoundingClientRect().left+parseFloat(this.renderer._getAttrVal(n(this.rightSlider),"width"))-this.leftSlider.getBoundingClientRect().left)/(this.newWidth-this.padding*2);n(this.rightTooltip).show();n(this.gRightSlider).show();n(this.rightUnSelected).show()}else if(u!=null){var ft=this.renderer._getAttrVal(n(this.leftSlider),"width"),tt=this.newWidth-this.padding-2,l=u+parseFloat(this.renderer._getAttrVal(n(this.centerSlider),"width"));if(l>tt?(a=u=tt-parseFloat(this.renderer._getAttrVal(n(this.centerSlider),"width")),l=tt):a=u,rt=new Date(this.model.rangeSettings.start),y=this.model.enableRTL?this.newWidth-a:a,a>=this.padding&&l<=this.newWidth-this.padding-2){if(o=y/this.eachInterval+this.startDateTime.getTime()-this.padding/this.eachInterval,v=="datetime"&&(o=new Date(o)),this.model.enableRTL?this.model.selectedRangeSettings.end=o:this.model.selectedRangeSettings.start=o,this.svgSupport?this.renderer._setAttr(n(this.leftSlider),{transform:"translate("+a+",0)"}):this.renderer._setAttr(n(this.leftSlider),{transform:"translate("+a+","+parseFloat(this.sliderPosition)+")"}),this.leftSliderPosition=a,this.leftCircle!=i?(this.svgSupport?this.renderer._setAttr(n(this.leftCircle),{cx:a}):this.renderer._setAttr(n(this.leftCircle),{transform:"translate("+parseFloat(a-10)+","+parseFloat(this.sliderPosition+this.sliderHeight/2)+")"}),this._leftValue={left:a-this.model.navigatorStyleSettings.thumbRadius,right:a+this.model.navigatorStyleSettings.thumbRadius}):(h=a-n("#"+this._id+"thumbleft").width()/2,n("#"+this._id+"thumbleft").css("left",h),this._leftValue={left:h,right:h+n("#"+this._id+"thumbleft").width()}),this.model.tooltipSettings.visible&&this.svgSupport&&this.leftTxt){this.leftTooltip.setAttribute("opacity",this.setopacity);this.leftTxt.textContent=v=="datetime"?t.format(new Date(o),this.model.tooltipSettings.labelFormat,b):p.indexOf("e")==0||p.indexOf("E")==0?o.toExponential(w):t.format(o,this.model.tooltipSettings.labelFormat,b);var e=this.leftTooltip.lastChild.getBBox().width+15,s=this.leftTooltip.lastChild.getBBox().height,nt=this.brow!="msie"?(s+6)/2:s/2+6;this.leftTxt.setAttribute("y",nt);this.rightTxt.setAttribute("y",nt);this.leftd="M 0 0 L 0 "+(s+6)+" L "+(e-6)+" "+(s+6)+" L "+(e-6)+" 6 L "+e+" 0 Z";this.rightd="M 0 0 L "+e+" 0 L "+e+" "+(s+6)+" L 6 "+(s+6)+" L 6 6 Z";a-e>=0?(this.leftTooltip.firstChild.setAttribute("d",this.leftd),this.renderer._setAttr(n(this.leftTooltip),{transform:"translate("+(a-e)+","+c+")"}),this.leftTxt.setAttribute("x",5)):(this.renderer._setAttr(n(this.leftTooltip),{transform:"translate("+a+","+c+")"}),this.leftTooltip.firstChild.setAttribute("d",this.rightd),this.leftTxt.setAttribute("x",10),this.leftSliderPosition+e>this.rightSlider.getCTM().e&&this.renderer._setAttr(n(this.leftTooltip),{transform:"translate("+a+","+(c+30)+")"}))}y=this.model.enableRTL?this.newWidth-l:l;o=y/this.eachInterval+this.startDateTime.getTime()-this.padding/this.eachInterval;o=v=="datetime"?new Date(o):y/this.eachInterval+this.startValue-this.padding/this.eachInterval;this.model.enableRTL?this.model.selectedRangeSettings.start=o:this.model.selectedRangeSettings.end=o;this.renderer._setAttr(n(this.rightSlider),{transform:"translate("+l+","+this.sliderPosition+")"});this.rightSliderPosition=l;this.rightCircle!=i?(this.svgSupport?this.renderer._setAttr(n(this.rightCircle),{cx:l}):this.renderer._setAttr(n(this.rightCircle),{transform:"translate("+parseFloat(l-10)+","+parseFloat(this.sliderPosition+this.sliderHeight/2)+")"}),this._rightValue={left:l-this.model.navigatorStyleSettings.thumbRadius,right:l+this.model.navigatorStyleSettings.thumbRadius}):(h=l-n("#"+this._id+"thumbright").width()/2,n("#"+this._id+"thumbright").css("left",h),this._rightValue={left:h,right:h+n("#"+this._id+"thumbright").width()});this.model.tooltipSettings.visible&&this.svgSupport&&(this.rightTooltip.setAttribute("opacity",this.setopacity),this.rightTxt.textContent=v=="datetime"?t.format(new Date(o),this.model.tooltipSettings.labelFormat,b):p.indexOf("e")==0||p.indexOf("E")==0?o.toExponential(w):t.format(o,this.model.tooltipSettings.labelFormat,b),e=this.rightTooltip.lastChild.getBBox().width+15,s=this.rightTooltip.lastChild.getBBox().height,this.leftd="M 0 0 L 0 "+(s+6)+" L "+(e-6)+" "+(s+6)+" L "+(e-6)+" 6 L "+e+" 0 Z",this.rightd="M 0 0 L "+e+" 0 L "+e+" "+(s+6)+" L 6 "+(s+6)+" L 6 6 Z",l+e<=this.newWidth?(this.rightTooltip.firstChild.setAttribute("d",this.rightd),this.renderer._setAttr(n(this.rightTooltip),{transform:"translate("+l+","+c+")"}),this.rightTxt.setAttribute("x",10)):(this.renderer._setAttr(n(this.rightTooltip),{transform:"translate("+(l-e)+","+c+")"}),this.rightTooltip.firstChild.setAttribute("d",this.leftd),this.rightTxt.setAttribute("x",5),this.rightTooltip.getBoundingClientRect().left<this.leftSlider.getBoundingClientRect().left&&this.renderer._setAttr(n(this.rightTooltip),{transform:"translate("+(l-e)+","+(c+30)+")"})));this.renderer._setAttr(n(this.leftUnArea),{width:a-this.padding,transform:"translate("+this.padding+","+this.sliderPosition+")"});this.renderer._setAttr(n(this.rightUnArea),{transform:"translate("+parseFloat(l+2)+","+this.sliderPosition+")",width:this.newWidth-l-this.padding-2});this.model.zoomPosition=(this.leftSlider.getBoundingClientRect().left-this.padding)/(this.newWidth-this.padding*2);this.renderer._setAttr(n(this.centerSlider),{transform:"translate("+u+","+this.sliderPosition+")"});this.model.zoomFactor=(this.rightSlider.getBoundingClientRect().left+parseFloat(this.renderer._getAttrVal(n(this.rightSlider),"width"))-this.leftSlider.getBoundingClientRect().left)/(this.newWidth-this.padding*2);n(this.leftTooltip).show();n(this.rightTooltip).show();n(this.gRightSlider).show();n(this.rightUnSelected).show();n(this.gLeftSlider).show();n(this.leftUnSelected).show()}}this._scrollChanged||this.isSelection||!this.model.enableDeferredUpdate&&this.leftSlider.getBoundingClientRect().left<this.rightSlider.getBoundingClientRect().left&&(this._calculateSelectedData(),(this.zoomp!=this.model.zoomPosition||this.zoomf!=this.model.zoomFactor)&&(this._calculateSliderZoomFactPosition(),this._trigger("rangeChanged",this.model),this.zoomp=this.model.zoomPosition,this.zoomf=this.model.zoomFactor))},_calculateSliderZoomFactPosition:function(){var f=this.model,r,e,o,n,t,u=f.selectedRangeSettings,i=f.scrollRangeSettings;f.valueType.toLowerCase()=="datetime"?(n=i.start?new Date(i.start):this.startDateTime,n=n<this.startDateTime?n:this.startDateTime,t=i.end?new Date(i.end):this.endDateTime,t=t>this.endDateTime?t:this.endDateTime,r=t-n,e=(new Date(u.start)-n)/r,o=(new Date(u.end)-new Date(u.start))/r):(n=i.start!=""?parseFloat(i.start):this.startValue,n=n<this.startValue?n:this.startValue,t=i.end!=""?parseFloat(i.end):this.endValue,t=t>this.endValue?t:this.endValue,r=t-n,e=(parseFloat(u.start)-n)/r,o=(parseFloat(u.end)-parseFloat(u.start))/r);f.zoomPosition=e;f.zoomFactor=o},_calculateSelectedData:function(){var f=[],e=0,i,o=this.model.valueType=="datetime",r=this.model.selectedRangeSettings.start,u=this.model.selectedRangeSettings.end,n,t;if(o&&(r=Date.parse(r),u=Date.parse(u)),n=this.model.dataSource!=""?this.model.dataSource:this.model.series!=""&&this.model.series[0].dataSource!=""?this.model.series[0].dataSource:null,n!=null)for(t=0;t<n.length-1;t++)i=o?Date.parse(n[t][Object.keys(n[t])[0]]):n[t][Object.keys(n[t])[0]],r<=i&&i<=u&&(f[e]=n[t],e++);this.model.selectedData=f},calculateInterval:function(n,t){if(t._intervalType=t.intervalType?t.intervalType:"auto",t.visible===!0&&this.model.valueType=="datetime"&&this.calculateVisibleLabels(t,"lower",0),n.visible===!0&&this.model.valueType=="datetime"){if(n.intervalType&&n.intervalType.toLowerCase()!="auto")n._intervalType=n.intervalType;else switch(t._intervalType){case"years":n._intervalType="years";break;case"quarters":n._intervalType="years";break;case"months":n._intervalType="quarters";break;case"weeks":n._intervalType="months";break;case"days":n._intervalType="weeks";break;case"hours":n._intervalType="days";break;case"minutes":n._intervalType="hours"}n.autoInterval==!1?this.calculateVisibleLabels(n,"higher",0):this.calculateVisibleLabels(n,"higher",0)}t.visible===!0&&this.model.valueType=="numeric"&&this.setInterval(this)},calculateVisibleLabels:function(n,t,i){var f=n._intervalType,r,u;switch(f){case"auto":r=this.calculateDateTimeNiceInterval(this,this.startDateTime,this.endDateTime);u=r.intervalType.toLowerCase();n._intervalType=u;n._interval=r.interval;this.calculateVisibleLabels(n,t,i);break;case"years":this.setYearInterval(this,t);break;case"quarters":this.setQuarterInterval(this,t,i);break;case"months":this.setMonthInterval(this,t,i);break;case"weeks":this.setWeekInterval(this,t,i);break;case"days":this.setDayInterval(this,t,i);break;case"hours":this.setHourInterval(this,t,i);break;case"minutes":this.setMinuteInterval(this,t,i)}},setInterval:function(n){var f,o,h=this.endValue-this.startValue,s=n.model.rangePadding,u=this.startValue,t=this.calculateNumericInterval(n,h),i,e,r;for(s=="additional"?(this.endValue=this.endValue+t,this.startValue=this.startValue-t,h=this.endValue-this.startValue):s=="normal"?(i=0,u<0?(u=0,i=this.startValue+this.startValue/20,e=t+i%t,.365*t>=e&&(i-=t),i%t<0&&(i=i-t-i%t)):(i=u<5/6*this.endValue?0:u-(this.endValue-u)/2,i%t>0&&(i-=i%t)),r=this.endValue+(this.endValue-u)/20,e=t-r%t,.365*t>=e&&(r+=t),r%t>0&&(r=r+t-r%t),t=this.calculateNumericInterval(n,r-i),this.startValue=i,this.endValue=r):s=="round"&&(this.startValue=Math.floor(this.startValue/t*t),this.endValue=Math.ceil(this.endValue/t*t)),f=n.startValue,o=0;f<=n.endValue;)this._insertNumericText(n,f,o),o++,f=f+t;this.insertLabels(n,"lower")},calculateDateTimeNiceInterval:function(n,t,i){var u=Math.abs((t.getTime()-i.getTime())/864e5),r=this.calculateNumericInterval(n,u/365),f,e;return r>=1?{interval:r,intervalType:"Years"}:(r=this.calculateNumericInterval(n,u/90),r>=1)?{interval:r,intervalType:"Quarters"}:(r=this.calculateNumericInterval(n,u/30),r>=1)?{interval:r,interval1:r,intervalType:"Months"}:(r=this.calculateNumericInterval(n,u/7),r>=1)?{interval:r,intervalType:"Weeks",intervalType1:"Weeks"}:(r=this.calculateNumericInterval(n,u),r>=1)?{interval:r,intervalType:"Days"}:(f=u*24,r=this.calculateNumericInterval(n,f),r>=1)?{interval:r,intervalType:"Hours"}:(e=u*1440,r=this.calculateNumericInterval(n,e),{interval:r,intervalType:"Minutes"})},calculateNumericInterval:function(n,i){for(var f,e=this.GetDesiredIntervalsCount(n.newWidth),r=i/e,s=Math.pow(10,Math.floor(t.EjSvgRender.utils._logBase(r,10))),o=[10,5,2,1],u=0;u<o.length;u++){if(f=s*o[u],e<i/f)return r;r=f}return r},GetDesiredIntervalsCount:function(n){return Math.max(n*(.8/50),1)},setHourInterval:function(t,i,r){t._higherTextNode=[];t._higherTotalValues=[];t._lowerTextNode=[];t._lowerTotalValues=[];i==="higher"?n("#higherLevel").children().empty():n("#lowerLevel").children().empty();Date.prototype.addHours=function(n){var t=new Date(this.valueOf());return t.getMilliseconds()!==0&&t.setMilliseconds(t.getMilliseconds()+(1e3-t.getMilliseconds())),t.getSeconds()!==0&&t.setSeconds(t.getSeconds()+(60-t.getSeconds())),t.getMinutes()!==0?t.setMinutes(t.getMinutes()+(60-t.getMinutes())):t.setHours(t.getHours()+n),t};var u=t.startDateTime,e=0,o=0,f=0;switch(r){case 0:while(u<=t.endDateTime)f=this._insertText(t,i,"hhtt",u,e,f,!1),o+=f,e++,u=u.addHours(1);o>=t.newWidth?t.setHourInterval(t,i,1):this.insertLabels(t,i);break;case 1:while(u<=t.endDateTime)f=this._insertText(t,i,"hht",u,e,f,!1),o+=f,e++,u=u.addHours(1);o>=t.newWidth?t.setHourInterval(t,i,2):this.insertLabels(t,i);break;case 2:while(u<=t.endDateTime)f=this._insertText(t,i,"hh",u,e,f,!1),o+=f,e++,u=u.addHours(1);o>=t.newWidth?(n(t.lowerLevel).empty(),t.model.labelSettings.lowerLevel._intervalType="days",t.setDayInterval(t,i,0)):this.insertLabels(t,i)}},setMinuteInterval:function(t,i,r){t._higherTextNode=[];t._higherTotalValues=[];t._lowerTextNode=[];t._lowerTotalValues=[];i==="higher"?n("#higherLevel").children().empty():n("#lowerLevel").children().empty();Date.prototype.addMinutes=function(n){var t=new Date(this.valueOf());return t.getMilliseconds()!==0&&t.setMilliseconds(t.getMilliseconds()+(1e3-t.getMilliseconds())),t.getSeconds()!==0?t.setSeconds(t.getSeconds()+(60-t.getSeconds())):t.setMinutes(t.getMinutes()+n),t};var u=t.startDateTime,e=0,o=0,f=0;switch(r){case 0:while(u<=t.endDateTime)f=this._insertText(t,i,"mm",u,e,f,!1),o+=f,e++,u=u.addMinutes(1);o>=t.newWidth?(n(t.lowerLevel).empty(),t.model.labelSettings.lowerLevel._intervalType="hours",t.setHourInterval(t,i,0)):this.insertLabels(t,i)}},_emptyLabelBars:function(t,i){t._higherTextNode=[];t._higherTotalValues=[];t._lowerTextNode=[];t._lowerTotalValues=[];i==="higher"?n("#higherLevel").children().empty():n("#lowerLevel").children().empty()},_insertWeekText:function(n,i,r,u,f,e){var s=n.model.labelSettings.higherLevel.style.font,h=n.model.labelSettings.lowerLevel.style.font,c,o,l;return i==="higher"?(n._higherTotalValues[f]=f==0?0:u.getTime()-n.startDateTime.getTime(),c={id:this._rootId+"_higLabelBarText_"+f,x:0,y:0,"text-anchor":"start","dominant-baseline":"middle","font-size":s.size,"font-family":s.family,"font-style":s.style,"font-weight":s.weight,fill:s.color},n.renderer.drawText(c,r,n.higherLevel),n._higherTextNode[f]=n.higherLevel.childNodes[f],o={size:n.model.labelSettings.higherLevel.style.font.size,fontStyle:n.model.labelSettings.higherLevel.style.font.fontStyle,fontFamily:n.model.labelSettings.higherLevel.style.font.fontFamily},l=this.svgSupport?t.EjSvgRender.utils._measureText(n.higherLevel.childNodes[f].textContent,null,o):t.EjSvgRender.utils._measureText(n.higherLevel.childNodes[f].innerHTML,null,o),e=Math.max(e,l.width)):(n._lowerTotalValues[f]=f==0?0:u.getTime()-n.startDateTime.getTime(),c={id:this._rootId+"_LabelBarText_"+f,x:0,y:0,"text-anchor":"start","dominant-baseline":"middle","font-size":h.size,"font-family":h.family,"font-style":h.style,"font-weight":h.weight,fill:h.color},n.renderer.drawText(c,r,n.lowerLevel),n._lowerTextNode[f]=n.lowerLevel.childNodes[f],o={size:n.model.labelSettings.lowerLevel.style.font.size,fontStyle:n.model.labelSettings.lowerLevel.style.font.fontStyle,fontFamily:n.model.labelSettings.lowerLevel.style.font.fontFamily},l=this.svgSupport?t.EjSvgRender.utils._measureText(n.lowerLevel.childNodes[f].textContent,null,o):t.EjSvgRender.utils._measureText(n.lowerLevel.childNodes[f].innerHTML,null,o),e=Math.max(e,l.width)),e},setWeekInterval:function(n,i,r){this._emptyLabelBars(n,i);Date.prototype.getWeek=function(){var n=new Date(this.getTime()),t;return n.setHours(0,0,0,0),n.setDate(n.getDate()+3-(n.getDay()+6)%7),t=new Date(n.getFullYear(),0,4),1+Math.round(((n.getTime()-t.getTime())/864e5-3+(t.getDay()+6)%7)/7)};Date.prototype.dayOfWeek=function(){return["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][this.getDay()]};Date.prototype.addDays=function(n){var t=new Date(this.valueOf());return new Date(t.getFullYear(),t.getMonth(),t.getDate()+n)};Date.prototype.subDays=function(n){var t=new Date(this.valueOf());return new Date(t.getFullYear(),t.getMonth(),t.getDate()-n)};for(var u=n.startDateTime,e=0,o=0,f=0,s=this.model.locale,h=t.datavisualization.RangeNavigator.Locale[s]?t.datavisualization.RangeNavigator.Locale[s]:t.datavisualization.RangeNavigator.Locale["en-US"];u.dayOfWeek()!="Monday";)u=u.subDays(1);switch(r){case 0:while(u<=n.endDateTime)f=this._insertWeekText(n,i,this._localizedLabels.intervals.week.longWeeks+u.getWeek()+t.format(u," MMMM, yyyy",s),u,e,f),o+=f,e++,u=u.addDays(7);o>=n.newWidth?n.setWeekInterval(n,i,1):this.insertLabels(n,i);break;case 1:while(u<=n.endDateTime)f=this._insertWeekText(n,i,this._localizedLabels.intervals.week.longWeeks+u.getWeek()+t.format(u," MMM, yy",s),u,e,f),o+=f,e++,u=u.addDays(7);o>=n.newWidth?n.setWeekInterval(n,i,2):this.insertLabels(n,i);break;case 2:while(u<=n.endDateTime)f=this._insertWeekText(n,i,this._localizedLabels.intervals.week.longWeeks+u.getWeek(),u,e,f),o+=f,e++,u=u.addDays(7);o>=n.newWidth?n.setWeekInterval(n,i,3):this.insertLabels(n,i);break;case 3:while(u<=n.endDateTime)f=this._insertWeekText(n,i,this._localizedLabels.intervals.week.shortWeeks+u.getWeek(),u,e,f),o+=f,e++,u=u.addDays(7);this.insertLabels(n,i)}},setDayInterval:function(t,i,r){t._higherTextNode=[];t._higherTotalValues=[];t._lowerTextNode=[];t._lowerTotalValues=[];i==="higher"?n("#higherLevel").children().empty():n("#lowerLevel").children().empty();Date.prototype.addDays=function(){var n=new Date(this.valueOf());return new Date(n.getFullYear(),n.getMonth(),n.getDate()+1)};var u=t.startDateTime,e=0,o=0,f=0;switch(r){case 0:while(u<=t.endDateTime)f=this._insertText(t,i,"dddd, MMMM d, yyyy",u,e,f,!1),o+=f,e++,u=u.addDays();o>=t.newWidth?t.setDayInterval(t,i,1):this.insertLabels(t,i);break;case 1:while(u<=t.endDateTime)f=this._insertText(t,i,"ddd, MMM d, yy",u,e,f,!1),o+=f,e++,u=u.addDays();o>=t.newWidth?t.setDayInterval(t,i,2):this.insertLabels(t,i);break;case 2:while(u<=t.endDateTime)f=this._insertText(t,i,"dddd, d",u,e,f,!1),o+=f,e++,u=u.addDays();o>=t.newWidth?t.setDayInterval(t,i,3):this.insertLabels(t,i);break;case 3:while(u<=t.endDateTime)f=this._insertText(t,i,"MMM, d",u,e,f,!1),o+=f,e++,u=u.addDays();o>=t.newWidth?t.setDayInterval(t,i,4):this.insertLabels(t,i);break;case 4:while(u<=t.endDateTime)f=this._insertText(t,i,"dd",u,e,f,!1),o+=f,e++,u=u.addDays();this.insertLabels(t,i)}},setMonthInterval:function(t,i,r){t._higherTextNode=[];t._higherTotalValues=[];t._lowerTextNode=[];t._lowerTotalValues=[];i==="higher"?n("#higherLevel").children().empty():n("#lowerLevel").children().empty();var u=t.startDateTime,s=5,e=0,h=new Date(t.startDateTime),o=0,f=0;switch(r){case 0:while(u<=t.endDateTime)f=this._insertText(t,i,"MMMM, yyyy",u,e,f,!1),o+=f,e++,u=u.addMonths();o+s>=t.newWidth?t.setMonthInterval(t,i,1):this.insertLabels(t,i);break;case 1:while(u<=t.endDateTime)f=this._insertText(t,i,"MMMM",u,e,f,!1),o+=f,e++,u=u.addMonths();o+s>=t.newWidth?t.setMonthInterval(t,i,2):this.insertLabels(t,i);break;case 2:while(u<=t.endDateTime)f=this._insertText(t,i,"MMM",u,e,f,!1),o+=f,e++,u=u.addMonths();o+s>=t.newWidth?t.setMonthInterval(t,i,3):this.insertLabels(t,i);break;case 3:while(u<=t.endDateTime)f=this._insertText(t,i,"MMM",u,e,f,!0),o+=f,e++,u=u.addMonths();this.insertLabels(t,i)}},_quarter:function(n,i,r,u,f,e,o){for(var c,s,h,a,v,l,y=n.startYear;y<=n.endYear;y++)if(c=u!=null?t.format(f,u,n.model.locale):"",y===n.startYear)for(s=n.startMonth;s<=11&&f<=this.endDateTime;s++)h=i=="higher"?{id:this._rootId+"_higLabelBarText_"+o,x:0,y:0,"text-anchor":"start","dominant-baseline":"middle","font-size":n.model.labelSettings.higherLevel.style.font.size,"font-family":n.model.labelSettings.higherLevel.style.font.fontFamily,"font-style":n.model.labelSettings.higherLevel.style.font.fontStyle,"font-weight":n.model.labelSettings.higherLevel.style.font.fontWeight,fill:n.model.labelSettings.higherLevel.style.font.color,opacity:n.model.labelSettings.higherLevel.style.font.opacity}:{id:this._rootId+"_LabelBarText_"+o,x:0,y:0,"text-anchor":"start","dominant-baseline":"middle","font-size":n.model.labelSettings.lowerLevel.style.font.size,"font-family":n.model.labelSettings.lowerLevel.style.font.fontFamily,"font-style":n.model.labelSettings.lowerLevel.style.font.fontStyle,"font-weight":n.model.labelSettings.lowerLevel.style.font.fontWeight,fill:n.model.labelSettings.lowerLevel.style.font.color,opacity:n.model.labelSettings.lowerLevel.style.font.opacity},s>=0&&s<=2?(i=="higher"?n.renderer.drawText(h,r+"1 "+c,n.higherLevel):n.renderer.drawText(h,r+"1 "+c,n.lowerLevel),f=new Date(f.setMonth(3)),l=2):s>=3&&s<=5?(i=="higher"?n.renderer.drawText(h,r+"2 "+c,n.higherLevel):n.renderer.drawText(h,r+"2 "+c,n.lowerLevel),f=new Date(f.setMonth(6)),l=5):s>=6&&s<=8?(i=="higher"?n.renderer.drawText(h,r+"3 "+c,n.higherLevel):n.renderer.drawText(h,r+"3 "+c,n.lowerLevel),f=new Date(f.setMonth(9)),l=8):s>=9&&s<=11&&(i=="higher"?n.renderer.drawText(h,r+"4 "+c,n.higherLevel):n.renderer.drawText(h,r+"4 "+c,n.lowerLevel),f=new Date(f.setMonth(12)),l=11),i==="lower"?(n._lowerTotalValues[o]=s===n.startMonth?n.startDateTime.getTime()-n.startDateTime.getTime():new Date(n.startYear+"/"+parseInt(s+1)+"/1").getTime()-n.startDateTime.getTime(),s=l,n._lowerTextNode[o]=n.lowerLevel.childNodes[o],a={size:n.model.labelSettings.lowerLevel.style.font.size,fontStyle:n.model.labelSettings.lowerLevel.style.font.fontStyle,fontFamily:n.model.labelSettings.lowerLevel.style.font.fontFamily},v=t.EjSvgRender.utils._measureText(n.lowerLevel.childNodes[o].textContent,null,a),e+=v.width):(n._higherTotalValues[o]=s===n.startMonth?n.startDateTime.getTime()-n.startDateTime.getTime():new Date(n.startYear+"/"+parseInt(s+1)+"/1").getTime()-n.startDateTime.getTime(),s=l,n._higherTextNode[o]=n.higherLevel.childNodes[o],a={size:n.model.labelSettings.higherLevel.style.font.size,fontStyle:n.model.labelSettings.higherLevel.style.font.fontStyle,fontFamily:n.model.labelSettings.higherLevel.style.font.fontFamily},v=t.EjSvgRender.utils._measureText(n.higherLevel.childNodes[o].textContent,null,a),e+=v.width),o++;else if(y===n.endYear)for(s=0;s<=n.endMonth;s++)h=i=="higher"?{id:this._rootId+"_higLabelBarText_"+o,x:0,y:0,"text-anchor":"start","dominant-baseline":"middle","font-size":n.model.labelSettings.higherLevel.style.font.size,"font-family":n.model.labelSettings.higherLevel.style.font.fontFamily,"font-style":n.model.labelSettings.higherLevel.style.font.fontStyle,"font-weight":n.model.labelSettings.higherLevel.style.font.fontWeight,fill:n.model.labelSettings.higherLevel.style.font.color,opacity:n.model.labelSettings.higherLevel.style.font.opacity}:{id:this._rootId+"_LabelBarText_"+o,x:0,y:0,"text-anchor":"start","dominant-baseline":"middle","font-size":n.model.labelSettings.lowerLevel.style.font.size,"font-family":n.model.labelSettings.lowerLevel.style.font.fontFamily,"font-style":n.model.labelSettings.lowerLevel.style.font.fontStyle,"font-weight":n.model.labelSettings.lowerLevel.style.font.fontWeight,fill:n.model.labelSettings.lowerLevel.style.font.color,opacity:n.model.labelSettings.lowerLevel.style.font.opacity},s>=0&&s<=2?(i=="higher"?n.renderer.drawText(h,r+"1 "+c,n.higherLevel):n.renderer.drawText(h,r+"1 "+c,n.lowerLevel),f=new Date(f.setMonth(3)),l=2):s>=3&&s<=5?(i=="higher"?n.renderer.drawText(h,r+"2 "+c,n.higherLevel):n.renderer.drawText(h,r+"2 "+c,n.lowerLevel),f=new Date(f.setMonth(6)),l=5):s>=6&&s<=8?(i=="higher"?n.renderer.drawText(h,r+"3 "+c,n.higherLevel):n.renderer.drawText(h,r+"3 "+c,n.lowerLevel),f=new Date(f.setMonth(9)),l=8):s>=9&&s<=11&&(i=="higher"?n.renderer.drawText(h,r+"4 "+c,n.higherLevel):n.renderer.drawText(h,r+"4 "+c,n.lowerLevel),f=new Date(f.setMonth(12)),l=11),i==="lower"?(n._lowerTotalValues[o]=new Date(n.endYear+"/"+parseInt(s+1)+"/1").getTime()-n.startDateTime.getTime(),s=l,n._lowerTextNode[o]=n.lowerLevel.childNodes[o],a={size:n.model.labelSettings.lowerLevel.style.font.size,fontStyle:n.model.labelSettings.lowerLevel.style.font.fontStyle,fontFamily:n.model.labelSettings.lowerLevel.style.font.fontFamily},v=t.EjSvgRender.utils._measureText(n.lowerLevel.childNodes[o].textContent,null,a),e+=v.width):(n._higherTotalValues[o]=new Date(n.endYear+"/"+parseInt(s+1)+"/1").getTime()-n.startDateTime.getTime(),s=l,n._higherTextNode[o]=n.higherLevel.childNodes[o],a={size:n.model.labelSettings.higherLevel.style.font.size,fontStyle:n.model.labelSettings.higherLevel.style.font.fontStyle,fontFamily:n.model.labelSettings.higherLevel.style.font.fontFamily},v=t.EjSvgRender.utils._measureText(n.higherLevel.childNodes[o].textContent,null,a),e+=v.width),o++;else for(s=0;s<=11;s++)h=i=="higher"?{id:this._rootId+"_higLabelBarText_"+o,x:0,y:0,"text-anchor":"start","dominant-baseline":"middle","font-size":n.model.labelSettings.higherLevel.style.font.size,"font-family":n.model.labelSettings.higherLevel.style.font.fontFamily,"font-style":n.model.labelSettings.higherLevel.style.font.fontStyle,"font-weight":n.model.labelSettings.higherLevel.style.font.fontWeight}:{id:this._rootId+"_LabelBarText_"+o,x:0,y:0,"text-anchor":"start","dominant-baseline":"middle","font-size":n.model.labelSettings.lowerLevel.style.font.size,"font-family":n.model.labelSettings.lowerLevel.style.font.fontFamily,"font-style":n.model.labelSettings.lowerLevel.style.font.fontStyle,"font-weight":n.model.labelSettings.lowerLevel.style.font.fontWeight},s>=0&&s<=2?(i=="higher"?n.renderer.drawText(h,r+"1 "+c,n.higherLevel):n.renderer.drawText(h,r+"1 "+c,n.lowerLevel),f=new Date(f.setMonth(3)),l=2):s>=3&&s<=5?(i=="higher"?n.renderer.drawText(h,r+"2 "+c,n.higherLevel):n.renderer.drawText(h,r+"2 "+c,n.lowerLevel),f=new Date(f.setMonth(6)),l=5):s>=6&&s<=8?(i=="higher"?n.renderer.drawText(h,r+"3 "+c,n.higherLevel):n.renderer.drawText(h,r+"3 "+c,n.lowerLevel),f=new Date(f.setMonth(9)),l=8):s>=9&&s<=11&&(i=="higher"?n.renderer.drawText(h,r+"4 "+c,n.higherLevel):n.renderer.drawText(h,r+"4 "+c,n.lowerLevel),f=new Date(f.setMonth(12)),l=11),i==="lower"?(n._lowerTotalValues[o]=new Date(y+"/"+parseInt(s+1)+"/1").getTime()-n.startDateTime.getTime(),s=l,n._lowerTextNode[o]=n.lowerLevel.childNodes[o],a={size:n.model.labelSettings.lowerLevel.style.font.size,fontStyle:n.model.labelSettings.lowerLevel.style.font.fontStyle,fontFamily:n.model.labelSettings.lowerLevel.style.font.fontFamily},v=t.EjSvgRender.utils._measureText(n.lowerLevel.childNodes[o].textContent,null,a),e+=v.width):(n._higherTotalValues[o]=new Date(y+"/"+parseInt(s+1)+"/1").getTime()-n.startDateTime.getTime(),s=l,n._higherTextNode[o]=n.higherLevel.childNodes[o],a={size:n.model.labelSettings.higherLevel.style.font.size,fontStyle:n.model.labelSettings.higherLevel.style.font.fontStyle,fontFamily:n.model.labelSettings.higherLevel.style.font.fontFamily},v=t.EjSvgRender.utils._measureText(n.higherLevel.childNodes[o].textContent,null,a),e+=v.width),o++;return e},setQuarterInterval:function(i,r,u){i._higherTextNode=[];i._higherTotalValues=[];i._lowerTextNode=[];i._lowerTotalValues=[];r==="higher"?n("#higherLevel").children().empty():n("#lowerLevel").children().empty();var e=new Date(i.startDateTime),o=0,f=0,s=this.model.locale,h=t.datavisualization.RangeNavigator.Locale[s]?t.datavisualization.RangeNavigator.Locale[s]:t.datavisualization.RangeNavigator.Locale["en-US"];switch(u){case 0:f=this._quarter(i,r,this._localizedLabels.intervals.quarter.longQuarters,"yyyy",e,f,o);f>=i.newWidth-this.padding?i.setQuarterInterval(i,r,1):this.insertLabels(i,r);break;case 1:f=this._quarter(i,r,this._localizedLabels.intervals.quarter.longQuarters,"yy",e,f,o);f>=i.newWidth-this.padding?i.setQuarterInterval(i,r,2):this.insertLabels(i,r);break;case 2:f=this._quarter(i,r,this._localizedLabels.intervals.quarter.shortQuarters,"yyyy",e,f,o);f>=i.newWidth-this.padding?i.setQuarterInterval(i,r,3):this.insertLabels(i,r);break;case 3:f=this._quarter(i,r,this._localizedLabels.intervals.quarter.shortQuarters,null,e,f,o);this.insertLabels(i,r)}},_insertText:function(n,i,r,u,f,e,o){var c=n.model.locale,s,h,l;return Date.prototype.addYears=function(n){var t=new Date(this.valueOf());return t.getMilliseconds()!==0||t.getSeconds()!==0||t.getMinutes()!==0||t.getHours()!==0||t.getDate()!==0||t.getMonth()!==0?t=new Date(t.getFullYear()+1,0,1):t.setYear(t.getFullYear()+n),t},Date.prototype.addMonths=function(){var n=new Date(this.valueOf());return new Date(n.getFullYear(),n.getMonth()+1,1)},i==="higher"?(n._higherTotalValues[f]=u.getTime()-n.startDateTime.getTime(),s={id:this._rootId+"_higLabelBarText_"+f,x:0,y:0,"text-anchor":"start","dominant-baseline":"middle","font-size":n.model.labelSettings.higherLevel.style.font.size,"font-family":n.model.labelSettings.higherLevel.style.font.fontFamily,"font-style":n.model.labelSettings.higherLevel.style.font.fontStyle,"font-weight":n.model.labelSettings.higherLevel.style.font.fontWeight,fill:n.model.labelSettings.higherLevel.style.font.color},o==!1?n.renderer.drawText(s,t.format(u,r,c),n.higherLevel):n.renderer.drawText(s,t.format(u,r,c).toString().substring(0,1),n.higherLevel),n._higherTextNode[f]=n.higherLevel.childNodes[f],h={size:n.model.labelSettings.higherLevel.style.font.size,fontStyle:n.model.labelSettings.higherLevel.style.font.fontStyle,fontFamily:n.model.labelSettings.higherLevel.style.font.fontFamily},l=this.svgSupport?t.EjSvgRender.utils._measureText(n.higherLevel.childNodes[f].textContent,null,h):t.EjSvgRender.utils._measureText(n.higherLevel.childNodes[f].innerHTML,null,h),e=Math.max(e,l.width)):(n._lowerTotalValues[f]=u.getTime()-n.startDateTime.getTime(),s={id:this._rootId+"_LabelBarText_"+f,x:0,y:0,"text-anchor":"start","dominant-baseline":"middle","font-size":n.model.labelSettings.lowerLevel.style.font.size,"font-family":n.model.labelSettings.lowerLevel.style.font.fontFamily,"font-style":n.model.labelSettings.lowerLevel.style.font.fontStyle,"font-weight":n.model.labelSettings.lowerLevel.style.font.fontWeight,fill:n.model.labelSettings.lowerLevel.style.font.color},o==!1?n.renderer.drawText(s,t.format(u,r,c),n.lowerLevel):n.renderer.drawText(s,t.format(u,r,c).substring(0,1),n.lowerLevel),n._lowerTextNode[f]=n.lowerLevel.childNodes[f],h={size:n.model.labelSettings.lowerLevel.style.font.size,fontStyle:n.model.labelSettings.lowerLevel.style.font.fontStyle,fontFamily:n.model.labelSettings.lowerLevel.style.font.fontFamily},l=this.svgSupport?t.EjSvgRender.utils._measureText(n.lowerLevel.childNodes[f].textContent,null,h):t.EjSvgRender.utils._measureText(n.lowerLevel.childNodes[f].innerHTML,null,h),e=Math.max(e,l.width)),e},_insertNumericText:function(n,i,r){var u,e,f,o;n._lowerTotalValues[r]=i-n.startValue;u=n.model.labelSettings.style.font;e={id:this._rootId+"_LabelBarText_"+r,x:0,y:0,"text-anchor":"start","dominant-baseline":"middle","font-size":u.size,"font-family":u.family,"font-style":u.style,"font-weight":u.weight,fill:u.color};n.renderer.drawText(e,i,n.lowerLevel);n._lowerTextNode[r]=n.lowerLevel.childNodes[r];f={size:n.model.labelSettings.lowerLevel.style.font.size,fontStyle:n.model.labelSettings.lowerLevel.style.font.fontStyle,fontFamily:n.model.labelSettings.lowerLevel.style.font.fontFamily};o=this.svgSupport?t.EjSvgRender.utils._measureText(n.lowerLevel.childNodes[r].textContent,null,f):t.EjSvgRender.utils._measureText(n.lowerLevel.childNodes[r].innerHTML,null,f)},setYearInterval:function(n,t){for(var i=n.startDateTime,r=0;i<=n.endDateTime;)this._insertText(n,t,"yyyy",i,r,0,!1),r++,i=i.addYears(1);this.insertLabels(n,t)},_applyPadding:function(){for(var n=0;n<this._higherTotalValues.length;n++)this._higherTotalValues[n]+=this.padding;for(n=0;n<this._lowerTotalValues.length;n++)this._lowerTotalValues[n]+=this.padding},isIntersect:function(n,i,r,u,f,e){var s,f,o;if(e&&r==0)s=parseFloat(n.renderer._getAttrVal(i[r],"x")),f=t.EjSvgRender.utils._measureText(i[r].innerHTML,null,u),s+f.width>n.newWidth&&(i[r].innerHTML="");else for(o=i.length-1,e?o>1:o=1;e?o>r:o<r;e?o--:o++){var c=parseFloat(n.renderer._getAttrVal(i[o],"x")),h=t.EjSvgRender.utils._measureText(i[o].innerHTML,null,u),s=parseFloat(n.renderer._getAttrVal(i[r],"x")),a=h.width==0?h.width:f,l=c+h.width;l>s&&(i[r].innerHTML="")}},insertLabels:function(r,u){var tt=jQuery.uaMatch(navigator.userAgent),ft=this.model.labelSettings.lowerLevel.gridLineStyle.color,y,d,h,et,rt,l,ut,v,k;this.lgClr=this.renderer.createGradientElement("lgColor",ft,150,0,150,100,this.svgDocument);y=this.model.valueType;d=parseFloat(this.renderer._getAttrVal(n(r.svgDocument),"width"));this.eachInterval=y=="datetime"?(d-this.padding*2)/parseFloat(this.endDateTime.getTime()-this.startDateTime.getTime()):(d-this.padding*2)/parseFloat(this.endValue-this.startValue);var p=0,o=0,s,e,c,a,w,f,it=r._higherTextNode.length,g=[],nt=[],b=0;if(u==="higher"&&r.model.labelSettings.higherLevel.visible===!0){for(r.model.labelSettings.higherLevel.position=="top"&&r.model.labelSettings.lowerLevel.position=="top"?(s={x:this.padding,y:1,width:r.newWidth>0?r.newWidth-r.padding*2:0,height:r.minHighHeight,stroke:r.model.labelSettings.higherLevel.border.color,"stroke-width":r.model.labelSettings.higherLevel.border.width,fill:"transparent"},r.renderer.drawRect(s,r.higherLevel)):r.model.labelSettings.higherLevel.position=="top"?(s={x:this.padding,y:1,width:r.newWidth-r.padding*2,height:r.minHighHeight,stroke:r.model.labelSettings.higherLevel.border.color,"stroke-width":r.model.labelSettings.higherLevel.border.width,fill:"transparent"},r.renderer.drawRect(s,r.higherLevel)):(s={x:this.padding,y:r.bottomPosition,width:r.newWidth>0?r.newWidth-r.padding*2:0,height:r.minHighHeight,stroke:r.model.labelSettings.higherLevel.border.color,"stroke-width":r.model.labelSettings.higherLevel.border.width,fill:"transparent"},r.renderer.drawRect(s,r.higherLevel)),r._higherLineLeft=[],e=r.model.enableRTL?r.newWidth-(r._higherTotalValues[r._higherTextNode.length-1]*this.eachInterval+this.padding):r._higherTotalValues[p]*this.eachInterval+this.padding,r.model.enableRTL?(f=r._higherTextNode.length-1,h=!0):(f=0,h=!1),et=0;h?f>=0:f<r._higherTextNode.length;h?f--:f++){if(r._higherLineLeft[f]=e,w={size:r.model.labelSettings.higherLevel.style.font.size,fontStyle:r.model.labelSettings.higherLevel.style.font.fontStyle,fontFamily:r.model.labelSettings.higherLevel.style.font.fontFamily},a=t.EjSvgRender.utils._measureText(r.higherLevel.childNodes[p].textContent,null,w),o=a.width,c=r._higherTotalValues[f+1]===i?h?0:r.newWidth:h?r.newWidth-(r._higherTotalValues[f+1]*this.eachInterval+this.padding):r._higherTotalValues[f+1]*this.eachInterval+this.padding,l=r.model.labelSettings.higherLevel.style.horizontalAlignment==="middle"?h?c-(c-e)/2-o/2:(c-e)/2+e-o/2:r.model.labelSettings.higherLevel.style.horizontalAlignment==="left"?h?c+10:e+10:h?e-o-2:c-o-2,this.model.enableScrollbar&&((!h&&f==0||h&&it==f)&&(this.firstLabelPosition=l+o),(!h&&f==1||h&&it-1==f)&&this.firstLabelPosition>e&&(r._higherTextNode[h?f+1:f-1].textContent="")),r.renderer._setAttr(n(r._higherTextNode[f]),{x:l}),r.renderer._setAttr(n(r._higherTextNode[f]),{fill:r.model.labelSettings.higherLevel.style.font.color}),r._higherTextLeft[f]=l,this.model.labelSettings.higherLevel.labelIntersectAction!="none"&&this.isIntersect(r,r._higherTextNode,f,w,o,h),r.model.labelSettings.higherLevel.position=="top"&&r.model.labelSettings.lowerLevel.position=="top"?(r._higherTextNode[f].setAttribute("y",r.minHighHeight-5),s={x1:e,y1:"0",x2:e,y2:r.centerPosition,stroke:r.model.labelSettings.higherLevel.gridLineStyle.color,"stroke-width":r.model.labelSettings.higherLevel.gridLineStyle.width,"stroke-dasharray":r.model.labelSettings.higherLevel.gridLineStyle.dashArray},f!=0&&r.renderer.drawLine(s,r.higherLevel)):r.model.labelSettings.higherLevel.position=="top"?(this.svgSupport?tt.browser.toLowerCase()=="msie"?r._higherTextNode[f].setAttribute("y",r.centerPosition-5):r._higherTextNode[f].setAttribute("y",r.centerPosition-9):r.renderer._setAttr(n(r._higherTextNode[f]),{y:r.centerPosition/2-5}),s={x1:e,y1:"0",x2:e,y2:r.centerPosition,stroke:r.model.labelSettings.higherLevel.gridLineStyle.color,"stroke-width":r.model.labelSettings.higherLevel.gridLineStyle.width,"stroke-dasharray":r.model.labelSettings.higherLevel.gridLineStyle.dashArray},f!=0&&r.renderer.drawLine(s,r.higherLevel)):(r._higherTextNode[f].setAttribute("y",r.newHeight-5),s={x1:e,y1:this.bottomPosition,x2:e,y2:r.newHeight,stroke:r.model.labelSettings.higherLevel.gridLineStyle.color,"stroke-width":r.model.labelSettings.higherLevel.gridLineStyle.width,"stroke-dasharray":r.model.labelSettings.higherLevel.gridLineStyle.dashArray},f!=0&&r.renderer.drawLine(s,r.higherLevel)),(l<this.padding||r.newWidth-e-this.padding<o)&&y=="datetime")if(h)l<this.padding&&e-this.padding>o?r.model.labelSettings.higherLevel.style.horizontalAlignment==="middle"?r._higherTextNode[f].setAttribute("x",this.padding+(e-this.padding)/2-o/2):r.model.labelSettings.higherLevel.style.horizontalAlignment==="left"?r._higherTextNode[f].setAttribute("x",this.padding+10):r._higherTextNode[f].setAttribute("x",e-o-2):r.newWidth-e<o&&r.newWidth-c-this.padding>o?r.model.labelSettings.higherLevel.style.horizontalAlignment==="middle"?r._higherTextNode[f].setAttribute("x",c+(r.newWidth-c-this.padding)/2-o/2):r.model.labelSettings.higherLevel.style.horizontalAlignment==="left"?r._higherTextNode[f].setAttribute("x",c+10):r._higherTextNode[f].setAttribute("x",r.newWidth-o-2-this.padding):r.higherLevel.removeChild(r._higherTextNode[f]);else{r.higherLevel.removeChild(r._higherTextNode[f]);continue}else if(l+o>r.newWidth-this.padding&&y=="datetime"){l=r.model.labelSettings.higherLevel.style.horizontalAlignment==="middle"?e+(r.newWidth-e-this.padding)/2-o/2:r.model.labelSettings.higherLevel.style.horizontalAlignment==="left"?e:r.newWidth-o*3/2;r._higherTextNode[f].setAttribute("x",l);continue}e=this.model.enableRTL?r.newWidth-(r._higherTotalValues[f-1]*this.eachInterval+this.padding):r._higherTotalValues[f+1]*this.eachInterval+this.padding;p++}for(v=0,k=r._higherLineLeft.length;v<k;v++)a=t.EjSvgRender.utils._measureText(r.higherLevel.childNodes[v].textContent,null,w),(a.width!=0||a.height!=0)&&(this.svgSupport?nt.push({size:a,x:r._higherLineLeft[v],y:r._higherTextNode[v].getAttribute("y")-a.height/2+b,lableType:"higherLevel"}):nt.push({size:a,x:r._higherLineLeft[v],y:r._higherTextNode[v].offsetTop-a.height/2+b,lableType:"higherLevel"}));this._addLabelsRegion(r,nt)}else if(r.model.labelSettings.lowerLevel.visible===!0){for(r._lowerLineLeft=[],e=r.model.enableRTL?r.newWidth-(r._lowerTotalValues[r._lowerTextNode.length-1]*this.eachInterval+this.padding):r._lowerTotalValues[p]*this.eachInterval+this.padding,rt=r.model.labelSettings.higherLevel.visible?r.bottomPosition:r.centerHeight,r.model.labelSettings.lowerLevel.position==="bottom"&&r.model.labelSettings.higherLevel.position==="top"?(s={x:this.padding,y:rt,width:this.newWidth-r.padding*2,height:r.minLowHeight-1,stroke:r.model.labelSettings.lowerLevel.border.color,"stroke-width":r.model.labelSettings.lowerLevel.border.width,fill:"transparent"},y!="numeric"&&r.renderer.drawRect(s,r.lowerLevel)):r.model.labelSettings.lowerLevel.position==="bottom"&&r.model.labelSettings.higherLevel.position==="bottom"?(s={x:this.padding,y:r.centerHeight,width:this.newWidth>0?this.newWidth-r.padding*2:0,height:r.minLowHeight-1,stroke:r.model.labelSettings.lowerLevel.border.color,"stroke-width":r.model.labelSettings.lowerLevel.border.width,fill:"transparent"},r.renderer.drawRect(s,r.lowerLevel)):r.model.labelSettings.lowerLevel.position==="top"&&r.model.labelSettings.higherLevel.position==="bottom"?(s={x:this.padding,y:0,width:this.newWidth>0?this.newWidth-r.padding*2:0,height:r.minLowHeight-1,stroke:r.model.labelSettings.lowerLevel.border.color,"stroke-width":r.model.labelSettings.lowerLevel.border.width,fill:"transparent"},r.renderer.drawRect(s,r.lowerLevel)):r.model.labelSettings.higherLevel.position==="top"&&r.model.labelSettings.lowerLevel.position==="top"&&(s={x:this.padding,y:r.minHighHeight-1,width:this.newWidth>0?this.newWidth-r.padding*2:0,height:r.minLowHeight,stroke:r.model.labelSettings.lowerLevel.border.color,"stroke-width":r.model.labelSettings.lowerLevel.border.width,fill:"transparent"},r.renderer.drawRect(s,r.lowerLevel)),r.model.enableRTL?(f=r._lowerTextNode.length-1,h=!0):(f=0,h=!1);h?f>=0:f<r._lowerTextNode.length;h?f--:f++)r._lowerLineLeft[f]=e,w={size:r.model.labelSettings.lowerLevel.style.font.size,fontStyle:r.model.labelSettings.lowerLevel.style.font.fontStyle,fontFamily:r.model.labelSettings.lowerLevel.style.font.fontFamily},a=t.EjSvgRender.utils._measureText(r.lowerLevel.childNodes[p].textContent,null,w),o=a.width,l=0,c=r._lowerTotalValues[f+1]===i?h?0:r.newWidth:h?r.newWidth-(r._lowerTotalValues[f+1]*this.eachInterval+this.padding):r._lowerTotalValues[f+1]*this.eachInterval+this.padding,l=r.model.labelSettings.lowerLevel.style.horizontalAlignment==="middle"?h?(e-c)/2+c-o/2:(c-e)/2+e-o/2:r.model.labelSettings.lowerLevel.style.horizontalAlignment==="left"?h?c+2:e+2:h?e-o-2:c-o-2,y=="numeric"&&(l=e-o/2),r.renderer._setAttr(n(r._lowerTextNode[f]),{x:l}),r.renderer._setAttr(n(r._lowerTextNode[f]),{fill:r.model.labelSettings.lowerLevel.style.font.color}),this.model.labelSettings.lowerLevel.labelIntersectAction!="none"&&this.isIntersect(r,r._lowerTextNode,f,w,o,h),r._lowerTextLeft[f]=l,r.model.labelSettings.lowerLevel.position==="bottom"&&r.model.labelSettings.higherLevel.position==="top"?(this.svgSupport?tt.browser.toLowerCase()=="msie"?r._lowerTextNode[f].setAttribute("y",r.newHeight-5):r._lowerTextNode[f].setAttribute("y",r.newHeight-9):r.renderer._setAttr(n(r._lowerTextNode[f]),{y:r.newHeight-a.height}),ut=r.model.labelSettings.higherLevel.visible?parseFloat(r.minHighHeight+r.centerHeight):r.minHighHeight,s={x1:e,y1:ut,x2:e,y2:r.newHeight,stroke:this.lgClr,"stroke-width":r.model.labelSettings.lowerLevel.gridLineStyle.width,"stroke-dasharray":r.model.labelSettings.lowerLevel.gridLineStyle.dashArray},f!=0&&y!="numeric"&&r.renderer.drawLine(s,r.lowerLevel)):r.model.labelSettings.lowerLevel.position==="bottom"&&r.model.labelSettings.higherLevel.position==="bottom"?(r._lowerTextNode[f].setAttribute("y",r.bottomPosition-5),s={x1:e,y1:r.centerHeight,x2:e,y2:r.bottomPosition,stroke:r.model.labelSettings.lowerLevel.gridLineStyle.color,"stroke-width":r.model.labelSettings.lowerLevel.gridLineStyle.width,"stroke-dasharray":r.model.labelSettings.lowerLevel.gridLineStyle.dashArray},f!=0&&r.renderer.drawLine(s,r.lowerLevel)):r.model.labelSettings.lowerLevel.position==="top"&&r.model.labelSettings.higherLevel.position==="bottom"?(r._lowerTextNode[f].setAttribute("y",r.centerPosition-5),s={x1:e,y1:0,x2:e,y2:r.centerPosition,stroke:r.model.labelSettings.lowerLevel.gridLineStyle.color,"stroke-width":r.model.labelSettings.lowerLevel.gridLineStyle.width,"stroke-dasharray":r.model.labelSettings.lowerLevel.gridLineStyle.dashArray},f!=0&&r.renderer.drawLine(s,r.lowerLevel)):r.model.labelSettings.higherLevel.position==="top"&&r.model.labelSettings.lowerLevel.position==="top"&&(r._lowerTextNode[f].setAttribute("y",r.centerPosition-5),s={x1:e,y1:r.minHighHeight,x2:e,y2:r.centerPosition,stroke:r.model.labelSettings.lowerLevel.gridLineStyle.color,"stroke-width":r.model.labelSettings.lowerLevel.gridLineStyle.width,"stroke-dasharray":r.model.labelSettings.lowerLevel.gridLineStyle.dashArray},f!=0&&r.renderer.drawLine(s,r.lowerLevel)),(l<this.padding||r.newWidth-e-this.padding<o)&&y=="datetime"?h?(l<this.padding&&e-this.padding>o?r.model.labelSettings.lowerLevel.style.horizontalAlignment==="middle"?r._lowerTextNode[f].setAttribute("x",this.padding+(e-this.padding)/2-o/2):r.model.labelSettings.lowerLevel.style.horizontalAlignment==="left"?r._lowerTextNode[f].setAttribute("x",this.padding+10):r._lowerTextNode[f].setAttribute("x",e-o-2):r.newWidth-e<o&&r.newWidth-this.padding-c>o?r.model.labelSettings.lowerLevel.style.horizontalAlignment==="middle"?r._lowerTextNode[f].setAttribute("x",c+(r.newWidth-c-this.padding)/2-o/2):r.model.labelSettings.lowerLevel.style.horizontalAlignment==="left"?r._lowerTextNode[f].setAttribute("x",c+10):r._lowerTextNode[f].setAttribute("x",r.newWidth-o-2-this.padding):r.lowerLevel.removeChild(r._lowerTextNode[f]),p--):(r.lowerLevel.removeChild(r._lowerTextNode[f]),p--):l+o>r.newWidth-this.padding&&y=="datetime"&&(l=r.model.labelSettings.lowerLevel.style.horizontalAlignment==="middle"?e+(r.newWidth-e-this.padding)/2-o/2:r.model.labelSettings.lowerLevel.style.horizontalAlignment==="left"?e:r.newWidth-o*3/2,r._lowerTextNode[f].setAttribute("x",l)),e=this.model.enableRTL?r.newWidth-(r._lowerTotalValues[f-1]*this.eachInterval+this.padding):r._lowerTotalValues[f+1]*this.eachInterval+this.padding,p++;for(v=0,k=r._lowerLineLeft.length;v<k;v++)a=t.EjSvgRender.utils._measureText(r.lowerLevel.childNodes[v].textContent,null,w),(a.width!=0||a.height!=0)&&(this.svgSupport?g.push({size:a,x:r._lowerLineLeft[v],y:r._lowerTextNode[v].getAttribute("y")-a.height/2+b,lableType:"lowerLevel"}):g.push({size:a,x:r._lowerLineLeft[v],y:r._lowerTextNode[v].offsetTop+b,lableType:"lowerLevel"}));this._addLabelsRegion(r,g)}r.model.enableRTL?r._lowerLineLeft.push(this.padding):r._lowerLineLeft.push(this.newWidth-this.padding);r._setGridlines(r);n(this.higherLevel).css("cursor","pointer");n(this.lowerLevel).css("cursor","pointer")},_addLabelsRegion:function(n,t){var r,f,u,e=t.length,i;if(e==1)n.labelRegions.push({X:t[0].x,Y:t[0].y,Height:t[0].size.height,Width:n.newWidth-t[0].x,LabelType:t[0].lableType});else for(i=0;i<e;i++)n.model.enableRTL?(r=t[i+1]?t[i].x-t[i+1].x:t[i].x-parseInt(n.model.padding),u=t[i+1]?t[i+1].x:parseInt(n.model.padding)):(r=t[i+1]?t[i+1].x-t[i].x:n.newWidth-t[i].x-parseInt(n.model.padding),u=t[i].x),f=t[i+1]?t[i].size.height:t[i-1].size.height,n.labelRegions.push({X:u,Y:t[i].y,Height:f,Width:r,LabelType:t[i].lableType})},_setGridlines:function(n){var t,i;if(n.model.navigatorStyleSettings.minorGridLineStyle.visible===!0)for(t=1;t<n._lowerTextNode.length;t++)n.model.labelSettings.higherLevel.position=="top"&&n.model.labelSettings.lowerLevel.position=="top"?(i={x1:n._lowerLineLeft[t],y1:n.centerHeight+n.minLowHeight+n.minHighHeight,x2:n._lowerLineLeft[t],y2:n.centerPosition,stroke:n.model.navigatorStyleSettings.minorGridLineStyle.color,"stroke-width":"1"},n.renderer.drawLine(i,n.centerLevel)):(i={x1:n._lowerLineLeft[t],y1:n.centerPosition,x2:n._lowerLineLeft[t],y2:n.centerPosition+n.centerHeight,stroke:n.model.navigatorStyleSettings.minorGridLineStyle.color,"stroke-width":"1"},n.renderer.drawLine(i,n.centerLevel));else for(t=0;t<n._higherTextNode.length;t++)i={x1:n._higherLineLeft[t],y1:n.centerHeight+n.minLowHeight,x2:n._higherLineLeft[t],y2:n.centerPosition,stroke:n.model.navigatorStyleSettings.majorGridLineStyle.color,"stroke-width":"1"},n.renderer.drawLine(i,n.centerLevel)},bindTo:function(){t.util.isNullOrUndefined(this.model.dataSource)||this.model.dataSource!=null&&this.model.dataSource.length>0&&this._processJsonData(this.model.dataSource,this.model)},_processJsonData:function(n,r){for(var f,a,e,o,s,v,h=[],y=[],p=0,u=0;u<n.length;u++)typeof n[u][r.xName]=="string"&&n[u][r.xName].indexOf("/Date(")!=-1&&(n[u][r.xName]=new Date(parseInt(n[u][r.xName].substr(6)))),jQuery.type(n[u][r.xName])!="string"&&jQuery.type(n[u][r.xName])!="date"?h.push(parseFloat(t.util.isNullOrUndefined(n[u][r.xName])?u:n[u][r.xName])):h.push(t.util.isNullOrUndefined(n[u][r.xName])?u:n[u][r.xName]);if(!t.util.isNullOrUndefined(r.yName))for(f=0;f<r.yName.length;f++){for(a=[],e=0;e<n.length;e++)a.push(n[e][r.yName[f]]===null||n[e][r.yName[f]]===i?n[e][r.yName[f]]:parseFloat(n[e][r.yName[f]]));y[f]=a;p=f+1}o=new Date(Math.max.apply(null,h));s=new Date(Math.min.apply(null,h));o.getTime()==s.getTime()&&(s.setHours(0),o.setHours(23));var c=s.getTime(),l=o.getTime(),w=this.model.scrollRangeSettings;this.model.enableScrollbar?(this._scrollRange||this.scrollbarUpdate)&&this.scrollbarUpdate?this._scrollRange={min:Math.min(this._scrollRange.min,c),max:Math.max(this._scrollRange.max,l),delta:l-c}:(v=[c,l,s,o],this.calculateZoomingRange.apply(this,v)):this.setRange.call(this,c,l,s,o)},calculateZoomingRange:function(n,t,i,r){return this.calculateZoomFactorPosition.call(this,n,t,i,r),this.setRange.call(this,n,t,i,r),0},setRange:function(n,t,i,r){var u=this.model.rangeSettings;return u.start!=""&&u.end!=""?(this.startDateTime=new Date(u.start),this.endDateTime=new Date(u.end),this.startValue=u.start,this.endValue=u.end):(this.startDateTime=new Date(i),this.startValue=n,this.endDateTime=new Date(r),this.endValue=t),0},calculateZoomFactorPosition:function(n,t,i,r){var l=this.model,h=l.rangeSettings,u,a,v,f=i,e=r,o=n,s=t,c=l.scrollRangeSettings;return l.valueType.toLowerCase()=="datetime"?(f=Date.parse(c.start?new Date(c.start):i),f=Math.min(f,Date.parse(i)),f=h.start!=""?Math.min(f,Date.parse(new Date(h.start))):f,e=Date.parse(c.end?new Date(c.end):r),e=Math.max(e,Date.parse(r)),e=h.end!=""?Math.max(e,Date.parse(new Date(h.end))):e,u=e-f,l.enableRTL?(a=(e-r)/u,v=(r-i)/u):(a=(i-f)/u,v=(r-i)/u),this._scrollRange={min:f,max:e,delta:e-f}):(o=c.start!=""?parseFloat(c.start):n,o=Math.min(o,n),o=h.start!=""?Math.min(o,parseFloat(h.start)):o,s=c.end!=""?parseFloat(c.end):t,s=Math.max(s,t),s=h.end!=""?Math.max(s,parseFloat(h.end)):s,u=s-o,l.enableRTL?(a=(s-t)/u,v=(t-n)/u):(a=(n-o)/u,v=(t-n)/u),this._scrollRange={min:o,max:s,delta:s-o}),this.scrollZoomPos=a,this.scrollZoomFact=v,0},mousePosition:function(n){if(!t.util.isNullOrUndefined(n.pageX)&&n.pageX>0)return{x:n.pageX,y:n.pageY};if(n.originalEvent&&!t.util.isNullOrUndefined(n.originalEvent.pageX)&&n.originalEvent.pageX>0)return{x:n.originalEvent.pageX,y:n.originalEvent.pageY};if(n.originalEvent&&n.originalEvent.changedTouches!=i){if(!t.util.isNullOrUndefined(n.originalEvent.changedTouches[0].pageX)&&n.originalEvent.changedTouches[0].pageX>0)return{x:n.originalEvent.changedTouches[0].pageX,y:n.originalEvent.changedTouches[0].pageY}}else return{x:0,y:0}},calTouchPosition:function(t){var f=jQuery.uaMatch(navigator.userAgent),i=this.mousePosition(t),r,u,e;return t.pageX=i.x,t.pageY=i.y,e=f.browser.toLowerCase(),r=t.pageX-n(this.svgDocument).offset().left,u=t.pageY-n(this.svgDocument).offset().top,this.leftPadding=n(this.svgDocument).offset().left,{X:r,Y:u}},calMousePosition:function(t){var u=jQuery.uaMatch(navigator.userAgent),i,r;return u.browser.toLowerCase()=="mozilla"||u.browser.toLowerCase()=="webkit"?(i=t.pageX-n(this.svgDocument).parent().offset().left,r=t.pageY-n(this.svgDocument).parent().offset().top,this.leftPadding=0,this.grabPadding=0):(i=(t.originalEvent?t.originalEvent.pageX:t.pageX)-n(this.svgDocument).offset().left,r=(t.originalEvent?t.originalEvent.pageY:t.pageY)-n(this.svgDocument).offset().top,this.leftPadding=n(this.svgDocument).offset().left),{X:i,Y:r}},_performSelection:function(t){var i=this,s=i.model,r=s.navigatorStyleSettings.selectionSettings,h=r.color,u=t.target.parentNode.id,f=u=="leftslider"||u=="rightslider"||u==i._id+"thumbleft"||u==i._id+"thumbright",e=i.model.valueType,o;i._sliderHover=f||i.switched;i.selectionGrad=i.renderer.createGradientElement("selectionGrad",h,150,0,150,100,i.svgDocument);i.isNumericType=u!=i._id;(!i.isNumericType||e!="numeric")&&e=="numeric"||f||i.switched||(n("#highlightRect"+i._id).hide(),n("#selectionRect"+i._id).length==0&&(i.gselection=i.renderer.createGroup({id:"selectionRect"+i._id}),o={height:i.sliderHeight,opacity:r.opacity,stroke:r.border.color,"stroke-width":r.border.width},i.renderer.drawRect(o,i.gselection),i.selectionRect=i.gselection.firstChild,i.renderer.append(i.gselection,i.styleRect)),i.rect=i.selectionRect,i.startX=i.mouseDownX,i.renderer._setAttr(n(i.selectionRect),{fill:i.selectionGrad}),i.renderer._setAttr(n(i.selectionRect),{stroke:r.border.color}),i._highlight.call(i,i,t))},_performHighlight:function(t){var i=this,e=i.model,r=t.target.parentNode.id,u=e.navigatorStyleSettings.highlightSettings,o=u.color,s=r=="leftslider"||r=="rightslider"||r==this._id+"thumbleft"||r==this._id+"thumbright",f;this.highlightGrad=this.renderer.createGradientElement("highlightGrad",o,150,0,150,100,this.svgDocument);n("#highlightRect"+this._id).hide();s||this.isSelection||this._sliderHover||i.switched||(n("#highlightRect"+this._id).length==0&&(this.gHighlight=this.renderer.createGroup({id:"highlightRect"+this._id}),f={height:this.sliderHeight,fill:this.highlightGrad,opacity:u.opacity,stroke:u.border.color,"stroke-width":u.border.width},this.renderer.drawRect(f,this.gHighlight),this.highlightRect=this.gHighlight.firstChild,this.renderer.append(this.gHighlight,this.styleRect)),this.rect=this.highlightRect,i.startX=i.mouseDownX,this._highlight.call(this,i,t))},isTouch:function(n){var t=n.originalEvent?n.originalEvent:n;return t.pointerType=="touch"||t.pointerType==2||t.type.indexOf("touch")>-1?!0:!1},_rangeClick:function(n){var t=new Date;this.model.click!=""&&this._trigger("click",{data:{event:n}});this._doubleTapTimer!=null&&t-this._doubleTapTimer<300&&this._doubleClick(n);this._doubleTapTimer=t},_doubleClick:function(n){this.model.doubleClick!=""&&this._trigger("doubleClick",{data:{event:n}})},_rightClick:function(n){var t=this.model.browserInfo;return this.model.rightClick!=""&&this._trigger("rightClick",{data:{event:n}}),this.isTouch(n)&&this.model.navigatorStyleSettings.selectionSettings.enable?(event.preventDefault(),event.stopPropagation(),!1):void 0},_grab:function(i){var f,h,e,v,nt,tt,it,a,rt,s,u,o,p;this.isTouch(i)||i.preventDefault();var r=this,ut=r.model,yt=ut.browserInfo,ft=this.isTouch(i),et=ut.navigatorStyleSettings.selectionSettings,ot=ft?r.calTouchPosition(i):r.calMousePosition(i);for(r.mouseDownX=ot.X,r.mouseDownY=ot.Y,et.enable&&this._performSelection(i),f=r.edge==!0?r.target:i.target,h=f;h.parentNode;)h=h.parentNode,h.id==this._id+"thumbleft"?e=this._id+"thumbleft":h.id==this._id+"thumbright"&&(e=this._id+"thumbright");var w,b,c,k,d,g,lt=r.labelRegions.length,l=r.mouseDownX,st=r.mouseDownY;for(v=0;v<lt;v++)if(c=r.labelRegions[v],k=c.X,d=c.Y,g=c.LabelType,k<=l&&k+c.Width>=l&&d<=st&&d+c.Height>=st){w=g=="lowerLevel"?!0:!1;b=g=="higherLevel"?!0:!1;break}if(nt=!1,r.isSelection=!1,r.highlightRect&&(tt=r.highlightRect.getBoundingClientRect(),it=r.centerSlider.getBoundingClientRect(),tt.left>=it.left&&tt.right<=it.right&&(nt=!0)),!et.enable||!r.selectionRect||f.parentNode.id==="leftslider"||f.parentNode.id==="rightslider"||e==this._id+"thumbleft"||e==this._id+"thumbright"||(r.isSelection=!0,r.startX=r.mouseDownX),ft&&!r.switched){var ht=this._leftValue,ct=this._rightValue,y=10,at=n("#"+this._id+"thumbleft"),vt=n("#"+this._id+"thumbright");l>=ht.left-y&&l<=ht.right+y&&(f=this.leftCircle?this.leftCircle:at[0].firstChild,e=this.leftCircle?e:this._id+"thumbleft",this._sliderMove=!0);l>=ct.left-y&&l<=ct.right+y&&(f=this.rightCircle?this.rightCircle:vt[0].firstChild,e=this.rightCircle?e:this._id+"thumbright",this._sliderMove=!0)}if(f.parentNode.id==="leftslider"||f.parentNode.id==="rightslider"||e==this._id+"thumbleft"||e==this._id+"thumbright"||f.parentNode.id==="highlightRect"+this._id&&nt||f.parentNode.id==="centerslider"||w||b||r.isSelection)if(r.dragTarget=f.parentNode.id==="highlightRect"+this._id?n("#centerslider").children()[0]:f,r.grabbed=!0,r.edge=!1,a=r.dragTarget.parentNode.id,this.svgSupport&&(rt=e==this._id+"thumbleft"||e==this._id+"thumbright"?{e:i.clientX,f:i.clientY}:r.dragTarget.getCTM(),r.grabPoint.x=r.mouseDownX+this.leftPadding-Number(rt.e),r.grabPoint.y=r.trueCoords.y-Number(rt.f)),o=!1,p=r.model.enableRTL,p&&(r._higherLineLeft.sort(function(n,t){return n-t}),r._lowerLineLeft.sort(function(n,t){return n-t})),a!="leftslider"&&a!="rightslider"&&r.model.valueType=="datetime"&&b){for(r.grabbed=r.dragTarget.parentNode.id=="centerslider"?!0:!1,s=r.mouseDownX,u=0;u<r._higherLineLeft.length;u++)u+1<r._higherLineLeft.length&&s>r._higherLineLeft[u]&&s<r._higherLineLeft[u+1]?(r.setSliderPositions(r._higherLineLeft[u],null,null),r.setSliderPositions(null,null,r._higherLineLeft[u+1]),o=!0):u+1===r._higherLineLeft.length&&s>r._higherLineLeft[u]&&(r.setSliderPositions(r._higherLineLeft[u],null,null),r.setSliderPositions(null,null,r.newWidth-r.padding),o=!0);p&&!o&&(r.setSliderPositions(0+r.padding,null,null),r.setSliderPositions(null,null,r._higherLineLeft[0]),o=!1)}else if(a!="leftslider"&&a!="rightslider"&&r.model.valueType=="datetime"&&w){for(r.center=r.mouseDownX,r.grabbed=r.dragTarget.parentNode.id=="centerslider"?!0:!1,s=r.mouseDownX,u=0;u<r._lowerLineLeft.length;u++)u+1<r._lowerLineLeft.length&&s>r._lowerLineLeft[u]&&s<r._lowerLineLeft[u+1]?(r.setSliderPositions(r._lowerLineLeft[u],null,null),r.setSliderPositions(null,null,r._lowerLineLeft[u+1]),o=!0):u+1===r._lowerLineLeft.length&&s>r._lowerLineLeft[u]&&(r.setSliderPositions(r._lowerLineLeft[u],null,null),r.setSliderPositions(null,null,r.newWidth-r.padding),o=!0);p&&!o&&(r.setSliderPositions(0+r.padding,null,null),r.setSliderPositions(null,null,r._lowerLineLeft[0]),o=!1)}else r.isSelection&&(r.grabbed=!0);t.isTouchDevice()&&this.model.rightClick!=""&&(this._longPressTimer=new Date)},_leave:function(){n("#highlightRect"+this._id).hide()},_drag:function(t){var e,c,l,i,a,u,v,w,y,p,o,r,s,f,h;if(t.preventDefault(),e=jQuery.uaMatch(navigator.userAgent),i=this,a=this.svgSupport&&t.originalEvent.toString()!=="[object TouchEvent]"&&e.browser.toLowerCase()!="msie"?i.calMousePosition(t):this.svgSupport?i.calTouchPosition(t):i.calMousePosition(t),c=i.mouseDownX=a.X,l=i.mouseDownY=a.Y,y=i.model.navigatorStyleSettings.highlightSettings,!y.enable||this._sliderMove||this.isTouch(t)||this._performHighlight(t),this.svgSupport&&i.GetTrueCoords(t),i.model.labelSettings.higherLevel.visible||i.model.labelSettings.lowerLevel.visible)for(p=i.labelRegions.length,o=0;o<p;o++)if(u=i.labelRegions[o],v=u.X,w=u.Y,v<=c&&v+u.Width>=c&&u.Y<=l&&u.Y+u.Height>=l){n(i.rightUnSelected).css("cursor","pointer");n(i.leftUnSelected).css("cursor","pointer");break}else n(i.rightUnSelected).css("cursor","default"),n(i.leftUnSelected).css("cursor","default");if(i.dragTarget){for((i.edge||i.dragTarget!==i.leftSlider)&&i.dragTarget!==i.rightSlider&&i.dragTarget!==i.highlightRect&&i.dragTarget!==i.centerSlider?(r=i.mouseDownX,s=i.mouseDownY):this.svgSupport?e.browser.toLowerCase()=="webkit"||e.browser.toLowerCase()=="mozilla"?(r=i.mouseDownX-i.grabPoint.x,r<this.padding&&(r=this.padding),s=i.mouseDownY-i.grabPoint.y):(r=i.trueCoords.x-i.grabPoint.x,r<this.padding&&(r=this.padding),s=i.trueCoords.y-i.grabPoint.y):(r=i.mouseDownX,s=i.mouseDownY),f=i.dragTarget;f.parentNode;)f=f.parentNode,f.id==this._id+"thumbleft"?h=this._id+"thumbleft":f.id==this._id+"thumbright"&&(h=this._id+"thumbright");i.diff=i.leftSlider.getBoundingClientRect().left-i.rightSlider.getBoundingClientRect().left;i.leftdiff=i.rightSlider.getBoundingClientRect().left-this.padding-n(this.svgDocument).offset().left;i.rightdiff=i.leftSlider.getBoundingClientRect().left-this.padding-n(this.svgDocument).offset().left;i.dragTarget.parentNode.id==="leftslider"||h==this._id+"thumbleft"?(i.grabbed=!1,i.diff>=-1&&i.diff<=1&&i.switched==!1&&i.rightdiff<i.newWidth-2*this.padding?(i.target=i.rightSlider,i.switched=!0,i.edge=!0,i._grab(t),i._drag(t)):(r<=this.rightSliderPosition?(i.setSliderPositions(r,null,null),this.leftSliderPosition=r,this.leftset=!1):(i.setSliderPositions(this.rightSliderPosition,null,null),this.leftset=!0,this.leftSliderPosition=this.rightSliderPosition),(i.diff>=1||i.diff<=-1)&&(i.switched=!1)),i.left=r,i.isSelection&&(i.isSelection=!1,this.renderer._setAttr(n(this.selectionRect),{fill:"transparent"}),this.renderer._setAttr(n(this.selectionRect),{stroke:"transparent"}))):i.dragTarget.parentNode.id==="rightslider"||h==this._id+"thumbright"?(i.grabbed=!1,i.diff>=0&&i.diff<=1&&i.switched==!1&&i.leftdiff>0?(i.target=i.leftSlider,i.switched=!0,i.edge=!0,i._grab(t),i._drag(t)):(r>=this.leftSliderPosition?(i.setSliderPositions(null,null,r),this.rightSliderPosition=r):(i.setSliderPositions(null,null,this.leftSliderPosition),this.rightSliderPosition=this.leftSliderPosition),i.switched=!1),i.right=r,i.isSelection&&(i.isSelection=!1,this.renderer._setAttr(n(this.selectionRect),{fill:"transparent"}),this.renderer._setAttr(n(this.selectionRect),{stroke:"transparent"}))):i.dragTarget.parentNode.id!=="centerslider"&&i.dragTarget.parentNode.id!=="highlightRect"+this._id||i.isSelection?i.isSelection&&(this.rect=this.selectionRect,this._highlight.call(this,i,t),i.grabbed=!1):(n("#highlightRect"+this._id).hide(),r<this.padding&&(r=parseFloat(this.padding)),i.grabbed?i.grabbed=!1:(i.setSliderPositions(null,r,null),i.center=r),r>r+this.renderer._getAttrVal(n(i.dragTarget),"width")&&i.setSliderPositions(null,r,null))}},_highlight:function(i,r){var o=[],f=[],b=this.sliderHeight,y=i.startX>i.mouseDownX?i.mouseDownX:i.startX,k=i.startX==y?i.mouseDownX:i.startX,d=i.mouseDownY,s,p,w,h,c,tt=this.model.labelSettings.higherLevel.labelPlacement,g=this.model.labelSettings.lowerLevel.visible,nt=r.target.parentNode.id,u,e,l,a,v;n.each(i.labelRegions,function(n,t){s=t.X;p=t.Y;w=t.Width;h=t.LabelType=="higherLevel";c=h?nt.indexOf("higherLevel")<0&&g?t.Height:b:t.Height;y<=s+w&&s<=k&&p+c>=d&&(h?f.indexOf(t)>-1||f.push(t):o.indexOf(t)>-1||o.push(t))});v=function(n){if(n.length>0){l=n[0].X;a=n[0].X;u=n[0];e=n[0];for(var t=0;t<n.length;t++)n[t].X<l?(l=n[t].X,u=n[t]):n[t].X>a&&(a=n[t].X,e=n[t])}};f.length>0?v(f):v(o);t.isNullOrUndefined(u)||(n("#"+this.rect.parentNode.id).show(),this.renderer._setAttr(n(this.rect),{transform:"translate("+u.X+","+this.sliderPosition+")",width:e.Width+e.X-u.X}))},_drop:function(r){var w,u,a,h,e,s,f,o,p;if(n("#highlightRect"+this._id).hide(),this._sliderMove=!1,this.dragTarget){if(w=r.target,this._sliderHover=!1,this.grabbed=!1,u=this,u.model.allowSnapping&&parseInt(u.leftSlider.getBoundingClientRect().left+2)<parseInt(u.rightSlider.getBoundingClientRect().left)||u.isSelection){a=parseInt(u.model.padding);h=u.isSelection?u.rect.getBoundingClientRect():0;u.ismouseup=!0;u.dragTarget.parentNode.id==="leftslider"?e=u.left:u.dragTarget.parentNode.id==="rightslider"?e=u.right:u.isSelection?e=Math.abs(parseFloat(h.left-n("#"+this._id)[0].getBoundingClientRect().left+a)):u.dragTarget.parentNode.id==="centerslider"&&(e=u.leftSliderPosition,s=u.rightSliderPosition);var v=0,y=0,b=u._lowerLineLeft.length,c,l;for(f=0;f<u._lowerLineLeft.length;f++)if(f+1<u._lowerLineLeft.length&&e>=u._lowerLineLeft[f]&&e<u._lowerLineLeft[f+1]){if(u.snapValue=u._lowerLineLeft[f],v=u._lowerLineLeft[f+1]-u.snapValue,u.dragTarget.parentNode.id==="centerslider"){for(o=f+1;o<u._lowerLineLeft.length;o++)o+1<u._lowerLineLeft.length&&s>=u._lowerLineLeft[o]&&s<u._lowerLineLeft[o+1]&&(c=u._lowerLineLeft[o],l=u._lowerLineLeft[o+1],y=l-c);s=s>=c+y/2?l:c}e>=u.snapValue+v/2?(u.snapValue=u._lowerLineLeft[f+1],u.dragTarget.parentNode.id==="leftslider"?u.setSliderPositions(u._lowerLineLeft[f+1],null,null):u.dragTarget.parentNode.id==="rightslider"?u.setSliderPositions(null,null,u._lowerLineLeft[f+1]):u.isSelection?(u.setSliderPositions(null,null,u._lowerLineLeft[f+1]+parseFloat(h.right-h.left)),u.setSliderPositions(u._lowerLineLeft[f+1],null,null)):u.dragTarget.parentNode.id!=="centerslider"||u.grabbed||(u.setSliderPositions(null,null,s),u.setSliderPositions(u._lowerLineLeft[f+1],null,null))):(u.snapValue=u._lowerLineLeft[f],u.dragTarget.parentNode.id==="leftslider"?u.setSliderPositions(u._lowerLineLeft[f],null,null):u.dragTarget.parentNode.id==="rightslider"?u.setSliderPositions(null,null,u._lowerLineLeft[f]):u.isSelection?(u.setSliderPositions(null,null,u._lowerLineLeft[f]+parseFloat(h.right-h.left)),u.setSliderPositions(u._lowerLineLeft[f],null,null)):u.dragTarget.parentNode.id!=="centerslider"||u.grabbed||(u.setSliderPositions(null,null,s),u.setSliderPositions(u._lowerLineLeft[f],null,null)))}else f+1===u._lowerLineLeft.length&&e>=u._lowerLineLeft[f]&&(u.isSelection?(u.setSliderPositions(null,null,u._lowerLineLeft[f]+parseFloat(u.rect.getBoundingClientRect().width)),u.setSliderPositions(u._lowerLineLeft[f],null,null)):u.setSliderPositions(null,null,u.newWidth-u.padding))}this.dragTarget=null;this.leftTooltip!=i&&this.model.tooltipSettings.tooltipDisplayMode=="always"?(this.leftTooltip.setAttribute("opacity",1),this.rightTooltip.setAttribute("opacity",1)):this.leftTooltip!=i&&this.model.tooltipSettings.tooltipDisplayMode.toLowerCase()=="ondemand"&&(this.isTouch(r)?(p=[this.leftTooltip,this.rightTooltip],window.clearTimeout(this.model.timer),this.model.timer=setTimeout(function(){n(p).fadeOut(500)},1200)):(this.leftTooltip.setAttribute("opacity",0),this.rightTooltip.setAttribute("opacity",0)));this.model.enableDeferredUpdate&&(this._calculateSelectedData(),(!u.grabbed||u.isSelection)&&(this._calculateSliderZoomFactPosition(),this.model.valueType=="datetime"?(this.previousSelectedRangeStart!==new Date(this.model.selectedRangeSettings.start).getTime()||this.previousSelectedRangeEnd!==new Date(this.model.selectedRangeSettings.end).getTime())&&this._trigger("rangeChanged",this.model):(this.previousSelectedRangeStart!==this.model.selectedRangeSettings.start||this.previousSelectedRangeEnd!==this.model.selectedRangeSettings.end)&&this._trigger("rangeChanged",this.model)));u.isSelection&&(u.isSelection=!1,this.renderer._setAttr(n(this.selectionRect),{fill:"transparent"}),this.renderer._setAttr(n(this.selectionRect),{stroke:"transparent"}))}this._setSelectedRange();t.isTouchDevice()&&this.model.rightClick!=""&&new Date-this._longPressTimer>1500&&this._trigger("rightClcik",{data:{event:r}})},mouseup:function(n){this.dragTarget!=null&&(this._drop(n),this.dragTarget!=null&&(this.dragTarget.setAttributeNS(null,"pointer-events","all"),this.dragTarget=null),this.model.enableDeferredUpdate&&(this._calculateSelectedData(),this._calculateSliderZoomFactPosition(),this._trigger("rangeChanged",this.model)))},_setSelectedRange:function(){this._selectedRangeStart(this.model.selectedRangeSettings.start);this._selectedRangeEnd(this.model.selectedRangeSettings.end)},GetTrueCoords:function(n){var t=this.svgDocument.currentScale,i=this.svgDocument.currentTranslate,r=jQuery.uaMatch(navigator.userAgent);n.originalEvent.toString()!=="[object TouchEvent]"&&r.browser.toLowerCase()!="msie"?(this.trueCoords.x=(n.clientX-i.x)/t,this.trueCoords.y=(n.clientY-i.y)/t):r.browser.toLowerCase()=="msie"?(this.trueCoords.x=(n.originalEvent.clientX-i.x)/t,this.trueCoords.y=(n.originalEvent.clientY-i.y)/t):(this.trueCoords.x=(n.originalEvent.touches[0].clientX-i.x)/t,this.trueCoords.y=(n.originalEvent.touches[0].clientY-i.y)/t)},_getLocalizedLabels:function(){return t.getLocalizedConstants(this.sfType,this.model.locale)}});t.datavisualization.RangeNavigator.Locale=t.datavisualization.RangeNavigator.Locale||{};t.datavisualization.RangeNavigator.Locale["default"]=t.datavisualization.RangeNavigator.Locale["en-US"]={intervals:{quarter:{longQuarters:"Quarter",shortQuarters:"Q"},week:{longWeeks:"Week",shortWeeks:"W"}}};t.datavisualization.RangeNavigator.Locale["fr-FR"]={intervals:{quarter:{longQuarters:"Trimestre",shortQuarters:"T"},week:{longWeeks:"Semaine",shortWeeks:"S"}}};t.datavisualization.RangeNavigator.IntervalType={Auto:"auto",Years:"years",Quarters:"quarters",Months:"months",Weeks:"weeks",Days:"days",Hours:"hours",Minutes:"minutes"};t.datavisualization.RangeNavigator.Position={Top:"top",Bottom:"bottom"};t.datavisualization.RangeNavigator.FontStyle={Normal:"normal",Italic:"italic",Bold:"bold"};t.datavisualization.RangeNavigator.FontWeight={Regular:"regular",Bold:"bold",Lighter:"lighter"};t.datavisualization.RangeNavigator.HorizontalAlignment={Left:"left",Right:"right",Middle:"middle"};t.datavisualization.RangeNavigator.LabelPlacement={Inside:"inside",Outside:"outside"};t.datavisualization.RangeNavigator.LabelIntersectAction={None:"none",Hide:"hide"};t.datavisualization.RangeNavigator.ValueType={Numeric:"numeric",Datetime:"datetime"};t.datavisualization.RangeNavigator.RangePadding={Additional:"additional",Normal:"normal",None:"none",Round:"round"};t.datavisualization.RangeNavigator.Theme={Azure:"azure",FlatLight:"flatlight",Azuredark:"azuredark",Lime:"lime",LimeDark:"limedark",Saffron:"saffron",SaffronDark:"saffrondark",GradientLight:"gradientlight",GradientDark:"gradientdark"}})(jQuery,Syncfusion);jQuery.uaMatch=function(n){n=n.toLowerCase();var t=/(chrome)[ \/]([\w.]+)/.exec(n)||/(webkit)[ \/]([\w.]+)/.exec(n)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(n)||/(msie) ([\w.]+)/.exec(n)||n.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(n)||[];return{browser:t[1]||"",version:t[2]||"0"}};ej.EjSvgScrollbarRender=function(n,t){var i,r;this.svgSupport=window.SVGSVGElement?!0:!1;i=jQuery(n).attr("id");this.scrollsvgObj=this.scrollsvgObj?this.scrollsvgObj:[];this.svgSupport?(this.svgLink="http://www.w3.org/2000/svg",this.scrollsvgObj[t.index]=document.createElementNS(this.svgLink,"svg"),this.scrollsvgObj[t.index].setAttribute("id","scrollbar_"+i+t.index)):(r=document,this.scrollsvgObj[t.index]=r.createElement("div"),this.scrollsvgObj[t.index].style.position="relative",this.scrollsvgObj[t.index].setAttribute("id","scrollbar_"+i+t.index))},function(n){ej.EjSvgScrollbarRender.prototype={_initializeScrollbarVariables:function(n){n.offsetLeftX=0;n.rectWidth=n.offsetRightX=n.oldWidth=n.width;n.startX=0;n.endX=0;n.scrollbarLoaded=!1;(n.zoomPosition||n.zoomFactor)&&(n.offsetLeftX=n.zoomPosition*n.width,n.rectWidth=n.zoomFactor*n.width)},_scrollbarUpdate:function(n){var o=parseFloat(n.width),t=15,u=44,e=o-30,f;n.offsetLeftX=n.scrollbarLoaded?n.offsetLeftX-t:n.offsetLeftX;n.offsetLeftX=n.offsetLeftX<t?0:n.offsetLeftX;var r=n.offsetLeftX/n.oldWidth*e+t,i=n.rectWidth/n.oldWidth*e,s=i<u;n._diff=s?u-i:0;i=i>u?i:u;n.oldWidth=e;f=r+i;n.rectWidth=i;n.offsetLeftX=n.startX=r;n.offsetRightX=f-t<=r?f+t:f;n.endX=n.width-n.offsetRightX;n.startX=r<t?t:r;r+i>n.width-t&&(n.offsetLeftX=n.startX=n.width-t-i,n.offsetRightX=n.width-t,n.endX=n.width-n.offsetRightX);n.scrollbarLoaded=!0},_renderScrollbar:function(t){var o,w,b,k,d,g,nt,tt,it,rt,ut,ft,et,v;this.scrollsvgObj=this.scrollsvgObj?this.scrollsvgObj:[];n(this.scrollsvgObj[t.index]).empty();this.scrollsvgObj[t.index]&&n(document).find("[id*= "+this.scrollsvgObj[t.index].id+"]").attr("height","0px");var i=this._id,e=15,s=18,c=44;this.scrollsvgObj[t.index]=this.scrollbarContainer.scrollsvgObj[t.index];var l=17,f=parseFloat(t.width),y=8,p=l/2,h=f-e*2;t.offsetLeftX=t.scrollbarLoaded?t.offsetLeftX-e:t.offsetLeftX;t.offsetLeftX=t.offsetLeftX<e?0:t.offsetLeftX;var u=t.offsetLeftX/t.oldWidth*h+e,r=t.rectWidth/t.oldWidth*h,ot=r<c;t._diff=ot?c-r:0;r=r>c?r:c;t.oldWidth=h;u=u+r>h?h-(r-e):u;o=u+r;t.rectWidth=r;t.offsetLeftX=t.startX=u;t.offsetRightX=u+r;t.endX=t.width-t.offsetRightX;t.scrollbarLoaded=!0;o=o-e<=u?o+e:o;w=this.padding||0;b=this.vmlRendering?-5:0;this.scrollbar=t.orientation=="horizontal"?this.renderer.createGroup({id:i+"_scrollbar_"+t.index,transform:"translate("+w+","+b+")"}):this.renderer.createGroup({id:i+"_scrollbar_"+t.index,transform:"translate(0,"+t.width+") rotate(270)"});var st=function(n,t,i,r,u,f){var e=" ",c="M",h="h",o="v",s="a",l="z",a=f.orientation,v=f.opposed;return a=="vertical"&&!v||v&&a!="vertical"?c+e+n+e+u+e+o+e+(r-u)+e+h+e+i+e+o+e+(u-r)+e+s+e+u+e+u+e+0+e+0+e+0+e+-u+e+-u+e+h+e+-(i-2*u)+e+s+e+u+e+u+e+0+e+0+e+0+e+-4+e+u+e+l:c+e+n+e+t+e+o+e+(r-u)+e+s+e+u+e+u+e+0+e+0+e+0+e+u+e+u+e+h+e+(i-2*u)+e+s+e+u+e+u+e+0+e+0+e+0+e+u+e+-u+e+o+e+(u-r)+e+l},ht=st(0,0,f,s,4,t),a={id:i+"_scrollbarBackRect_"+t.index,"stroke-width":1,height:s,width:f,"stroke-linejoin":"round",stroke:"#B4B4B4",fill:"#F7F7F7","class":"e-rangeScroll-backRect"};this.vmlRendering?this.renderer.drawRect(a,this.scrollbar):(a.d=ht,this.renderer.drawPath(a,this.scrollbar));k={id:i+"_scrollbarRightRect_"+t.index,"stroke-width":1,x:f-5,height:s,width:5,"stroke-linejoin":"round",stroke:"transparent",fill:"transparent"};this.renderer.drawRect(k,this.scrollbar);d={id:i+"_scrollbarLeftRect_"+t.index,"stroke-width":1,x:0,height:s,width:5,"stroke-linejoin":"round",stroke:"transparent",fill:"transparent"};this.renderer.drawRect(d,this.scrollbar);g={id:i+"_scrollbarLeftArrow_"+t.index,"stroke-width":1,stroke:"#999999",d:"M 5 9 L 10 14 L 10 "+3.5+" Z",fill:"#999999","class":"e-rangeScroll-arrow"};this.renderer.drawPath(g,this.scrollbar);nt={id:i+"_scrollbarRightArrow_"+t.index,"stroke-width":1,stroke:"#999999",d:"M "+(f-5)+" 9 L "+(f-10)+" 14 L "+(f-10)+" "+3.5+" Z",fill:"#999999","class":"e-rangeScroll-arrow"};this.renderer.drawPath(nt,this.scrollbar);tt={id:i+"_scrollbarSelect_"+t.index,x:u,y:0,width:r,rx:4,ry:4,height:l,stroke:"#999999","stroke-width":1,fill:"#CECECE","class":"e-rangeScroll-select"};this.renderer.drawRect(tt,this.scrollbar);this.centerLine=this.renderer.createGroup({id:i+"_scrollbarCenterLine_"+t.index,transform:"translate("+(u+r/2-7.5)+")"});it={id:i+"_scrollbarCenterShape_"+t.index,"stroke-width":1,stroke:"#999999",d:"M 0 "+3.5+" L 0 "+13.5+" ZM 5 "+3.5+" L 5 "+13.5+" ZM 10 "+3.5+" L 10 "+13.5+" ZM 15 "+3.5+" L 15 "+13.5+" Z",fill:"#999999","class":"e-rangeScroll-centerShape"};this.renderer.drawPath(it,this.centerLine);this.renderer.append(this.centerLine,this.scrollbar);rt={id:i+"_leftHeaderHideRect_"+t.index,x:u,y:0,width:11,height:17,fill:"transparent",opacity:0,"stroke-width":1,"class":"e-rangeScroll-leftRect"};this.renderer.drawRect(rt,this.scrollbar);ut={id:i+"_scrollbarLeftHeader_"+t.index,cx:u+y,cy:p,r:3,fill:"#999999",stroke:"#999999","stroke-width":1,"class":"e-rangeScroll-leftCircle"};this.renderer.drawCircle(ut,this.scrollbar);ft={id:i+"_rightHeaderHideRect_"+t.index,x:o-11,y:0,width:11,height:l,fill:"transparent",opacity:0,"stroke-width":1,"class":"e-rangeScroll-rightRect"};this.renderer.drawRect(ft,this.scrollbar);et={id:i+"_scrollbarRightHeader_"+t.index,fill:"#999999",cx:o-y,cy:p,r:3,stroke:"#999999","stroke-width":1,"class":"e-rangeScroll-rightCircle"};this.renderer.drawCircle(et,this.scrollbar);this.renderer.append(this.scrollbar,this.scrollsvgObj[t.index]);t.orientation=="horizontal"?(this.scrollsvgObj[t.index].setAttribute("height",s),this.scrollsvgObj[t.index].setAttribute("width",f)):(this.scrollsvgObj[t.index].setAttribute("height",f),this.scrollsvgObj[t.index].setAttribute("width",s));this.renderer.append(this.scrollsvgObj[t.index],t.parent);v=this.vmlRendering?t.y+5:t.y;this.pluginName=="ejRangeNavigator"?this.scrollsvgObj[t.index].setAttribute("style","overflow:visible;position:relative;display:block; top:"+(v-t.y)+"px;left:"+t.x+"px"):this.scrollsvgObj[t.index].setAttribute("style","overflow:visible;position:absolute;display:block; top:"+v+"px;left:"+t.x+"px");n("#"+i+"_scrollbarSelect_"+t.index).css({cursor:"pointer"});t.enableResize?(n("#"+i+"_leftHeaderHideRect_"+t.index).css({cursor:t.orientation=="horizontal"?"w-resize":"n-resize"}),n("#"+i+"_rightHeaderHideRect_"+t.index).css({cursor:t.orientation=="horizontal"?"w-resize":"n-resize"}),n("#"+i+"_scrollbarRightHeader_"+t.index).css({cursor:t.orientation=="horizontal"?"w-resize":"n-resize"}),n("#"+i+"_scrollbarLeftHeader_"+t.index).css({cursor:t.orientation=="horizontal"?"w-resize":"n-resize"})):(n("#"+i+"_leftHeaderHideRect_"+t.index).hide(),n("#"+i+"_rightHeaderHideRect_"+t.index).hide(),n("#"+i+"_scrollbarLeftHeader_"+t.index).hide(),n("#"+i+"_scrollbarRightHeader_"+t.index).hide());this.vmlRendering&&(n("#"+i+"_leftHeaderHideRect_"+t.index).css("visibility","hidden"),n("#"+i+"_rightHeaderHideRect_"+t.index).css("visibility","hidden"));this.scrollbarContainer._bindScrollEvents.call(this,t)},_bindScrollEvents:function(t){function e(t){this._on(n("#"+i+"_scrollbarLeftHeader_"+t.index),"mousedown",this.scrollbarContainer._leftScrollbarDown);this._on(n("#"+i+"_scrollbarRightHeader_"+t.index),"mousedown",this.scrollbarContainer._rightScrollbarDown);this._on(n("#"+i+"_leftHeaderHideRect_"+t.index),"mousedown",this.scrollbarContainer._leftScrollbarDown);this._on(n("#"+i+"_rightHeaderHideRect_"+t.index),"mousedown",this.scrollbarContainer._rightScrollbarDown);this._on(n("#"+i+"_scrollbarLeftArrow_"+t.index),"mousedown",this.scrollbarContainer._leftArrowDown);this._on(n("#"+i+"_scrollbarLeftArrow_"+t.index),"mouseup",this.scrollbarContainer._leftArrowUp);this._on(n("#"+i+"_scrollbarRightArrow_"+t.index),"mousedown",this.scrollbarContainer._rightArrowDown);this._on(n("#"+i+"_scrollbarRightArrow_"+t.index),"mouseup",this.scrollbarContainer._rightArrowUp);this._on(n("#"+i+"_scrollbarSelect_"+t.index),"mousedown",this.scrollbarContainer._scrollSelectRectDown);this._on(n("#"+i+"_scrollbarBackRect_"+t.index),"mousedown",this.scrollbarContainer._scrollBackRectDown);this._on(n("#"+i+"_scrollbarBackRect_"+t.index),"mouseup",this.scrollbarContainer._scrollBackRectUp);this._on(n(window),"mousemove",this.scrollbarContainer._scrollbarMove);this._on(n(window),"mouseup",this.scrollbarContainer._scrollbarUp);this._on(n(this.svgObject),"mousemove",this.scrollbarContainer._scrollbarMove);this._on(n(this.scrollsvgObj[t.index]),"mousemove",this.scrollbarContainer._scrollbarMove);this._on(n(this.scrollsvgObj[t.index]),"mouseup",this.scrollbarContainer._scrollbarUp)}function o(t){this._on(n("#"+i+"_scrollbarLeftHeader_"+t.index),"touchstart",this.scrollbarContainer._leftScrollbarDown);this._on(n("#"+i+"_scrollbarRightHeader_"+t.index),"touchstart",this.scrollbarContainer._rightScrollbarDown);this._on(n("#"+i+"_leftHeaderHideRect_"+t.index),"touchstart",this.scrollbarContainer._leftScrollbarDown);this._on(n("#"+i+"_rightHeaderHideRect_"+t.index),"touchstart",this.scrollbarContainer._rightScrollbarDown);this._on(n("#"+i+"_scrollbarLeftArrow_"+t.index),"touchstart",this.scrollbarContainer._leftArrowDown);this._on(n("#"+i+"_scrollbarLeftArrow_"+t.index),"touchend",this.scrollbarContainer._leftArrowUp);this._on(n("#"+i+"_scrollbarRightArrow_"+t.index),"touchstart",this.scrollbarContainer._rightArrowDown);this._on(n("#"+i+"_scrollbarRightArrow_"+t.index),"touchend",this.scrollbarContainer._rightArrowUp);this._on(n("#"+i+"_scrollbarSelect_"+t.index),"touchstart",this.scrollbarContainer._scrollSelectRectDown);this._on(n("#"+i+"_scrollbarBackRect_"+t.index),"touchstart",this.scrollbarContainer._scrollBackRectDown);this._on(n("#"+i+"_scrollbarBackRect_"+t.index),"touchend",this.scrollbarContainer._scrollBackRectUp);this._on(n(window),"touchmove",this.scrollbarContainer._scrollbarMove);this._on(n(window),"touchend",this.scrollbarContainer._scrollbarUp);this._on(n(this.scrollsvgObj[t.index]),"touchmove",this.scrollbarContainer._scrollbarMove);this._on(n(this.scrollsvgObj[t.index]),"touchend",this.scrollbarContainer._scrollbarUp)}var i=this._id,u=jQuery.uaMatch(navigator.userAgent),r=!!navigator.userAgent.match(/Trident\/7\./),f;if(e.call(this,t),window.PointerEvent)this._on(n("#"+i+"_scrollbarLeftHeader_"+t.index),"pointerdown",this.scrollbarContainer._leftScrollbarDown),this._on(n("#"+i+"_scrollbarRightHeader_"+t.index),"pointerdown",this.scrollbarContainer._rightScrollbarDown),this._on(n("#"+i+"_leftHeaderHideRect_"+t.index),"pointerdown",this.scrollbarContainer._leftScrollbarDown),this._on(n("#"+i+"_rightHeaderHideRect_"+t.index),"pointerdown",this.scrollbarContainer._rightScrollbarDown),this._on(n("#"+i+"_scrollbarLeftArrow_"+t.index),"pointerdown",this.scrollbarContainer._leftArrowDown),this._on(n("#"+i+"_scrollbarLeftArrow_"+t.index),"pointerup",this.scrollbarContainer._leftArrowUp),this._on(n("#"+i+"_scrollbarRightArrow_"+t.index),"pointerdown",this.scrollbarContainer._rightArrowDown),this._on(n("#"+i+"_scrollbarRightArrow_"+t.index),"pointerup",this.scrollbarContainer._rightArrowUp),this._on(n("#"+i+"_scrollbarSelect_"+t.index),"pointerdown",this.scrollbarContainer._scrollSelectRectDown),this._on(n("#"+i+"_scrollbarBackRect_"+t.index),"pointerdown",this.scrollbarContainer._scrollBackRectDown),this._on(n("#"+i+"_scrollbarBackRect_"+t.index),"pointerup",this.scrollbarContainer._scrollBackRectUp),this._on(n(window),"pointermove",this.scrollbarContainer._scrollbarMove),this._on(n(window),"pointerup",this.scrollbarContainer._scrollbarUp),this._on(n(this.scrollsvgObj[t.index]),"pointermove",this.scrollbarContainer._scrollbarMove),this._on(n(this.scrollsvgObj[t.index]),"pointerup",this.scrollbarContainer._scrollbarUp),n(this.scrollsvgObj[t.index]).css("touch-action","none");else if(window.navigator.msPointerEnabled&&!r)this._on(n("#"+i+"_scrollbarLeftHeader_"+t.index),"MSPointerDown",this.scrollbarContainer._leftScrollbarDown),this._on(n("#"+i+"_scrollbarRightHeader_"+t.index),"MSPointerDown",this.scrollbarContainer._rightScrollbarDown),this._on(n("#"+i+"_leftHeaderHideRect_"+t.index),"MSPointerDown",this.scrollbarContainer._leftScrollbarDown),this._on(n("#"+i+"_rightHeaderHideRect_"+t.index),"MSPointerDown",this.scrollbarContainer._rightScrollbarDown),this._on(n("#"+i+"_scrollbarLeftArrow_"+t.index),"MSPointerDown",this.scrollbarContainer._leftArrowDown),this._on(n("#"+i+"_scrollbarLeftArrow_"+t.index),"MSPointerUp",this.scrollbarContainer._leftArrowUp),this._on(n("#"+i+"_scrollbarRightArrow_"+t.index),"MSPointerDown",this.scrollbarContainer._rightArrowDown),this._on(n("#"+i+"_scrollbarRightArrow_"+t.index),"MSPointerUp",this.scrollbarContainer._rightArrowUp),this._on(n("#"+i+"_scrollbarSelect_"+t.index),"MSPointerDown",this.scrollbarContainer._scrollSelectRectDown),this._on(n("#"+i+"_scrollbarBackRect_"+t.index),"MSPointerDown",this.scrollbarContainer._scrollBackRectDown),this._on(n("#"+i+"_scrollbarBackRect_"+t.index),"MSPointerUp",this.scrollbarContainer._scrollBackRectUp),this._on(n(window),"MSPointerMove",this.scrollbarContainer._scrollbarMove),this._on(n(window),"MSPointerUp",this.scrollbarContainer._scrollbarUp),this._on(n(this.scrollsvgObj[t.index]),"MSPointerMove",this.scrollbarContainer._scrollbarMove),this._on(n(this.scrollsvgObj[t.index]),"MSPointerUp",this.scrollbarContainer._scrollbarUp),n(this.scrollsvgObj[t.index]).css("-ms-touch-action","none");else if(u.browser.toLowerCase()=="chrome")o.call(this,t);else if(this.isDevice()){var s=navigator.userAgent.indexOf("Safari")!=-1&&navigator.userAgent.indexOf("Chrome")==-1,h=window.location!=window.parent.location?!0:!1,c=h?window.parent.navigator.userAgent.toLowerCase():window.navigator.userAgent.toLowerCase(),l=/mobile|tablet|android|kindle/i.test(c);ej.isMobile()&&this.isWindows()||!l&&ej.isMobile()&&s?(e.call(this),n(this.scrollsvgObj[t.index]).css("-ms-touch-action","none")):o.call(this,t)}f=u.browser.toLowerCase()=="mozilla"?r?"mousewheel":"DOMMouseScroll":"mousewheel";this._on(n(this.scrollsvgObj[t.index]),f,this.scrollbarContainer._scrollMouseWheel);r&&n(this.scrollsvgObj[t.index]).css("touch-action","none")},_setScrollPosition:function(t,i,r){var f=8,u=this._id,e=t+r.rectWidth/2-7.5;n("#"+u+"_scrollbarLeftHeader_"+r.index).attr("cx",t+f);n("#"+u+"_leftHeaderHideRect_"+r.index).attr("x",t);n("#"+u+"_scrollbarRightHeader_"+r.index).attr("cx",i-f);n("#"+u+"_rightHeaderHideRect_"+r.index).attr("x",i-5);n("#"+u+"_scrollbarSelect_"+r.index).attr("x",t);n("#"+u+"_scrollbarSelect_"+r.index).attr("width",r.rectWidth);n("#"+u+"_scrollbarCenterLine_"+r.index).attr("transform","translate("+e+")");this.pluginName=="ejRangeNavigator"&&(n("#"+u+"_scrollbarLeftHeader_"+r.index).css("left",t),n("#"+u+"_leftHeaderHideRect_"+r.index).css("left",t),n("#"+u+"_scrollbarRightHeader_"+r.index).css("left",i-f),n("#"+u+"_rightHeaderHideRect_"+r.index).css("left",i-5),n("#"+u+"_scrollbarSelect_"+r.index).css("left",t),n("#"+u+"_scrollbarCenterLine_"+r.index).css("left",e))},_calculateScrollLeftMove:function(n,t){t._startX-n>15?(t.startX=t._startX-n,t.offsetLeftX=t.startX,t._offsetRightX=t.startX+t.rectWidth,t.offsetRightX=t._offsetRightX,t.endX=t.width-t.offsetRightX):(t.startX=15,t._offsetLeftX=t.offsetLeftX,t.offsetLeftX=t.startX,t._offsetRightX=t.startX+t.rectWidth,t.offsetRightX=t._offsetRightX,t.endX=t.width-t.offsetRightX)},_calculateScrollRightMove:function(n,t){t&&t._offsetRightX+Math.abs(n)<t.width-15?(t._startX=t.startX+Math.abs(n),t.offsetLeftX=t._startX,t.offsetRightX=t._offsetRightX+Math.abs(n),t.endX=t.width-t.offsetRightX):(t._ofsetRightX=t.offsetRightX,t.offsetRightX=t.width-15,t._startX=t.offsetRightX-t.rectWidth,t.offsetLeftX=t._startX,t.endX=t.width-t.offsetRightX)},_calculateRange:function(t,i,r){var o,s,e,u,f,a=30,h,c,l;return o=(t-15)/(r.width-a-r._diff),e=r.scrollRange,s=(r.rectWidth-r._diff)/(r.width-a-r._diff),r.isRTL?(f=e.max-o*e.delta,u=f-s*e.delta):(u=e.min+o*e.delta,f=u+s*e.delta),r.valueType=="datetime"?(h=r.startDateTime,c=r.endDateTime,r.startDateTime=this.startDateTime=u=new Date(u),r.endDateTime=this.endDateTime=f=new Date(f)):(h=parseInt(r.startValue),c=parseInt(r.endValue),r.startValue=this.startValue=u=Math.ceil(u),r.endValue=this.endValue=f=Math.ceil(f)),r._scrollChanged=!0,l=n.extend({},ej.EjSvgRender.commonChartEventArgs),l.data={zoomPosition:o,zoomFactor:s,oldRange:{start:h,end:c},newRange:{start:u,end:f}},r.zoomPosition=o,r.zoomFactor=s,l},_calculateScrollPosition:function(t,i){var s,h,f,r,u,e,o,y=30,a=15,c,p,w=36,l,v;if(this.leftScrollbarClicked&&i.enableResize)return t=t>=a?t:a,t=t>=a&&t<i.offsetRightX-a?t:i.offsetRightX-a,s=(t-w)/(i.width-y),h=i.rectWidth/(i.width-y),c=i.valueType.toLowerCase(),f=i.scrollRange,i.isRTL?(u=f.max-s*f.delta,u=u<f.max?u:f.max,c=="datetime"?(e=i.startDateTime,o=i.endDateTime,i.endDateTime=this.endDateTime=u=new Date(u),r=i.startDateTime):(e=parseInt(i.startValue),o=parseInt(i.endValue),i.endValue=this.endValue=u=Math.ceil(u),r=Math.ceil(i.startValue))):(r=f.min+s*f.delta,r=r>f.min?r:f.min,c=="datetime"?(e=i.startDateTime,o=i.endDateTime,i.startDateTime=this.startDateTime=r=new Date(r),u=i.endDateTime):(e=parseInt(i.startValue),o=parseInt(i.endValue),i.startValue=this.startValue=r=Math.ceil(r),u=Math.ceil(i.endValue))),l=n.extend({},ej.EjSvgRender.commonChartEventArgs),l.data={zoomPosition:s,zoomFactor:h,oldRange:{start:e,end:o},newRange:{start:r,end:u}},i._scrollChanged=!0,i.zoomPosition=s,i.zoomFactor=h,l;if(this.rightScrollbarClicked&&i.enableResize)return f=i.scrollRange,h=i.rectWidth/(i.width-y),s=i.zoomPosition,c=i.valueType.toLowerCase(),i.isRTL?(s=(t-w)/(i.width-y),r=f.max-s*f.delta,c=="datetime"?(e=i.startDateTime,o=i.endDateTime,i.startDateTime=this.startDateTime=r=new Date(r),u=i.endDateTime):(e=parseInt(i.startValue),o=parseInt(i.endValue),i.startValue=this.startValue=r=Math.floor(r),u=Math.ceil(i.endValue))):(p=f.min+s*f.delta,u=p+h*f.delta,c=="datetime"?(e=i.startDateTime,o=i.endDateTime,i.endDateTime=this.endDateTime=u=new Date(u),r=i.startDateTime):(e=parseInt(i.startValue),o=parseInt(i.endValue),i.endValue=this.endValue=u=Math.floor(u),r=Math.ceil(i.startValue))),l=n.extend({},ej.EjSvgRender.commonChartEventArgs),l.data={zoomPosition:i.zoomPosition?i.zoomPosition:0,zoomFactor:h,oldRange:{start:e,end:o},newRange:{start:r,end:u}},i._scrollChanged=!0,i.zoomFactor=h,l;if(i&&this.scrollRectClicked){if(v=this.mouseDownPos-t,v>0&&i.offsetLeftX>=0)return this.scrollbarContainer._calculateScrollLeftMove.call(this,v,i),this.scrollbarContainer._calculateRange.call(this,i.startX,i.offsetRightX,i);if(v<0)return this.scrollbarContainer._calculateScrollRightMove.call(this,v,i),this.scrollbarContainer._calculateRange.call(this,i._startX,i.offsetRightX,i)}},_scrollbarMove:function(t){var u,s;if(this.moveClientX=ej.isNullOrUndefined(t.clientX)?t.originalEvent.clientX==null?t.originalEvent.touches[0].clientX:t.originalEvent.clientX:t.clientX,this.moveClientX!=this.downClientX){var f=8,o=34,r,e,w=this.model.padding||0,b=jQuery.uaMatch(navigator.userAgent),i=this.scrollbarContainer._doScrollIndex.call(this,t);if(!i)return 0;if(this.lastIndex=i.index,r=!this.vmlRendering&&t.originalEvent.toString()!=="[object TouchEvent]"&&b.browser.toLowerCase()!="msie"||this.vmlRendering?(i.orientation=="horizontal"?this.calMousePosition(t).X-i.x:i.width-(this.calMousePosition(t).Y-i.y))-w:(i.orientation=="horizontal"?this.calTouchPosition(t).X-i.x:i.width-(this.calTouchPosition(t).Y-i.y))-w,this.mouseX=r,u=this._id,this.leftScrollbarClicked&&i.enableResize&&(i&&(r>o||r>23)&&r<i.offsetRightX-o?(i.rectWidth=i.width-(i.endX+r-f),e=r-f+i.rectWidth/2-7.5,n("#"+u+"_scrollbarLeftHeader_"+i.index).attr("cx",r),n("#"+u+"_leftHeaderHideRect_"+i.index).attr("x",r-7.5),n("#"+u+"_scrollbarSelect_"+i.index).attr("x",r-f),n("#"+u+"_scrollbarSelect_"+i.index).attr("width",i.rectWidth),n("#"+u+"_scrollbarCenterLine_"+i.index).attr("transform","translate("+e+")"),this.pluginName=="ejRangeNavigator"&&(n("#"+u+"_scrollbarLeftHeader_"+i.index).css("left",r),n("#"+u+"_leftHeaderHideRect_"+i.index).css("left",r-7.5),n("#"+u+"_scrollbarSelect_"+i.index).css("left",r),n("#"+u+"_scrollbarSelect_"+i.index).css("width",i.rectWidth),n("#"+u+"_scrollbarCenterLine_"+i.index).css("left",e)),i.offsetLeftX=i.startX=r-8,this.scrollbarContainer._scrollStartArgs.call(this,i),this.scrollbarContainer._scrollChangeArgs.call(this,i),i._diff=0):r>i.offsetRightX-o&&(this.rectWidth=42.899841017488143)),this.rightScrollbarClicked&&i.enableResize&&i&&(r<=i.width-o||r<=i.width-23)&&r>i.offsetLeftX+o&&(i.rectWidth=r-i.offsetLeftX+f,e=i.offsetLeftX+i.rectWidth/2-7.5,n("#"+u+"_scrollbarRightHeader_"+i.index).attr("cx",r),n("#"+u+"_rightHeaderHideRect_"+i.index).attr("x",r-2.5),n("#"+u+"_scrollbarSelect_"+i.index).attr("width",i.rectWidth),n("#"+u+"_scrollbarCenterLine_"+i.index).attr("transform","translate("+e+")"),this.pluginName=="ejRangeNavigator"&&(n("#"+u+"_scrollbarRightHeader_"+i.index).css("left",r),n("#"+u+"_rightHeaderHideRect_"+i.index).css("left",r-2.5),n("#"+u+"_scrollbarSelect_"+i.index).css("width",i.rectWidth),n("#"+u+"_scrollbarCenterLine_"+i.index).css("left",e)),i.offsetRightX=r+f,i.endX=i.width-r-f,this.scrollbarContainer._scrollStartArgs.call(this,i),this.scrollbarContainer._scrollChangeArgs.call(this,i),i._diff=0),this.scrollRectClicked&&(s=this.mouseDownPos-r,i._startX=ej.isNullOrUndefined(i._startX)?i.startX:i._startX,i&&s>0&&i.offsetLeftX>=0&&(this.scrollbarContainer._calculateScrollLeftMove.call(this,s,i),this.scrollbarContainer._setScrollPosition.call(this,i.startX,i.offsetRightX,i),(i.offsetLeftX>15||i.offsetLeftX!=i._offsetLeftX)&&(this.scrollbarContainer._scrollStartArgs.call(this,i),this.scrollbarContainer._scrollChangeArgs.call(this,i))),i&&s<0&&(ej.isNullOrUndefined(i._offsetRightX)&&(this.scrollbarContainer._scrollSelectRectDown.call(this,t),this.scrollbarContainer._scrollbarMove.call(this,t)),this.scrollbarContainer._calculateScrollRightMove.call(this,s,i),this.scrollbarContainer._setScrollPosition.call(this,i._startX,i._startX+i.rectWidth,i),(i.offsetRightX<i.width-15||i.offsetRightX!=i._ofsetRightX)&&(this.scrollbarContainer._scrollStartArgs.call(this,i),this.model.enableDeferredUpdate===!1||i.isZooming||this.model.scrollChanged!="")))){var h=this.scrollbarContainer._calculateRange.call(this,i._startX,i._startX+i.rectWidth,i),c=h.data.oldRange,l=h.data.newRange,a,v,y,p;i.valueType.toLowerCase()=="datetime"?(a=Date.parse(c.start),y=Date.parse(l.start),v=Date.parse(c.end),p=Date.parse(l.end)):(a=c.start,y=l.start,v=c.end,p=l.end);this.model.scrollChanged!=""&&(a!=y||v!=p)&&(h=this.scrollbarContainer._args.call(this,i,h),this._trigger("scrollChanged",h));this.model.enableDeferredUpdate||(this.scrollUpdate?this.scrollUpdate=!1:this.scrollbarContainer._appendScrollRange.call(this,i))}}this.offsetX=r},_scrollbarUp:function(t){var i=this.scrollbarContainer._doScrollIndex.call(this,t),r,s,u,f,h,c,e,o,l;n("[id^="+this._id+"_scrollbarSelect_]").attr("class","e-rangeScroll-select");this.lastIndex=null;r=this.scrollbarContainer._calculateScrollPosition.call(this,this.mouseX,i);s=this.model.enableDeferredUpdate;r&&(u=r.data.oldRange,f=r.data.newRange,i.valueType.toLowerCase()=="datetime"?(h=Date.parse(u.start),e=Date.parse(f.start),c=Date.parse(u.end),o=Date.parse(f.end)):(h=u.start,e=f.start,c=u.end,o=f.end),(h!=e||c!=o||this.scrollRectClicked&&!s||s||this.leftScrollbarClicked||this.rightScrollbarClicked)&&(l=navigator.userAgent.toLowerCase().indexOf("chrome")>-1&&navigator.vendor.toLowerCase().indexOf("google")>-1,(!l||l&&(this.leftScrollbarClicked||this.rightScrollbarClicked)&&this.downClientX!=this.moveClientX||this.scrollRectClicked||this.leftArrowClicked||this.rightArrowClicked)&&(!this.leftScrollbarClicked&&!this.rightScrollbarClicked||(this.leftScrollbarClicked||this.rightScrollbarClicked)&&Math.ceil(i.rectWidth)!=Math.ceil(this.rectOldWidth)&&(Math.abs(i.rectWidth-i.rectOldWidth)>4||!i.rectOldWidth))&&(ej.isNullOrUndefined(i._previousStart)||i._previousStart!=e||i._previousEnd!=o)&&(r=this.scrollbarContainer._args.call(this,i,r),this._trigger("scrollEnd",r),i._scrollStarted=!1,i._previousStart=e,i._previousEnd=o,i.rectOldWidth=i.rectWidth,this.scrollUpdate?this.scrollUpdate=!1:this.scrollbarContainer._appendScrollRange.call(this,i))),i.release=!0,i.clicked=!1);this._scrollEnd=!1;this.leftScrollbarClicked=!1;this.rightScrollbarClicked=!1;this.scrollRectClicked=!1;this.leftArrowClicked=!1;this.rightArrowClicked=!1;this.scrollbarBackRectClicked=!1},_leftScrollbarDown:function(n){n.preventDefault();this.leftScrollbarClicked=!0;this.downClientX=ej.isNullOrUndefined(n.clientX)?n.originalEvent.touches[0].clientX:n.clientX},_rightScrollbarDown:function(n){n.preventDefault();this.rightScrollbarClicked=!0;this.downClientX=ej.isNullOrUndefined(n.clientX)?n.originalEvent.touches[0].clientX:n.clientX},_scrollSelectRectDown:function(t){var i=this.scrollbarContainer._doScrollIndex.call(this,t),u;n("#"+this._id+"_scrollbarSelect_"+i.index).attr("class","e-rangeScroll-select e-rangeScroll-select-hover");t.preventDefault();var f=jQuery.uaMatch(navigator.userAgent),r=this.model.padding||0,i=this.scrollbarContainer._doScrollIndex.call(this,t);u=!this.vmlRendering&&t.originalEvent.toString()!=="[object TouchEvent]"&&f.browser.toLowerCase()!="msie"||this.vmlRendering?(i.orientation=="horizontal"?this.calMousePosition(t).X-i.x:i.width-(this.calMousePosition(t).Y-i.y))-r:(i.orientation=="horizontal"?this.calTouchPosition(t).X-i.x:i.width-(this.calTouchPosition(t).Y-i.y))-r;this.mouseDownPos=u;i._startX=i.startX=i.offsetLeftX;i._offsetRightX=i.offsetRightX;i.clicked=!0;i.release=!1;this.scrollRectClicked=!0},_scrollMouseWheel:function(n){n.preventDefault();var t=this.scrollbarContainer._doScrollIndex.call(this,n),r=!!navigator.userAgent.match(/Trident\/7\./),i=n.originalEvent.wheelDelta,u=jQuery.uaMatch(navigator.userAgent),f=u.browser.toLowerCase()=="mozilla"?r?i/120>0?1:-1:-n.originalEvent.detail/3>0?1:-1:i/120>0?1:-1,e=Math.max(1/ej.EjSvgRender.utils._minMax(t.zoomFactor,0,1),1),o=Math.max(e+.25*f,1);this.scrollbarContainer.doMouseWheelZoom.call(this,o,.5,t)},_scrollStartArgs:function(t){if(t._scrollStarted=!0,this.model.scrollStart!=""&&!this._scrollEnd){var i=n.extend({},ej.EjSvgRender.commonChartEventArgs);i.data=t.isZooming?{axis:this.model._axes[t.index],currentRange:this.model._axes[t.index].visibleRange}:{startRange:t.valueType=="datetime"?t.startDateTime:t.startValue,endRange:t.valueType=="datetime"?t.endDateTime:t.endValue};this._trigger("scrollStart",i);this._scrollEnd=!0}},_scrollChangeArgs:function(n){if(this.model.enableDeferredUpdate===!1||n.isZooming||this.model.scrollChanged!=""){var t=this.scrollbarContainer._calculateRange.call(this,n.offsetLeftX,n.offsetRightX,n),i=t.data.oldRange,r=t.data.newRange,u,f,e,o;n.valueType.toLowerCase()=="datetime"?(u=Date.parse(i.start),e=Date.parse(r.start),f=Date.parse(i.end),o=Date.parse(r.end)):(u=i.start,e=r.start,f=i.end,o=r.end);this.model.scrollChanged!=""&&(u!=e||f!=o)&&(t=this.scrollbarContainer._args.call(this,n,t),this._trigger("scrollChanged",t));this.model.enableDeferredUpdate||(this.scrollUpdate?this.scrollUpdate=!1:this.scrollbarContainer._appendScrollRange.call(this,n))}},_scrollEndArgs:function(n,t){var i=this.scrollbarContainer._calculateRange.call(this,n,t.offsetRightX,t),r,u,f,e,o,s;i&&t._scrollStarted&&(r=i.data.oldRange,u=i.data.newRange,t.valueType.toLowerCase()=="datetime"?(f=Date.parse(r.start),o=Date.parse(u.start),e=Date.parse(r.end),s=Date.parse(u.end)):(f=r.start,o=u.start,e=r.end,s=u.end),(f!=o||e!=s||this.leftScrollbarClicked||this.rightScrollbarClicked)&&(i=this.scrollbarContainer._args.call(this,t,i),this._trigger("scrollEnd",i)),t._scrollStarted=!1,this.scrollUpdate?this.scrollUpdate=!1:this.scrollbarContainer._appendScrollRange.call(this,t))},_args:function(n,t){if(n.isZooming){var i=this.model._axes[n.index],r=t.data;r.axis=i;r.newRange=i.visibleRange;r.oldRange=i.previousRange}return n.isVirtual&&(t.data.axis=this.model._axes[n.index],delete t.data.zoomFactor,delete t.data.zoomPosition),t},doMouseWheelZoom:function(n,t,i){if(n>=1){var r=this.scrollbarContainer.calZoomFactors(n,t,i.zoomFactor,i.zoomPosition);i.zoomPosition!=r.zoomMPosition&&r.zoomMPosition+i.zoomFactor<=1&&i.zoomPosition>=0&&(r.zoomMPosition+i.zoomFactor>=1&&(r.zoomMPosition=1-i.zoomFactor),r.zoomMPosition<0&&(r.zoomMPosition=0),i.zoomPosition=r.zoomMPosition,i.startX=i.zoomPosition*(i.width-30-i._diff)+15,i.startX=i.startX+i.rectWidth+5>i.width?i.width-i.rectWidth-15:i.startX,i.offsetLeftX=i.startX,i._offsetRightX=i.startX+i.rectWidth,i._offsetRightX>i.width-15?(i.offsetRightX=i.width-15,i.startX=i.offsetRightX-i.rectWidth,i.offsetLeftX=i.startX):i.offsetRightX=i._offsetRightX,i.endX=i.width-i.offsetRightX,this.scrollbarContainer._setScrollPosition.call(this,i.startX,i.offsetRightX,i),this.scrollbarContainer._calculateMouseWheelRange.call(this,i.startX,i.offsetRightX,i))}return!1},_calculateMouseWheelRange:function(t,i,r){var e,u,f,w=30,c,l,o,s,h,a,v,y,p;r.zoomFactor=r.rectWidth/(r.width-w);r.zoomPosition=(t-15)/(r.width-w-r._diff);e=r.scrollRange;r.isRTL?(f=e.max-r.zoomPosition*e.delta,u=f-r.zoomFactor*e.delta):(u=e.min+r.zoomPosition*e.delta,f=u+r.zoomFactor*e.delta);r.valueType=="datetime"?(c=r.startDateTime,l=r.endDateTime,r.startDateTime=this.startDateTime=u=new Date(u),r.endDateTime=this.endDateTime=f=new Date(f)):(c=parseInt(r.startValue),l=parseInt(r.endValue),r.startValue=this.startValue=u=Math.ceil(u),r.endValue=this.endValue=f=Math.ceil(f));o=n.extend({},ej.EjSvgRender.commonChartEventArgs);o.data={zoomPosition:r.zoomPosition,zoomFactor:r.zoomFactor,oldRange:{start:c,end:l},newRange:{start:u,end:f}};r._scrollChanged=!0;r._scrollStarted=!1;s=o.data.oldRange;h=o.data.newRange;r.valueType.toLowerCase()=="datetime"?(a=Date.parse(s.start),y=Date.parse(h.start),v=Date.parse(s.end),p=Date.parse(h.end)):(a=s.start,y=h.start,v=s.end,p=h.end);(a!=y||v!=p||r.isZooming)&&(this.scrollbarContainer._scrollStartArgs.call(this,r),o=this.scrollbarContainer._args.call(this,r,o),this._trigger("scrollEnd",o),this.scrollUpdate?this.scrollUpdate=!1:this.scrollbarContainer._appendScrollRange.call(this,r),this._scrollEnd=!1)},calZoomFactors:function(n,t,i,r){var u,f;return n==1?(u=1,f=0):(u=ej.EjSvgRender.utils._minMax(1/n,0,1),f=r+(i-u)*t),{zoomMFactor:u,zoomMPosition:f}},_leftArrowDown:function(n){var t,i;this.leftArrowClicked=!0;t=this.scrollbarContainer._doScrollIndex.call(this,n);t._startX=t.startX=t.offsetLeftX;t._offsetRightX=t.offsetRightX;t.leftIncrement=5;i=this;t.interval=setInterval(function(){i.scrollbarContainer.leftArrowRecursive.call(i,t)},50)},_leftArrowUp:function(n){this.leftArrowClicked=!1;var t=this.scrollbarContainer._doScrollIndex.call(this,n);this.scrollbarContainer.leftArrowRecursive.call(this,t)},leftArrowRecursive:function(n){if(this.leftArrowClicked){var t=this._id;this.scrollbarContainer._calculateScrollLeftMove.call(this,n.leftIncrement,n);n._scrollStarted=!0;n.startX>15&&this.scrollbarContainer._scrollEndArgs.call(this,n.startX,n);this.scrollbarContainer._setScrollPosition.call(this,n.startX,n.offsetRightX,n);n.leftIncrement+=5}else clearInterval(n.interval),n.leftIncrement=0},_rightArrowDown:function(n){var t,i;this.rightArrowClicked=!0;t=this.scrollbarContainer._doScrollIndex.call(this,n);t._startX=t.startX=t.offsetLeftX;t._offsetRightX=t.offsetRightX;t.rightIncrement=-5;i=this;t.interval=setInterval(function(){i.scrollbarContainer.rightArrowRecursive.call(i,t)},50)},_rightArrowUp:function(n){this.rightArrowClicked=!1;var t=this.scrollbarContainer._doScrollIndex.call(this,n);this.scrollbarContainer.rightArrowRecursive.call(this,t)},rightArrowRecursive:function(n){if(this.rightArrowClicked){var t=this._id;this.scrollbarContainer._calculateScrollRightMove.call(this,n.rightIncrement,n);n._scrollStarted=!0;n.offsetRightX<n.width-15&&this.scrollbarContainer._scrollEndArgs.call(this,n._startX,n);this.scrollbarContainer._setScrollPosition.call(this,n._startX,n.offsetRightX,n);n.rightIncrement-=5}else clearInterval(n.interval),n.rightIncrement=0},_scrollBackRectDown:function(n){var e=jQuery.uaMatch(navigator.userAgent),r=this.model.padding||0,t=this.scrollbarContainer._doScrollIndex.call(this,n),u,i,f;u=!this.vmlRendering&&n.originalEvent.toString()!=="[object TouchEvent]"&&e.browser.toLowerCase()!="msie"||this.vmlRendering?(t.orientation=="horizontal"?this.calMousePosition(n).X-t.x:t.width-(this.calMousePosition(n).Y-t.y))-r:(t.orientation=="horizontal"?this.calTouchPosition(n).X-t.x:t.width-(this.calTouchPosition(n).Y-t.y))-r;this.mouseDownPos=u;this.scrollbarBackRectClicked=!0;i=this;this.scrollbarContainer._scrollStartArgs.call(this,t);f=setInterval(function(){i.scrollbarContainer.scrollBackRectRecursive.call(i,t);this.scrollbarBackRectClicked||clearInterval(f)},50)},_scrollBackRectUp:function(n){this.scrollbarBackRectClicked=!1;var t=this.scrollbarContainer._doScrollIndex.call(this,n);this.scrollbarContainer.scrollBackRectRecursive.call(this,t)},_doScrollIndex:function(n){var i=this._id+"_scrollbar_",r=n.target.parentNode&&n.target.parentNode.id?n.target.parentNode.id:"",t=r.indexOf(i)>-1?parseInt(r.substr(i.length)):NaN;return ej.isNullOrUndefined(this.lastIndex)||isNaN(this.lastIndex)||!this.model.scrollObj[this.lastIndex].release&&this.model.scrollObj[this.lastIndex].clicked&&(t=this.lastIndex),this.lastIndex=ej.isNullOrUndefined(this.lastIndex)?t:this.lastIndex,ej.isNullOrUndefined(this.model.scrollObj[t])?this.model.scrollObj[this.lastIndex]:this.model.scrollObj[t]},scrollBackRectRecursive:function(n){if(this.scrollbarBackRectClicked){var t=10/100*(n.width-30);n._startX=n.startX=n.offsetLeftX;n._offsetRightX=n.offsetRightX;this.mouseDownPos<n.startX?(t=t<n.startX?t:n.startX-16,this.scrollbarContainer._calculateScrollLeftMove.call(this,t,n),n._scrollStarted=!0,this.scrollbarContainer._scrollEndArgs.call(this,n.startX,n),this.scrollbarContainer._setScrollPosition.call(this,n.startX,n.offsetRightX,n)):this.mouseDownPos>n.offsetRightX?(t=(t<n.endX?t:n.endX-16)*-1,this.scrollbarContainer._calculateScrollRightMove.call(this,t,n),n._scrollStarted=!0,this.scrollbarContainer._scrollEndArgs.call(this,n._startX,n),this.scrollbarContainer._setScrollPosition.call(this,n._startX,n.offsetRightX,n)):n._scrollStarted=!1}else n._scrollStarted=!1},_appendScrollRange:function(n){if(this.scrollbarUpdate=!0,this.pluginName=="ejRangeNavigator")this.renderNavigator();else{var i=n.index,t=this.model._axes[i];n.isZooming&&(t.zoomFactor!=n.zoomFactor||t.zoomPosition!=n.zoomPosition)&&(t.zoomFactor=n.zoomFactor,t.zoomPosition=n.zoomPosition,this.redraw(!0))}this.scrollbarUpdate=!1}}}(jQuery)});
