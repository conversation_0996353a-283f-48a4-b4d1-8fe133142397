﻿////////////////////////////////////////////////////////////////////////////////////////////
//Ο παρακάτω κώδικας σχετίζεται με την μετατροπή φωνής σε text στη λειτουργία Speech-to-Text

let recognition;

function startSpeech(textBoxId) {
    if (!('webkitSpeechRecognition' in window)) {
        alert("Speech recognition not supported in this browser.");
        return;
    }

    recognition = new webkitSpeechRecognition();
    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'el-GR';

    recognition.onresult = function (event) {
        let interimTranscript = "";
        let finalTranscript = "";

        for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            if (event.results[i].isFinal) {
                finalTranscript += transcript + " ";
                finalTranscript = finalTranscript.trim();
            } else {
                interimTranscript += transcript;
                interimTranscript = interimTranscript.trim();
            }
        }

        const textBox = document.getElementById(textBoxId);
        const existingText = textBox.value;
        textBox.value = (existingText + ' ' + (finalTranscript)).trim();
    };

    recognition.start();
}

function stopSpeech() {
    if (recognition) {
        recognition.stop();
    }
}



///////////////////////////////////////////////////////////////////////////////
//Ο παρακάτω κώδικας είναι για τα controls που σχετίζονται με το Speech-to-Text
var isListening = false;
let activeTextBoxId = null;

function toggleSpeech(textboxId) {
    var initialActiveTextBox = activeTextBoxId;

    // If we are currently listening voice for a textbox
    if (isListening) {

        stopSpeech();
        //clears all controls from speech-to-text
        try {document.getElementById('requestMicIcon').classList.remove('listening');}
        catch (e) { }
        try {document.getElementById('therapistCommentsMicIcon').classList.remove('listening');}
        catch (e) { }
        try { document.getElementById('notesMicIcon').classList.remove('listening'); }
        catch (e) { }
        isListening = false;
        activeTextBoxId = null;
    }
    else {

    }

    // if the user clicked another Textbox, start listening to the new control
    if (textboxId !== initialActiveTextBox) {
        startSpeech(textboxId);

        if (textboxId == 'requestTxtBox') {
            document.getElementById('requestMicIcon').classList.add('listening');
        } else if (textboxId == 'therapistCommentsTxtBox') {
            document.getElementById('therapistCommentsMicIcon').classList.add('listening');
        }
        else if (textboxId == 'notesTxtBox') {
            document.getElementById('notesMicIcon').classList.add('listening');
        }

        isListening = true;
        activeTextBoxId = textboxId;
        const textBox = document.getElementById(textboxId);
        if (textBox) {
            textBox.focus();
        } else {
            console.error(`Element with id "${textboxId}" not found.`);
        }
    }
}

function speechTextBoxBlur(textboxId) {
    if (isListening && activeTextBoxId === textboxId) {
        stopSpeech();
        //clears all controls from speech-to-text
        try { document.getElementById('requestMicIcon').classList.remove('listening'); }
        catch (e) { }
        try { document.getElementById('therapistCommentsMicIcon').classList.remove('listening'); }
        catch (e) { }
        try { document.getElementById('notesMicIcon').classList.remove('listening'); }
        catch (e) { }

        isListening = false;
        activeTextBoxId = null;
    }
    //toggleSpeech(textboxId);
    return true;
}