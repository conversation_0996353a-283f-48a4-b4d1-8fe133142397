/*!
*  filename: ej.mobile.tab.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./ej.mobile.core.min","./../common/ej.touch.min","./../common/ej.unobtrusive.min"],n):n()})(function(){(function(n,t){t.mobile.AjaxLoader={_renderItems:function(i,r,u){var i=n(i),e,f,o;this._control=this._rootCSS.split("e-m-")[1];i.attr("id",this.elementId+"-"+this._control+"-item"+u).addClass("e-m-"+this._control+"-item");e=!t.isNullOrUndefined(this._items[u].enableAjax)&&this._items[u].enableAjax?this._items[u].enableAjax:this.model.enableAjax;this._storedContent[u]||e||(this._items[u].href?(f=t.getCurrentPage().find(this._items[u].href),this._storedContent[u]=f[0]&&f[0].nodeName&&f[0].nodeName.toLowerCase()=="script"?t.getClearString(f[0].innerHTML):f):this._storedContent[u]=this._items[u].template?this._items[u].template:i.html());e||(o=t.buildTag("div.e-m-"+this._control+"-content e-m-"+this._control+"-static-content#"+this.elementId+"-"+this._control+"-item"+u+"-content",this._storedContent[u]).appendTo(r));e&&this.model.showAjaxPopup&&t.mobile.WaitingPopup.show();e&&this.model.prefetchAjaxContent&&this._loadAjaxContent(i,r,u)},_loadAjaxContent:function(i,r,u){var o=n(i),f=this,s=this,e=this._items[u].href,h,c;(!n.support.pushstate||t.isWindowsWebView())&&(e=this._makeUrlAbsolute(e));h={item:i,index:u,text:o.text(),url:this.model.ajaxSettings.url?this.model.ajaxSettings.url:e};this.model.ajaxBeforeLoad&&this._trigger("ajaxBeforeLoad",h);c={cache:this.model.ajaxSettings.cache,async:this.model.ajaxSettings.async,type:this.model.ajaxSettings.type,contentType:this.model.ajaxSettings.contentType,url:t.isWindowsWebView()?e:this.model.ajaxSettings.url?this.model.ajaxSettings.url:e,dataType:this.model.ajaxSettings.dataType,data:this.model.ajaxSettings.data,successHandler:function(h){var l={item:i,index:u,text:o.text(),url:f.model.ajaxSettings.url?f.model.ajaxSettings.url:e},a=t.getCurrentPage().find("#"+f.elementId+"-"+s._control+"-item"+u+"-content"),c;f.model.enableCache&&a.length!=0||(c=t.buildTag("div.e-m-"+s._control+"-content e-m-"+s._control+"-ajax-content#"+f.elementId+"-"+s._control+"-item"+u+"-content",/<\/?body[^>]*>/gmi.test(h)?h.split(/<\/?body[^>]*>/gmi)[1]:h||"").appendTo(r),f._content=this._control=="acc"?c.children():c,(!t.isAppNullOrUndefined()&&App.angularAppName||t.angular.defaultAppName)&&t.angular.compile(n(c)));t.widget.init(f._content);f.model.ajaxSuccess&&f._trigger("ajaxSuccess",l);f._ajaxSuccessHandler&&f._ajaxSuccessHandler(l)},errorHandler:function(n,t,r){var u={xhr:n,textStatus:t,errorThrown:r,item:i,index:o.index(),text:o.text(),url:f.model.ajaxSettings.url?f.model.ajaxSettings.url:e};f.model.ajaxError&&f._trigger("ajaxError",u)},completeHandler:function(){f.model.showAjaxPopup&&t.mobile.WaitingPopup.hide();var n={content:f._content,item:i,index:o.index(),text:o.text(),url:f.model.ajaxSettings.url?f.model.ajaxSettings.url:e};f.model.ajaxComplete&&f._trigger("ajaxComplete",n)}};t.sendAjaxRequest(c)},_showHideItems:function(i,r,u){var f=t.getCurrentPage(),e=t.isNullOrUndefined(this._items[u].enableAjax)?this.model.enableAjax:this._items[u].enableAjax,o=f.find("#"+i.id+"-content").length;e&&o==0&&!this.model.prefetchAjaxContent&&this._loadAjaxContent(i,r,u);n(this["_"+this._control+"Items"]).removeClass("e-m-state-active");n(i).addClass("e-m-state-active");f.find(".e-m-"+this._control+"-content").addClass("e-m-"+this._control+"-state-hide");f.find("#"+i.id+"-content").removeClass("e-m-"+this._control+"-state-hide");this.model.prefetchAjaxContent||this.model.enableCache||f.find(".e-m-"+this._control+"-ajax-content.e-m-"+this._control+"-state-hide").remove();this.model.select&&this._trigger("select",{index:u,item:n(i),text:n(i).text(),isInteraction:this._isInteraction?!0:!1})},ajaxReload:function(){var n=this.element.find(".e-m-"+this._control+"-item.e-m-state-active"),t=this.element.find(".e-m-"+this._control+"-content:not(.e-m-"+this._control+"-state-hide)"),i=t.parent(),r=n[0].id.split("item")[1];t.remove();this._loadAjaxContent(n,i,r)},_makeUrlAbsolute:function(i){var r=n("<a href ='"+i+"'><\/a>")[0],u;n("body").append(r);u=r.pathname.indexOf("/")!=0?"/"+r.pathname:r.pathname;var f=t.browserInfo(),e=f.name=="msie"&&f.version=="9.0",o=r.protocol+"//"+(e&&r.port=="80"?r.host.replace(":80",""):r.host)+u+r.search+r.hash;return n(r).remove(),o}}})(jQuery,Syncfusion),function($,ej,undefined){ej.widget("ejmTab","ej.mobile.Tab",{_setFirst:!0,_rootCSS:"e-m-tab",validTags:["ul"],_tags:[{tag:"items",attr:["icon","text","href","enableAjax","imageClass","badge.value","badge.maxValue"],content:"template"}],defaults:{renderMode:"auto",enableAjax:!1,enableCache:!1,showAjaxPopup:!1,selectedItemIndex:0,enablePersistence:!1,badge:{maxValue:"100"},itemStyle:"bothblock",allowSwiping:!0,position:"bottom",prefetchAjaxContent:!1,cssClass:"",items:[],contentId:null,ajaxSettings:{type:"GET",cache:!1,async:!0,dataType:"html",contentType:"html",url:"",data:{}},enableRippleEffect:ej.isAndroid()?!0:!1,load:null,loadComplete:null,touchStart:null,touchEnd:null,ajaxBeforeLoad:null,ajaxSuccess:null,ajaxError:null,ajaxComplete:null,select:null},observables:["selectedItemIndex"],selectedItemIndex:ej.util.valueFunction("selectedItemIndex"),dataTypes:{renderMode:"enum",enablePersistence:"boolean",items:"data",enableRippleEffect:"boolean"},_init:function(){this._orgEle=this.element.clone();this._storedContent=[];this._items=[];this._renderControl();this._wireEvents(!1)},_renderControl:function(){var i,itemLength,liItem;if(ej.setRenderMode(this),this.element.addClass("e-m-user-select e-m-"+this.model.renderMode+" e-m-tab-"+this.model.position+" e-m-tab-content-"+this.model.itemStyle+" "+this.model.cssClass),this.elementId=this.element[0].id,this._tabContainer=$("#"+this.model.contentId).addClass("e-m-"+this.model.renderMode+" e-m-tab-"+this.model.position+" e-m-tab-content-wrapper"),this.model.items=typeof this.model.items=="string"?eval(this.model.items):this.model.items,this.model.itemStyle=="bothblock"&&this._tabContainer.addClass("e-m-tab-content-bothblock"),this.model.load&&this._trigger("load"),this.model.items.length){for(i=0,itemLength=this.model.items.length;i<itemLength;i++)liItem=ej.buildTag("li",this.model.items[i].template),this.element.append(liItem),this._renderTabItems(liItem,i);this._tabItems=this.element.find("li")}else for(this._tabItems=this.element.find("li"),i=0,itemLength=this._tabItems.length;i<itemLength;i++)this._items.push(this._insertItemValue($(this._tabItems[i]))),this._renderTabItems(this._tabItems[i],i);!this.model.prefetchAjaxContent&&this.selectedItemIndex()>=0&&this._showHideItems(this._tabItems[this.selectedItemIndex()],this._tabContainer,this.selectedItemIndex());this.model.loadComplete&&this._trigger("loadComplete");this._setEnableRippleEffect()},_setEnableRippleEffect:function(){this.element.find("li")[this.model.enableRippleEffect?"addClass":"removeClass"]("e-ripple")},_insertItemValue:function(n){for(var t,r={},i=0,u=this._tags[0].attr.length+1;i<u;i++)t=this._tags[0].attr[i]==undefined?"template":this._tags[0].attr[i],r[t]=t=="enableAjax"?ej.getBooleanVal(n,"data-ej-"+t.replace(".","-").toLowerCase(),undefined):t=="template"?n.html():ej.getAttrVal(n,"data-ej-"+t.replace(".","-").toLowerCase(),undefined);return r},_renderTabItems:function(n,t){var n,i,r;this._items=this.model.items.length>0?this.model.items:this._items;n=$(n);n.attr("id",this.elementId+"-tab-item"+t);ej.isNullOrUndefined(this._items[t].icon)||n.addClass("e-m-icon-"+this._items[t].icon.toLowerCase());(this._items[t]["badge.value"]||this.model.badge.value)>0&&(i=this._items[t]["badge.value"]?this._items[t]["badge.value"]:null,r=this._items[t]["badge.maxValue"]?this._items[t]["badge.maxValue"]:this.model.badge.maxValue,n.addClass("e-m-tab-badge").attr("badgeValue",this._setBadgeValue(parseInt(i),parseInt(r))));this._renderItems(n,this._tabContainer,t);ej.isNullOrUndefined(this._items[t].text)||n.text(this._items[t].text)},_ajaxSuccessHandler:function(){this.model.prefetchAjaxContent&&this._showHideItems(this._tabItems[this.selectedItemIndex()],this._tabContainer,this.selectedItemIndex())},_setBadgeValue:function(n,t){return(n<=t?n:t+"+").toString()},_createDelegates:function(){this._touchStartDelegate=$.proxy(this._onTouchStartHandler,this);this._touchEndDelegate=$.proxy(this._onTouchEndHandler,this);this._touchMoveDelegate=$.proxy(this._onTouchMoveHandler,this);this._contentTouchStartDelegate=$.proxy(this._onContentTouchStartHandler,this);this._contentTouchMoveDelegate=$.proxy(this._onContentTouchMoveHandler,this);this._contentTouchEndDelegate=$.proxy(this._onContentTouchEndHandler,this)},_wireEvents:function(n){this._createDelegates();ej.listenEvents([this._tabItems],[ej.startEvent()],[this._touchStartDelegate],n);this.model.allowSwiping&&ej.listenEvents([this._tabContainer],[ej.startEvent()],[this._contentTouchStartDelegate],n)},_eventArgs:function(n){return{item:$(n),text:$(n).text(),index:$(n).index()}},_onTouchStartHandler:function(n){var t=n.touches?n.touches[0]?n.touches[0]:n.changedTouches?n.changedTouches[0]:n:n;this._tabClickX=t.clientX;this._currentTab=n.target;this._targetItemId=n.target.id;this._tabItems=this.element.find(".e-m-tab-item");this._index=parseInt(this._targetItemId.split(this.elementId+"-tab-item")[1]);this.model.touchStart&&this._trigger("touchStart",this._eventArgs(this._currentTab));this._tabMouseMove=!1;ej.listenEvents([this._tabItems,this._tabItems],[ej.moveEvent(),ej.endEvent()],[this._touchMoveDelegate,this._touchEndDelegate],!1)},_onTouchMoveHandler:function(n){var t=n.touches?n.touches[0]?n.touches[0]:n.changedTouches?n.changedTouches[0]:n:n;this._tabClickX!=t.clientX&&(this._tabMouseMove=!0)},_onTouchEndHandler:function(){(this._isInteraction=!0,ej.listenEvents([this._tabItems,this._tabItems],[ej.moveEvent(),ej.endEvent()],[this._touchMoveDelegate,this._touchEndDelegate],!0),this._tabMouseMove)||(this.selectedItemIndex(this._index),this._showHideItems(this._currentTab,this._tabContainer,this._index),this.model.touchEnd&&this._trigger("touchEnd",this._eventArgs(this._currentTab)),this._isInteraction=!1)},_onContentTouchStartHandler:function(n){var t=n.touches?n.touches[0]:n;this._pointX=t.pageX;this._pointY=t.pageY;ej.listenEvents([this._tabContainer],[ej.moveEvent()],[this._contentTouchMoveDelegate],!1)},_onContentTouchMoveHandler:function(){ej.listenEvents([this._tabContainer],[ej.moveEvent()],[this._contentTouchMoveDelegate],!0);ej.listenEvents([this._tabContainer],[ej.endEvent()],[this._contentTouchEndDelegate],!1)},_onContentTouchEndHandler:function(n){var i=n.touches?n.touches[0]?n.touches[0]:n.changedTouches?n.changedTouches[0]:n:n,t,r;ej.listenEvents([this._tabContainer,this._tabContainer],[ej.moveEvent(),ej.endEvent()],[this._contentTouchMoveDelegate,this._contentTouchEndDelegate],!0);this._endPointX=i.pageX;this._endPointY=i.pageY;relativeX=Math.abs(this._endPointX-this._pointX);relativeY=Math.abs(this._endPointY-this._pointY);relativeXY=Math.abs(relativeX-relativeY);relativeX>30&&relativeXY>30&&relativeX>relativeY&&(direction=this._endPointX-this._pointX>30?"swiperight":"swipeleft",t=direction=="swiperight"?this.selectedItemIndex()-1:this.selectedItemIndex()+1,t>=0&&t<this._items.length&&(r=this.element.find("#"+this.elementId+"-tab-item"+t),this.selectedItemIndex(t),this._showHideItems(r[0],this._tabContainer,t)))},_setModel:function(n){var r=!1,t,i;for(t in n)i="_set"+t.charAt(0).toUpperCase()+t.slice(1),this[i]?this[i](n[t]):r=!0;r&&this._refresh()},_refresh:function(){this._destroy();this.element.addClass("e-m-tab");this._renderControl();this._wireEvents()},_clearElement:function(){this.element.removeAttr("class").find("*").removeAttr("class id");$.each(this._storedContent,function(n,t){ej.getCurrentPage().append(t)});this._tabContainer.removeAttr("class").find("*").remove()},_destroy:function(){this._wireEvents(!0);this._clearElement()},_setContentId:function(n){$.each(this._storedContent,function(n,t){ej.getCurrentPage().append(t)});this.model.allowSwiping&&ej.listenEvents([this._tabContainer],[ej.startEvent()],[this._contentTouchStartDelegate],!0);this._tabContainer.removeAttr("class").find("*").remove();this._tabContainer=$("#"+n).addClass("e-m-"+this.model.renderMode+" e-m-tab-"+this.model.position+" e-m-tab-content-wrapper");for(var t=0,i=this._tabItems.length;t<i;t++)this._renderItems(this._tabItems[t],this._tabContainer,t);!this.model.prefetchAjaxContent&&this.selectedItemIndex()>=0&&this._showHideItems(this._tabItems[this.selectedItemIndex()],this._tabContainer,this.selectedItemIndex());this.model.allowSwiping&&ej.listenEvents([this._tabContainer],[ej.startEvent()],[this._contentTouchStartDelegate],!1)},_addRemovClass:function(n,t){this.element.removeClass(n).addClass("e-m-"+t);this._tabContainer.removeClass(n).addClass("e-m-"+t)},_setRenderMode:function(n){this._addRemovClass("e-m-ios7 e-m-android e-m-windows e-m-flat",n)},_setPosition:function(n){this._addRemovClass("e-m-tab-top e-m-tab-bottom",n)},_setItemStyle:function(n){this.element.removeClass("e-m-tab-content-bothblock e-m-tab-content-bothinline e-m-tab-content-image e-m-tab-content-text").addClass("e-m-"+n)},_setSelectedItemIndex:function(){this.selectedItemIndex()>=0&&this.selectedItemIndex()!=""&&this._showHideItems(this._tabItems[parseInt(this.selectedItemIndex())],this._tabContainer,parseInt(this.selectedItemIndex()))},_setCssClass:function(n){this.element.addClass(n)},showBadge:function(n){var t=this.element.find("#"+this.elementId+"-tab-item"+n);n>=0&&t.addClass("e-m-tab-badge")},hideBadge:function(n){var t=this.element.find("#"+this.elementId+"-tab-item"+n);n>=0&&t.removeClass("e-m-tab-badge")},setBadgeValue:function(n,t){var r=this.element.find("#"+this.elementId+"-tab-item"+n),i;n>=0&&t!=undefined&&(i=this._items[n]["badge.maxValue"]?this._items[n]["badge.maxValue"]:this.model.badge.maxValue,r.addClass("e-m-tab-badge").attr("badgeValue",this._setBadgeValue(parseInt(t),parseInt(i))),this._items[n]["badge.value"]=t)},setActiveItem:function(n){n>=0&&(this.selectedItemIndex(n),this._showHideItems(this._tabItems[n],this._tabContainer,n))},enableItem:function(n){if(n>=0){var t=this.element.find("#"+this.elementId+"-tab-item"+n);t.removeClass("e-m-state-disabled");this.enableContent(n)}},disableItem:function(n){if(n>=0){var t=this.element.find("#"+this.elementId+"-tab-item"+n);t.addClass("e-m-state-disabled");this.disableContent(n)}},enableContent:function(n){var t=$(this._tabContainer.find("#"+this.elementId+"-tab-item"+n+"-content"));n>=0?t.removeClass("e-m-state-disabled"):this._tabContainer.removeClass("e-m-state-disabled")},disableContent:function(n){var t=$(this._tabContainer.find("#"+this.elementId+"-tab-item"+n+"-content"));n>=0?t.addClass("e-m-state-disabled"):this._tabContainer.addClass("e-m-state-disabled")},addItem:function(n,t){var i,r,u;for(t>=0?(this._items.splice(t,0,this._insertItemValue($(n))),this.element.find("#"+this.elementId+"-tab-item"+t).before($(n).addClass("e-m-tab-item"))):(this._items.push(this._insertItemValue($(n).addClass("e-m-tab-item"))),this.element.find("#"+this.elementId+"-tab-item"+this._items.length).after($(n))),$.each(this._storedContent,function(n,t){ej.getCurrentPage().append(t)}),this._storedContent=[],this._tabContainer.find("*").remove(),i=0,r=this.element.find(".e-m-tab-item").length;i<r;i++)u=$(this.element.find(".e-m-tab-item")[i]),this._renderTabItems(u,i);!this.model.prefetchAjaxContent&&this.selectedItemIndex()>=0&&this._showHideItems(this._tabItems[this.selectedItemIndex()],this._tabContainer,this.selectedItemIndex());this._tabItems=this.element.find("li");this._wireEvents(!1)},removeItem:function(n){if(n>=0){this.element.find("#"+this.elementId+"-tab-item"+n).remove();this._tabContainer.find("#"+this.elementId+"-tab-item"+n+"-content").remove();this._items.splice(n,1);for(var t=0,i=this.element.find(".e-m-tab-item").length;t<i;t++)$(this.element.find(".e-m-tab-item")[t]).attr("id",this.elementId+"-tab-item"+t),$(this._tabContainer.find(".e-m-tab-content")[t]).attr("id",this.elementId+"-tab-item"+t+"-content")}},getItemsCount:function(){return this.element.find(".e-m-tab-item").length},getActiveItem:function(){return this.element.find("li.e-m-state-active")},getActiveItemText:function(){return this.element.find("li.e-m-state-active").text()},setContent:function(n,t){t>=0&&(this._items[t].href=n);this._refresh()}});ej.mobile.Tab.ItemStyle={BothBlock:"bothblock",Image:"image",Text:"text",BothInline:"bothinline"};ej.mobile.Tab.Position={Top:"top",Bottom:"bottom"};$.extend(!0,ej.mobile.Tab.prototype,ej.mobile.AjaxLoader)}(jQuery,Syncfusion)});
