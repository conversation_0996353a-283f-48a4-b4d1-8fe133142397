/*!
*  filename: ej.mobile.groupbutton.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function($,ej,undefined){ej.widget("ejmGroupButton","ej.mobile.GroupButton",{_setFirst:!0,_rootCSS:"e-m-grpbtn",_tags:[{tag:"items",attr:["text","type","name","imageClass","imageUrl"]}],defaults:{selectedItemIndex:0,renderMode:"auto",enablePersistence:!1,cssClass:"",items:[],touchStart:null,touchEnd:null,select:null},dataTypes:{renderMode:"enum",enablePersistence:"boolean"},observables:["selectedItemIndex"],selectedItemIndex:ej.util.valueFunction("selectedItemIndex"),_init:function(){this._orgEle=this.element.clone();this._renderControl();this._createDelegates();this._wireEvents()},_renderControl:function(){var element,isAngular,i,btnelement,label,proxy;for(ej.setRenderMode(this),element=this.element,element.addClass("e-m-grpbtn e-m-"+this.model.renderMode+" "+this.model.cssClass),typeof this.model.items=="string"&&(this.model.items=eval(this.model.items)),isAngular=this.model.items.length>0?!0:!1,i=0,btnelement=element.children();i<(isAngular?this.model.items.length:btnelement.length);i++)isAngular?label=!ej.isNullOrUndefined(this.model.items[i].type)||!ej.isNullOrUndefined(this.model.items[i].type)?ej.buildTag("label","<input "+(ej.isNullOrUndefined(this.model.items[i].type)?"":'type="'+this.model.items[i].type+'"')+(ej.isNullOrUndefined(this.model.items[i].name)?"":'name="'+this.model.items[i].name+'"')+" />"+this.model.items[i].text):ej.buildTag("button",this.model.items[i].text).appendTo(element):this.model.items.push(this._insertItem(btnelement[i]));proxy=this;$.each(element.children(),function(n){var t=$(this),i;imageClass=proxy.model.items[n].imageClass;imagePath=proxy.model.items[n].imageUrl;(imageClass||imagePath)&&(i=ej.buildTag("div.grpimage "+imageClass).appendTo(t),imagePath&&(i.css({"background-image":"url('"+imagePath+"')"}),t.text().trim()==""&&t.addClass("e-m-imageonly")));t.addClass("e-m-btn")});this._selectItem(this.selectedItemIndex())},_insertItem:function(n){for(var i,r={},t=0,u=this._tags[0].attr.length;t<u;t++)i=this._tags[0].attr[t],r[i]=ej.getAttrVal(n,"data-ej-"+i.toLowerCase(),undefined);return r},_createDelegates:function(){this._touchStartHandler=$.proxy(this._touchStartHandler,this);this._touchEndHandler=$.proxy(this._touchEndHandler,this);this._touchMoveHandler=$.proxy(this._onTouchMove,this)},_wireEvents:function(n){var t=this.element.find(".e-m-btn");ej.listenEvents([t],[ej.startEvent()],[this._touchStartHandler],n)},_touchStartHandler:function(n){$(this.element.children()[this.model.selectedItemIndex]).children().is(":checked")||$(this.element.children()[this.model.selectedItemIndex]).removeClass("e-m-active");this._prevItem=this.model.selectedItemIndex;var t=n.touches?n.touches[0]?n.touches[0]:n.changedTouches?n.changedTouches[0]:n:n;this._clickX=t.clientX;this._isMouseMove=!1;this._target=n.target;this.currentText=$(n.target).text();this.data={text:this.currentText.trim()};ej.listenEvents([this._target,this._target],[ej.endEvent(),ej.moveEvent()],[this._touchEndHandler,this._touchMoveHandler],!1);this.model.touchStart&&this._trigger("touchStart",this.data)},_onTouchMove:function(n){var t=n.touches?n.touches[0]?n.touches[0]:n.changedTouches?n.changedTouches[0]:n:n;this._clickX!=t.clientX&&(this._isMouseMove=!0)},_touchEndHandler:function(n){var t,i;ej.listenEvents([$(n.currentTarget),$(n.currentTarget)],[ej.moveEvent(),ej.endEvent()],[this._touchMoveHandler,this._touchEndHandler],!0);n.target!==this._target||this._isMouseMove||(t=this.element,$(n.target).addClass("e-m-active"),i=$(n.currentTarget),this.model.selectedItemIndex=i.index(),i.find("input").length==0||i.find("input[type='groupbutton']").length==0?(t.children().find("input").attr("type")!="checkbox"&&t.find(".e-m-active").removeClass("e-m-active").find("input[type='radio']").attr("checked",!1),i.addClass("e-m-active").find("input[type='radio']").prop("checked",!0)):i.find("input").is(":checked")?i.removeClass("e-m-active"):i.addClass("e-m-active"),t.children().find("input").attr("type")=="checkbox"&&($(t.children()[this._prevItem]).children().is(":checked")&&$(t.children()[this._prevItem]).addClass("e-m-active"),$(t.children()[this.model.selectedItemIndex]).children().is(":checked")&&$(t.children()[this.model.selectedItemIndex]).removeClass("e-m-active")),this.selectedItemIndex($(n.currentTarget).index()),this.model.touchEnd&&this._trigger("touchEnd",this.data));this.data={text:this.currentText.trim()};this.model.select&&this._trigger("select",this.data)},_selectItem:function(n){var t=this.element,i;n>=0&&(this.selectedItemIndex(n),i=$(t.find("input")[this.selectedItemIndex()]),t.find(".e-m-active").children().attr("type")=="checkbox"?$(t.children().find("input")[n]).is(":checked")?($(t.children()[n]).removeClass("e-m-active"),i.prop("checked",!1)):($(t.children()[n]).addClass("e-m-active"),i.prop("checked",!0)):(t.find(".e-m-active").removeClass("e-m-active"),$(t.find(".e-m-btn")[this.selectedItemIndex()]).addClass("e-m-active"),i.prop("checked",!0)),this.currentText=$(t.find(".e-m-btn")[this.selectedItemIndex()]).text(),this.data={text:this.currentText.trim()},this.model.select&&this._trigger("select",this.data))},_setModel:function(n){var r=!1,t,i,u;for(t in n)i="_set"+t.charAt(0).toUpperCase()+t.slice(1),u=typeof n[t]=="function"?this[t]():n[t],this[i]?this[i](u):r=!0;r&&this._refresh()},_setRenderMode:function(){this.element.removeClass("e-m-ios7 e-m-android e-m-windows e-m-flat").addClass("e-m-"+this.model.renderMode+"")},_setSelectedItemIndex:function(n){this._selectItem(n)},_refresh:function(){this._destroy();this.element.addClass("ejm-groupbutton");this._renderControl();this._wireEvents(!1)},_clearElement:function(){this.element.removeAttr("class");this.element.html(this._orgEle.html())},_destroy:function(){this._wireEvents(!0);this._clearElement()}})})(jQuery,Syncfusion)});
