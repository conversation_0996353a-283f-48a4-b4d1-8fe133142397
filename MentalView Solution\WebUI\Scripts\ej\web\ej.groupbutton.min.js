/*!
*  filename: ej.groupbutton.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min"],n):n()})(function(){(function(n,t,i){t.widget("ejGroupButton","ej.GroupButton",{element:null,model:null,validTags:["div","span"],_addToPersist:["selectedItemIndex"],_setFirst:!1,_rootCSS:"e-groupbutton",defaults:{height:"",width:"",cssClass:"",enabled:!0,enableRTL:!1,showRoundedCorner:!1,size:"normal",groupButtonMode:"radiobutton",orientation:t.Orientation.Horizontal,selectedItemIndex:[],htmlAttributes:{},dataSource:null,query:null,fields:{text:"text",prefixIcon:"prefixIcon",suffixIcon:"suffixIcon",contentType:"contentType",imagePosition:"imagePosition",selected:"selected",url:"url",htmlAttribute:"htmlAttribute",linkAttribute:"linkAttribute"},create:null,beforeSelect:null,select:null,keyPress:null,destroy:null},dataTypes:{cssClass:"string",enabled:"boolean",dataSource:"data",query:"data",fields:"data",showRoundedCorner:"boolean",enableRTL:"boolean",size:"enum",groupButtonMode:"enum",orientation:"enum",selectedItemIndex:"array"},_setModel:function(t){var f,i,u,r,e;for(f in t){i=t[f];switch(f){case"size":this._setSize(i);break;case"height":this._setDimension("height",i);break;case"width":this._setDimension("width",i);this._setWidth();break;case"enabled":this._setControlStatus(i);break;case"showRoundedCorner":this._setRoundedCorner(i);break;case"cssClass":this._setSkin(i);break;case"enableRTL":this._setRTL(i);break;case"htmlAttributes":this._setAttr(i);break;case"dataSource":this.element.html("");this.model.dataSource=i;this._initialize();break;case"groupButtonMode":this.model.groupButtonMode!=i&&this.itemsContainer.children(".e-grp-btn-item.e-active").removeClass("e-active").attr({"aria-selected":!1});break;case"orientation":this._setOrientation(i);this._setDimension("width",this.model.width);this._setDimension("height",this.model.height);this.model.width&&this.model.width!=""&&this._setWidth();break;case"selectedItemIndex":for(u=JSON.parse(JSON.stringify(this.model.selectedItemIndex)),r=0,e=u.length;r<e;r++)i.indexOf(u[r])==-1&&this._removeSelection(n(this.items[u[r]]));this.model.selectedItemIndex=i;this._handleSeletedItem()}}},_setWidth:function(){var r,u,t;for(n(this.element.find("div.e-btn-content")).children(".e-btntxt").removeClass("e-grp-responsive"),this.element.find("div.e-btn-content").children(".e-btntxt").removeAttr("style title"),this.model.orientation!="vertical"?this.element.find("div.e-btn-content").css({width:this.element.width()/this.items.length-15-(2*parseInt(this.element.css("border-width"))==""||isNaN(parseInt(this.element.css("border-width")))?0:this.element.css("border-width")+this.items.length)}):this.model.orientation=="vertical"&&this.element.find("div.e-btn-content").css({width:this.element.width()-20}),this.element.find("div.e-btn-content").width()<25||this.element.find("div.e-btn-content").height()<25?(this._minWidth="25px",this._minHeight="25px",this.model.orientation=="vertical"&&this.element.find("div.e-btn-content").height()<25&&this.element.css("min-height",parseInt(this._minHeight)*this.items.length),this.model.orientation=="horizontal"&&this.element.find("div.e-btn-content").height()<25&&this.element.css("min-height",this._minHeight),this.model.orientation!="vertical"&&this.element.find("div.e-btn-content").width()<25?(this.element.css("min-width",this.items.length*parseInt(this._minWidth)),this._minHLI=parseInt(this._minWidth)-(2*parseInt(this.element.css("border-width"))+this.items.length),this.element.find("div.e-btn-content").css({"min-width":this._minHLI,padding:"0px"})):this.model.orientation=="vertical"&&this.element.find("div.e-btn-content").width()<25&&(this.element.css("min-width",parseInt(this._minWidth)),this._minVLI=parseInt(this._minWidth)-2*parseInt(this.element.css("border-width")),this.element.find("div.e-btn-content").css({"min-width":this._minVLI,padding:"0px"}))):(this._minWidth=i,this._minHeight=i),r=0,u=this.items.length;r<u;r++)t=n(this.items[r]).children("div.e-btn-content"),t.children(".e-btntxt").width()>t.width()?(this._minWidth||t.css("padding",""),n(t.children(".e-btntxt").addClass("e-grp-responsive")),t.attr("title",t.text()),n(t.children(".e-btntxt")).css("line-height",t.children(".e-btntxt").height()+"px")):(t.children().removeAttr("style"),t.removeAttr("title")),this.element.find("div.e-btn-content").width()<25&&n(this.items[r]).find(".e-btn-content").children().length>1&&n(this.items[r]).find(".e-btntxt").hasClass("e-grp-responsive")?n(t).find(".e-btntxt").css("display","none"):n(t).find(".e-btntxt").css("display","block")},resize:function(){this._setWidth();this._setDimension("height",this.model.height)},_destroy:function(){this.element.html("");this._cloneElement.removeClass("e-groupbutton e-js e-widget e-box");this.element.replaceWith(this._cloneElement)},_init:function(){this._cloneElement=n(this.element).clone();this._isRender=!1;this._initialize();this._isRender=!0;this._setWidth();this.resize()},_initialize:function(){this.element.addClass("e-widget e-box e-widget").attr({role:"tree",tabindex:"0","aria-disabled":!1});this.model.dataSource!=null&&this._checkDataBinding();this.element.hasClass("onloading")||this._render()},_render:function(){this._renderItems();this._setSize(this.model.size);this._setOrientation(this.model.orientation);this._setDimension("width",this.model.width);this._setDimension("height",this.model.height);this._setRoundedCorner(this.model.showRoundedCorner);this._setSkin(this.model.cssClass);this._setAttr(this.model.htmlAttributes);this.model.enableRTL&&this._setRTL(this.model.enableRTL);this._handleSeletedItem();this._setControlStatus(this.model.enabled)},_checkDataBinding:function(){var n=this.model.dataSource;n!=null&&(this.element.addClass("onloading"),t.DataManager&&n instanceof t.DataManager?this._initDataSource(n):this._createItems(n))},_initDataSource:function(n){var t=this,i;i=n.executeQuery(this._getQuery());i.done(function(n){t._createItems(n.result);t._render()}).fail(function(){t.element.removeClass("onloading")})},_getQuery:function(){var r;if(t.isNullOrUndefined(this.model.query)){var u=[],i=t.Query(),n=this.model.fields;for(r in n)r!=="tableName"&&n[r]&&u.push(n[r]);u.length>0&&i.select(u);this.model.dataSource.dataSource.url.match(n.tableName+"$")||t.isNullOrUndefined(n.tableName)||i.from(n.tableName)}else i=this.model.query;return i},_createItems:function(n){this.list=n;var i;for(this.mapField=this._getMapper(),this.ultag=t.buildTag("ul"),this.element.append(this.ultag),i=0;i<n.length;i++)this._generateTagItems(n[i],this.mapField,i);this.element.removeClass("onloading")},_getMapper:function(){var n=this.model.fields,t={_text:null};return t._text=n&&n.text?n.text:"text",t._prefixIcon=n&&n.prefixIcon?n.prefixIcon:"prefixIcon",t._suffixIcon=n&&n.suffixIcon?n.suffixIcon:"suffixIcon",t._contentType=n&&n.contentType?n.contentType:"contentType",t._imagePosition=n&&n.imagePosition?n.imagePosition:"imagePosition",t._selected=n&&n.selected?n.selected:"selected",t._url=n&&n.url?n.url:"url",t._htmlAttribute=n&&n.htmlAttribute?n.htmlAttribute:"htmlAttribute",t._linkAttribute=n&&n.linkAttribute?n.linkAttribute:"linkAttribute",t},_generateTagItems:function(i,r,u){var o=i[r._text]?t.buildTag("span.e-btntxt",i[r._text]):null,f,s,e=t.buildTag("li"),l,h,c;i[r._htmlAttribute]&&this._setAttributes(i[r._htmlAttribute],e);l=i[r._imagePosition]?i[r._imagePosition]:"imageleft";h=i[r._contentType]?i[r._contentType]:"textonly";h.indexOf("image")>-1&&(i[r._prefixIcon]&&(f=t.buildTag("span"),f.addClass(i[r._prefixIcon])),i[r._suffixIcon]&&(s=t.buildTag("span"),s.addClass(i[r._suffixIcon])));switch(h){case t.ContentType.TextAndImage:switch(l){case t.ImagePosition.ImageRight:e.append(o,f);break;case t.ImagePosition.ImageLeft:e.append(f,o);break;case t.ImagePosition.ImageBottom:f.attr("style","display:inherit");e.append(o,f);break;case t.ImagePosition.ImageTop:f.attr("style","display:inherit");e.append(f,o)}break;case t.ContentType.ImageTextImage:e.append(f,o,s);break;case t.ContentType.ImageBoth:e.append(f,s);break;case t.ContentType.ImageOnly:e.append(f);break;case t.ContentType.TextOnly:e.append(o)}i[r._url]&&(c=t.buildTag("a.e-grp-btn-link").attr("href",i[r._url]),i[r._linkAttribute]&&this._setAttributes(i[r._linkAttribute],c),e.wrapInner(c));this.ultag.append(e);i[r._selected]&&n.inArray(u,this.model.selectedItemIndex)<=-1&&this.model.selectedItemIndex.push(u)},_setAttributes:function(n,t){for(var i in n)i=="class"?t.addClass(n[i]):t.attr(i,n[i])},_renderItems:function(){var r;this.itemsContainer=this.element.children("ol, ul, div").addClass("e-ul");this.itemsContainer.children("ol, ul").remove();this.items=this.itemsContainer.children("li, div").addClass("e-grp-btn-item").attr({"aria-selected":!1,"aria-disabled":!1,role:"treeitem",tabindex:"0"});for(var u=0,t=[],e=this.items.length,i,f;u<e;u++)t=n(this.items[u]),i=document.createElement("div"),i.className="e-btn-content",i.setAttribute("tabindex","-1"),t.children().length==0?(f=document.createElement("span"),n(f).text(t.text()).addClass("e-btntxt"),t.text(""),i.appendChild(f)):(r=t.children("a").length>0?t.children("a").children():t.children(),r.appendTo(i)),r=t.children("a").length>0?t.children("a"):t,r.append(i);this.items.filter(":last").addClass("last")},_handleSeletedItem:function(){var i=this.model.selectedItemIndex,r,u;if(this.model.groupButtonMode==t.GroupButtonMode.RadioButton)this._setSeletedItem(n(this.items[i[i.length-1]]));else for(r=0,u=i.length;r<u;r++)this._setSeletedItem(n(this.items[i[r]]))},_setSeletedItem:function(n,i){var r;this.isSelected(n)||(r=this._getDetails(n),r.element=n,r.event=i,this._triggerEvent("beforeSelect",r))||(this.model.groupButtonMode==t.GroupButtonMode.RadioButton&&this._removeSelection(this.items.filter(".e-active")),n.addClass("e-active").attr("aria-selected",!0).siblings(".e-grp-btn-item").removeClass("e-hover"),this._addSelectedIndex(this.items.index(n)),r=this._getDetails(n),r.element=n,r.event=i,this._triggerEvent("select",r))},_removeSelection:function(n){n.removeClass("e-active").attr("aria-selected",!1);this._removeSelectedIndex(this.items.index(n))},_getDetails:function(n){if(n[0]!=null&&n.hasClass("e-grp-btn-item")){var t,i,r,u;return t=n.attr("id"),r=this.isSelected(n),i=this.isDisabled(n),u=this.getIndex(n),{id:t,selected:r,disabled:i,index:u}}return{id:"",selected:"",disabled:"",index:""}},_setAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.element.addClass(n):i.element.attr(t,n);t=="disabled"&&n=="disabled"&&i.disable()})},_setSize:function(n){this.element.removeClass("e-grp-btn-mini e-grp-btn-medium e-grp-btn-small e-grp-btn-large e-grp-btn-normal");this.element.addClass("e-grp-btn-"+n)},_setDimension:function(t,i){var o=26,e,h=0,c=t=="width"?"outerWidth":"outerHeight",f,l,r,u,s,a;if(i=="auto"){for(e=this.element[c]()-this.element[t](),r=0,u=this.items.length;r<u;r++)h+=n(this.items[r])[c]();this.element.css(t,h+e)}else if(e=2*parseInt(this.element.find(".e-grp-btn-item").css("border-width")),this.element.css(t,i),t=="height"&&i!==""){for(r=0,u=this.items.length;r<u;r++)n(this.items[r]).find(".e-btn-content").css(t,parseInt(i)-e+"px"),n(this.items[r]).find(".e-btn-content").css("padding","0px"),n(this.items[r]).find(".e-btn-content .e-btntxt").css("padding","0px"),n(this.items[r]).find(".e-btn-content .e-btntxt").css("line-height",parseInt(i)-e+"px");for(r=0,u=this.items.length;r<u;r++)f=n(this.items[r]).children("div.e-btn-content"),f.children(".e-btntxt").height()>f.height()&&(n(f.children(".e-btntxt").addClass("e-grp-responsive")),f.attr("title",f.text()))}if(t!="height"||this._vertical){if(t=="height")if(this.element.css("min-height","").find("div.e-btn-content").removeClass("e-groupBtn-padding"),this.element.find(".e-grp-btn-item").css("height","").find("div.e-btn-content").css("margin-top","").find(".e-btntxt"),l=this.element.height()/this.items.length,l<o)for(this.element.css("min-height",this.items.length*o).find("div.e-btn-content").addClass("e-groupBtn-padding"),this.element.find(".e-grp-btn-item").css({height:Math.ceil(this.element.height()/this.items.length)+"px"}),r=0,u=this.items.length;r<u;r++)n(this.items[r]).find(".e-btn-content .e-btntxt").css("line-height",Math.ceil(this.element.height()/this.items.length)+"px");else s=this.element.height()/this.items.length,a=(s-this.element.find("div.e-btn-content").outerHeight())/2,this.element.find(".e-grp-btn-item").css({height:s}).find("div.e-btn-content").css("margin-top",a+"px").find(".e-btntxt").css("line-height",this.element.find("div.e-btn-content").height()+"px")}else this.element.css("min-height","").find("div.e-btn-content").removeClass("e-groupBtn-padding"),this.element.find(".e-grp-btn-item").css("height","").find("div.e-btn-content").css("margin-top",""),t=="height"&&parseInt(this.element.height())<o?this.element.addClass("e-groupbutton-hSmall").find("div.e-btn-content").addClass("e-groupBtn-padding"):parseInt(this.element.height())<35?this.element.find("div.e-btn-content").addClass("e-groupBtn-padding"):this.element.removeClass("e-groupbutton-hSmall").find("div.e-btn-content").removeClass("e-groupBtn-padding")},_setRTL:function(n){n?this.element.addClass("e-rtl"):this.element.removeClass("e-rtl")},_setControlStatus:function(n){n?this.enable():this.disable()},_setSkin:function(n){this.model.cssClass!=n?this.element.removeClass(this.model.cssClass).addClass(n):this.element.addClass(n)},_setOrientation:function(n){n!=t.Orientation.Vertical?(this.itemsContainer.removeClass("e-vertical").addClass("e-horizontal"),this._vertical=!1,this.model.orientation=t.Orientation.Horizontal):(this.itemsContainer.removeClass("e-horizontal").addClass("e-vertical"),this._vertical=!0,this.model.orientation=t.Orientation.Vertical)},_setRoundedCorner:function(n){n?this.element.addClass("e-corner"):this.element.removeClass("e-corner")},_updateCss:function(){this.items.filter(".last").removeClass("last");this.items.filter(".e-grp-btn-item:visible:not(.e-hidden)").last().addClass("last");this.items.filter(".e-hidden").length==this.items.length?this.element.addClass("e-no-border"):this.element.removeClass("e-no-border")},_selectDeselect:function(n,t){this.isSelected(n)?this.model.groupButtonMode=="checkbox"&&(this._removeSelection(n),this._focusedItem=null):(this._focusedItem=n,this._setSeletedItem(n,t))},_getVisibleItems:function(){return this.items.filter(".e-grp-btn-item:visible:not(.e-hidden, .e-disable)")},_getNodeByID:function(t){return typeof t=="number"?t=this.items[t]:typeof t!="object"&&t!=""&&(t=this.itemsContainer.find(".e-grp-btn-item#"+t)),t=n(t),t=n(t[0]),t},_addSelectedIndex:function(n){var t=this.model.selectedItemIndex;this._removeNullInArray(t);!t instanceof Array&&(t=[]);t.indexOf(n)==-1&&t.push(n)},_removeSelectedIndex:function(n){var t=this.model.selectedItemIndex,i;!t instanceof Array&&(t=[]);i=t.indexOf(n);i!=-1&&(t.splice(i,1),t.length==0&&t.push(-1))},_removeNullInArray:function(n){var t=n.indexOf(-1);t!=-1&&n.splice(t,1)},_onKeyPress:function(i){var u,r,e,f,o;if(u=i.keyCode?i.keyCode:i.which?i.which:i.charCode,r=this._getVisibleItems(),this._focusedItem?(e=this._focusedItem,this._focusedItem=null):e=r.filter(".e-hover"),i.type=="keydown"){if((u==38||u==39)&&this.model.orientation!=t.Orientation.Vertical||(u==39||u==40)&&this.model.orientation==t.Orientation.Vertical?(i.preventDefault(),f=n(r[r.index(e)+1]).length>0?n(r[r.index(e)+1]):r.first()):(u==37||u==40)&&this.model.orientation!=t.Orientation.Vertical||(u==37||u==38)&&this.model.orientation==t.Orientation.Vertical?(i.preventDefault(),f=n(r[r.index(e)-1]).length>0?n(r[r.index(e)-1]):r.last()):u==36?(i.preventDefault(),f=r.first()):u==35?(i.preventDefault(),f=r.last()):u==32&&i.preventDefault(),f){if(o=this._getDetails(f),o.element=f,o.event=i,this._triggerEvent("keyPress",o))return;f.addClass("e-hover").siblings(".e-hover").removeClass("e-hover")}}else switch(u){case 13:case 32:i.preventDefault();this._selectDeselect(e,i);break;case 27:i.preventDefault();this.element.blur()}},_onfocusIn:function(){this.element.addClass("e-focus");this._clicked||this._getVisibleItems().first().addClass("e-hover").siblings(".e-hover").removeClass("e-hover");this._on(this.element,"keyup keydown",this._onKeyPress)},_onfocusOut:function(){this.element.removeClass("e-focus").find(".e-grp-btn-item.e-hover").removeClass("e-hover");this._off(this.element,"keyup keydown",this._onKeyPress)},_onBtnHover:function(t){var i=n(t.currentTarget);i.hasClass("e-disable")||i.addClass("e-hover")},_onBtnLeave:function(t){var i=n(t.currentTarget);i.hasClass("e-disable")||i.removeClass("e-hover")},_onBtnClick:function(t){var i=n(t.currentTarget);i.hasClass("e-disable")||this._selectDeselect(i,t)},_onMouseDown:function(){this._clicked=!0},_triggerEvent:function(n,t){if(this._isRender)return this._trigger(n,t)},_wireUnwireEvents:function(n){this[n](this.element,"mousedown",this._onMouseDown)[n](this.element,"focus",this._onfocusIn)[n](this.element,"blur",this._onfocusOut);this[n](this.items,"click",this._onBtnClick);t.isTouchDevice()||(this[n](this.items,"mouseenter",this._onBtnHover),this[n](this.items,"mouseleave",this._onBtnLeave))},isSelected:function(n){return n=this._getNodeByID(n),n[0]!=null&&n.hasClass("e-grp-btn-item")?n.hasClass("e-active"):void 0},isDisabled:function(n){return n=this._getNodeByID(n),n[0]!=null&&n.hasClass("e-grp-btn-item")?n.attr("aria-disabled")=="true"?!0:!1:void 0},disable:function(){this.element.addClass("e-disable").attr("aria-disabled",!0);this.items.addClass("e-disable").attr("aria-disabled",!0);this.model.enabled=!1;this._wireUnwireEvents("_off")},enable:function(){this.element.removeClass("e-disable").attr("aria-disabled",!1);this.items.removeClass("e-disable").attr("aria-disabled",!1);this.model.enabled=!0;this._wireUnwireEvents("_on")},show:function(){this.element.removeClass("e-hidden")},hide:function(){this.element.addClass("e-hidden")},getSelectedItem:function(){return this.element.find(".e-grp-btn-item.e-active")},getIndex:function(n){return n=this._getNodeByID(n),n[0]!=null&&n.hasClass("e-grp-btn-item")?this.items.index(n):void 0},enableItem:function(n){n=this._getNodeByID(n);n[0]!=null&&n.hasClass("e-grp-btn-item")&&(n.removeClass("e-disable").attr("aria-disabled",!1),n.next(".e-grp-btn-item").removeClass("e-border-left e-border-bottom"))},disableItem:function(n){n=this._getNodeByID(n);n[0]!=null&&n.hasClass("e-grp-btn-item")&&!n.hasClass("e-disable")&&(n.addClass("e-disable").attr("aria-disabled",!0),this.model.orientation==t.Orientation.Horizontal?(this._setDimension("width",this.model.width),n.next(".e-grp-btn-item").addClass("e-border-left")):(n.next(".e-grp-btn-item").addClass("e-border-bottom"),this._setDimension("height",this.model.height)))},selectItem:function(n){n=this._getNodeByID(n);n[0]!=null&&n.hasClass("e-grp-btn-item")&&this._setSeletedItem(n)},deselectItem:function(n){n=this._getNodeByID(n);n[0]!=null&&n.hasClass("e-grp-btn-item")&&this._removeSelection(n)},showItem:function(n){n=this._getNodeByID(n);n[0]!=null&&n.hasClass("e-grp-btn-item")&&(n.removeClass("e-hidden"),this._updateCss())},hideItem:function(n){n=this._getNodeByID(n);n[0]!=null&&n.hasClass("e-grp-btn-item")&&(n.addClass("e-hidden"),this._updateCss())}});t.ButtonSize={Normal:"normal",Mini:"mini",Small:"small",Medium:"medium",Large:"large"};t.GroupButtonMode={CheckBox:"checkbox",RadioButton:"radiobutton"};t.ContentType={TextOnly:"textonly",ImageOnly:"imageonly",ImageBoth:"imageboth",TextAndImage:"textandimage",ImageTextImage:"imagetextimage"};t.ImagePosition={ImageRight:"imageright",ImageLeft:"imageleft",ImageTop:"imagetop",ImageBottom:"imagebottom"}})(jQuery,Syncfusion)});
