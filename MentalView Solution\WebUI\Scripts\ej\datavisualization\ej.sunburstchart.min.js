/*!
*  filename: ej.sunburstchart.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min"],n):n()})(function(){var t=this&&this.__extends||function(n,t){function r(){this.constructor=n}for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)},n;(function(n){var h,v,c,y,u,o,r,i,a,f,e,p,s,l,w;(function(n){n[n.left="left"]="left";n[n.right="right"]="right";n[n.center="center"]="center"})(n.sunburstHorizontalAlignment||(n.sunburstHorizontalAlignment={}));h=n.sunburstHorizontalAlignment,function(n){n[n.circle="circle"]="circle";n[n.diamond="diamond"]="diamond";n[n.cross="cross"]="cross";n[n.pentagon="pentagon"]="pentagon";n[n.rectangle="rectangle"]="rectangle";n[n.triangle="triangle"]="triangle"}(n.sunburstLegendShape||(n.sunburstLegendShape={}));v=n.sunburstLegendShape,function(n){n[n.top="top"]="top";n[n.bottom="bottom"]="bottom";n[n.middle="middle"]="middle"}(n.sunburstVerticalAlignment||(n.sunburstVerticalAlignment={}));c=n.sunburstVerticalAlignment,function(n){n[n.flatlight="flatlight"]="flatlight";n[n.flatdark="flatdark"]="flatdark"}(n.sunburstTheme||(n.sunburstTheme={}));y=n.sunburstTheme,function(n){n[n.opacity="opacity"]="opacity";n[n.color="color"]="color"}(n.sunburstHighlightType||(n.sunburstHighlightType={}));u=n.sunburstHighlightType,function(n){n[n.rotation="rotation"]="rotation";n[n.fadeIn="fadeIn"]="fadeIn"}(n.sunburstAnimationType||(n.sunburstAnimationType={}));o=n.sunburstAnimationType,function(n){n[n.opacity="opacity"]="opacity";n[n.color="color"]="color"}(n.sunburstSelectionType||(n.sunburstSelectionType={}));r=n.sunburstSelectionType,function(n){n[n.top="top"]="top";n[n.bottom="bottom"]="bottom";n[n.left="left"]="left";n[n.right="right"]="right";n[n.float="float"]="float"}(n.sunburstLegendPosition||(n.sunburstLegendPosition={}));i=n.sunburstLegendPosition,function(n){n[n.none="none"]="none";n[n.toggleSegmentVisibility="toggleSegmentVisibility"]="toggleSegmentVisibility";n[n.toggleSegmentSelection="toggleSegmentSelection"]="toggleSegmentSelection"}(n.sunburstLegendClickAction||(n.sunburstLegendClickAction={}));a=n.sunburstLegendClickAction,function(n){n[n.near="near"]="near";n[n.far="far"]="far";n[n.center="center"]="center"}(n.sunburstAlignment||(n.sunburstAlignment={}));f=n.sunburstAlignment,function(n){n[n.point="point"]="point";n[n.all="all"]="all";n[n.child="child"]="child";n[n.parent="parent"]="parent"}(n.sunburstHighlightMode||(n.sunburstHighlightMode={}));e=n.sunburstHighlightMode,function(n){n[n.point="point"]="point";n[n.all="all"]="all";n[n.child="child"]="child";n[n.parent="parent"]="parent"}(n.sunburstSelectionMode||(n.sunburstSelectionMode={}));p=n.sunburstSelectionMode,function(n){n[n.trim="trim"]="trim";n[n.hide="hide"]="hide";n[n.none="none"]="none"}(n.sunburstLabelOverflowMode||(n.sunburstLabelOverflowMode={}));s=n.sunburstLabelOverflowMode,function(n){n[n.angle="angle"]="angle";n[n.normal="normal"]="normal"}(n.sunburstLabelRotationMode||(n.sunburstLabelRotationMode={}));l=n.sunburstLabelRotationMode,function(n){n[n.center="center"]="center";n[n.near="near"]="near";n[n.far="far"]="far"}(n.titleAlignment||(n.titleAlignment={}));w=n.titleAlignment,function(w){var b=function(b){function k(t,w){b.call(this);this.defaults={enableAnimation:!1,load:null,preRender:null,loaded:null,dataLabelRendering:null,titleRendering:null,tooltipInitialize:null,pointRegionClick:null,drillDownClick:null,drillDownBack:null,drillDownReset:null,pointRegionMouseMove:null,legendItemRendering:null,legendItemClick:null,segmentRendering:null,animationType:o.rotation,palette:null,opacity:1,valueMemberPath:null,margin:{top:10,bottom:10,left:10,right:10},points:{x:null,y:null,text:null,fill:null},border:{color:null,width:2},segmentBorder:{color:null,width:2},startAngle:null,endAngle:null,dataSource:null,isResponsive:!0,size:{height:"",width:""},innerRadius:.4,radius:1,tooltip:{visible:!1,border:{color:"#707070",width:1},fill:"#FFFFFF",opacity:.95,font:{fontFamily:"Segoe UI",fontStyle:"Normal",fontWeight:"Regular",color:"#707070",opacity:1,size:"12px"},template:null,format:"#point.x# : #point.y#"},dataLabelSettings:{visible:!1,labelRotationMode:l.angle,font:{fontFamily:"Segoe UI",fontStyle:"Normal",fontWeight:"Regular",color:null,opacity:1,size:"12px"},template:null,fill:null,labelOverflowMode:s.trim},title:{text:"",textAlignment:f.center,font:{fontFamily:"Segoe UI",fontWeight:"Regular",fontStyle:"Normal",color:null,opacity:1,size:"20px"},subtitle:{visible:!0,textAlignment:f.far,text:"",font:{fontFamily:"Segoe UI",fontWeight:"Regular",fontStyle:"Normal",color:null,opacity:1,size:"12px"}},visible:!0},zoomSettings:{enable:!1,toolbarHorizontalAlignment:h.right,toolbarVerticalAlignment:c.top},highlightSettings:{enable:!1,mode:e.point,opacity:.5,color:"red",type:u.opacity},selectionSettings:{enable:!1,mode:p.point,opacity:.5,color:"green",type:r.opacity},levels:null,legend:{clickAction:a.toggleSegmentVisibility,visible:!1,position:i.bottom,rowCount:null,columnCount:null,shape:v.circle,alignment:f.center,title:{font:{fontFamily:"Segoe UI",fontWeight:"Regular",fontStyle:"Normal",color:null,opacity:1,size:"12px"},text:"",textAlignment:f.center,visible:!0},itemStyle:{height:7.5,width:7.5},itemPadding:10,size:{height:null,width:null},border:{color:null,width:1},font:{fontFamily:"Segoe UI",fontWeight:"Regular",fontStyle:"Normal",color:null,opacity:1,size:"12px"},location:{x:null,y:null}},theme:y.flatlight,background:null};this.model=null;this.svgLink="http://www.w3.org/2000/svg";this.elementSpacing=10;this._id=null;this._legendHighlight=!1;this.legendClicked=!1;this._legendMaxWidth=0;this._legendMaxHeight=0;this._drillInnerRadius=[];this._drillOuterRadius=[];this.common={data:null,cancel:!1};this.legendPages=[];this.groupID=[];this.highlightgroupID=[];this.selectedgroupID=[];this.baseID=null;this._previousState=[];this._drillDownStartAngle=[];this._drillDownEndAngle=[];this._sunburstRedraw=!1;this._isDoubleClickEvent=!1;this.tapCount=0;this.drilldownCount=0;this.tapped=0;this._points=[];this.visiblePoints=[];this.parentNames=[];this.levelNames=[];this.isResize=!1;this.revAnimation=!1;this.validTags=["div"];this._id=t;!w||(this.model=n.compareExtend({},w,this.defaults))}return t(k,b),k.prototype._destroy=function(){var n;n=document.getElementById(this.container.id);this.isNullOrUndefined(n)||(this.unBindEvents(this.parentElement),this.unbindResizeEvents(),n.parentNode.removeChild(n))},k.prototype.unBindEvents=function(n){var t="",r=this.browserInfo(),u=r.isMSPointerEnabled,f=r.pointerEnabled,e=u?f?"pointerup":"MSPointerUp":"touchend mouseup",o=u?f?"pointermove":"MSPointerMove":"touchmove mousemove",i;for(t=o,t=t.split(" "),i=0;i<t.length;i++)n.removeEventListener(t[i],this.sunburstMousemove),n.removeEventListener(e,this.sunburstLeave);n.removeEventListener("mouseout",this.sunburstLeave);n.removeEventListener("dblclick",this.sunburstChartDoubleClick);n.removeEventListener("contextmenu",this.sunburstChartRightClick)},k.prototype.unbindResizeEvents=function(){ej.isTouchDevice()&&this._isOrientationSupported()?window.removeEventListener("orientationchange",this.sunBurstResize):window.removeEventListener("resize",this.sunBurstResize)},k.prototype._setModel=function(t){for(var i in t)switch(i){default:n.deepExtend(!0,this.model,{},t[i])}this.redraw()},k.prototype.supportSVG=function(){return!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg")},k.prototype._init=function(){if(this.supportSVG()){var n=void 0,r=void 0,t=void 0,i=void 0;this._startAngle=this.model.startAngle;this._endAngle=this.model.endAngle;this.isResize||(this._enableAnimation=this.model.enableAnimation,this._drillDownStartAngle=[],this._drillDownEndAngle=[],this._drillInnerRadius=[],this._drillOuterRadius=[]);r=w.extend({},this.common);this._trigger("load",r);this.parentElement=this.isNullOrUndefined(this.parentElement)?document.getElementById(this._id):this.parentElement;n=this.createSvg({id:this._id+"_svg"});this.container=n;this.parentElement.appendChild(n);this.setSvgsize(this.model.size,this._id);this._sunburstRedraw==!1&&this.bindEvents(this.parentElement);this.isResize?this.isResize=this.isResize?!1:!0:(!this.isNullOrUndefined(this.model.dataSource)&&this.model.dataSource.length>0&&this.processDataSource(this.model.dataSource),this.setColors(this.model.points),this._previousState.push(this.model.points),this.processData(this.model.points));t=w.extend({},this.common);t.data={};this._trigger("preRender",t);this.drawSunburst(this.layerData);this._isDrillDown&&this.drawToolbar(n);i=w.extend({},this.common);i.data={model:this.model};this._trigger("loaded",i)}else alert("Sunburst will not be support in IE version < 8")},k.prototype.drawSunburst=function(n){var s,h,f,t,e,i=this.model.title,c=this.model.legend,l=this.model.dataLabelSettings,v=this.model.enableAnimation,r,u,a;s=this.drawContainerRect();document.getElementById(this._id+"_svg").appendChild(s);c.visible&&!this.legendClicked&&this.calculateLegendBounds();e={width:this.width,height:this.height};i.text!=""&&i.visible&&(h=this.drawTitle(i,e),document.getElementById(this._id+"_svg").appendChild(h));this.areaBounds=this.calculateSize(e,i);f=this.sunburstRender(n);document.getElementById(this._id+"_svg").appendChild(f);this._enableAnimation&&(r=void 0,this.groupingRegions(this.model.points),this.animationRegions.length>0&&(this.model.animationType==o.rotation?this._isDrillDown?this.rotateDrillDownAnimation(this,this.animationRegions):this.rotateAnimation(this,this.animationRegions):(this.totallayerCount>1?(u=this.animationRegions[0].length,r=this.animationRegions[0][u-1].endAngle):(u=this.animationRegions.length,r=this.animationRegions[u-1][0].endAngle),this.animateLayer(this,f,this.animationRegions[0],0,0,r))));l.visible&&(t=this.drawDatalabel(n),l.template==null?(this._enableAnimation&&t.setAttribute("visibility","hidden"),document.getElementById(this._id+"_svg").appendChild(t)):this._enableAnimation&&(document.getElementById(t.id).style.visibility="hidden"),this.sunburstDoubleClick=this.sunburstDoubleClick.bind(this),document.getElementById(t.id).addEventListener("dblclick",this.sunburstDoubleClick,!0));c.visible&&(this.gLegendElement=this.createGroup({id:this._id+"SunburstLegend"}),this.model.legendInitialize&&(a=w.extend({},this.common),this._trigger("legendInitialize",a)),this.drawSunburstLegend())},k.prototype.animateLayer=function(n,t,i,r,u,f){var h=i[r].startAngle,o=i[r].endAngle,w,e,a,g,nt,c,b,k,s,v=h,l,y,p=0,d=50;w=(o-h)/Math.ceil(d*(o-h)/f);y=n.model.opacity/2/Math.ceil(d*(o-h)/f);e=i[r];this.layerAnimate=setInterval(function(){a=e.layerNumber;g=e.layerIndex;nt=e.pointIndex;k=n.circularRadius[a-1];l=e.id;v=v+w;c=v;e.endAngle=c>o?o:c;n.isNullOrUndefined(n.model)?clearInterval(n.layerAnimate):(b=n.calculateArcData(e,a,k),s=t.querySelector("#"+l),p+=y,s&&(s.setAttribute("d",b.Direction),s.setAttribute("opacity",p.toString())),document.getElementById(l).getAttribute("visibility")=="hidden"&&document.getElementById(l).setAttribute("visibility","visible"),c>=o&&(clearInterval(n.layerAnimate),n.fadeIn(s,p,n,y),r=r+1,r<i.length?n.animateLayer(n,t,i,r,u,f):(r=0,u=u+1,u<n.animationRegions.length?n.animateLayer(n,t,n.animationRegions[u],r,u,f):n.model.dataLabelSettings.visible&&(document.getElementById(n._id+"_svg_DataLabelGroup").style.visibility="visible"))))},10)},k.prototype.fadeIn=function(n,t,i){var r=setInterval(function(){t=t+.1;n.setAttribute("opacity",t.toString());(i.model==null||t>=i.model.opacity)&&clearInterval(r)},50)},k.prototype.rotateAnimation=function(n,t){var y=t.length,r=0,e,h,u,f,l,i=0,a,v,o,c,s=0;l=document.getElementById(n._id+"_svg");f=n.model.startAngle||0;u=n.model.endAngle||360;o=Math.abs(u-f);c=10/o;r=20/360*o+f;y>0&&(this.rotateAnimate=setInterval(function(){if(n.model){if(e=document.getElementById(n._id+"_svg_SunBurstElementGroup"),e!=null&&e.parentNode.removeChild(e),n.model.startAngle=f,n.model.endAngle=r>u?u:r,n._enableAnimation=!1,h=n.sunburstRender(n.layerData),l.appendChild(h),i=i+c,i=i>1?1:i,a=n.width/2*(1-i),v=n.height/2*(1-i),h.setAttribute("transform","translate("+a+","+v+") scale("+i+")"),r>=u&&(clearInterval(n.rotateAnimate),n.rotateAnimate=undefined,n._enableAnimation=!0,n.model.startAngle=f,n.model.endAngle=u,n.model.dataLabelSettings.visible)){var t=document.getElementById(n._id+"_svg_DataLabelGroup"),y=.1;n.model.dataLabelSettings.template==null?document.getElementById(n._id+"_svg").appendChild(t):document.getElementById(n._id).appendChild(t);t.style.visibility="visible";t.style.opacity=y.toString();n.dLAnimate=setInterval(function(){t.style.opacity=y.toString();y=y+.05;y>1&&clearInterval(n.dLAnimate)},10)}r=r+(10-s);s=s+.1;c=1/(o/10-s)}else clearInterval(n.rotateAnimate)},25))},k.prototype.rotateDrillDownAnimation=function(n,t){var w=t.length,s=0,h=0,c,l,i,r,a,f=0,v,y,o=1,u=.05,e=0,p;a=document.getElementById(n._id+"_svg");r=n.model.startAngle||0;i=n.model.endAngle||360;s=f=n._drillDownStartAngle[n.drilldownCount-1];h=e=n._drillDownEndAngle[n.drilldownCount-1];v=(s-r)/20;y=(i-h)/20;w>0&&(p=setInterval(function(){var t,w,b;c=document.getElementById(n._id+"_svg_SunBurstElementGroup");c.parentNode.removeChild(c);n.model.startAngle=f<r?r:f;n.model.endAngle=e>i?i:e;n._enableAnimation=!1;l=n.sunburstRender(n.layerData);a.appendChild(l);f<=r&&e>=i&&(clearInterval(p),n._enableAnimation=!0,n.model.startAngle=r,n.model.endAngle=i,n.model.dataLabelSettings.visible&&(t=document.getElementById(n._id+"_svg_DataLabelGroup"),w=.1,n.model.dataLabelSettings.template==null?document.getElementById(n._id+"_svg").appendChild(t):document.getElementById(n._id).appendChild(t),t.style.visibility="visible",t.style.opacity=w.toString(),b=setInterval(function(){t.style.opacity=w.toString();w=w+.05;w>1&&clearInterval(b)},10)));o=o+(1-u);u=u+.015;u=u>.7?.7:u;f=s-v*o;e=h+y*o},25))},k.prototype.groupingRegions=function(){var e=this.sunburstRegions,n,t,r=this.sunburstRegions[this.sunburstRegions.length-1].region,u=[],f,i;for(this.animationRegions=[],u.push(r),i=0;i<r.length;i++)e.length>1?(n=[],t=this.layerData[r[i].layerIndex].point[r[i].pointIndex],f=t.groupNumber,this.isNullOrUndefined(t.point)||this.findChildRegions(t.point,n,t.layerNumber,u,f),this.model.animationType==o.rotation?(n.splice(0,0,t),t.count=0,t.groupLength=n.length,this.animationRegions.push(n)):this.animationRegions=u):(n=[],n.push(r[i]),this.animationRegions.push(n))},k.prototype.findChildRegions=function(n,t,i,r,u){for(var f=0;f<n.length;f++)n[f].pointIndex=f,n[f].layerNumber=this.layerData[n.layerData].layerNumber,n[f].groupNumber=u,n[f].count=0,this.model.animationType==o.rotation?t.push(n[f]):(this.isNullOrUndefined(r[n[f].layerNumber-1])&&(r[n[f].layerNumber-1]=[]),r[n[f].layerNumber-1].push(n[f])),this.isNullOrUndefined(n[f].point)||this.findChildRegions(n[f].point,t,i,r,u)},k.prototype.bindEvents=function(n){var t="",r="",u=this.browserInfo(),f=ej.isDevice(),e=u.isMSPointerEnabled,o=u.pointerEnabled,s=e?o?"pointerup":"MSPointerUp":"touchend mouseup",h=e?o?"pointermove":"MSPointerMove":"touchmove mousemove",i;for(t=h,t=t.split(" "),this.sunburstMousemove=this.sunburstMousemove.bind(this),this.sunburstClick=this.sunburstClick.bind(this),this.sunburstChartDoubleClick=this.sunburstChartDoubleClick.bind(this),this.sunburstChartRightClick=this.sunburstChartRightClick.bind(this),n.addEventListener("click",this.sunburstClick,!0),this.sunburstLeave=this.sunburstLeave.bind(this),i=0;i<t.length;i++)(n.addEventListener(s,this.sunburstLeave),r=t[i],f&&r=="mousemove")||n.addEventListener(r,this.sunburstMousemove);f&&n.addEventListener("mousedown",this.sunburstMousemove);n.addEventListener("mouseout",this.sunburstLeave,!0);this.getIEversion()!=-1&&(this.sunburstDoubletap=this.sunburstDoubletap.bind(this),n.addEventListener("pointerdown",this.sunburstDoubletap,!0));this.model.isResponsive&&this.bindResizeEvents();n.addEventListener("dblclick",this.sunburstChartDoubleClick,!0);n.addEventListener("contextmenu",this.sunburstChartRightClick,!0)},k.prototype.browserInfo=function(){var n={},t=[],i={webkit:/(chrome)[ \/]([\w.]+)/i,safari:/(webkit)[ \/]([\w.]+)/i,msie:/(msie) ([\w.]+)/i,opera:/(opera)(?:.*version|)[ \/]([\w.]+)/i,mozilla:/(mozilla)(?:.*? rv:([\w.]+)|)/i};for(var r in i)if(i.hasOwnProperty(r)&&(t=navigator.userAgent.match(i[r]),t)){n.name=t[1].toLowerCase();n.version=t[2];!navigator.userAgent.match(/Trident\/7\./)||(n.name="msie");break}return n.isMSPointerEnabled=n.name=="msie"&&n.version>9&&window.navigator.msPointerEnabled,n.pointerEnabled=window.navigator.pointerEnabled,n},k.prototype.sunburstChartDoubleClick=function(n){if(this.model.doubleClick!=""){var t=w.extend({},this.common);t.model=this.model;t.event=n;this._trigger("doubleClick",t)}},k.prototype.sunburstChartRightClick=function(n){if(this.model.rightClick!=""){var t=w.extend({},this.common);t.model=this.model;t.event=n;this._trigger("rightClick",t)}},k.prototype.sunburstDoubletap=function(n){var t=n.target.id,i;t.indexOf("reset")==-1&&t.indexOf("back")==-1&&(n.preventDefault(),this.tapped=this.tapped+1,i=this.tapped)},k.prototype.bindResizeEvents=function(){this.sunBurstResize=this.sunBurstResize.bind(this);ej.isTouchDevice()&&this._isOrientationSupported()?window.addEventListener("orientationchange",this.sunBurstResize,!0):window.addEventListener("resize",this.sunBurstResize.bind(this),!0)},k.prototype._isOrientationSupported=function(){return"orientation"in window&&"onorientationchange"in window},k.prototype.sunBurstResize=function(){var n=this,t=w(n.container);this._resizeTO&&clearTimeout(this._resizeTO);this._resizeTO=setTimeout(function(){n.model&&(n.isResize=!0,n.isNullOrUndefined(document.getElementById(n._id+"reset"))||(n._isDrillDown=!0),n.legendClicked=!0,n._enableAnimation=!1,n.redraw(),n._enableAnimation=n.model.enableAnimation,n._isDrillDown=!1,n.legendClicked=!1)},500)},k.prototype.getIEversion=function(){var i=-1,n,t;return navigator.appName=="Microsoft Internet Explorer"?(n=navigator.userAgent,t=new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})"),t.exec(n)!=null&&(i=parseFloat(RegExp.$1))):navigator.appName=="Netscape"&&(n=navigator.userAgent,t=new RegExp("Trident/.*rv:([0-9]{1,}[.0-9]{0,})"),t.exec(n)!=null&&(i=parseFloat(RegExp.$1))),i},k.prototype.sunburstDoubleClick=function(n){var t;this.model.zoomSettings.enable&&(t=this.getSunburstPoint(n),this.isNullOrUndefined(t)||(t.pointData[0].layerNumber==1?this.drilldownCount==0&&this.drillDown(t):this.drillDown(t)))},k.prototype.drillDown=function(n){var t,u=[],f,i,e,s,r,h,c;i=document.getElementById(this._id+"_svg");i.parentNode.replaceChild(i.cloneNode(!1),i);t=this.layerData[n.pointData[0].layerIndex].point[n.pointData[0].pointIndex];this.drilldownCount=this.drilldownCount+1;this._drillDownStartAngle.push(t.startAngle*180/Math.PI);this._drillDownEndAngle.push(t.endAngle*180/Math.PI);this._isDrillDown=!0;this._isDoubleClickEvent=!0;this.model.enableAnimation&&this.model.animationType==o.rotation&&(r=n.pointData[0].layerNumber,r==1?(this._drillInnerRadius.push(this.innerRadius),this._drillOuterRadius.push(this.circularRadius)):(h=this.innerRadius.slice(r-1,this.innerRadius.length),c=this.circularRadius.slice(r-1,this.circularRadius.length),this._drillInnerRadius.push(h),this._drillOuterRadius.push(c)));f=this.isNullOrUndefined(this.model.dataSource)?{fill:n.pointData[0].fill,x:n.pointData[0].x,y:n.pointData[0].y,point:t.point,text:n.pointData[0].text}:{fill:n.pointData[0].fill,x:n.pointData[0].x,parentChildName:n.pointData[0].parentChildName,y:n.pointData[0].y,point:t.point,text:n.pointData[0].text};this.model.drillDownClick&&(e=w.extend({},this.common),e.data=f,this._trigger("drillDownClick",e));u.push(f);this._previousState.push(u);this.processData(u);this.drawSunburst(this.layerData);this._isDrillDown=!1;this.isNullOrUndefined(document.getElementById(this._id+"reset"))&&(s=document.getElementById(this._id+"_svg"),this.drawToolbar(s))},k.prototype.drawToolbar=function(n){var t,i,r;r=n.getBoundingClientRect();i=this.drawResetButton(r);n.appendChild(i);this.reset=this.reset.bind(this);document.getElementById(this._id+"reset").addEventListener("click",this.reset,!0);t=this.drawBackButton(r);n.appendChild(t);this.back=this.back.bind(this);document.getElementById(this._id+"back").addEventListener("click",this.back,!0);ej.isDevice()||(document.getElementById(this._id+"back").addEventListener("mousemove",this.sunburstMousemove,!0),document.getElementById(this._id+"reset").addEventListener("mousemove",this.sunburstMousemove,!0));this.toolbarPositioning(t,i)},k.prototype.toolbarPositioning=function(n,t){var e,o,u,f,s,r,i=this.elementSpacing,l=this.model.zoomSettings.toolbarHorizontalAlignment,a=this.model.zoomSettings.toolbarVerticalAlignment;r=n.getBoundingClientRect();s=t.getBoundingClientRect();l==h.right?(o=this.width-i-s.width,u=this.width-2*i-2*s.width):l==h.left?(u=i,o=r.width+2*i):l==h.center&&(u=this.width/2-(r.width+i+s.width)/2,o=u+i+r.width);a==c.top?f=i:a==c.bottom?f=this.height-i-r.height:a==c.middle&&(f=this.height/2-r.height/2);e="translate("+o.toString()+","+f.toString()+")";t.setAttribute("transform",e);e="translate("+u.toString()+","+f.toString()+")";n.setAttribute("transform",e)},k.prototype.drawBackButton=function(){var n,t,i;return i=this.createGroup({id:this._id+"back"}),n={id:this._id+"backPath",d:"M24,27H3c-1.7,0-3-1.3-3-3V3c0-1.7,1.3-3,3-3h21c1.7,0,3,1.3,3,3v21C27,25.7,25.7,27,24,27z",fill:"#D6D6D6"},t=this.drawPath(n),i.appendChild(t),n={id:this._id+"backPath1",x:7.7,y:12.5,width:13.8,height:2,fill:"#5E5C5C"},t=this.drawRect(n),i.appendChild(t),n={id:this._id+"backPath2",points:"11.4,19.8 5.5,13.5 11.4,7.2 12.9,8.6 8.2,13.5 12.9,18.4",fill:"#5E5C5C"},t=this.drawPolygon(n),i.appendChild(t),i},k.prototype.back=function(){var f=this.layerData,n=document.getElementById(this._id+"_svg"),e=document.getElementById(this._id+"back"),s=document.getElementById(this._id+"reset"),u=this.model,t,i,r;this.revAnimation||(u.drillDownBack&&(i=w.extend({},this.common),i.data=f,this._trigger("drillDownBack",i)),this._enableAnimation&&u.animationType==o.rotation?(this._enableAnimation=!1,this.reverseAnimation(this,this.animationRegions)):(n.parentNode.replaceChild(n.cloneNode(!1),n),r=this._previousState.length-1,this.layerData=this._previousState[r-1],this._previousState.splice(r,1),this.drilldownCount=this.drilldownCount-1,this.processData(this.layerData),this.drawSunburst(this.layerData),this.drilldownCount!=0&&(t=document.getElementById(this._id+"_svg"),t.appendChild(s),t.appendChild(e))))},k.prototype.reverseAnimation=function(n,t){var nt=t.length,e=0,o=0,h,b,c,l,r,a,v,k,d,y=0,i,tt=document.getElementById(n._id+"back"),it=document.getElementById(n._id+"reset"),s,f,p,w,u=.1,g;r=document.getElementById(n._id+"_svg");l=n.model.startAngle||0;c=n.model.endAngle||360;e=n._drillDownStartAngle[n.drilldownCount-1];o=n._drillDownEndAngle[n.drilldownCount-1];k=(e-l)/20;d=(c-o)/20;i=document.getElementById(n._id+"_svg_DataLabelGroup");nt>0&&(g=setInterval(function(){if(n.revAnimation=!0,h=document.getElementById(n._id+"_svg_SunBurstElementGroup"),h&&h.parentNode.removeChild(h),i&&(i.style.visibility="hidden"),a=k*y+l,n.model.startAngle=a>=e?e:a,v=c-d*y,n.model.endAngle=v<o?o:v,b=n.sunburstRender(n.layerData),r.appendChild(b),v<=o&&a>=e){clearInterval(g);s=document.getElementById(n._id+"_svg_SunBurstElementGroup");s.id=n._id+"_svg_tempSeg";f=document.getElementById(n._id+"_svg_DataLabelGroup");r.parentNode.replaceChild(r.cloneNode(!1),r);document.getElementById(n._id+"_svg").appendChild(s);f&&(f.id=n._id+"_svg_tempLabel",document.getElementById(n._id+"_svg").appendChild(f));p=n._previousState.length-1;n.layerData=n._previousState[p-1];n._previousState.splice(p,1);n.model.startAngle=l;n.model.endAngle=c;n.drilldownCount=n.drilldownCount-1;n._drillDownStartAngle.length=n.drilldownCount;n._drillDownEndAngle.length=n.drilldownCount;n._drillInnerRadius.length=n.drilldownCount;n._drillOuterRadius.length=n.drilldownCount;n.processData(n.layerData);n.drawSunburst(n.layerData);n.drilldownCount!=0&&(r=document.getElementById(n._id+"_svg"),r.appendChild(it),r.appendChild(tt));n._enableAnimation=!0;w=document.getElementById(n._id+"_svg_SunBurstElementGroup");w.style.opacity=u.toString();i&&(i.style.opacity=u.toString());n.model.dataLabelSettings.visible&&(i=document.getElementById(n._id+"_svg_DataLabelGroup"),n.model.dataLabelSettings.template==null?document.getElementById(n._id+"_svg").appendChild(i):document.getElementById(n._id).appendChild(i),i.style.visibility="visible",i.style.opacity=u.toString());var t=setInterval(function(){w.style.opacity=u.toString();i&&(i.style.opacity=u.toString());u>=1&&(clearInterval(t),n.revAnimation=!1,s.parentNode.removeChild(s),f&&f.parentNode.removeChild(f));u=u+.1},50)}y=y+1},40))},k.prototype.drawResetButton=function(){var n,i,t,r;return t=this.createGroup({id:this._id+"reset"}),n={id:this._id+"resetPath",d:"M24,27H3c-1.7,0-3-1.3-3-3V3c0-1.7,1.3-3,3-3h21c1.7,0,3,1.3,3,3v21C27,25.7,25.7,27,24,27z",fill:"#D6D6D6"},i=this.drawPath(n),t.appendChild(i),n={id:this._id+"resetPath1",d:"M13.6,21.9c-3.6,0-6.8-2.3-8-5.7l1.8-0.6c0.9,2.6,3.4,4.4,6.2,4.4c3.4,0,6.2-2.6,6.5-6l1.9,0.2   C21.6,18.6,18,21.9,13.6,21.9z",fill:"#5E5C5C"},i=this.drawPath(n),t.appendChild(i),n={id:this._id+"resetPath2",d:"M7.1,12.5l-1.9-0.3c0.6-4.1,4.2-7.1,8.3-7.1c3,0,5.8,1.6,7.3,4.2c0.1,0.2,0.2,0.4,0.3,0.6l-1.7,0.8   c-0.1-0.2-0.2-0.3-0.2-0.4c-1.2-2-3.3-3.3-5.7-3.3C10.4,7,7.6,9.3,7.1,12.5z",fill:"#5E5C5C"},i=this.drawPath(n),t.appendChild(i),n={id:this._id+"resetPath3",points:"21.4,11.9 16.2,10.7 16.6,8.9 19.9,9.6 20.7,6.3 22.5,6.7",fill:"#5E5C5C"},r=this.drawPolygon(n),t.appendChild(r),n={id:this._id+"resetPath4",points:"6.3,20.3 4.5,19.9 5.6,14.8 10.8,16 10.4,17.8 7.1,17.1",fill:"#5E5C5C"},r=this.drawPolygon(n),t.appendChild(r),t},k.prototype.reset=function(){if(this.model.drillDownReset){var n=w.extend({},this.common);this._trigger("drillDownReset",n)}this.redraw()},k.prototype.redraw=function(){this.legendPages=[];this.isResize||(this.drilldownCount=0,this._previousState=[],this._drillDownStartAngle=[],this._drillDownEndAngle=[],this._drillInnerRadius=[],this._drillOuterRadius=[]);this._sunburstRedraw=!0;var n=document.getElementById(this._id+"_svg");this.isNullOrUndefined(n)||(n.parentNode.removeChild(n),(this.rotateAnimate||this.model.enableAnimation)&&this.stopAnimation(),this.parentElement=undefined,this._init(),this._sunburstRedraw=!1)},k.prototype.stopAnimation=function(){this.rotateAnimate&&(clearInterval(this.rotateAnimate),this.isResize||(this._enableAnimation=this.model.enableAnimation),this.model.startAngle=this._startAngle,this.model.endAngle=this._endAngle,this.dLAnimate&&clearInterval(this.dLAnimate));this.layerAnimate&&clearInterval(this.layerAnimate)},k.prototype.sunburstMousemove=function(n){var e=n.target.id,y,i,c,v,l,o,s,t,r,h,f;if(e.indexOf("reset")!=-1||e.indexOf("back")!=-1)if(this.toolbarTooltip(n,e),e.indexOf("reset")!=-1)for(y=document.getElementById(this._id+"reset"),t=0;t<5;t++)t==0?document.getElementById(this._id+"resetPath").setAttribute("fill","rgb(20, 185, 255)"):document.getElementById(this._id+"resetPath"+t.toString()).setAttribute("fill","#FFFFFF");else for(t=0;t<3;t++)t==0?document.getElementById(this._id+"backPath").setAttribute("fill","rgb(20, 185, 255)"):document.getElementById(this._id+"backPath"+t.toString()).setAttribute("fill","#FFFFFF");else if(document.getElementById(this._id+"toolbarTooltip")!=null&&document.getElementById(this._id+"toolbarTooltip").style.visibility!="hidden"&&(document.getElementById(this._id+"toolbarTooltip").style.visibility="hidden",document.getElementById(this._id+"reset")!=null&&document.getElementById(this._id+"back")!=null)){for(t=0;t<5;t++)t==0?document.getElementById(this._id+"resetPath").setAttribute("fill","#D6D6D6"):document.getElementById(this._id+"resetPath"+t.toString()).setAttribute("fill","#5E5C5C");for(t=0;t<3;t++)t==0?document.getElementById(this._id+"backPath").setAttribute("fill","#D6D6D6"):document.getElementById(this._id+"backPath"+t.toString()).setAttribute("fill","#5E5C5C")}if(i=this.getSunburstPoint(n),i!=undefined&&this.model.pointRegionMouseMove&&(c=w.extend({},this.common),c.data=i,this._trigger("pointRegionMouseMove",c)),!this.isNullOrUndefined(i)&&this.model.tooltip.visible){for(v={id:this._id+"_ToolTip"},this.gTransToolElement=this.createGroup(v),l={X:i.location.x,Y:Math.abs(i.location.y)},t=0;t<this.layerData.length;t++)this.layerData[t].layerNumber==i.pointData[0].layerNumber&&(this.isNullOrUndefined(i.pointData[0].parentName)?(s=this.layerData[0],o=this.layerData[0].point[i.pointData[0].pointIndex]):this.layerData[t].parentName==i.pointData[0].parentName&&(s=this.layerData[t],o=this.layerData[t].point[i.pointData[0].pointIndex]));this.model.tooltip.template?this.createTooltiptemplate(l,o,s,i):this.showTooltip(l,o,s,i)}else this.isNullOrUndefined(document.getElementById(this._id+"_ToolTip"))||(document.getElementById(this._id+"_ToolTip").style.visibility="hidden");if(this.model.highlightSettings.enable&&(r=void 0,h=this.model.highlightSettings,this.highlightSunburst(i),!this.isNullOrUndefined(i)&&this.model.legend.visible))for(f=0;f<this.model.points.length;f++)r=document.getElementById(this._id+"_svg_LegendItemShape"+f),h.type==u.opacity?this.selectedGroupNumber-1!=f&&i.pointData[0].legendIndex!=f&&(this.isNullOrUndefined(r)||(r.style.opacity=h.opacity.toString())):i.pointData[0].legendIndex==f?r.getAttribute("class")!="SelectionStyleSunburst"&&(r.setAttribute("class","HighlightStyleSunburst"),r.style.opacity=h.opacity.toString()):this.isNullOrUndefined(r)||r.getAttribute("class")=="HighlightStyleSunburst"||r.removeAttribute("class");e.indexOf(this._id+"_svg_LegendItem")!=-1&&this.model.legend.clickAction==a.toggleSegmentSelection&&this.legendSegments(e)},k.prototype.legendSegments=function(n){var o,u,s,t,f,h,e,r,i;for(o=n.replace(this._id+"_svg",""),u=parseInt(o.replace(/[^0-9\.]/g,"")),s=document.getElementById(this._id+"legendItem_Group"),t=s.childNodes,f=this.model.highlightSettings.opacity.toString(),r=0;r<t.length;r++)document.getElementById(t[r].id).style.opacity=u!=r?f:"1";for(h=document.getElementById(this._id+"_svg_SunBurstElementGroup"),t=h.childNodes,i=0;i<t.length;i++)t[i].id.indexOf("_legendIndex"+u)==-1&&(document.getElementById(t[i].id).style.opacity=f);if(this.model.dataLabelSettings.visible&&(e=document.getElementById(this._id+"_svg_DataLabelGroup"),e))for(t=e.childNodes,i=0;i<t.length;i++)t[i].id.indexOf("_legendIndex"+u)==-1&&(document.getElementById(t[i].id).style.opacity=f);this._legendHighlight=!0},k.prototype.toolbarTooltip=function(n,t){var i,r,u;t=t.indexOf("reset")!=-1?"Reset":"Back";r={top:n.clientY,left:n.clientX,"background-color":"white","border-style":"solid",position:"absolute","border-color":"#2F4F4F","border-width":"1px",opacity:.95,"z-index":1e6,"border-radius":"0px 0px","padding-left":"5px","padding-right":"5px","padding-top":"2px","padding-bottom":"2px"};this.isNullOrUndefined(document.getElementById(this._id+"toolbarTooltip"))?(i=document.createElement("div"),i.setAttribute("id",this._id+"toolbarTooltip")):i=document.getElementById(this._id+"toolbarTooltip");i.style.pointerEvents="none";this.setStyles(r,i);u={top:n.clientY,left:n.clientX,color:this.model.tooltip.font.color,"font-size":this.model.tooltip.font.size,"font-family":this.model.tooltip.font.fontFamily,"font-style":this.model.tooltip.font.fontStyle,"font-weight":this.model.tooltip.font.fontWeight,"align-self":"baseline"};this.setStyles(u,i);this.isNullOrUndefined(document.getElementById(this._id+"toolbarTooltip"))&&document.body.appendChild(i);i.innerHTML=t;i.style.visibility=="hidden"&&(i.style.visibility="visible");i.style.left=(n.clientX+window.pageXOffset+10).toString()+"px";i.style.top=(n.clientY+window.pageYOffset+10).toString()+"px"},k.prototype.removeHighlight=function(n){for(var t=0;t<n.length&&document.getElementById(n[t])!=null;t++)document.getElementById(n[t]).getAttribute("class")=="HighlightStyleSunburst"&&document.getElementById(n[t]).removeAttribute("class")},k.prototype.isSelectedElement=function(n,t){for(var r=!1,i=0;i<t.length;i++)n==t[i]&&(r=!0);return r},k.prototype.highlightSunburst=function(n){var r=document.getElementsByTagName("path"),o=n,f,h,y,b,p,d=this.model.legend,i=this.model.highlightSettings,g=this.model.points,k,c,l,a,v,s,w,t;if(this.isNullOrUndefined(o))if(i.type!=u.opacity){if(i.mode==e.point)f=document.getElementsByClassName("HighlightStyleSunburst"),f.length>0&&(f[0].removeAttribute("class"),this.baseID=undefined);else if(i.mode==e.all){if(!this.isNullOrUndefined(this.highlightgroupID)){for(s=0;s<this.highlightgroupID.length;s++)document.getElementById(this.highlightgroupID[s])!=null&&(document.getElementById(this.highlightgroupID[s]).getAttribute("class")==null||document.getElementById(this.highlightgroupID[s]).getAttribute("class")=="HighlightStyleSunburst")&&document.getElementById(this.highlightgroupID[s]).removeAttribute("class");this.groupID=undefined;this.selelectedElementId=undefined}}else if(f=document.getElementsByClassName("HighlightStyleSunburst"),f.length>0){for(w=0;w<f.length;w++)this.highlightgroupID.push(f[w].id);this.removeHighlight(this.highlightgroupID);this.highlightgroupID=[];this.highlightedElementId=null}}else for(t=0;t<r.length;t++)this.selectedgroupID.length==0?this.selectedgroupID.indexOf(r[t].id)==-1&&(r[t].style.opacity="1"):this.selectedgroupID.indexOf(r[t].id)==-1&&(r[t].style.opacity=this.model.selectionSettings.opacity.toString());else if(i.mode==e.point)if(i.type!=u.opacity)f=document.getElementsByClassName("HighlightStyleSunburst"),i.mode==e.point&&f.length>0&&f[0].removeAttribute("class"),h=document.getElementById(o.pointData[0].id),k=this.isSelectedElement(h.id,this.selectedgroupID),document.getElementById(h.id).getAttribute("class")!=null||k||this.createHighlightStyle(h,i),this.baseID=h.id;else{for(t=0;t<r.length;t++)this.selectedgroupID.indexOf(r[t].id)==-1&&(r[t].style.opacity=i.opacity.toString());document.getElementById(o.pointData[0].id).style.opacity="1"}else if(i.mode==e.all){if(y=this.findGroupElements(o),b=this._id+"_legendIndex"+y[0].legendIndex+"_layerNumber1_layerIndex0_pointIndex"+y[0].pointIndex.toString(),i.type!=u.opacity){if(p=document.getElementsByClassName("HighlightStyleSunburst"),p.length>0)for(c=0;c<p.length;c++)this.highlightgroupID.push(p[c].id),this.highlightgroupID[c].indexOf(this._id+"_svg_LegendItemShape")<0&&this.removeHighlight(this.highlightgroupID);this.groupID=undefined;this.highlightgroupID=[];this.selelectedElementId=undefined}if(this.highlightGroup(y,o),i.type==u.opacity){for(l=0;l<r.length;l++)this.selectedgroupID.indexOf(r[l].id)==-1&&(r[l].style.opacity=i.opacity.toString());for(t=0;t<this.highlightgroupID.length;t++)document.getElementById(this.highlightgroupID[t]).style.opacity="1"}}else if(i.mode==e.child){if(b=o.pointData[0].id,i.type!=u.opacity&&(!this.isNullOrUndefined(this.highlightgroupID)&&this.highlightgroupID.length>0&&this.removeHighlight(this.highlightgroupID),this.highlightgroupID=[],this.highlightedElementId=undefined),this.highlightChild(o),i.type==u.opacity){for(a=0;a<r.length;a++)this.selectedgroupID.indexOf(r[a].id)==-1&&(r[a].style.opacity=i.opacity.toString());for(t=0;t<this.highlightgroupID.length;t++)document.getElementById(this.highlightgroupID[t]).style.opacity="1";this.highlightedElementId=undefined}}else if(b=o.pointData[0].id,i.type!=u.opacity&&(!this.isNullOrUndefined(this.groupID)&&this.groupID.length>0&&this.removeHighlight(this.groupID),this.groupID=undefined,this.selelectedElementId=undefined),this.highlightParent(o),i.type==u.opacity){for(v=0;v<r.length;v++)this.selectedgroupID.indexOf(r[v].id)==-1&&(r[v].style.opacity=this.model.selectionSettings.opacity.toString());for(t=0;t<this.groupID.length;t++)document.getElementById(this.groupID[t]).style.opacity="1"}},k.prototype.highlightGroup=function(n,t){var i,f=this.model.highlightSettings,e=!1,r,o;for(this.highlightgroupID=[],r=0;r<n.length;r++)if(n[r].layerNumber==1)i=document.getElementById(this._id+"_legendIndex"+t.pointData[0].legendIndex+"_layerNumber1_layerIndex0_pointIndex"+n[r].pointIndex.toString()),e=this.isSelectedElement(i.id,this.selectedgroupID),f.type==u.opacity||document.getElementById(i.id).getAttribute("class")!=null||e||(this.createHighlightStyle(i,f),this.selelectedElementId=i.id),this.highlightgroupID.push(i.id);else for(o=0;o<n[r].point.length;o++)n[r].layerNumber!=1&&(i=document.getElementById(this._id+"_legendIndex"+t.pointData[0].legendIndex+"_layerNumber"+n[r].layerNumber.toString()+"_layerIndex"+n[r].layerIndex.toString()+"_pointIndex"+o.toString()),e=this.isSelectedElement(i.id,this.selectedgroupID),f.type==u.opacity||document.getElementById(i.id).getAttribute("class")!=null||e||this.createHighlightStyle(i,f),this.highlightgroupID.push(i.id))},k.prototype.highlightParent=function(n){var t,o,f=this.model.highlightSettings,e=!1,r=n.pointData[0],i;for(this.groupID=[],this.baseElement=[],this.baseElement.push(r),o=r.id,this.isNullOrUndefined(r.parentName)||this.findSelectionParent(r,r.legendIndex),i=0;i<this.baseElement.length;i++)document.getElementById("selectionSunburst")?(t=document.getElementById(this._id+"_legendIndex"+r.legendIndex+"_layerNumber"+this.baseElement[i].layerNumber.toString()+"_layerIndex"+this.baseElement[i].layerIndex.toString()+"_pointIndex"+this.baseElement[i].pointIndex),e=this.isSelectedElement(t.id,this.selectedgroupID),f.type==u.opacity||e||this.createHighlightStyle(t,f),this.groupID.push(t.id),this.highlightgroupID.push(t.id)):(t=document.getElementById(this._id+"_legendIndex"+r.legendIndex+"_layerNumber"+this.baseElement[i].layerNumber.toString()+"_layerIndex"+this.baseElement[i].layerIndex.toString()+"_pointIndex"+this.baseElement[i].pointIndex),e=this.isSelectedElement(t.id,this.selectedgroupID),f.type==u.opacity||e||this.createHighlightStyle(t,f),this.groupID.push(t.id),this.highlightgroupID.push(t.id))},k.prototype.highlightChild=function(n){var f,o,i=this.model.highlightSettings,l,h,c,e=!1,r,t,s;if(this.childrenElement=[],this.highlightgroupID=[],f=this.layerData[n.pointData[0].layerIndex].point[n.pointData[0].pointIndex],o=n.pointData[0].id,r=document.getElementById(o),e=this.isSelectedElement(r.id,this.selectedgroupID),f.point||f.layerIndex==0||e){for(l=n.pointData[0].layerNumber,this.isNullOrUndefined(f.point)||this.findChild(f,l),i.type==u.opacity||e||this.createHighlightStyle(r,i),this.highlightgroupID.push(r.id),this.highlightedElementId=o,t=0;t<this.childrenElement.length;t++)if(this.childrenElement[t].layerNumber>n.pointData[0].layerNumber)for(s=0;s<this.childrenElement[t].length;s++)h=this._id+"_legendIndex"+n.pointData[0].legendIndex+"_layerNumber"+this.childrenElement[t].layerNumber.toString()+"_layerIndex"+this.childrenElement[t].layerIndex+"_pointIndex"+s.toString(),c=document.getElementById(h),e=this.isSelectedElement(h,this.selectedgroupID),i.type==u.opacity||e||this.createHighlightStyle(c,i),this.highlightgroupID.push(c.id)}else i.type!=u.opacity&&(this.createHighlightStyle(r,i),this.highlightedElementId=o),this.highlightgroupID.push(r.id)},k.prototype.createHighlightStyle=function(n,t){var i,r,u;n.getAttribute("class")!="SelectionStyleSunburst"&&(document.getElementById("highlightSunburst")||(i=document.createElement("style"),i.type="text/css",r=t.color,u="1",i.innerHTML=" .HighlightStyleSunburst{ fill:"+r+";opacity:"+u+";stroke:;stroke-width:1}",i.setAttribute("id","highlightSunburst"),document.body.appendChild(i)),n.setAttribute("class","HighlightStyleSunburst"))},k.prototype.showTooltip=function(n,t,i,r){var h=7,c,u,l,o,s,e,f=this.model.tooltip,y=document.getElementById(this._id+"_svg"),p=window.pageYOffset,b=window.pageXOffset,a=y.getBoundingClientRect(),v;o=n.X+h/2+b+a.left;s=n.Y-3*h+p+a.top;document.getElementById(this._id+"_ToolTip")==null?(u=document.createElement("div"),u.id=this._id+"_ToolTip",u.style.pointerEvents="none"):(u=document.getElementById(this._id+"_ToolTip"),u.style.pointerEvents="none");c=this.formatTooltip(r.pointData[0],f.format);e=w.extend({},this.common);e.data={pointIndex:r.pointData[0].pointIndex,currentText:c,Location:{x:o,Y:s}};this._trigger("tooltipInitialize",e);u.innerHTML=e.data.currentText;l={top:e.data.Location.X,left:e.data.Location.Y,"background-color":f.fill,"border-style":"solid",position:"absolute","border-color":f.border.color,"border-width":f.border.width+"px",opacity:f.opacity,"z-index":1e6,"border-radius":"0px 0px","padding-left":"5px","padding-right":"5px","padding-top":"2px","padding-bottom":"2px"};this.setStyles(l,u);v={top:s,left:o,color:f.font.color,"font-size":f.font.size,"font-family":f.font.fontFamily,"font-style":f.font.fontStyle,"font-weight":f.font.fontWeight,"align-self":"baseline"};this.setStyles(v,u);this.isNullOrUndefined(document.getElementById(this._id+"Tooltip"))&&document.body.appendChild(u);u.style.visibility=="hidden"&&(u.style.visibility="visible");u.style.left=o.toString()+"px";u.style.top=s.toString()+"px"},k.prototype.formatTooltip=function(n,t){var i=t;return i.search("#point.x#")!=-1&&(i=i.replace("#point.x#",n.x)),i.search("#point.y#")!=-1&&(i=i.replace("#point.y#",n.y)),i},k.prototype.setStyles=function(n,t){for(var r=Object.keys(n),u,f=r.map(function(t){return n[t]}),i=0,e=r.length;i<e;i++)u=r[i],t.style[u]=f[i]},k.prototype.createTooltiptemplate=function(n,t,i,r){var u=document.getElementById("tooltipDiv"),f,e,s=document.getElementById(this._id+"_svg"),h=window.pageYOffset,c=window.pageXOffset,o=s.getBoundingClientRect();f=n.X+c+o.left+10;e=n.Y+h+o.top;this.isNullOrUndefined(u)?(this.tooltipFirst=!0,u=document.createElement("div"),u.className="tooltipDiv",u.style.position="absolute",u.style.zIndex="13000",u.style.display="block",u.id="tooltipDiv",u.style.backgroundColor="white",document.body.appendChild(u),this.clonenode=document.getElementById(this.model.tooltip.template).cloneNode(!0),this.clonenode.style.display="block",document.getElementById("tooltipDiv").innerHTML="",this.clonenode.innerHTML=this.parseTooltipTemplate(r.pointData[0],this.clonenode.innerHTML),document.getElementById("tooltipDiv").appendChild(this.clonenode)):(u.style.display="block",this.tooltipFirst=!1,this.clonenode.innerHTML=this.parseTooltipTemplate(r.pointData[0],this.clonenode.innerHTML));document.getElementById("tooltipDiv").style.left=f.toString()+"px";document.getElementById("tooltipDiv").style.top=e.toString()+"px"},k.prototype.parseTooltipTemplate=function(n){var i=document.getElementById(this.model.tooltip.template).cloneNode(!0),t=i.innerHTML;return t.search("#point.x#")!=-1&&(t=t.replace("#point.x#",n.x.toString())),t.search("#point.y#")!=-1&&(t=t.replace("#point.y#",n.y.toString())),t.search("#point.text#")!=-1&&(t=this.isNullOrUndefined(n.text)?t.replace("#point.text#",n.y.toString()):t.replace("#point.text#",n.text.toString())),t},k.prototype.bindClickEvent=function(n){var t,i;return t=this.getSunburstPoint(n),!this.isNullOrUndefined(t)&&this.model.pointRegionClick&&(i=w.extend({},this.common),i.data=t,this._trigger("pointRegionClick",i)),t},k.prototype.sunburstClick=function(n){var o=this.bindClickEvent(n),t,c,f,e=this.model.selectionSettings,l,i,s=new Date,h,u;if(this.model.click!=null&&(h={model:this.model,event:n},this._trigger("click",h)),ej.isTouchDevice()&&this._doubleTapTimer!=null&&s-this._doubleTapTimer<300&&this.sunburstChartDoubleClick(n),this._doubleTapTimer=s,e.enable&&(this.selectSunburst(o,n),!this.isNullOrUndefined(o)&&this.model.legend.visible))for(u=0;u<this.model.points.length;u++)i=document.getElementById(this._id+"_svg_LegendItemShape"+u),this.isNullOrUndefined(i)||(l=this.model.points[u],e.type==r.opacity?o.pointData[0].legendIndex!=u&&(i.style.opacity=e.opacity.toString()):this.selectedGroupNumber-1==u?(i.setAttribute("class","SelectionStyleSunburst"),i.style.opacity=e.opacity.toString()):i.getAttribute("class")=="SelectionStyleSunburst"||i.removeAttribute("class"));t=this;t.tapCount++;c=setTimeout(function(){t.tapCount>=2&&t._isDoubleClickEvent==!1&&(t.sunburstDoubleClick(n),t._isDoubleClickEvent=!1);t.tapCount=0},200);f=document.getElementById(t._id+"toolbarTooltip");t.isNullOrUndefined(f)||f.parentNode.removeChild(f)},k.prototype.isTouch=function(n){var i,t;return(i=this.browserInfo(),t=n.originalEvent?n.originalEvent:n,t.pointerType=="touch"||t.pointerType==2||t.type.indexOf("touch")>-1)?!0:!1},k.prototype.sunburstLeave=function(n){var i=this,u=n.target.id,l,h,c,f,e,o,s,r,a,v,t;if(this._legendHighlight){for(l=document.getElementById(this._id+"legendItem_Group"),h=l.childNodes,r=0;r<h.length;r++)document.getElementById(h[r].id).style.opacity="1";for(c=document.querySelectorAll("[id^="+this._id+"_legendIndex]"),s=0;s<c.length;s++)document.getElementById(c[s].id).style.opacity="1";this._legendHighlight=!1}if(this.model.legend.visible&&this.model.highlightSettings.enable)for(r=0;r<this.model.points.length;r++)f=document.getElementById(this._id+"_svg_LegendItemShape"+r),f!=null&&(f.style.opacity="1",f.removeAttribute("class"));if(this.isNullOrUndefined(document.getElementById(i._id+"_ToolTip"))||(ej.isTouchDevice()||this.isTouch(n)?a=setTimeout(function(){document.getElementById(i._id+"_ToolTip").style.visibility="hidden"},3e3):u==i._id+"_svg_svgRect"&&(this._hideTooltip||clearTimeout(this._hideTooltip),this._hideTooltip=setTimeout(function(){document.getElementById(i._id+"_ToolTip").style.visibility="hidden"},1e3))),o=document.getElementById(i._id+"toolbarTooltip"),i.isNullOrUndefined(o)||o.parentNode.removeChild(o),e=document.getElementById("tooltipDiv"),i.isNullOrUndefined(e)||(ej.isTouchDevice()||this.isTouch(n)?v=setTimeout(function(){e.style.display="none"},3e3):u==i._id+"_svg_svgRect"&&(this._hideTooltipTemplate||clearTimeout(this._hideTooltipTemplate),this._hideTooltipTemplate=setTimeout(function(){e.style.display="none"},1e3))),u.indexOf(this._id+"reset")!=-1)for(t=0;t<5;t++)t==0?document.getElementById(this._id+"resetPath").setAttribute("fill","#D6D6D6"):document.getElementById(this._id+"resetPath"+t.toString()).setAttribute("fill","#5E5C5C");if(u.indexOf(this._id+"back")!=-1)for(t=0;t<3;t++)t==0?document.getElementById(this._id+"backPath").setAttribute("fill","#D6D6D6"):document.getElementById(this._id+"backPath"+t.toString()).setAttribute("fill","#5E5C5C")},k.prototype.selectSunburst=function(n,t){var l,d,a,v=this.model.selectionSettings.mode.toString(),e=document.getElementsByTagName("path"),g,s=this.model.selectionSettings.opacity.toString(),o=this.model.selectionSettings.type,p=this.model.legend,w=this.model.points,i,y,h,b,c,u,k,f;if(this.isNullOrUndefined(n)){if(r.opacity!=o)v=="point"?(h=document.getElementsByClassName("SelectionStyleSunburst"),h.length>0&&h[0].removeAttribute("class")):this.selectedgroupID.length>0&&(this.removeSelection(this.selectedgroupID),this.selectedgroupID=[],this.selectedGroupNumber=null,v=="child"&&(this.selelectedElementId=null),v=="parent"&&(this.groupNumber=null));else{for(this.selectedgroupID=[],f=0;f<e.length;f++)e[f].style.opacity="1";this.mouseClickedElementId="";this.selectedGroupNumber=null}this.removeLegendSelection()}else if(this.selectedgroupID.length>0&&this.removeSelection(this.selectedgroupID),v=="point"){if(this.selectedgroupID=[],g=t.target.id.replace("_layerIndex"+n.pointData[0].layerIndex.toString(),""),r.opacity!=o)!this.isNullOrUndefined(this.highlightgroupID)&&this.highlightgroupID.length>0&&this.removeHighlight(this.highlightgroupID),l=document.getElementById(n.pointData[0].id),l.getAttribute("class")=="SelectionStyleSunburst"?(l.removeAttribute("class"),this.highlightgroupID.indexOf(l.id)!=-1&&l.setAttribute("class","HighlightStyleSunburst")):(h=document.getElementsByClassName("SelectionStyleSunburst"),this.highlightgroupID.length>0&&h.length>0&&this.highlightgroupID.indexOf(h[0].id)!=-1&&h[0].setAttribute("class","HighlightStyleSunburst"),h.length>0&&h[0].getAttribute("class")=="SelectionStyleSunburst"&&h[0].removeAttribute("class"),!this.isNullOrUndefined(this.highlightgroupID)&&this.highlightgroupID.length>0&&this.removeHighlight(this.highlightgroupID),this.selectPointData(l),this.selectedgroupID.push(l.id));else{if(this.mouseClickedElementId!=g){for(u=0;u<e.length;u++)e[u].style.opacity=s;document.getElementById(n.pointData[0].id).style.opacity="1";this.mouseClickedElementId=g;this.selectedgroupID.push(n.pointData[0].id)}else{for(f=0;f<e.length;f++)e[f].style.opacity="1";this.mouseClickedElementId=""}this.selectedGroupNumber=n.pointData[0].groupNumber}if(p.visible&&this.selectedgroupID.length>0)for(c=0;c<w.length;c++)i=document.getElementById(this._id+"_svg_LegendItemShape"+c),n.pointData[0].legendIndex!=c?i!=null&&(i.style.opacity=s,i.removeAttribute("class")):r.opacity==o?i.style.opacity="1":(i.setAttribute("class","SelectionStyleSunburst"),i.style.opacity=s);this.selectedgroupID.length==0&&this.removeLegendSelection()}else if(v=="all")if(y=this.findGroupElements(n),a=n.pointData[0].id,this.selectedgroupID.indexOf(a)==-1){if(this.selectedGroupNumber!=y[0].groupNumber?(this.selectedgroupID.length>0&&this.removeSelection(this.selectedgroupID),this.selectGroup(y,n),this.selectedGroupNumber=y[0].groupNumber):(this.selectedgroupID.length>0&&this.removeSelection(this.selectedgroupID),this.selectedgroupID=[],this.selectedGroupNumber=null),r.opacity==o){for(u=0;u<e.length;u++)e[u].style.opacity=s;for(f=0;f<this.selectedgroupID.length;f++)document.getElementById(this.selectedgroupID[f]).style.opacity="1";this.isSelected=!0}if(p.visible){for(b=0;b<w.length;b++)i=document.getElementById(this._id+"_svg_LegendItemShape"+b),i!=null&&(i.style.opacity=s,i.removeAttribute("class"));r.opacity!=o?document.getElementById(this._id+"_svg_LegendItemShape"+y[0].pointIndex.toString()).setAttribute("class","SelectionStyleSunburst"):document.getElementById(this._id+"_svg_LegendItemShape"+y[0].pointIndex.toString()).style.opacity="1"}}else{if(this.selectedgroupID.length>0)if(r.opacity!=o)this.removeSelection(this.selectedgroupID);else for(u=0;u<e.length;u++)e[u].style.opacity="1";this.isSelected=!1;this.selectedGroupNumber=null;this.selectedgroupID=[];this.baseID="";this.removeLegendSelection()}else if(v=="child")if(a=n.pointData[0].id,this.selectedgroupID.indexOf(a)==-1){if(!this.isNullOrUndefined(this.selectedgroupID)&&this.selectedgroupID.length>0&&this.removeSelection(this.selectedgroupID),!this.isNullOrUndefined(this.highlightgroupID)&&this.highlightgroupID.length>0&&this.removeHighlight(this.highlightgroupID),this.selectedgroupID=[],this.selelectedElementId=undefined,this.selectchild(n),r.opacity==o){for(u=0;u<e.length;u++)e[u].style.opacity=s;for(f=0;f<this.selectedgroupID.length;f++)document.getElementById(this.selectedgroupID[f]).style.opacity="1";this.isSelected=!0}if(this.selectedGroupNumber=n.pointData[0].groupNumber,p.visible)for(c=0;c<w.length;c++)i=document.getElementById(this._id+"_svg_LegendItemShape"+c),n.pointData[0].legendIndex!=c?i!=null&&(i.style.opacity=s,i.removeAttribute("class")):r.opacity==o?i.style.opacity="1":(i.setAttribute("class","SelectionStyleSunburst"),i.style.opacity=s)}else this.selectedgroupID=[],this.selelectedElementId=this.baseID="",this.removeSegmentSelection(e,o),this.removeLegendSelection(),this.isSelected=!1,this.childrenElement=[];else if(a=n.pointData[0].id,this.selectedgroupID.indexOf(a)==-1){if(this.isNullOrUndefined(this.groupNumber)||this.groupNumber==n.pointData[0].groupNumber?this.selectedgroupID.length>0&&(this.removeSelection(this.selectedgroupID),this.selectedgroupID=[]):this.removeSegmentSelection(e,this.model.selectionSettings.type),!this.isNullOrUndefined(this.highlightgroupID)&&this.highlightgroupID.length>0&&this.removeHighlight(this.highlightgroupID),this.selectParent(n),this.groupNumber=n.pointData[0].groupNumber,r.opacity==o){for(u=0;u<e.length;u++)e[u].style.opacity=s;for(f=0;f<this.selectedgroupID.length;f++)document.getElementById(this.selectedgroupID[f]).style.opacity="1"}if(this.selectedGroupNumber=n.pointData[0].groupNumber,d=n.pointData[0].legendIndex,p.visible){for(k=0;k<w.length;k++)i=document.getElementById(this._id+"_svg_LegendItemShape"+k),i!=null&&(i.style.opacity=s,i.removeAttribute("class"));r.opacity!=o?document.getElementById(this._id+"_svg_LegendItemShape"+d.toString()).setAttribute("class","SelectionStyleSunburst"):document.getElementById(this._id+"_svg_LegendItemShape"+d.toString()).style.opacity="1"}}else this.selectedgroupID=[],this.selelectedElementId=this.baseID="",this.removeSegmentSelection(e,this.model.selectionSettings.type),this.removeLegendSelection(),this.baseElement=[]},k.prototype.removeSegmentSelection=function(n,t){for(var i=0;i<n.length;i++)t==r.opacity?n[i].style.opacity="1":n[i].removeAttribute("class")},k.prototype.removeLegendSelection=function(){var n,t;if(this.model.legend.visible)for(t=0;t<this.model.points.length;t++)n=document.getElementById(this._id+"_svg_LegendItemShape"+t),n!=null&&(n.style.opacity="1",n.removeAttribute("class"))},k.prototype.selectParent=function(n){var i,f,u,e=this.model.selectionSettings,o,t;for(this.baseElement=[],u=n.pointData[0].legendIndex,r.opacity==e.type&&(this.selectedgroupID=[]),this.baseElement.push(n.pointData[0]),o=n.pointData[0].id,r.opacity!=e.type&&(this.selectedgroupID=[],this.removeSelection(this.selectedgroupID),this.selelectedElementId=o),this.isNullOrUndefined(n.pointData[0].parentName)||this.findSelectionParent(n.pointData[0],u),t=0;t<this.baseElement.length;t++)document.getElementById("selectionSunburst")?(i=document.getElementById(this._id+"_legendIndex"+u+"_layerNumber"+this.baseElement[t].layerNumber.toString()+"_layerIndex"+this.baseElement[t].layerIndex.toString()+"_pointIndex"+this.baseElement[t].pointIndex),r.opacity!=this.model.selectionSettings.type&&i.setAttribute("class","SelectionStyleSunburst"),this.selectedgroupID.push(i.id)):(i=document.getElementById(this._id+"_legendIndex"+u+"_layerNumber"+this.baseElement[t].layerNumber.toString()+"_layerIndex"+this.baseElement[t].layerIndex.toString()+"_pointIndex"+this.baseElement[t].pointIndex),r.opacity!=this.model.selectionSettings.type&&(f=this.createselectionStyle(i,this.model.selectionSettings),document.body.appendChild(f),i.setAttribute("class","SelectionStyleSunburst")),this.selectedgroupID.push(i.id))},k.prototype.findSelectionParent=function(n,t){var f=this.sunburstRegions,e=f.length,r,u,i;n:for(u=0;u<e;u++)for(r=f[u],i=0;i<r.region.length;i++)if(this.isNullOrUndefined(this.model.dataSource)){if(n.parentName==r.region[i].x&&t==r.region[i].legendIndex)if(this.baseElement.push(r.region[i]),this.isNullOrUndefined(r.region[i].parentName))break n;else{this.findSelectionParent(r.region[i],r.region[i].legendIndex);break n}}else if(n.parentName==r.region[i].x&&r.region[i].parentChildName+"_"+n.x==n.parentChildName&&t==r.region[i].legendIndex)if(this.baseElement.push(r.region[i]),this.isNullOrUndefined(r.region[i].parentName))break n;else{this.findSelectionParent(r.region[i],r.region[i].legendIndex);break n}},k.prototype.selectchild=function(n){var u,e,t,f=this.model.selectionSettings,o,c,l,h,a,i,s;if(this.childrenElement=[],u=this.layerData[n.pointData[0].layerIndex].point[n.pointData[0].pointIndex],e=n.pointData[0].id,t=document.getElementById(e),u.point||u.layerIndex==0){for(a=n.pointData[0].layerNumber,this.isNullOrUndefined(u.point)||this.findChild(u,a),f.type!=r.opacity&&(document.getElementById("SelectionStyleSunburst")||(o=this.createselectionStyle(t,f),document.body.appendChild(o)),t.setAttribute("class","SelectionStyleSunburst")),this.selectedgroupID.push(t.id),this.selelectedElementId=e,c=n.pointData[0].legendIndex,i=0;i<this.childrenElement.length;i++)if(this.childrenElement[i].layerNumber>n.pointData[0].layerNumber)for(s=0;s<this.childrenElement[i].length;s++)l=this._id+"_legendIndex"+c+"_layerNumber"+this.childrenElement[i].layerNumber.toString()+"_layerIndex"+this.childrenElement[i].layerIndex+"_pointIndex"+s.toString(),h=document.getElementById(l),f.type!=r.opacity&&h.setAttribute("class","SelectionStyleSunburst"),this.selectedgroupID.push(h.id)}else f.type!=r.opacity&&(document.getElementById("selectionSunburst")||(o=this.createselectionStyle(t,f),document.body.appendChild(o)),t.setAttribute("class","SelectionStyleSunburst"),this.selelectedElementId=e),this.selectedgroupID.push(t.id)},k.prototype.findChild=function(n,t){for(var r=[],i=0;i<n.point.length;i++)n.point[i].point?(r.push(n.point[i]),this.findChild(n.point[i],t+1)):r.push(n.point[i]);r.layerNumber=t+1;r.layerIndex=n.point.layerData;this.childrenElement.push(r)},k.prototype.selectGroup=function(n,t){var s,f=this.model.selectionSettings,i,e,u,o,h;for(this.selectedgroupID=[],s=t.pointData[0].legendIndex,u=0;u<n.length;u++)if(n[u].layerNumber==1)i=document.getElementById(this._id+"_legendIndex"+s+"_layerNumber1_layerIndex0_pointIndex"+n[u].pointIndex.toString()),f.type!=r.opacity&&(e=this.createselectionStyle(i,f),document.body.appendChild(e),i.setAttribute("class","SelectionStyleSunburst")),this.selectedgroupID.push(i.id);else for(o=0;o<n[u].point.length;o++)i=document.getElementById(this._id+"_legendIndex"+s+"_layerNumber"+n[u].layerNumber.toString()+"_layerIndex"+n[u].layerIndex.toString()+"_pointIndex"+o.toString()),f.type!=r.opacity&&(h=document.getElementById("selectionSunburst"),h?i.setAttribute("class","SelectionStyleSunburst"):(e=this.createselectionStyle(i,f),document.body.appendChild(e),i.setAttribute("class","SelectionStyleSunburst"))),this.selectedgroupID.push(i.id)},k.prototype.removeSelection=function(n){for(var t=0;t<n.length&&document.getElementById(n[t])!=null;t++)document.getElementById(n[t]).getAttribute("class")=="SelectionStyleSunburst"&&document.getElementById(n[t]).removeAttribute("class")},k.prototype.findGroupElements=function(n){for(var i,u=n.pointData[0].groupNumber?n.pointData[0].groupNumber:n.pointData[0].pointIndex+1,r=[],t=0;t<this.layerData.length;t++)if(t==0)for(i=0;i<this.layerData[t].point.length;i++)this.layerData[t].point[i].groupNumber==u&&r.push(this.layerData[t].point[i]);else this.layerData[t].groupNumber==u&&(this.layerData[t].layerIndex=t,r.push(this.layerData[t]));return r},k.prototype.selectPointData=function(n){var t;document.getElementById("selectionSunburst")?(n.setAttribute("class","SelectionStyleSunburst"),this.selelectedElementId=n.id):(t=this.createselectionStyle(n,this.model.selectionSettings),document.body.appendChild(t),n.setAttribute("class","SelectionStyleSunburst"),this.selelectedElementId=n.id)},k.prototype.createselectionStyle=function(n,t){var i,r,u;return i=document.createElement("style"),i.type="text/css",r=t.color,u="1",i.innerHTML=" .SelectionStyleSunburst{ fill:"+r+";opacity:"+u+";stroke:;stroke-width:1}",i.setAttribute("id","selectionSunburst"),i},k.prototype.getSunburstPoint=function(n){var r,u,e,s,h,c,f,i,t,o;return f=this.calMousePosition(n),c=this.circularRadius,r=f.X,u=f.Y,i=this.model.startAngle,t=this.model.endAngle,s=this.areaBounds.actualWidth,h=this.areaBounds.actualHeight,o=this.model.startAngle*(Math.PI/180),this.sunburstRegions.forEach(function(n){var a=n.sunburstData,l=n.region,v,y,d,nt,p=0,h,f,s,w=[],g,b,c,k;if(d=a.innerRadius?a.innerRadius:0,v=r-a.centerX,y=u-a.centerY,nt=n.region,i=i<0?i+360:i,t=t<0?t+360:t,g=t-i,b=-.5*Math.PI+o,g<0){for(t=t/360,p=t?2*Math.PI*(t<0?1+t:t):0,h=(Math.atan2(y,v)-b-p)%(2*Math.PI),h<0&&(h=2*Math.PI+h),c=0;c<l.length;c++)if(f=parseFloat(l[c].startAngle.toFixed(14)),s=parseFloat(l[c].endAngle.toFixed(14)),f=f<0?2*Math.PI+f:f,s=s<0?2*Math.PI+s:s,f-=p,s-=p,f=parseFloat(f.toFixed(14)),s=parseFloat(s.toFixed(14)),h<=f&&h>=s){w.push(l[c]);break}}else for(h=(Math.atan2(y,v)-b)%(2*Math.PI),h<0&&(h=2*Math.PI+h),c=0;c<l.length;c++)if(f=parseFloat(l[c].startAngle.toFixed(14)),s=parseFloat(l[c].endAngle.toFixed(14)),f=f<0?2*Math.PI+f:f,s=s<0?2*Math.PI+s:s,f=parseFloat(f.toFixed(14)),s=parseFloat(s.toFixed(14)),h>=f&&h<=s){w.push(l[c]);break}w.length>0&&(k=Math.sqrt(Math.pow(Math.abs(v),2)+Math.pow(Math.abs(y),2)),k<=a.Radius&&k>d&&(e={pointData:w,location:{x:r,y:u}}))}),e},k.prototype.calMousePosition=function(n){var r,u,f,e,t,o,i,s,h;return t=this.mousePosition(n),f=t.x,e=t.y,o=document.getElementById(this._id+"_svg"),s=window.pageYOffset,h=window.pageXOffset,i=o.getBoundingClientRect(),r=f-h-i.left,u=e-s-i.top,{X:r,Y:u}},k.prototype.mousePosition=function(n){return!this.isNullOrUndefined(n.pageX)&&n.pageX>0?{x:n.pageX,y:n.pageY}:{x:0,y:0}},k.prototype.drawSunburstLegend=function(){var n=this.model.legend,t=this.model.legend.title,f,r=!1,u,e=this.elementSpacing;this.gLegendElement=this.createGroup({id:this._id+"_legend_group"});(n.position==i.top||n.postion==i.bottom)&&(f=this.height*(20/100));document.getElementById(this._id+"_svg").appendChild(this.gLegendElement);t.text!=""&&t.visible&&this.gLegendElement.appendChild(this.drawLegendTitle(t,n));u=this.drawlegendItem(n,this.legendCollection,r);this.drilldownCount==0&&(this.legendClick=this.legendClick.bind(this),this.legendItemGroup.addEventListener("click",this.legendClick,!0));this.gLegendElement.appendChild(u);this.legendPages.length>0&&this.drawScrollbuttons(n);this.legendPositioning(n,r)},k.prototype.legendPositioning=function(n,t){var u,c,l,o,p,e,a=this.elementSpacing,s,r,w,b,k,h,v,y;u=document.getElementById(this._id+"_legend_group");c=document.getElementById(this._id+"legendItem_Group");n.position==i.bottom||n.position==i.top?(l=document.getElementById(this._id+"legendItem_Group"),o=l.getBoundingClientRect(),p=o.width,e=this.width/2-p/2,s=n.position==i.bottom?this.isNullOrUndefined(n.size.height)?this.LegendBounds.Height-a:o.height+a:this.isNullOrUndefined(n.size.height)?0:this.LegendBounds.Height-o.top+a,r="translate("+e.toString()+",0)",l.setAttribute("transform",r),n.title.text!=""&&n.title.visible&&document.getElementById(this._id+"_LegendTitleText").setAttribute("transform",r),this.legendPages.length>0&&(w=document.getElementById(this._id).getBoundingClientRect(),e=this.width/2-document.getElementById("scrollButtons").getBoundingClientRect().width/2,n.position==i.bottom?s=o.bottom-w.top:n.position==i.top&&(s=o.top+o.height),r="translate("+e.toString()+","+s.toString()+")",document.getElementById("scrollButtons").setAttribute("transform",r))):n.position==i.left||n.position==i.right?(t||(h=void 0,v=document.getElementById(this._id+"_svg").getBoundingClientRect().height/2-u.getBoundingClientRect().height/2,n.position==i.left?r="translate(0,"+v.toString()+")":(this.isNullOrUndefined(n.size.width)?h=this.width-u.getBoundingClientRect().width-2*this.elementSpacing:(h=this.width-u.getBoundingClientRect().width-u.getBoundingClientRect().left-2*this.elementSpacing,h=h-n.size.width),r="translate("+h.toString()+","+v.toString()+")"),u.setAttribute("transform",r)),this.legendPages.length>0&&(o=u.getBoundingClientRect(),b=document.getElementById("scrollButtons").getBoundingClientRect(),e=o.width/2-b.width/2,s=o.height,r="translate("+e.toString()+","+s.toString()+")",document.getElementById("scrollButtons").setAttribute("transform",r))):n.title.visible&&n.title.text!=""&&(k=(c.getBoundingClientRect().width-document.getElementById(this._id+"_LegendTitleText").getBoundingClientRect().width)/2,r="translate("+k.toString()+",0)",document.getElementById(this._id+"_LegendTitleText").setAttribute("transform",r));(n.position==i.bottom||n.position==i.top)&&n.alignment!=f.center?n.alignment==f.near?(e=-Math.abs(c.getBoundingClientRect().left)+this.elementSpacing,r="translate("+e.toString()+"0)",u.setAttribute("transform",r)):n.alignment==f.far&&(e=Math.abs(c.getBoundingClientRect().right-c.getBoundingClientRect().width),e=e-(this.elementSpacing+this.model.margin.right),r="translate("+e.toString()+",0)",u.setAttribute("transform",r)):(n.position==i.left||n.position==i.right)&&n.alignment!=f.center&&(y=u.getAttribute("transform").split(" "),n.alignment==f.near?(e=u.getBoundingClientRect().left+this.elementSpacing,s=this.elementSpacing+this.model.margin.top,r=y[0]+","+s.toString()+")",u.setAttribute("transform",r)):(e=u.getBoundingClientRect().left+this.elementSpacing,s=this.areaBounds.actualHeight-u.getBoundingClientRect().height+this.elementSpacing+this.model.margin.bottom,r=y[0]+","+s.toString()+")",u.setAttribute("transform",r)))},k.prototype.drawScrollbuttons=function(n){var o=n.position,t,r,u,f,e;n.position!=i.float&&(r=this.createGroup({id:"scrollButtons"}),t={id:"scrollUp",d:"M 6 0 L 12 12 0 12 Z",fill:"#3E576F"},u=this.drawPath(t),r.appendChild(u),t={id:"scrollDown",d:"M 40.359375 0 L 52.359375 0 46.359375 12 Z",fill:"#3E576F"},u=this.drawPath(t),r.appendChild(u),t={id:"pageNumber",x:15,y:10,fill:n.font.color,"font-size":n.font.size,"font-style":n.font.fontStyle,"font-family":n.font.fontFamily,"font-weight":n.font.fontWeight,"text-anchor":"start"},e=this.currentpageNumber.toString()+"/"+this.totalpageNumbers,f=this.createText(t,e),r.appendChild(f),this.scrollClick=this.scrollClick.bind(this),r.addEventListener("click",this.scrollClick,!0));this.gLegendElement.appendChild(r)},k.prototype.scrollClick=function(n){var r=this.model.legend,o=n.target.id,u,f=this.currentpageNumber,s=this.totalpageNumbers,e,t;u=document.getElementById(this._id+"legendItem_Group");e=document.getElementById("pageNumber");o=="scrollDown"&&f!=s?(this.currentpageNumber=f+1,u.parentNode.removeChild(u),t=this.drawlegendItem(r,this.legendPages[this.currentpageNumber-1],!0),this.gLegendElement.appendChild(t),document.getElementById(this._id+"_svg").appendChild(this.gLegendElement),e.textContent=this.currentpageNumber.toString()+"/"+this.totalpageNumbers.toString(),this.legendClick=this.legendClick.bind(this),t.addEventListener("click",this.legendClick,!0)):o=="scrollUp"&&f!=1&&(this.currentpageNumber=f-1,u.parentNode.removeChild(u),t=this.drawlegendItem(r,this.legendPages[this.currentpageNumber-1],!0),this.gLegendElement.appendChild(t),document.getElementById(this._id+"_svg").appendChild(this.gLegendElement),e.textContent=this.currentpageNumber.toString()+"/"+this.totalpageNumbers.toString(),this.legendClick=this.legendClick.bind(this),t.addEventListener("click",this.legendClick,!0));(r.position==i.top||r.position==i.bottom)&&this.legendPositioning(r,!1)},k.prototype.drawLegendTitle=function(n,t){var h=n.text,f=this.measureText(n.text,n.font),s,e=this.elementSpacing,c=this.circularRadius[this.totallayerCount-1],r=0,u=0,o=n.font.color;o=this.isNullOrUndefined(o)?this.model.theme.toString().indexOf("dark")!=-1?"white":"black":o;t.position==i.bottom?u=this.areaBounds.actualHeight+f.height/2+e+this.yOffset:t.position==i.top?(u=e+this.yOffset+f.height/2,this.isNullOrUndefined(t.size.height)||(u=t.size.height+this.yOffset)):t.position==i.left||t.position==i.right?(u=e+this.model.margin.top,r-=e):(u=t.location.y,r=t.location.x);switch(n.textAlignment){case"far":r+=this.LegendBounds.Width-f.width/2;break;case"near":r+=f.width/2;break;case"center":r+=this.LegendBounds.Width/2}return this.legendTitleLocation={x:r,y:u,width:f.width,height:f.height},s={id:this._id+"_LegendTitleText",x:r,y:u,fill:o,"font-size":n.font.size,"font-style":n.font.fontStyle,"font-family":n.font.fontFamily,"font-weight":n.font.fontWeight,"text-anchor":"middle"},this.createText(s,n.text)},k.prototype.drawlegendItem=function(n,t,r){var f=0,e=0,a,s,h=this.model.legend.itemStyle,d,v,y=0,o,p,b,c,u,l,k;if(this.legendItemGroup=this.createGroup({id:this._id+"legendItem_Group"}),this.model.legend.position==i.bottom||this.model.legend.position==i.top)return this.drawBottomTopLegend(n,t);if(this.model.legend.position==i.left||this.model.legend.position==i.right)return this.drawLeftRightLegend(n,t,r);for(c=0,n.title.visible&&n.title.text!=" "?(a=this.measureText(n.title.text,n.title.font),e=n.location.y+a.height/2+this.elementSpacing):e=n.location.y,f=n.location.x,u=0;u<t.length;u++)f+=y+c,s=Math.sqrt(h.width*h.width+h.height*h.height)/2,d=t[u].visibility?t[u].fill:"grey",v=this.drawLegendShape(t[u],f,e,u),this.model.legendItemRendering&&(l=w.extend({},this.common),k={location:{x:f,y:e},text:t[u].Text,shape:t[u].sunburstLegendShape},l.data=k,this._trigger("legendItemRendering",l)),y=this.measureText(t[u].Text,t[u].font).width+s+this.model.legend.itemPadding,c=n.itemPadding,o=this.createGroup({id:this._id+"_svg_Legend"+u.toString()}),o.style.cursor="pointer",p={id:this._id+"_svg_LegendItemText"+u.toString(),x:f+s,y:e+s*3/4,fill:t[u].font.color,"font-size":t[u].font.size,"font-style":t[u].font.fontStyle,"font-family":t[u].font.fontFamily,"font-weight":t[u].font.fontWeight,"text-anchor":"start"},b=this.createText(p,t[u].Text),o.appendChild(v),o.appendChild(b),this.legendItemGroup.appendChild(o);return this.legendItemGroup},k.prototype.legendClick=function(n){var s,h,u,f=[],i={},l,e,r,t,o,c;if((this.rotateAnimate||this.model.enableAnimation)&&this.stopAnimation(),this.model.legend.clickAction==a.toggleSegmentVisibility){for(s=n.target.id,e=document.getElementById(s).parentNode,h=e.childNodes[1],l=e.childNodes[0],u=h.textContent,r=document.getElementById(this._id+"_svg"),r.parentNode.replaceChild(r.cloneNode(!1),r),t=0;t<this.legendCollection.length;t++)u==this.legendCollection[t].Text?this.legendCollection[t].visibility?(this.legendCollection[t].visibility=!1,i={x:this.legendCollection[t].x,parentChildName:this.legendCollection[t].x,y:this.legendCollection[t].y,fill:this.legendCollection[t].fill,text:this.legendCollection[t].Text,point:this.legendCollection[t].points}):(this.legendCollection[t].visibility=!0,f.push({x:this.legendCollection[t].x,parentChildName:this.legendCollection[t].x,y:this.legendCollection[t].y,fill:this.legendCollection[t].fill,text:this.legendCollection[t].Text,point:this.legendCollection[t].points})):this.legendCollection[t].visibility&&f.push({x:this.legendCollection[t].x,parentChildName:this.legendCollection[t].x,y:this.legendCollection[t].y,fill:this.legendCollection[t].fill,text:this.legendCollection[t].Text,point:this.legendCollection[t].points});this.model.legendItemClick&&(o=w.extend({},this.common),c={location:{x:i.x,y:i.y},text:u,point:i.point},o.data=c,this._trigger("legendItemClick",o));this.processData(f);this.legendClicked=!0;this.drawSunburst(this.layerData);this.legendClicked=!1}},k.prototype.drawLegendShape=function(n,t,i,r){var y=this.model.legend.itemStyle,e=y.width,u=y.height,p=n.visibility?n.fill:"grey",b=n.sunburstLegendShape,w="Path",k=n.visibility?this.model.legend.border.color:"grey",f,s=t+-e/2,d=i+-u/2,c,o,l,a,v,h;o={id:this._id+"_svg_LegendItemShape"+r.toString(),fill:p,stroke:k,"stroke-width":this.model.legend.border.width};switch(b){case"circle":w="Circle";c=Math.sqrt(u*u+e*e)/2;o.r=c;o.cx=t;o.cy=i;break;case"diamond":f="M "+s+" "+i+" L "+t+" "+(i+-u/2)+" L "+(t+e/2)+" "+i+" L "+t+" "+(i+u/2)+" L "+s+" "+i+" z";o.d=f;break;case"rectangle":f="M "+s+" "+(i+-u/2)+" L "+(t+e/2)+" "+(i+-u/2)+" L "+(t+e/2)+" "+(i+u/2)+" L "+s+" "+(i+u/2)+" L "+s+" "+(i+-u/2)+" z";o.d=f;break;case"triangle":f="M "+s+" "+(i+u/2)+" L "+t+" "+(i+-u/2)+" L "+(t+e/2)+" "+(i+u/2)+" L "+s+" "+(i+u/2)+" z";o.d=f;break;case"pentagon":for(l=72,c=Math.sqrt(u*u+e*e)/2,h=0;h<=5;h++)a=c*Math.cos(Math.PI/180*h*l),v=c*Math.sin(Math.PI/180*h*l),f=h===0?"M "+(t+a)+" "+(i+v)+" ":f.concat("L "+(t+a)+" "+(i+v)+" ");f=f.concat("Z");o.d=f;break;case"cross":f="M "+(t-e)+" "+i+" L "+(t+e)+" "+i+" M "+t+" "+(i+u)+" L "+t+" "+(i-u);o.d=f;o.stroke=p}return this["draw"+w](o)},k.prototype.drawBottomTopLegend=function(n,t){var ut=this.legendTitleLocation,v=this.model.margin,c=this.yOffset,o=0,tt=0,u=this.elementSpacing,it,f,y,s,l=this.model.legend.itemStyle,b=this.model.legend.font.color,k,h=0,e,d,g,r,p,nt;if(it=this.isNullOrUndefined(b)?this.model.theme.toString().indexOf("dark")!=-1?"white":"black":b,this.isNullOrUndefined(n.rowCount||n.columnCount))for(this.legendItemGroup=this.createGroup({id:this._id+"legendItem_Group"}),n.title.visible&&n.title.text!=" "?(y=this.measureText(n.title.text,n.title.font),f=n.position==i.bottom?this.areaBounds.actualHeight+y.height+2*u+c:c+y.height+2*u):f=(n.position=i.bottom)?u+v.top+2*this.circularRadius[this.totallayerCount-1]+c+u:u+v.top+c+u,r=0;r<t.length;r++)if(o+=u+h+tt,h=this.measureText(t[r].Text,t[r].font).width,o+u+h+tt>this.width&&(o=u,f+=this.measureText(t[r].Text,t[r].font).height),s=Math.sqrt(l.width*l.width+l.height*l.height)/2,b=t[r].visibility?t[r].fill:"grey",o+h>900&&(f=f+this.measureText(t[r].Text,t[r].font).height+u,o=u),this.findLegendPosition(s,o,f,n,t[r]),this.legendInRegion)this.model.legendItemRendering&&(p=w.extend({},this.common),nt={location:{x:o,y:f},text:t[r].Text,shape:t[r].sunburstLegendShape},p.data=nt,this._trigger("legendItemRendering",p)),e=this.createGroup({id:this._id+"_svg_Legend"+r.toString()}),e.style.cursor="pointer",k=this.drawLegendShape(t[r],o,f,r),e.appendChild(k),h=this.measureText(t[r].Text,t[r].font).width+s*2,d={id:this._id+"_svg_LegendItemText"+r.toString(),x:o+u,y:f+s*3/4,fill:it,"font-size":t[r].font.size,"font-style":t[r].font.fontStyle,"font-family":t[r].font.fontFamily,"font-weight":t[r].font.fontWeight,"text-anchor":"start"},tt=n.itemPadding,g=this.createText(d,t[r].Text),e.appendChild(g),this.legendItemGroup.appendChild(e);else{this.legendPaging(n,r,t);break}else{var ft=n.rowCount,et=n.columnCount,rt=t.length,a=10;for(n.title.visible&&n.title.text!=" "?(y=this.measureText(n.title.text,n.title.font),f=n.position==i.bottom?2*u+v.top+2*this.circularRadius[this.totallayerCount-1]+c+y.height:2*u+v.top+c+y.height):f=n.position==i.bottom?2*u+v.top+2*this.circularRadius[this.totallayerCount-1]+c:2*u+v.top+c,r=0;r<rt;r++)if(b=t[r].visibility?t[r].fill:"grey",e=this.createGroup({id:this._id+"_svg_Legend"+r.toString()}),s=Math.sqrt(l.width*l.width+l.height*l.height)/2,a+t[r].Bounds.Width>this.LegendBounds.Width+10&&(a=h=10,f+=u+t[r].Bounds.Height),this.findLegendPosition(s,a,f,n,t[r]),this.legendInRegion)this.model.legendItemRendering&&(p=w.extend({},this.common),nt={location:{x:a,y:f},text:t[r].Text,shape:t[r].sunburstLegendShape},p.data=nt,this._trigger("legendItemRendering",p)),h=this.measureText(t[r].Text,t[r].font).width+s*2,e=this.createGroup({id:this._id+"_svg_Legend"+r.toString()}),e.style.cursor="pointer",k=this.drawLegendShape(t[r],a,f,r),e.appendChild(k),h=this.measureText(t[r].Text,t[r].font).width+s*2,d={id:this._id+"_svg_LegendItemText"+r.toString(),x:a+u,y:f+s*3/4,fill:it,"font-size":t[r].font.size,"font-style":t[r].font.fontStyle,"font-family":t[r].font.fontFamily,"font-weight":t[r].font.fontWeight,"text-anchor":"start"},a+=h+u,g=this.createText(d,t[r].Text),e.appendChild(g),this.legendItemGroup.appendChild(e);else{this.legendPaging(n,r,t);break}}return this.legendItemGroup},k.prototype.drawLeftRightLegend=function(n,t,i){var c=0,rt=0,g,o=0,s,u,e=this.model.legend.itemStyle,y=this.model.legend.font.color,p,nt=0,f,b,k,a=0,tt=this.elementSpacing,ut=this.model.margin,r,l,d;if(g=this.isNullOrUndefined(y)?this.model.theme.toString().indexOf("dark")!=-1?"white":"black":y,this.isNullOrUndefined(n.rowCount||n.columnCount)){for(n.title.visible&&n.title.text!=" "&&(s=this.measureText(n.title.text,n.title.font),a=s.height+tt),o=a+tt+rt,c=tt+ut.left,r=0;r<t.length;r++)if(s=this.measureText(t[r].Text,t[r].font),u=Math.sqrt(e.width*e.width+e.height*e.height)/2,y=t[r].visibility?t[r].fill:"grey",this.findLegendPosition(u,c,o,n,t[r]),this.legendInRegion)this.model.legendItemRendering&&(l=w.extend({},this.common),d={location:{x:c,y:o},text:t[r].Text,shape:t[r].sunburstLegendShape},l.data=d,this._trigger("legendItemRendering",l)),p=this.drawLegendShape(t[r],c,o,r),nt+=s.width+u,f=this.createGroup({id:this._id+"_svg_Legend"+r.toString()}),b={id:this._id+"_svg_LegendItemText"+r.toString(),x:c+this.elementSpacing,y:o+u*3/4,fill:g,"font-size":t[r].font.size,"font-style":t[r].font.fontStyle,"font-family":t[r].font.fontFamily,"font-weight":t[r].font.fontWeight,"text-anchor":"start"},rt=n.itemPadding,o+=s.height/2+this.elementSpacing,k=this.createText(b,t[r].Text),f.appendChild(p),f.appendChild(k),this.legendItemGroup.appendChild(f);else{this.legendPaging(n,r,t);break}return this.legendItemGroup}var et=n.rowCount,ot=n.columnCount,ft=t.length,v=10,h=26,it=0;for(i||(this.LegendBounds.Height+=this.measureText(n.title.text,n.title.font).height),this.model.legend.title.visible&&this.model.legend.title.text!=" "&&(s=this.measureText(n.title.text,n.title.font),a=s.height),o=a+this.elementSpacing,c=this.elementSpacing+this.model.margin.left,r=0;r<ft;r++)if(y=t[r].visibility?t[r].fill:"grey",f=this.createGroup({id:this._id+"_svg_Legend"+r.toString()}),u=Math.sqrt(e.width*e.width+e.height*e.height)/2,h+t[r].Bounds.Height+n.itemPadding+a+2>this.LegendBounds.Height+26&&(h=26,v+=it+n.itemPadding),h+=t[r].Bounds.Height/2+n.itemPadding,it=Math.max(it,t[r].Bounds.Width),this.findLegendPosition(u,v,h,n,t[r]),this.legendInRegion)this.model.legendItemRendering&&(l=w.extend({},this.common),d={location:{x:v,y:h},text:t[r].Text,shape:t[r].sunburstLegendShape},l.data=d,this._trigger("legendItemRendering",l)),nt=this.measureText(t[r].Text,t[r].font).width+u*2,f=this.createGroup({id:this._id+"_svg_Legend"+r.toString()}),f.style.cursor="pointer",p=this.drawLegendShape(t[r],v,h,r),f.appendChild(p),nt=this.measureText(t[r].Text,t[r].font).width+u*2,b={id:this._id+"_svg_LegendItemText"+r.toString(),x:v+u,y:h+u*3/4,fill:g,"font-size":t[r].font.size,"font-style":t[r].font.fontStyle,"font-family":t[r].font.fontFamily,"font-weight":t[r].font.fontWeight,"text-anchor":"start"},k=this.createText(b,t[r].Text),f.appendChild(k),this.legendItemGroup.appendChild(f);else{this.legendPaging(n,r,t);break}return this.legendItemGroup},k.prototype.legendPaging=function(n,t,i){var u=this.legendCollection.length,o=t,f,r,e;for(this.legendPages=[],this.currentpageNumber=1,this.totalpageNumbers=t==0?u:u%t==0?Math.floor(u/t):Math.floor(u/t)+1,r=0;r<u;r=r+o){for(f=[],e=0;e<o;e++)r+e<u&&f.push(i[r+e]);if(o==0){f.push(i[r]);this.legendPages.push(f);break}this.legendPages.push(f)}},k.prototype.findLegendPosition=function(n,t,r,u,f){var e=this.elementSpacing,o,s=20/100;u.position==i.bottom?this.legendInRegion=n+r+e>this.height-e?!1:!0:u.position==i.top?this.legendInRegion=this.yOffset+s*this.height-this.elementSpacing-n>r?!0:!1:(u.position==i.left||u.position==i.right)&&(this.isNullOrUndefined(u.rowCount)&&this.isNullOrUndefined(u.columnCount)?this.legendInRegion=n+r+e>this.height-e?!1:!0:(o=this.measureText(f.Text,f.font),this.legendInRegion=s*this.width+e>t+n+o.width?!0:!1))},k.prototype.drawDatalabel=function(n){var y,a,v;for(this.dataLabelGroupEle=this.createGroup({id:this._id+"_svg_DataLabelGroup"}),y=0;y<n.length;y++){var r=n[y],t=void 0,u=void 0,e=void 0,nt=void 0,f=void 0,tt=void 0,it=void 0,rt=void 0,d=void 0,yt=void 0,g=void 0,p=void 0,b=void 0,ut=void 0,c=void 0,ft=void 0,et=void 0,pt=void 0,o=void 0,h=void 0,ot=void 0,st=void 0,ht=void 0,ct=void 0,k=void 0,lt=void 0,at=void 0,i=this.model,wt=this.circularRadius[0]-this.innerRadius[0],vt=void 0;for(a=0;a<r.point.length;a++)t=r.point[a],t.y!=null&&(k=r.layerNumber,u=t.text?t.text:i.dataSource?t.x:t.y.toString(),e=this.measureText(u,i.dataLabelSettings.font),nt=-.5*Math.PI,d=this.circularRadius[r.layerNumber-1],pt=e.height/2,o=this.circleCenterX,h=this.circleCenterY,f=t.midAngle+nt,ot=this.getXcordinate(o,d,f),st=this.getYcordinate(h,d,f),lt=this.getXcordinate(o,this.innerRadius[k-1],f),at=this.getYcordinate(h,this.innerRadius[k-1],f),vt={dMidX:lt,dMidY:at},r.layerNumber>1&&(ht=this.getXcordinate(o,this.circularRadius[r.layerNumber-2],f),ct=this.getYcordinate(h,this.circularRadius[r.layerNumber-2],f)),tt={midX:ot,midY:st},it={childMidX:ht,childMidY:ct},rt={midPoint:tt,childMidPoint:it,sunburstMidPoint:vt},et={startX:o,startY:h},yt={width:this.width,height:this.height},g=this.calculateLabelPosition(et,rt),p=g.positionX,b=g.positionY,ut="middle",c=this.textOptions(t,p,b,e,ut,r.layerNumber,a),this.isNullOrUndefined(i.dataLabelSettings.template)?(v=w.extend({},this.common),v.data={Text:u,location:{x:p,y:b},pointIndex:t.pointIndex},this._trigger("dataLabelRendering",v),i.dataLabelSettings.labelRotationMode==l.normal&&i.dataLabelSettings.labelOverflowMode!=s.none&&this.isNullOrUndefined(i.dataLabelSettings.template)?u=this.horizontalTrim(c,e.width,v.data.Text,o,h,t,this.model.dataLabelSettings.font,k-1):i.dataLabelSettings.labelRotationMode==l.angle&&i.dataLabelSettings.labelOverflowMode!=s.none&&this.isNullOrUndefined(i.dataLabelSettings.template)&&(u=this.rotatedTrim(c,e.width,v.data.Text,wt,t)),u!=""&&(ft=this.createText(c,u),this.dataLabelGroupEle.appendChild(ft))):this.labelTemplate(p,b,c,e,t))}return this.dataLabelGroupEle},k.prototype.labelTemplate=function(n,t,i,r,u){var l=this._id,o={interior:this.model.dataLabelSettings.fill,opacity:1,borderColor:"white",borderWidth:.1},s,f,e,h,c;s=!this.isNullOrUndefined(o)&&o.interior?o.interior:"transparent";e=document.getElementById(this._id+"_svg_DataLabelGroup");this.isNullOrUndefined(e)&&(e=document.createElement("div"),e.id=this._id+"_svg_DataLabelGroup",e.style.zIndex="5000",document.getElementById(this._id).appendChild(e));f=document.createElement("div");f.id="template_ele_"+this._id;this.clonenode=document.getElementById(this.model.dataLabelSettings.template).cloneNode(!0);this.clonenode.style.display="block";this.clonenode.innerHTML=this.parseTemplate(this.clonenode,u);f.style.backgroundColor=s;f.style.display="block";f.style.cursor="default";f.style.position="absolute";f.appendChild(this.clonenode);document.getElementById(this._id+"_svg_DataLabelGroup").appendChild(f);h=f.clientWidth;c=f.clientHeight;f.style.left=(i.x-h/2).toString()+"px";f.style.top=(i.y-c/2).toString()+"px"},k.prototype.parseTemplate=function(n,t){var i;return i=n.innerHTML,i.search("#point.x#")!=-1&&(i=i.replace("#point.x#",t.x.toString())),i.search("#point.y#")!=-1&&(i=i.replace("#point.y#",t.y.toString())),i.search("#point.text#")!=-1&&(i=this.isNullOrUndefined(t.text)?i.replace("#point.text#",t.y.toString()):i.replace("#point.text#",t.text.toString())),i},k.prototype.rotatedTrim=function(n,t,i,r){for(var u=this.model;r-u.segmentBorder.width<t;)if(u.dataLabelSettings.labelOverflowMode==s.trim)if(i=this.trimText(i,i.length,"..."),i=="..."){i="";break}else t=this.measureText(i,u.dataLabelSettings.font).width;else{i="";break}return i},k.prototype.trimText=function(n,t,i){var r,u;return t--,r=t-i.length,u=n.substr(0,r),u+i},k.prototype.horizontalTrim=function(n,t,i,r,u,f,e,o){var w=-.5*Math.PI,v,a,c,l,d="...",k,y,b,p=this.model,h;if(h=this.calculatePosition(n,t,r,u),v=p.startAngle,a=p.endAngle,k=a-v,v=(Math.atan2(h.rightEndY,h.rightEndX)-w)%(2*Math.PI),v<0&&(v=2*Math.PI+v),a=(Math.atan2(h.leftEndY,h.leftEndX)-w)%(2*Math.PI),a<0&&(a=2*Math.PI+a),c=parseFloat(f.start.toFixed(14)),l=parseFloat(f.end.toFixed(14)),k>0?(c=c,l=l):(c=c<0?2*Math.PI+c:c,l=l<0?2*Math.PI+l:l),v<0&&(a>0||a==null)&&(c=c<0?2*Math.PI+c:c,l=l<=0?2*Math.PI+l:l),y=this.circleCenterX<n.x?Math.sqrt(Math.pow(Math.abs(h.rightEndX),2)+Math.pow(Math.abs(h.rightEndY),2)):Math.sqrt(Math.pow(Math.abs(h.leftEndX),2)+Math.pow(Math.abs(h.leftEndY),2)),k<0&&(l=[c,c=l][0]),a>=c&&a<=l&&v>=c&&v<=l&&y<=this.circularRadius[o]-p.segmentBorder.width&&y>0)b=!0;else{if(p.dataLabelSettings.labelOverflowMode==s.hide)return"";for(b=!1;!b;){if(i=this.trimText(i,i.length,d),i==d){i="";break}t=this.measureText(i,p.dataLabelSettings.font).width;h=this.calculatePosition(n,t,r,u);v=(Math.atan2(h.rightEndY,h.rightEndX)-w)%(2*Math.PI);v<0&&(v=2*Math.PI+v);a=(Math.atan2(h.leftEndY,h.leftEndX)-w)%(2*Math.PI);a<0&&(a=2*Math.PI+a);y=this.circleCenterX<n.x?Math.sqrt(Math.pow(Math.abs(h.rightEndX),2)+Math.pow(Math.abs(h.rightEndY),2)):Math.sqrt(Math.pow(Math.abs(h.leftEndX),2)+Math.pow(Math.abs(h.leftEndY),2));a>=c&&a<=l&&v>=c&&v<=l&&y<=this.circularRadius[o]&&y>0&&(b=!0)}}return i},k.prototype.calculatePosition=function(n,t,i,r){var u,f,e,o;return u=n.x+t/2-i,f=n.y-r,e=n.x-t/2-i,o=n.y-r,{rightEndX:u,rightEndY:f,leftEndX:e,leftEndY:o}},k.prototype.textOptions=function(n,t,i,r,u,f,e){var s,a=0,v,o=this.model.dataLabelSettings.font,h=o.color,c,y;return this.model.dataLabelSettings.labelRotationMode==l.angle&&((n.midAngle>2*Math.PI||n.midAngle<0)&&(s=this.findAngle(n.midAngle)),a=n.midAngle<=Math.PI||!this.isNullOrUndefined(s)?this.isNullOrUndefined(s)?(n.startAngle-Math.PI/2+(n.endAngle-Math.PI/2))/2*(180/Math.PI):s<=Math.PI?(n.startAngle-Math.PI/2+(n.endAngle-Math.PI/2))/2*(180/Math.PI):(n.startAngle+n.endAngle)*90/Math.PI-270:(n.startAngle+n.endAngle)*90/Math.PI-270),v=this._colorNameToHex(n.fill),(this.isNullOrUndefined(h)||h=="")&&(c=this._hexToRgb(v),y=Math.round((parseInt(c.r)*299+parseInt(c.g)*587+parseInt(c.b)*114)/1e3),h=y>=128?"black":"white"),{id:this._id+"_legendIndex"+n.legendIndex+"_layerNumber"+f.toString()+"_pointIndex"+e.toString(),x:t,y:i+r.height/4,fill:h,"font-size":o.size,"font-family":o.fontFamily,"font-style":o.fontStyle,"font-weight":o.fontWeight,opacity:o.opacity,"text-anchor":u,cursor:"default",transform:"rotate("+a+","+t+","+i+")"}},k.prototype._colorNameToHex=function(n){var t=n,i={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4","indianred ":"#cd5c5c","indigo ":"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370d8",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#d87093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};return Object.prototype.toString.call(t)=="[object Array]"?t:typeof i[t.toLowerCase()]!="undefined"?i[t.toLowerCase()]:t},k.prototype._hexToRgb=function(n){var t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(n);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:null},k.prototype.findAngle=function(n){var t;if(n>0)if(t=n%(2*Math.PI),t>2*Math.PI)this.findAngle(t);else return t;else return 2*Math.PI+n},k.prototype.calculateLabelPosition=function(n,t){var i,r,u,f,e,o,c,l,s,h;return u=t.midPoint.midX,f=t.midPoint.midY,e=t.childMidPoint.childMidX,o=t.childMidPoint.childMidY,c=n.startX,l=n.startY,s=t.sunburstMidPoint.dMidX,h=t.sunburstMidPoint.dMidY,this.isNullOrUndefined(e)?(i=(u+s)/2,r=(f+h)/2):(i=(u+e)/2,r=(f+o)/2),{positionX:i,positionY:r}},k.prototype.drawPath=function(n){var t=document.getElementById(n._id);return t===null&&(t=this.createElement("path")),this.setAttributes(n,t),t},k.prototype.sunburstRender=function(n){var i,r,t;for(this.circleCenterX=[],this.circleCenterY=[],this.circularRadius=[],this.sunburstRegions=[],this.startX=[],this.startY=[],this._visiblePoints=n,this.calculateSliceAngle(n[0].point),i=2;i<=this.totallayerCount;i++)for(t=1;t<n.length;t++)i==n[t].layerNumber&&n[t].point.length>0&&this.calculateChildAngle(n[t]);for(this.circularRadius=this.calculateRadius(this.areaBounds.actualWidth,this.areaBounds.actualHeight,this.totallayerCount),this._drillInnerRadius.length==0?this.circularRadius=this.calculateRadius(this.areaBounds.actualWidth,this.areaBounds.actualHeight,this.totallayerCount):(this.circularRadius=this._drillOuterRadius[this.drilldownCount-1],this.innerRadius=this._drillInnerRadius[this.drilldownCount-1]),this.gSeriesGroupEle=this.createGroup({id:this._id+"_svg_SunBurstElementGroup"}),r=this.totallayerCount;r>0;r--)for(t=0;t<n.length;t++)r==n[t].layerNumber&&this.drawRegion(n[t],t);return this.sunburstDoubleClick=this.sunburstDoubleClick.bind(this),this.gSeriesGroupEle.addEventListener("dblclick",this.sunburstDoubleClick,!0),this.gSeriesGroupEle},k.prototype.calculateArcData=function(n,t,i){var b,c,r,u,a,o,k,v,f,e,s,y,h,p,w,d,g;b=this.model.points;c=-.5*Math.PI;r=n.startAngle+c;u=n.endAngle+c-1e-6;a=this.model.endAngle-this.model.startAngle;o=u-r<Math.PI?0:1;k=(r+u)/2;n.currentMidAngle==undefined&&(n.currentMidAngle=(r+u)/2);s=a>0?1:0;o=s?u-r<Math.PI?0:1:u-r>-1*Math.PI?0:1;f=this.circleCenterX;e=this.circleCenterY;y=f+i*Math.cos(r);p=e+i*Math.sin(r);h=f+i*Math.cos(u);w=e+i*Math.sin(u);var nt=f+this.innerRadius[t-1]*Math.cos(r),tt=e+this.innerRadius[t-1]*Math.sin(r),l=f+this.innerRadius[t-1]*Math.cos(u),it=e+this.innerRadius[t-1]*Math.sin(u),rt=s?0:1;return r<0&&Math.round(n.endAngle-n.startAngle)==6&&(l=l-.01,h=h-.01),v="M "+y+" "+p+" A "+i+" "+i+" 0 "+o+" "+s+" "+h+" "+w+" L "+l+" "+it+" A "+this.innerRadius[t-1]+" "+this.innerRadius[t-1]+" 1 "+o+" "+rt+" "+nt+" "+tt+" z",{Direction:v,centerX:d,centerY:g}},k.prototype.findParent=function(n){for(var i,r,f,e=n.layerNumber-1,u,t=0;t<this._visiblePoints.length;t++)if(e==this._visiblePoints[t].layerNumber)for(i=0;i<this._visiblePoints[t].point.length;i++)if(this._visiblePoints[t].point[i].x==n.parentName&&this._visiblePoints[t].point[i].legendIndex==n.legendIndex){if(this.isNullOrUndefined(this.model.dataSource))return this._visiblePoints[t].point[i];for(r=0;r<n.point.length;r++)if(u=this._visiblePoints[t].point[i].parentChildName,f=n.point[r].parentChildName.length-n.point[r].x.length-1,n.point[r].parentChildName.substr(0,f)==u)return this._visiblePoints[t].point[i]}},k.prototype.drawRegion=function(n,t){var l,a,tt,i,f,k,d=[],g,it,e=this.model,v,y,o=e.segmentBorder.color,p,s,h,rt,b,u,c,nt;for(v=this.model.startAngle*(Math.PI/180),h=e.selectionSettings,o=this.isNullOrUndefined(o)?this.model.theme.toString().search("dark")!=-1?"black":"white":o,u=0;u<n.point.length;u++)if(s=!1,y=e.opacity,!isNaN(n.point[u].startAngle)){for(i=n.point[u],rt=i.fill,l=this.calculateArcData(i,n.layerNumber,this.circularRadius[n.layerNumber-1]),tt=l.Direction.split(" "),it=this.circularRadius[n.layerNumber-1],i.layerIndex=t,f=this._id+"_legendIndex"+i.legendIndex+"_layerNumber"+n.layerNumber.toString()+"_layerIndex"+t.toString()+"_pointIndex"+u.toString(),c=0;c<this.selectedgroupID.length;c++)f==this.selectedgroupID[c]&&(s=!0);h.type==r.opacity&&!s&&this.selectedgroupID.length>0&&(y=h.opacity);i.id=f;a={id:f,fill:i.fill,"stroke-width":e.segmentBorder.width,"stroke-dasharray":"",d:l.Direction,stroke:o,"stroke-linecap":"butt","stroke-linejoin":"round",opacity:y,visibility:this._enableAnimation?"hidden":"visible"};p=w.extend({},this.common);p.data={point:i,pathOptions:a};this._trigger("segmentRendering",p);b=this.drawPath(a);s&&h.type==r.color&&this.selectPointData(b);this.gSeriesGroupEle.appendChild(b);k={x:i.x,y:i.y,fill:i.fill,text:i.text,layerNumber:n.layerNumber,layerIndex:t,parentName:n.parentName,pointIndex:u,startAngle:i.startAngle-v,endAngle:i.endAngle-v,startX:this.startX,startY:this.startY,groupNumber:n.groupNumber,id:f,legendIndex:i.legendIndex,parentChildName:i.parentChildName};d.push(k)}g={Radius:this.circularRadius[n.layerNumber-1],centerX:this.circleCenterX,centerY:this.circleCenterY,innerRadius:this.innerRadius[n.layerNumber-1]};nt={sunburstData:g,region:d};this.sunburstRegions.push(nt)},k.prototype.calculateRadius=function(n,t,i){var r=0,f=[],s,e=this.model.radius,c=Math.min(n,t),o,h=this.model.innerRadius,u;for(this.innerRadius=[],s=e>1?1:e<0?0:e,o=c/2*s,h!=0?this.innerRadius.push(h*o):this.innerRadius.push(0),u=0;u<i;u++)u==0?(r=(o-this.innerRadius[0])/i+this.innerRadius[0],f[u]=r):(this.innerRadius.push(r),r=r-this.innerRadius[u-1]+r,f[u]=r);return f},k.prototype.calculateSliceAngle=function(n){for(var v=n.length,o=0,u=360,t,s=this.model.margin,y,p,h,c=0,w,b,f,d,l=0,a=0,k=this.model.border.width,r=this.model.legend,e=0;e<v;e++)n[e].y=this.isNullOrUndefined(n[e].y)?this.isNullOrUndefined(n[e].point)?0:this.calculateSumofY(n[e],o):n[e].y,o+=n[e].y;for(this.model.startAngle>this.model.endAngle&&(this.model.endAngle==0?this.model.endAngle=360:this.model.endAngle<0&&(this.model.endAngle=360+this.model.endAngle)),u=this.model.endAngle!=null&&this.model.startAngle<this.model.endAngle?this.model.endAngle-this.model.startAngle:this.model.startAngle>this.model.endAngle&&!this.isNullOrUndefined(this.model.endAngle)?360-this.model.startAngle+this.model.endAngle:360,u=u!=360&&u!=-360?u%360:u,this.model.startAngle?(this.model.startAngle=this.model.startAngle%360,this.model.endAngle=this.model.startAngle+u,c=this.model.startAngle?o/360*this.model.startAngle:0,c=c/o):this.model.endAngle=u,r.visible&&(r.position==i.right||r.position==i.left?(l+=this.elementSpacing+this.LegendBounds.Width,this.isNullOrUndefined(r.size.width)||(l=parseFloat(r.size.width))):(r.position==i.bottom||r.position==i.top)&&(a+=this.elementSpacing+this.LegendBounds.Height,this.isNullOrUndefined(r.size.height)||(a=parseFloat(r.size.height)))),y=this.height-(a+s.top+s.bottom+k*2+this.yOffset),p=this.width-(l+s.left+s.right+k*2),this.circleCenterX=p*.5+s.left+(r.visible&&r.position==i.left?l:0),this.circleCenterY=y*.5+s.top+this.yOffset+(r.visible&&r.position==i.top?a:0),u=this.model.endAngle-this.model.startAngle,b=u/180,f=0;f<v;f++)t=n[f],t.startAngle=f==0?2*Math.PI*c:h,h=t.endAngle=b*Math.PI*(t.y/o)+t.startAngle,h=t.endAngle=isNaN(h)?t.startAngle:h,t.start=t.startAngle,t.end=t.endAngle,t.midAngle=(t.endAngle+t.startAngle)/2,t.pointIndex=f,w=-.5*Math.PI,t.radian=t.midAngle/2%(2*Math.PI),c+=t.y/o,d=t.midAngle+w,this.startX[f]=this.circleCenterX,this.startY[f]=this.circleCenterY},k.prototype.calculateTotaldegree=function(n,t){var i=0;return n<t?i=t-n:n>t&&(i=360-n+t),i},k.prototype.calculateSumofY=function(n,t){for(var r=n.point,i=0;i<r.length;i++)this.isNullOrUndefined(r[i].y)?this.isNullOrUndefined(r[i].point)||(r[i].y=this.calculateSumofY(r[i],t),t+=r[i].y):t+=r[i].y;return t},k.prototype.calculateChildAngle=function(n){for(var i,u,o,f=0,s,t,r=this.findParent(n),e=0;e<n.point.length;e++)f=f+n.point[e].y;for(s=r.y,u=i=r.startAngle,o=r.endAngle-r.startAngle,t=0;t<n.point.length;t++)t==0?(n.point[t].startAngle=u,n.point[t].start=u,i=n.point[t].y/f*o+u,n.point[t].end=n.point[t].endAngle=i,n.point[t].midAngle=(n.point[t].startAngle+n.point[t].endAngle)/2):(n.point[t].startAngle=i,n.point[t].start=i,i=n.point[t].y/f*o+i,n.point[t].end=n.point[t].endAngle=i,n.point[t].midAngle=(n.point[t].startAngle+n.point[t].endAngle)/2)},k.prototype.calculateSize=function(n,t){var o,s,u=this.model.border.width,i,f,h,c,l,r=this.model.margin,e=this.elementSpacing;return i=this._getLegendSpace(this.model.legend),f=i.leftLegendWidth+u+r.left,h=i.rightLegendWidth+r.right+r.left+2*u,c=n.width,o=c-(f+h),l=r.top+e+i.modelTitleHeight+i.modelsubTitleHeight+i.topLegendHeight+u,this.yOffset=t.text&&t.visible?this.titleLocation.size.height+e+(t.subtitle.text==""?0:t.subtitle.text&&t.subtitle.visible?this.subTitleLocatation.size.height:0):0,s=n.height-(i.modelTitleHeight+e+r.top+i.modelsubTitleHeight+i.topLegendHeight+i.bottomLegendHeight),{x:f,y:l,actualWidth:o,actualHeight:s}},k.prototype._getLegendSpace=function(n){var l=this.LegendActualBounds,f=this.elementSpacing,k=this.model.legend.border.width,e=0,r=0,u=0,o=0,a,c=this.model.legend.position,v=0,y=0,p=0,w,b,s,h,t=this.model.title;return f=this.legendClicked?0:f,n.visible&&(a=parseFloat(l.Width)+5+f+2*k,h=this.width*(20/100),e=c==i.left?a:0,e>h&&this.isNullOrUndefined(n.size.width)&&(e=h),r=c==i.right?a:0,r>h&&this.isNullOrUndefined(n.size.width)&&(r=h),s=this.height*(20/100),v=n.title.visible?this.measureText(n.title.text,n.title.font).height:0,u=c==i.top?f+parseFloat(l.Height)+v:0,o=c==i.bottom?f+parseFloat(l.Height)+v:0,u>s&&this.isNullOrUndefined(n.size.height)?u=s:o>s&&this.isNullOrUndefined(n.size.height)&&(o=s),this.LegendActualBounds.Height=u==0&&o!=0?o:u!=0?u:this.LegendActualBounds.Height,this.LegendActualBounds.Width=r==0&&e!=0?e:r!=0?r:this.LegendActualBounds.Width),y=this.measureText(t.text,t.font).height,p=this.measureText(t.subtitle.text,t.subtitle.font).height,w=t.text==""||!t.visible?0:y+f,b=t.subtitle.text==""||!t.subtitle.visible?0:p,{leftLegendWidth:e,rightLegendWidth:r,topLegendHeight:u,bottomLegendHeight:o,modelTitleHeight:w,modelsubTitleHeight:b}},k.prototype.calculateLegendBounds=function(){var e,rt,k,w;this.legendCollection=[];this.legendRegion=[];this.legendTextRegion=[];this._legendMaxHeight=0;this._legendMaxWidth=0;var r=this.model.legend,h=10,et=r.size.height,ot=r.size.width,n=r.itemPadding>0?r.itemPadding:0,c=r.position.toLowerCase(),u=0,f=0,vt=this.height,yt=this.width,l=0,a=0,t=0,s=0,d=1,st={},v=[],g=this.model.legend.rowCount,nt=this.model.legend.columnCount,tt=this.elementSpacing,it=this.measureText(r.title.text,r.title.font),ht=r.border.width,ct=this.model.border.width,pt=vt-(tt*4+ct*2+ht*2),wt=yt-(tt*4+ct*2+ht*2),o=[],lt,bt,ut,b,y,at,i,kt,p,ft;if(r.visible){for(o=this.layerData[0].point,lt=o.length,e=0;e<lt;e++)kt=o[e],bt=o[e].fill,ut=o[e].x,st={sunburstLegendShape:r.shape,Text:ut,displayText:ut,font:r.font,groupNumber:o[e].groupNumber,layerIndex:o[e].layerIndex,layerNumber:o[e].layerNumber,Shape:"None",visibility:!0,fill:o[e].fill,points:o[e].point,x:o[e].x,y:o[e].y},v.push(st);for(b=v.length,rt=0;rt<b;rt++)y=v[rt],at=r.itemStyle.width,i=this._getLegendSize(y),l=Math.max(this._legendMaxWidth>0?this._legendMaxWidth+n+at:i.Width,l),a=Math.max(i.Height,a);for(s=a+tt*2,t=l,(g||nt)&&(ft=this.legendItemBounds(b,l+n,a+n),t=ft.LegendWidth,s=ft.LegendHeight,c==="top"||c==="bottom"||c==="custom"?s=s-n+tt:t=t-n),k=0;k<b;k++)y=v[k],i=this._getLegendSize(y),p=g||nt?{Width:l,Height:a}:i,p._Width=i.Width,v[k].Bounds=p,this.legendCollection.push(y);for(w=0;w<b;w++)y=v[w],i=this._getLegendSize(y),g||nt||(c=="top"||c=="bottom"||c=="custom"?(u+=i.Width+n,u>wt&&w!=0?(u-=i.Width+n,t=Math.max(t,u),u=i.Width+n,d++,s+=a+n):t=Math.max(t,u),f=Math.max(f,a)):(f+=i.Height+n,f>pt?(f-=i.Height+n,s=Math.max(s,f),f=i.Height+n,t+=l+n):s=Math.max(s,f),u=Math.max(u,l))),p=g||nt?{Width:l,Height:a}:i,p._Width=i.Width,v[w].Bounds=p;c==="top"||c==="bottom"||c==="custom"?(t=it.width>t-n?it.width+h*2+n:t+h*2,u+=h,f+=h*2,this.LegendBounds={Width:Math.max(t,u)-n,Height:Math.max(s,f),Rows:d}):(t=it.width>t-n?it.width+h*2+n:t+h*2,u+=h,f+=h*2,this.LegendBounds=r.rowCount==null&&r.columnCount==null?{Width:u,Height:f,Columns:d}:{Width:Math.max(t,u),Height:Math.max(s,f)+h-n,Columns:d})}else this.LegendBounds={Width:0,Height:0};this.LegendActualBounds=this.LegendBounds;ot!=null&&(this.LegendActualBounds.Width=ot);et!=null&&(this.LegendActualBounds.Height=et)},k.prototype._getLegendSize=function(n){var r=this.model.legend,t=r.itemStyle,i=this.measureText(n.Text,n.font),u=t.width+10+i.width,f=Math.max(t.height,i.height);return{Width:u,Height:f}},k.prototype.legendItemBounds=function(n,t,i){var r=this.model.legend,s,h=r.position,c=r.itemPadding,u,e,o=0,f=0;return this.isNullOrUndefined(r.columnCount)&&r.rowCount?(u=r.rowCount,e=Math.ceil(n/u),o=t*e,f=i*u):this.isNullOrUndefined(r.rowCount)&&r.columnCount?(u=Math.ceil(n/r.columnCount),s=r.columnCount,o=t*s,f=i*u):r.rowCount&&r.columnCount&&(r.columnCount<r.rowCount?(u=r.rowCount,e=Math.ceil(n/u),o=t*e,f=i*u):r.columnCount>r.rowCount?h==="top"||h==="bottom"||h==="custom"?(u=Math.ceil(n/r.columnCount),s=r.columnCount,o=t*s,f=i*u):(u=Math.ceil(n/r.columnCount),e=Math.ceil(n/u),o=t*e,f=i*u):h==="top"||h==="bottom"?(u=Math.ceil(n/r.columnCount),s=Math.ceil(n/r.rowCount),o=t*r.columnCount,f=i*s):(u=r.rowCount,e=Math.ceil(n/u),o=t*e,f=i*u)),f+=this.elementSpacing,{LegendWidth:o,LegendHeight:f}},k.prototype.drawTitle=function(n,t){var r,o,s,h,c,i,e=this.elementSpacing,u=n.font.color;return u=this.isNullOrUndefined(u)?this.model.theme.toString().indexOf("dark")!=-1?"white":"black":u,r=this.measureText(n.text,n.font),o=n.textAlignment==f.center?t.width/2-r.width/2:n.textAlignment==f.near?2*e:t.width-r.width-2*e,s=r.height/2+e,i=w.extend({},this.common),i.data={TitleText:n.text,location:{X:o,Y:s},size:r},this._trigger("titleRendering",i),h={id:this._id+"SunburstTitle",x:i.data.location.X,y:i.data.location.Y,fill:u,"font-size":n.font.size,"font-family":n.font.fontFamily,"font-style":n.font.fontStyle,"font-weight":n.font.fontWeight,opacity:n.font.opacity,"text-anchor":"start"},this.gTitleGroupElement=this.createGroup({id:this._id+"_Sunburst_Title"}),c=this.createText(h,i.data.TitleText),this.gTitleGroupElement.appendChild(c),this.titleLocation={size:r,x:i.data.location.X,y:i.data.location.Y},n.subtitle.text!=""&&n.subtitle.visible&&this.drawSubtitle(n,t,r,this.titleLocation),this.gTitleGroupElement},k.prototype.drawSubtitle=function(n,t,i,r){var s,e,o,h,c,l,u=n.subtitle.font.color;u=this.isNullOrUndefined(u)?this.model.theme.toString().indexOf("dark")!=-1?"white":"black":u;s=this.measureText(n.subtitle.text,n.subtitle.font);e=n.subtitle.textAlignment==f.far?r.x+r.size.width:n.subtitle.textAlignment==f.near?r.x:r.x+i.width/2;o=r.size.height+2*this.elementSpacing;l=n.subtitle.textAlignment==f.far?"end":n.subtitle.textAlignment==f.near?"start":"middle";h={id:this._id+"SunburstSunbtitle",x:e,y:o,fill:u,"font-size":n.subtitle.font.size,"font-family":n.subtitle.font.fontFamily,"font-style":n.subtitle.font.fontStyle,"font-weight":n.subtitle.font.fontWeight,opacity:n.subtitle.font.opacity,"text-anchor":l};this.subTitleLocatation={size:s,x:e,y:o};c=this.createText(h,n.subtitle.text);this.gTitleGroupElement.appendChild(c)},k.prototype.measureText=function(n,t){var r=document.querySelectorAll("#measureTex"),i;return r.length==0?(i=document.createElement("text"),i.setAttribute("id","measureTex"),document.body.appendChild(i)):(i=r[0],i.style.display="block"),i.innerHTML=n,i.style.fontSize=t.size,i.style.fontFamily=t.fontFamily,i.style.fontWeight=t.fontWeight,i.style.backgroundColor="white",i.style.position="absolute",i.style.visibility="hidden",i.style.whiteSpace="nowrap",{width:i.offsetWidth,height:i.offsetHeight}},k.prototype.drawContainerRect=function(){var n;return n={id:this._id+"_svg_svgRect",x:0,y:0,width:this.width,height:this.height,fill:this.model.background||"transparent",stroke:this.model.border.color,"stroke-width":this.model.border.width,opacity:"0.5"},this.drawRect(n)},k.prototype.processData=function(n){var r=n.length,i,t;for(this.findBaseData(n),t=0;t<n.length;t++)i=n[t],i.legendIndex=t,n[t].point&&this.findLayerData(n[t].point,n[t].x,1,n[t].groupNumber,t)},k.prototype.findBaseData=function(n){var i=[],t;for(this.layerData=[],t=0;t<n.length;t++)i.push(n[t]),i[t].groupNumber=t+1,i[t].layerIndex=0,i[t].layerNumber=1,i[t].legendIndex=t,n[t].groupNumber=t+1;this.layerData.push({point:i,layerNumber:1,legendIndex:0});this.totallayerCount=1},k.prototype.findLayerData=function(n,t,i,r,u){for(var e=[],f=0;f<n.length;f++)n[f].point&&n[f].point.length>0?(n[f].legendIndex=u,this.findLayerData(n[f].point,n[f].x,i+1,r,u),n[f].layerIndex=this.layerData.length,e.push(n[f])):(n[f].legendIndex=u,n[f].layerIndex=this.layerData.length,e.push(n[f]));n.layerData=this.layerData.length;this.layerData.push({point:e,parentName:t,layerNumber:i+1,groupNumber:r,layerIndex:this.layerData.length,legendIndex:u});i+1>this.totallayerCount&&(this.totallayerCount=i+1)},k.prototype.setColors=function(n){for(var r=["#3082bd","#e55725","#9cbb59","#F6851F","#8165a3","#4fa045","#d456a0","#0dbdef","#e2a325","#03a09c"],i=this.model.palette,t=0;t<n.length;t++)this.isNullOrUndefined(n[t].fill)||this._sunburstRedraw?(this.isNullOrUndefined(i)||i.length!=0?(n[t].fill=this.isNullOrUndefined(i)?r[t%r.length]:i[t%i.length],n[t].fill.trim().length==0&&(n[t].fill="black")):n[t].fill="black",this.isNullOrUndefined(n[t].point)||this.setchildColors(n[t].point,n[t].fill)):this.isNullOrUndefined(n[t].point)||(n[t].fill.trim().length==0&&(n[t].fill="black"),this.setchildColors(n[t].point,n[t].fill))},k.prototype.setchildColors=function(n,t){t=t.trim().length==0?"black":t;for(var i=0;i<n.length;i++)this.isNullOrUndefined(n[i].fill)||this._sunburstRedraw?(n[i].fill=t,n[i].fill.trim().length==0&&(n[i].fill="black"),this.isNullOrUndefined(n[i].point)||this.setchildColors(n[i].point,t)):this.isNullOrUndefined(n[i].point)||this.setchildColors(n[i].point,t)},k.prototype.processDataSource=function(n){var h=this.model.levels.length,u=[],f=0,e,o,t,s,i,r;for(this.model.points=[],this.parentNames=[],this.levelNames=[],this.childProcess=undefined,i=0;i<n.length;i++)if(e=n[i],o=this.model.levels[f].groupMemberPath,t=e[o],s=w.inArray(t,u),s<0)this.model.points.push({x:t,y:n[i][this.model.valueMemberPath],layer:f+1,parentName:t,parentChildName:t}),u.push(t);else for(r=0;r<this.model.points.length;r++)this.model.points[r].x==t&&(this.model.points[r].y+=n[i][this.model.valueMemberPath]);this.model.levels.length>1&&this.createOtherLayerPoints(n,1)},k.prototype.createOtherLayerPoints=function(n,t){for(var o,e,i=n,s=this.model.levels[t].groupMemberPath,h,c=this.model.levels,f=this.model.points,r=0;r<i.length;r++){var u="",l="",v=0,a=void 0,y=void 0,p=void 0,b=void 0;for(o=0;o<=t;o++){if(y=c[o].groupMemberPath,l=i[r][y],this.isNullOrUndefined(l)||(u+=l+"_"),v==t){u=u.substring(0,u.length-1);break}v++}if(p=w.inArray(u,this.levelNames),b=c[t-1].groupMemberPath,a=i[r][b],p<0&&!this.isNullOrUndefined(i[r][s]))this.levelNames.push(u),h=i[r][this.model.valueMemberPath],f.push({x:i[r][s],y:h,layer:t+1,parentChildName:u,parentName:a});else if(!this.isNullOrUndefined(i[r][s]))for(e=0;e<f.length;e++)f[e].parentName==a&&u==f[e].parentChildName&&(f[e].y+=h)}t<c.length-1?this.createOtherLayerPoints(i,t+1):(this._points=f,this.model.points=[],this.alignDataSourcePoints(i,1))},k.prototype.alignDataSourcePoints=function(n,t){for(var i,u=this.model.levels,r=t;r<u.length;r++)for(i=0;i<this._points.length;i++)if(r==this._points[i].layer)this.parentNames.push(this._points[i].parentName+"_layer_"+this._points[i].layer);else{r=u.length;break}this.findParentElements(this.model.points)},k.prototype.findParentElements=function(n){for(var r,t,i=0;i<this.parentNames.length;i++)for(r=this.parentNames[i],t=0;t<this._points.length;t++)r==this._points[t].x+"_layer_"+this._points[t].layer&&n.push(this._points[t]);(this.isNullOrUndefined(this.childProcess)||this.childProcess)&&this.findChildElements(n,0)},k.prototype.findChildElements=function(n,t){var i,r;this.visiblePoints=[];t<=n.length-1?(this.childProcess=!0,this.visiblePoints.push(n[t]),this.findOtherChildElements(this.visiblePoints,this.parentNames[t],2,0,i,r,t)):this.childProcess=!1},k.prototype.findOtherChildElements=function(n,t,i,r,u,f,e){for(var h,c,l,v,s=n,a,y=t.substring(0,t.indexOf("_layer_")),o=0;o<s.length;o++){for(s[o].point=[],h=0;h<this._points.length;h++)i==this._points[h].layer&&s[o].parentChildName+"_"+this._points[h].x==this._points[h].parentChildName&&s[o].x==this._points[h].parentName&&this._points[h].parentChildName.indexOf(y)>=0&&s[o].point.push(this._points[h]);if(this.isNullOrUndefined(u))u=s,f=[],f.push({layer:i}),f[0].point=[];else if(r>=u[o].point.length){for(r>=u[o].point.length&&i>=this.model.levels.length&&this.findChildElements(this.model.points,e+1),r=0,c=0;c<s[o].point.length;c++)f[0].point.push(s[o].point[c]);u=f}else for(c=0;c<s[o].point.length;c++)f[0].point.push(s[o].point[c]);for(l=r;l<u[o].point.length;l++)r<u[o].point.length&&(i=r==0?i+1:i,r++,v=u[o].point[l],a=[],a.push(v)),this.childProcess&&this.isNullOrUndefined(u[o].point[l].point)&&this.findOtherChildElements(a,t,a[0].layer+1,r,u,f,e)}},k.prototype.setSvgsize=function(n,t){var u,f,i,r,e,o;u=document.getElementById(t).clientWidth;f=document.getElementById(t).clientHeight;i=450;r=ej.isTouchDevice()?250:600;n.width==""||this.isResize&&this.model.isResponsive?u>0&&(r=u):(e=n.width,r=typeof e=="string"&&e.indexOf("%")!=-1?u/100*parseInt(e):parseFloat(n.width));this.width=r;this.container.setAttribute("width",r.toString());n.height!=""?(o=n.height,i=typeof o=="string"&&o.indexOf("%")!=-1?f/100*parseInt(o):parseFloat(n.height)):f>0&&(i=f);this.height=i;this.container.setAttribute("height",i.toString())},k.prototype.isNullOrUndefined=function(n){return n===undefined||n===null},k.prototype.createSvg=function(n){var t=this.createElement("svg");return this.setAttributes(n,t),t},k.prototype.drawPolygon=function(n){var t=document.getElementById(n.id);return this.isNullOrUndefined(t)&&(t=this.createElement("polygon")),this.setAttributes(n,t),t},k.prototype.createText=function(n,t){var i=this.createElement("text");return this.setAttributes(n,i),t&&(i.textContent=t),i},k.prototype.createGroup=function(n){var t=this.createElement("g");return this.setAttributes(n,t),t},k.prototype.drawRect=function(n){var t=document.getElementById(n._id);return t===null&&(t=this.createElement("rect")),this.setAttributes(n,t),t},k.prototype.drawCircle=function(n){var t=document.getElementById(n.id);return this.isNullOrUndefined(t)&&(t=this.createElement("circle")),this.setAttributes(n,t),t},k.prototype.createElement=function(n){return document.createElementNS("http://www.w3.org/2000/svg",n)},k.prototype.getXcordinate=function(n,t,i){return n+t*Math.cos(i)},k.prototype.getYcordinate=function(n,t,i){return n+t*Math.sin(i)},k.prototype.setAttributes=function(n,t){var r,u,i,f;for(r=Object.keys(n),u=r.map(function(t){return n[t]}),i=0,f=r.length;i<f;i++)t.setAttribute(r[i],u[i])},k}(ej.WidgetBase);n.SunburstChart=b;ej.widget("ejSunburstChart","ej.SunburstChart",new b)}(jQuery);n.compareExtend=function(t,i,r){var e,o,u,f;if(typeof r=="object"&&r!==null)for(e=Object.keys(r),o=e.length,f=0;f<o;f++)u=e[f],i.hasOwnProperty(u)&&i[u]!=null?(Array.isArray(i[u])||typeof i[u]=="object"&&i[u]!==null)&&n.compareExtend({},i[u],r[u]):i[u]=r[u];return i};n.deepExtend=function(t){var u,i,r;for(t=t||{},u=1;u<arguments.length;u++)if(i=arguments[u],i)for(r in i)i.hasOwnProperty(r)&&(t[r]=typeof i[r]=="object"?n.deepExtend(t[r],i[r]):i[r]);return t}})(n||(n={}))});
