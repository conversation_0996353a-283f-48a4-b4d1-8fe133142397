﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="blockOtherLbl.Text" xml:space="preserve">
    <value>Εμποδίζει άλλα συμβάντα</value>
  </data>
  <data name="endTimeLbl.Text" xml:space="preserve">
    <value>Λήξη</value>
  </data>
  <data name="EndTimeRequiredMessage" xml:space="preserve">
    <value>Το πεδίο Λήξη δεν έχει συμπληρωθεί.</value>
  </data>
  <data name="ExceedingAppointmentRecurrenceMessage" xml:space="preserve">
    <value>Το συμβάν επαναλαμβάνεται για χρονικό διάστημα μεγαλύτερο του ενός έτους. Μειώστε τις επαναλήψεις σε διάρκεια μικρότερη του ενός έτους και μέχρι 15 επαναλήψεις.</value>
  </data>
  <data name="InvalidAppointmentRecurrenceMessage" xml:space="preserve">
    <value>Οι ρυθμίσεις για την επαναληψιμότητα δεν είναι σωστές.</value>
  </data>
  <data name="notesLbl.Text" xml:space="preserve">
    <value>Σημειώσεις</value>
  </data>
  <data name="Page.Title" xml:space="preserve">
    <value>Συμβάν</value>
  </data>
  <data name="RecurrenceInfoMessage" xml:space="preserve">
    <value>Το συμβάν είναι επαναλαμβανόμενο. Οι επαναλήψεις συμβαίνουν τις ημερομηνίες: {0}</value>
  </data>
  <data name="recurrentAppointmentWarningMessageLbl.Text" xml:space="preserve">
    <value>Το συμβάν δεν πρέπει να επαναλαμβάνεται για περισσότερο από ένα έτος ή να έχει περισσότερες από 50 επαναλήψεις.</value>
  </data>
  <data name="SaveRecurrentAppointmentConfirmationMessage" xml:space="preserve">
    <value>Είστε σίγουρος ότι θέλετε να αποθηκεύσετε το επαναλαμβανόμενο συμβάν;&lt;br/&gt;&lt;br/&gt;
Οι επαναλήψεις που θα δημιουργηθούν είναι:&lt;br/&gt;
{0}</value>
  </data>
  <data name="SelectAppointmentCategoryBeforeAddingTasksMessage" xml:space="preserve">
    <value>Επιλέξτε πρώτα μια κατηγορία.</value>
  </data>
  <data name="setAppointmentNoCanceledLnk.Text" xml:space="preserve">
    <value>Αναίρεση ακύρωσης</value>
  </data>
  <data name="startDateLbl.Text" xml:space="preserve">
    <value>Ημερομηνία</value>
  </data>
  <data name="StartTimeLaterThanEndTimeMessage" xml:space="preserve">
    <value>Το πεδίο Άφιξη έχει ημερομηνία μεταγενέστερη από την ημερομηνία στο πεδίο Αναχώρηση.</value>
  </data>
  <data name="startTimeLbl.Text" xml:space="preserve">
    <value>Έναρξη</value>
  </data>
  <data name="StartTimeRequiredMessage" xml:space="preserve">
    <value>Το πεδίο Έναρξη δεν έχει συμπληρωθεί.</value>
  </data>
  <data name="subjectLbl.Text" xml:space="preserve">
    <value>Περιγραφή</value>
  </data>
  <data name="TaskConflictsWithOthers" xml:space="preserve">
    <value>Το συμβάν δεν μπορεί να καταχωρηθεί γιατί συμπίπτει με άλλο συμβάν ή συνεδρία.</value>
  </data>
  <data name="taskSupervisionCustomersLbl.Text" xml:space="preserve">
    <value>Πελάτες</value>
  </data>
  <data name="taskSupervisionLbl.Text" xml:space="preserve">
    <value>Εποπτεία</value>
  </data>
  <data name="taskSupervisionRepliesLbl.Text" xml:space="preserve">
    <value>Απαντήσεις</value>
  </data>
  <data name="taskSupervisionSubjectLbl.Text" xml:space="preserve">
    <value>Θέμα</value>
  </data>
  <data name="taskSupervisionTherapistIDsLbl.Text" xml:space="preserve">
    <value>Θεραπευτές</value>
  </data>
  <data name="therapistCommentsInfoLbl.Text" xml:space="preserve">
    <value>(Συμπληρώνεται από τον θεραπευτή)</value>
  </data>
  <data name="therapistCommentsLbl.Text" xml:space="preserve">
    <value>Γενικά Σχόλια</value>
  </data>
  <data name="therapistIdLbl.Text" xml:space="preserve">
    <value>Ιατρός</value>
  </data>
  <data name="TherapistRequiredMessage" xml:space="preserve">
    <value>Το πεδίο Θεραπευτής δεν έχει συμπληρωθεί.</value>
  </data>
  <data name="visibleToAllLbl.Text" xml:space="preserve">
    <value>Φανερό σε Όλους</value>
  </data>
</root>