﻿using Data;
using Newtonsoft.Json;
using Resources;
using Syncfusion.JavaScript.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Syncfusion.DocIO;
using Syncfusion.DocIO.DLS;
using System.IO;
using Syncfusion.DocToPDFConverter;
using Syncfusion.Pdf;
using System.Web.Services;
using Syncfusion.JavaScript.Web;
using System.Data;
using System.Threading;
using System.Drawing;
using static Data.MentalViewDataSet;
using System.Runtime.InteropServices.ComTypes;
using Microsoft.Ajax.Utilities;
using Syncfusion.Linq;
using System.Web.UI.HtmlControls;

namespace WebUI
{

    public partial class Task : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = null;

                ((Main)this.Master).ServerMessage.ButtonClicked += new ButtonClickedHandler(this.ServerMessageButtonClicked);
                ((Main)this.Master).PageTitle = GetLocalResourceObject("Page.Title").ToString();
                //Καλούμε την javascript Initialization() λόγω του UpdatePanel.
                ScriptManager.RegisterStartupScript(this, this.GetType(), "temp", "<script language='javascript'>Initialization();</script>", false);

                #region  Controls Localization
                //this.startTimeTxtBox.DatePickerButtonText = new ButtonText() { Today = GlobalResources.TodayText, TimeNow = GlobalResources.TimeNowText, Done = GlobalResources.DoneText, TimeTitle = GlobalResources.TimeText };
                //this.startDateTxtBox.ButtonText = GlobalResources.TodayText;
                this.startDateTxtBox.DateTimePickerButtonText.Done = GlobalResources.Close;
                this.startDateTxtBox.DateTimePickerButtonText.Today = GlobalResources.TodayText;
                this.startDateTxtBox.DateTimePickerButtonText.TimeNow = GlobalResources.TimeNowText;
                this.startDateTxtBox.DateTimePickerButtonText.TimeTitle = GlobalResources.TimeText;
                this.startDateTxtBox.DateTimeFormat = "dd/MM/yyyy HH:mm"; //System.Threading.Thread.CurrentThread.CurrentUICulture.DateTimeFormat.ShortDatePattern;
                //this.startTimeTxtBox.TimeFormat = System.Threading.Thread.CurrentThread.CurrentUICulture.DateTimeFormat.ShortTimePattern;
                //this.endTimeTxtBox.TimeFormat = System.Threading.Thread.CurrentThread.CurrentUICulture.DateTimeFormat.ShortTimePattern;

                this.endDateTxtBox.DateTimePickerButtonText.Done = GlobalResources.Close;
                this.endDateTxtBox.DateTimePickerButtonText.Today = GlobalResources.TodayText;
                this.endDateTxtBox.DateTimePickerButtonText.TimeNow = GlobalResources.TimeNowText;
                this.endDateTxtBox.DateTimePickerButtonText.TimeTitle = GlobalResources.TimeText;
                this.endDateTxtBox.DateTimeFormat = "dd/MM/yyyy HH:mm";
                #endregion


                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                Int64 userId = Convert.ToInt64(userData["UserId"]);
                string roleName = userData["Role"].ToString();

                if (roleName == "Guest")
                {
                    Response.StatusCode = 404;
                    Response.End();
                }

                #region  Data Bindings
                DropDownListItem ddlItem;

                #region  TherapistId
                if (this.therapistIdCmbBox.Items.Count == 0)
                {
                    this.therapistIdCmbBox.Items.Clear();
                    ddlItem = new DropDownListItem();
                    ddlItem.Text = "&nbsp;";
                    ddlItem.Value = "-1";
                    this.therapistIdCmbBox.Items.Add(ddlItem);

                    MentalViewDataSet doctorsDS = Data.Business.UsersBusiness.GetAllDoctorUsersList(tenantId);
                    foreach (DataRow doctorRow in doctorsDS.Users.Rows)
                    {
                        ddlItem = new DropDownListItem();
                        ddlItem.Text = doctorRow["FullName"].ToString();
                        ddlItem.Value = doctorRow["UserId"].ToString();
                        this.therapistIdCmbBox.Items.Add(ddlItem);
                    }
                }
                #endregion

                #region  Ρυθμίσεις για το taskSupervisionTherapistIDsDDL
                MentalViewDataSet usersDS = Data.Business.UsersBusiness.GetAllDoctorUsersList(tenantId);
                if (this.taskSupervisionTherapistIDsDDL.Items.Count == 0)
                {
                    //dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-BodyLanguage"].Copy();

                    this.taskSupervisionTherapistIDsDDL.DataSource = usersDS.Users;
                    this.taskSupervisionTherapistIDsDDL.DataBind();
                }
                #endregion
                #endregion

                if (!IsPostBack)
                {
                    //Αν δημιουργούμε νέο Appointment
                    if (string.IsNullOrEmpty(this.Request.Params["AppointmentId"]))
                    {
                        ds = new Data.MentalViewDataSet();
                        ds.EnforceConstraints = false;
                        Data.Business.ConfigureDataSet(ref ds);

                        //Δημιουργεί το νεο Appointment
                        MentalViewDataSet.AppointmentsRow appointmentsRow = ds.Appointments.NewAppointmentsRow();
                        appointmentsRow.TenantId = tenantId;
                        appointmentsRow.UserId = userId;
                        appointmentsRow.AppointmentType = "Event";  //Ορίζουμε ότι είναι τύπος 'Συμβάν'
                        appointmentsRow.StartTime = DateTime.Now;
                        appointmentsRow.EndTime = appointmentsRow.StartTime.AddHours(1);
                        appointmentsRow.State = "";
                        appointmentsRow.Categorize = "";
                        //appointmentsRow.SetAppointmentCategoryIdNull();
                        appointmentsRow.Room = "";
                        appointmentsRow.Request = "";
                        appointmentsRow.SupervisorCommentsAfter = "";
                        appointmentsRow.SupervisorInstructionsBefore = "";
                        appointmentsRow.VisibleToAll = false;
                        ds.Appointments.AddAppointmentsRow(appointmentsRow);

                        ViewState["Appointment"] = ds;
                    }
                    else  //Αν κάνουμε επεξεργασία
                    {
                        int appointmentId = int.Parse(this.Request.Params["AppointmentId"]);

                        ds = (Data.MentalViewDataSet)Data.Business.AppointmentsBusiness.GetAppointmentById(appointmentId);
                        //this.originalStartTimeHiddenField.Value = ds.Appointments[0].StartTime.ToString();  //TODO: Feature:Ενημέρωση ημέρας/ώρας και στα upcoming appointments
                        //this.originalEndTimeHiddenField.Value = ds.Appointments[0].EndTime.ToString();  //TODO: Feature:Ενημέρωση ημέρας/ώρας και στα upcoming appointments

                        //Αν το appointment που κάνουμε edit έχει ήδη CustomRecurrence.
                        if (ds.Appointments[0].CustomRecurrence)
                        {
                            ViewState["CustomRecurrenceExists"] = true;  //Καταχωρούμε τοπικά ότι το appointment υπό επεξεργασία έχει ήδη recurrence.
                        }
                        else  //Αν το appointment που κάνουμε edit ΔΕΝ έχει CustomRecurrence.
                        {
                            //Ελέγχουμε μήπως έχει το "παλιό" recurrence (της Syncfusion).
                            //Ο παρακάτω κώδικας υπάρχει για να μετατρέπει το recurrence της Syncfusion στο νέο CustomRecurrence.
                            if (ds.Appointments[0].Recurrence == true)
                            {
                                ds.Appointments[0].CustomRecurrence = ds.Appointments[0].Recurrence;
                                ds.Appointments[0].CustomRecurrenceRule = ds.Appointments[0].RecurrenceRule;

                                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, "H συνεδρία είναι επαναλαμβανόμενη με το παλιό τρόπο επανάληψης. Κάντε αποθήκευση της συνεδρίας ώστε η επαναληψιμότητα να μετατραπεί στο νέο τρόπο.", ServerMessageButtons.Ok);
                                //Προσοχή: αυτός ο κώδικας εμφανίζει MessageBox και σταματάει προκαλεί reload, ίσως ο παρακάτω κώδικας να μην εκτελείται.
                            }
                        }

                        ViewState["Appointment"] = ds;
                    }

                    this.SetDataOnUIControls(ds);
                }
                //else  //Αν γίνεται postback
                //{
                //    ds = (Data.MentalViewDataSet)ViewState["Appointment"];

                //    if (Request.Form["__EVENTTARGET"] == this.contactIdCmbBox.ID)
                //    {
                //        try
                //        {
                //            this.contactIdCmbBox.Value = Request.Form["ctl00$ctl00$includeFilesBody$mainBody$contactIdCmbBox"];
                //            this.GetDataFromUIControls(ref ds);

                //            this.SetDataOnUIControls(ds);
                //        }
                //        catch (Exception exp)
                //        {
                //            Data.ExceptionLogger.LogException(exp);
                //            ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
                //        }
                //    }
                //}

                ds = (Data.MentalViewDataSet)ViewState["Appointment"];
                //Αν ο χρήστης δεν είναι αυτός που δημιούργησε το Task, απενεργοποιούμε τα controls.
                //if (ds.Appointments[0].UserId!=userId)
                //{
                //    this.therapistIdCmbBox.Enabled = false;
                //    this.startDateTxtBox.Enabled = false;
                //    this.endDateTxtBox.Enabled = false;
                //    this.notesTxtBox.Attributes.Add("disabled", "");
                //    this.visibleToAllChkBox.Disabled = true;
                //    this.subjectTxtBox.Attributes.Add("disabled", "");
                //    this.saveBtn.Attributes.Add("disabled", "");
                //    this.saveToggleBtn.Attributes.Add("disabled", "");
                //    this.deleteBtn.Attributes.Add("disabled", "");
                //    this.saveCloseBtn.Attributes.Add("disabled", "");
                //    this.recurrenceChkBox.Disabled = true;
                //    this.recurrenceEditor.Enabled = false;
                //}
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }


        private void SetDataOnUIControls(Data.MentalViewDataSet ds)
        {
            this.appointmentIdHiddenField.Value = ds.Appointments[0].AppointmentId.ToString();

            if (ds.Appointments[0].IsUserIdNull())
            {
                this.therapistIdCmbBox.Value = "";
            }
            else
            {
                this.therapistIdCmbBox.Value = ds.Appointments[0].UserId.ToString();
            }

            this.startDateTxtBox.Value = ds.Appointments[0].StartTime;
            this.endDateTxtBox.Value = ds.Appointments[0].EndTime;

            this.subjectTxtBox.Text = ds.Appointments[0].Subject;
            this.visibleToAllChkBox.Checked = ds.Appointments[0].VisibleToAll;
            this.blockOtherChkBox.Checked = ds.Appointments[0].BlockOther;
            this.notesTxtBox.Text = ds.Appointments[0].Notes;
            this.recurrenceChkBox.Checked = ds.Appointments[0].CustomRecurrence;
            this.recurrenceValueHiddenField.Value = ds.Appointments[0].CustomRecurrenceRule;
            this.taskSupervisionChkBox.Checked = ds.Appointments[0].TaskSupervision;
            this.taskSupervisionCustomersTxtBox.Text = ds.Appointments[0].TaskSupervisionCustomers;
            //this.taskSupervisionTherapistIDsDDL.Value = ds.Appointments[0].TaskSupervisionTherapistIDs;
            this.SetValueOnSelectControl(this.taskSupervisionTherapistIDsDDL, ds.Appointments[0].TaskSupervisionTherapistIDs);
            this.taskSupervisionSubjectTxtBox.Text = ds.Appointments[0].TaskSupervisionSubject;
            this.taskSupervisionRepliesTxtBox.Text = ds.Appointments[0].TaskSupervisionReplies;

            //Αν το Appointment είχε αρχικά CustomRecurrence.
            if (ViewState["CustomRecurrenceExists"] != null && Convert.ToBoolean(ViewState["CustomRecurrenceExists"]) == true)
            {
                this.recurrenceChkBox.Disabled = true;
                this.recurrenceEditorWrapper.Visible = false;

                string recurrencesDatesText = ds.Appointments[0]["RecurrencesStartDates"].ToString().Replace("<Expr1>", "").Replace("</Expr1>", "").Replace("Expr1>", "");  //Αφαιρούμε τα tags που εισάγει αυτοματα το SQL query λόγω του XML function που χρησιμοποιεί.
                string[] recurrencesDatesTextArray = recurrencesDatesText.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                recurrencesDatesTextArray = recurrencesDatesTextArray.Select(d => Convert.ToDateTime(d).ToString("ddd dd/MM/yyyy HH:mm")).ToArray();
                this.recurrenceInfoLbl.Text = string.Format(this.GetLocalResourceObject("RecurrenceInfoMessage").ToString(), string.Join(",&nbsp;&nbsp;&nbsp;", recurrencesDatesTextArray));
            }
            else  //Αν το Appointment δεν είχε αρχικά recurrence.
            {
                this.recurrenceChkBox.Disabled = false;
                this.recurrenceEditorWrapper.Visible = true;
                this.recurrenceInfoLbl.Text = "";
            }
        }

        private void GetDataFromUIControls(ref MentalViewDataSet ds)
        {
            try
            {
                ds.Appointments[0].SetContactIdNull();

                if (this.therapistIdCmbBox.Value != "-1" && this.therapistIdCmbBox.Value != "" && this.therapistIdCmbBox.Value != null)
                {
                    ds.Appointments[0].UserId = Convert.ToInt64(this.therapistIdCmbBox.Value);
                }
                else
                {
                    ds.Appointments[0].SetUserIdNull();
                }

                DateTime startDate = this.startDateTxtBox.Value.Value;
                DateTime endDate = this.endDateTxtBox.Value.Value;
                ds.Appointments[0].StartTime = new DateTime(startDate.Year, startDate.Month, startDate.Day, startDate.Hour, startDate.Minute, 0, 0);
                ds.Appointments[0].EndTime = new DateTime(startDate.Year, startDate.Month, startDate.Day, endDate.Hour, endDate.Minute, 0, 0);
                ds.Appointments[0].Subject = this.subjectTxtBox.Text;
                ds.Appointments[0].VisibleToAll = this.visibleToAllChkBox.Checked;
                ds.Appointments[0].BlockOther = this.blockOtherChkBox.Checked;
                ds.Appointments[0].TaskSupervision = this.taskSupervisionChkBox.Checked;
                ds.Appointments[0].TaskSupervisionCustomers = this.taskSupervisionCustomersTxtBox.Text;
                ds.Appointments[0].TaskSupervisionTherapistIDs = this.RetrieveSelect2ValueFromPageForm(this.taskSupervisionTherapistIDsDDL.ID);
                ds.Appointments[0].TaskSupervisionSubject = this.taskSupervisionSubjectTxtBox.Text;
                ds.Appointments[0].TaskSupervisionReplies = this.taskSupervisionRepliesTxtBox.Text;
                ds.Appointments[0].Notes = this.notesTxtBox.Text;

                //Αν το Appointment είχε CustomRecurrence αρχικά.
                if (ViewState["CustomRecurrenceExists"] != null && Convert.ToBoolean(ViewState["CustomRecurrenceExists"]) == true)
                {

                }
                else  //Αν το Appointment δεν είχε CustomRecurrence αρχικά.
                {
                    ds.Appointments[0].CustomRecurrence = this.recurrenceChkBox.Checked;
                    ds.Appointments[0].CustomRecurrenceRule = this.recurrenceChkBox.Checked ? this.recurrenceValueHiddenField.Value : "";
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void saveCloseBtn_Click(object sender, EventArgs e)
        {
            try
            {
                this.Save(true);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void saveBtn_Click(object sender, EventArgs e)
        {
            try
            {
                this.Save(false);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void Save(bool close)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Appointment"];
                ds.EnforceConstraints = false;

                if (this.ValidateControls() == true)
                {
                    this.GetDataFromUIControls(ref ds);

                    //Αν το Appointment ΔΕΝ είχε CustomRecurrence αρχικά.
                    if (ViewState["CustomRecurrenceExists"] == null || (ViewState["CustomRecurrenceExists"] != null && Convert.ToBoolean(ViewState["CustomRecurrenceExists"]) == false))
                    {
                        //Αν υπάρχει CustomRecurrence.
                        if (ds.Appointments[0].CustomRecurrence)
                        {
                            //Αφού το appointment είναι recurrent τότε ελέγχει να μην διαρκούν όλα τα appointment περισσότερο από ένα έτος ή να μην ξεπερνάνε τις 50 επαναλήψεις.
                            if (this.ValidateRecurrence(ds.Appointments[0].CustomRecurrenceRule, ds.Appointments[0].StartTime) == false)
                            {
                                ViewState["Appointment"] = ds;
                                this.SetDataOnUIControls(ds);
                                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("ExceedingAppointmentRecurrenceMessage").ToString(), ServerMessageButtons.Ok);
                                return;
                            }

                            #region Εμφανίζει το μήνυμα επιβεβαίωσης για το recurrent appointment
                            //Βρίσκει πόσες επαναλήψεις θα δημιουργηθούν και σε ποιες ημέρες.
                            List<DateTime> dates = new List<DateTime>();
                            dates = Data.RecurrenceHelper.GetRecurrenceDateTimeCollection(ds.Appointments[0].CustomRecurrenceRule, ds.Appointments[0].StartTime).ToList();
                            string datesText = "";
                            foreach (DateTime date in dates)
                            {
                                datesText += date.ToString("dddd dd MMM yyyy ") + "<br/>";
                            }

                            ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, string.Format(GetLocalResourceObject("SaveRecurrentAppointmentConfirmationMessage").ToString(), datesText), ServerMessageButtons.YesNo, "Save", close.ToString());
                            return;
                            #endregion
                        }
                    }
                    //TODO: Feature:Ενημέρωση ημέρας/ώρας και στα upcoming appointments
                    //else if (ViewState["CustomRecurrenceExists"] != null && Convert.ToBoolean(ViewState["CustomRecurrenceExists"]) == true)  //Αν το Appointment ΈΧΕΙ CustomRecurrence αρχικά (δηλαδή κάνουμε επεξεργασία).
                    //{
                    //    //Αν υπάρχει CustomRecurrence.
                    //    if (this.originalStartTimeHiddenField.Value != "")
                    //    {
                    //        DateTime originalStartTime = Convert.ToDateTime(this.originalStartTimeHiddenField.Value);
                    //        DateTime originalEndTime = Convert.ToDateTime(this.originalEndTimeHiddenField.Value);

                    //        //Αν οι ημερομηνίες Έναρξης ή Λήξης έχουν αλλάγξει από το χρήστη.
                    //        if (originalStartTime != ds.Appointments[0].StartTime || originalEndTime != ds.Appointments[0].EndTime)
                    //        {
                    //            ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("UpdateUpcomingRecurrentAppointmentsConfirmationMessage").ToString(), ServerMessageButtons.YesNoCancel, "SaveIncludeUpcoming", close.ToString());
                    //            return;
                    //        }
                    //    }
                    //}

                    Data.Business.SaveAllData(ds);
                    ViewState["Appointment"] = ds;


                    #region  Στέλνει το email στους θεραπευτές
                    //Αν είναι Εποπτεία.
                    if (ds.Appointments[0].TaskSupervision)
                    {
                        string[] therapistsIds = ds.Appointments[0].TaskSupervisionTherapistIDs.Split('|');

                        string host = System.Configuration.ConfigurationManager.AppSettings["EmailHost"];
                        int port = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings["EmailPort"]);
                        string username = System.Configuration.ConfigurationManager.AppSettings["EmailAcountUsername"];
                        string password = System.Configuration.ConfigurationManager.AppSettings["EmailAcountPassword"];
                        bool useDefaultCredentials = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["EmailUseDefaultCredentials"]);
                        bool enableSsl = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["EmailEnableSsl"]);
                        string senderEmail = System.Configuration.ConfigurationManager.AppSettings["SenderEmail"];
                        string senderName = System.Configuration.ConfigurationManager.AppSettings["SenderName"];
                        string htmlSignature = System.Configuration.ConfigurationManager.AppSettings["EmailHtmlSignature"];

                        EmailManager emailManager = new EmailManager(host, port, username, password, useDefaultCredentials, enableSsl, senderEmail, senderName, htmlSignature);
                        emailManager.SendTaskSupervisionNotificationToTherapists(therapistsIds, ds);
                    }
                    #endregion

                    //this.originalStartTimeHiddenField.Value = ds.Appointments[0].StartTime.ToString();  //TODO: Feature:Ενημέρωση ημέρας/ώρας και στα upcoming appointments
                    //this.originalEndTimeHiddenField.Value = ds.Appointments[0].EndTime.ToString();  //TODO: Feature:Ενημέρωση ημέρας/ώρας και στα upcoming appointments

                    if (close)
                    {
                        ViewState.Remove("Appointment");
                        Response.Redirect(@"~\Appointments.aspx");
                    }
                    else
                    {
                        this.SetDataOnUIControls(ds);
                    }
                }
                else
                {
                    this.GetDataFromUIControls(ref ds);
                    //Data.Business.SaveAllData(ds);
                    ViewState["Appointment"] = ds;
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private bool ValidateRecurrence(string recurrenceRule, DateTime startDate)
        {
            //Αν δεν περιέχει καν τη λέξη COUNT, δηλαδή δεν έχει τέλος του recurrence
            if (recurrenceRule.Contains("COUNT") == false)
            {
                return false;
            }
            else
            {
                List<DateTime> dates = new List<DateTime>();
                //Pass the recurrencerule string and the start date of the appointment to this method that returns the dates collection based on the recurrence rule
                dates = Data.RecurrenceHelper.GetRecurrenceDateTimeCollection(recurrenceRule, startDate).ToList();

                if ((dates[dates.Count - 1] - dates[0]).TotalDays > 365)  //Αν η διάρκεια των επαναλήψεων ξεπερνάει τις 365 ημέρες
                {
                    return false;
                }
                else if (dates.Count > 50)  //Αν οι επαναλήψεις ξεπερνάνε τις 50
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
        }

        protected void closeBtn_Click(object sender, EventArgs e)
        {
            ViewState.Remove("Appointment");
            Response.Redirect(@"Appointments.aspx");
        }

        protected void deleteBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Appointment"];

                //Αν το appointment είναι νέο ή αν είναι υπάρχον αλλά δεν έχει recurrency.
                if (ds.Appointments[0].RowState == DataRowState.Added || ((ds.Appointments[0].RowState == DataRowState.Modified || ds.Appointments[0].RowState == DataRowState.Unchanged) && ds.Appointments[0].CustomRecurrence == false))
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete");
                }
                else
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteTaskWithUpcomingRecurrencesConfirmationMessage, ServerMessageButtons.YesNoCancel, "DeleteWithUpcomingRecurrences");
                }

                this.GetDataFromUIControls(ref ds);
                ViewState["Appointment"] = ds;
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void Delete()
        {
            Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];

            ds.Appointments[0].Delete();
            Data.Business.SaveAllData(ds);

            ViewState.Remove("Appointment");
            Response.Redirect(@"Appointments.aspx");
        }

        private void DeleteWithUpcomingRecurrences()
        {
            Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];

            //Βρίσκει όλα τα υπόλοιπα recurrent appointments που ανήκουν στο συγκεκριμένο recurrence και συμβαίνουν μετά το επιλεγμένο appointment.
            Data.MentalViewDataSet upcomingAppointmentsDT = Data.Business.AppointmentsBusiness.GetUpcomingRecurrentAppointment(ds.Appointments[0].StartTime, ds.Appointments[0].CustomRecurrenceId);

            ds.Appointments.Merge(upcomingAppointmentsDT.Appointments); //Ενώνει τα επόμενα recurrent appointments με το επιλεγμένο appointment (στη φόρμα).
            foreach (DataRow appointmentRow in ds.Appointments.Rows)
            {
                appointmentRow.Delete();
            }
            Data.Business.SaveAllData(ds);

            ViewState.Remove("Appointment");
            Response.Redirect(@"Appointments.aspx");
        }

        private void ServerMessageButtonClicked(object sender, ButtonClickedArgs args)
        {
            try
            {
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];

                if (args.Action == "Delete")
                {
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        this.Delete();
                    }
                }
                else if (args.Action == "DeleteWithUpcomingRecurrences")
                {
                    //Αν ο χρήστης επέλεξε να διαγράψει το τρέχον appointment μαζί με αυτά που ακολουθούν.
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        this.DeleteWithUpcomingRecurrences();
                    }
                    else if (args.ButtonClicked == ButtonClicked.No)  //Αν ο χρήστης επέλεξε να διαγράψει το τρέχον appointment μαζί με αυτά που ακολουθούν.
                    {
                        this.Delete();
                    }
                }
                else if (args.Action == "Save")
                {
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        //Η αποθήκευση παρακάτω είναι μόνο για όταν πρόκειται να δημιουργηθούν recurrent appointemnts.
                        //Δημιουργεί τα recurrent appointments
                        //ΕΞΗΓΗΣΗ: το ds.Appointments[0] κανονικά είναι το αρχικό appointment που πάει να φτιάξει το πρόγραμμα (που δεν είναι επαναλαμβανόμενο)
                        //όμως τώρα θα πάρει την ημερομηνία του πρώτου από τα recurrent appointments και μετά θα προστεθούν τα άλλα.

                        //Διαγράφει το παλιό recurrence μηχανισμό
                        ds.Appointments[0].Recurrence = false;
                        ds.Appointments[0].RecurrenceRule = "";
                        ds.Appointments[0].RecurrenceExDate = "";
                        ds.Appointments[0].SetRecurrenceIdNull();

                        string guid = Guid.NewGuid().ToString();
                        List<DateTime> dates = new List<DateTime>();
                        dates = Data.RecurrenceHelper.GetRecurrenceDateTimeCollection(ds.Appointments[0].CustomRecurrenceRule, ds.Appointments[0].StartTime).ToList();
                        foreach (DateTime date in dates)
                        {
                            AppointmentsRow newAppointmentsRow = ds.Appointments.NewAppointmentsRow();
                            //newAppointmentsRow = (AppointmentsRow)ds.Appointments.ImportRow(ds.Appointments.Rows[0]);
                            newAppointmentsRow.ItemArray = ds.Appointments.Rows[0].ItemArray.Clone() as object[];
                            newAppointmentsRow.StartTime = new DateTime(date.Year, date.Month, date.Day, newAppointmentsRow.StartTime.Hour, newAppointmentsRow.StartTime.Minute, newAppointmentsRow.StartTime.Second);
                            newAppointmentsRow.EndTime = new DateTime(date.Year, date.Month, date.Day, newAppointmentsRow.EndTime.Hour, newAppointmentsRow.EndTime.Minute, newAppointmentsRow.EndTime.Second);
                            newAppointmentsRow.CustomRecurrence = ds.Appointments[0].CustomRecurrence;
                            newAppointmentsRow.CustomRecurrenceRule = ds.Appointments[0].CustomRecurrenceRule;
                            newAppointmentsRow.CustomRecurrenceId = guid;
                            ds.Appointments.AddAppointmentsRow(newAppointmentsRow);
                        }

                        ds.Appointments[0].Delete();

                        Data.Business.SaveAllData(ds);
                        ViewState["Appointment"] = ds;

                        bool close = true;  //Convert.ToBoolean(args);
                        if (close)
                        {
                            ViewState.Remove("Appointment");
                            Response.Redirect(@"~\Appointments.aspx");
                        }
                    }
                }
                //TODO: Feature:Ενημέρωση ημέρας/ώρας και στα upcoming appointments
                //else if (args.Action == "SaveIncludeUpcoming")
                //{
                //    bool close = Convert.ToBoolean(args.Tag);

                //    if (args.ButtonClicked != ButtonClicked.Cancel)  //Αν ο χρήστης επέλεξε είτε να γίνουν update τα upcoming appointmetns είτε οχι (δηλαδή δεν πάτησε ακύρωση).
                //    {
                //        if (args.ButtonClicked == ButtonClicked.Yes)
                //        {
                //            //Η αποθήκευση παρακάτω είναι μόνο για όταν πρόκειται να κάνει update το τρέχον appointment μαζί με τα επόμενα recurrent appointments.

                //            this.GetDataFromUIControls(ref ds);

                //            //Βρίσκει όλα τα υπόλοιπα recurrent appointments που ανήκουν στο συγκεκριμένο recurrence και συμβαίνουν μετά το επιλεγμένο appointment.
                //            Data.MentalViewDataSet upcomingAppointmentsDT = Data.Business.AppointmentsBusiness.GetUpcomingRecurrentAppointment(ds.Appointments[0].StartTime, ds.Appointments[0].CustomRecurrenceId);

                //            ds.Appointments.Merge(upcomingAppointmentsDT.Appointments); //Ενώνει τα επόμενα recurrent appointments με το επιλεγμένο appointment (στη φόρμα).
                //            foreach (MentalViewDataSet.AppointmentsRow appointmentRow in ds.Appointments.Rows)
                //            {
                //                appointmentRow.StartTime = ds.Appointments[0].StartTime;
                //                appointmentRow.EndTime = ds.Appointments[0].EndTime;
                //            }
                //        }
                //        else if (args.ButtonClicked == ButtonClicked.No)
                //        {
                //            //Η αποθήκευση παρακάτω είναι μόνο για όταν πρόκειται να κάνει update  MONO το τρέχον appointment (ΌΧΙ τα επόμενα recurrent appointments).

                //            this.GetDataFromUIControls(ref ds);

                //        }

                //        Data.Business.SaveAllData(ds);
                //        ViewState["Appointment"] = ds;

                //        if (close)
                //        {
                //            ViewState.Remove("Appointment");
                //            Response.Redirect(@"~\Appointments.aspx");
                //        }
                //    }
                //}
                else if (args.Action == "SessionExpired")
                {
                    Response.Redirect("Default.aspx");
                }

                //Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Appointment"];
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private bool ValidateControls()
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Appointment"];

                //Αν το πεδίο UserId (
                if (this.therapistIdCmbBox.Value == null || this.therapistIdCmbBox.Value == "" || this.therapistIdCmbBox.Value == "-1")
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("TherapistRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                //Αν η ημερομηνία Άφιξη είναι null
                if (this.startDateTxtBox.Value == null)
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("StartTimeRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                //Αν η ημερομηνία Αναχώρηση είναι null
                if (this.endDateTxtBox.Value == null)
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("EndTimeRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                //Αν η ώρα Έναρξης είναι μεταγενέστερη της Λήξης
                if (this.startDateTxtBox.Value > this.endDateTxtBox.Value)
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("StartTimeLaterThanEndTimeMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }


                DateTime startDate = this.startDateTxtBox.Value.Value;
                TimeSpan startTimeSpan = TimeSpan.Parse(this.startDateTxtBox.Value.Value.TimeOfDay.ToString());
                TimeSpan endTimeSpan = TimeSpan.Parse(this.endDateTxtBox.Value.Value.TimeOfDay.ToString());
                DateTime startDateTime = new DateTime(startDate.Year, startDate.Month, startDate.Day, startTimeSpan.Hours, startTimeSpan.Minutes, 0, 0);
                DateTime endDateTime = new DateTime(startDate.Year, startDate.Month, startDate.Day, endTimeSpan.Hours, endTimeSpan.Minutes, 0, 0);
                Int64 selectedUserId = Convert.ToInt64(this.therapistIdCmbBox.Value);

                #region  Ελέγχει αν δεν υπάρχει ήδη άλλο Appointment/Event την ώρα του Event.

                Int64 tenantId = ds.Appointments[0].TenantId;
                Int64 appointmentId = ds.Appointments[0].AppointmentId;

                //Ψάχνει να βρει τα Appointments που συμπίπτουν την ώρα του Event.
                List<Int64> conflictingAppointmentsEvents = Business.AppointmentsBusiness.CheckTaskConflicts(tenantId, appointmentId, selectedUserId, "", startDateTime, endDateTime);

                if (conflictingAppointmentsEvents.Count > 0)  //Αν υπάρχουν conflicting Appointments/Events
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("TaskConflictsWithOthers").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }
                #endregion

                return true;
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        protected void sendServiceEmailToContact_Click(object sender, EventArgs e)
        {
            try
            {
                this.SendServiceEmailToContact();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void SendServiceEmailToContact()
        {
            try
            {
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];

                //Ενημερώνει το ds με τα στοιχεία του Contact
                ds.Contacts.Merge(Data.Business.ContactsBusiness.GetContactById(ds.Appointments[0].ContactId).Contacts);

                //Αν το Contact έχει email
                if (ds.Appointments[0].ContactsRow.Email != "")
                {
                    if (ds.Appointments[0].IsUserIdNull() == false)
                    {
                        //Ενημερώνει το ds με τα στοιχεία του User
                        ds.Users.Merge(Data.Business.UsersBusiness.GetUserById(ds.Appointments[0].UserId).Users);
                    }

                    //Ενημερώνει το ds με τα στοιχεία του AppointmentCategories
                    ds.AppointmentCategories.Merge(Data.Business.AppointmentCategoriesBusiness.GetAllAppointmentCategories(ds.Appointments[0].TenantId));

                    //EmailManager.SendReservationEmailToContact(ds.Appointments[0].ContactsRow.Email, ds);

                    this.SetDataOnUIControls(ds);
                }
                else
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ContactHasNoEmailMessage, ServerMessageButtons.Ok, "");
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void sendServiceEmailToUser_Click(object sender, EventArgs e)
        {
            try
            {
                //this.SendServiceEmailToUser();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        //private void SendServiceEmailToUser()
        //{
        //    try
        //    {
        //        Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];

        //        this.GetDataFromUIControls(ref ds);

        //        //Διαβάζει το email του χρήστη
        //        Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
        //        Int64 userId = Convert.ToInt64(userData["UserId"]);
        //        Data.MentalViewDataSet userDS = Data.Business.UsersBusiness.GetUserById(userId);

        //        if (userDS.Users[0].Email != "")
        //        {
        //            //Ενημερώνει το ds με τα στοιχεία του Contact
        //            ds.Contacts.Merge(Data.Business.ContactsBusiness.GetContactById(ds.Appointments[0].ContactId).Contacts);

        //            //Ενημερώνει το ds με τα στοιχεία του Contact
        //            ds.PredefAppointmentItems.Merge(Data.Business.PredAppointmentItemsBusiness.GetAllPredefAppointmentItemsList(ds.Appointments[0].TenantId));

        //            if (ds.Appointments[0].IsUserIdNull() == false)
        //            {
        //                //Ενημερώνει το ds με τα στοιχεία του User
        //                ds.Users.Merge(Data.Business.UsersBusiness.GetUserById(ds.Appointments[0].UserId).Users);
        //            }

        //            //Ενημερώνει το ds με τα στοιχεία του AppointmentCategories
        //            ds.AppointmentCategories.Merge(Data.Business.AppointmentCategoriesBusiness.GetAllAppointmentCategories(ds.Appointments[0].TenantId));

        //            EmailManager.SendReservationEmailToContact(userDS.Users[0].Email, ds);

        //            this.SetDataOnUIControls(ds);
        //        }
        //        else
        //        {
        //            ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.UserHasNoEmailMessage, ServerMessageButtons.Ok, "");
        //        }
        //    }
        //    catch (Exception exp)
        //    {
        //        Data.ExceptionLogger.LogException(exp);
        //        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
        //    }
        //}

        //protected void sendServiceEmailToAdmin_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        this.SendServiceEmailToAdmin();
        //    }
        //    catch (Exception exp)
        //    {
        //        Data.ExceptionLogger.LogException(exp);
        //        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
        //    }
        //}

        //private void SendServiceEmailToAdmin()
        //{
        //    try
        //    {
        //        Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];
        //        this.GetDataFromUIControls(ref ds);

        //        //Ενημερώνει το ds με τα στοιχεία του Contact
        //        ds.Contacts.Merge(Data.Business.ContactsBusiness.GetContactById(ds.Appointments[0].ContactId).Contacts);

        //        //Ενημερώνει το ds με τα στοιχεία του Contact
        //        ds.PredefAppointmentItems.Merge(Data.Business.PredAppointmentItemsBusiness.GetAllPredefAppointmentItemsList(ds.Appointments[0].TenantId));

        //        if (ds.Appointments[0].IsUserIdNull() == false)
        //        {
        //            //Ενημερώνει το ds με τα στοιχεία του User
        //            ds.Users.Merge(Data.Business.UsersBusiness.GetUserById(ds.Appointments[0].UserId).Users);
        //        }

        //        //Ενημερώνει το ds με τα στοιχεία του AppointmentCategories
        //        ds.AppointmentCategories.Merge(Data.Business.AppointmentCategoriesBusiness.GetAllAppointmentCategories(ds.Appointments[0].TenantId));

        //        EmailManager.SendReservationEmailToContact("<EMAIL>", ds);

        //        this.SetDataOnUIControls(ds);
        //    }
        //    catch (Exception exp)
        //    {
        //        Data.ExceptionLogger.LogException(exp);
        //        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
        //    }
        //}


        protected void showContactBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];
                this.GetDataFromUIControls(ref ds);

                if (ds.Appointments[0].ContactId >= 0)
                {
                    Response.Redirect(@"Contact.aspx?ContactId=" + ds.Appointments[0].ContactId.ToString());
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void cancelAppointmentBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];
                this.GetDataFromUIControls(ref ds);

                ds.Appointments[0].Canceled = true;
                ds.Appointments[0].ChargeableCancellation = this.chargeableCanceledAppointmentRad.Checked;  //Ορίζει αν το appointment στη φόρμα είναι χρώσιμο ή όχι.

                //Αν ο χρήστης έχει επιλέξει τα να ακυρωθούν τα επόμενα ραντεβού, τα ακυρώνει αλλά δεν επηρεάζει αν είναι χρεώσιμα.
                if (this.cancelUpcomingAppointmentsChkBox.Checked == true)
                {
                    //Βρίσκει όλα τα υπόλοιπα recurrent appointments που ανήκουν στο συγκεκριμένο recurrence και συμβαίνουν μετά το επιλεγμένο appointment.
                    Data.MentalViewDataSet upcomingAppointmentsDT = Data.Business.AppointmentsBusiness.GetUpcomingRecurrentAppointment(ds.Appointments[0].StartTime, ds.Appointments[0].CustomRecurrenceId);

                    ds.Appointments.Merge(upcomingAppointmentsDT.Appointments); //Ενώνει τα επόμενα recurrent appointments με το επιλεγμένο appointment (στη φόρμα).
                    foreach (MentalViewDataSet.AppointmentsRow appointmentRow in ds.Appointments.Rows)
                    {
                        appointmentRow.Canceled = true;
                    }
                }

                this.Save(true);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void toggleChargableCanceledAppointmentLnk_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];
                this.GetDataFromUIControls(ref ds);

                ds.Appointments[0].ChargeableCancellation = !ds.Appointments[0].ChargeableCancellation;

                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void setAppointmentNoCanceledLnk_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];
                this.GetDataFromUIControls(ref ds);

                ds.Appointments[0].Canceled = false;

                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private string RetrieveControlValueFromPageForm(string controlId)
        {
            try
            {
                foreach (string key in Request.Form.AllKeys)
                {
                    if (key.EndsWith(controlId) == true)
                    {
                        return Request.Form[key];
                    }
                }

                return "";

                ////Αν βρήκε το control με το controlId στο Request.Form. Αν δεν το βρήκε σημαίνει ότι το select2 δεν έχει τιμή.
                //if (Request.Form.AllKeys.Where(n => n.EndsWith(controlId)).Count() > 0)
                //{
                //    List<string> values = new List<string>();
                //    Request.Form.AllKeys.Where(n => n.EndsWith(controlId)).ToList().ForEach(x => values.Add(Request.Form[x]));

                //    return values[0];
                //}
                //else
                //{
                //    return "";
                //}
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(new ApplicationException("controlId: " + controlId));
                throw exp;
            }
        }

        private string RetrieveSelect2ValueFromPageForm(string controlName)
        {
            try
            {
                controlName = "$" + controlName;  //Βάζουμε το $ μπροστά γιατί όλα τα select2 είναι σε container controls και έχουν κι άλλο όνομα μπροστά.
                return this.RetrieveControlValueFromPageForm(controlName).Replace(",", "|");
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(new ApplicationException("controlId: " + controlName));
                throw exp;
            }
        }

        protected void SetValueOnSelectControl(HtmlSelect selectControl, string value)
        {
            //Καθαρίζει τα selected
            foreach (System.Web.UI.WebControls.ListItem item in selectControl.Items)
            {
                item.Selected = false;
            }

            if (value != null && value.Trim() != "")
            {
                //Κάνει select αυτά που πρέπει
                string[] values = value.Split('|');

                foreach (string s in values)
                {
                    try
                    {
                        selectControl.Items.FindByValue(s).Selected = true;
                    }
                    catch (Exception exp)
                    {
                        Data.ExceptionLogger.LogException(new Exception("Exception in control " + selectControl.ID + ". Value:" + s, exp));
                    }
                }
            }
        }
    }
}