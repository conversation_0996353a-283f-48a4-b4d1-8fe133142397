/*!
*  filename: ej.mobile.dialog.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function(n,t){t.widget("ejmDialog","ej.mobile.Dialog",{_setFirst:!0,_rootCSS:"e-m-dialog",defaults:{renderMode:"auto",mode:"alert",enableAutoOpen:!1,title:null,closeOndocumentTap:!1,cssClass:"",enableModal:!0,showButtons:!0,showHeader:!0,leftButtonCaption:null,rightButtonCaption:null,allowScrolling:!1,enableNativeScrolling:t.isAndroid()||t.isIOS()?!1:t.isDevice()?!0:!1,templateId:null,height:null,width:null,enablePersistence:!1,enableAnimation:!0,locale:"en-US",open:null,close:null,beforeClose:null,buttonTap:null},dataTypes:{renderMode:"enum",mode:"enum",enableAutoOpen:"boolean",title:"string",cssClass:"string",enableModal:"boolean",showButtons:"boolean",showHeader:"boolean",leftButtonCaption:"string",rightButtonCaption:"string",allowScrolling:"boolean",templateId:"string",height:"string",width:"string",locale:"string",enablePersistence:"boolean",enableAnimation:"boolean"},_init:function(){t.setRenderMode(this);this._orgEle=this.element.clone();this._getLocalizedLabels();this.model.title=t.isNullOrUndefined(this.model.title)?this._localizedLabels.title:this.model.title;this.model.leftButtonCaption=t.isNullOrUndefined(this.model.leftButtonCaption)?this._localizedLabels.leftButtonCaption:this.model.leftButtonCaption;this.model.rightButtonCaption=t.isNullOrUndefined(this.model.rightButtonCaption)?this._localizedLabels.rightButtonCaption:this.model.rightButtonCaption;this._renderControl();this._createDelegates();this._wireEvents();this.model.enableAutoOpen&&this.open()},_getLocalizedLabels:function(){this._localizedLabels=t.getLocalizedConstants(this.sfType,this.model.locale)},_renderControl:function(){this.element.addClass("e-m-"+this.model.renderMode+" e-m-dialog e-m-user-select "+this.model.cssClass+" e-m-"+this.model.mode);this.model.enableAnimation&&this.element.addClass("e-m-dlg-animate");t.isNullOrUndefined(this.model.templateId)?this._html=this.element.html():this._setTemplate();this.element.empty();this._renderDialog();this._dialogInit();this.element.addClass("e-m-"+(this.model.enableAutoOpen?"show":"hide"));this.model.enableModal&&this._setModal()},_setTemplate:function(){if(t.getCurrentPage().find("#"+this.model.templateId).length){var n=t.getCurrentPage().find("#"+this.model.templateId);this._html=n[0].nodeName.toLowerCase()=="script"?t.getClearString(n[0].innerHTML):n}t.destroyWidgets(this._html)},_renderButton:function(){this._leftbtn=t.buildTag("button").addClass("e-m-"+this.model.mode+"btn e-m-dlg-btn e-m-dlg-alertbtn e-m-state-default").html(this.model.leftButtonCaption);this._dlgbtnwrapper=t.buildTag("div.e-m-dlgbtnwrapper");this._innerDiv.append(this._dlgbtnwrapper);this.model.mode=="alert"?this._dlgbtnwrapper.append(this._leftbtn):this.model.mode=="confirm"&&(this._leftBtn=this._leftbtn.removeClass("e-m-"+this.model.mode+"btn").addClass("e-m-dlg-btn e-m-dlg-leftbtn").html(this.model.leftButtonCaption),this._rightBtn=t.buildTag("button").addClass("e-m-dlg-btn e-m-dlg-rightbtn e-m-state-default").html(this.model.rightButtonCaption),this._dlgbtnwrapper.append(this._leftBtn).append(this._rightBtn))},_renderDialog:function(){var n=!t.isNullOrUndefined(this.model.height)&&parseInt(this.model.height)?this.model.height:"",i=!t.isNullOrUndefined(this.model.width)&&parseInt(this.model.width)?this.model.width:"";this.element.addClass("e-m-dialog e-m-abs");this._innerDiv=t.buildTag("div","",{width:i},{"class":"e-m-dlg-container e-m-abs "});this.element.append(this._innerDiv);this._contentContainer=t.buildTag("div",this._html,{height:n},{"class":"e-m-dlg-content e-m-rel"});this.model.allowScrolling&&this._contentContainer.attr({"data-role":"ejmscrollpanel","data-ej-showscrollbars":!0,"data-ej-enablenativescrolling":this.model.enableNativeScrolling,"data-ej-isrelative":!0});this._innerDiv.append(this._contentContainer);this.model.mode=="custom"||(t.buildTag("div",this.model.title,{},{"class":"e-m-dlg-hdr "+(this.model.showHeader?"":"e-m-hide")}).insertBefore(this._contentContainer),this._header=this._innerDiv.find(".e-m-dlg-hdr"),this.model.showButtons&&this._renderButton())},_dialogInit:function(){var n=this.element.parents("form").length>0?this.element.parents("form"):t.getCurrentPage();this.element.appendTo(n);t.widget.init(this._innerDiv);!t.isAppNullOrUndefined()&&t.angular.defaultAppName&&t.angular.compile(this._innerDiv)},_setModal:function(){var n=this.element.css("zIndex");this._overlay=t.buildTag("div#.e-m-overlay e-m-abs","",{zIndex:n-1});this.element.append(this._overlay)},_setTemplateId:function(){t.getCurrentPage().find("#"+this.model.templateId).length&&(this._setTemplate(),this._contentContainer.empty().html(this._html),this._dialogInit())},_setCloseOndocumentTap:function(){this.model.closeOndocumentTap&&(this._documentClickHandler=n.proxy(this._docClick,this));t.listenEvents([n(document)],[t.endEvent()],[this._documentClickHandler],!this.model.closeOndocumentTap)},_setLocale:function(){this._getLocalizedLabels();this.model.title=this._localizedLabels.title;this.model.leftButtonCaption=this._localizedLabels.leftButtonCaption;this.model.rightButtonCaption=this._localizedLabels.rightButtonCaption;this._setTitle();this._setLeftButtonCaption();this._setRightButtonCaption()},_createDelegates:function(){this._touchEndHandler=n.proxy(this._buttonTap,this);this._touchStartHandler=n.proxy(this._touchStart,this);this.model.closeOndocumentTap&&(this._documentClickHandler=n.proxy(this._docClick,this))},_wireEvents:function(i){var r="onorientationchange"in window?"orientationchange":"resize";t.listenEvents([window,this.element.find(".e-m-dlg-btn"),this.element.find(".e-m-dlg-btn"),n(document)],[r,t.startEvent(),t.endEvent(),t.endEvent()],[this._resizeHandler,this._touchStartHandler,this._touchEndHandler,this._documentClickHandler],i)},_docClick:function(t){(n(t.target).hasClass("e-m-overlay")||n(t.target).hasClass("e-m-dialog"))&&this.element.hasClass("e-m-show")&&this.close()},_buttonTap:function(t){n(t.target).removeClass("e-m-state-active").addClass("e-m-state-default");this.model.buttonTap&&this._trigger("buttonTap",{text:n(t.target).text(),title:this.model.title,currentEvent:t})},_touchStart:function(t){n(t.target).removeClass("e-m-state-default").addClass("e-m-state-active")},_setModel:function(n){var r=!1,t,i;for(t in n)i="_set"+t.charAt(0).toUpperCase()+t.slice(1),this[i]?this[i]():r=!0;r&&this._refresh()},_setRenderMode:function(){this.element.removeClass("e-m-android e-m-ios7 e-m-windows e-m-flat").addClass("e-m-"+this.model.renderMode)},_setMode:function(){this._refresh()},_setTitle:function(){this._header.length&&this._header.html(this.model.title)},_setLeftButtonCaption:function(){this.element.find(".e-m-dlg-btn.e-m-dlg-"+(this.model.mode=="confirm"?"leftbtn":"alertbtn")).html(this.model.leftButtonCaption)},_setRightButtonCaption:function(){this.element.find(".e-m-dlg-btn.e-m-dlg-rightbtn").html(this.model.rightButtonCaption)},_setEnableModal:function(){this.model.enableModal?this.element.find(".e-m-overlay").length?"":this._setModal():this.element.find(".e-m-overlay").remove()},_setShowButtons:function(){this._refresh()},_setShowHeader:function(){this.model.showHeader?this._header.removeClass("e-m-hide"):this._header.addClass("e-m-hide")},_setButtonTap:function(){},_setBeforeClose:function(){},_setClose:function(){},_setOpen:function(){},_destroy:function(){this._wireEvents(!0);this.element.removeAttr("class");this.element.html(this._orgEle.html())},_refresh:function(){this._destroy();this._renderControl();this._wireEvents(!1)},_animate:function(n,t){var i=this;this._innerDiv.removeClass(n+" "+t);this._innerDiv.addClass(n);n=="e-m-dlg-hideanimate"&&(delay=this.model.renderMode=="android"?250:this.model.renderMode=="windows"?200:0,setTimeout(function(){i.element.addClass("e-m-hide")},delay))},open:function(){this.model.allowScrolling&&this._contentContainer.ejmScrollPanel("scrollTo",0,0);this.element.addClass("e-m-show").removeClass("e-m-hide");this.model.enableAnimation&&this._animate("e-m-dlg-showanimate","e-m-dlg-hideanimate");this.model.open&&this._trigger("open",{title:this.model.title})},close:function(){var n={title:this.model.title};this.model.beforeClose&&this._trigger("beforeClose",n);this.model.enableAnimation?this._animate("e-m-dlg-hideanimate","e-m-dlg-showanimate"):this.element.addClass("e-m-hide");this.element.removeClass("e-m-show");this.model.close&&this._trigger("close",n)},disableButton:function(n){t.isNullOrUndefined(n)?this.element.find(".e-m-dlg-btn").addClass("e-m-state-disabled"):this.element.find(".e-m-dlg-btn.e-m-"+n+"btn").addClass("e-m-state-disabled")},enableButton:function(n){t.isNullOrUndefined(n)?this.element.find(".e-m-dlg-btn").removeClass("e-m-state-disabled"):this.element.find(".e-m-dlg-btn.e-m-"+n+"btn").removeClass("e-m-state-disabled")}});t.mobile.Dialog.Mode={Alert:"alert",Confirm:"confirm",Custom:"custom"};t.mobile.Dialog.Locale=t.mobile.Dialog.Locale||{};t.mobile.Dialog.Locale["default"]=t.mobile.Dialog.Locale["en-US"]={title:"Title",leftButtonCaption:"Cancel",rightButtonCaption:"Continue"}})(jQuery,Syncfusion)});
