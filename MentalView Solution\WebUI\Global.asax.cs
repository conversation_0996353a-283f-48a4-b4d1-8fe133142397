﻿using Data;
using System;
using System.Collections.Generic;
using System.IO.Compression;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Optimization;
using System.Web.Routing;
using System.Web.Security;
using System.Web.SessionState;

namespace WebUI
{
    public class Global : HttpApplication
    {
        void Application_PreRequestHandlerExecute(object sender, EventArgs e)
        {
            try
            {
              
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
            }
        }

        void Application_Start(object sender, EventArgs e)
        {
            try
            {
                //Register Syncfusion license
                Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("Mgo+DSMBaFt/QHNqVVhlW1pFdEBBXHxAd1p/VWJYdVt5flBPcDwsT3RfQF9iSHxTdkZnWHpYdn1dRg==;ORg4AjUWIQA/Gnt2VVhjQlFac1hJXGFWfVJpTGpQdk5xdV9DaVZUTWY/P1ZhSXxRd0RhWX9ddHVQQGddUEQ=;NRAiBiAaIQQuGjN/V0Z+X09EaFpFVmJLYVB3WmpQdldgdVRMZVVbQX9PIiBoS35RdERiW39ec3BUQ2ZbU0Z2;Mgo+DSMBMAY9C3t2VVhjQlFac1hJXGFWfVJpTGpQdk5xdV9DaVZUTWY/P1ZhSXxRd0RhWX9ddHVQQGdfVUQ=;NzY0NTMwQDMyMzAyZTMzMmUzMFVzVkJHZThsNExmZGNrYjJlYW02eU40NFVkWS8xTTV0UzBubW5SRGhaSHc9");


                // Code that runs on application startup
                RouteConfig.RegisterRoutes(RouteTable.Routes);
                BundleConfig.RegisterBundles(BundleTable.Bundles);

                Data.Business.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["MentalViewConnectionString"].ConnectionString;
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
            }
        }

        protected void Session_Start(object sender, EventArgs e)
        {
            // Code that runs when a new session is started
            Session.Timeout = 2880;
        }

        protected void Application_BeginRequest(object sender, EventArgs e)
        {
            try
            {
                System.Globalization.CultureInfo culture = new System.Globalization.CultureInfo("el-GR");

                //System.Globalization.CultureInfo culture = new System.Globalization.CultureInfo(System.Globalization.CultureInfo.CurrentCulture.LCID);
                culture.DateTimeFormat.ShortDatePattern = "dd/MM/yyyy";
                culture.DateTimeFormat.LongTimePattern = "HH:mm:ss";
                culture.DateTimeFormat.ShortTimePattern = "HH:mm";
                System.Threading.Thread.CurrentThread.CurrentCulture = culture;
                System.Threading.Thread.CurrentThread.CurrentUICulture = culture;
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
            }
        }

        protected void Application_AuthenticateRequest(object sender, EventArgs e)
        {

        }

        protected void Application_Error(object sender, EventArgs e)
        {
            Exception exp = Server.GetLastError();

            if (exp != null)
            {
                // Handle HTTP errors
                if (exp.GetType() == typeof(HttpException))
                {
                    //// The Complete Error Handling Example generates
                    //// some errors using URLs with "NoCatch" in them;
                    //// ignore these here to simulate what would happen
                    //// if a global.asax handler were not implemented.
                    //if (exc.Message.Contains("NoCatch") || exc.Message.Contains("maxUrlLength"))
                    //    return;

                    ////Redirect HTTP errors to HttpError page
                    //Server.Transfer("HttpErrorPage.aspx");
                    ExceptionLogger.LogException(exp);
                }
                else
                {
                    ExceptionLogger.LogException(exp);
                    // Clear the error from the server
                    Server.ClearError();
                }
            }
        }

        protected void Session_End(object sender, EventArgs e)
        {

        }

        protected void Application_End(object sender, EventArgs e)
        {

        }
    }
}