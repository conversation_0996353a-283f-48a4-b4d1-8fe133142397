/*!
*  filename: ej.calculate.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){var LookupCachingMode,CalcQuickBase,FormulaInfoSetAction,FormulaInfoHashtable,HashTable;(function($,ej,undefined){return window.__calcQuickextends=function(n,t){function r(){this.constructor=n}for(var i=0;i<t.length;i++)t.hasOwnProperty(i)&&(n[i]=t[i]);r.prototype=t.prototype;n.prototype=new r},window.CalcEngine=function(parentObject){function _getLocaleNumberValue(n){if(n=typeof n!="object"&&Number(n)?Number(n):!ej.isNullOrUndefined(n)&&typeof n!="object"?ej.parseFloat(n+"",ej.cultureObject.name):null,n){var t=ej.preferredCulture(ej.cultureObject.name).numberFormat,i=n.toString();t["."]!="."&&i.indexOf(t["."])<0&&(n=i.replace(".",t["."]))}return ej.isNullOrUndefined(n)?NaN:n.toString()}function _calculateDate(n){return n<10?"0"+n:n}function StringBuilder(){this.strings=[""];this.append=function(n){n&&this.strings.push(n)};this.toString=function(){return this.strings.join("")}}var _weekEndType,FormulaArgumentType,needToContinue;this.parentObject=parentObject;this._useFormulaValues=!1;this._multiTick=!1;this._isErrorString=!1;this._ignoreBracet=!1;this._libraryFunctions=new HashTable;this._customlibraryFunctions=new HashTable;this._refreshedCells=new HashTable;this._treatStringsAsZero=!0;this._supportLogicalOperators=!1;this._isRangeOperand=!1;this._uniqueStringMarker=String.fromCharCode(127);this._isParseArgumentSeparator=!1;this._isParseDecimalSeparatorChanged=!1;this._enableFormulaErrorValidation=!1;this._validPrecedingChars=" (+-*/^&<>=";this._validFunctionNameChars="_";this.bMARKER=String.fromCharCode(146);this.bMARKER2=this.bMARKER+this.bMARKER;this.tic='"';this._parseDecimalSeparator=".";this.parseDateTimeSeparator="/";this._parseArgumentSeparator=",";this._rightBracket=String.fromCharCode(131);this._leftBracket=String.fromCharCode(130);this.iFMarker="qIF"+String.fromCharCode(130);this.braceLeft="{";this.braceRight="}";this._braceRightNLeft=")(";this.computedValueLevel=0;this._circCheckList=[];this._maximumRecursiveCalls=100;this._sortedSheetNames=null;this.trueValueStr="TRUE";this.falseValueStr="FALSE";this.useDatesInCalcs=!1;this.sheetToken="!";this.namedRanges=null;this.undefinednamedRange=null;this.undefinedsheetNamedRnages=null;this.namerangecellcollection=null;this.sheetNamedRangesOriginalNames=null;this.sheetNamedRangeCellCollection=null;this.sheetDependentNamedRangeCells=null;this.namedRangesOriginalNames=null;this.namedRangeValues=null;this.rangeValues=null;this.dependentNamedRangeCells=null;this.namedRangesSized=null;this._namedRangesNonScoped=null;this.char_add="+";this.char_and="i";this.char_ANDop=String.fromCharCode(140);this.char_divide="/";this.char_ELSEop=String.fromCharCode(144);this.char_EM="r";this.char_EP="x";this.char_equal="=";this.char_greater=">";this.char_greatereq="h";this.char_IFop=String.fromCharCode(142);this.char_less="<";this.char_lesseq="f";this.char_multiply="*";this.char_noequal="p";this.char_NOTop=String.fromCharCode(145);this.char_or="w";this.char_ORop=String.fromCharCode(139);this.char_subtract="-";this.char_THENop=String.fromCharCode(143);this.char_XORop=String.fromCharCode(141);this.chartic="'"[0];this._string_and="&";this._string_E="E";this._string_EM="E-";this._string_empty="";this._string_EP="E+";this._string_fixedreference="$";this._string_greatereq=">=";this._string_lesseq="<=";this._string_noequal="<>";this._string_or="^";this.token_add="a";this.token_and="c";this.token_ANDop=String.fromCharCode(133);this.token_divide="d";this.token_ELSEop=String.fromCharCode(137);this.token_EM="v";this.token_EP="t";this.token_equal="e";this.token_greater="g";this.token_greatereq="j";this.token_IFop=String.fromCharCode(135);this.token_less="l";this.token_lesseq="k";this.token_multiply="m";this.token_noequal="o";this.token_NOTop=String.fromCharCode(138);this.token_or=String.fromCharCode(126);this.token_ORop=String.fromCharCode(132);this.token_subtract="s";this.token_THENop=String.fromCharCode(136);this.token_XORop=String.fromCharCode(134);this.tokens=[this.token_add,this.token_subtract,this.token_multiply,this.token_divide,this.token_less,this.token_greater,this.token_equal,this.token_lesseq,this.token_greatereq,this.token_noequal,this.token_and,this.token_or];this._dateTime1900=new Date(1900,0,1,0,0,0);this._preserveLeadingZeros=!1;this._ignoreCellValue=!1;this._errorStrings=null;this.cell="";this._iterationMaxCount=0;this._supportRangeOperands=!1;this._allowShortCircuitIFs=!1;this._processUpperCaseFormula="";this._processUpperCaseIvalue=0;this._processUpperCaseSheet="";this._markerChar="`";this._rowMaxCount=-1;this._columnMaxCount=-1;this._isInteriorFunction=!1;this._tempSheetPlaceHolder=String.fromCharCode(133);this.sheetFamilyID=0;this._supportsSheetRanges=!0;this._markers="()+-*/=><.,!";this._formulaInfoTable=null;this._dependentFormulaCells=null;this._dependentCells=new HashTable;this._calculatingSuspended=!1;this._inAPull=!1;this._useDatesInCalcs=!1;this._excelLikeComputations=!1;this._rethrowLibraryComputationExceptions=!1;this.formulaErrorStrings=["binary operators cannot start an expression","cannot parse","bad library","invalid char in front of","number contains 2 decimal points","expression cannot end with an operator","invalid characters following an operator","invalid character in number","mismatched parentheses","unknown formula name","requires a single argument","requires 3 arguments","invalid Math argument","requires 2 arguments","#NAME?","too complex","circular reference: ","missing formula","improper formula","invalid expression","cell empty","bad formula","empty expression","","mismatched string quotes","wrong number of arguments","invalid arguments","iterations do not converge","Control named '{0}' is already registered","Calculation overflow","Missing sheet"];this._parseDateTimeSeparator="/";this._millisecondsOfaDay=864e5;this.treat1900AsLeapYear=!1;this._oaDate=new Date(1899,11,30);this._saveStringsText="";this._processedCells=[];this.ignoreValueChanged=!1;this._breakedFormulaCells=[];this._tempBreakedFormulaCells=[];this._lockDependencies=!1;this._useDependencies=!1;this._inHandleIterations=!1;this._inRecalculateRange=!1;this._useNoAmpersandQuotes=!1;this._calcID=0;this._operators_cannot_start_an_expression=0;this._reservedWord_AND=1;this._reservedWord_XOR=2;this._reservedWord_IF=3;this._number_contains_2_decimal_points=4;this._reservedWord_ELSE=5;this._reservedWord_NOT=6;this._invalid_char_in_number=7;this._invalid_characters_following_an_operator=6;this._mismatched_parentheses=8;this._unknown_formula_name=9;this._requires_a_single_argument=10;this._requires_3_args=11;this._invalid_Math_argument=12;this._requires_2_args=13;this._bad_index=14;this._too_complex=15;this._circular_reference_=16;this._missing_formula=17;this._improper_formula=18;this._invalid_expression=19;this._cell_empty=20;this._bad_formula=21;this._empty_expression=22;this._virtual_mode_required=23;this._mismatched_tics=24;this._wrong_number_arguments=25;this._invalid_arguments=26;this._iterations_dont_converge=27;this._calculation_overflow=29;this._already_registered=28;this._missing_sheet=30;this._alwaysComputeDuringRefresh=!0;this._libraryComputationException=null;this._dependencyLevel=0;this._isDisposed=undefined;this._forceRefreshCall=!1;this.grid=this.parentObject;this._enableLookupTableCaching=0;this._lookupTables=new HashTable;this._isIE8=ej.browserInfo().name=="msie"&&ej.browserInfo().version=="8.0"?!0:!1;this._acc=4;this._bingo=1e10;this._bigni=1e-10;this.machineepsilon=5e-16;this.maxrealnumber=1e300;this.minrealnumber=1e-300;this.nativeFormats=[];this.nativeFormats.push("[DBNUM1]");this.nativeFormats.push("[DBNUM2]");this.nativeFormats.push("[DBNUM3]");this.nativeFormats.push("[DBNUM4]");this.computeFunctionLevel=0;this._table_Data="[#Data]";this._table_Row="[#THIS ROW]";this._table_Headers="[#Headers]";this._table_Totals="[#Totals]";this._table_All="[#All]";this._isArrayFormula=!1;this._getPositionHeight=0;this._getPositionWidth=0;this._calculateArraySizeheight=0;this._calculateArraySizewidth=0;this._calculateArraySizeminHeight=0;this._calculateArraySizeminWidth=0;this._length=0;this._arrayMarkers="+-*/&";this._validFunctionNameChars="_";this._findNamedRange=!1;this._addFunction=function(n,t){return(n=n.toUpperCase(),this._libraryFunctions.getItem(n)==undefined)?(this._libraryFunctions.add(n,t),!0):!1};this._addToFormulaDependentCells=function(n){var t=this.cell,i=CalcEngine.getSheetFamilyItem(this.grid),r;i.sheetNameToParentObject!=null&&t.indexOf(this.sheetToken)==-1&&(r=i.parentObjectToToken.getItem(this.grid),t=r+t);this.getDependentFormulaCells().containsKey(t)?this.getDependentFormulaCells().getItem(t).containsKey(n)||this.getDependentFormulaCells().getItem(t).add(n,n):(this.getDependentFormulaCells().add(t,new HashTable),this.getDependentFormulaCells().getItem(t).add(n,n))};this._arrayRemove=function(n,t){for(var i=null;(i=n.indexOf(t))!==-1;)n.splice(i,1);return n};this._canGetRowIndex=function(n){var t=0;if(t<n.length&&n[t]==this.sheetToken){for(t++;t<n.length&&n[t]!=this.sheetToken;)t++;t++}while(t<n.length&&this._isLetter(n[t]))t++;return t<n.length?this._isDigit(n[t])?!0:!1:!1};this._checkAddNonScopedNamedRange=function(n){var i=-1,t;(i=n.indexOf("!"))>-1&&(t=n.substring(i+1),this._namedRangesNonScoped.containsKey(t)||this._namedRangesNonScoped.add(t,this.namedRanges.getItem(n)))};this._checkHasCharBeforeNumber=function(n){for(var i=!1,t=n.length-1;t>0;t--)if(this._isLetter(n[t])){i=!0;break}return i};this._checkIfScopedRange=function(n,t){var u,o,c,f,i,s;t="";var e="NaN",h=this.getSheetID(this.grid),r=CalcEngine.getSheetFamilyItem(this.grid);if(n[0]==this.sheetToken.toString()&&(u=n.indexOf(this.sheetToken,1),o=parseInt(n.substr(1,u-1)),u>1&&!isNaN(o)&&(n=n.substring(u+1),h=o)),c="!"+h.toString()+"!",r==null||r.sheetNameToToken==null)return e;for(f=r.sheetNameToToken.keys(),i=0;i<f.length;i++)r.sheetNameToToken.getItem(f[i]).toString()==c&&(s=ej.isNullOrUndefined(this.parentObject)||this.parentObject.pluginName!="ejSpreadsheet"?(f[i]+"!"+n).toUpperCase():(this.parentObject.model.sheets[i+1].sheetInfo.text+"!"+n).toUpperCase(),this.getNamedRanges().containsKey(s)&&(t=this.getNamedRanges().getItem(s).toUpperCase(),e=t));return e};this._checkUnderTolerance=function(n,t){return Math.abs(t)>this._absoluteZero?Math.abs((n-t)/t)<this._iterationMaxTolerance:Math.abs(n-t)<this._iterationMaxTolerance};this._combineStack=function(n,t,i){var r="",f,u,e;if(i.length==2){for(f=3,u=n.substring(t);f>1;)e=i.pop().toString(),r=e==this.tic+this.tic?r+this.tic:e+r,u=f==3?e:u,f--;i[0]==this.tic+this.tic&&i.pop();r=n.length==t&&this._isTextEmpty(u.split(this.tic).join(""))?r+u:r;i.push(r)}return i};this._computeMath=function(n,t){var u=0,r,l=!1,a,i,f,e,s,y,h,c,o;if(n.length>0&&(n=this.splitArgsPreservingQuotedCommas(n)[0]),a=[this.getParseArgumentSeparator(),":"],n.length>0&&(!this._isLetter(n[0])&&n[0]!=this.sheetToken&&n[0]!=this.bMARKER||n[0]=="u"&&this._isDigit(n[1]))&&this._indexOfAny(n,a)==-1){if(n=n.split("u").join("-").split("n").join(""),i=this._parseDouble(n),isNaN(i))return this.getErrorStrings()[1].toString();u=t(i)}else if(n.length>0&&(n[0]==this.bMARKER||n[0]=="u"||n[0]=="n"||this._indexOfAny(n,this.tokens)>-1)){n=n.split("{").join("(");n=n.split("}").join(")");f="";e=Math.PI.toString();try{n.indexOf(e)>-1?(r=this._substring(n,1,n.indexOf(e)-2),r=this._isTextEmpty(r)?"1":r,f=this._indexOfAny(n,this.tokens)>-1?n.split(e.toString())[1]:"1",l=!0):r=this.computedValue(n)}catch(v){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return v}if(l)if(s=this.computedValue(r.toString()),f.indexOf("180")>-1)if(i=this._parseDouble(s),i)u=t(i*(Math.PI/180));else return this.getErrorStrings()[1].toString();else{if(y=this.computedValue(s+f),h=this._parseDouble(y),isNaN(h))return this.getErrorStrings()[1].toString();u=t(h*Math.PI)}else{if(i=this._parseDouble(r),isNaN(i))return this.getErrorStrings()[1].toString();u=t(i)}}else for(n=n.split("u").join("-"),c=this.getCellsFromArgs(n),o=0;o<c.length;o++){try{r=this.getValueFromArg(c[o])}catch(v){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return v}if(r.length>0){if(i=this._parseDouble(r),isNaN(i))return this.getErrorStrings()[1].toString();u=t(i)}break}return this.computeIsErr(u.toString())==this.trueValueStr?this.getErrorStrings()[4].toString():u.toString()};this._computeInteriorFunctions=function(n){var i,f,t,s,e,h,o,c,u;try{if(this._isTextEmpty(n))return n;for(this.computeFunctionLevel++,i=this._findLastqNotInBrackets(n);i>0;){if(f=n.substring(i).indexOf(this._rightBracket),f==-1)throw this.formulaErrorStrings[this._bad_formula];if(this._isInteriorFunction=!0,t=this._substring(n,i,f+1),t=this.computedValue(t),ej.isNullOrUndefined(this.parentObject)||this.parentObject.pluginName!="ejSpreadsheet")t==""||t[0]!=this.tic[0]||t[t.length-1]!=this.tic[0]||(u=this._substring(t,1,t.length-2),u.indexOf(this.tic)!=-1&&(this._multiTick=!0,u=u.split(this.tic).join("|")),t=this.tic+u+this.tic),this._isInteriorFunction||(t=this._markupResultToIncludeInFormula(t));else if(t[0]=="-")t="nu"+t.substring(1);else if(t.length>0&&(t[0]==this.tic[0]||t[0]=="#"))t.length>1&&t[0]==this.bmarker&&t[1]==this.bmarker&&(t=this.tic+t+this.tic);else if(!t.startsWith(this.trueValueStr)&&!t.startsWith(this.falseValueStr))if(s=this._parseDouble(t),isNaN(s))if(this._isRange(t)||t[0]!=this.braceLeft||t[t.length-1]!=this.braceRight)this._isRange(t)||(t=this.tic+t+this.tic);else{for(t=t.Replace("{","").Replace("}",""),e=string.Empty,h=this.splitArgsPreservingQuotedCommas(t),o=0;o<h.length;o++)c=0,(c=!isNaN(this.parseDouble(h[o])))&&(e+="n"+r+this._parseArgumentSeparator);t=e.substr(0,e.Length-1)}else t=s<0?"nu"+t.substring(1):"n"+t;n=n.substring(0,i)+t+n.substring(i+f+1);this._isInteriorFunction=!1;i=this._findLastqNotInBrackets(n)}}catch(l){return l}finally{this.computeFunctionLevel--}return n};this._computeArrayInteriorFunction=function(n,t,i){var r=this._string_empty;return t=="LEN"?r=this._computeArrayLen(n,i):t=="ROW"?r=this._computeArrayRow(n,i):t=="COLUMN"&&(r=this._computeArrayColumn(n,i)),i>0?"{"+r+"}":r};this._parseLibraryFormula=function(n){var u,e,r,s,t,i,f,o;if(n.length>0&&(n=n.substr(1,n.length-2)),u=n,n=this._checkForNamedRange(n),n[0]=="{"&&n[n.length-1]=="}"||this._findNamedRange)return this._parseDimensionalArray(n);if(e=this._saveStrings(n),n=this._saveStringsText,n=n.split(" ").join(this._string_empty).split("{").join('"[').split("}").join(']"'),r=n.indexOf(")"),r==-1)n=this._parseArrayFormula(n,u);else while(r>-1){for(s=0,t=r-1;t>-1&&(n[t]!="("||s!=0);)n[t]==")"&&s++,t--;if(t==-1)return this.getErrorStrings()[0].toString();for(i=t-1;i>-1&&(this._isLetterOrDigit(n[i])||this._validFunctionNameChars.indexOf(n[i])>-1||n[i]==this._parseDecimalSeparator);)i--;f=t-i-1;f>0&&this.getLibraryFunctions().getItem(n.substr(i+1,f).toUpperCase())!=null?(o=n.substr(t,r-t+1),o=this._parseArrayFormula(o,u),n=n.substr(0,i+1)+n.substr(i+1,f)+o.split("(").join("{").split(")").join("}")+n.substr(r+1)):n=f==0?this._parseArrayFormula(n,u).split("(").join("{").split(")").join("}"):n.split("(").join("{").split(")").join("}");r=n.indexOf(")")}return e!=null&&e.length>0&&(n=this._setStrings(n,e)),n=n.split("{").join("(").split("}").join(")"),this._isMultiCellArray(u)&&(n=this._parseMultiCellArray(n,u)),n};this._parseArrayFormula=function(n,t){var u=n,c=this._string_empty,w=this._string_empty,i=null,p,v,r,y,o,h,f;if(this._indexOfAny(n,this._arrayMarkers)<0)return n;if(u.indexOf(this._parseArgumentSeparator.toString())>-1){for(u=u.split("{").join('"{').split("}").join('}"'),i=this.splitArgsPreservingQuotedCommas(u),r=0;r<i.length;r++)this._indexOfAny(i[r],this._arrayMarkers)>-1&&i[r].indexOf('"')==-1&&(u=i[r],c=u);if(c==this._string_empty&&(i.length!=1&&this._indexOfAny(i[0],this._arrayMarkers)<0||i[0].indexOf('"{')>-1&&i[0].indexOf('}"')>-1))return n}u=u.split("(").join(this._string_empty).split(")").join(this._string_empty);u=u.split('"').join(this._string_empty);var s=this._resizeCellRange(u,t),l=[],a=this._string_empty,e=this._string_empty;for(length==0&&(length=s[0].length),p=s.length,v=0,r=0;r<length;r++){for(v=r,y="",o=0;o<p;o++)s[o].length==1&&(r=0),y+=s[o][r],r=v;l.push(y)}for(h=0;h<l.length;h++)e+=l[h]+this._parseArgumentSeparator;if(i!=null&&i.length>1)for(f=0;f<i.length;f++)a+=i[f]==c?f==0?"("+e.substr(0,e.length-1):this._parseArgumentSeparator+e.substr(0,e.length-1)+")":f==0?i[f]:this._parseArgumentSeparator+i[f];else a+="("+e.substr(0,e.length-1)+")";return a.split('"').join(this._string_empty)};this._isMultiCellArray=function(n){var i,u,r,t;for(n[0]=="("&&n[n.length-1]==")"&&(n=n.substr(1,n.length-2)),i=!1,u=this._splitString(n),r=0;r<u.length;r++){if(t=u[r],t.indexOf("(")>-1&&t.indexOf(")")==-1||t.indexOf("(")==-1&&t.indexOf(")")>-1){i=!1;break}(this._isRange(t.split(this._string_fixedreference).join(this._string_empty))||t[0]=="{"&&t[t.length-1]=="}")&&(i=!0)}return i};this._parseMultiCellArray=function(n,t){var o=t,u=this._splitString(t),f;if(t=this._markNamedRanges(t),o!=t)return this._parseDimensionalArray(n);if(!ej.isNullOrUndefined(this.cell)){var e=this._getHeight(u),r=this._getWidth(u),i=this._getPosition(e,r);e=this._getPositionHeight;r=this._getPositionWidth;this._getWidth(u)>r&&i!=-1&&i>=r&&(i=i+i/r*(this._getWidth(u)-r));i>=0?(n[0]=="("&&n[n.length-1]==")"&&(n=n.substr(1,n.length-2)),f=this.splitArgsPreservingQuotedCommas(n),f.length>i&&(n=f[i])):n=this.getErrorStrings()[0].toString()}return n};this._getWidth=function(n){var i=0,u=this._string_empty,r=0,t,e,f;for(n[0]=n[0].split(this._string_fixedreference).join(this._string_empty),this._isCellReference(n[0])?(u=this.computeColumns(n[0]),i=parseInt(u)):n[0].indexOf(",")>-1&&n[0].indexOf(";")==-1?(f=this.splitArgsPreservingQuotedCommas(n[0]),i=f.length):n[0].indexOf(";")>-1&&n[0].indexOf(",")==-1&&(i=1),t=0;t<n.length;t++)n[t]=n[t].split(this._string_fixedreference).join(this._string_empty),i==0&&(i=1),this._isCellReference(n[t])?(u=this.computeColumns(n[t]),e=parseInt(u),isNaN(e)||(r=e)):n[t].indexOf(",")>-1&&n[t].indexOf(";")==-1?(f=this.splitArgsPreservingQuotedCommas(n[t]),r=f.length):n[t].indexOf(";")>-1&&n[t].indexOf(",")==-1&&(r=1),r!=0&&(i==1||r<i&&r!=1)&&(i=r);return i};this._getHeight=function(n){var i=0,u=this._string_empty,r=0,t,f;for(n[0]=n[0].split(this._string_fixedreference).join(this._string_empty),this._isCellReference(n[0])?(u=this.computeRows(n[0]),i=parseInt(u)):n[0].indexOf(",")>-1&&n[0].indexOf(";")==-1?i=1:n[0].indexOf(";")>-1&&n[0].indexOf(",")==-1&&(f=this._splitArguments(n[0],";"),i=f.length),t=0;t<n.length;t++)i==0&&(i=1),n[t]=n[t].split(this._string_fixedreference).join(this._string_empty),this._isCellReference(n[t])?(u=this.computeRows(n[t]),r=parseInt(u),isNaN(r)&&(r=0)):n[t].indexOf(",")>-1&&n[t].indexOf(";")==-1?r=1:n[t].indexOf(";")>-1&&n[t].indexOf(",")==-1&&(f=this._splitArguments(n[t],";"),r=f.length),r!=0&&(i==1||i<i&&i!=1)&&(i=r);return i};this._getPosition=function(n,t){var i=this.rowIndex(this.cell),r=this.colIndex(this.cell);return(this._getPositionHeight=n,this._getPositionWidth=t,!ej.isNullOrUndefined(this.parentObject)&&this.parentObject.pluginName=="ejSpreadsheet")?this.parentObject.XLEdit._getCellPosition(n,t)[i-1][r-1]:0};this._resizeCellRange=function(n){var r=[],i=this._splitString(n),v=this._string_empty,s=this._getHeight(i),f=this._getWidth(i),e=0,h=0,t,c,o;for(length=s*f,t=0;t<i.length;t++)if(this._indexOfAny(i[t],this._arrayMarkers)>-1||!this._isCellReference(i[t])?i[t][0]=="["&&i[t][i[t].length-1]=="]"?(i[t]=i[t].substr(1,i[t].length-2),i[t].indexOf(",")>-1?(i[t]=this._calculateArraySize(i[t],s,f,e,h),r.push(this.splitArgsPreservingQuotedCommas(i[t]))):i[t].indexOf(";")>-1&&(i[t]=this._calculateArraySize(i[t],s,f,e,h),r.push(this._splitArguments(i[t],";"))),s=this._calculateArraySizeheight,f=this._calculateArraySizewidth,e=this._calculateArraySizeminHeight,h=this._calculateArraySizeminWidth):(r.push([i[t]]),this._indexOfAny(i[t],this._arrayMarkers)<0&&(e=1)):(i[t]=this._calculateArraySize(i[t],s,f,e,h),r.push(this.getCellsFromArgs(i[t])),s=this._calculateArraySizeheight,f=this._calculateArraySizewidth,e=this._calculateArraySizeminHeight,h=this._calculateArraySizeminWidth),r[t].length<length&&this._indexOfAny(i[t],this._arrayMarkers)<0){var l=r[t].slice(),a=r[t].length,u=0;if(l.length=length,h!=0&&h<f)for(u=0,o=0,c=0;c<length;){while(o<f&&c<length)u>=a&&(u=0),l[c]=r[t][u],o++,c++;u++;o=0}if(e!=0&&e<s)for(u=0,o=a;o<length;o++)u>=a&&(u=0),l[o]=r[t][u],u++;r[t]=l}return r};this._parseDimensionalArray=function(n){var t,f,e,r,w,s,h;if(n=n.split(" ").join(this._string_empty),t=n,this._indexOfAny(n,this._arrayMarkers)>-1&&(n=n.split("{").join('"[').split("}").join(']"'),n=this._parseArrayFormula(n,t).split("(").join("{").split(")").join("}")),n[0]=="{"&&n[n.length-1]=="}"&&(n=n.substr(1,n.length-2)),n.Length==1)return n;if(f=n.indexOf(":"),this._isCellReference(n)&&f>-1){var b=this.getCellsFromArgs(n),c=this._string_empty,l=",",d=this.rowIndex(n.substr(0,f)),g=this.rowIndex(n.substr(f+1)),nt=this.colIndex(n.substr(0,f)),tt=this.colIndex(n.substr(f+1)),a=g-d+1,v=tt-nt+1;for(v==1?l=";":a==1&&(l=","),r=0;r<b.length;r++)c+=b[r]+l;if(n=c.substr(0,c.length-1).split(" ").join(this._string_empty),t=n,a>1&&v>1)return this._parseRangeArray(n,a,v)}if(t.indexOf(",")>-1&&t.indexOf(";")==-1)return this._parseHorizontalArray(n);if(t.indexOf(";")>-1&&t.indexOf(",")==-1)return this._parseVerticalArray(n);if(this._indexOfAny(t,this._arrayMarkers)>-1)return e=this._splitString(t),this._parseRangeArray(n,this._getHeight(e),this._getWidth(e));var y=this._splitArguments(n,";"),e=[],p=[],o=y.length,i=0;for(r=0;r<y.length;r++)w=this.splitArgsPreservingQuotedCommas(y[r]),e.push(w),i=w.length;for(s=0;s<o;s++)for(h=0;h<i;h++)p.push(e[s][h]);if(this.cell==this._string_empty)return p[0];var it=o,k=i,u=this._getPosition(o,i);return o=this._getPositionHeight,i=this._getPositionWidth,k>i&&u!=-1&&u>=i&&(u=u+u/i*(k-i)),n=u>-1?p[u]:this.getErrorStrings()[0].toString(),n};this._parseRangeArray=function(n,t,i){var u=this.splitArgsPreservingQuotedCommas(n);if(this.cell==this._string_empty)return u[0];var e=t,f=i,r=this._getPosition(t,i);return t=this._getPositionHeight,i=this._getPositionWidth,f>i&&r!=-1&&r>=i&&(r=r+r/i*(f-i)),r>-1?u[r]:this.getErrorStrings()[0].toString()};this._parseVerticalArray=function(n){var i=16384,r=this._splitArguments(n,";"),u=r.length,t;return this.cell==this._string_empty?r[0]:(t=this._getPosition(u,i),u=this._getPositionHeight,i=this._getPositionWidth,t=t>-1?t/i:t,t>-1?r[t]:this.getErrorStrings()[0].toString())};this._parseHorizontalArray=function(n){var u=1048576,i=this.splitArgsPreservingQuotedCommas(n),r=i.length,t;return this.cell==this._string_empty?i[0]:(t=this._getPosition(u,r),u=this._getPositionHeight,r=this._getPositionWidth,t=t>-1?t%r:t,t>-1?i[t]:this.getErrorStrings()[0].toString())};this._splitString=function(n){n=n.split(" ").join(this._string_empty).toUpperCase();n=this._markNamedRanges(n);return n.split(/([-+*/&])/)};this._computeArrayLen=function(n,t){var r=this._string_empty,i=this._string_empty,o=this._string_empty,l,s,h,a,u,f,v,c,e;if(n.indexOf(":")>-1&&t>0){for(l=this.getCellsFromArgs(n),s=0;s<l.length;s++)r=this.getValueFromArg(l[s]).split(this.tic).join(this._string_empty),r!=this._string_empty&&(o=r.length,i+=o+this._parseArgumentSeparator);i=i.substring(0,i.length-1)}else if(t>0){for(e=this.splitArgsPreservingQuotedCommas(n),h=0;h<e.length;h++)r=this.getValueFromArg(e[h]).split(this.tic).join(this._string_empty),r!=this._string_empty&&(o=r.length.toString(),i+=o+this._parseArgumentSeparator);i=i.substring(0,i.length-1)}else n.indexOf(":")>-1?this.cell!=this._string_empty?(a=this.getCellsFromArgs(n),u=this.computeRows(n),u=isNaN(u)?0:u,f=this.computeColumns(n),f=isNaN(f)?0:f,v=parseInt(this._getPosition(u,f)),u=this._getPositionHeight,f=this._getPositionWidth,c=isNaN(v)?-1:v,c>=0&&a.length>c?(r=this.getValueFromArg(a[c]).split('"').join(this._string_empty),r!=this._string_empty&&(i=r.length.toString())):i=this.getErrorStrings()[0].toString()):i=this._string_empty:(e=this.splitArgsPreservingQuotedCommas(n),r=this.getValueFromArg(e[0]).split('"').join(this._string_empty),r!=this._string_empty&&(i=r.length.toString()));return i};this._computeArrayRow=function(n,t){var u=this._string_empty,o,l,r,e,i,f;n=n.split('"').join(this._string_empty);var s=this.getCellsFromArgs(n),h=this.rowIndex(s[0].toString()),c=this.rowIndex(s[s.length-1].toString());if(t>0){for(r=h;r<=c;r++)u+=r.toString()+this._parseArgumentSeparator;u=u.substr(0,u.length-1)}else if(this.cell!=this._string_empty){for(o=[],o.length=c-h+1,l=0,r=h;r<=c;r++)o[l]=r.toString(),l++;e=this.computeRows(n);e=isNaN(e)?0:e;i=this.computeColumns(n);i=isNaN(i)?0:i;i==1&&(i=16384);f=this._getPosition(e,i);e=this._getPositionHeight;i=this._getPositionWidth;f=f>-1?f/i:f;u=f>-1?o[f].toString():this.getErrorStrings()[0].toString()}else u=this._string_empty;return u};this._computeArrayColumn=function(n,t){var u=this._string_empty,o,l,i,r,f,e;n=n.split('"').join(this._string_empty);var s=this.getCellsFromArgs(n),h=this.colIndex(s[0].toString()),c=this.colIndex(s[s.length-1].toString());if(t>0){for(i=h;i<=c;i++)u+=i.toString()+this._parseArgumentSeparator;u=u.substr(u.length-1)}else if(this.cell!=this._string_empty){for(o=[],o.length=c-h+1,l=0,i=h;i<=c;i++)o[l]=i.toString(),l++;r=this.computeRows(n);r=isNaN(r)?0:r;f=this.computeColumns(n);f=isNaN(f)?0:f;r==1&&(r=1048576);e=this._getPosition(r,f);r=this._getPositionHeight;f=this._getPositionWidth;e=e>-1?e%f:e;u=e>-1?o[e].toString():this.getErrorStrings()[0].toString()}else u=this._string_empty;return u};this._findAndCheckPrecedingChar=function(n,t,i){var r=n.indexOf(t,i);if(r>0)while(r>-1&&this.getValidPrecedingChars().indexOf(n[r-1])==-1)r=n.indexOf(t,r+1);return this._findAndCheckPrecedingCharCopy=n,r};this._findLastNonQB=function(n){var r=-1,i,t;if(n.indexOf(this.bMARKER)>-1)for(i=0,t=n.length-1;t>=0;--t)if(n[t]==this._rightBracket)i--;else if(n[t]==this._leftBracket)i++;else if(n[t]==this.bMARKER&&i==0){r=t;break}return r};this._findLastqNotInBrackets=function(n){for(var r=-1,i=!1,t=n.length-1;t>-1;){if(n[t]=="q"&&i){r=t;break}n[t]==this._leftBracket?i=!0:n[t]==this._rightBracket&&(i=!1);t--}return r};this._findNextSeparator=function(n,t){for(var i=0,r=!1;!r&&t<n.length;)n[t]=="q"?i++:n[t]==this._rightBracket?i--:i==0&&n[t]==this.getParseArgumentSeparator()&&(r=!0,t--),t++;return t.toString()};this._findNextEndIndex=function(n,t){for(var i=0,u=t,r=!1;!r&&t<n.length;)n[t]=="["?i++:n[t]=="]"&&(i--,i==0&&(r=!0)),t++;return t-u};this._findNonQB=function(n){var r=-1,i,t;if(n.indexOf(this.bMARKER)>-1)for(i=0,t=0;t<n.length;++t)if(n[t]==this._rightBracket)i--;else if(n[t]==this._leftBracket)i++;else if(n[t]==this.bMARKER&&i==0){r=t;break}return r};this._findRightBracket=function(n,t){for(var i=0,r=!1;!r&&t<n.length;)n[t]=="q"?i++:i==0&&n[t]==this._rightBracket?(r=!0,t--):n[t]==this._rightBracket&&i--,t++;return r?t.toString():"NaN"};this._fromOADate=function(n){var t=new Date;return t.setTime(n*this._millisecondsOfaDay+Date.parse(this._oaDate)),t};this._getDateFromSerialDate=function(n){return this.treat1900AsLeapYear&&n>59&&(n-=1),new Date(this._oaDate.setDate(this._oaDate.getDate()+n))};_weekEndType=["","6,0","0,1","1,2","2,3","3,4","4,5","5,6","","","","0","1","2","3","4","5","6"];this._getDoubleArray=function(n){var i,t="",u=[],f,r,o,e;for(n=this.adjustRangeArg(n),f=this.splitArgsPreservingQuotedCommas(n),r=0;r<f.length;r++)if(f[r].indexOf(":")>-1)for(o=this.getCellsFromArgs(f[r]),e=0;e<o.length;e++)t=this.getValueFromArg(o[e]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,t.length>0&&(i=this._parseDouble(t),isNaN(i)?u.push(0):u.push(Number(i)));else t=this.getValueFromArg(f[r]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,t.length>0?(i=this._parseDouble(t),isNaN(Number(i))||u.push(i)):u.push(0);return u};this._getDoubleArrayA=function(n){var t,i="",e=[],u,r,o,f;for(n=this.adjustRangeArg(n),u=this.splitArgsPreservingQuotedCommas(n),r=0;r<u.length;r++)if(u[r].indexOf(":")>-1)for(o=this.getCellsFromArgs(u[r]),f=0;f<o.length;f++)i=this.getValueFromArg(o[f]),i.length>0&&(i==this.trueValueStr?t=1:(t=this._parseDouble(i),isNaN(Number(t))&&t==0),e.push(t));else i=this.getValueFromArg(u[r]),i==this.trueValueStr?t=1:(t=this._parseDouble(i.toString()),isNaN(t)&&(t=0)),e.push(t);return e};this._getFormulaArrayBounds=function(n,t,i){var o=this.colIndex(this.cell),r=this.rowIndex(this.cell),s="",v=this.getFormulaInfoTable().containsKey(this.cell)?this.getFormulaInfoTable().getItem(this.cell):null,h,u,f,e;for(s=v!=null?v.getFormulaText():this.parentObject.getValueRowCol==undefined?this.getValueRowCol(this.getSheetID(this.grid)+1,r,o):this.parentObject.getValueRowCol(this.getSheetID(this.grid)+1,r,o),h=1,u=1,f=1;f<=t;f++)if(o-f>0){var c=this._getSheetTokenFromReference(this.cell)+RangeInfo.getAlphaLabel(o-f)+r.toString(),l="",a=this.getFormulaInfoTable().containsKey(c)?this.getFormulaInfoTable().getItem(c):null;if(l=a!=null?a.getFormulaText():this.parentObject.getValueRowCol==undefined?this.getValueRowCol(this.getSheetID(this.grid)+1,r,o-f):this.parentObject.getValueRowCol(this.getSheetID(this.grid)+1,r,o-f),u=o-f,s!=null&&l!=s){u++;break}}for(e=1;e<=i;e++)if(r-e>0){var c=this._getSheetTokenFromReference(this.cell)+RangeInfo.getAlphaLabel(u)+(r-e).toString(),l="",a=this.getFormulaInfoTable().containsKey(c)?this.getFormulaInfoTable().getItem(c):null;if(l=a!=null?a.getFormulaText():this.parentObject.getValueRowCol==undefined?this.getValueRowCol(this.getSheetID(this.grid)+1,r-e,u):this.parentObject.getValueRowCol(this.getSheetID(this.grid)+1,r-e,u),h=r-e,s!=null&&l!=s){h++;break}}return this._getFormulaArrayBoundsfirstRowIndex=h,this._getFormulaArrayBoundsfirstColIndex=u,this._getFormulaArrayBoundslastRowIndex=h+t,this._getFormulaArrayBoundslastColIndex=u+i,!0};FormulaArgumentType={None:{},Range:{},CellReference:{},TwoTextWithNumber:{},TextWithNumber:{},Numbers:{},Text:{},Date:{}};this.formulaErrorStringCheck=function(n,t){var r=this.splitArgsPreservingQuotedCommas(n),o,k,l,s,f,y,p,w,b,c,a,h,u,g,e,i;switch(t){case FormulaArgumentType.None:if(n!=""){if(this.getRethrowLibraryComputationExceptions())throw this.getLibraryComputationException();return this.formulaErrorStrings[this._invalid_arguments]}break;case FormulaArgumentType.Range:if(o="",s=r,s!=null)for(i=0;i<s.length;i++)if(f=s[i],f.indexOf(":")>-1){if(f[0]==this.tic){if(this.getRethrowLibraryComputationExceptions())throw this.getLibraryComputationException();return this._errorStrings[1].toString()}for(k=this.getCellsFromArgs(f),l=0;l<k.length;l++){try{o=this.getValueFromArg(l)}catch(tt){}if(o.length>0&&this.getErrorStrings().indexOf(o)>-1){if(this.getRethrowLibraryComputationExceptions())throw this.getLibraryComputationException();return o}}}else{try{o=this.getValueFromArg(f)}catch(tt){}if(o.length>0&&(c=this.formulaErrorStringCheck(o,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(c)>-1))return c}break;case FormulaArgumentType.CellReference:if(s=r,s!=null)for(i=0;i<s.length;i++)if(f=s[i],this._isCellReference(f)||f[0]!=this.tic){if(!this._isCellReference(f))return this.getErrorStrings()[5].toString()}else return this.getErrorStrings()[1].toString();break;case FormulaArgumentType.TwoTextWithNumber:if(y=this.formulaErrorStringCheck(r[0],FormulaArgumentType.Text),this.getErrorStrings().indexOf(y)>-1)return y;if(p=this.formulaErrorStringCheck(r[1],FormulaArgumentType.Text),this.getErrorStrings().indexOf(p)>-1)return p;if(r.length==3&&(w=this.formulaErrorStringCheck(r[2],FormulaArgumentType.Text),this.getErrorStrings().indexOf(w)>-1))return w;break;case FormulaArgumentType.TextWithNumber:a=0;b=0;var v=this.getValueFromArg(r[0].split(this.tic).join("")),d=Boolean(v),it=r.length==2&&isNaN(typeof this.getValueFromArg(r[1])=="boolean"),a=isNaN(this._parseDouble(r[0].split(this.tic).join("")));if(a&&(u=this.getValueFromArg(r[0]),this.getErrorStrings().indexOf(u)>-1)){if(this.getRethrowLibraryComputationExceptions())throw this.getLibraryComputationException();return u}if(r.length==2&&(b=this._parseDouble(r[1].split(this.tic).join("")),isNaN(b)&&(c=this.formulaErrorStringCheck(r[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(c)>-1)))return c;break;case FormulaArgumentType.Text:for(e=r,i=0;i<e.length;i++){var v=this.getValueFromArg(e[i]),d=Boolean(v),a=isNaN(this._parseDouble(e[i].split(this.tic).join("")));if(a&&(u=this.getValueFromArg(e[i]),this.getErrorStrings().indexOf(u)>-1)){if(this.getRethrowLibraryComputationExceptions())throw this.getLibraryComputationException();return u}}break;case FormulaArgumentType.Numbers:for(h=r,i=0;i<h.length;i++){var v=this.getValueFromArg(h[i].split(this.tic).join("")),d=Boolean(v),a=ej.parseFloat(h[i].split(this.tic).join(""));if(isNaN(a)){if(u=this.getValueFromArg(h[i]),this.getErrorStrings().indexOf(u)>-1){if(this.getRethrowLibraryComputationExceptions())throw this.getLibraryComputationException();return u}if(!d&&isNaN(this._parseDouble(u))&&u!=""||h[i].includes(":")&&this._isCellReference(h[i])||u.startsWith(this.tic)){if(this.getRethrowLibraryComputationExceptions())throw this.getLibraryComputationException();return this._errorStrings[1].toString()}}}break;case FormulaArgumentType.Date:for(g=0,e=r,i=0;i<e.length;i++){var v=this.getValueFromArg(e[i].split(this.tic).join("")),nt=Boolean(v),u=this.getValueFromArg(e[i]);if(this.getErrorStrings().indexOf(u)>-1){if(this.getRethrowLibraryComputationExceptions())throw this.getLibraryComputationException();return u}if(!nt&&isNaN(Date.parse(this._stripTics0(u)))&&isNaN(this._parseDouble(this._stripTics0(u)))&&u!=""){if(this.getRethrowLibraryComputationExceptions())throw this.getLibraryComputationException();return this._errorStrings[1].toString()}}}return n};this._getSerialDateFromDate=function(n,t,i){var u=0,f,r,e;for(n<1900&&(n+=1900),f=!1;!f;){while(t>12)n++,t-=12;for(f=!0,r=new Date(n,t,1,-1).getDate();i>r;)r=new Date(n,t,1,-1).getDate(),t++,i-=r,f=!1;i<1&&(t--,r=new Date(n,t,1,-1).getDate(),i=r-i)}return e=Date.parse(n.toString()+this.getParseDateTimeSeparator()+t.toString()+this.getParseDateTimeSeparator()+i.toString()),isNaN(e)||(u=this._toOADate(new Date(e))),this.treat1900AsLeapYear&&u>59&&(u+=1),u};this._getSerialDateTimeFromDate=function(n){var t=this._toOADate(n);return this.treat1900AsLeapYear&&t>59&&(t+=1),t};this._getSheetTokenFromReference=function(n){var i="",t;if(n.length>2&&n[0]==this.sheetToken){for(t=1,i=this.sheetToken;t<n.length&&n[t]!=this.sheetToken;)i+=n[t],t++;i+=this.sheetToken}return i};this._getValueComputeFormulaIfNecessary=function(n,t,i){var e=!1,u=this.getFormulaInfoTable().getItem(this.cell),f,r,o,s;if(this.parentObject.getValueRowCol==undefined)f=this.getValueRowCol(this.getSheetID(i)+1,n,t);else if(f=this.parentObject.getValueRowCol(this.getSheetID(i)+1,n,t),ej.isNullOrUndefined(f))return this._string_empty;if(r=f!=null&&!(f.Length===0)?f:"",r[r.length-1]=="}"&&r[0]=="{"&&(r=this._substring(r,1,r.length-2)),r==""&&(u==null||u==undefined)||r!=""&&r[0]!=this.getFormulaCharacter()&&r[r.length-1]!="%")return u!=null&&r==u.getFormulaValue()?u.getFormulaValue():r;if(r.length>0&&r[0]==this.getFormulaCharacter()&&u==null){u=new FormulaInfo;u.setFormulaText(f.toString());this.getDependentFormulaCells().containsKey(this.cell)||this.getDependentFormulaCells().add(this.cell,new HashTable);o=!0;try{u.setParsedFormula(this.parseFormula(r))}catch(h){this._inAPull?(r=h,u=null):u.setFormulaValue(h);o=!1}o&&(u.setFormulaValue(this.computeFormula(u.getParsedFormula())),e=!0);u!=null&&(u.calcID=this._calcID,this.getFormulaInfoTable().containsKey(this.cell)||this.getFormulaInfoTable().add(this.cell,u),r=u.getFormulaValue()!=null?u.getFormulaValue():"")}return u!=null&&(this.getUseFormulaValues()||this.getCalculatingSuspended()&&(!this._inAPull||e)?r=u.getFormulaValue()!=null?u.getFormulaValue():"":e||(this._calcID==u.calcID?r=u.getFormulaValue():(r=this.computeFormula(u.getParsedFormula()),u.setFormulaValue(r),u.calcID=this._calcID))),(r==""||r==undefined)&&(r=""),s=ej.isNullOrUndefined(ej.cultureObject)?this._parseDouble(r):ej.parseFloat(r,0,ej.cultureObject.name),r[r.length-1]!="%"||isNaN(s)||(r=(Number(s)/100).toString()),r};this._handleEmbeddedEs=function(n){for(var t=0,i,r;t>-1&&(t=n.indexOf(this._string_EP,t))>-1;){if(this._notInBlock(n,t)){for(i=t;i>0&&(this._isDigit(n[i-1])||n[i-1]==this.getParseDecimalSeparator());)i--;if(i!=t&&(i==0||!this._isUpperChar(n[i-1]))){for(r=t+this._string_EP.length;r<n.length&&this._isDigit(n[r]);)r++;r!=t+this._string_EP.length&&(n=n.substring(0,t)+this.char_EP+this._substring(n,t+this._string_EP.length))}}t+=1}for(t=0;t>-1&&(t=n.indexOf(this._string_EM,t))>-1;){if(this._notInBlock(n,t)){for(i=t;i>0&&(this._isDigit(n[i-1])||n[i-1]==this.getParseDecimalSeparator());)i--;if(i!=t&&(i==0||!this._isUpperChar(n[i-1]))){for(r=t+this._string_EM.length;r<n.length&&this._isDigit(n[r]);)r++;r!=t+this._string_EM.length&&(n=n.substring(0,t)+this.char_EM+this._substring(n,t+this._string_EM.length))}}t+=1}for(t=0;t>-1&&(t=n.indexOf(this._string_E,t))>-1&&n[0]!=this.bMARKER;){if(this._notInBlock(n,t)){for(i=t;i>0&&(this._isDigit(n[i-1])||n[i-1]==this.getParseDecimalSeparator());)i--;if(i!=t&&(i==0||!this._isUpperChar(n[i-1]))){for(r=t+this._string_E.length;r<n.length&&this._isDigit(n[r]);)r++;r==t+this._string_E.length||i!=-1&&this._isUpperChar(n[i])||(n=n.substring(0,t)+this.char_EP+this._substring(n,t+this._string_E.length))}}t+=1}return n};this._handleIterations=function(n){this._inHandleIterations=!0;for(var i=0,t=0,r=1,u=!0;r<this._iterationMaxCount-1&&(u||!this._checkUnderTolerance(t,i));)u=!1,this.getIterationValues().containsKey(this.cell)||this.getIterationValues().add(this.cell,"0"),this.getIterationValues().add(this.cell,n.getFormulaValue()==""?"0":n.getFormulaValue()),n.setFormulaValue(this.computeFormula(n.getParsedFormula())),i=t,t=this._parseDouble(n.getFormulaValue()),isNaN(t)&&(t=0),r++;this.getIterationValues.getItem(this.cell,n.getFormulaValue()==""?"0":n.getFormulaValue());this._inHandleIterations=!1};this._indexOfAny=function(n,t){for(var i=0;i<n.length;i++)if(t.indexOf(n[i])>-1)return i;return-1};this._lastIndexOfAny=function(n,t){for(var i=n.length-1;i>-1;i--)if(t.indexOf(n[i])>-1)return i;return-1};this._initLibraryFunctions=function(){this._libraryFunctions=new HashTable;this._addFunction("Countifs","ComputeCOUNTIFS");this._addFunction("SUM","computeSum");this._addFunction("EncodeURL","computeEncodeURL");this._addFunction("CHAR","computeChar");this._addFunction("CODE","computeCode");this._addFunction("UNICODE","computeUniCode");this._addFunction("UNICHAR","computeUniChar");this._addFunction("UPPER","computeUpper");this._addFunction("LOWER","computeLower");this._addFunction("LEN","computeLen");this._addFunction("MID","computeMid");this._addFunction("LEFT","computeLeft");this._addFunction("CLEAN","computeClean");this._addFunction("REPT","computeRept");this._addFunction("RIGHT","computeRight");this._addFunction("REPLACE","computeReplace");this._addFunction("EXACT","computeExact");this._addFunction("FIND","computeFind");this._addFunction("TRIM","computeTrim");this._addFunction("SEARCH","computeSearch");this._addFunction("SUBSTITUTE","computeSubstitute");this._addFunction("PROPER","computeProper");this._addFunction("T","computeT");this._addFunction("NUMBERVALUE","computeNumberValue");this._addFunction("CONCATENATE","computeConcatenate");this._addFunction("VALUE","computeValue");this._addFunction("DOLLAR","computeDollar");this._addFunction("FIXED","computeFixed");this._addFunction("BIN2DEC","computeBin2Dec");this._addFunction("BIN2OCT","computeBin2Oct");this._addFunction("BIN2HEX","computeBin2Hex");this._addFunction("DEC2BIN","computeDec2Bin");this._addFunction("DEC2OCT","computeDec2Oct");this._addFunction("HEX2BIN","computeHex2Bin");this._addFunction("HEX2OCT","computeHex2Oct");this._addFunction("BITAND","computeBitAnd");this._addFunction("BITOR","computeBitOr");this._addFunction("BITLSHIFT","computeBitLShift");this._addFunction("BITRSHIFT","computeBitRShift");this._addFunction("BITXOR","computeBitXor");this._addFunction("DATE","computeDate");this._addFunction("DATEVALUE","computeDatevalue");this._addFunction("DAY","computeDay");this._addFunction("DAYS","computeDays");this._addFunction("DAYS360","computeDays360");this._addFunction("EDATE","computeEDate");this._addFunction("EOMONTH","computeEOMonth");this._addFunction("HOUR","computeHour");this._addFunction("ISOWEEKNUM","computeISOWeeknum");this._addFunction("MINUTE","computeMinute");this._addFunction("MONTH","computeMonth");this._addFunction("NETWORKDAYS","computeNetworkDays");this._addFunction("NETWORKDAYS.INTL","computeNetworkDaysOintl");this._addFunction("NOW","computeNow");this._addFunction("SECOND","computeSecond");this._addFunction("TIME","computeTime");this._addFunction("TIMEVALUE","computeTimevalue");this._addFunction("TODAY","computeToday");this._addFunction("WEEKDAY","computeWeekday");this._addFunction("WEEKNUM","computeWeeknum");this._addFunction("WORKDAY","computeWorkDay");this._addFunction("WORKDAY.INTL","computeWorkDayOintl");this._addFunction("YEAR","computeYear");this._addFunction("ADDRESS","computeAddress");this._addFunction("AREAS","computeAreas");this._addFunction("CHOOSE","computeChoose");this._addFunction("COLUMN","computeColumn");this._addFunction("COLUMNS","computeColumns");this._addFunction("FORMULATEXT","computeFormulaText");this._addFunction("HYPERLINK","computeHyperlink");this._addFunction("HLOOKUP","computeHLookUp");this._addFunction("INDEX","computeIndex");this._addFunction("INDIRECT","computeIndirect");this._addFunction("LOOKUP","computeLookUp");this._addFunction("OFFSET","computeOffSet");this._addFunction("TRANSPOSE","computeTranspose");this._addFunction("LOGNORM.INV","computeLognormOinv");this._addFunction("NORM.INV","computeNormOinv");this._addFunction("NORM.DIST","computeNormOdist");this._addFunction("NORM.S.DIST","computeNormOsODist");this._addFunction("NORM.S.INV","computeNormOsOInv");this._addFunction("PERMUT","computePermut");this._addFunction("PERMUTATIONA","computePermutationA");this._addFunction("STANDARDIZE","computeStandardize");this._addFunction("BINOM.DIST","computeBinomOdist");this._addFunction("BINOM.INV","computeBinomOInv");this._addFunction("CHISQ.INV.RT","computeChisqOinvOrt");this._addFunction("CHISQ.INV","computeChisqOinv");this._addFunction("CHISQ.DIST.RT","computeChisqOdistOrt");this._addFunction("F.DIST","computeFOdist");this._addFunction("GAMMALN","computeGammaln");this._addFunction("CONFIDENCE.NORM","computeConfidenceOnorm");this._addFunction("EXPON.DIST","computeExponOdist");this._addFunction("FISHER","computeFisher");this._addFunction("FISHERINV","computeFisherInv");this._addFunction("GAMMALN.PRECISE","computeGammalnOPrecise");this._addFunction("AVERAGE","computeAverage");this._addFunction("AVERAGEA","computeAverageA");this._addFunction("POISSON.DIST","computePoissonODist");this._addFunction("WEIBULL.DIST","computeWeiBullODist");this._addFunction("F.INV.RT","computeFOinvOrt");this._addFunction("T.DIST","computeTOdist");this._addFunction("MAX","computeMax");this._addFunction("MAXA","computeMaxa");this._addFunction("MEDIAN","computeMedian");this._addFunction("MIN","computeMin");this._addFunction("MINA","computeMina");this._addFunction("PERCENTRANK.INC","computePercentrankInc");this._addFunction("PERCENTILE","computePercentile");this._addFunction("RANK.EQ","computeRankOEq");this._addFunction("COUNT","computeCount");this._addFunction("COUNTA","computeCounta");this._addFunction("DEVSQ","computeDevsq");this._addFunction("F.DIST.RT","computeFOdistORt");this._addFunction("FORECAST","computeForecast");this._addFunction("GEOMEAN","computeGeomean");this._addFunction("HARMEAN","computeHarmean");this._addFunction("INTERCEPT","computeIntercept");this._addFunction("LARGE","computeLarge");this._addFunction("SMALL","computeSmall");this._addFunction("LOGNORM.DIST","computeLognormOdist");this._addFunction("AVEDEV","computeAvedev");this._addFunction("COUNTBLANK","computeCountblank");this._addFunction("STDEV.P","computeStdevOp");this._addFunction("STDEV.S","computeStdevOS");this._addFunction("STDEVA","computeStdeva");this._addFunction("STDEVPA","computeStdevpa");this._addFunction("T.INV","computeTOinv");this._addFunction("VAR.P","computeVarp");this._addFunction("VARA","computeVara");this._addFunction("VARPA","computeVarpa");this._addFunction("CORREL","computeCorrel");this._addFunction("PERCENTILE.EXC","computePercentileExc");this._addFunction("PERCENTILE.INC","computePercentileOInc");this._addFunction("TRIMMEAN","computeTrimmean");this._addFunction("RSQ","computeRsq");this._addFunction("PEARSON","computePearson");this._addFunction("CHIDIST","computeChidist");this._addFunction("MODE.MULT","computeModeOMult");this._addFunction("NORMINV","computeNormInv");this._addFunction("SLOPE","computeSlope");this._addFunction("ABS","computeAbs");this._addFunction("ACOS","computeAcos");this._addFunction("ACOSH","computeAcosh");this._addFunction("ACOT","computeAcot");this._addFunction("ACOTH","computeAcoth");this._addFunction("ARABIC","computeArabic");this._addFunction("ASIN","computeAsin");this._addFunction("ATAN","computeAtan");this._addFunction("ATAN2","computeAtan2");this._addFunction("BIGMUL","computeBigMul");this._addFunction("CEILING.MATH","computeCeilingMath");this._addFunction("CEILING","computeCeiling");this._addFunction("COMBIN","computeCombin");this._addFunction("COMBINA","computeCombinA");this._addFunction("COS","computeCos");this._addFunction("COSH","computeCosh");this._addFunction("COT","computeCot");this._addFunction("CSC","computeCsc");this._addFunction("CSCH","computeCsch");this._addFunction("DECIMAL","computeDecimal");this._addFunction("DEGREES","computeDegrees");this._addFunction("ISTEXT","computeIsText");this._addFunction("EXP","computeExp");this._addFunction("EVEN","computeEven");this._addFunction("FACT","computeFact");this._addFunction("FACTDOUBLE","computeFactdouble");this._addFunction("FLOOR","computeFloor");this._addFunction("GCD","computeGcd");this._addFunction("INT","computeInt");this._addFunction("LCM","computeLcm");this._addFunction("LN","computeLn");this._addFunction("LOG","computeLog");this._addFunction("MUNIT","computeMunit");this._addFunction("PI","computePI");this._addFunction("PRODUCT","computeProduct");this._addFunction("SEC","computeSecant");this._addFunction("SERIESSUM","computeSeriessum");this._addFunction("SIGN","computeSign");this._addFunction("SIN","computeSin");this._addFunction("SINH","computeSinh");this._addFunction("SQRT","computeSqrt");this._addFunction("SUBTOTAL","computeSubTotal");this._addFunction("SUMIF","computeSumif");this._addFunction("TRUNC","computeTrunc");this._addFunction("TRUNCATE","computeTruncate");this._addFunction("TAN","computeTan");this._addFunction("LOG10","computeLogTen");this._addFunction("COTH","computeCoth");this._addFunction("RADIANS","computeRadians");this._addFunction("ODD","computeOdd");this._addFunction("MOD","computeMod");this._addFunction("RAND","computeRand");this._addFunction("RANDBETWEEN","computeRandBetween");this._addFunction("SECH","computeSecanth");this._addFunction("AND","computeAnd");this._addFunction("FALSE","computeFalse");this._addFunction("IF","computeIf");this._addFunction("IFERROR","computeIfError");this._addFunction("NOT","computeNot");this._addFunction("OR","computeOr");this._addFunction("TRUE","computeTrue");this._addFunction("XOR","computeXor");this._addFunction("IFS","computeIfs");this._addFunction("CELL","computeCell");this._addFunction("ERROR.TYPE","computeErrorType");this._addFunction("INFO","computeInfo");this._addFunction("ISBLANK","computeIsBlank");this._addFunction("ISERR","computeIsErr");this._addFunction("ISERROR","computeIsError");this._addFunction("ISEVEN","computeIsEven");this._addFunction("ISFORMULA","computeIsFormula");this._addFunction("ISLOGICAL","computeIsLogical");this._addFunction("ISNA","computeIsNA");this._addFunction("ISNONTEXT","computeIsNonText");this._addFunction("ISNUMBER","computeIsNumber");this._addFunction("ISODD","computeIsOdd");this._addFunction("ISREF","computeIsRef");this._addFunction("ISTEXT","computeIsText");this._addFunction("N","computeN");this._addFunction("NA","computeNA");this._addFunction("SHEET","computeSheet");this._addFunction("SHEETS","computeSheets");this._addFunction("TYPE","computeType");this._addFunction("ROW","computeRow");this._addFunction("ROWS","computeRows");this._addFunction("MATCH","computeMatch");this._addFunction("PMT","computePmt");this._addFunction("COUNTIF","computeCountif");this._addFunction("ROUND","computeRound");this._addFunction("ROUNDDOWN","computeRoundDown");this._addFunction("ROUNDUP","computeRoundUp");this._addFunction("MMULT","computeMmult");this._addFunction("PV","computePv");this._addFunction("NORMSDIST","computeNormsDist");this._addFunction("NORMDIST","computeNormDist");this._addFunction("SUMPRODUCT","computeSumProduct");this._addFunction("STDEV","computeStdev");this._addFunction("VLOOKUP","computeVLookUp");this._addFunction("ASINH","computeAsinh");this._addFunction("ATANH","computeAtanh");this._addFunction("BASE","computeBase");this._addFunction("AVERAGEIF","computeAverageIf");this._addFunction("AVERAGEIFS","computeAverageIfS");this._addFunction("BESSELI","computeBesselI");this._addFunction("BESSELJ","computeBesselJ");this._addFunction("BESSELK","computeBesselK");this._addFunction("BESSELY","computeBesselY");this._addFunction("TEXT","computeText");this._addFunction("POWER","computePow")};this._comb=function(n,t){for(var r,u=1,i=n+1;i<=t;++i)u=u*i;for(r=1,i=2;i<=t-n;++i)r=r*i;return u/r};this._finv=function(n,t,i){for(var o=Math.exp(this._gammaln((t+i)/2)-this._gammaln(t/2)-this._gammaln(i/2)+t/2*Math.log(t/i)),r=o,u=r/2,e=0,f=100,s=3,h=0;f==100&&s>0;)for(s--,r=r/2,u=r/2,f=0;f<100;++f){if(h++,e=1-o*this._fdist(r,t,i),Math.abs((e-n)/n)<1e-7)break;e>n?r=r+u:(u=u/2,r-u<0&&(u=r/2),r=r-u)}return f==100&&(r=-1),r};this._tProbabilityDensity=function(n,t){var i=this._gammaFunction(.5*t+.5),r=Math.pow(1+n*n/t,-.5*t-.5),u=Math.sqrt(t*Math.PI)*this._gammaFunction(.5*t);return i*r/u};this._tCumulativeDensity=function(n,t){if(isNaN(n))return NaN;if(n==0)return.5;if(n>0){var i=t/(n*n+t);return 1-.5*this._rIBetaFunction(i,.5*t,.5)}return 1-this._tCumulativeDensity(-n,t)};this._sign=function(n){return n===0||isNaN(n)?n:n>0?1:-1};this._var=function(n){for(var u,f,i=0,r=n.length,t=0;t<r;++t)i+=n[t];for(i=i/r,u=0,t=0;t<r;++t)f=n[t]-i,u+=f*f;return u/(r-1)};this._tCumulativeDistributionInverse=function(n,t){var r,i,u,f,o,s;if(n<0||n>1)throw"Probability must be between 0 and 1";if(n==0)return Number.NEGATIVE_INFINITY;if(n==1)return Number.POSITIVE_INFINITY;if(n==.5)return 0;switch(t){case 1:return Math.tan(Math.PI*(n-.5));case 2:return r=4*n*(1-n),2*(n-.5)*Math.sqrt(2/r);case 4:var r=4*n*(1-n),e=Math.sqrt(r),h=Math.cos(1/3*Math.acos(e))/e;return this._sign(n-.5)*2*Math.sqrt(h-1);default:for(i=t>6?this._standardNormalCumulativeDistributionFunctionInverse(n):this._tCumulativeDistributionInverse(n,4),u=0;u<50;u++){if(f=this._tCumulativeDensity(i,t),o=Math.abs(n-f),o<.0001)return i;s=this._tProbabilityDensity(i,t);i=i+(n-f)/s}throw"Solution did not converge";}};this._fdist=function(n,t,i){for(var s,p,f=32,e=0,c=n,o=(c-e)/f,l=o/3,y=this._fdensity(e,t,i)+this._fdensity(c,t,i),u=0,r=1;r<f;r+=2)u=u+4*this._fdensity(e+r*o,t,i);for(s=0,r=2;r<f;r+=2)s=s+2*this._fdensity(e+r*o,t,i);for(var h=l*(y+s+u),a=h,v=0,v=0;v<10;++v){for(f=f*2,s+=u/2,u=0,o=(c-e)/f,r=0;r<f;++r)r%2==1&&(p=this._fdensity(e+o*r,t,i),u=u+p);if(u=4*u,l=o/3,h=l*(y+s+u),Math.abs((h-a)/a)<1e-7)break;a=h}return h};this._fdensity=function(n,t,i){return Math.pow(n,(t-2)/2)/Math.pow(1+t*n/i,(t+i)/2)};this._normaldist=function(n,t,i){var h=32,f,c,o,r,p;n>t?(f=t-(n-t),c=n):(f=n,c=t+(t-n));var e=(c-f)/h,l=e/3,y=this._normaldensity(f,t,i)+this._normaldensity(c,t,i),u=0;for(r=1;r<h;r+=2)u=u+4*this._normaldensity(f+r*e,t,i);for(o=0,r=2;r<h;r+=2)o=o+2*this._normaldensity(f+r*e,t,i);for(var s=l*(y+o+u),a=s,v=0,v=0;v<10;++v){for(h*=2,o=o+u/2,u=0,e=e=(c-f)/h,r=0;r<h;++r)r%2==1&&(p=this._normaldensity(f+e*r,t,i),u+=p);if(u=4*u,l=e/3,s=l*(y+o+u),Math.abs((s-a)/a)<1e-7)break;a=s}return n>t?s+(1-s)/2:(1-s)/2};this._normaldensity=function(n,t,i){return 1/(Math.sqrt(2*Math.PI)*i)*Math.exp(-(n-t)*(n-t)/(2*i*i))};this._standardNormalCumulativeDistribution=function(n){if(n<0)return 1-this._standardNormalCumulativeDistribution(-n);var i=this._standardNormalProbabilityDensityFunction(n),t=1/(1+.2316419*n);return 1-i*(.31938153*t+-.356563782*Math.pow(t,2)+1.781477937*Math.pow(t,3)+-1.821255978*Math.pow(t,4)+1.330274429*Math.pow(t,5))};this._normalinv=function(n,t,i){var r=t;r=n<.05?t-2*i:n<.5?t:n<.95?t+2*i:t+5*i;for(var u=r/2,e=0,f=100,o=3,s=0;f==100&&o>0;)for(o--,r=r/2,u=r/2,f=0;f<100;++f){if(s++,e=this._normaldist(r,t,i),Math.abs((e-n)/n)<1e-7)break;e<n?r=r+u:(u=u/2,r-u<0&&(u=r/2),r=r-u)}return f==100&&(r=-1),r};this._normalCumulativeDistributionFunctionInverse=function(n,t,i){var r=this._standardNormalCumulativeDistributionInverse(n);return i*r+t};this._standardNormalCumulativeDistributionInverse=function(n){var i;if(n<0||n>1)throw"Probability must be between 0 and 1";if(n==0)return Number.NEGATIVE_INFINITY;if(n==1)return Number.POSITIVE_INFINITY;if(n==.5)return 0;var u=-.0077848940024302926,f=-.32239645804113648,e=-2.4007582771618381,o=-2.5497325393437338,s=4.3746641414649678,h=2.9381639826987831,c=.0077846957090414622,l=.32246712907003983,a=2.445134137142996,v=3.7544086619074162,r=.02425,y=1-r,t;return 0<n&&n<r?(t=Math.sqrt(-2*Math.log(n)),(((((u*t+f)*t+e)*t+o)*t+s)*t+h)/((((c*t+l)*t+a)*t+v)*t+1)):r<=n&&n<=y?(t=n-.5,i=t*t,(((((-39.696830286653757*i+220.9460984245205)*i+-275.92851044696869)*i+138.357751867269)*i+-30.66479806614716)*i+2.5066282774592392)*t/(((((-54.476098798224058*i+161.58583685804089)*i+-155.69897985988661)*i+66.80131188771972)*i+-13.280681552885721)*i+1)):(t=Math.sqrt(-2*Math.log(1-n)),-(((((u*t+f)*t+e)*t+o)*t+s)*t+h)/((((c*t+l)*t+a)*t+v)*t+1))};this._normalProbabilityDensity=function(n,t,i){var r=(n-t)/i;return.398942280401433*Math.exp(-.5*r*r)/i};this._standardNormalProbabilityDensityFunction=function(n){return.398942280401433*Math.exp(-.5*n*n)};this._standardNormalCumulativeDistributionFunction=function(n){if(n<0)return 1-this._standardNormalCumulativeDistributionFunction(-n);var i=this._standardNormalProbabilityDensityFunction(n),t=1/(1+.2316419*n);return 1-i*(.31938153*t+-.356563782*Math.pow(t,2)+1.781477937*Math.pow(t,3)+-1.821255978*Math.pow(t,4)+1.330274429*Math.pow(t,5))};this._standardNormalCumulativeDistributionFunctionInverse=function(n){var i;if(n<0||n>1)throw"Probability must be between 0 and 1";if(n==0)return Number.NEGATIVE_INFINITY;if(n==1)return Number.POSITIVE_INFINITY;if(n==.5)return 0;var u=-.0077848940024302926,f=-.32239645804113648,e=-2.4007582771618381,o=-2.5497325393437338,s=4.3746641414649678,h=2.9381639826987831,c=.0077846957090414622,l=.32246712907003983,a=2.445134137142996,v=3.7544086619074162,r=.02425,y=1-r,t;return 0<n&&n<r?(t=Math.sqrt(-2*Math.log(n)),(((((u*t+f)*t+e)*t+o)*t+s)*t+h)/((((c*t+l)*t+a)*t+v)*t+1)):r<=n&&n<=y?(t=n-.5,i=t*t,(((((-39.696830286653757*i+220.9460984245205)*i+-275.92851044696869)*i+138.357751867269)*i+-30.66479806614716)*i+2.5066282774592392)*t/(((((-54.476098798224058*i+161.58583685804089)*i+-155.69897985988661)*i+66.80131188771972)*i+-13.280681552885721)*i+1)):(t=Math.sqrt(-2*Math.log(1-n)),-(((((u*t+f)*t+e)*t+o)*t+s)*t+h)/((((c*t+l)*t+a)*t+v)*t+1))};this._chiSquaredProbabilityDensityFunction=function(n,t){var i=this._gammaFunction(.5*t),r=1/(Math.pow(2,.5*t)*i);return r*Math.pow(n,.5*t-1)*Math.exp(-.5*n)};this._binomdist=function(n,t,i){var f=1-i,o=Math.pow(f,n),e=0,r=1*Math.pow(f,n),u;if(r==0)return NaN;for(u=0;u<=t;++u)if(e+=r,r=r*i/f*(n-u)/(u+1),!isFinite(r)||isNaN(r)){e=NaN;break}return e};this._gammadensity=function(n,t,i){return Math.pow(i/t,n-1)*Math.exp(-i/t)/(t*Math.exp(this._gammaln(n)))};this.minValue=-179769e303;this.maxValue=2147483647;this._charTable=[this._string_empty,"A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"];this._stdevdotP=function(n){for(var r=n.length,i=0,u=0,f=0,t=0;t<r;++t)i=i+n[t];for(i=i/r,t=0;t<r;t++)u=u+Math.pow(n[t]-i,2),f++;return Math.sqrt(u/f)};this._determinant=function(n,t){var o=1,e=0,c=parseInt(t.toString()),s=[c,c],i,r,h,u,f;if(t==1)return n[0];for(e=0,f=0;f<t;f++){for(h=0,u=0,i=0;i<t;i++)for(r=0;r<t;r++)s[(i,r)]=0,i!=0&&r!=f&&(s[(h,u)]=n[(i,r)],u<t-2?u++:(u=0,h++));e=e+o*n[f]*this._determinant(s,t-1);o=-1*o}return e};this._factorialTable=[1,1,2,6,24,120,720,5040,40320,362880,3628800,39916800,479001600];this._mean=function(n){return this._mean(n,1)};this._negbinomdensity=function(n,t,i){return this._comb(t-1,n+t-1)*Math.pow(i,t)*Math.pow(1-i,n)};this._sd=function(n,t){var r=n.length,f,u,i;for(t=0,i=0;i<r;++i)t+=n[i];for(t=t/r,f=0,u=0,i=0;i<r;++i)u=n[i]-t,f+=u*u;return r==1?0:Math.sqrt(f/(r-1))};this._betaCumulativeDist=function(n,t,i){return this._rIBetaFunction(n,t,i)};this._betaProbabilityDens=function(n,t,i){var r=this._betaFunction(t,i);return Math.pow(n,t-1)*Math.pow(1-n,i-1)/r};this._critbinom=function(n,t,i){var r=n,f=n,e=1,u=1;do{if(f=Math.floor(f/2)+1,e>=i){if(u=this._binomdist(n,r-1,t),isNaN(u))return this.maxValue;if(u<i&&u>0)break;r=r-f}else{if(u=this._binomdist(n,r+1,t),u>=i){r=r+1;break}r=r+f}e=this._binomdist(n,r,t)}while(r<n&&r>0);return r};this._newnormalinv=function(n){var f=[-39.696830286653757,220.9460984245205,-275.92851044696869,138.357751867269,-30.66479806614716,2.5066282774592392],e=[-54.476098798224058,161.58583685804089,-155.69897985988661,66.80131188771972,-13.280681552885721],i=[-.0077848940024302926,-.32239645804113648,-2.4007582771618381,-2.5497325393437338,4.3746641414649678,2.9381639826987831],u=[.0077846957090414622,.32246712907003983,2.445134137142996,3.7544086619074162],o=.02425,s=1-o,t=0,r=0;return n<o?(t=Math.sqrt(-2*Math.log(n)),(((((i[0]*t+i[1])*t+i[2])*t+i[3])*t+i[4])*t+i[5])/((((u[0]*t+u[1])*t+u[2])*t+u[3])*t+1)):s<n?(t=Math.sqrt(-2*Math.log(1-n)),-(((((i[0]*t+i[1])*t+i[2])*t+i[3])*t+i[4])*t+i[5])/((((u[0]*t+u[1])*t+u[2])*t+u[3])*t+1)):(t=n-.5,r=t*t,(((((f[0]*r+f[1])*r+f[2])*r+f[3])*r+f[4])*r+f[5])*t/(((((e[0]*r+e[1])*r+e[2])*r+e[3])*r+e[4])*r+1))};this._chiinv=function(n,t){for(var i=n,r=Number(i/2),f=0,u=100,e=3;u==100&&e>0;)for(e--,i=i/2,r=i/2,u=0;u<100;++u){if(f=1-this._chidist(i,t),Math.abs((f-n)/n)<1e-7)break;f>n?i=i+r:(r=r/2,i-r<0&&(r=i/2),i=i-r)}return u==100&&(i=-1),i};this._chidist=function(n,t){var c=[.09654008851,.09563872008,.09384439908,.0911738787,.087652093,.08331192423,.07819389579,.07234579411,.06582222278,.05868409348,.05099805926,.04283589802,.03427386291,.02539206531,.01627439473,.00701861001],l=[.04830766569,.14447196158,.23928736225,.33186860228,.42135127613,.50689990893,.58771575724,.66304426693,.73218211874,.79448379597,.84936761373,.89632115577,.93490607594,.96476225559,.98561151155,.99726386185],i=t/2,a=1/(Math.pow(2,i)*Math.exp(this._gammaln(i))),u;i=i-1;var o=0,s=n,f=(o+s)/2,h=(s-o)/2,e=0,r;for(u=0;u<16;++u)r=l[u]*h,e=Number(e)+Number(c[u]*(Math.pow(f+r,i)*Math.exp(-(f+r)/2)+Math.pow(f-r,i)*Math.exp(-(f-r)/2)));return Number(a)*Number(e)*Number(h)};this._gammaln=function(n){var r=[.918938533204673,.000595238095238,.000793650793651,.002777777777778,.083333333333333],t=n,i=0,u;if(t<7){for(i=t,t++;t<7;)i=i*t,t<7&&t++;i=-Math.log(i)}return u=this._parseDouble((1/(t*t)).toString()),i+(t-this._parseDouble("0.5"))*Math.log(t)-t+r[0]+(((-r[1]*u+r[2])*u-r[3])*u+r[4])/t};this._pearson=function(n,t,i){for(var u=0,f=0,r=0;r<i;++r)u+=n[r],f+=t[r];u=u/i;f=f/i;var s=0,h=0,c=0,e,o;for(r=0;r<i;++r)o=n[r]-u,e=t[r]-f,s+=o*e,h+=o*o,c+=e*e;return s/Math.sqrt(h*c)};this._fProbabilityDensity=function(n,t,i){if(t<=0||i<=0)throw"k1 and k2 must be greater than 0.";if(n==0)return 0;var r=Math.pow(t*n,.5*t),u=Math.pow(i,.5*i),f=Math.pow(t*n+i,.5*(t+i)),e=r*u/f,o=this._betaFunction(.5*t,.5*i),s=n*o;return e/s};this._fCumulativeDensity=function(n,t,i){if(t<=0||i<=0)throw"k1 and k2 must be greater than 0.";if(n<0)throw"x must be greater than 0.";if(n==0)return 0;var r=t*n/(t*n+i),u=this._rIBetaFunction(r,.5*t,.5*i);return Math.min(1,u)};this._rIBetaFunction=function(n,t,i){var s,r,h,c;if(n<=0)return 0;if(n>=1)return 1;if(t%1==0&&i%1==0&&t+i>0)return this._rIBetaFunction1(n,t,i);if(i%1==0&&t+i<172)return this._rIBetaFunction2(n,t,i);if(t%1==0&&t+i<172)return 1-this._rIBetaFunction2(1-n,i,t);if(t==.5&&i==.5)return 2/Math.PI*Math.atan(Math.sqrt(n/(1-n)));if(t==.5&&i%.5==0)return 1-this._rIBetaFunction(1-n,i,.5);if(t%.5==0&&i==.5){if(t<45){var u=0,e=Math.round(t-.5),l=this._parseDouble(this.computeGammaln("0.5".toString()));for(r=0;r<e;r++)u=u+Math.exp(this._gammaln(r+1)-this._gammaln(r+1.5)-l)*Math.pow(n,r);return this._rIBetaFunction(n,.5,.5)-Math.sqrt(n*(1-n))*u}var a=this._betaFunction(t,.5),o=Math.sqrt(1-Math.pow(a*Math.sqrt((t-1)/Math.PI)*this._epsilon,1/(t-1))),v=[.066671344308688,.14945134915058,.21908636251598,.26926671931,.29552422471475,.29552422471475,.26926671931,.21908636251598,.14945134915058,.066671344308688],y=[.013046735791414,.067468316655507,.16029521585049,.28330230293538,.42556283050918,.57443716949081,.71669769706462,.83970478414951,.93253168334449,.98695326420859],f=Math.sqrt(1-n),u=0;for(r=0;r<10;r++)u=u+v[r]*Math.pow(1-Math.pow((o-f)*y[r]+f,2),t-1);return s=Math.exp(this._gammaln(t+.5)-this._gammaln(t)-this._gammaln(.5)),(o-f)*s*u}if(t%.5==0&&i%.5==0){var u=0,e=Math.round(i-.5),p=this._gammaln(t),w=Math.pow(n,t);for(r=0;r<e;r++)u=u+Math.exp(this._gammaln(t+r+.5)-p-this._gammaln(r+1.5))*Math.pow(1-n,r)*w;return h=this._rIBetaFunction(n,t,.5)+Math.sqrt(1-n)*u,Math.max(0,Math.min(1,h))}return n>.5?1-this._rIBetaFunction(1-n,i,t):(c=this._iBetaFunction(n,t,i)/this._betaFunction(t,i),Math.max(0,Math.min(1,c)))};this._rIBetaFunction1=function(n,t,i){var u=0,f=t+i-1,r;if(f<21){for(r=t;r<t+i;r++)u=u+Math.pow(n,r)*Math.pow(1-n,f-r)/(this._factorial(r)*this._factorial(f-r));u=u*this._factorial(f)}else for(r=t;r<t+i;r++)u=u+Math.pow(n,r)*Math.pow(1-n,f-r)*this._combinations(f,r);return Math.max(0,Math.min(1,u))};this._rIBetaFunction2=function(n,t,i){var u,r;if(t+i>172)throw"Cannot currently compute RegularizedIncompleteBetaFunction for a + b > 172";for(u=0,r=1;r<i+1;r++)u=u+Math.pow(1-n,r-1)*Math.exp(this._gammaln(t+r-1)-this._gammaln(r));return u*Math.pow(n,t)*Math.exp(-this._gammaln(t))};this._iBetaFunction=function(n,t,i){var r,u;if(n==0)return 0;if(n==1)return this._betaFunction(t,i);if(n<=.9)return this._pIBetaFunction(0,n,t,i);if(n<=.99)return r=this._pIBetaFunction(0,.9,t,i),u=this._pIBetaFunction(.9,n,t,i),r+u;var r=this._pIBetaFunction(0,.9,t,i),u=this._pIBetaFunction(.9,.99,t,i),f=this._pIBetaFunction(.99,n,t,i);return r+u+f};this._pIBetaFunction=function(n,t,i,r){var u,s;if(t==n)return 0;if(t<n)return NaN;u=1e3;i<1&&(u=8e4);var e=(t-n)/u,f=n+.5*e,o=0;for(s=0;s<u;s++)o=o+Math.pow(f,i-1)*Math.pow(1-f,r-1)*e,f=f+e;return o};this._betaFunction=function(n,t){return n+t>143?t>20?2.5066282746310002*Math.pow(n,n-.5)*Math.pow(t,t-.5)/Math.pow(n+t,n+t-.5):this._gammaFunction(t)*Math.pow(n,-t):this._gammaFunction(n)*this._gammaFunction(t)/this._gammaFunction(n+t)};this._gammaFunction=function(n){var t,i;if(n>143)throw"Cannot currently compute gamma function for z > 143";if(n>0&&n<21&&n%1==0)return this._factorial(Math.round(n-1));if(n>0&&n<11&&n%.5==0)return t=parseInt(n),i=1.77245385090552,i*this._factorial(2*t)/(Math.pow(4,t)*this._factorial(t));var r=1+1/(12*n)+1/(288*n*n)-139/(51840*Math.pow(n,3))-571/(2488320*Math.pow(n,4))+163879/(209018880*Math.pow(n,5))+5246819/(75246796800*Math.pow(n,6));return Math.pow(n,n-.5)*Math.exp(-n)*2.5066282746310002*r};this._epsilon=494065645841247e-338;this._factorial=function(n){var t,i;if(n<0)throw"Factorial not defined for negative n";if(n>20)throw"Answer will exceed max long";for(t=1,i=n;i>0;i--)t=t*i;return t};this._combinations=function(n,t){var i=this._logCombin(n,t),r=Math.exp(i);return Math.round(r)};this._logCombin=function(n,t){return this._logFactorial(n)-this._logFactorial(t)-this._logFactorial(n-t)};this._logFactorial=function(n){for(var t=0,i=2;i<=n;i++)t=t+Math.log(i);return t};this._isCellReference=function(n){var e,f,t,i,u,r;if(n==""||(n=this.setTokensForSheets(n),e=this._sheetToken(n),f=!1,e!=""&&(n=n.split(this.sheetToken).join("")),t=!1,i=!1,n.indexOf(":")!=n.lastIndexOf(":")))return!1;for(u=n.split("").join(this.getParseArgumentSeparator()).split(this.getParseArgumentSeparator()),r=0;r<u.length;r++)if(this._isLetter(u[r]))t=!0;else if(this._isDigit(u[r])){if(u[r]===0)return!1;i=!0}else if(u[r]==":")t&&i&&(f=!0),t=!1,i=!1;else return!1;return n.indexOf(":")>-1&&n.indexOf(this.tic)==-1?f&&t&&i?!0:(!t||i)&&(t||!i)||f?!1:!0:t&&i&&n.indexOf(this.tic)==-1?!0:!1};this._isDate=function(n){if(typeof n=="object"||ej.parseDate(n)!=null){var t=new Date(Date.parse(n));return t>=this._dateTime1900?t:"NaN"}return"NaN"};this._isDigit=function(n){var t=n.charCodeAt(0);return t>47&&t<58?!0:!1};this._isHLookupCachingEnabled=function(){return(this._enableLookupTableCaching&3)!=0||(this._enableLookupTableCaching&2)!=0};this._isVLookupCachingEnabled=function(){return(this._enableLookupTableCaching&3)!=0||(this._enableLookupTableCaching&1)!=0};this._isLetter=function(n){var t=n.charCodeAt(0);return t>=65&&t<=90||t>=97&&t<=122?!0:!1};this._isLetterOrDigit=function(n){var t=n.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=90||t>=97&&t<=122?!0:!1};this._isLookupCachingEnabled=function(){return(this._enableLookupTableCaching&3)!=0||(this._enableLookupTableCaching&1)!=0||(this._enableLookupTableCaching&2)!=0};this._isOptimizedMatchesEnabled=function(){return(this._enableLookupTableCaching&4)!=0};this._isRange=function(n){var u=!1,r=n.indexOf(":"),t,i;if(r>1&&r<n.length-2&&(t=r-1,this._isDigit(n[t]))){for(i=!1,t--;t>0&&this._isDigit(n[t]);)t--;if(this._isLetter(n[t])){for(t--;t>=0&&this._isLetter(n[t]);)t--;t>-1&&n[t]==this._string_fixedreference[0]&&t--;t<0?i=!0:n[t]==this.sheetToken&&(t-->1&&n[t]==this.tic[0]?i=n.substring(0,t-1).lastIndexOf(this.tic[0])==0:t>0&&this._isDigit(n[t])&&(i=n.substring(0,t).lastIndexOf(this.sheetToken)==0))}if(i&&(t=r+1,t<n.length-6&&n[t]==this.tic[0]&&(t=n.indexOf(this.sheetToken,t+1),t<n.length-2&&t++),t<n.length-2&&n[t]==this._string_fixedreference[0]&&t++,this._isLetter(n[t]))){for(t++;t<n.length-1&&this._isLetter(n[t]);)t++;if(this._isDigit(n[t])){for(t++;t<n.length&&this._isDigit(n[t]);)t++;u=t==n.length}}}return u};this._isUpperChar=function(n){var t=n.charCodeAt(0);return t>64&&t<91};this._iisVLookupCachingEnabled=function(){return(this._enableLookupTableCaching&3)!=0||(this._enableLookupTableCaching&1)!=0};this._mark=function(n,t,i,r,u){var f=0,e;for(u?(f=this._findAndCheckPrecedingChar(n,i,f),n=this._findAndCheckPrecedingCharCopy):f=n.indexOf(i),e=i.length;f>-1;)n=n.substring(0,f)+r+n.substring(f+e),t=t.substring(0,f)+r+t.substring(f+e),u?(f=this._findAndCheckPrecedingChar(n,i,f),n=this._findAndCheckPrecedingCharCopy):f=n.indexOf(i,f);return this._markCopy="",t};this._markColonsInQuotes=function(n){for(var i=!1,t=0;t<n.length;++t)n[t]==this.tic[0]?i=!i:n[t]==":"&&i&&(n=n.split(":").join(this._markerChar));return n};this._markIF=function(n,t){var c=n.indexOf(this.getReservedWordOperators()[this._reservedWord_IF]),i=n.indexOf(this.getReservedWordOperators()[this._reservedWord_THEN]),u=n.indexOf(this.getReservedWordOperators()[this._reservedWord_ELSE]);if(u>-1&&i>-1){var f=this.getReservedWordOperators()[this._reservedWord_IF].length,e=i-f,r=i+this.getReservedWordOperators()[this._reservedWord_THEN].length,o=u-r,s=u+this.getReservedWordOperators()[this._reservedWord_ELSE].length,h=t.length-s;t="IF(("+this._substring(t,f,e)+")"+this.getParseArgumentSeparator()+"("+this._substring(t,r,o)+")"+this.getParseArgumentSeparator()+"("+this._substring(t,s,h)+"))";n=t}else if(i>-1){var f=0,e=i,r=i+this.getReservedWordOperators()[this._reservedWord_THEN].length,o=t.length-r+1;t="IF(("+this._substring(t,f,e)+")"+this.getParseArgumentSeparator()+"("+this._substring(t,r,o)+"))";n=t}return this._markIFCopy=t,t};this._markLibraryFormulas=function(n){var u=n.indexOf(")"),l,i,r,f,v,s,e,o,a,c,h,t;if(u==-1)n=this._markNamedRanges(n);else while(u>-1){for(l=0,i=u-1;i>-1&&(n[i]!="("||l!=0);)n[i]==")"?l++:n[i]==")"&&l--,i--;if(i==-1)throw this.formulaErrorStrings[this._mismatched_parentheses];for(r=i-1;r>-1&&(this._isLetterOrDigit(n[r])||this._validFunctionNameChars.indexOf(n[r])>-1||n[r]==this.getParseDecimalSeparator());)r--;if(f=i-r-1,f>0&&this.getLibraryFunctions().getItem(this._substring(n,r+1,f))!=undefined){if(this._ignoreBracet=this._substring(n,r+1,f)=="AREAS"?!0:!1,t=this._substring(n,i,u-i+1),t.indexOf("({")>-1&&t.indexOf("})")>-1){for(v=this._string_empty,s=this._string_empty,e=0;e<t.length;e++){if(o=this._string_empty,t[e]=="{")for(o+=t[e++];t[e]!="}";)s+=t[e++];o+=s;o+=t[e];this.getNamedRanges().length>0&&s!=this._string_empty&&(this._checkForNamedRange(s.toUpperCase()),this._findNamedRange&&(o=o.split("{").join(this._string_empty)),this._findNamedRange=!1);s=this._string_empty;v+=o}t=v}t=this._markNamedRanges(t);t=this._swapInnerParens(t);t=this._addParensToArgs(t);n=n.substring(0,r+1)+"q"+this._substring(n,r+1,f)+t.split("(").join(this._leftBracket).split(")").join(this._rightBracket)+n.substring(u+1)}else{if(f>0){if(this.unknownFunction!=null){var y=CalcEngine.getSheetFamilyItem(this.grid),p=this.grid,w=this._sheetToken(this.cell),t=this.cell;for(w.length>0&&(p=y.tokenToParentObject.getItem(w),t=t.substring(t.lastIndexOf(this.sheetToken)+1)),a=y.sheetNameToParentObject.keys(),c=0;c<a.length;c++)if(y.sheetNameToParentObject.getItem(a[c])==p){t=a[c]+this.sheetToken+t;break}if(h=new UnknownFunctionEventArgs,h.setMissingFunctionName(this._substring(n,r+1,f)),h.setCellLocation(t),this.unknownFunction(this,h),this._ignoreBracet=n.substring(r+1,f)=="AREAS"?!0:!1,h.getHandled())return h.getResult().toString()}if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._unknown_formula_name]+" "+this._substring(n,r+1,f);return this.getErrorStrings()[5].toString()}t="";i>0&&(t=n.substring(0,i));t=t+"{"+this._substring(n,i+1,u-i-1)+"}";u<n.length&&(t=t+n.substring(u+1));ej.isNullOrUndefined(this.parentObject)||this.parentObject.pluginName=="ejSpreadsheet"||(t=this._markNamedRanges(t));n=t}u=n.indexOf(")")}return n.split("{").join("(").split("}").join(")")};this._swapInnerParens=function(n){return n.length>2&&(n=n[0]+n.substr(1,n.length-2).split("(").join("{").split(")").join("}")+n[n.length-1]),n};this._addParensToArgs=function(n){var u,t,r,e,f,i;if(n.length==0)return this._string_empty;if(u=[],u.push(this._parseArgumentSeparator),u.push(this._rightBracket),t=n.lastIndexOf(this._parseArgumentSeparator),r=0,t==-1)n.length>2&&n[0]=="("&&n[n.length-1]==")"&&(n[1]!="{"&&n[1]!="("?(n=n.substring(0,n.length-1)+"}"+n.substring(n.length-1),n=n[0]+"{"+n.substring(1)):(e=["+","-","*","/"],t=this._lastIndexOfAny(n,e),r==0&&n[n.length-1]==")"&&(r=n.length-1),r>0&&n[t+1]!="{"&&n[t-1]=="}"&&(n=n.substr(0,r)+"}"+n.substr(r),n=n.substr(0,t+1)+"{"+n.substr(t+1))));else for(f=!0;t>-1;)i=this._indexOfAny(n.substring(t+1,n.length),u),i>=0?i=t+i+1:i==-1&&n[n.length-1]==")"&&(i=n.length-1),i>0&&n[t+1]!="{"&&n[i-1]!="}"&&(n=n.substr(0,i)+"}"+n.substr(i),n=n.substr(0,t+1)+"{"+n.substr(t+1)),t=n.substr(0,t).lastIndexOf(this._parseArgumentSeparator),f&&t==-1&&n[0]=="("&&(t=0,f=!1);return n.split("{}").join(this._string_empty)};this._markNamedRanges=function(n){var s=n.indexOf(")"),u=[")",this.getParseArgumentSeparator(),"}","+","-","*","/","<",">","=","&"],t=n.length>0&&(n[0]=="("||n[0]=="{")?1:0,r,f,i,e,o;for(n.indexOf("#N/A")>-1&&(n=n.split("#N/A").join("#N~A")),n.indexOf("#DIV/0!")>-1&&(n=n.split("#DIV/0!").join("#DIV~0!")),r=t,r=n.indexOf("[")==-1||n.indexOf("[")>this._indexOfAny(n.substring(t),u)?this._indexOfAny(n.substring(t),u):this._findNextEndIndex(n,r);r>-1&&r+t<n.length;){if(f="",i=null,this._substring(n,t,r).indexOf("[")>-1?i=this._getTableRange(this._substring(n,t,r)):this.getNamedRanges().containsKey(this._substring(n,t,r))&&(i=this._checkForNamedRange(this._substring(n,t,r))),i==undefined||typeof i=="string"||(i=i.getItem(this._substring(n,t,r))),ej.isNullOrUndefined(i)&&(f=this._checkIfScopedRange(this._substring(n,t,r)),f!="NaN"?(this._findNamedRange=!0,i=f):this._substring(n,t,r).startsWith(this.sheetToken.toString())&&(e=this._substring(n,t,r).indexOf(this.sheetToken,1),e>1&&(i=this.getNamedRanges().getItem(this._substring(n.substring(t),e+1,r-e-1)))),!ej.isNullOrUndefined(i)&&this._findNamedRange&&i.indexOf(this._string_fixedreference)>-1&&(i=i.split(this._string_fixedreference).join(this._string_empty))),ej.isNullOrUndefined(i)||(i=i.toUpperCase(),i=this.setTokensForSheets(i),i=this._markLibraryFormulas(i)),ej.isNullOrUndefined(i)||i==this._string_empty)for(t+=r+1;t<n.length&&!this._isUpperChar(n[t])&&n[t]!=this.sheetToken;)t++;else n=n.substring(0,t)+i+n.substring(t+r),t+=i.length+1;for(r=t,t<n.length-1&&n[t]=="{"&&(t=t+1),r=n.indexOf("[")==-1||n.indexOf("[")>this._indexOfAny(n.substring(t),u)?this._indexOfAny(n.substring(t),u):this._findNextEndIndex(n,r),r=this._indexOfAny(n.substring(t),u);r==0&&t<n.length-1;)t++,r=this._indexOfAny(n.substring(t),u);(r==-1||n.substring(t).indexOf("[")>-1)&&t<n.length&&(i=n.substring(t).indexOf("[")>-1?this._getTableRange(n.substring(t)):this.getNamedRanges().length>0?this._checkForNamedRange(n.substring(t)):i,ej.isNullOrUndefined(i)&&(f=this._checkIfScopedRange(n.substring(t)),f!="NaN"&&(i=f)),ej.isNullOrUndefined(i)||i==this._string_empty||(i=i.toUpperCase(),i=this.setTokensForSheets(i),i=this._markLibraryFormulas(i),i!=null&&(o=n.substring(t),n=o[o.length-1]==")"?n.substring(0,t)+i+")":n.substring(0,t)+i,t+=i.toString().length+1)),r=t<n.length?this._indexOfAny(n.substring(t),u):-1)}return n.indexOf("#N~A")>-1&&(n=n.split("#N~A").join("#N/A")),n.indexOf("#DIV~0!")>-1&&(n=n.split("#DIV~0!").join("#DIV/0!")),n};this._markReserveWords=function(n){var t=n.toLowerCase(),i=this._isIE8?t.replace(/^\s+|\s+$/g,""):t.trim();return i[0]==this.getReservedWordOperators()[this._reservedWord_IF]&&(n=this._markIF(t,n),t=this._markIFCopy),n=this._mark(t,n,this.getReservedWordOperators()[this._reservedWord_NOT],this.char_NOTop,!0),t=this._markCopy,n=this._mark(t,n,this.getReservedWordOperators()[this._reservedWord_OR],this.char_ORop,!1),t=this._markCopy,n=this._mark(t,n,this.getReservedWordOperators()[this._reservedWord_AND],this.char_ANDop,!1),t=this._markCopy,n=this._mark(t,n,this.getReservedWordOperators()[this._reservedWord_XOR],this.char_XORop,!1),t=this._markCopy,n};this._markupResultToIncludeInFormula=function(n){return n.length>0&&n[0]=="-"&&!isNaN(this._parseDouble(n))?n="nu"+n.substring(1):n.length>0&&(n[0]==this.tic[0]||n[0]==this.bMARKER||n[0]=="#")||n.startsWith(this.trueValueStr)||n.startsWith(this.falseValueStr)||(isNaN(this._parseDouble(n))?this._isRange(n)||(n=this.tic+n+this.tic):(n=n.split(this.getParseArgumentSeparator()).join(String.fromCharCode(32)),n="n"+n)),n};this._matchCompare=function(n,t){var i=n.toString(),r=t.toString(),u=this._parseDouble(i),f=this._parseDouble(r);return isNaN(u)||isNaN(f)?i==r?0:i>r?1:-1:u==f?0:u>f?1:-1};this._matchingRightBracket=function(n){for(var i=-1,t=1,r=0,e=this._sheetToken(n),u,f;i==-1&&t<n.length;)n[t]==this._rightBracket?r==0?i=t:(r--,r==0&&t==n.length-1&&(i=t)):n[t]=="q"&&(u=t+1,u<n.length&&(f=this._processUpperCase(n,u,e),n=this._processUpperCaseFormula,u=this._processUpperCaseIvalue,e=this._processUpperCaseSheet,f!=""&&this.getLibraryFunctions().containsKey(f)&&r++)),t++;return i};this._notInBlock=function(n,t){for(var i=n.indexOf(this.bMARKER),r=!1;i>-1&&i<t;)r=!r,i=n.indexOf(this.bMARKER,i+1);return!r};this._getTopRowIndexFromRange=function(n){var i,r,t,u;return(n=this._markColonsInQuotes(n),i=n.indexOf(":"),i==-1)?this.rowIndex(n):(r="",t=n.indexOf(this.sheetToken),t>-1&&(u=n.indexOf(this.sheetToken,t+1),u>-1&&(r=this._substring(n,t,u-t+1),n=n.replace(r,""),i=n.indexOf(":"))),this.rowIndex(n.substring(0,i)))};this._getTableRange=function(n){var t,f,r,i,e,s,u,o;if(n=n.replace(" ","").toUpperCase(),t=n.replace("]","").replace("#DATA",""),f=t,t.indexOf(this.getParseArgumentSeparator())>-1&&(f=t.substring(0,t.indexOf(this.getParseArgumentSeparator())).replace("[",""),t=t.replace("[","").replace(this.getParseArgumentSeparator(),"_")),r="",n.indexOf("#THISROW")>-1){if(i=this.getNamedRanges().getItem(t.replace("#THISROW","")),i==undefined||typeof i=="string"||(i=i.getItem(t.replace("#THISROW",""))),i==null)return i;i=i.toUpperCase();e=this.rowIndex(this.cell);i=i.replace(this._string_fixedreference,"");i=this.setTokensForSheets(i);s=0;u=this.getNamedRanges().getItem(f.Replace("#THISROW",""));u==undefined||typeof u=="string"||(u=u.getItem(f.Replace("#THISROW","")));u=u.replace(this._string_fixedreference,"").toUpperCase();u=this.setTokensForSheets(u);o=this._getTopRowIndexFromRange(i);r=this.getCellsFromArgs(i)[e-o]}else if(t=t[t.length-1]=="["?t.replace("[",""):t.replace("[","_"),(t.indexOf("#TOTALS")>-1||t.indexOf("#ALL")>-1||t.indexOf("#HEADERS")>-1||t.indexOf("#DATA")>-1)&&(t=t.replace("#","")),r=this.getNamedRanges().getItem(t),r==undefined||typeof r=="string"||(r=r.getItem(t)),r==null)return null;return r.toUpperCase()};this._calculateArraySize=function(n,t,i,r,u){var o=n.indexOf(":"),c=this._string_empty,f=0,e=0;if(u=0,r=0,this._isCellReference(n))if(o>-1){var l=this.rowIndex(n.substr(0,o)),s=this.rowIndex(n.substr(o+1)),a=this.colIndex(n.substr(0,o)),h=this.colIndex(n.substr(o+1));f=s-l+1;e=h-a+1;f>t&&(s=s-(f-t));e>i&&(h=h-(e-i));f<t&&(r=f);e<i&&(u=e);c=RangeInfo.getAlphaLabel(a)+l.toString()+":"+RangeInfo.getAlphaLabel(h)+s.toString();n=c}else r=1,u=1;else f=1,e=1,n.indexOf(",")>-1&&n.indexOf(";")==-1?e=this.splitArgsPreservingQuotedCommas(n).length:n.indexOf(";")>-1&&n.indexOf(",")==-1&&(f=this._splitArguments(n,";").length),f<t&&(r=f),e<i&&(u=e);return this._calculateArraySizeheight=t,this._calculateArraySizewidth=i,this._calculateArraySizeminHeight=r,this._calculateArraySizeminWidth=u,n};this._checkForNamedRange=function(n){var i=this._string_empty,r,u,t,e,f;return n.indexOf("[")>-1&&(r=this._getTableRange(n),ej.isNullOrUndefined(r)||(this._findNamedRange=!0,n=r)),i=this._checkIfScopedRange(n),i!="NaN"?(this._findNamedRange=!0,n=i):(n.indexOf(this.sheetToken)>-1&&(u=CalcEngine.getSheetFamilyItem(this.grid),t=n.split("'").join(this._string_empty),t=t.substr(0,t.indexOf(this.sheetToken)),u.sheetNameToToken.containsKey(t.toUpperCase())&&(e=parseInt(u.sheetNameToToken.getItem(t.toUpperCase()).split(this.sheetToken).join(this._string_empty)),ej.isNullOrUndefined(this.parentObject)||this.parentObject.pluginName!="ejSpreadsheet"||(f=n.replace(t,this.parentObject.model.sheets[e+1].sheetInfo.text.toUpperCase()).split("'").join(this._string_empty),this.getNamedRanges().length>0&&this.getNamedRanges().contains(f.toUpperCase())&&(n=f)))),this.getNamedRanges().length>0&&this.getNamedRanges().contains(n.toUpperCase())&&(ej.isNullOrUndefined(this.parentObject)||this.parentObject.pluginName!="ejSpreadsheet"?(n=this._parse(this.getNamedRanges().getItem(n.toUpperCase())),n=this.setTokensForSheets(n),n.indexOf(this._string_fixedreference)>-1&&n.split(this._string_fixedreference).join(this._string_empty)):n=this._parse(this.getNamedRanges().getItem(n.toUpperCase())),this._findNamedRange=!0)),this._findNamedRange&&n[0]!="!"&&n[0]!="q"&&n[0]!="bq"&&(n=this.setTokensForSheets(n),n.indexOf(this._string_fixedreference)>-1&&(n=n.split(this._string_fixedreference).join(this._string_empty))),n};this._getTableRange=function(n){var i,t=n.split(this._table_Data).join(this._string_empty),a=t,u,r,o,c,l,e;if(t.indexOf(this._parseArgumentSeparator)>-1&&(a=t.substring(0,t.indexOf(this._parseArgumentSeparator)),t=t.split(this._parseArgumentSeparator).join(this._string_empty)),u=this._string_empty,n.toUpperCase().indexOf(this._table_Row)>-1)if(t=t[t.length-1]==")"?t.replace(")",this._string_empty):t,t.indexOf(":")>-1){var s=this._string_empty,h=t.toUpperCase().split(this._table_Row),f=this._splitArguments(h[1],":");for(r=0;r<f.length-1;r++){if(f[r]=h[0]+f[r],ej.isNullOrUndefined(this.parentObject)||this.parentObject.pluginName!="ejSpreadsheet"?i=this.getNamedRanges()[f[r].toUpperCase().split(this._table_Row).join(this._string_empty).split("[[").join("[").split("]]").join("]")]:(this.SetNamedRangeDependency(f[r].toUpperCase(),this.cell),o=this.getNamedRanges()[f[r].toUpperCase()].split(this._table_Row).join(this._string_empty),ej.isNullOrUndefined(o)||(i=this.getNamedRanges()[f[r].toUpperCase()].split(this._table_Row).join(this._string_empty))),ej.isNullOrUndefined(i))return i;i=i.toUpperCase();i=this.setTokensForSheets(i);i=i.split(this._string_fixedreference).join(this._string_empty);s=this._sheetToken(i.split(this.tic).join(this._string_empty));u+=r==f.length-1?this._getCellFrom(i):this._getCellFrom(i)+":"}u=s+u}else{if(ej.isNullOrUndefined(this.parentObject)||this.parentObject.pluginName!="ejSpreadsheet"?i=this.getNamedRanges()[t.toUpperCase().split(this._table_Row).join(this._string_empty).split("[[").join("[").split("]]").join("]")]:(this.SetNamedRangeDependency(t.toUpperCase(),this.cell),o=this.getNamedRanges()[t.toUpperCase()],ej.isNullOrUndefined(o)||(i=this.getNamedRanges()[t.toUpperCase()].split(this._table_Row).join(this._string_empty))),ej.isNullOrUndefined(i))return i;i=i.toUpperCase();c=this.rowIndex(this.cell);i=i.split(this._string_fixedreference).join(this._string_empty);s=this._sheetToken(i.split(this.tic).join(this._string_empty));l=this._getTopRowIndex(i);u=this.getCellsFromArgs(i)[c-l]}else if(t=t[t.length-1]=="["?t.split("[").join(this._string_empty):t,t=t[t.length-1]==")"?t.split(")").join(this._string_empty):t,ej.isNullOrUndefined(this.parentObject)||this.parentObject.pluginName!="ejSpreadsheet"?(e=this.getNamedRanges()[t.toUpperCase()],u=ej.isNullOrUndefined(e)?null:e.toString()):(this.SetNamedRangeDependency(t.toUpperCase(),this.cell),e=this.getNamedRanges().getItem(t.toUpperCase()),u=ej.isNullOrUndefined(e)?null:this.getNamedRanges().getItem(t.toUpperCase()).toString().toUpperCase()),u==null)return null;return u.toUpperCase()};this._getTopRowIndex=function(n){var i,r,t,u;return n=this._markColonsInQuotes(n),i=n.indexOf(":"),i==-1&&this.rowIndex(n),r=this._string_empty,t=n.indexOf(this.sheetToken),t>-1&&(u=n.substring(0,t+1).indexOf(this.sheetToken),u>-1&&(r=n.substring(t,u-t+1),n=n.split(r,this._string_empty),i=n.indexOf(":"))),this.rowIndex(arg.substring(0,i))};this._markColonsInQuotes=function(n){var r=!1,i,t;if(n.indexOf(":")==-1)return n;for(i=new StringBuilder,t=0;t<n.length;t++)n[t]==this.tic?r=!r:n[t]==":"&&r?ej.isNullOrUndefined(this.parentObject)||this.parentObject.pluginName!="ejSpreadsheet"?n=n.split(":",this._markerChar):i.append(this._markerChar):i.append(n[t]);return ej.isNullOrUndefined(this.parentObject)||this.parentObject.pluginName!="ejSpreadsheet"||(n=i.toString()),n};this._parse=function(n){var t=n,l,w,b,o,h,f,i,r,s,e,v,y,p;if(this._isTextEmpty(t))return t;if(t=this.getFormulaText(t),this.getSupportLogicalOperators()&&(t=this._markReserveWords(t)),this.getFormulaCharacter()!=String.fromCharCode(0)&&this.getFormulaCharacter()==t[0]&&(t=t.substring(1)),this.getNamedRanges().length>0&&(t=this._checkForNamedRange(t),this._findNamedRange=!1),l=this._saveStrings(t),t=this._saveStringsText,t=t.split(this.braceLeft).join(this.tic),t=t.split(this.braceRight).join(this.tic),t=t.split("-+").join("-"),f=0,(t[t.length-1]!=this.bMARKER||this._indexOfAny(t,this.tokens)!=t.length-2)&&(t=t.toUpperCase()),t.indexOf(this.sheetToken)>-1&&(w=CalcEngine.getSheetFamilyItem(this.grid),w.sheetNameToParentObject!=null&&w.sheetNameToParentObject.length>0))try{t[0]!=this.sheetToken.toString()&&(t=this.setTokensForSheets(t));b=this._sheetToken(t.split(this.tic).join(this._string_empty));o=this._checkIfScopedRange(t.split("'").join(this._string_empty).split(this.sheetToken).join(this._string_empty));ej.isNullOrUndefined(b)&&b!=this._string_empty&&this.getNamedRanges().length>0&&o!=this._string_empty&&(t=o)}catch(a){if(this._rethrowExceptions)throw a;else return a}if(this._isRangeOperand)return this._isRangeOperand=!1,this._getCellFrom(this._parseSimple(t));if(h=this._string_empty,t.indexOf("[")>-1){for(f=0;f<t.length;f++){if(t[f]=="[")while(t[f]!="]")h=h.concat(t[f]),f++;t[f]!=" "&&(h=h.concat(t[f]))}t=h}else if(t.indexOf(" ")>-1&&this.getNamedRanges().length>0){var d=this._string_empty,o=this._string_empty,c=!1,u=this._string_empty,k=!1;for(i=0;i<t.length;i++){for(r=this._string_empty;i!=t.length&&this._isLetterOrDigit(t[i])||i!=t.length&&t[i]==":";)u=u.concat(t[i++]);if(o=this.getNamedRanges().getItem(u.toUpperCase()),o!=undefined?(r=r.concat(u),c=!0):this.getLibraryFunctions().getItem(u)!=undefined?r=r.concat(u):u!=this._string_empty&&(r=r.concat(u)),i!=t.length&&t[i]==" "&&i!=0&&this._isLetterOrDigit(t[i-1])|t[i-1]==")"){for(u=this._string_empty,i++;i!=t.length&&this._isLetterOrDigit(t[i])||i!=t.length&&t[i]==":";)u=u.concat(t[i++]);o=this.getNamedRanges().getItem(u.toUpperCase());c&&!k&&o!=undefined?r=r.concat(" "+u):c&&u.indexOf(":")>-1?r=r.concat(" "+u):o==undefined&&this.getLibraryFunctions().getItem(u)!=undefined&&c?(r=r.concat(" "+u),k=!0):u!=this._string_empty&&(r=r.concat(u))}i!=t.length&&t[i]==")"?(r=r.concat(t[i]),k=!1):i!=t.length&&t[i]!=")"&&t[i]!=" "?r=r.concat(t[i]):i!=t.length&&t[i]==" "&&c&&this._isLetterOrDigit(t[i-1])|t[i-1]==")"?r=r.concat(t[i]):i==t.length||u!=this._string_empty||c||t[i]==" "||(r=r.concat(this._string_empty));d+=r;u=this._string_empty}t=d}else t=t.split(" ").join("");t=t.split("=>").join(">=");t=t.split("=<").join("<=");try{t=this._markLibraryFormulas(t)}catch(a){if(this._rethrowLibraryComputationExceptions)throw a;}if(!this._ignoreBracet)while((f=t.indexOf(")"))>-1){if(s=t.substring(0,f).lastIndexOf("("),s==-1)throw this.formulaErrorStrings[this._mismatched_parentheses];if(s==f-1)throw this.formulaErrorStrings[this._empty_expression];if(e="",e=this._ignoreBracet?this._substring(t,s,f-s+1):this._substring(t,s+1,f-s-1),e.indexOf(":")>-1&&e.indexOf("q")==-1&&e.indexOf("=")==-1&&e.indexOf("[")==-1){e.indexOf(this._string_fixedreference)>-1&&(e=e.split(this._string_fixedreference).join(this._string_empty));try{if(v=this.getCellsFromArgs(e),v.length>0)for(y=0;y<v.length;y++)this.updateDependencies(v[y])}catch(a){return this._errorStrings[5]}}t=t.substring(0,s)+this._parseSimple(e)+t.substring(f+1)}if(!this._ignoreBracet&&t.indexOf("(")>-1)throw this.formulaErrorStrings[this._mismatched_parentheses];return p=this._parseSimple(t),l!=null&&l.length>0&&(p=this._setStrings(p,l)),p};this._parseDouble=function(n){var t=ej.parseFloat(n.toString(),0,ej.cultureObject.name);return isNaN(t)?NaN:t};needToContinue=!0;this._parseSimple=function(n){var t=n;if(t.length>0&&t[0]=="+"&&(t=t.substring(1)),t=="#N/A"||t=="#N~A")return"#N/A";if(t.indexOf("#N/A")>-1&&(t=t.split("#N/A").join("#N~A")),t=="#DIV/0!"||t=="#DIV~0!")return"#DIV/0!";if(t.indexOf("#DIV/0!")>-1&&(t=t.split("#DIV/0!").join("#DIV~0!")),t=this._handleEmbeddedEs(t),t=t.split(this._string_lesseq).join(this.char_lesseq),t=t.split(this._string_greatereq).join(this.char_greatereq),t=t.split(this._string_noequal).join(this.char_noequal),t=t.split(this._string_fixedreference).join(this._string_empty),t=t.split(this._string_or).join(this.char_or),t=t.split(this._string_and).join(this.char_and),t=="")return t;needToContinue=!0;var i=[this.token_EP,this.token_EM],r=[this.token_or],u=[this.token_multiply,this.token_divide],f=[this.token_add,this.token_subtract],e=[this.token_less,this.token_greater,this.token_equal,this.token_lesseq,this.token_greatereq,this.token_noequal],o=[this.token_NOTop],s=[this.token_ORop,this.token_ANDop,this.token_XORop],h=[this.token_and],c=[this.char_EP,this.char_EM],l=[this.char_or],a=[this.char_multiply,this.char_divide],v=[this.char_add,this.char_subtract],y=[this.char_less,this.char_greater,this.char_equal,this.char_lesseq,this.char_greatereq,this.char_noequal],p=[this.char_NOTop],w=[this.char_ORop,this.char_ANDop,this.char_XORop],b=[this.char_and];return t=this._parseSimpleOperators(t,i,c),t=this._parseSimpleOperators(t,r,l),needToContinue&&(t=this._parseSimpleOperators(t,u,a)),needToContinue&&(t=this._parseSimpleOperators(t,f,v)),needToContinue&&(t=this._parseSimpleOperators(t,e,y)),needToContinue&&(t=this._parseSimpleOperators(t,o,p)),needToContinue&&(t=this._parseSimpleOperators(t,s,w)),needToContinue&&(t=this._parseSimpleOperators(t,h,b)),t.indexOf("#N~A")>-1&&(t=t.split("#N~A").join("#N/A")),t.indexOf("#DIV~0!")>-1&&(t=t.split("#DIV~0!").join("#DIV/0!")),t};this._parseSimpleOperators=function(n,t,i){for(var p,nt,b,w,tt,it,a,r,h,c,y,f,u=n,e,d="",g=0;g<i.length;g++)d=d+i[g];u=u.split("---").join("-").split("--").join("+").split(this.getParseArgumentSeparator()+"-").join(this.getParseArgumentSeparator()+"u").split(this._leftBracket+"-").join(this._leftBracket+"u").split("=-").join("=u");u=u.split(">-").join(">u").split("<-").join("<u").split("/-").join("/u").split("*-").join("*u").split("+-").join("-").split("--").join("-u").split("w-").join("wu").split(this.tic+"-").join(this.tic+"u").toString();u=u.split(",+").join(",").split(this._leftBracket+"+").join(this._leftBracket).split("=+").join("=").split(">+").join(">").split("<+").join("<").split("/+").join("/").split("*+").join("*").split("++").join("+").toString();u.length>0&&u[0]=="-"?(u=u.substring(1).split("-").join(this.token_ORop),u="0-"+u,u=this._parseSimpleOperators(u,[this.token_subtract],[this.char_subtract]),u=u.split(this.token_ORop).join("-")):u.length>0&&u[0]=="+"&&(u=u.substring(1));try{if(this._indexOfAny(u,i)>-1)while((e=this._indexOfAny(u,i))>-1){var o="",s="",l=0,v=0,ft=this._supportLogicalOperators&&u[e]==this.char_NOTop,r=0;if(ft)l=e;else{if(e<1&&u[e]!="-")throw this.formulaErrorStrings[this._operators_cannot_start_an_expression];if(r=e-1,e==0&&u[e]=="-"){u=this.bMARKER+"nu"+u.substring(1)+this.bMARKER;continue}else if(u[r]==this.tic[0]){if(f=u.substring(0,r-1).lastIndexOf(this.tic),f<0)throw this.formulaErrorStrings[this._cannot_parse];o=this._substring(u,f,r-f+1);l=f}else if(u[r]==this.bMARKER){if(f=this._findLastNonQB(u.substring(0,r-1)),f<0)throw this.formulaErrorStrings[this._cannot_parse];o=this._substring(u,f+1,r-f-1);l=f+1}else if(u[r]==this._rightBracket){for(h=0,f=r-1;f>0&&(u[f]!="q"||h!=0);)u[f]=="q"?h--:u[f]==this._rightBracket&&h++,f--;if(f<0)throw this.formulaErrorStrings[this._cannot_parse];o=this._substring(u,f,r-f+1);l=f}else if(this._isDigit(u[r])||u[r]=="%"){for(c=!1,y=!1;r>-1&&(this._isDigit(u[r])||!c&&u[r]==this.getParseDecimalSeparator()||!y&&u[r]=="%"||u[r]=="u"||u[r]=="_");)u[r]==this.getParseDecimalSeparator()?c=!0:u[r]=="%"&&(y=!0),r=r-1;if(r>-1&&c&&u[r]==this.getParseDecimalSeparator())throw this.formulaErrorStrings[this._number_contains_2_decimal_points];if(r=r+1,r==0||r>0&&!this._isUpperChar(u[r-1]))o="n"+this._substring(u,r,e-r),l=r;else{for(r=r-1;r>-1&&(this._isUpperChar(u[r])||this._isDigit(u[r])||u[r]=="_");)r=r-1;if(r>-1&&u[r]=="u"&&(r=r-1),r>-1&&u[r]==this.sheetToken){for(r=r-1;r>-1&&u[r]!=this.sheetToken;)r=r-1;r>-1&&u[r]==this.sheetToken&&(r=r-1)}if(r>-1&&u[r]==":"){for(r=r-1;r>-1&&this._isDigit(u[r]);)r=r-1;while(r>-1&&this._isUpperChar(u[r]))r=r-1;if(r>-1&&u[r]==this.sheetToken){for(r--;r>-1&&u[r]!=this.sheetToken;)r--;r>-1&&u[r]==this.sheetToken&&r--}r=r+1;o=this._substring(u,r,e-r);o=this._getCellFrom(o)}else r=r+1,o=this._substring(u,r,e-r);this.updateDependencies(o);l=r;this.getNamedRanges().length>0&&(o=this._checkForNamedRange(o))}}else{while(r>=0&&(this._isUpperChar(u[r])||u[r]=="_"||u[r]=="."))r--;if(o=this._substring(u,r+1,e-r-1),l=r+1,tt="",this.getNamedRanges().length>0&&(o=this._checkForNamedRange(o)),!this._findNamedRange)if(o==this.trueValueStr)o="n"+this.trueValueStr;else if(o==this.falseValueStr)o="n"+this.falseValueStr;else{if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_char_in_front_of]+" "+u[e];return this.getErrorStrings()[5].toString()}this._findNamedRange=!1}}if(e==u.length-1)throw this.formulaErrorStrings[this._expression_cannot_end_with_an_operator];else if(r=e+1,p=u[r]=="u",p&&r++,u[r]==this.tic[0]){if(f=u.substring(r+1).indexOf(this.tic),f<0)throw this.formulaErrorStrings[this._cannot_parse];s=this._substring(u,r,f+2);v=f+r+2}else if(u[r]==this.bMARKER){if(f=this._findNonQB(u.substring(r+1)),f<0)throw this.formulaErrorStrings[this._cannot_parse];s=this._substring(u,r+1,f);p&&(s=s+"nu1m");v=f+r+2}else if(u[r]=="q"){for(h=0,f=r+1;f<u.length&&(u[f]!=this._rightBracket||h!=0);)u[f]==this._rightBracket?h++:u[f]=="q"&&h--,f++;if(f==u.length)throw this.formulaErrorStrings[this._cannot_parse];s=this._substring(u,r,f-r+1);p&&(s="u"+s);v=f+1}else if(this._isDigit(u[r])||u[r]==this.getParseDecimalSeparator()){for(c=u[r]==this.getParseDecimalSeparator(),r=r+1;r<u.length&&(this._isDigit(u[r])||!c&&u[r]==this.getParseDecimalSeparator());)u[r]==this.getParseDecimalSeparator()&&(c=!0),r=r+1;if(r<u.length&&u[r]=="%"&&(r+=1),c&&r<u.length&&u[r]==this.getParseDecimalSeparator())throw this.formulaErrorStrings[this._number_contains_2_decimal_points];s="n"+this._substring(u,e+1,r-e-1);v=r}else if(this._isUpperChar(u[r])||u[r]==this.sheetToken||u[r]=="u"){if(u[r]==this.sheetToken)for(r++;r<u.length&&u[r]!=this.sheetToken;)r++;for(r=r+1,nt=0,b=!1;r<u.length&&(this._isUpperChar(u[r])||u[r]=="_"||u[r]=="."||u[r]=="["||u[r]=="]"||u[r]=="#"||u[r]==" "||u[r]=="%"||u[r]==this.getParseDecimalSeparator()&&b);)r!=u.length-1&&u[r]=="["&&u[r+1]=="["&&(b=!0),r!=u.length-1&&u[r]=="]"&&u[r+1]=="]"&&(b=!1),r++,nt++;if(w=r==u.length||!this._isDigit(u[r]),nt>1){while(r<u.length&&(this._isUpperChar(u[r])||this._isDigit(u[r])||u[r]==" "||u[r]=="_"))r++;w=!0}while(r<u.length&&this._isDigit(u[r]))r=r+1;if(r<u.length&&u[r]==":"){if(r=r+1,r<u.length&&u[r]==this.sheetToken){for(r++;r<u.length&&u[r]!=this.sheetToken;)r=r+1;r<u.length&&u[r]==this.sheetToken&&r++}while(r<u.length&&this._isUpperChar(u[r]))r=r+1;while(r<u.length&&this._isDigit(u[r]))r=r+1;r=r-1;s=this._substring(u,e+1,r-e);s=this._getCellFrom(s)}else r=r-1,s=this._substring(u,e+1,r-e),p=u[r]=="u",p&&(s="u"+s);if(w&&s.startsWith(this.sheetToken)&&(w=!this._isCellReference(s)),w){if(tt="",this.getNamedRanges().length>0&&(s=this._checkForNamedRange(s)),!this._findNamedRange)if(o==this.trueValueStr)o="n"+this.trueValueStr;else if(o==this.falseValueStr)o="n"+this.falseValueStr;else{if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_char_in_front_of]+" "+u[e];return this.getErrorStrings()[5].toString()}this._findNamedRange=!1}else this.updateDependencies(s);v=r+1}else{if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_characters_following_an_operator];return u.indexOf("#REF!")>-1?this.getErrorStrings()[2].toString():this.getErrorStrings()[5].toString()}it=d.indexOf(u[e]);a=this.bMARKER+this._zapBlocks(o)+this._zapBlocks(s)+t[it]+this.bMARKER;l>0&&(a=u.substring(0,l)+a);v<u.length&&(a=a+u.substring(v));a=a.split(this.bMARKER2).join(this.bMARKER.toString());u=a}else{if(r=u.length-1,u[r]==this.bMARKER){if(f=this._findLastNonQB(u.substring(0,r-1)),f<0)throw this.formulaErrorStrings[this._cannot_parse];}else if(u[r]==this._rightBracket){for(h=0,f=r-1;f>0&&(u[f]!="q"||h!=0);)u[f]=="q"?h--:u[f]==this._rightBracket&&h++,f--;if(f<0)throw this.formulaErrorStrings[this._bad_library];}else if(this._isDigit(u[r])){for(c=!1,y=!1;r>-1&&(this._isDigit(u[r])||!c&&u[r]==this.getParseDecimalSeparator()||!y&&u[r]=="%");)u[r]==this.getParseDecimalSeparator()?c=!0:u[r]=="%"&&(y=!0),r=r-1;if(r>-1&&c&&u[r]==this.getParseDecimalSeparator())throw this.formulaErrorStrings[this._number_contains_2_decimal_points];}if(u.length>0&&(this._isUpperChar(u[0])||u[0]==this.sheetToken)){var k=!0,rt=!0,ut=!1;for(f=0;f<u.length;++f)if(u[f]==this.sheetToken){if(f>0&&!ut)throw this.formulaErrorStrings[this._missing_sheet];for(ut=!0,f++;f<u.length&&this._isDigit(u[f]);)f++;if(f==u.length||u[f]!=this.sheetToken){k=!1;break}}else{if(!rt&&this._isLetter(u[f])){k=!1;break}if(this._isLetterOrDigit(u[f])||u[f]==this.sheetToken)rt=this._isUpperChar(u[f]);else{k=!1;break}}k&&(this.updateDependencies(u),needToContinue=!1)}}return u}catch(et){return et}};this._pop=function(n){var i=n.pop(),t,u,r;if(i!=null){if(i.toString()==this.tic+this.tic)return NaN;if(t=i.toString().split(this.tic).join(""),this.getErrorStrings().indexOf(t)>-1)return this._isErrorString=!0,this.getErrorStrings().indexOf(t);if(t[0]=="#"||t=="")return 0;if(t==this.trueValueStr)return 1;if(t==this.falseValueStr)return 0;if(ej.isNullOrUndefined(ej.cultureObject)||ej.cultureObject.name=="en-US"||isNaN(parseFloat(t))||(t=_getLocaleNumberValue(t)),u=ej.isNullOrUndefined(ej.cultureObject)?this._parseDouble(t):ej.parseFloat(t,0,ej.cultureObject.name),isNaN(u)){if(this.getUseDatesInCalculations()&&isNaN(this._parseDouble(i)))return r=this._isDate(t),isNaN(r)?0:t.indexOf(":")>-1?this._getSerialDateTimeFromDate(r):Math.round(this._getSerialDateTimeFromDate(r))}else return u}return t==""&&this.getTreatStringsAsZero()?0:i!=null&&i.toString().length>0?Number.NaN:0};this._popString=function(n){var t=n.pop(),i=t!=null?ej.isNullOrUndefined(ej.cultureObject)?this._parseDouble(t.toString()):ej.parseFloat(t.toString(),0,ej.cultureObject.name):NaN;if(t==null)t="";else if(!isNaN(i))return i.toString();return this.removeTics(t.toString())};this._populateNamedRangesNonScoped=function(){this._namedRangesNonScoped==null&&(this._namedRangesNonScoped=new HashTable);this._namedRangesNonScoped.clear();for(var n=0;n<this.namedRanges.keys().length;n++)this._checkAddNonScopedNamedRange(this.namedRanges.keys()[n])};this._processUpperCase=function(n,t,i){for(var r="",u,f;t<n.length&&this._isUpperChar(n[t]);)r=r+n[t],t=t+1;while(t<n.length&&this._isDigit(n[t]))r=r+n[t],t=t+1;if(u=r,f=t,t<n.length&&n[t]==":"){if(r=r+n[t],t=t+1,t<n.length&&n[t]==this.sheetToken)for(r=r+n[t],t=t+1;t<n.length&&n[t]!=this.sheetToken;)r=r+n[t],t=t+1;while(t<n.length&&this._isUpperChar(n[t]))r=r+n[t],t=t+1;while(t<n.length&&this._isDigit(n[t]))r=r+n[t],t=t+1;r=i+this._getCellFrom(r)}else r=i+r;return i="",this._processUpperCaseFormula=n,this._processUpperCaseIvalue=t,this._processUpperCaseSheet=i,r};this._saveStrings=function(n){var f=null,h=this.tic+this.tic,s=0,t=-1,r,e,o,i,u;if((t=n.indexOf(this.tic))>-1)while(t>-1&&t<n.length)if(f==null&&(f=new HashTable),r=t+1<n.length?n.indexOf(this.tic,t+1):-1,r>-1){if(e=this.tic+this._uniqueStringMarker+s.toString()+this.tic,r<n.length-2&&n[r+1]==this.tic[0]&&(r=n.indexOf(this.tic,r+2),r==-1))throw this.formulaErrorStrings[this._mismatched_tics];o=this._substring(n,t,r-t+1);f.add(e,o);o=o.split(h).join(this.tic);s++;n=n.substring(0,t)+e+n.substring(r+1);t=t+e.length;t<n.length&&(t=n.indexOf(this.tic,t))}else if(r==-1&&n.indexOf(this.sheetToken)>-1&&t<n.indexOf(this.sheetToken,t)){for(i="",u=n.indexOf(this.sheetToken,t)-1;u>-1;u--)if(this.getValidPrecedingChars().indexOf(n[u].toString())==-1)i=n[u]+i;else break;if(!(i[0]=="'"&&i[i.length-1]=="'"))throw this.formulaErrorStrings[this._mismatched_tics];if(i[0]=="'"&&(i=i.substring(1)),i[i.length-1]=="'"&&(i=i.substring(0,i.length-1)),this.getSortedSheetNames().indexOf(i.toUpperCase())==-1)throw this.formulaErrorStrings[this._missing_sheet];t+1<n.length&&(t=n.indexOf(this.tic,t+1))}else throw this.formulaErrorStrings[this._mismatched_tics];return this._saveStringsText=n,f};this._setStrings=function(n,t){for(var r=t.keys(),i=0;i<r.length;i++)n=n.split(r[i]).join(t.getItem(r[i]));return n};this._sheetToken=function(n){var t=0,i="";if(t<n.length&&n[t]==this.sheetToken){for(t++;t<n.length&&n[t]!=this.sheetToken;)t++;i=n.substring(0,t+1)}if(t<n.length)return i;throw this.formulaErrorStrings[this._bad_index];};this._splitArguments=function(n,t){return n.split(t)};this._stripTics0=function(n){return n.length>1&&n[0]==this.tic[0]&&n[n.length-1]==this.tic[0]&&(n=this._substring(n,1,n.length-2)),n};this._substring=function(n,t,i){return n.substring(t,i+t)};this._isTextEmpty=function(n){return n==null||n==""};this._toOADate=function(n){return(n.getTime()-Date.parse(this._oaDate))/this._millisecondsOfaDay};this._zapBlocks=function(n){var i,t;if(n.indexOf(this.bMARKER)>-1)for(i=0,t=n.length-1;t>0;--t)n[t]==this._rightBracket?i--:n[t]==this._leftBracket?i++:n[t]==this.bMARKER&&i==0&&(n=n.substring(0,t-1)+n.substring(t+1));return n};this.addCustomFunction=function(n,t){return(n=n.toUpperCase(),this._addFunction(n,t),this._customlibraryFunctions.getItem(n)==undefined)?(this._customlibraryFunctions.add(n,t),!0):!1};this.UpdateDependentNamedRangeCell=function(n){var i,r,o;if(this.getDependentNamedRangeCells()!=null&&this.getDependentNamedRangeCells().containsKey(n)&&(i=this.getDependentNamedRangeCells().keys(),i!=null&&i.length>0))for(r=0;r<i.length;r++){var s=this.getDependentNamedRangeCells().getItem(i[r]),h=s.keys(),t=h[0],e=t.indexOf(this.sheetToken),u,f,c=this.grid;e>-1?(o=CalcEngine.getSheetFamilyItem(this.grid),this.grid=o.tokenToParentObject.getItem(t.substring(0,e+3)),u=this.rowIndex(t),f=this.colIndex(t)):(u=this.rowIndex(t),f=this.colIndex(t));this.recalculateRange(RangeInfo.cells(u,f,u,f),this.grid);this.grid=c}};this.RemoveNamedRangeDependency=function(n){if(this.getDependentNamedRangeCells().containsKey(n)){var t=Hashtable(this.getDependentNamedRangeCells()[n]);t.Clear();this.getDependentNamedRangeCells().remove(n)}};this.SetNamedRangeDependency=function(n,t){var u=CalcEngine.getSheetFamilyItem(this.grid),f,i,r;u.sheetNameToParentObject!=null&&t.indexOf(this.sheetToken)==-1&&(f=u.GridModelToToken[this.grid],t=f+t);this.getDependentNamedRangeCells()!=null&&this.getDependentNamedRangeCells().containsKey(n)?(i=this.getDependentNamedRangeCells().getItem(n),i.containsKey(t)||i.add(t,"0")):(r=new HashTable,r.add(t,"0"),this.getDependentNamedRangeCells().add(n,r))};this.addNamedRange=function(n,t){return(n=n.toUpperCase(),this.getNamedRanges().getItem(n)==null)?(this.getNamedRanges().add(n,t),this.getNameRangeValues().containsKey(n)||this.getNameRangeValues().add(n,t.toString()),(this.getUndefinedNamedRanges().containsKey(n)||this.getUndefinedNamedRanges().containsKey(n.toUpperCase())||this.getUndefinedNamedRanges().containsKey(n.toLowerCase()))&&(this.SetNamedRangeDependency(n.toUpperCase(),this.cell),this.UpdateDependentNamedRangeCell(n.toUpperCase())),this.namedRangesSized=null,this._checkAddNonScopedNamedRange(n),!0):!1};this.adjustRangeArg=function(n){return n.length>1&&n[0]==this.bMARKER&&n[n.length-1]==this.bMARKER&&this._substring(n,1,n.length-2).indexOf(this.bMARKER)==-1&&(n=this.computedValue(n)),n.length>1&&n[0]==this.tic[0]&&n[n.length-1]==this.tic[0]&&(n=this._substring(n,1,n.length-2)),n};this.clearFormulaDependentCells=function(n){var f=this.getDependentFormulaCells().getItem(n),i,t,r,u;if(f!=null){for(i=f.keys(),t=0;t<i.length;t++)r=i[t],u=this.getDependentCells().getItem(r),this._arrayRemove(u,n),u.length==0&&this.getDependentCells().remove(r);this.getDependentFormulaCells().remove(n)}};this.clearLibraryComputationException=function(){this._libraryComputationException=null};this.colIndex=function(n){var t=0,i=0,r;if(n=n.toUpperCase(),t<n.length&&n[t]==this.sheetToken){for(t++;t<n.length&&n[t]!=this.sheetToken;)t++;t++}while(t<n.length&&this._isLetter(n[t]))r=n[t].charCodeAt(0),i=i*26+r-64,t++;return i==0?-1:i};this.computedValue=function(formula){var _stack,i,sheet,loc,nextIfLoc,sepLoc,ifResult,rightPiece,uFound,d3,family,token,ii,t,name,j,args,functionName,result,isErrorStringValue,errIndex,o,x,s2,d1,s1,val,s,d,cc;if(this._isTextEmpty(formula))return formula;try{if(this.computedValueLevel++,this.computedValueLevel>1&&(this._isInteriorFunction=!0),this.computedValueLevel>this._maximumRecursiveCalls){this.computedValueLevel=0;throw this.formulaErrorStrings[this._too_complex];}if(_stack=[],i=0,_stack.length=0,sheet="",this.getAllowShortCircuitIFs()){loc=-1;do if(i<formula.length&&(i=formula.indexOf(this.iFMarker,i))>-1&&(loc=this._matchingRightBracket(formula.substring(i)),loc>-1)){result="";nextIfLoc=formula.indexOf(this.iFMarker,loc);do{loc=i;sepLoc=this._findNextSeparator(formula,loc);isNaN(sepLoc)||(loc=sepLoc);for(var funcArgs=this._substring(formula,i+this.iFMarker.length,loc-this.iFMarker.length-1),ifArguments=[],argLoc=0,argNo=0;argLoc<funcArgs.length;)sepLoc=this._findNextSeparator(funcArgs,argLoc),isNaN(sepLoc)?argLoc>0&&arguments.push(funcArgs.substring(0,argLoc)):(argLoc=sepLoc,ifArguments.push(funcArgs.substring(0,argLoc)),funcArgs=funcArgs.substring(Number(argLoc)+1),argLoc=0);ifResult=this.getValueFromArg(ifArguments[0]);result=ifResult==this.trueValueStr?this.getValueFromArg(ifArguments[1]):this.getValueFromArg(ifArguments[2]);nextIfLoc=formula.indexOf(this.iFMarker,loc)}while(formula.indexOf(this.iFMarker)>-1&&nextIfLoc>-1);result=this._markupResultToIncludeInFormula(result);rightPiece="";i+loc+1<formula.length&&(rightPiece=formula.substring(i+loc+1));formula=formula.substring(0,i)+result+rightPiece}while(formula.indexOf(this.iFMarker)>-1&&loc>-1)}for(i=0;i<formula.length;){if(formula[i]==this.bMARKER){i=i+1;continue}if(uFound=formula[i]=="u",uFound){if(i++,i>=formula.length)continue;if(formula[i]==this.bMARKER&&i++,i>=formula.length)continue}if(formula[i]=="%"&&_stack.length>0){o=_stack[0];d=this._parseDouble(o);isNaN(d)||(_stack.pop(),_stack.push(Number(d)/100));i=i+1;continue}if(formula[i]==this.sheetToken){for(sheet=formula[i].toString(),i++;i<formula.length&&formula[i]!=this.sheetToken;)sheet+=formula[i],i++;if(i<formula.length)sheet+=formula[i],i++;else continue}if(formula.substring(i).indexOf(this.trueValueStr)==0)_stack.push(this.trueValueStr),i+=this.trueValueStr.length;else if(formula.substring(i).indexOf(this.falseValueStr)==0)_stack.push(this.falseValueStr),i+=this.falseValueStr.length;else if(formula[i]==this.tic[0]||formula[i]=="|"){for(_stack.length==3&&(_stack=this._combineStack(formula,i-2,_stack)),s=formula[i].toString(),i++;i<formula.length&&formula[i]!=this.tic[0];)s=s+formula[i],i=i+1;this._multiTick&&(s=s.split("|").join(this.tic));_stack.push(s+this.tic);i+=1}else if(this._isUpperChar(formula[i]))if(s=this._processUpperCase(formula,i,sheet),formula=this._processUpperCaseFormula,i=this._processUpperCaseIvalue,sheet=this._processUpperCaseSheet,uFound)s=this.getValueFromParentObjectCell(s),d3=this._parseDouble(s),isNaN(d3)?_stack.push(s):(d3=-d3,_stack.push(d3.toString()));else if(this.getUseFormulaValues()&&this.computedValueLevel>50){if(this._breakedFormulaCells.indexOf(s)==-1&&this.getUseFormulaValues()){family=CalcEngine.getSheetFamilyItem(this.grid);token=this.sheetToken+this.getSheetID(this.grid).toString()+this.sheetToken;s=token+s;this._breakedFormulaCells.add(s);this._tempBreakedFormulaCells.add(s);break}_stack.push("FALSE")}else _stack.push(this.getValueFromParentObjectCell(s));else if(formula[i]=="q")if(formula=this._computeInteriorFunctions(formula),ii=formula.substring(i+1).indexOf(this._leftBracket),ii>0){for(var bracketCount=0,bracketFound=!1,start=ii+i+2,k=start;k<formula.length&&(formula[k]!=this._rightBracket||bracketCount>0);)formula[k]==this._leftBracket?(bracketCount++,bracketFound=!0):formula[k]==this._leftBracket&&bracketCount--,k++;if(bracketFound){var s=this._substring(formula,start,k-start-2),s1="",splits=this.splitArgsPreservingQuotedCommas(s);for(t=0;t<splits.length;t++)s1.length>0&&(s1+=","),j=this._findLastqNotInBrackets(t),s1+=j>0?splits[t].substring(0,j)+this.computedValue(splits[t].substring(j)):this.computedValue(splits[t]);formula=formula.substring(0,start)+s1+formula.substring(k-2)}if(name=this._substring(formula,i+1,ii),name=="AVG"&&this._excelLikeComputations)throw this.formulaErrorStrings[this._bad_index];if(this.getLibraryFunctions().getItem(name)!=undefined){j=formula.substring(i+ii+1).indexOf(this._rightBracket);args=this._substring(formula,i+ii+2,j-1);try{if(functionName=this.getLibraryFunctions().getItem(name),result=this.getCustomLibraryFunctions().getItem(name)!=undefined?eval(functionName)(args,this.parentObject):this[functionName](args),isErrorStringValue=!1,this.getErrorStrings().indexOf(result)>0&&(isErrorStringValue=!0),!ej.isNullOrUndefined(this.parentObject)&&this.parentObject.pluginName=="ejSpreadsheet"&&this.parentObject.model.formulaComputed!=null){if(args={reqType:"formulaComputed",Formula:name,ComputedValue:result,Cell:this.cell,IsErrorString:isErrorStringValue,IsInteriorFunction:this._isInteriorFunction,Handled:!1},this.parentObject._trigger("formulaComputed",args),args.Handled)return this._string_empty;result=args.ComputedValue}_stack.push(result)}catch(ex){if(this.getRethrowLibraryComputationExceptions()){this._libraryComputationException=ex;throw ex;}return ex}i+=j+ii+2}else if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._missing_formula];}else if(formula[0]==this.bMARKER){i=0;_stack.length=0;continue}else throw this.formulaErrorStrings[this._improper_formula];else if(this._isDigit(formula[i])||formula[i]=="u"){if(s="",(formula[i]=="u"||uFound)&&(s="-",uFound?uFound=!1:i++),i<formula.length&&this._isUpperChar(formula[i]))s=s+this.getValueFromParentObjectCell(this._processUpperCase(formula,i,sheet)),formula=this._processUpperCaseFormula,i=this._processUpperCaseIvalue,sheet=this._processUpperCaseSheet;else while(i<formula.length&&(this._isDigit(formula[i])||formula[i]==this.getParseDecimalSeparator()))s=s+formula[i],i=i+1;_stack.push(s)}else if(formula[i]==this._parseArgumentSeparator){i++;continue}else switch(formula[i]){case"#":errIndex=0;this.getErrorStrings().indexOf(formula.substring(i)>-1)?(errIndex=formula.indexOf("!")==-1||formula.substring(i).indexOf("!")==-1?formula.indexOf("#N/A")>-1?formula.indexOf("#N/A")+4+i:formula.indexOf("?")+1+i:formula.indexOf("!")+1+i,_stack.push(this._substring(formula,i,errIndex-i))):(errIndex=i+1,_stack.push(this._substring(formula,i,errIndex-i)));i=errIndex;break;case"n":if(i=i+1,s="",formula.substring(i).indexOf("Infinity")==0)s="Infinity",i+=s.length;else if(formula.substring(i).indexOf("uInfinity")==0)s="Infinity",i+=s.length+1;else if(formula.substring(i).indexOf(this.trueValueStr)==0)s=this.trueValueStr,i+=s.length;else if(formula.substring(i).indexOf(this.falseValueStr)==0)s=this.falseValueStr,i+=s.length;else if(i<=formula.length-3&&formula.substring(i,3)=="NaN")i+=3,s="0";else{for((formula[i]=="u"||uFound)&&(s="-",uFound?uFound=!1:i=i+1);i<formula.length&&(this._isDigit(formula[i])||formula[i]==this.getParseDecimalSeparator());)s=s+formula[i],i=i+1;i<formula.length&&formula[i]=="%"?(i=i+1,s==""?_stack.length>0&&(o=_stack[0],d=this._parseDouble(o.toString()),isNaN(d)||(_stack.pop(),s=(d/100).toString())):s=(this._parseDouble(s)/100).toString()):i<formula.length-2&&formula[i]=="E"&&(formula[i+1]=="+"||formula[i+1]=="-")&&(s+=this._substring(formula,i,4),i+=4)}_stack.push(s);break;case this.token_add:if((d=this._pop(_stack),x=parseInt(d.toString()),!isNaN(x)&&this._isErrorString)||(d1=this._pop(_stack),x=parseInt(d1.toString()),!isNaN(x)&&this._isErrorString))return this._isErrorString=!1,this.getErrorStrings()[x].toString();isNaN(d)||isNaN(d1)?_stack.push("#VALUE!"):_stack.push((Number(d1)+Number(d)).toString());i=i+1;break;case this.token_subtract:if((d=this._pop(_stack),x=parseInt(d.toString()),!isNaN(x)&&this._isErrorString)||(d1=this._pop(_stack),x=parseInt(d1.toString()),!isNaN(x)&&this._isErrorString))return this._isErrorString=!1,this.getErrorStrings()[x].toString();isNaN(d)||isNaN(d1)?_stack.push("#VALUE!"):_stack.push((d1-d).toString());i=i+1;break;case this.token_multiply:if((d=this._pop(_stack),x=parseInt(d.toString()),!isNaN(x)&&this._isErrorString)||(d1=this._pop(_stack),x=parseInt(d1.toString()),!isNaN(x)&&this._isErrorString))return this._isErrorString=!1,this.getErrorStrings()[x].toString();isNaN(d)||isNaN(d1)?_stack.push("#VALUE!"):_stack.push((d1*d).toString());i=i+1;break;case this.token_divide:if((d=this._pop(_stack),x=parseInt(d.toString()),!isNaN(x)&&this._isErrorString)||(d1=this._pop(_stack),x=parseInt(d1.toString()),!isNaN(x)&&this._isErrorString))return this._isErrorString=!1,this.getErrorStrings()[x].toString();isNaN(d)||isNaN(d1)?_stack.push("#VALUE!"):d==0?_stack.push(this.getErrorStrings()[3]):_stack.push((d1/d).toString());i=i+1;break;case this.token_EP:d=this._pop(_stack);d1=this._pop(_stack);_stack.push((d1*Math.pow(10,d)).toString());i=i+1;break;case this.token_EM:d=this._pop(_stack);d1=this._pop(_stack);_stack.push((Number(d1)*Math.pow(10,-Number(d))).toString());i=i+1;break;case this.token_less:s1=this._popString(_stack);s2=this._popString(_stack);s2==""&&this.getTreatStringsAsZero()&&s1!=""&&(s2="0");d=this._parseDouble(s1);d1=this._parseDouble(s2);val=isNaN(d)||isNaN(d1)?s1=="#VALUE!"||s2=="#VALUE!"?"#VALUE!":s2.toUpperCase().split(this.tic).join("").localeCompare(s1.toUpperCase().split(this.tic).join(""))<0?this.trueValueStr:this.falseValueStr:d1<d?this.trueValueStr:this.falseValueStr;_stack.push(val);i=i+1;break;case this.token_greater:s1=this._popString(_stack);s2=this._popString(_stack);s2==""&&s1!=""&&(s2=this.getTreatStringsAsZero()?"0":s1+1);d=this._parseDouble(s1);d1=this._parseDouble(s2);val=isNaN(d)||isNaN(d1)?s1=="#VALUE!"||s2=="#VALUE!"?"#VALUE!":s2.toUpperCase().split(this.tic).join("").localeCompare(s1.toUpperCase().split(this.tic).join(""))>0?this.trueValueStr:this.falseValueStr:Number(d1)>Number(d)?this.trueValueStr:this.falseValueStr;_stack.push(val);i=i+1;break;case this.token_equal:s1=this._popString(_stack);s2=this._popString(_stack);s2==""&&this.getTreatStringsAsZero()&&s1!=""&&(s2="0");d=this._parseDouble(s1);d1=this._parseDouble(s2);val=isNaN(d)||isNaN(d1)?s1=="#VALUE!"||s2=="#VALUE!"?"#VALUE!":s1.toUpperCase().split(this.tic).join("")==s2.toUpperCase().split(this.tic).join("")?this.trueValueStr:this.falseValueStr:Number(d1)==Number(d)?this.trueValueStr:this.falseValueStr;_stack.push(val);i=i+1;break;case this.token_lesseq:s1=this._popString(_stack);s2=this._popString(_stack);s2==""&&this.getTreatStringsAsZero()&&s2!=""&&(s2="0");d=this._parseDouble(s1);d1=this._parseDouble(s2);val=isNaN(d)||isNaN(d1)?s1=="#VALUE!"||s2=="#VALUE!"?"#VALUE!":s1.toUpperCase().split(this.tic).join("").localeCompare(s2.toUpperCase().split(this.tic).join(""))<=0?this.trueValueStr:this.falseValueStr:Number(d1)<=Number(d)?this.trueValueStr:this.falseValueStr;_stack.push(val);i=i+1;break;case this.token_greatereq:s1=this._popString(_stack);s2=this._popString(_stack);s2==""&&this.getTreatStringsAsZero()&&s1!=""&&(s2="0");d=this._parseDouble(s1);d1=this._parseDouble(s2);val=isNaN(d)||isNaN(d1)?s1=="#VALUE!"||s2=="#VALUE!"?"#VALUE!":s1.toUpperCase().split(this.tic).join("").localeCompare(s2.toUpperCase().split(this.tic).join(""))>=0?this.trueValueStr:this.falseValueStr:Number(d1)>=Number(d)?this.trueValueStr:this.falseValueStr;_stack.push(val);i=i+1;break;case this.token_noequal:s1=this._popString(_stack);s2=this._popString(_stack);s2==""&&this.getTreatStringsAsZero()&&s1!=""&&(s2="0");d=this._parseDouble(s1);d1=this._parseDouble(s2);val=isNaN(d)||isNaN(d1)?s1=="#VALUE!"||s2=="#VALUE!"?"#VALUE!":s1.toUpperCase().split(this.tic).join("")!=s2.toUpperCase().split(this.tic).join("")?this.trueValueStr:this.falseValueStr:Number(d1)!=Number(d)?this.trueValueStr:this.falseValueStr;_stack.push(val);i=i+1;break;case this.token_and:s1=this._popString(_stack);s2="";_stack.length>0&&(s2=this._popString(_stack));this.getUseNoAmpersandQuotes()?_stack.push(s2+s1):_stack.push(this.tic+s2+s1+this.tic);i=i+1;break;case this.token_or:d=this._pop(_stack);d1=this._pop(_stack);_stack.push(Math.pow(d1,d).toString());i=i+1;break;case this.token_ORop:var s1=this._popString(_stack),s2=this._popString(_stack),d=this._parseDouble(s1),d1=this._parseDouble(s2),val;s1==""?s1=this.falseValueStr:isNaN(d)||(s1=Number(s1)!=0?this.trueValueStr:this.falseValueStr);s2==""?s2=this.falseValueStr:isNaN(d1)||(s2=Number(s2)!=0?this.trueValueStr:this.falseValueStr);val=s1.toUpperCase().split(this.tic).join("")==this.trueValueStr||s2.toUpperCase().split(this.tic).join("")==this.trueValueStr?this.trueValueStr:this.falseValueStr;_stack.push(val);i=i+1;break;case this.token_ANDop:var s1=this._popString(_stack),s2=this._popString(_stack),d=this._parseDouble(s1),d1=this._parseDouble(s2),val;s1==""?s1=this.falseValueStr:isNaN(d)||(s1=Number(s1)!=0?this.trueValueStr:this.falseValueStr);s2==""?s2=this.falseValueStr:isNaN(d1)||(s2=Number(s2)!=0?this.trueValueStr:this.falseValueStr);val=s1.toUpperCase().split(this.tic).join("")==this.trueValueStr&&s2.toUpperCase().split(this.tic).join("")==this.trueValueStr?this.trueValueStr:this.falseValueStr;_stack.push(val);i=i+1;break;case this.token_XORop:var s1=this._popString(_stack),s2=this._popString(_stack),d=this._parseDouble(s1),d1=this._parseDouble(s2),val;s1==""?s1=this.falseValueStr:isNaN(d)||(s1=Number(s1)!=0?this.trueValueStr:this.falseValueStr);s2==""?s2=this.falseValueStr:isNaN(d1)||(s2=Number(s2)!=0?this.trueValueStr:this.falseValueStr);val=s1.toUpperCase().split(this.tic).join("")==this.trueValueStr&&s2.toUpperCase().split(this.tic).join("")!=this.trueValueStr||s2.toUpperCase().split(this.tic).join("")==this.trueValueStr&&s1.toUpperCase().split(this.tic).join("")!=this.trueValueStr?this.trueValueStr:this.falseValueStr;_stack.push(val);i=i+1;break;case this.token_NOTop:s1=this._popString(_stack);d=this._parseDouble(s1);s1==""?s1=this.falseValueStr:isNaN(d)||(s1=Number(s1)!=0?this.trueValueStr:this.falseValueStr);val=s1.toUpperCase().split(this.tic).join("")==this.falseValueStr?this.trueValueStr:this.falseValueStr;_stack.push(val);i=i+1;break;default:if(this.computedValueLevel=0,this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_expression];else return this.getErrorStrings()[1].toString()}}if(this._checkDanglingStack&&_stack.length>1&&formula.length>1&&(formula.indexOf(this.bmarker.toString())!=0||formula.indexOf(this.bmarker)!=formula.length-1||formula.indexOf(this.bmarker2)>-1))return this.computedValueLevel=0,this.formulaErrorStrings[this._improper_formula];if(_stack.length==0)return"";s="";cc=_stack.length;do return(s=_stack.pop().toString()+s,s==""&&!this.getUseNoAmpersandQuotes()&&this._isCellReference(formula)&&this.getTreatStringsAsZero())?"0":!ej.isNullOrUndefined(ej.cultureObject)&&ej.cultureObject.name!="en-US"&&!isNaN(this._parseDouble(s))?_getLocaleNumberValue(s):s;while(cc>0&&!(s.indexOf(this.FALSEVALUESTR)>-1||s.indexOf(this.tRUEVALUESTR)>1));return!ej.isNullOrUndefined(ej.cultureObject)&&ej.cultureObject.name!="en-US"&&!isNaN(this._parseDouble(s))?_getLocaleNumberValue(s):s}catch(ex){if(this.computedValueLevel=0,ex.toString().indexOf(this.formulaErrorStrings[this._circular_reference_])>-1||this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();throw ex;}if(ex.toString().indexOf(this.formulaErrorStrings[this._cell_empty])>-1)return"";if(this.getRethrowLibraryComputationExceptions()){this._libraryComputationException=ex;throw ex;}return ex}finally{this.computedValueLevel--;this._isInteriorFunction=!1;this.computedValueLevel<0&&(this.computedValueLevel=0)}};this.computeFormula=function(n){if(this.getThrowCircularException())if(this._iterationMaxCount>0){if(this.computedValueLevel==0)this._circCheckList.length=0,this._circCheckList.push(this.cell);else if(this._circCheckList.indexOf(this.cell)>-1)return this.getIterationValues().containsKey(this.cell)||this.getIterationValues().add(this.cell,"0"),this.getIterationValues().getItem(this.cell).toString()}else{if(this.computedValueLevel==0)this._circCheckList.length=0;else if(this._circCheckList.indexOf(this.cell)>-1){this.computedValueLevel=0;this._circCheckList.length=0;throw this.formulaErrorStrings[this._circular_reference_];}this._circCheckList.push(this.cell)}var t=this.computedValue(n);return this.getUseNoAmpersandQuotes()&&t.length>1&&t[0]==this.tic[0]&&t[t.length-1]==this.tic[0]&&(t=this._substring(t,1,t.length-2)),t};this.dispose=function(){if(!this._isDisposed){var n=0;this.grid!=null&&(n=this.getSheetID(this.grid));CalcEngine._tokenCount--;CalcEngine.sheetFamilyID--;CalcEngine._sheetFamiliesList!=null&&CalcEngine._sheetFamiliesList.length>0&&(CalcEngine._sheetFamiliesList[n]=null,CalcEngine._sheetFamiliesList.remove(n));CalcEngine.modelToSheetID!=null&&CalcEngine.modelToSheetID.length>0&&(CalcEngine.modelToSheetID[n]=null,CalcEngine._sheetFamiliesList.remove(n));this._formulaInfoTable!=null&&(this._formulaInfoTable.clear(),this._formulaInfoTable=null);this._circCheckList!=null&&(this._circCheckList.length=0,this._circCheckList=null);this._dependentCells!=null&&(this._dependentCells.clear(),this._dependentCells=null);this._dependentFormulaCells!=null&&(this._dependentFormulaCells.clear(),this._dependentFormulaCells=null);this.getIterationValues()!=null&&(this.getIterationValues().clear(),this.getIterationValues(null));this._libraryFunctions!=null&&(this._libraryFunctions.clear(),this._libraryFunctions=null);this._lookupTables!=null&&(this._lookupTables.clear(),this._lookupTables=null);this.grid=null;this.getLibraryFunctions()!=null&&(this.getLibraryFunctions().clear(),this.setLibraryFunctions(null));this._isDisposed=!0}};this.getCalcID=function(){return this._calcID};this._getCellFrom=function(n){var u="",t=this.getCellsFromArgs(n),i=t.length-1,f=this.rowIndex(t[0]),o,s,r;if(f==this.rowIndex(t[i])){var h=this.colIndex(t[0]),c=this.colIndex(t[i]),e=this.colIndex(this.cell);e>=h&&e<=c&&(u=RangeInfo.getAlphaLabel(e).toString()+f.toString())}else(o=this.colIndex(t[0]))==this.colIndex(t[i])&&(s=this.rowIndex(t[i]),r=this.rowIndex(this.cell),r>=f&&r<=s&&(u=RangeInfo.getAlphaLabel(o).toString()+r.toString()));return u};this.getCellsFromArgs=function(n){var i,f,t,h,l,e,s,a,u,o;if(n=this._markColonsInQuotes(n),t=n.indexOf(":"),t==-1)return n=n.split(this._markerChar).join(":"),t=n.indexOf(this.getParseArgumentSeparator()),t==-1?(i=this.rowIndex(n),f=this.colIndex(n),h=[],h.push(n),h):this.splitArgsPreservingQuotedCommas(n);this.getRowIndexFromName=function(n){var i,t;if(n.indexOf(":")==-1){for(i="",t=0;t<n.length;t++)this._isDigit(n[t])&&(i+=n[t]);return i}};var c="",y=n,r=n.indexOf(this.sheetToken);if(r>-1&&(l=n.indexOf(this.sheetToken,r+1),l>-1&&(c=this._substring(n,r,l-r+1),n=n.split(c).join(""),t=n.indexOf(":"))),t>0&&this._isLetter(n[t-1]))s=this._rowMaxCount>0?this._rowMaxCount:this.parentObject&&this.parentObject.pluginName=="ejSpreadsheet"&&this.parentObject.getSheet(this.parentObject.getActiveSheetIndex()).usedRange.rowIndex||50,n=n.substring(0,t)+"1:"+n.substring(t+1)+s.toString(),t=n.indexOf(":");else if(t>0&&this._isDigit(n[t-1])){for(e=t-2;e>=0&&this._isDigit(n[e]);)e--;e!=-1&&this._isLetter(n[e])||(s=this._columnMaxCount>0?this._columnMaxCount:50,n="A"+n.substring(0,t)+":"+RangeInfo.getAlphaLabel(s)+n.substring(t+1),t=n.indexOf(":"))}if(a=this._canGetRowIndex(n.substring(0,t)),!a)return this._ignoreCellValue=!0,n=y,this.splitArgsPreservingQuotedCommas(n);if(i=this.rowIndex(n.substring(0,t)),u=this.rowIndex(this._substring(n,t+1,n.length-t-1)),!(!(i==-1)||u==-1)==(i==-1||!(u==-1)))throw this.getErrorStrings()[5].toString();f=this.colIndex(n.substring(0,t));o=this.colIndex(this._substring(n,t+1,n.length-t-1));i>u&&(t=u,u=i,i=t);f>o&&(t=o,o=f,f=t);var w=(u-i+1)*(o-f+1),v=[],p=0;for(t=i;t<=u;++t)for(r=f;r<=o;++r)try{v[p++]=c+RangeInfo.getAlphaLabel(r)+t.toString()}catch(b){continue}return v};this.getFormulaRowCol=function(n,t,i){var f=CalcEngine.getSheetFamilyItem(n),r=RangeInfo.getAlphaLabel(i)+t.toString(),e,o,u;return(f.sheetNameToParentObject!=null&&(e=f.parentObjectToToken.getItem(n),r=e+r),this.getFormulaInfoTable().containsKey(r))?(o=this.getFormulaInfoTable.getItem(r),u=o.getFormulaText(),this.getFormulaText(u),u):""};this.getFormulaText=function(n){var t=new FormulaParsing(n);return t.getText()};this.getSheetID=function(n){var i=CalcEngine.getSheetFamilyItem(n),t,r;return i.sheetNameToParentObject!=null&&i.sheetNameToParentObject.length>0&&(t=i.parentObjectToToken.getItem(n),t=t.split("!").join(""),r=this._parseDouble(t),!isNaN(r))?r:-1};this.getStringArray=function(n){for(var f,r=[],i=0,u=!1,t=0;t<n.length;t++)f=n[t],f==this.tic[0]?u=!u:u||f!=this.getParseArgumentSeparator()||(r.push(this._substring(n,i,t-i)),i=t+1);return r.push(n.substring(i)),r};this.getValueFromArg=function(n){var r,i=this._dateTime1900,f,u,s,o,h,e,t,c;if(isNaN(n)||(n=n.toString()),this._isTextEmpty(n))return n;if(n[0]==this.tic[0])return(i=this._isDate(n.split(this.tic).join("")),this.getExcelLikeComputations()&&this.getUseDatesInCalculations()&&isNaN(this._parseDouble(n.split(this.tic).join("")))&&!isNaN(i)&&!isNaN(i.getDate())&&this._dateTime1900<=i)?this._toOADate(i).toString():n;if(n[0]==this.bMARKER||n[0]=="q"||n[0]=="!"||n.indexOf(":")>-1)return n=n.split("{").join("("),n=n.split("}").join(")"),this.computedValue(n);if(n.length>1&&n.substring(0,2)=="ub")return(n=n.split("{").join("("),n=n.split("}").join(")"),f=n.substring(1),f=this.computedValue(f),u=this._parseDouble(f),!isNaN(u))?(u=-u,u.toString()):this.computedValue(n);if(n.indexOf("unu")==0?n="n"+n.substring(3):n.indexOf("un")==0&&(n="-"+n.substring(2)),n=n.split("u").join("-"),!this._isUpperChar(n[0])&&(this._isDigit(n[0])||n[0]==this.getParseDecimalSeparator()||n[0]=="-"||n[0]=="n"))if(n[0]=="n"&&(n=n.substring(1)),r=ej.isNullOrUndefined(ej.cultureObject)||ej.cultureObject.name=="en-US"?this._parseDouble(n):ej.isNullOrUndefined(n)?NaN:ej.parseFloat(n.toString(),0,ej.cultureObject.name),isNaN(r)){if(n.indexOf(this.trueValueStr)==0||n.indexOf(this.falseValueStr)==0)return n}else return this._preserveLeadingZeros?n:r.toString();if(this._ignoreCellValue&&!(n.indexOf(this.trueValueStr)==0||n.indexOf(this.falseValueStr)==0))return this._ignoreCellValue=!1,n;if(s=["+","-","/","*",")",")","{"],this._indexOfAny(n,s)==-1&&this._isUpperChar(n[0])||n[0]==this.sheetToken){if(n!=this.trueValueStr&&n!=this.falseValueStr&&this._isCellReference(n)&&(o=CalcEngine.getSheetFamilyItem(this.grid),o.sheetNameToParentObject!=null&&n.indexOf(this.sheetToken)==-1&&(h=o.parentObjectToToken.getItem(this.grid),n=h+n)),n==this.cell){e=this.getDependentCells().getItem(n);e!=null&&e.indexOf(n)>-1&&this._arrayRemove(e,n);this.getDependentFormulaCells().containsKey(this.cell)||this.clearFormulaDependentCells(this.cell);throw this.formulaErrorStrings[this._circular_reference_]+n;}return(t=this.getValueFromParentObjectCell(n),i=this._isDate(t),t=t.toString(),n!=this.trueValueStr&&n!=this.falseValueStr&&(r=this._parseDouble(t.split(this.tic).join("")),!this._preserveLeadingZeros&&!this._isCellReference(n)&&t.length>0&&!isNaN(r)&&(t=r.toString()),this.updateDependencies(n)),t==this.tic+this.tic)?"NaN":(this.getExcelLikeComputations()&&this.getUseDatesInCalculations()&&!isNaN(i)&&isNaN(this._parseDouble(t))&&!isNaN(i.getDate())&&this._dateTime1900<=i&&(t=this._toOADate(i).toString()),t)}return(n=n.split("{").join("("),n=n.split("}").join(")"),n=this._parse(n),r=ej.parseFloat(n.substring(0,n.length-1)),n.indexOf("%")!=n.length-1||isNaN(r)||(n=(Number(r)/100).toString()),this.getErrorStrings().indexOf(n)>-1)?n:this.computedValue(n)};this.getValueFromParentObjectCell=function(n){var t,e,s;if(n==this.trueValueStr||n==this.falseValueStr)return n;var r=n.lastIndexOf(this.sheetToken),u=0,f=0,o=this.grid,i=CalcEngine.getSheetFamilyItem(this.grid);if(r>-1&&i.tokenToParentObject!=null)this.grid=i.tokenToParentObject.getItem(n.substring(0,r+1)),u=this.rowIndex(n),f=this.colIndex(n);else if(r==-1){for(t=0;t<n.length&&this._isLetter(n[t]);)t++;if(t==n.length){if(n=n.toLowerCase(),this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[5].toString()}u=this.rowIndex(n);f=this.colIndex(n);this.isSheetMember()&&i.parentObjectToToken!=null&&(n=i.parentObjectToToken.getItem(this.grid)+n)}if(e=this.cell==""||this.cell==null?"":this.cell,this.cell=n,this._iterationMaxCount>0){if(this._circCheckList.indexOf(this.cell)>-1&&this.computedValueLevel>0)return this.grid=o,this.cell=e,this.getIterationValues().containsKey(this.cell)||this.getIterationValues().add(this.cell,"0"),this.getIterationValues().getItem(this.cell).toString();if(this.getIterationValues().containsKey(this.cell)&&this.computedValueLevel>0)return this.getIterationValues.getItem(this.cell).toString()}return s=this._getValueComputeFormulaIfNecessary(u,f,this.grid),this.grid=o,this.cell=e,s};this.getValueFromParentObject=function(n,t,i){var u=CalcEngine.getSheetFamilyItem(n),f=u.parentObjectToToken==null||u.parentObjectToToken.length==0?"":u.parentObjectToToken.getItem(n).toString(),o,s;f=f+RangeInfo.getAlphaLabel(i)+t.toString();o=this.grid;s=this.cell;this.cell=f;this.grid=n;var r=this._getValueComputeFormulaIfNecessary(t,i,n),e=new Date(Date.parse(r)),h=this._parseDouble(r);return this.getExcelLikeComputations()&&this.getUseDatesInCalculations()&&typeof r!="string"&&isNaN(h)&&!isNaN(e.getDate())&&this._dateTime1900<=e&&(r=this._toOADate(e).toString()),this.grid=o,this.cell=s,r};this.getValueFromParentObjectRowCol=function(n,t){return this._getValueComputeFormulaIfNecessary(n,t,this.grid)};this.handleIteration=function(n,t){return this.getFormulaInfoTable().containsKey(n)?this.getFormulaInfoTable.add(n,t):this.getFormulaInfoTable.add(n,t),this._iterationMaxCount>0&&n==this.cell&&this._handleIterations(t),t.getFormulaValue()};this.handleSheetRanges=function(n,t){for(var r=n.indexOf(this.sheetToken),i=0,o,s,e,u,v,f,y,h,c;r>0;){if(o=this._substring(n,i,r).lastIndexOf(":"),o>-1)if(s=this._substring(n,i+o+1,r-o-1).toUpperCase().split("'").join(""),e=this._isIE8?s.replace(/^\s+|\s+$/g,""):s.trim(),t.sheetNameToToken.containsKey(e)){if(u=i+o-1,this._sortedSheetNames.contains(s))u=u-s.length-1;else while(u>0&&this._markers.indexOf(n[u])==-1)u--;if(v=this._substring(n,u+1,o-u+i-1).toUpperCase().split("'").join(""),e=this._isIE8?v.replace(/^\s+|\s+$/g,""):v.trim(),t.sheetNameToToken.containsKey(e)){if(f=r+i+1,this._sortedSheetNames.contains(e))f=n.length-1;else while(f<n.length&&this._markers.indexOf(n[f])==-1)f++;var w=this._substring(n,i+r+1,f-i-r-1),l=t.sheetNameToToken.getItem(e).toString(),a=t.sheetNameToToken.getItem(e).toString();for(a<l&&(y=l,l=a,a=y),h="",c=0;c<_sortedSheetNames.length;c++){var y=t.sheetNameToToken.getItem(this._sortedSheetNames[c]).toString(),p=parseInt(y.split("!").join("")),b=parseInt(l.split("!").join("")),k=parseInt(a.split("!").join(""));p>b&&p<k&&(h.length>0&&(h+=this.getParseArgumentSeparator()),h+=c+String.fromCharCode(131)+w)}n=n.substring(0,u+1)+h+n.substring(f);i=n.length-f}else i=r+i}else i=r+i;else i=r+i;r=n.substring(i+1).indexOf(this.sheetToken)+1}return n.split(String.fromCharCode(131)).join(this.sheetToken)};this.isSheetMember=function(){var n=CalcEngine.getSheetFamilyItem(this.grid);return n==null||n==undefined?!1:n.isSheetMember};this.parseAndComputeFormula=function(n){var i=n,r,t,u,f;for(i.length>0&&i[0]==this.getFormulaCharacter()&&(i=i.substring(1)),i.length>0&&i[0]=="+"&&(i=i.substring(1)),i.length>1&&i[0]==","&&i[1]>="0"&&i[1]<="9"&&(i="0,"+i.substring(1)),r=i.split(" ").join(""),t=r.indexOf("(");t!=-1&&t<r.length;t++)if(t>0){if(this._isDigit(r[t-1])&&!this._checkHasCharBeforeNumber(r.substring(0,t)))throw this.formulaErrorStrings[this._bad_formula]+" "+r.substring(0,t+1);if(u=r.substring(t+1).indexOf("("),u==-1)break;else t+=u}for(t=r.indexOf(")");t!=-1&&t<r.length-1;t++){if(this._isDigit(r[t+1]))throw this.formulaErrorStrings[this._bad_formula]+" "+r.substring(0,t+2);if(u=r.substring(t+1).indexOf(")"),u==-1)break;else t+=u}return this._multiTick=!1,f=this._parse(i),this.computedValue(f)};this.parseFormula=function(n){var t;if(n.length>0&&n[0]==this.getFormulaCharacter()&&(n=n.substring(1)),n.length>0&&n[0]=="+"&&(n=n.substring(1)),this._isRangeOperand=this._supportRangeOperands&&this._isRange(n),this.getCheckDanglingStack()&&n.split(" ").join("").indexOf(this._braceRightNLeft)>-1)return this.computedValueLevel=0,this.formulaErrorStrings[this._improper_formula];if(t=this._isIE8?n.replace(/^\s+|\s+$/g,""):n.trim(),!ej.isNullOrUndefined(this.parentObject)&&this.parentObject.pluginName=="ejSpreadsheet"){var r=this.rowIndex(this.cell),u=this.colIndex(this.cell),i=this.parentObject.XLEdit.getPropertyValue(r-1,u-1,"hasFormulaArray");this._isArrayFormula=ej.isNullOrUndefined(i)?!1:i;this._isArrayFormula&&(t=this._parseLibraryFormula("{"+t+"}"))}return this._parse(t)};this.getUpdatedValueCell=function(n){var i,h,c,f,e,u,s;this._inAPull=!0;var l=this.grid,a=this.cell,o=CalcEngine.getSheetFamilyItem(this.grid),t=n.toUpperCase(),r;if((r=t.indexOf(this.sheetToken))==-1&&this.cell!=""&&this.cell!=undefined?(r=this.cell.indexOf(this.sheetToken,1),r>-1&&o.tokenToParentObject!=null&&(t=this.cell.substring(0,r+1)+t,this.grid=o.tokenToParentObject.getItem(this.cell.substring(0,r+1)))):r>0&&o.sheetNameToToken!=null&&o.tokenToParentObject!=null&&(u=o.sheetNameToToken.getItem(t.substring(0,r)),t=u+t.substring(r+1),this.grid=o.tokenToParentObject.getItem(u),this.cell=t),this.updateCalcID(),this.getDependentFormulaCells().containsKey(t)||this.getFormulaInfoTable().containsKey(t))this._processedCells.clear(),this.updateDependenciesAndCell(t),this._processedCells.clear(),i=this.getValueFromParentObject(t);else{for(i=this.getValueFromParentObjectCell(t);this._breakedFormulaCells.length==1;)this.updateCalcID(),i=this.getValueFromParentObjectCell(this._breakedFormulaCells[0].toString()),this._breakedFormulaCells.removeAt(0),this.setUseFormulaValues(!0);if(this.getUseFormulaValues()&&(this.isUseFormulaValueChanged=!0,this.setUseFormulaValue(!1)),this._tempBreakedFormulaCells.length>0){for(h=1;h<=this._tempBreakedFormulaCells.length;h++)i=this.getValueFromParentObjectCell(this._tempBreakedFormulaCells[this._tempBreakedFormulaCells.length-h].toString());this._tempBreakedFormulaCells.length=0;i=this.getValueFromParentObjectCell(t)}this.setUseFormulaValues(this.isUseFormulaValueChanged);c=this.ignoreValueChanged;this.ignoreValueChanged=!0;f=this.rowIndex(t);e=this.colIndex(t);this.getPreserveFormula()?(u=this._sheetToken(n),u==""?(u="!"+this.getSheetID(this.grid)+"!",s=this.getFormulaInfoTable().getItem(u+t),s!=null&&s!=undefined&&(this.parentObject.setValueRowCol==undefined?this.setValueRowCol(this.getSheetID(this.grid)+1,s.getFormulaText(),f,e):this.parentObject.setValueRowCol(this.getSheetID(this.grid)+1,s.getFormulaText(),f,e))):this.parentObject.setValueRowCol==undefined?this.setValueRowCol(this.getSheetID(this.grid)+1,i,f,e):this.parentObject.setValueRowCol(this.getSheetID(this.grid)+1,i,f,e)):this.parentObject.setValueRowCol==undefined?this.setValueRowCol(this.getSheetID(this.grid)+1,i,f,e):this.parentObject.setValueRowCol(this.getSheetID(this.grid)+1,i,f,e);this.ignoreValueChanged=c}return this.grid=l,this.cell=a,this._inAPull=!1,i};this.getUpdatedValueRowCol=function(n,t,i){var o=this.grid,s=CalcEngine.getSheetFamilyItem(this.grid),f=this.sheetToken+n.toString()+this.sheetToken,r,u,e;this.grid=s.tokenToParentObject.getItem(f);this._inAPull=!0;r=f+RangeInfo.getAlphaLabel(i)+t.toString();this.updateCalcID();this.getDependentFormulaCells().containsKey(r)||this.formulaInfoTable.containsKey(r)?(this._processedCells.clear(),this.updateDependenciesAndCell(r),this._processedCells.clear()):(u=this.getValueFromParentObjectCell(r),e=this.ignoreValueChanged,this.ignoreValueChanged=!0,this.parentObject.setValueRowCol==undefined?this.setValueRowCol(this.getSheetID(this.grid)+1,u,t,i):this.parentObject.setValueRowCol(this.getSheetID(this.grid)+1,u,t,i),this.ignoreValueChanged=e);this.grid=o;this._inAPull=!1};this.setTokensForSheets=function(n){var f=CalcEngine.getSheetFamilyItem(this.grid),t,i,u,r;if(this.getSupportsSheetRanges()&&(n=this.handleSheetRanges(n,f)),t=this.getSortedSheetNames(),t!=null)for(i=0;i<t.length;i++)u=f.sheetNameToToken.getItem(t[i]),u=u.split(this.sheetToken).join(this._tempSheetPlaceHolder),r="'"+t[i].toUpperCase()+"'"+this.sheetToken,n.indexOf(r)==-1&&(r=t[i].toUpperCase()+this.sheetToken),n=n.split(r).join(u),r=t[i].toUpperCase()+this.sheetToken,n=n.split(r).join(u);return n.split(this._tempSheetPlaceHolder).join(this.sheetToken)};this.recalculateRange=function(n,t){var i,r;for(this._inRecalculateRange=!0,i=n.getTop();i<=n.getBottom();++i)for(r=n.getLeft();r<=n.getRight();++r)this.parentObject.getValueRowCol!=undefined?this.parentObject.setValueRowCol(this.getSheetID(t)+1,this.parentObject.getValueRowCol(this.getSheetID(t)+1,i,r),i,r):t.setValueRowCol(this.getSheetID(t)+1,t.getValueRowCol(this.getSheetID(t)+1,i,r),i,r);this._inRecalculateRange=!1};this.refresh=function(n){var u,t,o,f,c,r,l;if(!this.getCalculatingSuspended()&&(this._dependencyLevel==0&&this._refreshedCells.clear(),this.getDependentCells().containsKey(n)&&this.getDependentCells().getItem(n)!=null)){this._dependencyLevel++;try{var a=CalcEngine.getSheetFamilyItem(this.grid),e=this.getDependentCells().getItem(n),v=this._lockDependencies;for(this._lockDependencies=!0,u=0;u<e.length;u++)if(t=e[u],t!=null){o=this.grid;f=this._sheetToken(t);f.length>0&&(this.grid=a.tokenToParentObject.getItem(f));try{var s=this.rowIndex(t),h=this.colIndex(t),i=this.getFormulaInfoTable().getItem(t);i!=null&&(c=this.cell,this.cell=t,(this.getAlwaysComputeDuringRefresh()||i.calcID!=this._calcID||i.getFormulaValue()=="")&&(this.getComputedValue().containsKey(t)?r=this.getComputedValue().getItem(t):(r=this.computeFormula(i.getParsedFormula()),this._computedValue.add(t,r)),r!=i._formulaValue&&this._refreshedCells.containsKey(t)&&this._refreshedCells.remove(t),i._formulaValue=r),i.calcID=this._calcID,this.cell=c,l=this.ignoreValueChanged,this.ignoreValueChanged=!0,this.parentObject.setValueRowCol==undefined?this.setValueRowCol(this.getSheetID(this.grid)+1,i.getFormulaValue(),s,h):this.parentObject.setValueRowCol(this.getSheetID(this.grid)+1,i.getFormulaValue(),s,h),this.ignoreValueChanged=l,this._refreshedCells.containsKey(t)||(this._refreshedCells.add(t,0),this.refresh(t)))}catch(y){continue}this.grid=o}this._lockDependencies=v}finally{this._refreshedCells.containsKey(n)||this._refreshedCells.add(n,0);this._dependencyLevel--;this._dependencyLevel==0&&this._refreshedCells.clear()}}};this.refreshRange=function(n){for(var i,r,u,f,t=n.getTop();t<=n.getBottom();t++)for(i=n.getLeft();i<=n.getRight();i++)r=RangeInfo.getAlphaLabel(i)+t.toString(),this._dependencyLevel=0,u=CalcEngine.getSheetFamilyItem(this.grid),f=this.sheetToken+this.getSheetID(this.grid).toString()+this.sheetToken,u.tokenToParentObject!=null&&u.tokenToParentObject.contains(f)&&(this.grid=u.tokenToParentObject.getItem(f)),r=f+r,this.getComputedValue().clear(),this.refresh(r),this.getComputedValue().clear()};this.registerGridAsSheet=function(n,t,i){var r,f,u;return CalcEngine.modelToSheetID!=null,CalcEngine.modelToSheetID==null&&(CalcEngine.modelToSheetID=new HashTable),(CalcEngine.modelToSheetID.getItem(t)==null||CalcEngine.modelToSheetID.getItem(t)==undefined)&&CalcEngine.modelToSheetID.add(t,i),r=CalcEngine.getSheetFamilyItem(t),r.isSheetMember=!0,f=n.toUpperCase(),r.sheetNameToParentObject==null&&(r.sheetNameToParentObject=new HashTable),r.tokenToParentObject==null&&(r.tokenToParentObject=new HashTable),r.sheetNameToToken==null&&(r.sheetNameToToken=new HashTable),r.parentObjectToToken==null&&(r.parentObjectToToken=new HashTable),r.sheetNameToParentObject.getItem(f)!=undefined?(u=r.sheetNameToToken.getItem(f),r.tokenToParentObject.add(u,t),r.parentObjectToToken.add(t,u)):(u=this.sheetToken+CalcEngine._tokenCount.toString()+this.sheetToken,CalcEngine._tokenCount++,r.tokenToParentObject.add(u,t),r.sheetNameToToken.add(f,u),r.sheetNameToParentObject.add(f,t),r.parentObjectToToken.add(t,u),this.sortedSheetNames=null),n};this.renameSheet=function(n,t){var i=CalcEngine.getSheetFamilyItem(t),r="",u;if(n.includes("/")||n.includes("?")||n.includes("*")||n.includes("[")||n.includes("]")||n.includes(":")||n.includes("\\"))throw"The name does not contain any of following characters:  / ? *  [ ] : \\";r=t.toString();r=r.toUpperCase();currentSheetName=n.toUpperCase();u=i.sheetNameToToken.getItem(r);CalcEngine.modelToSheetID.containsKey(t)&&(CalcEngine.modelToSheetID.remove(t),CalcEngine.modelToSheetID.add(n,this._calcID));i.sheetNameToParentObject.containsKey(r)&&(i.sheetNameToParentObject.remove(r),i.sheetNameToParentObject.add(currentSheetName,n));i.tokenToParentObject.containsKey(u)&&(i.tokenToParentObject.remove(u),i.tokenToParentObject.add(u,n));i.sheetNameToToken.containsKey(r)&&(i.sheetNameToToken.remove(r),i.sheetNameToToken.add(currentSheetName,u));i.parentObjectToToken.containsKey(r)&&(i.parentObjectToToken.remove(r),i.parentObjectToToken.add(n,u))};this.removeFunction=function(n){return(this._customlibraryFunctions.getItem(n)!=undefined&&this._customlibraryFunctions.remove(n),this._libraryFunctions.getItem(n)!=null)?(this._libraryFunctions.remove(n),!0):!1};this.removeNamedRange=function(n){return(n=n.toUpperCase(),this.getNamedRanges().getItem(n)!=null)?(this.getNamedRanges().remove(n),this._populateNamedRangesNonScoped(),!0):!1};this.rowIndex=function(n){var t=0,i,r=!1;if(t<n.length&&n[t]==this.sheetToken){for(t++;t<n.length&&n[t]!=this.sheetToken;)t++;t++}while(t<n.length&&this._isLetter(n[t]))r=!0,t++;if(i=parseInt(n.substring(t)),t<n.length&&!isNaN(i))return i;if(r)return-1;throw this.formulaErrorStrings[this._bad_index];};this.splitArgsPreservingQuotedCommas=function(n){var f,r,u,t,i;if(n.toString().indexOf(this.tic)==-1)return n.toString().split(this._parseArgumentSeparator);for(f=this._saveStrings(n),n=this._saveStringsText,r=n.split(this._parseArgumentSeparator),u=[],t=0;t<r.length;t++)i=r[t],i=this._setStrings(i,f),u.push(i);return u};this.removeTics=function(n){return n.length>1&&n[0]==this.tic[0]&&n[n.length-1]==this.tic[0]&&(this._substring(n,1,n.length-2).indexOf(this.tic)==-1?n=this._substring(n,1,n.length-2):this._multiTick&&(n=this._substring(n,1,n.length-2))),n};this.updateCalcID=function(){this._calcID++;this._calcID==Number.MAX_SAFE_INTEGER&&(this._calcID=Number.MIN_SAFE_INTEGER+1)};this.updateDependencies=function(n){var s,t,c,h,u,e,o,i,f,r;if(!this._lockDependencies&&this.getUseDependencies())if(s=CalcEngine.getSheetFamilyItem(this.grid),t=this.cell,s.sheetNameToParentObject!=null&&t.indexOf(this.sheetToken)==-1&&(c=s.parentObjectToToken.getItem(this.grid),t=c+t),s.sheetNameToParentObject!=null&&n.indexOf(this.sheetToken)==-1&&(c=s.parentObjectToToken.getItem(this.grid),n=c+n),this.getDependentCells().containsKey(n)){if(u=this.getDependentCells().getItem(n),u.indexOf(t)==-1&&u.push(t),this._addToFormulaDependentCells(n),ej.isNullOrUndefined(this.parentObject)&&this.parentObject.pluginName!="ejSpreadsheet"&&this.getDependentFormulaCells().containsKey(n)&&(e=this.getDependentFormulaCells().getItem(n),e!=null))for(o=e.keys(),i=0;i<o.length;i++)f=o[i],r=this.getDependentCells().getItem(f),r==null&&(r=[],this.getDependentCells().add(f,r)),r.indexOf(t)==-1&&r.push(t)}else{if(u=[],this.getDependentCells().add(n,u),u.push(t),this.getDependentFormulaCells().containsKey(n)&&(e=this.getDependentFormulaCells().getItem(n),e!=null))for(o=e.keys(),i=0;i<o.length;i++)f=o[i],r=this.getDependentCells().getItem(f),r==null&&(r=[],this.getDependentCells().add(f,r)),r.indexOf(t)==-1&&r.push(t);if(this._addToFormulaDependentCells(n),this.getDependentCells().containsKey(t)&&(h=this.getDependentCells().getItem(t),h!=null&&h!=u))for(i=0;i<h.length;i++)f=h[i],u.push(f)}};this.updateDependenciesAndCell=function(n){var v=this.grid,y=CalcEngine.getSheetFamilyItem(this.grid),h=this._sheetToken(n),t,c,o,u,f,l,i,r,a,s,e;if(h.length>0&&(this.grid=y.tokenToParentObject.getItem(h)),this.formulaInfoTable.containsKey(n)){if(t=this.formulaInfoTable[n],t.calcID!=this._calcID){if(c=this.cell,this.cell=n,this._iterationMaxCount>0&&this._circCheckList.indexOf(this.cell)>-1?this._handleIterations(t):t.setFormulaValue(this.computeFormula(t.getParsedFormula())),this.getDependentCells().containsKey(n))for(o=this.getDependentCells().getItem(n),u=0;u<o.length;u++)f=this.FormulaInfoTable.getItem(o[u]),f!=null&&f!=undefined&&(f.calcID=Number.MIN_SAFE_INTEGER);t.calcID=this._calcID;this.cell=c}if(l=this.ignoreValueChanged,this.ignoreValueChanged=!0,i=this.rowIndex(n),r=this.colIndex(n),this.getPreserveFormula()&&(this.parentObject.setValueRowCol==undefined?this.setValueRowCol(this.getSheetID(this.grid)+1,t.getFormulaText(),i,r):this.parentObject.setValueRowCol(this.getSheetID(this.grid)+1,t.getFormulaText(),i,r)),this.ignoreValueChanged=l,this.grid=v,this._processedCells.contains(n))return;if(this._processedCells.add(n),this.parentObject.setValueRowCol==undefined?this.setValueRowCol(this.getSheetID(this.grid)+1,t.getFormulaValue(),i,r):this.parentObject.setValueRowCol(this.getSheetID(this.grid)+1,t.getFormulaValue(),i,r),this.getDependentFormulaCells().containsKey(n))for(a=this.getDependentFormulaCells().getItem(n),s=a.keys(),e=0;e<s.length;e++)this.updateDependenciesAndCell(s[e])}};this.valueChanged=function(n,t,i){var e,c,r,f,s,a;if(!this.ignoreValueChanged){e=n;this.grid=n;var h=!0,o=CalcEngine.getSheetFamilyItem(e),u=RangeInfo.getAlphaLabel(t.getColIndex())+t.getRowIndex().toString();if(o.sheetNameToParentObject!=null&&o.sheetNameToParentObject.length>0&&(c=o.parentObjectToToken.getItem(e),u=c+u),t.getValue().length>0&&t.getValue()[0]==this.getFormulaCharacter()){if(this.cell=u,f=!0,ej.isNullOrUndefined(i)||(f=i),this.getFormulaInfoTable().containsKey(u)){if(r=this.getFormulaInfoTable().getItem(u),t.getValue()!=r.getFormulaText()||r.getParsedFormula()==null){r.setFormulaText(t.getValue());this.getDependentFormulaCells().containsKey(this.cell)&&this.clearFormulaDependentCells(this.cell);try{r.setParsedFormula(this.parseFormula(t.getValue()))}catch(l){r.setFormulaValue(l);f=!1}}f&&(s=this.computeFormula(r.getParsedFormula()),h=s!=r.getFormulaValue()||this.getForceRefreshCall(),r.setFormulaValue(s));r.calcID=this._calcID}else{r=new FormulaInfo;r.setFormulaText(t.getValue());this.getDependentFormulaCells().containsKey(u)||this.getDependentFormulaCells().add(u,new HashTable);try{r.setParsedFormula(this.parseFormula(t.getValue()))}catch(l){r.setFormulaValue(l);f=!1}f&&(r.setFormulaValue(this.computeFormula(r.getParsedFormula())),this.computeIsFormula(r).toString()!="#NAME?"||this.getNamedRanges().containsKey(r.getFormulaText().split("=")[1].toUpperCase())||this.getUndefinedNamedRanges().add(r.getFormulaText().split("=")[1].toUpperCase()));r.calcID=this._calcID;this.getFormulaInfoTable().containsKey(u)?this.getFormulaInfoTable().add(u,r):this.getFormulaInfoTable().add(u,r)}this._iterationMaxCount>0&&f&&!this._inHandleIterations&&u==this.cell&&this._handleIterations(r);a=this.ignoreValueChanged;this.ignoreValueChanged=!0;f&&(this.parentObject.setValueRowCol==undefined?this.setValueRowCol(this.getSheetID(e)+1,r.getFormulaValue(),t.getRowIndex(),t.getColIndex()):this.parentObject.setValueRowCol(this.getSheetID(e)+1,r.getFormulaValue(),t.getRowIndex(),t.getColIndex()));this.ignoreValueChanged=a}else!this._inRecalculateRange&&this.getFormulaInfoTable().containsKey(u)&&(this.getFormulaInfoTable().remove(u),this.getDependentFormulaCells().containsKey(u)&&this.clearFormulaDependentCells(u));f&&h&&this.getDependentCells().containsKey(u)&&(this._dependencyLevel=0,this.getComputedValue().clear(),this.refresh(u))}};this.getActiveCell=function(){return this.cell};this.getAllowShortCircuitIFs=function(){return this._allowShortCircuitIFs};this.setAllowShortCircuitIFs=function(n){this._allowShortCircuitIFs=n};this.getAlwaysComputeDuringRefresh=function(){return this._alwaysComputeDuringRefresh};this.setAlwaysComputeDuringRefresh=function(n){this._alwaysComputeDuringRefresh=n};this.getCalculatingSuspended=function(){return this._calculatingSuspended};this.setCalculatingSuspended=function(n){this._calculatingSuspended=n};this.getCheckDanglingStack=function(){return this._checkDanglingStack};this.setCheckDanglingStack=function(n){this._checkDanglingStack=n};this.getCustomLibraryFunctions=function(){return this._customlibraryFunctions};this._computedValue=null;this.getComputedValue=function(){return this._computedValue==null&&(this._computedValue=new HashTable),this._computedValue};this.getDependentCells=function(){if(this.isSheetMember()){var n=CalcEngine.getSheetFamilyItem(this.grid);return n.sheetDependentCells==null&&(n.sheetDependentCells=new HashTable),n.sheetDependentCells}return this._dependentCells==null&&(this._dependentCells=new HashTable),this._dependentCells};this.getDependentFormulaCells=function(){if(this.isSheetMember()){var n=CalcEngine.getSheetFamilyItem(this.grid);return n.sheetDependentFormulaCells==null&&(n.sheetDependentFormulaCells=new HashTable),n.sheetDependentFormulaCells}return this._dependentFormulaCells==null&&(this._dependentFormulaCells=new HashTable),this._dependentFormulaCells};this.getEnableLookupTableCaching=function(){return this._enableLookupTableCaching};this.setEnableLookupTableCaching=function(n){this._enableLookupTableCaching=n};this.getErrorStrings=function(){return this._errorStrings==null&&(this._errorStrings=["#N/A","#VALUE!","#REF!","#DIV/0!","#NUM!","#NAME?","#NULL!"]),this._errorStrings};this.setErrorStrings=function(n){this._errorStrings=n};this.getExcelLikeComputations=function(){return this._excelLikeComputations};this.setExcelLikeComputations=function(n){this._excelLikeComputations=n};this.getForceRefreshCall=function(){return this._forceRefreshCall};this.setForceRefreshCall=function(n){this._forceRefreshCall=n};this.getFormulaCharacter=function(){return CalcEngine._formulaChar=='\0'&&(CalcEngine._formulaChar="="),CalcEngine._formulaChar};this.setFormulaCharacter=function(n){CalcEngine._formulaChar=n};this.getFormulaInfoTable=function(){if(this.isSheetMember()){var n=CalcEngine.getSheetFamilyItem(this.grid);return n.sheetFormulaInfoTable==null&&(n.sheetFormulaInfoTable=new HashTable),n.sheetFormulaInfoTable}return this._formulaInfoTable==null&&(this._formulaInfoTable=new HashTable),this._formulaInfoTable};this.getPreserveLeadingZeros=function(){return this._preserveLeadingZeros};this.setPreserveLeadingZeros=function(n){this._preserveLeadingZeros=n};this.getIterationMaxCount=function(){return this._iterationMaxCount};this.setIterationMaxCount=function(n){this._iterationMaxCount=n;this._iterationMaxCount>0&&this.setThrowCircularException(!0)};this.getIterationMaxTolerance=function(){return this._iterationMaxTolerance};this.setIterationMaxTolerance=function(n){this._iterationMaxTolerance=n};this.getIterationValues=function(){return this._iterationValues==null&&(this._iterationValues=new HashTable),this._iterationValues};this.getLibraryComputationException=function(){return this._libraryComputationException};this.getLibraryFunctions=function(){return this._libraryFunctions};this.getLockDependencies=function(){return this._lockDependencies};this.setLockDependencies=function(n){this._lockDependencies=n};this.getMaximumRecursiveCalls=function(){return this._maximumRecursiveCalls};this.setMaximumRecursiveCalls=function(n){this._maximumRecursiveCalls=n};this.getNamedRanges=function(){return this.namedRanges==null&&(this.namedRanges=new HashTable,this._namedRangesNonScoped=new HashTable),this.namedRanges};this.setNamedRanges=function(n){this.namedRanges=n;this._populateNamedRangesNonScoped()};this.getUndefinedNamedRanges=function(){var n=CalcEngine.getSheetFamilyItem(this.grid);return n!=null||n!=undefined?(this.undefinedsheetNamedRnages==null&&(this.undefinedsheetNamedRnages=new HashTable),this.undefinedsheetNamedRnages):(this.undefinednamedRange==null&&(this.undefinednamedRange=new HashTable),this.undefinednamedRange)};this.getNameRangeValues=function(){var n=CalcEngine.getSheetFamilyItem(this.grid);return n!=null||n!=undefined?(this.namedRangeValues==null&&(this.namedRangeValues=new HashTable),this.namedRangeValues):(this.rangeValues==null&&(this.rangeValues=new HashTable),this.rangeValues)};this.getNamedRangesOriginalNames=function(){var n=CalcEngine.getSheetFamilyItem(this.grid);return n!=null||n!=undefined?(this.sheetNamedRangesOriginalNames==null&&(this.sheetNamedRangesOriginalNames=new HashTable),this.sheetNamedRangesOriginalNames):(this.namedRangesOriginalNames==null&&(this.namedRangesOriginalNames=new HashTable),this.namedRangesOriginalNames)};this.getNamedRangeCellCollection=function(){var n=CalcEngine.getSheetFamilyItem(this.grid);return n!=null||n!=undefined?(this.sheetNamedRangeCellCollection==null&&(this.sheetNamedRangeCellCollection=new HashTable),this.sheetNamedRangeCellCollection):(this.namerangecellcollection==null&&(this.namerangecellcollection=new HashTable),this.namerangecellcollection)};this.getDependentNamedRangeCells=function(){var n=CalcEngine.getSheetFamilyItem(this.grid);return n!=null||n!=undefined?(this.sheetDependentNamedRangeCells==null&&(this.sheetDependentNamedRangeCells=new HashTable),this.sheetDependentNamedRangeCells):(this.dependentNamedRangeCells==null&&(this.dependentNamedRangeCells=new HashTable),this.dependentNamedRangeCells)};this.getParseArgumentSeparator=function(){this._parseArgumentSeparator=='\0'&&(this._parseArgumentSeparator=",");var n=",";return this._isParseArgumentSeparator||n==this._parseArgumentSeparator||(this._parseArgumentSeparator=n),this._parseArgumentSeparator};this.setParseArgumentSeparator=function(n){this._parseArgumentSeparator=n;this._isParseArgumentSeparator=!0};this.getParseDateTimeSeparator=function(){return this._parseDateTimeSeparator};this.setParseDateTimeSeparator=function(n){this._parseDateTimeSeparator=n};this.getParseDecimalSeparator=function(){this._parseDecimalSeparator=='\0'&&(this._parseDecimalSeparator=".");var n=".";return this._isParseDecimalSeparatorChanged||n==this._parseDecimalSeparator||(this._parseDecimalSeparator=n),this._parseDecimalSeparator};this.setParseDecimalSeparator=function(n){this._parseDecimalSeparator=n;this._isParseDecimalSeparatorChanged=!0};this.getPreserveFormula=function(){return this._preserveFormula};this.setPreserveFormula=function(n){this._preserveFormula=n};this.getReservedWordOperators=function(){return this._reservedWordOperators==null&&(this._reservedWordOperators=[" or "," and "," xor ","if "," then "," else ","not "]),this._reservedWordOperators};this.setReservedWordOperators=function(n){this._reservedWordOperators=n};this.getRethrowLibraryComputationExceptions=function(){return this._rethrowLibraryComputationExceptions};this.setRethrowLibraryComputationExceptions=function(n){this._rethrowLibraryComputationExceptions=n};this.getRethrowParseExceptions=function(){return this._rethrowExceptions};this.setRethrowParseExceptions=function(n){this._rethrowExceptions=n};this.getRowMaxCount=function(){return this._rowMaxCount};this.setRowMaxCount=function(n){this._rowMaxCount=n};this.getSortedSheetNames=function(){var n=CalcEngine.getSheetFamilyItem(this.grid);return n!=null&&n.sheetNameToToken!=null&&(this._sortedSheetNames=n.sheetNameToToken.keys(),this._sortedSheetNames.sort()),this._sortedSheetNames};this.getSupportLogicalOperators=function(){return this._supportLogicalOperators};this.setSupportLogicalOperators=function(n){this._supportLogicalOperators=n};this.getSupportRangeOperands=function(){return this._supportRangeOperands};this.setSupportRangeOperands=function(n){this._supportRangeOperands=n};this.getSupportsSheetRanges=function(){return this._supportsSheetRanges};this.setSupportsSheetRanges=function(n){this._supportsSheetRanges=n};this.getThrowCircularException=function(){return this._throwCircularException};this.setThrowCircularException=function(n){this._throwCircularException=n};this.getTreatStringsAsZero=function(){return this._treatStringsAsZero};this.setTreatStringsAsZero=function(n){this._treatStringsAsZero=n};this.getUseDatesInCalculations=function(){return this._useDatesInCalcs};this.setUseDatesInCalculations=function(n){this._useDatesInCalcs=n};this.getUseDependencies=function(){return this._useDependencies};this.setUseDependencies=function(n){this._useDependencies=n};this.getUseFormulaValues=function(){return this._useFormulaValues};this.setUseFormulaValues=function(n){this._useFormulaValues=n};this.getUseNoAmpersandQuotes=function(){return this._useNoAmpersandQuotes};this.setUseNoAmpersandQuotes=function(n){this._useNoAmpersandQuotes=n};this.getValidPrecedingChars=function(){return this._validPrecedingChar+this.getParseArgumentSeparator()};this.setValidPrecedingChars=function(n){this._validPrecedingChars=n};this.getEnableFormulaErrorValidation=function(){return this._enableFormulaErrorValidation};this.setEnableFormulaErrorValidation=function(n){this._enableFormulaErrorValidation=n};this.getWeekEndType=function(){return["","6,0","0,1","1,2","2,3","3,4","4,5","5,6","","","","0","1","2","3","4","5","6"]};this.computeSum=function(n){var e=0,t,i,u,l=this.splitArgsPreservingQuotedCommas(n),s,o,f,r,h,a,c;if(!n||n.length==0){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(s=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(s)>-1))return s;for(o=0;o<l.length;o++)if(u=l[o],u.indexOf(":")>-1){if(u.startsWith(this.tic)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[4].toString()}for(f=this.getCellsFromArgs(u),r=0;r<f.length;r++){try{if(f[r].startsWith(this.tic)&&isNaN(this._parseDouble(f[r]))){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}if(h=CalcEngine.getSheetFamilyItem(this.grid),ej.isNullOrUndefined(h.parentObjectToToken)?t=this.getValueFromArg(f[r]).split(this.tic).join(""):(a=h.parentObjectToToken.getItem(this.grid)+f[r],c=this.getFormulaInfoTable().getItem(a),t=ej.isNullOrUndefined(c)||ej.isNullOrUndefined(c._formulaText.toUpperCase().match("SUBTOTAL"))?this.getValueFromArg(f[r]).split(this.tic).join(""):"0"),this.getErrorStrings().indexOf(t)>-1){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return t}}catch(v){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return v}if(t!=""){if(i=t==this.trueValueStr?!0:t==this.falseValueStr?!1:this._parseDouble(t),this.getErrorStrings().indexOf(t)>-1){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return t}ej.isNullOrUndefined(ej.cultureObject)||ej.cultureObject.name=="en-US"||(i=ej.isNullOrUndefined(t)?NaN:ej.parseFloat(t.toString(),0,ej.cultureObject.name));isNaN(i)?t="":e=e+i}}}else{try{if(u.startsWith(this.tic)&&isNaN(this._parseDouble(u))){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}if(t=this.getValueFromArg(u).split(this.tic).join(""),this.getErrorStrings().indexOf(t)>-1){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return t}}catch(v){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return v}if(t.length>0){if(i=t==this.trueValueStr?!0:t==this.falseValueStr?!1:this._parseDouble(t),this.getErrorStrings().indexOf(t)>-1)return t;ej.isNullOrUndefined(ej.cultureObject)||ej.cultureObject.name=="en-US"||(i=ej.isNullOrUndefined(t)?NaN:ej.parseFloat(t.toString(),0,ej.cultureObject.name));isNaN(i)||(e=e+i)}}return e.toString()};this.computeDate=function(n){var t=this.splitArgsPreservingQuotedCommas(n),h=t.length,s,r,i,o;if(h!=3||n=="")return this.formulaErrorStrings[this._wrong_number_arguments];if(this.getEnableFormulaErrorValidation()&&(s=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(s)>-1))return s;for(r=0;r<h;++r)t[r]=this.getValueFromArg(t[r]);t[0]=t[0].split(this.tic).join("")=="TRUE"?"1":t[0].split(this.tic).join("")=="FALSE"?"0":t[0];t[1]=t[1].split(this.tic).join("")=="TRUE"?"1":t[1].split(this.tic).join("")=="FALSE"?"0":t[1];t[2]=t[2].split(this.tic).join("")=="TRUE"?"1":t[2].split(this.tic).join("")=="FALSE"?"0":t[2];var u=this._parseDouble(t[0]),f=this._parseDouble(t[1]),c=this._parseDouble(t[2]),e=0;if(!isNaN(u)&&!isNaN(f)&&!isNaN(c)){if(u<0){if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}while(f>12)f-=12,u++;e=this._getSerialDateFromDate(u,f,c)}if(e==0){if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}return this._excelLikeComputations&&(i=this._fromOADate(e),i.toString()!="Invalid Date")?(o=ej.browserInfo(),o.name!="msie"&&o.name!="mozilla"&&!ej.isNullOrUndefined(o.name)&&this._isInteriorFunction?i.toLocaleDateString():_calculateDate(i.getMonth()+1)+"/"+_calculateDate(i.getDate())+"/"+i.getFullYear()):e.toString()};this.computeDatevalue=function(n){var f=this.splitArgsPreservingQuotedCommas(n),s=f.length,h,i,e,t,u;if(s!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(h=this.formulaErrorStringCheck(n,FormulaArgumentType.Date),this.getErrorStrings().indexOf(h)>-1))return h;if(this._isCellReference(n)){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_Math_argument];return this.getErrorStrings()[1].toString()}i=new Date(Date.parse(f[0]));e=this._parseDouble(f[0]);try{if(!isNaN(e)||i.toString()=="Invalid Date")if(e>0)i=this._fromOADate(e);else{var r=n.split(this.getParseDateTimeSeparator()),s=r.length,o="";for(t=0;t<s;++t)r[t]=this.computedValue(r[t]),o+=t<s-1?r[t]+this.getParseDateTimeSeparator():r[t];o=o.split(this.tic).join(this._string_empty);i=new Date(Date.parse(o))}}catch(c){return this.formulaErrorStrings[this.invalid_arguments]}if(f[0].indexOf("2001")==-1&&i.getFullYear()==2001&&i.setFullYear(new Date(Date.now()).getFullYear()),u=this._toOADate(i),isNaN(u)){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_Math_argument];return this.getErrorStrings()[1].toString()}return this.treat1900AsLeapYear&&u>59&&(u+=1),Math.round(u).toString()};this.computeDay=function(n){var e=this.splitArgsPreservingQuotedCommas(n),i,t;if(e.length>1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Date),this.getErrorStrings().indexOf(i)>-1))return i;var r=1,f=this.getValueFromArg(n).split(this.tic).join(""),u=this._parseDouble(f);if(isNaN(u))if(t=new Date(Date.parse(f)),t.toString()!="Invalid Date")r=t.getDate();else{if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_Math_argument];return this.getErrorStrings()[1].toString()}else{if(u<1){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_Math_argument];return this.getErrorStrings()[4].toString()}t=this._getDateFromSerialDate(u);r=t.getDate()}return r.toString()};this.computeDays=function(n){var l=n,r=this.splitArgsPreservingQuotedCommas(n),u,t,i,o,s,f,e,h,c;if(r.length!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Date),this.getErrorStrings().indexOf(u)>-1)?u:(t=this.getValueFromArg(r[0]),t=t.split(this.tic).join("").toUpperCase()=="TRUE"?"1":t.split(this.tic).join("").toUpperCase()=="FALSE"?"0":t,i=this.getValueFromArg(r[1]),i=i.split(this.tic).join("").toUpperCase()=="TRUE"?"1":i.split(this.tic).join("").toUpperCase()=="FALSE"?"0":i,t=t==""||t==null?new Date(Date.parse("1899-12-31")).toDateString():t,i=i==""||i==null?new Date(Date.parse("1899-12-31")).toDateString():i,f=this._parseDouble(t.split(this.tic).join("")),e=this._parseDouble(i.split(this.tic).join("")),o=isNaN(f)?new Date(Date.parse(t)):this._getDateFromSerialDate(f),s=isNaN(e)?new Date(Date.parse(i)):this._getDateFromSerialDate(e),h=o.getTime()-s.getTime(),c=Math.ceil(h/864e5),c.toString())};this.computeDays360=function(n){var r=this.splitArgsPreservingQuotedCommas(n),o=r.length,s,l,a,y;if(o!=2&&o!=3||n=="")return this.formulaErrorStrings[this._wrong_number_arguments];if(this.getEnableFormulaErrorValidation()&&(s=this.formulaErrorStringCheck(n,FormulaArgumentType.Date),this.getErrorStrings().indexOf(s)>-1))return s;var v=!1,u=0,i=new Date(Date.parse(this.getValueFromArg(r[0]).split(this.tic).join(""))),t=new Date(Date.parse(this.getValueFromArg(r[1]).split(this.tic).join(""))),h=this.getValueFromArg(r[0]),c=this.getValueFromArg(r[1]),f=h.split(this.tic).join("").toUpperCase()=="TRUE"?"1":h.split(this.tic).join("").toUpperCase()=="FALSE"?"0":h,e=c.split(this.tic).join("").toUpperCase()=="TRUE"?"1":c.split(this.tic).join("").toUpperCase()=="FALSE"?"0":c;if(f=this._parseDouble(f.split(this.tic).join("")),e=this._parseDouble(e.split(this.tic).join("")),isNaN(f)&&isNaN(this._isDate(i))||isNaN(e)&&isNaN(this._isDate(t))){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_Math_argument];return this.getErrorStrings()[1].toString()}return i=f>0?this._getDateFromSerialDate(f):i,t=e>0?this._getDateFromSerialDate(e):t,l=!1,a=this.falseValueStr,o==3&&(a=this.getValueFromArg(r[2])),v=a==this.trueValueStr?!0:!1,i.getDate()==31&&(i=new Date(i.setDate(i.getDate()-1))),t.getDate()==31&&!v&&i.getDate()<30?t=new Date(t.setDate(t.getDate()+1)):t.getDate()==31&&(t=new Date(t.setDate(t.getDate()-1))),t<i&&(l=!0,y=i,i=t,t=y),u=t.getDate()-i.getDate(),u+=30*(t.getMonth()-i.getMonth()),u+=360*(t.getFullYear()-i.getFullYear()),l&&(u=-u),u.toString()};this.computeEDate=function(n){var v=n,i=this.splitArgsPreservingQuotedCommas(n),l=i.length,r,e;if(l!=2){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(i[0]==""||i[1]==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[0].toString()}if(this.getEnableFormulaErrorValidation()&&((r=this.formulaErrorStringCheck(i[0],FormulaArgumentType.Date),this.getErrorStrings().indexOf(r)>-1)||(r=this.formulaErrorStringCheck(i[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1)))return r;var u=this.getValueFromArg(i[0]).split(this.tic).join(""),y=parseInt(u),t=isNaN(u)?new Date(Date.parse(u)):this._fromOADate(parseInt(u)),h=this.getValueFromArg(i[1]).split(this.tic).join(""),c=parseInt(h);if(t.toString()=="Invalid Date"&&isNaN(c))try{var o=new Date(Date.now()),s=this._parseDouble(u),f=parseInt(h);if(f<0&&f<s){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[4].toString()}if(s!=0)e=this.dateTime1900,e=this._fromOADate(s),t=new Date(e.setMonth(e.getMonth()+f));else return t=new Date(o.setMonth(o.getMonth()+f)),this.computeDays(o.toLocaleDateString()+this.getParseArgumentSeparator()+t.toLocaleDateString())}catch(a){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula]+a;return this.getErrorStrings()[1].toString()}return t=new Date(t.setMonth(t.getMonth()+c)),this.getExcelLikeComputations()?t.toLocaleDateString():parseInt(this._toOADate(t)).toString()};this.computeEOMonth=function(n){var l=n,i=this.splitArgsPreservingQuotedCommas(n),h=i.length,r,c,e,o,s;if(h!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(i[0]==""||i[1]==""){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[0].toString();return this.getErrorStrings()[0].toString()}if(this.getEnableFormulaErrorValidation()&&((r=this.formulaErrorStringCheck(i[0],FormulaArgumentType.Date),this.getErrorStrings().indexOf(r)>-1)||(r=this.formulaErrorStringCheck(i[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1)))return r;var f=this.getValueFromArg(i[0]).split(this.tic).join(""),t=new Date(f),u=this._parseDouble(this.getValueFromArg(i[1]).split(this.TIC).join(""));if(t.toString()=="Invalid Date"){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[1].toString()}if(isNaN(u))try{if(c=new Date(Date.now()),e=parseInt(f),u=this._parseDouble(this.getValueFromArg(i[1]).split(this.tic).join("")),u<0&&e<=0&&u<e){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[4].toString()}}catch(a){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[1].toString()}return o=parseInt(f),!isNaN(o)&&t.getFullYear()>9999&&(t=this._fromOADate(o)),t=new Date(t.setMonth(t.getMonth()+u)),s=new Date(t.getFullYear(),t.getMonth()+1,1,-1).getDate(),t=new Date(t.getFullYear(),t.getMonth(),s),this.getExcelLikeComputations()?t.toLocaleDateString():this._getSerialDateTimeFromDate(t).toString()};this.computeHour=function(n){var f=this.splitArgsPreservingQuotedCommas(n),t,i,r,u;if(f.length!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(t=new Date(Date.now()),this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Date),this.getErrorStrings().indexOf(i)>-1))return i;if(n=this.getValueFromArg(n),n=n.split(this.tic).join(""),n=n=="TRUE"?"1":n=="FALSE"?"0":n,t=new Date(Date.parse(n)),r=parseInt(n),r<0){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[4].toString()}if(t.toString()=="Invalid Date"&&(u=new Date(Date.now()).toLocaleDateString()+" "+n,t=new Date(Date.parse(u))),t.toString()=="Invalid Date"&&(t=this._fromOADate(n)),t.toString()=="Invalid Date"){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[1].toString()}return t.getHours().toString()};this.computeISOWeeknum=function(n){var i=this.splitArgsPreservingQuotedCommas(n),h=i.length,u,r,t,e,o,s;if(h!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Date),this.getErrorStrings().indexOf(u)>-1))return u;if(r=this.getValueFromArg(i[0]),r[0]==this.tic&&this._isTextEmpty(r.split(this.tic).join(""))){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[1].toString()}this._isTextEmpty(r)&&(n=this.computeDatevalue(new Date(1990,12,28).toLocaleDateString()));(n.indexOf(":")>-1||!this._isCellReference(this.getValueFromArg(i[0]))&&this.getValueFromArg(this.DateFormatter(i[0])).indexOf(":")>-1)&&(n="0");t=this.getValueFromArg(i[0]).split(this.tic).join("");t=t.split(this.tic).join("").toUpperCase()=="TRUE"?"1":t.split(this.tic).join("").toUpperCase()=="FALSE"?"0":t;var l=new Date(t),c=this.DateFormatter(t),f=new Date(Date.parse(c)),a=this.dateTime1900;if(f.toString()=="Invalid Date")try{e=parseInt(t);f=this._fromOADate(e);t=f.toLocaleDateString()}catch(v){if(this.getRethrowLibraryComputationExceptions())throw"Bad Cell reference";return this.getErrorStrings()[1].toString()}return o=n+this.getParseArgumentSeparator()+"21",s=parseInt(this.computeWeeknum(o))-1,s.toString()};this.DateFormatter=function(n){var r=new Date(n),t=r.getDate(),i=r.getMonth()+1,u=r.getFullYear(),n;return t<10&&(t=+t),i<10&&(i=i),i+"/"+t+"/"+u};this.computeMinute=function(n){var u=this.splitArgsPreservingQuotedCommas(n),i,t,r;if(u.length>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(i)>-1))return i;if(t=new Date(Date.now()),n=this.getValueFromArg(n),n=n.split(this.tic).join(""),n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,t=new Date(Date.parse(n)),n<0){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[4].toString()}if(t.toString()=="Invalid Date"&&(r=new Date(Date.now()).toLocaleDateString()+" "+n,t=new Date(Date.parse(r))),t.toString()=="Invalid Date"&&(t=this._fromOADate(n)),t.toString()=="Invalid Date"){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[1].toString()}return t.getMinutes().toString()};this.computeMonth=function(n){var r=1,o=this.splitArgsPreservingQuotedCommas(n),e,i,t,u,f;if(o.length!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Date),this.getErrorStrings().indexOf(e)>-1))return e;if(i=this.getValueFromArg(n),i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,t=new Date(Date.parse(i.split(this.tic).join(""))),u=this._parseDouble(i),!isNaN(u)&&t.getFullYear()>9999&&(t=this._fromOADate(u)),i<0){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[4].toString()}if(t.toString()=="Invalid Date"&&(t=this._fromOADate(u)),t.toString()=="Invalid Date"){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[1].toString()}if(t.toString()!="Invalid Date")r=t.getMonth()+1;else{if(f=this._parseDouble(this.getValueFromArg(n)),isNaN(f))return this.formulaErrorStrings[this._invalid_arguments];f<1?r=1:(t=this._getDateFromSerialDate(f),r=t.getMonth()+1)}return r.toString()};this.computeNetworkDays=function(n){var h=this.splitArgsPreservingQuotedCommas(n),y=h.length,c,f,t,e=[],p,o,s,u,k,l,w,a,rt;if(y!=2&&y!=3||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(p=this.formulaErrorStringCheck(n,FormulaArgumentType.Date),this.getErrorStrings().indexOf(p)>-1))return p;if(o=this.getValueFromArg(h[0]).split(this.tic).join(""),s=this.getValueFromArg(h[1]).split(this.tic).join(""),o==""||s==""){if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[0].toString();return this.getErrorStrings()[0].toString()}var i=new Date(Date.parse(o)),r=new Date(Date.parse(s)),u=this._parseDouble(o);if(!isNaN(u)&&i.getFullYear()>9999&&(i=this._fromOADate(u)),i.toString()=="Invalid Date"&&(i=this._fromOADate(u)),i.toString()=="Invalid Date"){if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[1].toString();return this.getErrorStrings()[1].toString()}if(u=this._parseDouble(s),!isNaN(u)&&r.getFullYear()>9999&&(r=this._fromOADate(u)),r.toString()=="Invalid Date"&&(r=this._fromOADate(u)),r.toString()=="Invalid Date"){if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[1].toString();return this.getErrorStrings()[1].toString()}if(y==3)if(c=h[2],c.indexOf(":")>-1)for(k=this.getCellsFromArgs(c),l=0;l<k.length;l++){try{f=this.getValueFromArg(l).split(this.tic).join("")}catch(d){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return d}f!=""&&(t=new Date(Date.parse(f)),t.toString()!="Invalid Date"&&e.indexOf(t)==-1&&t>i&&t<r&&t.getDay()!=0&&t.getDay()!=6&&e.push(t))}else for(w=this.splitArgsPreservingQuotedCommas(c.split(this.tic).join("")),a=0;a<w.length;a++){try{f=this.getValueFromArg(w[a])}catch(d){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return d}f.length>0&&(t=new Date(Date.parse(f)),t.toString()!="Invalid Date"&&e.indexOf(t)==-1&&t>i&&t<r&&t.getDay()!=0&&t.getDay()!=6&&e.push(t))}var b=0,v=parseInt(this.computeDatevalue(s))-parseInt(this.computeDatevalue(o)),g=i.getDay(),nt=r.getDay(),tt=g-1,it=7-nt;return v=v+tt+it,rt=parseInt(v/7)*2,b=v-tt-it-rt+1-e.length,(nt==6||g==0)&&b--,b.toString()};this.computeNetworkDaysOintl=function(n){var i=this.splitArgsPreservingQuotedCommas(n),u=i.length,r,f,t,h=[],c,e,l,a,o,v,s,b,w,y,tt;if(u!=2&&u>4||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()){for(c=0;c<u;c++)if(c!=2&&(e=this.formulaErrorStringCheck(i[c],FormulaArgumentType.Date),this.getErrorStrings().indexOf(e)>-1))return e;if(u>=3&&(e=this.formulaErrorStringCheck(i[2],FormulaArgumentType.Text),this.getErrorStrings().indexOf(e)>-1))return e}if(l=this.getValueFromArg(i[0]).split(this.tic).join(""),a=this.getValueFromArg(i[1]).split(this.tic).join(""),l==""||a==""){if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[0].toString();return this.getErrorStrings()[0].toString()}if(o=new Date(Date.parse(this._fromOADate(l))),v=new Date(Date.parse(this._fromOADate(a))),o.toString()=="Invalid Date"){if(s=this._parseDouble(l),isNaN(s)){if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[1].toString();return this.getErrorStrings()[1].toString()}o=this.fromOADate(s)}if(v.toString()=="Invalid Date"){if(s=this._parseDouble(a),isNaN(s)){if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[1].toString();return this.getErrorStrings()[1].toString()}v=this._fromOADate(s)}if(u==4)if(r=i[3],r=this.adjustRangeArg(r),r.indexOf(":")>-1||r.indexOf(",")>-1)for(b=this.getCellsFromArgs(r),w=0;w<b.length;w++){try{f=this.getValueFromArg(b[w]).toString().split(this.tic).join("")}catch(d){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return d}f!=""&&(t=new Date(Date.parse(f)),t.toString()!="Invalid Date"&&h.indexOf(t)==-1&&t>o&&t>v&&t.getDay()!=0&&t.getDay()!=6&&h.push(t))}else{try{f=this.getValueFromArg(r).split(this.tic).join("")}catch(d){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return d}f.length>0&&(t=new Date(Date.parse(f)),t.toString()!="Invalid Date"&&h.indexOf(t)==-1&&t>o&&t<v&&t.getDay()!=0&&t.getDay()!=6&&h.push(t))}if(y=parseInt(i[2]),u>2&&!isNaN(y)){if(isNaN(i[2])){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[4].toString()}y=parseInt(i[2])}else y=1;for(var it=this._splitArguments(this.getWeekEndType()[y].toString(),","),g=parseInt(this.computeDatevalue(a))-parseInt(this.computeDatevalue(l))+1,k=g,nt=1,p=o;nt<g;)tt=p.getDay(),it.indexOf(tt.toString())>=0?k--:h.indexOf(p)>=0&&k--,p=new Date(p.setDate(p.getDate()+1)),nt++;return k.toString()};this.computeNow=function(n){var r=this.splitArgsPreservingQuotedCommas(n),u=r.length,t,i;if(n!=""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return(t=new Date(Date.now()),this.getExcelLikeComputations())?(i=ej.browserInfo(),i.name!="msie"&&i.name!="mozilla"&&!ej.isNullOrUndefined(i.name)&&this._isInteriorFunction?t.toLocaleDateString():t.getFullYear()+"/"+_calculateDate(t.getMonth()+1)+"/"+_calculateDate(t.getDate())):this._toOADate(t).toString()};this.computeSecond=function(n){var u=this.splitArgsPreservingQuotedCommas(n),f=u.length,i,t,r;if(f!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Date),this.getErrorStrings().indexOf(i)>-1))return i;if(t=new Date(Date.now()),n=this.getValueFromArg(n),n=n.split(this.tic).join(""),n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,t=new Date(Date.parse(n)),n<0){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[4].toString()}if(t.toString()=="Invalid Date"&&(r=new Date(Date.now()).toLocaleDateString()+" "+n,t=new Date(Date.parse(r))),t.toString()=="Invalid Date"&&(t=this._fromOADate(n)),t.toString()=="Invalid Date"){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[1].toString()}return t.getSeconds().toString()};this.computeTime=function(n){var t=this.splitArgsPreservingQuotedCommas(n),s=t.length,r,o;if(s!=3||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;t[0]=this.getValueFromArg(t[0]);t[1]=this.getValueFromArg(t[1]);t[2]=this.getValueFromArg(t[2]);t[0]=t[0]==""?"0":t[0];t[0]=t[0].split(this.tic).join("")=="TRUE"?"1":t[0].split(this.tic).join("")=="FALSE"?"0":t[0];t[1]=t[1]==""?"0":t[1];t[1]=t[1].split(this.tic).join("")=="TRUE"?"1":t[1].split(this.tic).join("")=="FALSE"?"0":t[1];t[2]=t[2]==""?"0":t[2];t[2]=t[2].split(this.tic).join("")=="TRUE"?"1":t[2].split(this.tic).join("")=="FALSE"?"0":t[2];var u=this._parseDouble(t[0]),f=this._parseDouble(t[1]),e=this._parseDouble(t[2]),i=0;if(isNaN(u)||isNaN(f)||isNaN(e)){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[1].toString()}if(i=(3600*u+60*f+e)/86400,o=new Date(Date.now()).toLocaleDateString()+" "+i,dt=new Date(Date.parse(o)),i<0){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[4].toString()}return i.toString()};this.computeTimevalue=function(n){var u=this.splitArgsPreservingQuotedCommas(n),o=u.length,r,f,t,i,e;if(o!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(r)>-1))return r;if(t=this.getValueFromArg(u[0]).split(this.tic).join(""),t=t.split(this.tic).join(""),t.indexOf(":")>-1&&(f=new Date,t=f.toLocaleDateString()+" "+t),i=new Date(Date.parse(t)),i.toString()=="Invalid Date"&&(n=new Date(Date.now()).toLocaleDateString()+" "+n,i=new Date(Date.parse(n))),i.toString()=="Invalid Date"){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[1].toString()}return e=(3600*i.getHours()+60*i.getMinutes()+i.getSeconds())/86400,e.toString()};this.computeToday=function(n){var t,i;if(n!=""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return t=new Date(Date.now()),this.getExcelLikeComputations()?(i=ej.browserInfo(),i.name!="msie"&&i.name!="mozilla"&&!ej.isNullOrUndefined(i.name)&&this._isInteriorFunction?t.toLocaleDateString():t.getFullYear()+"/"+_calculateDate(t.getMonth()+1)+"/"+_calculateDate(t.getDate())):this._toOADate(t).toString()};this.computeWeekday=function(n){var t=this.splitArgsPreservingQuotedCommas(n),e=t.length,o,s,i,r,f,h,u;if(e!=1&&e!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(o=this.formulaErrorStringCheck(t[0],FormulaArgumentType.Date),this.getErrorStrings().indexOf(o)>-1)?o:t.length==2&&this.getEnableFormulaErrorValidation()&&(s=this.formulaErrorStringCheck(t[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(s)>-1)?s:(i=this.getValueFromArg(t[0]),i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,r=new Date(Date.parse(i.split(this.tic).join(""))),f=this._parseDouble(i),!isNaN(f)&&r.getFullYear()>9999&&(r=this._fromOADate(f)),t[1]=e==2?t[1]:"1",r.toString()=="Invalid Date"&&(r=this._fromOADate(f)),h=parseInt(this.getValueFromArg(t[1])),u=r.getDay(),h==1?u+=1:(u==0&&(u=7),h==3&&(u-=1)),u.toString())};this.computeWeeknum=function(n){var t=this.splitArgsPreservingQuotedCommas(n),e=t.length,u,r,h,i;if(e>3||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&((u=this.formulaErrorStringCheck(t[0],FormulaArgumentType.Date),this.getErrorStrings().indexOf(u)>-1)||e==2&&(u=this.formulaErrorStringCheck(t[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1)))return u;if(t[0]==""){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[0].toString();return this.getErrorStrings()[0].toString()}this._fromOADate(this.DateFormatter(this.getValueFromArg(t[0]).split(this.tic).join("")))=="Invalid Date"?(r=this.getValueFromArg(t[0]).split(this.tic).join(""),r=this._fromOADate(r)):r=this.DateFormatter(this.getValueFromArg(t[0]).split(this.tic).join(""));var p=parseInt(r),w=new Date(r),o=new Date(Date.parse(r));if(o.toString()=="Invalid Date"){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[4].toString()}if(h=["","0","1","","","","","","","","","1","2","3","4","5","6","0","","","","1"],e!=2?i=1:(i=this.getValueFromArg(t[1]),i==""&&(i=1)),Boolean(i=="TRUE"||i=="FALSE")){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[1].toString();return this.getErrorStrings()[1].toString()}!isNaN(i);var f=parseInt(h[i]),c=new Date(o.getFullYear(),0,1),s=c.getDate(),l=s<f?f-s+1:s-f,a=this._fromOADate(this._toOADate(c)-l),v=this._toOADate(o)-this._toOADate(a),y=Math.floor(v/7)+1+f;return y.toString()};this.computeWorkDay=function(n){var t=this.splitArgsPreservingQuotedCommas(n),f=t.length,r,i,o,s,u,e;if(f!=2&&f>3||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()){for(r=0;r<f;r++)if(r!=1&&(i=this.formulaErrorStringCheck(t[r],FormulaArgumentType.Date),this.getErrorStrings().indexOf(i)>-1))return i;if(i=this.formulaErrorStringCheck(t[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(i)>-1)return i}if(t[0]==""||t[1]==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[0].toString()}if(o="",s=0,o=f==3?t[0]+this.getParseArgumentSeparator()+t[1]+this.getParseArgumentSeparator()+"1"+this.getParseArgumentSeparator()+t[2]:t[0]+this.getParseArgumentSeparator()+t[1]+this.getParseArgumentSeparator()+"1",u=this.computeWorkDayOintl(o),e=new Date(Date.parse(this._fromOADate(u).toString())),isNaN(Date.parse(u))&&parseInt(u)&&(e=this._fromOADate(u)),e.toString()=="Invalid Date"){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this.bad_formula];return this.getErrorStrings()[1].toString()}return this._toOADate(e).toString()};this.computeWorkDayOintl=function(n){var t=this.splitArgsPreservingQuotedCommas(n),o=t.length,u,a,b,v,i,l,p;if(o>4||o<2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(t[0]==""||t[1]==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[0].toString()}if(this.getEnableFormulaErrorValidation()&&((u=this.formulaErrorStringCheck(t[0],FormulaArgumentType.Date),this.getErrorStrings().indexOf(u)>-1)||(u=this.formulaErrorStringCheck(t[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1)||o>=3&&(u=this.formulaErrorStringCheck(t[2],FormulaArgumentType.Text),this.getErrorStrings().indexOf(u)>-1)||o==4&&(u=this.formulaErrorStringCheck(t[3],FormulaArgumentType.Date),this.getErrorStrings().indexOf(u)>-1)))return u;var e,f,s=[],c=this.getValueFromArg(t[0]).split(this.tic).join(""),h=new Date(Date.parse(c)),w=this.getValueFromArg(t[1]).split(this.tic).join(""),d;if(!isNaN(Date.parse(c))&&parseInt(c)&&(h=this._fromOADate(c)),isNaN(w)){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this.bad_formula];return this.getErrorStrings()[1].toString()}if(d=this._toOADate(h)+w,c.split(this.tic).join("")==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this.bad_formula];return this.getErrorStrings()[0].toString()}if(h.toString()=="Invalid Date"){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this.bad_formula];return this.getErrorStrings()[1].toString()}if(a=parseInt(w.toString()),o==4)if(e=t[3],e=this.adjustRangeArg(e),e.indexOf(":")>-1||e.indexOf(",")>-1)for(b=this.getCellsFromArgs(e),v=0;v<b.length;v++){try{f=this.getValueFromArg(b[v]).split(this.tic).join("")}catch(g){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this.bad_formula];return this.getErrorStrings()[4].toString()}f!=""&&(i=new Date(Date.parse(f)),i.toString()!="Invalid Date"&&s.indexOf(i)==-1&&i>h&&s.push(i.toString()))}else{try{f=this.getValueFromArg(e).split(this.tic).join("")}catch(nt){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this.bad_formula];return this.getErrorStrings()[4].toString()}f!=""&&(i=new Date(Date.parse(f)),isNaN(Date.parse(f))&&parseInt(f)&&(i=this._fromOADate(f)),i.toString()!="Invalid Date"&&s.indexOf(i)==-1&&i>h&&s.push(i))}if(l=parseInt(t[2]),o>2&&!isNaN(l)||(l=1),l==0){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this.bad_formula];return this.getErrorStrings()[4].toString()}var k=this._splitArguments(_weekEndType[l].toString(),","),y=0,r=h;if(a<0)while(y>a)r=new Date(r.setDate(r.getDate()-1)),p=r.getDay(),k.indexOf(p.toString())<0&&s.indexOf(r)<0&&y--;else while(y<a)r=new Date(r.setDate(r.getDate()+1)),p=r.getDay(),k.indexOf(p.toString())<0&&s.indexOf(r.toString())<0&&y++;return this._toOADate(r).toString()};this.computeYear=function(n){var u=this.splitArgsPreservingQuotedCommas(n),s=u.length,f,t,o;if(s!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(n,FormulaArgumentType.Date),this.getErrorStrings().indexOf(f)>-1))return f;t=this.getValueFromArg(u[0]).split(this.tic).join("");t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t;var i=parseInt(t),r=new Date(Date.parse(i)),e=ej.browserInfo();if(isNaN(Date.parse(i))&&(e.name=="msie"||e.name=="mozilla"||ej.isNullOrUndefined(e.name))&&(o=u[0].split(this.tic),r=new Date(o[1])),!isNaN(i)&&r.getFullYear()>9999&&(r=this._fromOADate(t)),i==0)return"1900";if(i<0){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this.bad_formula];return this.getErrorStrings()[4].toString()}if(t.toString().split(this.tic).join("")==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this.bad_formula];return this.getErrorStrings()[0].toString()}if(r.toString()=="Invalid Date"){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this.bad_formula];return this.getErrorStrings()[1].toString()}return r.getFullYear().toString()};this.computeChar=function(n){var u=this.splitArgsPreservingQuotedCommas(n),f=u.length,r,t,i;if(f!=1||n==""){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;if(t=this.getValueFromArg(u[0]).split(this.tic).join(""),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,i=this._parseDouble(t),!isNaN(i)&&i>0&&i<256)return String.fromCharCode(t);if(this.getRethrowLibraryComputationExceptions())throw this.getLibraryComputationException();return this._errorStrings[1].toString()};this.computeCode=function(n){var i=this.splitArgsPreservingQuotedCommas(n),e=i.length,r,t,u,f;if(e!=1||n==""){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(r)>-1))return r;if(t=this.getValueFromArg(i[0]).split(this.tic).join(""),u=parseInt(t),!isNaN(u))return t.charCodeAt(0).toString();if(t==null||t==""){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return i[0].length>4?this.getErrorStrings()[5].toString():this.getErrorStrings()[1].toString()}return f=t,f.charCodeAt(0).toString()};this.computeUniCode=function(n){var i=this.splitArgsPreservingQuotedCommas(n),f=i.length,r,t,e,u;if(f!=1||n==""){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(r)>-1))return r;if(t=this.getValueFromArg(i[0]),this._isCellReference(i[0])){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[5].toString()}if(e=this._parseDouble(t),t=="invalid expression"||t==null||t==""){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}return u=t.split(this.tic).join("").charCodeAt(0),u.toString()};this.computeUpper=function(n){var i=this.splitArgsPreservingQuotedCommas(n),t;if(i.length>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(t=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(t)>-1)?t:this._stripTics0(this.getValueFromArg(n)).toUpperCase()};this.computeLower=function(n){var i=this.splitArgsPreservingQuotedCommas(n),t;if(i.length>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(t=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(t)>-1)?t:this._stripTics0(this.getValueFromArg(n)).toLowerCase()};this.computeLen=function(n){var f=this.splitArgsPreservingQuotedCommas(n),l=f.length,o,s,h,r,c,u;if((l!=1||n=="")&&!this._isArrayFormula){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}var t=f[0],i="",e=0;if(this._isArrayFormula){if(o=this._computeArrayInteriorFunction(n,"LEN",this.computeFunctionLevel),o!=this._string_empty)return o;t.indexOf(":")>-1&&(t=this.getCellsFromArgs(t)[0])}if(this.getEnableFormulaErrorValidation()&&(s=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(s)>-1))return s;if(t.indexOf(":")>-1&&t.indexOf(":")>2)for(h=this.getCellsFromArgs(t),r=0;r<h.length;r++){try{i=this.getValueFromArg(h[r].split(this.tic).join(this._string_empty))}catch(a){return a}i!=this._string_empty&&(u=i[0]==this.tic&&i[i.length-1]==this.tic,e+=u?i.length-2:i.length)}else t=this._stripTics0(this.getValueFromArg(f[0])),isNaN(parseInt(t))&&(c=new Date(Date.parse(t)),c!="Invalid Date"&&(t=this.DateFormatter(t))),u=t[0]==this.tic&&t[t.length-1]==this.tic,e=u?t.length-2:t.length;return e.toString()};this.computeMid=function(n){var i=this.splitArgsPreservingQuotedCommas(n),s=i.length,r,t,u;if(s!=3||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&((r=this.formulaErrorStringCheck(i[0],FormulaArgumentType.Text),this.getErrorStrings().indexOf(r)>-1)||(r=this.formulaErrorStringCheck(i[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1)||(r=this.formulaErrorStringCheck(i[2],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1)))return r;t=i[0];t=this.getValueFromArg(t);var f=t[0]==this.tic&&t[t.length-1]==this.tic,e=this.getValueFromArg(i[1]),o=this._parseDouble(i[2]);if(e=this.getValueFromArg(i[1]),u=Number(e)+Number(f?0:-1),e==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}return i[1].indexOf("#VALUE!")>-1?"#VALUE!":Number(u)>t.length?"":(t=u+o>t.length?t.substring(u):this._substring(t,u,o),f&&t[0]!=this.tic&&(t=this.tic+t),f&&t&&(t=t+this.tic),this._stripTics0(t))};this.computeLeft=function(n){var u=this.splitArgsPreservingQuotedCommas(n),f=u.length,e,t,o,i,r;if(f!=1&&f!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.TextWithNumber),this.getErrorStrings().indexOf(e)>-1))return e;if(t=this._stripTics0(this.getValueFromArg(u[0])),!this._isCellReference(u[0])&&t.indexOf(this.tic)>-1){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[5].toString()}return(o=t[0]==this.tic&&t[t.length-1]==this.tic,i=f==2?u[1]:"1",i=this._stripTics0(i),i=this.computedValue(i),i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,r=Number(i)+Number(o?1:0),r=t.length>=r?r:t.length,r=t.length>=r?r:t.length,r<0)?this.getErrorStrings()[1].toString():(r==0?t="":(t=t.substring(0,r),o&&t[t.length-1]!=this.tic&&(t=t+this.tic)),this.getUseNoAmpersandQuotes()&&t.length>1&&t[0]==this.tic[0]&&t[t.length-1]==this.tic[0]&&(t=this._substring(t,1,t.length-2)),t)};this.computeRight=function(n){var r=this.splitArgsPreservingQuotedCommas(n),u=r.length,f,e,i,t,o;if(u!=1&&u!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(n,FormulaArgumentType.TextWithNumber),this.getErrorStrings().indexOf(f)>-1))return f;if(s1=this._stripTics0(this.getValueFromArg(r[0])),!this._isCellReference(r[0])&&r[0].indexOf(this.tic)==-1){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[5].toString()}return(e=s1[0]==this.tic&&s1[s1.length-1]==this.tic,i=u==2?this.getValueFromArg(r[1]):"1",i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,t=parseInt(this._stripTics0(i)),isNaN(t)||t<0||i.indexOf("#VALUE!")>-1)?"#VALUE!":t==0?"":(t=Number(t)+Number(e?1:0),o=s1.length>=t?s1.length-t:0,s1=s1.substring(o),e&&s1[0]!=this.tic&&(s1=this.tic+s1),s1)};this.computeReplace=function(n){var r=this.splitArgsPreservingQuotedCommas(n),c=r.length,s,h,i,o;if(c!=4||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()){if(s=this.formulaErrorStringCheck(r[0],FormulaArgumentType.Text),this.getErrorStrings().indexOf(s)>-1)return s;if(h=this.formulaErrorStringCheck(r[3],FormulaArgumentType.Text),this.getErrorStrings().indexOf(h)>-1)return h}var u=this._stripTics0(this.getValueFromArg(r[0])),f=this._stripTics0(this.getValueFromArg(r[3])),e="",t=this.getValueFromArg(r[1]);if(t=t=="TRUE"?"1":t=="FALSE"?"0":t,t=this._parseDouble(t),i=this.getValueFromArg(r[2]),i=i=="TRUE"?"1":i=="FALSE"?"0":i,i=this._parseDouble(i),!this._isCellReference(r[0])&&r[0][0]!=this.tic||!this._isCellReference(r[3])&&r[3][0]!=this.tic){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[5].toString()}if(t==0&&i==0)if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[1].toString();if(isNaN(t)||isNaN(i))if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[1].toString();else try{if(o="",u==null||u=="")return f;u.length<=t&&t>i?e=u+f:u.length<=t?(o=u,e=u.split(o).join(f)):i==0?e=f+u:(o=this._substring(u,t-1,i),e=u.split(o).join(f))}catch(l){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[1].toString()}return e};this.computeExact=function(n){var r=this.splitArgsPreservingQuotedCommas(n),u,t,i,f,e;if(r.length!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(u)>-1)?u:(t=this.getValueFromArg(r[0]),i=this.getValueFromArg(r[1]),f=this._parseDouble(t),e=this._parseDouble(i),isNaN(f)||(t=f.toString()),isNaN(e)||(i=e.toString()),t.split(this.tic).join("")==i.split(this.tic).join("")?"TRUE":"FALSE")};this.computeFind=function(n){var t=this.splitArgsPreservingQuotedCommas(n),r,e,u;if(t.length!=2&&t.length!=3||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.TwoTextWithNumber),this.getErrorStrings().indexOf(r)>-1))return r;var o=this._stripTics0(this.getValueFromArg(t[0])),f=this._stripTics0(this.getValueFromArg(t[1])),i=1;return(t.length==3&&(e=this.getValueFromArg(t[2]),i=this._parseDouble(e),isNaN(i)&&(i=1)),i<=0||i>f.length)?"#VALUE!":(u=f.indexOf(o,i-1),u<0)?"#VALUE!":(Number(u)+Number(1)).toString()};this.computeSearch=function(n){var u=this.splitArgsPreservingQuotedCommas(n),s=u.length,o,e=1,h,t,r,i,c,f;if(s!=2&&s!=3||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(h=this.formulaErrorStringCheck(n,FormulaArgumentType.TwoTextWithNumber),this.getErrorStrings().indexOf(h)>-1))return h;if(t=this._stripTics0(this.getValueFromArg(u[0])).toUpperCase(),r=this._stripTics0(this.getValueFromArg(u[1])).toUpperCase(),!this._isCellReference(u[0])&&u[0][0]!=this.tic||!this._isCellReference(u[1])&&u[1][0]!=this.tic){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[5].toString()}if(i=t,t.indexOf("*")>-1)t=t.split("*").join("")[0].toString();else if(t.indexOf("?")>-1){for(c=i.substring(0,i.indexOf("?")),f=Number(r.indexOf(c));r.indexOf(i)==-1;)for(f=Number(r.substring(f).indexOf(c))+Number(f)+1,i=t;i.indexOf("?")!=-1;){if(r.length<=f)return this.getErrorStrings()[1].toString();i=i.substring(0,i.indexOf("?"))+r[f]+t.substring(i.indexOf("?")+1);f++}t=i}if(o=Number(r.indexOf(t))+1,s==3){if(e=parseInt(this.getValueFromArg(u[2])),e==1&&t[0]==r[0])return e;if(!isNaN(e))try{o=Number(r.indexOf(t,e))+1}catch(l){return this.getErrorStrings()[1].toString()}}return o==0?this.getErrorStrings()[1].toString():o.toString()};this.computeSubstitute=function(n){var t=this.splitArgsPreservingQuotedCommas(n),e,r,c,s,o,f;if(t.length!=3&&t.length!=4){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation())for(e=0;e<3;e++)if(r=this.formulaErrorStringCheck(t[e],FormulaArgumentType.Text),this.getErrorStrings().indexOf(r)>-1)return r;if(t.length==4&&this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(t[3],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;var i=this._stripTics0(this.getValueFromArg(t[0])),u=this._stripTics0(this.getValueFromArg(t[1])),h=this._stripTics0(this.getValueFromArg(t[2]));if(t.length==3){if(u==""||u==null)return i;i=i.split(u).join(h)}else{if(c=this.getValueFromArg(t[3]),!this._isCellReference(t[3])&&t[3][0]==this.tic){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[5].toString()}if(s=parseInt(c),!isNaN(s)){for(o=s,f=-1;o>0&&(f=i.indexOf(u,Number(f)+1))>-1;)o--;o==0&&(i=i.substring(0,f)+h+i.substring(Number(f)+Number(u.length)))}}return i};this.computeUniChar=function(n){var u=this.splitArgsPreservingQuotedCommas(n),e=u.length,i,t,r,f;if(e!=1||n==""){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(i)>-1))return i;if(t=this.getValueFromArg(u[0]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,r=this._parseDouble(this._stripTics0(t)),isNaN(r)||r<=0){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return t[0]==this.tic?this.getErrorStrings()[5].toString():this.getErrorStrings()[1].toString()}return f=String.fromCharCode(t),f.toString()};this.computeClean=function(n){var f=this.splitArgsPreservingQuotedCommas(n),r,i,u;if(f.length!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments].toString()}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(r)>-1))return r;var t=this.getValueFromArg(f[0]),e=t=="TRUE"||t=="FALSE"?!0:!1;if(this.getErrorStrings().indexOf(t)>-1)return t;if(t.indexOf(this.tic)==-1&&!this._isCellReference(n)&&isNaN(this._parseDouble(t))&&!e)return this.getErrorStrings()[5].toString();for(i=0;i<=31;i++)u=this.computeChar(i.toString()),t.indexOf(u)>-1&&(t=t.replace(u,""));return t[0]==this.tic&&t[t.length-1]==this.tic&&(t=this._substring(t,1,t.length-1)),t};this.computeTrim=function(n){var u=this.splitArgsPreservingQuotedCommas(n),i;if(u.length!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(i)>-1))return i;for(var t=this._stripTics0(this.getValueFromArg(n)),f=[this.tic,""],r=0;t.length!=r;)r=t.length,t=t.split("  ").join(" ");return t};this.computeRept=function(n){var t=this.splitArgsPreservingQuotedCommas(n),o=t.length,f,r,s,u,i,e;if(o!=2||n==""){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(n,FormulaArgumentType.TextWithNumber),this.getErrorStrings().indexOf(f)>-1))return f;if(r=this._stripTics0(this.getValueFromArg(t[0])),s=1,t[1]=this.getValueFromArg(t[1].split(this.tic).join("")),t[1]=t[1].split(this.tic).join("")=="TRUE"?"1":t[1].split(this.tic).join("")=="FALSE"?"0":t[1],r==null||r==""||t[1]==null||t[1]=="")return"";if(u=parseInt(t[1].split(this.tic).join("")),u<0||isNaN(u)){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[1].toString()}for(i="",e=0;e<u;e++){if(i.length>32767){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[1].toString()}i=i.concat(r)}return i};this.computeProper=function(n){var i=this.splitArgsPreservingQuotedCommas(n),f=i.length,u,t,r;if(f!=1){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._wrong_number_arguments]}if(u=this.getValueFromArg(i[0]),!this._isCellReference(i[0])&&i[0][0]!=this.tic){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[5].toString()}return t=this._stripTics0(u),r=t.indexOf("'"),r>=0&&r<t.length-1&&(t=t.replace(/[']/," '")),/[^a - zA - Z]/.test(t)?t=t.replace(/\w\S*/g,function(n){for(var t=0,i=n[t].toString().toUpperCase().charCodeAt(0);!(i>64&&i<91);)t++,n[t]!=undefined?i=n[t].toUpperCase().charCodeAt(0):(t--,i=0);return n.substr(0,t)+n.charAt(t).toUpperCase()+n.substr(t+1).toLowerCase()}):/[a - zA - Z0 - 9] + $/.test(t)&&(t=t.replace(/[^a-zA-Z0-9_\\]/,"")),t};this.computeT=function(n){var r=0,u=this.splitArgsPreservingQuotedCommas(n),i,o,f,e;if(u.length>1)for(i=0;i<u.length;i++)if(r++,u[i].indexOf(":")>-1)for(--r,o=this.getCellsFromArgs(i),f=0;f<o.length;f++)r++;if(r>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(e)>-1))return e;var t=this.getValueFromArg(n),s=t=="TRUE"||t=="FALSE"?!0:!1;return isNaN(this._parseDouble(t))&&!s&&isNaN(this._isDate(t))?/[a-zA-Z0-9!#$%&'()*+,/:;<=>?@\^_`{|}~-]/.test(t)?this._stripTics0(t):" ":" "};this.computeNumberValue=function(n){var i=this.splitArgsPreservingQuotedCommas(n),l=i.length,f=this._parseDecimalSeparator,e=this._parseArgumentSeparator,o,t,r,u,s,h,c;if(l>3||n==""){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(o=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(o)>-1))return o;if(t=this._stripTics0(this.getValueFromArg(i[0])),l>1)try{if(r=i.length>1&&i[2]!=null?this._stripTics0(this.getValueFromArg(i[1])):e,u=i.length>1&&i[2]!=null?this._stripTics0(this.getValueFromArg(i[2])):f,r=="invalid expression"||u=="invalid expression"||(r==null||r=="")&&(u==null||u=="")){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}f=this._stripTics0(r)[0];e=this._stripTics0(u)[0]}catch(a){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}if(t==""||t==null)return"0";if(s=t.indexOf(f),h=t.indexOf(e),h>=s&&(s!=-1||h!=-1)&&i.length>2){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}if(t=t.replace(this.tic,""),t=t.replace(" ",""),t=t.replace(e.toString(),""),t=t.replace(f,this._parseDecimalSeparator),c=this.computeValue(t),isNaN(c)||t[t.length-1]=="$"){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}return c};this.computeConcatenate=function(n){var i="",r=this.splitArgsPreservingQuotedCommas(n),u,t,f;if(!n||n.length==0){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(u)>-1))return u;for(t=0;t<r.length;t++){if(this._isCellReference(r[t])&&r[t].indexOf(":")!=-1){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}f=r[t];i=i.concat(this._stripTics0(this.getValueFromArg(r[t])))}return i.indexOf("#N/A")>-1&&(i="#N/A"),i};this.computeValue=function(n){var i="",r,h,f,e,o,s;try{if(r=this.splitArgsPreservingQuotedCommas(n),h=r.length,h!=1||n==""){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(f)>-1))return f;var t=r[0],u=!1,c=!1;if(t.indexOf(this.tic)>-1&&(u=!0),t=this._stripTics0(this.getValueFromArg(t)),(t!=null||t!="")&&!this._isCellReference(t)&&u&&isNaN(this._parseDouble(t.split(",").join("").split("$").join("")))&&(t=t.split(this.tic).join(""),c=!0),!this._isCellReference(r[0])&&r[0].indexOf(this.tic)==-1&&isNaN(this._parseDouble(t))){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[5].toString()}if((t==null||t=="")&&u){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}if((t==null||t=="")&&!u){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._invalid_arguments]}if(t.indexOf(":")>-1)return i=this.computeTimevalue(t),i.toString();if(t[0]=="$"&&(t=t.split("$").join("")),t.indexOf(",")>-1&&(t=t.split(",").join("")),(t[0]=="%"||t[t.length-1]=="%")&&(t=t.split("%").join(""),e=this._parseDouble(t),isNaN(e)||(t=(e/100).toString())),o=new Date(Date.parse(t)),s=this._parseDouble(t),isNaN(s))if(o.toString()!="Invalid Date")i=this._toOADate(o);else{if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}else i=s.toString()}catch(l){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}return i.toString()};this.computeDollar=function(n){var u=this.splitArgsPreservingQuotedCommas(n),t=u[0],i="2",e,f,o,r,s,h;if(u.length==2&&(i=u[1]),u.length>2||n==""){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;if(t=this.getValueFromArg(t),t=t==""||t==null?"0":t,t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,i=this.getValueFromArg(i),i=i==""||i==null?"0":i,i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,f=this._parseDouble(t),o=!1,isNaN(this._parseDouble(i))){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[5].toString()}if((t==null||t=="")&&this._isCellReference(u[0])&&t[0]!=this.tic&&t[t.length-1]!=this.tic&&(f=0,o=!0),isNaN(f)&&!o){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}return r=this._parseDouble(i),s=0,isNaN(r)&&(r=i==""?0:2),r>0?ej.format(f,"c"+r):(h=Math.pow(10,-r),s=Math.round(f/h)*h,ej.format(s,"c0"))};this.computeFixed=function(n){var f=this.splitArgsPreservingQuotedCommas(n),r=f[0],i="2",t="FALSE",s,h,e,u,c,o,l;if(f.length>3||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(s=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(s)>-1))return s;if(h=f.length,h>1&&(i=f[1]),h>2&&(t=f[2]),r=this.getValueFromArg(r),i=this.getValueFromArg(i),t=this.getValueFromArg(t),r=r==""||r==null?"0":r,i=i==""||i==null?"0":i,t=t==""||t==null?"0":t,e=this._parseDouble(r),u=this._parseDouble(i),isNaN(u)&&(u=2),c=this._parseDouble(t),isNaN(c))if(t.toUpperCase()==this.falseValueStr)o=!1;else if(t.toUpperCase()==this.trueValueStr)o=!0;else return"#NAME?";else o=c==0?!1:!0;return o&&(ej.preferredCulture().numberFormat[","]=""),u>0?ej.format(e,"n"+u):(l=Math.pow(10,-u),e=Math.round(e/l)*l,ej.format(e,"n0"))};this.computeBin2Dec=function(n){var r=this.splitArgsPreservingQuotedCommas(n),i,t,u;if(r.length>1||n=="")if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.formulaErrorStrings[this._wrong_number_arguments];if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(i)>-1))return i;if(t=this.getValueFromArg(r[0]).split(this.tic).join(this._string_empty),!/^[01]{1,10}$/.test(t))return this.getErrorStrings()[4].toString();try{return t==""&&(t="0"),u=parseInt(t,2),t.length==10&&t.substring(0,1)=="1"?(parseInt(t.substring(1),2)-512).toString():u.toString()}catch(f){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}};this.computeBin2Oct=function(n){var i=this.splitArgsPreservingQuotedCommas(n),t="",s,r,f,e,o,h,u;if(i.length>2||n=="")if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.formulaErrorStrings[this._wrong_number_arguments];else i.length>1&&(places=parseInt(this.getValueFromArg(i[1]).split(this.tic).join("")));if(this.getEnableFormulaErrorValidation()&&(s=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(s)>-1))return s;if(r=this.getValueFromArg(i[0]).split(this.tic).join(""),!/^[01]{1,10}$/.test(r))return this.getErrorStrings()[4].toString();if(f=i.toString(),f.length==10&&f.substring(0,1)=="1"){for(e=10,o="",h=0;h<3;h++){e=e-3;var l=this._substring(f,e,3),a=this._parseDouble(parseInt(l,2).toString(8)),c=0;for(u=a;u>0;c+=parseInt(u%10),u=parseInt(u/10));o=c.toString()+o}return"7777777"+o}if(r<0)return(1073741824+r).toString(8);if(t=parseInt(r,2).toString(8),typeof places=="undefined")return t;if(isNaN(places))return"#VALUE!";if(places<0)return"#NUM!";if(places=Math.floor(places),places>=t.length&&places<=10)while(places-t.length>0)t="0"+t,places--;else if(this.getRethrowLibraryComputationExceptions())throw new Error("Parameter is not valid");else throw(new this.getErrorStrings)[4].toString();return t};this.computeBin2Hex=function(n){var u=0,r=this.splitArgsPreservingQuotedCommas(n),t=this.getValueFromArg(r[0]).split(this.tic).join(this._string_empty),i="",f;if(r.length>2||n=="")if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.formulaErrorStrings[this._wrong_number_arguments];else r.length>1&&(u=this._parseDouble(r[1]));if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1))return f;if(t=="1111111111")return t.replace("1","F");if(t.length==10&&t.substring(0,1)=="1")return(1073741312+parseInt(t,2).toString(8)).toString();try{if(t.length>10||this._parseDouble(t)<0||isNaN(t))if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[4].toString();else if(i=parseInt(t,2).toString(16),i=="NaN"&&(i=this.getErrorStrings()[4].toString()),r.length>1)if(u>=i.length&&u<=10)i=this._padLeft("0",u,i);else if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else i=this.getErrorStrings()[4].toString()}catch(e){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else if(this.computeIsText(t)==this.trueValueStr)i=this.getErrorStrings()[5].toString();else return t==""?"0":this.getErrorStrings()[4].toString()}return i.toUpperCase()};this.computeDec2Bin=function(n){var f=0,i=this.splitArgsPreservingQuotedCommas(n),u="",e,t,r,o;if(n==null||n==""||i.length>2||i.length<1){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;if(t=this.getValueFromArg(i[0]).split(this.tic).join(this._string_empty),r=i.length>1?this.getValueFromArg(i[1]).split(this.tic).join(this._string_empty):"10",isNaN(this._parseDouble(t))&&isNaN(this._parseDouble(r))){if((t!=null||t!="")&&this.computeIsText(t)==this.trueValueStr||(r!=null||r!="")&&this.computeIsText(r)==this.trueValueStr){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}if(t!=null&&t!=""&&this.computeIsText(t)==this.falseValueStr||t!=null&&t!=""&&this.computeIsText(r)==this.falseValueStr){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}if(t==null&&t==""||r==null||r==""){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}}try{if(i.length>1&&(o=this.getValueFromArg(i[1]).replace(this.tic,""),f=this._parseDouble(o)),isNaN(t)){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}if(this._parseDouble(t)<-512||this._parseDouble(t)>511)if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[4].toString();else if(u=(t>>>0).toString(2),!isNaN(this._parseDouble(t))&&this._parseDouble(t)<0&&(u=u.substring(u.length-10)),i.length>1){if(f>10){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[4].toString()}f<=10&&(u=this._padLeft("0",f,u))}}catch(s){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();}return u};this.computeDec2Oct=function(n){var f=0,u=this.splitArgsPreservingQuotedCommas(n),t=this.getValueFromArg(u[0]).split(this.tic).join(this._string_empty),r=u.length>1?this.getValueFromArg(u[1]).replace(this.tic,""):"10",i="",e,o;if(n==null||n==""||u.length>2||u.length<1){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;if(!(!isNaN(this._parseDouble(t))&&!isNaN(this._parseDouble(r)))){if((t!=null||t!="")&&this.computeIsText(t)==this.trueValueStr||(r!=null||r!="")&&this.computeIsText(r)==this.trueValueStr){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}if(this.getErrorStrings().indexOf(t)>-1)return t;if(this.getErrorStrings().indexOf(r)>-1)return r;if(t!=null&&t!=""&&this.computeIsText(t)==this.falseValueStr||t!=null&&t!=""&&this.computeIsText(r)==this.falseValueStr){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[1].toString()}if(t==null&&t==""||r==null||r==""){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[0].toString()}}try{if(u.length>1&&(o=this.getValueFromArg(u[1]).replace(this.tic,""),f=this._parseDouble(o)),this._parseDouble(t)<-536870912||this._parseDouble(t)>536870911)if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[4].toString();else if(this._parseDouble(t)<0)i=parseInt(t>>>0,10).toString(8),!isNaN(this._parseDouble(t))&&this._parseDouble(t)<0&&(i=i.substring(i.length-10));else if(i=parseInt(t,10).toString(8),!isNaN(this._parseDouble(t))&&this._parseDouble(t)<0&&(i=i.substring(i.length-10)),u.length>1){if(f>10){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[4].toString()}f<=10&&(i=this._padLeft("0",f,i))}}catch(s){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();}return i};this.computeHex2Bin=function(n){var u=0,r=this.splitArgsPreservingQuotedCommas(n),s,h,i,o,c;if(r.length>2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(n==this._parseArgumentSeparator.toString())if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[0].toString();if(this.getEnableFormulaErrorValidation()&&(s=this.formulaErrorStringCheck(r[0],FormulaArgumentType.Text),this.getErrorStrings().indexOf(s)>-1))return s;if(r.length==2&&this.getEnableFormulaErrorValidation()&&(h=this.formulaErrorStringCheck(r[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(h)>-1))return h;var t=this.getValueFromArg(r[0]),f=r.length==2?this.getValueFromArg(r[1]):"0",e=0;if(e=this._parseDouble(f),!isNaN(e)&&(e<0||e>10)){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}if(Boolean(t=="TRUE"||t=="FALSE")||r.length==2&&Boolean(f=="TRUE"||f=="FALSE")){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}if(t!=""){if(t[0]!=this.tic&&!this._isCellReference(t)&&isNaN(this._parseDouble(t)))if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[5].toString()}else if(this._isCellReference(r[0])&&t=="")return"0";if(t[0]==this.tic&&(t.split(this.tic).join("")==null||t.split(this.tic).join("")==""))return"0";if(f[0]==this.tic)if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[1].toString();else if(isNaN(this._parseDouble(f.split(this.tic).join(""))))if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[5].toString();if(u=this._parseDouble(f),u<0||t[0]=="-")if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[4].toString();i="";t=t.split(this.tic).join("");try{for(o=0;o<t.length;o++)c=this._padLeft("0",4,parseInt(t.charAt(o),16).toString(2)),i+=c;if(t.split(this.tic).join("")=="FFFFFFFFFF")return this._parseDouble(t.split("F").join("1"));if(i.length>10||u>10||u<i.length&&u!=0)if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[4].toString();if(u>=i.length)i=this._padLeft("0",u,i);else if(u!=0)if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[4].toString();if(!isNaN(this._parseDouble(i))&&this._parseDouble(i)>536870911)if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[4].toString()}catch(l){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[4].toString()}return i};this.computeHex2Oct=function(n){var u=0,i=this.splitArgsPreservingQuotedCommas(n),e,o,t,f,s,r;if(i.length>2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(n==this._parseArgumentSeparator.toString())if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[0].toString();if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(i[0],FormulaArgumentType.Text),this.getErrorStrings().indexOf(e)>-1))return e;if(i.length==2&&this.getEnableFormulaErrorValidation()&&(o=this.formulaErrorStringCheck(i[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(o)>-1))return o;if(t=this.getValueFromArg(i[0]),i[0]=i[0].split(this.TIC).join(""),f=i.length==2?this.getValueFromArg(i[1]):"0",t!=""&&t[0]!=this.tic&&!this._isCellReference(t)&&isNaN(this._parseDouble(t))){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[5].toString()}if(Boolean(t=="TRUE"||t=="FALSE")||i.length==2&&Boolean(f=="TRUE"||f=="FALSE")){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}if(t[0]==this.tic&&(t.split(this.tic).join("")==null||t.split(this.tic).join("")==""))return"0";if(t.indexOf(this._parseArgumentSeparator.toString())>-1&&(s=t.indexOf(this._parseArgumentSeparator),t=t.split(this.tic).join("").substring(0,s-1)),r="",isNaN(this._parseDouble(f.split(this.tic).join(""))))if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[5].toString();if(u=this._parseDouble(f),u<0||t[0]=="-")if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[4].toString();if(i.length>2)if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[4].toString();i.length>1&&(u=this._parseDouble(i[1]));try{if(t=t.split(this.tic).join(""),r=parseInt(t,16).toString(8),!isNaN(this._parseDouble(r))&&r.length>10&&(r=r.substring(r.length-10)),i.length>1){if(u>=r.length&&u<=10)r=this._padLeft("0",u,r);else if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[4].toString();if(!isNaN(this._parseDouble(r))&&this._parseDouble(r)>536870911)if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[4].toString()}}catch(h){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.computeIsText(t)==this.trueValueStr?this.getErrorStrings()[4].toString():t==""?"0":this.getErrorStrings()[4].toString()}return r};this._padLeft=function(n,t,i){var u=i.length,r;if(i.length<t&&n.toString().length>0)for(r=0;r<t-u;r++)i=n.toString().charAt(0).concat(i);return i};this.computeAddress=function(n){var i=this.splitArgsPreservingQuotedCommas(n),f=i.length,c,s,t,r,e,o,u,a,h;if(f>5||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()){for(c=0;c<f-1;c++)if(s=this.formulaErrorStringCheck(i[c],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(s)>-1)return s;if(f==5&&(s=this.formulaErrorStringCheck(i[4],FormulaArgumentType.Text),this.getErrorStrings().indexOf(s)>-1))return s}if(t=this._string_empty,r=this.getValueFromArg(i[0].split(this.tic).join(this._string_empty)),r=r.split(this.tic).join("")=="TRUE"?"1":r.split(this.tic).join("")=="FALSE"?"0":r,e=this.getValueFromArg(i[1].split(this.tic).join(this._string_empty)),e=e.split(this.tic).join("")=="TRUE"?"1":e.split(this.tic).join("")=="FALSE"?"0":e,r==""||e==""){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[1].toString();return this.getErrorStrings()[1].toString()}if(o=this._parseDouble(r),u=this._parseDouble(e),o<1||u<1){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[1].toString();return this.getErrorStrings()[1].toString()}var v=f>2?this.getValueFromArg(i[2].split(this.tic).join(this._string_empty)):"1",l=u,y=l%26;if(l=parseInt(l/26),t=t+this._charTable[y],t=t+this._charTable[parseInt(l)],a=t.split("").join(",").split(this.getParseArgumentSeparator()),a.reverse(),h=a.join(""),t="$"+h+"$"+r,f>2)switch(v){case"2":t=h+"$"+r;break;case"3":t="$"+h+r;break;case"4":t=h+r}if(f>3&&(this.getValueFromArg(i[3].split(this.tic).join(this._string_empty))=="FALSE"||this.getValueFromArg(i[3].split(this.tic).join(this._string_empty))=="0"))switch(v){case"":t="R"+o+"C"+u;break;case"1":t="R"+o+"C"+u;break;case"2":t="R"+o+"C["+u+"]";break;case"3":t="R["+o+"]C"+u;break;case"4":t="R["+o+"]C["+u+"]"}return f>4&&this.getValueFromArg(i[4].split(this.tic).join(this._string_empty))==this._string_empty&&(i[4]=this.getValueFromArg(i[4]),t=i[4].split(this.tic).join(this._string_empty)+"!"+t),t};this.computeAreas=function(n){var r,u,f,t,e,i;if(n.indexOf(" ")>0&&(n=n.substring(0,n.indexOf(" "))),!this._isCellReference(n)&&!this.getNamedRanges().containsKey(n)&&!n.indexOf(")")){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_arguments];return this.formulaErrorStrings[this._invalid_arguments]}if(r=this._splitArguments(n,")"),r.length>2){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.getErrorStrings()[1].toString()}if(u=r[0].split("(").join(this._string_empty),this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(u,FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(f)>-1))return f;for(t=this.splitArgsPreservingQuotedCommas(u),e=t.length,i=0;i<t.length;i++)if(!this._isCellReference(t[i])){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[5].toString()}return t.length.toString()};this.computeChoose=function(n){var t,i,e,r,u,f;if(t=this.splitArgsPreservingQuotedCommas(n),t.length<2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(t[0],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1))return u;if(i=this.getValueFromArg(t[0]).split(this.tic).join(""),i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,e=this.splitArgsPreservingQuotedCommas(i),i=e[0],r=parseInt(i),isNaN(r)||r>t.length-1||r<1)return"#VALUE!";if(this._isInteriorFunction)return t[r];if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(t[r],FormulaArgumentType.Text),this.getErrorStrings().indexOf(u)>-1))return u;if(f=this.getValueFromArg(t[r]),f==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_Math_argument];return this.getErrorStrings()[5].toString()}return f};this.computeColumn=function(n){var t,u,i,r;if(n==null||n==this._string_empty)return this.colIndex(this.cell).toString();if(u=this.splitArgsPreservingQuotedCommas(n),u.length!=1){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(i)>-1))return i;if(n.indexOf(":")>-1&&this._isArrayFormula){if(r=this._computeArrayInteriorFunction(n,"COLUMN",this.copmuteFunctionLevel),r==this._string_empty)n=this.getCellsFromArgs(n)[0];else return r;n.indexOf(":")>-1&&(n=this.getCellsFromArgs(n)[0])}if(t=this.colIndex(n).toString(),t<=0||!isNaN(this._parseDouble(n))){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_Math_argument];return this.getErrorStrings()[5].toString()}return t};this.computeColumns=function(n){var t,e,o,s,h,i,u,f,r;if(t=this.splitArgsPreservingQuotedCommas(n),t.length!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(e)>-1))return e;if(o=-1,this._parseDouble(t[0],o))return"1";if(t[0].indexOf(this.tic)&&!t[0].indexOf(";")&&!t[0].indexOf(this.getParseArgumentSeparator().toString())||!this._isCellReference(t[0])&&!this.getNamedRanges().containsValue(t[0])&&!n.indexOf(";")&&!n.indexOf(this.getParseArgumentSeparator().toString())){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[1].toString();return this.getErrorStrings()[1].toString()}if(i=1,n.indexOf(":")>-1)u=this.getCellsFromArgs(n),s=this.colIndex(u[0].toString()),h=this.colIndex(u[u.length-1].toString()),i=h-s+1,i=i>0?i:1;else if(n.indexOf(";")>-1)for(r=this._splitArguments(n.split(this.tic).join(this._string_empty),";"),f=1;f<r.length;f++){if(this.splitArgsPreservingQuotedCommas(r[f-1]).length!=this.splitArgsPreservingQuotedCommas(r[1]).length){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}i=this.splitArgsPreservingQuotedCommas(r[0]).length}else r=this.splitArgsPreservingQuotedCommas(t[0].split(this.tic).join(this._string_empty)),i=r.length;return i.toString()};this.computeFormulaText=function(n){var i=this.splitArgsPreservingQuotedCommas(n),f=i.length,t=this._string_empty,r,u;if(f!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(t=this.getValueFromArg(i[0]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,this.getNamedRanges().containsValue(t)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[1].toString()}if(r=CalcEngine.getSheetFamilyItem(this.grid),i[0].indexOf(this.sheetToken)==-1&&this.isSheetMember()&&r.parentObjectToToken!=null&&(i[0]=r.parentObjectToToken.getItem(this.grid)+i[0]),this.getFormulaInfoTable().containsKey(i[0]))u=this.getFormulaInfoTable().getItem(i[0]),t=u.getFormulaText(),this.getFormulaText(t);else{if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.rowIndex(i[0])<=0?this.getErrorStrings()[5].toString():this.getErrorStrings()[0].toString()}return t};this.computeHyperlink=function(n){var i=this.splitArgsPreservingQuotedCommas(n),u=i.length,r,f,t;if(u>2||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(r)>-1)?r:(f=this._stripTics0(this.getValueFromArg(i[0])),u==2)?(t=this._stripTics0(this.getValueFromArg(i[1])),t==null)?"0":t==this._string_empty?"0":t:f};this.computeAbs=function(n){var i=this.splitArgsPreservingQuotedCommas(n),f=i.length,r,t,u;if(f!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;if(t=this.getValueFromArg(i[0]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,u=ej.parseFloat(t.split(this.tic).join("")),isNaN(u)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return t[0]==this.tic||this._isCellReference(i[0])?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}return Math.abs(u).toString()};this.computeAcos=function(n){var r=this.splitArgsPreservingQuotedCommas(n),f=r.length,u,t,i;if(f!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1))return u;if(t=this.getValueFromArg(r[0]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,i=this._parseDouble(t),isNaN(i)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return t[0]==this.tic||this._isCellReference(r[0])?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}if(i>1||i<-1){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[4].toString()}return Math.acos(i).toString()};this.computeAcosh=function(n){var r=this.splitArgsPreservingQuotedCommas(n),f=r.length,u,t,i;if(f!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1))return u;if(t=this.getValueFromArg(r[0]),t=t.split(this.tic).join("").toUpperCase()=="TRUE"?"1":t.split(this.tic).join("").toUpperCase()=="FALSE"?"0":t,t=this._stripTics0(t),i=this._parseDouble(t),isNaN(i)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return t[0]==this.tic||this._isCellReference(r[0])?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}if(z=Math.log(i+Math.sqrt(i*i-1)),i<=0||z==Infinity){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[4].toString()}return z.toString()};this.computeAcot=function(n){var u=this.splitArgsPreservingQuotedCommas(n),f=u.length,i,e,t,r;if(f!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(i)>-1)?i:(e=this.getValueFromArg(n),n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,t=this._parseDouble(n),r=0,isNaN(t)||(r=t<=0?Math.PI/2-Math.atan(t):Math.atan(1/t)),r.toString())};this.computeAcoth=function(n){var f=this.splitArgsPreservingQuotedCommas(n),e=f.length,r,t,i,u;if(e!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;if(t=this.getValueFromArg(f[0]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,i=this._parseDouble(t),isNaN(i)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[5].toString()}if(i<1&&i>-1){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[4].toString()}if(u=(Math.log((i+1)/(i-1))/2).toString(),this.computeIsErr(u)==this.trueValueStr){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[4].toString()}return u};this.computeArabic=function(n){var u=this.splitArgsPreservingQuotedCommas(n),i,o,s;if(u.length!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(i=this.getValueFromArg(u[0]),this.getErrorStrings().indexOf(i)!=-1)return i;if(o=parseInt(i.split(this.tic).join("").toUpperCase()),!isNaN(o)||!this._isCellReference(u[0])&&u[0][0]!=this.tic){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return isNaN(o)?this.getErrorStrings()[5].toString():this.getErrorStrings()[1].toString()}i=i.split(this.tic).join("").toUpperCase();var f=0,h=null,r=0,t=0,e=0;for(e=1e3,s=!1,f=1;f<=i.length;f++){h=this._substring(i,f-1,1);switch(h){case"-":s=!0;break;case"I":t=1;break;case"V":t=5;break;case"X":t=10;break;case"L":t=50;break;case"C":t=100;break;case"D":t=500;break;case"M":t=1e3;break;default:if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[1].toString()}r=t>e?r+t-2*e:r+t;e=t}return s&&(r=-r),r.toString()};this.computeAsin=function(n){var r=this.splitArgsPreservingQuotedCommas(n),o=r.length,u,t,i,f,e;if(o!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1))return u;if(t=this.getValueFromArg(r[0]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,i=this._parseDouble(t),isNaN(i)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return t[0]==this.tic||this._isCellReference(r[0])?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}if(i>1||i<-1){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[4].toString()}return f=this._parseDouble(t),e=0,isNaN(f)||(e=Math.asin(f)),e.toString()};this.computeAtan=function(n){var i=this.splitArgsPreservingQuotedCommas(n),t;if(i.length>1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(t=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(t)>-1)?t:(n=this.getValueFromArg(n),n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,this._computeMath(n,Math.atan).toString())};this.computeAtan2=function(n){var f=this.splitArgsPreservingQuotedCommas(n),c=f.length,e,u,r,o,t,i,s,h;if(c!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;if(r=0,o=0,t=this.getValueFromArg(f[0]),i=this.getValueFromArg(f[1]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,s=t=="true"?!0:!1,h=i=="true"?!0:!1,s?t=s.toString():t==this._string_empty&&(t="0"),h?i=h.toString():i==this._string_empty&&(i="0"),this.getErrorStrings().indexOf(t)>-1)return t;if(this.getErrorStrings().indexOf(i)>-1)return i;if(u=this._parseDouble(t),r=this._parseDouble(i),isNaN(u)||isNaN(r))if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[1].toString();else return this.getErrorStrings()[1].toString();else if(r==0&&u==0)if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[3].toString();else return this.getErrorStrings()[3].toString();return o=Math.atan2(r,u),o.toString()};this.computeCeilingMath=function(n){var e=this.splitArgsPreservingQuotedCommas(n),s=e.length,h,r,t,u,y;if(this._isTextEmpty(n)||s>3||s<1){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(h=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(h)>-1))return h;r=this.getValueFromArg(e[0]);r=r.split(this.tic).join("")=="TRUE"?"1":r.split(this.tic).join("")=="FALSE"?"0":r;t=s>1&&e[1].length!=0?this.getValueFromArg(e[1]):"1";t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t;u=s==3&&e[2].length!=0?this.getValueFromArg(e[2]):"1";u=u.split(this.tic).join("")=="TRUE"?"1":u.split(this.tic).join("")=="FALSE"?"0":u;var i,f=-1,o=-1,c,l=!1,a=!1,v=!1;if(l=r.split(this.tic).join(this._string_empty)=="true"?!0:!1,a=t.split(this.tic).join(this._string_empty)=="true"?!0:!1,v=u.split(this.tic).join(this._string_empty)=="true"?!0:!1,r=l?l.toString():r,t=a?a.toString():t,u=v?v.toString():u,this.getErrorStrings().indexOf(r)!=-1)return r;if(this.getErrorStrings().indexOf(t)!=-1)return t;if(this.getErrorStrings().indexOf(u)!=-1)return u;if(t.indexOf(" ")>-1){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[1].toString()}if(t.startsWith(this.tic)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[4].toString()}if(i=this._parseDouble(r),f=this._parseDouble(t),o=this._parseDouble(u),isNaN(i)&&!isNaN(f)&&!isNaN(o)){if(i==0&&!this._isTextEmpty(r)&&this.computeIsText(r)==this.falseValueStr||f==0&&!this._isTextEmpty(t)&&this.computeIsText(t)==this.falseValueStr||o==0&&!this._isTextEmpty(u)&&this.computeIsText(u)==this.falseValueStr){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[5].toString()}if(this._isTextEmpty(r.split(this.tic).join(this._string_empty))&&r[0]==this.tic||this._isTextEmpty(t.split(this.tic).join(this._string_empty))&&t[1]==this.tic||this._isTextEmpty(u.split(this.tic).join(this._string_empty))&&u[2]==this.tic){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}if(this._isTextEmpty(r.split(this.tic).join(this._string_empty))||this._isTextEmpty(t.split(this.tic).join(this._string_empty))||this._isTextEmpty(u.split(this.tic).join(this._string_empty)))return"0";if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}return(i<0&&o<0&&(f=-1),i==0||f==0)?"0":(y=0,i=this._parseDouble(i.toString()),i>0&&(i+=.4999999999),i<0&&-.5>=i-parseInt(i)&&(i-=.4999999999),i=Math.round(i),y=Math.floor(i/f),c=y*f,i<0&&i%2!=0&&o==0&&(c+=f),c.toString())};this.computeCeiling=function(n){var f=this.splitArgsPreservingQuotedCommas(n),h=f.length,c,i,t,o,s,u,r,e;if(h!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(c=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(c)>-1))return c;if(f[0]=this.getValueFromArg(f[0]),i=h>1?f[0]:"1",i=i==null||i==""?"0":i,i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,f[1]=this.getValueFromArg(f[1]),t=h>1&&f[1].length!=0?f[1]:"0",t=t==""||t==null?"0":t,t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,o=!1,s=!1,o=i.split(this.tic).join(this._string_empty)=="true"?!0:!1,s=t.split(this.tic).join(this._string_empty)=="true"?!0:!1,i=o?o.toString():i,t=s?s.toString():t,t==this.trueValueStr)return"1";if(t==this.falseValueStr)return"0";if(t.indexOf(" ")>-1){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[1].toString()}if(this.getErrorStrings().indexOf(i)!=-1)return i;if(this.getErrorStrings().indexOf(t)!=-1)return t;if(r=-1,e=-1,u=this._parseDouble(i),r=this._parseDouble(t),isNaN(u)&&!isNaN(r)){if(u==0&&!this._isTextEmpty(i)&&this.computeIsText(i)==this.falseValueStr||r==0&&!this._isTextEmpty(t)&&this.computeIsText(t)==this.falseValueStr){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_index];return this.getErrorStrings()[5].toString()}if(this._isTextEmpty(i.split(this.tic).join(this._string_empty))&&i[0]==this.tic||this._isTextEmpty(t.split(this.tic).join(this._string_empty))&&t[0]==this.TIC){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this.invalid_arguments];return this.getErrorStrings()[1].toString()}if(this._isTextEmpty(i.split(this.tic).join(this._string_empty))||this._isTextEmpty(t.split(this.tic).join(this._string_empty)))return"0";if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}if(u=this._parseDouble(i),r=this._parseDouble(t),!isNaN(u)&&!isNaN(r)){if(u>0&&r<0){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}if(u==0||r==0)return"0";if(e=Math.floor(u/r)*r,r>0)while(e<u)e+=r;else while(e>u)e+=r}return e.toString()};this.computeDecimal2=function(n){var r=this.splitArgsPreservingQuotedCommas(n),h=r.length,o,t,i,f,u,s,e;if(h>3){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/",!this.getValueFromArg(r[0]).indexOf(this.tic)&&!this._isCellReference(r[0])){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_Math_argument];return this.getErrorStrings()[1].toString()}i=0;try{t=this.getValueFromArg(r[0]).split(this.tic).join("");i=parseInt(this.getValueFromArg(r[1]).split(this.tic).join(""))}catch(c){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_Math_argument]+c;return this.getErrorStrings()[1].toString()}if(i<2||i>o.length){if(this._rethrowLibraryComputationExceptions)throw"Base requested outside range, it should be from 2 - 16";return this.getErrorStrings()[4].toString()}for(t=this._isIE8?t.replace(/^\s+|\s+$/g,""):t.trim(),i<=36&&(t=t.toUpperCase()),u=0,f=0;u<t.length&&!this._isWhiteSpace(t[u]);)if(s=t.substring(u,1),e=o.indexOf(s),e>=0&&e<i)f*=i,f+=e,u++;else{if(this._rethrowLibraryComputationExceptions)throw"Base requested outside range, it should be from 2 - 16";return this.getErrorStrings()[4].toString()}return f.toString()};this.computeCombin=function(n){var e=this.splitArgsPreservingQuotedCommas(n),c=e.length,o,s,r,h,t,i,u,f;if(c!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(o=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(o)>-1))return o;if(h=0,t=this.getValueFromArg(e[0]).split(this.tic).join(""),t=t==null||t==""?"0":t,t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,i=this.getValueFromArg(e[1]).split(this.tic).join(""),i=i==null||i==""?"0":i,i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,t.split(this.tic).join("")==this._string_empty||i.split(this.tic).join("")==this._string_empty)if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[1].toString();else return this.getErrorStrings()[1].toString();if(s=this._parseDouble(t),r=this._parseDouble(i),isNaN(s)||isNaN(r)){if(this._rethrowLibraryComputationExceptions)return this.formulaErrorStrings[this._invalid_arguments];throw this.getErrorStrings()[1].toString();}else{if(u=parseInt((r+.1).toString()),f=parseInt((s+.1).toString()),f<u||u<0||f<0||r<0)if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[1].toString();else return this.getErrorStrings()[4].toString();h=this._comb(u,f)}return h.toString()};this.computeCombinA=function(n){var r=this.splitArgsPreservingQuotedCommas(n),s=r.length,u,t,i,f,e,o;if(n.indexOf("u")>-1){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}if(s!=2||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1))return u;if(n.length>15){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}return t=this.getValueFromArg(r[0]).split(this.tic).join(""),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,t=t==""||t==0?"0":t,t=parseInt(t.split(this.tic).join("")),i=this.getValueFromArg(r[1]).split(this.tic).join(""),i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,i=i==""||i==0?"0":i,i=parseInt(i.split(this.tic).join("")),t==0&&i==0?f="1":(e=t+i-1,o=e+","+(t-1),f=this.computeCombin(o)),f};this.computeCos=function(n){var i=this.splitArgsPreservingQuotedCommas(n),t;if(i.length>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(t=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(t)>-1)?t:(n=this.getValueFromArg(n),n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,this._computeMath(n,Math.cos).toString())};this.computeCosh=function(n){var i=this._splitArguments(n,this.getParseArgumentSeparator().toString()),e=i.length,r,t,u,f;if(e>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;if(t=this.getValueFromArg(i[0]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,u=this._parseDouble(t.split(this.tic).join("")),isNaN(u)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return t[0]==this.tic||this._isCellReference(i[0])?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}return f=Math.exp(u),(f+1/f)/2};this.computeCot=function(n){var u=this.splitArgsPreservingQuotedCommas(n),o=u.length,o=u.length,r,f,t,e,i;if(o!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;if(n=this.getValueFromArg(n),n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,n[0]==this.tic&&n[length-1]==this.tic&&(n=n.split(this.tic).join("")),n==this._string_empty){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}if(f=this.computeIsNumber(n),f==this.trueValueStr){if(t=n,t=t.indexOf("u")?t.split("u").join(this._string_empty):t,e=this._parseDouble(t),e>=134217728){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}}else{if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[5].toString()}if(n=="0"){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[3].toString();return this.getErrorStrings()[3].toString()}return i=this._computeMath(n,Math.tan),(i!="#NUM!"||i!="#VALUE!")&&(i=(1/parseFloat(i)).toString()),i};this.computeCsc=function(n){var r=this.splitArgsPreservingQuotedCommas(n),f=r.length,u,t,i;if(f!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1))return u;if(t=this.getValueFromArg(r[0]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,i=this._parseDouble(t),isNaN(i)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return t[0]==this.tic||this._isCellReference(r[0])?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}if(i>=134217728){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[4].toString()}return(1/parseFloat(Math.sin(i))).toString()};this.computeCsch=function(n){var r=this.splitArgsPreservingQuotedCommas(n),f=r.length,u,t,i,e;if(f!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1))return u;if(t=this.getValueFromArg(r[0]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,i=this._parseDouble(t),e=this.computeIsNumber(n),isNaN(i)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return t[0]==this.tic||this._isCellReference(r[0])?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}if(i==0){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[3].toString()}return i>709?"0":(2/(Math.exp(i)-Math.exp(-i))).toString()};this.computeDecimal=function(n){var r=this.splitArgsPreservingQuotedCommas(n),c=r.length,o,s,t,i,u,f,h,e;if(c>3||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(o=this.formulaErrorStringCheck(n,FormulaArgumentType.TextWithNumber),this.getErrorStrings().indexOf(o)>-1))return o;if(r[1]==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}s="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/";i=0;try{t=this.getValueFromArg(r[0]).split(this.tic).join("");i=parseInt(this.getValueFromArg(r[1]).split(this.tic).join(""))}catch(l){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_Math_argument]+l;return this.getErrorStrings()[1].toString()}if(i<2||i>s.length){if(this._rethrowLibraryComputationExceptions)throw"Base requested outside range, it should be from 2 - 16";return this.getErrorStrings()[4].toString()}for(t=this._isIE8?t.replace(/^\s+|\s+$/g,""):t.trim(),i<=36&&(t=t.toUpperCase()),f=0,u=0;f<t.length;)if(h=this._substring(t,f,1),e=s.indexOf(h),e>=0&&e<i)u*=i,u+=e,f++;else{if(this._rethrowLibraryComputationExceptions)throw"Base requested outside range, it should be from 2 - 16";return this.getErrorStrings()[4].toString()}return u.toString()};this.computeDegrees=function(n){var i=this.splitArgsPreservingQuotedCommas(n),e=i.length,r,t,u,f;if(e!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;if(t=this.getValueFromArg(i[0]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,u=this._parseDouble(t),isNaN(u)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return t[0]==this.tic||this._isCellReference(i[0])?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}return f=0,f=180*u/Math.PI,f.toString()};this.computeExp=function(n){var r=this.splitArgsPreservingQuotedCommas(n),f=r.length,i,t,u;if(f>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(i)>-1))return i;if(t=this.getValueFromArg(r[0]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,t==""&&(n="0"),u=this._parseDouble(t),u>709){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[4].toString()}return this._computeMath(n,Math.exp).toString()};this.computeEven=function(n){var e=0,r=this.splitArgsPreservingQuotedCommas(n),o=r.length,u,i,t,f;if(o>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1))return u;if(i=this.getValueFromArg(r[0]),t=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,t=this._parseDouble(t.split(this.tic).join("")),isNaN(t)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return i[0]==this.tic||this._isCellReference(r[0])?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}return f=Math.sign(t),t=Math.abs(t),t=Math.ceil(t),e=t%2==1?f*(t+1):f*t,e.toString()};this.computeFact=function(n){var i=0,o=this.splitArgsPreservingQuotedCommas(n),e,r,t,u,f;if(o.length>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;if(r=this.getValueFromArg(n),t=r.split(this.tic).join("").toUpperCase()=="TRUE"?"1":r.split(this.tic).join("").toUpperCase()=="FALSE"?"0":r,t=this._parseDouble(t.split(this.tic).join("")),isNaN(t))return this.getErrorStrings()[1].toString();if(t<0)return this.getErrorStrings()[4].toString();if(u=parseInt(t),u>12)for(i=this._factorialTable[12],f=13;f<=u;f++)i*=f;else i=this._factorialTable[u];return i.toString()};this.computeFactdouble=function(n){var r,u=1,e=this.splitArgsPreservingQuotedCommas(n),f,t,i;if(e.length>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1))return f;if(r=parseInt(this.getValueFromArg(n)),isNaN(r))return this.getErrorStrings()[1].toString();if(r<0)return this.getErrorStrings()[4].toString();if(t=r,i=t,t>3)while(i>0)u=u*i,i=i-2;else u=t==0?1:t;return u.toString()};this.computeFloor=function(n){var u=this.splitArgsPreservingQuotedCommas(n),e=u.length,f;if(e!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1))return f;var t=this._parseDouble(this.getValueFromArg(u[0])),i=this._parseDouble(this.getValueFromArg(u[1])),r=0;if(!isNaN(t)&&!isNaN(i)){if(t==0)return r.toString();if(t*i<=0&&!(t<0))return this.formulaErrorStrings[this._invalid_arguments];if(r=Math.ceil(t/i)*i,i>0)while(r>t)r-=i;else while(r<t)r-=i;i==.1&&(r=t)}return r.toString()};this.computeInt=function(n){var i=this.splitArgsPreservingQuotedCommas(n),f=i.length,t,r,u;if(f>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(t=this.getValueFromArg(i[0]),this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;if(t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,u=Math.floor(t).toString(),isNaN(u)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return t[0]==this.tic||this._isCellReference(i[0])?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}return u};this.computeLn=function(n){var i=this.splitArgsPreservingQuotedCommas(n),u=i.length,r,t;if(u>1)return this.formulaErrorStrings[this.invalid_arguments];if(r=this.getValueFromArg(i[0]),t=this._parseDouble(r),t<=0){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[4].toString()}if(isNaN(t)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return r[0]==this.tic||this._isCellReference(i[0])?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}return Math.log(t).toString()};this.computeLog=function(n){var f=this.splitArgsPreservingQuotedCommas(n),o=f.length,e,t,i,r,u;if(o>2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;if(t=this.getValueFromArg(f[0]),i=o==2?this.getValueFromArg(f[1]):"10",ej.isNullOrUndefined(ej.cultureObject)||ej.cultureObject.name=="en-US"||(t=!ej.isNullOrUndefined(t)&&t!=this._string_empty?ej.parseFloat(t.toString(),0,ej.cultureObject.name).toString():t,i=!ej.isNullOrUndefined(i)&&i!=this._string_empty?ej.parseFloat(i.toString(),0,ej.cultureObject.name).toString():i),t==""||t==null||i==""||i==null){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}if(t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,r=this._parseDouble(t),u=this._parseDouble(i),r<=0||u<=0){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[4].toString()}if(isNaN(r)||isNaN(u)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return t[0]==this.tic||i[0]==this.tic?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}return(Math.log(r)/Math.LN10/(Math.log(u)/Math.LN10)).toString()};this.computeLogTen=function(n){var i=this._splitArguments(n,this.getParseArgumentSeparator().toString()),f=i.length,r,t,u;if(f>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;if(t=this.getValueFromArg(i[0]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,u=this._parseDouble(t.split(this.tic).join("")),isNaN(u)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return t[0]==this.tic||this._isCellReference(i[0])?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}if(u<=0){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[4].toString()}return(this._computeMath(t,Math.log)/Math.LN10).toString()};this.computePI=function(n){if(this.getEnableFormulaErrorValidation()){var t=this.formulaErrorStringCheck(n,FormulaArgumentType.None);return this.formulaErrorStrings[this._invalid_arguments]==t?t:Math.PI.toString()}return Math.PI.toString()};this.computeProduct=function(n){var f=1,r,t,o=!0,u,s,i,c,h,e;if(this.adjustRangeArg(n),u=this.splitArgsPreservingQuotedCommas(n),n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(s=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(s)>-1))return s;for(i=0;i<u.length;i++)if(c=u[i],u[i].indexOf(":")>-1)for(h=this.getCellsFromArgs(c),e=0;e<h.length;e++){try{t=this.getValueFromArg(h[e]);t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t}catch(l){return l}if(t.length>0){if(r=this._parseDouble(t),isNaN(r)){if(this.getErrorStrings().indexOf(t)>-1)return t;if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[1].toString()}f=f*r;o=!1}}else{try{t=this.getValueFromArg(u[i]);t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t}catch(l){return l}if(t.length>0){if(r=this._parseDouble(t),isNaN(r)){if(this.getErrorStrings().indexOf(t)>-1)return t;if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return t==this.tic||this._isCellReference(u[i])?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}f=f*r;o=!1}}return o?"0":f.toString()};this.computeSecant=function(n){var e=this.splitArgsPreservingQuotedCommas(n),r,u,t,f,i;if(n=this.getValueFromArg(n),r=e.length,r!=1){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(n[0]==this.tic&&n[length-1]==this.tic&&(n=n.split(this.tic).join("")),n==this._string_empty){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}if(u=this.computeIsNumber(n),u==this.trueValueStr){if(t=n,t=t.indexOf("u")?t.split("u").join(""):t,f=parseFloat(t),f>=134217728){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}}else{if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[5].toString()}return i=this._computeMath(n,Math.cos),(i!="#NUM!"||i!="#VALUE!")&&(i=(1/parseFloat(i)).toString()),i};this.computeSecanth=function(n){var o=this.splitArgsPreservingQuotedCommas(n),u,r,f,t,e,i;if(n=this.getValueFromArg(n),u=o.length,u!=1){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;if(n[0]==this.tic&&n[length-1]==this.tic&&(n=n.split(this.tic).join("")),n==this._string_empty){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}if(n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,n=n.split(this.tic).join(""),f=this.computeIsNumber(n),f==this.trueValueStr&&(t=n,t=t.includes("u")?t.replace("u",""):t,e=parseFloat(t),e>=134217728)){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}return n=n.includes("-")?n.replace("-",""):n,i=this._computeMath(n,Math.cosh),(i!="#NUM!"||i!="#VALUE!")&&(i=(1/parseFloat(i)).toString()),i};this.computeSeriessum=function(n){var t=this.splitArgsPreservingQuotedCommas(n),r,f,e,i;if(t.length!=4||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}for(r=0;r<=2;r++)if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(t[r],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1))return f;if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(t[3],FormulaArgumentType.Text),this.getErrorStrings().indexOf(e)>-1))return e;var h=0,c=0,l=0,o=0,u=0,s;if(s=t[3].indexOf(";")>-1?this._splitArguments(t[3].split(this.tic).join(""),";"):this.getCellsFromArgs(t[3].split(this.tic).join("")),t[0]==""||t[1]==""||t[2]==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[0].toString()}for(h=this._parseDouble(this.getValueFromArg(t[0])),c=this._parseDouble(this.getValueFromArg(t[1])),l=this._parseDouble(this.getValueFromArg(t[2])),i=0;i<s.length;i++){if(o=this._parseDouble(this.getValueFromArg(s[i])),isNaN(o)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[1].toString()}u=u+o*Math.pow(h,c+i*l)}if(isNaN(u)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[4].toString()}return u.toString()};this.computeSin=function(n){var i=this.splitArgsPreservingQuotedCommas(n),t;if(i.length>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(t=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(t)>-1)?t:(n=this.getValueFromArg(n),n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,this._computeMath(n,Math.sin).toString())};this.computeSinh=function(n){var i=this._splitArguments(n,this.getParseArgumentSeparator().toString()),e=i.length,r,t,u,f;if(e>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;if(t=this.getValueFromArg(i[0]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,u=this._parseDouble(t.split(this.tic).join("")),isNaN(u)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return t[0]==this.tic||this._isCellReference(i[0])?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}return f=Math.exp(u),(f-1/f)/2};this.computeSqrt=function(n){var i=this._splitArguments(n,this.getParseArgumentSeparator().toString()),r=i.length,t;if(r>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(t=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(t)>-1)?t:(n=this.getValueFromArg(n),n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,this._computeMath(n,Math.sqrt).toString())};this.computeSubTotal=function(n){var f=!1,t=this._string_empty,e,r=0,u=this._splitArguments(n,this.getParseArgumentSeparator().toString()),s=u.length,o,i;if(s<2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(o=this.formulaErrorStringCheck(u[0],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(o)>-1))return o;if(t=this._isCellReference(u[0])?this.getValueFromArg(u[0]):u[0].toString(),e=this._parseDouble(t),isNaN(e)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[5].toString()}if(r=parseInt(e.toString()),(r<1||r>11)&&(r<101||r>111))return this.getErrorStrings()[1].toString();if(f)return"0";t=n.substring(u[0].toString().length+1);f=!0;i=this._string_empty;switch(r){case 1:case 101:i=this.computeAverage(t);break;case 2:case 102:i=this.computeCount(t);break;case 3:case 103:i=this.computeCounta(t);break;case 4:case 104:i=this.computeMax(t);break;case 5:case 105:i=this.computeMin(t);break;case 6:case 106:i=this.computeProduct(t);break;case 7:case 107:i=this.computeStdev(t);break;case 8:case 108:i=this.computeStdevp(t);break;case 9:case 109:i=this.computeSum(t);break;case 10:case 110:i=this.computeVar(t);break;case 11:case 111:i=this.ComputeVarp(t);break;default:i=this.getErrorStrings()[1].toString()}return f=!1,i};this.computeSumif=function(n){var b=this.splitArgsPreservingQuotedCommas(n),ut=b.length,tt,e,s,i,t,c,l,nt,r,u;if(ut!=2&&ut!=3||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&((tt=this.formulaErrorStringCheck(b[0],FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(tt)>-1)||ut==3&&(tt=this.formulaErrorStringCheck(b[2],FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(tt)>-1)))return tt;var p=b[0],f=b[1].split(this.tic).join(this._string_empty),k=this.token_equal;f[0]!=this.tic[0]&&"=><".indexOf(f[0])==-1&&this._isCellReference(f)&&(f=this.getValueFromArg(f));var h=ut==2?p:b[2],w=this.getCellsFromArgs(p),o=this.getCellsFromArgs(h),a=w.length;if(a>o.length){var d=h.indexOf(":"),ft=p.indexOf(":"),et=this.rowIndex(p.substr(0,ft)),it=this.colIndex(p.substr(0,ft)),g=this.rowIndex(p.substr(ft+1)),rt=this.colIndex(p.substr(ft+1));if(d>-1){var st=this.rowIndex(h.substr(0,d)),ot=this.colIndex(h.substr(0,d)),y=this.rowIndex(h.substr(d+1)),v=this.colIndex(h.substr(d+1));st!=y?y+=a-o.length:ot!=v&&(v+=a-o.length);st==et&&y==g?v+=rt-it:ot==it&&v==rt?y+=g-et:st==et&&y<=g&&ot==v?(y=g,v+=rt-it):(y=g,v+=rt-it-(v-ot));h=h.substr(0,d+1)+RangeInfo.getAlphaLabel(v)+y.toString()}else{var ht=0,ct=0,lt=string.Empty;ht=this.RowIndex(h);ct=this.ColIndex(h);ht+=g-et;ct+=rt-it;lt=RangeInfo.GetAlphaLabel(ct);h=h+":"+lt+ht}o=this.getCellsFromArgs(h)}for(e=[],s=!1,e[0]=0,f=f.split(this.tic).join(""),numer=f.split(this._parseArgumentSeparator),l=f.length>0&&f[0]==this.tic[0]?1:0,nt=this.minValue,f=numer,r=0;r<numer.length;r++)if(f[r].startsWith(">=")?(f[r]=this._substring(numer[r],l+2,numer[r].length-2-2*l),numer[r]=this._parseDouble(f[r],nt),k=this.token_greatereq):f[r].startsWith("<=")?(f[r]=this._substring(numer[r],l+2,numer[r].length-2-2*l),numer[r]=this._parseDouble(f[r],nt),k=this.token_lesseq):f[r].startsWith("<")?(f[r]=this._substring(numer[r],l+1,numer[r].length-1-2*l),numer[r]=this._parseDouble(f[r],nt),k=this.token_less):f[r].startsWith(">")?(f[r]=this._substring(numer[r],l+1,numer[r].length-1-2*l),numer[r]=this._parseDouble(f[r],nt),k=this.token_greater):f[r].startsWith("=")&&(f[r]=this._substring(numer[r],l+1,numer[r].length-1-2*l),numer[r]=this._parseDouble(f[r],nt),k=this.token_equal),!s)switch(k){case this.token_equal:for(u=0;u<a;++u)t=this.getValueFromArg(w[u]),i=t,numer.length>1?(t.split(this.tic).join("")==f[r]||numer[r]==i&&i!=this._string_empty)&&(t=o[u],t=this.getValueFromArg(t.split(this.tic).join("")),i=this._parseDouble(t.split(this.tic).join("").split("$").join("")),this.computeFunctionLevel==0?isNaN(i)||(e[0]+=i,s=!0):isNaN(i)||e.push(i)):(t.split(this.tic).join("")==f[r]||i==numer[r])&&(t=o[u],t=this.getValueFromArg(t.split(this.tic).join("")),i=this._parseDouble(t.split(this.tic).join("").split("$").join("")),isNaN(i)||(e[0]+=i));if(s)break;break;case this.token_noequal:for(u=0;u<a;++u)t=this.getValueFromArg(w[u]),i=t!=this._string_empty?this._parseDouble(t):t,numer.length>1?(t.split(this.tic).join("")==f[r]||numer[r]!=i&&i!=this._string_empty)&&(t=o[u],t=this.getValueFromArg(t.split(this.tic).join("")),i=this._parseDouble(t.split(this.tic).join("").split("$").join("")),this.computeFunctionLevel==0?isNaN(i)||(e[0]+=i,s=!0):isNaN(i)||e.push(i)):(t.split(this.tic).join("")==f[r]||i!=numer[r])&&(t=o[u],t=this.getValueFromArg(t.split(this.tic).join("")),i=this._parseDouble(t.split(this.tic).join("").split("$").join("")),isNaN(i)||(e[0]+=i));if(s)break;break;case this.token_greatereq:for(u=0;u<a;++u)t=this.getValueFromArg(w[u]),i=t!=this._string_empty?this._parseDouble(t):t,c=t.localeCompare(f[r]),numer.length>1?c>=0&&!isNaN(i)&&i>=numer[r]&&i!=this._string_empty&&(t=o[u],t=this.getValueFromArg(t.split(this.tic).join("")),i=this._parseDouble(t.split(this.tic).join("").split("$").join("")),this.computeFunctionLevel==0?isNaN(i)||(e[0]+=i,s=!0):isNaN(i)||e.push(i)):(c>=0||i>=numer[r])&&(t=o[u],t=this.getValueFromArg(t.split(this.tic).join("")),i=this._parseDouble(t.split(this.tic).join("").split("$").join("")),isNaN(i)||(e[0]+=i));if(s)break;break;case this.token_greater:for(u=0;u<a;++u)t=this.getValueFromArg(w[u]),i=t!=this._string_empty?this._parseDouble(t):t,c=t.localeCompare(f[r]),numer.length>1?c>0&&!isNaN(i)&&i>numer[r]&&i!=this._string_empty&&(t=o[u],t=this.getValueFromArg(t.split(this.tic).join("")),i=this._parseDouble(t.split(this.tic).join("").split("$").join("")),this.computeFunctionLevel==0?isNaN(i)||(e[0]+=i,s=!0):isNaN(i)||e.push(i)):(c>0||i>numer[r])&&(t=o[u],t=this.getValueFromArg(t.split(this.tic).join("")),i=this._parseDouble(t.split(this.tic).join("").split("$").join("")),isNaN(i)||(e[0]+=i));if(s)break;break;case this.token_less:for(u=0;u<a;++u)t=this.getValueFromArg(w[u]),i=t!=this._string_empty?this._parseDouble(t):t,c=t.localeCompare(f[r]),numer.length>1?c<0&&!isNaN(i)&&i<numer[r]&&i!=this._string_empty&&(t=o[u],t=this.getValueFromArg(t.split(this.tic).join("")),i=this._parseDouble(t.split(this.tic).join("").split("$").join("")),this.computeFunctionLevel==0?isNaN(i)||(e[0]+=i,s=!0):isNaN(i)||e.push(i)):(c<0||i<numer[r])&&(t=o[u],t=this.getValueFromArg(t.split(this.tic).join("")),i=this._parseDouble(t.split(this.tic).join("").split("$").join("")),isNaN(i)||(e[0]+=i));if(s)break;break;case this.token_lesseq:for(u=0;u<a;++u)t=this.getValueFromArg(w[u]),i=t!=this._string_empty?this._parseDouble(t):t,c=t.localeCompare(f[r]),numer.length>1?c<=0&&!isNaN(i)&&i<=numer[r]&&i!=this._string_empty&&(t=o[u],t=this.getValueFromArg(t.split(this.tic).join("")),i=this._parseDouble(t.split(this.tic).join("").split("$").join("")),this.computeFunctionLevel==0?isNaN(i)||(e[0]+=i,s=!0):isNaN(i)||e.push(i)):(c<=0||i<=numer[r])&&(t=o[u],t=this.getValueFromArg(t.split(this.tic).join("")),i=this._parseDouble(t.split(this.tic).join("").split("$").join("")),isNaN(i)||(e[0]+=i));if(s)break}return e.toString()};this.computeTan=function(n){return this._computeMath(n,Math.tan).toString()};this.computeTrunc=function(n){var r=this.splitArgsPreservingQuotedCommas(n),o=r.length,t=0,f,i,u,e,s;if(o>2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1))return f;if(o==2&&(t=this.getValueFromArg(r[1]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,t=this._parseDouble(t)),i=this.getValueFromArg(r[0]),i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,u=this._parseDouble(i.split(this.tic).join("")),isNaN(u)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return i[0]==this.tic||this._isCellReference(r[0])?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}return e=Math.pow(10,t),s=u<0?-1:1,(s*Math.floor(e*Math.abs(u))/e).toString()};this.computeLognormOdist=function(n){var t=this.splitArgsPreservingQuotedCommas(n),s=t.length,o,r,e,u,f,i,h,c;if(s!=4||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(o=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(o)>-1))return o;for(f=0,i=0;i<s;++i)t[i]=this.getValueFromArg(t[i]).split(this.tic).join(this._string_empty),t[i]=t[i].split(this.tic).join("").toUpperCase()=="TRUE"?"1":t[i].split(this.tic).join("").toUpperCase()=="FALSE"?"0":t[i];if(r=this._parseDouble(t[0]),e=this._parseDouble(t[1]),u=this._parseDouble(t[2]),isNaN(r)||isNaN(e)||isNaN(u)){if(this._rethrowLibraryComputationExceptions)throw"#Value! Passsed value is non - numeric";return this.getErrorStrings()[1].toString()}if(r<=0||u<=0){if(this._rethrowLibraryComputationExceptions)throw"#Num! Passsed value is incorrect";return this.getErrorStrings()[4].toString()}return t[3]=="0"?(h=0,c=0,f=Math.exp(-(Math.pow(Math.log(r)-e,2)/(2*Math.pow(u,2)))),f=f/(r*u*Math.sqrt(2*Math.PI))):f=this._standardNormalCumulativeDistribution((Math.log(r)-e)/u),f.toString()};this.computeLognormOinv=function(n){var t=this.splitArgsPreservingQuotedCommas(n),s=t.length,f,r,e,u,o,i;if(s!=3||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1))return f;for(o=0,i=0;i<s;++i)t[i]=this.getValueFromArg(t[i]),t[i]=t[i]==null||t[i]==""?"0":t[i],t[i]=t[i].split(this.tic).join("")=="TRUE"?"1":t[i].split(this.tic).join("")=="FALSE"?"0":t[i];if(r=this._parseDouble(t[0]),e=this._parseDouble(t[1]),u=this._parseDouble(t[2]),!isNaN(r)&&!isNaN(e)&&!isNaN(u)){if(r<=0||r>=1||u<=0){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}o=Math.exp(this._normalinv(r,e,u))}return o.toString()};this.computeNormOinv=function(n){var t=this.splitArgsPreservingQuotedCommas(n),s=t.length,e,r,o,u,f,i;if(s!=3||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;for(f=0,i=0;i<s;++i)t[i]=this.getValueFromArg(t[i]).split(this.tic).join(this._string_empty),t[i]=t[i]==null||t[i]==""?"0":t[i],t[i]=t[i].split(this.tic).join("")=="TRUE"?"1":t[i].split(this.tic).join("")=="FALSE"?"0":t[i];if(r=this._parseDouble(t[0]),o=this._parseDouble(t[1]),u=this._parseDouble(t[2]),!isNaN(r)&&!isNaN(o)&&!isNaN(u)){if(r>=0&&r<=0||u<=0){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments]+this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}f=this._normalCumulativeDistributionFunctionInverse(r,o,u)}return f<=0?this.formulaErrorStrings[this._iterations_dont_converge]:f.toString()};this.computeNormOdist=function(n){var t=this.splitArgsPreservingQuotedCommas(n),e=t.length,o,u,f,r,s,h,i;if(e!=4||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(o=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(o)>-1))return o;for(s=0,h=0,i=0;i<e;++i)t[i]=this.getValueFromArg(t[i]),t[i]=t[i]==null||t[i]==""?"0":t[i],t[i]=t[i].split(this.tic).join("").toUpperCase()=="TRUE"?"1":t[i].split(this.tic).join("").toUpperCase()=="FALSE"?"0":t[i];if(u=this._parseDouble(t[0]),f=this._parseDouble(t[1]),r=this._parseDouble(t[2]),!isNaN(u)&&!isNaN(f)&&!isNaN(r)){if(r<=0){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}e!=3&&(s=this._parseDouble(t[3]));h=s==0?this._normalProbabilityDensity(u,f,r):this._normaldist(u,f,r)}return h.toString()};this.computeNormOsODist=function(n){var t=this.splitArgsPreservingQuotedCommas(n),r=t.length,u,f,e,i,o;if(r!=1&&r!=2){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}for(f=0,e=0,i=0;i<r;++i)t[i]=this.getValueFromArg(t[i]).split(this.tic).join(this._string_empty);if(u=this._parseDouble(t[0]),isNaN(u)){if(this._rethrowLibraryComputationExceptions)throw"#VALUE! Passed argument value is non numeric";return this.getErrorStrings()[1].toString()}return r==2&&(f=t[1]==this.trueValueStr?1:0,o=0,o=this._parseDouble(t[1]),isNaN(o)||(f=1)),e=f==0?this._standardNormalProbabilityDensityFunction(u):this._standardNormalCumulativeDistributionFunction(u),e.toString()};this.computeNormOsOInv=function(n){var r=this.splitArgsPreservingQuotedCommas(n),f=r.length,t,u,i;if(f!=1){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}for(u=0,i=0;i<f;++i)r[i]=this.getValueFromArg(r[i]).split(this.tic).join(this._string_empty);if(t=this._parseDouble(r[0]),isNaN(t)){if(this._rethrowLibraryComputationExceptions)throw"#VALUE! Passed argument value is non numeric";return this.getErrorStrings()[1].toString()}if(t<=0||t>=1){if(this._rethrowLibraryComputationExceptions)throw"#NUM! Passed argument value doesnot match with in range level";return this.getErrorStrings()[4].toString()}return u=this._standardNormalCumulativeDistributionFunctionInverse(t),u.toString()};this.computePermut=function(n){var t=this.splitArgsPreservingQuotedCommas(n),h=t.length,r,u,f,e,i;if(h!=2||n==""){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;if(e=0,t[0]=this.getValueFromArg(t[0]),t[0]=t[0]==""||t[0]==null?"0":t[0],t[0]=t[0].split(this.tic).join("")=="TRUE"?"1":t[0].split(this.tic).join("")=="FALSE"?"0":t[0],t[1]=this.getValueFromArg(t[1]),t[1]=t[1]==""||t[1]==null?"0":t[1],t[1]=t[1].split(this.tic).join("")=="TRUE"?"1":t[1].split(this.tic).join("")=="FALSE"?"0":t[1],u=this._parseDouble(this.getValueFromArg(t[0])),f=this._parseDouble(this.getValueFromArg(t[1])),isNaN(u)||isNaN(f))return this.formulaErrorStrings[this._invalid_arguments];var c=parseInt(f+.1),s=parseInt(u+.1),o=1;for(i=s-c+1;i<=s;++i)o=o*i;return e=o,e.toString()};this.computePermutationA=function(n){var t=this.splitArgsPreservingQuotedCommas(n),f=t.length,u,i,r,e,o,s;if(f!=2||n==""){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1)?u:(e=0,t[0]=this.getValueFromArg(t[0]),t[0]=t[0]==""||t[0]==null?"0":t[0],t[0]=t[0].split(this.tic).join("")=="TRUE"?"1":t[0].split(this.tic).join("")=="FALSE"?"0":t[0],t[1]=this.getValueFromArg(t[1]),t[1]=t[1]==""||t[1]==null?"0":t[1],t[1]=t[1].split(this.tic).join("")=="TRUE"?"1":t[1].split(this.tic).join("")=="FALSE"?"0":t[1],i=this._parseDouble(t[0]),r=this._parseDouble(t[1]),isNaN(i)||isNaN(r))?this.formulaErrorStrings[this._invalid_arguments]:(o=r+.1,s=i+.1,Math.pow(i,r).toString())};this.computeStandardize=function(n){var t=this.splitArgsPreservingQuotedCommas(n),s=t.length,u,f,e,r,o,i;if(s!=3||n=="")return this.formulaErrorStrings[this._wrong_number_arguments];if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1))return u;for(r=0,o=0,i=0;i<s;++i)t[i]=this.getValueFromArg(t[i]),t[i]=t[i]==""||t[i]==null?"0":t[i],t[i]=t[i].split(this.tic).join("")=="TRUE"?"1":t[i].split(this.tic).join("")=="FALSE"?"0":t[i];if(f=this._parseDouble(t[0]),e=this._parseDouble(t[1]),r=this._parseDouble(t[2]),!isNaN(f)&&!isNaN(e)&&!isNaN(r)){if(r<=0){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}o=(f-e)/r}return o.toString()};this.computeBinomOdist=function(n){var t=this.splitArgsPreservingQuotedCommas(n),h=t.length,e,r,f,u,o,s,i;if(h!=4||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;for(o=0,s=0,i=0;i<h;++i)t[i]=this.getValueFromArg(t[i]).split(this.tic).join(this._string_empty),t[i]=t[i]==null||t[i]==""?"0":t[i],t[i]=this._stripTics0(t[i]),t[i]=t[i].split(this.tic).join("")=="TRUE"?"1":t[i].split(this.tic).join("")=="FALSE"?"0":t[i];if(r=this._parseDouble(t[0]),f=this._parseDouble(t[1]),u=this._parseDouble(t[2]),isNaN(r)||isNaN(f)||isNaN(u)){if(this._rethrowLibraryComputationExceptions)throw"#VALUE! Passed value is nonnumeric";return this.getErrorStrings()[1].toString()}if(r<0||r>f||u<0||u>1){if(this._rethrowLibraryComputationExceptions)throw"#NAME! Passed argument value is not equal to minimum par value";return this.getErrorStrings()[4].toString()}return o=this._parseDouble(t[3]),s=o==0?this._comb(r,f)*Math.pow(u,r)*Math.pow(1-u,f-r):this._binomdist(f,r,u),s.toString()};this.computeChisqOinvOrt=function(n){var t=this.splitArgsPreservingQuotedCommas(n),o=t.length,f,r,u,e,i;if(o!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1))return f;for(e=0,i=0;i<o;++i)t[i]=this.getValueFromArg(t[i]),t[i]=t[i]==null||t[i]==""?"0":t[i],t[i]=t[i].split(this.tic).join("")=="TRUE"?"1":t[i].split(this.tic).join("")=="FALSE"?"0":t[i];if(r=this._parseDouble(t[0]),u=this._parseDouble(t[1]),!isNaN(r)&&!isNaN(u)){if(r<=0||r>1||u<1){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}if(r==1)return"0";e=this._chiinv(r,u)}return e.toString()};this.computeChisqOdistOrt=function(n){var i=this.splitArgsPreservingQuotedCommas(n),o=i.length,e,u,r,f,t,s;if(o!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;for(f=0,t=0;t<o;++t)i[t]=this.getValueFromArg(i[t]),i[t]=i[t]==null||i[t]==""?"0":i[t],i[t]=i[t].split(this.tic).join("")=="TRUE"?"1":i[t].split(this.tic).join("")=="FALSE"?"0":i[t];if(u=this._parseDouble(i[0]),r=this._parseDouble(i[1]),!isNaN(u)&&!isNaN(r)){if(u<0||r<1){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}if(u==0)return r.toString();for(r==1&&this._excelLikeComputations,f=1-this._chidist(u,r),t=0;t<f.toString().length;t++);s=Math.round(f)}return f.toString()};this.computeFOdist=function(n){var t=this.splitArgsPreservingQuotedCommas(n),s=t.length,e,i;if(s!=4||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;var r,u=0,f=0,o=0;for(i=0;i<s;++i)t[i]=this.getValueFromArg(t[i]).split(this.tic).join(this._string_empty),t[i]=t[i].split(this.tic).join("")=="TRUE"?"1":t[i].split(this.tic).join("")=="FALSE"?"0":t[i];if(r=this._parseDouble(t[0]),u=this._parseDouble(t[1]),f=this._parseDouble(t[2]),isNaN(r)||isNaN(u)||isNaN(f)){if(this._rethrowLibraryComputationExceptions)throw"#VALUE! Passed argument value is incorrect";return this.getErrorStrings()[1].toString()}if(u<1||f<1||r[0]=="-"){if(this._rethrowLibraryComputationExceptions)throw"#NUM! Passed argument value is incorrect";return this.getErrorStrings()[4].toString()}return t[3]==1?o=this._fCumulativeDensity(r,u,f):t[3]==0&&(o=this._fProbabilityDensity(r,u,f)),o.toString()};this.computeGammaln=function(n){var r=this.splitArgsPreservingQuotedCommas(n),i,t;if(r.length>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(i)>-1))return i;if(n=this.getValueFromArg(n),n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,t=0,t=this._parseDouble(n),!isNaN(t)&&t>1)t=this._gammaln(t);else{if(t==1)return"0";if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}return t.toString()};this.computeConfidenceOnorm=function(n){var t=this.splitArgsPreservingQuotedCommas(n),s=t.length,o,r,f,e,u,i;if(s!=3||n==""){if(this._rethrowLibraryComputationExceptions)throw"Wrong number of arguments";return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(o=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(o)>-1))return o;for(u=0,i=0;i<s;++i)t[i]=this.getValueFromArg(t[i]).split(this.tic).join(this._string_empty),t[i]=t[i]==null||t[i]==""?"0":t[i],t[i]=t[i].split(this.tic).join("")=="TRUE"?"1":t[i].split(this.tic).join("")=="FALSE"?"0":t[i];if(r=this._parseDouble(t[0]),f=this._parseDouble(t[1]),e=this._parseDouble(t[2]),!isNaN(r)&&!isNaN(f)&&!isNaN(e)){if(r<=0||r>=1||f<=0||e<1){if(this._rethrowLibraryComputationExceptions)throw"Passed argument value is different from minimum par";return this.getErrorStrings()[4].toString()}u=this._excelLikeComputations?this._normalinv(1-r+r/2,0,1):this._newnormalinv(1-r+r/2);u=u*f/Math.sqrt(e)}return u.toString()};this.computeExponOdist=function(n){var t=this.splitArgsPreservingQuotedCommas(n),s=t.length,e,u,r,f,o,i,h;if(s!=3||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;for(f=0,o=0,i=0;i<s;++i)t[i]=this.getValueFromArg(t[i]),t[i]=t[i]==null||t[i]==null?"0":t[i],t[i]=t[i].split(this.tic).join("")=="TRUE"?"1":t[i].split(this.tic).join("")=="FALSE"?"0":t[i];if(u=this._parseDouble(t[0]),r=this._parseDouble(t[1]),isNaN(u)||isNaN(r)){if(this._rethrowLibraryComputationExceptions)throw"Passed argument value is non-numerical";return this.getErrorStrings()[1].toString()}if(u<0||r<=0){if(this._rethrowLibraryComputationExceptions)throw"Passed argument value is below or equal to 0";return this.getErrorStrings()[4].toString()}return f=t[2]==this.trueValueStr?1:0,h=this._parseDouble(t[2]),isNaN(h)||(f=0),o=f==0?r*Math.exp(-r*u):1-Math.exp(-r*u),o.toString()};this.computeFisher=function(n){var f=this.splitArgsPreservingQuotedCommas(n),e=f.length,r,t,u,i;if(e!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;if(u=0,i=this.getValueFromArg(f[0]),i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,t=this._parseDouble(i),!isNaN(t)&&t>-1&&t<1)u=.5*Math.log((1+t)/(1-t));else{if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}return u.toString()};this.computeFisherInv=function(n){var e=this.splitArgsPreservingQuotedCommas(n),o=e.length,i,r,u,t,f;if(o!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(i)>-1)?i:(u=0,t=this.getValueFromArg(e[0]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,r=this._parseDouble(t),isNaN(r)||(f=Math.exp(2*r),u=(f-1)/(f+1)),u.toString())};this.computeGammalnOPrecise=function(n){var r=this.splitArgsPreservingQuotedCommas(n),u=r.length,i,t;if(u!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(i)>-1))return i;if(n=this.getValueFromArg(n),n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,t=0,t=this._parseDouble(n.toString()),!isNaN(t)&&t>1)t=this._gammaln(t);else{if(t==1)return"0";if(t<=0){if(this._rethrowLibraryComputationExceptions)throw"Passed Argument value is less than or equal to minimum value 0";return this.getErrorStrings()[4].toString()}}return t.toString()};this.computeLarge=function(n){var t=this.splitArgsPreservingQuotedCommas(n),h=t.length,r,u,f,i;if(h!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()){if(r=this.formulaErrorStringCheck(t[0],FormulaArgumentType.Text),this.getErrorStrings().indexOf(r)>-1)return r;if(t[0][0]==this.tic)return this.getErrorStrings()[4].toString();if(u=this.formulaErrorStringCheck(t[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1)return u}if(i=this.getValueFromArg(t[1]),i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,f=this._parseDouble(i),isNaN(f))return this.formulaErrorStrings[this._invalid_arguments];var e=f,o=this._getDoubleArray(t[0]),s=o.length;return e<1||e>s?this.formulaErrorStrings[this._invalid_arguments]:(o.sort(function(n,t){return isNaN(n)||isNaN(t)?n>t?1:-1:n-t}),o[s-e].toString())};this.computeSmall=function(n){var i=this.splitArgsPreservingQuotedCommas(n),s=i.length,u,f,t,e;if(s!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()){if(u=this.formulaErrorStringCheck(i[0],FormulaArgumentType.Text),this.getErrorStrings().indexOf(u)>-1)return u;if(f=this.formulaErrorStringCheck(i[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1)return f}if(t=this.getValueFromArg(i[1]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,e=this._parseDouble(t),isNaN(e))return this.formulaErrorStrings[this._invalid_arguments];var o=e,r=this._getDoubleArray(i[0]),h=r.length;return o<1||o>r.length?this.formulaErrorStrings[this._invalid_arguments]:(r.sort(function(n,t){return isNaN(n)||isNaN(t)?n<t?1:-1:t-n}),r[h-o].toString())};this.computeCounta=function(n){var r=0,f,h=this.splitArgsPreservingQuotedCommas(n),t,u,o,e,s,i;if(n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}for(u=0;u<h.length;u++)if(t=h[u],t.indexOf(":")>-1)for(o=this.getCellsFromArgs(t),e=0;e<o.length;e++){try{f=this.getValueFromArg(o[e]).split(this.tic).join(this._string_empty)}catch(c){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return c}f.length>0&&r++}else if(t=="")r++;else if(this._isLetter(t[0]))f=this.getValueFromArg(t),f.length>0&&r++;else if(t.indexOf(this.getParseArgumentSeparator())>-1)for(s=this.splitArgsPreservingQuotedCommas(t.split(this.tic).join("")),i=0;i<s.length;i++){if(i[0]!=this.tic&&this._isCellReference(s[i]))if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this.invalid_arguments];else return this.formulaErrorStrings[this.invalid_arguments];(i.length>0||i=="")&&r++}else u.length>0&&r++;return r.toString()};this.computeAverage=function(n){var i=0,e=0,r,t,f=[],h=this.splitArgsPreservingQuotedCommas(n),c,o,l,s,u;if(h.length<1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(c=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(c)>-1))return c;for(o=0;o<h.length;o++)if(f=h[o],f.indexOf(":")>-1)for(l=this.getCellsFromArgs(f),s=0;s<l.length;s++){try{t=this.getValueFromArg(l[s]).split(this.tic).join("");t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t}catch(a){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();else return this.getErrorStrings()[4].toString()}if(t.length>0&&!isNaN(t))if(r=this._parseDouble(t),isNaN(r)){if(this.getErrorStrings().indexOf(t)==-1)return t;if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[5].toString();else return this.getErrorStrings()[5].toString()}else i=Number(i)+Number(r),e++}else{try{t=this.getValueFromArg(f).split(this.tic).join(this._string_empty);t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t;u=this.getValueFromArg(f);u=u.split(this.tic).join("")=="TRUE"?"1":u.split(this.tic).join("")=="FALSE"?"0":u}catch(a){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();else return this.getErrorStrings()[4].toString()}if(t.length>0)if(r=this._parseDouble(t),isNaN(r))if(u[0]==this.tic)if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[1].toString();else return this.getErrorStrings()[1].toString();else if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[5].toString();else return this.getErrorStrings()[5].toString();else i=Number(i)+Number(r),e++}return e>0&&(i=Number(i)/Number(e)),i.toString()};this.computeAverageA=function(n){var i=this.splitArgsPreservingQuotedCommas(n),e,t,h,u,r,o,c,f,s;if(i.length<1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(e)>-1))return e;if(t=this._string_empty,h=0,i==this.trueValueStr||i==this.falseValueStr)return u=i==this.trueValueStr,u?+u:+u;for(r=0;r<i.length;r++)if(i[r].indexOf(this.getParseArgumentSeparator().toString())>-1)for(o=this.splitArgsPreservingQuotedCommas(i[r].split(this.tic).join(this._string_empty)),c=0,f=0;f<o.length;f++)s=o[f],t=t+s+this.getParseArgumentSeparator().toString(),i[c]=s;else this._isCellReference(i[r])?t+=i[r]+this.getParseArgumentSeparator().toString():t=this._parseDouble(i[r],h)?t+i[r]+this.getParseArgumentSeparator().toString():t+"0"+this.getParseArgumentSeparator().toString();return t[t.length-1]==","&&(t=t.substring(0,t.length-1)),this.computeAverage(t)};this.computeMax=function(n){var r=this.minValue,u,t,i,f,e,s,o;if(this.adjustRangeArg(n),f=this.splitArgsPreservingQuotedCommas(n),f==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}for(e=0;e<f.length;e++)if(i=f[e],i.indexOf(":")>-1)for(s=this.getCellsFromArgs(i),o=0;o<s.length;o++){try{t=this.getValueFromArg(s[o]).split(this.tic).join(this._string_empty)}catch(h){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return h}if(t.length>0)if(u=this._parseDouble(t),isNaN(u)){if(this.getErrorStrings().indexOf(t)!=-1)return t}else r=Math.max(r,u)}else{try{if(!this._isCellReference(i)&&i[0]==this.tic){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[1].toString();return this.getErrorStrings()[1].toString()}t=i==this._string_empty?"0":this.getValueFromArg(i);t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t}catch(h){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return h}if(t.length>0)if(u=this._parseDouble(t),isNaN(u)){if(this.getErrorStrings().indexOf(t)!=-1)return t}else r=Math.max(r,u)}return r!=this.minValue?r.toString():this._string_empty};this.computeMaxa=function(n){var f=this.minValue,i,t,u,o,r,s,e;if(this.adjustRangeArg(n),u=this.splitArgsPreservingQuotedCommas(n),n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(o=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(o)>-1))return o;for(r=0;r<u.length;r++)if(u[r].indexOf(":")>-1)for(s=this.getCellsFromArgs(u[r]),e=0;e<s.length;e++){try{t=this.getValueFromArg(s[e]);t=t.split(this.tic).join("").toUpperCase()=="TRUE"?"1":t.split(this.tic).join("").toUpperCase()=="FALSE"?"0":t}catch(h){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();}if(t.length>0){if(i=0,t.toUpperCase()==this.trueValueStr)i=1;else{if(this.getErrorStrings().indexOf(t)>-1)return t;i=this._parseDouble(t)}f=Math.max(f,i)}}else{try{if(u[r].startsWith(this.tic)||u[r]!=""){if(t=this.getValueFromArg(u[r]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,this.getErrorStrings().indexOf(t)>-1){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return t}t==""&&(t="0")}else t="0"}catch(h){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();}if(t.length>0){if(i=0,t.toUpperCase()==this.trueValueStr)i=1;else{if(this.getErrorStrings().indexOf(t)>-1)return t;i=this._parseDouble(t)}f=Math.max(f,i)}}return f!=this.minValue?f.toString():this._string_empty};this.computeMedian=function(n){var r,t,i,u;if(n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(r)>-1)?r:(t=this._getDoubleArray(n),t.sort(function(n,t){return isNaN(n)||isNaN(t)?n>t?1:-1:n-t}),i=parseInt((t.length/2).toString()),u="",t.length%2==1?t[i].toString():((t[i]+t[i-1])/2).toString())};this.computeMin=function(n){var r=this.maxValue,u,t,f,s,h,e,i,c,o;if(this.adjustRangeArg(n),f=this.splitArgsPreservingQuotedCommas(n),n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(s=n.split(";"),s.length>1){for(i=0;i<s.length;i++)for(h=this.splitArgsPreservingQuotedCommas(s[i]),o=0;o<h.length;o++)if(e=this.getValueFromArg(h[o]),e=e.split(this.tic).join("")=="TRUE"?"1":e.split(this.tic).join("")=="FALSE"?"0":e,e.length>0)if(u=this._parseDouble(e),isNaN(u)){if(this.getErrorStrings().indexOf(e)==-1)return e}else r=Math.min(r,u);if(r!=this.maxValue)return r.toString()}for(i=0;i<f.length;i++)if(f[i].indexOf(":")>-1)for(c=this.getCellsFromArgs(f[i]),o=0;o<c.length;o++){try{t=this.getValueFromArg(c[o])}catch(l){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return l}if(t.length>0)if(u=this._parseDouble(t),isNaN(u)){if(this.getErrorStrings().indexOf(t)!=-1)return t}else r=Math.min(r,u)}else{try{if(!this._isCellReference(f[i])&&f[i][0]==this.tic){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[1].toString();return this.getErrorStrings()[1].toString()}t=f[i]==this._string_empty?"0":this.getValueFromArg(f[i]);t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t;t==""&&this._isCellReference(f[i])&&(t="0")}catch(l){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return l}if(t.length>0)if(u=this._parseDouble(t),isNaN(u)){if(this.getErrorStrings().indexOf(t)!=-1)return t}else r=Math.min(r,u)}return r!=this.maxValue?r.toString():this._string_empty};this.computeMina=function(n){var u=this.maxValue,i,t,o,s,h,c,f,r,l,e;if(this.adjustRangeArg(n),o=this.splitArgsPreservingQuotedCommas(n),s=n.split(";"),n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(h=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(h)>-1))return h;if(s.length>1){for(r=0;r<s.length;r++)for(c=this.splitArgsPreservingQuotedCommas(s[r]),e=0;e<c.length;e++)if(f=this.getValueFromArg(c[e]),f.length>0){if(i=0,f.toUpperCase()==this.trueValueStr)i=1;else{if(this.getErrorStrings().indexOf(f)==-1)return f;f=f.split(this.tic).join("")=="TRUE"?"1":f.split(this.tic).join("")=="FALSE"?"0":f;i=this._parseDouble(f)}u=Math.min(u,i)}if(u!=this.maxValue)return u.toString()}for(r=0;r<o.length;r++)if(o[r].indexOf(":")>-1)for(l=this.getCellsFromArgs(o[r]),e=0;e<l.length;e++){try{t=this.getValueFromArg(l[e]);t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t}catch(a){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return a}if(t.length>0){if(i=0,t.toUpperCase()==this.trueValueStr)i=1;else if(t.toUpperCase()==this.falseValueStr)i=0;else{if(this.getErrorStrings().indexOf(t)!=-1)return t;i=this._parseDouble(t)}u=Math.min(u,i)}}else{try{o[r].startsWith(this.tic)||o[r]!=""?(t=this.getValueFromArg(o[r]),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,t==""&&(t="0")):t="0"}catch(a){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return a}if(t.length>0){if(i=0,t.toUpperCase()==this.trueValueStr)i=1;else{if(this.getErrorStrings().indexOf(t)>-1)return t;i=this._parseDouble(t)}u=Math.min(u,i)}}return u!=this.maxValue?u.toString():this._string_empty};this.computePercentrankInc=function(n){var i=this.splitArgsPreservingQuotedCommas(n),l=i.length,f,u,o,e,r,s,h,t,c,a,v;if(l!=2&&l!=3||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(i[0]!=""&&i[1]==""||i[1]!=""&&i[0]==""){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[0].toString();return this.getErrorStrings()[0].toString()}if(this.getEnableFormulaErrorValidation()){if(f=this.formulaErrorStringCheck(i[0],FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(f)>-1)return f;for(t=1;t<l;t++)if(f=this.formulaErrorStringCheck(i[t],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1)return f}if(o=3,e=this.getValueFromArg(i[1]),e==""&&(e="0"),u=this._parseDouble(e),isNaN(u)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_arguments];return this.formulaErrorStrings[this._invalid_arguments]}if(u==0,l==3&&(i[2]=i[2].split(this.tic).join("")=="TRUE"?"1":i[2].split(this.tic).join("")=="FALSE"?"0":i[2],e=this.getValueFromArg(i[2]),o=this._parseDouble(e),isNaN(o)||o<1)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}for(r=this._getDoubleArray(i[0]),s=r.length,r.sort(),h=1,t=0;t<s;++t)if(r[t]==u){for(c=0;c+t<s&&r[c+t]==u;)c++;h=(t-1)/(t+s-t-c-1);t>0&&r[t-1]<u&&(a=t/(s-1),h=a+(h-a)*(1-(u-r[t-1])/(r[t]-r[t-1])));break}return v="0."+parseInt(o.toString()),h.toString()};this.computeRankOEq=function(n){var i=this.splitArgsPreservingQuotedCommas(n),s=i.length,f,e,r,t,h,c,v,o;if(s!=2&&s!=3||n=="")return this.formulaErrorStrings[this._wrong_number_arguments];if(this.getEnableFormulaErrorValidation()&&((f=this.formulaErrorStringCheck(i[0],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1)||(f=this.formulaErrorStringCheck(i[1],FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(f)>-1)))return f;if(e=0,t=this.getValueFromArg(i[0]),i[0]==this._string_empty||i[1]==this._string_empty||!this._isCellReference(i[1]))return this.getErrorStrings()[4].toString();if(r=this._parseDouble(t),!isNaN(r)){if(h=0,s==3){if(this.getEnableFormulaErrorValidation()&&(c=this.formulaErrorStringCheck(i[2],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(c)>-1))return c;t=this.getValueFromArg(i[2]);t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t;t==this._string_empty?t="0":this._parseDouble(t)>1&&(t="1");h=this._parseDouble(t)}var l=i[1],u=0,a=!1;if(l.indexOf(":")==-1)return this.getErrorStrings()[4].toString();if(l.indexOf(":")>-1){for(v=this.getCellsFromArgs(l),o=0;o<v.length;o++){try{t=this._getDoubleArray(v[o])}catch(y){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return y}u=this._parseDouble(t.toString());isNaN(u)||(h==1?u<r?e+=1:u==r&&(a=!0):u>r?e+=1:u==r&&(a=!0))}a&&(e+=1)}}return e.toString()};this.computePercentile=function(n){var u=this.splitArgsPreservingQuotedCommas(n),c=u.length,f,t,h,r,e,o,s,i;if(c!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&((f=this.formulaErrorStringCheck(u[0],FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(f)>-1)||(f=this.formulaErrorStringCheck(u[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1)))return f;if(h=this.getValueFromArg(u[1]),t=this._parseDouble(h),isNaN(t)&&(t<0||t>1))return this.formulaErrorStrings[this._invalid_arguments];for(r=this._getDoubleArray(u[0]),e=r.length,r.sort(function(n,t){return isNaN(n)||isNaN(t)?n<t?1:-1:n-t}),o=1/(e-1),s=r[e-1],i=0;i<e-1;++i)if((i+1)*o>t){t=(t-i*o)/o;s=r[i]+t*(r[i+1]-r[i]);break}return s.toString()};this.computePoissonODist=function(n){var r=this.splitArgsPreservingQuotedCommas(n),c=r.length,o,u,s,i,e,f,h,t;if(c!=3)return this.formulaErrorStrings[this._wrong_number_arguments];for(s=0,i=0,t=0;t<c;++t)r[t]=this.getValueFromArg(r[t]);if(o=this._parseDouble(r[0]),u=this._parseDouble(r[1]),!isNaN(o)&&!isNaN(u))if(s=r[2]==this.trueValueStr?1:0,e=o,s==0){for(f=1,t=2;t<=e;++t)f*=t;i=Math.exp(-u)*Math.pow(u,e)/f}else{for(f=1,i=0,h=1,t=0;t<=e;++t)i+=h/f,f*=t+1,h*=u;i=Math.exp(-u)*i}return i.toString()};this.computeWeiBullODist=function(n){var i=this.splitArgsPreservingQuotedCommas(n),o=i.length,s,u,t,r,f,h,e,c;if(o!=4||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(s=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(s)>-1))return s;for(f=0,h=!0,e=0;e<o;++e)i[e]=this.getValueFromArg(i[e]).split(this.tic).join(this._string_empty);if(u=this._parseDouble(i[0]),t=this._parseDouble(i[1]),r=this._parseDouble(i[2]),isNaN(u)||isNaN(t)||isNaN(r)){if(this._rethrowLibraryComputationExceptions)throw"Passed argument value is non-numerical";return this.getErrorStrings()[1].toString()}if(u<0||t<=0||r<=0){if(this._rethrowLibraryComputationExceptions)throw"Passed argument value is below 0";return this.getErrorStrings()[4].toString()}if(t.toString().length>=5&&r.toString().length>=3){if(this._rethrowLibraryComputationExceptions)throw"Passed argument length exceeded the minimum length";return this.getErrorStrings()[4].toString()}return o!=3&&(f=i[3]==this.trueValueStr?1:0,c=0,c=this._parseDouble(i[3]),isNaN(c)||(f=1)),h=f==1?!0:!1,f=h?1-Math.exp(-Math.pow(u/r,t)):Math.pow(u,t-1)*Math.exp(-Math.pow(u/r,t))*t/Math.pow(r,t),f.toString()};this.computeFOinvOrt=function(n){var t=this.splitArgsPreservingQuotedCommas(n),s=t.length,o,r,u,f,e,i;if(s!=3){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(o=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(o)>-1))return o;for(e=0,i=0;i<s;++i)t[i]=this.getValueFromArg(t[i]),t[i]=t[i]==null||t[i]==""?"0":t[i],t[i]=t[i].split(this.tic).join("")=="TRUE"?"1":t[i].split(this.tic).join("")=="FALSE"?"0":t[i];if(r=this._parseDouble(t[0]),u=this._parseDouble(t[1]),f=this._parseDouble(t[2]),!isNaN(r)&&r>0&&r<1&&!isNaN(u)&&!isNaN(f)){if((r<0||r>1||u<1||f<1)&&this.getRethrowLibraryComputationExceptions())throw"#NUM! Passed Argument value is less than or equal to minimum value";e=this._finv(r,u,f)}if(e<=0){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.getErrorStrings()[4].toString()}return e.toString()};this.computeTOdist=function(n){var t=this.splitArgsPreservingQuotedCommas(n),o=t.length,e,r,i,f,u;if(o!=3||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;for(i=0,f=0,u=0;u<o;++u)t[u]=this.getValueFromArg(t[u]).split(this.tic).join(this._string_empty);if(t[0]=t[0]==null||t[0]==""?"0":t[0],t[0]=t[0].split(this.tic).join("")=="TRUE"?"1":t[0].split(this.tic).join("")=="FALSE"?"0":t[0],t[1]=t[1]==null||t[1]==""?"0":t[1],t[1]=t[1].split(this.tic).join("")=="TRUE"?"1":t[1].split(this.tic).join("")=="FALSE"?"0":t[1],r=this._parseDouble(t[0]),i=this._parseDouble(t[1]),isNaN(r)||isNaN(i)){if(this._rethrowLibraryComputationExceptions)throw"#VALUE! Passed argument value is incorrect";return this.getErrorStrings()[1].toString()}if(i<1||r[0]=="-"){if(this._rethrowLibraryComputationExceptions)throw"#NUM! Passed argument value is incorrect";return this.getErrorStrings()[4].toString()}return t[2]==this.trueValueStr?f=this._tCumulativeDensity(r,i):t[2]==this.falseValueStr&&(f=this._tProbabilityDensity(r,i)),f.toString()};this.computeAvedev=function(n){var i=0,t,r,h,u=[],f,e,c,o,l,v,s;if(h=this.splitArgsPreservingQuotedCommas(n),n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}for(e=0;e<h.length;e++)if(f=h[e],f.indexOf(":")>-1)for(c=this.getCellsFromArgs(f),o=0;o<c.length;o++){try{t=this.getValueFromArg(c[o]).split(this.tic).join(this._string_empty);t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t}catch(a){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return a}if(t.length>0)if(r=this._parseDouble(t),isNaN(r)){if(this.getErrorStrings().indexOf(t)>0)return t}else i=i+r,u.push(r)}else{try{if(t=this.getValueFromArg(f).split(this.tic).join(this._string_empty),t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,this.getEnableFormulaErrorValidation()&&(l=this.formulaErrorStringCheck(t,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(l)>-1))return l}catch(a){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return a}if(t.length>0)if(r=this._parseDouble(t),isNaN(r)){if(this.getErrorStrings().indexOf(t)==-1)return t}else i=i+r,u.push(r)}if(u.length>0){for(v=i/u.length,i=0,s=0;s<u.length;++s)i=i+Math.abs(u[s]-v);i=i/u.length}return i.toString()};this.computeTOinv=function(n){var t=this.splitArgsPreservingQuotedCommas(n),o=t.length,f,i,r,e,u;if(o!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1))return f;for(r=0,e=0,u=0;u<o;++u)t[u]=this.getValueFromArg(t[u]).split(this.tic).join(this._string_empty);if(t[0]=t[0]==""||t[0]==null?"0":t[0],t[0]=t[0].split(this.tic).join("")=="TRUE"?"1":t[0].split(this.tic).join("")=="FALSE"?"0":t[0],t[1]=t[1]==""||t[1]==null?"0":t[1],t[1]=t[1].split(this.tic).join("")=="TRUE"?"1":t[1].split(this.tic).join("")=="FALSE"?"0":t[1],i=this._parseDouble(t[0]),r=this._parseDouble(t[1]),isNaN(i)||isNaN(r)){if(this._rethrowLibraryComputationExceptions)throw"#VALUE! Passed argument value is incorrect";return this.getErrorStrings()[1].toString()}if(r<1||i<0||i[0]=="-"){if(this._rethrowLibraryComputationExceptions)throw"#NUM! Passed argument value is incorrect";return this.getErrorStrings()[4].toString()}return e=this._tCumulativeDistributionInverse(i,r),e.toString()};this.computeChisqOinv=function(n){var t=this.splitArgsPreservingQuotedCommas(n),o=t.length,f,r,u,e,i;if(o!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1))return f;for(e=0,i=0;i<o;++i)t[i]=this.getValueFromArg(t[i]),t[i]=t[i]==null||t[i]==""?"0":t[i],t[i]=t[i].split(this.tic).join("")=="TRUE"?"1":t[i].split(this.tic).join("")=="FALSE"?"0":t[i];if(r=this._parseDouble(t[0]),u=this._parseDouble(t[1]),!isNaN(r)&&!isNaN(u)){if(r<0||r>=1||u<=0){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}return r==0?"0":(e=this._chiinv(r,u),e.toString())}};this.computeCount=function(n){var u=0,t,f,e=new Date,r,i,s,o;if(n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}for(r=this.splitArgsPreservingQuotedCommas(n),i=0;i<r.length;i++)if(r[i].indexOf(":")>-1)for(s=this.getCellsFromArgs(r[i].split(this.tic).join("")),o=0;o<s.length;o++){try{t=this.getValueFromArg(s[o]);t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t}catch(h){if(this._rethrowLibraryComputationExceptions)throw this.getLibraryComputationException();return this.getErrorStrings()[4].toString()}if(t.length>0){if(t==this.formulaErrorStrings[19]){if(this.geLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[19]}f=this._parseDouble(t);e=new Date(Date.parse(t));isNaN(f)&&e.toString()=="Invalid Date"||this.getErrorStrings().indexOf(t)!=-1||u++}}else{try{r[i]!=this._string_empty||r[i][0]==this.tic||u++;t=this.getValueFromArg(r[i].split(this.tic).join(""));t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t}catch(h){if(this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();throw this.getErrorStrings()[4].toString();}if(t.length>0){if(t[0]==this.formulaErrorStrings[19]){if(this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.formulaErrorStrings[19]}f=this._parseDouble(t);e=new Date(Date.parse(t));isNaN(f)&&e.toString()=="Invalid Date"||this.getErrorStrings().indexOf(t)!=-1||u++}}return u.toString()};this.computeMod=function(n){var r=this.splitArgsPreservingQuotedCommas(n),u,t,i;if(this._isArrayFormula&&n.length>2&&(r[1]=r[r.length-1]),r.length!=2||n==this._string_empty&&!this._isArrayFormula){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1))return u;t=r[0];i=r[1];t=this.getValueFromArg(t).split(this.tic).join(this._string_empty);i=this.getValueFromArg(i).split(this.tic).join(this._string_empty);t=t=="TRUE"?"1":t=="FALSE"||t==this._string_empty?"0":t;i=i=="TRUE"?"1":i=="FALSE"||i==this._string_empty?"0":i;var f=parseFloat(t),e=parseFloat(i),o=0;if(!isNaN(f)&&!isNaN(e)){if(o=f-e*Math.floor(f/e),this.computeIsError(o)==this.trueValueStr){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[3].toString()}return o.toString()}};this.computeRadians=function(n){var u=this.splitArgsPreservingQuotedCommas(n),i,r,t;if(u.length>1||u==this._string_empty){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(i)>-1)?i:(r=0,t=this.getValueFromArg(n).split(this.tic).join(this._string_empty),n=t=="TRUE"?"1":t=="FALSE"||t==this._string_empty?"0":t,n=this._parseDouble(n),isNaN(n))?this.formulaErrorStrings[this._invalid_arguments]:(r=Math.PI*n/180,r.toString())};this.computeOdd=function(n){var f=this.splitArgsPreservingQuotedCommas(n),i,t,r,u;if(f.length>1||f==this._string_empty){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(i)>-1)?i:(n=this.getValueFromArg(n).split(this.tic).join(this._string_empty),n=n=="TRUE"?"1":n=="FALSE"||n==this._string_empty?"0":n,t=this._parseDouble(n),r=0,isNaN(t)||(u=t==0?1:t<0?-1:1,t=Math.abs(t),(parseInt(t)!=t||t==0)&&(t=parseInt(t)+1),r=t%2==0?u*(t+1):u*t),r.toString())};this.computeRand=function(n){var i=this.splitArgsPreservingQuotedCommas(n),t=this.formulaErrorStringCheck(n,FormulaArgumentType.None);return this.formulaErrorStrings[this._invalid_arguments]==t?t:Math.random().toString()};this.computeRandBetween=function(n){var t=this.splitArgsPreservingQuotedCommas(n),h=t.length,e,i,r,u,f,o,s;if(h!=2||t==this._string_empty){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;if(i=this.getValueFromArg(t[0]).split(this.tic).join(this._string_empty),r=this.getValueFromArg(t[1]).split(this.tic).join(this._string_empty),this._isCellReference(t[0])&&i==""?i="0":this._isCellReference(t[1])&&r==""&&(r="0"),i==null||i==""||r==null||r==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[0].toString()}return(u=this._parseDouble(i),f=this._parseDouble(r),isNaN(u)||isNaN(f))?this.getErrorStrings()[1].toString():u>f?this.getErrorStrings()[4].toString():(o=parseInt(u),s=parseInt(f),Math.floor(Math.random()*(s-o+1)+o).toString())};this.computeCountif=function(n){return this.computeCountIFFunctions(n,!0)};this.computeCountIFFunctions=function(n,t){var a=this.splitArgsPreservingQuotedCommas(n),nt=a.length,rt=0,b=!1,y=[],p=[],k=[],tt=[],d,ut,h,r,u,o,c,s,ft,et;for(i=0;i<nt;i++)y.push(a[i]),i++,p.push(a[i]);if(d=this.formulaErrorStringCheck(a[0],FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(d)>-1&&d==this.getErrorStrings()[1]||a[0]==""||!this._isCellReference(a[0]))return this.formulaErrorStrings[this._invalid_arguments];if(this.getErrorStrings().indexOf(d)>-1)return d;if(ut=this.getCellsFromArgs(y[0]),nt<2&&y.length==p.length&&!t)return this.formulaErrorStrings[this._wrong_number_arguments];if(y.length!=p.length&&this.getErrorStrings()[1].toString(),nt!=2&&nt!=3&&t){if(this._rethrowLibraryComputationExceptions)throw new ArgumentException(this.FormulaErrorStrings[this._wrong_number_arguments]);return this.formulaErrorStrings[this._wrong_number_arguments]}for(v=0;v<p.length;v++){if(h=this.token_equal,r=p[v],r[0]!=this.tic[0]&&"=><".indexOf(r[0])==-1&&(r=this.getValueFromArg(r)),v!=y.length-1||t||(b=!0),t&&(b=!0),r.length<1&&t)return"0";if(u=r.length>0&&r[0]==this.tic[0]?1:0,o=Number.MIN_VALUE,r.substring(u).startsWith(">=")?(r=r.substr(u+2,r.length-2-2*u),h=this.token_greatereq):r.substring(u).startsWith("<=")?(r=r.substr(u+2,r.length-2-2*u),h=this.token_lesseq):r.substring(u).startsWith("<>")?(r=r.substr(u+2,r.length-2-2*u),h=this.token_noequal):r.substring(u).startsWith("<")?(r=r.substr(u+1,r.length-1-2*u),h=this.token_less):r.substring(u).startsWith(">")?(r=r.substr(u+1,r.length-1-2*u),h=this.token_greater):r.substring(u).startsWith("=")&&(r=r.substr(u+1,r.length-1-2*u)),r=r.split(this.tic).join(this._string_empty),o=this._parseDouble(r),c=!isNaN(o),s=this.getCellsFromArgs(y[v]),s.length!=ut.length)return this.getErrorStrings()[1].toString();if(s[0]==this.getErrorStrings()[5]){if(this._rethrowLibraryComputationExceptions)throw new ArgumentException(this.formulaErrorStrings[this._bad_index]);return this.getErrorStrings()[5].toString()}var ot=s.length,e,f,g=0,l=!1,w="=",it=r.indexOf("*"),st=r;for(index=0;index<ot;++index){f=this.getValueFromArg(s[index]);g=f.replace(this.tic,"").toUpperCase().localeCompare(r.toUpperCase());switch(h){case this.token_equal:it=r.indexOf("*");it!=-1&&(r=r.substring(0,it));ft=st.indexOf("*");ft!=-1&&f!=""&&(f=f.replace(this.tic,"").substring(0,it));e=this._parseDouble(f);l=c?!isNaN(e)&&e==o:!(f==this._string_empty)&&f.replace(this.tic,this._string_empty).toUpperCase()==r.toUpperCase();w="=";break;case this.token_noequal:e=this._parseDouble(f);l=c?!isNaN(e)&&e!=o:!(f==this._string_empty)&&f.replace(this.tic,this._string_empty).toUpperCase()!=r.toUpperCase();w="!=";break;case this.token_greatereq:e=this._parseDouble(f);l=c?!isNaN(e)&&e>=o:!(f==this._string_empty)&&g>=0;w=">=";break;case this.token_greater:e=this._parseDouble(f);l=c?!isNaN(e)&&e>o:!(f==this._string_empty)&&g>0;w=">";break;case this.token_less:e=this._parseDouble(f);l=c?!isNaN(e)&&e<o:!(f==this._string_empty)&&g<0;w="<";break;case this.token_lesseq:e=this._parseDouble(f);l=c?!isNaN(e)&&e<=o:!(f==this._string_empty)&&g<=0;w="<="}if(l)if(t&&b||b&&p.length==1)rt++;else if(k.length>0&&v!=0)for(et=k.length,i=0;i<et;i++)ej.isNullOrUndefined(k[i])||this.rowIndex(k[i].toString())!=this.rowIndex(s[index])||(tt.push(s[index]),b&&rt++);else tt.push(s[index])}k=tt;tt=[]}return rt.toString()};this.computeRound=function(n){var u=this.splitArgsPreservingQuotedCommas(n),h=u.length,e,s;if(n==""){if(this._rethrowLibraryComputationExceptions)throw new ArgumentException(this.formulaErrorStrings[this._wrong_number_arguments]);return this.formulaErrorStrings[this._wrong_number_arguments]}if(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1)return e;if(h==1)return n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,this._computeMath(n,Math.round).toString();var f=0,r=0,o=0,t=this.getValueFromArg(u[0]),i=u[1]==""?"0":this.getValueFromArg(u[1]);return t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,t!=""&&i!=""&&(f=this._parseDouble(t),r=this._parseDouble(i),!isNaN(r)&&!isNaN(f)&&r>0?o=f.toFixed(r):(s=Math.pow(10,-r),o=Math.round(f/s)*s)),o.toString()};this.computeRoundDown=function(n){var t=0,u=this.splitArgsPreservingQuotedCommas(n),o=u.length,e,i,r,f;if(n==""){if(this._rethrowLibraryComputationExceptions)throw new ArgumentException(this.formulaErrorStrings[this._wrong_number_arguments]);return this.formulaErrorStrings[this._wrong_number_arguments]}if(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1)return e;if(o==1){if(n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,t=this._parseDouble(this.getValueFromArg(n).split(this.tic).join(this._string_empty)),!isNaN(t))return t=t-.4999999999*Math.sign(t),this.computeRound(String.format("{0}",t))}else if(o!=2)return this.formulaErrorStrings[this._invalid_arguments];return(i=this.getValueFromArg(u[0]),r=u[1]==""?"0":this.getValueFromArg(u[1]),i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,r=r.split(this.tic).join("")=="TRUE"?"1":r.split(this.tic).join("")=="FALSE"?"0":r,f=0,i!=""&&(t=this._parseDouble(i.split(this.tic).join(this._string_empty))),f=this._parseDouble(r.split(this.tic).join(this._string_empty)),t!=Number.NaN)?(t=t-.4999999999*Math.pow(10,-f)*Math.sign(t),this.computeRound(String.format("{0}{1}{2}",t,this._parseArgumentSeparator,f))):this.getErrorStrings()[1].toString()};this.computeRoundUp=function(n){var t=0,f=this.splitArgsPreservingQuotedCommas(n),o=f.length,e;if(n==""){if(this._rethrowLibraryComputationExceptions)throw new ArgumentException(this.formulaErrorStrings[this._wrong_number_arguments]);return this.formulaErrorStrings[this._wrong_number_arguments]}if(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1)return e;if(o==1)return n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,t=this._parseDouble(this.getValueFromArg(n).split(this.tic).join(this._string_empty)),t>0?t+=.4999999999:t<0&&(t-=.4999999999),t=t.toFixed(0),String.format("{0}",t);if(o!=2)return this.formulaErrorStrings[this._invalid_arguments];var i=0,r=this.getValueFromArg(f[0]),u=f[1]==""?"0":this.getValueFromArg(f[1]);return r=r.split(this.tic).join("")=="TRUE"?"1":r.split(this.tic).join("")=="FALSE"?"0":r,u=u.split(this.tic).join("")=="TRUE"?"1":u.split(this.tic).join("")=="FALSE"?"0":u,r!=""&&(t=this._parseDouble(r.split(this.tic).join(this._string_empty))),i=this._parseDouble(u.split(this.tic).join(this._string_empty)),i=Math.ceil(i),i>0?(t>0?t+=.4999999999/Math.pow(10,i):t<0&&(t-=.4999999999/Math.pow(10,i)),t=t.toFixed(i)):(t>0?t=t/Math.pow(10,-i)+.49999:t<0&&(t=t/Math.pow(10,-i)-.49999),t=t.toFixed(0)*Math.pow(10,-i)),String.format("{0}",t)};this.computeMmult=function(n){var r=this.splitArgsPreservingQuotedCommas(n),v,g,y,p,w,b,it,rt,k,d;if(r.length!=2||n==""){if(this._rethrowLibraryComputationExceptions)throw new ArgumentException(this.formulaErrorStrings[this._wrong_number_arguments]);return this.formulaErrorStrings[this._wrong_number_arguments]}if(v=this.formulaErrorStringCheck(n,FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(v)>-1)return v;if(g=0,!this._isCellReference(r[0])&&!this._isCellReference(r[1])&&(!isNaN(this._parseDouble(this._stripTics0(r[0])))||!isNaN(this._parseDouble(this._stripTics0(r[0]))))){if(this._rethrowLibraryComputationExceptions)throw new ArgumentException(this.formulaErrorStrings[this._invalid_arguments]);return this.getErrorStrings()[1].toString()}r[0]=this._stripTics0(r[0]);r[1]=this._stripTics0(r[1]);var nt=this.getCellsFromArgs(r[0]),tt=this.getCellsFromArgs(r[1]),t,i,e,u,s,h,c,o=0;if(u=parseInt(this.computeRows(r[0])),s=parseInt(this.computeColumns(r[0])),h=parseInt(this.computeRows(r[1])),c=parseInt(this.computeColumns(r[1])),u!=c||h!=s){if(this._rethrowLibraryComputationExceptions)throw new ArgumentException(this.formulaErrorStrings[this._invalid_Math_argument]);return this.getErrorStrings()[0].toString()}var ut=u-1,l=[],a=[],f=[];for(t=0;t<=u-1;t++)for(l[t]=[],i=0;i<=s-1;i++){if(l[t][i]=this._parseDouble(this.getValueFromArg(nt[o])),isNaN(l[t][i]))return this.getErrorStrings()[1].toString();o++}for(o=0,t=0;t<=h-1;t++)for(a[t]=[],i=0;i<=c-1;i++){if(a[t][i]=this._parseDouble(this.getValueFromArg(tt[o])),isNaN(a[t][i]))return this.getErrorStrings()[1].toString();o++}for(t=0;t<=u-1;t++)for(f[t]=[],i=0;i<=c-1;i++)for(e=0;e<=h-1;e++)isNaN(f[t][i])&&(f[t][i]=0),f[t][i]+=l[t][e]*a[e][i];return this.cell==""?f[0][0].toString():(y=this.colIndex(this.cell),p=this.rowIndex(this.cell),this._getFormulaArrayBounds(this.cell,u,s),w=this._getFormulaArrayBoundsfirstRowIndex,b=this._getFormulaArrayBoundsfirstColIndex,it=this._getFormulaArrayBoundslastRowIndex,rt=this._getFormulaArrayBoundslastColIndex,k=p-w,d=y-b,f[k][d].toString())};this.computePv=function(n){var t=this.splitArgsPreservingQuotedCommas(n),u=t.length,e,i,l;if(u!=5&&u!=4&&u!=3||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1)return e;var r,o,s=0,h=0,f=0,c=0;for(i=0;i<u;++i)t[i]=this.getValueFromArg(t[i]),t[i]=t[i]==""?"0":t[i],t[i]=t[i].split(this.tic).join("")=="TRUE"?"1":t[i].split(this.tic).join("")=="FALSE"?"0":t[i],t[i]=t[i].split(this.tic).join("");return(r=this._parseDouble(t[0]),o=this._parseDouble(t[1]),s=this._parseDouble(t[2]),r==0)?c:(h=u==4&&!isNaN(t[3])?this._parseDouble(t[3]):0,f=u<=5&&!isNaN(t[4])?this._parseDouble(t[4]):0,isNaN(r)||isNaN(o)||isNaN(s)||isNaN(h)||isNaN(f))?this.formulaErrorStrings[this._invalid_arguments]:(f=Math.abs(f)>.5?1:0,l=Math.pow(1+r,o),c=(s*(1+r*f)*(1-l)-r*h)/(r*l),c.toString())};this.computeNormsDist=function(n){var r=this.splitArgsPreservingQuotedCommas(n),t,i;if(r.length>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return(t=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(t)>-1)?t:(n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,i=n+", 0, 1, "+this.trueValueStr,this.computeNormDist(i))};this.computeNormDist=function(n){var t=this.splitArgsPreservingQuotedCommas(n),l=t.length,a,f,c;if(l!=4){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(a=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(a)>-1)return a;var o=0,s=0,h=0,v=0,y=0;for(f=0;f<l;++f)t[f]=this.getValueFromArg(t[f]);var i=t[0],r=t[1],u=t[2],e=t[3];if(i=i==""&&this._treatStringsAsZero?"0":i,r=r==""&&this._treatStringsAsZero?"0":r,i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,r=r.split(this.tic).join("")=="TRUE"?"1":r.split(this.tic).join("")=="FALSE"?"0":r,u=u.split(this.tic).join("")=="TRUE"?"1":u.split(this.tic).join("")=="FALSE"?"0":u,e=e.split(this.tic).join("")=="TRUE"?"1":e.split(this.tic).join("")=="FALSE"?"0":e,u==""||e==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[4].toString()}if(o=this._parseDouble(i.split(this.tic).join(this._string_empty)),s=this._parseDouble(r.split(this.tic).join(this._string_empty)),h=this._parseDouble(u.split(this.tic).join(this._string_empty)),!isNaN(o)&&!isNaN(s)&&!isNaN(h)){if(l!=3&&(c=0,c=this._parseDouble(t[3]),v=isNaN(c)?t[3].split(this.tic).join(this._string_empty).toUpperCase()==this.trueValueStr?1:t[3].split(this.tic).join(this._string_empty).toUpperCase()==this.falseValueStr?0:-1:c==0?0:1),v==-1){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[1].toString()}y=v==0?this._normaldensity(o,s,h):this._normaldist(o,s,h)}return y.toString()};this.computeSumProduct=function(n){var w=0,h=0,c,r=null,l,u,i,a,v,o,s,p;for(this.adjustRangeArg(n),l=this.splitArgsPreservingQuotedCommas(n),u=0;u<l.length;++u)if(i=l[u],!i.startsWith(this.tic)&&i.indexOf(":")>-1){var t=i.indexOf(":"),f=this.rowIndex(i.substr(0,t)),e=this.rowIndex(i.substr(t+1));if(!(f!=-1||e==-1)==(f==-1||e!=-1)&&this.getErrorStrings()[5].toString(),a=this.colIndex(i.substr(0,t)),v=this.colIndex(i.substr(t+1)),r==null)for(h=(e-f+1)*(v-a+1),r=[],t=0;t<h;++t)r[t]=1;var b=CalcEngine.getSheetFamilyItem(this.grid),y=this._getSheetTokenFromReference(i),k=y==null||y==""?this.grid:b.tokenToParentObject.getItem(y);for(t=0,o=f;o<=e;++o)for(s=a;s<=v;++s){if(c=this.getValueFromParentObject(k,o,s).toString().split(this.tic).join(""),isNaN(c))r[t]=0;else{if(isNaN(r[t]))return this.getErrorStrings()[1].toString();r[t]=r[t]*c}t++}}else{if(p=this.getValueFromArg(i),this.getErrorStrings().indexOf(p)!=-1)return p;if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[1].toString()}for(t=0;t<h;++t)w+=r[t];return w.toString()};this.computeStdev=function(n){var t,i,r,u;if(n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return(t=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(t)>-1)?t:(n=n.split(this.tic).join(""),i=this._getDoubleArray(n),r=i.length,r<2)?this.formulaErrorStrings[this._invalid_arguments]:(u=0,this._sd(i,u).toString())};this.computeVLookUp=function(n){var c=this._isVLookupCachingEnabled(),b=this._isOptimizedMatchesEnabled(),p,i,u,it,e,a,v,rt,f;if(c&&this._lookupTables==null&&(this._lookupTables=new HashTable),p=this.splitArgsPreservingQuotedCommas(n),i=this.getValueFromArg(p[0]),i=i.split(this.tic).join(this._string_empty).toUpperCase(),u=p[1].split('"').join(this._string_empty),u=="#REF!")return u;if(it=this.getValueFromArg(p[2]).split('"').join(this._string_empty),e=0,e=this._parseDouble(it),this.computeIsLogical(it)==this.trueValueStr)e=this._parseDouble(this.computeN(it));else if(isNaN(e)||it=="NaN")return"#N/A";if(e<1)return this.getErrorStrings()[1].toString();var lt=e,k=!0,et=!0;p.length==4&&(k=et=this.getValueFromArg(p[3])==this.trueValueStr||this.getValueFromArg(p[3].split(this.tic).join(this._string_empty))=="1");e=this._parseDouble(i);a=!isNaN(e);v=u.indexOf(":");v==-1&&(u=u+":"+u,v=u.indexOf(":"));var ot=u.substr(0,v).lastIndexOf(this.sheetToken),st=this.grid,at=CalcEngine.getSheetFamilyItem(this.grid);ot>-1&&(this.grid=at.tokenToParentObject.getItem(u.substring(0,ot+1)));var l=this.rowIndex(u.substring(0,v)),w=this.rowIndex(u.substring(v+1)),d=this.colIndex(u.substring(0,v)),ht=this.colIndex(u.substring(v+1));if(!(l!=-1||w==-1)==(l==-1||w!=-1))return this.getErrorStrings()[5].toString();l==-1&&(l=1);d==-1&&(d=1);w==-1&&this.parentObject.getSheet!=undefined&&(w=this.parentObject.getSheet(this.parentObject._getSheetIndex()).usedRange.rowIndex);ht==-1&&this.parentObject.getSheet!=undefined&&(ht=this.parentObject.getSheet(this.parentObject._getSheetIndex()).usedRange.colIndex);var ut=!0,g="",r=null,o=null;c&&(g=d.toString()+"_"+l.toString()+"_"+w.toString()+"_"+this.grid.GetHashCode(),this._lookupTables.containsKey(g)||(b?(rt=new LookUps,rt.setMatchLookUpList(new HashTable),this._lookupTables.add(g,rt)):(rt=new LookUps,this._lookupTables.add(g,rt)),ut=!0),r=this._lookupTables.getItem(g).getLinearLookUpList(),b&&(o=this._lookupTables.getItem(g).getMatchLookUpList()));var s="",nt=l,ct=0,t="",y=0,ft=!0,tt=!1,h=[];if(c&&b&&o.length>0)o.containsKey(i)&&(nt=o.getItem(i),t=i,tt=!0);else for(f=l;f<=w;++f){if(c&&b){t=this.getValueFromParentObject(this.grid,f,d).toString().toUpperCase().split('"').join("");r.indexOf(t)==-1&&r.push(t);b&&!o.containsKey(t)&&o.add(t,f);y=a?this._parseDouble(t):y;(f==w&&o.containsKey(i)||k&&(a?!isNaN(y)&&y>e:t>i))&&(o.containsKey(i)?(nt=o.getItem(i),t=i,tt=!0):ft=!1);ft&&!o.containsKey(i)&&(nt=f);continue}if(!c||f-l>=r.length||ut?(t=this.getValueFromParentObject(this.grid,f,d).toString().toUpperCase().split('"').join(""),h.indexOf(t)==-1&&h.push(t),c&&(r.indexOf(t)==-1&&r.push(t),b&&!o.containsKey(t)&&o.add(t,f))):t=r[f-l],y=a?this._parseDouble(t):y,t==i||k&&(a?!isNaN(y)&&y>e:t>i))if(t.toUpperCase()==i&&(nt=f,k=!0,tt=!0,ct++),ut)ft=!1;else break;ft&&(nt=f);ut=ct==0?!0:!1;k=!0}if(k||t==i){if(r!=null&&r.length>0&&(c||r.sort(),r[0]=r[0]==""?"0":r[0]),h!=null&&h.length>0&&i!=""&&(c||h.sort(),h[0]=h[0]==""?"0":h[0]),!tt&&(!a||a&&h!=null&&h.length>0&&this._parseDouble(h[0].toString())>this._parseDouble(i))||!tt&&c&&a&&r!=null&&this._parseDouble(r[0].toString())>this._parseDouble(i)||!et&&!tt)return this.grid=st,"#N/A";s=this.getValueFromParentObject(this.grid,nt,lt+d-1).toString();s.length>0&&s[0]==CalcEngine.getFormulaCharacter()&&(s=this.parseFormula(s));e=this._parseDouble(s);s.length>0&&s[0]!=this.tic[0]&&isNaN(e)&&(s=this.tic+s+this.tic)}else s="#N/A";return this.grid=st,s};this.computeAsinh=function(n){var r=this.splitArgsPreservingQuotedCommas(n),i,t;if(r.length>1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(i)>-1))return i;if(n=this.getValueFromArg(n),n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,t=this._parseDouble(n),isNaN(t)){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[1].toString();return this.getErrorStrings()[1].toString()}return t=Math.sign(t)*Math.log(Math.abs(t)+Math.sqrt(t*t+1)),t.toString()};this.computeAtanh=function(n){var u=this.splitArgsPreservingQuotedCommas(n),r,t,i;if(u.length>1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;if(n=this.getValueFromArg(n),n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,t=this._parseDouble(n),i=Math.abs(t),!isNaN(t)&&i<1)t=.5*Math.sign(t)*Math.log((1+i)/(1-i));else{if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}return t.toString()};this.computeBase=function(n){var t=this.splitArgsPreservingQuotedCommas(n),u=t.length,h,o,c,p,i,l,a;if(u<2||u>3||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(h=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(h)>-1))return h;var f=0,r=0,e=1;try{t[0]=this.getValueFromArg(t[0]);t[1]=this.getValueFromArg(t[1]);t[0]=t[0]==null||t[0]==""?"0":t[0];t[0]=t[0].split(this.tic).join("")=="TRUE"?"1":t[0].split(this.tic).join("")=="FALSE"?"0":t[0];t[1]=t[1]==null||t[1]==""?"0":t[1];t[1]=t[1].split(this.tic).join("")=="TRUE"?"1":t[1].split(this.tic).join("")=="FALSE"?"0":t[1];u==3&&(t[2]=this.getValueFromArg(t[2]),t[2]=t[2]==null||t[2]==""?"0":t[2],t[2]=t[2].split(this.tic).join("")=="TRUE"?"1":t[2].split(this.tic).join("")=="FALSE"?"0":t[2]);f=parseInt(this.getValueFromArg(t[0]).split(this.tic).join(""));r=parseInt(this.getValueFromArg(t[1]).split(this.tic).join(""));e=u==3?parseInt(this.getValueFromArg(t[2]).split(this.tic).join("")):1}catch(w){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[5].toString();return this.getErrorStrings()[5].toString()}if(o=64,c="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ",f<0||r<2||r>c.length||e<0||e>255){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}for(var v=o-1,s=Math.abs(f),y=new Array(o);s!=0;)p=parseInt(s%r),y[v--]=c[p],s=parseInt(s/r);for(i=y.slice(v+1,o),l=new StringBuilder,a=0;a<e-i.length;a++)l.append("0");return i=l.toString()+i,f<0&&(i="-"+i),i=i.split(",").join(""),i.toString()};this.computeAverageIf=function(n){var w=this.splitArgsPreservingQuotedCommas(n),d=w.length,l=0,g,nt,t,v,f,a,o,y,b,p,rt,k,c,r,i,tt,u,it;if(d!=2&&d!=3||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(g=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(g)>-1))return g;if(nt=w[0],t=w[1],t=t==null||t==""?"0":t,t.length<1)return"0";v=this.token_equal;t[0]!=this.tic[0]&&"=><".indexOf(t[0])==-1&&(t=this.getValueFromArg(t));f=t.length>0&&t[0]==this.tic[0]?1:0;a=Number.MIN_VALUE;t.substring(f).startsWith(">=")?(t=t.substr(f+2,t.length-2-2*f),v=this.token_greatereq):t.substring(f).startsWith("<=")?(t=t.substr(f+2,t.length-2-2*f),v=this.token_lesseq):t.substring(f).startsWith("<>")?(t=t.substr(f+2,t.length-2-2*f),v=this.token_noequal):t.substring(f).startsWith("<")?(t=t.substr(f+1,t.length-1-2*f),v=this.token_less):t.substring(f).startsWith(">")?(t=t.substr(f+1,t.length-1-2*f),v=this.token_greater):t.substring(f).startsWith("=")&&(t=t.substr(f+1,t.length-1-2*f));t=t.split(this.tic).join("");var a=this._parseDouble(t),s=d==2?nt:w[2],h=this.getCellsFromArgs(nt),e=this.getCellsFromArgs(s);if(h[0]==this.getErrorStrings()[5]||e[0]==this.getErrorStrings()[5]){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[5].toString();return this.getErrorStrings()[5].toString()}if(o=h.length,o>e.length&&(y=s.indexOf(":"),y>-1)){if(b=this.rowIndex(s.substring(0,y)),p=this.rowIndex(s.substring(y+1)),!(b!=-1||p==-1)==(b==-1||p!=-1))return this.getErrorStrings()[5].toString();rt=this.colIndex(s.substring(0,y));k=this.colIndex(s.substr(y+1));b!=p?p+=o-e.length:rt!=k&&(k+=o-e.length);s=s.substring(0,y+1)+RangeInfo.getAlphaLabel(k)+p.toString();e=this.getCellsFromArgs(s)}c=0;switch(v){case this.token_equal:if(t.indexOf("*")>-1)for(tt=t.indexOf("*"),t=t.substring(0,tt),u=0;u<o;++u)i=this.getValueFromArg(h[u]),r=this._parseDouble(i),i.split(this.tic).join("").substr(0,tt)!=t&&(isNaN(r)||r!=a)||(i=e[u],i=this.getValueFromArg(i),r=this._parseDouble(i),isNaN(r)||(c+=r,l++));else for(u=0;u<o;++u)i=this.getValueFromArg(h[u]),r=this._parseDouble(i),i.split(this.tic).join("")!=t&&(isNaN(r)||r!=a)||(i=e[u],i=this.getValueFromArg(i),r=this._parseDouble(i),isNaN(r)||(c+=r,l++));break;case this.token_noequal:for(u=0;u<o;++u)i=this.getValueFromArg(h[u]),r=this._parseDouble(i),i.split(this.tic).join("")==t&&(isNaN(r)||r==a)||(i=e[u],i=this.getValueFromArg(i),r=this._parseDouble(i),isNaN(r)||(c+=r,l++));break;case this.token_greatereq:for(u=0;u<o;++u)i=this.getValueFromArg(h[u]),r=this._parseDouble(i),isNaN(r)||r>=a&&(i=e[u],i=this.getValueFromArg(i),r=this._parseDouble(i),isNaN(r)||(c+=r,l++));break;case this.token_greater:for(u=0;u<o;++u)i=this.getValueFromArg(h[u]),r=this._parseDouble(i),isNaN(r)||r>a&&(i=e[u],i=this.getValueFromArg(i),r=this._parseDouble(i),isNaN(r)||(c+=r,l++));break;case this.token_less:for(u=0;u<o;++u)i=this.getValueFromArg(h[u]),r=this._parseDouble(i),isNaN(r)||r<a&&(i=e[u],i=this.getValueFromArg(i),r=this._parseDouble(i),isNaN(r)||(c+=r,l++));break;case this.token_lesseq:for(u=0;u<o;++u)i=this.getValueFromArg(h[u]),r=this._parseDouble(i),isNaN(r)||r<=a&&(i=e[u],i=this.getValueFromArg(i),r=this._parseDouble(i),isNaN(r)||(c+=r,l++))}if(it=c/l,this.computeIsErr(it.toString())==this.trueValueStr){if(this.getRethrowLibraryComputationExceptions())throw new ArgumentException(this.formulaErrorStrings[this._bad_formula]);return this.getErrorStrings()[3].toString()}return it.toString()};this.computeAverageIfS=function(n){for(var w,u,e,h,et,c,s,nt,b,ft,tt,r,i,it,t,rt,k=this.splitArgsPreservingQuotedCommas(n),ut=k.length,a=0,d=[],g=[],o=1;o<ut;o++)d.push(k[o]),o++,g.push(k[o]);if(ut<3&&d.length==g.length)return this.formulaErrorStrings[this._wrong_number_arguments];var l=k[0],y=0,ot=this.getCellsFromArgs(k[0]),f=this.getCellsFromArgs(l),p=!1;for(v=0;v<g.length;v++){if(w=this.token_equal,u=g[v],u[0]!=this.tic[0]&&"=><".indexOf(u[0])==-1&&(u=this.getValueFromArg(u)),v==d.length-1&&(p=!0),e=u.length>0&&u[0]==this.tic[0]?1:0,h=Number.MIN_VALUE,u.substring(e).startsWith(">=")?(u=u.substr(e+2,u.length-2-2*e),w=this.token_greatereq):u.substring(e).startsWith("<=")?(u=u.substr(e+2,u.length-2-2*e),w=this.token_lesseq):u.substring(e).startsWith("<>")?(u=u.substr(e+2,u.length-2-2*e),w=this.token_noequal):u.substring(e).startsWith("<")?(u=u.substr(e+1,u.length-1-2*e),w=this.token_less):u.substring(e).startsWith(">")?(u=u.substr(e+1,u.length-1-2*e),w=this.token_greater):u.substring(e).startsWith("=")&&(u=u.substr(e+1,u.length-1-2*e)),u=u.split(this.tic).join(""),h=this._parseDouble(u),et=!isNaN(h),c=this.getCellsFromArgs(d[v]),c[0]==this.getErrorStrings()[5]||f[0]==this.getErrorStrings()[5])return this.getErrorStrings()[5].toString();if(s=c.length,s>f.length&&(o=l.indexOf(":"),o>1)){if(nt=this.rowIndex(l.substring(0,o)),b=this.rowIndex(l.substring(o+1)),!(nt!=-1||b==-1)==(nt==-1||b!=-1))return this.getErrorStrings()[5].toString();ft=this.colIndex(l.substring(0,o));tt=this.colIndex(l.substr(o+1));nt!=b?b+=s-f.length:ft!=tt&&(tt+=s-f.length);l=l.substring(0,o+1)+RangeInfo.getAlphaLabel(tt)+b.toString();f=this.getCellsFromArgs(l)}switch(w){case this.token_equal:if(u.indexOf("*")>-1)for(it=u.indexOf("*"),u=u.substring(0,it),t=0;t<s;++t)i=this.getValueFromArg(c[t]),r=this._parseDouble(i),i.split(this.tic).join("").substr(0,it)!=u&&(isNaN(r)||r!=h)?f[t]=null:f[t]!=null&&(i=f[t],i=this.getValueFromArg(i),r=this._parseDouble(i),!isNaN(r)&&p&&(y+=r,a++));else for(t=0;t<s;++t)i=this.getValueFromArg(c[t]),r=this._parseDouble(i),i.split(this.tic).join("")!=u&&(isNaN(r)||r!=h)?f[t]=null:f[t]!=null&&(i=f[t],i=this.getValueFromArg(i),r=this._parseDouble(i),!isNaN(r)&&p&&(y+=r,a++));break;case this.token_noequal:for(t=0;t<s;++t)i=this.getValueFromArg(c[t]),r=this._parseDouble(i),i.split(this.tic).join("")==u&&(isNaN(r)||r==h)?f[t]=null:f[t]!=null&&(i=f[t],i=this.getValueFromArg(i),r=this._parseDouble(i),!isNaN(r)&&p&&(y+=r,a++));break;case this.token_greatereq:for(t=0;t<s;++t)i=this.getValueFromArg(c[t]),r=this._parseDouble(i),isNaN(r)||(r>=h?f[t]!=null&&(i=f[t],i=this.getValueFromArg(i),r=this._parseDouble(i),!isNaN(r)&&p&&(y+=r,a++)):f[t]=null);break;case this.token_greater:for(t=0;t<s;++t)i=this.getValueFromArg(c[t]),r=this._parseDouble(i),isNaN(r)||(r>h?f[t]!=null&&(i=f[t],i=this.getValueFromArg(i),r=this._parseDouble(i),!isNaN(r)&&p&&(y+=r,a++)):f[t]=null);break;case this.token_less:for(t=0;t<s;++t)i=this.getValueFromArg(c[t]),r=this._parseDouble(i),isNaN(r)||(r<h?f[t]!=null&&(i=f[t],i=this.getValueFromArg(i),r=this._parseDouble(i),!isNaN(r)&&p&&(y+=r,a++)):f[t]=null);break;case this.token_lesseq:for(t=0;t<s;++t)i=this.getValueFromArg(c[t]),r=this._parseDouble(i),isNaN(r)||(r<=h?f[t]!=null&&(i=f[t],i=this.getValueFromArg(i),r=this._parseDouble(i),!isNaN(r)&&p&&(y+=r,a++)):f[t]=null)}}if(rt=y/a,this.computeIsErr(rt.toString())==this.trueValueStr){if(this.getRethrowLibraryComputationExceptions())throw new ArgumentException(this.formulaErrorStrings[this._bad_formula]);return this.getErrorStrings()[3].toString()}return rt.toString()};this.computeBesselI=function(n){var o=this.splitArgsPreservingQuotedCommas(n),t="",i="",l,f,s,h,c,p,y;if(o.length>2||o.length<=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(t=this.getValueFromArg(o[0]),t=t.split(this.tic).join(""),i=this.getValueFromArg(o[1]),i=i.split(this.tic).join(""),l=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(l)>-1)return l;try{if(this._parseDouble(t)==0)return"0.0";if(this._parseDouble(i)<0){if(this._rethrowLibraryComputationExceptions)throw new ArgumentException(this.formulaErrorStrings[this._bad_formula]);return this.getErrorStrings()[4].toString()}if(t==""||i=="")return this.getErrorStrings()[0].toString();if(this.computeIsText(t)==this.trueValueStr||this.computeIsText(i)==this.trueValueStr)return this.getErrorStrings()[5].toString();if(t==this.trueValueStr||i==this.trueValueStr||t==this.falseValueStr||i==this.falseValueStr)return this.getErrorStrings()[1].toString();if(this._parseDouble(t)<2&&this._parseDouble(t)>1){var a=2/Math.abs(this._parseDouble(t)),r=0,e=0,u=1,v=Math.round(2*(this._parseDouble(i)+Math.sqrt(this._acc*this._parseDouble(i))));for(f=v;f>0;--f)s=e+f*a*u,e=u,u=s,Math.abs(u)>this._bingo&&(r*=this._bigni,u*=this._bigni,e*=this._bigni),h=this.computeRound(f.toString()),c=this.computeRound(i),parseInt(h)==parseInt(c)&&(r=e);return r*=this.besseli0(this._parseDouble(t))/u,r==Number.POSITIVE_INFINITY||isNaN(r)?this.getErrorStrings()[4].toString():(this._parseDouble(t)<0&&parseInt(i)%2==0?-r:r).toString()}var a=2/Math.abs(this._parseDouble(t)),r=0,e=0,u=1,v=Math.round(2*(this._parseDouble(i)+Math.sqrt(this._acc*this._parseDouble(i))));for(f=v;f>0;--f)s=e+f*a*u,e=u,u=s,Math.abs(u)>this._bingo&&(r*=this._bigni,u*=this._bigni,e*=this._bigni),h=this.computeRound(f.toString()),c=this.computeRound(i),h==c&&(r=e);return(r*=this.besseli0(this._parseDouble(t))/u,p=r,this._parseDouble(t)<.9&&this._parseDouble(t)>0)?(y=this.besseli0(this._parseDouble(t)),y.toString()):r==Number.POSITIVE_INFINITY||isNaN(r)?this.getErrorStrings()[4].toString():(this._parseDouble(t)<0&&parseInt(i)%2!=0?-r:r).toString()}catch(w){if(this.getRethrowLibraryComputationExceptions())throw new ArgumentException("The Parameters are not correct");else return this.computeIsText(t)==this.trueValueStr||this.computeIsText(i)==this.trueValueStr?this.getErrorStrings()[5].toString():this.getErrorStrings()[1].toString()}};this.computeBesselJ=function(n){var v=this.splitArgsPreservingQuotedCommas(n),u="",t="",b,p,w;if(v.length>2||v.length<=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(u=this.getValueFromArg(v[0]),u=u.split(this.tic).join(""),t=this.getValueFromArg(v[1]),t=t.split(this.tic).join(""),b=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(b)>-1)return b;var r,y,s,i,h,f,e,a,o,c,k=40,d=1e10,l=1e-10;try{if(this._parseDouble(t)==0)return this.j0(u).toString();if(this._parseDouble(t)==1)return this.j1(u).toString();if(this._parseDouble(t)<0){if(this._rethrowLibraryComputationExceptions)throw new ArgumentException(this.formulaErrorStrings[this._bad_formula]);return this.getErrorStrings()[4].toString()}if(u==""||t=="")return this.getErrorStrings()[0].toString();if(this.computeIsText(u)==this.trueValueStr||this.computeIsText(t)==this.trueValueStr)return this.getErrorStrings()[5].toString();if(u==this.trueValueStr||t==this.trueValueStr||u==this.falseValueStr||t==this.falseValueStr)return this.getErrorStrings()[1].toString();if(s=Math.abs(this._parseDouble(u)),s==0)return"0";if(this._parseDouble(u)<.9&&this._parseDouble(t)<.9)a=2/s,h=this.j0(s.toString()),o=h;else if(s>this._parseDouble(t)){for(a=2/s,h=this.j0(s.toString()),i=this.j1(s.toString()),r=1;r<this._parseDouble(t);r++)f=r*a*i-h,h=i,i=f;o=i}else if(this._parseDouble(u)<2&&this._parseDouble(t)<2){for(a=2/s,y=Math.round(2*(this._parseDouble(t)+parseInt(Math.sqrt(k*this._parseDouble(t)))/2)),c=!1,f=o=e=0,i=1,r=y;r>0;r--)h=r*a*i-f,f=i,i=h,Math.abs(i)>d&&(i*=l,f*=l,o*=l,e*=l),c&&(e+=i),c=!c,p=this.computeRound(r.toString()),w=this.computeRound(t),parseInt(p)<=parseInt(w)&&(o=f);e=2*e-i;o/=e}else{for(a=2/s,y=2*(this._parseDouble(t)+parseInt(Math.sqrt(k*this._parseDouble(t)))/2),c=!1,f=o=e=0,i=1,r=y;r>0;r--)h=r*a*i-f,f=i,i=h,Math.abs(i)>d&&(i*=l,f*=l,o*=l,e*=l),c&&(e+=i),c=!c,p=this.computeRound(r.toString()),w=this.computeRound(t),p==w&&(o=f);e=2*e-i;o/=e}return(this._parseDouble(u)<0&&this._parseDouble(t)%2==1?-o:o).toString()}catch(g){if(this.getRethrowLibraryComputationExceptions())throw new ArgumentException("The Parameters are not correct");else return this.computeIsText(x)==this.trueValueStr||this.computeIsText(t)==this.trueValueStr?this.getErrorStrings()[5].toString():this.getErrorStrings()[1].toString()}};this.computeBesselK=function(n){var o=this.splitArgsPreservingQuotedCommas(n),t="",r="",ft=o.length==2?o.indexOf("u")>-1:!1,g,ut;if(o.length>2||o.length<=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(t=this.getValueFromArg(o[0]),t=t.split(this.tic).join(""),r=this.getValueFromArg(o[1]),r=r.split(this.tic).join(""),g=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(g)>-1)return g;if(ft=o.length==2?this.computeIsNumber(o[1])!=this.trueValueStr:!1,t==""&&r=="")if(this._rethrowLibraryComputationExceptions)throw new ArgumentException("Passed arguments contains invalid argument");else return this.getErrorStrings()[0].toString();if(o.length==2)t=this.getValueFromArg(o[0]),t=t.split(this.tic).join(""),r=this.getValueFromArg(o[1]),r=r.split(this.tic).join("");else if(o.length==1)t=this.getValueFromArg(o[0]),r="0";else return this.formulaErrorStrings[10].toString();var v=0,e=0,b=0,a=0,it=0,k=0,u=0,i=0,w=0,d=0,y=0,l=0,s=0,c=0,p=0,nt=0,tt=0,h=0,f,rt=.57721566490153287;try{if(f=this._parseDouble(r)<0?"-"+r.toString():r,this._parseDouble(t)<0||this._parseDouble(r)<0){if(this.getRethrowLibraryComputationExceptions())throw new ArgumentException(this.formulaErrorStrings[this._bad_formula]);return this.getErrorStrings()[4].toString()}if(t==""||r=="")return this.getErrorStrings()[0].toString();if(this.computeIsText(t)==this.trueValueStr||this.computeIsText(r)==this.trueValueStr)return this.getErrorStrings()[5].toString();if(t==this.trueValueStr||r==this.trueValueStr||t==this.falseValueStr||r==this.falseValueStr)return this.getErrorStrings()[1].toString();if(this._parseDouble(t)<=9.55){if(y=0,w=.25*this._parseDouble(t)*this._parseDouble(t),l=1,s=0,p=1,tt=2/this._parseDouble(t),this._parseDouble(f)>0){for(s=-rt,e=1,f=parseInt(this._parseDouble(f)).toString(),h=1;h<=this._parseDouble(f)-1;h++)s=s+1/e,e=e+1,l=l*e;if(p=tt,this._parseDouble(f)==1)y=1/this._parseDouble(t);else{for(a=l/this._parseDouble(f),b=1,i=a,d=-w,k=1,h=1;h<=this._parseDouble(f)-1;h++)a=a/(this._parseDouble(f)-h),b=b*h,k=k*d,u=a*k/b,i=i+u,p=p*tt;i=i*.5;u=Math.abs(i);y=i*p}}nt=2*Math.log(.5*this._parseDouble(t));c=-rt;this._parseDouble(f)==0?(s=c,u=1):(s=s+1/this._parseDouble(f),u=1/l);i=(c+s-nt)*u;e=1;do u=u*(w/(e*(e+this._parseDouble(f)))),c=c+1/e,s=s+1/(e+this._parseDouble(f)),i=i+(c+s-nt)*u,e=e+1;while(this._parseDouble(Math.abs(u/i))>this.machineepsilon);return i=.5*i/p,this._parseDouble(f)%2!=0&&(i=-i),y=y+i,v=y,v.toString()}if(this._parseDouble(t)>this._parseDouble(Math.log(this.maxrealnumber)))return v=0,v.toString();e=this._parseDouble(f);s=4*e*e;c=1;w=8*this._parseDouble(t);l=1;u=1;i=u;h=0;do{if(d=s-c*c,u=u*d/(l*w),a=Math.abs(u),h>=this._parseDouble(f)&&this._parseDouble(a)>this._parseDouble(it))break;it=a;i=i+u;l=l+1;c=c+2;h=h+1}while(this._parseDouble(Math.abs(u/i))>this.machineepsilon);return ut="-"+t,v=Math.exp(this._parseDouble(ut))*Math.sqrt(Math.PI/(2*this._parseDouble(t)))*i,v.toString()}catch(et){if(this.getRethrowLibraryComputationExceptions())throw new ArgumentException("The Parameters are not correct");else return this.computeIsText(t)==this.trueValueStr||this.computeIsText(r)==this.trueValueStr?this.getErrorStrings()[5].toString():this.getErrorStrings()[1].toString()}};this.computeBesselY=function(n){var r=this.splitArgsPreservingQuotedCommas(n),t="",i="",c=r.length==2?r.indexOf("u")>-1:!1,o,u,f,h,s,e;if(r.length>2||r.length<=1){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(t=this.getValueFromArg(r[0]),t=t.split(this.tic).join(""),i=this.getValueFromArg(r[1]),i=i.split(this.tic).join(""),o=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(o)>-1)return o;if(c=r.length==2?this.computeIsNumber(r[1])!=this.trueValueStr:!1,t==""&&i=="")if(this._rethrowLibraryComputationExceptions)throw new ArgumentException("Passed arguments contains invalid argument");else return this.getErrorStrings()[0].toString();if(r.length==2)t=this.getValueFromArg(r[0]),t=t.split(this.tic).join(""),i=this.getValueFromArg(r[1]),i=i.split(this.tic).join("");else if(r.length==1)t=this.getValueFromArg(r[0]),i="0";else return this.formulaErrorStrings[10].toString();try{if(this._parseDouble(t)<0||this._parseDouble(i)<0){if(this._rethrowLibraryComputationExceptions)throw new ArgumentException(this.formulaErrorStrings[this._bad_formula]);return this.getErrorStrings()[4].toString()}if(t==""||i=="")return this.getErrorStrings()[0].toString();if(this.computeIsText(t)==this.trueValueStr||this.computeIsText(i)==this.trueValueStr)return this.getErrorStrings()[5].toString();if(t==this.trueValueStr||i==this.trueValueStr||t==this.falseValueStr||i==this.falseValueStr)return this.getErrorStrings()[1].toString();if(this._parseDouble(t)==0)return this.y0(this._parseDouble(t)).toString();if(this._parseDouble(i)==1)return this.y1(this._parseDouble(t)).toString();if(this._parseDouble(t)<2&&this._parseDouble(t)>1)return s=2/this._parseDouble(t),u=this.y1(this._parseDouble(t)),f=this.y0(this._parseDouble(t)),u.toString();for(s=2/this._parseDouble(t),u=this.y1(this._parseDouble(t)),f=this.y0(this._parseDouble(t)),e=1;e<this._parseDouble(i);e++)h=e*s*u-f,f=u,u=h;return u.toString()}catch(l){if(this.getRethrowLibraryComputationExceptions())throw new ArgumentException("The Parameters are not correct");else return this.computeIsText(x)==this.trueValueStr||this.computeIsText(nn)==this.trueValueStr?this.getErrorStrings()[5].toString():this.getErrorStrings()[1].toString()}};this.computeText=function(n){var h=this.splitArgsPreservingQuotedCommas(n),f,r,v;if(h.length!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw new ArgumentException(this.formulaErrorStrings[this._wrong_number_arguments]);return this.formulaErrorStrings[this._wrong_number_arguments]}var i=h[0],t=h[1],s=this._dateTime1900,y=this.formulaErrorStringCheck(n,FormulaArgumentType.Text);if(this.getErrorStrings().indexOf(y)>-1)return y;if(i=this.getValueFromArg(i),t=this.getValueFromArg(t),t=t.split(this.tic).join(""),t==this._string_empty)return this._string_empty;if(i==this._string_empty&&t.length>0&&(t.toUpperCase().indexOf("M")>-1||t.toUpperCase().indexOf("D")>-1||t.toUpperCase().indexOf("Y")>-1||t.toUpperCase().indexOf("S")>-1||t.toUpperCase().indexOf("T")>-1)&&(i=s.toString()),f=this._parseDouble(i),isNaN(f)&&this._isDate(new Date(i))!="NaN"&&(f=this._toOADate(new Date(i))),s=Date.parse(i.split(this.tic).join("")),!isNaN(f)||!isNaN(s)){if(t[0]=="["&&this.nativeFormats.indexOf(t)!=-1)return this.tic+i+this.tic;if(t.length>0&&(t.toUpperCase().indexOf("M")>-1||t.toUpperCase().indexOf("D")>-1||t.toUpperCase().indexOf("Y")>-1||t.toUpperCase().indexOf("S")>-1||t.toUpperCase().indexOf("T")>-1)){t=t.split("Y").join("y").split("D").join("d").split("H").join("h").split("S").join("s").split("m").join("M").split("AM/PM").join("tt");for(var u=t.split(""),c=!1,l=!1,w=!1,e=0,p=0,o=0,a=0,e=0;e<u.length;)r=u[e],r=="s"&&u[o]=="M"&&(u[o]="m",u[o-1]=="M"&&(u[o-1]="m")),this._isLetter(r)?(o=e,r=="M"&&(p++,a++)):a>1&&a++,r=="M"&&c&&(u[e]="m",l=!0,w=p==1),r=="h"?c=!0:this._isLetter(r)&&r!="M"&&r!="h"&&!l&&(c=!1,l=!1),e++;t=new String(u);t=t.split(",").join("").split("\n").join(" ");v=this._fromOADate(f);f==0&&(v=s);i=ej.format(v,t,"en-US")}else i=ej.format(f,t,"en-US")}return this.tic+i+this.tic};this.computePow=function(n){var o=0,r,u,t,i,s=this.splitArgsPreservingQuotedCommas(n),f,e;if(s.length!=2||n==this._string_empty){if(this.getRethrowLibraryComputationExceptions())throw new ArgumentException(this.formulaErrorStrings[this._wrong_number_arguments]);return this.formulaErrorStrings[this._wrong_number_arguments]}if(f=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1)return f;if(e=this.splitArgsPreservingQuotedCommas(n),t=this.getValueFromArg(e[0]),i=this.getValueFromArg(e[1]),t==""&&i==""){if(this.getRethrowLibraryComputationExceptions())throw new ArgumentException(this.formulaErrorStrings[this._bad_formula]);return this.getErrorStrings()[4].toString()}if(t=t==""&&this._treatStringsAsZero?"0":t,i=i==""&&this._treatStringsAsZero?"0":i,t=this.getValueFromArg(t).split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,i=this.getValueFromArg(i).split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,r=this._parseDouble(t),u=this._parseDouble(i),!isNaN(r)&&!isNaN(u)){if(r==0&&u<0){if(this.getRethrowLibraryComputationExceptions())throw new ArgumentException(this.formulaErrorStrings[this._bad_formula]);return this.getErrorStrings()[3].toString()}o=Math.pow(r,u)}return o.toString()};this.j0=function(n){var i;if(i=Math.abs(this._parseDouble(n)),i<8){var t=this._parseDouble(n)*this._parseDouble(n),u=57568490574+t*(-13362590354+t*(651619640.7+t*(-11214424.18+t*(77392.33017+t*-184.9052456)))),f=57568490411+t*(1029532985+t*(9494680.718+t*(59272.64853+t*(267.8532712+t*1))));return u/f}var r=8/i,t=r*r,e=i-.785398164,u=1+t*(-.001098628627+t*(2734510407e-14+t*(-2073370639e-15+t*2093887211e-16))),f=-.01562499995+t*(.0001430488765+t*(-6911147651e-15+t*(7621095161e-16-t*934935152e-16)));return Math.sqrt(.636619772/i)*(Math.cos(e)*u-r*Math.sin(e)*f)};this.j1=function(n){var i,t,r,u,f,o,e;return i=Math.abs(this._parseDouble(n)),i<8?(t=this._parseDouble(n)*this._parseDouble(n),r=this._parseDouble(n)*(72362614232+t*(-7895059235+t*(242396853.1+t*(-2972611.439+t*(15704.4826+t*-30.16036606))))),u=144725228442+t*(2300535178+t*(18583304.74+t*(99447.43394+t*(376.9991397+t*1)))),r/u):(f=8/i,o=i-2.356194491,t=f*f,r=1+t*(.00183105+t*(-3516396496e-14+t*(2457520174e-15+t*-240337019e-15))),u=.04687499995+t*(-.0002002690873+t*(8449199096e-15+t*(-88228987e-14+t*105787412e-15))),e=Math.sqrt(.636619772/i)*(Math.cos(o)*r-f*Math.sin(o)*u),this._parseDouble(n)<0&&(e=-e),e)};this.y0=function(n){if(n<8){var t=n*n,r=-2957821389+t*(7062834065+t*(-512359803.6+t*(10879881.29+t*(-86327.92757+t*228.4622733)))),u=40076544269+t*(745249964.8+t*(7189466.438+t*(47447.2647+t*(226.1030244+t*1))));return r/u+.636619772*this.j0(n.toString())*Math.log(n)}var i=8/n,t=i*i,f=n-.785398164,r=1+t*(-.001098628627+t*(2734510407e-14+t*(-2073370639e-15+t*2093887211e-16))),u=-.01562499995+t*(.0001430488765+t*(-6911147651e-15+t*(7621095161e-16+t*-934945152e-16)));return Math.sqrt(.636619772/n)*(Math.sin(f)*r+i*Math.cos(f)*u)};this.y1=function(n){if(n<8){var t=n*n,r=n*(-4900604943e3+t*(127527439e4+t*(-51534381390+t*(734926455.1+t*(-4237922.726+t*8511.937935))))),u=249958057e5+t*(424441966400+t*(3733650367+t*(22459040.02+t*(102042.605+t*(354.9632885+t)))));return r/u+.636619772*(this.j1(n.toString())*Math.log(n)-1/n)}var i=8/n,t=i*i,f=n-2.356194491,r=1+t*(.00183105+t*(-3516396496e-14+t*(2457520174e-15+t*-240337019e-15))),u=.04687499995+t*(-.0002002690873+t*(8449199096e-15+t*(-88228987e-14+t*105787412e-15)));return Math.sqrt(.636619772/n)*(Math.sin(f)*r+i*Math.cos(f)*u)};this.besseli0=function(n){var i=0,u=0,r=0,t={b0:0,b1:0,b2:0};return(this._parseDouble(n)<this._parseDouble(0)&&(n=-n),this._parseDouble(n)<=this._parseDouble(8))?(i=n/2-2,this.besselmfirstcheb(-44153416464793395e-34,t),this.besselmnextcheb(i,33307945188222384e-33,t),this.besselmnextcheb(i,-24312798465479549e-32,t),this.besselmnextcheb(i,17153912855551331e-31,t),this.besselmnextcheb(i,-11685332877993451e-30,t),this.besselmnextcheb(i,76761854986049361e-30,t),this.besselmnextcheb(i,-4856446783111929e-28,t),this.besselmnextcheb(i,295505266312964e-26,t),this.besselmnextcheb(i,-17268262914415555e-27,t),this.besselmnextcheb(i,9675809035373237e-26,t),this.besselmnextcheb(i,-51897956016352627e-26,t),this.besselmnextcheb(i,26598237246823866e-25,t),this.besselmnextcheb(i,-13000250099862481e-24,t),this.besselmnextcheb(i,60469950225419186e-24,t),this.besselmnextcheb(i,-26707938539406119e-23,t),this.besselmnextcheb(i,11173875391201037e-22,t),this.besselmnextcheb(i,-44167383584587505e-22,t),this.besselmnextcheb(i,16448448070728896e-21,t),this.besselmnextcheb(i,-5754195010082104e-20,t),this.besselmnextcheb(i,.00018850288509584165,t),this.besselmnextcheb(i,-.00057637557453858236,t),this.besselmnextcheb(i,.0016394756169413357,t),this.besselmnextcheb(i,-.0043243099950505759,t),this.besselmnextcheb(i,.010546460394594998,t),this.besselmnextcheb(i,-.023737414805899471,t),this.besselmnextcheb(i,.049305284239670712,t),this.besselmnextcheb(i,-.094901097048047639,t),this.besselmnextcheb(i,.17162090152220877,t),this.besselmnextcheb(i,-.3046826723431984,t),this.besselmnextcheb(i,.67679527440947607,t),u=.5*(t.b0-t.b2),Math.exp(n)*u):(r=32/n-2,this.besselmfirstcheb(-72331804878747538e-34,t),this.besselmnextcheb(r,-48305044859441819e-34,t),this.besselmnextcheb(r,446562142029676e-31,t),this.besselmnextcheb(r,34612228676974612e-33,t),this.besselmnextcheb(r,-28276239805165836e-32,t),this.besselmnextcheb(r,-3425485619677219e-31,t),this.besselmnextcheb(r,17725601330565263e-31,t),this.besselmnextcheb(r,38116806693526224e-31,t),this.besselmnextcheb(r,-95548466988283073e-31,t),this.besselmnextcheb(r,-41505693472872222e-30,t),this.besselmnextcheb(r,154008621752141e-28,t),this.besselmnextcheb(r,38527783827421426e-29,t),this.besselmnextcheb(r,7180124451383666e-28,t),this.besselmnextcheb(r,-17941785315068062e-28,t),this.besselmnextcheb(r,-13215811840447713e-27,t),this.besselmnextcheb(r,-31499165279632416e-27,t),this.besselmnextcheb(r,11889147107846439e-27,t),this.besselmnextcheb(r,494060238822497e-24,t),this.besselmnextcheb(r,33962320257083865e-25,t),this.besselmnextcheb(r,2266668990498178e-23,t),this.besselmnextcheb(r,20489185894690638e-23,t),this.besselmnextcheb(r,28913705208347567e-22,t),this.besselmnextcheb(r,68897583469168245e-21,t),this.besselmnextcheb(r,.0033691164782556943,t),this.besselmnextcheb(r,.80449041101410879,t),u=.5*(t.b0-t.b2),Math.exp(n)*u/Math.sqrt(n))};this.besselmfirstcheb=function(n,t){t.b0=n;t.b1=0;t.b2=0};this.besselmnextcheb=function(n,t,i){i.b2=i.b1;i.b1=i.b0;i.b0=n*i.b1-i.b2+t};this.computeFOdistORt=function(n){var t=this.splitArgsPreservingQuotedCommas(n),o=t.length,e,i,h;if(o!=3||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;var f,r=0,u=0,s=0;for(i=0;i<o;++i)t[i]=this.getValueFromArg(t[i]).split(this.tic).join(""),t[i]=t[i]==null||t[i]==""?"0":t[i],t[i]=t[i].split(this.tic).join("")=="TRUE"?"1":t[i].split(this.tic).join("")=="FALSE"?"0":t[i];if(f=this._parseDouble(t[0]),r=this._parseDouble(t[1]),u=this._parseDouble(t[2]),isNaN(f)||isNaN(r)||isNaN(u)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this.invalid_Math_argument];return this._formulaErrorStrings[this._invalid_Math_argument]}if(f<0||r<1||u<1){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this._errorStrings[4].toString()}return h=Math.exp(this._gammaln((r+u)/2)-this._gammaln(r/2)-this._gammaln(u/2)+r/2*Math.log(r/u)),s=1-h*this._fdist(f,parseInt(r.toString()),parseInt(u.toString())),s.toString()};this.computeGeomean=function(n){var i=1,t,u,e=0,f,r,s,o,h;if(n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}for(this.adjustRangeArg(n),f=this.splitArgsPreservingQuotedCommas(n),r=0;r<f.length;r++)if(f[r].indexOf(":")>-1){if(r[0]==this.tic){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}for(s=this.getCellsFromArgs(f[r]),o=0;o<s.length;o++){try{t=this.getValueFromArg(s[o])}catch(c){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return c}if(t.length>0)if(u=this._parseDouble(t),isNaN(u)){if(this.getErrorStrings().indexOf(t)==-1)return t}else e++,i=i*u}}else{try{t=this.getValueFromArg(f[r])}catch(c){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return c}if(t.length>0){if(this.getEnableFormulaErrorValidation()&&(h=this.formulaErrorStringCheck(t,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(h)>-1))return h;if(u=this._parseDouble(t),isNaN(u)){if(this.getErrorStrings().indexOf(t)==-1)return t}else e++,i=i*u}}return e>0&&(i=Math.pow(i,1/e)),i.toString()};this.computeHarmean=function(n){var r=0,t,i,e=0,f,u,s,o,h;if(n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}for(this.adjustRangeArg(n),f=this.splitArgsPreservingQuotedCommas(n),u=0;u<f.length;u++)if(f[u].indexOf(":")>-1){if(u[0]==this.tic){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}for(s=this.getCellsFromArgs(f[u]),o=0;o<s.length;o++){try{t=this.getValueFromArg(s[o])}catch(c){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return c}if(t.length>0)if(i=this._parseDouble(t),isNaN(i)||i==0){if(this.getErrorStrings().indexOf(t)==-1)return t}else e++,r=r+1/i}}else{try{t=this.getValueFromArg(f[u])}catch(c){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return c}if(t.length>0){if(this.getEnableFormulaErrorValidation()&&(h=this.formulaErrorStringCheck(t,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(h)>-1))return h;if(i=this._parseDouble(t),isNaN(i)||i==0){if(this.getErrorStrings().indexOf(t))return t}else e++,r=r+1/i}}return e>0&&(r=e/r),r.toString()};this.computeIntercept=function(n){var f=this.splitArgsPreservingQuotedCommas(n),a=f.length,o,r,u,c,l,e,t;if(a!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(o=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(o)>-1))return o;if(value1=this.getValueFromArg(f[0]),value2=this.getValueFromArg(f[1]),value1.toString().toUpperCase()==this.trueValueStr||value1.toString().toUpperCase()==this.falseValueStr||value2.toString().toUpperCase()==this.trueValueStr||value2.toString().toUpperCase()==this.falseValueStr){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}var s=this._getDoubleArray(f[0]),h=this._getDoubleArray(f[1]),i=h.length;if(i<=0||i!=s.length)return this.formulaErrorStrings[this._wrong_number_arguments];for(r=0,u=0,t=0;t<i;++t)r+=h[t],u+=s[t];for(r=r/i,u=u/i,c=0,l=0,t=0;t<i;++t)e=h[t]-r,c+=e*(s[t]-u),l+=e*e;return(u-c/l*r).toString()};this.computeCountblank=function(n){var e=0,t,i,r,o,u,s,f,h;if(this.adjustRangeArg(n),i=this.splitArgsPreservingQuotedCommas(n),n==""||i.length>1){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(o=this.formulaErrorStringCheck(n,FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(o)>-1))return o;for(u=0;u<i.length;u++)if(r=i[u],r.indexOf(":")>-1)for(s=this.getCellsFromArgs(r),f=0;f<s.length;f++){try{t=this.getValueFromArg(s[f])}catch(c){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();return this.getErrorStrings()[4].toString()}t==this._string_empty&&e++}else{try{t=this.getValueFromArg(i)}catch(c){if(this._rethrowLibraryComputationExceptions&&this.getLibraryComputationException()!=null)throw this.getLibraryComputationException();else return this.getErrorStrings()[4].toString()}if(!this._isCellReference(r)){if(h=this._parseDouble(t),!isNaN(h)||this.getErrorStrings.indexOf(t))if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_arguments].toString();else return this.formulaErrorStrings[this._invalid_arguments].toString();if(t.split(this.tic).join(this._string_empty)==this._string_empty)if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_arguments].toString();else return this.formulaErrorStrings[this._invalid_arguments].toString();else if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[5].toString();else return this.getErrorStrings()[5].toString()}t==this._string_empty&&e++}return e.toString()};this.computeDevsq=function(n){var f,s=this.splitArgsPreservingQuotedCommas(n),r=this._getDoubleArray(n),e=r.length,o,i,u,t;if(n=="")return this.formulaErrorStrings[this._wrong_number_arguments];if(this.getEnableFormulaErrorValidation()&&(o=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(o)>-1))return o;if(r.length<=0){if(this.getLibraryComputationException()!=null)throw this.formulaErrorStrings[this._bad_formula];throw this.getErrorStrings()[4].toString();}for(i=0,t=0;t<e;++t)i=i+r[t];for(i=i/e,u=0,t=0;t<e;++t)f=r[t]-i,u=u+f*f;return u.toString()};this.computeForecast=function(n){var f=this.splitArgsPreservingQuotedCommas(n),b=f.length,a,v,y,o,e,s,h,i,r,u,c,l,t,p,w;if(b!=3||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(a=this.formulaErrorStringCheck(f[0],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(a)>-1))return a;for(t=1;t<=2;t++)if(this.getEnableFormulaErrorValidation()&&(v=this.formulaErrorStringCheck(f[t],FormulaArgumentType.Text),this.getErrorStrings().indexOf(v)>-1))return v;if(e=this.getValueFromArg(f[0]),e=e.split(this.tic).join("")=="TRUE"?"1":e.split(this.tic).join("")=="FALSE"?"0":e,y=this._parseDouble(e),isNaN(y)){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}if(s=this._getDoubleArray(f[1]),h=this._getDoubleArray(f[2]),i=h.length,i<=0||i!=s.length)return this.formulaErrorStrings[this._wrong_number_arguments];for(r=0,u=0,t=0;t<i;++t)r=r+h[t],u=u+s[t];for(r=r/i,u=u/i,c=0,l=0,t=0;t<i;++t)o=h[t]-r,c=c+o*(s[t]-u),l=l+o*o;return p=c/l,w=u-p*r,(w+p*y).toString()};this.computeStdevOp=function(n){var t,i,r;if(n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(t=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(t)>-1))return t;if(i=this._getDoubleArrayA(n),r=i.length,r<2){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_arguments];return this.formulaErrorStrings[this.invalid_arguments]}return this._stdevdotP(i).toString()};this.computeStdevOS=function(n){var t,i,r,u;if(n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(t=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(t)>-1))return t;if(i=this._getDoubleArrayA(n),r=i.length,r<2){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_arguments];return this.formulaErrorStrings[this.invalid_arguments]}return u=0,this._sd(i,u).toString()};this.computeStdeva=function(n){var t,i,r,u;if(n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(t=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(t)>-1)?t:(i=this._getDoubleArrayA(n),r=i.length,r<2)?this.formulaErrorStrings[this._invalid_arguments]:(u=0,this._sd(i,u).toString())};this.computeStdevpa=function(n){var i,r,t,u,f;if(n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(i)>-1)?i:(r=this._getDoubleArrayA(n),t=r.length,t<2)?this.formulaErrorStrings[this._invalid_arguments]:(u=0,f=this._sd(r,u),(f*Math.sqrt(t-1)/Math.sqrt(t)).toString())};this.computeVarp=function(n){var t=this._getDoubleArray(n.split(this.tic).join(this._string_empty)),i=t.length;return((i-1)*this._var(t)/i).toString()};this.computeVara=function(n){var t=this._getDoubleArrayA(n);return this._var(t).toString()};this.computeVarpa=function(n){var t=this._getDoubleArrayA(n),i=t.length;return((i-1)*this._var(t)/i).toString()};this.computeCorrel=function(n){var f=this.splitArgsPreservingQuotedCommas(n),b=f.length,v,y,p,e,o,i,r,u,t,w;if(b!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(v=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(v)>-1))return v;if(y=this.getValueFromArg(f[0]),p=this.getValueFromArg(f[1]),y==null||y==""||p==null||p==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this._errorStrings[1].toString()}if(e=this._getDoubleArray(f[0]),o=this._getDoubleArray(f[1]),i=o.length,i<=0||i!=e.length){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this._errorStrings[0].toString()}for(r=0,u=0,t=0;t<i;++t)r=r+o[t],u=u+e[t];r=r/i;u=u/i;var s=0,h=0,c=0,l=0,a=0;for(t=0;t<i;++t)l=o[t]-r,a=e[t]-u,s=s+l*a,h=h+l*l,c=c+a*a;if(w=(s/Math.sqrt(h*c)).toString(),this.computeIsError(w)==this.trueValueStr){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this._errorStrings[3].toString()}return(s/Math.sqrt(h*c)).toString()};this.computeNegbinomODist=function(n){var t=this.splitArgsPreservingQuotedCommas(n),o=t.length,r,u,f,e,i;if(o!=4)return this.formulaErrorStrings[this._wrong_number_arguments];for(e=0,i=0;i<o;++i)t[i]=this.getValueFromArg(t[i]);return u=this._parseDouble(t[0]),r=this._parseDouble(t[1]),f=this._parseDouble(t[2]),isNaN(u)||isNaN(r)||isNaN(f)||(e=this._negbinomdensity(parseInt(u.toString()),parseInt(r.toString()),f)),e.toString()};this.computePercentileExc=function(n){var r=this.splitArgsPreservingQuotedCommas(n),l=r.length,s,h,t,i,u,e,o,a,f,c;if(l!=2||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()){if(s=this.formulaErrorStringCheck(r[0],FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(s)>-1)return s;if(h=this.formulaErrorStringCheck(r[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(h)>-1)return h}if(i=this.getValueFromArg(r[1]),u=this.getValueFromArg(r[0]),i==null||i==""||u==""||u==null||Boolean(i=="TRUE"||i=="FALSE")||Boolean(u=="TRUE"||u=="FALSE")){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}if(t=this._parseDouble(i),isNaN(t)&&(t<0||t>1))return this.formulaErrorStrings[this._invalid_arguments];if(e=this._parseDouble(i),e==0||e<=0||e>=1){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}if(o=this._getDoubleArray(r[0]),a=o.length,o.sort(function(n,t){return isNaN(n)||isNaN(t)?n<t?1:-1:n-t}),f=o.length,t<=this._parseDouble((1/(f+1)).toString())||t>=this._parseDouble((f/(f+1)).toString())){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}return c=t*(f+1),c.toString()};this.computePercentileOInc=function(n){var f=this.splitArgsPreservingQuotedCommas(n),a=f.length,e,t,r,o,s,u,h,c,l,i;if(a!=2||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&((e=this.formulaErrorStringCheck(f[0],FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(e)>-1)||(e=this.formulaErrorStringCheck(f[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1)))return e;if(r=this.getValueFromArg(f[1]),o=this.getValueFromArg(f[0]),r==null||r==""||o==null||o==""||Boolean(r=="TRUE"||r=="FALSE")||Boolean(o=="TRUE"||o=="FALSE")){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}if(t=this._parseDouble(r),isNaN(t)&&(t<0||t>1))return this.formulaErrorStrings[this._invalid_arguments];if(s=this._parseDouble(r),s==0||s<0||s>1){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}for(u=this._getDoubleArray(f[0]),h=u.length,u.sort(function(n,t){return n-t}),c=1/(h-1),l=u[h-1],i=0;i<h-1;++i)if((i+1)*c>t){t=(t-i*c)/c;l=u[i]+t*(u[i+1]-u[i]);break}return l.toString()};this.computeTrimmean=function(n){var r=this.splitArgsPreservingQuotedCommas(n),l=r.length,o,s,t,i,c,e;if(l!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()){if(o=this.formulaErrorStringCheck(r[0],FormulaArgumentType.Text),this.getErrorStrings().indexOf(o)>-1)return o;if(s=this.formulaErrorStringCheck(r[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(s)>-1)return s}if(i=this.getValueFromArg(r[1]),i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,t=this._parseDouble(i),isNaN(t)&&(t="1"),t>=1||t==0||t<0){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}var h=this._getDoubleArray(r[0]),u=h.length,f=parseInt((t*u).toString());for(f=parseInt(f/2),h.sort(function(n,t){return isNaN(n)||isNaN(t)?n>t?1:-1:n-t}),c=0,u=u-f,e=f;e<u;++e)c+=h[e];return(c/(u-f)).toString()};this.computePearson=function(n){var t=this.splitArgsPreservingQuotedCommas(n),e=t.length,i,u;if(e!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(i)>-1))return i;if(t[0]==null||t[0]==""||t[1]==null||t[1]==""||Boolean(t[0]=="TRUE"||t[0]=="FALSE")||Boolean(t[1]=="TRUE"||t[1]=="FALSE")){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}var r=this._getDoubleArray(t[0]),o=this._getDoubleArray(t[1]),f=r.length;if(f<=0||!isNaN(r)){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_Math_argument];return this.getErrorStrings()[4].toString()}if(u=this._pearson(o,r,f).toString(),this.computeIsErr(u)==this.trueValueStr){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_Math_argument];return this.getErrorStrings()[3].toString()}return u};this.computeRsq=function(n){var u=this.splitArgsPreservingQuotedCommas(n),r,i,t;if(u.length!=2){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(r)>-1))return r;if(i=this.computePearson(n).toString(),t=0,t=this._parseDouble(i),isNaN(t)){if(this.getErrorStrings().indexOf(i)!=-1)return i}else t=t*t;return t.toString()};this.computeHLookUp=function(n){var v=this._isHLookupCachingEnabled(),w=this._isOptimizedMatchesEnabled(),r,s,u,rt,h,b,t,c,k,p,o;if(v&&this._lookupTables==null&&(this.lookupTables=new HashTable),r=this.splitArgsPreservingQuotedCommas(n),r.length>4||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()){if((s=this.formulaErrorStringCheck(r[0],FormulaArgumentType.Text),this.getErrorStrings().indexOf(s)>-1)||(s=this.formulaErrorStringCheck(r[1],FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(s)>-1))return s;for(t=2;t<r.length;t++)if(s=this.formulaErrorStringCheck(r[t],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(s)>-1)return s}u=this.getValueFromArg(r[0]);u=u.split(this.tic).join("").toUpperCase();var f=r[1].split('"').join(""),ut=this.getValueFromArg(r[2]).split(this.tic).join(""),l=this._parseDouble(ut);if(isNaN(l))return"#N/A";rt=parseInt(ut);h=!0;r.length==4&&(h=this.getValueFromArg(r[3])==this.trueValueStr);l=this._parseDouble(u);b=h?!isNaN(l):!1;t=f.indexOf(":");t==-1&&(f=f+":"+f,t=f.indexOf(":"));var ft=f.substring(0,t).lastIndexOf(this.sheetToken),et=this.grid,ct=CalcEngine.getSheetFamilyItem(this.grid);if(ft>-1&&(this.grid=ct.tokenToParentObject.getItem(f.substring(0,ft+1))),c=this.rowIndex(f.substring(0,t)),k=this.rowIndex(f.substring(t+1)),!(c!=-1||k==-1)==(c==-1||k!=-1))return this.getErrorStrings()[5].toString();var y=this.colIndex(f.substring(0,t)),ot=this.colIndex(f.substring(t+1)),d=!1,a="",g=null,nt=null;v&&(a=c.toString()+"_"+y.toString()+"_"+ot.toString()+"_"+this.grid.GetHashCode(),this._lookupTables.containsKey(a)||(w?(p=new LookUps,p.setMatchLookUpList(new HashTable),this._lookupTables.add(a,p)):(p=new LookUps,this._lookupTables.add(a,p)),d=!0),g=this._lookupTables.getItem(a).getLinearLookUpList(),w&&(nt=this._lookupTables.getItem(a).getMatchLookUpList()));var i="",tt=y,e="",it=0,st=!0,ht=!1;if(v&&w&&nt.containsKey(u))tt=nt.getItem(u),e=u;else for(o=y;o<=ot;++o){if(!v||o-y>=g.length||d?(e=this.getValueFromParentObject(this.grid,c,o).toString().toUpperCase().split('"').join(""),v&&(g.push(e),w&&nt.add(e,o))):e=g[o-y],it=b?this._parseDouble(e):it,e==u||h&&(b?!isNaN(it)&&it>l:e>u))if(e==u&&(tt=o,h=!0,ht=!0),d)st=!1;else break;st&&(tt=o);h=d=!0}if(h||e==u){if(!ht&&!b)return this.grid=et,"#N/A";if(rt+c-1>k)return"#REF!";i=this.getValueFromParentObject(this.grid,rt+c-1,tt).toString();i.length>0&&i[0]==this.getFormulaCharacter()&&(i=this.parseFormula(i));l=this._parseDouble(i);i.length>0&&i[0]!=this.tic[0]&&isNaN(l)&&(i=i)}else i="#N/A";return this.grid=et,i};this.computeIndex=function(n){var t=this.splitArgsPreservingQuotedCommas(n),o=t.length,a,i,r,y,s,u,f,g,h,c,l,p,w,nt,tt,it,b;if(o<2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&((a=this.formulaErrorStringCheck(t[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(a)>-1)||o==3&&(a=this.formulaErrorStringCheck(t[2],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(a)>-1)))return a;if(this._isTextEmpty(t[0])||this._isTextEmpty(t[1])||o==3&&this._isTextEmpty(t[2])){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[bad_formula];return this.getErrorStrings()[1].toString()}if(i=t[0],i=i.split(this.tic).join(""),r=i.indexOf(":"),r==-1)if(this._isCellReference(i))i=i+":"+i;else if(i.indexOf(";")>-1)try{if(y=this._splitArguments(i,";"),y[0].indexOf(",")>-1){var rt=parseInt(this.splitArgsPreservingQuotedCommas(y[0]).length),e=[],k=rt,d=0;for(r=0;r<y.length;r++){if(e[r]=[],s=this.splitArgsPreservingQuotedCommas(y[r]),d=s.length,k!=d){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}for(v=0;v<k;v++)s[v]=s[v].split(this.tic).join("")=="TRUE"?"1":s[v].split(this.tic).join("")=="FALSE"?"0":s[v],e[r][v]=this._parseDouble(this.getValueFromArg(s[v]))}if(u=-1,f=-1,t[1]=this.getValueFromArg(t[1]),t[1]=t[1].split(this.tic).join("")=="TRUE"?"1":t[1].split(this.tic).join("")=="FALSE"?"0":t[1],u=this._parseDouble(t[1]),o==3&&(t[2]=this.getValueFromArg(t[2]),t[2]=t[2].split(this.tic).join("")=="TRUE"?"1":t[2].split(this.tic).join("")=="FALSE"?"0":t[2],f=this._parseDouble(t[2])),o==3&&u>=0&&f>=0)return u==0&&f!=0?e[parseInt(u)][parseInt(f)-1].toString():f==0&&u!=0?e[parseInt(u)-1][parseInt(f)].toString():f==0&&u==0?e[0][0].toString():e[parseInt(u)-1][parseInt(f)-1].toString();if(u>=0)return u==0?e[0][0].toString():e[parseInt(u)-1][0].toString()}else return this._findArrayIndex(i,t)}catch(ut){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_Math_argument];return this.getErrorStrings()[2].toString()}else return this._findArrayIndex(i,t);return(r=i.indexOf(":"),n.indexOf("#N/A")>-1||n.indexOf("#N~A")>-1)?"#N/A":n.indexOf("#DIV/0!")>-1||n.indexOf("#DIV~0!")>-1?"#DIV/0!":(g=this._getSheetTokenFromReference(i),t[1]=o==1||t[1]==""?"1":t[1],t[2]=o<=2||t[2]==""?"1":t[2],t[1]=this.getValueFromArg(t[1]),t[1]=t[1].split(this.tic).join("")=="TRUE"?"1":t[1].split(this.tic).join("")=="FALSE"?"0":t[1],h=parseInt(t[1]),c=isNaN(h)?-1:h,t[2]=this.getValueFromArg(t[2]),t[2]=t[2].split(this.tic).join("")=="TRUE"?"1":t[2].split(this.tic).join("")=="FALSE"?"0":t[2],h=parseInt(t[2]),l=isNaN(h)?-1:h,c==-1||l==-1)?"#REF":(p=this.rowIndex(i.substring(0,r)),w=this.rowIndex(i.substring(r+1)),!(p!=-1||w==-1)==(p==-1||w!=-1))?this.getErrorStrings()[5].toString():(nt=this.colIndex(i.substring(0,r)),tt=this.colIndex(i.substring(r+1)),c>w-p+1||l>tt-nt+1)?"#REF":(c=this.rowIndex(i.substring(0,r))+c-1,l=this.colIndex(i.substring(0,r))+l-1,it=g+RangeInfo.getAlphaLabel(l)+c,b=this.getValueFromArg(it),this._isTextEmpty(b))?"0":b};this._findArrayIndex=function(n,t){var i=[],f=this._string_empty,r=-1,u=-1,e=t.length;if(i=n.indexOf(";")>-1?this._splitArguments(n,";"):this.splitArgsPreservingQuotedCommas(n),t[1]=this.getValueFromArg(t[1]),t[1]=t[1].split(this.tic).join("")=="TRUE"?"1":t[1].split(this.tic).join("")=="FALSE"?"0":t[1],r=this._parseDouble(t[1]),e==3&&(t[2]=this.getValueFromArg(t[2]),t[2]=t[2].split(this.tic).join("")=="TRUE"?"1":t[2].split(this.tic).join("")=="FALSE"?"0":t[2],u=this._parseDouble(t[2])),e==3&&r>=0&&r==1&&u>=0)i.length>=u&&(f=parseInt(u)==0?i[0]:i[parseInt(u)-1]);else if(r>=0)if(i.length>=r)f=parseInt(r)==0?i[0]:i[parseInt(r)-1];else{if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[2].toString()}else{if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}return f};this.computeIndirect=function(n){var i,s,o,r,h,t,f;if(n[n.length-1]==this.bMARKER&&(n=this.getValueFromArg(n)),i=this.splitArgsPreservingQuotedCommas(n),s=i.length,s>2||s==0||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&((o=this.formulaErrorStringCheck(i[0],FormulaArgumentType.Text),this.getErrorStrings().indexOf(o)>-1)||s==2&&(o=this.formulaErrorStringCheck(i[1],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(o)>-1)))return o;if(i[0]=i[0].toUpperCase(),i[0]=this.setTokensForSheets(i[0]),r=this._sheetToken(i[0].split(this.tic).join("")),r!=""&&(i[0]=i[0].split(r).join("")),i.length==2&&i[1]==this.falseValueStr){var l=i[0][0]==this.tic&&i[0][n[0].length-1]==this.tic,a=i[0].toUpperCase().split(this.tic).join(""),e=a.split(":"),u=e[0].split("R").join("C").split("C"),c=u.indexOf("");if(c>-1&&u.splice(c,1),u.length>2)return this.getErrorStrings()[2].toString();i[0]=RangeInfo.getAlphaLabel(parseInt(u[1]))+u[0];e.length==2&&(h=this._sheetToken(e[1]),(h!=null||h!="")&&(e[1]=e[1].split(h).join("")),u=e[0].split("R").join("C").split("C"),i[0]+=":"+RangeInfo.getAlphaLabel(parseInt(u[1]))+u[0]);l&&(i[0]=this.tic+i[0]+this.tic)}if(t="",i[0][0]==this.tic)t=r+i[0].split(this.tic).join("");else if(this._isCellReference(i[0])){if(f="",t=this.getValueFromArg(r+i[0]),t==null||t=="")return"0";if(this._isInteriorFunction)return this._isInteriorFunction=!this._isInteriorFunction,i[0].split(this._string_fixedreference).join("");if(f=this._checkIfScopedRange(t.toUpperCase()),this.getNamedRanges().containsKey(t.toUpperCase()))t=this.getNamedRanges().getItem(t.toUpperCase()),t=t.toUpperCase(),t=t.split(this._string_fixedreference).join(""),t=this.setTokensForSheets(t),t[0]!=this.sheetToken&&(t=r+t);else if(f!="NaN")t=f,t=t.toUpperCase(),t=t.split(this._string_fixedreference).join(""),t=this.setTokensForSheets(t),t[0]!=this.sheetToken&&(t=r+t);else return this._isCellReference(t)?this.getValueFromArg(t):t.indexOf(!1)?this.getErrorStrings()[2].toString():t}else t=i[0];if(!this._isCellReference(t.split(this._string_fixedreference).join("")))if(f="",f=this._checkIfScopedRange(t.toUpperCase()),this.getNamedRanges().containsKey(t.toUpperCase()))t=this.getNamedRanges().getItem(t.toUpperCase()),t[0]!=this.sheetToken&&(t=r+t);else if(f!="NaN")t=f,t[0]!=this.sheetToken&&(t=r+t);else return this.getErrorStrings()[2].toString();return t.indexOf(":")>-1&&this._isInteriorFunction||this.computedValueLevel>1?(this._isInteriorFunction=!this._isInteriorFunction,t.split(this._string_fixedreference).join("")):this.getValueFromArg(t)};this.computeLookUp=function(n){var st=this._isHLookupCachingEnabled(),lt=this._isOptimizedMatchesEnabled(),g={value:!1},t,p,y,f,ht,ct,a,ut,w,v,u,h,b,r,k,d,o,c,ot,tt,it,e;if(st&&this._lookupTables==null&&(this.lookupTables=new HashTable),t=this.splitArgsPreservingQuotedCommas(n),t.length>3||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()){if(p=this.formulaErrorStringCheck(t[0],FormulaArgumentType.Text),this.getErrorStrings().indexOf(p)>-1)return p;for(y=1;y<t.length;y++)if(p=this.formulaErrorStringCheck(t[y],FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(p)>-1)return p}f=this.getValueFromArg(t[0]);f=f.split(this.tic).join("").toUpperCase();var nt=0,rt="",l=t[1].split('"').join(""),at=this.getValueFromArg(t[0]),i=[],s=[];if(t.length==2)if(ht=t[1],ct=this.splitArgsPreservingQuotedCommas(t[1]),t[1].indexOf(";")>-1)if(a=this._splitArguments(t[1],";"),a.length==1)for(v=this.splitArgsPreservingQuotedCommas(a[0].toUpperCase()),u=0;u<v.length;u++)i.push(this.getValueFromArg(v[u]).toUpperCase()),s.push(this.getValueFromArg(v[u]).toUpperCase());else if(a.length==2){for(w=this.splitArgsPreservingQuotedCommas(a[0].toUpperCase()),ut=this.splitArgsPreservingQuotedCommas(a[1].toUpperCase()),u=0;u<w.length;u++)i.push(this.getValueFromArg(w[u]).toUpperCase());for(u=0;u<ut.length;u++)s.push(this.getValueFromArg(ut[u]).toUpperCase())}else for(e=0;e<a.length;e++)w=this.splitArgsPreservingQuotedCommas(a[e].toUpperCase()),i.push(this.getValueFromArg(w[0]).toUpperCase()),s.push(this.getValueFromArg(w[1]).toUpperCase());else if(t[1].indexOf(":")>-1)for(h=this.getCellsFromArgs(t[1]),r=0;r<h.length;r++)i.push(this.getValueFromArg(h[r]).toUpperCase()),s.push(this.getValueFromArg(h[r]).toUpperCase());else for(v=this.splitArgsPreservingQuotedCommas(t[1].toUpperCase()),u=0;u<v.length;u++)i.push(this.getValueFromArg(v[u]).toUpperCase()),s.push(this.getValueFromArg(v[u]).toUpperCase());else if(t.length==3){if(l.indexOf(":")>-1){var y=l.indexOf(":"),ft=this.colIndex(l.substr(0,y)),et=this.colIndex(l.substr(y+1,l.length-y-1));for(nt=et>ft?et-ft+1:ft-et+1,h=this.getCellsFromArgs(l),r=0;r<h.length;r++)try{i.push(this.getValueFromArg(h[r]))}catch(vt){return this.getErrorStrings()[0].toString()}}else for(b=this.splitArgsPreservingQuotedCommas(t[1]),r=0;r<b.length;r++)try{i.push(this.getValueFromArg(b[r]))}catch(vt){return this.getErrorStrings()[0].toString()}if(c=t[2],l.indexOf(":")>-1&&c.indexOf(":")==-1||this.getCellsFromArgs(t[1]).length>this.getCellsFromArgs(t[2]).length?c=this.getResultRange(l,c,g):g.value=!0,!this._isCellReference(c))return this.getErrorStrings()[0].toString();if(c.indexOf(":")>-1)for(h=this.getCellsFromArgs(c),r=0;r<h.length;r++)try{s.push(this.getValueFromArg(h[r]))}catch(vt){return this.getErrorStrings()[0].toString()}else for(b=this.splitArgsPreservingQuotedCommas(t[1]),r=0;r<b.length;r++)try{s.push(this.getValueFromArg(b[r]))}catch(vt){return this.getErrorStrings()[0].toString()}}if(i.indexOf(f)==-1){for(k=i[0],d=0,o=0;o<i.length;o++)if(c=this._parseDouble(f),isNaN(c)){if(i[o]>k&&f>i[o]&&k>f||f>i[o])k=f>i[o]?i[o]:i[o+1],d++;else if(d!=0)break}else ot=this._parseDouble(i[o]),!isNaN(ot)&&c>=ot&&(k=i[o],d++);if(d==0)return this.getErrorStrings()[0].toString();f=k}if(tt=0,it=0,t.length==3&&t[2].indexOf(":")>-1){for(e=0;e<i.length;e++)if(i[e]==f){it=i.indexOf(f);break}rt=g.value?s[it]:s[it/nt]}else{for(e=0;e<i.length;e++)if(i[e]==f)nt=i.indexOf(f),tt++;else if(tt!=0)break;rt=s[nt+tt-1]}return g.value=!1,rt};this.getResultRange=function(n,t,i){var r=this._string_empty,f=n.indexOf(":"),s=this.rowIndex(n.substr(0,f)),h=this.rowIndex(n.substr(f+1)),c=this.colIndex(n.substr(0,f)),l=this.colIndex(n.substr(f+1)),u=0,e=0,o;if(s==h)u=this.rowIndex(t),e=this.colIndex(t)+(l-c);else if(c==l)u=this.rowIndex(t),e=this.colIndex(t)+(h-s);else return t;return r=RangeInfo.getAlphaLabel(e),t.indexOf(":")>-1?(o=t.indexOf(":"),u=this.rowIndex(t.substr(0,o)),r=t.substr(0,o)+":"+r+u):r=t+":"+r+u,i.value=!0,r};this.computeOffSet=function(n){var u=this.splitArgsPreservingQuotedCommas(n),f=u.length,e,s,v,w,h,b,a,k,y;if(f<3||f>5||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()){if(e=this.formulaErrorStringCheck(u[0],FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(e)>-1)return e;for(s=1;s<f;s++)if(s<=f&&(e=this.formulaErrorStringCheck(u[s],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e}var t=u[0],i=this._parseDouble(this.getValueFromArg(u[1])),p=isNaN(i)?-1:parseInt(i.toString());i=this._parseDouble(this.getValueFromArg(u[2]));v=isNaN(i)?-1:parseInt(i.toString());w=f>=4?u[3]:"-1";i=parseInt(this.getValueFromArg(w));h=isNaN(i)?1:i;b=f==5?u[4]:"-1";i=parseInt(this.getValueFromArg(b));var c=isNaN(i)?1:i,r=t.indexOf(":"),o=r==-1;o&&(t=t+":"+t,r=t.indexOf(":"));o=o&&c<=1&&h<=1;c==-1&&(o=!0);var d=this._getSheetTokenFromReference(t),l=this.rowIndex(t.substring(0,r))+p,g=this.rowIndex(t.substring(r+1))+p;return!(this.rowIndex(t.substring(0,r))!=-1||this.rowIndex(t.substring(r+1))!=-1)==(this.rowIndex(t.substring(0,r))==-1||this.rowIndex(t.substring(r+1))!=-1)?this.getErrorStrings()[5].toString():(a=this.colIndex(t.substring(0,r))+v,k=this.colIndex(t.substring(r+1))+v,l<=0||a<=0)?this.getErrorStrings()[2].toString():((h>0&&c<0||h<1&&c>1)&&(l=this.rowIndex(this.cell)),k==parseInt(this.computeColumn(t))-1&&(o=!0),y=d+RangeInfo.getAlphaLabel(a)+l,o?this.computedValue(y):y+":"+RangeInfo.getAlphaLabel(a+c-1)+(l+h-1))};this.computeTranspose=function(n){var f=this.splitArgsPreservingQuotedCommas(n),e,c,o,l,s,a,i,t,u,r;if(f.length!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(!this._isCellReference(n)&&!this.getNamedRanges().containsKey(n)&&n.indexOf(";")==-1){if(t=this._splitArguments(n.split(this.tic).join(""),";"),t.length==1)return this.getValueFromArg(this.splitArgsPreservingQuotedCommas(t[0])[0]);if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.formulaErrorStrings[this._invalid_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(e)>-1))return e;if(f=this.splitArgsPreservingQuotedCommas(n),c=f.length,c!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(i="",n.indexOf(";")>-1){for(t=this._splitArguments(n.split(this.tic).join(""),";"),u=1;u<t.length;u++)if(this.splitArgsPreservingQuotedCommas(t[u-1]).length!=this.splitArgsPreservingQuotedCommas(t[1]).length){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}i=this.splitArgsPreservingQuotedCommas(t[0])[0]}else{r=this.getCellsFromArgs(n);o=this.rowIndex(r[0].toString());l=this.rowIndex(r[r.length-1].toString());s=this.colIndex(r[0].toString());a=this.colIndex(r[r.length-1].toString());var v=this.cell,b=this.colIndex(v),k=this.rowIndex(v),d=l-o+1,g=a-s+1,y,p,nt,tt;this._getFormulaArrayBounds(this.cell,d,g);y=this._getFormulaArrayBoundsfirstRowIndex;p=this._getFormulaArrayBoundsfirstColIndex;nt=this._getFormulaArrayBoundslastRowIndex;tt=this._getFormulaArrayBoundslastColIndex;var it=k-y,rt=b-p,h=n.indexOf(":");h==-1&&(n=n+":"+n,h=n.indexOf(":"));var w=n.substring(0,h).lastIndexOf(this.sheetToken),ut=this.grid,ft=CalcEngine.getSheetFamilyItem(this.grid);w>-1&&(this.grid=ft.tokenToParentObject[n.substring(0,w+1)]);i=this._getValueComputeFormulaIfNecessary(o+rt,s+it,this.grid);this.grid=ut}return(i==null||i=="")&&(i="0"),i};this.computeEncodeURL=function(n){var t=this.splitArgsPreservingQuotedCommas(n),u=t.length,i,r;if(u!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(i)>-1))return i;if(!this._isCellReference(t[0])&&!this.getNamedRanges().containsKey(t[0])&&t[0].indexOf(this.tic)==-1&&isNaN(this._parseDouble(t[0]))&&!Boolean(t[0]=="TRUE"||t[0]=="FALSE")){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[5]}return r=this._stripTics0(this.getValueFromArg(t[0])),encodeURIComponent(r)};this.ComputeCOUNTIFS=function(n){return this.computeCountIFFunctions(n,!1)};this.computeAnd=function(n){var f=!0,t,e,r,i,u,o,s;if(n==null||n=="")if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];else return this.formulaErrorStrings[this._bad_formula];for(r=this.splitArgsPreservingQuotedCommas(n),i=0;i<r.length;i++){if(r[i]==this.tic)if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[1].toString();else return this.getErrorStrings()[1].toString();if(r[i].indexOf(":")>-1)for(cells=this.getCellsFromArgs(r[i]),u=0;u<cells.length;u++){if(this.getErrorStrings().indexOf(cells[u])>-1)return cells[u];if(cells[u][0]==this.tic)if(this.getRethrowLibraryComputationExceptions)throw(new this.getErrorStrings)[5].toString();else return this.getErrorStrings()[5].toString();try{if(t=this.getValueFromArg(cells[u]),this.getErrorStrings().indexOf(t)>-1)return t}catch(h){if(this.rethrowLibraryComputationExceptions&&this.getLibraryComputationException!=null)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}f=f&(t==""?!0:t.split(this.tic).join("").toUpperCase()==this.trueValueStr||(e=this._parseDouble(t))&&e!=0)}else{try{if(t=this.getValueFromArg(r[i]),this.getErrorStrings().indexOf(t)>-1)return t;if(o=Date.parse(t.split(this.tic).join("")),e=this._parseDouble(t.split(this.tic).join("")),isNaN(o)){if(isNaN(e)&&!(t==this._string_empty)&&!(t.split(this.tic).join("").toUpperCase()==this.trueValueStr||t.split(this.tic).join("").toUpperCase()==this.falseValueStr))if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[1].toString();else return this._isCellReference(r[i])||t[0]==this.tic?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}else return this.trueValueStr}catch(c){if(this.getRethrowLibraryComputationExceptions()&&this.getLibraryComputationException()!=null)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}s=this._parseDouble(t);f=f&(t.split(this.tic).join("").toUpperCase()==this.trueValueStr||!isNaN(s)&&e!=0)}}return f?this.trueValueStr:this.falseValueStr};this.computeFalse=function(n){var t=this.splitArgsPreservingQuotedCommas(n);if(t.length>0&&t!=""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.falseValueStr};this.computeIf=function(n){var t=this._string_empty,i=this.splitArgsPreservingQuotedCommas(n),u,f,r;if(this.getErrorStrings().indexOf(n)>0)return n;if(i.length==1||i.length>3||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(i.length<=3)try{if(t=this.getValueFromArg(i[0]),this.getErrorStrings().indexOf(t)!=-1)return t;if(!this._isCellReference(i[0])&&!isNaN(typeof t=="boolean")&&t.startsWith(this.tic)||this._isCellReference(i[0])&&t.startsWith(this.tic)){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}if(t.split(this.tic).join("")==this.trueValueStr||(u=this._parseDouble(t))&&u!=0){if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(i[1],FormulaArgumentType.Text),this.getErrorStrings().indexOf(f)>-1))return f;if(t=this.getValueFromArg(i[1]),t==""&&this.getTreatStringsAsZero())return"0";t==this._string_empty||t[0]!=this.tic[0]||this._isCellReference(i[1])||(t=t.split(this.tic[0]).join(this._string_empty))}else if(t.split(this.tic).join(this._string_empty)==this.falseValueStr&&i.length<3)t=this.falseValueStr;else if(t.split(this.tic).join(this._string_empty)==this.falseValueStr||t==this._string_empty||(u=this._parseDouble(t))&&u==0){if(t=i.length==3?this.getValueFromArg(i[2]):!1,t==this._string_empty&&this.getTreatStringsAsZero())return"0";if(t==this._string_empty||t[0]!=this.tic[0]||this._isCellReference(i[2])||(t=t.split(this.tic[0]).join(this._string_empty)),this.getErrorStrings().indexOf(t)!=-1)return t}else{if(i.indexOf(this.tic)>-1){var e=i[0].indexOf(this.tic)+1,o=i[0].lastIndexOf(this.tic)-1,s=i[0].substring(e,o-e+1);for(r=0;r<this.formulaErrorStrings.length;r++)if(s===r)return r}t="NaN"}}catch(h){if(this.rethrowLibraryComputationExceptions&&this.getLibraryComputationException!=null)throw this.getLibraryComputationException;return h}else return this.formulaErrorStrings[this._requires_3_args];return t};this.computeIfs=function(n){var r=this._string_empty,u=this.splitArgsPreservingQuotedCommas(n),e=u.length,t,f;if(e%2!=0)throw new Error("You have entered too few arguments in this function");for(i=0;i<e;i++){if(r=this.getValueFromArg(u[i]).split(this.tic).join(this._string_empty),this.getErrorStrings().indexOf(r)!=-1)return r;if(t=r.split(this.tic).join("").toUpperCase(),f=this._parseDouble(t),!this._isCellReference(u[0])&&t!=this.trueValueStr&&t!=this.falseValueStr&&r[0]==this.TIC||this._isCellReference(u[0])&&r[0]==this.tic||t!=this.trueValueStr&&t!=this.falseValueStr&&isNaN(f)){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[1].toString()}if(i++,t==this.trueValueStr||!isNaN(f))return this.getValueFromArg(u[i]).split(this.tic).join(this._string_empty)}return this.getErrorStrings()[0].toString()};this.computeIfError=function(n){var i=n.split(this.getParseArgumentSeparator()),t=i[0],r;if(i.length!=2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(i[1],FormulaArgumentType.Text),this.getErrorStrings().indexOf(r)>-1))return r;if(t[0]==this.tic)return this.getValueFromArg(i[0]);try{if(t[0]==this.bMARKER&&(t=t.replace(this.bMARKER," "),t=this._isIE8?t.replace(/^\s+|\s+$/g,""):t.trim(),t[0]=="NAN"||t[0]=="-NAN"||t[0]=="INFINITY"||t[0]=="-INFINITY"||t[0]=="#"||t[0]=="n#"))return this.getValueFromArg(i[1]);t=i[0];t=this.getValueFromArg(t).toUpperCase().split(this.tic).join(this._string_empty)}catch(u){t=t.toUpperCase()}return t[0]=="NAN"||t[0]=="-NAN"||t[0]=="INFINITY"||t[0]=="-INFINITY"||t[0]=="#"||t[0]=="n#"?this.getValueFromArg(i[1]):this.getValueFromArg(i[0])};this.computeIfNA=function(n){var t=this.splitArgsPreservingQuotedCommas(n),u=t.length,i=this._string_empty,r;if(u!=2)return this.getFormulaErrorStrings();if(this.computeIsNA(t[0])==this.trueValueStr){if(this.tic.indexOf(t[1])==-1&&!this._isCellReference(t[1])&&!(r=this._parseDouble(t[0]))&&!this.namedRanges.containsKey(t[1]))return this.getErrorStrings()[5].toString();i=this.getValueFromArg(t[1])}else if(this._isRange(t[0]))i=this.getErrorStrings()[1].toString();else{if(this.tic.indexOf(t[0])==-1&&!this._isCellReference(t[0])&&!(r=this._parseDouble(t[0]))&&!this.NamedRanges.ContainsKey(t[0]))return this.getErrorStrings()[5].toString();i=this.getValueFromArg(t[0])}return i[0]==this.tic&&i[length-1]==this.tic&&(i=this.substring(i,1,i.length-2)),i};this.computeNot=function(n){var t=n,i,r=[this.getParseArgumentSeparator(),":"];if(n.length>0&&this._indexOfAny(n,r)>-1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}try{if(t=this.getValueFromArg(t),this.getErrorStrings().indexOf(t)!=-1)return t;if(t==this.trueValueStr)t=this.falseValueStr;else if(t==this.falseValueStr)t=this.trueValueStr;else{if(isNaN(i=this._parseDouble(t))){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}t=Math.abs(i)>1e-10?this.falseValueStr:this.trueValueStr}}catch(u){if(this.rethrowLibraryComputationExceptions&&this.getLibraryComputationException!=null)throw this.getLibraryComputationException;return u}return t};this.computeOr=function(n){var e=this.falseValueStr,t,r,u=this.splitArgsPreservingQuotedCommas(n),i,f;if(n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}for(i=0;i<u.length;i++)if(u[i].indexOf(":")>-1&&this._isCellReference(i))for(cells=this.getCellsFromArgs(u[i]),f=0;f<cells.length;f++){try{if(t=this.getValueFromArg(cells[f]),this.getErrorStrings().indexOf(t)!=-1)return t}catch(o){if(this.rethrowLibraryComputationExceptions&&this.getLibraryComputationException!=null)throw this.getLibraryComputationException;return o}e|=t==this.trueValueStr||r==this._parseDouble(t)&&r!=0}else{try{if(t=this.getValueFromArg(u[i]),this.getErrorStrings().indexOf(t)!=-1)return t;if(r=this._parseDouble(t.split(this.tic).join("")),isNaN(r)&&!(t==this._string_empty)&&!(t.split(this.tic).join("").toUpperCase()==this.trueValueStr||t.split(this.tic).join("").toUpperCase()==this.falseValueStr))if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[1].toString();else return this._isCellReference(u[i])||t[0]==this.tic?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}catch(o){if(this.rethrowLibraryComputationExceptions&&this.getLibraryComputationException!=null)throw this.getLibraryComputationException;return o}e|=t==this.trueValueStr||r==this._parseDouble(t)&&r!=0}return e?this.trueValueStr:this.falseValueStr};this.computeTrue=function(){return this.trueValueStr};this.computeXor=function(n){var e=!1,t,r,u=this.splitArgsPreservingQuotedCommas(n),i,o,f;if(n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}for(i=0;i<u.length;i++)if(u[i].indexOf(":")>-1&&this._isCellReference(i))for(o=this.getCellsFromArgs(u[i]),f=0;f<o.length;f++){try{if(t=this.getValueFromArg(o[f]),this.getErrorStrings().indexOf(t)!=-1)return t}catch(s){if(this.rethrowLibraryComputationExceptions&&this.getLibraryComputationException!=null)throw this.getLibraryComputationException;return s}r=this._parseDouble(t);e^=t==this.trueValueStr^(!isNaN(r)&&r!=0)}else{try{if(t=this.getValueFromArg(u[i]),this.getErrorStrings().indexOf(t)!=-1)return t;if(d=this._parseDouble(t.split(this.tic).join("")),isNaN(d)&&!(t==this._string_empty)&&!(t.split(this.tic).join("").toUpperCase()==this.trueValueStr||t.split(this.tic).join("").toUpperCase()==this.falseValueStr))if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[1].toString();else return this._isCellReference(u[i])||t[0]==this.tic?this.getErrorStrings()[1].toString():this.getErrorStrings()[5].toString()}catch(s){if(this.rethrowLibraryComputationExceptions&&this.getLibraryComputationException!=null)throw this.getLibraryComputationException;return s}r=this._parseDouble(t);e^=t==this.trueValueStr^(!isNaN(r)&&r!=0)}return e?this.trueValueStr:this.falseValueStr};this.computeCell=function(n){var r=this.splitArgsPreservingQuotedCommas(n),u,i,f,t;if(r.length>2||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&((u=this.formulaErrorStringCheck(r[0],FormulaArgumentType.Text),this.getErrorStrings().indexOf(u)>-1)||r.length==2&&(u=this.formulaErrorStringCheck(r[1],FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(u)>-1)))return u;if(i="",r.length==2){if(!this._isCellReference(r[1])&&!this.namedRanges.containsKey(r[1])){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[5].toString()}i=r[1]}else i=this.cell;f=r[0].split(this.tic).join(this._string_empty).toLowerCase();t="";switch(f){case"address":t=this.rowIndex(i).toString()+this.getParseArgumentSeparator()+this.colIndex(i).toString();t=this.computeAddress(t);break;case"col":t=this.computeColumn(i);break;case"contents":t=this.getValueFromArg(i);break;case"filename":t="Not Supported";break;case"row":t=this.computeRow(i);break;case"type":t=this.computeIsBlank(i)==this.trueValueStr?"b":this.computeIsText(i)==this.trueValueStr?"l":"v"}if(t==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}return t.toString()};this.computeErrorType=function(n){var t=this._string_empty,r=this._splitArguments(n,this.getParseArgumentSeparator()),u=r.length,i;if(u!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(i)>-1)?i:(t=this._isCellReference(n)?this.getValueFromArg(n):n,t.length>1&&t[0]==this.tic[0]&&t[t.length-1]==this.tic[0])?"#N/A":t=="#NULL!"?"1":t=="#DIV/0!"?"2":t=="#VALUE!"?"3":t=="#REF!"?"4":t=="#NAME?"?"5":t=="#NUM!"?"6":t=="#N/A"?"7":t=="#GETTING_DATA"?"8":"#N/A"};this.computeInfo=function(n){var r=this.splitArgsPreservingQuotedCommas(n),e=r.length,i,u,t,f;if(e!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(i)>-1))return i;u=this._stripTics0(this.getValueFromArg(r[0])).toLowerCase();t=this._string_empty;switch(u){case"directory":t=window.location.toString();break;case"numfile":t=this.getSortedSheetNames()!=null&&CalcEngine._sheetFamiliesList.length==0?this.getSortedSheetNames().length.toString():"1";break;case"origin":f=this.rowIndex(this.cell).toString()+this.getParseArgumentSeparator()+this.colIndex(this.cell).toString();t="$A: "+this.computeAddress(f);break;case"osversion":t=navigator.platform.toString();break;case"recalc":t=this._alwaysComputeDuringRefresh?"Automatic":"Manual";break;case"release":t=this.System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.toString();break;case"system":switch(navigator.appVersion.indexOf()){case navigator.platform:t="unix";break;case navigator.platform:t="mac";break;default:t="pcdos"}break;default:if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[1].toString()}return t};this.computeIsBlank=function(n){var t=this.splitArgsPreservingQuotedCommas(n),i=t.length;if(i!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.getValueFromArg(n)==""?this.trueValueStr:this.falseValueStr};this.computeIsErr=function(n){var t=this.splitArgsPreservingQuotedCommas(n),i=t.length;if(i!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return n=this.getValueFromArg(n).toUpperCase().split(this.tic).join(this._string_empty),n.count>1,(n.startsWith("NAN")||n.startsWith("-NAN")||n.startsWith("INFINITY")||n.startsWith("-INFINITY")||n.startsWith("#")||n.startsWith("n#"))&&!n.startsWith("#N/A")?this.trueValueStr:this.falseValueStr};this.computeIsError=function(n){var t=this.splitArgsPreservingQuotedCommas(n),i=t.length;if(i!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}try{n=this.getValueFromArg(n).toUpperCase().split(this.tic).join(this._string_empty)}catch(r){return this.trueValueStr}return n.indexOf("NAN")==0||n.indexOf("-NAN")==0||n.indexOf("INFINITY")==0||n.indexOf("-INFINITY")==1||n.indexOf("#")==0||n.indexOf("n#")==0?this.trueValueStr:this.falseValueStr};this.computeIsEven=function(n){var s=this._string_empty,f=this.splitArgsPreservingQuotedCommas(n),o=f.length,i,t,r,u,e;if(o!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(i=this.getValueFromArg(f[0]),t=parseInt(i.split(this.tic).join("")),this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;if(u=new Date(Date.parse(i)),isNaN(t)&&u.toString()!="invalid Date"&&(t=this._toOADate(u)),e=t,isNaN(t)){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}return e%2==0?this.trueValueStr:this.falseValueStr};this.computeIsFormula=function(n){var t=this.splitArgsPreservingQuotedCommas(n),f=t.length,u=this._string_empty,i,r;if(f!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(i)>-1))return i;if(u=this.getValueFromArg(t[0]),this.namedRanges.containsValue(u)){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[1].toString()}return r=CalcEngine.getSheetFamilyItem(this.grid),this.isSheetMember()&&r.parentObjectToToken!=null&&(t[0]=r.parentObjectToToken.getItem(this.grid)+t[0]),this.getFormulaInfoTable().containsKey(t[0])?this.trueValueStr:this.falseValueStr};this.computeIsLogical=function(n){var t=this.splitArgsPreservingQuotedCommas(n);if(t.length!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return(n=this.getValueFromArg(n),n==this.falseValueStr||n==this.trueValueStr)?this.trueValueStr:this.falseValueStr};this.computeIsNA=function(n){var t=this.splitArgsPreservingQuotedCommas(n);if(t.length>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(n=this.getValueFromArg(n),this.getErrorStrings().indexOf(n.toUpperCase())!=-1)return n.toUpperCase()=="#N/A"?this.trueValueStr:this.falseValueStr;try{n=this.getValueFromArg(n).toUpperCase()}catch(i){return this.falseValueStr}return n[0]=="#N/A"?this.trueValueStr:this.falseValueStr};this.computeIsNonText=function(n){var t=this.splitArgsPreservingQuotedCommas(n);if(t.length>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return this.computeIsText(n)==this.trueValueStr?this.falseValueStr:this.trueValueStr};this.computeIsNumber=function(n){var t=this.splitArgsPreservingQuotedCommas(n);if(t.length>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return n=this.getValueFromArg(n),isNaN(this._parseDouble(n))?this.falseValueStr:this.trueValueStr};this.computeIsRef=function(n){var t=this.splitArgsPreservingQuotedCommas(n);if(t.length>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(t.length!=1){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return((this.namedRanges.containsKey(n)||this._isCellReference(n))&&n[0]!=this.tic&&n[length-1]!=this.tic).toString().toUpperCase()};this.computeIsOdd=function(n){var r=this._string_empty,t=0,u=this._splitArguments(n,this.getParseArgumentSeparator()),f=u.length,e=[this.getParseArgumentSeparator(),":"],i;if(f!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(i)>-1))return i;if(n!=this._string_empty){if(this._indexOfAny(n,e)!=-1)return"#VALUE!";if(r=this.getValueFromArg(u[0]),t=parseInt(r.split(this.tic).join(this._string_empty)),isNaN(t)){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}return t%2!=0?this.trueValueStr:this.falseValueStr}return this.trueValueStr};this.computeIsText=function(n){var r=this.splitArgsPreservingQuotedCommas(n),i,t;if(r.length>1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return i=this._isCellReference(n),t=i?this.getValueFromArg(n):n,(i||t[0]==this.tic)&&t.length>0&&isNaN(this._parseDouble(t))?this.trueValueStr:this.falseValueStr};this.computeN=function(n){var t=this._string_empty,i=0,u,f=this._splitArguments(n,this.getParseArgumentSeparator()),e=f.length,r;if(e!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(r)>-1))return r;if(t=this.getValueFromArg(n),u=new Date(Date.parse(t)),this._parseDouble(t))return i=this._parseDouble(t),i.toString();if(new Date(Date.parse(t))&&(i=this._getSerialDateTimeFromDate(u)),t==this.trueValueStr)i=1;else if(t==this.falseValueStr)i=0;else return this.getErrorStrings().indexOf(t)>-1||this.formulaErrorStrings.indexOf(t)>-1?t:isNaN(i)?"0":i.toString();return i.toString()};this.computeNA=function(n){var t=this.splitArgsPreservingQuotedCommas(n);if(t.length>0&&t!=""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}return"#N/A"};this.computeSheet=function(n){var o=this.splitArgsPreservingQuotedCommas(n),h=o.length,c=this._string_empty,e,t,i,s;if(h>1){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(e)>-1))return e;if(t=o[0].toUpperCase(),t=t==null||t==this._string_empty?this.cell:t,t==null||t==this._string_empty)return"1";if(this.getErrorStrings().indexOf(t)>-1)return t;if(!this._isCellReference(t)&&!this.namedRanges.containsKey(t)&&this.getSortedSheetNames().indexOf(t)>-1){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[0].toString()}var r=CalcEngine.getSheetFamilyItem(this.grid),u=this._getSheetTokenFromReference(t),f=0;if(u!=null&&u!=this._string_empty||this.getSortedSheetNames().indexOf(t)!=-1||t.indexOf(this.sheetToken.toString()))if(u.length>0)f=parseInt(u.split(this.sheetToken).join(this._string_empty))+1;else try{for(i=0;i<r.tokenToParentObject.length;i++)if(r.sheetNameToParentObject.getItem(t.split(this.tic).join(this._string_empty))==r.tokenToParentObject.values()[i]){s=r.tokenToParentObject.values()[i];f=this.getSheetID(s)+1;break}}catch(l){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[2].toString()}else f=this.getSheetID(this.grid)+1;return f.toString()};this.computeSheets=function(n){var u=this.splitArgsPreservingQuotedCommas(n),e=u.length,o=this._string_empty,i,t,r,f;if(e>1){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(i=0,t=u[0].toUpperCase(),this.getErrorStrings().indexOf(t)>-1)return t;if(t==null||t==this._string_empty)return this.getSortedSheetNames().length.toString();if(this.getEnableFormulaErrorValidation()){if(r=this.formulaErrorStringCheck(n,FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(r)>-1)return r}else{if(t.split(this.tic).join(this._string_empty)==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[5].toString()}if(!this._isCellReference(t)&&!this.namedRanges.containsKey(t)&&this.getSortedSheetNames().indexOf(t)>-1){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[0].toString()}}try{f=this._splitArguments(t,"!");i=(f.length-1)/2}catch(s){if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[2].toString();return this.getErrorStrings()[2].toString()}return i.toString()};this.computeType=function(n){var r=this.splitArgsPreservingQuotedCommas(n),u=r.length,i=0,t;if(u!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(t=this.getValueFromArg(r[0]),t==null||t==this._string_empty){if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[1].toString();return this.getErrorStrings()[1].toString()}return n.indexOf(this.getParseArgumentSeparator())>-1?i=64:this.computeIsNumber(t)==this.trueValueStr?i=1:this.computeIsLogical(t)==this.trueValueStr?i=4:this.getErrorStrings().indexOf(t)>-1?i=16:this.computeIsText(t)==this.trueValueStr&&(i=2),i.toString()};this.computeRow=function(n){var t=this.splitArgsPreservingQuotedCommas(n),u=t.length,i,r;if(u!=1){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(t[0]==this._string_empty)return this.rowIndex(this.cell).toString();if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(i)>-1))return i;if(!this._isCellReference(t[0])&&!this.namedRanges.containsKey(t[0])){if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}if(n.indexOf(":")>-1&&this._isArrayFormula)if(r=this._computeArrayInteriorFunction(n,"ROW",this.computeFunctionLevel),r==this._string_empty)n=this.getCellsFromArgs(n)[0];else return r;return n.indexOf(":")>-1&&(n=this.getCellsFromArgs(n)[0]),this.rowIndex(n).toString()};this.computeRows=function(n){var f=this.splitArgsPreservingQuotedCommas(n),e,o,s,t,r,i,u;if(f.length!=1||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(e)>-1))return e;if(t=1,f[0].indexOf(":")>-1)r=this.getCellsFromArgs(n),o=this.rowIndex(r[0].toString()),s=this.rowIndex(r[r.length-1].toString()),t=s-o+1,t=t>0?t:1;else if(f[0].indexOf(";")>-1)for(i=this._splitArguments(n.split(this.tic).join(this._string_empty),";"),u=1;u<i.length;u++){if(this.splitArgsPreservingQuotedCommas(i[u-1]).length!=this.splitArgsPreservingQuotedCommas(i[1]).length){if(this.getRethrowLibraryComputationExceptions())throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}t=i.length}return t.toString()};this.computePmt=function(n){var t=this.splitArgsPreservingQuotedCommas(n),f=t.length,h,i,l;if(f<3||f>5||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(h=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(h)>-1))return h;var r,e=0,o=0,s=0,u=0,c=0;for(i=0;i<f;i++)t[i]=this.getValueFromArg(t[i].split(this.tic).join(this._string_empty)),t[i]=t[i]==null||t[i]==""?"0":t[i],t[i]=t[i].split(this.tic).join("")=="TRUE"?"1":t[i].split(this.tic).join("")=="FALSE"?"0":t[i];if(r=this._parseDouble(t[0]),e=this._parseDouble(t[1]),o=this._parseDouble(t[2]),f==4&&(s=this._parseDouble(t[3])),f==5&&(u=this._parseDouble(t[4])),isNaN(r)&&isNaN(e)&&isNaN(o)&&isNaN(s)&&isNaN(u)){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}if(e==0){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[4].toString()}return u=Math.abs(u)>.5?1:0,r==0?c=1*(s+o)/((1+r*u)*(1-(e+1))):(l=Math.pow(1+r,e),c=r*(s+o*l)/((1+r*u)*(1-l))),this.computeDollar(c.toString())};this.computeMatch=function(n){var f=this.splitArgsPreservingQuotedCommas(n),v=f.length,e,l,a,r,c,p,u;if(v>3||n==""){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&((e=this.formulaErrorStringCheck(f[0],FormulaArgumentType.Text),this.getErrorStrings().indexOf(e)>-1)||(e=this.formulaErrorStringCheck(f[1],FormulaArgumentType.CellReference),this.getErrorStrings().indexOf(e)>-1)))return e;var y=f[1].split(this.tic).join(this._string_empty),w=y.indexOf(":"),t=1;if(v==3){if(this.getEnableFormulaErrorValidation()&&(l=this.formulaErrorStringCheck(f[2],FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(l)>-1))return l;if(r=this.getValueFromArg(f[2]),r=r.split(this.tic).join(this._string_empty),a=this._parseDouble(r),t=isNaN(a)?1:a,r==this.falseValueStr)t=0;else if(r==this.trueValueStr)t=1;else if(r.includes(this.tic)&&this.computeIsText(r)==this.trueValueStr){if(this.getRethrowLibraryComputationExceptions())throw this.formulaErrorStrings[this._invalid_arguments];return this.getErrorStrings()[1].toString()}}var s=this.getValueFromArg(f[0].split(this.tic).join(this._string_empty)).toUpperCase(),o=this.getCellsFromArgs(this._stripTics0(y)),i=1,h="",u;for(c=0;c<o.length;c++){if(p=o[c],u=this.getValueFromArg(p).split(this.tic).join(this._string_empty).toUpperCase(),h!="")if(t==1){if(this._matchCompare(u,h)<0&&u==s){i--;break}}else if(t==-1&&this._matchCompare(u,h)>0){i=-1;break}if((t==0||t==1)&&u==s)break;else if(t==1&&this._matchCompare(s,u)<0){i--;break}else if(t==-1&&this._matchCompare(s,u)>0){i--;break}i++;h==u}return t!=0&&i==o.length+1&&(i=o.length),i>0&&i<=o.length?i.toString():this.getErrorStrings()[0].toString()};this.computeBitAnd=function(n){var r=this.splitArgsPreservingQuotedCommas(n),t=null,i=null,f,e,u;try{if(r.length!=2||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1))return u;if(t=this.getValueFromArg(r[0]),t=t==null||t==""?"0":t,t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,t=t.split(this.tic).join(this._string_empty),i=this.getValueFromArg(r[1]),i=i==null||i==""?"0":i,i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,i=i.split(this.tic).join(this._string_empty),t<0||i<0){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}return f=this._parseDouble(t),e=this._parseDouble(i),(f&e).toString()}catch(o){if(this._rethrowLibraryComputationExceptions)throw"The parameters are not correct";else return this.computeIsText(t)==this.trueValueStr||this.computeIsText(i)==this.trueValueStr?this.getErrorStrings()[5].toString():t==""||i==""?this.getErrorStrings()[1].toString():this.getErrorStrings()[1].toString()}};this.computeBitOr=function(n){var r=this.splitArgsPreservingQuotedCommas(n),t=null,i=null,f,e,u;try{if(r.length!=2||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1))return u;if(t=this.getValueFromArg(r[0]),t=t==null||t==""?"0":t,t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,t=t.split(this.tic).join(this._string_empty),i=this.getValueFromArg(r[1]),i=i==null||i==""?"0":i,i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,i=i.split(this.tic).join(this._string_empty),t<0||i<0){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}return f=this._parseDouble(t),e=this._parseDouble(i),(f|e).toString()}catch(o){if(this._rethrowLibraryComputationExceptions)throw"The parameters are not correct";else return this.computeIsText(t)==this.trueValueStr||this.computeIsText(i)==this.trueValueStr?this.getErrorStrings()[5].toString():t==""||i==""?this.getErrorStrings()[1].toString():this.getErrorStrings()[1].toString()}};this.computeBitLShift=function(n){var f=this.splitArgsPreservingQuotedCommas(n),t=null,i=null,r,u,e;try{if(f.length!=2||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;if(t=this.getValueFromArg(f[0]),t=t==null||t==""?"0":t,t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,t=t.split(this.tic).join(this._string_empty),i=this.getValueFromArg(f[1]),i=i==null||i==""?"0":i,i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,i=i.split(this.tic).join(this._string_empty),r=this._parseDouble(t),u=this._parseDouble(i),r>0xffffffffffff||Math.abs(u)>53||r<0){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}return(u>=0?r<<u:r>>-u).toString()}catch(o){if(this._rethrowLibraryComputationExceptions)throw"The parameters are not correct";else return this.computeIsText(t)==this.trueValueStr||this.computeIsText(i)==this.trueValueStr?this.getErrorStrings()[5].toString():t==""||i==""?this.getErrorStrings()[1].toString():this._parseDouble(t)<0||this._parseDouble(i)<0?this.getErrorStrings()[4].toString():this.getErrorStrings()[1].toString()}};this.computeBitRShift=function(n){var f=this.splitArgsPreservingQuotedCommas(n),t=null,i=null,r,u,e;try{if(f.length!=2||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;if(t=this.getValueFromArg(f[0]),t=t==null||t==""?"0":t,t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,t=t.split(this.tic).join(this._string_empty),i=this.getValueFromArg(f[1]),i=i==null||i==""?"0":i,i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,i=i.split(this.tic).join(this._string_empty),r=this._parseDouble(t),u=this._parseDouble(i),r>0xffffffffffff||Math.abs(u)>53||r<0){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}return(u>=0?r>>u:r<<-u).toString()}catch(o){if(this._rethrowLibraryComputationExceptions)throw"The parameters are not correct";else return this.computeIsText(t)==this.trueValueStr||this.computeIsText(i)==this.trueValueStr?this.getErrorStrings()[5].toString():t==""||i==""?this.getErrorStrings()[1].toString():this._parseDouble(t)<0||this._parseDouble(i)<0?this.getErrorStrings()[4].toString():this.getErrorStrings()[1].toString()}};this.computeBitXor=function(n){var f=this.splitArgsPreservingQuotedCommas(n),t=null,i=null,r,u,e;try{if(f.length!=2||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;if(t=this.getValueFromArg(f[0]),t=t==null||t==""?"0":t,t=t.split(this.tic).join("")=="TRUE"?"1":t.split(this.tic).join("")=="FALSE"?"0":t,t=t.split(this.tic).join(this._string_empty),i=this.getValueFromArg(f[1]),i=i==null||i==""?"0":i,i=i.split(this.tic).join("")=="TRUE"?"1":i.split(this.tic).join("")=="FALSE"?"0":i,i=i.split(this.tic).join(this._string_empty),r=this._parseDouble(t),u=this._parseDouble(i),r>0xffffffffffff||u>0xffffffffffff||r<0||u<0){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}return(r^u).toString()}catch(o){if(this._rethrowLibraryComputationExceptions)throw"The parameters are not correct";else return this.computeIsText(t)==this.trueValueStr||this.computeIsText(i)==this.trueValueStr?this.getErrorStrings()[1].toString():t==""||i==""?this.getErrorStrings()[1].toString():this.getErrorStrings()[1].toString()}};this.computeBinomOInv=function(n){var r=this.splitArgsPreservingQuotedCommas(n),c=r.length,f;if(c!=3||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1))return f;var u,t,i=0,e=0,o=this.getValueFromArg(r[0]),s=this.getValueFromArg(r[1]),h=this.getValueFromArg(r[2]);if(o==""&&(r[0]=o="0"),s==""||h==""){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}if(u=this._parseDouble(o.split(this.tic).join("")),t=this._parseDouble(s.split(this.tic).join("")),i=this._parseDouble(h.split(this.tic).join("")),u==0)return"0";if(u<0||t<=0||t>=1||i<=0||i>=1){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}if(t>0&&t<1&&i>=0&&i<1){if(e=this._critbinom(u,t,i),e==this.maxValue){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._calculation_overflow];return this.formulaErrorStrings[this._calculation_overflow]}}else{if(u<=0||t<=0||t>=1||i<0||i>1){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[1].toString();return this.getErrorStrings()[1].toString()}return e.toString()};this.computeChidist=function(n){var t=this.splitArgsPreservingQuotedCommas(n),o=t.length,f,e,i,r,u;if(o!=2||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1))return f;for(e=0,i=0;i<o;++i)t[i]=this.getValueFromArg(t[i]),t[i]=this._stripTics0(t[i]);if(t[0]=t[0]==null||t[0]==""?"0":t[0],t[1]=t[1]==null||t[1]==""?"0":t[1],t[0]=t[0].split(this.tic).join("")=="TRUE"?"1":t[0].split(this.tic).join("")=="FALSE"?"0":t[0],t[1]=t[1].split(this.tic).join("")=="TRUE"?"1":t[1].split(this.tic).join("")=="FALSE"?"0":t[1],r=this._parseDouble(t[0]),u=this._parseDouble(t[1]),!isNaN(r)&&!isNaN(u)){if(r<0||u<1){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}if(r==0)return"1";if(u<1)return this._excelLikeComputations?this.getErrorStrings()[4].toString():this.formulaErrorStrings[this._invalid_arguments];e=1-this._chidist(r,u)}return e.toString()};this.computeModeOMult=function(n){var s,i,o;if(n==this._string_empty){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(s=this.formulaErrorStringCheck(n,FormulaArgumentType.Range),this.getErrorStrings().indexOf(s)>-1))return s;var t=this._getDoubleArray(n),h=t.slice(),c=t.length;if(c<=1)return this._errorStrings[0].toString();t.sort();var r=[],e,u=0,f=0;for(i=1;i<c;i++)t[i]==t[i-1]?u++:(u>f?(f=u,e=t[i-1],r.length=0,r.push(e)):u==f&&r.push(t[i-1]),u=0);if(u>f&&(f=u,e=t[i-1],r.length=0,r.push(e)),f>0){if(u==f&&r.push(t[i-1]),r.length>1)for(o=0;o<h.lenght;o++)if(r.indexof(h[o])>-1){e=d;break}return e.toString()}return this._errorStrings[0].toString()};this.computeMunit=function(n){var e=this.splitArgsPreservingQuotedCommas(n),o,t,r,f,i,u;if(e.length!=1||e==this._string_empty){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(o=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(o)>-1))return o;if(t=this.getValueFromArg(e[0]).split(this.tic).join(this._string_empty),t=t==this.trueValueStr?1:t==this.falseValueStr?0:t,r=parseInt(t),r<=0||isNaN(r)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this._errorStrings[1].toString()}for(f=[],i=0;i<r;i++)for(f[i]=[],u=0;u<r;u++)f[i][u]=i==u?1:0;return f[0][0].toString()};this.computeSign=function(n){var r=0,i,h=this.splitArgsPreservingQuotedCommas(n),f,e,o,u,t;if(h.length>1||n==this._string_empty){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(f=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(f)>-1))return f;if(n=this.getValueFromArg(n).split(this.tic).join(this._string_empty),n=n==this.trueValueStr?"1":n==this.falseValueStr?"0":n,n.length>0&&!this._isLetter(n[0])&&(n.indexOf(":")==-1||n.indexOf(this._parseArgumentSeparator)==-1))e=parseFloat(n),isNaN(e)||(r=Math.sign(e));else if(n.length>0&&(n[0]==this.bMARKER||n[0]=="u"||n[0]=="n"||this._indexOfAny(n,this.tokens))){n=n.replace("{","(");n=n.replace("}",")");try{i=this.computeValue(n)}catch(s){return s.message}if(t=parseFloat(i),isNaN(t))return this.formulaErrorStrings[this._invalid_arguments];r=Math.Sign(t)}else for(o=this.getCellsFromArgs(n),u=0;u<o.length;u++){try{i=this.getValueFromArg(o[u])}catch(s){return s.message}if(i.length>0&&(t=parseFloat(i),!isNaN)){r=Math.sign(t);break}}return r.toString()};this.computeBigMul=function(n){var t=this.splitArgsPreservingQuotedCommas(n),u,i,r;if(t.length!=2){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(u=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(u)>-1))return u;if(t[0]=t[0].toString()==this.trueValueStr?1:t[0].toString()==this.falseValueStr?0:t[0].split(this.tic).join(this._string_empty),t[1]=t[1].toString()==this.trueValueStr?1:t[1].toString()==this.falseValueStr?0:t[1].split(this.tic).join(this._string_empty),this.computeIsNumber(t[0]).toString()!=this.trueValueStr||this.computeIsNumber(t[1]).toString()!=this.trueValueStr){if(this._rethrowLibraryComputationExceptions)throw this._errorStrings[4].toString();return this._errorStrings[4].toString()}if(n.length>150){if(this._rethrowLibraryComputationExceptions)throw this._errorStrings[4].toString();return this._errorStrings[4].toString()}return i=this.getValueFromArg(t[0]),i=i.toString()==this.trueValueStr?1:i.toString()==this.falseValueStr?0:i,i=parseInt(i),r=this.getValueFromArg(t[1]),r=r.toString()==this.trueValueStr?1:r.toString()==this.falseValueStr?0:r,r=parseInt(r),(i*r).toString()};this.computeNormInv=function(n){var t=this.splitArgsPreservingQuotedCommas(n),s=t.length,e,r,o,u,f,i;if(s!=3||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(e)>-1))return e;for(f=0,i=0;i<s;++i)t[i]=this.getValueFromArg(t[i]).split(this.tic).join(this._string_empty),t[i]=t[i]==null||t[i]==this._string_empty?"0":t[i],t[i]=t[i]=="TRUE"?"1":t[i]=="FALSE"?"0":t[i];if(r=this._parseDouble(t[0]),o=this._parseDouble(t[1]),u=this._parseDouble(t[2]),!isNaN(r)&&!isNaN(o)&&!isNaN(u)){if(r<=0||r>=1||u<=0){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments]+this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}f=this._normalinv(r,o,u)}return f<=0?this.formulaErrorStrings[this._iterations_dont_converge]:f.toString()};this.computeSlope=function(n){var i=this.splitArgsPreservingQuotedCommas(n),y=i.length,h,c,e,t,s;if(y!=2||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(h=this.getValueFromArg(i[0]),c=this.getValueFromArg(i[1]),!this._isCellReference(i[0])&&this.getEnableFormulaErrorValidation()&&(e=this.formulaErrorStringCheck(n,FormulaArgumentType.Text),this.getErrorStrings().indexOf(e)>-1))return e;if(!this._isCellReference(i[0])&&h==this._string_empty||!this._isCellReference(i[1])&&c==this._string_empty){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[1].toString()}var u=this._getDoubleArray(i[0]),r=this._getDoubleArray(i[1]),o=r.length;if(o!=u.length){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[0].toString()}if(r.length==0||u.length==0){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[1].toString()}var f=0,l=0,a=0,v=0;for(t=0;t<r.length;++t)r[t].toString()!="NaN"&&u[t].toString()!="NaN"&&(a+=r[t]*u[t],f+=r[t],l+=u[t],v+=r[t]*r[t]);if(s=((a-f*l/o)/(v-f*f/o)).toString(),this.computeIsErr(s.toString())==this.trueValueStr){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._bad_formula];return this.getErrorStrings()[3].toString()}return s};this.computeGcd=function(n){var c,i=0,u=this.splitArgsPreservingQuotedCommas(n),t=[],r,s,f,e,h,o;if(n==this._string_empty){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}for(r=0;r<u.length;r++)if(u[r].indexOf(":")>-1)for(s=this.getCellsFromArgs(u[r]),f=0;f<s.length;f++){if(e=this.getValueFromArg(s[f]),this._errorStrings.indexOf(e)>-1){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[_invalid_arguments];return e}if(i=parseFloat(e.split(this.tic).join(this._string_empty)),(!isNaN(i)||s[f].length>0)&&i>0)t.push(i);else{if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[_invalid_arguments];return this._errorStrings[4].toString()}}else{if(h=this.getValueFromArg(u[r]).split(this.tic).join(this._string_empty),this._errorStrings.indexOf(h)>-1&&this._rethrowLibraryComputationExceptions)return e;if(!this._isCellReference(u[r])&&h==this._string_empty){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._invalid_arguments];return this._errorStrings[1].toString()}if(i=parseFloat(h),isNaN(i)){if(!this._isCellReference(u[r])&&!u[r].startsWith(this.tic)){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[_invalid_arguments];return this._errorStrings[5].toString()}if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[_invalid_arguments];return this._errorStrings[1].toString()}if(i>=0)i=parseInt(i),t.push(i);else{if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[_invalid_arguments];return this._errorStrings[4].toString()}}if(t.length==1)return t[0].toString();for(o=0;o<t.length;o++){while(t[1]!=0)c=t[0]%t[1],t[0]=t[1],t[1]=c;t.length>2&&o<t.length-2&&(t[1]=t[o+2])}return t[0].toString()};this.computeLcm=function(n){var f=0,c=0,t,a,v=this._string_empty,n=this.adjustRangeArg(n),i=this.splitArgsPreservingQuotedCommas(n),e,y,l,r,u,s,o,b,h,p,w;if(n==this._string_empty){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(i.length>0)for(e=0;e<i.length;e++)if(c++,i[e].indexOf(":")>-1)for(--c,y=this.getCellsFromArgs(i[e]),l=0;l<y.length;l++)c++;for(r=[],u=0;u<i.length;u++)if(i[u].indexOf(":")>-1)for(s=this.getCellsFromArgs(i[u]),o=0;o<s.length;o++){if(t=parseFloat(this.getValueFromArg(s[o]).split(this.tic).join(this._string_empty)),isNaN(t))return this.getErrorStrings()[1].toString();if(b=parseInt(t),s[o].length>0&&t>=0)r[f]=parseInt(t),f++;else return this.getErrorStrings()[4].toString()}else{if(h=this.getValueFromArg(i[u]).split(this.tic).join(this._string_empty),this._errorStrings.indexOf(h)>-1)return h;if(t=parseFloat(h),isNaN(t))return this.getErrorStrings()[1].toString();if(i[f].length>0&&t>=0)r[f]=parseInt(t),f++;else return this.getErrorStrings()[4].toString()}return r.length==1?r[0].toString():(p=r[0]*r[1],v=r[0].toString()+this._parseArgumentSeparator+r[1].toString(),a=this.computeGcd(v),w=p/parseInt(a),w.toString())};this.computeTruncate=function(n){var u=this.splitArgsPreservingQuotedCommas(n),f=u.length,i,r,t;if(f!=1){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(i=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(i)>-1))return i;if(n.length>15){if(this._rethrowLibraryComputationExceptions)throw this._errorStrings[4].toString();return this._errorStrings[4].toString()}return r=this._string_empty,t=this.getValueFromArg(u[0]).split(this.tic).join(this._string_empty),n=t==this.trueValueStr?1:t==this.falseValueStr?0:t,t=parseFloat(n),isNaN(t)&&t>=0&&(r=t),r.toString()};this.computeCoth=function(n){var o=this.splitArgsPreservingQuotedCommas(n),s=o.length,r,u,f,i,e,t;if(s!=1||n==""){if(this._rethrowLibraryComputationExceptions)throw this.formulaErrorStrings[this._wrong_number_arguments];return this.formulaErrorStrings[this._wrong_number_arguments]}if(this.getEnableFormulaErrorValidation()&&(r=this.formulaErrorStringCheck(n,FormulaArgumentType.Numbers),this.getErrorStrings().indexOf(r)>-1))return r;if(n=this.getValueFromArg(n),u=!1,n=n.split(this.tic).join("")=="TRUE"?"1":n.split(this.tic).join("")=="FALSE"?"0":n,f=this.computeIsNumber(n),f==this.trueValueStr&&(i=n,i.includes("-")&&(u=!0),i=i.includes("u")?i.replace("u",""):i,e=this._parseDouble(i),e>=134217728)){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[4].toString();return this.getErrorStrings()[4].toString()}if(n=="0"){if(this._rethrowLibraryComputationExceptions)throw this.getErrorStrings()[3].toString();return this.getErrorStrings()[3].toString()}return n=n.includes("-")?n.replace("-",""):n,t=this._computeMath(n,Math.tanh),(t!="#NUM!"||t!="#VALUE!")&&(t=(1/parseFloat(t)).toString()),u&&(t="-"+t),t};this._initLibraryFunctions()},CalcEngine.CalcEngine=function(){},CalcEngine.getFormulaCharacter=function(){return this._formulaChar=='\0'&&(this._formulaChar="="),this._formulaChar},CalcEngine._formulaChar="=",CalcEngine.sheetFamilyID=0,CalcEngine._tokenCount=0,CalcEngine.modelToSheetID=null,CalcEngine._sheetFamiliesList=null,CalcEngine._defaultFamilyItem=null,CalcEngine.createSheetFamilyID=function(){return this.sheetFamilyID==Number.MAX_SAFE_INTEGER&&(this.sheetFamilyID=Number.MIN_SAFE_INTEGER),this.sheetFamilyID++},CalcEngine.getSheetFamilyItem=function(n){if(this.sheetFamilyID==0)return CalcEngine._defaultFamilyItem==null&&(CalcEngine._defaultFamilyItem=new GridSheetFamilyItem),CalcEngine._defaultFamilyItem;CalcEngine._sheetFamiliesList==null&&(CalcEngine._sheetFamiliesList=new HashTable);var t=CalcEngine.modelToSheetID.getItem(n);return CalcEngine._sheetFamiliesList.containsKey(t)||CalcEngine._sheetFamiliesList.add(t,new GridSheetFamilyItem),CalcEngine._sheetFamiliesList.getItem(t)},CalcEngine.resetSheetFamilyID=function(){this.sheetFamilyID=0;CalcEngine.modelToSheetID!=null&&(CalcEngine.modelToSheetID.clear(),CalcEngine.modelToSheetID=null);CalcEngine._sheetFamiliesList!=null&&(CalcEngine._sheetFamiliesList.clear(),CalcEngine._sheetFamiliesList=null,this.resetSheetIDs())},CalcEngine.resetSheetIDs=function(){this._tokenCount=0},CalcEngine.unregisterGridAsSheet=function(n,t){var i=CalcEngine.getSheetFamilyItem(t),r=n.toUpperCase(),u;i.sheetNameToParentObject!=null&&i.sheetNameToParentObject.containsKey(r)&&(i.sheetNameToParentObject.remove(r),u=i.sheetNameToToken.getItem(r),i.sheetNameToToken.remove(r),i.tokenToParentObject.remove(u),i.parentObjectToToken.remove(t))},CalcEngine})(jQuery,Syncfusion),function(n){n[n.None=0]="None";n[n.VLOOKUP=1]="VLOOKUP";n[n.HLOOKUP=2]="HLOOKUP";n[n.Both=3]="Both";n[n.OptimizeForMatches=4]="OptimizeForMatches"}(LookupCachingMode||(LookupCachingMode={})),function(n){n[n.FormulaSet=0]="FormulaSet";n[n.NonFormulaSet=1]="NonFormulaSet";n[n.CalculatedValueSet=2]="CalculatedValueSet"}(FormulaInfoSetAction||(FormulaInfoSetAction={}));CalcQuickBase=function(){function n(n){this.resetStaticMembers=n;this._calcQuickID=0;this._controlModifiedFlags=null;this._dataStore=null;this._engine={};this._keyToRowsMap=null;this._keyToVectors=null;this._nameToControlMap=null;this._rowsToKeyMap=null;this._isValueSetEventChanged=!1;this._autoCalc=!1;this._isIE8=!1;this._cellPrefix="!0!A";this._checkKeys=!0;this._disposeEngineResource=!0;this._ignoreChanges=!1;this._leftBrace="{";this._tic='"';this._leftBracket="[";this._rightBracket="]";this._validLeftChars="+-*/><=^(&,";this._validRightChars="+-*/><=^)&,";this.getKeyValue=function(n){if(n=n.toUpperCase(),this.getDataStore().containsKey(n)){var t=this.getDataStore().getItem(n),i=t.getFormulaText();if(i.length>0&&i[0]==CalcEngine.getFormulaCharacter()&&t.calcID!=this.getEngine().getCalcID()){this.getEngine().cell=this._cellPrefix+this.getKeyToRowsMap().getItem(n).toString();i=i.substring(1);try{t.setParsedFormula(this.getEngine().parseFormula(this._markKeys(i)))}catch(r){if(this.getCheckKeys())return t.setFormulaValue(r.Message),t.calcID=this.getEngine().getCalcID(),this._valueSetEvent!=null&&this._valueSetEvent.trigger(this,new ValueSetEventArgs(n,t.getFormulaValue(),2)),this.getDataStore().getItem(n).getFormulaValue()}try{t.setFormulaValue(this.getEngine().computeFormula(t.getParsedFormula()))}catch(r){if(this.getThrowCircularException()&&r.toString().indexOf(this.getEngine().formulaErrorStrings[this.getEngine().circular_reference_])==0)throw r;}t.calcID=this.getEngine().getCalcID();this._valueSetEvent!=null&&this._valueSetEvent.trigger(this,new ValueSetEventArgs(n,t.getFormulaValue(),2))}return this.getEngine().getThrowCircularException()&&this.getEngine().getIterationMaxCount()>0&&t.setFormulaValue(this.getEngine().handleIteration(this.getEngine().cell,t)),this.getDataStore().getItem(n).getFormulaValue()}return this.getKeyToVectors().containsKey(n)?this.getKeyToVectors().getItem(n).toString():""};this.setKeyValue=function(n,t){var i,f,u,e,r,s,c;if(n=n.toUpperCase(),i=this._isIE8?t.toString().replace(/^\s+|\s+$/g,""):t.toString().trim(),!this.getDataStore().containsKey(n)||i.indexOf(this._leftBrace)==0){if(i.indexOf(this._leftBrace)==0){this.getKeyToVectors().containsKey(n)||this.getKeyToVectors().add(n,"");i=i.substr(1,i.length-2);var h=this.getKeyToRowsMap().length+1,o=i.split(this.getEngine().getParseArgumentSeparator()),l="A"+h+":A"+(h+o.length-1);for(this.getKeyToVectors()[n]=l,f=0;f<o.length;f++)u="Q_"+(this.getKeyToRowsMap().length+1),this.getDataStore().add(u,new FormulaInfo),this.getKeyToRowsMap().add(u,this.getKeyToRowsMap().length+1),this.getRowsToKeyMap().add(this.getRowsToKeyMap().length+1,u),e=this.getDataStore().getItem(u),e.setFormulaText(""),e.setParsedFormula(""),e.setFormulaValue(this.parseAndCompute(o[f]));return}this.getDataStore().add(n,new FormulaInfo);this.getKeyToRowsMap().add(n,this.getKeyToRowsMap().length+1);this.getRowsToKeyMap().add(this.getRowsToKeyMap().length+1,n)}this.getKeyToVectors().containsKey(n)&&this.getKeyToVectors().remove(n);r=this.getDataStore().getItem(n);!this.ignoreChanges&&r.getFormulaText()!=null&&r.getFormulaText().length>0&&r.getFormulaText()!=i&&(s=this._cellPrefix+this.getKeyToRowsMap().getItem(n).toString(),c=this.getEngine().getDependentFormulaCells().getItem(s),c!=null&&this.getEngine().clearFormulaDependentCells(s));i.length>0&&i[0]==CalcEngine.getFormulaCharacter()?(r.setFormulaText(i),this._valueSetEvent!=null&&this._valueSetEvent.trigger(this,new ValueSetEventArgs(n,r.getFormulaValue(),2))):r.getFormulaValue()!=i&&(r.setFormulaText(""),r.setParsedFormula(""),r.setFormulaValue(i),this._valueSetEvent!=null&&this._valueSetEvent.trigger(this,new ValueSetEventArgs(n,r.getFormulaValue(),2)));this.getAutoCalc()&&this.updateDependencies(n)};this.resetKeys=function(){this.getDataStore().clear();this.getKeyToRowsMap().clear();this.getRowsToKeyMap().clear();this.getKeyToVectors().clear();this.getNameToControlMap().clear()};this._checkAdjacentPiece=function(n,t,i){var r=!0;return n=this._isIE8?n.replace(/^\s+|\s+$/g,""):n.trim(),n.length>0&&(r=t.indexOf(n[i?0:n.length-1])>-1),r};this.createEngine=function(){return new CalcEngine(this)};this.dispose=function(){this._dataStore=null;this._rowsToKeyMap=null;this._keyToRowsMap=null;this._keyToVectors=null;this._controlModifiedFlags=null;this._nameToControlMap=null;this.getDisposeEngineResource()&&(this._engine.getDependentFormulaCells().clear(),this._engine.getDependentCells().clear(),this._engine!=null&&this._engine.dispose(),this._engine=null)};this.tryParseAndCompute=function(n){var t="";try{t=this.parseAndCompute(n)}catch(i){t=i.message}return t};this.getFormula=function(n){return(n=n.toUpperCase(),this.getDataStore().containsKey(n))?this.getDataStore().getItem(n).getFormulaText():""};this.getValueRowCol=function(n,t){var u=this.getRowsToKeyMap().getItem(t).toString(),i=this.getKeyValue(u).toString(),r;return i!=null&&i[i.length-1]=="%"&&i.length>1&&(r=this._parseDouble(i.substring(0,i.length-1)),isNaN(r)&&(i=(Number(r)/100).toString())),i};this.initCalcQuick=function(n){this._dataStore=new HashTable;this._rowsToKeyMap=new HashTable;this._keyToRowsMap=new HashTable;this._keyToVectors=new HashTable;this._controlModifiedFlags=new HashTable;this._nameToControlMap=new HashTable;var t=CalcEngine.createSheetFamilyID();this._engine=this.createEngine();n&&(CalcEngine.resetSheetFamilyID(),this._engine.getDependentFormulaCells().clear(),this._engine.getDependentCells().clear());this._cellPrefix="!"+t+"!A";this._engine.registerGridAsSheet(RangeInfo.getAlphaLabel(this.getCalcQuickID()),this,t);this._engine.setCalculatingSuspended(!0);this._engine.ignoreValueChanged=!0;this._isIE8=ej.browserInfo().name=="msie"&&ej.browserInfo().version=="8.0"?!0:!1};this._markKeys=function(n){for(var t=n.indexOf(this._leftBracket),r,i,u,f;t>-1;)if(r=n.substring(t).indexOf(this._rightBracket)-1,i="",r>0)if(i=n.substring(t+1,r+t+1).toUpperCase(),this.getKeyToVectors().containsKey(i)){if(u=t+r+2<n.length?n.substring(t+r+2):"",this.getCheckKeys()&&!this._checkAdjacentPiece(u,this._validRightChars,!0))throw"not followed properly"+i;if(f=t>0?n.substring(0,t):"",this.getCheckKeys()&&!this._checkAdjacentPiece(f,this._validLeftChars,!1))throw"not followed properly"+i;n=f+this.getKeyToVectors().getItem(i).toString()+u;t=n.indexOf(this._leftbraket)}else if(this.getKeyToRowsMap().containsKey(i)){if(u=t+r+2<n.length?n.substring(t+r+2):"",this.getCheckKeys()&&!this._checkAdjacentPiece(u,this._validRightChars,!0))throw"not followed properly"+i;if(f=t>0?n.substring(0,t):"",this.getCheckKeys()&&!this._checkAdjacentPiece(f,this._validLeftChars,!1))throw"not followed properly"+i;n=f+"A"+this.getKeyToRowsMap().getItem(i).toString()+u;t=n.indexOf(this._leftBracket)}else if(n.toUpperCase().indexOf(this._tic+this._leftBracket+i+this._rightBracket+this.tic)>0)break;else throw"Unknown key: "+i;else t=-1;return n};this.parseAndCompute=function(n){return n.length>0&&n[0]==this.getEngine().getFormulaCharacter()&&(n=n.substring(1)),this.getEngine().parseAndComputeFormula(this._markKeys(n))};this.refreshAllCalculations=function(){var i,u;if(this.getAutoCalc()){for(this.setDirty(),this.ignoreChanges=!0,i=0;i<this.getDataStore().keys().length;i++){var r=this.getDataStore().keys()[i],n=this.getDataStore().getItem(r),t=n.getFormulaText();t.length>0&&t[0]==this.getEngine().getFormulaCharacter()&&n.calcID!=this.getEngine().getCalcID()&&(t=t.substring(1),this.getEngine().cell=this._cellPrefix+this.getKeyToRowsMap().getItem(r).toString(),n.setParsedFormula(this.getEngine().parseFormula(this._markKeys(t))),n.setFormulaValue(this.getEngine().computeFormula(n.getParsedFormula())),n.calcID=this.getEngine().getCalcID(),u=parseInt(this.getKeyToRowsMap().getItem(r)),this.getEngine().valueChanged(this,new ValueChangedArgs(u,1,n.getFormulaValue())));this._valueSetEvent!=null&&this._valueSetEvent.trigger(this,new ValueSetEventArgs(r,n.getFormulaValue(),2))}this.ignoreChanges=!1}};this.setDirty=function(){this.getEngine().updateCalcID()};this.setValueRowCol=function(){};this.updateDependencies=function(n){var u,t,i,r;if(this.getAutoCalc()&&(u=this._cellPrefix+this.getKeyToRowsMap().getItem(n).toString(),t=this.getEngine().getDependentCells().getItem(u),this.setDirty(),t!=null))for(i=0;i<t.length;i++)r=t[i].indexOf("A"),r>-1&&(r=parseInt(t[i].substring(r+1)),n=this.getRowsToKeyMap().getItem(r).toString(),this.ignoreChanges=!0,this.setKeyValue(n,this.getKeyValue(n)),this.ignoreChanges=!1)};this.initCalcQuick(n)}return n.prototype.getAutoCalc=function(){return this._autoCalc},n.prototype.setAutoCalc=function(n){this._autoCalc=n;this.getEngine().setCalculatingSuspended(!n);this.getEngine().setUseDependencies(n);n&&this.setDirty()},n.prototype.getCalcQuickID=function(){return this._calcQuickID++,this._calcQuickID==Number.MAX_VALUE&&(this._calcQuickID=1),this._calcQuickID},n.prototype.getCheckKeys=function(){return this._checkKeys},n.prototype.setCheckKeys=function(n){this._checkKeys=n},n.prototype.getControlModifiedFlags=function(){return this._controlModifiedFlags},n.prototype.getDataStore=function(){return this._dataStore},n.prototype.getDisposeEngineResource=function(){return this._disposeEngineResource},n.prototype.setDisposeEngineResource=function(n){this._disposeEngineResource=n},n.prototype.getEngine=function(){return this._engine},n.prototype.getFormulaCharacter=function(){return this.getEngine().getFormulaCharacter()},n.prototype.setFormulaCharacter=function(n){this.getEngine().setFormulaCharacter(n)},n.prototype.getKeyToRowsMap=function(){return this._keyToRowsMap},n.prototype.getKeyToVectors=function(){return this._keyToVectors},n.prototype.getNameToControlMap=function(){return this._nameToControlMap},n.prototype.getRowsToKeyMap=function(){return this._rowsToKeyMap},n.prototype.getThrowCircularException=function(){return this.getEngine().getThrowCircularException()},n.prototype.setThrowCircularException=function(n){this.getEngine().setThrowCircularException(n)},n.prototype.getValueSetEventHandler=function(){return this._valueSetEvent==null&&(this._valueSetEvent=new ValueSetEvent),this._valueSetEvent.getValueSet()},n.prototype.setValueSetEventHandler=function(n){this._valueSetEvent==null&&(this._valueSetEvent=new ValueSetEvent);this._valueSetEvent.setValueSet(n)},n}();window.CalcQuick=function(n){function t(){n.apply(this,arguments);this.registerControlArray=function(n){for(var t=0;t<n.length;t++)this.registerControl(n[t]);this.setAutoCalc(!0)};this.registerControl=function(n){var t;if(this.getNameToControlMap().length==0&&this.setValueSetEventHandler(this.calcQuickValueSet),this.getControlModifiedFlags().containsKey(n.id)||this.getNameToControlMap().containsKey(n.id))throw"error";this.getControlModifiedFlags().add(n.id,!1);this.getNameToControlMap().add(n.id.toUpperCase(),n.id);this.setKeyValue(n.id,n.value);n.type=="select-one"?(t=$.proxy(this.controlTextChanged,this,this),n.onchange=t):(t=$.proxy(this.controlTextChanged,this,this),this._isIE8?n.onpropertychange=t:n.oninput=t);t=$.proxy(this.controlLeave,this,this);n.onblur=t};this.calcQuickValueSet=function(n,t){if(n.getNameToControlMap().containsKey(t.getKey())){var i=n.getNameToControlMap().getItem(t.getKey());document.getElementById(i).value=t.getValue()}};this.controlTextChanged=function(n,t){var i=n._isIE8?event.srcElement:t.target;i!=null&&!n.ignoreChanges&&n.getControlModifiedFlags().containsKey(i.id)&&(n.getControlModifiedFlags().items[i.id]=!0)};this.controlLeave=function(n,t){var i=n._isIE8?event.srcElement:t.target;i!=null&&this.getControlModifiedFlags().containsKey(i.id)&&n.getControlModifiedFlags().getItem(i.id)&&(n.setKeyValue(i.id,i.value),n.getControlModifiedFlags().items[i.id]=!1)}}return window.__calcQuickextends(t,n),t}(CalcQuickBase);window.ValueSetEventArgs=function(){function n(n,t,i){this._id=n;this._val=t;this._action=i}return n.prototype.getAction=function(){return this._action},n.prototype.setAction=function(n){this._action=n},n.prototype.getKey=function(){return this._id},n.prototype.setKey=function(n){this._id=n},n.prototype.getValue=function(){return this._val},n.prototype.setValue=function(n){this._val=n},n}(),function(n){n[n.FormulaSet=0]="FormulaSet";n[n.NonFormulaSet=1]="NonFormulaSet";n[n.CalculatedValueSet=2]="CalculatedValueSet"}(FormulaInfoSetAction||(FormulaInfoSetAction={}));FormulaInfoHashtable=function(){function n(){this.get=function(){return new FormulaInfo};this.set=function(){return new FormulaInfo}}return n}();HashTable=function(){function n(){this.length=0;this.items=[];this.add=function(n,t){return this.previous=undefined,this.containsKey(n)?this.previous=this.items[n]:this.length++,this.items[n]=t,this.previous};this.clear=function(){this.items={};this.length=0};this.contains=function(n){return this.items.hasOwnProperty(n)};this.containsKey=function(n){return this.items.hasOwnProperty(n)};this.containsValue=function(n){return this.items.hasOwnProperty(n)&&this.items[n]!=undefined?!0:!1};this.getItem=function(n){return this.containsKey(n)?this.items[n]:undefined};this.keys=function(){var n=[];for(var t in this.items)this.containsKey(t)&&n.push(t);return n};this.remove=function(n){return this.containsKey(n)?(this.previous=this.items[n],this.length--,delete this.items[n],this.previous):undefined};this.values=function(){var n=[];for(var t in this.items)this.containsKey(t)&&n.push(this.items[t]);return n};this.each=function(n){for(var t in this.items)this.containsKey(t)&&n(t,this.items[t])};var n=undefined}return n}();window.RangeInfo=function(){function n(n,t,i,r){this.top=n;this.bottom=i;this.left=t;this.right=r;this.getBottom=function(){return this.bottom};this.setBottom=function(n){this.bottom=n};this.getTop=function(){return this.top};this.setTop=function(n){this.top=n};this.getLeft=function(){return this.left};this.setLeft=function(n){this.left=n};this.getRight=function(){return this.right};this.setRight=function(n){this.right=n}}return n.cells=function(t,i,r,u){return new n(t,i,r,u)},n.getAlphaLabel=function(n){for(var u=[],t=0,f,r,i;n>0&&t<9;)n--,f="A".charCodeAt(0),u[t]=String.fromCharCode(n%26+f),n=parseInt((n/26).toString()),t++;for(r=[],i=0;i<t;i++)r[t-i-1]=u[i];return r.join("")},n}();window.GridSheetFamilyItem=function(){function n(){this.isSheetMember=!1;this.parentObjectToToken=null;this.sheetDependentCells=null;this.sheetDependentFormulaCells=null;this.sheetFormulaInfoTable=null;this.sheetNameToParentObject=null;this.sheetNameToToken=null;this.tokenToParentObject=null}return n}();window.FormulaInfo=function(){function n(){this.calcID=Number.MIN_SAFE_INTEGER+1;this.getFormulaText=function(){return this._formulaText};this.setFormulaText=function(n){this._formulaText=n};this.getFormulaValue=function(){return this._formulaValue};this.setFormulaValue=function(n){this._formulaValue=n};this.getParsedFormula=function(){return this._parsedFormula};this.setParsedFormula=function(n){this._parsedFormula=n};var n=Number.MIN_SAFE_INTEGER+1}return n}();window.ValueChangedArgs=function(){function n(n,t,i){this.row=n;this.col=t;this.value=i;this.getRowIndex=function(){return this.row};this.setRowIndex=function(n){this.row=n};this.getColIndex=function(){return this.col};this.setColIndex=function(n){this.col=n};this.getValue=function(){return this.value};this.setValue=function(n){this.value=n}}return n}();window.FormulaParsing=function(){function n(n){this._text=n;this.getText=function(){return this._text};this.setText=function(n){this._text=n}}return n}();window.UnknownFunctionEventArgs=function(){function n(){this.getMissingFunctionName=function(){return this.m_missingFunctionName};this.setMissingFunctionName=function(n){this.m_missingFunctionName=n};this.getCellLocation=function(){return this.m_cellLocation};this.setCellLocation=function(n){this.m_cellLocation=n};this.getResult=function(){return this.result};this.setResult=function(n){this.result=n};this.getHandled=function(){return this.handled};this.setHandled=function(n){this.handled=n}}return n}();window.LookUps=function(){function n(){this.getLinearLookUpList=function(){return this._linearLookUpList};this.setLinearLookUpList=function(n){this._linearLookUpList=n};this.getMatchLookUpList=function(){return this._matchLookUpList};this.setMatchLookUpList=function(n){this._matchLookUpList=n}}return n}();window.ValueSetEvent=function(){function n(){}return n.prototype.getValueSet=function(){return this.eventFn},n.prototype.setValueSet=function(n){this.eventFn=n},n.prototype.trigger=function(){for(var i,t=[],n=0;n<arguments.length-0;n++)t[n]=arguments[n+0];i={};this.getValueSet().apply(i,t||[])},n}()});
