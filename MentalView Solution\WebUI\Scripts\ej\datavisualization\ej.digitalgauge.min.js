/*!
*  filename: ej.digitalgauge.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function(n,t,i){var u,r,f;t.widget({ejDigitalGauge:"ej.datavisualization.DigitalGauge"},{element:null,model:null,validTags:["div","span"],_rootCSS:"e-digitalgauge",defaults:{exportSettings:{mode:"client",type:"png",fileName:"DigitalGauge",action:""},segmentData:{"0":[0,1,2,3,4,5,14,15],"1":[1,2],"2":[0,14,1,6,8,4,3,15],"3":[0,1,2,3,6,8,14,15],"4":[1,2,5,6,8],"5":[0,2,3,5,6,8,14,15],"6":[0,2,3,4,5,6,8,14,15],"7":[0,1,2,14],"8":[0,1,2,3,4,5,6,8,14,15],"9":[0,1,2,3,5,6,8,14,15],A:[0,1,2,4,5,6,8,14],B:[0,1,2,3,7,9,8,14,15],C:[0,3,4,5,14,15],D:[0,1,2,3,7,9,14,15],E:[0,3,4,5,6,8,14,15],F:[0,4,5,6,8,14],G:[0,2,3,4,5,8,14,15],H:[1,2,4,5,6,8],I:[0,3,7,9,14,15],J:[1,2,3,4,15],K:[4,5,6,10,11],L:[3,4,5,15],M:[1,2,4,5,10,13],N:[1,2,4,5,11,13],O:[0,1,2,3,4,5,14,15],P:[0,1,4,5,6,8,14],Q:[0,1,2,3,4,5,11,14,15],R:[0,1,4,5,6,8,11,14],S:[0,2,3,5,6,8,14,15],T:[0,7,9,14],U:[1,2,3,4,5,15],V:[4,5,10,12],W:[1,2,4,5,11,12],X:[10,11,12,13],Y:[1,5,6,7,8],Z:[0,3,10,12,14,15]},matrixSegmentData:{"1":[0,3,0,4,1,1,1,2,1,3,1,4,2,3,2,4,3,3,3,4,4,3,4,4,5,3,5,4,6,1,6,2,6,3,6,4,6,5,6,6],"2":[0,1,0,2,0,3,0,4,0,5,1,5,1,6,2,5,2,6,3,4,3,5,4,3,4,2,5,2,5,1,6,1,6,2,6,3,6,4,6,5,6,6],"3":[0,1,0,2,0,3,0,4,0,5,1,5,1,6,2,5,2,6,3,2,3,3,3,4,3,5,3,6,4,5,4,6,5,5,5,6,6,1,6,2,6,3,6,4,6,5],"4":[0,3,0,4,0,5,1,2,1,3,1,4,1,5,2,1,2,2,2,4,2,5,3,0,3,1,3,4,3,5,4,0,4,1,4,2,4,3,4,4,4,5,4,6,5,4,5,5,6,4,6,5],"5":[0,1,0,2,0,3,0,4,0,5,0,6,1,1,1,2,2,1,2,2,3,1,3,2,3,3,3,4,3,5,3,6,4,5,4,6,5,5,5,6,6,1,6,2,6,3,6,4,6,5,6,6],"6":[0,3,0,4,0,5,0,6,1,2,1,3,2,1,2,2,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,4,3,4,6,4,7,5,2,5,3,5,6,5,7,6,3,6,4,6,5,6,6],"7":[0,1,0,2,0,3,0,4,0,5,0,6,0,7,1,6,1,7,2,5,2,6,3,4,3,5,4,3,4,4,5,2,5,3,6,1,6,2],"8":[0,2,0,3,0,4,0,5,0,6,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,2,3,3,3,4,3,5,3,6,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,2,6,3,6,4,6,5,6,6],"9":[0,2,0,3,0,4,0,5,1,1,1,2,1,5,1,6,2,1,2,2,2,4,2,5,2,6,3,2,3,3,3,4,3,5,3,6,4,5,4,6,5,5,5,6,6,2,6,3,6,4,6,4,6,5],"0":[0,2,0,3,0,4,0,5,0,6,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,2,6,3,6,4,6,5,6,6],a:[0,2,0,3,0,4,0,5,0,6,1,6,1,7,2,6,2,7,3,2,3,3,3,4,3,5,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,2,6,3,6,4,6,5,6,6,6,7],b:[0,1,0,2,1,1,1,2,2,1,2,2,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,4,3,4,6,4,7,5,1,5,2,5,6,5,7,6,1,6,2,6,3,6,4,6,5,6,6],c:[1,3,1,4,1,5,1,6,2,2,2,3,3,1,3,2,4,1,4,2,5,2,5,3,6,3,6,4,6,5,6,6],d:[0,6,0,7,1,6,1,7,2,6,2,7,3,2,3,3,3,4,3,5,3,6,3,7,4,1,4,2,4,5,4,6,4,7,5,1,5,2,5,6,5,7,6,2,6,3,6,4,6,5,6,6,6,7],e:[0,2,0,3,0,4,0,5,0,6,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,5,1,5,2,6,2,6,3,6,4,6,5,6,6,6,7],f:[0,4,0,5,0,6,0,7,1,3,1,4,2,3,2,4,3,1,3,2,3,3,3,4,3,5,3,6,3,7,4,3,4,4,5,3,5,4,6,3,6,4],g:[0,2,0,3,0,4,0,5,0,6,0,7,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,2,3,3,3,4,3,5,3,6,3,7,4,6,4,7,5,6,5,7,6,2,6,3,6,4,6,5,6,6],h:[0,1,0,2,1,1,1,2,2,1,2,2,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,4,3,4,6,4,7,5,1,5,2,5,6,5,7,6,1,6,2,6,6,6,7],i:[0,3,0,4,2,1,2,2,2,3,2,4,3,3,3,4,4,3,4,4,5,3,5,4,6,3,6,4],j:[1,5,1,6,2,5,2,6,3,5,3,6,4,1,4,2,4,5,4,6,5,1,5,2,5,5,5,6,6,2,6,3,6,4,6,5],k:[0,1,0,2,1,1,1,2,1,4,1,5,2,1,2,2,2,3,2,4,3,1,3,2,3,3,4,1,4,2,4,3,4,4,4,5,5,1,5,2,5,5,5,6,6,1,6,2,6,6,6,7],l:[0,2,0,3,0,4,0,5,0,6,1,5,1,6,2,5,2,6,3,5,3,6,4,5,4,6,5,5,5,6,6,5,6,6],m:[0,1,0,2,0,3,0,4,0,5,0,6,1,0,1,1,1,2,1,3,1,4,1,5,1,6,1,7,2,0,2,1,2,3,2,4,2,6,2,7,3,0,3,1,3,3,3,4,3,6,3,7,4,0,4,1,4,3,4,4,4,6,4,7,5,0,5,1,5,3,5,4,5,6,5,7,6,0,6,1,6,3,6,4,6,6,6,7],n:[1,1,1,2,1,3,1,4,1,5,1,6,2,0,2,1,2,2,2,6,2,7,3,0,3,1,3,6,3,7,4,0,4,1,4,6,4,7,5,0,5,1,5,6,5,7,6,0,6,1,6,6,6,7],o:[1,2,1,3,1,4,1,5,2,1,2,2,2,5,2,6,3,1,3,2,3,5,3,6,4,1,4,2,4,5,4,6,5,1,5,2,5,5,5,6,6,2,6,3,6,4,6,5],p:[1,1,1,2,1,3,1,4,1,5,1,6,2,1,2,2,2,3,2,6,2,7,3,1,3,2,3,6,3,7,4,1,4,2,4,3,4,4,4,5,4,6,5,1,5,2,6,1,6,2],q:[0,2,0,3,0,4,0,5,0,6,0,7,1,1,1,2,1,5,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,5,3,6,3,7,4,2,4,3,4,4,4,5,4,6,4,7,5,6,5,7,6,6,6,7],r:[0,1,0,3,0,4,0,5,1,1,1,2,1,3,1,4,1,5,1,6,2,1,2,2,2,6,3,1,3,2,4,1,4,2,5,1,5,2,6,1,6,2],s:[1,2,1,3,1,4,1,5,2,1,2,2,3,1,3,2,4,3,4,4,5,4,5,5,6,1,6,2,6,3,6,4],t:[0,3,0,4,1,3,1,4,2,1,2,2,2,3,2,4,2,5,2,6,3,3,3,4,4,3,4,4,5,3,5,4,6,4,6,5,6,6,6,7],u:[1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,5,5,6,5,7,6,2,6,3,6,4,6,5,6,6,6,7],v:[1,1,1,2,1,6,1,7,2,2,2,3,2,5,2,6,3,2,3,3,3,5,3,6,4,3,4,4,4,5,5,4,5,5,6,4],w:[0,0,0,1,0,6,0,7,1,0,1,1,1,6,1,7,2,0,2,1,2,3,2,4,2,6,2,7,3,0,3,1,3,3,3,4,3,6,3,7,4,0,4,1,4,3,4,4,4,6,4,7,5,0,5,1,5,2,5,3,5,4,5,5,5,6,5,7,6,1,6,2,6,3,6,4,6,5,6,6],x:[1,1,1,2,1,6,1,7,2,2,2,3,2,5,2,6,3,3,3,4,3,5,4,3,4,4,4,5,5,2,5,3,5,5,5,6,6,1,6,2,6,6,6,7],y:[1,1,1,2,1,5,1,6,2,1,2,2,2,5,2,6,3,2,3,3,3,4,3,5,4,3,4,4,5,2,5,3,6,1,6,2],z:[1,2,1,3,1,4,1,5,1,6,1,7,2,6,2,7,3,5,3,6,4,4,4,5,5,3,5,4,6,2,6,3,6,4,6,5,6,6,6,7],A:[0,3,0,4,1,2,1,3,1,4,1,5,2,2,2,3,2,4,2,5,3,1,3,2,3,5,3,6,4,1,4,2,4,3,4,4,4,5,4,6,5,1,5,2,5,5,5,6,6,0,6,1,6,6,6,7],B:[0,1,0,2,0,3,0,4,0,5,0,6,1,1,1,2,1,3,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,4,3,4,6,4,7,5,1,5,2,5,6,5,7,6,1,6,2,6,3,6,4,6,5,6,6],C:[0,2,0,3,0,4,0,5,0,6,1,1,1,2,2,0,2,1,3,0,3,1,4,0,4,1,5,1,5,2,6,2,6,3,6,4,6,5,6,6],D:[0,1,0,2,0,3,0,4,0,5,0,6,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,1,6,2,6,3,6,4,6,5,6,6],E:[0,1,0,2,0,3,0,4,0,5,0,6,1,1,1,2,2,1,2,2,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,5,1,5,2,6,1,6,2,6,3,6,4,6,5,6,6],F:[0,1,0,2,0,3,0,4,0,5,0,6,1,1,1,2,2,1,2,2,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,5,1,5,2,6,1,6,2],G:[0,2,0,3,0,4,0,5,0,6,1,1,1,2,2,0,2,1,3,0,3,1,3,4,3,5,3,6,4,0,4,1,4,6,5,1,5,2,5,6,6,2,6,3,6,4,6,5,6,6],H:[0,1,0,2,0,6,0,7,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,3,3,4,3,5,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,1,6,2,6,6,6,7],I:[0,2,0,3,0,4,0,5,0,6,1,4,2,4,3,4,4,4,5,4,6,1,6,2,6,3,6,4,6,5,6,6,6,7],J:[0,2,0,3,0,4,0,5,0,6,1,5,1,6,2,5,2,6,3,5,3,6,4,1,4,2,4,5,4,6,5,1,5,2,5,5,5,6,6,2,6,3,6,4,6,5],K:[0,1,0,2,0,5,0,6,1,1,1,2,1,4,1,5,2,1,2,2,2,3,2,4,3,1,3,2,3,3,4,1,4,2,4,3,4,4,5,1,5,2,5,4,5,5,6,1,6,2,6,5,6,6],L:[0,1,0,2,1,1,1,2,2,1,2,2,3,1,3,2,4,1,4,2,5,1,5,2,6,1,6,2,6,3,6,4,6,5,6,6],M:[0,1,0,2,0,6,0,7,1,1,1,2,1,3,1,5,1,6,1,7,2,1,2,2,2,4,2,6,2,7,3,1,3,2,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,1,6,2,6,6,6,7],N:[0,1,0,2,0,6,0,7,1,1,1,2,1,3,1,6,1,7,2,1,2,2,2,4,2,6,2,7,3,1,3,2,3,5,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,1,6,2,6,6,6,7],O:[0,2,0,3,0,4,0,5,1,1,1,2,1,5,1,6,2,0,2,1,2,6,2,7,3,0,3,1,3,6,3,7,4,0,4,1,4,6,4,7,5,1,5,2,5,5,5,6,6,2,6,3,6,4,6,5],P:[0,1,0,2,0,3,0,4,0,5,0,6,1,1,1,2,1,3,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,5,1,5,2,6,1,6,2],Q:[0,2,0,3,0,4,0,5,1,1,1,2,1,5,1,6,2,0,2,1,2,6,2,7,3,0,3,1,3,6,3,7,4,0,4,1,4,4,4,6,4,7,5,1,5,2,5,5,5,6,6,2,6,3,6,4,6,5,6,6,6,7],R:[0,1,0,2,0,3,0,4,0,5,0,6,1,1,1,2,1,3,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,3,3,4,3,5,3,6,4,1,4,2,4,5,5,1,5,5,6,2,6,1,6,2,6,6],S:[0,2,0,3,0,4,0,5,0,6,1,1,1,2,2,1,2,2,3,2,3,3,3,4,3,5,4,5,4,6,5,5,5,6,6,1,6,2,6,3,6,4,6,5,6,6],T:[0,1,0,2,0,3,0,4,0,5,0,6,0,7,1,4,2,4,3,4,4,4,5,4],U:[0,1,0,2,0,6,0,7,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,6,3,7,4,1,4,2,4,6,4,7,5,1,5,2,5,6,5,7,6,2,6,3,6,4,6,5,6,6],V:[0,0,0,1,0,6,0,7,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,2,3,3,3,5,3,6,4,3,4,4,4,5,4,6,5,4,5,5,6,4],W:[0,1,0,2,0,6,0,7,1,1,1,2,1,6,1,7,2,1,2,2,2,6,2,7,3,1,3,2,3,6,3,7,4,1,4,2,4,4,4,6,4,7,5,1,5,2,5,3,5,5,5,6,5,7,6,1,6,2,6,6,6,7],X:[0,0,0,1,0,6,0,7,1,1,1,2,1,5,1,6,2,1,2,2,2,5,2,6,3,2,3,3,3,4,3,5,4,2,4,3,4,4,4,5,5,1,5,2,5,5,5,6,6,0,6,1,6,6,6,7],Y:[0,0,0,1,0,6,0,7,1,0,1,1,1,6,1,7,2,1,2,2,2,5,2,6,3,2,3,3,3,4,3,5,4,3,4,4,5,3,5,4,6,3,6,4],Z:[0,1,0,2,0,3,0,4,0,5,0,6,0,7,1,6,1,7,2,5,2,6,3,4,3,5,4,2,4,3,5,1,5,2,6,1,6,2,6,3,6,4,6,5,6,6,6,7],",":[5,3,5,4,5,5,6,4,6,5,7,3,7,4],":":[1,3,1,4,1,5,2,3,2,4,2,5,4,3,4,4,4,5,5,3,5,4,5,5],"%":[0,6,0,7,1,1,1,2,1,5,1,6,2,1,2,2,2,4,2,5,3,3,3,4,4,2,4,3,5,1,5,2,5,4,5,5,6,0,6,1,6,4,6,5],"!":[0,3,0,4,0,5,1,3,1,4,1,5,2,3,2,4,2,5,3,3,3,4,3,5,4,3,4,4,4,5,5,3,5,4,5,5,7,4],"(":[0,2,0,3,1,1,1,2,2,1,2,2,3,1,3,2,4,1,4,2,5,1,5,2,6,1,6,2,7,2,7,3],")":[0,5,0,6,1,6,1,7,2,6,2,7,3,6,3,7,4,6,4,7,5,6,5,7,6,6,6,7,7,5,7,6],".":[5,3,5,4,5,5,6,3,6,4,6,5,7,3,7,4,7,5]},frame:{backgroundImageUrl:null,innerWidth:6,outerWidth:10},height:150,width:400,enableResize:!1,isResponsive:!1,themes:"flatlight",items:null,init:null,load:null,doubleClick:"",rightClick:"",click:"",renderComplete:null,itemRendering:null,value:"text",themeProperties:{flatlight:{items:{segmentSettings:{color:"#232323"},shadowColor:"#232323",textColor:"#232323"}},flatdark:{items:{segmentSettings:{color:"#b1b0b0"},shadowColor:"#b1b0b0",textColor:"#b1b0b0"}}}},dataTypes:{segmentData:"data",matrixSegmentData:"data",items:"array",isResponsive:"boolean"},_setValues:function(){this.gaugeEl=this.element;this.segmentCount=null;this.contextEl=null;this.style=null;this._value=null;this.region=null;this.canvasEl=null;this.segement16X=null;this.segment16Y=null;this.segmentHeight=null;this.segmentAngle=null;this.startX=5;this.startY=5;this.gradient=null;this.itemIndex=null;this.characterSpace=null;this.outerImage=null;this.radius=null;this.frameOuterLocation=null;this.frameInnerLocation=null;this.glassFrameLocation=null;this.glassFrameStyle=null;this.frameOuterStyle=null;this.character=null;this.frameInnerStyle=null;this._itemInitialize()},observables:["value"],_tags:[{tag:"items",attr:["textAlign","textColor","characterSettings.count","characterSettings.opacity","characterSettings.spacing","characterSettings.type","enableCustomFont","segmentSettings.color","segmentSettings.gradient","segmentSettings.length","segmentSettings.opacity","segmentSettings.spacing","segmentSettings.width","shadowBlur","shadowOffsetX","shadowOffsetY","textAlign","shadowColor","textColor","font.size","font.fontFamily","font.fontStyle","position.x","position.y"]}],value:t.util.valueFunction("value"),_destroy:function(){this._unwireEvents();this.element.empty().removeClass("e-digitalgauge e-js e-widget")},_setModel:function(t){var i,r;for(i in t)switch(i){case"height":this.model.height=t[i];break;case"width":this.model.width=t[i];break;case"items":this.model.items=t[i];this._itemInitialize();break;case"frame":n.extend(this.model.frame,t[i]);break;case"themes":this.model.themes=t[i];break;case"value":for(r=0;this.model.items[r]!=null;r++)this.model.items[r].value=this.value()}this.refresh()},_init:function(){r=n(".e-digitalgauge").length;f=r;this._setValues();this._trigger("load");this._setTheme();this._initialize();this._onWindowResize()},_onWindowResize:function(){(this.model.enableResize||this.model.isResponsive)&&(t.isTouchDevice()?this._on(n(window),"orientationchange",this.resizeCanvas):this._on(n(window),"resize",this.resizeCanvas))},_setTheme:function(){var n=this.model.themeProperties[this.model.themes];this._setThemeColors(n)},_setThemeColors:function(n){var u=[],f=this.model.themeProperties,e,r,i;for(e in f)u.push(e);for(r=0;r<u.length;r++)for(i=0;i<this.model.items.length;i++)this.model.items[i].segmentSettings.color=t.isNullOrUndefined(this.model.items[i].segmentSettings.color)||this.model.items[i].segmentSettings.color==f[u[r]].items.segmentSettings.color?n.items.segmentSettings.color:this.model.items[i].segmentSettings.color,this.model.items[i].shadowColor=!this.model.items[i].shadowColor||this.model.items[i].shadowColor==f[u[r]].items.shadowColor?n.items.shadowColor:this.model.items[i].shadowColor,this.model.items[i].textColor=!this.model.items[i].textColor||this.model.items[i].textColor==f[u[r]].items.textColor?n.items.textColor:this.model.items[i].textColor},_wireEvents:function(){this._on(n(this.canvasEl),"touchstart",this._dgStart);this._on(n(this.canvasEl),t.isTouchDevice()?"touchend":"mouseup",this._dgClick);this._on(n(this.canvasEl),"contextmenu",this._dgRightClick)},_unwireEvents:function(){this._off(n(this.canvasEl),"touchstart",this._dgStart);this._off(n(this.canvasEl),t.isTouchDevice()?"touchend":"mouseup",this._dgClick);this._off(n(this.canvasEl),"contextmenu",this._dgRightClick)},_initialize:function(){this.model.init&&this._clientSideOnInit();this._initObject(this);this.model.load&&this._clientSideOnLoad();this.model.frame.backgroundImageUrl!=null?this._drawCustomImage(this,this.model.frame.backgroundImageUrl):this._renderItems();this.model.renderComplete&&this._clientSideOnRenderComplete();this._wireEvents()},_dgStart:function(){t.isTouchDevice()&&this.model.rightClick!=""&&(this._longPressTimer=new Date)},_dgClick:function(n){var i=new Date;this.model.click!=""&&this._trigger("click",{data:{event:n}});this._doubleTapTimer!=null&&i-this._doubleTapTimer<300&&this._dgDoubleClick(n);this._doubleTapTimer=i;t.isTouchDevice()&&this.model.rightClick!=""&&new Date-this._longPressTimer>1500&&this._dgRightClick(n)},_dgDoubleClick:function(n){this.model.doubleClick!=""&&this._trigger("doubleClick",{data:{event:n}})},_dgRightClick:function(n){this.model.rightClick!=""&&this._trigger("rightClick",{data:{event:n}})},_itemInitialize:function(){var t=this;this.model.items!=null?n.each(this.model.items,function(i,r){var u=t._sendDefaultItem();n.extend(!0,u,r);n.extend(!0,r,u)}):this.model.items=[this._sendDefaultItem()]},_sendDefaultItem:function(){return{characterSettings:{count:4,opacity:1,spacing:2,type:t.datavisualization.DigitalGauge.CharacterType.EightCrossEightDotMatrix},enableCustomFont:!1,segmentSettings:{color:null,gradient:null,length:2,opacity:0,spacing:1,width:1},shadowBlur:0,shadowOffsetX:1,shadowOffsetY:1,textAlign:"left",font:{size:"11px",fontFamily:"Arial",fontStyle:"italic"},position:{x:0,y:0},shadowColor:null,textColor:null,value:null}},_initObject:function(t){var i,e;for(this.element.addClass("e-widget"),t.gaugeEl=this.element,i=0;this.model.items[i]!=null;i++)this.model.items[i].value==null&&(this.model.items[i].value=this.value());(t.canvasEl&&t.canvasEl.parent().empty(),t.canvasEl=n("<canvas><\/canvas>"),t.gaugeEl.append(t.canvasEl),t.canvasEl.attr("role","presentation"),r==f&&(u=window.innerWidth),t.canvasEl[0].setAttribute("width",t.model.width),t.canvasEl[0].setAttribute("height",t.model.height),t.centerX=t.canvasEl[0].width/2,t.centerY=t.canvasEl[0].height/2,e=t.canvasEl[0],typeof G_vmlCanvasManager!="undefined"&&(e=window.G_vmlCanvasManager.initElement(e)),e&&e.getContext)&&(t.contextEl=t.canvasEl[0].getContext("2d"))},_drawCustomImage:function(t,i){var r=new Image;n(r).on("load",function(){t.contextEl.drawImage(this,0,0,t.model.width,t.model.height);t.model.Scales!=null&&t._drawScales();t.model.items!=null&&t._renderItems()}).attr("src",i)},_setSegmentCount:function(n){switch(n){case"sevensegment":this._SegmentCount=7;break;case"fourteensegment":this._SegmentCount=14;break;case"sixteensegment":this._SegmentCount=16;break;case"eightcrosseightdotmatrix":this._SegmentCount=[8,8];break;case"eightcrosseightsquarematrix":this._SegmentCount=[8,8];break;default:this._SegmentCount=7}},_setInnerPosition:function(){this.contextEl.save();this.contextEl.translate(this.model.frame.outerWidth+this.model.frame.innerWidth,this.model.frame.outerWidth+this.model.frame.innerWidth);this.bounds={height:this.canvasEl[0].height-2*(this.model.frame.outerWidth+this.model.frame.innerWidth),width:this.canvasEl[0].width-2*(this.model.frame.outerWidth+this.model.frame.innerWidth)}},_setWidth:function(){var t=[];this.model.items!=null&&n.each(this.model.items,function(n,i){t.push(i.characterSettings.count)})},_renderItems:function(){if(this.model.items!=null){this._setInnerPosition();var t=this;n.each(this.model.items,function(n,i){var r=i.characterSettings.type.toLowerCase();t._setSegmentCount(r);t.itemIndex=n;t.canvasEl.attr("aria-label",i.value);t._setShadow(n,i);i.enableCustomFont?t._setCustomFont(n,i):r.indexOf("matrix")!=-1?t._drawMatrixSegments(n,i):t._drawSegments(n,i)})}},_setGradientColor:function(t,i,r){r.Name||typeof r=="string"?(i.addColorStop(0,this._getColor(t,r)),i.addColorStop(1,this._getColor(t,r))):n.each(r,function(n,r){i.addColorStop(r.colorStop!=NaN?r.colorStop:0,typeof r.color=="string"?r.color:this._getColor(t,r.color))})},_getColor:function(n,t){return typeof t=="string"?t:"rgba("+t.R+", "+t.G+","+t.B+", "+t.A/255+")"},_drawMatrixSegments:function(t,i){var s=[],u=[],o=i.characterSettings.type.toLowerCase(),h,c,f,r,e;for(i.value?(this._value=i.value.toString().split(""),i.characterSettings.count=this._value.length>4?this._value.length:4):this._value="",this.radius=o.indexOf("dot")!=-1?(i.segmentSettings.length+i.segmentSettings.width)/2:i.segmentSettings.width/2,h=this.startX=(this.bounds.width-i.characterSettings.count*(this._SegmentCount[0]*2*this.radius+i.characterSettings.spacing+this._SegmentCount[0]*i.segmentSettings.spacing))*(i.position.x/100),c=this.startY=(this.bounds.height-(this._SegmentCount[1]*(o.indexOf("dot")!=-1?2*this.radius:i.segmentSettings.length)+this._SegmentCount[1]*i.segmentSettings.spacing))*(i.position.y/100),f=0;f<i.characterSettings.count;f++)for(this._value&&(this.character=i.textAlign=="right"?this._value[this._value.length-i.characterSettings.count+f]:this._value[f],u=this.model.matrixSegmentData[this.character]),f!=0&&(h=this.startX=this.startX+i.characterSettings.spacing+i.segmentSettings.spacing+2*this.radius,this.startY=c),r=0;r<this._SegmentCount[1];r++){for(r!=0&&(this.startY=(o.indexOf("dot")!=-1?2*this.radius:i.segmentSettings.length)+this.startY+i.segmentSettings.spacing,this.startX=h),u&&n.each(u,function(n){if(n%2==0){if(u[n]>r)return!1;u[n]==r&&s.push(parseInt(u[n+1]))}}),e=0;e<this._SegmentCount[0];e++)e!=0&&(this.startX=this.startX+2*this.radius+i.segmentSettings.spacing),this.gradient=o.indexOf("dot")!=-1?this.contextEl.createRadialGradient(0,0,0,0,0,this.radius):this.contextEl.createLinearGradient(0,0,i.segmentSettings.width,0),i.segmentSettings.gradient&&this._setGradientColor(this,this.gradient,i.segmentSettings.gradient.colorInfo),this.region={startX:this.startX,startY:this.startY},this.style={opacity:s&&n.inArray(e,s)!=-1?i.characterSettings.opacity:i.segmentSettings.opacity,height:i.segmentSettings.length,width:i.segmentSettings.width,fillStyle:i.segmentSettings.color=="transparent"?"rgba(0,0,0,0)":this._getColor(this,i.segmentSettings.color),skewX:i.SkewAngleX,skewY:i.SkewAngleX},this.model.itemRendering&&this._clientSideOnItemRendering(!0,e,r),o.indexOf("dot")!=-1?this._drawDot(this.region,this.style):this._drawSquare(this.region,this.style);s=[]}},_drawSegments:function(t,i){var f=[],e=i.characterSettings.type.toLowerCase(),u,r;for(i.value&&(this._value=i.value.toUpperCase().toString().split(""),i.characterSettings.count=this._value.length>4?this._value.length:4),this.characterSpace=e=="sevensegment"?2*i.segmentSettings.width:4*i.segmentSettings.width,this._renderSegmentCalculation(i),this.gradient=this.contextEl.createLinearGradient(0,0,0,i.segmentSettings.width),i.segmentSettings.color?this._setGradientColor(this,this.gradient,i.segmentSettings.color):i.segmentSettings.gradient&&this._setGradientColor(this,this.gradient,i.segmentSettings.gradient.colorInfo),u=0;u<i.characterSettings.count;u++){for(i.value&&(f=this.model.segmentData[i.textAlign=="right"?this._value[this._value.length-i.characterSettings.count+u]:this._value[u]]),r=0;r<this._SegmentCount;r++)u!=0&&(this.segment16X[r]=this.segment16X[r]+i.segmentSettings.length+this.characterSpace+i.characterSettings.spacing),this._value&&(this.character=i.textAlign=="right"?this._value[this._value.length-i.characterSettings.count+u]:this._value[u]),this.region={startX:this.segment16X[r],startY:this.segment16Y[r]},this.style={angle:this.angle[r],fillStyle:this.gradient,isStroke:!1,isFill:!0,characterHeight:e=="sevensegment"?i.segmentSettings.length:this.segmentHeight[r],segmentWidth:i.segmentSettings.width,opacity:f&&n.inArray(r,f)!=-1?i.characterSettings.opacity:i.segmentSettings.opacity},this.model.itemRendering&&this._clientSideOnItemRendering(!1,r),this._drawSegmentLayers(this.region,this.style);this._notifyArrayChange&&this._notifyArrayChange("items["+t+"]value",i.value);this.value(i.value);f=[]}},_setCustomFont:function(n,t){this.startX=(this.bounds.width-this._measureText(t.value,0,this._getFontString(this,t.font)).width)*(t.position.x/100);this.startY=(this.bounds.height-this._measureText(t.value,0,this._getFontString(this,t.font)).height)*(t.position.y/100);this.region={startX:this.startX,startY:this.startY};this.style={font:this._getFontString(this,t.font),text:t.value,textColor:t.textColor?t.textColor=="transparent"?"rgba(0,0,0,0)":this._getColor(this,t.textColor):t.segmentSettings.color=="transparent"?"rgba(0,0,0,0)":this._getColor(this,t.segmentSettings.color)};this.model.itemRendering&&this._clientSideOnItemRendering(!1);this._drawText(this.region,this.style)},_getFontString:function(n,t){return(t.size==null?"11px":t.size)+" "+t.fontFamily+" "+(t.fontStyle?t.fontStyle:"")},_renderSegmentCalculation:function(n){var t=n.segmentSettings.length,i=n.segmentSettings.width,u=n.characterSettings.type.toLowerCase(),r;this.startX=(this.bounds.width-n.characterSettings.count*(t+this.characterSpace+n.characterSettings.spacing))*(n.position.x/100);this.startY=(this.bounds.height-2*t-i)*(n.position.y/100);r=u=="sevensegment"?t:t/2;this.segment16X=[this.startX+i/2,this.startX+t+4*i,this.startX+t+4*i,this.startX+i/2,this.startX,this.startX,this.startX+i/2,this.startX+r+2*i,this.startX+2.5*i+r,this.startX+r+2*i,this.startX+t+2.5*i,this.startX+r+2.5*i,this.startX+r+1.5*i,this.startX+1.5*i,this.startX+5*i/2+r,this.startX+2.5*i+r];this.segment16Y=[this.startY,this.startY,this.startY+t+i,this.startY+2*t+2*i,this.startY+t+i,this.startY,this.startY+t+i,this.startY+t+i,this.startY+t+i,this.startY,this.startY+i,this.startY+t+i,this.startY+t+i,this.startY+i,this.startY,this.startY+2*t+2*i];this.segmentHeight=[t/2,t,t,t/2,t,t,t/2,t,t/2,t,t,t,t,t,t/2,t/2];this.angle=[-90,0,0,-90,0,0,-90,0,-90,0,27,-27,27,-27,-90,-90];u=="sevensegment"&&(this.segment16X[2]=this.segment16X[1]=this.startX+t+2*i);u=="fourteensegment"&&(this.segmentHeight[3]=this.segmentHeight[0]=t+2*i)},_drawSegmentLayers:function(n,t){this._contextOpenPath(t,this);this.contextEl.translate(n.startX,n.startY);this.contextEl.rotate(Math.PI*(t.angle/180));this.contextEl.lineTo(0,0);this.contextEl.lineTo(-t.segmentWidth,t.segmentWidth);this.contextEl.lineTo(-t.segmentWidth,t.characterHeight);this.contextEl.lineTo(0,t.characterHeight+t.segmentWidth);this.contextEl.lineTo(t.segmentWidth,t.characterHeight);this.contextEl.lineTo(t.segmentWidth,t.segmentWidth);this._contextClosePath(t,this)},_drawDot:function(n,t){this.contextEl.beginPath();this.contextEl.save();this.contextEl.translate(n.startX,n.startY);this.contextEl.fillStyle=t.fillStyle;this.contextEl.globalAlpha=t.opacity;this.contextEl.arc(0,0,this.radius,0,2*Math.PI,!0);this.contextEl.fill();this.contextEl.closePath();this.contextEl.restore()},_setShadow:function(n,t){this.contextEl.save();this.contextEl.shadowColor=t.shadowColor=="transparent"?"rgba(0,0,0,0)":this._getColor(this,t.shadowColor);this.contextEl.shadowOffsetY=t.shadowOffsetY;this.contextEl.shadowOffsetX=t.shadowOffsetX;this.contextEl.shadowBlur=t.shadowBlur},_drawSquare:function(n,t){this.contextEl.beginPath();this.contextEl.save();this.contextEl.translate(n.startX,n.startY);this.contextEl.fillStyle=t.fillStyle;this.contextEl.globalAlpha=t.opacity;this.contextEl.rect(0,0,t.width,t.height);this.contextEl.fill();this.contextEl.closePath();this.contextEl.restore()},_drawText:function(n,t){this.contextEl.beginPath();this.contextEl.save();this.contextEl.font=t.font;this.contextEl.textBaseline="hanging";this.contextEl.fillStyle=t.textColor=="transparent"?"rgba(0,0,0,0)":t.textColor;this.contextEl.fillText(t.text,n.startX,n.startY);this.contextEl.closePath();this.contextEl.restore()},setValue:function(n,t){n<this.model.items.length&&t!=null&&(this.model.items[n].value=t,this._initialize())},getValue:function(n){return this.model.items[n].value},setPosition:function(n,t){n<this.model.items.length&&t!=null&&(this.model.items[n].position.x=t.x,this.model.items[n].position.y=t.y,this._initialize())},getPosition:function(n){return n<this.model.items.length?{x:this.model.items[n].position.x,y:this.model.items[n].position.y}:null},refresh:function(){this._setTheme();this._initialize()},"export":function(){var i=this.model.exportSettings,u,f,e,r,o,s;i.mode.toLowerCase()==="client"?this.exportImage(i.fileName,i.fileType):(f=i.type.toLowerCase()==="jpg"?"image/jpeg":"image/png",u=this.canvasEl[0].toDataURL(f),e={action:i.action,method:"post"},r=t.buildTag("form","",null,e),o={name:"Data",type:"hidden",value:u},s=t.buildTag("input","",null,o),r.append(s).append(this),n("body").append(r),r.submit())},exportImage:function(n,i){var f,u,o,r,c,s;if(t.browserInfo().name==="msie"&&parseFloat(t.browserInfo().version)<10)return!1;f=this.canvasEl[0].toDataURL();f=f.replace(/^data:[a-z]*;,/,"");var l=f.split(","),e=atob(l[1]),h=new ArrayBuffer(e.length),a=new Uint8Array(h);for(u=0;u<e.length;u++)a[u]=e.charCodeAt(u);return o=new Blob([h],{type:"image/png"}),t.browserInfo().name==="msie"?window.navigator.msSaveOrOpenBlob(o,n+"."+i):(r=document.createElement("a"),c=URL.createObjectURL(o),r.href=c,r.setAttribute("download",n+"."+i),document.createEvent?(s=document.createEvent("MouseEvents"),s.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),r.dispatchEvent(s)):r.fireEvent&&r.fireEvent("onclick")),!0},resizeCanvas:function(){var e,o,f,i;if(r=r!=0?r-1:n(".e-digitalgauge").length-1,o=!0,t.isNullOrUndefined(this.canvasEl.parent().attr("style"))||(e=this.GaugeEl.parent().attr("style").split(";")),t.isNullOrUndefined(e)||n.each(e,function(n,t){while(t.indexOf("width")!=-1){o=t.indexOf("px")==-1?!0:!1;break}}),o){for(f=window.innerWidth/u,this.model.width*=f,i=0;this.model.items[i]!=null;i++)this.model.items[i].segmentSettings.width*=f,this.model.items[i].segmentSettings.length*=f,this.model.items[i].segmentSettings.spacing*=f,this.model.items[i].characterSettings.spacing*=f;this.refresh();r==0&&(u=window.innerWidth)}},_clientSideOnLoad:function(){var n={object:this,items:this.model.items,context:this.contextEl};this._trigger("load",n)},_clientSideOnItemRendering:function(n,t,i){var r;r=n?{object:this,items:this.model.items,character:this.character,context:this.contextEl,position:this.region,style:this.style,row:t,column:i}:{object:this,model:this.model,id:this.model.ClientId,items:this.model.items,character:this.character,context:this.contextEl,position:this.region,style:this.style,segment:t};this._trigger("itemRendering",r)},_clientSideOnInit:function(){var n={object:this,items:this.model.items,context:this.contextEl};this._trigger("init",n)},_clientSideOnRenderComplete:function(){var n={object:this,items:this.model.items,context:this.contextEl};this._trigger("renderComplete",n)},_contextOpenPath:function(n,t){t.contextEl.save();t.contextEl.beginPath();n.strokeStyle&&(t.contextEl.strokeStyle=n.strokeStyle);n.opacity!=i&&(t.contextEl.globalAlpha=n.opacity);n.lineWidth&&(t.contextEl.lineWidth=n.lineWidth);n.fillStyle&&(t.contextEl.fillStyle=n.fillStyle)},_contextClosePath:function(n,t){t.contextEl.closePath();n.isFill&&t.contextEl.fill();n.isStroke&&t.contextEl.stroke();t.contextEl.restore()},_measureText:function(t,i,r){var u=document.createElement("DIV"),f;return u.innerHTML=t,r!=null&&(u.style.font=r),u.style.backgroundColor="white",u.style.position="absolute",u.style.top=-100,u.style.left=0,i&&(u.style.maxwidth=i+"px"),document.body.appendChild(u),f={width:u.offsetWidth,height:u.offsetHeight},n(u).remove(),f}});t.datavisualization.DigitalGauge.CharacterType={SevenSegment:"sevensegment",FourteenSegment:"fourteensegment",SixteenSegment:"sixteensegment",EightCrossEightDotMatrix:"eightcrosseightdotmatrix",EightCrossEightSquareMatrix:"eightcrosseightsquarematrix"};t.datavisualization.DigitalGauge.TextAlign={Left:"left",Right:"right"};t.datavisualization.DigitalGauge.FontStyle={Normal:"normal",Bold:"bold",Italic:"italic",Underline:"underline",Strikeout:"strikeout"};t.datavisualization.DigitalGauge.Themes={FlatLight:"flatlight",FlatDark:"flatdark"}})(jQuery,Syncfusion)});
