/*!
 *  filename: ej.culture.en-GB.min.js
 *  version : 20.3.0.59
 *  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
 *  Use of this code is subject to the terms of our license.
 *  A copy of the current license can be obtained at any time by e-mailing
 *  <EMAIL>. Any infringement will be prosecuted under
 *  applicable laws. 
 */

ej.addCulture("en-GB", {
    name: "en-GB",
    englishName: "English (United Kingdom)",
    nativeName: "English (United Kingdom)",
    numberFormat: {
        percent: {
            pattern: ["-n%", "n%"]
        },
        currency: {
            pattern: ["-$n", "$n"],
            symbol: "£"
        }
    },
    calendars: {
        standard: {
            firstDay: 1,
            patterns: {
                d: "dd/MM/yyyy",
                D: "dd MMMM yyyy",
                t: "HH:mm",
                T: "HH:mm:ss",
                f: "dd MMMM yyyy HH:mm",
                F: "dd MMMM yyyy HH:mm:ss",
                M: "d MMMM"
            }
        }
    }
});;