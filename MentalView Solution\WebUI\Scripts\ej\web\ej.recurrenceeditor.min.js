/*!
*  filename: ej.recurrenceeditor.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.globalize.min","jsrender","./../common/ej.core.min","./../common/ej.scroller.min","./ej.button.min","./ej.checkbox.min","./ej.datepicker.min","./ej.dropdownlist.min","./ej.togglebutton.min","./ej.radiobutton.min","./ej.editor.min"],n):n()})(function(){var n=this&&this.__extends||function(n,t){function r(){this.constructor=n}for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)};(function(t){var i=function(i){function r(n,r){return i.call(this),this.defaults={frequencies:["never","daily","weekly","monthly","yearly","everyweekday"],firstDayOfWeek:null,enableSpinners:!0,startDate:new Date,locale:"en-US",enableRTL:!1,value:"",dateFormat:"",selectedRecurrenceType:0,minDate:new Date(1900,1,1),maxDate:new Date(2099,12,31),cssClass:"",change:null,create:null},this.dataTypes={frequencies:"array",enableSpinners:"boolean",enableRTL:"boolean"},this.rootCSS="e-recurrenceeditor",this.PluginName="ejRecurrenceEditor",this._id="null",this.validTags=["div"],this.model=this.defaults,n&&(this._id=n[0].id,n.jquery||(n=t("#"+n)),n.length)?t(n).ejRecurrenceEditor(r).data(this.PluginName):void 0}return n(r,i),r.prototype.setModel=function(n,t){this.setModel(n,t)},r.prototype.option=function(n,t){this.option(n,t)},r.prototype._setModel=function(n){var r=!1;for(var i in n){switch(i){case"frequencies":this.model.frequencies=n[i];r=!0;break;case"firstDayOfWeek":this.model.firstDayOfWeek=this._firstDayOfWeek=n[i];r=!0;break;case"enableSpinners":this.model.enableSpinners=n[i];r=!0;break;case"startDate":t.type(n[i])=="string"?this._currentDateFormat(this.model.dateFormat):t.type(n[i])=="function"&&t.type(ej.util.getVal(n[i]))=="string"&&(this.model.startDate=ej.parseDate(this.model.startDate.toString(),this._datepattern()));this.model.startDate<this.model.minDate?this.model.startDate=this.model.minDate:"";this.model.startDate>this.model.maxDate?this.model.startDate=this.model.maxDate:"";this._recurrenceContent.find(".recurstartdate").ejDatePicker("option","value",this.model.startDate);break;case"locale":this.model.locale=n[i];r=!0;break;case"enableRTL":this.model.enableRTL=n[i];this._recurrenceLayout.addClass("e-rtl");break;case"value":this.model.value=n[i];break;case"dateFormat":this.model.dateFormat=n[i];this._recurrenceContent.find(".recurstartdate").ejDatePicker("option","dateFormat",this.model.dateFormat);this._recurrenceContent.find(".until").ejDatePicker("option","dateFormat",this.model.dateFormat);break;case"selectedRecurrenceType":this.model.selectedRecurrenceType=n[i];this._recurrenceLayout.find(".e-recurrencetype").ejDropDownList({selectedItemIndex:this.model.selectedRecurrenceType});break;case"minDate":case"maxDate":this._currentDateFormat(this.model.dateFormat);this._recurrenceContent.find(".recurstartdate").ejDatePicker({minDate:this.model.minDate,maxDate:this.model.maxDate});break;case"cssClass":this.model.cssClass=n[i];this.element.removeClass(this.model.cssClass).addClass(n[i])}r==!0&&this._render()}},r.prototype._init=function(){ej.isNullOrUndefined(this.element)||this._render()},r.prototype._destroy=function(){var r,i,n;for(this.element.off(),r=this.element.find(".e-datepicker,.e-dropdownlist"),i=0;i<r.length;i++)n=t(r[i]),n.hasClass("e-datepicker")?n.ejDatePicker("destroy"):n.hasClass("e-dropdownlist")&&n.ejDropDownList("destroy");this.element.empty().removeClass("e-recurrenceeditor")},r.prototype._render=function(){var i,n;for(t("#"+this._id+"recurrenceeditor").remove(),this._currentDateFormat(this.model.dateFormat),this._initializePrivateProperties(),i=typeof innerWidth=="number"&&window.innerWidth>document.documentElement.clientWidth,this._mediaQuery=this._browserInfo.name=="msie"&&parseInt(this._browserInfo.version,10)<=8?!1:document.documentElement.clientWidth+(i?17:0)<361||ej.isMobile(),this.model.frequencies=this.model.frequencies.toString().toLowerCase().split(","),n=0;n<this.model.frequencies.length;n++)this.model.frequencies[n]=this.model.frequencies[n]=="everyweekday"?"EveryWeekDay":this.model.frequencies[n].replace(/^./,function(n){return n.toUpperCase()});this._renderRecurrenceEditor()},r.prototype._initializePrivateProperties=function(){this._rRule={};this.flag=!0;this._subControlChange=!1;this._culture=ej.preferredCulture(this.model.locale);this._dayNamesArray=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"];this._firstDayOfWeek=this.model.firstDayOfWeek!=null?typeof this.model.firstDayOfWeek=="string"?this._dayNamesArray.indexOf(this.model.firstDayOfWeek.toString().toLowerCase()):this.model.firstDayOfWeek:this._culture.calendar.firstDay;this._monthNames=this._culture.calendar.months.names;this._pattern=this._culture.calendar.patterns;this._browserInfo=ej.browserInfo();this._dayNamesValue=["SU","MO","TU","WE","TH","FR","SA"];this._dayNames=[];this._dayShortNames=[];this._dayFullNames=[];var n=this._firstDayOfWeek;do n>6&&(n=0),this._dayFullNames.push(this._culture.calendar.days.names[n]),this._dayShortNames.push(this._culture.calendar.days.namesAbbr[n]),this._dayNames.push(this._dayNamesValue[n]),n++;while(this._dayNames.length<7);this._mediaQuery=!1},r.prototype._currentDateFormat=function(n){var i,r,u,f;ej.isNullOrUndefined(n)||t.type(this.model.startDate)=="date"&&t.type(this.model.minDate)=="date"&&t.type(this.model.maxDate)=="date"?n=="MM/dd/yyyy"&&(t.type(this.model.startDate)!="date"||t.type(this.model.minDate)!="date"||t.type(this.model.maxDate)!="date")&&(f=ej.parseDate(this.model.startDate.toString(),n),i=ej.parseDate(this.model.minDate.toString(),n),r=ej.parseDate(this.model.maxDate.toString(),n),this.model.startDate=ej.isNullOrUndefined(f)?this.model.startDate:f,this.model.minDate=ej.isNullOrUndefined(i)?this.model.minDate:i,this.model.maxDate=ej.isNullOrUndefined(r)?this.model.maxDate:r):(u=ej.parseDate(this.model.startDate.toString(),this.model.dateFormat),i=ej.parseDate(this.model.minDate.toString(),this.model.dateFormat),r=ej.parseDate(this.model.maxDate.toString(),this.model.dateFormat),this.model.startDate=ej.isNullOrUndefined(u)?new Date(this.model.startDate.toString()).toString()=="Invalid Date"||new Date(this.model.startDate.toString()).toString()=="NaN"?this._dateConvert(this.model.startDate):new Date(this.model.startDate.toString()):u,this.model.minDate=ej.isNullOrUndefined(i)?new Date(this.model.minDate.toString()).toString()=="Invalid Date"||new Date(this.model.minDate.toString()).toString()=="NaN"?this._dateConvert(this.model.minDate):new Date(this.model.minDate.toString()):i,this.model.maxDate=ej.isNullOrUndefined(r)?new Date(this.model.maxDate.toString()).toString()=="Invalid Date"||new Date(this.model.maxDate.toString()).toString()=="NaN"?this._dateConvert(this.model.maxDate):new Date(this.model.maxDate.toString()):r)},r.prototype._renderRecurrenceEditor=function(){var n,i;for(this._recurrenceLayout=ej.buildTag("div#"+this._id+"recurrenceeditor","",{},{}),this.model.enableRTL&&this._recurrenceLayout.addClass("e-rtl"),n="<table style='width:100%'><tr><td style='width:20%'><div class='e-textlabel'>"+this._getLocalizedLabels("Repeat")+":<\/div><\/td><td><input class='e-recurrencetype' id='"+this._id+"_recurrenceType' type='text' name='RecurrenceType' value='' /><\/td><\/tr><\/table><div id='"+this._id+"_recurtypelist'><ul>",i=0;i<this.model.frequencies.length;i++)n+="<li>"+this._getLocalizedLabels(this.model.frequencies[i])+"<\/li>";n+="<\/ul><\/div>";this.element.append(this._recurrenceLayout.append(n));this._renderRecurrenceContent();this._initialSubControlRender=!0;this._recurrenceLayout.find(".e-recurrencetype").ejDropDownList({enableRTL:this.model.enableRTL,targetID:this._id+"_recurtypelist",width:this._mediaQuery?"140px":"33%",change:t.proxy(this._recurrenceTypeChange,this),cssClass:this.model.cssClass});this._recurrenceLayout.find(".e-recurrencetype").ejDropDownList({selectedItemIndex:this.model.selectedRecurrenceType})},r.prototype._renderRecurrenceContent=function(){for(var i,c,n,t,r,u,f,e,o,s,h=0;h<this._dayNames.length;h++)this._dayNames[h]=this._dayNames[h].toUpperCase();if(i=[this._getLocalizedLabels("First"),this._getLocalizedLabels("Second"),this._getLocalizedLabels("Third"),this._getLocalizedLabels("Fourth"),this._getLocalizedLabels("Last")],c=this.model.enableRTL?"margin-right":"margin-left",this._recurrenceContent=ej.buildTag("div.e-recurrencecontent#"+this._id+"recurrencecontent","",{},{}),this._mediaQuery){for(n="<form id='"+this._id+"_recurrenceForm'><table class='e-table' width='100%' cellpadding='7'><tbody>",n+="<tr id='"+this._id+"_every' style='display:none'><td width='16%' id='everylabel' class='e-textlabel'>"+this._getLocalizedLabels("Every")+":<\/td><td id='everycount' class='e-tdpadding'><table><tr><td><div class='e-floatleft'><input id='"+this._id+"_recurevery' class='recurevery' type='text' /><\/div><\/td><td><div id='"+this._id+"_recurtypes' class='e-appcheckbox e-labelcursor'>"+this._getLocalizedLabels("RecurrenceDay")+"<\/div><\/td><\/tr><\/table><\/td><\/tr>",n+="<tr id='"+this._id+"_weekly' class='"+this._id+"_weekly' style='display:none'><td colspan='2' id='weeklabel' class='e-textlabel'><div>"+this._getLocalizedLabels("RepeatOn")+":<\/div><\/td><\/tr><tr class='"+this._id+"_weekly' style='display:none'><td colspan='2' id='weekcount'><table class='e-table' cellpadding='3'><tr>",t=0;t<this._dayShortNames.length;t++)n+="<td><div class='e-weekday "+this._dayShortNames[t]+"'><input id='"+this._id+"_"+this._dayNames[t]+"' class='weekdays e-weekly"+this._dayNames[t]+"'name='weekdays_ "+this._dayNames[t]+"' type='checkbox'/><label class='e-textmargin' for='"+this._id+"_"+this._dayNames[t]+"'>"+this._dayShortNames[t]+"<\/label><\/div><\/td>";for(n+="<\/tr><\/table><\/td><\/tr>",n+="<tr id='"+this._id+"_monthly' class='"+this._id+"_monthly' style='display:none'><td colspan='2' id='monthlabel' class='e-recurendslabel'><div class='e-recurendsalign'>"+this._getLocalizedLabels("RepeatBy")+":<\/div><\/td><\/tr><tr class='"+this._id+"_monthly' style='display:none'><td colspan='2' id='monthcount'><table class='e-table' cellpadding='3'><tr id='monthdaytr'><td><div><input id='"+this._id+"_monthday' class='monthdaytype' name='"+this._id+"_monthday' type='radio'/><label class='e-textmargin' for='"+this._id+"_monthday'>"+this._getLocalizedLabels("Day")+"<\/label><\/div><\/td><td><div><input id='"+this._id+"_monthdate' class='monthdate' type='text'/><\/div><\/td><\/tr><tr id='monthweekdaytr'><td><div><input id='"+this._id+"_monthon' class='monthposition' name='"+this._id+"_monthday' type='radio'/><label class='e-textmargin' for='"+this._id+"_monthon'>"+this._getLocalizedLabels("The")+"<\/label><\/div><\/td><td><div><input id='"+this._id+"_monthsrt' class='monthsrt' type='text' name='monthsrt' value=''/><div id='"+this._id+"_monthsrtlist'><ul>",r=0;r<i.length;r++)n+="<li>"+i[r]+"<\/li>";for(n+="<\/div><\/div><\/td>",n+="<td><div class='e-appcheckbox e-labelcursor'><input id='"+this._id+"_monthsrtday' class='e-monthsrtday monthsrtday' type='text' name='monthsrtday' value=''/><div id='"+this._id+"_monthsrtdaylist'><ul>",u=0;u<this._dayFullNames.length;u++)n+="<li>"+this._dayFullNames[u]+"<\/li>";for(n+="<\/div><\/div><\/td><\/tr><\/table><\/td><\/tr>",n+="<tr id='"+this._id+"_yearly' class='"+this._id+"_yearly' style='display:none'><td colspan='2' id='yearlabel' class='e-recurendslabel'><div class='e-recurendsalign'>"+this._getLocalizedLabels("RepeatBy")+":<\/div><\/td><\/tr><tr class='"+this._id+"_yearly' style='display:none'><td colspan='2' id='yearcount' ><table class='e-table' cellpadding='3'><tr id='yeardaytr'><td><div><input id='"+this._id+"_yearday' class='yearrecurday' name='"+this._id+"_yearday' type='radio'/><label class='e-textmargin' for='"+this._id+"_yearday'>"+this._getLocalizedLabels("The")+"<\/label><\/div><\/td><td><div class='e-controlalign'><input id='"+this._id+"_yearmonth' class='yearmonth' type='text' name='yearmonth' value=''/><\/div><\/td><div id='"+this._id+"_yearmonthlist'><ul>",f=0;f<this._monthNames.length-1;f++)n+="<li>"+this._monthNames[f]+"<\/li>";for(n+="<\/ul><\/div><td><input id='"+this._id+"_yeardate' class='yeardate' text='text'/><\/td><\/tr><tr id='yearweekdaytr'><td><div><input id='"+this._id+"_yearother' class='yearrecurposi' name='"+this._id+"_yearday' type='radio'/><label class='e-textmargin' for='"+this._id+"_yearother'>"+this._getLocalizedLabels("The")+"<\/label><\/div><\/td><td><div><input id='"+this._id+"_yearsrt' class='yearsrt' type='text' name='yearsrt' value=''/><div id='"+this._id+"_yearsrtlist'><ul>",e=0;e<i.length;e++)n+="<li>"+i[e]+"<\/li>";for(n+="<\/ul><\/div><\/div><\/td><td><div class='e-controlalign'><input id='"+this._id+"_yearsrtday' class='yearsrtday' type='text' name='yearsrtday' value=''/><div id='"+this._id+"_yearsrtdaylist'><ul>",o=0;o<this._dayFullNames.length;o++)n+="<li>"+this._dayFullNames[o]+"<\/li>";for(n+="<\/ul><\/div><\/div><\/td><td><div><span>"+this._getLocalizedLabels("OfEvery")+"<\/span><\/div><\/td><\/tr><tr><td><\/td><td><div><input id='"+this._id+"_yearsrtmonth' class='yearsrtmonth' type='text' name='yearsrtmonth' value=''/><div id='"+this._id+"_yearsrtmonthlist'><ul>",s=0;s<this._monthNames.length-1;s++)n+="<li>"+this._monthNames[s]+"<\/li>";n+="<\/ul><\/div><\/div><\/td><\/tr><\/table><\/td><\/tr>";n+="<tr id='"+this._id+"_startson' ><td colspan='2' id='startsonlabel' class='e-textlabel'>"+this._getLocalizedLabels("StartsOn")+":<\/td><\/tr><tr><td colspan='2' id='startsoncount'><input id='"+this._id+"_recurstartdate' class='recurstartdate' type='text' name='RecurStartDate' value=''/><\/td><\/tr>";n+="<tr id='"+this._id+"_endson' ><td colspan='2' id='endsonlabel' class='e-recurendslabel'><div class='e-recurendsalign'>"+this._getLocalizedLabels("Ends")+":<\/div><\/td><\/tr><tr><td colspan='2' id='endsoncount'><table class='e-table' cellpadding='3'><tr id='endsonnever'><td><div><input id='"+this._id+"_repeatendnever' class='recurends e-recurnoend' type='radio' name='"+this._id+"_repeatend' value='Never'/><label class='e-textmargin' for='"+this._id+"_repeatendnever'>"+this._getLocalizedLabels("Never")+"<\/label><\/div><\/td><\/tr><tr id='endsonafter'><td><div><input id='"+this._id+"_repeatendafter' class='recurends e-recurafter' type='radio' name='"+this._id+"_repeatend'/><label class='e-textmargin' for='"+this._id+"_repeatendafter'>"+this._getLocalizedLabels("After")+"<\/label><\/div><\/td><td><div><input id='"+this._id+"_recurcount' class='recurcount' type='text'/><\/div><\/td><td><span class='e-labelcursor' style='"+c+": -80px;'>"+this._getLocalizedLabels("Occurrence")+"<\/span><\/td><\/tr><tr id='endsonuntil'><td><div><input id='"+this._id+"_repeatendon' class='recurends e-recuruntil' type='radio' name='"+this._id+"_repeatend'/><label class='e-textmargin' for='"+this._id+"_repeatendon'>"+this._getLocalizedLabels("On")+"<\/label><\/div><\/td><td><input id='"+this._id+"_daily' class='e-until until' type='text' name='daily' value=''/><\/td><\/tr>  <\/table><\/td><\/tr>";n+="<tr style='display:none'><td><span class='e-textlabel'>Summary:<\/span><\/td><td><span class=e-recurRule><\/span><\/td><\/tr><\/tbody><\/table><\/form>"}else{for(n="<form id='"+this._id+"_recurrenceForm'><table class='e-table' width='100%' cellpadding='7'><tbody>",n+="<tr id='"+this._id+"_every' style='display:none'><td width='20%' id='everylabel' class='e-textlabel'>"+this._getLocalizedLabels("Every")+":<\/td><td id='everycount' class='e-tdpadding'><table><tr><td><div class='e-floatleft'><input id='"+this._id+"_recurevery' class='recurevery' type='text' /><\/div><\/td><td><div id='"+this._id+"_recurtypes' class='e-appcheckbox e-labelcursor'>"+this._getLocalizedLabels("RecurrenceDay")+"<\/div><\/td><\/tr><\/table><\/td><\/tr>",n+="<tr id='"+this._id+"_weekly' class='"+this._id+"_weekly' style='display:none'><td id='weeklabel' class='e-textlabel'><div>"+this._getLocalizedLabels("RepeatOn")+":<\/div><\/td><td id='weekcount'><table class='e-table' cellpadding='3'><tr>",t=0;t<this._dayShortNames.length;t++)n+="<td><div class='e-weekday "+this._dayShortNames[t]+"'><input id='"+this._id+"_"+this._dayNames[t]+"' class='weekdays e-weekly"+this._dayNames[t]+"'name='weekdays_ "+this._dayNames[t]+"' type='checkbox'/><label class='e-textmargin' for='"+this._id+"_"+this._dayNames[t]+"'>"+this._dayShortNames[t]+"<\/label><\/div><\/td>";for(n+="<\/tr><\/table><\/td><\/tr>",n+="<tr id='"+this._id+"_monthly' class='"+this._id+"_monthly' style='display:none'><td id='monthlabel' class='e-recurendslabel'><div class='e-recurendsalign'>"+this._getLocalizedLabels("RepeatBy")+":<\/div><\/td><td id='monthcount'><table class='e-table' cellpadding='3'><tr id='monthdaytr'><td><div><input id='"+this._id+"_monthday' class='monthdaytype' name='"+this._id+"_monthday' type='radio'/><label class='e-textmargin' for='"+this._id+"_monthday'>"+this._getLocalizedLabels("Day")+"<\/label><\/div><\/td><td><div><input id='"+this._id+"_monthdate' class='monthdate' type='text'/><\/div><\/td><\/tr><tr id='monthweekdaytr'><td><div><input id='"+this._id+"_monthon' class='monthposition' name='"+this._id+"_monthday' type='radio'/><label class='e-textmargin' for='"+this._id+"_monthon'>"+this._getLocalizedLabels("The")+"<\/label><\/div><\/td><td><div><input id='"+this._id+"_monthsrt' class='monthsrt' type='text' name='monthsrt' value=''/><div id='"+this._id+"_monthsrtlist'><ul>",r=0;r<i.length;r++)n+="<li>"+i[r]+"<\/li>";for(n+="<\/div><\/div><\/td>",n+="<td><div class='e-appcheckbox e-labelcursor'><input id='"+this._id+"_monthsrtday' class='e-monthsrtday monthsrtday' type='text' name='monthsrtday' value=''/><div id='"+this._id+"_monthsrtdaylist'><ul>",u=0;u<this._dayFullNames.length;u++)n+="<li>"+this._dayFullNames[u]+"<\/li>";for(n+="<\/div><\/div><\/td><\/tr><\/table><\/td><\/tr>",n+="<tr id='"+this._id+"_yearly' class='"+this._id+"_yearly' style='display:none'><td id='yearlabel' class='e-recurendslabel'><div class='e-recurendsalign'>"+this._getLocalizedLabels("RepeatBy")+":<\/div><\/td><td id='yearcount' ><table class='e-table' cellpadding='3'><tr id='yeardaytr'><td><div><input id='"+this._id+"_yearday' class='yearrecurday' name='"+this._id+"_yearday' type='radio'/><label class='e-textmargin' for='"+this._id+"_yearday'>"+this._getLocalizedLabels("The")+"<\/label><\/div><\/td><td><div class='e-controlalign'><input id='"+this._id+"_yearmonth' class='yearmonth' type='text' name='yearmonth' value=''/><\/div><\/td><div id='"+this._id+"_yearmonthlist'><ul>",f=0;f<this._monthNames.length-1;f++)n+="<li>"+this._monthNames[f]+"<\/li>";for(n+="<\/ul><\/div><td><input id='"+this._id+"_yeardate' class='yeardate' text='text'/><\/td><\/tr><tr id='yearweekdaytr'><td><div><input id='"+this._id+"_yearother' class='yearrecurposi' name='"+this._id+"_yearday' type='radio'/><label class='e-textmargin' for='"+this._id+"_yearother'>"+this._getLocalizedLabels("The")+"<\/label><\/div><\/td><td><div><input id='"+this._id+"_yearsrt' class='yearsrt' type='text' name='yearsrt' value=''/><div id='"+this._id+"_yearsrtlist'><ul>",e=0;e<i.length;e++)n+="<li>"+i[e]+"<\/li>";for(n+="<\/ul><\/div><\/div><\/td><td><div class='e-controlalign'><input id='"+this._id+"_yearsrtday' class='yearsrtday' type='text' name='yearsrtday' value=''/><div id='"+this._id+"_yearsrtdaylist'><ul>",o=0;o<this._dayFullNames.length;o++)n+="<li>"+this._dayFullNames[o]+"<\/li>";for(n+="<\/ul><\/div><\/div><\/td><td><div><span>"+this._getLocalizedLabels("OfEvery")+"<\/span><\/div><\/td><td><div><input id='"+this._id+"_yearsrtmonth' class='yearsrtmonth' type='text' name='yearsrtmonth' value=''/><div id='"+this._id+"_yearsrtmonthlist'><ul>",s=0;s<this._monthNames.length-1;s++)n+="<li>"+this._monthNames[s]+"<\/li>";n+="<\/ul><\/div><\/div><\/td><\/tr><\/table><\/td><\/tr>";n+="<tr id='"+this._id+"_startson' style='display:none'><td width='20%' id='startsonlabel' class='e-textlabel'>"+this._getLocalizedLabels("StartsOn")+":<\/td><td id='startsoncount'><input id='"+this._id+"_recurstartdate' class='recurstartdate' type='text' name='RecurStartDate' value=''/><\/td><\/tr>";n+="<tr id='"+this._id+"_endson' style='display:none'><td id='endsonlabel' class='e-recurendslabel'><div class='e-recurendsalign'>"+this._getLocalizedLabels("Ends")+":<\/div><\/td><td id='endsoncount'><table class='e-table' cellpadding='3'><tr id='endsonnever'><td><div><input id='"+this._id+"_repeatendnever' class='recurends e-recurnoend' type='radio' name='"+this._id+"_repeatend' value='Never'/><label class='e-textmargin' for='"+this._id+"_repeatendnever'>"+this._getLocalizedLabels("Never")+"<\/label><\/div><\/td><\/tr><tr id='endsonafter'><td><div><input id='"+this._id+"_repeatendafter' class='recurends e-recurafter' type='radio' name='"+this._id+"_repeatend'/><label class='e-textmargin' for='"+this._id+"_repeatendafter'>"+this._getLocalizedLabels("After")+"<\/label><\/div><\/td><td><div><input id='"+this._id+"_recurcount' class='recurcount' type='text'/><\/div><\/td><td><span class='e-labelcursor' style='"+c+": -80px;'>"+this._getLocalizedLabels("Occurrence")+"<\/span><\/td><\/tr><tr id='endsonuntil'><td><div><input id='"+this._id+"_repeatendon' class='recurends e-recuruntil' type='radio' name='"+this._id+"_repeatend'/><label class='e-textmargin' for='"+this._id+"_repeatendon'>"+this._getLocalizedLabels("On")+"<\/label><\/div><\/td><td><input id='"+this._id+"_daily' class='e-until until' type='text' name='daily' value=''/><\/td><\/tr>  <\/table><\/td><\/tr>";n+="<tr style='display:none'><td><span class='e-textlabel'>Summary:<\/span><\/td><td><span class=e-recurRule><\/span><\/td><\/tr><\/tbody><\/table><\/form>"}this._recurrenceLayout.append(this._recurrenceContent.append(n));this._renderControls()},r.prototype._renderControls=function(){var n=this._recurrenceContent;n.find(".weekdays").ejCheckBox({enableRTL:this.model.enableRTL,cssClass:this.model.cssClass,change:t.proxy(this._weeklyClick,this)});n.find(".monthsrt").ejDropDownList({enableRTL:this.model.enableRTL,targetID:this._id+"_monthsrtlist",width:"100px",cssClass:this.model.cssClass,change:t.proxy(this._monthSrt,this)});n.find(".monthsrtday").ejDropDownList({enableRTL:this.model.enableRTL,targetID:this._id+"_monthsrtdaylist",width:"100px",cssClass:this.model.cssClass,change:t.proxy(this._monthSrt,this)});n.find(".yearsrt").ejDropDownList({enableRTL:this.model.enableRTL,targetID:this._id+"_yearsrtlist",width:"100px",cssClass:this.model.cssClass,change:t.proxy(this._yearsrt,this)});n.find(".yearmonth").ejDropDownList({enableRTL:this.model.enableRTL,targetID:this._id+"_yearmonthlist",width:"100px",cssClass:this.model.cssClass,change:t.proxy(this._yearmonth,this)});n.find(".yearsrtday").ejDropDownList({enableRTL:this.model.enableRTL,targetID:this._id+"_yearsrtdaylist",width:"100px",cssClass:this.model.cssClass,change:t.proxy(this._yearsrt,this)});n.find(".yearsrtmonth").ejDropDownList({enableRTL:this.model.enableRTL,targetID:this._id+"_yearsrtmonthlist",width:"100px",cssClass:this.model.cssClass,change:t.proxy(this._yearsrt,this)});n.find(".recurstartdate").ejDatePicker({buttonText:"Today",startDay:this._firstDayOfWeek,enableRTL:this.model.enableRTL,locale:this.model.locale,cssClass:this.model.cssClass,dateFormat:this.model.dateFormat,value:this.model.startDate,minDate:this.model.minDate,maxDate:this.model.maxDate,change:t.proxy(this._startDateChange,this)});n.find(".until").ejDatePicker({buttonText:"Today",startDay:this._firstDayOfWeek,enableRTL:this.model.enableRTL,locale:this.model.locale,cssClass:this.model.cssClass,dateFormat:this.model.dateFormat,change:t.proxy(this._recurUntil,this)});n.find(".recurcount").ejNumericTextbox({enableRTL:this.model.enableRTL,showSpinButton:this.model.enableSpinners,name:"recurcount",minValue:1,value:10,width:"65px",decimalPlaces:0,cssClass:this.model.cssClass,change:t.proxy(this._recurCount,this)});n.find(".recurevery").ejNumericTextbox({enableRTL:this.model.enableRTL,showSpinButton:this.model.enableSpinners,name:"recurevery",minValue:1,value:1,decimalPlaces:0,width:"100px",cssClass:this.model.cssClass,change:t.proxy(this._everyCount,this)});n.find(".monthdate").ejNumericTextbox({enableRTL:this.model.enableRTL,showSpinButton:this.model.enableSpinners,name:"monthdate",minValue:1,decimalPlaces:0,maxValue:31,value:1,width:"70px",cssClass:this.model.cssClass,change:t.proxy(this._monthDate,this)});n.find(".yeardate").ejNumericTextbox({enableRTL:this.model.enableRTL,showSpinButton:this.model.enableSpinners,name:"yeardate",minValue:1,decimalPlaces:0,maxValue:31,value:1,width:"70px",cssClass:this.model.cssClass,change:t.proxy(this._yearmonth,this)});n.find(".recurends").ejRadioButton({enableRTL:this.model.enableRTL,cssClass:this.model.cssClass,change:t.proxy(this._recurEndChange,this)});n.find(".monthposition,.monthdaytype").ejRadioButton({enableRTL:this.model.enableRTL,cssClass:this.model.cssClass,change:t.proxy(this._recurEndChange,this)});n.find(".yearrecurposi,.yearrecurday").ejRadioButton({enableRTL:this.model.enableRTL,cssClass:this.model.cssClass,change:t.proxy(this._recurEndChange,this)})},r.prototype._recurrenceTypeChange=function(n){this._recurrenceType(n);this._rRuleFreq!=""&&this.closeRecurPublic();this._rRuleFreq==""&&(this._recRule=undefined);this._trigger("change",{recurrenceRule:this._recRule})},r.prototype._recurrenceType=function(n){var r,i,u,f;if(this._rRuleFreq="",r="",i=t.type(this.model.startDate)=="string"?this._dateConvert(this.model.startDate):this.model.startDate,this.flag&&(this._subControlChange=!0,this._recurrenceContent.find(".until").ejDatePicker({value:new Date(new Date(i.toDateString()).setDate(new Date(i.toDateString()).getDate()+70))}),this._subControlChange=!1,this._recurrenceContent.find(".e-recurnoend").ejRadioButton({checked:!0}),this.flag=!1),this._mediaQuery||this._recurrenceContent.find("#"+this._id+"_startson,#"+this._id+"_endson").css("display","table-row"),n.itemId!=null){this._recurrenceContent.find("#"+this._id+"_every").css("display","table-row");u=this._getLocalizedLabels(this.model.frequencies[n.itemId]);switch(u){case this._getLocalizedLabels("Never"):this._recurrenceContent.css("display","none");break;case this._getLocalizedLabels("Daily"):this._recurrenceContent.css("display","block");this._recurrenceContent.find("#"+this._id+"_recurtypes").get(0).innerHTML=this._getLocalizedLabels("RecurrenceDay");this._recurrenceContent.find("."+this._id+"_weekly,."+this._id+"_monthly,."+this._id+"_yearly").css("display","none");this._rRuleFreq="DAILY";r=this._getLocalizedLabels("RecurrenceDay").toLowerCase();break;case this._getLocalizedLabels("Weekly"):this._recurrenceContent.css("display","block");this._recurrenceContent.find("#"+this._id+"_recurtypes").get(0).innerHTML=this._getLocalizedLabels("RecurrenceWeek");f=this._dayShortNames.indexOf(ej.format(i,"ddd",this.model.locale));this._initialSubControlRender=!0;this._recurrenceContent.find(".e-weekly"+this._dayNames[f]).ejCheckBox({checked:!0});r=this._getLocalizedLabels("RecurrenceWeek").toLowerCase()+" "+this._getLocalizedLabels("On").toLowerCase()+" "+this._dayFullNames[i.getDay()];this._rRuleFreq="WEEKLY";this._recurrenceContent.find("."+this._id+"_monthly,."+this._id+"_yearly").css("display","none");this._recurrenceContent.find("."+this._id+"_weekly").css("display","table-row");break;case this._getLocalizedLabels("Monthly"):this._recurrenceContent.css("display","block");this._recurrenceContent.find("#"+this._id+"_recurtypes").get(0).innerHTML=this._getLocalizedLabels("RecurrenceMonth");this._recurrenceContent.find("."+this._id+"_weekly,."+this._id+"_yearly").css("display","none");this._recurrenceContent.find("."+this._id+"_monthly").css("display","table-row");this._initialSubControlRender=!0;this._recurrenceContent.find(".monthdate").ejNumericTextbox({value:i.getDate()});this._recurrenceContent.find(".monthsrtday").ejDropDownList({selectedItemIndex:this._dayNames.indexOf(ej.format(i,"ddd","en-US").substr(0,2).toUpperCase())});this._recurrenceContent.find(".monthsrt").ejDropDownList({selectedItemIndex:this._getWeekIndex(i)});this._recurrenceContent.find(".monthdaytype").ejRadioButton({checked:!0});this._rRuleFreq="MONTHLY";r=this._getLocalizedLabels("RecurrenceMonth").toLowerCase();break;case this._getLocalizedLabels("Yearly"):this._recurrenceContent.css("display","block");this._recurrenceContent.find("#"+this._id+"_recurtypes").get(0).innerHTML=this._getLocalizedLabels("RecurrenceYear");this._recurrenceContent.find("."+this._id+"_weekly,."+this._id+"_monthly").css("display","none");this._recurrenceContent.find("."+this._id+"_yearly").css("display","table-row");this._initialSubControlRender=!0;this._recurrenceContent.find(".yearmonth").ejDropDownList({selectedItemIndex:i.getMonth()});this._recurrenceContent.find(".yeardate").ejNumericTextbox({value:i.getDate()});this._recurrenceContent.find(".yearsrtday").ejDropDownList({selectedItemIndex:this._dayNames.indexOf(ej.format(i,"ddd","en-US").substr(0,2).toUpperCase())});this._recurrenceContent.find(".yearsrtmonth").ejDropDownList({selectedItemIndex:i.getMonth()});this._recurrenceContent.find(".yearsrt").ejDropDownList({selectedItemIndex:this._getWeekIndex(i)});this._recurrenceContent.find(".yearrecurday").ejRadioButton({checked:!0});this._rRuleFreq="YEARLY";r=this._getLocalizedLabels("RecurrenceYear").toLowerCase();break;case this._getLocalizedLabels("EveryWeekDay"):this._recurrenceContent.css("display","block");this._rRuleFreq="WEEKDAYS";this._recurrenceContent.find("."+this._id+"_weekly,."+this._id+"_yearly,#"+this._id+"_every,."+this._id+"_monthly").css("display","none");r=n.text}this._initialSubControlRender=!1}this._recurrenceContent.find(".e-recurRule").html(r)},r.prototype._getWeekIndex=function(n){var i=0,t=new Date(n.getFullYear(),n.getMonth(),1),r=n.getDay();if(r!=t.getDay())do t=new Date(t.setDate(t.getDate()+1));while(r!=t.getDay());while(t.getDate()!=n.getDate())t=new Date(t.setDate(t.getDate()+7)),i++;return i},r.prototype._weeklyClick=function(){this._initialSubControlRender||this._stringGenerate()},r.prototype._monthSrt=function(){this._subControlChange=!0;this._recurrenceContent.find("#"+this._id+"_monthon").ejRadioButton("option","checked",!0);this._subControlChange=!1;this._initialSubControlRender||this._stringGenerate()},r.prototype._yearsrt=function(){this._subControlChange=!0;this._recurrenceContent.find("#"+this._id+"_yearother").ejRadioButton("option","checked",!0);this._subControlChange=!1;this._initialSubControlRender||this._stringGenerate()},r.prototype._yearmonth=function(){this._subControlChange=!0;this._recurrenceContent.find("#"+this._id+"_yearday").ejRadioButton("option","checked",!0);this._subControlChange=!1;this._initialSubControlRender||this._stringGenerate()},r.prototype._startDateChange=function(n){this.model.startDate=new Date(n.model.value);(!this._recurrenceContent.find("#"+this._id+"_repeatendon").ejRadioButton("model.checked")||this.model.startDate>this._recurrenceContent.find(".until").ejDatePicker("option","value"))&&(this._subControlChange=!0,this._recurrenceContent.find(".until").ejDatePicker("option","value",new Date(new Date(n.model.value).setDate(n.model.value.getDate()+70))));this._subControlChange=!1;this._initialSubControlRender||this._stringGenerate()},r.prototype._recurUntil=function(){this._subControlChange||(this._subControlChange=!0,this._recurrenceContent.find("#"+this._id+"_repeatendon").ejRadioButton("option","checked",!0),this._subControlChange=!1,this._initialSubControlRender||this._stringGenerate())},r.prototype._recurCount=function(){this._recurrenceContent.find("#"+this._id+"_repeatendafter").ejRadioButton("option","checked",!0);this._initialSubControlRender||this._stringGenerate()},r.prototype._everyCount=function(){this._initialSubControlRender||this._stringGenerate()},r.prototype._monthDate=function(){this._recurrenceContent.find("#"+this._id+"_monthday").ejRadioButton("option","checked",!0);this._initialSubControlRender||this._stringGenerate()},r.prototype._recurEndChange=function(){this._subControlChange||this._initialSubControlRender||this._stringGenerate()},r.prototype._stringGenerate=function(){this.closeRecurPublic();this._trigger("change",{recurrenceRule:this._recRule})},r.prototype._findInterval=function(n){var t;return t=n.ejNumericTextbox("model.value"),ej.isNullOrUndefined(t)?0:t},r.prototype._weeklyDayFind=function(n,t){var i=n,r;do i=new Date(i.setDate(i.getDate()+1)),r=this._dayNamesValue[new Date(i).getDay()].toUpperCase();while(t.indexOf(r)==-1);return i},r.prototype._dayOfWeekInMonth=function(n,t,i){var e=this._dayNamesValue.indexOf(t),u,r,f,o;if(n.setHours(0,0,0,0),u=new Date(n),i==-1){for(f=new Date(u.getFullYear(),u.getMonth()+1,0);f.getDay()!=e;)f=new Date(f.setDate(f.getDate()-1));r=new Date(f.toString())}else do{for(o=0,r=new Date(u.getFullYear(),u.getMonth(),1);;){if(r.getDay()===e)if(i!=5){if(++o==i)break}else if(u.getMonth()!=r.getMonth()){r.setDate(r.getDate()-7);break}r.setDate(r.getDate()+1)}u.setMonth(u.getMonth()+1)}while(r.getTime()<n.getTime());return r},r.prototype._dayOfMonthInYear=function(n,t,i){var e=this._dayNamesValue.indexOf(t),o=this._rRule.month-1,u,r,f,s;if(n.setHours(0,0,0,0),u=new Date(n),i==-1){for(f=new Date(u.getFullYear(),u.getMonth()+1,0);f.getDay()!=e;)f=new Date(f.setDate(f.getDate()-1));r=new Date(f.toString())}else do{for(s=0,r=new Date(u.getFullYear(),o,1);;){if(r.getDay()===e)if(i!=5){if(++s==i)break}else if(r.getMonth()!=o){r.setDate(r.getDate()-7);break}r.setDate(r.getDate()+1)}u.setFullYear(u.getFullYear()+1)}while(r.getTime()<n.getTime());return r},r.prototype._findRecurCount=function(n,t,i){var p=0,n,t,u=!1,c,l,a=this._interval,r=new Date(this._recurrenceContent.find(".recurstartdate").ejDatePicker("model.value")),v,e,y,w,o,s,f,h;for(ej.isNullOrUndefined(this._actualDate)||this._actualDate==0||(v=new Date(r.getFullYear(),r.getMonth()+1,0),r=v.getDate()<this._actualDate?v:new Date(r.getFullYear(),r.getMonth(),this._actualDate)),l=c=new Date(r.toString()),e=new Date(this.element.find(".e-until").ejDatePicker("model.value"));r<=e;){switch(this._rRuleFreq){case"DAILY":r=new Date(r.setDate(r.getDate()+n));u=!0;break;case"WEEKLY":case"WEEKDAYS":for(y=t.split(","),w=i._dayNames[new Date(r.toString()).getDay()],o=0;o<y.length;o++)if(y[o]==w){if(a==this._interval){u=!0;a=1;break}a++}break;case"MONTHLY":!ej.isNullOrUndefined(this._month)&&this._month&&c.getMonth()>r.getMonth()&&(r=new Date(r.setDate(c.getDate())));r.getDate()!=l.getDate()&&(ej.isNullOrUndefined(this._month)||!this._month)&&(this._month=!0,r=new Date(r.setDate(r.getDate()-r.getDate())));s=null;this.recurrenceRuleSplit(this._recRule,s);ej.isNullOrUndefined(this._rRule.setPositions)?(f=new Date(r.toString()),f.setDate(this._rRule.monthDay),f<=e&&f>=r&&(u=!0)):(h=this._dayOfWeekInMonth(r,this._rRule.weekDays,this._rRule.setPositions),this._dayOfWeekInMonth(r,this._rRule.weekDays,this._rRule.setPositions)<=e&&h.getMonth()==r.getMonth()&&(u=!0));r=new Date(r.getFullYear(),r.getMonth()+n,1);break;case"YEARLY":s=null;this.recurrenceRuleSplit(this._recRule,s);ej.isNullOrUndefined(this._rRule.setPositions)?(f=new Date(r.toString()),f.setDate(this._rRule.monthDay),f<=e&&f>=r&&(u=!0)):(h=this._dayOfWeekInMonth(r,this._rRule.weekDays,this._rRule.setPositions),this._dayOfWeekInMonth(r,this._rRule.weekDays,this._rRule.setPositions)<=e&&h.getMonth()==r.getMonth()&&(u=!0));r=new Date(r.setFullYear(r.getFullYear()+n))}u&&p++;u=!1;(this._rRuleFreq=="WEEKLY"||this._rRuleFreq=="WEEKDAYS")&&(r=new Date(r.setDate(r.getDate()+1)));l=new Date(r.toString())}return this._month=!1,this._actualDate=0,p},r.prototype._getLocalizedLabels=function(n){return ej.isNullOrUndefined(ej.RecurrenceEditor.Locale[this.model.locale])?ej.RecurrenceEditor.Locale["en-US"][n]:ej.RecurrenceEditor.Locale[this.model.locale][n]},r.prototype._datepattern=function(){return this.model.dateFormat!=""&&!ej.isNullOrUndefined(this.model.dateFormat)?this.model.dateFormat:this._pattern.d},r.prototype._dateConvert=function(n){if(ej.isNullOrUndefined(n))i=null;else{var i=new Date(parseInt(n.match(/\d+/).toString()));i=t.type(i)=="date"?i:new Date}return i},r.prototype.recurrenceDateGenerator=function(n,t,i){var s=[],ht=n.split(";"),r,c,rt,ut,y,e,u,f,p,w,k,l,a,d,h,g,v,it;if(this._recurDates={},r=new Date(t),c=0,y=new Date(r),ej.isNullOrUndefined(i)&&(i=null),this.recurrenceRuleSplit(n,i),ej.isNullOrUndefined(this._rRule.recurEditId)){if(u="",f=ej.isNullOrUndefined(this._rRule.interval)?1:this._rRule.interval,ej.isNullOrUndefined(this._rRule.weekDays)||(u=this._rRule.weekDays.split(",")),ej.isNullOrUndefined(this._rRule.until))if(ej.isNullOrUndefined(this._rRule.count))e=new Date(new Date(this.model.startDate.toString()).setDate(r.getDate()+42*f));else{if(this._rRule.count==0)return s;switch(this._rRule.freq){case"DAILY":e=new Date(new Date(r).setDate(r.getDate()+this._rRule.count*f));break;case"WEEKLY":p=Math.round(r.getDate()+this._rRule.count/u.length*7*f);e=new Date(new Date(r).setDate(p+1));break;case"MONTHLY":e=new Date(new Date(r).setMonth(r.getMonth()+this._rRule.count*f));break;case"YEARLY":e=new Date(new Date(r).setFullYear(r.getFullYear()+this._rRule.count*f))}}else e=new Date(this._rRule.until),e.setDate(e.getDate()+1);switch(this._rRule.freq){case"WEEKLY":ej.isNullOrUndefined(this._rRule.weekDays)?(w=["SU","MO","TU","WE","TH","FR","SA"],u=w[r.getDay()]):(u=this._rRule.weekDays.split(","),u.indexOf(this._dayNamesValue[new Date(r).getDay()].toUpperCase())==-1&&(r=this._weeklyDayFind(r,u)));break;case"MONTHLY":if(!ej.isNullOrUndefined(this._rRule.monthDay))for(var ft=new Date(r).getDate()==new Date(r.getFullYear(),r.getMonth()+1,0).getDate()?!0:!1,et=this._rRule.monthDay>=new Date(r).getDate(),ot=new Date(r).getMonth(),b=!1;new Date(r).getDate()!=this._rRule.monthDay&&!b;)k=et?new Date(r).getMonth()==ot:!0,k?r=new Date(r.setDate(r.getDate()+1)):(r=new Date(r.setDate(r.getDate()-1)),b=!0);rt=ut=new Date(r);break;case"YEARLY":ej.isNullOrUndefined(this._rRule.monthDay)||(l=new Date(y.setMonth(this._rRule.month-1)),a=new Date(l.getFullYear(),l.getMonth()+1,0).getDate(),this._rRule.monthDay>a&&(this._rRule.monthDay=a),r=r.getMonth()+1>=this._rRule.month&&r.getDate()>this._rRule.monthDay?new Date(r.getFullYear()+1,this._rRule.month-1,this._rRule.monthDay):new Date(r.getFullYear(),this._rRule.month-1,this._rRule.monthDay))}while(r<=e){this._rRule.freq!="MONTHLY"||ej.isNullOrUndefined(this._rRule.setPositions)||(r=this._dayOfWeekInMonth(r,this._rRule.weekDays,this._rRule.setPositions));this._rRule.freq!="YEARLY"||ej.isNullOrUndefined(this._rRule.setPositions)||(r=this._dayOfMonthInYear(r,this._rRule.weekDays,this._rRule.setPositions));ej.isNullOrUndefined(this._rRule.exDate)?d=0:this._rRule.exDate.indexOf(ej.format(new Date(r),this._pattern.d,this.model.locale))==-1&&(d=2);h=new Date(r);switch(this._rRule.freq){case"DAILY":(u.length>0&&u.indexOf(this._dayNamesValue[h.getDay()])>-1||u.length===0)&&s.push(new Date(h.toString()).getTime());r=new Date(r.setDate(r.getDate()+f));break;case"WEEKLY":g=this._dayNamesValue[new Date(r).getDay()].toUpperCase();u.indexOf(g)!=-1&&(s.push(new Date(h.toString()).getTime()),r=this._weeklyDayFind(r,u));break;case"MONTHLY":if(s.push(new Date(h.toString()).getTime()),ft)r=new Date(r.getFullYear(),r.getMonth()+f+1,0);else if(ej.isNullOrUndefined(this._rRule.setPositions)){var nt=new Date(r),o=new Date(nt.setMonth(nt.getMonth()+f)),tt=new Date(o.getFullYear(),o.getMonth()+1,0),st=r.getFullYear()==o.getFullYear()?r.getMonth()+f:o.getMonth();st==o.getMonth()?r=o>tt?new Date(tt.toString()):new Date(o.setDate(this._rRule.monthDay)):(v=new Date(o.setMonth(o.getMonth()-1)),r=new Date(v.getFullYear(),v.getMonth()+1,0))}else r=new Date(r.getFullYear(),r.getMonth()+f,1);break;case"YEARLY":s.push(new Date(h.toString()).getTime());it=ej.isNullOrUndefined(this._rRule.setPositions)?r.getDate():1;r=new Date(r.getFullYear()+f,this._rRule.month-1,it)}if(c++,!ej.isNullOrUndefined(this._rRule.count)&&c==this._rRule.count)break}}return s},r.prototype.recurrenceRuleSplit=function(n,i){var u,e,s,o,f,r;if(this._rRule={},e=i,s=n.split(";"),!ej.isNullOrUndefined(i)&&!ej.isNullOrUndefined(e.split(",")))for(f=e.split(","),this._rRule.exDate=[],r=0;r<f.length;r++)this._rRule.exDate[r]=f[r];for(o=0;o<s.length;o++){u=s[o].split("=");switch(t.trim(u[0]).toUpperCase()){case"FREQ":this._rRule.freq=t.trim(u[1]).toUpperCase();break;case"INTERVAL":this._rRule.interval=parseInt(u[1],10);break;case"COUNT":this._rRule.count=parseInt(u[1],10);break;case"UNTIL":this._rRule.until=ej.parseDate(ej.format(u[1],this._pattern.d,this.model.locale),this._pattern.d);break;case"BYDAY":this._rRule.weekDays=u[1];break;case"BYMONTHDAY":this._rRule.monthDay=parseInt(u[1],10);break;case"BYMONTH":this._rRule.month=parseInt(u[1],10);break;case"BYSETPOS":this._rRule.setPositions=parseInt(u[1],10);break;case"WKST":this._rRule.weekStart=u[1];break;case"EXDATE":if(ej.isNullOrUndefined(i)||ej.isNullOrUndefined(e.split(",")))for(f=u[1].split(","),this._rRule.exDate=[],r=0;r<f.length;r++)this._rRule.exDate[r]=f[r];else for(f=e.split(","),this._rRule.exDate=[],r=0;r<f.length;r++)this._rRule.exDate[r]=f[r];break;case"RECUREDITID":this._rRule.recurEditId=parseInt(u[1])||u[1]}}return this._rRule},r.prototype.clearRecurrenceFields=function(){for(var t=this.element.find("input.weekdays"),n=0;n<t.length;n++)t.eq(n).ejCheckBox({checked:!1});this.element.find(".recurevery").ejNumericTextbox({value:1});this.element.find(".recurcount").ejNumericTextbox({value:10});this.element.find(".e-recurRule").html("")},r.prototype.setDefaultValues=function(){var n=this.model.startDate.toString();this._recurrenceLayout.find(".e-recurrencetype").ejDropDownList({selectedItemIndex:0});this._recurrenceLayout.find(".e-recurrencetype").ejDropDownList({selectedItemIndex:1});this.element.find(".recurstartdate").ejDatePicker({value:new Date(n)});this.element.find(".e-until").ejDatePicker({value:new Date(new Date(n).setDate(new Date(n).getDate()+70))});this.element.find(".e-recurnoend").ejRadioButton({checked:!0})},r.prototype.showRecurrenceSummary=function(){for(var r,n=0;n<this._dayNames.length;n++)this._dayNames[n]=this._dayNames[n].toUpperCase();var t="",i="",u=this.element.find("#"+this._id+"_recurrenceType");t=ej.isNullOrUndefined(this._rRule.interval)?this._getLocalizedLabels("Every")+" ":this._getLocalizedLabels("Every")+" "+this._rRule.interval+" ";u.ejDropDownList({selectedItemIndex:this.model.frequencies.indexOf(this._rRule.freq.toLowerCase().replace(/^./,function(n){return n.toUpperCase()}))});switch(this._rRule.freq){case"DAILY":t+=this._getLocalizedLabels("RecurrenceDay").toLowerCase();this._rRuleFreq="DAILY";break;case"WEEKLY":for(this._rRuleFreq="WEEKLY",this.element.find(".weekdays").ejCheckBox({checked:!1}),r=this._rRule.weekDays.split(","),n=0;n<r.length;n++)this.element.find(".e-weekly"+r[n]).ejCheckBox({checked:!0}),i+=this._dayShortNames[this._dayNames.indexOf(r[n])]+(n==r.length-1?"":", ");t+=this._getLocalizedLabels("RecurrenceWeek").toLowerCase()+" "+this._getLocalizedLabels("On").toLowerCase()+" "+i;break;case"MONTHLY":this._rRuleFreq="MONTHLY";ej.isNullOrUndefined(this._rRule.monthDay)?(this.element.find(".e-monthposition").ejRadioButton({checked:!0}),this.element.find(".e-monthsrtday").ejDropDownList({selectedItemIndex:this._dayNames.indexOf(this._rRule.weekDays)}),this.element.find(".monthsrt").ejDropDownList({selectedItemIndex:this._rRule.setPositions-1}),i=this.element.find(".monthsrt").ejDropDownList("model.value").toLowerCase()+" "+this._dayFullNames[this._dayNames.indexOf(this._rRule.weekDays)]):(this.element.find(".e-monthdaytype").ejRadioButton({checked:!0}),this.element.find(".e-monthdate").ejNumericTextbox({value:parseInt(this._rRule.monthDay)}),i=parseInt(this._rRule.monthDay).toString());t+=this._getLocalizedLabels("RecurrenceMonth").toLowerCase()+" "+this._getLocalizedLabels("On").toLowerCase()+" "+i;break;case"YEARLY":this._rRuleFreq="YEARLY";ej.isNullOrUndefined(this._rRule.monthDay)?(this.element.find(".e-yearrecurposi").ejRadioButton({checked:!0}),this.element.find(".yearsrtday").ejDropDownList({selectedItemIndex:this._dayNames.indexOf(this._rRule.weekDays)}),this.element.find(".yearsrtmonth").ejDropDownList({selectedItemIndex:this._rRule.month-1}),this.element.find(".yearsrt").ejDropDownList({selectedItemIndex:this._rRule.setPositions-1}),i=this.element.find(".yearsrt").ejDropDownList("model.value").toLowerCase()+" "+this._dayFullNames[this._dayNames.indexOf(this._rRule.weekDays)]+" "+this._monthNames[this._rRule.month-1]):(this.element.find(".e-yearrecurday").ejRadioButton({checked:!0}),this.element.find(".yearmonth").ejDropDownList({selectedItemIndex:this._rRule.month-1}),this.element.find(".yeardate").ejNumericTextbox({value:parseInt(this._rRule.monthDay)}),i=this._monthNames[this._rRule.month-1]+" "+parseInt(this._rRule.monthDay));t+=this._getLocalizedLabels("RecurrenceYear").toLowerCase()+" "+this._getLocalizedLabels("On").toLowerCase()+" "+i}return this.element.find(".recurevery").ejNumericTextbox({value:ej.isNullOrUndefined(this._rRule.interval)?1:this._rRule.interval}),ej.isNullOrUndefined(this._rRule.count)?ej.isNullOrUndefined(this._rRule.until)?this.element.find(".e-recurnoend").ejRadioButton({checked:!0}):(this.element.find(".e-recuruntil").ejRadioButton({checked:!0}),this.element.find(".e-until").ejDatePicker({value:new Date(this._rRule.until)}),t+=" "+this._getLocalizedLabels("Until").toLowerCase()+" "+ej.format(new Date(this._rRule.until),this._datepattern(),this.model.locale)):(this.element.find(".e-recurafter").ejRadioButton({checked:!0}),this.element.find(".recurcount").ejNumericTextbox({value:this._rRule.count}),t+=", "+this._rRule.count+this._getLocalizedLabels("Times").toLowerCase()),t},r.prototype.getRecurrenceRule=function(){return this.closeRecurPublic(),this._recRule},r.prototype.closeRecurPublic=function(){var u=this._rRuleFreq,n,v,y="",o="",i="",t="",f,e,b,k,r,h,c,w,l,a,d,g;n=this._interval=this._findInterval(this.element.find(".recurevery"));switch(u){case"DAILY":t="FREQ="+u+";INTERVAL="+n;break;case"WEEKDAYS":t="FREQ=WEEKLY;BYDAY=MO,TU,WE,TH,FR";i="MO,TU,WE,TH,FR";break;case"WEEKLY":var p=this.element.find("input.weekdays"),s="";for(f=0;f<p.length;f++)p.eq(f).ejCheckBox("model.checked")==!0?i+=(i!=""?",":"")+this._dayNames[f].toUpperCase():"",p.eq(f).ejCheckBox("model.checked")==!0?s+=(s!=""?", ":"")+this._dayShortNames[f]:"";for(e=0;e<this._dayNamesValue.length;e++)"".indexOf(this._dayNamesValue[e])!=-1&&(i+=(i!=""?",":"")+this._dayNamesValue[e]),"".indexOf(this._culture.calendar.days.namesAbbr[e])!=-1&&(s+=(s!=""?",":"")+this._culture.calendar.days.namesAbbr[e]);i==""&&(b=this.element.find(".recurstartdate").ejDatePicker("model.value"),i=this._dayNames[b.getDay()].toUpperCase());k=this._getLocalizedLabels("RecurrenceWeek").toLowerCase()+" on "+s;this.element.find(".e-recurRule").html(k);t="FREQ="+u+";INTERVAL="+n+";BYDAY="+i;break;case"MONTHLY":this.element.find(".monthdaytype").ejRadioButton("model.checked")?(v=this.element.find(".monthdate").ejNumericTextbox("model.value"),t="FREQ="+u+";BYMONTHDAY="+v+";INTERVAL="+n,o=" "+this._getLocalizedLabels("On").toLowerCase()+" "+v):(r=this.element.find(".monthsrt").ejDropDownList("selectedItemIndex"),h=this.element.find(".e-monthsrtday").ejDropDownList("selectedItemIndex"),r=r<5?r+1:-1,t="FREQ="+u+";BYDAY="+this._dayNames[h]+";BYSETPOS="+r+";INTERVAL="+n,o=" "+this._getLocalizedLabels("On").toLowerCase()+" "+this.element.find(".monthsrt").ejDropDownList("model.value").toLowerCase()+" "+this._dayFullNames[h]);break;case"YEARLY":if(this.element.find("#"+this._id+"_yearday").ejRadioButton("model.checked"))c=this.element.find(".yearmonth").ejDropDownList("selectedItemIndex"),w=this._findInterval(this.element.find(".yeardate")),t="FREQ="+u+";BYMONTHDAY="+w+";BYMONTH="+(c+1)+";INTERVAL="+n,o=" "+this._getLocalizedLabels("On").toLowerCase()+" "+this._monthNames[c]+" "+w;else{var r=this.element.find(".yearsrt").ejDropDownList("selectedItemIndex"),c=this.element.find(".yearsrtmonth").ejDropDownList("selectedItemIndex"),h=this.element.find(".yearsrtday").ejDropDownList("selectedItemIndex");r=r<5?r+1:-1;t="FREQ="+u+";BYDAY="+this._dayNames[h]+";BYMONTH="+(c+1)+";BYSETPOS="+r+";INTERVAL="+n;o=" "+this._getLocalizedLabels("On").toLowerCase()+" "+this.element.find(".yearsrt").ejDropDownList("model.value").toLowerCase()+" "+this._dayFullNames[h]+" "+this._monthNames[c]}break;case"NEVER":this._recRule=undefined}return this.element.find(".e-recurafter").ejRadioButton("model.checked")?(l=this._findInterval(this.element.find(".recurcount")),t+=";COUNT="+l,y=l?", "+l+" "+this._getLocalizedLabels("Times"):", "):this.element.find(".e-recuruntil").ejRadioButton("model.checked")&&(a=this._findRecurCount(n,i,this),t+=";COUNT="+a,y=a?", "+a+" "+this._getLocalizedLabels("Times"):", "),d=(this._rRuleFreq!="WEEKDAYS"?this._getLocalizedLabels("Every"):"")+" "+(n>1?n+" ":""),g=d+this.element.find(".e-recurRule").html()+o+y,this._recRule=t,g},r}(ej.WidgetBase);ej.widget("ejRecurrenceEditor","ej.RecurrenceEditor",new i)})(jQuery);ej.RecurrenceEditor.Locale={};ej.RecurrenceEditor.Locale["en-US"]={Repeat:"Repeat",Never:"Never",Daily:"Daily",Weekly:"Weekly",Monthly:"Monthly",Yearly:"Yearly",First:"First",Second:"Second",Third:"Third",Fourth:"Fourth",Last:"Last",EveryWeekDay:"Every weekday",Every:"Every",RecurrenceDay:"Day(s)",RecurrenceWeek:"Week(s)",RecurrenceMonth:"Month(s)",RecurrenceYear:"Year(s)",RepeatOn:"Repeat on",RepeatBy:"Repeat by",StartsOn:"Starts on",Times:"times",Ends:"Ends",Day:"Day",The:"The",OfEvery:"Of",After:"After",On:"On",Occurrence:"Occurrence(s)",Until:"Until"}});
