/*!
*  filename: ej.datepicker.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.globalize.min","./../common/ej.core.min","./../common/ej.touch.min"],n):n()})(function(){(function(n,t,i){t.widget("ejDatePicker","ej.DatePicker",{element:null,_rootCss:"e-datepicker",model:null,validTags:["input","div","span"],_setFirst:!1,_addToPersist:["value"],_cancelValue:!1,type:"editor",angular:{require:["?ngModel","^?form","^?ngModelOptions"],requireFormatters:!0},defaults:{dayHeaderFormat:"min",showPopupButton:!0,enableAnimation:!0,showFooter:!0,displayInline:!1,htmlAttributes:{},dateFormat:"",watermarkText:"Select date",value:null,minDate:new Date("01/01/1900"),maxDate:new Date("12/31/2099"),startLevel:"month",depthLevel:"",cssClass:"",startDay:-1,stepMonths:1,locale:"en-US",showOtherMonths:!0,enableStrictMode:!1,enablePersistence:!1,enabled:!0,width:"",height:"",enableRTL:!1,showRoundedCorner:!1,headerFormat:"MMMM yyyy",buttonText:"Today",readOnly:!1,specialDates:null,fields:{date:"date",tooltip:"tooltip",iconClass:"iconClass",cssClass:"cssClass"},showTooltip:!0,showDisabledRange:!0,highlightSection:"none",highlightWeekend:!1,validationRules:null,validationMessage:null,validationMessages:null,allowEdit:!0,tooltipFormat:"ddd MMM dd yyyy",allowDrillDown:!0,blackoutDates:[],beforeDateCreate:null,open:null,close:null,select:null,change:null,focusIn:null,focusOut:null,beforeOpen:null,beforeClose:null,navigate:null,create:null,destroy:null,weekNumber:!1,timeZone:!0},dataTypes:{startDay:"number",stepMonths:"number",showOtherMonths:"boolean",enableStrictMode:"boolean",showRoundedCorner:"boolean",enableRTL:"boolean",displayInline:"boolean",showPopupButton:"boolean",locale:"string",readOnly:"boolean",cssClass:"string",dateFormat:"string",watermarkText:"string",headerFormat:"string",buttonText:"string",specialDates:"data",showTooltip:"boolean",highlightSection:"enum",highlightWeekend:"boolean",enableAnimation:"boolean",validationRules:"data",validationMessage:"data",validationMessages:"data",htmlAttributes:"data",tooltipFormat:"string",allowEdit:"boolean",allowDrillDown:"boolean",weekNumber:"boolean"},_renderPopup:function(){this.sfCalendar=t.buildTag("div.e-datepicker e-popup e-widget "+this.model.cssClass+" e-calendar "+(this.model.specialDates?this.model.specialDates[0][this._mapField._icon]?"e-icons ":"":""),"",{},{id:this._id?"e-"+this._id:""}).attr({"aria-hidden":"true"}).attr(this._isIE8?{unselectable:"on"}:{}).insertBefore(this.element);this.model.displayInline&&!this.element.is("input")&&this.sfCalendar.addClass("e-inline");this.popup=this.sfCalendar;t.isTouchDevice()||this.sfCalendar.addClass("e-ntouch");this._setRestrictDateState(this.model.showDisabledRange);this._createCalender();this._setDisplayInline(this.model.displayInline);this._resizeCalender();this._setRTL(this.model.enableRTL);this._setRoundedCorner(this.model.showRoundedCorner);this._wireCalendarEvents()},_setModel:function(i){var u=!1,f=!1,e=!1,r,o;for(r in i){r!="showPopupButton"&&r!="width"&&r!="dateFormat"&&r!="height"&&r!="readOnly"&&r!="allowEdit"&&r!="enabled"&&r!="watermarkText"&&r!="htmlAttributes"&&r!="validationMessages"&&r!="validationRules"&&t.isNullOrUndefined(this.sfCalendar)&&this._renderPopup();switch(r){case"dayHeaderFormat":this.model.dayHeaderFormat=i[r];u=f=!0;break;case"weekNumber":this.model.weekNumber=i[r];this._refreshDatepicker();break;case"showPopupButton":this._renderDateIcon(i[r],!0);break;case"displayInline":if(i[r]||this._bindDateButton(),this._setDisplayInline(i[r]),!this.model.allowEdit&&!i[r]&&this._isInputBox)this.element.on("mousedown",n.proxy(this._showDatePopUp,this));break;case"value":t.isPlainObject(i[r])&&(i[r]=null);t.isNullOrUndefined(i.minDate)&&t.isNullOrUndefined(i.maxDate)?(this._setDateValue(i[r]),this._specificFormat()&&(this._stopRefresh=!0),i[r]=this.model.value):this._updateDateValue(i[r]);this.model.value&&this.sfCalendar?n(this.element).attr("aria-activedescendant",n(this.sfCalendar.find(".e-active")).attr("id")):n(this.element).removeAttr("aria-activedescendant");e=u=f=!0;break;case"specialDates":this.model.specialDates=i[r];this._createSpecialDateObject();u=f=!0;break;case"fields":this.model.fields=i[r];this._mapField=this._getMapper();u=f=!0;break;case"showTooltip":this.model.showTooltip=i[r];u=f=!0;break;case"highlightWeekend":this.model.highlightWeekend=i[r];u=f=!0;break;case"highlightSection":this.model.highlightSection=i[r];u=f=!0;break;case"dateFormat":this.model.dateFormat=i[r];this._ensureValue();break;case"minDate":this._setMinDate(i[r]);i[r]=this.model.minDate;this._ensureValue();e=u=f=!0;break;case"maxDate":this._setMaxDate(i[r]);i[r]=this.model.maxDate;this._ensureValue();e=u=f=!0;break;case"locale":this.model.locale=i[r];this.model.startDay=t.isNullOrUndefined(this._options.startDay)&&this.model.startDay===this.culture.calendar.firstDay?-1:this._options.startDay===this.defaults.startDay?-1:this.model.startDay;this.model.dateFormat=t.isNullOrUndefined(this._options.dateFormat)&&this.model.dateFormat===this.culture.calendar.patterns.d?"":this.model.dateFormat;this._setCulture(i[r]);this.model.value&&this._setDateValue(this.model.value);i[r]=this.model.locale;u=f=!0;break;case"showOtherMonths":this.model.showOtherMonths=i[r];this._otherMonthsVisibility();break;case"enableStrictMode":this.model.enableStrictMode=i[r];e=u=f=!0;break;case"validationRules":this.model.validationRules!=null&&(this.element.rules("remove"),this.model.validationMessages=null);this.model.validationRules=i[r];this.model.validationRules!=null&&(this._initValidator(),this._setValidation());break;case"validationMessages":this.model.validationMessages=i[r];this.model.validationRules!=null&&this.model.validationMessages!=null&&(this._initValidator(),this._setValidation());break;case"validationMessage":this.model.validationMessages=i[r];this.model.validationRules!=null&&this.model.validationMessages!=null&&(this._initValidator(),this._setValidation());break;case"readOnly":this.model.readOnly=i[r];this._disbleMaualInput();break;case"width":this._setWidth(i[r]);break;case"height":this._setHeight(i[r]);break;case"cssClass":this._setSkin(i[r]);break;case"enableRTL":this._setRTL(i[r]);break;case"showRoundedCorner":this._setRoundedCorner(i[r]);break;case"enabled":i[r]?this.enable():this.disable();break;case"buttonText":t.isNullOrUndefined(this._options)&&(this._options={});this._options.buttonText=this.model.buttonText=i[r];this._localizedLabels.buttonText=this.model.buttonText;this._setFooterText(i[r]);break;case"showFooter":this._enableFooter(i[r]);break;case"watermarkText":t.isNullOrUndefined(this._options)&&(this._options={});this._options.watermarkText=this.model.watermarkText=i[r];this._localizedLabels.watermarkText=this.model.watermarkText;this._setWaterMark();break;case"startDay":o=i[r];(parseInt(i[r])<0||parseInt(i[r])>6)&&(i[r]=this.culture.calendar.firstDay,o=-1);this.model.startDay=i[r];t.isNullOrUndefined(this._options)&&(this._options={});this._options.startDay=o;u=f=!0;break;case"startLevel":this.model.startLevel=i[r];u=f=!0;break;case"headerFormat":this.model.headerFormat=i[r];u=f=!0;break;case"depthLevel":this.model.depthLevel=i[r];u=f=!0;break;case"htmlAttributes":this._addAttr(i[r]);break;case"allowEdit":this._changeEditable(i[r]);break;case"tooltipFormat":this.model.tooltipFormat=i[r];u=f=!0;break;case"allowDrillDown":this._allowQuickPick(i[r]);u=f=!0;break;case"showDisabledRange":this._setRestrictDateState(i[r]);break;case"blackoutDates":this.model.blackoutDates=i[r];this._initDisableObj(this.model.blackoutDates);u=f=!0}}e&&(this._validateMinMaxDate(),i.value=this.model.value,i.maxDate=this.model.maxDate,i.minDate=this.model.minDate);this._setWaterMark();u&&(this.isValidState||this.model.displayInline)&&this._refreshDatepicker();f&&!this._startNavigate&&this._startLevel(this.model.startLevel);this._triggerChangeEvent();this._checkErrorClass()},observables:["value"],_destroy:function(){this.model.displayInline&&n(window).off("resize",n.proxy(this._OnWindowResize,this));this._isOpen&&this.hide();this.sfCalendar&&this.sfCalendar.remove();this.wrapper&&(this.element.insertAfter(this.wrapper),this.wrapper.remove());this.element.removeClass("e-datepicker e-input");this.element.removeAttr("aria-atomic aria-live aria-activedescendant aria-expanded role placeholder tabindex");this._cloneElement.hasAttribute("name")||this.element.removeAttr("name")},_init:function(n){this._options=n;this._cloneElement=this.element.clone();this._dt_drilldown=!1;this._ISORegex();this._initDisableObj(this.model.blackoutDates);this.animation={open:{duration:200},close:{duration:100}};this._animating=!1;this._isInputBox=this._isInputBox();this._startNavigate=!1;this._keyboardInteraction=!1;this._isSupport=document.createElement("input").placeholder==i?!1:!0;this._checkAttribute();this._setValues();this._createDatePicker();t.isNullOrUndefined(n)||t.isNullOrUndefined(n.validationMessage)||(this.model.validationMessages=this.model.validationMessage);this.model.validationRules!=null&&(this._initValidator(),this._setValidation());n&&n.value!=i&&n.value!=this.element.val()&&this._trigger("_change",{value:this.element.val()})},_ISORegex:function(){this._tokens=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g;this._extISORegex=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/;this._basicISORegex=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/;this._isISODate=/[T]/;this._numberRegex={2:/\d\d?/,4:/^\d{4}/,z:/Z|[+-]\d\d(?::?\d\d)?/gi,t:/T/,"-":/\-/,":":/:/};this._zeroRegex=/Z|[+-]\d\d(?::?\d\d)?/;this._dates=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/]];this._times=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]]},_initValidator:function(){this.element.closest("form").data("validator")||this.element.closest("form").validate()},_setValidation:function(){var r,f,i,u,e;this.element.rules("add",this.model.validationRules);r=this.element.closest("form").data("validator");r=r?r:this.element.closest("form").validate();f=this.element.attr("name");r.settings.messages[f]={};for(i in this.model.validationRules)if(u=null,!t.isNullOrUndefined(this.model.validationRules[i])){if(t.isNullOrUndefined(this.model.validationRules.messages&&this.model.validationRules.messages[i])){r.settings.messages[f][i]=n.validator.messages[i];for(e in this.model.validationMessages)i==e?u=this.model.validationMessages[i]:""}else u=this.model.validationRules.messages[i];r.settings.messages[f][i]=u!=null?u:n.validator.messages[i]}},setValue:function(n){typeof n=="string"&&(n=new Date(n));var t=this.model.value;this._setDateValue(n);this._checkErrorClass();t!=this.model.value&&this._triggerChangeEvent()},_checkAttribute:function(){for(var u=["min","max","readonly","disabled"],f=["minDate","maxDate","readOnly","enabled"],i,n,r=0;r<u.length;r++)i=this.element.attr(u[r]),n=f[r],t.isNullOrUndefined(i)||(t.isNullOrUndefined(this._options)?this.model[n]=n!="enabled"&&n!="readOnly"?new Date(i):n=="readOnly"?this.element.is("[readonly]"):!this.element.is("[disabled]"):t.isNullOrUndefined(this._options[n])&&(this.model[n]=n!="enabled"&&n!="readOnly"?new Date(i):n=="readOnly"?this.element.is("[readonly]"):!this.element.is("[disabled]")))},_updateDateValue:function(n){var t=this._checkDateObject(n);t!=null?(this.isValidState=!0,t==""?(this.element.val(""),this.model.value=null):(this.model.value=t,this._preTxtValue=this.element.val(this._formatter(this.model.value,this.model.dateFormat)))):(typeof t=="string"&&this.model.enableStrictMode?this.element.val(n):this.element.val(""),this.model.value=null,this.isValidState=this.element.val()==""?!0:!1);this._removeWatermark()},_ensureValue:function(){var n=this._parseDate(this.element.val(),this.model.dateFormat);this.model.value?this._setDateValue(this.model.value):n&&this._setDateValue(n)},_changeEditable:function(t){var i=t?"_on":"_off";if(this.element.is(":input")){if(t)this.model.readOnly||this.element.attr("readonly",!1),this.element.off("mousedown",n.proxy(this._showDatePopUp,this));else if(this.model.readOnly||this.element.attr("readonly","readonly"),!this.model.displayInline)this.element.on("mousedown",n.proxy(this._showDatePopUp,this));this[i](this.element,"blur",this._onFocusOut);this[i](this.element,"focus",this._onFocusIn);this[i](this.element,"keydown",this._onKeyDown)}},_allowQuickPick:function(t){n(".e-datepicker-headertext",this.sfCalendar)[t?"on":"off"]("click",n.proxy(this._forwardNavHandler,this))},_setRestrictDateState:function(n){var t=n?"addClass":"removeClass";this.sfCalendar[t]("e-dp-restrict-show")},_setValues:function(){this.Date=new Date;this._id=this.element[0].id;this.isValidState=!0;this._setCulture(this.model.locale);this._setMinDate(this.model.minDate);this._setMaxDate(this.model.maxDate);this._calendarDate=this._zeroTime(new Date);(this.model.startDay<0||this.model.startDay>6)&&(this.model.startDay=0);this.Date.firstDayOfWeek=this.model.startDay;this.Date.fullYearStart="20";this._showHeader=!0;t.isNullOrUndefined(this.model.value)&&this.element[0].value!=""&&(this.model.value=this.element[0].value);this._validateMinMaxDate();this._dateValue=new Date(this._calendarDate.toString());this._isIE7=this._checkIE7();this._isIE8=t.browserInfo().name=="msie"&&t.browserInfo().version=="8.0"?!0:!1;this._isIE9=t.browserInfo().name=="msie"&&t.browserInfo().version=="9.0"?!0:!1;this._getInternalEvents=!1;this._flag=!0;this._ejHLWeekEnd=!1;this._isOpen=!1;this._prevDate=null;this._preValue=null;this._isFocused=!1},_addAttr:function(i){var r=this;n.map(i,function(n,i){var u=i.toLowerCase();u=="class"?r.wrapper.addClass(n):u=="disabled"?r.disable():u=="readOnly"?r.model.readOnly=!0:u=="style"?r.wrapper.attr(i,n):u=="id"?(r.wrapper.attr(i,n+"_wrapper"),r.element.attr(i,n)):t.isValidAttr(r.element[0],i)?r.element.attr(i,n):r.wrapper.attr(i,n)})},_createDatePicker:function(){this._createWrapper();this._wireEvents();this.model.displayInline&&this.show();this.model.enableRTL&&this._setRTL(!0);this.model.showRoundedCorner&&this._setRoundedCorner(!0)},_checkNameAttr:function(){!this.element.attr("name")&&this._isInputBox&&this.element.attr("name",this.element[0].id);this.model.displayInline&&!this._isInputBox&&this._hiddenInput.attr("name",this.element[0].id)},_createWrapper:function(){if(this._getMapper(),this.model.specialDates&&this._createSpecialDateObject(),this.element[0].hasAttribute("tabindex")||this.element.attr("tabindex","0"),this._isInputBox&&(this.element.addClass("e-input").attr({"aria-atomic":"true","aria-live":"assertive","aria-expanded":"false",role:"combobox"}),this.wrapper=t.buildTag("span.e-datewidget e-widget "+this.model.cssClass),this.wrapper.attr("style",this.element.attr("style")),this.element.removeAttr("style"),t.isTouchDevice()||this.wrapper.addClass("e-ntouch"),this.innerWrapper=t.buildTag("span.e-in-wrap e-box e-padding"),this.wrapper.append(this.innerWrapper).insertBefore(this.element),this.innerWrapper.append(this.element),this.dateIcon=t.buildTag("span.e-select#"+this._id+"-img","",{},this._isIE8?{unselectable:"on"}:{}).append(t.buildTag("span.e-icon e-calendar","",{},{"aria-label":"Select"}).attr(this._isIE8?{unselectable:"on"}:{})).insertAfter(this.element)),!this._isSupport||this.model.displayInline&&!this._isInputBox){this._hiddenInput=t.buildTag("input.e-input e-placeholder ","",{},{type:"text"}).insertAfter(this.element);this._isInputBox&&this._hiddenInput.val(this._localizedLabels.watermarkText);this._hiddenInput.css("display","block");var i=this;n(this._hiddenInput).focus(function(){i.element.focus()})}this._checkNameAttr();this.model.height||(this.model.height=this.element.attr("height"));this.model.width||(this.model.width=this.element.attr("width"));this._setHeight(this.model.height);this._setWidth(this.model.width);this._id&&n("#e-"+this._id).remove();this._setDateValue(this.model.value);this._preValue=this._parseDate(this.element.val(),this.model.dateFormat);this._setWaterMark();this._dateValue=new Date(this._calendarDate.toString());this.model.displayInline?this._renderPopup():this._isInputBox&&this._renderDateIcon(this.model.showPopupButton,!1);this.model.readOnly&&this._disbleMaualInput();this.model.enabled?this.model.enabled&&n(this.element).hasClass("e-disable")&&this.enable():this.disable();this._layoutChanged();this._checkErrorClass();this._addAttr(this.model.htmlAttributes)},_isInputBox:function(){return this.element.is("input[type=date]")&&this.element.attr("type","text"),this.element.is("input")&&(this.element.is("input[type=text]")||!this.element.attr("type"))},_renderDateIcon:function(n,t){t&&this.model.showPopupButton==n||(!n&&this.dateIcon?(this._bindInputEvent(),this.dateIcon.css("display","none"),this.innerWrapper.removeClass("e-padding")):(this.innerWrapper&&(this.innerWrapper.addClass("e-padding"),this.dateIcon.css("display","block")),this.model.displayInline||this._bindDateButton()),this.model.showPopupButton=n)},_resizeCalender:function(){this.model.dayHeaderFormat=="short"||this.model.dayHeaderFormat=="min"||this.model.dayHeaderFormat=="none"?this.sfCalendar.removeClass("e-headerlong"):this.model.dayHeaderFormat=="long"&&this.sfCalendar.addClass("e-headerlong")},_setWidth:function(n){n?this.wrapper?this.wrapper.width(n):this.element.width(n):this.model.width=this.wrapper?this.wrapper.outerWidth():this.element.width()},_setHeight:function(n){n?this.wrapper?this.wrapper.height(n):this.element.height(n):this.model.height=this.wrapper?this.wrapper.outerHeight():this.element.height();this._isIE7&&this.element.height(this.innerWrapper.height())},_setRTL:function(n){n?(this.wrapper&&this.wrapper.addClass("e-rtl"),this.sfCalendar&&this.sfCalendar.addClass("e-rtl")):(this.wrapper&&this.wrapper.removeClass("e-rtl"),this.sfCalendar&&this.sfCalendar.removeClass("e-rtl"))},_setRoundedCorner:function(n){n?(this.innerWrapper&&this.innerWrapper.addClass("e-corner"),this.sfCalendar&&this.sfCalendar.addClass("e-corner")):(this.innerWrapper&&this.innerWrapper.removeClass("e-corner"),this.sfCalendar&&this.sfCalendar.removeClass("e-corner"))},_refreshDatepicker:function(){var r,f,u,t;if(this._stopRefresh){this._stopRefresh=!1;return}if(r=this.element.val(),f=!1,this.model.navigate!==null&&this.sfCalendar.find("table")[0]!==i)if(u=this.sfCalendar.find("table")[0].className,u==="e-dp-viewdays"&&+this._dateValue!=+this._calendarDate)this._calendarDate=this._dateValue,f=!0;else if(u!=="e-dp-viewdays"){(u==="e-dp-viewmonths"||u==="e-dp-viewyears"||u==="e-dp-viewallyears")&&(this._startNavigate=!0);return}t=this._specificFormat()&&this._formatter(this._preValue,this.model.dateFormat,this.model.locale)!=r?this._parseDate(r,!0):this._parseDate(r);t=this._validateYearValue(t);this.model.navigate!==null&&t!==null&&t!==this._calendarDate&&(t=this._calendarDate);this._setDateValue(t);this._specificFormat()&&this._compareDate(this.model.value,this._calendarDate)&&this.element.val(r);n(".e-datepicker-headertext",this.sfCalendar).text(this._formatter(this._calendarDate,this.model.headerFormat));this._resizeCalender();this._dateValue=new Date(this._calendarDate.toString());this._hoverDate=this._calendarDate.getDate()-1;this._renderCalendar(this,this._dateValue);f&&r===""&&this._addFocus("day",this._hoverDate);this._setFooterText(this._localizedLabels.buttonText);this._enableFooter(this.model.showFooter);this._layoutChanged()},_removeCurrentMonthFromHideDate:function(){for(var u,i,r,f=this.sfCalendar.find("tbody.e-datepicker-days tr").length,t=0;t<f;t++)for(u=this.sfCalendar.find("tbody.e-datepicker-days tr")[t].cells.length,i=0;i<u;i++)r=n(this.sfCalendar.find("tbody.e-datepicker-days tr")[t].cells[i]),r.hasClass("e-hidedate")&&r.hasClass("current-month")&&r.removeClass("current-month")},_validateYearValue:function(n){if(n!=null){var i=t.preferredCulture(this.model.locale).calendars.standard.twoDigitYearMax;i=typeof i=="string"?(new Date).getFullYear()%100+parseInt(i,10):i;this._calendarDate.getFullYear()-n.getFullYear()==100&&this._calendarDate.getFullYear()>i&&n.setFullYear(this._calendarDate.getFullYear())}return n},_setFooterText:function(t){n(".e-footer-text",this.sfCalendar).html(t)},_setSkin:function(n){this.wrapper?(this.wrapper.removeClass(this.model.cssClass),this.wrapper.addClass(n)):(this.element.removeClass(this.model.cssClass),this.element.addClass(n));this.sfCalendar.removeClass(this.model.cssClass);this.sfCalendar.addClass(n)},_setDisplayInline:function(t){this.model.displayInline=t;t&&this._isInputBox?(this.sfCalendar.insertAfter(this.wrapper),this._setDatePickerPosition()):t?(this.element.append(this.sfCalendar),this._isSupport&&this._isInputBox||this._hiddenInput.css("display","none")):(this.sfCalendar.css("display","none"),n("body").append(this.sfCalendar),this._isOpen=!1);t&&(this.show(),this._off(this.dateIcon,"mousedown",this._showDatePopUp),this.element.off("mousedown",n.proxy(this._showDatePopUp,this)))},_disbleMaualInput:function(){this.model.readOnly?(n(this.element).attr("readonly","readonly"),this.model.displayInline||this.hide()):this.model.allowEdit&&n(this.element).prop("readonly",!1)},_checkDateObject:function(n,t){var t;if(n&&(typeof JSON!="object"||JSON.stringify(n)!=="{}"))n instanceof Date||(t=this._specificFormat()?this._parseDate(n,!0):this._parseDate(n,t),n=t?t:(t=this._checkJSONString(n))?t:null);else return null;return!isNaN(Date.parse(n))&&(this._dateValue=this._calendarDate=this._zeroTime(n),this._validateDate(n))?this._dateValue:null},_checkJSONString:function(n){if(isNaN(Date.parse(n))){if(this.model.enableStrictMode&&t.parseDate(n,this.model.value,this.model.locale)==null)return null;if(this._extISORegex.exec(n)||this._basicISORegex.exec(n))return this._dateFromISO(n)}else if(new Date(n).toJSON()===n||new Date(n).toDateString()===n||new Date(n).toGMTString()===n||new Date(n).toISOString()===n||new Date(n).toLocaleString()===n||new Date(n).toString()===n||new Date(n).toUTCString()===n){if(this.model.timeZone)return new Date(new Date(n).getTime()+t.serverTimezoneOffset*36e5);if(n.match(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(\.\d+)?(([+-]\d\d:\d\d)|Z)?$/i)&&n.match(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(\.\d+)?(([+-]\d\d:\d\d)|Z)?$/i).length>0)return n=n.split("T"),n=n[0],t.parseDate(n,"yyyy-MM-dd",this.model.locale)}else if(typeof n=="string")return this._dateFromISO(n)},_dateFromISO:function(n){var i=this._isISODate.test(n)&&(this._extISORegex.exec(n)||this._basicISORegex.exec(n)),c="",l="",a="",v,e,o,f,r,t,s,u,h,y;if(i){for(e=0;e<this._dates.length;e++)if(this._dates[e][1].exec(i[1])){c=this._dates[e][0];break}if(i[3])for(o=0;o<this._times.length;o++)if(this._times[o][1].exec(i[3])){l=(i[2]||" ")+this._times[o][0];break}for(i[4]&&this._zeroRegex.exec(i[4])&&(a="Z"),v=c+l+a,f=v.match(this._tokens),t=[],u=0;u<f.length;u++)h=f[u],s=this._checkLiteral(f[u]),y=this._numberRegex[s?f[u].toLowerCase():h.length]||new RegExp("^\\d{1,"+h.length+"}"),r=n.match(y),r&&(n.substr(0,n.indexOf(r))>=0&&!s&&(f[u].indexOf("M")>=0?t.push(parseInt(r[0])-1):t.push(parseInt(r[0]))),n=n.slice(n.indexOf(r[0])+r[0].length));return i[4]=="Z"?new Date(Date.UTC.apply(null,t)):new Date(t[0],t[1],t[2],t[3],t[4],t[5])}return new Date(n+"")},_checkLiteral:function(n){var t=n.toLowerCase();return t=="t"||t=="z"||t==":"||t=="-"?!0:!1},_checkInstanceType:function(n){if(n=this._stringToObject(n),n)n instanceof Date||(n=this._parseDate(n));else return null;return isNaN(Date.parse(n))?null:this._zeroTime(n)},_stringToObject:function(n){if(typeof n=="string"){var i=t.parseDate(n,this.model.dateFormat,this.model.locale);n=i!=null?i:new Date(n)}return n},_validateMinMaxDate:function(){var n=!1,t=!1;this.model.maxDate<this.model.minDate&&(this.model.minDate=this.model.maxDate);this.model.enableStrictMode?this.model.value?this.model.value<this.model.minDate?(this._calendarDate=this.model.minDate,this.isValidState=!1,t=!0):this.model.value>this.model.maxDate?(this._calendarDate=this.model.maxDate,this.isValidState=!1,t=!0):this.isValidState=!0:this._calendarDate<this.model.minDate?this._calendarDate=this.model.minDate:this._calendarDate>this.model.maxDate&&(this._calendarDate=this.model.maxDate):(this.model.value?this.model.value<this.model.minDate?(this._calendarDate=this.model.value=this.model.minDate,n=!0):this.model.value>this.model.maxDate&&(this._calendarDate=this.model.value=this.model.maxDate,n=!0):(this.element.val(""),this._calendarDate<this.model.minDate?this._calendarDate=this.model.minDate:this._calendarDate>this.model.maxDate&&(this._calendarDate=this.model.maxDate)),this.isValidState=!0);n&&this.element.val(this._formatter(this.model.value,this.model.dateFormat));t&&this._getInternalEvents&&this._trigger("outOfRange")},_setCulture:function(n){this.culture=t.preferredCulture(n);this.culture&&(this.model.locale=this.culture.name=="en"?"en-US":this.culture.name,this.Date.dayNames=this.culture.calendar.days.names,this.Date.dayNamesMin=this.culture.calendar.days.namesShort,this.Date.abbrDayNames=this.culture.calendar.days.namesAbbr,this.Date.monthNames=this.culture.calendar.months.names,this.Date.abbrMonthNames=this.culture.calendar.months.namesAbbr,this.Date.format=this.culture.calendar.patterns.d,this.model.dateFormat==""&&(this.model.dateFormat=this.culture.calendar.patterns.d),this.model.startDay==-1&&(this.model.startDay=this.culture.calendar.firstDay));this._separator=this._getSeparator();this._localizedLabels=this._getLocalizedLabels();t.isNullOrUndefined(this._options)||(t.isNullOrUndefined(this._options.watermarkText)||(this._localizedLabels.watermarkText=this._options.watermarkText),t.isNullOrUndefined(this._options.buttonText)||(this._localizedLabels.buttonText=this._options.buttonText));this._localizedLabelToModel()},_localizedLabelToModel:function(){this.model.watermarkText=this._localizedLabels.watermarkText;this.model.buttonText=this._localizedLabels.buttonText},_setWaterMark:function(){if(this.element!=null&&this.element.hasClass("e-input"))return this._localizedLabels.watermarkText&&this.element.val()==""&&(this.isValidState=!0,this._checkErrorClass()),this._isSupport||this.element.val()!=""?n(this.element).attr("placeholder",this._localizedLabels.watermarkText):this._hiddenInput.css("display","block").val(this._localizedLabels.watermarkText),!0},_setDatePickerPosition:function(){if(!this.model.displayInline||this._isInputBox){var i=this.element.is("input")?this.wrapper:this.element,r=this._getOffset(i),o,s,l=n(document).scrollTop()+n(window).height()-(r.top+n(i).outerHeight()),a=r.top-n(document).scrollTop(),f=this.sfCalendar.outerHeight(),e=this.sfCalendar.outerWidth(),u=r.left,h=i.outerHeight(),p=(h-i.height())/2,v=this._getZindexPartial(),c=3,y=f<l||f>a?r.top+h+c:r.top-f-c;o=n(document).scrollLeft()+n(window).width()-u;s=n(document).scrollLeft()+u+i.width();(this.model.enableRTL||e>o&&e<u+i.outerWidth()&&!t.isNullOrUndefined(this.wrapper))&&(u+=this.wrapper.width()-this.sfCalendar.width());e>s&&(u=r.left);this.sfCalendar.css({left:u+"px",top:y+"px","z-index":v})}},_getOffset:function(n){return t.util.getOffset(n)},_getZindexPartial:function(){return t.util.getZindexPartial(this.element,this.sfCalendar)},_setMinDate:function(n){this.model.minDate=this._checkInstanceType(n);this.model.minDate||(this.model.minDate=new Date("11/31/1899"))},_setMaxDate:function(n){this.model.maxDate=this._checkInstanceType(n);this.model.maxDate||(this.model.maxDate=new Date("12/31/2099"))},_setDateValue:function(n,i){var r=this._checkDateObject(n,i);r!=null?(this.isValidState=!0,this.model.value=new Date(r),this.model.displayInline||this.wrapper.addClass("e-valid"),this._validateMinMaxDate(),this._preTxtValue=this.element.val(this._formatter(this.model.value,this.model.dateFormat))):(n instanceof Date&&(this._validateMinMaxDate(),n=this._formatter(n,this.model.dateFormat)),this.model.enableStrictMode?this.element.val(n):this.element.val(null),this.model.value=null,this.model.displayInline||this.wrapper.removeClass("e-valid"),this._triggerChangeEvent(),this.isValidState=this.element.val()==""||t.isNullOrUndefined(this.element.val())?!0:!1);this._removeWatermark()},_updateInputVal:function(){var n=this._validateValue();(n!=null||!this.model.enableStrictMode)&&this.sfCalendar&&this.sfCalendar.find(".e-datepicker-days").is(":visible")&&this._refreshDatepicker()},_validateInputVal:function(){var n=this._validateValue();n!=null&&(this.model.enableStrictMode||(n<=this.model.maxDate&&n>=this.model.minDate?this.isValidState=!0:(this.model.value=null,this.isValidState=!0)))},_validateValue:function(){var n;return n=this._specificFormat()&&this.element.val()!=this._formatter(this._preValue,this.model.dateFormat,this.model.locale)?this._parseDate(this.element.val(),!0):this._parseDate(this.element.val()),this._validateYearValue(n)},_getSeparator:function(){var t,i,n;for(t=this.culture?this.culture.calendar.patterns.d:this.model.dateFormat,i=new RegExp("^[a-zA-Z0-9]+$"),n=0;n<t.length;n++)if(!i.test(t[n]))return t[n]},_checkIE7:function(){if(navigator.appName=="Microsoft Internet Explorer"){var t=new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})"),n=-1;if(t.exec(navigator.userAgent)!=null&&(n=parseFloat(RegExp.$1)),n>=7&&n<8)return!0}return!1},_isValidDate:function(n){return n&&typeof n.getTime=="function"&&isFinite(n.getTime())},_formatter:function(n,i){var r=this._checkFormat(i);return t.format(n,r,this.model.locale)},_parseDate:function(n,r){var f=this._checkFormat(this.model.dateFormat),u=n;return!this._specificFormat()||u==i||n==""||r==!0||t.format(t.parseDate(u,f,this.model.locale),this.model.dateFormat,this.model.locale)==u?t.parseDate(n,f,this.model.locale):this._dateValue},_checkFormat:function(n){var i=this,r=this._regExp();return n.replace(r,function(n){return n==="/"?t.preferredCulture(i.model.locale).calendars.standard["/"]!=="/"?"'/'":n:n})},_regExp:function(){return/\/dddd|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yy|HH|H|hh|h|mm|m|fff|ff|f|tt|ss|s|zzz|zz|z|gg|g|"[^"]*"|'[^']*'|[/]/g},isLeapYear:function(n){return n%4==0&&n%100!=0||n%400==0},_zeroTime:function(n){var t=typeof n=="string"?this._parseDate(n):new Date(n);return t.setMilliseconds(0),t.setSeconds(0),t.setMinutes(0),t.setHours(0),t},_getDaysInMonth:function(n){return[31,this.isLeapYear(n)?29:28,31,30,31,30,31,31,30,31,30,31][n.getMonth()]},_addDays:function(n,t){return n.setDate(n.getDate()+t),n},_addYears:function(n,t){return n.setFullYear(n.getFullYear()+t),n},_addMonths:function(n,t){var i=n.getDate();return n.setMonth(n.getMonth()+t),i>n.getDate()&&this._addDays(n,-n.getDate()),n},_isWeekend:function(n){return n.getDay()==0||n.getDay()==6},_isSpecialDates:function(n){if(this.model.specialDates)for(var t=0;t<this.model.specialDates.length;t++)if(this.model.specialDates[t]&&this.model.specialDates[t][this._mapField._date]&&n.getDate()==this.model.specialDates[t][this._mapField._date].getDate()&&n.getMonth()==this.model.specialDates[t][this._mapField._date].getMonth()&&n.getFullYear()==this.model.specialDates[t][this._mapField._date].getFullYear())return this._getIndex=t,!0;return!1},_getMapper:function(){var n=this.model.fields;this._mapField={};this._mapField._date=n&&n.date?n.date:"date";this._mapField._tooltip=n&&n.tooltip?n.tooltip:"tooltip";this._mapField._icon=n&&n.iconClass?n.iconClass:"iconClass";this._mapField._custom=n&&n.cssClass?n.cssClass:"cssClass"},_createSpecialDateObject:function(){for(var n=0;n<this.model.specialDates.length;n++)this.model.specialDates[n][this._mapField._date]=this._checkInstanceType(this.model.specialDates[n][this._mapField._date])},_getMonthName:function(n,t){return n?this.Date.abbrMonthNames[t.getMonth()]:this.Date.monthNames[t.getMonth()]},_displayNewMonth:function(n,t){return this._setDisplayedMonth(this.displayedMonth+n,this.displayedYear+t,!0),!1},_setDisplayedMonth:function(n,t,r){var f,e,u,s,h,o;this.model.minDate!=i&&this.model.maxDate!=i&&(f=new Date(this.model.minDate.getTime()),f.setDate(1),e=new Date(this.model.maxDate.getTime()),e.setDate(1),!n&&!t||isNaN(n)&&isNaN(t)?(u=this._zeroTime(new Date),u.setDate(1)):u=isNaN(n)?new Date(t,this.displayedMonth,1):isNaN(t)?new Date(this.displayedYear,n,1):new Date(t,n,1),u.getTime()<f.getTime()?u=f:u.getTime()>e.getTime()&&(u=e),s=this.displayedMonth,h=this.displayedYear,this.displayedMonth=u.getMonth(),this.displayedYear=u.getFullYear(),o=u,r&&(this.displayedMonth!=s||this.displayedYear!=h)&&(this._renderCalendar(this,o),this._dateValue=o,this._trigger("monthChanged",[this.displayedMonth,this.displayedYear])))},_clearSelected:function(){this.numSelected=0;t.isNullOrUndefined(this.sfCalendar)||(this.model.highlightSection=="week"?n("td.e-active",this.sfCalendar).removeClass("e-active").addClass("e-state-hover").attr("aria-selected",!1).parent().removeClass("e-selected-week"):this.model.highlightSection=="month"?n("td.e-active",this.sfCalendar).removeClass("e-active").addClass("e-state-hover").attr("aria-selected",!1).parent().parent().removeClass("e-selected-month"):this.model.highlightSection=="workdays"?n("td.e-active",this.sfCalendar).removeClass("e-active").addClass("e-state-hover").attr("aria-selected",!1).parent().removeClass("e-work-week"):n("td.e-active",this.sfCalendar).removeClass("e-active").addClass("e-state-hover").attr("aria-selected",!1))},_addSelected:function(){this.model.highlightSection=="week"?n("td.e-active",this.sfCalendar).parent().addClass("e-selected-week"):this.model.highlightSection=="month"?n("td.e-active, this.sfCalendar").parent().parent().addClass("e-selected-month"):this.model.highlightSection=="workdays"&&n("td.e-active",this.sfCalendar).parent().addClass("e-work-week")},_hideOtherMonths:function(t){n("td.other-month",t).css("visibility","hidden")},_showOtherMonths:function(t){n("td.other-month",t).css({visibility:"visible"})},_otherMonthsVisibility:function(){this.model.showOtherMonths?this._showOtherMonths(this.sfCalendar):this._hideOtherMonths(this.sfCalendar)},_createCalender:function(){t.buildTag("div.e-header").attr(this._isIE8?{unselectable:"on"}:{}).append(t.buildTag("span.e-prev").append(t.buildTag("a.e-icon e-arrow-sans-left").attr({role:"button"}).attr(this._isIE8?{unselectable:"on"}:{}))).append(t.buildTag("span.e-text").append(t.buildTag("span.e-datepicker-headertext").text(this._formatter(this._calendarDate,this.model.headerFormat)).attr({"aria-atomic":"true","aria-live":"assertive",role:"heading"}).attr(this._isIE8?{unselectable:"on"}:{}))).append(t.buildTag("span.e-next").append(t.buildTag("a.e-icon e-arrow-sans-right").attr({role:"button"}).attr(this._isIE8?{unselectable:"on"}:{}))).appendTo(this.sfCalendar);this._enableHeader(this._showHeader);var i=t.buildTag("table.e-dp-viewdays","",{}).data("e-table","data").attr({role:"grid"}).attr(this._isIE8?{unselectable:"on"}:{});this.sfCalendar.append(i);this._renderCalendar(this);this._startLevel(this.model.startLevel);t.buildTag("div.e-footer").append(t.buildTag("span.e-footer-icon")).append(t.buildTag("span.e-footer-text")).appendTo(this.sfCalendar);n(".e-footer-text",this.sfCalendar).html(this._localizedLabels.buttonText);this._enableFooter(this.model.showFooter)},_enableHeader:function(t){t?n(".e-header",this.sfCalendar).show():n(".e-header",this.sfCalendar).hide()},_enableFooter:function(t){t?n(".e-footer",this.sfCalendar).show():n(".e-footer",this.sfCalendar).hide();this._todayBtnDisable()},_todayBtnDisable:function(){var t=new Date((new Date).getFullYear(),(new Date).getMonth(),(new Date).getDate(),0,0,0);+this.model.minDate<=+t&&+this.model.maxDate>=+t?n(".e-footer",this.sfCalendar).removeClass("e-footer-disable"):n(".e-footer",this.sfCalendar).addClass("e-footer-disable")},_checkArrows:function(n,t){this._preArrowCondition(n,this.model.minDate.getFullYear());this._nextArrowCondition(t,this.model.maxDate.getFullYear())},_checkDateArrows:function(){this._preArrowCondition(this._tempMinDate,this.model.minDate);this._nextArrowCondition(this._tempMaxDate,this.model.maxDate)},_preArrowCondition:function(n,t){n<=t?this.sfCalendar.find(".e-prev").addClass("e-disable").attr({"aria-disabled":!0}):this.sfCalendar.find(".e-prev").removeClass("e-disable").attr({"aria-disabled":!1})},_nextArrowCondition:function(n,t){n>=t?this.sfCalendar.find(".e-next").addClass("e-disable").attr({"aria-disabled":!0}):this.sfCalendar.find(".e-next").removeClass("e-disable").attr({"aria-disabled":!1})},_previousNextHandler:function(t){var r,u,f,e,o,s,i;if(this.model.readOnly||!this.model.enabled||n(t.target).hasClass("e-disable")||n(t.currentTarget).hasClass("e-disable"))return!1;t.preventDefault();this._keyboardInteraction=!1;r=n("table",this.sfCalendar);u=this._navigateFrom(r);f=n(t.target).is("a")?n(t.target.parentNode):n(t.target);e=f.hasClass("e-prev")?!0:!1;this._processNextPrevDate(e);o=n("table",this.sfCalendar);s=o.get(0).className;switch(s){case"e-dp-viewdays":i="month";break;case"e-dp-viewmonths":i="year";break;case"e-dp-viewyears":i="decade";break;case"e-dp-viewallyears":i="century"}this._trigger("navigate",{date:this._dateValue,value:this._formatter(this._dateValue,this.model.dateFormat),navigateTo:i,navigateFrom:u})},_processNextPrevDate:function(t){var c,i,u,l,s,h,f,e,o,r;if((this._DRPdisableFade&&(c=new Date(this.sfCalendar.find("td.current-month").attr("data-date")),this._dateValue=c),t&&this.sfCalendar.find(".e-arrow-sans-left").hasClass("e-disable"))||!t&&this.sfCalendar.find(".e-arrow-sans-right").hasClass("e-disable"))return!1;i=n("table",this.sfCalendar);l=i.get(0).className;switch(l){case"e-dp-viewdays":if(s=this.model.stepMonths,t){if(this._dateValue<=this.model.minDate)return this._flag=!1,!1}else if(this._dateValue>=this.model.maxDate)return this._flag=!1,!1;this._flag=!0;this._addMonths(this._dateValue,t?-s:s);this._clickedDate&&(this._calendarDate=this._clickedDate);this._dateValue=this._dateValue<this.model.minDate?new Date(this.model.minDate.toString()):this._dateValue;this._dateValue=this._dateValue>this.model.maxDate?new Date(this.model.maxDate.toString()):this._dateValue;this._renderCalendar(this,this._dateValue);this._keyboardInteraction&&(this._trigger("navigate",{date:this._dateValue,value:this._formatter(this._dateValue,this.model.dateFormat)}),this._removeCurrentMonthFromHideDate(),this._keyboardInteraction=!1);n(".e-datepicker-headertext",this.sfCalendar).text(this._formatter(this._dateValue,this.model.headerFormat));this._addFocus("day",this._hoverDate);h=this._findFirstLastDay(new Date(this._dateValue.toString()));this._preArrowCondition(h.firstDay,this.model.minDate);this._nextArrowCondition(h.lastDay,this.model.maxDate);break;case"e-dp-viewmonths":if(f=this._dateValue,f.setFullYear(n(".e-datepicker-headertext",this.sfCalendar).text()),t){if(f.getFullYear()<=this.model.minDate.getFullYear())return this._flag=!1,!1}else if(f.getFullYear()>=this.model.maxDate.getFullYear())return this._flag=!1,!1;this._flag=!0;this._addYears(f,t?-1:1);this._renderCalendar(this,f);u=f.getFullYear();n(".e-datepicker-headertext",this.sfCalendar).text(u);n("tbody,tr.e-week-header",i).not(".e-datepicker-months").hide();n(n(i).find(".e-datepicker-months")).show();this._addFocus("month",this._hoverMonth);this._checkArrows(u,u);break;case"e-dp-viewyears":if(e=this._dateValue,e.setFullYear(n(i).find(".e-state-hover").text()),t){if(parseInt(this.popup.find("td.e-year-first:first").text())<=this.model.minDate.getFullYear())return this._flag=!1,!1}else if(parseInt(n("td.e-year-last:first").prev().text())>=this.model.maxDate.getFullYear())return this._flag=!1,!1;this._flag=!0;n(i).find(".e-state-hover").hasClass("e-year-first")&&t||n(i).find(".e-state-hover").hasClass("e-year-last")&&!t?this._dateValue.setFullYear(e.getFullYear()):n(i).find(".e-state-hover").hasClass("e-year-first")&&!t?this._dateValue.setFullYear(e.getFullYear()+11):n(i).find(".e-state-hover").hasClass("e-year-last")&&t?this._dateValue.setFullYear(e.getFullYear()-11):this._dateValue.setFullYear(e.getFullYear()+(t?-10:10));this._renderCalendar(this,this._dateValue);r=parseInt(this._dateValue.getFullYear())-(parseInt(this._dateValue.getFullYear())%10+1);n(".e-datepicker-headertext",this.sfCalendar).text(r+1+" - "+(r+10));n("tbody,tr.e-week-header",i).not(".e-datepicker-years").hide();n(n(i).find(".e-datepicker-years")).show();this._addFocus("year",this._hoverYear+(n(".e-year-first.e-hidedate").length?-1:0));this._checkArrows(r+1,r+10);break;case"e-dp-viewallyears":if(t){if(o=parseFloat(n("td.e-allyear-first",i.get(0)).text().split("-")[1]),o<=this.model.minDate.getFullYear())return this._flag=!1,!1;this._flag=!0}else{if(o=parseFloat(n("td.e-allyear-last",i.get(0)).prev().text().split("-")[1]),o>=this.model.maxDate.getFullYear())return this._flag=!1,!1;this._flag=!0}this._dateValue.setFullYear((this._lastHoveredYear?this._lastHoveredYear:this._dateValue.getFullYear())+(t?-100:100));this._lastHoveredYear=this._dateValue.getFullYear();this._renderCalendar(this,this._dateValue);r=parseInt(this._dateValue.getFullYear())-(parseInt(this._dateValue.getFullYear())%100+1);u=parseFloat(n("td.e-allyear-last",i.get(0)).prev().text().split("-")[1]);n(".e-datepicker-headertext",this.sfCalendar).text(r+1+" - "+u);n("tbody,tr.e-week-header",i).not(".e-datepicker-allyears").hide();n(n(i).find(".e-datepicker-allyears")).show();this._addFocus("allyear",this._hoverAllYear+(n(".e-allyear-first.e-hidedate").length?-1:0));this._checkArrows(r+1,u)}this._layoutChanged()},_addFocus:function(t,i){var f="e-current-"+t,u,r;return t=="day"&&(f="current-month"),u=this.sfCalendar.find("tbody tr td."+f),t=="month"&&n(u).each(function(t,r){if(parseInt(n(r).attr("data-index"))==parseInt(i)){i=t;return}}),r=u[i],r||(r=u.last()),this.sfCalendar.find("table td").removeClass("e-state-hover"),n(r).hasClass("e-hidedate")||n(r).addClass("e-state-hover"),this._setActiveState(t),i},_setActiveState:function(t){var e,f,o;if(this.model.value instanceof Date){var i=this.sfCalendar.find("tbody tr td.e-current-"+t),r,s=this,u=-1;switch(t){case"month":this.model.value.getFullYear()===parseInt(n(".e-text",this.sfCalendar).text())&&n(i).each(function(t,i){if(parseInt(n(i).attr("data-index"))==parseInt(s.model.value.getMonth())){u=t;return}});break;case"year":e=this.model.value.getFullYear();n(i).each(function(n,t){if(parseInt(t.innerHTML)==parseInt(e)){u=n;return}});break;case"allyear":f=parseInt(this.model.value.getFullYear())-(parseInt(this.model.value.getFullYear())%10+1);o=f+1+" - "+(f+10);n(i).each(function(n,t){if(parseInt(t.innerHTML)==parseInt(o)){u=n;return}})}r=i[u];r&&(this.sfCalendar.find("table td").removeClass("e-active"),n(r).hasClass("e-hidedate")||n(r).addClass("e-active"))}},_setFocusByName:function(t,i){var u=this.sfCalendar.find("tbody tr td.e-current-"+t),f,r;return n(u).each(function(n,t){if(parseInt(t.innerHTML)==parseInt(i)){f=n;return}}),r=u[f],r||(r=u.last()),this.sfCalendar.find("table td").removeClass("e-state-hover"),n(r).addClass("e-state-hover"),this._setActiveState(t),f},_getHeaderTxt:function(){return this.sfCalendar.find(".e-datepicker-headertext").text()},_findFirstLastDay:function(n){var t=n.getFullYear(),i=n.getMonth(),r=new Date(t,i,1),u=new Date(t,i+1,0);return{firstDay:r,lastDay:u}},_forwardNavHandler:function(t){if(this.model.readOnly||!this.model.enabled)return!1;t&&t.preventDefault();var u=n("table",this.sfCalendar),e=n("table",this.sfCalendar).get(0).className,o=this,i,r,f=this._navigateFrom(u);switch(e){case"e-dp-viewdays":this._hoverMonth=this._getDateObj(u.find(".e-state-hover")).getMonth()||this._getDateObj(u.find(".e-active")).getMonth()||0;this._DRPdisableFade&&(this._renderCalendar(this,this._calendarDate),n(".e-datepicker-headertext",this.sfCalendar).text(this._formatter(this._dateValue,this.model.headerFormat)));this._startLevel("year");r="year";this._addFocus("month",this._hoverMonth);break;case"e-dp-viewmonths":i=this._getHeaderTxt();this._startLevel("decade");r="decade";this._hoverYear=this._setFocusByName("year",i);break;case"e-dp-viewyears":i=this._getHeaderTxt();this._startLevel("century");r="century";this._hoverAllYear=this._setFocusByName("allyear",i)}f!="century"&&this._trigger("navigate",{date:this._dateValue,value:this._formatter(this._dateValue,this.model.dateFormat),navigateTo:r,navigateFrom:f});this._layoutChanged()},_cellSelection:function(){var u=n("table",this.sfCalendar),r=n("table",this.sfCalendar).get(0).className,t,i;switch(r){case"e-dp-viewmonths":this._hoverMonth=this._addFocus("month",this._dateValue.getMonth());break;case"e-dp-viewyears":t=new Date(this._dateValue.toString());this._navigationToPrevNext("year");this._dateValue=t;this._hoverYear=this._setFocusByName("year",this._dateValue.getFullYear());break;case"e-dp-viewallyears":t=new Date(this._dateValue.toString());this._navigationToPrevNext("allyear");this._dateValue=t;i=parseInt(this._dateValue.getFullYear())-(parseInt(this._dateValue.getFullYear())%10+1);this._hoverAllYear=this._setFocusByName("allyear",i+1+" - "+i+10)}this._layoutChanged()},_navigationToPrevNext:function(t){var r=this.sfCalendar.find("tbody tr td.e-current-"+t),u,i,f=this._dateValue.getFullYear();n(r).each(function(n,t){if(parseInt(t.innerHTML)==parseInt(f)){u=n;return}});i=r[u];i&&(n(i).hasClass("e-"+t+"-last")?this._processNextPrevDate(!1):n(i).hasClass("e-"+t+"-first")&&this._processNextPrevDate(!0))},_navigateFrom:function(n){var i=n.get(0).className,t;switch(i){case"e-dp-viewdays":t="month";break;case"e-dp-viewmonths":t="year";break;case"e-dp-viewyears":t="decade";break;case"e-dp-viewallyears":t="century"}return t},_backwardNavHandler:function(i){var u,r;if(this._animating=!0,this.model.readOnly||!this.model.enabled)return!1;i.type?(i.preventDefault(),u=n(i.currentTarget)):u=i;var f=n("table",this.sfCalendar),o,c=n("table",this.sfCalendar).get(0).className,e=this,s,h=this._navigateFrom(f);switch(c){case"e-dp-viewmonths":f.removeClass("e-dp-viewmonths").addClass("e-dp-viewdays");this._lastHoveredMonth=parseInt(n(u).attr("data-index"));this._dateValue=new Date(this._dateValue.getFullYear(),this._lastHoveredMonth,1);this._DRPdisableFade&&this._trigger("_month_Loaded",{currentTarget:i.currentTarget});this._renderCalendar(this,this._dateValue);n("tbody",f).not(".e-datepicker-days,.e-week-header").hide();n(n(f).find(".e-datepicker-days,.e-week-header")).fadeIn("fast",function(){e._addFocus("day",e._hoverDate||0);e._animating=!1});n(".e-datepicker-headertext",this.sfCalendar).text(this._formatter(this._dateValue,this.model.headerFormat));s="month";break;case"e-dp-viewyears":f.removeClass("e-dp-viewyears").addClass("e-dp-viewmonths");this._lastHoveredYear=parseInt(u.text());this._dateValue.setFullYear(this._lastHoveredYear);this._renderCalendar(this,this._dateValue);n("tbody,tr.e-week-header",f).not(".e-datepicker-months").hide();t.isNullOrUndefined(this._hoverMonth)&&!t.isNullOrUndefined(this._dateValue)&&(this._hoverMonth=this._dateValue.getMonth());n(n(f).find(".e-datepicker-months")).fadeIn("fast",function(){e._addFocus("month",e._hoverMonth||0);e._animating=!1});o=u.text();n(".e-datepicker-headertext",this.sfCalendar).text(o);this._checkArrows(o,o);s="year";break;case"e-dp-viewallyears":r=u.text().split("-");f.removeClass("e-dp-viewallyears").addClass("e-dp-viewyears");r[0]<this.model.minDate.getFullYear()?r[0]=this.model.minDate.getFullYear().toString():r[0]>this.model.maxDate.getFullYear()&&(r[0]=this.model.maxDate.getFullYear().toString());this._renderCalendar(this,new Date(r[0],0,1));n("tbody,tr.e-week-header",f).not(".e-datepicker-years").hide();n(n(f).find(".e-datepicker-years")).fadeIn("fast",function(){e._addFocus("year",e._hoverYear||0);e._animating=!1});n(".e-datepicker-headertext",this.sfCalendar).text(r[0]+" - "+r[1]);this._checkArrows(r[0],r[1]);s="decade";this._dateValue=new Date(this._dateValue.setFullYear(parseInt(n.trim(r[0]))+(this._lastHoveredYear?this._lastHoveredYear%10:this._dateValue.getFullYear()%10)));break;default:this._clearSelected();this.sfCalendar.find("table td").removeClass("e-state-hover");u.not("td.e-hidedate").addClass("e-active").attr("aria-selected",!0);this._addSelected();this._hoverDate=this._getDateObj(u).getDate()-1;this._dateValue=new Date(u.attr("data-date"));this._clickedDate=new Date(u.attr("data-date"));this._animating=!1}h!="month"&&this._trigger("navigate",{date:this._dateValue,value:this._formatter(this._dateValue,this.model.dateFormat),navigateTo:s,navigateFrom:h});this._layoutChanged()},_startLevel:function(t){var i=n("table",this.sfCalendar),o=n(".e-datepicker-headertext",this.sfCalendar),r,f,u=this._dateValue,s,e;switch(t){case"decade":i.removeClass("e-dp-viewallyears e-dp-viewmonths e-dp-viewdays").addClass("e-dp-viewyears");n("tbody,tr.e-week-header",i).not(".e-datepicker-years").hide();n(n(i).find(".e-datepicker-years")).show();u=this.model.enableStrictMode&&this._calendarDate<this._dateValue?this._calendarDate:u;e=parseInt(u.getFullYear())-(parseInt(u.getFullYear())%10+1);r=e+1;f=e+10;o.text(r+" - "+f);this._checkArrows(r,f);this._hoverYear=this._setFocusByName("year",u.getFullYear());break;case"century":this._calendarDate<this._dateValue||this._renderCalendar(this,u);i.removeClass("e-dp-viewyears e-dp-viewdays e-dp-viewmonths").addClass("e-dp-viewallyears");n("tbody,tr.e-week-header",i).not(".e-datepicker-allyears").hide();n(n(i).find(".e-datepicker-allyears")).show();r=parseFloat(n("td.e-allyear-first",i.get(0)).text().split("-")[1])+1;f=parseFloat(n("td.e-allyear-last",i.get(0)).prev().text().split("-")[1]);s=r+" - "+f;o.text(s);this._checkArrows(r,f);e=parseInt(u.getFullYear())-(parseInt(u.getFullYear())%10+1);this._hoverAllYear=this._setFocusByName("allyear",e+1+" - "+(e+10));break;case"year":i.removeClass("e-dp-viewyears e-dp-viewallyears e-dp-viewdays").addClass("e-dp-viewmonths");n("tbody,tr.e-week-header",i).hide();n(n(i).find(".e-datepicker-months")).show();r=this.model.enableStrictMode&&this._calendarDate<this._dateValue?this._calendarDate.getFullYear():u.getFullYear();o.text(r);this._checkArrows(r,r);this._hoverMonth=u.getMonth();this._addFocus("month",this._hoverMonth);break;case"month":i.removeClass("e-dp-viewyears e-dp-viewallyears e-dp-viewmonths").addClass("e-dp-viewdays ")}},_depthLevel:function(t){var i=this.sfCalendar;switch(t){case"year":n(i.find(".e-current-year,.e-current-allyear")).on("click",n.proxy(this._backwardNavHandler,this));this._on(n(".e-current-month",this.sfCalendar),"click",n.proxy(this._onDepthSelectHandler,this));break;case"decade":n(i.find(".e-current-allyear")).on("click",n.proxy(this._backwardNavHandler,this));n(".e-current-year",this.sfCalendar).on("click",n.proxy(this._onDepthSelectHandler,this));break;case"century":n(i.find(".e-current-allyear")).on("click",n.proxy(this._onDepthSelectHandler,this));break;case"month":this._on(i.find(".current-month,.other-month,.e-current-month,.e-current-year,.e-current-allyear"),"click",n.proxy(this._backwardNavHandler,this));this._on(i.find(".current-month , .other-month"),"click",n.proxy(this._onSetCancelDateHandler,this))}},_onDepthSelectHandler:function(t){if(this.model.readOnly||!this.model.enabled)return!1;n(t.target).hasClass("e-current-month")?this._dateValue=new Date(this._dateValue.setMonth(parseInt(t.target.attributes["data-index"].value))):n(t.target).hasClass("e-current-year")?this._dateValue=new Date(this._dateValue.setFullYear(parseInt(t.target.innerHTML))):n(t.target).hasClass("e-current-allyear")&&(this._dateValue=new Date(this._dateValue.setFullYear(parseInt(t.target.innerHTML))));this._onSetCancelDateHandler(t)},_datepickerMonths:function(t,i,r){for(var e,o,f,s,h=function(n){return document.createElement(n)},u=0,c=0;c<3;c++){for(e=n(h("tr")),o=0;o<4;o++)f=n(h("td")).addClass("e-current-month e-state-default").attr({"data-index":u,"data-month":this.Date.monthNames[u]+" "+r.getFullYear(),id:r.getFullYear()+""+(u+1)}).attr(this._isIE8?{unselectable:"on"}:{}).html(this.Date.abbrMonthNames[u++]),r.getFullYear()<this.model.minDate.getFullYear()||r.getFullYear()>this.model.maxDate.getFullYear()?(f.addClass("e-hidedate"),f.removeClass("e-current-month")):(r.getFullYear()<=this.model.minDate.getFullYear()&&u<this.model.minDate.getMonth()+1||r.getFullYear()>=this.model.maxDate.getFullYear()&&u>this.model.maxDate.getMonth()+1)&&(f.addClass("e-hidedate"),f.removeClass("e-current-month")),e.append(f);t.append(e)}i.append(t);s=r.getFullYear();this._checkArrows(s,s)},_datepickerYears:function(t,i,r){for(var e,s,h,u,c=function(n){return document.createElement(n)},l=parseInt(r)-(parseInt(r)%10+1),f=[],o=0;o<12;o++)f.push(l+o);for(e=0,s=0;s<3;s++){for(h=n(c("tr")),o=0;o<4;o++)u=n(c("td")),u.attr(this._isIE8?{unselectable:"on"}:{}),e==0?u.addClass("e-year-first e-current-year "):e==11?u.addClass("e-year-last e-current-year "):u.addClass("e-current-year e-state-default"),(f[e]<this.model.minDate.getFullYear()||f[e]>this.model.maxDate.getFullYear())&&(u.addClass("e-hidedate"),u.removeClass("e-current-year")),u.attr({"data-year":f[e],id:f[e]}),u.html(f[e++]),h.append(u);t.append(h)}i.append(t);this._checkArrows(f[0],f[f.length])},_datepickerAllYears:function(t,i,r){for(var f,h,c,u,o=parseInt(r)-(parseInt(r)%100+10),a=o,e=[],l=this._isIE8||this._isIE9?"":"\n",s=0;s<12;s++)e.push(parseInt(o)+" -"+l+parseInt(o+9)),o=o+10;for(f=0,h=0;h<3;h++){for(c=n(document.createElement("tr")),s=0;s<4;s++)u=n(document.createElement("td")),u.attr(this._isIE8?{unselectable:"on"}:{}),f==0?u.addClass("e-allyear-first e-current-allyear "):f==11?u.addClass("e-allyear-last e-current-allyear "):u.addClass("e-current-allyear e-state-default"),(parseInt(e[f].split("-\n")[1])<this.model.minDate.getFullYear()||parseInt(e[f].split("-\n")[0])>this.model.maxDate.getFullYear())&&(u.addClass("e-hidedate"),u.removeClass("e-current-allyear")),u.attr({"data-decade":e[f].replaceAll("-","to"),id:e[f].replaceAll(" ","")}),u.html(e[f++]),c.append(u);t.append(c)}i.append(t)},_renderHeader:function(i){var a=n(document.createElement("thead")),h=t.preferredCulture(this.model.locale).calendars.standard.days,f,e,c,s,u,r,o,l;if(i.model.dayHeaderFormat!="none")for(f=t.buildTag("tr.e-week-header").attr({role:"row"}).attr(this._isIE8?{unselectable:"on"}:{}),this.model.weekNumber==!0&&(e=t.preferredCulture(this.model.locale).calendars.standard.week,r=e.name,o=i.model.dayHeaderFormat=="short"?e.nameAbbr:i.model.dayHeaderFormat=="long"?week:e.nameShort,c=t.buildTag("th","",{},{scope:"col",abbr:r,"data-date":r,title:this._formatter(r,"dddd")}).attr(this._isIE8?{unselectable:"on"}:{}).html(o),f.append(c)),s=this.Date.firstDayOfWeek;s<this.Date.firstDayOfWeek+7;s++)u=s%7,r=h.names[u],o=i.model.dayHeaderFormat=="short"?h.namesAbbr[u]:i.model.dayHeaderFormat=="long"?r:h.namesShort[u],l=t.buildTag("th","",{},{scope:"col",abbr:r,"data-date":r,title:this._formatter(r,"dddd"),"class":u==0||u==6?"e-week-end":"e-week-day"}).attr(this._isIE8?{unselectable:"on"}:{}).html(o),f.append(l);return a.append(f)},_renderCalendar:function(r,u){var d=this,h,s,e,nt,tt,it,c,v,b,k;r=n.extend({},t.DatePicker.prototype.defaults,r);this.Date.firstDayOfWeek=this.model.startDay;h=u?u:this._calendarDate?this._calendarDate:d._zeroTime(new Date);s=n("table",this.sfCalendar);s.empty();s.append(this._renderHeader(r));e=t.buildTag("tbody.e-datepicker-allyears","",{display:"none"}).attr(this._isIE8?{unselectable:"on"}:{});this._datepickerAllYears(e,s,h.getFullYear());e=t.buildTag("tbody.e-datepicker-years","",{display:"none"}).attr(this._isIE8?{unselectable:"on"}:{});this._datepickerYears(e,s,h.getFullYear());var g=r.model.month==i?h.getMonth():r.model.month,ut=r.model.year||h.getFullYear(),f=new Date(ut,g,1,0,0,0),a=this.Date.firstDayOfWeek-f.getDay()+1;a>1&&(a-=7);nt=Math.ceil((-1*a+1+this._getDaysInMonth(f))/7);this._addDays(f,a-1);tt=d._zeroTime(new Date);it=this._calendarDate;e=t.buildTag("tbody.e-datepicker-months","",{display:"none"}).attr(this._isIE8?{unselectable:"on"}:{});this._datepickerMonths(e,s,h);e=t.buildTag("tbody.e-datepicker-days","",{display:"none"}).attr(this._isIE8?{unselectable:"on"}:{});for(var ft=0,rt=!0,w=!0;ft++<nt;){for(c=jQuery(document.createElement("tr")).attr({role:"row"}),this.model.weekNumber==!0&&(v=this._weekDate(f),v=n(document.createElement("td")).attr({}).addClass("e-weeknumber").html(v),c.append(v)),b=0;b<7;b++){var y=f.getMonth()==g,p=this._isSpecialDates(f),et=this._checkDisableRange(f),l=this._getIndex,o=n(document.createElement("td")).html(p?"<span><\/span>"+f.getDate():f.getDate()+"").attr({"data-date":f.toDateString(),title:this.model.showTooltip?p&&this.model.specialDates[l][this._mapField._tooltip]?this.model.specialDates[l][this._mapField._tooltip]:this._formatter(f,this.model.tooltipFormat):"","aria-selected":!1,role:"gridcell",id:this._formatter(f,"yyyyddMM")}).attr(this._isIE8?{unselectable:"on"}:{}).addClass((y?"current-month e-state-default ":"other-month e-state-default ")+(this._isWeekend(f)?this._ejHLWeekEnd?"e-dp-weekend e-week-end ":this.model.highlightWeekend?"e-week-end ":"":"e-week-day ")+(y&&f.getTime()==tt.getTime()?"today e-state-hover":""));o.find("span:first-of-type").addClass(p?this.model.specialDates[l][this._mapField._icon]?"e-special-date-icon "+this.model.specialDates[l][this._mapField._icon]+" ":"e-special-day":"");o.addClass(p?this.model.specialDates[l][this._mapField._custom]?this.model.specialDates[l][this._mapField._custom]:"":"");et&&this._disableDates({date:f,element:o});it.getTime()==f.getTime()&&y&&(o.hasClass("e-hidedate")||(this.model.value?(o.addClass("e-active").attr({"aria-selected":!0}),this.model.highlightSection=="week"&&c.addClass("e-selected-week"),this.model.highlightSection=="month"&&e.addClass("e-selected-month"),this.model.highlightSection=="workdays"&&c.addClass("e-work-week")):this.model.value!=null&&o.addClass("e-state-hover").attr({"aria-selected":!1})),this._hoverDate||(o.hasClass("e-hidedate")||o.addClass("e-state-hover"),this._hoverDate=f.getDate()-1));k=!0;(f<this.model.minDate||f>this.model.maxDate)&&(o.addClass("e-hidedate"),o.removeClass("current-month"),this.model.showOtherMonths&&o.removeClass("other-month"),k=w=!1);y&&(k&&rt&&(this._tempMinDate=f,rt=!1,w=!0),w&&(this._tempMaxDate=f));this._trigger("beforeDateCreate",{date:f,value:this._formatter(f,this.model.dateFormat),element:o});c.append(o);f=new Date(f.getFullYear(),f.getMonth(),f.getDate()+1,0,0,0)}e.append(c)}s.append(e);this._DRPdisableFade?(n(e).css("display","block"),n(e).css({display:"table-row-group","vertical-align":"middle","border-color":"inherit"})):this._isIE8||this._isIE7?n(e).css("display","table-row-group"):n(e).fadeIn("fast");this.model.startLevel===this.model.depthLevel?this._depthLevel(this.model.depthLevel):this.model.depthLevel!="month"&&this.model.depthLevel!=""?this.model.startLevel=="century"?this._depthLevel(this.model.depthLevel):this.model.startLevel=="decade"&&this.model.depthLevel!="century"?this._depthLevel(this.model.depthLevel):this.model.startLevel=="year"&&this.model.depthLevel!="decade"&&this.model.depthLevel!="century"?this._depthLevel(this.model.depthLevel):(this._on(s.find(".current-month,.other-month,.e-current-month,.e-current-year,.e-current-allyear"),"click",n.proxy(this._backwardNavHandler,this)),this._on(s.find(".current-month , .other-month"),"click",n.proxy(this._onSetCancelDateHandler,this))):(this._on(s.find(".current-month,.other-month,.e-current-month,.e-current-year,.e-current-allyear"),"click",n.proxy(this._backwardNavHandler,this)),this._on(s.find(".current-month , .other-month"),"click",n.proxy(this._onSetCancelDateHandler,this)));this._otherMonthsVisibility();this._checkDateArrows()},_checkDisableRange:function(n){return!t.isNullOrUndefined(this._disableCollection[n.getFullYear()])&&jQuery.inArray(n.getMonth(),this._disableCollection[n.getFullYear()])!==-1?!0:!1},_initDisableObj:function(){var i,r,n,u;for(this._disableCollection={},i=0;i<this.model.blackoutDates.length;i++)r=this._checkInstanceType(this.model.blackoutDates[i]),r&&(n=r.getFullYear(),u=r.getMonth(),t.isNullOrUndefined(this._disableCollection[n])&&(this._disableCollection[n]=[]),jQuery.inArray(u,this._disableCollection[n])==-1&&this._disableCollection[n].push(u))},_disableDates:function(n){for(var i,t=0;t<this.model.blackoutDates.length;t++)i=this._checkInstanceType(this.model.blackoutDates[t]),i&&+n.date==+i&&n.element.addClass("e-hidedate")},_keyboardNavigation:function(n){var t;if(this._keyboardInteraction=!0,this._animating)return!1;if(this._isOpen&&(n.keyCode==37||n.keyCode==38||n.keyCode==39||n.keyCode==40||n.keyCode==13||n.keyCode==36||n.keyCode==35)){if(n.preventDefault&&n.preventDefault(),n.altKey)return n.keyCode==13?(this._setCurrDate(n),!1):void 0;t={row:null,col:null};t.col=this.sfCalendar.find("tbody tr td.e-state-hover").index();t.row=this.sfCalendar.find("tbody tr td.e-state-hover").parent().index();t.col=t.col!=-1?t.col+1:this.sfCalendar.find("tbody tr td.e-active").index()+1;t.row=t.row!=-1?t.row+1:this.sfCalendar.find("tbody tr td.e-active").parent().index()+1;var f=this.sfCalendar.find("table")[0].className,i,r=3,u=4;switch(f){case"e-dp-viewallyears":i=this._changeRowCol(t,n.keyCode,r,u,"yearall",n.ctrlKey);n.ctrlKey||(this._hoverAllYear=this.sfCalendar.find("tbody.e-datepicker-allyears tr td").index(i));break;case"e-dp-viewyears":i=this._changeRowCol(t,n.keyCode,r,u,"year",n.ctrlKey);n.ctrlKey||(this._hoverYear=this.sfCalendar.find("tbody.e-datepicker-years tr td").index(i));break;case"e-dp-viewmonths":i=this._changeRowCol(t,n.keyCode,r,u,"month",n.ctrlKey);n.ctrlKey||(this._hoverMonth=this.sfCalendar.find("tbody.e-datepicker-months tr td").index(i));break;case"e-dp-viewdays":r=this.sfCalendar.find("tbody.e-datepicker-days tr").length;u=7;i=this._changeRowCol(t,n.keyCode,r,u,"day",n.ctrlKey);n.ctrlKey||(this._hoverDate=this._getDateObj(i).getDate()-1)}n.ctrlKey||(this.sfCalendar.find("table td").removeClass("e-state-hover"),i.addClass("e-state-hover"),this._setAriaAttributes(i))}else if(this.model.displayInline||n.keyCode!=27&&n.keyCode!=9){if(n.altKey&&n.keyCode==40)return this.show(),!1}else this.hide()},_setAriaAttributes:function(t){if(this._popupOpen){this.sfCalendar.find("[aria-selected=true]").attr("aria-selected",!1);this.sfCalendar.find("[aria-label]").removeAttr("aria-label");n(this.element).attr("aria-activedescendant",t.attr("id"));this._trigger("_setAriaAttribute",t);n(t).attr("aria-selected",!0);var i=this._getViewLabel(t);n(t).attr("aria-label","The current focused "+i[1]+" is "+i[0])}},_getViewLabel:function(n){return n.attr("data-decade")?[n.attr("data-decade"),"decade"]:n.attr("data-year")?[this._formatter(this._getDateObj(n),"yyyy"),"year"]:n.attr("data-month")?[this._formatter(this._getDateObj(n),"MMMM, yyyy"),"month"]:[this._formatter(this._getDateObj(n),"dddd, dd MMMM, yyyy"),"date"]},_changeRowCol:function(t,i,r,u,f,e){var h,s={parent:null,child:null},o,v,l,c,a;switch(f){case"day":h="tbody.e-datepicker-days tr td.current-month";s.parent=".e-datepicker-days";s.child=".current-month";break;case"month":h="tbody.e-datepicker-months tr td.e-current-month";s.parent=".e-datepicker-months";s.child=".e-current-month";break;case"year":h="tbody.e-datepicker-years tr td.e-current-year";s.parent=".e-datepicker-years";s.child=".e-current-year";break;case"yearall":h="tbody.e-datepicker-allyears tr td.e-current-allyear";s.parent=".e-datepicker-allyears";s.child=".e-current-allyear"}if(t.row<=0&&t.col<=0)return this._removeCurrentMonthFromHideDate(),this.sfCalendar.find(h+":first");v=this;switch(i){case 36:return this.sfCalendar.find(h+":first");case 35:return this.sfCalendar.find(h+":last");case 38:if(e&&this.model.allowDrillDown)this._forwardNavHandler();else if(t.row>1)t.row-=1;else return this._processNextPrevDate(!0),this.sfCalendar.find(h+":nth-child("+t.col+"):last");if(o=this._getCell(t,s),o.length<=1){if(o=this._findVisible(t,s,"up"),o!==null)return o;this._processNextPrevDate(!0);o=this.sfCalendar.find(h+":nth-child("+t.col+"):last")}return o;case 37:if(e)return this._processNextPrevDate(!0),this.sfCalendar.find("tbody tr td.e-state-hover");if(t.col>1)t.col-=1;else if(t.row>1)t={row:t.row-1,col:u};else return this._processNextPrevDate(!0),this.sfCalendar.find(h+":last");if(o=this._getCell(t,s),o.length<=1){if(o=this._findVisible(t,s,"left"),o!==null)return o;this._processNextPrevDate(!0);o=this.sfCalendar.find(h+":last")}return o;case 39:if(e)return this._processNextPrevDate(!1),this.sfCalendar.find("tbody tr td.e-state-hover");if(t.col<u)t.col+=1;else if(t.row<r)t={row:t.row+1,col:1};else return this._processNextPrevDate(!1),this.sfCalendar.find(h+":first");if(o=this._getCell(t,s),o.length<=1){if(o=this._findVisible(t,s,"right"),o!==null)return o;this._processNextPrevDate(!1);o=this.sfCalendar.find(h+":first")}return o;case 40:if(!e){if(t.row<r)t.row+=1;else return this._processNextPrevDate(!1),this.sfCalendar.find(h+":nth-child("+t.col+"):first");if(o=this._getCell(t,s),o.length<=1){if(o=this._findVisible(t,s,"down"),o!==null)return o;this._processNextPrevDate(!1);o=this.sfCalendar.find(h+":nth-child("+t.col+"):first")}return o}case 13:l=n("table",this.sfCalendar).get(0).className;c=this._getCell(t,s);a=n(c)[0];l=="e-dp-viewmonths"&&this.model.startLevel=="year"&&this.model.depthLevel=="year"?(this._dateValue=new Date(this._dateValue.setMonth(parseInt(a.attributes["data-index"].value))),this._onSetCancelDateHandler({type:null,target:c})):l=="e-dp-viewyears"&&this.model.startLevel=="decade"&&this.model.depthLevel=="decade"||l=="e-dp-viewallyears"&&this.model.startLevel=="century"&&this.model.depthLevel=="century"?(this._dateValue=new Date(this._dateValue.setFullYear(parseInt(a.innerHTML))),this._onSetCancelDateHandler({type:null,target:c})):l=="e-dp-viewdays"?(this._backwardNavHandler(c),this._onSetCancelDateHandler({type:null,target:c})):this._backwardNavHandler(c)}return this._getCell(t,s)},_findVisible:function(n,t,i){for(var f,u=n.col,r=n.row,o=t.child.slice(1,t.child.length),e=0;e>=0;e++){if(f=this.sfCalendar.find("tbody"+t.parent+" tr:nth-child("+r+") td:nth-child("+u+")"),f.length<=0)return null;if(f.hasClass("e-hidedate")||!f.is(":visible")){if((i=="right"||i=="left"?i=="right"?u++:u--:i=="down"?r++:r--,r<=0||r>this.sfCalendar.find("tbody"+t.parent+" tr").length)||(u>this.sfCalendar.find("tbody"+t.parent+" tr:nth-child("+r+") td").length&&(r++,u=1),u<=0&&(r--,u=this.sfCalendar.find("tbody"+t.parent+" tr:nth-child("+r+") td").length),r<=0||r>this.sfCalendar.find("tbody"+t.parent+" tr").length))return null}else{if(f.hasClass("other-month"))return null;if(f.hasClass(o))return n.col=u,n.row=r,f}}},_getCell:function(n,t){return this.sfCalendar.find("tbody"+t.parent+" tr:nth-child("+n.row+") td"+t.child+":nth-child("+n.col+")")},_getDateObj:function(n){return n.attr("data-year")?new Date(n.attr("data-year")):n.attr("data-month")?new Date(n.attr("data-month")):new Date(n.attr("data-date"))},_touchCalendar:function(n){var t=this.sfCalendar.find("table")[0].className;switch(n.type){case"pinchin":t!="e-dp-viewdays"&&this._keyboardNavigation({keyCode:13});break;case"pinchout":t!="e-dp-viewallyears"&&this.model.allowDrillDown&&this._forwardNavHandler();break;case"swipeleft":this._processNextPrevDate(!1);break;case"swiperight":this._processNextPrevDate(!0)}},show:function(i){var r,u;if(t.isNullOrUndefined(this.sfCalendar)&&this._renderPopup(),this._isOpen)return!1;if(r=this,this._popupOpen=!0,n(this.element).attr("aria-expanded",!0),u=this._preValue!=null?new Date(this._preValue.toString()):this._preValue,this.model.enabled){if(this.model.displayInline||this._setDatePickerPosition(),this._trigger("beforeOpen",{element:this.sfCalendar,events:i}))return!1;this.sfCalendar.attr({"aria-hidden":"false"});r._isOpen=!0;this.sfCalendar.slideDown(this.model.enableAnimation?this.animation.open.duration:0,function(){if(r.model&&!r.model.displayInline)n(document).on("mousedown",n.proxy(r._onDocumentClick,r))});this._isIE8?this.element.val()&&this._compareDate(new Date(this.element.val()),u)&&this._updateInputVal():this._updateInputVal();this._refreshLevel(u);this._trigger("open",{prevDate:u,date:this.model.value,value:this._formatter(this.model.value,this.model.dateFormat)});n(window).on("resize",n.proxy(this._OnWindowResize,this));this.model.displayInline||(this._on(t.getScrollableParents(this.wrapper),"scroll",this.hide),this._on(t.getScrollableParents(this.wrapper),"touchmove",this.hide));this._isInputBox&&this.wrapper.addClass("e-active");this.model.value!=null?n(this.element).attr("aria-activedescendant",n(this.sfCalendar.find(".e-active")).attr("id")):n(this.element).attr("aria-activedescendant",n(this.sfCalendar.find(".today")).attr("id"))}},hide:function(r){if(!this._isOpen||this._getInternalEvents||this._trigger("beforeClose",{element:this.sfCalendar,events:r}))return!1;var u=this;(this._popupOpen=!1,n(this.element).attr("aria-expanded",!1),this.sfCalendar.attr({"aria-hidden":"true"}),this._popClose&&r!=i&&r.type!="click")||(this.sfCalendar.slideUp(this.model.enableAnimation?this.animation.close.duration:0,function(){u._isOpen=!1;n(document).off("mousedown",n.proxy(u._onDocumentClick,u));u._setWaterMark()}),this.element.val()!=""&&this._validateInputVal(),this._trigger("close",{prevDate:this._prevDate,date:this.model.value,value:this._formatter(this.model.value,this.model.dateFormat)}),n(window).off("resize",n.proxy(this._OnWindowResize,this)),this._off(t.getScrollableParents(this.wrapper),"scroll",this.hide),this._off(t.getScrollableParents(this.wrapper),"touchmove",this.hide),this._isInputBox&&this.wrapper.removeClass("e-active"),this.model.value?n(this.element).attr("aria-activedescendant",n(this.sfCalendar.find(".e-active")).attr("id")):n(this.element).removeAttr("aria-activedescendant"))},enable:function(){this.model.enabled=!0;this.wrapper&&this.wrapper.removeClass("e-disable");this.element.removeClass("e-disable").attr({"aria-disabled":!1});this.element.prop("disabled",!1);this.dateIcon&&this.dateIcon.removeClass("e-disable").attr({"aria-disabled":!1});this._isIE8&&this.dateIcon&&this.dateIcon.children().removeClass("e-disable");this.element.prop("disabled",!1);this._isSupport||this._hiddenInput.prop("disabled",!1);this.sfCalendar&&this.sfCalendar.removeClass("e-disable").attr({"aria-disabled":!1})},disable:function(){this.model.enabled=!1;this.wrapper&&this.wrapper.addClass("e-disable");this.element.addClass("e-disable").attr({"aria-disabled":!0});this.element.attr("disabled","disabled");this.dateIcon&&this.dateIcon.addClass("e-disable").attr({"aria-disabled":!0});this._isIE8&&this.dateIcon&&this.dateIcon.children().addClass("e-disable");this.element.attr("disabled","disabled");this._isSupport||this._hiddenInput.attr("disabled","disabled");this.sfCalendar&&this.sfCalendar.addClass("e-disable").attr({"aria-disabled":!0});this._isOpen&&(this.element.is(":input")&&this.element.blur(),this.model.displayInline||this.hide())},getValue:function(){return this._formatter(this.model.value,this.model.dateFormat)},_wireCalendarEvents:function(){if(this._allowQuickPick(this.model.allowDrillDown),this._on(n(".e-next",this.sfCalendar),"click",n.proxy(this._previousNextHandler,this)),this._on(n(".e-prev",this.sfCalendar),"click",n.proxy(this._previousNextHandler,this)),!this.model.displayInline){this.sfCalendar.on("mouseenter touchstart",n.proxy(function(){this._popClose=!0},this));this.sfCalendar.on("mouseleave touchend",n.proxy(function(){this._popClose=!1},this))}this.model.showFooter&&this._on(n(".e-footer",this.sfCalendar),"click",this._setCurrDate);this.sfCalendar&&this._on(this.sfCalendar,"pinchin pinchout swipeleft swiperight",n.proxy(this._touchCalendar,this))},_wireEvents:function(){if(this.element.is(":input")&&this.model.allowEdit&&(this._on(this.element,"blur",this._onFocusOut),this._on(this.element,"focus",this._onFocusIn),this._on(this.element,"keydown",this._onKeyDown)),!this.model.allowEdit){this.element.attr("readonly","readonly");this.element.on("mousedown",n.proxy(this._showDatePopUp,this))}},_bindDateButton:function(){this._on(this.dateIcon,"mousedown",this._showDatePopUp);this.model.allowEdit&&this.element.off("mousedown",n.proxy(this._showDatePopUp,this))},_bindInputEvent:function(){this._off(this.dateIcon,"mousedown",this._showDatePopUp)},_specificFormat:function(){var i=t.globalize._getDateParseRegExp(t.globalize.findCulture(this.model.locale).calendar,this.model.dateFormat);return n.inArray("dddd",i.groups)>-1||n.inArray("ddd",i.groups)>-1},_onFocusOut:function(i){var f,r,u,e;if(this._isFocused=!1,f=this._preValue!=null?new Date(this._preValue.toString()):this._preValue,this.model.enableStrictMode&&this.element.val()&&!isNaN(+new Date(this.element.val())))for(this._formatArray=this.model.dateFormat.split(this._getSeparator()),this._valArray=this.element.val().split(this._getSeparator()),r=0;r<this._formatArray.length;r++)this._formatArray[r].startsWith("y")&&this._valArray.length>1&&this._formatArray[r].length==4&&this._valArray[r].length==2&&(this._valArray[r]=(parseInt(this._valArray[r])+2e3).toString(),this.element.val(this._valArray.join(this._getSeparator())));this._validateOnFocusOut(this._validateValue(),i);this.wrapper.removeClass("e-focus");t.isNullOrUndefined(this.model.value)?this.wrapper.removeClass("e-valid"):this.wrapper.addClass("e-valid");this._isOpen&&!this.model.displayInline||this._setWaterMark()||this._compareDate(this._preValue,this._parseDate(this.element.val(),this.model.dateFormat))||this._updateInputVal();(!this._isOpen||this.model.displayInline)&&this._refreshLevel(f);this.element.val()!=""&&(!this._isOpen||this.model.displayInline)&&this._validateInputVal();this.element.off("keydown",n.proxy(this._keyboardNavigation,this));this.model.showPopupButton||this._off(this.element,"click",this._elementClick);u=this.element.val();e={prevDate:this._prevDate,value:u};this._specificFormat()?this._prevDate!=u&&this._setDateValue(u,!0):this._setDateValue(u);this.model.value||this._clearSelected();this._trigger("focusOut",e);this._checkErrorClass()},_onFocusIn:function(t){if(this._isSupport&&(t.preventDefault(),this._isFocused=!0),this.wrapper.removeClass("e-error"),this.isValidState=!0,this.wrapper.addClass("e-focus"),this.wrapper.addClass("e-valid"),!this.model.readOnly){this._isSupport||this._hiddenInput.css("display","none");this.element.on("keydown",n.proxy(this._keyboardNavigation,this));this.model.showPopupButton||this.model.readOnly||this.show(t);this.model.showPopupButton||this._on(this.element,"click",this._elementClick);this._trigger("focusIn",{date:this.model.value,value:this._formatter(this.model.value,this.model.dateFormat)})}},_elementClick:function(n){this._popupOpen||this.show(n)},_removeWatermark:function(){this.element.val()==""||this._isSupport||this._hiddenInput.css("display","none")},_refreshPopup:function(){this._refreshDatepicker();this._startLevel(this.model.startLevel)},_weekDate:function(n){var i,t=new Date(n.getTime());return t.setDate(t.getDate()+4-(t.getDay()||7)),i=t.getTime(),t.setMonth(0),t.setDate(1),Math.floor(Math.round((i-t)/864e5)/7)+1},_refreshLevel:function(n){if(this.model.startLevel==this.model.depthLevel&&this.model.startLevel!="month"){var t=this._stringToObject(this.element.val());t=this._validateYearValue(t);t&&(this._compareDate(n,t)||this._refreshPopup())}},_validateOnFocusOut:function(n,i){var f=this._preValue!=null?this._calendarDate:this._preValue,e=this._formatter(f,this.model.dateFormat),o,s,h,r,u;this._prevDate=this._formatter(this._preValue,this.model.dateFormat);o=e;s={prevDate:this._prevDate,value:o,isInteraction:!!i};this._specificFormat()&&n>this.model.minDate&&n<this.model.maxDate?n==null?this.model.value=f:(this.model.value=n,h=this._formatter(n,this.model.dateFormat,this.model.locale)):h=this._formatter(this._parseDate(this._formatter(new Date,"MM/dd/yyyy")),this.model.dateFormat);r=!1;u=!1;n==null||this.model.enableStrictMode?n!=null||this.model.enableStrictMode?n?(n<this.model.minDate||n>this.model.maxDate?(this.isValidState=!1,u=!0,this._calendarDate=n<this.model.minDate?this.model.minDate:this.model.maxDate):this.isValidState=!0,this._triggerChangeEvent(i),u&&this._getInternalEvents&&this._trigger("outOfRange")):(this.isValidState=!1,this._calendarDate<this.model.minDate?this._calendarDate=this.model.minDate:this._calendarDate>this.model.maxDate&&(this._calendarDate=this.model.maxDate)):(this._preTxtValue==null||this.element.val()==""?(this.element.val(""),this._isSupport||this._hiddenInput.css("display","block")):this.element.val(e),this._triggerChangeEvent(i),this.model.value!=null&&this._trigger("change",s)):(t.isNullOrUndefined(this.model.value)&&(this.model.value=this._parseDate(this.element.val())),this.model.maxDate<this.model.minDate&&(this.model.minDate=this.model.maxDate),this.model.enableStrictMode||(n?(n<this.model.minDate||n>this.model.maxDate)&&(r=!0,this._calendarDate=n=n<this.model.minDate?this.model.minDate:this.model.maxDate):(this.element.val(""),this._calendarDate<this.model.minDate?this._calendarDate=this.model.minDate:this._calendarDate>this.model.maxDate&&(this._calendarDate=this.model.maxDate)),this.isValidState=!0),r&&this.element.val(this._formatter(n,this.model.dateFormat)),this._compareDate(this._preValue,this._parseDate(this.element.val(),!0))||this._triggerChangeEvent(i))},_onKeyDown:function(n){if(n.keyCode===13){var t=this._preValue!=null?new Date(this._preValue.toString()):this._preValue;this._validateOnFocusOut(this._validateValue(),n);this._isOpen&&!this.model.displayInline||this._setWaterMark()||this._compareDate(this._preValue,this._parseDate(this.element.val(),this.model.dateFormat))||this._updateInputVal();(!this._isOpen||this.model.displayInline)&&this._refreshLevel(t);this.element.val()!=""&&(!this._isOpen||this.model.displayInline)&&this._validateInputVal();this._checkErrorClass()}},_showhidePopup:function(n){if(!this.model.enabled)return!1;this._isOpen?(this._isFocused||!this.element.is(":input")||t.isTouchDevice()||this.element.focus(),this._cancelValue||this.hide(n)):(this._isFocused||!this.element.is(":input")||t.isTouchDevice()||this.element.focus(),this.show(n))},_compareDate:function(n,t){return+n==+t?!0:!1},_validateDate:function(n){var i=!0,t,r;if(n!=null){for(t=0;t<this.model.blackoutDates.length;t++)r=this._checkInstanceType(this.model.blackoutDates[t]),r&&+n==+r&&(i=!1);(n<this.model.minDate||n>this.model.maxDate)&&this.model.enableStrictMode&&(i=!1,this.isValidState=!1)}return i},_triggerChangeEvent:function(n){var r,u=this.element.val()==""?null:this.element.val(),t;this._prevDate=this._formatter(this._preValue,this.model.dateFormat);t={prevDate:this._prevDate,value:u,isInteraction:!!n};r=this._specificFormat()&&n!=i&&n.type=="keydown"&&this._formatter(this._preValue,this.model.dateFormat,this.model.locale)!=this.element.val()?this._parseDate(this.element.val(),!0):this._specificFormat()&&n!=i&&n.type=="blur"?this.model.value:this._parseDate(u);r=this._validateYearValue(r);this._validateDate(r)||(r=null);this._compareDate(this._preValue,r)?this.element.val()==""&&this._prevDate==null||this.element.val()==this._prevDate||(t.value=this.element.val(),this._trigger("_change",t)):(this._preValue=this.model.value=r,t.value=this._formatter(this.model.value,this.model.dateFormat),this.model.value&&(this._clickedDate=this._calendarDate=this.model.value),this.model.displayInline&&!this._isInputBox&&this._hiddenInput.attr("value",u),this.model.value||this.model.enableStrictMode||this._setDateValue(this.model.value),t.value=u,this._trigger("_change",t),t.value=this._formatter(this.model.value,this.model.dateFormat),this._trigger("change",t),this._checkErrorClass())},_triggerSelectEvent:function(){var i=this.element.val(),t;this._parseDate(i)&&(t={prevDate:this._prevDate,date:this.model.value,value:i,isSpecialDay:this._isSpecialDates(this.model.value)},this._prevDate!=i&&this._parseDate(t.value)&&this.model.value>=this.model.minDate&&this.model.value<=this.model.maxDate&&(this._cancelValue=this._trigger("select",t)),this._dt_drilldown&&this._trigger("dt_drilldown",t));this.model.value&&n(this.element).attr("aria-activedescendant",n(this.sfCalendar.find(".e-active")).attr("id"))},_onDocumentClick:function(t){this.model&&(n(t.target).is(this.popup)||n(t.target).parents(".e-popup").is(this.popup)||n(t.target).is(this.wrapper)||n(t.target).parents(".e-datewidget").is(this.wrapper)?(n(t.target).is(this.popup)||n(t.target).parents(".e-popup").is(this.popup))&&t.preventDefault():this.hide(t))},_OnWindowResize:function(){this.sfCalendar&&this._setDatePickerPosition()},_showDatePopUp:function(n){var t=!1;if((n.button?t=n.button==2:n.which&&(t=n.which==3),!t)&&(this._isSupport||this.model.showPopupButton||(n.preventDefault(),this._onFocusIn()),!this.model.readOnly)){if(n.preventDefault(),!this.model.enabled&&this.model.displayInline)return!1;this._showhidePopup(n)}},_layoutChanged:function(){this._getInternalEvents&&this._trigger("layoutChange")},_setCurrDate:function(n){if(this.model.readOnly||!this.model.enabled)return!1;n&&n.preventDefault();var t=this;this._prevDate=this._formatter(this.model.value,this.model.dateFormat);this._dateValue=this._zeroTime(new Date);this.model.value=this._calendarDate=new Date(this._dateValue.toString());this._setDateValue(this.model.value);this._triggerSelectEvent(n);this._triggerChangeEvent(n);this._refreshDatepicker();this._changeDayClass();this._startLevel(this.model.startLevel);this._onSetCancelDateHandler(n);this._layoutChanged()},_changeDayClass:function(){var n=this.popup.children("table")[0].className;n!="e-dp-viewdays"&&this.popup.children("table").removeClass(n).addClass("e-dp-viewdays")},_onSetCancelDateHandler:function(t){if(this.model.readOnly||!this.model.enabled||t&&(n(t.target).hasClass("e-disable")||n(t.target).hasClass("e-hidedate")))return!1;t&&t.type&&t.preventDefault();this._specificFormat()?this._prevDate=this.element.val():this.model.value=this._parseDate(this.element.val());this._prevDate=this._formatter(this.model.value,this.model.dateFormat);this._setDateValue(this._dateValue);this._triggerSelectEvent(t);this._triggerChangeEvent(t);this._dateValue=this.model.value==null?null:new Date(this.model.value.toString());this.element.is(":input")&&!this.model.displayInline&&this._showhidePopup(t);t&&n(t.currentTarget).hasClass("other-month")&&this._refreshDatepicker();this._cellSelection()},_closeCalendar:function(n){n&&n!=this.element||this.sfCalendar.empty().remove()},_checkErrorClass:function(){this.wrapper&&(this.isValidState?this.wrapper.removeClass("e-error"):this.wrapper.addClass("e-error"))},_getLocalizedLabels:function(){return t.getLocalizedConstants(this.sfType,this.model.locale)}});t.DatePicker.Locale=t.DatePicker.Locale||{};t.DatePicker.Locale["default"]=t.DatePicker.Locale["en-US"]={watermarkText:"Select date",buttonText:"Today"};t.DatePicker.Header={None:"none",Short:"short",Min:"min",Long:"long"};t.DatePicker.HighlightSection={Month:"month",Week:"week",WorkDays:"workdays",None:"none"};t.DatePicker.Level={Month:"month",Year:"year",Decade:"decade",Century:"century"}})(jQuery,Syncfusion)});
