/*!
*  filename: ej.ribbon.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./ej.dropdownlist.min","./ej.button.min","./ej.tab.min","./ej.splitbutton.min","./ej.menu.min","./../common/ej.scroller.min","./ej.checkbox.min","./ej.togglebutton.min","./ej.waitingpopup.min"],n):n()})(function(){(function(n,t,i){t.widget("ejR<PERSON><PERSON>","ej.Ribbon",{element:null,validTags:["div"],model:null,_rootCSS:"e-ribbon",_requiresID:!0,defaults:{width:null,cssClass:"",buttonDefaults:{width:null,height:null,enableRTL:!1,showRoundedCorner:!1,enabled:!0,cssClass:null},expandPinSettings:{toolTip:null,customToolTip:{title:null,content:null,prefixIcon:null}},collapsePinSettings:{toolTip:null,customToolTip:{title:null,content:null,prefixIcon:null}},applicationTab:{type:"menu",backstageSettings:{text:null,height:null,width:null,headerWidth:null,pages:[{id:null,text:null,itemType:"tab",contentID:null,enableSeparator:!1}]},menuItemID:null,menuSettings:{}},tabs:[{id:null,text:null,groups:[{id:null,text:null,type:null,contentID:null,customContent:null,alignType:"rows",enableGroupExpander:!1,groupExpanderSettings:{toolTip:null,customToolTip:{title:null,content:null,prefixIcon:null}},content:[{groups:[{id:null,text:null,toolTip:null,quickAccessMode:"none",isMobileOnly:"false",customToolTip:{title:null,content:null,prefixIcon:null},columns:null,itemHeight:null,itemWidth:null,expandedColumns:null,type:"button",galleryItems:[{text:null,toolTip:null,customToolTip:{},buttonSettings:{}}],customGalleryItems:[{text:null,toolTip:null,customToolTip:{},customItemType:"button",buttonSettings:{},menuId:"",menuSettings:{}}],contentID:null,enableSeparator:!1,isBig:!1,cssClass:null,buttonSettings:{},splitButtonSettings:{},toggleButtonSettings:{},dropdownSettings:{}}],defaults:{}}]}]}],contextualTabs:[{backgroundColor:null,borderColor:null,tabs:[]}],tabSelect:null,tabClick:null,tabAdd:null,tabRemove:null,load:null,beforeTabRemove:null,tabCreate:null,create:null,groupClick:null,beforeTabClick:null,galleryItemClick:null,backstageItemClick:null,collapse:null,expand:null,pinState:null,toggleButtonClick:null,groupExpand:null,qatMenuItemClick:null,selectedItemIndex:1,enabledItemIndex:[],disabledItemIndex:[0],allowResizing:!1,isResponsive:!1,showQAT:!1,showBelowQAT:!1,enableOnDemand:!1,collapsible:!1,locale:"en-US",enableRTL:!1,destory:null,_destroyed:null},dataTypes:{tabs:"array",contextualTabs:"array",disabledItemIndex:"data",enabledItemIndex:"data",selectedItemIndex:"number",applicationTab:{backstageSettings:{pages:"array"}}},_destroy:function(){var i,r,u,f,e,o,s,t;for(f=this._applicationTab.find(".e-menu"),f.length>0&&f.data("ejMenu").destroy(),this.element.find(".e-rbncustomelement,.e-backstagetabarea,.e-gallerymenu").hide(),i=this.element.find(".e-gallerymenu"),e=i.length,t=0;t<e;t++)i.eq(t).data("ejMenu").destroy(),i.eq(t).remove();for(this.element.find(".e-rbncustomelement,.e-backstagetabarea,.e-gallerymenu").appendTo(this.element.parent()),r=n(".e-rbn-ddl.e-ddl").find(".e-dropdownlist"),o=r.length,t=0;t<o;t++)r.eq(t).data("ejDropDownList").destroy(),r.siblings("ul").appendTo(this.element.parent());for(u=n(".e-rbn-splitbtn.e-splitbutton"),s=u.length,t=0;t<s;t++)u.eq(t).data("ejSplitButton").destroy(),u.siblings("ul").appendTo(this.element.parent());this.element.children().remove();this._cloneElement.attr("role")||this.element.removeAttr("role");this.element.removeClass("e-tab e-widget");this.element.hasClass("e-ribwithout-apptab")&&this.element.removeClass("e-ribwithout-apptab");this.model.enableOnDemand&&this.element.ejWaitingPopup("destroy");this._trigger("_destroyed")},_tags:[{tag:"applicationTab.backstageSettings.pages",attr:["id","text","itemType","contentID","enableSeparator"]},{tag:"tabs",attr:["id","text",[{tag:"groups",attr:["id","text","alignType","type","contentID","enableGroupExpander",[{tag:"content",attr:["defaults.type","defaults.width","defaults.height","defaults.isBig",[{tag:"groups",attr:["id","columns","itemHeight","itemWidth","expandedColumns","contentID","text","type","toolTip","isMobileOnly","quickAccessMode","width","enableSeparator","buttonSettings.contentType","buttonSettings.type","buttonSettings.imagePosition","buttonSettings.prefixIcon","buttonSettings.click","buttonSettings.width","buttonSettings.cssClass","buttonSettings.enabled","buttonSettings.showRoundedCorner","buttonSettings.enableRTL","buttonSettings.repeatButton","buttonSettings.size","buttonSettings.height","buttonSettings.text","buttonSettings.timeInterval","buttonSettings.create","buttonSettings.click","buttonSettings.destroy","splitButtonSettings.contentType","splitButtonSettings.prefixIcon","splitButtonSettings.targetID","splitButtonSettings.buttonMode","splitButtonSettings.arrowPosition","splitButtonSettings.type","splitButtonSettings.click","splitButtonSettings.enabled","splitButtonSettings.showRoundedCorner","splitButtonSettings.enableRTL","splitButtonSettings.size","splitButtonSettings.height","splitButtonSettings.imagePosition","splitButtonSettings.width","splitButtonSettings.text","splitButtonSettings.suffixIcon","splitButtonSettings.create","splitButtonSettings.itemMouseOver","splitButtonSettings.itemSelected","splitButtonSettings.beforeOpen","splitButtonSettings.destroy","splitButtonSettings.cssClass","dropdownSettings.dataSource","dropdownSettings.text","dropdownSettings.value","dropdownSettings.select","dropdownSettings.width","dropdownSettings.showRoundedCorner","dropdownSettings.showPopupOnLoad","dropdownSettings.allowMultiSelection","dropdownSettings.enableRTL","dropdownSettings.enabled","dropdownSettings.caseSensitiveSearch","dropdownSettings.showCheckbox","dropdownSettings.checkAll","dropdownSettings.uncheckAll","dropdownSettings.enablePersistence","dropdownSettings.enableIncrementalSearch","dropdownSettings.readOnly","dropdownSettings.allowGrouping","dropdownSettings.fields","dropdownSettings.selectedItems","dropdownSettings.itemsCount","dropdownSettings.height","dropdownSettings.cssClass","dropdownSettings.itemValue","dropdownSettings.popupHeight","dropdownSettings.targetID","dropdownSettings.waterMarkText","dropdownSettings.template","dropdownSettings.cascadeTo","dropdownSettings.query","dropdownSettings.create","dropdownSettings.popupHide","dropdownSettings.popupShown","dropdownSettings.beforePopupShown","dropdownSettings.change","dropdownSettings.checkChange","dropdownSettings.enableAnimation","dropdownSettings.destroy","toggleButtonSettings.contentType","toggleButtonSettings.defaultText","toggleButtonSettings.activeText","toggleButtonSettings.type","toggleButtonSettings.defaultPrefixIcon","toggleButtonSettings.activePrefixIcon","toggleButtonSettings.click","toggleButtonSettings.enabled","toggleButtonSettings.showRoundedCorner","toggleButtonSettings.enableRTL","toggleButtonSettings.enablePersistence","toggleButtonSettings.toggleState","toggleButtonSettings.preventToggle","toggleButtonSettings.size","toggleButtonSettings.imagePosition","toggleButtonSettings.height","toggleButtonSettings.width","toggleButtonSettings.cssClass","toggleButtonSettings.defaultText","toggleButtonSettings.activeText","toggleButtonSettings.defaultPrefixIcon","toggleButtonSettings.defaultSuffixIcon","toggleButtonSettings.activePrefixIcon","toggleButtonSettings.create","toggleButtonSettings.change","toggleButtonSettings.destroy","customToolTip.title","customToolTip.content","customToolTip.prefixIcon",[{tag:"galleryItems",attr:["text","toolTip","customToolTip.title","customToolTip.content","customToolTip.prefixIcon","buttonSettings.contentType","buttonSettings.type","buttonSettings.imagePosition","buttonSettings.prefixIcon","buttonSettings.click","buttonSettings.width","buttonSettings.cssClass","buttonSettings.enabled","buttonSettings.showRoundedCorner","buttonSettings.enableRTL","buttonSettings.repeatButton","buttonSettings.size","buttonSettings.height","buttonSettings.text","buttonSettings.timeInterval","buttonSettings.create","buttonSettings.click","buttonSettings.destroy"]},{tag:"customGalleryItems",attr:["text","toolTip","customToolTip.title","customToolTip.content","customToolTip.prefixIcon","menuId","customItemType","buttonSettings.contentType","buttonSettings.type","buttonSettings.imagePosition","buttonSettings.prefixIcon","buttonSettings.click","buttonSettings.width","buttonSettings.cssClass","buttonSettings.enabled","buttonSettings.showRoundedCorner","buttonSettings.enableRTL","buttonSettings.repeatButton","buttonSettings.size","buttonSettings.height","buttonSettings.text","buttonSettings.timeInterval","buttonSettings.create","buttonSettings.click","buttonSettings.destroy","menuSettings.openOnClick","menuSettings.enableCenterAlign","menuSettings.showRooltLevelArrows","menuSettings.showSubLevelArrows","menuSettings.enableSeparator","menuSettings.enabled","menuSettings.orientation","menuSettings.menuType","menuSettings.animationType","menuSettings.subMenuDirection","menuSettings.fields","menuSettings.cssClass","menuSettings.contextMenuTarget","menuSettings.excludeTarget","menuSettings.height","menuSettings.width","menuSettings.titleText","menuSettings.create","menuSettings.beforeOpen","menuSettings.open","menuSettings.close","menuSettings.mouseover","menuSettings.mouseout","menuSettings.keydown","menuSettings.destroy"]}]]}]]}]]}]]},{tag:"contextualTabs",attr:["backgroundColor","borderColor",[{tag:"tabs",attr:["id","text",[{tag:"groups",attr:["id","text","alignType","type","contentID",[{tag:"content",attr:["defaults.type","defaults.width","defaults.height","defaults.isBig",[{tag:"groups",attr:["id","columns","itemHeight","itemWidth","expandedColumns","contentID","text","type","toolTip","width","enableSeparator","buttonSettings.contentType","buttonSettings.type","buttonSettings.imagePosition","buttonSettings.prefixIcon","buttonSettings.click","buttonSettings.width","buttonSettings.cssClass","buttonSettings.enabled","buttonSettings.showRoundedCorner","buttonSettings.enableRTL","buttonSettings.repeatButton","buttonSettings.size","buttonSettings.height","buttonSettings.text","buttonSettings.timeInterval","buttonSettings.create","buttonSettings.click","buttonSettings.destroy","splitButtonSettings.contentType","splitButtonSettings.prefixIcon","splitButtonSettings.targetID","splitButtonSettings.buttonMode","splitButtonSettings.arrowPosition","splitButtonSettings.type","splitButtonSettings.click","splitButtonSettings.enabled","splitButtonSettings.showRoundedCorner","splitButtonSettings.enableRTL","splitButtonSettings.size","splitButtonSettings.height","splitButtonSettings.imagePosition","splitButtonSettings.width","splitButtonSettings.text","splitButtonSettings.suffixIcon","splitButtonSettings.create","splitButtonSettings.itemMouseOver","splitButtonSettings.itemSelected","splitButtonSettings.beforeOpen","splitButtonSettings.destroy","splitButtonSettings.cssClass","dropdownSettings.dataSource","dropdownSettings.text","dropdownSettings.value","dropdownSettings.select","dropdownSettings.width","dropdownSettings.showRoundedCorner","dropdownSettings.showPopupOnLoad","dropdownSettings.allowMultiSelection","dropdownSettings.enableRTL","dropdownSettings.enabled","dropdownSettings.caseSensitiveSearch","dropdownSettings.showCheckbox","dropdownSettings.checkAll","dropdownSettings.uncheckAll","dropdownSettings.enablePersistence","dropdownSettings.enableIncrementalSearch","dropdownSettings.readOnly","dropdownSettings.allowGrouping","dropdownSettings.fields","dropdownSettings.selectedItems","dropdownSettings.itemsCount","dropdownSettings.height","dropdownSettings.cssClass","dropdownSettings.itemValue","dropdownSettings.popupHeight","dropdownSettings.targetID","dropdownSettings.waterMarkText","dropdownSettings.template","dropdownSettings.cascadeTo","dropdownSettings.query","dropdownSettings.create","dropdownSettings.popupHide","dropdownSettings.popupShown","dropdownSettings.beforePopupShown","dropdownSettings.change","dropdownSettings.checkChange","dropdownSettings.enableAnimation","dropdownSettings.destroy","toggleButtonSettings.contentType","toggleButtonSettings.defaultText","toggleButtonSettings.activeText","toggleButtonSettings.type","toggleButtonSettings.defaultPrefixIcon","toggleButtonSettings.activePrefixIcon","toggleButtonSettings.click","toggleButtonSettings.enabled","toggleButtonSettings.showRoundedCorner","toggleButtonSettings.enableRTL","toggleButtonSettings.enablePersistence","toggleButtonSettings.toggleState","toggleButtonSettings.preventToggle","toggleButtonSettings.size","toggleButtonSettings.imagePosition","toggleButtonSettings.height","toggleButtonSettings.width","toggleButtonSettings.cssClass","toggleButtonSettings.defaultText","toggleButtonSettings.activeText","toggleButtonSettings.defaultPrefixIcon","toggleButtonSettings.defaultSuffixIcon","toggleButtonSettings.activePrefixIcon","toggleButtonSettings.create","toggleButtonSettings.change","toggleButtonSettings.destroy","customToolTip.title","customToolTip.content","customToolTip.prefixIcon",[{tag:"galleryItems",attr:["text","toolTip","customToolTip.title","customToolTip.content","customToolTip.prefixIcon","buttonSettings.contentType","buttonSettings.type","buttonSettings.imagePosition","buttonSettings.prefixIcon","buttonSettings.click","buttonSettings.width","buttonSettings.cssClass","buttonSettings.enabled","buttonSettings.showRoundedCorner","buttonSettings.enableRTL","buttonSettings.repeatButton","buttonSettings.size","buttonSettings.height","buttonSettings.text","buttonSettings.timeInterval","buttonSettings.create","buttonSettings.click","buttonSettings.destroy"]},{tag:"customGalleryItems",attr:["text","toolTip","customToolTip.title","customToolTip.content","customToolTip.prefixIcon","menuId","customItemType","buttonSettings.contentType","buttonSettings.type","buttonSettings.imagePosition","buttonSettings.prefixIcon","buttonSettings.click","buttonSettings.width","buttonSettings.cssClass","buttonSettings.enabled","buttonSettings.showRoundedCorner","buttonSettings.enableRTL","buttonSettings.repeatButton","buttonSettings.size","buttonSettings.height","buttonSettings.text","buttonSettings.timeInterval","buttonSettings.create","buttonSettings.click","buttonSettings.destroy","menuSettings.openOnClick","menuSettings.enableCenterAlign","menuSettings.showRooltLevelArrows","menuSettings.showSubLevelArrows","menuSettings.enableSeparator","menuSettings.enabled","menuSettings.orientation","menuSettings.menuType","menuSettings.animationType","menuSettings.subMenuDirection","menuSettings.fields","menuSettings.cssClass","menuSettings.contextMenuTarget","menuSettings.excludeTarget","menuSettings.height","menuSettings.width","menuSettings.titleText","menuSettings.create","menuSettings.beforeOpen","menuSettings.open","menuSettings.close","menuSettings.mouseover","menuSettings.mouseout","menuSettings.keydown","menuSettings.destroy"]}]]}]]}]]}]]}]]}],_ribbonRerender:function(){var n=this.model,r=this.element,t,i,u;if(i=this.element.find(".e-content.e-content-item"),this.element.find(".e-collapse-content").length>0)for(u=i.length,t=0;t<u;t++)if(i.eq(t).hasClass("e-active-content")&&i.eq(t).hasClass("e-collapse-content")){this._selectedItemIndex=t;this._isCollapsed=!0;break}this._isCollapsed?(n.selectedItemIndex=this._selectedItemIndex,n._isCollapsed=!0):n._isCollapsed=!1;this.element.data("ejTab")&&(this.element.data("ejTab")._destroy(),this.element=r,this.model=n);this.element.ejRibbon("destroy").ejRibbon(n);this.model=n;this.element=r;this.model._isCollapsed&&this.collapse()},_setModel:function(i){var e,u,a,s,r,y,f,p,o,h,v;for(e in i)switch(e){case"applicationTab":t.isNullOrUndefined(i.applicationTab.backstageSettings)||t.isNullOrUndefined(i.applicationTab.backstageSettings.pages)||(this.model.applicationTab.backstageSettings.pages=i.applicationTab.backstageSettings.pages);this.model.enableOnDemand?this._createAppTabOnDemand():this._createApplicationTab();break;case"selectedItemIndex":this.model.selectedItemIndex>0&&this._tabObj.model.disabledItemIndex.indexOf(this.model.selectedItemIndex)===-1&&(this._ribbonTabs.eq(this.model.selectedItemIndex).is(":visible")||this._responsiveTabText.eq(t.isNullOrUndefined(t.isNullOrUndefined(this.model.applicationTab))?this.model.selectedItemIndex:this.model.selectedItemIndex-1).is(":visible"))&&this._tabObj.option({selectedItemIndex:this.model.selectedItemIndex});break;case"disabledItemIndex":var u,w=this.model.disabledItemIndex.length,c=this.model.disabledItemIndex,b=this.element.find(".e-link"),l;for(this.model.disabledItemIndex.push(0),this._tabObj.option({disabledItemIndex:this.model.disabledItemIndex}),r=0;r<w;r++)u=this._ribbonTabs.eq(c[r]).parent().parent(),(u.hasClass("e-contextli")||u.hasClass("e-contextliset"))&&u.addClass("e-disable"),this._tabObj.model.selectedItemIndex!=0&&this._tabObj.model.selectedItemIndex===c[r]&&(l=b.eq(c[r]).attr("href"),n(l).append(this.element.find("#"+this._id+"_disabled")),n(l).css("position","relative"),this.element.find("#"+this._id+"_disabled").show());break;case"enabledItemIndex":for(a=this.model.enabledItemIndex.length,s=this._tabUl.find(".e-contextualtabset"),this._ribbonTabs=this._ribbonTabs.not("span,.e-ribresmenu,.e-responsiveqat"),r=0;r<a;r++)this.model.enabledItemIndex[r]!==0&&this._ribbonTabs.eq(this.model.enabledItemIndex[r]).is(":visible")||(this.model.enabledItemIndex.splice(r,1),--a,--r);for(this._conTabsRemove(),this._tabObj.option({enabledItemIndex:this.model.enabledItemIndex}),this._contextualTabs(),y=this.model.enabledItemIndex.length,f=0;f<y;f++)this._tabText.eq(this.model.enabledItemIndex[f]).parent().removeClass("e-disable"),this._tabText.eq(this.model.enabledItemIndex[f]).parent().css("position",""),u=this._ribbonTabs.eq(this.model.enabledItemIndex[f]).parent().parent(),(u.hasClass("e-contextli")||u.hasClass("e-contextliset"))&&u.removeClass("e-disable"),this.model.enabledItemIndex[f]==this._tabObj.model.selectedItemIndex&&this.element.find("#"+this._id+"_disabled").hide();for(p=s.length,o=0;o<p;o++)n(s[o]).hasClass("e-disable")&&n(s[o]).parents(".e-contextliset").addClass("e-disable");break;case"allowResizing":this._ribbonWindowResize();break;case"isResponsive":this._ribbonWindowResize();break;case"width":this.element.width(i[e]);this._ribbonWindowResize();break;case"enableRTL":this.model.enableRTL=i[e];h=this.model;v=this.element;this.element.data("ejTab")&&(this.element.data("ejTab")._destroy(),this.element=v,this.model=h);this.element.ejRibbon("destroy");n("#"+this._id).ejRibbon(h);this.model=h;this.element=v;break;case"locale":this.model.locale=i[e];this._ribbonRerender();break;case"cssClass":this._changeSkin(i[e])}},_changeSkin:function(n){this._tabObj.option("cssClass",n)},_setTabContentHeight:function(){var i,f,r,e,u,t;for(this.element.find(".e-ribGroupContent").height("auto"),i=0,f=this.element.find(".e-content.e-content-item"),u=n("#"+this._id).parents(),n(f).addClass("e-content-show"),n("#"+this._id).is(":hidden")&&n("#"+this._id).addClass("e-ribbon-show"),t=0;t<u.length;t++)u.eq(t).is(":hidden")&&u.eq(t).addClass("e-ribbon-show");for(r=this.element.find(".e-separatordivrow"),e=r.length,t=0;t<e;t++)n(r).eq(t).height(n(r).eq(t).prev().height());this.element.find(".e-ribGroupContent").each(function(){i=Math.max(i,n(this).height())});this.element.find(".e-ribGroupContent").height(i);n(f).removeClass("e-content-show");n("#"+this._id).hasClass("e-ribbon-show")&&(this._ribbonResize(),n(".e-ribbon-show").removeClass("e-ribbon-show"))},_init:function(){t.isNullOrUndefined(this.element)||(this._cloneElement=this.element.clone(),this._trigger("load"),this._render(),this._wireEvents())},_render:function(){var i,e,o,u,f;this.model.enableOnDemand&&n.isFunction(n.fn.ejWaitingPopup)&&(this.element.ejWaitingPopup({showOnInit:!1}),n("#"+this._id+"_WaitingPopup").addClass("e-ribbonwaitingpopup"));this.model.disabledItemIndex.length==0&&this.model.disabledItemIndex.push(0);this._renderTab();this.model.showQAT&&(i=t.buildTag("div.e-rbnquickaccessbar").click(n.proxy(this._onQatClick,this)),this.model.showBelowQAT?(i.removeClass("e-rbnabove").addClass("e-rbnbelow"),this.element.append(i).addClass("e-rbnwithqatbelow")):(i.removeClass("e-rbnbelow").addClass("e-rbnabove"),this.element.prepend(i).addClass("e-rbnwithqatabove")),this.element.addClass("e-rbnwithqat"));this._initPrivateProperties();this.model.enableOnDemand?this.model.collapsible?this._createQAT():(this._groupContent(),this.element.find(".e-header").addClass("e-rbnondemand"),this._createQAT()):(this._groupingControls(),this._createQAT());this._initial=!0;this._customization();this._initialRender=!0;this.model.collapsible&&this.collapse();this._ribbonResponsive();this._responsiveTabText=this.element.find(".e-responsivetabheader li");this._phoneMode&&this._ribbonWindowResize();this._tabObj.initialRender=!1;this.element.append(t.buildTag("div#"+this._id+"_disabled"));this.element.append(t.buildTag("div#"+this._id+"_modelDiv"));e=this.element.find(".e-content");this.element.find("#"+this._id+"_disabled").height(this.element.find(".e-active-content").height()).width(this.model.width);this.element.find("#"+this._id+"_disabled").css({top:0,left:0,position:"absolute"}).addClass("e-disable");this.element.find("#"+this._id+"_disabled").hide();this.element.find("#"+this._id+"_modelDiv").height(n(e).height()).width(this.model.width);this.element.find("#"+this._id+"_modelDiv").css({top:0,left:0,position:"absolute"}).addClass("e-modelDiv");this.element.find("#"+this._id+"_modelDiv").hide();o=t.buildTag("div#"+this._id+"_resize").addClass("e-resizediv").hide().click(n.proxy(this._onResizeDivClick,this));this.element.append(o);var r=t.buildTag("div#"+this._id+"_tooltip").addClass("e-tooltipdiv").hide(),s=t.buildTag("div#"+this._id+"_toolTip_title").addClass("e-tooltiptitle"),h=t.buildTag("div#"+this._id+"_toolTip_Desc").addClass("e-tooltipdesc");r.append(s);r.append(h);this.element.append(r);this._applicationTab.hasClass("e-active")&&!t.isNullOrUndefined(this.model.tabs[0].id)&&(this._applicationTab.removeClass("e-active"),this.element.find(".e-content.e-content-item").remove(),this._tabUl.find(".e-expandcollapse").remove());u=this.element.find(".e-empty-content");f=this.element.find(".e-groupdiv").eq(0);n(u).length>0&&n(f).length>0&&n(u).height(n(f).parent().height());this.element.find(".e-rbncustomelement,.e-backstagetabarea,.e-gallerymenu").show()},_createQuickAccessBar:function(){var u,e,r,o,i,s,f;if(this.model.showQAT){for(u=t.buildTag("div.e-splitbtnqatdiv").attr("title",this.localizedLabels.CustomizeQuickAccess),e=t.buildTag("button#"+this._id+"_qatsplitbtn").addClass("e-splitbtnqat e-rbn-button"),u.append(e.ejButton({size:"normal",type:"button",contentType:"imageonly",height:30,width:14,prefixIcon:"e-icon e-ribbon e-qatexpand",click:n.proxy(this._onQatExpandBtnClick,this)})),this._qAccessBar.append(u),r=t.buildTag("ul.e-rbnqatmenu","<div>"+this.localizedLabels.CustomizeQuickAccess+"<\/div>").click(n.proxy(this._onQatMenuClick,this)),o=this._qatControlsName.length,i=0;i<o;i++)s=t.buildTag("li#"+this._qatControlsName[i].id+"_menuli.e-qatmenuli e-removemenuli","<p>"+this._qatControlsName[i].text+"<\/p>").appendTo(r).attr("title",this.localizedLabels.RemoveFromQuickAccessToolbar),this._qatControlsName[i].qAccess=="menu"&&s.addClass("e-addmenuli").removeClass("e-removemenuli").attr("title",this.localizedLabels.AddToQuickAccessToolbar);f="";f=this.model.showBelowQAT?"<li class='e-rbnshowabove e-qatmenuli'>"+this.localizedLabels.ShowAboveTheRibbon+"<\/li>":"<li class='e-rbnshowbelow e-qatmenuli'>"+this.localizedLabels.ShowBelowTheRibbon+"<\/li>";r.append("<li class='e-qatmenuli e-qatseparator'><\/li><li class='e-qatmorecommands e-qatmenuli' title="+this.localizedLabels.CustomizeQuickAccess+">"+this.localizedLabels.MoreCommands+"<\/li>"+f);this._qAccessBar.append(r.hide())}},_tooltip:function(t){var i=this.element.find(".e-controlclicked");(i.length<=0||n(i).find(".e-ribbonbtn").length>0)&&(this.element.find("#"+t.data.ribbonId+"_resize").is(":visible")||this.element.find(".e-gallexpandcontent").is(":visible")?(n(t.target).parents(".e-resizediv").length>0||n(t.target).parents(".e-gallexpandcontent").length>0)&&this._toolTipShow(t):this._toolTipShow(t))},_toolTipShow:function(i){var u=this.element.find("#"+i.data.ribbonId+"_tooltip"),f,e,s,b,o,l,v,k,y,h,p,r,w,a,c;(u.find(".e-tooltiptitle").length<=0&&(k=t.buildTag("div#"+i.data.ribbonId+"_toolTip_title").addClass("e-tooltiptitle"),u.prepend(k)),u.removeClass("e-tooltipdivwithimg"),l=u.find(".e-tooltiptitle"),l.children().remove(),n(i.target).is("li"))||(n(i.target).hasClass("e-rarrowup-2x")&&!t.isNullOrUndefined(this.model.expandPinSettings.toolTip)&&n(n(i.target).parent()).attr("title",this.model.expandPinSettings.toolTip),!n(i.target).hasClass("e-rarrowup-2x")||t.isNullOrUndefined(this.model.expandPinSettings.customToolTip.title)&&t.isNullOrUndefined(this.model.expandPinSettings.customToolTip.content)||(r=i.data.value.expandObj),n(i.target).hasClass("e-ribbonpinicon")&&!t.isNullOrUndefined(this.model.collapsePinSettings.toolTip)?n(n(i.target).parent()).attr("title",this.model.collapsePinSettings.toolTip):!n(i.target).hasClass("e-ribbonpinicon")||t.isNullOrUndefined(this.model.collapsePinSettings.customToolTip.title)&&t.isNullOrUndefined(this.model.collapsePinSettings.customToolTip.content)?n(i.target).hasClass("e-rarrowup-2x")||n(i.target).hasClass("e-ribbonpinicon")||(r=i.data.value):r=i.data.value.collapseObj,t.isNullOrUndefined(r)||t.isNullOrUndefined(r.customToolTip)||t.isNullOrUndefined(r.customToolTip.title)?l.remove():l.append(r.customToolTip.title),t.isNullOrUndefined(r)||t.isNullOrUndefined(r.customToolTip)||t.isNullOrUndefined(r.customToolTip.content)||(l.children().length<=0&&l.empty().append("<b>"+r.customToolTip.title+"<\/b>"),o=u.find(".e-tooltipdesc"),o.children().remove(),o.append(r.customToolTip.content),o.children().length<=0&&o.empty().append("<h6>"+r.customToolTip.content+"<\/h6>"),t.isNullOrUndefined(r)||t.isNullOrUndefined(r.customToolTip.prefixIcon)?o.children().addClass("e-tooltipcontent"):(o.prepend("<span class=e-tooltipimg><\/span>"),o.find(".e-tooltipimg").addClass(r.customToolTip.prefixIcon),o.children().not("."+r.customToolTip.prefixIcon).addClass("e-tooltipcntwithimg"),u.addClass("e-tooltipdivwithimg"))),t.isNullOrUndefined(r))||(e=this.element.find("#"+i.delegateTarget.id).offset(),f=this.element.find("#"+i.delegateTarget.id),u.stop(!0).delay(700).show(0).addClass("e-rbntooltipshadow"),b=this.element.find("#"+i.data.ribbonId+"_resize"),v=n(i.target).parents(".e-gallexpandcontent"),this.element.css({position:"relative"}),y=this.element.width()+this.element.offset().left-(e.left+parseInt(n(u).width(),10)),e.left=y<=0?e.left+y-this.element.offset().left:e.left-this.element.offset().left,p=this.element.find(".e-active-content").eq(0),h=p.is(":visible")?this._tabUl.height()+p.height():this._tabUl.height(),b.is(":visible")&&!v.is(":visible")?(u.offset({left:e.left}),s=f.parents(".e-galleryrow").length>0?h+f.position().top+f.parents(".e-galleryrow").height():h+f.position().top+f.height(),u.css({top:s})):v.is(":visible")?(w=f.parents(".e-galleryrow").length>0?f.parents(".e-galleryrow"):f.parents(".e-galleryexpanderrow").length>0?f.parents(".e-galleryexpanderrow"):f,s=n(v).parents(".e-resizediv").length>0?w.height()+h+f.position().top+12:w.height()+this._tabUl.height()+f.position().top+12,u.offset({left:e.left}),u.css({top:s})):n(i.target).parents(".e-qatooldiv").length>0||n(i.target).hasClass("e-qatooldiv")?(a=this.element.find(".e-qaresizediv"),c=this.element.find(".e-rbnquickaccessbar"),c.hasClass("e-rbnabove")&&a.length>0?a.is(":visible")&&u.offset({top:a.height()+c.height()+4,left:e.left}):c.hasClass("e-rbnabove")?u.offset({top:c.height()+4,left:e.left}):(s=a.is(":visible")?c.height()+a.height()+h+4:h+c.height()+4,u.offset({top:s,left:e.left}))):(s=h+3,u.offset({top:s,left:e.left})))},_toolTipOut:function(n){var t=this.element.find("#"+n.data.ribbonId+"_tooltip");t.hide(0);t.css({top:"",left:""});t.find(".e-tooltiptitle").children().remove();t.find(".e-tooltipdesc").children().remove()},_responsiveScrollRemove:function(){var t=this.element.find(".e-active-content .e-responsiveScroll"),n=this.element.find(".e-rescontentScroll");n.length>0&&(n.data("ejScroller")._destroy(),n.parent().append(n.find("div:first").children()),n.remove());t.length>0&&(t.data("ejScroller")._destroy(),this.element.find(".e-active-content").append(t.find("div:first").children()),t.remove())},_onGroupClick:function(i){var s,f,p,r,w,c,l,a,ft,o,e,v,g,y,nt,tt,it,rt,ut,et,ot;if(this._toolTipCustomization(i),s=n(i.target).parents(".e-resizebtnselect"),this.element.find(".e-resizebtnselect").removeClass("e-resizebtnselect e-active"),f=n(i.target).parents(".e-resizediv"),n(i.target).hasClass("e-resizebtnselect")?n(i.target).removeClass("e-resizebtnselect e-active"):s.length>0?s.removeClass("e-resizebtnselect e-active"):(s=n(i.target).parents(".e-resizebtn"),s.length>0?s.addClass("e-resizebtnselect e-active"):n(i.target).hasClass("e-resizebtn")&&n(i.target).addClass("e-resizebtnselect e-active")),this.model.showQAT&&!n(i.target).parents(".e-mobdiv").length>0&&(n(i.target).hasClass("e-togglebutton e-btn")||n(i.target).parents(".e-togglebutton.e-btn").length>0)&&(r=n(i.target),n(i.target).parents(".e-togglebutton.e-btn").length>0&&(r=n(i.target).parents(".e-togglebutton.e-btn")),p=this._QATclick?r.parent().find("input").attr("id")+"_mobEle":r.parent().find("input").attr("id")+"_qatEle",n(".e-ribbon.e-rbnwithqat").find("#"+p).length>0&&this._rbnToggleBtnCustomization(i,p)),!this._phoneMode)if(f.length<=0&&(n(i.target).hasClass("e-resizebtn")?f=n(i.target):t.isNullOrUndefined(n(i.target).parents(".e-resizebtn"))||(f=n(i.target).parents(".e-resizebtn"))),this.element.find(".e-resizediv").is(":visible")&&this.element.find(".e-resizediv .e-gallexpandcontent").is(":visible"))n(i.target).hasClass("e-gallerymoveicon")||n(i.target).hasClass("e-gallerymovediv")||n(i.target).hasClass("e-expgallerydiv")||n(i.target).hasClass("e-scrollbar")||n(i.target).parents().hasClass("e-scrollbar")?w=i.target:n(i.target).parents().hasClass("e-gallerymenu")&&(n(i.target).hasClass("e-haschild")||n(i.target).hasClass("aschild")||n(i.target).hasClass("e-arrows"))&&(w=i.target),t.isNullOrUndefined(w)&&(this._ribbonGalleryShow(),this.element.find(".e-resizediv").show().css("visibility",""),this.element.find(".e-resizediv .e-gallexpandcontent").hide().css("visibility",""));else if(f.length<=0){if(!n(i.target).parents(".e-content").length>0&&n(i.target).parents(".e-rbn-ddl").length>0||n(i.target).parents(".e-rbn-splitbtn").length>0||n(i.target).parents(".e-presetWrapper").length>0||n(i.target).parents(".e-ribbonpopup").length>0||n(i.target).parents(".e-colorpicker.e-ribbon:visible").length>0)return!1;this._resizeDivHide()}else n(f).hasClass("e-resizebtn")&&(this.element.find(".e-resizediv").is(":visible")?f.parent().siblings(".e-contentbottom").length<=0?this._resizeDivHide():(this._resizeDivHide(),this._resizeDivShow(f)):this._resizeDivShow(f));if(this._phoneMode){var u=this.element.find(".e-active-content"),h=0,b,k,d,r=i.target;if(n(r).hasClass("e-groupresponsive e-ribdownarrow")){for(b=u.find(".e-resptoolbarScroll .e-scrollbar").height(),d=u.children(".e-groupdiv,.e-groupmobdiv"),c=0;c<d.length;c++)h+=d.eq(c).outerHeight();h+=t.isNullOrUndefined(b)?0:b;k=u.find(".e-respmobcontent").length>0?h-u.find(".e-respmobcontent").outerHeight()>this._responsiveHeight:h>this._responsiveHeight;u.height(k?u.find(".e-respmobcontent").length>0?this._responsiveHeight+u.find(".e-respmobcontent").outerHeight():this._responsiveHeight:h).removeClass("e-responsiveheight e-rbnmobheader e-responsiveToolbarScroll");k&&(l=t.buildTag("div#"+this._id+"_responsiveScroller").addClass("e-responsiveScroll").append(t.buildTag("div")),u.find(".e-respmobcontent").length>0?l.find("div").append(u.children().eq(0).siblings()):l.find("div").append(u.children()),u.append(l),u.find(".e-responsiveScroll").ejScroller({height:this._responsiveHeight,scrollerSize:8,buttonSize:0}));n(r).removeClass("e-ribdownarrow").addClass("e-ribuparrow");n(r).parents(".e-groupdiv").find(".e-ribdownarrow").removeClass("e-ribdownarrow").addClass("e-ribuparrow")}else if(n(r).hasClass("e-groupresponsive e-ribuparrow"))u.addClass("e-responsiveheight e-rbnmobheader"),u.find(".e-resptoolbarScroll").length>0&&u.addClass("e-responsiveToolbarScroll"),this._responsiveScrollRemove(),n(r).removeClass("e-ribuparrow").addClass("e-ribdownarrow");else if(this.element.find(".e-responsivecontent").is(":hidden")&&(n(r).hasClass("e-resizebtn")||n(r).parents(".e-resizebtn").length>0||n(r).hasClass("e-groupresponsive e-ribrightarrow")||n(r).hasClass("e-ribrightdivarrow"))){if(n(r).hasClass("e-resizebtn")?(this._responsiveTarget=n(r).parents(".e-groupdiv"),this._responsiveContent=n(r).parent().siblings()):n(r).parents(".e-resizebtn").length>0?(this._responsiveTarget=n(r).parents(".e-resizebtn").parents(".e-groupdiv"),this._responsiveContent=n(r).parents(".e-resizebtn").parent().siblings()):(n(r).hasClass("e-groupresponsive e-ribrightarrow")||n(r).hasClass("e-ribrightdivarrow"))&&(a=n(r).parents(".e-resizegroupdiv").find(".e-resizebtn"),a.length>0&&(this._responsiveTarget=a.parents(".e-groupdiv"),this._responsiveContent=a.parent().siblings())),ft=this._responsiveContent.eq(0).removeClass("e-reshide"),o=this.element.find(".e-content.e-responsivecontent"),o.children().not(".e-responsiveback").remove(),o.append(ft),this.element.find(".e-restopbackcontent").text(this._responsiveTarget.find(".e-resizebtn .e-btntxt").text()),e=o.find(".e-ribbongallery"),e.length>0){for(v=this._responsiveGallery,g=Math.floor(e.find(".e-galleryrow").children().length/v),e.find(".e-galleryrow").children().length%v>0&&++g,y=1;y<=g;y++){for(nt=t.buildTag("div#"+e.attr("id")+"_galleryExpanderRow_"+y).addClass("e-galleryexpanderrow").click({model:e},n.proxy(this._onGalleryItemClick,this)),tt=0;tt<v;tt++)e.find(".e-galleryrow").children().eq(0).appendTo(nt);nt.appendTo(o.parent().find(".e-expandercontent"))}e.hide();o.parent().find(".e-gallexpandcontent").show()}this._responsiveContentShow(o)}}it=n(i.target).hasClass("e-groupdiv")?n(i.target).index():n(i.target).parents(".e-groupdiv").index();it!=-1&&(i.groupItems=this._groupClickEventArgs(it,i));n(i.target).parents(".e-ribbongallerycontrol").length===0&&(this._trigger("groupClick",i),i.cancel||!t.raiseWebFormsServerEvents||t.isNullOrUndefined(this.model.serverEvents)||this.model.serverEvents.indexOf("groupClick")!=-1&&(rt=n.extend(!0,{},this.model),ut=i.target.id?i.target.id:i.target.parentNode.id,et={model:rt,originalEventType:"groupClick",id:ut},ot={model:rt,id:ut},t.raiseWebFormsServerEvents("groupClick",et,ot)))},_groupClickEventArgs:function(i,r){var f=this._tabUl.find(".e-active"),u=f.index(),o,e;return this._phoneMode&&n(r.target).parents(".e-groupdiv").siblings(".e-groupmobdiv").length>0&&(i-=1),t.isNullOrUndefined(this.model.applicationTab)||f.hasClass("e-contextualtabset")||(u=u-1),(u<0)?void 0:(e=f.hasClass("e-contextualtabset")?this.model.contextualTabs[f.parents(".e-contextual").index()].tabs[u]:this.model.tabs[u],t.isNullOrUndefined(e)||(o=e.groups[i]),o)},goToMainContent:function(){var i=this.element.find(".e-responsivebackstage"),n=this.element.find(".e-responsivetabheader"),r={},t=this.element.find(".e-active-content"),f=this.element.find(".e-responsivebackstage"),u=this.element.find(".e-responsivebackstagecontent");n.removeClass("e-resshow");i.is(":visible")&&i.removeClass("e-backstageshowanimate").addClass("e-reshide");u.is(":visible")&&u.hide();this.element.find(".e-responsivecontent").is(":visible")&&this._responsiveContentBack();t.hasClass("e-responsiveheight")||(t.addClass("e-responsiveheight e-rbnmobheader"),this._responsiveScrollRemove(),t.find(".e-groupresponsive.e-ribuparrow").removeClass("e-ribuparrow").addClass("e-ribdownarrow"),r.type="collapse",this._trigger("collapse",r));n.hasClass("e-resshow")&&n.removeClass("e-resshow")},_onResizeDivClick:function(t){var f,r,i,u;this._toolTipCustomization(t);this.model.showQAT&&(n(t.target).hasClass("e-togglebutton e-btn")||n(t.target).parents(".e-togglebutton.e-btn").length>0)&&(r=n(t.target),n(t.target).parents(".e-togglebutton.e-btn").length>0&&(r=n(t.target).parents(".e-togglebutton.e-btn")),f=r.parent().find("input").attr("id")+"_qatEle",this._rbnToggleBtnCustomization(t,f));this._phoneMode&&((n(t.target).hasClass("e-togglebutton e-btn")||n(t.target).parents(".e-togglebutton.e-btn").length>0)&&(r=n(t.target),n(t.target).parents(".e-togglebutton.e-btn").length>0&&(r=n(t.target).parents(".e-togglebutton.e-btn")),f=r.parent().find("input").not(".e-active").attr("id")+"_mobEle",this._rbnToggleBtnCustomization(t,f)),n(t.target).hasClass("e-ribleftarrow")&&this._responsiveContentBack());n(t.currentTarget).find(".e-ribGroupContent").length>0&&(i=n(t.currentTarget).find(".e-ribGroupContent").attr("id").split("_"),u=this.element.find("#"+i[0]+"_"+i[1]+"_"+i[2]).index(),this._phoneMode&&u>0&&this.element.find("#"+i[0]+"_"+i[1]+"_"+i[2]).siblings(".e-groupmobdiv").length>0&&(u-=1),u!=-1&&(t.groupItems=this._groupClickEventArgs(u,t)));t.targetElement="resizedGroup";this._trigger("groupClick",t)},_toolTipCustomization:function(t){var i=n(t.target).parents(".e-controlpadding");n(i).find(".e-ribbongallerycontrol").length<=0&&n(t.target).find(".e-disable").length<=0&&!n(t.target).hasClass("e-disable")&&(this.element.find(".e-tooltipdiv").hide(0),n(i).hasClass("e-controlclicked")?n(i).removeClass("e-controlclicked"):n(i).addClass("e-controlclicked"))},_onTabClick:function(i){var e;this._trigger("beforeTabClick",i);this.model.enableOnDemand&&(n("#"+this._id+"_WaitingPopup").is(":hidden")&&this.element.find(".e-content.e-content-item").eq(i.activeIndex).children().length==0&&this.element.ejWaitingPopup("show"),this._groupContent(i));var l=this.element.find(".e-rarrowup-2x"),o=this._tabUl.find("li.e-active"),f=this.element.find(".e-collapseactive"),s,c,h=this.element.find(".e-active-content").eq(0);if(this._phoneMode){this._responsiveScrollRemove();this.element.find(".e-mobribgroupactive").removeClass("e-mobribgroupactive");var a=i.activeIndex,r=this.element.find(".e-content.e-content-item").eq(a),u=this._tabUl.children(".e-tab").index(i.activeHeader);if(t.isNullOrUndefined(this.model.applicationTab)&&(--i.activeIndex,--i.prevActiveIndex),this.element.find(".e-content.e-content-item").eq(i.prevActiveIndex).removeClass("e-responsiveheight e-rbnmobheader"),this.element.find(".e-content.e-content-item").eq(i.activeIndex).addClass("e-responsiveheight e-rbnmobheader"),this.element.find(".e-responsivecontent").is(":visible")&&this.element.find(".e-responsivecontent").hasClass("e-resshow")&&this.element.find(".e-responsivecontent").removeClass("e-resshow"),t.isNullOrUndefined(this._responsiveContent)||this._responsiveTarget.append(this._responsiveContent).css(this._responsiveContentStyle),u!=-1&&this._mobileContents[u].mobileContent.length>0&&!r.find(".e-respmobcontent").length>0){for(r.prepend(t.buildTag("div#"+this._id+"_mobcontent").addClass("e-respmobcontent e-groupmobdiv").append(t.buildTag("div#"+this._id+"_mobribgroup").addClass("e-mobribgroup e-mobribgroupactive").append(t.buildTag("div.e-ribupdivarrow").addClass("e-toolbaralign").append(t.buildTag("span.e-icon").addClass("e-groupresponsive e-ribdownarrow"))))).click(n.proxy(this._onGroupClick,this)).click(n.proxy(this._onMobContentClick,this)),r.find(".e-resgroupheader").removeClass("e-resgroupheader"),e=0;e<this._mobileToolbar[u].Content.length;e++)r.find(".e-mobribgroup").append(this._mobileToolbar[u].Content[e]);r.find(".e-mobribgroup").append(r.find(".e-mobribgroup .e-ribupdivarrow"))}u!=-1&&this._mobileContents[u].mobileContent.length>0&&!r.find(".e-mobribgroup").hasClass("e-mobribgroupactive")&&r.find(".e-mobribgroup").addClass("e-mobribgroupactive")}o.length>0?(o.removeClass("e-active"),o.children().removeClass("e-active"),h.removeClass("e-active-content").hide()):f.length>0&&(i.activeHeader.textContent===f.find(".e-link").text()?(f.addClass("e-active").removeClass("e-collapseactive"),h.removeClass("e-collapse-content").slideDown("fast","swing",n.proxy(this._ribExpand,this))):(s=this.element.find(".e-link"),f.removeClass("e-collapseactive"),this.element.find(".e-collapse-content").removeClass("e-collapse-content"),h.removeClass("e-active-content"),c=s.eq(i.activeIndex).attr("href"),s.eq(i.activeIndex).addClass("e-active"),n(c).slideDown("fast","swing",n.proxy(this._ribExpand,this))));l.length>0&&this.element.find(".e-collapsed").length>0&&this._addRibbonPin();this._initialRender||this._trigger("tabClick",i);this._clickValue="click"},_onTabSelect:function(i){var u,f,e,o,r,h;if(this.model.enableOnDemand&&(this._initialRender&&n("#"+this._id+"_WaitingPopup").is(":hidden")&&this.element.ejWaitingPopup("show"),this._setTabContentHeight()),u=this.element.find("li.e-active"),r=this.element.find(".e-expandcollapse"),this.model.selectedItemIndex=i.activeIndex,u.hasClass("e-disable")||(f=this.element.find(".e-link"),e=f.eq(i.activeIndex).attr("href"),o=n(e).find("#"+this._id+"_disabled"),o&&this.element.find("#"+this._id+"_disabled").hide()),r.length>0&&r.appendTo(this.element.find(".e-active-content").eq(0)),this._phoneMode||this._ribbonResize(),!this._initialRender&&(this._trigger("tabSelect",i),n(".e-menu-wrap").children(".e-split:visible").length>0&&n(n(".e-menu-wrap").children(".e-split")).css("display","none"),n(".e-rbn-ddl").parent(".e-ddl-popup:visible").length>0&&n(n(".e-rbn-ddl").parent(".e-ddl-popup:visible")).css("display","none"),n(".e-popupWrapper").parent(".e-colorpicker.e-ribbon:visible").length>0&&n(n(".e-popupWrapper").parent(".e-colorpicker.e-ribbon")).css("display","none"),t.isOnWebForms&&t.raiseWebFormsServerEvents&&!t.isNullOrUndefined(this.model.serverEvents)&&this.model.serverEvents.indexOf("tabSelect")!=-1)){var s=n.extend(!0,{},this.model),c={model:s,originalEventType:"tabSelect",activeIndex:i.activeIndex,prevActiveIndex:i.prevActiveIndex},l={model:s,activeIndex:i.activeIndex,prevActiveIndex:i.prevActiveIndex};t.raiseWebFormsServerEvents("tabSelect",c,l)}this._phoneMode&&!this._initialRender&&this._ribbonWindowResize();this._initialRender=!1;h=this;this.model.enableOnDemand&&n("#"+this._id+"_WaitingPopup").is(":visible")&&setTimeout(function(){h.element.ejWaitingPopup("hide")},300)},_create:function(n){this.model.enableOnDemand&&this.element.find(".e-content.e-content-item").addClass("e-rbn-ondemand");this._trigger("tabCreate",n)},_onGalMoveUpClick:function(t){var i=this.element.find("#"+t.data.galleryId),r=i.find(".e-gallerycontent").children(":visible").first().prev();this.element.find(".e-gallexpandcontent:visible").length>0&&this._ribbonGalleryShow();r.length>0&&(i.find(".e-gallerycontent").children(":visible").last().hide(),r.show().width(i.find(".e-galleryrow").first().width()),n(r).hasClass("e-gryfirstrow")&&i.find(".e-moveupdiv").addClass("e-disablegrymovebtn"),i.find(".e-movedowndiv").removeClass("e-disablegrymovebtn"))},_onGalMoveDownClick:function(t){var i=this.element.find("#"+t.data.galleryId),r=i.find(".e-gallerycontent").children(":visible").last().next();this.element.find(".e-gallexpandcontent:visible").length>0&&this._ribbonGalleryShow();r.length>0&&(i.find(".e-gallerycontent").children(":visible").first().hide(),r.show().width(i.find(".e-galleryrow").first().width()),n(r).hasClass("e-grylastrow")&&i.find(".e-movedowndiv").addClass("e-disablegrymovebtn"),i.find(".e-moveupdiv").removeClass("e-disablegrymovebtn"))},_onQatMenuItemClick:function(t){var i={target:n(t.target),cancel:!1,text:n(t.target).text()};this._trigger("qatMenuItemClick",i)},_onQatResizeBtnClick:function(t){var i=this.element.find(".e-qaresizediv"),r=this.element.find(".e-qaresizebtn"),u;r.hasClass("e-active")?r.removeClass("e-tbtn e-active"):r.addClass("e-tbtn e-active");i.is(":hidden")?(n(t.e.currentTarget).position().left+i.width()>this.element.width()?(u=n(t.e.currentTarget).position().left+i.width()-this.element.width(),i.show().css({left:n(t.e.currentTarget).position().left-u})):i.show().css({top:this._qAccessBar.height(),left:n(t.e.currentTarget).position().left}),this._qAccessBar.hasClass("e-rbnbelow")?i.css({top:this.element.height()}):i.css({top:this._qAccessBar.height()})):i.hide().css({top:"",left:""})},_onQatExpandBtnClick:function(t){var f=0,o=this.element.find(".e-splitbtnqat"),e=this.element.find(".e-qaresizediv"),i=this.element.find(".e-rbnqatmenu"),r=n(t.e.currentTarget),u;if(o.hasClass("e-active")){o.removeClass("e-tbtn e-active");i.hide();return}o.addClass("e-tbtn e-active");e.length>0&&e.is(":visible")?(u=e.position().left+r.position().left+i.width(),u>this.element.width()&&(f=u-this.element.width()),i.show().css({top:e.height()-6,left:e.position().left+r.position().left-(f+6)})):(u=r.position().left+i.width(),this.model.enableRTL&&(u=this.element.width()-r.offset().left+i.width()),u>this.element.width()&&(f=u-this.element.width()),this.model.enableRTL&&i.show().css({right:this.element.width()-r.offset().left+this.element.offset().left-r.outerWidth()-f}),i.show().css({left:r.position().left-f}),this.element.hasClass("e-grpdivhide")&&(this.element.removeClass("e-grpdivhide"),this.element.find(".e-active-content").eq(0).addClass("e-resdivshow")))},_onQatClick:function(t){if(this._QATclick||(this._QATclick=!0),this.model.showQAT&&(n(t.target).hasClass("e-togglebutton e-btn")||n(t.target).parents(".e-togglebutton.e-btn").length>0)){var i,r=n(t.target);n(t.target).parents(".e-togglebutton.e-btn").length>0&&(r=n(t.target).parents(".e-togglebutton.e-btn"));i=r.parents(".e-qatooldiv").attr("id").slice(0,-7);this._rbnToggleBtnCustomization(t,i)}this._QATclick=!1},_onMobContentClick:function(t){if(n(t.target).hasClass("e-togglebutton e-btn")||n(t.target).parents(".e-togglebutton.e-btn").length>0){var r,i=n(t.target);if(n(t.target).parents(".e-togglebutton.e-btn").length>0&&(i=n(t.target).parents(".e-togglebutton.e-btn")),!i.parents(".e-mobdiv").length>0)return;r=i.parents(".e-mobdiv").attr("id").slice(0,-7);this._rbnToggleBtnCustomization(t,r)}},_rbnToggleBtnCustomization:function(n,t){var r=this.element.find("#"+t+":input.e-togglebutton.e-chkbx-hidden"),f=r.parent().siblings(".e-togglebutton"),u,i;this._rbnToggleBtnAction&&r.length>0&&(this._rbnToggleBtnAction=!1,i=r.data("ejToggleButton"),u=i.model.click,i.model.click="",f.click(),i.model.click=u);this._rbnToggleBtnAction=!0},_onQatMenuClick:function(t){var f=this.element.find(".e-rbnquickaccessbar"),r,i=n(t.target),u=i.parents(".e-qatmenuli");(i.hasClass("e-qatmenuli")||u.length>0)&&(u.length>0&&(i=u),i.attr("id")&&(r=this.element.find("#"+this._id+"_"+i.attr("id").slice(0,-7)+"_qatEle").parents(".e-qatooldiv"),r.is(":visible")?(r.hide(),i.addClass("e-addmenuli").removeClass("e-removemenuli").attr("title",this.localizedLabels.AddToQuickAccessToolbar)):(r.show(),i.removeClass("e-addmenuli").addClass("e-removemenuli").attr("title",this.localizedLabels.RemoveFromQuickAccessToolbar))),i.hasClass("e-rbnshowbelow")?(this.element.append(f.removeClass("e-rbnabove").addClass("e-rbnbelow")).removeClass("e-rbnwithqatabove").addClass("e-rbnwithqatbelow"),i.addClass("e-rbnshowabove").removeClass("e-rbnshowbelow").text(this.localizedLabels.ShowAboveTheRibbon),this.model.showBelowQAT=!0):i.hasClass("e-rbnshowabove")&&(this.element.prepend(f.removeClass("e-rbnbelow").addClass("e-rbnabove")).removeClass("e-rbnwithqatbelow").addClass("e-rbnwithqatabove"),i.addClass("e-rbnshowbelow").removeClass("e-rbnshowabove").text(this.localizedLabels.ShowBelowTheRibbon),this.model.showBelowQAT=!1),this.element.find(".e-rbnqatmenu").hide().css({top:"",left:""}),this.element.find(".e-qaresizebtn,.e-splitbtnqat").removeClass("e-tbtn e-active"),(this.model.allowResizing||this.model.isResponsive)&&(this._qatResize(),this._qatResizeRemove()))},_onGalContentClick:function(t){var i=this.element.find("#"+t.data.galleryId);n(t.target).hasClass("e-gallerybtn")?(i.find(".e-galleryselect").removeClass("e-galleryselect"),n(t.target).addClass("e-galleryselect")):n(t.target).parents(".e-gallerybtn")&&(i.find(".e-galleryselect").removeClass("e-galleryselect"),n(t.target).parents(".e-gallerybtn").addClass("e-galleryselect"))},_onExpandContentClick:function(t){var i=this.element.find("#"+t.data.galleryId);n(t.target).hasClass("e-gallerybtn")?(i.parent().find(".e-galleryselect").removeClass("e-galleryselect"),n(t.target).addClass("e-galleryselect")):n(t.target).parents(".e-gallerybtn")&&(i.parent().find(".e-galleryselect").removeClass("e-galleryselect"),n(t.target).parents(".e-gallerybtn").addClass("e-galleryselect"))},_onExpandGalleryClick:function(i){var e,r=this.element.find("#"+i.data.galleryId),u=i.data.expandedColumns,f,o,s,h;for(this.element.find(".e-gallexpandcontent:visible").length>0&&this._ribbonGalleryShow(),t.isNullOrUndefined(i.data.expandedColumns)&&(u=i.data.columns),e=Math.floor(r.find(".e-galleryrow").children().length/u),r.find(".e-galleryrow").children().length%u>0&&++e,f=1;f<=e;f++){for(o=t.buildTag("div#"+r.attr("id")+"_galleryExpanderRow_"+f).addClass("e-galleryexpanderrow").click({model:r},n.proxy(this._onGalleryItemClick,this)),s=0;s<u;s++)r.find(".e-galleryrow").children().eq(0).appendTo(o);o.appendTo(this.element.find("#"+r.attr("id")).parent().find(".e-expandercontent"))}for(r.hide(),h=0;h<i.data.columns;h++)r.find(".e-galleryrow").eq(0).append("<div><\/div>");this.element.find(".e-resizediv").is(":visible")&&(this.element.find(".e-resizediv").css("visibility","hidden"),this.element.find(".e-resizediv .e-gallexpandcontent").css("visibility","visible"));this.element.find("#"+r.attr("id")).parent().find(".e-gallexpandcontent").show();this.element.find("#"+r.attr("id")).parent().find(".e-expandercontent").height()>160&&!this._phoneMode&&this.element.find("#"+r.attr("id")).parent().find(".e-gallscrollcontent").ejScroller({height:160})},_onGalleryItemClick:function(i){var r,f,u,e,o;r={target:i.target,galleryModel:i.data.model,cancel:!1};n(r.target).hasClass("e-galleryexpanderrow")||(this._trigger("galleryItemClick",r),i.preventDefault(),t.raiseWebFormsServerEvents&&!t.isNullOrUndefined(this.model.serverEvents)&&this.model.serverEvents.indexOf("galleryItemClick")!=-1&&(f=n.extend(!0,{},this.model),u=r.target.id?r.target.id:r.target.parentNode.id,e={model:f,originalEventType:"galleryItemClick",id:u},o={model:i.data.model,id:u},t.raiseWebFormsServerEvents("galleryItemClick",e,o)))},_ribbonKeyDown:function(n){(n.keyCode===40||n.keyCode===39)&&this._tabUl.find(".e-tab:visible,.e-contextualtabset:visible").length===this._tabObj.model.selectedItemIndex&&n.stopImmediatePropagation()},_OnKeyDown:function(t){var i,r;t.keyCode===27&&(i=this.element.find(".e-resizediv"),this.element.find(".e-ribbonbackstagepage").is(":visible")?this.hideBackstage():n(".e-menu-wrap").children(".e-split:visible").length<=0&&i.find(".e-ddl").parents(".e-controlclicked").length<=0&&i.is(":visible")?this._resizeDivHide():this.element.find(".e-gallexpandcontent").is(":visible")&&this._ribbonGalleryShow(),i.is(":visible")&&i.find(".e-controlclicked").removeClass("e-controlclicked"),r=this.element.find("#"+this._id+"_modelDiv"),r.is(":visible")&&r.css("display","none"))},_ribCollapse:function(n){var n={clickType:this._clickValue};this._clickValue==null&&(n={clickType:"click"});this._clickValue=null;this._trigger("collapse",n)},_ribExpand:function(n){this.element.find(".e-ribbonpin").length>0&&this._tabContents.width(this.element.width());var n={clickType:this._clickValue};this._clickValue==null&&(n={clickType:"click"});this._clickValue=null;this._trigger("expand",n)},collapse:function(){var t=this.element.find(".e-active-content").eq(0),i=this.element.find("#"+this._id+"_togglebutton");this._initialRender&&this.model.collapsible?t.hide():t.slideUp("fast","swing",n.proxy(this._ribCollapse,this));this.element.find("li.e-active").eq(0).removeClass("e-active").addClass("e-collapseactive e-select");t.addClass("e-collapse-content");this._selectedItemIndex=this._tabObj.model.selectedItemIndex;this._tabObj.option({selectedItemIndex:0});this._tabObj.model.selectedItemIndex=0;this.model.selectedItemIndex=0;i.removeClass("e-expanded").addClass("e-collapsed");this._isCollapsed=!0},expand:function(){var t=this.element.find(".e-active-content").eq(0),i;t.hasClass("e-rbn-ondemand")&&this._groupContent({activeIndex:this.model.selectedItemIndex+1});i=this.element.find("#"+this._id+"_togglebutton");t.slideDown("fast","swing",n.proxy(this._ribExpand,this));this.element.find(".e-collapseactive").addClass("e-active").removeClass("e-collapseactive");t.removeClass("e-collapse-content");this._tabObj.model.selectedItemIndex=this._selectedItemIndex;this.model.selectedItemIndex=this._selectedItemIndex;i.removeClass("e-collapsed").addClass("e-expanded");i.find("span").addClass("e-rarrowup-2x");this._phoneMode||(this._ribbonResize(),this._addRibbonPin())},_addRibbonPin:function(){if(this.element.find(".e-ribbonpinicon").length<=0){var t=this.element.find(".e-expandcollapse"),n=this.element.find(".e-rarrowup-2x");n.parent().removeClass("e-collapsed").addClass("e-expanded e-ribbonpin");n.removeClass("e-rarrowup-2x").addClass("e-ribbonpinicon");this._isCollapsed=!1}this._tabContents.css({position:"absolute"}).width(this.element.width());this.element.hasClass("e-grpdivhide")&&this._tabContents.css({position:""}).width(this.element.width());this.element.hasClass("e-grpdivhide")&&this.element.find(".e-ribbonpin").length>0&&this.model.showQAT&&this._qAccessBar.hasClass("e-rbnbelow")&&this.element.find(".e-rbnquickaccessbar").css({position:"absolute"})},_removeRibbonPin:function(){var n=this.element.find("#"+this._id+"_togglebutton");this._tabContents.css({position:"",width:""});n.removeClass("e-ribbonpin");n.find("span").removeClass("e-ribbonpinicon").addClass("e-rarrowup-2x")},_onRbnPinDivClick:function(){this._removeRibbonPin()},_onGroupExpandClick:function(n){this._trigger("groupExpand",n)},_onBackStageItemClick:function(i){var e,o,r,s,h,u,f,c,l;this._phoneMode&&this.element.find(".e-responsivebackstagecontent .e-backstageTitle").text(n(i.currentTarget).text());u=this.element.find(i.data.contentDivId);i.preventDefault();n(i.target).hasClass("e-backstagebutton")&&(this.element.find(".e-ribbonbackstagepage").hide(),this._phoneMode&&this.element.find(".e-responsivebackstage").is(":visible")&&this.element.find(".e-responsivebackstage").removeClass("e-backstageshowanimate"));this.element.find(".e-backstageactive-content").removeClass("e-backstageactive-content").hide();this.element.find(".e-backstageactive").removeClass("e-backstageactive");this.model.enableOnDemand&&u.children().length==0&&(f=this.model.applicationTab.backstageSettings,c=f.pages.length,this.model.enableOnDemand&&c>0&&(l=n(i.currentTarget).index()-n(i.currentTarget).prevAll(".e-backstageseparator").length,u.append(n("#"+f.pages[l].contentID).addClass("e-backstagetabarea").show())));this.element.find(i.data.contentDivId).addClass("e-backstageactive-content").show();n(i.target).hasClass("e-backstageli")?n(i.target).addClass("e-backstageactive"):n(i.target).parents(".e-backstageli").addClass("e-backstageactive");e=this.element.find(".e-ribbonbackstagetop").height();this.element.find(".e-backstagescrollcontent").ejScroller({height:0});h=this.model.applicationTab.backstageSettings.height;r=t.isNullOrUndefined(n(i.target).text())?n(i.target).children("a").text():n(i.target).text();o=t.isNullOrUndefined(n(i.target).attr("id"))?n(i.target).parent().attr("id"):n(i.target).attr("id");s={id:o,text:r,target:i.target,type:"backstageItemClick",cancel:!1};!this.element.find(i.data.contentDivId).children().hasClass("e-backstagetitlecontent")&&this.model.enableOnDemand?t.buildTag("div#"+this._id+"_BackStageTitleContent").addClass("e-backstagetitlecontent").text(r).prependTo(u):this.model.enableOnDemand&&this.element.find(".e-backstagetitlecontent").text("").append(r);this._backStageHeightCalculate(h,e);n(i.target).parents(".e-backstageheader").length>0&&(n(i.target).find("a").length>0||i.target.localName=="a")&&this._phoneMode&&(this.element.find(".e-responsivebackstagecontent").show(),this.element.find(".e-responsivebackstage").removeClass("e-backstageshowanimate"));this._trigger("backstageItemClick",s)},_onBackStageTopIcon:function(){this.hideBackstage()},_backStageHeightCalculate:function(i,r){var u=this.model.applicationTab.backstageSettings.width;t.isNullOrUndefined(i)||(i.toString().endsWith("%")?i=this.element.find(".e-ribbonbackstagepage").height():i.toString().endsWith("px")&&(i=parseInt(i,10)),this.model.applicationTab.backstageSettings.height!="100%"&&(this.element.find(".e-backstageactive-content").height()>i-r?(this.element.find(".e-ribbonbackstagepage").height(i).width(u),this.element.find(".e-backstagescrollcontent").ejScroller({height:i-r,scroll:n.proxy(this._scroll,this)})):t.isNullOrUndefined(i)&&this.element.find(".e-backstageactive-content").height()>this.element.height()-r&&this.element.find(".e-backstagescrollcontent").ejScroller({height:this.element.height()-r,scroll:n.proxy(this._scroll,this)})));t.isNullOrUndefined(u)||(u.toString().endsWith("%")?u=this.element.find(".e-ribbonbackstagepage").width():u.toString().endsWith("px")&&(i=parseInt(u,10)),this.model.applicationTab.backstageSettings.width!="100%"&&(this.element.find(".e-backstageactive-content").width()>u-this.element.find(".e-backstageheader").width()?(this.element.find(".e-ribbonbackstagepage").height(i).width(u),this.element.find(".e-backstagescrollcontent").ejScroller({width:u})):t.isNullOrUndefined(u)&&this.element.find(".e-backstageactive-content").width()>this.element.width()&&this.element.find(".e-backstagescrollcontent").ejScroller({width:this.element.width()})));this._refreshBackstageScroller()},_refreshBackstageScroller:function(){var i=this;this.element.find(".e-backstagescrollcontent").find(".e-content").scroll(t.proxy(function(t){i.model.enableRTL?i.element.find(".e-backstagetopcontent").css({position:"relative",left:0}):i.element.find(".e-backstagetopcontent").css({position:"relative",left:-n(t.currentTarget).scrollLeft()});i.element.find(".e-backstagetopicon").css({left:n(t.currentTarget).scrollLeft()+26})}))},_onApplicationTabClick:function(n){n.preventDefault();var t=this.element.find(".e-backstagecontent");this.model.enableOnDemand&&t.length==0&&this._createApplicationTab();t=this.element.find(".e-backstagecontent");this.showBackstage()},showBackstage:function(){var h,b,k,f,c,r,l,o,e,a,v=this.element.find(".e-backstagecontent"),p,s,u,i,w;if(this.model.enableOnDemand&&v.length==0&&this._createApplicationTab(),e=this.element.find(".e-ribbonbackstagebody"),a=this.element.find(".e-backstageheader"),f=this.model.applicationTab.backstageSettings.height,c=this.model.applicationTab.backstageSettings.width,this.model.applicationTab.type===t.Ribbon.ApplicationTabType.Backstage){t.isNullOrUndefined(f)?(this.element.find(".e-ribbonbackstagepage").height(this.element.height()).width(this.element.width()),e.height(this.element.height()-92),a.height(this.element.height()-92)):(e.height(f-92),a.height(f-92));t.isNullOrUndefined(c)?(this.element.find(".e-ribbonbackstagepage").height(this.element.height()).width(this.element.width()),e.width(this.element.width())):e.width(c);this.element.css({position:"relative"});this.element.find(".e-ribbonbackstagepage").show();h=this.element.find(".e-backstageheader").width();this.element.find(".e-backstagetopcontent").width(h);this.element.find(".e-backstageheader").width(h);o=this.element.find(".e-backstageli");o.width(o.width());k=this.element.find(".e-ribbonbackstagetop").height();this.element.find(".e-backstageactive-content").removeClass("e-backstageactive-content").hide();this.element.find(".e-backstageactive").removeClass("e-backstageactive");o.first().addClass("e-backstageactive");this.element.find(".e-backstagecontent").first().addClass("e-backstageactive-content").show();this.element.find(".e-backstagescrollcontent").ejScroller({height:0,width:0});r=this.element.find(".e-backstageli").first();l=t.isNullOrUndefined(n(r).text())?n(r).children("a").text():n(r).text();var y=this.model.applicationTab.backstageSettings,d=y.pages.length,v=this.element.find(".e-backstagecontent");this._phoneMode||this.model.enableOnDemand&&d>0&&!t.isNullOrUndefined(y.pages[0].text)&&(p=n(".e-backstageactive-content").index()-1,s=n(v).filter(".e-backstageactive-content"),p!=-1&&n(s).append(n("#"+y.pages[p].contentID).addClass("e-backstagetabarea").show()),n(s).children().hasClass("e-backstagetitlecontent")||n(s).prepend(t.buildTag("div#"+this._id+"_BackStageTitleContent").addClass("e-backstagetitlecontent")));this._backStageHeightCalculate(f,k);v=this.element.find(".e-backstagecontent");u=this.element.find(".e-responsivebackstage").removeClass("e-reshide");i=u.find(".e-backstageheader");this._phoneMode&&(this.element.find(".e-responsivetabheader").is(":visible")&&this.element.find(".e-responsivetabheader").removeClass("e-resshow"),u.children().length>0&&(w=parseInt(i.css("top")),this.element.parent(".e-js").length>0&&document.documentElement.clientHeight-u.find(".e-backstageheader").offset().top>this.element.parent(".e-js").height()?i.css({width:this.element.find(".e-header").outerWidth()/2,height:this.element.parent().height()}):i.css({width:this.element.find(".e-header").outerWidth()/2,height:document.documentElement.clientHeight-u.find(".e-backstageheader").offset().top}),i.offset().top!=w&&i.height(i.height()-w)),u.addClass("e-backstageshowanimate"),this.element.find(".e-backstageactive").removeClass("e-backstageactive"));b={id:n(r).attr("id"),text:l,target:n(r).children(),type:"backstageItemClick",cancel:!1};this.model.enableOnDemand&&this.element.find(".e-backstagetitlecontent").text("").append(l);this._trigger("backstageItemClick",b)}},hideBackstage:function(){this.element.find(".e-ribbonbackstagepage").hide();this._phoneMode&&this.goToMainContent()},_initPrivateProperties:function(){this._initialRender=!1;this._tabUl=this.element.find(".e-header");this._applicationTab=this.element.find(".e-apptab");this._ribbonTabs=this._tabUl.find(":not('a')");this._tabText=this.element.find(".e-link");this._id=this.element.attr("id");this._tabObj=this.element.data("ejTab");this._isCollapsed=!1;this._contextualTabSet="";this._resizeWidth=0;this._selectedItemIndex=0;this._clickValue=null;this._tabContents=this.element.children().attr("role","tablist").not(".e-header,.e-disable,.e-resizediv,.e-tooltipdiv,.e-ribbonbackstagepage");this._qatControlsName=[];this._qAccessBar=this.element.find(".e-rbnquickaccessbar");this._rbnToggleBtnAction=!0;this.localizedLabels=this._getLocalizedLabels();this._phoneMode=(this.model.allowResizing||this.model.isResponsive)&&document.documentElement.clientWidth<420?!0:!1;this._responsiveTarget=null;this._responsiveContent=null;this._responsiveContentStyle=null;this._responsiveGallery=null;this._mobileToolbar=[];this._mobileContents=[];this._responsiveTabText=null;this._responsiveHeight=document.documentElement.clientHeight*(parseInt("40%")/100);this._QATclick=!1},_getLocalizedLabels:function(){return t.getLocalizedConstants(this.sfType,this.model.locale)},_customization:function(){var f,r={},u,i=this;this._initial&&(this.model.contextualTabs.length>0&&this.model.contextualTabs[0].tabs.length>0&&this._contextualTabs(),f="<div id="+this._id+'_togglebutton class="e-expanded"><span class="e-icon e-rarrowup-2x"><\/span><\/div>',u=t.buildTag("li#"+this._id+"_expandCollapse.e-expandcollapse",f),this.element.find(".e-active-content").eq(0).append(u),r.expandObj=this.model.expandPinSettings,r.collapseObj=this.model.collapsePinSettings,u.mouseover({value:r,ribbonId:this._id},n.proxy(this._tooltip,this)).mouseout({ribbonId:this._id},n.proxy(this._toolTipOut,this)));this.model.enableOnDemand?this._createAppTabOnDemand():this._createApplicationTab();this.element.find(".e-content.e-content-item").on("webkitTransitionEnd otransitionend oTransitionEnd msTransitionEnd transitionend",function(){i.element.hasClass("e-responsive")&&(i.element.find(".e-ribupdivarrow .e-icon").hasClass("e-ribuparrow")?i._ribExpand(this):i.element.find(".e-ribupdivarrow .e-icon").hasClass("e-ribdownarrow")&&i._ribCollapse(this))})},_createAppTabOnDemand:function(){var i;this.model.applicationTab.type===t.Ribbon.ApplicationTabType.Menu?(this._applicationTab.find(".e-link").hide(),this.model.applicationTab.menuSettings&&(i=this.model.applicationTab.menuSettings),this.model.applicationTab.menuItemID&&(n("#"+this.model.applicationTab.menuItemID).appendTo(this._applicationTab),i.enableRTL=this.model.enableRTL,i.isResponsive=!1,n("#"+this.model.applicationTab.menuItemID).addClass("e-rbncustomelement").ejMenu(i)),n("#"+this._id+"_"+this.model.applicationTab.type.replace(/\s/g,"")).hide()):this._applicationTab.find(".e-link").text(this.model.applicationTab.backstageSettings.text);this._applicationTab.click(n.proxy(this._onApplicationTabClick,this));this.model.applicationTab.type===t.Ribbon.ApplicationTabType.Backstage&&this._applicationTab.find("a").addClass("e-apptabanchor")},_createApplicationTab:function(){var e=this.element.find(".e-ribbonbackstagepage"),s=this.element.find(".e-backstageheader"),l,a,b,k,h=this.element.find(".e-ribbonbackstagebody"),d=this.element.find(".e-backstagetopcontent"),ot,v,r,rt,y,g,f,c,p,u,w,nt,tt,o,ut,i,it,ft,et;if(r=this.model.applicationTab.backstageSettings,rt=r.pages.length,this.model.applicationTab.type!==t.Ribbon.ApplicationTabType.Menu||this.model.enableOnDemand?(t.isNullOrUndefined(this.model.applicationTab.menuItemID)||(g=n("#"+this.model.applicationTab.menuItemID).data("ejMenu"),g&&g._destroy(),n("#"+this.model.applicationTab.menuItemID).appendTo("body"),this.model.applicationTab.menuItemID=null,this.model.applicationTab.menuSettings={},this._applicationTab.find(".e-link").css("display","block")),this._applicationTab.find(".e-link").text(this.model.applicationTab.backstageSettings.text)):(this._applicationTab.find(".e-link").hide(),this.element.find(".e-backstagetabarea").appendTo("body").removeClass("e-backstagetabarea"),e.remove(),this.model.applicationTab.menuSettings&&(y=this.model.applicationTab.menuSettings),this.model.applicationTab.menuItemID&&(n("#"+this.model.applicationTab.menuItemID).appendTo(this._applicationTab),y.enableRTL=this.model.enableRTL,y.isResponsive=!1,n("#"+this.model.applicationTab.menuItemID).addClass("e-rbncustomelement").ejMenu(y)),n("#"+this._id+"_"+this.model.applicationTab.type.replace(/\s/g,"")).hide()),this._applicationTab.click(n.proxy(this._onApplicationTabClick,this)),this.element.find(".e-content.e-content-item").css("box-sizing","content-box"),this.model.applicationTab.type===t.Ribbon.ApplicationTabType.Backstage){for(f=this.model.applicationTab.backstageSettings.height,f.toString().endsWith("%")?f=n(document).height()*(parseInt(f.slice(0,-1),10)/100):f.toString().endsWith("px")&&(f=parseInt(f,10)),e.length==0&&(e=t.buildTag("div#"+this._id+"_BackStage").addClass("e-ribbonbackstagepage"),h=t.buildTag("div#"+this._id+"_BackStageBody").addClass("e-ribbonbackstagebody"),s=t.buildTag("ul#"+this._id+"_BackStageHeader").addClass("e-backstageheader")),e.height(f).width(this.model.applicationTab.backstageSettings.width).hide(),h.height(f-50),s.height(f-50),i=0;i<rt;i++)c=this.element.find("#"+r.pages[i].id+"_backStageTab"),p=this.element.find("#"+r.pages[i].id+"_backStageBtn"),t.isNullOrUndefined(r.pages[i].itemType)&&(r.pages[i].itemType=t.Ribbon.ItemType.Tab),r.pages[i].itemType==t.Ribbon.ItemType.Button&&(c=this.element.find("#"+r.pages[i].id+"_backStageBtn"),p=this.element.find("#"+r.pages[i].id+"_backStageTab")),this.element.find(".e-ribbonbackstagepage").length==0||c.length==0&&p.length==0?(l=t.buildTag("li").addClass("e-backstageli").attr("tabindex",0).click({contentDivId:"#"+this._id+"_BackStage_"+r.pages[i].id.replace(/\s/g,""),height:r.height},n.proxy(this._onBackStageItemClick,this)),r.pages[i].itemType==t.Ribbon.ItemType.Tab?(a=t.buildTag("a",r.pages[i].text,{},{href:"#"+this._id+"_BackStage_"+r.pages[i].id.replace(/\s/g,"")}).focus(function(){n(this).parents(".e-backstageli").addClass("e-bsmaterial-focus")}).focusout(function(){n(this).parents(".e-backstageli").removeClass("e-bsmaterial-focus")}),l.append(a).attr("id",r.pages[i].id+"_backStageTab")):l.append(r.pages[i].text).addClass("e-backstagebutton").attr("id",r.pages[i].id+"_backStageBtn"),s.append(l),r.pages[i].enableSeparator&&(v=t.buildTag("li").addClass("e-backstageseparator"),s.append(v)),r.pages[i].itemType==t.Ribbon.ItemType.Tab&&this._addBackStageTabCont(r,h,i)):(u=p,c.length>0&&(u=c),r.pages[i].itemType==t.Ribbon.ItemType.Button?(u.find("a").length>0&&(this._removeBackStageTabCont(this.element.find(u.find("a").attr("href"))),u.attr("id",r.pages[i].id+"_backStageBtn").addClass("e-backstagebutton"),u.find("a").remove()),u.text(r.pages[i].text)):(u.hasClass("e-backstagebutton")&&(a=t.buildTag("a",r.pages[i].text,{},{href:"#"+this._id+"_BackStage_"+r.pages[i].id.replace(/\s/g,"")}).focus(function(){n(this).parents(".e-backstageli").addClass("e-bsmaterial-focus")}).focusout(function(){n(this).parents(".e-backstageli").removeClass("e-bsmaterial-focus")}),u.append(a).attr("id",r.pages[i].id+"_backStageTab").removeClass("e-backstagebutton"),this._addBackStageTabCont(r,h,i)),u.find("a").text(r.pages[i].text),w=this.element.find(u.find("a").attr("href")),r.pages[i].contentID!=w.find(".e-backstagetabarea").attr("id")&&(w.find(".e-backstagetabarea").removeClass("e-backstagetabarea").appendTo("body"),w.append(n("#"+r.pages[i].contentID).addClass("e-backstagetabarea")))),r.pages[i].enableSeparator?u.next().length>0&&!u.next().hasClass("e-backstageseparator")&&(v=t.buildTag("li").addClass("e-backstageseparator"),u.after(v)):u.next().length>0&&u.next().hasClass("e-backstageseparator")&&u.next().remove());if(this.element.find(".e-ribbonbackstagepage").length==0)h.prepend(s),k=t.buildTag("div#"+this._id+"_BackStageTop").addClass("e-ribbonbackstagetop").height(92),d=t.buildTag("div#"+this._id+"_BackStageTopContent").addClass("e-backstagetopcontent").appendTo(k),ot=t.buildTag("div#"+this._id+"_BackStageTopIcon","<div class='e-ribbonbackicon'><span><\/span><\/div>").addClass("e-backstagetopicon").click(n.proxy(this._onBackStageTopIcon,this)).appendTo(d),e.append(k),b=t.buildTag("div#"+this._id+"_backStageScrollContent","<div><\/div>").addClass("e-backstagescrollcontent"),b.children().append(h),e.append(b),this._applicationTab.addClass("e-backstagetab"),this._applicationTab.find("a").addClass("e-apptabanchor"),this.element.find(".e-ribbonbackstagepage").length>0&&this.element.find(".e-ribbonbackstagepage").remove(),this.element.append(e),nt=this.element.find(".e-responsivebackstagecontent .e-resbackstagecontent"),e.find(".e-ribbonbackstagebody .e-backstagecontent").length>0&&this._phoneMode&&(nt.children().remove(),nt.append(e.find(".e-ribbonbackstagebody .e-backstagecontent")));else{for(tt=[],i=0;i<this.model.applicationTab.backstageSettings.pages.length;i++)tt.push(this.model.applicationTab.backstageSettings.pages[i].id);for(i=0;i<this.element.find(".e-backstageli").length;i++)o=this.element.find(".e-backstageli").eq(i),ut=o.attr("id").split("_")[0],n.inArray(ut,tt)<0&&(o.find("a").length>0&&this._removeBackStageTabCont(this.element.find(o.find("a").attr("href"))),o.next().length>0&&o.next().hasClass("e-backstageseparator")&&o.next().remove(),o.remove(),--i);for(i=0;i<r.pages.length;i++)this._checkBackstageTabOrder(r.pages[i],i)}t.isNullOrUndefined(r.headerWidth)||(d.css("width",r.headerWidth),s.css("width",r.headerWidth))}this.model.enableOnDemand&&this._phoneMode&&(it=this.element.find(".e-responsivebackstage"),ft=this.element.find(".e-ribbonbackstagepage"),this._tabUl.find(".e-backstagetab").length>0&&it.length==1&&(et=ft.find(".e-ribbonbackstagebody .e-backstageheader"),it.append(et).addClass("e-reshide")))},_addBackStageTabCont:function(i,r,u){var f=t.buildTag("div#"+this._id+"_BackStage_"+i.pages[u].id.replace(/\s/g,"")).addClass("e-backstagecontent").css({width:"auto"}),e;u>0&&f.hide();i.pages[u].contentID&&!this.model.enableOnDemand&&(e=t.buildTag("div#"+this._id+"_BackStageTitleContent").addClass("e-backstagetitlecontent").appendTo(n(f)),n(e).text("").append(i.pages[u].text),n(f).append(n("#"+i.pages[u].contentID).addClass("e-backstagetabarea").show()));r.append(f)},_removeBackStageTabCont:function(n){n.find(".e-backstagetabarea").removeClass("e-backstagetabarea").appendTo("body");n.remove()},_conTabsRemove:function(){for(var r=this.element.find(".e-contextli,.e-contextliset"),t,u=r.length,i=0;i<u;i++)t=n(r[i]),t.is(":visible")?t.find("li").insertAfter(t.eq(0)):t.find("li").hide().insertAfter(t.eq(0)),t.remove()},_contextualTabs:function(){var i,f,e,o,r,s,u;if(this.model.contextualTabs)for(o=this.model.contextualTabs.length,r=0;r<o;r++)if(i=this.model.contextualTabs[r],i.tabs.length>0){for(f=t.buildTag("li#e-contextlisetIn_"+r).addClass("e-contextliset"),this._contextualTabSet=this._tabUl.children(".e-contextualtabset").filter(function(){return n(this).text()===i.tabs[0].text}),this._contextualTabSet.before(f),this._tabUl.children(".e-contextualtabset").find("a").css("color",i.borderColor),e=t.buildTag("ul.e-contextual","",{background:i.backgroundColor,"border-top-color":i.borderColor}),s=i.tabs.length,u=0;u<s;u++)e.append(this._tabUl.children(".e-contextualtabset").filter(function(){return n(this).text()===i.tabs[u].text}));e.appendTo(f)}},addBackStageItem:function(n,i){var u=this.model.applicationTab.backstageSettings.pages.length,r;t.isNullOrUndefined(i)||(u=i);this.model.applicationTab.backstageSettings.pages.splice(u,0,n);this._initial=!1;this._customization();this._phoneMode&&(r=this.element.find(".e-ribbonbackstagepage"),r.find(".e-ribbonbackstagebody .e-backstagecontent").length>0&&this.element.find(".e-responsivebackstagecontent .e-resbackstagecontent").append(r.find(".e-ribbonbackstagebody .e-backstagecontent")))},_checkBackstageTabOrder:function(n,t){var i=this.element.find("#"+n.id+"_backStageTab"),r;i.length==0&&(i=this.element.find("#"+n.id+"_backStageBtn"));i.index()!=t&&(r=this.element.find(".e-backstageli").eq(t),n.enableSeparator&&i.next().hasClass("e-backstageseparator")&&(r=i.next().insertBefore(r)),i.insertBefore(r),this.element.find("#"+this._id+"_BackStage_"+n.id).insertBefore(this.element.find(".e-backstagecontent").eq(t)))},removeBackStageItem:function(t){var r,i;r=this.model.applicationTab.backstageSettings.pages[t].id;i="#"+this._id+"_BackStage_"+r;this.model.applicationTab.backstageSettings.pages.splice(t,1);this.element.find(i).hasClass("e-backstagecontent")&&this.element.find(i).remove();n(this.element.find(".e-backstageli")[t]).remove();this.hideBackstage()},updateBackStageItem:function(t,i){if(n.extend(this.model.applicationTab.backstageSettings.pages[t],i),this._initial=!1,this._customization(),this._phoneMode){var r=this.element.find(".e-ribbonbackstagepage");r.find(".e-ribbonbackstagebody .e-backstagecontent").length>0&&this.element.find(".e-responsivebackstagecontent .e-resbackstagecontent").append(r.find(".e-ribbonbackstagebody .e-backstagecontent"))}},showContextualTab:function(t){this.element.find(".e-contexttitle").filter(function(){return n(this).text()===t}).parent().parent().show()},hideContextualTab:function(t){this.element.find(".e-contexttitle").filter(function(){return n(this).text()===t}).parent().parent().hide();this._tabObj.option({selectedItemIndex:this._tabObj.model.selectedItemIndex+1})},_createQAT:function(){if(this.model.showQAT&&(!t.isNullOrUndefined(this.model.qatItems)||this.element.find(".e-rbnquickaccessbar").children().length==0)){if(this.model.enableOnDemand&&(t.isNullOrUndefined(this.model.tabs)||this._createCollapseQuickaccess(this.model.tabs),!t.isNullOrUndefined(this.model.contextualTabs)))for(var n=0;n<this.model.contextualTabs.length;n++)this._createCollapseQuickaccess(this.model.contextualTabs[n].tabs);this.model.showQAT&&!t.isNullOrUndefined(this.model.qatItems)&&this._createCollapseQuickaccess()}this._createQuickAccessBar()},_createCollapseQuickaccess:function(n){var i,r,u,f,e;if(t.isNullOrUndefined(n)||t.isNullOrUndefined(this.model.qatItems)&&this.element.find(".e-rbnquickaccessbar").children().length!=0)this._createqatItems(this.model.qatItems);else for(i=0;i<n.length;i++)if(!t.isNullOrUndefined(n[i].groups))for(r=0;r<n[i].groups.length;r++)if(!t.isNullOrUndefined(n[i].groups[r].content))for(u=0;u<n[i].groups[r].content.length;u++)f=n[i].groups[r].content[u].groups,e=n[i].groups[r].content[u].defaults,n[i].groups[r].content[u].defaults,this._createqatItems(f,e)},_createqatItems:function(i,r){var y,p,u,f,w,b,l,a,s,h,o,e,c,v;for(this.model.showQAT&&(!t.isNullOrUndefined(this.model.qatItems)||this.element.find(".e-rbnquickaccessbar").children().length==0)&&(y=t.buildTag("div#"+this._id+"order")),p=[],u=0;u<i.length;u++)if(!t.isNullOrUndefined(i[u])){t.isNullOrUndefined(r)||t.isNullOrUndefined(r.type)||(i[u].type=r.type);t.isNullOrUndefined(i[u].type)&&(i[u].type=t.Ribbon.Type.Button);t.isNullOrUndefined(i[u].isBig)&&!t.isNullOrUndefined(r)&&(i[u].isBig=r.isBig);f=this.model.buttonDefaults;t.isNullOrUndefined(i[u].height)&&(t.isNullOrUndefined(i[u].buttonSettings)||t.isNullOrUndefined(i[u].buttonSettings.height)?t.isNullOrUndefined(r)||t.isNullOrUndefined(r.height)?t.isNullOrUndefined(f)||t.isNullOrUndefined(f.height)||(w=f.height):w=r.height:w=i[u].buttonSettings.height,i[u].height=w,w=null);t.isNullOrUndefined(i[u].width)&&(t.isNullOrUndefined(i[u].buttonSettings)||t.isNullOrUndefined(i[u].buttonSettings.width)?t.isNullOrUndefined(r)||t.isNullOrUndefined(r.width)?t.isNullOrUndefined(f)||t.isNullOrUndefined(f.width)||(b=f.width):b=r.width:b=i[u].buttonSettings.width,i[u].width=b,b=null);t.isNullOrUndefined(i[u].buttonSettings)||(l=i[u].buttonSettings,!t.isNullOrUndefined(l.enabled)||t.isNullOrUndefined(f)||t.isNullOrUndefined(f.enabled)||(l.enabled=f.enabled),!t.isNullOrUndefined(l.enableRTL)||t.isNullOrUndefined(f)||t.isNullOrUndefined(f.enableRTL)||(l.enableRTL=f.enableRTL),!t.isNullOrUndefined(l.cssClass)||t.isNullOrUndefined(f)||t.isNullOrUndefined(f.cssClass)||(l.cssClass=f.cssClass),!t.isNullOrUndefined(l.showRoundedCorner)||t.isNullOrUndefined(f)||t.isNullOrUndefined(f.showRoundedCorner)||(l.showRoundedCorner=f.showRoundedCorner));t.isNullOrUndefined(i[u].splitButtonSettings)||(a=i[u].splitButtonSettings,!t.isNullOrUndefined(a.enabled)||t.isNullOrUndefined(f)||t.isNullOrUndefined(f.enabled)||(a.enabled=f.enabled),!t.isNullOrUndefined(a.enableRTL)||t.isNullOrUndefined(f)||t.isNullOrUndefined(f.enableRTL)||(a.enableRTL=f.enableRTL),!t.isNullOrUndefined(a.cssClass)||t.isNullOrUndefined(f)||t.isNullOrUndefined(f.cssClass)||(a.cssClass=f.cssClass),!t.isNullOrUndefined(a.showRoundedCorner)||t.isNullOrUndefined(f)||t.isNullOrUndefined(f.showRoundedCorner)||(a.showRoundedCorner=f.showRoundedCorner));t.isNullOrUndefined(i[u].toggleButtonSettings)||(!t.isNullOrUndefined(i[u].toggleButtonSettings.enabled)||t.isNullOrUndefined(f)||t.isNullOrUndefined(f.enabled)||(i[u].toggleButtonSettings.enabled=f.enabled),!t.isNullOrUndefined(i[u].toggleButtonSettings.enableRTL)||t.isNullOrUndefined(f)||t.isNullOrUndefined(f.enableRTL)||(i[u].toggleButtonSettings.enableRTL=f.enableRTL),!t.isNullOrUndefined(i[u].toggleButtonSettings.cssClass)||t.isNullOrUndefined(f)||t.isNullOrUndefined(f.cssClass)||(i[u].toggleButtonSettings.cssClass=f.cssClass),!t.isNullOrUndefined(i[u].toggleButtonSettings.showRoundedCorner)||t.isNullOrUndefined(f)||t.isNullOrUndefined(f.showRoundedCorner)||(i[u].toggleButtonSettings.showRoundedCorner=f.showRoundedCorner));t.isNullOrUndefined(i[u].quickAccessMode)&&(i[u].quickAccessMode="none");switch(i[u].type){case t.Ribbon.Type.SplitButton:s=i[u];e={};s.splitButtonSettings&&(e=JSON.parse(JSON.stringify(s.splitButtonSettings)));e.height=t.isNullOrUndefined(e.height)?i[u].height:e.height;e.width=t.isNullOrUndefined(e.width)?i[u].width:e.width;e.cssClass=e.cssClass?e.cssClass.concat(" e-rbn-splitbtn e-rbn-button"):"e-rbn-splitbtn e-rbn-button";e.enableRTL=this.model.enableRTL;s.quickAccessMode!="none"&&this.model.showQAT&&(this._splitButtonQAT=t.buildTag("button#"+this._id+"_"+s.id+"_qatEle",s.text,{},{type:"button"}).addClass(s.cssClass),c=t.buildTag("div#"+this._id+"_"+s.id+"_qatDiv",this._splitButtonQAT).addClass("e-qatooldiv"),y.append(c),e.height=30,e.arrowPosition=t.ArrowPosition.Right,e.contentType=t.ContentType.ImageOnly,n.isEmptyObject(s.customToolTip)?t.isNullOrUndefined(s.toolTip)||c.attr("title",s.toolTip):c.mouseover({value:s,ribbonId:this._id},n.proxy(this._tooltip,this)).mouseout({ribbonId:this._id},n.proxy(this._toolTipOut,this)),this._splitButtonQAT.ejSplitButton(e),s.quickAccessMode=="menu"&&c.hide(),t.isNullOrUndefined(s.text)?t.isNullOrUndefined(s.splitButtonSettings.text)||(v=s.splitButtonSettings.text):v=s.text,p.push({id:s.id,text:v,qAccess:s.quickAccessMode}));break;case t.Ribbon.Type.ToggleButton:h=i[u];e={};h.toggleButtonSettings&&(e=JSON.parse(JSON.stringify(h.toggleButtonSettings)));e.height=t.isNullOrUndefined(e.height)?i[u].height:e.height;e.width=t.isNullOrUndefined(e.width)?i[u].width:e.width;h.quickAccessMode!="none"&&this.model.showQAT&&(this._toggleButtonQAT=t.buildTag("input#"+this._id+"_"+h.id+"_qatEle","","",{type:"checkbox"}).addClass(h.cssClass),c=t.buildTag("div#"+this._id+"_"+h.id+"_qatDiv",this._toggleButtonQAT).addClass("e-qatooldiv e-rbn-button"),e.height=30,e.contentType=t.ContentType.ImageOnly,n.isEmptyObject(h.customToolTip)?t.isNullOrUndefined(h.toolTip)||c.attr("title",h.toolTip):c.mouseover({value:h,ribbonId:this._id},n.proxy(this._tooltip,this)).mouseout({ribbonId:this._id},n.proxy(this._toolTipOut,this)),this._toggleButtonQAT.ejToggleButton(e).addClass("e-ribbonbtn"),c.find("button").addClass("e-rbn-button"),y.append(c),h.quickAccessMode=="menu"&&c.hide(),t.isNullOrUndefined(h.text)?t.isNullOrUndefined(h.toggleButtonSettings.defaultText)||(v=h.toggleButtonSettings.defaultText):v=h.text,p.push({id:h.id,text:v,qAccess:h.quickAccessMode}));break;case t.Ribbon.Type.Button:o=i[u];e={};o.buttonSettings&&(e=JSON.parse(JSON.stringify(o.buttonSettings)));e.height=t.isNullOrUndefined(e.height)?i[u].height:e.height;e.width=t.isNullOrUndefined(e.width)?i[u].width:e.width;o.quickAccessMode!="none"&&this.model.showQAT&&(this._buttonQAT=o.buttonSettings?o.buttonSettings.type?t.buildTag("button#"+this._id+"_"+o.id+"_qatEle",o.text,{},{type:o.buttonSettings.type}).addClass(o.cssClass):t.buildTag("button#"+this._id+"_"+o.id+"_qatEle",o.text).addClass(o.cssClass):t.buildTag("button#"+this._id+"_"+o.id+"_qatEle",o.text).addClass(o.cssClass),c=t.buildTag("div#"+this._id+"_"+o.id+"_qatDiv",this._buttonQAT).addClass("e-qatooldiv"),e.height=30,e.contentType=t.ContentType.ImageOnly,n.isEmptyObject(o.customToolTip)?t.isNullOrUndefined(o.toolTip)||c.attr("title",o.toolTip):c.mouseover({value:o,ribbonId:this._id},n.proxy(this._tooltip,this)).mouseout({ribbonId:this._id},n.proxy(this._toolTipOut,this)),this._buttonQAT.ejButton(e).addClass("e-ribbonbtn e-rbn-button"),y.append(c),o.quickAccessMode=="menu"&&c.hide(),t.isNullOrUndefined(o.text)?t.isNullOrUndefined(o.buttonSettings.text)||(v=o.buttonSettings.text):v=o.text,p.push({id:o.id,text:v,qAccess:o.quickAccessMode}))}}y.children().prependTo(this._qAccessBar);this._qatControlsName=n.merge(p,this._qatControlsName)},_resizeTabName:function(i){var r,u,f;if(!t.isNullOrUndefined(this.model.tabs))for(r=0;r<this.model.tabs.length;r++)if(this.model.tabs[r].text===i)return n(this._tabUl.find(".e-tab")[r]);if(!t.isNullOrUndefined(this.model.contextualTabs))for(r=0;r<this.model.contextualTabs.length;r++)for(u=0;u<this.model.contextualTabs[r].tabs.length;u++)if(this.model.contextualTabs[r].tabs[u].text===i)return n(this._tabUl.find(".e-contextliset")[r]).children().find(".e-contextualtabset").eq(u)},hideTab:function(i){var u=this._tabText.map(function(t){if(n(this).text()===i)return t})[0],r,l=this._tabObj.model.disabledItemIndex.length,c=!0,h,f,e,o,s;for(this._width=this.element.width(),t.isNullOrUndefined(u)&&(f=this._resizeTabName(i),this._phoneMode&&(f=this._responsiveTabText.filter(function(){return n(this).text()===i}),this.element.find(".e-responsivetabheader").addClass("e-resshow")),t.isNullOrUndefined(f)||f.hide()),e=this.element.find(".e-link").eq(u).attr("href"),h=this._tabText.filter(function(){return n(this).text()===i}).parents(".e-contextualtabset"),o=0;o<l;o++)this._tabObj.model.disabledItemIndex[o]===u&&(c=!1);c&&(h.length>0?(h.hide(),this._responsiveTabText.eq(t.isNullOrUndefined(this.model.applicationTab)?u:u-1).hide()):(this._tabText.filter(function(){return n(this).text()===i}).parent().hide(),this._responsiveTabText.eq(t.isNullOrUndefined(this.model.applicationTab)?u:u-1).hide()),n(e).hasClass("e-active-content")&&(n(e).hide(),n(e).removeClass("e-active-content")));this._tabUl.find(".e-active").is(":hidden")&&(this._phoneMode?(this.element.find(".e-responsivetabheader").addClass("e-resshow"),r=this._getVisibleItemIndex(this._responsiveTabText)):r=this._getVisibleItemIndex(this._tabText),r&&(s=this.element.find(".e-link").eq(r).attr("href"),n(this._tabText[r]).parent().hasClass("e-disable")?(n(this._tabText[u]).parent().removeClass("e-active"),n(this._tabText[r]).parent().addClass("e-active"),n(s).append(this.element.find("#"+this._id+"_disabled")),n(s).css("position","relative"),this.element.find("#"+this._id+"_disabled").show(),n(s).show(),this._tabObj.option("selectedItemIndex",r)):this._tabObj.option("selectedItemIndex",r),this._tabObj.model.selectedItemIndex=r,this.model.selectedItemIndex=r));this.element.hasClass("e-responsive")&&this.element.find(".e-responsivetabheader").removeClass("e-resshow")},showTab:function(i){var r=this._tabText.map(function(t){if(n(this).text()===i)return t})[0],f,u,e,o;t.isNullOrUndefined(r)&&(u=this._resizeTabName(i),this._phoneMode&&(u=this._responsiveTabText.filter(function(){return n(this).text()===i}),this.element.find(".e-responsivetabheader").addClass("e-resshow")),t.isNullOrUndefined(u)||u.show());e=this._tabText.filter(function(){return n(this).text()===i}).parent();f=this._tabText.filter(function(){return n(this).text()===i}).parents(".e-contextualtabset");f.length>0?(f.show(),this._responsiveTabText.eq(t.isNullOrUndefined(this.model.applicationTab)?r:r-1).show()):(this._tabText.filter(function(){return n(this).text()===i}).parent().show(),this._responsiveTabText.eq(t.isNullOrUndefined(this.model.applicationTab)?r:r-1).show());o=this.element.find(".e-content").eq(r);e.hasClass("e-active")?o.css("display")=="none"&&o.show():e.addClass("e-select");this.element.hasClass("e-responsive")&&this.element.find(".e-responsivetabheader").removeClass("e-resshow")},hideGroups:function(t,i){if(typeof i=="string")n("#"+this._id+"_"+t+"_"+i).hide();else for(var r=0;r<i.length;r++)n("#"+this._id+"_"+t+"_"+i[r]).hide()},showGroups:function(t,i){if(typeof i=="string")n("#"+this._id+"_"+t+"_"+i).show();else for(var r=0;r<i.length;r++)n("#"+this._id+"_"+t+"_"+i[r]).show()},hideGroupItems:function(t,i,r){if(typeof r=="string")n("#"+this._id+"_"+t+"_"+i+"_"+r).hide();else for(var u=0;u<r.length;u++)n("#"+this._id+"_"+t+"_"+i+"_"+r[u]).hide()},showGroupItems:function(t,i,r){if(typeof r=="string")n("#"+this._id+"_"+t+"_"+i+"_"+r).show();else for(var u=0;u<r.length;u++)n("#"+this._id+"_"+t+"_"+i+"_"+r[u]).show()},_getVisibleItemIndex:function(t){for(var i=1;i<t.length;){if(n(t[i]).is(":visible"))return i;i++}},removeApplicationTab:function(){var r,t,i;this._ribbonTabs=this._tabUl.find(".e-link").parent();r=this._ribbonTabs.eq(0).children().attr("href");t=this._ribbonTabs.eq(0);t.hasClass("e-apptab")&&(this._tabObj.contentPanels.splice(0,1),this._tabObj.items.splice(0,1),this._tabObj.selectedItemIndex(this._tabObj.model.selectedItemIndex-1),t.remove(),n(r).remove(),n(this._tabUl.find(".e-link").parent(".e-active")).find("a").addClass("e-removeapptab"),i=this._tabUl.find("li").eq(0),i.hasClass("e-disable")||i.hasClass("e-apptab")||(this._tabObj.option({enabledItemIndex:[0]}),this.model.disabledItemIndex.splice(0)),this.element.addClass("e-ribwithout-apptab"),this._ribbonWindowResize())},removeTab:function(t){var i,r,u,e,f;this._tabObj.model&&t>0&&(u={index:t},this._trigger("beforeTabRemove",u),this._ribbonTabs=this._tabUl.find(".e-link").parent(),r=this._ribbonTabs.eq(t).children().attr("href"),i=this._ribbonTabs.eq(t),this._phoneMode&&(f=this._responsiveTabText.eq(t-1)),i.hasClass("e-contextualtab")?i.parent().remove():i.hasClass("e-contextualtabset")?i.siblings(".e-contextualtabset").length===0?i.parent().remove():i.remove():i.remove(),this._phoneMode&&f.remove(),this._responsiveTabText=this.element.find(".e-responsivetabheader li"),e=this.element.find(".e-expandcollapse"),n(r).remove(),this._ribbonTabs=this._tabUl.find(".e-link").parent(),this._tabObj.model.selectedItemIndex===t&&(i=this._ribbonTabs.eq(t),this._phoneMode&&(f=this._responsiveTabText.eq(t-1),f.addClass("e-resactive"),this.element.find(".e-ribresmenu .e-reslink").text(this._responsiveTabText.filter(".e-resactive").text())),i.addClass("e-active"),r=i.children().attr("href"),n(r).append(e.click(n.proxy(this._tabExpandCollapse,this))),n(r).addClass("e-active-content e-activetop").show()),this._isCollapsed&&this.element.find(this._ribbonTabs.eq(t).children().attr("href")).append(e.click(n.proxy(this._tabExpandCollapse,this))),this._tabObj.contentPanels.splice(t,1),this._tabObj.items.splice(t,1),this.model.tabs.splice(t-1,1),this._phoneMode&&(this._mobileContents.splice(t-1,1),this._mobileToolbar.splice(t-1,1),this._ribbonWindowResize()),u={removedIndex:t},this._trigger("tabRemove",u))},removeTabGroup:function(i,r){var e,l,o,c,s,y,u,a,h,v,f;if(e=n(n(".e-ribbon .e-content")[i]).find(".e-groupdiv"),l=this._tabUl.find(".e-select,.e-active").eq(i),n(l).hasClass("e-tab"))for(o=this.model.tabs[i-1].groups,u=0;u<o.length;u++)o[u].text==r?(o.splice(u,1),n(e[u]).remove()):t.isNullOrUndefined(r)&&n(e[u]).remove();else for(c=this.model.tabs.length,y=this.model.contextualTabs.length,u=0;u<this.model.contextualTabs.length;u++)for(a=this.model.contextualTabs[u].tabs.length,h=0;h<a;h++)if(++c,c===i)for(s=this.model.contextualTabs[u].tabs[h].groups,v=s.length,f=0;f<v;f++)s[f].text==r?(s.splice(f,1),n(e[f]).remove()):t.isNullOrUndefined(r)&&n(e[f]).remove()},_addTabGrpIntoCollec:function(t,i,r){var e=i.length,o=r,u,f;for(n.isPlainObject(i)&&(e=1),u=0;u<e;u++)f=i,n.isPlainObject(i)||(f=i[u],u>0&&++o),t.groups.splice(o,0,f);return t},addTabGroup:function(i,r,u){var s,a=0,c=0,o,v,y,b,p,k,d,f,g,h,w,e,l;if(v=this._tabUl.find(".e-select,.e-active").eq(i),s=v.children("a").attr("href"),n(v).hasClass("e-tab"))for(k=this.model.tabs.length,f=0;f<k;f++)f===i-1&&(c=this.model.tabs[f].groups.length,t.isNullOrUndefined(u)&&(u=c),o=this._addTabGrpIntoCollec(this.model.tabs[f],r,u));else for(a=this.model.tabs.length,d=this.model.contextualTabs.length,f=0;f<d;f++)for(g=this.model.contextualTabs[f].tabs.length,h=0;h<g;h++)++a,a===i&&(c=this.model.contextualTabs[f].tabs[h].groups.length,t.isNullOrUndefined(u)&&(u=c),o=this._addTabGrpIntoCollec(this.model.contextualTabs[f].tabs[h],r,u));if(!t.isNullOrUndefined(o.groups[u])&&!this.element.find(".e-content.e-content-item").eq(i).hasClass("e-rbn-ondemand")){for(w=r.length,e=u,n.isPlainObject(r)&&(w=1),l=0;l<w;l++)!n.isPlainObject(r)&&l>0&&++e,y=t.isNullOrUndefined(o.groups[e].id)?t.isNullOrUndefined(o.groups[e].text)?o.groups[e].text:o.groups[e].text.replace(/\s/g,""):o.groups[e].id.replace(/\s/g,""),b=this._createControls(e,o,y,i),p=this._addControlsToGroup(e,o,y,b),n(s).children().eq(e).length>0?n(s).children().eq(e).before(p):n(s).append(p);n(s).hasClass("e-empty-content")&&n(s).removeClass("e-empty-content")}n(s).hasClass("e-parentdiv")||n(s).addClass("e-parentdiv");this._setTabContentHeight()},addTabGroupContent:function(i,r,u,f,e){var l,p=0,s,w,v,y,b,c,k,a,d,g,nt=!1,tt,it,rt,o,ut,h,ft;if(w=this._tabUl.find(".e-select,.e-active").eq(i),l=w.children("a").attr("href"),t.isNullOrUndefined(f)&&(f=0),t.isNullOrUndefined(e)&&(e=0),w.hasClass("e-tab"))for(it=this.model.tabs.length,o=0;o<it;o++)o===i-1&&(t.isNullOrUndefined(this.model.tabs[o].groups[r].content)||(g=this.model.tabs[o].groups[r].alignType,f!=0&&t.isNullOrUndefined(this.model.tabs[o].groups[r].content[f-1])||(t.isNullOrUndefined(this.model.tabs[o].groups[r].content[f])?(this.model.tabs[o].groups[r].content[f]=u,nt=!0):this.model.tabs[o].groups[r].content[f].groups.splice(e,0,u),s=this.model.tabs[o])));else for(p=this.model.tabs.length,rt=this.model.contextualTabs.length,o=0;o<rt;o++)for(ut=this.model.contextualTabs[o].tabs.length,h=0;h<ut;h++)++p,p===i&&(t.isNullOrUndefined(this.model.contextualTabs[o].tabs[h].groups[r].content)||(g=this.model.contextualTabs[o].tabs[h].groups[r].alignType,t.isNullOrUndefined(f)&&(f=this.model.contextualTabs[o].tabs[h].groups[r].content[0].groups.length),f!=0&&t.isNullOrUndefined(this.model.contextualTabs[o].tabs[h].groups[r].content[f-1])||(t.isNullOrUndefined(this.model.contextualTabs[o].tabs[h].groups[r].content[f])?(this.model.contextualTabs[o].tabs[h].groups[r].content[f]=u,nt=!0):this.model.contextualTabs[o].tabs[h].groups[r].content[f].groups.splice(e,0,u),s=this.model.contextualTabs[o].tabs[h])));t.isNullOrUndefined(s)||t.isNullOrUndefined(s.groups[r])||this.element.find(".e-content.e-content-item").eq(i).hasClass("e-rbn-ondemand")||(v=t.isNullOrUndefined(s.groups[r].id)?t.isNullOrUndefined(s.groups[r].text)?s.groups[r].text:s.groups[r].text.replace(/\s/g,""):s.groups[r].id.replace(/\s/g,""),y=this._createControls(r,s,v,i),a=t.buildTag("div"),tt=y.find(".e-innerdiv,.e-innerdivrow").eq(f).find(".e-controlpadding"),b=nt?tt:tt.eq(e),k=y.find("#"+b.attr("id")).next(),k.hasClass("e-separatordivrow")&&a.append(k),a.prepend(b),c=n(l).children().eq(r).find(".e-innerdiv,.e-innerdivrow").eq(f),!c.length>0&&(ft=g==t.Ribbon.alignType.columns?t.buildTag("div#"+this._id+"_"+s.id+"_"+v+"_"+(f+1)).addClass("e-innerdiv"):t.buildTag("div#"+this._id+"_"+s.id+"_"+v+"_"+(f+1)).addClass("e-innerdivrow"),n(l).children().eq(r).find(".e-ribGroupContent").append(ft),c=n(l).children().eq(r).find(".e-innerdiv,.e-innerdivrow").eq(f)),d=n(c).parent().height(),c.find(".e-controlpadding").eq(e).length>0?c.find(".e-controlpadding").eq(e).before(a.children()):c.append(a.children()),n(c).parent().height()>d&&this.element.find(".e-content").height(this.element.find(".e-content").height()+(n(c).parent().height()-d)),y.children().remove(),n(l).hasClass("e-empty-content")&&n(l).removeClass("e-empty-content"),this._setTabContentHeight())},removeTabGroupContent:function(i,r,u,f){var rt,k,c,d,s,a,p,e,g,w,nt,o,tt,y,it,h,v,b,l;if(rt=n(n(".e-ribbon .e-content")[i]).find(".e-groupdiv"),k=this._tabUl.find(".e-select,.e-active").eq(i),a=k.children("a").attr("href"),n(k).hasClass("e-tab")){for(c=this.model.tabs[i-1].groups,g=c.length,o=0;o<g;o++)if(c[o].text==r)if(t.isNullOrUndefined(u)){if(c[o].type===t.Ribbon.type.custom&&c[o].text==r&&t.isNullOrUndefined(c[o].content))e=n(a).find(".e-groupdiv").eq(o).find(".e-ribGroupContent"),this._phoneMode&&this.element.find(".e-responsivecontent").is(":visible")&&e.length==0&&(e=this.element.find(".e-responsivecontent").find(".e-ribGroupContent")),e.children().remove(),c[o].contentID=null;else if(c[o].text==r&&t.isNullOrUndefined(u)){if(s=c[o].content,!t.isNullOrUndefined(s))for(w=c[o].content.length,l=0;l<w;l++)s[l].groups.splice(0,s[l].groups.length);e=n(a).find(".e-groupdiv").eq(o).find(".e-ribGroupContent");this._phoneMode&&this.element.find(".e-responsivecontent").is(":visible")&&e.length==0&&(e=this.element.find(".e-responsivecontent").find(".e-ribGroupContent"));e.children().remove()}}else if(t.isNullOrUndefined(f)||t.isNullOrUndefined(c[o].content[u].groups))s=c[o].content[u],e=n(a).children(".e-groupdiv").eq(o).find(".e-innerdiv,.e-innerdivrow"),this._phoneMode&&this.element.find(".e-responsivecontent").is(":visible")&&e.length==0&&(e=this.element.find(".e-responsivecontent").find(".e-innerdiv,.e-innerdivrow")),e.eq(u).children().remove(),s.groups.splice(0,s.groups.length);else{for(s=c[o].content[u].groups,e=n(a).children(".e-groupdiv").eq(o).find(".e-innerdiv,.e-innerdivrow"),this._phoneMode&&this.element.find(".e-responsivecontent").is(":visible")&&e.length==0&&(e=this.element.find(".e-responsivecontent").find(".e-innerdiv,.e-innerdivrow")),p=e.eq(u),y=0;y<p.children().length;y++)s.splice(y,1);p.children().eq(f).remove();e.children().length==0&&e.remove()}}else for(d=this.model.tabs.length,nt=this.model.contextualTabs.length,o=0;o<nt;o++)for(tt=this.model.contextualTabs[o].tabs.length,y=0;y<tt;y++)if(++d,d===i)for(s=this.model.contextualTabs[o].tabs[y].groups,it=s.length,h=0;h<it;h++)if(s[h].text==r)if(t.isNullOrUndefined(u)){if(s[h].type===t.Ribbon.type.custom&&s[h].text==r&&t.isNullOrUndefined(s[h].content))e=n(a).find(".e-groupdiv").eq(h).find(".e-ribGroupContent"),this._phoneMode&&this.element.find(".e-responsivecontent").is(":visible")&&e.length==0&&(e=this.element.find(".e-responsivecontent").find(".e-ribGroupContent")),e.children().remove(),s[h].contentID=null;else if(t.isNullOrUndefined(u)){if(v=s[h].content,!t.isNullOrUndefined(v))for(w=s[h].content.length,l=0;l<w;l++)v[l].groups.splice(0,v[l].groups.length);e=n(a).find(".e-groupdiv").eq(h).find(".e-ribGroupContent");this._phoneMode&&this.element.find(".e-responsivecontent").is(":visible")&&e.length==0&&(e=this.element.find(".e-responsivecontent").find(".e-ribGroupContent"));e.children().remove()}}else if(t.isNullOrUndefined(f)||t.isNullOrUndefined(s[h].content[u].groups))v=s[h].content[u],e=n(a).children(".e-groupdiv").eq(h).find(".e-innerdiv,.e-innerdivrow"),this._phoneMode&&this.element.find(".e-responsivecontent").is(":visible")&&e.length==0&&(e=this.element.find(".e-responsivecontent").find(".e-innerdiv,.e-innerdivrow")),e.eq(u).children().remove(),v.groups.splice(0,v.groups.length);else{for(v=s[h].content[u].groups,e=n(a).children(".e-groupdiv").eq(h).find(".e-innerdiv,.e-innerdivrow"),this._phoneMode&&this.element.find(".e-responsivecontent").is(":visible")&&e.length==0&&(e=this.element.find(".e-responsivecontent").find(".e-innerdiv,.e-innerdivrow")),p=e.eq(u),b=0;b<p.children().length;b++)v.splice(b,1);p.children().eq(f).remove();e.children().length==0&&e.remove()}},addTab:function(i,r,u){var c={id:i.replace(/\s/g,""),text:i,groups:r},b=this.model.tabs.length,l,o,a,v,s,f,h,e;if(t.isNullOrUndefined(u)&&(u=this._tabUl.find(".e-select,.e-active").length),u>0){for(this._conTabsRemove(),this._tabObj.addItem("#"+this._id+"_"+i.replace(/\s/g,""),i,u),this._tabUl.find(".e-ribresmenu").nextAll("li.e-select").length>0&&(v=this._tabUl.find(".e-select,.e-active").not(".e-apptab,.e-tab"),v.insertBefore(this._tabUl.find(".e-ribresmenu"))),this._contextualTabs(),this._tabUl.find(".e-select,.e-active").eq(u).addClass("e-tab"),this._tabUl.find(".e-select > a,.e-active > a ").eq(u).addClass("e-link"),t.isNullOrUndefined(this.model.tabs[0].id)?(this.model.tabs[0]=c,this._tabObj.showItem(1)):this.model.tabs.splice(u-1,0,c),s=0;s<1;s++)for(f=this.model.tabs[u-1],h=f.groups.length,h==0&&this.element.find("#"+this._id+"_"+f.id).addClass("e-empty-content"),e=0;e<h;e++)if(!t.isNullOrUndefined(f.groups[e])){var y=t.isNullOrUndefined(f.groups[e].id)?t.isNullOrUndefined(f.groups[e].text)?f.groups[e].text:f.groups[e].text.replace(/\s/g,""):f.groups[e].id.replace(/\s/g,""),p=this._createControls(e,f,y,u),w=this._addControlsToGroup(e,f,y,p);o=this.element.find("#"+this._id+"_"+f.id);o.append(w).addClass("e-parentdiv")}this._setTabContentHeight();this.element.find(".e-content.e-content-item").css("box-sizing","content-box");this._ribbonTabs=this._tabUl.find(".e-link").parent();this._tabContents=this.element.children().attr("role","tablist").not(".e-header,.e-disable,.e-resizediv,.e-tooltipdiv,.e-ribbonbackstagepage");this._applicationTab.click(n.proxy(this._onApplicationTabClick,this));l=this._tabUl.find(".e-select,.e-active").eq(u);a={tabHeader:l,tabContent:o};this._trigger("tabAdd",a)}this._tabText=this.element.find(".e-link");this._applicationTab.hasClass("e-active")&&this._applicationTab.removeClass("e-active");n(o).hasClass("e-parentdiv")||n(o).addClass("e-parentdiv")},addContextualTabs:function(i,r){var v,o,s,h,c,u,f,l,e;if(this._tabUl.find(".e-tab").length<r&&(v=this.model.contextualTabs.length,t.isNullOrUndefined(r)&&(r=this._tabUl.find(".e-select,.e-active").length),r>0)){for(this._conTabsRemove(),o=i.tabs.length,u=0;u<o;u++)t.isNullOrUndefined(this._mobileContents[r-1])?(this._mobileContents[r-1]={tab:i.tabs[u].text,mobileContent:[]},this._mobileToolbar[r-1]={tab:i.tabs[u].text,Content:[]}):(this._mobileContents.splice(r-1,0,{tab:i.tabs[u].text,mobileContent:[]}),this._mobileToolbar.splice(r-1,0,{tab:i.tabs[u].text,Content:[]})),this._tabObj.addItem("#"+this._id+"_"+i.tabs[u].id,i.tabs[u].text,r),s=t.buildTag("li",i.tabs[u].text).addClass("e-responsivetabli e-resContextual"),h=n.isEmptyObject(this.model.applicationTab.menuSettings)&&t.isNullOrUndefined(this.model.applicationTab.menuItemID)&&t.isNullOrUndefined(this.model.applicationTab.backstageSettings.pages[0].id),s.insertBefore(this.element.find(".e-rescontent").children(".e-responsivetabli ").eq(r-(h?0:1))),this._responsiveTabText=this.element.find(".e-responsivetabheader li"),this._tabUl.find(".e-ribresmenu").nextAll("li.e-select").length>0&&(c=this._tabUl.find(".e-select,.e-active").not(".e-apptab,.e-tab"),c.insertBefore(this._tabUl.find(".e-ribresmenu"))),this._tabUl.find(".e-select,.e-active").eq(r).addClass("e-contextualtabset"),this._tabUl.find(".e-select > a ,.e-active > a ").eq(r).addClass("e-link"),++r;for(this.model.contextualTabs[0].tabs.length>0?this.model.contextualTabs.splice(r-1,0,i):this.model.contextualTabs[0]=i,this._contextualTabs(),o=i.tabs.length,u=0;u<o;u++)for(f=i.tabs[u],l=f.groups.length,e=0;e<l;e++)if(!t.isNullOrUndefined(f.groups[e])){var a=t.isNullOrUndefined(f.groups[e].id)?t.isNullOrUndefined(f.groups[e].text)?f.groups[e].text:f.groups[e].text.replace(/\s/g,""):f.groups[e].id.replace(/\s/g,""),y=this._createControls(e,f,a),p=this._addControlsToGroup(e,f,a,y),w=this.element.find("#"+this._id+"_"+f.id);w.append(p).addClass("e-parentdiv")}this._setTabContentHeight();this.element.find(".e-content.e-content-item").css("box-sizing","content-box");this._ribbonTabs=this._tabUl.find(".e-link").parent();this._tabContents=this.element.children().attr("role","tablist").not(".e-header,.e-disable,.e-resizediv,.e-tooltipdiv,.e-ribbonbackstagepage");this._applicationTab.click(n.proxy(this._onApplicationTabClick,this))}},setTabText:function(t,i){var r=this;this._tabText.filter(function(u){var f=n(this).text();return f===t&&(r.model.tabs[u-1].text=i),f===t}).text(i);this._phoneMode&&(this._responsiveTabText.filter(function(){return n(this).text()===t}).text(i),this.element.find(".e-ribresmenu .e-reslink").text(this._responsiveTabText.filter(".e-resactive").text()))},getTabText:function(n){this._tabText=this.element.find(".e-link");return this._tabText.eq(n).text()},updateGroup:function(i,r,u){var tt,e,y,p,c,l,a,nt,w,b,k,d,v,g,f,s,h,o;if(tt=this._tabUl.find(".e-select,.e-active").eq(i),n(tt).hasClass("e-tab")){for(e=this.model.tabs[i-1].groups,w=e.length,f=0;f<w;f++)if(!t.isNullOrUndefined(e[f].content))for(b=e[f].content.length,s=0;s<b;s++)for(k=e[f].content[s].groups.length,h=0;h<k;h++)e[f].content[s].groups[h].id==r&&(o=e[f].content[s].groups[h],t.isNullOrUndefined(u.buttonSettings)||(n.extend(o.buttonSettings,u.buttonSettings),n(u).removeProp("buttonSettings")),t.isNullOrUndefined(u.dropdownSettings)||(n.extend(o.dropdownSettings,u.dropdownSettings),n(u).removeProp("dropdownSettings")),t.isNullOrUndefined(u.toggleButtonSettings)||(n.extend(o.toggleButtonSettings,u.toggleButtonSettings),n(u).removeProp("toggleButtonSettings")),t.isNullOrUndefined(u.splitButtonSettings)||(n.extend(o.splitButtonSettings,u.splitButtonSettings),n(u).removeProp("splitButtonSettings")),t.isNullOrUndefined(u.customToolTip)||(n.extend(o.customToolTip,u.customToolTip),n(u).removeProp("customToolTip")),n.extend(o,u),l=t.isNullOrUndefined(e[f].id)?t.isNullOrUndefined(e[f].text)?e[f].text:e[f].text.replace(/\s/g,""):e[f].id.replace(/\s/g,""),c=this.model.tabs[i-1],p=this._createControls(f,c,l,i),a="#"+this._id+"_"+c.id+"_"+l+"_"+r,y=this.element.find(a),d=p.find(a),y.replaceWith(d))}else for(nt=this.model.tabs.length,v=0;v<this.model.contextualTabs.length;v++)for(g=0;g<this.model.contextualTabs[v].tabs.length;g++)if(++nt,nt===i)for(e=this.model.contextualTabs[v].tabs[g].groups,w=e.length,f=0;f<w;f++)if(!t.isNullOrUndefined(e[f].content))for(b=e[f].content.length,s=0;s<b;s++)for(k=e[f].content[s].groups.length,h=0;h<k;h++)e[f].content[s].groups[h].id==r&&(o=e[f].content[s].groups[h],t.isNullOrUndefined(u.buttonSettings)||(n.extend(o.buttonSettings,u.buttonSettings),n(u).removeProp("buttonSettings")),t.isNullOrUndefined(u.dropdownSettings)||(n.extend(o.dropdownSettings,u.dropdownSettings),n(u).removeProp("dropdownSettings")),t.isNullOrUndefined(u.toggleButtonSettings)||(n.extend(o.toggleButtonSettings,u.toggleButtonSettings),n(u).removeProp("toggleButtonSettings")),t.isNullOrUndefined(u.splitButtonSettings)||(n.extend(o.splitButtonSettings,u.splitButtonSettings),n(u).removeProp("splitButtonSettings")),t.isNullOrUndefined(u.customToolTip)||(n.extend(o.customToolTip,u.customToolTip),n(u).removeProp("customToolTip")),n.extend(o,u),l=t.isNullOrUndefined(e[f].id)?t.isNullOrUndefined(e[f].text)?e[f].text:e[f].text.replace(/\s/g,""):e[f].id.replace(/\s/g,""),c=this.model.contextualTabs[0].tabs[0],p=this._createControls(f,c,l,i),a="#"+this._id+"_"+c.id+"_"+l+"_"+r,y=this.element.find(a),d=p.find(a),y.replaceWith(d))},isVisible:function(t){var i=this._tabText.filter(function(){return n(this).text()===t}).parent();return i.is(":visible")},isEnable:function(t){for(var r=!0,u=this._tabText.filter(function(){return n(this).text()===t}).parent().index(),f=this.model.disabledItemIndex.length,i=0;i<f;i++)this.model.disabledItemIndex[i]==u&&(r=!1);return r},_renderTab:function(){var r=t.buildTag("ul"),u,f,c=this.model.applicationTab.type,s,h,i,e,o=this.model.tabs,l=o.length,a,v;if(c!=null&&(f=t.buildTag("a","",{},{href:"#"+this._id+"_"+c.replace(/\s/g,"")})),u=t.buildTag("li.e-apptab",f),r.append(u),l>=1&&!t.isNullOrUndefined(o[0].id)&&!t.isNullOrUndefined(o[0].text))for(i=0;i<l;i++)s=o[i],f=t.buildTag("a",s.text,{},{href:"#"+this._id+"_"+s.id}),u=t.buildTag("li",f).addClass("e-tab"),r.append(u);if(this.model.contextualTabs)for(a=this.model.contextualTabs.length,i=0;i<a;i++)if(this.model.contextualTabs[i].tabs)for(v=this.model.contextualTabs[i].tabs.length,e=0;e<v;e++)h=this.model.contextualTabs[i].tabs[e],f=t.buildTag("a",h.text,{},{href:"#"+this._id+"_"+h.id}),u=t.buildTag("li",f).addClass("e-contextualtabset").css("border-bottom-color",this.model.contextualTabs[i].backgroundColor),r.append(u);r.append(t.buildTag("li.e-ribresmenu").append(t.buildTag("span.e-icon").addClass("e-ribdownarrow")).prepend(t.buildTag("a#"+this._id+"_responsivelink").addClass("e-reslink")).click(n.proxy(this._onResponsiveHeaderClick,this)));this.model.showQAT&&r.append(t.buildTag("li#"+this._id+"_responsiveqat").addClass("e-responsiveqat").click(n.proxy(this._onQatClick,this)));this.element.append(r);this.element.ejTab({width:this.model.width,allowKeyboardNavigation:!1,disabledItemIndex:this.model.disabledItemIndex,enabledItemIndex:this.model.enabledItemIndex,enableTabScroll:!1,itemActive:n.proxy(this._onTabSelect,this),beforeActive:n.proxy(this._onTabClick,this),create:n.proxy(this._create,this),selectedItemIndex:this.model.selectedItemIndex,enableRTL:this.model.enableRTL,cssClass:this.model.cssClass})},_createControls:function(r,u,f,e){var g,yt,p,st,o={},v,h,b,tt,y,it,ti,ii,fi,c,nt,a,ht,ct,ft,et,kt,d,rt,k,pt,ei,oi,si,dt,wt,l,lt,s,ot,gt,ut,at,bt,ui,w,vt;if(f=t.isNullOrUndefined(f)?f:f.replace(/\&/g,""),st=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_content").addClass("e-ribGroupContent"),u.groups[r].type==="custom")u.groups[r].contentID?(yt=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+u.groups[r].type).addClass("e-innerdiv"),n("#"+u.groups[r].contentID).addClass("e-rbncustomelement").appendTo(yt).show()):yt=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+u.groups[r].type,u.groups[r].customContent).addClass("e-innerdiv"),r===0&&st.append(t.buildTag("div.e-ribupdivarrow").append(t.buildTag("span.e-icon").addClass("e-groupresponsive e-ribdownarrow"))).addClass("e-resgroupheader"),st.append(yt);else if(u.groups[r].content)for(r===0&&st.append(t.buildTag("div.e-ribupdivarrow").append(t.buildTag("span.e-icon").addClass("e-groupresponsive e-ribdownarrow"))).addClass("e-resgroupheader"),ti=u.groups[r].content.length,w=0;w<ti;w++)if(u.groups[r].content[w]!=i)for(c=u.groups[r].content[w].groups,nt=u.groups[r].content[w].defaults,u.groups[r].content.length==1&&(fi=!0),t.isNullOrUndefined(u.groups[r].alignType)&&(u.groups[r].alignType=t.Ribbon.AlignType.Rows),u.groups[r].alignType===t.Ribbon.AlignType.Columns?g=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+(w+1)).addClass("e-innerdiv"):u.groups[r].alignType===t.Ribbon.AlignType.Rows&&(g=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+(w+1)).addClass("e-innerdivrow"),++s),ii=c.length,s=0;s<ii;s++)if(!t.isNullOrUndefined(c[s])){t.isNullOrUndefined(nt)||t.isNullOrUndefined(nt.type)||(c[s].type=nt.type);t.isNullOrUndefined(c[s].type)&&(c[s].type=t.Ribbon.Type.Button);t.isNullOrUndefined(c[s].isBig)&&!t.isNullOrUndefined(nt)&&(c[s].isBig=nt.isBig);a=this.model.buttonDefaults;t.isNullOrUndefined(c[s].height)&&(t.isNullOrUndefined(c[s].buttonSettings)||t.isNullOrUndefined(c[s].buttonSettings.height)?t.isNullOrUndefined(nt)||t.isNullOrUndefined(nt.height)?t.isNullOrUndefined(a)||t.isNullOrUndefined(a.height)||(ht=a.height):ht=nt.height:ht=c[s].buttonSettings.height,c[s].height=ht,ht=null);t.isNullOrUndefined(c[s].width)&&(t.isNullOrUndefined(c[s].buttonSettings)||t.isNullOrUndefined(c[s].buttonSettings.width)?t.isNullOrUndefined(nt)||t.isNullOrUndefined(nt.width)?t.isNullOrUndefined(a)||t.isNullOrUndefined(a.width)||(ct=a.width):ct=nt.width:ct=c[s].buttonSettings.width,c[s].width=ct,ct=null);t.isNullOrUndefined(c[s].buttonSettings)||(ft=c[s].buttonSettings,!t.isNullOrUndefined(ft.enabled)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.enabled)||(ft.enabled=a.enabled),!t.isNullOrUndefined(ft.enableRTL)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.enableRTL)||(ft.enableRTL=a.enableRTL),!t.isNullOrUndefined(ft.cssClass)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.cssClass)||(ft.cssClass=a.cssClass),!t.isNullOrUndefined(ft.showRoundedCorner)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.showRoundedCorner)||(ft.showRoundedCorner=a.showRoundedCorner));t.isNullOrUndefined(c[s].splitButtonSettings)||(et=c[s].splitButtonSettings,!t.isNullOrUndefined(et.enabled)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.enabled)||(et.enabled=a.enabled),!t.isNullOrUndefined(et.enableRTL)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.enableRTL)||(et.enableRTL=a.enableRTL),!t.isNullOrUndefined(et.cssClass)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.cssClass)||(et.cssClass=a.cssClass),!t.isNullOrUndefined(et.showRoundedCorner)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.showRoundedCorner)||(et.showRoundedCorner=a.showRoundedCorner));t.isNullOrUndefined(c[s].dropdownSettings)||(!t.isNullOrUndefined(c[s].dropdownSettings.enabled)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.enabled)||(c[s].dropdownSettings.enabled=a.enabled),!t.isNullOrUndefined(c[s].dropdownSettings.enableRTL)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.enableRTL)||(c[s].dropdownSettings.enableRTL=a.enableRTL),!t.isNullOrUndefined(c[s].dropdownSettings.cssClass)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.cssClass)||(c[s].dropdownSettings.cssClass=a.cssClass),!t.isNullOrUndefined(c[s].dropdownSettings.showRoundedCorner)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.showRoundedCorner)||(c[s].dropdownSettings.showRoundedCorner=a.showRoundedCorner));t.isNullOrUndefined(c[s].toggleButtonSettings)||(!t.isNullOrUndefined(c[s].toggleButtonSettings.enabled)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.enabled)||(c[s].toggleButtonSettings.enabled=a.enabled),!t.isNullOrUndefined(c[s].toggleButtonSettings.enableRTL)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.enableRTL)||(c[s].toggleButtonSettings.enableRTL=a.enableRTL),!t.isNullOrUndefined(c[s].toggleButtonSettings.cssClass)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.cssClass)||(c[s].toggleButtonSettings.cssClass=a.cssClass),!t.isNullOrUndefined(c[s].toggleButtonSettings.showRoundedCorner)||t.isNullOrUndefined(a)||t.isNullOrUndefined(a.showRoundedCorner)||(c[s].toggleButtonSettings.showRoundedCorner=a.showRoundedCorner));t.isNullOrUndefined(c[s].quickAccessMode)&&(c[s].quickAccessMode="none");switch(c[s].type){case t.Ribbon.Type.Custom:tt=c[s];u.groups[r].alignType===t.Ribbon.AlignType.Columns?p=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+tt.contentID).addClass("e-controlpadding"):u.groups[r].alignType===t.Ribbon.AlignType.Rows&&(p=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+tt.contentID).addClass("e-innerdivchild e-controlpadding"));n.isEmptyObject(tt.customToolTip)?t.isNullOrUndefined(tt.toolTip)||p.attr("title",tt.toolTip):p.mouseover({value:tt,ribbonId:this._id},n.proxy(this._tooltip,this)).mouseout({ribbonId:this._id},n.proxy(this._toolTipOut,this));n("#"+tt.contentID).addClass("e-rbncustomelement").appendTo(p);t.isNullOrUndefined(c[s])||(t.isNullOrUndefined(c[s].height)||p.children().height(c[s].height),t.isNullOrUndefined(c[s].width)||p.children().width(c[s].width));t.isNullOrUndefined(tt.isBig)||(tt.isBig?p.children().addClass("e-big"):p.children().addClass("e-small"));g.append(p);c[s].enableSeparator&&u.groups[r].alignType===t.Ribbon.AlignType.Rows&&(it=t.buildTag("div#"+this._id+"_separator_"+tt.toolTip).addClass("e-separatordivrow"),g.append(it));break;case t.Ribbon.Type.SplitButton:v=c[s];this._splitButtonControl=t.buildTag("button#"+this._id+"_"+v.id,v.text,{},{type:"button"}).addClass(v.cssClass);u.groups[r].alignType===t.Ribbon.AlignType.Columns?p=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+v.id,this._splitButtonControl).addClass("e-controlpadding"):u.groups[r].alignType===t.Ribbon.AlignType.Rows&&(p=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+v.id,this._splitButtonControl).addClass("e-innerdivchild e-controlpadding"));n.isEmptyObject(v.customToolTip)?t.isNullOrUndefined(v.toolTip)||p.attr("title",v.toolTip):p.mouseover({value:v,ribbonId:this._id},n.proxy(this._tooltip,this)).mouseout({ribbonId:this._id},n.proxy(this._toolTipOut,this));o={};v.splitButtonSettings&&(o=v.splitButtonSettings);o.height=t.isNullOrUndefined(o.height)?c[s].height:o.height;o.width=t.isNullOrUndefined(o.width)?c[s].width:o.width;o.cssClass=o.cssClass?o.cssClass.concat(" e-rbn-splitbtn e-rbn-button"):"e-rbn-splitbtn e-rbn-button";o.enableRTL=this.model.enableRTL;this._splitButtonControl.ejSplitButton(o);p.find(".e-split").addClass("e-rbn-split");v.isMobileOnly&&(this._mobileSplitButton=t.buildTag("button#"+this._id+"_"+v.id+"_mobEle",v.text,{},{type:"button"}).addClass(v.cssClass),rt=t.buildTag("div#"+this._id+"_"+v.id+"_mobDiv",this._mobileSplitButton).addClass("e-mobdiv"),o.height=v.splitButtonSettings.height,o.width=v.splitButtonSettings.width,o.arrowPosition=t.ArrowPosition.Right,o.imagePosition=v.splitButtonSettings.imagePosition,o.contentType=v.splitButtonSettings.contentType,this._mobileSplitButton.ejSplitButton(o),rt.find("button").addClass("e-rbn-button"),this._mobileToolbar[e].Content.push(rt),t.isNullOrUndefined(v.text)?t.isNullOrUndefined(v.splitButtonSettings.text)||(k=v.splitButtonSettings.text):k=v.text,this._mobileContents[e].mobileContent.push({id:v.id,text:k,isMobileOnly:v.isMobileOnly}));v.quickAccessMode!="none"&&this.model.showQAT&&!this.model.enableOnDemand&&(this._splitButtonQAT=t.buildTag("button#"+this._id+"_"+v.id+"_qatEle",v.text,{},{type:"button"}).addClass(v.cssClass),d=t.buildTag("div#"+this._id+"_"+v.id+"_qatDiv",this._splitButtonQAT).addClass("e-qatooldiv"),o.height=30,o.arrowPosition=t.ArrowPosition.Right,o.contentType=t.ContentType.ImageOnly,n.isEmptyObject(v.customToolTip)?t.isNullOrUndefined(v.toolTip)||d.attr("title",v.toolTip):d.mouseover({value:v,ribbonId:this._id},n.proxy(this._tooltip,this)).mouseout({ribbonId:this._id},n.proxy(this._toolTipOut,this)),this._splitButtonQAT.ejSplitButton(o),d.find("button").addClass("e-rbn-button"),d.appendTo(this._qAccessBar),v.quickAccessMode=="menu"&&d.hide(),t.isNullOrUndefined(v.text)?t.isNullOrUndefined(v.splitButtonSettings.text)||(k=v.splitButtonSettings.text):k=v.text,this._qatControlsName.push({id:v.id,text:k,qAccess:v.quickAccessMode}));t.isNullOrUndefined(v.isBig)||(v.isBig?p.children().prop("style",!1).addClass("e-big"):p.children().prop("style",!1).addClass("e-small"));g.append(p);c[s].enableSeparator&&u.groups[r].alignType===t.Ribbon.AlignType.Rows&&(it=t.buildTag("div#"+this._id+"_separator_"+v.id).addClass("e-separatordivrow"),g.append(it));break;case t.Ribbon.Type.ToggleButton:y=c[s];this._toggleButtonControl=t.buildTag("input#"+this._id+"_"+y.id,"","",{type:"checkbox"}).addClass(y.cssClass);u.groups[r].alignType===t.Ribbon.AlignType.Columns?p=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+y.id,this._toggleButtonControl).addClass("e-controlpadding"):u.groups[r].alignType===t.Ribbon.AlignType.Rows&&(p=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+y.id,this._toggleButtonControl).addClass("e-innerdivchild e-controlpadding"));n.isEmptyObject(y.customToolTip)?t.isNullOrUndefined(y.toolTip)||p.attr("title",y.toolTip):p.mouseover({value:y,ribbonId:this._id},n.proxy(this._tooltip,this)).mouseout({ribbonId:this._id},n.proxy(this._toolTipOut,this));o={};y.toggleButtonSettings&&(o=y.toggleButtonSettings);o.height=t.isNullOrUndefined(o.height)?c[s].height:o.height;o.width=t.isNullOrUndefined(o.width)?c[s].width:o.width;kt=n("#"+this._toggleButtonControl.attr("id")+"_qatDiv button.e-togglebutton");this.model.enableOnDemand&&this.model.showQAT&&kt.is(":visible")&&kt.hasClass("e-active")&&(o.toggleState=!0);this._toggleButtonControl.ejToggleButton(o).addClass("e-ribbonbtn e-rbn-button");p.find("button").addClass("e-rbn-button");y.isMobileOnly&&(this._mobileToggleButton=t.buildTag("button#"+this._id+"_"+y.id+"_mobEle","","",{type:"checkbox"}).addClass(y.cssClass),rt=t.buildTag("div#"+this._id+"_"+y.id+"_mobDiv",this._mobileToggleButton).addClass("e-mobdiv"),o.height=y.toggleButtonSettings.height,o.width=y.toggleButtonSettings.width,o.contentType=y.toggleButtonSettings.contentType,o.imagePosition=y.toggleButtonSettings.imagePosition,this._mobileToggleButton.ejToggleButton(o).addClass("e-ribbonbtn"),rt.find("button").addClass("e-rbn-button"),this._mobileToolbar[e].Content.push(rt),t.isNullOrUndefined(y.text)?t.isNullOrUndefined(y.toggleButtonSettings.defaultText)||(k=y.toggleButtonSettings.defaultText):k=y.text,this._mobileContents[e].mobileContent.push({id:y.id,text:k,isMobileOnly:y.isMobileOnly}));y.quickAccessMode!="none"&&this.model.showQAT&&!this.model.enableOnDemand&&(this._toggleButtonQAT=t.buildTag("input#"+this._id+"_"+y.id+"_qatEle","","",{type:"checkbox"}).addClass(y.cssClass),d=t.buildTag("div#"+this._id+"_"+y.id+"_qatDiv",this._toggleButtonQAT).addClass("e-qatooldiv"),o.height=30,o.contentType=t.ContentType.ImageOnly,n.isEmptyObject(y.customToolTip)?t.isNullOrUndefined(y.toolTip)||d.attr("title",y.toolTip):d.mouseover({value:y,ribbonId:this._id},n.proxy(this._tooltip,this)).mouseout({ribbonId:this._id},n.proxy(this._toolTipOut,this)),this._toggleButtonQAT.ejToggleButton(o).addClass("e-ribbonbtn"),d.find("button").addClass("e-rbn-button"),d.appendTo(this._qAccessBar),y.quickAccessMode=="menu"&&d.hide(),t.isNullOrUndefined(y.text)?t.isNullOrUndefined(y.toggleButtonSettings.defaultText)||(k=y.toggleButtonSettings.defaultText):k=y.text,this._qatControlsName.push({id:y.id,text:k,qAccess:y.quickAccessMode}));t.isNullOrUndefined(y.isBig)||(y.isBig?p.find(".e-togglebutton.e-tbtn.e-btn").addClass("e-big"):p.find(".e-togglebutton.e-tbtn.e-btn").addClass("e-small"));g.append(p);c[s].enableSeparator&&u.groups[r].alignType===t.Ribbon.AlignType.Rows&&(it=t.buildTag("div#"+this._id+"_separator_"+y.id).addClass("e-separatordivrow"),g.append(it));break;case t.Ribbon.Type.Button:h=c[s];this._buttonControl=h.buttonSettings?h.buttonSettings.type?t.buildTag("button#"+this._id+"_"+h.id,h.text,{},{type:h.buttonSettings.type}).addClass(h.cssClass):t.buildTag("button#"+this._id+"_"+h.id,h.text).addClass(h.cssClass):t.buildTag("button#"+this._id+"_"+h.id,h.text).addClass(h.cssClass);u.groups[r].alignType===t.Ribbon.AlignType.Columns?p=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+h.id,this._buttonControl).addClass("e-controlpadding"):u.groups[r].alignType===t.Ribbon.AlignType.Rows&&(p=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+h.id,this._buttonControl).addClass("e-innerdivchild e-controlpadding"));n.isEmptyObject(h.customToolTip)?t.isNullOrUndefined(h.toolTip)||p.attr("title",h.toolTip):p.mouseover({value:h,ribbonId:this._id},n.proxy(this._tooltip,this)).mouseout({ribbonId:this._id},n.proxy(this._toolTipOut,this));o={};h.buttonSettings&&(o=h.buttonSettings);o.height=t.isNullOrUndefined(o.height)?c[s].height:o.height;o.width=t.isNullOrUndefined(o.width)?c[s].width:o.width;this._buttonControl.ejButton(o).addClass("e-ribbonbtn  e-rbn-button");h.isMobileOnly&&(this._mobileButton=h.buttonSettings?h.buttonSettings.type?t.buildTag("button#"+this._id+"_"+h.id+"_mobEle",h.text,{},{type:h.buttonSettings.type}).addClass(h.cssClass):t.buildTag("button#"+this._id+"_"+h.id+"_mobEle",h.text).addClass(h.cssClass):t.buildTag("button#"+this._id+"_"+h.id+"_mobEle",h.text).addClass(h.cssClass),rt=t.buildTag("div#"+this._id+"_"+h.id+"_mobDiv",this._mobileButton).addClass("e-mobdiv"),this._mobileButton.ejButton(o).addClass("e-ribbon-resbtn  e-rbn-button"),this._mobileToolbar[e].Content.push(rt),t.isNullOrUndefined(h.text)?t.isNullOrUndefined(h.buttonSettings.text)||(k=h.buttonSettings.text):k=h.text,this._mobileContents[e].mobileContent.push({id:h.id,text:k,isMobileOnly:h.isMobileOnly}));h.quickAccessMode!="none"&&this.model.showQAT&&!this.model.enableOnDemand&&(this._buttonQAT=h.buttonSettings?h.buttonSettings.type?t.buildTag("button#"+this._id+"_"+h.id+"_qatEle",h.text,{},{type:h.buttonSettings.type}).addClass(h.cssClass):t.buildTag("button#"+this._id+"_"+h.id+"_qatEle",h.text).addClass(h.cssClass):t.buildTag("button#"+this._id+"_"+h.id+"_qatEle",h.text).addClass(h.cssClass),d=t.buildTag("div#"+this._id+"_"+h.id+"_qatDiv",this._buttonQAT).addClass("e-qatooldiv"),o.height=30,o.contentType=t.ContentType.ImageOnly,n.isEmptyObject(h.customToolTip)?t.isNullOrUndefined(h.toolTip)||d.attr("title",h.toolTip):d.mouseover({value:h,ribbonId:this._id},n.proxy(this._tooltip,this)).mouseout({ribbonId:this._id},n.proxy(this._toolTipOut,this)),this._buttonQAT.ejButton(o).addClass("e-ribbonbtn e-rbn-button"),d.appendTo(this._qAccessBar),h.quickAccessMode=="menu"&&d.hide(),t.isNullOrUndefined(h.text)?t.isNullOrUndefined(h.buttonSettings.text)||(k=h.buttonSettings.text):k=h.text,this._qatControlsName.push({id:h.id,text:k,qAccess:h.quickAccessMode}));t.isNullOrUndefined(h.isBig)||(h.isBig?p.children().addClass("e-big"):p.children().addClass("e-small"));g.append(p);c[s].enableSeparator&&u.groups[r].alignType===t.Ribbon.AlignType.Rows&&(it=t.buildTag("div#"+this._id+"_separator_"+h.id).addClass("e-separatordivrow"),g.append(it));break;case t.Ribbon.Type.DropDownList:b=c[s];this._dropdownControl=t.buildTag("input#"+this._id+"_"+b.id,{type:"text"}).addClass(b.cssClass);b.dropdownSettings&&(u.groups[r].alignType===t.Ribbon.AlignType.Columns?p=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+b.id,this._dropdownControl).append(n("#"+b.dropdownSettings.targetID)).addClass("e-controlpadding"):u.groups[r].alignType===t.Ribbon.AlignType.Rows&&(p=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+b.id,this._dropdownControl).append(n("#"+b.dropdownSettings.targetID)).addClass("e-innerdivchild e-controlpadding")),n.isEmptyObject(b.customToolTip)?t.isNullOrUndefined(b.toolTip)||p.attr("title",b.toolTip):p.mouseover({value:b,ribbonId:this._id},n.proxy(this._tooltip,this)).mouseout({ribbonId:this._id},n.proxy(this._toolTipOut,this)));o={};b.dropdownSettings&&(o=b.dropdownSettings);o.height=t.isNullOrUndefined(o.height)?c[s].height:o.height;o.width=t.isNullOrUndefined(o.width)?c[s].width:o.width;o.cssClass=o.cssClass?o.cssClass.concat(" e-rbn-ddl"):"e-rbn-ddl";b.isMobileOnly&&(this._mobileDropdown=t.buildTag("input#"+this._id+"_"+b.id+"_mobEle","","",{type:"text"}).addClass(b.cssClass),rt=t.buildTag("div#"+this._id+"_"+b.id+"_mobDiv",this._mobileDropdown).addClass("e-mobdiv"),this._mobileDropdown.ejDropDownList(o).addClass("e-ribbonbtn"),this._mobileToolbar[e].Content.push(rt),t.isNullOrUndefined(b.text)?t.isNullOrUndefined(b.dropdownSettings.defaultText)||(k=b.dropdownSettings.defaultText):k=b.text,this._mobileContents[e].mobileContent.push({id:b.id,text:k,isMobileOnly:b.isMobileOnly}));o.enableRTL=this.model.enableRTL;this._dropdownControl.ejDropDownList(o);g.append(p);c[s].enableSeparator&&u.groups[r].alignType===t.Ribbon.AlignType.Rows&&(it=t.buildTag("div#"+this._id+"_separator_"+b.id).addClass("e-separatordivrow"),g.append(it));break;case t.Ribbon.Type.Gallery:for(l=c[s],this._galleryControl=t.buildTag("div#"+this._id+"_"+l.id+"_gallerycontrol").addClass("e-ribbongallerycontrol"),this._gallery=t.buildTag("div#"+this._id+"_"+l.id).addClass("e-ribbongallery"),lt=t.buildTag("div#"+this._id+"_"+l.id+"_ContentDiv").addClass("e-gallerycontent").height(65).width(l.columns*l.itemWidth).click({galleryId:this._gallery.attr("id")},n.proxy(this._onGalContentClick,this)),wt=Math.floor(l.galleryItems.length/l.columns),l.galleryItems.length%l.columns>0&&++wt,s=1;s<=wt;s++){for(ot=t.buildTag("div#"+this._id+"_"+l.id+"_galleryrow_"+s).addClass("e-galleryrow").click({model:l},n.proxy(this._onGalleryItemClick,this)),s===1?ot.addClass("e-gryfirstrow"):s===wt&&ot.addClass("e-grylastrow"),gt=1;gt<=l.columns;gt++)ut=lt.find(".e-galleryrow").children().length+ot.children().length,t.isNullOrUndefined(l.galleryItems[ut])||(at=t.buildTag("button#"+l.id+"_galleryItem_"+(ut+1),l.galleryItems[ut].text,{},{type:"button"}).addClass("e-gallerybtn  e-rbn-button"),n.isEmptyObject(l.galleryItems[ut].customToolTip)?t.isNullOrUndefined(l.galleryItems[ut].toolTip)||at.attr("title",l.galleryItems[ut].toolTip):at.mouseover({value:l.galleryItems[ut],ribbonId:this._id},n.proxy(this._tooltip,this)).mouseout({ribbonId:this._id},n.proxy(this._toolTipOut,this)),o={},l.galleryItems[ut].buttonSettings&&(o=l.galleryItems[ut].buttonSettings),o.height=l.itemHeight<=65?l.itemHeight:65,o.width=l.itemWidth,at.ejButton(o),at.appendTo(ot));dt=l.itemHeight>65?1:lt.height()/l.itemHeight;s>dt&&ot.hide();ot.appendTo(lt)}this._gallery.append(lt);var pt=t.buildTag("div#"+this._id+"_"+l.id+"_PagesDiv").addClass("e-gallerymovediv"),ei=t.buildTag("div#"+this._id+"_"+l.id+"_prevPage","<span class='e-icon e-galleryup e-gallerymoveicon'><\/span>").click({galleryId:this._gallery.attr("id"),rowCnt:dt},n.proxy(this._onGalMoveUpClick,this)).addClass("e-moveupdiv e-disablegrymovebtn"),oi=t.buildTag("div#"+this._id+"_"+l.id+"_nextPage","<span class='e-icon e-gallerydown e-gallerymoveicon'><\/span>").click({galleryId:this._gallery.attr("id")},n.proxy(this._onGalMoveDownClick,this)).addClass("e-movedowndiv"),si=t.buildTag("div#"+this._id+"_"+l.id+"_expandGallery","<span class='e-icon e-galleryexpand e-gallerymoveicon'><\/span>").click({galleryId:this._gallery.attr("id"),columns:l.columns,expandedColumns:l.expandedColumns,itemWidth:l.itemWidth},n.proxy(this._onExpandGalleryClick,this)).addClass("e-expgallerydiv");this._responsiveGallery=l.expandedColumns;pt.append(ei);pt.append(oi);pt.append(si);this._gallery.append(pt);var ni=t.buildTag("div#"+this._id+"_"+l.id+"_ExpandContent").addClass("e-gallexpandcontent").hide(),ri=t.buildTag("div#"+this._id+"_"+l.id+"_scrollContent","<div><\/div>").addClass("e-gallscrollcontent"),hi=t.buildTag("div#"+this._id+"_"+l.id+"_ExpanderContent").addClass("e-expandercontent").click({galleryId:this._gallery.attr("id")},n.proxy(this._onExpandContentClick,this));if(ri.children().append(hi),ni.append(ri),!t.isNullOrUndefined(l.customGalleryItems)){for(bt=t.buildTag("div#"+this._id+"_"+l.id+"_ExtraContent").addClass("e-extracontent").click({model:l},n.proxy(this._onGalleryItemClick,this)),ui=l.customGalleryItems.length,w=0;w<ui;w++)t.isNullOrUndefined(l.customGalleryItems[w].customItemType)&&(l.customGalleryItems[w].customItemType=t.Ribbon.CustomItemType.Button),l.customGalleryItems[w].customItemType===t.Ribbon.CustomItemType.Button?(vt=t.buildTag("button#"+l.id+"__extraItem_"+w,l.customGalleryItems[w].text,{},{type:"button"}).addClass("e-galleryextrabtn e-rbn-button"),n.isEmptyObject(l.customGalleryItems[w].customToolTip)?t.isNullOrUndefined(l.customGalleryItems[w].toolTip)||vt.attr("title",l.customGalleryItems[w].toolTip):vt.mouseover({value:l.customGalleryItems[w],ribbonId:this._id},n.proxy(this._tooltip,this)).mouseout({ribbonId:this._id},n.proxy(this._toolTipOut,this)),o={},l.customGalleryItems[w]&&(l.customGalleryItems[w].buttonSettings&&(o=l.customGalleryItems[w].buttonSettings),vt.ejButton(o)),vt.appendTo(bt)):(o={},l.customGalleryItems[w].menuSettings&&(o=l.customGalleryItems[w].menuSettings),o.menuType=t.MenuType.NormalMenu,o.orientation=t.Orientation.Vertical,o.enableRTL=this.model.enableRTL,n("#"+l.customGalleryItems[w].menuId).ejMenu(o).addClass("e-gallerymenu"),n("#"+l.customGalleryItems[w].menuId).appendTo(bt).css("width","100%"));ni.append(bt)}this._galleryControl.append(this._gallery);this._galleryControl.append(ni);u.groups[r].alignType===t.Ribbon.AlignType.Columns?p=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+l.id,this._galleryControl).addClass("e-controlpadding"):u.groups[r].alignType===t.Ribbon.AlignType.Rows&&(p=t.buildTag("div#"+this._id+"_"+u.id+"_"+f+"_"+l.id,this._galleryControl).addClass("e-innerdivchild e-controlpadding"));g.append(p)}st.append(g)}return st},_groupContent:function(i){var h=0,f,c,a,s,y,e,r,p,u;if(t.isNullOrUndefined(i)?f=this._tabUl.find(".e-active").index()-1:(h=n(i.activeHeader).parents(".e-contextliset").length>0?n(i.activeHeader).parents(".e-contextliset").prevAll(".e-tab").eq(0).prevAll().not(".e-tab,.e-apptab").length:n(i.activeHeader).prevAll().not(".e-tab,.e-apptab").length,f=i.activeIndex-(h+1)),c=this.element.find(".e-content-item").eq(f+(1+h)),c.hasClass("e-rbn-ondemand")&&!(f<0)){if(a=n.extend(!0,{},this.model),s=this.model.tabs.length,l=t.isNullOrUndefined(i)?-1:n(i.activeHeader).hasClass("e-contextualtabset")?s-n(i.activeHeader).index():-1,this.model.contextualTabs&&l!=-1){var v=n(i.activeHeader).parents(".e-contextliset").attr("id"),l=parseInt(v[v.length-1]),o=this.model.contextualTabs[l];if(o.tabs)for(y=o.tabs.length,e=0;e<y;e++)!t.isNullOrUndefined(o.tabs[e].groups)&&o.tabs[e].groups.length>0&&(this.model.tabs[s]=o.tabs[e],f=s)}if(r=this.model.tabs[f],this._mobileContents[this._tabObj.model.selectedItemIndex-1]={tab:r.text,mobileContent:[]},this._mobileToolbar[this._tabObj.model.selectedItemIndex-1]={tab:r.text,Content:[]},t.isNullOrUndefined(r.groups))this.element.find(".e-content.e-content-item").eq(f+1).addClass("e-empty-content");else for(p=r.groups.length,u=0;u<p;u++)if(!t.isNullOrUndefined(r.groups[u])){var w=t.isNullOrUndefined(r.groups[u].id)?t.isNullOrUndefined(r.groups[u].text)?r.groups[u].text:r.groups[u].text.replace(/\s/g,""):r.groups[u].id.replace(/\s/g,""),b=this._createControls(u,r,w,f),k=this._addControlsToGroup(u,r,w,b),d=this.element.find("#"+this._id+"_"+r.id);d.append(k).addClass("e-parentdiv")}c.removeClass("e-rbn-ondemand");this.model=a}},_groupingControls:function(){var s=this.model.tabs.length,f,i,a,v,y,u,r,h,w=n.extend(!0,{},this.model),p,o,c,e,l;if(this.model.contextualTabs)for(p=this.model.contextualTabs.length,o=0;o<p;o++)if(f=this.model.contextualTabs[o],f.tabs)for(c=f.tabs.length,c==0&&this.element.find(".e-content.e-content-item").eq(u+1).addClass("e-empty-content"),e=0;e<c;e++)!t.isNullOrUndefined(f.tabs[e].groups)&&f.tabs[e].groups.length>0&&(this.model.tabs[s]=f.tabs[e],++s);for(u=0;u<s;u++)if(i=this.model.tabs[u],this._mobileContents[u]={tab:i.text,mobileContent:[]},this._mobileToolbar[u]={tab:i.text,Content:[]},t.isNullOrUndefined(i.groups))this.element.find(".e-content.e-content-item").eq(u+1).addClass("e-empty-content");else for(l=i.groups.length,l==0&&this.element.find(".e-content.e-content-item").eq(u+1).addClass("e-empty-content"),r=0;r<l;r++)t.isNullOrUndefined(i.groups[r])||(h=t.isNullOrUndefined(i.groups[r].id)?t.isNullOrUndefined(i.groups[r].text)?i.groups[r].text:i.groups[r].text.replace(/\s/g,""):i.groups[r].id.replace(/\s/g,""),v=this._createControls(r,i,h,u),a=this._addControlsToGroup(r,i,h,v),y=this.element.find("#"+this._id+"_"+i.id),y.append(a).addClass("e-parentdiv"));this._setTabContentHeight();this.model=w},_addControlsToGroup:function(i,r,u,f){var s=t.buildTag("div.e-contentbottom"),h=!1,e,o,c=t.buildTag("div.e-captionarea",r.groups[i].text);return s.append(c),r.groups[i].enableGroupExpander&&(o=t.buildTag("div#"+this._id+"_"+u+"_e-groupexpander.e-icon e-expander e-groupexpander"),s.append(o),t.isNullOrUndefined(r.groups[i].groupExpanderSettings)||(t.isNullOrUndefined(r.groups[i].groupExpanderSettings.toolTip)||o.attr("title",r.groups[i].groupExpanderSettings.toolTip),n.isEmptyObject(r.groups[i].groupExpanderSettings.customToolTip)||o.mouseover({value:r.groups[i].groupExpanderSettings,ribbonId:this._id},n.proxy(this._tooltip,this)).mouseout({ribbonId:this._id},n.proxy(this._toolTipOut,this)))),e=t.buildTag("div#"+this._id+"_"+r.id+"_"+u).addClass("e-groupdiv").click(n.proxy(this._onGroupClick,this)),t.isNullOrUndefined(r.groups[i].content)||r.groups[i].content.length==1&&(h=!0),r.groups[i].width&&e.width(r.groups[i].width),e.append(f),h&&(e.addClass("e-centeralign"),h==!1),e.append(s),e},_tabExpandCollapse:function(t){var i,r,u;(t.type=="touchend"&&t.preventDefault(),i=t.target.tagName=="div"?t.target:t.target.parentNode,u=i.className=="e-expanded"?"collapse":"expand",r={target:n(i),type:t.type,action:u,cancel:!1},this._trigger("toggleButtonClick",r),r.cancel)||(this.element.find(".e-rarrowup-2x").length>0?this.collapse():(this._removeRibbonPin(),this.model.showQAT&&this._qAccessBar.show(),this._trigger("pinState",{action:"pin"})),this._clickValue="click")},_responsiveback:function(){this.element.find(".e-resizebtnselect").removeClass("e-active e-resizebtnselect");this.element.find(".e-responsivebackstagecontent").is(":visible")&&this.element.find(".e-responsivebackstagecontent").hide();!this.element.find(".e-responsivecontent").hasClass("e-resshow")||this.element.find(".e-responsivecontent").find(".e-split.e-active").length>0||this._responsiveContentBack();this.element.find(".e-respcontent").is(":visible")&&(this.element.find(".e-active-content").addClass("e-responsiveheight e-rbnmobheader"),this.element.find(".e-respcontent .e-groupresponsive.e-ribuparrow").removeClass("e-ribuparrow").addClass("e-ribdownarrow"));return},_ribbonResponsive:function(){for(var f,e,u=t.buildTag("ul#"+this._id+"_responsivetabContent").addClass("e-rescontent"),r=this.model.tabs.length,o=!0,i=0;i<r;i++)u.append(t.buildTag("li",this.model.tabs[i].text).addClass("e-responsivetabli"));if(r=this.model.contextualTabs.length,r==1&&(o=this.model.contextualTabs[0].tabs.length>0?!0:!1),r!=0&&o)for(i=0;i<r;i++)u.append(t.buildTag("li",this.model.contextualTabs[i].tabs[0].text).addClass("e-responsivetabli").addClass("e-resContextual"));f=t.buildTag("div#"+this._id+"_responsivetabHeader").addClass("e-responsivetabheader").click(n.proxy(this._onResponsiveHeaderClick,this));f.append(u);this.element.append(f);this._initialRender&&this._tabUl.find("li.e-ribresmenu a").not(".e-backstagetab").text(this._tabUl.find("li.e-active a").text());this.model.applicationTab.type===t.Ribbon.ApplicationTabType.Backstage&&(this.element.prepend(t.buildTag("div#"+this._id+"_responsivebackstage").addClass("e-responsivebackstage")),e=t.buildTag("div#"+this._id+"_resbackstagecontent").addClass("e-responsivebackstagecontent").append(t.buildTag("div.e-resbackstagecontent")),e.prepend(t.buildTag("div#"+this._id+"backstagetop").addClass("e-backstagerestop").append(t.buildTag("div.e-backstagerestopcontent").append(t.buildTag("span.e-icon").addClass("e-ribleftarrow")).append(t.buildTag("div#"+this._id+"resbacstageTitle").addClass("e-backstageTitle")))),this.element.prepend(e.hide()))},_onResponsiveHeaderClick:function(t){if(n(t.target).hasClass("e-responsivetabli")){var i=n(t.target).parent("ul").children().index(t.target);this.element.find(".e-responsivetabheader").removeClass("e-resshow");this.element.find(".e-ribresmenu a").text(t.target.textContent);n(this._tabUl.find(".e-tab,.e-contextliset").find("a")[i]).trigger("click");n(t.target).parent("ul").children(".e-resactive").removeClass("e-resactive");n(t.target).addClass("e-resactive");return}if(n(t.target).parent(".e-ribresmenu").length>0){this.element.find(".e-responsivetabheader").addClass("e-resshow");return}},_ribbonResGroup:function(){var i=this._tabUl.find(".e-tab,.e-contextualtabset").index(this._tabUl.find("li.e-active")),r,l,s,u,c;i<0&&(i=this._tabUl.find(".e-tab,.e-contextualtabset").index(this._tabUl.find("li.e-collapseactive")));t.isNullOrUndefined(this.model.applicationTab)||(r=i+1);var e=this.element.find(".e-content.e-content-item"),f=this.element.find(".e-active-content"),a=e.eq(r).attr("id"),h=e.eq(r).children(".e-groupdiv").length,v=e.eq(r).children(".e-groupdiv")[h-1],o=e.eq(r).children(".e-groupdiv").eq(h-1).find(".e-resizebtn");if(i!=-1&&this.element.find(".e-content-item").eq(r).children().not(".e-expandcollapse").length>0&&this._mobileContents[i].mobileContent.length>0&&!f.find(".e-respmobcontent").length>0){for(f.prepend(t.buildTag("div#"+this._id+"_mobcontent").addClass("e-respmobcontent e-groupmobdiv").append(t.buildTag("div#"+this._id+"_mobribgroup").addClass("e-mobribgroup e-mobribgroupactive").append(t.buildTag("div.e-ribupdivarrow").addClass("e-toolbaralign").append(t.buildTag("span.e-icon").addClass("e-groupresponsive e-ribdownarrow"))))).click(n.proxy(this._onGroupClick,this)).click(n.proxy(this._onMobContentClick,this)),f.find(".e-resgroupheader").removeClass("e-resgroupheader"),l=this._mobileToolbar[i].Content.length,s=0;s<l;s++)f.find(".e-mobribgroup").append(this._mobileToolbar[i].Content[s]);f.find(".e-mobribgroup").append(f.find(".e-mobribgroup .e-ribupdivarrow"))}for(u=h-1;u>=0;u--)o=e.eq(r).children(".e-groupdiv").eq(u).find(".e-resizebtn"),o.length==0&&(u!=0||this._mobileContents[i].mobileContent.length>0)?this._createResizeBtn(r,u):u==0&&o.length>0&&!this._mobileContents[i].mobileContent.length>0&&(c=o.parent().siblings(),o.parent().remove(),c.show(),c.not(".e-contentbottom").css("width",""))},_ribbonWindowResize:function(){var o,e,s,h,w,b,k,d,it,g,f,r,l;this._phoneMode=(this.model.allowResizing||this.model.isResponsive)&&document.documentElement.clientWidth<420?!0:!1;var nt=this._tabUl.find(".e-tab,.e-contextualtabset").index(this._tabUl.find("li.e-active")),y=this.element.find(".e-ribbonpin"),u=this.element.find(".e-active-content").eq(0),a=this.element.find(".e-ribresmenu"),v,c,i,p=this.element.find(".e-respcontent"),tt;if(t.isNullOrUndefined(this.model.applicationTab)||(tt=nt+1),this.element.find(".e-ribGroupContent.e-resshow").addClass("e-reshide"),this._phoneMode){this._responsiveScrollRemove();p.is(":hidden")&&this.element.find(".e-responsivecontent").is(":hidden")&&p.removeClass("e-reshide");this._isCollapsed?this._tabUl.find(".e-active").length>0?a.find("a").text(this._tabUl.find(".e-active a").text()):(this._responsiveTabText.eq(0).addClass("e-resactive"),a.find("a").text(this._responsiveTabText.filter(".e-resactive").text())):(this._responsiveTabText.filter(".e-resactive").removeClass("e-resactive"),this._responsiveTabText.eq(n(this._ribbonTabs.filter(".e-tab ,.e-contextualtabset")).index(this._ribbonTabs.filter(".e-active"))).addClass("e-resactive"),a.find("a").text(this._responsiveTabText.filter(".e-resactive").text()));y.length>0&&y.trigger("click");u.addClass("e-responsiveheight e-rbnmobheader").addClass("e-tab_"+this._tabUl.find(".e-active a").text());u.hasClass("e-respcontent")||u.addClass("e-respcontent");o=u.find(".e-groupresponsive");(n(o).eq(0).hasClass("e-ribdownarrow")||o.hasClass("e-ribuparrow"))&&o.hasClass("e-ribuparrow")&&o.eq(0).removeClass("e-ribuparrow").addClass("e-ribdownarrow");t.isNullOrUndefined(this.model.applicationTab)||this.element.find(".e-ribresmenu").css("padding-left","12px");this._ribbonResGroup();this.element.addClass("e-responsive");this.element.find(".e-rbn-ddl.e-ddl").addClass("e-rbn-resize");c=this.element.find(".e-ribbonbackstagepage");v=this.element.find(".e-responsivebackstage");e=this.element.find(".e-header").width();this.model.enableOnDemand&&this.element.find(".e-backstagetab .e-resbackstage").length==0?(this.model.applicationTab.type===t.Ribbon.ApplicationTabType.Backstage&&this._applicationTab.addClass("e-backstagetab"),this.element.find(".e-backstagetab").append(t.buildTag("span.e-icon").addClass("e-ribbon e-resbackstage").click(n.proxy(this._onResponsiveHeaderClick,this))),this.element.find(".e-header").addClass("e-resheader")):this.model.applicationTab.type==t.Ribbon.ApplicationTabType.Backstage&&this.element.find(".e-backstagetab .e-resbackstage").length==0?(this.element.find(".e-backstagetab").append(t.buildTag("span.e-icon").addClass("e-ribbon e-resbackstage").click(n.proxy(this._onResponsiveHeaderClick,this))),this.element.find(".e-header").addClass("e-resheader")):this.model.applicationTab.type==t.Ribbon.ApplicationTabType.Menu&&this.model.applicationTab.menuItemID&&(this.element.find(".e-header").addClass("e-resheader"),this.element.find(".e-rescontent").addClass("e-rescontent-menu"));this.model.showQAT&&(this._tabUl.find("li.e-responsiveqat").append(this.element.find(".e-rbnquickaccessbar.e-rbnabove").children()),s=this.element.find(".e-ribdownarrow").not(".e-groupresponsive"),this.element.find(".e-resqatScroll").length>0&&(h=this.element.find(".e-resqatScroll"),h.data("ejScroller")._destroy(),h.parent().append(h.find("div:first").children()),h.remove()),w=e-(s.offset().left+s.outerWidth())<e-this.element.find("li.e-responsiveqat").offset().left,w&&!this.element.find(".e-resqatScroll").length>0&&(b=e-this.element.find("li.e-responsiveqat").offset().left-(e-(s.offset().left+s.outerWidth())),r=t.buildTag("div#"+this._id+"_responsiveqat").addClass("e-resqatScroll").append(t.buildTag("div")),r.find("div").append(this._tabUl.find("li.e-responsiveqat").children()),this._tabUl.find("li.e-responsiveqat").append(r),k=this.element.find(".e-resqatScroll"),k.ejScroller({width:e-this.element.find("li.e-responsiveqat").offset().left-b,height:"",scrollerSize:3,buttonSize:0})));u.find(".e-groupdiv .e-resizebtn").length>0&&!u.find(".e-groupdiv .e-groupresponsive").not(".e-ribdownarrow").length>0&&u.find(".e-groupdiv").find(".e-resizebtn").parent("div").prepend(t.buildTag("div.e-ribrightdivarrow").append(t.buildTag("span.e-icon").addClass("e-groupresponsive e-ribrightarrow")));this._tabUl.find(".e-backstagetab").length>0&&v.length==1&&(d=c.find(".e-ribbonbackstagebody .e-backstageheader"),v.append(d).addClass("e-reshide"));!this.element.find(".e-responsivecontent").length>0&&(r=t.buildTag("div#"+this._id+"_responsive").addClass("e-content e-responsivecontent").click(n.proxy(this._onResizeDivClick,this)),r.append(t.buildTag("div#"+this._id+"_resback").addClass("e-responsiveback").append(t.buildTag("span").addClass("e-groupresponsive e-icon e-ribleftarrow")).append(t.buildTag("div#"+this._id+"+rescontentback").addClass("e-restopbackcontent"))),this.element.append(r.hide()));c.find(".e-ribbonbackstagebody .e-backstagecontent").length>0&&this.element.find(".e-responsivebackstagecontent .e-resbackstagecontent").append(c.find(".e-ribbonbackstagebody .e-backstagecontent"));it=this.element.find(".e-header");g=document.documentElement.clientHeight;i=this.element.find(".e-mobribgroup.e-mobribgroupactive");this.element.find(".e-responsivebackstagecontent .e-resbackstagecontent").css({height:g-this.element.find(".e-backstagerestop").outerHeight()});i.find(".e-ribupdivarrow").removeClass("e-toolbaralign");f=i.find(".e-resptoolbarScroll");f.length>0&&(f.data("ejScroller")._destroy(),f.parent().append(f.find("div:first").children()),f.parents(".e-active-content").removeClass("e-responsiveToolbarScroll"),f.remove());this._phoneMode&&i.outerWidth()>this.element.find(".e-header").outerWidth()?(r=t.buildTag("div#"+this._id+"_responsivetoolbar").addClass("e-resptoolbarScroll").append(t.buildTag("div")),r.find("div").append(i.children()),i.append(r),l=this.element.find(".e-respmobcontent .e-resptoolbarScroll"),l.find(".e-toolbaralign").removeClass("e-toolbaralign"),l.ejScroller({width:this.element.find(".e-header").width(),height:"",scrollerSize:4,buttonSize:0}),l.parents(".e-active-content").addClass("e-responsiveToolbarScroll")):i.find(".e-resptoolbarScroll").length>0&&!i.find("e-responsiveToolbarScroll").length>0?i.parents(".e-active-content").addClass("e-responsiveToolbarScroll"):i.find(".e-ribupdivarrow").addClass("e-toolbaralign");this.goToMainContent();return}this.element.hasClass("e-responsive")&&(this.element.removeClass("e-responsive"),this._ribbonRerender());(this.model.allowResizing||this.model.isResponsive)&&(this.element.find(".e-ribbonpin").length>0&&this.element.find(".e-active-content").eq(0).is(":visible")?this.collapse():this._ribbonResize());this.element.find(".e-ribbonpin").length>0&&this._tabContents.width(this.element.width())},_ribbonResize:function(){var ni,e,r,a,v,ut,i,vt,p,w,yt,pt=0,c,b=[],d,o,ft,et,wt,f,bt=this.element.find(".e-active-content").eq(0),g,u,k,ot,st=this.element.find(".e-ribbonpin"),nt,kt,tt,it,dt,gt,s,l,y,h,ct,rt,lt,at;if(u=this._tabUl.find("li.e-select:visible,li.e-active:visible"),k=this.element.find(".e-resizediv"),et=this.element.find(".e-expandcollapse"),o=this.element.find(".e-header"),st.length>0&&(this._removeRibbonPin(),et.children().addClass("e-ribbonpin")),o.is(":hidden")&&bt.is(":hidden")&&this._resizeWidth<=this.element.width()+this.element.offset().left&&(o.show(),this._qAccessBar.show(),(this.element.find(".e-expandcollapse").children().hasClass("e-expanded")||o.find(".e-active").length>0)&&(st.length<=0?(bt.show(),et.children().addClass("e-expanded").removeClass("e-collapsed")):st.length>0&&this.collapse())),g=o.find("li.e-select,li.e-active"),ut=g.length,o.is(":visible")){for(l=ut-1;l>0;l--)for(i=g.eq(l).children(),vt=i.text().length-1,y=vt;y>1;y--)u.length>0&&(a=u.last().offset().left,v=u.last().width(),(this.element.width()+this.element.offset().left-30-(a+v)<=0||this.model.enableRTL&&u.last().offset().left<this.element.offset().left)&&(i.hasClass("e-resizeHead")?i.text(i.text().slice(0,-1)):i.text(i.text().slice(0,-1)).addClass("e-resizeHead")));ot=o.find(".e-resizeHead");ut-1===ot.length&&ot.eq(0).text().length<=2&&u.length>0&&(a=u.last().offset().left,v=u.last().width(),this.element.width()+this.element.offset().left-30-(a+v)<=0&&(this._resizeWidth=this.element.width()+this.element.offset().left))}if(r=this._tabObj.model.selectedItemIndex,f=this.element.find(".e-content.e-content-item"),ni=f.eq(r).attr("id"),p=f.eq(r).children(".e-groupdiv").length,e=f.eq(r).children(".e-groupdiv")[p-1],ft=f.eq(r).children().eq(p-1).find(".e-resizebtn"),!t.isNullOrUndefined(e)){for(s=p-1;s>=0;s--)ft=f.eq(r).children().eq(s).find(".e-resizebtn"),(this.element.width()+this.element.offset().left-25-(e.offsetWidth+n(e).offset().left)<=0||this.model.enableRTL&&n(window).width()-this.element.offset().left+25-(n(window).width()-n(e).offset().left+e.offsetWidth)<=0)&&ft.length===0&&this._createResizeBtn(r,s);this.element.width()+this.element.offset().left-25-(e.offsetWidth+n(e).offset().left)<=0||this.model.enableRTL&&n(window).width()-this.element.offset().left+25-(n(window).width()-n(e).offset().left+e.offsetWidth)<=0?p===f.eq(r).find(".e-resizebtn").length&&(this._resizeWidth=this.element.width()+this.element.offset().left,this.element.addClass("e-grpdivhide"),this.element.find(".e-expandcollapse").css("display","none")):(this.element.removeClass("e-grpdivhide"),this.element.find(".e-expandcollapse").css("display","block"))}for(this._qatResize(),k.is(":visible")&&(nt=k.children().eq(0).attr("id"),nt="#"+nt.replace("_content",""),k.children().hide().appendTo(nt),k.hide()),kt=this.model.tabs.length,tt=0;tt<kt;tt++)it=this.model.tabs[tt].text,b.push(it);if(this.model.contextualTabs)for(dt=this.model.contextualTabs.length,h=0;h<dt;h++)if(this.model.contextualTabs[h].tabs)for(gt=this.model.contextualTabs[h].tabs.length,s=0;s<gt;s++)it=this.model.contextualTabs[h].tabs[s].text,b.push(it);if(wt=o.find(".e-resizeHead").length,o.is(":visible"))for(l=0;l<wt;l++)if(i=o.find(".e-resizeHead").eq(0),i.hasClass("e-resizeHead")){var ht=g.find(".e-link").not(".e-resizeHead").length-1,ti=i.text().length,ii=b[ht].length;for(y=ti;y<=ii;y++)u.length>0&&(a=u.last().offset().left,v=u.last().width()+15,(this.element.width()+this.element.offset().left-30>=a+v&&!this.model.enableRTL||this.model.enableRTL&&u.last().offset().left-15>this.element.offset().left)&&(i.text().length===b[ht].length?i.removeClass("e-resizeHead"):this._width!=this.element.width()&&i.text(b[ht].slice(0,i.text().length+1))))}for(pt=f.eq(r).find(".e-resizebtn").length,h=0;h<pt;h++)if(c=f.eq(r).find(".e-resizebtn").eq(0),n(c).is(":visible")){if(yt=this.element.width()+this.element.offset().left-25,w=c.offset().left+c.parent().siblings().not(".e-contentbottom").width(),this.model.enableRTL&&(w=this.element.width()+this.element.offset().left-c.offset().left+c.parent().siblings().not(".e-contentbottom").width()),ct=f.eq(r).find(".e-resizebtn").length,ct>1)for(rt=0;rt<ct;rt++)w=w+f.eq(r).find(".e-resizebtn").eq(rt).parents(".e-groupdiv").width()+7;yt>w&&(lt=c.parent().siblings(),c.parent().remove(),lt.show(),lt.not(".e-contentbottom").css("width",""))}this._qatResizeRemove();d=this.element.find(".e-tooltipdiv");d.hide();d.find(".e-tooltiptitle").children().remove();d.find(".e-tooltipdesc").children().remove();this.element.find(".e-ribbonpin").length>0&&this.element.find(".e-active-content").eq(0).is(":visible")&&(this._addRibbonPin(),this._tabContents.width(this.element.width()));this.element.find(".e-resizebtnselect").removeClass("e-resizebtnselect e-active");this.model.showQAT&&(at=this.element.find(".e-rbnqatmenu"),at.is(":visible")&&at.hide().css({top:"",left:""}),this.element.find(".e-qaresizebtn,.e-splitbtnqat").removeClass("e-tbtn e-active"))},_qatResize:function(){var r,i,e,u,f,o;if(this.model.showQAT&&this._qAccessBar.is(":visible")){for(e=this._qAccessBar.children(":visible").length,u=0;u<e;u++)r=this._qAccessBar.children(":visible").last(),this.element.width()-(r.width()+n(r).position().left+6)<=0&&(r.hasClass("e-splitbtnqatdiv")?(f=t.buildTag("div.e-qaresizebtndiv").attr("title","More Controls"),o=t.buildTag("button.e-qaresizebtn"),i=t.buildTag("div.e-qaresizediv").hide(),this.element.append(i),i.append(r.prev()),f.append(o.ejButton({size:"normal",type:"button",contentType:"imageonly",height:30,width:14,prefixIcon:"e-icon e-ribbon e-qaresizebtnicon",click:n.proxy(this._onQatResizeBtnClick,this)})),i.append(r),f.insertBefore(this.element.find(".e-rbnqatmenu"))):r.hasClass("e-qaresizebtndiv")&&(i=this.element.find(".e-qaresizediv"),i.prepend(r.prevAll(":visible").first())));i=this.element.find(".e-qaresizediv");i.is(":visible")&&i.hide()}},_qatResizeRemove:function(){var n=this.element.find(".e-qaresizediv"),e=this.element.width(),i=0,r,u,f,t;if(n.length>0&&this._qAccessBar.is(":visible")&&this.model.showQAT){for(n.show(),r=this.element.find(".e-qaresizebtndiv"),u=this._qAccessBar.children(":visible").length,f=n.children().length,t=0;t<u;t++)i=i+this._qAccessBar.children(":visible").eq(t).width()+6;for(t=0;t<=f;t++)i=n.children().length>2?i+n.children().eq(0).width():i+n.width(),e>i&&(n.children().length>2?n.children().eq(0).insertBefore(r):(n.children().insertBefore(r),this.element.find(".e-qaresizebtndiv").remove(),n.remove()));n.hide()}},_createResizeBtn:function(i,r){var h,y,c=this.element.find(".e-content.e-content-item"),u,l,o,f,e,s,a;for(u=c.eq(i).children(".e-groupdiv").eq(r),this.element.find(".e-respcontent").length||(o=u.height()),f=n.extend(!0,[],this.model.tabs),e=0;e<this.model.contextualTabs.length;e++)for(s=0;s<this.model.contextualTabs[e].tabs.length;s++)f.push(this.model.contextualTabs[e].tabs[s]);a=t.isNullOrUndefined(f[i-1].groups[r].id)?f[i-1].groups[r].text:f[i-1].groups[r].id;h=u.find(".e-contentbottom").text();l=u.children().not(".e-contentbottom");y=l.width();t.isNullOrUndefined(this._responsiveContentStyle)&&(this._responsiveContentStyle={height:o});u.children().hide();l.css("width",y);var p=t.buildTag("div.e-resizegroupdiv#"+this._id+"_resizeDiv_"+a),v=t.buildTag("button#"+this._id+"_resizebtn_"+a,h,{},{type:"button"}).addClass("e-resizebtn e-rbn-button"),w={target:c.eq(i).children(".e-groupdiv").eq(r).children()};v.ejButton({click:n.proxy(this._resizeBtnClick,w),contentType:t.ContentType.TextAndImage,imagePosition:t.ImagePosition.ImageBottom,prefixIcon:"e-icon e-ribbonresize"});u.height(o);v.children().prepend("<span class='e-icon e-"+h+"'><\/span>").height(o-5);v.appendTo(p);p.appendTo(c.eq(i).children(".e-groupdiv").eq(r))},_resizeBtnClick:function(){},_responsiveCustomize:function(n){n.find(".e-rbn-resize").parents(".e-controlpadding").addClass("e-rbn-resize").addClass("e-rbn-dll-cus");n.find(".e-rbn-resize").parents(".e-innerdivrow").addClass("e-rbn-resize").next().css("display","inline-block")},_responsiveContentShow:function(n){var r=this.element.find(".e-active-content"),i;this._responsiveCustomize(n);r.addClass("e-reshide");n.addClass("e-resshow");this.element.find(".e-responsivecontent .e-ribGroupContent").addClass("e-resshow");n.find(".e-ribGroupContent").height()>this._responsiveHeight&&(i=t.buildTag("div#"+this._id+"_responsivecontentScroll").addClass("e-rescontentScroll").append(t.buildTag("div")),i.find("div").append(n.children(".e-ribGroupContent")),n.append(i),n.find(".e-rescontentScroll").ejScroller({height:this._responsiveHeight,scrollerSize:8,buttonSize:0}));r.find(".e-resizebtnselect").removeClass("e-resizebtnselect")},_responsiveContentBack:function(){this._responsiveTarget.append(this._responsiveContent).find(".e-ribGroupContent").addClass("e-reshide").css(this._responsiveContentStyle);this._responsiveContent=null;this.element.find(".e-resizebtn.e-active").removeClass("e-active e-resizebtnselect");this.element.find(".e-galleryexpand.e-gallerymoveicon").length>0&&this.element.find(".e-galleryexpand.e-gallerymoveicon").trigger("click");this.element.find(".e-content.e-responsivecontent").children().not(".e-responsiveback").remove();this.element.find(".e-active-content").removeClass("e-reshide");this.element.find(".e-responsivecontent").removeClass("e-resshow")},_ribbonDocClick:function(i){var u,y,h,f,c,p,e,l;if(this.element.hasClass("e-responsive")){var r=i.target,o={},d=this,s=this.element.find(".e-responsivebackstage"),a=this.element.find(".e-responsivetabheader"),v=this.element.find(".e-active-content"),g=this.element.find(".e-responsivebackstage");if(!n(i.target).parents(".e-ribbon").length>0&&!(i.target==document.documentElement)&&!n(i.target).parents(".e-rbn-ddl").length>0&&!n(i.target).parents(".e-popup").length>0&&!this.element.find(".e-responsivecontent").is(":visible")){if(a.removeClass("e-resshow"),s.is(":visible")&&s.removeClass("e-backstageshowanimate"),this.element.find(".e-responsivecontent").is(":visible")&&this._responsiveContentBack(),this.element.find(".e-responsivebackstagecontent").hide(),!v.hasClass("e-responsiveheight")){v.addClass("e-responsiveheight e-rbnmobheader");this._responsiveScrollRemove();v.find(".e-groupresponsive.e-ribuparrow").removeClass("e-ribuparrow").addClass("e-ribdownarrow");o.isMobile=this.element.hasClass("e-responsive");o.target=r;o.currentTarget=i.currentTarget;o.eventType=i.type;this.element.find(".e-content.e-content-item").on("webkitTransitionEnd otransitionend oTransitionEnd msTransitionEnd transitionend",function(){d._trigger("collapse",o)})}return}if((n(r).hasClass("e-header")||n(r).hasClass("e-ribGroupContent")||n(r).hasClass("e-groupresponsive")||n(r).hasClass("e-respmobcontent"))&&(a.hasClass("e-resshow")&&a.removeClass("e-resshow"),s.is(":visible")&&s.removeClass("e-backstageshowanimate")),n(r).parent().hasClass("e-backstagerestopcontent")&&!n(".e-ddl-popup").is(":visible")){n(r).parents(".e-responsivebackstagecontent").hide();return}return}if(l=this.element.find(".e-gallexpandcontent"),f=this.element.find("#"+this._id+"_modelDiv"),c=this.element.find(".e-active-content"),f.is(":visible")&&f.hide(),l.hasClass("e-gallerypopupshow")&&(l.removeClass("e-gallerypopupshow"),f.hide()),(n(".e-rbn-splitbtn").parents().hasClass("e-active")||this.element.find(".e-resizebtn").hasClass("e-resizebtnselect")||n(".e-ddl-popup.e-rbn-ddl:visible").length>0||this.element.find(".e-colorwidget").hasClass("e-active"))&&c.append(f.show().height("100%")),e=n(".e-ribbonpopup:visible"),e.length>0&&!e.hasClass("e-ribbonpopupshow")?(e.addClass("e-ribbonpopupshow"),c.append(f.show().height("100%"))):e.hasClass("e-ribbonpopupshow")&&e.removeClass("e-ribbonpopupshow"),(n(i.target).hasClass("e-expgallerydiv")||n(i.target).parents(".e-expgallerydiv").length>0)&&(l.addClass("e-gallerypopupshow"),c.append(f.show().height("100%"))),n(i.target).hasClass("e-gallerymoveicon")||n(i.target).hasClass("e-gallerymovediv")||n(i.target).hasClass("e-expgallerydiv")||n(i.target).hasClass("e-scrollbar")||n(i.target).parents().hasClass("e-scrollbar")?u=i.target:n(i.target).parents().hasClass("e-gallerymenu")&&(n(i.target).hasClass("e-haschild")||n(i.target).hasClass("aschild")||n(i.target).hasClass("e-arrows"))&&(u=i.target),t.isNullOrUndefined(u)&&this._ribbonGalleryShow(),y=this.element.find(".e-tooltipdiv"),p=n(i.target).find(".e-disable"),y.is(":visible")&&n(p).length<=0&&!n(i.target).hasClass("e-disable")&&y.hide(),(n(i.target).hasClass("e-backstagechild")||n(i.target).parents(".e-backstagechild").length>0||n(i.target).hasClass("e-ribbonbackstagepage")||n(i.target).hasClass("e-apptab")||n(i.target).parents(".e-ribbonbackstagepage").length>0||n(i.target).parents(".e-apptab").length>0)&&(u=i.target),t.isNullOrUndefined(u)&&n(document).find(n(i.target)).length==0&&(u=n(i.currentTarget.activeElement)),t.isNullOrUndefined(u)&&this.element.find(".e-ribbonbackstagepage").hide(),!n(i.target).hasClass("e-disable-item")&&n(i.target).parents(".e-disable-item").length<=0&&this.element.find(".e-controlclicked").removeClass("e-controlclicked"),h=n(i.target).parents(".e-controlpadding"),h.length>0&&n(p).length<=0&&!n(i.target).hasClass("e-disable")&&n(h).find(".e-ribbongallerycontrol").length<=0&&n(h).addClass("e-controlclicked"),this.element.find(".e-ribbonpin").is(":visible")&&n(i.target).parents(".e-ribbon").length<=0&&!n(i.target).hasClass("e-scrollbar")&&!n(i.target).parents().hasClass("e-scrollbar")&&this.collapse(),this.element.find(".e-resizediv").is(":hidden")&&this.element.find(".e-resizebtnselect").removeClass("e-resizebtnselect e-active"),this._tabUl.find(".e-link.e-active").removeClass("e-active"),this.model.showQAT){var w=this.element.find(".e-rbnqatmenu"),b=this.element.find(".e-qaresizediv"),k=this.element.find(".e-splitbtnqat");!n(i.target).hasClass("e-splitbtnqatdiv")&&n(i.target).parents(".e-splitbtnqatdiv").length<=0&&n(i.target).parents(".e-rbnqatmenu").length<=0&&w.hide().css({top:"",left:""});!n(i.target).hasClass("e-qaresizebtndiv")&&w.is(":hidden")&&n(i.target).parents(".e-qaresizediv").length<=0&&n(i.target).parents(".e-qaresizebtndiv").length<=0&&!n(i.target).hasClass("e-splitbtnqatdiv")&&n(i.target).parents(".e-splitbtnqatdiv").length<=0&&b.hide().css({top:"",left:""});b.is(":hidden")&&this.element.find(".e-qaresizebtn").hasClass("e-active")&&!n(i.target).hasClass("e-qaresizebtndiv")&&n(i.target).parents(".e-qaresizebtndiv").length<=0&&n(i.target).parents(".e-rbnqatmenu").length<=0&&this.element.find(".e-qaresizebtn").removeClass("e-tbtn e-active");k.hasClass("e-active")&&!n(i.target).hasClass("e-splitbtnqatdiv")&&n(i.target).parents(".e-splitbtnqatdiv").length<=0&&n(i.target).parents(".e-rbnqatmenu").length<=0&&k.removeClass("e-tbtn e-active")}},_ribbonGalleryShow:function(){var f=0,h,e,r,o,c,i;if(this.element.find(".e-gallexpandcontent:visible").length>0){var u=0,s=0,t=this.element.find(".e-gallexpandcontent:visible").parent().find(".e-ribbongallery"),e=t.find(".e-galleryrow").length;for(i=1;i<=e;i++)t.find(".e-galleryrow").eq(i-1).css("display")==="block"&&(u===0&&(f=i-1),++u);for(t.find(".e-galleryrow").hide(),t.parent().find(".e-gallexpandcontent").hide(),t.show(),h=t.find(".e-galleryrow").eq(0).children().length,t.find(".e-galleryrow").eq(0).children().remove(),e=t.find(".e-galleryrow").length,r=0;r<e;r++)for(o=0;o<h;o++)if(c=t.parent().find(".e-galleryexpanderrow").children().eq(0),n(c).hasClass("e-galleryselect")){for(t.find(".e-galleryrow").eq(r).append(t.parent().find(".e-galleryexpanderrow").children().eq(0)).show(),--u,i=1;i<=u;i++)t.find(".e-galleryrow").eq(r+i).length>0?t.find(".e-galleryrow").eq(r+i).show():(++s,t.find(".e-galleryrow").eq(r-s).show());t.find(".e-galleryrow").last().is(":visible")?t.parent().find(".e-movedowndiv").addClass("e-disablegrymovebtn"):t.parent().find(".e-movedowndiv").removeClass("e-disablegrymovebtn");t.find(".e-galleryrow").first().is(":visible")?t.parent().find(".e-moveupdiv").addClass("e-disablegrymovebtn"):t.parent().find(".e-moveupdiv").removeClass("e-disablegrymovebtn")}else t.find(".e-galleryrow").eq(r).append(t.parent().find(".e-galleryexpanderrow").children().eq(0));if(t.find(".e-galleryselect").length<=0)for(i=0;i<u;i++)t.find(".e-galleryrow").eq(f).show(),++f;t.parent().find(".e-galleryexpanderrow").remove()}},_resizeDivHide:function(){var i=this.element.find(".e-resizediv"),r;this._off(n(document),"mousedown",this._rbndocumentClick);n(i).is(":visible")&&(r=n(i).children().eq(0).attr("id"),t.isNullOrUndefined(r)||(r="#"+r.slice(0,-8),n(i).children().hide().appendTo(r),this.element.find("#"+this._id+"_disabled").hide(),n(i).hide()),this.element.find(".e-ribbonpin").length>0&&n(i).css({top:""}))},_resizeDivShow:function(t){var u,f,i,e,r=this.element.find(".e-resizediv");i=this.element.find("#"+t[0].id).parent().siblings();u=i.not(".e-contentbottom").width()+i.parent().offset().left;this.model.enableRTL&&(u=i.not(".e-contentbottom").width()+(this.element.width()+this.element.offset().left-i.parent().offset().left));this.element.css({position:"relative"});n(r).css({left:""});u>this.element.width()+this.element.offset().left?(f=i.parent().position().left-(u-(this.element.width()+this.element.offset().left)),this.model.enableRTL&&(f=this.element.width()+(this.element.offset().left-i.parent().offset().left)-(u-(this.element.width()+this.element.offset().left-i.parent().width()))),e=parseInt(n(r).css("padding-left"),10)+parseInt(n(r).css("padding-right"),10),this.model.enableRTL?n(r).css({right:f+e-1}):n(r).offset({left:f-e})):this.model.enableRTL?n(r).css({right:this.element.width()+(this.element.offset().left-i.parent().offset().left)-i.parent().outerWidth()}):n(r).offset({left:i.parent().position().left});this.element.find(".e-ribbonpin").length>0&&n(r).offset({top:this.element.height()+this.element.find(".e-active-content").eq(0).height()});n(r).width(i.not(".e-contentbottom").css("width"));n(r).append(i.show()).show();this.element.hasClass("e-grpdivhide")&&(this.element.removeClass("e-grpdivhide"),this.element.find(".e-active-content").eq(0).addClass("e-resdivshow"));this._on(n(document),"mousedown",this._rbndocumentClick)},_rbndocumentClick:function(t){this.element.find(".e-resizediv").is(":visible")&&n(t.target).parents(".e-resizediv").length<=0&&n(t.target).parents(".e-rbn-ddl").length<=0&&!n(t.target).hasClass("e-rbn-splitbtn")&&n(t.target).parents(".e-colorpicker.e-ribbon:visible").length<=0&&n(t.target).parents(".e-rbn-splitbtn:visible").length<=0&&this._resizeDivHide();this.element.find(".e-resizediv").is(":hidden")&&this.element.find(".e-resizebtnselect").removeClass("e-resizebtnselect e-active")},_ribbonDocRightClick:function(t){var r,i;r=n(t.target).parents(".e-resizediv");r.length<=0&&this._resizeDivHide();this.element.find(".e-resizediv").is(":hidden")&&this.element.find(".e-resizebtnselect").removeClass("e-resizebtnselect e-active");this.element.find(".e-gallexpandcontent").hasClass("e-gallerypopupshow")&&this._ribbonGalleryShow();i=this.element.find(".e-tooltipdiv");i.is(":visible")&&i.hide()},_onTabDblClick:function(t){this._phoneMode||this.element.find(".e-apptab .e-menu ul").is(":visible")||this.element.find(".e-ribbonbackstagepage").is(":visible")||(n(t.target).closest(".e-tab.e-disable").length||(this.element.find(".e-collapsed .e-rarrowup-2x").length&&this.expand(),this.element.find(".e-expanded .e-rarrowup-2x").length&&this.collapse(),this.element.find(".e-expanded.e-ribbonpin").length&&this._removeRibbonPin()),this._clickValue=t.type)},_ribbonhover:function(t){this._phoneMode&&(t.type=="mouseover"?n(t.currentTarget).hasClass("e-resizegroupdiv")&&n(t.currentTarget).addClass("e-reshover"):t.type=="mouseout"&&n(t.currentTarget).hasClass("e-resizegroupdiv e-reshover")&&n(t.currentTarget).removeClass("e-reshover"))},_wireEvents:function(){this._on(n(document),"keydown",this._OnKeyDown);this._on(this.element,"keydown",this._ribbonKeyDown);this._on(this.element,"mouseover mouseout",".e-resizegroupdiv",this._ribbonhover);this._on(this.element.find("#"+this._id+"_expandCollapse"),t.eventType.click,this._tabExpandCollapse);this._on(this.element.find(".e-groupexpander"),t.eventType.click,this._onGroupExpandClick);this._on(this.element.find(".e-qatmenuli"),t.eventType.click,this._onQatMenuItemClick);this._on(n(window),"resize",this._ribbonWindowResize);this._on(n(document),"click",this._ribbonDocClick);this._on(n(document),"contextmenu",this._ribbonDocRightClick);this._on(this.element.find(".e-header > li"),"dblclick",this._onTabDblClick)}});t.Ribbon.alignType={rows:"rows",columns:"columns"};t.Ribbon.AlignType={Rows:"rows",Columns:"columns"};t.Ribbon.applicationTabType={menu:"menu",backstage:"backstage"};t.Ribbon.ApplicationTabType={Menu:"menu",Backstage:"backstage"};t.Ribbon.quickAccessMode={none:"none",toolBar:"toolbar",menu:"menu"};t.Ribbon.QuickAccessMode={None:"none",ToolBar:"toolbar",Menu:"menu"};t.Ribbon.type={button:"button",splitButton:"splitbutton",dropDownList:"dropdownlist",custom:"custom",toggleButton:"togglebutton",gallery:"gallery"};t.Ribbon.Type={Button:"button",SplitButton:"splitbutton",DropDownList:"dropdownlist",Custom:"custom",ToggleButton:"togglebutton",Gallery:"gallery"};t.Ribbon.customItemType={button:"button",menu:"menu"};t.Ribbon.CustomItemType={Button:"button",Menu:"menu"};t.Ribbon.itemType={button:"button",tab:"tab"};t.Ribbon.ItemType={Button:"button",Tab:"tab"};t.Ribbon.Locale=t.Ribbon.Locale||{};t.Ribbon.Locale["default"]=t.Ribbon.Locale["en-US"]={CustomizeQuickAccess:"Customize Quick Access Toolbar",RemoveFromQuickAccessToolbar:"Remove from Quick Access Toolbar",AddToQuickAccessToolbar:"Add to Quick Access Toolbar",ShowAboveTheRibbon:"Show Above the Ribbon",ShowBelowTheRibbon:"Show Below the Ribbon",MoreCommands:"More Commands..."}})(jQuery,Syncfusion)});
