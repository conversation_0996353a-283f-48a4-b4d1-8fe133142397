﻿CREATE TABLE [dbo].[Contacts] (
    [ContactId]                                       BIGINT          IDENTITY (1, 1) NOT NULL,
    [TenantId]                                        BIGINT          NOT NULL,
    [TherapistId]                                     BIGINT          NULL,
    [CoTherapistId]                                   BIGINT          NULL,
    [GuestId]                                         BIGINT          NULL,
    [<PERSON>agnos<PERSON>ianId]                                 BIGINT          NULL,
    [ClinicSupervisorId]                              BIGINT          NULL,
    [ContactCode]                                     NVARCHAR (20)   CONSTRAINT [DF_Contacts_ContactCode] DEFAULT (left(abs(checksum(newid())),(6))) NULL,
    [AppointmentCategoryId]                           BIGINT          NULL,
    [Active]                                          BIT             CONSTRAINT [DF_Contacts_Active] DEFAULT ((1)) NOT NULL,
    [InactiveReason]                                  NVARCHAR (40)   CONSTRAINT [DF_Contacts_InactiveReason] DEFAULT ('') NOT NULL,
    [Waiting]                                         BIT             CONSTRAINT [DF_Contacts_Active1] DEFAULT ((0)) NOT NULL,
    [PsychotherapyStartDate]                          DATE            NULL,
    [ReferralToAnotherSpecialist]                     NVARCHAR (40)   CONSTRAINT [DF_Contacts_ReferralToAnotherSpecialist] DEFAULT ('') NOT NULL,
    [ReferralToAnotherSpecialistComment]              NVARCHAR (500)  CONSTRAINT [DF_Contacts_FirstNameComment1_1] DEFAULT ('') NOT NULL,
    [FirstName]                                       NVARCHAR (50)   CONSTRAINT [DF_Contacts_FirstName] DEFAULT ('') NOT NULL,
    [LastName]                                        NVARCHAR (50)   CONSTRAINT [DF_Contacts_FirstName1] DEFAULT ('') NOT NULL,
    [FatherName]                                      NVARCHAR (50)   CONSTRAINT [DF_Contacts_FirstName1_1] DEFAULT ('') NOT NULL,
    [Email]                                           NVARCHAR (50)   CONSTRAINT [DF_Contacts_Email] DEFAULT ('') NOT NULL,
    [Email2]                                          NVARCHAR (50)   CONSTRAINT [DF_Contacts_Email1] DEFAULT ('') NOT NULL,
    [Phone1]                                          NVARCHAR (20)   CONSTRAINT [DF_Contacts_Phone1] DEFAULT ('') NOT NULL,
    [Mobile1]                                         NVARCHAR (20)   CONSTRAINT [DF_Contacts_Mobile1] DEFAULT ('') NOT NULL,
    [EmergencyContactName]                            NVARCHAR (50)   CONSTRAINT [DF_Contacts_EmergencyContactName] DEFAULT ('') NOT NULL,
    [EmergencyPhone]                                  NVARCHAR (50)   CONSTRAINT [DF_Contacts_EmergencyPhone] DEFAULT ('') NOT NULL,
    [Residence]                                       NVARCHAR (500)  CONSTRAINT [DF_Contacts_Address] DEFAULT ('') NOT NULL,
    [BirthDate]                                       DATETIME        NULL,
    [BirthPlace]                                      NVARCHAR (50)   CONSTRAINT [DF_Contacts_BirthPlace] DEFAULT ('') NOT NULL,
    [Sex]                                             NVARCHAR (20)   NOT NULL,
    [SexComment]                                      NVARCHAR (500)  CONSTRAINT [DF_Contacts_SexComment] DEFAULT ('') NOT NULL,
    [PoliceIdentity]                                  NVARCHAR (10)   CONSTRAINT [DF_Contacts_PoliceIdentity] DEFAULT ('') NOT NULL,
    [AMA]                                             NVARCHAR (20)   CONSTRAINT [DF_Contacts_AMA] DEFAULT ('') NOT NULL,
    [AMKA]                                            NVARCHAR (20)   CONSTRAINT [DF_Contacts_AMKA] DEFAULT ('') NOT NULL,
    [Photo]                                           VARBINARY (MAX) NULL,
    [AFM]                                             NVARCHAR (20)   CONSTRAINT [DF_Contacts_AFM] DEFAULT ('') NOT NULL,
    [DOY]                                             NVARCHAR (50)   CONSTRAINT [DF_Contacts_DOY] DEFAULT ('') NOT NULL,
    [SessionOrigin]                                   NVARCHAR (20)   CONSTRAINT [DF_Contacts_SessionOrigin] DEFAULT ('') NOT NULL,
    [Occupation]                                      NVARCHAR (40)   CONSTRAINT [DF_Contacts_Occupation] DEFAULT ('') NOT NULL,
    [EducationLevel]                                  NVARCHAR (40)   CONSTRAINT [DF_Contacts_DOY1] DEFAULT ('') NOT NULL,
    [MaritalStatus]                                   NVARCHAR (40)   CONSTRAINT [DF_Contacts_EducationLevel1] DEFAULT ('') NOT NULL,
    [Children]                                        NVARCHAR (500)  CONSTRAINT [DF_Contacts_Residence1] DEFAULT ('') NOT NULL,
    [ChildrenAge]                                     NVARCHAR (500)  CONSTRAINT [DF_Contacts_Children1] DEFAULT ('') NOT NULL,
    [LivingStatus]                                    NVARCHAR (40)   CONSTRAINT [DF_Contacts_LivingStatus] DEFAULT ('') NOT NULL,
    [EconomicStatus]                                  NVARCHAR (40)   CONSTRAINT [DF_Contacts_LivingStatus1] DEFAULT ('') NOT NULL,
    [CommunicationMethod]                             NVARCHAR (40)   CONSTRAINT [DF_Contacts_EconomicStatus1] DEFAULT ('') NOT NULL,
    [SessionFrequency]                                NVARCHAR (40)   CONSTRAINT [DF_Contacts_CommunicationMethod1] DEFAULT ('') NOT NULL,
    [EyeContact]                                      NVARCHAR (40)   CONSTRAINT [DF_Contacts_SessionFrequency1_1] DEFAULT ('') NOT NULL,
    [BodyLanguage]                                    NVARCHAR (40)   CONSTRAINT [DF_Contacts_EyeContact1] DEFAULT ('') NOT NULL,
    [VoiceTone]                                       NVARCHAR (40)   CONSTRAINT [DF_Contacts_BodyLanguage1_1] DEFAULT ('') NOT NULL,
    [Narration]                                       NVARCHAR (40)   CONSTRAINT [DF_Contacts_VoiceTone1] DEFAULT ('') NOT NULL,
    [SexualOrientation]                               NVARCHAR (40)   CONSTRAINT [DF_Contacts_BodyLanguage1] DEFAULT ('') NOT NULL,
    [GeneralRequest]                                  NVARCHAR (40)   CONSTRAINT [DF_Contacts_SexualOrientation1] DEFAULT ('') NOT NULL,
    [SpecialRequest]                                  NVARCHAR (500)  CONSTRAINT [DF_Contacts_SexualOrientation1_1] DEFAULT ('') NOT NULL,
    [HealthHistory]                                   NVARCHAR (500)  CONSTRAINT [DF_Contacts_SpecialRequest1] DEFAULT ('') NOT NULL,
    [Medication]                                      NVARCHAR (500)  CONSTRAINT [DF_Contacts_HealthHistory1] DEFAULT ('') NOT NULL,
    [HealingExperience]                               NVARCHAR (40)   CONSTRAINT [DF_Contacts_GeneralRequest1] DEFAULT ('') NOT NULL,
    [OtherActivities]                                 NVARCHAR (500)  CONSTRAINT [DF_Contacts_Medication1] DEFAULT ('') NOT NULL,
    [TherapistFirstView]                              NVARCHAR (500)  CONSTRAINT [DF_Contacts_OtherActivities1] DEFAULT ('') NOT NULL,
    [AppointmentsStart]                               NVARCHAR (40)   CONSTRAINT [DF_Contacts_HealingExperience1] DEFAULT ('') NOT NULL,
    [AppointmentsFrequency]                           NVARCHAR (40)   CONSTRAINT [DF_Contacts_AppointmentsStart1] DEFAULT ('') NOT NULL,
    [InterventionModel]                               NVARCHAR (40)   CONSTRAINT [DF_Contacts_AppointmentsFrequency1] DEFAULT ('') NOT NULL,
    [LastMedicalCheckupDate]                          DATE            NULL,
    [LastMedicalCheckup]                              NVARCHAR (50)   CONSTRAINT [DF_Contacts_LastMedicalCheckup] DEFAULT ('') NOT NULL,
    [OtherImportantFromMotherFamily]                  NVARCHAR (40)   CONSTRAINT [DF_Contacts_OtherImportants] DEFAULT ('') NOT NULL,
    [OtherImportantFromMotherFamilyTherapistComment]  NVARCHAR (500)  CONSTRAINT [DF_Contacts_OtherImportantsTherapistComment] DEFAULT ('') NOT NULL,
    [OtherImportantFromMotherFamilySupervisorComment] NVARCHAR (500)  CONSTRAINT [DF_Contacts_OtherImportantsSupervisorComment] DEFAULT ('') NOT NULL,
    [MotherCharacteristics]                           NVARCHAR (40)   CONSTRAINT [DF_Contacts_OtherActivities1_1] DEFAULT ('') NOT NULL,
    [MotherCharacteristicsTherapistComment]           NVARCHAR (500)  CONSTRAINT [DF_Contacts_OtherActivitiesComment1_1] DEFAULT ('') NOT NULL,
    [MotherCharacteristicsSupervisorComment]          NVARCHAR (500)  CONSTRAINT [DF_Contacts_MotherCharacteristicsTherapistComment1] DEFAULT ('') NOT NULL,
    [MotherInfo]                                      NVARCHAR (500)  CONSTRAINT [DF_Contacts_MotherCharacteristics1] DEFAULT ('') NOT NULL,
    [MotherInfoTherapistComment]                      NVARCHAR (500)  CONSTRAINT [DF_Contacts_MotherCharacteristicsTherapistComment1_1] DEFAULT ('') NOT NULL,
    [MotherInfoSupervisorComment]                     NVARCHAR (500)  CONSTRAINT [DF_Contacts_MotherCharacteristicsSupervisorComment1] DEFAULT ('') NOT NULL,
    [FatherCharacteristics]                           NVARCHAR (40)   CONSTRAINT [DF_Contacts_MotherCharacteristics1_1] DEFAULT ('') NOT NULL,
    [FatherCharacteristicsTherapistComment]           NVARCHAR (500)  CONSTRAINT [DF_Contacts_OtherImportantFromMotherFamilyTherapistComment1] DEFAULT ('') NOT NULL,
    [FatherCharacteristicsSupervisorComment]          NVARCHAR (500)  CONSTRAINT [DF_Contacts_OtherImportantFromMotherFamilySupervisorComment1] DEFAULT ('') NOT NULL,
    [FatherInfo]                                      NVARCHAR (500)  CONSTRAINT [DF_Contacts_MotherInfo1_1] DEFAULT ('') NOT NULL,
    [FatherInfoTherapistComment]                      NVARCHAR (500)  CONSTRAINT [DF_Contacts_FatherCharacteristicsTherapistComment1] DEFAULT ('') NOT NULL,
    [FatherInfoSupervisorComment]                     NVARCHAR (500)  CONSTRAINT [DF_Contacts_FatherCharacteristicsSupervisorComment1] DEFAULT ('') NOT NULL,
    [OtherImportantFromFatherFamily]                  NVARCHAR (40)   CONSTRAINT [DF_Contacts_OtherImportantFromMotherFamily1] DEFAULT ('') NOT NULL,
    [OtherImportantFromFatherFamilyTherapistComment]  NVARCHAR (500)  CONSTRAINT [DF_Contacts_OtherImportantFromMotherFamilyTherapistComment1_1] DEFAULT ('') NOT NULL,
    [OtherImportantFromFatherFamilySupervisorComment] NVARCHAR (500)  CONSTRAINT [DF_Contacts_OtherImportantFromMotherFamilySupervisorComment1_1] DEFAULT ('') NOT NULL,
    [MotherFamilyHistory]                             NVARCHAR (40)   CONSTRAINT [DF_Contacts_OtherImportantFromFatherFamily1] DEFAULT ('') NOT NULL,
    [MotherFamilyHistoryTherapistComment]             NVARCHAR (500)  CONSTRAINT [DF_Contacts_OtherImportantFromFatherFamilyTherapistComment1] DEFAULT ('') NOT NULL,
    [MotherFamilyHistorySupervisorComment]            NVARCHAR (500)  CONSTRAINT [DF_Contacts_OtherImportantFromFatherFamilySupervisorComment1] DEFAULT ('') NOT NULL,
    [FatherFamilyHistory]                             NVARCHAR (40)   CONSTRAINT [DF_Contacts_MotherFamilyHistory1] DEFAULT ('') NOT NULL,
    [FatherFamilyHistoryTherapistComment]             NVARCHAR (500)  CONSTRAINT [DF_Contacts_MotherFamilyHistoryTherapistComment1] DEFAULT ('') NOT NULL,
    [FatherFamilyHistorySupervisorComment]            NVARCHAR (500)  CONSTRAINT [DF_Contacts_MotherFamilyHistorySupervisorComment1] DEFAULT ('') NOT NULL,
    [FamilyMedicalHistory]                            NVARCHAR (500)  CONSTRAINT [DF_Contacts_FamilyHistory1_2] DEFAULT ('') NOT NULL,
    [FamilyMedicalHistoryTherapistComment]            NVARCHAR (500)  CONSTRAINT [DF_Contacts_FamilyHistoryTherapistComment1_1] DEFAULT ('') NOT NULL,
    [FamilyMedicalHistorySupervisorComment]           NVARCHAR (500)  CONSTRAINT [DF_Contacts_FamilyHistorySupervisorComment1_1] DEFAULT ('') NOT NULL,
    [OtherImportant]                                  NVARCHAR (40)   CONSTRAINT [DF_Contacts_OtherImportantFromFatherFamily1_1] DEFAULT ('') NOT NULL,
    [OtherImportantTherapistComment]                  NVARCHAR (500)  CONSTRAINT [DF_Contacts_OtherImportantFromFatherFamilyTherapistComment1_1] DEFAULT ('') NOT NULL,
    [OtherImportantSupervisorComment]                 NVARCHAR (500)  CONSTRAINT [DF_Contacts_OtherImportantFromFatherFamilySupervisorComment1_1] DEFAULT ('') NOT NULL,
    [MotherFeedbackInMySuccess]                       NVARCHAR (40)   CONSTRAINT [DF_Contacts_FamilyHistory1] DEFAULT ('') NOT NULL,
    [MotherFeedbackInMySuccessTherapistComment]       NVARCHAR (500)  CONSTRAINT [DF_Contacts_FamilyHistoryTherapistComment1] DEFAULT ('') NOT NULL,
    [MotherFeedbackInMySuccessSupervisorComment]      NVARCHAR (500)  CONSTRAINT [DF_Contacts_FamilyHistorySupervisorComment1] DEFAULT ('') NOT NULL,
    [MotherFeedbackInMyFailure]                       NVARCHAR (40)   CONSTRAINT [DF_Contacts_MotherFeedbackInMySuccess1] DEFAULT ('') NOT NULL,
    [MotherFeedbackInMyFailureTherapistComment]       NVARCHAR (500)  CONSTRAINT [DF_Contacts_MotherFeedbackInMySuccessTherapistComment1] DEFAULT ('') NOT NULL,
    [MotherFeedbackInMyFailureSupervisorComment]      NVARCHAR (500)  CONSTRAINT [DF_Contacts_MotherFeedbackInMySuccessSupervisorComment1] DEFAULT ('') NOT NULL,
    [FatherFeedbackInMySuccess]                       NVARCHAR (40)   CONSTRAINT [DF_Contacts_FatherFeedbackInMyFailure1] DEFAULT ('') NOT NULL,
    [FatherFeedbackInMySuccessTherapistComment]       NVARCHAR (500)  CONSTRAINT [DF_Contacts_MotherFeedbackInMyFailureTherapistComment1] DEFAULT ('') NOT NULL,
    [FatherFeedbackInMySuccessSupervisorComment]      NVARCHAR (500)  CONSTRAINT [DF_Contacts_MotherFeedbackInMyFailureSupervisorComment1] DEFAULT ('') NOT NULL,
    [FatherFeedbackInMyFailure]                       NVARCHAR (40)   CONSTRAINT [DF_Contacts_MotherFeedbackInMyFailure1] DEFAULT ('') NOT NULL,
    [FatherFeedbackInMyFailureTherapistComment]       NVARCHAR (500)  CONSTRAINT [DF_Contacts_FatherFeedbackInMySuccessTherapistComment1] DEFAULT ('') NOT NULL,
    [FatherFeedbackInMyFailureSupervisorComment]      NVARCHAR (500)  CONSTRAINT [DF_Contacts_FatherFeedbackInMySuccessSupervisorComment1] DEFAULT ('') NOT NULL,
    [ImportantFeedbackInMySuccess]                    NVARCHAR (40)   CONSTRAINT [DF_Contacts_FatherFeedbackInMySuccess1] DEFAULT ('') NOT NULL,
    [ImportantFeedbackInMySuccessTherapistComment]    NVARCHAR (500)  CONSTRAINT [DF_Contacts_FatherFeedbackInMyFailureTherapistComment1] DEFAULT ('') NOT NULL,
    [ImportantFeedbackInMySuccessSupervisorComment]   NVARCHAR (500)  CONSTRAINT [DF_Contacts_FatherFeedbackInMyFailureSupervisorComment1] DEFAULT ('') NOT NULL,
    [ImportantFeedbackInMyFailure]                    NVARCHAR (40)   CONSTRAINT [DF_Contacts_FatherFeedbackInMyFailure1_1] DEFAULT ('') NOT NULL,
    [ImportantFeedbackInMyFailureTherapistComment]    NVARCHAR (500)  CONSTRAINT [DF_Contacts_ImportantFeedbackInMySuccessTherapistComment1] DEFAULT ('') NOT NULL,
    [ImportantFeedbackInMyFailureSupervisorComment]   NVARCHAR (500)  CONSTRAINT [DF_Contacts_ImportantFeedbackInMySuccessSupervisorComment1] DEFAULT ('') NOT NULL,
    [AdhesionType]                                    NVARCHAR (40)   CONSTRAINT [DF_Contacts_ImportantFeedbackInMyFailure1] DEFAULT ('') NOT NULL,
    [AdhesionTypeTherapistComment]                    NVARCHAR (500)  CONSTRAINT [DF_Contacts_ImportantFeedbackInMyFailureTherapistComment1] DEFAULT ('') NOT NULL,
    [AdhesionTypeSupervisorComment]                   NVARCHAR (500)  CONSTRAINT [DF_Contacts_ImportantFeedbackInMyFailureSupervisorComment1] DEFAULT ('') NOT NULL,
    [PreschoolExperiences]                            NVARCHAR (40)   CONSTRAINT [DF_Contacts_AdhesionType1] DEFAULT ('') NOT NULL,
    [PreschoolExperiencesTherapistComment]            NVARCHAR (500)  CONSTRAINT [DF_Contacts_AdhesionTypeTherapistComment1] DEFAULT ('') NOT NULL,
    [PreschoolExperiencesSupervisorComment]           NVARCHAR (500)  CONSTRAINT [DF_Contacts_AdhesionTypeSupervisorComment1] DEFAULT ('') NOT NULL,
    [SchoolExperiences]                               NVARCHAR (40)   CONSTRAINT [DF_Contacts_PreschoolExperiences1] DEFAULT ('') NOT NULL,
    [SchoolExperiencesTherapistComment]               NVARCHAR (500)  CONSTRAINT [DF_Contacts_PreschoolExperiencesTherapistComment1] DEFAULT ('') NOT NULL,
    [SchoolExperiencesSupervisorComment]              NVARCHAR (500)  CONSTRAINT [DF_Contacts_PreschoolExperiencesSupervisorComment1] DEFAULT ('') NOT NULL,
    [TeenageExperiences]                              NVARCHAR (40)   CONSTRAINT [DF_Contacts_SchoolExperiences1] DEFAULT ('') NOT NULL,
    [TeenageExperiencesTherapistComment]              NVARCHAR (500)  CONSTRAINT [DF_Contacts_SchoolExperiencesTherapistComment1] DEFAULT ('') NOT NULL,
    [TeenageExperiencesSupervisorComment]             NVARCHAR (500)  CONSTRAINT [DF_Contacts_SchoolExperiencesSupervisorComment1] DEFAULT ('') NOT NULL,
    [AdultExperiences]                                NVARCHAR (40)   CONSTRAINT [DF_Contacts_TeenageExperiences1] DEFAULT ('') NOT NULL,
    [AdultExperiencesTherapistComment]                NVARCHAR (500)  CONSTRAINT [DF_Contacts_TeenageExperiencesTherapistComment1] DEFAULT ('') NOT NULL,
    [AdultExperiencesSupervisorComment]               NVARCHAR (500)  CONSTRAINT [DF_Contacts_TeenageExperiencesSupervisorComment1] DEFAULT ('') NOT NULL,
    [WorkExperiences]                                 NVARCHAR (40)   CONSTRAINT [DF_Contacts_AdultExperiences1] DEFAULT ('') NOT NULL,
    [WorkExperiencesTherapistComment]                 NVARCHAR (500)  CONSTRAINT [DF_Contacts_AdultExperiencesTherapistComment1] DEFAULT ('') NOT NULL,
    [WorkExperiencesSupervisorComment]                NVARCHAR (500)  CONSTRAINT [DF_Contacts_AdultExperiencesSupervisorComment1] DEFAULT ('') NOT NULL,
    [GeneralTraumaHistory]                            NVARCHAR (500)  CONSTRAINT [DF_Contacts_FamilyHistory1_1] DEFAULT ('') NOT NULL,
    [GeneralTraumaHistoryTherapistComment]            NVARCHAR (500)  CONSTRAINT [DF_Contacts_WorkExperiencesTherapistComment1] DEFAULT ('') NOT NULL,
    [GeneralTraumaHistorySupervisorComment]           NVARCHAR (500)  CONSTRAINT [DF_Contacts_WorkExperiencesSupervisorComment1] DEFAULT ('') NOT NULL,
    [SpecificTraumaHistory]                           NVARCHAR (40)   CONSTRAINT [DF_Contacts_GeneralTraumaHistory1] DEFAULT ('') NOT NULL,
    [SpecificTraumaHistoryTherapistComment]           NVARCHAR (500)  CONSTRAINT [DF_Contacts_GeneralTraumaHistoryTherapistComment1] DEFAULT ('') NOT NULL,
    [SpecificTraumaHistorySupervisorComment]          NVARCHAR (500)  CONSTRAINT [DF_Contacts_GeneralTraumaHistorySupervisorComment1] DEFAULT ('') NOT NULL,
    [GeneralBiographicalInfo]                         NVARCHAR (500)  CONSTRAINT [DF_Contacts_GeneralTraumaHistory1_1] DEFAULT ('') NOT NULL,
    [GeneralBiographicalInfoTherapistComment]         NVARCHAR (500)  CONSTRAINT [DF_Contacts_SpecificTraumaHistoryTherapistComment1] DEFAULT ('') NOT NULL,
    [GeneralBiographicalInfoSupervisorComment]        NVARCHAR (500)  CONSTRAINT [DF_Contacts_SpecificTraumaHistorySupervisorComment1] DEFAULT ('') NOT NULL,
    [Developmental]                                   NVARCHAR (40)   CONSTRAINT [DF_Contacts_GeneralBiographicalInfo1] DEFAULT ('') NOT NULL,
    [DevelopmentalTherapistComment]                   NVARCHAR (500)  CONSTRAINT [DF_Contacts_GeneralBiographicalInfoTherapistComment1] DEFAULT ('') NOT NULL,
    [DevelopmentalSupervisorComment]                  NVARCHAR (500)  CONSTRAINT [DF_Contacts_GeneralBiographicalInfoSupervisorComment1] DEFAULT ('') NOT NULL,
    [EmotionalDifficulties]                           NVARCHAR (500)  CONSTRAINT [DF_Contacts_Learnings1] DEFAULT ('') NOT NULL,
    [EmotionalDifficultiesTherapistComment]           NVARCHAR (500)  CONSTRAINT [DF_Contacts_LearningsTherapistComment1] DEFAULT ('') NOT NULL,
    [EmotionalDifficultiesSupervisorComment]          NVARCHAR (500)  CONSTRAINT [DF_Contacts_LearningsSupervisorComment1] DEFAULT ('') NOT NULL,
    [EmotionalRemarks]                                NVARCHAR (500)  CONSTRAINT [DF_Contacts_EmotionalDifficulties1] DEFAULT ('') NOT NULL,
    [EmotionalRemarksTherapistComment]                NVARCHAR (500)  CONSTRAINT [DF_Contacts_EmotionalDifficultiesTherapistComment1] DEFAULT ('') NOT NULL,
    [EmotionalRemarksSupervisorComment]               NVARCHAR (500)  CONSTRAINT [DF_Contacts_EmotionalDifficultiesSupervisorComment1] DEFAULT ('') NOT NULL,
    [EatingDisorder]                                  NVARCHAR (40)   CONSTRAINT [DF_Contacts_EatingDisorder] DEFAULT ('') NOT NULL,
    [EatingDisorderTherapistComment]                  NVARCHAR (500)  CONSTRAINT [DF_Contacts_AnorexiaTherapistComment1] DEFAULT ('') NOT NULL,
    [EatingDisorderSupervisorComment]                 NVARCHAR (500)  CONSTRAINT [DF_Contacts_AnorexiaSupervisorComment1] DEFAULT ('') NOT NULL,
    [Moods]                                           NVARCHAR (40)   CONSTRAINT [DF_Contacts_MajorDepression1_1] DEFAULT ('') NOT NULL,
    [MoodsTherapistComment]                           NVARCHAR (500)  CONSTRAINT [DF_Contacts_MajorDepressionTherapistComment1_1] DEFAULT ('') NOT NULL,
    [MoodsSupervisorComment]                          NVARCHAR (500)  CONSTRAINT [DF_Contacts_MajorDepressionSupervisorComment1_1] DEFAULT ('') NOT NULL,
    [OtherMoodObservations]                           NVARCHAR (500)  CONSTRAINT [DF_Contacts_DepressivePseudonoiaSupervisorComment1] DEFAULT ('') NOT NULL,
    [OtherMoodObservationsTherapistComment]           NVARCHAR (500)  CONSTRAINT [DF_Contacts_OtherMoodObservations1] DEFAULT ('') NOT NULL,
    [OtherMoodObservationsSupervisorComment]          NVARCHAR (500)  CONSTRAINT [DF_Contacts_OtherMoodObservations1_1] DEFAULT ('') NOT NULL,
    [Anxiety]                                         NVARCHAR (40)   CONSTRAINT [DF_Contacts_Moods1] DEFAULT ('') NOT NULL,
    [AnxietyTherapistComment]                         NVARCHAR (500)  CONSTRAINT [DF_Contacts_MoodsTherapistComment1] DEFAULT ('') NOT NULL,
    [AnxietySupervisorComment]                        NVARCHAR (500)  CONSTRAINT [DF_Contacts_MoodsSupervisorComment1] DEFAULT ('') NOT NULL,
    [Sleep]                                           NVARCHAR (40)   CONSTRAINT [DF_Contacts_Insomnia1_2] DEFAULT ('') NOT NULL,
    [SleepTherapistComment]                           NVARCHAR (500)  CONSTRAINT [DF_Contacts_InsomniaTherapistComment1_2] DEFAULT ('') NOT NULL,
    [SleepSupervisorComment]                          NVARCHAR (500)  CONSTRAINT [DF_Contacts_InsomniaSupervisorComment1_2] DEFAULT ('') NOT NULL,
    [Psychotic]                                       NVARCHAR (40)   CONSTRAINT [DF_Contacts_SleepDisorder1] DEFAULT ('') NOT NULL,
    [PsychoticTherapistComment]                       NVARCHAR (500)  CONSTRAINT [DF_Contacts_SleepDisorderTherapistComment1] DEFAULT ('') NOT NULL,
    [PsychoticSupervisorComment]                      NVARCHAR (500)  CONSTRAINT [DF_Contacts_SleepDisorderSupervisorComment1] DEFAULT ('') NOT NULL,
    [DefenseMechanisms]                               NVARCHAR (40)   CONSTRAINT [DF_Contacts_DefenseMechanisms] DEFAULT ('') NOT NULL,
    [DefenseMechanismsTherapistComment]               NVARCHAR (500)  CONSTRAINT [DF_Contacts_PsychoticTherapistComment1] DEFAULT ('') NOT NULL,
    [DefenseMechanismsSupervisorComment]              NVARCHAR (500)  CONSTRAINT [DF_Contacts_PsychoticSupervisorComment1] DEFAULT ('') NOT NULL,
    [Shapes]                                          NVARCHAR (40)   CONSTRAINT [DF_Contacts_DefenseMechanisms1] DEFAULT ('') NOT NULL,
    [ShapesTherapistComment]                          NVARCHAR (500)  CONSTRAINT [DF_Contacts_DefenseMechanismsTherapistComment1] DEFAULT ('') NOT NULL,
    [ShapesSupervisorComment]                         NVARCHAR (500)  CONSTRAINT [DF_Contacts_DefenseMechanismsSupervisorComment1] DEFAULT ('') NOT NULL,
    [Paranoid]                                        NVARCHAR (40)   CONSTRAINT [DF_Contacts_Insomnia1_1] DEFAULT ('') NOT NULL,
    [ParanoidTherapistComment]                        NVARCHAR (500)  CONSTRAINT [DF_Contacts_InsomniaTherapistComment1_1] DEFAULT ('') NOT NULL,
    [ParanoidSupervisorComment]                       NVARCHAR (500)  CONSTRAINT [DF_Contacts_InsomniaSupervisorComment1_1] DEFAULT ('') NOT NULL,
    [Schizoid]                                        NVARCHAR (40)   CONSTRAINT [DF_Contacts_Paranoid1] DEFAULT ('') NOT NULL,
    [SchizoidTherapistComment]                        NVARCHAR (500)  CONSTRAINT [DF_Contacts_ParanoidTherapistComment1] DEFAULT ('') NOT NULL,
    [SchizoidSupervisorComment]                       NVARCHAR (500)  CONSTRAINT [DF_Contacts_ParanoidSupervisorComment1] DEFAULT ('') NOT NULL,
    [Schizotype]                                      NVARCHAR (40)   CONSTRAINT [DF_Contacts_Schizoid1] DEFAULT ('') NOT NULL,
    [SchizotypeTherapistComment]                      NVARCHAR (500)  CONSTRAINT [DF_Contacts_SchizoidTherapistComment1] DEFAULT ('') NOT NULL,
    [SchizotypeSupervisorComment]                     NVARCHAR (500)  CONSTRAINT [DF_Contacts_SchizoidSupervisorComment1] DEFAULT ('') NOT NULL,
    [OnLimit]                                         NVARCHAR (40)   CONSTRAINT [DF_Contacts_Schizotype1] DEFAULT ('') NOT NULL,
    [OnLimitTherapistComment]                         NVARCHAR (500)  CONSTRAINT [DF_Contacts_SchizotypeTherapistComment1] DEFAULT ('') NOT NULL,
    [OnLimitSupervisorComment]                        NVARCHAR (500)  CONSTRAINT [DF_Contacts_SchizotypeSupervisorComment1] DEFAULT ('') NOT NULL,
    [Antisocial]                                      NVARCHAR (40)   CONSTRAINT [DF_Contacts_OnLimit1] DEFAULT ('') NOT NULL,
    [AntisocialTherapistComment]                      NVARCHAR (500)  CONSTRAINT [DF_Contacts_OnLimitTherapistComment1] DEFAULT ('') NOT NULL,
    [AntisocialSupervisorComment]                     NVARCHAR (500)  CONSTRAINT [DF_Contacts_OnLimitSupervisorComment1] DEFAULT ('') NOT NULL,
    [Histronic]                                       NVARCHAR (40)   CONSTRAINT [DF_Contacts_Antisocial1] DEFAULT ('') NOT NULL,
    [HistronicTherapistComment]                       NVARCHAR (500)  CONSTRAINT [DF_Contacts_AntisocialTherapistComment1] DEFAULT ('') NOT NULL,
    [HistronicSupervisorComment]                      NVARCHAR (500)  CONSTRAINT [DF_Contacts_AntisocialSupervisorComment1] DEFAULT ('') NOT NULL,
    [Narcissistic]                                    NVARCHAR (40)   CONSTRAINT [DF_Contacts_Histronic1] DEFAULT ('') NOT NULL,
    [NarcissisticTherapistComment]                    NVARCHAR (500)  CONSTRAINT [DF_Contacts_HistronicTherapistComment1] DEFAULT ('') NOT NULL,
    [NarcissisticSupervisorComment]                   NVARCHAR (500)  CONSTRAINT [DF_Contacts_HistronicSupervisorComment1] DEFAULT ('') NOT NULL,
    [Ideopsychocompression]                           NVARCHAR (40)   CONSTRAINT [DF_Contacts_Narcissistic1] DEFAULT ('') NOT NULL,
    [IdeopsychocompressionTherapistComment]           NVARCHAR (500)  CONSTRAINT [DF_Contacts_NarcissisticTherapistComment1] DEFAULT ('') NOT NULL,
    [IdeopsychocompressionSupervisorComment]          NVARCHAR (500)  CONSTRAINT [DF_Contacts_NarcissisticSupervisorComment1] DEFAULT ('') NOT NULL,
    [Avoidable]                                       NVARCHAR (40)   CONSTRAINT [DF_Contacts_Ideopsychocompression1] DEFAULT ('') NOT NULL,
    [AvoidableTherapistComment]                       NVARCHAR (500)  CONSTRAINT [DF_Contacts_IdeopsychocompressionTherapistComment1] DEFAULT ('') NOT NULL,
    [AvoidableSupervisorComment]                      NVARCHAR (500)  CONSTRAINT [DF_Contacts_IdeopsychocompressionSupervisorComment1] DEFAULT ('') NOT NULL,
    [Addictive]                                       NVARCHAR (40)   CONSTRAINT [DF_Contacts_Avoidable1] DEFAULT ('') NOT NULL,
    [AddictiveTherapistComment]                       NVARCHAR (500)  CONSTRAINT [DF_Contacts_AvoidableTherapistComment1] DEFAULT ('') NOT NULL,
    [AddictiveSupervisorComment]                      NVARCHAR (500)  CONSTRAINT [DF_Contacts_AvoidableSupervisorComment1] DEFAULT ('') NOT NULL,
    [PassiveAggressive]                               NVARCHAR (40)   CONSTRAINT [DF_Contacts_Addictive1] DEFAULT ('') NOT NULL,
    [PassiveAggressiveTherapistComment]               NVARCHAR (500)  CONSTRAINT [DF_Contacts_AddictiveTherapistComment1] DEFAULT ('') NOT NULL,
    [PassiveAggressiveSupervisorComment]              NVARCHAR (500)  CONSTRAINT [DF_Contacts_AddictiveSupervisorComment1] DEFAULT ('') NOT NULL,
    [OtherDisorderInfo]                               NVARCHAR (500)  CONSTRAINT [DF_Contacts_PassiveAggressiveTherapistComment11] DEFAULT ('') NOT NULL,
    [OtherDisorderInfoTherapistComment]               NVARCHAR (500)  CONSTRAINT [DF_Contacts_PassiveAggressiveTherapistComment1] DEFAULT ('') NOT NULL,
    [OtherDisorderInfoSupervisorComment]              NVARCHAR (500)  CONSTRAINT [DF_Contacts_PassiveAggressiveSupervisorComment1] DEFAULT ('') NOT NULL,
    [Notes]                                           NVARCHAR (MAX)  CONSTRAINT [DF_Contacts_Notes] DEFAULT ('') NOT NULL,
    [CaseFormulation]                                 NVARCHAR (MAX)  CONSTRAINT [DF_Contacts_Notes1] DEFAULT ('') NOT NULL,
    [AccessLevel]                                     NVARCHAR (40)   CONSTRAINT [DF_Contacts_AccessLevel] DEFAULT ('') NOT NULL,
    [AppointmentsState]                               NVARCHAR (40)   CONSTRAINT [DF_Contacts_AppointmentsState] DEFAULT ('') NOT NULL,
    [QuestionnairiesLink]                             NVARCHAR (500)  CONSTRAINT [DF_Contacts_QuestionnairiesLink] DEFAULT ('') NOT NULL,
    [Skills]                                          NVARCHAR (500)  CONSTRAINT [DF_Contacts_Skills] DEFAULT ('') NOT NULL,
    [TraumaticHistory]                                NVARCHAR (500)  CONSTRAINT [DF_Contacts_TraumaticHistory] DEFAULT ('') NOT NULL,
    [NegativeBeliefs]                                 NVARCHAR (500)  CONSTRAINT [DF_Contacts_TraumaticHistory1] DEFAULT ('') NOT NULL,
    [NegativeEmotions]                                NVARCHAR (500)  CONSTRAINT [DF_Contacts_NegativeBeliefs1] DEFAULT ('') NOT NULL,
    [TriggeringEvents]                                NVARCHAR (500)  CONSTRAINT [DF_Contacts_NegativeEmotions1] DEFAULT ('') NOT NULL,
    [DysfunctionalBehaviors]                          NVARCHAR (500)  CONSTRAINT [DF_Contacts_TriggeringEvents1] DEFAULT ('') NOT NULL,
    [SecondaryBenefit]                                NVARCHAR (500)  CONSTRAINT [DF_Contacts_DysfunctionalBehaviors1] DEFAULT ('') NOT NULL,
    [EmotionalProfile]                                NVARCHAR (40)   CONSTRAINT [DF_Contacts_EmotionalProfile] DEFAULT ('') NOT NULL,
    [SpecialistObservations]                          NVARCHAR (MAX)  CONSTRAINT [DF_Contacts_CaseFormulation1] DEFAULT ('') NOT NULL,
    CONSTRAINT [PK_Contacts] PRIMARY KEY CLUSTERED ([ContactId] ASC),
    CONSTRAINT [FK_Contacts_AppointmentCategories] FOREIGN KEY ([AppointmentCategoryId]) REFERENCES [dbo].[AppointmentCategories] ([AppointmentCategoryId]) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT [FK_Contacts_Users] FOREIGN KEY ([GuestId]) REFERENCES [dbo].[Users] ([UserId])
);














































GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'ΠΡΟΕΛΕΥΣΗ ΣΥΝΕΔΡΙΑΣ

DropDown:
ΠΑΡΑΠΟΜΠΗ
INTERNET/SITE
ΠΡΟΩΘΗΤΙΚΗ ΕΝΕΡΓΕΙΑ
ΠΡΟΩΘΗΤΙΚΗ ΠΛΑΤΦΟΡΜΑ
ΓΝΩΣΤΟΣ ΠΕΛΑΤΗ
ΆΛΛΟ', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Contacts', @level2type = N'COLUMN', @level2name = N'SessionOrigin';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Τόπος Κατοικίας', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Contacts', @level2type = N'COLUMN', @level2name = N'Residence';


GO
CREATE NONCLUSTERED INDEX [TherapistIdIndex]
    ON [dbo].[Contacts]([TherapistId] ASC);


GO
CREATE NONCLUSTERED INDEX [FirstLastNameIndex]
    ON [dbo].[Contacts]([FirstName] ASC, [LastName] ASC);


GO
CREATE NONCLUSTERED INDEX [EmailPhoneMobileIndex]
    ON [dbo].[Contacts]([Email] ASC, [Phone1] ASC, [Mobile1] ASC);


GO
CREATE NONCLUSTERED INDEX [DiagnosticianIdIndex]
    ON [dbo].[Contacts]([DiagnosticianId] ASC);


GO
CREATE NONCLUSTERED INDEX [ClinicSupervisorIdIndex]
    ON [dbo].[Contacts]([ClinicSupervisorId] ASC);


GO
CREATE UNIQUE NONCLUSTERED INDEX [UniqueContactCode2]
    ON [dbo].[Contacts]([ContactCode] ASC);


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Τόπος Κατοικίας', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Contacts', @level2type = N'COLUMN', @level2name = N'ChildrenAge';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'Τόπος Κατοικίας', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Contacts', @level2type = N'COLUMN', @level2name = N'Children';

