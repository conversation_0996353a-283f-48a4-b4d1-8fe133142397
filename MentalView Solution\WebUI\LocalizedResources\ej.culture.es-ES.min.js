/*!
*  filename: ej.culture.es-ES.min.js
*  version : 18.1.0.42
*  Copyright Syncfusion Inc. 2001 - 2020. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/

ej.addCulture("es-ES", { name: "es-ES", englishName: "Spanish (Spain, International Sort)", nativeName: "Español (España, alfabetización internacional)", language: "es", numberFormat: { pattern: ["-n"], ",": ".", ".": ",", groupSizes: [3], negativeInfinity: "-Infinito", positiveInfinity: "Infinito", percent: { pattern: ["-n%", "n%"], groupSizes: [3], ",": ".", ".": ",", symbol: "%" }, currency: { pattern: ["-n $", "n $"], groupSizes: [3], ",": ".", ".": ",", symbol: "€" } }, calendars: { standard: { firstDay: 1, days: { names: ["domingo", "lunes", "martes", "miércoles", "jueves", "viernes", "sábado"], namesAbbr: ["do.", "lu.", "ma.", "mi.", "ju.", "vi.", "sá."], namesShort: ["D", "L", "M", "X", "J", "V", "S"] }, months: { names: ["enero", "febrero", "marzo", "abril", "mayo", "junio", "julio", "agosto", "septiembre", "octubre", "noviembre", "diciembre", ""], namesAbbr: ["ene.", "feb.", "mar.", "abr.", "may.", "jun.", "jul.", "ago.", "sep.", "oct.", "nov.", "dic.", ""] }, AM: null, PM: null, patterns: { d: "dd/MM/yyyy", D: "dddd, d' de 'MMMM' de 'yyyy", t: "H:mm", T: "H:mm:ss", f: "dddd, d' de 'MMMM' de 'yyyy H:mm", F: "dddd, d' de 'MMMM' de 'yyyy H:mm:ss", M: "d' de 'MMMM", Y: "MMMM' de 'yyyy" } } } });;