﻿using Admin.Data.Model;

namespace Admin.Business
{
    public interface IRolesBusiness
    {
        Task<Role?> GetRole(Int64 roleId);
        Task<List<Role>> GetAllRoles(Int64 tenantId);
        Task CreateOrUpdateRole(Role role);
        Task DeleteRole(Int64 roleId);
        Task<Role?> CheckRoleExists(Int64 tenantId, Int64 roleId, string name);
        Task CreateRolesForTenant(long tenantId);
    }
}