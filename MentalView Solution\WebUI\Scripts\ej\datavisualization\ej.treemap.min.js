/*!
*  filename: ej.treemap.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min","./../common/ej.globalize.min"],n):n()})(function(){(function(n,t,i){var r;t.widget({ejTreeMap:"ej.datavisualization.TreeMap"},{validTags:["div"],defaults:{locale:null,enableGroupSeparator:!1,colorPath:null,legendItemRendering:null,itemRendering:null,leafItemSettings:{borderThickness:1,borderBrush:"white",showLabels:!1,labelPath:null,gap:0,itemTemplate:null,textOverflow:"none",labelPosition:"topleft",labelVisibilityMode:"visible"},header:null,isHierarchicalDatasource:!1,dataSource:null,groupColorMapping:[],enableDrillDown:!1,drillDownHeaderColor:null,drillDownValue:null,drillDownLevel:0,drillDownSelectionColor:"#000000",colorValuePath:null,weightValuePath:null,treeMapItems:[],showLegend:!1,borderBrush:"white",borderThickness:1,enableResize:!0,enableGradient:!1,isResponsive:!0,itemsLayoutMode:"squarified",levels:[],headerTemplateRendering:null,doubleClick:"",click:"",rightClick:"",drillStarted:null,drillDownItemSelected:null,treeMapGroupSelected:null,treeMapItemSelected:null,legendSettings:{template:"",iconHeight:15,iconWidth:15,title:"",height:0,width:0,mode:"default",leftLabel:"",rightLabel:"",dockPosition:"top",alignment:"near",columnCount:0},rangeColorMapping:[],uniColorMapping:{color:null},desaturationColorMapping:{from:0,to:0,color:"",rangeMinimum:0,rangeMaximum:0},paletteColorMapping:{colors:[]},highlightOnSelection:!1,selectionMode:"default",highlightGroupOnSelection:!1,groupSelectionMode:"default",draggingOnSelection:!1,draggingGroupOnSelection:!1,showTooltip:!1,tooltipTemplate:null,highlightBorderThickness:5,highlightGroupBorderThickness:4,highlightBorderBrush:"gray",highlightGroupBorderBrush:"gray"},dataTypes:{dataSource:"data",treeMapItems:"array",levels:"array",rangeColorMapping:"array",paletteColorMapping:"object",groupColorMapping:"array"},observables:["dataSource","colorValuePath","weightValuePath","showLegend","enableResize","highlightOnSelection","selectionMode","groupSelectionMode","highlightGroupOnSelection","enableDrillDown","drillDownHeaderColor","drillDownSelectionColor","showTooltip","highlightBorderThickness","highlightBorderBrush","itemsLayoutMode","leafItemSettings.borderThickness","leafItemSettings.borderBrush","leafItemSettings.showLabels","leafItemSettings.labelPath","leafItemSettings.textOverflow","legendSettings.iconWidth","legendSettings.iconHeight","legendSettings.dockPosition","legendSettings.height","legendSettings.width","uniColorMapping.color","desaturationColorMapping.from","desaturationColorMapping.to","desaturationColorMapping.color","desaturationColorMapping.rangeMinimum","desaturationColorMapping.rangeMaximum"],_tags:[{tag:"levels",attr:["groupPath","groupGap","headerHeight","showHeader","groupPadding","showLabels","headerHeight","headerTemplate","headerVisibilityMode","labelPosition","labelTemplate","labelVisibilityMode","groupBackground","groupBorderColor","groupBorderThickness"],singular:"level"},{tag:"rangeColorMapping",attr:["color","legendLabel","from","to"],singular:"rangeColor"},{tag:"groupColorMapping",attr:["groupID",{tag:"groupColorMapping.rangeColorMapping",attr:["color","legendLabel","from","to"],singular:"groupRangeColor"},"uniColorMapping.color","desaturationColorMapping.from","desaturationColorMapping.to","desaturationColorMapping.color","desaturationColorMapping.rangeMinimum","desaturationColorMapping.rangeMaximum"],singular:"groupColor"}],dataSource:t.util.valueFunction("dataSource"),colorValuePath:t.util.valueFunction("colorValuePath"),weightValuePath:t.util.valueFunction("weightValuePath"),showLegend:t.util.valueFunction("showLegend"),enableResize:t.util.valueFunction("enableResize"),highlightOnSelection:t.util.valueFunction("highlightOnSelection"),selectionMode:t.util.valueFunction("selectionMode"),highlightGroupOnSelection:t.util.valueFunction("highlightGroupOnSelection"),groupSelectionMode:t.util.valueFunction("groupSelectionMode"),enableDrillDown:t.util.valueFunction("enableDrillDown"),drillDownValue:t.util.valueFunction("drillDownValue"),drillDownLevel:t.util.valueFunction("drillDownLevel"),drillDownHeaderColor:t.util.valueFunction("drillDownHeaderColor"),drillDownSelectionColor:t.util.valueFunction("drillDownSelectionColor"),showTooltip:t.util.valueFunction("showTooltip"),highlightBorderThickness:t.util.valueFunction("highlightBorderThickness"),itemsLayoutMode:t.util.valueFunction("itemsLayoutMode"),highlightBorderBrush:t.util.valueFunction("highlightBorderBrush"),borderThickness:t.util.valueFunction("leafItemSettings.borderThickness"),borderBrush:t.util.valueFunction("leafItemSettings.borderBrush"),showLabels:t.util.valueFunction("leafItemSettings.showLabels"),labelPath:t.util.valueFunction("leafItemSettings.labelPath"),textOverflow:t.util.valueFunction("leafItemSettings.textOverflow"),iconWidth:t.util.valueFunction("legendSettings.iconWidth"),iconHeight:t.util.valueFunction("legendSettings.iconHeight"),dockPosition:t.util.valueFunction("legendSettings.dockPosition"),legendheight:t.util.valueFunction("legendSettings.height"),legendwidth:t.util.valueFunction("legendSettings.width"),itemTemplate:t.util.valueFunction("leafItemSettings.itemTemplate"),uniColor:t.util.valueFunction("uniColorMapping.color"),_color:t.util.valueFunction("desaturationColorMapping.color"),_from:t.util.valueFunction("desaturationColorMapping.from"),_to:t.util.valueFunction("desaturationColorMapping.to"),_rangeMinimum:t.util.valueFunction("desaturationColorMapping.rangeMinimum"),_rangeMaximum:t.util.valueFunction("desaturationColorMapping.rangeMaximum"),_initPrivateProperties:function(){this._svgDocument=null;this._templateDiv=null;this._legenddiv=null;this._drillHeaderDiv=null;this._drillHoverDiv=null;this._legendHeight=0;this._backgroundTile=null;this._tooltipSize={height:0,width:0};this._height=500;this._margintop=null;this._marginleft=null;this._startPoint={x:0,y:0};this._stopPoint={x:0,y:0};this.mouseClickable=!1;this.dragDiv=null;this._initDiv=null;this._width=500;this._svgns="http://www.w3.org/2000/svg";this._prevSelectedItems=[];this._prevSelectedHeaders=[];this._isLevelColors;this.selectedItems==null&&(this.selectedItems=[]);this._browser=null;this._toolTipElement=null;this._rootTreeMapItems=[];this.treemapgroups=[];this._drillHeader=null;this._drilldownItem=null;this._drilldownItems=[];this._treeMapHeaders=[];this._drilldownHeaders=[];this._treeMapLabels=[];this._labelTemplateElements=[];this._itemGroups=[];this._prevSelectedGroupDatas=[];this._interactiveArrow=null;this._legendRects=[]},_setModel:function(n){for(var t in n)switch(t){case"itemsLayoutMode":this.itemsLayoutMode(n[t]);this.refresh()}},_levels:function(){this.refresh()},_rangeColorMapping:function(){this._setColorMappings(this._rootTreeMapItems,this.model)},_groupColorMapping:function(){this._setColorMappings(this._rootTreeMapItems,this.model)},_groupColorMapping_rangeColorMapping:function(){this._setColorMappings(this._rootTreeMapItems,this.model)},_init:function(){this._initPrivateProperties();var t=n("<style>.e-drillGroupRect:hover{ background-color:"+this.drillDownSelectionColor()+";}<\/style>");n("html > head").append(t);this._levelInitialize();this._isSVG()||(document.createStyleSheet().addRule(".vml","behavior:url(#default#VML);display:inline-block"),document.namespaces.add("v","urn:schemas-microsoft-com:vml","#default#VML"));this.refresh()},_destroy:function(){this._unWireEvents();n(this.element).removeClass("e-treemap e-js").find().remove(".e-tooltipelement");n(this.element).empty()},_levelInitialize:function(){var t=this;this.model.levels!=null?n.each(this.model.levels,function(i,u){u=t._checkArrayObject(u,i);var f=new r;n.extend(f,u);n.extend(u,f)}):this.levels.treeMapLevel=new r},_checkArrayObject:function(t,i){var f=["borderBrush","dataSource","groupColorMapping","enableDrillDown","drillDownHeaderColor","drillDownSelectionColor","colorValuePath","weightValuePath","treeMapItems","showLegend","borderBrush","borderThickness","enableResize","enableGradient","isResponsive","itemsLayoutMode","levels","groupBackground","groupBorderColor","groupBorderThickness","groupPadding","groupPath","groupGap","headerHeight","showHeader","showLabels","headerTemplate","labelTemplate","labelPosition","textOverflow","headerVisibilityMode","labelVisibilityMode","color","leafItemSettings","showLabels","labelPath","gap","itemTemplate","legendSettings","template","iconHeight","iconWidth","height","width","mode","leftLabel","rightLabel","dockPosition","alignment","highlightOnSelection","selectionMode","highlightGroupOnSelection","groupSelectionMode","draggingOnSelection","draggingGroupOnSelection","showTooltip","tooltipTemplate","highlightBorderThickness","highlightGroupBorderThickness","highlightBorderBrush","highlightGroupBorderBrush"],u=this;return n.each(t,function(n,t){if(f.indexOf(n)>-1)if(t instanceof Array)u._checkArrayObject(t,n);else if(t!=null&&typeof t=="object"){var e=new r;u._loadIndividualDefaultValues(t,e,typeof n=="number"?i:n)}}),t},_loadIndividualDefaultValues:function(t,i,r){var u=null,f=this;return n.each(i,function(n,t){if(r==n){u=t;return}}),u instanceof Array&&(u=u[0]),n.each(t,function(n,t){t instanceof Array?f._checkArrayObject(t,r):t!=null&&typeof t=="object"&&f._loadIndividualDefaultValues(t,t,typeof n=="number"?r:n)}),n.extend(u,t),n.extend(t,u),t},refresh:function(r){var f,u;if(document.getElementById(this._id)!=null||document.getElementById(this._id)!=i){this._unWireEvents();r||(this._drilldownItems=[]);this.model.enableResize||this.model.isResponsive?this._drilldownItems.length==0&&this._initPrivateProperties():this._initPrivateProperties();n(this.element).empty();this._svgDocument!=null&&n(this._svgDocument).empty();this._height=this.element.height();this._width=this.element.width();this._height==0&&(this._height=this.element[0].parentElement.clientHeight!=0?this.element[0].parentElement.clientHeight:n(document).height());this._width==0&&(this._width=this.element[0].parentElement.clientWidth!=0?this.element[0].parentElement.clientWidth:n(document).width());this.showLegend()&&(this.enableDrillDown()&&this.model.colorPath!=null&&(f=this._getGroupitem(this.model.levels[0].groupPath,this.model.dataSource,this.model.levels[0].headerHeight),f.sort(function(n,t){return parseFloat(t.weight)-parseFloat(n.weight)})),this._sizeCalculation(f));var e=jQuery.uaMatch(navigator.userAgent),o=e.browser.toLowerCase(),s,h=!!navigator.userAgent.match(/Trident\/7\./);if(this._browser=o,n(this.element).css({position:"relative"}),this._height!=0&&this._width!=0){if(this._templateDiv=n("<div class='e-_templateDiv'><\/div>"),this._templateDiv.css({"pointer-events":"none",overflow:"hidden",float:"left","z-index":"2",height:this._height,width:this._width,"margin-top":this._margintop,"margin-left":this._marginleft,position:"absolute",left:"0",top:"0"}),this.enableDrillDown()&&this._drilldownItems.length==0&&(this._drillHeaderDiv=n("<div class='e-_drillHeaderDiv'><\/div>"),this._drillHeaderDiv.css({overflow:"hidden",float:"left","z-index":"3",position:"absolute",left:this.model.legendSettings.dockPosition!="left"?"0":this._legendSize.width,top:"0"}),this._drillHoverDiv=n("<div class='e-_drillHoverDiv'><\/div>"),this._drillHoverDiv.css({overflow:"hidden",float:"left","z-index":"3",height:this._height-30,width:this._width,position:"absolute",left:this.model.legendSettings.dockPosition!="left"?"0":this._legendSize.width,top:"30px"})),this._backgroundTile=n('<div id="backgroundTile" style="overflow:hidden;z-index:0;"><\/div>'),this.showLegend()&&this.model.legendSettings!=null&&this._renderLegend(),this.dataSource()!=null)if(this._drilldownItems.length>0){if(this.enableDrillDown()&&this.model.levels.length>=1&&(this._drilldownItem!=null||this._drilldownItem==null)){for(u=0;u<this._drillHeaderDiv[0].children[0].children.length;u++)this._drillHeaderDiv[0].children[0].children[u].className=="e-drilldownlabel"&&(s=this._drillHeaderDiv[0].children[0].children[u].innerHTML);this._backgroundTile[0].innerHTML="";this._drillHeaderDiv[0].innerHTML="";this._templateDiv[0].innerHTML="";this._drillHoverDiv[0].innerHTML="";this._drillDownHeader(!0,this.model.levels[0],this.dataSource());this._groupdrillDownLevels(this._drilldownItems[this._drilldownItems.length-1],this._drilldownItems.length)}this.showLegend()&&this._generateLegend();this._generateToolTip()}else this.model.dataSource instanceof t.DataManager?this._processOData(this):this._renderTreeMap(this.dataSource());(this.enableResize()||this.model.isResponsive)&&this._on(n(window),"resize",this._treeMapResize)}this._selectItemResize();this._wireEvents();this._trigger("refreshed","")}},_renderDrilledState:function(n,t,i){var r,u,e,o,f,s,h;if(i==0||this.model.drillDownLevel==null||this._initialLevel)this._groupLevels(n),this._renderWithDrilledLevel=!1;else if(r=this._getPreRenderEventData(n,i-1),this._drillCurrentItem=r,this.enableDrillDown())for(u=0;u<r.length;u++)if(this._originalHeaders=[],this.renderHeader=!0,r[u].header==t){if(this.model.levels.length>0){if(this.model.levels[i].groupingLevel=0,this._drilldownHeaders.push(this.model.header||this.model.levels[0].groupPath),this._originalHeaders.push(this.model.header||this.model.levels[0].groupPath),i>1)for(e=this._getPreRenderEventData(this.dataSource(),i-2),this._drillPreviousItem=e,o=r[u].Data[0],f=2;f<=i;f++)s=this.model.levels[f-2].groupPath,h=this._reflection(o,s),r[u]._previousHeader=h,this._drilldownHeaders.push(r[u]._previousHeader),this._originalHeaders.push(r[u]._previousHeader);this._drilldownHeaders.push(t);this._originalHeaders.push(t);this._drillDownHeader(!0,this.model.levels[i-1],r[u])}else this._drilldownHeaders.push(""),this._drillDownHeader(!1);this._groupdrillDownLevels(r[u],i);this._renderWithDrilledLevel=!0;this._initialLevel=!0}},_renderTreeMap:function(n){this.model.browserInfo=this.browserInfo();this.showLegend()&&this.model.legendSettings.mode=="interactive"&&this._generateLegend();this._renderDrilledState(n,this.model.drillDownValue,this.model.drillDownLevel);this.renderHeader=!1;this._generateToolTip();this._renderLabels();this.showLegend()&&this.model.legendSettings.mode!="interactive"&&this._generateLegend()},_renderLegend:function(){this._legenddiv=n("<div class='e-LegendDiv'/>");this._legenddiv.appendTo(this.element);this._legenddiv.css({"pointer-events":"none",overflow:"hidden",position:"absolute","z-index":"2",height:this._legendSize.height,width:this._legendSize.width})},_processOData:function(n){var t=this,i=n.model.dataSource.executeQuery(n.model.query);i.done(function(n){n.result!=null&&t._renderTreeMap(n.result)})},_treeMapResize:function(n){n.preventDefault();n.stopPropagation();var t=this;this.resizeTO&&clearTimeout(this.resizeTO);this.resizeTO=setTimeout(function(){t.refresh(!0)},500)},_unWireEvents:function(){var t=jQuery.uaMatch(navigator.userAgent),i=t.browser.toLowerCase();this._off(n(this.element),"contextmenu",this._tmRightClick);this._off(n(this.element),"click",this._tmClick)},_wireEvents:function(){this._on(n(this.element),"contextmenu",this._tmRightClick);this._on(n(this.element),"click",this._tmClick)},_tmClick:function(n){var t=new Date;this.model.click!=""&&this._trigger("click",{data:{event:n}});this._doubleTaptimer!=null&&t-this._doubleTaptimer<300&&this._tmDoubleClick(n);this._doubleTaptimer=t},_tmDoubleClick:function(n){this.model.doubleClick!=""&&this._trigger("doubleClick",{data:{event:n}})},_tmRightClick:function(n){this.model.rightClick!=""&&this._trigger("rightClick",{data:{event:n}})},_groupLevels:function(t){var r=null,h=this.model.isHierarchicalDatasource,f,u,i,e,o,s;if(this.enableDrillDown()&&(this._originalHeaders=[],this.renderHeader=!0,this.model.levels.length>0?(this.model.levels[0].groupingLevel=0,this._drilldownHeaders.push(this.model.header||this.model.levels[0].groupPath),this._originalHeaders.push(this.model.levels[0].groupPath),this._drillDownHeader(!1,this.model.levels[0],t)):(this._drilldownHeaders.push(""),this._drillDownHeader(!1))),f=this._getTopGroupitem(this.labelPath(),t,0),r=f,this.model.levels.length==0&&(i=this.model.leafItemSettings,this._rootTreeMapItems=f,this._getTopLevels(f[0].innerItems,i),this._generateTreeMapItems(f[0].innerItems,"gray",this.colorValuePath()),this.showLabels()&&this._generateLabels(f[0].innerItems,i)),this.model.levels.length>0)for(u=0;u<=this.model.levels.length;u++)if(i=this.model.levels[u],i!=null&&(n.isNumeric(i.groupGap)||(i.groupGap=0),n.isNumeric(i.groupPadding)||(i.groupPadding=0),n.isNumeric(i.groupBorderThickness)||(i.groupBorderThickness=0)),u!=0){if(e=r,o=this.model.levels[u-1],r=[],i==null&&(i=this.model.leafItemSettings),this._generateSubItems(e,i,r,o,null,null,u-1),this._rootTreeMapItems=r,this.enableDrillDown()){if(u==1)return this._generateTreeMapItems(r,"gray",o.DisplayPath,e),this.showLabels()&&this._generateLabels(r,i),!1}else u==this.model.levels.length&&this._generateTreeMapItems(r,"gray",o.DisplayPath,e);this.showLabels()&&u==this.model.levels.length&&this._generateLabels(r,i)}else s=this._getGroupitem(h?this.labelPath():i.groupPath,f[0].Data,i.headerHeight),this._rootTreeMapItems=s,this._getTopLevels(s,i),r=s},_groupdrillDownLevels:function(t,i){var f=null,c=this.model.isHierarchicalDatasource,h,u,r,e,o,s;if(i==this.model.levels.length-1?this._drillHoverDiv.css("pointer-events","none"):this._drillHoverDiv.css("pointer-events","auto"),this.model.levels.length>0)for(h=!1,u=c?i-1:0;u<this.model.levels.length;u++)r=this.model.levels[u],r!=null&&(n.isNumeric(r.groupGap)||(r.groupGap=0),n.isNumeric(r.groupPadding)||(r.groupPadding=0),n.isNumeric(r.groupBorderThickness)||(r.groupBorderThickness=0)),u!=(c?i-1:0)?(e=f,o=this.model.levels[u-1],f=[],r==null&&(r=this.model.leafItemSettings),u==i+1?(this._generateSubItems(e,r,f,o,null,i+1!=u,t.GroupingLevel),this.showLabels()&&this._generateLabels(f,r)):(this._generateSubItems(e,r,f,o,!0,i+1!=u,t.GroupingLevel),!r.showLabels&&this.showLabels()&&u==this.model.levels.length-1&&this._generateLabels(f,r)),this._rootTreeMapItems=f,u!=i+1&&u!=this.model.levels.length-1||h||(this.showLegend()&&this.model.colorPath&&(this._height=this.element.height(),this._width=this.element.width(),this._sizeCalculation(u==i+1?e:f),this._legenddiv.empty(),this._legenddiv.css({width:this._legendSize.width+"px",height:this._legendSize.height+"px"}),this.model.legendSettings.dockPosition.toLowerCase()=="top"?(this._templateDiv.css({"margin-top":this._margintop}),this._drillHeaderDiv.css({top:this._legendSize.height}),this._drillHoverDiv.css({top:30+this._legendSize.height})):this.model.legendSettings.dockPosition.toLowerCase()=="left"&&(this._drillHeaderDiv.css({left:this._legendSize.width}),this._drillHoverDiv.css({left:this._legendSize.width}),this._templateDiv.css({"margin-left":this._marginleft}))),this._generateTreeMapItems(f,"gray",o.DisplayPath,u==i+1?e:f),h=!0),i==this.model.levels.length-1&&u==i&&r.showLabels&&this._generateLabels(f,r),this.renderHeader=u==t.GroupingLevel?!0:!1):(s=this._getGroupitem(this.model.isHierarchicalDatasource?null:r.groupPath,t.Data,r.headerHeight),this._rootTreeMapItems=s,this._getTopLevels(s,r),f=s);this.showLegend()&&this._generateLegend();this._renderLabels();this.renderHeader=!1;this._trigger("drillDownItemSelected",{level:i,header:this.getDrillDownHeader(this._drilldownHeaders),prevLevel:i-1})},drillDown:function(t,i){n("#"+this._id).find(".e-_templateDiv").parent().children().empty();this._drilldownHeaders=[];var r=this.model.dataSource;this._initialLevel=this.model.drillDownLevel!=0?!1:this._initialLevel;this._renderDrilledState(r,t,i)},_getTopLevels:function(n,i){var f=i.groupGap!=null?i.groupGap:i.gap!=null?i.gap:0,e=this.itemsLayoutMode(),r=0,u=0;this.enableDrillDown()&&(u=30);this.showLegend()&&this.model.legendSettings!=null&&(r=r);this._legendHeight=r;this.enableDrillDown()&&(this._drillHeaderDiv.css({top:this.showLegend()&&this.model.legendSettings.dockPosition.toLowerCase()=="top"?this._legendSize.height:r}),this._drillHoverDiv.css({top:30+(this.showLegend()&&this.model.legendSettings.dockPosition.toLowerCase()=="top"?this._legendSize.height:r)}));e==t.datavisualization.TreeMap.ItemsLayoutMode.SliceAndDiceHorizontal?this._calculateSliceAndDiceItemSize(n,0,r,this._width,this._height,f,u,!0,r):e==t.datavisualization.TreeMap.ItemsLayoutMode.SliceAndDiceVertical?this._calculateSliceAndDiceItemSize(n,0,r,this._width,this._height,f,u,!1,r):e==t.datavisualization.TreeMap.ItemsLayoutMode.SliceAndDiceAuto?this._calculateSliceAndDiceItemSize(n,0,r,this._width,this._height,f,u,null,r):this._calculateSquarifiedItemSize(n,0,r,this._width,this._height,f,u,r)},_generateSubItems:function(n,r,u,f,e,o,s,h){var l=f.groupPadding,c,v,a,p,b,w;l==""&&(l=0);var nt=this.itemsLayoutMode(),tt=n.length,k=this.model.isHierarchicalDatasource?null:r.groupPath;k==null&&(k=this.labelPath());k==null&&(k=this.weightValuePath());var d=r.groupGap!=null?r.groupGap:r.gap!=null?r.gap:0,g=(f.showHeader||f.showHeader==null)&&!this.enableDrillDown()?f.headerHeight:this.renderHeader&&f.showHeader?f.headerHeight:0,y=0;for(c=0;c<tt;c++){if(v=this._getGroupitem(k,n[c].Data,r.headerHeight,this.model.isHierarchicalDatasource?f.groupPath:null),h||(nt==t.datavisualization.TreeMap.ItemsLayoutMode.SliceAndDiceHorizontal?this._calculateSliceAndDiceItemSize(v,n[c].LeftPosition+parseFloat(l),n[c].TopPosition+parseFloat(l),n[c].ItemWidth-(f.groupBorderThickness+2*parseFloat(l)),n[c].ItemHeight-(f.groupBorderThickness+2*parseFloat(l)),parseFloat(d),g,!0,0):nt==t.datavisualization.TreeMap.ItemsLayoutMode.SliceAndDiceVertical?this._calculateSliceAndDiceItemSize(v,n[c].LeftPosition+parseFloat(l),n[c].TopPosition+parseFloat(l),n[c].ItemWidth-(f.groupBorderThickness+2*parseFloat(l)),n[c].ItemHeight-(f.groupBorderThickness+2*parseFloat(l)),parseFloat(d),g,!1,0):nt==t.datavisualization.TreeMap.ItemsLayoutMode.SliceAndDiceAuto?this._calculateSliceAndDiceItemSize(v,n[c].LeftPosition+parseFloat(l),n[c].TopPosition+parseFloat(l),n[c].ItemWidth-(f.groupBorderThickness+2*parseFloat(l)),n[c].ItemHeight-(f.groupBorderThickness+2*parseFloat(l)),parseFloat(d),g,null,0):this._calculateSquarifiedItemSize(v,n[c].LeftPosition+parseFloat(l),n[c].TopPosition+parseFloat(l),n[c].ItemWidth-(f.groupBorderThickness+2*parseFloat(l)),n[c].ItemHeight-(f.groupBorderThickness+2*parseFloat(l)),parseFloat(d),g,0),a=this._rootTreeMapItems[c],a.headerHeight=f.headerHeight,a.showHeader=f.showHeader,a.headerWidth=n[c].ItemWidth,a.headerTemplate=f.headerTemplate,a.headerLeftPosition=n[c].LeftPosition,a.headerTopPosition=n[c].TopPosition,this.enableDrillDown()&&this._createBackGround(a,f,n[c],o),(f.showHeader||f.showHeader==null)&&(this.enableDrillDown()?(this.renderHeader||(this.previousItem=a),(s==0||s==i||s==null)&&(this.previousItem=null),this.renderHeader&&(this._trigger("headerTemplateRendering",{levelItems:this._rootTreeMapItems,childItems:a.Data,headerItem:a,groupPath:f.groupPath,groupingLevel:t.util.isNullOrUndefined(s)?0:s,prevItem:this.previousItem}),this._generateHeaders(a,f))):(this._createBackGround(a,f,n[c]),this._trigger("headerTemplateRendering",{levelItems:this._rootTreeMapItems,childItems:a.Data,headerItem:a,groupPath:f.groupPath,groupingLevel:s,prevItem:f}),this._generateHeaders(a,f)))),n[c].ChildtreeMapItems=v,n[c].GroupingLevel=this.model.levels.indexOf(f)+1,this.model.levels.indexOf(f)!=0||h){if(this.model.levels.indexOf(f)>0&&!h&&this.model.groupColorMapping!=null&&this.model.groupColorMapping.length>0)for(p=0;p<this.model.groupColorMapping.length;p++)b=this.model.groupColorMapping[p],b.groupID==n[c].ParentHeader&&this._setColorMappings(v,b,!0)}else if(this.model.groupColorMapping!=null&&this.model.groupColorMapping.length>0)for(this._isLevelColors=!0,p=0;p<this.model.groupColorMapping.length;p++)b=this.model.groupColorMapping[p],b.groupID==a.header&&this._setColorMappings(v,b,!0);for(w=0;w<v.length;w++)u[y]=v[w],v[w].backgroundColor==null&&(v[w].backgroundColor=n[c].backgroundColor),v[w].backgroundOpacity==null&&(v[w].backgroundOpacity=n[c].backgroundOpacity),(this.enableDrillDown()||u[y].ItemHeight<a.ItemHeight-a.headerHeight)&&!h&&(u[y].ParentHeader=a.header,u[y].ItemHeight-=l,u[y].ItemWidth-=l,u[y].LeftPosition+=f.groupBorderThickness,u[y].TopPosition+=f.groupBorderThickness),y++}e==i&&f.showLabels&&!h&&this._generateLabels(n,f)},_createBackGround:function(t,i,r,u){var f=n("<div />"),o=t.showHeader||t.showHeader==null||this.enableDrillDown()?t.ItemHeight-2*i.groupBorderThickness:0,h=t.showHeader||t.showHeader==null||this.enableDrillDown()?t.TopPosition:0,e,s;f.css({height:o+"px",width:t.ItemWidth-2*i.groupBorderThickness+"px",left:t.LeftPosition+"px",top:h+"px","border-style":"solid","border-width":i.groupBorderThickness+"px",position:"absolute"});i.groupBorderColor!=null&&f.css("border-color",i.groupBorderColor);i.groupBackground!=null&&f.css("background-color",i.groupBackground);this.treemapgroups.push({header:t.header,element:f});f.appendTo(this._backgroundTile);o=parseFloat(f[0].style.top)-30-this._legendHeight+"px";this.enableDrillDown()&&!u&&(e=n("<div class='e-drillGroupRect'/>"),e.css({height:f[0].style.height,width:f[0].style.width,left:f[0].style.left,top:o,position:"absolute",opacity:.2,display:"block"}),this._drillHoverDiv.append(e),n(e).mousedown({treemap:this,level:i,param1:r},this._headerClickFunction),n(e).mouseleave({treemap:this,param1:t.header},this._mouseLeaveFunction),s={data:t.Data,label:t.header,header:t.header},n(e).mousemove({treemap:this,hoverItem:s},this._mouseRectHoverFunction));n(f).mousedown({treemap:this,level:i,param1:r},this._headerClickFunction);n(f).mousemove({treemap:this,hoverItem:t.Data},this._mouseRectHoverFunction)},_mouseRectHoverFunction:function(t){var r=t.data.treemap,l=t.data.hoverItem,i,f,s,h,c,u,e,o;r.showTooltip()&&(i=r._toolTipElement,f=r.model.tooltipTemplate,i!=null&&f!=null&&(n(i).css({left:t.pageX+10,top:t.pageY+10,display:"block"}),s=n("#"+f).render(l),n(i).html(s),h=i[0]!=null?i[0].clientHeight:i.clientHeight,c=i[0]!=null?i[0].clientWidth:i.clientWidth,r._tooltipSize={height:h,width:c}),u=r._tooltipSize,u.width+t.pageX>=r._width&&(e=t.pageX-u.width,e<0&&(e=10)),u.height+t.pageY>=r._height&&(o=t.pageY-u.height,o<0&&(o=10)),i!=null&&(r.enableDrillDown()?n(i).css({left:t.pageX+10,top:t.pageY+10,display:"block"}):n(i).css({left:t.pageX+10,top:t.pageY+10,display:"none"})))},_mouseLeaveFunction:function(t){var i=t.data.treemap;i.showTooltip()&&i._toolTipElement!=null&&n(i._toolTipElement).css("display","none")},_headerClickFunction:function(t){var e=t.data.level,r=t.data.param1,v=t.ctrlKey,i=t.data.treemap,l,h,o,a,u,c,s,f;if(i.highlightGroupOnSelection())if(i._browser!="msie"&&(l=i._backgroundTile[0].children[i._backgroundTile[0].children.length-0],i._backgroundTile[0].insertBefore(this,l)),i.groupSelectionMode()=="multiple"&&v)n.inArray(this,i._prevSelectedHeaders)==-1?(n(this).css({"border-width":i.model.highlightGroupBorderThickness,"border-color":i.highlightBorderBrush()}),i._prevSelectedHeaders.push(this),i._prevSelectedGroupDatas.push(t.data.Param1)):(n(this).css({"border-width":e.groupBorderThickness,"border-color":e.groupBorderColor}),h=i._prevSelectedHeaders.indexOf(this),i._prevSelectedHeaders.splice(h,1),i._prevSelectedGroupDatas.splice(h,1));else{for(o=0;o<i._prevSelectedHeaders.length;o++)n(i._prevSelectedHeaders[o]).css({"border-width":e.groupBorderThickness,"border-color":e.groupBorderColor});n.inArray(this,i._prevSelectedHeaders)==-1?(n(this).css({"border-width":i.model.highlightGroupBorderThickness,"border-color":i.highlightBorderBrush()}),i._prevSelectedHeaders=[],i._prevSelectedHeaders.push(this)):i._prevSelectedHeaders=[];i._prevSelectedGroupDatas.push(t.data.Param1)}if(i.enableDrillDown()&&i._trigger("drillStarted",t),!t.cancel&&i.enableDrillDown()&&i.model.levels.length>1&&(i._drilldownItem!=null&&i._drilldownItem.header!=r.header||i._drilldownItem==null)){for(a="",u=0;u<i._drillHeaderDiv[0].children[0].children.length;u++)i._drillHeaderDiv[0].children[0].children[u].className=="e-drilldownlabel"&&(a=i._drillHeaderDiv[0].children[0].children[u].innerHTML);if(i._backgroundTile[0].innerHTML="",i._labelTemplateElements=[],i._drillHeaderDiv[0].innerHTML="",i._templateDiv[0].innerHTML="",i._drillHoverDiv[0].innerHTML="",i._originalHeaders.push(r.header),i._drilldownHeaders.push(r.header),c=i.model.levels[i._drilldownHeaders.length-1],c.groupingLevel=r.GroupingLevel,i._drillDownHeader(!0,c,r.Data),i._drilldownItem=r,i._renderWithDrilledLevel)for(s=i._drillCurrentItem,f=0;f<s.length;f++)s[f].header==i.model.drillDownValue&&i._drilldownItems.length==0&&i._drilldownItems.push(s[f]);return i._drilldownItems.push(r),i._groupdrillDownLevels(r,i._drilldownItems.length),!1}},_rectMouseDown:function(n){var t=n.data.treeMap;t.isTouch(n)&&t.model.doubleClick!=""&&t.model.rightClick!=""&&n.preventDefault()},_docClickFunction:function(i){var f=i.data.treemap,u=this._toolTipElement,r=this;clearTimeout(r.model.timer);u&&(r.model.trackerElement=u,r.model.timer=setTimeout(function(){r.model.trackerElement&&n(r.model.trackerElement).fadeOut(500,function(){r.model.trackerElement=null})},1200));t.isTouchDevice()&&this.model.rightClick!=""&&new Date-this._longPressTimer>1500&&this._tmRightClick(i)},mousePosition:function(n){return!t.util.isNullOrUndefined(n.pageX)&&n.pageX>0?n:n.originalEvent&&!t.util.isNullOrUndefined(n.originalEvent.pageX)&&n.originalEvent.pageX>0?n.originalEvent:n.originalEvent&&n.originalEvent.changedTouches!=i&&!t.util.isNullOrUndefined(n.originalEvent.changedTouches[0].pageX)&&n.originalEvent.changedTouches[0].pageX>0?n.originalEvent.changedTouches[0]:n},_highlightTreemap:function(t){var i=t.data.treemap,v=t.data.Param1,h=i.model.levels[0],c,a=t.ctrlKey,l,e=i.mousePosition(t),f,r,u,s,o;if(i.highlightGroupOnSelection()){for(f=0;f<i.treemapgroups.length;f++)if(l=i.treemapgroups[f],r=i.treemapgroups[f].element,u=r[0].getBoundingClientRect(),u.left<e.clientX&&u.left+u.width>e.clientX&&u.top<e.clientY&&u.top+u.height>e.clientY){n(r).css({"border-width":i.model.highlightGroupBorderThickness,"border-color":i.highlightBorderBrush(),"box-sizing":"border-box","-moz-box-sizing":"border-box","-webkit-box-sizing":"border-box"});c=!0;break}if(c)if(i.groupSelectionMode()=="multiple"&&a)n.inArray(r,i._prevSelectedHeaders)==-1?(n(r).css({"border-width":i.model.highlightGroupBorderThickness,"border-color":i.highlightBorderBrush()}),i._prevSelectedHeaders.push(l),i._prevSelectedGroupDatas.push(t.data.Param1)):(n(r).css({"border-width":h.groupBorderThickness,"border-color":h.groupBorderColor}),s=i._prevSelectedHeaders.indexOf(l),i._prevSelectedHeaders.splice(s,1),i._prevSelectedGroupDatas.splice(s,1));else{for(o=0;o<i._prevSelectedHeaders.length;o++)n(i._prevSelectedHeaders[o].element).css({"border-width":h.groupBorderThickness,"border-color":h.groupBorderColor});n.inArray(r,i._prevSelectedHeaders)==-1?(n(r).css({"border-width":i.model.highlightGroupBorderThickness,"border-color":i.highlightBorderBrush()}),i._prevSelectedHeaders=[],i._prevSelectedHeaders.push(l)):i._prevSelectedHeaders=[];i._prevSelectedGroupDatas.push(t.data.Param1)}i._trigger("treeMapGroupSelected",{selectedGroups:i._prevSelectedHeaders,orginalEvent:t})}if(i.highlightOnSelection()){for(f=0;f<i._rootTreeMapItems.length;f++)if(r=i._rootTreeMapItems[f],u=r.Rectangle.getBoundingClientRect(),u.left<e.clientX&&u.left+u.width>e.clientX&&u.top<e.clientY&&u.top+u.height>e.clientY){n(r.Rectangle).css({"border-width":i.highlightBorderThickness(),"border-color":i.highlightBorderBrush(),"box-sizing":"border-box","-moz-box-sizing":"border-box","-webkit-box-sizing":"border-box"});c=!0;break}if(c){if(i.selectionMode()=="multiple"&&a)n.inArray(r.Rectangle,i._prevSelectedItems)==-1?(n(r.Rectangle).css({"border-width":i.highlightBorderThickness(),"border-color":i.highlightBorderBrush(),"box-sizing":"border-box","-moz-box-sizing":"border-box","-webkit-box-sizing":"border-box"}),i._prevSelectedItems.push(r.Rectangle),i.selectedItems.push(r.Data)):(n(r.Rectangle).css({"border-width":i.borderThickness(),"border-color":i.borderBrush()}),s=i._prevSelectedItems.indexOf(r.Rectangle),i._prevSelectedItems.splice(s,1),i.selectedItems.splice(s,1));else{for(o=0;o<i._prevSelectedItems.length;o++)n(i._prevSelectedItems[o]).css({"border-width":i.borderThickness(),"border-color":i.borderBrush()});n.inArray(r.Rectangle,i._prevSelectedItems)==-1?(n(r.Rectangle).css({"border-width":i.highlightBorderThickness(),"border-color":i.highlightBorderBrush()}),i._prevSelectedItems=[],i.selectedItems=[],i._prevSelectedItems.push(r.Rectangle),i.selectedItems.push(r.Data)):(i._prevSelectedItems=[],i.selectedItems=[])}i._trigger("treeMapItemSelected",{selectedItems:i.selectedItems,originalEvent:t})}}},doubleTap:function(n){var i=n.targetTouches?n.targetTouches[0]:n,r=i.pageX,u=i.pageY,o=this.model,f=200,t=n.data.treemap,e;t.model.cachedX=t.model.cachedX||r;t.model.cachedY=t.model.cachedY||u;e=(new Date).getTime();Math.abs(r-t.model.cachedX)<f&&Math.abs(u-t.model.cachedY)<f&&t.model.tapNum++},rectClick:function(n){var i=n.data.treemap;i.doubleTap(n);i.isTouch(n)||i._highlightTreemap(n);t.isTouchDevice()&&this.model.rightClick!=""&&(this._longPressTimer=new Date)},dragDown:function(t){var i=t.data.treemap,e=i.model.levels[0],r=i._svgDocument,u,f,o;if(t.type=="mousedown"?(i._startPoint={x:t.pageX-r[0].offsetLeft,y:t.pageY-r[0].offsetTop},i._stopPoint={x:t.pageX-r[0].offsetLeft,y:t.pageY-r[0].offsetTop}):t.type=="touchstart"?(i._startPoint={x:t.originalEvent.changedTouches[0].pageX-r[0].offsetLeft,y:t.originalEvent.changedTouches[0].pageY-r[0].offsetTop},i._stopPoint={x:t.originalEvent.changedTouches[0].pageX-r[0].offsetLeft,y:t.originalEvent.changedTouches[0].pageY-r[0].offsetTop}):t.type=="MSPointerDown"&&(i._startPoint={x:t.originalEvent.pageX-r[0].offsetLeft,y:t.originalEvent.pageY-r[0].offsetTop},i._stopPoint={x:t.originalEvent.pageX-r[0].offsetLeft,y:t.originalEvent.pageY-r[0].offsetTop}),i.mouseClickable=!0,i.model.draggingGroupOnSelection&&i._prevSelectedGroupDatas.length>0){for(u=0;u<i._prevSelectedGroupDatas.length;u++)f=i._prevSelectedGroupDatas[u][0],o=f.getBoundingClientRect(),n.inArray(f,i._prevSelectedGroupDatas[u][0])==-1&&n(i._prevSelectedGroupDatas[u]).css({"border-width":e.groupBorderThickness,"border-color":i.model.highlightGroupBorderBrush});i._prevSelectedGroupDatas=[]}if(i.model.draggingOnSelection&&i._prevSelectedItems.length>0){for(u=0;u<i._prevSelectedItems.length;u++)f=i._prevSelectedItems[u].rectangle,o=f.getBoundingClientRect(),n.inArray(f,i._prevSelectedItems[u].rectangle)==-1&&n(i._prevSelectedItems[u].rectangle).css({"border-width":e.groupBorderThickness,"border-color":i.model.highlightGroupBorderBrush});i._prevSelectedItems=[]}},dragMove:function(t){var f,e,i=t.data.treemap,r=i._svgDocument,u;(i.model.draggingGroupOnSelection||i.model.draggingOnSelection)&&i.mouseClickable&&(t.type=="mousemove"?i._stopPoint={x:t.pageX-r[0].offsetLeft,y:t.pageY-r[0].offsetTop}:t.type=="touchmove"?i._stopPoint={x:t.originalEvent.changedTouches[0].pageX-r[0].offsetLeft,y:t.originalEvent.changedTouches[0].pageY-r[0].offsetTop}:t.type=="MSPointerMove"&&(i._stopPoint={x:t.originalEvent.pageX-r[0].offsetLeft,y:t.originalEvent.pageY-r[0].offsetTop}),n("#dragDiv").remove(),u=n('<div id = "dragDiv"><\/div>'),f=Math.abs(i._stopPoint.x-i._startPoint.x),e=Math.abs(i._stopPoint.y-i._startPoint.y),n(u).css({top:Math.min(i._startPoint.y,i._stopPoint.y),left:Math.min(i._startPoint.x,i._stopPoint.x),width:f,height:e,border:"1px solid green",position:"absolute","z-index":100}),n(u).appendTo("#svgDocument"))},dragUp:function(t){var i=t.data.treemap,o=Math.abs(i._stopPoint.x-i._startPoint.x),s=Math.abs(i._stopPoint.y-i._startPoint.y),e=i._svgDocument,r,f,u;if((i.model.draggingGroupOnSelection||i.model.draggingOnSelection)&&(n("#dragDiv").remove(),n("#dragDiv").css({display:"none"}),i.mouseClickable=!1),i.model.draggingGroupOnSelection){for(r=0;r<i.treemapgroups.length;r++)f=i.treemapgroups[r].element,u=f[0].getBoundingClientRect(),u.left-e[0].offsetLeft+u.width<Math.min(i._startPoint.x,i._stopPoint.x)||Math.min(i._startPoint.x,i._stopPoint.x)+o<u.left-e[0].offsetLeft||u.top-e[0].offsetTop+u.height<Math.min(i._startPoint.y,i._stopPoint.y)||Math.min(i._startPoint.y,i._stopPoint.y)+s<u.top-e[0].offsetTop||i._contains(i._prevSelectedGroupDatas,f)||i._prevSelectedGroupDatas.push(f);for(r=0;r<i._prevSelectedGroupDatas.length;r++)f=i._prevSelectedGroupDatas[r][0],u=f.getBoundingClientRect(),n(f).css({"border-width":i.model.highlightGroupBorderThickness,"border-color":i.model.highlightGroupBorderBrush,"box-sizing":"border-box","-moz-box-sizing":"border-box","-webkit-box-sizing":"border-box"});i.selectedItems.push(i._prevSelectedGroupDatas)}if(i.model.draggingOnSelection){for(r=0;r<i._rootTreeMapItems.length;r++)f=i._rootTreeMapItems[r],u=f.rectangle.getBoundingClientRect(),u.left-e[0].offsetLeft+u.width<Math.min(i._startPoint.x,i._stopPoint.x)||Math.min(i._startPoint.x,i._stopPoint.x)+o<u.left-e[0].offsetLeft||u.top-e[0].offsetTop+u.height<Math.min(i._startPoint.y,i._stopPoint.y)||Math.min(i._startPoint.y,i._stopPoint.y)+s<u.top-e[0].offsetTop||i._contains(i._prevSelectedItems,f)||i._prevSelectedItems.push(f);for(r=0;r<i._prevSelectedItems.length;r++)f=i._prevSelectedItems[r].rectangle,u=f.getBoundingClientRect(),n(f).css({"border-width":i.model.highlightGroupBorderThickness,"border-color":i.model.highlightGroupBorderBrush,"box-sizing":"border-box","-moz-box-sizing":"border-box","-webkit-box-sizing":"border-box"});i.selectedItems.push(i._prevSelectedItems)}},_measureText:function(t,r,u){var h=n(document).find("#measureTex"),f,o,s,e,v;n("#measureTex").css("display","block");h.length==0?(f=document.createElement("text"),n(f).attr({id:"measureTex"}),document.body.appendChild(f)):f=h[0];var c=null,l=null,a=null,y=null;if(typeof t=="string"&&(t.indexOf("<")>-1||t.indexOf(">")>-1)){for(o=t.split(" "),s=0;s<o.length;s++)o[s].indexOf("<br/>")==-1&&(o[s]=o[s].replace(/[<>]/g,"&"));t=o.join(" ")}return f.innerHTML=t,u!=i&&u.size==i&&(e=u,e=e.split(" "),c=e[0],l=e[1],a=e[2],y=e[3]),u!=null&&(f.style.fontSize=u.size>0?u.size+"px":u.size?u.size:l,f.style.fontStyle&&(f.style.fontStyle=u.fontStyle?u.fontStyle:c),f.style.fontFamily=u.fontFamily?u.fontFamily:a),f.style.backgroundColor="white",f.style.position="absolute",f.style.top=-100,f.style.left=0,f.style.visibility="hidden",f.style.whiteSpace="nowrap",r&&(f.style.maxwidth=r+"px"),v={width:f.offsetWidth,height:f.offsetHeight},n("#measureTex").css("display","none"),v},_rowsCalculation:function(n,t,i,r,u,f){var d=this,nt=d.model,l,o=[],y,a,s=n?n.toString():"",g=s.length,w=this._measureText(s,t,u).width,b=this._measureText(s,t,u).height,v=s.split(" "),e=0,h=0,p=Math.round(t),k=v.length,f=f.toLowerCase(),i=f=="centerleft"||f=="center"||f=="centerright"?i/2:i,c;if(r=="wrap"||r=="hide"){if(w>p)for(c=1;c<=s.length;c++)if(n=s.substring(0,c),w=this._measureText(n,t,u).width,w>p){if(h=h+1,n=s.substring(0,c-1),n=="")break;h*b<i&&(o[e]=n);s=s.slice(c-1,g);e++;c=0}return h=h+1,h*b<i&&(o[e]=n),(r=="hide"||r=="wrap"&&(f=="bottomleft"||f=="bottomright"||f=="bottomcenter"))&&(o=o.slice(0,1)),o}if(r=="wrapbyword"){for(e=0;e<k;e++)if(l=v[e],y=this._measureText(l,t,u).width,h=h+1,y<p){while(e<k)if(y=this._measureText(l,t,u).width,a=v[e+1]?this._measureText(v[e+1],t,u).width:0,a=Math.round(a),y+a<p&&a>0)l=l.concat(" "+v[e+1]),e++;else break;h*b<i&&o.push(l)}return(f=="bottomleft"||f=="bottomright"||f=="bottomcenter")&&(o=o.slice(0,1)),o}},getStyleRuleValue:function(n,t){for(var i,r,o,u,e=typeof i!="undefined"?[i]:document.styleSheets,f=0,h=e.length;f<h;f++){i=e[f];try{if(!i.cssRules)continue;for(r=0,o=i.cssRules.length;r<o;r++)if(u=i.cssRules[r],u.selectorText&&u.selectorText.split(",").indexOf(t)!==-1)return u.style[n]}catch(s){if(s.name.toLowerCase()!=="securityerror")throw s;}}return null},_generateLabels:function(r,u){for(var f,tt,l,b,k,g,h,o,e,nt,p,a,s,lt,c=this.model.leafItemSettings.textOverflow.toLowerCase(),w=0;w<r.length;w++){if(f=r[w],u.labelTemplate!=null&&c!="none")var it=document.getElementById(u.labelTemplate),rt=it.innerHTML,ut=n(rt),ft=ut.find("label").eq(0).text(),et=ft.replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g,""),ot=f.Data[et];if(u.labelTemplate!=null&&c=="none")tt=n("#"+u.labelTemplate),l=n("<div style='overflow:hidden;display:block;position:absolute;pointer-events: none;'><\/div>"),l[0].data=f,f.labelPosition=u.labelPosition,f.labelVisibilityMode=u.labelVisibilityMode,b=n.templates(tt.html()),k="",k=f.Data instanceof Array?b.render(f.Data[0]):b.render(f.Data),n(l).html(k),this._templateDiv.append(l),this._labelTemplateElements.push(l);else{var d=8,v=5,y=this.getStyleRuleValue("font-size",".e-treemap-label");if(y=t.util.isNullOrUndefined(y)?"14px":y,g=u.labelTemplate!=null?ot:f.label,c=this.model.leafItemSettings.textOverflow.toLowerCase(),u.labelTemplate==null)var st=Math.round(f.ItemWidth-2*d),ht=Math.round(f.ItemHeight-2*v-(f.showHeader?f.headerHeight:0)),ct={fontWeight:"Normal",size:y,color:"black"};else var ct={fontWeight:"Normal",size:"16px",color:"black"},st=Math.round(f.ItemWidth),ht=Math.round(f.ItemHeight);for(c!="none"?h=this._rowsCalculation(g,st,ht,c,ct,u.labelPosition):(h=[],h.push(g)),o=[],e=0;e<h.length;e++)o[e]=u.labelTemplate==null?n('<label class="e-treemap-label" >'+h[e]+"<\/label>"):n("<label>"+h[e]+"<\/label>"),this.enableDrillDown()?n(o[e]).css({position:"absolute",color:"black",overflow:"hidden",left:f.LeftPosition+d+"px",top:f.TopPosition+v+"px","font-weight":"normal","pointer-events":"none"}):n(o[e]).css({position:"absolute",color:u.labelTemplate==null?"white":"black",overflow:"hidden",left:f.LeftPosition+d+"px",top:f.TopPosition+v+"px","font-weight":"normal","pointer-events":"none"}),this._treeMapLabels.push(o[e]),this._templateDiv.append(o[e]),nt=0,p=0,u.groupPadding!=i&&(nt=u.groupPadding),u.groupBorderThickness!=i&&(p=u.groupBorderThickness),a=o[e][0].getBoundingClientRect().width>0?o[e][0].getBoundingClientRect().width+nt+p+2:f.label.length*8,s=o[e][0].getBoundingClientRect().height,s==0&&f.ItemHeight>18?s=18:s>0&&s>f.ItemHeight&&(s=f.ItemHeight-v),n(o[e]).css({width:a+"px",height:s+"px"}),f.labelPosition=u.labelPosition,f.labelVisibilityMode=p,f.groupPadding=u.groupPadding,f.groupBorderThickness=u.groupBorderThickness,o[e][0].data=f,u.labelVisibilityMode==t.datavisualization.TreeMap.VisibilityMode.HideOnExceededLength?(a>f.ItemWidth&&(n(o[e]).css({display:"none"}),f.Rectangle!=null&&(f.Rectangle.title=f.label)),lt=u.labelVisibilityMode):a<f.ItemWidth?n(o[e]).css({width:a}):n(o[e]).css({width:f.ItemWidth});this._labelTemplateElements.push(o)}}},_generateToolTip:function(){var t,i,r;this.showTooltip()&&(t=document.documentMode==8?document.querySelectorAll("e-tooltipelement"):document.getElementsByClassName("e-tooltipelement"+this._id),t!=null&&t.length==0?(i=n("<div><\/div>").attr("class","e-tooltipelement"+this._id).css({position:"absolute","z-index":"1000",display:"none","pointer-events":"none"}),n(document.body).append(i),this._toolTipElement=i):this._toolTipElement=t[0],this.model.tooltipTemplate==null&&(r=this.labelPath()!=null?this.labelPath():this.weightValuePath(),r!=null&&(this.model.tooltipTemplate="defaultTooltip",this.element.append(n('<div id="defaultTooltip" style="display:none;"><div style="margin-left:10px;margin-top:-25px;"><div class="e-defaultToolTip"><p style="margin-top:-4px"><label  style="color:rgb(27, 20, 20);font-size:14px;font-weight:normal;font-family:Segoe UI">{{:#data["'+r+'"]}}<\/label><\/p><\/div><\/div><\/div>')))))},_generateLegend:function(){var u,a,o;if(this.model.rangeColorMapping!=null||this.model.colorPath!=null){var e=this.model.rangeColorMapping!=null&&this.model.rangeColorMapping.length>0?this.model.rangeColorMapping:this._legendItem,c=this.model.rangeColorMapping!=null&&this.model.rangeColorMapping.length>0?!0:!1,s=this.model.legendSettings;if(s.mode!=t.datavisualization.TreeMap.LegendMode.Interactive){var n=0,r=0,h=0,v=this.iconWidth()+5,y=this.iconHeight()+15,f,l=this.model.legendSettings.columnCount;for(u=0;u<e.length;u++)f={color:c?e[u].color:e[u]._color?e[u]._color:e[u].color,legendLabel:e[u].legendLabel,dataSource:c?i:e[u].Data,mapping:c?e[u]:i},this._trigger("legendItemRendering",{model:this.model,data:f}),a=this._calcWidth(f.legendLabel),o=this.iconWidth()+10+a,l!=0?u%l!=0?(this._drawLegend(f,n,r),n+=o+5):(u!=0&&(r+=this.iconHeight()+18),n=0,this._drawLegend(f,n,r),n+=o+5):this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Top||this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Bottom?this._legendSize.width<n+o?(r+=this.iconHeight()+18,n=0,this._drawLegend(f,n,r),n+=o):(this._drawLegend(f,n,r),n+=o):this._legendSize.height<r+this.iconHeight()?(r=0,n+=h+10,this._drawLegend(f,n,r),h=0,r+=this.iconHeight()+18):(this._drawLegend(f,n,r),h=Math.max(h,o),r+=this.iconHeight()+18)}else this._drawInteractiveLegend();(this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Top||this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Bottom)&&s.alignment==t.datavisualization.TreeMap.Alignment.Center?this._legenddiv.css({"margin-left":this._width/2-this._legendSize.width/2}):(this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Top||this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Bottom)&&s.alignment==t.datavisualization.TreeMap.Alignment.Far?this._legenddiv.css({"margin-left":this._width-this._legendSize.width}):(this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Left||this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Right)&&s.alignment==t.datavisualization.TreeMap.Alignment.Center?this._legenddiv.css({"margin-top":this._height/2-this._legendSize.height/2}):(this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Left||this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Right)&&s.alignment==t.datavisualization.TreeMap.Alignment.Far&&this._legenddiv.css({"margin-top":this._height-this._legendSize.height});this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Bottom?this._legenddiv.css({"margin-top":this._height+5}):this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Right&&this._legenddiv.css({"margin-left":this._width+5})}},_drawLegend:function(t,i,r){var f,u;f=this.model.legendSettings.template=="Ellipse"?this._getEllipseLegend(this.model.legendSettings,i,r):this._getRectLegend(this.model.legendSettings,i,r);i+=this.iconWidth()+5;n(f).css("background-color",t.color);f.appendTo(this._legenddiv);u=n("<div class='e-treemap-legendLabel'/>");u.css({left:i+"px",top:r+"px",position:"absolute"});u[0].innerHTML=t.legendLabel;u.appendTo(this._legenddiv)},_calcWidth:function(t){var i=n('<span class ="e-treemap-legendLabel" >'+t+"<\/span>"),r;return n("body").append(i),r=i.width(),i.remove(),r},_getEllipseLegend:function(t,i,r){var u=n("<div class='e-mapLegend'/>");return u.css({height:this.iconHeight()+"px",width:this.iconWidth()+"px","border-radius":this.iconHeight()/2+"px",left:i+"px",top:r+"px",position:"absolute"}),u},_getRectLegend:function(t,i,r){var u=n("<div />");return u.css({height:this.iconHeight()+"px",width:this.iconWidth()+"px",left:i+"px",top:r+"px",position:"absolute"}),u},_getPositionFromParent:function(n,t){var i=n.getBoundingClientRect(),r=t.getBoundingClientRect(),u=window.SVGSVGElement?i.width:i.right-i.left,f=window.SVGSVGElement?i.height:i.bottom-i.top;return{left:i.left-r.left,top:i.top-r.top,height:f,width:u}},_renderLabels:function(){for(var u,r=0;r<this._labelTemplateElements.length;r++)for(u=0;u<this._labelTemplateElements[r].length;u++){var f=this._labelTemplateElements[r][u],h=f[0]?f[0]:f,o=this._getPositionFromParent(h,this._templateDiv[0]),i=h.data,e=this._getDockPositionPoint({left:i.LeftPosition,top:i.TopPosition,width:i.ItemWidth,height:i.ItemHeight},o,i),s=i.labelPosition.toLowerCase();(s=="bottomleft"||s=="bottomright"||s=="bottomcenter")&&i.showHeader&&this.enableDrillDown()&&(e.y=e.y+i.headerHeight);n(f).css({left:e.x,top:e.y+18*u,"pointer-events":"none",overflow:"hidden"});i.labelVisibilityMode==t.datavisualization.TreeMap.VisibilityMode.HideOnExceededLength&&(o.height>i.ItemHeight||o.width>i.ItemWidth)&&n(f).css("display","none")}},_generateHeaders:function(i,r){var v=r.headerVisibilityMode,e=!1,s=2,h=2,u,c,o,f,l,a;v==t.datavisualization.TreeMap.VisibilityMode.HideOnExceededLength?(u=n("<span>"+i.header+"<\/span>"),n(document.body).append(u),c=u.outerWidth(),u.remove(),i.headerHeight<i.ItemHeight-2*s&&c<i.ItemWidth-2*h&&(e=!0)):i.headerHeight<i.ItemHeight&&(e=!0);e&&(i.headerTemplate==null?(o=n('<div style="display:block;position:absolute;pointer-events: stroke;overflow: hidden;"><label class="e-treemap-header">'+i.header+"<\/label><\/div>"),n(o).css({left:i.headerLeftPosition+s,top:i.headerTopPosition+h,width:i.headerWidth-r.groupPadding,height:i.headerHeight,"margin-left":r.groupPadding}),this._templateDiv.append(o)):(f=n("<div style='display:block;position:absolute;pointer-events: none;overflow: hidden;'><\/div>"),this._templateDiv.append(f),n(f).css({left:i.headerLeftPosition,width:i.headerWidth,top:i.headerTopPosition,height:i.headerHeight}),l={header:i.header,data:i.Data},a=n("#"+i.headerTemplate).render(l),n(f).html(a)))},getDrillDownHeader:function(n){var i="",t;if(n.length==1)return n[0];for(t=0;t<n.length;t++)i+=n[t],t!=n.length-1&&(i+=this.model._connectorText||".");return i},_getPreRenderEventData:function(n,t){var r,f,t=t>=this.model.levels.length-1?this.model.levels.length-1:t,o,u,e;for(f=t===i?this.model.levels[this._drilldownItems.length]:this.model.levels[t],r=this._getGroupitem(f.groupPath,n,f.headerHeight),o=this._getTotalWeight(r),u=r.length-1;u>=0;u--)e=r[u],e.AreaByWeight=e.weight/o;return r.sort(this._orderByAreaWight)},_drillDownHeader:function(t,i,r){var o={groupingLevel:i.groupingLevel,groupPath:i.groupPath,childItems:r,levelItems:i,headerItem:{drilldownHeaders:this._drilldownHeaders,originalHeaders:this._originalHeaders},connectorText:"."},f,s,e,u;for(this._trigger("headerTemplateRendering",o),this.model._connectorText=o.connectorText,f=[],s=[],e=0;e<this._drilldownHeaders.length;e++)f+="<label class='e-drilldownlabel' style='margin-left:-8px'>"+this._drilldownHeaders[e]+"<\/label>",this._drilldownHeaders.length>1&&e<this._drilldownHeaders.length-1&&(f=f+this.model._connectorText);u=n("<div class='e-drilldownHeader' style='display:block;overflow: hidden;'><div class='e-drilldownlabel'  style='position:absolute;height:30px;position:absolute;margin-top: 5px;top: 0px;'>"+f+"<\/div><\/div>");t&&(u=n("<div class='e-drilldownHeader' style='display:block;overflow: hidden;'><svg class='e-drilldownarrow' style='width:15px;height:15px;margin-top: 10px;'><polyline points='8,0 8,10 0,5 8,0'/><\/svg><label class='e-drilldownlabel'  style='left:17px;position:absolute;height:30px;position:absolute;margin-top: 5px;top: 0px;'>"+f+"<\/label><\/div>"));n(u).css({left:"0px",width:this._width+"px",top:0,height:"30px",cursor:"pointer"});this.drillDownHeaderColor()!=null&&n(u).css("background-color",this.drillDownHeaderColor());this._drillHeaderDiv.append(u);n(u).mousedown({treemap:this},this._drilldownfunction);n(u).find(".e-drilldownlabel").mousedown({treemap:this},this._drilldownLabel)},_drilldownLabel:function(n){var c=this.innerHTML,i=n.data.treemap,e,v,y,r,l,u,o,f,s;if(i._drilldownHeaders[0]==c)i._drilldownItems=[],i.refresh();else{for(v="",i._drillHeaderDiv[0].innerHTML="",i._backgroundTile[0].innerHTML="",y="",r=0;r<i._drilldownHeaders.length;r++)c==i._drilldownHeaders[r]&&(e=r+1,i._originalHeaders.splice(r+1),i._drilldownHeaders.splice(r+1));if(i._drilldownItems.length==0&&i._initialLevel)for(l=i._drilldownHeaders.length,u=0;u<l;u++)for(o=i._getPreRenderEventData(this.dataSource(),u),f=0;f<o.length;f++)o[f].header==i._drilldownHeaders[u+1]&&(s=o[f],i._drilldownItems.push(s),i._drilldownItem=s,a=i._drilldownItems[0]);i._labelTemplateElements=[];var p=i.model.levels[i._drilldownHeaders.length-1],h=i._drilldownHeaders.length-i._drilldownItems.length,w=i._drilldownItems.length,a=i._drilldownItems.length==1?i._drilldownItems[0].Data:i._drilldownItems[h<0?w+h:h].Data;return i._drillDownHeader(!0,p,a),e&&(i._drilldownItem=i._drilldownItems[e-2],i._drilldownItems.splice(e-1)),i._templateDiv[0].innerHTML="",t.util.isNullOrUndefined(i._drilldownItem)||i._groupdrillDownLevels(i._drilldownItem,i._drilldownItems.length),!1}},_drilledStateDrillDownFunction:function(n){var f,i,r,t,u;if(n._drilldownItems.length==0&&n._initialLevel)for(n._originalHeaders=n._drilldownHeaders,f=n._drilldownHeaders.length-1,i=0;i<f;i++)for(r=n._getPreRenderEventData(this.dataSource(),i),t=0;t<r.length;t++){var e=n._drilldownHeaders[i+1],o=e.indexOf(n.model.drillDownValue)!=-1,s=r[t].header.indexOf(n.model.drillDownValue)!=-1;(r[t].header==e||s&&o)&&(u=r[t],n._drilldownItems.push(u),n._drilldownItem=u)}},_drilldownfunction:function(n){var t=n.data.treemap,r,i,f,e,u;if(t._trigger("drillStarted",n),t._drilledStateDrillDownFunction(t),!n.cancel&&t._drilldownItems.length>0){if(t._drilldownItems.length==1)t._drilldownItems=[],t.refresh();else{for(r="",i=0;i<t._drillHeaderDiv[0].children[0].children.length;i++)t._drillHeaderDiv[0].children[0].children[i].className=="e-drilldownlabel"&&(r=t._drillHeaderDiv[0].children[0].children[i].innerHTML);f=r.split(t.model._connectorText||".");t._drillHeaderDiv[0].innerHTML="";t._backgroundTile[0].innerHTML="";e="";t._drilldownHeaders.length>1&&t._drilldownHeaders.pop(t._drilldownHeaders.length-1);t._labelTemplateElements=[];u=t.model.levels[t._drilldownHeaders.length-1];u.groupingLevel=n.data.treemap._drilldownItem.GroupingLevel?n.data.treemap._drilldownItem.GroupingLevel:t.model.drillDownLevel;t._drillDownHeader(!0,u,t._drilldownItem.Data);t._drilldownItem=t._drilldownItems[t._drilldownItems.length-2];t._drilldownItems.pop(t._drilldownItems[t._drilldownItems.length-1]);t._templateDiv[0].innerHTML="";t._groupdrillDownLevels(t._drilldownItem,t._drilldownItems.length)}t._trigger("drillDownItemSelected",{level:t._drilldownItems.length,header:t.getDrillDownHeader(t._drilldownHeaders),prevLevel:t._drilldownItems.length-1})}},_sizeCalculation:function(r){var h,u,v;if(this.model.rangeColorMapping!=null||this.model.colorPath!=null){var f=this.model.rangeColorMapping!=null&&this.model.rangeColorMapping.length>0?this.model.rangeColorMapping:r?r:n.extend(!0,[],this.model.dataSource),w=this.model.rangeColorMapping!=null&&this.model.rangeColorMapping.length>0?!0:!1,s=0,y,b,l=0,p=this.iconWidth()+5,e=this.iconHeight()+15,o=0,a=this.model.legendSettings.columnCount,c=this.model.legendSettings;if(this.model.legendSettings.mode!=t.datavisualization.TreeMap.LegendMode.Interactive){if(this.legendheight()==0&&this.legendwidth()==0&&a==0){for(w||(this._legendItem=f,this._generateLegendLabels(this.model,this._legendItem)),u=0;u<f.length;u++)f[u].legendLabel==i&&(f[u].legendLabel=f[u].from+"-"+f[u].to),v=this._calcWidth(f[u].legendLabel),h=p+v+10,this.legendheight()==0&&this.legendwidth()==0?this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Top||this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Bottom?this._width<s+h?(e=this.iconHeight()+15+e,o=Math.max(o,s),s=h):s+=h:this._height<l+this.iconHeight()+15?(o+=s,e=Math.max(e,l),s=h,l=0):(l+=this.iconHeight()+18,s=Math.max(h,s)):(y+=h+18,y+150>=this.legendwidth()&&(y=10,b+=40));this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Top||this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Bottom?(o=Math.max(o,s),e+=5):(o+=s+15,e=Math.max(e,l))}else if(a==0){for(u=0;u<f.length;u++)f[u].legendLabel==i&&(f[u].legendLabel=f[u].from+"-"+f[u].to);e=this.legendheight().toString().indexOf("%")!=-1?this._height/100*parseInt(this.legendheight().replace("%","")):this.legendheight();o=this.legendwidth().toString().indexOf("%")!=-1?this._width/100*parseInt(this.legendwidth().replace("%","")):this.legendwidth()}if(a!=0)for(u=0;u<f.length;u++)f[u].legendLabel==i&&(f[u].legendLabel=f[u].from+"-"+f[u].to),v=this._calcWidth(f[u].legendLabel),h=p+v+10,u%a!=0?(s+=h,u==a-1&&(o=Math.max(o,s))):(u!=0&&(e=this.iconHeight()+15+e),o=Math.max(o,s),s=h)}else e=(c.height!=0?c.height:30)+18,c.title!=null&&c.title!=""&&(e+=25),o=(c.width!=0?c.width:100)+20+c.leftLabel.length*10+c.rightLabel.length*10;this._legendSize={height:e,width:o};this.model.legendSettings!=null&&(this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Bottom?(this._height=this._height-parseFloat(e),this.model.legendSettings.tempWidth=s):this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Top?(this._height=this._height-parseFloat(e),this._margintop=parseFloat(e)):this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Left?(this._width=this._width-o,this._marginleft=o):this.dockPosition()==t.datavisualization.TreeMap.DockPosition.Right&&(this._height=this._height,this._width=this._width-o))}},_generateLegendLabels:function(n,t){for(var r=n.colorPath,i=0;i<t.length;i++)this.enableDrillDown()?t[i].legendLabel=t[i].header:(t[i].legendLabel=this.labelPath()?t[i][this.labelPath()]:t[i][n.weightValuePath],t[i].color=t[i][r])},_getDockPositionPoint:function(n,r,u){var o=u.labelPosition,e=2,f=2;return u.groupPadding!=i&&u.groupBorderThickness!=i&&(e=u.groupPadding+u.groupBorderThickness+e,f=u.groupPadding+u.groupBorderThickness+f),u.showHeader&&(f=f+u.headerHeight),o==t.datavisualization.TreeMap.Position.TopCenter?e=n.width/2-r.width/2:o==t.datavisualization.TreeMap.Position.TopRight?e=n.width-r.width-e:o==t.datavisualization.TreeMap.Position.CenterLeft?f=n.height/2-r.height/2:o==t.datavisualization.TreeMap.Position.Center||o==null?(e=n.width/2-r.width/2,f=n.height/2-r.height/2):o==t.datavisualization.TreeMap.Position.CenterRight?(e=n.width-r.width-e,f=n.height/2-r.height/2):o==t.datavisualization.TreeMap.Position.BottomLeft?f=n.height-r.height-f:o==t.datavisualization.TreeMap.Position.BottomCenter?(e=n.width/2-r.width/2,f=n.height-r.height-f):o==t.datavisualization.TreeMap.Position.BottomRight&&(e=n.width-r.width-e,f=n.height-r.height-f),{x:n.left+e,y:n.top+f}},_generateTreeMapItems:function(t,r,u,f){var c,e,s,o,h,l;if(this._rootTreeMapItems=t,this._getTreeMapItemTemplate(t),t!=null){for(this._isLevelColors||this._setColorMappings(t,this.model,i,f),c=0;c<t.length;c++){if(e=t[c],s=e.rectangle,e.Data!=null&&e.Data.length>0&&(e.Data=e.Data[0]),e.Rectangle=s,o={borderThickness:this.model.leafItemSettings.borderThickness,borderBrush:this.model.leafItemSettings.borderBrush,fill:e.backgroundColor,dataSource:e.Data},this._trigger("itemRendering",{model:this.model,data:o}),o.fill!=e.backgroundColor&&(e._color=o.fill,this.enableDrillDown()))for(h=0;h<f.length;h++)if(f[h].ChildtreeMapItems)for(l=0;l<f[h].ChildtreeMapItems.length;l++)e.header==f[h].ChildtreeMapItems[l].header&&(f[h]._color=o.fill);n(s).css({"box-sizing":"border-box","-moz-box-sizing":"border-box","-webkit-box-sizing":"border-box","border-color":o.borderBrush,"border-width":o.borderThickness+"px"});o.fill?n(s).css("background-color",o.fill):n(s).css("background-color","#E5E5E5");e.backgroundOpacity!=null&&n(s).css("opacity",e.backgroundOpacity);this._wireEventForTreeMapItem(e,s)}this.model.rangeColorMapping.length==0&&this.uniColor()==null&&this._color()!=""?this._setDesaturationColor(t,this.model.desaturationColorMapping):this.model.PaletteColorMapping!=null&&this._setPaletteColor(t,this.model.PaletteColorMapping)}},browserInfo:function(){var n={},t=[],i={webkit:/(chrome)[ \/]([\w.]+)/i,safari:/(webkit)[ \/]([\w.]+)/i,msie:/(msie) ([\w.]+)/i,opera:/(opera)(?:.*version|)[ \/]([\w.]+)/i,mozilla:/(mozilla)(?:.*? rv:([\w.]+)|)/i};for(var r in i)if(i.hasOwnProperty(r)&&(t=navigator.userAgent.match(i[r]),t)){n.name=t[1].toLowerCase();n.version=t[2];!navigator.userAgent.match(/Trident\/7\./)||(n.name="msie");break}return n.isMSPointerEnabled=n.name=="msie"&&n.version>9&&window.navigator.msPointerEnabled,n.pointerEnabled=window.navigator.pointerEnabled,n},isTouch:function(n){var t=n.originalEvent?n.originalEvent:n;return t.pointerType=="touch"||t.pointerType==2||t.type.indexOf("touch")>-1?!0:!1},_wireEventForTreeMapItem:function(t,i){var f=this.model.browserInfo,r=f.isMSPointerEnabled,u=f.pointerEnabled,e=r?u?"pointerdown":"MSPointerDown":"touchstart mousedown",o=r?u?"pointerup":"MSPointerUp":"touchend mouseup",s=r?u?"pointermove":"MSPointerMove":"touchmove mousemove",h=r?u?"pointerenter":"MSPointerEnter":"touchenter mouseenter",c=r?u?"pointerleave":"MSPointerOut":"touchleave mouseleave";this._on(n(i),s,{Param1:t,Param2:this},this._rectMouseMove);this._on(n(i),c,{treeMap:this},this._rectMouseLeave);this._on(n(i),o,{Param1:t,Param2:this},this._rectMouseUp);this._on(n(i),e,{treeMap:this},this._rectMouseDown);this._on(n(i),h,{Param1:t,Param2:this},this._rectMouseEnter)},_getTreeMapItemTemplate:function(i){for(var r,e,o,s,f="",u=0;u<i.length;u++)r=i[u],this.model.leafItemSettings.itemTemplate==null?f+='<div style="border-style:solid;position:absolute;left:'+r.LeftPosition+"px;top:"+r.TopPosition+"px;height:"+r.ItemHeight+"px;width:"+r.ItemWidth+"px;border-width:"+this.borderThickness()+"px;border-color:"+this.borderBrush()+'"><\/div>':(e=n("#"+this.model.leafItemSettings.itemTemplate),r.Data!=null&&r.Data.length==1&&(r.Data=r.Data[0]),o=n.templates(e.html()),s="<div style='overflow:hidden;position:absolute;left:"+r.LeftPosition+"px;top:"+r.TopPosition+"px;height:"+r.ItemHeight+"px;width:"+r.ItemWidth+"px'>"+o.render(r)+"<\/div>",f+=s);this._svgDocument!=null&&this.enableDrillDown()&&(this._svgDocument[0].innerHTML="");this._svgDocument=n('<div style= "overflow:hidden;z-index:1;"id="svgDocument">'+f+"<\/div>");this._backgroundTile.appendTo(this.element);this._svgDocument.appendTo(this.element);this._svgDocument.css({height:this._height+"px",width:this._width+"px","margin-top":this._margintop+"px","margin-left":this._marginleft,overflow:"hidden","z-index":1,position:"absolute",left:"0",top:"0"});n(this._backgroundTile).css({height:this._height+"px",width:this._width+"px","margin-top":this._margintop+"px","margin-left":this._marginleft,overflow:"hidden","z-index":0,position:"absolute",left:"0",top:"0"});this._templateDiv.appendTo(this.element);n(this._templateDiv).mousemove({Param2:this},this._rectMouseMove);var h=this.model.browserInfo,c=h.isMSPointerEnabled,l=h.pointerEnabled,a=c?l?"pointerdown":"MSPointerDown":"touchstart mousedown",v=c?l?"pointerup":"MSPointerUp":"touchend mouseup";for(this._off(n(this.element),a,this.rectClick),this._off(n(this.element),v,this._docClickFunction),this._off(n(this.element),t.eventType.mouseDown,this.dragDown),this._off(n(this.element),t.eventType.mouseMove,this.dragMove),this._off(n(this.element),t.eventType.mouseUp,this.dragUp),this._on(n(this.element),a,{treemap:this,Param1:i},this.rectClick),this._on(n(this.element),v,{treemap:this,Param1:i},this._docClickFunction),this._on(n(this.element),t.eventType.mouseDown,{treemap:this},this.dragDown),this._on(n(this.element),t.eventType.mouseMove,{treemap:this},this.dragMove),this._on(n(this.element),t.eventType.mouseUp,{treemap:this},this.dragUp),this.enableDrillDown()&&(this._drillHeaderDiv.appendTo(this.element),this._drillHoverDiv.appendTo(this.element)),u=0;u<i.length;u++)i[u].rectangle=this._svgDocument[0].childNodes[u]},selectItem:function(t){var r,i,u,f;if(this._rootTreeMapItems!=null)for(r=0;r<this._rootTreeMapItems.length;r++)i=this._rootTreeMapItems[r],u=i.rectangle,i.Data!=null&&i.Data.length>0&&(i.Data=i.Data[0]),t==i.Data&&this.highlightOnSelection()&&(n(u).css({"border-width":this.highlightBorderThickness(),"border-color":this.highlightBorderBrush()}),this._browser!="msie"&&(f=this._svgDocument[0].children[this._svgDocument[0].children.length-1],this._svgDocument[0].insertBefore(u,f)),this._prevSelectedItems.push(u),this._trigger("treeMapItemSelected",{selectedItems:t,originalEvent:event}))},_selectTreemapItem:function(t){var r,i,u,f;if(this._rootTreeMapItems!=null)for(r=0;r<this._rootTreeMapItems.length;r++)i=this._rootTreeMapItems[r],u=i.rectangle,i.Data!=null&&i.Data.length>0&&(i.Data=i.Data[0]),t!=i.Data||this._contains(this.selectedItems,t)||this.highlightOnSelection()&&(n(u).css({"border-width":this.highlightBorderThickness(),"border-color":this.highlightBorderBrush()}),this._browser!="msie"&&(f=this._svgDocument[0].children[this._svgDocument[0].children.length-1],this._svgDocument[0].insertBefore(u,f)),this._prevSelectedItems=[],this.selectedItems=[],this._prevSelectedItems.push(u),this.selectedItems.push(t))},_selectItemResize:function(){for(var n,i,t=0;t<this._rootTreeMapItems.length;t++)n=this._rootTreeMapItems[t],i=n.rectangle,n.Data!=null&&n.Data.length>0&&(n.Data=n.Data[0]),this._contains(this.selectedItems,n.Data)&&this.selectItem(n.Data)},_updateLegendRange:function(n,t){for(var o,r,s,c,e,f,h,a,l=this.model.rangeColorMapping,u=0;u<l.length;u++)if(o=null,r=l[u],this.model.enableGradient&&(o=this._generateGradientCollection(r.gradientColors)),n>=r.from&&n<=r.to)for(s=u,u!=0&&(s=u*10),c=r.from,e=r.from+(r.to-r.from)/10,f=s;f<s+10;f++){if(n>=c&&n<=e)return h={},a=this._getColorRatio(.7,1,n,r.from,r.to),this._legendRects[f]!=i&&(h=this._legendRects[f]),t.backgroundColor=this.model.enableGradient?u!=0?o[f-u*10]:o[f]:r.color,t.backgroundOpacity=a,h.color=t.backgroundColor,h;c=e;e=e+(r.to-r.from)/10}},selectItemByPath:function(n,t,i){for(var e,c,l,s,f,o=n.split(i),a=t.split(i),n,t,u=this._rootTreeMapItems,r=0;r<o.length;r++){var n=o[r],t=a[r],h=[];for(e=0;e<u.length;e++){for(f=u[e],c=f.Data,s=0;s<o.length;s++)l=this._reflection(c,n);t==l&&h.push(f)}u=h}for(r=0;r<u.length;r++)f=u[r],this.selectItem(f.Data)},_getColorRatio:function(n,t,i,r,u){var f=100/(u-r)*(i-r);return(parseFloat(t)-parseFloat(n))/100*f+parseFloat(n)},_contains:function(n,t){var i=n.length;if(i>0)while(i--)if(n[i]===t)return!0;return!1},_rectMouseEnter:function(r){var u=r.data.Param2,h=u.model.legendSettings,y=r.data.Param1.Data,f,c,l,e,s,p,w,b,o,k,a,v,d,g,nt;if(y!=null&&(f=r.data.Param2._toolTipElement,c=r.data.Param2.model.tooltipTemplate,f!=null&&c!=null)){if(u.isTouch(r)||n(f).css({left:r.pageX+10,top:r.pageY+10,display:"block"}),l=this.model.locale,e=n.extend({},y),l&&this.model.enableGroupSeparator)for(s in e)e[s]=isNaN(parseFloat(e[s]))?e[s]:parseFloat(e[s]).toLocaleString(l);p=n("#"+c).render(e);n(f).html(p);w=f[0]!=null?f[0].clientHeight:f.clientHeight;b=f[0]!=null?f[0].clientWidth:f.clientWidth;r.data.Param2._tooltipSize={height:w,width:b}}if(h!=null&&h.mode==t.datavisualization.TreeMap.LegendMode.Interactive&&u.model.rangeColorMapping!=null&&(u.showLegend()==i||u.showLegend()))for(o=0;o<u._rootTreeMapItems.length;o++)if(k=u._rootTreeMapItems[o].Rectangle,k==r.target&&(a=null,u.model.rangeColorMapping!=i&&(a=u.model.rangeColorMapping),v=h.width,h.width==i&&(v=150),d=v/a.length/10,u._rootTreeMapItems[o].legendrect!=null)){g=u._rootTreeMapItems[o].legendrect.marginLeft;nt=g+Math.ceil(d)-u._interactiveArrow.width()/2;n(u._interactiveArrow).css({"margin-left":nt,display:"block"});break}},displayTooltip:function(t){var u=t.data.Param2,f=u._toolTipElement,r=u.mousePosition(t),a=!0,h,o,c,v,y,p,s,e,l;if(u.model.trackerElement&&(clearTimeout(u.model.timer),n(".e-tooltipelement"+this._id).finish()),t.data.Param1==i&&f!=null){for(e=0;e<u._rootTreeMapItems.length;e++)if(o=u._rootTreeMapItems[e],o.rectangle.offsetLeft<r.offsetX&&o.rectangle.offsetLeft+o.rectangle.offsetWidth>r.offsetX&&o.rectangle.offsetTop<r.offsetY&&o.rectangle.offsetTop+o.rectangle.offsetHeight>r.offsetY){h=o.Data;break}h!=null?(c=t.data.Param2.model.tooltipTemplate,f!=null&&c!=null&&(n(f).css({left:r.pageX+10,top:r.pageY+10,display:"block"}),v=n("#"+c).render(h),n(f).html(v),y=f[0]!=null?f[0].clientHeight:f.clientHeight,p=f[0]!=null?f[0].clientWidth:f.clientWidth,u._tooltipSize={height:y,width:p})):f!=null&&(a=!1,n(f).css("display","none"))}if(a){if(s=u._tooltipSize,s.width+r.pageX>=u._width&&(r.pageX-=s.width,r.pageX<0&&(r.pageX=10)),s.height+r.pageY>=u._height&&(r.pageY-=s.height,r.pageY<0&&(r.pageY=10)),u.enableDrillDown()&&u._browser!="msie")for(e=0;e<u._drillHoverDiv[0].children.length;e++)l=u._drillHoverDiv[0].children[e],l.className==t.data.Param1.ParentHeader&&n(l).css({display:"block"});f!=null&&n(f).css({left:r.pageX+10,top:r.pageY+10,display:"block"})}},_rectMouseUp:function(n){this.isTouch(n)&&this.displayTooltip(n)},_rectMouseMove:function(n){n.data.Param2.isTouch(n)||n.data.Param2.displayTooltip(n)},_rectMouseLeave:function(t){var i=t.data.treeMap._toolTipElement;i==null||t.data.treeMap.isTouch(t)||n(i).css("display","none")},_setColorMappings:function(n,i,r,u){var e=this,h=this.model.legendSettings,f,o,s;if(i.rangeColorMapping!=null&&i.rangeColorMapping.length>0||i.uniColorMapping!=null&&(i.uniColorMapping.color!=null||this.uniColor()!=null))for(f=0;f<n.length;f++)r||(o=n[f].Data instanceof Array?e._reflection(n[f].Data[0],this.colorValuePath()):e._reflection(n[f].Data,this.colorValuePath())),h.mode==t.datavisualization.TreeMap.LegendMode.Interactive||this.model.enableGradient?n[f].legendrect=this._updateLegendRange(o,n[f]):(s=n[f]._generateColorMappings(i,this),n[f].backgroundColor=s);else i.desaturationColorMapping!=null&&(i.desaturationColorMapping.color!=""||this._color()!="")?this._setDesaturationColor(n,i.desaturationColorMapping):i.paletteColorMapping!=null&&i.paletteColorMapping.colors.length>0?this._setPaletteColor(n,i.paletteColorMapping):this.model.colorPath!=null&&this._setFillColor(n,this.model.colorPath,u)},_setFillColor:function(n,t,i){for(var u,r=0;r<n.length;r++)n[r].color=n[r].backgroundColor=n[r].Data[0]?n[r].Data[0][t]:n[r].Data[t],n[r].legendLabel=n[r].header;if(this.enableDrillDown()&&this.showLegend()){for(u=0;u<i.length;u++)i[u].ChildtreeMapItems&&(i[u].ChildtreeMapItems.sort(function(n,t){return parseFloat(t.weight)-parseFloat(n.weight)}),i[u].color=i[u].ChildtreeMapItems[0].Data[0][t],i[u].legendLabel=i[u].header);this._legendItem=i}else this.enableDrillDown()||!this.showLegend()||this.model.isHierarchicalDatasource||(this._legendItem=n)},_setDesaturationColor:function(n,t){var e,c,u,l,r;n=n.sort(this._orderBycolorWeight);var i=typeof t.from=="number"?t.from:this._from(),f=typeof t.to=="number"?t.to:this._to(),s=typeof t.rangeMinimum=="number"?t.rangeMinimum:this._rangeMinimum(),h=typeof t.rangeMaximum=="number"?t.rangeMaximum:this._rangeMaximum(),o=[];for(e=0;e<n.length;e++)c=n[e].Data[0],u=this._reflection(c,this.colorValuePath()),u!=null&&typeof u!="number"&&(u=Number(u.replace(/[^0-9\.]+/g,""))),o.push(u);for(this._rangeMinimum()==0&&(s=Math.min.apply(Math,o)),this._rangeMaximum()==0&&(h=Math.max.apply(Math,o)),(i<0||i>1)&&(i=1),(f>1||f<0)&&(f=0),l=(i-f)/n.length,r=0;r<n.length;r++)n[r].backgroundColor=typeof t.color=="string"?t.color:this._color(),n[r].colorWeight>=s&&n[r].colorWeight<=h&&(n[r].backgroundOpacity=i,i=i-l)},_generateGradientCollection:function(n){for(var f=[],r=n[0],u=n[n.length-1],e=this._hexToR(r),o=this._hexToG(r),s=this._hexToB(r),h=this._hexToR(u),c=this._hexToG(u),l=this._hexToB(u),i=10,a=this._getRangeColorValues(i,e,h),v=this._getRangeColorValues(i,o,c),y=this._getRangeColorValues(i,s,l),t=0;t<i;t++)f.push(this._rgbToHex(a[t],v[t],y[t]));return f},_hexToR:function(n){return parseInt(this._cropHex(n).substring(0,2),16)},_hexToG:function(n){return parseInt(this._cropHex(n).substring(2,4),16)},_hexToB:function(n){return parseInt(this._cropHex(n).substring(4,6),16)},_cropHex:function(n){return n.charAt(0)=="#"?n.substring(1,7):n},_rgbToHex:function(n,t,i){return"#"+this._toHex(n)+this._toHex(t)+this._toHex(i)},_toHex:function(n){var t="0123456789ABCDEF";return(n=parseInt(n,10),isNaN(n))?"00":(n=Math.max(0,Math.min(n,255)),t.charAt((n-n%16)/16)+t.charAt(n%16))},_getRangeColorValues:function(n,t,i){var u=[],f,r;if(u.push(t),t>i)for(f=(t-i)/(n-1),r=n;r>2;r--)t=t-f,u.push(t);else for(f=(i-t)/(n-1),r=2;r<n;r++)t=t+f,u.push(t+f);return u.push(i),u},_isSVG:function(){return window.SVGSVGElement?!0:!1},_drawInteractiveLegend:function(){var r=this.model.legendSettings,u=0,h=0,lt,it,rt,ut,at,k,ft,a,l,b,e,v,d,st,vt,ht,g,s,tt,o;this._legenddiv.empty();var f=this._legenddiv,w=this.model.legendSettings.height!=0?this.model.legendSettings.height:30,c=this.model.legendSettings.width!=0?this.model.legendSettings.width:100;if(r.mode==t.datavisualization.TreeMap.LegendMode.Interactive||this.model.rangeColorMapping!=null){for(o="",r.height==0&&(w=30),r.width==0&&(c=100),r.leftLabel==null&&(r.leftLabel=""),r.title!=null&&(ut=u,r.showLabels||(ut=u+r.leftLabel.length*10),at=r.title.length*10,k=lt=r.title,at>c&&(k=this._trimFunction(k,c)),o=this._createLabel(k,ut,h,"e-interactivelegend-title"),o[0].title=lt,o.css({width:c+"px"}),this._isSVG()?o.appendTo(f):f.append(o),h+=25),r.showLabels&&(h+=25),r.leftLabel==null||r.showLabels||(o=this._createLabel(r.leftLabel,u,h-3,"e-interactivelegend-leftlabel"),this._isSVG()?o.appendTo(f):f.append(o),u=u+r.leftLabel.length*10),ft=this._createInteractiveArrow(u,h+w),ft.appendTo(f),this._interactiveArrow=ft,a=null,!this._isSVG()&&this.model.enableGradient&&(a=this._createGroup(!1,"legendGroup"),a.style.left="0px",a.style.top="0px",a.style.position="relative",f.append(a)),l=this.model.rangeColorMapping,b=0;b<l.length;b++)if(e=l[b],!e.hideLegend){if(v=[],this.model.enableGradient&&(v=this._generateGradientCollection(e.gradientColors)),d={},this.model.enableGradient)if(this._isSVG()){var et=n("<canvas/>"),ot=et[0].getContext("2d"),y=ot.createLinearGradient(0,0,300,0),p=0;for(s=0;s<10;s++)p=p+1/10,s==0?(y.addColorStop(0,e.gradientColors[0]),y.addColorStop(p,v[s])):s==v.length-1?(y.addColorStop(p-1/10,v[s]),y.addColorStop(p,e.gradientColors[1])):(y.addColorStop(p-1/10,v[s]),y.addColorStop(p,v[s+1]));ot.fillStyle=y;ot.fillRect(0,0,300,300);et.css({height:w+"px",width:c/l.length+"px","margin-left":u+"px","margin-top":h+"px",opacity:"0.9",filter:"alpha(opacity=90)",position:"absolute"});et.appendTo(f)}else st="legend"+b,vt="<v:rect id="+st+' display="block" style="position:absolute;top: '+(h-2)+"px;left:"+u+"px;width:"+c/l.length+"px;height:"+w+'px;"><v:fill opacity="0.9px" type="gradient" method="linear sigma" angle="270"/><v:stroke opacity="0px"/><\/v:rect>',a.innerHTML=a.innerHTML+vt,ht=document.getElementById(st),ht.fillcolor=e.gradientColors[0],ht.fill.color2=e.gradientColors[1];else g=n("<div/>"),g.css({height:w+"px",width:c/this.model.rangeColorMapping.length+"px","background-color":e.color,"margin-left":u+"px","margin-top":h+"px",opacity:"0.9",filter:"alpha(opacity=90)",position:"absolute"}),this._isSVG()?g.appendTo(f):f.append(g);for(s=0;s<10;s++)d={},d.marginLeft=u,this._legendRects.push(d),u=u+c/l.length/10;if(this.model.legendSettings.showLabels){var nt=u-c/l.length,ct=h-25,yt=this._createLabel(e.from,nt,ct,"e-legend-rangestartlabel");nt=u;tt=this._createLabel(e.to,nt,ct);e.legendLabel!=i&&(tt=this._createLabel(e.legendLabel,nt-e.legendLabel.length*5,ct,"e-legend-rangeendlabel"));this._isSVG()?(e==l[0]&&yt.appendTo(f),tt.appendTo(f)):(e==l[0]&&f.append(yt),f.append(tt))}}r.rightLabel==null||r.showLabels||(o=this._createLabel(r.rightLabel,u+10,h-3,"e-interactivelegend-rightlabel"),this._isSVG()?o.appendTo(f):f.append(o),u=u+r.rightLabel.length*10);it=u+10;rt=h+w+this._interactiveArrow.height();this._legendSize={width:it,height:rt};r.dockPosition=="left"?this._marginleft=it:r.dockPosition=="top"&&(this._margintop=rt)}},_trimFunction:function(t,i){var r=n('<div style="width:auto;position:absolute;" class= "e-interactivelegend-title">'+t+"<\/div>"),u;for(n(document.body).append(r),u=t;r.width()>i;)u=u.slice(0,-2),r.text(u+"...");return document.body.removeChild(r[0]),r.text()},_createLabel:function(t,i,r,u){var f=n("<div class="+u+"><\/div>");return f[0].innerHTML=t,f.css({"margin-left":i+"px","margin-top":r+"px",position:"absolute"}),f},_createInteractiveArrow:function(t,i){var r=n("<div class='e-icon1 e-interactiveArrow'><\/div>");return r[0].innerHTML="&#9650",r.css({"margin-left":t+"px","margin-top":i+"px",position:"absolute"}),r},_createGroup:function(n,t){var i;return i=document.createElement("<v:group class="+t+">"),i.style.width=this._width+"px",i.style.height=this._height+"px",i.coordorigin="0 0",i.coordsize=this._width+" "+this._height,n&&(this._rootgroup=i,i.style.left="20px",i.style.top="20px"),i},_setPaletteColor:function(n,t){n=n.sort(this._orderBycolorWeight);for(var i=0;i<n.length;i++)t.colors!=null&&t.colors.length>0&&(n[i].backgroundColor=t.colors[i%t.colors.length])},_orderBycolorWeight:function(n,t){return n.colorWeight==t.colorWeight?0:n.colorWeight<t.colorWeight?1:-1},_orderByAreaWight:function(n,t){return n.AreaByWeight==t.AreaByWeight?0:n.AreaByWeight<t.AreaByWeight?1:-1},_calculateSliceAndDiceItemSize:function(n,t,r,u,f,e,o,s,h){var a,g,l,p,nt,b,v,tt,d,c,k,y;e==""&&(e=0);a=e;g=this._getTotalWeight(n);s==i&&(s=u>f?!0:!1);l={Width:u,Height:f-h};p=o<l.Height?o:0;l.Height=l.Height-p;var rt={X:t,Y:r+p,Width:l.Width,Height:l.Height},it=l.Height*l.Width,w=n.length;if(s)for(nt=l.Height,b=0,c=0;c<w;c++)k=it/g*n[c].weight,v=k/nt,e=v>a?a:0,b<=l.Width&&(n[c].ItemWidth=c!=w-1?v-parseFloat(e):v,n[c].ItemHeight=nt,n[c].LeftPosition=b+t,n[c].TopPosition=r+o,b+=v);else for(tt=l.Width,d=0,c=0;c<w;c++)k=it/g*n[c].weight,y=k/tt,e=y>a?a:0,d<=l.Height&&(n[c].ItemWidth=tt,n[c].ItemHeight=c!=w-1?y-parseFloat(e):y,n[c].LeftPosition=t,n[c].TopPosition=d+r+p,d+=y)},_calculateSquarifiedItemSize:function(n,t,i,r,u,f,e,o){var a,s,v,y,l,g,c;f==""&&(f=0);var it=this._getTotalWeight(n),p={Width:r,Height:u-o},nt=e<p.Height?e:0;for(p.Height=p.Height-nt,a=n.length-1;a>=0;a--)l=n[a],l.AreaByWeight=p.Height*p.Width*l.weight/it,l.headerTopPosition=i;var h={X:t,Y:i+nt,Width:p.Width,Height:p.Height},b=n.sort(this._orderByAreaWight),k=0,d=0,w=0;for(a=0;a<n.length;a=w){var rt=b[a],tt=this._getGroupTotalWeight(rt,b,h,a),ut=tt.totalWeight;for(w=tt.index,s={},v=h.Width>h.Height?!0:!1,y=a;y<w;y++)l=b[y],g=ut,y==a&&(s.X=h.X,s.Y=h.Y,v?(s.Width=g/h.Height,s.Height=h.Height,h.X+=s.Width,h.Width=Math.max(0,h.Width-s.Width)):(s.Width=h.Width,s.Height=g/h.Width,h.Y+=s.Height,h.Height=Math.max(0,h.Height-s.Height)),k=s.X,d=s.Y),c={},b.indexOf(l)!=b.length-1?(c.X=0,c.Y=0,c.Width=v?s.Width-parseFloat(f):l.AreaByWeight/s.Height,c.Height=v?l.AreaByWeight/s.Width:s.Height-parseFloat(f),w-y!=1&&(v?c.Height-=parseFloat(f):c.Width-=parseFloat(f))):(c.Width=v?s.Width:l.AreaByWeight/s.Height,c.Height=v?l.AreaByWeight/s.Width:s.Height),l.ItemWidth=c.Width,l.ItemHeight=c.Height,l.LeftPosition=k,l.TopPosition=d,v?d+=w-y!=1?c.Height+parseFloat(f):c.Height:k+=w-y!=1?c.Width+parseFloat(f):c.Width}},_getGroupTotalWeight:function(n,t,i,r){for(var f=0,o=0,s,e,h;r<t.length;r++){s=this._getShortersideLength(i.Width,i.Height);e=t[r];f+=e.AreaByWeight;var u=f/s,c=n.AreaByWeight/u,l=e.AreaByWeight/u;if(r==0&&(o=this._aspectRatio(u,s)),h=Math.max(this._aspectRatio(c,u),this._aspectRatio(l,u)),f==e.AreaByWeight||h<o)o=h;else{f-=e.AreaByWeight;u=f/s;o=Math.max(this._aspectRatio(c,u),this._aspectRatio(l,u));break}}return{totalWeight:f,index:r}},_aspectRatio:function(n,t){return n>t?n/t:t/n},_getShortersideLength:function(n,t){return n>t?t:n},_getTotalWeight:function(n){for(var i=0,t=0;t<n.length;t++)i+=parseFloat(n[t].weight);return i},_getGroupitem:function(t,i,r,f){var a=[],y,h,v,e,l,o,s,d;if(f&&(i=i[0]?i[0][f]:i[f]),t==null&&(t=this.labelPath()||this.weightValuePath()),i!=null&&this.weightValuePath()!=null){for(h in i)e=i[h],(e!=null&&e.hasOwnProperty(t)?!0:this._containsProperty(e,t))&&(l=this._reflection(e,t),n.inArray(l,a)==-1&&a.push(l));for(y=[],h=0;h<a.length;h++){var c={},w=[],b=a[h],k=0,p=0,g=0;for(v=0;v<i.length;v++)e=i[v],l=this._reflection(e,t),l==b&&(o=this._reflection(e,this.weightValuePath()),o!=null&&typeof o!="number"&&(o=Number(o.replace(/[^0-9\.]+/g,""))),o<0&&(o=-1*o),s=this._reflection(e,this.colorValuePath()),s!=null&&typeof s!="number"&&(s=Number(s.replace(/[^0-9\.]+/g,""))),w.push(e),g++,o!=null&&(p+=parseFloat(o)),s!=null&&(k+=parseFloat(s)));c.header=b;c.data=w;c.weight=p;c.colorWeight=k;c.headerHeight=r;p>0&&(d=new u(c),y.push(d))}return y}return null},_getTopGroupitem:function(n,t){var s,r,y,h,p;n==null&&(n=this.labelPath());n==null&&(n=this.weightValuePath());var f={},c=0,l=0,a=[],v=[];for(s=0;s<t.length;s++){var e=t[s],w=this._reflection(e,n),o={},i=this._reflection(e,this.weightValuePath());i!=null&&typeof i!="number"&&(i=Number(i.replace(/[^0-9\.]+/g,"")));i<0&&(i=-1*i);r=this._reflection(e,this.colorValuePath());r!=null&&typeof r!="number"&&(r=Number(r.replace(/[^0-9\.]+/g,"")));i!=null&&(c+=parseFloat(i));r!=null&&(l+=parseFloat(r));v.push(e);o.weight=i;o.data=e;o.header=w;o.colorWeight=r;y=new u(o);a.push(y)}return f.header="",f.data=v,f.weight=c,f.innerItem=a,f.colorWeight=l,h=[],p=new u(f),h.push(p),h},_containsProperty:function(n,t){for(var i in n)if(i==t)return!0;return!1},_reflection:function(t,i){var r=t,u,f,e;if(i!=null&&t!=null){for(u=i.split("."),u.push(i),f=0;f<u.length;f++)r!=null&&(e=r.hasOwnProperty(u[f])?!0:this._containsProperty(r,u[f]),e&&n.each(r,function(n,t){if(n==u[f])return r=t,!1}));return r}return null}});r=function(){this.groupBackground=null;this.groupBorderColor=null;this.groupBorderThickness=1;this.groupPadding=4;this.groupPath=null;this.groupGap=1;this.headerHeight=20;this.showHeader=!0;this.showLabels=!1;this.headerTemplate=null;this.labelTemplate=null;this.labelPosition="center";this.labelVisibilityMode="visible";this.headerVisibilityMode="visible";this.treeMapItems=[]};r.prototype={};var f=function(){this.groupID=null;this.rangeColorMapping=[];this.desaturationColorMapping={from:0,to:0,color:"",rangeMinimum:0,rangeMaximum:0};this.uniColorMapping={color:null};this.paletteColorMapping={colors:[]}},e=function(){this.from=-1;this.to=-1;this.legendlabel=null;this.color=null},u=function(n){this.weight=n.weight;this.colorWeight=n.colorWeight;this.Data=n.data;this.headerHeight=n.headerHeight;this.header=n.header;this.label=n.header;this.headerLeftPosition=0;this.headerTopPosition=0;this.innerItems=n.innerItem;this.headerWidth=0;this.headerTemplate=null};u.prototype={_generateColorMappings:function(n,t){return n.rangeColorMapping!=null&&n.rangeColorMapping.length>0?this._getRangeBrushColor(n.rangeColorMapping):t.uniColor()!=null&&n.uniColorMapping!=null?t.uniColor():n.uniColorMapping.color!=null&&n.uniColorMapping!=null?this._getUniColor(n.uniColorMapping):void 0},_getUniColor:function(n){return n.color},_getRangeBrushColor:function(n){for(var i,t=0;t<n.length;t++)if(i=n[t],this.colorWeight>=i.from&&this.colorWeight<=i.to)return i.color}};t.datavisualization.TreeMap.selectionMode={Default:"default",Multiple:"multiple"};t.datavisualization.TreeMap.textOverflow={None:"none",Hide:"hide",Wrap:"wrap",WrapByWord:"wrapbyword"};t.datavisualization.TreeMap.groupSelectionMode={Default:"default",Multiple:"multiple"};t.datavisualization.TreeMap.ItemsLayoutMode={Squarified:"squarified",SliceAndDiceHorizontal:"sliceanddicehorizontal",SliceAndDiceVertical:"sliceanddicevertical",SliceAndDiceAuto:"sliceanddiceauto"};t.datavisualization.TreeMap.DockPosition={Top:"top",Bottom:"bottom",Right:"right",Left:"left"};t.datavisualization.TreeMap.LegendMode={Default:"default",Interactive:"interactive"};t.datavisualization.TreeMap.Position={TopLeft:"topleft",TopCenter:"topcenter",TopRight:"topright",CenterLeft:"centerleft",Center:"center",CenterRight:"centerright",BottomLeft:"bottomleft",BottomCenter:"bottomcenter",BottomRight:"bottomright"};t.datavisualization.TreeMap.Alignment={Near:"near",Center:"center",Far:"far"};t.datavisualization.TreeMap.VisibilityMode={Visible:"visible",HideOnExceededLength:"hideonexceededlength"}})(jQuery,Syncfusion)});
