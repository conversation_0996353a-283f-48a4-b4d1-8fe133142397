/*!
 *  filename: ej.localetexts.el-GR.min.js
 *  version : 20.2.0.45
 *  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
 *  Use of this code is subject to the terms of our license.
 *  A copy of the current license can be obtained at any time by e-mailing
 *  <EMAIL>. Any infringement will be prosecuted under
 *  applicable laws.
 */
(function (n, t) {
    var i, r;
    t.locales.indexOf("el-GR") < 0 && t.locales.push("el-GR");
    n.fn.Locale_elGR = function (n) {
        switch (n) {
            case "ejAutocomplete":
                t.Autocomplete.Locale = t.Autocomplete.Locale || {};
                t.Autocomplete.Locale["el-GR"] = { addNewText: "Add New", emptyResultText: "No suggestions", actionFailure: "The specified field doesn't exist in the given data source", watermarkText: " " };
                break;
            case "ejColorPicker":
                t.ColorPicker.Locale = t.ColorPicker.Locale || {};
                t.ColorPicker.Locale["el-GR"] = {
                    buttonText: { apply: "Apply", cancel: "Cancel", swatches: "Swatches" },
                    tooltipText: {
                        switcher: "Switcher",
                        addButton: "Add color",
                        basic: "Basic",
                        monoChrome: "Mono chrome",
                        flatColors: "Flat colors",
                        seaWolf: "Sea wolf",
                        webColors: "Web colors",
                        sandy: "Sandy",
                        pinkShades: "Pink shades",
                        misty: "Misty",
                        citrus: "Citrus",
                        vintage: "Vintage",
                        moonLight: "Moon light",
                        candyCrush: "Candy crush",
                        currentColor: "Current color",
                        selectedColor: "Selected color",
                    },
                };
                break;
            case "ejDatePicker":
                t.DatePicker.Locale = t.DatePicker.Locale || {};
                t.DatePicker.Locale["el-GR"] = { watermarkText: "Select date", buttonText: "Σήμερα" };
                break;
            case "ejDateRangePicker":
                t.DateRangePicker.Locale = t.DateRangePicker.Locale || {};
                t.DateRangePicker.Locale["el-GR"] = { ButtonText: { apply: "Apply", cancel: "Cancel", reset: "Reset" }, watermarkText: "Select range", customPicker: "Custom picker" };
                break;
            case "ejDateTimePicker":
                t.DateTimePicker.Locale = t.DateTimePicker.Locale || {};
                t.DateTimePicker.Locale["el-GR"] = { watermarkText: "Select datetime", buttonText: { today: "Σήμερα", timeNow: "Time Now", done: "Done", timeTitle: "Time" } };
                break;
            case "ejDialog":
                t.Dialog.Locale = t.Dialog.Locale || {};
                t.Dialog.Locale["el-GR"] = { tooltip: { close: "Close", collapse: "Collapse", restore: "Restore", maximize: "Maximize", minimize: "Minimize", expand: "Expand", unPin: "UnPin", pin: "Pin" }, closeIconTooltip: "close" };
                break;
            case "ejDropDownList":
                t.DropDownList.Locale = t.DropDownList.Locale || {};
                t.DropDownList.Locale["el-GR"] = { emptyResultText: "No suggestions,", watermarkText: " " };
                break;
            case "ejComboBox":
                t.ComboBox.Locale = t.ComboBox.Locale || {};
                t.ComboBox.Locale["el-GR"] = { noRecordsTemplate: "No Records Found", actionFailureTemplate: "The Request Failed" };
                break;
            case "ejMenu":
                t.Menu.Locale = t.Menu.Locale || {};
                t.Menu.Locale["el-GR"] = { titleText: "Menu" };
                break;
            case "ejCaptcha":
                t.Captcha.Locale = t.Captcha.Locale || {};
                t.Captcha.Locale["el-GR"] = { placeHolderText: "Type the code shown", CustomErrorMessage: "Invalid Captcha" };
                break;
            case "ejNumericTextbox":
                t.NumericTextbox.Locale = t.NumericTextbox.Locale || {};
                t.NumericTextbox.Locale["el-GR"] = { watermarkText: "Enter value" };
                break;
            case "ejPercentageTextbox":
                t.PercentageTextbox.Locale = t.PercentageTextbox.Locale || {};
                t.PercentageTextbox.Locale["el-GR"] = { watermarkText: "Enter value" };
                break;
            case "ejCurrencyTextbox":
                t.CurrencyTextbox.Locale = t.CurrencyTextbox.Locale || {};
                t.CurrencyTextbox.Locale["el-GR"] = { watermarkText: "Enter value" };
                break;
            case "ejExcelFilter":
                t.ExcelFilter.Locale = t.ExcelFilter.Locale || {};
                t.ExcelFilter.Locale["el-GR"] = {
                    SortNoSmaller: "Sort Smallest to Largest",
                    SortNoLarger: "Sort Largest to Smallest",
                    SortTextAscending: "Sort A to Z",
                    SortTextDescending: "Sort Z to A",
                    SortDateOldest: "Sort by Oldest",
                    SortDateNewest: "Sort by Newest",
                    SortByColor: "Sort By Color",
                    SortByCellColor: "Sort by Cell Color",
                    SortByFontColor: "Sort by Font Color",
                    FilterByColor: "Filter By Color",
                    CustomSort: "Custom Sort",
                    FilterByCellColor: "Filter by Cell Color",
                    FilterByFontColor: "Filter by Font Color",
                    ClearFilter: "Clear Filter",
                    NumberFilter: "Number Filters",
                    GuidFilter: "Guid Filters",
                    TextFilter: "Text Filters",
                    DateFilter: "Date Filters",
                    DateTimeFilter: "Date Time Filters",
                    SelectAll: "Select All",
                    Blanks: "Blanks",
                    Search: "Search",
                    Showrowswhere: "Show rows where",
                    NumericTextboxWaterMark: "Enter value",
                    StringMenuOptions: [
                        { text: "Equal", value: "equal" },
                        { text: "Not Equal", value: "notequal" },
                        { text: "Starts With", value: "startswith" },
                        { text: "Ends With", value: "endswith" },
                        { text: "Contains", value: "contains" },
                        { text: "Custom Filter", value: "customfilter" },
                    ],
                    NumberMenuOptions: [
                        { text: "Equal", value: "equal" },
                        { text: "Not Equal", value: "notequal" },
                        { text: "Less Than", value: "lessthan" },
                        { text: "Less Than Or Equal", value: "lessthanorequal" },
                        { text: "Greater Than", value: "greaterthan" },
                        { text: "Greater Than Or Equal", value: "greaterthanorequal" },
                        { text: "Between", value: "between" },
                        { text: "Custom Filter", value: "customfilter" },
                    ],
                    GuidMenuOptions: [
                        { text: "Equal", value: "equal" },
                        { text: "Not Equal", value: "notequal" },
                        { text: "Custom Filter", value: "customfilter" },
                    ],
                    DateMenuOptions: [
                        { text: "Equal", value: "equal" },
                        { text: "Not Equal", value: "notequal" },
                        { text: "Less Than", value: "lessthan" },
                        { text: "Less Than Or Equal", value: "lessthanorequal" },
                        { text: "Greater Than", value: "greaterthan" },
                        { text: "Greater Than Or Equal", value: "greaterthanorequal" },
                        { text: "Between", value: "between" },
                        { text: "Custom Filter", value: "customfilter" },
                    ],
                    DatetimeMenuOptions: [
                        { text: "Equal", value: "equal" },
                        { text: "Not Equal", value: "notequal" },
                        { text: "Less Than", value: "lessthan" },
                        { text: "Less Than Or Equal", value: "lessthanorequal" },
                        { text: "Greater Than", value: "greaterthan" },
                        { text: "Greater Than Or Equal", value: "greaterthanorequal" },
                        { text: "Between", value: "between" },
                        { text: "Custom Filter", value: "customfilter" },
                    ],
                    Top10MenuOptions: [
                        { text: "Top", value: "top" },
                        { text: "Bottom", value: "bottom" },
                    ],
                    title: "Custom Filter",
                    PredicateAnd: "AND",
                    PredicateOr: "OR",
                    OK: "OK",
                    MatchCase: "Match Case",
                    Cancel: "Cancel",
                    NoResult: "No Matches Found",
                    CheckBoxStatusMsg: "Not showing all items",
                    DatePickerWaterMark: "Select date",
                    DateTimePickerWaterMark: "Select date time",
                    True: "true",
                    False: "false",
                    AddToFilter: "Add current selection to filter",
                };
                break;
            case "ejFileExplorer":
                t.FileExplorer.Locale = t.FileExplorer.Locale || {};
                t.FileExplorer.Locale["el-GR"] = {
                    Folder: "Folder",
                    EmptyFolder: "This folder is empty",
                    ProtectedFolder: "You don't currently have permission to access this folder",
                    EmptyResult: "No items match your search",
                    Back: "Backward",
                    Forward: "Forward",
                    Upward: "Upward",
                    Refresh: "Refresh",
                    Addressbar: "Address bar",
                    Upload: "Upload",
                    Rename: "Rename",
                    Delete: "Delete",
                    Download: "Download",
                    Error: "Error",
                    PasteError: "Error",
                    UploadError: "Error",
                    RenameError: "Error",
                    Cut: "Cut",
                    Copy: "Copy",
                    Paste: "Paste",
                    Details: "Details",
                    Searchbar: "Search bar",
                    Open: "Open",
                    Search: "Search",
                    NewFolder: "New folder",
                    SortBy: "Sort by",
                    Size: "Size",
                    RenameAlert: "Please enter new name",
                    NewFolderAlert: "Please enter new folder name",
                    ContextMenuOpen: "Open",
                    ContextMenuNewFolder: "New folder",
                    ContextMenuDelete: "Delete",
                    ContextMenuRename: "Rename",
                    ContextMenuUpload: "Upload",
                    ContextMenuDownload: "Download",
                    ContextMenuCut: "Cut",
                    ContextMenuCopy: "Copy",
                    ContextMenuPaste: "Paste",
                    ContextMenuGetinfo: "Get info",
                    ContextMenuRefresh: "Refresh",
                    ContextMenuOpenFolderLocation: "Open folder location",
                    Item: "item",
                    Items: "items",
                    Selected: "selected",
                    ErrorOnFolderCreation: "This destination already contains a folder named '{0}'. Do you want to merge this folder content with already existing folder '{0}'?",
                    InvalidFileName: "A file name can't contain any of the following characters: \\/: *? \\ <> |",
                    GeneralError: "Please see browser console window for more information",
                    ErrorPath: "FileExplorer can't find '{0}'. Check the spelling and try again.",
                    UploadReplaceAlert: "File named '{0}' already exists. Replace old file with new one?",
                    PasteReplaceAlert: "File named '{0}' already exists. Replace old file with new one?",
                    DuplicateAlert: "There is already a file with the same name '{0}'. Do you want to create file with duplicate name",
                    DuplicateFileCreation: "There is already a file with the same name in this location. Do you want to rename '{0}' to '{1}'?",
                    DeleteFolder: " Are you sure you want to delete ",
                    DeleteMultipleFolder: "Are you sure you want to delete these {0} items?",
                    CancelPasteAction: "The destination folder is a subfolder of source folder.",
                    OkButton: "OK",
                    ContextMenuSortBy: "Sort by",
                    CancelButton: "Cancel",
                    YesToAllButton: "Yes to all",
                    NoToAllButton: "No to all",
                    YesButton: "Yes",
                    NoButton: "No",
                    SkipButton: "Skip",
                    Grid: "Grid view",
                    Tile: "Tile view",
                    LargeIcons: "Large icons",
                    Name: "Name",
                    Location: "Location",
                    Type: "Item type",
                    Layout: "Layout",
                    Created: "Created",
                    Accessed: "Accessed",
                    Modified: "Modified",
                    Permission: "Permission",
                    DialogCloseToolTip: "Close",
                    UploadSettings: {
                        buttonText: { upload: "Upload", browse: "Browse", cancel: "Cancel", close: "Close" },
                        dialogText: { title: "Upload Box", name: "Name", size: "Size", status: "Status" },
                        dropAreaText: "Drop files or click to upload",
                        filedetail: "The selected file size is too large. Please select a file within the valid size.",
                        denyError: "Files with #Extension extensions are not allowed.",
                        allowError: "Only files with #Extension extensions are allowed.",
                        cancelToolTip: "Cancel",
                        removeToolTip: "Remove",
                        retryToolTip: "Retry",
                        completedToolTip: "Completed",
                        failedToolTip: "Failed",
                        closeToolTip: "Close",
                    },
                };
                break;
            case "ejGantt":
                t.Gantt.Locale = t.Gantt.Locale || {};
                t.Gantt.Locale["el-GR"] = {
                    emptyRecord: "No records to display",
                    unassignedTask: "Unassigned Task",
                    alertTexts: {
                        indentAlert: "There is no Gantt record is selected to perform the indent",
                        outdentAlert: "There is no Gantt record is selected to perform the outdent",
                        predecessorEditingValidationAlert: "Cyclic dependency occurred, please check the predecessor",
                        predecessorAddingValidationAlert: "Fill all the columns in predecessor table",
                        idValidationAlert: "Duplicate ID",
                        dateValidationAlert: "Invalid end date",
                        dialogResourceAlert: "Fill all the columns in resource table",
                    },
                    columnHeaderTexts: {
                        taskId: "ID",
                        taskName: "Task Name",
                        startDate: "Start Date",
                        endDate: "End Date",
                        resourceInfo: "Resources",
                        duration: "Duration",
                        status: "Progress",
                        taskMode: "Task Mode",
                        serialNumber: "S.No",
                        subTasksStartDate: "SubTasks Start Date",
                        subTasksEndDate: "SubTasks End Date",
                        scheduleStartDate: "Schedule Start Date",
                        scheduleEndDate: "Schedule End Date",
                        predecessor: "Predecessors",
                        type: "Type",
                        offset: "Offset",
                        baselineStartDate: "Baseline Start Date",
                        baselineEndDate: "Baseline End Date",
                        WBS: "WBS",
                        WBSPredecessor: "WBS Predecessor",
                        dialogCustomFieldName: "Column Name",
                        dialogCustomFieldValue: "Value",
                        notes: "Notes",
                        taskType: "Task Type",
                        work: "Work",
                        unit: "Unit",
                        effortDriven: "Effort Driven",
                        resourceName: "Resource Name",
                    },
                    editDialogTexts: { addFormTitle: "New Task", editFormTitle: "Edit Task", saveButton: "Save", deleteButton: "Delete", cancelButton: "Cancel", addPredecessor: "Add New", removePredecessor: "Remove", addButton: "Add" },
                    columnDialogTexts: {
                        field: "Field",
                        headerText: "Header Text",
                        editType: "Edit Type",
                        filterEditType: "Filter Edit Type",
                        allowFiltering: "Allow Filtering",
                        allowFilteringBlankContent: "Allow Filtering Blank Content",
                        allowSorting: "Allow Sorting",
                        visible: "Visible",
                        width: "Width",
                        textAlign: "Text Alignment",
                        headerTextAlign: "Header Text Alignment",
                        columnsDropdownData: "Column Dropdown Data",
                        dropdownTableText: "Text",
                        dropdownTableValue: "Value",
                        addData: "Add",
                        deleteData: "Remove",
                        allowCellSelection: "Allow Cell Selection",
                        displayAsCheckbox: "Display As Checkbox",
                        clipMode: "Clip Mode",
                        tooltip: "Tooltip",
                        showInColumnChooser: "Show In Column Chooser",
                        headerTooltip: "Header Tooltip",
                    },
                    editTypeTexts: { string: "String", numeric: "Numeric", datePicker: "Date Picker", dateTimePicker: "Date Time Picker", dropdown: "Dropdown", boolean: "Boolean" },
                    textAlignTypes: { right: "Right", left: "Left", center: "Center" },
                    clipModeTexts: { clip: "Clip", ellipsis: "Ellipsis" },
                    toolboxTooltipTexts: {
                        addTool: "Add",
                        editTool: "Edit",
                        saveTool: "Update",
                        deleteTool: "Delete",
                        cancelTool: "Cancel",
                        searchTool: "Search",
                        indentTool: "Indent",
                        outdentTool: "Outdent",
                        expandAllTool: "Expand All",
                        collapseAllTool: "Collapse All",
                        nextTimeSpanTool: "Next Timespan",
                        prevTimeSpanTool: "Previous Timespan",
                        criticalPathTool: "Critical Path",
                        excelExportTool: "Excel Export",
                        printTool: "Print",
                        pdfExportTool: "PDF Export",
                    },
                    durationUnitTexts: { days: "days", hours: "hours", minutes: "minutes", day: "day", hour: "hour", minute: "minute" },
                    durationUnitEditText: { minute: ["m", "min", "minute", "minutes"], hour: ["h", "hr", "hour", "hours"], day: ["d", "dy", "day", "days"] },
                    workUnitTexts: { days: "days", hours: "hours", minutes: "minutes" },
                    taskTypeTexts: { fixedWork: "Fixed Work", fixedUnit: "Fixed Units", fixedDuration: "Fixed Duration" },
                    effortDrivenTexts: { yes: "Yes", no: "No" },
                    contextMenuTexts: { taskDetailsText: "Task Details...", addNewTaskText: "Add New Task", indentText: "Indent", outdentText: "Outdent", deleteText: "Delete", aboveText: "Above", belowText: "Below" },
                    newTaskTexts: { newTaskName: "New Task" },
                    columnMenuTexts: {
                        sortAscendingText: "Sort Ascending",
                        sortDescendingText: "Sort Descending",
                        columnsText: "Columns",
                        insertColumnLeft: "Insert Column Left",
                        insertColumnRight: "Insert Column Right",
                        deleteColumn: "Delete Column",
                        renameColumn: "Rename Column",
                    },
                    taskModeTexts: { manual: "Manual", auto: "Auto" },
                    columnDialogTitle: { insertColumn: "Insert Column", deleteColumn: "Delete Column", renameColumn: "Rename Column" },
                    deleteColumnText: "Are you sure you want to delete this column?",
                    okButtonText: "OK",
                    cancelButtonText: "Cancel",
                    confirmDeleteText: "Confirm Delete",
                    predecessorEditingTexts: { fromText: "From", toText: "To" },
                    dialogTabTitleTexts: { generalTabText: "General", predecessorsTabText: "Predecessors", resourcesTabText: "Resources", customFieldsTabText: "Custom Fields", notesTabText: "Notes" },
                    predecessorCollectionText: [
                        { id: "SS", text: "Start-Start", value: "Start-Start" },
                        { id: "SF", text: "Start-Finish", value: "Start-Finish" },
                        { id: "FS", text: "Finish-Start", value: "Finish-Start" },
                        { id: "FF", text: "Finish-Finish", value: "Finish-Finish" },
                    ],
                    linkValidationRuleText: {
                        taskBeforePredecessor: "You moved '{0}' to start before '{1}' finishes and the two tasks are linked. As the result, the links cannot be honored. Select one action below to perform",
                        taskAfterPredecessor: "You moved '{0}' away from '{1}' and the two tasks are linked. As the result, the links cannot be honored. Select one action below to perform",
                    },
                    linkValidationDialogTitle: "Validate Editing",
                    linkValidationRuleOptions: { cancel: "Cancel, keep the existing link", removeLink: "Remove the link and move '{0}' to start on '{1}'.", preserveLink: "Move the '{0}' to start on '{1}' and keep the link." },
                    connectorLineDialogText: { from: "From", to: "To", taskLink: "Task Link", lag: "Lag", okButtonText: "OK", cancelButtonText: "Cancel", deleteButtonText: "Delete", title: "Task Dependency" },
                    nullText: "Null",
                };
                break;
            case "ejGrid":
                t.Grid.Locale = t.Grid.Locale || {};
                t.Grid.Locale["el-GR"] = {
                    EmptyRecord: "Δεν υπάρχουν εγγραφές",
                    GroupDropArea: "Drag a column header here to group its column",
                    DeleteOperationAlert: "No records selected for delete operation",
                    EditOperationAlert: "No records selected for edit operation",
                    SaveButton: "Save",
                    OKButton: "OK",
                    CancelButton: "Cancel",
                    EditFormTitle: "Details of ",
                    AddFormTitle: "Add New Record",
                    GroupCaptionFormat: "{{:headerText}}: {{:key}} - {{:count}} {{if count == 1 }} item {{else}} items {{/if}} ",
                    BatchSaveConfirm: "Are you sure you want to save changes?",
                    BatchSaveLostChanges: "Unsaved changes will be lost. Are you sure you want to continue?",
                    ConfirmDelete: "Are you sure you want to Delete Record?",
                    CancelEdit: "Are you sure you want to Cancel the changes?",
                    PagerInfo: "{0} από {1} σελίδες ({2} εγγραφές)",
                    FrozenColumnsViewAlert: "Frozen columns should be in grid view area",
                    FrozenColumnsScrollAlert: "Enable allowScrolling while using frozen Columns",
                    FrozenNotSupportedException: "Frozen Columns and Rows are not supported for Grouping, Row Template, Detail Template, Hierarchy Grid and Batch Editing",
                    Add: "Add",
                    Edit: "Edit",
                    Delete: "Delete",
                    Update: "Update",
                    Cancel: "Cancel",
                    Done: "Done",
                    Columns: "Columns",
                    SelectAll: "(Select All)",
                    PrintGrid: "Print",
                    ExcelExport: "Excel Export",
                    WordExport: "Word Export",
                    PdfExport: "PDF Export",
                    StringMenuOptions: [
                        { text: "StartsWith", value: "StartsWith" },
                        { text: "EndsWith", value: "EndsWith" },
                        { text: "Contains", value: "Contains" },
                        { text: "Equal", value: "Equal" },
                        { text: "NotEqual", value: "NotEqual" },
                    ],
                    NumberMenuOptions: [
                        { text: "LessThan", value: "LessThan" },
                        { text: "GreaterThan", value: "GreaterThan" },
                        { text: "LessThanOrEqual", value: "LessThanOrEqual" },
                        { text: "GreaterThanOrEqual", value: "GreaterThanOrEqual" },
                        { text: "Equal", value: "Equal" },
                        { text: "NotEqual", value: "NotEqual" },
                        { text: "Between", value: "Between" },
                    ],
                    PredicateAnd: "AND",
                    PredicateOr: "OR",
                    Filter: "Filter",
                    FilterMenuCaption: "Filter Value",
                    FilterMenuFromCaption: "From",
                    FilterMenuToCaption: "To",
                    FilterbarTitle: "'s filter bar cell",
                    MatchCase: "Match Case",
                    Clear: "Clear",
                    ResponsiveFilter: "Filter",
                    ResponsiveSorting: "Sort",
                    Search: "Search",
                    NumericTextBoxWaterMark: "Enter value",
                    DatePickerWaterMark: "Select date",
                    EmptyDataSource: "DataSource must not be empty at initial load since columns are generated from dataSource in AutoGenerate Column Grid",
                    ForeignKeyAlert: "The updated value should be a valid foreign key value",
                    True: "true",
                    False: "false",
                    UnGroup: "Click here to ungroup",
                    AddRecord: "Add Record",
                    EditRecord: "Edit Record",
                    DeleteRecord: "Delete Record",
                    Save: "Save",
                    Grouping: "Group",
                    Ungrouping: "Ungroup",
                    SortInAscendingOrder: "Sort In Ascending Order",
                    SortInDescendingOrder: "Sort In Descending Order",
                    NextPage: "Next Page",
                    PreviousPage: "Previous Page",
                    FirstPage: "First Page",
                    LastPage: "Last Page",
                    EmptyRowValidationMessage: "Atleast one field must be updated",
                    NoResult: "No Matches Found",
                };
                break;
            case "ejmobile":
                t.mobile.Grid &&
                    ((t.mobile.Grid.Locale = t.mobile.Grid.Locale || {}),
                        (t.mobile.Grid.Locale["el-GR"] = {
                            emptyResult: "No records to display",
                            filterValidation: "Enter valid filter data",
                            filterTypeValidation: "Enter valid filter data. The current filter column is of type",
                            captionText: "Items",
                            spinnerText: "loading...",
                            HideColumnAlert: "At Least one column must be displayed in grid",
                            columnSelectorText: "Hide Column",
                            columnSelectorDone: "OK",
                            columnSelectorCancel: "Cancel",
                            columnSelectorWarning: "Warning",
                            filterOk: "Ok",
                            filterWarning: "Warning",
                        }));
                t.mobile.DatePicker &&
                    ((t.mobile.DatePicker.Locale = t.mobile.DatePicker.Locale || {}),
                        (t.mobile.DatePicker.Locale["el-GR"] = { confirmText: "Done", Windows: { cancelText: "Cancel", headerText: "CHOOSE DATE", toolbarConfirmText: "done", toolbarCancelText: "close" } }));
                t.mobile.TimePicker &&
                    ((t.mobile.TimePicker.Locale = t.mobile.TimePicker.Locale || {}),
                        (t.mobile.TimePicker.Locale["el-GR"] = {
                            confirmText: "Done",
                            AM: "AM",
                            PM: "PM",
                            Android: { headerText: "Set Time" },
                            Windows: { cancelText: "Cancel", headerText: "CHOOSE TIME", toolbarCancelText: "close", toolbarConfirmText: "done" },
                        }));
                break;
            case "ejPivotChart":
                t.PivotChart.Locale = t.PivotChart.Locale || {};
                t.PivotChart.Locale["el-GR"] = {
                    Measure: "Measure",
                    Row: "Row",
                    Column: "Column",
                    Value: "Value",
                    Expand: "Expand",
                    Collapse: "Collapse",
                    Exit: "Exit",
                    ChartTypes: "Chart Types",
                    TDCharts: "3D Charts",
                    Tooltip: "Tooltip",
                    Exporting: "Exporting",
                    Line: "Line",
                    Spline: "Spline",
                    Column: "Column",
                    Area: "Area",
                    SplineArea: "Spline Area",
                    StepLine: "Step Line",
                    StepArea: "Step Area",
                    Pie: "Pie",
                    Bar: "Bar",
                    StackingArea: "Stacking Area",
                    StackingColumn: "Stacking Column",
                    StackingBar: "Stacking Bar",
                    Pyramid: "Pyramid",
                    Funnel: "Funnel",
                    Doughnut: "Doughnut",
                    Scatter: "Scatter",
                    Bubble: "Bubble",
                    TreeMap: "TreeMap",
                    ColumnTD: "Column 3D",
                    PieTD: "Pie 3D",
                    BarTD: "Bar 3D",
                    StackingBarTD: "StackingBar 3D",
                    StackingColumnTD: "StackingColumn 3D",
                    Excel: "Excel",
                    Word: "Word",
                    Pdf: "PDF",
                    PNG: "PNG",
                    EMF: "EMF",
                    GIF: "GIF",
                    JPG: "JPG",
                    BMP: "BMP",
                    ZoomIn: "Zoom In",
                    ZoomOut: "Zoom Out",
                    Legend: "Legend",
                    SmartLabels: "Smart Labels",
                    Interactions: "Interactions",
                    Zooming: "Zooming",
                    Rotate45: "Rotate45",
                    Rotate90: "Rotate90",
                    Trim: "Trim",
                    MultipleRows: "Multiple Rows",
                    Wrap: "Wrap",
                    Hide: "Hide",
                    WrapByWord: "Wrap By word",
                    CrossHair: "Cross Hair",
                    TrackBall: "Track Ball",
                    DisableTD: "Disable 3D Charts",
                    None: "None",
                    Exception: "Exception",
                    OK: "OK",
                };
                break;
            case "ejPivotClient":
                t.PivotClient.Locale = t.PivotClient.Locale || {};
                t.PivotClient.Locale["el-GR"] = {
                    NoReports: "No Reports Found in DB",
                    Sort: "Sort",
                    Search: "Search",
                    SelectField: "Select field",
                    LabelFilterLabel: "Show the items for which the label",
                    ValueFilterLabel: "Show the items for which",
                    ClearSorting: "Clear Sorting",
                    ClearFilterFrom: "Clear Filter From",
                    SortAtoZ: "Sort A to Z",
                    SortZtoA: "Sort Z to A",
                    LabelFilters: "Label Filters  ",
                    BeginsWith: "Begins With",
                    DoesNotBeginsWith: "Does Not Begin With",
                    EndsWith: "Ends With",
                    NotEndsWith: "Not Ends With",
                    DoesNotEndsWith: "Does Not End With",
                    Contains: "Contains",
                    DoesNotContains: "Does Not Contain",
                    ValueFilters: "Value Filters",
                    ClearFilter: "Clear Filter",
                    Equals: "Equals",
                    DoesNotEquals: "Does Not Equal",
                    NotEquals: "Not Equals",
                    GreaterThan: "Greater Than",
                    GreaterThanOrEqualTo: "Greater Than Or Equal To",
                    LessThan: "Less Than",
                    LessThanOrEqualTo: "Less Than Or Equal To",
                    Between: "Between",
                    NotBetween: "Not Between",
                    Top10: "Top Count",
                    GreaterThan: "Greater Than",
                    IsGreaterThan: "Is Greater Than",
                    IsGreaterThanOrEqualTo: "Is Greater Than Or Equal To",
                    IsLessThan: "Is Less Than",
                    IsLessThanOrEqualTo: "Is Less Than Or Equal To",
                    DeferUpdate: "Defer Update",
                    MDXQuery: "MDX Query",
                    Column: "Column",
                    Row: "Row",
                    Slicer: "Slicer",
                    CubeSelector: "Cube Selector",
                    ReportName: "Report Name",
                    NewReport: "New Report",
                    CubeDimensionBrowser: "Cube Dimension Browser",
                    AddReport: "Add Report",
                    RemoveReport: "Remove Report",
                    CannotRemoveSingleReport: "Cannot remove single report",
                    AreYouSureToDeleteTheReport: "Are you sure to delete the Report",
                    RenameReport: "Rename Report",
                    ChartTypes: "Chart Types",
                    ToggleAxis: "Toggle Axis",
                    Load: "Load",
                    ExportToExcel: "Export To Excel",
                    ExportToWord: "Export To Word",
                    ExportToPdf: "Export To Pdf",
                    FullScreen: "Full Screen",
                    Grid: "Grid",
                    Chart: "Chart",
                    OK: "<u>O</u>K",
                    Cancel: "<u>C</u>ancel",
                    Close: "Close",
                    AddToColumn: "Add to Column",
                    AddToRow: "Add to Row",
                    AddToSlicer: "Add to Slicer",
                    MeasureEditor: "Measure Editor",
                    MemberEditor: "Member Editor",
                    Measures: "Measures",
                    SortOrFilterColumn: "Sort/Filter (Column)",
                    SortOrFilterRow: "Sort/Filter (Row)",
                    SortingAndFiltering: "Sorting And Filtering",
                    Sorting: "Sorting",
                    Measure: "<u>M</u>easure",
                    Order: "Order",
                    Filtering: "Filtering",
                    Condition: "C<u>o</u>ndition",
                    Value: "Val<u>u</u>e",
                    PreserveHierarchy: "P<u>r</u>eserve  Hierarchy",
                    Ascending: "<u>A</u>scending",
                    Descending: "D<u>e</u>scending",
                    Enable: "E<u>n</u>able",
                    Disable: "D<u>i</u>sable",
                    and: "<u>a</u>nd",
                    EqualTo: "EqualTo",
                    NotEquals: "NotEquals",
                    GreaterThan: "GreaterThan",
                    GreaterThanOrEqualTo: "GreaterThanOrEqualTo",
                    LessThan: "LessThan",
                    LessThanOrEqualTo: "LessThanOrEqualTo",
                    Between: "Between",
                    NotBetween: "NotBetween",
                    ReportList: "Report List",
                    Line: "Line",
                    Spline: "Spline",
                    Column: "Column",
                    Area: "Area",
                    SplineArea: "Spline Area",
                    StepLine: "Step Line",
                    StepArea: "Step Area",
                    Pie: "Pie",
                    Bar: "Bar",
                    StackingArea: "Stacking Area",
                    StackingColumn: "Stacking Column",
                    StackingBar: "Stacking Bar",
                    Pyramid: "Pyramid",
                    Funnel: "Funnel",
                    Doughnut: "Doughnut",
                    Scatter: "Scatter",
                    Bubble: "Bubble",
                    WaterFall: "WaterFall",
                    TreeMap: "TreeMap",
                    Alert: "Alert",
                    MDXAlertMsg: "Please add a measure, dimension, or hierarchy in an appropriate axis to view the MDX Query.",
                    FilterSortRowAlertMsg: "Dimension is not found in the row axis. Please add Dimension element in the row axis for sorting/filtering.",
                    FilterSortColumnAlertMsg: "Dimension is not found in the column axis. Please add Dimension element in the column axis for sorting/filtering.",
                    FilterSortcolMeasureAlertMsg: "Please add measure to the column axis",
                    FilterSortrowMeasureAlertMsg: "Please add measure to the row axis",
                    FilterSortElementAlertMsg: "Element is not found in the column axis. Please add an element in column axis for sorting/filtering.",
                    SelectRecordAlertMsg: "Please select a valid report.",
                    FilterMeasureSelectionAlertMsg: "Please select a valid measure.",
                    FilterConditionAlertMsg: "Please set a valid condition.",
                    FilterStartValueAlertMsg: "Please set a start value.",
                    FilterEndValueAlertMsg: "Please set a end value.",
                    FilterInvalidAlertMsg: "Invalid operation !",
                    Remove: "Remove",
                    Save: "Save",
                    SaveAs: "Save As",
                    SelectReport: "Select Report",
                    DBReport: "Report Manipulation in DB",
                    Rename: "Rename",
                    Remove: "Remove",
                    SetReportNameAlertMsg: "Please set report name.",
                    MultipleItems: "Multiple items",
                    All: "All",
                    CalculatedMember: "Calculated Member",
                    Caption: "Caption:",
                    Expression: "Expression:",
                    MemberType: "MemberType:",
                    FormatString: "Format String:",
                    MultipleMeasure: "More than one measure cannot be sliced.",
                    DuplicateCalcMeasure: "Calculated Member with same name already exists.",
                    EmptyField: "Calculated Member name or Expression should not be empty.",
                    EmptyFormat: "Format String for Calculated Member is empty.",
                    Warning: "Warning",
                    Confirm: "Calculated Member with the same name already exists. Do you want to replace?",
                    KPIs: "KPIs",
                    Collection: "Collection",
                    Report: "Report",
                    AddCurrentSelectionToFilter: "Add current selection to filter",
                    SaveMsg: "Report saved successfully!!!",
                    RenameMsg: "Report renamed successfully!!!",
                    RemoveMsg: "Report removed successfully!!!",
                    Success: "Success",
                    KpiAlertMsg: "The field you are moving cannot be placed in that area of the report",
                    NotAllItemsShowing: "Not all child nodes are shown",
                    EditorLinkPanelAlert: "The members have more than 1000 items under one or more parent. Only the first 1000 items are displayed under each parent.",
                    NamedSetAlert: "Named sets of same field cannot be added to the PivotTable report at the same time. Click OK to remove ' <Set 1> ' named set and add ' <Set 2> ' named set.",
                    Exception: "Exception",
                };
                break;
            case "ejPivotGauge":
                t.PivotGauge.Locale = t.PivotGauge.Locale || {};
                t.PivotGauge.Locale["el-GR"] = { RevenueGoal: "Revenue Goal", RevenueValue: "Revenue Value", Exception: "Exception" };
                break;
            case "ejPager":
                t.Pager.Locale = t.Pager.Locale || {};
                t.Pager.Locale["el-GR"] = {
                    pagerInfo: "{0} από {1} σελίδες ({2} εγγραφές)",
                    firstPageTooltip: "Go to first page",
                    lastPageTooltip: "Go to last page",
                    nextPageTooltip: "Go to next page",
                    previousPageTooltip: "Go to previous page",
                    nextPagerTooltip: "Go to next Pager",
                    previousPagerTooltip: "Go to previous Pager",
                };
                break;
            case "ejPdfViewer":
                t.PdfViewer.Locale = t.PdfViewer.Locale || {};
                t.PdfViewer.Locale["el-GR"] = {
                    toolbar: {
                        print: { headerText: "Print", contentText: "Print the PDF document." },
                        first: { headerText: "First", contentText: "Go to the first page of the PDF document." },
                        previous: { headerText: "Previous", contentText: "Go to the previous page of the PDF document." },
                        next: { headerText: "Next", contentText: "Go to the next page of the PDF document." },
                        last: { headerText: "Last", contentText: "Go to the last page of the PDF document." },
                        zoomIn: { headerText: "Zoom-In", contentText: "Zoom in to the PDF document." },
                        zoomOut: { headerText: "Zoom-Out", contentText: "Zoom out of the PDF document." },
                        pageIndex: { headerText: "Page Number", contentText: "Current page number to view." },
                        zoom: { headerText: "Zoom", contentText: "Zoom in or out on the PDF document." },
                        fitToWidth: { headerText: "Fit to Width", contentText: "Fit the PDF page to the width of the container." },
                        fitToPage: { headerText: "Fit to Page", contentText: "Fit the PDF page to the container." },
                        search: { headerText: "Search Text", contentText: "Search text in the PDF pages." },
                        download: { headerText: "Download", contentText: "Download the PDF document." },
                        highlight: { headerText: "Highlight Text", contentText: "Highlight text in the PDF pages." },
                        strikeout: { headerText: "Strikethrough Text", contentText: "Strikethrough text in the PDF pages." },
                        underline: { headerText: "Underline Text", contentText: "Underline text in the PDF pages." },
                        signature: { headerText: "Signature", contentText: "Add or create hand-written signature." },
                        select: { headerText: "Selection", contentText: "Selection tool for text." },
                        scroll: { headerText: "Panning", contentText: "Click to pan around the document" },
                    },
                    contextMenu: {
                        copy: { contentText: "Copy" },
                        googleSearch: { contentText: "Search google" },
                        Find: { contentText: "Find:" },
                        matchCase: { contentText: "Match Case" },
                        auto: { contentText: "Auto" },
                        openPopup: { contentText: "Open Pop-Up Note" },
                        Delete: { contentText: "Delete" },
                        properties: { contentText: "Properties...." },
                    },
                    propertyWindow: {
                        underlineProperties: { contentText: "Underline Properties" },
                        strikeOutProperties: { contentText: "StrikeOut Properties" },
                        highlightProperties: { contentText: "Highlight Properties" },
                        signatureProperties: { contentText: "Signature Properties" },
                        appearance: { contentText: "Appearance" },
                        general: { contentText: "General" },
                        color: { contentText: "Color:" },
                        opacity: { contentText: "Opacity:" },
                        author: { contentText: "Author:" },
                        subject: { contentText: "Subject:" },
                        modified: { contentText: "Modified:" },
                        ok: { contentText: "OK" },
                        cancel: { contentText: "Cancel" },
                        locked: { contentText: "Locked" },
                    },
                    signatureWindow: { Signature: { contentText: "Add Signature" }, Add: { contentText: "Add" }, clear: { contentText: "Clear" } },
                    waitingPopup: { print: { contentText: "Preparing document for printing..." } },
                };
                break;
            case "ejPivotGrid":
                t.PivotGrid.Locale = t.PivotGrid.Locale || {};
                t.PivotGrid.Locale["el-GR"] = {
                    Total: "Total",
                    GrandTotal: "Grand Total",
                    Sort: "Sort",
                    SelectField: "select Field",
                    LabelFilterLabel: "Show the items for which the label",
                    ValueFilterLabel: "Show the items for which",
                    ClearSorting: "Clear Sorting",
                    ClearFilterFrom: "Clear Filter From",
                    SortAtoZ: "Sort A to Z",
                    SortZtoA: "Sort Z to A",
                    and: "<u>a</u>nd",
                    LabelFilters: "Label Filters  ",
                    BeginsWith: "Begins With",
                    NotBeginsWith: "Not Begins With",
                    EndsWith: "Ends With",
                    NotEndsWith: "Not Ends With",
                    Contains: "Contains",
                    NotContains: "Not Contains",
                    ValueFilters: "Value Filters",
                    ClearFilter: "Clear Filter",
                    Equals: "Equals",
                    NotEquals: "Not Equals",
                    GreaterThan: "Greater Than ",
                    GreaterThanOrEqualTo: "Greater Than Or Equal To ",
                    LessThan: "Less Than ",
                    LessThanOrEqualTo: "Less Than Or Equal To ",
                    Between: "Between",
                    NotBetween: "Not Between",
                    DoesNotBeginsWith: "Does Not Begin With",
                    DoesNotEndsWith: "Does Not Ends With",
                    DoesNotContains: "Does Not Contains",
                    DoesNotEquals: "Does Not Equals",
                    IsGreaterThan: "Is Greater Than",
                    IsGreaterThanOrEqualTo: "Is Greater Than Or Equal To",
                    IsLessThan: "Is Less Than",
                    IsLessThanOrEqualTo: "Is Less Than Or Equal To",
                    NumberFormatting: "Number Formatting",
                    FrozenHeaders: "Frozen Headers",
                    CellSelection: "Cell Selection",
                    CellContext: "Cell Context",
                    ColumnResize: "Column Resize",
                    Layouts: "Layouts",
                    NormalLayout: "Normal Layout",
                    NormalTopSummary: "NormalTopSummary Layout",
                    NoSummaries: "NoSummaries Layout",
                    ExcelLikeLayout: "Excel Like Layout",
                    FrozenHeader: "Frozen Header",
                    AdvancedFiltering: "Advanced Filtering",
                    Amount: "Amount",
                    Quantity: "Quantity",
                    Measures: "Measures",
                    NumberFormats: "Number Formats",
                    Exporting: "Exporting",
                    FileName: "File Name",
                    ToolTip: "Tool Tip",
                    RTL: "RTL",
                    CollapseByDefault: "Collapse By Default",
                    EnableDisablePaging: "Enable / Disable Paging",
                    PagingOptions: "Paging Options",
                    CategoricalPageSize: "Categorical Page Size",
                    SeriesPageSize: "Series Page Size",
                    HyperLink: "HyperLink",
                    CellEditing: "Cell Editing",
                    GroupingBar: "Grouping Bar",
                    SummaryCustomization: "Summary Customization",
                    SummaryTypes: "Summary Types",
                    SummaryType: "Summary Type",
                    EnableRowHeaderHyperlink: "Enable RowHeaderHyperLink",
                    EnableColumnHeaderHyperlink: "Enable ColumnHeaderHyperLink",
                    EnableValueCellHyperlink: "Enable ValueCellHyperLink",
                    EnableSummaryCellHyperlink: "Enable SummaryCellHyperLink",
                    HideGrandTotal: "Hide GrandTotal",
                    HideSubTotal: "Hide SubTotal",
                    Row: "Row",
                    Column: "Column",
                    Both: "Both",
                    Sum: "Sum",
                    Average: "Average",
                    Count: "Count",
                    Min: "Min",
                    Max: "Max",
                    Row: "Row",
                    Column: "Column",
                    Both: "Both",
                    Excel: "Excel",
                    Word: "Word",
                    PDF: "PDF",
                    CSV: "CSV",
                    AddToFilter: "Add to Filter",
                    AddToRow: "Add to Row",
                    AddToColumn: "Add to Column",
                    AddToValues: "Add to Values",
                    Warning: "Warning",
                    Error: "Error",
                    GroupingBarAlertMsg: "The field you are moving cannot be placed in that area of the report",
                    Measures: "Measures",
                    Expand: "Expand",
                    Collapse: "Collapse",
                    ToolTipRow: "Row",
                    ToolTipColumn: "Column",
                    ToolTipValue: "Value",
                    NoValue: "No value",
                    SeriesPage: "Series Page",
                    CategoricalPage: "Categorical Page",
                    DragFieldHere: "Drag field here",
                    ColumnArea: "Drop column here",
                    RowArea: "Drop row here",
                    ValueArea: "Drop values here",
                    Close: "Close",
                    OK: "OK",
                    Cancel: "Cancel",
                    Remove: "Remove",
                    Goal: "Goal",
                    Status: "Status",
                    Trend: "Trend",
                    Value: "Value",
                    ConditionalFormattingErrorMsg: "The given value is not matched",
                    ConditionalFormattingConformMsg: "Are you sure you want to remove the selected format?",
                    EnterOperand1: "Enter Operand1",
                    EnterOperand2: "Enter Operand2",
                    ConditionalFormatting: "Conditional Formatting",
                    Condition: "Conditional Type",
                    Value1: "Value1",
                    Value2: "Value2",
                    Editcondtion: "Edit Condition",
                    AddNew: "Add New",
                    Format: "Format",
                    Backcolor: "Back Color",
                    Borderrange: "Border Range",
                    Borderstyle: "Border Style",
                    Fontsize: "Font Size",
                    Fontstyle: "Font Style",
                    Bordercolor: "Border Color",
                    NoMeasure: "Please add any measure",
                    Solid: "Solid",
                    Dashed: "Dashed",
                    Dotted: "Dotted",
                    Double: "Double",
                    Groove: "Groove",
                    Inset: "Inset",
                    Outset: "Outset",
                    Ridge: "Ridge",
                    None: "None",
                    Algerian: "Algerian",
                    Arial: "Arial",
                    BodoniMT: "Bodoni MT",
                    BritannicBold: "Britannic Bold",
                    Cambria: "Cambria",
                    Calibri: "Calibri",
                    CourierNew: "Courier New",
                    DejaVuSans: "DejaVu Sans",
                    Forte: "Forte",
                    Gerogia: "Gerogia",
                    Impact: "Impact",
                    SegoeUI: "Segoe UI",
                    Tahoma: "Tahoma",
                    TimesNewRoman: "Times New Roman",
                    Verdana: "Verdana",
                    CubeDimensionBrowser: "Cube Dimension Browser",
                    SelectHierarchy: "Select Hierarchy",
                    CalculatedField: "Calculated Field",
                    Name: "Name:",
                    Add: "Add",
                    Formula: "Formula:",
                    Delete: "Delete",
                    Fields: "Fields:",
                    CalculatedFieldNameNotFound: "Given CalculatedField name is not found",
                    InsertField: "Insert Field",
                    EmptyField: "Please enter Calculated field name or formula",
                    NotValid: "Given formula is not valid",
                    NotPresent: "Value field used in any of the Calculated Field formula is not present in the PivotGrid",
                    Confirm: "Calculated field with the same name already exists. Do you want to Replace ?",
                    CalcValue: "Calculated field can be inserted only in value area field",
                    MultipleItems: "Multiple items",
                    All: "All",
                    Search: "Search",
                    Fontcolor: "Font Color",
                    AddCurrentSelectionToFilter: "Add current selection to filter",
                    Months: "Months",
                    Days: "Days",
                    Quarters: "Quarters",
                    Years: "Years",
                    Qtr: "Qtr",
                    Quarter: "Quarter",
                    NoRecordsToDisplay: "No records to display.",
                    EnterFormatName: "Enter Format Name",
                    FormatName: "Format Name",
                    RemoveFormat: "Remove Format",
                    Edit: "Edit",
                    DuplicateFormatName: "Duplicate Format Name",
                    NotAllItemsShowing: "Not all child nodes are shown",
                    EditorLinkPanelAlert: "The members have more than 1000 items under one or more parent. Only the first 1000 items are displayed under each parent.",
                    Exception: "Exception",
                };
                break;
            case "ejPivotPager":
                t.PivotPager.Locale = t.PivotPager.Locale || {};
                t.PivotPager.Locale["el-GR"] = { SeriesPage: "Series Page", CategoricalPage: "Categorical Page", Error: "Error", OK: "OK", Close: "Close", PageCountErrorMsg: "Enter valid page number" };
                break;
            case "ejPivotSchemaDesigner":
                t.PivotSchemaDesigner.Locale = t.PivotSchemaDesigner.Locale || {};
                t.PivotSchemaDesigner.Locale["el-GR"] = {
                    Sort: "Sort",
                    SelectField: "select Field",
                    LabelFilterLabel: "Show the items for which the label",
                    ValueFilterLabel: "Show the items for which",
                    ClearSorting: "Clear Sorting",
                    ClearFilterFrom: "Clear Filter From",
                    SortAtoZ: "Sort A to Z",
                    SortZtoA: "Sort Z to A",
                    and: "<u>a</u>nd",
                    LabelFilters: "Label Filters  ",
                    BeginsWith: "Begins With",
                    NotBeginsWith: "Not Begins With",
                    EndsWith: "Ends With",
                    NotEndsWith: "Not Ends With",
                    Contains: "Contains",
                    NotContains: "Not Contains",
                    ValueFilters: "Value Filters",
                    ClearFilter: "Clear Filter",
                    Equals: "Equals",
                    NotEquals: "Not Equals",
                    GreaterThan: "Greater Than ",
                    GreaterThanOrEqualTo: "Greater Than Or Equal To ",
                    LessThan: "Less Than ",
                    LessThanOrEqualTo: "Less Than Or Equal To ",
                    Between: "Between",
                    NotBetween: "Not Between",
                    DoesNotBeginsWith: "Does Not Begins With",
                    DoesNotEndsWith: "Does Not Ends With",
                    DoesNotContains: "Does Not Contains",
                    DoesNotEquals: "Does Not Equals",
                    IsGreaterThan: "Is Greater Than",
                    IsGreaterThanOrEqualTo: "Is Greater Than Or Equal To",
                    IsLessThan: "Is Less Than",
                    IsLessThanOrEqualTo: "Is Less Than Or Equal To",
                    ClearFilter: "Clear Filter",
                    SelectField: "select Field",
                    Measures: "Measures",
                    Warning: "Warning",
                    AlertMsg: "The field you are moving cannot be placed in that area of the report",
                    Goal: "Goal",
                    Status: "Status",
                    Trend: "Trend",
                    Value: "Value",
                    AddToFilter: "Add to Filter",
                    AddToRow: "Add to Row",
                    AddToColumn: "Add to Column",
                    AddToValues: "Add to Value",
                    SummarizeValueBy: "Summarize value by",
                    Sum: "Sum",
                    Average: "Average",
                    Count: "Count",
                    Min: "Min",
                    Max: "Max",
                    DoubleSum: "DoubleSum",
                    DoubleAverage: "DoubleAverage",
                    DoubleMin: "DoubleMin",
                    DoubleMax: "DoubleMax",
                    DoubleStandardDeviation: "DoubleStandardDeviation",
                    DoubleVariance: "DoubleVariance",
                    DecimalSum: "DecimalSum",
                    IntSum: "IntSum",
                    Custom: "Custom",
                    Discrete: "Discrete",
                    CountNumbers: "Count Numbers",
                    StdDev: "StdDev",
                    StdDevP: "StdDevP",
                    Variance: "Var",
                    VarP: "VarP",
                    SummaryOf: "of",
                    PivotTableFieldList: "PivotTable Field List",
                    ChooseFieldsToAddToReport: "Choose fields to add to the report:",
                    DragFieldBetweenAreasBelow: "Drag fields between areas below:",
                    ReportFilter: "Filter",
                    ColumnLabel: "Column",
                    RowLabel: "Row",
                    Values: "Values",
                    DeferLayoutUpdate: "Defer Layout Update",
                    Update: "Update",
                    OK: "OK",
                    Cancel: "Cancel",
                    Close: "Close",
                    Search: "Search",
                    Remove: "Remove",
                    AddCurrentSelectionToFilter: "Add current selection to filter",
                    NotAllItemsShowing: "Not all child nodes are shown",
                    EditorLinkPanelAlert: "The members have more than 1000 items under one or more parent. Only the first 1000 items are displayed under each parent.",
                    NamedSetAlert: "Named sets cannot be added to the PivotTable report at the same time. Click OK to remove ' <Set 1> ' named set and add ' <Set 2> ' named set.",
                };
                break;
            case "ejDiagram":
                t.datavisualization.Diagram.Locale = t.datavisualization.Diagram.Locale || {};
                t.datavisualization.Diagram.Locale["el-GR"] = {
                    cut: "Cut",
                    copy: "Copy",
                    paste: "Paste",
                    undo: "Undo",
                    redo: "Redo",
                    selectAll: "Select All",
                    grouping: "Grouping",
                    group: "Group",
                    ungroup: "Ungroup",
                    order: "Order",
                    bringToFront: "Bring To Front",
                    moveForward: "Move Forward",
                    sendBackward: "Send Backward",
                    sendToBack: "Send To Back",
                };
                break;
            case "ejChart":
                t.datavisualization.Chart.Locale = t.datavisualization.Chart.Locale || {};
                t.datavisualization.Chart.Locale["el-GR"] = { zoomIn: "Zoom In", zoomOut: "Zoom Out", zoom: "Zoom", pan: "Pan", reset: "Reset" };
                break;
            case "ejRangeNavigator":
                t.datavisualization.RangeNavigator.Locale = t.datavisualization.RangeNavigator.Locale || {};
                t.datavisualization.RangeNavigator.Locale["el-GR"] = { intervals: { quarter: { longQuarters: "Quarter", shortQuarters: "Q" }, week: { longWeeks: "Week", shortWeeks: "W" } } };
                break;
            case "ejMap":
                t.datavisualization.Map.Locale = t.datavisualization.Map.Locale || {};
                t.datavisualization.Map.Locale["el-GR"] = { zoomIn: "Zoom In", zoomOut: "Zoom Out", panTop: "Pan Top", panBottom: "Pan Bottom", panLeft: "Pan Left", panRight: "Pan Right", home: "Home" };
                break;
            case "ejRibbon":
                t.Ribbon.Locale = t.Ribbon.Locale || {};
                t.Ribbon.Locale["el-GR"] = {
                    CustomizeQuickAccess: "Customize Quick Access Toolbar",
                    RemoveFromQuickAccessToolbar: "Remove from Quick Access Toolbar",
                    AddToQuickAccessToolbar: "Add to Quick Access Toolbar",
                    ShowAboveTheRibbon: "Show Above the Ribbon",
                    ShowBelowTheRibbon: "Show Below the Ribbon",
                    MoreCommands: "More Commands...",
                };
                break;
            case "ejKanban":
                t.Kanban.Locale = t.Kanban.Locale || {};
                t.Kanban.Locale["el-GR"] = {
                    EmptyCard: "No cards to display",
                    SaveButton: "Save",
                    CancelButton: "Cancel",
                    EditFormTitle: "Details of ",
                    AddFormTitle: "Add New Card",
                    SwimlaneCaptionFormat: "- {{:count}}{{if count == 1 }} item {{else}} items {{/if}}",
                    FilterSettings: "Filters:",
                    FilterOfText: "of",
                    Max: "Max",
                    Min: "Min",
                    Cards: "  Cards",
                    ItemsCount: "Items Count :",
                    Unassigned: "Unassigned",
                    AddCard: "Add Card",
                    EditCard: "Edit Card",
                    DeleteCard: "Delete Card",
                    TopofRow: "Top of Row",
                    BottomofRow: "Bottom of Row",
                    MoveUp: "Move Up",
                    MoveDown: "Move Down",
                    MoveLeft: "Move Left",
                    MoveRight: "Move Right",
                    MovetoSwimlane: "Move to Swimlane",
                    HideColumn: "Hide Column",
                    VisibleColumns: "Visible Columns",
                    PrintCard: "Print Card",
                };
                break;
            case "ejRTE":
                t.RTE.Locale = t.RTE.Locale || {};
                t.RTE.Locale["el-GR"] = {
                    bold: "Bold",
                    italic: "Italic",
                    underline: "Underline",
                    strikethrough: "Strikethrough",
                    superscript: "Superscript",
                    subscript: "Subscript",
                    justifyCenter: "Align text center",
                    justifyLeft: "Align text left",
                    justifyRight: "Align text right",
                    justifyFull: "Justify",
                    unorderedList: "Insert unordered list",
                    orderedList: "Insert ordered list",
                    indent: "Increase Indent",
                    fileBrowser: "File Browser",
                    outdent: "Decrease Indent",
                    cut: "Cut",
                    copy: "Copy",
                    paste: "Paste",
                    paragraph: "Paragraph",
                    undo: "Undo",
                    redo: "Redo",
                    upperCase: "Upper Case",
                    lowerCase: "Lower Case",
                    clearAll: "Clear All",
                    clearFormat: "Clear Format",
                    createLink: "Insert/Edit Hyperlink",
                    removeLink: "Remove Hyperlink",
                    tableProperties: "Table Properties",
                    insertTable: "Insert",
                    deleteTables: "Delete",
                    imageProperties: "Image Properties",
                    openLink: "Open Hyperlink",
                    image: "Insert image",
                    video: "Insert video",
                    editTable: "Edit Table Properties",
                    embedVideo: "Paste your embed code below",
                    viewHtml: "View HTML",
                    fontName: "Select font family",
                    fontSize: "Select font size",
                    fontColor: "Select color",
                    format: "Format",
                    backgroundColor: "Background color",
                    style: "Styles",
                    deleteAlert: "Are you sure you want to clear all the contents?",
                    copyAlert: "Your browser doesn't support direct access to the clipboard. Please use the Ctrl+C keyboard shortcut instead of copy operation.",
                    pasteAlert: "Your browser doesn't support direct access to the clipboard. Please use the Ctrl+V keyboard shortcut instead of paste operation.",
                    cutAlert: "Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X keyboard shortcut instead of cut operation.",
                    videoError: "The text area can not be empty",
                    imageWebUrl: "Web Address",
                    imageAltText: "Alternate text",
                    dimensions: "Dimensions",
                    constrainProportions: "Constrain Proportions",
                    linkWebUrl: "Web Address",
                    imageLink: "Image as Link",
                    imageBorder: "Image Border",
                    imageStyle: "Style",
                    linkText: "Text",
                    linkTooltipLabel: "Tooltip",
                    html5Support: "This tool icon only enabled in HTML5 supported browsers",
                    linkOpenInNewWindow: "Open link in new window",
                    tableColumns: "No.of Columns",
                    tableRows: "No.of Rows",
                    tableWidth: "Width",
                    tableHeight: "Height",
                    tableCellSpacing: "Cell spacing",
                    tableCellPadding: "Cell padding",
                    tableBorder: "Border",
                    tableCaption: "Caption",
                    tableAlignment: "Alignment",
                    textAlign: "Text align",
                    dialogUpdate: "Update",
                    dialogInsert: "Insert",
                    dialogCancel: "Cancel",
                    dialogApply: "Apply",
                    dialogOk: "Ok",
                    createTable: "Insert Table",
                    insertTable: "Insert",
                    addColumnLeft: "Insert Columns to the Left",
                    addColumnRight: "Insert Columns to the Right",
                    addRowAbove: "Insert Rows Above",
                    addRowBelow: "Insert Rows Below",
                    deleteRow: "Delete entire row",
                    deleteColumn: "Delete entire column",
                    deleteTable: "Delete Table",
                    customTable: "Create custom table...",
                    characters: "Characters",
                    words: "Words",
                    general: "General",
                    advanced: "Advanced",
                    table: "Table",
                    row: "Row",
                    column: "Column",
                    cell: "Cell",
                    solid: "Solid",
                    dotted: "Dotted",
                    dashed: "Dashed",
                    doubled: "Double",
                    maximize: "Maximize",
                    resize: "Minimize",
                    swatches: "Swatches",
                    paragraph: "Paragraph",
                    quotation: "Quotation",
                    heading1: "Heading 1",
                    heading2: "Heading 2",
                    heading3: "Heading 3",
                    heading4: "Heading 4",
                    heading5: "Heading 5",
                    heading6: "Heading 6",
                    segoeui: "Segoe UI",
                    arial: "Arial",
                    couriernew: "Courier New",
                    georgia: "Georgia",
                    impact: "Impact",
                    lucidaconsole: "Lucida Console",
                    tahoma: "Tahoma",
                    timesnewroman: "Times New Roman",
                    trebuchetms: "Trebuchet MS",
                    verdana: "Verdana",
                    disc: "Disc",
                    circle: "Circle",
                    square: "Square",
                    number: "Number",
                    loweralpha: "Lower Alpha",
                    upperalpha: "Upper Alpha",
                    lowerroman: "Lower Roman",
                    upperroman: "Upper Roman",
                    none: "None",
                    linkTooltip: "ctrl + click to follow link",
                    charSpace: "Characters (with spaces)",
                    charNoSpace: "Characters (no spaces)",
                    wordCount: "Word Count",
                    left: "Left",
                    right: "Right",
                    center: "Center",
                    zoomIn: "Zoom In",
                    zoomOut: "Zoom Out",
                    print: "Print",
                    import: "Import a Document",
                    wordExport: "Export as Word Document",
                    pdfExport: "Export as Pdf File",
                    FindAndReplace: "Find and Replace",
                    Find: "Find",
                    MatchCase: "Match Case",
                    WholeWord: "Whole Word",
                    ReplaceWith: "Replace with",
                    Replace: "Replace",
                    ReplaceAll: "Replace All",
                    FindErrorMsg: "Couldn't find specified word.",
                    customFontColor: "More Colors...",
                    customBGColor: "More Colors...",
                    TransBGColor: "Transparent",
                    addtodictionary: "Add to Dictionary",
                    ignoreall: "IgnoreAll",
                };
                break;
            case "ejRecurrenceEditor":
                t.RecurrenceEditor.Locale = t.RecurrenceEditor.Locale || {};
                t.RecurrenceEditor.Locale["el-GR"] = {
                    Repeat: "Επανάληψη",
                    Never: "Ποτέ",
                    Daily: "Καθημερινά",
                    Weekly: "Εβδομαδιαία",
                    Monthly: "Μηνιαία",
                    Yearly: "Ετήσια",
                    First: "Πρώτη/ο",
                    Second: "Δεύτερη/ο",
                    Third: "Τρίτη/ο",
                    Fourth: "Τέταρτη/ο",
                    Last: "Τελευταία/ο",
                    EveryWeekDay: "Κάθε Σαββατοκύριακο",
                    Every: "Κάθε",
                    RecurrenceDay: "Ημέρα(ες)",
                    RecurrenceWeek: "Εβδομάδα(ες)",
                    RecurrenceMonth: "Μήνα(ες)",
                    RecurrenceYear: "Έτος(η)",
                    RepeatOn: "Επανάληψη την",
                    RepeatBy: "Επανάληψη κάθε",
                    StartsOn: "Έναρξη",
                    Times: "φορές",
                    Ends: "Λήξη",
                    Day: "Ημέρα",
                    The: "Την/Το",
                    OfEvery: "Από",
                    After: "Μετά",
                    On: "Στις",
                    Occurrence: "Επαναλήψεις",
                    Until: "Μέχρι",
                };
                break;
            case "ejSchedule":
                t.Schedule.Locale = t.Schedule.Locale || {};
                t.Schedule.Locale["el-GR"] = {
                    ReminderWindowTitle: "Reminder window",
                    CreateAppointmentTitle: "Create Appointment",
                    RecurrenceEditTitle: "Edit Repeat Appointment",
                    RecurrenceEditMessage: "How would you like to change the appointment in the series?",
                    RecurrenceEditOnly: "Only this appointment",
                    RecurrenceEditFollowingEvent: "Following Events",
                    RecurrenceEditSeries: "Entire series",
                    PreviousAppointment: "Previous Appointment",
                    NextAppointment: "Next Appointment",
                    AppointmentSubject: "Subject",
                    StartTime: "Start Time",
                    EndTime: "End Time",
                    AllDay: "All day",
                    StartTimeZone: "Start TimeZone",
                    EndTimeZone: "End TimeZone",
                    Today: "Today",
                    Recurrence: "Repeat",
                    Done: "Done",
                    Cancel: "Cancel",
                    Ok: "OK",
                    Repeat: "Repeat",
                    RepeatBy: "Repeat by",
                    RepeatEvery: "Repeat every",
                    RepeatOn: "Repeat on",
                    StartsOn: "Starts on",
                    Ends: "Ends",
                    Summary: "Summary",
                    Daily: "Daily",
                    Weekly: "Weekly",
                    Monthly: "Monthly",
                    Yearly: "Yearly",
                    Every: "Every",
                    EveryWeekDay: "Every weekday",
                    Never: "Never",
                    After: "After",
                    Occurrence: "Occurrence(s)",
                    On: "On",
                    Edit: "Edit",
                    RecurrenceDay: "Day(s)",
                    RecurrenceWeek: "Week(s)",
                    RecurrenceMonth: "Month(s)",
                    RecurrenceYear: "Year(s)",
                    The: "The",
                    OfEvery: "of every",
                    First: "First",
                    Second: "Second",
                    Third: "Third",
                    Fourth: "Fourth",
                    Last: "Last",
                    WeekDay: "Weekday",
                    WeekEndDay: "Weekendday",
                    Subject: "Subject",
                    Categorize: "Categories",
                    DueIn: "Due In",
                    DismissAll: "Dismiss All",
                    Dismiss: "Dismiss",
                    OpenItem: "Open Item",
                    Snooze: "Snooze",
                    Day: "Day",
                    Week: "Week",
                    WorkWeek: "Work Week",
                    Month: "Month",
                    AddEvent: "Add Event",
                    CustomView: "Custom View",
                    Agenda: "Agenda",
                    Detailed: "Edit Appointment",
                    EventBeginsin: "Appointment begins in",
                    Editevent: "Edit Appointment",
                    Editfollowingevent: "Following Events",
                    Editseries: "Edit Series",
                    Times: "times",
                    Until: "until",
                    Eventwas: "Appointment was",
                    Hours: "hrs",
                    Minutes: "mins",
                    Overdue: "Overdue Appointment",
                    Days: "day(s)",
                    Event: "Event",
                    Select: "select",
                    Previous: "Previous",
                    Next: "Next",
                    Close: "Close",
                    Delete: "Delete",
                    Date: "Date",
                    Showin: "Show in",
                    Gotodate: "Go To Date",
                    Resources: "RESOURCES",
                    RecurrenceDeleteTitle: "Delete Repeat Appointment",
                    Location: "Location",
                    Priority: "Priority",
                    RecurrenceAlert: "Alert",
                    NoTitle: "No Title",
                    OverFlowAppCount: "more appointment(s)",
                    AppointmentIndicator: "Click for more appointments",
                    WrongPattern: "The recurrence pattern is not valid",
                    CreateError: "The duration of the appointment must be shorter than how frequently it occurs. Shorten the duration, or change the recurrence pattern in the Appointment Recurrence dialog box.",
                    DragResizeError: "Cannot reschedule an occurrence of the recurring appointment if it skips over a later occurrence of the same appointment.",
                    StartEndError: "The selected end date occurs before the start date.",
                    MouseOverDeleteTitle: "Delete Appointment",
                    DeleteConfirmation: "Are you sure you want to delete this appointment?",
                    Time: "Time",
                    EmptyResultText: "No suggestions",
                    BlockIntervalAlertTitle: "Alert",
                    BlockIntervalError: "The selected time interval has been blocked and is unavailable for selection.",
                    RecurrenceDateValidation: "Some months have fewer than selected dates. For these months, the occurrence will fall on the last date of the month.",
                    SeriesChangeAlert: "The changes made to specific instances of this series will be cancelled and those appointments will match the series again.",
                };
                break;
            case "ejSpreadsheet":
                t.Spreadsheet.Locale = t.Spreadsheet.Locale || {};
                t.Spreadsheet.Locale["el-GR"] = {
                    Cut: "Cut",
                    Copy: "Copy",
                    FormatPainter: "Format Painter",
                    Paste: "Paste",
                    PasteValues: "Paste Values Only",
                    PasteSpecial: "Paste",
                    Filter: "Filter",
                    FilterContent: "Turn on filtering for the selected cells.",
                    FilterSelected: "Filter by Selected Cell's value",
                    Sort: "Sort",
                    Clear: "Clear",
                    ClearContent: "Delete everything in the cell, or remove just the formatting, contents, comments or hyperlinks.",
                    ClearFilter: "Clear Filter",
                    ClearFilterContent: "Clear the filter and sort state for the current range of data.",
                    SortAtoZ: "Sort A to Z",
                    SortAtoZContent: "Lowest to Highest.",
                    SortZtoA: "Sort Z to A",
                    SortZtoAContent: "Highest to Lowest.",
                    SortSmallesttoLargest: "Sort Smallest to Largest",
                    SortLargesttoSmallest: "Sort Largest to Smallest",
                    SortOldesttoNewest: "Sort Oldest to Newest",
                    SortNewesttoOldest: "Sort Newest to Oldest",
                    Insert: "Insert",
                    InsertTitle: "Insert Cells",
                    InsertContent: "Add new cells, rows, or columns to your workbook.",
                    MultipleInsertContent: "FYI: To insert multiple rows or columns at a time, select multiple rows or columns in the sheet, and click Insert.",
                    InsertSBContent: "Add cells, rows, columns, or sheets to your workbook.",
                    Delete: "Delete",
                    DeleteTitle: "Delete Cells",
                    DeleteContent: "Delete cells, rows, columns, or sheets from your workbook.",
                    MultipleDeleteContent: "FYI: To delete multiple rows or columns at a time, select multiple rows or columns in the sheet, and click Delete.",
                    FindSelectTitle: "Find & Select",
                    FindSelectContent: "Click to see options for finding text in your document.",
                    CalculationOptions: "Calculation Options",
                    CalcOptTitle: "Calculation Options",
                    CalcOptContent: "Choose to calculate formulas automatically or manually.",
                    CalcOptRecalcContent: "If you make a change that affects a value, Spreadsheet will automatically recalculate it.",
                    CalculateSheet: "Calculate Sheet",
                    CalculateNow: "Calculate Now",
                    CalculateNowContent: "Calculate the entire workbook now.",
                    CalculateNowTurnOffContent: "You only need to use this if automatic calculation is turned off.",
                    CalculateSheetContent: "Calculate the active sheet now.",
                    CalculateSheetTurnOffContent: "You only need to use this if automatic calculation is turned off.",
                    Title: "Spreadsheet",
                    ColorPicker: "Color Picker",
                    Ok: "OK",
                    Cancel: "Cancel",
                    Alert: "We couldn't do this for the selected range of cells. Select a single cell within a range of data and then try again.",
                    HeaderAlert: "The command could not be completed as you are attempting to filter with the filter header. Select a single cell in the filter range and try the command again.",
                    FlashFillAlert: "All the data next to your selection was checked and there was no pattern for filling in values.",
                    Formatcells: "Format Cells",
                    FontFamily: "Font",
                    FFContent: "Pick a new font for your text.",
                    FontSize: "Font Size",
                    FSContent: "Change the size of your text.",
                    IncreaseFontSize: "Increase Font Size",
                    IFSContent: "Make your text a bit bigger.",
                    DecreaseFontSize: "Decrease Font Size",
                    DFSContent: "Make your text a bit smaller.",
                    Bold: "Bold",
                    Italic: "Italic",
                    Underline: "Underline",
                    Linethrough: "Linethrough",
                    FillColor: "Fill Color",
                    FontColor: "Font Color",
                    TopAlign: "Top Align",
                    TopAlignContent: "Align text to the top.",
                    MiddleAlign: "Middle Align",
                    MiddleAlignContent: "Align text so that it is centered between the top and bottom of the cell.",
                    BottomAlign: "Bottom Align",
                    BottomAlignContent: "Align text to the bottom.",
                    WrapText: "Wrap Text",
                    WrapTextContent: "Wrap extra-long text into multiple lines so you can see all of it.",
                    AlignLeft: "Align Left",
                    AlignLeftContent: "Align your content to the left.",
                    AlignCenter: "Center",
                    AlignCenterContent: "Center your content.",
                    AlignRight: "Align Right",
                    AlignRightContent: "Align your content to the right.",
                    IncreaseIndent: "Increase Indent",
                    IncreaseIndentContent: "Move your content farther away from the cell border.",
                    DecreaseIndent: "Decrease Indent",
                    DecreaseIndentContent: "Move your content closer to the cell border.",
                    Undo: "Undo",
                    Redo: "Redo",
                    NumberFormat: "Number Format",
                    NumberFormatContent: "Choose the format for your cells, such as percentage, currency, date or time.",
                    AccountingStyle: "Accounting Style",
                    AccountingStyleContent: "Format as dollar accounting number format.",
                    PercentageStyle: "Percent Style",
                    PercentageStyleContent: "Format as a percent.",
                    CommaStyle: "Comma Style",
                    CommaStyleContent: "Format with a thousands separator.",
                    IncreaseDecimal: "Increase Decimal",
                    IncreaseDecimalContent: "Show more decimal places for a more precise value.",
                    DecreaseDecimal: "Decrease Decimal",
                    DecreaseDecimalContent: "Show fewer decimal places.",
                    AutoSum: "AutoSum",
                    AutoSumTitle: "Sum",
                    AutoSumContent: "Automatically add a quick calculation to your worksheet, such as sum or average.",
                    Fill: "Fill",
                    ExportXL: "Excel",
                    ExportCsv: "CSV",
                    ExportPdf: "PDF",
                    BackgroundColor: "Fill Color",
                    BGContent: "Color the background of the cells to make them stand out.",
                    ColorContent: "Change the color of your text.",
                    Border: "Border",
                    BorderContent: "Apply borders to the currently selected cells.",
                    BottomBorder: "Bottom Border",
                    TopBorder: "Top Border",
                    LeftBorder: "Left Border",
                    RightBorder: "Right Border",
                    OutsideBorder: "Outside Borders",
                    NoBorder: "No Border",
                    AllBorder: "All Borders",
                    ThickBoxBorder: "Thick Box Border",
                    ThickBottomBorder: "Thick Bottom Border",
                    TopandThickBottomBorder: "Top and Thick Bottom Border",
                    BottomDoubleBorder: "Bottom Double Border",
                    TopandBottomDoubleBorder: "Top and Bottom Double Border",
                    DrawBorderGrid: "Draw Border Grid",
                    DrawBorder: "Draw Border",
                    TopandBottomBorder: "Top and Bottom Border",
                    BorderColor: "Line Color",
                    BorderStyle: "Line Style",
                    Number: "Number is used for general display of numbers. Currency and Accounting offer specialized formatting for monetary value.",
                    General: "General format cells have no specific number format.",
                    Currency: "Currency formats are used for general monetary values. Use Accounting formats to align decimal points in a column.",
                    Accounting: "Accounting formats line up the currency symbols and decimal points in a column.",
                    Text: "Text format cells are treated as text even when a number is in the cell.  The cell is displayed exactly as entered.",
                    Percentage: "Percentage formats multiply the cell value by 100 and displays the result with a percent symbol.",
                    CustomMessage: "Type number format code, using one of existing codes as a starting point.",
                    Fraction: "Fraction is used to indicate a part of a whole number or a ratio between two numbers.",
                    Scientific: "Scientific is used to represent a decimal number between 1 and 10 multiplied by ten, so the large numbers using less digits.",
                    Type: "Type:",
                    CustomFormatAlert: "Enter a valid format",
                    Date: "Date formats display date and time serial numbers as date values.",
                    Time: "Time formats display date and time serial numbers as date values.",
                    File: "FILE",
                    New: "New",
                    Open: "Open",
                    SaveAs: "Save As",
                    Print: "Print",
                    PrintContent: "Print the current sheet.",
                    PrintSheet: "Print Sheet",
                    PrintSelected: "Print Selected",
                    PrintSelectedContent: "Select an area on the sheet you would like to print.",
                    HighlightVal: "Format Invalid Data",
                    ClearVal: "Clear Validation",
                    Validation: "Validation",
                    DataValidation: "Data Validation",
                    DVContent: "Pick from a list of rules to limit the type of data that can be entered in a cell.",
                    PageSize: "Page Size",
                    PageSizeContent: "Choose a page size for your document.",
                    FormatCells: "FormatCells",
                    ConditionalFormat: "Conditional Formatting",
                    CFContent: "Easily spot trends and patterns in your data using colors to visually highlight important values.",
                    And: "and",
                    With: "with",
                    GTTitle: "Greater Than",
                    GTContent: "Format cells that are GREATER THAN:",
                    LTTitle: "Less Than",
                    LTContent: "Format cells that are LESS THAN:",
                    BWTitle: "Between",
                    BWContent: "Format cells that are BETWEEN:",
                    EQTitle: "Equal To",
                    EQContent: "Format cells that are EQUAL TO:",
                    DateTitle: "A Date Occurring",
                    DateContent: "Format cells that contain a DATE:",
                    ContainsTitle: "Text That Contains",
                    ContainsContent: "Format cells that contain the text:",
                    GreaterThan: "Greater Than",
                    LessThan: "Less Than",
                    Between: "Between",
                    EqualTo: "Equal To",
                    TextthatContains: "Text that Contains",
                    DateOccurring: "A Date Occurring",
                    ClearRules: "Clear Rules",
                    ClearRulesfromSelected: "Clear Rules from Selected Cells",
                    ClearRulesfromEntireSheets: "Clear Rules from Entire Sheet",
                    CellStyles: "Cell Styles",
                    CellStylesContent: "A colorful style is a great way to make important data stand out on the sheet.",
                    CellStyleHeaderText: "Good, Bad and Neutral/Titles and Headings/Themed Cell Styles",
                    Custom: "Type the number format code, using one of the existing codes as starting point.",
                    CellStyleGBN: "Normal/Bad/Good/Neutral",
                    CellStyleTH: "Heading 4/Title",
                    Accent: "Accent",
                    Style: "Style",
                    FormatAsTable: "Format As Table",
                    FormatasTable: "Format as Table",
                    FATContent: "Quickly convert a range of cells to a table with its own style.",
                    FATHeaderText: "Light/Medium/Dark",
                    FATNameDlgText: "Table Name :/My table has headers",
                    InvalidReference: "The range you have specified is invalid",
                    ResizeAlert: "The specified range is invalid. The top of the table must remain in the same row, and the resulting table must overlap the original table. Specify a valid range.",
                    RangeNotCreated: "Increasing the row beyond the maximum sheet row count is restricted in Format as Table.",
                    ResizeRestrictAlert: "Increase or decrease of column count and decrease of row count is restricted in Format as Table.",
                    FATResizeTableText: "Enter new data range for your table:",
                    FATReizeTableNote: "Note: The headers must remain in the same row and the resulting table range must overlap the original table range.",
                    FormatAsTableAlert: "Cannot create a table with a single row. A table must have at least two rows, one for the table header, and one for data",
                    FormatAsTableTitle:
                        "Light 1/Light 2/Light 3/Light 4/Light 5/Light 6/Light 7/Light 8/Light 9/Light 10/Light 11/Light 12/Medium 1/Medium 2/Medium 3/Medium 4/Medium 5/Medium 6/Medium 7/Medium 8/Dark 1/Dark 2/Dark 3/Dark 4",
                    NewTableStyle: "New Table Style",
                    ResizeTable: "Resize Table",
                    ResizeTableContent: "Resize this table by adding or removing rows and columns.",
                    ConvertToRange: "Convert to Range",
                    ConvertToRangeContent: "Convert this table into a normal range of cells.",
                    ConverToRangeAlert: "Do you want to convert the table to a normal range?",
                    TableID: "Table ID:",
                    Table: "Table",
                    TableContent: "Create a table to organize and analyze related data.",
                    TableStyleOptions: "First Column/Last Column/Total Row/Filter Button",
                    Format: "Format",
                    NameManager: "Name Manager",
                    NameManagerContent: "Create, edit, delete and find all the names used in the workbook.",
                    NameManagerFormulaContent: "Names can be used in formulas as substitutes for cell references.",
                    DefinedNames: "Defined Names",
                    DefineName: "Define Name",
                    DefineNameContent: "Define and apply names.",
                    UseInFormula: "Use In Formula",
                    UseInFormulaContent: "Choose a name used in this workbook and insert it into the current formula.",
                    RefersTo: "Refers To",
                    Name: "Name",
                    Scope: "Scope",
                    NMNameAlert:
                        "The name that you entered is not valid./Reason for this can include:/The name does not begin with a letter or an underscore/The name contains a space or other invalid characters/The name conflicts with a spreadsheet built-in name or the name of another object in the workbook",
                    NMUniqueNameAlert: "The name entered already exists. Enter a unique name.",
                    NMRangeAlert: "Enter a valid range",
                    FORMULAS: "FORMULAS",
                    Value: "Values",
                    DataValue: "Values:",
                    Formula: "Formulas",
                    MissingParenthesisAlert: "Your formula is missing a parenthesis--) or (. Check the formula, and then add the parenthesis in the appropriate place.",
                    UnsupportedFile: "Unsupported File",
                    IncorrectPassword: "Unable to open the file or worksheet with the given password",
                    InvalidUrl: "Please specify proper URL",
                    Up: "Up",
                    Down: "Down",
                    Sheet: "Sheet",
                    Workbook: "Workbook",
                    Rows: "By Rows",
                    Columns: "By Columns",
                    FindReplace: "Find Replace",
                    FindnReplace: "Find and Replace",
                    Find: "Find",
                    Replace: "Replace",
                    FindLabel: "Find what:",
                    ReplaceLabel: "Replace with:",
                    ReplaceAll: "Replace All",
                    Close: "Close",
                    FindNext: "Find Next",
                    FindPrev: "Find Prev",
                    Automatic: "Automatic",
                    Manual: "Manual",
                    Settings: "Settings",
                    MatchCase: "Match case",
                    MatchAll: "Match entire cell contents",
                    Within: "Within:",
                    Search: "Search:",
                    Lookin: "Look in:",
                    ShiftRight: "Shift cells right",
                    ShiftBottom: "Shift cells down",
                    EntireRow: "Entire row",
                    EntireColumn: "Entire column",
                    ShiftUp: "Shift cells up",
                    ShiftLeft: "Shift cells left",
                    Direction: "Direction:",
                    GoTo: "Go To",
                    GoToName: "Go to:",
                    Reference: "Reference:",
                    Special: "Special",
                    Select: "Select",
                    Comments: "Comments",
                    Formulas: "Formulas",
                    Constants: "Constants",
                    RowDiff: "Row differences",
                    ColDiff: "Column differences",
                    LastCell: "Last cell",
                    CFormat: "Conditional formats",
                    Blanks: "Blanks",
                    GotoError: "Error",
                    GotoLogicals: "Logicals",
                    GotoNumbers: "Numbers",
                    GotoText: "Text",
                    FindSelect: "Find & Select",
                    Comment: "Comment",
                    NewComment: "New",
                    InsertComment: "Insert Comment",
                    EditComment: "Edit",
                    DeleteComment: "Delete Comment",
                    DeleteCommentContent: "Delete the selected comment.",
                    HideComment: "Hide Comment",
                    Next: "Next",
                    NextContent: "Jump to the next comment.",
                    Previous: "Previous",
                    PreviousContent: "Jump to the previous comment.",
                    ShowHide: "Show/Hide Comment",
                    ShowHideContent: "Show or hide the comment on the active cell.",
                    ShowAll: "Show All Comments",
                    ShowAllContent: "Display all comments in the sheet.",
                    UserName: "User Name",
                    Hide: "Hide",
                    Unhide: "Unhide",
                    Add: "Add",
                    DropAlert: "Do you want to replace the existing data?",
                    PutCellColor: "Put Selected Cell Color To The Top",
                    PutFontColor: "Put Selected Font Color To The Top",
                    WebPage: "Web Page",
                    WorkSheet: "Worksheet Reference",
                    SheetReference: "Sheet Reference",
                    InsertHyperLink: "Insert Hyperlink",
                    HyperLink: "Hyperlink",
                    EditLink: "Editlink",
                    OpenLink: "Openlink",
                    HyperlinkText: "Text:",
                    RemoveLink: "Removelink",
                    WebAddress: "Web Address:",
                    CellAddress: "Cell Reference:",
                    SheetIndex: "Select a place in this document",
                    ClearAll: "Clear All",
                    ClearFormats: "Clear Formats",
                    ClearContents: "Clear Contents",
                    ClearComments: "Clear Comments",
                    ClearHyperLinks: "Clear Hyperlinks",
                    SortFilter: "Sort & Filter",
                    SortFilterContent: "Organize your data so it's easier to analyze.",
                    NumberStart: "Minimum:",
                    NumberEnd: "Maximum:",
                    DecimalStart: "Minimum:",
                    DecimalEnd: "Maximum:",
                    DateStart: "Start Date:",
                    DateEnd: "End Date:",
                    ListStart: "Source:",
                    FreeText: "Show error alert after invalid data is entered",
                    ListEnd: "Cell Reference:",
                    TimeStart: "Start Time:",
                    TimeEnd: "End Time:",
                    TextLengthStart: "Minimum:",
                    TextLengthEnd: "Maximum:",
                    CommentFindEndAlert: "Spreadsheet reached the end of the workbook. Do you want to continue reviewing from the beginning of the workbook?",
                    InsertSheet: "Insert",
                    DeleteSheet: "Delete",
                    RenameSheet: "Rename",
                    MoveorCopy: "Move or Copy",
                    HideSheet: "Hide",
                    UnhideSheet: "Unhide",
                    SheetRenameAlert: "That name is already taken. Try a different one.",
                    SheetRenameEmptyAlert: "You typed an invalid name for a sheet. Make sure that:",
                    SheetRenameEmptyCharExceedAlert: "The name that you type does not exceed 31 characters.",
                    SheetRenameEmptySplCharAlert: "The name does not contain any of the following characters:",
                    SheetRenameEmptyBlankAlert: "You did not leave the name blank.",
                    SheetDeleteAlert: "You can't undo deleting sheets, and you might be removing some data. If you don't need it, click OK to delete.",
                    SheetDeleteErrorAlert: "A workbook must contain at least one visible worksheet. To hide, delete, or move the selected sheet, you must first insert a new sheet or unhide a sheet that is already hidden.",
                    CtrlKeyErrorAlert: "That command cannot be used on  multiple selections.",
                    ClipboardAccessError: "Your browser can't access the clipboard, so use these shortcuts:",
                    MoveToEnd: "Move To End",
                    Beforesheet: "Before sheet:",
                    CreateaCopy: "Create a copy",
                    AutoFillOptions: "Copy Cells/Fill Series/Fill Formatting Only/Fill Without Formatting/Flash Fill",
                    NumberValidationMsg: "Enter only digits",
                    DateValidationMsg: "Enter only date",
                    Required: "Required",
                    TimeValidationMsg: "The value you entered for the Time is invalid.",
                    CellAddrsValidationMsg: "Reference is not valid.",
                    PivotTable: "Pivot Table",
                    PivotTableContent: "Easily arrange and summarize complex data in a PivotTable.",
                    NumberTab: "Number",
                    AlignmentTab: "Alignment",
                    FontTab: "Font",
                    FillTab: "Fill",
                    TextAlignment: "Text alignment",
                    Horizontal: "Horizontal:",
                    Vertical: "Vertical:",
                    Indent: "Indent",
                    TextControl: "Text Control",
                    FontGroup: "Font:",
                    FontStyle: "Font style:",
                    Size: "Size:",
                    PSize: "Page size",
                    Effects: "Effects:",
                    StrikeThrough: "Strikethrough",
                    Overline: "Overline",
                    NormalFont: "Normal font",
                    Preview: "Preview",
                    Line: "Line",
                    Presets: "Presets",
                    None: "None",
                    Outline: "Outline",
                    AllSide: "All sides",
                    InsCells: "Insert Cells",
                    InsRows: "Insert Sheet Rows",
                    InsCols: "Insert Sheet Columns",
                    InsSheet: "Insert Sheet",
                    DelCells: "Delete Cells",
                    DelRows: "Delete Sheet Rows",
                    DelCols: "Delete Sheet Columns",
                    DelSheet: "Delete Sheet",
                    HyperLinkAlert: "The address of this site is not valid.Check the address and try again.",
                    ReplaceData: "All done. We made / replacements.",
                    NotFound: "We couldn't find what you were looking for. Select settings tab for more ways to search",
                    Data: "Data:",
                    Allow: "Allow:",
                    IgnoreBlank: "Ignore blank",
                    NotFind: "Unable to find the match to replace",
                    FreezeTopRow: "Freeze Top Row",
                    FreezeFirstColumn: "Freeze First Column",
                    UnFreezePanes: "Unfreeze Panes",
                    DestroyAlert: "Are you sure you want to destroy the current workbook without saving and create a new workbook?",
                    ImageValAlert: "Upload image files only",
                    Pictures: "Pictures",
                    PicturesTitle: "From File",
                    PicturesContent: "Insert pictures from computer or from other computers that you are connected to.",
                    ImportAlert: "Are you sure you want to destroy the current workbook without saving and open a new workbook?",
                    UnmergeCells: "Unmerge Cells",
                    MergeCells: "Merge Cells",
                    MergeAcross: "Merge Across",
                    MergeAndCenter: "Merge & Center",
                    MergeAndCenterContent: "Combine and center the contents of the selected cells in a new larger cell.",
                    MergeCellsAlert: "Merging Cells keeps only upper left cell value and discards the other values.",
                    MergeInsertAlert: "This operation will causes some merged cells to unmerge. Do you wish to continue?",
                    Axes: "Axes",
                    PHAxis: "Primary Horizontal",
                    PVAxis: "Primary Vertical",
                    AxisTitle: "Axis Title",
                    CTNone: "None",
                    CTCenter: "Center",
                    CTFar: "Far",
                    CTNear: "Near",
                    DataLabels: "Data Labels",
                    DLNone: "None",
                    DLCenter: "Center",
                    DLIEnd: "Inside End",
                    DLIBase: "Inside Base",
                    DLOEnd: "Outside End",
                    ErrorBar: "Error Bars",
                    Gridline: "Gridlines",
                    PMajorH: "Primary Major Horizontal",
                    PMajorV: "Primary Major Vertical",
                    PMinorH: "Primary Minor Horizontal",
                    PMinorV: "Primary Minor Vertical",
                    Legend: "Legends",
                    LNone: "None",
                    LLeft: "Left",
                    LRight: "Right",
                    LBottom: "Bottom",
                    LTop: "Top",
                    ChartTitleDlgText: "Enter Title",
                    ChartTitle: "Title",
                    InvalidTitle: "You typed an invalid name for the Title.",
                    CorrectFormat: "Select the correct Format File",
                    ResetPicture: "Reset Picture",
                    ResetPictureContent: "Discard all of the formatting changes made to this picture.",
                    PictureBorder: "Picture Border",
                    PictureBorderContent: "Pick the color, width, and line style for the outline of your shape.",
                    ResetSize: "Reset Picture & Size",
                    Height: "Height",
                    Width: "Width",
                    ThemeColor: "Theme Colors",
                    NoOutline: "No Outline",
                    Weight: "Weight",
                    Dashes: "Dashes",
                    ColumnChart: "Column",
                    ColumnChartTitle: "Insert Column Chart",
                    ColumnChartContent: "Use this chart type to visually compare values across a few categories.",
                    BarChart: "Bar",
                    BarChartTitle: "Insert Bar Chart",
                    BarChartContent: "Use this chart type to visually compare values across a few categories when the chart shows duration or the category text is long.",
                    StockChart: "Radar",
                    StockChartTitle: "Insert Radar Chart",
                    StockChartContent: "Use this chart type to show values relative to a center point.",
                    LineChart: "Line",
                    LineChartTitle: "Insert Line Chart",
                    LineChartContent: "Use this chart type to show trends over time (years, months, and days) or categories.",
                    AreaChart: "Area",
                    AreaChartTitle: "Insert Area Chart",
                    AreaChartContent: "Use this chart type to show trends over time (years, months, and days) or categories. Use it to highlight the magnitude of change over time.",
                    ComboChart: "Combo",
                    PieChart: "Pie",
                    PieChartTitle: "Insert Pie/Doughnut Chart",
                    PieChartContent: "Use this chart type to show proportions of a whole. Use it when the total of your numbers is 100%.",
                    ScatterChart: "Scatter",
                    ScatterChartTitle: "Insert Scatter (X, Y) Chart",
                    ScatterChartContent: "Use this chart type to show the relationship between sets of values.",
                    ClusteredColumn: "Clustered&nbsp;Column",
                    StackedColumn: "Stacked&nbsp;Column",
                    ClusteredBar: "Clustered&nbsp;Bar",
                    StackedBar: "Stacked&nbsp;Bar",
                    Radar: "Radar",
                    RadarMarkers: "Radar&nbsp;with&nbsp;Markers",
                    LineMarkers: "Line&nbsp;with&nbsp;Markers",
                    Area: "Area",
                    StackedArea: "Stacked&nbsp;Area",
                    Pie: "Pie",
                    Doughnut: "Doughnut",
                    Scatter: "Scatter",
                    ChartRange: "Chart Range",
                    XAxisRange: "Enter X-axis range:",
                    YAxisRange: "Enter Y-axis range:",
                    LegendRange: "Enter legend range:",
                    YAxisMissing: "Enter Y-axis range to create chart",
                    InvalidYAxis: "Y-axis range must be within the selected range",
                    InvalidXAxis: "X-axis range must be within the selected range",
                    InvalidLegend: "Legend range must be within the selected range",
                    InvalidXAxisColumns: "X-axis range should be within a single column",
                    FreezePanes: "Freeze Panes",
                    FreezePanesContent: "Freeze a portion of the sheet to keep it visible while you scroll through the rest of the sheet.",
                    PasteContent: "Add content on the Clipboard to your document.",
                    PasteSplitContent: "Pick a paste option, such as keeping formatting or pasting only content.",
                    CutContent: "Remove the selection and put it on the Clipboard so you can paste it somewhere else.",
                    CopyContent: "Put a copy of the selection on the Clipboard so you can paste it somewhere else.",
                    FPTitle: "Format Painter",
                    FPContent: "Like the look of a particular selection? You can apply that look to other content in the document.",
                    BoldContent: "Make your text bold.",
                    ItalicContent: "Italicize your text.",
                    ULineContent: "Underline your text.",
                    LineTrContent: "Cross something out by drawing a strike through it.",
                    UndoContent: "Undo your last action.",
                    RedoContent: "Redo your last action.",
                    HyperLinkTitle: "Add a Hyperlink",
                    HyperLinkContent: "Create a link in your document for quick access to webpages and files.",
                    HyperLinkPlaceContent: "Hyperlinks can also take you to places in your document.",
                    NewCommentTitle: "Insert a Comment",
                    NewCommentContent: "Add a note about this part of the document.",
                    RefreshTitle: "Refresh",
                    RefreshContent: "Get the latest data from the source connected to the active cell",
                    FieldListTitle: "Field List",
                    FieldListContent: "Show or hide the Field List.",
                    FieldListRemoveContent: "The field list allows you to add and remove fields from your PivotTable report",
                    AddChartElement: "Add Chart Element",
                    AddChartElementContent: "Add elements to the created chart.",
                    SwitchRowColumn: "Switch Row/ Column",
                    SwitchRowColumnContent: "Swap the data over the axis.",
                    MergeAlert: "We can't do that to a merged cell.",
                    UnhideDlgText: "Unhide Sheet:",
                    ChartThemes: "Chart Themes",
                    ChartThemesContent: "Pick a new theme for your chart.",
                    ChangePicture: "Change Picture",
                    ChangePictureContent: "Change to a different picture, preserving the formatting and size of the current picture.",
                    ChangeChartType: "Change Chart Type",
                    SelectData: "Select Data",
                    SelectDataContent: "Change the data range included in the chart.",
                    Sum: "Sum",
                    Average: "Average",
                    CountNumber: "Count Numbers",
                    Max: "Max",
                    Min: "Min",
                    ChartType: "Change Chart Type",
                    ChartTypeContent: "Change to a different chart type.",
                    AllCharts: "All Charts",
                    defaultfont: "Default",
                    LGeneral: "General",
                    LCurrency: "Currency",
                    LAccounting: "Accounting",
                    LDate: "Date",
                    LTime: "Time",
                    LPercentage: "Percentage",
                    LFraction: "Fraction",
                    LScientific: "Scientific",
                    LText: "Text",
                    LCustom: "Custom",
                    FormatSample: "Sample",
                    Category: "Category:",
                    Top: "Top",
                    Center: "Center",
                    Bottom: "Bottom",
                    Left: "Left (Indent)",
                    Right: "Right",
                    Justify: "Justify",
                    GeneralTxt: "General format cells have no specific number format.",
                    NegativeNumbersTxt: "Negative Numbers",
                    ThousandSeparatorTxt: "Use 1000 Separator",
                    DecimalPlacesTxt: "Decimal Places:",
                    TextTxt: "Text format cells are treated as text even when a number is in the cell. The cell is displayed exactly as entered.",
                    BoldItalic: "Bold Italic",
                    Regular: "Regular",
                    HyperLinkHide: "<<Selection in Document>>",
                    InvalidSheetIndex: "Specify proper SheetIndex",
                    HugeDataAlert: "File too large to open.",
                    ImportExportUrl: "Give import/export URL and try again.",
                    BetweenAlert: "The Maximum must be greater or equal to the Minimum.",
                    BorderStyles: "Solid/Dashed/Dotted/Double",
                    FPaneAlert: "Freeze Pane is not applied for the First Cell",
                    ReplaceNotFound: "Spreadsheet cannot find a match.",
                    BlankWorkbook: "Blank workbook",
                    SaveAsExcel: "Save As Excel",
                    SaveAsCsv: "Save As CSV",
                    SaveAsPdf: "Save As PDF",
                    Design: "DESIGN",
                    NewName: "New Name",
                    FormulaBar: "Formula Bar",
                    NameBox: "Name Box",
                    NumberValMsg: "Decimal values cannot be used for number conditions.",
                    NumberAlertMsg: "Enter only digits.",
                    ListAlert: "Cell range is incorrect, Please enter correct cell range.",
                    ListValAlert: "The list source must be a delimited list, or a reference to single row or column.",
                    ListAlertMsg: "The value you entered is not valid",
                    AutoFillTitle: "AutoFillOptions",
                    NewSheet: "New Sheet",
                    FullSheetCopyPasteAlert: "We can't paste because the Copy area and paste area aren't the same size.",
                    Heading: "Headings",
                    Gridlines: "Gridlines",
                    Firstsheet: "Scroll to the first sheet",
                    Lastsheet: "Scroll to the last sheet",
                    Nextsheet: "Scroll to the next sheet",
                    Prevsheet: "Scroll to the previous sheet",
                    ProtectWorkbook: "Protect Workbook",
                    UnProtectWorkbook: "Unprotect Workbook",
                    ProtectWBContent: "Keep others from making structural changes to your workbook",
                    Password: "Password",
                    ConfirmPassword: "Reenter password to proceed:",
                    PasswordAlert1: "Confirmation password is not identical.",
                    PasswordAlert2: "Please enter a password.",
                    PasswordAlert3: "The password you supplied is not correct. Verify that the CAPS LOCK key is off and be sure to use the correct capitalization.",
                    Protect: "is protected.",
                    Lock: "LockCell",
                    Unlock: "UnlockCell",
                    Protectsheet: "Protect Sheet",
                    ProtectSheetToolTip: "Prevent unwanted changes from others by limiting their ability to edit",
                    Unprotect: "Unprotect Sheet",
                    LockAlert: "The cell you are trying to change is on protected sheet. To make changes, click Unprotect Sheet in the Review tab.",
                    InsertDeleteAlert: "This operation is not allowed. The operation is attempting to shift cells in a table on your worksheet.",
                    CreateRule: "New Rule",
                    NewRule: "New Formatting Rule",
                    NewRuleLabelContent: "Format values where this formula is true:",
                    ReadOnly: "The range you are trying to change contains readonly cells.",
                    CreatePivotTable: "Create PivotTable",
                    Range: "Range:",
                    ChoosePivotTable: "Choose where you want the PivotTable to be placed",
                    NewWorksheet: "New Worksheet",
                    ExistingWorksheet: "Existing Worksheet",
                    Location: "Location:",
                    Refresh: "Refresh",
                    PivotRowsAlert: "This command requires at least two rows of source data. You cannot use the command on a selection in only one row.",
                    PivotLabelsAlert:
                        "The PivotTable field name is not valid, To create a PivotTable report, you must use data that is organized as a list with labeled columns. If you are changing the name of a PivotTable field, you must type a new name for the field.",
                    FieldList: "Field List",
                    MergeSortAlert: "To do this, all the merged cells need to be the same size.",
                    FormulaSortAlert: "The sort range with formula cannot be sorted.",
                    MergePreventInsertDelete: "This operation is not allowed. The operation is attempting to shift a merge cells on your worksheet.",
                    FormulaRuleMsg: "Please enter correct format.",
                    MovePivotTable: "Move PivotTable",
                    MovePivotTableContent: "Move the PivotTable to another location in the workbook.",
                    ClearAllContent: "Remove fields and filters.",
                    ChangeDataSource: "Modify",
                    ChangeDataSourceContent: "Change the source data for this PivotTable",
                    ChangePivotTableDataSource: "Change PivotTable Data Source",
                    TotalRowAlert: "This operation is not allowed. The operation is attempting to shift cells in a table on your worksheet. Click OK to proceed with entire row.",
                    CellTypeAlert: "This operation is not allowed in cell type applied range.",
                    PivotOverlapAlert: "A PivotTable report cannot overlap another PivotTable report",
                    NoCellFound: "No cells were found",
                    CorrectArgument: "Please enter the correct argument",
                    CorrectFormula: "Please enter the correct formula",
                    CorrectCellAddress: "Please enter the correct cell address",
                    ChartType: "Chart Type",
                    NumberValidationAlert: "The Maximum must be greater than or equal to Minimum",
                    DateValidationAlert: "The End Date must be greater than or equal to Start Date",
                    TimeValidationAlert: "The End Time must be greater than or equal to Start Time",
                    NewRuleAlert: "There's a problem with this formula",
                    DragAlert: "The command could not be completed as you are attempting to move cells within the filter range. Select a range out of the filter range and try the command again.",
                    OR: "or",
                    HOME: "HOME",
                    INSERT: "INSERT",
                    DATATAB: "DATA",
                    PAGELAYOUT: "PAGE LAYOUT",
                    REVIEW: "REVIEW",
                    OTHERS: "OTHERS",
                    FORMAT: "Format",
                    DESIGN: "DESIGN",
                    CHARTDESIGN: "Chart Design",
                    ANALYZE: "Analyze",
                    FileName: "File Name",
                    Save: "Save",
                    SaveFile: "Save the file",
                    HighlightCellRules: "Highlight Cell Rules",
                    LightRedFillDark: "Light Red Fill with Dark Red Text",
                    YellowFillDark: "Yellow Fill with Dark Yellow Text",
                    GreenFillDark: "Green Fill with Dark Green Text",
                    RedFill: "Red Fill",
                    RedText: "Red Text",
                    Column: "Column",
                    Bar: "Bar",
                    Line: "Line",
                    DataValidationType: "Number/Decimal/Date/Time/TextLength/List",
                    DataValidationAction: "greater than/greater than or equal to/less than/less than or equal to/equal to/not equal to/between/not between",
                    Modify: "Modify",
                    Apply: "Apply",
                    NewCellStyle: "New Cell Style",
                    cellStyleAlert: "This style name already exists",
                    modifyCellStyleAlert: "This style name does not exists",
                    Color: "Color",
                    StyleName: "Style Name",
                    LShortdate: "Short Date",
                    LLongdate: "Long Date",
                    Clipboard: "Clipboard",
                    Font: "Font",
                    Actions: "Actions",
                    Styles: "Styles",
                    Editing: "Editing",
                    Tables: "Tables",
                    Illustrations: "Illustrations",
                    Links: "Links",
                    Charts: "Charts",
                    DataTools: "Data Tools",
                    Show: "Show",
                    PageLayout: "Page Layout",
                    Changes: "Changes",
                    Window: "Window",
                    Cells: "Cells",
                    Calculation: "Calculation",
                    Properties: "Properties",
                    Tools: "Tools",
                    TableStyleOption: "Table Style Options",
                    ChartLayouts: "Chart Layouts",
                    ChartDesignData: "Data",
                    ChartDesignType: "Type",
                    ChartDesignSize: "Size",
                    Adjust: "Adjust",
                    FormatSize: "Size",
                    AnalyzePivotTable: "PivotTable",
                    DataSource: "DataSource",
                    FATTitlePrefix: "Table Style",
                    HighPoint: "High Point",
                    LowPoint: "Low Point",
                    FirstPoint: "First Point",
                    LastPoint: "Last Point",
                    NegativePoint: "Negative Points",
                    Markers: "Markers",
                    NegativePoints: "Negative Points",
                    LineSparklineTitle: "Insert Line Sparkline",
                    LineSparklineContent: "Sparklines are mini charts placed in a single cells,each representing a row of data in your selection",
                    ColumnSparklineTitle: "Insert Column Sparkline",
                    ColumnSparklineContent: "Sparklines are mini charts placed in a single cells,each representing a row of data in your selection",
                    WinLossSparklineTitle: "Insert Win/Loss Sparkline",
                    WinLossSparklineContent: "Sparklines are mini charts placed in a single cells,each representing a row of data in your selection",
                    Line: "Line",
                    SparklineColor: "Sparkline Color",
                    SparklineColorTitle: "Sparkline Color",
                    SparklineColorContent: "Specify the color of the sparklines in the selected sparkline group",
                    MarkerColor: "Marker Color",
                    MarkerColorContent: "Change the color for negative points, markers, and all other points for selected sparkline group",
                    ChooseDataRange: "Choose the Data Range",
                    ChooseLocationRange: "Choose the Location Range",
                    DataRange: "Data Range",
                    LocationRange: "Location Range",
                    EmptyDLRnge: "Data Source or Location reference is not valid",
                    SourceDataRange: "Select a source data range for the sparkline",
                    SparklineAlert: "Location reference is not valid because the cells are not all in same column or same row. Select cells that are all in single row or single column.",
                    SparklineDataAlert: "Enter the Correct Data Format",
                    SparklineLocAlert: "The reference for the location or data range is not valid",
                    SDataRangeAlert: "Data Source reference is not valid",
                    LineAlert: "Cannot create the line chart for single value",
                    EditData: "Edit Data",
                    EditDataContent: "Edit Group Location & Data",
                    EditSingleSparklineData: "Edit Single Sparkline Data",
                    EditSparklineData: "Edit Sparkline Data",
                    HiddenEmptyCells: "Hidden & Empty Cells",
                    SwitchRowCol: "Switch Row/Column",
                    CreateSparkline: "Create Sparkline",
                    SelectDataSource: "Select a source data range for the sparkline",
                    SPARKLINEDESIGN: "Sparkline Design",
                    CancellationRequested: "Couldn't open within the timeout specified",
                    ImportPreventedOnUnknownFormula: "This file cannot be loaded since it contains unsupported formulas.",
                    PivotTableName: "PivotTable Name",
                    ArrayaFormula: "You can't change part of an array",
                    ArrayaFormulaTableAlert: "Multi-cell array formulas aren't allowed in tables",
                    ValueFieldSettings: "Value Field Settings",
                    FieldTab: "Summarize Values By",
                    SummarizeValue: "Summarize Value Field By",
                    SummarizeChooseType: "Choose the type of calculation that you want to use to summarize data from the selected field",
                    FieldValue: "Source Name:",
                };
                break;
            case "ejTimePicker":
                t.TimePicker.Locale = t.TimePicker.Locale || {};
                t.TimePicker.Locale["el-GR"] = { watermarkText: "Select time" };
                break;
            case "ejTreeGrid":
                t.TreeGrid.Locale = t.TreeGrid.Locale || {};
                t.TreeGrid.Locale["el-GR"] = {
                    toolboxTooltipTexts: {
                        addTool: "Add",
                        editTool: "Edit",
                        updateTool: "Update",
                        deleteTool: "Delete",
                        cancelTool: "Cancel",
                        expandAllTool: "Expand All",
                        collapseAllTool: "Collapse All",
                        pdfExportTool: "PDF Export",
                        excelExportTool: "Excel Export",
                        printTool: "Print",
                        searchTool: "Search",
                    },
                    contextMenuTexts: { addRowText: "Add Row", editText: "Edit", deleteText: "Delete", saveText: "Save", cancelText: "Cancel", aboveText: "Above", belowText: "Below" },
                    columnMenuTexts: {
                        sortAscendingText: "Sort Ascending",
                        sortDescendingText: "Sort Descending",
                        columnsText: "Columns",
                        freezeText: "Freeze",
                        unfreezeText: "Unfreeze",
                        freezePrecedingColumnsText: "Freeze Preceding Columns",
                        insertColumnLeft: "Insert Column Left",
                        insertColumnRight: "Insert Column Right",
                        deleteColumn: "Delete Column",
                        renameColumn: "Rename Column",
                        menuFilter: "Filter",
                    },
                    columnDialogTexts: {
                        field: "Field",
                        headerText: "Header Text",
                        editType: "Edit Type",
                        filterEditType: "Filter Edit Type",
                        allowFiltering: "Allow Filtering",
                        allowFilteringBlankContent: "Allow Filtering Blank Content",
                        allowSorting: "Allow Sorting",
                        visible: "Visible",
                        width: "Width",
                        textAlign: "Text Alignment",
                        headerTextAlign: "Header Text Alignment",
                        isFrozen: "Is Frozen",
                        allowFreezing: "Allow Freezing",
                        columnsDropdownData: "Column Dropdown Data",
                        dropdownTableText: "Text",
                        dropdownTableValue: "Value",
                        addData: "Add",
                        deleteData: "Remove",
                        allowCellSelection: "Allow Cell Selection",
                        showInColumnChooser: "Show In Column Chooser",
                        displayAsCheckbox: "Display As Checkbox",
                        clipMode: "Clip Mode",
                        tooltip: "Tooltip",
                        headerTooltip: "Header Tooltip",
                    },
                    editTypeTexts: { string: "String", numeric: "Numeric", datePicker: "Date Picker", dateTimePicker: "Date Time Picker", dropdown: "Dropdown", boolean: "Boolean" },
                    textAlignTypes: { right: "Right", left: "Left", center: "Center" },
                    clipModeTexts: { clip: "Clip", ellipsis: "Ellipsis" },
                    columnDialogTitle: { insertColumn: "Insert Column", deleteColumn: "Delete Column", renameColumn: "Rename Column" },
                    filterMenuTexts: {
                        stringMenuOptions: [
                            { text: "Starts With", value: "startswith" },
                            { text: "Ends With", value: "endswith" },
                            { text: "Contains", value: "contains" },
                            { text: "Equals", value: "equal" },
                            { text: "Does Not Equal", value: "notequal" },
                        ],
                        numberMenuOptions: [
                            { text: "Less Than", value: "lessthan" },
                            { text: "Greater Than", value: "greaterthan" },
                            { text: "Less Than Or Equal To", value: "lessthanorequal" },
                            { text: "Greater Than Or Equal To", value: "greaterthanorequal" },
                            { text: "Equals", value: "equal" },
                            { text: "Does Not Equal", value: "notequal" },
                        ],
                        filterValue: "Filter Value",
                        filterButton: "Filter",
                        clearButton: "Clear",
                        enterValueText: "enter value",
                    },
                    deleteColumnText: "Are you sure you want to delete this column?",
                    deleteRecordText: "Are you sure you want to delete record?",
                    okButtonText: "OK",
                    cancelButtonText: "Cancel",
                    confirmDeleteText: "Confirm Delete",
                    batchSaveConfirmText: "Are you sure you want to save changes?",
                    batchSaveLostChangesText: "Unsaved changes will be lost. Are you sure you want to continue?",
                    cancelEditText: "Are you sure you want to cancel the changes?",
                    dropDownListBlanksText: "(Blanks)",
                    dropDownListClearText: "(Clear Filter)",
                    trueText: "True",
                    falseText: "False",
                    emptyRecord: "No records to display",
                };
                break;
            case "ejUploadbox":
                t.Uploadbox.Locale = t.Uploadbox.Locale || {};
                t.Uploadbox.Locale["el-GR"] = {
                    buttonText: { upload: "Upload", browse: "Browse", cancel: "Cancel", close: "Close" },
                    dialogText: { title: "Upload Box", name: "Name", size: "Size", status: "Status" },
                    dropAreaText: "Drop files or click to upload",
                    filedetail: "The selected file size is too large. Please select a file within the valid size.",
                    denyError: "Files with #Extension extensions are not allowed.",
                    allowError: "Only files with #Extension extensions are allowed.",
                    cancelToolTip: "Cancel",
                    removeToolTip: "Remove",
                    retryToolTip: "Retry",
                    completedToolTip: "Completed",
                    failedToolTip: "Failed",
                    closeToolTip: "Close",
                };
                break;
            case "ejSpellCheck":
                t.SpellCheck.Locale = t.SpellCheck.Locale || {};
                t.SpellCheck.Locale["el-GR"] = {
                    SpellCheckButtonText: "Spelling:",
                    NotInDictionary: "Not in Dictionary:",
                    SuggestionLabel: "Suggestions:",
                    IgnoreOnceButtonText: "Ignore Once",
                    IgnoreAllButtonText: "Ignore All",
                    AddToDictionary: "Add to Dictionary",
                    ChangeButtonText: "Change",
                    ChangeAllButtonText: "ChangeAll",
                    CloseButtonText: "Close",
                    CompletionPopupMessage: "Spell check is complete",
                    CompletionPopupTitle: "Spell check",
                    Ok: "OK",
                    NoSuggestionMessage: "No suggestions available",
                    NotValidElement: "Specify the valid control id or class name to spell check",
                };
                break;
            case "ejMediaPlayer":
                t.MediaPlayer.Locale = t.MediaPlayer.Locale || {};
                t.MediaPlayer.Locale["el-GR"] = {
                    Play: "Play",
                    Pause: "Pause",
                    Mute: "Mute",
                    Unmute: "Unmute",
                    Settings: "Settings",
                    FullScreen: "Full screen",
                    ExitFullScreen: "Exit full screen",
                    HidePlaylist: "Hide playlist",
                    Previous: "Previous",
                    Next: "Next",
                    TogglePlaylist: "Toggle playlist",
                    Rewind: "Rewind",
                    Forward: "Forward",
                    Playlist: "Playlist",
                    RepeatPlaylist: "Repeat playlist",
                    Shuffle: "Shuffle",
                    VideoTitle: "Video",
                    PlaylistTitle: "Playlist",
                    PlaylistItemName: "List item",
                    PlaylistItemAuthor: "Author",
                    Media: "Media",
                    Speed: "Speed",
                    Quality: "Quality",
                    Normal: "Normal",
                    Auto: "Auto",
                };
                break;
            case "ejTile":
                t.Tile.Locale = t.Tile.Locale || {};
                t.Tile.Locale["el-GR"] = { captionText: "text" };
                break;
            case "ejListView":
                t.ListView.Locale = t.ListView.Locale || {};
                t.ListView.Locale["el-GR"] = { headerTitle: "Title", headerBackButtonText: "Back" };
        }
    };
    r = t.widget.registeredWidgets;
    for (i in r) n.fn.Locale_elGR(i);
    t.ExcelFilter && n.fn.Locale_elGR("ejExcelFilter");
})(window.jQuery, window.Syncfusion);
