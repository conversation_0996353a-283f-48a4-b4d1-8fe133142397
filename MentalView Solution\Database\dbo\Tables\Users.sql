﻿CREATE TABLE [dbo].[Users] (
    [UserId]                               BIGINT         IDENTITY (1, 1) NOT NULL,
    [RoleId]                               BIGINT         NOT NULL,
    [TenantId]                             BIGINT         NOT NULL,
    [Username]                             NVARCHAR (20)  CONSTRAINT [DF_Users_Username] DEFAULT ('') NOT NULL,
    [Password]                             NVARCHAR (20)  NOT NULL,
    [FullName]                             NVARCHAR (30)  CONSTRAINT [DF_Users_FullName] DEFAULT ('') NOT NULL,
    [Email]                                NVARCHAR (50)  CONSTRAINT [DF_Users_Email] DEFAULT ('') NOT NULL,
    [IsDoctor]                             BIT            CONSTRAINT [DF_Users_IsTechnician] DEFAULT ((0)) NOT NULL,
    [JobTitle]                             NVARCHAR (50)  CONSTRAINT [DF_Users_Email1] DEFAULT ('') NOT NULL,
    [AcademicTitle]                        NVARCHAR (50)  CONSTRAINT [DF_Users_JobTitle1] DEFAULT ('') NOT NULL,
    [Specialisation]                       NVARCHAR (50)  CONSTRAINT [DF_Users_JobTitle1_1] DEFAULT ('') NOT NULL,
    [AppointmentsListSort]                 NVARCHAR (20)  CONSTRAINT [DF_Users_TasksListSort] DEFAULT ('') NOT NULL,
    [AppointmentsScheduleView]             NVARCHAR (20)  CONSTRAINT [DF_Users_TasksScheduleView] DEFAULT (N'Week') NOT NULL,
    [AppointmentsScheduleVisibleResources] NVARCHAR (MAX) CONSTRAINT [DF_Users_AppointmentsScheduleVisibleResources] DEFAULT ('') NOT NULL,
    CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED ([UserId] ASC),
    CONSTRAINT [FK_Users_Roles] FOREIGN KEY ([RoleId]) REFERENCES [dbo].[Roles] ([RoleId]) ON DELETE CASCADE ON UPDATE CASCADE
);







