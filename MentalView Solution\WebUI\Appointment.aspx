﻿<%@ Page Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Appointment.aspx.cs" Inherits="WebUI.Appointment" meta:resourcekey="Page" %>

<%@ Register Assembly="Syncfusion.EJ.Web" Namespace="Syncfusion.JavaScript.Web" TagPrefix="ej" %>
<%@ Register Assembly="Syncfusion.EJ" Namespace="Syncfusion.JavaScript.Models" TagPrefix="ej" %>

<%@ Register TagPrefix="cc1" Namespace="System" Assembly="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" %>
<asp:Content ID="mainHeadContent" ContentPlaceHolderID="mainHead" runat="server">
    <script type="text/javascript">
        var initialized = false;

        $(document).ready(function () {
            try {
                $('select').select2({ width: '100%' });
                $('select.no-search-select').select2({ width: '100%', minimumResultsForSearch: -1 });

                if (document.getElementById("recurrenceEditorWrapper") != null) {
                    var recObj = $("#recurrenceEditor").ejRecurrenceEditor('instance');
                    recObj._recRule = $("#recurrenceValueHiddenField").val();
                    recObj.recurrenceRuleSplit(recObj._recRule, ''); //splitting the recurrence rule. Κανανονικά στη 2η παράμετρο πρέπει να μπει το πεδίο appointment.recurrenceExDate που είναι οι ημερομηνίες που εξαιρούνται από το recurrence.
                    //Αν το AppointmentId είναι >0 (δηλαδή το Appointment δεν είναι καινούριο) αλλιώς βγάζει σφάλμα.
                    if ($("#appointmentIdHiddenField").val() > 0) {
                        if ($("#recurrenceValueHiddenField").val().length > 0) {
                            recObj.showRecurrenceSummary($("#appointmentIdHiddenField").val()); // updating the recurrence rule in Recurrence editor
                        }
                    }

                    initialized = true;

                    updateRecurrenceEditorVisibility();
                    setBankDisability();
                    updateSupervisor();
                }
            }
            catch (exp) {
                alert('exception:' + exp);
                throw exp;
            }
        });

        function Initialization() {
            SetLocalization();

            //Σετάρει τα Currency TextBoxes
            $(document).on('keydown', 'input[pattern]', function (e) {
                var input = $(this);
                var oldVal = input.val();
                var regex = new RegExp(input.attr('pattern'), 'g');

                setTimeout(function () {
                    var newVal = input.val();
                    if (!regex.test(newVal)) {
                        input.val(oldVal);
                    }
                }, 0);
            });

            $("input[pattern]").change(function () {
                this.value = parseFloat(this.value).toFixed(2);
                if (this.value == 'NaN') {
                    this.value = 0;
                }
            });

            $("#supervisionRequestChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#recurrenceChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#therapyResistantChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            //$("#cancelUpcomingAppointmentsChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#priceTxtBox").TouchSpin({ min: -0, max: 100, decimals: 2, stepinterval: 50, maxboostedstep: 1e7, postfix: "€", prefix_extraclass: "" });
            $("#deductionsTxtBox").TouchSpin({ min: -0, max: 100, decimals: 2, stepinterval: 50, maxboostedstep: 1e7, postfix: "€", prefix_extraclass: "" });
            //$("#balanceTxtBox").TouchSpin({ min: -0, max: 100, decimals: 2, stepinterval: 50, maxboostedstep: 1e7, postfix: "€", prefix_extraclass: "" });

            onPriceChange();
            updateSupervisor();
        }

        function SearchContacts() {
            try {
                __doPostBack('contactIdCmbBox', $("#contactIdCmbBox").val());
            }
            catch (e) { return true; }
        }

        function NotFound() {
            var $not_found = "Δεν βρέθηκαν αποτελέσματα";
            return $not_found;
        }

        function onRecurrenceEditorChange(args) {
            if (initialized) {
                var obj = $("#recurrenceEditor").data("ejRecurrenceEditor");
                $("#recurrenceValueHiddenField").val(obj.getRecurrenceRule());
                console.log('onRecurrenceEditorChange' + obj.getRecurrenceRule());
            }
        }

        function recurrenceCheckClicked() {
            updateRecurrenceEditorVisibility();
        }

        function updateRecurrenceEditorVisibility() {
            var checked = $('#recurrenceChkBox').is(':checked');
            if (checked == true) {
                $("#recurrenceEditorWrapper").css("display", "");
            }
            else {
                $("#recurrenceEditorWrapper").css("display", "none");
            }
        }

        //Εκτελείται όταν ο χρήστης πατήσει Save πριν το server-side event.
        function onClientSaveClose() {
            if (initialized) {
                //Διαβάζει το recurrenceRule που έφτιαξε ο χρήστης, για να αποθηκευτεί στο server side.
                var obj = $("#recurrenceEditor").data("ejRecurrenceEditor");
                obj.closeRecurPublic();
                $("#recurrenceValueHiddenField").val(obj.getRecurrenceRule());
                console.log('clientSaveClose' + obj.getRecurrenceRule());
            }
        }

        //Εκτελείται όταν ο χρήστης αλλάξει PaymentType
        function onPaymentTypeChange() {
            if (initialized) {
                setBankDisability();
            }
        }

        function setBankDisability() {
            if ($("#paymentTypeDDL").val() == "Deposit") {
                $("#bankTxtBox").prop('disabled', false);
            }
            else {
                $("#bankTxtBox").prop('disabled', true);
            }
        }

        function onPriceChange() {
            var price = Number($("#priceTxtBox").val());
            var deductions = Number($("#deductionsTxtBox").val());

            if (price < deductions) {
                $("#priceTxtBox").val(deductions);
                price = deductions;
            }

            $("#balanceTxtBox").val((price - deductions).toFixed(2));
        }

        function onDeductionsChange() {
            var price = Number($("#priceTxtBox").val());
            var deductions = Number($("#deductionsTxtBox").val());

            if (deductions > price) {
                $("#deductionsTxtBox").val(price);
                deductions = price;
            }
            $("#balanceTxtBox").val((price - deductions).toFixed(2));
        }


        function supervisionRequestCheckChanged() {
            updateSupervisor();
        }

        function updateSupervisor() {
            var supervisorDdl = $("#supervisorIdCmbBox").data("ejDropDownList");
            if (!supervisorDdl) return;

            var checked = $('#supervisionRequestChkBox').is(':checked');
            if (checked == true) {
                supervisorDdl.enable();
            }
            else {
                supervisorDdl.disable();
            }
        }
    </script>

    <script type="text/javascript">
        function openCancelAppointmentDialog() {
            $("#cancelAppointmentDialog").ejDialog("open");
        }

        function closeCancelAppointmentDialog() {
            $("#cancelAppointmentDialog").ejDialog("close");
        }

        function SetNoChargeableCancellation() {
            $("#chargeableCancellation").val("false");
        }

        function SetChargeableCancellation() {
            $("#chargeableCancellation").val("true");
        }
    </script>
</asp:Content>
<asp:Content ID="mainBodyContent" ContentPlaceHolderID="mainBody" runat="server">
    <div class="row margin-bottom">
        <div class="col-xs-8">
            <div class="btn-group margin-r-5">
                <asp:LinkButton ID="saveBtn" runat="server" CssClass="btn btn-primary btn-flat" OnClientClick="onClientSaveClose()" OnClick="saveBtn_Click"><span class="hidden-md hidden-lg"><i class="fa fa-save "></i></span><asp:Label Text="<%$ Resources:GlobalResources, Save %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
                <button type="button" class="btn btn-primary btn-flat dropdown-toggle" data-toggle="dropdown">
                    <span class="caret"></span>
                    <span class="sr-only">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu" role="menu">
                    <li>
                        <asp:LinkButton ID="saveCloseBtn" runat="server" Text="<%$ Resources:GlobalResources, SaveClose %>" OnClientClick="onClientSaveClose()" OnClick="saveCloseBtn_Click"></asp:LinkButton>
                    </li>
                </ul>
            </div>
            <asp:LinkButton ID="deleteBtn" runat="server" DisableValidation="True" CssClass="btn btn-danger btn-flat margin-bottom margin-r-5" OnClick="deleteBtn_Click" CausesValidation="False"><span class="hidden-md hidden-lg"><i class="fa fa-trash"></i></span><asp:Label Text="<%$ Resources:GlobalResources, Delete %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
            <button id="cancelBtn" type="button" runat="server" class="btn btn-default btn-flat margin-bottom margin-r-5" onclick="openCancelAppointmentDialog(); return false;">
                <span class="hidden-md hidden-lg"><i class="fa fa-trash-alt"></i></span>
                <asp:Label Text="<%$ Resources:GlobalResources, Cancel %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></button>
            <asp:LinkButton ID="closeBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat margin-bottom margin-r-5" OnClick="closeBtn_Click" CausesValidation="False" PostBackUrl="~/Appointments.aspx"><span class="hidden-md hidden-lg"><i class="fa fa-remove"></i></span><asp:Label Text="<%$ Resources:GlobalResources, Close %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
        </div>
        <div class="col-cs-4">
            <div class="btn-group margin-bottom margin-r-5 pull-right">
                <button type="button" id="appointmentReportBtn" runat="server" class="btn btn-default btn-flat dropdown-toggle" data-toggle="dropdown" aria-expanded="true">
                    <asp:Label runat="server" meta:resourcekey="appointmentReportLbl"></asp:Label>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" role="menu">
                    <li>
                        <asp:LinkButton ID="previewAppointmentReportBtn" runat="server" Text="<%$ Resources:GlobalResources, Preview %>" OnClick="previewAppointmentReportBtn_Click"></asp:LinkButton>
                    </li>
                    <li>
                        <asp:LinkButton ID="exportAppointmentReportToPdfBtn" runat="server" Text="<%$ Resources:GlobalResources, ExportToPdf %>" OnClick="exportAppointmentReportToPdfBtn_Click"></asp:LinkButton>
                    </li>
                    <li>
                        <asp:LinkButton ID="exportAppointmentReportToPWordBtn" runat="server" Text="<%$ Resources:GlobalResources, ExportToWord %>" OnClick="exportAppointmentReportToWordBtn_Click"></asp:LinkButton>
                    </li>
                </ul>
            </div>
            <div class="btn-group margin-bottom margin-r-5 pull-right">
                <%--<button type="button" id="serviceEmailBtn" runat="server" class="btn btn-default btn-flat dropdown-toggle" data-toggle="dropdown" aria-expanded="true">
                    <asp:Label runat="server" meta:resourcekey="serviceEmailLbl"></asp:Label>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" role="menu">
                    <li>
                        <asp:LinkButton ID="sendServiceEmailToUser" runat="server" meta:resourceKey="sendServiceEmailToUser" OnClick="sendServiceEmailToUser_Click"></asp:LinkButton>
                    </li>
                    <li>
                        <asp:LinkButton ID="sendServiceEmailToContact" runat="server" meta:resourceKey="sendServiceEmailToContact" OnClick="sendServiceEmailToContact_Click"></asp:LinkButton>
                    </li>
                      <li>
                        <asp:LinkButton ID="sendServiceEmailToAdmin" runat="server" meta:resourceKey="sendServiceEmailToAdmin" OnClick="sendServiceEmailToAdmin_Click"></asp:LinkButton>
                    </li>
                </ul>--%>
            </div>
        </div>
    </div>
    <div runat="server" class="row" id="canceledAppointmentFieldsDiv">
        <div class="col-md-12">
            <div class="callout callout-warning">
                <h4>Η Συνεδρία έχει ακυρωθεί!</h4>
                <p>
                    <asp:Label ID="chargeableCanceledInfoLbl" runat="server" Text="Label"></asp:Label><asp:LinkButton ID="toggleChargableCanceledAppointmentLnk" runat="server" DisableValidation="True" CssClass="btn btn-link" OnClick="toggleChargableCanceledAppointmentLnk_Click" CausesValidation="False"></asp:LinkButton><asp:LinkButton ID="setAppointmentNoCanceledLnk" runat="server" DisableValidation="True" CssClass="btn btn-link margin-r-5" OnClick="setAppointmentNoCanceledLnk_Click" meta:resourcekey="setAppointmentNoCanceledLnk" CausesValidation="False"></asp:LinkButton>
                </p>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="contactIdCmbBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="contactIdLbl"></asp:Label>
                            <div class="col-sm-10">
                                <div class="input-group">
                                    <ej:DropDownList ID="contactIdCmbBox" DataValueField="ContactId" DataTextField="FullName" runat="server" FilterType="Contains" EnableFilterSearch="true" WatermarkText="" Width="100%">
                                    </ej:DropDownList>
                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk margin-r-5"></i></span>
                                    <asp:LinkButton ID="showContactBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat margin-r-5" OnClick="showContactBtn_Click" OnClientClick="document.forms[0].target = '_blank';" CausesValidation="False"><span class="hidden-md hidden-lg"><i class="fa fa-user"></i></span><asp:Label meta:resourceKey="showContactLbl" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="startDateTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="startDateLbl"></asp:Label>
                            <div class="col-sm-4">
                                <div class="input-group">
                                    <ej:DatePicker ID="startDateTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:DatePicker>
                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="startTimeTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="startTimeLbl"></asp:Label>
                            <div class="col-sm-8">
                                <div class="input-group">
                                    <ej:TimePicker ID="startTimeTxtBox" Width="100%" Locale="el-GR" runat="server"></ej:TimePicker>
                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="endTimeTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="endTimeLbl"></asp:Label>
                            <div class="col-sm-8">
                                <div class="input-group">
                                    <ej:TimePicker ID="endTimeTxtBox" Width="100%" Locale="el-GR" runat="server">
                                        <ValidationRules>
                                            <ej:KeyValue Key="required" Value="true" />
                                        </ValidationRules>
                                        <ValidationMessages>
                                            <ej:KeyValue Key="required" Value="* απαιτείται" />
                                        </ValidationMessages>
                                    </ej:TimePicker>
                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="therapistIdCmbBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="therapistIdLbl"></asp:Label>
                            <div class="col-sm-8">
                                <div class="input-group">
                                    <%--<select runat="server" style="width: 100%;" datavaluefield="UserId" datatextfield="FullName" id="therapistIdCmbBox" class="form-control"></select>--%>
                                    <ej:DropDownList ID="therapistIdCmbBox" DataTextField="FullName" DataValueField="UserId" runat="server" WatermarkText="" Width="100%">
                                    </ej:DropDownList>
                                    <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="col-sm-6">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="roomDDL" runat="server" class="col-sm-4 control-label" meta:resourcekey="roomLbl"></asp:Label>
                            <div class="col-sm-8">
                                <ej:DropDownList ID="roomDDL" runat="server" DataTextField="RoomName" DataValueField="RoomId" DataHtmlAttributesField="HtmlAttr" WatermarkText="" Width="100%">
                                </ej:DropDownList>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="requestTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="requestLbl"></asp:Label>
                            <div class="col-sm-10">
                                <div class="input-group">
                                    <asp:TextBox runat="server" ID="requestTxtBox" class="form-control" TextMode="MultiLine" Rows="3" MaxLength="1000" Style="resize: none;" onblur="speechTextBoxBlur('requestTxtBox')"></asp:TextBox>
                                    <div class="input-group-addon borderless" style="padding-left: 30px">
                                        <i id="requestMicIcon" class="fas fa-microphone mic-icon" onclick="toggleSpeech('requestTxtBox')"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="supervisorInstructionsBeforeTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="supervisorInstructionsBeforeLbl"></asp:Label>
                            <div class="col-sm-10">
                                <asp:TextBox runat="server" ID="supervisorInstructionsBeforeTxtBox" class="form-control" TextMode="MultiLine" Rows="3" MaxLength="1000" Style="resize: none;"></asp:TextBox>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="intervetionModelDDL" runat="server" class="col-sm-4 control-label" meta:resourcekey="intervetionModelLbl"></asp:Label>
                            <div class="col-sm-8">
                                <%-- <ej:DropDownList ID="intervetionModelDDL" runat="server" WatermarkText="" Width="100%" FilterType="Contains" MultiSelectMode="VisualMode" EnableFilterSearch="true">
                                </ej:DropDownList>--%>
                                <select runat="server" id="intervetionModelDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="intervetionTechniquesDDL" runat="server" class="col-sm-4 control-label" meta:resourcekey="intervetionTechniquesLbl"></asp:Label>
                            <div class="col-sm-8">
                                <select runat="server" id="intervetionTechniquesDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="therapistCommentsTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="therapistCommentsLbl"></asp:Label>
                            <div class="col-sm-10">
                                <div class="input-group">
                                    <asp:TextBox runat="server" ID="therapistCommentsTxtBox" class="form-control" TextMode="MultiLine" Rows="3" MaxLength="1000" Style="resize: none;" onblur="speechTextBoxBlur('therapistCommentsTxtBox')"></asp:TextBox>
                                    <p>
                                        <asp:Label runat="server" class="control-label" meta:resourcekey="therapistCommentsInfoLbl"></asp:Label>
                                    </p>
                                    <div class="input-group-addon borderless" style="padding-left: 30px">
                                        <i id="therapistCommentsMicIcon" class="fas fa-microphone mic-icon" onclick="toggleSpeech('therapistCommentsTxtBox')"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label runat="server" for="supervisionRequestChkBox" CssClass="col-sm-4 control-label" meta:resourcekey="supervisionRequestLbl"></asp:Label>
                            <div class="col-sm-3 switch">
                                <input runat="server" type="checkbox" id="supervisionRequestChkBox" onchange="supervisionRequestCheckChanged();" />
                            </div>
                        </div>
                    </div>

                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="supervisorIdCmbBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="supervisorIdLbl"></asp:Label>
                            <div class="col-sm-8">
                                <div class="input-group" style="width: 100%;">
                                    <ej:DropDownList ID="supervisorIdCmbBox" DataTextField="FullName" DataValueField="UserId" runat="server" WatermarkText="" Width="100%">
                                    </ej:DropDownList>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="supervisorCommentsAfterTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="supervisorCommentsAfterLbl"></asp:Label>
                            <div class="col-sm-8">
                                <asp:TextBox runat="server" ID="supervisorCommentsAfterTxtBox" class="form-control" TextMode="MultiLine" Rows="3" MaxLength="1000" Style="resize: none;"></asp:TextBox>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-4">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="priceTxtBox" runat="server" class="col-sm-6 control-label" meta:resourcekey="priceLbl"></asp:Label>
                            <div class="col-sm-6" style="width: 250px;">
                                <input runat="server" type="text" class="form-control" id="priceTxtBox" onkeyup="onPriceChange();" onchange="onPriceChange();" value='<%# Bind("Price") %>' required />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="deductionsTxtBox" runat="server" class="col-sm-6 control-label" meta:resourcekey="deductionsLbl"></asp:Label>
                            <div class="col-sm-6" style="width: 250px;">
                                <input runat="server" type="text" class="form-control" id="deductionsTxtBox" onkeyup="onDeductionsChange();" onchange="onDeductionsChange();" value='<%# Bind("Deductions") %>' required />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="balanceTxtBox" runat="server" class="col-sm-6 control-label" meta:resourcekey="balanceLbl"></asp:Label>
                            <div class="col-sm-6" style="width: 250px;">
                                <div class="input-group">
                                    <input runat="server" type="text" class="form-control" id="balanceTxtBox" value='<%# Bind("Balance") %>' disabled />
                                    <span class="input-group-addon">€</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-4">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="paymentTypeDDL" runat="server" class="col-sm-6 control-label" meta:resourcekey="paymentTypeLbl"></asp:Label>
                            <div class="col-sm-6">
                                <ej:DropDownList ID="paymentTypeDDL" runat="server" WatermarkText="" Width="100%" FilterType="Contains" EnableFilterSearch="false" ClientSideOnChange="onPaymentTypeChange">
                                </ej:DropDownList>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="bankTxtBox" runat="server" class="col-sm-6 control-label" meta:resourcekey="bankLbl"></asp:Label>
                            <div class="col-sm-6">
                                <input runat="server" type="text" class="form-control" id="bankTxtBox" value='<%# Bind("Bank") %>' />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="importantNotesTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="importantNotesLbl"></asp:Label>
                            <div class="col-sm-10">
                                <div class="input-group">
                                    <asp:TextBox runat="server" ID="importantNotesTxtBox" class="form-control" TextMode="MultiLine" Rows="3" MaxLength="1000" Style="resize: none;" onblur="speechTextBoxBlur('importantNotesTxtBox')"></asp:TextBox>
                                    <div class="input-group-addon borderless" style="padding-left: 30px">
                                        <i id="importantNotesMicIcon" class="fas fa-microphone mic-icon" onclick="toggleSpeech('importantNotesTxtBox')"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="notesTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="notesLbl"></asp:Label>
                            <div class="col-sm-10">
                                <div class="input-group">
                                    <asp:TextBox runat="server" ID="notesTxtBox" class="form-control" TextMode="MultiLine" Rows="3" MaxLength="1000" Style="resize: none;" onblur="speechTextBoxBlur('notesTxtBox')"></asp:TextBox>
                                    <div class="input-group-addon borderless" style="padding-left: 30px">
                                        <i id="notesMicIcon" class="fas fa-microphone mic-icon" onclick="toggleSpeech('notesTxtBox')"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <%-- <div runat="server" id="recurrenceInfoPanel" class="row">
                <div class="col-xs-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="recurrenceInfoLbl" runat="server" class="col-sm-2 control-label"></asp:Label>
                            <div class="col-sm-10">
                                <div class="callout callout-info">
                                    <p></p>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>--%>
            <div runat="server" id="recurrenceEditorPanel" class="row">
                <div class="col-xs-12">
                    <div class="row">
                        <div class="col-xs-2">
                        </div>
                        <div class="col-sm-10">
                            <div class="box box-default">
                                <div class="box-header">
                                    <h3 class="box-title">Επανάληψη</h3>
                                </div>
                                <div class="box-body">
                                    <input type="checkbox" id="recurrenceChkBox" onchange="recurrenceCheckClicked()" class="inl" runat="server" />
                                    <p>
                                        <asp:Label runat="server" ID="recurrenceInfoLbl" class="text-light-blue"></asp:Label>
                                    </p>

                                    <div runat="server" id="recurrenceEditorWrapper" style="display: none;">
                                        <br />
                                        <br />
                                        <div class="callout callout-warning">
                                            <%--<h4>I am a warning callout!</h4>--%>
                                            <i class="icon fa fa-warning margin-r-5" style="float: left;"></i>
                                            <p>
                                                <asp:Label runat="server" class="control-label" meta:resourcekey="recurrentAppointmentWarningMessageLbl"></asp:Label>
                                            </p>
                                        </div>
                                        <ej:RecurrenceEditor ID="recurrenceEditor" Locale="el-GR" runat="server" EnableViewState="true" Frequencies="daily,weekly,monthly,everyweekday" Change="onRecurrenceEditorChange">
                                        </ej:RecurrenceEditor>
                                    </div>
                                    <asp:HiddenField ID="recurrenceValueHiddenField" runat="server" />
                                    <asp:HiddenField ID="appointmentIdHiddenField" runat="server" />
                                    <asp:HiddenField ID="originalStartTimeHiddenField" runat="server" />
                                    <asp:HiddenField ID="originalEndTimeHiddenField" runat="server" />
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <asp:HiddenField ID="initialSupervisionRequest" runat="server" />

    <asp:HiddenField ID="chargeableCancellation" runat="server" />
    <ej:Dialog ID="cancelAppointmentDialog" meta:resourceKey="cancelAppointmentDialog" runat="server" ShowOnInit="false" IsResponsive="true" EnableResize="false" EnableModal="true">
        <DialogContent>
            <div class="row margin-bottom">
                <div class="col-sm-12">
                    <asp:Label runat="server" class="control-label" meta:resourcekey="cancelAppointmentInfoLbl"></asp:Label>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <%--<asp:Label for="noChargeableCanceledAppointmentChkBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="noChargeableCanceledAppointmentChkBox"></asp:Label>--%>
                    <%--<asp:RadioButton GroupName="ChargableCanceledAppointmentOptions" ValidationGroup="CancelAppointmentGroup" ValidateRequestMode="Enabled" TextAlign="Left" ID="noChargeableCanceledAppointmentRad" runat="server" meta:resourcekey="noChargeableCanceledAppointmentChkBox" />--%>
                    <input runat="server" type="radio" onclick="SetNoChargeableCancellation()" id="noChargeableCanceledAppointmentRad" name="CancelAppointmentGroup" value="" /><%= GetLocalResourceObject("noChargeableCanceledAppointmentChkBox.Text") %>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <%--<asp:RadioButton GroupName="ChargableCanceledAppointmentOptions" ValidationGroup="CancelAppointmentGroup" ValidateRequestMode="Enabled" TextAlign="Left" ID="chargeableCanceledAppointmentRad" runat="server" meta:resourcekey="chargeableCanceledAppointmentChkBox" />--%>
                    <input runat="server" type="radio" onclick="SetChargeableCancellation()" id="chargeableCanceledAppointmentRad" name="CancelAppointmentGroup" meta:resourcekey="chargeableCanceledAppointmentChkBox"><%= GetLocalResourceObject("chargeableCanceledAppointmentChkBox.Text") %>
                </div>
            </div>
            <div runat="server" id="cancelUpcomingAppointmentsDiv" class="row margin-bottom">
                <div class="col-sm-12">
                    <div class="form-group left-side">
                        <asp:Label for="cancelUpcomingAppointmentsChkBox" runat="server" class="control-label" meta:resourcekey="cancelUpcomingAppointmentsLbl"></asp:Label>
                        <%--<input type="checkbox" runat="server" id="cancelUpcomingAppointmentsChkBox" />--%>
                        <ej:CheckBox runat="server" ID="cancelUpcomingAppointmentsChkBox" Size="Medium" EnableViewState="true"></ej:CheckBox>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 text-right">
                    <asp:LinkButton ID="cancelAppointmentBtn" runat="server" CssClass="btn btn-danger btn-flat margin-r-5" Text="<%$ Resources:GlobalResources, Cancel %>" OnClientClick="return onCancelAppointmentClick();" OnClick="cancelAppointmentBtn_Click"></asp:LinkButton>
                    <asp:LinkButton ID="closeCancelAppointmentDialogBtn" runat="server" CssClass="btn btn-default btn-flat margin-r-5" Text="<%$ Resources:GlobalResources, Close %>" OnClientClick="closeCancelAppointmentDialog(); return false;"></asp:LinkButton>
                </div>
            </div>
        </DialogContent>
    </ej:Dialog>
    <script language="javascript" type="text/javascript">
        function onCancelAppointmentClick() {
            if (document.getElementById("<%= noChargeableCanceledAppointmentRad.ClientID %>").checked || document.getElementById("<%= chargeableCanceledAppointmentRad.ClientID %>").checked) {
                return true;
            }
            else {
                alert("<%= this.GetLocalResourceObject("ChargeableOptionRequiredMessage") %>");

                return false;
            }
        }
        //-->
    </script>
</asp:Content>
