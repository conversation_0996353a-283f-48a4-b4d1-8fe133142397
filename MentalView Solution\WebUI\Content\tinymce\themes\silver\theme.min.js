/**
 * TinyMCE version 6.5.1 (2023-06-19)
 */
!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,s=e=>t=>e===t,r=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=s(null),d=n("boolean"),u=s(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=e=>()=>e(),y=(e,t)=>(...o)=>e(t.apply(null,o)),x=e=>()=>e,w=e=>e,S=(e,t)=>e===t;function k(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const C=e=>t=>!e(t),O=e=>()=>{throw new Error(e)},_=e=>e(),T=x(!1),E=x(!0);class A{constructor(e,t){this.tag=e,this.value=t}static some(e){return new A(!0,e)}static none(){return A.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?A.some(e(this.value)):A.none()}bind(e){return this.tag?e(this.value):A.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:A.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?A.some(e):A.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}A.singletonNone=new A(!1);const M=Array.prototype.slice,D=Array.prototype.indexOf,B=Array.prototype.push,F=(e,t)=>D.call(e,t),I=(e,t)=>{const o=F(e,t);return-1===o?A.none():A.some(o)},R=(e,t)=>F(e,t)>-1,N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},V=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},z=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const s=M.call(e,n,n+t);o.push(s)}return o},H=(e,t)=>{const o=e.length,n=new Array(o);for(let s=0;s<o;s++){const o=e[s];n[s]=t(o,s)}return n},L=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},P=(e,t)=>{const o=[],n=[];for(let s=0,r=e.length;s<r;s++){const r=e[s];(t(r,s)?o:n).push(r)}return{pass:o,fail:n}},U=(e,t)=>{const o=[];for(let n=0,s=e.length;n<s;n++){const s=e[n];t(s,n)&&o.push(s)}return o},W=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),j=(e,t,o)=>(L(e,((e,n)=>{o=t(o,e,n)})),o),G=(e,t)=>((e,t,o)=>{for(let n=0,s=e.length;n<s;n++){const s=e[n];if(t(s,n))return A.some(s);if(o(s,n))break}return A.none()})(e,t,T),$=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return A.some(o);return A.none()},q=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);B.apply(t,e[o])}return t},X=(e,t)=>q(H(e,t)),K=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},Y=e=>{const t=M.call(e,0);return t.reverse(),t},J=(e,t)=>U(e,(e=>!R(t,e))),Z=(e,t)=>{const o={};for(let n=0,s=e.length;n<s;n++){const s=e[n];o[String(s)]=t(s,n)}return o},Q=e=>[e],ee=(e,t)=>{const o=M.call(e,0);return o.sort(t),o},te=(e,t)=>t>=0&&t<e.length?A.some(e[t]):A.none(),oe=e=>te(e,0),ne=e=>te(e,e.length-1),se=p(Array.from)?Array.from:e=>M.call(e),re=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return A.none()},ae=Object.keys,ie=Object.hasOwnProperty,le=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n];t(e[s],s)}},ce=(e,t)=>de(e,((e,o)=>({k:o,v:t(e,o)}))),de=(e,t)=>{const o={};return le(e,((e,n)=>{const s=t(e,n);o[s.k]=s.v})),o},ue=e=>(t,o)=>{e[o]=t},me=(e,t,o,n)=>{le(e,((e,s)=>{(t(e,s)?o:n)(e,s)}))},ge=(e,t)=>{const o={};return me(e,t,ue(o),b),o},pe=(e,t)=>{const o=[];return le(e,((e,n)=>{o.push(t(e,n))})),o},he=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n],r=e[s];if(t(r,s,e))return A.some(r)}return A.none()},fe=e=>pe(e,w),be=(e,t)=>ve(e,t)?A.from(e[t]):A.none(),ve=(e,t)=>ie.call(e,t),ye=(e,t)=>ve(e,t)&&void 0!==e[t]&&null!==e[t],xe=(e,t,o=S)=>e.exists((e=>o(e,t))),we=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},Se=(e,t,o)=>e.isSome()&&t.isSome()?A.some(o(e.getOrDie(),t.getOrDie())):A.none(),ke=(e,t)=>null!=e?A.some(t(e)):A.none(),Ce=(e,t)=>e?A.some(t):A.none(),Oe=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,_e=(e,t)=>Ee(e,t)?((e,t)=>e.substring(t))(e,t.length):e,Te=(e,t,o=0,n)=>{const s=e.indexOf(t,o);return-1!==s&&(!!u(n)||s+t.length<=n)},Ee=(e,t)=>Oe(e,t,0),Ae=(e,t)=>Oe(e,t,e.length-t.length),Me=(Ao=/^\s+|\s+$/g,e=>e.replace(Ao,"")),De=e=>e.length>0,Be=e=>void 0!==e.style&&p(e.style.getPropertyValue),Fe=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},Ie=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return Fe(o.childNodes[0])},Re=(e,t)=>{const o=(t||document).createElement(e);return Fe(o)},Ne=(e,t)=>{const o=(t||document).createTextNode(e);return Fe(o)},Ve=Fe,ze="undefined"!=typeof window?window:Function("return this;")(),He=(e,t)=>((e,t)=>{let o=null!=t?t:ze;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t),Le=Object.getPrototypeOf,Pe=e=>{const t=He("ownerDocument.defaultView",e);return a(e)&&((e=>((e,t)=>{const o=((e,t)=>He(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(Le(e).constructor.name))},Ue=e=>e.dom.nodeName.toLowerCase(),We=e=>t=>(e=>e.dom.nodeType)(t)===e,je=e=>Ge(e)&&Pe(e.dom),Ge=We(1),$e=We(3),qe=We(9),Xe=We(11),Ke=e=>t=>Ge(t)&&Ue(t)===e,Ye=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Je=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Ze=(e,t)=>e.dom===t.dom,Qe=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},et=e=>Ve(e.dom.ownerDocument),tt=e=>qe(e)?e:et(e),ot=e=>Ve(tt(e).dom.documentElement),nt=e=>Ve(tt(e).dom.defaultView),st=e=>A.from(e.dom.parentNode).map(Ve),rt=e=>A.from(e.dom.parentElement).map(Ve),at=e=>A.from(e.dom.offsetParent).map(Ve),it=e=>H(e.dom.childNodes,Ve),lt=(e,t)=>{const o=e.dom.childNodes;return A.from(o[t]).map(Ve)},ct=e=>lt(e,0),dt=(e,t)=>({element:e,offset:t}),ut=(e,t)=>{const o=it(e);return o.length>0&&t<o.length?dt(o[t],0):dt(e,t)},mt=e=>Xe(e)&&g(e.dom.host),gt=p(Element.prototype.attachShadow)&&p(Node.prototype.getRootNode),pt=x(gt),ht=gt?e=>Ve(e.dom.getRootNode()):tt,ft=e=>mt(e)?e:Ve(tt(e).dom.body),bt=e=>{const t=ht(e);return mt(t)?A.some(t):A.none()},vt=e=>Ve(e.dom.host),yt=e=>{const t=$e(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return bt(Ve(t)).fold((()=>o.body.contains(t)),(n=yt,s=vt,e=>n(s(e))));var n,s},xt=()=>wt(Ve(document)),wt=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return Ve(t)},St=(e,t,o)=>{if(!(r(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},kt=(e,t,o)=>{St(e.dom,t,o)},Ct=(e,t)=>{const o=e.dom;le(t,((e,t)=>{St(o,t,e)}))},Ot=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},_t=(e,t)=>A.from(Ot(e,t)),Tt=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},Et=(e,t)=>{e.dom.removeAttribute(t)},At=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Be(e)&&e.style.setProperty(t,o)},Mt=(e,t)=>{Be(e)&&e.style.removeProperty(t)},Dt=(e,t,o)=>{const n=e.dom;At(n,t,o)},Bt=(e,t)=>{const o=e.dom;le(t,((e,t)=>{At(o,t,e)}))},Ft=(e,t)=>{const o=e.dom;le(t,((e,t)=>{e.fold((()=>{Mt(o,t)}),(e=>{At(o,t,e)}))}))},It=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||yt(e)?n:Rt(o,t)},Rt=(e,t)=>Be(e)?e.style.getPropertyValue(t):"",Nt=(e,t)=>{const o=e.dom,n=Rt(o,t);return A.from(n).filter((e=>e.length>0))},Vt=e=>{const t={},o=e.dom;if(Be(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},zt=(e,t,o)=>{const n=Re(e);return Dt(n,t,o),Nt(n,t).isSome()},Ht=(e,t)=>{const o=e.dom;Mt(o,t),xe(_t(e,"style").map(Me),"")&&Et(e,"style")},Lt=e=>e.dom.offsetWidth,Pt=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=It(o,e);return parseFloat(t)||0}return n},n=(e,t)=>j(t,((t,o)=>{const n=It(e,o),s=void 0===n?0:parseInt(n,10);return isNaN(s)?t:t+s}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Be(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const s=n(e,o);return t>s?t-s:0}}},Ut=Pt("height",(e=>{const t=e.dom;return yt(e)?t.getBoundingClientRect().height:t.offsetHeight})),Wt=e=>Ut.get(e),jt=e=>Ut.getOuter(e),Gt=(e,t)=>({left:e,top:t,translate:(o,n)=>Gt(e+o,t+n)}),$t=Gt,qt=(e,t)=>void 0!==e?e:void 0!==t?t:0,Xt=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,s=t.documentElement;if(o===e.dom)return $t(o.offsetLeft,o.offsetTop);const r=qt(null==n?void 0:n.pageYOffset,s.scrollTop),a=qt(null==n?void 0:n.pageXOffset,s.scrollLeft),i=qt(s.clientTop,o.clientTop),l=qt(s.clientLeft,o.clientLeft);return Kt(e).translate(a-l,r-i)},Kt=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?$t(o.offsetLeft,o.offsetTop):yt(e)?(e=>{const t=e.getBoundingClientRect();return $t(t.left,t.top)})(t):$t(0,0)},Yt=Pt("width",(e=>e.dom.offsetWidth)),Jt=e=>Yt.get(e),Zt=e=>Yt.getOuter(e),Qt=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},eo=()=>to(0,0),to=(e,t)=>({major:e,minor:t}),oo={nu:to,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?eo():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return to(n(1),n(2))})(e,o)},unknown:eo},no=(e,t)=>{const o=String(t).toLowerCase();return G(e,(e=>e.search(o)))},so=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ro=e=>t=>Te(t,e),ao=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Te(e,"edge/")&&Te(e,"chrome")&&Te(e,"safari")&&Te(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,so],search:e=>Te(e,"chrome")&&!Te(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Te(e,"msie")||Te(e,"trident")},{name:"Opera",versionRegexes:[so,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:ro("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:ro("firefox")},{name:"Safari",versionRegexes:[so,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Te(e,"safari")||Te(e,"mobile/"))&&Te(e,"applewebkit")}],io=[{name:"Windows",search:ro("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Te(e,"iphone")||Te(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:ro("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:ro("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:ro("linux"),versionRegexes:[]},{name:"Solaris",search:ro("sunos"),versionRegexes:[]},{name:"FreeBSD",search:ro("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:ro("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],lo={browsers:x(ao),oses:x(io)},co="Edge",uo="Chromium",mo="Opera",go="Firefox",po="Safari",ho=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(co),isChromium:n(uo),isIE:n("IE"),isOpera:n(mo),isFirefox:n(go),isSafari:n(po)}},fo=()=>ho({current:void 0,version:oo.unknown()}),bo=ho,vo=(x(co),x(uo),x("IE"),x(mo),x(go),x(po),"Windows"),yo="Android",xo="Linux",wo="macOS",So="Solaris",ko="FreeBSD",Co="ChromeOS",Oo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(vo),isiOS:n("iOS"),isAndroid:n(yo),isMacOS:n(wo),isLinux:n(xo),isSolaris:n(So),isFreeBSD:n(ko),isChromeOS:n(Co)}},_o=()=>Oo({current:void 0,version:oo.unknown()}),To=Oo,Eo=(x(vo),x("iOS"),x(yo),x(xo),x(wo),x(So),x(ko),x(Co),e=>window.matchMedia(e).matches);var Ao;let Mo=Qt((()=>((e,t,o)=>{const n=lo.browsers(),s=lo.oses(),r=t.bind((e=>((e,t)=>re(t.brands,(t=>{const o=t.brand.toLowerCase();return G(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:oo.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>no(e,t).map((e=>{const o=oo.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(fo,bo),a=((e,t)=>no(e,t).map((e=>{const o=oo.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(s,e).fold(_o,To),i=((e,t,o,n)=>{const s=e.isiOS()&&!0===/ipad/i.test(o),r=e.isiOS()&&!s,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=s||!r&&a&&n("(min-device-width:768px)"),c=r||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:x(s),isiPhone:x(r),isTablet:x(l),isPhone:x(c),isTouch:x(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:x(d),isDesktop:x(u)}})(a,r,e,o);return{browser:r,os:a,deviceType:i}})(navigator.userAgent,A.from(navigator.userAgentData),Eo)));const Do=()=>Mo(),Bo=e=>{const t=Ve((e=>{if(pt()&&g(e.target)){const t=Ve(e.target);if(Ge(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return oe(t)}}return A.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),s=y(n,o);return((e,t,o,n,s,r,a)=>({target:e,x:t,y:o,stop:n,prevent:s,kill:r,raw:a}))(t,e.clientX,e.clientY,o,n,s,e)},Fo=(e,t,o,n,s)=>{const r=((e,t)=>o=>{e(o)&&t(Bo(o))})(o,n);return e.dom.addEventListener(t,r,s),{unbind:k(Io,e,t,r,s)}},Io=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Ro=(e,t)=>{st(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},No=(e,t)=>{const o=(e=>A.from(e.dom.nextSibling).map(Ve))(e);o.fold((()=>{st(e).each((e=>{zo(e,t)}))}),(e=>{Ro(e,t)}))},Vo=(e,t)=>{ct(e).fold((()=>{zo(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},zo=(e,t)=>{e.dom.appendChild(t.dom)},Ho=(e,t)=>{L(t,(t=>{zo(e,t)}))},Lo=e=>{e.dom.textContent="",L(it(e),(e=>{Po(e)}))},Po=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Uo=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return $t(o,n)},Wo=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},jo=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Go=e=>{const t=void 0===e?window:e,o=t.document,n=Uo(Ve(o));return(e=>{const t=void 0===e?window:e;return Do().browser.isFirefox()?A.none():A.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,s=e.clientHeight;return jo(n.left,n.top,o,s)}),(e=>jo(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},$o=()=>Ve(document),qo=(e,t)=>e.view(t).fold(x([]),(t=>{const o=e.owner(t),n=qo(e,o);return[t].concat(n)}));var Xo=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?A.none():A.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(Ve)},owner:e=>et(e)});const Ko=e=>{const t=$o(),o=Uo(t),n=((e,t)=>{const o=t.owner(e),n=qo(t,o);return A.some(n)})(e,Xo);return n.fold(k(Xt,e),(t=>{const n=Kt(e),s=W(t,((e,t)=>{const o=Kt(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return $t(s.left+n.left+o.left,s.top+n.top+o.top)}))},Yo=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Jo=e=>{const t=Xt(e),o=Zt(e),n=jt(e);return Yo(t.left,t.top,o,n)},Zo=e=>{const t=Ko(e),o=Zt(e),n=jt(e);return Yo(t.left,t.top,o,n)},Qo=(e,t)=>{const o=Math.max(e.x,t.x),n=Math.max(e.y,t.y),s=Math.min(e.right,t.right),r=Math.min(e.bottom,t.bottom);return Yo(o,n,s-o,r-n)},en=()=>Go(window);var tn=tinymce.util.Tools.resolve("tinymce.ThemeManager");const on=e=>{const t=t=>t(e),o=x(e),n=()=>s,s={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:E,isError:T,map:t=>sn.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>A.some(e)};return s},nn=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:T,isError:E,map:t,mapError:t=>sn.error(t(e)),bind:t,exists:T,forall:E,getOr:w,or:w,getOrThunk:_,orThunk:_,getOrDie:O(String(e)),each:b,toOptional:A.none};return o},sn={value:on,error:nn,fromOption:(e,t)=>e.fold((()=>nn(t)),on)};var rn;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(rn||(rn={}));const an=(e,t,o)=>e.stype===rn.Error?t(e.serror):o(e.svalue),ln=e=>({stype:rn.Value,svalue:e}),cn=e=>({stype:rn.Error,serror:e}),dn=ln,un=cn,mn=an,gn=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),pn=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},hn=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const s=t[n];for(const t in s)ve(s,t)&&(o[t]=e(o[t],s[t]))}return o},fn=hn(((e,t)=>i(e)&&i(t)?fn(e,t):t)),bn=hn(((e,t)=>t)),vn=e=>({tag:"defaultedThunk",process:e}),yn=e=>vn(x(e)),xn=e=>({tag:"mergeWithThunk",process:e}),wn=e=>{const t=(e=>{const t=[],o=[];return L(e,(e=>{an(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,y(un,q)(o)):dn(t.values);var o},Sn=e=>a(e)&&ae(e).length>100?" removed due to size":JSON.stringify(e,null,2),kn=(e,t)=>un([{path:e,getErrorInfo:t}]),Cn=e=>({extract:(t,o)=>((e,t)=>e.stype===rn.Error?t(e.serror):e)(e(o),(e=>((e,t)=>kn(e,x(t)))(t,e))),toString:x("val")}),On=Cn(dn),_n=(e,t,o,n)=>n(be(e,t).getOrThunk((()=>o(e)))),Tn=(e,t,o,n,s)=>{const r=e=>s.extract(t.concat([n]),e),a=e=>e.fold((()=>dn(A.none())),(e=>((e,t)=>e.stype===rn.Value?{stype:rn.Value,svalue:t(e.svalue)}:e)(s.extract(t.concat([n]),e),A.some)));switch(e.tag){case"required":return((e,t,o,n)=>be(t,o).fold((()=>((e,t,o)=>kn(e,(()=>'Could not find valid *required* value for "'+t+'" in '+Sn(o))))(e,o,t)),n))(t,o,n,r);case"defaultedThunk":return _n(o,n,e.process,r);case"option":return((e,t,o)=>o(be(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(be(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return _n(o,n,x({}),(t=>{const n=fn(e.process(o),t);return r(n)}))}},En=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),An=e=>ae(ge(e,g)),Mn=e=>{const t=Dn(e),o=W(e,((e,t)=>pn(t,(t=>fn(e,{[t]:!0})),x(e))),{});return{extract:(e,n)=>{const s=d(n)?[]:An(n),r=U(s,(e=>!ye(o,e)));return 0===r.length?t.extract(e,n):((e,t)=>kn(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,r)},toString:t.toString}},Dn=e=>({extract:(t,o)=>((e,t,o)=>{const n={},s=[];for(const r of o)pn(r,((o,r,a,i)=>{const l=Tn(a,e,t,o,i);mn(l,(e=>{s.push(...e)}),(e=>{n[r]=e}))}),((e,o)=>{n[e]=o(t)}));return s.length>0?un(s):dn(n)})(t,o,e),toString:()=>{const t=H(e,(e=>pn(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),Bn=e=>({extract:(t,o)=>{const n=H(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return wn(n)},toString:()=>"array("+e.toString()+")"}),Fn=(e,t)=>{const o=void 0!==t?t:w;return{extract:(t,n)=>{const s=[];for(const r of e){const e=r.extract(t,n);if(e.stype===rn.Value)return{stype:rn.Value,svalue:o(e.svalue)};s.push(e)}return wn(s)},toString:()=>"oneOf("+H(e,(e=>e.toString())).join(", ")+")"}},In=(e,t)=>({extract:(o,n)=>{const s=ae(n),r=((t,o)=>Bn(Cn(e)).extract(t,o))(o,s);return((e,t)=>e.stype===rn.Value?t(e.svalue):e)(r,(e=>{const s=H(e,(e=>gn(e,e,{tag:"required",process:{}},t)));return Dn(s).extract(o,n)}))},toString:()=>"setOf("+t.toString()+")"}),Rn=y(Bn,Dn),Nn=x(On),Vn=(e,t)=>Cn((o=>{const n=typeof o;return e(o)?dn(o):un(`Expected type: ${t} but got: ${n}`)})),zn=Vn(h,"number"),Hn=Vn(r,"string"),Ln=Vn(d,"boolean"),Pn=Vn(p,"function"),Un=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>Un(e[t])));default:return!1}},Wn=Cn((e=>Un(e)?dn(e):un("Expected value to be acceptable for sending via postMessage"))),jn=(e,t)=>({extract:(o,n)=>be(n,e).fold((()=>((e,t)=>kn(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>be(o,n).fold((()=>((e,t,o)=>kn(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+Sn(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+ae(t)}),Gn=e=>Cn((t=>e(t).fold(un,dn))),$n=(e,t)=>In((t=>e(t).fold(cn,ln)),t),qn=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===rn.Error?{stype:rn.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),an(n,sn.error,sn.value);var n},Xn=e=>e.fold((e=>{throw new Error(Yn(e))}),w),Kn=(e,t,o)=>Xn(qn(e,t,o)),Yn=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:x("... (only showing first ten failures)")}]):e;return H(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+Sn(e.input),Jn=(e,t)=>jn(e,ce(t,Dn)),Zn=(e,t)=>((e,t)=>{const o=Qt(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,t),Qn=gn,es=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),ts=e=>Gn((t=>R(e,t)?sn.value(t):sn.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),os=e=>Qn(e,e,{tag:"required",process:{}},Nn()),ns=(e,t)=>Qn(e,e,{tag:"required",process:{}},t),ss=e=>ns(e,zn),rs=e=>ns(e,Hn),as=(e,t)=>Qn(e,e,{tag:"required",process:{}},ts(t)),is=e=>ns(e,Pn),ls=(e,t)=>Qn(e,e,{tag:"required",process:{}},Dn(t)),cs=(e,t)=>Qn(e,e,{tag:"required",process:{}},Rn(t)),ds=(e,t)=>Qn(e,e,{tag:"required",process:{}},Bn(t)),us=e=>Qn(e,e,{tag:"option",process:{}},Nn()),ms=(e,t)=>Qn(e,e,{tag:"option",process:{}},t),gs=e=>ms(e,zn),ps=e=>ms(e,Hn),hs=(e,t)=>ms(e,ts(t)),fs=e=>ms(e,Pn),bs=(e,t)=>ms(e,Bn(t)),vs=(e,t)=>ms(e,Dn(t)),ys=(e,t)=>Qn(e,e,yn(t),Nn()),xs=(e,t,o)=>Qn(e,e,yn(t),o),ws=(e,t)=>xs(e,t,zn),Ss=(e,t)=>xs(e,t,Hn),ks=(e,t,o)=>xs(e,t,ts(o)),Cs=(e,t)=>xs(e,t,Ln),Os=(e,t)=>xs(e,t,Pn),_s=(e,t,o)=>xs(e,t,Bn(o)),Ts=(e,t,o)=>xs(e,t,Dn(o)),Es=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},As=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return L(e,((n,s)=>{const r=ae(n);if(1!==r.length)throw new Error("one and only one name per case");const a=r[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,o)},match:e=>{const n=ae(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!K(t,(e=>R(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o};As([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const Ms=(e,t)=>((e,t)=>({[e]:t}))(e,t),Ds=e=>(e=>{const t={};return L(e,(e=>{t[e.key]=e.value})),t})(e),Bs=e=>p(e)?e:T,Fs=(e,t,o)=>{let n=e.dom;const s=Bs(o);for(;n.parentNode;){n=n.parentNode;const e=Ve(n),o=t(e);if(o.isSome())return o;if(s(e))break}return A.none()},Is=(e,t,o)=>{const n=t(e),s=Bs(o);return n.orThunk((()=>s(e)?A.none():Fs(e,t,s)))},Rs=(e,t)=>Ze(e.element,t.event.target),Ns={can:E,abort:T,run:b},Vs=e=>{if(!ye(e,"can")&&!ye(e,"abort")&&!ye(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...Ns,...e}},zs=x,Hs=zs("touchstart"),Ls=zs("touchmove"),Ps=zs("touchend"),Us=zs("touchcancel"),Ws=zs("mousedown"),js=zs("mousemove"),Gs=zs("mouseout"),$s=zs("mouseup"),qs=zs("mouseover"),Xs=zs("focusin"),Ks=zs("focusout"),Ys=zs("keydown"),Js=zs("keyup"),Zs=zs("input"),Qs=zs("change"),er=zs("click"),tr=zs("transitioncancel"),or=zs("transitionend"),nr=zs("transitionstart"),sr=zs("selectstart"),rr=e=>x("alloy."+e),ar={tap:rr("tap")},ir=rr("focus"),lr=rr("blur.post"),cr=rr("paste.post"),dr=rr("receive"),ur=rr("execute"),mr=rr("focus.item"),gr=ar.tap,pr=rr("longpress"),hr=rr("sandbox.close"),fr=rr("typeahead.cancel"),br=rr("system.init"),vr=rr("system.touchmove"),yr=rr("system.touchend"),xr=rr("system.scroll"),wr=rr("system.resize"),Sr=rr("system.attached"),kr=rr("system.detached"),Cr=rr("system.dismissRequested"),Or=rr("system.repositionRequested"),_r=rr("focusmanager.shifted"),Tr=rr("slotcontainer.visibility"),Er=rr("system.external.element.scroll"),Ar=rr("change.tab"),Mr=rr("dismiss.tab"),Dr=rr("highlight"),Br=rr("dehighlight"),Fr=(e,t)=>{Vr(e,e.element,t,{})},Ir=(e,t,o)=>{Vr(e,e.element,t,o)},Rr=e=>{Fr(e,ur())},Nr=(e,t,o)=>{Vr(e,t,o,{})},Vr=(e,t,o,n)=>{const s={target:t,...n};e.getSystem().triggerEvent(o,t,s)},zr=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},Hr=e=>Ds(e),Lr=(e,t)=>({key:e,value:Vs({abort:t})}),Pr=e=>({key:e,value:Vs({run:(e,t)=>{t.event.prevent()}})}),Ur=(e,t)=>({key:e,value:Vs({run:t})}),Wr=(e,t,o)=>({key:e,value:Vs({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),jr=e=>t=>({key:e,value:Vs({run:(e,o)=>{Rs(e,o)&&t(e,o)}})}),Gr=(e,t,o)=>((e,t)=>Ur(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{zr(t,t.element,e,n)}))})))(e,t.partUids[o]),$r=(e,t)=>Ur(e,((e,o)=>{const n=o.event,s=e.getSystem().getByDom(n.target).getOrThunk((()=>Is(n.target,(t=>e.getSystem().getByDom(t).toOptional()),T).getOr(e)));t(e,s,o)})),qr=e=>Ur(e,((e,t)=>{t.cut()})),Xr=e=>Ur(e,((e,t)=>{t.stop()})),Kr=(e,t)=>jr(e)(t),Yr=jr(Sr()),Jr=jr(kr()),Zr=jr(br()),Qr=(ra=ur(),e=>Ur(ra,e)),ea=e=>e.dom.innerHTML,ta=(e,t)=>{const o=et(e).dom,n=Ve(o.createDocumentFragment()),s=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,it(Ve(o))})(t,o);Ho(n,s),Lo(e),zo(e,n)},oa=e=>mt(e)?"#shadow-root":(e=>{const t=Re("div"),o=Ve(e.dom.cloneNode(!0));return zo(t,o),ea(t)})((e=>((e,t)=>Ve(e.dom.cloneNode(!1)))(e))(e)),na=e=>oa(e),sa=Hr([((e,t)=>({key:e,value:Vs({can:(e,t)=>{const o=t.event,n=o.originator,s=o.target;return!((e,t,o)=>Ze(t,e.element)&&!Ze(t,o))(e,n,s)||(console.warn(ir()+" did not get interpreted by the desired target. \nOriginator: "+na(n)+"\nTarget: "+na(s)+"\nCheck the "+ir()+" event handlers"),!1)}})}))(ir())]);var ra,aa=Object.freeze({__proto__:null,events:sa});let ia=0;const la=e=>{const t=(new Date).getTime(),o=Math.floor(1e9*Math.random());return ia++,e+"_"+o+ia+String(t)},ca=x("alloy-id-"),da=x("data-alloy-id"),ua=ca(),ma=da(),ga=(e,t)=>{Object.defineProperty(e.dom,ma,{value:t,writable:!0})},pa=e=>{const t=Ge(e)?e.dom[ma]:null;return A.from(t)},ha=e=>la(e),fa=w,ba=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+na(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:x("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:T}},va=ba(),ya=e=>H(e,(e=>Ae(e,"/*")?e.substring(0,e.length-2):e)),xa=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:ya(r)}),e},wa=la("alloy-premade"),Sa=e=>(Object.defineProperty(e.element.dom,wa,{value:e.uid,writable:!0}),Ms(wa,e)),ka=e=>be(e,wa),Ca=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:ya(r.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),Oa={init:()=>_a({readState:x("No State required")})},_a=e=>e,Ta=(e,t)=>{const o={};return le(e,((e,n)=>{le(e,((e,s)=>{const r=be(o,s).getOr([]);o[s]=r.concat([t(n,e)])}))})),o},Ea=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),Aa=e=>e.cHandler,Ma=(e,t)=>({name:e,handler:t}),Da=(e,t)=>{const o={};return L(e,(e=>{o[e.name()]=e.handlers(t)})),o},Ba=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const s=ee(o,((o,s)=>{const r=o[t],a=s[t],i=n.indexOf(r),l=n.indexOf(a);if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===l)throw new Error("The ordering for "+e+" does not have an entry for "+a+".\nOrder specified: "+JSON.stringify(n,null,2));return i<l?-1:l<i?1:0}));return sn.value(s)}catch(e){return sn.error([e])}})("Event: "+o,"name",e,n).map((e=>(e=>{const t=((e,t)=>(...t)=>j(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=((e,t)=>(...t)=>j(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{L(e,(e=>{e.run.apply(void 0,t)}))}}})(H(e,(e=>e.handler))))):((e,t)=>sn.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(H(t,(e=>e.name)),null,2)]))(o,e)},Fa=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return L(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,sn.error(q(n))):((e,t)=>0===e.length?sn.value(t):sn.value(fn(t,bn.apply(void 0,e))))(o.values,t);var n})(pe(e,((e,o)=>(1===e.length?sn.value(e[0].handler):Ba(e,t,o)).map((n=>{const s=(e=>{const t=(e=>p(e)?{can:E,abort:T,run:e}:e)(e);return(e,o,...n)=>{const s=[e,o].concat(n);t.abort.apply(void 0,s)?o.stop():t.can.apply(void 0,s)&&t.run.apply(void 0,s)}})(n),r=e.length>1?U(t[o],(t=>N(e,(e=>e.name===t)))).join(" > "):e[0].name;return Ms(o,((e,t)=>({handler:e,purpose:t}))(s,r))})))),{}),Ia="alloy.base.behaviour",Ra=Dn([Qn("dom","dom",{tag:"required",process:{}},Dn([os("tag"),ys("styles",{}),ys("classes",[]),ys("attributes",{}),us("value"),us("innerHtml")])),os("components"),os("uid"),ys("events",{}),ys("apis",{}),Qn("eventOrder","eventOrder",(ii={[ur()]:["disabling",Ia,"toggling","typeaheadevents"],[ir()]:[Ia,"focusing","keying"],[br()]:[Ia,"disabling","toggling","representing"],[Zs()]:[Ia,"representing","streaming","invalidating"],[kr()]:[Ia,"representing","item-events","tooltipping"],[Ws()]:["focusing",Ia,"item-type-events"],[Hs()]:["focusing",Ia,"item-type-events"],[qs()]:["item-type-events","tooltipping"],[dr()]:["receiving","reflecting","tooltipping"]},xn(x(ii))),Nn()),us("domModification")]),Na=e=>e.events,Va=(e,t)=>{const o=Ot(e,t);return void 0===o||""===o?[]:o.split(" ")},za=e=>void 0!==e.dom.classList,Ha=e=>Va(e,"class"),La=(e,t)=>{za(e)?e.dom.classList.add(t):((e,t)=>{((e,t,o)=>{const n=Va(e,t).concat([o]);kt(e,t,n.join(" "))})(e,"class",t)})(e,t)},Pa=(e,t)=>{za(e)?e.dom.classList.remove(t):((e,t)=>{((e,t,o)=>{const n=U(Va(e,t),(e=>e!==o));n.length>0?kt(e,t,n.join(" ")):Et(e,t)})(e,"class",t)})(e,t),(e=>{0===(za(e)?e.dom.classList:Ha(e)).length&&Et(e,"class")})(e)},Ua=(e,t)=>za(e)&&e.dom.classList.contains(t),Wa=(e,t)=>{L(t,(t=>{La(e,t)}))},ja=(e,t)=>{L(t,(t=>{Pa(e,t)}))},Ga=(e,t)=>K(t,(t=>Ua(e,t))),$a=e=>e.dom.value,qa=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},Xa=(e,t,o)=>{o.fold((()=>zo(e,t)),(e=>{Ze(e,t)||(Ro(e,t),Po(e))}))},Ka=(e,t,o)=>{const n=H(t,o),s=it(e);return L(s.slice(n.length),Po),n},Ya=(e,t,o,n)=>{const s=lt(e,t),r=n(o,s),a=((e,t,o)=>lt(e,t).map((e=>{if(o.exists((t=>!Ze(t,e)))){const t=o.map(Ue).getOr("span"),n=Re(t);return Ro(e,n),n}return e})))(e,t,s);return Xa(e,r.element,a),r},Ja=(e,t)=>{const o=ae(e),n=ae(t),s=J(n,o),r=((e,o)=>{const n={},s={};return me(e,((e,o)=>!ve(t,o)||e!==t[o]),ue(n),ue(s)),{t:n,f:s}})(e).t;return{toRemove:s,toSet:r}},Za=(e,t)=>{const{class:o,style:n,...s}=(e=>j(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:r,toRemove:a}=Ja(e.attributes,s),i=Vt(t),{toSet:l,toRemove:c}=Ja(e.styles,i),d=(e=>za(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):Ha(e))(t),u=J(d,e.classes),m=J(e.classes,d);return L(a,(e=>Et(t,e))),Ct(t,r),Wa(t,m),ja(t,u),L(c,(e=>Ht(t,e))),Bt(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{Ka(e,t,((t,o)=>{const n=lt(e,o);return Xa(e,t,n),t}))})(t,o)}),(e=>{ta(t,e)})),(()=>{const o=t,n=e.value.getOrUndefined();n!==$a(o)&&qa(o,null!=n?n:"")})(),t},Qa=e=>{const t=(e=>{const t=be(e,"behaviours").getOr({});return X(ae(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=H(t,(e=>vs(e.name(),[os("config"),ys("state",Oa)]))),n=qn("component.behaviours",Dn(o),e.behaviours).fold((t=>{throw new Error(Yn(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),w);return{list:t,data:ce(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return x(t)}))}})(e,t))(e,t)},ei=(e,t)=>{const o=()=>m,n=Es(va),s=Xn((e=>qn("custom.definition",Ra,e))(e)),r=Qa(e),a=(e=>e.list)(r),i=(e=>e.data)(r),l=((e,t,o)=>{const n={...(s=e).dom,uid:s.uid,domChildren:H(s.components,(e=>e.element))};var s;const r=(e=>e.domModification.fold((()=>Ea({})),Ea))(e),a={"alloy.base.modification":r},i=t.length>0?((e,t,o,n)=>{const s={...t};L(o,(t=>{s[t.name()]=t.exhibit(e,n)}));const r=Ta(s,((e,t)=>({name:e,modification:t}))),a=e=>W(e,((e,t)=>({...t.modification,...e})),{}),i=W(r.classes,((e,t)=>t.modification.concat(e)),[]),l=a(r.attributes),c=a(r.styles);return Ea({classes:i,attributes:l,styles:c})})(o,a,t,n):r;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(s,a,i),c=((e,t)=>{const o=t.filter((t=>Ue(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>ve(e.dom,wa))(t))).bind((t=>((e,t)=>{try{const o=Za(e,t);return A.some(o)}catch(e){return A.none()}})(e,t))).getOrThunk((()=>(e=>{const t=Re(e.tag);Ct(t,e.attributes),Wa(t,e.classes),Bt(t,e.styles),e.innerHtml.each((e=>ta(t,e)));const o=e.domChildren;return Ho(t,o),e.value.each((e=>{qa(t,e)})),t})(e)));return ga(o,e.uid),o})(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":Na(e)};return((e,t,o,n)=>{const s=((e,t,o)=>{const n={...o,...Da(t,e)};return Ta(n,Ma)})(e,o,n);return Fa(s,t)})(o,e.eventOrder,t,n).getOrDie()})(s,a,i),u=Es(s.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>s.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(ba(o))},element:c,syncComponents:()=>{const e=it(c),t=X(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},ti=e=>{const t=Ne(e);return oi({element:t})},oi=e=>{const t=Kn("external.component",Mn([os("element"),us("uid")]),e),o=Es(ba()),n=t.uid.getOrThunk((()=>ha("external")));ga(t.element,n);const s={uid:n,getSystem:o.get,config:A.none,hasConfigured:T,connect:e=>{o.set(e)},disconnect:()=>{o.set(ba((()=>s)))},getApis:()=>({}),element:t.element,spec:e,readState:x("No state"),syncComponents:b,components:x([]),events:{}};return Sa(s)},ni=ha,si=(e,t)=>ka(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=fa(e),s=((e,t)=>{const o=be(e,"components").getOr([]);return t.fold((()=>H(o,ri)),(e=>H(o,((t,o)=>si(t,lt(e,o))))))})(n,t),r={...n,events:{...aa,...o},components:s};return sn.value(ei(r,t))})((e=>ve(e,"uid"))(e)?e:{uid:ni(""),...e},t).getOrDie())),ri=e=>si(e,A.none()),ai=Sa;var ii,li=(e,t,o,n,s)=>e(o,n)?A.some(o):p(s)&&s(o)?A.none():t(o,n,s);const ci=(e,t,o)=>{let n=e.dom;const s=p(o)?o:T;for(;n.parentNode;){n=n.parentNode;const e=Ve(n);if(t(e))return A.some(e);if(s(e))break}return A.none()},di=(e,t,o)=>li(((e,t)=>t(e)),ci,e,t,o),ui=(e,t,o)=>di(e,t,o).isSome(),mi=(e,t,o)=>ci(e,(e=>Ye(e,t)),o),gi=(e,t)=>((e,o)=>G(e.dom.childNodes,(e=>{return o=Ve(e),Ye(o,t);var o})).map(Ve))(e),pi=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Je(o)?A.none():A.from(o.querySelector(e)).map(Ve)})(t,e),hi=(e,t,o)=>li(((e,t)=>Ye(e,t)),mi,e,t,o),fi="aria-controls",bi=()=>{const e=la(fi);return{id:e,link:t=>{kt(t,fi,e)},unlink:e=>{Et(e,fi)}}},vi=(e,t)=>ui(t,(t=>Ze(t,e.element)),T)||((e,t)=>(e=>di(e,(e=>{if(!Ge(e))return!1;const t=Ot(e,"id");return void 0!==t&&t.indexOf(fi)>-1})).bind((e=>{const t=Ot(e,"id"),o=ht(e);return pi(o,`[${fi}="${t}"]`)})))(t).exists((t=>vi(e,t))))(e,t);var yi;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(yi||(yi={}));const xi=Es({}),wi=["alloy/data/Fields","alloy/debugging/Debugging"],Si=(e,t,o)=>((e,t,o)=>{switch(be(xi.get(),e).orThunk((()=>{const t=ae(xi.get());return re(t,(t=>e.indexOf(t)>-1?A.some(xi.get()[t]):A.none()))})).getOr(yi.NORMAL)){case yi.NORMAL:return o(ki());case yi.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const s=(new Date).getTime();R(["mousemove","mouseover","mouseout",br()],e)||console.log(e,{event:e,time:s-n,target:t.dom,sequence:H(o,(e=>R(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+na(e.target)+")":e.outcome))})}}})(e,t),s=o(n);return n.write(),s}case yi.STOP:return!0}})(e,t,o),ki=x({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),Ci=x([os("menu"),os("selectedMenu")]),Oi=x([os("item"),os("selectedItem")]);x(Dn(Oi().concat(Ci())));const _i=x(Dn(Oi())),Ti=ls("initSize",[os("numColumns"),os("numRows")]),Ei=()=>ls("markers",[os("backgroundMenu")].concat(Ci()).concat(Oi())),Ai=e=>ls("markers",H(e,os)),Mi=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");G(t,(e=>e.indexOf("alloy")>0&&!N(wi,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),Qn(t,t,o,Gn((e=>sn.value(((...t)=>e.apply(void 0,t))))))),Di=e=>Mi(0,e,yn(b)),Bi=e=>Mi(0,e,yn(A.none)),Fi=e=>Mi(0,e,{tag:"required",process:{}}),Ii=e=>Mi(0,e,{tag:"required",process:{}}),Ri=(e,t)=>es(e,x(t)),Ni=e=>es(e,w),Vi=x(Ti),zi=(e,t,o,n,s,r,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:s,restriction:r,label:`${a}-${s}`,alwaysFit:i}),Hi=As([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Li=Hi.southeast,Pi=Hi.southwest,Ui=Hi.northeast,Wi=Hi.northwest,ji=Hi.south,Gi=Hi.north,$i=Hi.east,qi=Hi.west,Xi=(e,t,o,n)=>{const s=e+t;return s>n?o:s<o?n:s},Ki=(e,t,o)=>Math.min(Math.max(e,t),o),Yi=(e,t)=>Z(["left","right","top","bottom"],(o=>be(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),Ji="layout",Zi=e=>e.x,Qi=(e,t)=>e.x+e.width/2-t.width/2,el=(e,t)=>e.x+e.width-t.width,tl=(e,t)=>e.y-t.height,ol=e=>e.y+e.height,nl=(e,t)=>e.y+e.height/2-t.height/2,sl=(e,t,o)=>zi(Zi(e),ol(e),o.southeast(),Li(),"southeast",Yi(e,{left:1,top:3}),Ji),rl=(e,t,o)=>zi(el(e,t),ol(e),o.southwest(),Pi(),"southwest",Yi(e,{right:0,top:3}),Ji),al=(e,t,o)=>zi(Zi(e),tl(e,t),o.northeast(),Ui(),"northeast",Yi(e,{left:1,bottom:2}),Ji),il=(e,t,o)=>zi(el(e,t),tl(e,t),o.northwest(),Wi(),"northwest",Yi(e,{right:0,bottom:2}),Ji),ll=(e,t,o)=>zi(Qi(e,t),tl(e,t),o.north(),Gi(),"north",Yi(e,{bottom:2}),Ji),cl=(e,t,o)=>zi(Qi(e,t),ol(e),o.south(),ji(),"south",Yi(e,{top:3}),Ji),dl=(e,t,o)=>zi((e=>e.x+e.width)(e),nl(e,t),o.east(),$i(),"east",Yi(e,{left:0}),Ji),ul=(e,t,o)=>zi(((e,t)=>e.x-t.width)(e,t),nl(e,t),o.west(),qi(),"west",Yi(e,{right:1}),Ji),ml=()=>[sl,rl,al,il,cl,ll,dl,ul],gl=()=>[rl,sl,il,al,cl,ll,dl,ul],pl=()=>[al,il,sl,rl,ll,cl],hl=()=>[il,al,rl,sl,ll,cl],fl=()=>[sl,rl,al,il,cl,ll],bl=()=>[rl,sl,il,al,cl,ll];var vl=Object.freeze({__proto__:null,events:e=>Hr([Ur(dr(),((t,o)=>{const n=e.channels,s=ae(n),r=o,a=((e,t)=>t.universal?e:U(e,(e=>R(t.channels,e))))(s,r);L(a,(e=>{const o=n[e],s=o.schema,a=Kn("channel["+e+"] data\nReceiver: "+na(t.element),s,r.data);o.onReceive(t,a)}))}))])}),yl=[ns("channels",$n(sn.value,Mn([Fi("onReceive"),ys("schema",Nn())])))];const xl=(e,t,o)=>Zr(((n,s)=>{o(n,e,t)})),wl=e=>({key:e,value:void 0}),Sl=(e,t,o,n,s,r,a)=>{const i=e=>ye(e,o)?e[o]():A.none(),l=ce(s,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),s=n.indexOf(")")+1,r=n.indexOf("("),a=n.substring(r+1,s-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:ya(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...s)=>{const r=[n].concat(s);return n.config({name:x(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(r,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...ce(r,((e,t)=>xa(e,t))),...l,revoke:k(wl,o),config:t=>{const n=Kn(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:Qt((()=>Kn(o+"-config",e,t))),initialConfig:t,state:a}}},schema:x(t),exhibit:(e,t)=>Se(i(e),be(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>Ea({}))),name:x(o),handlers:e=>i(e).map((e=>be(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},kl=e=>Ds(e),Cl=Mn([os("fields"),os("name"),ys("active",{}),ys("apis",{}),ys("state",Oa),ys("extra",{})]),Ol=e=>{const t=Kn("Creating behaviour: "+e.name,Cl,e);return((e,t,o,n,s,r)=>{const a=Mn(e),i=vs(t,[("config",l=e,ms("config",Mn(l)))]);var l;return Sl(a,i,t,o,n,s,r)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},_l=Mn([os("branchKey"),os("branches"),os("name"),ys("active",{}),ys("apis",{}),ys("state",Oa),ys("extra",{})]),Tl=e=>{const t=Kn("Creating behaviour: "+e.name,_l,e);return((e,t,o,n,s,r)=>{const a=e,i=vs(t,[ms("config",e)]);return Sl(a,i,t,o,n,s,r)})(Jn(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},El=x(void 0),Al=Ol({fields:yl,name:"receiving",active:vl});var Ml=Object.freeze({__proto__:null,exhibit:(e,t)=>Ea({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const Dl=e=>e.dom.focus(),Bl=e=>e.dom.blur(),Fl=e=>{const t=ht(e).dom;return e.dom===t.activeElement},Il=(e=$o())=>A.from(e.dom.activeElement).map(Ve),Rl=e=>Il(ht(e)).filter((t=>e.dom.contains(t.dom))),Nl=(e,t)=>{const o=ht(t),n=Il(o).bind((e=>{const o=t=>Ze(e,t);return o(t)?A.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const s=Ve(e.childNodes[n]);if(t(s))return A.some(s);const r=o(e.childNodes[n]);if(r.isSome())return r}return A.none()};return o(e.dom)})(t,o)})),s=e(t);return n.each((e=>{Il(o).filter((t=>Ze(t,e))).fold((()=>{Dl(e)}),b)})),s},Vl=(e,t,o,n,s)=>{const r=e=>e+"px";return{position:e,left:t.map(r),top:o.map(r),right:n.map(r),bottom:s.map(r)}},zl=(e,t)=>{Ft(e,(e=>({...e,position:A.some(e.position)}))(t))},Hl=As([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),Ll=(e,t,o,n,s,r)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=s-(i+a.width),d=r-(l+a.height),u=A.some(i),m=A.some(l),g=A.some(c),p=A.some(d),h=A.none();return t.direction.fold((()=>Vl(e,u,m,h,h)),(()=>Vl(e,h,m,g,h)),(()=>Vl(e,u,h,h,p)),(()=>Vl(e,h,h,g,p)),(()=>Vl(e,u,m,h,h)),(()=>Vl(e,u,h,h,p)),(()=>Vl(e,u,m,h,h)),(()=>Vl(e,h,m,g,h)))},Pl=(e,t)=>e.fold((()=>{const e=t.rect;return Vl("absolute",A.some(e.x),A.some(e.y),A.none(),A.none())}),((e,o,n,s)=>Ll("absolute",t,e,o,n,s)),((e,o,n,s)=>Ll("fixed",t,e,o,n,s))),Ul=(e,t)=>{const o=k(Ko,t),n=e.fold(o,o,(()=>{const e=Uo();return Ko(t).translate(-e.left,-e.top)})),s=Zt(t),r=jt(t);return Yo(n.left,n.top,s,r)},Wl=(e,t)=>t.fold((()=>e.fold(en,en,Yo)),(t=>e.fold(x(t),x(t),(()=>{const o=jl(e,t.x,t.y);return Yo(o.left,o.top,t.width,t.height)})))),jl=(e,t,o)=>{const n=$t(t,o);return e.fold(x(n),x(n),(()=>{const e=Uo();return n.translate(-e.left,-e.top)}))};Hl.none;const Gl=Hl.relative,$l=Hl.fixed,ql="data-alloy-placement",Xl=e=>_t(e,ql),Kl=As([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),Yl=(e,t,o,n)=>{const s=e.bubble,r=s.offset,a=((e,t,o)=>{const n=(n,s)=>t[n].map((t=>{const r="top"===n||"bottom"===n,a=r?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,s)+a;return r?Ki(i,e.y,e.bottom):Ki(i,e.x,e.right)})).getOr(s),s=n("left",e.x),r=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return Yo(s,r,a-s,i-r)})(n,e.restriction,r),i=e.x+r.left,l=e.y+r.top,c=Yo(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=s&&i>=n&&i<=r,sizeInBounds:l<=s&&l>=o&&c<=r&&c>=n,visibleW:Math.min(d,a>=o?s-a:l-o),visibleH:Math.min(u,i>=n?r-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,s-l),u=Math.max(n,r-c),m=Ki(a,o,d),g=Ki(i,n,u),p=Math.min(m+l,s)-m,h=Math.min(g+c,r)-g;return Yo(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=x(t.bottom-o.y),s=x(o.bottom-t.y),r=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,s,s,n),a=x(t.right-o.x),i=x(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:r}})(e.direction,h,n),y={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:s.classesOn,off:s.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?Kl.fit(y):Kl.nofit(y,m,g,f)},Jl=e=>{const t=Es(A.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(A.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(A.some(e))}}},Zl=()=>Jl((e=>e.unbind())),Ql=()=>{const e=Jl(b);return{...e,on:t=>e.get().each(t)}},ec=E,tc=(e,t,o)=>((e,t,o,n)=>Fo(e,t,o,n,!1))(e,t,ec,o),oc=(e,t,o)=>((e,t,o,n)=>Fo(e,t,o,n,!0))(e,t,ec,o),nc=Bo,sc=["top","bottom","right","left"],rc="data-alloy-transition-timer",ac=(e,t,o,n,s,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,s,a);if(i||((e,t)=>Ga(e,t.classes))(e,n)){Dt(e,"position",o.position);const a=Ul(t,e),l=Pl(t,{...s,rect:a}),c=Z(sc,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return he(t,((t,n)=>!((e,t,o=S)=>Se(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(Ft(e,c),i&&((e,t)=>{Wa(e,t.classes),_t(e,rc).each((t=>{clearTimeout(parseInt(t,10)),Et(e,rc)})),((e,t)=>{const o=Zl(),n=Zl();let s;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return Ze(t.target,e)&&!De(n)&&R(sc,t.raw.propertyName)},i=r=>{if(m(r)||a(r)){o.clear(),n.clear();const a=null==r?void 0:r.raw.type;(m(a)||a===or())&&(clearTimeout(s),Et(e,rc),ja(e,t.classes))}},l=tc(e,nr(),(t=>{a(t)&&(l.unbind(),o.set(tc(e,or(),i)),n.set(tc(e,tr(),i)))})),c=(e=>{const t=t=>{const o=It(e,t).split(/\s*,\s*/);return U(o,De)},o=e=>{if(r(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return Ae(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),s=t("transition-duration");return j(s,((e,t,s)=>{const r=o(n[s])+o(t);return Math.max(e,r)}),0)})(e);requestAnimationFrame((()=>{s=setTimeout(i,c+17),kt(e,rc,s)}))})(e,t)})(e,n),Lt(e))}else ja(e,n.classes)},ic=(e,t)=>{((e,t)=>{const o=Ut.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);Dt(e,"max-height",o+"px")})(e,Math.floor(t))},lc=x(((e,t)=>{ic(e,t),Bt(e,{"overflow-x":"hidden","overflow-y":"auto"})})),cc=x(((e,t)=>{ic(e,t)})),dc=(e,t,o)=>void 0===e[t]?o:e[t],uc=(e,t,o,n)=>{const s=((e,t,o,n)=>{Ht(t,"max-height"),Ht(t,"max-width");const s={width:Zt(r=t),height:jt(r)};var r;return((e,t,o,n,s,r)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,s,e,r),g=Yl(m,a,i,r);return g.fold(x(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:Kl.nofit(l,c,d,u)))};return j(t,((e,t)=>{const o=k(l,t);return e.fold(x(e),o)}),Kl.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:Li(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(w,w)})(t,n.preference,e,s,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=Pl(o.origin,t);o.transition.each((s=>{ac(e,o.origin,n,s,t,o.lastPlacement)})),zl(e,n)})(t,s,n),((e,t)=>{((e,t)=>{kt(e,ql,t)})(e,t.placement)})(t,s),((e,t)=>{const o=t.classes;ja(e,o.off),Wa(e,o.on)})(t,s),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,s,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,s,n),{layout:s.layout,placement:s.placement}},mc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],gc=(e,t,o,n=1)=>{const s=e*n,r=t*n,a=e=>be(o,e).getOr([]),i=(e,t,o)=>{const n=J(mc,o);return{offset:$t(e,t),classesOn:X(o,a),classesOff:X(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(s,r,["top","alignLeft","inset"]),insetNorthwest:()=>i(-s,r,["top","alignRight","inset"]),insetNorth:()=>i(-s/2,r,["top","alignCentre","inset"]),insetSoutheast:()=>i(s,-r,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-s,-r,["bottom","alignRight","inset"]),insetSouth:()=>i(-s/2,-r,["bottom","alignCentre","inset"]),insetEast:()=>i(-s,-r/2,["valignCentre","right","inset"]),insetWest:()=>i(s,-r/2,["valignCentre","left","inset"])}},pc=()=>gc(0,0,{}),hc=w,fc=(e,t)=>o=>"rtl"===bc(o)?t:e,bc=e=>"rtl"===It(e,"direction")?"rtl":"ltr";var vc;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(vc||(vc={}));const yc="data-alloy-vertical-dir",xc=e=>ui(e,(e=>Ge(e)&&Ot(e,"data-alloy-vertical-dir")===vc.BottomToTop)),wc=()=>vs("layouts",[os("onLtr"),os("onRtl"),us("onBottomLtr"),us("onBottomRtl")]),Sc=(e,t,o,n,s,r,a)=>{const i=a.map(xc).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(s):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(r):c.getOr(n);return fc(d,u)(e)};var kc=[os("hotspot"),us("bubble"),ys("overrides",{}),wc(),Ri("placement",((e,t,o)=>{const n=t.hotspot,s=Ul(o,n.element),r=Sc(e.element,t,fl(),bl(),pl(),hl(),A.some(t.hotspot.element));return A.some(hc({anchorBox:s,bubble:t.bubble.getOr(pc()),overrides:t.overrides,layouts:r}))}))],Cc=[os("x"),os("y"),ys("height",0),ys("width",0),ys("bubble",pc()),ys("overrides",{}),wc(),Ri("placement",((e,t,o)=>{const n=jl(o,t.x,t.y),s=Yo(n.left,n.top,t.width,t.height),r=Sc(e.element,t,ml(),gl(),ml(),gl(),A.none());return A.some(hc({anchorBox:s,bubble:t.bubble,overrides:t.overrides,layouts:r}))}))];const Oc=As([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),_c=e=>e.fold(w,((e,t,o)=>e.translate(-t,-o))),Tc=e=>e.fold(w,w),Ec=e=>j(e,((e,t)=>e.translate(t.left,t.top)),$t(0,0)),Ac=e=>{const t=H(e,Tc);return Ec(t)},Mc=Oc.screen,Dc=Oc.absolute,Bc=(e,t,o)=>{const n=et(e.element),s=Uo(n),r=((e,t,o)=>{const n=nt(o.root).dom;return A.from(n.frameElement).map(Ve).filter((t=>{const o=et(t),n=et(e.element);return Ze(o,n)})).map(Xt)})(e,0,o).getOr(s);return Dc(r,s.left,s.top)},Fc=(e,t,o,n)=>{const s=Mc($t(e,t));return A.some(((e,t,o)=>({point:e,width:t,height:o}))(s,o,n))},Ic=(e,t,o,n,s)=>e.map((e=>{const r=[t,e.point],a=(i=()=>Ac(r),l=()=>Ac(r),c=()=>(e=>{const t=H(e,_c);return Ec(t)})(r),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?pl():fl(),m=o.showAbove?hl():bl(),g=Sc(s,o,u,m,u,m,A.none());var p,h,f,b;return hc({anchorBox:d,bubble:o.bubble.getOr(pc()),overrides:o.overrides,layouts:g})}));var Rc=[os("node"),os("root"),us("bubble"),wc(),ys("overrides",{}),ys("showAbove",!1),Ri("placement",((e,t,o)=>{const n=Bc(e,0,t);return t.node.filter(yt).bind((s=>{const r=s.dom.getBoundingClientRect(),a=Fc(r.left,r.top,r.width,r.height),i=t.node.getOr(e.element);return Ic(a,n,t,o,i)}))}))];const Nc=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),Vc=As([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),zc=(Vc.before,Vc.on,Vc.after,e=>e.fold(w,w,w)),Hc=As([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Lc={domRange:Hc.domRange,relative:Hc.relative,exact:Hc.exact,exactFromRange:e=>Hc.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>Ve(e.startContainer),relative:(e,t)=>zc(e),exact:(e,t,o,n)=>e}))(e);return nt(t)},range:Nc},Pc=(e,t,o)=>{const n=e.document.createRange();var s;return s=n,t.fold((e=>{s.setStartBefore(e.dom)}),((e,t)=>{s.setStart(e.dom,t)}),(e=>{s.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},Uc=(e,t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},Wc=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),jc=As([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Gc=(e,t,o)=>t(Ve(o.startContainer),o.startOffset,Ve(o.endContainer),o.endOffset),$c=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:x(e),rtl:A.none}),relative:(t,o)=>({ltr:Qt((()=>Pc(e,t,o))),rtl:Qt((()=>A.some(Pc(e,o,t))))}),exact:(t,o,n,s)=>({ltr:Qt((()=>Uc(e,t,o,n,s))),rtl:Qt((()=>A.some(Uc(e,n,s,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>jc.rtl(Ve(e.endContainer),e.endOffset,Ve(e.startContainer),e.startOffset))).getOrThunk((()=>Gc(0,jc.ltr,o))):Gc(0,jc.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},rtl:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(n.dom,s),r.setEnd(t.dom,o),r}});jc.ltr,jc.rtl;const qc=(e,t,o)=>U(((e,t)=>{const o=p(t)?t:T;let n=e.dom;const s=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=Ve(e);if(s.push(t),!0===o(t))break;n=e}return s})(e,o),t),Xc=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Je(o)?[]:H(o.querySelectorAll(e),Ve)})(t,e),Kc=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return A.some(Nc(Ve(t.startContainer),t.startOffset,Ve(o.endContainer),o.endOffset))}return A.none()},Yc=e=>{if(null===e.anchorNode||null===e.focusNode)return Kc(e);{const t=Ve(e.anchorNode),o=Ve(e.focusNode);return((e,t,o,n)=>{const s=((e,t,o,n)=>{const s=et(e).dom.createRange();return s.setStart(e.dom,t),s.setEnd(o.dom,n),s})(e,t,o,n),r=Ze(e,o)&&t===n;return s.collapsed&&!r})(t,e.anchorOffset,o,e.focusOffset)?A.some(Nc(t,e.anchorOffset,o,e.focusOffset)):Kc(e)}},Jc=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?A.some(o).map(Wc):A.none()})($c(e,t)),Zc=((e,t)=>{const o=t=>e(t)?A.from(t.dom.nodeValue):A.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})($e),Qc=(e,t)=>({element:e,offset:t}),ed=(e,t)=>$e(e)?Qc(e,t):((e,t)=>{const o=it(e);if(0===o.length)return Qc(e,t);if(t<o.length)return Qc(o[t],0);{const e=o[o.length-1],t=$e(e)?(e=>Zc.get(e))(e).length:it(e).length;return Qc(e,t)}})(e,t),td=e=>void 0!==e.foffset,od=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>A.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(Yc))(e)))().map((e=>{if(td(e)){const t=ed(e.start,e.soffset),o=ed(e.finish,e.foffset);return Lc.range(t.element,t.offset,o.element,o.offset)}return e}));var nd=[us("getSelection"),os("root"),us("bubble"),wc(),ys("overrides",{}),ys("showAbove",!1),Ri("placement",((e,t,o)=>{const n=nt(t.root).dom,s=Bc(e,0,t),r=od(n,t).bind((e=>{if(td(e)){const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?A.some(t).map(Wc):A.none()})($c(e,t)))(n,Lc.exactFromRange(e)).orThunk((()=>{const t=Ne("\ufeff");Ro(e.start,t);const o=Jc(n,Lc.exact(t,0,t,1));return Po(t),o}));return t.bind((e=>Fc(e.left,e.top,e.width,e.height)))}{const t=ce(e,(e=>e.dom.getBoundingClientRect())),o={left:Math.min(t.firstCell.left,t.lastCell.left),right:Math.max(t.firstCell.right,t.lastCell.right),top:Math.min(t.firstCell.top,t.lastCell.top),bottom:Math.max(t.firstCell.bottom,t.lastCell.bottom)};return Fc(o.left,o.top,o.right-o.left,o.bottom-o.top)}})),a=od(n,t).bind((e=>td(e)?Ge(e.start)?A.some(e.start):rt(e.start):A.some(e.firstCell))).getOr(e.element);return Ic(r,s,t,o,a)}))];const sd="link-layout",rd=e=>e.x+e.width,ad=(e,t)=>e.x-t.width,id=(e,t)=>e.y-t.height+e.height,ld=e=>e.y,cd=(e,t,o)=>zi(rd(e),ld(e),o.southeast(),Li(),"southeast",Yi(e,{left:0,top:2}),sd),dd=(e,t,o)=>zi(ad(e,t),ld(e),o.southwest(),Pi(),"southwest",Yi(e,{right:1,top:2}),sd),ud=(e,t,o)=>zi(rd(e),id(e,t),o.northeast(),Ui(),"northeast",Yi(e,{left:0,bottom:3}),sd),md=(e,t,o)=>zi(ad(e,t),id(e,t),o.northwest(),Wi(),"northwest",Yi(e,{right:1,bottom:3}),sd),gd=()=>[cd,dd,ud,md],pd=()=>[dd,cd,md,ud];var hd=[os("item"),wc(),ys("overrides",{}),Ri("placement",((e,t,o)=>{const n=Ul(o,t.item.element),s=Sc(e.element,t,gd(),pd(),gd(),pd(),A.none());return A.some(hc({anchorBox:n,bubble:pc(),overrides:t.overrides,layouts:s}))}))],fd=Jn("type",{selection:nd,node:Rc,hotspot:kc,submenu:hd,makeshift:Cc});const bd=[ds("classes",Hn),ks("mode","all",["all","layout","placement"])],vd=[ys("useFixed",T),us("getBounds")],yd=[ns("anchor",fd),vs("transition",bd)],xd=(e,t,o,n,s,r)=>{const a=Kn("placement.info",Dn(yd),s),i=a.anchor,l=n.element,c=o.get(n.uid);Nl((()=>{Dt(l,"position","fixed");const s=Nt(l,"visibility");Dt(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return $l(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=Xt(e.element),o=e.element.dom.getBoundingClientRect();return Gl(t.left,t.top,o.width,o.height)})(e);i.placement(e,i,d).each((e=>{const s=r.orThunk((()=>t.getBounds.map(_))),i=((e,t,o,n,s,r)=>((e,t,o,n,s,r,a,i)=>{const l=dc(a,"maxHeightFunction",lc()),c=dc(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:Wl(u,r),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:s,transition:i};return uc(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(t.anchorBox,e),n.element,t.bubble,t.layouts,s,o,t.overrides,r))(d,e,s,n,c,a.transition);o.set(n.uid,i)})),s.fold((()=>{Ht(l,"visibility")}),(e=>{Dt(l,"visibility",e)})),Nt(l,"left").isNone()&&Nt(l,"top").isNone()&&Nt(l,"right").isNone()&&Nt(l,"bottom").isNone()&&xe(Nt(l,"position"),"fixed")&&Ht(l,"position")}),l)};var wd=Object.freeze({__proto__:null,position:(e,t,o,n,s)=>{const r=A.none();xd(e,t,o,n,s,r)},positionWithinBounds:xd,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const s=n.element;L(["position","left","right","top","bottom"],(e=>Ht(s,e))),(e=>{Et(e,ql)})(s),o.clear(n.uid)}});const Sd=Ol({fields:vd,name:"positioning",active:Ml,apis:wd,state:Object.freeze({__proto__:null,init:()=>{let e={};return _a({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>be(e,t)})}})}),kd=e=>e.getSystem().isConnected(),Cd=e=>{Fr(e,kr());const t=e.components();L(t,Cd)},Od=e=>{const t=e.components();L(t,Od),Fr(e,Sr())},_d=(e,t)=>{e.getSystem().addToWorld(t),yt(e.element)&&Od(t)},Td=e=>{Cd(e),e.getSystem().removeFromWorld(e)},Ed=(e,t)=>{zo(e.element,t.element)},Ad=(e,t)=>{Md(e,t,zo)},Md=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),yt(e.element)&&Od(t),e.syncComponents()},Dd=e=>{Cd(e),Po(e.element),e.getSystem().removeFromWorld(e)},Bd=e=>{const t=st(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));Dd(e),t.each((e=>{e.syncComponents()}))},Fd=e=>{const t=e.components();L(t,Dd),Lo(e.element),e.syncComponents()},Id=(e,t)=>{Nd(e,t,zo)},Rd=(e,t)=>{Nd(e,t,No)},Nd=(e,t,o)=>{o(e,t.element);const n=it(t.element);L(n,(e=>{t.getByDom(e).each(Od)}))},Vd=e=>{const t=it(e.element);L(t,(t=>{e.getByDom(t).each(Cd)})),Po(e.element)},zd=(e,t,o,n)=>{o.get().each((t=>{Fd(e)}));const s=t.getAttachPoint(e);Ad(s,e);const r=e.getSystem().build(n);return Ad(e,r),o.set(r),r},Hd=(e,t,o,n)=>{const s=zd(e,t,o,n);return t.onOpen(e,s),s},Ld=(e,t,o)=>{o.get().each((n=>{Fd(e),Bd(e),t.onClose(e,n),o.clear()}))},Pd=(e,t,o)=>o.isOpen(),Ud=(e,t,o)=>{const n=t.getAttachPoint(e);Dt(e.element,"position",Sd.getMode(n)),((e,t,o,n)=>{Nt(e.element,t).fold((()=>{Et(e.element,o)}),(t=>{kt(e.element,o,t)})),Dt(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},Wd=(e,t,o)=>{(e=>N(["top","left","right","bottom"],(t=>Nt(e,t).isSome())))(e.element)||Ht(e.element,"position"),((e,t,o)=>{_t(e.element,o).fold((()=>Ht(e.element,t)),(o=>Dt(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var jd=Object.freeze({__proto__:null,cloak:Ud,decloak:Wd,open:Hd,openWhileCloaked:(e,t,o,n,s)=>{Ud(e,t),Hd(e,t,o,n),s(),Wd(e,t)},close:Ld,isOpen:Pd,isPartOf:(e,t,o,n)=>Pd(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>zd(e,t,o,n)))}),Gd=Object.freeze({__proto__:null,events:(e,t)=>Hr([Ur(hr(),((o,n)=>{Ld(o,e,t)}))])}),$d=[Di("onOpen"),Di("onClose"),os("isPartOf"),os("getAttachPoint"),ys("cloakVisibilityAttr","data-precloak-visibility")],qd=Object.freeze({__proto__:null,init:()=>{const e=Ql(),t=x("not-implemented");return _a({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const Xd=Ol({fields:$d,name:"sandboxing",active:Gd,apis:jd,state:qd}),Kd=x("dismiss.popups"),Yd=x("reposition.popups"),Jd=x("mouse.released"),Zd=Mn([ys("isExtraPart",T),vs("fireEventInstead",[ys("event",Cr())])]),Qd=e=>{const t=Kn("Dismissal",Zd,e);return{[Kd()]:{schema:Mn([os("target")]),onReceive:(e,o)=>{Xd.isOpen(e)&&(Xd.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>Xd.close(e)),(t=>Fr(e,t.event))))}}}},eu=Mn([vs("fireEventInstead",[ys("event",Or())]),is("doReposition")]),tu=e=>{const t=Kn("Reposition",eu,e);return{[Yd()]:{onReceive:e=>{Xd.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>Fr(e,t.event)))}}}},ou=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},nu=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var su=Object.freeze({__proto__:null,onLoad:ou,onUnload:nu,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),ru=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[Yr(((o,n)=>{ou(o,e,t)})),Jr(((o,n)=>{nu(o,e,t)}))]:[xl(e,t,ou)];return Hr(o)}});const au=()=>{const e=Es(null);return _a({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},iu=()=>{const e=Es({}),t=Es({});return _a({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>be(e.get(),o).orThunk((()=>be(t.get(),o))),update:o=>{const n=e.get(),s=t.get(),r={},a={};L(o,(e=>{r[e.value]=e,be(e,"meta").each((t=>{be(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...r}),t.set({...s,...a})},clear:()=>{e.set({}),t.set({})}})};var lu=Object.freeze({__proto__:null,memory:au,dataset:iu,manual:()=>_a({readState:b}),init:e=>e.store.manager.state(e)});const cu=(e,t,o,n)=>{const s=t.store;o.update([n]),s.setValue(e,n),t.onSetValue(e,n)};var du=[us("initialValue"),os("getFallbackEntry"),os("getDataKey"),os("setValue"),Ri("manager",{setValue:cu,getValue:(e,t,o)=>{const n=t.store,s=n.getDataKey(e);return o.lookup(s).getOrThunk((()=>n.getFallbackEntry(s)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{cu(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:iu})],uu=[os("getValue"),ys("setValue",b),us("initialValue"),Ri("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:Oa.init})],mu=[us("initialValue"),Ri("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:au})],gu=[xs("store",{mode:"memory"},Jn("mode",{memory:mu,manual:uu,dataset:du})),Di("onSetValue"),ys("resetOnDom",!1)];const pu=Ol({fields:gu,name:"representing",active:ru,apis:su,extra:{setValueFrom:(e,t)=>{const o=pu.getValue(t);pu.setValue(e,o)}},state:lu}),hu=(e,t)=>Ts(e,{},H(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,Qn(o,o,{tag:"option",process:{}},Cn((e=>un("The field: "+o+" is forbidden. "+n))));var o,n})).concat([es("dump",w)])),fu=e=>e.dump,bu=(e,t)=>({...kl(t),...e.dump}),vu=hu,yu=bu,xu="placeholder",wu=As([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Su=e=>ve(e,"uiType"),ku=(e,t,o,n)=>((e,t,o,n)=>Su(o)&&o.uiType===xu?((e,t,o,n)=>e.exists((e=>e!==o.owner))?wu.single(!0,x(o)):be(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+ae(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):wu.single(!1,x(o)))(e,0,o,n).fold(((s,r)=>{const a=Su(o)?r(t,o.config,o.validated):r(t),i=be(a,"components").getOr([]),l=X(i,(o=>ku(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(Su(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(w)(e)}return n(t)})),Cu=wu.single,Ou=wu.multiple,_u=x(xu),Tu=As([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Eu=ys("factory",{sketch:w}),Au=ys("schema",[]),Mu=os("name"),Du=Qn("pname","pname",vn((e=>"<alloy."+la(e.name)+">")),Nn()),Bu=es("schema",(()=>[us("preprocess")])),Fu=ys("defaults",x({})),Iu=ys("overrides",x({})),Ru=Dn([Eu,Au,Mu,Du,Fu,Iu]),Nu=Dn([Eu,Au,Mu,Fu,Iu]),Vu=Dn([Eu,Au,Mu,Du,Fu,Iu]),zu=Dn([Eu,Bu,Mu,os("unit"),Du,Fu,Iu]),Hu=e=>e.fold(A.some,A.none,A.some,A.some),Lu=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},Pu=(e,t)=>o=>{const n=Kn("Converting part type",t,o);return e(n)},Uu=Pu(Tu.required,Ru),Wu=Pu(Tu.external,Nu),ju=Pu(Tu.optional,Vu),Gu=Pu(Tu.group,zu),$u=x("entirety");var qu=Object.freeze({__proto__:null,required:Uu,external:Wu,optional:ju,group:Gu,asNamedPart:Hu,name:Lu,asCommon:e=>e.fold(w,w,w,w),original:$u});const Xu=(e,t,o,n)=>fn(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),Ku=(e,t)=>{const o={};return L(t,(t=>{Hu(t).each((t=>{const n=Yu(e,t.pname);o[t.name]=o=>{const s=Kn("Part: "+t.name+" in "+e,Dn(t.schema),o);return{...n,config:o,validated:s}}}))})),o},Yu=(e,t)=>({uiType:_u(),owner:e,name:t}),Ju=(e,t,o)=>({uiType:_u(),owner:e,name:t,config:o,validated:{}}),Zu=e=>X(e,(e=>e.fold(A.none,A.some,A.none,A.none).map((e=>ls(e.name,e.schema.concat([Ni($u())])))).toArray())),Qu=e=>H(e,Lu),em=(e,t,o)=>((e,t,o)=>{const n={},s={};return L(o,(e=>{e.fold((e=>{n[e.pname]=Cu(!0,((t,o,n)=>e.factory.sketch(Xu(t,e,o,n))))}),(e=>{const o=t.parts[e.name];s[e.name]=x(e.factory.sketch(Xu(t,e,o[$u()]),o))}),(e=>{n[e.pname]=Cu(!1,((t,o,n)=>e.factory.sketch(Xu(t,e,o,n))))}),(e=>{n[e.pname]=Ou(!0,((t,o,n)=>{const s=t[e.name];return H(s,(o=>e.factory.sketch(fn(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:x(n),externals:x(s)}})(0,t,o),tm=(e,t,o)=>((e,t,o,n)=>{const s=ce(n,((e,t)=>((e,t)=>{let o=!1;return{name:x(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),r=((e,t,o,n)=>X(o,(o=>ku(e,t,o,n))))(e,t,o,s);return le(s,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),r})(A.some(e),t,t.components,o),om=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},nm=(e,t,o)=>om(e,t,o).getOrDie("Could not find part: "+o),sm=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return L(o,(e=>{n[e]=x(r.getByUid(s[e]))})),n},rm=(e,t)=>{const o=e.getSystem();return ce(t.partUids,((e,t)=>x(o.getByUid(e))))},am=e=>ae(e.partUids),im=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return L(o,(e=>{n[e]=x(r.getByUid(s[e]).getOrDie())})),n},lm=(e,t)=>{const o=Qu(t);return Ds(H(o,(t=>({key:t,value:e+"-"+t}))))},cm=e=>Qn("partUids","partUids",xn((t=>lm(t.uid,e))),Nn());var dm=Object.freeze({__proto__:null,generate:Ku,generateOne:Ju,schemas:Zu,names:Qu,substitutes:em,components:tm,defaultUids:lm,defaultUidsSchema:cm,getAllParts:rm,getAllPartNames:am,getPart:om,getPartOrDie:nm,getParts:sm,getPartsOrDie:im});const um=(e,t,o,n,s)=>{const r=((e,t)=>(e.length>0?[ls("parts",e)]:[]).concat([os("uid"),ys("dom",{}),ys("components",[]),Ni("originalSpec"),ys("debug.sketcher",{})]).concat(t))(n,s);return Kn(e+" [SpecSchema]",Mn(r.concat(t)),o)},mm=(e,t,o,n,s)=>{const r=gm(s),a=Zu(o),i=cm(o),l=um(e,t,r,a,[i]),c=em(0,l,o);return n(l,tm(e,l,c.internals()),r,c.externals())},gm=e=>(e=>ve(e,"uid"))(e)?e:{...e,uid:ha("uid")},pm=Mn([os("name"),os("factory"),os("configFields"),ys("apis",{}),ys("extraApis",{})]),hm=Mn([os("name"),os("factory"),os("configFields"),os("partFields"),ys("apis",{}),ys("extraApis",{})]),fm=e=>{const t=Kn("Sketcher for "+e.name,pm,e),o=ce(t.apis,Ca),n=ce(t.extraApis,((e,t)=>xa(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const s=gm(n);return o(um(e,t,s,[],[]),s)})(t.name,t.configFields,t.factory,e),...o,...n}},bm=e=>{const t=Kn("Sketcher for "+e.name,hm,e),o=Ku(t.name,t.partFields),n=ce(t.apis,Ca),s=ce(t.extraApis,((e,t)=>xa(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>mm(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...s}},vm=e=>Ke("input")(e)&&"radio"!==Ot(e,"type")||Ke("textarea")(e);var ym=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const xm=[os("find")],wm=Ol({fields:xm,name:"composing",apis:ym}),Sm=["input","button","textarea","select"],km=(e,t,o)=>{(t.disabled()?Am:Mm)(e,t)},Cm=(e,t)=>!0===t.useNative&&R(Sm,Ue(e.element)),Om=e=>{kt(e.element,"disabled","disabled")},_m=e=>{Et(e.element,"disabled")},Tm=e=>{kt(e.element,"aria-disabled","true")},Em=e=>{kt(e.element,"aria-disabled","false")},Am=(e,t,o)=>{t.disableClass.each((t=>{La(e.element,t)})),(Cm(e,t)?Om:Tm)(e),t.onDisabled(e)},Mm=(e,t,o)=>{t.disableClass.each((t=>{Pa(e.element,t)})),(Cm(e,t)?_m:Em)(e),t.onEnabled(e)},Dm=(e,t)=>Cm(e,t)?(e=>Tt(e.element,"disabled"))(e):(e=>"true"===Ot(e.element,"aria-disabled"))(e);var Bm=Object.freeze({__proto__:null,enable:Mm,disable:Am,isDisabled:Dm,onLoad:km,set:(e,t,o,n)=>{(n?Am:Mm)(e,t)}}),Fm=Object.freeze({__proto__:null,exhibit:(e,t)=>Ea({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>Hr([Lr(ur(),((t,o)=>Dm(t,e))),xl(e,t,km)])}),Im=[Os("disabled",T),ys("useNative",!0),us("disableClass"),Di("onDisabled"),Di("onEnabled")];const Rm=Ol({fields:Im,name:"disabling",active:Fm,apis:Bm}),Nm=(e,t,o,n)=>{const s=Xc(e.element,"."+t.highlightClass);L(s,(o=>{N(n,(e=>Ze(e.element,o)))||(Pa(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),Fr(o,Br())})))}))},Vm=(e,t,o,n)=>{Nm(e,t,0,[n]),zm(e,t,o,n)||(La(n.element,t.highlightClass),t.onHighlight(e,n),Fr(n,Dr()))},zm=(e,t,o,n)=>Ua(n.element,t.highlightClass),Hm=(e,t,o)=>pi(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),Lm=(e,t,o)=>{const n=Xc(e.element,"."+t.itemClass);return(n.length>0?A.some(n[n.length-1]):A.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},Pm=(e,t,o,n)=>{const s=Xc(e.element,"."+t.itemClass);return $(s,(e=>Ua(e,t.highlightClass))).bind((t=>{const o=Xi(t,n,0,s.length-1);return e.getSystem().getByDom(s[o]).toOptional()}))},Um=(e,t,o)=>{const n=Xc(e.element,"."+t.itemClass);return we(H(n,(t=>e.getSystem().getByDom(t).toOptional())))};var Wm=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>Nm(e,t,0,[]),dehighlight:(e,t,o,n)=>{zm(e,t,o,n)&&(Pa(n.element,t.highlightClass),t.onDehighlight(e,n),Fr(n,Br()))},highlight:Vm,highlightFirst:(e,t,o)=>{Hm(e,t).each((n=>{Vm(e,t,o,n)}))},highlightLast:(e,t,o)=>{Lm(e,t).each((n=>{Vm(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const s=Xc(e.element,"."+t.itemClass);return A.from(s[n]).fold((()=>sn.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{Vm(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const s=Um(e,t);G(s,n).each((n=>{Vm(e,t,o,n)}))},isHighlighted:zm,getHighlighted:(e,t,o)=>pi(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:Hm,getLast:Lm,getPrevious:(e,t,o)=>Pm(e,t,0,-1),getNext:(e,t,o)=>Pm(e,t,0,1),getCandidates:Um}),jm=[os("highlightClass"),os("itemClass"),Di("onHighlight"),Di("onDehighlight")];const Gm=Ol({fields:jm,name:"highlighting",apis:Wm}),$m=[8],qm=[9],Xm=[13],Km=[27],Ym=[32],Jm=[37],Zm=[38],Qm=[39],eg=[40],tg=(e,t,o)=>{const n=Y(e.slice(0,t)),s=Y(e.slice(t+1));return G(n.concat(s),o)},og=(e,t,o)=>{const n=Y(e.slice(0,t));return G(n,o)},ng=(e,t,o)=>{const n=e.slice(0,t),s=e.slice(t+1);return G(s.concat(n),o)},sg=(e,t,o)=>{const n=e.slice(t+1);return G(n,o)},rg=e=>t=>{const o=t.raw;return R(e,o.which)},ag=e=>t=>K(e,(e=>e(t))),ig=e=>!0===e.raw.shiftKey,lg=e=>!0===e.raw.ctrlKey,cg=C(ig),dg=(e,t)=>({matches:e,classification:t}),ug=(e,t,o)=>{t.exists((e=>o.exists((t=>Ze(t,e)))))||Ir(e,_r(),{prevFocus:t,newFocus:o})},mg=()=>{const e=e=>Rl(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const s=e(t);ug(t,n,s)}}},gg=()=>{const e=e=>Gm.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Gm.highlight(t,e)}));const s=e(t);ug(t,n,s)}}};var pg;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}(pg||(pg={}));const hg=(e,t,o,n,s)=>{const r=(e,t,o,n,s)=>{return(r=o(e,t,n,s),a=t.event,G(r,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,s)));var r,a},a={schema:()=>e.concat([ys("focusManager",mg()),xs("focusInside","onFocus",Gn((e=>R(["onFocus","onEnterOrSpace","onApi"],e)?sn.value(e):sn.error("Invalid value for focusInside")))),Ri("handler",a),Ri("state",t),Ri("sendFocusIn",s)]),processKey:r,toEvents:(e,t)=>{const a=e.focusInside!==pg.OnFocusMode?A.none():s(e).map((o=>Ur(ir(),((n,s)=>{o(n,e,t),s.stop()})))),i=[Ur(Ys(),((n,a)=>{r(n,a,o,e,t).fold((()=>{((o,n)=>{const r=rg(Ym.concat(Xm))(n.event);e.focusInside===pg.OnEnterOrSpaceMode&&r&&Rs(o,n)&&s(e).each((s=>{s(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),Ur(Js(),((o,s)=>{r(o,s,n,e,t).each((e=>{s.stop()}))}))];return Hr(a.toArray().concat(i))}};return a},fg=e=>{const t=[us("onEscape"),us("onEnter"),ys("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),ys("firstTabstop",0),ys("useTabstopAt",E),us("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>hi(t,e))).getOr(t);return Wt(o)>0},n=(e,t,n)=>{((e,t)=>{const n=Xc(e.element,t.selector),s=U(n,(e=>o(t,e)));return A.from(s[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},s=(e,t,n,s)=>{const r=Xc(e.element,n.selector);return((e,t)=>t.focusManager.get(e).bind((e=>hi(e,t.selector))))(e,n).bind((t=>$(r,k(Ze,t)).bind((t=>((e,t,n,s,r)=>r(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(s,e))).fold((()=>s.cyclic?A.some(!0):A.none()),(t=>(s.focusManager.set(e,t),A.some(!0)))))(e,r,t,n,s)))))},r=x([dg(ag([ig,rg(qm)]),((e,t,o)=>{const n=o.cyclic?tg:og;return s(e,0,o,n)})),dg(rg(qm),((e,t,o)=>{const n=o.cyclic?ng:sg;return s(e,0,o,n)})),dg(ag([cg,rg(Xm)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),a=x([dg(rg(Km),((e,t,o)=>o.onEscape.bind((o=>o(e,t)))))]);return hg(t,Oa.init,r,a,(()=>A.some(n)))};var bg=fg(es("cyclic",T)),vg=fg(es("cyclic",E));const yg=(e,t,o)=>vm(o)&&rg(Ym)(t.event)?A.none():((e,t,o)=>(Nr(e,o,ur()),A.some(!0)))(e,0,o),xg=(e,t)=>A.some(!0),wg=[ys("execute",yg),ys("useSpace",!1),ys("useEnter",!0),ys("useControlEnter",!1),ys("useDown",!1)],Sg=(e,t,o)=>o.execute(e,t,e.element);var kg=hg(wg,Oa.init,((e,t,o,n)=>{const s=o.useSpace&&!vm(e.element)?Ym:[],r=o.useEnter?Xm:[],a=o.useDown?eg:[],i=s.concat(r).concat(a);return[dg(rg(i),Sg)].concat(o.useControlEnter?[dg(ag([lg,rg(Xm)]),Sg)]:[])}),((e,t,o,n)=>o.useSpace&&!vm(e.element)?[dg(rg(Ym),xg)]:[]),(()=>A.none()));const Cg=()=>{const e=Ql();return _a({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var Og=Object.freeze({__proto__:null,flatgrid:Cg,init:e=>e.state(e)});const _g=e=>(t,o,n,s)=>{const r=e(t.element);return Mg(r,t,o,n,s)},Tg=(e,t)=>{const o=fc(e,t);return _g(o)},Eg=(e,t)=>{const o=fc(t,e);return _g(o)},Ag=e=>(t,o,n,s)=>Mg(e,t,o,n,s),Mg=(e,t,o,n,s)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,s))).map((e=>(n.focusManager.set(t,e),!0))),Dg=Ag,Bg=Ag,Fg=Ag,Ig=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),Rg=(e,t,o)=>{const n=Xc(e,o);return((e,o)=>$(e,(e=>Ze(e,t))).map((t=>({index:t,candidates:e}))))(U(n,Ig))},Ng=(e,t)=>$(e,(e=>Ze(t,e))),Vg=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?A.some(e[n]):A.none()})),zg=(e,t,o,n,s)=>Vg(e,t,n,((t,r)=>{const a=t===o-1?e.length-t*n:n,i=Xi(r,s,0,a-1);return A.some({row:t,column:i})})),Hg=(e,t,o,n,s)=>Vg(e,t,n,((t,r)=>{const a=Xi(t,s,0,o-1),i=a===o-1?e.length-a*n:n,l=Ki(r,0,i-1);return A.some({row:a,column:l})})),Lg=[os("selector"),ys("execute",yg),Bi("onEscape"),ys("captureTab",!1),Vi()],Pg=(e,t,o)=>{pi(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},Ug=e=>(t,o,n,s)=>Rg(t,o,n.selector).bind((t=>e(t.candidates,t.index,s.getNumRows().getOr(n.initSize.numRows),s.getNumColumns().getOr(n.initSize.numColumns)))),Wg=(e,t,o)=>o.captureTab?A.some(!0):A.none(),jg=Ug(((e,t,o,n)=>zg(e,t,o,n,-1))),Gg=Ug(((e,t,o,n)=>zg(e,t,o,n,1))),$g=Ug(((e,t,o,n)=>Hg(e,t,o,n,-1))),qg=Ug(((e,t,o,n)=>Hg(e,t,o,n,1))),Xg=x([dg(rg(Jm),Tg(jg,Gg)),dg(rg(Qm),Eg(jg,Gg)),dg(rg(Zm),Dg($g)),dg(rg(eg),Bg(qg)),dg(ag([ig,rg(qm)]),Wg),dg(ag([cg,rg(qm)]),Wg),dg(rg(Ym.concat(Xm)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>hi(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),Kg=x([dg(rg(Km),((e,t,o)=>o.onEscape(e,t))),dg(rg(Ym),xg)]);var Yg=hg(Lg,Cg,Xg,Kg,(()=>A.some(Pg)));const Jg=(e,t,o,n,s)=>{const r=(e,t,o)=>s(e,t,n,0,o.length-1,o[t],(t=>{return n=o[t],"button"===Ue(n)&&"disabled"===Ot(n,"disabled")?r(e,t,o):A.from(o[t]);var n}));return Rg(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return r(t,t,o)}))},Zg=(e,t,o,n)=>Jg(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Ki(t+o,n,s);return i===e?A.from(r):a(i)})),Qg=(e,t,o,n)=>Jg(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Xi(t,o,n,s);return i===e?A.none():a(i)})),ep=[os("selector"),ys("getInitial",A.none),ys("execute",yg),Bi("onEscape"),ys("executeOnMove",!1),ys("allowVertical",!0),ys("allowHorizontal",!0),ys("cycles",!0)],tp=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>hi(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),op=(e,t,o)=>{t.getInitial(e).orThunk((()=>pi(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},np=(e,t,o)=>(o.cycles?Qg:Zg)(e,o.selector,t,-1),sp=(e,t,o)=>(o.cycles?Qg:Zg)(e,o.selector,t,1),rp=e=>(t,o,n,s)=>e(t,o,n,s).bind((()=>n.executeOnMove?tp(t,o,n):A.some(!0))),ap=x([dg(rg(Ym),xg),dg(rg(Km),((e,t,o)=>o.onEscape(e,t)))]);var ip=hg(ep,Oa.init,((e,t,o,n)=>{const s=[...o.allowHorizontal?Jm:[]].concat(o.allowVertical?Zm:[]),r=[...o.allowHorizontal?Qm:[]].concat(o.allowVertical?eg:[]);return[dg(rg(s),rp(Tg(np,sp))),dg(rg(r),rp(Eg(np,sp))),dg(rg(Xm),tp),dg(rg(Ym),tp)]}),ap,(()=>A.some(op)));const lp=(e,t,o)=>A.from(e[t]).bind((e=>A.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),cp=(e,t,o,n)=>{const s=e[t].length,r=Xi(o,n,0,s-1);return lp(e,t,r)},dp=(e,t,o,n)=>{const s=Xi(o,n,0,e.length-1),r=e[s].length,a=Ki(t,0,r-1);return lp(e,s,a)},up=(e,t,o,n)=>{const s=e[t].length,r=Ki(o+n,0,s-1);return lp(e,t,r)},mp=(e,t,o,n)=>{const s=Ki(o+n,0,e.length-1),r=e[s].length,a=Ki(t,0,r-1);return lp(e,s,a)},gp=[ls("selectors",[os("row"),os("cell")]),ys("cycles",!0),ys("previousSelector",A.none),ys("execute",yg)],pp=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return pi(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},hp=(e,t)=>(o,n,s)=>{const r=s.cycles?e:t;return hi(n,s.selectors.row).bind((e=>{const t=Xc(e,s.selectors.cell);return Ng(t,n).bind((t=>{const n=Xc(o,s.selectors.row);return Ng(n,e).bind((e=>{const o=((e,t)=>H(e,(e=>Xc(e,t.selectors.cell))))(n,s);return r(o,e,t).map((e=>e.cell))}))}))}))},fp=hp(((e,t,o)=>cp(e,t,o,-1)),((e,t,o)=>up(e,t,o,-1))),bp=hp(((e,t,o)=>cp(e,t,o,1)),((e,t,o)=>up(e,t,o,1))),vp=hp(((e,t,o)=>dp(e,o,t,-1)),((e,t,o)=>mp(e,o,t,-1))),yp=hp(((e,t,o)=>dp(e,o,t,1)),((e,t,o)=>mp(e,o,t,1))),xp=x([dg(rg(Jm),Tg(fp,bp)),dg(rg(Qm),Eg(fp,bp)),dg(rg(Zm),Dg(vp)),dg(rg(eg),Bg(yp)),dg(rg(Ym.concat(Xm)),((e,t,o)=>Rl(e.element).bind((n=>o.execute(e,t,n)))))]),wp=x([dg(rg(Ym),xg)]);var Sp=hg(gp,Oa.init,xp,wp,(()=>A.some(pp)));const kp=[os("selector"),ys("execute",yg),ys("moveOnTab",!1)],Cp=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),Op=(e,t,o)=>{pi(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},_p=(e,t,o)=>Qg(e,o.selector,t,-1),Tp=(e,t,o)=>Qg(e,o.selector,t,1),Ep=x([dg(rg(Zm),Fg(_p)),dg(rg(eg),Fg(Tp)),dg(ag([ig,rg(qm)]),((e,t,o,n)=>o.moveOnTab?Fg(_p)(e,t,o,n):A.none())),dg(ag([cg,rg(qm)]),((e,t,o,n)=>o.moveOnTab?Fg(Tp)(e,t,o,n):A.none())),dg(rg(Xm),Cp),dg(rg(Ym),Cp)]),Ap=x([dg(rg(Ym),xg)]);var Mp=hg(kp,Oa.init,Ep,Ap,(()=>A.some(Op)));const Dp=[Bi("onSpace"),Bi("onEnter"),Bi("onShiftEnter"),Bi("onLeft"),Bi("onRight"),Bi("onTab"),Bi("onShiftTab"),Bi("onUp"),Bi("onDown"),Bi("onEscape"),ys("stopSpaceKeyup",!1),us("focusIn")];var Bp=hg(Dp,Oa.init,((e,t,o)=>[dg(rg(Ym),o.onSpace),dg(ag([cg,rg(Xm)]),o.onEnter),dg(ag([ig,rg(Xm)]),o.onShiftEnter),dg(ag([ig,rg(qm)]),o.onShiftTab),dg(ag([cg,rg(qm)]),o.onTab),dg(rg(Zm),o.onUp),dg(rg(eg),o.onDown),dg(rg(Jm),o.onLeft),dg(rg(Qm),o.onRight),dg(rg(Ym),o.onSpace)]),((e,t,o)=>[...o.stopSpaceKeyup?[dg(rg(Ym),xg)]:[],dg(rg(Km),o.onEscape)]),(e=>e.focusIn));const Fp=bg.schema(),Ip=vg.schema(),Rp=ip.schema(),Np=Yg.schema(),Vp=Sp.schema(),zp=kg.schema(),Hp=Mp.schema(),Lp=Bp.schema(),Pp=Tl({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:Fp,cyclic:Ip,flow:Rp,flatgrid:Np,matrix:Vp,execution:zp,menu:Hp,special:Lp}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,s)=>{(e=>ye(e,"setGridSize"))(o)?o.setGridSize(n,s):console.error("Layout does not support setGridSize")}},state:Og}),Up=(e,t)=>{Nl((()=>{((e,t,o)=>{const n=e.components();(e=>{L(e.components(),(e=>Po(e.element))),Lo(e.element),e.syncComponents()})(e);const s=o(t),r=J(n,s);L(r,(t=>{Cd(t),e.getSystem().removeFromWorld(t)})),L(s,(t=>{kd(t)?Ed(e,t):(e.getSystem().addToWorld(t),Ed(e,t),yt(e.element)&&Od(t))})),e.syncComponents()})(e,t,(()=>H(t,e.getSystem().build)))}),e.element)},Wp=(e,t)=>{Nl((()=>{((o,n,s)=>{const r=o.components(),a=X(n,(e=>ka(e).toArray()));L(r,(e=>{R(a,e)||Td(e)}));const i=((e,t,o)=>Ka(e,t,((t,n)=>Ya(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),l=J(r,i);L(l,(e=>{kd(e)&&Td(e)})),L(i,(e=>{kd(e)||_d(o,e)})),o.syncComponents()})(e,t)}),e.element)},jp=(e,t,o,n)=>{Td(t);const s=Ya(e.element,o,n,e.getSystem().buildOrPatch);_d(e,s),e.syncComponents()},Gp=(e,t,o)=>{const n=e.getSystem().build(o);Md(e,n,t)},$p=(e,t,o,n)=>{Bd(t),Gp(e,((e,t)=>((e,t,o)=>{lt(e,o).fold((()=>{zo(e,t)}),(e=>{Ro(e,t)}))})(e,t,o)),n)},qp=(e,t)=>e.components(),Xp=(e,t,o,n,s)=>{const r=qp(e);return A.from(r[n]).map((o=>(s.fold((()=>Bd(o)),(s=>{(t.reuseDom?jp:$p)(e,o,n,s)})),o)))};var Kp=Object.freeze({__proto__:null,append:(e,t,o,n)=>{Gp(e,zo,n)},prepend:(e,t,o,n)=>{Gp(e,Vo,n)},remove:(e,t,o,n)=>{const s=qp(e),r=G(s,(e=>Ze(n.element,e.element)));r.each(Bd)},replaceAt:Xp,replaceBy:(e,t,o,n,s)=>{const r=qp(e);return $(r,n).bind((o=>Xp(e,t,0,o,s)))},set:(e,t,o,n)=>(t.reuseDom?Wp:Up)(e,n),contents:qp});const Yp=Ol({fields:[Cs("reuseDom",!0)],name:"replacing",apis:Kp}),Jp=(e,t)=>{const o=((e,t)=>{const o=Hr(t);return Ol({fields:[os("enabled")],name:e,active:{events:x(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:x({}),initialConfig:{},state:Oa}}},Zp=(e,t)=>{t.ignore||(Dl(e.element),t.onFocus(e))};var Qp=Object.freeze({__proto__:null,focus:Zp,blur:(e,t)=>{t.ignore||Bl(e.element)},isFocused:e=>Fl(e.element)}),eh=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return Ea(o)},events:e=>Hr([Ur(ir(),((t,o)=>{Zp(t,e),o.stop()}))].concat(e.stopMousedown?[Ur(Ws(),((e,t)=>{t.event.prevent()}))]:[]))}),th=[Di("onFocus"),ys("stopMousedown",!1),ys("ignore",!1)];const oh=Ol({fields:th,name:"focusing",active:eh,apis:Qp}),nh=(e,t,o,n)=>{const s=o.get();o.set(n),((e,t,o)=>{t.toggleClass.each((t=>{o.get()?La(e.element,t):Pa(e.element,t)}))})(e,t,o),((e,t,o)=>{const n=t.aria;n.update(e,n,o.get())})(e,t,o),s!==n&&t.onToggled(e,n)},sh=(e,t,o)=>{nh(e,t,o,!o.get())},rh=(e,t,o)=>{nh(e,t,o,t.selected)};var ah=Object.freeze({__proto__:null,onLoad:rh,toggle:sh,isOn:(e,t,o)=>o.get(),on:(e,t,o)=>{nh(e,t,o,!0)},off:(e,t,o)=>{nh(e,t,o,!1)},set:nh}),ih=Object.freeze({__proto__:null,exhibit:()=>Ea({}),events:(e,t)=>{const o=(n=e,s=t,r=sh,Qr((e=>{r(e,n,s)})));var n,s,r;const a=xl(e,t,rh);return Hr(q([e.toggleOnExecute?[o]:[],[a]]))}});const lh=(e,t,o)=>{kt(e.element,"aria-expanded",o)};var ch=[ys("selected",!1),us("toggleClass"),ys("toggleOnExecute",!0),Di("onToggled"),xs("aria",{mode:"none"},Jn("mode",{pressed:[ys("syncWithExpanded",!1),Ri("update",((e,t,o)=>{kt(e.element,"aria-pressed",o),t.syncWithExpanded&&lh(e,0,o)}))],checked:[Ri("update",((e,t,o)=>{kt(e.element,"aria-checked",o)}))],expanded:[Ri("update",lh)],selected:[Ri("update",((e,t,o)=>{kt(e.element,"aria-selected",o)}))],none:[Ri("update",b)]}))];const dh=Ol({fields:ch,name:"toggling",active:ih,apis:ah,state:(!1,{init:()=>{const e=Es(false);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(false),readState:()=>e.get()}}})});const uh=()=>{const e=(e,t)=>{t.stop(),Rr(e)};return[Ur(er(),e),Ur(gr(),e),qr(Hs()),qr(Ws())]},mh=e=>Hr(q([e.map((e=>Qr(((t,o)=>{e(t),o.stop()})))).toArray(),uh()])),gh="alloy.item-hover",ph="alloy.item-focus",hh="alloy.item-toggled",fh=e=>{(Rl(e.element).isNone()||oh.isFocused(e))&&(oh.isFocused(e)||oh.focus(e),Ir(e,gh,{item:e}))},bh=e=>{Ir(e,ph,{item:e})},vh=x(gh),yh=x(ph),xh=x(hh),wh=e=>e.toggling.map((e=>e.exclusive?"menuitemradio":"menuitemcheckbox")).getOr("menuitem"),Sh=[os("data"),os("components"),os("dom"),ys("hasSubmenu",!1),us("toggling"),vu("itemBehaviours",[dh,oh,Pp,pu]),ys("ignoreFocus",!1),ys("domModification",{}),Ri("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:wh(e),...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:yu(e.itemBehaviours,[e.toggling.fold(dh.revoke,(e=>dh.config((e=>({aria:{mode:"checked"},...ge(e,((e,t)=>"exclusive"!==t)),onToggled:(t,o)=>{p(e.onToggled)&&e.onToggled(t,o),((e,t)=>{Ir(e,hh,{item:e,state:t})})(t,o)}}))(e)))),oh.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{bh(e)}}),Pp.config({mode:"execution"}),pu.config({store:{mode:"memory",initialValue:e.data}}),Jp("item-type-events",[...uh(),Ur(qs(),fh),Ur(mr(),oh.focus)])]),components:e.components,eventOrder:e.eventOrder}))),ys("eventOrder",{})],kh=[os("dom"),os("components"),Ri("builder",(e=>({dom:e.dom,components:e.components,events:Hr([Xr(mr())])})))],Ch=x("item-widget"),Oh=x([Uu({name:"widget",overrides:e=>({behaviours:kl([pu.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),_h=[os("uid"),os("data"),os("components"),os("dom"),ys("autofocus",!1),ys("ignoreFocus",!1),vu("widgetBehaviours",[pu,oh,Pp]),ys("domModification",{}),cm(Oh()),Ri("builder",(e=>{const t=em(Ch(),e,Oh()),o=tm(Ch(),e,t.internals()),n=t=>om(t,e,"widget").map((e=>(Pp.focusIn(e),e))),s=(t,o)=>vm(o.event.target)?A.none():e.autofocus?(o.setSource(t.element),A.none()):A.none();return{dom:e.dom,components:o,domModification:e.domModification,events:Hr([Qr(((e,t)=>{n(e).each((e=>{t.stop()}))})),Ur(qs(),fh),Ur(mr(),((t,o)=>{e.autofocus?n(t):oh.focus(t)}))]),behaviours:yu(e.widgetBehaviours,[pu.config({store:{mode:"memory",initialValue:e.data}}),oh.config({ignore:e.ignoreFocus,onFocus:e=>{bh(e)}}),Pp.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:El(),onLeft:s,onRight:s,onEscape:(t,o)=>oh.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),A.none()):A.none():(oh.focus(t),A.some(!0))})])}}))],Th=Jn("type",{widget:_h,item:Sh,separator:kh}),Eh=x([Gu({factory:{sketch:e=>{const t=Kn("menu.spec item",Th,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>ve(t,"uid")?t:{...t,uid:ha("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),Ah=x([os("value"),os("items"),os("dom"),os("components"),ys("eventOrder",{}),hu("menuBehaviours",[Gm,pu,wm,Pp]),xs("movement",{mode:"menu",moveOnTab:!0},Jn("mode",{grid:[Vi(),Ri("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[Ri("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},previousSelector:t.previousSelector,focusManager:e.focusManager}))),os("rowSelector"),ys("previousSelector",A.none)],menu:[ys("moveOnTab",!0),Ri("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),ns("markers",_i()),ys("fakeFocus",!1),ys("focusManager",mg()),Di("onHighlight"),Di("onDehighlight")]),Mh=x("alloy.menu-focus"),Dh=bm({name:"Menu",configFields:Ah(),partFields:Eh(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:bu(e.menuBehaviours,[Gm.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight,onDehighlight:e.onDehighlight}),pu.config({store:{mode:"memory",initialValue:e.value}}),wm.config({find:A.some}),Pp.config(e.movement.config(e,e.movement))]),events:Hr([Ur(yh(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Gm.highlight(e,o),t.stop(),Ir(e,Mh(),{menu:e,item:o})}))})),Ur(vh(),((e,t)=>{const o=t.event.item;Gm.highlight(e,o)})),Ur(xh(),((e,t)=>{const{item:o,state:n}=t.event;n&&"menuitemradio"===Ot(o.element,"role")&&((e,t)=>{const o=Xc(e.element,'[role="menuitemradio"][aria-checked="true"]');L(o,(o=>{Ze(o,t.element)||e.getSystem().getByDom(o).each((e=>{dh.off(e)}))}))})(e,o)}))]),components:t,eventOrder:e.eventOrder,domModification:{attributes:{role:"menu"}}})}),Bh=(e,t,o,n)=>be(o,n).bind((n=>be(e,n).bind((n=>{const s=Bh(e,t,o,n);return A.some([n].concat(s))})))).getOr([]),Fh=e=>"prepared"===e.type?A.some(e.menu):A.none(),Ih=()=>{const e=Es({}),t=Es({}),o=Es({}),n=Ql(),s=Es({}),r=e=>a(e).bind(Fh),a=e=>be(t.get(),e),i=t=>be(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(r,a,i,l)=>{n.set(r),e.set(i),t.set(a),s.set(l);const c=((e,t)=>{const o={};le(e,((e,t)=>{L(e,(e=>{o[e]=t}))}));const n=t,s=de(t,((e,t)=>({k:e,v:t}))),r=ce(s,((e,t)=>[t].concat(Bh(o,n,s,t))));return ce(o,(e=>be(r,e).getOr([e])))})(l,i);o.set(c)},expand:t=>be(e.get(),t).map((e=>{const n=be(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>be(o.get(),e),collapse:e=>be(o.get(),e).bind((e=>e.length>1?A.some(e.slice(1)):A.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=s.get();return J(ae(t),e)},getPrimary:()=>n.get().bind(r),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,s)=>{const a=U(i(t).toArray(),(e=>r(e).isSome()));return be(o.get(),t).bind((t=>{const o=Y(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return A.none();t.push(n.getOrDie())}return A.some(t)})(X(o,((t,a)=>((t,o,n)=>r(t).bind((s=>(t=>he(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:s,triggeringItem:e,triggeringPath:n}))))))))(t,s,o.slice(0,a+1)).fold((()=>xe(n.get(),t)?[]:[A.none()]),(e=>[A.some(e)])))))}))}}},Rh=Fh,Nh=la("tiered-menu-item-highlight"),Vh=la("tiered-menu-item-dehighlight");var zh;!function(e){e[e.HighlightMenuAndItem=0]="HighlightMenuAndItem",e[e.HighlightJustMenu=1]="HighlightJustMenu",e[e.HighlightNone=2]="HighlightNone"}(zh||(zh={}));const Hh=x("collapse-item"),Lh=fm({name:"TieredMenu",configFields:[Ii("onExecute"),Ii("onEscape"),Fi("onOpenMenu"),Fi("onOpenSubmenu"),Di("onRepositionMenu"),Di("onCollapseMenu"),ys("highlightOnOpen",zh.HighlightMenuAndItem),ls("data",[os("primary"),os("menus"),os("expansions")]),ys("fakeFocus",!1),Di("onHighlightItem"),Di("onDehighlightItem"),Di("onHover"),Ei(),os("dom"),ys("navigateOnHover",!0),ys("stayInDom",!1),hu("tmenuBehaviours",[Pp,Gm,wm,Yp]),ys("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=Ql(),n=Ih(),s=e=>pu.getValue(e).value,r=t=>ce(e.data.menus,((e,t)=>X(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=Gm.highlight,i=(t,o)=>{a(t,o),Gm.getHighlighted(o).orThunk((()=>Gm.getFirst(o))).each((n=>{e.fakeFocus?Gm.highlight(o,n):Nr(t,n.element,mr())}))},l=(e,t)=>we(H(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?A.some(e.menu):A.none()))))),c=(t,o,n)=>{const s=l(o,o.otherMenus(n));L(s,(o=>{ja(o.element,[e.markers.backgroundMenu]),e.stayInDom||Yp.remove(t,o)}))},d=(t,n)=>{const r=(t=>o.get().getOrThunk((()=>{const n={},r=Xc(t.element,`.${e.markers.item}`),a=U(r,(e=>"true"===Ot(e,"aria-haspopup")));return L(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=s(e);n[t]=e}))})),o.set(n),n})))(t);le(r,((e,t)=>{const o=R(n,t);kt(e.element,"aria-expanded",o)}))},u=(t,o,n)=>A.from(n[0]).bind((s=>o.lookupMenu(s).bind((s=>{if("notbuilt"===s.type)return A.none();{const r=s.menu,a=l(o,n.slice(1));return L(a,(t=>{La(t.element,e.markers.backgroundMenu)})),yt(r.element)||Yp.append(t,ai(r)),ja(r.element,[e.markers.backgroundMenu]),i(t,r),c(t,o,n),A.some(r)}}))));let m;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(m||(m={}));const g=(t,o,r=m.HighlightSubmenu)=>{if(o.hasConfigured(Rm)&&Rm.isDisabled(o))return A.some(o);{const a=s(o);return n.expand(a).bind((s=>(d(t,s),A.from(s[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const s=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,s),s}return o.menu})(t,a,i);return yt(l.element)||Yp.append(t,ai(l)),e.onOpenSubmenu(t,o,l,Y(s)),r===m.HighlightSubmenu?(Gm.highlightFirst(l),u(t,n,s)):(Gm.dehighlightAll(l),A.some(o))})))))))}},p=(t,o)=>{const r=s(o);return n.collapse(r).bind((s=>(d(t,s),u(t,n,s).map((n=>(e.onCollapseMenu(t,o,n),n))))))},h=t=>(o,n)=>hi(n.getSource(),`.${e.markers.item}`).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(E))))),f=Hr([Ur(Mh(),((e,t)=>{const o=t.event.item;n.lookupItem(s(o)).each((()=>{const o=t.event.menu;Gm.highlight(e,o);const r=s(t.event.item);n.refresh(r).each((t=>c(e,n,t)))}))})),Qr(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===s(o).indexOf("collapse-item")&&p(t,o),g(t,o,m.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),Yr(((t,o)=>{(t=>{const o=((t,o,n)=>ce(n,((n,s)=>{const r=()=>Dh.sketch({...n,value:s,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:(e,t)=>{Ir(e,Nh,{menuComp:e,itemComp:t})},onDehighlight:(e,t)=>{Ir(e,Vh,{menuComp:e,itemComp:t})},focusManager:e.fakeFocus?gg():mg()});return s===o?{type:"prepared",menu:t.getSystem().build(r())}:{type:"notbuilt",nbMenu:r}})))(t,e.data.primary,e.data.menus),s=r();return n.setContents(e.data.primary,o,e.data.expansions,s),n.getPrimary()})(t).each((o=>{Yp.append(t,ai(o)),e.onOpenMenu(t,o),e.highlightOnOpen===zh.HighlightMenuAndItem?i(t,o):e.highlightOnOpen===zh.HighlightJustMenu&&a(t,o)}))})),Ur(Nh,((t,o)=>{e.onHighlightItem(t,o.event.menuComp,o.event.itemComp)})),Ur(Vh,((t,o)=>{e.onDehighlightItem(t,o.event.menuComp,o.event.itemComp)})),...e.navigateOnHover?[Ur(vh(),((t,o)=>{const r=o.event.item;((e,t)=>{const o=s(t);n.refresh(o).bind((t=>(d(e,t),u(e,n,t))))})(t,r),g(t,r,m.HighlightParent),e.onHover(t,r)}))]:[]]),v=e=>Gm.getHighlighted(e).bind(Gm.getHighlighted),y={collapseMenu:e=>{v(e).each((t=>{p(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{i(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>v(t).bind((e=>{const t=s(e),o=fe(n.getMenus()),r=we(H(o,Rh));return n.getTriggeringPath(t,(e=>((e,t,o)=>re(t,(e=>{if(!e.getSystem().isConnected())return A.none();const t=Gm.getCandidates(e);return G(t,(e=>s(e)===o))})))(0,r,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>A.from(e.components()[0]).filter((e=>"menu"===Ot(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:bu(e.tmenuBehaviours,[Pp.config({mode:"special",onRight:h(((e,t)=>vm(t.element)?A.none():g(e,t,m.HighlightSubmenu))),onLeft:h(((e,t)=>vm(t.element)?A.none():p(e,t))),onEscape:h(((t,o)=>p(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{Nr(e,t.element,mr())}))}}),Gm.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),wm.config({find:e=>Gm.getHighlighted(e)}),Yp.config({})]),eventOrder:e.eventOrder,apis:y,events:f}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:Ms(e,t),expansions:{}}),collapseItem:e=>({value:la(Hh()),meta:{text:e}})}}),Ph=fm({name:"InlineView",configFields:[os("lazySink"),Di("onShow"),Di("onHide"),fs("onEscape"),hu("inlineBehaviours",[Xd,pu,Al]),vs("fireDismissalEventInstead",[ys("event",Cr())]),vs("fireRepositionEventInstead",[ys("event",Or())]),ys("getRelated",A.none),ys("isExtraPart",T),ys("eventOrder",A.none)],factory:(e,t)=>{const o=(t,o,n,s)=>{const r=e.lazySink(t).getOrDie();Xd.openWhileCloaked(t,o,(()=>Sd.positionWithinBounds(r,t,n,s()))),pu.setValue(t,A.some({mode:"position",config:n,getBounds:s}))},n=(t,o,n,s)=>{const r=((e,t,o,n,s)=>{const r=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>fl(),onRtl:()=>bl()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return Lh.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightOnOpen:n.menu.highlightOnOpen,fakeFocus:n.menu.fakeFocus,onEscape:()=>(Xd.close(t),e.onEscape.map((e=>e(t))),A.some(!0)),onExecute:()=>A.some(!0),onOpenMenu:(e,t)=>{Sd.positionWithinBounds(r().getOrDie(),t,o,s())},onOpenSubmenu:(e,t,o,n)=>{const s=r().getOrDie();Sd.position(s,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=r().getOrDie();Sd.positionWithinBounds(a,t,o,s()),L(n,(e=>{const t=i(e.triggeringPath);Sd.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,s);Xd.open(t,r),pu.setValue(t,A.some({mode:"menu",menu:r}))},s=t=>{Xd.isOpen(t)&&pu.getValue(t).each((o=>{switch(o.mode){case"menu":Xd.getState(t).each(Lh.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();Sd.positionWithinBounds(n,t,o.config,o.getBounds())}}))},r={setContent:(e,t)=>{Xd.setContent(e,t)},showAt:(e,t,n)=>{const s=A.none;o(e,t,n,s)},showWithinBounds:o,showMenuAt:(e,t,o)=>{n(e,t,o,A.none)},showMenuWithinBounds:n,hide:e=>{Xd.isOpen(e)&&(pu.setValue(e,A.none()),Xd.close(e))},getContent:e=>Xd.getState(e),reposition:s,isOpen:Xd.isOpen};return{uid:e.uid,dom:e.dom,behaviours:bu(e.inlineBehaviours,[Xd.config({isPartOf:(t,o,n)=>vi(o,n)||((t,o)=>e.getRelated(t).exists((e=>vi(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),pu.config({store:{mode:"memory",initialValue:A.none()}}),Al.config({channels:{...Qd({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...tu({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:s})}})]),eventOrder:e.eventOrder,apis:r}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithinBounds:(e,t,o,n,s)=>{e.showWithinBounds(t,o,n,s)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,s)=>{e.showMenuWithinBounds(t,o,n,s)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}});var Uh=tinymce.util.Tools.resolve("tinymce.util.Delay");const Wh=fm({name:"Button",factory:e=>{const t=mh(e.action),o=e.dom.tag,n=t=>be(e.dom,"attributes").bind((e=>be(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:yu(e.buttonBehaviours,[oh.config({}),Pp.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:e.role.getOr(n("role").getOr("button"))}},eventOrder:e.eventOrder}},configFields:[ys("uid",void 0),os("dom"),ys("components",[]),vu("buttonBehaviours",[oh,Pp]),us("action"),us("role"),ys("eventOrder",{})]}),jh=e=>{const t=(e=>void 0!==e.uid)(e)&&ye(e,"uid")?e.uid:ha("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}};var Gh=tinymce.util.Tools.resolve("tinymce.util.I18n");const $h={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},qh="temporary-placeholder",Xh=e=>()=>be(e,qh).getOr("!not found!"),Kh=(e,t)=>{const o=e.toLowerCase();if(Gh.isRtl()){const e=((e,t)=>Ae(e,t)?e:((e,t)=>e+t)(e,t))(o,"-rtl");return ve(t,e)?e:o}return o},Yh=(e,t)=>be(t,Kh(e,t)),Jh=(e,t)=>{const o=t();return Yh(e,o).getOrThunk(Xh(o))},Zh=()=>Jp("add-focusable",[Yr((e=>{gi(e.element,"svg").each((e=>kt(e,"focusable","false")))}))]),Qh=(e,t,o,n)=>{var s,r;const a=(e=>!!Gh.isRtl()&&ve($h,e))(t)?["tox-icon--flip"]:[],i=be(o,Kh(t,o)).or(n).getOrThunk(Xh(o));return{dom:{tag:e.tag,attributes:null!==(s=e.attributes)&&void 0!==s?s:{},classes:e.classes.concat(a),innerHtml:i},behaviours:kl([...null!==(r=e.behaviours)&&void 0!==r?r:[],Zh()])}},ef=(e,t,o,n=A.none())=>Qh(t,e,o(),n),tf={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},of=fm({name:"Notification",factory:e=>{const t=jh({dom:{tag:"p",innerHtml:e.translationProvider(e.text)},behaviours:kl([Yp.config({})])}),o=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),n=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),s=jh({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(0)]},n(0)],behaviours:kl([Yp.config({})])}),r={updateProgress:(e,t)=>{e.getSystem().isConnected()&&s.getOpt(e).each((e=>{Yp.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(t)]},n(t)])}))},updateText:(e,o)=>{if(e.getSystem().isConnected()){const n=t.get(e);Yp.set(n,[ti(o)])}}},a=q([e.icon.toArray(),e.level.toArray(),e.level.bind((e=>A.from(tf[e]))).toArray()]),i=jh(Wh.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[ef("close",{tag:"span",classes:["tox-icon"],attributes:{"aria-label":e.translationProvider("Close")}},e.iconProvider)],action:t=>{e.onAction(t)}})),l=((e,t,o)=>{const n=o(),s=G(e,(e=>ve(n,Kh(e,n))));return Qh({tag:"div",classes:["tox-notification__icon"]},s.getOr(qh),n,A.none())})(a,0,e.iconProvider),c=[l,{dom:{tag:"div",classes:["tox-notification__body"]},components:[t.asSpec()],behaviours:kl([Yp.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert"},classes:e.level.map((e=>["tox-notification","tox-notification--in",`tox-notification--${e}`])).getOr(["tox-notification","tox-notification--in"])},behaviours:kl([oh.config({}),Jp("notification-events",[Ur(Xs(),(e=>{i.getOpt(e).each(oh.focus)}))])]),components:c.concat(e.progress?[s.asSpec()]:[]).concat(e.closeButton?[i.asSpec()]:[]),apis:r}},configFields:[us("level"),os("progress"),us("icon"),os("onAction"),os("text"),os("iconProvider"),os("translationProvider"),Cs("closeButton",!0)],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var nf,sf,rf=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),af=tinymce.util.Tools.resolve("tinymce.EditorManager"),lf=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(nf||(nf={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(sf||(sf={}));const cf=e=>t=>t.options.get(e),df=e=>t=>A.from(e(t)),uf=e=>{const t=lf.deviceType.isPhone(),o=lf.deviceType.isTablet()||t,n=e.options.register,s=e=>r(e)||!1===e,a=e=>r(e)||h(e);n("skin",{processor:e=>r(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:rf.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("font_size_input_default_unit",{processor:"string",default:"pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>r(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||r(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),V(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:sf.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("ui_mode",{processor:"string",default:"combined"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:s,default:"#top"}),n("anchor_bottom",{processor:s,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("promotion",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!lf.deviceType.isTouch()}),n("sidebar_show",{processor:"string"})},mf=cf("readonly"),gf=cf("height"),pf=cf("width"),hf=df(cf("min_width")),ff=df(cf("min_height")),bf=df(cf("max_width")),vf=df(cf("max_height")),yf=df(cf("style_formats")),xf=cf("style_formats_merge"),wf=cf("style_formats_autohide"),Sf=cf("content_langs"),kf=cf("removed_menuitems"),Cf=cf("toolbar_mode"),Of=cf("toolbar_groups"),_f=cf("toolbar_location"),Tf=cf("fixed_toolbar_container"),Ef=cf("fixed_toolbar_container_target"),Af=cf("toolbar_persist"),Mf=cf("toolbar_sticky_offset"),Df=cf("menubar"),Bf=cf("toolbar"),Ff=cf("file_picker_callback"),If=cf("file_picker_validator_handler"),Rf=cf("font_size_input_default_unit"),Nf=cf("file_picker_types"),Vf=cf("typeahead_urls"),zf=cf("anchor_top"),Hf=cf("anchor_bottom"),Lf=cf("draggable_modal"),Pf=cf("statusbar"),Uf=cf("elementpath"),Wf=cf("branding"),jf=cf("resize"),Gf=cf("paste_as_text"),$f=cf("sidebar_show"),qf=cf("promotion"),Xf=e=>!1===e.options.get("skin"),Kf=e=>!1!==e.options.get("menubar"),Yf=e=>{const t=e.options.get("skin_url");if(Xf(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return af.baseURL+"/skins/ui/"+t}},Jf=e=>e.options.get("line_height_formats").split(" "),Zf=e=>{const t=Bf(e),o=r(t),n=l(t)&&t.length>0;return!eb(e)&&(n||o||!0===t)},Qf=e=>{const t=V(9,(t=>e.options.get("toolbar"+(t+1)))),o=U(t,r);return Ce(o.length>0,o)},eb=e=>Qf(e).fold((()=>{const t=Bf(e);return f(t,r)&&t.length>0}),E),tb=e=>_f(e)===sf.bottom,ob=e=>{var t;if(!e.inline)return A.none();const o=null!==(t=Tf(e))&&void 0!==t?t:"";if(o.length>0)return pi(xt(),o);const n=Ef(e);return g(n)?A.some(Ve(n)):A.none()},nb=e=>e.inline&&ob(e).isSome(),sb=e=>ob(e).getOrThunk((()=>ft(ht(Ve(e.getElement()))))),rb=e=>e.inline&&!Kf(e)&&!Zf(e)&&!eb(e),ab=e=>(e.options.get("toolbar_sticky")||e.inline)&&!nb(e)&&!rb(e),ib=e=>!nb(e)&&"split"===e.options.get("ui_mode"),lb=e=>{const t=e.options.get("menu");return ce(t,(e=>({...e,items:e.items})))};var cb=Object.freeze({__proto__:null,get ToolbarMode(){return nf},get ToolbarLocation(){return sf},register:uf,getSkinUrl:Yf,isReadOnly:mf,isSkinDisabled:Xf,getHeightOption:gf,getWidthOption:pf,getMinWidthOption:hf,getMinHeightOption:ff,getMaxWidthOption:bf,getMaxHeightOption:vf,getUserStyleFormats:yf,shouldMergeStyleFormats:xf,shouldAutoHideStyleFormats:wf,getLineHeightFormats:Jf,getContentLanguages:Sf,getRemovedMenuItems:kf,isMenubarEnabled:Kf,isMultipleToolbars:eb,isToolbarEnabled:Zf,isToolbarPersist:Af,getMultipleToolbarsOption:Qf,getUiContainer:sb,useFixedContainer:nb,isSplitUiMode:ib,getToolbarMode:Cf,isDraggableModal:Lf,isDistractionFree:rb,isStickyToolbar:ab,getStickyToolbarOffset:Mf,getToolbarLocation:_f,isToolbarLocationBottom:tb,getToolbarGroups:Of,getMenus:lb,getMenubar:Df,getToolbar:Bf,getFilePickerCallback:Ff,getFilePickerTypes:Nf,useTypeaheadUrls:Vf,getAnchorTop:zf,getAnchorBottom:Hf,getFilePickerValidatorHandler:If,getFontSizeInputDefaultUnit:Rf,useStatusBar:Pf,useElementPath:Uf,promotionEnabled:qf,useBranding:Wf,getResize:jf,getPasteAsText:Gf,getSidebarShow:$f});const db="[data-mce-autocompleter]",ub=e=>hi(e,db);var mb;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(mb||(mb={}));var gb=mb;const pb="tox-menu-nav__js",hb="tox-collection__item",fb="tox-swatch",bb={normal:pb,color:fb},vb="tox-collection__item--enabled",yb="tox-collection__item-icon",xb="tox-collection__item-label",wb="tox-collection__item-caret",Sb="tox-collection__item--active",kb="tox-collection__item-container",Cb="tox-collection__item-container--row",Ob=e=>be(bb,e).getOr(pb),_b=e=>"color"===e?"tox-swatches":"tox-menu",Tb=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:_b(e),tieredMenu:"tox-tiered-menu"}),Eb=e=>{const t=Tb(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:Ob(e)}},Ab=(e,t,o)=>{const n=Tb(o);return{tag:"div",classes:q([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},Mb=[Dh.parts.items({})],Db=(e,t,o)=>{const n=Tb(o);return{dom:{tag:"div",classes:q([[n.tieredMenu]])},markers:Eb(o)}},Bb=x([us("data"),ys("inputAttributes",{}),ys("inputStyles",{}),ys("tag","input"),ys("inputClasses",[]),Di("onSetValue"),ys("styles",{}),ys("eventOrder",{}),hu("inputBehaviours",[pu,oh]),ys("selectOnFocus",!0)]),Fb=e=>kl([oh.config({onFocus:e.selectOnFocus?e=>{const t=e.element,o=$a(t);t.dom.setSelectionRange(0,o.length)}:b})]),Ib=e=>({...Fb(e),...bu(e.inputBehaviours,[pu.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:e=>$a(e.element),setValue:(e,t)=>{$a(e.element)!==t&&qa(e.element,t)}},onSetValue:e.onSetValue})])}),Rb=e=>({tag:e.tag,attributes:{type:"text",...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),Nb=fm({name:"Input",configFields:Bb(),factory:(e,t)=>({uid:e.uid,dom:Rb(e),components:[],behaviours:Ib(e),eventOrder:e.eventOrder})}),Vb=la("refetch-trigger-event"),zb=la("redirect-menu-item-interaction"),Hb="tox-menu__searcher",Lb=e=>pi(e.element,`.${Hb}`).bind((t=>e.getSystem().getByDom(t).toOptional())),Pb=Lb,Ub=e=>({fetchPattern:pu.getValue(e),selectionStart:e.element.dom.selectionStart,selectionEnd:e.element.dom.selectionEnd}),Wb=e=>{const t=(e,t)=>(t.cut(),A.none()),o=(e,t)=>{const o={interactionEvent:t.event,eventType:t.event.raw.type};return Ir(e,zb,o),A.some(!0)},n="searcher-events";return{dom:{tag:"div",classes:[hb]},components:[Nb.sketch({inputClasses:[Hb,"tox-textfield"],inputAttributes:{...e.placeholder.map((t=>({placeholder:e.i18n(t)}))).getOr({}),type:"search","aria-autocomplete":"list"},inputBehaviours:kl([Jp(n,[Ur(Zs(),(e=>{Fr(e,Vb)})),Ur(Ys(),((e,t)=>{"Escape"===t.event.raw.key&&t.stop()}))]),Pp.config({mode:"special",onLeft:t,onRight:t,onSpace:t,onEnter:o,onEscape:o,onUp:o,onDown:o})]),eventOrder:{keydown:[n,Pp.name()]}})]}},jb="tox-collection--results__js",Gb=e=>{var t;return e.dom?{...e,dom:{...e.dom,attributes:{...null!==(t=e.dom.attributes)&&void 0!==t?t:{},id:la("aria-item-search-result-id"),"aria-selected":"false"}}}:e},$b=(e,t)=>o=>{const n=z(o,t);return H(n,(t=>({dom:e,components:t})))},qb=(e,t)=>{const o=[];let n=[];return L(e,((e,s)=>{t(e,s)?(n.length>0&&o.push(n),n=[],(ve(e.dom,"innerHtml")||e.components&&e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),H(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},Xb=(e,t,o)=>Dh.parts.items({preprocess:n=>{const s=H(n,o);return"auto"!==e&&e>1?$b({tag:"div",classes:["tox-collection__group"]},e)(s):qb(s,((e,o)=>"separator"===t[o].type))}}),Kb=(e,t,o=!0)=>({dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Xb(e,t,w)]}),Yb=e=>N(e,(e=>"icon"in e&&void 0!==e.icon)),Jb=e=>(console.error(Yn(e)),console.log(e),A.none()),Zb=(e,t,o,n,s)=>{const r=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Dh.parts.items({preprocess:e=>qb(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:r.dom,components:r.components,items:o}},Qb=(e,t,o,n,s)=>{if("color"===s.menuType){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Dh.parts.items({preprocess:"auto"!==e?$b({tag:"div",classes:["tox-swatches__row"]},e):w})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType&&"auto"===n){const t=Kb(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType||"searchable"===s.menuType){const t="searchable"!==s.menuType?Kb(n,o):"search-with-field"===s.searchMode.searchMode?((e,t,o)=>{const n=la("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Wb({i18n:Gh.translate,placeholder:o.placeholder}),{dom:{tag:"div",classes:[...1===e?["tox-collection--list"]:["tox-collection--grid"],jb],attributes:{id:n}},components:[Xb(e,t,Gb)]}]}})(n,o,s.searchMode):((e,t,o=!0)=>{const n=la("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection",jb].concat(1===e?["tox-collection--list"]:["tox-collection--grid"]),attributes:{id:n}},components:[Xb(e,t,Gb)]}})(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===s.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Dh.parts.items({preprocess:$b({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:Ab(t,n,s.menuType),components:Mb,items:o}},ev=rs("type"),tv=rs("name"),ov=rs("label"),nv=rs("text"),sv=rs("title"),rv=rs("icon"),av=rs("value"),iv=is("fetch"),lv=is("getSubmenuItems"),cv=is("onAction"),dv=is("onItemAction"),uv=Os("onSetup",(()=>b)),mv=ps("name"),gv=ps("text"),pv=ps("icon"),hv=ps("tooltip"),fv=ps("label"),bv=ps("shortcut"),vv=fs("select"),yv=Cs("active",!1),xv=Cs("borderless",!1),wv=Cs("enabled",!0),Sv=Cs("primary",!1),kv=e=>ys("columns",e),Cv=ys("meta",{}),Ov=Os("onAction",b),_v=e=>Ss("type",e),Tv=e=>Qn("name","name",vn((()=>la(`${e}-name`))),Hn),Ev=Dn([ev,gv]),Av=Dn([_v("autocompleteitem"),yv,wv,Cv,av,gv,pv]),Mv=[wv,hv,pv,gv,uv],Dv=Dn([ev,cv].concat(Mv)),Bv=e=>qn("toolbarbutton",Dv,e),Fv=[yv].concat(Mv),Iv=Dn(Fv.concat([ev,cv])),Rv=e=>qn("ToggleButton",Iv,e),Nv=[Os("predicate",T),ks("scope","node",["node","editor"]),ks("position","selection",["node","selection","line"])],Vv=Mv.concat([_v("contextformbutton"),Sv,cv,es("original",w)]),zv=Fv.concat([_v("contextformbutton"),Sv,cv,es("original",w)]),Hv=Mv.concat([_v("contextformbutton")]),Lv=Fv.concat([_v("contextformtogglebutton")]),Pv=Jn("type",{contextformbutton:Vv,contextformtogglebutton:zv}),Uv=Dn([_v("contextform"),Os("initValue",x("")),fv,ds("commands",Pv),ms("launch",Jn("type",{contextformbutton:Hv,contextformtogglebutton:Lv}))].concat(Nv)),Wv=Dn([_v("contexttoolbar"),rs("items")].concat(Nv)),jv=[ev,rs("src"),ps("alt"),_s("classes",[],Hn)],Gv=Dn(jv),$v=[ev,nv,mv,_s("classes",["tox-collection__item-label"],Hn)],qv=Dn($v),Xv=En((()=>jn("type",{cardimage:Gv,cardtext:qv,cardcontainer:Kv}))),Kv=Dn([ev,Ss("direction","horizontal"),Ss("align","left"),Ss("valign","middle"),ds("items",Xv)]),Yv=[wv,gv,bv,("menuitem",Qn("value","value",vn((()=>la("menuitem-value"))),Nn())),Cv];const Jv=Dn([ev,fv,ds("items",Xv),uv,Ov].concat(Yv)),Zv=Dn([ev,yv,pv].concat(Yv)),Qv=[ev,rs("fancytype"),Ov],ey=[ys("initData",{})].concat(Qv),ty=[fs("select"),Ts("initData",{},[Cs("allowCustomColors",!0),Ss("storageKey","default"),bs("colors",Nn())])].concat(Qv),oy=Jn("fancytype",{inserttable:ey,colorswatch:ty}),ny=Dn([ev,uv,Ov,pv].concat(Yv)),sy=Dn([ev,lv,uv,pv].concat(Yv)),ry=Dn([ev,pv,yv,uv,cv].concat(Yv)),ay=(e,t,o)=>{const n=Xc(e.element,"."+o);if(n.length>0){const e=$(n,(e=>{const o=e.dom.getBoundingClientRect().top,s=n[0].dom.getBoundingClientRect().top;return Math.abs(o-s)>t})).getOr(n.length);return A.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return A.none()},iy=e=>((e,t)=>kl([Jp(e,t)]))(la("unnamed-events"),e),ly=la("tooltip.exclusive"),cy=la("tooltip.show"),dy=la("tooltip.hide"),uy=(e,t,o)=>{e.getSystem().broadcastOn([ly],{})};var my=Object.freeze({__proto__:null,hideAllExclusive:uy,setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&Yp.set(e,n)}))}}),gy=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{Bd(n),e.onHide(o,n),t.clearTooltip()})),t.clearTimer()};return Hr(q([[Ur(cy,(o=>{t.resetTimer((()=>{(o=>{if(!t.isShowing()){uy(o);const n=e.lazySink(o).getOrDie(),s=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:Hr("normal"===e.mode?[Ur(qs(),(e=>{Fr(o,cy)})),Ur(Gs(),(e=>{Fr(o,dy)}))]:[]),behaviours:kl([Yp.config({})])});t.setTooltip(s),Ad(n,s),e.onShow(o,s),Sd.position(n,s,{anchor:e.anchor(o)})}})(o)}),e.delay)})),Ur(dy,(n=>{t.resetTimer((()=>{o(n)}),e.delay)})),Ur(dr(),((e,t)=>{const n=t;n.universal||R(n.channels,ly)&&o(e)})),Jr((e=>{o(e)}))],"normal"===e.mode?[Ur(Xs(),(e=>{Fr(e,cy)})),Ur(lr(),(e=>{Fr(e,dy)})),Ur(qs(),(e=>{Fr(e,cy)})),Ur(Gs(),(e=>{Fr(e,dy)}))]:[Ur(Dr(),((e,t)=>{Fr(e,cy)})),Ur(Br(),(e=>{Fr(e,dy)}))]]))}}),py=[os("lazySink"),os("tooltipDom"),ys("exclusive",!0),ys("tooltipComponents",[]),ys("delay",300),ks("mode","normal",["normal","follow-highlight"]),ys("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:x([cl,ll,sl,al,rl,il]),onRtl:x([cl,ll,sl,al,rl,il])}}))),Di("onHide"),Di("onShow")],hy=Object.freeze({__proto__:null,init:()=>{const e=Ql(),t=Ql(),o=()=>{e.on(clearTimeout)},n=x("not-implemented");return _a({getTooltip:t.get,isShowing:t.isSet,setTooltip:t.set,clearTooltip:t.clear,clearTimer:o,resetTimer:(t,n)=>{o(),e.set(setTimeout(t,n))},readState:n})}});const fy=Ol({fields:py,name:"tooltipping",active:gy,state:hy,apis:my}),by="silver.readonly",vy=Dn([("readonly",ns("readonly",Ln))]);const yy=(e,t)=>{const o=e.mainUi.outerContainer.element,n=[e.mainUi.mothership,...e.uiMotherships];t&&L(n,(e=>{e.broadcastOn([Kd()],{target:o})})),L(n,(e=>{e.broadcastOn([by],{readonly:t})}))},xy=(e,t)=>{e.on("init",(()=>{e.mode.isReadOnly()&&yy(t,!0)})),e.on("SwitchMode",(()=>yy(t,e.mode.isReadOnly()))),mf(e)&&e.mode.set("readonly")},wy=()=>Al.config({channels:{[by]:{schema:vy,onReceive:(e,t)=>{Rm.set(e,t.readonly)}}}}),Sy=e=>Rm.config({disabled:e}),ky=e=>Rm.config({disabled:e,disableClass:"tox-tbtn--disabled"}),Cy=e=>Rm.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),Oy=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},_y=(e,t)=>Yr((o=>{Oy(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),Ty=(e,t)=>Jr((o=>Oy(e,o)(t.get()))),Ey=(e,t)=>Qr(((o,n)=>{Oy(e,o)(e.onAction),e.triggersSubmenu||t!==gb.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&Fr(o,hr()),n.stop())})),Ay={[ur()]:["disabling","alloy.base.behaviour","toggling","item-events"]},My=we,Dy=(e,t,o,n)=>{const s=Es(b);return{type:"item",dom:t.dom,components:My(t.optComponents),data:e.data,eventOrder:Ay,hasSubmenu:e.triggersSubmenu,itemBehaviours:kl([Jp("item-events",[Ey(e,o),_y(e,s),Ty(e,s)]),(r=()=>!e.enabled||n.isDisabled(),Rm.config({disabled:r,disableClass:"tox-collection__item--state-disabled"})),wy(),Yp.config({})].concat(e.itemBehaviours))};var r},By=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),Fy=e=>{const t=lf.os.isMacOS()||lf.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),s=H(n,(e=>{const t=e.toLowerCase().trim();return ve(o,t)?o[t]:e}));return t?s.join(""):s.join("+")},Iy=(e,t,o=[yb])=>ef(e,{tag:"div",classes:o},t),Ry=e=>({dom:{tag:"div",classes:[xb]},components:[ti(Gh.translate(e))]}),Ny=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),Vy=(e,t)=>({dom:{tag:"div",classes:[xb]},components:[{dom:{tag:e.tag,styles:e.styles},components:[ti(Gh.translate(t))]}]}),zy=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[ti(Fy(e))]}),Hy=e=>Iy("checkmark",e,["tox-collection__item-checkmark"]),Ly=e=>{const t=e.map((e=>({attributes:{title:Gh.translate(e),id:la("menu-item")}}))).getOr({});return{tag:"div",classes:[pb,hb],...t}},Py=(e,t,o,n=A.none())=>"color"===e.presets?((e,t,o)=>{const n=e.ariaLabel,s=e.value,r=e.iconContent.map((e=>((e,t,o)=>{const n=t();return Yh(e,n).or(o).getOrThunk(Xh(n))})(e,t.icons,o)));return{dom:(()=>{const e=fb,o=r.getOr(""),a=n.map((e=>({title:t.translate(e)}))).getOr({}),i={tag:"div",attributes:a,classes:[e]};return"custom"===s?{...i,tag:"button",classes:[...i.classes,"tox-swatches__picker-btn"],innerHtml:o}:"remove"===s?{...i,classes:[...i.classes,"tox-swatch--remove"],innerHtml:o}:g(s)?{...i,attributes:{...i.attributes,"data-mce-color":s},styles:{"background-color":s},innerHtml:o}:i})(),optComponents:[]}})(e,t,n):((e,t,o,n)=>{const s={tag:"div",classes:[yb]},r=o?e.iconContent.map((e=>ef(e,s,t.icons,n))).orThunk((()=>A.some({dom:s}))):A.none(),a=e.checkMark,i=A.from(e.meta).fold((()=>Ry),(e=>ve(e,"style")?k(Vy,e.style):Ry)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>A.some(Ny(e,[xb]))));return{dom:Ly(e.ariaLabel),optComponents:[r,l,e.shortcutContent.map(zy),a,e.caret]}})(e,t,o,n),Uy=(e,t)=>be(e,"tooltipWorker").map((e=>[fy.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:cc}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{fy.setComponents(t,[oi({element:Ve(e)})])}))}})])).getOr([]),Wy=(e,t)=>{const o=(e=>rf.DOM.encode(e))(Gh.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},jy=(e,t)=>H(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":Cb,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[kb,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,jy(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>R(t.cardText.highlightOn,e))),n=o?A.from(t.cardText.matchText).getOr(""):"";return Ny(Wy(e.text,n),e.classes)}})),Gy=Ku(Ch(),Oh()),$y=e=>({value:Yy(e)}),qy=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,Xy=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Ky=e=>qy.test(e)||Xy.test(e),Yy=e=>_e(e,"#").toUpperCase(),Jy=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},Zy=e=>{const t=Jy(e.red)+Jy(e.green)+Jy(e.blue);return $y(t)},Qy=Math.min,ex=Math.max,tx=Math.round,ox=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,nx=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,sx=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),rx=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},ax=e=>{let t,o,n;const s=(e.hue||0)%360;let r=e.saturation/100,a=e.value/100;if(r=ex(0,Qy(r,1)),a=ex(0,Qy(a,1)),0===r)return t=o=n=tx(255*a),sx(t,o,n,1);const i=s/60,l=a*r,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=tx(255*(t+d)),o=tx(255*(o+d)),n=tx(255*(n+d)),sx(t,o,n,1)},ix=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(qy,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=Xy.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),s=parseInt(t[3],16);return sx(o,n,s,1)},lx=(e,t,o,n)=>{const s=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return sx(s,r,a,i)},cx=e=>{if("transparent"===e)return A.some(sx(0,0,0,0));const t=ox.exec(e);if(null!==t)return A.some(lx(t[1],t[2],t[3],"1"));const o=nx.exec(e);return null!==o?A.some(lx(o[1],o[2],o[3],o[4])):A.none()},dx=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,ux=sx(255,0,0,1),mx=(e,t)=>{e.dispatch("ResizeContent",t)},gx=(e,t)=>{e.dispatch("TextColorChange",t)},px=(e,t)=>e.dispatch("ResolveName",{name:t.nodeName.toLowerCase(),target:t}),hx=(e,t)=>()=>{e(),t()},fx=e=>vx(e,"NodeChange",(t=>{t.setEnabled(e.selection.isEditable())})),bx=(e,t)=>o=>{const n=fx(e)(o),s=((e,t)=>o=>{const n=Zl(),s=()=>{o.setActive(e.formatter.match(t));const s=e.formatter.formatChanged(t,o.setActive);n.set(s)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),n.clear()}})(e,t)(o);return()=>{n(),s()}},vx=(e,t,o)=>n=>{const s=()=>o(n),r=()=>{o(n),e.on(t,s)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),e.off(t,s)}},yx=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},xx=(e,t)=>()=>e.execCommand(t);var wx=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const Sx={},kx=e=>be(Sx,e).getOrThunk((()=>{const t=`tinymce-custom-colors-${e}`,o=wx.getItem(t);if(m(o)){const e=wx.getItem("tinymce-custom-colors");wx.setItem(t,g(e)?e:"[]")}const n=((e,t=10)=>{const o=wx.getItem(e),n=r(o)?JSON.parse(o):[],s=t-(a=n).length<0?a.slice(0,t):a;var a;const i=e=>{s.splice(e,1)};return{add:o=>{I(s,o).each(i),s.unshift(o),s.length>t&&s.pop(),wx.setItem(e,JSON.stringify(s))},state:()=>s.slice(0)}})(t,10);return Sx[e]=n,n})),Cx=(e,t)=>{kx(e).add(t)},Ox=(e,t,o)=>({hue:e,saturation:t,value:o}),_x=e=>{let t=0,o=0,n=0;const s=e.red/255,r=e.green/255,a=e.blue/255,i=Math.min(s,Math.min(r,a)),l=Math.max(s,Math.max(r,a));return i===l?(n=i,Ox(0,0,100*n)):(t=s===i?3:a===i?1:5,t=60*(t-(s===i?r-a:a===i?s-r:a-s)/(l-i)),o=(l-i)/l,n=l,Ox(Math.round(t),Math.round(100*o),Math.round(100*n)))},Tx=e=>Zy(ax(e)),Ex=e=>{return(t=e,Ky(t)?A.some({value:Yy(t)}):A.none()).orThunk((()=>cx(e).map(Zy))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,s=n[0],r=n[1],a=n[2],i=n[3];return Zy(sx(s,r,a,i))}));var t},Ax="forecolor",Mx="hilitecolor",Dx=e=>Math.max(5,Math.ceil(Math.sqrt(e))),Bx=(e,t)=>{const o=Dx(t),n=Ix("color_cols")(e);return 5===o?n:o},Fx=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:"#"+Ex(e[o]).value,icon:"checkmark",type:"choiceitem"});return t},Ix=e=>t=>t.options.get(e),Rx="#000000",Nx=(e,t)=>{const o=((e,t)=>t===Ax?Ix("color_cols_foreground")(e):t===Mx?Ix("color_cols_background")(e):Ix("color_cols")(e))(e,t);return o>0?o:5},Vx=Ix("custom_colors"),zx=(e,t)=>t===Ax&&e.options.isSet("color_map_foreground")?Ix("color_map_foreground")(e):t===Mx&&e.options.isSet("color_map_background")?Ix("color_map_background")(e):Ix("color_map")(e),Hx=Ix("color_default_foreground"),Lx=Ix("color_default_background"),Px=(e,t)=>{const o=Ve(e.selection.getStart()),n="hilitecolor"===t?Is(o,(e=>{if(Ge(e)){const t=It(e,"background-color");return Ce(cx(t).exists((e=>0!==e.alpha)),t)}return A.none()})).getOr("rgba(0, 0, 0, 0)"):It(o,"color");return cx(n).map((e=>"#"+Zy(e).value))},Ux=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},Wx=(e,t,o,n)=>{"custom"===o?Yx(e)((o=>{o.each((o=>{Cx(t,o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),Px(e,t).getOr(Rx)):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},jx=(e,t,o)=>e.concat((e=>H(kx(e).state(),(e=>({type:"choiceitem",text:e,icon:"checkmark",value:e}))))(t).concat(Ux(o))),Gx=(e,t,o)=>n=>{n(jx(e,t,o))},$x=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},qx=(e,t)=>o=>{const n=Px(e,t);return xe(n,o.toUpperCase())},Xx=(e,t,o,n,s)=>{e.ui.registry.addSplitButton(t,{tooltip:n,presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:qx(e,o),columns:Nx(e,o),fetch:Gx(zx(e,o),o,Vx(e)),onAction:t=>{Wx(e,o,s.get(),b)},onItemAction:(n,r)=>{Wx(e,o,r,(o=>{s.set(o),gx(e,{name:t,color:o})}))},onSetup:o=>{$x(o,t,s.get());const n=e=>{e.name===t&&$x(o,e.name,e.color)};return e.on("TextColorChange",n),hx(fx(e)(o),(()=>{e.off("TextColorChange",n)}))}})},Kx=(e,t,o,n,s)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",onSetup:o=>($x(o,t,s.get()),fx(e)(o)),getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",select:qx(e,o),initData:{storageKey:o},onAction:n=>{Wx(e,o,n.value,(o=>{s.set(o),gx(e,{name:t,color:o})}))}}]})},Yx=e=>(t,o)=>{let n=!1;const s={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const s=o.getData().colorpicker;n?(t(A.from(s)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",s]))},onClose:b,onCancel:()=>{t(A.none())}})},Jx=(e,t,o,n,s,r,a,i)=>{const l=Yb(t),c=Zx(t,o,n,"color"!==s?"normal":"color",r,a,i);return Qb(e,l,c,n,{menuType:s})},Zx=(e,t,o,n,s,r,a)=>we(H(e,(i=>{return"choiceitem"===i.type?(l=i,qn("choicemenuitem",Zv,l)).fold(Jb,(i=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Py({presets:o,textContent:t?e.text:A.none(),htmlContent:A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:A.none(),checkMark:t?A.some(Hy(a.icons)):A.none(),caret:A.none(),value:e.value},a,i);return fn(Dy({data:By(e),enabled:e.enabled,getApi:e=>({setActive:t=>{dh.set(e,t)},isActive:()=>dh.isOn(e),isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(s),b),triggersSubmenu:!1,itemBehaviours:[]},l,r,a),{toggling:{toggleClass:vb,toggleOnExecute:!1,selected:e.active,exclusive:!0}})})(i,1===o,n,t,r(i.value),s,a,Yb(e))))):A.none();var l}))),Qx=(e,t)=>{const o=Eb(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group"),previousSelector:e=>"color"===t?pi(e.element,"[aria-checked=true]"):A.none()}},ew=la("cell-over"),tw=la("cell-execute"),ow=(e,t,o)=>{const n=o=>Ir(o,tw,{row:e,col:t}),s=(e,t)=>{t.stop(),n(e)};return ri({dom:{tag:"div",attributes:{role:"button","aria-labelledby":o}},behaviours:kl([Jp("insert-table-picker-cell",[Ur(qs(),oh.focus),Ur(ur(),n),Ur(er(),s),Ur(gr(),s)]),dh.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),oh.config({onFocus:o=>Ir(o,ew,{row:e,col:t})})])})},nw=e=>X(e,(e=>H(e,ai))),sw=(e,t)=>ti(`${t}x${e}`),rw={inserttable:e=>{const t=la("size-label"),o=((e,t,o)=>{const n=[];for(let t=0;t<10;t++){const o=[];for(let n=0;n<10;n++)o.push(ow(t,n,e));n.push(o)}return n})(t),n=sw(0,0),s=jh({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:t}},components:[n],behaviours:kl([Yp.config({})])});return{type:"widget",data:{value:la("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Gy.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:nw(o).concat(s.asSpec()),behaviours:kl([Jp("insert-table-picker",[Yr((e=>{Yp.set(s.get(e),[n])})),$r(ew,((e,t,n)=>{const{row:r,col:a}=n.event;((e,t,o,n,s)=>{for(let n=0;n<10;n++)for(let s=0;s<10;s++)dh.set(e[n][s],n<=t&&s<=o)})(o,r,a),Yp.set(s.get(e),[sw(r+1,a+1)])})),$r(tw,((t,o,n)=>{const{row:s,col:r}=n.event;e.onAction({numRows:s+1,numColumns:r+1}),Fr(t,hr())}))]),Pp.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>jx(t.colorinput.getColors(e.initData.storageKey),e.initData.storageKey,o)),(e=>e.concat(Ux(o))))})(e,t),n=t.colorinput.getColorCols(e.initData.storageKey),s="color",r={...Jx(la("menu-value"),o,(t=>{e.onAction({value:t})}),n,s,gb.CLOSE_ON_EXECUTE,e.select.getOr(T),t.shared.providers),markers:Eb(s),movement:Qx(n,s)};return{type:"widget",data:{value:la("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Gy.widget(Dh.sketch(r))]}}},aw=e=>({type:"separator",dom:{tag:"div",classes:[hb,"tox-collection__group-heading"]},components:e.text.map(ti).toArray()});var iw=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n),getExistingCoupled:(e,t,o,n)=>o.getExisting(e,t,n)}),lw=[ns("others",$n(sn.value,Nn()))],cw=Object.freeze({__proto__:null,init:()=>{const e={},t=(t,o)=>{if(0===ae(t.others).length)throw new Error("Cannot find any known coupled components");return be(e,o)},o=x({});return _a({readState:o,getExisting:(e,o,n)=>t(o,n).orThunk((()=>(be(o.others,n).getOrDie("No information found for coupled component: "+n),A.none()))),getOrCreate:(o,n,s)=>t(n,s).getOrThunk((()=>{const t=be(n.others,s).getOrDie("No information found for coupled component: "+s)(o),r=o.getSystem().build(t);return e[s]=r,r}))})}});const dw=Ol({fields:lw,name:"coupling",apis:iw,state:cw}),uw=e=>{let t=A.none(),o=[];const n=e=>{s()?r(e):o.push(e)},s=()=>t.isSome(),r=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{s()||(t=A.some(e),L(o,r),o=[])})),{get:n,map:e=>uw((t=>{n((o=>{t(e(o))}))})),isReady:s}},mw={nu:uw,pure:e=>uw((t=>{t(e)}))},gw=e=>{setTimeout((()=>{throw e}),0)},pw=e=>{const t=t=>{e().then(t,gw)};return{map:t=>pw((()=>e().then(t))),bind:t=>pw((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>pw((()=>e().then((()=>t.toPromise())))),toLazy:()=>mw.nu(t),toCached:()=>{let t=null;return pw((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},hw=e=>pw((()=>new Promise(e))),fw=e=>pw((()=>Promise.resolve(e))),bw=x("sink"),vw=x(ju({name:bw(),overrides:x({dom:{tag:"div"},behaviours:kl([Sd.config({useFixed:E})]),events:Hr([qr(Ys()),qr(Ws()),qr(er())])})})),yw=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",s=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:s})),(e=>({type:n,hotspot:o,overrides:s,layouts:e})))},xw=(e,t,o,n,s,r,a)=>{const i=((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=kw(n,e);return i.map((e=>e.bind((e=>A.from(Lh.sketch({...r.menu(),uid:ha(""),data:e,highlightOnOpen:a,onOpenMenu:(e,t)=>{const n=l().getOrDie();Sd.position(n,t,{anchor:o}),Xd.decloak(s)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();Sd.position(n,o,{anchor:{type:"submenu",item:t}}),Xd.decloak(s)},onRepositionMenu:(e,t,n)=>{const s=l().getOrDie();Sd.position(s,t,{anchor:o}),L(n,(e=>{Sd.position(s,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(oh.focus(n),Xd.close(s),A.some(!0))}))))))})(e,t,yw(e,o),o,n,s,a);return i.map((e=>(e.fold((()=>{Xd.isOpen(n)&&Xd.close(n)}),(e=>{Xd.cloak(n),Xd.open(n,e),r(n)})),n)))},ww=(e,t,o,n,s,r,a)=>(Xd.close(n),fw(n)),Sw=(e,t,o,n,s,r)=>{const a=dw.getCoupled(o,"sandbox");return(Xd.isOpen(a)?ww:xw)(e,t,o,a,n,s,r)},kw=(e,t)=>e.getSystem().getByUid(t.uid+"-"+bw()).map((e=>()=>sn.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>sn.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),Cw=e=>{Xd.getState(e).each((e=>{Lh.repositionMenus(e)}))},Ow=(e,t,o)=>{const n=bi(),s=kw(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id,role:"listbox"}},behaviours:yu(e.sandboxBehaviours,[pu.config({store:{mode:"memory",initialValue:t}}),Xd.config({onOpen:(s,r)=>{const a=yw(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=wm.getCurrent(t).getOr(t),s=Jt(e.element);o?Dt(n.element,"min-width",s+"px"):((e,t)=>{Yt.set(e,t)})(n.element,s)})(a.hotspot,r,e.useMinWidth),e.onOpen(a,s,r),void 0!==o&&void 0!==o.onOpen&&o.onOpen(s,r)},onClose:(e,s)=>{n.unlink(t.element),void 0!==o&&void 0!==o.onClose&&o.onClose(e,s)},isPartOf:(e,o,n)=>vi(o,n)||vi(t,n),getAttachPoint:()=>s().getOrDie()}),wm.config({find:e=>Xd.getState(e).bind((e=>wm.getCurrent(e)))}),Al.config({channels:{...Qd({isExtraPart:T}),...tu({doReposition:Cw})}})])}},_w=e=>{const t=dw.getCoupled(e,"sandbox");Cw(t)},Tw=()=>[ys("sandboxClasses",[]),vu("sandboxBehaviours",[wm,Al,Xd,pu])],Ew=x([os("dom"),os("fetch"),Di("onOpen"),Bi("onExecute"),ys("getHotspot",A.some),ys("getAnchorOverrides",x({})),wc(),hu("dropdownBehaviours",[dh,dw,Pp,oh]),os("toggleClass"),ys("eventOrder",{}),us("lazySink"),ys("matchWidth",!1),ys("useMinWidth",!1),us("role")].concat(Tw())),Aw=x([Wu({schema:[Ei(),ys("fakeFocus",!1)],name:"menu",defaults:e=>({onExecute:e.onExecute})}),vw()]),Mw=bm({name:"Dropdown",configFields:Ew(),partFields:Aw(),factory:(e,t,o,n)=>{const s=e=>{Xd.getState(e).each((e=>{Lh.highlightPrimary(e)}))},r=(t,o,s)=>Sw(e,w,t,n,o,s),a={expand:e=>{dh.isOn(e)||r(e,b,zh.HighlightNone).get(b)},open:e=>{dh.isOn(e)||r(e,b,zh.HighlightMenuAndItem).get(b)},refetch:t=>dw.getExistingCoupled(t,"sandbox").fold((()=>r(t,b,zh.HighlightMenuAndItem).map(b)),(o=>xw(e,w,t,o,n,b,zh.HighlightMenuAndItem).map(b))),isOpen:dh.isOn,close:e=>{dh.isOn(e)&&r(e,b,zh.HighlightMenuAndItem).get(b)},repositionMenus:e=>{dh.isOn(e)&&_w(e)}},i=(e,t)=>(Rr(e),A.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.dropdownBehaviours,[dh.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),dw.config({others:{sandbox:t=>Ow(e,t,{onOpen:()=>dh.on(t),onClose:()=>dh.off(t)})}}),Pp.config({mode:"special",onSpace:i,onEnter:i,onDown:(e,t)=>{if(Mw.isOpen(e)){const t=dw.getCoupled(e,"sandbox");s(t)}else Mw.open(e);return A.some(!0)},onEscape:(e,t)=>Mw.isOpen(e)?(Mw.close(e),A.some(!0)):A.none()}),oh.config({})]),events:mh(A.some((e=>{r(e,s,zh.HighlightMenuAndItem).get(b)}))),eventOrder:{...e.eventOrder,[ur()]:["disabling","toggling","alloy.base.behaviour"]},apis:a,domModification:{attributes:{"aria-haspopup":"true",...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:("type",be(e.dom,"attributes").bind((e=>be(e,"type")))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),refetch:(e,t)=>e.refetch(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),Dw=(e,t,o)=>{Pb(e).each((e=>{var n;((e,t)=>{_t(t.element,"id").each((t=>kt(e.element,"aria-activedescendant",t)))})(e,o),(Ua((n=t).element,jb)?A.some(n.element):pi(n.element,"."+jb)).each((t=>{_t(t,"id").each((t=>kt(e.element,"aria-controls",t)))}))})),kt(o.element,"aria-selected","true")},Bw=(e,t,o)=>{kt(o.element,"aria-selected","false")},Fw=e=>dw.getExistingCoupled(e,"sandbox").bind(Lb).map(Ub).map((e=>e.fetchPattern)).getOr("");var Iw;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(Iw||(Iw={}));const Rw=(e,t,o,n,s)=>{const r=o.shared.providers,a=e=>s?{...e,shortcut:A.none(),icon:e.text.isSome()?A.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,qn("menuitem",ny,i)).fold(Jb,(e=>A.some(((e,t,o,n=!0)=>{const s=Py({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.none(),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return Dy({data:By(e),getApi:e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o)})(a(e),t,r,n))));case"nestedmenuitem":return(e=>qn("nestedmenuitem",sy,e))(e).fold(Jb,(e=>A.some(((e,t,o,n=!0,s=!1)=>{const r=s?(a=o.icons,Iy("chevron-down",a,[wb])):(e=>Iy("chevron-right",e,[wb]))(o.icons);var a;const i=Py({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.some(r),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return Dy({data:By(e),getApi:e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t),setIconFill:(t,o)=>{pi(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{kt(e,"fill",o)}))}}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,r,n,s))));case"togglemenuitem":return(e=>qn("togglemenuitem",ry,e))(e).fold(Jb,(e=>A.some(((e,t,o,n=!0)=>{const s=Py({iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,checkMark:A.some(Hy(o.icons)),caret:A.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return fn(Dy({data:By(e),enabled:e.enabled,getApi:e=>({setActive:t=>{dh.set(e,t)},isActive:()=>dh.isOn(e),isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o),{toggling:{toggleClass:vb,toggleOnExecute:!1,selected:e.active}})})(a(e),t,r,n))));case"separator":return(e=>qn("separatormenuitem",Ev,e))(e).fold(Jb,(e=>A.some(aw(e))));case"fancymenuitem":return(e=>qn("fancymenuitem",oy,e))(e).fold(Jb,(e=>((e,t)=>be(rw,e.fancytype).map((o=>o(e,t))))(e,o)));default:return console.error("Unknown item in general menu",e),A.none()}var i},Nw=(e,t,o,n,s,r,a)=>{const i=1===n,l=!i||Yb(e);return we(H(e,(e=>{switch(e.type){case"separator":return(n=e,qn("Autocompleter.Separator",Ev,n)).fold(Jb,(e=>A.some(aw(e))));case"cardmenuitem":return(e=>qn("cardmenuitem",Jv,e))(e).fold(Jb,(e=>A.some(((e,t,o,n)=>{const s={dom:Ly(e.label),optComponents:[A.some({dom:{tag:"div",classes:[kb,Cb]},components:jy(e.items,n)})]};return Dy({data:By({text:A.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>{Rm.set(e,!t),L(Xc(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(Rm)&&Rm.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:A.from(n.itemBehaviours).getOr([])},s,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},s,r,{itemBehaviours:Uy(e.meta,r),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>qn("Autocompleter.Item",Av,e))(e).fold(Jb,(e=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Py({presets:n,textContent:A.none(),htmlContent:o?e.text.map((e=>Wy(e,t))):A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:A.none(),checkMark:A.none(),caret:A.none(),value:e.value},a.providers,i,e.icon);return Dy({data:By(e),enabled:e.enabled,getApi:x({}),onAction:t=>s(e.value,e.meta),onSetup:x(b),triggersSubmenu:!1,itemBehaviours:Uy(e.meta,a)},l,r,a.providers)})(e,t,i,"normal",o,s,r,l))))}var n})))},Vw=(e,t,o,n,s,r)=>{const a=Yb(t),i=we(H(t,(e=>{const t=e=>Rw(e,o,n,(e=>s?!ve(e,"text"):a)(e),s);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)}))),l=(e=>"no-search"===e.searchMode?{menuType:"normal"}:{menuType:"searchable",searchMode:e})(r);return(s?Zb:Qb)(e,a,i,1,l)},zw=e=>Lh.singleData(e.value,e),Hw=(e,t)=>{const o=Es(!1),n=Es(!1),s=ri(Ph.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:kl([Jp("dismissAutocompleter",[Ur(Cr(),(()=>c()))])]),lazySink:t.getSink})),r=()=>Ph.isOpen(s),a=n.get,i=()=>{r()&&Ph.hide(s)},l=()=>Ph.getContent(s).bind((e=>te(e.components(),0))),c=()=>e.execCommand("mceAutocompleterClose"),d=n=>{const r=(n=>{const s=re(n,(e=>A.from(e.columns))).getOr(1);return X(n,(n=>{const r=n.items;return Nw(r,n.matchText,((t,s)=>{const r=e.selection.getRng();((e,t)=>ub(Ve(t.startContainer)).map((t=>{const o=e.createRng();return o.selectNode(t.dom),o})))(e.dom,r).each((r=>{const a={hide:()=>c(),reload:t=>{i(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};o.set(!0),n.onAction(a,r,t,s),o.set(!1)}))}),s,gb.BUBBLE_TO_SANDBOX,t,n.highlightOn)}))})(n);r.length>0?((t,o)=>{var n;(n=Ve(e.getBody()),pi(n,db)).each((n=>{const r=re(t,(e=>A.from(e.columns))).getOr(1);Ph.showMenuAt(s,{anchor:{type:"node",root:Ve(e.getBody()),node:A.from(n)}},((e,t,o,n)=>{const s=Qx(t,n),r=Eb(n);return{data:zw({...e,movement:s,menuBehaviours:iy("auto"!==t?[]:[Yr(((e,t)=>{ay(e,4,r.item).each((({numColumns:t,numRows:o})=>{Pp.setGridSize(e,o,t)}))}))])}),menu:{markers:Eb(n),fakeFocus:o===Iw.ContentFocus}}})(Qb("autocompleter-value",!0,o,r,{menuType:"normal"}),r,Iw.ContentFocus,"normal"))})),l().each(Gm.highlightFirst)})(n,r):i()};e.on("AutocompleterStart",(({lookupData:e})=>{n.set(!0),o.set(!1),d(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>d(e))),e.on("AutocompleterEnd",(()=>{i(),n.set(!1),o.set(!1)}));((e,t)=>{const o=(e,t)=>{Ir(e,Ys(),{raw:t})},n=()=>e.getMenu().bind(Gm.getHighlighted);t.on("keydown",(t=>{const s=t.which;e.isActive()&&(e.isMenuOpen()?13===s?(n().each(Rr),t.preventDefault()):40===s?(n().fold((()=>{e.getMenu().each(Gm.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==s&&38!==s&&39!==s||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==s&&38!==s&&40!==s||e.cancelIfNecessary())})),t.on("NodeChange",(t=>{e.isActive()&&!e.isProcessingAction()&&ub(Ve(t.element)).isNone()&&e.cancelIfNecessary()}))})({cancelIfNecessary:c,isMenuOpen:r,isActive:a,isProcessingAction:o.get,getMenu:l},e)},Lw=["visible","hidden"],Pw=e=>{if(je(e)){const t=It(e,"overflow");return Me(t).length>0&&!R(Lw,t)}return!1},Uw=(e,t)=>ib(e)?(e=>{const t=qc(e,Pw),o=0===t.length?bt(e).map(vt).map((e=>qc(e,Pw))).getOr([]):t;return oe(o).map((e=>({element:e,others:o.slice(1)})))})(t):A.none(),Ww=e=>{const t=[...H(e.others,Jo),en()];return((e,t)=>j(t,((e,t)=>Qo(e,t)),e))(Jo(e.element),t)},jw=(e,t,o)=>hi(e,t,o).isSome(),Gw=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},$w=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?A.none():A.some(t.touches[0])},qw=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=Ql(),o=Es(!1),n=Gw((t=>{e.triggerEvent(pr(),t),o.set(!0)}),400),s=Ds([{key:Hs(),value:e=>($w(e).each((s=>{n.cancel();const r={x:s.clientX,y:s.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(r)})),A.none())},{key:Ls(),value:e=>(n.cancel(),$w(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),A.none())},{key:Ps(),value:s=>(n.cancel(),t.get().filter((e=>Ze(e.target,s.target))).map((t=>o.get()?(s.prevent(),!1):e.triggerEvent(gr(),s))))}]);return{fireIfReady:(e,t)=>be(s,t).bind((t=>t(e)))}})(o),s=H(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>tc(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),r=Ql(),a=tc(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),r.set(setTimeout((()=>{o.triggerEvent(cr(),e)}),0))})),i=tc(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===$m[0]&&!R(["input","textarea"],Ue(e.target))&&!jw(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=tc(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=Ql(),d=tc(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(lr(),e)}),0))}));return{unbind:()=>{L(s,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),r.on(clearTimeout),c.on(clearTimeout)}}},Xw=(e,t)=>{const o=be(e,"target").getOr(t);return Es(o)},Kw=As([{stopped:[]},{resume:["element"]},{complete:[]}]),Yw=(e,t,o,n,s,r)=>{const a=e(t,n),i=((e,t)=>{const o=Es(!1),n=Es(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,s);return a.fold((()=>(r.logEventNoHandlers(t,n),Kw.complete())),(e=>{const o=e.descHandler;return Aa(o)(i),i.isStopped()?(r.logEventStopped(t,e.element,o.purpose),Kw.stopped()):i.isCut()?(r.logEventCut(t,e.element,o.purpose),Kw.complete()):st(e.element).fold((()=>(r.logNoParent(t,e.element,o.purpose),Kw.complete())),(n=>(r.logEventResponse(t,e.element,o.purpose),Kw.resume(n))))}))},Jw=(e,t,o,n,s,r)=>Yw(e,t,o,n,s,r).fold(E,(n=>Jw(e,t,o,n,s,r)),T),Zw=(e,t,o,n,s)=>{const r=Xw(o,n);return Jw(e,t,o,n,r,s)},Qw=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{le(n,((n,s)=>{const r=void 0!==e[s]?e[s]:{};r[o]=((e,t)=>({cHandler:k.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[s]=r}))},unregisterId:t=>{le(e,((e,o)=>{ve(e,t)&&delete e[t]}))},filterByType:t=>be(e,t).map((e=>pe(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>be(e,o).bind((e=>Is(n,(t=>((e,t)=>pa(t).bind((t=>be(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{pa(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const s=(e=>{const t=e.element;return pa(t).getOrThunk((()=>((e,t)=>{const o=la(ua+"uid-");return ga(t,o),o})(0,e.element)))})(n);ye(t,s)&&((e,n)=>{const s=t[n];if(s!==e)throw new Error('The tagId "'+n+'" is already used by: '+na(s.element)+"\nCannot use it for: "+na(e.element)+"\nThe conflicting element is"+(yt(s.element)?" ":" not ")+"already in the DOM");o(e)})(n,s);const r=[n];e.registerId(r,s,n.events),t[s]=n},unregister:o,getById:e=>be(t,e)}},eS=fm({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:fu(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[ys("components",[]),hu("containerBehaviours",[]),ys("events",{}),ys("domModification",{}),ys("eventOrder",{})]}),tS=e=>{const t=t=>st(e.element).fold(E,(e=>Ze(t,e))),o=Qw(),n=(e,n)=>o.find(t,e,n),s=qw(e.element,{triggerEvent:(e,t)=>Si(e,t.target,(o=>((e,t,o,n)=>Zw(e,t,o,o.target,n))(n,e,t,o)))}),r={debugInfo:x("real"),triggerEvent:(e,t,o)=>{Si(e,t,(s=>Zw(n,e,o,t,s)))},triggerFocus:(e,t)=>{pa(e).fold((()=>{Dl(e)}),(o=>{Si(ir(),e,(o=>(((e,t,o,n,s)=>{const r=Xw(o,n);Yw(e,t,o,n,r,s)})(n,ir(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{r.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:ri,buildOrPatch:si,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:E},a=e=>{e.connect(r),$e(e.element)||(o.register(e),L(e.components(),a),r.triggerEvent(br(),e.element,{target:e.element}))},i=e=>{$e(e.element)||(L(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{Ad(e,t)},c=e=>{Bd(e)},d=e=>{const t=o.filter(dr());L(t,(t=>{const o=t.descHandler;Aa(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t,o)=>{const n=(e=>{const t=Es(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:T,event:e,setSource:O("Cannot set source of a broadcasted event"),getSource:O("Cannot get source of a broadcasted event")}})(t);return L(e,(e=>{const t=e.descHandler;Aa(t)(n)})),n.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>sn.error(new Error('Could not find component with uid: "'+e+'" in system.'))),sn.value),h=e=>{const t=pa(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{s.unbind(),Po(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},oS=x([ys("prefix","form-field"),hu("fieldBehaviours",[wm,pu])]),nS=x([ju({schema:[os("dom")],name:"label"}),ju({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[os("text")],name:"aria-descriptor"}),Uu({factory:{sketch:e=>{const t=((e,t)=>{const o={};return le(e,((e,n)=>{R(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[os("factory")],name:"field"})]),sS=bm({name:"FormField",configFields:oS(),partFields:nS(),factory:(e,t,o,n)=>{const s=bu(e.fieldBehaviours,[wm.config({find:t=>om(t,e,"field")}),pu.config({store:{mode:"manual",getValue:e=>wm.getCurrent(e).bind(pu.getValue),setValue:(e,t)=>{wm.getCurrent(e).each((e=>{pu.setValue(e,t)}))}}})]),r=Hr([Yr(((t,o)=>{const n=sm(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=la(e.prefix);n.label().each((e=>{kt(e.element,"for",o),kt(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=la(e.prefix);kt(o.element,"id",n),kt(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>om(t,e,"field"),getLabel:t=>om(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:s,events:r,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}});var rS=Object.freeze({__proto__:null,exhibit:(e,t)=>Ea({attributes:Ds([{key:t.tabAttr,value:"true"}])})}),aS=[ys("tabAttr","data-alloy-tabstop")];const iS=Ol({fields:aS,name:"tabstopping",active:rS});var lS=tinymce.util.Tools.resolve("tinymce.html.Entities");const cS=(e,t,o,n)=>{const s=dS(e,t,o,n);return sS.sketch(s)},dS=(e,t,o,n)=>({dom:uS(o),components:e.toArray().concat([t]),fieldBehaviours:kl(n)}),uS=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),mS=(e,t)=>sS.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ti(t.translate(e))]}),gS=la("form-component-change"),pS=la("form-close"),hS=la("form-cancel"),fS=la("form-action"),bS=la("form-submit"),vS=la("form-block"),yS=la("form-unblock"),xS=la("form-tabchange"),wS=la("form-resize"),SS=["input","textarea"],kS=e=>{const t=Ue(e);return R(SS,t)},CS=(e,t)=>{const o=t.getRoot(e).getOr(e.element);Pa(o,t.invalidClass),t.notify.each((t=>{kS(e.element)&&kt(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{ta(e,t.validHtml)})),t.onValid(e)}))},OS=(e,t,o,n)=>{const s=t.getRoot(e).getOr(e.element);La(s,t.invalidClass),t.notify.each((t=>{kS(e.element)&&kt(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{ta(e,n)})),t.onInvalid(e,n)}))},_S=(e,t,o)=>t.validator.fold((()=>fw(sn.value(!0))),(t=>t.validate(e))),TS=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),_S(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(OS(e,t,0,o),sn.error(o))),(o=>(CS(e,t),sn.value(o)))):sn.error("No longer in system"))));var ES=Object.freeze({__proto__:null,markValid:CS,markInvalid:OS,query:_S,run:TS,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return Ua(o,t.invalidClass)}}),AS=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>Hr([Ur(t.onEvent,(t=>{TS(t,e).get(w)}))].concat(t.validateOnLoad?[Yr((t=>{TS(t,e).get(b)}))]:[])))).getOr({})}),MS=[os("invalidClass"),ys("getRoot",A.none),vs("notify",[ys("aria","alert"),ys("getContainer",A.none),ys("validHtml",""),Di("onValid"),Di("onInvalid"),Di("onValidate")]),vs("validator",[os("validate"),ys("onEvent","input"),ys("validateOnLoad",!0)])];const DS=Ol({fields:MS,name:"invalidating",active:AS,apis:ES,extra:{validation:e=>t=>{const o=pu.getValue(t);return fw(e(o))}}}),BS=Ol({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>Hr([Lr(sr(),E)]),exhibit:()=>Ea({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),FS=la("color-input-change"),IS=la("color-swatch-change"),RS=la("color-picker-cancel"),NS=ju({schema:[os("dom")],name:"label"}),VS=e=>ju({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:Hr([Wr(Hs(),((t,o,n)=>e(t,n)),[t]),Wr(Ws(),((t,o,n)=>e(t,n)),[t]),Wr(js(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),zS=VS("top-left"),HS=VS("top"),LS=VS("top-right"),PS=VS("right"),US=VS("bottom-right"),WS=VS("bottom"),jS=VS("bottom-left");var GS=[NS,VS("left"),PS,HS,WS,zS,LS,jS,US,Uu({name:"thumb",defaults:x({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:Hr([Gr(Hs(),e,"spectrum"),Gr(Ls(),e,"spectrum"),Gr(Ps(),e,"spectrum"),Gr(Ws(),e,"spectrum"),Gr(js(),e,"spectrum"),Gr($s(),e,"spectrum")])})}),Uu({schema:[es("mouseIsDown",(()=>Es(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:kl([Pp.config({mode:"special",onLeft:o=>t.onLeft(o,e),onRight:o=>t.onRight(o,e),onUp:o=>t.onUp(o,e),onDown:o=>t.onDown(o,e)}),oh.config({})]),events:Hr([Ur(Hs(),o),Ur(Ls(),o),Ur(Ws(),o),Ur(js(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}})];const $S=x("slider.change.value"),qS=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?A.some(e.touches[0]).map((e=>$t(e.clientX,e.clientY))):A.none()}{const e=t;return void 0!==e.clientX?A.some(e).map((e=>$t(e.clientX,e.clientY))):A.none()}},XS=e=>e.model.minX,KS=e=>e.model.minY,YS=e=>e.model.minX-1,JS=e=>e.model.minY-1,ZS=e=>e.model.maxX,QS=e=>e.model.maxY,ek=e=>e.model.maxX+1,tk=e=>e.model.maxY+1,ok=(e,t,o)=>t(e)-o(e),nk=e=>ok(e,ZS,XS),sk=e=>ok(e,QS,KS),rk=e=>nk(e)/2,ak=e=>sk(e)/2,ik=e=>e.stepSize,lk=e=>e.snapToGrid,ck=e=>e.snapStart,dk=e=>e.rounded,uk=(e,t)=>void 0!==e[t+"-edge"],mk=e=>uk(e,"left"),gk=e=>uk(e,"right"),pk=e=>uk(e,"top"),hk=e=>uk(e,"bottom"),fk=e=>e.model.value.get(),bk=(e,t)=>({x:e,y:t}),vk=(e,t)=>{Ir(e,$S(),{value:t})},yk=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),xk=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),wk=(e,t,o)=>Math.max(t,Math.min(o,e)),Sk=e=>{const{min:t,max:o,range:n,value:s,step:r,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(s<u)return p;if(s>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(s,u,m),c=wk(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,s)=>s.fold((()=>{const s=e-t,r=Math.round(s/n)*n;return wk(t+r,t-1,o+1)}),(t=>{const s=(e-t)%n,r=Math.round(s/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+r)*n;return Math.max(t,l)})))(c,t,o,r,i):l?Math.round(c):c}},kk=e=>{const{min:t,max:o,range:n,value:s,hasMinEdge:r,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return s<t?r?0:c:s>o?a?i:d:(s-t)/n*l},Ck="top",Ok="right",_k="bottom",Tk="left",Ek=e=>e.element.dom.getBoundingClientRect(),Ak=(e,t)=>e[t],Mk=e=>{const t=Ek(e);return Ak(t,Tk)},Dk=e=>{const t=Ek(e);return Ak(t,Ok)},Bk=e=>{const t=Ek(e);return Ak(t,Ck)},Fk=e=>{const t=Ek(e);return Ak(t,_k)},Ik=e=>{const t=Ek(e);return Ak(t,"width")},Rk=e=>{const t=Ek(e);return Ak(t,"height")},Nk=(e,t,o)=>(e+t)/2-o,Vk=(e,t)=>{const o=Ek(e),n=Ek(t),s=Ak(o,Tk),r=Ak(o,Ok),a=Ak(n,Tk);return Nk(s,r,a)},zk=(e,t)=>{const o=Ek(e),n=Ek(t),s=Ak(o,Ck),r=Ak(o,_k),a=Ak(n,Ck);return Nk(s,r,a)},Hk=(e,t)=>{Ir(e,$S(),{value:t})},Lk=(e,t,o)=>{const n={min:XS(t),max:ZS(t),range:nk(t),value:o,step:ik(t),snap:lk(t),snapStart:ck(t),rounded:dk(t),hasMinEdge:mk(t),hasMaxEdge:gk(t),minBound:Mk(e),maxBound:Dk(e),screenRange:Ik(e)};return Sk(n)},Pk=e=>(t,o)=>((e,t,o)=>{const n=(e>0?xk:yk)(fk(o),XS(o),ZS(o),ik(o));return Hk(t,n),A.some(n)})(e,t,o).map(E),Uk=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=Ik(e),a=n.bind((t=>A.some(Vk(t,e)))).getOr(0),i=s.bind((t=>A.some(Vk(t,e)))).getOr(r),l={min:XS(t),max:ZS(t),range:nk(t),value:o,hasMinEdge:mk(t),hasMaxEdge:gk(t),minBound:Mk(e),minOffset:0,maxBound:Dk(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return kk(l)})(t,r,o,n,s);return Mk(t)-Mk(e)+a},Wk=Pk(-1),jk=Pk(1),Gk=A.none,$k=A.none,qk={"top-left":A.none(),top:A.none(),"top-right":A.none(),right:A.some(((e,t)=>{vk(e,ek(t))})),"bottom-right":A.none(),bottom:A.none(),"bottom-left":A.none(),left:A.some(((e,t)=>{vk(e,YS(t))}))};var Xk=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=Lk(e,t,o);return Hk(e,n),n},setToMin:(e,t)=>{const o=XS(t);Hk(e,o)},setToMax:(e,t)=>{const o=ZS(t);Hk(e,o)},findValueOfOffset:Lk,getValueFromEvent:e=>qS(e).map((e=>e.left)),findPositionOfValue:Uk,setPositionFromValue:(e,t,o,n)=>{const s=fk(o),r=Uk(e,n.getSpectrum(e),s,n.getLeftEdge(e),n.getRightEdge(e),o),a=Jt(t.element)/2;Dt(t.element,"left",r-a+"px")},onLeft:Wk,onRight:jk,onUp:Gk,onDown:$k,edgeActions:qk});const Kk=(e,t)=>{Ir(e,$S(),{value:t})},Yk=(e,t,o)=>{const n={min:KS(t),max:QS(t),range:sk(t),value:o,step:ik(t),snap:lk(t),snapStart:ck(t),rounded:dk(t),hasMinEdge:pk(t),hasMaxEdge:hk(t),minBound:Bk(e),maxBound:Fk(e),screenRange:Rk(e)};return Sk(n)},Jk=e=>(t,o)=>((e,t,o)=>{const n=(e>0?xk:yk)(fk(o),KS(o),QS(o),ik(o));return Kk(t,n),A.some(n)})(e,t,o).map(E),Zk=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=Rk(e),a=n.bind((t=>A.some(zk(t,e)))).getOr(0),i=s.bind((t=>A.some(zk(t,e)))).getOr(r),l={min:KS(t),max:QS(t),range:sk(t),value:o,hasMinEdge:pk(t),hasMaxEdge:hk(t),minBound:Bk(e),minOffset:0,maxBound:Fk(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return kk(l)})(t,r,o,n,s);return Bk(t)-Bk(e)+a},Qk=A.none,eC=A.none,tC=Jk(-1),oC=Jk(1),nC={"top-left":A.none(),top:A.some(((e,t)=>{vk(e,JS(t))})),"top-right":A.none(),right:A.none(),"bottom-right":A.none(),bottom:A.some(((e,t)=>{vk(e,tk(t))})),"bottom-left":A.none(),left:A.none()};var sC=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=Yk(e,t,o);return Kk(e,n),n},setToMin:(e,t)=>{const o=KS(t);Kk(e,o)},setToMax:(e,t)=>{const o=QS(t);Kk(e,o)},findValueOfOffset:Yk,getValueFromEvent:e=>qS(e).map((e=>e.top)),findPositionOfValue:Zk,setPositionFromValue:(e,t,o,n)=>{const s=fk(o),r=Zk(e,n.getSpectrum(e),s,n.getTopEdge(e),n.getBottomEdge(e),o),a=Wt(t.element)/2;Dt(t.element,"top",r-a+"px")},onLeft:Qk,onRight:eC,onUp:tC,onDown:oC,edgeActions:nC});const rC=(e,t)=>{Ir(e,$S(),{value:t})},aC=(e,t)=>({x:e,y:t}),iC=(e,t)=>(o,n)=>((e,t,o,n)=>{const s=e>0?xk:yk,r=t?fk(n).x:s(fk(n).x,XS(n),ZS(n),ik(n)),a=t?s(fk(n).y,KS(n),QS(n),ik(n)):fk(n).y;return rC(o,aC(r,a)),A.some(r)})(e,t,o,n).map(E),lC=iC(-1,!1),cC=iC(1,!1),dC=iC(-1,!0),uC=iC(1,!0),mC={"top-left":A.some(((e,t)=>{vk(e,bk(YS(t),JS(t)))})),top:A.some(((e,t)=>{vk(e,bk(rk(t),JS(t)))})),"top-right":A.some(((e,t)=>{vk(e,bk(ek(t),JS(t)))})),right:A.some(((e,t)=>{vk(e,bk(ek(t),ak(t)))})),"bottom-right":A.some(((e,t)=>{vk(e,bk(ek(t),tk(t)))})),bottom:A.some(((e,t)=>{vk(e,bk(rk(t),tk(t)))})),"bottom-left":A.some(((e,t)=>{vk(e,bk(YS(t),tk(t)))})),left:A.some(((e,t)=>{vk(e,bk(YS(t),ak(t)))}))};var gC=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=Lk(e,t,o.left),s=Yk(e,t,o.top),r=aC(n,s);return rC(e,r),r},setToMin:(e,t)=>{const o=XS(t),n=KS(t);rC(e,aC(o,n))},setToMax:(e,t)=>{const o=ZS(t),n=QS(t);rC(e,aC(o,n))},getValueFromEvent:e=>qS(e),setPositionFromValue:(e,t,o,n)=>{const s=fk(o),r=Uk(e,n.getSpectrum(e),s.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=Zk(e,n.getSpectrum(e),s.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=Jt(t.element)/2,l=Wt(t.element)/2;Dt(t.element,"left",r-i+"px"),Dt(t.element,"top",a-l+"px")},onLeft:lC,onRight:cC,onUp:dC,onDown:uC,edgeActions:mC});const pC=bm({name:"Slider",configFields:[ys("stepSize",1),ys("onChange",b),ys("onChoose",b),ys("onInit",b),ys("onDragStart",b),ys("onDragEnd",b),ys("snapToGrid",!1),ys("rounded",!0),us("snapStart"),ns("model",Jn("mode",{x:[ys("minX",0),ys("maxX",100),es("value",(e=>Es(e.mode.minX))),os("getInitialValue"),Ri("manager",Xk)],y:[ys("minY",0),ys("maxY",100),es("value",(e=>Es(e.mode.minY))),os("getInitialValue"),Ri("manager",sC)],xy:[ys("minX",0),ys("maxX",100),ys("minY",0),ys("maxY",100),es("value",(e=>Es({x:e.mode.minX,y:e.mode.minY}))),os("getInitialValue"),Ri("manager",gC)]})),hu("sliderBehaviours",[Pp,pu]),es("mouseIsDown",(()=>Es(!1)))],partFields:GS,factory:(e,t,o,n)=>{const s=t=>nm(t,e,"thumb"),r=t=>nm(t,e,"spectrum"),a=t=>om(t,e,"left-edge"),i=t=>om(t,e,"right-edge"),l=t=>om(t,e,"top-edge"),c=t=>om(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:r})},g=(e,t)=>{d.value.set(t);const o=s(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&om(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,s(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,s(t)),p(t)};return{uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.sliderBehaviours,[Pp.config({mode:"special",focusIn:t=>om(t,e,"spectrum").map(Pp.focusIn).map(E)}),pu.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),Al.config({channels:{[Jd()]:{onReceive:p}}})]),events:Hr([Ur($S(),((t,o)=>{((t,o)=>{g(t,o);const n=s(t);e.onChange(t,n,o),A.some(!0)})(t,o.event.value)})),Yr(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=s(t);m(t,a);const i=r(t);e.onInit(t,a,i,d.value.get())})),Ur(Hs(),h),Ur(Ps(),f),Ur(Ws(),h),Ur($s(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),hC=la("rgb-hex-update"),fC=la("slider-update"),bC=la("palette-update"),vC="form",yC=[hu("formBehaviours",[pu])],xC=e=>"<alloy.field."+e+">",wC=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.formBehaviours,[pu.config({store:{mode:"manual",getValue:t=>{const o=rm(t,e);return ce(o,((e,t)=>e().bind((e=>{return o=wm.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+na(e.element)),o.fold((()=>sn.error(n)),sn.value);var o,n})).map(pu.getValue)))},setValue:(t,o)=>{le(o,((o,n)=>{om(t,e,n).each((e=>{wm.getCurrent(e).each((e=>{pu.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>om(t,e,o).bind(wm.getCurrent)}}),SC={getField:Ca(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),Ju(vC,xC(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=H(n,(e=>Uu({name:e,pname:xC(e)})));return mm(vC,yC,s,wC,o)}},kC=la("valid-input"),CC=la("invalid-input"),OC=la("validating-input"),_C="colorcustom.rgb.",TC=(e,t,o,n)=>{const s=(o,n)=>DS.config({invalidClass:t("invalid"),notify:{onValidate:e=>{Ir(e,OC,{type:o})},onValid:e=>{Ir(e,kC,{type:o,value:pu.getValue(e)})},onInvalid:e=>{Ir(e,CC,{type:o,value:pu.getValue(e)})}},validator:{validate:t=>{const o=pu.getValue(t),s=n(o)?sn.value(!0):sn.error(e("aria.input.invalid"));return fw(s)},validateOnLoad:!1}}),r=(o,n,r,a,i)=>{const l=e(_C+"range"),c=sS.parts.label({dom:{tag:"label",attributes:{"aria-label":a}},components:[ti(r)]}),d=sS.parts.field({data:i,factory:Nb,inputAttributes:{type:"text",..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:kl([s(n,o),iS.config({})]),onSetValue:e=>{DS.isInvalid(e)&&DS.run(e).get(b)}}),u=[c,d],m="hex"!==n?[sS.parts["aria-descriptor"]({text:l})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(m)}},a=(e,t)=>{const o=t.red,n=t.green,s=t.blue;pu.setValue(e,{red:o,green:n,blue:s})},i=jh({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),l=(e,t)=>{i.getOpt(e).each((e=>{Dt(e.element,"background-color","#"+t.value)}))},c=fm({factory:()=>{const s={red:Es(A.some(255)),green:Es(A.some(255)),blue:Es(A.some(255)),hex:Es(A.some("ffffff"))},c=e=>s[e].get(),d=(e,t)=>{s[e].set(t)},u=e=>{const t=e.red,o=e.green,n=e.blue;d("red",A.some(t)),d("green",A.some(o)),d("blue",A.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?d(o.type,A.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=$y(t);d("hex",A.some(n.value));const s=ix(n);a(e,s),u(s),Ir(e,hC,{hex:n}),l(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);d(t,A.some(n)),c("red").bind((e=>c("green").bind((t=>c("blue").map((o=>sx(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=Zy(t);return SC.getField(e,"hex").each((t=>{oh.isFocused(t)||pu.setValue(e,{hex:o.value})})),o})(e,t);Ir(e,hC,{hex:o}),l(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(_C+t+".label"),description:e(_C+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return fn(SC.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",sS.sketch(r(rx,"red",h.label,h.description,255))),o.field("green",sS.sketch(r(rx,"green",f.label,f.description,255))),o.field("blue",sS.sketch(r(rx,"blue",b.label,b.description,255))),o.field("hex",sS.sketch(r(Ky,"hex",v.label,v.description,"ffffff"))),i.asSpec()],formBehaviours:kl([DS.config({invalidClass:t("form-invalid")}),Jp("rgb-form-events",[Ur(kC,g),Ur(CC,m),Ur(OC,m)])])}))),{apis:{updateHex:(e,t)=>{pu.setValue(e,{hex:t.value}),((e,t)=>{const o=ix(t);a(e,o),u(o)})(e,t),l(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return c},EC=(e,t)=>{const o=fm({name:"ColourPicker",configFields:[os("dom"),ys("onValidHex",b),ys("onInvalidHex",b)],factory:o=>{const n=TC(e,t,o.onValidHex,o.onInvalidHex),s=((e,t)=>{const o=pC.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=pC.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),s=(e,t)=>{const{width:o,height:n}=e,s=e.getContext("2d");if(null===s)return;s.fillStyle=t,s.fillRect(0,0,o,n);const r=s.createLinearGradient(0,0,o,0);r.addColorStop(0,"rgba(255,255,255,1)"),r.addColorStop(1,"rgba(255,255,255,0)"),s.fillStyle=r,s.fillRect(0,0,o,n);const a=s.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),s.fillStyle=a,s.fillRect(0,0,o,n)};return fm({factory:e=>{const r=x({x:0,y:0}),a=kl([wm.config({find:A.some}),oh.config({})]);return pC.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:r},rounded:!1,components:[o,n],onChange:(e,t,o)=>{Ir(e,bC,{value:o})},onInit:(e,t,o,n)=>{s(o.element.dom,dx(ux))},sliderBehaviours:a})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=Ox(t,100,100),r=ax(n);s(o,dx(r))})(t,o)},setThumb:(e,t,o)=>{((e,t)=>{const o=_x(ix(t));pC.setValue(e,{x:o.saturation,y:100-o.value})})(t,o)}},extraApis:{}})})(0,t),r={paletteRgba:Es(ux),paletteHue:Es(0)},a=jh(((e,t)=>{const o=pC.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=pC.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return pC.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:x(0)},components:[o,n],sliderBehaviours:kl([oh.config({})]),onChange:(e,t,o)=>{Ir(e,fC,{value:o})}})})(0,t)),i=jh(s.sketch({})),l=jh(n.sketch({})),c=(e,t,o)=>{i.getOpt(e).each((e=>{s.setHue(e,o)}))},d=(e,t)=>{l.getOpt(e).each((e=>{n.updateHex(e,t)}))},u=(e,t,o)=>{a.getOpt(e).each((e=>{pC.setValue(e,(e=>100-e/360*100)(o))}))},m=(e,t)=>{i.getOpt(e).each((e=>{s.setThumb(e,t)}))},g=(e,t,o,n)=>{((e,t)=>{const o=ix(e);r.paletteRgba.set(o),r.paletteHue.set(t)})(t,o),L(n,(n=>{n(e,t,o)}))};return{uid:o.uid,dom:o.dom,components:[i.asSpec(),a.asSpec(),l.asSpec()],behaviours:kl([Jp("colour-picker-events",[Ur(hC,(()=>{const e=[c,u,m];return(t,o)=>{const n=o.event.hex,s=(e=>_x(ix(e)))(n);g(t,n,s.hue,e)}})()),Ur(bC,(()=>{const e=[d];return(t,o)=>{const n=o.event.value,s=r.paletteHue.get(),a=Ox(s,n.x,100-n.y),i=Tx(a);g(t,i,s,e)}})()),Ur(fC,(()=>{const e=[c,d];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),s=r.paletteRgba.get(),a=_x(s),i=Ox(n,a.saturation,a.value),l=Tx(i);g(t,l,n,e)}})())]),wm.config({find:e=>l.getOpt(e)}),Pp.config({mode:"acyclic"})])}}});return o},AC=()=>wm.config({find:A.some}),MC=e=>wm.config({find:t=>lt(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),DC=Dn([ys("preprocess",w),ys("postprocess",w)]),BC=(e,t,o)=>pu.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),FC=(e,t,o)=>BC(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),IC=(e,t)=>{const o=Kn("RepresentingConfigs.memento processors",DC,t);return pu.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),s=pu.getValue(n);return o.postprocess(s)},setValue:(t,n)=>{const s=o.preprocess(n),r=e.get(t);pu.setValue(r,s)}}})},RC=FC,NC=BC,VC=e=>pu.config({store:{mode:"memory",initialValue:e}}),zC={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var HC=tinymce.util.Tools.resolve("tinymce.Resource"),LC=tinymce.util.Tools.resolve("tinymce.util.Tools");const PC=la("alloy-fake-before-tabstop"),UC=la("alloy-fake-after-tabstop"),WC=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:kl([oh.config({ignore:!0}),iS.config({})])}),jC=e=>({dom:{tag:"div",classes:["tox-navobj"]},components:[WC([PC]),e,WC([UC])],behaviours:kl([MC(1)])}),GC=(e,t)=>{Ir(e,Ys(),{raw:{which:9,shiftKey:t}})},$C=(e,t)=>{const o=t.element;Ua(o,PC)?GC(e,!0):Ua(o,UC)&&GC(e,!1)},qC=e=>jw(e,["."+PC,"."+UC].join(","),T),XC=la("toolbar.button.execute"),KC=la("common-button-display-events"),YC={[ur()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events"],[Sr()]:["toolbar-button-events",KC],[Ws()]:["focusing","alloy.base.behaviour",KC]},JC=e=>Dt(e.element,"width",It(e.element,"width")),ZC=(e,t,o)=>ef(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),QC=(e,t)=>ZC(e,t,[]),eO=(e,t)=>ZC(e,t,[Yp.config({})]),tO=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[ti(o.translate(e))],behaviours:kl([Yp.config({})])}),oO=la("update-menu-text"),nO=la("update-menu-icon"),sO=(e,t,o)=>{const n=Es(b),s=e.text.map((e=>jh(tO(e,t,o.providers)))),r=e.icon.map((e=>jh(eO(e,o.providers.icons)))),a=(e,t)=>{const o=pu.getValue(e);return oh.focus(o),Ir(o,"keydown",{raw:t.event.raw}),Mw.close(o),A.some(!0)},i=e.role.fold((()=>({})),(e=>({role:e}))),l=e.tooltip.fold((()=>({})),(e=>{const t=o.providers.translate(e);return{title:t,"aria-label":t}})),c=ef("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons),d=la("common-button-display-events"),u=jh(Mw.sketch({...e.uid?{uid:e.uid}:{},...i,dom:{tag:"button",classes:[t,`${t}--select`].concat(H(e.classes,(e=>`${t}--${e}`))),attributes:{...l}},components:My([r.map((e=>e.asSpec())),s.map((e=>e.asSpec())),A.some(c)]),matchWidth:!0,useMinWidth:!0,onOpen:(t,o,n)=>{e.searchable&&(e=>{Pb(e).each((e=>oh.focus(e)))})(n)},dropdownBehaviours:kl([...e.dropdownBehaviours,Sy((()=>e.disabled||o.providers.isDisabled())),wy(),BS.config({}),Yp.config({}),Jp("dropdown-events",[_y(e,n),Ty(e,n)]),Jp(d,[Yr(((e,t)=>JC(e)))]),Jp("menubutton-update-display-text",[Ur(oO,((e,t)=>{s.bind((t=>t.getOpt(e))).each((e=>{Yp.set(e,[ti(o.providers.translate(t.event.text))])}))})),Ur(nO,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{Yp.set(e,[eO(t.event.icon,o.providers.icons)])}))}))])]),eventOrder:fn(YC,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"],[Sr()]:["toolbar-button-events","dropdown-events",d]}),sandboxBehaviours:kl([Pp.config({mode:"special",onLeft:a,onRight:a}),Jp("dropdown-sandbox-events",[Ur(Vb,((e,t)=>{(e=>{const t=pu.getValue(e),o=Lb(e).map(Ub);Mw.refetch(t).get((()=>{const e=dw.getCoupled(t,"sandbox");o.each((t=>Lb(e).each((e=>((e,t)=>{pu.setValue(e,t.fetchPattern),e.element.dom.selectionStart=t.selectionStart,e.element.dom.selectionEnd=t.selectionEnd})(e,t)))))}))})(e),t.stop()})),Ur(zb,((e,t)=>{((e,t)=>{(e=>Xd.getState(e).bind(Gm.getHighlighted).bind(Gm.getHighlighted))(e).each((o=>{((e,t,o,n)=>{const s={...n,target:t};e.getSystem().triggerEvent(o,t,s)})(e,o.element,t.event.eventType,t.event.interactionEvent)}))})(e,t),t.stop()}))])]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:{...Db(0,e.columns,e.presets),fakeFocus:e.searchable,onHighlightItem:Dw,onCollapseMenu:(e,t,o)=>{Gm.getHighlighted(o).each((t=>{Dw(e,o,t)}))},onDehighlightItem:Bw}},fetch:t=>hw(k(e.fetch,t))}));return u.asSpec()},rO=e=>"separator"===e.type,aO={type:"separator"},iO=(e,t)=>{const o=((e,t)=>{const o=j(e,((e,o)=>(e=>r(e))(o)?""===o?e:"|"===o?e.length>0&&!rO(e[e.length-1])?e.concat([aO]):e:ve(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&rO(o[o.length-1])&&o.pop(),o})(r(e)?e.split(" "):e,t);return W(o,((e,o)=>{if((e=>ve(e,"getSubmenuItems"))(o)){const n=(e=>{const t=be(e,"value").getOrThunk((()=>la("generated-menu-item")));return fn({value:t},e)})(o),s=((e,t)=>{const o=e.getSubmenuItems(),n=iO(o,t);return{item:e,menus:fn(n.menus,{[e.value]:n.items}),expansions:fn(n.expansions,{[e.value]:e.value})}})(n,t);return{menus:fn(e.menus,s.menus),items:[s.item,...e.items],expansions:fn(e.expansions,s.expansions)}}return{...e,items:[o,...e.items]}}),{menus:{},expansions:{},items:[]})},lO=(e,t,o,n)=>{const s=la("primary-menu"),r=iO(e,o.shared.providers.menuItems());if(0===r.items.length)return A.none();const a=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-field",placeholder:e.placeholder}))))(n),i=Vw(s,r.items,t,o,n.isHorizontalMenu,a),l=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-results"}))))(n),c=ce(r.menus,((e,n)=>Vw(n,e,t,o,!1,l))),d=fn(c,Ms(s,i));return A.from(Lh.tieredData(s,d,r.expansions))},cO=e=>!ve(e,"items"),dO="data-value",uO=(e,t,o,n)=>H(o,(o=>cO(o)?{type:"togglemenuitem",text:o.text,value:o.value,active:o.value===n,onAction:()=>{pu.setValue(e,o.value),Ir(e,gS,{name:t}),oh.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>uO(e,t,o.items,n)})),mO=(e,t)=>re(e,(e=>cO(e)?Ce(e.value===t,e):mO(e.items,t))),gO=fm({name:"HtmlSelect",configFields:[os("options"),hu("selectBehaviours",[oh,pu]),ys("selectClasses",[]),ys("selectAttributes",{}),us("data")],factory:(e,t)=>{const o=H(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>Ms("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:bu(e.selectBehaviours,[oh.config({}),pu.config({store:{mode:"manual",getValue:e=>$a(e.element),setValue:(t,o)=>{const n=oe(e.options);G(e.options,(e=>e.value===o)).isSome()?qa(t.element,o):-1===t.element.dom.selectedIndex&&""===o&&n.each((e=>qa(t.element,e.value)))},...n}})])}}}),pO=x([ys("field1Name","field1"),ys("field2Name","field2"),Fi("onLockedChange"),Ai(["lockClass"]),ys("locked",!1),vu("coupledFieldBehaviours",[wm,pu])]),hO=(e,t)=>Uu({factory:sS,name:e,overrides:e=>({fieldBehaviours:kl([Jp("coupled-input-behaviour",[Ur(Zs(),(o=>{((e,t,o)=>om(e,t,o).bind(wm.getCurrent))(o,e,t).each((t=>{om(o,e,"lock").each((n=>{dh.isOn(n)&&e.onLockedChange(o,t,n)}))}))}))])])})}),fO=x([hO("field1","field2"),hO("field2","field1"),Uu({factory:Wh,schema:[os("dom")],name:"lock",overrides:e=>({buttonBehaviours:kl([dh.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),bO=bm({name:"FormCoupledInputs",configFields:pO(),partFields:fO(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:yu(e.coupledFieldBehaviours,[wm.config({find:A.some}),pu.config({store:{mode:"manual",getValue:t=>{const o=im(t,e,["field1","field2"]);return{[e.field1Name]:pu.getValue(o.field1()),[e.field2Name]:pu.getValue(o.field2())}},setValue:(t,o)=>{const n=im(t,e,["field1","field2"]);ye(o,e.field1Name)&&pu.setValue(n.field1(),o[e.field1Name]),ye(o,e.field2Name)&&pu.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>om(t,e,"field1"),getField2:t=>om(t,e,"field2"),getLock:t=>om(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),vO=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return sn.value({value:e,unit:o})}return sn.error(e)},yO=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>ve(o,e);return e.unit===t?A.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?A.some(e.value):A.some(e.value/o[e.unit]*o[t]):A.none()},xO=e=>A.none(),wO=(e,t)=>{const o=e.label.map((e=>mS(e,t))),n=[Rm.config({disabled:()=>e.disabled||t.isDisabled()}),wy(),Pp.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>(Fr(e,bS),A.some(!0))}),Jp("textfield-change",[Ur(Zs(),((t,o)=>{Ir(t,gS,{name:e.name})})),Ur(cr(),((t,o)=>{Ir(t,gS,{name:e.name})}))]),iS.config({})],s=e.validation.map((e=>DS.config({getRoot:e=>rt(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=pu.getValue(t),n=e.validator(o);return fw(!0===n?sn.value(o):sn.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),r={...e.placeholder.fold(x({}),(e=>({placeholder:t.translate(e)}))),...e.inputMode.fold(x({}),(e=>({inputmode:e})))},a=sS.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:r,inputClasses:[e.classname],inputBehaviours:kl(q([n,s])),selectOnFocus:!1,factory:Nb}),i=e.multiline?{dom:{tag:"div",classes:["tox-textarea-wrap"]},components:[a]}:a,l=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),c=[Rm.config({disabled:()=>e.disabled||t.isDisabled(),onDisabled:e=>{sS.getField(e).each(Rm.disable)},onEnabled:e=>{sS.getField(e).each(Rm.enable)}}),wy()];return cS(o,i,l,c)},SO=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),kO=e=>e.dimension.property,CO=(e,t)=>e.dimension.getDimension(t),OO=(e,t)=>{const o=SO(e,t);ja(o,[t.shrinkingClass,t.growingClass])},_O=(e,t)=>{Pa(e.element,t.openClass),La(e.element,t.closedClass),Dt(e.element,kO(t),"0px"),Lt(e.element)},TO=(e,t)=>{Pa(e.element,t.closedClass),La(e.element,t.openClass),Ht(e.element,kO(t))},EO=(e,t,o,n)=>{o.setCollapsed(),Dt(e.element,kO(t),CO(t,e.element)),OO(e,t),_O(e,t),t.onStartShrink(e),t.onShrunk(e)},AO=(e,t,o,n)=>{const s=n.getOrThunk((()=>CO(t,e.element)));o.setCollapsed(),Dt(e.element,kO(t),s),Lt(e.element);const r=SO(e,t);Pa(r,t.growingClass),La(r,t.shrinkingClass),_O(e,t),t.onStartShrink(e)},MO=(e,t,o)=>{const n=CO(t,e.element);("0px"===n?EO:AO)(e,t,o,A.some(n))},DO=(e,t,o)=>{const n=SO(e,t),s=Ua(n,t.shrinkingClass),r=CO(t,e.element);TO(e,t);const a=CO(t,e.element);(s?()=>{Dt(e.element,kO(t),r),Lt(e.element)}:()=>{_O(e,t)})(),Pa(n,t.shrinkingClass),La(n,t.growingClass),TO(e,t),Dt(e.element,kO(t),a),o.setExpanded(),t.onStartGrow(e)},BO=(e,t,o)=>{const n=SO(e,t);return!0===Ua(n,t.growingClass)},FO=(e,t,o)=>{const n=SO(e,t);return!0===Ua(n,t.shrinkingClass)};var IO=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){Ht(e.element,kO(t));const o=CO(t,e.element);Dt(e.element,kO(t),o)}},grow:(e,t,o)=>{o.isExpanded()||DO(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&MO(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&EO(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:BO,isShrinking:FO,isTransitioning:(e,t,o)=>BO(e,t)||FO(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?MO:DO)(e,t,o)},disableTransitions:OO,immediateGrow:(e,t,o)=>{o.isExpanded()||(TO(e,t),Dt(e.element,kO(t),CO(t,e.element)),OO(e,t),o.setExpanded(),t.onStartGrow(e),t.onGrown(e))}}),RO=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return Ea(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:Ms(t.dimension.property,"0px")})},events:(e,t)=>Hr([Kr(or(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(OO(o,e),t.isExpanded()&&Ht(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),NO=[os("closedClass"),os("openClass"),os("shrinkingClass"),os("growingClass"),us("getAnimationRoot"),Di("onShrunk"),Di("onStartShrink"),Di("onGrown"),Di("onStartGrow"),ys("expanded",!1),ns("dimension",Jn("property",{width:[Ri("property","width"),Ri("getDimension",(e=>Jt(e)+"px"))],height:[Ri("property","height"),Ri("getDimension",(e=>Wt(e)+"px"))]}))];const VO=Ol({fields:NO,name:"sliding",active:RO,apis:IO,state:Object.freeze({__proto__:null,init:e=>{const t=Es(e.expanded);return _a({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:k(t.set,!1),setExpanded:k(t.set,!0),readState:()=>"expanded: "+t.get()})}})}),zO=e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t),setActive:t=>{const o=e.element;t?(La(o,"tox-tbtn--enabled"),kt(o,"aria-pressed",!0)):(Pa(o,"tox-tbtn--enabled"),Et(o,"aria-pressed"))},isActive:()=>Ua(e.element,"tox-tbtn--enabled"),setText:t=>{Ir(e,oO,{text:t})},setIcon:t=>Ir(e,nO,{icon:t})}),HO=(e,t,o,n,s=!0)=>sO({text:e.text,icon:e.icon,tooltip:e.tooltip,searchable:e.search.isSome(),role:n,fetch:(t,n)=>{const s={pattern:e.search.isSome()?Fw(t):""};e.fetch((t=>{n(lO(t,gb.CLOSE_ON_EXECUTE,o,{isHorizontalMenu:!1,search:e.search}))}),s,zO(t))},onSetup:e.onSetup,getApi:zO,columns:1,presets:"normal",classes:[],dropdownBehaviours:[...s?[iS.config({})]:[]]},t,o.shared),LO=(e,t,o)=>{const n=e=>n=>{const s=!n.isActive();n.setActive(s),e.storage.set(s),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{Dl(t.element),Ir(t,fS,{name:e.name,value:e.storage.get()})}))}))},s=e=>t=>{t.setActive(e.storage.get())};return t=>{t(H(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,onAction:n(e),onSetup:s(e)}})))}},PO=e=>({dom:{tag:"span",classes:["tox-tree__label"],attributes:{title:e,"aria-label":e}},components:[ti(e)]}),UO=la("leaf-label-event-id"),WO=({leaf:e,onLeafAction:t,visible:o,treeId:n,selectedId:s,backstage:r})=>{const a=e.menu.map((e=>HO(e,"tox-mbtn",r,A.none(),o))),i=[PO(e.title)];return a.each((e=>i.push(e))),Wh.sketch({dom:{tag:"div",classes:["tox-tree--leaf__label","tox-trbtn"].concat(o?["tox-tree--leaf__label--visible"]:[])},components:i,role:"treeitem",action:o=>{t(e.id),o.getSystem().broadcastOn([`update-active-item-${n}`],{value:e.id})},eventOrder:{[Ys()]:[UO,"keying"]},buttonBehaviours:kl([...o?[iS.config({})]:[],dh.config({toggleClass:"tox-trbtn--enabled",toggleOnExecute:!1,aria:{mode:"selected"}}),Al.config({channels:{[`update-active-item-${n}`]:{onReceive:(t,o)=>{(o.value===e.id?dh.on:dh.off)(t)}}}}),Jp(UO,[Yr(((t,o)=>{s.each((o=>{(o===e.id?dh.on:dh.off)(t)}))})),Ur(Ys(),((e,t)=>{const o="ArrowLeft"===t.event.raw.code,n="ArrowRight"===t.event.raw.code;o?(mi(e.element,".tox-tree--directory").each((t=>{e.getSystem().getByDom(t).each((e=>{gi(t,".tox-tree--directory__label").each((t=>{e.getSystem().getByDom(t).each(oh.focus)}))}))})),t.stop()):n&&t.stop()}))])])})},jO=la("directory-label-event-id"),GO=({directory:e,visible:t,noChildren:o,backstage:n})=>{const s=e.menu.map((e=>HO(e,"tox-mbtn",n,A.none()))),r=[{dom:{tag:"div",classes:["tox-chevron"]},components:[(a="chevron-right",i=n.shared.providers.icons,((e,t,o)=>ef(e,{tag:"span",classes:["tox-tree__icon-wrap","tox-icon"],behaviours:[]},t))(a,i))]},PO(e.title)];var a,i;s.each((e=>{r.push(e)}));const l=t=>{mi(t.element,".tox-tree--directory").each((o=>{t.getSystem().getByDom(o).each((o=>{const n=!dh.isOn(o);dh.toggle(o),Ir(t,"expand-tree-node",{expanded:n,node:e.id})}))}))};return Wh.sketch({dom:{tag:"div",classes:["tox-tree--directory__label","tox-trbtn"].concat(t?["tox-tree--directory__label--visible"]:[])},components:r,action:l,eventOrder:{[Ys()]:[jO,"keying"]},buttonBehaviours:kl([...t?[iS.config({})]:[],Jp(jO,[Ur(Ys(),((e,t)=>{const n="ArrowRight"===t.event.raw.code,s="ArrowLeft"===t.event.raw.code;n&&o&&t.stop(),(n||s)&&mi(e.element,".tox-tree--directory").each((o=>{e.getSystem().getByDom(o).each((o=>{!dh.isOn(o)&&n||dh.isOn(o)&&s?(l(e),t.stop()):s&&!dh.isOn(o)&&(mi(o.element,".tox-tree--directory").each((e=>{gi(e,".tox-tree--directory__label").each((e=>{o.getSystem().getByDom(e).each(oh.focus)}))})),t.stop())}))}))}))])])})},$O=({children:e,onLeafAction:t,visible:o,treeId:n,expandedIds:s,selectedId:r,backstage:a})=>({dom:{tag:"div",classes:["tox-tree--directory__children"]},components:e.map((e=>"leaf"===e.type?WO({leaf:e,selectedId:r,onLeafAction:t,visible:o,treeId:n,backstage:a}):XO({directory:e,expandedIds:s,selectedId:r,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:a}))),behaviours:kl([VO.config({dimension:{property:"height"},closedClass:"tox-tree--directory__children--closed",openClass:"tox-tree--directory__children--open",growingClass:"tox-tree--directory__children--growing",shrinkingClass:"tox-tree--directory__children--shrinking",expanded:o}),Yp.config({})])}),qO=la("directory-event-id"),XO=({directory:e,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:s,expandedIds:r,selectedId:a})=>{const{children:i}=e,l=Es(r),c=r.includes(e.id);return{dom:{tag:"div",classes:["tox-tree--directory"],attributes:{role:"treeitem"}},components:[GO({directory:e,visible:o,noChildren:0===e.children.length,backstage:s}),$O({children:i,expandedIds:r,selectedId:a,onLeafAction:t,visible:c,treeId:n,backstage:s})],behaviours:kl([Jp(qO,[Yr(((e,t)=>{dh.set(e,c)})),Ur("expand-tree-node",((e,t)=>{const{expanded:o,node:n}=t.event;l.set(o?[...l.get(),n]:l.get().filter((e=>e!==n)))}))]),dh.config({...e.children.length>0?{aria:{mode:"expanded"}}:{},toggleClass:"tox-tree--directory--expanded",onToggled:(e,o)=>{const r=e.components()[1],c=(d=o,i.map((e=>"leaf"===e.type?WO({leaf:e,selectedId:a,onLeafAction:t,visible:d,treeId:n,backstage:s}):XO({directory:e,expandedIds:l.get(),selectedId:a,onLeafAction:t,labelTabstopping:d,treeId:n,backstage:s}))));var d;o?VO.grow(r):VO.shrink(r),Yp.set(r,c)}})])}},KO=la("tree-event-id");var YO=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return Hr([Ur(e.event,o),Jr((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[Ur(e,(()=>t.cancel()))])).getOr([])))}});const JO=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...s)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,s)}),t)}}},ZO=e=>{const t=Es(null);return _a({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var QO=Object.freeze({__proto__:null,throttle:ZO,init:e=>e.stream.streams.state(e)}),e_=[ns("stream",Jn("mode",{throttle:[os("delay"),ys("stopEvent",!0),Ri("streams",{setup:(e,t)=>{const o=e.stream,n=JO(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:ZO})]})),ys("event","input"),us("cancelEvent"),Fi("onStream")];const t_=Ol({fields:e_,name:"streaming",active:YO,state:QO}),o_=(e,t,o)=>{const n=pu.getValue(o);pu.setValue(t,n),s_(t)},n_=(e,t)=>{const o=e.element,n=$a(o),s=o.dom;"number"!==Ot(o,"type")&&t(s,n)},s_=e=>{n_(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},r_=x("alloy.typeahead.itemexecute"),a_=x([us("lazySink"),os("fetch"),ys("minChars",5),ys("responseTime",1e3),Di("onOpen"),ys("getHotspot",A.some),ys("getAnchorOverrides",x({})),ys("layouts",A.none()),ys("eventOrder",{}),Ts("model",{},[ys("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),ys("selectsOver",!0),ys("populateFromBrowse",!0)]),Di("onSetValue"),Bi("onExecute"),Di("onItemExecute"),ys("inputClasses",[]),ys("inputAttributes",{}),ys("inputStyles",{}),ys("matchWidth",!0),ys("useMinWidth",!1),ys("dismissOnBlur",!0),Ai(["openClass"]),us("initialData"),hu("typeaheadBehaviours",[oh,pu,t_,Pp,dh,dw]),es("lazyTypeaheadComp",(()=>Es(A.none))),es("previewing",(()=>Es(!0)))].concat(Bb()).concat(Tw())),i_=x([Wu({schema:[Ei()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlightItem:(t,o,n)=>{e.previewing.get()?e.lazyTypeaheadComp.get().each((t=>{((e,t,o)=>{if(e.selectsOver){const n=pu.getValue(t),s=e.getDisplayText(n),r=pu.getValue(o);return 0===e.getDisplayText(r).indexOf(s)?A.some((()=>{o_(0,t,o),((e,t)=>{n_(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,s.length)})):A.none()}return A.none()})(e.model,t,n).fold((()=>{e.model.selectsOver?(Gm.dehighlight(o,n),e.previewing.set(!0)):e.previewing.set(!1)}),(t=>{t(),e.previewing.set(!1)}))})):e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&o_(e.model,t,n),_t(n.element,"id").each((e=>kt(t.element,"aria-activedescendant",e)))}))},onExecute:(t,o)=>e.lazyTypeaheadComp.get().map((e=>(Ir(e,r_(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&o_(e.model,t,o)}))}})})]),l_=bm({name:"Typeahead",configFields:a_(),partFields:i_(),factory:(e,t,o,n)=>{const s=(t,o,s)=>{e.previewing.set(!1);const r=dw.getCoupled(t,"sandbox");if(Xd.isOpen(r))wm.getCurrent(r).each((e=>{Gm.getHighlighted(e).fold((()=>{s(e)}),(()=>{zr(r,e.element,"keydown",o)}))}));else{const o=e=>{wm.getCurrent(e).each(s)};xw(e,a(t),t,r,n,o,zh.HighlightMenuAndItem).get(b)}},r=Fb(e),a=e=>t=>t.map((t=>{const o=fe(t.menus),n=X(o,(e=>U(e.items,(e=>"item"===e.type))));return pu.getState(e).update(H(n,(e=>e.data))),t})),i=e=>wm.getCurrent(e),l="typeaheadevents",c=[oh.config({}),pu.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>$a(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{qa(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>Ms("initialValue",e))).getOr({})}}),t_.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const s=dw.getCoupled(t,"sandbox");if(oh.isFocused(t)&&$a(t.element).length>=e.minChars){const o=i(s).bind((e=>Gm.getHighlighted(e).map(pu.getValue)));e.previewing.set(!0);const r=t=>{i(s).each((t=>{o.fold((()=>{e.model.selectsOver&&Gm.highlightFirst(t)}),(e=>{Gm.highlightBy(t,(t=>pu.getValue(t).value===e.value)),Gm.getHighlighted(t).orThunk((()=>(Gm.highlightFirst(t),A.none())))}))}))};xw(e,a(t),t,s,n,r,zh.HighlightJustMenu).get(b)}},cancelEvent:fr()}),Pp.config({mode:"special",onDown:(e,t)=>(s(e,t,Gm.highlightFirst),A.some(!0)),onEscape:e=>{const t=dw.getCoupled(e,"sandbox");return Xd.isOpen(t)?(Xd.close(t),A.some(!0)):A.none()},onUp:(e,t)=>(s(e,t,Gm.highlightLast),A.some(!0)),onEnter:t=>{const o=dw.getCoupled(t,"sandbox"),n=Xd.isOpen(o);if(n&&!e.previewing.get())return i(o).bind((e=>Gm.getHighlighted(e))).map((e=>(Ir(t,r_(),{item:e}),!0)));{const s=pu.getValue(t);return Fr(t,fr()),e.onExecute(o,t,s),n&&Xd.close(o),A.some(!0)}}}),dh.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),dw.config({others:{sandbox:t=>Ow(e,t,{onOpen:()=>dh.on(t),onClose:()=>{e.lazyTypeaheadComp.get().each((e=>Et(e.element,"aria-activedescendant"))),dh.off(t)}})}}),Jp(l,[Yr((t=>{e.lazyTypeaheadComp.set(A.some(t))})),Jr((t=>{e.lazyTypeaheadComp.set(A.none())})),Qr((t=>{const o=b;Sw(e,a(t),t,n,o,zh.HighlightMenuAndItem).get(b)})),Ur(r_(),((t,o)=>{const n=dw.getCoupled(t,"sandbox");o_(e.model,t,o.event.item),Fr(t,fr()),e.onItemExecute(t,n,o.event.item,pu.getValue(t)),Xd.close(n),s_(t)}))].concat(e.dismissOnBlur?[Ur(lr(),(e=>{const t=dw.getCoupled(e,"sandbox");Rl(t.element).isNone()&&Xd.close(t)}))]:[]))],d={[kr()]:[pu.name(),t_.name(),l],...e.eventOrder};return{uid:e.uid,dom:Rb(fn(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...r,...bu(e.typeaheadBehaviours,c)},eventOrder:d}}}),c_=e=>({...e,toCached:()=>c_(e.toCached()),bindFuture:t=>c_(e.bind((e=>e.fold((e=>fw(sn.error(e))),(e=>t(e)))))),bindResult:t=>c_(e.map((e=>e.bind(t)))),mapResult:t=>c_(e.map((e=>e.map(t)))),mapError:t=>c_(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>c_(hw((n=>{let s=!1;const r=setTimeout((()=>{s=!0,n(sn.error(o()))}),t);e.get((e=>{s||(clearTimeout(r),n(e))}))})))}),d_=e=>c_(hw(e)),u_=(e,t,o=[],n,s,r)=>{const a=t.fold((()=>({})),(e=>({action:e}))),i={buttonBehaviours:kl([Sy((()=>!e.enabled||r.isDisabled())),wy(),iS.config({}),Jp("button press",[Pr("click"),Pr("mousedown")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...a},l=fn(i,{dom:n});return fn(l,{components:s})},m_=(e,t,o,n=[])=>{const s={tag:"button",classes:["tox-tbtn"],attributes:e.tooltip.map((e=>({"aria-label":o.translate(e),title:o.translate(e)}))).getOr({})},r=e.icon.map((e=>QC(e,o.icons))),a=My([r]);return u_(e,t,n,s,a,o)},g_=e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}},p_=(e,t,o,n=[],s=[])=>{const r=o.translate(e.text),a=e.icon.map((e=>QC(e,o.icons))),i=[a.getOrThunk((()=>ti(r)))],l=e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary"),c=[...g_(l),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...s];return u_(e,t,n,{tag:"button",classes:c,attributes:{title:r}},i,o)},h_=(e,t,o,n=[],s=[])=>{const r=p_(e,A.some(t),o,n,s);return Wh.sketch(r)},f_=(e,t)=>o=>{"custom"===t?Ir(o,fS,{name:e,value:{}}):"submit"===t?Fr(o,bS):"cancel"===t?Fr(o,hS):console.error("Unknown button type: ",t)},b_=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>r,n=e,s={...e,type:"menubutton",search:A.none(),onSetup:t=>(t.setEnabled(e.enabled),b),fetch:LO(n.items,t,o)},r=jh(HO(s,"tox-tbtn",o,A.none()));return r.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=f_(e.name,t),s={...e,borderless:!1};return h_(s,n,o.shared.providers,[])}if(((e,t)=>"togglebutton"===t)(0,t))return((e,t)=>{var o,n;const s=e.icon.map((e=>eO(e,t.icons))).map(jh),r=e.buttonType.getOr(e.primary?"primary":"secondary"),a={...e,name:null!==(o=e.name)&&void 0!==o?o:"",primary:"primary"===r,tooltip:A.from(e.tooltip),enabled:null!==(n=e.enabled)&&void 0!==n&&n,borderless:!1},i=a.tooltip.map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),l=g_(null!=r?r:"secondary"),c=e.icon.isSome()&&e.text.isSome(),d={tag:"button",classes:[...l.concat(e.icon.isSome()?["tox-button--icon"]:[]),...e.active?["tox-button--enabled"]:[],...c?["tox-button--icon-and-text"]:[]],attributes:i},u=t.translate(e.text.getOr("")),m=ti(u),g=[...My([s.map((e=>e.asSpec()))]),...e.text.isSome()?[m]:[]],p=u_(a,A.some((o=>{Ir(o,fS,{name:e.name,value:{setIcon:e=>{s.map((n=>n.getOpt(o).each((o=>{Yp.set(o,[eO(e,t.icons)])}))))}}})})),[],d,g,t);return Wh.sketch(p)})(e,o.shared.providers);throw console.error("Unknown footer button type: ",t),new Error("Unknown footer button type")},v_={type:"separator"},y_=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),x_=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),w_=(e,t)=>(e=>H(e,y_))(((e,t)=>U(t,(t=>t.type===e)))(e,t)),S_=e=>w_("header",e.targets),k_=e=>w_("anchor",e.targets),C_=e=>A.from(e.anchorTop).map((e=>x_("<top>",e))).toArray(),O_=e=>A.from(e.anchorBottom).map((e=>x_("<bottom>",e))).toArray(),__=(e,t)=>{const o=e.toLowerCase();return U(t,(e=>{var t;const n=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text,s=null!==(t=e.value)&&void 0!==t?t:"";return Te(n.toLowerCase(),o)||Te(s.toLowerCase(),o)}))},T_=la("aria-invalid"),E_=(e,t)=>{e.dom.checked=t},A_=e=>e.dom.checked,M_=e=>(t,o,n,s)=>be(o,"name").fold((()=>e(o,s,A.none())),(r=>t.field(r,e(o,s,be(n,r))))),D_={bar:M_(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:H(e.items,t.interpreter)}))(e,t.shared))),collection:M_(((e,t,o)=>((e,t,o)=>{const n=e.label.map((e=>mS(e,t))),s=e=>(t,o)=>{hi(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,Ot(n,"data-collection-item-value"))}))},r=s(((o,n,s,r)=>{n.stop(),t.isDisabled()||Ir(o,fS,{name:e.name,value:r})})),a=[Ur(qs(),s(((e,t,o)=>{Dl(o)}))),Ur(er(),r),Ur(gr(),r),Ur(Xs(),s(((e,t,o)=>{pi(e.element,"."+Sb).each((e=>{Pa(e,Sb)})),La(o,Sb)}))),Ur(Ks(),s((e=>{pi(e.element,"."+Sb).each((e=>{Pa(e,Sb)}))}))),Qr(s(((t,o,n,s)=>{Ir(t,fS,{name:e.name,value:s})})))],i=(e,t)=>H(Xc(e.element,".tox-collection__item"),t),l=sS.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:w},behaviours:kl([Rm.config({disabled:t.isDisabled,onDisabled:e=>{i(e,(e=>{La(e,"tox-collection__item--state-disabled"),kt(e,"aria-disabled",!0)}))},onEnabled:e=>{i(e,(e=>{Pa(e,"tox-collection__item--state-disabled"),Et(e,"aria-disabled")}))}}),wy(),Yp.config({}),pu.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const s=H(n,(o=>{const n=Gh.translate(o.text),s=1===e.columns?`<div class="tox-collection__item-label">${n}</div>`:"",r=`<div class="tox-collection__item-icon">${o.icon}</div>`,a={_:" "," - ":" ","-":" "},i=n.replace(/\_| \- |\-/g,(e=>a[e]));return`<div class="tox-collection__item${t.isDisabled()?" tox-collection__item--state-disabled":""}" tabindex="-1" data-collection-item-value="${lS.encodeAllRaw(o.value)}" title="${i}" aria-label="${i}">${r}${s}</div>`})),r="auto"!==e.columns&&e.columns>1?z(s,e.columns):[s],a=H(r,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));ta(o.element,a.join(""))})(o,n),"auto"===e.columns&&ay(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{Pp.setGridSize(o,e,t)})),Fr(o,wS)}}),iS.config({}),Pp.config((c=e.columns,1===c?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===c?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${hb}`}})),Jp("collection-events",a)]),eventOrder:{[ur()]:["disabling","alloy.base.behaviour","collection-events"]}});var c;return cS(n,l,["tox-form__group--collection"],[])})(e,t.shared.providers,o))),alertbanner:M_(((e,t)=>((e,t)=>eS.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[Wh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:Jh(e.icon,t.icons),attributes:{title:t.translate(e.iconTooltip)}},action:t=>{Ir(t,fS,{name:"alert-banner",value:e.url})},buttonBehaviours:kl([Zh()])})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]}))(e,t.shared.providers))),input:M_(((e,t,o)=>((e,t,o)=>wO({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),textarea:M_(((e,t,o)=>((e,t,o)=>wO({name:e.name,multiline:!0,label:e.label,inputMode:A.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),label:M_(((e,t)=>((e,t)=>{return{dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"label",classes:["tox-label"]},components:[ti(t.providers.translate(e.label))]},...H(e.items,t.interpreter)],behaviours:kl([AC(),Yp.config({}),(o=A.none(),FC(o,ea,ta)),Pp.config({mode:"acyclic"})])};var o})(e,t.shared))),iframe:(oE=(e,t,o)=>((e,t,o)=>{const n=e.sandboxed,s=e.transparent,r="tox-dialog__iframe",a={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...n?{sandbox:"allow-scripts allow-same-origin"}:{}},i=(e=>{const t=Es(e.getOr(""));return{getValue:e=>t.get(),setValue:(e,o)=>{t.get()!==o&&kt(e.element,"srcdoc",o),t.set(o)}}})(o),l=e.label.map((e=>mS(e,t))),c=sS.parts.field({factory:{sketch:e=>jC({uid:e.uid,dom:{tag:"iframe",attributes:a,classes:s?[r]:[r,`${r}--opaque`]},behaviours:kl([iS.config({}),oh.config({}),NC(o,i.getValue,i.setValue)])})}});return cS(l,c,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n)=>{const s=fn(t,{source:"dynamic"});return M_(oE)(e,s,o,n)}),button:M_(((e,t)=>((e,t)=>{const o=f_(e.name,"custom");return n=A.none(),s=sS.parts.field({factory:Wh,...p_(e,A.some(o),t,[VC(""),AC()])}),cS(n,s,[],[]);var n,s})(e,t.shared.providers))),checkbox:M_(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),A.some(!0)),s=sS.parts.field({factory:{sketch:w},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:kl([AC(),Rm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{rt(e.element).each((e=>La(e,"tox-checkbox--disabled")))},onEnabled:e=>{rt(e.element).each((e=>Pa(e,"tox-checkbox--disabled")))}}),iS.config({}),oh.config({}),RC(o,A_,E_),Pp.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),Jp("checkbox-events",[Ur(Qs(),((t,o)=>{Ir(t,gS,{name:e.name})}))])])}),r=sS.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[ti(t.translate(e.label))],behaviours:kl([BS.config({})])}),a=e=>ef("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=jh({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return sS.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[s,i.asSpec(),r],fieldBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled()}),wy()])})})(e,t.shared.providers,o))),colorinput:M_(((e,t,o)=>((e,t,o,n)=>{const s=sS.parts.field({factory:Nb,inputClasses:["tox-textfield"],data:n,onSetValue:e=>DS.run(e).get(b),inputBehaviours:kl([Rm.config({disabled:t.providers.isDisabled}),wy(),iS.config({}),DS.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>rt(e.element),notify:{onValid:e=>{const t=pu.getValue(e);Ir(e,FS,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=pu.getValue(e);if(0===t.length)return fw(sn.value(!0));{const e=Re("span");Dt(e,"background-color",t);const o=Nt(e,"background-color").fold((()=>sn.error("blah")),(e=>sn.value(t)));return fw(o)}}}})]),selectOnFocus:!1}),r=e.label.map((e=>mS(e,t.providers))),a=(e,t)=>{Ir(e,IS,{value:t})},i=jh(((e,t)=>Mw.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:kl([Sy(t.providers.isDisabled),wy(),BS.config({}),iS.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>hw((t=>e.fetch(t))).map((n=>A.from(zw(fn(Jx(la("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,gb.CLOSE_ON_EXECUTE,T,t.providers),{movement:Qx(e.columns,e.presets)}))))),parts:{menu:Db(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[rl,sl,cl],onLtr:()=>[sl,rl,cl]},components:[],fetch:Gx(o.getColors(e.storageKey),e.storageKey,o.hasCustomColors()),columns:o.getColorCols(e.storageKey),presets:"color",onItemAction:(t,n)=>{i.getOpt(t).each((t=>{"custom"===n?o.colorPicker((o=>{o.fold((()=>Fr(t,RS)),(o=>{a(t,o),Cx(e.storageKey,o)}))}),"#ffffff"):a(t,"remove"===n?"":n)}))}},t));return sS.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[s,i.asSpec()]}]),fieldBehaviours:kl([Jp("form-field-events",[Ur(FS,((t,o)=>{i.getOpt(t).each((e=>{Dt(e.element,"background-color",o.event.color)})),Ir(t,gS,{name:e.name})})),Ur(IS,((e,t)=>{sS.getField(e).each((o=>{pu.setValue(o,t.event.value),wm.getCurrent(e).each(oh.focus)}))})),Ur(RS,((e,t)=>{sS.getField(e).each((t=>{wm.getCurrent(e).each(oh.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:M_(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,s=EC((e=>t=>e.translate(zC[t]))(t),n),r=jh(s.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{Ir(e,fS,{name:"hex-valid",value:!0})},onInvalidHex:e=>{Ir(e,fS,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:kl([NC(o,(e=>{const t=r.get(e);return wm.getCurrent(t).bind((e=>pu.getValue(e).hex)).map((e=>"#"+_e(e,"#"))).getOr("")}),((e,t)=>{const o=A.from(/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t)).bind((e=>te(e,1))),n=r.get(e);wm.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{pu.setValue(e,{hex:o.getOr("")}),SC.getField(e,"hex").each((e=>{Fr(e,Zs())}))}))})),AC()])}})(0,t.shared.providers,o))),dropzone:M_(((e,t,o)=>((e,t,o)=>{const n=(e,t)=>{t.stop()},s=e=>(t,o)=>{L(e,(e=>{e(t,o)}))},r=(e,t)=>{var o;if(!Rm.isDisabled(e)){const n=t.event.raw;i(e,null===(o=n.dataTransfer)||void 0===o?void 0:o.files)}},a=(e,t)=>{const o=t.event.raw.target;i(e,o.files)},i=(o,n)=>{n&&(pu.setValue(o,((e,t)=>{const o=LC.explode(t.getOption("images_file_types"));return U(se(e),(e=>N(o,(t=>Ae(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(n,t)),Ir(o,gS,{name:e.name}))},l=jh({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:kl([Jp("input-file-events",[qr(er()),qr(gr())])])}),c=e.label.map((e=>mS(e,t))),d=sS.parts.field({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:kl([VC(o.getOr([])),AC(),Rm.config({}),dh.config({toggleClass:"dragenter",toggleOnExecute:!1}),Jp("dropzone-events",[Ur("dragenter",s([n,dh.toggle])),Ur("dragleave",s([n,dh.toggle])),Ur("dragover",n),Ur("drop",s([n,r])),Ur(Qs(),a)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[ti(t.translate("Drop an image here"))]},Wh.sketch({dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[ti(t.translate("Browse for an image")),l.asSpec()],action:e=>{l.get(e).element.dom.click()},buttonBehaviours:kl([iS.config({}),Sy(t.isDisabled),wy()])})]}]})}});return cS(c,d,["tox-form__group--stretched"],[])})(e,t.shared.providers,o))),grid:M_(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:H(e.items,t.interpreter)}))(e,t.shared))),listbox:M_(((e,t,o)=>((e,t,o)=>{const n=t.shared.providers,s=o.bind((t=>mO(e.items,t))).orThunk((()=>oe(e.items).filter(cO))),r=e.label.map((e=>mS(e,n))),a=sS.parts.field({dom:{},factory:{sketch:o=>sO({uid:o.uid,text:s.map((e=>e.text)),icon:A.none(),tooltip:e.label,role:A.none(),fetch:(o,n)=>{const s=uO(o,e.name,e.items,pu.getValue(o));n(lO(s,gb.CLOSE_ON_EXECUTE,t,{isHorizontalMenu:!1,search:A.none()}))},onSetup:x(b),getApi:x({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[iS.config({}),NC(s.map((e=>e.value)),(e=>Ot(e.element,dO)),((t,o)=>{mO(e.items,o).each((e=>{kt(t.element,dO,e.value),Ir(t,oO,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),i={dom:{tag:"div",classes:["tox-listboxfield"]},components:[a]};return sS.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([r.toArray(),[i]]),fieldBehaviours:kl([Rm.config({disabled:x(!e.enabled),onDisabled:e=>{sS.getField(e).each(Rm.disable)},onEnabled:e=>{sS.getField(e).each(Rm.enable)}})])})})(e,t,o))),selectbox:M_(((e,t,o)=>((e,t,o)=>{const n=H(e.items,(e=>({text:t.translate(e.text),value:e.value}))),s=e.label.map((e=>mS(e,t))),r=sS.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:gO,selectBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled()}),iS.config({}),Jp("selectbox-change",[Ur(Qs(),((t,o)=>{Ir(t,gS,{name:e.name})}))])])}),a=e.size>1?A.none():A.some(ef("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:q([[r],a.toArray()])};return sS.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([s.toArray(),[i]]),fieldBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{sS.getField(e).each(Rm.disable)},onEnabled:e=>{sS.getField(e).each(Rm.enable)}}),wy()])})})(e,t.shared.providers,o))),sizeinput:M_(((e,t)=>((e,t)=>{let o=xO;const n=la("ratio-event"),s=e=>ef(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),r=bO.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:t.translate(e.label.getOr("Constrain proportions"))}},components:[s("lock"),s("unlock")],buttonBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled()}),wy(),iS.config({})])}),a=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),i=o=>sS.parts.field({factory:Nb,inputClasses:["tox-textfield"],inputBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled()}),wy(),iS.config({}),Jp("size-input-events",[Ur(Xs(),((e,t)=>{Ir(e,n,{isField1:o})})),Ur(Qs(),((t,o)=>{Ir(t,gS,{name:e.name})}))])]),selectOnFocus:!1}),l=e=>({dom:{tag:"label",classes:["tox-label"]},components:[ti(t.translate(e))]}),c=bO.parts.field1(a([sS.parts.label(l("Width")),i(!0)])),d=bO.parts.field2(a([sS.parts.label(l("Height")),i(!1)]));return bO.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,d,a([l("\xa0"),r])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{vO(pu.getValue(e)).each((e=>{o(e).each((e=>{pu.setValue(t,(e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit})(e))}))}))},coupledFieldBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{bO.getField1(e).bind(sS.getField).each(Rm.disable),bO.getField2(e).bind(sS.getField).each(Rm.disable),bO.getLock(e).each(Rm.disable)},onEnabled:e=>{bO.getField1(e).bind(sS.getField).each(Rm.enable),bO.getField2(e).bind(sS.getField).each(Rm.enable),bO.getLock(e).each(Rm.enable)}}),wy(),Jp("size-input-events2",[Ur(n,((e,t)=>{const n=t.event.isField1,s=n?bO.getField1(e):bO.getField2(e),r=n?bO.getField2(e):bO.getField1(e),a=s.map(pu.getValue).getOr(""),i=r.map(pu.getValue).getOr("");o=((e,t)=>{const o=vO(e).toOptional(),n=vO(t).toOptional();return Se(o,n,((e,t)=>yO(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>yO(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(xO))).getOr(xO)})(a,i)}))])])})})(e,t.shared.providers))),slider:M_(((e,t,o)=>((e,t,o)=>{const n=pC.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ti(t.translate(e.label))]}),s=pC.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),r=pC.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return pC.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:x(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,s,r],sliderBehaviours:kl([AC(),oh.config({})]),onChoose:(t,o,n)=>{Ir(t,gS,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:M_(((e,t,o)=>((e,t,o,n)=>{const s=t.shared.providers,r=t=>{const n=pu.getValue(t);o.addToHistory(n.value,e.filetype)},a={...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":T_,type:"url"},minChars:0,responseTime:0,fetch:n=>{const s=((e,t,o)=>{var n,s;const r=pu.getValue(t),a=null!==(s=null===(n=null==r?void 0:r.meta)||void 0===n?void 0:n.text)&&void 0!==s?s:r.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=__(a,(e=>H(e,(e=>x_(e,e))))(o.getHistory(e)));return"file"===e?(s=[n,__(a,S_(t)),__(a,q([C_(t),k_(t),O_(t)]))],j(s,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(v_,t)),[])):n;var s}))})(e.filetype,n,o),r=lO(s,gb.BUBBLE_TO_SANDBOX,t,{isHorizontalMenu:!1,search:A.none()});return fw(r)},getHotspot:e=>g.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(DS)&&DS.run(e).get(b)},typeaheadBehaviours:kl([...o.getValidationHandler().map((t=>DS.config({getRoot:e=>rt(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{c.getOpt(e).each((e=>{kt(e.element,"title",s.translate(t))}))}},validator:{validate:o=>{const n=pu.getValue(o);return d_((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=sn.error(e.message);o(t)}else{const t=sn.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),Rm.config({disabled:()=>!e.enabled||s.isDisabled()}),iS.config({}),Jp("urlinput-events",[Ur(Zs(),(t=>{const o=$a(t.element),n=o.trim();n!==o&&qa(t.element,n),"file"===e.filetype&&Ir(t,gS,{name:e.name})})),Ur(Qs(),(t=>{Ir(t,gS,{name:e.name}),r(t)})),Ur(cr(),(t=>{Ir(t,gS,{name:e.name}),r(t)}))])]),eventOrder:{[Zs()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:Db(0,0,"normal")},onExecute:(e,t,o)=>{Ir(t,bS,{})},onItemExecute:(t,o,n,s)=>{r(t),Ir(t,gS,{name:e.name})}},i=sS.parts.field({...a,factory:l_}),l=e.label.map((e=>mS(e,s))),c=jh(((e,t,o=e,n=e)=>ef(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:s.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},s.icons))("invalid",A.some(T_),"warning")),d=jh({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[c.asSpec()]}),u=o.getUrlPicker(e.filetype),m=la("browser.url.event"),g=jh({dom:{tag:"div",classes:["tox-control-wrap"]},components:[i,d.asSpec()],behaviours:kl([Rm.config({disabled:()=>!e.enabled||s.isDisabled()})])}),p=jh(h_({name:e.name,icon:A.some("browse"),text:e.label.getOr(""),enabled:e.enabled,primary:!1,buttonType:A.none(),borderless:!0},(e=>Fr(e,m)),s,[],["tox-browse-url"]));return sS.sketch({dom:uS([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:q([[g.asSpec()],u.map((()=>p.asSpec())).toArray()])}]),fieldBehaviours:kl([Rm.config({disabled:()=>!e.enabled||s.isDisabled(),onDisabled:e=>{sS.getField(e).each(Rm.disable),p.getOpt(e).each(Rm.disable)},onEnabled:e=>{sS.getField(e).each(Rm.enable),p.getOpt(e).each(Rm.enable)}}),wy(),Jp("url-input-events",[Ur(m,(t=>{wm.getCurrent(t).each((o=>{const n=pu.getValue(o),s={fieldname:e.name,...n};u.each((n=>{n(s).get((n=>{pu.setValue(o,n),Ir(t,gS,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:M_((e=>{const t=Ql(),o=jh({dom:{tag:e.tag}}),n=Ql();return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:kl([Jp("custom-editor-events",[Yr((s=>{o.getOpt(s).each((o=>{((e=>ve(e,"init"))(e)?e.init(o.element.dom):HC.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),NC(A.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),AC()]),components:[o.asSpec()]}})),htmlpanel:M_((e=>"presentation"===e.presets?eS.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html}}):eS.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:kl([iS.config({}),oh.config({})])}))),imagepreview:M_(((e,t,o)=>((e,t)=>{const o=Es(t.getOr({url:""})),n=jh({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),s=jh({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),r={};e.height.each((e=>r.height=e));const a=t.map((e=>({url:e.url,zoom:A.from(e.zoom),cachedWidth:A.from(e.cachedWidth),cachedHeight:A.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:r,attributes:{role:"presentation"}},components:[s.asSpec()],behaviours:kl([AC(),NC(a,(()=>o.get()),((e,t)=>{const r={url:t.url};t.zoom.each((e=>r.zoom=e)),t.cachedWidth.each((e=>r.cachedWidth=e)),t.cachedHeight.each((e=>r.cachedHeight=e)),o.set(r);const a=()=>{const{cachedWidth:t,cachedHeight:o,zoom:n}=r;if(!u(t)&&!u(o)){if(u(n)){const n=((e,t,o)=>{const n=Jt(e),s=Wt(e);return Math.min(n/t,s/o,1)})(e.element,t,o);r.zoom=n}const a=((e,t,o,n,s)=>{const r=o*s,a=n*s,i=Math.max(0,e/2-r/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:r.toString()+"px",height:a.toString()+"px"}})(Jt(e.element),Wt(e.element),t,o,r.zoom);s.getOpt(e).each((e=>{Bt(e.element,a)}))}};n.getOpt(e).each((o=>{const n=o.element;var s;t.url!==Ot(n,"src")&&(kt(n,"src",t.url),Pa(e.element,"tox-imagepreview__loaded")),a(),(s=n,new Promise(((e,t)=>{const o=()=>{r(),e(s)},n=[tc(s,"load",o),tc(s,"error",(()=>{r(),t("Unable to load data from image: "+s.dom.src)}))],r=()=>L(n,(e=>e.unbind()));s.dom.complete&&o()}))).then((t=>{e.getSystem().isConnected()&&(La(e.element,"tox-imagepreview__loaded"),r.cachedWidth=t.dom.naturalWidth,r.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:M_(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(s=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:H(s,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:H(n,(e=>({dom:{tag:"tr"},components:H(e,o)})))})],behaviours:kl([iS.config({}),oh.config({})])};var n,s})(e,t.shared.providers))),tree:M_(((e,t)=>((e,t)=>{const o=e.onLeafAction.getOr(b),n=e.onToggleExpand.getOr(b),s=e.defaultExpandedIds,r=Es(s),a=Es(e.defaultSelectedId),i=la("tree-id"),l=(n,s)=>e.items.map((e=>"leaf"===e.type?WO({leaf:e,selectedId:n,onLeafAction:o,visible:!0,treeId:i,backstage:t}):XO({directory:e,selectedId:n,onLeafAction:o,expandedIds:s,labelTabstopping:!0,treeId:i,backstage:t})));return{dom:{tag:"div",classes:["tox-tree"],attributes:{role:"tree"}},components:l(a.get(),r.get()),behaviours:kl([Pp.config({mode:"flow",selector:".tox-tree--leaf__label--visible, .tox-tree--directory__label--visible",cycles:!1}),Jp(KO,[Ur("expand-tree-node",((e,t)=>{const{expanded:o,node:s}=t.event;r.set(o?[...r.get(),s]:r.get().filter((e=>e!==s))),n(r.get(),{expanded:o,node:s})}))]),Al.config({channels:{[`update-active-item-${i}`]:{onReceive:(e,t)=>{a.set(A.some(t.value)),Yp.set(e,l(A.some(t.value),r.get()))}}}}),Yp.config({})])}})(e,t))),panel:M_(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:H(e.items,t.shared.interpreter)}))(e,t)))},B_={field:(e,t)=>t,record:x([])},F_=(e,t,o,n)=>{const s=fn(n,{shared:{interpreter:t=>I_(e,t,o,s)}});return I_(e,t,o,s)},I_=(e,t,o,n)=>be(D_,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(s=>s(e,t,o,n))),R_=(e,t,o)=>I_(B_,e,t,o),N_="layout-inset",V_=e=>e.x,z_=(e,t)=>e.x+e.width/2-t.width/2,H_=(e,t)=>e.x+e.width-t.width,L_=e=>e.y,P_=(e,t)=>e.y+e.height-t.height,U_=(e,t)=>e.y+e.height/2-t.height/2,W_=(e,t,o)=>zi(H_(e,t),P_(e,t),o.insetSouthwest(),Wi(),"southwest",Yi(e,{right:0,bottom:3}),N_),j_=(e,t,o)=>zi(V_(e),P_(e,t),o.insetSoutheast(),Ui(),"southeast",Yi(e,{left:1,bottom:3}),N_),G_=(e,t,o)=>zi(H_(e,t),L_(e),o.insetNorthwest(),Pi(),"northwest",Yi(e,{right:0,top:2}),N_),$_=(e,t,o)=>zi(V_(e),L_(e),o.insetNortheast(),Li(),"northeast",Yi(e,{left:1,top:2}),N_),q_=(e,t,o)=>zi(z_(e,t),L_(e),o.insetNorth(),ji(),"north",Yi(e,{top:2}),N_),X_=(e,t,o)=>zi(z_(e,t),P_(e,t),o.insetSouth(),Gi(),"south",Yi(e,{bottom:3}),N_),K_=(e,t,o)=>zi(H_(e,t),U_(e,t),o.insetEast(),qi(),"east",Yi(e,{right:0}),N_),Y_=(e,t,o)=>zi(V_(e),U_(e,t),o.insetWest(),$i(),"west",Yi(e,{left:1}),N_),J_=e=>{switch(e){case"north":return q_;case"northeast":return $_;case"northwest":return G_;case"south":return X_;case"southeast":return j_;case"southwest":return W_;case"east":return K_;case"west":return Y_}},Z_=(e,t,o,n,s)=>Xl(n).map(J_).getOr(q_)(e,t,o,n,s),Q_=e=>{switch(e){case"north":return X_;case"northeast":return j_;case"northwest":return W_;case"south":return q_;case"southeast":return $_;case"southwest":return G_;case"east":return Y_;case"west":return K_}},eT=(e,t,o,n,s)=>Xl(n).map(Q_).getOr(q_)(e,t,o,n,s),tT={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},oT=(e,t,o)=>{const n={maxHeightFunction:cc()};return()=>o()?{type:"node",root:ft(ht(e())),node:A.from(e()),bubble:gc(12,12,tT),layouts:{onRtl:()=>[$_],onLtr:()=>[G_]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:gc(-12,12,tT),layouts:{onRtl:()=>[sl,rl,cl],onLtr:()=>[rl,sl,cl]},overrides:n}},nT=(e,t,o)=>()=>o()?{type:"node",root:ft(ht(e())),node:A.from(e()),layouts:{onRtl:()=>[q_],onLtr:()=>[q_]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[cl],onLtr:()=>[cl]}},sT=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng(),o=e.model.table.getSelectedCells();if(o.length>1){const e=o[0],t=o[o.length-1],n={firstCell:Ve(e),lastCell:Ve(t)};return A.some(n)}return A.some(Lc.range(Ve(t.startContainer),t.startOffset,Ve(t.endContainer),t.endOffset))}}),rT=e=>t=>({type:"node",root:e(),node:t}),aT=(e,t,o)=>{const n=nb(e),s=()=>Ve(e.getBody()),r=()=>Ve(e.getContentAreaContainer()),a=()=>n||!o();return{inlineDialog:oT(r,t,a),banner:nT(r,t,a),cursor:sT(e,s),node:rT(s)}},iT=e=>(t,o)=>{Yx(e)(t,o)},lT=e=>()=>Vx(e),cT=e=>t=>zx(e,t),dT=e=>t=>Nx(e,t),uT=e=>()=>Lf(e),mT=e=>ye(e,"items"),gT=e=>ye(e,"format"),pT=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],hT=e=>j(e,((e,t)=>{if(ve(t,"items")){const o=hT(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(ve(t,"inline")||(e=>ve(e,"block"))(t)||(e=>ve(e,"selector"))(t)){const o=`custom-${r(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),fT=e=>yf(e).map((t=>{const o=((e,t)=>{const o=hT(t),n=t=>{L(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return xf(e)?pT.concat(o):o})).getOr(pT),bT=(e,t,o)=>({...e,type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)}),vT=(e,t,o,n)=>{const s=t=>H(t,(t=>mT(t)?(e=>{const t=s(e.items);return{...e,type:"submenu",getStyleItems:x(t)}})(t):gT(t)?(e=>bT(e,o,n))(t):(e=>{const t=ae(e);return 1===t.length&&R(t,"title")})(t)?{...t,type:"separator"}:(t=>{const s=r(t.name)?t.name:la(t.title),a=`custom-${s}`,i={...t,type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)};return e.formatter.register(s,i),i})(t)));return s(t)},yT=LC.trim,xT=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},wT=xT("true"),ST=xT("false"),kT=(e,t,o,n,s)=>({type:e,title:t,url:o,level:n,attach:s}),CT=e=>e.innerText||e.textContent,OT=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&TT(e),_T=e=>e&&/^(H[1-6])$/.test(e.nodeName),TT=e=>(e=>{let t=e;for(;t=t.parentNode;){const e=t.contentEditable;if(e&&"inherit"!==e)return wT(t)}return!1})(e)&&!ST(e),ET=e=>_T(e)&&TT(e),AT=e=>{var t;const o=(e=>e.id?e.id:la("h"))(e);return kT("header",null!==(t=CT(e))&&void 0!==t?t:"","#"+o,(e=>_T(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=o}))},MT=e=>{const t=e.id||e.name,o=CT(e);return kT("anchor",o||"#"+t,"#"+t,0,b)},DT=e=>yT(e.title).length>0,BT=e=>{const t=(e=>{const t=H(Xc(Ve(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return U((e=>H(U(e,ET),AT))(t).concat((e=>H(U(e,OT),MT))(t)),DT)},FT="tinymce-url-history",IT=e=>r(e)&&/^https?/.test(e),RT=e=>a(e)&&he(e,(e=>{return!(l(t=e)&&t.length<=5&&K(t,IT));var t})).isNone(),NT=()=>{const e=wx.getItem(FT);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+FT+" was not valid JSON",e),{};throw e}return RT(t)?t:(console.log("Local storage "+FT+" was not valid format",t),{})},VT=e=>{const t=NT();return be(t,e).getOr([])},zT=(e,t)=>{if(!IT(e))return;const o=NT(),n=be(o,t).getOr([]),s=U(n,(t=>t!==e));o[t]=[e].concat(s).slice(0,5),(e=>{if(!RT(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));wx.setItem(FT,JSON.stringify(e))})(o)},HT=e=>!!e,LT=e=>ce(LC.makeMap(e,/[, ]/),HT),PT=e=>A.from(Ff(e)),UT=e=>A.from(e).filter(r).getOrUndefined(),WT=e=>({getHistory:VT,addToHistory:zT,getLinkInformation:()=>(e=>Vf(e)?A.some({targets:BT(e.getBody()),anchorTop:UT(zf(e)),anchorBottom:UT(Hf(e))}):A.none())(e),getValidationHandler:()=>(e=>A.from(If(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=A.from(Nf(e)).filter(HT).map(LT);return PT(e).fold(T,(e=>t.fold(E,(e=>ae(e).length>0&&e))))})(e);return d(o)?o?PT(e):A.none():o[t]?PT(e):A.none()})(e,t).map((o=>n=>hw((s=>{const i={filetype:t,fieldname:n.fieldname,...A.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!r(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");s({value:e,meta:t})}),n.value,i)})))))(e,t)}),jT=dm,GT=qu,$T=x([ys("shell",!1),os("makeItem"),ys("setupItem",b),vu("listBehaviours",[Yp])]),qT=ju({name:"items",overrides:()=>({behaviours:kl([Yp.config({})])})}),XT=x([qT]),KT=bm({name:x("CustomList")(),configFields:$T(),partFields:XT(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Yp.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:bu(e.listBehaviours,s.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?A.some(n):om(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const s=Yp.contents(n),r=o.length,a=r-s.length,i=a>0?V(a,(()=>e.makeItem())):[],l=s.slice(r);L(l,(e=>Yp.remove(n,e))),L(i,(e=>Yp.append(n,e)));const c=Yp.contents(n);L(c,((n,s)=>{e.setupItem(t,n,o[s],s)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),YT=x([os("dom"),ys("shell",!0),hu("toolbarBehaviours",[Yp])]),JT=x([ju({name:"groups",overrides:()=>({behaviours:kl([Yp.config({})])})})]),ZT=bm({name:"Toolbar",configFields:YT(),partFields:JT(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Yp.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:bu(e.toolbarBehaviours,s.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?A.some(n):om(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{Yp.set(e,o)}))}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),QT=b,eE=T,tE=x([]);var oE,nE=Object.freeze({__proto__:null,setup:QT,isDocked:eE,getBehaviours:tE});const sE=e=>(xe(Nt(e,"position"),"fixed")?A.none():at(e)).orThunk((()=>{const t=Re("span");return st(e).bind((e=>{zo(e,t);const o=at(t);return Po(t),o}))})),rE=e=>sE(e).map(Xt).getOrThunk((()=>$t(0,0))),aE=(e,t)=>{const o=e.element;La(o,t.transitionClass),Pa(o,t.fadeOutClass),La(o,t.fadeInClass),t.onShow(e)},iE=(e,t)=>{const o=e.element;La(o,t.transitionClass),Pa(o,t.fadeInClass),La(o,t.fadeOutClass),t.onHide(e)},lE=(e,t)=>e.y>=t.y,cE=(e,t)=>e.bottom<=t.bottom,dE=(e,t,o)=>({location:"top",leftX:t,topY:o.bounds.y-e.y}),uE=(e,t,o)=>({location:"bottom",leftX:t,bottomY:e.bottom-o.bounds.bottom}),mE=e=>e.box.x-e.win.x,gE=(e,t,o)=>o.getInitialPos().map((o=>{const n=((e,t)=>{const o=t.optScrollEnv.fold(x(e.bounds.y),(t=>t.scrollElmTop+(e.bounds.y-t.currentScrollTop)));return $t(e.bounds.x,o)})(o,t);return{box:Yo(n.left,n.top,Jt(e),Wt(e)),location:o.location}})),pE=(e,t,o,n,s)=>{const r=((e,t)=>{const o=t.optScrollEnv.fold(x(e.y),(t=>e.y+t.currentScrollTop-t.scrollElmTop));return $t(e.x,o)})(t,o),a=Yo(r.left,r.top,t.width,t.height);n.setInitialPos({style:Vt(e),position:It(e,"position")||"static",bounds:a,location:s.location})},hE=(e,t,o)=>o.getInitialPos().bind((n=>{var s;switch(o.clearInitialPos(),n.position){case"static":return A.some({morph:"static"});case"absolute":const o=sE(e).getOr(xt()),r=Jo(o),a=null!==(s=o.dom.scrollTop)&&void 0!==s?s:0;return A.some({morph:"absolute",positionCss:Vl("absolute",be(n.style,"left").map((e=>t.x-r.x)),be(n.style,"top").map((e=>t.y-r.y+a)),be(n.style,"right").map((e=>r.right-t.right)),be(n.style,"bottom").map((e=>r.bottom-t.bottom)))});default:return A.none()}})),fE=e=>{switch(e.location){case"top":return A.some({morph:"fixed",positionCss:Vl("fixed",A.some(e.leftX),A.some(e.topY),A.none(),A.none())});case"bottom":return A.some({morph:"fixed",positionCss:Vl("fixed",A.some(e.leftX),A.none(),A.none(),A.some(e.bottomY))});default:return A.none()}},bE=(e,t,o)=>{const n=e.element;return xe(Nt(n,"position"),"fixed")?((e,t,o)=>((e,t,o)=>gE(e,t,o).filter((({box:e})=>((e,t,o)=>K(e,(e=>{switch(e){case"bottom":return cE(t,o.bounds);case"top":return lE(t,o.bounds)}})))(o.getModes(),e,t))).bind((({box:t})=>hE(e,t,o))))(e,t,o).orThunk((()=>t.optScrollEnv.bind((n=>gE(e,t,o))).bind((({box:e,location:o})=>{const n=en(),s=mE({win:n,box:e}),r="top"===o?dE(n,s,t):uE(n,s,t);return fE(r)})))))(n,t,o):((e,t,o)=>{const n=Jo(e),s=en(),r=((e,t,o)=>{const n=t.win,s=t.box,r=mE(t);return re(e,(e=>{switch(e){case"bottom":return cE(s,o.bounds)?A.none():A.some(uE(n,r,o));case"top":return lE(s,o.bounds)?A.none():A.some(dE(n,r,o));default:return A.none()}})).getOr({location:"no-dock"})})(o.getModes(),{win:s,box:n},t);return"top"===r.location||"bottom"===r.location?(pE(e,n,t,o,r),fE(r)):A.none()})(n,t,o)},vE=(e,t,o)=>{o.setDocked(!1),L(["left","right","top","bottom","position"],(t=>Ht(e.element,t))),t.onUndocked(e)},yE=(e,t,o,n)=>{const s="fixed"===n.position;o.setDocked(s),zl(e.element,n),(s?t.onDocked:t.onUndocked)(e)},xE=(e,t,o,n,s=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((r=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(r,n.bounds);a!==o.isVisible()&&(o.setVisible(a),s&&!a?(Wa(e.element,[t.fadeOutClass]),t.onHide(e)):(a?aE:iE)(e,t))}))}))},wE=(e,t,o,n,s)=>{xE(e,t,o,n,!0),yE(e,t,o,s.positionCss)},SE=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);xE(e,t,o,n),bE(e,n,o).each((s=>{((e,t,o,n,s)=>{switch(s.morph){case"static":return vE(e,t,o);case"absolute":return yE(e,t,o,s.positionCss);case"fixed":wE(e,t,o,n,s)}})(e,t,o,n,s)}))})(e,t,o)},kE=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1);const s=t.lazyViewport(e);((e,t,o)=>{const n=e.element;return gE(n,t,o).bind((({box:e})=>hE(n,e,o)))})(e,s,o).each((n=>{switch(n.morph){case"static":vE(e,t,o);break;case"absolute":yE(e,t,o,n.positionCss)}})),o.setVisible(!0),t.contextual.each((t=>{ja(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),SE(e,t,o)})(e,t,o)},CE=e=>(t,o,n)=>{const s=o.lazyViewport(t);((e,t,o,n)=>{const s=Jo(e),r=en(),a=n(r,mE({win:r,box:s}),t);return"bottom"===a.location||"top"===a.location?(((e,t,o,n,s)=>{n.getInitialPos().fold((()=>pE(e,t,o,n,s)),(()=>b))})(e,s,t,o,a),fE(a)):A.none()})(t.element,s,n,e).each((e=>{wE(t,o,n,s,e)}))},OE=CE(dE),_E=CE(uE);var TE=Object.freeze({__proto__:null,refresh:SE,reset:kE,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n),forceDockToTop:OE,forceDockToBottom:_E}),EE=Object.freeze({__proto__:null,events:(e,t)=>Hr([Kr(or(),((o,n)=>{e.contextual.each((e=>{Ua(o.element,e.transitionClass)&&(ja(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),Ur(xr(),((o,n)=>{SE(o,e,t)})),Ur(Er(),((o,n)=>{SE(o,e,t)})),Ur(wr(),((o,n)=>{kE(o,e,t)}))])}),AE=[vs("contextual",[rs("fadeInClass"),rs("fadeOutClass"),rs("transitionClass"),is("lazyContext"),Di("onShow"),Di("onShown"),Di("onHide"),Di("onHidden")]),Os("lazyViewport",(()=>({bounds:en(),optScrollEnv:A.none()}))),_s("modes",["top","bottom"],Hn),Di("onDocked"),Di("onUndocked")];const ME=Ol({fields:AE,name:"docking",active:EE,apis:TE,state:Object.freeze({__proto__:null,init:e=>{const t=Es(!1),o=Es(!0),n=Ql(),s=Es(e.modes);return _a({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:s.get,setModes:s.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${s.get().join(",")}`})}})}),DE=x(la("toolbar-height-change")),BE={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},FE="tox-tinymce--toolbar-sticky-on",IE="tox-tinymce--toolbar-sticky-off",RE=(e,t)=>R(ME.getModes(e),t),NE=e=>{const t=e.element;rt(t).each((o=>{const n="padding-"+ME.getModes(e)[0];if(ME.isDocked(e)){const e=Jt(o);Dt(t,"width",e+"px"),Dt(o,n,(e=>jt(e)+(parseInt(It(e,"margin-top"),10)||0)+(parseInt(It(e,"margin-bottom"),10)||0))(t)+"px")}else Ht(t,"width"),Ht(o,n)}))},VE=(e,t)=>{t?(Pa(e,BE.fadeOutClass),Wa(e,[BE.transitionClass,BE.fadeInClass])):(Pa(e,BE.fadeInClass),Wa(e,[BE.fadeOutClass,BE.transitionClass]))},zE=(e,t)=>{const o=Ve(e.getContainer());t?(La(o,FE),Pa(o,IE)):(La(o,IE),Pa(o,FE))},HE=(e,t)=>{const o=Ql(),n=t.getSink,s=e=>{n().each((t=>e(t.element)))},r=t=>{e.inline||NE(t),zE(e,ME.isDocked(t)),t.getSystem().broadcastOn([Yd()],{}),n().each((e=>e.getSystem().broadcastOn([Yd()],{})))},a=e.inline?[]:[Al.config({channels:{[DE()]:{onReceive:NE}}})];return[oh.config({}),ME.config({contextual:{lazyContext:t=>{const o=jt(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer();return A.from(n).map((n=>{const s=Jo(Ve(n));return Uw(e,t.element).fold((()=>{const e=s.height-o,n=s.y+(RE(t,"top")?0:o);return Yo(s.x,n,s.width,e)}),(e=>{const n=Qo(s,Ww(e)),r=RE(t,"top")?n.y:n.y+o;return Yo(n.x,r,n.width,n.height-o)}))}))},onShow:()=>{s((e=>VE(e,!0)))},onShown:e=>{s((e=>ja(e,[BE.transitionClass,BE.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=et(t);Il(o).filter((e=>!Ze(t,e))).filter((t=>Ze(t,Ve(o.dom.body))||Qe(e,t))).each((()=>Dl(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>Rl(e).orThunk((()=>t().toOptional().bind((e=>Rl(e.element))))))(e.element,n).fold(o.clear,o.set),s((e=>VE(e,!1)))},onHidden:()=>{s((e=>ja(e,[BE.transitionClass])))},...BE},lazyViewport:t=>Uw(e,t.element).fold((()=>{const o=en(),n=Mf(e),s=o.y+(RE(t,"top")?n:0),r=o.height-(RE(t,"bottom")?n:0);return{bounds:Yo(o.x,s,o.width,r),optScrollEnv:A.none()}}),(e=>({bounds:Ww(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Xt(e.element).top})}))),modes:[t.header.getDockingMode()],onDocked:r,onUndocked:r}),...a]};var LE=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each(ME.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(NE)})),e.on("SkinLoaded",(()=>{o().each((e=>{ME.isDocked(e)?ME.reset(e):ME.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each(ME.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{ME.refresh(t);const o=t.element;Ig(o)&&((e,t)=>{const o=et(t),n=nt(t).dom.innerHeight,s=Uo(o),r=Ve(e.elm),a=Zo(r),i=Wt(r),l=a.y,c=l+i,d=Xt(t),u=Wt(t),m=d.top,g=m+u,p=Math.abs(m-s.top)<2,h=Math.abs(g-(s.top+n))<2;if(p&&l<g)Wo(s.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;Wo(s.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{zE(e,!1)}))},isDocked:e=>e().map(ME.isDocked).getOr(!1),getBehaviours:HE});const PE=Dn([ev,ns("items",Fn([Rn([tv,ds("items",Hn)]),Hn]))].concat(Mv)),UE=[ps("text"),ps("tooltip"),ps("icon"),xs("search",!1,Fn([Ln,Dn([ps("placeholder")])],(e=>d(e)?e?A.some({placeholder:A.none()}):A.none():A.some(e)))),is("fetch"),Os("onSetup",(()=>b))],WE=Dn([ev,...UE]),jE=e=>qn("menubutton",WE,e),GE=Dn([ev,hv,pv,gv,vv,iv,uv,ks("presets","normal",["normal","color","listpreview"]),kv(1),cv,dv]);var $E=fm({factory:(e,t)=>{const o={focus:Pp.focusIn,setMenus:(e,o)=>{const n=H(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())}},n=jE(o).mapError((e=>Yn(e))).getOrDie();return HO(n,"tox-mbtn",t.backstage,A.some("menuitem"))}));Yp.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:kl([Yp.config({}),Jp("menubar-events",[Yr((t=>{e.onSetup(t)})),Ur(qs(),((e,t)=>{pi(e.element,".tox-mbtn--active").each((o=>{hi(t.event.target,".tox-mbtn").each((t=>{Ze(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{Mw.expand(e),Mw.close(o),oh.focus(e)}))}))}))}))})),Ur(_r(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{Mw.isOpen(o)&&(Mw.expand(e),Mw.close(o))}))}))}))]),Pp.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),A.some(!0))}),iS.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[os("dom"),os("uid"),os("onEscape"),os("backstage"),ys("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const qE="container",XE=[hu("slotBehaviours",[])],KE=e=>"<alloy.field."+e+">",YE=(e,t)=>{const o=t=>am(e),n=(t,o)=>(n,s)=>om(n,e,s).map((e=>t(e,s))).getOr(o),s=(e,t)=>"true"!==Ot(e.element,"aria-hidden"),r=n(s,!1),a=n(((e,t)=>{if(s(e)){const o=e.element;Dt(o,"display","none"),kt(o,"aria-hidden","true"),Ir(e,Tr(),{name:t,visible:!1})}})),i=(e=>(t,o)=>{L(o,(o=>e(t,o)))})(a),l=n(((e,t)=>{if(!s(e)){const o=e.element;Ht(o,"display"),Et(o,"aria-hidden"),Ir(e,Tr(),{name:t,visible:!0})}})),c={getSlotNames:o,getSlot:(t,o)=>om(t,e,o),isShowing:r,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:l};return{uid:e.uid,dom:e.dom,components:t,behaviours:fu(e.slotBehaviours),apis:c}},JE=ce({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>Ca(e))),ZE={...JE,sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),Ju(qE,KE(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=H(n,(e=>Uu({name:e,pname:KE(e)})));return mm(qE,XE,s,YE,o)}},QE=Dn([pv,hv,Os("onShow",b),Os("onHide",b),uv]),eA=e=>({element:()=>e.element.dom}),tA=(e,t)=>{const o=H(ae(t),(e=>{const o=t[e],n=Xn((e=>qn("sidebar",QE,e))(o));return{name:e,getApi:eA,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return H(o,(t=>{const n=Es(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:iy([_y(t,n),Ty(t,n),Ur(Tr(),((e,t)=>{const n=t.event,s=G(o,(e=>e.name===n.name));s.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},oA=e=>ZE.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:tA(t,e),slotBehaviours:iy([Yr((e=>ZE.hideAllSlots(e)))])}))),nA=(e,t)=>{kt(e,"role",t)},sA=e=>wm.getCurrent(e).bind((e=>VO.isGrowing(e)||VO.hasGrown(e)?wm.getCurrent(e).bind((e=>G(ZE.getSlotNames(e),(t=>ZE.isShowing(e,t))))):A.none())),rA=la("FixSizeEvent"),aA=la("AutoSizeEvent");var iA=Object.freeze({__proto__:null,block:(e,t,o,n)=>{kt(e.element,"aria-busy",!0);const s=t.getRoot(e).getOr(e),r=kl([Pp.config({mode:"special",onTab:()=>A.some(!0),onShiftTab:()=>A.some(!0)}),oh.config({})]),a=n(s,r),i=s.getSystem().build(a);Yp.append(s,ai(i)),i.hasConfigured(Pp)&&t.focus&&Pp.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>Yp.remove(s,i)))},unblock:(e,t,o)=>{Et(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()}}),lA=[Os("getRoot",A.none),Cs("focus",!0),Di("onBlock"),Di("onUnblock")];const cA=Ol({fields:lA,name:"blocking",apis:iA,state:Object.freeze({__proto__:null,init:()=>{const e=Jl((e=>e.destroy()));return _a({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})}),dA=e=>{const t=Ie(e),o=it(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return j(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),s=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),r=0===o.length?{}:{innerHtml:ea(t)};return{tag:Ue(t),classes:s,attributes:n,...r}},uA=e=>wm.getCurrent(e).each((e=>Dl(e.element))),mA=(e,t,o)=>{const n=Es(!1),s=Ql(),r=o=>{var s;n.get()&&(!(e=>"focusin"===e.type)(s=o)||!(s.composed?oe(s.composedPath()):A.from(s.target)).map(Ve).filter(Ge).exists((e=>Ua(e,"mce-pastebin"))))&&(o.preventDefault(),uA(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",r),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&r(e)}))}));const a=s=>{s!==n.get()&&(n.set(s),((e,t,o,n)=>{const s=t.element;if(((e,t)=>{const o="tabindex",n=`data-mce-${o}`;A.from(e.iframeElement).map(Ve).each((e=>{t?(_t(e,o).each((t=>kt(e,n,t))),kt(e,o,-1)):(Et(e,o),_t(e,n).each((t=>{kt(e,o,t),Et(e,n)})))}))})(e,o),o)cA.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:dA('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),Ht(s,"display"),Et(s,"aria-hidden"),e.hasFocus()&&uA(t);else{const o=wm.getCurrent(t).exists((e=>Fl(e.element)));cA.unblock(t),Dt(s,"display","none"),kt(s,"aria-hidden","true"),o&&e.focus()}})(e,t(),s,o.providers),((e,t)=>{e.dispatch("AfterProgressState",{state:t})})(e,s))};e.on("ProgressState",(t=>{if(s.on(clearTimeout),h(t.time)){const o=Uh.setEditorTimeout(e,(()=>a(t.state)),t.time);s.set(o)}else a(t.state),s.clear()}))},gA=(e,t,o)=>({within:e,extra:t,withinWidth:o}),pA=(e,t,o)=>{const n=j(e,((e,t)=>((e,t)=>{const n=o(e);return A.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(x(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,s=U(n,(e=>e.finish<=t)),r=W(s,((e,t)=>e+t.width),0);return{within:s,extra:n.slice(s.length),withinWidth:r}},hA=e=>H(e,(e=>e.element)),fA=(e,t)=>{const o=H(t,(e=>ai(e)));ZT.setGroups(e,o)},bA=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const s=nm(e,t,"primary"),r=dw.getCoupled(e,"overflowGroup");Dt(s.element,"visibility","hidden");const a=n.concat([r]),i=re(a,(e=>Rl(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),fA(s,a);const l=((e,t,o,n)=>{const s=((e,t,o)=>{const n=pA(t,e,o);return 0===n.extra.length?A.some(n):A.none()})(e,t,o).getOrThunk((()=>pA(t,e-o(n),o))),r=s.within,a=s.extra,i=s.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=hA(e.concat(t));return gA(n,[],o)})(r,a,i):a.length>=1?((e,t,o,n)=>{const s=hA(e).concat([o]);return gA(s,hA(t),n)})(r,a,n,i):((e,t,o)=>gA(hA(e),[],o))(r,0,i)})(Jt(s.element),t.builtGroups.get(),(e=>Jt(e.element)),r);0===l.extra.length?(Yp.remove(s,r),o([])):(fA(s,l.within),o(l.extra)),Ht(s.element,"visibility"),Lt(s.element),i.each(oh.focus)},vA=x([hu("splitToolbarBehaviours",[dw]),es("builtGroups",(()=>Es([])))]),yA=x([Ai(["overflowToggledClass"]),fs("getOverflowBounds"),os("lazySink"),es("overflowGroups",(()=>Es([]))),Di("onOpened"),Di("onClosed")].concat(vA())),xA=x([Uu({factory:ZT,schema:YT(),name:"primary"}),Wu({schema:YT(),name:"overflow"}),Wu({name:"overflow-button"}),Wu({name:"overflow-group"})]),wA=x(((e,t)=>{((e,t)=>{const o=Yt.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);Dt(e,"max-width",o+"px")})(e,Math.floor(t))})),SA=x([Ai(["toggledClass"]),os("lazySink"),is("fetch"),fs("getBounds"),vs("fireDismissalEventInstead",[ys("event",Cr())]),wc(),Di("onToggled")]),kA=x([Wu({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:kl([dh.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1,onToggled:e.onToggled})])})}),Wu({factory:ZT,schema:YT(),name:"toolbar",overrides:e=>({toolbarBehaviours:kl([Pp.config({mode:"cyclic",onEscape:t=>(om(t,e,"button").each(oh.focus),A.none())})])})})]),CA=Ql(),OA=(e,t)=>{const o=dw.getCoupled(e,"toolbarSandbox");Xd.isOpen(o)?Xd.close(o):Xd.open(o,t.toolbar())},_A=(e,t,o,n)=>{const s=o.getBounds.map((e=>e())),r=o.lazySink(e).getOrDie();Sd.positionWithinBounds(r,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:wA()}}},s)},TA=(e,t,o,n,s)=>{ZT.setGroups(t,s),_A(e,t,o,n),dh.on(e)},EA=bm({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({...Wh.sketch({...n.button(),action:e=>{OA(e,n)},buttonBehaviours:yu({dump:n.button().buttonBehaviours},[dw.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=bi();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:kl([Pp.config({mode:"special",onEscape:e=>(Xd.close(e),A.some(!0))}),Xd.config({onOpen:(s,r)=>{const a=CA.get().getOr(!1);o.fetch().get((s=>{TA(e,r,o,t.layouts,s),n.link(e.element),a||Pp.focusIn(r)}))},onClose:()=>{dh.off(e),CA.get().getOr(!1)||oh.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>vi(o,n)||vi(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),Al.config({channels:{...Qd({isExtraPart:T,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...tu({doReposition:()=>{Xd.getState(dw.getCoupled(e,"toolbarSandbox")).each((n=>{_A(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{Xd.getState(dw.getCoupled(t,"toolbarSandbox")).each((s=>{TA(t,s,e,o.layouts,n)}))},reposition:t=>{Xd.getState(dw.getCoupled(t,"toolbarSandbox")).each((n=>{_A(t,n,e,o.layouts)}))},toggle:e=>{OA(e,n)},toggleWithoutFocusing:e=>{((e,t)=>{CA.set(!0),OA(e,t),CA.clear()})(e,n)},getToolbar:e=>Xd.getState(dw.getCoupled(e,"toolbarSandbox")),isOpen:e=>Xd.isOpen(dw.getCoupled(e,"toolbarSandbox"))}}),configFields:SA(),partFields:kA(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggleWithoutFocusing(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),AA=x([os("items"),Ai(["itemSelector"]),hu("tgroupBehaviours",[Pp])]),MA=x([Gu({name:"items",unit:"item"})]),DA=bm({name:"ToolbarGroup",configFields:AA(),partFields:MA(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.tgroupBehaviours,[Pp.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),BA=e=>H(e,(e=>ai(e))),FA=(e,t,o)=>{bA(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{EA.setGroups(e,BA(n))}))}))},IA=bm({name:"SplitFloatingToolbar",configFields:yA(),partFields:xA(),factory:(e,t,o,n)=>{const s=jh(EA.sketch({fetch:()=>hw((t=>{t(BA(e.overflowGroups.get()))})),layouts:{onLtr:()=>[rl,sl],onRtl:()=>[sl,rl],onBottomLtr:()=>[il,al],onBottomRtl:()=>[al,il]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()},onToggled:(t,o)=>e[o?"onOpened":"onClosed"](t)}));return{uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.splitToolbarBehaviours,[dw.config({others:{overflowGroup:()=>DA.sketch({...n["overflow-group"](),items:[s.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(H(o,t.getSystem().build)),FA(t,s,e)},refresh:t=>FA(t,s,e),toggle:e=>{s.getOpt(e).each((e=>{EA.toggle(e)}))},toggleWithoutFocusing:e=>{s.getOpt(e).each(EA.toggleWithoutFocusing)},isOpen:e=>s.getOpt(e).map(EA.isOpen).getOr(!1),reposition:e=>{s.getOpt(e).each((e=>{EA.reposition(e)}))},getOverflow:e=>s.getOpt(e).bind(EA.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),RA=x([Ai(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),Di("onOpened"),Di("onClosed")].concat(vA())),NA=x([Uu({factory:ZT,schema:YT(),name:"primary"}),Uu({factory:ZT,schema:YT(),name:"overflow",overrides:e=>({toolbarBehaviours:kl([VO.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{om(t,e,"overflow-button").each((e=>{dh.off(e),oh.focus(e)})),e.onClosed(t)},onGrown:t=>{Pp.focusIn(t),e.onOpened(t)},onStartGrow:t=>{om(t,e,"overflow-button").each(dh.on)}}),Pp.config({mode:"acyclic",onEscape:t=>(om(t,e,"overflow-button").each(oh.focus),A.some(!0))})])})}),Wu({name:"overflow-button",overrides:e=>({buttonBehaviours:kl([dh.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])})}),Wu({name:"overflow-group"})]),VA=(e,t)=>{om(e,t,"overflow-button").bind((()=>om(e,t,"overflow"))).each((o=>{zA(e,t),VO.toggleGrow(o)}))},zA=(e,t)=>{om(e,t,"overflow").each((o=>{bA(e,t,(e=>{const t=H(e,(e=>ai(e)));ZT.setGroups(o,t)})),om(e,t,"overflow-button").each((e=>{VO.hasGrown(o)&&dh.on(e)})),VO.refresh(o)}))},HA=bm({name:"SplitSlidingToolbar",configFields:RA(),partFields:NA(),factory:(e,t,o,n)=>{const s="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.splitToolbarBehaviours,[dw.config({others:{overflowGroup:e=>DA.sketch({...n["overflow-group"](),items:[Wh.sketch({...n["overflow-button"](),action:t=>{Fr(e,s)}})]})}}),Jp("toolbar-toggle-events",[Ur(s,(t=>{VA(t,e)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=H(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),zA(t,e)},refresh:t=>zA(t,e),toggle:t=>VA(t,e),isOpen:t=>((e,t)=>om(e,t,"overflow").map(VO.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),LA=e=>{const t=e.title.fold((()=>({})),(e=>({attributes:{title:e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"],...t},components:[DA.parts.items({})],items:e.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled]), .tox-number-input:not([disabled])"},tgroupBehaviours:kl([iS.config({}),oh.config({})])}},PA=e=>DA.sketch(LA(e)),UA=(e,t)=>{const o=Yr((t=>{const o=H(e.initGroups,PA);ZT.setGroups(t,o)}));return kl([Cy(e.providers.isDisabled),wy(),Pp.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),Jp("toolbar-events",[o])])},WA=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":LA({title:A.none(),items:[]}),"overflow-button":m_({name:"more",icon:A.some("more-drawer"),enabled:!0,tooltip:A.some("More..."),primary:!1,buttonType:A.none(),borderless:!1},A.none(),e.providers)},splitToolbarBehaviours:UA(e,t)}},jA=e=>{const t=WA(e),o=IA.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return IA.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=Zo(t),n=ot(t),s=Zo(n),r=Math.max(n.dom.scrollHeight,s.height);return Yo(o.x+4,s.y,o.width-8,r)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>e.onToggled(t,!0),onClosed:t=>e.onToggled(t,!1)})},GA=e=>{const t=HA.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=HA.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=WA(e);return HA.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>{t.getSystem().broadcastOn([DE()],{type:"opened"}),e.onToggled(t,!0)},onClosed:t=>{t.getSystem().broadcastOn([DE()],{type:"closed"}),e.onToggled(t,!1)}})},$A=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return ZT.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===nf.scrolling?["tox-toolbar--scrolling"]:[])},components:[ZT.parts.groups({})],toolbarBehaviours:UA(e,t)})},qA=[gv,pv,ps("tooltip"),ks("buttonType","secondary",["primary","secondary"]),Cs("borderless",!1),is("onAction")],XA={button:[...qA,nv,as("type",["button"])],togglebutton:[...qA,Cs("active",!1),as("type",["togglebutton"])]},KA=[as("type",["group"]),_s("buttons",[],Jn("type",XA))],YA=Jn("type",{...XA,group:KA}),JA=Dn([_s("buttons",[],YA),is("onShow"),is("onHide")]),ZA=(e,t)=>((e,t)=>{var o,n;const s="togglebutton"===e.type,r=e.icon.map((e=>eO(e,t.icons))).map(jh),a={...e,name:s?e.text.getOr(e.icon.getOr("")):null!==(o=e.text)&&void 0!==o?o:e.icon.getOr(""),primary:"primary"===e.buttonType,buttonType:A.from(e.buttonType),tooltip:e.tooltip,icon:e.icon,enabled:!0,borderless:e.borderless},i=g_(null!==(n=e.buttonType)&&void 0!==n?n:"secondary"),l=s?e.text.map(t.translate):A.some(t.translate(e.text)),c=l.map(ti),d=a.tooltip.or(l).map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),u=r.map((e=>e.asSpec())),m=My([u,c]),g=e.icon.isSome()&&c.isSome(),p={tag:"button",classes:i.concat(...e.icon.isSome()&&!g?["tox-button--icon"]:[]).concat(...g?["tox-button--icon-and-text"]:[]).concat(...e.borderless?["tox-button--naked"]:[]).concat(..."togglebutton"===e.type&&e.active?["tox-button--enabled"]:[]),attributes:d},h=u_(a,A.some((o=>{const n=e=>{r.map((n=>n.getOpt(o).each((o=>{Yp.set(o,[eO(e,t.icons)])}))))};return s?e.onAction({setIcon:n,setActive:e=>{const t=o.element;e?(La(t,"tox-button--enabled"),kt(t,"aria-pressed",!0)):(Pa(t,"tox-button--enabled"),Et(t,"aria-pressed"))},isActive:()=>Ua(o.element,"tox-button--enabled")}):"button"===e.type?e.onAction({setIcon:n}):void 0})),[],p,m,t);return Wh.sketch(h)})(e,t),QA=Do().deviceType,eM=QA.isPhone(),tM=QA.isTablet();var oM=bm({name:"silver.View",configFields:[os("viewConfig")],partFields:[ju({factory:{sketch:e=>{let t=!1;const o=H(e.buttons,(o=>"group"===o.type?(t=!0,((e,t)=>({dom:{tag:"div",classes:["tox-view__toolbar__group"]},components:H(e.buttons,(e=>ZA(e,t)))}))(o,e.providers)):ZA(o,e.providers)));return{uid:e.uid,dom:{tag:"div",classes:[t?"tox-view__toolbar":"tox-view__header",...eM||tM?["tox-view--mobile","tox-view--scrolling"]:[]]},behaviours:kl([oh.config({}),Pp.config({mode:"flow",selector:"button, .tox-button",focusInside:pg.OnEnterOrSpaceMode})]),components:t?o:[eS.sketch({dom:{tag:"div",classes:["tox-view__header-start"]},components:[]}),eS.sketch({dom:{tag:"div",classes:["tox-view__header-end"]},components:o})]}}},schema:[os("buttons"),os("providers")],name:"header"}),ju({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-view__pane"]}})},schema:[],name:"pane"})],factory:(e,t,o,n)=>{const s={getPane:t=>jT.getPart(t,e,"pane"),getOnShow:t=>e.viewConfig.onShow,getOnHide:t=>e.viewConfig.onHide};return{uid:e.uid,dom:e.dom,components:t,apis:s}},apis:{getPane:(e,t)=>e.getPane(t),getOnShow:(e,t)=>e.getOnShow(t),getOnHide:(e,t)=>e.getOnHide(t)}});const nM=(e,t,o)=>pe(t,((t,n)=>{const s=Xn(qn("view",JA,t));return e.slot(n,oM.sketch({dom:{tag:"div",classes:["tox-view"]},viewConfig:s,components:[...s.buttons.length>0?[oM.parts.header({buttons:s.buttons,providers:o})]:[],oM.parts.pane({})]}))})),sM=(e,t)=>ZE.sketch((o=>({dom:{tag:"div",classes:["tox-view-wrap__slot-container"]},components:nM(o,e,t),slotBehaviours:iy([Yr((e=>ZE.hideAllSlots(e)))])}))),rM=e=>G(ZE.getSlotNames(e),(t=>ZE.isShowing(e,t))),aM=(e,t,o)=>{ZE.getSlot(e,t).each((e=>{oM.getPane(e).each((t=>{var n;o(e)((n=t.element.dom,{getContainer:x(n)}))}))}))};var iM=fm({factory:(e,t)=>{const o={setViews:(e,o)=>{Yp.set(e,[sM(o,t.backstage.shared.providers)])},whichView:e=>wm.getCurrent(e).bind(rM),toggleView:(e,t,o,n)=>wm.getCurrent(e).exists((s=>{const r=rM(s),a=r.exists((e=>n===e)),i=ZE.getSlot(s,n).isSome();return i&&(ZE.hideAllSlots(s),a?((e=>{const t=e.element;Dt(t,"display","none"),kt(t,"aria-hidden","true")})(e),t()):(o(),(e=>{const t=e.element;Ht(t,"display"),Et(t,"aria-hidden")})(e),ZE.showSlot(s,n),((e,t)=>{aM(e,t,oM.getOnShow)})(s,n)),r.each((e=>((e,t)=>aM(e,t,oM.getOnHide))(s,e)))),i}))};return{uid:e.uid,dom:{tag:"div",classes:["tox-view-wrap"],attributes:{"aria-hidden":"true"},styles:{display:"none"}},components:[],behaviours:kl([Yp.config({}),wm.config({find:e=>{const t=Yp.contents(e);return oe(t)}})]),apis:o}},name:"silver.ViewWrapper",configFields:[os("backstage")],apis:{setViews:(e,t,o)=>e.setViews(t,o),toggleView:(e,t,o,n,s)=>e.toggleView(t,o,n,s),whichView:(e,t)=>e.whichView(t)}});const lM=GT.optional({factory:$E,name:"menubar",schema:[os("backstage")]}),cM=GT.optional({factory:{sketch:e=>KT.sketch({uid:e.uid,dom:e.dom,listBehaviours:kl([Pp.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>$A({type:e.type,uid:la("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),A.some(!0))}),setupItem:(e,t,o,n)=>{ZT.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[os("dom"),os("onEscape")]}),dM=GT.optional({factory:{sketch:e=>{const t=(e=>e.type===nf.sliding?GA:e.type===nf.floating?jA:$A)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),A.some(!0)),onToggled:(t,o)=>e.onToolbarToggled(o),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[os("dom"),os("onEscape"),os("getSink")]}),uM=GT.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?HE:tE;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:kl(o(t,e.sharedBackstage))}}},name:"header",schema:[os("dom")]}),mM=GT.optional({factory:{sketch:e=>({uid:e.uid,dom:e.dom,components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/tinymce-self-hosted-premium-features/?utm_source=TinyMCE&utm_medium=SPAP&utm_campaign=SPAP&utm_id=editorreferral",rel:"noopener",target:"_blank","aria-hidden":"true"},classes:["tox-promotion-link"],innerHtml:"\u26a1\ufe0fUpgrade"}}]})},name:"promotion",schema:[os("dom")]}),gM=GT.optional({name:"socket",schema:[os("dom")]}),pM=GT.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"presentation"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:kl([iS.config({}),oh.config({}),VO.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{wm.getCurrent(e).each(ZE.hideAllSlots),Fr(e,aA)},onGrown:e=>{Fr(e,aA)},onStartGrow:e=>{Ir(e,rA,{width:Nt(e.element,"width").getOr("")})},onStartShrink:e=>{Ir(e,rA,{width:Jt(e.element)+"px"})}}),Yp.config({}),wm.config({find:e=>{const t=Yp.contents(e);return oe(t)}})])}],behaviours:kl([MC(0),Jp("sidebar-sliding-events",[Ur(rA,((e,t)=>{Dt(e.element,"width",t.event.width)})),Ur(aA,((e,t)=>{Ht(e.element,"width")}))])])})},name:"sidebar",schema:[os("dom")]}),hM=GT.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:kl([Yp.config({}),cA.config({focus:!1}),wm.config({find:e=>oe(e.components())})]),components:[]})},name:"throbber",schema:[os("dom")]}),fM=GT.optional({factory:iM,name:"viewWrapper",schema:[os("backstage")]}),bM=GT.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-editor-container"]},components:e.components})},name:"editorContainer",schema:[]});var vM=bm({name:"OuterContainer",factory:(e,t,o)=>{let n=!1;const s={getSocket:t=>jT.getPart(t,e,"socket"),setSidebar:(t,o,n)=>{jT.getPart(t,e,"sidebar").each((e=>((e,t,o)=>{wm.getCurrent(e).each((n=>{Yp.set(n,[oA(t)]);const s=null==o?void 0:o.toLowerCase();r(s)&&ve(t,s)&&wm.getCurrent(n).each((t=>{ZE.showSlot(t,s),VO.immediateGrow(n),Ht(n.element,"width"),nA(e.element,"region")}))}))})(e,o,n)))},toggleSidebar:(t,o)=>{jT.getPart(t,e,"sidebar").each((e=>((e,t)=>{wm.getCurrent(e).each((o=>{wm.getCurrent(o).each((n=>{VO.hasGrown(o)?ZE.isShowing(n,t)?(VO.shrink(o),nA(e.element,"presentation")):(ZE.hideAllSlots(n),ZE.showSlot(n,t),nA(e.element,"region")):(ZE.hideAllSlots(n),ZE.showSlot(n,t),VO.grow(o),nA(e.element,"region"))}))}))})(e,o)))},whichSidebar:t=>jT.getPart(t,e,"sidebar").bind(sA).getOrNull(),getHeader:t=>jT.getPart(t,e,"header"),getToolbar:t=>jT.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{jT.getPart(t,e,"toolbar").each((e=>{const t=H(o,PA);e.getApis().setGroups(e,t)}))},setToolbars:(t,o)=>{jT.getPart(t,e,"multiple-toolbar").each((e=>{const t=H(o,(e=>H(e,PA)));KT.setItems(e,t)}))},refreshToolbar:t=>{jT.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{jT.getPart(t,e,"toolbar").each((e=>{ke(e.getApis().toggle,(t=>t(e)))}))},toggleToolbarDrawerWithoutFocusing:t=>{jT.getPart(t,e,"toolbar").each((e=>{ke(e.getApis().toggleWithoutFocusing,(t=>t(e)))}))},isToolbarDrawerToggled:t=>jT.getPart(t,e,"toolbar").bind((e=>A.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>jT.getPart(t,e,"throbber"),focusToolbar:t=>{jT.getPart(t,e,"toolbar").orThunk((()=>jT.getPart(t,e,"multiple-toolbar"))).each((e=>{Pp.focusIn(e)}))},setMenubar:(t,o)=>{jT.getPart(t,e,"menubar").each((e=>{$E.setMenus(e,o)}))},focusMenubar:t=>{jT.getPart(t,e,"menubar").each((e=>{$E.focus(e)}))},setViews:(t,o)=>{jT.getPart(t,e,"viewWrapper").each((e=>{iM.setViews(e,o)}))},toggleView:(t,o)=>jT.getPart(t,e,"viewWrapper").exists((e=>iM.toggleView(e,(()=>s.showMainView(t)),(()=>s.hideMainView(t)),o))),whichView:t=>jT.getPart(t,e,"viewWrapper").bind(iM.whichView).getOrNull(),hideMainView:t=>{n=s.isToolbarDrawerToggled(t),n&&s.toggleToolbarDrawer(t),jT.getPart(t,e,"editorContainer").each((e=>{const t=e.element;Dt(t,"display","none"),kt(t,"aria-hidden","true")}))},showMainView:t=>{n&&s.toggleToolbarDrawer(t),jT.getPart(t,e,"editorContainer").each((e=>{const t=e.element;Ht(t,"display"),Et(t,"aria-hidden")}))}};return{uid:e.uid,dom:e.dom,components:t,apis:s,behaviours:e.behaviours}},configFields:[os("dom"),os("behaviours")],partFields:[uM,lM,dM,cM,gM,pM,mM,hM,fM,bM],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o,n)=>{e.setSidebar(t,o,n)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{e.setToolbar(t,o)},setToolbars:(e,t,o)=>{e.setToolbars(t,o)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},toggleToolbarDrawerWithoutFocusing:(e,t)=>{e.toggleToolbarDrawerWithoutFocusing(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)},setViews:(e,t,o)=>{e.setViews(t,o)},toggleView:(e,t,o)=>e.toggleView(t,o),whichView:(e,t)=>e.whichView(t)}});const yM={file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template inserttemplate codesample inserttable accordion | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents footnotes | mergetags | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | autocorrect capitalization | a11ycheck code typography wordcount addtemplate"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},xM=e=>e.split(" "),wM=(e,t)=>{const o={...yM,...t.menus},n=ae(t.menus).length>0,s=void 0===t.menubar||!0===t.menubar?xM("file edit view insert format tools table help"):xM(!1===t.menubar?"":t.menubar),a=U(s,(e=>{const o=ve(yM,e);return n?o||be(t.menus,e).exists((e=>ve(e,"items"))):o})),i=H(a,(n=>{const s=o[n];return((e,t,o)=>{const n=kf(o).split(/[ ,]/);return{text:e.title,getItems:()=>X(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||N(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:s.title,items:xM(s.items)},t,e)}));return U(i,(e=>e.getItems().length>0&&N(e.getItems(),(e=>r(e)||"separator"!==e.type))))},SM=e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}},kM=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),CM=(e,t)=>kM(e,t+"/skin.min.css",e.ui.styleSheetLoader),OM=(e,t)=>{var o;return o=Ve(e.getElement()),bt(o).isSome()?kM(e,t+"/skin.shadowdom.min.css",rf.DOM.styleSheetLoader):Promise.resolve()},_M=(e,t)=>{const o=Yf(t);return o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+".min.css"),!Xf(t)&&r(o)?Promise.all([CM(t,o),OM(t,o)]).then(SM(t),((e,t)=>()=>((e,t)=>{e.dispatch("SkinLoadError",t)})(e,{message:"Skin could not be loaded"}))(t)):Promise.resolve(SM(t)())},TM=k(_M,!1),EM=k(_M,!0),AM=(e,t,o)=>{const n=(e,n,r,a)=>{const i=t.shared.providers.translate(e.title);if("separator"===e.type)return A.some({type:"separator",text:i});if("submenu"===e.type){const t=X(e.getStyleItems(),(e=>s(e,n,a)));return 0===n&&t.length<=0?A.none():A.some({type:"nestedmenuitem",text:i,enabled:t.length>0,getSubmenuItems:()=>X(e.getStyleItems(),(e=>s(e,n,a)))})}return A.some({type:"togglemenuitem",text:i,icon:e.icon,active:e.isSelected(a),enabled:!r,onAction:o.onAction(e),...e.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},s=(e,t,s)=>{const r="formatter"===e.type&&o.isInvalid(e);return 0===t?r?[]:n(e,t,!1,s).toArray():n(e,t,r,s).toArray()},r=e=>{const t=o.getCurrentValue(),n=o.shouldHide?0:1;return X(e,(e=>s(e,n,t)))};return{validateItems:r,getFetch:(e,t)=>(o,n)=>{const s=t(),a=r(s);n(lO(a,gb.CLOSE_ON_EXECUTE,e,{isHorizontalMenu:!1,search:A.none()}))}}},MM=(e,t,o)=>{const n=o.dataset,s="basic"===n.type?()=>H(n.data,(e=>bT(e,o.isSelectedFor,o.getPreviewFor))):n.getData;return{items:AM(0,t,o),getStyleItems:s}},DM=(e,t,o)=>{const{items:n,getStyleItems:s}=MM(0,t,o),r=vx(e,"NodeChange",(t=>{const n=t.getComponent();o.updateText(n),Rm.set(t.getComponent(),!e.selection.isEditable())}));return sO({text:o.icon.isSome()?A.none():o.text,icon:o.icon,tooltip:A.from(o.tooltip),role:A.none(),fetch:n.getFetch(t,s),onSetup:r,getApi:e=>({getComponent:x(e)}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",t.shared)};var BM;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(BM||(BM={}));const FM=(e,t,o)=>{const n=(s=((e,t)=>t===BM.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),H(s,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var s;return{type:"basic",data:n}},IM=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],RM=e=>{const t={type:"basic",data:IM};return{tooltip:"Align",text:A.none(),icon:A.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:e=>A.none,onAction:t=>()=>G(IM,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=G(IM,(t=>e.formatter.match(t.format))).fold(x("left"),(e=>e.title.toLowerCase()));Ir(t,nO,{icon:`align-${o}`})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},NM=(e,t)=>{const o=t(),n=H(o,(e=>e.format));return A.from(e.formatter.closest(n)).bind((e=>G(o,(t=>t.format===e)))).orThunk((()=>Ce(e.formatter.match("p"),{title:"Paragraph",format:"p"})))},VM=e=>{const t="Paragraph",o=FM(e,"block_formats",BM.SemiColon);return{tooltip:"Blocks",text:A.some(t),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:yx(e),updateText:n=>{const s=NM(e,(()=>o.data)).fold(x(t),(e=>e.title));Ir(n,oO,{text:s})},dataset:o,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},zM=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],HM=e=>{const t=e.split(/\s*,\s*/);return H(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},LM=e=>{const t="System Font",o=()=>{const o=e=>e?HM(e)[0]:"",s=e.queryCommandValue("FontName"),r=n.data,a=s?s.toLowerCase():"",i=G(r,(e=>{const t=e.format;return t.toLowerCase()===a||o(t).toLowerCase()===o(a).toLowerCase()})).orThunk((()=>Ce((e=>0===e.indexOf("-apple-system")&&(()=>{const t=HM(e.toLowerCase());return K(zM,(e=>t.indexOf(e.toLowerCase())>-1))})())(a),{title:t,format:a})));return{matchOpt:i,font:s}},n=FM(e,"font_family_formats",BM.SemiColon);return{tooltip:"Fonts",text:A.some(t),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=o();return e},getPreviewFor:e=>()=>A.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:e=>{const{matchOpt:t,font:n}=o(),s=t.fold(x(n),(e=>e.title));Ir(e,oO,{text:s})},dataset:n,shouldHide:!1,isInvalid:T}},PM={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},UM=(()=>{const e="[0-9]+",t="[eE][+-]?"+e,o=e=>`(?:${e})?`,n=["Infinity",e+"\\."+o(e)+o(t),"\\."+e+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),WM=(e,t)=>A.from(UM.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>N(t,(t=>N(PM[t],(t=>e===t)))))(n,t)?A.some({value:o,unit:n}):A.none()})),jM={tab:x(9),escape:x(27),enter:x(13),backspace:x(8),delete:x(46),left:x(37),up:x(38),right:x(39),down:x(40),space:x(32),home:x(36),end:x(35),pageUp:x(33),pageDown:x(34)},GM={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},$M={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},qM=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":be($M,e).getOr(e),XM=e=>be(GM,e).getOr(""),KM=e=>{const t=()=>{let t=A.none();const o=n.data,s=e.queryCommandValue("FontSize");if(s)for(let e=3;t.isNone()&&e>=0;e--){const n=qM(s,e),r=XM(n);t=G(o,(e=>e.format===s||e.format===n||e.format===r))}return{matchOpt:t,size:s}},o=x(A.none),n=FM(e,"font_size_formats",BM.Space);return{tooltip:"Font sizes",text:A.some("12pt"),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:e=>{const{matchOpt:o,size:n}=t(),s=o.fold(x(n),(e=>e.title));Ir(e,oO,{text:s})},dataset:n,shouldHide:!1,isInvalid:T}},YM=(e,t)=>{const o="Paragraph";return{tooltip:"Formats",text:A.some(o),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:yx(e),updateText:t=>{const n=e=>mT(e)?X(e.items,n):gT(e)?[{title:e.title,format:e.format}]:[],s=X(fT(e),n),r=NM(e,x(s)).fold(x(o),(e=>e.title));Ir(t,oO,{text:r})},shouldHide:wf(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}},JM=x([os("toggleClass"),os("fetch"),Fi("onExecute"),ys("getHotspot",A.some),ys("getAnchorOverrides",x({})),wc(),Fi("onItemExecute"),us("lazySink"),os("dom"),Di("onOpen"),hu("splitDropdownBehaviours",[dw,Pp,oh]),ys("matchWidth",!1),ys("useMinWidth",!1),ys("eventOrder",{}),us("role")].concat(Tw())),ZM=Uu({factory:Wh,schema:[os("dom")],name:"arrow",defaults:()=>({buttonBehaviours:kl([oh.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each(Rr)},buttonBehaviours:kl([dh.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])})}),QM=Uu({factory:Wh,schema:[os("dom")],name:"button",defaults:()=>({buttonBehaviours:kl([oh.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each((o=>{e.onExecute(o,t)}))}})}),eD=x([ZM,QM,ju({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[os("text")],name:"aria-descriptor"}),Wu({schema:[Ei()],name:"menu",defaults:e=>({onExecute:(t,o)=>{t.getSystem().getByUid(e.uid).each((n=>{e.onItemExecute(n,t,o)}))}})}),vw()]),tD=bm({name:"SplitDropdown",configFields:JM(),partFields:eD(),factory:(e,t,o,n)=>{const s=e=>{wm.getCurrent(e).each((e=>{Gm.highlightFirst(e),Pp.focusIn(e)}))},r=t=>{Sw(e,w,t,n,s,zh.HighlightMenuAndItem).get(b)},a=t=>{const o=nm(t,e,"button");return Rr(o),A.some(!0)},i={...Hr([Yr(((t,o)=>{om(t,e,"aria-descriptor").each((e=>{const o=la("aria");kt(e.element,"id",o),kt(t.element,"aria-describedby",o)}))}))]),...mh(A.some(r))},l={repositionMenus:e=>{dh.isOn(e)&&_w(e)}};return{uid:e.uid,dom:e.dom,components:t,apis:l,eventOrder:{...e.eventOrder,[ur()]:["disabling","toggling","alloy.base.behaviour"]},events:i,behaviours:bu(e.splitDropdownBehaviours,[dw.config({others:{sandbox:t=>{const o=nm(t,e,"arrow");return Ow(e,t,{onOpen:()=>{dh.on(o),dh.on(t)},onClose:()=>{dh.off(o),dh.off(t)}})}}}),Pp.config({mode:"special",onSpace:a,onEnter:a,onDown:e=>(r(e),A.some(!0))}),oh.config({}),dh.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:e.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:(e,t)=>e.repositionMenus(t)}}),oD=e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t),setText:t=>Ir(e,oO,{text:t}),setIcon:t=>Ir(e,nO,{icon:t})}),nD=e=>({setActive:t=>{dh.set(e,t)},isActive:()=>dh.isOn(e),isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t),setText:t=>Ir(e,oO,{text:t}),setIcon:t=>Ir(e,nO,{icon:t})}),sD=(e,t)=>e.map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),rD=la("focus-button"),aD=(e,t,o,n,s)=>{const r=t.map((e=>jh(tO(e,"tox-tbtn",s)))),a=e.map((e=>jh(eO(e,s.icons))));return{dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:sD(o,s)},components:My([a.map((e=>e.asSpec())),r.map((e=>e.asSpec()))]),eventOrder:{[Ws()]:["focusing","alloy.base.behaviour",KC],[Sr()]:[KC,"toolbar-group-button-events"]},buttonBehaviours:kl([Cy(s.isDisabled),wy(),Jp(KC,[Yr(((e,t)=>JC(e))),Ur(oO,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{Yp.set(e,[ti(s.translate(t.event.text))])}))})),Ur(nO,((e,t)=>{a.bind((t=>t.getOpt(e))).each((e=>{Yp.set(e,[eO(t.event.icon,s.icons)])}))})),Ur(Ws(),((e,t)=>{t.event.prevent(),Fr(e,rD)}))])].concat(n.getOr([])))}},iD=(e,t,o)=>{var n;const s=Es(b),r=aD(e.icon,e.text,e.tooltip,A.none(),o);return Wh.sketch({dom:r.dom,components:r.components,eventOrder:YC,buttonBehaviours:{...kl([Jp("toolbar-button-events",[(a={onAction:e.onAction,getApi:t.getApi},Qr(((e,t)=>{Oy(a,e)((t=>{Ir(e,XC,{buttonApi:t}),a.onAction(t)}))}))),_y(t,s),Ty(t,s)]),Cy((()=>!e.enabled||o.isDisabled())),wy()].concat(t.toolbarButtonBehaviours)),[KC]:null===(n=r.buttonBehaviours)||void 0===n?void 0:n[KC]}});var a},lD=(e,t,o)=>iD(e,{toolbarButtonBehaviours:o.length>0?[Jp("toolbarButtonWith",o)]:[],getApi:oD,onSetup:e.onSetup},t),cD=(e,t,o)=>iD(e,{toolbarButtonBehaviours:[Yp.config({}),dh.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[Jp("toolbarToggleButtonWith",o)]:[]),getApi:nD,onSetup:e.onSetup},t),dD=(e,t,o)=>n=>hw((e=>t.fetch(e))).map((s=>A.from(zw(fn(Jx(la("menu-value"),s,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,gb.CLOSE_ON_EXECUTE,t.select.getOr(T),o),{movement:Qx(t.columns,t.presets),menuBehaviours:iy("auto"!==t.columns?[]:[Yr(((e,o)=>{ay(e,4,Ob(t.presets)).each((({numRows:t,numColumns:o})=>{Pp.setGridSize(e,t,o)}))}))])}))))),uD=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],mD=(e,t)=>(o,n,s)=>{const r=e(o).mapError((e=>Yn(e))).getOrDie();return t(r,n,s)},gD={button:mD(Bv,((e,t)=>{return o=e,n=t.shared.providers,lD(o,n,[]);var o,n})),togglebutton:mD(Rv,((e,t)=>{return o=e,n=t.shared.providers,cD(o,n,[]);var o,n})),menubutton:mD(jE,((e,t)=>HO(e,"tox-tbtn",t,A.none(),!1))),splitbutton:mD((e=>qn("SplitButton",GE,e)),((e,t)=>((e,t)=>{const o=e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t),setIconFill:(t,o)=>{pi(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{kt(e,"fill",o)}))},setActive:t=>{kt(e.element,"aria-pressed",t),pi(e.element,"span").each((o=>{e.getSystem().getByDom(o).each((e=>dh.set(e,t)))}))},isActive:()=>pi(e.element,"span").exists((t=>e.getSystem().getByDom(t).exists(dh.isOn))),setText:t=>pi(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Ir(e,oO,{text:t}))))),setIcon:t=>pi(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Ir(e,nO,{icon:t})))))}),n=Es(b),s={getApi:o,onSetup:e.onSetup};return tD.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:{"aria-pressed":!1,...sD(e.tooltip,t.providers)}},onExecute:t=>{const n=o(t);n.isEnabled()&&e.onAction(n)},onItemExecute:(e,t,o)=>{},splitDropdownBehaviours:kl([ky(t.providers.isDisabled),wy(),Jp("split-dropdown-events",[Yr(((e,t)=>JC(e))),Ur(rD,oh.focus),_y(s,n),Ty(s,n)]),BS.config({})]),eventOrder:{[Sr()]:["alloy.base.behaviour","split-dropdown-events"]},toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:dD(o,e,t.providers),parts:{menu:Db(0,e.columns,e.presets)},components:[tD.parts.button(aD(e.icon,e.text,A.none(),A.some([dh.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),tD.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:Jh("chevron-down",t.providers.icons)},buttonBehaviours:kl([ky(t.providers.isDisabled),wy(),Zh()])}),tD.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})})(e,t.shared))),grouptoolbarbutton:mD((e=>qn("GroupToolbarButton",PE,e)),((e,t,o)=>{const n=o.ui.registry.getAll().buttons,s={[yc]:t.shared.header.isPositionedAtTop()?vc.TopToBottom:vc.BottomToTop};if(Cf(o)===nf.floating)return((e,t,o,n)=>{const s=t.shared,r=Es(b),a={toolbarButtonBehaviours:[],getApi:oD,onSetup:e.onSetup},i=[Jp("toolbar-group-button-events",[_y(a,r),Ty(a,r)])];return EA.sketch({lazySink:s.getSink,fetch:()=>hw((t=>{t(H(o(e.items),PA))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:aD(e.icon,e.text,e.tooltip,A.some(i),s.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t,(e=>hD(o,{buttons:n,toolbar:e,allowToolbarGroups:!1},t,A.none())),s);throw new Error("Toolbar groups are only supported when using floating toolbar mode")}))},pD={styles:(e,t)=>{const o={type:"advanced",...t.styles};return DM(e,t,YM(e,o))},fontsize:(e,t)=>DM(e,t,KM(e)),fontsizeinput:(e,t)=>((e,t,o)=>{let n=A.none();const s=vx(e,"NodeChange",(t=>{const s=t.getComponent();n=A.some(s),o.updateInputValue(s),Rm.set(s,!e.selection.isEditable())})),r=e=>({getComponent:x(e)}),a=Es(b),i=la("custom-number-input-events"),l=(e,t,s)=>{const r=n.map((e=>pu.getValue(e))).getOr(""),a=o.getNewValue(r,e),i=r.length-`${a}`.length,l=n.map((e=>e.element.dom.selectionStart-i)),c=n.map((e=>e.element.dom.selectionEnd-i));o.onAction(a,s),n.each((e=>{pu.setValue(e,a),t&&(l.each((t=>e.element.dom.selectionStart=t)),c.each((t=>e.element.dom.selectionEnd=t)))}))},c=(e,t)=>l(((e,t)=>e-t),e,t),d=(e,t)=>l(((e,t)=>e+t),e,t),u=e=>rt(e.element).fold(A.none,(e=>(Dl(e),A.some(!0)))),m=e=>Fl(e.element)?(ct(e.element).each((e=>Dl(e))),A.some(!0)):A.none(),g=(o,n,s,i)=>{const l=t.shared.providers.translate(s),c=la("altExecuting"),d=vx(e,"NodeChange",(t=>{Rm.set(t.getComponent(),!e.selection.isEditable())})),u=e=>{Rm.isDisabled(e)||o(!0)};return Wh.sketch({dom:{tag:"button",attributes:{title:l,"aria-label":l},classes:i.concat(n)},components:[QC(n,t.shared.providers.icons)],buttonBehaviours:kl([Rm.config({}),Jp(c,[_y({onSetup:d,getApi:r},a),Ty({getApi:r},a),Ur(Ys(),((e,t)=>{t.event.raw.keyCode!==jM.space()&&t.event.raw.keyCode!==jM.enter()||Rm.isDisabled(e)||o(!1)})),Ur(er(),u),Ur(Ps(),u)])]),eventOrder:{[Ys()]:[c,"keying"],[er()]:[c,"alloy.base.behaviour"],[Ps()]:[c,"alloy.base.behaviour"]}})},p=jh(g((e=>c(!1,e)),"minus","Decrease font size",["highlight-on-focus"])),h=jh(g((e=>d(!1,e)),"plus","Increase font size",["highlight-on-focus"])),f=jh({dom:{tag:"div",classes:["tox-input-wrapper","highlight-on-focus"]},components:[Nb.sketch({inputBehaviours:kl([Rm.config({}),Jp(i,[_y({onSetup:s,getApi:r},a),Ty({getApi:r},a)]),Jp("input-update-display-text",[Ur(oO,((e,t)=>{pu.setValue(e,t.event.text)})),Ur(Ks(),(e=>{o.onAction(pu.getValue(e))})),Ur(Qs(),(e=>{o.onAction(pu.getValue(e))}))]),Pp.config({mode:"special",onEnter:e=>(l(w,!0,!0),A.some(!0)),onEscape:u,onUp:e=>(d(!0,!1),A.some(!0)),onDown:e=>(c(!0,!1),A.some(!0)),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})],behaviours:kl([oh.config({}),Pp.config({mode:"special",onEnter:m,onSpace:m,onEscape:u}),Jp("input-wrapper-events",[Ur(qs(),(e=>{L([p,h],(t=>{const o=Ve(t.get(e).element.dom);Fl(o)&&Bl(o)}))}))])])});return{dom:{tag:"div",classes:["tox-number-input"]},components:[p.asSpec(),f.asSpec(),h.asSpec()],behaviours:kl([oh.config({}),Pp.config({mode:"flow",focusInside:pg.OnEnterOrSpaceMode,cycles:!1,selector:"button, .tox-input-wrapper",onEscape:e=>Fl(e.element)?A.none():(Dl(e.element),A.some(!0))})])}})(e,t,(e=>{const t=()=>e.queryCommandValue("FontSize");return{updateInputValue:e=>Ir(e,oO,{text:t()}),onAction:(t,o)=>e.execCommand("FontSize",!1,t,{skip_focus:!o}),getNewValue:(o,n)=>{WM(o,["unsupportedLength","empty"]);const s=WM(o,["unsupportedLength","empty"]).or(WM(t(),["unsupportedLength","empty"])),r=s.map((e=>e.value)).getOr(16),a=Rf(e),i=s.map((e=>e.unit)).filter((e=>""!==e)).getOr(a),l=n(r,(e=>{var t;return null!==(t={em:{step:.1},cm:{step:.1},in:{step:.1},pc:{step:.1},ch:{step:.1},rem:{step:.1}}[e])&&void 0!==t?t:{step:1}})(i).step);return`${(e=>e>=0)(l)?l:r}${i}`}}})(e)),fontfamily:(e,t)=>DM(e,t,LM(e)),blocks:(e,t)=>DM(e,t,VM(e)),align:(e,t)=>DM(e,t,RM(e))},hD=(e,t,o,n)=>{const s=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=H(uD,(t=>{const o=U(t.items,(t=>ve(e,t)||ve(pD,t)));return{name:t.name,items:o}}));return U(t,(e=>e.items.length>0))})(o):r(t)?(e=>{const t=e.split("|");return H(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>ve(e,"name")&&ve(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=H(s,(s=>{const r=X(s.items,(s=>0===s.trim().length?[]:((e,t,o,n,s,r)=>be(t,o.toLowerCase()).orThunk((()=>r.bind((e=>re(e,(e=>be(t,e+o.toLowerCase()))))))).fold((()=>be(pD,o.toLowerCase()).map((t=>t(e,s)))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o)=>be(gD,e.type).fold((()=>(console.error("skipping button defined by",e),A.none())),(n=>A.some(n(e,t,o)))))(t,s,e):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),A.none()))))(e,t.buttons,s,t.allowToolbarGroups,o,n).toArray()));return{title:A.from(e.translate(s.name)),items:r}}));return U(a,(e=>e.items.length>0))},fD=(e,t,o,n)=>{const s=t.mainUi.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,r)){const t=a.map((t=>{const s={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return hD(e,s,n,A.none())}));vM.setToolbars(s,t)}else vM.setToolbar(s,hD(e,o,n,A.none()))},bD=Do(),vD=bD.os.isiOS()&&bD.os.version.major<=12;var yD=Object.freeze({__proto__:null,render:async(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=t,i=Es(0),l=r.outerContainer;await TM(e);const d=Ve(s.targetNode),u=ft(ht(d));Rd(d,r.mothership),((e,t,o)=>{ib(e)&&Rd(o.mainUi.mothership.element,o.popupUi.mothership),Id(t,o.dialogUi.mothership)})(e,u,t),e.on("PostRender",(()=>{vM.setSidebar(l,o.sidebar,$f(e)),fD(e,t,o,n),i.set(e.getWin().innerWidth),vM.setMenubar(l,wM(e,o)),vM.setViews(l,o.views),((e,t)=>{const{uiMotherships:o}=t,n=e.dom;let s=e.getWin();const r=e.getDoc().documentElement,a=Es($t(s.innerWidth,s.innerHeight)),i=Es($t(r.offsetWidth,r.offsetHeight)),l=()=>{const t=a.get();t.left===s.innerWidth&&t.top===s.innerHeight||(a.set($t(s.innerWidth,s.innerHeight)),mx(e))},c=()=>{const t=e.getDoc().documentElement,o=i.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(i.set($t(t.offsetWidth,t.offsetHeight)),mx(e))},d=t=>{((e,t)=>{e.dispatch("ScrollContent",t)})(e,t)};n.bind(s,"resize",l),n.bind(s,"scroll",d);const u=oc(Ve(e.getBody()),"load",c);e.on("hide",(()=>{L(o,(e=>{Dt(e.element,"display","none")}))})),e.on("show",(()=>{L(o,(e=>{Ht(e.element,"display")}))})),e.on("NodeChange",c),e.on("remove",(()=>{u.unbind(),n.unbind(s,"resize",l),n.unbind(s,"scroll",d),s=null}))})(e,t)}));const m=vM.getSocket(l).getOrDie("Could not find expected socket element");if(vD){Bt(m.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=((e,t)=>{let o=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null)},throttle:(...t)=>{c(o)&&(o=setTimeout((()=>{o=null,e.apply(null,t)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=tc(m.element,"scroll",t.throttle);e.on("remove",o.unbind)}xy(e,t),e.addCommand("ToggleSidebar",((t,o)=>{vM.toggleSidebar(l,o),e.dispatch("ToggleSidebar")})),e.addQueryValueHandler("ToggleSidebar",(()=>{var e;return null!==(e=vM.whichSidebar(l))&&void 0!==e?e:""})),e.addCommand("ToggleView",((t,o)=>{if(vM.toggleView(l,o)){const t=l.element;r.mothership.broadcastOn([Kd()],{target:t}),L(a,(e=>{e.broadcastOn([Kd()],{target:t})})),c(vM.whichView(l))&&(e.focus(),e.nodeChanged(),vM.refreshToolbar(l))}})),e.addQueryValueHandler("ToggleView",(()=>{var e;return null!==(e=vM.whichView(l))&&void 0!==e?e:""}));const g=Cf(e);g!==nf.sliding&&g!==nf.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==i.get()&&(vM.refreshToolbar(t.mainUi.outerContainer),i.set(o))}));const p={setEnabled:e=>{yy(t,!e)},isEnabled:()=>!Rm.isDisabled(l)};return{iframeContainer:m.element.dom,editorContainer:l.element.dom,api:p}}});const xD=e=>/^[0-9\.]+(|px)$/i.test(""+e)?A.some(parseInt(""+e,10)):A.none(),wD=e=>h(e)?e+"px":e,SD=(e,t,o)=>{const n=t.filter((t=>e<t)),s=o.filter((t=>e>t));return n.or(s).getOr(e)},kD=e=>{const t=pf(e),o=hf(e),n=bf(e);return xD(t).map((e=>SD(e,o,n)))},{ToolbarLocation:CD,ToolbarMode:OD}=cb,_D=(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=o,i=rf.DOM,l=nb(e),c=ab(e),d=bf(e).or(kD(e)),u=n.shared.header,m=u.isPositionedAtTop,g=Cf(e),p=g===OD.sliding||g===OD.floating,h=Es(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(x(0),(e=>e.components().length>1?Wt(e.components()[1].element):0)):0,v=()=>{L(a,(e=>{e.broadcastOn([Yd()],{})}))},y=o=>{if(!f())return;l||s.on((e=>{const o=d.getOrThunk((()=>{const e=xD(It(xt(),"margin-left")).getOr(0);return Jt(xt())-Xt(t).left+e}));Dt(e.element,"max-width",o+"px")}));const n=l?A.none():(()=>{if(l)return A.none();if(Xt(r.outerContainer.element).left+Zt(r.outerContainer.element)>=window.innerWidth-40||Nt(r.outerContainer.element,"width").isSome()){Dt(r.outerContainer.element,"position","absolute"),Dt(r.outerContainer.element,"left","0px"),Ht(r.outerContainer.element,"width");const e=Zt(r.outerContainer.element);return A.some(e)}return A.none()})();p&&vM.refreshToolbar(r.outerContainer),l||(o=>{s.on((n=>{const s=vM.getToolbar(r.outerContainer),a=b(s),i=Jo(t),{top:l,left:c}=((e,t)=>ib(e)?sE(t):A.none())(e,r.outerContainer.element).fold((()=>({top:m()?Math.max(i.y-Wt(n.element)+a,0):i.bottom,left:i.x})),(e=>{var t;const o=Jo(e),s=null!==(t=e.dom.scrollTop)&&void 0!==t?t:0,r=Ze(e,xt()),l=r?Math.max(i.y-Wt(n.element)+a,0):i.y-o.y+s-Wt(n.element)+a;return{top:m()?l:i.bottom,left:r?i.x:i.x-o.x}})),d={position:"absolute",left:Math.round(c)+"px",top:Math.round(l)+"px"},u=o.map((e=>{const t=Uo(),o=window.innerWidth-(c-t.left);return{width:Math.max(Math.min(e,o),150)+"px"}})).getOr({});Bt(r.outerContainer.element,{...d,...u})}))})(n),c&&s.on(o),v()},w=()=>!(l||!c||!f())&&s.get().exists((o=>{const n=u.getDockingMode(),a=(o=>{switch(_f(e)){case CD.auto:const e=vM.getToolbar(r.outerContainer),n=b(e),s=Wt(o.element)-n,a=Jo(t);if(a.y>s)return"top";{const e=ot(t),o=Math.max(e.dom.scrollHeight,Wt(e));return a.bottom<o-s||en().bottom<a.bottom-s?"bottom":"top"}case CD.bottom:return"bottom";case CD.top:default:return"top"}})(o);return a!==n&&(i=a,s.on((e=>{ME.setModes(e,[i]),u.setDockingMode(i);const t=m()?vc.TopToBottom:vc.BottomToTop;kt(e.element,yc,t)})),!0);var i}));return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),Dt(r.outerContainer.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),L(a,(e=>{Ht(e.element,"display")})),w(),ib(e)?y((e=>ME.isDocked(e)?ME.reset(e):ME.refresh(e))):y(ME.refresh)},hide:()=>{h.set(!1),Dt(r.outerContainer.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus"),L(a,(e=>{Dt(e.element,"display","none")}))},update:y,updateMode:()=>{w()&&y(ME.reset)},repositionPopups:v}},TD=(e,t)=>{const o=Jo(e);return{pos:t?o.y:o.bottom,bounds:o}};var ED=Object.freeze({__proto__:null,render:async(e,t,o,n,s)=>{const{mainUi:r}=t,a=Ql(),i=Ve(s.targetNode),l=_D(e,i,t,n,a),c=Af(e);await EM(e);const d=()=>{if(a.isSet())return void l.show();a.set(vM.getHeader(r.outerContainer).getOrDie());const s=sb(e);ib(e)?(Rd(i,r.mothership),Rd(i,t.popupUi.mothership)):Id(s,r.mothership),Id(s,t.dialogUi.mothership),fD(e,t,o,n),vM.setMenubar(r.outerContainer,wM(e,o)),l.show(),((e,t,o,n)=>{const s=Es(TD(t,o.isPositionedAtTop())),r=n=>{const{pos:r,bounds:a}=TD(t,o.isPositionedAtTop()),{pos:i,bounds:l}=s.get(),c=a.height!==l.height||a.width!==l.width;s.set({pos:r,bounds:a}),c&&mx(e,n),o.isVisible()&&(i!==r?o.update(ME.reset):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update(ME.reset))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>r(e)))}));let a=0;const i=JO((()=>o.update(ME.refresh)),33);e.on("ScrollWindow",(()=>{const e=Uo().left;e!==a&&(a=e,i.throttle()),o.updateMode()})),ib(e)&&e.on("ElementScroll",(e=>{o.update(ME.refresh)}));const l=Zl();l.set(oc(Ve(e.getBody()),"load",(e=>r(e.raw)))),e.on("remove",(()=>{l.clear()}))})(e,i,l,c),e.nodeChanged()};e.on("show",d),e.on("hide",l.hide),c||(e.on("focus",d),e.on("blur",l.hide)),e.on("init",(()=>{(e.hasFocus()||c)&&d()})),xy(e,t);const u={show:d,hide:l.hide,setEnabled:e=>{yy(t,!e)},isEnabled:()=>!Rm.isDisabled(r.outerContainer)};return{editorContainer:r.outerContainer.element.dom,api:u}}});const AD="contexttoolbar-hide",MD=(e,t)=>Ur(XC,((o,n)=>{const s=(e=>({hide:()=>Fr(e,hr()),getValue:()=>pu.getValue(e)}))(e.get(o));t.onAction(s,n.event.buttonApi)})),DD=(e,t)=>{const o=e.label.fold((()=>({})),(e=>({"aria-label":e}))),n=jh(Nb.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:e.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:kl([Pp.config({mode:"special",onEnter:e=>s.findPrimary(e).map((e=>(Rr(e),!0))),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})),s=((e,t,o)=>{const n=H(t,(t=>jh(((e,t,o)=>(e=>"contextformtogglebutton"===e.type)(t)?((e,t,o)=>{const{primary:n,...s}=t.original,r=Xn(Rv({...s,type:"togglebutton",onAction:b}));return cD(r,o,[MD(e,t)])})(e,t,o):((e,t,o)=>{const{primary:n,...s}=t.original,r=Xn(Bv({...s,type:"button",onAction:b}));return lD(r,o,[MD(e,t)])})(e,t,o))(e,t,o))));return{asSpecs:()=>H(n,(e=>e.asSpec())),findPrimary:e=>re(t,((t,o)=>t.primary?A.from(n[o]).bind((t=>t.getOpt(e))).filter(C(Rm.isDisabled)):A.none()))}})(n,e.commands,t);return[{title:A.none(),items:[n.asSpec()]},{title:A.none(),items:s.asSpecs()}]},BD=(e,t,o)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,FD=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=ut(Ve(e.startContainer),e.startOffset).element;return($e(o)?st(o):A.some(o)).filter(Ge).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=Uo();return Yo(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=Zo(Ve(e.getBody()));return Yo(o.x+t.left,o.y+t.top,t.width,t.height)}},ID=(e,t,o,n=0)=>{const s=Go(window),r=Jo(Ve(e.getContentAreaContainer())),a=Kf(e)||Zf(e)||eb(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(r,s,n);if(e.inline&&!a)return Yo(i,s.y,l,s.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,s,r)=>{const a=Ve(e.getContainer()),i=pi(a,".tox-editor-header").getOr(a),l=Jo(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+r,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-r,o.bottom)};const u="line"===s?Jo(a):t;return d?{y:Math.max(l.bottom+r,o.y),bottom:Math.min(u.bottom-r,o.bottom)}:{y:Math.max(u.y+r,o.y),bottom:Math.min(l.y-r,o.bottom)}})(e,r,s,a,o,n);return Yo(i,c,l,d-c)}},RD={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},ND={maxHeightFunction:cc(),maxWidthFunction:wA()},VD=e=>"node"===e,zD=(e,t,o,n,s)=>{const r=FD(e),a=n.lastElement().exists((e=>Ze(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=ut(Ve(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&Ze(n.element,t)})(e,o)?a?Z_:q_:a?((e,o,s)=>{const a=Nt(e,"position");Dt(e,"position",o);const i=BD(r,Jo(t),-20)&&!n.isReposition()?eT:Z_;return a.each((t=>Dt(e,"position",t))),i})(t,n.getMode()):("fixed"===n.getMode()?s.y+Uo().top:s.y)+(Wt(t)+12)<=r.y?q_:X_},HD=(e,t,o,n)=>{const s=t=>(n,s,r,a,i)=>({...zD(e,a,t,o,i)({...n,y:i.y,height:i.height},s,r,a,i),alwaysFit:!0}),r=e=>VD(n)?[s(e)]:[];return t?{onLtr:e=>[cl,sl,rl,al,il,ll].concat(r(e)),onRtl:e=>[cl,rl,sl,il,al,ll].concat(r(e))}:{onLtr:e=>[ll,cl,al,sl,il,rl].concat(r(e)),onRtl:e=>[ll,cl,il,rl,al,sl].concat(r(e))}},LD=(e,t)=>{const o=U(t,(t=>t.predicate(e.dom))),{pass:n,fail:s}=P(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:s}},PD=(e,t)=>{const o={},n=[],s=[],r={},a={},i=ae(e);return L(i,(i=>{const l=e[i];"contextform"===l.type?((e,i)=>{const l=Xn(qn("ContextForm",Uv,i));o[e]=l,l.launch.map((o=>{r["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?s.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,t)=>{var o;(o=t,qn("ContextToolbar",Wv,o)).each((o=>{"editor"===t.scope?s.push(o):n.push(o),a[e]=o}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:s,lookupTable:a,formNavigators:r}},UD=la("forward-slide"),WD=la("backward-slide"),jD=la("change-slide-event"),GD="tox-pop--resizing",$D="tox-pop--transition",qD=(e,t,o,n)=>{const s=n.backstage,r=s.shared,a=Do().deviceType.isTouch,i=Ql(),l=Ql(),c=Ql(),d=ri((e=>{const t=Es([]);return Ph.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),Ph.getContent(e).each((e=>{Ht(e.element,"visibility")})),Pa(e.element,GD),Ht(e.element,"width")},inlineBehaviours:kl([Jp("context-toolbar-events",[Kr(or(),((e,t)=>{"width"===t.event.raw.propertyName&&(Pa(e.element,GD),Ht(e.element,"width"))})),Ur(jD,((e,t)=>{const o=e.element;Ht(o,"width");const n=Jt(o);Ph.setContent(e,t.event.contents),La(o,GD);const s=Jt(o);Dt(o,"width",n+"px"),Ph.getContent(e).each((e=>{t.event.focus.bind((e=>(Dl(e),Rl(o)))).orThunk((()=>(Pp.focusIn(e),Il(ht(o)))))})),setTimeout((()=>{Dt(e.element,"width",s+"px")}),0)})),Ur(UD,((e,o)=>{Ph.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:Il(ht(e.element))}]))})),Ir(e,jD,{contents:o.event.forwardContents,focus:A.none()})})),Ur(WD,((e,o)=>{ne(t.get()).each((o=>{t.set(t.get().slice(0,t.get().length-1)),Ir(e,jD,{contents:ai(o.bar),focus:o.focus})}))}))]),Pp.config({mode:"special",onEscape:o=>ne(t.get()).fold((()=>e.onEscape()),(e=>(Fr(o,WD),A.some(!0))))})]),lazySink:()=>sn.value(e.sink)})})({sink:o,onEscape:()=>(e.focus(),A.some(!0))})),u=()=>{const t=c.get().getOr("node"),o=VD(t)?1:0;return ID(e,r,t,o)},m=()=>!(e.removed||a()&&s.isContextMenuOpen()),g=()=>{if(m()){const t=u(),o=xe(c.get(),"node")?((e,t)=>t.filter((e=>yt(e)&&je(e))).map(Zo).getOrThunk((()=>FD(e))))(e,i.get()):FD(e);return t.height<=0||!BD(o,t,.01)}return!0},p=()=>{i.clear(),l.clear(),c.clear(),Ph.hide(d)},h=()=>{if(Ph.isOpen(d)){const e=d.element;Ht(e,"display"),g()?Dt(e,"display","none"):(l.set(0),Ph.reposition(d))}},f=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:kl([Pp.config({mode:"acyclic"}),Jp("pop-dialog-wrap-events",[Yr((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>Pp.focusIn(t)))})),Jr((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),v=Qt((()=>PD(t,(e=>{const t=y([e]);Ir(d,UD,{forwardContents:f(t)})})))),y=t=>{const{buttons:o}=e.ui.registry.getAll(),s={...o,...v().formNavigators},a=Cf(e)===nf.scrolling?nf.scrolling:nf.default,i=q(H(t,(t=>"contexttoolbar"===t.type?((t,o)=>hD(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n.backstage,A.some(["form:"])))(s,t):((e,t)=>DD(e,t))(t,r.providers))));return $A({type:a,uid:la("context-toolbar"),initGroups:i,onEscape:A.none,cyclicKeying:!0,providers:r.providers})},x=(t,n)=>{if(S.cancel(),!m())return;const s=y(t),p=t[0].position,h=((t,n)=>{const s="node"===t?r.anchors.node(n):r.anchors.cursor(),c=((e,t,o,n)=>"line"===t?{bubble:gc(12,0,RD),layouts:{onLtr:()=>[dl],onRtl:()=>[ul]},overrides:ND}:{bubble:gc(0,12,RD,1/12),layouts:HD(e,o,n,t),overrides:ND})(e,t,a(),{lastElement:i.get,isReposition:()=>xe(l.get(),0),getMode:()=>Sd.getMode(o)});return fn(s,c)})(p,n);c.set(p),l.set(1);const b=d.element;Ht(b,"display"),(e=>xe(Se(e,i.get(),Ze),!0))(n)||(Pa(b,$D),Sd.reset(o,d)),Ph.showWithinBounds(d,f(s),{anchor:h,transition:{classes:[$D],mode:"placement"}},(()=>A.some(u()))),n.fold(i.clear,i.set),g()&&Dt(b,"display","none")};let w=!1;const S=JO((()=>{!e.hasFocus()||e.removed||w||(Ua(d.element,$D)?S.throttle():((e,t)=>{const o=Ve(t.getBody()),n=e=>Ze(e,o),s=Ve(t.selection.getNode());return(e=>!n(e)&&!Qe(o,e))(s)?A.none():((e,t,o)=>{const n=LD(e,t);if(n.contextForms.length>0)return A.some({elem:e,toolbars:[n.contextForms[0]]});{const t=LD(e,o);if(t.contextForms.length>0)return A.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>N(e,(e=>e.position===t)),o=t=>U(e,(e=>e.position===t)),n=t("selection"),s=t("node");if(n||s){if(s&&n){const e=o("node"),t=H(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return A.some({elem:e,toolbars:o})}return A.none()}})(s,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?A.none():Fs(t,(e=>{if(Ge(e)){const{contextToolbars:t,contextForms:n}=LD(e,o.inNodeScope),s=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>G(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>U(e,(e=>e.position===t))))}})(t);return s.length>0?A.some({elem:e,toolbars:s}):A.none()}return A.none()}),e))(n,s,e)))})(v(),e).fold(p,(e=>{x(e.toolbars,A.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",p),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",h),e.on("click keyup focus SetContent",S.throttle),e.on(AD,p),e.on("contexttoolbar-show",(t=>{const o=v();be(o.lookupTable,t.toolbarKey).each((o=>{x([o],Ce(t.target!==e,t.target)),Ph.getContent(d).each(Pp.focusIn)}))})),e.on("focusout",(t=>{Uh.setEditorTimeout(e,(()=>{Rl(o.element).isNone()&&Rl(d.element).isNone()&&p()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&p()})),e.on("AfterProgressState",(t=>{t.state?p():e.hasFocus()&&S.throttle()})),e.on("dragstart",(()=>{w=!0})),e.on("dragend drop",(()=>{w=!1})),e.on("NodeChange",(e=>{Rl(d.element).fold(S.throttle,b)}))}))},XD=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),s=Ql();return H(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:r=>{const a=e=>{e&&(s.on((e=>e.setActive(!1))),s.set(r)),r.setActive(e)};a(xe(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{s.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},KD=e=>{XD(e,(e=>({name:"lineheight",text:"Line height",icon:"line-height",getOptions:Jf,hash:e=>((e,t)=>WM(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:w,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>A.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t),onToolbarSetup:fx(e),onMenuSetup:fx(e)}))(e)),(e=>A.from(Sf(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:x(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>{var n;return e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:null!==(n=t.customCode)&&void 0!==n?n:null}).unbind},getCurrent:e=>{const t=Ve(e.selection.getNode());return Is(t,(e=>A.some(e).filter(Ge).bind((e=>_t(e,"lang").map((t=>({code:t,customCode:_t(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=Zl();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),hx(o.clear,fx(e)(t))},onMenuSetup:fx(e)}))))(e).each((t=>XD(e,t)))},YD=e=>vx(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent")&&e.selection.isEditable())})),JD=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),hx((()=>e.off("PastePlainTextToggle",n)),fx(e)(o))},ZD=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},QD=e=>{(e=>{(e=>{LC.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:bx(e,t.name),onAction:ZD(e,t.name)})}));for(let t=1;t<=6;t++){const o="h"+t;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:bx(e,o),onAction:ZD(e,o)})}})(e),(e=>{LC.each([{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"print",text:"Print",action:"mcePrint",icon:"print"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:xx(e,t.action)})})),LC.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:fx(e),onAction:xx(e,t.action)})}))})(e),(e=>{LC.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:xx(e,t.action),onSetup:bx(e,t.name)})}))})(e)})(e),(e=>{LC.each([{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:xx(e,t.action)})})),LC.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onSetup:fx(e),onAction:xx(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onSetup:fx(e),onAction:ZD(e,"code")})})(e)},eB=(e,t)=>vx(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),tB=e=>vx(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),oB=(e,t)=>{(e=>{L([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:xx(e,t.cmd),onSetup:bx(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onSetup:fx(e),onAction:xx(e,"JustifyNone")})})(e),QD(e),((e,t)=>{((e,t)=>{const o=MM(0,t,RM(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),onSetup:fx(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=MM(0,t,LM(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),onSetup:fx(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=MM(0,t,YM(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",onSetup:fx(e),getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=MM(0,t,VM(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",onSetup:fx(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=MM(0,t,KM(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",onSetup:fx(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:eB(e,"hasUndo"),onAction:xx(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:eB(e,"hasRedo"),onAction:xx(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:eB(e,"hasUndo"),onAction:xx(e,"undo")}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:eB(e,"hasRedo"),onAction:xx(e,"redo")})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},void 0,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=Hx(e),o=Lx(e),n=Es(t),s=Es(o);Xx(e,"forecolor","forecolor","Text color",n),Xx(e,"backcolor","hilitecolor","Background color",s),Kx(e,"forecolor","forecolor","Text color",n),Kx(e,"backcolor","hilitecolor","Background color",s)})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:xx(e,"mceToggleVisualAid")})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:tB(e),onAction:xx(e,"mceToggleVisualAid")})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:YD(e),onAction:xx(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onSetup:fx(e),onAction:xx(e,"indent")})})(e)})(e),KD(e),(e=>{const t=Es(Gf(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:JD(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:JD(e,t)})})(e)},nB=e=>r(e)?e.split(/[ ,]/):e,sB=e=>t=>t.options.get(e),rB=sB("contextmenu_never_use_native"),aB=sB("contextmenu_avoid_overlap"),iB=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:U(o,(e=>ve(t,e)))},lB=(e,t)=>({type:"makeshift",x:e,y:t}),cB=e=>"longpress"===e.type||0===e.type.indexOf("touch"),dB=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if(cB(e)){const t=e.touches[0];return lB(t.pageX,t.pageY)}return lB(e.pageX,e.pageY)})(t):((e,t)=>{const o=rf.DOM.getPos(e);return((e,t,o)=>lB(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if(cB(e)){const t=e.touches[0];return lB(t.clientX,t.clientY)}return lB(e.clientX,e.clientY)})(t)):uB(e),uB=e=>({type:"selection",root:Ve(e.selection.getNode())}),mB=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:A.some(Ve(e.selection.getNode())),root:Ve(e.getBody())}))(e);case"point":return dB(e,t);case"selection":return uB(e)}},gB=(e,t,o,n,s,r)=>{const a=o(),i=mB(e,t,r);lO(a,gb.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!1,search:A.none()}).map((e=>{t.preventDefault(),Ph.showMenuAt(s,{anchor:i},{menu:{markers:Eb("normal")},data:e})}))},pB={onLtr:()=>[cl,sl,rl,al,il,ll,q_,X_,$_,j_,G_,W_],onRtl:()=>[cl,rl,sl,il,al,ll,q_,X_,G_,W_,$_,j_]},hB={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},fB=(e,t,o,n,s,r)=>{const a=Do(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>{const n=mB(e,t,o);return{bubble:gc(0,"point"===o?12:0,hB),layouts:pB,overrides:{maxWidthFunction:wA(),maxHeightFunction:cc()},...n}})(e,t,r);lO(o,gb.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!0,search:A.none()}).map((o=>{t.preventDefault();const l=a?zh.HighlightMenuAndItem:zh.HighlightNone;Ph.showMenuWithinBounds(s,{anchor:i},{menu:{markers:Eb("normal"),highlightOnOpen:l},data:o,type:"horizontal"},(()=>A.some(ID(e,n.shared,"node"===r?"node":"selection")))),e.dispatch(AD)}))})(e,t,a,n,s,r,!(c||i||l&&d))};if((l||i)&&"node"!==r){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{Uh.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),r()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const s=()=>r();e.once("longpresscancel",s);const r=()=>{e.off("touchend",o),e.off("longpresscancel",s),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],s=o.getRng();return Jc(e.getWin(),Lc.domRange(s)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},bB=e=>r(e)?"|"===e:"separator"===e.type,vB={type:"separator"},yB=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(r(e))return e;switch(e.type){case"separator":return vB;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return r(t)?t:H(t,yB)}};default:const o=e;return{type:"menuitem",...t(o),onAction:v(o.onAction)}}},xB=(e,t)=>{if(0===t.length)return e;const o=ne(e).filter((e=>!bB(e))).fold((()=>[]),(e=>[vB]));return e.concat(o).concat(t).concat([vB])},wB=(e,t)=>!(e=>"longpress"===e.type||ve(e,"touches"))(t)&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),SB=(e,t)=>wB(e,t)?e.selection.getStart(!0):t.target,kB=(e,t,o)=>{const n=Do().deviceType.isTouch,s=ri(Ph.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:kl([Jp("dismissContextMenu",[Ur(Cr(),((t,o)=>{Xd.close(t),e.focus()}))])])})),a=()=>Ph.hide(s),i=t=>{if(rB(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!rB(e))(e,t)||(e=>0===iB(e).length)(e))return;const a=((e,t)=>{const o=aB(e),n=wB(e,t)?"selection":"point";if(De(o)){const s=SB(e,t);return jw(Ve(s),o)?"node":n}return n})(e,t);(n()?fB:gB)(e,t,(()=>{const o=SB(e,t),n=e.ui.registry.getAll(),s=iB(e);return((e,t,o)=>{const n=j(t,((t,n)=>be(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(r(n))return xB(t,n.split(" "));if(n.length>0){const e=H(n,yB);return xB(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&bB(n[n.length-1])&&n.pop(),n})(n.contextMenus,s,o)}),o,s,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},CB=As([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),OB=e=>t=>t.translate(-e.left,-e.top),_B=e=>t=>t.translate(e.left,e.top),TB=e=>(t,o)=>j(e,((e,t)=>t(e)),$t(t,o)),EB=(e,t,o)=>e.fold(TB([_B(o),OB(t)]),TB([OB(t)]),TB([])),AB=(e,t,o)=>e.fold(TB([_B(o)]),TB([]),TB([_B(t)])),MB=(e,t,o)=>e.fold(TB([]),TB([OB(o)]),TB([_B(t),OB(o)])),DB=(e,t,o)=>{const n=e.fold(((e,t)=>({position:A.some("absolute"),left:A.some(e+"px"),top:A.some(t+"px")})),((e,t)=>({position:A.some("absolute"),left:A.some(e-o.left+"px"),top:A.some(t-o.top+"px")})),((e,t)=>({position:A.some("fixed"),left:A.some(e+"px"),top:A.some(t+"px")})));return{right:A.none(),bottom:A.none(),...n}},BB=(e,t,o,n)=>{const s=(e,s)=>(r,a)=>{const i=e(t,o,n);return s(r.getOr(i.left),a.getOr(i.top))};return e.fold(s(MB,FB),s(AB,IB),s(EB,RB))},FB=CB.offset,IB=CB.absolute,RB=CB.fixed,NB=(e,t)=>{const o=Ot(e,t);return u(o)?NaN:parseInt(o,10)},VB=(e,t,o,n,s,r)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=NB(o,t.leftAttr),s=NB(o,t.topAttr);return isNaN(n)||isNaN(s)?A.none():A.some($t(n,s))})(e,t).fold((()=>o),(e=>RB(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?HB(e,t,a,s,r):LB(e,t,a,s,r),l=EB(a,s,r);return((e,t,o)=>{const n=e.element;kt(n,t.leftAttr,o.left+"px"),kt(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:RB(l.left,l.top),extra:A.none()})),(e=>({coord:e.output,extra:e.extra})))},zB=(e,t,o,n)=>re(e,(e=>{const s=e.sensor,r=((e,t,o,n,s,r)=>{const a=AB(e,s,r),i=AB(t,s,r);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,s,e.range.left,e.range.top,o,n);return r?A.some({output:BB(e.output,t,o,n),extra:e.extra}):A.none()})),HB=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return zB(r,o,n,s).orThunk((()=>{const e=j(r,((e,t)=>{const r=t.sensor,a=((e,t,o,n,s,r)=>{const a=AB(e,s,r),i=AB(t,s,r),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return $t(l,c)})(o,r,t.range.left,t.range.top,n,s);return e.deltas.fold((()=>({deltas:A.some(a),snap:A.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:A.some(a),snap:A.some(t)}:e))}),{deltas:A.none(),snap:A.none()});return e.snap.map((e=>({output:BB(e.output,o,n,s),extra:e.extra})))}))},LB=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return zB(r,o,n,s)};var PB=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const t=et(e.element),o=Uo(t),r=rE(s),a=((e,t,o)=>({coord:BB(e.output,e.output,t,o),extra:e.extra}))(n,o,r),i=DB(a.coord,0,r);Ft(s,i)}}});const UB="data-initial-z-index",WB=(e,t)=>{e.getSystem().addToGui(t),(e=>{st(e.element).filter(Ge).each((t=>{Nt(t,"z-index").each((e=>{kt(t,UB,e)})),Dt(t,"z-index",It(e.element,"z-index"))}))})(t)},jB=e=>{(e=>{st(e.element).filter(Ge).each((e=>{_t(e,UB).fold((()=>Ht(e,"z-index")),(t=>Dt(e,"z-index",t))),Et(e,UB)}))})(e),e.getSystem().removeFromGui(e)},GB=(e,t,o)=>e.getSystem().build(eS.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var $B=vs("snaps",[os("getSnapPoints"),Di("onSensor"),os("leftAttr"),os("topAttr"),ys("lazyViewport",en),ys("mustSnap",!1)]);const qB=[ys("useFixed",T),os("blockerClass"),ys("getTarget",w),ys("onDrag",b),ys("repositionTarget",!0),ys("onDrop",b),Os("getBounds",en),$B],XB=e=>{return(t=Nt(e,"left"),o=Nt(e,"top"),n=Nt(e,"position"),t.isSome()&&o.isSome()&&n.isSome()?A.some(((e,t,o)=>("fixed"===o?RB:FB)(parseInt(e,10),parseInt(t,10)))(t.getOrDie(),o.getOrDie(),n.getOrDie())):A.none()).getOrThunk((()=>{const t=Xt(e);return IB(t.left,t.top)}));var t,o,n},KB=(e,t)=>({bounds:e.getBounds(),height:jt(t.element),width:Zt(t.element)}),YB=(e,t,o,n,s)=>{const r=o.update(n,s),a=o.getStartData().getOrThunk((()=>KB(t,e)));r.each((o=>{((e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const r=et(e.element),a=Uo(r),i=rE(s),l=XB(s),c=((e,t,o,n,s,r,a)=>((e,t,o,n,s)=>{const r=s.bounds,a=AB(t,o,n),i=Ki(a.left,r.x,r.x+r.width-s.width),l=Ki(a.top,r.y,r.y+r.height-s.height),c=IB(i,l);return t.fold((()=>{const e=MB(c,o,n);return FB(e.left,e.top)}),x(c),(()=>{const e=EB(c,o,n);return RB(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=r.left,i=r.top,t.fold(((e,t)=>FB(e+a,t+i)),((e,t)=>IB(e+a,t+i)),((e,t)=>RB(e+a,t+i))));var t,a,i;const l=EB(e,n,s);return RB(l.left,l.top)}),(t=>{const a=VB(e,t,o,r,n,s);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,s,a))(e,t.snaps,l,a,i,n,o),d=DB(c,0,i);Ft(s,d)}t.onDrag(e,s,n)})(e,t,a,o)}))},JB=(e,t,o,n)=>{t.each(jB),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;Et(o,t.leftAttr),Et(o,t.topAttr)})(e,t)})(e,t)}));const s=o.getTarget(e.element);n.reset(),o.onDrop(e,s)},ZB=e=>(t,o)=>{const n=e=>{o.setStartData(KB(t,e))};return Hr([Ur(xr(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var QB=Object.freeze({__proto__:null,getData:e=>A.from($t(e.x,e.y)),getDelta:(e,t)=>$t(t.left-e.left,t.top-e.top)});const eF=(e,t,o)=>[Ur(Ws(),((n,s)=>{if(0!==s.event.raw.button)return;s.stop();const r=()=>JB(n,A.some(l),e,t),a=Gw(r,200),i={drop:r,delayDrop:a.schedule,forceDrop:r,move:o=>{a.cancel(),YB(n,e,t,QB,o)}},l=GB(n,e.blockerClass,(e=>Hr([Ur(Ws(),e.forceDrop),Ur($s(),e.drop),Ur(js(),((t,o)=>{e.move(o.event)})),Ur(Gs(),e.delayDrop)]))(i));o(n),WB(n,l)}))],tF=[...qB,Ri("dragger",{handlers:ZB(eF)})];var oF=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return A.some($t(t.clientX,t.clientY))})(t):A.none()},getDelta:(e,t)=>$t(t.left-e.left,t.top-e.top)});const nF=(e,t,o)=>{const n=Ql(),s=o=>{JB(o,n.get(),e,t),n.clear()};return[Ur(Hs(),((r,a)=>{a.stop();const i=()=>s(r),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{YB(r,e,t,oF,o)}},c=GB(r,e.blockerClass,(e=>Hr([Ur(Hs(),e.forceDrop),Ur(Ps(),e.drop),Ur(Us(),e.drop),Ur(Ls(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(r),WB(r,c)})),Ur(Ls(),((o,n)=>{n.stop(),YB(o,e,t,oF,n.event)})),Ur(Ps(),((e,t)=>{t.stop(),s(e)})),Ur(Us(),s)]},sF=tF,rF=[...qB,Ri("dragger",{handlers:ZB(nF)})],aF=[...qB,Ri("dragger",{handlers:ZB(((e,t,o)=>[...eF(e,t,o),...nF(e,t,o)]))})];var iF=Object.freeze({__proto__:null,mouse:sF,touch:rF,mouseOrTouch:aF}),lF=Object.freeze({__proto__:null,init:()=>{let e=A.none(),t=A.none();const o=x({});return _a({readState:o,reset:()=>{e=A.none(),t=A.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=A.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=A.some(e)}})}});const cF=Tl({branchKey:"mode",branches:iF,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:A.from(e.extra)})},state:lF,apis:PB}),dF=(e,t,o,n,s,r)=>e.fold((()=>cF.snap({sensor:IB(o-20,n-20),range:$t(s,r),output:IB(A.some(o),A.some(n)),extra:{td:t}})),(e=>{const s=o-20,r=n-20,a=e.element.dom.getBoundingClientRect();return cF.snap({sensor:IB(s,r),range:$t(40,40),output:IB(A.some(o-a.width/2),A.some(n-a.height/2)),extra:{td:t}})})),uF=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const s=n.td;((e,t)=>e.exists((e=>Ze(e,t))))(t.get(),s)||(t.set(s),o(s))},mustSnap:!0}),mF=e=>jh(Wh.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:kl([cF.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),BS.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),gF=(e,t)=>{const o=Es([]),n=Es([]),s=Es(!1),r=Ql(),a=Ql(),i=e=>{const o=Zo(e);return dF(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=Zo(e);return dF(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=uF((()=>H(o.get(),(e=>i(e)))),r,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=uF((()=>H(n.get(),(e=>l(e)))),a,(t=>{r.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=mF(c),m=mF(d),g=ri(u.asSpec()),p=ri(m.asSpec()),h=(t,o,n,s)=>{const r=n(o);cF.snapTo(t,r),((t,o,n,r)=>{const a=o.dom.getBoundingClientRect();Ht(t.element,"display");const i=nt(Ve(e.getBody())).dom.innerHeight,l=a[s]<0,c=((e,t)=>e[s]>t)(a,i);(l||c)&&Dt(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");Do().deviceType.isTouch()&&(e.on("TableSelectionChange",(e=>{s.get()||(Ad(t,g),Ad(t,p),s.set(!0)),r.set(e.start),a.set(e.finish),e.otherCells.each((t=>{o.set(t.upOrLeftCells),n.set(t.downOrRightCells),f(e.start),b(e.finish)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{r.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{s.get()&&(Bd(g),Bd(p),s.set(!1)),r.clear(),a.clear()})))},pF=(e,t,o)=>{var n;const s=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:kl([Pp.config({mode:"flow",selector:"div[role=button]"}),Rm.config({disabled:o.isDisabled}),wy(),iS.config({}),Yp.config({}),Jp("elementPathEvents",[Yr(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>Pp.focusIn(t))),e.on("NodeChange",(n=>{const r=(t=>{const o=[];let n=t.length;for(;n-- >0;){const r=t[n];if(1===r.nodeType&&"BR"!==(s=r).nodeName&&!s.getAttribute("data-mce-bogus")&&"bookmark"!==s.getAttribute("data-mce-type")){const t=px(e,r);if(t.isDefaultPrevented()||o.push({name:t.name,element:r}),t.isPropagationStopped())break}}var s;return o})(n.parents),a=r.length>0?j(r,((t,n,r)=>{const a=((t,n,s)=>Wh.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":s,"aria-level":s+1}},components:[ti(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:kl([Sy(o.isDisabled),wy()])}))(n.name,n.element,r);return 0===r?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[ti(` ${s} `)]},a])}),[]):[];Yp.set(t,a)}))}))])]),components:[]}};var hF;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}(hF||(hF={}));const fF=(e,t,o)=>{const n=Ve(e.getContainer()),s=((e,t,o,n,s)=>{const r={height:SD(n+t.top,ff(e),vf(e))};return o===hF.Both&&(r.width=SD(s+t.left,hf(e),bf(e))),r})(e,t,o,Wt(n),Jt(n));le(s,((e,t)=>{h(e)&&Dt(n,t,wD(e))})),(e=>{e.dispatch("ResizeEditor")})(e)},bF=(e,t,o,n)=>{const s=$t(20*o,20*n);return fF(e,s,t),A.some(!0)},vF=(e,t)=>({dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const o=(()=>{const o=[];return Uf(e)&&o.push(pF(e,{},t)),e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>Yp.set(e,[ti(t.translate(["{0} "+n,o[n]]))]);return Wh.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:kl([Sy(t.isDisabled),wy(),iS.config({}),Yp.config({}),pu.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),Jp("wordcount-events",[Qr((e=>{const t=pu.getValue(e),n="words"===t.mode?"characters":"words";pu.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),Yr((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=pu.getValue(t);pu.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[ur()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),Wf(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=editor_referral&utm_medium=poweredby&utm_source=tinymce&utm_content=v6",rel:"noopener",target:"_blank","aria-label":Gh.translate(["Powered by {0}","Tiny"])},innerHtml:'<svg width="50px" height="16px" viewBox="0 0 50 16" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M10.143 0c2.608.015 5.186 2.178 5.186 5.331 0 0 .077 3.812-.084 4.87-.361 2.41-2.164 4.074-4.65 4.496-1.453.284-2.523.49-3.212.623-.373.071-.634.122-.785.152-.184.038-.997.145-1.35.145-2.732 0-5.21-2.04-5.248-5.33 0 0 0-3.514.03-4.442.093-2.4 1.758-4.342 4.926-4.963 0 0 3.875-.752 4.036-.782.368-.07.775-.1 1.15-.1Zm1.826 2.8L5.83 3.989v2.393l-2.455.475v5.968l6.137-1.189V9.243l2.456-.476V2.8ZM5.83 6.382l3.682-.713v3.574l-3.682.713V6.382Zm27.173-1.64-.084-1.066h-2.226v9.132h2.456V7.743c-.008-1.151.998-2.064 2.149-2.072 1.15-.008 1.987.92 1.995 2.072v5.065h2.455V7.359c-.015-2.18-1.657-3.929-3.837-3.913a3.993 3.993 0 0 0-2.908 1.296Zm-6.3-4.266L29.16 0v2.387l-2.456.475V.476Zm0 3.2v9.132h2.456V3.676h-2.456Zm18.179 11.787L49.11 3.676H46.58l-1.612 4.527-.46 1.382-.384-1.382-1.611-4.527H39.98l3.3 9.132L42.15 16l2.732-.537ZM22.867 9.738c0 .752.568 1.075.921 1.075.353 0 .668-.047.998-.154l.537 1.765c-.23.154-.92.537-2.225.537-1.305 0-2.655-.997-2.686-2.686a136.877 136.877 0 0 1 0-4.374H18.8V3.676h1.612v-1.98l2.455-.476v2.456h2.302V5.9h-2.302v3.837Z"/>\n</svg>\n'.trim()},behaviours:kl([oh.config({})])}]}),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:o}]:[]})(),n=((e,t)=>{const o=(e=>{const t=jf(e);return!1===t?hF.None:"both"===t?hF.Both:hF.Vertical})(e);if(o===hF.None)return A.none();const n=o===hF.Both?"Press the arrow keys to resize the editor.":"Press the Up and Down arrow keys to resize the editor.";return A.some(ef("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize"),"aria-label":t.translate(n)},behaviours:[cF.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,s)=>fF(e,s,o),blockerClass:"tox-blocker"}),Pp.config({mode:"special",onLeft:()=>bF(e,o,-1,0),onRight:()=>bF(e,o,1,0),onUp:()=>bF(e,o,0,-1),onDown:()=>bF(e,o,0,1)}),iS.config({}),oh.config({})]},t.icons))})(e,t);return o.concat(n.toArray())})()}),yF=(e,t)=>t.get().getOrDie(`UI for ${e} has not been rendered`),xF=(e,t)=>{const o=e.inline,n=o?ED:yD,s=ab(e)?LE:nE,r=(()=>{const e=Ql(),t=Ql(),o=Ql();return{dialogUi:e,popupUi:t,mainUi:o,getUiMotherships:()=>{const o=e.get().map((e=>e.mothership)),n=t.get().map((e=>e.mothership));return o.fold((()=>n.toArray()),(e=>n.fold((()=>[e]),(t=>Ze(e.element,t.element)?[e]:[e,t]))))},lazyGetInOuterOrDie:(e,t)=>()=>o.get().bind((e=>t(e.outerContainer))).getOrDie(`Could not find ${e} element in OuterContainer`)}})(),a=Ql(),i=Ql(),l=Ql(),c=Do().deviceType.isTouch()?["tox-platform-touch"]:[],d=tb(e),u=Cf(e),m=jh({dom:{tag:"div",classes:["tox-anchorbar"]}}),g=()=>r.mainUi.get().map((e=>e.outerContainer)).bind(vM.getHeader),p=r.lazyGetInOuterOrDie("anchor bar",m.getOpt),h=r.lazyGetInOuterOrDie("toolbar",vM.getToolbar),f=r.lazyGetInOuterOrDie("throbber",vM.getThrobber),b=((e,t,o)=>{const n=Es(!1),s=(e=>{const t=Es(tb(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),r={icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:Gh.translate,isDisabled:()=>t.mode.isReadOnly()||!t.ui.isEnabled(),getOption:t.options.get},a=WT(t),i=(e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},n=Es([]),s=Es([]),r=Es(!1);return e.on("PreInit",(s=>{const r=fT(e),a=vT(e,r,t,o);n.set(a)})),e.on("addStyleModifications",(n=>{const a=vT(e,n.items,t,o);s.set(a),r.set(n.replace)})),{getData:()=>{const e=r.get()?[]:n.get(),t=s.get();return e.concat(t)}}})(t),l=(e=>({colorPicker:iT(e),hasCustomColors:lT(e),getColors:cT(e),getColorCols:dT(e)}))(t),c=(e=>({isDraggableModal:uT(e)}))(t),d={shared:{providers:r,anchors:aT(t,o,s.isPositionedAtTop),header:s},urlinput:a,styles:i,colorinput:l,dialog:c,isContextMenuOpen:()=>n.get(),setContextMenuState:e=>n.set(e)},u={...d,shared:{...d.shared,interpreter:e=>R_(e,{},u),getSink:e.popup}},m={...d,shared:{...d.shared,interpreter:e=>R_(e,{},m),getSink:e.dialog}};return{popup:u,dialog:m}})({popup:()=>sn.fromOption(r.popupUi.get().map((e=>e.sink)),"(popup) UI has not been rendered"),dialog:()=>sn.fromOption(r.dialogUi.get().map((e=>e.sink)),"UI has not been rendered")},e,p),v=()=>{const t=(()=>{const t={attributes:{[yc]:d?vc.BottomToTop:vc.TopToBottom}},o=vM.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:b.popup,onEscape:()=>{e.focus()}}),n=vM.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:b.popup.shared.getSink,providers:b.popup.shared.providers,onEscape:()=>{e.focus()},onToolbarToggled:t=>{((e,t)=>{e.dispatch("ToggleToolbarDrawer",{state:t})})(e,t)},type:u,lazyToolbar:h,lazyHeader:()=>g().getOrDie("Could not find header element"),...t}),s=vM.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:b.popup.shared.providers,onEscape:()=>{e.focus()},type:u}),r=eb(e),a=Zf(e),i=Kf(e),l=qf(e),c=vM.parts.promotion({dom:{tag:"div",classes:["tox-promotion"]}}),p=r||a||i,f=l?[c,o]:[o];return vM.parts.header({dom:{tag:"div",classes:["tox-editor-header"].concat(p?[]:["tox-editor-header--empty"]),...t},components:q([i?f:[],r?[s]:a?[n]:[],nb(e)?[]:[m.asSpec()]]),sticky:ab(e),editor:e,sharedBackstage:b.popup.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[vM.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),vM.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},s=vM.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:b.popup}),r=vM.parts.viewWrapper({backstage:b.popup}),i=Pf(e)&&!o?A.some(vF(e,b.popup.shared.providers)):A.none(),l=q([d?[]:[t],o?[]:[n],d?[t]:[]]),p=vM.parts.editorContainer({components:q([l,o?[]:i.toArray()])}),f=rb(e),v={role:"application",...Gh.isRtl()?{dir:"rtl"}:{},...f?{"aria-hidden":"true"}:{}},y=ri(vM.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(o?["tox-tinymce-inline"]:[]).concat(d?["tox-tinymce--toolbar-bottom"]:[]).concat(c),styles:{visibility:"hidden",...f?{opacity:"0",border:"0"}:{}},attributes:v},components:[p,...o?[]:[r],s],behaviours:kl([wy(),Rm.config({disableClass:"tox-tinymce--disabled"}),Pp.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),x=tS(y);return a.set(x),{mothership:x,outerContainer:y}},y=t=>{const o=wD((e=>{const t=(e=>{const t=gf(e),o=ff(e),n=vf(e);return xD(t).map((e=>SD(e,o,n)))})(e);return t.getOr(gf(e))})(e)),n=wD((e=>kD(e).getOr(pf(e)))(e));return e.inline||(zt("div","width",n)&&Dt(t.element,"width",n),zt("div","height",o)?Dt(t.element,"height",o):Dt(t.element,"height","400px")),o};return{popups:{backstage:b.popup,getMothership:()=>yF("popups",l)},dialogs:{backstage:b.dialog,getMothership:()=>yF("dialogs",i)},renderUI:()=>{const o=v(),a=(()=>{const t=sb(e),o=Ze(xt(),t)&&"grid"===It(t,"display"),n={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(c),attributes:{...Gh.isRtl()?{dir:"rtl"}:{}}},behaviours:kl([Sd.config({useFixed:()=>s.isDocked(g)})])},r={dom:{styles:{width:document.body.clientWidth+"px"}},events:Hr([Ur(wr(),(e=>{Dt(e.element,"width",document.body.clientWidth+"px")}))])},a=ri(fn(n,o?r:{})),l=tS(a);return i.set(l),{sink:a,mothership:l}})(),d=ib(e)?(()=>{const e={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-silver-popup-sink","tox-tinymce-aux"].concat(c),attributes:{...Gh.isRtl()?{dir:"rtl"}:{}}},behaviours:kl([Sd.config({useFixed:()=>s.isDocked(g),getBounds:()=>t.getPopupSinkBounds()})])},o=ri(e),n=tS(o);return l.set(n),{sink:o,mothership:n}})():(e=>(l.set(e.mothership),e))(a);r.dialogUi.set(a),r.popupUi.set(d),r.mainUi.set(o);return(t=>{const{mainUi:o,popupUi:r,uiMotherships:a}=t;ce(Of(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:i,menuItems:l,contextToolbars:c,sidebars:d,views:m}=e.ui.registry.getAll(),p=Qf(e),h={menuItems:l,menus:lb(e),menubar:Df(e),toolbar:p.getOrThunk((()=>Bf(e))),allowToolbarGroups:u===nf.floating,buttons:i,sidebar:d,views:m};var v;v=o.outerContainer,e.addShortcut("alt+F9","focus menubar",(()=>{vM.focusMenubar(v)})),e.addShortcut("alt+F10","focus toolbar",(()=>{vM.focusToolbar(v)})),e.addCommand("ToggleToolbarDrawer",((e,t)=>{(null==t?void 0:t.skipFocus)?vM.toggleToolbarDrawerWithoutFocusing(v):vM.toggleToolbarDrawer(v)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>vM.isToolbarDrawerToggled(v))),((e,t,o)=>{const n=(e,n)=>{L([t,...o],(t=>{t.broadcastEvent(e,n)}))},s=(e,n)=>{L([t,...o],(t=>{t.broadcastOn([e],n)}))},r=e=>s(Kd(),{target:e.target}),a=$o(),i=tc(a,"touchstart",r),l=tc(a,"touchmove",(e=>n(vr(),e))),c=tc(a,"touchend",(e=>n(yr(),e))),d=tc(a,"mousedown",r),u=tc(a,"mouseup",(e=>{0===e.raw.button&&s(Jd(),{target:e.target})})),m=e=>s(Kd(),{target:Ve(e.target)}),g=e=>{0===e.button&&s(Jd(),{target:Ve(e.target)})},p=()=>{L(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},h=e=>n(xr(),nc(e)),f=e=>{s(Yd(),{}),n(wr(),nc(e))},b=ht(Ve(e.getElement())),v=oc(b,"scroll",(o=>{requestAnimationFrame((()=>{if(null!=e.getContainer()){const s=Uw(e,t.element).map((e=>[e.element,...e.others])).getOr([]);N(s,(e=>Ze(e,o.target)))&&(e.dispatch("ElementScroll",{target:o.target.dom}),n(Er(),o))}}))})),y=()=>s(Yd(),{}),x=t=>{t.state&&s(Kd(),{target:Ve(e.getContainer())})},w=e=>{s(Kd(),{target:Ve(e.relatedTarget.getContainer())})};e.on("PostRender",(()=>{e.on("click",m),e.on("tap",m),e.on("mouseup",g),e.on("mousedown",p),e.on("ScrollWindow",h),e.on("ResizeWindow",f),e.on("ResizeEditor",y),e.on("AfterProgressState",x),e.on("DismissPopups",w)})),e.on("remove",(()=>{e.off("click",m),e.off("tap",m),e.off("mouseup",g),e.off("mousedown",p),e.off("ScrollWindow",h),e.off("ResizeWindow",f),e.off("ResizeEditor",y),e.off("AfterProgressState",x),e.off("DismissPopups",w),d.unbind(),i.unbind(),l.unbind(),c.unbind(),u.unbind(),v.unbind()})),e.on("detach",(()=>{L([t,...o],Vd),L([t,...o],(e=>e.destroy()))}))})(e,o.mothership,a),s.setup(e,b.popup.shared,g),oB(e,b.popup),kB(e,b.popup.shared.getSink,b.popup),(e=>{const{sidebars:t}=e.ui.registry.getAll();L(ae(t),(o=>{const n=t[o],s=()=>xe(A.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(s())},onSetup:t=>{t.setActive(s());const o=()=>t.setActive(s());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}}})}))})(e),mA(e,f,b.popup.shared),qD(e,c,r.sink,{backstage:b.popup}),gF(e,r.sink);const x={targetNode:e.getElement(),height:y(o.outerContainer)};return n.render(e,t,h,b.popup,x)})({popupUi:d,dialogUi:a,mainUi:o,uiMotherships:r.getUiMotherships()})}}},wF=x([os("lazySink"),us("dragBlockClass"),Os("getBounds",en),ys("useTabstopAt",E),ys("firstTabstop",0),ys("eventOrder",{}),hu("modalBehaviours",[Pp]),Bi("onExecute"),Ii("onEscape")]),SF={sketch:w},kF=x([ju({name:"draghandle",overrides:(e,t)=>({behaviours:kl([cF.config({mode:"mouse",getTarget:e=>mi(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),Uu({schema:[os("dom")],name:"title"}),Uu({factory:SF,schema:[os("dom")],name:"close"}),Uu({factory:SF,schema:[os("dom")],name:"body"}),ju({factory:SF,schema:[os("dom")],name:"footer"}),Wu({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[ys("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),ys("components",[])],name:"blocker"})]),CF=bm({name:"ModalDialog",configFields:wF(),partFields:kF(),factory:(e,t,o,n)=>{const s=Ql(),r=la("modal-events"),a={...e.eventOrder,[Sr()]:[r].concat(e.eventOrder["alloy.system.attached"]||[])};return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{s.set(t);const o=e.lazySink(t).getOrDie(),r=n.blocker(),a=o.getSystem().build({...r,components:r.components.concat([ai(t)]),behaviours:kl([oh.config({}),Jp("dialog-blocker-events",[Kr(Xs(),(()=>{Pp.focusIn(t)}))])])});Ad(o,a),Pp.focusIn(t)},hide:e=>{s.clear(),st(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{Bd(e)}))}))},getBody:t=>nm(t,e,"body"),getFooter:t=>nm(t,e,"footer"),setIdle:e=>{cA.unblock(e)},setBusy:(e,t)=>{cA.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:bu(e.modalBehaviours,[Yp.config({}),Pp.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt,firstTabstop:e.firstTabstop}),cA.config({getRoot:s.get}),Jp(r,[Yr((t=>{((e,t)=>{const o=_t(e,"id").fold((()=>{const e=la("dialog-label");return kt(t,"id",e),e}),w);kt(e,"aria-labelledby",o)})(t.element,nm(t,e,"title").element)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),OF=Dn([ev,tv].concat(Yv)),_F=Ln,TF=[Tv("button"),pv,ks("align","end",["start","end"]),Sv,wv,hs("buttonType",["primary","secondary"])],EF=[...TF,nv],AF=[as("type",["submit","cancel","custom"]),...EF],MF=[as("type",["menu"]),gv,hv,pv,ds("items",OF),...TF],DF=[...TF,as("type",["togglebutton"]),rs("tooltip"),pv,gv,Cs("active",!1)],BF=Jn("type",{submit:AF,cancel:AF,custom:AF,menu:MF,togglebutton:DF}),FF=[ev,nv,as("level",["info","warn","error","success"]),rv,ys("url","")],IF=Dn(FF),RF=[ev,nv,wv,Tv("button"),pv,xv,hs("buttonType",["primary","secondary","toolbar"]),Sv],NF=Dn(RF),VF=[ev,tv],zF=VF.concat([fv]),HF=VF.concat([ov,wv]),LF=Dn(HF),PF=Ln,UF=zF.concat([kv("auto")]),WF=Dn(UF),jF=Rn([av,nv,rv]),GF=zF.concat([Ss("storageKey","default")]),$F=Dn(GF),qF=Hn,XF=Dn(zF),KF=Hn,YF=VF.concat([Ss("tag","textarea"),rs("scriptId"),rs("scriptUrl"),xs("settings",void 0,Wn)]),JF=VF.concat([Ss("tag","textarea"),is("init")]),ZF=Gn((e=>qn("customeditor.old",Mn(JF),e).orThunk((()=>qn("customeditor.new",Mn(YF),e))))),QF=Hn,eI=Dn(zF),tI=Bn(On),oI=e=>[ev,ss("columns"),e],nI=[ev,rs("html"),ks("presets","presentation",["presentation","document"])],sI=Dn(nI),rI=zF.concat([Cs("sandboxed",!0),Cs("transparent",!0)]),aI=Dn(rI),iI=Hn,lI=Dn(VF.concat([ps("height")])),cI=Dn([rs("url"),gs("zoom"),gs("cachedWidth"),gs("cachedHeight")]),dI=zF.concat([ps("inputMode"),ps("placeholder"),Cs("maximized",!1),wv]),uI=Dn(dI),mI=Hn,gI=e=>[ev,ov,e],pI=[nv,av],hI=[nv,ds("items",Zn(0,(()=>fI)))],fI=Fn([Dn(pI),Dn(hI)]),bI=zF.concat([ds("items",fI),wv]),vI=Dn(bI),yI=Hn,xI=zF.concat([cs("items",[nv,av]),ws("size",1),wv]),wI=Dn(xI),SI=Hn,kI=zF.concat([Cs("constrain",!0),wv]),CI=Dn(kI),OI=Dn([rs("width"),rs("height")]),_I=VF.concat([ov,ws("min",0),ws("max",0)]),TI=Dn(_I),EI=zn,AI=[ev,ds("header",Hn),ds("cells",Bn(Hn))],MI=Dn(AI),DI=zF.concat([ps("placeholder"),Cs("maximized",!1),wv]),BI=Dn(DI),FI=Hn,II=[as("type",["directory","leaf"]),sv,rs("id"),ms("menu",WE)],RI=Dn(II),NI=II.concat([ds("children",Zn(0,(()=>jn("type",{directory:VI,leaf:RI}))))]),VI=Dn(NI),zI=jn("type",{directory:VI,leaf:RI}),HI=[ev,ds("items",zI),fs("onLeafAction"),fs("onToggleExpand"),_s("defaultExpandedIds",[],Hn),ps("defaultSelectedId")],LI=Dn(HI),PI=zF.concat([ks("filetype","file",["image","media","file"]),wv]),UI=Dn(PI),WI=Dn([av,Cv]),jI=e=>Qn("items","items",{tag:"required",process:{}},Bn(Gn((t=>qn(`Checking item of ${e}`,GI,t).fold((e=>sn.error(Yn(e))),(e=>sn.value(e))))))),GI=En((()=>{return jn("type",{alertbanner:IF,bar:Dn((e=jI("bar"),[ev,e])),button:NF,checkbox:LF,colorinput:$F,colorpicker:XF,dropzone:eI,grid:Dn(oI(jI("grid"))),iframe:aI,input:uI,listbox:vI,selectbox:wI,sizeinput:CI,slider:TI,textarea:BI,urlinput:UI,customeditor:ZF,htmlpanel:sI,imagepreview:lI,collection:WF,label:Dn(gI(jI("label"))),table:MI,tree:LI,panel:qI});var e})),$I=[ev,ys("classes",[]),ds("items",GI)],qI=Dn($I),XI=[Tv("tab"),sv,ds("items",GI)],KI=[ev,cs("tabs",XI)],YI=Dn(KI),JI=EF,ZI=BF,QI=Dn([rs("title"),ns("body",jn("type",{panel:qI,tabpanel:YI})),Ss("size","normal"),ds("buttons",ZI),ys("initialData",{}),Os("onAction",b),Os("onChange",b),Os("onSubmit",b),Os("onClose",b),Os("onCancel",b),Os("onTabChange",b)]),eR=Dn([as("type",["cancel","custom"]),...JI]),tR=Dn([rs("title"),rs("url"),gs("height"),gs("width"),bs("buttons",eR),Os("onAction",b),Os("onCancel",b),Os("onClose",b),Os("onMessage",b)]),oR=e=>a(e)?[e].concat(X(fe(e),oR)):l(e)?X(e,oR):[],nR=e=>r(e.type)&&r(e.name),sR={checkbox:PF,colorinput:qF,colorpicker:KF,dropzone:tI,input:mI,iframe:iI,imagepreview:cI,selectbox:SI,sizeinput:OI,slider:EI,listbox:yI,size:OI,textarea:FI,urlinput:WI,customeditor:QF,collection:jF,togglemenuitem:_F},rR=e=>{const t=(e=>U(oR(e),nR))(e),o=X(t,(e=>(e=>A.from(sR[e.type]))(e).fold((()=>[]),(t=>[ns(e.name,t)]))));return Dn(o)},aR=e=>{var t;return{internalDialog:Xn(qn("dialog",QI,e)),dataValidator:rR(e),initialData:null!==(t=e.initialData)&&void 0!==t?t:{}}},iR={open:(e,t)=>{const o=aR(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(Xn(qn("dialog",tR,t))),redial:e=>aR(e)};var lR=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const s=e(o,n);t.set(s)})),e.renderComponents.each((s=>{const r=s(n,t.get());(e.reuseDom?Wp:Up)(o,r)}))};return Hr([Ur(dr(),((t,n)=>{const s=n;if(!s.universal){const n=e.channel;R(s.channels,n)&&o(t,s.data)}})),Yr(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),cR=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),dR=[os("channel"),us("renderComponents"),us("updateState"),us("initialData"),Cs("reuseDom",!0)];const uR=Ol({fields:dR,name:"reflecting",active:lR,apis:cR,state:Object.freeze({__proto__:null,init:()=>{const e=Es(A.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(A.none())}}})}),mR=e=>{const t=[],o={};return le(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?sn.error(t):sn.value(o)},gR=(e,t,o)=>{const n=jh(SC.sketch((n=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:H(e.items,(e=>F_(n,e,t,o)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[n.asSpec()]}],behaviours:kl([Pp.config({mode:"acyclic",useTabstopAt:C(qC)}),(s=n,wm.config({find:s.getOpt})),IC(n,{postprocess:e=>mR(e).fold((e=>(console.error(e),{})),w)})])};var s},pR=fm({name:"TabButton",configFields:[ys("uid",void 0),os("value"),Qn("dom","dom",xn((()=>({attributes:{role:"tab",id:la("aria"),"aria-selected":"false"}}))),Nn()),us("action"),ys("domModification",{}),hu("tabButtonBehaviours",[oh,Pp,pu]),os("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:mh(e.action),behaviours:bu(e.tabButtonBehaviours,[oh.config({}),Pp.config({mode:"execution",useSpace:!0,useEnter:!0}),pu.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),hR=x([os("tabs"),os("dom"),ys("clickToDismiss",!1),hu("tabbarBehaviours",[Gm,Pp]),Ai(["tabClass","selectedClass"])]),fR=Gu({factory:pR,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Gm.dehighlight(e,t),Ir(e,Mr(),{tabbar:e,button:t})},o=(e,t)=>{Gm.highlight(e,t),Ir(e,Ar(),{tabbar:e,button:t})};return{action:n=>{const s=n.getSystem().getByUid(e.uid).getOrDie(),r=Gm.isHighlighted(s,n);(r&&e.clickToDismiss?t:r?b:o)(s,n)},domModification:{classes:[e.markers.tabClass]}}}}),bR=x([fR]),vR=bm({name:"Tabbar",configFields:hR(),partFields:bR(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:bu(e.tabbarBehaviours,[Gm.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{kt(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{kt(t.element,"aria-selected","false")}}),Pp.config({mode:"flow",getInitial:e=>Gm.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),yR=fm({name:"Tabview",configFields:[hu("tabviewBehaviours",[Yp])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:bu(e.tabviewBehaviours,[Yp.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),xR=x([ys("selectFirst",!0),Di("onChangeTab"),Di("onDismissTab"),ys("tabs",[]),hu("tabSectionBehaviours",[])]),wR=Uu({factory:vR,schema:[os("dom"),ls("markers",[os("tabClass"),os("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),SR=Uu({factory:yR,name:"tabview"}),kR=x([wR,SR]),CR=bm({name:"TabSection",configFields:xR(),partFields:kR(),factory:(e,t,o,n)=>{const s=(t,o)=>{om(t,e,"tabbar").each((e=>{o(e).each(Rr)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:fu(e.tabSectionBehaviours),events:Hr(q([e.selectFirst?[Yr(((e,t)=>{s(e,Gm.getFirst)}))]:[],[Ur(Ar(),((t,o)=>{(t=>{const o=pu.getValue(t);om(t,e,"tabview").each((n=>{G(e.tabs,(e=>e.value===o)).each((o=>{const s=o.view();_t(t.element,"id").each((e=>{kt(n.element,"aria-labelledby",e)})),Yp.set(n,s),e.onChangeTab(n,t,s)}))}))})(o.event.button)})),Ur(Mr(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>om(t,e,"tabview").map((e=>Yp.contents(e))).getOr([]),showTab:(e,t)=>{s(e,(e=>{const o=Gm.getCandidates(e);return G(o,(e=>pu.getValue(e)===t)).filter((t=>!Gm.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),OR=(e,t)=>{Dt(e,"height",t+"px"),Dt(e,"flex-basis",t+"px")},_R=(e,t,o)=>{mi(e,'[role="dialog"]').each((e=>{pi(e,'[role="tablist"]').each((n=>{o.get().map((o=>(Dt(t,"height","0"),Dt(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=ot(e).dom,s=mi(e,".tox-dialog-wrap").getOr(e);let r;r="fixed"===It(s,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=Wt(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+Jt(o)?Math.max(Wt(o),a):a,l=parseInt(It(e,"margin-top"),10)||0,c=parseInt(It(e,"margin-bottom"),10)||0;return r-(Wt(e)+l+c-i)})(e,t,n))))).each((e=>{OR(t,e)}))}))}))},TR=e=>pi(e,'[role="tabpanel"]'),ER="send-data-to-section",AR="send-data-to-view",MR=(e,t,o)=>{const n=Es({}),s=e=>{const t=pu.getValue(e),o=mR(t).getOr({}),s=n.get(),r=fn(s,o);n.set(r)},r=e=>{const t=n.get();pu.setValue(e,t)},a=Es(null),i=H(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[ti(o.shared.providers.translate(e.title))],view:()=>[SC.sketch((n=>({dom:{tag:"div",classes:["tox-form"]},components:H(e.items,(e=>F_(n,e,t,o))),formBehaviours:kl([Pp.config({mode:"acyclic",useTabstopAt:C(qC)}),Jp("TabView.form.events",[Yr(r),Jr(s)]),Al.config({channels:Ds([{key:ER,value:{onReceive:s}},{key:AR,value:{onReceive:r}}])})])})))]}))),l=(e=>{const t=Ql(),o=[Yr((o=>{const n=o.element;TR(n).each((s=>{Dt(s,"visibility","hidden"),o.getSystem().getByDom(s).toOptional().each((o=>{const n=((e,t,o)=>H(e,((n,s)=>{Yp.set(o,e[s].view());const r=t.dom.getBoundingClientRect();return Yp.set(o,[]),r.height})))(e,s,o),r=(e=>oe(ee(e,((e,t)=>e>t?-1:e<t?1:0))))(n);r.fold(t.clear,t.set)})),_R(n,s,t),Ht(s,"visibility"),((e,t)=>{oe(e).each((e=>CR.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{_R(n,s,t)}))}))})),Ur(wr(),(e=>{const o=e.element;TR(o).each((e=>{_R(o,e,t)}))})),Ur(wS,((e,o)=>{const n=e.element;TR(n).each((e=>{const o=Il(ht(e));Dt(e,"visibility","hidden");const s=Nt(e,"height").map((e=>parseInt(e,10)));Ht(e,"height"),Ht(e,"flex-basis");const r=e.dom.getBoundingClientRect().height;s.forall((e=>r>e))?(t.set(r),_R(n,e,t)):s.each((t=>{OR(e,t)})),Ht(e,"visibility"),o.each(Dl)}))}))];return{extraEvents:o,selectFirst:!1}})(i);return CR.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=pu.getValue(t);Ir(e,xS,{name:n,oldName:a.get()}),a.set(n)},tabs:i,components:[CR.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[vR.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:kl([iS.config({})])}),CR.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:l.selectFirst,tabSectionBehaviours:kl([Jp("tabpanel",l.extraEvents),Pp.config({mode:"acyclic"}),wm.config({find:e=>oe(CR.getViewItems(e))}),NC(A.none(),(e=>(e.getSystem().broadcastOn([ER],{}),n.get())),((e,t)=>{n.set(t),e.getSystem().broadcastOn([AR],{})}))])})},DR=la("update-dialog"),BR=la("update-title"),FR=la("update-body"),IR=la("update-footer"),RR=la("body-send-message"),NR=(e,t,o,n,s)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...s?{"aria-live":"polite"}:{}}},components:[],behaviours:kl([MC(0),uR.config({channel:`${FR}-${t}`,updateState:(e,t)=>A.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[MR(t,e.initialData,n)]:[gR(t,e.initialData,n)]},initialData:e})])});function VR(e){return VR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},VR(e)}function zR(e,t){return zR=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},zR(e,t)}function HR(e,t,o){return HR=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}()?Reflect.construct:function(e,t,o){var n=[null];n.push.apply(n,t);var s=new(Function.bind.apply(e,n));return o&&zR(s,o.prototype),s},HR.apply(null,arguments)}function LR(e){return function(e){if(Array.isArray(e))return PR(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return PR(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?PR(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function PR(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,n=new Array(t);o<t;o++)n[o]=e[o];return n}var UR=Object.hasOwnProperty,WR=Object.setPrototypeOf,jR=Object.isFrozen,GR=Object.getPrototypeOf,$R=Object.getOwnPropertyDescriptor,qR=Object.freeze,XR=Object.seal,KR=Object.create,YR="undefined"!=typeof Reflect&&Reflect,JR=YR.apply,ZR=YR.construct;JR||(JR=function(e,t,o){return e.apply(t,o)}),qR||(qR=function(e){return e}),XR||(XR=function(e){return e}),ZR||(ZR=function(e,t){return HR(e,LR(t))});var QR,eN=dN(Array.prototype.forEach),tN=dN(Array.prototype.pop),oN=dN(Array.prototype.push),nN=dN(String.prototype.toLowerCase),sN=dN(String.prototype.match),rN=dN(String.prototype.replace),aN=dN(String.prototype.indexOf),iN=dN(String.prototype.trim),lN=dN(RegExp.prototype.test),cN=(QR=TypeError,function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return ZR(QR,t)});function dN(e){return function(t){for(var o=arguments.length,n=new Array(o>1?o-1:0),s=1;s<o;s++)n[s-1]=arguments[s];return JR(e,t,n)}}function uN(e,t){WR&&WR(e,null);for(var o=t.length;o--;){var n=t[o];if("string"==typeof n){var s=nN(n);s!==n&&(jR(t)||(t[o]=s),n=s)}e[n]=!0}return e}function mN(e){var t,o=KR(null);for(t in e)JR(UR,e,[t])&&(o[t]=e[t]);return o}function gN(e,t){for(;null!==e;){var o=$R(e,t);if(o){if(o.get)return dN(o.get);if("function"==typeof o.value)return dN(o.value)}e=GR(e)}return function(e){return console.warn("fallback value for",e),null}}var pN=qR(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),hN=qR(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),fN=qR(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),bN=qR(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),vN=qR(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),yN=qR(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),xN=qR(["#text"]),wN=qR(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),SN=qR(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),kN=qR(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),CN=qR(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),ON=XR(/\{\{[\w\W]*|[\w\W]*\}\}/gm),_N=XR(/<%[\w\W]*|[\w\W]*%>/gm),TN=XR(/^data-[\-\w.\u00B7-\uFFFF]/),EN=XR(/^aria-[\-\w]+$/),AN=XR(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),MN=XR(/^(?:\w+script|data):/i),DN=XR(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),BN=XR(/^html$/i),FN=function(){return"undefined"==typeof window?null:window},IN=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:FN(),o=function(t){return e(t)};if(o.version="2.3.8",o.removed=[],!t||!t.document||9!==t.document.nodeType)return o.isSupported=!1,o;var n=t.document,s=t.document,r=t.DocumentFragment,a=t.HTMLTemplateElement,i=t.Node,l=t.Element,c=t.NodeFilter,d=t.NamedNodeMap,u=void 0===d?t.NamedNodeMap||t.MozNamedAttrMap:d,m=t.HTMLFormElement,g=t.DOMParser,p=t.trustedTypes,h=l.prototype,f=gN(h,"cloneNode"),b=gN(h,"nextSibling"),v=gN(h,"childNodes"),y=gN(h,"parentNode");if("function"==typeof a){var x=s.createElement("template");x.content&&x.content.ownerDocument&&(s=x.content.ownerDocument)}var w=function(e,t){if("object"!==VR(e)||"function"!=typeof e.createPolicy)return null;var o=null,n="data-tt-policy-suffix";t.currentScript&&t.currentScript.hasAttribute(n)&&(o=t.currentScript.getAttribute(n));var s="dompurify"+(o?"#"+o:"");try{return e.createPolicy(s,{createHTML:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+s+" could not be created."),null}}(p,n),S=w?w.createHTML(""):"",k=s,C=k.implementation,O=k.createNodeIterator,_=k.createDocumentFragment,T=k.getElementsByTagName,E=n.importNode,A={};try{A=mN(s).documentMode?s.documentMode:{}}catch(e){}var M={};o.isSupported="function"==typeof y&&C&&void 0!==C.createHTMLDocument&&9!==A;var D,B,F=ON,I=_N,R=TN,N=EN,V=MN,z=DN,H=AN,L=null,P=uN({},[].concat(LR(pN),LR(hN),LR(fN),LR(vN),LR(xN))),U=null,W=uN({},[].concat(LR(wN),LR(SN),LR(kN),LR(CN))),j=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),G=null,$=null,q=!0,X=!0,K=!1,Y=!1,J=!1,Z=!1,Q=!1,ee=!1,te=!1,oe=!1,ne=!0,se=!0,re=!1,ae={},ie=null,le=uN({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),ce=null,de=uN({},["audio","video","img","source","image","track"]),ue=null,me=uN({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ge="http://www.w3.org/1998/Math/MathML",pe="http://www.w3.org/2000/svg",he="http://www.w3.org/1999/xhtml",fe=he,be=!1,ve=["application/xhtml+xml","text/html"],ye=null,xe=s.createElement("form"),we=function(e){return e instanceof RegExp||e instanceof Function},Se=function(e){ye&&ye===e||(e&&"object"===VR(e)||(e={}),e=mN(e),L="ALLOWED_TAGS"in e?uN({},e.ALLOWED_TAGS):P,U="ALLOWED_ATTR"in e?uN({},e.ALLOWED_ATTR):W,ue="ADD_URI_SAFE_ATTR"in e?uN(mN(me),e.ADD_URI_SAFE_ATTR):me,ce="ADD_DATA_URI_TAGS"in e?uN(mN(de),e.ADD_DATA_URI_TAGS):de,ie="FORBID_CONTENTS"in e?uN({},e.FORBID_CONTENTS):le,G="FORBID_TAGS"in e?uN({},e.FORBID_TAGS):{},$="FORBID_ATTR"in e?uN({},e.FORBID_ATTR):{},ae="USE_PROFILES"in e&&e.USE_PROFILES,q=!1!==e.ALLOW_ARIA_ATTR,X=!1!==e.ALLOW_DATA_ATTR,K=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Y=e.SAFE_FOR_TEMPLATES||!1,J=e.WHOLE_DOCUMENT||!1,ee=e.RETURN_DOM||!1,te=e.RETURN_DOM_FRAGMENT||!1,oe=e.RETURN_TRUSTED_TYPE||!1,Q=e.FORCE_BODY||!1,ne=!1!==e.SANITIZE_DOM,se=!1!==e.KEEP_CONTENT,re=e.IN_PLACE||!1,H=e.ALLOWED_URI_REGEXP||H,fe=e.NAMESPACE||he,e.CUSTOM_ELEMENT_HANDLING&&we(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(j.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&we(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(j.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(j.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),D=D=-1===ve.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,B="application/xhtml+xml"===D?function(e){return e}:nN,Y&&(X=!1),te&&(ee=!0),ae&&(L=uN({},LR(xN)),U=[],!0===ae.html&&(uN(L,pN),uN(U,wN)),!0===ae.svg&&(uN(L,hN),uN(U,SN),uN(U,CN)),!0===ae.svgFilters&&(uN(L,fN),uN(U,SN),uN(U,CN)),!0===ae.mathMl&&(uN(L,vN),uN(U,kN),uN(U,CN))),e.ADD_TAGS&&(L===P&&(L=mN(L)),uN(L,e.ADD_TAGS)),e.ADD_ATTR&&(U===W&&(U=mN(U)),uN(U,e.ADD_ATTR)),e.ADD_URI_SAFE_ATTR&&uN(ue,e.ADD_URI_SAFE_ATTR),e.FORBID_CONTENTS&&(ie===le&&(ie=mN(ie)),uN(ie,e.FORBID_CONTENTS)),se&&(L["#text"]=!0),J&&uN(L,["html","head","body"]),L.table&&(uN(L,["tbody"]),delete G.tbody),qR&&qR(e),ye=e)},ke=uN({},["mi","mo","mn","ms","mtext"]),Ce=uN({},["foreignobject","desc","title","annotation-xml"]),Oe=uN({},["title","style","font","a","script"]),_e=uN({},hN);uN(_e,fN),uN(_e,bN);var Te=uN({},vN);uN(Te,yN);var Ee=function(e){oN(o.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=S}catch(t){e.remove()}}},Ae=function(e,t){try{oN(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){oN(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!U[e])if(ee||te)try{Ee(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Me=function(e){var t,o;if(Q)e="<remove></remove>"+e;else{var n=sN(e,/^[\r\n\t ]+/);o=n&&n[0]}"application/xhtml+xml"===D&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var r=w?w.createHTML(e):e;if(fe===he)try{t=(new g).parseFromString(r,D)}catch(e){}if(!t||!t.documentElement){t=C.createDocument(fe,"template",null);try{t.documentElement.innerHTML=be?"":r}catch(e){}}var a=t.body||t.documentElement;return e&&o&&a.insertBefore(s.createTextNode(o),a.childNodes[0]||null),fe===he?T.call(t,J?"html":"body")[0]:J?t.documentElement:a},De=function(e){return O.call(e.ownerDocument||e,e,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT,null,!1)},Be=function(e){return"object"===VR(i)?e instanceof i:e&&"object"===VR(e)&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName},Fe=function(e,t,n){M[e]&&eN(M[e],(function(e){e.call(o,t,n,ye)}))},Ie=function(e){var t,n;if(Fe("beforeSanitizeElements",e,null),(n=e)instanceof m&&("string"!=typeof n.nodeName||"string"!=typeof n.textContent||"function"!=typeof n.removeChild||!(n.attributes instanceof u)||"function"!=typeof n.removeAttribute||"function"!=typeof n.setAttribute||"string"!=typeof n.namespaceURI||"function"!=typeof n.insertBefore))return Ee(e),!0;if(lN(/[\u0080-\uFFFF]/,e.nodeName))return Ee(e),!0;var s=B(e.nodeName);if(Fe("uponSanitizeElement",e,{tagName:s,allowedTags:L}),e.hasChildNodes()&&!Be(e.firstElementChild)&&(!Be(e.content)||!Be(e.content.firstElementChild))&&lN(/<[/\w]/g,e.innerHTML)&&lN(/<[/\w]/g,e.textContent))return Ee(e),!0;if("select"===s&&lN(/<template/i,e.innerHTML))return Ee(e),!0;if(!L[s]||G[s]){if(!G[s]&&Ne(s)){if(j.tagNameCheck instanceof RegExp&&lN(j.tagNameCheck,s))return!1;if(j.tagNameCheck instanceof Function&&j.tagNameCheck(s))return!1}if(se&&!ie[s]){var r=y(e)||e.parentNode,a=v(e)||e.childNodes;if(a&&r)for(var i=a.length-1;i>=0;--i)r.insertBefore(f(a[i],!0),b(e))}return Ee(e),!0}return e instanceof l&&!function(e){var t=y(e);t&&t.tagName||(t={namespaceURI:he,tagName:"template"});var o=nN(e.tagName),n=nN(t.tagName);return e.namespaceURI===pe?t.namespaceURI===he?"svg"===o:t.namespaceURI===ge?"svg"===o&&("annotation-xml"===n||ke[n]):Boolean(_e[o]):e.namespaceURI===ge?t.namespaceURI===he?"math"===o:t.namespaceURI===pe?"math"===o&&Ce[n]:Boolean(Te[o]):e.namespaceURI===he&&!(t.namespaceURI===pe&&!Ce[n])&&!(t.namespaceURI===ge&&!ke[n])&&!Te[o]&&(Oe[o]||!_e[o])}(e)?(Ee(e),!0):"noscript"!==s&&"noembed"!==s||!lN(/<\/no(script|embed)/i,e.innerHTML)?(Y&&3===e.nodeType&&(t=e.textContent,t=rN(t,F," "),t=rN(t,I," "),e.textContent!==t&&(oN(o.removed,{element:e.cloneNode()}),e.textContent=t)),Fe("afterSanitizeElements",e,null),!1):(Ee(e),!0)},Re=function(e,t,o){if(ne&&("id"===t||"name"===t)&&(o in s||o in xe))return!1;if(X&&!$[t]&&lN(R,t));else if(q&&lN(N,t));else if(!U[t]||$[t]){if(!(Ne(e)&&(j.tagNameCheck instanceof RegExp&&lN(j.tagNameCheck,e)||j.tagNameCheck instanceof Function&&j.tagNameCheck(e))&&(j.attributeNameCheck instanceof RegExp&&lN(j.attributeNameCheck,t)||j.attributeNameCheck instanceof Function&&j.attributeNameCheck(t))||"is"===t&&j.allowCustomizedBuiltInElements&&(j.tagNameCheck instanceof RegExp&&lN(j.tagNameCheck,o)||j.tagNameCheck instanceof Function&&j.tagNameCheck(o))))return!1}else if(ue[t]);else if(lN(H,rN(o,z,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==aN(o,"data:")||!ce[e])if(K&&!lN(V,rN(o,z,"")));else if(o)return!1;return!0},Ne=function(e){return e.indexOf("-")>0},Ve=function(e){var t,o,n,s;Fe("beforeSanitizeAttributes",e,null);var r=e.attributes;if(r){var a={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:U};for(s=r.length;s--;){var i=t=r[s],l=i.name,c=i.namespaceURI;o="value"===l?t.value:iN(t.value),n=B(l);var d=o;if(a.attrName=n,a.attrValue=o,a.keepAttr=!0,a.forceKeepAttr=void 0,Fe("uponSanitizeAttribute",e,a),o=a.attrValue,!a.forceKeepAttr)if(a.keepAttr)if(lN(/\/>/i,o))Ae(l,e);else{Y&&(o=rN(o,F," "),o=rN(o,I," "));var u=B(e.nodeName);if(Re(u,n,o)){if(o!==d)try{c?e.setAttributeNS(c,l,o):e.setAttribute(l,o)}catch(t){Ae(l,e)}}else Ae(l,e)}else Ae(l,e)}Fe("afterSanitizeAttributes",e,null)}},ze=function e(t){var o,n=De(t);for(Fe("beforeSanitizeShadowDOM",t,null);o=n.nextNode();)Fe("uponSanitizeShadowNode",o,null),Ie(o)||(o.content instanceof r&&e(o.content),Ve(o));Fe("afterSanitizeShadowDOM",t,null)};return o.sanitize=function(e,s){var a,l,c,d,u;if((be=!e)&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Be(e)){if("function"!=typeof e.toString)throw cN("toString is not a function");if("string"!=typeof(e=e.toString()))throw cN("dirty is not a string, aborting")}if(!o.isSupported){if("object"===VR(t.toStaticHTML)||"function"==typeof t.toStaticHTML){if("string"==typeof e)return t.toStaticHTML(e);if(Be(e))return t.toStaticHTML(e.outerHTML)}return e}if(Z||Se(s),o.removed=[],"string"==typeof e&&(re=!1),re){if(e.nodeName){var m=B(e.nodeName);if(!L[m]||G[m])throw cN("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof i)1===(l=(a=Me("\x3c!----\x3e")).ownerDocument.importNode(e,!0)).nodeType&&"BODY"===l.nodeName||"HTML"===l.nodeName?a=l:a.appendChild(l);else{if(!ee&&!Y&&!J&&-1===e.indexOf("<"))return w&&oe?w.createHTML(e):e;if(!(a=Me(e)))return ee?null:oe?S:""}a&&Q&&Ee(a.firstChild);for(var g=De(re?e:a);c=g.nextNode();)3===c.nodeType&&c===d||Ie(c)||(c.content instanceof r&&ze(c.content),Ve(c),d=c);if(d=null,re)return e;if(ee){if(te)for(u=_.call(a.ownerDocument);a.firstChild;)u.appendChild(a.firstChild);else u=a;return U.shadowroot&&(u=E.call(n,u,!0)),u}var p=J?a.outerHTML:a.innerHTML;return J&&L["!doctype"]&&a.ownerDocument&&a.ownerDocument.doctype&&a.ownerDocument.doctype.name&&lN(BN,a.ownerDocument.doctype.name)&&(p="<!DOCTYPE "+a.ownerDocument.doctype.name+">\n"+p),Y&&(p=rN(p,F," "),p=rN(p,I," ")),w&&oe?w.createHTML(p):p},o.setConfig=function(e){Se(e),Z=!0},o.clearConfig=function(){ye=null,Z=!1},o.isValidAttribute=function(e,t,o){ye||Se({});var n=B(e),s=B(t);return Re(n,s,o)},o.addHook=function(e,t){"function"==typeof t&&(M[e]=M[e]||[],oN(M[e],t))},o.removeHook=function(e){if(M[e])return tN(M[e])},o.removeHooks=function(e){M[e]&&(M[e]=[])},o.removeAllHooks=function(){M={}},o}();const RN=e=>IN().sanitize(e),NN=lf.deviceType.isTouch(),VN=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),zN=(e,t)=>CF.parts.close(Wh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:kl([iS.config({})])})),HN=()=>CF.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),LN=(e,t)=>CF.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:dA(`<p>${RN(t.translate(e))}</p>`)}]}]}),PN=e=>CF.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),UN=(e,t)=>[eS.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),eS.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],WN=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",s=t+"__disable-scroll";return CF.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),A.some(!0)),useTabstopAt:e=>!qC(e),firstTabstop:e.firstTabstop,dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:dA(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:NN?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:kl([oh.config({}),Jp("dialog-events",e.dialogEvents.concat([Kr(Xs(),((e,t)=>{Pp.focusIn(e)}))])),Jp("scroll-lock",[Yr((()=>{La(xt(),s)})),Jr((()=>{Pa(xt(),s)}))]),...e.extraBehaviours]),eventOrder:{[ur()]:["dialog-events"],[Sr()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[kr()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},jN=e=>Wh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),title:e.translate("Close")}},buttonBehaviours:kl([iS.config({})]),components:[ef("close",{tag:"span",classes:["tox-icon"]},e.icons)],action:e=>{Fr(e,hS)}}),GN=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:kl([uR.config({channel:`${BR}-${t}`,initialData:e,renderComponents:e=>[ti(n.translate(e.title))]})])}),$N=()=>({dom:dA('<div class="tox-dialog__draghandle"></div>')}),qN=(e,t,o)=>((e,t,o)=>{const n=CF.parts.title(GN(e,t,A.none(),o)),s=CF.parts.draghandle($N()),r=CF.parts.close(jN(o)),a=[n].concat(e.draggable?[s]:[]).concat([r]);return eS.sketch({dom:dA('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),XN=(e,t,o)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:t,components:[{dom:dA('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),KN=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{CF.setBusy(e(),((e,n)=>XN(o.message,n,t)))},onUnblock:()=>{CF.setIdle(e())}}),YN=(e,t,o,n)=>ri(WN({...e,firstTabstop:1,lazySink:n.shared.getSink,extraBehaviours:[uR.config({channel:`${DR}-${e.id}`,updateState:(e,t)=>A.some(t),initialData:t}),VC({}),...e.extraBehaviours],onEscape:e=>{Fr(e,hS)},dialogEvents:o,eventOrder:{[dr()]:[uR.name(),Al.name()],[Sr()]:["scroll-lock",uR.name(),"messages","dialog-events","alloy.base.behaviour"],[kr()]:["alloy.base.behaviour","dialog-events","messages",uR.name(),"scroll-lock"]}})),JN=(e,t={})=>H(e,(e=>"menu"===e.type?(e=>{const o=H(e.items,(e=>{const o=be(t,e.name).getOr(Es(!1));return{...e,storage:o}}));return{...e,items:o}})(e):e)),ZN=e=>j(e,((e,t)=>"menu"===t.type?j(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),QN=(e,t)=>[$r(Xs(),$C),e(pS,((e,o,n,s)=>{Il(ht(s.element)).fold(b,Bl),t.onClose(),o.onClose()})),e(hS,((e,t,o,n)=>{t.onCancel(e),Fr(n,pS)})),Ur(yS,((e,o)=>t.onUnblock())),Ur(vS,((e,o)=>t.onBlock(o.event)))],eV=(e,t,o)=>{const n=(t,o)=>Ur(t,((t,n)=>{s(t,((s,r)=>{o(e(),s,n.event,t)}))})),s=(e,t)=>{uR.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...QN(n,t),n(bS,((e,t)=>t.onSubmit(e))),n(gS,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(fS,((e,t,n,s)=>{const r=()=>Pp.focusIn(s),a=e=>Tt(e,"disabled")||_t(e,"aria-disabled").exists((e=>"true"===e)),i=ht(s.element),l=Il(i);t.onAction(e,{name:n.name,value:n.value}),Il(i).fold(r,(e=>{a(e)||l.exists((t=>Qe(e,t)&&a(t)))?r():o().toOptional().filter((t=>!Qe(t.element,e))).each(r)}))})),n(xS,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),Jr((t=>{const o=e();pu.setValue(t,o.getData())}))]},tV=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=P(o,(e=>"start"===e.align)),s=(e,t)=>eS.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:H(t,(e=>e.memento.asSpec()))});return[s("start",n.pass),s("end",n.fail)]},oV=(e,t,o)=>({dom:dA('<div class="tox-dialog__footer"></div>'),components:[],behaviours:kl([uR.config({channel:`${IR}-${t}`,initialData:e,updateState:(e,t)=>{const n=H(t.buttons,(e=>{const t=jh(((e,t)=>b_(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return A.some({lookupByName:t=>((e,t,o)=>G(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:tV})])}),nV=(e,t,o)=>CF.parts.footer(oV(e,t,o)),sV=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=wm.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return SC.getField(o,t).orThunk((()=>{const o=e.getFooter();return uR.getState(o).get().bind((e=>e.lookupByName(t)))}))}return A.none()},rV=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},s={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...pu.getValue(n),...ce(o,(e=>e.get()))}},setData:t=>{n((n=>{const r=s.getData(),a=fn(r,t),i=((e,t)=>{const o=e.getRoot();return uR.getState(o).get().map((e=>Xn(qn("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();pu.setValue(l,i),le(o,((e,t)=>{ve(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{sV(e,t).each(o?Rm.enable:Rm.disable)},focus:t=>{sV(e,t).each(oh.focus)},block:e=>{if(!r(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{Ir(t,vS,{message:e})}))},unblock:()=>{n((e=>{Fr(e,yS)}))},showTab:t=>{n((o=>{const n=e.getBody();uR.getState(n).get().exists((e=>e.isTabPanel()))&&wm.getCurrent(n).each((e=>{CR.showTab(e,t)}))}))},redial:r=>{n((n=>{const a=e.getId(),i=t(r),l=JN(i.internalDialog.buttons,o);n.getSystem().broadcastOn([`${DR}-${a}`],i),n.getSystem().broadcastOn([`${BR}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${FR}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${IR}-${a}`],{...i.internalDialog,buttons:l}),s.setData(i.initialData)}))},close:()=>{n((e=>{Fr(e,pS)}))},toggleFullscreen:e.toggleFullscreen};return s};var aV=tinymce.util.Tools.resolve("tinymce.util.URI");const iV=["insertContent","setContent","execCommand","close","block","unblock"],lV=e=>a(e)&&-1!==iV.indexOf(e.mceAction),cV=(e,t,o,n)=>{const s=la("dialog"),i=qN(e.title,s,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[jC({dom:{tag:"iframe",attributes:{src:e.url}},behaviours:kl([iS.config({}),oh.config({})])})]}],behaviours:kl([Pp.config({mode:"acyclic",useTabstopAt:C(qC)})])};return CF.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?A.none():A.some(nV({buttons:e},s,n)))),u=((e,t)=>{const o=(t,o)=>Ur(t,((t,s)=>{n(t,((n,r)=>{o(e(),n,s.event,t)}))})),n=(e,t)=>{uR.getState(e).get().each((o=>{t(o,e)}))};return[...QN(o,t),o(fS,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})((()=>x),KN((()=>y),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},p=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],h=new aV(e.url,{base_uri:new aV(window.location.href)}),f=`${h.protocol}://${h.host}${h.port?":"+h.port:""}`,b=Zl(),v=[Jp("messages",[Yr((()=>{const t=tc(Ve(window),"message",(t=>{if(h.isSameOrigin(new aV(t.raw.origin))){const n=t.raw.data;lV(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,x,n):(e=>!lV(e)&&a(e)&&ve(e,"mceAction"))(n)&&e.onMessage(x,n)}}));b.set(t)})),Jr(b.clear)]),Al.config({channels:{[RR]:{onReceive:(e,t)=>{pi(e.element,"iframe").each((e=>{const o=e.dom.contentWindow;g(o)&&o.postMessage(t,f)}))}}}})],y=YN({id:s,header:i,body:l,footer:c,extraClasses:p,extraBehaviours:v,extraStyles:m},e,u,n),x=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!r(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{Ir(t,vS,{message:e})}))},unblock:()=>{t((e=>{Fr(e,yS)}))},close:()=>{t((e=>{Fr(e,pS)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([RR],e)}))}}})(y);return{dialog:y,instanceApi:x}},dV=(e,t,o)=>t&&o?[]:[ME.config({contextual:{lazyContext:()=>A.some(Jo(Ve(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"],lazyViewport:t=>Uw(e,t.element).map((e=>({bounds:Ww(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Xt(e.element).top})}))).getOrThunk((()=>({bounds:en(),optScrollEnv:A.none()})))})],uV=e=>{const t=e.editor,o=ab(t),n=(e=>{const t=e.shared;return{open:(o,n)=>{const s=()=>{CF.hide(l),n()},r=jh(b_({name:"close-alert",text:"OK",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"cancel",e)),a=HN(),i=zN(s,t.providers),l=ri(WN({lazySink:()=>t.getSink(),header:VN(a,i),body:LN(o,t.providers),footer:A.some(PN(UN([],[r.asSpec()]))),onEscape:s,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Ur(hS,s)],eventOrder:{}}));CF.show(l);const c=r.get(l);oh.focus(c)}}})(e.backstages.dialog),s=(e=>{const t=e.shared;return{open:(o,n)=>{const s=e=>{CF.hide(c),n(e)},r=jh(b_({name:"yes",text:"Yes",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"submit",e)),a=b_({name:"no",text:"No",primary:!1,buttonType:A.some("secondary"),align:"end",enabled:!0,icon:A.none()},"cancel",e),i=HN(),l=zN((()=>s(!1)),t.providers),c=ri(WN({lazySink:()=>t.getSink(),header:VN(i,l),body:LN(o,t.providers),footer:A.some(PN(UN([],[a,r.asSpec()]))),onEscape:()=>s(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Ur(hS,(()=>s(!1))),Ur(bS,(()=>s(!0)))],eventOrder:{}}));CF.show(c);const d=r.get(c);oh.focus(d)}}})(e.backstages.dialog),r=(t,o)=>iR.open(((t,n,s)=>{const r=n,a=((e,t,o)=>{const n=la("dialog"),s=e.internalDialog,r=qN(s.title,n,o),a=((e,t,o)=>{const n=NR(e,t,A.none(),o,!1);return CF.parts.body(n)})({body:s.body,initialData:s.initialData},n,o),i=JN(s.buttons),l=ZN(i),c=nV({buttons:i},n,o),d=eV((()=>h),KN((()=>g),o.shared.providers,t),o.shared.getSink),u=(e=>{switch(e){case"large":return["tox-dialog--width-lg"];case"medium":return["tox-dialog--width-md"];default:return[]}})(s.size),m={id:n,header:r,body:a,footer:A.some(c),extraClasses:u,extraBehaviours:[],extraStyles:{}},g=YN(m,e,d,o),p={getId:x(n),getRoot:x(g),getBody:()=>CF.getBody(g),getFooter:()=>CF.getFooter(g),getFormWrapper:()=>{const e=CF.getBody(g);return wm.getCurrent(e).getOr(e)},toggleFullscreen:()=>{const e="tox-dialog--fullscreen",t=Ve(g.element.dom);Ua(t,e)?(Pa(t,e),Wa(t,u)):(ja(t,u),La(t,e))}},h=rV(p,t.redial,l);return{dialog:g,instanceApi:h}})({dataValidator:s,initialData:r,internalDialog:t},{redial:iR.redial,closeWindow:()=>{CF.hide(a.dialog),o(a.instanceApi)}},e.backstages.dialog);return CF.show(a.dialog),a.instanceApi.setData(r),a.instanceApi}),t),a=(n,s,r,a=!1)=>iR.open(((n,i,l)=>{const c=Xn(qn("data",l,i)),d=Ql(),u=e.backstages.popup.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{Ph.reposition(e),ME.refresh(e)})),g=((e,t,o,n)=>{const s=la("dialog"),r=la("dialog-label"),a=la("dialog-content"),i=e.internalDialog,l=jh(((e,t,o,n)=>eS.sketch({dom:dA('<div class="tox-dialog__header"></div>'),components:[GN(e,t,A.some(o),n),$N(),jN(n)],containerBehaviours:kl([cF.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>hi(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))({title:i.title,draggable:!0},s,r,o.shared.providers)),c=jh(((e,t,o,n,s)=>NR(e,t,A.some(o),n,s))({body:i.body,initialData:i.initialData},s,a,o,n)),d=JN(i.buttons),u=ZN(d),m=jh(((e,t,o)=>oV(e,t,o))({buttons:d},s,o)),g=eV((()=>f),{onBlock:e=>{cA.block(h,((t,n)=>XN(e.message,n,o.shared.providers)))},onUnblock:()=>{cA.unblock(h)},onClose:()=>t.closeWindow()},o.shared.getSink),p="tox-dialog-inline",h=ri({dom:{tag:"div",classes:["tox-dialog",p],attributes:{role:"dialog","aria-labelledby":r}},eventOrder:{[dr()]:[uR.name(),Al.name()],[ur()]:["execute-on-form"],[Sr()]:["reflecting","execute-on-form"]},behaviours:kl([Pp.config({mode:"cyclic",onEscape:e=>(Fr(e,pS),A.some(!0)),useTabstopAt:e=>!qC(e)&&("button"!==Ue(e)||"disabled"!==Ot(e,"disabled")),firstTabstop:1}),uR.config({channel:`${DR}-${s}`,updateState:(e,t)=>A.some(t),initialData:e}),oh.config({}),Jp("execute-on-form",g.concat([Kr(Xs(),((e,t)=>{Pp.focusIn(e)}))])),cA.config({getRoot:()=>A.some(h)}),Yp.config({}),VC({})]),components:[l.asSpec(),c.asSpec(),m.asSpec()]}),f=rV({getId:x(s),getRoot:x(h),getFooter:()=>m.get(h),getBody:()=>c.get(h),getFormWrapper:()=>{const e=c.get(h);return wm.getCurrent(e).getOr(e)},toggleFullscreen:()=>{const e="tox-dialog--fullscreen",t=Ve(h.element.dom);Ga(t,[e])?(ja(t,[e]),Wa(t,[p])):(ja(t,[p]),Wa(t,[e]))}},t.redial,u);return{dialog:h,instanceApi:f}})({dataValidator:l,initialData:c,internalDialog:n},{redial:iR.redial,closeWindow:()=>{d.on(Ph.hide),t.off("ResizeEditor",m),d.clear(),r(g.instanceApi)}},e.backstages.popup,a),p=ri(Ph.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:kl([Jp("window-manager-inline-events",[Ur(Cr(),((e,t)=>{Fr(g.dialog,hS)}))]),...dV(t,o,u)]),isExtraPart:(e,t)=>(e=>jw(e,".tox-alert-dialog")||jw(e,".tox-confirm-dialog"))(t)}));return d.set(p),Ph.showWithinBounds(p,ai(g.dialog),{anchor:s},(()=>{const e=t.inline?xt():Ve(t.getContainer()),o=Jo(e);return A.some(o)})),o&&u||(ME.refresh(p),t.on("ResizeEditor",m)),g.instanceApi.setData(c),Pp.focusIn(g.dialog),g.instanceApi}),n);return{open:(t,o,n)=>void 0!==o&&"toolbar"===o.inline?a(t,e.backstages.popup.shared.anchors.inlineDialog(),n,o.ariaAttrs):void 0!==o&&"cursor"===o.inline?a(t,e.backstages.popup.shared.anchors.cursor(),n,o.ariaAttrs):r(t,n),openUrl:(o,n)=>((o,n)=>iR.openUrl((o=>{const s=cV(o,{closeWindow:()=>{CF.hide(s.dialog),n(s.instanceApi)}},t,e.backstages.dialog);return CF.show(s.dialog),s.instanceApi}),o))(o,n),alert:(e,t)=>{n.open(e,t)},close:e=>{e.close()},confirm:(e,t)=>{s.open(e,t)}}};tn.add("silver",(e=>{(e=>{uf(e),(e=>{const t=e.options.register,o=e=>f(e,r)?{value:Fx(e),valid:!0}:{valid:!1,message:"Must be an array of strings."};t("color_map",{processor:o,default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_map_background",{processor:o}),t("color_map_foreground",{processor:o}),t("color_cols",{processor:"number",default:Dx(zx(e,"default").length)}),t("color_cols_foreground",{processor:"number",default:Bx(e,zx(e,Ax).length)}),t("color_cols_background",{processor:"number",default:Bx(e,zx(e,Mx).length)}),t("custom_colors",{processor:"boolean",default:!0}),t("color_default_foreground",{processor:"string",default:Rx}),t("color_default_background",{processor:"string",default:Rx})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:r(e)||f(e,r)?{value:nB(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)})(e);let t=()=>en();const{dialogs:o,popups:n,renderUI:s}=xF(e,{getPopupSinkBounds:()=>t()});Hw(e,n.backstage.shared);const a=uV({editor:e,backstages:{popup:n.backstage,dialog:o.backstage}});return{renderUI:async()=>{const o=await s();return Uw(e,n.getMothership().element).each((e=>{t=()=>Ww(e)})),o},getWindowManagerImpl:x(a),getNotificationManagerImpl:()=>((e,t,o)=>{const n=t.backstage.shared,s=()=>{const t=Jo(Ve(e.getContentAreaContainer())),o=en(),n=Ki(o.x,t.x,t.right),s=Ki(o.y,t.y,t.bottom),r=Math.max(t.right,o.right),a=Math.max(t.bottom,o.bottom);return A.some(Yo(n,s,r-n,a-s))};return{open:(t,r)=>{const a=()=>{r(),Ph.hide(l)},i=ri(of.sketch({text:t.text,level:R(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:t.icon,closeButton:t.closeButton,onAction:a,iconProvider:n.providers.icons,translationProvider:n.providers.translate})),l=ri(Ph.sketch({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:n.getSink,fireDismissalEventInstead:{},...n.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}}}));o.add(l),h(t.timeout)&&t.timeout>0&&Uh.setEditorTimeout(e,(()=>{a()}),t.timeout);const c={close:a,reposition:()=>{const t=ai(i),o={maxHeightFunction:cc()},r=e.notificationManager.getNotifications();if(r[0]===c){const e={...n.anchors.banner(),overrides:o};Ph.showWithinBounds(l,t,{anchor:e},s)}else I(r,c).each((e=>{const n=r[e-1].getEl(),a={type:"node",root:xt(),node:A.some(Ve(n)),overrides:o,layouts:{onRtl:()=>[cl],onLtr:()=>[cl]}};Ph.showWithinBounds(l,t,{anchor:a},s)}))},text:e=>{of.updateText(i,e)},settings:t,getEl:()=>i.element.dom,progressBar:{value:e=>{of.updateProgress(i,e)}}};return c},close:e=>{e.close()},getArgs:e=>e.settings}})(e,{backstage:n.backstage},n.getMothership())}}))}();