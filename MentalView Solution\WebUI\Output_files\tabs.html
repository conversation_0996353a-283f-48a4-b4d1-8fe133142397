﻿<html>
  <style type="text/css">table{border-collapse:collapse;border-spacing:0;empty-cells:show;}a{text-decoration:none;font-family:Verdana;font-size:13px;font-weight:bold;color:#000000;}a:hover{text-decoration:none;}td{white-space:nowrap;}.X1{position:absolute;left:0em;top:0px;}</style>
  <script type="text/javascript"> function hyperclick(element) {var anchorId = element.id;var clicking_td_tag = document.getElementById(anchorId).parentNode.id;var count = document.getElementById('tr_book').cells.length;for (var i = 1; i <= count; i++) {var td_Id= "td"+i;if(clicking_td_tag == td_Id) {document.getElementById(td_Id).setAttribute("bgcolor", "#FFFFFF"); }else {     document.getElementById(td_Id).setAttribute("bgcolor", "#808080"); }}}</script>
  <body alink="rgb(0,0,255)" vlink="rgb(0,0,0)" link="rgb(0,0,0)" bgColor="#808080">
    <div style="position: absolute; overflow:auto; bottom: 0px;left:0; width:100%; background-color:#808080; background-repeat:repeat-x ; height:35px ">
      <table border="0">
        <tr id="tr_book" class="X1">
          <td id="td1" bgColor="#FFFFFF" nowrap="">
            <a id="link1" onclick="hyperclick(this)" href="Φύλλο1.html" target="top">Φύλλο1</a>
          </td>
        </tr>
      </table>
    </div>
  </body>
</html>