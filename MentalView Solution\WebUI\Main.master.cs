﻿using Data;
using Newtonsoft.Json;
using Syncfusion.JavaScript.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebUI
{
    public partial class Main : System.Web.UI.MasterPage
    {
        public string PageTitle
        {
            set
            {
                this.Page.Title = GetGlobalResourceObject("GlobalResources", "ApplicationTitle").ToString() + " - " + value;
                this.pageHeaderLbl.Text = value;
            }
        }

        public ServerMessage ServerMessage
        {
            get
            {
                return this.serverMessage;
            }
        }

        public ScriptManager ScriptManager
        {
            get
            {
                return ((IncludeFiles)this.Master).ScriptManager;

            }
        }

        public void SetMinimalView()
        {
            this.mainSidebar.Visible = false;
            this.contentWrapper.Style.Add("margin-left", "0px");
            this.sidebarToggle.Visible = false;
            this.footer.Visible = false;
        }

        protected void Page_Init(object sender, EventArgs e)
        {

        }

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                //if (this.timezoneOffset.Value == "" || this.dateWithTimezone.Value == "")
                //{
                //    ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "DoPostBack", "__doPostBack('test', '');", true);
                //    //return;
                //}
                if (this.Page.User.Identity.IsAuthenticated == false)
                {
                    Response.Redirect(@"~\Login.aspx");
                }

                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                string roleName = userData["Role"].ToString();

                //Αν ο χρήστης είναι απλός User
                if (roleName == "User")
                {
                    this.usersMenuItem.Visible = false;
                    this.reportsMenuItem.Visible = false;
                }
                //Αν ο χρήστης είναι Guest
                if (roleName == "Guest")
                {
                    //this.dashboardMenuItem.Visible = false;
                    this.partnersMenuItem.Visible = false;
                    this.appointmentsMenuItem.Visible = false;
                    this.emailTemplatesMenuItem.Visible = false;
                    this.usersMenuItem.Visible = false;
                    this.reportsMenuItem.Visible = false;
                    this.linksMenuItem.Visible = false;
                }

                if (Session["UserFullName"] != null)
                {
                    this.usernameLbl.Text = Session["UserFullName"].ToString();
                }
                else
                {
                    Session["UserFullName"] = Business.AdministrationBusiness.GetUserByUsername(this.Page.User.Identity.Name).Users[0].FullName;
                    this.usernameLbl.Text = Session["UserFullName"].ToString();
                }

                this.appointmentsSubmenu.Visible = Request.Path.Contains("Tasks.aspx");
            }
            catch (Exception exp)
            {
                ExceptionLogger.LogException(exp);
                this.serverMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok, "");
            }
        }

        protected void signOutLnk_Click(object sender, EventArgs e)
        {
            try
            {
                FormsAuthentication.SignOut();
                Response.Redirect("~/Login.aspx");
            }
            catch (Exception exp)
            {
                ExceptionLogger.LogException(exp);
                this.serverMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok, "");
            }
        }

        protected void selectAllScheduleResourcesBtn_Click(object sender, EventArgs e)
        {

        }
    }
}