﻿CREATE TABLE [dbo].[Tenants] (
    [TenantId]               BIGINT        IDENTITY (1, 1) NOT NULL,
    [FullName]               NVARCHAR (50) CONSTRAINT [DF_Tenants_FullName] DEFAULT ('') NOT NULL,
    [<PERSON><PERSON><PERSON>UserId]           INT           NULL,
    [SubscriptionPlanId]     INT           NULL,
    [SubscriptionPlan]       NVARCHAR (50) CONSTRAINT [DF_Tenants_Subscription] DEFAULT ('') NOT NULL,
    [LastLoggedUserId]       BIGINT        NULL,
    [LastLoggedUserFullName] NVARCHAR (50) CONSTRAINT [DF_Tenants_LastLoggedUser] DEFAULT ('') NOT NULL,
    [LastLoggedUserDate]     DATETIME      NULL,
    [DateCreated]            DATE          CONSTRAINT [DF_Tenants_DateCreated] DEFAULT (getdate()) NOT NULL,
    CONSTRAINT [PK_Tenants] PRIMARY KEY CLUSTERED ([TenantId] ASC)
);

