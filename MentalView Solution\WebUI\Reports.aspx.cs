﻿using C1.C1Preview.DataBinding;
using C1.C1Report;
using Data;
using Newtonsoft.Json;
using Resources;
using Syncfusion.XlsIO;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebUI
{
    public partial class Reports : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                ((Main)this.Master).PageTitle = GetLocalResourceObject("Page.Title").ToString();
                //Καλούμε την javascript Initialization() λόγω του UpdatePanel.
                ScriptManager.RegisterStartupScript(this, this.GetType(), "temp", "<script language='javascript'>Initialization();</script>", false);


                #region  Controls Localization
                //this.startTimeTxtBox.DatePickerButtonText = new ButtonText() { Today = GlobalResources.TodayText, TimeNow = GlobalResources.TimeNowText, Done = GlobalResources.DoneText, TimeTitle = GlobalResources.TimeText };
                this.appointmentsStartDateTxtBox.ButtonText = GlobalResources.TodayText;
                this.appointmentsStartDateTxtBox.DateFormat = System.Threading.Thread.CurrentThread.CurrentUICulture.DateTimeFormat.ShortDatePattern;
                this.appointmentsStartDateTxtBox.WatermarkText = "";
                this.appointmentsEndDateTxtBox.ButtonText = GlobalResources.TodayText;
                this.appointmentsEndDateTxtBox.DateFormat = System.Threading.Thread.CurrentThread.CurrentUICulture.DateTimeFormat.ShortDatePattern;
                this.appointmentsEndDateTxtBox.WatermarkText = "";
                #endregion

                if (this.IsPostBack == false)
                {
                    this.contactsExportOrderByDDL.SelectedIndex = 0;
                }

                if (this.Page.User.Identity.IsAuthenticated == true)
                {
                    Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                    Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                    Int64 loggedUserId = Convert.ToInt64(userData["UserId"]);
                    string roleName = userData["Role"].ToString();

                    if (roleName == "Guest")
                    {
                        Response.StatusCode = 404;
                        Response.End();
                    }

                    if (this.IsPostBack == false)
                    {
                        this.contactsTypeDDL.SelectedIndex = 0;
                    }

                    Data.MentalViewDataSet usersDS = Data.Business.UsersBusiness.GetAllDoctorUsersList(tenantId);
                    this.therapistContactsTherapistIdDDL.DataSource = usersDS.Users;
                    this.therapistContactsTherapistIdDDL.DataBind();
                    this.appointmentsTherapistIdDDL.DataSource = usersDS.Users;
                    this.appointmentsTherapistIdDDL.DataBind();
                    this.eventsTherapistIdDDL.DataSource = usersDS.Users;
                    this.eventsTherapistIdDDL.DataBind();
                    this.incomeClearanceTherapistIdDDL.DataSource = usersDS.Users;
                    this.incomeClearanceTherapistIdDDL.DataBind();
                }

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        #region  Contacts
        private bool ValidateContactsReportData()
        {
            if (this.contactsTypeDDL.SelectedIndex < 0)
            {
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
                return false;
            }

            return true;
        }

        protected void previewContactsReportBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateContactsReportData())
                {
                    this.PreviewContactsReport();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void PreviewContactsReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareContactsReport(tenantId, this.contactsTypeDDL.Value);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=true','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        protected void exportContactsToPdfBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateContactsReportData())
                {
                    this.ExportContactsReport();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void exportContactsToExcelBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateContactsReportData())
                {
                    this.ExportContactsReportToExcel();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void ExportContactsReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareContactsReport(tenantId, this.contactsTypeDDL.Value);
                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportContactsReportToExcel()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareContactsReport(tenantId, this.contactsTypeDDL.Value);
                Session["ExcelReport"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }
        #endregion  Contacts

        #region  TherapistContacts

        private bool ValidateTherapistContactsReportData()
        {
            if (this.therapistContactsTherapistIdDDL.SelectedIndex < 0)
            {
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
                return false;
            }

            return true;
        }

        protected void previewTherapistContactsReportBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateTherapistContactsReportData())
                {
                    this.PreviewTherapistContactsReport();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void PreviewTherapistContactsReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareTherapistContactsReport(tenantId, Convert.ToInt64(this.therapistContactsTherapistIdDDL.Value), this.therapistContactsTherapistIdDDL.Text);
                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=true','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        protected void exportTherapistContactsReportBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateTherapistContactsReportData())
                {
                    this.ExportTherapistContactsReport2();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void exportTherapistContactsReportToExcelBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateTherapistContactsReportData())
                {
                    this.ExportTherapistContactsReport2ToExcel();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void ExportTherapistContactsReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareTherapistContactsReport(tenantId, Convert.ToInt64(this.therapistContactsTherapistIdDDL.Value), this.therapistContactsTherapistIdDDL.Text);
                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportTherapistContactsReportToExcel()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareTherapistContactsReport(tenantId, Convert.ToInt64(this.therapistContactsTherapistIdDDL.Value), this.therapistContactsTherapistIdDDL.Text);
                Session["ExcelReport"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportTherapistContactsReport2()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareTherapistContactsReport(tenantId, Convert.ToInt64(this.therapistContactsTherapistIdDDL.Value), this.therapistContactsTherapistIdDDL.Text);

                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportTherapistContactsReport2ToExcel()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareTherapistContactsReport(tenantId, Convert.ToInt64(this.therapistContactsTherapistIdDDL.Value), this.therapistContactsTherapistIdDDL.Text);

                Session["ExcelReport"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }
        #endregion

        #region  Appointments
        private bool ValidateAppointmentsReportData()
        {
            if (this.appointmentsTherapistIdDDL.SelectedIndex < 0)
            {
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
                return false;
            }

            return true;
        }

        private void PreviewAppointmentsReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                //foreach (string selectedValue in this.scheduleResourcesDDL.SelectedItemsValue)
                //{
                //    Data.MentalViewDataSet.UsersRow selectedUsersRow = usersDT.FindByUserId(Convert.ToInt64(selectedValue));
                //    resources.Add(new { UserId = selectedUsersRow.UserId, FullName = selectedUsersRow.FullName, Color = Color.FromName("#f8a398") });

                //    selectedUsersIds.Add(Convert.ToInt64(selectedValue));
                //}

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareAppointmentsReport(tenantId, this.appointmentsStartDateTxtBox.Value.Value, this.appointmentsEndDateTxtBox.Value.Value, this.appointmentsTherapistIdDDL.Value);
                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=true','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportAppointmentsReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareAppointmentsReport(tenantId, this.appointmentsStartDateTxtBox.Value.Value, this.appointmentsEndDateTxtBox.Value.Value, this.appointmentsTherapistIdDDL.Value);
                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportAppointmentsReportToExcel()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareAppointmentsReport(tenantId, this.appointmentsStartDateTxtBox.Value.Value, this.appointmentsEndDateTxtBox.Value.Value, this.appointmentsTherapistIdDDL.Value);
                Session["ExcelReport"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        protected void previewAppointmentsReportBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateAppointmentsReportData())
                {
                    this.PreviewAppointmentsReport();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void printAppointmentsReportBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateAppointmentsReportData())
                {
                    this.ExportAppointmentsReport();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void exportAppointmentsReportToExcelBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateAppointmentsReportData())
                {
                    this.ExportAppointmentsReportToExcel();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        #endregion

        #region  Events
        private bool ValidateEventsReportData()
        {
            //if (this.appointmentsTherapistIdDDL.SelectedIndex < 0)
            //{
            //    ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
            //    return false;
            //}


            if (this.eventsStartDateTxtBox.Value.HasValue == false)
            {
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
                return false;
            }

            if (this.eventsEndDateTxtBox.Value.HasValue == false)
            {
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
                return false;
            }

            return true;
        }

        private void PreviewEventsReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                //foreach (string selectedValue in this.scheduleResourcesDDL.SelectedItemsValue)
                //{
                //    Data.MentalViewDataSet.UsersRow selectedUsersRow = usersDT.FindByUserId(Convert.ToInt64(selectedValue));
                //    resources.Add(new { UserId = selectedUsersRow.UserId, FullName = selectedUsersRow.FullName, Color = Color.FromName("#f8a398") });

                //    selectedUsersIds.Add(Convert.ToInt64(selectedValue));
                //}

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareEventsReport(tenantId, this.eventsStartDateTxtBox.Value.Value, this.eventsEndDateTxtBox.Value.Value, this.eventsTherapistIdDDL.Value);
                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=true','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportEventsReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareEventsReport(tenantId, this.eventsStartDateTxtBox.Value.Value, this.eventsEndDateTxtBox.Value.Value);
                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportEventsReportToExcel()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareEventsReport(tenantId, this.eventsStartDateTxtBox.Value.Value, this.eventsEndDateTxtBox.Value.Value);
                Session["ExcelReport"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        protected void previewEventsReportBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateEventsReportData())
                {
                    this.PreviewEventsReport();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void printEventsReportBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateEventsReportData())
                {
                    this.ExportEventsReport();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void exportEventsReportToExcelBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateEventsReportData())
                {
                    this.ExportEventsReportToExcel();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        #endregion

        #region  AppointmentsInDebt

        private bool ValidateAppointmentsInDebtReportData()
        {
            //if (this.appointmentsTherapistIdDDL.SelectedIndex < 0)
            //{
            //    ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
            //    return false;
            //}


            if (this.appointmentsInDebtStartDateTxtBox.Value.HasValue == false)
            {
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
                return false;
            }

            if (this.appointmentsInDebtEndDateTxtBox.Value.HasValue == false)
            {
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
                return false;
            }

            return true;
        }

        private void PreviewAppointmentsInDebtReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                //foreach (string selectedValue in this.scheduleResourcesDDL.SelectedItemsValue)
                //{
                //    Data.MentalViewDataSet.UsersRow selectedUsersRow = usersDT.FindByUserId(Convert.ToInt64(selectedValue));
                //    resources.Add(new { UserId = selectedUsersRow.UserId, FullName = selectedUsersRow.FullName, Color = Color.FromName("#f8a398") });

                //    selectedUsersIds.Add(Convert.ToInt64(selectedValue));
                //}

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareAppointmentsInDebtReport(tenantId, this.appointmentsInDebtStartDateTxtBox.Value.Value, this.appointmentsInDebtEndDateTxtBox.Value.Value);
                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=true','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportAppointmentsInDebtReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareAppointmentsInDebtReport(tenantId, this.appointmentsInDebtStartDateTxtBox.Value.Value, this.appointmentsInDebtEndDateTxtBox.Value.Value);
                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportAppointmentsInDebtReportToExcel()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareAppointmentsInDebtReport(tenantId, this.appointmentsInDebtStartDateTxtBox.Value.Value, this.appointmentsInDebtEndDateTxtBox.Value.Value);
                Session["ExcelReport"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        protected void previewAppointmentsInDebtReportBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateAppointmentsInDebtReportData())
                {
                    this.PreviewAppointmentsInDebtReport();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void printAppointmentsInDebtReportBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateAppointmentsInDebtReportData())
                {
                    this.ExportAppointmentsInDebtReport();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void exportAppointmentsInDebtReportToExcelBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateAppointmentsInDebtReportData())
                {
                    this.ExportAppointmentsInDebtReportToExcel();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }
        #endregion

        #region CanceledAppointments
        private bool ValidateCanceledAppointmentsReportData()
        {
            //if (this.appointmentsTherapistIdDDL.SelectedIndex < 0)
            //{
            //    ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
            //    return false;
            //}


            if (this.canceledAppointmentsStartDateTxtBox.Value.HasValue == false)
            {
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
                return false;
            }

            if (this.canceledAppointmentsEndDateTxtBox.Value.HasValue == false)
            {
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
                return false;
            }

            return true;
        }

        private void PreviewCanceledAppointmentsReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                //foreach (string selectedValue in this.scheduleResourcesDDL.SelectedItemsValue)
                //{
                //    Data.MentalViewDataSet.UsersRow selectedUsersRow = usersDT.FindByUserId(Convert.ToInt64(selectedValue));
                //    resources.Add(new { UserId = selectedUsersRow.UserId, FullName = selectedUsersRow.FullName, Color = Color.FromName("#f8a398") });

                //    selectedUsersIds.Add(Convert.ToInt64(selectedValue));
                //}

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareCanceledAppointmentsReport(tenantId, this.canceledAppointmentsStartDateTxtBox.Value.Value, this.canceledAppointmentsEndDateTxtBox.Value.Value);
                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=true','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportCanceledAppointmentsReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareCanceledAppointmentsReport(tenantId, this.canceledAppointmentsStartDateTxtBox.Value.Value, this.canceledAppointmentsEndDateTxtBox.Value.Value);
                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportCanceledAppointmentsReportToExcel()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareCanceledAppointmentsReport(tenantId, this.canceledAppointmentsStartDateTxtBox.Value.Value, this.canceledAppointmentsEndDateTxtBox.Value.Value);
                Session["ExcelReport"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        protected void previewCanceledAppointmentsReportBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateCanceledAppointmentsReportData())
                {
                    this.PreviewCanceledAppointmentsReport();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void printCanceledAppointmentsReportBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateCanceledAppointmentsReportData())
                {
                    this.ExportCanceledAppointmentsReport();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void exportCanceledAppointmentsReportToExcelBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateCanceledAppointmentsReportData())
                {
                    this.ExportCanceledAppointmentsReportToExcel();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }
        #endregion

        #region  SupervisionEvents
        private bool ValidateSupervisionEventsReportData()
        {
            //if (this.appointmentsTherapistIdDDL.SelectedIndex < 0)
            //{
            //    ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
            //    return false;
            //}


            if (this.supervisionEventsStartDateTxtBox.Value.HasValue == false)
            {
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
                return false;
            }

            if (this.supervisionEventsEndDateTxtBox.Value.HasValue == false)
            {
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
                return false;
            }

            return true;
        }

        private void PreviewSupervisionEventsReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                //foreach (string selectedValue in this.scheduleResourcesDDL.SelectedItemsValue)
                //{
                //    Data.MentalViewDataSet.UsersRow selectedUsersRow = usersDT.FindByUserId(Convert.ToInt64(selectedValue));
                //    resources.Add(new { UserId = selectedUsersRow.UserId, FullName = selectedUsersRow.FullName, Color = Color.FromName("#f8a398") });

                //    selectedUsersIds.Add(Convert.ToInt64(selectedValue));
                //}

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareSupervisionEventsReport(tenantId, this.supervisionEventsStartDateTxtBox.Value.Value, this.supervisionEventsEndDateTxtBox.Value.Value);
                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=true','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportSupervisionEventsReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareSupervisionEventsReport(tenantId, this.supervisionEventsStartDateTxtBox.Value.Value, this.supervisionEventsEndDateTxtBox.Value.Value);
                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportSupervisionEventsReportToExcel()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareSupervisionEventsReport(tenantId, this.supervisionEventsStartDateTxtBox.Value.Value, this.supervisionEventsEndDateTxtBox.Value.Value);
                Session["ExcelReport"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        protected void previewSupervisionEventsReportBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateSupervisionEventsReportData())
                {
                    this.PreviewSupervisionEventsReport();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void printSupervisionEventsReportBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateSupervisionEventsReportData())
                {
                    this.ExportSupervisionEventsReport();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void exportSupervisionEventsReportToExcelBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateSupervisionEventsReportData())
                {
                    this.ExportSupervisionEventsReportToExcel();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        #endregion

        #region  Export Contacts
        protected void exportsContactsToExcelBtn_Click(object sender, EventArgs e)
        {
            try
            {
                using (ExcelEngine excelEngine = new ExcelEngine())
                {
                    IApplication application = excelEngine.Excel;
                    application.DefaultVersion = ExcelVersion.Excel2016;

                    //Create a new workbook
                    IWorkbook workbook = application.Workbooks.Create(1);
                    IWorksheet sheet = workbook.Worksheets[0];

                    //Διαβάζει τα δεδομένα
                    Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                    Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                    //Create datatable from the dataset
                    DataTable dataTable;
                    dataTable = Business.ContactsBusiness.GetContactsForExport(tenantId, this.contactsExportOrderByDDL.Value);

                    //Import data from the data table with column header, at first row and first column, 
                    //and by its column type.
                    sheet.ImportDataTable(dataTable, true, 1, 1, true);

                    //Creating Excel table or list object and apply style to the table
                    IListObject table = sheet.ListObjects.Create("Πελάτες", sheet.UsedRange);

                    table.BuiltInTableStyle = TableBuiltInStyles.TableStyleMedium13;

                    //Autofit the columns
                    sheet.UsedRange.AutofitColumns();

                    //Save the file in the given path
                    //Stream excelStream = File.Create(Path.GetFullPath(@"Πελάτες.xlsx"));
                    // workbook.SaveAs(excelStream);
                    //excelStream.Dispose();
                    workbook.SaveAs("Πελάτες.xlsx", Response, ExcelDownloadType.PromptDialog, ExcelHttpContentType.Excel2016);
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }
        #endregion

        protected void replaceContactsInExcelBtn_Click(object sender, EventArgs e)
        {
            string rootDirectory = Server.MapPath("~");
            string filePath = rootDirectory + replaceContactsInExcelFileUpload.FileName;
            string column = this.replaceContactsInExcelColumnTxtBox.Text;

            try
            {
                if (column.Trim() != "" && new string[] { "A", "B", "C", "D" }.Any(x => x.Equals(column) == true))
                {
                    if (this.replaceContactsInExcelFileUpload.HasFile)
                    {
                        try
                        {
                            Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                            Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                            replaceContactsInExcelFileUpload.SaveAs(filePath);

                            //Creates a new instance for ExcelEngine
                            ExcelEngine excelEngine = new ExcelEngine();

                            //Initialize IApplication
                            IApplication application = excelEngine.Excel;

                            //Loads or open an existing workbook through Open method of IWorkbooks
                            IWorkbook workbook = application.Workbooks.Open(filePath);

                            IWorksheet sheet = workbook.Worksheets[0];

                            //Διαβάζει όλους τους πελάτες
                            DataTable contactsDT = Business.ContactsBusiness.GetAllContactsList(tenantId);

                            int emptyCellsFound = 0;
                            int rowIndex = 1;
                            while (emptyCellsFound < 4)  //Εφόσον δεν έχει βρει κενά κελιά 4 φορές.
                            {
                                string cellValue = sheet.Range[column + rowIndex.ToString()].DisplayText;

                                //Αν βρει κενά κελιά.
                                if (cellValue == "" || cellValue == null)
                                {
                                    emptyCellsFound++;
                                    continue;
                                }

                                //Αν βρήκε πελάτη με το ContactCode
                                DataRow[] foundContacts = contactsDT.Select("ContactCode='" + cellValue + "'");
                                if (foundContacts.Length > 0)
                                {
                                    sheet.Range[column + rowIndex.ToString()].Value = foundContacts[0]["FullName"].ToString();
                                }
                                rowIndex++;
                            }

                            workbook.SaveAs(replaceContactsInExcelFileUpload.FileName, Response, ExcelDownloadType.PromptDialog, ExcelHttpContentType.Excel2016);
                        }
                        catch (Exception ex)
                        {
                            Data.ExceptionLogger.LogException(ex);
                            ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
                            if (File.Exists(filePath))
                            {
                                System.IO.File.Delete(filePath);
                            }
                        }
                        finally
                        {
                            if (File.Exists(filePath))
                            {
                                System.IO.File.Delete(filePath);
                            }
                        }
                    }
                    else
                    {
                        //lblmessage.Text = sb.ToString();
                    }
                }
                else
                {
                    ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, this.GetLocalResourceObject("ReplaceContactsInExcelColumnRequired").ToString(), ServerMessageButtons.Ok);
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void previewIncomeClearanceBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateIncomeClearanceReportData())
                {
                    this.PreviewIncomeClearanceReport();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private bool ValidateIncomeClearanceReportData()
        {
            if (this.incomeClearanceTherapistIdDDL.SelectedIndex < 0)
            {
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
                return false;
            }


            if (this.incomeClearanceStartDateTxtBox.Value.HasValue == false)
            {
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
                return false;
            }

            if (this.incomeClearanceEndDateTxtBox.Value.HasValue == false)
            {
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.InvalidInputDataMessage, ServerMessageButtons.Ok);
                return false;
            }

            return true;
        }

        protected void printIncomeClearanceBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateIncomeClearanceReportData())
                {
                    this.ExportIncomeClearanceReport();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void exportIncomeClearanceToExcelBtn_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.ValidateIncomeClearanceReportData())
                {
                    this.ExportIncomeClearanceReportToExcel();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void PreviewIncomeClearanceReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                //foreach (string selectedValue in this.scheduleResourcesDDL.SelectedItemsValue)
                //{
                //    Data.MentalViewDataSet.UsersRow selectedUsersRow = usersDT.FindByUserId(Convert.ToInt64(selectedValue));
                //    resources.Add(new { UserId = selectedUsersRow.UserId, FullName = selectedUsersRow.FullName, Color = Color.FromName("#f8a398") });

                //    selectedUsersIds.Add(Convert.ToInt64(selectedValue));
                //}

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareIncomeClearanceReport(tenantId, this.incomeClearanceStartDateTxtBox.Value.Value, this.incomeClearanceEndDateTxtBox.Value.Value, this.incomeClearanceTherapistIdDDL.Value);
                report = reportFactory.PrepareIncomeClearanceReport(tenantId, this.incomeClearanceStartDateTxtBox.Value.Value, this.incomeClearanceEndDateTxtBox.Value.Value, this.incomeClearanceTherapistIdDDL.Value);  //2η φορά για να φύγει το trial
                Session["C1Report"] = report;
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=true','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportIncomeClearanceReport()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareIncomeClearanceReport(tenantId, this.incomeClearanceStartDateTxtBox.Value.Value, this.incomeClearanceEndDateTxtBox.Value.Value, this.incomeClearanceTherapistIdDDL.Value);
                report = reportFactory.PrepareIncomeClearanceReport(tenantId, this.incomeClearanceStartDateTxtBox.Value.Value, this.incomeClearanceEndDateTxtBox.Value.Value, this.incomeClearanceTherapistIdDDL.Value);  //2η φορά για να φύγει το trial
                Session["C1Report"] = report;
                Session["ReportName"] = this.GetLocalResourceObject("IncomeClearanceReportName") + ".pdf";
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void ExportIncomeClearanceReportToExcel()
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                //Καθαρίζει τα άλλα reports στο session
                Session.Remove("C1Report");
                Session.Remove("PdfReport");
                Session.Remove("Report");
                Session.Remove("ExcelReport");

                Reporting.ReportsFactory reportFactory = new WebUI.Reporting.ReportsFactory();
                C1Report report = reportFactory.PrepareIncomeClearanceReport(tenantId, this.incomeClearanceStartDateTxtBox.Value.Value, this.incomeClearanceEndDateTxtBox.Value.Value, this.incomeClearanceTherapistIdDDL.Value);
                report = reportFactory.PrepareIncomeClearanceReport(tenantId, this.incomeClearanceStartDateTxtBox.Value.Value, this.incomeClearanceEndDateTxtBox.Value.Value, this.incomeClearanceTherapistIdDDL.Value);  //2η φορά για να φύγει το trial
                Session["ExcelReport"] = report;
                Session["ReportName"] = this.GetLocalResourceObject("IncomeClearanceReportName") + ".pdf";
                ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=false','_blank');", true);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

    }
}