/*!
*  filename: ej.localetexts.en-US.min.js
*  version : *********
*  Copyright Syncfusion Inc. 2001 - 2020. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
ej.ReportDesigner.Locale["en-US"]={itemPanel:{waterMarkText:"Search Widgets",noDataFound:"No matches found...",customCategory:"Barcodes",dataRequirements:"Data Requirements",customRptItemName:"1D Barcode",customTooltip:{tooltip:{requirements:"Add a report item to the designer area.",description:"Displays the custom report item.",title:"Custom Reportitem"}},groupItems:{basicItems:{groupName:"Basic Items",Items:{line:{displayText:"Line",tooltip:{requirements:"To separate a region through a line in report sections.",description:"Graphical element to seperate the report region.",title:"Line"}},image:{displayText:"Image",tooltip:{requirements:"To display an image from the database, embed the image.",description:"Displays the images.",title:"Image"}},textBox:{displayText:"TextBox",tooltip:{requirements:"Add any text.",description:"Displays the static and dynamic text.",title:"TextBox"}},rectangle:{displayText:"Rectangle",tooltip:{requirements:"Combine one or more report items inside it.",description:"Graphical container element.",title:"Rectangle"}}}},comparison:{groupName:"Comparison",Items:{column:{displayText:"Column",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Compares values for a set of unordered items across various categories using the vertical bars arranged horizontally.",title:"Column"}},bar:{displayText:"Bar",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Compares values for a set of unordered items across various categories using the horizontal bars arranged vertically.",title:"Bar"}},stackedColumn:{displayText:"Stacked Column",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Compares multiple measures using the bars stacked vertically.",title:"Stacked Column"}},stackedBar:{displayText:"Stacked Bar",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Compares multiple measures using the bars stacked horizontally.",title:"Stacked Bar"}},stackedColumnPercent:{displayText:"Stacked Column100%",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Compares multiple measures as parts of a whole using the bars stacked vertically.",title:"Stacked Column100%"}},stackedBarPercent:{displayText:"Stacked Bar100%",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Compares multiple measures as parts of a whole using the bars stacked horizontally.",title:"Stacked Bar100%"}}}},proportion:{groupName:"Proportion",Items:{pie:{displayText:"Pie",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Showcases the proportions of each items contribution to the total in the form of pie-slices.",title:"Pie"}},explodedPie:{displayText:"Exploded Pie",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Emphasizes an individual slice of a pie chart.",title:"Exploded Pie"}},doughnut:{displayText:"Doughnut",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Showcases the proportions of each items contribution to the total in the form of doughnut-slices.",title:"Doughnut"}},pyramid:{displayText:"Pyramid",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Showcases the proportional comparison between values in a progressively increasing manner.",title:"Pyramid"}},funnel:{displayText:"Funnel",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Showcases the proportional comparison between values in a progressively decreasing manner.",title:"Funnel"}}}},distribution:{groupName:"Distribution",Items:{area:{displayText:"Area",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Compares values for a set of unordered items across various categories through the filled curves ordered vertically.",title:"Area"}},smoothArea:{displayText:"Smooth Area",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Compares values for a set of unordered items across various categories through the filled curves ordered vertically with smooth surface.",title:"Smooth Area"}},stackedArea:{displayText:"Stacked Area",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Compares multiple measures through the filled curves stacked vertically.",title:"Stacked Area"}},stackedAreaPercent:{displayText:"Stacked Area100%",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Compares multiple measures as parts of a whole through the filled curves stacked vertically.",title:"Stacked Area100%"}},line:{displayText:"Line",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Showcases the trends for analysis over a time with data points connected using the straight lines.",title:"Line"}},smoothLine:{displayText:"Smooth Line",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Compares the distribution of values over a time period connected using the smooth lines.",title:"Smooth Line"}},steppedLine:{displayText:"Stepped Line",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Compares the distribution of values over a time period connected using the stepped lines.",title:"Stepped Line"}},lineWithMarkers:{displayText:"Line with Markers",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Compare changes over the same period of time for more than one group.",title:"Line with Markers"}},smoothLineWithMarkers:{displayText:"SmoothLine with Markers",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Plotted values are represented with a marker point and those points are connected using a smooth line.",title:"SmoothLine with Markers"}},scatter:{displayText:"Scatter",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Displays a series as a set of points and values are represented by the position of points on the chart.",title:"Scatter"}},bubble:{displayText:"Bubble",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Displays the difference between two values of a data point based on the size of the bubble.",title:"Bubble"}},polar:{displayText:"Polar",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Displays a series as a set of points that are grouped by category on a 360-degree circle.",title:"Polar"}},radar:{displayText:"Radar",tooltip:{requirements:"1 or more values and 1 or more columns.",description:"Displays a series as a circular line or area.",title:"Radar"}}}},dataRegions:{groupName:"Data Regions",Items:{tablix:{displayText:"Table",tooltip:{requirements:"1 or more rows/columns.",description:"Displays paginated report data in cells.",title:"Table"}},list:{displayText:"List",tooltip:{requirements:"1 or more rows/columns.",description:"A List displays data in a free-form format. Place fields anywhere within the list.",title:"List"}}}},subReports:{groupName:"Sub Reports",Items:{subreport:{displayText:"SubReport",tooltip:{requirements:"Display/Embed the report in main report.",description:"Displays another report in the main report body.",title:"SubReport"}}}}}},toolbar:{newReport:"New",open:"Open",openMenu:{fromDevice:"From Device",fromServer:"From Server"},save:"Save",saveMenu:{saveLabel:"Save",saveAs:"Save As",saveAsMenu:{saveToDevice:"To Device",saveToServer:"To Server"}},cut:"Cut",copy:"Copy",paste:"Paste",deleteItem:"Delete",undo:"Undo",redo:"Redo",zoomIn:"Zoom In",zoomOut:"Zoom Out",header:"Header",footer:"Footer",order:"Order",orderMenu:{sendBackward:"Send Backward",bringForward:"Bring Forward",sendToBack:"Send To Back",bringToFront:"Bring To Front"},left:"Left Align",center:"Center",right:"Right Align",top:"Top Align",middle:"Middle",bottom:"Bottom Align",distributeHorizontally:"Distribute Horizontally",distributeVertically:"Distribute Vertically",centerHorizontally:"Center Horizontally",centerVertically:"Center Vertically",sizing:"Sizing",sizingMenu:{sameSize:"Same Size",sameWidth:"Same Width",sameHeight:"Same Height"},alignToGrid:"Align To Grid",sizeToGrid:"Size To Grid",gridLine:"Grid Lines",snapToShape:"Snap To Shape",fullScreen:"Full Screen",preview:"Preview",reportUpload:{alertLabel:"Upload",alertMessage:"Error in uploading file. Please upload again"},grouping:"Grouping",view:"View"},newReport:{title:"New Report",fileName:"File Name",waterMark:"Report Name",create:"Create",cancel:"Cancel",close:"Close"},reportAction:{enableLink:"Enable Link",linkTo:"Link To",report:"Report",url:"URL"},linkReport:{reportCaption:"Report",setParameter:"Set Parameters"},imageProperty:{basicSettings:{categoryName:"Basic Settings",source:"Source",sourceTypes:{external:"External",embedded:"Embedded",database:"Database"},value:"Value",mimeType:"MIME Type",mimeTypes:{bmp:"image/bmp",jpeg:"image/jpeg",gif:"image/gif",png:"image/png",xPng:"image/x-png"}},categoryName:"Link",linkReport:"Link Report",appearance:{categoryName:"Appearance",styleTooltip:"Style",colorTooltip:"Color",sizeTooltip:"Size",borderTypes:{border:"Border",borderLeft:"Left",borderTop:"Top",borderRight:"Right",borderBottom:"Bottom"},borderStyles:{solid:"Solid",none:"None",double:"Double",dashed:"Dashed",dotted:"Dotted"}},size:{categoryName:"Size",paddingTypes:{padding:"Padding",paddingLeft:"Left",paddingTop:"Top",paddingRight:"Right",paddingBottom:"Bottom"},sizing:"Sizing",sizeTypes:{auto:"AutoSize",fit:"Fit",proportional:"FitProportional",clip:"Clip"}},position:{categoryPosition:"Position",positionLabel:"Position",left:"Left",top:"Top",sizeLabel:"Size",width:"Width",height:"Height"},visibility:{categoryName:"Visibility",visible:"Visible",toggleItem:"Toggle Item"}},chartProperty:{commonProperties:{showBorder:"Show Border",border:{border:"Border",borderLeft:"Left",borderTop:"Top",borderRight:"Right",borderBottom:"Bottom"},background:"Background Color",font:"Font",fontStyle:"Font Style",labelRotation:"Label Rotation",categoryAxis:"Category Axis",valueAxis:"Value Axis",defaultText:"Default",auto:"Auto",borderStyles:{solid:"Solid",none:"None",double:"Double",dashed:"Dashed",dotted:"Dotted",dashDot:"DashDot",dashDotDot:"DashDotDot"},horizontalAlignments:{near:"Near",far:"Far"},textAlignments:{right:"Right",bottom:"Bottom",center:"Center",topLeft:"TopLeft",topCenter:"TopCenter",topRight:"TopRight",rightTop:"RightTop",rightCenter:"RightCenter",rightBottom:"RightBottom",bottomLeft:"BottomLeft",bottomCenter:"BottomCenter",bottomRight:"BottomRight",leftTop:"LeftTop",leftCenter:"LeftCenter",leftBottom:"LeftBottom"},fontStyleTypes:{normal:"Normal",italic:"Italic"},fontWeightTypes:{light:"Light",bold:"Bold"}},basicSettings:{categoryName:"Basic Settings",showLegend:{showLegendText:"Show Legend",title:"Title",titleFont:"Title Font",titleFontStyle:"Title Font Style",titleAlignment:"Title Alignment",legendPosition:"Legend Position",enableCustomBounds:"Enable Custom Bounds"},chooseSeries:"Choose Series",showMarker:{showMarkerText:"Show Marker",color:"Color",markerType:"Marker Type",markerTypes:{square:"Square",circle:"Circle",diamond:"Diamond",triangle:"Triangle",cross:"Cross",star5:"Star5"},size:"Size"},showDataLabel:{showDataLabelText:"Show Data Label",dataLabelFormat:"Format",dataLabelText:"Label",dataLabelValueAsText:"UseValueAsLabel",dataLabelTypes:{valueX:"#VALX",valueY:"#VALY",valueY2:"#VALY2",valueY3:"#VALY3",valueY4:"#VALY4",valueY5:"#VALY5",valueY6:"#VALY6",index:"#INDEX",percent:"#PERCENT",total:"#TOTAL",axisLabel:"#AXISLABEL"}},enableSmartLabel:{smartLabelText:"Enable SmartLabel",labelStyle:"Label Style",value:"Value",smartLabelPositions:{outside:"Outside",inside:"Inside",outsideInColumn:"OutsideInColumn"},smartLabelStyles:{pieLabelStyle:"PieLabelStyle",funnelLabelStyle:"FunnelLabelStyle",pyramidLabelStyle:"PyramidLabelStyle",barLabelStyle:"BarLabelStyle",smartLabelStyle:"LabelStyle"}},seriesBorder:"Series Border",seriesColor:"Series Color"},categoryName:"Appearance",customAttribute:{categoryName:"CustomAttributes",userDefined:"UserDefined",alertHeader:"Chart ReportItem",alertMessage:"Invalid custom attributes format. Correct example: 'AttrName1= Value1, AttrName2 = Value2'."},chartArea:{categoryName:"Chart Area",colorPalette:"Color Palette",colorPaletteTypes:{earthTones:"EarthTones",excel:"Excel",grayScale:"GrayScale",pastel:"Pastel",semiTransparent:"SemiTransparent",berry:"Berry",chocolate:"Chocolate",fire:"Fire",seaGreen:"SeaGreen",brightPastel:"BrightPastel",pacific:"Pacific",pacificLight:"PacificLight",pacificSemiTransparent:"PacificSemiTransparent"}},title:{categoryName:"Title",showChartTitle:"Show Chart Title",titleText:"Title Text",titlePosition:"Title Position"},axis:{enableAxis:"Enable Axis",axisTitle:"Axis Title",alignment:"Alignment",lineStyle:"Line Style",labelOverflowMode:"Label Overflow Mode",overFlowModeTypes:{trim:"Trim",hide:"Hide"},labelFont:"Label Font",labelFormat:"Label Format",enableMajorTicks:"Enable Major Ticks",enableMinorTicks:"Enable Minor Ticks",tickProperties:{tickSize:"Tick Size",tickColor:"Tick Color",tickWidth:"Width",length:"Length"},tickPosition:"Tick Position"},gridLine:{categoryName:"Grid Line",gridLineStyle:{minorGridLine:"Show Minor GridLine",majorGridLineStyle:"Major GridLine Style",minorGridLineStyle:"Minor GridLine Style"}},pageBreak:{categoryName:"Page Break",enablePageBreak:"Enable Page Break",breakLocation:"Break Location",breakLocationTypes:{none:"None",start:"Start",end:"End",startAndEnd:"StartAndEnd",between:"Between"},pageNumberReset:"Page Number Reset",pageName:"Page Name"},position:{categoryPosition:"Position",positionLabel:"Position",left:"Left",top:"Top",sizeLabel:"Size",width:"Width",height:"Height"},visibility:{categoryName:"Visibility",visible:"Visible",toggleItem:"Toggle Item"},fontStyleTooltip:"Style",fontWeightTooltip:"Weight",fontSizeTooltip:"Size",fontColorTooltip:"Color",fontFamilyTooltip:"Font Family",styleTooltip:"Style",colorTooltip:"Color",sizeTooltip:"Size"},lineProperty:{basicSettings:{categoryBasicSettings:"Basic Settings",line:"Line",lineTypes:{solid:"Solid",dashed:"Dashed",dotted:"Dotted"}},position:{categoryPosition:"Position",positionLabel:"Position",left:"Left",top:"Top",sizeLabel:"Size",width:"Width",height:"Height"},visibility:{categoryName:"Visibility",visible:"Visible",toggleItem:"Toggle Item"},styleTooltip:"Style",colorTooltip:"Color",sizeTooltip:"Size"},subReportProperty:{basicSettings:{categoryBasicSettings:"Basic Settings"},appearance:{categoryAppearance:"Appearance",borderTypes:{border:"Border",borderLeft:"Left",borderTop:"Top",borderRight:"Right",borderBottom:"Bottom"},borderStyles:{solid:"Solid",none:"None",double:"Double",dashed:"Dashed",dotted:"Dotted"}},noRows:{noRowsLabel:"No Rows",font:"Font",fontStyle:{fontStyleLabel:"Font Style",fontItem:{defaultStyle:"Default",fontNormal:"Normal",italic:"Italic"},fontWeight:{defaultElement:"Default",normal:"Normal",thin:"Thin",extraLight:"ExtraLight",light:"Light",medium:"Medium",semiBold:"SemiBold",bold:"Bold",extraBold:"ExtraBold",heavy:"Heavy"}},textDecoration:{textDecorationLabel:"Text Decoration",defaultDecoration:"Default",none:"None",underLine:"Underline",overLine:"Overline",lineThrough:"LineThrough"},format:"Format",lineHeight:"Line Height",message:"Message",paddingTypes:{padding:"Padding",paddingLeft:"Left",paddingRight:"Right",paddingTop:"Top",paddingBottom:"Bottom"},textAlign:{textAlignLabel:"Text Align",textAlignDefault:"Default",textAlignGeneral:"General",textAlignLeft:"Left",textAlignRight:"Right",textAlignCenter:"Center"},verticalAlign:{verticalAlignlabel:"Vertical Align",verticalAlignDefault:"Default",verticalAlignTop:"Top",verticalAlignMiddle:"Middle",verticalAlignBottom:"Bottom"},writingMode:{writingModeLabel:"Writing Mode",writingModeDefault:"Default",writingModeHorizontal:"Horizontal",writingModeVertical:"Vertical",writingModeRotate:"Rotate270"}},visibility:{categoryName:"Visibility",visible:"Visible",toggleItem:"Toggle Item"},position:{categoryPosition:"Position",positionLabel:"Position",left:"Left",top:"Top",sizeLabel:"Size",width:"Width",height:"Height"},miscellaneous:{categoryMiscellaneous:"Miscellaneous",keepTogether:"Keep Together"},fontStyleTooltip:"Style",fontWeightTooltip:"Weight",fontSizeTooltip:"Size",fontColorTooltip:"Color",fontFamilyTooltip:"Font Family",styleTooltip:"Style",colorTooltip:"Color",sizeTooltip:"Size"},rectangleProperty:{basicSettings:{categoryBasicSettings:"Basic Settings",borderTypes:{border:"Border",borderLeft:"Left",borderTop:"Top",borderRight:"Right",borderBottom:"Bottom"},borderStyles:{solid:"Solid",none:"None",double:"Double",dashed:"Dashed",dotted:"Dotted"},backGround:"Background Color",styleTooltip:"Style",colorTooltip:"Color",sizeTooltip:"Size"},pageBreak:{pageBreak:"Page Break",enablePageBreak:{enablePageBreak:"Enable Page Break",breakLocation:{breakLocationLabel:"Break Location",none:"None",start:"Start",end:"End",startAndEnd:"StartAndEnd",between:"Between"},pageNumberReset:"Page Number Reset"}},position:{categoryPosition:"Position",positionLabel:"Position",left:"Left",top:"Top",sizeLabel:"Size",width:"Width",height:"Height"},visibility:{categoryName:"Visibility",visible:"Visible",toggleItem:"Toggle Item"},rectangleMiscellaneous:{categoryMiscellaneous:"Miscellaneous",keepTogether:"Keep Together",pageName:"Page Name"}},browseFile:{openFile:{selectReport:"Select Report",open:"Open"},saveFile:{saveAsReport:"Save As Report",name:"Name",save:"Save"},close:"Close",cancel:"Cancel",waterMark:"Report Name",emptyMessage:"This category is empty",alertMessage:{reportServer:"Report Server",selectCategory:"Please Select Category"},warningMessage:{fileNameLabel:'An item "',fileNameExist:'.rdl" already exists. Do you want to replace the existing item?',populateCategory:"ReportDesigner failed to retrieve the Resources from the ReportServer"}},expressionMenu:{reset:"Reset",expression:"Expression",advanced:"Advanced"},propertyPanel:{property:"PROPERTIES",data:"DATA",name:"Name",toolTipStyle:"Style",toolTipColor:"Color",toolTipWidth:"Width",setSorts:"Set Sorts",setFilters:"Set Filters",advancedOptions:"Advanced Options",codemodules:"Code",expressionList:{top:"Top",right:"Right",bottom:"Bottom",left:"Left",style:"Style",color:"Color",size:"Size",fontFamily:"Font Family",width:"Width",height:"Height",weight:"Weight",image:"Image"},alertMessage:{nameWarning:"Name cannot be empty",nameAlert:"Name already exists",nameValidation:"Name should not contain spaces and special characters"},unitType:{inchText:"in",centimeterText:"cm",pixelText:"pixel",pointText:"pt",millimeterText:"mm",picaText:"pc"},setGroups:"Set Groups",addDatasource:"Add Datasource",dataAlertMsg:"No data source added !"},dataSource:{newDatasource:"NEW DATASOURCE",datasource:"DATASOURCES",datasourceList:{data:"Data",contextMenu:{editItem:"Edit",deleteItem:"Delete",createDataSet:"Create DataSet",cloneDatasource:"Clone"}},datasourceType:{existOption:"Existing",newOption:"Create New",selectDatasoure:"Select the DataSource",connectDatasource:"Connect DataSource",datasourceType:"Choose the type to connect",sqlLabel:"SQL",sqlCeLabel:"SQLCE",odbcLabel:"ODBC",oracleLabel:"ORACLE",oledbLabel:"OLEDB",xmlLabel:"XML",sharedLabel:"Shared"},datasourceConnection:{newConnection:"NEW CONNECTION",editConnection:"EDIT CONNECTION",name:"Name",save:"Save",connect:"Connect",cancel:"Cancel"},sqlDatasource:{authenticationType:"Authentication Type",window:"Windows",sqlServer:"SQL Server",userName:"Username",password:"Password",switchLabel:"DataSource AdvancePanel",switchAlert:"Switching to the visual designer will discard the manual changes made to the connection string. Do you want to use the visual designer anyway? ",basicOption:{serverName:"Server Name",savePassword:"Save Password",database:"Database",advanceSwitch:"Advance Option"},advanceOption:{connectionString:"Connection String",promptLabel:"Prompt Text",prompt:"Prompt",none:"None",savePassword:"Save Password",basicSwitch:"Basic Option"},alertMessage:{alertConnectionString:"Specify the Connection string",alertPrompt:"Specify the Prompt Text",alertUserName:"Specify the User Name",alertPassword:"Specify the Password",alertServerName:"Specify the server name",alertDatabaseName:"Specify the database name"}},sqlceDatasource:{connectionString:"Connection String",authenticationType:"Authentication Type",authentication:"Authentication",none:"None",password:"Password",savePassword:"Save Password",alertMessage:{alertConnectionString:"Specify the connection string",alertPassword:"Specify the Password"}},odbcDatasource:{connectionString:"Connection String",authenticationType:"Authentication Type",authentication:"Authentication",prompt:"Prompt",none:"None",userName:"Username",password:"Password",promptLabel:"Prompt Text",savePassword:"Save Password",alertMessage:{alertConnectionString:"Specify the Connection string",alertPrompt:"Specify the Prompt Text",alertUserName:"Specify the User Name",alertPassword:"Specify the Password"}},oracleDatasource:{connectionString:"Connection String",authenticationType:"Authentication Type",authentication:"Authentication",prompt:"Prompt",none:"None",userName:"Username",password:"Password",promptLabel:"Prompt Text",savePassword:"Save Password",alertMessage:{alertConnectionString:"Specify the Connection string",alertPrompt:"Specify the Prompt Text",alertUserName:"Specify the User Name",alertPassword:"Specify the Password"}},oledbDatasource:{connectionString:"Connection String",authenticationType:"Authentication Type",authentication:"Authentication",prompt:"Prompt",none:"None",userName:"Username",password:"Password",promptLabel:"Prompt Text",savePassword:"Save Password",alertMessage:{alertConnectionString:"Specify the Connection string",alertPrompt:"Specify the Prompt Text",alertUserName:"Specify the User Name",alertPassword:"Specify the Password"}},xmlDatasource:{connectionString:"Connection String"},sharedDatasource:{datasource:"Shared DataSource",alertMessage:"Select a shared DataSource"},alertMessage:{alertLabel:"DataSource",alertConnectionFailed:"ReportDesigner failed to connect the datasource",dataExtensionFailed:"The selected data provider is not available. Please check the data extension.",connectStringValidation:"As the connection string contains expressions in the data source ",validationMessage:" Please update with a valid connection string.",executionMessage:", we cannot execute the data set for this connection.",confirmMessage:" Are you sure want to save the data source?",deleteValue:"Delete data source '",nameWarning:"Specify the DataSource name",nameAlert:"The specified name already exists in the DataSource list",nameValidation:"Name should not contain spaces and special characters"}},imageManager:{headerText:"IMAGE MANAGER",addImageButton:"ADD IMAGE",deleteImage:"Delete embedded image",image:"Image"},linkParameter:{title:"Parameters",headerTxt:"Link Parameter",descriptionText:"Report Parameters",addText:"ADD",ok:"OK",cancel:"Cancel",nameWaterMark:"Parameter Name",valueWaterMark:"Value",errorMessage:"Enter a value for this property",closeToolTip:"Close"},filter:{title:"Filter",descriptionLable:"Include rows where the following conditions are true.",add:"ADD",ok:"OK",cancel:"Cancel",valueWaterMark:"Value",fieldWaterMark:"Choose Field",closeToolTip:"Close",errorMessage:{booleanValidation:"Value is not a boolean value.",intValidation:"Value is not an integer.",floatValidation:"Value is not a float.",dateTimeValidation:"Value is an invalid date/time format.",topBottomFilter:"The Top % and Bottom % filter operators require a float or an integer datatype.",expressionValidation:"Choose value for expression field"},operatorTypes:{like:"Like",topN:"TopN",bottomN:"BottomN",topPercent:"Top%",bottomPercent:"Bottom%",between:"Between",inFilter:"In"}},dataField:{title:"Data Fields",descriptionLable:"Include the data field rows",add:"ADD",ok:"OK",cancel:"Cancel",fieldNameWaterMark:"Field Name",closeToolTip:"Close",errorMessages:{emptyField:"Specify the field name",invalidCharacters:"Field name should not contain spaces and special characters",sameCharacter:"Field name already exists"},dsNameLabel:"Name",dsNameWaterMark:"Data Name",dsNameValidation:{nameWarning:"Specify the DataSet name",nameAlert:"The specified name already exists in DataSet list",nameValidation:"DataSet name should not contain spaces and special characters"}},dataPanel:{itemTooltip:{properties:"Properties",data:"Data",parameters:"Parameters",imageManager:"Image Manager",expand:"Expand",collapse:"Collapse"},dataSourceNewAlert:{title:"DataSource",contentMessage:"Do you want to cancel the DataSource creation?"},dataSourceEditAlert:{title:"DataSource",contentMessage:"Do you want to cancel the DataSource editing?"},dataSetNewAlert:{title:"DataSet",contentMessage:"Do you want to cancel the DataSet creation?"},dataSetEditAlert:{title:"DataSet",contentMessage:"Do you want to cancel the DataSet editing?"},parameterNewAlert:{title:"Parameter",contentMessage:"Do you want to cancel the Parameter creation?"},parameterEditAlert:{title:"Parameter",contentMessage:"Do you want to cancel the Parameter editing?"}},dataSet:{headerText:"DATA",newData:"ADD DATASET",shareDataset:{headerText:"NEW DATASET",editHeaderText:"EDIT DATASET",save:"Save",cancel:"Cancel",nameLable:"Name",sharedDatasetLabel:"Shared DataSet",errorMessage:{nameValidation:"Specify the DataSet name",datasetValidation:"Select a shared DataSource",duplicateName:"The specified name already exists in the DataSet list",specialCharacter:"Name should not contain spaces and special characters"}},contextMenu:{edit:"Edit",remove:"Delete",cloneDataset:"Clone",filter:"Filter",setField:"Fields"},datasourceSwitcher:"Datasources",deleteDataset:"Delete dataset",deleteField:"Delete field",newDataText:"New Data",sharedDataText:"Shared Data",dataRestriction:{dsCreateRestriction:"DataSource creation has been restricted",title:"Data"},dataFieldSearch:{errorMessage:"No matches found",searchText:"Search"}},reportViewer:{toolbar:{print:"Print",exportText:"Export",pageFit:"Fit to Page",exportformat:{Pdf:"PDF",Excel:"Excel",Word:"Word",Html:"Html",PPT:"PowerPoint",CSV:"CSV"},pageSetup:"Page Setup",gotoFirst:"Goto First",gotoLast:"Goto Last",gotoNext:"Goto Next",gotoPrevious:"Goto Previous",gotoParanet:"Goto Parent",zoomIn:"Zoom In",zoomOut:"Zoom Out",fittopage:{pageWidth:"Page Width",pageHeight:"Whole Page"},printLayout:"Print Layout",refresh:"Refresh",documentMap:"Document Map",parameter:"Parameter",viewDesign:"Close Preview"},pagesetupDialog:{close:"Close",paperSize:"Paper Size",height:"Height",width:"Width",margins:"Margins",top:"Top",bottom:"Bottom",right:"Right",left:"Left",unit:"in",orientation:"Orientation",portrait:"Portrait",landscape:"Landscape",doneButton:"OK",cancelButton:"Cancel"},credential:{userName:"Username",password:"Password"},waterMark:{selectOption:"Select Option",selectValue:"Select a value"},errorMessage:{startMessage:"Report Viewer encountered some issues on loading this report. Please",middleMessage:" Click here",endMessage:"to see the error details",closeMessage:"Close this message"},alertMessage:{close:"Close",title:"ReportViewer",done:"OK",showDetails:"Show details",hideDetails:"Hide Details",reportLoad:"Report Loaded:",RVERR0001:"ReportViewer failed to load the Report",RVERR0002:"ReportViewer failed to render the Report",RVERR0003:"An error occurred in ajax postback",RVERR0004:"Please select a value for the parameter",RVERR0005:"The {parametername} parameter is missing a value",RVERR0006:"Please give the float data type input",RVERR0007:"Please give the integer data type input",RVERR0008:"ReportViewer failed to validate the Datasource credentials",RVERR0009:"The margins are overlapped, or they are off the paper. Enter a different margin size.",RVERR0010:"Please enter a value for the parameter",RVERR0011:"The parameter cannot be blank",RVERR0012:"The value provided for the report parameter {parameterprompt} is not valid for its type."},selectAll:"Select All",viewButton:"View Report"},sortData:{sorting:"Sorting",headerText:"Sort Filter",add:"ADD",changeSortingOptions:"Change Sorting Options.",sortBy:"Sort by",thenBy:"Then by",direction:{ascending:"Ascending",descending:"Descending"},chooseField:"Choose Field",errorMessage:"Choose value for expression field",ok:"OK",cancel:"Cancel",close:"Close"},groupData:{grouping:"Grouping",headerTxt:"Group",headerTxtLabel:"Group Label",name:"Name",label:"Label",changeGroupingOptions:"Change Grouping Options.",add:"ADD",groupBy:"Group by",andOn:"And on",chooseField:"Choose Field",ok:"OK",cancel:"Cancel",close:"Close",errorMessage:{nameErrorMessage:"Please enter the valid name",expressionErrorMessage:"Choose value for an expression field"}},alertMessage:{yes:"Yes",no:"No",ok:"OK",showDetails:"Show Details",hideDetails:"Hide Details",close:"Close"},parameter:{listPanel:{headerText:"PARAMETERS",newParameter:"NEW PARAMETER",editMenu:{edit:"Edit",remove:"Delete"},alertTitle:"Parameter"},configurationPanel:{newHeaderText:"NEW PARAMETER",editHeaderText:"EDIT PARAMETER",nameLabel:"Name",promptLable:"Prompt",dataTypeLable:"Data Type",blankValueLable:'Allow blank value ("")',nullValueLable:"Allow null value",multipleValueLable:"Allow multiple values",visibilityLable:"Visibility",assignValueLable:"Assign Value >>",save:"Save",cancel:"Cancel",visibility:{visible:"Visible",hidden:"Hidden",internal:"Internal"},dataType:{stringType:"String",booleanType:"Boolean",dateTimeType:"DateTime",integerType:"Integer",floatType:"Float"}},errorMessage:{nameField:"Please enter the name",promptField:"Please enter the value",nameAlreadyExists:"The parameter name already exists"},warningMessage:{specialCharacter:"Name should not contain spaces and special characters",multipleValueAlert:"Multiple default values were specified. The parameter does not allow multiple values.",nullValueAlert:"In value field, a null value was specified. The parameter does not allow null values. "},alertMessage:{confirmNullCheck:"Available or default values might contain null value, do you want to enable the allow null value checkbox?",confirmBlankValue:"Available or default values might contain blank value, do you want to enable the blank value checkbox?",dataTypeChange:"Changing the datatype will discard the changes made to the available and default values. Do you want to change the datatype anyway?",deleteAlert:"Delete report parameter"},assignData:{title:"Parameter",availableValue:"Available Value",defaultValue:"Default Value",none:"None",specify:"Specify",query:"Query Value",ok:"OK",cancel:"Cancel",availableFields:{specifyDescriptionText:"Add the available values for the parameters:",queryDescriptionText:"Choose the dataset and fields for the available values:",nameFieldWaterMark:"Label",valueFieldWaterMark:"Value"},defaultFields:{specifyDescriptionText:"Add the default values for the parameters:",queryDescriptionText:"Choose the dataset and fields for the default values:",defValueWaterMark:"Choose Default Value"},datasetWaterMark:"Choose Dataset Value",valueWaterMark:"Choose Value",lableWaterMark:"Choose Label",add:"ADD",datasetLableText:"Dataset",valueLableText:"Value Field",labelFieldText:"Label Field",errorMessage:{boolTypeCheck:"Value is not a boolean value.",dateTypeCheck:"Value is an invalid date format.",intTypeCheck:"Value is not an integer.",floatTypeCheck:"Value is not a float.",multipleValuesCheck:"A multi-value parameter cannot include null values",datasetFieldCheck:"Dataset field is required.",valueFieldCheck:"Value field is required.",syntaxLabelField:"The entered value in the label field is not a valid token syntax.",syntaxValueField:"The entered value in the value field is not a valid token syntax.",blankValueCheck:"The value field is blank. The parameter does not allow blank values.",nullValueCheck:"In value field, a null value was specified. The parameter does not allow null values. "},closeToolTip:"Close"}},formatData:{title:"Format Dialog",typeSelect:"Type",typeFormat:{numberType:{numberType:"Number",decimalPlaces:"Decimal Places",negativeValues:"Negative values",showZeroAs:{showZeroAs:"Show zero as",none:"(none)"},representation:"Representation",repDropDwn:{thousands:"Thousands",millions:"Millions",billions:"Billions"},useRegionFormating:"Use regional formatting",use1000Separator:"Use 1000 Separator (,)"},currency:{currencyType:"Currency",decimalPlaces:"Decimal Places",negativeValues:"Negative values",cultureCurrency:"Currency Culture",showZeroAs:{none:"(none)"},representation:"Representation",repDropDwn:{thousands:"Thousands",millions:"Millions",billions:"Billions"},useRegionFormating:"Use regional formatting",use1000Separator:"Use 1000 Separator (,)",includeSpace:"Include a space"},date:{dateType:"Date",date:"Date"},time:{timeType:"Time",time:"Time"},percentage:{percentageType:"Percentage",decimalPlaces:"Decimal Places",includeSpace:"Include a space"},scientific:{scientificType:"Scientific",decimalPlaces:"Decimal Places"},custom:{customType:"Custom",customFormat:"Custom format"}},preview:"Preview",ok:"OK",cancel:"Cancel",close:"Close"},expression:{title:"Expression",descriptionText:"Set Expression for: ",optionLabel:"Options",dataLabel:"Data",descritionLabel:"Description",exampleLabelText:"Example",ok:"OK",cancel:"Cancel",closeToolTip:"Close",textAreaWaterMark:"Expression",category:{builtInFields:"Built-in Fields",operators:"Operators",functions:"Functions"},parameters:"Parameters",optionWaterMark:"Select an option",dataWaterMark:"Select a data",reportData:"No report data found",description:{executionTime:"The date and time that the reports start to run.",overallPageNumber:"The current overall page number can be used only in the page header or footer.",overallTotalPages:"The total number of pages in the report can be used only in the page header and footer.",pageName:"The name of the current page in the report can be used only in the page header or footer.",pageNumber:"The current page number that can be reset through the use of page breaks",isInteractive:"A boolean that indicates whether the current rendering request uses an interactive format.",renderName:"The name of the renderer as registered in the RSReportServer configuration file.",reportFolder:"The full path to the folder containing the report does not include the report server URL.",reportName:"The URL of the report server where the report is run.",reportServerUrl:"The URL of the report server on which the report is being run.",totalPages:"The total number of pages in the current continuous page sequence can be used only in the page header and footer. The number can be reset by using page breaks.",language:"The language ID of the client running the report.",userID:"The ID of the user running the report.",powerNumberType:"Raises a number to the power of another number.",multiply:"Multiplies two numbers.",integerDivision:"Divides two numbers and returns an integer.",floatDivision:"Divides two numbers and returns a floating-point.",modulus:"Divides two numbers and returns only the remainder.",add:"Adds two numbers and can be used to concatenate two strings.",difference:"Yields the difference between two numbers or indicates the negative value of a numeric expression.",lesser:"Less than.",lesserOrEqual:"Less than or equal to.",greater:"Greater than.",greaterOrEqual:"Greater than or equal to.",equal:"Equal to.",notEqual:"Not equal to.",like:"Compares two strings.",isOperator:"Compares two object reference variables.",expression:"Generates a string concatenation of two expressions.",stringType:"Adds two numbers, and it can be used to concatenate two strings.",and:"Performs a logical conjunction on two Boolean expressions, or bitwise conjunction on two ",not:"Performs logical negation on a Boolean expression, or bitwise negation on a numeric expression.",or:"Used to perform a logical disjunction on two Boolean expressions, or bitwise disjunction on two numeric values.",xor:"Performs a logical exclusion operation on two Boolean expressions, or a bitwise exclusion on two numeric expressions.",andAlso:"Performs short-circuiting logical conjunction on two expressions.",orElse:"Used to perform short-circuiting logical disjunction on two expressions.",left:"Performs an arithmetic left shift on a bit pattern.",right:"Performs an arithmetic right shift on a bit pattern.",asc:"Returns an integer value representing the character code corresponding to a character.",ascW:"Returns an integer value representing the character code corresponding to a character.",chr:"Returns the character associated with the specified character code.",chrW:"Returns the character associated with the specified character code.",filter:"Returns a zero-based array containing a subset of a string array based on specified filter criteria.",formatStringType:"Returns a formatted string according to the instructions in a format string expression.",currency:"Returns an expression formatted as a currency value using the currency symbol defined in the system control panel.",dateTime:"Returns a string expression representing a date/time value.",numberType:"Returns an expression formatted as a number.",percent:"Returns an expression formatted as a percentage (that is, multiplied by 100).",getChar:"Returns a char value representing the character from the specified index in the supplied string.",inStr:"Returns an integer specifying the start position of the first occurrence of one string within another.",inStrRev:"Returns the position of the first occurrence of one string within another, starting from the right side of the string.",join:"Returns a string created by joining a number of substrings in an array.",lCase:"Returns a string or character converted to lowercase.",leftStringType:"Returns a string containing a specified number of characters from the left side of a string.",stringLength:"Returns an integer containing either the number of characters in a string or the number.",lSet:"Returns a left-aligned string containing the specified string adjusted to the specified length.",leftTrim:"Returns the string without left side trailing spaces in the given string.",middle:"Returns a string containing a specified number of characters from a string.",replace:"Returns a string in which a specified substring has been replaced with another.",rightString:"Returns a string containing a specified number of characters from the right side of a string.",rightSet:"Returns a right-aligned string containing the specified string adjusted to the specified length.",rightTrim:"Returns the string without right side trailing spaces in the given string.",stringSpace:"Returns a string consisting of the specified number of spaces.",splitString:"Returns a zero-based, one-dimensional array containing a specified number of substrings.",strComp:"Returns -1, 0, or 1, based on the result of a string comparison.",strConv:"Returns a string converted as specified.",duplicateString:"Returns a string or object consisting of the specified character repeated the specified number of times.",strReverse:"Returns a string in which the character order of a specified string is reversed.",trim:"Returns the string without trailing spaces in the given string",upperCase:"Returns a string or character containing the specified string converted to uppercase.",cDate:"Convert to date.",dateAdd:"Returns a date value containing date and time values to which a specified time interval has been added.",dateDiff:"Returns a long value specifying the number of time intervals between two date values.",datePart:"Returns an integer value containing the specified component of a given date value.",dateSerial:"Returns a date value representing a specified year, month, and day, with the time information set to midnight (00:00:00).",dateString:"Returns or sets a string value representing the current date according to your system.",dateValue:"Returns a date value containing the date information represented by a string, with the time information.",day:"Returns an integer value from 1 through 31 representing the day of the month.",format:"Returns a string expression representing date/time value.",hour:"Returns an integer value from 0 through 23 representing the hour of the day.",minute:"Returns an integer value from 0 through 59 representing the minute of the hour.",month:"Returns an integer value from 1 through 12 representing the month of the year.",monthName:"Returns a string value containing the name of the specified month.",now:"Returns a date value containing the current date and time according to your system.",second:"Returns an integer value from 0 through 59 representing the second of the minute.",timeOfDay:"Returns or sets a date value containing the current time of day according to your system.",timer:"Returns a double value representing the number of seconds elapsed since midnight.",timeSerial:"Returns a date value representing a specified hour, minute, and second, with the date information set relative to January 1 of the year 1.",timeString:"Returns or sets a string value representing the current time of day according to your system.",timeValue:"Returns a date value containing the time information represented by a string, with the date information set to January 1 of the year 1.",timeToday:"Returns or sets a date value containing the current date according to your system.",timeWeekday:"Returns an integer value containing a number that represents the day of the week.",timeWeekdayName:"Returns a string value containing the name of the specified weekday.",year:"Returns an integer value from 1 through 9999 representing the year.",abs:"Returns the absolute value of a single-precision floating-point number.",acos:"Returns the angle whose cosine is the specified number.",asin:"Returns the angle whose sine is the specified number.",atan:"Returns the angle whose tangent is the specified number.",atan2:"Returns the angle whose tangent is the quotient of two specified numbers.",bigMultiply:"Produces the full product of two 32-bit numbers.",ceiling:"Returns the smallest integer that is greater than or equal to the specified integer.",cos:"Returns the cosine of the specified angle.",cosh:"Returns the hyperbolic cosine of the specified angle.",exponent:"Returns e raised to the specified power.",fixNumberType:"Returns an integer portion of a number.",floor:"Returns the largest integer less than or equal to the specified integer.",integer:"Returns an integer portion of a number.",logrithm:"Returns the natural (base e) logarithm of a specified number.",logrithm10:"Returns the base 10 logarithm of a specified number.",maximum:"Returns the maximum value from all non-null values of the specified expression.",minimum:"Returns the minimum value from all non-null values of the specified expression.",power:"Returns a specified number raised to the specified power.",random:"Returns a random number of single type.",round:"Rounds a double-precision floating-point value to the nearest integer.",sign:"Returns a value indicating the sign of an 8-bit signed integer.",sin:"Returns the sine of the specified angle.",sinh:"Returns the hyperbolic sine of the specified angle.",squareRoot:"Returns the square root of a specified number.",tangent:"Returns the tangent of the specified angle.",tangentH:"Returns the hyperbolic tangent of the specified angle.",isArray:"Returns a Boolean value indicating whether variable points to an array.",isDate:"Returns a Boolean value indicating whether an expression represents a valid.",isNothing:"Returns a Boolean value indicating whether an expression has no object.",isNumeric:"Returns a Boolean value indicating whether an expression can be evaluated as a number.",flowChoose:"Selects and returns a value from a list of arguments.",flowIIf:"Returns one of two objects depending upon the evaluation of an expression.",switchFlow:"Evaluates a list of expressions and returns an object value corresponding to the first expression in the list that is true.",avg:"Returns the average of all non-null values from the specified expression.",count:"Returns a count of the values from the specified expression.",countDistinct:"Returns a count of all distinct values from the specified expression.",countRows:"Returns a count of rows within the specified scope.",first:"Returns the first value from the specified expression.",last:"Returns the last value from the specified expression.",standardDev:"Returns the standard deviation of all non-null values of the specified expression.",standardDevP:"Returns the population standard deviation of all non-null values of the specified expression.",sum:"Returns a sum of the values of the specified expression.",variance:"Returns the variance of all non-null values of the specified expression.",varianceP:"Returns the population variance of all non-null values of the specified expression.",runningValue:"Uses a specified function to return a running aggregate of the specified expression.",aggregate:"Returns a custom aggregate of the specified expression, as defined by the data provider.",doubleDeclining:"Returns a double value specifying the depreciation of an asset for a specific time period using the double-declining balance method or some other method you specify.",futureValue:"Returns double value specifying the future value of an annuity based on periodic fixed payments and a fixed interest rate.",interestPayment:"Returns double value specifying the interest payment for a given period of an annuity based on periodic, fixed payments and a fixed interest rate.",numberOfPeriods:"Returns a double value specifying the number of periods for an annuity based on periodic fixed payments and a fixed interest rate.",annuityPayment:"Returns a double value specifying the payment for an annuity based on periodic, fixed payments and a fixed interest rate.",principalPayment:"Returns a double value specifying the principal payment for a given period of an annuity based on periodic fixed payments and a fixed interest rate.",presentValue:"Returns a double value specifying the present value of an annuity based on periodic, fixed payments to be paid in the future and a fixed interest rate.",rateOfInterest:"Returns a double value specifying the interest rate per period for an annuity.",straightLine:"Returns a double value specifying the straight-line depreciation of an asset for a single period.",sumOfYearsDigits:"Returns a double value specifying the sum-of-years digits depreciation of an asset for a specified period.",convertBool:"Convert to Boolean.",convertByte:"Convert to byte.",convertChar:"Convert to char.",convertDate:"Convert to date.",convertDouble:"Convert to double.",convertDecimal:"Convert to decimal.",convertInteger:"Convert to integer.",convertLong:"Convert to long.",convertObject:"Convert to object.",convertShort:"Convert to short.",convertSingle:"Convert to single.",convertString:"Convert to string.",fix:"Returns an integer portion of a number.",hexaDecimal:"Returns a string representing the hexadecimal value of a number.",integerPortion:"Returns an integer portion of a number.",octal:"Returns a string representing the octal value of a number.",stringOfNumber:"Returns a string that represents a number.",stringAsNumeric:"Returns numbers in a string as a numeric value of appropriate type.",inScope:"Returns true if the current instance is within the specified scope.",depthLevel:"Returns a zero-based integer representing the current depth level.",previous:"Returns the value of the expression for the previous row of data.",rowNumber:"Returns a running count of all rows in the specified scope."}},dataAssign:{measures:"Measures",dimensions:"Dimensions",addDatasource:"Add Datasource",errorMessagePrefix:"You have not configured a data source yet.",errorMessageSuffix:"Add a data source to bind data to report items in your designer.",search:"Search",dragOnDrop:"Drag & Drop"},reportProperty:{header:"Header",body:"Body",footer:"Footer",report:"Report",basicSettings:{categoryName:"Basic Settings",background:"Background Color",borderTypes:{border:"Border",borderLeft:"Left",borderTop:"Top",borderRight:"Right",borderBottom:"Bottom"},borderStyles:{solid:"Solid",none:"None",double:"Double",dashed:"Dashed",dotted:"Dotted"}},generalSettings:{categoryName:"General",printFirstPage:"Print on First Page",printLastPage:"Print on Last Page"},size:{sizeLabel:"Size",paddingTypes:{padding:"Padding",paddingLeft:"Left",paddingTop:"Top",paddingRight:"Right",paddingBottom:"Bottom"}},position:{categoryPosition:"Position",positionLabel:"Position",left:"Left",top:"Top",sizeLabel:"Size",width:"Width",height:"Height"},codeModule:{code:"Code"},margin:{categoryName:"Margin",categoryHeader:"Margin",types:{left:"Left",right:"Right",bottom:"Bottom",top:"Top"}},pageUnit:{header:"Page Units",label:"Page Unit",types:{inches:"Inches",centimeters:"Centimeters",pixels:"Pixels",points:"Points",millimeters:"Millimeters",picas:"Picas"}},columns:{header:"Page Column",label:"Columns",columnSpacing:"Column Spacing"},paperSize:{orientation:"Orientation",header:"PaperSize",label:"Paper size",orientationTypes:{landScape:"Landscape",portrait:"Portrait"},types:{a3Size:"A3",a4Size:"A4",b4Size:"B4(JIS)",b5Size:"B5(JIS)",envelope:"Envelope #10",envelopeMonarch:"Envelope Monarch",executive:"Executive",legal:"Legal",letter:"Letter",tabloid:"Tabloid",custom:"Custom"}},styleTooltip:"Style",colorTooltip:"Color",sizeTooltip:"Size"},textBoxProperty:{contents:{categoryName:"Content",content:"Content"},basicSettings:{categoryName:"Basic Settings",font:{categoryName:"Font",defaultStyle:"Default",normal:"Normal",italic:"Italic"},fontStyle:{categoryName:"Font Style",defaultStyle:"Default",normal:"Normal",thin:"Thin",extraLight:"ExtraLight",light:"Light",medium:"Medium",semiBold:"SemiBold",bold:"Bold",extraBold:"ExtraBold",heavy:"Heavy"},textDecoration:{categoryName:"Text Decoration",defaultStyle:"Default",none:"None",underline:"Underline",lineThrough:"LineThrough",overline:"Overline"},format:"Format"},alignment:{categoryName:"Alignment",textAlignment:{categoryName:"Text Alignment",defaultStyle:"Default",left:"Left",center:"Center",right:"Right"},verticalAlignment:{categoryName:"Vertical Alignment",defaultStyle:"Default",top:"Top",middle:"Middle",bottom:"Bottom"},lineSpacing:"Line Height"},appearance:{categoryName:"Appearance",borderTypes:{border:"Border",borderLeft:"Left",borderTop:"Top",borderRight:"Right",borderBottom:"Bottom"},borderStyles:{solid:"Solid",none:"None",double:"Double",dashed:"Dashed",dotted:"Dotted"},background:"Background Color"},link:"Link",linkReport:"Link Report",position:{categoryPosition:"Position",positionLabel:"Position",sizeLabel:"Size",left:"Left",top:"Top",width:"Width",height:"Height",direction:{categoryName:"Direction",leftToRight:"LeftToRight",rightToLeft:"RightToLeft"}},visibility:{categoryName:"Visibility",visible:"Visible",toggleItem:"Toggle Item",intialToggleState:"Initial Toggle State"},miscellaneous:{categoryName:"Miscellaneous",canGrow:"Can Grow",canShrink:"Can Shrink"},paragraphSettings:{categoryName:"Paragraph Settings",textAlignment:{categoryName:"Text Alignment",defaultStyle:"Default",left:"Left",center:"Center",right:"Right"},indent:{categoryName:"Indent",leftIndent:"Left",rightIndent:"Right"},space:{categoryName:"Space",topSpace:"Top",bottomSpace:"Bottom"},listLevel:{categoryName:"List Level",zeroLevel:"",oneLevel:"",twoLevel:"",threeLevel:"",fourLevel:""},listStyle:{categoryName:"List Style",none:"None",numbered:"Numbered",bulleted:"Bulleted"}},padding:{padding:"Padding",paddingLeft:"Left",paddingTop:"Top",paddingRight:"Right",paddingBottom:"Bottom"},contextMenu:{cut:"Cut",copy:"Copy",paste:"Paste",expression:"Expression",pasteAlert:"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+V keyboard shortcut instead of paste operation."},fontStyleTooltip:"Style",fontWeightTooltip:"Weight",fontSizeTooltip:"Size",fontColorTooltip:"Color",fontFamilyTooltip:"Font Family",styleTooltip:"Style",colorTooltip:"Color",sizeTooltip:"Size",selectedText:"Selected Text"},designPanel:{headerText:"Header",footerText:"Footer",pasteAlert:"Only basic item are supported in header and footer area",pasteTitle:"Paste"},customProperty:{position:{categoryPosition:"Position",positionLabel:"Position",left:"Left",top:"Top",sizeLabel:"Size",width:"Width",height:"Height"},appearance:{categoryAppearance:"Appearance",borderTypes:{border:"Border",borderLeft:"Left",borderTop:"Top",borderRight:"Right",borderBottom:"Bottom"},borderStyles:{solid:"Solid",none:"None",double:"Double",dashed:"Dashed",dotted:"Dotted"},backGround:"Background Color"},visibility:{categoryName:"Visibility",visible:"Visible"},styleTooltip:"Style",colorTooltip:"Color",sizeTooltip:"Size"},tablixProperty:{data:{categoryName:"Data",datasetName:"Dataset",datasetNone:"None"},appearance:{categoryName:"Appearance",borderTypes:{border:"Border",borderLeft:"Left",borderTop:"Top",borderRight:"Right",borderBottom:"Bottom"},borderStyles:{solid:"Solid",none:"None",double:"Double",dashed:"Dashed",dotted:"Dotted"},backGround:"Background Color"},miscellaneous:{categoryName:"Miscellaneous",noRowsMessage:"No Rows Message",pageName:"Page Name",keepTogether:"Keep Together",repeatColumnHeaders:"Repeat Column Headers",repeatRowHeaders:"Repeat Row Headers",fixedColumnHeaders:"Fixed Column Headers",fixedRowHeaders:"Fixed Row Headers"},font:{categoryName:"Font",defaultStyle:"Default",normal:"Normal",italic:"Italic"},fontStyle:{categoryName:"Font Style",defaultStyle:"Default",normal:"Normal",thin:"Thin",extraLight:"ExtraLight",light:"Light",medium:"Medium",semiBold:"SemiBold",bold:"Bold",extraBold:"ExtraBold",heavy:"Heavy"},textDecoration:{categoryName:"Text Decoration",defaultStyle:"Default",none:"None",underline:"Underline",lineThrough:"LineThrough",overline:"Overline"},alignment:{categoryName:"Alignment",textAlignment:{categoryName:"Text Alignment",defaultStyle:"Default",left:"Left",center:"Center",right:"Right"},verticalAlignment:{categoryName:"Vertical Alignment",defaultStyle:"Default",top:"Top",middle:"Middle",bottom:"Bottom"}},padding:{padding:"Padding",paddingLeft:"Left",paddingTop:"Top",paddingRight:"Right",paddingBottom:"Bottom"},position:{categoryPosition:"Position",positionLabel:"Position",left:"Left",top:"Top",sizeLabel:"Size",width:"Width",height:"Height"},visibility:{categoryName:"Visibility",visible:"Visible",toggleItem:"Toggle"},staticGroupProp:{categoryName:"Basic Settings",filters:"Filters",sorts:"Sorts",fixedData:"Fixed Data",groupExp:"Groups",hideIfNoRows:"Hide If No Rows",keepWithGroup:"Keep With Group",repeatOnNewPage:"Repeat On New Page",afterGroup:"After",beforeGroup:"Before",pageBreak:{categoryName:"Page Break",enablePageBreak:"Enable Page Break",breakLocation:{categoryName:"Break Location",none:"None",start:"Start",end:"End",startAndEnd:"StartAndEnd",between:"Between"},pageNumberReset:"Page Number Reset"}},fontStyleTooltip:"Style",fontWeightTooltip:"Weight",fontSizeTooltip:"Size",fontColorTooltip:"Color",fontFamilyTooltip:"Font Family",styleTooltip:"Style",colorTooltip:"Color",sizeTooltip:"Size",tablixMember:"Tablix Member"},rowColumnGroup:{rowGroupLable:"Row Groups",columnGroupLable:"Column Groups",tablixAlertHeader:"Tablix",alertMessage:"Enable the expand option to select the tablix report item",contextMenu:{addgroup:"Add Group",advanced:"Advanced",deletegroup:"Delete Group",addtotal:"Add Total",groupproperties:"Group Properties",addColumnGroup:"Add Column Group",addRowGroup:"Add Row Group"},contextSubMenu:{adjacentafter:"Adjacent After",adjacentbefore:"Adjacent Before",childgroup:"Child Group",parentgroup:"Parent Group",totalafter:"After",totalbefore:"Before",childGroupAlert:"Cannot insert group inside detail."}},tablixContextMenu:{rowMenu:{insertRow:"Insert Row",above:"Above",below:"Below"},columnMenu:{insertColumn:"Insert Column",left:"Left",right:"Right"},rowGroupMenu:{insideGroupAbove:"Inside Group - Above",insideGroupBelow:"Inside Group - Below",outsideGroupAbove:"Outside Group - Above",outsideGroupBelow:"Outside Group - Below"},columnGroupMenu:{insideGroupLeft:"Inside Group - Left",insideGroupRight:"Inside Group - Right",outsideGroupLeft:"Outside Group - Left",outsideGroupRight:"Outside Group - Right"},deleteRows:"Delete Rows",deleteColumns:"Delete Columns",rowVisibility:"Row Visibility",columnVisibility:"Column Visibility",tablixProperties:"Tablix Properties",splitcells:"Split Cells",mergecells:"Merge Cells",groupMenu:{adjacentAbove:"Adjacent Above",adjacentleft:"Adjacent Left",adjacentright:"Adjacent Right",adjacentBelow:"Adjacent Below",childGroup:"Child Group",parentGroup:"Parent Group",deleteRowGroup:"Delete Row Group",deleteColGroup:"Delete Column Group",addRowGroup:"Row Group",addColGroup:"Column Group"},reportItemMenu:{insertItem:"Insert",chart:"Chart"},totalMenu:{total:"Add Total",row:"Row",column:"Column",before:"Before",after:"After"},cellMenu:{addExpression:"Add Expression",editExpression:"Edit Expression",datasource:"Add Data Source",noFields:"No Fields",addText:"Add Text",editText:"Edit Text"},basicItems:{deleteItem:"Delete",cut:"Cut",copy:"Copy",paste:"Paste"}},tablixAlertDialog:{ok:"OK",cancel:"Cancel",closeToolTip:"Close",deleteRowTitle:"Delete Rows",deleteRow:"Delete rows only",deleteRowGroup:"Delete rows and associated groups",deleteRowContent:"Delete row options",deleteBodyRow:"Tablix body must contain at least one row.",deleteColumnTitle:"Delete Columns",deleteColumn:"Delete columns only",deleteColumnGroup:"Delete columns and associated groups",deleteColumnContent:"Delete column options",deleteBodyColumn:"Tablix body must contain at least one column.",deleteGroup:"Delete group only",deleteGroupRowColumn:"Delete group and related rows and columns",deleteGroupTitle:"Delete Group",deleteGroupContent:"Delete group options",deleteStructure:"Group structure not available.",removeRowAlert:"Failed to remove row in tablix report item",removeRow:"Remove Rows",removeColumn:"Remove Columns",addRow:"Add Row",addColumn:"Add Column",removeColumnAlert:"Failed to remove column in tablix report item",addRowAlert:"Failed to add row in tablix report item",addColumnAlert:"Failed to add column in tablix report item"},cellMergingAlertInfo:{merge:"Merge Cells",mergeAlert:"Failed to merge cells in tablix report item",split:"Split Cells",splitAlert:"Failed to split cells in tablix report item"},tablixAlertInfo:{addGroup:"Add Group",removeGroup:"Remove Group",adjacentAfterAlert:"Failed to add adjacent group in hierarchy structure",adjacentBeforeAlert:"Failed to add adjacent group in hierarchy structure",childGroupALert:"Failed to add child group in hierarchy structure",title:"Tablix ReportItem",parentGroupAlert:"Failed to add parent group in hierarchy structure",removeGroupAlert:"Failed to remove group in hierarchy structure",selectedMemberAlert:"Selected member is not a group member",pasteActionAlert:"The information cannot be posted because the copy area and the paste area are not the same size and shape.",pasteTitle:"Paste"},tablixGroup:{title:"Tablix Group",headerTxt:"Group Label",groupBy:"Group by:",chooseField:"Choose Field",showDetailData:"Show detail data",addGroupHeader:"Add header",addGroupFooter:"Add footer",ok:"OK",cancel:"Cancel",closeToolTip:"Close"},tablixDataAssignMenu:{datasource:"Add Data Source",addExpression:"Add Expression",editExpression:"Edit Expression",addText:"Add Text",editText:"Edit Text",search:"Search",noFieldsFound:"No Fields Found"},tablixTotalAlert:{totalHeader:"Add Total Header",totalStatic:"Add Total",headerMessage:"Failed to add total row or column to the group header in tablix report item",staticMessage:"Failed to add total row or column to the tablix body in tablix report item"},tablixAddTextDialog:{save:"Save",add:"Add",cancel:"Cancel",closeToolTip:"Close",addText:"Add Text",editText:"Edit Text"},queryDesigner:{storeParameter:{title:"Parameters",ok:"OK",cancel:"Cancel",parameterLable:"Parameter",nullLable:"Null",valueLable:"Value",dataTypeLable:"DataType",closeToolTip:"Close"},parameter:{title:"Parameters",ok:"OK",cancel:"Cancel",parameterLable:"Parameter",nullLable:"Null",valueLable:"Value",dataTypeLable:"DataType",auto:"Auto",text:"Text",closeToolTip:"Close"},filter:{title:"Query Filters",descriptionLable:"List of Table Filters",add:"ADD",save:"OK",cancel:"Cancel",nullLable:"Null",trueLable:"True",falseLable:"False",parameterTooltip:"Include as Parameter",closeToolTip:"Close",intOperatorType:{equals:"Equals",doesNotEqual:"Does Not Equal",greaterThan:"Greater Than",greaterThanOrEqual:"Greater Than or Equal To",lessThan:"Less Than",lessThanOrEqual:"Less Than or Equal To",between:"Between",notBetween:"Not Between"},stringOpertorType:{equals:"Equals",startsWith:"Starts With",endWith:"Ends With",contains:"Contains",notContains:"Not Contains"},errorMessage:{dateValidation:"Value is an invalid date format.",commonContent:"The filter on ",booleanValidation:" does not have any values to filter on. Please provide the values for the filter.",stringValidation:" does not have proper values to filter on. "}},previewArea:{dataPreview:"Data Preview",noRecords:"No records to display",generatePreview:"Generate Preview",executeRecords:"Execute to preview records",record:"Record",records:"Records",retrieved:"Retrieved",loadRecord:"Load More",update:"Update"},schemaArea:{search:"Search",matchesFound:"No matches found",rename:"Rename",aggregation:"Aggregation",dialogHeader:"DataSet",alertMessage:{datasourceAlert:"Select a DataSource to Configure Report DataSet",removeTable:"The below associated tables will remove with this",duplicateName:"The specified column name already exists",duplicateDatasetName:"The specified name already exists in DataSet list",datasetSpecialCharacter:"Name should not contain spaces and special characters",specialCharacter:"Column name should not contain special characters.",switcherAlert:"Switching to the visual designer will discard manual changes made to the query. Do you want to use the visual designer anyway?"},errorMessage:{specifyName:"Specify the column name",specifyDatasetName:"Specify the DataSet name",previewFailed:"DataSet failed to preview the selected table",specifyQuery:"Specify the DataSet query",selectTable:"Select the table to save the DataSet",queryFailed:"DataSet failed to save the Query of selected table",tableProcedure:"DataSet failed to retrieve the selected table procedure"}},toolBar:{datasourceLable:"DataSource",datasetName:"Name",run:"Run",join:"Join",expression:"Expression",filter:"Filter",code:"Code",finish:"Finish",cancel:"Cancel",parameter:"Parameter",datasourceWaterMark:"Select a DataSource",autoPreview:"Auto Preview"},joiner:{title:"Query Joiner",descriptionLable:"List of Table Relations",add:"ADD",save:"OK",cancel:"Cancel",closeToolTip:"Close",addField:"Add Field",leftTableWaterMark:"Left Table",rightTableWaterMark:"Right Table",leftFieldWaterMark:"Left Field",rightFieldWaterMark:"Right Field",operatorWaterMark:"Operator",joinTypeWaterMark:"Join Type",joinTypes:{inner:"Inner",outer:"Left Outer",rightOuter:"Right Outer",fullOuter:"Full Outer"},errorMessage:{removeField:"Each relation must have one field condition. So, it does not allow deleting this field",noRelationAlert:" is no relation with other tables",selectLeftTable:"Select the left table value",selectRightTable:"Select the right table value",selectRelation:"Select the relation for tables",selectLeftColumn:"Select the left column value of field row #",selectRightColumn:"Select the right column value of field row #",selectOperator:"Select the operator of field row #",relationExists:"Already relation exists between tables"}},credentialDialog:{title:"Credential Dialog",userName:"Username",password:"Password",userNameWaterMark:"Username",passwordWaterMark:"Password",userNameErrorMessage:"Please enter Username",passwordErrorMessage:"Please enter Password",connect:"Connect",close:"Close"},queryExpression:{title:"Query Expressions",functionLabel:"Functions",columnLabel:"Column Settings",expressionLabel:"Expression",nameLabel:"Name",descriptionLabel:"Description ",exampleLabelText:"Example",ok:"Save",cancel:"Cancel",add:"Add",textAreaWaterMark:"Query Expression",nameFieldWaterMark:"Expression Name",closeToolTip:"Close",errorMessage:{saveAlert:"Expression is not saved. Do you want to save and continue?",bracketSyntax:"Incorrect Syntax near open/close bracket(s).",parseAlert:"ReportDesigner failed to parse the specified expression.",nameAlert:"Name field should not be empty.",emptyAlert:"Expression field should not be empty",duplicateName:"The specified expression name already exists",specialCharacter:"Expression name should not contain special characters.",referenceError:"Column cannot be referred within its own expression!",invalidSyntax:"Invalid syntax near open/close bracket(s).",retrieveExpression:"ReportDesigner failed to retrieve the specified expression."},datasetTitle:"DataSet",expressions:{all:"All",numbers:"Numbers",logical:"Logical",conditional:"Conditional",date:"Date",stringType:"String",text:"Text",miscellenuous:"Miscellaneous ",abs:"Returns the absolute value of the given expression.",acos:"Returns the inverse cosine (also known as arccosine) of the given numeric expression.",asin:"Returns the inverse sine (also known as arcsine) of the given numeric expression.",atan:"Returns the inverse tangent (also known as arctangent) of the given numeric expression.",cos:"Returns the cosine of the angle specified in radians of the given expression.",degree:"Returns the angle in degrees for the one which specified in radians of the given numeric expression.",exponent:"Returns the exponential value of the given expression.",logrithm:"Returns the logarithm of the given expression to the specified base.",pi:"Returns the constant value of PI.",power:"Returns the value of the given expression (expression1) to the specified power (expression2).",radians:"Returns the angle in radians for the one which specified in degrees in the given numeric expression.",round:"Returns a rounded value.",sign:"Returns a value representing the positive (+1), zero (0), or negative (-1) sign of the given numeric expression.",sin:"Returns the sine of the angle specified in radians of the given expression.",squareRoot:"Returns the square root of the given numeric expression.",tan:"Returns the tangent of the given numeric expression.",ifCondition:"Returns either true part or false part, depending upon the evaluation of the expression.",ifNull:"If the expression is numeric/string/date, returns the first expression. If the first expression is NULL, returns the second expression.",isNotNull:"If the numeric/ string / date_expression is NULL, returns a string representing false; otherwise represents true.",isNull:"If the numeric/string/date_expression is NULL, returns a string representing true; otherwise represents false.",and:"Returns true if both the expressions evaluate to true.",notOperation:"Returns the reversal logical value of the expression being evaluated.",orOperation:"Returns true if any of the expressions evaluates to true.",addDate:"Adds the number of days to the specified date.",name:"Returns a string representing the specified datepart of the given date expression.",part:"Returns an integer value representing the specified date part of the given date expression.",sub:"Returns the date subtracted from the specified date.",day:"Returns a numeric value representing the day part of the specified date.",daydiff:"Returns a numeric value representing the difference between two specified dates.",hour:"Returns the hour of the given date as an integer.",minute:"Returns a numeric value representing the minute part of the date resulting from specified date expression.",month:"Returns a numeric value representing the month part of the date resulting from specified date expression.",now:"Returns the current date and time.",today:"Returns the current date.",year:"Returns a numeric value representing the year part of the date resulting from the specified date expression.",char:"Converts the given integer ASCII code into a character.",concat:"Returns a string value resulting from the concatenation of two or more string values.",contains:"Returns true if the given string expression contains the specified substring expression.",endsWith:"Returns true if the given string expression ends with the specified substring expression.",left:"Returns the specified number of characters from starting of the given string expression.",length:"Returns the natural logarithm of the given expression.",lower:"Returns a lower case converted string value from the given string expression.",leftTrim:"Returns the string value with leading blanks removed from string expression.",maximum:"Returns the maximum value in the given expression.",minimum:"Returns the minimum value in the given expression.",right:"Returns the specified number of characters from the end of the given string expression.",rightTrim:"Returns the string without right side trailing spaces in the given string.",startswith:"Returns true if the given string expressions starts with the specified substring expression.",subString:"Returns a specific length of string starting from specific index of the given string expression.",upper:"Returns an upper case converted string value from a given string expression."}},reportParameter:{title:"Parameters",descriptionText:"Report Parameters",addText:"ADD",ok:"OK",cancel:"Cancel",nameWaterMark:"Parameter Name",valueWaterMark:"Value",closeToolTip:"Close"}},chartItem:{categoryItems:{yvalue:"Y Value(s)",size:"Size(s)",xvalue:"X Value(s)",column:"Column",row:"Row(s)"},categoryItemsMenu:{filter:"Filters",sort:"Sorts",group:"Groups",expression:"Expression",aggregate:"Aggregate"}},codeDialog:{title:"Code Module",ok:"OK",cancel:"Cancel",add:"ADD",closeToolTip:"Close",reference:{title:"References",waterMark:"Reference",errorMessage:"The field is empty",headerText:"List of assembly references",infoTipText:"Add an assembly reference to utilize your assembly functions in report."},classes:{title:"Classes",classWaterMark:"Class Name",instanceWaterMark:"Instance Name",classErrorMessage:"The fields are empty",instanceErrorMessage:"The field is empty",headerText:"List of class instances",infoTipText:"Add class instances to access your object functions in report."},code:{title:"Code",headerText:"VB code function for report",infoTipText:"Syncfusion reporting engine supports VB code functions to integrate with report element and data."}},previewData:{title:"Preview Data",ok:"OK",cancel:"Cancel",description:"Bind JSON data for Preview",close:"Close",infoToolTip:"Report requires JSON format data to preview and it contains the key and value in the list of array format.",jsonHeader:"JSON Data :",errorMessage:"Specify the valid JSON format",previewDataAlert:{title:"Preview Data",alertMessage:"Are you want switch to report designer ?"}},sampleDataSource:{sampleDSHeader:"IMPORT SAMPLE DATA",addText:"Add",searchText:"Search",noDataFound:"No Data found.",welcomeContentPrefix:"Start by creating a data source",welcomeContentSuffix:"You can connect to your own custom data or can import one from the predefined shared data that we offer.",sampleDSText:"import sample data",exploreSampleText:"Explore Sample Data",accordionText:"Kickstart your first report and explore the customization options using the sample data.",errorMessage:"Network Error",alertHeaderText:"Import Data",alertMessage:"ReportDesigner failed to import the data from the ReportServer"},field:{title:"Fields",nameWaterMark:"Field Name",sourceWaterMark:"Field Source",ok:"OK",cancel:"Cancel",description:"Change query and calculated fields",query:"Query Field",calculated:"Calculated Field",fieldError:"Field is empty",fieldsError:"Fields are empty",add:"ADD",closeToolTip:"Close",invalidFormat:"Field name should not contain spaces and special characters",sameFieldName:"Field Name already exists"},commonProperty:{commonProperties:"Common Properties",basicSettings:{categoryBasicSettings:"Basic Settings",borderTypes:{border:"Border",borderLeft:"Left",borderTop:"Top",borderRight:"Right",borderBottom:"Bottom"},borderStyles:{solid:"Solid",none:"None",double:"Double",dashed:"Dashed",dotted:"Dotted"},backGround:"Background Color",styleTooltip:"Style",colorTooltip:"Color",sizeTooltip:"Size"},position:{categoryPosition:"Position",positionLabel:"Position",left:"Left",top:"Top"},visibility:{categoryVisibility:"Visibility",visible:"Visible"}}};
