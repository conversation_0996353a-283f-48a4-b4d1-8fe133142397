/*
 * Skin: Purple
 * ------------
 */
.skin-purple-light .main-header .navbar {
  background-color: #605ca8;
}
.skin-purple-light .main-header .navbar .nav > li > a {
  color: #ffffff;
}
.skin-purple-light .main-header .navbar .nav > li > a:hover,
.skin-purple-light .main-header .navbar .nav > li > a:active,
.skin-purple-light .main-header .navbar .nav > li > a:focus,
.skin-purple-light .main-header .navbar .nav .open > a,
.skin-purple-light .main-header .navbar .nav .open > a:hover,
.skin-purple-light .main-header .navbar .nav .open > a:focus,
.skin-purple-light .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.1);
  color: #f6f6f6;
}
.skin-purple-light .main-header .navbar .sidebar-toggle {
  color: #ffffff;
}
.skin-purple-light .main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: rgba(0, 0, 0, 0.1);
}
.skin-purple-light .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-purple-light .main-header .navbar .sidebar-toggle:hover {
  background-color: #555299;
}
@media (max-width: 767px) {
  .skin-purple-light .main-header .navbar .dropdown-menu li.divider {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .skin-purple-light .main-header .navbar .dropdown-menu li a {
    color: #fff;
  }
  .skin-purple-light .main-header .navbar .dropdown-menu li a:hover {
    background: #555299;
  }
}
.skin-purple-light .main-header .logo {
  background-color: #605ca8;
  color: #ffffff;
  border-bottom: 0 solid transparent;
}
.skin-purple-light .main-header .logo:hover {
  background-color: #5d59a6;
}
.skin-purple-light .main-header li.user-header {
  background-color: #605ca8;
}
.skin-purple-light .content-header {
  background: transparent;
}
.skin-purple-light .wrapper,
.skin-purple-light .main-sidebar,
.skin-purple-light .left-side {
  background-color: #f9fafc;
}
.skin-purple-light .main-sidebar {
  border-right: 1px solid #d2d6de;
}
.skin-purple-light .user-panel > .info,
.skin-purple-light .user-panel > .info > a {
  color: #444444;
}
.skin-purple-light .sidebar-menu > li {
  -webkit-transition: border-left-color 0.3s ease;
  -o-transition: border-left-color 0.3s ease;
  transition: border-left-color 0.3s ease;
}
.skin-purple-light .sidebar-menu > li.header {
  color: #848484;
  background: #f9fafc;
}
.skin-purple-light .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  font-weight: 600;
}
.skin-purple-light .sidebar-menu > li:hover > a,
.skin-purple-light .sidebar-menu > li.active > a {
  color: #000000;
  background: #f4f4f5;
}
.skin-purple-light .sidebar-menu > li.active {
  border-left-color: #605ca8;
}
.skin-purple-light .sidebar-menu > li.active > a {
  font-weight: 600;
}
.skin-purple-light .sidebar-menu > li > .treeview-menu {
  background: #f4f4f5;
}
.skin-purple-light .sidebar a {
  color: #444444;
}
.skin-purple-light .sidebar a:hover {
  text-decoration: none;
}
.skin-purple-light .sidebar-menu .treeview-menu > li > a {
  color: #777777;
}
.skin-purple-light .sidebar-menu .treeview-menu > li.active > a,
.skin-purple-light .sidebar-menu .treeview-menu > li > a:hover {
  color: #000000;
}
.skin-purple-light .sidebar-menu .treeview-menu > li.active > a {
  font-weight: 600;
}
.skin-purple-light .sidebar-form {
  border-radius: 3px;
  border: 1px solid #d2d6de;
  margin: 10px 10px;
}
.skin-purple-light .sidebar-form input[type="text"],
.skin-purple-light .sidebar-form .btn {
  box-shadow: none;
  background-color: #fff;
  border: 1px solid transparent;
  height: 35px;
}
.skin-purple-light .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-purple-light .sidebar-form input[type="text"]:focus,
.skin-purple-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-purple-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-purple-light .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
@media (min-width: 768px) {
  .skin-purple-light.sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {
    border-left: 1px solid #d2d6de;
  }
}
