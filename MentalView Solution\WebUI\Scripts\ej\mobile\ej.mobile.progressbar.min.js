/*!
*  filename: ej.mobile.progressbar.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function(n,t,i){t.widget("ejmProgress","ej.mobile.Progress",{_setFirst:!0,_rootCSS:"e-m-progress",defaults:{renderMode:"auto",value:0,percentage:0,incrementStep:0,minValue:0,maxValue:100,width:null,height:null,orientation:"horizontal",create:null,change:null,start:null,complete:null,text:"",enabled:!0,enableCustomText:!1,theme:"auto",enablePersistence:!1},dataTypes:{renderMode:"enum",orientation:"enum",enabled:"boolean",enableCustomText:"boolean",theme:"enum",enablePersistence:"boolean"},_init:function(){App.activePage||App.createAppView();this._getLocalizedLabels();this.model.text=t.isNullOrUndefined(this.model.text)?this._localizedLabels.text:this.model.text;this._cloneElement=this.element.clone();this._createDelegate();this._wireEvents(!1);this._renderControl();this._progressbarInit();this._pageLoad()},_getLocalizedLabels:function(){this._localizedLabels=t.getLocalizedConstants(this.sfType,this.model.locale)},_renderControl:function(){t.setRenderMode(this);t.setTheme(this);var n=t.buildTag("canvas");this.element.append(n)},_createDelegate:function(){this._onOrientationChangedDelegate=n.proxy(this._onOrientationChangedHandler,this);this._onWindowsResizeDelegate=n.proxy(this._onWindowResizeHandler,this)},_wireEvents:function(n){n||t.listenEvents([window],["onorientationchange"in window?"orientationchange":"resize"],[this._onWindowsResizeDelegate],n)},_onWindowResizeHandler:function(){var n=this;window.setTimeout(function(){n._draw()},250)},_widthAndHeightProperties:function(){this._tempHeight=this.model.orientation=="horizontal"?this.model.height?this.model.height:3:this.model.width?this.model.width:this.element.parent().height();this._tempWidth=this.model.orientation=="horizontal"?this.model.width?this.model.width:this.element.parent().width():this.model.height?this.model.height:3},_progressbarInit:function(){this._initialX=0;this._initialY=0;this._mProgress=this.element;this._increment=0;this._result=0;this._context=null;this._text=this.model.textStyle;this._externalText=null;this._totalWidth=0;this._totalHeight=0;this._radius=0;this._currentValue=1},_pageLoad:function(){var t=this.element.find("canvas")[0],r=n(t).parent(),i;(typeof G_vmlCanvasManager!="undefined"&&(t=window.G_vmlCanvasManager.initElement(t)),t&&t.getContext)&&(t=this.element.find("canvas")[0],this._context=t.getContext("2d"),this._context)&&(this.model.value=this.model.value+this.model.incrementStep,this._validate(),i={element:this.element,value:this.model.value},this._result=this._draw(),i={value:this._setValue,percentage:this.model.percentage},this.model.minValue==this.model.value&&this._trigger("start",i))},_validate:function(){if(this._setValue==this.model.maxValue){var n={value:this._setValue,text:this.model.text,percentage:this.model.percentage};this.model.complete&&this._trigger("complete",n)}this._setValue=this._setValue<this.model.minValue?this.model.minValue:this._setValue>this.model.maxValue?this.model.maxValue:this._setValue;this._step>this._max&&(this._step=1)},_draw:function(){if(this._widthAndHeightProperties(),this._context!=null){this.model.enabled||(n(this.element).addClass("e-m-state-disabled"),this._wireEvents(!0));this.model.value<=0&&this.model.percentage!=0?this.model.value=Math.round(this.model.percentage*this.model.maxValue/100):this.model.value!=0&&this.model.percentage<=0?this.model.percentage=Math.round(this.model.value/this.model.maxValue*100):this.model.value!=0&&this.model.percentage!=0&&(this.model.percentage=Math.round(this.model.value/this.model.maxValue*100));this.model.minValue<=this.model.value&&this.model.maxValue>=this.model.value?(this._cutOffValue=this.model.value-this.model.minValue,this.model.percentage>=100?this.model.percentage=100:this.model.percentage<=0&&(this.model.percentage=0),this._setValue=this.model.percentage!=i?this.model.percentage:Math.round(100/(this.model.maxValue-this.model.minValue)*this._cutOffValue)):this.model.minValue>this.model.value&&this.model.maxValue>this.model.value?this._setValue=0:this.model.minValue<this.model.value&&this.model.maxValue<this.model.value&&(this._setValue=100);this._setOrientationProperties(this._tempWidth,this._tempHeight,this._tempHeight+50,this._tempWidth+50);this._context.translate(.5,.5);this._context.clearRect(this._initialX-5,this._initialY-5,this._totalWidth+15,this._totalHeight+15);var t;t=this.model.renderMode=="windows"||this.model.renderMode=="flat"?10:25;this._initialY=this.model.orientation=="horizontal"?t:0;this._initialX=this.model.orientation=="horizontal"?0:t;this.model.renderMode=="ios7"?(this.model.orientation=="horizontal"?this._totalHeight=this._tempHeight==""?3:this._tempHeight:this._totalWidth=this._tempWidth==""?3:this._tempWidth,this.totalRadius=this.model.orientation=="horizontal"?Math.round(this._totalHeight/2):Math.round(this._totalWidth/2),this._fillRadius=this.totalRadius):(this.model.orientation=="horizontal"?this._totalHeight=this._tempHeight==""?2:this._tempHeight:this._totalWidth=this._tempWidth==""?2:this._tempWidth,this.totalRadius=0,this._fillRadius=0);this.model.orientation=="horizontal"?this._roundRectOuter(this._context,this._initialX,this._initialY,this._totalWidth-15,this._totalHeight,this.totalRadius):this._roundRectOuter(this._context,this._initialX,this._initialY,this._totalWidth,this._totalHeight-15,this.totalRadius);this.model.orientation=="horizontal"?this._roundRectFill(this._context,this._initialX,this._initialY,this._increment,this._totalHeight,this._fillRadius):this._roundRectFill(this._context,this._initialX,this._initialY,this._increment,this._totalHeight-15,this._fillRadius);this._increment>=this._totalWidth&&window.clearInterval(this._result)}this._validate()},_setOrientationProperties:function(n,t,i,r){this._totalWidth=n;this._totalHeight=t;this._radius=(this.model.orientation=="horizontal"?this._totalHeight:this._totalWidth)/2;this._increment=this._setValue*(this.model.orientation=="horizontal"?(this._totalWidth-15)/100:(this._totalHeight-15)/100);this._context.canvas.width=this.model.orientation=="horizontal"?n:r;this._context.canvas.height=this.model.orientation=="horizontal"?i:t},_setLocale:function(){this._getLocalizedLabels();this._setText(this._localizedLabels.text)},_roundRect:function(n,t,i,r,u,f){n.save();n.beginPath();n.strokeStyle=this._strokeStyle;n.fillStyle=this._fillStyle;n.lineWidth=1;n.moveTo(t+f,i);n.lineTo(t+r-f,i);n.quadraticCurveTo(t+r,i,t+r,i+f);n.lineTo(t+r,i+u-f);n.quadraticCurveTo(t+r,i+u,t+r-f,i+u);n.lineTo(t+f,i+u);n.quadraticCurveTo(t,i+u,t,i+u-f);n.lineTo(t,i+f);n.quadraticCurveTo(t,i,t+f,i);n.fill();n.stroke();n.closePath();n.restore()},_roundRectOuter:function(n,i,r,u,f,e){if(this.model.renderMode=="ios7"){var o=this.model.theme=="dark"?[{ColorStop:1,Color:"#363636"}]:[{ColorStop:1,Color:"#b5b5b6"}];grd=this.model.orientation=="horizontal"?n.createLinearGradient(i,r,i,r+f):n.createLinearGradient(i,r,i+u,r);t._setGradientColor(this,grd,o);this._strokeStyle=grd;this._fillStyle=grd}else this.model.renderMode=="android"?this._setRectOuterProperties("#3A3A3A","#A9A9A9"):this.model.renderMode=="windows"?this._setRectOuterProperties("#373737","#c5c5c5"):this.model.renderMode=="flat"&&this._setRectOuterProperties("#b3b3b3","#b3b3b3");this.model.orientation=="horizontal"?this._roundRect(n,i,r,u+e,f,e):this._roundRect(n,i+5,r,u,f+e,e)},_setRectOuterProperties:function(n,t){this.model.theme=="dark"?(this._strokeStyle=n,this._fillStyle=n):(this._strokeStyle=t,this._fillStyle=t)},_roundRectFill:function(n,r,u,f,e,o){var s,h,c;this.model.renderMode=="ios7"?(s=[{ColorStop:0,Color:"#017aff"}],grd=this.model.orientation=="horizontal"?n.createLinearGradient(r,u,r,u+e):n.createLinearGradient(r,u,r+f,u),t._setGradientColor(this,grd,s),this._strokeStyle=grd,this._fillStyle=grd):this.model.renderMode=="android"?this._strokeStyle=this._fillStyle="#33B5E5":this.model.renderMode=="windows"?this._strokeStyle=this._fillStyle="#2ca1dd":this.model.renderMode=="flat"&&(this._strokeStyle=this._fillStyle="#f48b22");f>0&&(this.model.orientation=="horizontal"?this._roundRect(n,r,u,f+o,e,o):this._roundRect(n,r+5,e-f+u,this._totalWidth,f+o,o));n.save();this.model.orientation=="horizontal"?this._setTextProperties():(this._setTextProperties(),h=270,n.save(),n.translate(this._totalWidth/2,e/2),n.rotate(h*2*3.1416/360),this.model.renderMode=="ios7"?n.textAlign="center":this.model.renderMode=="android"&&(n.textAlign="right"));this.model.enableCustomText?this.model.text==""||this.model.text==i?this.setCustomText(this._text):this.setCustomText(this.model.text):this.setCustomText(this._text);c={value:this._setValue,text:this.model.text,percentage:this.model.percentage};this.model.change&&this.model.value>0&&this._trigger("change",c);n.restore()},_setTextProperties:function(){this.model.renderMode=="ios7"?this._text="Downloading "+this._setValue+" of 100%":this.model.renderMode=="android"?this._text=this._setValue+"%":(this.model.renderMode=="windows"||this.model.renderMode=="flat")&&(this._text=this._setValue)},_setCustomtextProperties:function(n,t,i,r){n.fillStyle=this.model.theme=="dark"?t:i;n.font=r},_setRenderMode:function(n){this.model.enabled&&(this.model.renderMode=n,n=="auto"&&t.setRenderMode(this),this._draw())},_setTheme:function(n){this.model.enabled&&(this.model.theme=n,this._draw())},_value:function(n){this.model.enabled&&(this._setValue=n,this.model.percentage=Math.round(this.model.value*this.model.maxValue/100),this._draw())},_setPercentage:function(n){this.model.enabled&&(this._value=n,this._draw())},_disable:function(){this.model.enabled=!1;this.element.addClass("e-m-state-disabled");this._wireEvents(!0)},_enable:function(){this.model.enabled=!0;this.element.removeClass("e-m-state-disabled");this._wireEvents(!1)},_refresh:function(){this._clearElements();this._wireEvents(!1);this._renderControl();this._progressbarInit();this._pageLoad()},_setWidth:function(n){this._tempWidth=this.model.width=n;this._draw()},_setHeight:function(n){this._tempHeight=this.model.height=n;this._draw()},_setText:function(n){this.model.enabled&&(this.model.text=n,this._draw())},_setModel:function(n){for(var t in n)switch(t){case"renderMode":this._setRenderMode(n.renderMode);break;case"theme":this.model.enabled?this._setTheme(n.theme):"";break;case"width":this.model.enabled?this._setWidth(n.width):"";break;case"height":this.model.enabled?this._setHeight(n.height):"";break;case"orientation":this.model.enabled?this._draw():"";break;case"value":this.model.enabled?this._value(n.value):"";break;case"percentage":this.model.enabled?this._setPercentage(n.percentage):"";break;case"minValue":this.model.enabled?this._draw():"";break;case"maxValue":this.model.enabled?this._draw():"";break;case"enabled":n.enabled?this._enable():this._disable();break;case"text":this.model.enabled?this._setText(n.text):""}},_clearElements:function(){this._wireEvents(!0);this.element.removeAttr("class");this.element.removeClass("e-m-progress");this.element.html("")},_destroy:function(){this._cloneElement.insertBefore(this.element).removeClass("e-m-progress");this.element.remove()},getValue:function(){return this._setValue},getPercentage:function(){return this.model.percentage},setCustomText:function(n){var t=this._context,u=this._totalHeight,l=this.totalRadius,f=this._totalWidth,e,r,c,s,h,o;this.model.text=n;c=270;this.model.renderMode=="ios7"?(e=this.model.orientation=="horizontal"?0:8,r=this.model.orientation=="horizontal"?8:0):this.model.renderMode=="android"?r=15:(this.model.renderMode=="windows"||this.model.renderMode=="flat")&&(e=0,r=30);t.save();this.model.renderMode=="ios7"?this._setCustomtextProperties(t,"#fefefe","#000000","14px Helvetica"):this.model.renderMode=="android"?this._setCustomtextProperties(t,"#fefefe","#333333","17px Roboto"):this.model.renderMode=="windows"?this._setCustomtextProperties(t,"#ffffff","#000000","17px Segoe UI"):this.model.renderMode=="flat"&&this._setCustomtextProperties(t,"#333333","#333333","17px Segoe UI");s=t.measureText(this.model.text).width;h=t.measureText(this._setValue+"%").width;this.model.orientation=="horizontal"?(o=e+f/2-s/2,this.model.renderMode=="ios7"?t.fillText(this.model.text,o,r+5):this.model.renderMode=="android"?this.model.text!=""&&this.model.text!=i?t.fillText(this.model.text,f-s-15,r):t.fillText(this.model.text,f-h-15,r):(this.model.renderMode=="windows"||this.model.renderMode=="flat")&&t.fillText(this.model.text,e,u+r)):(o=r+15,this.model.renderMode=="ios7"?t.fillText(this.model.text,r,o):this.model.renderMode=="android"?(this.model.text!=""&&this.model.text!=i?t.fillText(this.model.text,(u-15)/2,15):t.fillText(this.model.text,(u-15)/2,r),t.textAlign="right"):(this.model.renderMode=="windows"||this.model.renderMode=="flat")&&t.fillText(this.model.text,-(u-15)/2,r+f));t.restore()},changeEventHandler:function(){var n={element:this.element,value:this._setValue,percentage:this._value};this.model.change&&this._trigger("change",n)}});t.mobile.Progress.Orientation={Horizontal:"horizontal",Vertical:"vertical"};t.mobile.Progress.Locale=t.mobile.Progress.Locale||{};t.mobile.Progress.Locale["default"]=t.mobile.Progress.Locale["en-US"]={text:""}})(jQuery,Syncfusion)});
