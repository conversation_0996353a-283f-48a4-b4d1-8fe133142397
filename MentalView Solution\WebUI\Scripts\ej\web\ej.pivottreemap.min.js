/*!
*  filename: ej.pivottreemap.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.globalize.min","./../common/ej.core.min","./../common/ej.data.min","./../common/ej.touch.min","./ej.waitingpopup.min","./../datavisualization/ej.treemap.min","./ej.pivotanalysis.base.min","./ej.olap.base.min","./ej.pivot.common.min"],n):n()})(function(){(function(n,t,i){t.widget("ejPivotTreeMap","ej.PivotTreeMap",{_rootCSS:"e-pivottreemap",element:null,model:null,validTags:["div","span"],defaults:n.extend(t.datavisualization.TreeMap.prototype.defaults,{url:"",cssClass:"",currentReport:"",operationalMode:"clientmode",customObject:{},isResponsive:!1,enableXHRCredentials:!1,dataSource:{data:null,isFormattedValues:!1,columns:[],cube:"",catalog:"",rows:[],values:[],filters:[]},serviceMethodSettings:{initialize:"InitializeTreeMap",drillDown:"DrillTreeMap"},locale:"en-US",drillSuccess:null,beforeServiceInvoke:null,afterServiceInvoke:null,load:null,renderComplete:null,renderFailure:null,renderSuccess:null,beforePivotEnginePopulate:null}),dataTypes:{dataSource:{data:"data",columns:"array",rows:"array",values:"array",filters:"array"},serviceMethodSettings:{initialize:"enum",drillDown:"enum"},customObject:"data"},locale:t.util.valueFunction("locale"),getOlapReport:function(){return this._olapReport},setOlapReport:function(n){this._olapReport=n},getJsonRecords:function(){return this._JSONRecords},setJsonRecords:function(n){this._JSONRecords=JSON.parse(n)},_init:function(){this._initPrivateProperties();this._load()},_destroy:function(){this.element.empty().removeClass("e-pivottreemap"+this.model.cssClass);this._waitingPopup!=i&&this._waitingPopup.destroy();this.element.attr("class")==""&&this.element.removeAttr("class")},_initPrivateProperties:function(){this._id=this.element.attr("id");this._olapReport="";this._JSONRecords=null;this._treeMapDatasource=[];this._currentAction="initialize";this._selectedItem="";this._selectedTagInfo=null;this._tagCollection=[];this._drilledMembers=[];this._startDrilldown=!1;this._isDrilled=!1;this._treeMap=null;this._pivotClientObj=null;this._waitingPopup=null;this._drillText="";this._showDrillText="";this._isOnlyMeasureElement=!1},_load:function(){this.element.addClass(this.model.cssClass);n(this.element).parents(".e-pivotclient").length>0?(this._pivotClientObj=n(this.element).parents(".e-pivotclient").data("ejPivotClient"),this.model.customObject=this._pivotClientObj.model.customObject,n("#"+this._pivotClientObj._id+"_maxView")[0]?n("#"+this._pivotClientObj._id+"_maxView").ejWaitingPopup({showOnInit:!0}):t.isNullOrUndefined(this._pivotClientObj._waitingPopup)||this._pivotClientObj._waitingPopup.show()):(n("#"+this._id).parent()[0]&&(n("#"+this._id).parent()[0].style.position="relative",this.element.ejWaitingPopup({showOnInit:!0,appendTo:n("#"+this._id).parent()[0]})),this._waitingPopup=this.element.data("ejWaitingPopup"),this._waitingPopup.show());var r=JSON.stringify(this.model.customObject);if(this.model.dataSource.data==null&&this.model.url==""&&this.model.dataSource.cube==""){this.renderTreeMapFromJSON(null);this._waitingPopup.hide();return}this.model.dataSource.data==null&&this.model.url!=""||this.model.dataSource.data!=null&&this.model.url!=""&&this.model.operationalMode==t.PivotTreeMap.OperationalMode.ServerMode?(this.model.operationalMode=t.PivotTreeMap.OperationalMode.ServerMode,this.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:this._currentAction,element:this.element,customObject:r}),this.model.customObject!=""&&this.model.customObject!=null&&this.model.customObject!=i?this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.initialize,JSON.stringify({action:"initialize",currentReport:this.model.currentReport,customObject:r}),this.renderControlSuccess):this.doAjaxPost("POST",this.model.url+"/"+this.model.serviceMethodSettings.initialize,JSON.stringify({action:"initialize"}),this.renderControlSuccess)):(this.model.operationalMode=t.PivotTreeMap.OperationalMode.ClientMode,this.model.dataSource.rows.length>1&&(this.model.dataSource.rows=[this.model.dataSource.rows[this.model.dataSource.rows.length-1]]),this.model.dataSource.cube!=""&&(this._trigger("beforePivotEnginePopulate",{treeMapObject:this}),this._dataTrans(this.model.dataSource),this.setOlapReport(this.model.dataSource),t.olap.base.getJSONData({action:"initialLoad"},this.model.dataSource,this)))},_setFirst:!1,_setModel:function(n){for(var t in n)switch(t){case"operationalMode":this.model.operationalMode=n[t];break;case"analysisMode":this.model.analysisMode=n[t];break;case"customObject":this.model.customObject=n[t];break;case"isResponsive":this.model.isResponsive=n[t];this._load();break;case"locale":this.model.locale=n[t];this._load()}},generateJSON:function(n,i){for(var d,a,c,ut,v,g,e,u,f,o,ft=this,nt=[],tt=[],p="",k=[],y=[],s=[],w=[],l={},r=0;r<this.model.dataSource.values[0].measures.length;r++)p+=p==""?this.model.dataSource.values[0].measures[r].fieldCaption:"~"+this.model.dataSource.values[0].measures[r].fieldCaption;var it=this.model.dataSource.values[0].axis,rt=this.model.dataSource.rows.length,h=i[0][0].ColSpan,b=0;for(h-rt<=0?(h=0,b=rt):(b=it=="rows"?h-1:h,h=b-1),u=[],r=0;r<i.length;r++)for(f=0;f<i[r].length;f++)if(d=null,i[r][f].CSS==" value"&&w.push(i[r][f].Value),i[r][f].CSS=="rowheader"&&i[r][f].Value!=""){if(o={},d=i[r][f].Info+"::"+i[r][f].State,tt.push(i[r][f].Info.replace(/&/g,"&amp;")),nt.push(d),u.length==0&&i[r][f].Info.split(".")[0].indexOf("Measures")<0&&(o.level=r,o.index=f,o.label=i[r][f].Value,u.push(o)),u.length!=0&&i[r][f].Info.split(".")[0].indexOf("Measures")<0)for(e=0;e<u.length;e++)if(!t.isNullOrUndefined(u[e]))if(u[e].index==f&&u[e].level!=r){u[e].level=r;u[e].label=u[e].label+"~~"+i[r][f].Value;break}else if(u[e].level==r){for(a=!1,c=0;c<u.length;c++)if(!t.isNullOrUndefined(u[c])&&u[c].index==f){a=!0;break}if(!a&&i[r][f].Info.split(".")[0].indexOf("Measures")<0){o.level=r;o.index=f;o.label=i[r][f].Value;u.push(o);break}}}else if(i[r][f].CSS=="summary"&&u.length!=0)for(e=0;e<u.length;e++)if(!t.isNullOrUndefined(u[e])&&u[e].index==f){delete u[e];break}for(e=0;e<u.length;e++)t.isNullOrUndefined(u[e])||b==u[e].label.split("~~").length&&s.push(u[e].label);for(u=[],r=0;r<i.length;r++)for(f=0;f<i[r].length;f++)if(i[r][f].CSS=="colheader"&&i[r][f].Value!=""){if(o={},u.length==0&&(o.level=f,o.index=r,o.label=this._isOnlyMeasureElement?"Total":i[r][f].Value,u.push(o)),u.length!=0)for(e=0;e<u.length;e++)if(!t.isNullOrUndefined(u[e]))if(u[e].index==r&&u[e].level!=f&&i[r][f].Info.split(".")[0].indexOf("Measures")<0){u[e].index=r;u[e].label=u[e].label+"~~"+i[r][f].Value;break}else if(u[e].level==f&&i[r][f].Info.split(".")[0].indexOf("Measures")<0||this._isOnlyMeasureElement){for(a=!0,c=0;c<u.length;c++)if(!t.isNullOrUndefined(u[c])&&(a=!1,u[c].index==r)){a=!0;break}if(!a&&(i[r][f].Info.split(".")[0].indexOf("Measures")<0||this._isOnlyMeasureElement)){o.level=f;o.index=r;o.label=this._isOnlyMeasureElement?"Total":i[r][f].Value;u.push(o);break}}}else if(i[r][f].CSS=="summary"&&u.length!=0)for(e=0;e<u.length;e++)if(!t.isNullOrUndefined(u[e])&&u[e].index==r){delete u[e];break}for(e=0;e<u.length;e++)t.isNullOrUndefined(u[e])||y.push(u[e].label);if(p!=""&&it==t.olap.AxisName.Column)for(r=0;r<y.length;r++)y[r]=y[r]+"~"+p;if(h==0)for(ut=w.length/y.length,r=0;r<s.length;r++){for(u=[],f=r;f<w.length;f=f+ut)o={},o.Item1=s[r].indexOf("~~")>=0?s[r].split("~~")[0]:s[r],o.Item2=w[f],u.push(o);k.push(u)}else{for(v=[],g=null,r=0;r<s.length;r++)for(u=[],f=0;f<i[h].length;f++)if(i[h][f].CSS=="rowheader"&&i[h][f].Value!=""&&s[r].split("~~")[s[r].split("~~").length-1]==i[h][f].Value){g=f;u.push(g);u.push(s[r]);v.push(u);break}for(e=0;e<v.length;e++){for(u=[],r=0;r<i.length;r++)for(f=v[e][0];f<i[r].length;f++){i[r][f].CSS==" value"&&(o={},o.Item1=v[e][1].indexOf("~~")>=0?v[e][1].split("~~")[0]:v[e][1],o.Item2=i[r][f].Value,u.push(o));break}u.length!=0&&k.push(u)}}l.labelTags=nt;l.drillTags=tt;l.measureNames=p;l.points_Y=k;l.seriesNames=y;l.treemapLabels=s;ft._JSONRecords=l;this.renderTreeMapFromJSON(l);this._unWireEvents();this._wireEvents()},_dataTrans:function(n){if(n.columns.length==0&&n.rows.length>0){var i=!1;n.values.length>0&&n.values[0].measures.length>0&&(n.values[0].axis==t.olap.AxisName.Row&&(n.values[0].axis=t.olap.AxisName.Column),i=this._isOnlyMeasureElement=!0)}},_wireEvents:function(){n(window).on("resize",n.proxy(this._reSizeHandler,this));this._on(this.element,"click",".drillItem",this._drillTreeMap)},_drillTreeMap:function(r){var u=t.isNullOrUndefined(this.model.ptreemapProxy)?this:this.model.ptreemapProxy,f=null,o,e,s,h;if(r.type=="treeMapGroupSelected"){if(u._treemapWaitingPopup({show:!0}),r.selectedGroups.length==0){u._treemapWaitingPopup({show:!1});return}for(u._currentAction="drilldown",u._selectedItem=r.selectedGroups[0].header,o=0;o<u._treeMapDatasource.length;o++)if(u._treeMapDatasource[o].Tag.split("::")[2]==u._selectedItem){if(u._selectedTagInfo=u._treeMapDatasource[o].Tag,u.model.operationalMode==t.PivotTreeMap.OperationalMode.ClientMode&&(f=u._treeMapDatasource[o].drillTag),u._selectedTagInfo.split("::")[u._selectedTagInfo.split("::").length-1]!=2){u._treeMap.refresh();u._treemapWaitingPopup({show:!1});return}break}u._startDrilldown=!0;u.model.operationalMode==t.PivotTreeMap.OperationalMode.ServerMode?(u.model.beforeServiceInvoke!=null&&u._trigger("beforeServiceInvoke",{action:u._currentAction,element:u.element,customObject:u.model.customObject}),s=JSON.stringify(u.model.customObject),u.model.customObject!=""&&u.model.customObject!=null&&u.model.customObject!=i?t.isNullOrUndefined(u._pivotClientObj)?u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.drillDown,JSON.stringify({action:"drilldown",drillInfo:u._selectedTagInfo,olapReport:u.getOlapReport(),customObject:s}),u.renderControlSuccess):u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.drillDown,JSON.stringify({action:"drilldown",drillInfo:u._selectedTagInfo,olapReport:u.getOlapReport(),clientReports:u._pivotClientObj.reports,customObject:s}),u.renderControlSuccess):t.isNullOrUndefined(u._pivotClientObj)?u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.drillDown,JSON.stringify({action:"drilldown",drillInfo:u._selectedTagInfo,olapReport:u.getOlapReport(),clientReports:u._pivotClientObj.reports}),u.renderControlSuccess):u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.drillDown,JSON.stringify({action:"drilldown",drillInfo:u._selectedTagInfo,olapReport:u.getOlapReport()}),u.renderControlSuccess)):(u._drilledMembers.push(f),t.olap._mdxParser.updateDrilledReport({uniqueName:f,seriesInfo:[f],uniqueNameArray:u._drilledMembers},"rowheader",u))}else{if(u._treemapWaitingPopup({show:!0}),u._currentAction="drillup",n(r.target).hasClass("drillItem"))if(u.getJsonRecords().treemapLabels[0].indexOf("~~")>=0){for(u._selectedItem=n(r.target).text(),e=0;e<u.getJsonRecords().labelTags.length;e++)if(u.getJsonRecords().labelTags[e].split("::")[2]==u._selectedItem&&u.getJsonRecords().labelTags[e].split("::")[u.getJsonRecords().labelTags[e].split("::").length-1]==1){if(u._selectedTagInfo=u.getJsonRecords().labelTags[e],u.model.operationalMode==t.PivotTreeMap.OperationalMode.ClientMode&&(f=u.getJsonRecords().drillTags[e]),u._selectedTagInfo.split("::")[u._selectedTagInfo.split("::").length-1]!=1){u._treemapWaitingPopup({show:!1});return}break}}else{u._treemapWaitingPopup({show:!1});return}u._tagCollection.length==0&&(u._isDrilled=!1);u._startDrilldown=!0;u.model.operationalMode==t.PivotTreeMap.OperationalMode.ServerMode?(u.model.beforeServiceInvoke!=null&&u._trigger("beforeServiceInvoke",{action:u._currentAction,element:u.element,customObject:u.model.customObject}),s=JSON.stringify(u.model.customObject),u.model.customObject!=""&&u.model.customObject!=null&&u.model.customObject!=i?t.isNullOrUndefined(u._pivotClientObj)?u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.drillDown,JSON.stringify({action:"drillup",drillInfo:u._selectedTagInfo,olapReport:u.getOlapReport(),customObject:s}),u.renderControlSuccess):u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.drillDown,JSON.stringify({action:"drillup",drillInfo:u._selectedTagInfo,olapReport:u.getOlapReport(),clientReports:u._pivotClientObj.reports,customObject:s}),u.renderControlSuccess):t.isNullOrUndefined(u._pivotClientObj)?u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.drillDown,JSON.stringify({action:"drillup",drillInfo:u._selectedTagInfo,olapReport:u.getOlapReport(),clientReports:u._pivotClientObj.reports}),u.renderControlSuccess):u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.drillDown,JSON.stringify({action:"drillup",drillInfo:u._selectedTagInfo,olapReport:u.getOlapReport()}),u.renderControlSuccess)):(h=u._drilledMembers.slice(0,n.inArray(f,u._drilledMembers)+1),u._drilledMembers=u._drilledMembers.slice(0,n.inArray(f,u._drilledMembers)),t.olap._mdxParser.updateDrilledReport({uniqueName:f,seriesInfo:[f],uniqueNameArray:h,action:"collapse"},"rowheader",u))}},_unWireEvents:function(){n(window).off("resize",n.proxy(this._reSizeHandler,this));this._off(this.element,"click",".drillItem",this._drillTreeMap)},renderControlSuccess:function(r){var e=this,u,f;e._treemapWaitingPopup({show:!0});try{r[0]!=i?(this.setJsonRecords(r[0].Value),this.setOlapReport(r[1].Value),t.isNullOrUndefined(this._pivotClientObj)||(this._pivotClientObj.currentReport=r[1].Value,r[2]!=null&&r[2]!=i&&(u=r[2].Key,u=="ClientReports"&&(this._pivotClientObj.reports=r[2].Value))),r[2]!=null&&r[2]!=i&&(u=r[2].Key,!u=="ClientReports"&&(this.model.customObject=r[2].Value))):r.d!=i?(this.setJsonRecords(r.d[0].Value),this.setOlapReport(r.d[1].Value),t.isNullOrUndefined(this._pivotClientObj)||(this._pivotClientObj.currentReport=r.d[1].Value,r.d[2]!=null&&r.d[2]!=i&&(u=r.d[2].Key,u=="ClientReports"&&(this._pivotClientObj.reports=r.d[2].Value))),r.d[2]!=null&&r.d[2]!=i&&(u=r.d[2].Key,u=="ClientReports"||(this.model.customObject=r.d[2].Value))):(this.setJsonRecords(r.JsonRecords),this.setOlapReport(r.OlapReport),r.customObject!=null&&r.customObject!=null&&(this.model.customObject=r.customObject),t.isNullOrUndefined(this._pivotClientObj)||(typeof this._pivotClientObj.currentReport!="undefined"&&(this._pivotClientObj.currentReport=r.OlapReport),typeof this._pivotClientObj.reports!="undefined"&&r.reports!=i&&r.reports!="undefined"&&(this._pivotClientObj.reports=r.reports)));this.model.afterServiceInvoke!=null&&this.model.operationalMode==t.PivotTreeMap.OperationalMode.ServerMode&&(f=this._currentAction!="initialize"?{action:this._currentAction,element:this.element,customObject:this.model.customObject}:{action:this._currentAction,element:this.element,customObject:this.model.customObject},this._trigger("afterServiceInvoke",f));this.renderTreeMapFromJSON(this.getJsonRecords());this._unWireEvents();this._wireEvents();this._currentAction!="initialize"&&(this.model.currentReport=this.getOlapReport(),t.isNullOrUndefined(this._pivotClientObj)?this._trigger("drillSuccess",this.element):this._pivotClientObj._trigger("treeMapDrillSuccess",this.element));t.isNullOrUndefined(this._pivotClientObj)||(this._pivotClientObj._isTimeOut=!1);typeof this._pivotClientObj!="undefined"&&this._pivotClientObj._waitingPopup!=null?(n("#"+this._pivotClientObj._id+"_maxView")[0]?n("#"+this._pivotClientObj._id+"_maxView").ejWaitingPopup({showOnInit:!1}):typeof this._pivotClientObj._pivotGrid!="undefined"&&(this._pivotClientObj&&this._pivotClientObj._pivotGrid._drillAction&&!this._pivotClientObj._pivotGrid._startDrilldown||this._pivotClientObj.otreemapObj._currentAction&&!this._pivotClientObj.otreemapObj._startDrilldown?this._pivotClientObj._waitingPopup.hide():!this._pivotClientObj||this._pivotClientObj._pivotGrid._drillAction!=""||this._pivotClientObj.otreemapObj._currentAction!=""||this._pivotClientObj._pivotGrid._startDrilldown||this._pivotClientObj.otreemapObj._startDrilldown||this._pivotClientObj._pivotGrid._JSONRecords==null&&this._pivotClientObj.otreemapObj._JSONRecords!=null?!this._pivotClientObj.otreemapObj._startDrilldown||this._pivotClientObj._pivotGrid._startDrilldown||n("#"+this._pivotClientObj._id+"_maxView")[0]?this._pivotClientObj.otreemapObj._startDrilldown||this._pivotClientObj._pivotGrid._startDrilldown||this._pivotClientObj.otreemapObj._currentAction!=""||this._pivotClientObj._pivotGrid._drillAction!=""||this._pivotClientObj._pivotGrid._JSONRecords==null&&this._pivotClientObj.otreemapObj._JSONRecords!=null||this._pivotClientObj._waitingPopup.hide():this._pivotClientObj._waitingPopup.hide():this._pivotClientObj._waitingPopup.hide()),this._pivotClientObj.model.displaySettings.mode=="chartonly"&&this._pivotClientObj._waitingPopup.hide()):this._waitingPopup.hide();typeof this._pivotClientObj!="undefined"&&(this._pivotClientObj.otreemapObj._startDrilldown=!1)}catch(o){}t.isNullOrUndefined(r.Exception)||t.Pivot._createErrorDialog(r,"Exception",this)},renderTreeMapFromJSON:function(i){var e=this,h,f,p,a,o,s,y,c,u,v,l,w,r,b,k;if(this._treeMapDatasource=[],h="",t.isNullOrUndefined(i)||i.length<=0||i.labelTags.length==0||i.points_Y.length==0){e._treemapWaitingPopup({show:!1});return}if(t.isNullOrUndefined(i.chartLables)?i.treemapLabels:i.treemapLabels=i.chartLables,i.points_Y.length!=i.treemapLabels.length)for(this._pivotClientObj._onFiltered=!1,f=[],r=0;r<JSON.parse(JSON.stringify(e.getJsonRecords().points_Y)).length-1;r++)f.push(JSON.parse(JSON.stringify(e.getJsonRecords().points_Y))[r]);else f=JSON.parse(JSON.stringify(e.getJsonRecords().points_Y));for(t.isNullOrUndefined(this._pivotClientObj)||this._pivotClientObj.model.enableDeferUpdate&&this._pivotClientObj._ischartTypesChanged&&(this._pivotClientObj._ischartTypesChanged=!1),r=0;r<f.length;r++)for(p=e.getJsonRecords().treemapLabels[r].split("~~")[e.getJsonRecords().treemapLabels[r].split("~~").length-1],u=0;u<f[r].length;u++)f[r][u].Item1=f[r][u].Item1.replace(f[r][u].Item1,p),f[r][u].Column=e.getJsonRecords().seriesNames[u].split("~")[0],f[r][u].Measures=e.getJsonRecords().measureNames,f[r][u].Tag=n.grep(e.getJsonRecords().labelTags,function(n){return n.split("::")[2]==f[r][u].Item1})[0],this.model.operationalMode==t.PivotTreeMap.OperationalMode.ClientMode&&(f[r][u].drillTag=n.grep(e.getJsonRecords().drillTags,function(n){return n.split("::")[2]==f[r][u].Item1})[0]),f[r][u][f[r][u].Tag.split("::")[1].split(".")[0].replace("[","").replace("]","")]=f[r][u].Item1,f[r][u].RowItem=f[r][u].Item1,f[r][u].Value=f[r][u].Item2!=""?f[r][u].Item2:"0",f[r][u].Index=u,delete f[r][u].Item1,delete f[r][u].Item2,h+=(h==""?"":";")+f[r][u].Tag.split("::")[1].split(".")[0].replace("[","").replace("]","");if(t.isNullOrUndefined(e.getJsonRecords().measureNames)||(a=e.getJsonRecords().measureNames.split("~")),a.length!=2)for(r=0;r<f.length;r++)for(u=0;u<f[r].length;u++)f[r][u].Value=t.isNullOrUndefined(f[r][u].Value)?0:(f[r][u].Value.indexOf(",")||f[r][u].Value.indexOf(".")||f[r][u].Value.indexOf(" "))>-1?t.globalize.parseFloat(t.globalize.format(f[r][u].Value,"c",this.model.locale),this.model.locale):t.globalize.parseFloat(f[r][u].Value),this._treeMapDatasource.push(f[r][u]),(t.isNullOrUndefined(o)||o>=f[r][u].Value)&&(o=f[r][u].Value),(t.isNullOrUndefined(s)||s<=f[r][u].Value)&&(s=f[r][u].Value);else a.length==2&&(y=[],n.each(f,function(i,r){for(var u=0;u<r.length;u++)t.isNullOrUndefined(r[u].Value)||(r[u].Value=t.isNullOrUndefined(r[u].Value)?0:(r[u].Value.indexOf(",")||r[u].Value.indexOf(".")||r[u].Value.indexOf(" "))>-1?t.globalize.parseFloat(t.globalize.format(r[u].Value,"c",e.model.locale),e.model.locale):t.globalize.parseFloat(r[u].Value),r[u].Color=r[u].Value,t.isNullOrUndefined(r[u+1])?r[u].Value=0:r[u].Column==r[u+1].Column&&(r[u].Value=t.isNullOrUndefined(r[u+1].Value)?0:(r[u+1].Value.indexOf(",")||r[u+1].Value.indexOf(".")||r[u+1].Value.indexOf(" "))>-1?t.globalize.parseFloat(t.globalize.format(r[u+1].Value,"c",e.model.locale),e.model.locale):t.globalize.parseFloat(r[u+1].Value),u=u+1));n.each(r,function(n,i){t.isNullOrUndefined(i)||t.isNullOrUndefined(i.Value)||!t.isNullOrUndefined(i.Color)||r.splice(n,1)});n.each(r,function(n,i){t.isNullOrUndefined(i)||(i.Index=n,y.push(i))})}),this._treeMapDatasource=y,n.each(this._treeMapDatasource,function(n,i){t.isNullOrUndefined(i.Color)||((t.isNullOrUndefined(o)||o>=i.Color)&&(o=i.Color),(t.isNullOrUndefined(s)||s<=i.Color)&&(s=i.Color))}));if(e.getJsonRecords().treemapLabels.length>0){for(e._drillText="",c=[],r=0;r<e.getJsonRecords().treemapLabels.length;r++)if(e.getJsonRecords().treemapLabels[r].split("~~").length>1)for(u=0;u<e.getJsonRecords().treemapLabels[r].split("~~").length-1;u++){for(v=0,l=0;l<e.getJsonRecords().labelTags.length;l++)if(e.getJsonRecords().labelTags[l].split("::")[2]==e.getJsonRecords().treemapLabels[r].split("~~")[u]&&e.getJsonRecords().labelTags[l].split("::")[e.getJsonRecords().labelTags[l].split("::").length-1]==1){v=1;break}c.length==0&&v==1?c.push(e.getJsonRecords().treemapLabels[r].split("~~")[u]):c.length>0&&(w=jQuery.inArray(e.getJsonRecords().treemapLabels[r].split("~~")[u],c),w==-1&&v==1&&c.push(e.getJsonRecords().treemapLabels[r].split("~~")[u]))}for(r=0;r<c.length;r++)e._drillText+=e._drillText==""?t.buildTag("a.drillItem",c[r])[0].outerHTML:" > "+t.buildTag("a.drillItem",c[r])[0].outerHTML}this._showDrillText=e._drillText==""?t.buildTag("span.drillup",h.split(";")[0],{"margin-left":"5px"})[0].outerHTML:t.buildTag("span.drillup",h.split(";")[0],{"margin-left":"5px"})[0].outerHTML+": "+e._drillText;b=t.buildTag("div#"+this._id+"TreeMapContainer","",{height:t.isNullOrUndefined(this._pivotClientObj)?this.element.height():(parseInt(this._pivotClientObj._chartHeight)-50).toString()+"px",width:t.isNullOrUndefined(this._pivotClientObj)?this.element.width():this._pivotClientObj._chartWidth,"margin-top":"50px"})[0].outerHTML;this.element.html(b);o=o-1;s=s+1;h=h.split(";")[0];n("#"+this._id+"TreeMapContainer").ejTreeMap({ptreemapProxy:this,dataSource:this._treeMapDatasource,showTooltip:!0,colorValuePath:a.length==2?"Color":"Value",tooltipTemplate:"tooltipTemplate",enableDrillDown:!1,enableGradient:!0,isResponsive:this.model.isResponsive,highlightGroupOnSelection:t.isNullOrUndefined(this._pivotClientObj)?!0:this._pivotClientObj.model.enableDeferUpdate?!1:!0,treeMapGroupSelected:this._drillTreeMap,refreshed:this._treeMapRenderSuccess,showLegend:!0,legendSettings:{leftLabel:o.toString(),width:150,height:20,title:this._treeMapDatasource[0].Measures,rightLabel:s.toString(),mode:"interactive",dockPosition:"bottom",alignment:"center"},rangeColorMapping:[{from:o,to:s,gradientColors:["#fde6cc","#fab665"]}],weightValuePath:"Value",leafItemSettings:{showLabels:!0,labelPath:"Column",labelVisibilityMode:"hideonexceededlength"},levels:[{groupPath:h,groupGap:2,showHeader:!0,headerHeight:25,labelPosition:"topleft",headerVisibilityMode:"hideonexceededlength"}]});this._treeMap=this.element.find("#"+this._id+"TreeMapContainer").data("ejTreeMap");this._treeMap.rowItem=h;n.views.helpers({Measures:function(n){return n.Measures},Column:function(n){return n.Column},Row:function(n){return n.RowItem},Value:function(n){return n.Value},Color:function(n){return t.isNullOrUndefined(n.Color)?n.Value:n.Color}});k={action:this._currentAction,customObject:this.model.customObject,element:this.element};this._trigger("renderSuccess",k);t.isNullOrUndefined(this._pivotClientObj)||n("#"+this._id+"TreeMapContainer").css({width:"100%"});this._treeMap.refresh();e=this;e._JSONRecords=i;e._treemapWaitingPopup({show:!1})},_treemapWaitingPopup:function(i){var r=n(this.element).hasClass("e-pivottreemap")?n(this.element).data("ejPivotTreeMap"):n(this.element).parents(".e-pivottreemap").data("ejPivotTreeMap");n(r.element).parents(".e-pivotclient").length>0?(t.isNullOrUndefined(r._pivotClientObj)||i.show||(r._pivotClientObj._isTimeOut=!1),n("#"+r._pivotClientObj._id+"_maxView")[0]?n("#"+r._pivotClientObj._id+"_maxView").ejWaitingPopup({showOnInit:i.show}):t.isNullOrUndefined(r._pivotClientObj._waitingPopup)||(i.show?r._pivotClientObj._waitingPopup.show():r._pivotClientObj._waitingPopup.hide())):!t.isNullOrUndefined(r._waitingPopup)&&i.show?r._waitingPopup.show():r._waitingPopup.hide()},_treeMapRenderSuccess:function(){var i=this.model.ptreemapProxy;i._treeMap=n("#"+i._id+"TreeMapContainer").data("ejTreeMap");t.isNullOrUndefined(i._treeMap)||(i._treeMap.model.legendSettings.dockPosition=="top"?i.element.find("#"+i._id+"TreeMapContainer").children().hasClass("e-drillupAction")||n(t.buildTag("div#"+this._id+"_drillHeader .e-drillupAction",i._showDrillText,{height:"25px",width:"99.8%","z-index":"10",position:"absolute","margin-top":"33px"})[0].outerHTML).insertAfter(n("#"+i._id+"TreeMapContainer").find(".LegendDiv").css("margin-top","-50px").css("font-size","12px")):(n("#"+i._id+"TreeMapContainer").find(".LegendDiv").css("font-size","12px").insertAfter(n("#"+i._id+"TreeMapContainer").find("._templateDiv")),n(t.buildTag("div#"+this._id+"_drillHeader .e-drillupAction",i._showDrillText,{height:"25px",width:"99.8%",position:"absolute","margin-top":"-32px"})[0].outerHTML).insertBefore(n("#"+i._id+"TreeMapContainer").find("#"+this._id+"_backgroundTile"))))},_reSizeHandler:function(){var n=this,i=n.element.find("#"+n._id+"TreeMapContainer").width(n.element.width()).data("ejTreeMap");t.isNullOrUndefined(i)||i.refresh()},doAjaxPost:function(r,u,f,e,o,s){var h,c,l,v=!0,p=this,y=this.model.enableXHRCredentials||!t.isNullOrUndefined(this._pivotClientObj)&&this._pivotClientObj.model.enableXHRCredentials,a;f.XMLA==i?(h="application/json; charset=utf-8",c="json",l=n.proxy(e,this)):(h="text/xml",c="xml",f=f.XMLA,l=n.proxy(e,t.olap.base,s),v=t.browserInfo().name=="msie"&&t.browserInfo().version<=9?!1:!t.isNullOrUndefined(s)&&s.action!="loadFieldElements"?!0:!1);a={type:r,url:u,contentType:h,async:v,dataType:c,data:f,success:l,xhrFields:{withCredentials:y},complete:t.proxy(function(t){n.proxy(t,this);var i={action:this._currentAction,customObject:this.model.customObject,element:this.element};this._trigger("renderComplete",i)},this),error:t.proxy(function(n){typeof this._waitingPopup!="undefined"&&this._waitingPopup!=null&&this._waitingPopup.hide();p._treemapWaitingPopup({show:!1});var t={action:this._currentAction,customObject:this.model.customObject,element:this.element,Message:n};this._trigger("renderFailure",t);this.renderTreeMapFromJSON("")},this)};y||delete a.xhrFields;n.ajax(a)},doPostBack:function(t,i){var r=n("<form>").attr({action:t,method:"POST",name:"export"}),f=function(t,i){var u=n('<input type="hidden" title="params">').attr({id:t,name:t,value:i}).appendTo(r)};for(var u in i)f(u,i[u]);r.appendTo(document.body).submit().remove()}});t.PivotTreeMap.OperationalMode={ClientMode:"clientmode",ServerMode:"servermode"}})(jQuery,Syncfusion)});
