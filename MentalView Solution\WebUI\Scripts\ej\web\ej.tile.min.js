/*!
*  filename: ej.tile.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.globalize.min","./../common/ej.core.min","./../common/ej.touch.min"],n):n()})(function(){(function(n,t){t.widget("ejTileBase","ej.TileBase",{defaults:{badge:{enabled:!1,value:1,text:null,maxValue:100,minValue:1,position:"bottomright"},caption:{enabled:!0,text:"text",icon:null,position:"innerbottom",alignment:"normal"},enablePersistence:!1,imageClass:null,imagePosition:"center",imageTemplateId:null,imageUrl:null,liveTile:{text:null,enabled:!1,imageClass:null,imageTemplateId:null,imageUrl:null,type:"flip",updateInterval:2e3},cssClass:"",tileSize:"small",width:null,height:null,showRoundedCorner:!1,backgroundColor:null,allowSelection:!1,locale:"en-US",showText:!0,text:"text",textAlignment:"normal"},dataTypes:{imagePosition:"enum",liveTile:{text:"array",type:"enum",enabled:"boolean",updateInterval:"number",imageUrl:"array",imageClass:"array",imageTemplateId:"array"},alignment:"enum",tileSize:"enum",locale:"string"},observables:["badge.value","badge.enabled","badge.text","badge.position","text","caption.text"],_badgeValue:t.util.valueFunction("badge.value"),_badgeEnabled:t.util.valueFunction("badge.enabled"),_badgeText:t.util.valueFunction("badge.text"),_badgePosition:t.util.valueFunction("badge.position"),_captionText:t.util.valueFunction("caption.text"),text:t.util.valueFunction("text"),_setDeprecatedProperties:function(){this.model.caption.enabled=this.defaults.caption.enabled==this.model.caption.enabled&&this.defaults.showText==this.model.showText?this.defaults.caption.enabled:this.model.caption.enabled!=this.defaults.caption.enabled?this.model.caption.enabled:this.model.showText;this._captionText(this.defaults.caption.text==this._captionText()&&this.defaults.text==this.text()?this.defaults.caption.text:this._captionText()!=this.defaults.caption.text?this._captionText():this.text());this.model.caption.alignment=this.defaults.caption.alignment==this.model.caption.alignment&&this.defaults.textAlignment==this.model.textAlignment?this.defaults.caption.alignment:this.model.caption.alignment!=this.defaults.caption.alignment?this.model.caption.alignment:this.model.textAlignment},_tileRender:function(){var u,r,i,f;if(this._setDeprecatedProperties(),this._imagePosition=this.model.imagePosition,this._cssClass=this.model.cssClass,this.element.addClass(this.model.cssClass+" "+this._prefix+"tile-"+this.model.tileSize),this.model.showRoundedCorner&&this.element.addClass(this._prefix+"tile-round-corner"),this._isLiveTile()?this._liveTile():(this._image=t.buildTag("div").addClass(this._prefix+"tile-image"+this.model.imagePosition+" "+this._prefix+"image-parent"),this._innerImage=t.buildTag("span").addClass(this._prefix+"tile-image "+this._prefix+"tile-image"+this.model.imagePosition),this.model.imageTemplateId?(u=this._isMobile()?t.getCurrentPage().find("#"+this.model.imageTemplateId):n("#"+this.model.imageTemplateId),this._innerImage.addClass(this._prefix+"tile-template").append(u),(this.model.imageUrl||this.model.imageClass)&&this._setImageProcess()):this.model.imageClass?this._setImageProcess():this.model.imageUrl&&this._setImageProcess(),this._image.append(this._innerImage),this.element.append(this._image),this._setBackgroundColor(this.model.backgroundColor),this.model.imageTemplateId&&this._prefix=="e-m-"&&t.angular.defaultAppName&&t.angular.compile(this._image)),this._isCaptionEnable())if(this.model.liveTile.text&&this._isLiveTile())for(r=this.element.find("."+this._prefix+"image-parent"),i=0,f=r.length;i<f;i++)this._setCaptionClass(n(r[i])),n(r[i]).addClass(this._prefix+"tile-caption-text ").attr("data-ej-text",this.model.liveTile.text[i]);else this._setCaptionClass(this.element),this.model.caption.icon?(this._captionIcon=this.model.caption.icon,this.element.addClass(this._prefix+"tile-caption-icon "+this._prefix+"icon-"+this._captionIcon)):this.element.addClass(this._prefix+"tile-caption-text ").attr("data-ej-text",this._captionText());this._badgeEnabled()&&this._setBadgeEnabled();this._selectElement=t.buildTag("span."+this._prefix+"tile-overlay");this.element.append(this._selectElement);this.model.height||(this.model.height=this.model.caption.position=="outer"?this.element.height()-50:this.element.height());this.model.width||(this.model.width=this.element.width());this._isCustomizeSize()&&this._setCustomizSize()},_setImageProcess:function(){if(this.model.imageUrl){var n=this._isMobile()&&this.model.imagePath?this._getAbsolutePath(this.model.imagePath)+"/"+this.model.renderMode.toLowerCase()+"/"+this.model.imageUrl:this._getAbsolutePath(this.model.imageUrl);this._innerImage.css({"background-image":"url('"+n+"')"});t.browserInfo().name=="msie"&&t.browserInfo().version<9&&this.model.imagePosition=="fill"&&this._innerImage.css({filter:"progid:DXImageTransform.Microsoft.AlphaImageLoader(src='"+n+"',sizingMethod='scale')","-ms-filter":"progid:DXImageTransform.Microsoft.AlphaImageLoader(src='"+n+"',sizingMethod='scale')"})}else this.model.imageClass&&this._innerImage.addClass(this.model.imageClass)},_liveTile:function(){var r,e,o,u,i,s,f;if(this.element.addClass(this._prefix+"livetile-enable"),this.model.liveTile.imageTemplateId)for(i=0;i<this.model.liveTile.imageTemplateId.length;i++)e=t.buildTag("div").addClass(this._prefix+"tile-template "+this._prefix+"tile-image"+this.model.imagePosition+" "+this._prefix+"image-parent "+this._prefix+"tile-"+this.model.liveTile.type),o=this._isMobile()?t.getCurrentPage().find("#"+this.model.liveTile.imageTemplateId[i]):n("#"+this.model.liveTile.imageTemplateId[i]),this.element.append(e.append(o));else if(this.model.liveTile.imageClass)for(i=0;i<this.model.liveTile.imageClass.length;i++)r=t.buildTag("span").addClass(this.model.liveTile.imageClass[i]+" "+this._prefix+"tile-image "+this._prefix+"tile-image"+this.model.imagePosition),u=t.buildTag("div").addClass(this._prefix+"tile-image"+this.model.imagePosition+" "+this._prefix+"tile-"+this.model.liveTile.type+" "+this._prefix+"image-parent"),r.appendTo(u),this.element.append(u);else for(i=0;i<this.model.liveTile.imageUrl.length;i++)s=this._isMobile()&&this.model.imagePath?this._getAbsolutePath(this.model.imagePath)+"/windows/"+this.model.liveTile.imageUrl[i]:this._getAbsolutePath(this.model.liveTile.imageUrl[i]),r=t.buildTag("span").addClass(this._prefix+"tile-image "+this._prefix+"tile-image"+this.model.imagePosition).css({"background-image":"url('"+s+"')"}),f=t.buildTag("div."+this._prefix+"tile-image"+this.model.imagePosition+" "+this._prefix+"tile-"+this.model.liveTile.type+" "+this._prefix+"image-parent"),r.appendTo(f),this.element.append(f);this._setBackgroundColor(this.model.backgroundColor);n(this.element.children()[0]).addClass(this._prefix+"tile-"+this.model.liveTile.type+"back").removeClass(this._prefix+"tile-"+this.model.liveTile.type);this.model.imageTemplateId&&this._prefix=="e-m-"&&(App.angularAppName||t.angular.defaultAppName)&&t.angular.compile(this.element)},_setCustomizSize:function(){var i=(this._isLiveTileMode()||this.model.renderMode=="flat")&&parseInt(this.model.width)<=70?"70":this._isMobile()&&this.model.renderMode=="android"&&this._isBelowSmallSize()?"85":this._isMobile()&&this.model.renderMode=="ios7"&&this._isBelowSmallSize()?"74":this.model.width,n=(this._isLiveTileMode()||this.model.renderMode=="flat")&&parseInt(this.model.height)<=70?"70":this._isMobile()&&(this.model.renderMode=="android"||this.model.renderMode=="ios7")&&this._isBelowSmallSize()?"70":this._isMobile()&&this.model.renderMode=="ios7"&&this._isBelowSmallSize()?"70":this.model.height,t=(this._isLiveTileMode()||this.model.renderMode=="flat")&&!this._isBelowSmallSize()?50:(this._isLiveTileMode()||this.model.renderMode=="flat")&&this._isBelowSmallSize()?0:this._isMobile()&&this.model.renderMode=="android"&&this._isBelowSmallSize()?30:30,r=this.model.caption.enabled&&this.model.caption.position=="outer"&&this.model.height?n?parseInt(n)+t:parseInt(n)+t:this.model.height;this.element.css({width:this.model.width?this.model.width:"70",height:this.model.height?r:this.element.height()+t});this.element.find("."+this._prefix+"image-parent").css({width:this.element.css("width"),height:this.model.height?n:this.element.height()-t});!this._isBelowSmallSize()&&this.model.caption.enabled&&this.model.caption.position=="outer"&&(this.element.find(".e-m-tile-image").css({width:this.model.width?i:"",height:this.model.height?n:""}),this._selectElement.css({width:this.model.width?i:"",height:this.model.height?n:""}));this._isBelowSmallSize()&&(this.element.addClass(this._prefix+"tile-small"),this._isMobile()&&(this.model.renderMode=="android"||this.model.renderMode=="ios7")&&this.model.caption.position=="outer"||this.element.removeAttr("data-ej-text"))},_isLiveTile:function(){return this._isLiveTileMode()&&this.model.liveTile.enabled},_isLiveTileMode:function(){return!this._isMobile()||this.model.renderMode=="windows"},_setCaptionClass:function(n){n.addClass(this._prefix+"tile-caption "+this._prefix+"caption-"+this.model.caption.position+" "+this._prefix+"caption-align-"+this.model.caption.alignment)},_isCaptionEnable:function(){return this.model.caption.enabled&&!(this.model.tileSize=="small"||this._isBelowSmallSize()&&this.model.tileSize=="small")||this._isMobile()&&(this.model.renderMode=="ios7"||this.model.renderMode=="android")&&this.model.caption.position=="outer"||this._isMobile()&&(this.model.renderMode=="android"||this.model.renderMode=="ios7")&&this.model.caption.position=="outer"&&this._isCustomizeSize()&&this._isBelowSmallSize()},_isBelowSmallSize:function(){var n=this.model.width?parseInt(this.model.width):this.model.width,t=this.model.height?parseInt(this.model.height):this.model.height,i=!this._isMobile()||this._isMobile()&&(this.model.renderMode=="windows"||this.model.renderMode=="flat")?70:this._isMobile()&&this.model.renderMode=="android"?85:74;return n<=i&&n!=null||t<=i&&t!=null},_isCustomizeSize:function(){return this.model.height||this.model.width},_createDelegates:function(){this._onStartDelegate=n.proxy(this._isMobile()?this._onTouchStartHandler:this._onMouseDownHandler,this);this._onEndDelegate=n.proxy(this._isMobile()?this._onTouchEndHandler:this._onMouseUpHandler,this);this._onMoveDelegate=n.proxy(this._isMobile()?this._onTouchMoveHandler:this._onMouseMoveHandler,this);this._onDocClickDelegate=n.proxy(this._onDocClickHandler,this);this._startAnimationDelegate=n.proxy(this["_"+this.model.liveTile.type+"Tile"],this)},_wireEvents:function(i){this._createDelegates();t.listenEvents([this.element,n(document)],[t.startEvent(),t.endEvent()],[this._onStartDelegate,this._onDocClickDelegate],i,this);this._isLiveTile()&&(i?this._stopTileAnimation():this._startTileAnimation())},_onDocClickHandler:function(){this._removeActiveClass()},_removeActiveClass:function(){this._isLiveTileMode()?t._removeSkewClass(this.element):n(this.element).removeClass(this._prefix+"state-active")},_startTileAnimation:function(){this._intervalCounter=setInterval(this._startAnimationDelegate,this.model.liveTile.updateInterval)},_stopTileAnimation:function(){clearInterval(this._intervalCounter)},_flipTile:function(){var n=this.element.find("."+this._prefix+"tile-flipback"),t=this.element.find("."+this._prefix+"tile-flip").first();n.addClass(this._prefix+"tile-flip").removeClass(this._prefix+"tile-flipback");t.addClass(this._prefix+"tile-flipback").removeClass(this._prefix+"tile-flip")},_slideTile:function(){this._animateEffect()},_carouselTile:function(){this._animateEffect()},_animateEffect:function(){var i=this.model.liveTile.type.toLowerCase(),r=this.element.find("."+this._prefix+"image-parent"),t=this.element.find("."+this._prefix+"tile-"+i+"back"),e=t.next()[0]==null?r.first():t.next(),o=t.prev()[0]==null?r.last():t.prev(),u,f;(n(t.next()[0]).hasClass(this._prefix+"tile-overlay")||n(t.prev()[0]).hasClass(this._prefix+"tile-overlay"))&&(u=n(t.next()[0]).next(),f=n(t.next()[0]).prev(),e=u.next()[0]==null?r.first():u.next(),o=f.prev()[0]==null?r.last():f.prev());o.addClass(this._prefix+"tile-"+i).removeClass(this._prefix+"tile-"+i+"up");t.addClass(this._prefix+"tile-"+i+"up").removeClass(this._prefix+"tile-"+i+"back");e.removeClass(this._prefix+"tile-"+i).addClass(this._prefix+"tile-"+i+"back")},_getAbsolutePath:function(n){return this._isMobile()?this._makeUrlAbsolute(n).toString():n},_makeUrlAbsolute:function(i){var r=n("<a href ='"+i+"'><\/a>")[0],u;n("body").append(r);u=r.pathname.indexOf("/")!=0?"/"+r.pathname:r.pathname;var f=t.browserInfo(),e=f.name=="msie"&&f.version=="9.0",o=r.protocol+"//"+(e&&r.port=="80"?r.host.replace(":80",""):r.host)+u+r.search+r.hash;return n(r).remove(),o},_isMobile:function(){return this._prefix=="e-m-"?!0:!1},updateTemplate:function(i,r){var u,f;this._isLiveTile()?(n(this.element.children()[r]).empty(),u=this._isMobile()?t.getCurrentPage().find("#"+i):n("#"+i),n(this.element.children()[r]).append(u)):(f=this._isMobile()?t.getCurrentPage().find("#"+i):n("#"+i),this._image.addClass(this._prefix+"tile-template").empty().append(f))},_setModel:function(n){var u=!1,i,r;for(i in n)if(r="_set"+i.charAt(0).toUpperCase()+i.slice(1),this[r]||i=="locale")switch(i){case"text":this[r](t.util.getVal(n[i]));n[i]=this.text(t.util.getVal(n[i]));t.isNullOrUndefined(this._options)&&(this._options={});this._options.caption=this.model.caption;break;case"locale":t.Tile.Locale[n[i]]&&(this.model.locale=n[i],this._setCulture(),this._setCaptionText(this._captionText()));break;default:this[r](n[i])}else u=!0;u&&this._refresh()},_setCaption:function(n){var u=!1,i,r;for(i in n)if(r="_setCaption"+i.charAt(0).toUpperCase()+i.slice(1),this[r])switch(i){case"text":this[r](t.util.getVal(n[i]));n[i]=this._captionText(t.util.getVal(n[i]));break;default:this[r](n[i])}else u=!0;u&&this._refresh()},_setText:function(n){this._captionText(n);this._setCaptionText(n)},_setCaptionText:function(n){this._isCaptionEnable()?this.element.attr("data-ej-text",n):this.element.removeAttr("data-ej-text")},_setCaptionEnabled:function(){this._isCaptionEnable()?this.element.attr("data-ej-text",this._captionText()):this.element.removeAttr("data-ej-text")},_setCaptionIcon:function(n){this._isCaptionEnable()&&(this.element.removeClass(this._prefix+"caption-text "+this._prefix+"icon-"+this._captionIcon).addClass(this._prefix+"caption-icon "+this._prefix+"icon-"+n).removeAttr("data-ej-text"),this._captionIcon=n)},_setCaptionAlignment:function(n){this._isCaptionEnable()&&this.element.removeClass(this._prefix+"caption-align-normal "+this._prefix+"caption-align-left "+this._prefix+"caption-align-center "+this._prefix+"caption-align-right").addClass(this._prefix+"caption-align-"+n)},_setShowRoundedCorner:function(n){n?this.element.addClass(this._prefix+"tile-round-corner"):this.element.removeClass(this._prefix+"tile-round-corner")},_setCssClass:function(n){this.element.removeClass(this._cssClass).addClass(n);this._cssClass=n},_setBackgroundColor:function(n){!this._isMobile()||this._isMobile()&&this.model.renderMode=="windows"||this.model.caption.position=="outer"?this.element.find("."+this._prefix+"tile-image").css("background-color",n):this.element.css("background-color",n)},_setTileSize:function(n){this._isCaptionEnable()?this._isCaptionEnable()&&this.element.attr("data-ej-text",this._captionText()):this.element.removeAttr("data-ej-text");this.element.css({width:"",height:""});this.element.find("."+this._prefix+"image-parent").css({width:"",height:""});this.element.removeClass(this._prefix+"tile-small "+this._prefix+"tile-medium "+this._prefix+"tile-wide "+this._prefix+"tile-large").addClass(this._prefix+"tile-"+n);this.model.height=this.element.height();this.model.width=this.element.width()},_setImagePosition:function(t){this.element.addClass(this._prefix+"tile-image"+t);n(this.element.find("."+this._prefix+"tile-image")).removeClass(this._prefix+"tile-image"+this._imagePosition).addClass(this._prefix+"tile-image"+t);this._imagePosition=this.model.imagePosition},_setBadge:function(n){var i,r;for(i in n)if(r="_setBadge"+i.charAt(0).toUpperCase()+i.slice(1),this[r])switch(i){case"value":case"enabled":case"text":this[r](t.util.getVal(n[i]));typeof n[i]=="function"?n[i](this["_badge"+i.charAt(0).toUpperCase()+i.slice(1)]()):n[i]=this["_badge"+i.charAt(0).toUpperCase()+i.slice(1)]();break;default:this[r](n[i])}},_setBadgeValue:function(n){if(this._badgeEnabled()){n=t.isNullOrUndefined(n)?this._badgeValue():n;var i;this._badgeText()?i=this._badgeText():n<=this.model.badge.maxValue&&n>=this.model.badge.minValue?(i=n.toString(),this._badgeValue(n)):n>this.model.badge.maxValue?(i=this.model.badge.maxValue.toString()+"+",this._badgeValue(this.model.badge.maxValue)):(i=this.model.badge.minValue.toString(),this._badgeValue(this.model.badge.minValue));this.element.addClass(this._prefix+"tile-badge "+this._prefix+"badge-position-"+this._badgePosition());this.element.addClass(this._prefix+"tile-badge-value").attr("data-ej-badgeValue",i)}else this.element.removeClass(this._prefix+"tile-badge "+this._prefix+"badge-position-"+this._badgePosition()+" "+this._prefix+"tile-badge-value")},_setBadgePosition:function(n){this._badgePosition(n);this._badgeEnabled()&&this.element.removeClass(this._prefix+"badge-position-bottomright "+this._prefix+"badge-position-topright").addClass(this._prefix+"badge-position-"+this._badgePosition())},_setBadgeEnabled:function(n){this._badgeEnabled(n);this._setBadgeValue()},_setBadgeMaxValue:function(n){this.model.badge.maxValue=n;this._setBadgeValue()},_setBadgeMinValue:function(n){this.model.badge.minValue=n;this._badgeEnabled()&&this._setBadgeValue()},_setBadgeText:function(n){this._badgeText(n);this._badgeEnabled()&&this._setBadgeValue()},_clearElement:function(){this.element.removeAttr("class");this.element.html(this._orgEle.html())},_destroy:function(){this._wireEvents(!0);this._clearElement()}})})(jQuery,Syncfusion),function(n,t){t.widget("ejTile","ej.Tile",{_setFirst:!0,validTags:["div"],_rootCSS:"e-js e-tile",defaults:{mouseDown:null,mouseUp:null},_init:function(n){this._options=n;this._setCulture();this._orgEle=this.element.clone();this._liveTileimageTemplateParent=[];this._cloneLiveTileImageTemplateElement=[];this._renderEJControl()},_renderEJControl:function(){if(this.model.captionTemplateId&&(this._captionTemplateParent=n("#"+this.model.captionTemplateId).parent(),this._cloneCaptionTemplateElement=n("#"+this.model.captionTemplateId).clone()),this.model.imageTemplateId&&(this._imageTemplateParent=n("#"+this.model.imageTemplateId).parent(),this._cloneImageTemplateElement=n("#"+this.model.imageTemplateId).clone()),this.model.liveTile.enabled&&this.model.liveTile.imageTemplateId)for(var t=0;t<this.model.liveTile.imageTemplateId.length;t++)this._liveTileimageTemplateParent[t]=n("#"+this.model.liveTile.imageTemplateId[t]).parent(),this._cloneLiveTileImageTemplateElement[t]=n("#"+this.model.liveTile.imageTemplateId[t]).clone();this.element.addClass("e-tile-web");this._prefix="e-";this._tileRender();this._wireEvents()},_onMouseDownHandler:function(i){var r=this.element;r.addClass(t._getSkewClass(r,i.pageX,i.pageY).replace("e-m-","e-"));this._trigger("mouseDown",{text:r.attr("data-ej-text"),index:n(i.target).index()});t.listenTouchEvent(this.element,t.moveEvent(),this._onMoveDelegate,!1,this);t.listenTouchEvent(this.element,t.endEvent(),this._onEndDelegate,!1,this);this._isMoved=!1},_onMouseMoveHandler:function(){this._isMoved=!0;this._removeActiveClass()},_onMouseUpHandler:function(i){this.model.allowSelection&&(this._selected?this._selected&&(this._selected=!1,this._selectElement.removeClass("e-tile-selected")):(this._selected=!0,this._selectElement.addClass("e-tile-selected")));!this._isMoved&&this.model.mouseUp&&this._trigger("mouseUp",{text:this.element.attr("data-ej-text"),index:n(i.target).index(),select:this._selected});this._removeActiveClass();this._isMoved=!1;t.listenTouchEvent(this.element,t.moveEvent(),this._onMoveDelegate,!0,this);t.listenTouchEvent(this.element,t.endEvent(),this._onEndDelegate,!0,this)},_refresh:function(){if(this._destroy(),this.element.addClass("e-tile e-js"),this.model.captionTemplateId&&n(this._captionTemplateParent).append(this._cloneCaptionTemplateElement),this.model.imageTemplateId&&n(this._imageTemplateParent).append(this._cloneImageTemplateElement),this.model.liveTile.enabled&&this.model.liveTile.imageTemplateId)for(var t=0;t<this.model.liveTile.imageTemplateId.length;t++)n(this._liveTileimageTemplateParent[t]).append(this._cloneLiveTileImageTemplateElement[t]);this._renderEJControl()},_setCulture:function(){this.model.locale!="en-US"&&(t.isNullOrUndefined(this._options)||(t.isNullOrUndefined(this._options.caption)||t.isNullOrUndefined(this._options.caption.text))&&t.isNullOrUndefined(this._options.text))&&this._captionText(this._getLocalizedLabels().captionText)},_getLocalizedLabels:function(){return t.getLocalizedConstants(this.sfType,this.model.locale)}});t.Tile.Locale=t.Tile.Locale||{};t.Tile.Locale["default"]=t.Tile.Locale["en-US"]={captionText:"text"};t.Tile.ImagePosition={Center:"center",TopCenter:"topcenter",BottomCenter:"bottomcenter",RightCenter:"rightcenter",LeftCenter:"leftcenter",TopLeft:"topleft",TopRight:"topright",BottomRight:"bottomright",BottomLeft:"bottomleft",Fill:"fill"};t.Tile.BadgePosition={TopRight:"topright",BottomRight:"bottomright"};t.Tile.CaptionAlignment={Normal:"normal",Left:"left",Right:"right",Center:"center"};t.Tile.CaptionPosition={InnerTop:"innertop",InnerBottom:"innerbottom",Outer:"outer"};t.Tile.TileSize={Medium:"medium",Small:"small",Large:"large",Wide:"wide"};t.Tile.LiveTileType={Flip:"flip",Slide:"slide",Carousel:"carousel"};n.extend(!0,t.Tile.prototype,t.TileBase.prototype)}(jQuery,Syncfusion)});
