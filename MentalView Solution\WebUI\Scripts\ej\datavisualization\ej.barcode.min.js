/*!
*  filename: ej.barcode.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function(n,t,i){t.widget("ejBarcode","ej.datavisualization.Barcode",{element:{target:null},model:null,validTags:["div","span"],_rootCSS:"e-barcode",defaults:{displayText:!0,text:"",height:"",width:"",textSize:12,symbologyType:"qrbarcode",textColor:"black",lightBarColor:"white",darkBarColor:"black",quietZone:{left:1,top:1,right:1,bottom:1,all:1},narrowBarWidth:1,wideBarWidth:3,barHeight:150,barcodeToTextGapHeight:10,xDimension:4,encodeStartStopSymbol:!0,load:null,enabled:!0},dataTypes:{displayText:"boolean",text:"string",textSize:"number",enabled:"boolean",symbologyType:"enum",narrowBarWidth:"number",wideBarWidth:"number",barHeight:"number",encodeStartStopSymbol:"boolean"},_defaultScaleValues:function(){return{enabled:!0,displayText:!0,text:"",textSize:12,symbologyType:"qrbarcode",textColor:"black",lightBarColor:"white",darkBarColor:"black",quietZone:{left:1,top:1,right:1,bottom:1},narrowBarWidth:1,wideBarWidth:3,barHeight:150,barcodeToTextGapHeight:10,xDimension:4,encodeStartStopSymbol:!0}},_init:function(){this._initialize();this._renderBarcode()},_destroy:function(){this.element.empty()},_setModel:function(n){for(var t,i=0;i<n.length;i++){t=n[i];switch(t){case"enabled":this._disabled(!n[t]);break;case"height":this.model.height=n[t];break;case"width":this.model.width=n[t];break;case"lightBarColor":this.model.lightBarColor=n[t];break;case"darkBarColor":this.model.darkBarColor=n[t];break;case"textColor":this.model.textColor=n[t];break;case"displayText":this.model.displayText=n[t];break;case"quietZone":this.model.quietZone=n[t];break;case"narrowBarWidth":this.model.narrowBarWidth=n[t];break;case"wideBarWidth":this.model.wideBarWidth=n[t];break;case"barHeight":this.model.barHeight=n[t];break;case"xDimension":this.model.xDimension=n[t];break;case"encodeStartStopSymbol":this.model.encodeStartStopSymbol=n[t];break;case"symbologyType":this.model.symbologyType=n[t];this._itemInitialize();break;case"text":this.model.text=n[t];this._itemInitialize()}}this._renderBarcode()},_initialize:function(){this.BarcodeEl=this.element;this.target=this.element[0];this._itemInitialize();this.Model=this.model},_renderBarcode:function(){this.initialize()},_itemInitialize:function(){var t=this;this.model.scales!=null?n.each(this.model.scales,function(i,r){r=t._checkArrayObject(r,i);var u=t._defaultScaleValues();n.extend(u,r);n.extend(r,u)}):this.model.scales=[this._defaultScaleValues()]},_checkArrayObject:function(t,i){var r=this;return n.each(t,function(n,t){if(t instanceof Array)t=r._checkArrayObject(t,n);else if(t!=null&&typeof t=="object"){var u=r._defaultScaleValues();t=r._LoadIndividualDefaultValues(t,u,typeof n=="number"?i:n)}}),t},_LoadIndividualDefaultValues:function(t,i,r){var u=null,f=this;return n.each(i,function(n,t){if(r==n){u=t;return}}),u instanceof Array&&(u=u[0]),n.each(t,function(n,t){t instanceof Array?t=f._checkArrayObject(t,r):t!=null&&typeof t=="object"&&(t=f._LoadIndividualDefaultValues(t,u,typeof n=="number"?r:n))}),n.extend(u,t),n.extend(t,u),t},initialize:function(){this.model.enabled&&(this._initObject(this),this.Model.text!=null&&this._findBarcodeType())},enable:function(){this.model.enabled||(this.element.removeClass("e-disable"),this.model.enabled=!0)},height:function(n){this.Model.height=n},width:function(n){this.Model.width=n},textSize:function(n){this.Model.textSize=n},resize:function(){this.Model.text!=null&&this._findBarcodeType()},disable:function(){this.model.enabled&&(this.element.addClass("e-disable"),this.model.enabled=!1)},_disabled:function(n){n?this.disable():this.enable()},_initObject:function(t){t.BarcodeEl=n("#"+t.target.id);t.canvasEl?t.canvasEl.remove():t.canvasEl=n("<canvas><\/canvas>");t.BarcodeEl.append(t.canvasEl);t.BarcodeEl.css("width",t.model.width);t.BarcodeEl.css("height",t.model.height);t.canvasEl[0].setAttribute("width",t.BarcodeEl.width());t.canvasEl[0].setAttribute("height",t.BarcodeEl.height());t.centerX=t.canvasEl[0].width/2;t.centerY=t.canvasEl[0].height/2;var r=t.canvasEl[0];(typeof G_vmlCanvasManager!="undefined"&&(r=window.G_vmlCanvasManager.initElement(r)),r&&r.getContext)&&(t.contextEl=t.canvasEl[0].getContext("2d"),t.contextEl==i)},_findBarcodeType:function(){var c,l,f,y,u,e,o,p,g,nt;if(this.contextEl!=i&&this.Model.text!=""){this.textFont={size:this.model.textSize,fontFamily:"Segoe UI",fontStyle:"Regular"};this.model.quietZone.all>1&&(this.model.quietZone.left=this.model.quietZone.right=this.model.quietZone.top=this.model.quietZone.bottom=this.model.quietZone.all);var a=1,h="",w="",r=this.Model.text,b,k,v,s,n=this.Model.symbologyType,d=!0;if(t.datavisualization.Barcode.SymbologyType.dataMatrix==n||t.datavisualization.Barcode.SymbologyType.qrBarcode==n)d=!1;else if(b=t.datavisualization.Barcode.symbologySettings[n].startSymbol,k=t.datavisualization.Barcode.symbologySettings[n].stopSymbol,w=t.datavisualization.Barcode.symbologySettings[n].validatorExp,v=t.datavisualization.Barcode.symbologySettings[n].symbolTable,s=t.datavisualization.Barcode.symbologySettings[n].extendedCodes,r=this._calculateCheckDigit(r,v,n),t.datavisualization.Barcode.SymbologyType.code39Extended==n){for(c=r.split(""),l="",f=0;f<c.length;f++)for(y=c[f],u=0;u<s.length;u++)if(s[u][0]==y&&(e=s[u][1],!(e==i)))for(o=0;o<e.length;o++)l+=e[o];h=l}else if(t.datavisualization.Barcode.SymbologyType.code93Extended==n){for(c=r.split(""),l="",f=0;f<c.length;f++)for(y=c[f],u=0;u<s.length;u++)if(s[u][0]==y&&(e=s[u][1],!(e==i)))for(o=0;o<e.length;o++)l+=e[o];h=l}if(d){if(!this._validateText(this.Model.text,w)){console.log("Barcode Text contains characters that are not accepted by this barcode specification.");return}if(this.model.symbologyType=="code32"&&this.model.text.length!=8){console.log("Barcode Text Length that are not accepted by this barcode specification.");return}if(h.length>0&&(r=h),this.model.symbologyType=="ean"&&(this.Model.encodeStartStopSymbol=!0),this.model.symbologyType=="ean"&&this.model.text.length!=7&&this.model.text.length!=12){console.log("Barcode Text Length that are not accepted by this barcode specification, EAN barcode can accept only 7 or 13 characters.");return}this.Model.encodeStartStopSymbol&&(r=b.toString()+r+k.toString());p=this._getCharWidth(r,v);p-=a*r.length;g=parseInt(this.Model.displayText?this.textFont.size:0);nt=this.Model.quietZone.top+this.Model.quietZone.bottom+this.Model.barHeight+a+(this.Model.displayText?g+this.Model.barcodeToTextGapHeight:0);p+=this.Model.quietZone.left+this.Model.quietZone.right;this._draw1DBarcode(r,v,p)}else t.datavisualization.Barcode.SymbologyType.dataMatrix==n?this._buildDataMatrix():t.datavisualization.Barcode.SymbologyType.qrBarcode==n&&this._buildQRBarcode();this._raiseEvent("load")}},_calculateCheckDigit:function(n,t,i){var v=n,f,y,p,w,c,e,r,u;if(i=="code128b"||i=="code128a"){for(f=0,y=[n.split("")],r=0;r<n.split("").length;r++)p=y[0][r],w=this._findCheckDigit(p,t)[1],f+=w*(r+1);if(i=="code128b"){for(f+=104,f=f%103,c=[1],e=0;e<t.length;e++)e==f&&(c[0]=t[e][0]);n+=c.toString()}else{for(f+=103,f=f%103,c=[1],e=0;e<t.length;e++)f==t[e][1]&&(c[0]=t[e][0]);n+=c.toString()}}else{if(i=="upcbarcode"){var y=[n.split("")],l=0,o=0;for(r=0;r<n.split("").length;r++)r<11&&(o=n[r],l+=r%2==0?o*3:o*1);var a=(10-l%10)%10,s=n.split(""),h=6;for(u=0;u<s.length;u++)u==6?h+="B"+s[u]:u!=11&&(h+=s[u]);n=h;n+=a;n+=6}if(i=="ean")if(n.length==12){var y=[n.split("")],l=0,o=0;for(r=0;r<n.split("").length;r++)r<12&&(o=n[r],l+=r%2==0?o*1:o*3);var a=(10-l%10)%10,s=n.split(""),h="A";for(u=0;u<s.length;u++)u==7?h+="B"+s[u]:u!=12&&(h+=s[u]);n=h;n+=a;v+=a;n+="A"}else if(n.length==7){var y=[n.split("")],l=0,o=0;for(r=0;r<n.split("").length;r++)r<7&&(o=n[r],l+=r%2==0?o*3:o*1);var a=(10-l%10)%10,s=n.split(""),h="A";for(u=0;u<s.length;u++)u==4?h+="B"+s[u]:u!=7&&(h+=s[u]);n=h;n+=a;v+=a;n+="A"}}return n},_findCheckDigit:function(n,t){for(var i=0;i<t.length;i++)if(t[i][0]==n)return t[i]},_raiseEvent:function(n){this.model[n]&&this._trigger(n)},_buildQRBarcode:function(){var t=this.Model.text,u,i,f,n,r,e,o,s,h;for(this.m_EciAssignmentNumber=3,this.mode="numeric",this.isEci=!1,this.m_Version=1,u=[14,26,42,62,84,106,122,152,180,213,251,287,331,362,412,450,504,560,624,666,711,779,857,911,997,1059,1125,1190,1264,1370,1452,1538,1628,1722,1809,1911,1989,2099,2213,2331],n=0;n<t.length;n++)if(i=t.charCodeAt(n),!(i<58)||!(i>47))if(i<91&&i>64||t[n]=="$"||t[n]=="%"||t[n]=="*"||t[n]=="+"||t[n]=="-"||t[n]=="."||t[n]=="/"||t[n]==":"||t[n]==" ")this.mode="alphanumeric";else if(i>=65377&&i<=65439||i>=97&&i<=122){this.mode="binary";break}else{this.mode="binary";this.isEci=!0;break}for(this.mode=="numeric"?u=[34,63,101,149,202,255,293,365,432,513,604,691,796,871,991,1082,1212,1346,1500,1600,1708,1872,2059,2188,2395,2544,2701,2857,3035,3289,3486,3693,3909,4134,4343,4588,4775,5039,5313,5596]:this.mode=="alphanumeric"&&(u=[20,38,61,90,122,154,178,221,262,311,366,419,483,528,600,656,734,816,909,970,1035,1134,1248,1326,1451,1542,1637,1732,1839,1994,2113,2238,2369,2506,2632,2780,2894,3054,3220,3391]),this.isEci==!0&&(this.m_EciAssignmentNumber=this._findECINumber(t)),n=0;n<u.length;n++)if(u[n]>t.length){this.m_Version=n+1;break}for(this.qrbarcodeValues={Version:this.m_Version,ErrorCorrectionLevel:1,NumberOfDataCodeWord:this._getNumberOfDataCodeWord(),NumberOfErrorCorrectingCodeWords:this._getNumberOfErrorCorrectingCodeWords(),NumberOfErrorCorrectionBlocks:this._getNumberOfErrorCorrectionBlocks(),End:this._getEnd(),DataCapacity:this._getDataCapacity(),FormatInformation:this._getFormatInformation(),VersionInformation:this._getVersionInformation()},this.noOfModules=(this.m_Version-1)*4+21,this.moduleValue=this._create2DArray(this.noOfModules,this.noOfModules),n=0;n<this.noOfModules;n++)for(r=0;r<this.noOfModules;r++)this.moduleValue[n][r]={IsBlack:!1,IsFilled:!1,IsPDP:!1};if(this._drawPDP(0,0),this._drawPDP(this.noOfModules-7,0),this._drawPDP(0,this.noOfModules-7),this._drawTimingPattern(),this.m_Version!=1)for(f=this._getAlignmentPatternCoOrdinates(),n=0;n<f.length;n++)for(r=0;r<f.length;r++)e=f[n],o=f[r],this.moduleValue[e][o].IsPDP!=!0&&this._drawAlignmentPattern(e,o);this._allocateFormatVersionInfo();this._encodeData();this._drawFormatInformation();this._addQuietZone();s=this.moduleValue.length*this.Model.xDimension;h=this.moduleValue[0].length*this.Model.xDimension;this.contextEl.beginPath();this.contextEl.fillStyle="white";this.contextEl.fillRect(0,0,s,h);this.contextEl.closePath();this._drawQRBarcode(h,s)},_allocateFormatVersionInfo:function(){for(var i,r,t,n=0;n<9;n++)this.moduleValue[8][n].IsFilled=!0,this.moduleValue[n][8].IsFilled=!0;for(n=this.noOfModules-8;n<this.noOfModules;n++)this.moduleValue[8][n].IsFilled=!0,this.moduleValue[n][8].IsFilled=!0;if(this.m_Version>6)for(i=this.qrbarcodeValues.VersionInformation,r=0,n=0;n<6;n++)for(t=2;t>=0;t--)this.moduleValue[n][this.noOfModules-9-t].IsBlack=i[r]==1?!0:!1,this.moduleValue[n][this.noOfModules-9-t].IsFilled=!0,this.moduleValue[this.noOfModules-9-t][n].IsBlack=i[r++]==1?!0:!1,this.moduleValue[this.noOfModules-9-t][n].IsFilled=!0},_drawTimingPattern:function(){for(var n=8;n<this.noOfModules-8;n+=2)this.moduleValue[n][6].IsBlack=!0,this.moduleValue[n][6].IsFilled=!0,this.moduleValue[n+1][6].IsBlack=!1,this.moduleValue[n+1][6].IsFilled=!0,this.moduleValue[6][n].IsBlack=!0,this.moduleValue[6][n].IsFilled=!0,this.moduleValue[6][n+1].IsBlack=!1,this.moduleValue[6][n+1].IsFilled=!0;this.moduleValue[this.noOfModules-8][8].IsBlack=!0;this.moduleValue[this.noOfModules-8][8].IsFilled=!0},_drawQRBarcode:function(n,t){var u=this.Model.quietZone.all,h,i,c,r,e,l,o;u<2&&(u=2);var s=this.textFont.size,f=parseInt(this.Model.xDimension);this.Model.height!=""&&this.Model.width!=""?(this.Model.height=this._getProperValue(this.Model.height),this.Model.width=this._getProperValue(this.Model.width)):(this.Model.height=n,this.Model.height+=s+this.Model.barcodeToTextGapHeight,this.Model.width=t,this.canvasEl[0].setAttribute("width",this.Model.width),this.BarcodeEl.css("width",this.Model.width),this.canvasEl[0].setAttribute("height",this.Model.height),this.BarcodeEl.css("height",this.Model.height));h=Math.min(this.Model.width,this.Model.height);h!=0&&(f=h/(this.noOfModules+2*u));var a=0,t=this.noOfModules+2*u,v=this.noOfModules+2*u;for(this.contextEl.beginPath(),i=0;i<t;i++){for(c=0,r=0;r<v;r++)e=null,e=this.moduleValue[i][r].IsBlack?this.model.darkBarColor:this.model.lightBarColor,this.dataAllocationValues[r][i].IsFilled&&this.dataAllocationValues[r][i].IsBlack&&(e="black"),this.contextEl.fillStyle=e,this.contextEl.fillRect(c,a,f,f),c+=f;a+=f}this.Model.displayText&&(l=this.textFont.fontStyle.toLowerCase()=="regular"?"":+this.textFont.fontStyle.toLowerCase()+" ",l+=this.textFont.size+"px "+this.textFont.fontFamily.toLowerCase()+",sans-serif",this.contextEl.font=l,o=this.Model.height,this.contextEl.beginPath(),this.contextEl.fillStyle=this.Model.textColor,this.contextEl.textAlign="center",o=o-s+s/2,this.contextEl.fillText(this.Model.text,this.Model.width/2,o));this.contextEl.closePath();this.contextEl.stroke()},_getProperValue:function(n){return n==""?"":typeof n=="string"?(n=n.replace("px",""),parseInt(n)):typeof n=="number"?n:0},_addQuietZone:function(){var n=this.Model.quietZone.all,t,i;n<2&&(n=2);var r=this.noOfModules+2*n,u=this.noOfModules+2*n,f=this._create2DArray(r,u),e=this._create2DArray(r,u);for(t=0;t<r;t++)for(i=0;i<u;i++)f[t][i]={IsBlack:!1,IsFilled:!1,IsPDP:!1},e[t][i]={IsBlack:!1,IsFilled:!1,IsPDP:!1};for(t=n;t<r-n;t++)for(i=n;i<u-n;i++)f[t][i]=this.moduleValue[t-n][i-n],e[t][i]=this.dataAllocationValues[t-n][i-n];this.moduleValue=f;this.dataAllocationValues=e},_drawFormatInformation:function(){for(var t=this.qrbarcodeValues.FormatInformation,i=0,n=0;n<7;n++)n==6?this.moduleValue[n+1][8].IsBlack=t[i]==1?!0:!1:this.moduleValue[n][8].IsBlack=t[i]==1?!0:!1,this.moduleValue[8][this.noOfModules-n-1].IsBlack=t[i++]==1?!0:!1;for(i=14,n=0;n<7;n++)n==6?this.moduleValue[8][n+1].IsBlack=t[i]==1?!0:!1:this.moduleValue[8][n].IsBlack=t[i]==1?!0:!1,this.moduleValue[this.noOfModules-n-1][8].IsBlack=t[i--]==1?!0:!1;this.moduleValue[8][8].IsBlack=t[7]==1?!0:!1;this.moduleValue[8][this.noOfModules-8].IsBlack=t[7]==1?!0:!1},_dataAllocationAndMasking:function(n){var r=this.noOfModules,u,t,i,f;for(this.dataAllocationValues=this._create2DArray(r,r),u=0,t=0;t<r;t++)for(i=0;i<r;i++)this.dataAllocationValues[t][i]={IsBlack:!1,IsFilled:!1,IsPDP:!1};for(t=r-1;t>=0;t-=2){for(i=r-1;i>=0;i--)u=this._allocateValues(n,t,i,u);for(t-=2,t==6&&t--,i=0;i<r;i++)u=this._allocateValues(n,t,i,u)}for(t=0;t<r;t++)for(i=0;i<r;i++)this.moduleValue[t][i].IsFilled||(f=this.dataAllocationValues[t][i].IsBlack,this.dataAllocationValues[t][i].IsBlack=f?!1:!0)},_allocateValues:function(n,t,i,r){return this.moduleValue[t][i].IsFilled&&this.moduleValue[t-1][i].IsFilled||(this.moduleValue[t][i].IsFilled||(r+1<n.length&&(this.dataAllocationValues[t][i].IsBlack=n[r++]),this.dataAllocationValues[t][i].IsBlack=(t+i)%3==0?this.dataAllocationValues[t][i].IsBlack?!0:!1:this.dataAllocationValues[t][i].IsBlack?!1:!0,this.dataAllocationValues[t][i].IsFilled=!0),this.moduleValue[t-1][i].IsFilled||(r+1<n.length&&(this.dataAllocationValues[t-1][i].IsBlack=n[r++]),this.dataAllocationValues[t-1][i].IsBlack=(t-1+i)%3==0?this.dataAllocationValues[t-1][i].IsBlack?!0:!1:this.dataAllocationValues[t-1][i].IsBlack?!1:!0,this.dataAllocationValues[t-1][i].IsFilled=!0)),r},_encodeData:function(){var t=[],i=-1,b,c,f,tt,w,a,e,o,d,g,nt,it,k,rt,h,y,ut,n,s,u;switch(this.mode){case"numeric":t[++i]=!1;t[++i]=!1;t[++i]=!1;t[++i]=!0;break;case"alphanumeric":t[++i]=!1;t[++i]=!1;t[++i]=!0;t[++i]=!1;break;case"binary":if(this.isEci)for(t[++i]=!1,t[++i]=!0,t[++i]=!0,t[++i]=!0,o=this._stringToBoolArray(this.m_EciAssignmentNumber.toString(),8),b=0;b<o.length;b++)t[++i]=o[b];t[++i]=!1;t[++i]=!0;t[++i]=!1;t[++i]=!1}if(c=0,this.m_Version<10)switch(this.mode){case"numeric":c=10;break;case"alphanumeric":c=9;break;case"binary":c=8}else if(this.m_Version<27)switch(this.mode){case"numeric":c=12;break;case"alphanumeric":c=11;break;case"binary":c=16}else switch(this.mode){case"numeric":c=14;break;case"alphanumeric":c=13;break;case"binary":c=16}for(f=this.Model.text,tt=this._intToBoolArray(f.length,c),n=0;n<c;n++)t[++i]=tt[n];if(this.mode=="numeric"){for(e="",n=0;n<f.length;n++)if(o=[],e+=f[n],n%3==2&&n!=0||n==f.length-1)for(o=e.length==3?this._stringToBoolArray(e,10):e.length==2?this._stringToBoolArray(e,7):this._stringToBoolArray(e,4),e="",u=0;u<o.length;u++)t[++i]=o[u]}else if(this.mode=="alphanumeric")for(w="",e=0,n=0;n<f.length;n++){if(o=[],w+=f[n],n%2==0&&n+1!=f.length&&(e=45*this._getAlphanumericvalues(f[n])),n%2==1&&n!=0){for(e+=this._getAlphanumericvalues(f[n]),o=this._intToBoolArray(e,11),e=0,a=0;a<o.length;a++)t[++i]=o[a];w=""}if(n!=1&&w!=null&&n+1==f.length&&w.length==1)for(e=this._getAlphanumericvalues(f[n]),o=this._intToBoolArray(e,6),e=0,u=0;u<o.length;u++)t[++i]=o[u]}else if(this.mode="binary")for(n=0;n<f.length;n++){if(e=0,f.charCodeAt(n)>=32&&f.charCodeAt(n)<=126||f.charCodeAt(n)>=161&&f.charCodeAt(n)<=255)e=f.charCodeAt(n);else if(f.charCodeAt(n)>=65377&&f.charCodeAt(n)<=65439)e=f.charCodeAt(n)-65216;else{console.log("Input text contains non-convertable characters");return}for(o=this._intToBoolArray(e,8),u=0;u<o.length;u++)t[++i]=o[u]}for(n=0;n<4;n++)if(t.length/8==this.qrbarcodeValues.NumberOfDataCodeWord)break;else t[++i]=!1;for(;;)if(t.length%8==0)break;else t[++i]=!1;for(;;){if(t.length/8==this.qrbarcodeValues.NumberOfDataCodeWord)break;else t[++i]=!1,t[++i]=!0,t[++i]=!0,t[++i]=!1,t[++i]=!0,t[++i]=!0,t[++i]=!1,t[++i]=!1;if(t.length/8==this.qrbarcodeValues.NumberOfDataCodeWord)break;else t[++i]=!1,t[++i]=!1,t[++i]=!1,t[++i]=!0,t[++i]=!1,t[++i]=!1,t[++i]=!1,t[++i]=!0}var k=this.qrbarcodeValues.NumberOfDataCodeWord,r=this.qrbarcodeValues.NumberOfErrorCorrectionBlocks,v=r[0];r.length==6&&(v=r[0]+r[3]);var p=new Array(v),l=t,a=-1;if(r.length==6)for(d=r[0]*r[2]*8,l=new Array(d),n=0;n<d;n++)l[++a]=t[n];for(g=this._create2DArray(r[0],l.length/8/r[0]),g=this._createBlocks(l,r[0]),n=0;n<r[0];n++)p[n]=this._splitCodeWord(g,n,l.length/8/r[0]);if(a=-1,r.length==6){for(l=[],n=parseInt(r[0]*r[2]*8);n<t.length;n++)l[++a]=t[n];for(nt=this._create2DArray(r[0],l.length/8/r[3]),nt=this._createBlocks(l,r[3]),n=r[0],y=0;n<v;n++)p[n]=this._splitCodeWord(nt,y++,l.length/8/r[3])}for(t=[],i=-1,n=0;n<125;n++)for(s=0;s<v;s++)for(u=0;u<8;u++)n<p[s].length&&(it=p[s][n][u],t[++i]=it=="1"?!0:!1);for(this.corCodeWords={DataBits:this.qrbarcodeValues.NumberOfDataCodeWord,ECCW:this.qrbarcodeValues.NumberOfErrorCorrectingCodeWords,DC:0},k=this.corCodeWords.DataBits,rt=this.corCodeWords.ECCW,r=this.qrbarcodeValues.NumberOfErrorCorrectionBlocks,this.corCodeWords.DataBits=r.length==6?(k-r[3]*r[5])/r[0]:k/r[0],this.corCodeWords.ECCW=rt/v,h=new Array(v),y=0,n=0;n<r[0];n++)this.corCodeWords.DC=p[y],h[y++]=this._getERCW();if(r.length==6)for(this.corCodeWords.DataBits=(k-r[0]*r[2])/r[3],n=0;n<r[3];n++)this.corCodeWords.DC=p[y],h[y++]=this._getERCW();if(ut=h[0].slice(-1)[h.length-1],typeof ut=="undefined"&&(h[0].length=h[0].length-1),r.length!=6)for(n=0;n<h[0].length;n++)for(s=0;s<r[0];s++)for(u=0;u<8;u++)n<h[s].length&&(t[++i]=h[s][n][u]=="1"?!0:!1);else for(n=0;n<h[0].length;n++)for(s=0;s<v;s++)for(u=0;u<8;u++)n<h[s].length&&(t[++i]=h[s][n][u]=="1"?!0:!1);this._dataAllocationAndMasking(t)},_getERCW:function(){var i,t=new Array(this.corCodeWords.DataBits),n=[];switch(this.corCodeWords.ECCW){case 7:n=[0,87,229,146,149,238,102,21];n=this._getElement(n);break;case 10:n=[0,251,67,46,61,118,70,64,94,32,45];n=this._getElement(n);break;case 13:n=[0,74,152,176,100,86,100,106,104,130,218,206,140,78];n=this._getElement(n);break;case 15:n=[0,8,183,61,91,202,37,51,58,58,237,140,124,5,99,105];n=this._getElement(n);break;case 16:n=[0,120,104,107,109,102,161,76,3,91,191,147,169,182,194,225,120];n=this._getElement(n);break;case 17:n=[0,43,139,206,78,43,239,123,206,214,147,24,99,150,39,243,163,136];n=this._getElement(n);break;case 18:n=[0,215,234,158,94,184,97,118,170,79,187,152,148,252,179,5,98,96,153];n=this._getElement(n);break;case 20:n=[0,17,60,79,50,61,163,26,187,202,180,221,225,83,239,156,164,212,212,188,190];n=this._getElement(n);break;case 22:n=[0,210,171,247,242,93,230,14,109,221,53,200,74,8,172,98,80,219,134,160,105,165,231];n=this._getElement(n);break;case 24:n=[0,229,121,135,48,211,117,251,126,159,180,169,152,192,226,228,218,111,0,117,232,87,96,227,21];n=this._getElement(n);break;case 26:n=[0,173,125,158,2,103,182,118,17,145,201,111,28,165,53,161,21,245,142,13,102,48,227,153,145,218,70];n=this._getElement(n);break;case 28:n=[0,168,223,200,104,224,234,108,180,110,190,195,147,205,27,232,201,21,43,245,87,42,195,212,119,242,37,9,123];n=this._getElement(n);break;case 30:n=[0,41,173,145,152,216,31,179,182,50,48,110,86,239,96,222,125,42,173,226,193,224,130,156,37,251,216,238,40,192,180];n=this._getElement(n);break;case 32:n=[0,10,6,106,190,249,167,4,67,209,138,138,32,242,123,89,27,120,185,80,156,38,69,171,60,28,222,80,52,254,185,220,241];n=this._getElement(n);break;case 34:n=[0,111,77,146,94,26,21,108,19,105,94,113,193,86,140,163,125,58,158,229,239,218,103,56,70,114,61,183,129,167,13,98,62,129,51];n=this._getElement(n);break;case 36:n=[0,200,183,98,16,172,31,246,234,60,152,115,0,167,152,113,248,238,107,18,63,218,37,87,210,105,177,120,74,121,196,117,251,113,233,30,120];n=this._getElement(n);break;case 40:n=[0,59,116,79,161,252,98,128,205,128,161,247,57,163,56,235,106,53,26,187,174,226,104,170,7,175,35,181,114,88,41,47,163,125,134,72,20,232,53,35,15];n=this._getElement(n);break;case 42:n=[0,250,103,221,230,25,18,137,231,0,3,58,242,221,191,110,84,230,8,188,106,96,147,15,131,139,34,101,223,39,101,213,199,237,254,201,123,171,162,194,117,50,96];n=this._getElement(n);break;case 44:n=[0,190,7,61,121,71,246,69,55,168,188,89,243,191,25,72,123,9,145,14,247,1,238,44,78,143,62,224,126,118,114,68,163,52,194,217,147,204,169,37,130,113,102,73,181];n=this._getElement(n);break;case 46:n=[0,112,94,88,112,253,224,202,115,187,99,89,5,54,113,129,44,58,16,135,216,169,211,36,1,4,96,60,241,73,104,234,8,249,245,119,174,52,25,157,224,43,202,223,19,82,15];n=this._getElement(n);break;case 48:n=[0,228,25,196,130,211,146,60,24,251,90,39,102,240,61,178,63,46,123,115,18,221,111,135,160,182,205,107,206,95,150,120,184,91,21,247,156,140,238,191,11,94,227,84,50,163,39,34,108];n=this._getElement(n);break;case 50:n=[0,232,125,157,161,164,9,118,46,209,99,203,193,35,3,209,111,195,242,203,225,46,13,32,160,126,209,130,160,242,215,242,75,77,42,189,32,113,65,124,69,228,114,235,175,124,170,215,232,133,205];n=this._getElement(n);break;case 52:n=[0,116,50,86,186,50,220,251,89,192,46,86,127,124,19,184,233,151,215,22,14,59,145,37,242,203,134,254,89,190,94,59,65,124,113,100,233,235,121,22,76,86,97,39,242,200,220,101,33,239,254,116,51];n=this._getElement(n);break;case 54:n=[0,183,26,201,87,210,221,113,21,46,65,45,50,238,184,249,225,102,58,209,218,109,165,26,95,184,192,52,245,35,254,238,175,172,79,123,25,122,43,120,108,215,80,128,201,235,8,153,59,101,31,198,76,31,156];n=this._getElement(n);break;case 56:n=[0,106,120,107,157,164,216,112,116,2,91,248,163,36,201,202,229,6,144,254,155,135,208,170,209,12,139,127,142,182,249,177,174,190,28,10,85,239,184,101,124,152,206,96,23,163,61,27,196,247,151,154,202,207,20,61,10];n=this._getElement(n);break;case 58:n=[0,82,116,26,247,66,27,62,107,252,182,200,185,235,55,251,242,210,144,154,237,176,141,192,248,152,249,206,85,253,142,65,165,125,23,24,30,122,240,214,6,129,218,29,145,127,134,206,245,117,29,41,63,159,142,233,125,148,123];n=this._getElement(n);break;case 60:n=[0,107,140,26,12,9,141,243,197,226,197,219,45,211,101,219,120,28,181,127,6,100,247,2,205,198,57,115,219,101,109,160,82,37,38,238,49,160,209,121,86,11,124,30,181,84,25,194,87,65,102,190,220,70,27,209,16,89,7,33,240];n=this._getElement(n);break;case 62:n=[0,65,202,113,98,71,223,248,118,214,94,0,122,37,23,2,228,58,121,7,105,135,78,243,118,70,76,223,89,72,50,70,111,194,17,212,126,181,35,221,117,235,11,229,149,147,123,213,40,115,6,200,100,26,246,182,218,127,215,36,186,110,106];n=this._getElement(n);break;case 64:n=[0,45,51,175,9,7,158,159,49,68,119,92,123,177,204,187,254,200,78,141,149,119,26,127,53,160,93,199,212,29,24,145,156,208,150,218,209,4,216,91,47,184,146,47,140,195,195,125,242,238,63,99,108,140,230,242,31,204,11,178,243,217,156,213,231];n=this._getElement(n);break;case 66:n=[0,5,118,222,180,136,136,162,51,46,117,13,215,81,17,139,247,197,171,95,173,65,137,178,68,111,95,101,41,72,214,169,197,95,7,44,154,77,111,236,40,121,143,63,87,80,253,240,126,217,77,34,232,106,50,168,82,76,146,67,106,171,25,132,93,45,105];n=this._getElement(n);break;case 68:n=[0,247,159,223,33,224,93,77,70,90,160,32,254,43,150,84,101,190,205,133,52,60,202,165,220,203,151,93,84,15,84,253,173,160,89,227,52,199,97,95,231,52,177,41,125,137,241,166,225,118,2,54,32,82,215,175,198,43,238,235,27,101,184,127,3,5,8,163,238];n=this._getElement(n)}return t=this._toDecimal(this.corCodeWords.DC,t),i=this._divide(n,t),this._toBinary(i)},_toBinary:function(n){for(var i,r,u,f=new Array(this.corCodeWords.ECCW),t=0;t<n.length;t++){for(i=n[t].toString(2),r=new String,u=0;u<8-i.length;u++)r+="0";f[t]=r+i}return f},_divide:function(n,t){for(var r,h,u,c,e,s,o=[1,2,4,8,16,32,64,128,29,58,116,232,205,135,19,38,76,152,45,90,180,117,234,201,143,3,6,12,24,48,96,192,157,39,78,156,37,74,148,53,106,212,181,119,238,193,159,35,70,140,5,10,20,40,80,160,93,186,105,210,185,111,222,161,95,190,97,194,153,47,94,188,101,202,137,15,30,60,120,240,253,231,211,187,107,214,177,127,254,225,223,163,91,182,113,226,217,175,67,134,17,34,68,136,13,26,52,104,208,189,103,206,129,31,62,124,248,237,199,147,59,118,236,197,151,51,102,204,133,23,46,92,184,109,218,169,79,158,33,66,132,21,42,84,168,77,154,41,82,164,85,170,73,146,57,114,228,213,183,115,230,209,191,99,198,145,63,126,252,229,215,179,123,246,241,255,227,219,171,75,150,49,98,196,149,55,110,220,165,87,174,65,130,25,50,100,200,141,7,14,28,56,112,224,221,167,83,166,81,162,89,178,121,242,249,239,195,155,43,86,172,69,138,9,18,36,72,144,61,122,244,245,247,243,251,235,203,139,11,22,44,88,176,125,250,233,207,131,27,54,108,216,173,71,142],l=this.corCodeWords.ECCW,f=[],i=0;i<t.length;i++)f.push({Exponent:t.length-1-i,Coefficient:t[i]});for(r=[],i=0;i<n.length;i++)r.push({Exponent:n.length-1-i,Coefficient:this._findElement(n[i],o)});for(i=0;i<f.length;i++)f[i].Exponent=f[i].Exponent+l;for(h=f[0].Exponent-r[0].Exponent,i=0;i<r.length;i++)r[i].Coefficient=r[i].Coefficient,r[i].Exponent=r[i].Exponent+h;for(u=f,i=0;i<f.length;i++)u[0].Coefficient==0?u.splice(0,1):(c=this._convertToAlphaNotation(u,o),e=this._multiplyGeneratorPolynomByLeadterm(r,c[0],i),e=this._convertToDecNotation(e,o),e=this._XORPolynoms(u,e),u=e);for(s=[],i=0;i<u.length;i++)s.push(u[i].Coefficient);return s},_convertToAlphaNotation:function(n,t){for(var r=[],i=0;i<n.length;i++)n[i].Coefficient!=0&&r.push({Exponent:n[i].Exponent,Coefficient:this._findElement(n[i].Coefficient,t)});return r},_multiplyGeneratorPolynomByLeadterm:function(n,t,i){for(var u=[],r=0;r<n.length;r++)u.push({Exponent:n[r].Exponent-i,Coefficient:(n[r].Coefficient+t.Coefficient)%255});return u},_convertToDecNotation:function(n,t){for(var r=[],i=0;i<n.length;i++)r.push({Exponent:n[i].Exponent,Coefficient:this._getIntValFromAlphaExp(n[i].Coefficient,t)});return r},_getIntValFromAlphaExp:function(n,t){return n>255&&(n=n-255),t[n]},_XORPolynoms:function(n,t){var f=[],r=[],u=[],i;for(n.length>=t.length?(r=n,u=t):(r=t,u=n),i=0;i<r.length;i++)f.push({Exponent:n[0].Exponent-i,Coefficient:r[i].Coefficient^(u.length>i?u[i].Coefficient:0)});return f.splice(0,1),f},_findElement:function(n,t){for(var i=0;i<t.length;i++)if(n==t[i])break;return i},_getElement:function(n){for(var i=new Array(n.length),r=[1,2,4,8,16,32,64,128,29,58,116,232,205,135,19,38,76,152,45,90,180,117,234,201,143,3,6,12,24,48,96,192,157,39,78,156,37,74,148,53,106,212,181,119,238,193,159,35,70,140,5,10,20,40,80,160,93,186,105,210,185,111,222,161,95,190,97,194,153,47,94,188,101,202,137,15,30,60,120,240,253,231,211,187,107,214,177,127,254,225,223,163,91,182,113,226,217,175,67,134,17,34,68,136,13,26,52,104,208,189,103,206,129,31,62,124,248,237,199,147,59,118,236,197,151,51,102,204,133,23,46,92,184,109,218,169,79,158,33,66,132,21,42,84,168,77,154,41,82,164,85,170,73,146,57,114,228,213,183,115,230,209,191,99,198,145,63,126,252,229,215,179,123,246,241,255,227,219,171,75,150,49,98,196,149,55,110,220,165,87,174,65,130,25,50,100,200,141,7,14,28,56,112,224,221,167,83,166,81,162,89,178,121,242,249,239,195,155,43,86,172,69,138,9,18,36,72,144,61,122,244,245,247,243,251,235,203,139,11,22,44,88,176,125,250,233,207,131,27,54,108,216,173,71,142],t=0;t<n.length;t++)n[t]>255&&(n[t]=n[t]-255),i[t]=r[n[t]];return i},_toDecimal:function(n){for(var i=new Array(n.length),t=0;t<n.length;t++)i[t]=parseInt(n[t],2);return i},_splitCodeWord:function(n,t,i){var u,r;for(i=parseInt(i),u=new Array(i),r=0;r<i;r++)u[r]=n[t][r];return u},_createBlocks:function(n,t){for(var f=this._create2DArray(t,n.length/8/t),u="",i=0,r=0,e=0;i<n.length;i++)i%8==0&&i!=0&&(f[e][r]=u,u="",r++,r==n.length/t/8&&(e++,r=0)),u+=n[i]?1:0;return f[e][r]=u,f},_getAlphanumericvalues:function(n){return["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"].indexOf(n)},_stringToBoolArray:function(n,t){for(var u=new Array(t),r=0,i=0;i<n.length;i++)r=r*10+n.charCodeAt(i)-48;for(i=0;i<t;i++)u[t-i-1]=(r>>i&1)==1;return u},_intToBoolArray:function(n,t){for(var r=new Array(t),i=0;i<t;i++)r[t-i-1]=(n>>i&1)==1;return r},_drawPDP:function(n,t){for(var i=n,r=t;i<n+7;i++,r++)this.moduleValue[i][t].IsBlack=!0,this.moduleValue[i][t].IsFilled=!0,this.moduleValue[i][t].IsPDP=!0,this.moduleValue[i][t+6].IsBlack=!0,this.moduleValue[i][t+6].IsFilled=!0,this.moduleValue[i][t+6].IsPDP=!0,t+7<this.noOfModules?(this.moduleValue[i][t+7].IsBlack=!1,this.moduleValue[i][t+7].IsFilled=!0,this.moduleValue[i][t+7].IsPDP=!0):t-1>=0&&(this.moduleValue[i][t-1].IsBlack=!1,this.moduleValue[i][t-1].IsFilled=!0,this.moduleValue[i][t-1].IsPDP=!0),this.moduleValue[n][r].IsBlack=!0,this.moduleValue[n][r].IsFilled=!0,this.moduleValue[n][r].IsPDP=!0,this.moduleValue[n+6][r].IsBlack=!0,this.moduleValue[n+6][r].IsFilled=!0,this.moduleValue[n+6][r].IsPDP=!0,n+7<this.noOfModules?(this.moduleValue[n+7][r].IsBlack=!1,this.moduleValue[n+7][r].IsFilled=!0,this.moduleValue[n+7][r].IsPDP=!0):n-1>=0&&(this.moduleValue[n-1][r].IsBlack=!1,this.moduleValue[n-1][r].IsFilled=!0,this.moduleValue[n-1][r].IsPDP=!0);for(n+7<this.noOfModules&&t+7<this.noOfModules?(this.moduleValue[n+7][t+7].IsBlack=!1,this.moduleValue[n+7][t+7].IsFilled=!0,this.moduleValue[n+7][t+7].IsPDP=!0):n+7<this.noOfModules&&t+7>=this.noOfModules?(this.moduleValue[n+7][t-1].IsBlack=!1,this.moduleValue[n+7][t-1].IsFilled=!0,this.moduleValue[n+7][t-1].IsPDP=!0):n+7>=this.noOfModules&&t+7<this.noOfModules&&(this.moduleValue[n-1][t+7].IsBlack=!1,this.moduleValue[n-1][t+7].IsFilled=!0,this.moduleValue[n-1][t+7].IsPDP=!0),n++,t++,i=n,r=t;i<n+5;i++,r++)this.moduleValue[i][t].IsBlack=!1,this.moduleValue[i][t].IsFilled=!0,this.moduleValue[i][t].IsPDP=!0,this.moduleValue[i][t+4].IsBlack=!1,this.moduleValue[i][t+4].IsFilled=!0,this.moduleValue[i][t+4].IsPDP=!0,this.moduleValue[n][r].IsBlack=!1,this.moduleValue[n][r].IsFilled=!0,this.moduleValue[n][r].IsPDP=!0,this.moduleValue[n+4][r].IsBlack=!1,this.moduleValue[n+4][r].IsFilled=!0,this.moduleValue[n+4][r].IsPDP=!0;for(n++,t++,i=n,r=t;i<n+3;i++,r++)this.moduleValue[i][t].IsBlack=!0,this.moduleValue[i][t].IsFilled=!0,this.moduleValue[i][t].IsPDP=!0,this.moduleValue[i][t+2].IsBlack=!0,this.moduleValue[i][t+2].IsFilled=!0,this.moduleValue[i][t+2].IsPDP=!0,this.moduleValue[n][r].IsBlack=!0,this.moduleValue[n][r].IsFilled=!0,this.moduleValue[n][r].IsPDP=!0,this.moduleValue[n+2][r].IsBlack=!0,this.moduleValue[n+2][r].IsFilled=!0,this.moduleValue[n+2][r].IsPDP=!0;this.moduleValue[n+1][t+1].IsBlack=!0;this.moduleValue[n+1][t+1].IsFilled=!0;this.moduleValue[n+1][t+1].IsPDP=!0},_drawAlignmentPattern:function(n,t){for(var i=n-2,r=t-2;i<n+3;i++,r++)this.moduleValue[i][t-2].IsBlack=!0,this.moduleValue[i][t-2].IsFilled=!0,this.moduleValue[i][t+2].IsBlack=!0,this.moduleValue[i][t+2].IsFilled=!0,this.moduleValue[n-2][r].IsBlack=!0,this.moduleValue[n-2][r].IsFilled=!0,this.moduleValue[n+2][r].IsBlack=!0,this.moduleValue[n+2][r].IsFilled=!0;for(i=n-1,r=t-1;i<n+2;i++,r++)this.moduleValue[i][t-1].IsBlack=!1,this.moduleValue[i][t-1].IsFilled=!0,this.moduleValue[i][t+1].IsBlack=!1,this.moduleValue[i][t+1].IsFilled=!0,this.moduleValue[n-1][r].IsBlack=!1,this.moduleValue[n-1][r].IsFilled=!0,this.moduleValue[n+1][r].IsBlack=!1,this.moduleValue[n+1][r].IsFilled=!0;this.moduleValue[n][t].IsBlack=!0;this.moduleValue[n][t].IsFilled=!0},_getAlignmentPatternCoOrdinates:function(){var n=[];switch(this.m_Version){case 2:n=[6,18];break;case 3:n=[6,22];break;case 4:n=[6,26];break;case 5:n=[6,30];break;case 6:n=[6,34];break;case 7:n=[6,22,38];break;case 8:n=[6,24,42];break;case 9:n=[6,26,46];break;case 10:n=[6,28,50];break;case 11:n=[6,30,54];break;case 12:n=[6,32,58];break;case 13:n=[6,34,62];break;case 14:n=[6,26,46,66];break;case 15:n=[6,26,48,70];break;case 16:n=[6,26,50,74];break;case 17:n=[6,30,54,78];break;case 18:n=[6,30,56,82];break;case 19:n=[6,30,58,86];break;case 20:n=[6,34,62,90];break;case 21:n=[6,28,50,72,94];break;case 22:n=[6,26,50,74,98];break;case 23:n=[6,30,54,78,102];break;case 24:n=[6,28,54,80,106];break;case 25:n=[6,32,58,84,110];break;case 26:n=[6,30,58,86,114];break;case 27:n=[6,34,62,90,118];break;case 28:n=[6,26,50,74,98,122];break;case 29:n=[6,30,54,78,102,126];break;case 30:n=[6,26,52,78,104,130];break;case 31:n=[6,30,56,82,108,134];break;case 32:n=[6,34,60,86,112,138];break;case 33:n=[6,30,58,86,114,142];break;case 34:n=[6,34,62,90,118,146];break;case 35:n=[6,30,54,78,102,126,150];break;case 36:n=[6,24,50,76,102,128,154];break;case 37:n=[6,28,54,80,106,132,158];break;case 38:n=[6,32,58,84,110,136,162];break;case 39:n=[6,26,54,82,110,138,166];break;case 40:n=[6,30,58,86,114,142,170]}return n},_findECINumber:function(n){for(var r,i=3,u=0;u<n.length;u++)if(r=n.charCodeAt(u),!(r>=32)||!(r<=255)){var t=r.toString(16);if(["2591","2592","2593","2502","2524","2561","2562","2556","2555","2563","2551","2557","255D","255C","255B","2510","2514","2534","252C","251C","2500","253C","255E","255F","255A","2554","2569","2566","2560","2550","256C","2567","2568","2564","2565","2559","2558","2552","2553","256B","256A","2518","250C","2588","2584","258C","2590","2580","25A0"].indexOf(t)!=-1){i=2;break}else if(["104","2D8","141","13D","15A","160","15E","164","179","17D","17B","105","2DB","142","13E","15B","2C7","161","15F","165","17A","2DD","17E","17C","154","102","139","106","10C","118","11A","10E","110","143","147","150","158","16E","170","162","155","103","13A","107","10D","119","11B","10F","111","144","148","151","159","16F","171","163","2D9"].indexOf(t)!=-1){i=4;break}else if(["126","124","130","15E","11E","134","17B","127","125","131","15F","11F","135","17C","10A","108","120","11C","16C","15C","10B","109","121","11D","16D","15D"].indexOf(t)!=-1){i=5;break}else if(["104","138","156","128","13B","160","112","122","166","17D","105","2DB","157","129","13C","2C7","161","113","123","167","14A","17E","14B","100","12E","10C","118","116","12A","110","145","14C","136","172","168","16A","101","12F","10D","119","117","12B","111","146","14D","137","173","169","16B"].indexOf(t)!=-1){i=6;break}else if(t>=1025&&t<=1119&&t!=1037&&t!=1104&&t!=1117){i=7;break}else if(t>=1569&&t<=1594||t>=1600&&t<=1618||t==1567||t==1563||t==1548){i=8;break}else if(t>=900&&t<=974||t==890){i=9;break}else if(t>=1488&&t<=1514){i=10;break}else if(t>=3585&&t<=3675){i=13;break}else if(["141","104","15E","17B","142","105","15F","13D","13E","17C"].indexOf(t)!=1||t>=1569&&t<=1610){i=21;break}else if(["402","403","453","409","40A","40C","40B","40F","452","459","45A","45C","45B","45F","40E","45E","408","490","401","404","407","406","456","491","451","454","458","405","455","457"].indexOf(t)!=1||t>=1040&&t<=1103){i=22;break}else if(["20AC","201A","192","201E","2026","2020","2021","2C6","2030","160","2039","152","17D","2018","2019","201C","201D","2022","2013","2014","2DC","2122","161","203A","153","17E","178"].indexOf(t)!=-1){i=23;break}else if(["67E","679","152","686","698","688","6AF","6A9","691","153","6BA","6BE","6C1"].indexOf(t)!=-1){i=24;break}}return i},_getNumberOfDataCodeWord:function(){var n=0;switch(this.m_Version){case 1:n=16;break;case 2:n=28;break;case 3:n=44;break;case 4:n=64;break;case 5:n=86;break;case 6:n=108;break;case 7:n=124;break;case 8:n=154;break;case 9:n=182;break;case 10:n=216;break;case 11:n=254;break;case 12:n=290;break;case 13:n=334;break;case 14:n=365;break;case 15:n=415;break;case 16:n=453;break;case 17:n=507;break;case 18:n=563;break;case 19:n=627;break;case 20:n=669;break;case 21:n=714;break;case 22:n=782;break;case 23:n=860;break;case 24:n=914;break;case 25:n=1e3;break;case 26:n=1062;break;case 27:n=1128;break;case 28:n=1193;break;case 29:n=1267;break;case 30:n=1373;break;case 31:n=1455;break;case 32:n=1541;break;case 33:n=1631;break;case 34:n=1725;break;case 35:n=1812;break;case 36:n=1914;break;case 37:n=1992;break;case 38:n=2102;break;case 39:n=2216;break;case 40:n=2334}return n},_getNumberOfErrorCorrectingCodeWords:function(){var n=(this.m_Version-1)*4,t;return n+=1,t=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430],t[n]},_getNumberOfErrorCorrectionBlocks:function(){var n=[];switch(this.m_Version){case 1:case 2:case 3:n[0]=1;break;case 4:case 5:n[0]=2;break;case 6:case 7:n[0]=4;break;case 8:n=[2,60,38,2,61,39];break;case 9:n=[3,58,36,2,59,37];break;case 10:n=[4,69,43,1,70,44];break;case 11:n=[1,80,50,4,81,51];break;case 12:n=[6,58,36,2,59,37];break;case 13:n=[8,59,37,1,60,38];break;case 14:n=[4,64,40,5,65,41];break;case 15:n=[5,65,41,5,66,42];break;case 16:n=[7,73,45,3,74,46];break;case 17:n=[10,74,46,1,75,47];break;case 18:n=[9,69,43,4,70,44];break;case 19:n=[3,70,44,11,71,45];break;case 20:n=[3,67,41,13,68,42];break;case 21:case 22:n[0]=17;break;case 23:n=[4,75,47,14,76,48];break;case 24:n=[6,73,45,14,74,46];break;case 25:n=[8,75,47,13,76,48];break;case 26:n=[19,74,46,4,75,47];break;case 27:n=[22,73,45,3,74,46];break;case 28:n=[3,73,45,23,74,46];break;case 29:n=[21,73,45,7,74,46];break;case 30:n=[19,75,47,10,76,48];break;case 31:n=[2,74,46,29,75,47];break;case 32:n=[10,74,46,23,75,47];break;case 33:n=[14,74,46,21,75,47];break;case 34:n=[14,74,46,23,75,47];break;case 35:n=[12,75,47,26,76,48];break;case 36:n=[6,75,47,34,76,48];break;case 37:n=[29,74,46,14,75,47];break;case 38:n=[13,74,46,32,75,47];break;case 39:n=[40,75,47,7,76,48];break;case 40:n=[18,75,47,31,76,48]}return n},_getEnd:function(){return[208,359,567,807,1079,1383,1568,1936,2336,2768,3232,3728,4256,4651,5243,5867,6523,7211,7931,8683,9252,10068,10916,11796,12708,13652,14628,15371,16411,17483,18587,19723,20891,22091,23008,24272,25568,26896,28256,29648][this.m_Version-1]},_getDataCapacity:function(){return[26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706][this.m_Version-1]},_getFormatInformation:function(){return[1,1,0,1,0,0,1,0,1,1,0,1,1,0,1]},_getVersionInformation:function(){var n=[];switch(this.m_Version){case 7:n=[0,0,1,0,1,0,0,1,0,0,1,1,1,1,1,0,0,0];break;case 8:n=[0,0,1,1,1,1,0,1,1,0,1,0,0,0,0,1,0,0];break;case 9:n=[1,0,0,1,1,0,0,1,0,1,0,1,1,0,0,1,0,0];break;case 10:n=[1,1,0,0,1,0,1,1,0,0,1,0,0,1,0,1,0,0];break;case 11:n=[0,1,1,0,1,1,1,1,1,1,0,1,1,1,0,1,0,0];break;case 12:n=[0,1,0,0,0,1,1,0,1,1,1,0,0,0,1,1,0,0];break;case 13:n=[1,1,1,0,0,0,1,0,0,0,0,1,1,0,1,1,0,0];break;case 14:n=[1,0,1,1,0,0,0,0,0,1,1,0,0,1,1,1,0,0];break;case 15:n=[0,0,0,1,0,1,0,0,1,0,0,1,1,1,1,1,0,0];break;case 16:n=[0,0,0,1,1,1,1,0,1,1,0,1,0,0,0,0,1,0];break;case 17:n=[1,0,1,1,1,0,1,0,0,0,1,0,1,0,0,0,1,0];break;case 18:n=[1,1,1,0,1,0,0,0,0,1,0,1,0,1,0,0,1,0];break;case 19:n=[0,1,0,0,1,1,0,0,1,0,1,0,1,1,0,0,1,0];break;case 20:n=[0,1,1,0,0,1,0,1,1,0,0,1,0,0,1,0,1,0];break;case 21:n=[1,1,0,0,0,0,0,1,0,1,1,0,1,0,1,0,1,0];break;case 22:n=[1,0,0,1,0,0,1,1,0,0,0,1,0,1,1,0,1,0];break;case 23:n=[0,0,1,1,0,1,1,1,1,1,1,0,1,1,1,0,1,0];break;case 24:n=[0,0,1,0,0,0,1,1,0,1,1,1,0,0,0,1,1,0];break;case 25:n=[1,0,0,0,0,1,1,1,1,0,0,0,1,0,0,1,1,0];break;case 26:n=[1,1,0,1,0,1,0,1,1,1,1,1,0,1,0,1,1,0];break;case 27:n=[0,1,1,1,0,0,0,1,0,0,0,0,1,1,0,1,1,0];break;case 28:n=[0,1,0,1,1,0,0,0,0,0,1,1,0,0,1,1,1,0];break;case 29:n=[1,1,1,1,1,1,0,0,1,1,0,0,1,0,1,1,1,0];break;case 30:n=[1,0,1,0,1,1,1,0,1,0,1,1,0,1,1,1,1,0];break;case 31:n=[0,0,0,0,1,0,1,0,0,1,0,0,1,1,1,1,1,0];break;case 32:n=[1,0,1,0,1,0,1,1,1,0,0,1,0,0,0,0,0,1];break;case 33:n=[0,0,0,0,1,1,1,1,0,1,1,0,1,0,0,0,0,1];break;case 34:n=[0,1,0,1,1,1,0,1,0,0,0,1,0,1,0,0,0,1];break;case 35:n=[1,1,1,1,1,0,0,1,1,1,1,0,1,1,0,0,0,1];break;case 36:n=[1,1,0,1,0,0,0,0,1,1,0,1,0,0,1,0,0,1];break;case 37:n=[0,1,1,1,0,1,0,0,0,0,1,0,1,0,1,0,0,1];break;case 38:n=[0,0,1,0,0,1,1,0,0,1,0,1,0,1,1,0,0,1];break;case 39:n=[1,0,0,0,0,0,1,0,1,0,1,0,1,1,1,0,0,1];break;case 40:n=[1,0,0,1,0,1,1,0,0,0,1,1,0,0,0,1,0,1]}return n},_getStringCodePoints:function(){function n(n,t){return((n&1023)<<10)+(t&1023)+65536}return function(t){for(var u=[],i=0,r;i<t.length;)r=t.charCodeAt(i),(r&63488)==55296?u.push(n(r,t.charCodeAt(++i))):u.push(r),++i;return u}}(),_create2DArray:function(n,t){var r,i;for(n=parseInt(n),t=parseInt(t),r=new Array(n),i=0;i<n;i++)r[i]=new Array(t);return r},_buildDataMatrix:function(){for(var f,s,h=[[10,10,1,1,3,5,1,3],[12,12,1,1,5,7,1,5],[14,14,1,1,8,10,1,8],[16,16,1,1,12,12,1,12],[18,18,1,1,18,14,1,18],[20,20,1,1,22,18,1,22],[22,22,1,1,30,20,1,30],[24,24,1,1,36,24,1,36],[26,26,1,1,44,28,1,44],[32,32,2,2,62,36,1,62],[36,36,2,2,86,42,1,86],[40,40,2,2,114,48,1,114],[44,44,2,2,144,56,1,144],[48,48,2,2,174,68,1,174],[52,52,2,2,204,84,2,102],[64,64,4,4,280,112,2,140],[72,72,4,4,368,144,4,92],[80,80,4,4,456,192,4,114],[88,88,4,4,576,224,4,144],[96,96,4,4,696,272,4,174],[104,104,4,4,816,336,6,136],[120,120,6,6,1050,408,6,175],[132,132,6,6,1304,496,8,163],[144,144,6,6,1558,620,10,156],[8,18,1,1,5,7,1,5],[8,32,2,1,10,11,1,10],[12,26,1,1,16,14,1,16],[12,36,2,1,22,18,1,22],[16,36,2,1,32,24,1,32],[16,48,2,1,49,28,1,49]],u=this._getStringCodePoints(this.Model.text),l,i,t=[],e=0,n=0;n<u.length;n++)u[n]<127?t[e]=u[n]+1:(t[e++]=235,t[e]=u[n]-127),e++;for(n=0;n<h.length;n++)if(h[n][4]>=u.length){i=h[n];break}if(i[4]>t.length){var r=t.length,o=new Array(i[4]);for(f=0;f<r;f++)o[f]=t[f];for(r<i[4]+1&&(o[r]=129),r++;r<i[4];)s=129+(r+1)*149%253+1,s>254&&(s-=254),o[r++]=s;t=o}l=i[5];var y=this._errorCorrection(l,t),p=t.concat(y),c=this._createMatrix(p,i),a=c.length*this.Model.xDimension,v=c[0].length*this.Model.xDimension;this.contextEl.beginPath();this.contextEl.fillStyle="white";this.contextEl.fillRect(0,0,a,v);this.contextEl.closePath();this._drawDataMatrix(c,a,v)},_drawDataMatrix:function(n,t,i){var r=parseInt(this.Model.xDimension),o=this.textFont.size,a,s,u,h,f,c,l,e;for(n.length==n[0].length&&this.Model.height!=""&&this.Model.width!=""?(this.Model.height=this._getProperValue(this.Model.height),this.Model.width=this._getProperValue(this.Model.width),a=Math.min(this.Model.width,this.Model.height),r=a/n.length):(this.Model.height=i,this.Model.height+=o+this.Model.barcodeToTextGapHeight,this.Model.width=t,this.canvasEl[0].setAttribute("width",this.Model.width),this.BarcodeEl.css("width",this.Model.width),this.canvasEl[0].setAttribute("height",this.Model.height),this.BarcodeEl.css("height",this.Model.height)),s=0,this.contextEl.beginPath(),u=0;u<n.length;u++){for(h=0,f=0;f<n[u].length;f++)c=null,c=n[u][f]==1?this.model.darkBarColor:this.model.lightBarColor,this.contextEl.fillStyle=c,this.contextEl.fillRect(h,s,r,r),h+=r;s+=r}this.Model.displayText&&(l=this.textFont.fontStyle.toLowerCase()=="regular"?"":+this.textFont.fontStyle.toLowerCase()+" ",l+=this.textFont.size+"px "+this.textFont.fontFamily.toLowerCase()+",sans-serif",this.contextEl.font=l,e=this.Model.height,this.contextEl.beginPath(),this.contextEl.fillStyle=this.Model.textColor,this.contextEl.textAlign="center",e=e-o+o/2,this.contextEl.fillText(this.Model.text,this.Model.width/2,e));this.contextEl.closePath();this.contextEl.stroke()},_createMatrix:function(n,t){var i,r,c,l,d=[],e=t[1],s=t[0],b=e/t[2],k=s/t[3],o,a,rt,p,w,g,u,f;for(c=e-2*(e/b),l=s-2*(s/k),d=new Array(c*l),this._ecc200placement(d,l,c),o=new Array(e*s),u=0;u<o.length;u++)o[u]=0;for(r=0;r<s;r+=k){for(i=0;i<e;i++)o[parseInt(r*e)+i]=1;for(i=0;i<e;i+=2)o[parseInt((r+k-1)*e)+i]=1}for(i=0;i<e;i+=b){for(r=0;r<s;r++)o[parseInt(r*e)+i]=1;for(r=0;r<s;r+=2)o[parseInt(r*e)+i+b-1]=1}for(r=0;r<l;r++)for(i=0;i<c;i++)a=d[(l-r-1)*c+i],(a==1||a>7&&(n[(a>>3)-1]&1<<(a&7))!=0)&&(rt=parseInt(parseInt(1+r+2*parseInt(r/(k-2)))*e+1+i+ +(2*parseInt(i/(b-2)))),o[rt]=1);var v=t[1],y=t[0],ut=this._create2DArray(v,y);for(p=0;p<v;p++)for(w=0;w<y;w++)ut[p][w]=o[v*w+p];for(g=this._create2DArray(y,v),u=0;u<y;u++)for(f=0;f<v;f++)g[y-1-u][f]=ut[f][u];var nt=t[0]+(this.Model.quietZone.top+this.Model.quietZone.bottom),tt=t[1]+(this.Model.quietZone.left+this.Model.quietZone.right),h=this.Model.quietZone.all,it=this._create2DArray(nt,tt);for(u=0;u<nt;u++)for(f=0;f<tt;f++)it[u][f]=0;for(u=h;u<nt-h;u++)for(f=h;f<tt-h;f++)it[u][f]=g[u-h][f-h];return it},_ecc200placement:function(n,t,i){for(var u,f,r=0;r<t;r++)for(u=0;u<i;u++)n[r*i+u]=0;f=1;r=4;u=0;do{r!=t||u!=0||this._ecc200placementcornerA(n,t,i,f++);r!=t-2||u!=0||i%4==0||this._ecc200placementcornerB(n,t,i,f++);r!=t-2||u!=0||i%8!=4||this._ecc200placementcornerC(n,t,i,f++);r!=t+4||u!=2||i%8!=0||this._ecc200placementcornerD(n,t,i,f++);do r<t&&u>=0&&!(n[r*i+u]!=0)&&this._ecc200placementblock(n,t,i,r,u,f++),r-=2,u+=2;while(r>=0&&u<i);r++;u+=3;do r>-1&&u<i&&!(n[r*i+u]!=0)&&this._ecc200placementblock(n,t,i,r,u,f++),r+=2,u-=2;while(r<t&&u>=0);r+=3;u++}while(r<t||u<i);n[t*i-1]!=0||(n[t*i-1]=n[t*i-i-2]=1)},_ecc200placementcornerA:function(n,t,i,r){this._ecc200placementbit(n,t,i,t-1,0,r,7);this._ecc200placementbit(n,t,i,t-1,1,r,6);this._ecc200placementbit(n,t,i,t-1,2,r,5);this._ecc200placementbit(n,t,i,0,i-2,r,4);this._ecc200placementbit(n,t,i,0,i-1,r,3);this._ecc200placementbit(n,t,i,1,i-1,r,2);this._ecc200placementbit(n,t,i,2,i-1,r,1);this._ecc200placementbit(n,t,i,3,i-1,r,0)},_ecc200placementcornerB:function(n,t,i,r){this._ecc200placementbit(n,t,i,t-3,0,r,7);this._ecc200placementbit(n,t,i,t-2,0,r,6);this._ecc200placementbit(n,t,i,t-1,0,r,5);this._ecc200placementbit(n,t,i,0,i-4,r,4);this._ecc200placementbit(n,t,i,0,i-3,r,3);this._ecc200placementbit(n,t,i,0,i-2,r,2);this._ecc200placementbit(n,t,i,0,i-1,r,1);this._ecc200placementbit(n,t,i,1,i-1,r,0)},_ecc200placementcornerC:function(n,t,i,r){this._ecc200placementbit(n,t,i,t-3,0,r,7);this._ecc200placementbit(n,t,i,t-2,0,r,6);this._ecc200placementbit(n,t,i,t-1,0,r,5);this._ecc200placementbit(n,t,i,0,i-2,r,4);this._ecc200placementbit(n,t,i,0,i-1,r,3);this._ecc200placementbit(n,t,i,1,i-1,r,2);this._ecc200placementbit(n,t,i,2,i-1,r,1);this._ecc200placementbit(n,t,i,3,i-1,r,0)},_ecc200placementcornerD:function(n,t,i,r){this._ecc200placementbit(n,t,i,t-1,0,r,7);this._ecc200placementbit(n,t,i,t-1,i-1,r,6);this._ecc200placementbit(n,t,i,0,i-3,r,5);this._ecc200placementbit(n,t,i,0,i-2,r,4);this._ecc200placementbit(n,t,i,0,i-1,r,3);this._ecc200placementbit(n,t,i,1,i-3,r,2);this._ecc200placementbit(n,t,i,1,i-2,r,1);this._ecc200placementbit(n,t,i,1,i-1,r,0)},_ecc200placementblock:function(n,t,i,r,u,f){this._ecc200placementbit(n,t,i,r-2,u-2,f,7);this._ecc200placementbit(n,t,i,r-2,u-1,f,6);this._ecc200placementbit(n,t,i,r-1,u-2,f,5);this._ecc200placementbit(n,t,i,r-1,u-1,f,4);this._ecc200placementbit(n,t,i,r-1,+u,f,3);this._ecc200placementbit(n,t,i,+r,u-2,f,2);this._ecc200placementbit(n,t,i,+r,u-1,f,1);this._ecc200placementbit(n,t,i,+r,+u,f,0)},_ecc200placementbit:function(n,t,i,r,u,f,e){r<0&&(r+=t,u+=4-(t+4)%8);u<0&&(u+=i,r+=4-(i+4)%8);n[r*i+u]=(f<<3)+e},_errorCorrection:function(n,t){var f,a=1,h,u=new Array(n+1),e=new Array(256),r=new Array(256),l,c,o,i,s;for(e[0]=-255,r[0]=1,i=1;i<=255;i++)r[i]=r[i-1]*2,r[i]>=256&&(r[i]=r[i]^301),e[r[i]]=i;for(u[0]=1,i=1;i<=n;i++){for(u[i]=1,f=i-1;f>0;f--)u[f]!=0&&(u[f]=r[(e[u[f]]+a)%255]),u[f]^=u[f-1];u[0]=r[(e[u[0]]+a)%255];a++}for(h=new Array(n),l=0;l<h.length;l++)h[l]=u[l];for(c=0,o=new Array(n),i=0;i<n;i++)o[i]=0;for(i=0;i<t.length;i++){for(c=o[n-1]^t[i],s=n-1;s>0;s--)o[s]=c!=0&&h[s]!=0?o[s-1]^r[(e[c]+e[h[s]])%255]:o[s-1];o[0]=c!=0&&h[0]!=0?r[(e[c]+e[h[0]])%255]:0}return o.reverse()},_validateText:function(n,t){var i=new RegExp(t);return i.test(n)},_getCharWidth:function(n,t){for(var i=0,f=this.Model.narrowBarWidth,u=n.split(""),r=0;r<u.length;r++)i=i+f+this._getWidth(t,u[r]);return i},_getWidth:function(n,t){for(var u,f,e=!1,i=0,o,r=0;r<n.length;r++)if(n[r][0]==t){for(u=n[r][2],f=0;f<u.length;f++)e==!1&&u.length%2!=0&&(o=!0),i=i+u[f]*this.Model.narrowBarWidth,e=!0;return i}return i},_draw1DBarcode:function(n,t,i){var nt,at,ft,p,tt,it,rt,f,s,h,r,e,d,v,et,ot,c,w,o;if(t.length>0){var y=n.split(""),u=this.Model.quietZone.left,l=this.Model.quietZone.top,g=this.textFont.size;this.Model.height=l+this.Model.barHeight+this.Model.quietZone.bottom;this.Model.width=i;this.Model.displayText&&(this.Model.height+=g+this.Model.barcodeToTextGapHeight);this.canvasEl[0].setAttribute("width",this.Model.width);this.BarcodeEl.css("width",this.Model.width);this.canvasEl[0].setAttribute("height",this.Model.height);this.BarcodeEl.css("height",this.Model.height);var st=this.Model.width,b=this.Model.height,k=0;if(this.model.symbologyType=="ean"&&n.length==18){for(var ht=[["0",0,[3,2,1,1]],["1",1,[2,2,2,1]],["2",2,[2,1,2,2]],["3",3,[1,4,1,1]],["4",4,[1,1,3,2]],["5",5,[1,2,3,1]],["6",6,[1,1,1,4]],["7",7,[1,3,1,2]],["8",8,[1,2,1,3]],["9",9,[3,1,1,2]],["B",0,[1,1,1,1]]],ct=[["0",0,[1,1,2,3]],["1",1,[1,2,2,2]],["2",2,[2,2,1,2]],["3",3,[1,1,4,1]],["4",4,[2,3,1,1]],["5",5,[1,3,2,1]],["6",6,[4,1,1,1]],["7",7,[2,1,3,1]],["8",8,[3,1,2,1]],["9",9,[2,1,1,3]]],vt=[["0",0,[3,2,1,1]],["1",1,[2,2,2,1]],["2",2,[2,1,2,2]],["3",3,[1,4,1,1]],["4",4,[1,1,3,2]],["5",5,[1,2,3,1]],["6",6,[1,1,1,4]],["7",7,[1,3,1,2]],["8",8,[1,2,1,3]],["9",9,[3,1,1,2]]],v=n[2],lt=[["Odd","Odd","Odd","Odd","Odd","Odd"],["Odd","Odd","Even","Odd","Even","Even"],["Odd","Odd","Even","Even","Odd","Even"],["Odd","Odd","Even","Even","Even","Odd"],["Odd","Even","Odd","Odd","Even","Even"],["Odd","Even","Even","Odd","Odd","Even"],["Odd","Even","Even","Even","Odd","Odd"],["Odd","Even","Odd","Even","Odd","Even"],["Odd","Even","Odd","Even","Even","Odd"],["Odd","Even","Even","Odd","Even","Odd"]][v],c=n.split(""),a=[],r=0,ut=0,r=3;r<c.length/2;r++)lt[ut]=="Odd"?(v=c[r],a.push(ht[v])):(v=c[r],a.push(ct[v])),ut++;for(r==c.length/2&&(lt[ut]=="Odd"?(v=c[r+1],a.push(ht[v])):(v=c[r+1],a.push(ct[v]))),nt="",r=10;r<c.length-2;r++)nt+=c[r];for(r=0;r<nt.length;r++)v=nt[r],a.push(vt[v]);for(at=["A",0,[1,1,1,1]],a.push(at),a.push(t[t.length-1]),ft="",r=0;r<c.length;r++)r!=2&&(ft+=c[r]);for(f=0;f<y.length;f++)for(s=0;s<t.length;s++)if(t[s][0]==y[f]){for(h=t[s][2],r=0;r<h.length;r++)e=h[r]*this.Model.narrowBarWidth,k+=h[r];h.length%2!=0&&k++}for(p=(this._getProperValue(this.Model.width)-this.Model.quietZone.right-this.Model.quietZone.left)/k,p<1&&(p=1),this.Model.narrowBarWidth=Math.floor(p),tt=(p%1).toFixed(3),it=k*tt,u=parseInt(this.model.quietZone.left)+Math.round(it/2),this.model.barHeight=this._getProperValue(this.Model.height)-this.Model.quietZone.top-this.Model.quietZone.bottom-(g+this.Model.barcodeToTextGapHeight),this.model.barHeight<0&&(this.model.barHeight=0),rt=this.Model.narrowBarWidth,this.contextEl.fillStyle="white",this.contextEl.fillRect(0,0,st,b),y=ft.split(""),o=0,f=0;f<y.length;f++)for(s=0;s<a.length;s++)if(a[s][0]==y[f]){if(a[o][0]==y[f]||a[s][0]=="A"||a[s][0]=="B"){for(h=a[s][0]=="A"||a[s][0]=="B"?a[s][2]:a[o][2],r=0;r<h.length;r++)e=h[r]*this.Model.narrowBarWidth,f>1&&f<=7?r%2!=0?(this.contextEl.beginPath(),this.contextEl.fillStyle=this.Model.darkBarColor,this.contextEl.fillRect(u,l,e,this.Model.barHeight),this.contextEl.closePath()):(this.contextEl.beginPath(),this.contextEl.fillStyle=this.model.lightBarColor,this.contextEl.fillRect(u,l,e,this.model.barHeight)):r%2==0?(this.contextEl.beginPath(),this.contextEl.fillStyle=this.Model.darkBarColor,this.contextEl.fillRect(u,l,e,this.Model.barHeight),this.contextEl.closePath()):(this.contextEl.beginPath(),this.contextEl.fillStyle=this.model.lightBarColor,this.contextEl.fillRect(u,l,e,this.model.barHeight)),u+=e,f==1&&r==h.length-1&&(u-=e);a[s][0]!="A"&&o++}d=u;(h.length%2!=0||this.model.symbologyType=="ean"&&f==7)&&(u+=rt,v=u-d,this.contextEl.beginPath(),this.contextEl.fillStyle=this.model.lightBarColor,this.contextEl.fillRect(d,l,e,this.model.barHeight))}}else{for(f=0;f<y.length;f++)for(s=0;s<t.length;s++)if(t[s][0]==y[f]){for(h=t[s][2],r=0;r<h.length;r++)e=h[r]*this.Model.narrowBarWidth,k+=h[r];h.length%2!=0&&k++}for(p=(this._getProperValue(this.Model.width)-this.Model.quietZone.right-this.Model.quietZone.left)/k,p<1&&(p=1),this.Model.narrowBarWidth=Math.floor(p),tt=(p%1).toFixed(3),it=k*tt,u=parseInt(this.model.quietZone.left)+Math.round(it/2),this.model.barHeight=this._getProperValue(this.Model.height)-this.Model.quietZone.top-this.Model.quietZone.bottom-(g+this.Model.barcodeToTextGapHeight),this.model.barHeight<0&&(this.model.barHeight=0),rt=this.Model.narrowBarWidth,this.contextEl.fillStyle="white",this.contextEl.fillRect(0,0,st,b),f=0;f<y.length;f++)for(s=0;s<t.length;s++)if(t[s][0]==y[f]){if(h=t[s][2],this.model.symbologyType=="upcbarcode")for(r=0;r<h.length;r++)e=h[r]*this.Model.narrowBarWidth,f>1&&f<=7?r%2!=0?(this.contextEl.beginPath(),this.contextEl.fillStyle=this.Model.darkBarColor,this.contextEl.fillRect(u,l,e,this.Model.barHeight),this.contextEl.closePath()):(this.contextEl.beginPath(),this.contextEl.fillStyle=this.model.lightBarColor,this.contextEl.fillRect(u,l,e,this.model.barHeight)):r%2==0?(this.contextEl.beginPath(),this.contextEl.fillStyle=this.Model.darkBarColor,this.contextEl.fillRect(u,l,e,this.Model.barHeight),this.contextEl.closePath()):(this.contextEl.beginPath(),this.contextEl.fillStyle=this.model.lightBarColor,this.contextEl.fillRect(u,l,e,this.model.barHeight)),u+=e,f==1&&r==h.length-1&&(u-=e);else if(this.model.symbologyType=="ean"&&n.length==13)for(r=0;r<h.length;r++)e=h[r]*this.Model.narrowBarWidth,f>1&&f<=5?r%2!=0?(this.contextEl.beginPath(),this.contextEl.fillStyle=this.Model.darkBarColor,this.contextEl.fillRect(u,l,e,this.Model.barHeight),this.contextEl.closePath()):(this.contextEl.beginPath(),this.contextEl.fillStyle=this.model.lightBarColor,this.contextEl.fillRect(u,l,e,this.model.barHeight)):r%2==0?(this.contextEl.beginPath(),this.contextEl.fillStyle=this.Model.darkBarColor,this.contextEl.fillRect(u,l,e,this.Model.barHeight),this.contextEl.closePath()):(this.contextEl.beginPath(),this.contextEl.fillStyle=this.model.lightBarColor,this.contextEl.fillRect(u,l,e,this.model.barHeight)),u+=e,f==1&&r==h.length-1&&(u-=e);else for(r=0;r<h.length;r++)e=h[r]*this.Model.narrowBarWidth,r%2==0?(this.contextEl.beginPath(),this.contextEl.fillStyle=this.Model.darkBarColor,this.contextEl.fillRect(u,l,e,this.Model.barHeight),this.contextEl.closePath()):(this.contextEl.beginPath(),this.contextEl.fillStyle=this.model.lightBarColor,this.contextEl.fillRect(u,l,e,this.model.barHeight),this.contextEl.closePath()),u+=e;d=u;(h.length%2!=0||this.model.symbologyType=="upcbarcode"&&f==7||this.model.symbologyType=="ean"&&f==5)&&(u+=rt,v=u-d,this.contextEl.beginPath(),this.contextEl.fillStyle=this.model.lightBarColor,this.contextEl.fillRect(d,l,e,this.model.barHeight))}}if(this.Model.displayText){if(et=this.textFont.fontStyle.toLowerCase()=="regular"?"":+this.textFont.fontStyle.toLowerCase()+" ",et+=this.textFont.size+"px "+this.textFont.fontFamily.toLowerCase()+",sans-serif",this.contextEl.font=et,ot=0,ot=this.contextEl.measureText(this.Model.text).width,this.Model.width<ot&&(this.canvasEl[0].setAttribute("width",this.Model.width),this.BarcodeEl.css("width",this.Model.width)),this.contextEl.beginPath(),this.contextEl.fillStyle=this.Model.textColor,this.contextEl.textAlign="center",b=b-g+g/2,this.model.symbologyType=="upcbarcode"){for(c=n.split(""),w="",o=0;o<c.length;o++)(o>1&&o<=7||o>8&&o<=14)&&(w+=c[o]);this.contextEl.fillText(w,this.Model.width/2,b)}else if(this.model.symbologyType=="ean"&&n.length==18){for(c=n.split(""),w="",o=0;o<c.length;o++)(o>1&&o<=8||o>9&&o<=15)&&(w+=c[o]);this.contextEl.fillText(w,this.Model.width/2,b)}else if(this.model.symbologyType=="ean"&&n.length==13){for(c=n.split(""),w="",o=0;o<c.length;o++)(o>1&&o<=5||o>6&&o<=10)&&(w+=c[o]);this.contextEl.fillText(w,this.Model.width/2,b)}else this.contextEl.fillText(this.Model.text,this.Model.width/2,b);this.contextEl.closePath();this.contextEl.stroke()}}}});t.datavisualization.Barcode.SymbologyType={code39:"code39",code39Extended:"code39extended",code11:"code11",codabar:"codabar",code32:"code32",code93:"code93",code93Extended:"code93extended",code128A:"code128a",code128B:"code128b",code128C:"code128c",dataMatrix:"datamatrix",qrBarcode:"qrbarcode",upca:"upcbarcode",ean:"ean"};t.datavisualization.Barcode.symbologySettings={code39:{startSymbol:"*",stopSymbol:"*",validatorExp:"^[A-Z0-9-.$/+% ]+$",symbolTable:[["0",0,[1,1,1,3,3,1,3,1,1]],["1",1,[3,1,1,3,1,1,1,1,3]],["2",2,[1,1,3,3,1,1,1,1,3]],["3",3,[3,1,3,3,1,1,1,1,1]],["4",4,[1,1,1,3,3,1,1,1,3]],["5",5,[3,1,1,3,3,1,1,1,1]],["6",6,[1,1,3,3,3,1,1,1,1]],["7",7,[1,1,1,3,1,1,3,1,3]],["8",8,[3,1,1,3,1,1,3,1,1]],["9",9,[1,1,3,3,1,1,3,1,1]],["A",10,[3,1,1,1,1,3,1,1,3]],["B",11,[1,1,3,1,1,3,1,1,3]],["C",12,[3,1,3,1,1,3,1,1,1]],["D",13,[1,1,1,1,3,3,1,1,3]],["E",14,[3,1,1,1,3,3,1,1,1]],["F",15,[1,1,3,1,3,3,1,1,1]],["G",16,[1,1,1,1,1,3,3,1,3]],["H",17,[3,1,1,1,1,3,3,1,1]],["I",18,[1,1,3,1,1,3,3,1,1]],["J",19,[1,1,1,1,3,3,3,1,1]],["K",20,[3,1,1,1,1,1,1,3,3]],["L",21,[1,1,3,1,1,1,1,3,3]],["M",22,[3,1,3,1,1,1,1,3,1]],["N",23,[1,1,1,1,3,1,1,3,3]],["O",24,[3,1,1,1,3,1,1,3,1]],["P",25,[1,1,3,1,3,1,1,3,1]],["Q",26,[1,1,1,1,1,1,3,3,3]],["R",27,[3,1,1,1,1,1,3,3,1]],["S",28,[1,1,3,1,1,1,3,3,1]],["T",29,[1,1,1,1,3,1,3,3,1]],["U",30,[3,3,1,1,1,1,1,1,3]],["V",31,[1,3,3,1,1,1,1,1,3]],["W",32,[3,3,3,1,1,1,1,1,1]],["X",33,[1,3,1,1,3,1,1,1,3]],["Y",34,[3,3,1,1,3,1,1,1,1]],["Z",35,[1,3,3,1,3,1,1,1,1]],["-",36,[1,3,1,1,1,1,3,1,3]],[".",37,[3,3,1,1,1,1,3,1,1]],[" ",38,[1,3,3,1,1,1,3,1,1]],["$",39,[1,3,1,3,1,3,1,1,1]],["/",40,[1,3,1,3,1,1,1,3,1]],["+",41,[1,3,1,1,1,3,1,3,1]],["%",42,[1,1,1,3,1,3,1,3,1]],["*",0,[1,3,1,1,3,1,3,1,1]]]},code39extended:{startSymbol:"*",stopSymbol:"*",validatorExp:"^[\x00-]+$",symbolTable:[["0",0,[1,1,1,3,3,1,3,1,1]],["1",1,[3,1,1,3,1,1,1,1,3]],["2",2,[1,1,3,3,1,1,1,1,3]],["3",3,[3,1,3,3,1,1,1,1,1]],["4",4,[1,1,1,3,3,1,1,1,3]],["5",5,[3,1,1,3,3,1,1,1,1]],["6",6,[1,1,3,3,3,1,1,1,1]],["7",7,[1,1,1,3,1,1,3,1,3]],["8",8,[3,1,1,3,1,1,3,1,1]],["9",9,[1,1,3,3,1,1,3,1,1]],["A",10,[3,1,1,1,1,3,1,1,3]],["B",11,[1,1,3,1,1,3,1,1,3]],["C",12,[3,1,3,1,1,3,1,1,1]],["D",13,[1,1,1,1,3,3,1,1,3]],["E",14,[3,1,1,1,3,3,1,1,1]],["F",15,[1,1,3,1,3,3,1,1,1]],["G",16,[1,1,1,1,1,3,3,1,3]],["H",17,[3,1,1,1,1,3,3,1,1]],["I",18,[1,1,3,1,1,3,3,1,1]],["J",19,[1,1,1,1,3,3,3,1,1]],["K",20,[3,1,1,1,1,1,1,3,3]],["L",21,[1,1,3,1,1,1,1,3,3]],["M",22,[3,1,3,1,1,1,1,3,1]],["N",23,[1,1,1,1,3,1,1,3,3]],["O",24,[3,1,1,1,3,1,1,3,1]],["P",25,[1,1,3,1,3,1,1,3,1]],["Q",26,[1,1,1,1,1,1,3,3,3]],["R",27,[3,1,1,1,1,1,3,3,1]],["S",28,[1,1,3,1,1,1,3,3,1]],["T",29,[1,1,1,1,3,1,3,3,1]],["U",30,[3,3,1,1,1,1,1,1,3]],["V",31,[1,3,3,1,1,1,1,1,3]],["W",32,[3,3,3,1,1,1,1,1,1]],["X",33,[1,3,1,1,3,1,1,1,3]],["Y",34,[3,3,1,1,3,1,1,1,1]],["Z",35,[1,3,3,1,3,1,1,1,1]],["-",36,[1,3,1,1,1,1,3,1,3]],[".",37,[3,3,1,1,1,1,3,1,1]],[" ",38,[1,3,3,1,1,1,3,1,1]],["$",39,[1,3,1,3,1,3,1,1,1]],["/",40,[1,3,1,3,1,1,1,3,1]],["+",41,[1,3,1,1,1,3,1,3,1]],["%",42,[1,1,1,3,1,3,1,3,1]],["*",0,[1,3,1,1,3,1,3,1,1]]],extendedCodes:[['\0',["%","U"]],[String.fromCharCode(parseInt("0001",16)),["$","A"]],[String.fromCharCode(parseInt("0002",16)),["$","B"]],[String.fromCharCode(parseInt("0003",16)),["$","C"]],[String.fromCharCode(parseInt("0004",16)),["$","D"]],[String.fromCharCode(parseInt("0005",16)),["$","E"]],[String.fromCharCode(parseInt("0006",16)),["$","F"]],["a",["$","G"]],["\b",["$","H"]],["\t",["$","I"]],["\n",["$","J"]],['\v',["$","K"]],["\f",["$","L"]],["\r",["$","M"]],[String.fromCharCode(parseInt("000e",16)),["$","N"]],[String.fromCharCode(parseInt("000f",16)),["$","O"]],[String.fromCharCode(parseInt("0010",16)),["$","P"]],[String.fromCharCode(parseInt("0011",16)),["$","Q"]],[String.fromCharCode(parseInt("0012",16)),["$","R"]],[String.fromCharCode(parseInt("0013",16)),["$","S"]],[String.fromCharCode(parseInt("0014",16)),["$","T"]],[String.fromCharCode(parseInt("0015",16)),["$","U"]],[String.fromCharCode(parseInt("0016",16)),["$","V"]],[String.fromCharCode(parseInt("0017",16)),["$","W"]],[String.fromCharCode(parseInt("0018",16)),["$","X"]],[String.fromCharCode(parseInt("0019",16)),["$","Y"]],[String.fromCharCode(parseInt("001a",16)),["$","Z"]],[String.fromCharCode(parseInt("001b",16)),["%","A"]],[String.fromCharCode(parseInt("001c",16)),["%","B"]],[String.fromCharCode(parseInt("001d",16)),["%","C"]],[String.fromCharCode(parseInt("001e",16)),["%","D"]],[String.fromCharCode(parseInt("001f",16)),["%","E"]],[" ",[" "]],["!",["/","A"]],['"',["/","B"]],["#",["/","C"]],["$",["/","D"]],["%",["/","E"]],["&",["/","F"]],["'",["/","G"]],["(",["/","H"]],[")",["/","I"]],["*",["/","J"]],["+",["/","K"]],[",",["/","L"]],["-",["/","M"]],[".",["/","N"]],["/",["/","O"]],["0",["0"]],["1",["1"]],["2",["2"]],["3",["3"]],["4",["4"]],["5",["5"]],["6",["6"]],["7",["7"]],["8",["8"]],["9",["9"]],[":",["/","Z"]],[";",["%","F"]],["<",["%","G"]],["=",["%","H"]],[">",["%","I"]],["?",["%","J"]],["@",["%","V"]],["A",["A"]],["B",["B"]],["C",["C"]],["D",["D"]],["E",["E"]],["F",["F"]],["G",["G"]],["H",["H"]],["I",["I"]],["J",["J"]],["K",["K"]],["L",["L"]],["M",["M"]],["N",["N"]],["O",["O"]],["P",["P"]],["Q",["Q"]],["R",["R"]],["S",["S"]],["T",["T"]],["U",["U"]],["V",["V"]],["W",["W"]],["X",["X"]],["Y",["Y"]],["Z",["Z"]],["[",["%","K"]],["\\",["%","L"]],["]",["%","M"]],["^",["%","N"]],["_",["%","O"]],["`",["%","W"]],["a",["+","A"]],["b",["+","B"]],["c",["+","C"]],["d",["+","D"]],["e",["+","E"]],["f",["+","F"]],["g",["+","G"]],["h",["+","H"]],["i",["+","I"]],["j",["+","J"]],["k",["+","K"]],["l",["+","L"]],["m",["+","M"]],["n",["+","N"]],["o",["+","O"]],["p",["+","P"]],["q",["+","Q"]],["r",["+","R"]],["s",["+","S"]],["t",["+","T"]],["u",["+","U"]],["v",["+","V"]],["w",["+","W"]],["x",["+","X"]],["y",["+","Y"]],["z",["+","Z"]],["{",["%","P"]],["|",["%","Q"]],["}",["%","R"]],["~",["%","S"]],[String.fromCharCode(parseInt("007f",16)),["%","T"]]]},code11:{startSymbol:"*",stopSymbol:"*",validatorExp:"^[0-9-]*$",symbolTable:[["0",0,[1,1,1,1,2]],["1",1,[2,1,1,1,2]],["2",2,[1,2,1,1,2]],["3",3,[2,2,1,1,1]],["4",4,[1,1,2,1,2]],["5",5,[2,1,2,1,1]],["6",6,[1,2,2,1,1]],["7",7,[1,1,1,2,2]],["8",8,[2,1,1,2,1]],["9",9,[2,1,1,1,1]],["-",10,[1,1,2,1,1]],["*",0,[1,1,2,2,1]]]},codabar:{startSymbol:"A",stopSymbol:"B",validatorExp:"^[0-9-.$/:+]*$",symbolTable:[["0",0,[1,1,1,1,1,2,2]],["1",0,[1,1,1,1,2,2,1]],["2",0,[1,1,1,2,1,1,2]],["3",0,[2,2,1,1,1,1,1]],["4",0,[1,1,2,1,1,2,1]],["5",0,[2,1,1,1,1,2,1]],["6",0,[1,2,1,1,1,1,2]],["7",0,[1,2,1,1,2,1,1]],["8",0,[1,2,2,1,1,1,1]],["9",0,[2,1,1,2,1,1,1]],["-",0,[1,1,1,2,2,1,1]],["$",0,[1,1,2,2,1,1,1]],[":",0,[2,1,1,1,2,1,2]],["/",0,[2,1,2,1,1,1,2]],[".",0,[2,1,2,1,2,1,1]],["+",0,[1,1,2,1,2,1,2]],["A",0,[1,1,2,2,1,2,1]],["B",0,[1,1,1,2,1,2,2]]]},code32:{startSymbol:"*",stopSymbol:"*",validatorExp:"^[A-Z0-9 -*.$/+%]+$",symbolTable:[["0",0,[1,1,1,3,3,1,3,1,1]],["1",1,[3,1,1,3,1,1,1,1,3]],["2",2,[1,1,3,3,1,1,1,1,3]],["3",3,[3,1,3,3,1,1,1,1,1]],["4",4,[1,1,1,3,3,1,1,1,3]],["5",5,[3,1,1,3,3,1,1,1,1]],["6",6,[1,1,3,3,3,1,1,1,1]],["7",7,[1,1,1,3,1,1,3,1,3]],["8",8,[3,1,1,3,1,1,3,1,1]],["9",9,[1,1,3,3,1,1,3,1,1]],["B",10,[1,1,3,1,1,3,1,1,3]],["C",11,[3,1,3,1,1,3,1,1,1]],["D",12,[1,1,1,1,3,3,1,1,3]],["F",13,[1,1,3,1,3,3,1,1,1]],["G",14,[1,1,1,1,1,3,3,1,3]],["H",15,[3,1,1,1,1,3,3,1,1]],["J",16,[1,1,1,1,3,3,3,1,1]],["K",17,[3,1,1,1,1,1,1,3,3]],["L",18,[1,1,3,1,1,1,1,3,3]],["M",19,[3,1,3,1,1,1,1,3,1]],["N",20,[1,1,1,1,3,1,1,3,3]],["P",21,[1,1,3,1,3,1,1,3,1]],["Q",22,[1,1,1,1,1,1,3,3,3]],["R",23,[3,1,1,1,1,1,3,3,1]],["S",24,[1,1,3,1,1,1,3,3,1]],["T",25,[1,1,1,1,3,1,3,3,1]],["U",26,[3,3,1,1,1,1,1,1,3]],["V",27,[1,3,3,1,1,1,1,1,3]],["W",28,[3,3,3,1,1,1,1,1,1]],["X",29,[1,3,1,1,3,1,1,1,3]],["Y",30,[3,3,1,1,3,1,1,1,1]],["Z",31,[1,3,3,1,3,1,1,1,1]],["*",0,[1,3,1,1,3,1,3,1,1]]]},code93:{startSymbol:"*",stopSymbol:String.fromCharCode(parseInt("00ff",16)),validatorExp:"^[A-Z0-9-.$/+% ]+$",symbolTable:[["0",0,[1,3,1,1,1,2]],["1",1,[1,1,1,2,1,3]],["2",2,[1,1,1,3,1,2]],["3",3,[1,1,1,4,1,1]],["4",4,[1,2,1,1,1,2]],["5",5,[1,2,1,2,1,2]],["6",6,[1,2,1,3,1,1]],["7",7,[1,1,1,1,1,4]],["8",8,[1,3,1,2,1,1]],["9",9,[1,4,1,1,1,1]],["A",10,[2,1,1,1,1,3]],["B",11,[2,1,1,2,1,2]],["C",12,[2,1,1,3,1,1]],["D",13,[2,2,1,1,1,2]],["E",14,[2,2,1,2,1,1]],["F",15,[2,3,1,1,1,1]],["G",16,[1,1,2,1,1,3]],["H",17,[1,1,2,2,1,2]],["I",18,[1,1,2,3,1,1]],["J",19,[1,2,2,1,1,2]],["K",20,[1,3,2,1,1,1]],["L",21,[1,1,1,1,2,3]],["M",22,[1,1,1,2,2,2]],["N",23,[1,1,1,3,2,1]],["O",24,[1,2,1,1,2,2]],["P",25,[1,3,1,1,2,1]],["Q",26,[2,1,2,1,1,2]],["R",27,[2,1,2,2,1,1]],["S",28,[2,1,1,1,2,2]],["T",29,[2,1,1,2,2,1]],["U",30,[2,2,1,1,2,1]],["V",31,[2,2,2,1,1,1]],["W",32,[1,1,2,1,2,2]],["X",33,[1,1,2,2,2,1]],["Y",34,[1,2,2,1,2,1]],["Z",35,[1,2,3,1,1,1]],["-",36,[1,2,1,1,3,1]],[".",37,[3,1,1,1,1,2]],[" ",38,[3,1,1,2,1,1]],["$",39,[3,2,1,1,1,1]],["/",40,[1,1,2,1,3,1]],["+",41,[1,1,3,1,2,1]],["%",42,[2,1,1,1,3,1]],["*",0,[1,1,1,1,4,1]],[String.fromCharCode(parseInt("00ff",16)),0,[1,1,1,1,4,1,1]],[String.fromCharCode(parseInt("00fb",16)),43,[1,2,1,2,2,0]],[String.fromCharCode(parseInt("00fc",16)),44,[3,1,2,1,1,1]],[String.fromCharCode(parseInt("00fd",16)),45,[3,1,1,1,2,1]],[String.fromCharCode(parseInt("00fe",16)),46,[1,2,2,2,1,1]]]},code93extended:{startSymbol:"*",stopSymbol:String.fromCharCode(parseInt("00ff",16)),validatorExp:"^[\x00-\x00fb\x00fd\x00fe'þ''ü''ý']+$",symbolTable:[["0",0,[1,3,1,1,1,2]],["1",1,[1,1,1,2,1,3]],["2",2,[1,1,1,3,1,2]],["3",3,[1,1,1,4,1,1]],["4",4,[1,2,1,1,1,2]],["5",5,[1,2,1,2,1,2]],["6",6,[1,2,1,3,1,1]],["7",7,[1,1,1,1,1,4]],["8",8,[1,3,1,2,1,1]],["9",9,[1,4,1,1,1,1]],["A",10,[2,1,1,1,1,3]],["B",11,[2,1,1,2,1,2]],["C",12,[2,1,1,3,1,1]],["D",13,[2,2,1,1,1,2]],["E",14,[2,2,1,2,1,1]],["F",15,[2,3,1,1,1,1]],["G",16,[1,1,2,1,1,3]],["H",17,[1,1,2,2,1,2]],["I",18,[1,1,2,3,1,1]],["J",19,[1,2,2,1,1,2]],["K",20,[1,3,2,1,1,1]],["L",21,[1,1,1,1,2,3]],["M",22,[1,1,1,2,2,2]],["N",23,[1,1,1,3,2,1]],["O",24,[1,2,1,1,2,2]],["P",25,[1,3,1,1,2,1]],["Q",26,[2,1,2,1,1,2]],["R",27,[2,1,2,2,1,1]],["S",28,[2,1,1,1,2,2]],["T",29,[2,1,1,2,2,1]],["U",30,[2,2,1,1,2,1]],["V",31,[2,2,2,1,1,1]],["W",32,[1,1,2,1,2,2]],["X",33,[1,1,2,2,2,1]],["Y",34,[1,2,2,1,2,1]],["Z",35,[1,2,3,1,1,1]],["-",36,[1,2,1,1,3,1]],[".",37,[3,1,1,1,1,2]],[" ",38,[3,1,1,2,1,1]],["$",39,[3,2,1,1,1,1]],["/",40,[1,1,2,1,3,1]],["+",41,[1,1,3,1,2,1]],["%",42,[2,1,1,1,3,1]],["*",0,[1,1,1,1,4,1]],[String.fromCharCode(parseInt("00ff",16)),0,[1,1,1,1,4,1,1]],[String.fromCharCode(parseInt("00fb",16)),43,[1,2,1,2,2,0]],[String.fromCharCode(parseInt("00fc",16)),44,[3,1,2,1,1,1]],[String.fromCharCode(parseInt("00fd",16)),45,[3,1,1,1,2,1]],[String.fromCharCode(parseInt("00fe",16)),46,[1,2,2,2,1,1]]],extendedCodes:[['\0',[String.fromCharCode(parseInt("00fc",16)),"U"]],[String.fromCharCode(parseInt("0001",16)),[String.fromCharCode(parseInt("00fb",16)),"A"]],[String.fromCharCode(parseInt("0002",16)),[String.fromCharCode(parseInt("00fb",16)),"B"]],[String.fromCharCode(parseInt("0003",16)),[String.fromCharCode(parseInt("00fb",16)),"C"]],[String.fromCharCode(parseInt("0004",16)),[String.fromCharCode(parseInt("00fb",16)),"D"]],[String.fromCharCode(parseInt("0005",16)),[String.fromCharCode(parseInt("00fb",16)),"E"]],[String.fromCharCode(parseInt("0006",16)),[String.fromCharCode(parseInt("00fb",16)),"F"]],["a",[String.fromCharCode(parseInt("00fb",16)),"G"]],["\b",[String.fromCharCode(parseInt("00fb",16)),"H"]],["\t",[String.fromCharCode(parseInt("00fb",16)),"I"]],["\n",[String.fromCharCode(parseInt("00fb",16)),"J"]],['\v',[String.fromCharCode(parseInt("00fb",16)),"K"]],["\f",[String.fromCharCode(parseInt("00fb",16)),"L"]],["\r",[String.fromCharCode(parseInt("00fb",16)),"M"]],[String.fromCharCode(parseInt("000e",16)),[String.fromCharCode(parseInt("00fb",16)),"N"]],[String.fromCharCode(parseInt("000f",16)),[String.fromCharCode(parseInt("00fb",16)),"O"]],[String.fromCharCode(parseInt("0010",16)),[String.fromCharCode(parseInt("00fb",16)),"P"]],[String.fromCharCode(parseInt("0011",16)),[String.fromCharCode(parseInt("00fb",16)),"Q"]],[String.fromCharCode(parseInt("0012",16)),[String.fromCharCode(parseInt("00fb",16)),"R"]],[String.fromCharCode(parseInt("0013",16)),[String.fromCharCode(parseInt("00fb",16)),"S"]],[String.fromCharCode(parseInt("0014",16)),[String.fromCharCode(parseInt("00fb",16)),"T"]],[String.fromCharCode(parseInt("0015",16)),[String.fromCharCode(parseInt("00fb",16)),"U"]],[String.fromCharCode(parseInt("0016",16)),[String.fromCharCode(parseInt("00fb",16)),"V"]],[String.fromCharCode(parseInt("0017",16)),[String.fromCharCode(parseInt("00fb",16)),"W"]],[String.fromCharCode(parseInt("0018",16)),[String.fromCharCode(parseInt("00fb",16)),"X"]],[String.fromCharCode(parseInt("0019",16)),[String.fromCharCode(parseInt("00fb",16)),"Y"]],[String.fromCharCode(parseInt("001a",16)),[String.fromCharCode(parseInt("00fb",16)),"Z"]],[String.fromCharCode(parseInt("001b",16)),[String.fromCharCode(parseInt("00fc",16)),"A"]],[String.fromCharCode(parseInt("001c",16)),[String.fromCharCode(parseInt("00fc",16)),"B"]],[String.fromCharCode(parseInt("001d",16)),[String.fromCharCode(parseInt("00fc",16)),"C"]],[String.fromCharCode(parseInt("001e",16)),[String.fromCharCode(parseInt("00fc",16)),"D"]],[String.fromCharCode(parseInt("001f",16)),[String.fromCharCode(parseInt("00fc",16)),"E"]],[" ",[" "]],["!",[String.fromCharCode(parseInt("00fd",16)),"A"]],['"',[String.fromCharCode(parseInt("00fd",16)),"B"]],["#",[String.fromCharCode(parseInt("00fd",16)),"C"]],["$",[String.fromCharCode(parseInt("00fd",16)),"D"]],["%",[String.fromCharCode(parseInt("00fd",16)),"E"]],["&",[String.fromCharCode(parseInt("00fd",16)),"F"]],["'",[String.fromCharCode(parseInt("00fd",16)),"G"]],["(",[String.fromCharCode(parseInt("00fd",16)),"H"]],[")",[String.fromCharCode(parseInt("00fd",16)),"I"]],["*",[String.fromCharCode(parseInt("00fd",16)),"J"]],["+",[String.fromCharCode(parseInt("00fd",16)),"K"]],[",",[String.fromCharCode(parseInt("00fd",16)),"L"]],["-",[String.fromCharCode(parseInt("00fd",16)),"M"]],[".",[String.fromCharCode(parseInt("00fd",16)),"N"]],["/",[String.fromCharCode(parseInt("00fd",16)),"O"]],["0",["0"]],["1",["1"]],["2",["2"]],["3",["3"]],["4",["4"]],["5",["5"]],["6",["6"]],["7",["7"]],["8",["8"]],["9",["9"]],[":",[String.fromCharCode(parseInt("00fd",16)),"Z"]],[";",[String.fromCharCode(parseInt("00fc",16)),"F"]],["<",[String.fromCharCode(parseInt("00fc",16)),"G"]],["=",[String.fromCharCode(parseInt("00fc",16)),"H"]],[">",[String.fromCharCode(parseInt("00fc",16)),"I"]],["?",[String.fromCharCode(parseInt("00fc",16)),"J"]],["@",[String.fromCharCode(parseInt("00fc",16)),"V"]],["A",["A"]],["B",["B"]],["C",["C"]],["D",["D"]],["E",["E"]],["F",["F"]],["G",["G"]],["H",["H"]],["I",["I"]],["J",["J"]],["K",["K"]],["L",["L"]],["M",["M"]],["N",["N"]],["O",["O"]],["P",["P"]],["Q",["Q"]],["R",["R"]],["S",["S"]],["T",["T"]],["U",["U"]],["V",["V"]],["W",["W"]],["X",["X"]],["Y",["Y"]],["Z",["Z"]],["[",[String.fromCharCode(parseInt("00fc",16)),"K"]],["\\",[String.fromCharCode(parseInt("00fc",16)),"L"]],["]",[String.fromCharCode(parseInt("00fc",16)),"M"]],["^",[String.fromCharCode(parseInt("00fc",16)),"N"]],["_",[String.fromCharCode(parseInt("00fc",16)),"O"]],["`",[String.fromCharCode(parseInt("00fc",16)),"W"]],["a",[String.fromCharCode(parseInt("00fe",16)),"A"]],["b",[String.fromCharCode(parseInt("00fe",16)),"B"]],["c",[String.fromCharCode(parseInt("00fe",16)),"C"]],["d",[String.fromCharCode(parseInt("00fe",16)),"D"]],["e",[String.fromCharCode(parseInt("00fe",16)),"E"]],["f",[String.fromCharCode(parseInt("00fe",16)),"F"]],["g",[String.fromCharCode(parseInt("00fe",16)),"G"]],["h",[String.fromCharCode(parseInt("00fe",16)),"H"]],["i",[String.fromCharCode(parseInt("00fe",16)),"I"]],["j",[String.fromCharCode(parseInt("00fe",16)),"J"]],["k",[String.fromCharCode(parseInt("00fe",16)),"K"]],["l",[String.fromCharCode(parseInt("00fe",16)),"L"]],["m",[String.fromCharCode(parseInt("00fe",16)),"M"]],["n",[String.fromCharCode(parseInt("00fe",16)),"N"]],["o",[String.fromCharCode(parseInt("00fe",16)),"O"]],["p",[String.fromCharCode(parseInt("00fe",16)),"P"]],["q",[String.fromCharCode(parseInt("00fe",16)),"Q"]],["r",[String.fromCharCode(parseInt("00fe",16)),"R"]],["s",[String.fromCharCode(parseInt("00fe",16)),"S"]],["t",[String.fromCharCode(parseInt("00fe",16)),"T"]],["u",[String.fromCharCode(parseInt("00fe",16)),"U"]],["v",[String.fromCharCode(parseInt("00fe",16)),"V"]],["w",[String.fromCharCode(parseInt("00fe",16)),"W"]],["x",[String.fromCharCode(parseInt("00fe",16)),"X"]],["y",[String.fromCharCode(parseInt("00fe",16)),"Y"]],["z",[String.fromCharCode(parseInt("00fe",16)),"Z"]],["{",[String.fromCharCode(parseInt("00fc",16)),"P"]],["|",[String.fromCharCode(parseInt("00fc",16)),"Q"]],["}",[String.fromCharCode(parseInt("00fc",16)),"R"]],["~",[String.fromCharCode(parseInt("00fc",16)),"S"]],[String.fromCharCode(parseInt("007f",16)),[String.fromCharCode(parseInt("00fc",16)),"T"]]]},code128a:{startSymbol:String.fromCharCode(parseInt("00f9",16)),stopSymbol:String.fromCharCode(parseInt("00ff",16)),validatorExp:"[\0\x0001\x0002\x0003\x0004\x0005\x0006\a\b\t\n\v\f\r\x000e\x000f\x0010\x0011\x0012\x0013\x0014\x0015\x0016\x0017\x0018\x0019\x001a\x001b\x001c\x001d\x001e\x001f !\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_\x00f0\x00f1\x00f2\x00f3\x00f4]",symbolTable:[["\0",64,[1,1,1,4,2,2]],[String.fromCharCode(parseInt("0001",16)),65,[1,2,1,1,2,4]],[String.fromCharCode(parseInt("0002",16)),66,[1,2,1,4,2,1]],[String.fromCharCode(parseInt("0003",16)),67,[1,4,1,1,2,2]],[String.fromCharCode(parseInt("0004",16)),68,[1,4,1,2,2,1]],[String.fromCharCode(parseInt("0005",16)),69,[1,1,2,2,1,4]],[String.fromCharCode(parseInt("0006",16)),70,[1,1,2,4,1,2]],["a",71,[1,2,2,1,1,4]],["\b",72,[1,2,2,4,1,1]],["\t",73,[1,4,2,1,1,2]],["\n",74,[1,4,2,2,1,1]],["\v",75,[2,4,1,2,1,1]],["\f",76,[2,2,1,1,1,4]],["\r",77,[4,1,3,1,1,1]],[String.fromCharCode(parseInt("000e",16)),78,[2,4,1,1,1,2]],[String.fromCharCode(parseInt("000f",16)),79,[1,3,4,1,1,1]],[String.fromCharCode(parseInt("0010",16)),80,[1,1,1,2,4,2]],[String.fromCharCode(parseInt("0011",16)),81,[1,2,1,1,4,2]],[String.fromCharCode(parseInt("0012",16)),82,[1,2,1,2,4,1]],[String.fromCharCode(parseInt("0013",16)),83,[1,1,4,2,1,2]],[String.fromCharCode(parseInt("0014",16)),84,[1,2,4,1,1,2]],[String.fromCharCode(parseInt("0015",16)),85,[1,2,4,2,1,1]],[String.fromCharCode(parseInt("0016",16)),86,[4,1,1,2,1,2]],[String.fromCharCode(parseInt("0017",16)),87,[4,2,1,1,1,2]],[String.fromCharCode(parseInt("0018",16)),88,[4,2,1,2,1,1]],[String.fromCharCode(parseInt("0019",16)),89,[2,1,2,1,4,1]],[String.fromCharCode(parseInt("001a",16)),90,[2,1,4,1,2,1]],[String.fromCharCode(parseInt("001b",16)),91,[4,1,2,1,2,1]],[String.fromCharCode(parseInt("001c",16)),92,[1,1,1,1,4,3]],[String.fromCharCode(parseInt("001d",16)),93,[1,1,1,3,4,1]],[String.fromCharCode(parseInt("001e",16)),94,[1,3,1,1,4,1]],[String.fromCharCode(parseInt("001f",16)),95,[1,1,4,1,1,3]],[" ",0,[2,1,2,2,2,2]],["!",1,[2,2,2,1,2,2]],['"',2,[2,2,2,2,2,1]],["#",3,[1,2,1,2,2,3]],["$",4,[1,2,1,3,2,2]],["%",5,[1,3,1,2,2,2]],["&",6,[1,2,2,2,1,3]],["'",7,[1,2,2,3,1,2]],["(",8,[1,3,2,2,1,2]],[")",9,[2,2,1,2,1,3]],["*",10,[2,2,1,3,1,2]],["+",11,[2,3,1,2,1,2]],[",",12,[1,1,2,2,3,2]],["-",13,[1,2,2,1,3,2]],[".",14,[1,2,2,2,3,1]],["/",15,[1,1,3,2,2,2]],["0",16,[1,2,3,1,2,2]],["1",17,[1,2,3,2,2,1]],["2",18,[2,2,3,2,1,1]],["3",19,[2,2,1,1,3,2]],["4",20,[2,2,1,2,3,1]],["5",21,[2,1,3,2,1,2]],["6",22,[2,2,3,1,1,2]],["7",23,[3,1,2,1,3,1]],["8",24,[3,1,1,2,2,2]],["9",25,[3,2,1,1,2,2]],[":",26,[3,2,1,2,2,1]],[";",27,[3,1,2,2,1,2]],["<",28,[3,2,2,1,1,2]],["=",29,[3,2,2,2,1,1]],[">",30,[2,1,2,1,2,3]],["?",31,[2,1,2,3,2,1]],["@",32,[2,3,2,1,2,1]],["A",33,[1,1,1,3,2,3]],["B",34,[1,3,1,1,2,3]],["C",35,[1,3,1,3,2,1]],["D",36,[1,1,2,3,1,3]],["E",37,[1,3,2,1,1,3]],["F",38,[1,3,2,3,1,1]],["G",39,[2,1,1,3,1,3]],["H",40,[2,3,1,1,1,3]],["I",41,[2,3,1,3,1,1]],["J",42,[1,1,2,1,3,3]],["K",43,[1,1,2,3,3,1]],["L",44,[1,3,2,1,3,1]],["M",45,[1,1,3,1,2,3]],["N",46,[1,1,3,3,2,1]],["O",47,[1,3,3,1,2,1]],["P",48,[3,1,3,1,2,1]],["Q",49,[2,1,1,3,3,1]],["R",50,[2,3,1,1,3,1]],["S",51,[2,1,3,1,1,3]],["T",52,[2,1,3,3,1,1]],["U",53,[2,1,3,1,3,1]],["V",54,[3,1,1,1,2,3]],["W",55,[3,1,1,3,2,1]],["X",56,[3,3,1,1,2,1]],["Y",57,[3,1,2,1,1,3]],["Z",58,[3,1,2,3,1,1]],["[",59,[3,3,2,1,1,1]],["\\",60,[3,1,4,1,1,1]],["]",61,[2,2,1,4,1,1]],["^",62,[4,3,1,1,1,1]],["_",63,[1,1,1,2,2,4]],[String.fromCharCode(parseInt("00f0",16)),102,[4,1,1,1,3,1]],[String.fromCharCode(parseInt("00f1",16)),97,[4,1,1,1,1,3]],[String.fromCharCode(parseInt("00f2",16)),96,[1,1,4,3,1,1]],[String.fromCharCode(parseInt("00f3",16)),101,[3,1,1,1,4,1]],[String.fromCharCode(parseInt("00f4",16)),98,[4,1,1,3,1,1]],[String.fromCharCode(parseInt("00fc",16)),99,[1,1,3,1,4,1]],[String.fromCharCode(parseInt("00fb",16)),100,[1,1,4,1,3,1]],[String.fromCharCode(parseInt("00f9",16)),103,[2,1,1,4,1,2]],[String.fromCharCode(parseInt("00ff",16)),-1,[2,3,3,1,1,1,2]]]},code128b:{startSymbol:String.fromCharCode(parseInt("00fd",16)),stopSymbol:String.fromCharCode(parseInt("00ff",16)),validatorExp:"^[\x00-]",symbolTable:[[" ",0,[2,1,2,2,2,2]],["!",1,[2,2,2,1,2,2]],['"',2,[2,2,2,2,2,1]],["#",3,[1,2,1,2,2,3]],["$",4,[1,2,1,3,2,2]],["%",5,[1,3,1,2,2,2]],["&",6,[1,2,2,2,1,3]],["'",7,[1,2,2,3,1,2]],["(",8,[1,3,2,2,1,2]],[")",9,[2,2,1,2,1,3]],["*",10,[2,2,1,3,1,2]],["+",11,[2,3,1,2,1,2]],[",",12,[1,1,2,2,3,2]],["-",13,[1,2,2,1,3,2]],[".",14,[1,2,2,2,3,1]],["/",15,[1,1,3,2,2,2]],["0",16,[1,2,3,1,2,2]],["1",17,[1,2,3,2,2,1]],["2",18,[2,2,3,2,1,1]],["3",19,[2,2,1,1,3,2]],["4",20,[2,2,1,2,3,1]],["5",21,[2,1,3,2,1,2]],["6",22,[2,2,3,1,1,2]],["7",23,[3,1,2,1,3,1]],["8",24,[3,1,1,2,2,2]],["9",25,[3,2,1,1,2,2]],[":",26,[3,2,1,2,2,1]],[";",27,[3,1,2,2,1,2]],["<",28,[3,2,2,1,1,2]],["=",29,[3,2,2,2,1,1]],[">",30,[2,1,2,1,2,3]],["?",31,[2,1,2,3,2,1]],["@",32,[2,3,2,1,2,1]],["A",33,[1,1,1,3,2,3]],["B",34,[1,3,1,1,2,3]],["C",35,[1,3,1,3,2,1]],["D",36,[1,1,2,3,1,3]],["E",37,[1,3,2,1,1,3]],["F",38,[1,3,2,3,1,1]],["G",39,[2,1,1,3,1,3]],["H",40,[2,3,1,1,1,3]],["I",41,[2,3,1,3,1,1]],["J",42,[1,1,2,1,3,3]],["K",43,[1,1,2,3,3,1]],["L",44,[1,3,2,1,3,1]],["M",45,[1,1,3,1,2,3]],["N",46,[1,1,3,3,2,1]],["O",47,[1,3,3,1,2,1]],["P",48,[3,1,3,1,2,1]],["Q",49,[2,1,1,3,3,1]],["R",50,[2,3,1,1,3,1]],["S",51,[2,1,3,1,1,3]],["T",52,[2,1,3,3,1,1]],["U",53,[2,1,3,1,3,1]],["V",54,[3,1,1,1,2,3]],["W",55,[3,1,1,3,2,1]],["X",56,[3,3,1,1,2,1]],["Y",57,[3,1,2,1,1,3]],["Z",58,[3,1,2,3,1,1]],["[",59,[3,3,2,1,1,1]],["\\",60,[3,1,4,1,1,1]],["]",61,[2,2,1,4,1,1]],["^",62,[4,3,1,1,1,1]],["_",63,[1,1,1,2,2,4]],["`",64,[1,1,1,4,2,2]],["a",65,[1,2,1,1,2,4]],["b",66,[1,2,1,4,2,1]],["c",67,[1,4,1,1,2,2]],["d",68,[1,4,1,2,2,1]],["e",69,[1,1,2,2,1,4]],["f",70,[1,1,2,4,1,2]],["g",71,[1,2,2,1,1,4]],["h",72,[1,2,2,4,1,1]],["i",73,[1,4,2,1,1,2]],["j",74,[1,4,2,2,1,1]],["k",75,[2,4,1,2,1,1]],["l",76,[2,2,1,1,1,4]],["m",77,[4,1,3,1,1,1]],["n",78,[2,4,1,1,1,2]],["o",79,[1,3,4,1,1,1]],["p",80,[1,1,1,2,4,2]],["q",81,[1,2,1,1,4,2]],["r",82,[1,2,1,2,4,1]],["s",83,[1,1,4,2,1,2]],["t",84,[1,2,4,1,1,2]],["u",85,[1,2,4,2,1,1]],["v",86,[4,1,1,2,1,2]],["w",87,[4,2,1,1,1,2]],["x",88,[4,2,1,2,1,1]],["y",89,[2,1,2,1,4,1]],["z",90,[2,1,4,1,2,1]],["{",91,[4,1,2,1,2,1]],["|",92,[1,1,1,1,4,3]],["}",93,[1,1,1,3,4,1]],["~",94,[1,3,1,1,4,1]],[String.fromCharCode(parseInt("007f",16)),95,[1,1,4,1,1,3]],[String.fromCharCode(parseInt("00f0",16)),102,[4,1,1,1,3,1]],[String.fromCharCode(parseInt("00f1",16)),97,[4,1,1,1,1,3]],[String.fromCharCode(parseInt("00f2",16)),96,[1,1,4,3,1,1]],[String.fromCharCode(parseInt("00f3",16)),100,[1,1,4,1,3,1]],[String.fromCharCode(parseInt("00f4",16)),98,[4,1,1,3,1,1]],[String.fromCharCode(parseInt("00fc",16)),99,[1,1,3,1,4,1]],[String.fromCharCode(parseInt("00fa",16)),101,[3,1,1,1,4,1]],[String.fromCharCode(parseInt("00fd",16)),104,[2,1,1,2,1,4]],[String.fromCharCode(parseInt("00ff",16)),-1,[2,3,3,1,1,1,2]]]},code128c:{startSymbol:String.fromCharCode(parseInt("00fe",16)),stopSymbol:String.fromCharCode(parseInt("00ff",16)),validatorExp:"[0-9]",symbolTable:[["\0",0,[2,1,2,2,2,2]],[String.fromCharCode(parseInt("0001",16)),1,[2,2,2,1,2,2]],[String.fromCharCode(parseInt("0002",16)),2,[2,2,2,2,2,1]],[String.fromCharCode(parseInt("0003",16)),3,[1,2,1,2,2,3]],[String.fromCharCode(parseInt("0004",16)),4,[1,2,1,3,2,2]],[String.fromCharCode(parseInt("0005",16)),5,[1,3,1,2,2,2]],[String.fromCharCode(parseInt("0006",16)),6,[1,2,2,2,1,3]],["a",7,[1,2,2,3,1,2]],["\b",8,[1,3,2,2,1,2]],["\t",9,[2,2,1,2,1,3]],["\n",10,[2,2,1,3,1,2]],["\v",11,[2,3,1,2,1,2]],["\f",12,[1,1,2,2,3,2]],["\r",13,[1,2,2,1,3,2]],[String.fromCharCode(parseInt("000e",16)),14,[1,2,2,2,3,1]],[String.fromCharCode(parseInt("000f",16)),15,[1,1,3,2,2,2]],[String.fromCharCode(parseInt("0010",16)),16,[1,2,3,1,2,2]],[String.fromCharCode(parseInt("0011",16)),17,[1,2,3,2,2,1]],[String.fromCharCode(parseInt("0012",16)),18,[2,2,3,2,1,1]],[String.fromCharCode(parseInt("0013",16)),19,[2,2,1,1,3,2]],[String.fromCharCode(parseInt("0014",16)),20,[2,2,1,2,3,1]],[String.fromCharCode(parseInt("0015",16)),21,[2,1,3,2,1,2]],[String.fromCharCode(parseInt("0016",16)),22,[2,2,3,1,1,2]],[String.fromCharCode(parseInt("0017",16)),23,[3,1,2,1,3,1]],[String.fromCharCode(parseInt("0018",16)),24,[3,1,1,2,2,2]],[String.fromCharCode(parseInt("0019",16)),25,[3,2,1,1,2,2]],[String.fromCharCode(parseInt("001a",16)),26,[3,2,1,2,2,1]],[String.fromCharCode(parseInt("001b",16)),27,[3,1,2,2,1,2]],[String.fromCharCode(parseInt("001c",16)),28,[3,2,2,1,1,2]],[String.fromCharCode(parseInt("001d",16)),29,[3,2,2,2,1,1]],[String.fromCharCode(parseInt("001e",16)),30,[2,1,2,1,2,3]],[String.fromCharCode(parseInt("001f",16)),31,[2,1,2,3,2,1]],[" ",32,[2,3,2,1,2,1]],["!",33,[1,1,1,3,2,3]],['"',34,[1,3,1,1,2,3]],["#",35,[1,3,1,3,2,1]],["$",36,[1,1,2,3,1,3]],["%",37,[1,3,2,1,1,3]],["&",38,[1,3,2,3,1,1]],["'",39,[2,1,1,3,1,3]],["[",40,[2,3,1,1,1,3]],[")",41,[2,3,1,3,1,1]],["*",42,[1,1,2,1,3,3]],["+",43,[1,1,2,3,3,1]],[",",44,[1,3,2,1,3,1]],["-",45,[1,1,3,1,2,3]],[".",46,[1,1,3,3,2,1]],["/",47,[1,3,3,1,2,1]],["0",48,[3,1,3,1,2,1]],["1",49,[2,1,1,3,3,1]],["2",50,[2,3,1,1,3,1]],["3",51,[2,1,3,1,1,3]],["4",52,[2,1,3,3,1,1]],["5",53,[2,1,3,1,3,1]],["6",54,[3,1,1,1,2,3]],["7",55,[3,1,1,3,2,1]],["8",56,[3,3,1,1,2,1]],["9",57,[3,1,2,1,1,3]],[":",58,[3,1,2,3,1,1]],[";",59,[3,3,2,1,1,1]],["<",60,[3,1,4,1,1,1]],["=",61,[2,2,1,4,1,1]],[">",62,[4,3,1,1,1,1]],["?",63,[1,1,1,2,2,4]],["@",64,[1,1,1,4,2,2]],["A",65,[1,2,1,1,2,4]],["B",66,[1,2,1,4,2,1]],["C",67,[1,4,1,1,2,2]],["D",68,[1,4,1,2,2,1]],["E",69,[1,1,2,2,1,4]],["F",70,[1,1,2,4,1,2]],["G",71,[1,2,2,1,1,4]],["H",72,[1,2,2,4,1,1]],["I",73,[1,4,2,1,1,2]],["J",74,[1,4,2,2,1,1]],["K",75,[2,4,1,2,1,1]],["L",76,[2,2,1,1,1,4]],["M",77,[4,1,3,1,1,1]],["N",78,[2,4,1,1,1,2]],["O",79,[1,3,4,1,1,1]],["P",80,[1,1,1,2,4,2]],["Q",81,[1,2,1,1,4,2]],["R",82,[1,2,1,2,4,1]],["S",83,[1,1,4,2,1,2]],["T",84,[1,2,4,1,1,2]],["U",85,[1,2,4,2,1,1]],["V",86,[4,1,1,2,1,2]],["W",87,[4,2,1,1,1,2]],["X",88,[4,2,1,2,1,1]],["Y",89,[2,1,2,1,4,1]],["Z",90,[2,1,4,1,2,1]],["[",91,[4,1,2,1,2,1]],["\\",92,[1,1,1,1,4,3]],["]",93,[1,1,1,3,4,1]],["^",94,[1,3,1,1,4,1]],["_",95,[1,1,4,1,1,3]],["`",96,[1,1,4,3,1,1]],["a",97,[4,1,1,1,1,3]],["b",98,[4,1,1,3,1,1]],["c",99,[1,1,3,1,4,1]],[String.fromCharCode(parseInt("00f0",16)),102,[4,1,1,1,3,1]],[String.fromCharCode(parseInt("00fa",16)),101,[3,1,1,1,4,1]],[String.fromCharCode(parseInt("00fb",16)),100,[1,1,4,1,3,1]],[String.fromCharCode(parseInt("00fe",16)),105,[2,1,1,2,3,2]],[String.fromCharCode(parseInt("00ff",16)),-1,[2,3,3,1,1,1,2]]]},upcbarcode:{startSymbol:String.fromCharCode(parseInt("00fd",16)),stopSymbol:String.fromCharCode(parseInt("00ff",16)),validatorExp:"[0-9]",symbolTable:[["0",0,[3,2,1,1]],["1",1,[2,2,2,1]],["2",2,[2,1,2,2]],["3",3,[1,4,1,1]],["4",4,[1,1,3,2]],["5",5,[1,2,3,1]],["6",6,[1,1,1,4]],["7",7,[1,3,1,2]],["8",8,[1,2,1,3]],["9",9,[3,1,1,2]],["B",0,[1,1,1,1]]]},ean:{startSymbol:String.fromCharCode(parseInt("00fd",16)),stopSymbol:String.fromCharCode(parseInt("00fd",16)),validatorExp:"^[0-9]+$",symbolTable:[["0",0,[3,2,1,1]],["1",1,[2,2,2,1]],["2",2,[2,1,2,2]],["3",3,[1,4,1,1]],["4",4,[1,1,3,2]],["5",5,[1,2,3,1]],["6",6,[1,1,1,4]],["7",7,[1,3,1,2]],["8",8,[1,2,1,3]],["9",9,[3,1,1,2]],["A",0,[1,1,1,1]],["B",0,[1,1,1,1]]]}}})(jQuery,Syncfusion)});
