﻿using Data;
using Newtonsoft.Json;
using Syncfusion.JavaScript;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebUI
{
    public partial class Login : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                Console.WriteLine("Login page loading....");
                this.loginToLbl.InnerText = string.Format(this.loginToLbl.InnerText, GetGlobalResourceObject("GlobalResources", "ApplicationTitle").ToString());
                //this.usernameTxtBox.Text = System.Configuration.ConfigurationManager.ConnectionStrings["ComfortTransferConnectionString"].ConnectionString ;
                if (this.Page.User.Identity.IsAuthenticated == true)
                {
                    //Response.RedirectPermanent(@"~/Default.aspx");
                    Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                    Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                    string roleName = userData["Role"].ToString();

                    if (roleName == "Guest")
                    {
                        Response.RedirectPermanent(@"~/Contacts.aspx");
                    }
                    else
                    {
                        Response.RedirectPermanent(@"~/Default.aspx");
                    }
                }
            }
            catch (Exception exp)
            {
                ExceptionLogger.LogException(exp);
            }
        }

        protected void loginBtn_ServerClick(object sender, EventArgs e)
        {
            try
            {
                Console.WriteLine("Login page authenticating....");

                string joomlaUrl = System.Configuration.ConfigurationManager.AppSettings["JoomlaUrl"];
                AuthenticationResult authResult = Data.Business.AdministrationBusiness.AuthenticateUser(joomlaUrl, this.usernameTxtBox.Value, this.passwordTxtBox.Value).Result;

                if (authResult.Result == "Ok")
                {
                    Session.Clear();  //Καθαρίζει το Session
                    Session["UserFullName"] = authResult.UserDS.Users[0].FullName;
                    //Context.Response.SuppressFormsAuthenticationRedirect = true;
                    FormsAuthentication.RedirectFromLoginPage(this.usernameTxtBox.Value, true);

                    MentalViewDataSet ds = Data.Business.AdministrationBusiness.GetUserByUsername(usernameTxtBox.Value);

                    #region  Διαβάζει όλα τα στοιχεία του χρήστη για να τα βάλει στο cookie και στο Session.
                    Dictionary<string, object> userData = new Dictionary<string, object>();
                    userData.Add("UserId", ds.Users[0].UserId);
                    userData.Add("Role", ds.Users[0].RolesRow.Name);
                    userData.Add("TenantId", ds.Users[0].TenantId);
                    string roleName = userData["Role"].ToString();
                    CookieHandler.SetAuthCookie(Page.Response, this.usernameTxtBox.Value, true, userData.ToJsonString());

                    Session["TasksListSort"] = ds.Users[0].AppointmentsListSort;
                    #endregion
                    //FormsAuthenticationTicket cookie = CookieHandler.GetAuthCookie(this.Page);
                    //Session["Role"] = Data.CommonBusiness.AdministrationBusiness.GetRoleOfUser(usernameTxtBox.Text);

                    //Πριν κάνει το redirect στη σελίδα, καταγράφει τη σύνδεση στο Tenant.
                    Data.Business.TenantsBusiness.SetLastLoggedUserData(ds.Users[0].TenantId, authResult.UserDS.Users[0].FullName, ds.Users[0].UserId, DateTime.Now);

                    if (roleName == "Guest")
                    {
                        Response.RedirectPermanent(@"~/Contacts.aspx");
                    }
                    else
                    {
                        Response.RedirectPermanent(@"~/Default.aspx");
                    }
                }
                else if (authResult.Result == "LoginFailedInvalidUser")
                {
                    this.messageLbl.Text = Resources.GlobalResources.WrongCredentialsMessage;
                }
                else if (authResult.Result == "LoginFailedSubscriptionExpired")
                {
                    this.messageLbl.Text = Resources.GlobalResources.SubscriptionExpired;
                }
                else if (authResult.Result == "LoginFailedAccountNotExists")
                {
                    this.messageLbl.Text = Resources.GlobalResources.AccountNotExists;
                }
                else
                {
                    this.messageLbl.Text = Resources.GlobalResources.WrongCredentialsMessage;
                }
            }
            catch (ThreadAbortException)
            {
            }
            catch (Exception exp)
            {
                ExceptionLogger.LogException(exp);
                this.serverMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok, "");
            }
        }
    }
}