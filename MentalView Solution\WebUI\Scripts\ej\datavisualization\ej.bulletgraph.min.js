/*!
*  filename: ej.bulletgraph.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min","./../common/ej.globalize.min"],n):n()})(function(){(function(n,t,i){t.widget("ejBulletGraph","ej.datavisualization.BulletGraph",{element:null,model:null,validTags:["div"],_tags:[{tag:"qualitativeRanges",attr:["rangeEnd","rangeStroke","rangeOpacity"]},{tag:"quantitativeScaleSettings.featureMeasures",attr:["value","comparativeMeasureValue"]}],defaults:{locale:null,enableGroupSeparator:!1,value:0,comparativeMeasureValue:0,width:null,height:null,theme:"flatlight",orientation:"horizontal",flowDirection:"forward",qualitativeRangeSize:32,quantitativeScaleLength:475,tooltipSettings:{enableCaptionTooltip:!1,captionTemplate:null,visible:!0,template:null},quantitativeScaleSettings:{location:{x:10,y:10},minimum:0,maximum:10,interval:1,minorTicksPerInterval:4,majorTickSettings:{size:13,stroke:null,width:2},minorTickSettings:{size:7,stroke:null,width:2},tickPosition:"far",tickPlacement:"outside",labelSettings:{labelPlacement:"outside",labelPrefix:"",labelSuffix:"",stroke:null,size:12,offset:15,font:{fontFamily:"Segoe UI",fontStyle:"Normal ",fontWeight:"regular",opacity:1},position:"below"},featuredMeasureSettings:{stroke:null,width:6},comparativeMeasureSettings:{stroke:null,width:5},featureMeasures:[{value:null,comparativeMeasureValue:null,category:null}]},fields:{dataSource:null,query:null,tableName:null,category:null,featureMeasures:null,comparativeMeasure:null},enableAnimation:!0,enableResizing:!0,isResponsive:!0,applyRangeStrokeToTicks:!1,applyRangeStrokeToLabels:!1,qualitativeRanges:[{rangeEnd:4.3,rangeStroke:null,rangeOpacity:1},{rangeEnd:7.3,rangeStroke:null,rangeOpacity:1},{rangeEnd:10,rangeStroke:null,rangeOpacity:1}],captionSettings:{enableTrim:!0,textPosition:"float",textAlignment:"Near",textAnchor:"start",padding:5,textAngle:0,location:{x:17,y:30},text:"",font:{color:null,fontFamily:"Segoe UI",fontStyle:"Normal",size:"12px",fontWeight:"regular",opacity:1},subTitle:{textPosition:"float",textAlignment:"Near",textAnchor:"start",padding:5,textAngle:0,text:"",location:{x:10,y:45},font:{color:null,fontFamily:"Segoe UI",fontStyle:"Normal ",size:"12px",fontWeight:"regular",opacity:1}},indicator:{textPosition:"float",textAlignment:"Near",textAnchor:"start",padding:5,visible:!1,textAngle:0,textSpacing:3,text:"",symbol:{border:{color:null,width:1},color:null,shape:"",imageURL:"",size:{width:10,height:10},opacity:1},location:{x:10,y:60},font:{color:null,fontFamily:"Segoe UI",fontStyle:"Normal ",size:"12px",fontWeight:"regular",opacity:1}}},load:"",click:"",doubleClick:"",rightClick:"",drawTicks:null,drawLabels:null,drawCaption:null,drawIndicator:null,drawQualitativeRanges:null,drawFeatureMeasureBar:null,drawCategory:null,drawComparativeMeasureSymbol:null},observables:["value","comparativeMeasureValue"],value:t.util.valueFunction("value"),comparativeMeasureValue:t.util.valueFunction("comparativeMeasureValue"),dataTypes:{quantitativeScaleSettings:{labelSettings:"data",featureMeasures:"array"},fields:{dataSource:"data",query:"data"},qualitativeRanges:"array",captionSettings:"data",isResponsive:"boolean"},_init:function(){this._renderBulletGraph()},_destroy:function(){n(this.element).removeClass("e-datavisualization-bulletgraph e-js").find("#"+this.svgObject.id).remove()},_isSVG:function(){return window.SVGSVGElement?!0:!1},_value:t.util.valueFunction("value"),_comparativeMeasureValue:t.util.valueFunction("comparativeMeasureValue"),_qualitativeRanges:function(){this.redraw();this._trigger("refresh")},_quantitativeScaleSettings_featureMeasures:function(){this.redraw();this._trigger("refresh")},_renderBulletGraph:function(){var n=this.model.theme.toLowerCase();this._isSVG()&&(this.svgRenderer=new t.EjSvgRender(this.element),this.svgObject=this.svgRenderer.svgObj,this._trigger("load"),this._setSvgSize(this),this._setTheme(t.datavisualization.BulletGraph.Themes,n),this.bindEvents(),this._renderBulletElements(),this.model.enableAnimation&&this._animateMeasures())},_animateMeasures:function(){this._doAnimation();this._doLineAnimation()},_setSvgSize:function(i){var r=i,o=n(r.element).height(),f=90,u=t.isTouchDevice()?250:595,e=n(r.element).width();r.model.width?u=parseInt(r.model.width):e>0&&e<595?u=e:e>595&&(u=595);n(r.svgObject).width(u);r.model.height?f=parseInt(r.model.height):o>0?f=o:n(r.svgObject).css("display","block");n(r.svgObject).height(f);r.svgObject.setAttribute("width",u);r.svgObject.setAttribute("height",f)},_renderBulletElements:function(){var f=this.model.width?this.model.width:595,e=this.model.height?this.model.height:90,i;this.svgObject.setAttribute("viewBox","0 0 "+f+" "+e);this.svgObject.setAttribute("preserveAspectRatio","xMinYMin");this.svgWidth=n(this.svgObject).width();this.svgHeight=n(this.svgObject).height();var r=this.svgRenderer.createGroup({id:this.svgObject.id+"_captionGroup"}),t=this.svgRenderer.createGroup({id:this.svgObject.id+"_scaleGroup"}),u=this.svgRenderer.createGroup({id:this.svgObject.id+"_outerWrap"});this._initializeValues();i=this.model.quantitativeScaleLength/((this._scale.maximum-this._scale.minimum)/this._scale.interval);this._drawCaption();this._drawIndicator();this._drawCaptionGroup(r);this._scaleGroup=t;this._drawScale(r,t);this._drawQualitativeRanges(t);this._drawMajorTicks(i,t);this._drawMinorTicks(i,t);this._drawLabels(i,t);this._bindData();n(t).appendTo(u);n(r).appendTo(u);this._changeOrientation(t);n(u).appendTo(this.svgObject);n(this.svgObject).appendTo(this.element);this._bindHighlightRemoving()},_bindHighlightRemoving:function(){var i=this.model.browserInfo.isMSPointerEnabled,t,r=this.model.browserInfo.pointerEnabled;t=i?r?"pointerout":"MSPointerOut":"touchout mouseout";this._on(n("[id*="+this.svgObject.id+"_FeatureMeasure_]"),t,function(t){this.isTouch(t)||n(t.target).attr("opacity",1)})},_drawCaptionGroup:function(n){var u=this.model.captionSettings,f=u.subTitle,r=this._indicator?this._indicator.settings:u.indicator,c,b,k,l,a,v;u._location=f._location=r._location=c;var y=u.textPosition.toLowerCase()!="float"&&u.text!="",p=f.textPosition.toLowerCase()!="float"&&f.text!="",w=r.visible&&r.textPosition.toLowerCase()!="float";if(y||p||w){b=t.EjSvgRender.utils._measureText(u.text,null,u.font);k=t.EjSvgRender.utils._measureText(f.text,null,f.font);r.visible&&(l=this._indicator.bounds,v={width:r.symbol.size.width+l.width+r.textSpacing,height:Math.max(r.symbol.size.height,l.height)},a={x:0,y:0,width:v.width,height:v.height,padding:r.padding,anchor:r.textAnchor.toLowerCase(),alignment:r.textAlignment.toLowerCase()});var d={x:0,y:0,width:b.width,height:parseFloat(u.font.size),padding:u.padding,anchor:u.textAnchor.toLowerCase(),alignment:u.textAlignment.toLowerCase()},g={x:0,y:0,width:k.width,height:parseFloat(f.font.size),padding:f.padding,anchor:f.textAnchor.toLowerCase(),alignment:f.textAlignment.toLowerCase()},tt={x:this._scale.location.x,y:this._scale.location.y,width:this.model.quantitativeScaleLength,height:this.model.qualitativeRangeSize},nt=[y?u:null,p?f:null,w?r:null];this._positionTextGroup(nt,[d,g,a],tt);this._locateTextGroup(nt,[d,g,a])}var e=u._location==i?u.location:u._location,o=f._location==i?f.location:f._location,s=r._location==i?r.location:r._location;if(u.displayText=u.text,u.enableTrim==!0&&(e.x=e.x<0?0:e.x,e.y=e.y<0?0:e.y,u.displayText=this._displayText(u,e)),f.text!=""&&(f.displayText=f.text,u.enableTrim==!0&&(o.x=o.x<0?0:o.x,o.y=o.y<0?0:o.y,f.displayText=this._displayText(f,o))),r.visible){r.displayText=r.text;this.model.captionSettings.enableTrim==!0&&(s.x=s.x<0?0:s.x,s.y=s.y<0?0:s.y);var c=r._location?r._location:r.location,h={x:c.x+r.symbol.size.width/2,y:c.y-r.symbol.size.height/2,width:r.symbol.size.width,height:r.symbol.size.height},it=this._indicatorTextOptions(r,h,this._indicator.bounds,this.svgObject.id+"_Indicator"),rt=this._indicatorSymbolOptions(n,r);this._drawBulletSymbol(this._indicator.settings.symbol.shape,h,rt,n);this.model.captionSettings.enableTrim==!0&&(r.displayText=this._displayText(r,h))}u.displayText=u.textPosition.toLowerCase()!="float"?this._captionOverlap(this.model.captionSettings):u.displayText;this.svgRenderer.drawText(this._textOptions(u,this.svgObject.id+"_Caption"),u.displayText,n);f.text!=""&&(f.displayText=f.textPosition.toLowerCase()!="float"?this._subOverlap(this.model.captionSettings.subTitle):f.displayText,this.svgRenderer.drawText(this._textOptions(f,this.svgObject.id+"_SubTitle"),f.displayText,n));r.visible&&(r.displayText=r.textPosition.toLowerCase()!="float"?this._indOverlap(r,h):r.displayText,this.svgRenderer.drawText(it,r.displayText,n))},_captionOverlap:function(n){var t,i;return i=!0,n.textPosition==n.subTitle.textPosition&&n.textAlignment==n.subTitle.textAlignment&&n.textAngle>0&&n.textAngle<120?t=n.subTitle._location.y-n._location.y:n.textPosition==n.indicator.textPosition&&n.textAlignment==n.indicator.textAlignment&&n.textAngle>0&&n.textAngle<120?t=n.indicator._location.y-n._location.y:n.textPosition==n.subTitle.textPosition&&n._location.y<n.subTitle._location.y&&n.textAngle>0&&n.textAngle<180?t=n.subTitle._location.y-n._location.y:n.textPosition==n.subTitle.textPosition&&n._location.y>n.subTitle._location.y&&n.textAngle>190&&n.textAngle<360?t=n._location.y-n.subTitle._location.y:n.textPosition==n.indicator.textPosition&&n._location.y<n.indicator._location.y&&n.textAngle>0&&n.textAngle<180?t=n.indicator._location.y-n._location.y:n.textPosition==n.indicator.textPosition&&n._location.y>n.indicator._location.y&&n.textAngle>190&&n.textAngle<360&&(t=n._location.y-n.indicator._location.y),this._trim(n.displayText,n,t,i)},_indOverlap:function(n,t){var u,f,i,r;return f=!0,i=this.model.captionSettings.subTitle,r=this.model.captionSettings,n.textPosition==i.textPosition&&n.textAlignment==i.textAlignment&&n.textAngle>190&&n.textAngle<360?u=t.y-i._location.y:n.textPosition==r.textPosition&&n.textAlignment==r.textAlignment&&n.textAngle>190&&n.textAngle<360?u=t.y-r._location.y:n.textPosition==i.textPosition&&n._location.y<i._location.y&&n.textAngle>0&&n.textAngle<180?u=i._location.y-n._location.y:n.textPosition==i.textPosition&&n._location.y>i._location.y&&n.textAngle>190&&n.textAngle<360?u=n._location.y-i._location.y:n.textPosition==r.textPosition&&n._location.y<r._location.y&&n.textAngle>0&&n.textAngle<180?u=r._location.y-n._location.y:n.textPosition==r.textPosition&&n._location.y>r._location.y&&n.textAngle>190&&n.textAngle<360&&(u=n._location.y-r._location.y),this._trim(n.displayText,n,u,f)},_subOverlap:function(n){var r,u,t,i;return u=!0,t=this.model.captionSettings.indicator,i=this.model.captionSettings,n.textPosition==t.textPosition&&n.textAlignment==t.textAlignment&&n.textAngle>0&&n.textAngle<180?r=t._location.y-n._location.y:n.textPosition==i.textPosition&&n.textAlignment==i.textAlignment&&n.textAngle>190&&n.textAngle<360?r=n._location.y-i._location.y:n.textPosition==t.textPosition&&n._location.y<t._location.y&&n.textAngle>0&&n.textAngle<180?r=t._location.y-n._location.y:n.textPosition==t.textPosition&&n._location.y>t._location.y&&n.textAngle>190&&n.textAngle<360?r=n._location.y-t._location.y:n.textPosition==i.textPosition&&n._location.y<i._location.y&&n.textAngle>0&&n.textAngle<180?r=i._location.y-n._location.y:n.textPosition==i.textPosition&&n._location.y>i._location.y&&n.textAngle>190&&n.textAngle<360&&(r=n._location.y-i._location.y),this._trim(n.displayText,n,r,u)},rotatedLabel:function(n,i,r,u){var f={"font-size":n.size,transform:"rotate("+r+",0,0)","font-family":n.fontFamily,"font-style":n.fontStyle,rotateAngle:"rotate("+r+"deg)","text-anchor":"middle"},e=i.svgRenderer.createText(f,u);return Math.ceil(t.EjSvgRender.utils._measureBounds(e,i).width)},calcGap:function(n,t,i,r,u,f,e){var o;return n>=0&&n<=90?r+t.y>=e&&n>0?(o=e-t.y,u=!0):r+t.y<=e&&i+t.x>=f&&n>0?o=f-t.x:r+t.y<=e&&n>0?(o=e-t.y,u=!0):o=f-t.x:n>90&&n<180?r+t.y>=e?(o=e-t.y,u=!0):r+t.y<=e&&i+t.x>=f?o=t.x:i+t.x<=f?o=t.x:(o=e-t.y,u=!0):n>=180&&n<270?t.x-i>=0?(o=t.y,u=!0):t.y-r<=0&&t.x-i<=0?o=t.x:t.y-r<=0?(o=e-t.y,u=!0):o=t.x:i+t.x<=f?(o=t.y,u=!0):t.y-r>=0&&i+t.x>=f?o=f-t.x:i+t.x>=f?o=f-t.x:(o=t.y,u=!0),{trimSize:o,rotate:u}},_scaleLoc:function(){var o=this._scale,u=o.labelSettings.offset,i=o.majorTickSettings.size,n,e,s=this._scale.labelSettings,h=t.EjSvgRender.utils._measureText(s.labelPrefix+o.maximum+s.labelSuffix,null,s),f=this._orientation==t.datavisualization.BulletGraph.Orientation.Horizontal?h.height:h.width,r,c;n=o.location.y;r=this.model.qualitativeRangeSize;c=this._tickPosition+this._tickPlacement+this._labelPosition+this._labelPlacement;this._tickPosition=="center"&&(i=i>r&&(i-r)/2);switch(c){case"faroutsidebelowoutside":e=n+f+r+u+(i>u&&i-u);break;case"faroutsidebelowinside":e=n+i+r;n=n-(u+f>r&&u+f-r);break;case"faroutsideaboveoutside":e=n+i+r;n=n-(u+f);break;case"faroutsideaboveinside":e=n+i+r+(u+f>r+i?u+f-(r+i):0);break;case"farinsidebelowoutside":e=n+f+r+u;n=n-(i>r&&i-r);break;case"farinsidebelowinside":e=n+r;n=n-(i>r&&i-r)-(u+f>r+i&&u+f-i);break;case"farinsideaboveoutside":e=n+r;n=n-(u+f)-(i>u+f+r&&i-(u+f+r));break;case"farinsideaboveinside":e=n+r+(u+f>r&&f+u-r);n=n-(i>r&&i-r);break;case"nearoutsidebelowoutside":e=n+r+u+f;n=n-i;break;case"nearoutsidebelowinside":e=n+r;n=n-i-(u+f>r+i&&u+f-(r+i));break;case"nearoutsideaboveoutside":e=n+r;n=n-i-(i<u+f&&u+f-i);break;case"nearoutsideaboveinside":e=n+r+(u+f>r&&u+f-r);n=n-i;break;case"nearinsidebelowoutside":e=n+r+(i>u+f+r?i-r:u+f);break;case"nearinsidebelowinside":e=n+r+(i>r&&i-r);n=n-(u+f>r&&u+f-r);break;case"nearinsideaboveoutside":e=n+(i>r?i:r);n=n-u-f;break;case"nearinsideaboveinside":e=n+r+(i>f+u?i-r:f+u-r);break;case"centeroutsidebelowoutside":e=n+r+(i>u+f?i:u+f);n=n-i;break;case"centeroutsidebelowinside":e=n+r+i;n=n-i-(u+f>r+i&&u+f-(r+i));break;case"centeroutsideaboveoutside":e=n+r+i;n=n-i-(i<u+f&&u+f-i);break;case"centeroutsideaboveinside":e=n+r+(u+f>r+i?u+f-r-i:i);n=n-i;break;case"centerinsidebelowoutside":e=n+r+(i>u+f?i:u+f);n=n-i;break;case"centerinsidebelowinside":e=n+r+i;n=n-(u+f-r>i?u+f-r:i);break;case"centerinsideaboveoutside":e=n+r+i;n=n-i-(i<u+f&&u+f-i);break;case"centerinsideaboveinside":e=n+r+(i>f+u?i:f+u-r);n=n-i}return{x:o.location.x,y:n,height:e}},_displayText:function(i,r){var f=n.extend(!0,r),e=i.textAngle%360,y=i.text,w=i.font,a=this._scale,p=this._scale.labelSettings,b=t.EjSvgRender.utils._measureText(p.labelPrefix+a.minimum+p.labelSuffix,null,p).width,k=t.EjSvgRender.utils._measureText(y,null,w).width,d=t.EjSvgRender.utils.rotatedLabel(i,this,e,i.text),v=this.rotatedLabel(w,this,e,i.text),o,s=this.svgHeight,h=this.svgWidth,l=!1,u=this._scaleLoc(),c,g;return i.textSpacing&&(k=k+i.textSpacing,v=v+i.textSpacing),this._orientation==t.datavisualization.BulletGraph.Orientation.Horizontal?(f.x<=u.x&&u.y<=f.y&&f.y<=u.height?h=a.location.x-b/2:f.x<=u.x&&f.y<=u.y&&e<90?s=u.y:f.x>=u.x&&f.x<=u.x+this.model.quantitativeScaleLength&&f.y<=u.y?s=u.y:f.y<=u.y&&f.x>=u.x+this.model.quantitativeScaleLength&&e>=90?s=u.y:f.x>=u.x+this.model.quantitativeScaleLength&&u.y<=f.y&&f.y<=u.height&&e<270?(h=h-(u.x+this.model.quantitativeScaleLength),f.x=f.x-(u.x+this.model.quantitativeScaleLength)):f.x<=u.x&&f.y>=u.height&&e>270?f.y=f.y-u.height:f.x>=u.x&&f.x<=u.x+this.model.quantitativeScaleLength&&f.y>=u.height?f.y=f.y-u.height:f.x>=u.x+this.model.quantitativeScaleLength&&f.y>=u.height&&e>=180&&e<270&&(f.y=f.y-u.height),o=this.calcGap(e,f,v,d,l,h,s),l=o.rotate,o=o.trimSize):(c=u.height-u.y,u.y=this.svgHeight-a.location.y-this.model.quantitativeScaleLength,u.height=u.y+this.model.quantitativeScaleLength,f.x<=u.x&&u.y<=f.y&&f.y<=u.height?h=a.location.x-b/2:f.x<=u.x&&f.y<=u.y&&e<90?s=u.y:f.x>=u.x&&f.x<=u.x+c&&f.y<=u.y?s=u.y:f.y<=u.y&&f.x>=u.x+c&&e>=90?s=u.y:f.x>=u.x+c&&u.y<=f.y&&f.y<=u.height&&e<=260?(h=h-(u.x+c),f.x=f.x-(u.x+c)):f.x<=u.x&&f.y>=u.height&&e>=280?f.y=f.y-u.height:f.x>=u.x&&f.x<=u.x+c&&f.y>=u.height?f.y=f.y-u.height:f.x>=u.x+c&&f.y>=u.height&&e>=180&&e<=260&&(f.y=f.y-u.height),o=this.calcGap(e,f,v,d,l,h,s),l=o.rotate,o=o.trimSize),g=y,this._trim(y,i,o,l)},_trim:function(n,i,r,u){var f=n,h=this.rotatedLabel(i.font,this,i.textAngle,n),s=t.EjSvgRender.utils._measureText(n,null,i.font).width,o,e,c;if(i.textSpacing&&(s=s+i.textSpacing,h=h+i.textSpacing),s>r&&n!=""){for(e=1;e<=n.toString().length;e++)if(n=f.toString().substring(0,e)+"... ",o=u==!0?t.EjSvgRender.utils.rotatedLabel(i,this,i.textAngle,n):t.EjSvgRender.utils._measureText(n,null,i.font).width,i.textSpacing&&(o=o+i.textSpacing),o>=r){n=n.toString().substring(0,e-1)+"... ";f=n;break}c=f.toString(0,n.toString.lenght-4);c!=f&&(f=n)}return f},_getVerticalScaleLocation:function(n){return{x:this._scale.location.y,y:this.svgHeight-this._scale.location.x-n}},_horizontalTextPositioning:function(n,i,r,u,f){var h=n.textPosition.toLowerCase(),e=i.alignment,r=this._scaleLoc(),o=this._scale.labelSettings,s=t.EjSvgRender.utils._measureText(o.labelPrefix+this._scale.maximum+o.labelSuffix,null,o).width;switch(h){case"left":i.x=r.x-i.width-i.padding-s/2;i.y=r.y+(e=="center"?u.height/2+i.height/3:e=="far"?u.height:i.height);f[0].push(i);break;case"right":i.x=r.x+u.width+i.padding+s/2;i.y=r.y+(e=="center"?u.height/2+i.height/3:e=="far"?u.height:i.height);f[1].push(i);break;case"top":i.x=e=="center"?u.x+u.width/2-i.width/2:e=="near"?u.x:u.x+u.width-i.width;i.y=r.y-i.padding;f[2].push(i);break;case"bottom":i.x=e=="center"?u.x+u.width/2-i.width/2:e=="near"?u.x:u.x+u.width-i.width;i.y=r.height+i.height/2+i.padding;f[3].push(i)}},_VerticalTextPositioning:function(n,t,i,r,u){var o=n.textPosition.toLowerCase(),f=t.alignment,e=this._scaleLoc();switch(o){case"left":t.x=e.y-t.width-t.padding;t.y=i.y+(f=="center"?r.height/2+t.height/3:f=="far"?r.height:t.height);this._tickPosition=="near"&&this._tickPlacement=="inside"&&this._labelPosition=="above"&&this._labelPlacement=="inside"&&(t.x+=this._scale.majorTickSettings.size);u[0].push(t);break;case"right":t.x=e.height+t.padding;t.y=i.y+(f=="center"?r.height/2+t.height/3:f=="far"?r.height:t.height);u[1].push(t);break;case"top":t.x=r.x-t.width/2+(f=="center"?r.width/2:f=="far"&&r.width);t.y=r.y-t.padding;u[2].push(t);break;case"bottom":t.x=r.x-t.width/2+(f=="center"?r.width/2:f=="far"&&r.width);t.y=r.y+r.height+t.height+t.padding-t.height/3+this._scale.labelSettings.size/2;u[3].push(t)}},_positionTextGroup:function(n,t,i){for(var f=[[],[],[],[]],e=this._orientation=="horizontal",u=e?this._scaleLocation:this._getVerticalScaleLocation(i.width),o=e?i:{x:u.x,y:u.y,width:i.height,height:i.width},r=0;r<n.length;r++)n[r]&&(e?this._horizontalTextPositioning(n[r],t[r],u,o,f):this._VerticalTextPositioning(n[r],t[r],u,o,f));this._avoidElementsOverlapping(f)},_avoidElementsOverlapping:function(n){for(var t,f,r,i,u=0;u<n.length;u++){t=n[u];f=!1;do for(r=0;r<t.length;r++)for(i=r-1;i>=0;i--)if(this._isOverlapping(t[r],t[i])){switch(u){case 0:case 1:t[i].y>t[r].y?t[i].y=t[r].y+t[i].height+(t[i].padding>1?t[i].padding:1):t[r].y=t[i].y+t[r].height+(t[r].padding>1?t[r].padding:1);break;case 2:t[i].y=t[r].y-t[r].height-(t[i].padding>1?t[i].padding:1);break;default:t[r].y=t[i].y+t[i].height+(t[i].padding>1?t[i].padding:1)}f=!0;break}else f=!1;while(f)}this._applyLeftRightAnchor([n[0],n[1]]);this._applyTopBottomAnchor([n[2],n[3]])},_locateTextGroup:function(n,t){for(var i=0;i<n.length;i++)n[i]&&(n[i]._location={x:0,y:0},n[i]._location.x=t[i].x,n[i]._location.y=t[i].y)},_applyLeftRightAnchor:function(n){for(var i,r,t,f,u=0;u<n.length;u++){for(i=n[u],r=0,t=0;t<i.length;t++)r=Math.max(r,i[t].width);for(t=0;t<i.length;t++)f=i[t].anchor,i[t].x+=f=="start"?u==0&&i[t].width-r:f=="middle"?u==0?(i[t].width-r)/2:(r-i[t].width)/2:u!=0&&r-i[t].width}},_applyTopBottomAnchor:function(n){for(var t,r=0;r<n.length;r++){var u=[],f=[],e=[],i=n[r];for(t=0;t<i.length;t++)i[t].alignment=="near"?u.push(i[t]):i[t].alignment=="far"?f.push(i[t]):e.push(i[t]);this._applyNearAnchor(u);this._applyCenterAnchor(e);this._applyFarAnchor(f)}},_applyNearAnchor:function(n){var i,t;if(n.length>1){for(i=0,t=0;t<n.length;t++)i=Math.max(i,n[t].width);for(t=0;t<n.length;t++)n[t].x+=n[t].anchor=="start"?this._orientation=="vertical"&&(n[t].width-i)/2:n[t].anchor=="middle"?this._orientation=="horizontal"&&(i-n[t].width)/2:this._orientation=="horizontal"?i-n[t].width:(i-n[t].width)/2}},_applyCenterAnchor:function(n){var i,t;if(n.length>1){for(i=0,t=0;t<n.length;t++)i=Math.max(i,n[t].width);for(t=0;t<n.length;t++)n[t].anchor=="start"?n[t].x+=(n[t].width-i)/2:n[t].anchor=="end"&&(n[t].x+=(i-n[t].width)/2)}},_applyFarAnchor:function(n){var i,t;if(n.length>1){for(i=0,t=0;t<n.length;t++)i=Math.max(i,n[t].width);for(t=0;t<n.length;t++)n[t].anchor=="start"?n[t].x+=this._orientation=="horizontal"?n[t].width-i:(n[t].width-i)/2:n[t].anchor=="middle"?n[t].x+=this._orientation=="horizontal"&&(n[t].width-i)/2:n[t].anchor=="end"&&(n[t].x+=this._orientation=="vertical"&&(i-n[t].width)/2)}},_isOverlapping:function(n,t){return!(n.x+n.width<t.x||n.x>t.x+t.width||n.y-n.height>t.y||n.y<t.y-t.height)},_initializeValues:function(){this._scale=this.model.quantitativeScaleSettings;this._labelPosition=this._scale.labelSettings.position.toLowerCase();this._tickPosition=this._scale.tickPosition.toLowerCase();this._flowDirection=this.model.flowDirection.toLowerCase();this._orientation=this.model.orientation.toLowerCase();this._tickPlacement=this._scale.tickPlacement.toLowerCase();this._labelPlacement=this._scale.labelSettings.labelPlacement.toLowerCase()},_changeOrientation:function(n){this._orientation==t.datavisualization.BulletGraph.Orientation.Vertical&&n.setAttribute("transform","translate(0,"+this.svgHeight+")rotate(-90)")},_setModel:function(i){var f=!0,r,u;for(r in i){this.model.enableAnimation=!1;switch(r){case"height":this.model.height=i[r];break;case"width":this.model.width=i[r];break;case"theme":this.model.theme=i[r];this._setTheme(t.datavisualization.BulletGraph.Themes,this.model.theme);break;case"orientation":this.model.orientation=i[r];break;case"flowDirection":this.model.flowDirection=i[r];break;case"qualitativeRangeSize":this.model.qualitativeRangeSize=i[r];break;case"quantitativeScaleLength":this.model.quantitativeScaleLength=i[r];break;case"quantitativeScaleSettings":n.extend(!0,this.model.quantitativeScaleSettings,{},i[r]);break;case"applyRangeStrokeToTicks":this.model.applyRangeStrokeToTicks=i[r];break;case"applyRangeStrokeToLabels":this.model.applyRangeStrokeToLabels=i[r];break;case"qualitativeRanges":n.extend(!0,this.model.qualitativeRanges,{},i[r]);break;case"captionSettings":n.extend(!0,this.model.captionSettings,{},i[r]);break;case"dataSource":n.extend(!0,this.model.fields,{},i[r]);break;case"value":for(u=0;this.model.quantitativeScaleSettings.featureMeasures[u]!=null;u++)this.model.quantitativeScaleSettings.featureMeasures[u].value=parseFloat(this.value());break;case"comparativeMeasureValue":for(u=0;this.model.quantitativeScaleSettings.featureMeasures[u]!=null;u++)this.model.quantitativeScaleSettings.featureMeasures[u].comparativeMeasureValue=parseFloat(this.comparativeMeasureValue());break;case"enableAnimation":this.model.enableAnimation=i[r];this.model.enableAnimation&&(n(this.svgObject).empty(),this._renderBulletElements(),this._animateMeasures());f=!1}}f&&(n(this.svgObject).empty(),this._renderBulletElements())},_bindData:function(){t.isNullOrUndefined(this.model.fields)||this.model.fields.dataSource==null?(this._dataCount=this._scale.featureMeasures.length,this._drawMeasures()):this.model.fields.dataSource instanceof t.DataManager?this._initDataSource():(this._dataCount=this.model.fields.dataSource.length,this._drawMeasures())},_drawMeasures:function(){this._drawFeatureMeasureBar();this._drawComparativeMeasureSymbol()},_initDataSource:function(){var t=this._columnToSelect(this.model.fields),n=this,i=this.model.fields.dataSource.executeQuery(t);i.done(function(t){n.model.fields.dataSource=t.result;n._dataCount=t.result.length;n._drawFeatureMeasureBar();n._drawComparativeMeasureSymbol();n._bindHighlightRemoving()})},_columnToSelect:function(n){var u=[],r=t.Query(),i;if(t.isNullOrUndefined(n.query)){for(i in n)i!=="tableName"&&i!=="query"&&i!=="dataSource"&&u.push(n[i]);u.length>0&&r.select(u);this.model.fields.dataSource.dataSource.url.match(n.tableName+"$")||t.isNullOrUndefined(n.tableName)||r.from(n.tableName)}else r=n.query;return r},_drawCaption:function(){if(this.model.drawCaption){var t={font:this.model.captionSettings.font,location:this.model.captionSettings.location,subTitle:this.model.captionSettings.subTitle,text:this.model.captionSettings.text,textAngle:this.model.captionSettings.textAngle};this._trigger("drawCaption",t);this.model.captionSettings=n.extend(this.model.captionSettings,t)}},_drawBulletSymbol:function(n,i,r,u){var e="M "+(i.x-i.width/2+i.width)+" "+(i.y+i.height/4)+" L "+(i.x-i.width/2+i.width)+" "+(i.y+-i.height/4)+" L "+(i.x-i.width/2+i.width/2)+" "+(i.y+-i.height/4)+" L "+(i.x-i.width/2+i.width/2)+" "+(i.y+-i.height/2)+" L "+(i.x-i.width/2)+" "+i.y+" L "+(i.x-i.width/2+i.width/2)+" "+(i.y+i.height/2)+" L "+(i.x-i.width/2+i.width/2)+" "+(i.y+i.height/4)+" L "+(i.x-i.width/2+i.width)+" "+(i.y+i.height/4),o,l,a,v,s,y,p,f,d,g,h;switch(n.toLowerCase()){case"circle":o=Math.min(i.height,i.width)/2;r.cx=i.x;r.cy=i.y;r.r=o;this.svgRenderer.drawCircle(r,u);break;case"leftarrow":r.d=e;this.svgRenderer.drawPath(r,u);break;case"rightarrow":r.d=e;r.transform="rotate(180,"+i.x+","+i.y+")";this.svgRenderer.drawPath(r,u);break;case"uparrow":r.d=e;r.transform="rotate(90,"+i.x+","+i.y+")";this.svgRenderer.drawPath(r,u);break;case"downarrow":r.d=e;r.transform="rotate(-90,"+i.x+","+i.y+")";this.svgRenderer.drawPath(r,u);break;case"cross":l="M "+(i.x+-i.width/2)+" "+i.y+" L "+(i.x+i.width/2)+" "+i.y+" M "+i.x+" "+(i.y+i.height/2)+" L "+i.x+" "+(i.y+-i.height/2);r.d=l;r.stroke=r.stroke?r.stroke:r.fill;this.svgRenderer.drawPath(r,u);break;case"horizontalline":a="M "+(i.x+-i.width/2)+" "+i.y+" L "+(i.x+i.width/2)+" "+i.y;r.d=a;r.stroke=r.stroke?r.stroke:r.fill;this.svgRenderer.drawPath(r,u);break;case"verticalline":v="M "+i.x+" "+(i.y+i.height/2)+" L "+i.x+" "+(i.y+-i.height/2);r.d=v;r.stroke=r.stroke?r.stroke:r.fill;this.svgRenderer.drawPath(r,u);break;case"triangle":s="M "+(i.x+-i.width/2)+" "+(i.y+i.height/2)+" L "+i.x+" "+(i.y+-i.height/2)+" L "+(i.x+i.width/2)+" "+(i.y+i.height/2)+" L "+(i.x+-i.width/2)+" "+(i.y+i.height/2)+" Z";r.d=s;this.svgRenderer.drawPath(r,u);break;case"invertedtriangle":s="M "+(i.x+-i.width/2)+" "+(i.y+i.height/2)+" L "+i.x+" "+(i.y+-i.height/2)+" L "+(i.x+i.width/2)+" "+(i.y+i.height/2)+" L "+(i.x+-i.width/2)+" "+(i.y+i.height/2)+" Z";r.d=s;r.transform="rotate(180,"+i.x+","+i.y+")";this.svgRenderer.drawPath(r,u);break;case"hexagon":y="M "+(i.x+-i.width/2)+" "+i.y+" L "+(i.x+-i.width/4)+" "+(i.y+-i.height/2)+" L "+(i.x+i.width/4)+" "+(i.y+-i.height/2)+" L "+(i.x+i.width/2)+" "+i.y+" L "+(i.x+i.width/4)+" "+(i.y+i.height/2)+" L "+(i.x+-i.width/4)+" "+(i.y+i.height/2)+" L "+(i.x+-i.width/2)+" "+i.y;r.d=y;this.svgRenderer.drawPath(r,u);break;case"wedge":p="M "+(i.x+-i.width/2)+" "+i.y+" L "+(i.x+i.width/2)+" "+(i.y+-i.height/2)+" L "+(i.x+i.width/4)+" "+i.y+" L "+(i.x+i.width/2)+" "+(i.y+i.height/2)+" L "+(i.x+-i.width/2)+" "+i.y;r.d=p;this.svgRenderer.drawPath(r,u);break;case"pentagon":var o=Math.sqrt(i.height*i.height+i.width*i.width)/2,c=t.EjSvgRender.utils._getStringBuilder();for(f=0;f<=5;f++){var nt=f*72,w=Math.PI/180*nt,b=o*Math.cos(w),k=o*Math.sin(w);f==0?c.append("M "+(i.x+b)+" "+(i.y+k)+" "):c.append("L "+(i.x+b)+" "+(i.y+k)+" ")}r.d=c.toString();this.svgRenderer.drawPath(r,u);break;case"star":d="M "+(i.x+i.width/3)+" "+(i.y+-i.height/2)+" L "+(i.x+-i.width/2)+" "+(i.y+i.height/6)+" L "+(i.x+i.width/2)+" "+(i.y+i.height/6)+" L "+(i.x+-i.width/3)+" "+(i.y+-i.height/2)+" L "+i.x+" "+(i.y+i.height/2)+" L "+(i.x+i.width/3)+" "+(i.y+-i.height/2);r.d=d;this.svgRenderer.drawPath(r,u);break;case"rectangle":g="M "+(i.x+-i.width/2)+" "+(i.y+-i.height/2)+" L "+(i.x+i.width/2)+" "+(i.y+-i.height/2)+" L "+(i.x+i.width/2)+" "+(i.y+i.height/2)+" L "+(i.x+-i.width/2)+" "+(i.y+i.height/2)+" L "+(i.x+-i.width/2)+" "+(i.y+-i.height/2);r.d=g;this.svgRenderer.drawPath(r,u);break;case"trapezoid":h="M "+(i.x+-i.width/2)+" "+i.y+" L "+(i.x+-i.width/2)+" "+(i.y+-i.height/4)+" L "+(i.x+-i.width/2+i.width)+" "+(i.y+-i.height/2)+" L "+(i.x+-i.width/2+i.width)+" "+(i.y+i.height/2)+" L "+(i.x+-i.width/2)+" "+(i.y+i.height/4)+" L "+(i.x+-i.width/2)+" "+i.y;r.d=h;this.svgRenderer.drawPath(r,u);break;case"diamond":h="M "+(i.x+-i.width/2)+" "+i.y+" L "+i.x+" "+(i.y+-i.height/2)+" L "+(i.x+i.width/2)+" "+i.y+" L "+i.x+" "+(i.y+i.height/2)+" L "+(i.x+-i.width/2)+" "+i.y;r.d=h;this.svgRenderer.drawPath(r,u);break;case"ellipse":r.cx=i.x;r.cy=i.y;r.rx=i.width/2;r.ry=i.height/2;this.svgRenderer.drawEllipse(r,u);break;case"image":r.x=i.x-i.width/2;r.y=i.y-i.height/2;r.width=i.width;r.height=i.height;r.href=this.model.captionSettings.indicator.symbol.imageURL;r.visibility="visible";this.svgRenderer.drawImage(r,u)}},_drawIndicator:function(){var i,r,u;this.model.captionSettings.indicator.visible&&(i=this.model.captionSettings.indicator,r=n.extend({},{indicatorSettings:this.model.captionSettings.indicator}),this.model.drawIndicator&&(this._trigger("drawIndicator",r),r.cancel==!1&&(i=r.indicatorSettings,this.model.captionSettings.indicator=i)),u=t.EjSvgRender.utils._measureText(i.text,null,i.font),this._indicator={bounds:u,settings:i})},_indicatorTextOptions:function(n,t,i,r){var u=n._location?n._location:n.location;return{"class":r,x:u.x+t.width+n.textSpacing,y:u.y-t.height/2+parseFloat(n.font.size)/3,fill:n.font.color,"font-size":n.font.size,"font-family":n.font.fontFamily,"font-style":n.font.fontStyle,"font-weight":n.font.fontWeight,"text-anchor":"start",opacity:n.font.opacity,transform:"rotate("+n.textAngle+","+t.x+","+t.y+")"}},_indicatorSymbolOptions:function(n,t){return{id:n.id+"_indicatorSymbol",stroke:t.symbol.border.color,fill:t.symbol.color,"stroke-width":t.symbol.border.width,opacity:t.symbol.opacity}},_textOptions:function(n,t){var i=n._location?n._location:n.location;return{"class":t,x:i.x,y:i.y,fill:n.font.color,"font-size":n.font.size,"font-family":n.font.fontFamily,"font-style":n.font.fontStyle,"font-weight":n.font.fontWeight,"text-anchor":"start",opacity:n.font.opacity,cursor:"default",transform:"rotate("+n.textAngle+","+i.x+","+i.y+")"}},_drawScale:function(n,i){var r=this._scale._location?this._scale._location:this._scale.location,u,f,e;u=this._tickPosition==t.datavisualization.BulletGraph.TickPosition.Far||this._tickPosition==t.datavisualization.BulletGraph.TickPosition.Center?this._labelPosition==t.datavisualization.BulletGraph.LabelPosition.Below?r.y:r.y+this._scale.labelSettings.offset+this._scale.labelSettings.size:this._labelPosition==t.datavisualization.BulletGraph.LabelPosition.Below?r.y+this._scale.majorTickSettings.size:r.y+this._scale.labelSettings.offset+this._scale.labelSettings.size+this._scale.majorTickSettings.size;f=this._flowDirection==t.datavisualization.BulletGraph.FlowDirection.Forward?r.x:this._orientation==t.datavisualization.BulletGraph.Orientation.Horizontal?r.x-this._scale.labelSettings.offset:r.x;e={id:this.svgObject.id+"_SvgScale",x:f,y:u,width:this.model.quantitativeScaleLength,height:this.model.qualitativeRangeSize,fill:"transparent","stroke-width":0};this.svgRenderer.drawRect(e,i)},_drawMajorTicks:function(n,i){var r,l,tt=0,a=this._scale.location,b=this._tickPlacement=="inside",s=this._scale.location.x,k=this.model.quantitativeScaleLength,it=this._scale.labelSettings.offset,d=this.model.qualitativeRangeSize,u=this._scale.majorTickSettings.width,v=this._scale.majorTickSettings.size,f=this._orientation==t.datavisualization.BulletGraph.Orientation.Horizontal,o=this._flowDirection==t.datavisualization.BulletGraph.FlowDirection.Forward,rt=this._labelPosition==t.datavisualization.BulletGraph.LabelPosition.Below,e,y,p,w,nt;r=o?f?s+u/2:s+k+u/2:f?s+k-u/2:s+u/2;l=this._tickPosition=="far"?a.y+d+(b?-v:0):this._tickPosition=="near"?a.y+(b?0:-v):a.y+d/2-v/2;var h=this._scale.minimum,c=this._scale.maximum,g=this._scale.interval;for(e=h;e<=c;e+=g)tt++,e>=c&&(f&&!o?r+=u:r-=u),!f&&o&&(r=e>=c?r+u:e==h?r-u:r),this.model.applyRangeStrokeToTicks&&(y=this._bindingRangeStrokes(r-u/2)),p=this._majorTickLines(this._scale,r,l,y),!f&&o&&e==h&&(r=r+u),this.model.drawTicks&&(w={majorTickSettings:this._scale.majorTickSettings,minorTickSettings:this._scale.minorTickSettings,minorTicksPerInterval:this._scale.minorTicksPerInterval,maximum:c,minimum:h,interval:g},this._trigger("drawTicks",w),p=this._majorTickLines(w,r,l,y)),nt=this.svgRenderer.createLine(p),r=!o&&f||o&&!f?r-n:r+n,i.appendChild(nt)},_majorTickLines:function(n,t,i,r){return{x1:t,y1:i,x2:t,y2:i+n.majorTickSettings.size,"stroke-width":n.majorTickSettings.width,stroke:this.model.applyRangeStrokeToTicks&&r?r:n.majorTickSettings.stroke}},_forwardStrokeBinding:function(n){if(n>=this._scale.location.x&&n<=this._rangeCollection[0]+this._scale.location.x)return this.model.qualitativeRanges[0].rangeStroke;for(var t=0;t<=this._rangeCollection.length-1;t++)if(n>=this._rangeCollection[t]+this._scale.location.x&&n<=this._rangeCollection[t+1]+this._scale.location.x)return this.model.qualitativeRanges[t+1].rangeStroke},_backwardStrokeBinding:function(t){var i,r;if(t>=this._rangeCollection[this._rangeCollection.length-1])return this.model.qualitativeRanges[0].rangeStroke;for(i=0;i<=this._rangeCollection.length-1;i++)if(t>=this._rangeCollection[i]&&t<this._rangeCollection[i+1])return r=n.inArray(this._rangeCollection[i],this._rangeCollection),this.model.qualitativeRanges[this._rangeCollection.length-1-r].rangeStroke},_bindingRangeStrokes:function(n){if(this._rangeCollection.length==1){if(n>=this._scale.location.x&&n<=this._rangeCollection[0]+this._scale.location.x)return this.model.qualitativeRanges[0].rangeStroke}else return this.model.orientation==t.datavisualization.BulletGraph.Orientation.Horizontal&&this._flowDirection==t.datavisualization.BulletGraph.FlowDirection.Forward||this.model.orientation==t.datavisualization.BulletGraph.Orientation.Vertical&&this._flowDirection==t.datavisualization.BulletGraph.FlowDirection.Backward?this._forwardStrokeBinding(n):this._backwardStrokeBinding(n)},_drawMinorTicks:function(i,r){var h,c,s,p,u,tt=0,e=this._flowDirection==t.datavisualization.BulletGraph.FlowDirection.Forward,f=this._orientation==t.datavisualization.BulletGraph.Orientation.Horizontal,it=this._tickPlacement=="inside",o=this._scale.location.x,rt=this.model.qualitativeRangeSize,st=this._scale.labelSettings.offset,ht=this._scale.majorTickSettings.size,l=this._scale.minorTickSettings.size,ut=this._scale.minorTickSettings.width,b=this.model.quantitativeScaleLength,g,k,nt,w,d,v,y;h=e?f?o:o+b:f?o+b-ut/2:o;switch(this._tickPosition){case"far":s=this._scale.location.y+rt;p=s+(it?-l:l);break;case"near":s=this._scale.location.y+(it?l:-l);p=this._scale.location.y;break;default:s=this._scale.location.y+rt/2-l/2;p=s+l}var ft=this._scale.maximum,et=this._scale.minimum,ot=this._scale.interval,a=this._scale.minorTicksPerInterval;for(g=et;g<=ft;g+=ot){for(c=i/a,k=1;k<=a;k++)tt++,u=!f&&e||f&&!e?h-c+c/(a+1):h+c-c/(a+1),!e&&f&&tt>=(ft-et)*a&&(u+=ut/2),this.model.applyRangeStrokeToTicks&&(nt=this._bindingRangeStrokes(u)),w=this._minorTickLines(this._scale,u,s,p,nt),this.model.drawTicks&&(d={majorTickSettings:this._scale.majorTickSettings,minorTickSettings:this._scale.minorTickSettings,minorTicksPerInterval:this._scale.minorTicksPerInterval,maximum:this._scale.maximum,minimum:this._scale.minimum,interval:this._scale.interval},this._trigger("drawTicks",d),this._scale=n.extend(this._scale,d),w=this._minorTickLines(d,u,s,p,nt)),!e&&f?(v=b+o+1,u<=v&&u>=o+1&&(y=this.svgRenderer.createLine(w),r.appendChild(y))):e&&!f?(v=o+1,u>=v&&(y=this.svgRenderer.createLine(w),r.appendChild(y))):(v=b+o+1,u<=v&&(y=this.svgRenderer.createLine(w),r.appendChild(y))),c=i/a*(k+1);h=!e&&f||e&&!f?h-i:h+i}},_minorTickLines:function(n,t,i,r,u){return{x1:t,y1:i,x2:t,y2:r,"stroke-width":n.minorTickSettings.width,stroke:this.model.applyRangeStrokeToTicks&&u?u:n.minorTickSettings.stroke}},_drawLabels:function(n,i){var o,a,it=this.model.locale,et=it&&this.model.enableGroupSeparator,g,b,rt=this._flowDirection==t.datavisualization.BulletGraph.FlowDirection.Forward,c=this._orientation==t.datavisualization.BulletGraph.Orientation.Horizontal,k=this._scale.location.x,f=this._scale.labelSettings.offset,ut=this.model.quantitativeScaleLength,ct=this._scale.majorTickSettings.width,v,l,e,w,nt,h,tt;o=rt?c?k:k+ut:c?k+ut:k;var ft=this._tickPosition+this._labelPlacement,s=this._labelPosition==t.datavisualization.BulletGraph.LabelPosition.Below,lt=this._scale.location.y,d=this.model.qualitativeRangeSize,u=this._scale.labelSettings.size,at=this._scale.majorTickSettings.size;switch(ft){case"faroutside":case"centeroutside":a=s?this._scale.location.y+d+f+u:this._scale.location.y-f;break;case"farinside":case"centerinside":a=s?this._scale.location.y+d-f:this._scale.location.y+f+(c?u:0);break;case"nearoutside":a=s?this._scale.location.y+d+f+u:this._scale.location.y-f;break;case"nearinside":a=s?this._scale.location.y+d-f:this._scale.location.y+f+u}var ot=this._scale.minimum,st=this._scale.maximum,ht=this._scale.interval,vt=this._labelPlacement=="inside";for(l=ot;l<=st;l+=ht){var r=a,y=o,p=this._scale.labelSettings.font;if(p.size=u,e={width:0,height:0},c)b="rotate(0,"+y+","+r+")";else{w=this._scale.labelSettings.labelPrefix+l+this._scale.labelSettings.labelSuffix;switch(ft){case"faroutside":case"centeroutside":s?r-=u:(e=t.EjSvgRender.utils._measureText(w,null,p),r-=e.width);break;case"farinside":case"centerinside":s&&(e=t.EjSvgRender.utils._measureText(w,null,p),r-=e.width);break;case"nearoutside":s?r-=u:(e=t.EjSvgRender.utils._measureText(w,null,p),r-=e.width);break;case"nearinside":s?(e=t.EjSvgRender.utils._measureText(w,null,p),r-=e.width):r-=u}y-=u/3;b="rotate(90,"+y+","+r+")"}this.model.applyRangeStrokeToLabels&&(nt=this._bindingRangeStrokes(o));h=this._labelOptions(this.model.quantitativeScaleSettings.labelSettings,y,r,b,nt);this.model.drawLabels&&(tt={font:this._scale.labelSettings.font,labelPrefix:this._scale.labelSettings.labelPrefix,labelSuffix:this._scale.labelSettings.labelSuffix,offset:f,size:u,stroke:this._scale.labelSettings.stroke},this._trigger("drawLabels",tt),h=this._labelOptions(tt,y,r,b,nt));o=rt?c?o+n:o-n:c?o-n:o+n;g=et?l.toLocaleString(it):l;v=g;t.util.isNullOrUndefined(h.labelPrefix)||(v=h.labelPrefix+g);t.util.isNullOrUndefined(h.labelSuffix)||(v=v+h.labelSuffix);this.svgRenderer.drawText(h,v,i)}},_labelOptions:function(n,i,r,u,f){var e={x:i,y:r,"text-anchor":this._orientation==t.datavisualization.BulletGraph.Orientation.Horizontal?"middle":"start",fill:this.model.applyRangeStrokeToLabels&&f?f:n.stroke,"font-size":n.size+"px","font-family":n.font.fontFamily,"font-style":n.font.fontStyle,"font-weight":n.font.fontWeight,opacity:n.font.opacity,transform:u};return n.labelPrefix!=""&&(e.labelPrefix=n.labelPrefix),n.labelSuffix!=""&&(e.labelSuffix=n.labelSuffix),e},_drawQualitativeRanges:function(n){var c=this._scale.location.x,y=this._scale.location.y,e,a=this._flowDirection==t.datavisualization.BulletGraph.FlowDirection.Forward,o=this._orientation==t.datavisualization.BulletGraph.Orientation.Horizontal,p=this._scale.labelSettings.offset,s,h,f,v;this._rangeCollection=[];var w=this._tickPosition+this._labelPlacement,b=this._labelPosition==t.datavisualization.BulletGraph.LabelPosition.Below,k=this.model.qualitativeRangeSize,l=this._scale.location.x,r=this._scale.minimum,i=this._scale.maximum,u=this.model.quantitativeScaleLength;for(s=this.model.qualitativeRanges.length-1;s>=0;s--)h={object:this,rangeIndex:s,rangeOptions:this.model.qualitativeRanges[s],rangeEndValue:this.model.qualitativeRanges[s].rangeEnd},this.model.drawQualitativeRanges&&this._trigger("drawQualitativeRanges",h),f=h.rangeEndValue,f=f>i?i:f,a?(c=o?l:l+(u-u/((i-r)/(i-r-(i-f)))),e=o?u/((i-r)/(i-r-(i-f))):u/((i-r)/(i-r-(i-f)))):(c=o?l+(u-u/((i-r)/(i-r-(i-f)))):l,e=o?u/((i-r)/(i-r-(i-f))):u/((i-r)/(i-r-(i-f)))),v={x:c,y:y,height:this.model.qualitativeRangeSize,width:e>0?e<u?e:u:0,fill:h.rangeOptions.rangeStroke,opacity:h.rangeOptions.rangeOpacity},o&&a||!o&&!a?this._rangeCollection.push(e):this._rangeCollection.push(c),this.svgRenderer.drawRect(v,n);this._rangeCollection.sort(this._sortRangeCollection)},_sortRangeCollection:function(n,t){return n-t},_calculateFeatureMeasureBounds:function(n,i){var o=this._scale.minimum,a,v;if(n=n<o&&o<0?o:n,n>=o){var u,h,r,f=this._scale.location.x,e=this.model.quantitativeScaleLength,s=this._scale.maximum-this._scale.minimum,c=this._scale.maximum-n,l=this._flowDirection.toLowerCase()+this._orientation.toLowerCase();i=i==null?"":i;a=this._scale.labelSettings.font;a.size=this._scale.labelSettings.size;v=t.EjSvgRender.utils._measureText(i.toString(),null,a).width;switch(l){case"forwardhorizontal":case"backwardvertical":u=f+(o>0?0:e/s*Math.abs(o));r=e/(s/(o>0?s-c:n));n<0&&(r=Math.abs(r),u-=r);r=u+r<f+e?r:f+e-u;h=f-(l=="forwardhorizontal"?v/2+this._scale.labelSettings.offset:this._scale.labelSettings.offset);break;default:u=f+(e-e/(s/(s-c)));r=o>0?e/(s/(s-c)):e/(s/n);n<0&&(r=Math.abs(r),u-=r);u<f&&(r=u+r-f,u=f);h=f+e+(l=="backwardhorizontal"?v/2+this._scale.labelSettings.offset:this._scale.labelSettings.offset)}return{pointX:u,Width:r,lPointX:h}}return!1},_drawFeatureMeasureBar:function(){var e=1,o,r,s,h,f,a=this.model.locale,y=a&&this.model.enableGroupSeparator,v=!t.isNullOrUndefined(this.model.fields)&&this.model.fields.dataSource!=null,p=this._orientation==t.datavisualization.BulletGraph.Orientation.Vertical,w=typeof this.model.value=="function"?this._value():this.model.value,n,i,c,l,u;if(this._dataCount>0)for(n=this._dataCount-1;n>=0;n--)o=v?this.model.fields.dataSource[n][this.model.fields.featureMeasures]:t.isNullOrUndefined(this._scale.featureMeasures[n].value)?w:this._scale.featureMeasures[n].value,r=v?this.model.fields.dataSource[n][this.model.fields.category]:this._scale.featureMeasures[n].category,h=this._scale.location.y+this.model.qualitativeRangeSize/this._dataCount*e/2-this._scale.featuredMeasureSettings.width/2,f=this._scale.location.y+this.model.qualitativeRangeSize/this._dataCount*e/2+this._scale.featuredMeasureSettings.width/2-1,i=this._calculateFeatureMeasureBounds(o,r),i&&(c=this._featureBar(this._scale,i.pointX,h,i.Width,n),this.model.drawFeatureMeasureBar&&(u={featuredMeasureSettings:this._scale.featuredMeasureSettings},this._trigger("drawFeatureMeasureBar",u),c=this._featureBar(u,i.pointX,h,i.Width,n)),this.svgRenderer.drawRect(c,this._scaleGroup),s=p?"rotate(90,"+i.lPointX+","+(f-4)+")":"rotate(0,"+i.lPointX+","+f+")",l=this._drawcategory(this.model.quantitativeScaleSettings.labelSettings,i.lPointX,f,s),this.model.drawCategory&&(u={size:this.model.quantitativeScaleSettings.labelSettings.size,stroke:this.model.quantitativeScaleSettings.labelSettings.stroke,font:this.model.quantitativeScaleSettings.labelSettings.font,categoryValue:r},this._trigger("drawCategory",u),l=this._drawcategory(u,i.lPointX,f,s),r=u.categoryValue),t.isNullOrUndefined(r)||this.svgRenderer.drawText(l,y?r.toLocaleString(a):r,this._scaleGroup),e+=2,this.value(o))},_drawcategory:function(n,t,i,r){return{x:t,y:i+this._scale.featuredMeasureSettings.width/2,"text-anchor":"middle",fill:n.stroke,"font-size":n.size+"px","font-family":n.font.fontFamily,"font-style":n.font.fontStyle,"font-weight":n.font.fontWeight,opacity:n.font.opacity,transform:r}},_featureBar:function(n,t,i,r,u){return{"class":this.svgObject.id+"_FeatureMeasure",id:this.svgObject.id+"_FeatureMeasure_"+u,x:t,y:i,height:n.featuredMeasureSettings.width,width:r,fill:n.featuredMeasureSettings.stroke}},_drawComparativeMeasureSymbol:function(){var r=1,n,u,f,e,c=this._flowDirection==t.datavisualization.BulletGraph.FlowDirection.Forward,h=this._orientation==t.datavisualization.BulletGraph.Orientation.Horizontal,l=typeof this.model.comparativeMeasureValue=="function"?this._comparativeMeasureValue():this.model.comparativeMeasureValue,i,o,s;if(this._dataCount>0)for(i=this._dataCount-1;i>=0;i--)n=!t.isNullOrUndefined(this.model.fields)&&this.model.fields.dataSource!=null?this.model.fields.dataSource[i][this.model.fields.comparativeMeasure]:t.isNullOrUndefined(this._scale.featureMeasures[i].comparativeMeasureValue)?l:this._scale.featureMeasures[i].comparativeMeasureValue,n>=this._scale.minimum&&n<=this._scale.maximum&&(u=this._scale.location.y+this.model.qualitativeRangeSize/this._dataCount*r/2-this._scale.featuredMeasureSettings.width/2-this._scale.featuredMeasureSettings.width,f=this._scale.location.y+this.model.qualitativeRangeSize/this._dataCount*r/2-this._scale.featuredMeasureSettings.width/2+2*this._scale.featuredMeasureSettings.width,e=c?h?this._scale.location.x+this.model.quantitativeScaleLength/((this._scale.maximum-this._scale.minimum)/(this._scale.maximum-this._scale.minimum-(this._scale.maximum-n))):this._scale.location.x+(this.model.quantitativeScaleLength-this.model.quantitativeScaleLength/((this._scale.maximum-this._scale.minimum)/(this._scale.maximum-this._scale.minimum-(this._scale.maximum-n)))):h?this._scale.location.x+(this.model.quantitativeScaleLength-this.model.quantitativeScaleLength/((this._scale.maximum-this._scale.minimum)/(this._scale.maximum-this._scale.minimum-(this._scale.maximum-n)))):this._scale.location.x+this.model.quantitativeScaleLength/((this._scale.maximum-this._scale.minimum)/(this._scale.maximum-this._scale.minimum-(this._scale.maximum-n))),o=this._compareMeasure(this._scale,e,u,f,i,n),this.model.drawComparativeMeasureSymbol&&(s={comparativeMeasureSettings:this._scale.comparativeMeasureSettings},this._trigger("drawComparativeMeasureSymbol",s),o=this._compareMeasure(s,e,u,f,i,n)),this.svgRenderer.drawLine(o,this._scaleGroup),r+=2,this.comparativeMeasureValue(n))},_compareMeasure:function(n,t,i,r,u,f){return{"class":this.svgObject.id+"_ComparativeMeasure",id:this.svgObject.id+"_ComparativeMeasure_"+u,x1:f==this._scale.maximum?t-n.comparativeMeasureSettings.width/2:f==this._scale.minimum?t+n.comparativeMeasureSettings.width/2:t,y1:i,x2:f==this._scale.maximum?t-n.comparativeMeasureSettings.width/2:f==this._scale.minimum?t+n.comparativeMeasureSettings.width/2:t,y2:r,"stroke-width":n.comparativeMeasureSettings.width,stroke:n.comparativeMeasureSettings.stroke}},_setTheme:function(n,i){var u=[],e,r,f;this._scale=this.model.quantitativeScaleSettings;for(e in n)u.push(e);for(r=0;r<u.length;r++)for(this._scale.majorTickSettings.stroke=!this._scale.majorTickSettings.stroke||this._scale.majorTickSettings.stroke==n[u[r]].quantitativeScaleSettings.majorTickSettings.stroke?n[i].quantitativeScaleSettings.majorTickSettings.stroke:this._scale.majorTickSettings.stroke,this._scale.minorTickSettings.stroke=!this._scale.minorTickSettings.stroke||this._scale.minorTickSettings.stroke==n[u[r]].quantitativeScaleSettings.minorTickSettings.stroke?n[i].quantitativeScaleSettings.minorTickSettings.stroke:this._scale.minorTickSettings.stroke,this._scale.labelSettings.stroke=!this._scale.labelSettings.stroke||this._scale.labelSettings.stroke==n[u[r]].quantitativeScaleSettings.labelSettings.stroke?n[i].quantitativeScaleSettings.labelSettings.stroke:this._scale.labelSettings.stroke,this._scale.featuredMeasureSettings.stroke=!this._scale.featuredMeasureSettings.stroke||this._scale.featuredMeasureSettings.stroke==n[u[r]].quantitativeScaleSettings.featuredMeasureSettings.stroke?n[i].quantitativeScaleSettings.featuredMeasureSettings.stroke:this._scale.featuredMeasureSettings.stroke,this._scale.comparativeMeasureSettings.stroke=!this._scale.comparativeMeasureSettings.stroke||this._scale.comparativeMeasureSettings.stroke==n[u[r]].quantitativeScaleSettings.comparativeMeasureSettings.stroke?n[i].quantitativeScaleSettings.comparativeMeasureSettings.stroke:this._scale.comparativeMeasureSettings.stroke,this.model.captionSettings.font.color=!this.model.captionSettings.font.color||this.model.captionSettings.font.color==n[u[r]].captionSettings.font.color?n[i].captionSettings.font.color:this.model.captionSettings.font.color,this.model.captionSettings.subTitle.font.color=!this.model.captionSettings.subTitle.font.color||this.model.captionSettings.subTitle.font.color==n[u[r]].captionSettings.subTitle.font.color?n[i].captionSettings.subTitle.font.color:this.model.captionSettings.subTitle.font.color,this.model.captionSettings.indicator.font.color=!this.model.captionSettings.indicator.font.color||this.model.captionSettings.indicator.font.color==n[u[r]].captionSettings.indicator.font.color?n[i].captionSettings.indicator.font.color:this.model.captionSettings.indicator.font.color,this.model.captionSettings.indicator.symbol.color=!this.model.captionSettings.indicator.symbol.color||this.model.captionSettings.indicator.symbol.color==n[u[r]].captionSettings.indicator.symbol.color?n[i].captionSettings.indicator.symbol.color:this.model.captionSettings.indicator.symbol.color,f=0;f<this.model.qualitativeRanges.length;f++)this.model.qualitativeRanges[f].rangeStroke=this.model.qualitativeRanges[f].rangeStroke?this.model.qualitativeRanges[f].rangeStroke:t.isNullOrUndefined(n[i].qualitativeRanges[f])?n[i].qualitativeRanges[0].rangeStroke:n[i].qualitativeRanges[f].rangeStroke},_onDrawQualitativeRanges:function(n,t,i){var r={object:this,scaleElement:this.model.quantitativeScaleSettings,rangeIndex:i,rangeElement:n,rangeEndValue:t};this._trigger("drawQualitativeRanges",r)},_doAnimation:function(){for(var r,i=n("."+this.svgObject.id+"_FeatureMeasure"),t=i.length-1;t>=0;t--)r=i[t],this._animateFeatureBar(r)},_doLineAnimation:function(){for(var r,i=n("."+this.svgObject.id+"_ComparativeMeasure"),u=2e3/i.length,t=i.length-1;t>=0;t--)r=i[t],n(r).attr("transform","scale(0)"),this._doLineSymbol(r,u,t)},_animateFeatureBar:function(i){var r=i.getBBox(),u,f,e;this._orientation==t.datavisualization.BulletGraph.Orientation.Horizontal&&this._flowDirection==t.datavisualization.BulletGraph.FlowDirection.Backward||this._orientation==t.datavisualization.BulletGraph.Orientation.Vertical&&this._flowDirection==t.datavisualization.BulletGraph.FlowDirection.Forward?(u=r.x+r.width,f=r.y+r.height):(u=r.x,f=r.y);n(i).animate({scale:1},{duration:1e3,step:function(t){e=t;n(i).attr("transform","translate("+u+" "+f+") scale("+t+",1) translate("+-u+" "+-f+")")}})},_doLineSymbol:function(t,i,r){var o=parseInt(r*i),u=t.getBBox(),f=u.x+u.width/2,e=u.y+u.height/2;n(t).delay(o).animate({scale:1},{duration:200,step:function(i){n(t).attr("transform","translate("+f+" "+e+") scale("+i+") translate("+-f+" "+-e+")")}})},bindEvents:function(){var u=t.EjSvgRender.utils.browserInfo(),i=u.isMSPointerEnabled,r=u.pointerEnabled,f=i?r?"pointerdown":"MSPointerDown":"touchstart mousedown",e=i?r?"pointerup":"MSPointerUp":"touchend mouseup",o=i?r?"pointermove":"MSPointerMove":"touchmove mousemove",s=i?r?"pointerleave":"MSPointerOut":"touchleave mouseleave";this.model.browserInfo=u;this._on(n(this.svgObject),o,this._bulletMouseMove);this._on(n(this.svgObject),s,this._bulletMouseLeave);this._on(n(this.svgObject),f,this._bulletMouseDown);this._on(n(this.svgObject),e,this._bulletMouseUp);this._on(n(this.svgObject),"contextmenu",this._bulletRightClick);this._on(n(this.svgObject),"click",this._bulletClick);(this.model.enableResizing||this.model.isResponsive)&&(t.isTouchDevice()?this._on(n(window),"orientationchange",this._bulletResize):this._on(n(window),"resize",this._bulletResize))},_unwireEvents:function(){this._off(n(this.svgObject),"contextmenu",this._bulletRightClick);this._off(n(this.svgObject),"click",this._bulletClick)},isDevice:function(){return/mobile|tablet|android|kindle/i.test(navigator.userAgent.toLowerCase())},_bulletResize:function(){var i=this,f=this.model.height,r=this.model.width,e=n(i.svgObject),f=90,r=t.isTouchDevice()?250:595,u=n(i.element).width(),o;i.model.width?r=parseInt(i.model.width):u>0&&u<595?r=u:u>595&&(r=595);i.model.height&&(f=parseInt(i.model.height));o=n(i.element).height()>f?f:n(i.element).height();this.resizeTO&&clearTimeout(this.resizeTO);this.resizeTO=setTimeout(function(){r<n(i.element).width()?e.width(r):e.width(n(i.element).width());e.height(o);i.redraw()},500)},_bulletMouseLeave:function(t){this.isTouch(t)||(n("#tooltip").remove(),n(".tooltipDiv"+this._id).remove())},isTouch:function(n){var t=n.originalEvent?n.originalEvent:n;return t.pointerType=="touch"||t.pointerType==2||t.type.indexOf("touch")>-1?!0:!1},_bulletMouseMove:function(t){var i,r;i=n(t.target).attr("id");r=n(t.target).attr("class");this.isTouch(t)||(n("#tooltip").remove(),n(".tooltipDiv"+this._id).remove(),this._elementTooltip(t,r,i),this._displayTooltip(t,r,i))},_bulletMouseDown:function(i){if(this.isTouch(i)){n("#tooltip").remove();n(".tooltipDiv"+this._id).remove();var r=n(i.target).attr("id"),u=n(i.target).attr("class");this._elementTooltip(i,u,r);this._displayTooltip(i,u,r)}t.isTouchDevice()&&this.model.rightClick!=""&&(this._longPressTimer=new Date)},_bulletMouseUp:function(i){if(this.isTouch(i)){var r=this,u;window.clearTimeout(r.model.timer);n(".tooltipDiv"+this._id).length==1&&(u=n(".tooltipDiv"+this._id));u&&(r.model.trackerElement=u,r.model.timer=setTimeout(function(){r.model.trackerElement&&n(".tooltipDiv"+r._id).fadeOut(500,function(){n(".tooltipDiv"+r._id).remove();n("[id*="+r.svgObject.id+"_FeatureMeasure_]").attr("opacity","1")})},1200))}t.isTouchDevice()&&this.model.rightClick!=""&&new Date-this.model._longPressTimer>1500&&this._bulletRightClick(i)},_bulletClick:function(n){var t=new Date;this.model.click!=""&&this._trigger("click",{data:{event:n}});this._doubleTapTimer!=null&&t-this._doubleTapTimer<300&&this._bulletDoubleClick(n);this._doubleTapTimer=t},_bulletDoubleClick:function(n){this.model.doubleClick!=""&&this._trigger("doubleClick",{data:{event:n}})},_bulletRightClick:function(n){this.model.rightClick!=""&&this._trigger("rightClick",{data:{event:n}})},_elementTooltip:function(t,r){var o=this.model.captionSettings,u=n("<div><\/div>").attr({id:"tooltip","class":"tooltipDiv"+this._id}),s=this.mousePosition(t),c="",h,f,e;if(n(u).css({left:s.pageX+10,top:s.pageY+10,display:"block",position:"absolute","z-index":"13000",cursor:"default","font-family":"Segoe UI",color:"#707070","font-size":"12px","background-color":"#FFFFFF",border:"1px solid #707070"}),r==this.svgObject.id+"_Caption"?c=t.target.textContent==o.text?"":o.text:r==this.svgObject.id+"_SubTitle"?c=t.target.textContent==o.subTitle.text?"":o.subTitle.text:r==this.svgObject.id+"_Indicator"&&(c=t.target.textContent==o.indicator.text?"":o.indicator.text),c!="")n(u).html("&nbsp"+c+"&nbsp"),n(document.body).append(u);else if(this.model.tooltipSettings.enableCaptionTooltip&&(r==this.svgObject.id+"_Caption"||r==this.svgObject.id+"_SubTitle"||r==this.svgObject.id+"_Indicator")){var a=n("."+r).text(),v={Text:a},l=this.model.tooltipSettings.captionTemplate;n(".tooltipDiv"+this._id).length==0&&(u=n("<div><\/div>").attr("class","tooltipDiv"+this._id).css({position:"absolute","z-index":"13000",display:"block"}),n(document.body).append(u));l!=""&&l!=null?(h=document.getElementById(l),h=h?n(h).clone():n(l),n(h).css("display","block").appendTo(u),n(u).html(n(h).render(v))):n(u).html(a);n(u).css("font-size","12px");f=s.clientX+n(document).scrollLeft()+10;e=s.clientY+n(document).scrollTop()+10;f=f+n(u).width()<n(window).width()?f:f-n(u).width();e=e+n(u).height()<n(window).height()?e:e-n(u).height();(f===i||f===null)&&(f=s.clientX+n(document).scrollLeft()+10);(e===i||e===null)&&(e=s.clientY+n(document).scrollTop()+10);n(u).css({left:f,top:e,"-webkit-border-radius":"5px 5px 5px 5px","-moz-border-radius":"5px 5px 5px 5px","-o-border-radius":"5px 5px 5px 5px","border-radius":"5px 5px 5px 5px","background-color":"White",border:"1px Solid Black","padding-bottom":"5px","padding-left":"5px","padding-right":"5px","padding-top":"5px"})}},mousePosition:function(n){return!t.util.isNullOrUndefined(n.pageX)&&n.pageX>0?n:n.originalEvent&&!t.util.isNullOrUndefined(n.originalEvent.pageX)&&n.originalEvent.pageX>0?n.originalEvent:n.originalEvent&&n.originalEvent.changedTouches!=i&&!t.util.isNullOrUndefined(n.originalEvent.changedTouches[0].pageX)&&n.originalEvent.changedTouches[0].pageX>0?n.originalEvent.changedTouches[0]:n},_displayTooltip:function(r,u,f){var w,b,s,h;if(u!="undefined"&&(u==this.svgObject.id+"_FeatureMeasure"||u==this.svgObject.id+"_ComparativeMeasure")){var a=this.model.locale,k=a&&this.model.enableGroupSeparator,c,e,y,p,l,o,d=typeof this.model.value=="function"?this._value():this.model.value,g=typeof this.model.comparativeMeasureValue=="function"?this._comparativeMeasureValue():this.model.comparativeMeasureValue,v=this.mousePosition(r);e=f.substring(f.lastIndexOf("_")+1);y=!t.isNullOrUndefined(this.model.fields)&&this.model.fields.dataSource!=null?this.model.fields.dataSource[e][this.model.fields.featureMeasures]:t.isNullOrUndefined(this._scale.featureMeasures[e].value)?d:this._scale.featureMeasures[e].value;p=!t.isNullOrUndefined(this.model.fields)&&this.model.fields.dataSource!=null?this.model.fields.dataSource[e][this.model.fields.comparativeMeasure]:t.isNullOrUndefined(this._scale.featureMeasures[e].comparativeMeasureValue)?g:this._scale.featureMeasures[e].comparativeMeasureValue;l=!t.isNullOrUndefined(this.model.fields)&&this.model.fields.dataSource!=null?t.isNullOrUndefined(this.model.fields.dataSource[e][this.model.fields.category])?null:this.model.fields.dataSource[e][this.model.fields.category]:t.isNullOrUndefined(this._scale.featureMeasures[e].category)?null:this._scale.featureMeasures[e].category;c=k?{currentValue:y.toLocaleString(a),targetValue:p.toLocaleString(a),category:t.util.isNullOrUndefined(l)?l:l.toLocaleString(a)}:{currentValue:y,targetValue:p,category:l};n(".tooltipDiv"+this._id).length==0&&(o=n("<div><\/div>").attr("class","tooltipDiv"+this._id).css({position:"absolute","z-index":"13000",display:"block"}),n(document.body).append(o));this.model.tooltipSettings.template!=""&&this.model.tooltipSettings.template!=null?(w=n("#"+this.model.tooltipSettings.template).clone(),n(w).css("display","block").appendTo(o),n(o).html(n(w).render(c))):(b=t.isNullOrUndefined(c.category)?"":"<br/> Category : "+c.category,n(o).html("Current : "+c.currentValue+"<br/> Target : "+c.targetValue+b));n(o).css("font-size","12px");s=v.clientX+n(document).scrollLeft()+10;h=v.clientY+n(document).scrollTop()+10;s=s+n(o).width()<n(window).width()?s:s-n(o).width();h=h+n(o).height()<n(window).height()?h:h-n(o).height();(s===i||s===null)&&(s=v.clientX+n(document).scrollLeft()+10);(h===i||h===null)&&(h=v.clientY+n(document).scrollTop()+10);n(o).css({left:s,top:h,"-webkit-border-radius":"5px 5px 5px 5px","-moz-border-radius":"5px 5px 5px 5px","-o-border-radius":"5px 5px 5px 5px","border-radius":"5px 5px 5px 5px","background-color":"White",color:"black",border:"1px Solid Black","padding-bottom":"5px","padding-left":"5px","padding-right":"5px","padding-top":"5px"});u==this.svgObject.id+"_FeatureMeasure"&&n("#"+f).attr("opacity","0.7")}},redraw:function(){n(this.svgObject).empty();this._renderBulletElements()},destroy:function(){this._unwireEvents();n(this.element).removeClass("e-bulletgraph e-js").find("#"+this.svgObject.id).remove()},setFeatureMeasureBarValue:function(n,i){this._scale=this.model.quantitativeScaleSettings;var r=!t.isNullOrUndefined(this.model.fields)&&this.model.fields.dataSource!=null?this.model.fields.dataSource.length:this._scale.featureMeasures.length;n<r&&i!=null&&(t.isNullOrUndefined(this.model.fields)||this.model.fields.dataSource==null?this._scale.featureMeasures[n].value=i:this.model.fields.dataSource[n][this.model.fields.featureMeasures]=i,this.redraw(),this.model.enableAnimation&&this._doAnimation())},setComparativeMeasureSymbol:function(n,i){this._scale=this.model.quantitativeScaleSettings;var r=!t.isNullOrUndefined(this.model.fields)&&this.model.fields.dataSource!=null?this.model.fields.dataSource.length:this._scale.featureMeasures.length;n<r&&i!=null&&(t.isNullOrUndefined(this.model.fields)||this.model.fields.dataSource==null?this._scale.featureMeasures[n].comparativeMeasureValue=i:this.model.fields.dataSource[n][this.model.fields.comparativeMeasure]=i,this.redraw(),this.model.enableAnimation&&this._doLineAnimation())}});t.datavisualization.BulletGraph.Orientation={Horizontal:"horizontal",Vertical:"vertical"};t.datavisualization.BulletGraph.TickPlacement={Inside:"inside",Outside:"outside"};t.datavisualization.BulletGraph.LabelPlacement={Inside:"inside",Outside:"outside"};t.datavisualization.BulletGraph.Shape={Circle:"circle",Cross:"cross",Diamond:"diamond",DownArrow:"downarrow",Ellipse:"ellipse",HorizontalLine:"horizontalLine",Image:"image",InvertedTriangle:"invertedtriangle",LeftArrow:"leftarrow",Pentagon:"pentagon",Rectangle:"Rectangle",RightArrow:"rightarrow",Star:"star",Trapezoid:"trapezoid",Triangle:"triangle",UpArrow:"uparrow",VerticalLine:"verticalline",Wedge:"wedge"};t.datavisualization.BulletGraph.TickPosition={Far:"far",Near:"near",Center:"center"};t.datavisualization.BulletGraph.LabelPosition={Below:"below",Above:"above"};t.datavisualization.BulletGraph.FlowDirection={Forward:"forward",Backward:"backward"};t.datavisualization.BulletGraph.FontStyle={Normal:"Normal",Italic:"Italic",Oblique:"Oblique"};t.datavisualization.BulletGraph.FontWeight={Normal:"Normal",Bold:"Bold",Bolder:"Bolder",Lighter:"Lighter"};t.datavisualization.BulletGraph.Themes={flatlight:{quantitativeScaleSettings:{majorTickSettings:{stroke:"#191919"},minorTickSettings:{stroke:"#191919"},labelSettings:{stroke:"#191919"},featuredMeasureSettings:{stroke:"#191919"},comparativeMeasureSettings:{stroke:"#191919"}},qualitativeRanges:[{rangeStroke:"#ebebeb"},{rangeStroke:"#d8d8d8"},{rangeStroke:"#7f7f7f"}],captionSettings:{font:{color:"#191919"},subTitle:{font:{color:"#191919"}},indicator:{font:{color:"#191919"},symbol:{color:"#191919"}}}},material:{quantitativeScaleSettings:{majorTickSettings:{stroke:"#333333"},minorTickSettings:{stroke:"#191919"},labelSettings:{stroke:"#333333"},featuredMeasureSettings:{stroke:"#333333"},comparativeMeasureSettings:{stroke:"#333333"}},qualitativeRanges:[{rangeStroke:"#ebebeb"},{rangeStroke:"#d8d8d8"},{rangeStroke:"#7f7f7f"}],captionSettings:{font:{color:"#333333"},subTitle:{font:{color:"#333333"}},indicator:{font:{color:"#333333"},symbol:{color:"#333333"}}}},flatdark:{quantitativeScaleSettings:{majorTickSettings:{stroke:"#ffffff"},minorTickSettings:{stroke:"#ffffff"},labelSettings:{stroke:"#ffffff"},featuredMeasureSettings:{stroke:"#ffffff"},comparativeMeasureSettings:{stroke:"#ffffff"}},qualitativeRanges:[{rangeStroke:"#b3b3b3"},{rangeStroke:"#999999"},{rangeStroke:"#4d4d4d"}],captionSettings:{font:{color:"#ffffff"},subTitle:{font:{color:"#ffffff"}},indicator:{font:{color:"#ffffff"},symbol:{color:"#ffffff"}}}}}})(jQuery,Syncfusion);ej.EjSvgRender=function(n){var t,i;if(this.svgLink="http://www.w3.org/2000/svg",this.svgObj=document.createElementNS(this.svgLink,"svg"),this._rootId=jQuery(n).attr("id"),t=this._rootId+"_svg",$(document).find("#"+t).length>0){i=0;do i++;while($(document).find("#"+this._rootId+"_svg"+i).length>0);t=this._rootId+"_svg"+i}this.svgObj.setAttribute("id",t)},function(n){function t(n,t,i,r){var u=r*Math.PI/180,f=n+i*Math.cos(u),e=t+i*Math.sin(u);return[f,e]}ej.EjSvgRender.prototype={drawPath:function(t,i){if(n("#"+t.id).length>0)n("#"+t.id).attr(t);else{var r=document.createElementNS(this.svgLink,"path");n(r).attr(t).appendTo(i)}},createLegendSvg:function(n){return this.svgLink="http://www.w3.org/2000/svg",this.legendsvgObj=document.createElementNS(this.svgLink,"svg"),this._rootId=jQuery(n).attr("id"),this.legendsvgObj.setAttribute("id","legend_"+this._rootId+"_svg"),this.legendsvgObj},drawPolyline:function(t,i){if(n("#"+t.id).length>0)n("#"+t.id).attr(t);else{var r=document.createElementNS(this.svgLink,"polyline");n(r).attr(t).appendTo(i)}},drawLine:function(t,i){if(n("#"+t.id).length>0)n("#"+t.id).attr(t);else{var r=document.createElementNS(this.svgLink,"line");n(r).attr(t);n(r).appendTo(i)}},drawPolygon:function(t,i){if(n("#"+t.id).length>0)n("#"+t.id).attr(t);else{var r=document.createElementNS(this.svgLink,"polygon");n(r).attr(t);n(r).appendTo(i)}},drawCircle:function(t,i){if(n("#"+t.id).length>0)n("#"+t.id).attr(t);else{var r=document.createElementNS(this.svgLink,"circle");n(r).attr(t).appendTo(i)}},drawEllipse:function(t,i){if(n("#"+t.id).length>0)n("#"+t.id).attr(t);else{var r=document.createElementNS(this.svgLink,"ellipse");n(r).attr(t).appendTo(i)}},drawRect:function(t,i){if(n("#"+t.id).length>0)n("#"+t.id).attr(t);else{var r=document.createElementNS(this.svgLink,"rect");n(r).attr(t).appendTo(i)}},drawCylinder:function(t,i,r){var d,ot,y;if(n("#"+t.id).length>0)n("#"+t.id).attr(t);else{var rt=t.x,s=t.y,g=t.id,h=t.fill,st=h,ht=this.checkColorFormat(h);ht||(h=ej.datavisualization.Chart.prototype.colorNameToHex(h));var a,v,p,w,l,e,b,nt,ut=2,tt,k,ft=0,et=0,c,it={svgRenderer:this};if(r.isColumn==!0){var o=t.width/2,u=t.height,f=o/4;l=tt=rt;e=f<s?s-f:r.stacking?s+f:s-f;k=e;a=2*o;v=0;p=0;w=f<s?u:u<2*f?u:r.stacking?u-2*f:u;b=l;nt=f<s?e+u:u<e?u+e:r.stacking?u+(s-f):u+e;ft=100;(r.stacking=!0)&&(r.isLastSeries||(e=k=s+f,w=u<o/2?u:u-o/2))}else{var f=t.height/2,u=t.width,o=f/4;e=k=s;l=tt=rt+o;a=0;v=2*f;p=u;w=0;b=l+u;nt=e;et=100;(r.stacking=!0)&&(r.isLastSeries||(b=l+u-o*2,p=u-o*2))}for(delete t.x,delete t.y,delete t.width,delete t.height,delete t.isColumn;ut--;)c="M"+l.toString()+","+e.toString(),c+="a"+o.toString()+","+f.toString()+" 0 1,0 "+a.toString()+","+v.toString(),c+="a"+o.toString()+","+f.toString()+" 0 1,0 "+(-1*a).toString()+","+(-1*v).toString(),t.d=c,t.id=g+"_Region_"+ut,t.fill=ej.Ej3DRender.prototype.polygon3D.prototype.applyZLight(h,it),this.drawPath(t,i),l=b,e=nt;c="M"+tt.toString()+","+k.toString();c+="a"+o.toString()+","+f.toString()+" 0 1,0 "+a.toString()+","+v.toString();c+="l"+p.toString()+" "+w.toString();c+="a"+o.toString()+","+f.toString()+" 0 1,1 "+(-1*a).toString()+","+(-1*v).toString()+" z";t.d=c;t.id=g+"_Region_2";st.indexOf("url")==-1&&(d=g,n("#"+d).length==0&&(ot={id:d,x1:"0%",y1:"0%",x2:ft.toString()+"%",y2:et.toString()+"%"},y=[],y[0]={colorStop:"0%",color:h},y[1]={colorStop:"30%",color:ej.Ej3DRender.prototype.polygon3D.prototype.applyXLight(h,it)},y[2]={colorStop:"70%",color:ej.Ej3DRender.prototype.polygon3D.prototype.applyXLight(h,it)},y[3]={colorStop:"100%",color:h},this.drawGradient(ot,y,i)),t.fill="url(#"+d+")");this.drawPath(t,i)}},createGradientElement:function(n,t,i,r,u,f,e){var o,s,h;return Object.prototype.toString.call(t)=="[object Array]"?(s={id:this.svgObj.id+"_"+n+"Gradient",x1:i+"%",y1:r+"%",x2:u+"%",y2:f+"%"},h="#"+this.svgObj.id+"_"+n+"Gradient",this.drawGradient(s,t,e),o="url(#"+this.svgObj.id+"_"+n+"Gradient)"):o=t,o},drawGradient:function(t,i,r){var o=this.createDefs(),f=document.createElementNS(this.svgLink,"linearGradient"),u,e;for(n(f).attr(t),u=0;u<i.length;u++)e=document.createElementNS(this.svgLink,"stop"),n(e).attr({offset:i[u].colorStop,"stop-color":i[u].color,"stop-opacity":1}),n(e).appendTo(f);n(f).appendTo(o);n(o).appendTo(r)},drawText:function(t,i,r,u){var e,o,h,f,s,c;if(n("#"+t.id).length>0)this._textAttrReplace(t,i,u);else{if(e=document.createElementNS(this.svgLink,"text"),o=n(e),jQuery.type(i)=="array")for(h=0,o.attr(t),f=0;f<i.length;f++)s=document.createElementNS(this.svgLink,"tspan"),s.textContent=i[f],n(s).attr({x:t.x,dy:h}),n(s).appendTo(e),u=this.enable3D?u.font:u,c=ej.EjSvgRender.utils._measureText(i[f],null,u),h=t.isTrackball?c.height+2:c.height;else e.textContent=i,o.attr(t);o.appendTo(r)}},_textAttrReplace:function(t,i,r){var f,e,u,o,s;if(n("#"+t.id).attr(t),jQuery.type(i)=="array")if(f=n("#"+t.id).children("tspan"),e=0,f.length>0&&f.length==i.length)for(u=0;u<f.length;u++)o=f[u],n(o).attr({x:t.x,dy:e}),o.textContent=i[u],s=ej.EjSvgRender.utils._measureText(i[u],null,r),e=s.height+2;else n("#"+t.id).remove(),this.drawText(t,i,this.gTransToolEle,r);else n("#"+t.id).text(i)},drawImage:function(t,i){var r=document.createElementNS(this.svgLink,"image");r.setAttributeNS(null,"height",t.height);r.setAttributeNS(null,"width",t.width);r.setAttributeNS("http://www.w3.org/1999/xlink","href",t.href);r.setAttributeNS(null,"x",t.x);r.setAttributeNS(null,"y",t.y);r.setAttributeNS(null,"id",t.id);r.setAttributeNS(null,"visibility",t.visibility);ej.util.isNullOrUndefined(t.clippath)&&ej.util.isNullOrUndefined(t.preserveAspectRatio)||(r.setAttributeNS(null,"clip-path",t.clippath),r.setAttributeNS(null,"preserveAspectRatio",t.preserveAspectRatio));n(r).appendTo(i)},createDefs:function(){return document.createElementNS(this.svgLink,"defs")},createClipPath:function(t){var i=document.createElementNS(this.svgLink,"clipPath");return n(i).attr(t),i},createForeignObject:function(t){var i=document.createElementNS(this.svgLink,"foreignObject");return n(i).attr(t),i},createGroup:function(t){var i=document.createElementNS(this.svgLink,"g");return n(i).attr(t),i},createPattern:function(n,t){var r=document.createElementNS(this.svgLink,t);for(var i in n)n.hasOwnProperty(i)&&r.setAttribute(i,n[i]);return r},createText:function(t,i){var r=document.createElementNS(this.svgLink,"text");return n(r).attr(t),ej.util.isNullOrUndefined(i)||(r.textContent=i),r},createPath:function(t){var i=document.createElementNS(this.svgLink,"path");return n(i).attr(t),i},createCircle:function(t){var i=document.createElementNS(this.svgLink,"circle");return n(i).attr(t),i},createLine:function(t){var i=document.createElementNS(this.svgLink,"line");return n(i).attr(t),i},_getAttrVal:function(t,i,r){var u=n(t).attr(i);return u!=null?u:r},hexFromRGB:function(t){var r=t.R,u=t.G,f=t.B,i;return t.A?"rgba("+r.toString()+","+u.toString()+","+f.toString()+","+t.A+")":(i=[r.toString(16),u.toString(16),f.toString(16)],n.each(i,function(n,t){t.length===1&&(i[n]="0"+t)}),"#"+i.join("").toUpperCase())},checkColorFormat:function(n){return/(rgba?\((?:\d{1,3}[,\)]){3}(?:\d+\.\d+\))?)|(^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$)/gmi.test(n)},hexToRGB:function(n){var i=/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/.test(n),t;return i==!0?(t=/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/.exec(n),t?{R:parseInt(t[1]),G:parseInt(t[2]),B:parseInt(t[3]),A:t[4]}:null):(t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(n),t?{R:parseInt(t[1],16),G:parseInt(t[2],16),B:parseInt(t[3],16)}:null)},createDelegate:function(n,t){return function(i){t.apply(n,[i,this])}},drawClipPath:function(t,i){if(n(i).find("#"+t.id).length>0)n(i).find("#"+t.id).attr(t);else{var r=this.createDefs(),u=this.createClipPath({id:t.id});t.id=t.id+"_Rect";this.drawRect(t,u);this.append(u,r);this.append(r,i)}},drawCircularClipPath:function(n,t){var i=this.createDefs(),r=this.createClipPath({id:n.id});this.drawCircle(n,r);this.append(r,i);this.append(i,t)},append:function(t,i){n(t).appendTo(i)},_setAttr:function(t,i){n(t).attr(i)}};ej.EjSvgRender.commonChartEventArgs={cancel:!1,data:null};ej.EjSvgRender.utils={_decimalPlaces:function(n){var t=(""+n).match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);return t?Math.max(0,(t[1]?t[1].length:0)-(t[2]?+t[2]:0)):0},_getLabelContent:function(n,t,i){switch(t._categoryValueType){case"number":var r=ej.util.isNullOrUndefined(t.labelFormat)?null:t.labelFormat.match("{value}");return ej.util.isNullOrUndefined(t.labelFormat)?t.labels[Math.floor(n)]:r!=null?t.labelFormat.replace("{value}",t.labels[Math.floor(n)]):ej.format(t.labels[Math.floor(n)],t.labelFormat,i);case"date":return ej.format(new Date(t.labels[Math.floor(n)]),ej.util.isNullOrUndefined(t.labelFormat)?"dd/MM/yyyy":t.labelFormat,i);case"string":return t.labels[Math.floor(n)];default:return""}},_getSeriesTemplateSize:function(t,i,r,u,f){var t,i,a=f.model.AreaType,o=f._id,v=n.inArray(r,f.model._visibleSeries),s,h,e,c,l;s=n("#template_group_"+o).length!=0?n("#template_group_"+o):n("<div><\/div>").attr("id","template_group_"+o);s.css("position","relative").css("z-index",1e3);h=n("#"+r.marker.dataLabel.template).clone();n(h).attr("id",r.marker.dataLabel.template+"_"+v+"_"+i+"_"+o);e=n(h);e.css("position","absolute");t.count=1;c={series:r,point:t};e.html(e.html().parseTemplate(c));l=a=="cartesianaxes"||!r.enableAnimation||r.type.toLowerCase()=="pyramid"||r.type.toLowerCase()=="funnel"?"block":"none";e.css("display",l).appendTo(n(s));n(s).appendTo("#"+o);t.size={height:e.height(),width:e.width()};u?((ej.util.isNullOrUndefined(r.LeftLabelMaxHeight)||r.LeftLabelMaxHeight<t.size.height)&&(r.LeftLabelMaxHeight=t.size.height),(ej.util.isNullOrUndefined(r.LeftLabelMaxWidth)||r.LeftLabelMaxWidth<t.size.width)&&(r.LeftLabelMaxWidth=t.size.width)):((ej.util.isNullOrUndefined(r.RightLabelMaxHeight)||r.RightLabelMaxHeight<t.size.height)&&(r.RightLabelMaxHeight=t.size.height),(ej.util.isNullOrUndefined(r.RightLabelMaxWidth)||r.RightLabelMaxWidth<t.size.width)&&(r.RightLabelMaxWidth=t.size.width))},getMinPointsDelta:function(t,i,r){var f=i,u=Number.MAX_VALUE;return n.each(f.model._visibleSeries,function(i,e){var o,h,s;e.visibility.toLowerCase()=="visible"&&t.name==e._xAxisName&&(o=ej.DataManager(e.points,ej.Query().sortBy("xValue")).executeLocal(),o.length!=1||f.currentSeries&&f.currentSeries._yAxisName.toLowerCase()!=e._yAxisName.toLowerCase()?n.each(o,function(n,t){if(n>0&&t.xValue){var i=t.xValue-o[n-1].xValue;i!=0&&(u=Math.min(u,i))}}):(h=ej.util.isNullOrUndefined(r)?e.xAxis.visibleRange.min:r,s=o[0].xValue-h,s!=0&&(u=Math.min(u,s))))}),u==Number.MAX_VALUE&&(u=1),u},_getSeriesMaxLabel:function(t){var i={width:0,height:0},s=n(this.svgObject).width(),e=[],o,h,r,f,u;if(t.labels.length>0){for(u=0;u<t.labels.length;u++)r=this._measureText(t.visibleLabels[u],s,t.marker.dataLabel.font),i.width<r.width&&(i.width=r.width),i.height<r.height&&(i.height=r.height);t.LabelMaxHeight=i.height;t.LabelMaxWidth=i.width}else for(e.push(t.rightsidePoints),e.push(t.leftsidePoints),f=0;f<e.length;f++){for(u=0;u<e[f].length;u++)o=e[f][u],h=o.text?o.text:o.y,r=this._measureText(h,s,t.marker.dataLabel.font),i.width<r.width&&(i.width=r.width),i.height<r.height&&(i.height=r.height);f==0?(t.RightLabelMaxHeight=i.height,t.RightLabelMaxWidth=i.width):(t.LeftLabelMaxHeight=i.height,t.LeftLabelMaxWidth=i.width)}},_getHighestLabel:function(t,i,r){var c,y=0,f=[],o,s,e=0,v,l,h,a,u;if(ej.isNullOrUndefined(r))for(l=t.labels.length==0?t.visibleLabels:t.labels,h=0;h<l.length;h++){for(a=t.labels.length==0?l[h].Text:l[h],e=0,a.indexOf("<br>")!=-1?f=a.split("<br>"):f.push(a),u=0;u<f.length;u++)o=f[u],s=this._measureText(o,n(i.svgObject).width(),t.font).width,e<s&&(e=s,c=o);y<e&&(y=e,v=c)}else if(r&&(typeof r=="string"&&r.indexOf("<br>")!=-1||typeof r=="object"))for(e=0,f=typeof r=="object"?r:r.split("<br>"),u=0;u<f.length;u++)o=typeof f[u]=="object"?f[u].Text:f[u],s=this._measureText(o,i,t.font).width,e<s&&(e=s,c=o),v=c;return v},_getMaxLabelWidth:function(t,i){var e,g,ot,yt,pi,nt,a,rt,st,kt,p,ei,tt,l,ct,u,d,it,oi,dt,gt,lt,c,ni,s,ti,at,tr,ir;this.chartObj=i;var r={width:0,height:0,maxHeight:0,maxWidth:0},o="",hi=1,rr=i.svgRenderer.vmlNamespace,y=t.visibleRange,h=t.labelIntersectAction?t.labelIntersectAction.toLowerCase():"",vt=t.labelPlacement,ur=t.opposedPosition,ii=t.orientation.toLowerCase(),ci=i.model.requireInvertedAxes?ii=="vertical":ii=="horizontal",li=t.roundingPlaces,f=t.labelRotation,ii=t.orientation,k=0,et=t.visibleLabels,fr=et.length,ai,vi,yi,ri,ui;if(t.visible){for(e=0;e<fr;e++)t.visibleLabels[e].y=0,typeof t.visibleLabels[e].Text=="string"&&t.visibleLabels[e].Text.indexOf("<br>")!=-1&&t.orientation=="vertical"?(ai=t.visibleLabels[e].Text.split("<br>"),vi=this._getHighestLabel(t,n(this.svgObject).width(),ai),g=this._measureText(vi,n(this.svgObject).width(),t.font,t.labelRotation)):g=this._measureText(t.visibleLabels[e].Text,n(this.svgObject).width(),t.font,t.labelRotation),r.width<g.width&&(r.width=g.width,o=t.visibleLabels[e].Text),r.height<g.height&&(r.height=g.height);if((t.enableTrim||h=="trim"&&t.orientation!="vertical")&&(ot=t.maximumLabelWidth,r.width=r.width>ot?ot:r.width),r.rows=1,r.maxWidth=r.width,r.maxHeight=r.height,!rr&&(f||h)&&(yt=0,h=="rotate45"?yt=45:h=="rotate90"&&(yt=90),f=f!=null||ii=="vertical"?f:yt,t.rotationValue=f,f)){if(o=ej.isNullOrUndefined(o)?"":o,pi=this._measureText(o,n(this.svgObject).width(),t.font,t.labelRotation),(t.enableTrim||h=="trim")&&pi.width>ot&&o!="")for(a=o,p=1;p<a.toString().length;p++)if(a=a.toString().substring(0,p)+"... ",nt=this._measureText(a,n(i.svgObject).width(),t.font),nt.width>=ot){a=a.toString().substring(0,p-1)+"... ";o=a;break}o=(h||t.enableTrim)&&f?typeof o=="string"&&o.indexOf("<br>")!=-1?this._getHighestLabel(t,i,null):o:this._getHighestLabel(t,i,null);tt=this.rotatedLabel(t,i,f,o);t.labelIntersectAction&&t.labelIntersectAction.toLowerCase()=="multiplerows"&&(k=r.height+tt.height);r.height=t.rowsCount&&t.rowsCount>1&&f!=90&&t.enableTrim?t.rowsCount*r.height:tt.height;r.width=tt.width}if(t.labelIntersectAction)if(t.rowsCount&&(h=="none"||h=="trim"||h=="hide"||f==0)&&(h!="wrap"||t.enableTrim)&&(h!="wrapbyword"||t.enableTrim)&&(h!="multiplerows"||t.enableTrim))f==0&&(r.rows=t.rowsCount,r.height=t.rowsCount*r.height);else if(h=="wrap"||h=="wrapbyword"){if(vt=vt?vt:ej.datavisualization.Chart.LabelPlacement.BetweenTicks,vt.toLowerCase()=="betweenticks"?(st=1+(t.labels.length>1?-.5:0),st=li?parseFloat(st.toFixed(li)):parseFloat(st.toFixed(ej.EjSvgRender.utils._decimalPlaces(y.interval)==0?1:ej.EjSvgRender.utils._decimalPlaces(y.interval))),rt=Math.ceil((st-y.min)/(y.max-y.min)*t.length)):rt=Math.ceil(t.length/et.length),t.orientation=="horizontal"){var ht=0,wi=0,bi,ut=t.labels.length>0&&t.valueType!="datetimecategory"?t.labels:t.visibleLabels,er=t.width?t.width/ut.length:(t.length-(i.svgWidth-t.length)-20)/ut.length;for(t.labelsCollection=[],c=0;c<ut.length;c++){var pt=0,fi=[],wt=[],bt=typeof ut[c]=="object"?ut[c].Text:ut[c];for(typeof bt=="string"&&bt.indexOf("<br>")!=-1?wt=bt.split("<br>"):wt.push(bt),s=0;s<wt.length;s++)for(kt=ej.EjAxisRenderer.prototype.rowscalculation(wt[s],i.model.m_AreaBounds,t,er,null),pt=kt.length+pt,p=0;p<kt.length;p++)fi.push(kt[p]);f&&(o=this._getHighestLabel(t,i,fi),ei=this._measureText(o,n(i.svgObject).width(),t.font).width,wi<ei&&(wi=ei,bi=o));ht=Math.max(pt,ht);t.labelsCollection[c]=fi}tt=this.rotatedLabel(t,i,f,bi)}rt=rt<0?Math.ceil(t.length/et.length):rt;l=Math.round(r.width/rt);r.rows=f?l-hi:l+hi;r.rows<0&&(r.rows=0);ct=ht&&!f?ht:r.rows;ht>l&&!f&&(ct=ct-1);r.height=f?tt.height+ct*tt.height:r.height+ct*r.height}else if(h=="multiplerows"){var or=i._getLegendSpace(),sr=i.model.border.width,wr=i.model._axes[1],hr=n(i.svgObject).width()-i.model.margin.left-i.model.margin.right;ri=t.title;ui=ri.text;var cr=ui==""||!t.visible?0:this._measureText(ui,hr,ri.font).height+2*i.model.elementSpacing,lr=i.model.elementSpacing+cr+t.majorTickLines.size+t.axisLine.width,ar=i._getYValues(i.model._visibleSeries[0].points),vr=Math.max.apply(Math,ar),ki=i.model._axes[1],g=this._measureText(vr,n(this.svgObject).width(),ki.font,ki.labelRotation),yr=or.rightLegendWidth+lr+g.width+i.model.margin.right+i.model.margin.left+2*sr,di=n(i.svgObject).width()-yr;if(ci){var ft=0,w=[],l=0,bi,wi=0;for(e=0;e<et.length;e++){u=et[e];d=[];typeof u.Text=="string"&&u.Text.indexOf("<br>")!=-1?d=u.Text.split("<br>"):d.push(u);it=d.length;oi=!1;k=Math.max(k,ft);ft=u.y=r.height;var a=this._measureText(u.Text,n(this.svgObject).width(),t.font),v=a.height,si=Math.abs(Math.floor((u.Value-y.min)/y.delta*di));if(w.length>0)for(dt=w,gt=!0,lt=0;lt<dt.length&&gt;lt++){var pt=0,b=dt[lt];for(c=0;b&&c<b.length;c++)for(s=0;s<it;s++){var nt=ej.EjSvgRender.utils._measureText(it==1?d[s].Text:d[s],n(this.svgObject).width(),t.font).width,v=ej.EjSvgRender.utils._measureText(d[s],n(this.svgObject).width(),t.font).height,gi=c==b.length?b[c-1]:b[c],pr=Math.abs(Math.floor((gi.Value-y.min)/y.delta*di)),nr=ej.EjSvgRender.utils._measureText(gi.Text,n(this.svgObject).width(),t.font).width;if(nt=nt/2,ni=pr+(t.isInversed?-nr/2:nr/2),ni>=si-nt)ft=u.y+v,u.y+=v,pt++,lt+1==dt.length&&(gt=!1);else if(c+1==b.length){gt=s==it-1?!1:!0;break}}}if(u.y=ft,l=ft/v-1,w[l]==undefined&&(w[l]=[]),it==1)w[l].push(u);else for(s=0;s<it;s++)w[l]==undefined&&(w[l]=[]),w[l].push({Text:d[s],Value:u.Value}),s!=it-1&&(l=l+1,ft=u.y+v)}k=w.length*v;r.height=r.height>k?r.height:k;v>0&&(r.rows=ur?Math.round((k+v)/v):Math.round(k/v));r.rows=f?r.rows-1:r.rows;r.rows<1&&(r.rows=1)}if(!ci){for(ti=0,e=0;e<t.visibleLabels.length;e++){for(u=t.visibleLabels[e],a=this._measureText(u.Text,n(this.svgObject).width(),t.font),nt=a.width,v=a.height,si=Math.abs(Math.floor((u.Value-t.visibleRange.min)/t.visibleRange.delta*t.length)),at=0;at<e;at++)b=t.visibleLabels[at],yi=Math.abs(Math.floor((b.Value-t.visibleRange.min)/t.visibleRange.delta*t.length)),tr=this._measureText(b.Text,n(this.svgObject).width(),t.font).height,ni=yi+tr/2,ni>si-v/2&&t.visibleLabels[at].y==u.y&&(ti=u.y+nt,u.y=ti,oi=!0);ir=ti}r.width=r.width+ir+(oi?5:0)}}}return r},rotatedLabel:function(n,t,i,r,u){var e={"font-size":n.font.size,transform:"rotate("+i+",0,0)","font-family":n.font.fontFamily,"font-style":n.font.fontStyle,rotateAngle:"rotate("+i+"deg)","text-anchor":"middle"};t=u?this.chartObj:t;var f=t.svgRenderer.createText(e,r),o=Math.ceil(this._measureBounds(f,t).height),s=Math.ceil(this._measureBounds(f,t).width);return{height:o,width:s}},_getTransform:function(n,t,i){var r,u,f,e;return i?(r=t.x,u=n.y,f=t.width,e=n.height):(r=n.x,u=t.y,f=n.width,e=t.height),{x:r,y:u,width:f,height:e}},_calculateroundedCorner:function(n,t,i,r,u,f){var e=t.x,o=t.y,c=5,l=this.chartObj.model.enableCanvasRendering&&!f?c/4:0,s=t.width,h=t.height,v,y,p,w,a;return typeof n!="object"||ej.util.isNullOrUndefined(n)?v=y=p=w=n:(v=n.topLeft,y=n.bottomLeft,p=n.topRight,w=n.bottomRight),v=ej.util.isNullOrUndefined(t.rx)?v:t.rx,y=ej.util.isNullOrUndefined(t.rx)?y:t.rx,p=ej.util.isNullOrUndefined(t.ry)?p:t.ry,w=ej.util.isNullOrUndefined(t.ry)?w:t.ry,a="M "+e+" "+(v+o)+" Q "+e+" "+o+" "+(e+v)+" "+o+" ",i&&r=="top"&&(a+="L "+(e+s/2-c/2+l)+" "+o+" L "+(e+s/2+l-u)+" "+(o-c)+" L "+(e+s/2+c/2+l)+" "+o+" "),a+="L "+(e+s-p)+" "+o+" Q "+(e+s)+" "+o+" "+(e+s)+" "+(o+p)+" ",i&&r=="right"&&(a+="L "+(e+s)+" "+(o+h/2-c/2+l)+" L "+(e+s+c)+" "+(o+h/2+l-u)+" L "+(e+s)+" "+(o+h/2+c/2+l)+" "),a+="L "+(e+s)+" "+(o+h-w)+" Q "+(e+s)+" "+(o+h)+" "+(e+s-w)+" "+(o+h)+" ",i&&r=="bottom"&&(a+="L "+(e+s/2-c/2+l)+" "+(o+h)+" L "+(e+s/2+l-u)+" "+(o+h+c)+" L "+(e+s/2+c/2+l)+" "+(o+h)+" "),a+="L "+(e+y)+" "+(o+h)+" Q "+e+" "+(o+h)+" "+e+" "+(o+h-y)+" ",i&&r=="left"&&(a+="L "+e+" "+(o+h/2-c/2+l)+" L "+(e-c)+" "+(o+h/2+l-u)+" L "+e+" "+(o+h/2+c/2+l)+" "),a+("L "+e+" "+(v+o)+" z")},browserInfo:function(){var n={},t=[],i={webkit:/(chrome)[ \/]([\w.]+)/i,safari:/(webkit)[ \/]([\w.]+)/i,msie:/(msie) ([\w.]+)/i,opera:/(opera)(?:.*version|)[ \/]([\w.]+)/i,mozilla:/(mozilla)(?:.*? rv:([\w.]+)|)/i};for(var r in i)if(i.hasOwnProperty(r)&&(t=navigator.userAgent.match(i[r]),t)){n.name=t[1].toLowerCase();n.version=t[2];!navigator.userAgent.match(/Trident\/7\./)||(n.name="msie");break}return n.isMSPointerEnabled=n.name=="msie"&&n.version>9&&window.navigator.msPointerEnabled,n.pointerEnabled=window.navigator.pointerEnabled,n},_measureText:function(t,i,r){var s=n(document).find("#measureTex"),u,e,o,f,v;n("#measureTex").css("display","block");s.length==0?(u=document.createElement("text"),n(u).attr({id:"measureTex"}),document.body.appendChild(u)):u=s[0];var h=null,c=null,l=null,a=null;if(typeof t=="string"&&(t.indexOf("<")>-1||t.indexOf(">")>-1)){for(e=t.split(" "),o=0;o<e.length;o++)e[o].indexOf("<br/>")==-1&&(e[o]=e[o].replace(/[<>]/g,"&"));t=e.join(" ")}return/<\/?[a-z][\s\S]*>/.test(t)?n(u).html(t):u.textContent=t,r!=undefined&&r.size==undefined&&(f=r,f=f.split(" "),h=f[0],c=f[1],l=f[2],a=f[3]),r!=null&&(u.style.fontSize=r.size>0?r.size+"px":r.size?r.size:c,u.style.fontStyle&&(u.style.fontStyle=r.fontStyle?r.fontStyle:h),u.style.fontFamily=r.fontFamily?r.fontFamily:l,window.navigator.userAgent.indexOf("MSIE 8.0")==-1&&(u.style.fontWeight=r.fontWeight?r.fontWeight:a)),u.style.backgroundColor="white",u.style.position="absolute",u.style.top=-100,u.style.left=0,u.style.visibility="hidden",u.style.whiteSpace="nowrap",i&&(u.style.maxwidth=i+"px"),v={width:u.offsetWidth,height:u.offsetHeight},n("#measureTex").css("display","none"),v},_trimText:function(n,t,i){var f,r,e,u;for(r=n.toString(),e=r.length,u=1;u<e;u++)if(r=n.substring(0,u)+"...",f=ej.EjSvgRender.utils._measureText(r,null,i).width,f>=t)return r.substring(0,u-1)+"... ";return n},_measureBounds:function(t,i){i.model.enableCanvasRendering?n(document.body).append(t):(i.svgRenderer.append(t,i.svgObject),i.svgRenderer.append(i.svgObject,i.element));var r=t.getBoundingClientRect(),u={left:r.left,right:r.right,top:r.top,bottom:r.bottom,width:r.right-r.left,height:r.bottom-r.top};return n(t).remove(),u},_drawAxesBoundsClipPath:function(t,i,r){var u,s=n(t),f=this._getTransform(i.xAxis,i.yAxis,r.model.requireInvertedAxes),h=r.model.AreaType=="polaraxes"?n(r.svgObject).width():f.width,c=r.model.AreaType=="polaraxes"?n(r.svgObject).height():f.height,e=r.model.requireInvertedAxes?i.yAxis.plotOffset:i.xAxis.plotOffset,o=r.model.requireInvertedAxes?i.xAxis.plotOffset:i.yAxis.plotOffset;r.model.AreaType=="polaraxes"?(u={id:t?t.id+"_ClipRect":"",cx:r.model.centerX,cy:r.model.centerY,r:r.model.Radius,fill:"white","stroke-width":1,stroke:"transparent"},r.svgRenderer.drawCircularClipPath(u,t)):(u={id:t?t.id+"_ClipRect":"",x:0-e,y:0-o,width:h+2*e,height:c+2*o,fill:"white","stroke-width":1,stroke:"transparent"},r.svgRenderer.drawClipPath(u,t));u.id.indexOf("_Rect")&&(u.id=u.id.replace("_Rect",""));s.attr("clip-path","url(#"+u.id+")")},_getStringBuilder:function(){var n=[],t=0;return{append:function(i){return n[t++]=i,this},remove:function(t,i){return n.splice(t,i||1),this},insert:function(t,i){return n.splice(t,0,i),this},toString:function(t){return n.join(t||"")}}},_addRegion:function(t,i,r,u,f){var s=r.type,e=n.inArray(r,t.model._visibleSeries),o;e>=0&&(o={SeriesIndex:e,Region:{PointIndex:f,Bounds:i},type:s},t.model.chartRegions.push(o))},AddRegion:function(n,t,i){if(i){var r={isStripLine:i,Region:{Bounds:t}};n.model.chartRegions.push(r)}},_getSvgXY:function(n,t,i,r){var u,f;return r.model.requireInvertedAxes?(u=n+i.yAxis.x,f=t+i.xAxis.y):(u=n+i.xAxis.x,f=t+i.yAxis.y),{X:u,Y:f}},_getPoint:function(n,t){var f=n.xValue,i,r,u,e=t.type=="boxandwhisker"?n.YValues:n.YValues[0],s=t._isTransposed?t.xAxis.height:t.xAxis.width,o=t._isTransposed?t.yAxis.width:t.yAxis.height;return n.location={},t._hiloTypes&&(i=n.YValues[1],i=t.yAxis._valueType=="logarithmic"?ej.EjSvgRender.utils._logBase(i==0?1:i,t.xAxis.logBase):i,i=this._getPointXY(i,t.yAxis.visibleRange,t.yAxis.isInversed),n.location.low=(t._isTransposed?i:1-i)*o),r=t.xAxis._valueType=="logarithmic"?ej.EjSvgRender.utils._logBase(f==0?1:f,t.xAxis.logBase):f,u=t.yAxis._valueType=="logarithmic"?ej.EjSvgRender.utils._logBase(e==0?1:e,t.xAxis.logBase):e,r=this._getPointXY(r,t.xAxis.visibleRange,t.xAxis.isInversed),u=this._getPointXY(u,t.yAxis.visibleRange,t.yAxis.isInversed),n.location.X=t._isTransposed?u*o:r*s,n.location.Y=t._isTransposed?(1-r)*s:(1-u)*o,n.location},_getPointXY:function(n,t,i){var r=0;return r=(n-t.min)/t.delta,r=isNaN(r)?0:r,i?1-r:r},_dateTimeLabelFormat:function(n,t){switch(n.toLowerCase()){case"years":return t._labelFormat="MMM, yyyy";case"months":return t._labelFormat="dd, MMM";case"days":return t._labelFormat="dd/MM/yyyy";case"hours":return t._labelFormat="dd, hh:mm";case"minutes":return t._labelFormat="hh:mm";case"seconds":return t._labelFormat="mm:ss";case"milliseconds":return t._labelFormat="ss:fff";default:return t._labelFormat="dd/MM/yyyy"}},_getFontString:function(n){return n==null&&(n={}),n.FontFamily||(n.FontFamily="Arial"),n.FontStyle||(n.FontStyle="Normal"),n.Size||(n.Size="12px"),n.FontStyle+" "+n.Size+" "+n.FontFamily},_valueToVector:function(n,t){return this._coefficientToVector(this._valueToPolarCoefficient(n,t))},TransformToVisible:function(n,t,i,r){t=n.xAxis._valueType=="logarithmic"&&t>0?Math.log(t,n.xAxis.logBase):t;i=n.xAxis._valueType=="logarithmic"&&i>0?Math.log(i,n.yAxis.logBase):i;var u=r.model.Radius*this._valueToCoefficient(n.yAxis,i),f=this._valueToVector(n.xAxis,t);return{X:r.model.centerX+u*f.X,Y:r.model.centerY+u*f.Y}},Transform3DToVisible:function(n,t,i,r){var u,s,h,f;if(n.xAxis!=null&&n.yAxis!=null){if(u=n.xAxis._valueType.toLowerCase(),f=u=="logarithmic"?!0:!1,t=t=f&&t>0?math.log(t,s):t,i=i,r.model.requireInvertedAxes){var e=r.model.m_AreaBounds.X,o=r.model.m_AreaBounds.Y,c=e+n.yAxis.width*ej.EjSvgRender.utils._valueToCoefficient(n.yAxis,i,r),l=o+n.xAxis.height*(1-ej.EjSvgRender.utils._valueToCoefficient(n.xAxis,t,r));return{X:c,Y:l}}var e=n.xAxis.x,o=n.yAxis.y,t=e+Math.round(n.xAxis.width*ej.EjSvgRender.utils._valueToCoefficient(n.xAxis,t,r)),i=o+Math.round(n.yAxis.height*(1-ej.EjSvgRender.utils._valueToCoefficient(n.yAxis,i,r)));return{X:t,Y:i}}return new h(0,0)},_valueToPolarCoefficient:function(n,t){var f=n.visibleRange.min,r,u,i;return n._valueType!="category"?(r=n.visibleRange.max-n.visibleRange.interval-n.visibleRange.min,u=n.visibleLabels.length-1):(r=n.visibleRange.delta,u=n.visibleLabels.length),i=(t-f)/r,i*=1-1/u,i=isNaN(i)?0:i,n.isInversed?i:1-i},_coefficientToVector:function(n){var t=Math.PI*(1.5-2*n);return{X:Math.cos(t),Y:Math.sin(t)}},_valueToCoefficient:function(n,t,i){var r;return r=i&&i.model.AreaType=="polaraxes"?t:n._valueType&&n._valueType.toLowerCase()=="logarithmic"?ej.EjSvgRender.utils._logBase(t==0?1:t,n.logBase):t,r=(r-n.visibleRange.min)/n.visibleRange.delta,n.isInversed?1-r:r},_getBoundingClientRect:function(t,i,r,u){var f=t.getBoundingClientRect(),s=n("#"+i.svgObject.id)[0].getBoundingClientRect(),e,o,h,c;return u?(e=this._getTransform(r.xAxis,r.yAxis,!0).x,o=this._getTransform(r.xAxis,r.yAxis,!0).y):(e=this._getTransform(r.xAxis,r.yAxis,!1).x,o=this._getTransform(r.xAxis,r.yAxis,!1).y),h=f.left-(e+s.left),c=f.top-(o+s.top),{x:h,y:c,width:f.right-f.left,height:f.bottom-f.top}},_minMax:function(n,t,i){return n>i?i:n<t?t:n},_inside:function(n,t){return n===""?!1:n<=t.max&&n>=t.min},_logBase:function(n,t){return Math.log(n)/Math.log(t)},_correctRect:function(n,t,i,r){return{X:Math.min(n,i),Y:Math.min(t,r),Width:Math.abs(i-n),Height:Math.abs(r-t)}},_getValuebyPoint:function(n,t,i,r){var e=r?i.xAxis.height:i.xAxis.width,o=r?i.yAxis.width:i.yAxis.height,u=i.xAxis.isInversed?1-n/e:n/e,f=i.yAxis.isInversed?1-t/o:t/o;return u=u*i.xAxis.visibleRange.delta+i.xAxis.visibleRange.min,f=f*i.yAxis.visibleRange.delta+i.yAxis.visibleRange.min,u=i.xAxis._valueType=="logarithmic"?Math.pow(i.xAxis.logBase,u):u,f=i.yAxis._valueType=="logarithmic"?Math.pow(i.yAxis.logBase,f):f,{PointX:u,PointY:f}}};ej.EjSvgRender.chartTransform3D={ToRadial:Math.PI/180,transform3D:function(n){return this.vector||(this.vector=new(new ej.Ej3DRender).vector3D,this.matrixobj=new(new ej.Ej3DRender).matrix3D,this.bsptreeobj=new(new ej.Ej3DRender).BSPTreeBuilder,this.polygon=new(new ej.Ej3DRender).polygon3D),{mViewport:n,Rotation:0,Tilt:0,Depth:0,PerspectiveAngle:0,needUpdate:!0,centeredMatrix:this.matrixobj.getIdentity(),Perspective:this.matrixobj.getIdentity(),resultMatrix:this.matrixobj.getIdentity(),viewMatrix:this.matrixobj.getIdentity(),Depth:0}},transform:function(n){this.setCenter(this.vector.vector3D(n.mViewport.Width/2,n.mViewport.Height/2,n.Depth/2),n);this.setViewMatrix(this.matrixobj.transform(0,0,n.Depth),n);this.setViewMatrix(this.matrixobj.getMatrixMultiplication(n.viewMatrix,this.matrixobj.turn(-this.ToRadial*n.Rotation)),n);this.setViewMatrix(this.matrixobj.getMatrixMultiplication(n.viewMatrix,this.matrixobj.tilt(-this.ToRadial*n.Tilt)),n);this.updatePerspective(n.PerspectiveAngle,n);n.needUpdate=!0},updatePerspective:function(n,t){var i=(t.mViewport.Width+t.mViewport.Height)*Math.tan(this.degreeToRadianConverter((180-Math.abs(n%181))/2))+t.Depth/1;t.Perspective[0][0]=i;t.Perspective[1][1]=i;t.Perspective[2][3]=1;t.Perspective[3][3]=i},degreeToRadianConverter:function(n){return n*Math.PI/180},toScreen:function(n,t,i,r){var i=i?i:this.result;return r?(this.matrixobj=r,n=r.getMatrixVectorMutiple(i(t,r),n)):(t.chartObj=this.matrixobj,n=this.matrixobj.getMatrixVectorMutiple(i(t),n)),{x:n.x,y:n.y}},setViewMatrix:function(n,t){t.viewMatrix!=n&&(t.viewMatrix=n,t.needUpdate=!0)},setCenteredMatrix:function(n,t){t.viewMatrix!=n&&(t.centeredMatrix=n,t.needUpdate=!0)},result:function(n,t){var i=n.chartObj?n.chartObj:this.matrixobj,r;return(i||(i=t),!n.needUpdate)?n.resultMatrix:(r=this.matrixobj?this.matrixobj:t,n.resultMatrix=i.getMatrixMultiplication(r.getInvertal(n.centeredMatrix),n.Perspective),n.resultMatrix=i.getMatrixMultiplication(n.resultMatrix,n.viewMatrix),n.resultMatrix=i.getMatrixMultiplication(n.resultMatrix,n.centeredMatrix),n.needUpdate=!1,n.resultMatrix)},setCenter:function(n,t){t.centeredMatrix=this.matrixobj.transform(-n.x,-n.y,-n.z);t.needUpdate=!0},toPlane:function(n,t,i){var r=this.vector.vector3D(n.x,n.y,0),u=this.vector.vector3DPlus(r,this.vector.vector3D(0,0,1));return r=this.vector.vector3DMultiply(i.centeredMatrix,r),u=this.vector.vector3DMultiply(i.centeredMatrix,u),r=this.vector.vector3DMultiply(this.matrixobj.getInvertal(i.Perspective),r),u=this.vector.vector3DMultiply(this.matrixobj.getInvertal(i.Perspective),u),r=this.polygon.getPoint(r,this.vector.vector3DMinus(u-r)),r=this.vector.vector3DMultiply(this.matrixobj.getInvertal(i.viewMatrix),r),this.vector.vector3DMultiply(this.matrixobj.getInvertal(i.centeredMatrix),r)}};ej.EjSvgRender.chartSymbol={_drawSeriesType:function(n,t,i){var r=i.model.series[t.SeriesIndex].type.toLowerCase(),u=i.legendItem.drawType;r=ej.util.isNullOrUndefined(u)?r:u;switch(r){case ej.datavisualization.Chart.Type.Line:return ej.util.isNullOrUndefined(u)?this._drawLine(n,t,i,i.gLegendItemEle):this._drawStraightLine(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.StepLine:return this._drawStepLine(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.StackingArea:case ej.datavisualization.Chart.Type.StackingArea100:case ej.datavisualization.Chart.Type.Area:case ej.datavisualization.Chart.Type.RangeArea:return this._drawArea(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.StepArea:return this._drawStepArea(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.Bar:case ej.datavisualization.Chart.Type.StackingBar100:case ej.datavisualization.Chart.Type.StackingBar:return this._drawBar(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.Pie:return this._drawPie(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.Doughnut:return this._drawDoughnut(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.Hilo:return this._drawHilo(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.HiloOpenClose:return this._drawHiloOpenClose(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.Candle:return this._drawCandle(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.Pyramid:return this._drawPyramid(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.Funnel:return this._drawFunnel(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.Spline:return this._drawSpline(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.SplineArea:case ej.datavisualization.Chart.Type.StackingSplineArea:case ej.datavisualization.Chart.Type.StackingSplineArea100:return this._drawSplineArea(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.RangeColumn:return this._drawRangeColumn(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.Bubble:case ej.datavisualization.Chart.Type.Scatter:return this._drawCircle(n,t,i,i.gLegendItemEle);case ej.datavisualization.Chart.Type.Column:case ej.datavisualization.Chart.Type.StackingColumn:case ej.datavisualization.Chart.Type.StackingColumn100:case ej.datavisualization.Chart.Type.Waterfall:return this._drawColumn(n,t,i,i.gLegendItemEle);default:return this._drawRectangle(n,t,i,i.gLegendItemEle)}},_drawCircle:function(n,t,i,r){var o=i.svgRenderer,h=i.svgObject,f=t.ShapeSize,e=Math.sqrt(f.height*f.height+f.width*f.width)/2,u=t.Style,s={id:t.ID,cx:n.startX,cy:n.startY,r:e,fill:u.Color,"stroke-width":u.BorderWidth,stroke:u.BorderColor,opacity:u.Opacity,visibility:u.Visibility,lgndCtx:t.context};return o.drawCircle(s,r),n.startX-e},_drawLeftArrow:function(n,t,i,r){var o=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,f,e;f="M "+(n.startX-u.width/2+u.width)+" "+(n.startY+u.height/4)+" L "+(n.startX-u.width/2+u.width)+" "+(n.startY+-u.height/4)+" L "+(n.startX-u.width/2+u.width/2)+" "+(n.startY+-u.height/4)+" L "+(n.startX-u.width/2+u.width/2)+" "+(n.startY+-u.height/2)+" L "+(n.startX-u.width/2)+" "+n.startY+" L "+(n.startX-u.width/2+u.width/2)+" "+(n.startY+u.height/2)+" L "+(n.startX-u.width/2+u.width/2)+" "+(n.startY+u.height/4)+" L "+(n.startX-u.width/2+u.width)+" "+(n.startY+u.height/4)+" z";e={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,lgndCtx:t.context,d:f};o.drawPath(e,r)},_drawRightArrow:function(n,t,i,r){var o=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,f,e;f="M "+(n.startX-u.width/2)+" "+(n.startY+u.height/4)+" L "+(n.startX-u.width/2)+" "+(n.startY+-u.height/4)+" L "+(n.startX-u.width/2+u.width/2)+" "+(n.startY+-u.height/4)+" L "+(n.startX-u.width/2+u.width/2)+" "+(n.startY+-u.height/2)+" L "+(n.startX-u.width/2+u.width)+" "+n.startY+" L "+(n.startX-u.width/2+u.width/2)+" "+(n.startY+u.height/2)+" L "+(n.startX-u.width/2+u.width/2)+" "+(n.startY+u.height/4)+" L "+(n.startX-u.width/2)+" "+(n.startY+u.height/4)+" z";e={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,lgndCtx:t.context,d:f};o.drawPath(e,r)},_drawUpArrow:function(n,t,i,r){var o=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,f,e;f="M "+(n.startX-u.width/2+u.width/4)+" "+(n.startY-u.height/2+u.height)+" L "+(n.startX-u.width/2+u.width/4)+" "+(n.startY-u.height/2+u.height/2)+" L "+(n.startX-u.width/2)+" "+(n.startY-u.height/2+u.height/2)+" L "+n.startX+" "+(n.startY-u.height/2)+" L "+(n.startX+u.width/2)+" "+(n.startY-u.height/2+u.height/2)+" L "+(n.startX+u.width/2-u.width/4)+" "+(n.startY-u.height/2+u.height/2)+" L "+(n.startX+u.width/2-u.width/4)+" "+(n.startY-u.height/2+u.height)+" L "+(n.startX-u.width/2+u.width/4)+" "+(n.startY-u.height/2+u.height)+" z";e={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,lgndCtx:t.context,d:f};o.drawPath(e,r)},_drawDownArrow:function(n,t,i,r){var o=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,f,e;f="M "+(n.startX-u.width/2+u.width/4)+" "+(n.startY-u.height/2)+" L "+(n.startX+u.width/2-u.width/4)+" "+(n.startY-u.height/2)+" L "+(n.startX+u.width/2-u.width/4)+" "+(n.startY-u.height/2+u.height/2)+" L "+(n.startX+u.width/2)+" "+(n.startY-u.height/2+u.height/2)+" L "+n.startX+" "+(n.startY-u.height/2+u.height)+" L "+(n.startX-u.width/2)+" "+(n.startY-u.height/2+u.height/2)+" L "+(n.startX-u.width/2+u.width/4)+" "+(n.startY-u.height/2+u.height/2)+" L "+(n.startX-u.width/2+u.width/4)+" "+(n.startY-u.height/2)+" z";e={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,lgndCtx:t.context,d:f};o.drawPath(e,r)},_drawCross:function(n,t,i,r){var o=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,f,e;f="M "+(n.startX+-u.width/2)+" "+n.startY+" L "+(n.startX+u.width/2)+" "+n.startY+" M "+n.startX+" "+(n.startY+u.height/2)+" L "+n.startX+" "+(n.startY+-u.height/2);e={id:t.ID,opacity:t.Style.Opacity,"stroke-width":t.Style.BorderWidth,stroke:t.Style.Color,visibility:t.Style.Visibility,lgndCtx:t.context,d:f};o.drawPath(e,r)},_drawHorizLine:function(n,t,i,r){var o=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,f,e;f="M "+(n.startX+-u.width/2)+" "+n.startY+" L "+(n.startX+u.width/2)+" "+n.startY;e={id:t.ID,opacity:t.Style.Opacity,"stroke-width":t.Style.BorderWidth,stroke:t.Style.Color,visibility:t.Style.Visibility,lgndCtx:t.context,d:f};o.drawPath(e,r)},_drawVertLine:function(n,t,i,r){var o=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,f,e;f="M "+n.startX+" "+(n.startY+u.height/2)+" L "+n.startX+" "+(n.startY+-u.height/2);e={id:t.ID,opacity:t.Style.Opacity,"stroke-width":t.Style.BorderWidth,stroke:t.Style.Color,visibility:t.Style.Visibility,lgndCtx:t.context,d:f};o.drawPath(e,r)},_drawTriangle:function(n,t,i,r){var o=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,f,e;f="M "+(n.startX+-u.width/2)+" "+(n.startY+u.height/2)+" L "+n.startX+" "+(n.startY+-u.height/2)+" L "+(n.startX+u.width/2)+" "+(n.startY+u.height/2)+" L "+(n.startX+-u.width/2)+" "+(n.startY+u.height/2)+" z";e={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,lgndCtx:t.context,d:f};o.drawPath(e,r)},_drawInvertedTriangle:function(n,t,i,r){var f=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,e="M "+(n.startX+u.width/2)+" "+(n.startY-u.height/2)+" L "+n.startX+" "+(n.startY+u.height/2)+" L "+(n.startX-u.width/2)+" "+(n.startY-u.height/2)+" L "+(n.startX+u.width/2)+" "+(n.startY-u.height/2)+" z";var h=n.startX,c=n.startY,o={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,lgndCtx:t.context,d:e};f.drawPath(o,r)},_drawHexagon:function(n,t,i,r){var o=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,f,e;f="M "+(n.startX+-u.width/2)+" "+n.startY+" L "+(n.startX+-u.width/4)+" "+(n.startY+-u.height/2)+" L "+(n.startX+u.width/4)+" "+(n.startY+-u.height/2)+" L "+(n.startX+u.width/2)+" "+n.startY+" L "+(n.startX+u.width/4)+" "+(n.startY+u.height/2)+" L "+(n.startX+-u.width/4)+" "+(n.startY+u.height/2)+" L "+(n.startX+-u.width/2)+" "+n.startY+" z";e={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,lgndCtx:t.context,d:f};o.drawPath(e,r)},_drawWedge:function(n,t,i,r){var o=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,f,e;f="M "+(n.startX-u.width)+" "+n.startY+" L "+(n.startX+u.width)+" "+(n.startY+-u.height/2)+" L "+(n.startX+3*(u.width/4))+" "+n.startY+" L "+(n.startX+u.width)+" "+(n.startY+u.height/2)+" L "+(n.startX-u.width)+" "+n.startY+" z";e={"stroke-linecap":"miter","stroke-miterlimit":u.width/4,id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,lgndCtx:t.context,d:f};o.drawPath(e,r)},_drawPentagon:function(n,t,i,r){for(var l,a,v=i.svgRenderer,p=i.svgObject,u=t.ShapeSize,o=Math.sqrt(u.height*u.height+u.width*u.width)/2,f=ej.EjSvgRender.utils._getStringBuilder(),e=0;e<=5;e++){var y=e*72,s=Math.PI/180*y,h=o*Math.cos(s),c=o*Math.sin(s);e==0?f.append("M "+(n.startX+h)+" "+(n.startY+c)+" "):f.append("L "+(n.startX+h)+" "+(n.startY+c)+" ")}f.append("z");l=f.toString();a={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,lgndCtx:t.context,d:l};v.drawPath(a,r)},_drawStar:function(n,t,i,r){var o=i.svgRenderer,u=t.ShapeSize,s=i.svgObject,f,e;f="M "+(n.startX+u.width/3)+" "+(n.startY+-u.height/2)+" L "+(n.startX+-u.width/2)+" "+(n.startY+u.height/6)+" L "+(n.startX+u.width/2)+" "+(n.startY+u.height/6)+" L "+(n.startX+-u.width/3)+" "+(n.startY+-u.height/2)+" L "+n.startX+" "+(n.startY+u.height/2)+" L "+(n.startX+u.width/3)+" "+(n.startY+-u.height/2)+" z";e={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,lgndCtx:t.context,d:f};o.drawPath(e,r)},_drawRectangle:function(n,t,i,r){var o=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,f,e;f="M "+(n.startX+-u.width/2)+" "+(n.startY+-u.height/2)+" L "+(n.startX+u.width/2)+" "+(n.startY+-u.height/2)+" L "+(n.startX+u.width/2)+" "+(n.startY+u.height/2)+" L "+(n.startX+-u.width/2)+" "+(n.startY+u.height/2)+" L "+(n.startX+-u.width/2)+" "+(n.startY+-u.height/2)+" z";e={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,lgndCtx:t.context,d:f};o.drawPath(e,r)},_drawTrapezoid:function(n,t,i,r){var o=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,f,e;f="M "+(n.startX+-u.width/2)+" "+n.startY+" L "+(n.startX+-u.width/2)+" "+(n.startY+-u.height/4)+" L "+(n.startX+-u.width/2+u.width)+" "+(n.startY+-u.height/2)+" L "+(n.startX+-u.width/2+u.width)+" "+(n.startY+u.height/2)+" L "+(n.startX+-u.width/2)+" "+(n.startY+u.height/4)+" L "+(n.startX+-u.width/2)+" "+n.startY+" z";e={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,lgndCtx:t.context,d:f};o.drawPath(e,r)},_drawDiamond:function(n,t,i,r){var o=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,f,e;f="M "+(n.startX+-u.width/2)+" "+n.startY+" L "+n.startX+" "+(n.startY+-u.height/2)+" L "+(n.startX+u.width/2)+" "+n.startY+" L "+n.startX+" "+(n.startY+u.height/2)+" L "+(n.startX+-u.width/2)+" "+n.startY+" z";e={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,lgndCtx:t.context,d:f};o.drawPath(e,r)},_drawEllipse:function(n,t,i,r){var f=i.svgRenderer,h=i.svgObject,u=t.ShapeSize,e=n.startX,o=n.startY,s={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,lgndCtx:t.context,cx:e,cy:o,rx:u.width,ry:u.height/2};f.drawEllipse(s,r)},_drawImage:function(n,t,i,r){var f=i.svgRenderer,e=i.svgObject,u=t.ShapeSize,o=n.startX+-u.width/2,s=n.startY+-u.width/2,h=u.width,c=u.height,l={id:e.id+"_image"+t.PointIndex,height:c,width:h,href:t.Imageurl,x:o,y:s,visibility:"visible",lgndCtx:t.context};f.drawImage(l,r)},_drawStraightLine:function(n,t,i,r){var s=i.svgRenderer,h=i.svgObject,u=t.ShapeSize,f=t.ElementSpace,e,o;return e="M "+(n.startX+-u.width/2+-f/4)+" "+(n.startY+u.height/10)+" L "+(n.startX+u.width/2+f/4)+" "+(n.startY+u.height/10),o={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth*2,stroke:t.Style.Color,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:e,lgndCtx:!0},s.drawPath(o,r),n.startX+-u.width/2+-f/4},_drawLine:function(n,t,i,r){var o=i.svgRenderer,l=i.svgObject,u=t.ShapeSize,f=t.ElementSpace,e,s;if(i.model.enableCanvasRendering===!0){e="M "+(n.startX+-u.width/2+-f/4)+" "+(n.startY+u.height/10)+" L "+(n.startX-Math.floor(u.width/3))+" "+(n.startY+u.height/10)+" M "+(n.startX+Math.floor(u.width/3))+" "+(n.startY+u.height/10)+" L "+(n.startX+u.width/2+f/4)+" "+(n.startY+u.height/10);var s={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth*2,stroke:t.Style.Color,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:e,lgndCtx:!0},h="M "+(n.startX-Math.floor(u.width/3))+" "+(n.startY+u.height/10)+" a "+Math.floor(u.width/3)+" "+Math.floor(u.width/3)+" 0 1 0 "+2*Math.floor(u.width/3)+" 0 a"+Math.floor(u.width/3)+" "+Math.floor(u.width/3)+" 0 1 0 "+-2*Math.floor(u.width/3)+" 0",c={id:t.ID,fill:"transparent","stroke-width":t.Style.BorderWidth*2,stroke:t.Style.Color,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:h,lgndCtx:!0};o.drawPath(s,r);o.drawPath(c,r)}else e="M "+(n.startX+-u.width/2+-f/4)+" "+(n.startY+u.height/10)+" L "+(n.startX-Math.floor(u.width/3))+" "+(n.startY+u.height/10)+" M "+(n.startX+Math.floor(u.width/3))+" "+(n.startY+u.height/10)+" L "+(n.startX+u.width/2+f/4)+" "+(n.startY+u.height/10)+" M "+(n.startX-Math.floor(u.width/3))+" "+(n.startY+u.height/10)+" a "+Math.floor(u.width/3)+" "+Math.floor(u.width/3)+" 0 1 0 "+2*Math.floor(u.width/3)+" 0 a"+Math.floor(u.width/3)+" "+Math.floor(u.width/3)+" 0 1 0 "+-2*Math.floor(u.width/3)+" 0",s={id:t.ID,fill:"transparent","stroke-width":t.Style.BorderWidth*2,stroke:t.Style.Color,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:e,lgndCtx:!0},o.drawPath(s,r);return n.startX+-u.width/2+-f/4},_drawColumn:function(n,t,i,r){var s=i.svgRenderer,h=i.svgObject,u=t.ShapeSize,f=t.ElementSpace,e,o;return e="M "+(n.startX-3*(u.width/5))+" "+(n.startY-u.height/5)+" L "+(n.startX+3*(-u.width/10))+" "+(n.startY-u.height/5)+" L "+(n.startX+3*(-u.width/10))+" "+(n.startY+u.height/2)+" L "+(n.startX-3*(u.height/5))+" "+(n.startY+u.height/2)+" Z M "+(n.startX+-u.width/10-u.width/20)+" "+(n.startY-u.height/4-f/2)+" L "+(n.startX+u.width/10+u.width/20)+" "+(n.startY-u.height/4-f/2)+" L "+(n.startX+u.width/10+u.width/20)+" "+(n.startY+u.height/2)+" L "+(n.startX+-u.width/10-u.width/20)+" "+(n.startY+u.height/2)+" Z M "+(n.startX+3*(u.width/10))+" "+n.startY+" L "+(n.startX+3*(u.width/5))+" "+n.startY+" L "+(n.startX+3*(u.width/5))+" "+(n.startY+u.height/2)+" L "+(n.startX+3*(u.width/10))+" "+(n.startY+u.height/2)+" Z",o={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:e,lgndCtx:!0},s.drawPath(o,r),n.startX-3*(u.width/5)},_drawRangeColumn:function(n,t,i,r){var s=i.svgRenderer,h=i.svgObject,u=t.ShapeSize,f=t.ElementSpace,e,o;return e="M "+(n.startX+-u.width/5)+" "+(n.startY+u.height/2+f/4)+" L "+(n.startX+-u.width/5)+" "+(n.startY-u.height/2-f/4)+" L "+(n.startX+u.width/2)+" "+(n.startY+-u.height/2+-f/4)+" L "+(n.startX+u.width/2)+" "+(n.startY+u.height/2+f/4)+" Z",o={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:e,lgndCtx:!0},s.drawPath(o,r),n.startX+-u.width/5},_drawBar:function(n,t,i,r){var s=i.svgRenderer,h=i.svgObject,u=t.ShapeSize,f=t.ElementSpace,e,o;return e="M "+(n.startX+-u.width/2+-f/4)+" "+(n.startY-3*(u.height/5))+" L "+(n.startX+3*(u.width/10))+" "+(n.startY-3*(u.height/5))+" L "+(n.startX+3*(u.width/10))+" "+(n.startY-3*(u.height/10))+" L "+(n.startX-u.width/2+-f/4)+" "+(n.startY-3*(u.height/10))+" Z M "+(n.startX+-u.width/2+-f/4)+" "+(n.startY-u.height/5+f/20)+" L "+(n.startX+u.width/2+f/4)+" "+(n.startY-u.height/5+f/20)+" L "+(n.startX+u.width/2+f/4)+" "+(n.startY+u.height/10+f/20)+" L "+(n.startX-u.width/2+-f/4)+" "+(n.startY+u.height/10+f/20)+" Z M "+(n.startX-u.width/2+-f/4)+" "+(n.startY+u.height/5+f/10)+" L "+(n.startX+-u.width/4)+" "+(n.startY+u.height/5+f/10)+" L "+(n.startX+-u.width/4)+" "+(n.startY+u.height/2+f/10)+" L "+(n.startX-u.width/2+-f/4)+" "+(n.startY+u.height/2+f/10)+" Z",o={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:e,lgndCtx:!0},s.drawPath(o,r),n.startX+-u.width/2+-f/4},_drawStepLine:function(n,t,i,r){var s=i.svgRenderer,h=i.svgObject,u=t.ShapeSize,f=t.ElementSpace,e,o;return e="M "+(n.startX+-u.width/2-f/4)+" "+(n.startY+u.height/2)+" L "+(n.startX+-u.width/2+u.width/10)+" "+(n.startY+u.height/2)+" L "+(n.startX+-u.width/2+u.width/10)+" "+n.startY+" L "+(n.startX+-u.width/10)+" "+n.startY+" L "+(n.startX+-u.width/10)+" "+(n.startY+u.height/2)+" L "+(n.startX+u.width/5)+" "+(n.startY+u.height/2)+" L "+(n.startX+u.width/5)+" "+(n.startY+-u.height/2)+" L "+(n.startX+u.width/2)+" "+(n.startY+-u.height/2)+"L "+(n.startX+u.width/2)+" "+(n.startY+u.height/2)+" L"+(n.startX+u.width/2+f/4)+" "+(n.startY+u.height/2),o={id:t.ID,fill:"transparent","stroke-width":t.Style.BorderWidth,stroke:t.Style.Color,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:e,lgndCtx:!0},s.drawPath(o,r),n.startX+-u.width/2-f/4},_drawSpline:function(n,t,i,r){var o=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,f,e;return f="M "+(n.startX-u.width/2)+" "+(n.startY+u.height/5)+" Q "+n.startX+" "+(n.startY-u.height)+" "+n.startX+" "+(n.startY+u.height/5)+" M "+n.startX+" "+(n.startY+u.height/5)+" Q "+(n.startX+u.width/2)+" "+(n.startY+u.height/2)+" "+(n.startX+u.width/2)+" "+(n.startY-u.height/2),e={id:t.ID,fill:"transparent","stroke-width":t.Style.BorderWidth*2,stroke:t.Style.Color,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:f,lgndCtx:!0},o.drawPath(e,r),n.startX-u.width/2},_drawSplineArea:function(n,t,i,r){var o=i.svgRenderer,s=i.svgObject,u=t.ShapeSize,f,e;return f="M "+(n.startX-u.width/2)+" "+(n.startY+u.height/5)+" Q "+n.startX+" "+(n.startY-u.height)+" "+n.startX+" "+(n.startY+u.height/5)+" Z M "+n.startX+" "+(n.startY+u.height/5)+" Q "+(n.startX+u.width/2)+" "+(n.startY+u.height/2)+" "+(n.startX+u.width/2)+" "+(n.startY-u.height/2)+"  Z",e={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.Color,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:f,lgndCtx:!0},o.drawPath(e,r),n.startX-u.width/2},_drawArea:function(n,t,i,r){var s=i.svgRenderer,h=i.svgObject,u=t.ShapeSize,f=t.ElementSpace,e,o;return e="M "+(n.startX-u.width/2-f/4)+" "+(n.startY+u.height/2)+" L "+(n.startX+-u.width/4+-f/8)+" "+(n.startY-u.height/2)+" L "+n.startX+" "+(n.startY+u.height/4)+" L "+(n.startX+u.width/4+f/8)+" "+(n.startY+-u.height/2+u.height/4)+" L "+(n.startX+u.height/2+f/4)+" "+(n.startY+u.height/2)+" Z",o={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:e,lgndCtx:!0},s.drawPath(o,r),n.startX-u.width/2-f/4},_drawStepArea:function(n,t,i,r){var s=i.svgRenderer,h=i.svgObject,u=t.ShapeSize,f=t.ElementSpace,e,o;return e="M "+(n.startX+-u.width/2+-f/4)+" "+(n.startY+u.height/2)+" L "+(n.startX+-u.width/2+-f/4)+" "+(n.startY+-u.height/2)+" L "+(n.startX-u.width/4)+" "+(n.startY-u.height/2)+" L "+(n.startX-u.width/4)+" "+(n.startY-u.height/4)+" L "+(n.startX+u.width/4)+" "+(n.startY-u.height/4)+" L "+(n.startX+u.width/4)+" "+n.startX+" L "+(n.startX+u.width/2+f/4)+" "+n.startY+" L "+(n.startX+u.width/2+f/4)+" "+(n.startY+u.height/2)+" Z",o={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:e,lgndCtx:!0},s.drawPath(o,r),n.startX+-u.width/2+-f/4},_drawPyramid:function(n,t,i,r){var s=i.svgRenderer,h=i.svgObject,u=t.ShapeSize,f=t.ElementSpace,e,o;return e="M "+(n.startX+-u.width/2+-f/4)+" "+(n.startY+u.height/2+f/8)+" L "+n.startX+" "+(n.startY+-u.height/2+-f/8)+" L "+(n.startX+u.width/2+f/4)+" "+(n.startY+u.height/2+f/8)+" Z",o={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:e,lgndCtx:!0},s.drawPath(o,r),n.startX+-u.width/2+-f/4},_drawFunnel:function(n,t,i,r){var s=i.svgRenderer,h=i.svgObject,u=t.ShapeSize,f=t.ElementSpace,e,o;return e="M "+(n.startX+-u.width/2+-f/5)+" "+(n.startY+-u.height/2+-f/4)+" L "+(n.startX+-u.width/5)+" "+(n.startY+u.height/4)+" L "+(n.startX+-u.width/5)+" "+(n.startY+u.height/2+f/4)+" L "+(n.startX+u.width/5)+" "+(n.startY+u.height/2+f/4)+" L "+(n.startX+u.width/5)+" "+(n.startY+u.height/4)+" L "+(n.startX+u.width/2+f/5)+" "+(n.startY+-u.height/2+-f/4)+" Z",o={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.BorderColor,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:e,lgndCtx:!0},s.drawPath(o,r),n.startX+-u.width/2+-f/5},_drawCandle:function(n,t,i,r){var s=i.svgRenderer,h=i.svgObject,u=t.ShapeSize,f=t.ElementSpace,e,o;return e="M "+(n.startX+-u.width/2)+" "+(n.startY+u.height/4+f/8)+" L "+(n.startX+u.width/2)+" "+(n.startY+u.height/4+f/8)+" L "+(n.startX+u.width/2)+" "+(n.startY+-u.height/2+f/8)+" L "+(n.startX+-u.width/2)+" "+(n.startY+-u.height/2+f/8)+" Z M "+n.startX+" "+(n.startY+-u.height/2+f/8)+" L "+n.startX+" "+(n.startY+-u.height/2+-f/4)+" M "+n.startX+" "+(n.startY+u.height/4+f/8)+" L "+n.startX+" "+(n.startY+u.height/4+f/2),o={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.Color,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:e,lgndCtx:!0},s.drawPath(o,r),n.startX+-u.width/2},_drawHilo:function(n,t,i,r){var s=i.svgRenderer,h=i.svgObject,u=t.ShapeSize,f=t.ElementSpace,e,o;return e="M "+(n.startX+u.width/2)+" "+(n.startY+u.height/2+f/4)+" L "+(n.startX+u.width/2)+" "+(n.startY+-u.height/2+-f/4),o={id:t.ID,fill:t.Style.Color,"stroke-width":t.Style.BorderWidth,stroke:t.Style.Color,opacity:t.Style.Opacity,visibility:t.Style.Visibility,d:e,lgndCtx:!0},s.drawPath(o,r),n.startX+u.width/2},_drawHiloOpenClose:function(n,i,r,u){var h=r.svgRenderer,c=r.svgObject,f=i.ShapeSize,l=t(18,12,4.0710678118654755,270),e=i.ElementSpace,o,s;return o="M "+n.startX+" "+(n.startY-3*(f.height/10))+" L "+(n.startX-f.width/2-e/4)+" "+(n.startY-3*(f.height/10))+" M "+n.startX+" "+(n.startY-f.height/2-e/4)+" L "+n.startX+" "+(n.startY+f.height/2+e/4)+" M "+n.startX+" "+(n.startY+3*(f.height/10))+" L "+(n.startX+f.width/2+e/4)+" "+(n.startY+3*(f.height/10)),s={id:i.ID,fill:"transparent","stroke-width":i.Style.BorderWidth,stroke:i.Style.Color,opacity:i.Style.Opacity,visibility:i.Style.Visibility,d:o,lgndCtx:!0},h.drawPath(s,u),n.startX-f.width/2},_drawDoughnut:function(n,i,r,u){var o=r.svgRenderer,y=r.svgObject,e=i.ShapeSize,c=i.ElementSpace,f=Math.sqrt(e.height*e.width)/2;f=f+c/5;var s=t(n.startX,n.startY,f,270),h=t(n.startX+e.width/10,n.startY,f,270),c=i.ElementSpace,l="M "+n.startX+" "+n.startY+" L "+(n.startX+f)+" "+n.startY+" A "+f+" "+f+" 0 1 1 "+s[0]+" "+s[1]+" Z M "+(n.startX+e.width/10)+" "+(n.startY-e.height/10)+" L"+(n.startX+f)+" "+(n.startY-e.height/10)+" A "+f+" "+f+" 0 0 0 "+h[0]+" "+h[1]+" Z",a={id:i.ID,fill:i.Style.Color,"stroke-width":i.Style.BorderWidth,stroke:i.Style.BorderColor,opacity:i.Style.Opacity,visibility:i.Style.Visibility,d:l,lgndCtx:!0},v={id:i.ID,cx:n.startX,cy:n.startY,r:f/2,lgndCtx:!0,fill:"white","stroke-width":i.Style.BorderWidth,stroke:i.Style.BorderColor,opacity:i.Style.Opacity,visibility:i.Style.Visibility};return o.drawPath(a,u),o.drawCircle(v,u),n.startX-f},_drawPie:function(n,i,r,u){var h=r.svgRenderer,v=r.svgObject,e=i.ShapeSize,c=i.ElementSpace,f=Math.sqrt(e.height*e.width)/2;f=f+c/5;var o=t(n.startX,n.startY,f,270),s=t(n.startX+e.width/10,n.startY,f,270),c=i.ElementSpace,l="M "+n.startX+" "+n.startY+" L "+(n.startX+f)+" "+n.startY+" A "+f+" "+f+" 0 1 1 "+o[0]+" "+o[1]+" Z M "+(n.startX+e.width/10)+" "+(n.startY-e.height/10)+" L"+(n.startX+f)+" "+(n.startY-e.height/10)+" A "+f+" "+f+" 0 0 0 "+s[0]+" "+s[1]+" Z",a={id:i.ID,fill:i.Style.Color,"stroke-width":i.Style.BorderWidth,stroke:i.Style.BorderColor,opacity:i.Style.Opacity,visibility:i.Style.Visibility,d:l,lgndCtx:!0};return h.drawPath(a,u),n.startX-f}};ej.EjSvgRender.seriesPalette={defaultMetro:["#E94649","#F6B53F","#6FAAB0","#C4C24A","#FB954F","#005277","#8BC652","#69D2E7","#E27F2D","#6A4B82"],defaultHighContrast:["#F93A00","#44E2D6","#DDD10D","#0AA368","#0556CB","#AB40B2","#5F930A","#D12E41","#E0670E","#008FFF"],defaultOffice:["#005277","#8BC652","#6A4B82","#E94649","#6FAAB0","#F7B74F","#C4C24A","#EF863F","#69D2E7","#FFD13E"],defaultMaterial:["#663AB6","#EB3F79","#F8AB1D","#B82E3D","#049CB1","#F2424F","#C2C924","#3DA046","#074D67","#02A8F4"],defaultGradient:{borderColors:["#F34649","#F6D321","#6EB9B0","#CBC26A","#FBAF4F","#E2CDB1","#FFC0B7","#68E1E6","#E1A62D","#9C6EBF"],seriesColors:[[{color:"#F34649",colorStop:"0%"},{color:"#B74143",colorStop:"100%"}],[{color:"#F6D321",colorStop:"0%"},{color:"#F6AE26",colorStop:"100%"}],[{color:"#6EB9B0",colorStop:"0%"},{color:"#3F77BD",colorStop:"100%"}],[{color:"#CBC26A",colorStop:"0%"},{color:"#9AAD21",colorStop:"100%"}],[{color:"#FBAF4F",colorStop:"0%"},{color:"#F07542",colorStop:"100%"}],[{color:"#E2CDB1",colorStop:"0%"},{color:"#AAA089",colorStop:"100%"}],[{color:"#8BC652",colorStop:"0%"},{color:"#6F9E41",colorStop:"100%"}],[{color:"#68E1E6",colorStop:"0%"},{color:"#3D9CBE",colorStop:"100%"}],[{color:"#E1A62D",colorStop:"0%"},{color:"#B66824",colorStop:"100%"}],[{color:"#9C6EBF",colorStop:"0%"}],[{color:"#593F6D",colorStop:"100%"}]]},blueMetro:["#005378","#006691","#007EB5","#0D97D4","#00AEFF","#14B9FF","#54CCFF","#87DBFF","#ADE5FF","#C5EDFF"],blueGradient:{seriesColors:[[{color:"#005277",colorStop:"0%"},{color:"#00304F",colorStop:"100%"}],[{color:"#006590",colorStop:"0%"},{color:"#004068",colorStop:"100%"}],[{color:"#007DB4",colorStop:"0%"},{color:"#00558B",colorStop:"100%"}],[{color:"#0D97D4",colorStop:"0%"},{color:"#057FC7",colorStop:"100%"}],[{color:"#00ADFE",colorStop:"0%"},{color:"#008BE9",colorStop:"100%"}],[{color:"#14B8FE",colorStop:"0%"},{color:"#0798EB",colorStop:"100%"}],[{color:"#53CBFF",colorStop:"0%"},{color:"#35AFEB",colorStop:"100%"}],[{color:"#86DAFF",colorStop:"0%"},{color:"#64C0EC",colorStop:"100%"}],[{color:"#ACE5FF",colorStop:"0%"},{color:"#8DCEED",colorStop:"100%"}],[{color:"#C4ECFF",colorStop:"0%"}],[{color:"#A3D1E6",colorStop:"100%"}]],borderColors:["#005277","#006590","#007DB4","#0D97D4","#00ADFE","#14B8FE","#53CBFF","#86DAFF","#ACE5FF","#C4ECFF"]},greenMetro:["#496612","#597B15","#709A1B","#87B62A","#9AD926","#A6DC37","#BCE654","#C8E780","#D5EFA5","#E2F3BE"],greenGradient:{seriesColors:[[{color:"#5C7F16",colorStop:"0%"},{color:"#384C08",colorStop:"100%"}],[{color:"#6A9319",colorStop:"0%"},{color:"#486009",colorStop:"100%"}],[{color:"#739D1C",colorStop:"0%"},{color:"#57760B",colorStop:"100%"}],[{color:"#90B546",colorStop:"0%"},{color:"#6E9215",colorStop:"100%"}],[{color:"#9AD826",colorStop:"0%"},{color:"#75A010",colorStop:"100%"}],[{color:"#A5DB36",colorStop:"0%"},{color:"#8EB91D",colorStop:"100%"}],[{color:"#BBE554",colorStop:"0%"},{color:"#A4C849",colorStop:"100%"}],[{color:"#C8E780",colorStop:"0%"},{color:"#B4D072",colorStop:"100%"}],[{color:"#D4EEA5",colorStop:"0%"},{color:"#BFD593",colorStop:"100%"}],[{color:"#E1F2BD",colorStop:"0%"}],[{color:"#C8D7A8",colorStop:"100%"}]],borderColors:["#5C7F16","#6A9319","#739D1C","#90B546","#9AD826","#A5DB36","#BBE554","#C8E780","#D4EEA5","#E1F2BD"]},sandleMetro:["#6C450C","#82520D","#A36812","#C07F1F","#E69719","#E89A2B","#EEB564","#F3CB93","#F7DEB4","#F9E6CA"],sandleGradient:{seriesColors:[[{color:"#7F602F",colorStop:"0%"},{color:"#512D04",colorStop:"100%"}],[{color:"#986827",colorStop:"0%"},{color:"#673803",colorStop:"100%"}],[{color:"#A16C1F",colorStop:"0%"},{color:"#8A4B05",colorStop:"100%"}],[{color:"#BF812A",colorStop:"0%"},{color:"#AD630D",colorStop:"100%"}],[{color:"#E49519",colorStop:"0%"},{color:"#B86607",colorStop:"100%"}],[{color:"#E7992A",colorStop:"0%"},{color:"#D7780D",colorStop:"100%"}],[{color:"#EDB463",colorStop:"0%"},{color:"#D98F31",colorStop:"100%"}],[{color:"#F2CA92",colorStop:"0%"},{color:"#DAAC6F",colorStop:"100%"}],[{color:"#F6DDB3",colorStop:"0%"},{color:"#DABE8F",colorStop:"100%"}],[{color:"#F8E5C9",colorStop:"0%"}],[{color:"#DDBE92",colorStop:"100%"}]],borderColors:["#7F602F","#986827","#A16C1F","#BF812A","#E49519","#E7992A","#EDB463","#F2CA92","#F6DDB3","#F8E5C9"]}};ej.EjSvgRender.themes={flatlight:{highlightColor:"black",background:"transparent",legend:{font:{color:"#282828"},title:{font:{color:"#282828"}}},title:{font:{color:"#565656"},subTitle:{font:{color:"#565656"}}},primaryXAxis:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},labelBorder:{color:"#8E8E8E"},multiLevelLabelsColor:"#8E8E8E",multiLevelLabelsFontColor:"#282828",axisLine:{color:"#8E8E8E"},font:{color:"#282828"},title:{font:{color:"#282828"}},crosshairLabel:{rx:3,ry:3,border:{color:"#3D3D3D",width:1},fill:"#3D3D3D",font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},secondaryX:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},labelBorder:{color:"#8E8E8E"},multiLevelLabelsColor:"#8E8E8E",multiLevelLabelsFontColor:"#282828",axisLine:{color:"#8E8E8E"},font:{color:"#282828"},title:{font:{color:"#282828"}},crosshairLabel:{rx:3,ry:3,border:{color:"#3D3D3D",width:1},fill:"#3D3D3D",font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},primaryYAxis:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},labelBorder:{color:"#8E8E8E"},multiLevelLabelsColor:"#8E8E8E",multiLevelLabelsFontColor:"#282828",axisLine:{color:"#8E8E8E"},font:{color:"#282828"},title:{font:{color:"#282828"}},crosshairLabel:{rx:3,ry:3,border:{color:"#3D3D3D",borderWidth:1},fill:"#3D3D3D",font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},secondaryY:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},labelBorder:{color:"#8E8E8E"},multiLevelLabelsColor:"#8E8E8E",multiLevelLabelsFontColor:"#282828",axisLine:{color:"#8E8E8E"},font:{color:"#282828"},title:{font:{color:"#282828"}},crosshairLabel:{rx:3,ry:3,border:{color:"#3D3D3D",width:1},fill:"#3D3D3D",font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},commonSeriesOptions:{marker:{dataLabel:{font:{color:"#565656"}}},errorBar:{fill:"#000000",cap:{fill:"#000000"}},connectorLine:{color:"#565656"}},crosshair:{line:{width:1,color:"Black"}},seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.defaultMetro,colors:ej.EjSvgRender.seriesPalette.defaultMetro},flatdark:{highlightColor:"white",background:"#111111",legend:{font:{color:"#C9C9C9"},title:{font:{color:"#C9C9C9"}}},title:{font:{color:"#C9C9C9"},subTitle:{font:{color:"#C9C9C9"}}},primaryXAxis:{majorGridLines:{color:"#333333"},majorTickLines:{color:"#AAAAAA"},minorGridLines:{color:"#333333"},minorTickLines:{color:"#AAAAAA"},labelBorder:{color:"#AAAAAA"},multiLevelLabelsColor:"#AAAAAA",multiLevelLabelsFontColor:"#C9C9C9",axisLine:{color:"#AAAAAA"},font:{color:"#C9C9C9"},title:{font:{color:"#C9C9C9"}},crosshairLabel:{rx:0,ry:0,fill:"#B5B5B5",border:{color:"#B5B5B5",width:1},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#444444"}}},secondaryX:{majorGridLines:{color:"#333333"},majorTickLines:{color:"#AAAAAA"},minorGridLines:{color:"#333333"},minorTickLines:{color:"#AAAAAA"},labelBorder:{color:"#AAAAAA"},multiLevelLabelsColor:"#AAAAAA",multiLevelLabelsFontColor:"#C9C9C9",axisLine:{color:"#AAAAAA"},font:{color:"#C9C9C9"},title:{font:{color:"#C9C9C9"}},crosshairLabel:{rx:0,ry:0,fill:"#B5B5B5",border:{color:"#B5B5B5",width:1},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#444444"}}},primaryYAxis:{majorGridLines:{color:"#333333"},majorTickLines:{color:"#AAAAAA"},minorGridLines:{color:"#333333"},minorTickLines:{color:"#AAAAAA"},labelBorder:{color:"#AAAAAA"},multiLevelLabelsColor:"#AAAAAA",multiLevelLabelsFontColor:"#C9C9C9",axisLine:{color:"#AAAAAA"},font:{color:"#C9C9C9"},title:{font:{color:"#C9C9C9"}},crosshairLabel:{rx:0,ry:0,fill:"#B5B5B5",border:{color:"#B5B5B5",width:1},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#444444"}}},secondaryY:{majorGridLines:{color:"#333333"},majorTickLines:{color:"#AAAAAA"},minorGridLines:{color:"#333333"},minorTickLines:{color:"#AAAAAA"},labelBorder:{color:"#AAAAAA"},multiLevelLabelsColor:"#AAAAAA",multiLevelLabelsFontColor:"#C9C9C9",axisLine:{color:"#AAAAAA"},font:{color:"#C9C9C9"},title:{font:{color:"#C9C9C9"}},crosshairLabel:{rx:0,ry:0,fill:"#B5B5B5",border:{color:"#B5B5B5",width:1},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#444444"}}},commonSeriesOptions:{marker:{dataLabel:{font:{color:"#C9C9C9"}}},errorBar:{fill:"#ffffff",cap:{fill:"#ffffff"}},connectorLine:{color:"#C9C9C9"}},crosshair:{line:{width:1,color:"White"}},seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.defaultMetro,colors:ej.EjSvgRender.seriesPalette.defaultMetro},gradientlight:{highlightColor:"black",background:"transparent",legend:{font:{color:"#282828"},title:{font:{color:"#282828"}}},title:{font:{color:"#565656"},subTitle:{font:{color:"#565656"}}},primaryXAxis:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},labelBorder:{color:"#8E8E8E"},multiLevelLabelsColor:"#8E8E8E",multiLevelLabelsFontColor:"#282828",axisLine:{color:"#8E8E8E"},font:{color:"#282828"},title:{font:{color:"#282828"}},crosshairLabel:{rx:3,ry:3,fill:"#3D3D3D",border:{color:"#3D3D3D",width:2},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},secondaryX:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},labelBorder:{color:"#8E8E8E"},multiLevelLabelsColor:"#8E8E8E",multiLevelLabelsFontColor:"#282828",axisLine:{color:"#8E8E8E"},font:{color:"#282828"},title:{font:{color:"#282828"}},crosshairLabel:{rx:3,ry:3,fill:"#3D3D3D",border:{color:"#3D3D3D",width:2},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},primaryYAxis:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},labelBorder:{color:"#8E8E8E"},multiLevelLabelsColor:"#8E8E8E",multiLevelLabelsFontColor:"#282828",axisLine:{color:"#8E8E8E"},font:{color:"#282828"},title:{font:{color:"#282828"}},crosshairLabel:{rx:3,ry:3,fill:"#3D3D3D",border:{color:"#3D3D3D",width:2},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},secondaryY:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},labelBorder:{color:"#8E8E8E"},multiLevelLabelsColor:"#8E8E8E",multiLevelLabelsFontColor:"#282828",axisLine:{color:"#8E8E8E"},font:{color:"#282828"},title:{font:{color:"#282828"}},crosshairLabel:{rx:3,ry:3,fill:"#3D3D3D",border:{color:"#3D3D3D",width:2},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},commonSeriesOptions:{marker:{dataLabel:{font:{color:"#565656"}}},errorBar:{fill:"#000000",cap:{fill:"#000000"}},connectorLine:{color:"#565656"}},crosshair:{line:{width:1,color:"Black"}},seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.defaultGradient.borderColors,colors:ej.EjSvgRender.seriesPalette.defaultGradient.seriesColors},gradientdark:{highlightColor:"white",background:"#111111",legend:{font:{color:"#C9C9C9"},title:{font:{color:"#C9C9C9"}}},title:{font:{color:"#C9C9C9"},subTitle:{font:{color:"#C9C9C9"}}},primaryXAxis:{majorGridLines:{color:"#333333"},majorTickLines:{color:"#AAAAAA"},minorGridLines:{color:"#333333"},minorTickLines:{color:"#AAAAAA"},labelBorder:{color:"#AAAAAA"},multiLevelLabelsColor:"#AAAAAA",multiLevelLabelsFontColor:"#C9C9C9",axisLine:{color:"#AAAAAA"},font:{color:"#C9C9C9"},title:{font:{color:"#C9C9C9"}},crosshairLabel:{rx:3,ry:3,fill:"#B5B5B5",border:{color:"#B5B5B5",width:2},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#444444"}}},secondaryX:{majorGridLines:{color:"#333333"},majorTickLines:{color:"#AAAAAA"},minorGridLines:{color:"#333333"},minorTickLines:{color:"#AAAAAA"},labelBorder:{color:"#AAAAAA"},multiLevelLabelsColor:"#AAAAAA",multiLevelLabelsFontColor:"#C9C9C9",axisLine:{color:"#AAAAAA"},font:{color:"#C9C9C9"},title:{font:{color:"#C9C9C9"}},crosshairLabel:{rx:3,ry:3,fill:"#B5B5B5",border:{color:"#B5B5B5",width:2},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#444444"}}},primaryYAxis:{majorGridLines:{color:"#333333"},majorTickLines:{color:"#AAAAAA"},minorGridLines:{color:"#333333"},minorTickLines:{color:"#AAAAAA"},labelBorder:{color:"#AAAAAA"},multiLevelLabelsColor:"#AAAAAA",multiLevelLabelsFontColor:"#C9C9C9",axisLine:{color:"#AAAAAA"},font:{color:"#C9C9C9"},title:{font:{color:"#C9C9C9"}},crosshairLabel:{rx:3,ry:3,fill:"#B5B5B5",border:{color:"#B5B5B5",width:2},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#444444"}}},secondaryY:{majorGridLines:{color:"#333333"},majorTickLines:{color:"#AAAAAA"},minorGridLines:{color:"#333333"},minorTickLines:{color:"#AAAAAA"},labelBorder:{color:"#AAAAAA"},multiLevelLabelsColor:"#AAAAAA",multiLevelLabelsFontColor:"#C9C9C9",axisLine:{color:"#AAAAAA"},font:{color:"#C9C9C9"},title:{font:{color:"#C9C9C9"}},crosshairLabel:{rx:3,ry:3,fill:"#B5B5B5",border:{color:"#B5B5B5",width:2},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#444444"}}},commonSeriesOptions:{marker:{dataLabel:{font:{color:"#C9C9C9"}}},errorBar:{fill:"#ffffff",cap:{fill:"#ffffff"}},connectorLine:{color:"#C9C9C9"}},crosshair:{line:{width:1,color:"White"}},seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.defaultGradient.borderColors,colors:ej.EjSvgRender.seriesPalette.defaultGradient.seriesColors},highcontrast01:{highlightColor:"white",background:"#111111",legend:{font:{color:"#ffffff"},title:{font:{color:"#ffffff"}}},title:{font:{color:"#ffffff"},subTitle:{font:{color:"#ffffff"}}},primaryXAxis:{majorGridLines:{color:"#333333"},majorTickLines:{color:"#AAAAAA"},minorGridLines:{color:"#333333"},minorTickLines:{color:"#AAAAAA"},labelBorder:{color:"#AAAAAA"},multiLevelLabelsColor:"#AAAAAA",multiLevelLabelsFontColor:"#ffffff",axisLine:{color:"#AAAAAA"},font:{color:"#ffffff"},title:{font:{color:"#ffffff"}},crosshairLabel:{rx:3,ry:3,fill:"#B5B5B5",border:{color:"#B5B5B5",width:2},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#444444"}}},secondaryX:{majorGridLines:{color:"#333333"},majorTickLines:{color:"#AAAAAA"},minorGridLines:{color:"#333333"},minorTickLines:{color:"#AAAAAA"},axisLine:{color:"#AAAAAA"},font:{color:"#ffffff"},title:{font:{color:"#ffffff"}},crosshairLabel:{rx:3,ry:3,fill:"#B5B5B5",border:{color:"#B5B5B5",width:2},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#444444"}}},primaryYAxis:{majorGridLines:{color:"#333333"},majorTickLines:{color:"#AAAAAA"},minorGridLines:{color:"#333333"},minorTickLines:{color:"#AAAAAA"},labelBorder:{color:"#AAAAAA"},multiLevelLabelsColor:"#AAAAAA",multiLevelLabelsFontColor:"#ffffff",axisLine:{color:"#AAAAAA"},font:{color:"#ffffff"},title:{font:{color:"#ffffff"}},crosshairLabel:{rx:3,ry:3,fill:"#B5B5B5",border:{color:"#B5B5B5",width:2},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#444444"}}},secondaryY:{majorGridLines:{color:"#333333"},majorTickLines:{color:"#AAAAAA"},minorGridLines:{color:"#333333"},minorTickLines:{color:"#AAAAAA"},axisLine:{color:"#AAAAAA"},font:{color:"#ffffff"},title:{font:{color:"#ffffff"}},crosshairLabel:{rx:3,ry:3,fill:"#B5B5B5",border:{color:"#B5B5B5",width:2},font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#444444"}}},commonSeriesOptions:{marker:{dataLabel:{font:{color:"#ffffff"}}},errorBar:{fill:"#ffffff",cap:{fill:"#ffffff"}},connectorLine:{color:"#C9C9C9"}},crosshair:{line:{width:1,color:"White"}},seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.defaultHighContrast,colors:ej.EjSvgRender.seriesPalette.defaultHighContrast},material:{highlightColor:"black",background:"transparent",legend:{font:{color:"#333333"},title:{font:{color:"#333333"}}},title:{font:{color:"#333333"},subTitle:{font:{color:"#333333"}}},primaryXAxis:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},labelBorder:{color:"#8E8E8E"},multiLevelLabelsColor:"#8E8E8E",multiLevelLabelsFontColor:"#333333",axisLine:{color:"#8E8E8E"},font:{color:"#333333"},title:{font:{color:"#333333"}},crosshairLabel:{rx:0,ry:0,border:{color:"#3D3D3D",width:1},fill:"#3D3D3D",font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},secondaryX:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},axisLine:{color:"#8E8E8E"},font:{color:"#333333"},title:{font:{color:"#333333"}},crosshairLabel:{rx:0,ry:0,border:{color:"#3D3D3D",width:1},fill:"#3D3D3D",font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},primaryYAxis:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},labelBorder:{color:"#8E8E8E"},multiLevelLabelsColor:"#8E8E8E",multiLevelLabelsFontColor:"#333333",axisLine:{color:"#8E8E8E"},font:{color:"#333333"},title:{font:{color:"#333333"}},crosshairLabel:{rx:0,ry:0,border:{color:"#3D3D3D",borderWidth:1},fill:"#3D3D3D",font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},secondaryY:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},axisLine:{color:"#8E8E8E"},font:{color:"#333333"},title:{font:{color:"#333333"}},crosshairLabel:{rx:0,ry:0,border:{color:"#3D3D3D",width:1},fill:"#3D3D3D",font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},commonSeriesOptions:{marker:{dataLabel:{font:{color:"#333333"}}},errorBar:{fill:"#000000",cap:{fill:"#000000"}},connectorLine:{color:"#565656"}},crosshair:{line:{width:1,color:"Black"}},seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.defaultMaterial,colors:ej.EjSvgRender.seriesPalette.defaultMaterial},office365:{highlightColor:"black",background:"transparent",legend:{font:{color:"#333333"},title:{font:{color:"#333333"}}},title:{font:{color:"#333333"},subTitle:{font:{color:"#333333"}}},primaryXAxis:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},labelBorder:{color:"#8E8E8E"},multiLevelLabelsColor:"#8E8E8E",multiLevelLabelsFontColor:"#333333",axisLine:{color:"#8E8E8E"},font:{color:"#333333"},title:{font:{color:"#333333"}},crosshairLabel:{rx:0,ry:0,border:{color:"#3D3D3D",width:1},fill:"#3D3D3D",font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},secondaryX:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},axisLine:{color:"#8E8E8E"},font:{color:"#333333"},title:{font:{color:"#333333"}},crosshairLabel:{rx:0,ry:0,border:{color:"#3D3D3D",width:1},fill:"#3D3D3D",font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},primaryYAxis:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},labelBorder:{color:"#8E8E8E"},multiLevelLabelsColor:"#8E8E8E",multiLevelLabelsFontColor:"#333333",axisLine:{color:"#8E8E8E"},font:{color:"#333333"},title:{font:{color:"#333333"}},crosshairLabel:{rx:0,ry:0,border:{color:"#3D3D3D",borderWidth:1},fill:"#3D3D3D",font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},secondaryY:{majorGridLines:{color:"#DFDFDF"},majorTickLines:{color:"#8E8E8E"},minorGridLines:{color:"#DFDFDF"},minorTickLines:{color:"#8E8E8E"},axisLine:{color:"#8E8E8E"},font:{color:"#333333"},title:{font:{color:"#333333"}},crosshairLabel:{rx:0,ry:0,border:{color:"#3D3D3D",width:1},fill:"#3D3D3D",font:{fontFamily:"Segoe UI",fontStyle:"Normal",size:"13px",fontWeight:"regular",opacity:1,color:"#DBDBDB"}}},commonSeriesOptions:{marker:{dataLabel:{font:{color:"#333333"}}},errorBar:{fill:"#000000",cap:{fill:"#000000"}},connectorLine:{color:"#565656"}},crosshair:{line:{width:1,color:"Black"}},seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.defaultOffice,colors:ej.EjSvgRender.seriesPalette.defaultOffice}};n.extend(ej.EjSvgRender.themes,{azure:{seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.blueMetro,colors:ej.EjSvgRender.seriesPalette.blueMetro},azuredark:{seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.blueMetro,colors:ej.EjSvgRender.seriesPalette.blueMetro},"gradient-azure":{seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.blueGradient.borderColors,colors:ej.EjSvgRender.seriesPalette.blueGradient.seriesColors},"gradient-azuredark":{seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.blueGradient.borderColors,colors:ej.EjSvgRender.seriesPalette.blueGradient.seriesColors},lime:{seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.greenMetro,colors:ej.EjSvgRender.seriesPalette.greenMetro},limedark:{seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.greenMetro,colors:ej.EjSvgRender.seriesPalette.greenMetro},"gradient-lime":{seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.greenGradient.borderColors,colors:ej.EjSvgRender.seriesPalette.greenGradient.seriesColors},"gradient-limedark":{seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.greenGradient.borderColors,colors:ej.EjSvgRender.seriesPalette.greenGradient.seriesColors},saffron:{seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.sandleMetro,colors:ej.EjSvgRender.seriesPalette.sandleMetro},saffrondark:{seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.sandleMetro,colors:ej.EjSvgRender.seriesPalette.sandleMetro},"gradient-saffron":{seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.sandleGradient.borderColors,colors:ej.EjSvgRender.seriesPalette.sandleGradient.seriesColors},"gradient-saffrondark":{seriesBorderDefaultColors:ej.EjSvgRender.seriesPalette.sandleGradient.borderColors,colors:ej.EjSvgRender.seriesPalette.sandleGradient.seriesColors}})}(jQuery)});
