﻿using Azure;
using Newtonsoft.Json;
using static System.Net.Mime.MediaTypeNames;
using Syncfusion.ExcelExport;
using System.Data;
using Syncfusion.XlsIO;
using static System.Runtime.InteropServices.JavaScript.JSType;
using Microsoft.JSInterop;
using Admin.Components.Shared;
using Microsoft.AspNetCore.Components.Web;
using Admin.Data.Model;
using C1.C1Report;
using System.Diagnostics;
using Admin.Reporting;

namespace Admin.Components.Pages
{
    public partial class Reports
    {
        private bool appointmentsCountPerYearReportVisible = false;
        private List<AppointmentYearData> appointmentsData = new();

        #region  Export Contacts
        protected void ExportTenantsStatisticsExcelBtnClick(MouseEventArgs e)
        {
            try
            {
                using (ExcelEngine excelEngine = new ExcelEngine())
                {
                    IApplication application = excelEngine.Excel;
                    application.DefaultVersion = ExcelVersion.Excel2016;

                    //Create a new workbook
                    IWorkbook workbook = application.Workbooks.Create(1);
                    IWorksheet sheet = workbook.Worksheets[0];

                    //Create datatable from the dataset
                    DataTable dataTable;
                    dataTable = reportsFactory.GetTenantsStatisticsExcel();

                    //Import data from the data table with column header, at first row and first column, 
                    //and by its column type.
                    sheet.ImportDataTable(dataTable, true, 1, 1, true);

                    //Creating Excel table or list object and apply style to the table
                    IListObject table = sheet.ListObjects.Create("ΣτατιστικάTenants", sheet.UsedRange);

                    table.BuiltInTableStyle = TableBuiltInStyles.TableStyleMedium13;

                    //Autofit the columns
                    sheet.UsedRange.AutofitColumns();


                    workbook.Version = ExcelVersion.Xlsx;

                    //Save the document as a stream and retrun the stream.
                    using (MemoryStream outputStream = new MemoryStream())
                    {
                        //Save the created Excel document to MemoryStream.
                        workbook.SaveAs(outputStream);

                        js.InvokeAsync<object>("saveAsFile", "TenantsStatistics.xlsx", Convert.ToBase64String(outputStream.ToArray()));
                    }
                }
            }
            catch (Exception ex)
            {
                new ExceptionHandler("MentalView.Admin").LogException(ex);
                new ErrorNotifier(dialogService).ShowError(ex.Message, "");
            }
        }
        #endregion


        #region  AppointmentsCountPerYear

        protected async Task PreviewAppointmentsCountPerYearBtnClick(MouseEventArgs e)
        {
            try
            {
                DataTable dataTable = reportsFactory.GetAppointmentsCountPerYear();
                appointmentsData = dataTable.AsEnumerable()
                    .Select(row => new AppointmentYearData
                    {
                        Year = row.Field<int>("AppointmentYear"),
                        CurrentYearAppointments = row.Field<int>("CurrentYearAppointments"),
                        CumulativeAppointments = row.Field<int>("CumulativeAppointments")
                    })
                    .ToList();

                this.appointmentsCountPerYearReportVisible = true;
                this.StateHasChanged();
            }
            catch (Exception ex)
            {
                new ExceptionHandler("MentalView.Admin").LogException(ex);
                new ErrorNotifier(dialogService).ShowError(ex.Message, "");
            }
        }
        #endregion
    }
}
