/*!
*  filename: ej.daterangepicker.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.globalize.min","./../common/ej.core.min","./../common/ej.scroller.min","./ej.datepicker.min","./ej.timepicker.min"],n):n()})(function(){"use strict";var n=this&&this.__extends||function(n,t){function r(){this.constructor=n}for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);r.prototype=t.prototype;n.prototype=new r},t=function(t){function i(n){return t.call(this),this._rootCSS="e-daterangepicker",this._isApplied=!0,this._setFirst=!1,this.PluginName="ejDateRangePicker",this.id="myDateRange",this._addToPersist=["value"],this.type="editor",this.angular={require:["?ngModel","^?form","^?ngModelOptions"],requireFormatters:!0},this._requiresID=!0,this.model=null,this.validTags=["input"],this.defaults={height:"",width:"",isResponsive:!1,value:"",cssClass:"",enabled:!0,startDate:null,endDate:null,enableTimePicker:!1,backwardSelection:!1,minDate:new Date("01/01/1900"),maxDate:new Date("12/31/2099"),ranges:null,locale:"en-US",separator:"-",watermarkText:"Select Range",dateFormat:"",timeFormat:"",showPopupButton:!0,showRoundedCorner:!1,allowEdit:!0,enablePersistence:!1,create:null,change:null,beforeClose:null,beforeOpen:null,close:null,open:null,hover:null,click:null,clear:null,destroy:null,select:null,htmlAttributes:{}},this._isIE8=ej.browserInfo().name=="msie"&&ej.browserInfo().version=="8.0"?!0:!1,this._prevValue=null,this._validState=null,i.prototype.observables=["value"],n&&(n.jquery||(n=$("#"+n)),n.length)?$(n).ejDateRangePicker().data(this.PluginName):void 0}return n(i,t),i.prototype.setModel=function(n,t){this.setModel(n,t)},i.prototype.option=function(n,t){this.option(n,t)},i.prototype._setModel=function(n){var t,i;for(t in n)switch(t){case"allowEdit":if(!n[t]){this.element.attr("readonly","readonly");this.element.on("mousedown",$.proxy(this._showDatePopUp,this));this.element.off("blur",$.proxy(this._onMainFocusOut,this))}break;case"startDate":i=this._validateValues(n[t],"left");i==!1?n[t]=null:(this._startEndValidation()||this.model.endDate==null||(this._resetValues(),this.model.endDate=null),this._updateValues(),this._selectedStartDate=this.model.startDate,n[t]=this.model.startDate);break;case"endDate":i=this._validateValues(n[t],"right");i==!1?n[t]=null:(this._startEndValidation()||(this._rightDP.element.parents(".e-datewidget").addClass("e-val-error"),this.model.endDate=null),this._updateValues(),n[t]=this.model.endDate);break;case"minDate":this._leftDP&&this._leftDP.option(t,n[t]);this._rightDP&&this._rightDP.option(t,n[t]);this._updateInput();break;case"maxDate":this._leftDP&&this._leftDP.option(t,n[t]);this._rightDP&&this._rightDP.option(t,n[t]);this._updateInput();break;case"enableTimePicker":this.model.enableTimePicker=n[t];n[t]?this._renderTimePicker():this._removeTimePicker();this._scrollerObj&&(this._scrollerObj.model.height=this.datePopup.height(),this._scrollerObj.refresh());break;case"backwardSelection":this.model.backwardSelection=n[t];this._mainValue();break;case"locale":this._setCulture(n[t]);n[t]=this.model.locale;break;case"separator":this.model.separator=n[t];this._mainValue();break;case"dateFormat":this._leftDP&&this._leftDP.option(t,n[t]);this._rightDP&&this._rightDP.option(t,n[t]);this.model.dateFormat=n[t];this._getDateTimeFormat();this._updateInput();break;case"timeFormat":this._leftTP&&this._leftTP.option(t,n[t]);this._rightTP&&this._rightTP.option(t,n[t]);this.model.timeFormat=n[t];this._getDateTimeFormat();this._updateInput();break;case"watermarkText":ej.isNullOrUndefined(this._options)&&(this._options={});this._options.watermarkText=this.model.watermarkText=n[t];this._localizedLabels.watermarkText=this.model.watermarkText;this._setWaterMark();break;case"cssClass":this._changeSkin(n[t]);break;case"showRoundedCorner":this._setRoundedCorner(n[t]);break;case"showPopupButton":this._renderDateIcon(n[t]);break;case"value":this.element.val(n[t]);this._onMainFocusOut();n[t]=this.model.value;break;case"height":this.wrapper.height(n[t]);break;case"width":this.wrapper.width(n[t]);break;case"enabled":n[t]?this.enable():this.disable();break;case"htmlAttributes":this._addAttr(n[t])}},i.prototype._init=function(n){this._cloneElement=this.element.clone();this._options=n;this._flagevents=!1;this._id=this.element.attr("id");this._isSupport=document.createElement("input").placeholder==undefined?!1:!0;this._startEndValidation()||(this.model.endDate=null);this.element.val()==""&&this.model.value&&this.element.val(this.model.value);this._getDateTimeFormat();this.element.val()?(this._setInitValue(),this._updateValues()):this.model.startDate!=null&&this.model.endDate!=null&&this._startEndValidation()&&(this._updateValues(),this._mainValue());this._prevStartDate=this.model.startDate;this._prevEndDate=this.model.endDate;this._createWrapper();this.renderpopup();this._setLocalizedText();this._validState==!1&&this.element.val()&&this.wrapper.addClass("e-error");this._popupOpen=!1;this._isPopScroll=!1;this.model.enabled||this.disable();this._wireEvents();this.model.startDate&&!this._selectedStartDate&&(this._selectedStartDate=this.model.startDate);this.model.endDate&&!this._selectedEndDate&&(this._selectedEndDate=this.model.endDate)},i.prototype._setInitValue=function(){var n=this.element.val().split(this.model.separator),t=ej.parseDate(n[0],this._dateTimeFormat,this.model.locale),i=ej.parseDate(n[1],this._dateTimeFormat,this.model.locale);t&&i&&(this.model.startDate=t,this.model.endDate=i);(this.model.startDate<new Date(this.model.minDate)||this.model.startDate>new Date(this.model.maxDate))&&(this.model.startDate=new Date(this.model.minDate));(this.model.endDate>new Date(this.model.maxDate)||this.model.endDate<new Date(this.model.minDate))&&(this.model.endDate=new Date(this.model.maxDate))},i.prototype._getNextMonth=function(n){var r=n,i,u,t;return(r instanceof Date)?(i=r.getMonth(),i==11?(u=r.getFullYear()+1,i=-1):u=r.getFullYear(),t=new Date,t=new Date(t.setFullYear(u)),t=new Date(t.setDate(1)),t=new Date(t.setMonth(i+1)),t>new Date(this.model.maxDate)&&(t=new Date(t.setMonth(i))),new Date(this.model.minDate).getFullYear()==new Date(this.model.maxDate).getFullYear()&&new Date(this.model.minDate).getMonth()==new Date(this.model.maxDate).getMonth()&&(t=new Date(t.setDate(new Date(this.model.minDate).getDate()))),t):new Date},i.prototype._getDateTimeFormat=function(){var n=ej.preferredCulture(this.model.locale).calendars.standard.patterns;this.model.dateFormat||(this.model.dateFormat=n.d);this.model.enableTimePicker?(this.model.timeFormat||(this.model.timeFormat=n.t),this._dateTimeFormat=this.model.dateFormat+" "+this.model.timeFormat):this._dateTimeFormat=this.model.dateFormat},i.prototype._setValues=function(){this._leftDP.option("value",this.model.startDate);this._rightDP.option("value",this.model.endDate||null);this.model.startDate&&this._setStartDate(this.model.startDate,$('.current-month[data-date="'+this.model.startDate.toDateString()+'"]'),!0);this.model.endDate&&this._setEndDate(this.model.endDate,$('.current-month[data-date="'+this.model.endDate.toDateString()+'"]'),!0);this._rangeRefresh(this._setArgs(this._leftDP.popup));this._rangeRefresh(this._setArgs(this._rightDP.popup));this._rightDP.element.parents(".e-datewidget").removeClass("e-error");this.model.enableTimePicker&&(this._leftTP.option("value",this.model.startDate),this._rightTP.option("value",this.model.endDate));this._updateRanges("left");this._updateRanges("right")},i.prototype._customSet=function(){this._selectedStartDate=this.model.startDate;this._selectedEndDate=this.model.endDate;this._resetValues();this.popup&&this.datePopup&&this._setValues();this._popupOpen||this._mainValue();this._refreshMinMax();this._setWaterMark()},i.prototype._setCulture=function(n){n=ej.preferredCulture(n).name;this.model.locale=n;this._setOption("locale",n);this._localizedLabels=this._getLocalizedLabels();this._setLocalizedText();this._updateInput()},i.prototype._setRoundedCorner=function(n){n?(this._input_innerWrapper.addClass("e-corner"),this.popup&&this.popup.addClass("e-corner")):(this._input_innerWrapper.removeClass("e-corner"),this.popup&&this.popup.removeClass("e-corner"));this._setOption("showRoundedCorner",n)},i.prototype._renderDateIcon=function(n){n?(this.dateRangeIcon=ej.buildTag("span.e-select#"+this.id+"-img","",{},this._isIE8?{unselectable:"on"}:{}).append(ej.buildTag("span.e-icon e-calendar","",{},{"aria-label":"Select"}).attr(this._isIE8?{unselectable:"on"}:{})).insertAfter(this.element),this._input_innerWrapper.addClass("e-padding"),this._on(this.dateRangeIcon,"mousedown",this._showDatePopUp),this._off(this.dateRangeIcon,"click",this._showDatePopUp)):(this.dateRangeIcon&&this.dateRangeIcon.remove(),this._input_innerWrapper.removeClass("e-padding"),this._off(this.dateRangeIcon,"click",this._showDatePopUp))},i.prototype._validateValues=function(n,t,i){var e=t=="right"?this._rightDP:this._leftDP,f=t=="right"?this._rightTP:this._leftTP,r,u;if(i){if(f&&f._preVal&&f._preVal==n)return!0}else if(t=="left"&&e&&this._selectedStartDate&&this._selectedStartDate.getFullYear()==new Date(n).getFullYear()&&this._selectedStartDate.getMonth()==new Date(n).getMonth()&&this._selectedStartDate.getDate()==new Date(n).getDate()&&this._selectedStartDate.getHours()==new Date(n).getHours()&&this._selectedStartDate.getMinutes()==new Date(n).getMinutes()||t=="right"&&e&&this._selectedEndDate&&this._selectedEndDate.getFullYear()==new Date(n).getFullYear()&&this._selectedEndDate.getMonth()==new Date(n).getMonth()&&this._selectedEndDate.getDate()==new Date(n).getDate()&&this._selectedEndDate.getHours()==new Date(n).getHours()&&this._selectedEndDate.getMinutes()==new Date(n).getMinutes())return!0;if(r=n,r!=null&&typeof r=="string")t=="left"?(this.model.enableTimePicker?i?(u=ej.format(this._leftDP.model.value,this.model.dateFormat,this.model.locale),this.model.startDate=ej.parseDate(u+" "+n,this.model.dateFormat+" "+this.model.timeFormat,this.model.locale)||null):this.model.startDate=ej.parseDate(n+" "+this._leftTP.model.value,this.model.dateFormat+" "+this.model.timeFormat,this.model.locale)||null:this.model.startDate=ej.parseDate(n,this.model.dateFormat,this.model.locale)||null,this.model.startDate=this.model.startDate!=null?this.model.startDate:new Date(r)):(this.model.enableTimePicker?i?(u=ej.format(this._rightDP.model.value,this.model.dateFormat,this.model.locale),this.model.endDate=ej.parseDate(u+" "+n,this.model.dateFormat+" "+this.model.timeFormat,this.model.locale)||null):this.model.endDate=ej.parseDate(n+" "+this._rightTP.model.value,this.model.dateFormat+" "+this.model.timeFormat,this.model.locale)||null:this.model.endDate=ej.parseDate(n,this.model.dateFormat,this.model.locale)||null,this.model.endDate=this.model.endDate!=null?this.model.endDate:new Date(r));else if(n instanceof Date){if(t=="left"){if(this.model.startDate=n,isNaN(n.getDate()))return!1;typeof this._formatter(this.model.startDate,this.model.locale)!="string"&&(this.model.startDate=ej.parseDate(this._formatter(this.model.startDate,this.model.locale),this.model.dateFormat))}else if(t=="right"){if(this.model.endDate=n,isNaN(n.getDate()))return!1;typeof this._formatter(this.model.endDate,this.model.locale)!="string"&&(this.model.endDate=ej.parseDate(this._formatter(this.model.endDate,this.model.locale),this.model.dateFormat))}}else return!1;return!0},i.prototype._formatter=function(n,t){var i=this._checkFormat(t);return ej.format(n,i,this.model.locale)},i.prototype._checkFormat=function(n){var t=this,i=this._regExp();return n.replace(i,function(n){return n==="/"?ej.preferredCulture(t.model.locale).calendars.standard["/"]!=="/"?"'/'":n:n})},i.prototype._regExp=function(){return/\/dddd|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yy|HH|H|hh|h|mm|m|fff|ff|f|tt|ss|s|zzz|zz|z|gg|g|"[^"]*"|'[^']*'|[/]/g},i.prototype._setArgs=function(n){var t=new Date($(n.find(".current-month")[0]).attr("data-date"));return this.args={},this.args.element=n,this.args.month=t.getMonth(),this.args.year=t.getFullYear(),this.args},i.prototype._changeSkin=function(n){this.wrapper.removeClass(this.model.cssClass).addClass(n);this.popup.removeClass(this.model.cssClass).addClass(n);this._setOption("cssClass",n)},i.prototype._renderDatePicker=function(){this.calendar_left=ej.buildTag("input.leftDate_wrapper#"+this.element[0].id+"leftDate_wrapper","",{},{type:"text"});this._leftDiv.append(this.calendar_left);this.calendar_right=ej.buildTag("input.rightDate_wrapper#"+this.element[0].id+"rightDate_wrapper","",{},{type:"text"});this._rightDiv.append(this.calendar_right);var n=this,t={displayInline:!0,showFooter:!1,watermarkText:"",enableStrictMode:!0,locale:this.model.locale,dateFormat:this.model.dateFormat,_month_Loaded:function(t){n._previousNextHandler(t)},focusOut:function(t){t.prevDate!=t.value&&(n._isApplied=!1)},enablePersistence:this.model.enablePersistence,minDate:this.model.minDate,maxDate:this.model.maxDate};this.calendar_left.ejDatePicker(t);this.calendar_left.ejDatePicker("option","layoutChange",function(){n._refreshEvents("left");n._updateRanges("left");n.popup.hasClass("e-daterange-responsive")||n._scrollerObj&&(n._scrollerObj.model.height=n.datePopup.height(),n._scrollerObj.refresh())});this._leftDP=this.calendar_left.data("ejDatePicker");this._leftDP.option("showPopupButton",!1);this._leftDP._getInternalEvents=!0;this._leftDP._DRPdisableFade=!0;this._leftDP.popup.css({position:"static",visibility:"inherit"});this.calendar_right.ejDatePicker(t);this.calendar_right.ejDatePicker("option","layoutChange",function(){n._refreshEvents("right");n._updateRanges("right");n.popup.hasClass("e-daterange-responsive")||n._scrollerObj&&(n._scrollerObj.model.height=n.datePopup.height(),n._scrollerObj.refresh())});this.calendar_left.ejDatePicker("option","_setAriaAttribute",function(t){$(n.element).attr("aria-activedescendant",t.attr("id"))});this.calendar_right.ejDatePicker("option","_setAriaAttribute",function(t){$(n.element).attr("aria-activedescendant",t.attr("id"))});this._rightDP=this.calendar_right.data("ejDatePicker");this._rightDP.option("showPopupButton",!1);this._rightDP._getInternalEvents=!0;this._rightDP._DRPdisableFade=!0;this._rightDP.popup.css({position:"static",visibility:"inherit"});this._on($(this._leftDP.sfCalendar.find("table .e-datepicker-months td")),"click",$.proxy(this._previousNextHandler,this));this._on(this._leftDP.element,"keydown",this._onKeyDown);this._on(this._rightDP.element,"keydown",this._onKeyDown)},i.prototype._renderPopup=function(){this.popup||(this.popup=ej.buildTag("div.e-daterangepicker-popup e-popup e-widget e-box"+this.model.cssClass+"#"+this.element[0].id+"_popup").css("display","none"),$("body").append(this.popup));this.datePopup=ej.buildTag("div.e-datepickers-popup");this.popup.append(this.datePopup);this._leftDiv=ej.buildTag("div.e-left-datepicker");this._rightDiv=ej.buildTag("div.e-right-datepicker");this.datePopup.append(this._leftDiv);this.datePopup.append(this._rightDiv);this._renderDatePicker();this.model.ranges&&!this._customRangePicker&&this._renderRanges();this.model.enableTimePicker&&this._renderTimePicker();this._renderButton();this._bindDateButton();this._refreshEvents("left");this._refreshEvents("right");this._updateRanges("left");this._updateRanges("right");this._setRoundedCorner(this.model.showRoundedCorner);this._addAttr(this.model.htmlAttributes);this._on(this.popup.find(".leftDate_wrapper.e-datepicker.e-js.e-input"),"blur",this._onPopupFocusOut);this._on(this.popup.find(".rightDate_wrapper.e-datepicker.e-js.e-input"),"blur",this._onPopupFocusOut);this._on(this.popup.find(".leftTime.e-timepicker.e-js.e-input"),"blur",this._onPopupFocusOut);this._on(this.popup.find(".rightTime.e-timepicker.e-js.e-input"),"blur",this._onPopupFocusOut);this.popup.on("mouseenter touchstart",$.proxy(function(){this._isPopScroll=!0},this));this.popup.on("mouseleave touchend",$.proxy(function(){this._isPopScroll=!1},this));this._on($(window),"resize",this._resizePopup);this._wirePopupEvents()},i.prototype._createWrapper=function(){var n;this._localizedLabels=this._getLocalizedLabels();this.element.addClass("e-input").attr({"aria-atomic":"true","aria-live":"assertive",tabindex:"0",role:"combobox","aria-expanded":"false",name:this.element.attr("name")==undefined?this._id:this.element.attr("name")});this.wrapper=ej.buildTag("span.e-daterangewidget e-widget "+this.model.cssClass);ej.isTouchDevice()||this.wrapper.addClass("e-ntouch");this._input_innerWrapper=ej.buildTag("span.e-in-wrap e-box");this.wrapper.append(this._input_innerWrapper).insertBefore(this.element);this._input_innerWrapper.append(this.element);this._input_innerWrapper.addClass("e-padding");n=this;this.culture=ej.preferredCulture(this.model.locale);this._setRoundedCorner(this.model.showRoundedCorner);this._renderDateIcon(this.model.showPopupButton);this._isSupport||(this._hiddenInput=ej.buildTag("input.e-input e-placeholder ","",{},{type:"text"}).insertAfter(this.element),this._hiddenInput.val(this._localizedLabels.watermarkText),this._hiddenInput.css("display","block"),n=this,$(this._hiddenInput).focus(function(){n.element.focus()}));this.wrapper&&(this.model.width&&this.wrapper.width(this.model.width),this.model.height&&this.wrapper.height(this.model.height));this._setWaterMark()},i.prototype._updateOnRender=function(){this.model.enableTimePicker?this._rightTime&&(this._rightTP=this._rightTime.ejTimePicker("instance"),this._updateValues()):this.calendar_right&&(this._rightDP=this.calendar_right.ejDatePicker("instance"),this._updateValues())},i.prototype._updateRangesList=function(){$(".e-dateranges-ul").find(".rangeItem.e-active").removeClass("e-active");$(".e-dateranges-ul").find(".rangeItem.e-custompic").addClass("e-active")},i.prototype._updateValues=function(){this._updateRangesList();this._getDateTimeFormat();this._startEndValidation()||this.model.startDate!=null&&this.model.endDate!=null?(this._validateValues(this.model.startDate,"left"),this._validateValues(this.model.endDate,"right"),this.popup&&this.datePopup&&this._setValues(),this._mainValue()):(this._clearRanges(),this.element.val(""));this._refreshMinMax();this._setWaterMark()},i.prototype._startEndValidation=function(){if(this.model.startDate&&this.model.endDate){var t=this.model.startDate,n=this.model.endDate;return!(n&&t>n)}return!1},i.prototype._addAttr=function(n){var t=this;$.map(n,function(n,i){var r=i.toLowerCase();r=="class"?t.wrapper.addClass(n):r=="disabled"?t.disable():r=="readOnly"?t.model.readOnly=!0:r=="style"||r=="id"?t.wrapper.attr(i,n):ej.isValidAttr(t.element[0],i)?t.element.attr(i,n):t.wrapper.attr(i,n)})},i.prototype._renderButton=function(){var n;this._buttonDiv=ej.buildTag("div.e-drpbuttons");var t=ej.buildTag("div.e-drp-button e-drp-reset e-btn e-select e-flat").attr({tabindex:"0"}),i=ej.buildTag("div.e-drp-button e-drp-apply e-disable e-btn e-select e-flat").attr({tabindex:"0"}),r=ej.buildTag("div.e-drp-button e-drp-cancel e-btn e-select e-flat").attr({tabindex:"0"});this._buttonDiv.append(t);this._buttonDiv.append(i);this._buttonDiv.append(r);this.popup.append(this._buttonDiv);this._setLocalizedText();this._on($(this._buttonDiv.find("div.e-drp-reset")),"click",this.clearRanges);n=this;this._on($(this._buttonDiv.find("div.e-drp-apply")),"click",function(){n._buttonDiv.find(".e-drp-apply").hasClass("e-disable")||(this._isApplied=!0,n._isPopScroll=!1,n._updateInput(),n._showhidePopup(),n._buttonDiv.find(".e-drp-apply").addClass("e-disable"),this._prevStartDate=this.model.startDate,this._prevEndDate=this.model.endDate,this._trigger("change",{value:this.model.value,startDate:this.model.startDate,endDate:this.model.endDate}),this._trigger("select",{startDate:this.model.startDate,endDate:this.model.endDate,value:this.model.value}))});this._on($(this._buttonDiv.find("div.e-drp-cancel")),"click",this._cancelButton)},i.prototype._setLocalizedText=function(){ej.isNullOrUndefined(this._options)||(ej.isNullOrUndefined(this._options.buttonText)||$.extend(this._localizedLabels.ButtonText,this._options.buttonText),ej.isNullOrUndefined(this._options.watermarkText)||(this._localizedLabels.watermarkText=this._options.watermarkText));this.model.buttonText=this._localizedLabels.ButtonText;this._buttonDiv&&($(this._buttonDiv.find("div.e-drp-reset")).text(this.model.buttonText.reset),$(this._buttonDiv.find("div.e-drp-apply")).text(this.model.buttonText.apply),$(this._buttonDiv.find("div.e-drp-cancel")).text(this.model.buttonText.cancel));this._customRangePicker&&this._customRangePicker.find("ul li.e-custompic").length>0&&this._customRangePicker.find("ul li.e-custompic").text(this._localizedLabels.customPicker);this._setWaterMark()},i.prototype._renderRanges=function(){var f,i,t,n,r,u;if(this._renderRangesWrapper(),this.popup.append(this._customRangePicker),this._ranges_li="",f=this,this.model.ranges)for(i=0;i<this.model.ranges.length;i++)t=this.model.ranges[i],n=t.range,n.length===2&&(r=new Date(n[0]),u=new Date(n[1]),ej.isNullOrUndefined(r)&&(r=new Date(n[0])),ej.isNullOrUndefined(u)&&(u=new Date(n[1])),r<=u&&(f._ranges_li+="<li role='menuitem' title='"+t.label+"' class='rangeItem' data-e-range='"+JSON.stringify(n)+"' data-e-value='"+t.range+"'>"+t.label+"<\/li>"));this._ranges_li+="<li role='menuitem' class='rangeItem e-active e-custompic' data-e-range='customPicker'>"+this._localizedLabels.customPicker+"<\/li>";this.popup.find("div.e-custom-dateranges ul").append(this._ranges_li);this._on(this._customRangePicker.find("ul li.rangeItem"),"click",this._customSelection);this._customRangePicker.ejScroller({height:this.datePopup?this.datePopup.height():200,width:0,scrollerSize:15});f._scrollerObj=this._customRangePicker.ejScroller("instance")},i.prototype._removeTimePicker=function(){this._leftDiv.removeClass("e-left-timepicker");this._rightDiv.removeClass("e-right-timepicker");this._leftTP.destroy();this._rightTP.destroy();this._rightTP=null;this._leftTP=null;this._leftTime.remove();this._rightTime.remove();this._setOption("width","");this._setOption("width","100%");this._updateValues()},i.prototype._setOption=function(n,t){this._leftDP&&this._leftDP.option(n,t);this._rightDP&&this._rightDP.option(n,t);this.model.enableTimePicker&&(this._leftTP&&this._leftTP.option(n,t),this._rightTP&&this._rightTP.option(n,t))},i.prototype._renderTimePicker=function(){this.model.timeFormat==""&&(this.model.timeFormat=this.culture.calendar.patterns.t);this._leftTime=ej.buildTag("input.leftTime#"+this._id+"_lTime");this._leftDiv.append(this._leftTime);this._leftDiv.addClass("e-left-timepicker");var n=this,t={popupWidth:"115px",locale:this.model.locale,timeFormat:this.model.timeFormat,watermarkText:"",open:function(){this.popup.addClass("e-daterange-timepopup");this.model.open=null},focusOut:function(){n._isApplied=!1}};this._leftTime.ejTimePicker(t);this._leftTime.ejTimePicker({select:function(t){if(n._selectedStartDate){var i=t.value?t.value:t.model.value;n.model.startDate=n._selectedStartDate=new Date(n._selectedStartDate.toDateString()+" "+i);n._rightDP.element.val()!=""&&n._rightTP.element.val()!=""&&n._buttonDiv.find(".e-drp-apply").removeClass("e-disable");n._isApplied=!1;return}n._selectedEndDate=null;n._leftDP.option("value",n._selectedStartDate||(+new Date((new Date).setHours(0,0,0,0))>=+n.model.minDate&&+new Date((new Date).setHours(0,0,0,0))<=+n.model.maxDate?new Date((new Date).setHours(0,0,0,0)):n.model.minDate));n._selectedStartDate=n.model.startDate=n._selectedStartDate||(+new Date((new Date).setHours(0,0,0,0))>=+n.model.minDate&&+new Date((new Date).setHours(0,0,0,0))<=+n.model.maxDate?new Date((new Date).setHours(0,0,0,0)):n.model.minDate);n._selectedStartDate&&n._setStartDate(n.model.startDate,$('.current-month[data-date="'+n.model.startDate.toDateString()+'"]'),!0);n._rangeRefresh(n._setArgs(n._leftDP.popup));n.model.startDate&&n.model.endDate&&(n._updateRanges("left"),n._rightTP&&n.model.startDate.toLocaleDateString()==n.model.endDate.toLocaleDateString()&&n._buttonDiv.find(".e-drp-apply").removeClass("e-disable"))}});this._leftTP=this._leftTime.ejTimePicker("instance");this._rightTime=ej.buildTag("input.rightTime#"+this._id+"_rTime");this._rightDiv.append(this._rightTime);this._rightDiv.addClass("e-right-timepicker");this._rightTime.ejTimePicker(t);this._rightTime.ejTimePicker({select:function(t){var f,i,s,e,o;if(n._selectedEndDate){f=t.value?t.value:t.model.value;n.model.endDate=n._selectedEndDate=new Date(n._selectedEndDate.toDateString()+" "+f);n._leftDP.element.val()!=""&&n._leftTP.element.val()!=""&&n._buttonDiv.find(".e-drp-apply").removeClass("e-disable");n._isApplied=!1;return}i=new Date((new Date).toDateString()+" "+this.model.value);n._rightDP.option("value",n._selectedEndDate||(+new Date(i)>=+n.model.minDate&&+new Date(new Date(i))<=+n.model.maxDate?new Date(new Date(i)):n.model.minDate));n._selectedEndDate=n.model.endDate=n._selectedEndDate||(+new Date(new Date(i))>=+n.model.minDate&&+new Date(new Date(i))<=+n.model.maxDate?new Date(new Date(i)):n.model.minDate);var u=$($('.current-month[data-date="'+n.model.endDate.toDateString()+'"]')).parents(".e-left-datepicker").length>0?"left":"right",r=n._selectedEndDate,e=n.model.endDate.toDateString();n._selectedEndDate>=n._selectedStartDate?n._setEndDate(n.model.endDate,$('.current-month[data-date="'+n.model.endDate.toDateString()+'"]'),!0):r<n._selectedStartDate&&n.model.backwardSelection?(s=r,e=$($('.current-month[data-date="'+n.model.endDate.toDateString()+'"]')).attr("data-date"),n._updateDP(n,new Date(r),"rightDP"),n._rightDP._stopRefresh=!1,n._rightDP.element.parents(".e-datewidget").removeClass("e-error"),o=$(n.datePopup.find('.current-month[data-date="'+e+'"]')),n._selectedStartDate=n.model.startDate,n._selectedEndDate=new Date(r),n._setEndDate(n._selectedEndDate,o,!0),n._startDate.date=n._selectedStartDate,u=="left"?(n.model.startDate=n._selectedEndDate,n.model.endDate=n._selectedStartDate,n._updateDP(n,n._selectedStartDate,"rightDP"),n._updateDP(n,n._selectedEndDate,"leftDP")):u=="right"&&(n._updateDP(n,n.model.endDate,"leftDP"),n._updateDP(n,n.model.startDate,"rightDP"))):(n._selectedStartDate=r,n._selectedEndDate=null,n._endDate&&(n._endDate.date=null),n.popup.find(".in-range").removeClass("in-range"),n._leftDP.option("value",n._selectedStartDate||(+new Date(new Date(i))>=+n.model.minDate&&+new Date(new Date(i))<=+n.model.maxDate?new Date(new Date(i)):n.model.minDate)),n._leftTP.option("value",ej.format(n._selectedStartDate,n.model.timeFormat,n.model.locale)),u=="right"&&n._updateDP(n,n._selectedStartDate,"leftDP"),n._updateDP(n,null,"rightDP"),n._setStartDate(n._selectedStartDate,$('.current-month[data-date="'+n.model.endDate.toDateString()+'"]'),!0));n._updateRanges("left");n._updateRanges("right");n.model.startDate&&n.model.endDate&&(n._updateRanges("left"),n._buttonDiv.find(".e-drp-apply").removeClass("e-disable"))}});this._rightTP=this._rightTime.ejTimePicker("instance");this._on(this._leftTP.element,"keydown",this._onKeyDown);this._on(this._rightTP.element,"keydown",this._onKeyDown);this._setTimePickerPos()},i.prototype._updateDP=function(n,t,i){var r=n,u;i==="rightDP"?(u=r._rightDP,u.option("value",t),u.element.val(ej.format(t,r.model.dateFormat,r.model.locale)),r._rightTP.option("value",ej.format(t,r.model.timeFormat,r.model.locale))):(u=r._leftDP,u.option("value",t),u.element.val(ej.format(t,r.model.dateFormat,r.model.locale)),r._leftTP.option("value",ej.format(t,r.model.timeFormat,r.model.locale)))},i.prototype._setTimePickerPos=function(){$("#"+this._id+"_lTime_timewidget").css({position:"absolute",top:0,left:this._leftDP.popup.width()+this._leftDP.popup.position().left-this.popup.find($("#"+this._id+"_lTime_timewidget")).outerWidth()});$("#"+this._id+"_rTime_timewidget").css({position:"absolute",top:this.popup.hasClass("e-daterange-responsive")?this._rightDP.wrapper.position().top:0,left:this._rightDP.popup.width()+this._rightDP.popup.position().left-this.popup.find($("#"+this._id+"_rTime_timewidget")).outerWidth()})},i.prototype._updateInput=function(){if(!(this.model.startDate&&this.model.endDate)){this.model.value&&this.element.val(this.model.value);this._popupOpen&&this.popupHide();return}this._resetValues();this.wrapper.removeClass("e-error");this._mainValue();this._refreshMinMax()},i.prototype._removeWatermark=function(){this.element.val()==""||this._isSupport||this._hiddenInput.css("display","none")},i.prototype._mainValue=function(){var t,n;if((this.model.startDate<new Date(this.model.minDate)||this.model.startDate>new Date(this.model.maxDate))&&(this.model.startDate=new Date(this.model.minDate)),(this.model.endDate>new Date(this.model.maxDate)||this.model.endDate<new Date(this.model.minDate))&&(this.model.endDate=new Date(this.model.maxDate)),t=this.model.startDate,n=this.model.endDate,t>n&&n!=null&&this.model.backwardSelection)var u=ej.format(n,this.model.timeFormat,this.model.locale),f=ej.format(t,this.model.timeFormat,this.model.locale),i=ej.format(n,this.model.dateFormat,this.model.locale),r=ej.format(t,this.model.dateFormat,this.model.locale);else var u=ej.format(t,this.model.timeFormat,this.model.locale),f=ej.format(n,this.model.timeFormat,this.model.locale),i=ej.format(t,this.model.dateFormat,this.model.locale),r=ej.format(n,this.model.dateFormat,this.model.locale);this.model.enableTimePicker?(this.popup&&this.datePopup&&this._leftTP&&this._rightTP&&this._leftTP.model.value&&this._rightTP.model.value&&(u=this._leftTP.model.value,f=this._rightTP.model.value),i=ej.format(i+" "+u,this._dateTimeFormat,this.model.locale),r=ej.format(r+" "+f,this._dateTimeFormat,this.model.locale),this.model.startDate=ej.parseDate(i,this._dateTimeFormat,this.model.locale),this.model.endDate=ej.parseDate(r,this._dateTimeFormat,this.model.locale)):this.popup&&this.datePopup&&(this.model.startDate=ej.parseDate(i,this._leftDP.model.dateFormat,this.model.locale),this.model.endDate=ej.parseDate(r,this._rightDP.model.dateFormat,this.model.locale));i!=null&&r!=null?(this.model.value=i+" "+this.model.separator+" "+r,this.element.val(this.model.value),this._hiddenInput&&this._hiddenInput.attr("value",this.model.value),this._removeWatermark(),this._validState=!0):(this.model.value=null,this._setWaterMark(),this._validState=!1);this._prevValue=this.model.value;this._buttonDiv&&this._buttonDiv.find(".e-drp-apply").addClass("e-disable");this._trigger("_change",{value:this.model.value})},i.prototype._bindDateButton=function(){if(this.dateRangeicon&&this._on(this.dateRangeIcon,"click",this._showDatePopUp),!this.model.allowEdit){this.element.attr("readonly","readonly");this.element.on("mousedown",$.proxy(this._showDatePopUp,this));this.element.off("blur",$.proxy(this._onMainFocusOut,this))}this.model.allowEdit&&this.element.off("mousedown",$.proxy(this._showDatePopUp,this))},i.prototype._showDatePopUp=function(n){var t,i;if(!this.model.enabled)return!1;(t=!1,n.button?t=n.button==2:n.which&&(t=n.which==3),t)||(this._showhidePopup(n),this._isManualFocus,i=this,setTimeout(()=>{i._leftDP.element.focus(),i._isManualFocus=!0},5))},i.prototype._showhidePopup=function(n){this._popupOpen?(this._isFocused||!this.element.is(":input")||ej.isTouchDevice()||this.wrapper.addClass("e-focus"),this.popupHide(n)):(this._isFocused||!this.element.is(":input")||ej.isTouchDevice()||this.wrapper.addClass("e-focus"),this.popupShow(n))},i.prototype.popupHide=function(n){if(!n||n.type!="touchmove"&&n.type!="scroll"||!($(n.target).parents("#"+this.popup[0].id).length>0)){if(!this._popupOpen||this._isPopScroll)return!1;var t=this;if(this._trigger("beforeClose",{element:this.popup,events:n}))return!1;this.popup.attr({"aria-hidden":"true"});this.element.attr({"aria-expanded":"false"});this._leftTP&&!this._leftTP._popupOpen&&this._leftTP.hide();this._rightTP&&!this._rightTP._popupOpen&&this._rightTP.hide();this.popup.css("visibility","visible").slideUp(100,function(){t._popupOpen=!1});this._off(ej.getScrollableParents(this.wrapper),"scroll",this.popupHide);this._off(ej.getScrollableParents(this.wrapper),"touchmove",this.popupHide);this._trigger("close",{element:this.popup,events:n});this.wrapper.removeClass("e-active")}},i.prototype.renderpopup=function(){this.popup?this.model.isResponsive&&this._customRangePicker&&this.datePopup&&(this.datePopup.hide(),this._buttonDiv.hide(),this._customRangePicker.show().addClass("e-responsive"),this._showRangesOnly=!0):(this.popup=ej.buildTag("div.e-daterangepicker-popup e-popup e-widget e-box"+this.model.cssClass+"#"+this.element[0].id+"_popup").css("display","none"),$("body").append(this.popup),this.model.isResponsive?this.model.ranges?(this.model.ranges&&this._renderRanges(),this._customRangePicker&&this._customRangePicker.addClass("e-responsive"),this._showRangesOnly=!0):this.model.ranges||(this._renderPopup(),this._onMainFocusOut(),this._showRangesOnly=!1):(this._renderPopup(),this._onMainFocusOut(),this._showRangesOnly=!1));typeof this.model.value!="string"||ej.isNullOrUndefined(this.model.value)||this._notapplied?typeof this.model.value!="object"||this.model.value!=null||this._notapplied||this._updateValues():this._updateValues()},i.prototype.popupShow=function(n){if(!this.model.enabled||this._popupOpen)return!1;var t=this;if(this._trigger("beforeOpen",{element:this.popup,events:n}))return!1;this.wrapper.addClass("e-focus");this.popup.attr({"aria-hidden":"false"});this.element.attr({"aria-expanded":"true"});t._popupOpen=!0;this.popup.css({visibility:"hidden",display:"block"});this._resizePopup();this.popup.css({display:"none",visibility:"visible"}).slideDown(100,function(){});this._on(ej.getScrollableParents(this.wrapper),"scroll",this.popupHide);this._on(ej.getScrollableParents(this.wrapper),"touchmove",this.popupHide);this._trigger("open",{element:this.popup,events:n});this.wrapper.addClass("e-active");this.model.value!=null?$(this.element).attr("aria-activedescendant",$(this._leftDP.popup.find(".e-active")).attr("id")):$(this.element).attr("aria-activedescendant",$(this._leftDP.popup).find(".today").attr("id"));$(document).on("keyup",function(n){t.calendarType=="right"?t._rightDP._keyboardNavigation(n):t._leftDP._keyboardNavigation(n)})},i.prototype._resizePopup=function(){var n=this,r=0,t=0,u=300,i=200;if(!this._showRangesOnly&&this.datePopup&&(u=this.datePopup.find(".e-popup.e-calendar").outerWidth(),i=this.datePopup.find(".e-popup.e-calendar").height()),this._customRangePicker&&this._customRangePicker.height()<=0&&this._customRangePicker.height(this._showRangesOnly?200:this.datePopup.height()),this.model.ranges&&this.model.ranges.length>0&&(t=this._customRangePicker.find("ul").height(),r=n._customRangePicker.outerWidth()),$(window).width()-this.wrapper.position().left<u*2+r+25){if(n.popup.addClass("e-daterange-responsive"),this.model.isResponsive&&(this._isMobile=!0,this._resetValues()),n._setOption("width","95%"),this.model.enableTimePicker){this._setOption("width","47%");this._setDatePickerPosition();$("#"+this._id+"_lTime_timewidget").css({left:this._leftDP.wrapper.outerWidth()+5});$("#"+this._id+"_rTime_timewidget").css({left:this._rightDP.wrapper.outerWidth()+5});n._scrollerObj&&(n._scrollerObj.model.height=t<i?t+10:i,n._scrollerObj.refresh());return}n._scrollerObj&&(n._scrollerObj.model.height=t<i?t+10:i,n._scrollerObj.refresh())}else this._isMobile&&(this._isMobile=!1,this._resetValues()),n.popup.removeClass("e-daterange-responsive"),this.model.enableTimePicker&&this.datePopup&&(this._leftTP.option("width","115px"),this._rightTP.option("width","115px"),this._setTimePickerPos()),n._scrollerObj&&(n._scrollerObj.model.height=this._showRangesOnly?"200px":this.datePopup.height(),n._scrollerObj.refresh());this._isMobile&&this.popup&&this.datePopup?this.popup.addClass("e-responsive"):this.popup.removeClass("e-responsive");this._setDatePickerPosition()},i.prototype._onDocumentClick=function(n){this.model&&(this.popup?!this.popup||$(n.target).is(this.popup)||$(n.target).parents(".e-popup").is(this.popup)||$(n.target).is(this.wrapper)||$(n.target).parents(".e-daterangewidget").is(this.wrapper)||(this.model.enableTimePicker?!this.model.enableTimePicker||$(n.target).is(this._leftTP.popup)||$(n.target).parents(".e-popup").is(this._leftTP.popup)||$(n.target).is(this._rightTP.popup)||$(n.target).parents(".e-popup").is(this._rightTP.popup)||(this._popupOpen&&this.popupHide(n),this.wrapper.removeClass("e-focus")):(this._popupOpen&&this.popupHide(n),this.wrapper.removeClass("e-focus")),this._notapplied=!this._buttonDiv.find(".e-drp-apply").hasClass("e-disable")):this.wrapper.removeClass("e-focus"))},i.prototype._getOffset=function(n){var t=n.offset()||{left:0,top:0},i;return $("body").css("position")!="static"&&(i=$("body").offset(),t.left-=i.left,t.top-=i.top),t},i.prototype._getZindexPartial=function(n,t){var u,r,i;if(!ej.isNullOrUndefined(n)&&n.length>0)return u=n.parents(),r=$("body").children(),!ej.isNullOrUndefined(n)&&n.length>0&&r.splice(r.index(t),1),$(r).each(function(n,t){u.push(t)}),i=Math.max.apply(i,$.map(u,function(n){if($(n).css("position")!="static")return parseInt($(n).css("z-index"))||1})),!i||i<1e4?i=1e4:i+=1,i},i.prototype._setDatePickerPosition=function(){var n=this.element.is("input")?this.wrapper:this.element,t=this._getOffset(n),u,s=$(document).scrollTop()+$(window).height()-(t.top+$(n).outerHeight()),h=t.top-$(document).scrollTop(),r=this.popup.outerHeight(),f=this.popup.outerWidth(),i=t.left,e=n.outerHeight(),a=(e-n.height())/2,c=this._getZindexPartial(this.element,this.popup),o=3,l=r<s||r>h?t.top+e+o:t.top-r-o;u=$(document).scrollLeft()+$(window).width()-i;this._showRangesOnly?i=t.left+this.wrapper.outerWidth()-this.popup.outerWidth():f>u&&f<i+n.outerWidth()&&(i-=this.popup.outerWidth()-n.outerWidth());this.popup.css({left:i+"px",top:l+"px","z-index":c});this.model.enableTimePicker&&this.datePopup&&($("#"+this._id+"_lTime_timewidget").css({position:"absolute",top:0,left:this._leftDP.popup.width()+this._leftDP.popup.position().left-$("#"+this._id+"_lTime_timewidget").width()}),$("#"+this._id+"_rTime_timewidget").css({position:"absolute",top:this.popup.hasClass("e-daterange-responsive")?this._rightDP.wrapper.position().top:0,left:this._rightDP.popup.width()+this._rightDP.popup.position().left-$("#"+this._id+"_rTime_timewidget").width()}))},i.prototype._getDateValue=function(n){var t=n.toDateString();return new Date(t)},i.prototype._dateEleClicked=function(n){var r,i,e,o,t,f,u;if(this._updateRangesList(),this._activeItem=$(n.currentTarget),this._activeItem.hasClass("e-hidedate")){n.stopPropagation();return}(this._activeItem.hasClass("other-month")&&this._refreshMinMax(),t=this._activeItem.attr("data-date"),r=$(n.currentTarget).parents(".e-left-datepicker").length>0?"left":"right",this.calendarType=r,ej.isNullOrUndefined(t)||t==="")||(i=new Date(t),this._selectedStartDate!=null&&this._selectedEndDate!=null&&(this._selectedStartDate=null),this._selectedStartDate==null?(this._selectedStartDate=i,this._selectedEndDate=null,this._startDate&&(this._startDate.date=null),this._rightDP.element.val(null),r=="right"&&(this._leftDP._stopRefresh=!0,this._leftDP.option("value",this._selectedStartDate),this._leftDP.element.val(ej.format(this._selectedStartDate,this.model.dateFormat,this.model.locale))),this._rightTP&&this._rightTP.option("value",""),this.popup.find(".in-range").removeClass("in-range"),this.datePopup.find("td.e-state-default.e-active").removeClass("e-active"),this._rightDP.element.parents(".e-datewidget").removeClass("e-error"),e=$(this.datePopup.find('.current-month[data-date="'+t+'"]')),this._selectedStartDate=new Date(e.attr("data-date")),this.model.enableTimePicker&&this._leftTP.model.value&&(this._selectedStartDate=new Date(this._selectedStartDate.toDateString()+" "+this._leftTP.model.value)),this._setStartDate(this._selectedStartDate,e,!0)):this._selectedStartDate===null||this._selectedEndDate!=null||this._getDateValue(i)<this._getDateValue(this._selectedStartDate)?this._selectedStartDate!==null&&this._selectedEndDate==null&&this._getDateValue(i)<this._getDateValue(this._selectedStartDate)&&this.model.backwardSelection?(o=i,t=$(n.currentTarget).attr("data-date"),this._rightDP._stopRefresh=!0,this._rightDP.option("value",new Date(t)),this._rightDP._stopRefresh=!1,this._rightTP&&this._rightTP.option("value",new Date(t)),this._rightDP.element.parents(".e-datewidget").removeClass("e-error"),f=$(this.datePopup.find('.current-month[data-date="'+t+'"]')),this._selectedStartDate=this.model.startDate,this._selectedEndDate=new Date(t),this._setEndDate(this._selectedEndDate,f,!0),this._startDate={},this._startDate.date=this._selectedStartDate,r=="left"?(this._rightDP._stopRefresh=!0,this._rightDP.option("value",this.model.startDate),this._rightDP.element.val(ej.format(this._selectedStartDate,this.model.dateFormat,this.model.locale))):r=="right"&&(this._leftDP._stopRefresh=!0,this._leftDP.option("value",this.model.endDate),this._leftDP.element.val(ej.format(this._selectedStartDate,this.model.dateFormat,this.model.locale)),this._rightDP._stopRefresh=!0,this._rightDP.option("value",this.model.startDate),this._rightDP.element.val(ej.format(this._selectedStartDate,this.model.dateFormat,this.model.locale)))):(this._selectedStartDate=i,this._selectedEndDate=null,this._endDate&&(this._endDate.date=null),this.popup.find(".in-range").removeClass("in-range"),this._rightDP.option("value",null),this._rightTP&&this._rightTP.option("value",""),r=="right"&&(this._leftDP._stopRefresh=!0,this._leftDP.option("value",this._selectedStartDate),this._leftDP.element.val(ej.format(this._selectedStartDate,this.model.dateFormat,this.model.locale))),this._setStartDate(this._selectedStartDate,this._activeItem,!0),this._updateRanges("left"),this._updateRanges("right")):(o=i,t=$(n.currentTarget).attr("data-date"),this._leftDP._stopRefresh=!0,this._leftDP.option("value",new Date(this._selectedStartDate)),this._leftDP._stopRefresh=!1,this._rightDP._stopRefresh=!0,this._rightDP.option("value",new Date(t)),this._rightDP._stopRefresh=!1,this._rightTP&&this._rightTP.option("value",new Date(t)),this._getDateValue(i).toDateString()==this._getDateValue(this._selectedStartDate).toDateString()&&this._leftTP&&this._leftTP.model.value&&this._rightTP.option("minTime",this._leftTP.model.value),this._rightDP.element.parents(".e-datewidget").removeClass("e-error"),f=$(this.datePopup.find('.current-month[data-date="'+t+'"]')),this._selectedStartDate=this.model.startDate,this._selectedEndDate=new Date(t),this.model.enableTimePicker&&this._rightTP.model.value&&(this._selectedEndDate=new Date(this._selectedEndDate.toDateString()+" "+this._rightTP.model.value)),this._setEndDate(this._selectedEndDate,f,!0),this._startDate={},this._startDate.date=this._selectedStartDate,this._updateRanges("left"),this._updateRanges("right"),r=="left"&&(this._leftDP._stopRefresh=!0,this._leftDP.option("value",this.model.startDate),this._leftDP.element.val(ej.format(this._selectedStartDate,this.model.dateFormat,this.model.locale)))),u=this,setTimeout(()=>{u.calendarType=="right"?u._rightDP.element.focus():u._leftDP.element.focus(),u._isManualFocus=!0},5),this._trigger("click",{element:$(n.currentTarget),startDate:this.model.startDate,endDate:this.model.endDate,value:new Date($(n.currentTarget).attr("data-date"))}))},i.prototype._setStartDate=function(n,t,i,r){var u,e,f;(this._startDate={},this._startDate.date=n,i&&!r&&(this._endDate={},this._endDate.date=null),this._leftDP._checkDateArrows(),u="",u=r?"e-select e-start-date e-active showrange e-state-hover":"e-select e-start-date e-active showrange e-end-date e-state-hover",this.datePopup.find("td.e-state-default").removeClass(u),r||this.popup.find(".in-range").removeClass("in-range"),t.addClass("e-start-date"),this.model.startDate=n,this._buttonDiv&&this._isApplied?this._buttonDiv.find(".e-drp-apply").addClass("e-disable"):!this._isApplied&&this._buttonDiv.find(".e-drp-apply").hasClass("e-disable")&&this._rightDP.element.val()!=""&&(this._prevStartDate?this._prevStartDate.getTime()!=n.getTime():!0)&&this._buttonDiv.find(".e-drp-apply").removeClass("e-disable"),this.model.enableTimePicker)&&(e=this._leftTP,f=ej.format(this._leftTP.model.value||this._leftDP.model.value||this._rightDP.model.value,e.model.timeFormat,this.model.locale),f&&this._leftTP.option("value",f),this._prevStartDate=this.model.startDate,this._selectedStartDate=this.model.startDate)},i.prototype._setEndDate=function(n,t){if((this._rightDP.element.parents(".e-datewidget").removeClass("e-val-error"),this._endDate={},this._endDate.date=n,this.popup.find("td.e-end-date").removeClass("e-select e-end-date"),t.addClass("e-end-date"),(this._buttonDiv&&this._prevEndDate&&this._selectedStartDate?this._prevEndDate.getTime()!=n.getTime()||this._prevStartDate.toISOString()!=this._selectedStartDate.toISOString():!0)&&this._buttonDiv.find(".e-disable").removeClass("e-disable"),this.model.endDate=n,this.model.startDate>this.model.endDate&&(this.popup.find("td.e-start-date").removeClass("e-start-date").addClass("e-end-date"),t.removeClass("e-end-date").addClass("e-start-date showrange")),!this._startDate||ej.isNullOrUndefined(this._startDate.date)||this._startDate.date.getFullYear()!=this._endDate.date.getFullYear()||this._startDate.date.getMonth()!=this._endDate.date.getMonth())&&this.model.enableTimePicker){if(this.model.startDate&&this._getDateValue(this.model.startDate).toDateString()==this._getDateValue(this.model.endDate).toDateString())this._rightTP.option("minTime",this._leftTP.option("value")||""),this._rightTP.option("value",this._leftTP.option("value")||"");else{var r=this._rightTP,i=ej.format(this._rightTP.model.value,r.model.timeFormat,this.model.locale);i&&this._rightTP.option("value",i)}this._prevEndDate=this.model.endDate;this._selectedEndDate=this.model.endDate}},i.prototype._rangeRefresh=function(n){var t,r,i,f,u;this._rightDP&&(i=n.element.parent().hasClass("e-left-datepicker")?this._leftDP.popup:this._rightDP.popup);this._startDate&&this._startDate.date&&this._startDate.date.getMonth()==n.month&&this._startDate.date.getFullYear()==n.year&&(t=$(i.find('.current-month[data-date="'+this._startDate.date.toDateString()+'"]')),this._setStartDate(this._startDate.date,t,!1),this._startDate.date.getDate()+1==t.next("td").text()&&$(t).addClass("showrange"));this._endDate&&this._endDate.date&&this._endDate.date.getMonth()==n.month&&this._endDate.date.getFullYear()==n.year&&(r=$(i.find('.current-month[data-date="'+this._endDate.date.toDateString()+'"]')),this._setEndDate(this._endDate.date,r,!1));t==r&&$(t).removeClass("showrange");this._rightDP&&this._startDate&&this._startDate.date&&this._endDate&&this._endDate.date&&this._startDate.date.getFullYear()<=n.year&&this._endDate.date.getFullYear()>=n.year&&(f=n.element.parent().hasClass("e-left-datepicker")?$(i.find("td.current-month")[0]):$(i.find("td.current-month")[0]),u=n.element.parent().hasClass("e-left-datepicker")?"left":"right",this._updateRanges(u))},i.prototype._renderRangesWrapper=function(){ej.isNullOrUndefined(this._customRangePicker)&&(this._customRangePicker=ej.buildTag("div.e-custom-dateranges").css("height",this.datePopup?this.datePopup.height():"200px"),this.popup.append(this._customRangePicker),this._buttonDiv&&this._customRangePicker.insertBefore(this._buttonDiv),this._ranges_li="<ul class='e-dateranges-ul' role=menu><\/ul>",this._customRangePicker.append(this._ranges_li))},i.prototype.setRange=function(n){var t,i;if(this._clearRanges(),typeof n=="string"){for(i=0;i<this.model.ranges.length;i++)if(t=this.model.ranges[i],t.label==n){this.model.startDate=t.range[0];this.model.endDate=t.range[1];this._updatePreRanges();return}}else if(typeof n=="object"&&n.length==2){this.model.startDate=n[0];this.model.endDate=n[1];this._updatePreRanges();return}},i.prototype._updatePreRanges=function(){this._selectedStartDate=this.model.startDate;this._selectedEndDate=this.model.endDate;this._resetValues();this.popup&&this.datePopup&&this._setValues();this._refreshMinMax();this._popupOpen||this._mainValue();this._setWaterMark()},i.prototype.destroy=function(){this.destroy()},i.prototype._destroy=function(){this._popupOpen&&this._showhidePopup();this.wrapper&&(this.element.removeClass("e-input"),this.element.removeAttr("aria-atomic aria-live placeholder"),!this._cloneElement.attr("tabindex")&&this.element.attr("tabindex")&&this.element.removeAttr("tabindex"),this.element.insertAfter(this.wrapper),this.wrapper.remove());ej.isNullOrUndefined(this._leftDP)||this._leftDP.destroy();ej.isNullOrUndefined(this._rightDP)||this._rightDP.destroy();ej.isNullOrUndefined(this._rightTP)||this._rightTP.destroy();ej.isNullOrUndefined(this._leftTP)||this._leftTP.destroy();ej.isNullOrUndefined(this._scrollerObj)||this._scrollerObj.destroy();this.popup.remove()},i.prototype.addRanges=function(n,t){var e=this,f="",i,r,u;t&&(i=t,i.length===2&&(r=new Date(i[0]),u=new Date(i[1]),ej.isNullOrUndefined(r)&&(r=new Date(i[0])),ej.isNullOrUndefined(u)&&(u=new Date(i[1])),r<=u&&(n||(n="PreDefined Ranges"),f+="<li aria-selected='false' title='"+n+"'class='rangeItem' data-e-range='"+JSON.stringify(i)+"' data-e-value='"+t+"'>"+n+"<\/li>")));this._renderRangesWrapper();this._customRangePicker.find(".e-dateranges-ul").append(f);this._off(this._customRangePicker.find("ul li.rangeItem"),"click",this._customSelection);this._on(this._customRangePicker.find("ul li.rangeItem"),"click",this._customSelection);this._scrollerObj&&this._scrollerObj.refresh()},i.prototype._righthoverRange=function(n){var r,i,t;if(this._activeItem=$(n.currentTarget),this._activeItem.hasClass("e-hidedate")){n.stopPropagation();return}this.popup.find(".range-hover").removeClass("range-hover");this._activeItem.hasClass("in-range")&&this._activeItem.addClass("range-hover");r=this._activeItem.attr("data-date");i=new Date(r);this._trigger("hover",{element:n.currentTarget,events:n,value:new Date(this._activeItem.attr("data-date"))});t=this;!ej.isNullOrUndefined(t._selectedStartDate)&&ej.isNullOrUndefined(t._selectedEndDate)&&this.popup.find(".current-month").each(function(n,r){var u=$(r),e=u.attr("data-date"),f;ej.isNullOrUndefined(e)||e===""||(f=new Date(e),f>t._startDate.date&&f<i?u.addClass("in-range"):u.removeClass("in-range"),t.model.backwardSelection&&f<t._startDate.date&&f>i&&i<t._startDate.date&&u.addClass("in-range"),f.getTime()===t._selectedStartDate.getTime()&&u.next().length!==0&&u.next("td.current-month").length!==0&&i>t._selectedStartDate&&new Date(new Date(t._selectedStartDate.getTime()).setDate(t._selectedStartDate.getDate()+1)).getTime()!==i.getTime()?u.addClass("showrange"):u.removeClass("showrange"))})},i.prototype._customSelection=function(n){if(this._customRangePicker.find(".e-active").removeClass("e-active"),$(n.currentTarget).attr("data-e-range")!="customPicker"){var t=$(n.currentTarget).attr("data-e-value").split(",");this.model.startDate=new Date(t[0]);this.model.endDate=new Date(t[1]);this._showRangesOnly?(this._mainValue(),this._isPopScroll=!1,this.popupHide()):(this._rightDP.element.parents(".e-datewidget").removeClass("e-error"),this._customSet())}else this._showRangesOnly=!1,this.datePopup||this._renderPopup(),this.model.startDate=null,this.model.endDate=null,this.datePopup.show(),this._buttonDiv.show(),this.clearRanges(),this._setDatePickerPosition(),this._resizePopup(),this._customRangePicker.removeClass("e-responsive"),this.popup.hasClass("e-daterange-responsive")&&this._customRangePicker.insertBefore(this._buttonDiv).hide();$(n.currentTarget).addClass("e-active")},i.prototype._setWaterMark=function(){if(this.element!=null&&this.element.hasClass("e-input"))return this._isSupport||this.element.val()!=""?$(this.element).attr("placeholder",this._localizedLabels.watermarkText):this._hiddenInput.css("display","block").val(this._localizedLabels.watermarkText),!0},i.prototype._clearRanges=function(){this._updateRangesList();this._setOption("value","");this.popup&&this.datePopup&&this._rightDP.element.parents(".e-datewidget").removeClass("e-val-error");this._selectedStartDate=null;this._selectedEndDate=null;this._startDate&&(this._startDate.date=null);this._endDate&&(this._endDate.date=null);this.popup&&this.popup.find("td").removeClass("e-start-date e-end-date in-range e-active e-state-hover today");this.model.value=null;this.model.startDate=null;this.model.endDate=null;this.model.enableTimePicker&&(this._leftTP&&this._leftTP.option("value",null),this._rightTP&&this._rightTP.option("value",null),this._rightTP&&this._rightTP.option("minTime","12:00 AM"));this._buttonDiv&&this._buttonDiv.find(".e-drp-apply").addClass("e-disable")},i.prototype.clearRanges=function(){this._clearRanges();this._refreshMinMax();this.element.val("");this._trigger("_change",{value:this.model.value});this._trigger("change",{value:this.model.value,startDate:this.model.startDate,endDate:this.model.endDate});this._trigger("clear",{})},i.prototype._getLocalizedLabels=function(){return ej.getLocalizedConstants("ej.DateRangePicker",this.model.locale)},i.prototype._unWireEvents=function(){this._off($(".e-next",this.popup),"click",$.proxy(this._previousNextHandler,this));this._off($(".e-prev",this.popup),"click",$.proxy(this._previousNextHandler,this));this._off($(this._buttonDiv.find("div.e-drp-cancel")),"click",this._cancelButton);this._off($(this._buttonDiv.find("div.e-drp-reset")),"click",this.clearRanges);this._off(this.popup.find(".leftDate_wrapper.e-datepicker.e-js.e-input"),"blur",this._onPopupFocusOut);this._off(this.popup.find(".rightDate_wrapper.e-datepicker.e-js.e-input"),"blur",this._onPopupFocusOut);this.model.allowEdit&&(this._off(this.element,"blur",this._onMainFocusOut),this._off(this.element,"focus",this._onFocusIn),this._off(this.element,"keydown",this._onKeyDown))},i.prototype._onDocumentKeyDown=function(n){n.keyCode=="13"&&this.popup&&!this._buttonDiv.find(".e-drp-apply").hasClass("e-disable")&&this.wrapper.hasClass("e-focus")&&this._buttonDiv.find(".e-drp-apply").click()},i.prototype._wirePopupEvents=function(){this._on($(".e-next",this.popup),"click",$.proxy(this._previousNextHandler,this));this._on($(".e-prev",this.popup),"click",$.proxy(this._previousNextHandler,this))},i.prototype._wireEvents=function(){$(document).on("mousedown",$.proxy(this._onDocumentClick,this));$(document).on("keydown",$.proxy(this._onDocumentKeyDown,this));this.model.allowEdit&&(this._on(this.element,"blur",this._onMainFocusOut),this._on(this.element,"focus",this._onFocusIn),this._on(this.element,"keydown",this._onKeyDown))},i.prototype._onFocusIn=function(n){this._isSupport&&(n.preventDefault(),this._isFocused=!0);this.wrapper.hasClass("e-error")&&(this._validState=!1,this.wrapper.removeClass("e-error"));this.model.showPopupButton||this.model.readOnly||this.popupShow(n);this.model.showPopupButton||this._on(this.element,"click",this._elementClick);this.wrapper.addClass("e-focus");this._isSupport||this._hiddenInput.css("display","none")},i.prototype._onKeyDown=function(n){n.keyCode==13&&($(n.currentTarget).hasClass("e-datepicker")?(this._validateValues($(n.currentTarget).val(),$(n.currentTarget).parents(".e-left-datepicker").length>0?"left":"right",n.currentTarget.classList.contains("e-timepicker")?!0:!1),$(n.currentTarget).parents(".e-left-datepicker").length>0?this._leftDP.model.value=this.model.startDate:this._rightDP.model.value=this.model.endDate,this._onPopupFocusOut(n)):this.popup&&this.model.enableTimePicker&&($(n.currentTarget).hasClass("leftTime")&&this._leftTP&&this._rightTP?this._leftTP._trigger("select"):this._rightTP._trigger("select")));n.stopImmediatePropagation()},i.prototype._cancelButton=function(){this._prevValue=null;this._isPopScroll=!1;this._clearRanges();this._onMainFocusOut();this._showhidePopup()},i.prototype._updateRanges=function(n){var i=n=="left"?this._leftDP:this._rightDP,t=this;if(i.popup.find("td.current-month").each(function(n,i){var r=$(i),f=r.attr("data-date"),u;ej.isNullOrUndefined(f)||f===""||(u=new Date(f),ej.isNullOrUndefined(t._startDate)||ej.isNullOrUndefined(t._startDate.date)||ej.isNullOrUndefined(t._endDate.date)||(u>t._startDate.date&&u<t._endDate.date?r.addClass("in-range").removeClass("e-state-hover"):r.removeClass("in-range")),ej.isNullOrUndefined(t._startDate)||ej.isNullOrUndefined(t._startDate.date)||ej.isNullOrUndefined(t._endDate.date)||u>t._endDate.date&&u<t._startDate.date&&t._endDate.date<t._startDate.date&&r.addClass("in-range").removeClass("e-state-hover"),!ej.isNullOrUndefined(t._startDate)&&!ej.isNullOrUndefined(t._startDate.date)&&u.toDateString()==t._startDate.date.toDateString()&&t._endDate.date>=t._startDate.date&&(r.addClass("e-start-date").removeClass("e-active"),ej.isNullOrUndefined(t._endDate)||ej.isNullOrUndefined(t._endDate.date)||t._startDate.date.toDateString()==t._endDate.date.toDateString()||r.addClass("showrange"),r.removeClass("in-range")),ej.isNullOrUndefined(t._startDate)||ej.isNullOrUndefined(t._endDate.date)||!ej.isNullOrUndefined(t._endDate)&&u.toDateString()==t._endDate.date.toDateString()&&t._endDate.date>=t._startDate.date&&(r.addClass("e-end-date").removeClass("e-state-hover e-active"),r.removeClass("in-range")),!ej.isNullOrUndefined(t._startDate)&&!ej.isNullOrUndefined(t._startDate.date)&&u.toDateString()==t._startDate.date.toDateString()&&t._endDate.date<t._startDate.date&&(r.addClass("e-end-date").removeClass("e-state-hover e-active"),r.removeClass("in-range")),ej.isNullOrUndefined(t._startDate)||ej.isNullOrUndefined(t._endDate.date)||!ej.isNullOrUndefined(t._endDate)&&u.toDateString()==t._endDate.date.toDateString()&&t._endDate.date<t._startDate.date&&(r.addClass("e-start-date").removeClass("e-active"),ej.isNullOrUndefined(t._endDate)||ej.isNullOrUndefined(t._endDate.date)||t._startDate.date.toDateString()==t._endDate.date.toDateString()||r.addClass("showrange"),r.removeClass("in-range")))}),i.popup.find(".e-start-date").length>0){if($(i.popup.find(".e-start-date")).next("td.in-range").length>0)return;$(i.popup.find(".e-start-date")).removeClass("showrange")}},i.prototype.getSelectedRange=function(){return{startDate:this.model.startDate,endDate:this.model.endDate}},i.prototype.enable=function(){this.element[0].disabled=!1;this.model.enabled=!0;this.wrapper.removeClass("e-disable");this.element.removeClass("e-disable");this.element.attr("aria-disabled","false");this._isSupport||this._hiddenInput.attr("enabled","enabled");this.dateRangeIcon&&this.dateRangeIcon.removeClass("e-disable").attr("aria-disabled","false");this.popup&&this.popup.children("div").removeClass("e-disable").attr("aria-disabled","false");this._setOption("enabled",!0)},i.prototype.disable=function(){this.element[0].disabled=!0;this.model.enabled=!1;this.wrapper.addClass("e-disable");this.element.addClass("e-disable");this.element.attr("aria-disabled","true");this.element.attr("disabled","disabled");this._isSupport||this._hiddenInput.attr("disabled","disabled");this.dateRangeIcon&&this.dateRangeIcon.addClass("e-disable").attr("aria-disabled","true");this.popup&&this.popup.children("div").addClass("e-disable").attr("aria-disabled","true");this.popupHide();this._setOption("enabled",!1)},i.prototype._onMainFocusOut=function(){var n=this.element.val(),t;if(this.wrapper.removeClass("e-focus"),this._isSupport||this.element.val()!=""||this._hiddenInput.css("display","block"),this.element.val()!=""||this._prevValue!=null){if(this._prevValue&&this._prevValue==this.element.val()){this._validState?this.wrapper.removeClass("e-error"):this.wrapper.addClass("e-error");return}if(this._updateRangesList(),this.element.val()!=""&&this.element.val()!=null||this._isSupport||this._hiddenInput.css("display","block"),this.element.val()==""){this.wrapper.removeClass("e-error");this._clearRanges();this._setWaterMark();this._refreshMinMax();this._prevValue=this.model.value;this._trigger("change",{value:this.model.value,startDate:null,endDate:null});this._trigger("_change",{value:this.model.value});return}this.wrapper.removeClass("e-error");var i=this.element.val().split(this.model.separator),r=ej.parseDate(i[0],this._dateTimeFormat,this.model.locale),u=ej.parseDate(i[1],this._dateTimeFormat,this.model.locale);if(this._validState=!0,this._validateValues(r,"left")&&this._validateValues(u,"right")&&this._startEndValidation()||(this._clearRanges(),this._refreshMinMax(),t=!0),n!=""&&t){this.element.val(n);this.wrapper.addClass("e-error");this.model.value=null;this._prevValue=this.model.value;this._validState=!1;this._trigger("_change",{value:this.element.val()});this._trigger("change",{value:this.model.value,startDate:null,endDate:null});return}this._resetValues();this.popup&&this.datePopup&&this._setValues();this._refreshMinMax();this.model.value=this._validState&&n!=""?n:null;this._popupOpen||this._mainValue();this._prevValue=this.model.value;this._validState?this.wrapper.removeClass("e-error").removeClass("e-focus"):this.wrapper.addClass("e-error").removeClass("e-focus");this._trigger("change",{value:this.model.value,startDate:this.model.startDate,endDate:this.model.endDate})}},i.prototype._onPopupFocusOut=function(n){var t,u;if(!this._isManualFocus){if(n.currentTarget.classList.contains("leftTime")){if(t=null,t=this._leftDP.model.value==null&&this.model.startDate!=null?ej.format(this.model.startDate,this.model.dateFormat,this.model.locale):ej.format(this._leftDP.model.value,this.model.dateFormat,this.model.locale),this._selectedStartDate=ej.parseDate(t+" "+this._leftTP.model.value,this.model.dateFormat+" "+this.model.timeFormat,this.model.locale)||null,this.model.startDate=this._selectedStartDate,this.model.startDate&&this._setStartDate(this.model.startDate,$('.current-month[data-date="'+this.model.startDate.toDateString()+'"]'),!0,!0),this._rightDP.element.val()==""||this._rightTP.element.val()==""||!this._isApplied)return}else if(n.currentTarget.classList.contains("rightTime")&&(t=null,t=this._rightDP.model.value==null&&this.model.endDate!=null?ej.format(this.model.endDate,this.model.dateFormat,this.model.locale):ej.format(this._rightDP.model.value,this.model.dateFormat,this.model.locale),this._selectedEndDate=ej.parseDate(t+" "+this._rightTP.model.value,this.model.dateFormat+" "+this.model.timeFormat,this.model.locale)||null,this.model.endDate=this._selectedEndDate,this.model.endDate&&this._setEndDate(this.model.endDate,$('.current-month[data-date="'+this.model.endDate.toDateString()+'"]'),!0),!this._isApplied))return;(ej.format(this._selectedStartDate,"M/d/yyyy")!=$("#daterangeleftDate_wrapper").val()||ej.format(this._selectedEndDate,"M/d/yyyy")!=$("#daterangerightDate_wrapper").val())&&this._updateRangesList();u=$(n.currentTarget).parents(".e-left-datepicker").length>0?this._leftDP:this._rightDP;this._validateValues($(n.currentTarget).val(),$(n.currentTarget).parents(".e-left-datepicker").length>0?"left":"right",n.currentTarget.classList.contains("e-timepicker")?!0:!1);$(n.currentTarget).parents(".e-left-datepicker").length>0?this._leftDP.model.value=this.model.startDate:this._rightDP.model.value=this.model.endDate;var i=this.model.startDate,f=this.model.endDate,r=!1;if(this._rightDP.element.parents(".e-datewidget").removeClass("e-val-error"),this._startEndValidation()||($(n.currentTarget).parents(".e-left-datepicker").length>0?(this._clearRanges(),this.model.startDate=i,this._selectedStartDate=this.model.startDate):(this._rightDP.element.parents(".e-datewidget").addClass("e-val-error"),this.model.endDate=null,r=!0)),i==null){this._clearRanges();return}this._selectedStartDate=this.model.startDate;this._selectedEndDate=this.model.endDate;this._resetValues();this.popup&&this.datePopup&&this._setValues();this.model.showPopupButton||this._off(this.element,"click",this._elementClick);this._refreshMinMax();this._popupOpen||this._mainValue();r&&this._rightDP.element.val(f.toLocaleDateString());this._setWaterMark()}},i.prototype._resetValues=function(){if(this._isMobile){this._leftDP&&this._leftDP.option("maxDate",null);return}this.popup&&this.datePopup&&(this._leftDP.option("maxDate",null),this._rightDP.option("minDate",null),this._leftDP.option("value",this._leftDP.model.value),this._rightDP.option("value",this._rightDP.model.value))},i.prototype._resetRanges=function(){this.popup&&(this._leftDP.option("maxDate",null),this._rightDP.option("minDate",null),this._leftDP.option("value",this._leftDP.model.value),this._rightDP.option("value",this._rightDP.model.value))},i.prototype._setMinMax=function(){for(var t,i,r,u,f,e=this._rightDP.popup.find("td.e-state-default").length,n=0;n<e;n++)t=$(this._rightDP.popup.find("td.e-state-default")[n]).attr("data-date"),i=new Date(t),i<new Date(this.model.minDate)&&$(this._rightDP.popup.find("td.e-state-default")[n]).addClass("e-hidedate");for(r=this._leftDP.popup.find("td.e-state-default").length,n=0;n<r;n++)u=$(this._leftDP.popup.find("td.e-state-default")[n]).attr("data-date"),f=new Date(u),f<new Date(this.model.minDate)&&$(this._leftDP.popup.find("td.e-state-default")[n]).addClass("e-hidedate")},i.prototype._refreshMinMax=function(){var n,t;if(!this._isMobile){if(this.popup&&this.datePopup){n=this._getNextMonth(this._leftDP._calendarDate);n.toDateString()>this._rightDP._calendarDate.toDateString()&&(t=this._rightDP.model.value,this._rightDP._calendarDate=n,this._rightDP._dateValue=n,this._rightDP.option("value",n));this._rightDP.option("minDate",n);this._rightDP.option("value",t);var t=this._rightDP._calendarDate,i=t.getFullYear(),r=t.getMonth();this._leftDP.option("maxDate",new Date(i,r,0));this._setMinMax()}this.popup&&(this.popup.find("td.e-state-default").removeClass("e-state-hover"),this.popup.find("td.e-state-default.today").addClass("e-state-hover"))}},i.prototype._refreshEvents=function(){this._off(this._rightDP.sfCalendar.find("table .e-datepicker-days td.e-state-default"),"click",this._dateEleClicked);this._off(this._leftDP.sfCalendar.find("table .e-datepicker-days td.e-state-default"),"click",this._dateEleClicked);this._off(this._rightDP.sfCalendar.find("table .e-datepicker-days td.e-state-default"),"mouseover",this._righthoverRange);this._off(this._leftDP.sfCalendar.find("table .e-datepicker-days td.e-state-default"),"mouseover",this._righthoverRange);this._on(this._leftDP.sfCalendar.find("table .e-datepicker-days td.e-state-default"),"mouseover",this._righthoverRange);this._on(this._leftDP.sfCalendar.find("table .e-datepicker-days td.e-state-default"),"click",this._dateEleClicked);this._on(this._rightDP.sfCalendar.find("table .e-datepicker-days td.e-state-default"),"mouseover",this._righthoverRange);this._on(this._rightDP.sfCalendar.find("table .e-datepicker-days td.e-state-default"),"click",this._dateEleClicked);this._on($(this._leftDP.sfCalendar.find("table .e-datepicker-months td")),"click",$.proxy(this._previousNextHandler,this))},i.prototype._previousNextHandler=function(n){var o,i,r,u,f,e,h,t;if(!this._isMobile){if(o=$(n.currentTarget).closest(".e-calendar").parent(),i=o.hasClass("e-left-datepicker")?"left":"right",n.type=="_month_Loaded"?i=="left"?(u=$(this._rightDP.popup.find("td.current-month")[0]).attr("data-date"),r=this._leftDP._dateValue.toDateString()):i=="right"&&(r=$(this._leftDP.popup.find("td.current-month")[0]).attr("data-date"),u=this._rightDP._dateValue.toDateString()):(u=$(this._rightDP.popup.find("td.current-month")[0]).attr("data-date"),r=$(this._leftDP.popup.find("td.current-month")[0]).attr("data-date")),f=new Date(r),e=new Date(u),f.setHours(0,0,0,0),e.setHours(0,0,0,0),h=!0,i=="right"){t=$("table",this._leftDP.sfCalendar).get(0).className;t=="e-dp-viewdays"&&(this._leftDP._stopRefresh=!0);this._rightDP._stopRefresh=!1;var s=e,c=s.getFullYear(),l=s.getMonth();this._leftDP.option("maxDate",new Date(c,l,0));t=="e-dp-viewmonths"&&this._leftDP._startLevel("year");this._leftDP._checkDateArrows()}else t=$("table",this._rightDP.sfCalendar).get(0).className,$("table",this._rightDP.sfCalendar).get(0).className=="e-dp-viewdays"&&(this._rightDP._stopRefresh=!0),this._leftDP._stopRefresh=!1,this._rightDP.option("minDate",this._getNextMonth(f)),t=="e-dp-viewmonths"&&this._rightDP._startLevel("year"),this._rightDP._checkDateArrows();this._setMinMax();this._rightDP.element.parents(".e-datewidget").removeClass("e-error")}},i}(ej.WidgetBase);window.ej.widget("ejDateRangePicker","ej.DateRangePicker",new t);window.ejDateRangePicker=null;ej.DateRangePicker.Locale={};ej.DateRangePicker.Locale=ej.DateRangePicker.Locale||{};ej.DateRangePicker.Locale["default"]=ej.DateRangePicker.Locale["en-US"]={ButtonText:{apply:"Apply",cancel:"Cancel",reset:"Reset"},watermarkText:"Select Range",customPicker:"Custom Picker"}});
