﻿using C1.C1Report;
using Data;
using Newtonsoft.Json.Linq;
using Syncfusion.DocIO;
using Syncfusion.DocIO.DLS;
using Syncfusion.JavaScript.Models;
using Syncfusion.XPS;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Configuration;

namespace WebUI.Reporting
{
    public class ReportsFactory : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
        }

        #region  Reports

        #region  Contacts
        public WordDocument PrepareContactReport(MentalViewDataSet ds, Int64 tenantId, bool printData, bool printQuestionnaires, bool printAppointments)
        {
            //Ενημερώνει το ds με τα στοιχεία του TherapistId
            if (ds.Contacts[0].IsTherapistIdNull() == false)
            {
                ds.Users.Merge(Data.Business.UsersBusiness.GetUserById(ds.Contacts[0].TherapistId).Users);
            }

            WordDocument document = new WordDocument();
            IWSection section;
            IWParagraph paragraph;
            IWTable table;
            WTableRow row;
            WTableCell cell;

            //Loads or opens an existing Word document from stream
            FileStream fileStreamPath = new FileStream(Server.MapPath(@"ContactReport.docx"), FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
            //Loads or opens an existing Word document through Open method of WordDocument class 
            document.Open(fileStreamPath, FormatType.Automatic);

            //Εισάγει το όνομα στο τίτλο
            document.Replace("[Title-ContactFullName]", ds.Contacts[0].FullName, false, true);

            if (printData)
            {
                #region  Εισάγει τα πεδία
                string columnName;
                TextSelection[] textSelections = document.FindAll(new Regex("\\[([a-zA-Z0-9]+)\\]"));
                foreach (TextSelection textSelection in textSelections)
                {
                    columnName = textSelection.SelectedText.Replace("[", "").Replace("]", "");

                    //Αν υπάρχει το πεδίο στη βάση δεδομένων
                    if (ds.Contacts.Columns[columnName] != null)
                    {
                        //Για συγκεκριμένες στήλες
                        if (columnName == "Sex" || columnName == "SessionOrigin")
                        {
                            document.Replace(textSelection.SelectedText, FieldValuesMappings.GetDisplayOfMultipleValues("Contacts-" + columnName, ds.Contacts[0][columnName].ToString()), false, true);
                        }
                        //Συγκεκριμένα για την στήλη OnlineIntervations
                        else if (columnName == "OnlineIntervations")
                        {
                            document.Replace(textSelection.SelectedText, FieldValuesMappings.GetDisplayOfMultipleValues("Contacts-" + columnName, ds.Contacts[0][columnName].ToString()), false, true);
                        }
                        //Για όλες τις στήλες τύπου string
                        else if (ds.Contacts.Columns[columnName].DataType == typeof(string))
                        {
                            //Αν το μέγεθος του string πεδίου είναι μεγαλύτερο του 40 σημαίνει οτι είναι πεδίου κειμένου
                            if (ds.Contacts.Columns[columnName].MaxLength > 40)
                            {
                                document.Replace(textSelection.SelectedText, ds.Contacts[0][columnName].ToString(), false, true);
                            }
                            else if (ds.Contacts.Columns[columnName].MaxLength == 40)  //Αν το μέγεθος του string πεδίου είναι 40 τότε σημαίνει ότι είναι πολλαπλής επιλογής.
                            {
                                document.Replace(textSelection.SelectedText, FieldValuesMappings.GetDisplayOfMultipleValues("Contacts-" + columnName, ds.Contacts[0][columnName].ToString()), false, true);
                            }
                            else
                            {
                                document.Replace(textSelection.SelectedText, ds.Contacts[0][columnName].ToString(), false, true);
                            }
                        }
                        else if (ds.Contacts.Columns[columnName].DataType == typeof(bool))
                        {
                            document.Replace(textSelection.SelectedText, Convert.ToBoolean(ds.Contacts[0][columnName]) ? "Ναι" : "Όχι", false, true);
                        }
                        else if (ds.Contacts.Columns[columnName].DataType == typeof(Int64) || ds.Contacts.Columns[columnName].DataType == typeof(Int32))
                        {
                            if (columnName == "TherapistId" || columnName == "DiagnosticianId" || columnName == "ClinicSupervisorId")
                            {
                                Data.MentalViewDataSet doctorsDS = Data.Business.UsersBusiness.GetAllDoctorUsersList(tenantId);
                                if (ds.Contacts[0][columnName] != null && ds.Contacts[0][columnName] != DBNull.Value)  //Αν είναι συμπληρωμένο το ID και όχι null.
                                {
                                    document.Replace(textSelection.SelectedText, doctorsDS.Users.FindByUserId(Convert.ToInt64(ds.Contacts[0][columnName].ToString())).FullName, false, true);
                                }
                                else
                                {
                                    document.Replace(textSelection.SelectedText, "", false, true);
                                }
                            }
                            else
                            {
                                document.Replace(textSelection.SelectedText, ds.Contacts[0][columnName].ToString(), false, true);
                            }
                        }
                        else if (ds.Contacts.Columns[columnName].DataType == typeof(DateTime))
                        {
                            if (ds.Contacts[0][columnName] != null && ds.Contacts[0][columnName] != DBNull.Value)
                            {
                                document.Replace(textSelection.SelectedText, Convert.ToDateTime(ds.Contacts[0][columnName]).ToString(WebConfigurationManager.AppSettings["DateFormat"]), false, true);
                            }
                            else
                            {
                                document.Replace(textSelection.SelectedText, "", false, true);
                            }
                        }
                        else  //Για όλους τους άλλους τύπους πεδίων
                        {
                            document.Replace(textSelection.SelectedText, ds.Contacts[0][columnName].ToString(), false, true);
                        }
                    }
                    else
                    {
                        int i = 0;
                    }
                }
                #endregion
            }
            else
            {
                section = document.Sections[0];
                //Βρίσκει τον 1ο πίνακα που υπάρχει στο paragraph.
                table = section.Tables[0] as WTable;
                //Removes the table from the text body
                section.Body.ChildEntities.Remove(table);
            }


            //Style για τα table headers
            IWParagraphStyle appointmentsHeaderStyle = document.AddParagraphStyle("TableHeaderStyle");
            //appointmentsHeaderStyle.CharacterFormat.FontSize = 11f;
            appointmentsHeaderStyle.CharacterFormat.Bold = false;
            appointmentsHeaderStyle.ParagraphFormat.HorizontalAlignment = HorizontalAlignment.Center;
            appointmentsHeaderStyle.ParagraphFormat.BeforeSpacing = 2;
            appointmentsHeaderStyle.ParagraphFormat.AfterSpacing = 2;

            //Style για τα rows
            IWParagraphStyle appointmentsRowLeftAlignStyle = document.AddParagraphStyle("TableRowLeftAlignStyle");
            //appointmentsRowLeftAlignStyle.CharacterFormat.FontSize = 11f;
            appointmentsHeaderStyle.CharacterFormat.Bold = false;
            appointmentsRowLeftAlignStyle.ParagraphFormat.HorizontalAlignment = HorizontalAlignment.Left;
            appointmentsRowLeftAlignStyle.ParagraphFormat.BeforeSpacing = 2;
            appointmentsRowLeftAlignStyle.ParagraphFormat.AfterSpacing = 2;

            IWParagraphStyle appointmentsRowRightAlignStyle = document.AddParagraphStyle("TableRowRightAlignStyle");
            //appointmentsRowLeftAlignStyle.CharacterFormat.FontSize = 11f;
            appointmentsHeaderStyle.CharacterFormat.Bold = false;
            appointmentsRowRightAlignStyle.ParagraphFormat.HorizontalAlignment = HorizontalAlignment.Right;
            appointmentsRowRightAlignStyle.ParagraphFormat.BeforeSpacing = 2;
            appointmentsRowRightAlignStyle.ParagraphFormat.AfterSpacing = 2;



            #region  Εισάγει τα Ερωτηματολόγια
            if (printQuestionnaires)
            {
                section = document.Sections[0];  //document.AddSection();
                paragraph = section.AddParagraph();
                paragraph.AppendText("Ερωτηματολόγια");
                paragraph.ApplyStyle(BuiltinStyle.Heading1);
                section.AddParagraph();
                //Adds a new table into Word document
                table = section.AddTable();

                //Table Styling
                table.ApplyStyle(BuiltinTableStyle.MediumGrid1Accent1);
                //Enables special formatting for banded columns of the table 
                table.ApplyStyleForBandedColumns = true;
                //Enables special formatting for banded rows of the table
                table.ApplyStyleForBandedRows = true;
                //Disables special formatting for first column of the table
                table.ApplyStyleForFirstColumn = true;
                //Enables special formatting for header row of the table
                table.ApplyStyleForHeaderRow = true;
                //Enables special formatting for last column of the table
                table.ApplyStyleForLastColumn = true;
                //Disables special formatting for last row of the table
                table.ApplyStyleForLastRow = false;

                //Column widths
                int questionnaireNameColumnWidth = 140;
                int questionnaireCodeColumnWidth = 140;
                int questionnaireDateColumnWidth = 70;
                int questionnaireInputColumnWidth = 430;

                #region Εισάγει τα Headers
                row = table.AddRow();

                //1st column
                cell = row.AddCell();
                cell.Width = questionnaireNameColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Ονομασία");

                //2nd column
                cell = row.AddCell();
                cell.Width = questionnaireCodeColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Ερωτηματολόγιο");

                //3rd column
                cell = row.AddCell();
                cell.Width = questionnaireDateColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Ημ/νία");

                //4th column
                cell = row.AddCell();
                cell.Width = questionnaireInputColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Ερωτήσεις");

                #endregion

                #region  Εισάγει τα Records
                foreach (Data.MentalViewDataSet.QuestionnairesRow questionnairesRow in ds.Questionnaires.OrderBy(a => a.CreateDate))
                {
                    row = table.AddRow(true, false);

                    //1st column
                    cell = row.AddCell();
                    cell.Width = questionnaireNameColumnWidth;
                    paragraph = cell.AddParagraph();
                    paragraph.ApplyStyle("TableRowLeftAlignStyle");
                    paragraph.AppendText(questionnairesRow.Name);

                    //2nd column
                    cell = row.AddCell();
                    cell.Width = questionnaireCodeColumnWidth;
                    paragraph = cell.AddParagraph();
                    paragraph.ApplyStyle("TableRowLeftAlignStyle");
                    paragraph.AppendText(questionnairesRow.QuestionnaireCode);

                    //3rd column
                    cell = row.AddCell();
                    cell.Width = questionnaireDateColumnWidth;
                    paragraph = cell.AddParagraph();
                    paragraph.ApplyStyle("TableRowRightAlignStyle");
                    paragraph.AppendText(questionnairesRow.CreateDate.Date.ToString("dd/MM/yyyy"));

                    //4th column
                    cell = row.AddCell();
                    cell.Width = questionnaireInputColumnWidth;
                    paragraph = cell.AddParagraph();
                    paragraph.ApplyStyle("TableRowLeftAlignStyle");
                    paragraph.AppendText(questionnairesRow.QuestionnaireInput);

                    //document.Close();
                }
                #endregion
            }
            #endregion

            #region  Εισάγει τις Συνεδρίες
            if (printQuestionnaires)
            {
                section = document.Sections[0];  //document.AddSection();
                paragraph = section.AddParagraph();
                paragraph.AppendText("Συνεδρίες");
                paragraph.ApplyStyle(BuiltinStyle.Heading1);
                section.AddParagraph();
                //Adds a new table into Word document
                table = section.AddTable();

                //Table Styling
                table.ApplyStyle(BuiltinTableStyle.MediumGrid1Accent1);
                //Enables special formatting for banded columns of the table 
                table.ApplyStyleForBandedColumns = true;
                //Enables special formatting for banded rows of the table
                table.ApplyStyleForBandedRows = true;
                //Disables special formatting for first column of the table
                table.ApplyStyleForFirstColumn = true;
                //Enables special formatting for header row of the table
                table.ApplyStyleForHeaderRow = true;
                //Enables special formatting for last column of the table
                table.ApplyStyleForLastColumn = true;
                //Disables special formatting for last row of the table
                table.ApplyStyleForLastRow = false;

                //Column widths
                int recurrenceColumnWidth = 70;
                int appointmentsDateColumnWidth = 70;
                int customerColumnWidth = 200;
                int startEndTimeColumnWidth = 50;
                int descriptionColumnWidth = 340;

                #region Εισάγει τα Headers
                row = table.AddRow();

                //1st column
                cell = row.AddCell();
                cell.Width = recurrenceColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Επανάληψη");

                //2nd column
                cell = row.AddCell();
                cell.Width = appointmentsDateColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Ημ/νία");

                //3rd column
                cell = row.AddCell();
                cell.Width = customerColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Πελάτης");

                //4th column
                cell = row.AddCell();
                cell.Width = startEndTimeColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Έναρξη");

                //5th column
                cell = row.AddCell();
                cell.Width = startEndTimeColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Λήξη");

                //5th column
                cell = row.AddCell();
                cell.Width = descriptionColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Σημειώσεις");
                #endregion

                #region  Εισάγει τα Records
                foreach (Data.MentalViewDataSet.AppointmentsRow appointmentsRow in ds.Appointments.OrderBy(a => a.StartTime))
                {
                    row = table.AddRow(true, false);

                    //1st column
                    cell = row.AddCell();
                    cell.Width = recurrenceColumnWidth;
                    paragraph = cell.AddParagraph();
                    paragraph.ApplyStyle("TableRowLeftAlignStyle");
                    paragraph.AppendText(appointmentsRow.Recurrence ? "Ναι" : "");

                    //2nd column
                    cell = row.AddCell();
                    cell.Width = appointmentsDateColumnWidth;
                    paragraph = cell.AddParagraph();
                    paragraph.ApplyStyle("TableRowRightAlignStyle");
                    paragraph.AppendText(appointmentsRow.StartTime.Date.ToString("dd/MM/yyyy"));

                    //3rd column
                    cell = row.AddCell();
                    cell.Width = customerColumnWidth;
                    paragraph = cell.AddParagraph();
                    paragraph.ApplyStyle("TableRowLeftAlignStyle");
                    paragraph.AppendText(appointmentsRow.ContactFullName);

                    //4th column 
                    cell = row.AddCell();
                    cell.Width = startEndTimeColumnWidth;
                    paragraph = cell.AddParagraph();
                    paragraph.ApplyStyle("TableRowRightAlignStyle");
                    paragraph.AppendText(appointmentsRow.StartTime.ToString("HH:mm"));

                    //5th column
                    cell = row.AddCell();
                    cell.Width = startEndTimeColumnWidth;
                    paragraph = cell.AddParagraph();
                    paragraph.ApplyStyle("TableRowRightAlignStyle");
                    paragraph.AppendText(appointmentsRow.EndTime.ToString("HH:mm"));

                    //6th column
                    cell = row.AddCell();
                    cell.Width = descriptionColumnWidth;
                    paragraph = cell.AddParagraph();
                    paragraph.ApplyStyle("TableRowLeftAlignStyle");
                    paragraph.AppendText(appointmentsRow.Notes);


                    //document.Close();
                }
                #endregion
            }
            #endregion


            return document;
        }

        public WordDocument PrepareContactReportNew(MentalViewDataSet ds, Int64 tenantId, bool printData, bool printQuestionnaires, bool printAppointments, bool showPersonalData = true)
        {
            //Ενημερώνει το ds με τα στοιχεία του TherapistId
            if (ds.Contacts[0].IsTherapistIdNull() == false)
            {
                ds.Users.Merge(Data.Business.UsersBusiness.GetUserById(ds.Contacts[0].TherapistId).Users);
            }

            WordDocument document = new WordDocument();
            IWSection section;
            IWParagraph paragraph;
            IWTable table;
            WTableRow row;
            WTableCell cell;

            //Loads or opens an existing Word document from stream
            FileStream fileStreamPath = new FileStream(Server.MapPath(@"ContactReportNew.docx"), FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
            //Loads or opens an existing Word document through Open method of WordDocument class 
            document.Open(fileStreamPath, FormatType.Automatic);

            //Εισάγει το όνομα στο τίτλο
            if (showPersonalData)
            {
                document.Replace("[Title-ContactFullName]", ds.Contacts[0].FullName, false, true);
            }
            else
            {
                document.Replace("[Title-ContactFullName]", "", false, true);
            }

            if (printData)
            {
                //#region  Εισάγει τα πεδία δυναμικά (με τον παλιό τρόπο)
                //string columnName;
                //TextSelection[] textSelections = document.FindAll(new Regex("\\[([a-zA-Z0-9]+)\\]"));
                //foreach (TextSelection textSelection in textSelections)
                //{
                //    columnName = textSelection.SelectedText.Replace("[", "").Replace("]", "");

                //    //Αν υπάρχει το πεδίο στη βάση δεδομένων
                //    if (ds.Contacts.Columns[columnName] != null)
                //    {
                //        //Για συγκεκριμένες στήλες
                //        if (columnName == "Sex" || columnName == "SessionOrigin")
                //        {
                //            document.Replace(textSelection.SelectedText, FieldValuesMappings.GetDisplayOfMultipleValues("Contacts-" + columnName, ds.Contacts[0][columnName].ToString()), false, true);
                //        }
                //        //Συγκεκριμένα για την στήλη OnlineIntervations
                //        else if (columnName == "OnlineIntervations")
                //        {
                //            document.Replace(textSelection.SelectedText, FieldValuesMappings.GetDisplayOfMultipleValues("Contacts-" + columnName, ds.Contacts[0][columnName].ToString()), false, true);
                //        }
                //        //Για όλες τις στήλες τύπου string
                //        else if (ds.Contacts.Columns[columnName].DataType == typeof(string))
                //        {
                //            //Αν το μέγεθος του string πεδίου είναι μεγαλύτερο του 40 σημαίνει οτι είναι πεδίου κειμένου
                //            if (ds.Contacts.Columns[columnName].MaxLength > 40)
                //            {
                //                document.Replace(textSelection.SelectedText, ds.Contacts[0][columnName].ToString(), false, true);
                //            }
                //            else if (ds.Contacts.Columns[columnName].MaxLength == 40)  //Αν το μέγεθος του string πεδίου είναι 40 τότε σημαίνει ότι είναι πολλαπλής επιλογής.
                //            {
                //                document.Replace(textSelection.SelectedText, FieldValuesMappings.GetDisplayOfMultipleValues("Contacts-" + columnName, ds.Contacts[0][columnName].ToString()), false, true);
                //            }
                //            else
                //            {
                //                document.Replace(textSelection.SelectedText, ds.Contacts[0][columnName].ToString(), false, true);
                //            }
                //        }
                //        else if (ds.Contacts.Columns[columnName].DataType == typeof(bool))
                //        {
                //            document.Replace(textSelection.SelectedText, Convert.ToBoolean(ds.Contacts[0][columnName]) ? "Ναι" : "Όχι", false, true);
                //        }
                //        else if (ds.Contacts.Columns[columnName].DataType == typeof(Int64) || ds.Contacts.Columns[columnName].DataType == typeof(Int32))
                //        {
                //            if (columnName == "TherapistId" || columnName == "DiagnosticianId" || columnName == "ClinicSupervisorId")
                //            {
                //                Data.MentalViewDataSet.UsersDataTable doctorsDT = Data.Business.UsersBusiness.GetAllDoctorUsersList(tenantId);
                //                if (ds.Contacts[0][columnName] != null && ds.Contacts[0][columnName] != DBNull.Value)  //Αν είναι συμπληρωμένο το ID και όχι null.
                //                {
                //                    document.Replace(textSelection.SelectedText, doctorsDT.FindByUserId(Convert.ToInt64(ds.Contacts[0][columnName].ToString())).FullName, false, true);
                //                }
                //                else
                //                {
                //                    document.Replace(textSelection.SelectedText, "", false, true);
                //                }
                //            }
                //            else
                //            {
                //                document.Replace(textSelection.SelectedText, ds.Contacts[0][columnName].ToString(), false, true);
                //            }
                //        }
                //        else if (ds.Contacts.Columns[columnName].DataType == typeof(DateTime))
                //        {
                //            if (ds.Contacts[0][columnName] != null && ds.Contacts[0][columnName] != DBNull.Value)
                //            {
                //                document.Replace(textSelection.SelectedText, Convert.ToDateTime(ds.Contacts[0][columnName]).ToString(WebConfigurationManager.AppSettings["DateFormat"]), false, true);
                //            }
                //            else
                //            {
                //                document.Replace(textSelection.SelectedText, "", false, true);
                //            }
                //        }
                //        else  //Για όλους τους άλλους τύπους πεδίων
                //        {
                //            document.Replace(textSelection.SelectedText, ds.Contacts[0][columnName].ToString(), false, true);
                //        }
                //    }
                //    else
                //    {
                //        int i = 0;
                //    }
                //}
                //#endregion

                #region  Εισάγει τα πεδία "Δημογραφικές Πληροφορίες"

                if (showPersonalData == true)
                {
                    this.SetFieldTextInDocument(ds, document, "ContactId", "{ContactId}", "Αναγνωριστικό");
                    this.SetFieldTextInDocument(ds, document, "ContactCode", "{ContactCode}", "Κωδικός Πελάτη");
                    this.SetFieldTextInDocument(ds, document, "FirstName", "{FirstName}", "Όνομα Πελάτη");
                    this.SetFieldTextInDocument(ds, document, "LastName", "{LastName}", "Επώνυμο Πελάτη");
                    this.SetFieldTextInDocument(ds, document, "Mobile1", "{Mobile1}", "Κινητό");
                    this.SetFieldTextInDocument(ds, document, "Phone1", "{Phone1}", "Τηλέφωνο");
                    this.SetFieldTextInDocument(ds, document, "Email", "{Email}", "Ηλ. Διεύθυνση");
                    this.SetFieldTextInDocument(ds, document, "Email2", "{Email2}", "Ηλ. Διεύθυνση 2");
                    
                }
                else
                {
                    document.Replace("{ContactIdLabel}", "", false, true);
                    document.Replace("{ContactId}", "", false, true);
                    document.Replace("{ContactCodeLabel}", "", false, true);
                    document.Replace("{ContactCode}", "", false, true);
                    document.Replace("{FirstNameLabel}", "", false, true);
                    document.Replace("{FirstName}", "", false, true);
                    document.Replace("{LastNameLabel}", "", false, true);
                    document.Replace("{LastName}", "", false, true);
                    document.Replace("{Mobile1Label}", "", false, true);
                    document.Replace("{Mobile1}", "", false, true);
                    document.Replace("{Phone1Label}", "", false, true);
                    document.Replace("{Phone1}", "", false, true);
                    document.Replace("{EmailLabel}", "", false, true);
                    document.Replace("{Email}", "", false, true);
                    document.Replace("{Email2Label}", "", false, true);
                    document.Replace("{Email2}", "", false, true);
                    //document.Replace("{NotesLabel}", "", false, true);
                    //document.Replace("{Notes}", "", false, true);
                }

                this.SetFieldTextInDocument(ds, document, "Notes", "{Notes}", "Σημειώσεις");
                this.SetFieldTextInDocument(ds, document, "SexualOrientation", "{SexualOrientation}", "Σεξουαλικός");
                this.SetFieldTextInDocument(ds, document, "Sex", "{Sex}", "Φύλο");
                this.SetFieldTextInDocument(ds, document, "SessionOrigin", "{SessionOrigin}", "Προέλευση Συνεδρίας");
                this.SetFieldTextInDocument(ds, document, "EmergencyContactName", "{EmergencyContactName}", "Επαφή Έκτακτης Ανάγκης");
                this.SetFieldTextInDocument(ds, document, "EmergencyPhone", "{EmergencyPhone}", "Τηλέφωνο Έκτακτης Ανάγκης");
                this.SetFieldTextInDocument(ds, document, "Residence", "{Residence}", "Τόπος Κατοικίας");
                this.SetFieldTextInDocument(ds, document, "BirthDate", "{BirthDate}", "Ημ/νία Γέννησης");
                this.SetFieldTextInDocument(ds, document, "BirthPlace", "{BirthPlace}", "Τόπος Γέννησης");
                this.SetFieldTextInDocument(ds, document, "EducationLevel", "{EducationLevel}", "Επίπεδο Εκπαίδευσης");
                this.SetFieldTextInDocument(ds, document, "MaritalStatus", "{MaritalStatus}", "Καθεστώς Διαβίωσης");
                this.SetFieldTextInDocument(ds, document, "LivingStatus", "{LivingStatus}", "Καθεστώς Διαβίωσης");
                this.SetFieldTextInDocument(ds, document, "Occupation", "{Occupation}", "Ιδιότητα Επάγγελμα Ασχολία");
                this.SetFieldTextInDocument(ds, document, "EconomicStatus", "{EconomicStatus}", "Οικονομική Κατάσταση");
                this.SetFieldTextInDocument(ds, document, "CommunicationMethod", "{CommunicationMethod}", "Τρόπος Επικοινωνίας");
                this.SetFieldTextInDocument(ds, document, "SessionFrequency", "{SessionFrequency}", "Συχνότητα Συνεδριών");
                this.SetFieldTextInDocument(ds, document, "Skills", "{Skills}", "Ικανότητες/Δεξιότητες/Εφόδια");
                this.SetFieldTextInDocument(ds, document, "TraumaticHistory", "{TraumaticHistory}", "Τραυματικό Ιστορικό");
                this.SetFieldTextInDocument(ds, document, "NegativeBeliefs", "{NegativeBeliefs}", "Αρνητικές Πεποιθήσεις");
                this.SetFieldTextInDocument(ds, document, "NegativeEmotions", "{NegativeEmotions}", "Αρνητικά Συναισθήματα"); 
                this.SetFieldTextInDocument(ds, document, "TriggeringEvents", "{TriggeringEvents}", "Εκλυτικά γεγονότα/Υπενθυμιτές");
                this.SetFieldTextInDocument(ds, document, "DysfunctionalBehaviors", "{DysfunctionalBehaviors}", "Συμπτώματα/Δυσλειτουργικές συμπεριφορές");
                this.SetFieldTextInDocument(ds, document, "SecondaryBenefit", "{SecondaryBenefit}", "Δευτερογενές Όφελος");
                this.SetFieldTextInDocument(ds, document, "EmotionalProfile", "{EmotionalProfile}", "Συναισθηματικό προφίλ");
                #endregion

                #region  Εισάγει τα πεδία "Ατομικές & Ιατρικές"
                this.SetFieldTextInDocument(ds, document, "EyeContact", "{EyeContact}", "Βλεματική Επαφή");
                this.SetFieldTextInDocument(ds, document, "BodyLanguage", "{BodyLanguage}", "Γλώσσα Σώματος");
                this.SetFieldTextInDocument(ds, document, "VoiceTone", "{VoiceTone}", "Τόνος Φωνής");
                this.SetFieldTextInDocument(ds, document, "Narration", "{Narration}", "Αφήγηση");
                this.SetFieldTextInDocument(ds, document, "SexualOrientation", "{SexualOrientation}", "Σεξουαλικός Προσανατολισμός");
                this.SetFieldTextInDocument(ds, document, "GeneralRequest", "{GeneralRequest}", "Γενικό Αίτημα");
                this.SetFieldTextInDocument(ds, document, "SpecialRequest", "{SpecialRequest}", "Ειδικό Αίτημα");
                this.SetFieldTextInDocument(ds, document, "HealthHistory", "{HealthHistory}", "Ιστορικό Υγείας");
                this.SetFieldTextInDocument(ds, document, "Medication", "{Medication}", "Φαρμακευτική Αγωγή");
                this.SetFieldTextInDocument(ds, document, "HealingExperience", "{HealingExperience}", "Θεραπευτική Εμπειρία");
                this.SetFieldTextInDocument(ds, document, "OtherActivities", "{OtherActivities}", "Άλλες Δραστηριότητες");
                this.SetFieldTextInDocument(ds, document, "TherapistFirstView", "{TherapistFirstView}", "Πρώτη Εικόνα Θεραπευτή");
                this.SetFieldTextInDocument(ds, document, "AppointmentsStart", "{AppointmentsStart}", "Έναρξη Συνεδρίων");
                this.SetFieldTextInDocument(ds, document, "AppointmentsFrequency", "{AppointmentsFrequency}", "Συχνότητα Συνεδρίων");
                this.SetFieldTextInDocument(ds, document, "InterventionModel", "{InterventionModel}", "Μοντέλο Παρέμβασης");
                this.SetFieldTextInDocument(ds, document, "LastMedicalCheckupDate", "{LastMedicalCheckupDate}", "Ημ/νία Τελευτ. Ιατρικού Ελέγχου");
                this.SetFieldTextInDocument(ds, document, "LastMedicalCheckup", "{LastMedicalCheckup}", "Τελευταίος Ιατρικός Έλεγχος");
                #endregion

                #region  Εισάγει τα πεδία "Βιογραφικές"
                this.SetFieldTextInDocument(ds, document, "MotherCharacteristics", "{MotherCharacteristics}", "Χαρακτηριστικά Μητέρας");
                this.SetFieldTextInDocument(ds, document, "MotherInfo", "{MotherInfo}", "Πληροφορίες για την Μητέρα");
                this.SetFieldTextInDocument(ds, document, "OtherImportantFromMotherFamily", "{OtherImportantFromMotherFamily}", "Άλλος Σημαντικός από Οικ. Μητέρας");
                this.SetFieldTextInDocument(ds, document, "FatherCharacteristics", "{FatherCharacteristics}", "Χαρακτηριστικά Πατέρα");
                this.SetFieldTextInDocument(ds, document, "FatherInfo", "{FatherInfo}", "Πληροφορίες για τον Πατέρα");
                this.SetFieldTextInDocument(ds, document, "OtherImportantFromFatherFamily", "{OtherImportantFromFatherFamily}", "Άλλος Σημαντικός από Οικ. Πατέρα");
                this.SetFieldTextInDocument(ds, document, "MotherFamilyHistory", "{MotherFamilyHistory}", "Οικογενειακό Ιστορικό Μητέρας");
                this.SetFieldTextInDocument(ds, document, "FatherFamilyHistory", "{FatherFamilyHistory}", "Οικογενειακό Ιστορικό Πατέρα");
                this.SetFieldTextInDocument(ds, document, "FamilyMedicalHistory", "{FamilyMedicalHistory}", "Ιατρικό ιστορικό Οικογένειας");
                this.SetFieldTextInDocument(ds, document, "OtherImportant", "{OtherImportant}", "Άλλος Σημαντικός");
                this.SetFieldTextInDocument(ds, document, "MotherFeedbackInMySuccess", "{MotherFeedbackInMySuccess}", "Ανατροφοδότηση Μητέρας σε Επιτυχία μου");
                this.SetFieldTextInDocument(ds, document, "MotherFeedbackInMyFailure", "{MotherFeedbackInMyFailure}", "Ανατροφοδότηση Μητέρας σε Αποτυχία μου");
                this.SetFieldTextInDocument(ds, document, "FatherFeedbackInMySuccess", "{FatherFeedbackInMySuccess}", "Ανατροφοδότηση Πατέρα σε Επιτυχία μου");
                this.SetFieldTextInDocument(ds, document, "FatherFeedbackInMyFailure", "{FatherFeedbackInMyFailure}", "Ανατροφοδότηση Πατέρα σε Αποτυχία μου");
                this.SetFieldTextInDocument(ds, document, "ImportantFeedbackInMySuccess", "{ImportantFeedbackInMySuccess}", "Ανατροφοδότηση Σημαντικού σε Επιτυχία μου");
                this.SetFieldTextInDocument(ds, document, "ImportantFeedbackInMyFailure", "{ImportantFeedbackInMyFailure}", "Ανατροφοδότηση Σημαντικού σε Αποτυχία μου");
                this.SetFieldTextInDocument(ds, document, "AdhesionType", "{AdhesionType}", "Τύπος Προσκόλησης");
                this.SetFieldTextInDocument(ds, document, "PreschoolExperiences", "{PreschoolExperiences}", "Προσχολικές Εμπειρίες");
                this.SetFieldTextInDocument(ds, document, "SchoolExperiences", "{SchoolExperiences}", "Σχολικές Εμπειρίες");
                this.SetFieldTextInDocument(ds, document, "TeenageExperiences", "{TeenageExperiences}", "Εφηβικές Εμπειρίες");
                this.SetFieldTextInDocument(ds, document, "AdultExperiences", "{AdultExperiences}", "Ενήλικες Εμπειρίες");
                this.SetFieldTextInDocument(ds, document, "WorkExperiences", "{WorkExperiences}", "Εργασιακές Εμπειρίες");
                this.SetFieldTextInDocument(ds, document, "GeneralTraumaHistory", "{GeneralTraumaHistory}", "Γενικό Τραυματικό Ιστορικό");
                this.SetFieldTextInDocument(ds, document, "SpecificTraumaHistory", "{SpecificTraumaHistory}", "Ειδικό Τραυματικό Ιστορικό");
                #endregion

                #region  Εισάγει τα πεδία "Κλινική Εικόνα"
                this.SetFieldTextInDocument(ds, document, "Developmental", "{Developmental}", "Αναπτυξιακές");
                this.SetFieldTextInDocument(ds, document, "EmotionalDifficulties", "{EmotionalDifficulties}", "Συναισθηματικές Δυσκολίες");
                this.SetFieldTextInDocument(ds, document, "EmotionalRemarks", "{EmotionalRemarks}", "Πρόσθετες Παρατηρήσεις σε Συναισθηματικά ή Αναπτυξιακά Ζητήματα");
                this.SetFieldTextInDocument(ds, document, "EatingDisorder", "{EatingDisorder}", "Διατροφικές");
                this.SetFieldTextInDocument(ds, document, "Moods", "{Moods}", "Διαθεσεις");
                this.SetFieldTextInDocument(ds, document, "OtherMoodObservations", "{OtherMoodObservations}", "Άλλες Παρατηρήσεις στις Διαθέσεις");
                this.SetFieldTextInDocument(ds, document, "Anxiety", "{Anxiety}", "Αγχώδεις");
                this.SetFieldTextInDocument(ds, document, "Sleep", "{Sleep}", "Ύπνος");
                this.SetFieldTextInDocument(ds, document, "Abuses", "{Abuses}", "Καταχρήσεις, χρήση ουσιών, εξαρτήσεις");
                this.SetFieldTextInDocument(ds, document, "Psychotic", "{Psychotic}", "Ψυχωσικά");
                this.SetFieldTextInDocument(ds, document, "DefenseMechanisms", "{DefenseMechanisms}", "Αμυντικοί Μηχανισμοί");
                this.SetFieldTextInDocument(ds, document, "Shapes", "{Shapes}", "Σχήματα");
                this.SetFieldTextInDocument(ds, document, "Paranoid", "{Paranoid}", "Παρανοειδής");
                this.SetFieldTextInDocument(ds, document, "Schizoid", "{Schizoid}", "Σχιζοειδής");
                this.SetFieldTextInDocument(ds, document, "Schizotype", "{Schizotype}", "Σχιζότυπη");
                this.SetFieldTextInDocument(ds, document, "OnLimit", "{OnLimit}", "Οριακή");
                this.SetFieldTextInDocument(ds, document, "Antisocial", "{Antisocial}", "Αντικοινωνική");
                this.SetFieldTextInDocument(ds, document, "Histronic", "{Histronic}", "Ιστριονική");
                this.SetFieldTextInDocument(ds, document, "Narcissistic", "{Narcissistic}", "Ναρκισσιστική");
                this.SetFieldTextInDocument(ds, document, "Ideopsychocompression", "{Ideopsychocompression}", "Ιδεοψυχαναγκαστική");
                this.SetFieldTextInDocument(ds, document, "Avoidable", "{Avoidable}", "Αποφευτική");
                this.SetFieldTextInDocument(ds, document, "Addictive", "{Addictive}", "Εξαρτητική");
                this.SetFieldTextInDocument(ds, document, "PassiveAggressive", "{PassiveAggressive}", "Παθητική Επιθετική");
                this.SetFieldTextInDocument(ds, document, "OtherDisorderInfo", "{OtherDisorderInfo}", "Άλλες Πληροφορίες Συσχετιζόμενες με Διαταραχές");
                #endregion
            }
            else
            {
                section = document.Sections[0];
                //Βρίσκει τον 1ο πίνακα που υπάρχει στο paragraph.
                table = section.Tables[0] as WTable;
                //Removes the table from the text body
                section.Body.ChildEntities.Remove(table);
            }


            //Style για τα table headers
            IWParagraphStyle appointmentsHeaderStyle = document.AddParagraphStyle("TableHeaderStyle");
            //appointmentsHeaderStyle.CharacterFormat.FontSize = 11f;
            appointmentsHeaderStyle.CharacterFormat.Bold = false;
            appointmentsHeaderStyle.ParagraphFormat.HorizontalAlignment = HorizontalAlignment.Center;
            appointmentsHeaderStyle.ParagraphFormat.BeforeSpacing = 2;
            appointmentsHeaderStyle.ParagraphFormat.AfterSpacing = 2;

            //Style για τα rows
            IWParagraphStyle appointmentsRowLeftAlignStyle = document.AddParagraphStyle("TableRowLeftAlignStyle");
            //appointmentsRowLeftAlignStyle.CharacterFormat.FontSize = 11f;
            appointmentsHeaderStyle.CharacterFormat.Bold = false;
            appointmentsRowLeftAlignStyle.ParagraphFormat.HorizontalAlignment = HorizontalAlignment.Left;
            appointmentsRowLeftAlignStyle.ParagraphFormat.BeforeSpacing = 2;
            appointmentsRowLeftAlignStyle.ParagraphFormat.AfterSpacing = 2;

            IWParagraphStyle appointmentsRowRightAlignStyle = document.AddParagraphStyle("TableRowRightAlignStyle");
            //appointmentsRowLeftAlignStyle.CharacterFormat.FontSize = 11f;
            appointmentsHeaderStyle.CharacterFormat.Bold = false;
            appointmentsRowRightAlignStyle.ParagraphFormat.HorizontalAlignment = HorizontalAlignment.Right;
            appointmentsRowRightAlignStyle.ParagraphFormat.BeforeSpacing = 2;
            appointmentsRowRightAlignStyle.ParagraphFormat.AfterSpacing = 2;



            #region  Εισάγει τα Ερωτηματολόγια
            if (printQuestionnaires)
            {
                section = document.Sections[0];  //document.AddSection();
                paragraph = section.AddParagraph();
                paragraph.AppendText("Ερωτηματολόγια");
                paragraph.ApplyStyle(BuiltinStyle.Heading1);
                section.AddParagraph();
                //Adds a new table into Word document
                table = section.AddTable();

                //Table Styling
                table.ApplyStyle(BuiltinTableStyle.MediumGrid1Accent1);
                //Enables special formatting for banded columns of the table 
                table.ApplyStyleForBandedColumns = true;
                //Enables special formatting for banded rows of the table
                table.ApplyStyleForBandedRows = true;
                //Disables special formatting for first column of the table
                table.ApplyStyleForFirstColumn = true;
                //Enables special formatting for header row of the table
                table.ApplyStyleForHeaderRow = true;
                //Enables special formatting for last column of the table
                table.ApplyStyleForLastColumn = true;
                //Disables special formatting for last row of the table
                table.ApplyStyleForLastRow = false;

                //Column widths
                int questionnaireNameColumnWidth = 140;
                int questionnaireCodeColumnWidth = 140;
                int questionnaireDateColumnWidth = 70;
                int questionnaireInputColumnWidth = 430;

                #region Εισάγει τα Headers
                row = table.AddRow();

                //1st column
                cell = row.AddCell();
                cell.Width = questionnaireNameColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Ονομασία");

                //2nd column
                cell = row.AddCell();
                cell.Width = questionnaireCodeColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Ερωτηματολόγιο");

                //3rd column
                cell = row.AddCell();
                cell.Width = questionnaireDateColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Ημ/νία");

                //4th column
                cell = row.AddCell();
                cell.Width = questionnaireInputColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Αποτελέσματα Ερωτηματολογίου");

                #endregion

                #region  Εισάγει τα Records
                foreach (Data.MentalViewDataSet.QuestionnairesRow questionnairesRow in ds.Questionnaires.OrderBy(a => a.CreateDate))
                {
                    row = table.AddRow(true, false);

                    //1st column
                    cell = row.AddCell();
                    cell.Width = questionnaireNameColumnWidth;
                    paragraph = cell.AddParagraph();
                    paragraph.ApplyStyle("TableRowLeftAlignStyle");
                    paragraph.AppendText(questionnairesRow.Name);

                    //2nd column
                    cell = row.AddCell();
                    cell.Width = questionnaireCodeColumnWidth;
                    paragraph = cell.AddParagraph();
                    paragraph.ApplyStyle("TableRowLeftAlignStyle");
                    paragraph.AppendText(questionnairesRow.QuestionnaireCode);

                    //3rd column
                    cell = row.AddCell();
                    cell.Width = questionnaireDateColumnWidth;
                    paragraph = cell.AddParagraph();
                    paragraph.ApplyStyle("TableRowRightAlignStyle");
                    paragraph.AppendText(questionnairesRow.CreateDate.Date.ToString("dd/MM/yyyy"));

                    //4th column
                    cell = row.AddCell();
                    cell.Width = questionnaireInputColumnWidth;
                    paragraph = cell.AddParagraph();
                    paragraph.ApplyStyle("TableRowLeftAlignStyle");
                    paragraph.AppendText(questionnairesRow.QuestionnaireInput);

                    //document.Close();
                }
                #endregion
            }
            #endregion

            #region  Εισάγει τις Συνεδρίες
            if (printAppointments)
            {
                section = document.Sections[0];  //document.AddSection();
                paragraph = section.AddParagraph();
                paragraph.AppendText("Συνεδρίες");
                paragraph.ApplyStyle(BuiltinStyle.Heading1);
                section.AddParagraph();
                //Adds a new table into Word document
                table = section.AddTable();

                //Table Styling
                table.ApplyStyle(BuiltinTableStyle.MediumGrid1Accent1);
                //Enables special formatting for banded columns of the table 
                table.ApplyStyleForBandedColumns = true;
                //Enables special formatting for banded rows of the table
                table.ApplyStyleForBandedRows = true;
                //Disables special formatting for first column of the table
                table.ApplyStyleForFirstColumn = true;
                //Enables special formatting for header row of the table
                table.ApplyStyleForHeaderRow = true;
                //Enables special formatting for last column of the table
                table.ApplyStyleForLastColumn = true;
                //Disables special formatting for last row of the table
                table.ApplyStyleForLastRow = false;

                //Column widths
                int firstColumnWidth = 70;
                int secondColumnWidth = 140;
                int thirdColumnWidth = 140;
                int fourthColumnWidth = 140;
                int fifthColumnWidth = 150;
                int sixthColumnWidth = 200;

                #region Εισάγει τα Headers
                row = table.AddRow();

                ////1st column
                //cell = row.AddCell();
                //cell.Width = firstColumnWidth;
                //paragraph = cell.AddParagraph();
                //paragraph.ApplyStyle("TableHeaderStyle");
                //paragraph.AppendText("Επανάληψη");

                //2nd column
                cell = row.AddCell();
                cell.Width = secondColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Ημ/νία");

                //3rd column
                cell = row.AddCell();
                cell.Width = thirdColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Αίτημα");

                ////4th column
                //cell = row.AddCell();
                //cell.Width = startEndTimeColumnWidth;
                //paragraph = cell.AddParagraph();
                //paragraph.ApplyStyle("TableHeaderStyle");
                //paragraph.AppendText("Έναρξη");
                //4th column
                cell = row.AddCell();
                cell.Width = fourthColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Οδηγίες Επόπτη");

                ////5th column
                //cell = row.AddCell();
                //cell.Width = startEndTimeColumnWidth;
                //paragraph = cell.AddParagraph();
                //paragraph.ApplyStyle("TableHeaderStyle");
                //paragraph.AppendText("Λήξη");
                //5th column
                cell = row.AddCell();
                cell.Width = fifthColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Μοντέλο Παρέμβασης");

                //6th column
                cell = row.AddCell();
                cell.Width = sixthColumnWidth;
                paragraph = cell.AddParagraph();
                paragraph.ApplyStyle("TableHeaderStyle");
                paragraph.AppendText("Τεχνικές Παρέμβασης");
                #endregion

                #region  Εισάγει τα Records
                foreach (Data.MentalViewDataSet.AppointmentsRow appointmentsRow in ds.Appointments.OrderBy(a => a.StartTime))
                {
                    row = table.AddRow(true, false);

                    //Αν υπάρχουν δεδομένα στην συνεδρία.
                    if (appointmentsRow.Request != "" && appointmentsRow.SupervisorInstructionsBefore != "" && appointmentsRow.IntervetionModel != "" && appointmentsRow.IntervetionTechniques != "")
                    {



                        ////1st column
                        //cell = row.AddCell();
                        //cell.Width = recurrenceColumnWidth;
                        //paragraph = cell.AddParagraph();
                        //paragraph.ApplyStyle("TableRowLeftAlignStyle");
                        //paragraph.AppendText(appointmentsRow.Recurrence ? "Ναι" : "");

                        //2nd column
                        cell = row.AddCell();
                        cell.Width = secondColumnWidth;
                        paragraph = cell.AddParagraph();
                        paragraph.ApplyStyle("TableRowRightAlignStyle");
                        paragraph.AppendText(appointmentsRow.StartTime.Date.ToString("dd/MM/yyyy"));

                        //3rd column
                        cell = row.AddCell();
                        cell.Width = thirdColumnWidth;
                        paragraph = cell.AddParagraph();
                        paragraph.ApplyStyle("TableRowLeftAlignStyle");
                        paragraph.AppendText(appointmentsRow.Request);

                        ////4th column 
                        //cell = row.AddCell();
                        //cell.Width = startEndTimeColumnWidth;
                        //paragraph = cell.AddParagraph();
                        //paragraph.ApplyStyle("TableRowRightAlignStyle");
                        //paragraph.AppendText(appointmentsRow.StartTime.ToString("HH:mm"));
                        //4th column 
                        cell = row.AddCell();
                        cell.Width = fourthColumnWidth;
                        paragraph = cell.AddParagraph();
                        paragraph.ApplyStyle("TableRowRightAlignStyle");
                        paragraph.AppendText(appointmentsRow.SupervisorInstructionsBefore);

                        ////5th column
                        //cell = row.AddCell();
                        //cell.Width = startEndTimeColumnWidth;
                        //paragraph = cell.AddParagraph();
                        //paragraph.ApplyStyle("TableRowRightAlignStyle");
                        //paragraph.AppendText(appointmentsRow.EndTime.ToString("HH:mm"));
                        //5th column
                        cell = row.AddCell();
                        cell.Width = fifthColumnWidth;
                        paragraph = cell.AddParagraph();
                        paragraph.ApplyStyle("TableRowRightAlignStyle");
                        string intervetionModelDisplay = FieldValuesMappings.GetDisplayOfMultipleValues("Appointments-IntervetionModels", appointmentsRow.IntervetionModel);
                        paragraph.AppendText(intervetionModelDisplay);

                        //6th column
                        cell = row.AddCell();
                        cell.Width = sixthColumnWidth;
                        paragraph = cell.AddParagraph();
                        paragraph.ApplyStyle("TableRowLeftAlignStyle");
                        string intervetionTechniquesDisplay = FieldValuesMappings.GetDisplayOfMultipleValues("Appointments-IntervetionTechniques", appointmentsRow.IntervetionTechniques);
                        paragraph.AppendText(intervetionTechniquesDisplay);


                        //document.Close();
                    }
                }
                #endregion
            }
            #endregion


            return document;
        }

        private void SetFieldTextInDocument(MentalViewDataSet ds, WordDocument document, string columnName, string fieldToReplace, string fieldTitle)
        {
            string fieldTitleToReplace = fieldToReplace.Insert(fieldToReplace.Length - 1, "Label") + " ";  //Βρίσκει το κείμενο προς αντικατάσταση που αντιστοιχει το "τίτλο" του πεδίου
            fieldToReplace = fieldToReplace + " ";  //Προσθέτει το κενό στο τέλος

            //Αν δεν υπάρχει value στο πεδίο βάζει το κενό και τερματίζει.
            if (ds.Contacts[0][columnName] == null || ds.Contacts[0][columnName] == DBNull.Value || ds.Contacts[0][columnName].ToString() == "")
            {
                int replacementsCount2 = document.Replace(fieldTitleToReplace, string.Empty, false, true);  //Αντικαθιστά το "τίτλο" του πεδίου με το κενό
                
                int replacementsCount3 = document.Replace(fieldToReplace, string.Empty, false, true);  //Αντικαθιστά το "value" του πεδίου με το κενό
                if (replacementsCount3 == 0)  //Αν για κάποιο λόγο δεν βρήκε στο document το {fieldToReplace} μαζί με το whitespace στο τέλος
                {
                    document.Replace(fieldToReplace.Trim(), string.Empty, false, true);  //Αντικαθιστά το "value" του πεδίου χωρίς το κενό
                }
                return;
            }

            //Αντικαθιστά πρώτα το "τίτλο" του πεδίου με bold γράμματα.
            int replacementsCount = document.Replace(fieldTitleToReplace, fieldTitle + ":  ", false, true);

            //Παρακάτω αντικαθιστά το value του πεδίου
            //Για όλες τις στήλες τύπου string
            if (ds.Contacts.Columns[columnName].DataType == typeof(string))
            {
                //Αν το μέγεθος του string πεδίου είναι μεγαλύτερο του 40 σημαίνει οτι είναι πεδίου κειμένου
                if (ds.Contacts.Columns[columnName].MaxLength > 40)
                {
                    int replacementsCount4 = document.Replace(fieldToReplace, ds.Contacts[0][columnName].ToString() + Environment.NewLine, false, false);
                }
                else if (ds.Contacts.Columns[columnName].MaxLength == 40)  //Αν το μέγεθος του string πεδίου είναι 40 τότε σημαίνει ότι είναι πολλαπλής επιλογής.
                {
                    document.Replace(fieldToReplace, FieldValuesMappings.GetDisplayOfMultipleValues("Contacts-" + columnName, ds.Contacts[0][columnName].ToString()) + Environment.NewLine, false, false);
                }
                else
                {
                    document.Replace(fieldToReplace, ds.Contacts[0][columnName].ToString() + Environment.NewLine, false, false);
                }
            }
            else if (ds.Contacts.Columns[columnName].DataType == typeof(Int64))
            {
                if (ds.Contacts[0][columnName] != null && ds.Contacts[0][columnName] != DBNull.Value)
                {
                    document.Replace(fieldToReplace, ds.Contacts[0].ContactId.ToString() + Environment.NewLine, false, true);
                }
                else
                {
                    document.Replace(fieldToReplace, "", false, true);
                }
            }
            else if (ds.Contacts.Columns[columnName].DataType == typeof(bool))
            {
                document.Replace(fieldToReplace, (Convert.ToBoolean(ds.Contacts[0][columnName]) ? "Ναι" : "Όχι") + Environment.NewLine, false, true);
            }
            else if (ds.Contacts.Columns[columnName].DataType == typeof(DateTime))
            {
                if (ds.Contacts[0][columnName] != null && ds.Contacts[0][columnName] != DBNull.Value)
                {
                    document.Replace(fieldToReplace, Convert.ToDateTime(ds.Contacts[0][columnName]).ToString(WebConfigurationManager.AppSettings["DateFormat"]) + Environment.NewLine, false, true);
                }
                else
                {
                    document.Replace(fieldToReplace, "", false, true);
                }
            }
        }

        public C1Report PrepareContactsReport(Int64 tenantId, string contactsType)
        {
            C1Report report = new C1Report();

            DataTable dt = Data.Business.ContactsBusiness.GetContactsForContactsReport(tenantId, contactsType);

            report.Load(Server.MapPath("~") + @"\Reporting\MentalViewReports.xml", "Contacts");
            report.ReportName = report.ReportInfo.Title;
            report.Fields["ReportInfoField"].Text = "";
            if (contactsType == "Active")
            {
                report.Fields["TitleField"].Text = "Ενεργοί Πελάτες";
            }
            else if (contactsType == "Deactive")
            {
                report.Fields["TitleField"].Text = "Ανενεργοί Πελάτες";
            }
            else if (contactsType == "Waiting")
            {
                report.Fields["TitleField"].Text = "Πελάτες σε Αναμονή";
            }

            //Ρυθμίζει τα data sources της εκτύπωσης.
            //report.DataSource.DataProvider = DataProvider.ExternalObject;
            report.DataSource.Recordset = dt;
            report.Layout.PaperSize = System.Drawing.Printing.PaperKind.A4;

            return report;
        }
        #endregion

        #region  TherapistContacts

        public C1Report PrepareTherapistContactsReport(Int64 tenantId, Int64 therapistId, string therapistFullName)
        {
            C1Report report = new C1Report();

            DataTable dt = Data.Business.ContactsBusiness.GetContactsForTherapistContactsReport(tenantId, therapistId);
            //Data.ComfortTransferDataSet ds = commonBusiness.DataSet;

            report.Load(Server.MapPath("~") + @"\Reporting\MentalViewReports.xml", "TherapistContacts");
            report.ReportName = report.ReportInfo.Title;
            report.Fields["ReportInfoField"].Text = "Θεραπευτής: " + therapistFullName;

            //Ρυθμίζει τα data sources της εκτύπωσης.
            //report.DataSource.DataProvider = DataProvider.ExternalObject;
            report.DataSource.Recordset = dt;
            report.Layout.PaperSize = System.Drawing.Printing.PaperKind.A4;

            return report;
        }
        #endregion

        #region  Tasks
        //public C1Report PrepareTasksReport(Int64 tenantId, DateTime startDate, DateTime endDate, bool groupByMonth, bool groupByYear, bool groupByContact, Int64? contactId)
        //{
        //    C1Report report = new C1Report();

        //    //Data.CommonBusiness commonBusiness = new Data.CommonBusiness();
        //    DataTable dt = Data.Business.TasksBusiness.GetTasksByDates(tenantId, startDate, endDate, Data.TasksSearchByDateType.StartTime, contactId);
        //    //Data.ComfortTransferDataSet ds = commonBusiness.DataSet;

        //    report.Load(Server.MapPath("~") + @"\Reporting\SecurityVueReports.xml", "Tasks");
        //    report.ReportName = report.ReportInfo.Title;

        //    report.Fields["FilterField"].Text = "Αφίξεις από " + startDate.ToShortDateString() + " εως " + endDate.ToShortDateString();

        //    //report.Document.PrinterSettings = Properties.Settings.Default.ReceiptReportPageSettings.PrinterSettings;
        //    report.Sections["MonthGroupHeader"].Visible = groupByMonth;
        //    report.Sections["MonthGroupFooter"].Visible = groupByMonth;
        //    report.Sections["YearGroupHeader"].Visible = groupByYear;
        //    report.Sections["YearGroupFooter"].Visible = groupByYear;
        //    report.Sections["ContactGroupHeader"].Visible = groupByContact; 
        //    report.Sections["ContactGroupFooter"].Visible = groupByContact;

        //    if (groupByYear == false)
        //    {
        //        report.Groups.Remove("Year Group");
        //    }
        //    if (groupByMonth == false)
        //    {
        //        report.Groups.Remove("Month Group");
        //    }
        //    if (groupByContact == false)
        //    {
        //        report.Groups.Remove("Contact Group");
        //    }

        //    //Ρυθμίζει τα data sources της εκτύπωσης.
        //    report.DataSource.DataProvider = DataProvider.ExternalObject;
        //    //report.DataSource.ConnectionString = "";
        //    report.DataSource.Recordset = dt;
        //    report.Layout.PaperSize = System.Drawing.Printing.PaperKind.A4;

        //    Session["Report"] = report;

        //    return report;
        //}
        #endregion

        #region  Appointments
        public C1Report PrepareAppointmentsReport(Int64 tenantId, DateTime startDate, DateTime endDate, string therapistId)
        {
            C1Report report = new C1Report();

            List<Int64> selectedUsersIds = null;

            if (therapistId != "")
            {
                selectedUsersIds = new List<Int64>();
                foreach (string selectedValue in therapistId.Split(','))
                {
                    selectedUsersIds.Add(Convert.ToInt64(selectedValue));
                }
            }

            DataTable dt = Data.Business.AppointmentsBusiness.GetAppointmentsByDates(tenantId, startDate, endDate, AppointmentsSearchByDateType.StartTime, null, selectedUsersIds?.ToArray());

            #region  Προσθέτει τη στήλη με το BackgroundColor του Room.
            dt.Columns.Add("RoomBackgroundColor", typeof(string), "");

            DataTable roomsDT = Data.FieldValuesMappings.DataSet.Tables["Appointments-AppointmentRooms"].Copy();
            foreach (DataRow appointmentsRow in dt.Rows)
            {
                if (appointmentsRow["Room"].ToString() != "")
                {
                    if (roomsDT.Select("RoomId='" + appointmentsRow["Room"].ToString() + "'").Count() > 0)
                    {
                        appointmentsRow["RoomBackgroundColor"] = roomsDT.Select("RoomId='" + appointmentsRow["Room"] + "'").First()["BackColor"];
                    }
                }
            }
            #endregion

            report.Load(Server.MapPath("~") + @"\Reporting\MentalViewReports.xml", "Appointments");
            report.ReportName = report.ReportInfo.Title;
            report.DataSource.Filter = "AppointmentType='Appointment'";
            report.Fields["ReportInfoField"].Text = string.Format("Συνεδρίες από {0} έως {1}", startDate.ToString("dd/MM/yyyy"), endDate.ToString("dd/MM/yyyy"));


            //Ρυθμίζει τα data sources της εκτύπωσης.
            //report.DataSource.DataProvider = DataProvider.ExternalObject;
            report.DataSource.Recordset = dt;
            report.Layout.PaperSize = System.Drawing.Printing.PaperKind.A4;
            //report.Layout.Orientation = OrientationEnum.Landscape;
            return report;
        }
        #endregion

        #region  Events
        public C1Report PrepareEventsReport(Int64 tenantId, DateTime startDate, DateTime endDate, string therapistId = "")
        {
            C1Report report = new C1Report();

            List<Int64> selectedUsersIds = null;

            if (therapistId != "")
            {
                selectedUsersIds = new List<Int64>();
                foreach (string selectedValue in therapistId.Split(','))
                {
                    selectedUsersIds.Add(Convert.ToInt64(selectedValue));
                }
            }

            DataTable dt = Data.Business.AppointmentsBusiness.GetEventsByDates(tenantId, startDate, endDate, AppointmentsSearchByDateType.StartTime, null, selectedUsersIds?.ToArray());

            //Αφαιρεί τα Events
            //DataRow[] fitleredRows = dt.Select("AppointmentType='Appointment'");
            //DataTable filteredDt = new DataTable();
            //filteredDt.BeginLoadData();
            //foreach (DataRow row in fitleredRows)
            //{
            //    filteredDt.ImportRow(row);
            //}
            //filteredDt.EndLoadData();

            report.Load(Server.MapPath("~") + @"\Reporting\MentalViewReports.xml", "Events");
            report.ReportName = report.ReportInfo.Title;
            report.DataSource.Filter = "AppointmentType='Event'";
            report.Fields["ReportInfoField"].Text = string.Format("Συμβάντα από {0} έως {1}", startDate.ToString("dd/MM/yyyy"), endDate.ToString("dd/MM/yyyy"));


            //Ρυθμίζει τα data sources της εκτύπωσης.
            //report.DataSource.DataProvider = DataProvider.ExternalObject;
            report.DataSource.Recordset = dt;
            //report.Layout.PaperSize = System.Drawing.Printing.PaperKind.A4;

            return report;
        }
        #endregion

        #region  AppointmentsInDebt
        public C1Report PrepareAppointmentsInDebtReport(Int64 tenantId, DateTime startDate, DateTime endDate, string therapistId = "")
        {
            C1Report report = new C1Report();

            List<Int64> selectedUsersIds = null;

            if (therapistId != "")
            {
                selectedUsersIds = new List<Int64>();
                foreach (string selectedValue in therapistId.Split(','))
                {
                    selectedUsersIds.Add(Convert.ToInt64(selectedValue));
                }
            }

            DataTable dt = Data.Business.AppointmentsBusiness.GetAppointmentsInDebtByDates(tenantId, startDate, endDate, AppointmentsSearchByDateType.StartTime, null, selectedUsersIds?.ToArray());

            //Αφαιρεί τα Events
            //DataRow[] fitleredRows = dt.Select("AppointmentType='Appointment'");
            //DataTable filteredDt = new DataTable();
            //filteredDt.BeginLoadData();
            //foreach (DataRow row in fitleredRows)
            //{
            //    filteredDt.ImportRow(row);
            //}
            //filteredDt.EndLoadData();

            report.Load(Server.MapPath("~") + @"\Reporting\MentalViewReports.xml", "AppointmentsInDebt");
            report.ReportName = report.ReportInfo.Title;
            report.DataSource.Filter = "AppointmentType='Appointment'";
            report.Fields["ReportInfoField"].Text = "";


            //Ρυθμίζει τα data sources της εκτύπωσης.
            //report.DataSource.DataProvider = DataProvider.ExternalObject;
            report.DataSource.Recordset = dt;
            report.Layout.PaperSize = System.Drawing.Printing.PaperKind.A4;

            return report;
        }
        #endregion

        #region  CanceledAppointments
        public C1Report PrepareCanceledAppointmentsReport(Int64 tenantId, DateTime startDate, DateTime endDate, string therapistId = "")
        {
            C1Report report = new C1Report();

            List<Int64> selectedUsersIds = null;

            if (therapistId != "")
            {
                selectedUsersIds = new List<Int64>();
                foreach (string selectedValue in therapistId.Split(','))
                {
                    selectedUsersIds.Add(Convert.ToInt64(selectedValue));
                }
            }

            DataTable dt = Data.Business.AppointmentsBusiness.GetCanceledAppointmentsByDates(tenantId, startDate, endDate, AppointmentsSearchByDateType.StartTime, null, selectedUsersIds?.ToArray());

            //Αφαιρεί τα Events
            //DataRow[] fitleredRows = dt.Select("AppointmentType='Appointment'");
            //DataTable filteredDt = new DataTable();
            //filteredDt.BeginLoadData();
            //foreach (DataRow row in fitleredRows)
            //{
            //    filteredDt.ImportRow(row);
            //}
            //filteredDt.EndLoadData();

            report.Load(Server.MapPath("~") + @"\Reporting\MentalViewReports.xml", "CanceledAppointments");
            report.ReportName = report.ReportInfo.Title;
            report.DataSource.Filter = "AppointmentType='Appointment'";
            report.Fields["ReportInfoField"].Text = "";


            //Ρυθμίζει τα data sources της εκτύπωσης.
            //report.DataSource.DataProvider = DataProvider.ExternalObject;
            report.DataSource.Recordset = dt;
            report.Layout.PaperSize = System.Drawing.Printing.PaperKind.A4;

            return report;
        }
        #endregion

        #region  SupervisionEvents
        public C1Report PrepareSupervisionEventsReport(Int64 tenantId, DateTime startDate, DateTime endDate, string therapistId = "")
        {
            C1Report report = new C1Report();

            List<Int64> selectedUsersIds = null;

            if (therapistId != "")
            {
                selectedUsersIds = new List<Int64>();
                foreach (string selectedValue in therapistId.Split(','))
                {
                    selectedUsersIds.Add(Convert.ToInt64(selectedValue));
                }
            }

            DataTable dt = Data.Business.AppointmentsBusiness.GetEventsByDates(tenantId, startDate, endDate, AppointmentsSearchByDateType.StartTime, null, selectedUsersIds?.ToArray());

            //Αφαιρεί τα Events
            //DataRow[] fitleredRows = dt.Select("AppointmentType='Appointment'");
            //DataTable filteredDt = new DataTable();
            //filteredDt.BeginLoadData();
            //foreach (DataRow row in fitleredRows)
            //{
            //    filteredDt.ImportRow(row);
            //}
            //filteredDt.EndLoadData();

            report.Load(Server.MapPath("~") + @"\Reporting\MentalViewReports.xml", "SupervisionEvents");
            report.ReportName = report.ReportInfo.Title;
            report.DataSource.Filter = "AppointmentType='Event' AND TaskSupervision=True";
            report.Fields["ReportInfoField"].Text = "";


            //Ρυθμίζει τα data sources της εκτύπωσης.
            //report.DataSource.DataProvider = DataProvider.ExternalObject;
            report.DataSource.Recordset = dt;
            //report.Layout.PaperSize = System.Drawing.Printing.PaperKind.A4;

            return report;
        }
        #endregion

        #region  IncomeClearance
        public C1Report PrepareIncomeClearanceReport(Int64 tenantId, DateTime startDate, DateTime endDate, string therapistId)
        {
            C1Report report = new C1Report();

            List<Int64> selectedUsersIds = null;

            if (therapistId != "")
            {
                selectedUsersIds = new List<Int64>();
                foreach (string selectedValue in therapistId.Split(','))
                {
                    selectedUsersIds.Add(Convert.ToInt64(selectedValue));
                }
            }

            DataTable dt = Data.Business.AppointmentsBusiness.GetAppointmentsByDates(tenantId, startDate, endDate, AppointmentsSearchByDateType.StartTime, null, selectedUsersIds?.ToArray());

            //Προσθέτει επιπλέον στήλες για υπολογισμό των οικονομικών 
            dt.Columns.Add("Balance", typeof(decimal), "Price-Deductions");
            dt.Columns.Add("CashReceived", typeof(decimal), @"IIF(PaymentType='Cash', Price, 0)");
            dt.Columns.Add("Clearance", typeof(decimal), "Balance - CashReceived");

            report.Load(Server.MapPath("~") + @"\Reporting\MentalViewReports.xml", "IncomeClearance");
            report.ReportName = report.ReportInfo.Title;
            report.DataSource.Filter = "AppointmentType='Appointment'";
            report.Fields["ReportInfoField"].Text = "";


            //Ρυθμίζει τα data sources της εκτύπωσης.
            //report.DataSource.DataProvider = DataProvider.ExternalObject;
            report.DataSource.Recordset = dt;
            report.Layout.PaperSize = System.Drawing.Printing.PaperKind.A4;
            //report.Layout.Orientation = OrientationEnum.Landscape;
            return report;
        }
        #endregion
        #endregion
    }
}