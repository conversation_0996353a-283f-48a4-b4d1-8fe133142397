﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web.Hosting;

namespace Data
{
    public class Business
    {
        private static SqlConnection staticConnection;
        private static string connectionString;
        //private static FieldValuesMappings fieldValuesMappings;

        public static string ConnectionString
        {
            get
            {
                return connectionString;
            }
            set
            {
                connectionString = value;
                staticConnection = new SqlConnection(connectionString);
            }
        }

        public static SqlConnection Connection
        {
            get
            {
                return staticConnection;
            }
            set
            {
                staticConnection = value;
            }
        }

        //public static FieldValuesMappings FieldValuesMappings
        //{
        //    get
        //    {
        //        return fieldValuesMappings;
        //    }
        //    set
        //    {
        //        fieldValuesMappings = value;    
        //    }
        //}

        public Business()
        {
            //Properties.Settings.Default.Reload();
            this.InitializeMembers();
        }

        static Business()
        {
            //fieldValuesMappings = new FieldValuesMappings();

        }

        private void InitializeMembers()
        {
            if (staticConnection != null)
            {
                staticConnection.Close();
                staticConnection.Dispose();
                staticConnection = null;
            }
            staticConnection = new SqlConnection(connectionString);


        }

        public static void ConfigureDataSet(ref MentalViewDataSet ds)
        {
            //Tenants
            foreach (DataColumn column in ds.Tenants.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
                if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
            }

            //Contacts
            foreach (DataColumn column in ds.Contacts.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
            }
            ds.Contacts.BirthDateColumn.AllowDBNull = true;
            ds.Contacts.BirthDateColumn.DefaultValue = null;
            ds.Contacts.PhotoColumn.AllowDBNull = true;
            ds.Contacts.ActiveColumn.DefaultValue = true;

            //ContactEmailTemplates
            foreach (DataColumn column in ds.ContactEmailTemplates.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
            }

            //Roles
            foreach (DataColumn column in ds.Roles.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
            }

            //Users
            foreach (DataColumn column in ds.Users.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
            }

            //Appointments
            foreach (DataColumn column in ds.Appointments.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                if (column.DataType == typeof(bool))
                {
                    column.DefaultValue = false;
                }
                if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
                if (column.DataType == typeof(int))
                {
                    column.DefaultValue = 1;
                }
            }
            ds.Appointments.RecurrenceIdColumn.AllowDBNull = true;
            ds.Appointments.ContactIdColumn.AllowDBNull = true;

            //Questionnaires
            foreach (DataColumn column in ds.Questionnaires.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
            }

            //QuestionnaireQuestions
            foreach (DataColumn column in ds.QuestionnaireQuestions.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    column.DefaultValue = "";
                }
                if (column.DataType == typeof(decimal))
                {
                    column.DefaultValue = 0;
                }
            }


        }

        public static void InitTableAdapterManager(ref MentalViewDataSetTableAdapters.TableAdapterManager taManager, SqlConnection connection)
        {
            taManager = new MentalViewDataSetTableAdapters.TableAdapterManager();

            MentalViewDataSetTableAdapters.TenantsTableAdapter tenantsTA = new MentalViewDataSetTableAdapters.TenantsTableAdapter();
            tenantsTA.Connection = connection;
            MentalViewDataSetTableAdapters.ContactsTableAdapter contactsTA = new MentalViewDataSetTableAdapters.ContactsTableAdapter();
            contactsTA.Connection = connection;
            MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
            rolesTA.Connection = connection;
            MentalViewDataSetTableAdapters.UsersTableAdapter usersRA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
            usersRA.Connection = connection;
            MentalViewDataSetTableAdapters.AppointmentsTableAdapter appointmentsTA = new MentalViewDataSetTableAdapters.AppointmentsTableAdapter();
            appointmentsTA.Connection = connection;
            MentalViewDataSetTableAdapters.AppointmentCategoriesTableAdapter appointmentCategoriesTA = new MentalViewDataSetTableAdapters.AppointmentCategoriesTableAdapter();
            appointmentCategoriesTA.Connection = connection;
            MentalViewDataSetTableAdapters.QuestionnairesTableAdapter questionnairesTA = new MentalViewDataSetTableAdapters.QuestionnairesTableAdapter();
            questionnairesTA.Connection = connection;
            MentalViewDataSetTableAdapters.QuestionnaireQuestionsTableAdapter questionnaireQuestionsTA = new MentalViewDataSetTableAdapters.QuestionnaireQuestionsTableAdapter();
            questionnaireQuestionsTA.Connection = connection;
            MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter emailTemplatesTA = new MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter();
            emailTemplatesTA.Connection = connection;
            MentalViewDataSetTableAdapters.ContactEmailTemplatesTableAdapter contactEmailTemplatesTA = new MentalViewDataSetTableAdapters.ContactEmailTemplatesTableAdapter();
            contactEmailTemplatesTA.Connection = connection;


            taManager.Connection = connection;
            taManager.ContactsTableAdapter = contactsTA;
            taManager.TenantsTableAdapter = tenantsTA;
            taManager.RolesTableAdapter = rolesTA;
            taManager.UsersTableAdapter = usersRA;
            taManager.AppointmentsTableAdapter = appointmentsTA;
            taManager.AppointmentCategoriesTableAdapter = appointmentCategoriesTA;
            taManager.QuestionnairesTableAdapter = questionnairesTA;
            taManager.QuestionnaireQuestionsTableAdapter = questionnaireQuestionsTA;
            taManager.EmailTemplatesTableAdapter = emailTemplatesTA;
            taManager.ContactEmailTemplatesTableAdapter = contactEmailTemplatesTA;
        }

        public static void SaveAllData(MentalViewDataSet ds)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSetTableAdapters.TableAdapterManager taManager = new MentalViewDataSetTableAdapters.TableAdapterManager();
                    InitTableAdapterManager(ref taManager, connection);

                    using (taManager)
                    {
                        taManager.UpdateAll(ds);

                        ds.AcceptChanges();
                    }
                }
            }
            catch (Exception exp)
            {
                ds.RejectChanges();
                throw exp;
            }
            finally
            {

            }
        }

        public class GeneralBusiness
        {
            public static DashboardInfo GetDashboardInfo(Int64 tenantId, Int64 userId)
            {
                DataSet ds = new DataSet();

                DashboardInfo info = new DashboardInfo();
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        string queryText = @"SELECT ISNULL(COUNT(*), 0) AS ContactsCount FROM Contacts WHERE [Contacts].TenantId =" + tenantId + " ";
                        queryText += @"SELECT ISNULL(COUNT(*), 0) AS FutureAppointmentsCount FROM Appointments WHERE [StartTime] >= '@startDate' AND [Appointments].UserId =" + userId + " ";

                        using (SqlDataAdapter da = new SqlDataAdapter(queryText, connection))
                        {
                            da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", DateTime.UtcNow.ToString("yyyy-MM-dd"));
                            da.Fill(ds);
                        }

                        info.ContactsCount = int.Parse(ds.Tables[0].Rows[0][0].ToString());
                        info.FutureUserAppointmentsCount = int.Parse(ds.Tables[1].Rows[0][0].ToString());

                        connection.Close();
                    }
                }
                catch (Exception exp)
                {
                    Data.ExceptionLogger.LogException(exp);
                }
                finally
                {

                }

                return info;
            }
        }

        public class AdministrationBusiness
        {
            public static MentalViewDataSet GetAllRoles(Int64 tenantId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
                    rolesTA.Connection = connection;
                    rolesTA.FillByTenantId(ds.Roles, tenantId);

                    return ds;
                }
            }

            public static MentalViewDataSet GetAllUsers()
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
                    rolesTA.Connection = connection;
                    rolesTA.Fill(ds.Roles);

                    MentalViewDataSetTableAdapters.UsersTableAdapter usersTA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
                    usersTA.Connection = connection;
                    usersTA.Fill(ds.Users);

                    connection.Close();
                    return ds;
                }
            }

            public static bool AuthenticateUser(string username, string password, out string fullname)
            {
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        MentalViewDataSet ds = new Data.MentalViewDataSet();
                        ds.EnforceConstraints = false;

                        MentalViewDataSetTableAdapters.UsersTableAdapter usersTA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
                        usersTA.Connection = connection;
                        usersTA.FillByUsernameAndPassword(ds.Users, username, password);

                        if (ds.Users.Count == 1)
                        {
                            fullname = ds.Users[0].FullName;
                            return true;
                        }
                        else
                        {
                            fullname = "";
                            return false;
                        }
                    }
                }
                finally
                {

                }
            }

            public static bool CheckUsernameExistence(string username, int userId)
            {
                SqlDataReader reader;
                SqlCommand command;

                int result = 0;
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        command = new SqlCommand();
                        command.CommandText = @"SELECT Count(*) AS Result FROM Users WHERE [Users].[Username]='" + username + "' AND NOT [Users].[UserId]=" + userId.ToString() + ";";
                        command.Connection = connection;
                        connection.Open();

                        reader = command.ExecuteReader();
                        if (reader == null)
                        {
                            throw new Exception("variable reader=null");
                        }
                        reader.Read();
                        result = reader.GetInt32(0);
                        reader.Close();
                        connection.Close();
                    }
                }
                finally
                {

                }

                if (result >= 1) //Αν βρέθηκε τουλάχιστον ένα πρόσωπο
                {
                    return true;
                }
                else  //Αν δεν βρεθηκε πρόσωπο 
                {
                    return false;
                }
            }

            public static string GetRoleOfUser(string username)
            {
                SqlDataReader reader;
                SqlCommand command;

                string result;
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        command = new SqlCommand();
                        command.CommandText = @"SELECT Roles.Name FROM Users INNER JOIN Roles ON Roles.RoleId = Users.RoleId WHERE [Users].[Username] ='" + username + "'";
                        command.Connection = connection;
                        connection.Open();

                        reader = command.ExecuteReader();
                        if (reader == null)
                        {
                            throw new Exception("variable reader=null");
                        }
                        reader.Read();
                        result = reader.GetString(0);
                        reader.Close();
                        connection.Close();
                    }
                }
                finally
                {

                }

                return result;
            }

            public static MentalViewDataSet GetUserByUsername(string username)
            {
                bool connectionMustClose = true;  //Indicates if we must close the connection after the execution of query.
                SqlConnection connection = null;

                try
                {
                    connection = new SqlConnection(connectionString);
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    Business.ConfigureDataSet(ref ds);
                    ds.EnforceConstraints = false;

                    if (connection.State == ConnectionState.Closed)
                    {
                        connection.Open();
                        connectionMustClose = true;  //We must close the connection because we found it closed.
                    }
                    else
                    {
                        connectionMustClose = false;
                    }

                    MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
                    rolesTA.Connection = connection;
                    rolesTA.Fill(ds.Roles);

                    MentalViewDataSetTableAdapters.UsersTableAdapter usersTA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
                    usersTA.Connection = connection;
                    usersTA.FillByUsername(ds.Users, username);

                    MentalViewDataSetTableAdapters.TenantsTableAdapter tenantsTA = new MentalViewDataSetTableAdapters.TenantsTableAdapter();
                    tenantsTA.Connection = connection;
                    tenantsTA.FillByUser(ds.Tenants, ds.Users[0].Username);

                    ds.EnforceConstraints = true;

                    if (ds.Users.Count > 0)
                    {
                        return ds;
                    }
                    else
                    {
                        return null;
                    }
                }
                finally
                {
                    if (connectionMustClose)  //If we must close the connection
                    {
                        if (connection != null)
                        {
                            connection.Close();
                        }
                    }
                }
            }

            public static MentalViewDataSet GetUserByUserId(Int64 userId)
            {
                bool connectionMustClose = true;  //Indicates if we must close the connection after the execution of query.
                SqlConnection connection = null;

                try
                {
                    connection = new SqlConnection(connectionString);
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    Business.ConfigureDataSet(ref ds);
                    ds.EnforceConstraints = false;

                    if (connection.State == ConnectionState.Closed)
                    {
                        connection.Open();
                        connectionMustClose = true;  //We must close the connection because we found it closed.
                    }
                    else
                    {
                        connectionMustClose = false;
                    }

                    MentalViewDataSetTableAdapters.RolesTableAdapter rolesTA = new MentalViewDataSetTableAdapters.RolesTableAdapter();
                    rolesTA.Connection = connection;
                    rolesTA.Fill(ds.Roles);

                    MentalViewDataSetTableAdapters.UsersTableAdapter usersTA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
                    usersTA.Connection = connection;
                    usersTA.FillByUserId(ds.Users, userId);

                    MentalViewDataSetTableAdapters.TenantsTableAdapter tenantsTA = new MentalViewDataSetTableAdapters.TenantsTableAdapter();
                    tenantsTA.Connection = connection;
                    tenantsTA.FillByUser(ds.Tenants, ds.Users[0].Username);

                    ds.EnforceConstraints = true;

                    if (ds.Users.Count > 0)
                    {
                        return ds;
                    }
                    else
                    {
                        return null;
                    }
                }
                finally
                {
                    if (connectionMustClose)  //If we must close the connection
                    {
                        if (connection != null)
                        {
                            connection.Close();
                        }
                    }
                }
            }

            public static void SetUserAppointmentsSort(string username, string sortValue)
            {
                int affectedRows;
                SqlCommand command;

                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        command = new SqlCommand();
                        command.CommandText = @"Update Users SET AppointmentsListSort='" + sortValue + @"' WHERE [Users].[Username] ='" + username + "'";
                        command.Connection = connection;
                        connection.Open();

                        affectedRows = command.ExecuteNonQuery();
                        if (affectedRows == 0)
                        {
                            throw new Exception("User setting 'AppointmentsListSort' not updated in database.");
                        }
                        connection.Close();
                    }
                }
                finally
                {

                }
            }

            public static void SetUserAppointmentsScheduleView(string username, string view)
            {
                int affectedRows;
                SqlCommand command;

                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        command = new SqlCommand();
                        command.CommandText = @"Update Users SET AppointmentsScheduleView='" + view + @"' WHERE [Users].[Username] ='" + username + "'";
                        command.Connection = connection;
                        connection.Open();

                        affectedRows = command.ExecuteNonQuery();
                        if (affectedRows == 0)
                        {
                            throw new Exception("User setting 'AppointmentsScheduleView' not updated in database.");
                        }
                        connection.Close();
                    }
                }
                finally
                {

                }
            }
        }

        public class ContactsBusiness
        {
            /// <summary>
            /// Saves only data of Contacts.
            /// </summary>
            public static void SaveContacts(ref MentalViewDataSet ds)
            {
                SqlConnection connection = null;

                try
                {
                    using (connection = new SqlConnection(connectionString))
                    {
                        using (MentalViewDataSetTableAdapters.ContactsTableAdapter contactsTA = new MentalViewDataSetTableAdapters.ContactsTableAdapter())
                        {
                            contactsTA.Connection = connection;
                            contactsTA.Update(ds.Contacts);

                            ds.Contacts.AcceptChanges();
                        }
                    }
                }
                catch (Exception exp)
                {
                    ds.Contacts.RejectChanges();
                    throw exp;
                }
                finally
                {
                    if (connection != null)
                    {
                        connection.Close();
                    }
                }
            }

            public static MentalViewDataSet GetContactById(Int64 contactId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    Data.Business.ConfigureDataSet(ref ds);
                    MentalViewDataSetTableAdapters.ContactsTableAdapter customersTA = new MentalViewDataSetTableAdapters.ContactsTableAdapter();
                    customersTA.Connection = connection;
                    customersTA.FillByContactId(ds.Contacts, contactId);

                    MentalViewDataSetTableAdapters.AppointmentCategoriesTableAdapter appointmentCategoriesDT = new MentalViewDataSetTableAdapters.AppointmentCategoriesTableAdapter();
                    appointmentCategoriesDT.Connection = connection;
                    appointmentCategoriesDT.FillByTenantId(ds.AppointmentCategories, ds.Contacts[0].TenantId);

                    MentalViewDataSetTableAdapters.QuestionnairesTableAdapter questionnairesDT = new MentalViewDataSetTableAdapters.QuestionnairesTableAdapter();
                    questionnairesDT.Connection = connection;
                    questionnairesDT.FillByContactId(ds.Questionnaires, contactId);

                    MentalViewDataSetTableAdapters.QuestionnaireQuestionsTableAdapter questionnaireQuestionsTA = new MentalViewDataSetTableAdapters.QuestionnaireQuestionsTableAdapter();
                    questionnaireQuestionsTA.Connection = connection;
                    questionnaireQuestionsTA.FillByContactId(ds.QuestionnaireQuestions, contactId);

                    MentalViewDataSetTableAdapters.AppointmentsTableAdapter appointmensTA = new MentalViewDataSetTableAdapters.AppointmentsTableAdapter();
                    appointmensTA.Connection = connection;
                    appointmensTA.FillByContactId(ds.Appointments, contactId);

                    MentalViewDataSetTableAdapters.ContactEmailTemplatesTableAdapter contactEmailTemplatesTA = new MentalViewDataSetTableAdapters.ContactEmailTemplatesTableAdapter();
                    contactEmailTemplatesTA.Connection = connection;
                    contactEmailTemplatesTA.FillByContactId(ds.ContactEmailTemplates, contactId);

                    return ds;
                }
            }

            public static MentalViewDataSet GetContacts(Int64 tenantId, string filter, string sortExpression, Data.SortDirection? sortDirection, int pageIndex, int pageSize, out int totalCount)
            {
                MentalViewDataSet ds = new MentalViewDataSet();
                ds.EnforceConstraints = false;
                totalCount = 0;

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("GetContacts", connection))
                    {
                        DataSet genericDS = new DataSet();

                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add("@TenantId", SqlDbType.BigInt).Value = tenantId;
                        cmd.Parameters.Add("@Filter", SqlDbType.NVarChar).Value = filter;
                        cmd.Parameters.Add("@OrderBy", SqlDbType.NVarChar).Value = sortExpression;
                        if (sortDirection.HasValue)
                        {
                            cmd.Parameters.Add("@SortType", SqlDbType.NVarChar).Value = sortDirection == SortDirection.Ascending ? "ASC" : "DESC";
                        }
                        cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                        cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;

                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(genericDS);
                        }

                        ds.Contacts.Merge(genericDS.Tables[0]);
                        ds.AcceptChanges();
                        totalCount = int.Parse(genericDS.Tables[1].Rows[0][0].ToString());
                    }
                }

                return ds;
            }

            public static DataTable GetContactsForExport(Int64 tenantId, string orderBy)
            {
                DataTable dt = new DataTable();
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    string sqlText = "select ContactCode As 'Κωδικός Πελάτη', LastName AS N'Επώνυμο', FirstName AS N'Όνομα', Case When (SELECT Active)=1 Then N'Ναι' ELse N'Όχι' END AS Ενεργός, Mobile1 AS N'Κινητό', Email, ISNULL((SELECT FullName From Users Where UserId=TherapistId), '') As Θεραπευτής, (SELECT COUNT(*) FROM Appointments WHERE Appointments.ContactId = Contacts.ContactId) AS Συνεδρείες, " +
                        //"--(SELECT MIN(StartTime) FROM Appointments WHERE Appointments.ContactId = Contacts.ContactId) AS FirstAppointmentDate, " +
                        //"--(SELECT MAX(StartTime) FROM Appointments WHERE Appointments.ContactId = Contacts.ContactId) AS LastAppointmentDate, " +
                        "ISNULL(DATEDIFF(month, (SELECT MIN(StartTime) FROM Appointments WHERE Appointments.ContactId = Contacts.ContactId), (SELECT MAX(StartTime) FROM Appointments WHERE Appointments.ContactId = Contacts.ContactId)), 0) AS 'Διάρκεια (μήνες)' " +
                        "From Contacts ";
                    if (orderBy == "FirstLastName")
                    {
                        sqlText += " ORDER BY LastName, FirstName";
                    }
                    else if (orderBy == "MostAppointments")
                    {
                        sqlText += " ORDER BY (SELECT COUNT(*) FROM Appointments WHERE Appointments.ContactId = Contacts.ContactId) DESC";
                    }

                    using (SqlCommand cmd = new SqlCommand(sqlText, connection))
                    {
                        cmd.CommandType = CommandType.Text;

                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(dt);
                        }
                    }
                }

                return dt;
            }

            public static DataTable GetAllContactsList(Int64 tenantId)
            {
                DataTable dt = new DataTable();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("SELECT ContactId, FirstName, LastName, ContactCode FROM Contacts WHERE TenantId=" + tenantId + " ORDER BY LastName, FirstName", connection))
                    {
                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(dt);
                            dt.Columns.Add("FullName", typeof(string), "LastName+ISNULL(' '+FirstName,'')");
                        }
                    }
                }

                return dt;
            }

            public static DataTable GetContactsForContactsReport(Int64 tenantId, string contactType)
            {
                DataTable dt = new DataTable();

                string whereClause = "";
                if (contactType == "Active")
                {
                    whereClause = " AND Active=1 ";
                }
                else if (contactType == "Deactive")
                {
                    whereClause = " AND Active = 0 ";
                }
                else if (contactType == "Waiting")
                {
                    whereClause = " AND Waiting = 1 ";
                }

                string query = @"SELECT ContactId, Active, Waiting, FirstName, LastName, FatherName, Email, Phone1, Mobile1, 
                            ISNULL((SELECT FullName From Users WHERE UserId = TherapistId), '') AS TherapistFullName,
                            ISNULL((SELECT FullName From Users WHERE UserId = DiagnosticianId), '') AS DiagnosticianFullName,
                            ISNULL((SELECT FullName From Users WHERE UserId = ClinicSupervisorId), '') AS ClinicSupervisorFullName
                        FROM Contacts
                        WHERE TenantId = " + tenantId.ToString() + whereClause +
                        @" ORDER BY LastName, FirstName";

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(query, connection))
                    {
                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(dt);
                            dt.Columns.Add("FullName", typeof(string), "LastName+ISNULL(' '+FirstName,'')");
                        }
                    }
                }

                return dt;
            }

            public static DataTable GetContactsForTherapistContactsReport(Int64 tenantId, Int64 therapistId)
            {
                DataTable dt = new DataTable();

                string query = @"SELECT ContactId, Active, Waiting, FirstName, LastName, FatherName, Email, Phone1, Mobile1, 
                            ISNULL((SELECT FullName From Users WHERE UserId = TherapistId), '') AS TherapistFullName,
                            ISNULL((SELECT FullName From Users WHERE UserId = DiagnosticianId), '') AS DiagnosticianFullName,
                            ISNULL((SELECT FullName From Users WHERE UserId = ClinicSupervisorId), '') AS ClinicSupervisorFullName
                        FROM Contacts
                        WHERE TenantId = " + tenantId.ToString() + " AND TherapistId=" + therapistId.ToString() +
                        @" ORDER BY LastName, FirstName";

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(query, connection))
                    {
                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(dt);
                            dt.Columns.Add("FullName", typeof(string), "LastName+ISNULL(' '+FirstName,'')");
                        }
                    }
                }

                return dt;
            }

            public static void DeleteContact(Int64 contactId)
            {
                string sqlQuery = "DELETE FROM Contacts WHERE ContactId=" + contactId.ToString();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(sqlQuery, connection))
                    {
                        connection.Open();
                        cmd.CommandType = CommandType.Text;
                        cmd.ExecuteNonQuery();
                    }
                }
            }
        }

        public class AppointmentsBusiness
        {

            public static MentalViewDataSet GetAppointmentById(Int64 appointmentId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    Data.Business.ConfigureDataSet(ref ds);
                    ds.EnforceConstraints = false;

                    connection.Open();

                    MentalViewDataSetTableAdapters.AppointmentsTableAdapter AppointmentsTA = new MentalViewDataSetTableAdapters.AppointmentsTableAdapter();
                    AppointmentsTA.Connection = connection;
                    AppointmentsTA.FillByAppointmentId(ds.Appointments, appointmentId);

                    if (ds.Appointments[0].IsContactIdNull() == false)
                    {
                        MentalViewDataSetTableAdapters.ContactsTableAdapter contactsTA = new MentalViewDataSetTableAdapters.ContactsTableAdapter();
                        contactsTA.Connection = connection;
                        contactsTA.FillByContactId(ds.Contacts, ds.Appointments[0].ContactId);
                    }


                    connection.Close();

                    return ds;
                }
            }

            public static DataTable GetAppointments(Int64 tenantId, string filter, string sortExpression, Data.SortDirection? sortDirection, int pageIndex, int pageSize, DateTime? startTimeFilter, DateTime? endTimeFilter, out int totalCount)
            {
                DataSet genericDS = new DataSet();
                totalCount = 0;

                using (SqlConnection connection = new SqlConnection(connectionString))
                {

                    using (SqlCommand cmd = new SqlCommand("GetAppointments", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add("@TenantId", SqlDbType.BigInt).Value = tenantId;
                        cmd.Parameters.Add("@Filter", SqlDbType.NVarChar).Value = filter;
                        cmd.Parameters.Add("@OrderBy", SqlDbType.NVarChar).Value = sortExpression;
                        if (sortDirection.HasValue)
                        {
                            cmd.Parameters.Add("@SortType", SqlDbType.NVarChar).Value = sortDirection == SortDirection.Ascending ? "ASC" : "DESC";
                        }
                        cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                        cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                        cmd.Parameters.Add("@StartTimeFilter", SqlDbType.DateTime).Value = startTimeFilter;
                        cmd.Parameters.Add("@EndTimeFilter", SqlDbType.DateTime).Value = endTimeFilter;
                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(genericDS);
                        }


                        totalCount = int.Parse(genericDS.Tables[1].Rows[0][0].ToString());
                    }
                }

                return genericDS.Tables[0];
            }

            public static DataTable GetAppointmentsByDates(Int64 tenantId, DateTime startDate, DateTime endDate, AppointmentsSearchByDateType appointmentsSearchByDateType, Int64? contactId, Int64[] usersIds = null)
            {
                DataTable appointmentsDT = new DataTable();
                DataTable reccurentAppointmentsTable = new DataTable();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    #region  Βρίσκει όλα τα appointments (κανονικά και με recurrence) μεταξύ των ημερομηνιών startDate και endDate, και τα Tasks που είναι visible σε όλους τους θεραπευτές
                    string queryText = @"SELECT *, 
	                    (SELECT Contacts.FirstName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactFirstName,
                        (SELECT Contacts.LastName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactLastName,
                        ISNULL((SELECT Users.FullName FROM Users WHERE Users.UserId = Appointments.UserId), '') AS UserFullName,
                        ISNULL((SELECT AppointmentCategories.CategoryName FROM AppointmentCategories WHERE AppointmentCategories.AppointmentCategoryId = Appointments.AppointmentCategoryId), '') AS AppointmentCategoryName
                        FROM Appointments
                        WHERE TenantId=" + tenantId.ToString() + " AND (" +
                            "(" + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " BETWEEN '@startDate' AND '@endDate') " +
                            " OR VisibleToAll=1";

                    //Προσθέτει το φίλτρο για όσους θεραπευτές έχουν το ID τους μέσα στη στήλη TaskSupervisionTherapistIDs
                    if (usersIds != null && usersIds.Length > 0)
                    {
                        queryText += @" OR  (TaskSupervision=1 AND ( ";
                        foreach (Int64 userId in usersIds)
                        {
                            queryText += userId.ToString() + " in (SELECT * FROM STRING_SPLIT([TaskSupervisionTherapistIDs], N'|')) OR ";
                        }
                        queryText = queryText.Substring(0, queryText.Length - 3);
                        queryText += @" )) ";
                    }

                    queryText += ")";


                    if (contactId.HasValue)
                    {
                        queryText += @" AND ContactId = " + contactId.ToString() + " ";
                    }
                    if (usersIds != null && usersIds.Length > 0)
                    {
                        queryText += @" AND (";
                        foreach (Int64 userId in usersIds)
                        {
                            queryText += @" UserId=" + userId.ToString() + " OR ";
                        }
                        queryText = queryText.Substring(0, queryText.Length - 3);
                        queryText += @" ) ";
                    }
                    queryText += " ORDER BY " + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " ASC";
                    // Για ημερομηνίες σε sql CONVERT(VARCHAR(5),StartDate,108)


                    using (SqlDataAdapter da = new SqlDataAdapter(queryText, connection))
                    {
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.ToString("yyyy/MM/dd 00:00:00"));
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", endDate.ToString("yyyy/MM/dd 23:59:59"));

                        da.Fill(appointmentsDT);
                    }

                    //Βρίσκει τα appointments που είναι visisble σε όλους, δημιουργεί αντίγραφα με το UserId των άλλων θεραπευτών ώστε να εμφανιστεί στο ημερολόγιο όλων.
                    DataRow[] visibleToAllAppointmentRows = appointmentsDT.Select("VisibleToAll=1");
                    foreach (DataRow visibleToAllAppointmentsRow in visibleToAllAppointmentRows)
                    {
                        foreach (int userId in usersIds)
                        {
                            if (userId != Convert.ToInt32(visibleToAllAppointmentsRow["UserId"]))  //Παρακάτω δημιουργούμε το νέο fake appointment μόνο για τους υπόλοιπους θεραπευτές (εκτός από αυτόν που του ανήκει το appointment)
                            {
                                DataRow fakeAppointmentsRow = appointmentsDT.LoadDataRow(visibleToAllAppointmentsRow.ItemArray, true);
                                fakeAppointmentsRow["UserId"] = userId;  //Αλλάζει το UserId για να εμφανιστεί και στον άλλο θεραπευτή.
                            }
                        }
                    }

                    //Βρίσκει τα appointments που είναι TaskSupervision=1 και στη στήλη TaskSupervisionTherapistIDs υπάρχει το ID του θεραπευτή, δημιουργεί αντίγραφα με το UserId των άλλων θεραπευτών ώστε να εμφανιστεί στο ημερολόγιο όλων.
                    DataRow[] taskSupervisionAppointmentRows = appointmentsDT.Select("TaskSupervision=1");
                    foreach (DataRow taskSupervisionAppointmentRow in taskSupervisionAppointmentRows)
                    {
                        foreach (int userId in usersIds)
                        {
                            //Αν το UserId του User που θέλουμε να εμφανίστεί στο ημερολόγιο υπάρχει στη στήλη TaskSupervisionTherapistIDs
                            if (taskSupervisionAppointmentRow["TaskSupervisionTherapistIDs"].ToString().Split('|').Any(x => x == userId.ToString()))
                            {
                                if (userId != Convert.ToInt32(taskSupervisionAppointmentRow["UserId"]))  //Παρακάτω δημιουργούμε το νέο fake appointment μόνο για τους υπόλοιπους θεραπευτές (εκτός από αυτόν που του ανήκει το appointment)
                                {
                                    DataRow fakeAppointmentsRow = appointmentsDT.LoadDataRow(taskSupervisionAppointmentRow.ItemArray, true);
                                    fakeAppointmentsRow["UserId"] = userId;  //Αλλάζει το UserId για να εμφανιστεί και στον άλλο θεραπευτή.
                                }
                            }
                        }
                    }
                    #endregion

                    #region  //Βρίσκει μόνο τα reccurent appointments που έχουν δημιουργηθεί πριν από το startDate και ένα έτος πίσω.
                    string query2Text = @"SELECT *, 
	                    (SELECT Contacts.FirstName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactFirstName,
                        (SELECT Contacts.LastName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactLastName,
                        ISNULL((SELECT Users.FullName FROM Users WHERE Users.UserId = Appointments.UserId), '') AS UserFullName,
                        ISNULL((SELECT AppointmentCategories.CategoryName FROM AppointmentCategories WHERE AppointmentCategories.AppointmentCategoryId = Appointments.AppointmentCategoryId), '') AS AppointmentCategoryName
                        FROM Appointments
                        WHERE TenantId=" + tenantId.ToString() + " AND Recurrence=1 AND (StartTime BETWEEN '@startDate' AND '@endDate') ";
                    if (contactId.HasValue)
                    {
                        query2Text += @" AND ContactId = " + contactId.ToString() + " ";
                    }
                    if (usersIds != null && usersIds.Length > 0)
                    {
                        query2Text += @" AND (";
                        foreach (Int64 userId in usersIds)
                        {
                            query2Text += @" UserId=" + userId.ToString() + " OR ";
                        }
                        query2Text = query2Text.Substring(0, query2Text.Length - 3);
                        query2Text += @" ) ";
                    }
                    query2Text += " ORDER BY " + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " ASC";
                    // Για ημερομηνίες σε sql CONVERT(VARCHAR(5),StartDate,108)

                    using (SqlDataAdapter da = new SqlDataAdapter(query2Text, connection))
                    {
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.AddYears(-2).ToString("yyyy/MM/dd 00:00:00"));  //Ημερομηνία έναρξης βάζουμε 2 χρόνια πριν το startDate.
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", startDate.AddDays(-1).ToString("yyyy/MM/dd 23:59:59"));  //Ημερομηνία λήξης βάζουμε τη προηγούμενη ημέρα από το startDate.

                        da.Fill(reccurentAppointmentsTable);
                    }
                    #endregion
                }

                appointmentsDT.Merge(reccurentAppointmentsTable);
                appointmentsDT.Columns.Add("ContactFullName", typeof(string), @"ContactLastName+ISNULL(' '+ContactFirstName,'')");
                appointmentsDT.Columns.Add("Summary", typeof(string), @"IIF(0=1, '<i style=""margin-right:5px;"" class=""fa fa-asterisk""></i>', '') + IIF(SupervisionRequest=1, '<i style=""margin-right:5px; color:orange;"" class=""fa fa-star fa-solid""></i>', '') + ISNULL(TRIM(ContactFullName), '')");

                return appointmentsDT;
            }

            public static DataTable GetAppointmentsInDebtByDates(Int64 tenantId, DateTime startDate, DateTime endDate, AppointmentsSearchByDateType appointmentsSearchByDateType, Int64? contactId, Int64[] usersIds = null)
            {
                DataTable appointmentsDT = new DataTable();
                DataTable reccurentAppointmentsTable = new DataTable();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    #region  Βρίσκει όλα τα appointments (κανονικά και με recurrence) μεταξύ των ημερομηνιών startDate και endDate, και τα Tasks που είναι visible σε όλους τους θεραπευτές
                    string queryText = @"SELECT *, 
	                    (SELECT Contacts.FirstName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactFirstName,
                        (SELECT Contacts.LastName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactLastName,
                        ISNULL((SELECT Users.FullName FROM Users WHERE Users.UserId = Appointments.UserId), '') AS UserFullName,
                        ISNULL((SELECT AppointmentCategories.CategoryName FROM AppointmentCategories WHERE AppointmentCategories.AppointmentCategoryId = Appointments.AppointmentCategoryId), '') AS AppointmentCategoryName
                        FROM Appointments
                        WHERE TenantId=" + tenantId.ToString() + " AND PaymentType='Pending' AND ((" + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " BETWEEN '@startDate' AND '@endDate') " +
                        " OR VisibleToAll=1)";
                    if (contactId.HasValue)
                    {
                        queryText += @" AND ContactId = " + contactId.ToString() + " ";
                    }
                    if (usersIds != null && usersIds.Length > 0)
                    {
                        queryText += @" AND (";
                        foreach (Int64 userId in usersIds)
                        {
                            queryText += @" UserId=" + userId.ToString() + " OR ";
                        }
                        queryText = queryText.Substring(0, queryText.Length - 3);
                        queryText += @" ) ";
                    }
                    queryText += " ORDER BY " + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " ASC";
                    // Για ημερομηνίες σε sql CONVERT(VARCHAR(5),StartDate,108)


                    using (SqlDataAdapter da = new SqlDataAdapter(queryText, connection))
                    {
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.ToString("yyyy/MM/dd 00:00:00"));
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", endDate.ToString("yyyy/MM/dd 23:59:59"));

                        da.Fill(appointmentsDT);
                    }

                    ////Βρίσκει τα appointments που είναι visisble σε όλους, δημιουργεί αντίγραφα με το UserId των άλλων θεραπευτών ώστε να εμφανιστεί στο ημερολόγιο όλων.
                    //DataRow[] visibleToAllAppointmentRows = appointmentsDT.Select("VisibleToAll=1");
                    //foreach (DataRow visibleToAllAppointmentsRow in visibleToAllAppointmentRows)
                    //{
                    //    foreach (int userId in usersIds)
                    //    {
                    //        if (userId != Convert.ToInt32(visibleToAllAppointmentsRow["UserId"]))  //Παρακάτω δημιουργούμε το νέο fake appointment μόνο για τους υπόλοιπους θεραπευτές (εκτός από αυτόν που του ανήκει το appointment)
                    //        {
                    //            DataRow fakeAppointmentsRow = appointmentsDT.LoadDataRow(visibleToAllAppointmentsRow.ItemArray, true);
                    //            fakeAppointmentsRow["UserId"] = userId;  //Αλλάζει το UserId για να εμφανιστεί και στον άλλο θεραπευτή.
                    //        }
                    //    }
                    //}
                    #endregion

                    #region  //Βρίσκει μόνο τα reccurent appointments που έχουν δημιουργηθεί πριν από το startDate και ένα έτος πίσω.
                    string query2Text = @"SELECT *, 
	                    (SELECT Contacts.FirstName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactFirstName,
                        (SELECT Contacts.LastName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactLastName,
                        ISNULL((SELECT Users.FullName FROM Users WHERE Users.UserId = Appointments.UserId), '') AS UserFullName,
                        ISNULL((SELECT AppointmentCategories.CategoryName FROM AppointmentCategories WHERE AppointmentCategories.AppointmentCategoryId = Appointments.AppointmentCategoryId), '') AS AppointmentCategoryName
                        FROM Appointments
                        WHERE TenantId=" + tenantId.ToString() + " AND PaymentType='Pending' AND Recurrence=1 AND (StartTime BETWEEN '@startDate' AND '@endDate') ";
                    if (contactId.HasValue)
                    {
                        query2Text += @" AND ContactId = " + contactId.ToString() + " ";
                    }
                    if (usersIds != null && usersIds.Length > 0)
                    {
                        query2Text += @" AND (";
                        foreach (Int64 userId in usersIds)
                        {
                            query2Text += @" UserId=" + userId.ToString() + " OR ";
                        }
                        query2Text = query2Text.Substring(0, query2Text.Length - 3);
                        query2Text += @" ) ";
                    }
                    query2Text += " ORDER BY " + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " ASC";
                    // Για ημερομηνίες σε sql CONVERT(VARCHAR(5),StartDate,108)

                    using (SqlDataAdapter da = new SqlDataAdapter(query2Text, connection))
                    {
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.AddYears(-2).ToString("yyyy/MM/dd 00:00:00"));  //Ημερομηνία έναρξης βάζουμε 2 χρόνια πριν το startDate.
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", startDate.AddDays(-1).ToString("yyyy/MM/dd 23:59:59"));  //Ημερομηνία λήξης βάζουμε τη προηγούμενη ημέρα από το startDate.

                        da.Fill(reccurentAppointmentsTable);
                    }
                    #endregion
                }

                appointmentsDT.Merge(reccurentAppointmentsTable);
                appointmentsDT.Columns.Add("ContactFullName", typeof(string), @"ContactLastName+ISNULL(' '+ContactFirstName,'')");
                appointmentsDT.Columns.Add("Summary", typeof(string), @"IIF(0=1, '<i style=""margin-right:5px;"" class=""fa fa-asterisk""></i>', '') + IIF(SupervisionRequest=1, '<i style=""margin-right:5px; color:orange;"" class=""fa fa-star fa-solid""></i>', '') + ISNULL(TRIM(ContactFullName), '')");

                return appointmentsDT;
            }

            public static DataTable GetCanceledAppointmentsByDates(Int64 tenantId, DateTime startDate, DateTime endDate, AppointmentsSearchByDateType appointmentsSearchByDateType, Int64? contactId, Int64[] usersIds = null)
            {
                DataTable appointmentsDT = new DataTable();
                DataTable reccurentAppointmentsTable = new DataTable();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    #region  Βρίσκει όλα τα appointments (κανονικά και με recurrence) μεταξύ των ημερομηνιών startDate και endDate, και τα Tasks που είναι visible σε όλους τους θεραπευτές
                    string queryText = @"SELECT *, 
	                    (SELECT Contacts.FirstName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactFirstName,
                        (SELECT Contacts.LastName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactLastName,
                        ISNULL((SELECT Users.FullName FROM Users WHERE Users.UserId = Appointments.UserId), '') AS UserFullName,
                        ISNULL((SELECT AppointmentCategories.CategoryName FROM AppointmentCategories WHERE AppointmentCategories.AppointmentCategoryId = Appointments.AppointmentCategoryId), '') AS AppointmentCategoryName
                        FROM Appointments
                        WHERE TenantId=" + tenantId.ToString() + " AND Canceled=1 AND ((" + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " BETWEEN '@startDate' AND '@endDate') " +
                        " OR VisibleToAll=1)";
                    if (contactId.HasValue)
                    {
                        queryText += @" AND ContactId = " + contactId.ToString() + " ";
                    }
                    if (usersIds != null && usersIds.Length > 0)
                    {
                        queryText += @" AND (";
                        foreach (Int64 userId in usersIds)
                        {
                            queryText += @" UserId=" + userId.ToString() + " OR ";
                        }
                        queryText = queryText.Substring(0, queryText.Length - 3);
                        queryText += @" ) ";
                    }
                    queryText += " ORDER BY " + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " ASC";
                    // Για ημερομηνίες σε sql CONVERT(VARCHAR(5),StartDate,108)


                    using (SqlDataAdapter da = new SqlDataAdapter(queryText, connection))
                    {
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.ToString("yyyy/MM/dd 00:00:00"));
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", endDate.ToString("yyyy/MM/dd 23:59:59"));

                        da.Fill(appointmentsDT);
                    }

                    #endregion

                    //#region  //Βρίσκει μόνο τα reccurent appointments που έχουν δημιουργηθεί πριν από το startDate και ένα έτος πίσω.
                    //string query2Text = @"SELECT *, 
                    // (SELECT Contacts.FirstName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactFirstName,
                    //    (SELECT Contacts.LastName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactLastName,
                    //    ISNULL((SELECT Users.FullName FROM Users WHERE Users.UserId = Appointments.UserId), '') AS UserFullName,
                    //    ISNULL((SELECT AppointmentCategories.CategoryName FROM AppointmentCategories WHERE AppointmentCategories.AppointmentCategoryId = Appointments.AppointmentCategoryId), '') AS AppointmentCategoryName
                    //    FROM Appointments
                    //    WHERE TenantId=" + tenantId.ToString() + " AND PaymentType='Pending' AND Recurrence=1 AND (StartTime BETWEEN '@startDate' AND '@endDate') ";
                    //if (contactId.HasValue)
                    //{
                    //    query2Text += @" AND ContactId = " + contactId.ToString() + " ";
                    //}
                    //if (usersIds != null && usersIds.Length > 0)
                    //{
                    //    query2Text += @" AND (";
                    //    foreach (Int64 userId in usersIds)
                    //    {
                    //        query2Text += @" UserId=" + userId.ToString() + " OR ";
                    //    }
                    //    query2Text = query2Text.Substring(0, query2Text.Length - 3);
                    //    query2Text += @" ) ";
                    //}
                    //query2Text += " ORDER BY " + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " ASC";
                    //// Για ημερομηνίες σε sql CONVERT(VARCHAR(5),StartDate,108)

                    //using (SqlDataAdapter da = new SqlDataAdapter(query2Text, connection))
                    //{
                    //    da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.AddYears(-2).ToString("yyyy/MM/dd 00:00:00"));  //Ημερομηνία έναρξης βάζουμε 2 χρόνια πριν το startDate.
                    //    da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", startDate.AddDays(-1).ToString("yyyy/MM/dd 23:59:59"));  //Ημερομηνία λήξης βάζουμε τη προηγούμενη ημέρα από το startDate.

                    //    da.Fill(reccurentAppointmentsTable);
                    //}
                    //#endregion
                }

                appointmentsDT.Merge(reccurentAppointmentsTable);
                appointmentsDT.Columns.Add("ContactFullName", typeof(string), @"ContactLastName+ISNULL(' '+ContactFirstName,'')");
                appointmentsDT.Columns.Add("Summary", typeof(string), @"IIF(0=1, '<i style=""margin-right:5px;"" class=""fa fa-asterisk""></i>', '') + IIF(SupervisionRequest=1, '<i style=""margin-right:5px; color:orange;"" class=""fa fa-star fa-solid""></i>', '') + ISNULL(TRIM(ContactFullName), '')");

                return appointmentsDT;
            }

            public static MentalViewDataSet GetUpcomingRecurrentAppointment(DateTime startTime, string customRecurrenceId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    Data.Business.ConfigureDataSet(ref ds);
                    ds.EnforceConstraints = false;

                    connection.Open();

                    MentalViewDataSetTableAdapters.AppointmentsTableAdapter AppointmentsTA = new MentalViewDataSetTableAdapters.AppointmentsTableAdapter();
                    AppointmentsTA.Connection = connection;
                    AppointmentsTA.FillByUpcomingRecurrentAppointments(ds.Appointments, customRecurrenceId, startTime.Date);

                    connection.Close();

                    return ds;
                }
            }

            public static DataTable GetEventsByDates(Int64 tenantId, DateTime startDate, DateTime endDate, AppointmentsSearchByDateType appointmentsSearchByDateType, Int64? contactId, Int64[] usersIds = null)
            {
                DataTable appointmentsDT = new DataTable();
                DataTable reccurentAppointmentsTable = new DataTable();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    #region  Βρίσκει όλα τα appointments (κανονικά και με recurrence) μεταξύ των ημερομηνιών startDate και endDate, και τα Tasks που είναι visible σε όλους τους θεραπευτές
                    string queryText = @"SELECT *, 
	                    (SELECT Contacts.FirstName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactFirstName,
                        (SELECT Contacts.LastName FROM Contacts WHERE Contacts.ContactId = Appointments.ContactId) AS ContactLastName,
                        ISNULL((SELECT Users.FullName FROM Users WHERE Users.UserId = Appointments.UserId), '') AS UserFullName,
                        ISNULL((SELECT AppointmentCategories.CategoryName FROM AppointmentCategories WHERE AppointmentCategories.AppointmentCategoryId = Appointments.AppointmentCategoryId), '') AS AppointmentCategoryName
                        FROM Appointments
                        WHERE TenantId=" + tenantId.ToString() + " AND AppointmentType='Event' AND ((" + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " BETWEEN '@startDate' AND '@endDate') " +
                        " OR VisibleToAll=1)";
                    if (contactId.HasValue)
                    {
                        queryText += @" AND ContactId = " + contactId.ToString() + " ";
                    }
                    if (usersIds != null && usersIds.Length > 0)
                    {
                        queryText += @" AND (";
                        foreach (Int64 userId in usersIds)
                        {
                            queryText += @" UserId=" + userId.ToString() + " OR ";
                        }
                        queryText = queryText.Substring(0, queryText.Length - 3);
                        queryText += @" ) ";
                    }
                    queryText += " ORDER BY " + (appointmentsSearchByDateType == AppointmentsSearchByDateType.StartTime ? "StartTime" : "EndTime") + " ASC";
                    // Για ημερομηνίες σε sql CONVERT(VARCHAR(5),StartDate,108)


                    using (SqlDataAdapter da = new SqlDataAdapter(queryText, connection))
                    {
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@startDate", startDate.ToString("yyyy/MM/dd 00:00:00"));
                        da.SelectCommand.CommandText = da.SelectCommand.CommandText.Replace("@endDate", endDate.ToString("yyyy/MM/dd 23:59:59"));

                        da.Fill(appointmentsDT);
                    }

                    ////Βρίσκει τα appointments που είναι visisble σε όλους, δημιουργεί αντίγραφα με το UserId των άλλων θεραπευτών ώστε να εμφανιστεί στο ημερολόγιο όλων.
                    //DataRow[] visibleToAllAppointmentRows = appointmentsDT.Select("VisibleToAll=1");
                    //foreach (DataRow visibleToAllAppointmentsRow in visibleToAllAppointmentRows)
                    //{
                    //    foreach (int userId in usersIds)
                    //    {
                    //        if (userId != Convert.ToInt32(visibleToAllAppointmentsRow["UserId"]))  //Παρακάτω δημιουργούμε το νέο fake appointment μόνο για τους υπόλοιπους θεραπευτές (εκτός από αυτόν που του ανήκει το appointment)
                    //        {
                    //            DataRow fakeAppointmentsRow = appointmentsDT.LoadDataRow(visibleToAllAppointmentsRow.ItemArray, true);
                    //            fakeAppointmentsRow["UserId"] = userId;  //Αλλάζει το UserId για να εμφανιστεί και στον άλλο θεραπευτή.
                    //        }
                    //    }
                    //}
                    #endregion


                }

                //appointmentsDT.Merge(reccurentAppointmentsTable);
                appointmentsDT.Columns.Add("ContactFullName", typeof(string), @"ContactLastName+ISNULL(' '+ContactFirstName,'')");
                appointmentsDT.Columns.Add("Summary", typeof(string), @"IIF(0=1, '<i style=""margin-right:5px;"" class=""fa fa-asterisk""></i>', '') + IIF(SupervisionRequest=1, '<i style=""margin-right:5px; color:orange;"" class=""fa fa-star fa-solid""></i>', '') + ISNULL(TRIM(ContactFullName), '')");

                return appointmentsDT;
            }

            public static List<string> GetOccupiedRooms(Int64 tenantId, DateTime startDate, DateTime endDate)
            {
                DataTable dt = new DataTable();
                List<string> rooms = new List<string>();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(@"SELECT Room FROM Appointments WHERE TenantId=" + tenantId.ToString() + " AND ((StartTime  BETWEEN '" + startDate.ToString("yyyy-MM-dd HH:mm") + @"' AND '" + endDate.ToString("yyyy-MM-dd HH:mm") + @"')
                        OR(EndTime  BETWEEN '" + startDate.ToString("yyyy-MM-dd HH:mm") + @"' AND '" + endDate.ToString("yyyy-MM-dd HH:mm") + @"') 
                        OR (StartTime < '" + startDate.ToString("yyyy-MM-dd HH:mm") + @"' AND EndTime > '" + startDate.ToString("yyyy-MM-dd HH:mm") + @"'))", connection))
                    {
                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(dt);
                        }
                    }
                }

                foreach(DataRow dr in dt.Rows)
                {
                    rooms.Add(dr["Room"].ToString());
                }

                return rooms;
            }

            public static void DeleteAppointment(Int64 appointmentId)
            {
                string sqlQuery = "DELETE FROM Appointments WHERE AppointmentId=" + appointmentId.ToString();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(sqlQuery, connection))
                    {
                        connection.Open();
                        cmd.CommandType = CommandType.Text;
                        cmd.ExecuteNonQuery();
                    }
                }
            }

            public static DataTable GetPaymentTypes()
            {
                DataTable dt = new DataTable();
                dt.Columns.Add("PaymentTypeValue", typeof(string));
                dt.Columns.Add("PaymentTypeText", typeof(string));

                dt.Rows.Add(new string[] { "Check", "Επιταγή" });
                dt.Rows.Add(new string[] { "Cash", "Μετρητά" });
                dt.Rows.Add(new string[] { "Card", "Κάρτα" });

                dt.AcceptChanges();
                return dt;
            }

            //public static MentalViewDataSet.TaskImagesDataTable GetTaskImages(Int64 taskId)
            //{
            //    try
            //    {
            //        using (SqlConnection connection = new SqlConnection(connectionString))
            //        {
            //            MentalViewDataSet ds = new MentalViewDataSet();
            //            ds.EnforceConstraints = false;
            //            MentalViewDataSetTableAdapters.TableAdapterManager taManager = new MentalViewDataSetTableAdapters.TableAdapterManager();
            //            InitTableAdapterManager(ref taManager, connection);

            //            using (taManager)
            //            {
            //                taManager.TaskImagesTableAdapter.Fill(ds.TaskImages);
            //            }
            //            return ds.TaskImages;
            //        }
            //    }
            //    catch (Exception exp)
            //    {
            //        throw exp;
            //    }
            //    finally
            //    {

            //    }
            //}

            //public static string GetTaskImage(Int64 taskImageId)
            //{
            //    try
            //    {
            //        using (SqlConnection connection = new SqlConnection(connectionString))
            //        {
            //            MentalViewDataSet ds = new MentalViewDataSet();
            //            ds.EnforceConstraints = false;
            //            MentalViewDataSetTableAdapters.TableAdapterManager taManager = new MentalViewDataSetTableAdapters.TableAdapterManager();
            //            InitTableAdapterManager(ref taManager, connection);

            //            using (taManager)
            //            {
            //                taManager.TaskImagesTableAdapter.FillByTaskImageId(ds.TaskImages, taskImageId);
            //            }

            //            if (ds.TaskImages.Count > 0)
            //            {
            //                string imagesFolderPath = Data.Business.TenantsBusiness.GetTenantTaskImagesFolderPath(ds.TaskImages[0].TenantId, ds.TaskImages[0].TaskId);
            //                return imagesFolderPath + ds.TaskImages[0].FileName;
            //            }
            //            else
            //            {
            //                return "";
            //            }
            //        }
            //    }
            //    catch (Exception exp)
            //    {
            //        throw exp;
            //    }
            //    finally
            //    {

            //    }
            //}
        }

        public class AppointmentCategoriesBusiness
        {
            public static MentalViewDataSet.AppointmentCategoriesDataTable GetAllAppointmentCategories(Int64 tenantId)
            {
                MentalViewDataSet ds = new MentalViewDataSet();
                ds.EnforceConstraints = false;
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        MentalViewDataSetTableAdapters.TableAdapterManager taManager = new MentalViewDataSetTableAdapters.TableAdapterManager();
                        InitTableAdapterManager(ref taManager, connection);

                        using (taManager)
                        {
                            taManager.AppointmentCategoriesTableAdapter.FillByTenantId(ds.AppointmentCategories, tenantId);
                            return ds.AppointmentCategories;
                        }
                    }
                }
                catch (Exception exp)
                {
                    throw exp;
                }
                finally
                {

                }
                //return ds.AppointmentCategories;
            }

            public static MentalViewDataSet GetByAppointmentCategoryId(Int64 appointmentCategoryId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    Data.Business.ConfigureDataSet(ref ds);
                    MentalViewDataSetTableAdapters.AppointmentCategoriesTableAdapter appointmentCategoriesTA = new MentalViewDataSetTableAdapters.AppointmentCategoriesTableAdapter();
                    appointmentCategoriesTA.Connection = connection;
                    appointmentCategoriesTA.FillByAppointmentCategoryId(ds.AppointmentCategories, appointmentCategoryId);

                    return ds;
                }
            }
        }

        public class UsersBusiness
        {
            /// <summary>
            /// Saves only data of Users.
            /// </summary>
            public static void SaveUsers(ref MentalViewDataSet ds)
            {
                SqlConnection connection = null;

                try
                {
                    using (connection = new SqlConnection(connectionString))
                    {
                        using (MentalViewDataSetTableAdapters.UsersTableAdapter usersTA = new MentalViewDataSetTableAdapters.UsersTableAdapter())
                        {
                            usersTA.Connection = connection;
                            usersTA.Update(ds.Users);

                            ds.Users.AcceptChanges();
                        }
                    }
                }
                catch (Exception exp)
                {
                    ds.Users.RejectChanges();
                    throw exp;
                }
                finally
                {
                    if (connection != null)
                    {
                        connection.Close();
                    }
                }
            }

            public static MentalViewDataSet GetUserById(Int64 userId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    Data.Business.ConfigureDataSet(ref ds);
                    MentalViewDataSetTableAdapters.UsersTableAdapter customersTA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
                    customersTA.Connection = connection;
                    customersTA.FillByUserId(ds.Users, userId);

                    return ds;
                }
            }

            public static MentalViewDataSet GetUsers(Int64 tenantId, string filter, string sortExpression, Data.SortDirection? sortDirection, int pageIndex, int pageSize, out int totalCount)
            {
                MentalViewDataSet ds = new MentalViewDataSet();
                totalCount = 0;

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("GetUsers", connection))
                    {
                        DataSet genericDS = new DataSet();

                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add("@TenantId", SqlDbType.BigInt).Value = tenantId;
                        cmd.Parameters.Add("@Filter", SqlDbType.NVarChar).Value = filter;
                        cmd.Parameters.Add("@OrderBy", SqlDbType.NVarChar).Value = sortExpression;
                        if (sortDirection.HasValue)
                        {
                            cmd.Parameters.Add("@SortType", SqlDbType.NVarChar).Value = sortDirection == SortDirection.Ascending ? "ASC" : "DESC";
                        }
                        cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                        cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;

                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(genericDS);
                        }

                        ds.Users.Merge(genericDS.Tables[0]);
                        ds.AcceptChanges();
                        totalCount = int.Parse(genericDS.Tables[1].Rows[0][0].ToString());
                    }
                }

                return ds;
            }

            public static DataTable GetAllUsersList(Int64 tenantId)
            {
                DataTable dt = new DataTable();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM Users WHERE TenantId=" + tenantId + " ORDER BY FullName", connection))
                    {
                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(dt);
                        }
                    }
                }

                return dt;
            }


            public static MentalViewDataSet.UsersDataTable GetAllDoctorUsersList(Int64 tenantId)
            {
                MentalViewDataSet ds = new MentalViewDataSet();
                ds.EnforceConstraints = false;

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    Data.Business.ConfigureDataSet(ref ds);
                    MentalViewDataSetTableAdapters.UsersTableAdapter doctorsTA = new MentalViewDataSetTableAdapters.UsersTableAdapter();
                    doctorsTA.Connection = connection;
                    doctorsTA.FillWithAllDoctors(ds.Users, tenantId);

                    return ds.Users;
                }

                //using (SqlConnection connection = new SqlConnection(connectionString))
                //{
                //    using (SqlCommand cmd = new SqlCommand("SELECT * FROM Users WHERE TenantId=" + tenantId + " AND IsDoctor=1 ORDER BY FullName", connection))
                //    {
                //        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                //        {
                //            da.Fill(dt);
                //        }
                //    }
                ////}

                // return dt;
            }

            public static void DeleteUser(Int64 userId)
            {
                string sqlQuery = "DELETE FROM Users WHERE UserId=" + userId.ToString();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(sqlQuery, connection))
                    {
                        connection.Open();
                        cmd.CommandType = CommandType.Text;
                        cmd.ExecuteNonQuery();
                    }
                }
            }

            public static bool CheckUsernameExists(Int64 userId, string username)
            {
                SqlDataReader reader;
                SqlCommand command;

                int result = 0;
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        command = new SqlCommand();
                        command.CommandText = @"SELECT Count(*) AS Result FROM Users WHERE [Users].[Username]='" + username + "' AND UserId !=" + userId.ToString() + ";";
                        command.Connection = connection;
                        connection.Open();

                        reader = command.ExecuteReader();
                        if (reader == null)
                        {
                            throw new Exception("variable reader=null");
                        }
                        reader.Read();
                        result = reader.GetInt32(0);
                        reader.Close();
                        connection.Close();
                    }
                }
                finally
                {

                }

                if (result >= 1) //Αν βρέθηκε τουλάχιστον ένα πρόσωπο
                {
                    return true;
                }
                else  //Αν δεν βρεθηκε πρόσωπο 
                {
                    return false;
                }
            }
        }

        public class TenantsBusiness
        {
            //public static string GetTenantAppointmentImagesFolderPath(Int64 tenantId, Int64 taskId)
            //{
            //    System.Web.UI.Page page = new System.Web.UI.Page();
            //    return Path.Combine(page.Server.MapPath("~/TenantsFiles/" + tenantId.ToString() + "/" + taskId.ToString() + "/Images/"));
            //}
        }

        public class EmailTemplatesBusiness
        {
            /// <summary>
            /// Saves only data of EmailTemplates.
            /// </summary>
            public static void SaveEmailTemplates(ref MentalViewDataSet ds)
            {
                SqlConnection connection = null;

                try
                {
                    using (connection = new SqlConnection(connectionString))
                    {
                        using (MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter emailTemplatesTA = new MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter())
                        {
                            emailTemplatesTA.Connection = connection;
                            emailTemplatesTA.Update(ds.EmailTemplates);

                            ds.EmailTemplates.AcceptChanges();
                        }
                    }
                }
                catch (Exception exp)
                {
                    ds.EmailTemplates.RejectChanges();
                    throw exp;
                }
                finally
                {
                    if (connection != null)
                    {
                        connection.Close();
                    }
                }
            }

            public static MentalViewDataSet GetEmailTemplateById(Int64 emailTemplateId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    Data.Business.ConfigureDataSet(ref ds);
                    MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter emailTemplatesTA = new MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter();
                    emailTemplatesTA.Connection = connection;
                    emailTemplatesTA.FillByEmailTemplateId(ds.EmailTemplates, emailTemplateId);

                    return ds;
                }
            }

            //public static MentalViewDataSet GetEmailTemplates(Int64 tenantId, string filter, string sortExpression, Data.SortDirection? sortDirection, int pageIndex, int pageSize, out int totalCount)
            //{
            //    MentalViewDataSet ds = new MentalViewDataSet();
            //    totalCount = 0;

            //    using (SqlConnection connection = new SqlConnection(connectionString))
            //    {
            //        using (SqlCommand cmd = new SqlCommand("GetEmailTemplates", connection))
            //        {
            //            DataSet genericDS = new DataSet();

            //            cmd.CommandType = CommandType.StoredProcedure;
            //            cmd.Parameters.Add("@TenantId", SqlDbType.BigInt).Value = tenantId;
            //            cmd.Parameters.Add("@Filter", SqlDbType.NVarChar).Value = filter;
            //            cmd.Parameters.Add("@OrderBy", SqlDbType.NVarChar).Value = sortExpression;
            //            if (sortDirection.HasValue)
            //            {
            //                cmd.Parameters.Add("@SortType", SqlDbType.NVarChar).Value = sortDirection == SortDirection.Ascending ? "ASC" : "DESC";
            //            }
            //            cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
            //            cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;

            //            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            //            {
            //                da.Fill(genericDS);
            //            }

            //            ds.EmailTemplates.Merge(genericDS.Tables[0]);
            //            ds.AcceptChanges();
            //            totalCount = int.Parse(genericDS.Tables[1].Rows[0][0].ToString());
            //        }
            //    }

            //    return ds;
            //}

            public static MentalViewDataSet GetAllEmailTemplatesList(Int64 tenantId)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    MentalViewDataSet ds = new Data.MentalViewDataSet();
                    ds.EnforceConstraints = false;
                    Data.Business.ConfigureDataSet(ref ds);
                    MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter emailTemplatesTA = new MentalViewDataSetTableAdapters.EmailTemplatesTableAdapter();
                    emailTemplatesTA.Connection = connection;
                    emailTemplatesTA.Fill(ds.EmailTemplates);

                    return ds;
                }

                //DataTable dt = new DataTable();

                //using (SqlConnection connection = new SqlConnection(connectionString))
                //{
                //    using (SqlCommand cmd = new SqlCommand("SELECT * FROM EmailTemplates WHERE TenantId=" + tenantId + " ORDER BY Title", connection))
                //    {
                //        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                //        {
                //            da.Fill(dt);
                //        }
                //    }
                //}

                //return dt;
            }

            public static void DeleteEmailTemplate(Int64 emailTemplateId)
            {
                string sqlQuery = "DELETE FROM EmailTemplates WHERE EmailTemplateId=" + emailTemplateId.ToString();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(sqlQuery, connection))
                    {
                        connection.Open();
                        cmd.CommandType = CommandType.Text;
                        cmd.ExecuteNonQuery();
                    }
                }
            }

            public static bool CheckEmailTemplateTitleExists(Int64 emailTemplateId, string title)
            {
                SqlDataReader reader;
                SqlCommand command;

                int result = 0;
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        command = new SqlCommand();
                        command.CommandText = @"SELECT Count(*) AS Result FROM EmailTemplates WHERE [EmailTemplates].[Title]='" + title + "' AND EmailTemplateId !=" + emailTemplateId.ToString() + ";";
                        command.Connection = connection;
                        connection.Open();

                        reader = command.ExecuteReader();
                        if (reader == null)
                        {
                            throw new Exception("variable reader=null");
                        }
                        reader.Read();
                        result = reader.GetInt32(0);
                        reader.Close();
                        connection.Close();
                    }
                }
                finally
                {

                }

                if (result >= 1) //Αν βρέθηκε τουλάχιστον ένα πρόσωπο
                {
                    return true;
                }
                else  //Αν δεν βρεθηκε πρόσωπο 
                {
                    return false;
                }
            }
        }

    }
}
