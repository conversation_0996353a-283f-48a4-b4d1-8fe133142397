/*!
*  filename: ej.localetexts.fr-FR.min.js
*  version : 18.1.0.42
*  Copyright Syncfusion Inc. 2001 - 2020. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
ej.ReportDesigner.Locale["fr-FR"]={itemPanel:{waterMarkText:"Rechercher des widgets",noDataFound:"Aucun résultat..",customCategory:"Codes barres",customRptItemName:"1D code à barre",dataRequirements:"Exigences en matière de données",customTooltip:{tooltip:{requirements:"Ajoutez un élément de rapport à la zone du concepteur.",description:"Affiche l'élément de rapport personnalisé.",title:"Rapport personnalisé"}},groupItems:{basicItems:{groupName:"Articles de base",Items:{line:{displayText:"Ligne",tooltip:{requirements:"Pour séparer une région par une ligne dans les sections de rapport.",description:"Élément graphique pour séparer la région du rapport.",title:"Ligne"}},image:{displayText:"Image",tooltip:{requirements:"Pour afficher une image de la base de données, incorporez l'image.",description:"Affiche les images.",title:"Image"}},textBox:{displayText:"Zone de texte",tooltip:{requirements:"Ajouter du texte.",description:"Affiche le texte statique et dynamique.",title:"Zone de texte"}},rectangle:{displayText:"Rectangle",tooltip:{requirements:"Ajouter un élément de rapport à la zone du concepteur.",description:"Combinez un ou plusieurs éléments de rapport à l'intérieur",title:"Rectangle"}}}},comparison:{groupName:"Comparaison",Items:{column:{displayText:"Colonne",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Compare les valeurs d'un ensemble d'éléments non ordonnés dans différentes catégories en utilisant les barres verticales disposées horizontalement.",title:"Colonne"}},bar:{displayText:"Bar",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Compare les valeurs d'un ensemble d'éléments non ordonnés dans différentes catégories en utilisant les barres horizontales disposées verticalement.",title:"Bar"}},stackedColumn:{displayText:"Colonne empilée",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Compare plusieurs mesures à l'aide des barres empilées verticalement.",title:"Colonne empilée"}},stackedBar:{displayText:"Barre empilée",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Compare plusieurs mesures à l'aide des barres empilées horizontalement.",title:"Barre empilée"}},stackedColumnPercent:{displayText:"Colonne empilée100%",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Compare plusieurs mesures en tant que parties d'un tout en utilisant les barres empilées verticalement.",title:"Colonne empilée100%"}},stackedBarPercent:{displayText:"Barre empilée100%",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Compare plusieurs mesures en tant que parties d'un ensemble à l'aide des barres empilées horizontalement.",title:"Barre empilée100%"}}}},proportion:{groupName:"Proportion",Items:{pie:{displayText:"Tarte",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Présente les proportions de la contribution de chaque article au total sous forme de tranches de tarte.",title:"Tarte"}},explodedPie:{displayText:"Tarte Explosée",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Souligne une tranche individuelle d'un camembert.",title:"Tarte Explosée"}},doughnut:{displayText:"Beignet",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Présente les proportions de la contribution de chaque article au total sous forme de tranches de beignets.",title:"Beignet"}},pyramid:{displayText:"Pyramide",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Présente la comparaison proportionnelle entre les valeurs de manière progressivement croissante.",title:"Pyramide"}},funnel:{displayText:"Entonnoir",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Présente la comparaison proportionnelle entre les valeurs de manière progressivement décroissante.",title:"Entonnoir"}}}},distribution:{groupName:"Distribution",Items:{area:{displayText:"Région",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Compare les valeurs d'un ensemble d'éléments non ordonnés dans différentes catégories à travers les courbes remplies ordonnées verticalement.",title:"Région"}},smoothArea:{displayText:"Zone lisse",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Compare les valeurs d'un ensemble d'éléments non ordonnés dans différentes catégories à travers les courbes remplies ordonnées verticalement avec une surface lisse.",title:"Zone lisse"}},stackedArea:{displayText:"Zone empilée",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Compare plusieurs mesures à travers les courbes remplies empilées verticalement.",title:"Zone empilée"}},stackedAreaPercent:{displayText:"Zone empilée100%",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Compare plusieurs mesures en tant que parties d'un tout à travers les courbes remplies empilées verticalement.",title:"Zone empilée100%"}},line:{displayText:"Ligne",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Présente les tendances à analyser au fil du temps avec des points de données connectés en utilisant les lignes droites.",title:"Ligne"}},smoothLine:{displayText:"Ligne lisse",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Compare la distribution des valeurs sur une période de temps connectée en utilisant les lignes lisses.",title:"Ligne lisse"}},steppedLine:{displayText:"Ligne en escalier",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Compare la distribution des valeurs sur une période de temps connectée en utilisant les lignes échelonnées.",title:"Ligne en escalier"}},lineWithMarkers:{displayText:"Ligne avec des marqueurs",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Comparez les changements sur la même période pour plusieurs groupes",title:"Ligne avec des marqueurs"}},smoothLineWithMarkers:{displayText:"Ligne lisse avec des marqueurs",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Les valeurs tracées sont représentées par un point marqueur et ces points sont connectés via une ligne lisse.",title:"Ligne lisse avec des marqueurs"}},scatter:{displayText:"Dispersion",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Affiche une série sous forme d'un ensemble de points et les valeurs sont représentées par la position des points sur le graphique.",title:"Dispersion"}},bubble:{displayText:"Bulle",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Affiche la différence entre deux valeurs d'un point de données en fonction de la taille de la bulle.",title:"Bulle"}},polar:{displayText:"Polaire",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Affiche une série sous la forme d'un ensemble de points regroupés par catégorie sur un cercle de 360 ​​degrés.",title:"Polaire"}},radar:{displayText:"Radar",tooltip:{requirements:"1 ou plusieurs valeurs et 1 ou plusieurs colonnes.",description:"Affiche une série sous forme de ligne ou de zone circulaire.",title:"Radar"}}}},dataRegions:{groupName:"Régions de données",Items:{tablix:{displayText:"Table",tooltip:{requirements:"1 ou plusieurs lignes / colonnes.",description:"Affiche les données de rapport paginées dans les cellules.",title:"Table"}},list:{displayText:"liste",tooltip:{requirements:"1 ou plusieurs lignes / colonnes.",description:"Une liste affiche les données dans un format libre. Placez les champs n'importe où dans la liste.",title:"liste"}}}},subReports:{groupName:"Sous rapports",Items:{subreport:{displayText:"Sous-rapport",tooltip:{requirements:"Afficher / incorporer le rapport dans le rapport principal.",description:"Affiche un autre rapport dans le corps du rapport principal.",title:"Sous-rapport"}}}}}},toolbar:{newReport:"Nouveau",open:"ouvrir",openMenu:{fromDevice:" de l appareil",fromServer:"du serveur"},save:"enregistrer",saveMenu:{saveLabel:"enregistrer",saveAs:"enregistrer sous",saveAsMenu:{saveToDevice:"à l appareil",saveToServer:"au serveur"}},cut:"Couper",copy:"copie",paste:"coller",deleteItem:"effacer",undo:"annuler",redo:"refaire",zoomIn:"agrandir",zoomOut:"Dézoomer",header:"entête",footer:"bas de page",order:"Ordre",orderMenu:{sendBackward:"Envoyer vers l'arrière",bringForward:"Avancer",sendToBack:"Envoyer au fond",bringToFront:"Mettre au premier plan"},left:"Aligner à gauche",center:"Centre",right:"Aligner à droite",top:"Top Align",middle:"Milieu",bottom:"Alignement du fond",distributeHorizontally:"Distribuer horizontalement",distributeVertically:"Distribuer verticalement",centerHorizontally:"Centrer horizontalement",centerVertically:"Centrer verticalement",sizing:"Dimensionnement",sizingMenu:{sameSize:"Même taille",sameWidth:"Même largeur",sameHeight:"Même taille"},alignToGrid:"Aligner sur la grille",sizeToGrid:"Taille à la grille",gridLine:"le quadrillage",snapToShape:"Aligner sur la forme",fullScreen:"plein écran",preview:"Aperçu",reportUpload:{alertLabel:"Télécharger",alertMessage:"Erreur lors du téléchargement du fichier. Veuillez télécharger à nouveau"},grouping:"Regroupement",view:"Vue"},newReport:{cancel:"Annuler",create:"créer",title:"nouveau rapport",fileName:"nom de fichier",waterMark:"nom du rapport",close:"Fermer"},reportAction:{enableLink:"Activer le lien",linkTo:"Lié à",report:"rapport",url:"URL"},linkReport:{reportCaption:"rapport",setParameter:"Définir les paramètres"},imageProperty:{basicSettings:{categoryName:"Paramètres de base",source:"La source",sourceTypes:{external:"Externe",embedded:"Intégré",database:"Base de données"},value:"Valeur",mimeType:"Type MIME",mimeTypes:{bmp:"image/bmp",jpeg:"image/jpeg",gif:"image/gif",png:"image/png",xPng:"image/x-png"}},categoryName:"Lien",linkReport:"Rapport de lien",appearance:{categoryName:"Apparence",styleTooltip:"Style",colorTooltip:"Couleur",sizeTooltip:"Taille",borderTypes:{border:"Frontière",borderLeft:"La gauche",borderTop:"Haut",borderRight:"Droite",borderBottom:"Bas"},borderStyles:{solid:"Solide",none:"Aucun",double:"Double",dashed:"En pointillé",dotted:"Pointé"}},size:{categoryName:"Taille",paddingTypes:{padding:"Rembourrage",paddingLeft:"La gauche",paddingTop:"Haut",paddingRight:"Droite",paddingBottom:"Bas"},sizing:"Dimensionnement",sizeTypes:{auto:"Taille automatique",fit:"En forme",proportional:"FitProportional",clip:"Agrafe"}},position:{categoryPosition:"Position",positionLabel:"Position",left:"La gauche",top:"Haut",sizeLabel:"Taille",width:"Largeur",height:"la taille"},visibility:{categoryName:"Visibilité",visible:"Visible",toggleItem:"Basculer l'élément"}},chartProperty:{commonProperties:{showBorder:"Afficher la bordure",border:{border:"Frontière",borderLeft:"La gauche",borderTop:"Haut",borderRight:"Droite",borderBottom:"Bas"},background:"Couleur de fond",font:"Police de caractère",fontStyle:"Le style de police",labelRotation:"Rotation d'étiquette",categoryAxis:"Axe de catégorie",valueAxis:"Axe de valeur",defaultText:"Défaut",auto:"Auto",borderStyles:{solid:"Solide",none:"Aucun",double:"Double",dashed:"En pointillé",dotted:"Pointé",dashDot:"point de tiret",dashDotDot:"point de pointillé"},horizontalAlignments:{near:"Près",far:"Loin"},textAlignments:{right:"Droite",bottom:"Bas",center:"Centre",topLeft:"En haut à gauche",topCenter:"Centre supérieur",topRight:"En haut à droite",rightTop:"En haut a droite",rightCenter:"Centre droit",rightBottom:"Bas droit",bottomLeft:"En bas à gauche",bottomCenter:"Centre inférieur",bottomRight:"En bas à droite",leftTop:"En haut à gauche",leftCenter:"Centre gauche",leftBottom:"En bas à gauche"},fontStyleTypes:{normal:"Ordinaire",italic:"Italique"},fontWeightTypes:{light:"Lumière",bold:"Audacieux"}},basicSettings:{categoryName:"Paramètres de base",showLegend:{showLegendText:"Afficher la légende",title:"Titre",titleFont:"Police de titre",titleFontStyle:"Style de police du titre",titleAlignment:"Alignement du titre",legendPosition:"Position de légende",enableCustomBounds:"Activer les limites personnalisées"},chooseSeries:"Choisissez la série",showMarker:{showMarkerText:"Afficher le marqueur",color:"Couleur",markerType:"Type de marqueur",markerTypes:{square:"Carré",circle:"Cercle",diamond:"diamant",triangle:"Triangle",cross:"Traverser",star5:"Star5"},size:"Taille"},showDataLabel:{showDataLabelText:"Afficher l'étiquette de données",dataLabelFormat:"Format",dataLabelText:"Étiquette",dataLabelValueAsText:"utiliser la valeur comme étiquette",dataLabelTypes:{valueX:"#VALX",valueY:"#VALY",valueY2:"#VALY2",valueY3:"#VALY3",valueY4:"#VALY4",valueY5:"#VALY5",valueY6:"#VALY6",index:"#INDICE",percent:"#POUR CENT",total:"#TOTAL",axisLabel:"#étiquette d'axe"}},enableSmartLabel:{smartLabelText:"activer l'étiquette intelligente",labelStyle:"Style d'étiquette",value:"Valeur",smartLabelPositions:{outside:"À l'extérieur",inside:"À l'intérieur",outsideInColumn:"En dehors de la colonne"},smartLabelStyles:{pieLabelStyle:"style d'étiquette de tarte",funnelLabelStyle:"Style d'étiquette Entonnoir",pyramidLabelStyle:"Style d'étiquette pyramide",barLabelStyle:"Style d'étiquette de barre",smartLabelStyle:"style d'étiquette"}},seriesBorder:"Série Border",seriesColor:"Couleur de la série"},categoryName:"Apparence",customAttribute:{categoryName:"Attributs personnalisés",userDefined:"Défini par l'utilisateur",alertHeader:"Graphique ReportItem",alertMessage:"Format d'attributs personnalisés non valide.Exemple correct: 'AttrName1 = Valeur1, AttrName2 = Valeur2 '."},chartArea:{categoryName:"Zone de graphique",colorPalette:"Palette de couleurs",colorPaletteTypes:{earthTones:"Tons de terre",excel:"Exceller",grayScale:"Échelle de gris",pastel:"Pastel",semiTransparent:"Semi-transparent",berry:"Baie",chocolate:"Chocolat",fire:"Feu",seaGreen:"Vert de la mer",brightPastel:"Pastel lumineux",pacific:"Pacifique",pacificLight:"Lumière du Pacifique",pacificSemiTransparent:"pacifique semi-transparent"}},title:{categoryName:"Titre",showChartTitle:"Afficher le titre du graphique",titleText:"Texte du titre",titlePosition:"position du titre"},axis:{enableAxis:"Activer l'axe",axisTitle:"Titre de l'Axe",alignment:"Alignement",lineStyle:"Style de ligne",labelOverflowMode:"Mode de dépassement d'étiquette",overFlowModeTypes:{trim:"Réduire",hide:"Cacher"},labelFont:"Label Police",labelFormat:"Format d'étiquette",enableMajorTicks:"Activer les tiques majeures",enableMinorTicks:"Activer les tiques mineures",tickProperties:{tickSize:"Taille de tique",tickColor:"Tick Couleur",tickWidth:"Largeur",length:"Longueur"},tickPosition:"Tique la position"},gridLine:{categoryName:"Ligne de la grille",gridLineStyle:{minorGridLine:"Afficher le quadrillage mineur",majorGridLineStyle:"Style de grille principal",minorGridLineStyle:"Style de grille secondaire"}},pageBreak:{categoryName:"Saut de page",enablePageBreak:"Activer le saut de page",breakLocation:"Break Lieu",breakLocationTypes:{none:"Aucun",start:"Début",end:"Fin",startAndEnd:"début et fin",between:"Entre"},pageNumberReset:"Numéro de page réinitialisé",pageName:"Nom de la page"},position:{categoryPosition:"Position",positionLabel:"Position",left:"La gauche",top:"Haut",sizeLabel:"Taille",width:"Largeur",height:"la taille"},visibility:{categoryName:"Visibilité",visible:"Visible",toggleItem:"Basculer l'élément"},fontStyleTooltip:"Style",fontWeightTooltip:"Poids",fontSizeTooltip:"Taille",fontColorTooltip:"Couleur",fontFamilyTooltip:"Famille de polices",styleTooltip:"Style",colorTooltip:"Couleur",sizeTooltip:"Taille"},lineProperty:{basicSettings:{categoryBasicSettings:"Paramètres de base",line:"Ligne",lineTypes:{solid:"Solide",dashed:"En pointillé",dotted:"Pointé"}},position:{categoryPosition:"Position",positionLabel:"Position",left:"La gauche",top:"Haut",sizeLabel:"Taille",width:"Largeur",height:"la taille"},visibility:{categoryName:"Visibilité",visible:"Visible",toggleItem:"Basculer l'élément"},styleTooltip:"Style",colorTooltip:"Couleur",sizeTooltip:"Taille"},subReportProperty:{basicSettings:{categoryBasicSettings:"Paramètres de base"},appearance:{categoryAppearance:"Apparence",borderTypes:{border:"Frontière",borderLeft:"La gauche",borderTop:"Haut",borderRight:"Droite",borderBottom:"Bas"},borderStyles:{solid:"Solide",none:"Aucun",double:"Double",dashed:"En pointillé",dotted:"Pointé"}},noRows:{noRowsLabel:"Aucune ligne",font:"Police de caractère",fontStyle:{fontStyleLabel:"Le style de police",fontItem:{defaultStyle:"Défaut",fontNormal:"Ordinaire",italic:"Italique"},fontWeight:{defaultElement:"Défaut",normal:"Ordinaire",thin:"Mince",extraLight:"Lumière supplémentaire",light:"Lumière",medium:"Moyen",semiBold:"Semi Audacieux",bold:"Audacieux",extraBold:"supplémentaire Audacieux",heavy:"Lourd"}},textDecoration:{textDecorationLabel:"Décoration de texte",defaultDecoration:"Défaut",none:"Aucun",underLine:"Souligner",overLine:"Overline",lineThrough:"Ligne à travers"},format:"Format",lineHeight:"Hauteur de la ligne",message:"Message",paddingTypes:{padding:"Rembourrage",paddingLeft:"La gauche",paddingRight:"Droite",paddingTop:"Haut",paddingBottom:"Bas"},textAlign:{textAlignLabel:"Alignement de texte",textAlignDefault:"Défaut",textAlignGeneral:"Général",textAlignLeft:"La gauche",textAlignRight:"Droite",textAlignCenter:"Centre"},verticalAlign:{verticalAlignlabel:"Alignement vertical",verticalAlignDefault:"Défaut",verticalAlignTop:"Haut",verticalAlignMiddle:"Milieu",verticalAlignBottom:"Bas"},writingMode:{writingModeLabel:"Mode d'écriture",writingModeDefault:"Défaut",writingModeHorizontal:"Horizontal",writingModeVertical:"Verticale",writingModeRotate:"Rotation270"}},visibility:{categoryName:"Visibilité",visible:"Visible",toggleItem:"Basculer l'élément"},position:{categoryPosition:"Position",positionLabel:"Position",left:"La gauche",top:"Haut",sizeLabel:"Taille",width:"Largeur",height:"la taille"},miscellaneous:{categoryMiscellaneous:"Divers",keepTogether:"Rester ensemble"},fontStyleTooltip:"Style",fontWeightTooltip:"Poids",fontSizeTooltip:"Taille",fontColorTooltip:"Couleur",fontFamilyTooltip:"Famille de polices",styleTooltip:"Style",colorTooltip:"Couleur",sizeTooltip:"Taille"},rectangleProperty:{basicSettings:{categoryBasicSettings:"Paramètres de base",styleTooltip:"Style",colorTooltip:"Couleur",sizeTooltip:"Taille",borderTypes:{border:"Frontière",borderLeft:"La gauche",borderTop:"Haut",borderRight:"Droite",borderBottom:"Bas"},borderStyles:{solid:"Solide",none:"Aucun",double:"Double",dashed:"En pointillé",dotted:"Pointé"},backGround:"Couleur de fond"},pageBreak:{pageBreak:"Saut de page",enablePageBreak:{enablePageBreak:"Activer le saut de page",breakLocation:{breakLocationLabel:"Break Lieu",none:"Aucun",start:"Début",end:"Fin",startAndEnd:"StartAndEnd",between:"Entre"},pageNumberReset:"Numéro de page réinitialisé"}},position:{categoryPosition:"Position",positionLabel:"Position",left:"La gauche",top:"Haut",sizeLabel:"Taille",width:"Largeur",height:"la taille"},visibility:{categoryName:"Visibilité",visible:"Visible",toggleItem:"Basculer l'élément"},rectangleMiscellaneous:{categoryMiscellaneous:"Divers",keepTogether:"Rester ensemble",pageName:"Nom de la page"}},browseFile:{openFile:{selectReport:"Sélectionnez le rapport",open:"Ouvrir"},saveFile:{saveAsReport:"Enregistrer comme rapport",name:"prénom",save:"sauvegarder"},close:"Fermer",cancel:"Annuler",waterMark:"Nom du rapport",emptyMessage:"Cette catégorie est vide",alertMessage:{reportServer:"Serveur de rapport",selectCategory:"Veuillez sélectionner une catégorie"},warningMessage:{fileNameLabel:"Un élément '",fileNameExist:".rdl ' existe déjà. Voulez-vous remplacer l'élément existant?",populateCategory:"Le concepteur de rapports n'a pas pu extraire les ressources du serveur de rapports"}},expressionMenu:{reset:"Réinitialiser",expression:"Expression",advanced:"Avancée"},propertyPanel:{property:"Propriétés",data:"Les données",name:"Nom",toolTipStyle:"Style",toolTipColor:"Couleur",toolTipWidth:"Largeur",setSorts:"Définir les triages",setFilters:"Définir les filtres",advancedOptions:"Options avancées",codemodules:"Code",expressionList:{top:"Haut",right:"Droite",bottom:"Bas",left:"La gauche",style:"Style",color:"Couleur",size:"Taille",fontFamily:"Famille de polices",width:"Largeur",height:"la taille",weight:"Poids",image:"Image"},alertMessage:{nameWarning:"Le nom ne peut pas être vide",nameAlert:"Le nom existe déjà",nameValidation:"Le nom ne doit pas contenir d'espaces et de caractères spéciaux"},unitType:{inchText:"dans",centimeterText:"cm",pixelText:"pixel",pointText:"pt",millimeterText:"mm",picaText:"pc"},setGroups:"Définir des groupes",addDatasource:"Ajouter une source de données",dataAlertMsg:"Aucune source de données ajoutée !"},dataSource:{newDatasource:"NOUVELLE DATASOURCE",datasource:"LES SOURCES DE DONNÉES",datasourceList:{data:"Les données",contextMenu:{editItem:"modifier",deleteItem:"Effacer",createDataSet:"Créer un Ensemble de données",cloneDatasource:"Cloner"}},datasourceType:{existOption:"Existant",newOption:"Créer un nouveau",selectDatasoure:"Sélectionnez la source de données",connectDatasource:"Connecter la source de données",datasourceType:"Choisissez le type à connecter",sqlLabel:"SQL",sqlCeLabel:"SQLCE",odbcLabel:"ODBC",oracleLabel:"ORACLE",oledbLabel:"OLEDB",xmlLabel:"XML",sharedLabel:"partagé"},datasourceConnection:{newConnection:"NOUVELLE CONNEXION",editConnection:"MODIFIER LA CONNEXION",name:"prénom",save:"sauvegarder",connect:"Relier",cancel:"Annuler"},sqlDatasource:{authenticationType:"type d'identification",window:"les fenêtres",sqlServer:"serveur SQL",userName:"Nom d'utilisateur",password:"Mot de passe",switchLabel:"Source de données Advancepanel",switchAlert:"Passer au concepteur visuel annulera les modifications manuelles faites à la chaîne de connexion. Voulez-vous utiliser le concepteur visuel de toute façon ? ",basicOption:{serverName:"Nom du serveur",savePassword:"Enregistrer le mot de passe",database:"Base de données",advanceSwitch:"Option Advance"},advanceOption:{connectionString:"Chaîne de connexion",promptLabel:"Texte d'invite",prompt:"Rapide",none:"Aucun",savePassword:"Enregistrer le mot de passe",basicSwitch:"Option de base"},alertMessage:{alertConnectionString:"Spécifiez la chaîne de connexion",alertPrompt:"Spécifiez le texte d'invite",alertUserName:"Précisez le nom d'utilisateur",alertPassword:"Spécifiez le mot de passe",alertServerName:"Indiquez le nom du serveur",alertDatabaseName:"Indiquez le nom de la base de données"}},sqlceDatasource:{connectionString:"Chaîne de connexion",authenticationType:"type d'identification",authentication:"Authentification",none:"Aucun",password:"Mot de passe",savePassword:"Enregistrer le mot de passe",alertMessage:{alertConnectionString:"Spécifiez la chaîne de connexion",alertPassword:"Spécifiez le mot de passe"}},odbcDatasource:{connectionString:"Chaîne de connexion",authenticationType:"type d'identification",authentication:"Authentification",prompt:"Rapide",none:"Aucun",userName:"Nom d'utilisateur",password:"Mot de passe",promptLabel:"Texte d'invite",savePassword:"Enregistrer le mot de passe",alertMessage:{alertConnectionString:"Spécifiez la chaîne de connexion",alertPrompt:"Spécifiez le texte d'invite",alertUserName:"Précisez le nom d'utilisateur",alertPassword:"Spécifiez le mot de passe"}},oracleDatasource:{connectionString:"Chaîne de connexion",authenticationType:"type d'identification",authentication:"Authentification",prompt:"Rapide",none:"Aucun",userName:"Nom d'utilisateur",password:"Mot de passe",promptLabel:"Texte d'invite",savePassword:"Enregistrer le mot de passe",alertMessage:{alertConnectionString:"Spécifiez la chaîne de connexion",alertPrompt:"Spécifiez le texte d'invite",alertUserName:"Précisez le nom d'utilisateur",alertPassword:"Spécifiez le mot de passe"}},oledbDatasource:{connectionString:"Chaîne de connexion",authenticationType:"type d'identification",authentication:"Authentification",prompt:"Rapide",none:"Aucun",userName:"Nom d'utilisateur",password:"Mot de passe",promptLabel:"Texte d'invite",savePassword:"Enregistrer le mot de passe",alertMessage:{alertConnectionString:"Spécifiez la chaîne de connexion",alertPrompt:"Spécifiez le texte d'invite",alertUserName:"Précisez le nom d'utilisateur",alertPassword:"Spécifiez le mot de passe"}},xmlDatasource:{connectionString:"Chaîne de connexion"},sharedDatasource:{datasource:"Source de données partagée",alertMessage:"Sélectionnez une source de données partagée"},alertMessage:{alertLabel:"La source de données",deleteValue:"Supprimer la source de données '",alertConnectionFailed:"Le concepteur de rapports n'a pas réussi à connecter la source de données",dataExtensionFailed:"Le fournisseur de données sélectionné n'est pas disponible. Veuillez vérifier l'extension de données.",connectStringValidation:"Comme la chaîne de connexion contient des expressions dans la source de données ",validationMessage:" Veuillez mettre à jour avec une chaîne de connexion valide.",executionMessage:", nous ne pouvons pas exécuter le jeu de données pour cette connexion.",confirmMessage:" Voulez-vous vraiment enregistrer la source de données?",nameWarning:"Spécifiez le nom DataSource",nameAlert:"Le nom spécifié existe déjà dans la liste DataSource",nameValidation:"Le nom ne doit pas contenir d'espaces et de caractères spéciaux"}},imageManager:{headerText:"GESTIONNAIRE D'IMAGE",addImageButton:"AJOUTER UNE IMAGE",deleteImage:"Supprimer l'image incorporée",image:"Image"},linkParameter:{title:"Paramètres",headerTxt:"Paramètre de lien",descriptionText:"Paramètres de rapport",addText:"AJOUTER",ok:"D'accord",cancel:"Annuler",nameWaterMark:"Le nom du paramètre",valueWaterMark:"Valeur",errorMessage:"Entrez une valeur pour cette propriété",closeToolTip:"Fermer"},filter:{title:"Filtre",descriptionLable:"Inclure les lignes où les conditions suivantes sont vraies.",add:"AJOUTER",ok:"D'accord",cancel:"Annuler",valueWaterMark:"Valeur",fieldWaterMark:"Choisissez le champ",closeToolTip:"Fermer",errorMessage:{booleanValidation:"La valeur n'est pas une valeur booléenne. ",intValidation:"La valeur n'est pas un nombre entier. ",floatValidation:"La valeur n'est pas un flotteur. ",dateTimeValidation:"La valeur est un format date / heure invalide. ",topBottomFilter:"Les opérateurs de filtre Top% et Bottom% requièrent un float ou un type de données entier.",expressionValidation:"Choisir une valeur pour le champ d'expression"},operatorTypes:{like:"Comme",topN:"HautN",bottomN:"BasN",topPercent:"Haut%",bottomPercent:"Bas%",between:"Entre",inFilter:"Dans"}},dataField:{title:"Champs de données",descriptionLable:"Inclure les lignes de champs de données",add:"AJOUTER",ok:"D'accord",cancel:"Annuler",fieldNameWaterMark:"Nom de domaine",closeToolTip:"Fermer",errorMessages:{emptyField:"Spécifiez le nom du champ",invalidCharacters:"Le nom du champ ne doit pas contenir d'espaces ni de caractères spéciaux",sameCharacter:"Le nom du champ existe déjà"},dsNameLabel:"prénom",dsNameWaterMark:"Nom de données",dsNameValidation:{nameWarning:"Spécifiez le nom du DataSet",nameAlert:"Le nom spécifié existe déjà dans la liste de DataSet",nameValidation:"Le nom du DataSet ne doit pas contenir d'espaces ni de caractères spéciaux"}},dataPanel:{itemTooltip:{properties:"Propriétés",data:"Les données",parameters:"paramètres",imageManager:"Gestionnaire d'image",expand:"Développer",collapse:"Effondrer"},dataSourceNewAlert:{title:"La source de données",contentMessage:"Voulez-vous annuler la création de La source de données ?"},dataSourceEditAlert:{title:"La source de données",contentMessage:"Voulez-vous annuler l'édition de La source de données ?"},dataSetNewAlert:{title:"Ensemble de données",contentMessage:"Voulez-vous annuler la création de Ensemble de données ?"},dataSetEditAlert:{title:"Ensemble de données",contentMessage:"Voulez-vous annuler l'édition de Ensemble de données ?"},parameterNewAlert:{title:"Paramètre",contentMessage:"Voulez-vous annuler la création du paramètre ?"},parameterEditAlert:{title:"Paramètre",contentMessage:"Voulez-vous annuler l'édition du paramètre ?"}},dataSet:{headerText:"LES DONNÉES",newData:"AJOUTER LE DATASET",shareDataset:{headerText:"NOUVEAU DATASET",editHeaderText:"MODIFIER DATASET",save:"sauvegarder",cancel:"Annuler",nameLable:"prénom",sharedDatasetLabel:"Ensemble de données partagé",errorMessage:{nameValidation:"Indiquez le nom du Ensemble de données partagé",datasetValidation:"Sélectionnez une source de données partagée",duplicateName:"Le nom spécifié existe déjà dans la liste Ensemble de données partagé",specialCharacter:"Le nom ne doit pas contenir d'espaces et de caractères spéciaux"}},contextMenu:{edit:"modifier",remove:"Effacer",cloneDataset:"Cloner",filter:"Filtre",setField:"Des champs"},datasourceSwitcher:"Les sources de données",deleteDataset:"Supprimer le jeu de données",deleteField:"Supprimer le champ",newDataText:"Nouvelles données",sharedDataText:"Données partagées",dataRestriction:{dsCreateRestriction:"La création de la source de données a été restreinte",title:"Les données"},dataFieldSearch:{errorMessage:"Aucun résultat",searchText:"Chercher"}},reportViewer:{toolbar:{print:"Impression",exportText:"Exportation",pageFit:"Ajuster à la page",exportformat:{Pdf:"PDF",Excel:"Exceller",Word:"Mot",Html:"Html",PPT:"PowerPoint",CSV:"CSV"},pageSetup:"Mise en page",gotoFirst:"Aller à Premier",gotoLast:"Aller à Dernier",gotoNext:"Aller à Prochain",gotoPrevious:"Aller à précédent",gotoParanet:"Aller à Paranet",zoomIn:"Zoom au",zoomOut:"Zoom sur",fittopage:{pageWidth:"Largeur de page",pageHeight:"Page entière"},printLayout:"Imprimer la mise en page",refresh:"Refresh",documentMap:"document Carte",parameter:"Paramètre",viewDesign:"fermer l aperçu"},pagesetupDialog:{close:"Fermer",paperSize:"Taille de papier",height:"la taille",width:"Largeur",margins:"marges",top:"Sommet",bottom:"Bas",right:"Droite",left:"À gauche",unit:"po",orientation:"Orientation",portrait:"Portrait",landscape:"Paysage",doneButton:"Terminé",cancelButton:"Annuler"},credential:{userName:"Nom d'utilisateur",password:"Mot de passe"},waterMark:{selectOption:"Sélectionnez l'option",selectValue:"Sélectionnez une valeur"},errorMessage:{startMessage:"La visionneuse de rapports a rencontré des problèmes lors du chargement de ce rapport. S'il vous plaît",middleMessage:"Cliquez ici",endMessage:"pour voir les détails de l'erreur",closeMessage:"Fermez ce message"},alertMessage:{close:"Fermer",title:"Rapport spectateur",done:"D'accord",showDetails:"Afficher les détails",hideDetails:"Cacher les détails",reportLoad:"Rapport chargé:",RVERR0001:"La visionneuse de rapports n'a pas pu charger le rapport",RVERR0002:"La visionneuse de rapports n'a pas réussi à afficher le rapport",RVERR0003:"Une erreur s'est produite dans la publication ajax",RVERR0004:"Veuillez sélectionner une valeur pour le paramètre",RVERR0005:"le {le nom du paramètre} paramètre est manquant une valeur",RVERR0006:"Veuillez donner l'entrée de type de données floatt",RVERR0007:"Veuillez indiquer l'entrée du type de données entier",RVERR0008:"La visionneuse de rapports n'a pas réussi à valider les informations d'identification de la source de données",RVERR0009:"Les marges se chevauchent ou se trouvent sur le papier. Entrez une taille de marge différente.",RVERR0010:"Veuillez entrer une valeur pour le paramètre",RVERR0011:"Le paramètre ne peut pas être vide",RVERR0012:"La valeur fournie pour le paramètre de rapport {invite de paramètre} n'est pas valide pour son type."},selectAll:"Tout sélectionner",viewButton:"Voir le rapport"},sortData:{sorting:"Tri",headerText:"Filtre de tri",add:"AJOUTER",changeSortingOptions:"Modifier les options de tri.",sortBy:"Trier par",thenBy:"Puis par",direction:{ascending:"Ascendant",descending:"Descendant"},chooseField:"Choisissez le champ",errorMessage:"Choisir une valeur pour le champ d'expression",ok:"D'accord",cancel:"Annuler",close:"Fermer"},groupData:{grouping:"Regroupement",headerTxt:"Groupe",headerTxtLabel:"Label de groupe",name:"prénom",label:"Étiquette",changeGroupingOptions:"Modifier les options de regroupement.",add:"AJOUTER",groupBy:"Par groupe",andOn:"Et sur",chooseField:"Choisissez le champ",ok:"D'accord",cancel:"Annuler",close:"Fermer",errorMessage:{nameErrorMessage:"S'il vous plaît entrer le nom valide",expressionErrorMessage:"Choisir une valeur pour un champ d'expression"}},alertMessage:{yes:"Oui",no:"Non",ok:"D'accord",showDetails:"Afficher les détails",hideDetails:"Cacher les détails",close:"Fermer"},parameter:{listPanel:{headerText:"PARAMÈTRES",newParameter:"NOUVEAU PARAMETRE",editMenu:{edit:"modifier",remove:"Effacer"},alertTitle:"Parametre"},configurationPanel:{newHeaderText:"NOUVEAU PARAMETRE",editHeaderText:"MODIFIER PARAMÈTRE",nameLabel:"prénom",promptLable:"Rapide",dataTypeLable:"Type de données",blankValueLable:"Autoriser la valeur vide('')",nullValueLable:"Autoriser la valeur null",multipleValueLable:"Autoriser plusieurs valeurs",visibilityLable:"Visibilité",assignValueLable:"Attribuer une valeur >>",save:"sauvegarder",cancel:"Annuler",visibility:{visible:"Visible",hidden:"Caché",internal:"Interne"},dataType:{stringType:"Chaîne",booleanType:"Booléen",dateTimeType:"DateTime",integerType:"Entier",floatType:"Flotte"}},errorMessage:{nameField:"S'il vous plaît entrer le nom",promptField:"Veuillez entrer la valeur",nameAlreadyExists:"Le nom du paramètre existe déjà"},warningMessage:{specialCharacter:"Le nom ne doit pas contenir d'espaces et de caractères spéciaux",multipleValueAlert:"Plusieurs valeurs par défaut ont été spécifiées. Le paramètre n'autorise pas plusieurs valeurs. ",nullValueAlert:"Dans le champ de valeur, une valeur nulle a été spécifiée. Le paramètre n'autorise pas les valeurs nulles. "},alertMessage:{confirmNullCheck:"Les valeurs disponibles ou par défaut peuvent contenir une valeur nulle. Voulez-vous activer la case à cocher autoriser la valeur nulle?",confirmBlankValue:"Les valeurs disponibles ou par défaut peuvent contenir une valeur vide, Voulez-vous activer la case à cocher de la valeur vide?",dataTypeChange:"Changer le type de données annulera les modifications apportées aux valeurs disponibles et par défaut.Voulez - vous changer le type de données quand même?",deleteAlert:"Supprimer le paramètre de rapport"},assignData:{title:"Paramètre",availableValue:"Valeur disponible",defaultValue:"Valeur par défaut",none:"Aucun",specify:"Spécifier",query:"Valeur de requête",ok:"D'accord",cancel:"Annuler",availableFields:{specifyDescriptionText:"Ajouter les valeurs disponibles pour les paramètres:",queryDescriptionText:"Choisissez l'ensemble de données et les champs pour les valeurs disponibles:",nameFieldWaterMark:"Étiquette",valueFieldWaterMark:"Valeur"},defaultFields:{specifyDescriptionText:"Ajouter les valeurs par défaut pour les paramètres:",queryDescriptionText:"Choisissez l'ensemble de données et les champs pour les valeurs par défaut:",defValueWaterMark:"Choisissez la valeur par défaut"},datasetWaterMark:"Choisir la valeur du jeu de données",valueWaterMark:"Choisir une valeur",lableWaterMark:"Choisir une étiquette",add:"AJOUTER",datasetLableText:"Ensemble de données",valueLableText:"Champ de valeur",labelFieldText:"Champ d'étiquette",errorMessage:{boolTypeCheck:"La valeur n'est pas une valeur booléenne. ",dateTypeCheck:"La valeur est un format de date non valide. ",intTypeCheck:"La valeur n'est pas un nombre entier. ",floatTypeCheck:"La valeur n'est pas un flottant. ",multipleValuesCheck:"Un paramètre à valeurs multiples ne peut pas inclure de valeurs NULL",datasetFieldCheck:"Le champ Ensemble de données est requis. ",valueFieldCheck:"Le champ de valeur est requis. ",syntaxLabelField:"La valeur entrée dans le champ label n'est pas une syntaxe de jeton valide. ",syntaxValueField:"La valeur entrée dans le champ de valeur n'est pas une syntaxe de jeton valide. ",blankValueCheck:"Le champ de valeur est vide. Le paramètre n'autorise pas les valeurs vides. ",nullValueCheck:"Dans le champ de valeur, une valeur nulle a été spécifiée. Le paramètre n'autorise pas les valeurs nulles. "},closeToolTip:"Fermer"}},formatData:{title:"Format de dialogue",typeSelect:"Nombre",typeFormat:{numberType:{numberType:"Nombre",decimalPlaces:"Décimales",negativeValues:"Valeurs négatives",showZeroAs:{showZeroAs:"Montrer zéro comme",none:"(aucun)"},representation:"Représentation",repDropDwn:{thousands:"Milliers",millions:"Des millions",billions:"Des milliards"},useRegionFormating:"Utiliser le formatage régional",use1000Separator:"Utilisez le séparateur 1000 (,)"},currency:{currencyType:"Devise",decimalPlaces:"Décimales",negativeValues:"Valeurs négatives",cultureCurrency:"Culture monétaire",showZeroAs:{none:"(aucun)"},representation:"Représentation",repDropDwn:{thousands:"Milliers",millions:"Des millions",billions:"Des milliards"},useRegionFormating:"Utiliser le formatage régional",use1000Separator:"Utilisez le séparateur 1000 (,)",includeSpace:"Inclure un espace"},date:{dateType:"Rendez-vous amoureux",date:"Rendez-vous amoureux"},time:{timeType:"Temps",time:"Temps"},percentage:{percentageType:"Pourcentage",decimalPlaces:"Décimales",includeSpace:"Inclure un espace"},scientific:{scientificType:"Scientifique",decimalPlaces:"Décimales"},custom:{customType:"Douane",customFormat:"Format personnalisé"}},preview:"Aperçu",ok:"D accord",cancel:"Annuler",close:"Fermer"},expression:{title:"Expression",descriptionText:"Définir l'expression pour : ",optionLabel:"Options",dataLabel:"Les données",descritionLabel:"La description",exampleLabelText:"Exemple",ok:"D'accord",cancel:"Annuler",textAreaWaterMark:"Expression",parameters:"Paramètres",optionWaterMark:"Choisir une option",dataWaterMark:"Sélectionnez une donnée",reportData:"Aucune donnée de rapport trouvée",closeToolTip:"Fermer",category:{builtInFields:"Champs intégrés",operators:"Les opérateurs",functions:"Les fonctions"},description:{executionTime:"La date et l'heure auxquelles les rapports commencent à s'exécuter.",overallPageNumber:"Le numéro de page global actuel ne peut être utilisé que dans l'en-tête ou le pied de page.",overallTotalPages:"Le nombre total de pages du rapport ne peut être utilisé que dans l'en-tête et le pied de page.",pageName:"Le nom de la page en cours dans le rapport ne peut être utilisé que dans l'en-tête ou le pied de page.",pageNumber:"Le numéro de page actuel qui peut être réinitialisé par l'utilisation de sauts de page",isInteractive:"Un booléen qui indique si la demande de rendu en cours utilise un format interactif.",renderName:"Le nom du moteur de rendu tel qu'il est enregistré dans le fichier de configuration RSReportServer.",reportFolder:"Le chemin d'accès complet au dossier contenant le rapport n'inclut pas l'URL du serveur de rapports.",reportName:"L'URL du serveur de rapports sur lequel le rapport est exécuté",reportServerUrl:"L'URL du serveur de rapports sur lequel le rapport est exécuté.",totalPages:"Le nombre total de pages dans la séquence de pages continue actuelle peut être utilisé uniquement dans l'en-tête et le pied de page. Le nombre peut être réinitialisé en utilisant des sauts de page.",language:"L'ID de langue du client exécutant le rapport.",userID:"L'ID de l'utilisateur exécutant le rapport.",powerNumberType:"Lève un nombre à la puissance d'un autre nombre.",multiply:"Multiplie deux nombres.",integerDivision:"Divise deux nombres et renvoie un entier.",modulus:"Divise deux nombres et renvoie seulement le reste.",add:"Ajoute deux nombres et peut être utilisé pour concaténer deux chaînes.",floatDivision:"Divise deux nombres et renvoie un point flottant.",difference:"Donne la différence entre deux nombres ou indique la valeur négative d'une expression numérique.",lesser:"Moins que.",lesserOrEqual:"Inférieur ou égal à.",greater:"Plus grand que.",greaterOrEqual:"Plus grand ou égal à.",equal:"Égal à.",notEqual:"Pas égal à.",like:"Compare deux chaînes.",isOperator:"Compare deux variables de référence d'objet.",expression:"Génère une concaténation de chaîne de deux expressions.",stringType:"Ajoute deux nombres, et il peut être utilisé pour concaténer deux chaînes.",and:"Effectue une conjonction logique sur deux expressions booléennes ou une conjonction bit à bit sur deux",not:"Effectue une négation logique sur une expression booléenne ou une négation bit à bit sur une expression numérique.",or:"Utilisé pour effectuer une disjonction logique sur deux expressions booléennes ou une disjonction binaire sur deux valeurs numériques.",xor:"Effectue une opération d'exclusion logique sur deux expressions booléennes ou une exclusion binaire sur deux expressions numériques.",andAlso:"Effectue une conjonction logique en court-circuit sur deux expressions.",orElse:"Utilisé pour effectuer une disjonction logique de court-circuit sur deux expressions.",left:"Effectue un décalage arithmétique à gauche sur un motif de bits.",right:"Effectue un décalage arithmétique à droite sur un modèle de bits.",asc:"Renvoie une valeur entière représentant le code de caractère correspondant à un caractère.",ascW:"Renvoie une valeur entière représentant le code de caractère correspondant à un caractère.",chr:"Renvoie le caractère associé au code de caractère spécifié.",chrW:"Renvoie le caractère associé au code de caractère spécifié.",filter:"Renvoie un tableau basé sur zéro contenant un sous-ensemble d'un tableau de chaînes basé sur des critères de filtre spécifiés.",formatStringType:"Renvoie une chaîne formatée conformément aux instructions d'une expression de chaîne de format.",currency:"Renvoie une expression formatée en tant que valeur de devise à l'aide du symbole monétaire défini dans le panneau de configuration du système.",dateTime:"Renvoie une expression de chaîne représentant une valeur date / heure.",numberType:"Retourne une expression formatée en nombre.",percent:"Renvoie une expression formatée en pourcentage (multipliée par 100).",getChar:"Renvoie une valeur char représentant le caractère de l'index spécifié dans la chaîne fournie.",inStr:"Renvoie un entier spécifiant la position de départ de la première occurrence d'une chaîne dans une autre.",inStrRev:"Renvoie la position de la première occurrence d'une chaîne dans une autre, en commençant par le côté droit de la chaîne.",join:"Renvoie une chaîne créée en joignant un certain nombre de sous-chaînes dans un tableau.",lCase:"Renvoie une chaîne ou un caractère converti en minuscules.",leftStringType:"Renvoie une chaîne contenant un nombre spécifié de caractères du côté gauche d'une chaîne.",stringLength:"Renvoie un entier contenant le nombre de caractères d'une chaîne ou le nombre.",lSet:"Renvoie une chaîne alignée à gauche contenant la chaîne spécifiée ajustée à la longueur spécifiée.",leftTrim:"Renvoie la chaîne sans les espaces de fin du côté gauche dans la chaîne donnée.",middle:"Renvoie une chaîne contenant un nombre spécifié de caractères d'une chaîne.",replace:"Renvoie une chaîne dans laquelle une sous-chaîne spécifiée a été remplacée par une autre.",rightString:"Renvoie une chaîne contenant un nombre spécifié de caractères du côté droit d'une chaîne.",rightSet:"Renvoie une chaîne alignée à droite contenant la chaîne spécifiée ajustée à la longueur spécifiée.",rightTrim:"Renvoie la chaîne sans les espaces de fin du côté droit dans la chaîne donnée.",stringSpace:"Renvoie une chaîne constituée du nombre d'espaces spécifié.",splitString:"Renvoie un tableau unidimensionnel à base zéro contenant un nombre spécifié de sous-chaînes.",strComp:"Renvoie -1, 0 ou 1, en fonction du résultat d'une comparaison de chaînes.",strConv:"Renvoie une chaîne convertie comme spécifié.",duplicateString:"Renvoie une chaîne ou un objet constitué du caractère spécifié répété le nombre de fois spécifié.",strReverse:"Renvoie une chaîne dans laquelle l'ordre des caractères d'une chaîne spécifiée est inversé.",trim:"Renvoie la chaîne sans espaces de fin dans la chaîne donnée.",upperCase:"Renvoie une chaîne ou un caractère contenant la chaîne spécifiée convertie en majuscule.",cDate:"Convertir à ce jour.",dateAdd:"Renvoie une valeur de date contenant les valeurs de date et d'heure auxquelles un intervalle de temps spécifié a été ajouté.",dateDiff:"Renvoie une valeur longue spécifiant le nombre d'intervalles de temps entre deux valeurs de date.",datePart:"Renvoie une valeur entière contenant le composant spécifié d'une valeur de date donnée.",dateSerial:"Renvoie une valeur de date représentant une année, un mois et un jour spécifiés, avec l'heure définie sur minuit (00:00:00).",dateString:"Renvoie ou définit une valeur de chaîne représentant la date actuelle en fonction de votre système.",dateValue:"Renvoie une valeur de date contenant les informations de date représentées par une chaîne, avec les informations de temps",day:"Renvoie une valeur entière de 1 à 31 représentant le jour du mois.",format:"Renvoie une expression de chaîne représentant la valeur date / heure.",hour:"Renvoie une valeur entière de 0 à 23 représentant l'heure du jour.",minute:"Renvoie une valeur entière de 0 à 59 représentant la minute de l'heure.",month:"Renvoie une valeur entière de 1 à 12 représentant le mois de l'année.",monthName:"Renvoie une valeur de chaîne contenant le nom du mois spécifié.",now:"Renvoie une valeur de date contenant la date et l'heure actuelles en fonction de votre système.",second:"Renvoie une valeur entière de 0 à 59 représentant la seconde de la minute.",timeOfDay:"Renvoie ou définit une valeur de date contenant l'heure actuelle en fonction de votre système.",timer:"Renvoie une double valeur représentant le nombre de secondes écoulées depuis minuit.",timeSerial:"Renvoie une valeur de date représentant une heure, une minute et une seconde spécifiées, les informations de date étant définies par rapport au 1er janvier de l'année 1.",timeString:"Renvoie ou définit une valeur de chaîne représentant l'heure actuelle en fonction de votre système.",timeValue:"Renvoie une valeur de date contenant les informations temporelles représentées par une chaîne, avec les informations de date définies sur le 1er janvier de l'année 1.",timeToday:"Renvoie ou définit une valeur de date contenant la date actuelle en fonction de votre système.",timeWeekday:"Renvoie une valeur entière contenant un nombre représentant le jour de la semaine.",timeWeekdayName:"Renvoie une valeur de chaîne contenant le nom du jour de la semaine spécifié.",year:"Renvoie une valeur entière de 1 à 9999 représentant l'année.",abs:"Renvoie la valeur absolue d'un nombre à virgule flottante simple précision.",acos:"Renvoie l'angle dont le cosinus est le nombre spécifié.",asin:"Renvoie l'angle dont le sinus est le nombre spécifié.",atan:"Renvoie l'angle dont la tangente est le nombre spécifié.",atan2:"Renvoie l'angle dont la tangente est le quotient de deux nombres spécifiés.",bigMultiply:"Produit le produit complet de deux nombres de 32 bits.",ceiling:"Renvoie le plus petit entier supérieur ou égal à l'entier spécifié.",cos:"Renvoie le cosinus de l'angle spécifié.",cosh:"Renvoie le cosinus hyperbolique de l'angle spécifié.",exponent:"Renvoie e élevé à la puissance spécifiée.",fixNumberType:"Renvoie une partie entière d'un nombre.",floor:"Renvoie le plus grand nombre entier inférieur ou égal au nombre spécifié",integer:"Renvoie une partie entière d'un nombre.",logrithm:"Renvoie le logarithme naturel (base e) d'un nombre spécifié.",logrithm10:"Renvoie le logarithme en base 10 d'un nombre spécifié.",maximum:"Renvoie la valeur maximale de toutes les valeurs non nulles de l'expression spécifiée.",minimum:"Renvoie la valeur minimale de toutes les valeurs non nulles de l'expression spécifiée.",power:"Renvoie un nombre spécifié élevé à la puissance spécifiée.",random:"Renvoie un nombre aléatoire de type unique.",round:"Arrondit une valeur à virgule flottante double précision à l'entier le plus proche.",sign:"Renvoie une valeur indiquant le signe d'un entier signé de 8 bits.",sin:"Renvoie le sinus de l'angle spécifié.",sinh:"Renvoie le sinus hyperbolique de l'angle spécifié.",squareRoot:"Renvoie la racine carrée d'un nombre spécifié.",tangent:"Renvoie la tangente de l'angle spécifié.",tangentH:"Renvoie la tangente hyperbolique de l'angle spécifié.",isArray:"Renvoie une valeur booléenne indiquant si une variable pointe sur un tableau.",isDate:"Renvoie une valeur booléenne indiquant si une expression représente une valeur valide",isNothing:"Renvoie une valeur booléenne indiquant si une expression n'a aucun objet",isNumeric:"Renvoie une valeur booléenne indiquant si une expression peut être évaluée en tant que nombre.",flowChoose:"Sélectionne et renvoie une valeur à partir d'une liste d'arguments.",flowIIf:"Renvoie l'un des deux objets en fonction de l'évaluation d'une expression.",switchFlow:"Évalue une liste d'expressions et renvoie une valeur d'objet correspondant à la première expression de la liste true.",avg:"Renvoie la moyenne de toutes les valeurs non nulles de l'expression spécifiée.",count:"Renvoie le nombre de valeurs de l'expression spécifiée.",countDistinct:"Renvoie le nombre de toutes les valeurs distinctes de l'expression spécifiée. ",countRows:"Renvoie un nombre de lignes dans la portée spécifiée.",first:"Renvoie la première valeur de l'expression spécifiée.",last:"Renvoie la dernière valeur de l'expression spécifiée.",standardDev:"Renvoie l'écart type de toutes les valeurs non nulles de l'expression spécifiée.",standardDevP:"Renvoie l'écart type de population de toutes les valeurs non nulles de l'expression spécifiée.",sum:"Renvoie la somme des valeurs de l'expression spécifiée.",variance:"Renvoie la variance de toutes les valeurs non nulles de l'expression spécifiée.",varianceP:"Renvoie la variance de population de toutes les valeurs non nulles de l'expression spécifiée.",runningValue:"Utilise une fonction spécifiée pour renvoyer un agrégat en cours d'exécution de l'expression spécifiée.",aggregate:"Renvoie un agrégat personnalisé de l'expression spécifiée, tel que défini par le fournisseur de données.",doubleDeclining:"Renvoie un double spécifiant la dépréciation d'un actif pour une période donnée en utilisant la méthode du solde dégressif double ou une autre méthode que vous spécifiez.",futureValue:"Renvoie la valeur double en spécifiant la valeur future d'une annuité sur la base de paiements fixes périodiques et d'un taux d'intérêt fixe.",interestPayment:"Renvoie une valeur double spécifiant le paiement d'intérêts pour une période donnée d'une rente sur la base de paiements périodiques et fixes et d'un taux d'intérêt fixe.",numberOfPeriods:"Renvoie une valeur double spécifiant le nombre de périodes pour une annuité basée sur des paiements fixes périodiques et un taux d'intérêt fixe.",annuityPayment:"Renvoie  une valeur spécifiant le paiement d'une annuité sur la base de paiements périodiques et fixes et d'un taux d'intérêt fixe.",principalPayment:"Renvoie  une valeurspécifiant le paiement principal pour une période donnée d'une rente basée sur des versements fixes périodiques et un taux d'intérêt fixe.",presentValue:"Renvoie  une valeur spécifiant la valeur actuelle d'une rente basée sur des versements périodiques et fixes à payer dans le futur et un taux d'intérêt fixe.",rateOfInterest:"Renvoie une valeur spécifiant le taux d'intérêt par période pour une annuité.",straightLine:"Renvoie une valeur spécifiant l'amortissement linéaire d'un actif pour une période unique.",sumOfYearsDigits:"Renvoie un double spécifiant la dépréciation des chiffres de la somme des années d'un actif pour une période donnée.",convertBool:"Convertir en booléen.",convertByte:"Convertir en octet.",convertChar:"Convertir en caractère.",convertDate:"Convertir à ce jour.",convertDouble:"Convertir en double.",convertDecimal:"Convertir en décimal.",convertInteger:"Convertir en entier.",convertLong:"Convertir en long.",convertObject:"Convertir en objet",convertShort:"Convertir en court.",convertSingle:"Convertir en simple.",convertString:"Convertir en chaîne.",fix:"Renvoie une partie entière d'un nombre.",hexaDecimal:"Renvoie une chaîne représentant la valeur hexadécimale d'un nombre.",integerPortion:"Renvoie une partie entière d'un nombre.",octal:"Renvoie une chaîne représentant la valeur octale d'un nombre.",stringOfNumber:"Renvoie une chaîne qui représente un nombre.",stringAsNumeric:"Renvoie un nombre dans une chaîne sous la forme d'une valeur numérique du type approprié.",inScope:"Renvoie true si l'instance actuelle est dans la portée spécifiée.",depthLevel:"Renvoie un entier basé sur zéro représentant le niveau de profondeur actuel.",previous:"Renvoie la valeur de l'expression pour la ligne de données précédente.",rowNumber:"Renvoie un nombre en cours d'exécution de toutes les lignes de la portée spécifiée."}},dataAssign:{measures:"Les mesures",dimensions:"Dimensions",addDatasource:"Ajouter une source de données",errorMessagePrefix:"Vous n'avez pas encore configuré de source de données.",errorMessageSuffix:"Ajoutez une source de données pour lier des données à des éléments de rapport dans votre concepteur.",search:"Chercher",dragOnDrop:"Traîne & Laissez tomber"},reportProperty:{header:"Entête",body:"Corps",footer:"Bas de page",report:"Rapport",basicSettings:{categoryName:"Paramètres de base",background:"Couleur de fond",borderTypes:{border:"Frontière",borderLeft:"La gauche",borderTop:"Haut",borderRight:"Droite",borderBottom:"Bas"},borderStyles:{solid:"Solide",none:"Aucun",double:"Double",dashed:"En pointillé",dotted:"Pointé"}},generalSettings:{categoryName:"Général",printFirstPage:"Imprimer sur la première page",printLastPage:"Imprimer sur la dernière page"},size:{sizeLabel:"Taille",paddingTypes:{padding:"Rembourrage",paddingLeft:"La gauche",paddingTop:"Haut",paddingRight:"Droite",paddingBottom:"Bas"}},codeModule:{code:"Code"},position:{categoryPosition:"Position",positionLabel:"Position",left:"La gauche",top:"Haut",sizeLabel:"Taille",width:"Largeur",height:"la taille"},margin:{categoryName:"Marge",categoryHeader:"Marge",types:{left:"La gauche",right:"Droite",bottom:"Bas",top:"Haut"}},pageUnit:{header:"Unités de page",label:"Unité de la page",types:{inches:"Pouces",centimeters:"Centimètres",pixels:"Pixel",points:"Points",millimeters:"Millimètres",picas:"Picas"}},columns:{header:"Colonne de la page",label:"Les colonnes",columnSpacing:"Espacement des colonnes"},paperSize:{orientation:"Orientation",header:"Taille de papier",label:"Taille de papier",orientationTypes:{landScape:"Paysage",portrait:"Portrait"},types:{a3Size:"A3",a4Size:"A4",b4Size:"B4(JIS)",b5Size:"B5(JIS)",envelope:"Enveloppe #10",envelopeMonarch:"Enveloppe Monarch",executive:"Exécutif",legal:"Légal",letter:"Lettre",tabloid:"Tabloïde",custom:"Douane"}},styleTooltip:"Style",colorTooltip:"Couleur",sizeTooltip:"Taille"},textBoxProperty:{contents:{categoryName:"Contenu",content:"Contenu"},basicSettings:{categoryName:"Paramètres de base",font:{categoryName:"Police de caractère",defaultStyle:"Défaut",normal:"Ordinaire",italic:"Italique"},fontStyle:{categoryName:"Le style de police",defaultStyle:"Défaut",normal:"Ordinaire",thin:"Mince",extraLight:"Lumière supplémentaire",light:"Lumière",medium:"Moyen",semiBold:"Semi-gras",bold:"Audacieux",extraBold:"Extra Gras",heavy:"Lourd"},textDecoration:{categoryName:"Décoration de texte",defaultStyle:"Défaut",none:"Aucun",underline:"Souligner",lineThrough:"Ligne à travers",overline:"Sur la ligne"},format:"Format"},alignment:{categoryName:"Alignement",textAlignment:{categoryName:"Alignement du texte",defaultStyle:"Défaut",left:"La gauche",center:"Centre",right:"Droite"},verticalAlignment:{categoryName:"Alignement vertical",defaultStyle:"Défaut",top:"Haut",middle:"Milieu",bottom:"Bas"},lineSpacing:"Hauteur de la ligne"},appearance:{categoryName:"Apparence",borderTypes:{border:"Frontière",borderLeft:"La gauche",borderTop:"Haut",borderRight:"Droite",borderBottom:"Bas"},borderStyles:{solid:"Solide",none:"Aucun",double:"Double",dashed:"En pointillé",dotted:"Pointé"},background:"Couleur de fond"},link:"Lien",linkReport:"Rapport de lien",position:{categoryPosition:"Position",positionLabel:"Position",sizeLabel:"Taille",left:"La gauche",top:"Haut",width:"Largeur",height:"la taille",direction:{categoryName:"Direction",leftToRight:"De gauche à droite",rightToLeft:"De droite à gauche"}},visibility:{categoryName:"Visibilité",visible:"Visible",toggleItem:"Basculer l'élément",intialToggleState:"Etat de basculement initial"},miscellaneous:{categoryName:"Divers",canGrow:"Peut croître",canShrink:"Peut rétrécir"},paragraphSettings:{categoryName:"Paramètres de paragraphe",textAlignment:{categoryName:"Alignement du texte",defaultStyle:"Défaut",left:"La gauche",center:"Centre",right:"Droite"},indent:{categoryName:"Retrait",leftIndent:"La gauche",rightIndent:"Droite"},space:{categoryName:"Espace",topSpace:"Haut",bottomSpace:"Bas"},listLevel:{categoryName:"Niveau de la liste",zeroLevel:"",oneLevel:"",twoLevel:"",threeLevel:"",fourLevel:""},listStyle:{categoryName:"Style de liste",none:"Aucun",numbered:"Numéroté",bulleted:"À puces"}},padding:{padding:"Rembourrage",paddingLeft:"La gauche",paddingTop:"Haut",paddingRight:"Droite",paddingBottom:"Bas"},contextMenu:{cut:"Couper",copy:"Copie",paste:"Coller",expression:"Expression",pasteAlert:"Votre navigateur ne prend pas en charge l'accès direct au presse-papiers. Veuillez utiliser le raccourci clavier Ctrl + V au lieu de coller."},fontStyleTooltip:"Style",fontWeightTooltip:"Poids",fontSizeTooltip:"Taille",fontColorTooltip:"Couleur",fontFamilyTooltip:"Famille de polices",styleTooltip:"Style",colorTooltip:"Couleur",sizeTooltip:"Taille",selectedText:"Texte sélectionné"},designPanel:{headerText:"Entête",footerText:"Bas de page",pasteAlert:"Seuls les éléments de base sont pris en charge dans les zones d'en-tête et de pied de page.",pasteTitle:"Coller"},customProperty:{position:{categoryPosition:"Position",positionLabel:"Position",left:"La gauche",top:"Haut",sizeLabel:"Taille",width:"Largeur",height:"la taille"},appearance:{categoryAppearance:"Paramètres de base",borderTypes:{border:"Frontière",borderLeft:"La gauche",borderTop:"Haut",borderRight:"Droite",borderBottom:"Bas"},borderStyles:{solid:"Solide",none:"Aucun",double:"Double",dashed:"En pointillé",dotted:"Pointé"},backGround:"Couleur de fond"},visibility:{categoryName:"Visibilité",visible:"Visible"},styleTooltip:"Style",colorTooltip:"Couleur",sizeTooltip:"Taille"},tablixProperty:{data:{categoryName:"Les données",datasetName:"Ensemble de données",datasetNone:"Aucun"},appearance:{categoryName:"Apparence",borderTypes:{border:"Frontière",borderLeft:"La gauche",borderTop:"Haut",borderRight:"Droite",borderBottom:"Bas"},borderStyles:{solid:"Solide",none:"Aucun",double:"Double",dashed:"En pointillé",dotted:"Pointé"},backGround:"Couleur de fond"},miscellaneous:{categoryName:"Divers",noRowsMessage:"Message sans rangée",pageName:"Nom de la page",keepTogether:"Rester ensemble",repeatColumnHeaders:"Répéter les en-têtes de colonne",repeatRowHeaders:"Répéter les en-têtes",fixedColumnHeaders:"En-têtes de colonne fixes",fixedRowHeaders:"En-têtes de lignes fixes"},font:{categoryName:"Police de caractère",defaultStyle:"Défaut",normal:"Ordinaire",italic:"Italique"},fontStyle:{categoryName:"Le style de police",defaultStyle:"Défaut",normal:"Ordinaire",thin:"Mince",extraLight:"Lumière supplémentaire",light:"Lumière",medium:"Moyen",semiBold:"Semi-gras",bold:"Audacieux",extraBold:"Extra Gras",heavy:"Lourd"},textDecoration:{categoryName:"Décoration de texte",defaultStyle:"Défaut",none:"Aucun",underline:"Souligner",lineThrough:"Ligne à travers",overline:"Sur la ligne"},alignment:{categoryName:"Alignement",textAlignment:{categoryName:"Alignement du texte",defaultStyle:"Défaut",left:"La gauche",center:"Centre",right:"Droite"},verticalAlignment:{categoryName:"Alignement vertical",defaultStyle:"Défaut",top:"Haut",middle:"Milieu",bottom:"Bas"}},padding:{padding:"Rembourrage",paddingLeft:"La gauche",paddingTop:"Haut",paddingRight:"Droite",paddingBottom:"Bas"},position:{categoryPosition:"Position",positionLabel:"Position",sizeLabel:"Taille",left:"La gauche",top:"Haut",width:"Largeur",height:"la taille"},visibility:{categoryName:"Visibilité",visible:"Visible",toggleItem:"Basculer"},staticGroupProp:{categoryName:"Paramètres de base",filters:"Les filtres",sorts:"Trie",fixedData:"Données fixes",groupExp:"Groupes",hideIfNoRows:"Masquer si aucune rangée",keepWithGroup:"Garder avec le groupe",repeatOnNewPage:"Répéter sur une nouvelle page",afterGroup:"Après",beforeGroup:"Avant",pageBreak:{categoryName:"Saut de page",enablePageBreak:"Activer le saut de page",breakLocation:{categoryName:"Break Lieu",none:"Aucun",start:"Début",end:"Fin",startAndEnd:"Début et fin",between:"Entre"},pageNumberReset:"Numéro de page réinitialisé"}},fontStyleTooltip:"Style",fontWeightTooltip:"Poids",fontSizeTooltip:"Taille",fontColorTooltip:"Couleur",fontFamilyTooltip:"Famille de polices",styleTooltip:"Style",colorTooltip:"Couleur",sizeTooltip:"Taille",tablixMember:"Membre Tablix"},rowColumnGroup:{rowGroupLable:"Groupes de lignes",columnGroupLable:"Groupes de colonnes",tablixAlertHeader:"Tablix",alertMessage:"Activer l'option Développer pour sélectionner l'élément de rapport de tableau matriciel",contextMenu:{addgroup:"Ajouter un groupe",advanced:"Avancée",deletegroup:"Supprimer un groupe",addtotal:"Ajouter Total",groupproperties:"Propriétés du groupe",addColumnGroup:"Ajouter un groupe de colonnes",addRowGroup:"Ajouter un groupe de lignes"},contextSubMenu:{adjacentafter:"Adjacent Après",adjacentbefore:"Adjacent avant",childgroup:"Groupe d'enfants",parentgroup:"Groupe de parents",totalafter:"Après",totalbefore:"Avant",childGroupAlert:"Impossible d'insérer un groupe dans les détails."}},tablixContextMenu:{rowMenu:{insertRow:"Insérer une ligne",above:"Au dessus de",below:"Au dessous de"},columnMenu:{insertColumn:"Insérer une colonne",left:"La gauche",right:"Droite"},rowGroupMenu:{insideGroupAbove:"Inside Group - Ci-dessus",insideGroupBelow:"Inside Group - Ci-dessous",outsideGroupAbove:"Groupe extérieur - ci-dessus",outsideGroupBelow:"Groupe extérieur - ci-dessous"},columnGroupMenu:{insideGroupLeft:"À l'intérieur du groupe - gauche",insideGroupRight:"Inside Group - Right",outsideGroupLeft:"Groupe extérieur - gauche",outsideGroupRight:"Groupe extérieur - droit"},deleteRows:"Supprimer des lignes",deleteColumns:"Supprimer des colonnes",rowVisibility:"Visibilité des lignes",columnVisibility:"Visibilité de la colonne",tablixProperties:"Propriétés du tableau matriciel",splitcells:"Cellules divisés",mergecells:"Fusionner des cellules",groupMenu:{adjacentAbove:"Ci-dessus",adjacentleft:"Gauche adjacente",adjacentright:"Droit adjacent",adjacentBelow:"Adjacente ci-dessous",childGroup:"Groupe d'enfants",parentGroup:"Groupe parent",deleteRowGroup:"Supprimer le groupe de lignes",deleteColGroup:"Supprimer le groupe de colonnes",addRowGroup:"Groupe de lignes",addColGroup:"Groupe de colonnes"},reportItemMenu:{insertItem:"Insérer",chart:"Graphique"},totalMenu:{total:"Ajouter le total",row:"Rangée",column:"Colonne",before:"Avant",after:"Après"},cellMenu:{addExpression:"Ajouter une expression",editExpression:"Editer l'expression",datasource:"Ajouter une source de données",noFields:"Pas de champs",addText:"Ajouter du texte",editText:"Éditer le texte"},basicItems:{deleteItem:"Effacer",cut:"Couper",copy:"Copie",paste:"Coller"}},tablixAlertDialog:{ok:"D'accord",cancel:"Annuler",closeToolTip:"Fermer",deleteRowTitle:"Supprimer des lignes",deleteRow:"Supprimer uniquement les lignes",deleteRowGroup:"Supprimer des lignes et des groupes associés",deleteRowContent:"Supprimer les options de ligne",deleteBodyRow:"Le corps du tableau matriciel doit contenir au moins une ligne.",deleteColumnTitle:"Supprimer des colonnes",deleteColumn:"Supprimer uniquement les colonnes",deleteColumnGroup:"Supprimer des colonnes et des groupes associés",deleteColumnContent:"Supprimer les options de colonne",deleteBodyColumn:"Le corps du tableau matriciel doit contenir au moins une colonne.",deleteGroup:"Supprimer le groupe uniquement",deleteGroupRowColumn:"Supprimer le groupe et les lignes et colonnes associées",deleteGroupTitle:"Supprimer le groupe",deleteGroupContent:"Supprimer les options du groupe",deleteStructure:"Structure de groupe non disponible.",removeRowAlert:"Échec de la suppression de la ligne dans l'élément de rapport de tableau matriciel",removeRow:"Supprimer les lignes",removeColumn:"Supprimer des colonnes",addRow:"Ajouter une rangée",addColumn:"Ajouter une colonne",removeColumnAlert:"Impossible de supprimer la colonne dans l'élément de rapport de tableau matriciel",addRowAlert:"Échec de l'ajout d'une ligne dans l'élément de rapport de tableau matriciel",addColumnAlert:"Impossible d'ajouter la colonne dans l'élément de rapport de tableau matriciel"},tablixAlertInfo:{addGroup:"Ajouter un groupe",removeGroup:"Supprimer le groupe",adjacentAfterAlert:"Échec de l'ajout d'un groupe adjacent dans la structure hiérarchique",adjacentBeforeAlert:"Échec de l'ajout d'un groupe adjacent dans la structure hiérarchique",childGroupALert:"Échec de l'ajout d'un groupe enfant dans la structure hiérarchique",title:"Elément de rapport de tableau matriciel",parentGroupAlert:"Échec de l'ajout du groupe parent dans la structure hiérarchique",removeGroupAlert:"Échec de la suppression du groupe dans la structure hiérarchique",selectedMemberAlert:"Le membre sélectionné n'est pas un membre du groupe",pasteActionAlert:"Les informations ne peuvent pas être publiées car la zone de copie et la zone de collage n'ont pas la même taille et la même forme.",pasteTitle:"Coller"},cellMergingAlertInfo:{merge:"Fusionner des cellules",mergeAlert:"Échec de la fusion des cellules dans l'élément de rapport de tableau matriciel",split:"Cellules divisés",splitAlert:"Échec de la scission des cellules dans l'élément de rapport de tableau matriciel"},tablixGroup:{title:"Groupe Tablix",headerTxt:"Label de groupe",groupBy:"Par groupe:",chooseField:"Choisissez un champ",showDetailData:"Afficher les données détaillées",addGroupHeader:"Ajouter un en-tête",addGroupFooter:"Ajouter un pied de page",ok:"D'accord",cancel:"Annuler",closeToolTip:"Fermer"},tablixDataAssignMenu:{datasource:"Ajouter une source de données",addExpression:"Ajouter une expression",editExpression:"Editer l'expression",addText:"Ajouter du texte",editText:"Éditer le texte",search:"Chercher",noFieldsFound:"Aucun champ trouvé"},tablixTotalAlert:{totalHeader:"Ajouter un en-tête total",totalStatic:"Ajouter le total",headerMessage:"Échec de l'ajout du nombre total de lignes ou de colonnes à l'en-tête de groupe dans l'élément de rapport de tableau matriciel",staticMessage:"Échec de l'ajout du nombre total de lignes ou de colonnes au corps du tableau matriciel dans l'élément de rapport tableau matriciel"},tablixAddTextDialog:{save:"Sauvegarder",add:"Ajouter",cancel:"Annuler",closeToolTip:"Fermer",addText:"Ajouter du texte",editText:"Éditer le texte"},queryDesigner:{storeParameter:{title:"Paramètres",ok:"D'accord",cancel:"Annuler",parameterLable:"Paramètre",nullLable:"Nul",valueLable:"Valeur",dataTypeLable:"Type de données",closeToolTip:"Fermer"},parameter:{title:"Paramètres",ok:"D'accord",cancel:"Annuler",parameterLable:"Paramètre",nullLable:"Nul",valueLable:"Valeur",dataTypeLable:"Type de données",auto:"Auto",text:"texte",closeToolTip:"Fermer"},filter:{title:"Filtres de requête",descriptionLable:"Liste des filtres de table",add:"AJOUTER",save:"D'accord",cancel:"Annuler",nullLable:"Nul",trueLable:"Vrai",falseLable:"Faux",parameterTooltip:"Inclure comme paramètre",closeToolTip:"Fermer",intOperatorType:{equals:"Équivaut à",doesNotEqual:"Équivaut à",greaterThan:"Plus grand que",greaterThanOrEqual:"Plus grand ou égal à",lessThan:"Moins que",lessThanOrEqual:"Inférieur ou égal à",between:"Entre",notBetween:"Pas entre"},stringOpertorType:{equals:"Équivaut à",startsWith:"Commence avec",endWith:"Se termine par",contains:"Contient",notContains:"Ne contient pas"},errorMessage:{dateValidation:"La valeur est un format de date non valide. ",commonContent:"Le filtre sur ",booleanValidation:" n'a aucune valeur à filtrer. Veuillez fournir les valeurs pour le filtre.",stringValidation:" n'a pas de valeurs correctes à filtrer. "}},previewArea:{dataPreview:"Aperçu des données",noRecords:"Aucun enregistrement à afficher",generatePreview:"Générer un aperçu",executeRecords:"Exécuter pour prévisualiser les enregistrements",record:"Record",records:"Enregistrements",retrieved:"récupéré",loadRecord:"Charger plus",update:"Mettre à jour"},schemaArea:{search:"Chercher",rename:"Renommer",aggregation:"Agrégation",dialogHeader:"Ensemble de données",matchesFound:"Aucun résultat",alertMessage:{datasourceAlert:"Sélectionnez une DataSource pour configurer le Ensemble de données de rapport",removeTable:"Les tables assoiquées ci-dessous seront supprimées avec cette",duplicateName:"Le nom de colonne spécifié existe déjà",specialCharacter:"Le nom de la colonne ne doit pas contenir de caractères spéciaux.",switcherAlert:"Passer au concepteur visuel annulera les modifications manuelles apportées à la requête. Voulez-vous utiliser le concepteur visuel de toute façon?",duplicateDatasetName:"Le nom spécifié existe déjà dans la liste DataSet",datasetSpecialCharacter:"Le nom ne doit pas contenir d'espaces et de caractères spéciaux"},errorMessage:{specifyName:"Indiquez le nom de la colonne",specifyDatasetName:"Indiquez le nom du Ensemble de données",previewFailed:"Ensemble de données n'a pas réussi à prévisualiser la table sélectionnée",specifyQuery:"Spécifiez la requête Ensemble de données",selectTable:"Sélectionnez la table pour enregistrer le Ensemble de données",queryFailed:"Ensemble de données n'a pas pu enregistrer la requête de la table sélectionnée",tableProcedure:"Ensemble de données n'a pas réussi à récupérer la procédure de table sélectionnée"}},toolBar:{datasourceLable:"La source de données",datasetName:"prénom",run:"Courir",join:"Joindre",expression:"Expression",filter:"Filtre",code:"Code",finish:"terminer",cancel:"Annuler",parameter:"Paramètre",datasourceWaterMark:"Sélectionnez une source de données",autoPreview:"Aperçu automatique"},joiner:{title:"Requête Joiner",descriptionLable:"Liste des relations de table",add:"AJOUTER",save:"D'accord",cancel:"Annuler",addField:"Ajouter le champ",closeToolTip:"Fermer",leftFieldWaterMark:"Champ gauche",rightFieldWaterMark:"Champ droit",operatorWaterMark:"Opérateur",joinTypeWaterMark:"Type de jointure",leftTableWaterMark:"Table de gauche",rightTableWaterMark:"Table droite",joinTypes:{inner:"Interne",outer:"Gauche extérieure",rightOuter:"Extérieur droit",fullOuter:"Plein extérieur"},errorMessage:{removeField:"Chaque relation doit avoir une condition de champ. Donc, il ne permet pas de supprimer ce champ ",noRelationAlert:" est pas de relation avec d'autres tables",selectLeftTable:"Sélectionnez la valeur de la table de gauche",selectRightTable:"Sélectionnez la bonne valeur de la table",selectRelation:"Sélectionnez la relation pour les tables",selectLeftColumn:"Sélectionnez la valeur de la colonne de gauche de la ligne de champ #",selectRightColumn:"Sélectionnez la valeur de la colonne de droite de la ligne de champ #",selectOperator:"Sélectionnez l'opérateur de la ligne de champ #",relationExists:"La relation existe déjà entre les tables"}},credentialDialog:{title:"Boîte de dialogue Credential",userName:"Nom d'utilisateur",password:"Mot de passe",userNameWaterMark:"Nom d'utilisateur",passwordWaterMark:"Mot de passe",userNameErrorMessage:"Veuillez entrer le nom d'utilisateur",passwordErrorMessage:"Veuillez entrer le mot de passe",connect:"Relier",close:"Fermer"},queryExpression:{title:"Expressions de requête",functionLabel:"Les fonctions",columnLabel:"Paramètres de colonne",expressionLabel:"Expression",nameLabel:"prénom",descriptionLabel:"La description ",exampleLabelText:"Exemple",ok:"sauvegarder",cancel:"Annuler",add:"Ajouter",textAreaWaterMark:"Expression de requête",nameFieldWaterMark:"Nom de l'expression",closeToolTip:"Fermer",errorMessage:{saveAlert:"L'expression n'est pas enregistrée. Voulez-vous enregistrer et continuer?",bracketSyntax:"Syntaxe incorrecte près des parenthèses d'ouverture / fermeture.",parseAlert:"Le concepteur de rapports n'a pas pu analyser l'expression spécifiée.",nameAlert:"Le champ Nom ne doit pas être vide.",emptyAlert:"Le champ d'expression ne doit pas être vide.",duplicateName:"Le nom d'expression spécifié existe déjà",specialCharacter:"Le nom d'expression ne doit pas contenir de caractères spéciaux.",referenceError:"La colonne ne peut pas être référée dans sa propre expression!",invalidSyntax:"Syntaxe invalide près des parenthèses ouvertes / fermées.",retrieveExpression:"Le concepteur de rapports n'a pas pu récupérer l'expression spécifiée"},datasetTitle:"Ensemble de données",expressions:{all:"Tout",numbers:"Nombres",logical:"Logique",conditional:"Conditionnel",date:"Rendez-vous amoureux",stringType:"Chaîne",text:"Texte",miscellenuous:"Divers ",abs:"Renvoie la valeur absolue de l'expression donnée.",acos:"Renvoie le cosinus inverse (également appelé arccosine) de l'expression numérique donnée.",asin:"Renvoie le sinus inverse (également appelé arcsine) de l'expression numérique donnée.",atan:"Renvoie la tangente inverse (également appelée arctangente) de l'expression numérique donnée.",cos:"Renvoie le cosinus de l'angle spécifié en radians de l'expression donnée.",degree:"Renvoie l'angle en degrés de celui spécifié en radians de l'expression numérique donnée.",exponent:"Renvoie la valeur exponentielle de l'expression donnée. ",logrithm:"Renvoie le logarithme de l'expression donnée à la base spécifiée.",pi:"Renvoie la valeur constante de PI.",power:"Renvoie la valeur de l'expression donnée (expression1) à la puissance spécifiée (expression2). ",radians:"Renvoie l'angle en radians pour celui spécifié en degrés dans l'expression numérique donnée.",round:"Renvoie une valeur arrondie.",sign:"Renvoie une valeur représentant le signe positif (+1), zéro (0) ou négatif (-1) de l'expression numérique donnée.",sin:"Renvoie le sinus de l'angle spécifié en radians de l'expression donnée.",squareRoot:"Renvoie la racine carrée de l'expression numérique donnée.",tan:"Renvoie la tangente de l'expression numérique donnée.",ifCondition:"Renvoie la partie vraie ou la partie fausse, en fonction de l'évaluation de l'expression.",ifNull:"Si l'expression est numérique / chaîne / date, renvoie la première expression. Si la première expression est NULL, renvoie la deuxième expression.",isNotNull:"Si la valeur numeric / string / date_expression est NULL, renvoie une chaîne représentant false; sinon représente vrai.",isNull:"Si la valeur numeric / string / date_expression est NULL, renvoie une chaîne représentant true; sinon représente faux.",and:"Renvoie true si les deux expressions sont vraies.",notOperation:"Renvoie la valeur logique d'inversion de l'expression en cours d'évaluation.",orOperation:"Renvoie true si l'une des expressions est évaluée à true.",addDate:"Ajoute le nombre de jours à la date spécifiée.",name:"Renvoie une chaîne représentant la partie date spécifiée de l'expression de date donnée.",part:"Renvoie une valeur entière représentant la partie de date spécifiée de l'expression de date donnée.",sub:"Renvoie la date soustraite de la date spécifiée.",day:"Retourne une valeur numérique représentant la partie jour de la date spécifiée.",daydiff:"Renvoie une valeur numérique représentant la différence entre deux dates spécifiées.",hour:"Renvoie l'heure de la date donnée en entier.",maximum:"Renvoie la valeur maximale dans l'expression donnée.",minimum:"Renvoie la valeur minimale dans l'expression donnée.",minute:"Renvoie une valeur numérique représentant la partie minute de la date résultant de l'expression de date spécifiée.",month:"Renvoie une valeur numérique représentant la partie mois de la date résultant de l'expression de date spécifiée.",now:"Renvoie la date et l'heure actuelles.",today:"Renvoie la date actuelle.",year:"Renvoie une valeur numérique représentant la partie année de la date résultant de l'expression de date spécifiée.",char:"Convertit le code ASCII entier donné en caractère.",concat:"Renvoie une valeur de chaîne résultant de la concaténation de deux valeurs de chaîne ou plus.",contains:"Renvoie true si l'expression de chaîne donnée contient l'expression de sous-chaîne spécifiée.",endsWith:"Renvoie true si l'expression de chaîne donnée se termine avec l'expression de sous-chaîne spécifiée.",left:"Renvoie le nombre de caractères spécifié à partir du début de l'expression de chaîne donnée.",length:"Renvoie le logarithme naturel de l'expression donnée.",lower:"Renvoie une valeur de chaîne convertie en minuscules à partir de l'expression de chaîne donnée.",leftTrim:"Renvoie la valeur de chaîne avec les espaces vides supprimés de l'expression de chaîne.",right:"Renvoie le nombre de caractères spécifié à la fin de l'expression de chaîne donnée.",rightTrim:"Renvoie la chaîne sans les espaces de fin du côté droit dans la chaîne donnée.",startswith:"Renvoie true si les expressions de chaîne données commencent par l'expression de sous-chaîne spécifiée.",subString:"Renvoie une longueur de chaîne spécifique à partir de l'index spécifique de l'expression de chaîne donnée.",upper:"Renvoie une valeur de chaîne convertie majuscule à partir d'une expression de chaîne donnée."}},reportParameter:{title:"Paramètres",descriptionText:"Paramètres de rapport",addText:"AJOUTER",ok:"D'accord",cancel:"Annuler",nameWaterMark:"Le nom du paramètre",valueWaterMark:"Valeur",closeToolTip:"Fermer"}},chartItem:{categoryItems:{yvalue:"Y Valeur(s)",size:"Taille(s)",xvalue:"X Valeur(s)",column:"Colonne",row:"Rangée(s)"},categoryItemsMenu:{filter:"Filtres",sort:"Sorts",group:"Groupes",expression:"Expression",aggregate:"Agrégat"}},codeDialog:{title:"Module de code",ok:"D'accord",cancel:"Annuler",add:"Ajouter",closeToolTip:"Proche",reference:{title:"Références",waterMark:"Référence ",errorMessage:"Le champ est vide",headerText:"Liste de références d'assemblage",infoTipText:"Ajoutez une référence d'assemblage pour utiliser vos fonctions d'assemblage dans le rapport."},classes:{title:"Classes",classWaterMark:"Nom de la classe",instanceWaterMark:"Nom de linstance",classErrorMessage:"Les champs sont vides",instanceErrorMessage:"Le champ est vide",headerText:"Liste des instances de classe",infoTipText:"Ajoutez des instances de classe pour accéder à vos fonctions d'objet dans le rapport."},code:{title:"Code",headerText:"Fonction de code VB pour le rapport",infoTipText:"Le moteur de génération de rapports Syncfusion prend en charge les fonctions de code VB à intégrer aux éléments de rapport et aux données."}},previewData:{title:"Données de prévisualisation",ok:"D'accord",cancel:"Annuler",description:"Lier les données JSON pour la prévisualisation",close:"Fermer",infoToolTip:"Le rapport nécessite un aperçu des données au format JSON.Il contient la clé et la valeur dans la liste du format de tableau.",jsonHeader:"Données JSON:",errorMessage:"Spécifiez le format JSON valide",previewDataAlert:{title:"Données de prévisualisation",alertMessage:"Voulez-vous passer au concepteur de rapports ?"}},sampleDataSource:{sampleDSHeader:"IMPORTATION DE DONNÉES D'ÉCHANTILLON",addText:"Ajouter",searchText:"Chercher",noDataFound:"Aucune donnée disponible.",welcomeContentPrefix:"Commencez par créer une source de données",welcomeContentSuffix:"Vous pouvez vous connecter à vos propres données personnalisées ou en importer une à partir des données partagées prédéfinies que nous proposons.",sampleDSText:"importer des échantillons de données",exploreSampleText:"Explorez des exemples de données",accordionText:"Lancez votre premier rapport et explorez les options de personnalisation à l'aide des exemples de données.",errorMessage:"Erreur réseau",alertHeaderText:"Importer des données",alertMessage:"Le concepteur de rapports n'a pas pu importer les données du serveur de rapports"},field:{title:"Champs",nameWaterMark:"Nom de domaine",sourceWaterMark:"Source de champ",ok:"D'accord",cancel:"Annuler",description:"Modifier la requête et les champs calculés",query:"Champ de requête",calculated:"Champ calculé",fieldError:"Le champ est vide",fieldsError:"Les champs sont vides",add:"AJOUTER",closeToolTip:"Fermer",invalidFormat:"Le nom du champ ne doit pas contenir d'espaces ni de caractères spéciaux",sameFieldName:"Le nom de champ existe déjà"},commonProperty:{commonProperties:"Propriétés communes",basicSettings:{categoryBasicSettings:"Paramètres de base",borderTypes:{border:"Frontière",borderLeft:"La gauche",borderTop:"Haut",borderRight:"Droite",borderBottom:"Bas"},borderStyles:{solid:"Solide",none:"Aucun",double:"Double",dashed:"En pointillé",dotted:"Pointé"},backGround:"Couleur de fond",styleTooltip:"Style",colorTooltip:"Couleur",sizeTooltip:"Taille"},position:{categoryPosition:"Position",positionLabel:"Position",sizeLabel:"Taille",left:"La gauche",top:"Haut"},visibility:{categoryVisibility:"Visibilité",visible:"Visible"}}};
