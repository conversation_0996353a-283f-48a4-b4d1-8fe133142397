﻿using Admin.Components.Pages;
using C1.C1Report;
using Microsoft.AspNetCore.Hosting.Server;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Syncfusion.Blazor;
using System.Data;

namespace Admin.Reporting
{
    public class ReportsFactory : IReportsFactory
    {
        private IConfiguration configuration;
        private IWebHostEnvironment environment;

        public ReportsFactory(IConfiguration configuration, IWebHostEnvironment environment)
        {
            this.configuration = configuration;
            this.environment = environment;
        }

        #region  TenantsStatisticsExcel
        public DataTable GetTenantsStatisticsExcel()
        {
            DataTable dt = new DataTable();
            string? connectionString = configuration.GetValue<string>("MentalViewConnectionString");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                string sqlText = @"SELECT TenantId, FullName AS N'Επωνυμία', 
                FORMAT(DateCreated, 'dd/MM/yyyy HH:mm') AS N'Ημ/νία Δημιουργίας', --DateCreated
                FORMAT(LastLoggedUserDate, 'dd/MM/yyyy HH:mm') AS N'Τελευταία Σύνδεση', --LastLoggedUserDate
                (SELECT COUNT(*) FROM  Contacts WHERE Contacts.TenantId = Tenants.TenantId) AS N'Πελάτες', --TotalContacts
                (SELECT COUNT(*) FROM  Appointments WHERE Appointments.TenantId = Tenants.TenantId AND Appointments.Canceled = 0) AS  N'Συνεδρίες',  --TotalAppointments
                (SELECT COUNT(*) FROM  Users WHERE Users.TenantId = Tenants.TenantId) AS N'Χρήστες', --TotalUsers

                -- Average appointments per month for the last 12 months (dividing by 12)
                (SELECT COUNT(*) * 1.0 / 12
                 FROM Appointments
                 WHERE Appointments.TenantId = Tenants.TenantId
                   AND Appointments.StartTime >= DATEADD(MONTH, -12, GETDATE()) -- Only consider last 12 months
                ) AS N'Μέσος Όρος Συνεδριών ανά Μήνα (τελευταίους 12 μήνες)',  --AvgAppointmentsPerMonthForLast12Months

                -- Total appointments in the last 12 months
                (SELECT COUNT(*) 
                 FROM Appointments 
                 WHERE Appointments.TenantId = Tenants.TenantId 
                   AND Appointments.StartTime >= DATEADD(MONTH, -12, GETDATE())) 
                   AS N'Συνολικές Συνεδρίες (τελευταίους 12 μήνες)' --TotalAppointmentsLast12Months

                FROM Tenants";


                using (SqlCommand cmd = new SqlCommand(sqlText, connection))
                {
                    cmd.CommandType = CommandType.Text;

                    using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                    {
                        da.Fill(dt);
                    }
                }
            }

            return dt;
        }
        #endregion

        #region  AppointmentsCountPerYear

        public DataTable GetAppointmentsCountPerYear()
        {
            DataTable dt = new DataTable();
            string? connectionString = configuration.GetValue<string>("MentalViewConnectionString");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                string sqlText = @"WITH YearlyAppointments AS (
                    SELECT 
                        YEAR(StartTime) AS AppointmentYear,
                        COUNT(*) AS YearlyTotal
                    FROM 
                        Appointments
                    GROUP BY 
                        YEAR(StartTime)
                )
                SELECT 
                    ya.AppointmentYear,
                    ya.YearlyTotal AS CurrentYearAppointments,
                    SUM(ya2.YearlyTotal) AS CumulativeAppointments
                FROM 
                    YearlyAppointments ya
                JOIN 
                    YearlyAppointments ya2 ON ya2.AppointmentYear <= ya.AppointmentYear
                GROUP BY 
                    ya.AppointmentYear, ya.YearlyTotal
                ORDER BY 
                    ya.AppointmentYear;";

                using (SqlCommand cmd = new SqlCommand(sqlText, connection))
                {
                    cmd.CommandType = CommandType.Text;

                    using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                    {
                        da.Fill(dt);
                    }
                }
            }

            return dt;
        }


        public C1Report PrepareAppointmentsCountPerYearReport()
        {

            return null;
            
            //C1Report report = new C1Report();

            //DataTable dt = this.GetAppointmentsCountPerYear();

          

            //report.Load(this.environment.ContentRootPath + @"\Reporting\MentalViewAdminReports.xml", "AppointmentsCountPerYearReport");
            ////report.ReportName = report.ReportInfo.Title;
            ////report.Fields["ReportInfoField"].Text = string.Format("Συνεδρίες από {0} έως {1}", startDate.ToString("dd/MM/yyyy"), endDate.ToString("dd/MM/yyyy"));


            ////Ρυθμίζει τα data sources της εκτύπωσης.
            ////report.DataSource.DataProvider = DataProvider.ExternalObject;
            //report.DataSource.Recordset = dt;
            ////report.Layout.PaperSize = System.Drawing.Printing.PaperKind.A4;
            ////report.Layout.Orientation = OrientationEnum.Landscape;
            //return report;
        }

        #endregion
    }
}
