<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="Reporting\MentalViewAdminReports.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.3" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Syncfusion.Blazor" Version="28.2.11" />
    <PackageReference Include="Syncfusion.XlsIO.Net.Core" Version="28.2.11" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\fonts\font-awesome\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="efpt.config.json.user" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="C1.C1Report.2">
      <HintPath>C:\Program Files (x86)\ComponentOne\Studio for WinForms\bin\C1.C1Report.2.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Components\Layout\Resources\NavMenuResources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>NavMenuResources.resx</DependentUpon>
    </Compile>
    <Compile Update="Components\Pages\Resources\LoginResources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>LoginResources.resx</DependentUpon>
    </Compile>
    <Compile Update="Components\Pages\Resources\ReportsResources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ReportsResources.resx</DependentUpon>
    </Compile>
    <Compile Update="Components\Pages\Resources\TenantsResources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>TenantsResources.resx</DependentUpon>
    </Compile>
    <Compile Update="GlobalResources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>GlobalResources.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\SfResources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>SfResources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\scripts\app.js">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Components\Layout\Resources\NavMenuResources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>NavMenuResources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Components\Pages\Resources\LoginResources.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>LoginResources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Components\Pages\Resources\ReportsResources.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ReportsResources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Components\Pages\Resources\TenantsResources.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>TenantsResources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="GlobalResources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>GlobalResources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\SfResources.el.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\SfResources.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>SfResources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
