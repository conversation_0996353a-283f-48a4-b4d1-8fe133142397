﻿<%@ Page Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="EmailTemplates.aspx.cs" Inherits="WebUI.EmailTemplates" Culture="auto" meta:resourcekey="Page" UICulture="auto" %>

<asp:Content ID="mainHeadContent" ContentPlaceHolderID="mainHead" runat="server">
    <script src="LocalizedResources/ej.culture.en-US.min.js"></script>
    <script type="text/javascript">
        function Initialization() {
            SetLocalization();

            //$('#filterTxtBox').keydown(function (e) {
            //    if (e.keyCode == 13) {
            //        e.preventDefault();
            //        javascript: __doPostBack('searchBtn', '');
            //    }
            //});
        }
    </script>


</asp:Content>
<asp:Content ID="mainBodyContent" ContentPlaceHolderID="mainBody" runat="server">
    <div class="row">
        <div class="col-xs-12">
            <a id="newEmailTemplateBtn" href="EmailTemplate.aspx" runat="server" type="button" class="btn btn-primary btn-flat margin-bottom margin-r-5">
                <asp:Literal meta:resourcekey="newEmailTemplateBtn" runat="server"></asp:Literal></a>
        </div>
    </div>
    <asp:UpdatePanel ID="emailTemplatesUpdatePanel" runat="server">
        <ContentTemplate>

            <div class="row">
                <div class="col-xs-12 ">
                    <div class="input-group input-group-sm margin-bottom pull-right" style="min-width: 200px; max-width: 250px;">
                        <%--<input type="text" runat="server" id="filterTxtBox" class="form-control" maxlength="30">
                        <span class="input-group-btn">
                            <button type="button" id="clearSearchBtn" runat="server" class="btn btn-default btn-flat" style="border-radius: unset !important" onserverclick="clearSearchBtn_ServerClick">
                                <i class="fa fa-remove"></i>
                            </button>
                        </span>
                        <span class="input-group-btn">
                            <button type="button" id="searchBtn" runat="server" class="btn btn-info btn-flat" onserverclick="searchBtn_ServerClick">
                                <i class="fa fa-search  hidden-md hidden-lg"></i><asp:Label meta:resourcekey="searchBtn" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></button>
                        </span>--%>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <ej:Grid ID="emailTemplatesGrid" runat="server" Locale="el-GR" AllowMultiSorting="true" AllowSorting="True" AllowFiltering="true" Selectiontype="Single" IsResponsive="true" EnableResponsiveRow="true" MinWidth="900" OnServerRecordDoubleClick="emailTemplatesGrid_ServerRecordDoubleClick" OnServerCommandButtonClick="emailTemplatesGrid_ServerCommandButtonClick">
                        <SortedColumns>
                            <%--<ej:SortedColumn Field="RowIndex" Direction="Ascending" />--%>
                            <ej:SortedColumn Field="EmailTemplateId" Direction="Ascending" />
                        </SortedColumns>
                        <FilterSettings FilterType="FilterBar" IgnoreAccent="true" EnableCaseSensitivity="false" />
                        <%--<RowDropSettings DragBehavior="Move" DropMapper="EmailTemplates.aspx/Reordering" />--%>
                        <Columns>
                            <ej:Column Field="EmailTemplateId" HeaderText="A/A" IsIdentity="True" IsPrimaryKey="True" AllowSorting="true" Visible="True" Width="70">
                            </ej:Column>
                            <ej:Column Field="Title" HeaderText="Περιγραφή">
                            </ej:Column>
                            <ej:Column Field="Subject" HeaderText="Θέμα">
                            </ej:Column>
                            <ej:Column Field="EmailTemplateCategoryName" HeaderText="Κατηγορία" Width="250px">
                            </ej:Column>
                            <ej:Column HeaderText=" " IsUnbound="True" Width="60" AllowResizing="False" AllowSorting="False" AllowTextWrap="false">
                                <Command>
                                    <ej:Commands Type="Edit">
                                        <ButtonOptions Size="Small" Text="Edit" Type="Button" ContentType="ImageOnly" PrefixIcon="e-icon e-edit" Width="30"></ButtonOptions>
                                    </ej:Commands>
                                   <%-- Καταργήθηκε το custom indexing.
                                       <ej:Commands Type="Up">
                                        <ButtonOptions Size="Small" Text="Up" Type="Button" ContentType="ImageOnly" PrefixIcon="e-icon e-up-arrow" Width="30"></ButtonOptions>
                                    </ej:Commands>
                                    <ej:Commands Type="Down">
                                        <ButtonOptions Size="Small" Text="Down" Type="Button" ContentType="ImageOnly" PrefixIcon="e-icon e-down-arrow" Width="30"></ButtonOptions>
                                    </ej:Commands>--%>
                                </Command>
                                <Command></Command>
                            </ej:Column>
                            <ej:Column Field="RowIndex" HeaderText="" Visible="False" Width="0">
                            </ej:Column>

                        </Columns>
                    </ej:Grid>

                </div>
            </div>
        </ContentTemplate>
    </asp:UpdatePanel>
</asp:Content>
