/*!
*  filename: ej.mobile.editor.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function(n,t,i){t.widget({ejmTextBox:"ej.mobile.TextBox",ejmTextArea:"ej.mobile.TextArea",ejmPassword:"ej.mobile.Password",ejmNumeric:"ej.mobile.Numeric",ejmMaskEdit:"ej.mobile.MaskEdit"},{_setFirst:!0,_rootCSS:"e-m-editor",_requiresID:!0,validTags:["input","textarea"],defaults:{renderMode:"auto",cssClass:"",enablePersistence:!1,borderStyle:"box",watermarkText:"",value:"",readOnly:!1,type:"text",enabled:!0,maxLength:"",autoFocus:!1,height:"",width:"",step:1,minValue:-Number.MAX_VALUE,maxValue:Number.MAX_VALUE,format:"",locale:"en-US",touchStart:null,touchEnd:null,keyUp:null,keyDown:null},dataTypes:{renderMode:"enum",enablePersistence:"boolean",borderStyle:"enum",readOnly:"boolean",enabled:"boolean",autoFocus:"boolean",step:"number",minValue:"number",maxValue:"number",locale:"string"},observables:["value"],value:t.util.valueFunction("value"),_init:function(){this._cloneElement=this.element.clone();this.culture=t.preferredCulture(this.model.locale);this._getLocalizedLabels();this.model.watermarkText=!t.isNullOrUndefined(this.model.watermarkText)&&this.model.watermarkText!=""?this.model.watermarkText:this._localizedLabels.watermarkText;this._renderControl();this._wireEvents()},_getControlName:function(){return this.pluginName.replace("ejm","")},_getLocalizedLabels:function(){this._localizedLabels=t.getLocalizedConstants(this.sfType,this.model.locale)},_renderControl:function(){t.setRenderMode(this);var n=this.model;this.model.type=this.element.attr("type")?this.element.attr("type"):n.type;(this.element.attr("name")==""||t.isNullOrUndefined(this.element.attr("name")))&&this.element.attr("name",this._id);this._noClearBtn=["MaskEdit","Numeric","Password","TextArea"];this["_set"+this._getControlName()+"Properties"]()},_setTextBoxProperties:function(){this._setCommonWrapper(this.model.type);this._setCommonProperties()},_setPasswordProperties:function(){this._setCommonWrapper("password");this._setCommonProperties()},_setTextAreaProperties:function(){var n=this.model;this.element.addClass("e-m-"+n.renderMode+" e-m-editor "+n.cssClass+" e-m-input-wrapper e-m-textbox");this._setCommonProperties();n.height&&this._setHeight(n.height)},_setNumericProperties:function(){this._setCommonWrapper("number");this._numbersRegex=["0","1","2","3","4","5","6","7","8","9","-",".","Delete","Backspace","ArrowRight","Tab","ArrowLeft","Home","End","e"];this._keyPressCalled=!1;this._setCommonProperties();this.element.css("padding-right",64)},_setMaskEditProperties:function(){var n=this.model;this.element.addClass("e-m-"+n.renderMode+" e-m-input-wrapper e-m-mask "+n.cssClass);this._alphaNumeric=/^[a-zA-Z0-9]*$/;this._alphabetic=/^[a-zA-Z]/;this._numeric=/^[0-9]/;this._allowedKeys=["ArrowRight","Tab","ArrowLeft","Shift","Enter","Home","End","Ctrl"];this._restrictedKeys=["Backspace","ArrowUp","ArrowDown","Delete","VolumeMute","VolumeUp","VolumeDown","PageDown","PageUp","F1","F2","F3","F4","F5","F6","F7","F8","F9","F10","F11","F12"];this._islastindex=!1;this._keyDownCalled=!1;this._setCommonProperties()},_setCommonWrapper:function(n){this.model.type=n;this.element.attr("type",n);var i=this.model;this._inputWrapper=t.buildTag("div.e-m-"+i.renderMode+" e-m-editor "+i.cssClass+" e-m-input-wrapper");this.element.after(this._inputWrapper).addClass("e-m-textbox");this._inputWrapper.append(this.element);i.type!="number"?(this._rightIcon=t.buildTag("span"),this._inputWrapper.append(this._rightIcon)):(this._upBtn=t.buildTag("span.e-m-icon-up up-btn"),this._downBtn=t.buildTag("span.e-m-icon-down down-btn"),this._inputWrapper.append(this._upBtn).append(this._downBtn))},_setCssClass:function(){this._inputWrapper.addClass(this.model.cssClass)},_setCommonProperties:function(){var r=this.model,u;this._setBorderStyle();r.width!=""&&this._setWidth(r.width);r.type=="number"?this._setValue(this.value()==""?this._validateNumericInput(0):this._validateNumericInput(this.value())):(u=n(this.element).attr("value"),typeof u!=typeof i&&this.value(t.parseFloat(u)),this.value()&&this._setValue(this.value()));r.watermarkText!=""&&this._setWatermarkText(r.watermarkText);r.maxLength!=""&&this._setMaxLength(r.maxLength);r.autoFocus&&this._setAutoFocus(!0);r.readOnly&&this._setReadOnly(!0);r.enabled||this._setEnabled(!1)},_setMinValue:function(n){this.element.prop("disabled")||this.model.type!="number"||(this.element.attr("min",n),n>this.element.val()&&this.element.val(n))},_setMaxValue:function(n){this.element.prop("disabled")||this.model.type!="number"||(this.element.attr("max",n),n<this.element.val()&&this.element.val(n))},_setStep:function(n){this.model.type!="number"||this.element.prop("disabled")||!n||isNaN(n)||this.element.attr("step",n)},_setHeight:function(n){this._getControlName()=="TextArea"&&!this.element.prop("disabled")&&n&&this.element.height(n)},_setReadOnly:function(n){this.element.prop("disabled")||this.element.attr("readonly",n);this.model.type=="number"?(this._upBtn[n?"addClass":"removeClass"]("e-m-disabled"),this._downBtn[n?"addClass":"removeClass"]("e-m-disabled")):this._getControlName()!="MaskEdit"&&this._getControlName()!="TextArea"&&this._rightIcon[n?"removeClass":"addClass"]("e-m-disabled")},_setAutoFocus:function(n){!this.element.prop("disabled")&&n&&(this.element.focus(),this.element.prop("autofocus","autofocus"),this._getControlName()!="MaskEdit"&&this._getControlName()!="TextArea"&&this.element.parent().addClass("focus"))},_setMaxLength:function(n){!this.element.prop("disabled")&&n&&this.element.attr("maxlength",n)},_setEnabled:function(n){this.element.attr("disabled",!n);this._getControlName()=="TextArea"||this._getControlName()=="MaskEdit"?this.element[n?"removeClass":"addClass"]("e-m-state-disabled"):this.element.parent()[n?"removeClass":"addClass"]("e-m-state-disabled")},_setWidth:function(n){this.element.prop("disabled")||(this.element.width(n),(this._getControlName()!="MaskEdit"||this._getControlName()!="TextArea")&&this.element.parent().width(n))},_setWatermarkText:function(){this.element.prop("disabled")||this.element.attr("placeholder",this.model.watermarkText)},_setValue:function(n){this.element.prop("disabled")||(this.element.val(n),this.value(n))},_setBorderStyle:function(){this.element.prop("disabled")||(this._getControlName()!="MaskEdit"&&this._getControlName()!="TextArea"?this.element.parent().removeClass("e-m-line e-m-box e-m-none").addClass("e-m-"+this.model.borderStyle):this.element.removeClass("e-m-line e-m-box e-m-none").addClass("e-m-"+this.model.borderStyle))},_setRenderMode:function(n){this.element.prop("disabled")||this.element.removeClass("e-m-ios7 e-m-android e-m-windows e-m-flat").addClass("e-m-"+n)},_setLocale:function(){this._getLocalizedLabels();this.model.watermarkText=this._localizedLabels.watermarkText;this._setWatermarkText()},_createDelegate:function(){this._keyDownProxy=n.proxy(this._keyDown,this);this._keyUpProxy=n.proxy(this._keyUp,this);this._touchStartProxy=n.proxy(this._touchStart,this);this._touchEndProxy=n.proxy(this._touchEnd,this);this._focus=n.proxy(this._focus,this);this._blur=n.proxy(this._blur,this);this._keyPressProxy=n.proxy(this._keyPress,this);this._cutProxy=n.proxy(this._cut,this);this._pasteProxy=n.proxy(this._paste,this);this._docClickProxy=n.proxy(this._docClick,this);this._showPasswordProxy=n.proxy(this._showPassword,this);this._hidePasswordProxy=n.proxy(this._hidePassword,this);this._clearInputProxy=n.proxy(this._clearInput,this);this._stepProxy=n.proxy(this._step,this);this._stepCloseProxy=n.proxy(this._stepClose,this)},_wireEvents:function(i){var u=this.model,r=this.element,f=this._getControlName();this._createDelegate();(f=="MaskEdit"||f=="Numeric")&&t.listenTouchEvent(r,"keypress",this._keyPressProxy,i);u.type=="number"&&t.listenEvents([this._upBtn,this._downBtn,this._upBtn,this._downBtn,this._upBtn,this._downBtn,this._upBtn,this._downBtn],[t.startEvent(),t.startEvent(),t.endEvent(),t.endEvent(),t.cancelEvent(),t.cancelEvent(),"mouseout","mouseout"],[this._stepProxy,this._stepProxy,this._stepCloseProxy,this._stepCloseProxy,this._stepCloseProxy,this._stepCloseProxy,this._stepCloseProxy,this._stepCloseProxy],i);u.type=="password"&&t.listenEvents([this._rightIcon,this._rightIcon],[t.startEvent(),t.endEvent()],[this._showPasswordProxy,this._hidePasswordProxy],i);this._noClearBtn.indexOf(this._getControlName())==-1&&t.listenTouchEvent(this._rightIcon,"tap",this._clearInputProxy,i);t.listenEvents([r,r,r,r,r,r,r,r,n(document)],[t.startEvent(),t.endEvent(),"keydown","keyup","focus","blur","cut","paste","click"],[this._touchStartProxy,this._touchEndProxy,this._keyDownProxy,this._keyUpProxy,this._focus,this._blur,this._cutProxy,this._pasteProxy,this._docClickProxy],i)},_docClick:function(t){n(t.target).hasClass("e-m-editor")||n(t.target).hasClass("right-eye-btn e-m-icon-eye")||n(t.target).hasClass("e-m-icon-clear")||this._removecleariconclass()},_cut:function(n){(this._getControlName()=="MaskEdit"||this.model.type=="number")&&n.preventDefault()},_paste:function(n){(this._getControlName()=="MaskEdit"||this.model.type=="number")&&n.preventDefault()},_blur:function(){(this.model.type=="password"||this.model.type=="number"||this.model.type=="text"||this.model.type=="tel"||this.model.type=="email")&&this.element.parent().removeClass("focus");this.model.type=="number"&&this._setValue(this._validateNumericInput(this.element.val()));this._removecleariconclass()},_removecleariconclass:function(){if(this.value()){var n=this._getControlName()=="Password"?"eye":this._noClearBtn.indexOf(this._getControlName())==-1?"clear":"";n&&this._rightIcon.removeClass("right-"+n+"-btn e-m-icon-"+n)}},_step:function(t){if(this.model.enabled&&this.model.type=="number"){var r=this,i=n(t.target).hasClass("e-m-icon-down")?"down":"up";this._updateValue(i);this._timeout=setInterval(function(){r._updateValue(i)},200)}},_updateValue:function(n){var t=this.element.val()==""?this._validateNumericInput(0):n=="down"?parseFloat(this.element.val())-this.model.step:parseFloat(this.element.val())+this.model.step;this.model.step.toString().indexOf(".")!=-1&&(t=t.toFixed(this._trimfloat(this.model.step.toString())));this._setValue(this._validateNumericInput(t))},_stepClose:function(){clearTimeout(this._timeout)},_focus:function(){if(this._getControlName()!="MaskEdit"&&this._getControlName()!="TextArea"&&this.element.parent().addClass("focus"),this.value()){var n=this._getControlName()=="Password"?"eye":this._noClearBtn.indexOf(this._getControlName())==-1?"clear":"";n&&this._rightIcon.addClass("right-"+n+"-btn e-m-icon-"+n)}},_showPassword:function(){this.element.attr("type","text")},_hidePassword:function(){this.element.attr("type","password")},_clearInput:function(){this._rightIcon.hasClass("e-m-icon-clear")&&(this._rightIcon.removeClass("right-clear-btn e-m-icon-clear"),this._setValue(""))},_touchStart:function(){this.model.touchStart&&!this.model.readOnly&&this._trigger("touchStart",{value:this.element.val()})},_touchEnd:function(){var n={value:this.element.val()};this.model.touchEnd&&!this.model.readOnly&&this._trigger("touchEnd",n)},_setCharAt:function(n,t,i){return t>n.length-1?n:n.substr(0,t)+i+n.substr(t+1)},_validateMaskInput:function(n){if((this._charType=="alphabetic"||this._charType=="numeric"||n=="Backspace"||n=="Delete")&&this._charType!="")if(this["_"+this._charType].test(n)||n=="Delete"||n=="Backspace")if(this._replace){var t=n=="Backspace"||n=="Delete"?"_":this._restrictedKeys.indexOf(n)==-1?n:"";if(t=="")return!1;this.value(this._setCharAt(this.value(),n=="Backspace"?this._charIndex-1:this._charIndex,t));this.element.val(this.value());this._setCursor(this.element[0],n=="Backspace"?this._charIndex-1:this._charIndex+1)}else this._restrictedKeys.indexOf(n)==-1&&(this.value(this.value()+n),this.element.val(this.value()));else this["_"+this._charType].test(n)?this._setCursor(this.element[0],this._charIndex):(this.element.val(this.value()),this._setCursor(this.element[0],this._charIndex))},_getFormatCharType:function(n){this._formatChar=this.model.format.toString().charAt(n);return this._alphaNumeric.test(this._formatChar)?this._numeric.test(this._formatChar)?"numeric":this._alphabetic.test(this._formatChar)?"alphabetic":"":"symbol"},_setMaskSpecialChar:function(){this.value(this.value()+this._formatChar);this.element.val(this.value())},_setCursor:function(n,t){if(n=typeof n=="string"||n instanceof String?document.getElementById(n):n,n){if(n.createTextRange){var i=n.createTextRange();return i.collapse(!0),i.moveEnd("character",t),i.moveStart("character",t),i.select(),!0}if(n.setSelectionRange)return n.setSelectionRange(t,t),!0}else return!1;return!1},_handleMaskValue:function(n,t){if(this._allowedKeys.indexOf(t)==-1){if(this._charIndex=this.element[0].selectionStart,this.element[0].selectionEnd-this._charIndex==1&&this._charIndex==0&&n.keyCode==8&&(this._charIndex=1),this._islastindex=this._charIndex==this.model.format.length?!0:!1,this._replace=this.value().charAt(this._charIndex)||t=="Backspace"?!0:!1,this._charType=this._getFormatCharType(t=="Backspace"?this._charIndex-1:this._charIndex),!this._islastindex||t=="Backspace"){if(this._charType=="symbol"){var i=this._charIndex;do this._replace?(this._setCursor(this.element[0],t=="Backspace"?i:i+1),this._charIndex=this.element[0].selectionStart):this._setMaskSpecialChar(this.value()+this._formatChar,i),t=="Backspace"?i--:i++,this._charType=this._getFormatCharType(i);while(this._charType=="symbol")}this._validateMaskInput(t)}n.preventDefault()}},_trimfloat:function(n){return(n.split(".")[1]||[]).length},_validateNumericInput:function(n){n=parseFloat(n);var t=this.model.minValue,i=this.model.maxValue;return isNaN(n)&&(n=0),n>t&&n<i?n:n==t||n<t?t:i},_keyDown:function(n){if(this._keyDownCalled=!1,this._getControlName()=="MaskEdit"&&(n.keyCode==8||n.keyCode==46)){var t=n.keyCode==8?"Backspace":"Delete";this._handleMaskValue(n,t);this._keyDownCalled=!0}this.model.keyDown&&!this.model.readOnly&&this._trigger("keyDown",{value:this.element.val(),keyCode:n.keyCode})},_keyPress:function(n){var t=n.key?n.key:String.fromCharCode(n.keyCode||n.charCode);this._getControlName()=="MaskEdit"?t=="BackSpace"||t=="Delete"?this._keyDownCalled||this._handleMaskValue(n,t):this._handleMaskValue(n,t):this.model.type=="number"&&this._numbersRegex.indexOf(t)==-1&&n.preventDefault()},_keyUp:function(n){var r=this.model,i,t;r.readOnly?n.preventDefault():this._getControlName()=="MaskEdit"&&(n.keyCode==229||n.keyCode==0)?(i=String.fromCharCode(n.which),(n.keyCode==229||n.keyCode==0)&&(i=String.fromCharCode(this.element.val().charCodeAt(this.element[0].selectionStart-1)),this.element[0].selectionStart=this.element[0].selectionStart-1),this._handleMaskValue(n,i),this.element.val(this.value()),n.preventDefault()):this._getControlName()!="MaskEdit"&&r.type!="number"&&this._getControlName()!="TextArea"?(this.value(this.element.val()),t=this._getControlName()=="Password"?"eye":this._noClearBtn.indexOf(this._getControlName())==-1?"clear":"",t&&this._rightIcon[this.value()?"addClass":"removeClass"]("right-"+t+"-btn e-m-icon-"+t),this.value()&&this.element.css("padding-right",32)):(this.value(this.element.val()),n.preventDefault());this.model.keyUp&&!this.model.readOnly&&this._trigger("keyUp",{value:this.element.val(),keyCode:n.keyCode})},_setModel:function(n){var r=!1,t,i;for(t in n)i="_set"+t.charAt(0).toUpperCase()+t.slice(1),this[i]?this[i](n[t]):r=!0;r&&this._refresh()},_clearElement:function(){t.isNullOrUndefined(this._inputWrapper)?this.element.replaceWith(this._cloneElement):(this._inputWrapper.before(this._cloneElement),this._inputWrapper.remove(),this._inputWrapper=null);this.element=this._cloneElement;this.element.removeAttr("class")},_destroy:function(){this._wireEvents(!0);this._clearElement()},_refresh:function(){this._destroy();this.element.addClass("e-m-editor");this._init()}});t.mobile.TextBox.BorderStyle={Box:"box",Line:"line",None:"none"};t.mobile.TextBox.Type={Number:"number",Text:"text",Password:"password",Tel:"tel",Email:"email"};t.mobile.TextBox.Locale=t.mobile.TextBox.Locale||{};t.mobile.TextArea.Locale=t.mobile.TextArea.Locale||{};t.mobile.Password.Locale=t.mobile.Password.Locale||{};t.mobile.Numeric.Locale=t.mobile.Numeric.Locale||{};t.mobile.MaskEdit.Locale=t.mobile.MaskEdit.Locale||{};t.mobile.TextBox.Locale["default"]=t.mobile.TextBox.Locale["en-US"]=t.mobile.TextArea.Locale["default"]=t.mobile.TextArea.Locale["en-US"]=t.mobile.Password.Locale["default"]=t.mobile.Password.Locale["en-US"]=t.mobile.Numeric.Locale["default"]=t.mobile.Numeric.Locale["en-US"]=t.mobile.MaskEdit.Locale["default"]=t.mobile.MaskEdit.Locale["en-US"]={watermarkText:""}})(jQuery,Syncfusion)});
