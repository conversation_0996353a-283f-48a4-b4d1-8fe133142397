﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="abusesLbl.Text" xml:space="preserve">
    <value>Καταχρήσεις, χρήση ουσιών, εξαρτήσεις</value>
  </data>
  <data name="accessLevelLbl.Text" xml:space="preserve">
    <value>Επίπεδο Πρόσβασης</value>
  </data>
  <data name="activeLbl.Text" xml:space="preserve">
    <value>Ενεργός</value>
  </data>
  <data name="addictiveLbl.Text" xml:space="preserve">
    <value>Εξαρτητική</value>
  </data>
  <data name="addressLbl.Text" xml:space="preserve">
    <value>Διεύθυνση</value>
  </data>
  <data name="adhesionTypeLbl.Text" xml:space="preserve">
    <value>Τύπος Προσκόλησης</value>
  </data>
  <data name="adultExperiencesLbl.Text" xml:space="preserve">
    <value>Ενήλικες Εμπειρίες</value>
  </data>
  <data name="afmLbl.Text" xml:space="preserve">
    <value>ΑΦΜ</value>
  </data>
  <data name="agoraphobiaLbl.Text" xml:space="preserve">
    <value>Αγοραφοβία</value>
  </data>
  <data name="allophagiaLbl.Text" xml:space="preserve">
    <value>Αλλοτριοφαγία (Pica)</value>
  </data>
  <data name="anorexiaLbl.Text" xml:space="preserve">
    <value>Ψυχογενής Ανορεξία</value>
  </data>
  <data name="antisocialLbl.Text" xml:space="preserve">
    <value>Αντικοινωνική</value>
  </data>
  <data name="anxietyLbl.Text" xml:space="preserve">
    <value>Αγχώδεις</value>
  </data>
  <data name="anxietyPhysicalSymptomsLbl.Text" xml:space="preserve">
    <value>Άγχος Σωματικά Συμπτώματα</value>
  </data>
  <data name="anxiousInfoLbl.Text" xml:space="preserve">
    <value>Αγχώδεις</value>
  </data>
  <data name="appointmentCategoryIdLbl.Text" xml:space="preserve">
    <value>Κατηγορία</value>
  </data>
  <data name="appointmentsFrequencyLbl.Text" xml:space="preserve">
    <value>Συχνότητα Συνεδρίων</value>
  </data>
  <data name="appointmentsStartLbl.Text" xml:space="preserve">
    <value>Έναρξη Συνεδρίων</value>
  </data>
  <data name="avoidableLbl.Text" xml:space="preserve">
    <value>Αποφευτική</value>
  </data>
  <data name="birthDateLbl.Text" xml:space="preserve">
    <value>Ημ/νία Γέννησης</value>
  </data>
  <data name="birthPlaceLbl.Text" xml:space="preserve">
    <value>Τόπος Γέννησης</value>
  </data>
  <data name="bodyLanguageLbl.Text" xml:space="preserve">
    <value>Γλώσσα Σώματος</value>
  </data>
  <data name="bulimiaLbl.Text" xml:space="preserve">
    <value>Ψυχογενής Βουλιμία</value>
  </data>
  <data name="caseFormulationLbl.Text" xml:space="preserve">
    <value>Διατύπωση Περίπτωσης</value>
  </data>
  <data name="catatonicLbl.Text" xml:space="preserve">
    <value>Κατατονική (έμφαση στη βαρύτητα)</value>
  </data>
  <data name="chatGptDiagnosisAndTherapyBtn.Text" xml:space="preserve">
    <value>ChatGPT Διάγνωση &amp; Θεραπεία</value>
  </data>
  <data name="childrenAgeLbl.Text" xml:space="preserve">
    <value>Ηλικία Τέκνων</value>
  </data>
  <data name="childrenLbl.Text" xml:space="preserve">
    <value>Τέκνα</value>
  </data>
  <data name="chronicLbl.Text" xml:space="preserve">
    <value>Χρόνια</value>
  </data>
  <data name="cityLbl.Text" xml:space="preserve">
    <value>Πόλη</value>
  </data>
  <data name="clinicalEvaluationInfoLbl.Text" xml:space="preserve">
    <value>Πληροφορίες από Κλινικές Εκτιμήσεις</value>
  </data>
  <data name="clinicSupervisorIdLbl.Text" xml:space="preserve">
    <value>Κλινική Εποπτεία</value>
  </data>
  <data name="communicationMethodLbl.Text" xml:space="preserve">
    <value>Τρόπος Επικοινωνίας</value>
  </data>
  <data name="ContactAddressRequiredMessage" xml:space="preserve">
    <value>Το πεδίο Διεύθυνση δεν έχει συμπληρωθεί.</value>
  </data>
  <data name="contactCodeLbl.Text" xml:space="preserve">
    <value>Κωδικός</value>
  </data>
  <data name="ContactEmailRequiredMessage" xml:space="preserve">
    <value>Το πεδιο Email δεν έχει συμπληρωθεί.</value>
  </data>
  <data name="contactEmailTemplatesLbl.Text" xml:space="preserve">
    <value>Email Templates</value>
  </data>
  <data name="ContactFullNameRequiredMessage" xml:space="preserve">
    <value>Τα πεδία Όνομα και Επώνυμο δεν έχουν συμπληρωθεί.</value>
  </data>
  <data name="ContactMobile1RequiredMessage" xml:space="preserve">
    <value>Το πεδίο Κινητό 1 δεν έχει συμπληρωθεί.</value>
  </data>
  <data name="ContactPostalCodeRequiredMessage" xml:space="preserve">
    <value>Το πεδίο Ταχ. Κώδικας δεν έχει συμπληρωθεί.</value>
  </data>
  <data name="contactReportLbl.Text" xml:space="preserve">
    <value>Εκτύπωση</value>
  </data>
  <data name="coTherapistIdDDL.Text" xml:space="preserve">
    <value>Συνθεραπευτής</value>
  </data>
  <data name="cyclothymiaLbl.Text" xml:space="preserve">
    <value>Κυκλοθυμία</value>
  </data>
  <data name="defenseMechanismsLbl.Text" xml:space="preserve">
    <value>Αμυντικοί Μηχανισμοί</value>
  </data>
  <data name="delirusiveLbl.Text" xml:space="preserve">
    <value>Παραληρητική</value>
  </data>
  <data name="depressivePseudonoiaLbl.Text" xml:space="preserve">
    <value>Καταθλιπτική Ψευδοάνοια</value>
  </data>
  <data name="developmentalLbl.Text" xml:space="preserve">
    <value>Αναπτυξιακές</value>
  </data>
  <data name="diagnosticianIdLbl.Text" xml:space="preserve">
    <value>Διαγνωστικά</value>
  </data>
  <data name="dipolicLbl.Text" xml:space="preserve">
    <value>Διπολική</value>
  </data>
  <data name="doyLbl.Text" xml:space="preserve">
    <value>Δ.Ο.Υ.</value>
  </data>
  <data name="dysfunctionalBehaviorsLbl.Text" xml:space="preserve">
    <value>Συμπτώματα/Δυσλειτουργικές συμπεριφορές</value>
  </data>
  <data name="dysmorphobiaLbl.Text" xml:space="preserve">
    <value>Σωματοδυσμορφικη Διαταραχή (Δυσμορφοβια)</value>
  </data>
  <data name="dysthymiaLbl.Text" xml:space="preserve">
    <value>Δυσθυμία</value>
  </data>
  <data name="eatingDisorderLbl.Text" xml:space="preserve">
    <value>Διατροφικές</value>
  </data>
  <data name="eatingDisordersInfoLbl.Text" xml:space="preserve">
    <value>Διαταραχές Πρόσληψης Τροφής</value>
  </data>
  <data name="economicStatusLbl.Text" xml:space="preserve">
    <value>Οικον. Κατάσταση</value>
  </data>
  <data name="educationLevelLbl.Text" xml:space="preserve">
    <value>Επίπεδο Μόρφωσης</value>
  </data>
  <data name="elassonDepressiveDisorderLbl.Text" xml:space="preserve">
    <value>Ελάσσων Καταθλιπτική Διαταραχή</value>
  </data>
  <data name="email1Lbl.Text" xml:space="preserve">
    <value>Email 1</value>
  </data>
  <data name="Email2IsEmptyMessage" xml:space="preserve">
    <value>Το email2 του πελάτη δεν είναι συμπληρωμένο.</value>
  </data>
  <data name="email2Lbl.Text" xml:space="preserve">
    <value>Email 2</value>
  </data>
  <data name="EmailIsEmptyMessage" xml:space="preserve">
    <value>Το email του πελάτη δεν είναι συμπληρωμένο.</value>
  </data>
  <data name="EmailIsInvalidMessage" xml:space="preserve">
    <value>Το email του πελάτη δεν είναι έγκυρο.</value>
  </data>
  <data name="EmailTemplateRequiredMessage" xml:space="preserve">
    <value>Επιλέξτε ένα email template.</value>
  </data>
  <data name="emailTemplatesLbl.Text" xml:space="preserve">
    <value>Email Template</value>
  </data>
  <data name="emergencyContactNameLbl.Text" xml:space="preserve">
    <value>Επαφή Έκτακτης Ανάγκης</value>
  </data>
  <data name="emergencyPhoneLbl.Text" xml:space="preserve">
    <value>Τηλέφωνο Έκτακτης Ανάγκης</value>
  </data>
  <data name="emotionalDifficultiesLbl.Text" xml:space="preserve">
    <value>Συναισθηματικές Δυσκολίες</value>
  </data>
  <data name="emotionalProfileLbl.Text" xml:space="preserve">
    <value>Συναισθηματικό προφίλ</value>
  </data>
  <data name="emotionalRemarksLbl.Text" xml:space="preserve">
    <value>Πρόσθετες Παρατηρήσεις σε Συναισθηματικά ή Αναπτυξιακά Ζητήματα</value>
  </data>
  <data name="epilochiaLbl.Text" xml:space="preserve">
    <value>Επιλόχεια</value>
  </data>
  <data name="eyeContactLbl.Text" xml:space="preserve">
    <value>Βλεματική Επαφή</value>
  </data>
  <data name="familyMedicalHistoryLbl.Text" xml:space="preserve">
    <value>Ιατρικό ιστορικό Οικογένειας</value>
  </data>
  <data name="fatherCharacteristicsLbl.Text" xml:space="preserve">
    <value>Χαρακτηριστικά Πατέρα</value>
  </data>
  <data name="fatherFamilyHistoryLbl.Text" xml:space="preserve">
    <value>Οικογενειακό Ιστορικό Πατέρα</value>
  </data>
  <data name="fatherFeedbackInMyFailureLbl.Text" xml:space="preserve">
    <value>Ανατροφοδότηση Πατέρα σε Αποτυχία μου</value>
  </data>
  <data name="fatherFeedbackInMySuccessLbl.Text" xml:space="preserve">
    <value>Ανατροφοδότηση Πατέρα σε Επιτυχία μου</value>
  </data>
  <data name="fatherInfoLbl.Text" xml:space="preserve">
    <value>Πληροφορίες για τον Πατέρα</value>
  </data>
  <data name="firstNameLbl.Text" xml:space="preserve">
    <value>Όνομα</value>
  </data>
  <data name="foodIntakeDisorderLbl.Text" xml:space="preserve">
    <value>Αποφευκτική/Περιοριστική διαταραχή πρόσληψης τροφής (Avoidant/Restrictive food intake disorder)</value>
  </data>
  <data name="generalBiographicalInfoLbl.Text" xml:space="preserve">
    <value>Γενικά Σχόλια για τις Βιοματικές Πληροφορίες</value>
  </data>
  <data name="generalizedAxietyLbl.Text" xml:space="preserve">
    <value>Γενικευμένη Αγχώδης</value>
  </data>
  <data name="generalRequestLbl.Text" xml:space="preserve">
    <value>Γενικό Αίτημα</value>
  </data>
  <data name="generalTraumaHistoryTxtLbl.Text" xml:space="preserve">
    <value>Γενικό Τραυματικό Ιστορικό</value>
  </data>
  <data name="guestIdLbl.Text" xml:space="preserve">
    <value>Guest</value>
  </data>
  <data name="healingExperienceLbl.Text" xml:space="preserve">
    <value>Θεραπευτική Εμπειρία</value>
  </data>
  <data name="healthHistoryLbl.Text" xml:space="preserve">
    <value>Ιστορικό Υγείας</value>
  </data>
  <data name="histronicLbl.Text" xml:space="preserve">
    <value>Ιστριονική</value>
  </data>
  <data name="identityNumberLbl.Text" xml:space="preserve">
    <value>Αρ. Ταυτότητας</value>
  </data>
  <data name="ideoPsychoComplusionsLbl.Text" xml:space="preserve">
    <value>Ιδεοψυχαναγκαστική Καταναγκασμοί</value>
  </data>
  <data name="ideopsychocompressionLbl.Text" xml:space="preserve">
    <value>Ιδεοψυχαναγκαστική</value>
  </data>
  <data name="importantFeedbackInMyFailureLbl.Text" xml:space="preserve">
    <value>Ανατροφοδότηση Σημαντικού σε Αποτυχία μου</value>
  </data>
  <data name="importantFeedbackInMySuccessLbl.Text" xml:space="preserve">
    <value>Ανατροφοδότηση Σημαντικού σε Επιτυχία μου</value>
  </data>
  <data name="inactiveReasonLbl.Text" xml:space="preserve">
    <value>Αιτία Ανενεργίας</value>
  </data>
  <data name="informalLbl.Text" xml:space="preserve">
    <value>Άτυπη</value>
  </data>
  <data name="insomniaLbl.Text" xml:space="preserve">
    <value>Αϋπνία</value>
  </data>
  <data name="interventionModelLbl.Text" xml:space="preserve">
    <value>Μοντέλο Παρέμβασης</value>
  </data>
  <data name="lastMedicalCheckupDateLbl.Text" xml:space="preserve">
    <value>Ημ/νία Τελευτ. Ιατρικού Ελέγχου</value>
  </data>
  <data name="lastMedicalCheckupLbl.Text" xml:space="preserve">
    <value>Τελευταίος Ιατρικός Έλεγχος</value>
  </data>
  <data name="lastNameLbl.Text" xml:space="preserve">
    <value>Επώνυμο</value>
  </data>
  <data name="learningsLbl.Text" xml:space="preserve">
    <value>Μαθησιακές</value>
  </data>
  <data name="livingStatusLbl.Text" xml:space="preserve">
    <value>Καθεστώς Διαβίωσης</value>
  </data>
  <data name="majorDepressionLbl.Text" xml:space="preserve">
    <value>Κατάθλιψη Μείζωνα</value>
  </data>
  <data name="maritalStatusLbl.Text" xml:space="preserve">
    <value>Οικογ./Προσωπική</value>
  </data>
  <data name="medicationLbl.Text" xml:space="preserve">
    <value>Φαρμακευτική Αγωγή</value>
  </data>
  <data name="melancholicLbl.Text" xml:space="preserve">
    <value>Μελαγχολική (έμφαση στη βαρύτητα)</value>
  </data>
  <data name="mobile1Lbl.Text" xml:space="preserve">
    <value>Κινητό 1</value>
  </data>
  <data name="mobile2Lbl.Text" xml:space="preserve">
    <value>Κινητό 2</value>
  </data>
  <data name="mobile3Lbl.Text" xml:space="preserve">
    <value>Κινητό 3</value>
  </data>
  <data name="moodsInfoLbl.Text" xml:space="preserve">
    <value>Διαθέσεις</value>
  </data>
  <data name="moodsLbl.Text" xml:space="preserve">
    <value>Διαθεσεις</value>
  </data>
  <data name="motherCharacteristicsLbl.Text" xml:space="preserve">
    <value>Χαρακτηριστικά Μητέρας</value>
  </data>
  <data name="motherFamilyHistoryLbl.Text" xml:space="preserve">
    <value>Οικογενειακό Ιστορικό Μητέρας</value>
  </data>
  <data name="motherFeedbackInMyFailureLbl.Text" xml:space="preserve">
    <value>Ανατροφοδότηση Μητέρας σε Αποτυχία μου</value>
  </data>
  <data name="motherFeedbackInMySuccessLbl.Text" xml:space="preserve">
    <value>Ανατροφοδότηση Μητέρας σε Επιτυχία μου</value>
  </data>
  <data name="motherInfoLbl.Text" xml:space="preserve">
    <value>Πληροφορίες για την Μητέρα</value>
  </data>
  <data name="narcissisticLbl.Text" xml:space="preserve">
    <value>Ναρκισσιστική</value>
  </data>
  <data name="narrationLbl.Text" xml:space="preserve">
    <value>Αφήγηση</value>
  </data>
  <data name="negativeBeliefsLbl.Text" xml:space="preserve">
    <value>Αρνητικές Πεποιθήσεις</value>
  </data>
  <data name="negativeEmotionsLbl.Text" xml:space="preserve">
    <value>Αρνητικά Συναισθήματα</value>
  </data>
  <data name="neurodevelopmentalLbl.Text" xml:space="preserve">
    <value>Νευροαναπτυξιακές</value>
  </data>
  <data name="nonSpecifiedDisorderLbl.Text" xml:space="preserve">
    <value>Ψ Διαταραχή Μη Προσδιορισμένη Αλλιώς</value>
  </data>
  <data name="notesLbl.Text" xml:space="preserve">
    <value>Σημειώσεις</value>
  </data>
  <data name="obsessiveIdeasLbl.Text" xml:space="preserve">
    <value>Ιδεοψυχαναγκαστική Ιδεοληψίες</value>
  </data>
  <data name="occupationLbl.Text" xml:space="preserve">
    <value>Ιδιότητα Επάγγελμα</value>
  </data>
  <data name="onLimitLbl.Text" xml:space="preserve">
    <value>Οριακή</value>
  </data>
  <data name="organicPhsychosyndromeLbl.Text" xml:space="preserve">
    <value>Οργανικό Ψυχοσύνδρομο</value>
  </data>
  <data name="orthorexiaLbl.Text" xml:space="preserve">
    <value>Oρθορεξία</value>
  </data>
  <data name="osfedLbl.Text" xml:space="preserve">
    <value>Διατροφ. Διαταρ. μη προσδιοριζόμενες (OSFED)</value>
  </data>
  <data name="otherActivitiesLbl.Text" xml:space="preserve">
    <value>Άλλες Δραστηριότητες</value>
  </data>
  <data name="otherDisorderInfoLbl.Text" xml:space="preserve">
    <value>Άλλες Πληροφορίες Συσχετιζόμενες με Διαταραχές</value>
  </data>
  <data name="otherImportantFromFatherFamilyLbl.Text" xml:space="preserve">
    <value>Άλλος Σημαντικός από Οικ. Πατέρα</value>
  </data>
  <data name="otherImportantFromMotherFamilyLbl.Text" xml:space="preserve">
    <value>Άλλος Σημαντικός από Οικ. Μητέρας</value>
  </data>
  <data name="otherImportantLbl.Text" xml:space="preserve">
    <value>Άλλος Σημαντικός</value>
  </data>
  <data name="otherMoodObservationsLbl.Text" xml:space="preserve">
    <value>Άλλες Παρατηρήσεις στις Διαθέσεις</value>
  </data>
  <data name="otherSleepObservationsLbl.Text" xml:space="preserve">
    <value>Άλλες Παρατηρήσεις Σχετικές με τον Ύπνο</value>
  </data>
  <data name="otherStressObservationsLbl.Text" xml:space="preserve">
    <value>Άλλες Παρατηρήσεις στις Αγχώδεις</value>
  </data>
  <data name="overeatingLbl.Text" xml:space="preserve">
    <value>Επεισοδιακή Υπερφαγία</value>
  </data>
  <data name="Page.Title" xml:space="preserve">
    <value>Πελάτης</value>
  </data>
  <data name="panicDisorderLbl.Text" xml:space="preserve">
    <value>Διαταραχή Πανικού</value>
  </data>
  <data name="paracumulationDisorderLbl.Text" xml:space="preserve">
    <value>Δ/χη Παρασυσσόρευσης</value>
  </data>
  <data name="paranoidLbl.Text" xml:space="preserve">
    <value>Παρανοειδής</value>
  </data>
  <data name="passiveAggressiveLbl.Text" xml:space="preserve">
    <value>Παθητική Επιθετική</value>
  </data>
  <data name="pendingIssuesLbl.Text" xml:space="preserve">
    <value>Εκκρεμότητες</value>
  </data>
  <data name="persistentDisorderLbl.Text" xml:space="preserve">
    <value>Επιμενόμενη Ψ Διαταραχή</value>
  </data>
  <data name="personalityInfoLbl.Text" xml:space="preserve">
    <value>Προσωπικότητα</value>
  </data>
  <data name="phone1Lbl.Text" xml:space="preserve">
    <value>Τηλέφωνο 1</value>
  </data>
  <data name="phone2Lbl.Text" xml:space="preserve">
    <value>Τηλέφωνο 2</value>
  </data>
  <data name="phone3Lbl.Text" xml:space="preserve">
    <value>Τηλέφωνο 3</value>
  </data>
  <data name="phychoticSymptomsLbl.Text" xml:space="preserve">
    <value>Συμπτώματα Ψ</value>
  </data>
  <data name="physicalConditionDisorderLbl.Text" xml:space="preserve">
    <value>Ψ Διαταραχή από Σωματική Κατάσταση</value>
  </data>
  <data name="postalLbl.Text" xml:space="preserve">
    <value>Ταχ. Κώδικας</value>
  </data>
  <data name="postTraumaticStressLbl.Text" xml:space="preserve">
    <value>Μετατραυματικό Στρες</value>
  </data>
  <data name="pregnancyLbl.Text" xml:space="preserve">
    <value>Εγκυμοσύνης</value>
  </data>
  <data name="premenstrualDysphoricDisorderLbl.Text" xml:space="preserve">
    <value>Προεμμηνορρυσιακή Δυσφορική Διαταραχή</value>
  </data>
  <data name="preschoolExperiencesLbl.Text" xml:space="preserve">
    <value>Προσχολικές Εμπειρίες</value>
  </data>
  <data name="printContactAppointmetsLbl.Text" xml:space="preserve">
    <value>Εκτύπωση Συνεδριών</value>
  </data>
  <data name="printContactDataLbl.Text" xml:space="preserve">
    <value>Εκτύπωση Στοιχείων</value>
  </data>
  <data name="printContactQuestionnairesLbl.Text" xml:space="preserve">
    <value>Εκτύπωση Ερωτηματολογίων</value>
  </data>
  <data name="psychotherapyStartDateLbl.Text" xml:space="preserve">
    <value>Έναρξη Ψυχοθεραπείας</value>
  </data>
  <data name="psychoticDepressionLbl.Text" xml:space="preserve">
    <value>Ψυχωτική Κατάθλιψη</value>
  </data>
  <data name="psychoticInfoLbl.Text" xml:space="preserve">
    <value>Ψυχωσικά</value>
  </data>
  <data name="psychoticLbl.Text" xml:space="preserve">
    <value>Ψυχωσικά</value>
  </data>
  <data name="questionnaireNameLbl.Text" xml:space="preserve">
    <value>Ονομασία</value>
  </data>
  <data name="questionnairesTitleLbl.Text" xml:space="preserve">
    <value>Ερωτηματολόγια</value>
  </data>
  <data name="questionnairiesLinkLbl.Text" xml:space="preserve">
    <value>Σύνδεσμος</value>
  </data>
  <data name="questionnairiesQuestionsTitleLbl.Text" xml:space="preserve">
    <value>Ερωτήσεις</value>
  </data>
  <data name="referralToAnotherSpecialistLbl.Text" xml:space="preserve">
    <value>Παραπομπή σε Άλλον Ειδικό</value>
  </data>
  <data name="regionLbl.Text" xml:space="preserve">
    <value>Περιοχή Κατοικίας</value>
  </data>
  <data name="regressionLbl.Text" xml:space="preserve">
    <value>Υποτροπιάζουσα</value>
  </data>
  <data name="residenceLbl.Text" xml:space="preserve">
    <value>Τόπος Κατοικίας</value>
  </data>
  <data name="responsibleNameLbl.Text" xml:space="preserve">
    <value>Υπεύθυνος</value>
  </data>
  <data name="ruminationDisorderLbl.Text" xml:space="preserve">
    <value>Διαταραχή Μηρυκασμού (Rumination disorder)</value>
  </data>
  <data name="schizoemotionalLbl.Text" xml:space="preserve">
    <value>Σχιζοσυναισθηματική</value>
  </data>
  <data name="schizoidLbl.Text" xml:space="preserve">
    <value>Σχιζοειδής</value>
  </data>
  <data name="schizophreniaLbl.Text" xml:space="preserve">
    <value>Σχιζοφρένεια</value>
  </data>
  <data name="schizophrenicoformLbl.Text" xml:space="preserve">
    <value>Σχιζοφρενικομορφή</value>
  </data>
  <data name="schizotypeLbl.Text" xml:space="preserve">
    <value>Σχιζότυπη</value>
  </data>
  <data name="schoolExperiencesLbl.Text" xml:space="preserve">
    <value>Σχολικές Εμπειρίες</value>
  </data>
  <data name="seasonalLbl.Text" xml:space="preserve">
    <value>Εποχική</value>
  </data>
  <data name="secondaryBenefitLbl.Text" xml:space="preserve">
    <value>Δευτερογενές Όφελος</value>
  </data>
  <data name="sendEmail1Btn.Text" xml:space="preserve">
    <value>Αποστολή</value>
  </data>
  <data name="sendEmail1Lbl.Text" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="sendEmail2Btn.Text" xml:space="preserve">
    <value>Αποστολή</value>
  </data>
  <data name="sendEmail2Lbl.Text" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="sendEmailLbl.Text" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="sendEmailToContactBtn.Text" xml:space="preserve">
    <value>Αποστολή</value>
  </data>
  <data name="sessionFrequencyLbl.Text" xml:space="preserve">
    <value>Συχνότητα Συνεδρίων</value>
  </data>
  <data name="sessionOriginLbl.Text" xml:space="preserve">
    <value>Προέλευση Συνεδρίας</value>
  </data>
  <data name="sexLbl.Text" xml:space="preserve">
    <value>Φύλλο</value>
  </data>
  <data name="sexualOrientationLbl.Text" xml:space="preserve">
    <value>Σεξουαλικός Προσανατολισμός</value>
  </data>
  <data name="shapesLbl.Text" xml:space="preserve">
    <value>Σχήματα</value>
  </data>
  <data name="shortIntermmitentFormLbl.Text" xml:space="preserve">
    <value>Βραχεία Διαλείπουσα Μορφή</value>
  </data>
  <data name="skillsLbl.Text" xml:space="preserve">
    <value>Ικανότητες/Δεξιότητες/Εφόδια </value>
  </data>
  <data name="sleepDisorderLbl.Text" xml:space="preserve">
    <value>Διαταραχές Ύπνου</value>
  </data>
  <data name="sleepInfoLbl.Text" xml:space="preserve">
    <value>Ύπνος</value>
  </data>
  <data name="sleepLbl.Text" xml:space="preserve">
    <value>Ύπνος</value>
  </data>
  <data name="socialPhobiaLbl.Text" xml:space="preserve">
    <value>Κοινωνική Φοβία</value>
  </data>
  <data name="sortPshychoticLbl.Text" xml:space="preserve">
    <value>Βραχεία Ψυχωτική</value>
  </data>
  <data name="specialRequestLbl.Text" xml:space="preserve">
    <value>Ειδικό Αίτημα</value>
  </data>
  <data name="specificFearsLbl.Text" xml:space="preserve">
    <value>Ειδικές Φοβίες</value>
  </data>
  <data name="specificTraumaHistoryLbl.Text" xml:space="preserve">
    <value>Ειδικό Τραυματικό Ιστορικό</value>
  </data>
  <data name="specilistObservationsLbl.Text" xml:space="preserve">
    <value>Παρατηρήσεις Ειδικού Επαγγελματία</value>
  </data>
  <data name="stateLbl.Text" xml:space="preserve">
    <value>Κατάσταση</value>
  </data>
  <data name="teenageExperiencesLbl.Text" xml:space="preserve">
    <value>Εφηβικές Εμπειρίες</value>
  </data>
  <data name="therapistFirstViewLbl.Text" xml:space="preserve">
    <value>Πρώτη Εικόνα Θεραπευτή</value>
  </data>
  <data name="therapistIdLbl.Text" xml:space="preserve">
    <value>Θεραπευτής</value>
  </data>
  <data name="therapyResistantLbl.Text" xml:space="preserve">
    <value>Ανθεκτική στη Θεραπεία</value>
  </data>
  <data name="thirdAgeDDL.Text" xml:space="preserve">
    <value>Στην Τρίτη Ηλικία</value>
  </data>
  <data name="thirdAgeLbl.Text" xml:space="preserve">
    <value>Στην Τρίτη Ηλικία</value>
  </data>
  <data name="tikLbl.Text" xml:space="preserve">
    <value>Tik</value>
  </data>
  <data name="traumaticHistoryLbl.Text" xml:space="preserve">
    <value>Τραυματικό Ιστορικό  </value>
  </data>
  <data name="trichotillomaniaLbl.Text" xml:space="preserve">
    <value>Τροχοτιλλομανία</value>
  </data>
  <data name="triggeringEventsLbl.Text" xml:space="preserve">
    <value>Εκλυτικά γεγονότα/Υπενθυμιτές</value>
  </data>
  <data name="userIdLbl.Text" xml:space="preserve">
    <value>Χρήστης</value>
  </data>
  <data name="visitQuestionnairiesLinkLbl.Text" xml:space="preserve">
    <value>Άνοιγμα</value>
  </data>
  <data name="voiceToneLbl.Text" xml:space="preserve">
    <value>Τόνος Φωνής</value>
  </data>
  <data name="waitingLbl.Text" xml:space="preserve">
    <value>Σε Αναμονή Λίστας</value>
  </data>
  <data name="workExperiencesLbl.Text" xml:space="preserve">
    <value>Εργασιακές Εμπειρίες</value>
  </data>
</root>