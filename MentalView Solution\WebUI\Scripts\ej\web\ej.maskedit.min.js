/*!
*  filename: ej.maskedit.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function(n,t,i){t.widget("ejMaskEdit","ej.MaskEdit",{element:null,model:null,validTags:["input"],_addToPersist:["value"],_setFirst:!1,type:"editor",angular:{require:["?ngModel","^?form","^?ngModelOptions"],requireFormatters:!0},defaults:{maskFormat:"",value:null,watermarkText:"",name:null,height:"",width:"",showError:!1,htmlAttributes:{},cssClass:"",customCharacter:null,inputMode:"text",readOnly:!1,textAlign:t.TextAlign.Left,hidePromptOnLeave:!1,showRoundedCorner:!1,enablePersistence:!1,enabled:!0,locale:"en-US",showPromptChar:!0,validationRules:null,validationMessage:null,keydown:null,keyup:null,keyPress:null,change:null,mouseover:null,mouseout:null,focusIn:null,focusOut:null,create:null,destroy:null},dataTypes:{maskFormat:"string",showError:"boolean",enabled:"boolean",customCharacter:"string",cssClass:"string",watermarkText:"string",showRoundedCorner:"boolean",showPromptChar:"boolean",inputMode:"enum",textAlign:"enum",hidePromptOnLeave:"boolean",readOnly:"boolean",validationRules:"data",validationMessage:"data",htmlAttributes:"data"},_setModel:function(n){for(var i in n)switch(i){case"value":this.temp_index=0;this.indexValue=[];t.isPlainObject(n[i])&&(n[i]=null);this._setValue(n[i]);this._initObjects();n[i]=this.get_UnstrippedValue();this._raiseEvents("change",!0);break;case"width":this._setWidth(n[i]);break;case"height":this._setHeight(n[i]);break;case"watermarkText":this.model.watermarkText=n[i];this._changeWatermark(n[i]);break;case"showRoundedCorner":this._roundedCorner(n[i]);break;case"showPromptChar":this._setShowPrompt(n[i]);break;case"validationRules":this.model.validationRules!=null&&(this._hiddenInput.rules("remove"),this.model.validationMessage=null);this.model.validationRules=n[i];this.model.validationRules!=null&&(this._initValidator(),this._setValidation());break;case"validationMessage":this.model.validationMessage=n[i];this.model.validationRules!=null&&this.model.validationMessage!=null&&(this._initValidator(),this._setValidation());break;case"textAlign":this._setTextAlign(n[i]);break;case"inputMode":this._setInputMode(n[i]);break;case"maskFormat":this.model.maskFormat=n[i];this._valueMapper();this._maskModel=this.model.maskFormat;this._setMask(n[i]);this._initObjects();n[i]=this.model.maskFormat;break;case"locale":this.model.value=this.get_StrippedValue();this.model.locale=n[i];this._setValue(this.model.value);this._initObjects();break;case"cssClass":this._setSkin(n[i]);break;case"readOnly":this._setReadOnly(n[i]);break;case"enabled":this.model.enabled=n[i];this._controlStatus(n[i]);break;case"htmlAttributes":this._addAttr(n[i]);break;case"hidePromptOnLeave":this.model.hidePromptOnLeave=n[i];this._textbox.value!=""&&(this.model.hidePromptOnLeave?(this._unStrippedMask=this._textbox.value,this._textbox.value=this.get_UnstrippedValue()):this._textbox.value=this._textbox.value.replace(/[ ]/g,"_"),this.model.value=this._textbox.value)}},observables:["value"],_destroy:function(){t.isNullOrUndefined(this._hiddenInput)||this.element.attr("name",this._hiddenInput.attr("name"));this._isWatermark&&this.element.removeAttr("placeholder");this.element.insertAfter(this.wrapper);t.isNullOrUndefined(this.wrapper)||this.wrapper.remove();this.model.textAlign&&this.element.css("text-align","");this.element.val("").removeClass(" e-mask e-input e-disable").empty();this.element.removeAttr("aria-invalid aria-disabled")},_init:function(n){if(this.indexValue=[],this.temp_index=0,this._options=n,this._tempMask=this.model.maskFormat,this._keyFlag=!1,this._keyupFlag=!0,this._checkMask=!1,this._isAndroid=/android/i.test(navigator.userAgent.toLowerCase()),this.element.is("input")&&(this.element.is("input[type=text]")||this.element.is("input[type=password]")||!this.element.attr("type")))this.element.attr("autocomplete","off"),this._isWatermark="placeholder"in document.createElement("input"),this._setValues(),this._valueMapper(),this._renderControl(),this._initObjects(),this._wireEvents(),this._addAttr(this.model.htmlAttributes),this.model.validationRules!=null&&(this._initValidator(),this._setValidation());else return this._destroy(),!1;n&&n.value!=i&&this.model.value!==n.value&&this._trigger("_change",{value:this.get_UnstrippedValue(),unmaskedValue:this.get_StrippedValue()})},_initValidator:function(){this.element.closest("form").data("validator")||this.element.closest("form").validate()},_addAttr:function(i){var r=this;n.map(i,function(n,i){var u=i.toLowerCase();u=="class"?r.wrapper.addClass(n):u=="name"?r._hiddenInput.attr(i,n):u=="disabled"&&n=="disabled"?r.disable():u=="readonly"&&n=="readOnly"?r._setReadOnly(!0):u=="style"||u=="id"?r.wrapper.attr(i,n):t.isValidAttr(r.element[0],i)?r.element.attr(i,n):r.wrapper.attr(i,n)})},_setValidation:function(){var r,f,i,u,e;this._hiddenInput.rules("add",this.model.validationRules);r=this.element.closest("form").data("validator");r=r?r:this.element.closest("form").validate();f=this._hiddenInput.attr("name");r.settings.messages[f]={};for(i in this.model.validationRules)if(u=null,!t.isNullOrUndefined(this.model.validationRules[i])){if(t.isNullOrUndefined(this.model.validationRules.messages&&this.model.validationRules.messages[i])){r.settings.messages[f][i]=n.validator.messages[i];for(e in this.model.validationMessage)i==e?u=this.model.validationMessage[i]:""}else u=this.model.validationRules.messages[i];r.settings.messages[f][i]=u!=null?u:n.validator.messages[i]}},_setShowPrompt:function(n){this.model.showPromptChar=n;this._textbox.value=this.model.showPromptChar?this._unStrippedMask:this.get_UnstrippedValue()},_controlStatus:function(n){n!=!0?this.disable():this.enable()},_setValues:function(){this._unStrippedMask=null;this._charMap={"9":"[0-9 ]",a:"[A-Za-z0-9 ]",A:"[A-Za-z0-9]",N:"[0-9]","#":"[0-9]","&":"[^]+","<":"",">":"",C:this.model.customCharacter!=null?"["+this.model.customCharacter+"]":"[A-Za-z ]","?":"[A-Za-z]"}},_renderControl:function(){if(this.model.name=this.element.attr("name")!=null?this.element.attr("name"):this.model.name!=null?this.model.name:this.element[0].id,this.element.attr("name")!=null&&this.element.removeAttr("name"),this.wrapper=t.buildTag("span.e-mask e-widget "+this.model.cssClass),this.innerWrapper=t.buildTag("span.e-in-wrap e-box"),this.wrapper.append(this.innerWrapper).insertBefore(this.element),this.innerWrapper.append(this.element),this._hiddenInput=t.buildTag("input#"+this._id+"_hidden","",{},{type:"hidden"}).insertBefore(this.element),this._isWatermark||this.model.inputMode=="password"||(this._hiddenSpan=t.buildTag("span.e-input e-placeholder ").insertAfter(this.element),this._hiddenSpan.text(this.model.watermarkText),this._hiddenSpan.css("display","none"),this._hiddenSpan.bind("mousedown",n.proxy(this._OnFocusHandler,this))),t.isNullOrUndefined(this.model.value)&&this.element[0].value!=""&&(this.model.value=this.element[0].value),t.isNullOrUndefined(this.model.value)||(this.wrapper.addClass("e-valid"),typeof this.model.value=="number"&&(this.model.value=this.model.value.toString())),this._hiddenInput.attr({name:this.model.name,value:this.model.value}),this.element.attr({"aria-invalid":!1}).addClass("e-input"),this.model.maskFormat==""&&this.model.value==null){var i=this;setTimeout(function(){i.element.val()!=""&&(i.option("value",i.element.val()),i.previousValue=i.model.value,i._initObjects())},500)}else this.element.attr({value:this.model.value});t.isNullOrUndefined(this._options.inputMode)&&!t.isNullOrUndefined(this.element.attr("type"))?this.model.inputMode=this.element.attr("type"):this._setInputMode(this.model.inputMode);this._setWidth(this.model.width);this._setHeight(this.model.height);this._roundedCorner(this.model.showRoundedCorner);this._setTextAlign(this.model.textAlign);this._setReadOnly(this.model.readOnly);this._controlStatus(this.model.enabled);this.previousValue=this.model.value},_initObjects:function(){var i,t,n;if(this._textbox=this.element[0],this._keypressFlag=0,this._selectedTextKeyDown=0,this._keydownFlag=0,this.model.maskFormat.indexOf("\\")>=0){for(i=0,t=0;t<this.model.maskFormat.length;t++)this.model.maskFormat[t]=="\\"&&(i+=1);this._maskLength=this.model.maskFormat.length-i}else this._maskLength=this.model.maskFormat.length;if(this.model.maskFormat.indexOf("\\")>=0)if(this.model.value)for(this._maskModel="",n=0;n<this.model.maskFormat.length;++n)this.model.maskFormat[n]=="\\"?this._maskModel+=this.model.maskFormat[++n]:this.model.maskFormat[n]=="\\"||(this._maskModel+=this.model.maskFormat[n].replace(/[9?CANa#&]/g,"_"));else this._maskModel=this._emptyMask;else this._maskModel=this.model.maskFormat.replace(/[9?CANa#&]/g,"_");this._changeMask(this.model.locale);(this._maskModel.indexOf("<")>=0||this._maskModel.indexOf(">")>=0)&&(this._maskModel=this._maskModel.replace(/[<>]/g,""),this.model.maskFormat=this.model.maskFormat.replace(/[<>]/g,""),this._valueMapper());this._validatedValue=this._maskModel;this.model.inputMode!="password"&&(this._setValue(this.model.value),this._setWaterMark());this.model.showError&&this.element.addClass("e-error").attr("aria-invalid","true");this._prevValue=this.model.watermarkText?this._textbox.value?this._textbox.value:this._maskModel:this._textbox.value},_setWidth:function(n){this.wrapper.width(n)},_setHeight:function(n){this.wrapper.height(n)},_roundedCorner:function(n){n&&!this.innerWrapper.hasClass("e-corner")?this.innerWrapper.addClass("e-corner"):this.innerWrapper.hasClass("e-corner")&&this.innerWrapper.removeClass("e-corner")},_setTextAlign:function(n){n=="right"?(this.element.css("text-align",""),this.wrapper.addClass("e-rtl")):(this.wrapper.removeClass("e-rtl"),this.element.css("text-align",n))},_setInputMode:function(n){this.element.attr("type",n)},_setReadOnly:function(n){this.model.readOnly=n;n?this.element.attr("readonly",!0):this.element.removeAttr("readonly")},_setSkin:function(n){this.wrapper.removeClass(this.model.cssClass);this.wrapper.addClass(n)},_setWaterMark:function(){var i=this.model.showPromptChar?this._maskModel:this._maskModel!=""&&this._maskModel!=null?this._maskModel.replace(/[_]/g," "):this._maskModel;this._maskModel=this.model.maskFormat.indexOf("\\")>=0?this.model.maskFormat===""?null:i:n.trim(this.model.maskFormat.replace(/[9?CANa#&]/g,"_"))===""?null:this.model.maskFormat.replace(/[9?CANa#&]/g,"_");(this._maskModel!=""&&this._maskModel!=null&&this._textbox.value==i||i==""&&this._textbox.value==""&&this.model.inputMode!="password")&&(this._textbox.value="");this.model.watermarkText.length!=0||t.isNullOrUndefined(this.element.attr("placeholder"))||(this.model.watermarkText=this.element.attr("placeholder"));this.model.watermarkText&&(this._isWatermark?this.element.attr("placeholder",this.model.watermarkText):this._textbox.value?this._hiddenSpan.css("display","none").text(this.model.watermarkText):this._hiddenSpan.css("display","block").text(this.model.watermarkText));this.model.watermarkText||this._textbox.value||!this.model.maskFormat||(this.model.hidePromptOnLeave?(this._unStrippedMask=this._maskModel,this._textbox.value=this.get_UnstrippedValue()):this._textbox.value=i)},_changeWatermark:function(t){if(!this.model.enabled)return!1;var i=this.model.showPromptChar?this._maskModel:this._maskModel!=""&&this._maskModel!=null?this._maskModel.replace(/[_]/g," "):this._maskModel;(this._textbox.value==i||this._textbox.value==""||n.trim(this.get_StrippedValue())=="")&&this.model.inputMode!="password"&&(this._textbox.value="");this._isWatermark?this.element.attr("placeholder",t):this._hiddenSpan.text(t);!this.model.watermarkText&&n.trim(this.get_StrippedValue())==""&&this.model.maskFormat&&(this.model.hidePromptOnLeave?(this._unStrippedMask=this._maskModel,this._textbox.value=this.get_UnstrippedValue()):this._textbox.value=i)},_showAlert:function(){var t=this;this.element.addClass("e-error").attr("aria-invalid","true").animate({Opacity:1},700,null,function(){n(t._textbox).removeClass("e-error").attr("aria-invalid","false")})},_unMask:function(){for(var s,y,l,h="",a=0,n=0,u=0,e,v="_",c,t=this.model.value.toString(),o="",f=this._rules.slice(),r=0;r<this._rules.length;r++){for(s=0;s<t.length;s++)if(f[r].rule&&t[s].match(f[r].rule)||f[r].rule===i){o+=f[r].rule?t.charAt(s):f[r];t=f[r].rule?t.slice(s+1,t.length):f[r]===t[s]?t.slice(s+1,t.length):t;a=1;break}a===0&&t.length>0&&(o+=f[r].rule===i?f[r]:"");a=0}while(n<this._rules.length){if(e=o[u],c=this._rules[n],e==i)break;if(e===c||e===v?(h+=e===v?v:e,u+=1,n+=1):c.rule===i?(h+=c,n+=1):this._rules[n].rule!=i&&e.match(this._rules[n].rule)?(l=o.charCodeAt(u),y=n,(this._rules["0"]===">"||this._rules["0"]==="<")&&(y=n-1),this._validateChars(l,y)?(h+=o.charAt(u),n++,u++):n++):typeof c!="string"?e.match(this._rules[n].rule)?(l=o.charCodeAt(u),this._validateChars(l,n)&&(h+=o.charAt(u),n++,u++)):(u++,n++):n++,u>o.length)break}return h},_validateValue:function(){var d,a,y,s,e,k,p,h,u,f,w;if(!t.isNullOrUndefined(this.model.value)){var c=this.model.value,r=this._maskModel,n=f=0,o,v="_",b,l=this._unMask();if(this._isWatermark||this.model.inputMode=="password"||this._hiddenSpan.css("display","none"),this._maskLength==0)return this._textbox.value=c,this._setWaterMark(),!0;for(d=this.model.value.toString(),c=this.model.maskFormat.indexOf("\\")>=0||this.model.customCharacter!=null?this.model.value:this.model.value=d;n<this._rules.length;){if(o=l[f],o==="_"&&this._rules[n].rule==="[^]+"&&(this.indexValue[this.temp_index]=n,this.temp_index++),b=this._rules[n],o==i)break;o===b||o===v?(o===v?v:"",s=r.substring(0,n),e=r.substring(n),r=s+o+e.substr(1,e.length),f+=1,n+=1):this._rules[n].rule!=i?(y=l.charCodeAt(f),a=n,(this._rules["0"]===">"||this._rules["0"]==="<")&&(a=n-1),this._validateChars(y,a)?(s=r.substring(0,a),e=r.substring(a),r=s+l.charAt(f)+e.substr(1,e.length),n++,f++):n++):typeof b!="string"?o.match(this._rules[n].rule)?(y=l.charCodeAt(f),this._validateChars(y,n)?(s=r.substring(0,n),e=r.substring(n),r=s+l.charAt(f)+e.substr(1,e.length),n++,f++):n++):(s=r.substring(0,n),e=r.substring(n),r=s+v+e.substr(1,e.length),f++,n++):n++}if(this.model.value)if(this._textbox.value=r,this.model.value=this.get_UnstrippedValue(),this.model.value===null&&(this.model.value=r),this.model.maskFormat.indexOf("\\")>=0){for(this._textbox.value="",k=0,p=0;p<this.model.maskFormat.length;p++)this.model.maskFormat[p]=="\\"&&(k+=1);if(h=0,this.model.value.length==r.length-k)for(u=0;u<this.model.maskFormat.length;++u)this.model.maskFormat[u]=="\\"?h=h+1:this.model.maskFormat[u-1]=="\\"?this._textbox.value+=c[u-h]:this.model.maskFormat[u]=="\\"||(this._textbox.value+=c.length<=u-h?r[u].replace(/[9?CANa#&]/g,"_"):c[u-h].replace(/[9?CANa#&]/g,"_"));else this._textbox.value=r}else this.model.hidePromptOnLeave?(this._unStrippedMask=r.replace(/[ ]/g,"_"),this._textbox.value=r):this._textbox.value=r;else if(this.model.maskFormat.indexOf("\\")>=0)for(u=0;u<this.model.maskFormat.length;++u)this.model.maskFormat[u-1]=="\\"?this._textbox.value+=this.model.maskFormat[u]:this.model.maskFormat[u]=="\\"||(this._textbox.value+=this.model.maskFormat[u].replace(/[9?CANa#&]/g,"_"));else this._textbox.value=this.model.maskFormat.replace(/[9?CANa#&]/g,"_");if(this._textbox.value!=i&&this.model.hidePromptOnLeave&&(this._unStrippedMask=this._textbox.value.replace(/[ ]/g,"_"),this._textbox.value=this.get_UnstrippedValue()),this._maskModel!=null&&(this._textbox.value.indexOf(">")>=0||this._textbox.value.indexOf("<")>=0)&&(this._textbox.value=this._textbox.value.replace(/[<>]/g,""),this._tempMask=this.model.maskFormat,this.model.maskFormat=this.model.maskFormat.replace(/[<>]/g,"")),this.model.showPromptChar||(this._textbox.value=this._getUnstrippedValue(!0)),this._tempMask!=null)for(f=0;f<this._textbox.value.length;f++)w=this._updateCasing(this._textbox.value[f],f),t.isNullOrUndefined(w)||(this._textbox.value=this._textbox.value.substring(0,f)+w+this._textbox.value.substring(f+w.length));this._setWaterMark()}},_selectionText:function(n,i){var r=t.isNullOrUndefined(this._maskModel)?"":this._maskModel.substring(n,i);return(this._textbox.value=this._textbox.value.substring(0,n)+r+this._textbox.value.substring(i),this._keydownFlag==1)?(this._setCaretPosition(n),n):this._keydownFlag==2?(this._setCaretPosition(i),i):n},_caretPosition:function(n){var t=0,i,r;return document.selection?(i=document.selection.createRange(),r=i.text.length,i.moveStart("character",-n.value.length),t=i.text.length-r,r!=0&&(t=this._selectionText(t,i.text.length),this._selectedTextKeyDown=1)):(n.selectionStart||n.selectionStart=="0")&&(t=n.selectionStart,n.selectionStart!=n.selectionEnd&&(this._keydownFlag&&(t=this._selectionText(n.selectionStart,n.selectionEnd)),this._selectedTextKeyDown=1)),t},_setCaretPosition:function(n){var t=this._textbox,i;window.navigator.appName=="Microsoft Internet Explorer"?t.createTextRange&&(i=t.createTextRange(),i.move("character",n),i.select()):t.selectionStart||t.selectionStart=="0"?(t.focus(),this._isAndroid?setTimeout(function(){t.setSelectionRange(n,n)},0):t.setSelectionRange(n,n)):t.focus()},_validateChars:function(t,i){var s=this._charMap,u=!1,e,f,r,o;return this.model.maskFormat.indexOf("\\")>=0?(e=this._getCunrrentPos(i),f=this.model.maskFormat.substr(i+e,1)):f=this.model.maskFormat.substr(i,1),r=this.model.customCharacter,o=String.fromCharCode(t),n.each(s,function(n,t){f==n&&(r!=null&&(n=="C"?t="["+r+"]":(n=="A"||n=="N"||n=="#")&&(t=t.replace("]","")+r+"]")),u=o.match(new RegExp(t))?!0:!1)}),u},_seekNext:function(n,t){for(var f=this._caretPosition(this._textbox),i=n?t?f-t-1:f-1:f,e=i,o=!0,r,u;o;){if(i>=0&&i<this._maskLength){for(r=0,u=0;u<=i;u++)this.model.maskFormat[u]=="\\"&&(r+=1);if(this.model.maskFormat.charAt(i)!="C"&&(this.model.maskFormat.indexOf("\\")>=0?this.model.maskFormat.charAt(i+r)!="\\"&&this.model.maskFormat.charAt(i+r-1)!="\\"?this._charMap[this.model.maskFormat.charAt(i+r)]||i++:this.model.maskFormat.charAt(i+r)!="\\"&&this.model.maskFormat.charAt(i+r-1)=="\\"&&++i:this._charMap[this.model.maskFormat.charAt(i)]||i++,i!=e)){e=i;continue}}o=!1}return i},_seekBefore:function(n){var f=this._caretPosition(this._textbox),t=n?f+1:f,e=--t,u=!0,i,r;for(this._selectedTextKeyDown==1&&(u=!1);u;){if(t>=0&&t<this._maskLength){for(i=0,r=0;r<=t;r++)this.model.maskFormat[r]=="\\"&&(i+=1);if(this.model.maskFormat.charAt(t)!="C"&&(this.model.maskFormat.indexOf("\\")>=0?this.model.maskFormat.charAt(t+i)!="\\"&&this.model.maskFormat.charAt(t+i-1)!="\\"?this._charMap[this.model.maskFormat.charAt(t+i)]||this._setCaretPosition(--t):this.model.maskFormat.charAt(t+i-1)=="\\"&&this._setCaretPosition(--t+i):this._charMap[this.model.maskFormat.charAt(t)]||this._setCaretPosition(--t)),t!=e){e=t;continue}}u=!1}return t},_writeBuffer:function(n,t,r){var e,s,o,f,h,u;if(t<=this._maskLength){if(e=this._textbox.value,this.indexValue!=null&&r.keyCode!="95")for(u=0;u<this.indexValue.length;u++)this.indexValue[u]==t&&(n!="_"||r.keyCode=="8")&&(this.indexValue[u]="");if(r.keyCode=="95"&&(this.indexValue[this.temp_index]=t,this.temp_index++),this._tempMask!=null&&(s=this._updateCasing(n,t)),n=s==i?n:s,o=e.substring(0,t),f=e.substring(t),this.model.maskFormat.indexOf("\\")>=0){for(h=0,u=0;u<=t;u++)this.model.maskFormat[u].indexOf("\\")>=0&&t!=0&&(h+=1);this._textbox.value=this.model.maskFormat[t+h].indexOf("\\")>=0?o+e[t]+f.substr(1,f.length):o+n+f.substr(1,f.length)}else this._textbox.value=o+n+f.substr(1,f.length);this._setCaretPosition(t+1)}},_updateCasing:function(n,t){for(var r,i=0;i<t+1;i++)(this._tempMask.substr(i,1)=="<"||this._tempMask.substr(i,1)==">")&&t++;for(r=t;r>-1;r--){if(this._tempMask.substr(r,1)=="<")return n.toLowerCase();if(this._tempMask.substr(r,1)==">")return n.toUpperCase()}},_getStrippedValue:function(t){var f,s=this.model.maskFormat,e=t?"":null,o,r,i,u;if(f=this._textbox.value==""&&this.model.inputMode!="password"?this._maskModel:this._textbox.value,s.length==0)return f;for(o=0,r=0;r<s.length;r++)e=t?e:n.trim(e),this.model.maskFormat.indexOf("\\")>=0?this.model.maskFormat[r]=="\\"?o+=1:this.model.maskFormat[r-1]=="\\"?e+=this._textbox.value[r-o]:(i=s[r],u=null,"9?$a*".indexOf(i)!=-1?u=this._charMap[i]:i=="A"||i=="N"||i=="#"?u=this._charMap[i].replace("]","")+this.model.customCharacter+"]":i=="C"?u="["+this.model.customCharacter+"]":i=="&"&&(e+=f[r-o]),u&&f[r-o]&&f[r-o].match(new RegExp(u))&&(e+=f[r-o])):(i=s[r],u=null,"9?$a*".indexOf(i)!=-1?u=this._charMap[i]:i=="A"||i=="N"||i=="#"?u=this._charMap[i].replace("]","")+this.model.customCharacter+"]":i=="C"?u="["+this.model.customCharacter+"]":i=="&"&&(e+=f[r-o]),u&&f[r]&&f[r].match(new RegExp(u))&&(e+=f[r]));return e},_getUnstrippedValue:function(t){var u=0,f=this._textbox.value==""&&this.model.inputMode!="password"?this._maskModel:this._textbox.value,r=null;if(this.model.maskFormat.length==0)return f;if(f!=i&&(r=n.trim(f.replace(/[_]/g," "))==""?null:f.replace(/[_]/g," ")),this.model.customCharacter=="_"&&r==null&&(r=""),r==null&&this.indexValue[u]!=null&&(r=""),f!=i&&this.indexValue[u]!=null&&r!=null)for(;u<this.indexValue.length;u++)(this.indexValue[u]!=""||this.indexValue[u]=="0")&&(r=r.substr(0,this.indexValue[u])+"_"+r.substr(this.indexValue[u]+1));return r==null&&t?"":r},get_StrippedValue:function(){return this._getStrippedValue()},get_UnstrippedValue:function(){return this._getUnstrippedValue()},_setValue:function(i){(t.isNullOrUndefined(i)||n.trim(i)=="")&&(i=null);this.model.value=i;this._isWatermark||this.model.inputMode=="password"||this._hiddenSpan.css("display","none");t.isNullOrUndefined(this.model.maskFormat)||this.model.maskFormat==""?(this._textbox.value=t.isNullOrUndefined(i)?"":i,this._unStrippedMask=this._textbox.value):(this._validateValue(),t.isNullOrUndefined(this.model.value)&&(this._textbox.value=t.isNullOrUndefined(i)?"":i,this._unStrippedMask=this._maskModel));this._setWaterMark();this._prevValue=this._textbox.value;this._prevPosition=this.element[0].selectionStart},_valueMapper:function(){for(var f=[],r=0,o=this.model.maskFormat||"",s=o.split(""),t=0,n,h=0,u="",e,c=this._charMap,i;t<o.length;t++)if(n=s[t],e=c[n],e)f[r]={rule:e},u+="_",r+=1;else for(n==="\\"&&(h=t+1,t++,n=s[h]),n=n.split(""),i=0;i<n.length;i++)f[r]=n[i],u+=n[i],r+=1;this._rules=f;this._emptyMask=u;this._maskLength=u.length},_changeMask:function(n){var r,f,e,o,u,i;if(this._maskModel.length!=0){for(r=t.preferredCulture(n),u="",f=r.numberFormat[","],e=r.numberFormat.currency.symbol,o=r.numberFormat["."],i=0;i<this._maskModel.length;i++)u+=this._maskModel[i]==","?f:this._maskModel[i]=="."?o:this._maskModel[i]=="$"?e:this._maskModel[i];this._maskModel=u}},_setMask:function(i){if(this._maskLength=i.length,this.model.maskFormat=i,this._tempMask=this.model.maskFormat,this._maskModel=i.replace(/[9?CANa]/g,"_"),(this._maskModel.indexOf("<")>=0||this._maskModel.indexOf(">")>=0)&&(this._maskModel=this._maskModel.replace(/[<>]/g,""),this.model.maskFormat=this.model.maskFormat.replace(/[<>]/g,"")),t.isNullOrUndefined(this.model.maskFormat)||this.model.maskFormat==""){if(!this.model.watermarkText&&this._textbox.value!=this._maskModel)if(this.model.maskFormat)this.model.hidePromptOnLeave?(this._textbox.value=this._maskModel,this._unStrippedMask=this._textbox.value,this._textbox.value=this.get_UnstrippedValue()):this._textbox.value=this._maskModel;else{var r=this._unStrippedMask.replace(/[_]/g," ");this._textbox.value=this.model.customCharacter==null?n.trim(r.replace(/[\(\)-]/g,"")):n.trim(r);this._unStrippedMask=this._textbox.value}}else this._validateValue()},enable:function(){this.element.disabled=!1;this.element.removeAttr("disabled").removeClass("e-disable").attr({"aria-disabled":!1});this.wrapper.find(".e-placeholder").length>0&&this.wrapper.find(".e-placeholder").removeAttr("disabled","disabled");this.wrapper.removeClass("e-disable-wrap");this.model.enabled=!0},disable:function(){this.element.disabled=!0;this.element.attr("disabled","disabled").addClass("e-disable").attr({"aria-disabled":!0});this.wrapper.find(".e-placeholder").length>0&&this.wrapper.find(".e-placeholder").attr("disabled","disabled");this.wrapper.addClass("e-disable-wrap");this.model.enabled=!1},clear:function(){this._textbox.value=this.model.maskFormat.replace(/[9?aCAN]/g,"_");this.model.value=this.get_StrippedValue()},_wireEvents:function(){this._on(this.element,"focus",this._OnFocusHandler);this._on(this.element,"blur",this._OnBlurHandler);this._on(this.element,"keydown",this._OnKeyDownHandler);this._on(this.element,"input",this._OnInputHandler);this._on(this.element,"keypress",this._OnKeyPressHandler);this._on(this.element,"keyup",this._OnKeyUpHandler);this._on(this.element,"mouseover",this._OnMouseOverHandler);this._on(this.element,"mouseout",this._OnMouseOutHandler);this._on(this.element,"paste",this._OnPasteHandler);this._on(this.element,"cut",this._OnCutHandler)},_OnCutHandler:function(){var i=t.isNullOrUndefined(this._maskModel)?"":this._maskModel.substring(this._textbox.selectionStart,this._textbox.selectionEnd),r=this._textbox.value.substring(0,this._textbox.selectionStart),u=this._textbox.value.substring(this._textbox.selectionEnd),f=this._textbox.selectionStart,n=this;setTimeout(function(){n._textbox.value=r+i+u;n._setCaretPosition(f);n._prevValue=n._textbox.value;n._prevPosition=n.element[0].selectionStart;n._raiseEvents("change")},0)},_OnPasteHandler:function(){var t=this;return this._keyFlag=!0,setTimeout(function(){var i=n(t._textbox).val();t._setValue(i);t._raiseEvents("change")},0),!0},_OnFocusHandler:function(){var i,r;(this.wrapper.addClass("e-focus"),i=this.model.showPromptChar?this._maskModel:this._maskModel!=""&&this._maskModel!=null?this._maskModel.replace(/[_]/g," "):this._maskModel,this.model.readOnly)||(this._focusValue=this.model.value,this._isWatermark||this.model.inputMode=="password"||this._hiddenSpan.css("display","none"),this._textbox.value==""&&this._maskModel!=""&&this.model.inputMode!="password"&&(this._textbox.value=this.model.maskFormat.indexOf("\\")>=0?i:t.isNullOrUndefined(this._maskModel)?"":i),this._maskModel!=null&&(this._textbox.value.indexOf("<")>=0||this._textbox.value.indexOf(">")>=0)&&(this._textbox.value=this._textbox.value.replace(/[<>]/g,""),this._maskModel=this._textbox.value,this._tempMask=this.model.maskFormat,this.model.maskFormat=this.model.maskFormat.replace(/[<>]/g,"")),this._textbox.value!=i&&this._unStrippedMask!=null&&this.model.hidePromptOnLeave&&(this._textbox.value=this._unStrippedMask),this.model.showPromptChar||(this._textbox.value=this._getUnstrippedValue(!0)),n.fn.selectRange=function(n,t){return this.each(function(){if(this.setSelectionRange)this.focus(),this.setSelectionRange(n,t);else if(this.createTextRange){var i=this.createTextRange();i.collapse(!0);i.moveEnd("character",t);i.moveStart("character",n);i.select()}})},this.model.maskFormat&&n(this.element).selectRange(0,0),r=this,setTimeout(function(){r._prevPosition=r.element[0].selectionStart},0),this._raiseEvents("focusIn"))},_OnBlurHandler:function(){var f,r,n,u;if(this.wrapper.removeClass("e-focus"),this.model.value=this.get_StrippedValue(),f=[],r=[],(this._textbox.value.indexOf("_")!=-1||this._textbox.value.indexOf(" ")!=-1)&&!t.isNullOrUndefined(this.model.value)){for(n=0;n<this.model.maskFormat.length;n++)this.model.maskFormat[n]=="A"||this.model.maskFormat[n]=="?"?f.push(n):(this.model.maskFormat[n]=="9"||this.model.maskFormat[n]=="a"||this.model.maskFormat[n]=="C")&&r.push(n);for(n=0;n<f.length;n++)this._textbox.value[f[n]]==="_"&&(this._showAlert(),this.model.value=this._textbox.value);for(n=0;n<r.length;n++)if(this.model.value[r[n]]=="_"||this.model.value[r[n]]==" ")for(this.model.value=this.model.value.substr(0,r[n])+this.model.value.substr(r[n]+1,this.model.value.length),u=n;u<r.length;u++)r[u]=r[u]-1}this.model.value=this.model.value!=null?this.model.value.replace(/\s+/g,""):this.model.value;this._textbox.value!=i&&(this.model.watermarkText==""||this.model.watermarkText!=""&&this.model.value!="")&&(this.model.hidePromptOnLeave?(this._unStrippedMask=this._textbox.value,this._textbox.value=this.get_UnstrippedValue()):this._unStrippedMask=this._textbox.value);this.model.inputMode!="password"&&this._setWaterMark();this._raiseEvents("change");this._raiseEvents("focusOut")},_OnKeyDownHandler:function(n){var e,o,r,u,i,h;if(!this.model.readOnly){if(this._keyFlag=!0,this._checkMask&&(r=this.element[0].selectionStart,e=this._getStrippedValue(!0),this._setValue(e),this._setCaretPosition(r),this._checkMask=!1),this._keypressFlag=0,this._keyupFlag=!0,u=t.browserInfo().name,o=/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream,t.isDevice()&&t.isTouchDevice()&&!o&&(n.keyCode==229||u=="mozilla"&&n.keyCode==0&&n.charCode==0||u=="edge"||u=="msie")&&(this._keyupFlag=!1),this._raiseEvents("onKeyDown",null,{keyCode:n.keyCode,altKey:n.altKey,shiftKey:n.shiftKey,ctrlKey:n.ctrlKey,originalArgs:n}),this._raiseEvents("keyDown",null,{keyCode:n.keyCode,altKey:n.altKey,shiftKey:n.shiftKey,ctrlKey:n.ctrlKey,originalArgs:n}),this.element.removeClass("error"),this._maskLength==0)return!0;var f=n.keyCode?n.keyCode:n.charCode,c=String.fromCharCode(f),i=this._seekNext(),s=this.model.showPromptChar?"_":" ",u=t.browserInfo().name;if(u=="msie"&&this._textbox.value==""){if(this.model.maskFormat.indexOf("\\")>=0)for(r=0;r<this.model.maskFormat.length;++r)this.model.maskFormat[r-1]=="\\"?this._textbox.value+=this.model.maskFormat[r]:this.model.maskFormat[r]=="\\"||(this._textbox.value+=this.model.maskFormat[r].replace(/[9?CANa#&]/g,"_"));else this._textbox.value=this.model.maskFormat.replace(/[9?CANa#&]/g,"_");this._setCaretPosition(i)}if(f>=35&&f<=41&&(window.navigator.appCodeName=="Mozilla"||window.navigator.appCodeName=="opera")&&(this._keypressFlag=1),n.shiftKey&&(n.keyCode==35||n.keyCode==36||n.keyCode==37||n.keyCode==39||n.keyCode==46||n.keyCode==127)||n.ctrlKey&&(n.keyCode==86||n.keyCode==65||n.keyCode==67||n.keyCode==88)){this._keypressFlag=1;return}return f==8?this.model.inputMode!="password"?(this._keydownFlag=1,i=this._seekBefore(),this._selectedTextKeyDown?!this.model.showPromptChar&&this._selectedTextKeyDown&&(n.keyCode==8&&i=="-1"&&this.indexValue!=null&&(this.indexValue=[],this.temp_index=0),this._textbox.value=this.get_UnstrippedValue(),this._setCaretPosition(i+1)):(i>=0&&i<this._maskLength&&this._writeBuffer(s,i,n),i<0&&(i=0),this._setCaretPosition(i)),this._keydownFlag=0,this._selectedTextKeyDown=0,n.keyCode==8&&i=="-1"&&this.indexValue!=null&&(this.indexValue=[],this.temp_index=0),n.preventDefault(),this._prevValue=this._textbox.value,this._prevPosition=this.element[0].selectionStart,this._keyFlag=!1,!1):(this._keypressFlag=1,!0):n.keyCode==46||n.keyCode==127?this.model.inputMode!="password"?(this._keydownFlag=2,i=this._seekNext(),this._selectedTextKeyDown?!this.model.showPromptChar&&this._selectedTextKeyDown&&(this._textbox.value=this.get_UnstrippedValue(),this._setCaretPosition(i)):i>=0&&i<this._maskLength&&(h=this._getCunrrentPos(i),this.model.maskFormat[i+h]=="&"||this._writeBuffer(s,i,n)),this._keydownFlag=0,this._selectedTextKeyDown=0,n.preventDefault(),!1):(this._keypressFlag=1,!0):void 0}},_ErrorHandler:function(n){var t=this.model.showPromptChar?"_":" ";this._textbox.value==""&&(this._textbox.value=this.model.maskFormat.replace(/[9?$CANa*]/g,t));this._setCaretPosition(n);this._showAlert()},_getCunrrentPos:function(n){for(var t=0,i=0;i<=n+t;i++)this.model.maskFormat[i]=="\\"&&(t+=1);return t},_OnKeyPressHandler:function(n){var f;if(!this.model.readOnly){if(this._keyFlag=!1,this._raiseEvents("keyPress",null,{keyCode:n.keyCode,altKey:n.altKey,shiftKey:n.shiftKey,ctrlKey:n.ctrlKey,originalArgs:n}),this._maskLength==0)return!0;var r=n.keyCode?n.keyCode:n.charCode,e=String.fromCharCode(r),o=this.model.showPromptChar?"_":" ",i=this._seekNext(),u=t.browserInfo().name,s=/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream;if(t.isDevice()&&t.isTouchDevice()&&!s&&(u=="edge"||u=="msie"))return!0;if(this._validateChars(r,i)?((u=="mozilla"||u=="opera")&&(n.keyCode==35||n.keyCode==36||n.keyCode==37||n.keyCode==39)||(u=="mozilla"||u=="opera")&&n.ctrlKey&&(r==97||r==99||r==118||r==120)||(this._textbox.value==""&&(this._textbox.value=this.model.maskFormat.replace(/[9?$CANa&*]/g,o)),this.model.maskFormat.indexOf("\\")>=0?i<this._maskLength&&this._writeBuffer(e,i,n):this._writeBuffer(e,i,n)),this._prevValue=this._textbox.value):(u=="mozilla"||u=="opera")&&(n.keyCode==35||n.keyCode==36||n.keyCode==37||n.keyCode==39)||(u=="mozilla"||u=="opera")&&n.ctrlKey&&(r==97||r==99||r==118||r==120)||(this.model.maskFormat.indexOf("&")>=0||this.model.maskFormat.indexOf("\\")>=0?(f=this._getCunrrentPos(i),this.model.maskFormat[i+f]=="&"&&r==127||(this.model.maskFormat.indexOf("\\")>=0?i<this._maskLength&&(this._validateChars(r,i+f)||this.model.maskFormat[i+f-1]=="\\"?this._writeBuffer(e,i,n):this._ErrorHandler(i)):this._validateChars(r,i+f)?this._writeBuffer(e,i,n):this._ErrorHandler(i))):this._ErrorHandler(i)),!this._keypressFlag&&r!=9)return this._keypressFlag=0,n.preventDefault(),!1;this._keypressFlag=0}},_OnInputHandler:function(n){var c=t.browserInfo().name,l=/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream&&!this._keyFlag,y=this._keyupFlag&&(c=="edge"||c=="msie"),a,s,i,u,f;if(this._maskLength==0)return!0;if(t.isDevice()&&t.isTouchDevice()&&(l||y)){var h=this._textbox.value.length-this._prevValue.length,i=h==1?this._seekNext(!0):this._seekNext(!0,h-1),r,e=this._textbox.value[this.element[0].selectionStart-1];r=e.charCodeAt(0);this._validateChars(r,i)?(this._textbox.value=this._prevValue,u=String.fromCharCode(r),this._writeBuffer(u,i,n)):(this._textbox.value=this._prevValue,this._setCaretPosition(i),this._ErrorHandler(i));this._prevValue=this._textbox.value;this._prevPosition=this.element[0].selectionStart;this._checkMask=!0}if(t.isDevice()&&t.isTouchDevice()&&!l&&!this._keyupFlag){a=this.element[0].selectionStart;s=this.element[0].selectionEnd;a!=s&&this._textbox.setSelectionRange(s,s);var p=this.model.showPromptChar?"_":" ",h=this._textbox.value.length-this._prevValue.length,v=h<=0?!0:!1,o=v?this._seekBefore(!0):this._seekNext(!0),r,e=this._textbox.value[this.element[0].selectionStart-1],a=0;if(this.element[0].selectionStart>0&&(r=e.charCodeAt(0)),this._tempMask.length>this._textbox.value.length)t.isNullOrUndefined(e)&&this._emptyMask.slice(1,this._emptyMask.length)==this._textbox.value?this._textbox.value=this._emptyMask:v?(this._textbox.value=this._prevValue,this._writeBuffer(p,o,n)):(i=o,this._validateChars(r,i)?(this._textbox.value=this._prevValue,u=String.fromCharCode(r),this._writeBuffer(u,i,n)):(this._textbox.value=this._prevValue,this._setCaretPosition(i),this._ErrorHandler(i))),this._setCaretPosition(o);else if(i=o,this._validateChars(r,i))for(this._textbox.value=this._prevValue,u=String.fromCharCode(r),this._writeBuffer(u,i,n),f=0;f<this._emptyMask.length;f++)this._emptyMask[f]==" "&&this._textbox.value[f]!=" "&&e==this._textbox.value[f]&&(this._textbox.value=this._textbox.value.substring(0,startPos-1)+" "+e+this._textbox.value.substring(endPos,this._textbox.value.length),this._setCaretPosition(o+1));else this._textbox.value=this._prevValue,this._setCaretPosition(i),this._ErrorHandler(i);this._prevValue=this._textbox.value;this._prevPosition=this.element[0].selectionStart;this._keyupFlag=!0}this._keyFlag=!1},_OnKeyUpHandler:function(n){this._maskLength==0&&this._raiseEvents("change");this._raiseEvents("keyUp",null,{keyCode:n.keyCode,altKey:n.altKey,shiftKey:n.shiftKey,ctrlKey:n.ctrlKey,originalArgs:n});t.isNullOrUndefined(this.model.validationRules)||this._hiddenInput.valid()},_OnMouseOverHandler:function(){this._raiseEvents("mouseOver")},_OnMouseOutHandler:function(){this._raiseEvents("mouseOut")},_raiseEvents:function(i,r,u){var f,o=this.get_StrippedValue(),e=this.get_UnstrippedValue();if(this.model.value=e!==null?this.model.customCharacter==null&&n.trim(e.replace(/[\(\)-]/g,""))==""?null:e:null,i=="change")if(this.previousValue!=this.model.value)this.previousValue=this.model.value;else if(this._focusValue==this.model.value)return!1;f={value:e,unmaskedValue:o};i=="change"&&(f.isInteraction=!r);i=="change"&&(this._trigger("_change",f),t.isNullOrUndefined(this.model.value)?this.wrapper.removeClass("e-valid"):this.wrapper.addClass("e-valid"));u&&n.extend(!0,f,u);this._hiddenInput.val(o);this._trigger(i,f)},_OnValueChange:function(){this._textbox.value==""&&this._maskModel!=""&&(this._textbox.value=this._maskModel);this._setValue(this._textbox.value)}});t.InputMode={Password:"password",Text:"text"}})(jQuery,Syncfusion)});
