/*!
*  filename: ej.pivotschemadesigner.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min","./../common/ej.touch.min","./../common/ej.draggable.min","./../common/ej.scroller.min","./ej.menu.min","./ej.togglebutton.min","./ej.button.min","./ej.checkbox.min","./ej.dialog.min","./ej.maskedit.min","./ej.waitingpopup.min","./ej.treeview.min","./../datavisualization/ej.chart.min"],n):n()})(function(){(function(n,t,r){t.widget("ejPivotSchemaDesigner","ej.PivotSchemaDesigner",{_rootCSS:"e-pivotschemadesigner",element:null,model:null,_requiresID:!0,validTags:["div","span"],defaults:{url:"",cssClass:"",height:"630px",width:"415px",locale:"en-US",layout:"excel",enableRTL:!1,enableXHRCredentials:!1,pivotControl:null,pivotTableFields:[],pivotCalculations:[],pivotColumns:[],pivotRows:[],filters:[],olap:{showKpi:!1,showNamedSets:!1},enableMemberEditorSorting:!1,enableCheckStateOnSearch:!0,enableWrapper:!1,enableDragDrop:!0,serviceMethods:{fetchMembers:"FetchMembers",nodeStateModified:"NodeStateModified",nodeDropped:"NodeDropped",removeButton:"RemoveButton",memberExpand:"MemberExpanded",filtering:"filtering",sorting:"sorting"},customObject:{},beforeServiceInvoke:null,afterServiceInvoke:null,dragMove:null,applyFieldCaption:null},dataTypes:{serviceMethods:"data",customObject:"data",pivotControl:"data",pivotTableFieldList:"array",pivotCalculationList:"array",pivotColumnList:"array",pivotRowList:"array",filterList:"array"},locale:t.util.valueFunction("locale"),_init:function(){this._initPrivateProperties();this._load()},_destroy:function(){this.element.empty().removeClass("e-pivotschemadesigner"+this.model.cssClass).removeAttr("tabindex");this._waitingPopup!=r&&this._waitingPopup.destroy();this.element.attr("class")==""&&this.element.removeAttr("class")},_initPrivateProperties:function(){this._id=this.element.attr("id");this._dialogTitle="";this._currentMembers=[];this._memberTreeObj=null;this._tableTreeObj=null;this._dialogOKBtnObj=null;this._curFilteredText="";this._curFilteredAxis="";this._tempFilterData=null;this._droppedClass="";this._selectedTreeNode=null;this._selectedMember="";this._selectedLevel="";this._isDragging=!1;this._dataModel="";this._droppedPosition="";this._currentCubeName="";this._errorDialog="";this._nodeDropedParams="";this._contextMenuObj=null;this._removeButtonDeferUpdate=!1;this._isMeasureBtnRemove=!1;this._nodeCheck=!1;this._isFiltered=!1;this._curFocus={tree:null,node:null,tab:null,button:null};this._index={tree:0,node:0,tab:0,button:0};this._selectedFieldName="";this._selectedFieldCaption="";this._selectedFieldAxis="";this._isDropAction=!1;this._ascdes="";this._sortType="";this._pivotClientObj=null;this._memberPageSettings={currentMemeberPage:1,startPage:0,endPage:0};this._memberSearchPageSettings={currentMemberSearchPage:1,startSearchPage:0,endSearchPage:0};this._memberDrillPageSettings={currentMemberDrillPage:1,startDrillPage:0,endDrillPage:0};this._editorDrillTreeData={};this._editorDrillTreePageSettings={};this._lastSavedTree=[];this._isEditorDrillPaging=!1;this._isEditorCollapseDrillPaging=!1;this._isSearchApplied=!1;this._isAllMemberChecked=!0;this._editorTreeData=[];this._editorSearchTreeData=[];this._memberPagingAvdData=[];this._onDemandNodeExpand=!0;this._isOptionSearch=!1;this._isSelectSearchFilter=!0;this._currentFilterList={};this._repCollection=[];this._currentCheckedNode=null;this._isFilterBtnClick=!1;this._parentNodeCollection={};this._parentSearchNodeCollection={}},_load:function(){var f="",i,s,h,k,e,o,u;if(this.model.pivotControl!=null&&(this.model.pivotControl._schemaData=this,n(this.element).parents(".e-pivotclient").length>0&&(this._pivotClientObj=n(this.element).parents(".e-pivotclient").data("ejPivotClient")),this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?(f=JSON.parse(JSON.parse(this.model.pivotControl.getOlapReport()).ItemsProperties),i=JSON.parse(this.model.pivotControl.getOlapReport()),this.model.pivotControl._dataModel=="Pivot"?(i=this.model.pivotControl._filterReport(i),s=i.PivotRows.concat(i.PivotColumns,i.PivotCalculations,i.Filters),h=n.grep(f,function(t){return n.grep(s,function(n){t.id==(n.FieldHeader||n.DimensionHeader)&&(t.id=n.FieldName||n.DimensionName)}),t}),this._setTableFields(h)):this._setTableFields(f),this._setPivotRows(i.PivotRows),this._setPivotColumns(i.PivotColumns),this._setPivotCalculations(i.PivotCalculations),this._setFilters(i.Filters),this._dataModel=i.DataModel,this._currentCubeName=i.CurrentCube):(i=this.model.pivotControl.model.dataSource,this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&(this.model.pivotTableFields=t.PivotAnalysis.getTreeViewData(this.model.pivotControl.model.dataSource))),this._trigger("load",{element:this.element}),this.model._waitingPopup?this._waitingPopup=this.model._waitingPopup:(n("#"+this._id).parent()[0]&&(n("#"+this._id).parent()[0].style.position="relative",this.element.ejWaitingPopup({showOnInit:!0,appendTo:n("#"+this._id).parent()[0]})),this._waitingPopup=this.element.data("ejWaitingPopup")),this.element.width(this.model.width).height(this.model.height),this._waitingPopup.show()),this.model.enableWrapper)this.element.show(),this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?this._refreshPivotButtons():(this._setFilters(i.Filters),this._refreshPivotButtons(),this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this.element.find(".deferUpdateLayout").length==0&&this.element.append(t.buildTag("div.deferUpdateLayout",t.buildTag("input.chkDeferUpdate","",{}).attr("type","checkbox")[0].outerHTML+t.buildTag("button.btnDeferUpdate",this._getLocalizedLabels("Update"),{}).attr("type","button")[0].outerHTML)[0].outerHTML),this.model.pivotControl!=null&&this.model.enableDragDrop&&this.element.find(".e-pivotButton .e-pvtBtn").ejButton({size:"normal",type:t.ButtonType.Button,enableRTL:this.model.enableRTL}).ejDraggable({handle:"button",clone:!0,cursorAt:{left:-5,top:-5},dragStart:t.proxy(function(){this._isDragging=!0},this),dragStop:t.proxy(this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this._pvtBtnDropped:this._clientOnPvtBtnDropped,this),helper:t.proxy(function(t){if(n(t.element).addClass("dragHover"),t.sender.target.className.indexOf("e-btn")>-1){var i=n(t.sender.target).clone().attr("id",this._id+"_dragClone").appendTo("body");return n("#"+this._id+"_dragClone").removeAttr("style").height(n(t.sender.target).height()),i}return!1},this)})),this._reSizeHandler();else{var c=(this.model.layout=="onebyone"?"":t.buildTag("table.headerTable",t.buildTag("tr",t.buildTag("td",t.buildTag("div.e-listHeader",t.buildTag("span.headerText",this._getLocalizedLabels("PivotTableFieldList"),{})[0].outerHTML,{})[0].outerHTML+t.buildTag("div.listSubhead",t.buildTag("span.subheadText",this._getLocalizedLabels("ChooseFieldsToAddToReport"),{})[0].outerHTML,{})[0].outerHTML,{}).css("vertical-align","top")[0].outerHTML,{})[0].outerHTML,{width:"100%",height:"10%"})[0].outerHTML)+t.buildTag("div.e-fieldTable",(this.model.pivotControl!=null&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.layout==t.PivotSchemaDesigner.Layouts.OneByOne?t.buildTag("div.e-cubelists",t.buildTag("input#"+this._id+"_cubeList.cubeList").attr("type","text")[0].outerHTML)[0].outerHTML:"")+t.buildTag("div.parentSchemaFieldTree",t.buildTag("div.e-schemaFieldTree#"+this._id+"_schemaFieldTree",{},{width:"100%"})[0].outerHTML)[0].outerHTML)[0].outerHTML,l=t.buildTag("div.e-axisTd1",t.buildTag("div.e-pivotHeader",t.buildTag("span.headerText",this._getLocalizedLabels("ReportFilter"),{})[0].outerHTML,{})[0].outerHTML+t.buildTag("div.e-schemaFilter",this._createPivotButtons("filters",this.model.filters)).attr("aria-label","report filter").attr("aria-dropeffect","none").attr("aria-selected","false")[0].outerHTML,{})[0].outerHTML,a=t.buildTag("div.e-axisTd2",t.buildTag("div.e-rPivotHeader",t.buildTag("span.headerText",this._getLocalizedLabels("ColumnLabel"),{})[0].outerHTML,{})[0].outerHTML+t.buildTag("div.e-schemaColumn",this._createPivotButtons("columns",this.model.pivotColumns)+(this._dataModel!="XMLA"||this.model.pivotCalculations.length==0?"":this.model.pivotCalculations[0].measures!=r&&this.model.pivotCalculations[0].measures.length>0&&this.model.pivotCalculations[0].axis!=r&&this.model.pivotCalculations[0].axis=="columns"?this._createPivotButtons("columns",[{fieldName:"Measures",fieldCaption:this._getLocalizedLabels("Measures")}]):""),{})[0].outerHTML,{}).attr("aria-label","column label").attr("aria-dropeffect","none").attr("aria-selected","false")[0].outerHTML,v=t.buildTag("div.e-axisTd1#"+this._id+"_axisTd",t.buildTag("div.e-pivotHeader",t.buildTag("span.headerText",this._getLocalizedLabels("RowLabel"),{})[0].outerHTML,{})[0].outerHTML+t.buildTag("div.e-schemaRow",this._createPivotButtons("rows",this.model.pivotRows)+(this._dataModel!="XMLA"||this.model.pivotCalculations.length==0?"":this.model.pivotCalculations[0].measures!=r&&this.model.pivotCalculations[0].measures.length>0&&this.model.pivotCalculations[0].axis!=r&&this.model.pivotCalculations[0].axis=="rows"?this._createPivotButtons("rows",[{fieldName:"Measures",fieldCaption:this._getLocalizedLabels("Measures")}]):""),{})[0].outerHTML,{}).attr("aria-label","row label").attr("aria-dropeffect","none").attr("aria-selected","false")[0].outerHTML,y=t.buildTag("div.e-axisTd2#"+this._id+"_axisTd3",t.buildTag("div.e-rPivotHeader",t.buildTag("span.headerText",this._getLocalizedLabels("Values"),{})[0].outerHTML,{})[0].outerHTML+t.buildTag("div.e-schemaValue",this._createPivotButtons("values",this.model.pivotCalculations),{}).attr("aria-label","values").attr("aria-dropeffect","none").attr("aria-selected","false")[0].outerHTML,{})[0].outerHTML,p=t.buildTag("div.e-axisTable",this.model.layout=="onebyone"?a+v+l+y:t.buildTag("div",l+a,{})[0].outerHTML+t.buildTag("div",v+y,{})[0].outerHTML,this.model.enableRTL&&this.model.layout=="onebyone"?{position:"relative",left:this._pivotClientObj.model.enableSplitter?"":"7px"}:{})[0].outerHTML,w=t.buildTag("div.deferUpdateLayout",t.buildTag("input.chkDeferUpdate","",{}).attr("type","checkbox")[0].outerHTML+t.buildTag("button.btnDeferUpdate",this._getLocalizedLabels("Update"),{}).attr("type","button")[0].outerHTML)[0].outerHTML,b;b=this.model.enableRTL&&this.element.parents(".e-pivotclient").length>0&&this._pivotClientObj.model.enableSplitter?p+(this.model.layout=="onebyone"?"":t.buildTag("div.centerDiv")[0].outerHTML+t.buildTag("div.centerHead",this._getLocalizedLabels("DragFieldBetweenAreasBelow"))[0].outerHTML)+c+(n(this.element).parents(".e-pivotclient").length>0?"":t.buildTag("span.responsiveSchema","")[0].outerHTML)+(this.model.pivotControl!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&n(this.element).parents(".e-pivotclient").length==0?w:"")+(n(this.element).parents(".e-pivotclient").length>0?"":t.buildTag("div.schemaNoClick","")[0].outerHTML):c+(this.model.layout=="onebyone"?"":t.buildTag("div.centerDiv")[0].outerHTML+t.buildTag("div.centerHead",this._getLocalizedLabels("DragFieldBetweenAreasBelow"))[0].outerHTML)+p+(n(this.element).parents(".e-pivotclient").length>0?"":t.buildTag("span.responsiveSchema","")[0].outerHTML)+(this.model.pivotControl!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&n(this.element).parents(".e-pivotclient").length==0?w:"")+(n(this.element).parents(".e-pivotclient").length>0?"":t.buildTag("div.schemaNoClick","")[0].outerHTML);n(b).appendTo(this.element);this.element.parents(".e-pivotclient").length>0&&!this._pivotClientObj.model.enableSplitter&&(this._pivotClientObj.model.isResponsive&&this.element.find(".e-fieldTable").addClass("addedFieldTable"),this.model.enableRTL||this.element.find(".e-fieldTable").addClass("e-fieldDisSplitTable"));this.element.parents(".e-pivotclient").length>0&&this._pivotClientObj.model.enableSplitter&&this.element.find(".e-fieldTable").addClass("e-fieldEnSplitTable");this.model.enableRTL&&this.element.parents(".e-pivotclient").length>0&&(this.element.find(".e-fieldTable").addClass("e-clientFieldTable"),this.element.find(".e-fieldTable").addClass("rtlSplitTable"),this.element.parents(".e-pivotclient").length>0&&this._pivotClientObj.model.enableSplitter&&this.element.find(".e-axisTable").addClass("e-clientAxisSplitterTable"));this.model.pivotControl!=null?((this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode||this.model.pivotControl.element.hasClass("e-pivotclient"))&&this._refreshPivotButtons(),this._reSizeHandler()):this.element.hide()}this.model.enableDragDrop&&this.model.pivotControl&&this.element.find(".e-pivotButton .e-pvtBtn").ejButton({size:"normal",type:t.ButtonType.Button,enableRTL:this.model.enableRTL}).ejDraggable({handle:"button",clone:!0,cursorAt:{left:-5,top:-5},dragStart:t.proxy(function(){this._isDragging=!0},this),dragStop:t.proxy(this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this._pvtBtnDropped:this._clientOnPvtBtnDropped,this),helper:t.proxy(function(t){if(n(t.element).addClass("dragHover"),t.sender.target.className.indexOf("e-btn")>-1){var i=n(t.sender.target).clone().attr("id",this._id+"_dragClone").appendTo("body");return n("#"+this._id+"_dragClone").removeAttr("style").height(n(t.sender.target).height()),i}return!1},this)});this.element.find(".e-pivotButton .filterBtn").ejButton({size:"normal",enableRTL:this.model.enableRTL,type:t.ButtonType.Button,contentType:"imageonly",prefixIcon:"filter"});this.model.pivotControl&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.layout==t.PivotSchemaDesigner.Layouts.OneByOne&&(k={url:this.model.pivotControl.model.dataSource.data,cube:this.model.pivotControl.model.dataSource.cube,catalog:this.model.pivotControl.model.dataSource.catalog,request:"MDSCHEMA_CUBES"},t.Pivot._getTreeData(k,t.Pivot.getCubeList,{pvtCtrldObj:this,action:"loadcubelist",hierarchy:this._selectedField}),this.element.find(".cubeList").ejDropDownList({dataSource:this.model.cubeCollection,enableRTL:this.model.enableRTL,fields:{text:"name",value:"name"},width:"100%",height:"27px",change:t.proxy(this._cubeChanged,this),create:function(){n(this.wrapper.find(".e-input")).focus(function(){n(this).blur()})}}),e=this.element.find(".cubeList").data("ejDropDownList"),t.isNullOrUndefined(e)||e.selectItemByText(this.model.pivotControl.model.dataSource.cube));this.element.find(".e-pivotButton input").ejToggleButton({size:"normal",enableRTL:this.model.enableRTL,contentType:"imageonly",defaultPrefixIcon:"ascending",activePrefixIcon:"descending",click:t.proxy(this._sortBtnClick,this)});this.model.pivotControl!=null&&(this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot||this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?(this.model.pivotControl.operationalMode!=t.Pivot.OperationalMode.Olap||this.model.olap.showKpi||(o=[],u=!1,n.each(this.model.pivotTableFields,function(n,t){t.spriteCssClass.indexOf("kpiRootCDB")>-1?u=!0:t.spriteCssClass.indexOf("dimensionCDB")>-1&&(u=!1);u||o.push(t)}),this.model.pivotTableFields=o),this._createTreeView(this,this.model.pivotTableFields)):t.Pivot.generateTreeViewData(this));this.model.enableRTL&&this.element.addClass("e-rtl");this._createContextMenu();this._waitingPopup&&this._waitingPopup.hide();this._setPivotBtnWidth();this.element.parents(".e-pivotclient").length==0&&this.model.pivotControl!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode&&this._setFilterIcons()},_setFilterIcons:function(){var u=1,i,r,n,e,f;do{for(i=u==1?this.model.pivotControl.model.dataSource.columns:u==2?this.model.pivotControl.model.dataSource.rows:this.model.pivotControl.model.dataSource.filters,r=this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot?!0:!1,n=0;n<i.length;n++)e=r?t.PivotAnalysis.getMembers(i[n].fieldName):[],!t.isNullOrUndefined(i[n].filterItems)&&(i[n].filterItems.filterType=="exclude"||(r?i[n].filterItems.values.length<e.length:i[n].filterItems.values.length>0))&&this.element.find(".e-schemaFieldTree li["+r?"id":"data-tag='"+i[n].fieldName+"']").find(".filter").length==0&&(f=t.buildTag("span.e-icon",{display:"none"}).addClass("filter")[0].outerHTML,r?this.element.find(".e-schemaFieldTree li[id='"+i[n].fieldName+"']").find(".e-text").after(f):this.element.find(".e-schemaFieldTree li[data-tag='"+i[n].fieldName+"'] div:first .e-text").after(f),this.element.find(".e-pvtBtn[data-fieldName='"+i[n].fieldName+"']").parent().find(".filter").addClass("filtered"));u++}while(u<4)},setCubeList:function(n){this.model.cubeCollection=n},refreshControl:function(){var i,t;for(n(this._tableTreeObj.element).ejTreeView("unCheckAll"),this._isDropAction=!0,i=this.model.pivotControl.model.dataSource,t=0;t<i.rows.length;t++)this._tableTreeObj.checkNode(this._tableTreeObj.element.find("li[data-tag='"+i.rows[t].fieldName+"']"));for(t=0;t<i.columns.length;t++)this._tableTreeObj.checkNode(this._tableTreeObj.element.find("li[data-tag='"+i.columns[t].fieldName+"']"));for(t=0;t<i.filters.length;t++)this._tableTreeObj.checkNode(this._tableTreeObj.element.find("li[data-tag='"+i.filters[t].fieldName+"']"));for(t=0;t<i.values.length;t++)this._tableTreeObj.checkNode(this._tableTreeObj.element.find("li[data-tag='"+i.values[t].fieldName+"']"));this._isDropAction=!1;this._refreshPivotButtons()},_cubeChanged:function(i){if(this.model.pivotControl.model.dataSource.cube!=i.selectedValue){var r=this,o=-1,s=0,u,f=[],e=this.model.pivotControl.element.find("#"+this._id+"_reportList").data("ejDropDownList"),h={CubeName:this.model.pivotControl.model.dataSource.cube,CurrentReport:jQuery.extend(!0,{},this.model.pivotControl.model.dataSource),Reports:JSON.parse(JSON.stringify(this.model.pivotControl._clientReportCollection)),ReportIndex:e.selectedIndexValue,ReportList:JSON.parse(JSON.stringify(e.model.dataSource)),calculatedMembers:this.model.pivotControl.model.calculatedMembers};n.map(this._repCollection,function(n,t){n.CubeName==r.model.pivotControl.model.dataSource.cube&&(t=t)});o!=-1?this._repCollection[o]=h:this._repCollection.push(h);this._pivotClientObj._waitingPopup&&setTimeout(function(){var t=n(i.target).parents(".e-pivotclient").length>0?n(i.target).parents(".e-pivotclient").data("ejPivotClient"):n(this.element).parents(".e-pivotclient").data("ejPivotClient");t._waitingPopup.show()},0);this.model.pivotControl.model.dataSource.cube=i.selectedValue;this.model.pivotControl.model.dataSource.reportName="Default";this.model.pivotControl.model.dataSource.rows=this.model.pivotControl.model.dataSource.columns=this.model.pivotControl.model.dataSource.values=this.model.pivotControl.model.dataSource.filters=[];this.model.pivotControl._clientReportCollection=[];r.model.pivotControl.model.calculatedMembers=[];n.map(this._repCollection,function(n){n.CubeName==r.model.pivotControl.model.dataSource.cube&&(r.model.pivotControl.model.dataSource=n.CurrentReport,r.model.pivotControl.model.calculatedMembers=n.calculatedMembers,f=n.Reports,s=n.ReportIndex,u=n.ReportList)});delete this.model.pivotControl._fieldData;this.element.find(".e-pivotButton").remove();this.element.find(".e-schemaFieldTree").empty();t.Pivot.generateTreeViewData(this,this.model.pivotControl.model.dataSource);this._pivotClientObj.refreshControl();f.length>0?this.model.pivotControl._clientReportCollection=f:(u=[{name:"Default"}],this.model.pivotControl._clientReportCollection.push(this.model.pivotControl.model.dataSource));this._pivotClientObj.element.find(".reportlist").ejDropDownList("option","dataSource",u);e.selectItemByIndex(s)}},_setPivotBtnWidth:function(){if(this.model.layout!="excel"&&this.model.pivotControl){var r=this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?!0:!1,u=r?this._dataModel:this.model.pivotControl._dataModel,i=u=="Olap"||u=="XMLA"?15:20;n.each(this.element.find(".e-schemaRow .e-pvtBtn,.e-schemaColumn .e-pvtBtn"),function(t,u){n(u).attr("aria-describedby")=="Measures"?n(u).width(n(u).parent().width()-i-10):(n(u).width(n(u).parent().width()-3*i),this._dataModel!="Pivot"&&r&&n(".e-pivotButton:contains('"+n(u).text()+"'):first").find(".filtered").length>0&&n(u).parent().find(".filter").addClass("filtered"))});n.each(this.element.find(".e-schemaValue .e-pvtBtn"),function(t,r){n(r).width(n(r).parent().width()-i-10)});n.each(this.element.find(".e-schemaFilter .e-pvtBtn"),function(t,u){n(u).width(n(u).parent().width()-2*i-10);this._dataModel!="Pivot"&&r&&n(".e-pivotButton:contains('"+n(u).text()+"'):first").find(".filtered").length>0&&n(u).parent().find(".filter").addClass("filtered")})}else n.each(this.element.find(".e-pvtBtn"),function(t,i){n(i).width(n(i).parent().width()-15)})},_onContextOpen:function(i){n(i.target).hasClass("e-removeClientPivotBtn")||t.Pivot._contextMenuOpen(i,this)},_contextClick:function(r){var h,u,f,c,e,o,s;if(n(r.element).hasClass("summarytype")){if(f=r.element.id.split("_")[r.element.id.split("_").length-1],f!=null)if(h=f.toLowerCase(),u=this.model.pivotControl,u.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&u.model.operationalMode==t.Pivot.OperationalMode.ClientMode){if(u.model.dataSource.values.length>=1){for(i=0;i<u.model.dataSource.values.length;i++)f=this._selectedMember[0].dataset.fieldname,u.model.dataSource.values[i].fieldName==f&&(u.model.dataSource.values[i].summaryType=h);u.refreshControl()}}else if(u.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&u.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&JSON.parse(u.getOlapReport()).PivotCalculations.length>=1){var f=this._selectedMember[0].dataset.fieldname,l=r.element.id.split("_")[r.element.id.split("_").length-1],a={fieldname:f,type:l};u.doAjaxPost("POST",u.model.url+"/"+u.model.serviceMethodSettings.changeSummaryType,JSON.stringify({action:"changeSummaryType",currentReport:JSON.parse(u.getOlapReport()).Report,summaryType:JSON.stringify(a),clientParams:u.model.enableMeasureGroups+"-"+u.model.chartType+"-"+u.model.enableKPI}),u._renderControlSuccess)}}else c=this,e=n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejPivotClient"):null,t.isNullOrUndefined(e)?this.model.pivotControl._waitingPopup.show():(e._isTimeOut=!0,setTimeout(function(){e._isTimeOut&&c.model.pivotControl._waitingPopup.show()},800)),o=r.text==this._getLocalizedLabels("AddToColumn")?this.element.find(".e-schemaColumn"):r.text==this._getLocalizedLabels("AddToRow")?this.element.find(".e-schemaRow"):r.text==this._getLocalizedLabels("AddToValues")?this.element.find(".e-schemaValue"):r.text==this._getLocalizedLabels("AddToFilter")?this.element.find(".e-schemaFilter"):"",o!=""?(s={element:this._selectedMember,target:o[0],cancel:!1},this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this._pvtBtnDropped(s):this._clientOnPvtBtnDropped(s)):this.model.pivotControl._waitingPopup.hide()},_createTreeView:function(i,u){var a,e,v,h,o,f,y,p,c,s,w,b,l;if(this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.pivotControl!=null&&this.model.pivotControl.element.hasClass("e-pivotclient")&&this.model.pivotControl.model.toolbarIconSettings.enableCalculatedMember&&(a=[{id:"_0",name:"Calculated Members",hasChildren:!0,spriteCssClass:"e-calcMemberGroupCDB e-icon",tag:""}],u[0].id!="_0"&&u.splice(0,0,a[0]),n.map(this.model.pivotControl.model.calculatedMembers,function(n){var i=t.isNullOrUndefined(n.tag)?!t.isNullOrUndefined(n.memberType)&&n.memberType.toLowerCase().indexOf("measure")>-1?"[measures].["+n.caption+"]":n.caption:n.tag;u.push({id:n.caption,pid:"_0",name:n.caption,hasChildren:!1,spriteCssClass:"e-calcMemberCDB e-icon",tag:i,expression:n.expression,formatString:n.formatString,nodeType:0,hierarchyUniqueName:n.memberType=="Measure"?"":t.isNullOrUndefined(n.hierarchyUniqueName)?"":n.hierarchyUniqueName})})),this.element.find(".e-schemaFieldTree").ejTreeView({showCheckbox:this.model.layout=="onebyone"?!1:!0,fields:{id:"id",parentId:"pid",text:this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode||this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap?"name":"caption",isChecked:"isSelected",spriteCssClass:"spriteCssClass",dataSource:u,parentUniqueName:"parentUniqueName"},enableRTL:this.model.enableRTL,allowDragAndDrop:this.model.enableDragDrop?!0:!1,allowDropChild:!1,allowDropSibling:!1,dragAndDropAcrossControl:!0,beforeDelete:function(){return!1},cssClass:"pivotTreeViewDragedNode",nodeDropped:t.proxy(this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this._nodeDropped:this._clientOnNodeDropped,this),nodeDragStart:t.proxy(this._nodeDrag,this),nodeDrag:t.proxy(this._nodeDraged,this)}),e=[],v=this.model.pivotControl.model.dataSource.providerName==t.olap.Providers.Mondrian,this._tableTreeObj=this.element.find(".e-schemaFieldTree").data("ejTreeView"),this._tableTreeObj.element.find(".e-ul").css({width:"100%",height:"100%"}),this._tableTreeObj.element.find(".e-measureGroupCDB").parent().siblings(".e-chkbox-wrap").remove(),this._tableTreeObj.element.find(".e-kpiCDB, .e-kpiRootCDB, .e-kpiGoalCDB, .e-kpiStatusCDB, .e-kpiTrendCDB, .e-kpiValueCDB").parents().siblings(".e-chkbox-wrap").remove(),this._tableTreeObj.element.find(".e-folderCDB").parent().siblings(".e-chkbox-wrap").remove(),this._tableTreeObj.element.find(".e-dimensionCDB").parent().siblings(".e-chkbox-wrap").remove(),this.model.layout!=t.PivotSchemaDesigner.Layouts.OneByOne&&(this._tableTreeObj.element.find(".e-measureGroupCDB").parents("li").length==0?this._tableTreeObj.element.find("#\\[Measures\\]").append(t.buildTag("span.e-elementSeparator")[0].outerHTML):this._tableTreeObj.element.find(".e-measureGroupCDB").parents("li").append(t.buildTag("span.e-elementSeparator")[0].outerHTML),this._tableTreeObj.element.find(".e-kpiRootCDB, .e-dimensionCDB").parents("li").append(t.buildTag("span.e-elementSeparator")[0].outerHTML)),this.model.layout==t.PivotSchemaDesigner.Layouts.OneByOne&&(e=this._tableTreeObj.element,n.map(u,function(t){t.defaultHierarchy&&n(e).find("li[id='"+t.tag+"']").attr("data-defaultHierarchy",t.defaultHierarchy)})),h=[],this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap){for(o=JSON.parse(this.model.pivotControl.getOlapReport()),f=0;f<o.PivotRows.length;f++)o.PivotRows[f].FieldHeader!="Measures"&&o.PivotRows[f].Filtered&&h.push(o.PivotRows[f].FieldHeader);for(f=0;f<o.PivotColumns.length;f++)o.PivotColumns[f].FieldHeader!="Measures"&&o.PivotColumns[f].Filtered&&h.push(o.PivotColumns[f].FieldHeader);for(f=0;f<o.Filters.length;f++)o.Filters[f].FieldHeader!="Measures"&&o.Filters[f].Filtered&&h.push(o.Filters[f].FieldHeader)}for(e=this._tableTreeObj.element.find("li"),f=0;f<e.length;f++)y=this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode?v?n(e[f]).attr("id").split("~#^")[0]:n(e[f]).attr("id"):this.model.pivotTableFields[f].tag,e[f].setAttribute("data-tag",y),t.isNullOrUndefined(u[f].parentUniqueName)||u[f].parentUniqueName==""||e[f].setAttribute("data-parentUniqueName",t.isNullOrUndefined(u[f].parentUniqueName)?"":u[f].parentUniqueName.split(">>||>>")[1]),this.model.pivotControl.model.operationalMode!=t.PivotGrid.OperationalMode.ClientMode||t.isNullOrUndefined(e[f].id)||(e[f].id=n(e[f]).attr("id").replace(/ /g,"_")),p=n(e[f]).find(".e-chkbox-wrap"),this.element.find(".e-pvtBtn[data-fieldName=KPI]").length>0&&n(n(e[f])).find(".e-folderCDB").length>0&&n(e[f]).attr("data-tag")=="KPI"&&(s=t.buildTag("span.e-icon").css("display","none").addClass("treeDrop")[0].outerHTML,n(n(e[f]).find(".e-text")[0]).after(s)),n(p[0]).attr("aria-checked")=="true"&&n(n(e[f])).find(".e-folderCDB").length<=0&&n(e[f]).attr("data-tag").toLowerCase().indexOf("[measures]")==-1&&(this.model.pivotControl._dataModel=="XMLA"?n(e[f]).parents("li:eq(0)").length>0&&n(e[f]).parents("li:eq(0)").attr("data-tag").toLowerCase().indexOf("[measures]")==-1&&(c=t.Pivot.getReportItemByFieldName(n(e[f]).attr("data-tag"),this.model.pivotControl.model.dataSource,this._dataModel).item,!t.isNullOrUndefined(c)>0&&(c.isNamedSets==r||!c.isNamedSets)&&(s=t.buildTag("span.e-icon").css("display","inline-block").addClass("treeDrop").attr("role","button").attr("aria-label","filter button")[0].outerHTML,n(n(e[f]).find(".e-text")[0]).after(s))):(s=t.buildTag("span.e-icon").css("display","none").addClass("treeDrop")[0].outerHTML,n(n(e[f]).find(".e-text")[0]).after(s),n.inArray(n(n(e[f]).find(".e-text")[0])[0].innerText,h)>-1&&n(e[f]).find(".filter").length==0&&(w=t.buildTag("span.e-icon").addClass("filter")[0].outerHTML,n(n(e[f]).find(".e-text")[0]).after(w))));this.model.pivotControl!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode?(this._tableTreeObj.model.nodeCheck=t.proxy(this._clientPivotCheckedStateModified,this),this._tableTreeObj.model.nodeUncheck=t.proxy(this._clientPivotCheckedStateModified,this)):(this._tableTreeObj.model.nodeCheck=t.proxy(this._checkedStateModified,this),this._tableTreeObj.model.nodeUncheck=t.proxy(this._checkedStateModified,this));this._tableTreeObj.element.find(".e-attributeCDB").parent().siblings("ul").remove();this._tableTreeObj.element.find(".e-attributeCDB").closest("div").find(".e-plus").remove();this._tableTreeObj.element.find(".e-hierarchyCDB").parent().parent().siblings("ul").find(".e-chkbox-wrap").remove();this._tableTreeObj.element.find(".e-plus").length==0&&this._tableTreeObj.element.find(".e-item").css("padding","0px");this.model.pivotControl!=null&&this.model.pivotControl.element.hasClass("e-pivotclient")&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&t.Pivot._refreshFieldList(this);this.element.find(".e-schemaFilter, .e-schemaColumn, .e-schemaRow, .e-schemaValue").ejDroppable({});this.model.pivotControl!=null&&(b=t.buildTag("ul.pivotTreeContext#"+this._id+"_pivotTreeContext",t.buildTag("li",t.buildTag("a",this._getLocalizedLabels("AddToFilter"))[0].outerHTML)[0].outerHTML+t.buildTag("li",t.buildTag("a",this._getLocalizedLabels("AddToRow"))[0].outerHTML)[0].outerHTML+t.buildTag("li",t.buildTag("a",this._getLocalizedLabels("AddToColumn"))[0].outerHTML)[0].outerHTML+t.buildTag("li",t.buildTag("a",this._getLocalizedLabels("AddToValues"))[0].outerHTML)[0].outerHTML+t.buildTag("li#"+this._id+"_e-remove",t.buildTag("a",this._getLocalizedLabels("Remove"))[0].outerHTML)[0].outerHTML)[0].outerHTML,n(this.element).append(b),n("#"+this._id+"_pivotTreeContext").ejMenu({menuType:t.MenuType.ContextMenu,openOnClick:!1,enableRTL:this.model.enableRTL,contextMenuTarget:this._tableTreeObj.element,click:t.proxy(this._treeContextClick,this),beforeOpen:t.proxy(this._contextOpen,this),close:t.proxy(t.Pivot.closePreventPanel,this)}));this.model.pivotControl!=null&&(l=!1,this.model.pivotControl.model.enableDeferUpdate&&(l=!0),this.element.find(".btnDeferUpdate").ejButton({size:"mini",type:t.ButtonType.Button,enabled:l,click:t.proxy(this.model.pivotControl._deferUpdate,this.model.pivotControl)}),this.element.find(".chkDeferUpdate").ejCheckBox({text:this._getLocalizedLabels("DeferLayoutUpdate"),change:this._checkedChange,size:"normal",checked:l}));this._waitingPopup.hide();this._unWireEvents();this._wireEvents()},_nodeDraged:function(i){var r=this.model.pivotControl,u=(!t.isNullOrUndefined(r)&&r.model.operationalMode=="clientmode"&&!i.target.hasClass("e-schemaValue")&&!i.target.parents().hasClass("e-schemaValue")||r.model.operationalMode=="servermode"&&!(i.target.hasClass("e-schemaRow")||i.target.hasClass("e-schemaColumn"))&&!(i.target.parents().hasClass("e-schemaRow")||i.target.parents().hasClass("e-schemaColumn")))&&n(".pivotTreeViewDragedNode .e-dropedStatus").hasClass("e-plus")&&i.draggedElementData.id.indexOf("Measures")>=0;i.draggedElementData.id!=null&&r.model.analysisMode==t.Pivot.AnalysisMode.Olap&&r.model.operationalMode=="clientmode"&&(u=i.draggedElementData.id.indexOf("Measures")>0?!i.target.hasClass("e-schemaValue")&&!i.target.parents().hasClass("e-schemaValue"):i.target.hasClass("e-schemaValue")||i.target.parents().hasClass("e-schemaValue"));u&&(document.body.style.cursor="not-allowed",n(".pivotTreeViewDragedNode .e-dropedStatus").removeClass().addClass("e-dropedStatus e-icon e-minus"));this._trigger("dragMove",i)},_setFirst:!1,_setModel:function(t){for(var i in t)switch(i){case"OlapReport":this.setOlapReport(t[i]);break;case"locale":this.model.locale=t[i];this._load();break;case"locale":case"enableRTL":n(this.element).html("");this._load();break;case"height":this.model.height=t[i];this._load();break;case"width":this.model.width=t[i];this._load();break;case"layout":this.model.layout=t[i];this._load();break;case"olap":this.model.olap=n.extend({},this.model.olap,t[i]);this._load();break;case"enableRTL":this.model.enableRTL=t[i];this._load();break;case"enableWrapper":this.model.enableWrapper=t[i];this._load();break;case"enableDragDrop":this.model.enableDragDrop=t[i];this._load()}},_wireEvents:function(){if(this._on(n(document),"keydown",this._keyDownPress),this._on(n(document),"keyup",t.proxy(function(n){n.keyCode===93&&n.preventDefault()})),this._on(this.element.find("a.e-linkPanel"),"click",t.Pivot._editorLinkPanelClick),this.model.layout!="excel"&&this.model.layout!=t.PivotSchemaDesigner.Layouts.OneByOne&&(this._on(this.element,"mouseover",".e-pivotButton .pvtBtnDiv",t.proxy(function(t){this._isDragging||n(t.target).find("button").removeClass("e-hoverBtn").addClass("e-hoverBtn")},this)),this._on(this.element,"mouseleave",".e-pivotButton .pvtBtnDiv",t.proxy(function(t){n(t.target).find("button").length>0?n(t.target).find("button").removeClass("e-hoverBtn"):n(t.target).removeClass("e-hoverBtn")},this)),this._on(this.element,"mouseover",".filter,.sorting,.e-removeBtn",t.proxy(function(t){this._isDragging||n(t.target.parentElement).find("button").removeClass("e-hoverBtn").addClass("e-hoverBtn")},this)),this._on(this.element,"mouseleave",".filter,.sorting,.e-removeBtn",t.proxy(function(t){n(t.target.parentElement).find("button").removeClass("e-hoverBtn")},this))),this._on(this.element,"mouseover",".pvtBtnDiv .e-pvtBtn",t.proxy(function(t){this._isDragging&&(this.element.find(".e-dropIndicator").removeClass("e-dropIndicatorHover"),n(t.target).parent().siblings(".e-dropIndicator").addClass("e-dropIndicatorHover"))},this)),this._on(this.element,"click","#preventDiv",t.proxy(function(){this.element.find(".e-dialog.e-advancedFilterDlg:visible").length>0&&(this.element.find(".e-dialog.e-advancedFilterDlg").hide(),this.element.find("#preventDiv").remove())},this)),this._on(this.element,"mouseleave",".e-pivotButton",t.proxy(function(t){this._isDragging&&n(t.target).siblings(".e-dropIndicator").removeClass("e-dropIndicatorHover")},this)),this._on(this.element,"mouseover",".e-pivotButton",t.proxy(function(t){n(t.target).attr("title",t.target.textContent)},this)),this._tableTreeObj.element.find("li").mouseover(t.proxy(function(i){var u,r;n(i.target).siblings("span.e-icon.filter:eq(0)").length>0||n(i.target).find("span.e-icon.filter:eq(0)").length>0||n(i.target).parentsUntil("li").find("span.e-icon.filter:eq(0)").length>0?(u="-31px",this.element.find("span.e-icon.filter").attr("role","button").attr("aria-label","filtered")):(u="-20px",this.element.find("span.e-icon.treeDrop").attr("role","button").attr("aria-label","filter button"));r=n(i.target).siblings("span.e-icon.filter:eq(0)").length>0||n(i.target).find("span.e-icon.filter:eq(0)").length>0||n(i.target).parentsUntil("li").find("span.e-icon.filter:eq(0)").length>0?10:5;this.model.enableRTL?this.model.pivotControl.model.analysisMode!=t.Pivot.AnalysisMode.Pivot?n(i.target).siblings("span.e-icon.treeDrop:eq(0)").length>0?n(i.target).siblings("span.e-icon.treeDrop:eq(0)").css({display:"inline-block",position:"absolute",top:n(i.target).hasClass("filter")?n(i.target).position().top-22:n(i.target).position().top+2,left:n(i.target).hasClass("filter")?n(i.target).position().left+(6-r):(n(i.target).attr("role")==""?n(i.target).position().left:-2)+(5-r)}):n(i.target).find("span.e-icon.treeDrop:eq(0)").length>0?n(i.target).find("span.e-icon.treeDrop:eq(0)").css({display:"inline-block",position:"absolute"}):n(i.target).parentsUntil("li").find("span.e-icon.treeDrop:eq(0)").css({display:"inline-block",position:"absolute"}):n(i.target).siblings("span.e-icon.treeDrop:eq(0)").length>0?n(i.target).siblings("span.e-icon.treeDrop:eq(0)").css({display:"inline-block",position:"absolute",top:n(i.target).hasClass("filter")?n(i.target).position().top-22:n(i.target).position().top+2,left:n(i.target).hasClass("filter")?n(i.target).position().left+(7-r):n(i.target).position().left+(10-r)}):n(i.target).find("span.e-icon.treeDrop:eq(0)").length>0?n(i.target).find("span.e-icon.treeDrop:eq(0)").css({display:"inline-block",position:"absolute"}):n(i.target).parentsUntil("li").find("span.e-icon.treeDrop:eq(0)").css({display:"inline-block",position:"absolute"}):n(i.target).siblings("span.e-icon.treeDrop:eq(0)").length>0?n(i.target).siblings("span.e-icon.treeDrop:eq(0)").css({display:"inline-block",position:"static","margin-left":u}):n(i.target).find("span.e-icon.treeDrop:eq(0)").length>0?n(i.target).find("span.e-icon.treeDrop:eq(0)").css({display:"inline-block",position:"static"}):n(i.target).parentsUntil("li").find("span.e-icon.treeDrop:eq(0)").css({display:"inline-block",position:"static"});n(i.target).parent().find(".e-measureGroupCDB, .e-folderCDB",this.model.layout==t.PivotSchemaDesigner.Layouts.OneByOne?"":".e-dimensionCDB").length>0&&n(i.target).css("cursor","default")},this)).mouseout(t.proxy(function(){n(this._tableTreeObj.element).find("span.e-icon.treeDrop").css({display:"none"})},this)),this._on(this.element,"click",".filterBtn,.filter",t.proxy(this._filterBtnClickCommon,this)),this._on(this.element,"click",".sorting",t.proxy(this._sortBtnClick,this)),this._on(this.element,"click",".e-removeBtn",t.proxy(this._removePvtBtn,this)),this._on(this.element,"click",".e-pvtBtn",t.proxy(this._filterBtnClickCommon,this)),this._on(this._tableTreeObj.element,"click",".treeDrop",t.proxy(this._filterBtnClickCommon,this)),this._on(this.element,"click",".e-memberAscendingIcon, .e-memberDescendingIcon",t.proxy(t.Pivot._memberSortBtnClick,this)),this._on(this.element,"click",".e-removeClientPivotBtn",t.proxy(this._removeBtnClick,this)),this._on(this.element,"click",".e-removePivotBtn",t.proxy(this._removeBtnClick,this)),this._on(this.element,"click",".e-ascOrder, .e-descOrder",t.proxy(this._sortField,this)),this._on(this.element,"click",".collapseSchema",t.proxy(this._hideSchemaDesigner,this)),this._on(this.element,"click",".expandSchema",t.proxy(this._showSchemaDesigner,this)),this._on(this.element,"click",".e-nextPage, .e-prevPage, .e-firstPage, .e-lastPage",t.proxy(this._navigateTreeData,this)),this._on(this.element,"click",".e-searchEditorTree",t.proxy(function(n){t.Pivot._searchEditorTreeNodes(n,this)},this)),!(this.element.parents(".e-pivotclient").length>0))n(window).on("resize",n.proxy(this._reSizeHandler,this));this._on(this.element,"click",".e-summarytype",t.proxy(this._contextClick,this))},_navigateTreeData:function(n){t.Pivot.editorTreeNavigatee(n,this)},_filterBtnClickCommon:function(i){if(n(i.target).hasClass("e-pvtBtn")&&this.element.parents(".e-pivotclient").length>0||n(i.target).hasClass("treeDrop")||n(i.target).hasClass("filter"))if(this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode)this._clientOnFilterBtnClick(i);else if(this.model.pivotControl.model.operationalMode!=t.Pivot.OperationalMode.ServerMode||t.isNullOrUndefined(n(i.target.nextElementSibling)))this._filterBtnClick(i);else{if(n(i.target.nextElementSibling).hasClass("treeDrop"))return!1;this._filterBtnClick(i)}},_unWireEvents:function(){this._off(this.element,"click",".filterBtn, .e-ascOrder, .e-descOrder");this._off(n(document),"keydown",this._keyDownPress);this._off(n(document),"keyup");this._off(this.element.find("a.e-linkPanel"),"click",t.Pivot._editorLinkPanelClick);this._off(this.element,"click","#preventDiv");this._off(this.element,"mouseover",".e-pivotButton");this._off(this.element,"mouseover",".e-pivotButton .e-pvtBtn");this._off(this.element,"mouseleave",".e-pivotButton");this._off(this.element,"click",".e-memberAscendingIcon, .e-memberDescendingIcon");this._off(this.element,"click",".sorting");this._off(this.element,"click",".filter");this._off(this.element,"click",".e-removeBtn,.e-removeClientPivotBtn");this._off(this.element,"mouseover",".e-schemaFieldTree li");this._off(this.element,"mouseout",".e-schemaFieldTree li");this._off(this._tableTreeObj.element,"click",".treeDrop");this._off(this.element,"click",".e-ascOrder, .e-descOrder",t.proxy(this._sortField,this));this._off(this.element,"click",".filterBtn");this._off(this.element,"click",".e-pvtBtn");this._off(this._tableTreeObj.element,"click",".treeDrop");this._off(this._tableTreeObj.element,"click","li");this._off(this.element,"click",".e-removeClientPivotBtn, .e-removePivotBtn");n(window).off("resize",n.proxy(this._reSizeHandler,this));this._off(this.element,"click",".e-nextPage, .e-prevPage, .e-firstPage, .e-lastPage");this._off(this.element,"click",".e-searchEditorTree");this._off(this.element,"click",".e-summarytype")},_keyDownPress:function(i){var r,u,f,o,e;if((i.keyCode===40||i.which===38)&&(!t.isNullOrUndefined(this.model.pivotControl._curFocus.tab)&&this.model.pivotControl._curFocus.tab.hasClass("e-text")||n("#"+this._id).find(".e-schemaFieldList .e-text:visible").hasClass("e-hoverCell"))&&!n(".e-editorTreeView:visible").length>0&&!n(".pivotTree:visible,.pivotTreeContext:visible,.pivotTreeContextMenu:visible").length>0?(n("#"+this._id).find(".e-hoverCell").removeClass("e-hoverCell"),this.model.pivotControl._curFocus.tab.mouseleave(),i.preventDefault(),r=n("#"+this._id).find(".e-schemaFieldTree .e-text:visible"),t.isNullOrUndefined(this._curFocus.tree)?(this._index.tree=i.which==40?1:i.which==38?r.length-1:0,this._curFocus.tree=r.eq(this._index.tree).attr("tabindex","-1")):(this._curFocus.tree.attr("tabindex","0").removeClass("e-hoverCell").mouseleave(),i.which===40?this._index.tree=this._index.tree+1>r.length-1?0:this._index.tree+1:i.which===38&&(this._index.tree=this._index.tree-1<0?r.length-1:this._index.tree-1),this._curFocus.tree=r.eq(this._index.tree).attr("tabindex","-1")),this._curFocus.tree.focus().addClass("e-hoverCell").mouseover(),n(".e-node-focus").removeClass("e-node-focus")):(i.which===40||i.which===38||i.which===37||i.which===39)&&!t.isNullOrUndefined(this.model.pivotControl._curFocus.tab)&&this.model.pivotControl._curFocus.tab.hasClass("e-pvtBtn")&&!n(".pivotTree:visible,.pivotTreeContext:visible,.pivotTreeContextMenu:visible").length>0&&document.activeElement.className.startsWith("e-pvtBtn")?(i.preventDefault(),n("#"+this._id).find(".e-hoverCell").removeClass("e-hoverCell"),t.isNullOrUndefined(this.model.pivotControl._curFocus.tab)||this.model.pivotControl._curFocus.tab.mouseleave().attr("tabindex","0").removeClass("e-hoverCell"),r=n("#"+this._id).find(".e-pvtBtn"),t.isNullOrUndefined(this._curFocus.button)?i.which===40||i.which===39?this._curFocus.button=r.eq(1).attr("tabindex","-1"):(i.which===38||i.which===37)&&(this._curFocus.button=r.eq(r.length-1).attr("tabindex","-1")):(this._curFocus.button.attr("tabindex","0").removeClass("e-hoverCell").mouseleave(),i.which===40||i.which===39?this._index.button=this._index.button+1>r.length-1?0:this._index.button+1:(i.which===38||i.which===37)&&(this._index.button=this._index.button-1<0?r.length-1:this._index.button-1),this._curFocus.button=r.eq(this._index.button)),this._curFocus.button.addClass("e-hoverCell").mouseover().focus()):(i.keyCode===40||i.which===38)&&n("#"+this._id).find(".e-editorTreeView:visible").length>0&&!t.isNullOrUndefined(n("#"+this._id).find(".e-dialog .e-text"))&&n(".e-dialog .e-text").hasClass("e-hoverCell")&&(n("#"+this._id).find(".e-editorTreeView .e-hoverCell").removeClass("e-hoverCell"),i.preventDefault(),r=n("#"+this._id).find(".e-dialog .e-text:visible"),t.isNullOrUndefined(this._curFocus.node)?(this._index.node=i.which==40?1:i.which==38?r.length-1:0,this._curFocus.node=r.eq(this._index.node).attr("tabindex","-1")):(this._curFocus.node.attr("tabindex","0").removeClass("e-hoverCell"),i.which===40?this._index.node=this._index.node+1>r.length-1?0:this._index.node+1:i.which===38&&(this._index.node=this._index.node-1<0?r.length-1:this._index.node-1),this._curFocus.node=r.eq(this._index.node).attr("tabindex","-1")),this._curFocus.node.focus().addClass("e-hoverCell"),n(".e-node-focus").removeClass("e-node-focus")),(i.which===39||i.which===37)&&n("#"+this._id).find(".e-schemaFieldTree .e-text:visible").hasClass("e-hoverCell")&&!n(".e-editorTreeView:visible").length>0?n("#"+this._id).find(".e-schemaFieldTree .e-hoverCell").parent().find(".e-plus,.e-minus").click():(i.which===39||i.which===37)&&n("#"+this._id).find(".e-editorTreeView .e-text:visible").hasClass("e-hoverCell")?n("#"+this._id).find(".e-editorTreeView .e-hoverCell").parent().find(".e-plus,.e-minus").click():i.which===9&&n("#"+this._id).find(".e-editorTreeView:visible").length>0&&(i.preventDefault(),n("#"+this._id).find(".e-dialog .e-hoverCell").removeClass("e-hoverCell"),this._curFocus.node=null,this._index.node=0,u=[],u.push(n("#"+this._id).find(".e-dialog .e-text").first()),n("#"+this._id).find(".e-dialogOKBtn:visible").hasClass("e-disable")||u.push(n("#"+this._id).find(".e-dialogOKBtn:visible")),u.push(n("#"+this._id).find(".e-dialogCancelBtn:visible")),u.push(n("#"+this._id).find(".e-close:visible")),t.isNullOrUndefined(this._curFocus.tab)?(this._index.tab=1,this._curFocus.tab=u[this._index.tab].attr("tabindex","-1")):(this._curFocus.tab.attr("tabindex","0").removeClass("e-hoverCell"),this._index.tab=this._index.tab+1>u.length-1?0:this._index.tab+1,this._curFocus.tab=u[this._index.tab].attr("tabindex","-1")),this._curFocus.tab.focus().addClass("e-hoverCell"),i.stopImmediatePropagation()),i.which===13&&n("#"+this._id).find(".e-hoverCell").length>0&&!n(".pivotTree:visible,.pivotTreeContext:visible,.pivotTreeContextMenu:visible").length>0){if(n("#"+this._id).find(".e-dialog:visible").length>0||n("#"+this.model.pivotControl._id).find(".e-dialog:visible").length>0){if(f=n("#"+this._id).find(".e-dialog:visible").length>0?this:this.model.pivotControl,n(i.target).hasClass("e-memberCurrentPage")||n(i.target).hasClass("e-memberCurrentSearchPage")||n(i.target).hasClass("e-memberCurrentDrillPage")){t.Pivot.editorTreeNavigatee(i,f);return}if(n(i.target).hasClass("searchEditorTreeView")){t.Pivot._searchEditorTreeNodes(i,f);return}n("#"+f._id).find(".e-dialog .e-hoverCell").parent().find(".e-chkbox-small").length>0?n("#"+f._id).find(".e-dialog .e-hoverCell").parent().find(".e-chkbox-small").click():n("#"+this.model.pivotControl._id).find(".e-dialog:visible").length==0&&(n("#"+this._id).find(".e-dialog .e-hoverCell").click(),this._index.tab=0,t.isNullOrUndefined(this._curFocus.tree)?t.isNullOrUndefined(this.model.pivotControl._curFocus.tab)||this.model.pivotControl._curFocus.tab.attr("tabindex","-1").focus().mouseover().addClass("e-hoverCell"):this._curFocus.tree.attr("tabindex","-1").focus().mouseover().addClass("e-hoverCell"))}else n("#"+this._id).find(".e-schemaFieldTree .e-hoverCell").length>0?n("#"+this._id).find(".e-schemaFieldTree .e-hoverCell").parent().find(".e-chkbox-small").click():n(".pivotTree:visible,.pivotTreeContext:visible,.pivotTreeContextMenu:visible").length>0&&(this._curFocus.button&&(o=this._curFocus.button.parent().attr("data-tag")),this.model.pivotControl._curFocus.button=n("#"+this._id).find("[data-tag='"+o+"'] button"),this.model.pivotControl._curFocus.button.attr("tabindex","-1").focus().addClass("e-hoverCell"));(n(i.target).hasClass("e-memberCurrentPage")||n(i.target).hasClass("searchEditorTreeView")&&n(i.target).parents(".e-dialog").find(".e-nextPageDiv").length>0)&&(n(i.target).hasClass("e-memberCurrentSearchPage")||n(i.target).hasClass("searchEditorTreeView")&&n(i.target).parents(".e-dialog").find(".e-nextPageDiv").length>0)||i.stopImmediatePropagation()}i.which===70&&i.ctrlKey&&(i.preventDefault(),n("#"+this._id).find(".treeDrop").length>0&&n("#"+this._id).find(".e-hoverCell").length>0&&n("#"+this._id).find(".e-hoverCell").parent().find(".treeDrop").click());i.keyCode===93&&n("#"+this._id).find(".e-hoverCell").length>0&&(i.preventDefault(),e={x:n(".e-hoverCell").offset().left+n(".e-hoverCell").outerWidth(),y:n(".e-hoverCell").offset().top+n(".e-hoverCell").outerHeight()},n(".e-hoverCell").trigger({type:"mouseup",which:3,clientX:e.x,clientY:e.y,pageX:e.x,pageY:e.y}));((i.which===79||i.which===67)&&i.ctrlKey||i.which===27)&&n("#"+this._id).find(".e-hoverCell").length>0&&(i.keyCode==79&&n("#"+this._id).find(".e-dialog").length>0&&i.ctrlKey&&(i.preventDefault(),n("#"+this._id).find(".e-dialogOKBtn:visible").click()),i.keyCode==67&&n("#"+this._id).find(".e-dialog").length>0&&i.ctrlKey&&(i.preventDefault(),n("#"+this._id).find(".e-dialogCancelBtn:visible").click()),this._index.node=0,n("#"+this._id).find(".e-hoverCell").removeClass("e-hoverCell"),t.isNullOrUndefined(this._curFocus.tree)?t.isNullOrUndefined(this._curFocus.button)?t.isNullOrUndefined(this.model.pivotControl._curFocus.tab)||this.model.pivotControl._curFocus.tab.attr("tabindex","-1").focus().mouseover().addClass("e-hoverCell"):this._curFocus.button.attr("tabindex","-1").focus().mouseover().addClass("e-hoverCell"):this._curFocus.tree.attr("tabindex","-1").focus().mouseover().addClass("e-hoverCell"))},_getLocalizedLabels:function(n){return t.isNullOrUndefined(t.PivotSchemaDesigner.Locale[this.locale()])||t.PivotSchemaDesigner.Locale[this.locale()][n]==r?t.PivotSchemaDesigner.Locale["en-US"][n]:t.PivotSchemaDesigner.Locale[this.locale()][n]},_setTableFields:function(n){t.isNullOrUndefined(n)||(this.model.pivotTableFields=n)},_setPivotRows:function(n){t.isNullOrUndefined(n)||(this.model.pivotRows=n)},_setPivotColumns:function(n){t.isNullOrUndefined(n)||(this.model.pivotColumns=n)},_setPivotCalculations:function(n){t.isNullOrUndefined(n)||(this.model.pivotCalculations=n)},_setFilters:function(n){t.isNullOrUndefined(n)||(this.model.filters=n)},_getSortedHeaders:function(){for(var i=this.element.find(".descending").parents("label"),r="",t=0;t<i.length;t++)r+=n(i[t]).attr("for").replace("toggleBtn","")+"##";return r},_contextOpen:function(i){var u,r;if(n(i.target.parentElement).find(".e-measureGroupCDB").length>0||n(i.target.parentElement).find(".e-folderCDB").length>0||!(this.element.parents(".e-pivotclient").length>0)&&n(i.target.parentElement).find(".e-dimensionCDB").length>0||!n(i.target).hasClass("e-text"))return!1;t.Pivot.openPreventPanel(this);this._selectedMember=n(i.target);r=n("#"+this._id+"_pivotTreeContext").data("ejMenu");r&&r.hideItems(["#"+this._id+"_e-remove"]);this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap?(n(i.target).find(".e-calcMemberCDB").length>0&&r.showItems(["#"+this._id+"_e-remove"]),n(i.target).parents("li:eq(0)").attr("data-tag").toLowerCase().indexOf("[measures]")>=0?(r.disableItem(this._getLocalizedLabels("AddToFilter")),r.disableItem(this._getLocalizedLabels("AddToRow")),r.disableItem(this._getLocalizedLabels("AddToColumn")),r.enableItem(this._getLocalizedLabels("AddToValues"))):(r.disableItem(this._getLocalizedLabels("AddToValues")),n(i.target.parentElement).find(".e-namedSetCDB").length>0?r.disableItem(this._getLocalizedLabels("AddToFilter")):r.enableItem(this._getLocalizedLabels("AddToFilter")),r.enableItem(this._getLocalizedLabels("AddToRow")),r.enableItem(this._getLocalizedLabels("AddToColumn")))):this._dataModel=="Pivot"&&(u=i.target.textContent,n(i.target).hasClass("e-text")&&(n(this.element).parents(".e-pivotclient").length>0||n.grep(this.model.pivotControl._calculatedField,function(n){return n.name==u}).length==0)?(r=n("#"+this._id+"_pivotTreeContext").data("ejMenu"),r.enable()):(r=n("#"+this._id+"_pivotTreeContext").data("ejMenu"),r.disable()))},_nodeDrag:function(i){return this._isDragging=!0,(n(i.dragTarget.parentElement).find(".e-measureGroupCDB").length>0||n(i.dragTarget.parentElement).find(".e-kpiRootCDB").length>0||n(i.dragTarget.parentElement).find(".e-folderCDB").length>0||n(i.dragTarget.parentElement).find(".e-dimensionCDB").length>0)&&!(n(i.dragTarget.parentElement).find(".e-dimensionCDB").length>0&&this.model.layout==t.PivotSchemaDesigner.Layouts.OneByOne)?!1:void 0},_treeContextClick:function(i){var e=this,r=n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejPivotClient"):null,u,f;t.isNullOrUndefined(this._curFocus.tree)?t.isNullOrUndefined(this.model.pivotControl._curFocus.tab)||this.model.pivotControl._curFocus.tab.attr("tabindex","-1").focus().addClass("e-hoverCell"):this._curFocus.tree.attr("tabindex","-1").focus().addClass("e-hoverCell");u=i.text==this._getLocalizedLabels("AddToColumn")?this.element.find(".e-schemaColumn"):i.text==this._getLocalizedLabels("AddToRow")?this.element.find(".e-schemaRow"):i.text==this._getLocalizedLabels("AddToValues")?this.element.find(".e-schemaValue"):i.text==this._getLocalizedLabels("AddToFilter")?this.element.find(".e-schemaFilter"):"";f={dropTarget:u,droppedElement:this._selectedMember.parent().parent(),target:u};t.isNullOrUndefined(r)?this.model.pivotControl._waitingPopup.show():(r._isTimeOut=!0,setTimeout(function(){r._isTimeOut&&e.model.pivotControl._waitingPopup.show()},800));this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this._nodeDropped(f):this._clientOnNodeDropped(f)},_checkedChange:function(t){var i=n(t.event.target).parents(".e-pivotschemadesigner").data("ejPivotSchemaDesigner"),r=n(".btnDeferUpdate").data("ejButton");t.isChecked?(i.model.pivotControl.model.enableDeferUpdate=!0,r.enable()):(i.model.pivotControl.model.enableDeferUpdate=!1,i.model.pivotControl._isUpdateRequired&&i.model.pivotControl._deferUpdate(),r.disable())},_sortBtnClick:function(i){var r;if(n(i.target).toggleClass("descending"),this.model.pivotControl._dataModel!="Olap"&&this.model.pivotControl._dataModel!="XMLA")if(this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode){t.PivotAnalysis._valueSorting=null;var f=n(i.target).parents(".e-pivotButton").attr("data-tag").split(":")[1],e=this.model.pivotControl.model.dataSource[n(i.target).parents(".e-pivotButton").attr("data-tag").split(":")[0].toLowerCase()],u=n.grep(e,function(n){return n.fieldName==f}),o=i.target.className.indexOf("descending")>=0?t.PivotAnalysis.SortOrder.Descending:t.PivotAnalysis.SortOrder.Ascending;for(r=0;r<u.length;r++)u[r].sortOrder=o;this.model.pivotControl._populatePivotGrid()}else this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this.model.pivotControl._sortBtnClick(i)},_generateMembers:function(i,u){var s=n(u).find("Axis:eq(0) Tuple"),e=[],a={},o,h,f,l,v;for(e.push({id:"All",name:"All",checkedStatus:!0,tag:""}),o=t.Pivot.getReportItemByFieldName(this._selectedFieldName,this.model.pivotControl.model.dataSource).item,h=!t.isNullOrUndefined(o)&&!t.isNullOrUndefined(o.filterItems)?o.filterItems.values:[],f=0;f<s.length;f++){var c=n(n(u).find("Axis:eq(0) Tuple:eq("+f+")").children().children()[0]).text(),y=n(n(u).find("Axis:eq(0) Tuple:eq("+f+")").children().children()[1]).text()==""?"(Blank)":n(n(u).find("Axis:eq(0) Tuple:eq("+f+")").children().children()[1]).text(),p=h.length>0?n.inArray(c.replace("&","&amp;"),h)>-1?!0:!1:!0;a={hasChildren:n(s[f]).find("CHILDREN_CARDINALITY").text()!="0",checkedStatus:p,id:c.replace(/\]*\]/g,"-").replace(/\[*\[/g,"-").replace(/ /g,"_"),name:y,tag:c,level:parseInt(n(s[f]).find("LNum").text())};e.push(a)}t.isNullOrUndefined(this._waitingPopup)||(l=n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejPivotClient"):null,t.isNullOrUndefined(l)||(l._isTimeOut=!1),this._waitingPopup.hide());this._editorTreeData=e;this.model.pivotControl._currentReportItems.push({filterItems:e,fieldName:this._selectedFieldName,dataSrc:this.model.pivotControl.model.dataSource,pageSettings:this._memberCount});!t.isNullOrUndefined(this.model.pivotControl)&&this.model.pivotControl.model.enableMemberEditorPaging?(this._memberCount=t.DataManager(this._editorTreeData).executeLocal(t.Query().where(t.Predicate("pid","equal",null).and("id","notequal","All"))).length,v=t.DataManager(this._editorTreeData).executeLocal(t.Query().where("pid","equal",null||r).page(this._memberPageSettings.currentMemeberPage,this.model.pivotControl.model.memberEditorPageSize+1)),this._fetchMemberSuccess({EditorTreeInfo:JSON.stringify(v)})):this._fetchMemberSuccess({EditorTreeInfo:JSON.stringify(e)})},_generateChildMembers:function(i,r){for(var e=n(r).find("Axis:eq(0) Tuple"),o=[],u=this.element.find("[data-tag='"+i.currentNode.replace(/&amp;/g,"&")+"']"),f=0;f<e.length;f++){var s=n(n(r).find("Axis:eq(0) Tuple:eq("+f+")").children().children()[0]).text(),h=n(n(r).find("Axis:eq(0) Tuple:eq("+f+")").children()).find("Caption").text(),c={hasChildren:n(e[f]).find("CHILDREN_CARDINALITY").text()!="0",checkedStatus:!1,id:s.replace(/\]*\]/g,"-").replace(/\[*\[/g,"-"),name:h,tag:s,level:n(e[f]).find("LNum").text()};o.push(c)}parentNode=n(u).parents("li").length>1?n(u).parents("li").first():n(u).parents("li");u.find(".e-load").removeClass("e-load");n(n(parentNode).find("input.nodecheckbox")[0]).ejCheckBox({checked:!1});n.each(u.find("li"),function(n,t){t.setAttribute("data-tag",o[n].tag)});this._memberTreeObj=n(".e-editorTreeView").data("ejTreeView");this._memberTreeObj.addNode(t.Pivot._showEditorLinkPanel(o,this,this.model.pivotControl),n(u))},_sortField:function(i){if(t.Pivot.closePreventPanel(this),this.element.find(".e-dialog, .e-clientDialog").remove(),n(i.element).attr("id")=="descOrder"||n(i.element).attr("id")=="ascOrder"){var r=t.Pivot.getReportItemByFieldName(this._selectedFieldName,this.model.pivotControl.model.dataSource).item;t.isNullOrUndefined(r)||(r.sortOrder=n(i.element).attr("id")=="descOrder"?t.olap.SortOrder.Descending:t.olap.SortOrder.Ascending);this.model.pivotControl&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot?this.model.pivotControl.refreshControl():(t.olap.base._clearDrilledCellSet(),t.olap.base.getJSONData({action:"sorting"},this.model.pivotControl.model.dataSource,this.model.pivotControl))}},_clientOnFilterBtnClick:function(i){var y,s,a,u,v,e,f,h,l,c,o;if(t.Pivot.openPreventPanel(this),y=this,this._isAllMemberChecked=!0,s=n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejPivotClient"):null,this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap){if(n(i.target).parents().hasClass("e-pivotButton")&&n(i.target).parents(".e-pivotButton").attr("data-tag").indexOf(":[")>=0&&!(n(i.target).parents(".e-pivotButton").attr("data-tag").toLowerCase().indexOf("[measures]")>=0))this._selectedFieldName=n(i.target).parents(".e-pivotButton").attr("data-tag").split(":")[1];else{if(n(i.target).parents().attr("data-tag")!=null&&(n(i.target).parents().attr("data-tag").split(":")[1]=="Measures"||n(i.target).parent().attr("data-tag").toLowerCase().indexOf("[measures]")>=0))return t.Pivot.closePreventPanel(this),t.isNullOrUndefined(s)||(s._isTimeOut=!1,u=n(i.target).parents().hasClass("e-pivotButton")&&n(i.target).parents(".e-pivotButton").attr("data-tag")?n(i.target).parents(".e-pivotButton").attr("data-tag").split(":")[1]:"",u&&(u=u.split("].[").length>1?u.split("].[")[1].replace("]",""):"",e=n.map(this.model.pivotControl.model.calculatedMembers,function(n){if(u.indexOf("[")>-1&&u.indexOf(n.caption)>-1||n.caption==u)return n}),e.length>0&&(s._selectedCalcMember=e[0].caption,f=this.element.find(".e-schemaFieldTree").data("ejTreeView").model.fields.dataSource,h=[],f=n.grep(f,function(n){return n.id&&(n.id=n.id.replace(/\]/g,"_").replace(/\[/g,"_").replace(/\./g,"_").replace(/ /g,"_")),n.pid&&(n.pid=n.pid.replace(/\]/g,"_").replace(/\[/g,"_").replace(/\./g,"_").replace(/ /g,"_")),n.spriteCssClass.indexOf("e-level")>-1&&h.push({id:n.id+"_1",pid:n.id,name:"(Blank)",hasChildren:!1,spriteCssClass:""}),n}),f=n.merge(h,f),this._selectedFieldName=n(i.currentTarget).attr("data-fieldname"),l={CubeTreeInfo:JSON.stringify(f)},t.Pivot._createCalcMemberDialog(l,this.model.pivotControl))),clearTimeout(null),y._waitingPopup.hide()),!1;this._selectedLevel=n(n(i.target).parents("li:eq(0)")).attr("data-tag");this._selectedFieldName=n(i.target).parents("li:eq(0)").children("div:eq(0)").find(".levels").length>0?n(n(i.target).parents("li:eq(1)")).attr("data-tag"):n(n(i.target).parents("li:eq(0)")).attr("data-tag")}if(a=!1,t.isNullOrUndefined(s)?this._waitingPopup.show():(u=n(i.target).parents().hasClass("e-pivotButton")&&n(i.target).parents(".e-pivotButton").attr("data-tag")?n(i.target).parents(".e-pivotButton").attr("data-tag").split(":")[1]:"",e=n.map(this.model.pivotControl.model.calculatedMembers,function(n){if(u.indexOf("[")>-1&&u.indexOf(n.caption)>-1||n.caption==u)return n}),e.length>0&&(a=!0,this._selectedFieldName=u),a||(s._isTimeOut=!0,setTimeout(function(){s._isTimeOut&&y._waitingPopup.show()},800))),t.isNullOrUndefined(this._selectedFieldName)||this._selectedFieldName.toLocaleLowerCase().indexOf("measures")>=0)return!1;var b=this._selectedFieldName,w=this.model.pivotControl.model.dataSource,p,o=n.map(this.model.pivotControl._currentReportItems,function(i){if(i.fieldName==b&&(t.isNullOrUndefined(i.dataSrc)||i.dataSrc.cube==w.cube&&i.dataSrc.reportName==w.reportName)&&(p=i.pageSettings,!t.isNullOrUndefined(i.filterItems)))return n.map(i.filterItems,function(n){return n.expanded&&(n.expanded=!1),n})});if(this.model.pivotControl.model.enableMemberEditorPaging&&(p&&(this._memberCount=p),this._memberPageSettings.endPage=this.model.pivotControl.model.memberEditorPageSize,this._memberPageSettings.startPage=0,this._memberPageSettings.currentMemeberPage=1),o.length>0&&!a)this._editorTreeData=o,this.model.pivotControl.model.enableMemberEditorPaging&&(this._memberCount=t.DataManager(this._editorTreeData).executeLocal(t.Query().where(t.Predicate("pid","equal",null).and("id","notequal","All"))).length,o=t.DataManager(this._editorTreeData).executeLocal(t.Query().where("pid","equal",null||r).page(this._memberPageSettings.currentMemeberPage,this.model.pivotControl.model.memberEditorPageSize+1))),this._fetchMemberSuccess({EditorTreeInfo:JSON.stringify(o)});else if(v=this._selectedFieldName,n(this.element).parents(".e-pivotclient").length>0&&this.model.pivotControl.model.calculatedMembers.length>0){if(e=n.map(this.model.pivotControl.model.calculatedMembers,function(n){if(v.indexOf("[")>-1&&v.indexOf(n.caption)>-1||n.caption==v)return n}),e.length>0)return this._selectedFieldName=n(i.target).parents().hasClass("e-pivotButton")&&n(i.target).parents(".e-pivotButton").attr("data-tag")?n(i.target).parents(".e-pivotButton").attr("data-tag").split(":")[1]:"",this.model.pivotControl._selectedCalcMember=e[0].caption,f=this.element.find(".e-schemaFieldTree").data("ejTreeView").model.fields.dataSource,h=[],f=n.grep(f,function(n){return n.id&&(n.id=n.id.replace(/\]/g,"_").replace(/\[/g,"_").replace(/\./g,"_").replace(/ /g,"_")),n.pid&&(n.pid=n.pid.replace(/\]/g,"_").replace(/\[/g,"_").replace(/\./g,"_").replace(/ /g,"_")),n.spriteCssClass.indexOf("e-level")>-1&&h.push({id:n.id+"_1",pid:n.id,name:"(Blank)",hasChildren:!1,spriteCssClass:""}),n}),f=n.merge(h,f),this._selectedFieldName=n(i.currentTarget).attr("data-fieldname"),l={CubeTreeInfo:JSON.stringify(f)},t.Pivot._createCalcMemberDialog(l,this.model.pivotControl),!1;t.olap._mdxParser.getMembers(this.model.pivotControl.model.dataSource,this._selectedFieldName,this)}else t.olap._mdxParser.getMembers(this.model.pivotControl.model.dataSource,this._selectedFieldName,this)}else this._selectedFieldName=(n(i.target).parent().hasClass("e-pivotButton")||n(i.target).parent().hasClass("pvtBtnDiv"))&&n(i.target).parent().attr("data-tag").indexOf(":")>=0?n.grep(this.model.pivotTableFields,function(t){return t.name==n(i.target).parent().attr("data-tag").split(":")[1]})[0].name:n.grep(this.model.pivotTableFields,function(t){return t.id.replace(/ /g,"_")==n(n(i.target).closest("li")).attr("id")})[0].name;c=n(this.element.find(".e-pivotButton button[data-fieldName='"+this._selectedFieldName+"']"));this._dialogHead=this._selectedFieldCaption=c.attr("data-fieldCaption")||c.attr("data-fieldName");this._selectedFieldAxis=c.parents().hasClass("e-schemaRow")?"rows":c.parents().hasClass("e-schemaColumn")?"columns":c.parents().hasClass("e-schemaValue")?"values":"filters";this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&(this.model.pivotControl.model.enableMemberEditorPaging?(this._memberPageSettings.endPage=this.model.pivotControl.model.memberEditorPageSize,this._memberPageSettings.startPage=0,this._memberPageSettings.currentMemeberPage=1,o=this._getTreeViewData(),this._editorTreeData=o,this._memberCount=t.DataManager(this._editorTreeData).executeLocal(t.Query().where("id","notequal","All")).length,o=t.DataManager(this._editorTreeData).executeLocal(t.Query().page(this._memberPageSettings.currentMemeberPage,this.model.pivotControl.model.memberEditorPageSize+1)),this._fetchMemberSuccess({EditorTreeInfo:JSON.stringify(o)})):this._fetchMemberSuccess({EditorTreeInfo:JSON.stringify(this._getTreeViewData())}))},_getTreeViewData:function(){var s=this.model.pivotControl.model.dataSource[this._selectedFieldAxis],o=this._selectedFieldName,f=n.grep(s,function(n){return n.fieldName==o})[0],u=t.PivotAnalysis.getMembers(o),e=[{id:"All",name:"All",checkedStatus:!0}],i;if(f.filterItems!=null&&f.filterItems!=r)for(i=0;i<u.length;i++)e.push({id:u[i],name:u[i],checkedStatus:f.filterItems.filterType==t.PivotAnalysis.FilterType.Include?n.inArray(u[i],f.filterItems.values)>=0:n.inArray(u[i],f.filterItems.values)<0});else for(i=0;i<u.length;i++)e.push({id:u[i],name:u[i],checkedStatus:!0});return e},_filterBtnClick:function(i){var y,o,e,h,p,a,w,c,u,r,s,f,v,k,l,b;if(this.model.pivotControl._editorFlag=!1,this._isAllMemberChecked=!0,t.Pivot.openPreventPanel(this),y=this,o=n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejPivotClient"):null,this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode){for(this.model.pivotControl._dataModel=="XMLA"?n(i.target).parents("li:eq(0)").length>0?(this._selectedLevel=n(n(i.target).parents("li:eq(0)")).attr("data-tag"),e=n(i.target).parents("li:eq(0)").children("div:eq(0)").find(".levels").length>0?n(n(i.target).parents("li:eq(1)")).attr("data-tag"):n(n(i.target).parents("li:eq(0)")).attr("data-tag")):(this._selectedLevel=n(i.target).parent().attr("data-tag").split(":")[1],e=n(i.target).parent().attr("data-tag").split(":")[1]):e=n.grep(this.model.pivotTableFields,function(t){return t.caption==n(n(i.target).parent()).text()})[0].name,r=0;r<this.element.find(".e-pivotButton").length;r++)if(uniqueName=n(this.element.find(".e-pivotButton")[r]).find("button").attr("data-fieldName"),uniqueName==e){h=n(this.element.find(".e-pivotButton")[r]).find("button").text();this._selectedAxis=n(this.element.find(".e-pivotButton")[r]).parent()[0].className.split(" ")[0];break}this._selectedAxis=this.model.pivotControl._selectedAxis=this._selectedAxis=="e-schemaRow"?"rows":this._selectedAxis=="e-schemaColumn"?"columns":this._selectedAxis=="e-schemaValue"?"values":"filters";this._dialogTitle=this.model.pivotControl._dialogTitle=h;this._dialogHead=this.model.pivotControl._dialogHead=h;this._selectedMember=this.model.pivotControl._selectedField=e;this.model.pivotControl._dataModel=="XMLA"?(p=n(i.target).parents("li:eq(0)").length>0?n(i.currentTarget).parents("li:eq(0)").attr("data-tag"):n(i.target).parent().attr("data-tag").split(":")[1],a=n.map(this.model.pivotControl._currentReportItems,function(t){if(t.fieldName==p)return n(t.filterItems,function(n){return n.expanded&&(n.expanded=!1),n})}),a.length>0?(this._fetchMemberSuccess({EditorTreeInfo:JSON.stringify(a)}),this._editorTreeData=filterData):this._selectedMember=e):(w=this.model.pivotControl._getTreeViewData(e,h,this._selectedAxis),this._fetchMemberSuccess({EditorTreeInfo:JSON.stringify(w)}))}else{if(n(this._tableTreeObj.element).find("span.e-icon.treeDrop").css({display:"none"}),this._dataModel=="Olap"){if(n(i.target).attr("class").indexOf("filter")>-1){for(r=0;r<this.model.pivotTableFields.length;r++)(this.model.pivotTableFields[r].tag.replace(/\[/g,"").replace(/\]/g,"").indexOf("Measures")>-1&&this.model.pivotTableFields[r].isSelected==!0||this.model.pivotTableFields[r].isSelected==!0&&n(i.target.parentElement).attr("data-tag").split(":")[1]==this.model.pivotTableFields[r].tag.replace(/\[/g,"").replace(/\]/g,""))&&(c=this.model.pivotTableFields[r].spriteCssClass);for(s=0;s<this._tableTreeObj.element.find("li").length;s++)n(n(i.target).parent()).attr("data-tag").split(":")[1]==n(this._tableTreeObj.element.find("li")[s]).attr("data-tag").replace(/\]/g,"").replace(/\[/g,"")&&(u=n(this._tableTreeObj.element.find("li")[s]))}else c=n(i.target).parent().find("a>span").attr("class");n(i.target).attr("class").indexOf("filter")>-1||(u=c.indexOf("e-hierarchyCDB")>-1||c.indexOf("e-attributeCDB")>-1?n(i.target).parents("li")[0]:n(i.target).parents("li").attr("data-tag")=="KPI"?n(i.target).parents("li"):n(i.target).parents("li")[1])}else u=n(i.target).attr("class").indexOf("filter")>-1?this._tableTreeObj.element.find("li:contains('"+n(n(i.target).parent()).attr("data-tag").split(":")[1]+"')"):n(n(i.target).parent()).parent();if(this._selectedTreeNode=u,i.target.tagName.toLowerCase()=="span"||i.target.tagName.toLowerCase()=="button"){if(this._curFilteredText=this.model.layout!="excel"&&n(i.target).attr("class").indexOf("filter")>-1?n(n(i.target).parents(".e-pivotButton")).attr("data-tag").split(":")[1].replace(/\]/g,"").replace(/\[/g,""):this._dataModel=="Olap"?n(u).attr("data-tag").replace(/\]/g,"").replace(/\[/g,""):n(u).attr("id"),n(this.element).parents(".e-pivotclient").length>0&&(this._curFilteredText=n(n(i.target).parents(".e-pivotButton")).attr("data-tag").split(":")[1].replace(/\]/g,"").replace(/\[/g,"")),n(i.target).parents("li:eq(1)").find("div:first>a>span").hasClass("e-hierarchyCDB")&&(this._curFilteredText=n(i.target).parents("li:eq(1)").find("div:first>a").text()),this._dataModel=="Pivot"&&(n(this.element).parents(".e-pivotclient").length>0?(this._curFilteredAxis=n(u)[0].className.split(" ")[0],this.model.pivotControl._curFilteredText=this._curFilteredText):this._curFilteredAxis=t.isNullOrUndefined(this.element.find(".e-btn:contains('"+(n(this.element).parents(".e-pivotclient").length>0?n(u).text():n(u).find("div:first").text())+"')").parents(".e-droppable")[0])?"":this.element.find(".e-btn:contains('"+n(u).find("div:first").text()+"')").parents(".e-droppable")[0].className.split(" ")[0],this._dialogHead=n(i.target).attr("data-fieldCaption")||this._curFilteredText),this._dialogTitle=this._dataModel=="Olap"?n(this.element.find(".e-pivotButton:contains('"+n(u).find("div:first").text()+"')")[0]).attr("data-tag"):n(i.target).parents("li").length>0?n(i.target).parents("li").attr("id")||i.target.id.replace("filterBtn","")||n(i.target).parents(".filterBtn")[0].id.replace("filterBtn",""):n(i.target).parent().attr("data-tag").split(":")[1]||i.target.id.replace("filterBtn","")||n(i.target).parents(".filterBtn")[0].id.replace("filterBtn",""),n(this.element).parents(".e-pivotclient").length>0&&n(u)[0].className.split(" ")[0]=="e-schemaFilter"&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&(this._dialogTitle=this._curFilteredText),this._dataModel=="Olap")for(f=0;f<this.element.find(".e-btn").length;f++)if(!t.isNullOrUndefined(n(n(n(this.element.find(".e-btn"))[f]).parent()[0]).attr("data-tag"))&&n(n(n(this.element.find(".e-btn"))[f]).parent()[0]).attr("data-tag").split(":")[1]==n(this._selectedTreeNode).attr("data-tag").replace(/\]/g,"").replace(/\[/g,"")&&n(n(n(this.element.find(".e-btn"))[f]).parent()[0]).attr("data-tag").indexOf(this._curFilteredText)>-1){this._curFilteredAxis=t.isNullOrUndefined(n(n(this.element.find(".e-btn"))[0]).parents(".e-droppable")[0])?"":n(n(this.element.find(".e-btn"))[f]).parents(".e-droppable")[0].className.split(" ")[0];this._dialogTitle=n(n(n(this.element.find(".e-btn"))[f]).parent()[0]).attr("data-tag");this._dialogHead=n(n(n(this.element.find(".e-btn"))[f])[0]).attr("data-fieldCaption")||n(n(n(this.element.find(".e-btn"))[f])[0]).attr("title");break}try{v=JSON.parse(this.model.pivotControl.getOlapReport()).Report}catch(d){t.isNullOrUndefined(this.model.pivotControl.getOlapReport)||(v=this.model.pivotControl.getOlapReport())}k=this._getSortedHeaders();this._isFilterBtnClick=!0;l=t.isNullOrUndefined(o)?this:o;l.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&l._trigger("beforeServiceInvoke",{action:"fetchMembers",element:l.element,customObject:this.model.pivotControl.model.customObject});b=JSON.stringify({action:"fetchMembers",headerTag:this._dialogTitle+(this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.pivotControl.model.enableAdvancedFilter?"##true":"")||"UniqueName##"+n(u).attr("data-tag"),sortedHeaders:this.model.pivotControl._ascdes,currentReport:v,valueSorting:JSON.stringify(this.model.pivotControl.model.valueSortSettings),customObject:JSON.stringify(this.model.pivotControl.model.customObject)});t.isNullOrUndefined(o)?this.model.pivotControl._waitingPopup.show():(o._isTimeOut=!0,setTimeout(function(){o._isTimeOut&&y.model.pivotControl._waitingPopup.show()},800));this.element.find(".schemaNoClick").addClass("freeze").width(n(this.element).width()).height(n(this.element).height()).css({top:n(this.element).offset().top,left:n(this.element).offset().left});this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.fetchMembers,b,this.model.pivotControl.model.enableMemberEditorPaging?t.Pivot._fetchMemberPageSuccess:this._fetchMemberSuccess)}}},_removeBtnClick:function(i){var v=this,r=n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejPivotClient"):null,h,f,a,c,u,e,o,l,s;if(t.isNullOrUndefined(r)?this.model.pivotControl._waitingPopup.show():(r._isTimeOut=!0,setTimeout(function(){r._isTimeOut&&v.model.pivotControl._waitingPopup.show()},800)),this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode)t.PivotAnalysis._valueSorting=null,h=n(i.target).parent().attr("data-tag").split(":")[1],t.Pivot.removeReportItem(this.model.pivotControl.model.dataSource,h,h.toLocaleLowerCase().indexOf("measures")==0),this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(this.model.pivotControl._fieldMembers={},this.model.pivotControl._fieldSelectedMembers={},t.olap.base.clearDrilledItems(this.model.pivotControl.model.dataSource,{action:"nodeDropped"},this.model.pivotControl)),this.model.pivotControl.element.hasClass("e-pivotclient")&&this.model.pivotControl._pivotChart&&(this.model.pivotControl._pivotChart._labelCurrentTags={}),this._refreshPivotButtons(),this.model.pivotControl.refreshControl(),n(i.target).parent().remove();else{for(f=n(i.target.parentElement).find("button").attr("data-fieldName")||n(i.target.parentElement).text(),c=n(n(this._tableTreeObj.element).find("div:contains("+f+")>span")[1]).find(".e-chk-act"),n(c).removeClass("e-chk-act").addClass("e-chk-inact"),n(c).children("e-checkmark").removeClass("e-checkmark"),u=0;u<this.model.pivotTableFields.length;u++)this.model.pivotTableFields[u].id==f&&(this.model.pivotTableFields[u].IsSelected=!1,a=this.model.pivotTableFields[u]);if(n(i.target.parentElement).remove(),n(this.element).parents(".e-pivotclient").length>0&&(e=n(i.target).parent().attr("data-tag"),delete this.model.pivotControl._fieldMembers[e.split(":")[e.split(":").length-1]],delete this.model.pivotControl._fieldSelectedMembers[e.split(":")[e.split(":").length-1]]),this._clearFilterData(f),o=t.isNullOrUndefined(r)?this:r,o.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&o._trigger("beforeServiceInvoke",{action:"nodeStateModified",element:o.element,customObject:this.model.pivotControl.model.customObject}),this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&n(this.element).parents(".e-pivotclient").length>0){try{l=JSON.parse(this.model.pivotControl.getOlapReport()).Report}catch(y){t.isNullOrUndefined(this.model.pivotControl.getOlapReport)||(l=this.model.pivotControl.getOlapReport())}s=JSON.stringify({action:"removeButton",args:JSON.stringify({headerTag:f,currentReport:l,sortedHeaders:t.isNullOrUndefined(this.model.pivotControl._ascdes)?"":this.model.pivotControl._ascdes})+"-##-"+JSON.stringify(this.model.pivotControl.model.valueSortSettings),customObject:JSON.stringify(this.model.pivotControl.model.customObject)});this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeDropped,s,this._droppedSuccess)}else s=JSON.stringify({action:"nodeStateModified",headerTag:JSON.stringify(a),sortedHeaders:this._getSortedHeaders(),currentReport:this.model.pivotControl.getOlapReport(),customObject:JSON.stringify(this.model.pivotControl.model.customObject)}),this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeStateModified,s,this._nodeStateModifiedSuccess)}t.isNullOrUndefined(this.model.pivotControl._waitingPopup)||t.isNullOrUndefined(r)||(r._isTimeOut=!1)},_nodeCheckChanges:function(i){var f,s,e,h;if(this._onDemandNodeExpand){this._isFiltered=!0;var o=this,u=this.element.find(".e-editorTreeView"),c=null,l=!t.isNullOrUndefined(i.model.id)&&i.model.id.toLowerCase()==this._id.toLowerCase()+"_allelement"&&!i.isChecked||!t.isNullOrUndefined(i.id)&&(i.id.toLowerCase()=="(all)_0"||i.id.toLowerCase()=="all")&&!t.isNullOrUndefined(i.type)&&i.type=="nodeUncheck",a=!t.isNullOrUndefined(i.model.id)&&i.model.id.toLowerCase()==this._id.toLowerCase()+"_allelement"&&i.isChecked||!t.isNullOrUndefined(i.id)&&(i.id.toLowerCase()=="(all)_0"||i.id.toLowerCase()=="all")&&!t.isNullOrUndefined(i.type)&&i.type=="nodeCheck";if(l){if(this._memberTreeObj.model.nodeCheck="",this._memberTreeObj.model.nodeUncheck="",this._isOptionSearch?(c=n(u).find("li[data-isItemSearch='true']"),c.length>0&&n(c).each(function(t,i){n(u).ejTreeView("uncheckNode",n(i))})):!t.isNullOrUndefined(this.model.pivotControl)&&this.model.pivotControl.model.enableMemberEditorPaging&&n(u).find("li span.e-searchfilterselection").length>0?(n(u).ejTreeView("unCheckAll"),this._isSelectSearchFilter?n(u).find("li span.e-searchfilterselection").closest("li").find("span.e-chk-image").removeClass("e-stop").addClass("e-checkmark"):n(u).find("li span.e-searchfilterselection").closest("li").find("span.e-chk-image").removeClass("e-checkmark").removeClass("e-stop")):n(u).ejTreeView("unCheckAll"),this._memberTreeObj.model.nodeCheck=t.proxy(this._nodeCheckChanges,this),this._memberTreeObj.model.nodeUncheck=t.proxy(this._nodeCheckChanges,this),n(u).find("li").length>0&&!n(u).find("li").find("span.e-checkmark").length>0?this._dialogOKBtnObj.disable():n(u).find("li").length>0&&n(u).find("li").find("span.e-checkmark").length==1&&n(u).find("li span.e-searchfilterselection").closest("li").find("span.e-checkmark").length>0&&this._dialogOKBtnObj.disable(),this.model.pivotControl&&this.model.pivotControl.model.enableMemberEditorPaging||this._editorTreeData.length>0){if(this.element.find(".e-dialogOKBtn").attr("disabled","disabled"),this._isAllMemberChecked=!1,this._editorSearchTreeData.length>0)for(f=0;f<this._editorSearchTreeData.length;f++)n(this._editorTreeData).each(function(n,t){if(o._editorSearchTreeData[f].id==t.id)return o._editorSearchTreeData[f].checkedStatus=!1,t.checkedStatus=!1,!1});else n(this._editorTreeData).each(function(n,t){t.checkedStatus=!1});n(this._editorTreeData).each(function(n,t){if(t.checkedStatus)return o._dialogOKBtnObj.enable(),o.element.find(".e-dialogOKBtn").removeAttr("disabled"),!1})}}else if(a){if(this._memberTreeObj.model.nodeCheck="",this._memberTreeObj.model.nodeUncheck="",this._isOptionSearch?(c=n(u).find("li[data-isItemSearch='true']"),c.length>0&&n(c).each(function(t,i){n(u).ejTreeView("checkNode",n(i))})):!t.isNullOrUndefined(this.model.pivotControl)&&this.model.pivotControl.model.enableMemberEditorPaging&&n(u).find("li span.e-searchfilterselection").length>0?(n(u).ejTreeView("checkAll"),this._isSelectSearchFilter?n(u).find("li span.e-searchfilterselection").closest("li").find("span.e-chk-image").removeClass("e-stop").addClass("e-checkmark"):n(u).find("li span.e-searchfilterselection").closest("li").find("span.e-chk-image").removeClass("e-checkmark").removeClass("e-stop")):n(u).ejTreeView("checkAll"),n(this._memberTreeObj.element.find("li:first")).find("span.e-chk-image").removeClass("e-stop"),this._memberTreeObj.model.nodeCheck=t.proxy(this._nodeCheckChanges,this),this._memberTreeObj.model.nodeUncheck=t.proxy(this._nodeCheckChanges,this),n(u).find("li").length>0&&n(u).find("li").find("span.e-checkmark").length>0&&this._dialogOKBtnObj.enable(),this.model.pivotControl&&this.model.pivotControl.model.enableMemberEditorPaging||this._editorTreeData.length>0)if(this.element.find(".e-dialogOKBtn").removeAttr("disabled"),this._isAllMemberChecked=!0,this._editorSearchTreeData.length>0)for(f=0;f<this._editorSearchTreeData.length;f++)n(this._editorTreeData).each(function(n,t){if(o._editorSearchTreeData[f].id==t.id)return o._editorSearchTreeData[f].checkedStatus=!0,t.checkedStatus=!0,!1});else n(this._editorTreeData).each(function(n,t){t.checkedStatus=!0})}else i.id.toLowerCase()=="searchfilterselection"?t.Pivot._updateSearchFilterSelection(i,u,o):(t.isNullOrUndefined(i.model.id)||i.model.id.toLowerCase()!=this._id.toLowerCase()+"_allelement")&&(t.isNullOrUndefined(i.id)||i.id.toLowerCase()!="(all)_0"&&i.id.toLowerCase()=="all")||t.isNullOrUndefined(this.model.pivotControl)||!(this.model.pivotControl.model.enableMemberEditorPaging||this._editorTreeData.length>0)?(t.isNullOrUndefined(i.model.id)||i.model.id.toLowerCase()!=this._id.toLowerCase()+"_allElement")&&(t.isNullOrUndefined(i.id)||i.id.toLowerCase()!="(all)_0"&&i.id.toLowerCase()=="all")||(e=this._memberTreeObj.element.find(":input:gt(0).nodecheckbox:not(:checked)"),h=n(this._memberTreeObj.element.find("li:first")).find("span.e-chk-image"),e.length==0||e.length==1&&e[0].id[e[0].id.length-1]==0?(n(h).parent().removeClass("e-chk-inact").removeClass("e-chk-ind").addClass("e-chk-act"),h.removeClass("e-stop").addClass("e-checkmark"),this.element.find(".e-checkAllBox").find("span.e-chk-image").removeClass("e-stop").addClass("e-checkmark")):i.type=="nodeCheck"&&e.length==1&&e[0].id[e[0].id.length-1]==0?(h.removeClass("e-stop").addClass("e-checkmark"),this.element.find(".e-checkAllBox").find("span.e-chk-image").removeClass("e-stop").addClass("e-checkmark")):e.length>0&&(h.removeClass("e-checkmark").addClass("e-stop"),this.element.find(".e-checkAllBox").find("span.e-chk-image").removeClass("e-checkmark").addClass("e-stop")),this._dialogOKBtnObj.enable(),i.type=="nodeUncheck"&&(e.length+1==this._memberTreeObj.element.find("li").length||this._isOptionSearch&&n(u).find("li span.e-searchfilterselection").closest("li").find("span.e-checkmark").length>0&&this._memberTreeObj.element.find(":input:gt(0).nodecheckbox:checked").length==1)&&(h.removeClass("e-checkmark").removeClass("e-stop"),this.element.find(".e-checkAllBox").find("span.e-chk-image").removeClass("e-checkmark").removeClass("e-stop"),this._dialogOKBtnObj.disable())):i.type=="nodeUncheck"?(this.model.pivotControl._isMembersFiltered=!0,s="",t.Pivot._memberPageNodeUncheck(i,this),s=t.DataManager(this._editorTreeData).executeLocal(t.Query().where("pid","equal",null||r).where("checkedStatus","equal",!0)),!t.isNullOrUndefined(s)&&s.length>0&&(s[0].id=="All"||s[0].id=="(All)_0")&&s.splice(0,1),h=n(this._memberTreeObj.element.find("li:first")).find("span.e-chk-image"),t.isNullOrUndefined(s)||s.length!=0?(n(this._memberTreeObj.element.find("li:first")).find("span.e-chk-image").removeClass("e-stop").addClass("e-checkmark"),this.element.find(".e-checkAllBox").find("span.e-chk-image").removeClass("e-stop").addClass("e-checkmark"),n(this._editorTreeData).each(function(t,i){if(i.checkedStatus)return this._isAllMemberChecked=!1,n(o._memberTreeObj.element.find("li:first")).find("span.e-chk-image").removeClass("e-checkmark").addClass("e-stop"),!1})):(this._dialogOKBtnObj.disable(),this.element.find(".e-dialogOKBtn").attr("disabled","disabled"),this._isAllMemberChecked=!1,h.removeClass("e-stop").removeClass("e-checkmark").addClass("e-chk-inact"),this.element.find(".e-checkAllBox").find("span.e-chk-image").removeClass("e-stop").removeClass("e-checkmark").addClass("e-chk-inact"))):i.type=="nodeCheck"&&(this.model.pivotControl._isMembersFiltered=!0,t.Pivot._memberPageNodeCheck(i,this),this.element.find(".e-dialogOKBtn").data("ejButton").enable(),this.element.find(".e-dialogOKBtn").removeAttr("disabled"),this._isAllMemberChecked=!0,n(this._memberTreeObj.element.find("li:first")).find("span.e-chk-image").removeClass("e-stop").addClass("e-checkmark"),this.element.find(".e-checkAllBox").find("span.e-chk-image").removeClass("e-stop").addClass("e-checkmark"),n(this._editorTreeData).each(function(t,i){if(!i.checkedStatus)return this._isAllMemberChecked=!1,n(o._memberTreeObj.element.find("li:first")).find("span.e-chk-image").removeClass("e-checkmark").addClass("e-stop"),!1}))}},_pivotCheckedStateModified:function(i){var f,p,a,w,v,u;if(this._nodeCheck==!0)return this._nodeCheck=!1,!1;if(this._isDropAction)return!1;t.PivotAnalysis._valueSorting=null;var e=this.model.pivotControl._dataModel=="XMLA"?n(i.currentElement).attr("data-tag"):this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode?n.grep(this.model.pivotTableFields,function(t){return t.caption==n(i.currentElement).find("a")[0].textContent})[0].name:n(i.currentElement).find("a")[0].textContent,s=n(i.currentElement).find("a")[0].textContent,h="",c="",y="",l="",o=this.model.pivotControl._dataModel=="XMLA"?"XMLA":"",h=[];for(u=0;u<this.element.find(".e-pivotButton").length;u++)if(y=n(this.element.find(".e-pivotButton")[u]).find("button").attr("data-fieldName"),y==e&&(h.push(this.element.find(".e-pivotButton")[u]),o=="XMLA"))break;for(u=0;u<this.model.pivotTableFields.length;u++)this.model.pivotTableFields[u].name==e&&i.type=="nodeCheck"?(this.model.pivotTableFields[u].isSelected=!0,c=this.model.pivotTableFields[u]):this.model.pivotTableFields[u].name==e&&i.type=="nodeUncheck"&&(this.model.pivotTableFields[u].isSelected=!1,c=this.model.pivotTableFields[u]);if(i.type=="nodeUncheck")if(h.length>1)for(u=0;u<h.length;u++)n(h[u]).find("button").attr("fieldName")==e&&n(h[u]).remove();else n(h).remove();try{l=JSON.parse(this.model.pivotControl.getOlapReport()).Report}catch(b){t.isNullOrUndefined(this.model.pivotControl.getOlapReport)||(l=this.model.pivotControl.getOlapReport())}if(o=="XMLA"&&l==""&&(l=this.model.pivotControl.model.dataSource,this._selectedMember=e,this._selectedTreeNode=n(i.currentElement),this.model.pivotControl.dataSource=this.model.pivotControl._clearDrilledItems(this.model.pivotControl.model.dataSource,{action:"nodeStateModefied"})),f=this._droppedClass!=""?this._droppedClass:c.pivotType!=r?c.pivotType=="PivotItem"?"e-schemaRow":"e-schemaValue":"e-schemaRow",this._droppedClass="",i.type=="nodeCheck"){if(o=="XMLA"?(s=n(i.currentElement).find(".kpi").length>0?n(i.currentElement).parents("li:eq(0)").children("div").text()+" "+s:s,p=this.model.pivotControl.model.dataSource.enableAdvancedFilter?n(i.currentElement.find(".e-text")):n(i.currentElement.find(".e-text")[0]),n(i.currentElement).attr("data-tag").toLowerCase().indexOf("[measures]")<0&&!(n(i.currentElement).find(".e-namedSetCDB").length>0)?p.after(t.buildTag("span.e-icon").css("display","none").addClass("treeDrop")[0].outerHTML):"",n(i.currentElement).attr("data-tag").toLocaleLowerCase().indexOf("[measures]")>=0&&(f="e-schemaValue"),droppedItem=n(i.currentElement).find(".e-namedSetCDB").length>0?{fieldName:n(i.currentElement).attr("data-tag"),fieldCaption:n(i.currentElement).text(),isNamedSets:!0}:t.olap.base._getCaption({fieldName:e,fieldCaption:s},this.model.pivotControl._fieldData.hierarchy)):(n(i.currentElement.find(".e-text")[0]).after(t.buildTag("span.e-icon").css("display","none").addClass("treeDrop")[0].outerHTML),droppedItem={fieldName:e,fieldCaption:s}),f=f==""?"row":f=="e-schemaColumn"?"column":f=="e-schemaRow"?"row":f=="e-schemaValue"?"value":f=="e-schemaFilter"?"filter":"",this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&(a=n.grep(this.model.pivotControl._calculatedField,function(n){return n.name==e}),a.length>0)){if(this._calculatedFieldItems(a))return;f="value";droppedItem={fieldName:e,fieldCaption:s,isCalculatedField:!0,formula:a[0].formula}}this._createPivotButton(droppedItem,f,"","","");o=="XMLA"&&f=="value"?(this.model.pivotControl.model.dataSource.values.length==0&&this.model.pivotControl.model.dataSource.values.push({measures:[],axis:"columns"}),measuresAxis=this.model.pivotControl.model.dataSource.values[0].axis=="columns"?"Columns:":"Rows:",this.element.find("div[data-tag='"+measuresAxis+"Measures']").length==0&&this._createPivotButton({fieldName:"Measures",fieldCaption:this._getLocalizedLabels("Measures")},measuresAxis=="Columns:"?"column":"row","","",""),this.model.pivotControl.model.dataSource.values[0].measures.push(droppedItem)):n.grep(this.model.pivotControl.model.dataSource.values,function(n){return n.fieldName==droppedItem.fieldName}).length==0&&(f=="row"?this.model.pivotControl.model.dataSource.rows.push(droppedItem):f=="column"?this.model.pivotControl.model.dataSource.columns.push(droppedItem):f=="filter"?this.model.pivotControl.model.dataSource.filters.push(droppedItem):this.model.pivotControl.model.dataSource.values.push(droppedItem));o=="XMLA"?(t.isNullOrUndefined(this.model.pivotControl._ogridWaitingPopup)||this.model.pivotControl._ogridWaitingPopup.show(),t.olap.base.getJSONData({action:"nodeCheck"},this.model.pivotControl.model.dataSource,this.model.pivotControl)):(this._trigger("fieldItemDropped",{axis:f,fieldItem:i.currentElement}),this.model.pivotControl.model.editCellsInfo={},this.model.pivotControl._populatePivotGrid())}else if(i.type=="nodeUncheck"){if(n(i.currentElement).removeClass("filter").find(".filter").remove(),n(i.currentElement).removeClass("filter").find(".treeDrop").remove(),this.model.pivotControl.model.dataSource.columns=n.grep(this.model.pivotControl.model.dataSource.columns,function(n){return n.fieldName!=e}),this.model.pivotControl.model.dataSource.rows=n.grep(this.model.pivotControl.model.dataSource.rows,function(n){return n.fieldName!=e}),w=this.model.pivotControl.model.dataSource.values,o=="XMLA")t.olap._mdxParser._getItemPosition(this.model.pivotControl.model.dataSource.values[0].measures,e).length>0&&this.model.pivotControl.model.dataSource.values[0].measures.splice(t.olap._mdxParser._getItemPosition(this.model.pivotControl.model.dataSource.values[0].measures,e)[0],1),this.model.pivotControl.model.dataSource.values[0].measures.length==0&&this.element.find("div[data-tag='"+(this.model.pivotControl.model.dataSource.values[0].axis=="columns"?"Columns":"Rows")+":Measures']").remove();else if(this.model.pivotControl.model.dataSource.values=n.grep(w,function(n){return n.fieldName!=e}),this.model.pivotControl._calculatedField.length>0)for(v=n.grep(this.model.pivotControl.model.dataSource.values,function(n){return n.isCalculatedField==!0&&n.formula.indexOf(e)>-1}),u=0;u<v.length;u++)this._tableTreeObj.uncheckNode(v[u].fieldName);this.model.pivotControl.model.dataSource.filters=n.grep(this.model.pivotControl.model.dataSource.filters,function(n){return n.fieldName!=e});o=="XMLA"?(t.isNullOrUndefined(this.model.pivotControl._ogridWaitingPopup)||this.model.pivotControl._ogridWaitingPopup.show(),t.olap.base.getJSONData({action:"nodeUncheck"},this.model.pivotControl.model.dataSource,this.model.pivotControl)):(f=f==""?"row":f=="e-schemaColumn"?"column":f=="e-schemaRow"?"row":f=="e-schemaValue"?"value":f=="e-schemaFilter"?"filter":"",this._trigger("fieldItemDropped",{axis:f,fieldItem:i.currentElement}),this.model.pivotControl.model.editCellsInfo={},this.model.pivotControl._populatePivotGrid())}this._setPivotBtnWidth()},_calculatedFieldItems:function(i){var u,r;if(i.length>0)for(u=i[0].formula.replace(/\(|\)/g," ").replace(/[-+*/^%]/g," ").split(" "),r=0;r<u.length;r++)if(!n.isNumeric(u[r])&&u[r].replace(/\s+|\s+$/gm,"")!=""&&(this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode&&n.grep(this.model.pivotControl.model.dataSource.values,function(n){return n.fieldName==u[r]}).length==0||this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&n.grep(JSON.parse(this.model.pivotControl.getOlapReport()).PivotCalculations,function(n){return n.FieldName==u[r]}).length==0))return alert(this.model.pivotControl._getLocalizedLabels("NotPresent")),this._tableTreeObj.model.nodeCheck=null,this._tableTreeObj.model.nodeUncheck=null,this._tableTreeObj.uncheckNode(i[0].name),this._tableTreeObj.model.nodeCheck=this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?t.proxy(this._checkedStateModified,this):t.proxy(this._pivotCheckedStateModified,this),this._tableTreeObj.model.nodeUncheck=this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?t.proxy(this._checkedStateModified,this):t.proxy(this._pivotCheckedStateModified,this),!0;return!1},_checkedStateModified:function(i){var rt,ut,ft,et,d,f,g,nt,w,l,a,v,b,u,s,o,tt,ot,k,e;if(this.model.pivotControl._isUpdateRequired=!0,this._isMeasureBtnRemove==!0)return this._isMeasureBtnRemove=!1,!1;if(this._nodeCheck==!0)return this._nodeCheck=!1,!1;var c=n(i.currentElement).find("a")[0].textContent,p="",it="",h="",u="",y="";if(this._dataModel!="Pivot"||i.type!="nodeCheck"||(rt=n.grep(this.model.pivotControl._calculatedField,function(t){return t.name==n(i.currentElement).attr("id")}),!this._calculatedFieldItems(rt))){if(this._dataModel=="Olap")if(!i.currentElement[0].id.indexOf("[Measures]")>-1)for(e=0;e<this.element.find(".e-pivotButton").length;e++){if(i.type=="nodeCheck"&&!t.isNullOrUndefined(n(i.currentElement).attr("data-parentuniquename"))&&!t.isNullOrUndefined(n(this.element.find(".e-pivotButton")[e]).find(".e-btn"))&&!t.isNullOrUndefined(n(this.element.find(".e-pivotButton")[e]).find(".e-btn").attr("data-parentuniquename"))&&n(this.element.find(".e-pivotButton")[e]).find(".e-btn").attr("data-parentuniquename")==n(i.currentElement).attr("data-parentuniquename")){ft=this._getLocalizedLabels("NamedSetAlert").replace("<Set 1>",n(this.element.find(".e-pivotButton")[e]).find(".e-btn").text()).replace("<Set 2>",n(i.currentElement).find(".e-text").text());et=t.buildTag("div.data-namedSetDialog#"+this._id+"_NamedSetDialog",t.buildTag("div.data-namedSetDialogContent",ft)[0].outerHTML+t.buildTag("div",t.buildTag("button#"+this._id+"_OKBtn.e-okBtn","OK",{margin:"20px 0 10px 120px"}).attr("title","OK")[0].outerHTML+t.buildTag("button#"+this._id+"_CancelBtn.e-cancelBtn","Cancel",{margin:"20px 0 10px 10px"}).attr("title","Cancel")[0].outerHTML)[0].outerHTML).attr("title","Warning")[0].outerHTML;this.model.pivotControl.element.append(et);d=n(this.element.find(".e-pivotButton")[e]);this.model.pivotControl.element.find(".data-namedSetDialog").ejDialog({target:"#"+this.model.pivotControl._id,enableResize:!1,enableRTL:!1,width:"400px",close:function(){f._nodeCheck=!0;f._tableTreeObj.uncheckNode(i.currentElement)}});f=this;this.model.pivotControl.element.find(".e-okBtn,.e-cancelBtn").ejButton({type:t.ButtonType.Button,width:"70px"});this.model.pivotControl.element.find(".e-cancelBtn").on(t.eventType.click,function(){f._nodeCheck=!0;f._tableTreeObj.uncheckNode(i.currentElement);f.model.pivotControl.element.find(".e-dialog").remove()});this.model.pivotControl.element.find(".e-okBtn").on(t.eventType.click,function(){var e;f._currentCheckedNode=u=n(i.currentElement).attr("data-tag");e=f._droppedClass!=""&&f._droppedClass!=r?f._droppedClass:u.pivotType!=r?u.pivotType=="PivotItem"?"e-schemaRow":"e-schemaValue":"e-schemaRow";f._nodeDropedParams=e=="e-schemaColumn"?"Categorical":e=="e-schemaRow"?"Series":e=="e-schemaFilter"?"Slicer":"";a=f._nodeDropedParams==""?n(i.currentElement).attr("data-tag").indexOf("[Measures]")>=0?"Categorical":"Series":f._nodeDropedParams;try{s=JSON.parse(f.model.pivotControl.getOlapReport()).Report}catch(y){t.isNullOrUndefined(f.model.pivotControl.getOlapReport)||(s=f.model.pivotControl.getOlapReport())}var h=n(d).attr("data-tag"),c=f.model.pivotControl._getNodeUniqueName(h),l=f.model.pivotControl._getNodeByUniqueName(c);this._nodeCheck=!0;f._tableTreeObj.uncheckNode(l);v=f._currentCubeName+"--"+u+"--"+a+"--##"+n(d).attr("data-tag");s;f.model.beforeServiceInvoke!=null&&f.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&f._trigger("beforeServiceInvoke",{action:"nodeDropped",element:f.element,customObject:f.model.pivotControl.model.customObject});o=JSON.stringify({action:"nodeDroppedNamedSet",dropType:"TreeNode",nodeInfo:v,currentReport:s,gridLayout:f.model.pivotControl.model.layout,customObject:JSON.stringify(f.model.pivotControl.model.customObject)});f.model.pivotControl.model.enableDeferUpdate?f.doAjaxPost("POST",f.model.pivotControl.model.url+"/"+f.model.serviceMethods.nodeDropped,o.replace("nodeDropped","nodeDroppedDeferUpdate"),f._droppedSuccess):f.doAjaxPost("POST",f.model.pivotControl.model.url+"/"+f.model.serviceMethods.nodeDropped,o,f._droppedSuccess)});return 0}if(u=n(this.element.find(".e-pivotButton")[e]).attr("data-tag"),y="",y=u.indexOf("[Measures]")>-1||u.indexOf("[")>-1?u.split(":")[1]:this.model.pivotControl._getNodeUniqueName(u),y=y.replace("<>","."),ut=n(i.currentElement).attr("data-tag")=="Value"||n(i.currentElement).attr("data-tag")=="Goal"||n(i.currentElement).attr("data-tag")=="Status"||n(i.currentElement).attr("data-tag")=="Trend"?"["+n(i.currentElement).parents("ul:eq(1) li:eq(0)").attr("data-tag")+"]":n(i.currentElement).attr("data-tag"),ut.toLowerCase()==y.toLowerCase()){h=n(this.element.find(".e-pivotButton")[e]);break}}else h=this.element.find(".e-pivotButton:contains("+c+")"),u,s;else h=this.element.find(".e-pivotButton:contains("+c+")"),u,s;for(g=h.length>0&&n(h).siblings(".e-filterIndicator").length>0?!0:!1,nt=i.currentElement.find("~ span:eq(0) span.descending").length>0?!0:!1,e=0;e<this.model.pivotTableFields.length;e++)this.model.pivotTableFields[e].name==c&&i.type=="nodeCheck"?(this.model.pivotTableFields[e].isSelected=!0,u=this.model.pivotTableFields[e]):this.model.pivotTableFields[e].name==c&&i.type=="nodeUncheck"&&(this.model.pivotTableFields[e].isSelected=!1,u=this.model.pivotTableFields[e]);if(t.isNullOrUndefined(u)&&(u=n(i.currentElement).attr("data-tag")),i.type=="nodeUncheck")if(this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this._clearFilterData(c),h.length>1)for(e=0;e<h.length;e++)n(h[e]).text()==c&&n(h[e]).remove();else n(h).remove();else this._droppedClass!=""?this._createPivotButton({fieldName:u.id,fieldCaption:c},this._droppedClass=="e-schemaColumn"?"column":this._droppedClass=="e-schemaRow"?"row":this._droppedClass=="e-schemaValue"?"value":this._droppedClass=="e-schemaFilter"?"filter":"",g,nt,""):this._createPivotButton({fieldName:u.id,fieldCaption:c},this._dataModel=="Olap"&&n(i.currentElement).attr("data-tag").indexOf("[Measures]")<0||this._dataModel=="Pivot"&&u.pivotType=="PivotItem"?"row":"value",g,nt,"");if(!t.isNullOrUndefined(this._tempFilterData))for(e=0;e<this._tempFilterData.length;e++)if(!t.isNullOrUndefined(this._tempFilterData[e][u.id]))for(w=0;w<this._tempFilterData[e][u.id].length;w++)it+="##"+this._tempFilterData[e][u.id][w];try{s=JSON.parse(this.model.pivotControl.getOlapReport()).Report}catch(st){t.isNullOrUndefined(this.model.pivotControl.getOlapReport)||(s=this.model.pivotControl.getOlapReport())}if(p="e-schemaRow::"+c+"::FILTERED"+it,l=this._droppedClass!=""?this._droppedClass:u.pivotType=="PivotItem"?"e-schemaRow":"e-schemaValue",this._droppedClass="",t.isNullOrUndefined(this.model.pivotControl)||t.isNullOrUndefined(this.model.pivotControl._waitingPopup)||this.model.pivotControl._waitingPopup.show(),this.element.find(".schemaNoClick").addClass("freeze").width(n(this.element).width()).height(n(this.element).height()).css({top:n(this.element).offset().top,left:n(this.element).offset().left}),i.type=="nodeCheck"||i.type=="nodeUncheck"||l=="")if(i.type=="nodeCheck")this._dataModel=="Olap"?(n(i.currentElement).attr("data-tag").indexOf("[Measures]")==-1&&n(i.currentElement).find(".e-namedSetCDB").length==0&&(b=t.buildTag("span.e-icon").attr("role","button").attr("aria-label","filter button").addClass("treeDrop")[0].outerHTML,n(i.currentElement.find(".e-text")[0]).after(n(b).hide())),this._nodeDropedParams=l=="e-schemaColumn"?"Categorical":l=="e-schemaRow"?"Series":l=="e-schemaFilter"?"Slicer":"",this._currentCheckedNode=u=n(i.currentElement).attr("data-tag"),a=this._nodeDropedParams==""?n(i.currentElement).attr("data-tag").indexOf("[Measures]")>=0?"Categorical":"Series":this._nodeDropedParams,u.toLowerCase().indexOf("[measures]")>-1&&(a="Categorical"),v=this._currentCubeName+"--"+u+"--"+a+"--",s,this.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:"nodeDropped",element:this.element,customObject:this.model.pivotControl.model.customObject}),o=JSON.stringify({action:"nodeDropped",dropType:"TreeNode",nodeInfo:v,currentReport:s,gridLayout:this.model.pivotControl.model.layout,customObject:JSON.stringify(this.model.pivotControl.model.customObject)}),this.model.pivotControl.model.enableDeferUpdate?this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeDropped,o.replace("nodeDropped","nodeDroppedDeferUpdate"),this._droppedSuccess):this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeDropped,o,this._droppedSuccess)):(n(i.currentElement.find(".e-text")[0]).after(n(t.buildTag("span.e-icon").attr("role","button").attr("aria-label","filter button").addClass("treeDrop")[0].outerHTML).hide()),this.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:"nodeStateModified",element:this.element,customObject:this.model.pivotControl.model.customObject}),o=JSON.stringify({action:"nodeStateModified",headerTag:JSON.stringify(u),dropAxis:l+"::",sortedHeaders:this.model.pivotControl._ascdes,filterParams:p,currentReport:s,valueSorting:JSON.stringify(this.model.pivotControl.model.valueSortSettings),customObject:JSON.stringify(this.model.pivotControl.model.customObject)}),this.model.pivotControl.model.enableDeferUpdate?(this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeStateModified,o.replace("nodeStateModified","nodeStateModifiedDeferUpdate"),this._nodeStateModifiedSuccess),t.isNullOrUndefined(this.model.pivotControl._ogridWaitingPopup)||this.model.pivotControl._ogridWaitingPopup.hide()):this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeStateModified,o,this._nodeStateModifiedSuccess));else if(i.type=="nodeUncheck")if(this._dataModel=="Olap"){if(n(i.currentElement.find(".treeDrop")).remove(),n(i.currentElement).find(".filter").removeClass("filter"),u=this._currentCheckedNode=n(h).attr("data-tag"),delete this.model.pivotControl._fieldMembers[u.split(":")[u.split(":").length-1]],delete this.model.pivotControl._fieldSelectedMembers[u.split(":")[u.split(":").length-1]],this.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:"removeButton",element:this.element,customObject:this.model.pivotControl.model.customObject}),o=JSON.stringify({action:"removeButton",headerInfo:u,currentReport:s,gridLayout:this.model.pivotControl.model.layout,customObject:JSON.stringify(this.model.pivotControl.model.customObject)}),t.isNullOrUndefined(u))return!1;u.indexOf("[Measures]")>=0||u.toLowerCase().indexOf("[measures]")?(tt=u.split(":"),ot=tt[0]=="Rows"?".e-schemaRow":tt[0]=="Columns"?".e-schemaColumn":"",this.element.find(".e-schemaValue .e-pivotButton [data-tag*='Measures']").length<=0&&this.element.find(ot+" .e-pivotButton:contains('Measures')").remove()):u.indexOf("[Measures]")<0&&u.indexOf("Measures")>=0&&this.element.find(".e-schemaValue .e-pivotButton").remove();this.model.pivotControl.model.enableDeferUpdate?(this._removeButtonDeferUpdate=!0,this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.removeButton,o.replace("removeButton","removeButtonDeferUpdate"),this._droppedSuccess)):this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.removeButton,o,this._pvtBtnDroppedSuccess)}else{if(this._clearFilterData(u.id),this.model.pivotControl._calculatedField.length>0)for(k=n.grep(JSON.parse(this.model.pivotControl.getOlapReport()).PivotCalculations,function(n){return n.CalculationType==8&&n.Formula.indexOf(u.id)>-1}),e=0;e<k.length;e++)this._tableTreeObj.model.nodeUncheck=null,this._tableTreeObj.uncheckNode(k[e].FieldName),this._tableTreeObj.element.find("li[id='"+k[e].FieldName+"']").removeClass("filter").find(".treeDrop").remove(),this._tableTreeObj.model.nodeUncheck=t.proxy(this._checkedStateModified,this);delete this.model.pivotControl._fieldMembers[u.name];n(i.currentElement).removeClass("filter").find(".filter").remove();n(i.currentElement).removeClass("filter").find(".treeDrop").remove();this.model.pivotControl._ascdes=this.model.pivotControl._ascdes.replace(c+"##","");this.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:"nodeStateModified",element:this.element,customObject:this.model.pivotControl.model.customObject});o=JSON.stringify({action:"nodeStateModified",headerTag:JSON.stringify(u),dropAxis:l+"::",sortedHeaders:this.model.pivotControl._ascdes,filterParams:p,currentReport:s,valueSorting:JSON.stringify(this.model.pivotControl.model.valueSortSettings),customObject:JSON.stringify(this.model.pivotControl.model.customObject)});this.model.pivotControl.model.enableDeferUpdate?(this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeStateModified,o.replace("nodeStateModified","nodeStateModifiedDeferUpdate"),this._nodeStateModifiedSuccess),t.isNullOrUndefined(this.model.pivotControl._waitingPopup)||this.model.pivotControl._waitingPopup.hide()):this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeStateModified,o,this._nodeStateModifiedSuccess)}else t.Pivot.closePreventPanel(this),this.element.find(".schemaNoClick").removeClass("freeze").removeAttr("style");else this._dataModel=="Pivot"?(this.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:"nodeStateModified",element:this.element,customObject:this.model.pivotControl.model.customObject}),o=JSON.stringify({action:"nodeStateModified",headerTag:JSON.stringify(u),dropAxis:l+"::"+this._droppedPosition,sortedHeaders:this.model.pivotControl._ascdes,filterParams:p,currentReport:s,customObject:JSON.stringify(this.model.pivotControl.model.customObject)}),this._droppedPosition="",this.model.pivotControl.model.enableDeferUpdate?this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeStateModified,o.replace("nodeStateModified","nodeStateModifiedDeferUpdate"),this._nodeStateModifiedSuccess):this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeStateModified,o,this._nodeStateModifiedSuccess)):(n(i.currentElement).attr("data-tag").toLowerCase().indexOf("[measures]")==-1&&(b=t.buildTag("span.e-icon").attr("role","button").attr("aria-label","filter button").addClass("treeDrop")[0].outerHTML,n(i.currentElement.find(".e-text")[0]).after(n(b).hide())),u=n(i.currentElement).attr("data-tag"),a=this._nodeDropedParams==""?n(i.currentElement).attr("data-tag").toLowerCase().indexOf("[Measures]")>=0?"Categorical":"Series":this._nodeDropedParams,v=this._currentCubeName+"--"+u+"--"+a+"--",s,this.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:"nodeDropped",element:this.element,customObject:this.model.pivotControl.model.customObject}),o=JSON.stringify({action:"nodeDropped",dropType:"TreeNode",nodeInfo:v,currentReport:s,customObject:JSON.stringify(this.model.pivotControl.model.customObject)}),this.model.pivotControl.model.enableDeferUpdate?this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeDropped,o.replace("nodeDropped","nodeDroppedDeferUpdate"),this._droppedSuccess):this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeDropped,o,this._droppedSuccess));this._setPivotBtnWidth()}},_clearFilterData:function(n){var i,r;if(!t.isNullOrUndefined(this._tempFilterData))for(i=0;i<this._tempFilterData.length;i++)for(r in this._tempFilterData[i])r==n&&this._tempFilterData[i][n]!=""&&this._tempFilterData.splice(i,1);this.model.pivotControl._tempFilterData=this._tempFilterData},_nodeDropped:function(i){var b,it,nt,et,ot,st,rt,k,ht,tt,u,s,d,l,p,w,h,y,ct,g,a;if(n(i.dropTarget).hasClass("e-pvtBtn")&&(i.dropTarget=n(i.dropTarget).parents("div.e-droppable").length>0&&n(i.dropTarget).parents(".e-pivotschemadesigner").length>0?n(i.dropTarget).parents("div.e-droppable"):i.dropTarget),n(this.element).parents(".e-pivotclient").length>0&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot){var f="",o=this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode&&this.model.pivotControl._dataModel!="XMLA"?n.grep(this.model.pivotTableFields,function(n){return n.caption==i.droppedElement.text()})[0].name:this._dataModel=="XMLA"?i.droppedElement.attr("data-tag"):i.droppedElement.text(),lt=n(i.droppedElement).attr("id"),ut=i.dropTarget.hasClass("e-schemaColumn")?"schemaColumn":i.dropTarget.hasClass("e-schemaRow")?"schemaRow":i.dropTarget.hasClass("e-schemaFilter")?"schemaFilter":i.dropTarget.hasClass("e-schemaValue")?"schemaValue":"";if(ut=="")return!1;h=this._setSplitBtnTargetPos(t.isNullOrUndefined(i.event)?i:i.event);try{v=JSON.parse(this.model.pivotControl.getOlapReport()).Report}catch(vt){v=this.model.pivotControl.getOlapReport()}for(t.isNullOrUndefined(this.model.pivotControl._waitingPopup)||this.model.pivotControl._waitingPopup.show(),u=0;u<this.model.pivotTableFields.length;u++)if(this.model.pivotTableFields[u].name==o){this.model.pivotTableFields[u].isSelected=!0;f=this.model.pivotTableFields[u];break}n(this.element).parents(".e-pivotclient").length>0&&this.model.pivotControl.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this.model.pivotControl._trigger("beforeServiceInvoke",{action:"nodeDropped",element:this.model.pivotControl.element,customObject:this.model.pivotControl.model.customObject});a=JSON.stringify({action:this.model.pivotControl.model.enableDeferUpdate?"nodeDroppedDeferUpdate":"nodeDropped",args:JSON.stringify({droppedFieldCaption:o,droppedFieldName:lt,headerTag:f,dropAxis:ut,droppedPosition:h,currentReport:v})+"-##-"+JSON.stringify(this.model.pivotControl.model.valueSortSettings),customObject:JSON.stringify(this.model.pivotControl.model.customObject)});this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeDropped,a,this._pvtNodeDroppedSuccess);this._isDragging=!1;return}if(this.model.pivotControl._isUpdateRequired=!0,i.dropTarget[0]!=r||i.dropTarget.className!=r){if(b=!1,this._dataModel=="Pivot")for(it=n(i.dropTarget).parents(),u=0;u<it.length;u++)n(it[u]).hasClass("e-pivotgrid")&&(b=!0);if(i.dropTarget.hasClass("e-droppable")||i.dropTarget[0]!=r&&i.dropTarget[0].className.indexOf("e-droppable")>=0||i.dropTarget.className!=r&&i.dropTarget.className.indexOf("e-droppable")>=0||b){this._isDragging=!1;this.element.find(".e-dropIndicator").removeClass("e-dropIndicatorHover");var f=n(i.droppedElement).attr("data-tag"),o=this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode&&this.model.pivotControl._dataModel!="XMLA"?n.grep(this.model.pivotTableFields,function(n){return n.caption==i.droppedElement.text()})[0].name:this._dataModel=="XMLA"?i.droppedElement.attr("data-tag"):i.droppedElement.text(),at="",v,c=this.element.find(".e-pivotButton:contains("+o+")"),e=this._dataModel=="XMLA"?this._tableTreeObj.element.find("li[data-tag='"+f+"']").length>1?i.droppedElement:this._tableTreeObj.element.find("li[data-tag='"+f+"']"):this._dataModel=="Pivot"&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode?this._tableTreeObj.element.find("li[id="+o+"]"):this._dataModel=="Pivot"?this._tableTreeObj.element.find("li:contains('"+o+"'):last"):this._tableTreeObj.element.find("li[data-tag='"+f+"']"),y=n(c).find(".e-filterIndicator").length>0?!0:!1,ft=n(c).find("~ span:eq(0) span.descending").length>0?!0:!1;if(i.dropTarget.parent().attr("role")=="presentation")return!1;if(this.model.pivotControl._dataModel=="XMLA"){if(nt=n.map(n(f.split("].")),function(n){return n.lastIndexOf("]")>=0?n:n+"]"}),nt.length>=2&&(et=nt[0]+"."+nt[1],e=this._tableTreeObj.element.find("li[data-tag='"+et+"']"),f=e.attr("data-tag")),ot=n(i.dropTarget).hasClass("value")||n(i.dropTarget).hasClass("colheader")||n(i.dropTarget).hasClass("rowheader")||n(i.dropTarget).hasClass("summary")||n(i.dropTarget).parents("table:eq(0)").hasClass("e-pivotGridTable")||n(i.dropTarget).parents(".groupingBarPivot").length>0,st=i.droppedElement.find(".e-namedSetCDB").length>0&&n(i.dropTarget).hasClass("e-schemaFilter"),ot||st||n(i.droppedElement).attr("data-tag").toLowerCase().indexOf("measures")>=0&&!n(i.dropTarget).hasClass("e-schemaValue")||n(i.droppedElement).attr("data-tag").indexOf("Measures")<0&&n(i.dropTarget).hasClass("e-schemaValue"))return this._createErrorDialog(),this._waitingPopup.hide(),!1;if(rt=t.Pivot.getReportItemByFieldName(f,this.model.pivotControl.model.dataSource,this._dataModel),k=rt.axis,!(e.length>1)&&rt.item.length>0&&(k=="rows"&&i.dropTarget.hasClass("e-schemaRow")||k=="columns"&&i.dropTarget.hasClass("e-schemaColumn")||k=="values"&&n(i.dropTarget).hasClass("e-schemaValue")||k=="filters"&&n(i.dropTarget).hasClass("e-schemaFilter")))return!1;if(e.length>1&&i.droppedElement.find(".kpiValue").length>0&&(e=e.find(".kpiValue").parents("li:eq(0)")),this._tableTreeObj.isNodeChecked(e))for(u=0;u<this.element.find(".e-pivotButton").length;u++)this.element.find(".e-pivotButton:eq("+u+")").attr("data-tag").split(":")[1]==f?this.element.find(".e-pivotButton:eq("+u+")").remove():""}else if(this._dataModel=="Olap"&&((n(i.droppedElement).attr("data-tag").indexOf("Measures")>=0||n(i.droppedElement).parents("[data-tag='KPI']").length>0)&&(n(i.dropTarget).hasClass("e-schemaFilter")||n(i.dropTarget).hasClass("e-drag"))||(n(i.droppedElement).attr("data-tag").indexOf("Measures")<0||n(i.droppedElement).parents("[data-tag='KPI']").length<0)&&n(i.dropTarget).hasClass("e-schemaValue")))return this._createErrorDialog(),this._waitingPopup.hide(),!1;if(n(e).find(".filter").remove(),this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode)for(c=[],u=0;u<this.element.find(".e-pivotButton").length;u++)uniqueName=n(this.element.find(".e-pivotButton")[u]).find("button").attr("data-fieldName"),uniqueName==o&&c.push(this.element.find(".e-pivotButton")[u]);if(b)if(i.event.target.tagName=="TH"||i.event.target.tagName=="TD"||n(i.event.target).hasClass("cellValue")&&(n(i.event.target).parent("th").length>0||n(i.event.target).parent("td").length>0)){if(n(i.event.target).hasClass("cellValue")&&(n(i.event.target).parent("th").length>0?i.event.target=n(i.event.target).parent("th")[0]:n(i.event.target).parent("td").length>0&&(i.event.target=n(i.event.target).parent("td")[0])),this._droppedClass=i.event.target.className.split(" ")[0]=="rowheader"||i.event.target.className.split(" ")[0]=="e-grpRow"?"e-schemaRow":i.event.target.className.split(" ")[0]=="colheader"?"e-schemaColumn":n(i.event.target).hasClass("value")?"e-schemaValue":"",this._droppedClass=="")return}else this._droppedClass=i.event.target.className.split(" ")[0]=="e-rows"||i.event.target.className.split(" ")[0]=="emptyRows"?"e-schemaRow":i.event.target.className.split(" ")[0]=="columns"?"e-schemaColumn":i.event.target.className.split(" ")[0]=="values"?"e-schemaValue":i.event.target.className.split(" ")[0]=="e-drag"?"e-schemaFilter":"";else this._droppedClass=t.isNullOrUndefined(i.event)?i.dropTarget.hasClass("e-schemaColumn")?"e-schemaColumn":i.dropTarget.hasClass("e-schemaRow")?"e-schemaRow":i.dropTarget.hasClass("e-schemaFilter")?"e-schemaFilter":i.dropTarget.hasClass("e-schemaValue")?"e-schemaValue":"":i.event.target.className.split(" ")[0]=="e-drag"?"e-schemaFilter":i.event.target.className.split(" ")[0]=="values"?"e-schemaValue":i.event.target.className.split(" ")[0]=="columns"?"e-schemaColumn":i.event.target.className.split(" ")[0]=="e-grpRow"?"e-schemaRow":i.event.target.className.split(" ")[0];if(this._dataModel=="Olap"&&(ht=n(i.droppedElement).children("div:eq(0)").find(".e-checkbox").length,e=ht==0?i.droppedElement.parent("ul").parent("li:eq(0)"):i.droppedElement),this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._dataModel=="Pivot"&&this._droppedClass!="e-schemaValue"&&(n.grep(this.model.pivotControl._calculatedField,function(n){return n.name==e.attr("id")}).length>0||n.grep(JSON.parse(this.model.pivotControl.getOlapReport()).PivotCalculations,function(n){return n.FieldName==e.attr("id")&&n.CalculationType==8}).length>0)){this.model.pivotControl._createErrorDialog(this.model.pivotControl._getLocalizedLabels("CalcValue"),this.model.pivotControl._getLocalizedLabels("Warning"));return}if(e.find(".e-chk-inact").length>0&&this._droppedClass!=""&&(this._droppedClass=="e-schemaRow"||this._droppedClass=="e-schemaColumn"||this._droppedClass=="e-schemaFilter"||this._droppedClass=="e-schemaValue"))return this._tableTreeObj.checkNode(e),!1;if(e.find(".e-chk-inact").length>0&&this._droppedClass!=""&&(this._droppedClass=="e-pvtBtn"||this._droppedClass=="rowheader"||this._droppedClass=="colheader"||this._droppedClass=="value"&&n(i.droppedElement).attr("data-tag").indexOf("Measures")<0)&&(this._nodeCheck=!0,this._tableTreeObj.checkNode(e)),t.isNullOrUndefined(f))for(u=0;u<this.model.pivotTableFields.length;u++)this.model.pivotTableFields[u].name==o&&(this.model.pivotTableFields[u].isSelected=!0,f=this.model.pivotTableFields[u]);if(!t.isNullOrUndefined(this._tempFilterData))for(u=0;u<this._tempFilterData.length;u++)if(!t.isNullOrUndefined(this._tempFilterData[u][o]))for(tt=0;tt<this._tempFilterData[u][o].length;tt++)at+="##"+this._tempFilterData[u][o][tt];if(i.droppedElement[0].tagName.toLowerCase()=="button")if(c.length>1)for(u=0;u<c.length;u++)n(c[u]).text()==o&&n(c[u]).remove();else n(c).remove();try{v=JSON.parse(this.model.pivotControl.getOlapReport()).Report}catch(vt){t.isNullOrUndefined(this.model.pivotControl.getOlapReport)||(v=this.model.pivotControl.getOlapReport())}if(this.model.pivotControl._dataModel=="XMLA"&&(v=this.model.pivotControl.model.dataSource),this._dataModel=="Pivot"||this.model.pivotControl._dataModel=="XMLA"){for(o=this.model.pivotControl._dataModel=="XMLA"?f:o,h="",h=b?this._droppedPosition="":t.isNullOrUndefined(i.event)?"":this._droppedPosition=this._setSplitBtnTargetPos(i.event),u=0;u<this.model.pivotTableFields.length;u++)if(this.model.pivotTableFields[u].name==o){this.model.pivotTableFields[u].isSelected=!0;f=this.model.pivotTableFields[u];n(c).remove();break}if(e.find(".e-chk-inact").length>0&&this._droppedClass!=""&&(this._droppedClass=="e-schemaRow"||this._droppedClass=="e-schemaColumn"||this._droppedClass=="e-schemaFilter"||this._droppedClass=="e-schemaValue"))this._tableTreeObj.checkNode(e);else if(this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode){if(s=this.model.pivotControl._getDroppedItem(o),s.length&&s[0].filterItems&&s[0].filterItems.values&&s[0].filterItems.values.length&&n(n(e).find(".e-text")[0]).after(t.buildTag("span.e-icon").attr("role","button").attr("aria-label","filtered").addClass("filter")[0].outerHTML),s.length||(s[0]={fieldName:o,fieldCaption:this._dataModel=="XMLA"&&e.find(".e-hierarchyCDB ").length>0?e.find("div:eq(0)").text():o}),this.model.pivotControl.model.dataSource.columns=n.grep(this.model.pivotControl.model.dataSource.columns,function(n){return n.fieldName!=o}),this.model.pivotControl.model.dataSource.rows=n.grep(this.model.pivotControl.model.dataSource.rows,function(n){return n.fieldName!=o}),this._dataModel!="XMLA"?this.model.pivotControl.model.dataSource.values=n.grep(this.model.pivotControl.model.dataSource.values,function(n){return n.fieldName!=o}):this.model.pivotControl.model.dataSource.values[0].measures=n.grep(this.model.pivotControl.model.dataSource.values[0].measures,function(n){return n.fieldName!=o}),this.model.pivotControl.model.dataSource.filters=n.grep(this.model.pivotControl.model.dataSource.filters,function(n){return n.fieldName!=o}),d=this._droppedClass=="e-schemaRow"?"row":this._droppedClass=="e-schemaColumn"?"column":this._droppedClass=="e-schemaFilter"?"filter":this._droppedClass=="e-schemaValue"?"value":"",this.model.pivotControl._dataModel=="Pivot"&&n.grep(this.model.pivotControl._calculatedField,function(n){return n.name==s[0].fieldCaption}).length>0&&d!="value"){alert(this.model.pivotControl._getLocalizedLabels("CalcValue"));this._tableTreeObj.checkNode(s[0].fieldCaption);return}this._createPivotButton(s[0],d,y,ft,h);n.isNumeric(h)?this._droppedClass=="e-schemaRow"?this.model.pivotControl.model.dataSource.rows.splice(h,0,s[0]):this._droppedClass=="e-schemaColumn"?this.model.pivotControl.model.dataSource.columns.splice(h,0,s[0]):this._droppedClass=="e-schemaValue"?this.model.pivotControl._dataModel=="XMLA"?this.model.pivotControl.model.dataSource.values[0].measures.splice(h,0,s[0]):this.model.pivotControl.model.dataSource.values.splice(h,0,s[0]):this.model.pivotControl.model.dataSource.filters.splice(h,0,s[0]):this._droppedClass=="e-schemaRow"?this.model.pivotControl.model.dataSource.rows.push(s[0]):this._droppedClass=="e-schemaColumn"?this.model.pivotControl.model.dataSource.columns.push(s[0]):this._droppedClass=="e-schemaValue"?this.model.pivotControl._dataModel=="XMLA"?this.model.pivotControl.model.dataSource.values[0].measures.push(s[0]):this.model.pivotControl.model.dataSource.values.push(s[0]):this.model.pivotControl.model.dataSource.filters==null?(this.model.pivotControl.model.dataSource.filters=[],this.model.pivotControl.model.dataSource.filters.push(s[0])):this.model.pivotControl.model.dataSource.filters.push(s[0]);this.model.pivotControl._dataModel=="Pivot"&&this.model.pivotControl._calculatedField.length>0&&d!="value"&&(this.model.pivotControl._calcFieldNodeDrop(s[0]),this.model.pivotControl.model.dataSource.values=n.grep(this.model.pivotControl.model.dataSource.values,function(n){return t.isNullOrUndefined(n.isCalculatedField)||n.isCalculatedField==!1||n.isCalculatedField==!0&&n.formula.indexOf(s[0].fieldName)==-1}));this.model.pivotControl._dataModel=="XMLA"?(this._droppedClass=="e-schemaFilter"&&(this.model.pivotControl.model.dataSource.filters=n.map(this.model.pivotControl.model.dataSource.filters,function(n){return n.advancedFilter=[],n})),t.olap.base.getJSONData({action:"nodeDropped"},this.model.pivotControl.model.dataSource,this.model.pivotControl)):(this._trigger("fieldItemDropped",{axis:d,fieldItem:e}),this.model.pivotControl.model.editCellsInfo={},this.model.pivotControl._populatePivotGrid())}else y=!1,this._tempFilterData!=null&&n.each(this._tempFilterData,function(t,i){n.each(i,function(t,i){t==n(e).attr("id")&&i.length>0&&(y=!0)})}),y&&n(n(e).find(".e-text")[0]).after(t.buildTag("span.e-icon").addClass("filter")[0].outerHTML),this._dataModel=="Pivot"&&this.model.pivotControl._calculatedField.length>0&&this._droppedClass!="e-schemaValue"&&this.model.pivotControl._calculatedFieldNodeRemove(f),this._createPivotButton({fieldName:f.id,fieldCaption:o},this._droppedClass=="e-schemaRow"?"row":this._droppedClass=="e-schemaColumn"?"column":this._droppedClass=="e-schemaFilter"?"filter":this._droppedClass=="e-schemaValue"?"value":"",y,ft,h),g="",g=t.Pivot._getFilterParams(this._droppedClass,this._tempFilterData,o),this.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:"nodeDropped",element:this.element,customObject:this.model.pivotControl.model.customObject}),a=JSON.stringify({action:"nodeDropped",dropAxis:this._droppedClass+"::"+h,filterParams:g,headerTag:JSON.stringify(f),sortedHeaders:this.model.pivotControl._ascdes,currentReport:v,valueSorting:this.model.pivotControl.model.valueSortSettings,customObject:JSON.stringify(this.model.pivotControl.model.customObject)}),t.isNullOrUndefined(this.model.pivotControl._waitingPopup)||this.model.pivotControl._waitingPopup.show(),this.element.find(".schemaNoClick").addClass("freeze").width(n(this.element).width()).height(n(this.element).height()).css({top:n(this.element).offset().top,left:n(this.element).offset().left}),this.model.pivotControl.model.enableDeferUpdate?(this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeDropped,a.replace("nodeDropped","nodeDroppedDeferUpdate"),this._pvtNodeDroppedSuccess),t.isNullOrUndefined(this.model.pivotControl._waitingPopup)||this.model.pivotControl._waitingPopup.hide()):this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeDropped,a,this._pvtNodeDroppedSuccess)}else{if(l=i.dropTarget[0]!=r&&i.dropTarget[0].className=="e-pivotButton"?n(i.dropTarget).parents()[0]:i.dropTarget[0]!=r&&i.dropTarget[0].tagName.toLowerCase()=="td"&&!n(i.dropTarget.parents("table.e-pivotGridTable")[0]).length?i.dropTarget.children(":last")[0]:i.dropTarget,this._droppedClass=l[0].className.split(" ")[0],p="",w="",p=l[0].className.split(" ")[0],n(i.dropTarget.parents("table.e-pivotGridTable")[0]).length){if(w=n(l).hasClass("rowheader")?"Series":n(l).hasClass("e-grpRow")?"Series":n(l).hasClass("e-rows")?"Series":n(l).hasClass("colheader")?"Categorical":n(l).hasClass("columns")?"Categorical":n(l).hasClass("value")?"Slicer":n(l).hasClass("e-drag")?"Slicer":"",n(i.droppedElement).attr("data-tag").indexOf("Measures")>0&&w=="Slicer")return this._createErrorDialog(),!1}else w=p=="columns"?"Categorical":p=="e-schemaColumn"?"Categorical":p=="e-rows"?"Series":p=="e-schemaRow"?"Series":p=="e-drag"?"Slicer":p=="e-schemaFilter"?"Slicer":"";w==""&&(w=this.element.find(".e-schemaRow .e-pivotButton:contains('Measures')").length>0||this.element.find(".e-schemaRow .e-pivotButton:contains('MEASURES')").length>0?"Series":"Categorical");h=t.isNullOrUndefined(i.event)?"":this._setSplitBtnTargetPos(i.event);y=!1;this.model.pivotControl._tempFilterData!=null&&n.each(this.model.pivotControl._tempFilterData,function(t,i){n.each(i,function(n,t){n==f.replace(/[\[\]']/g,"")&&t.length>0&&(y=!0)})});y&&n(n(e).find(".e-text")[0]).after(t.buildTag("span.e-icon").addClass("filter")[0].outerHTML);f=f=="Value"||f=="Goal"||f=="Status"||f=="Trend"?f+":"+n(i.droppedElement).parents("li:eq(0)").attr("data-tag")+":KPI":n(e).parents("[data-tag='KPI']").length>0?f+":KPI":f;ct=this._currentCubeName+"--"+f+"--"+w+"--"+h;g="";this.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:"nodeDropped",element:this.element,customObject:this.model.pivotControl.model.customObject});a=JSON.stringify({action:"nodeDropped",dropType:"TreeNode",nodeInfo:ct,filterParams:g,currentReport:v,gridLayout:this.model.pivotControl.model.layout,customObject:JSON.stringify(this.model.pivotControl.model.customObject)});this.model.pivotControl.model.enableDeferUpdate||t.isNullOrUndefined(this.model.pivotControl._waitingPopup)||this.model.pivotControl._waitingPopup.show();this.element.find(".schemaNoClick").addClass("freeze").width(n(this.element).width()).height(n(this.element).height()).css({top:n(this.element).offset().top,left:n(this.element).offset().left});this.model.pivotControl.model.enableDeferUpdate?this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeDropped,a.replace("nodeDropped","nodeDroppedDeferUpdate"),this._droppedSuccess):this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeDropped,a,this._droppedSuccess)}}this._setPivotBtnWidth()}},_addTreeDropIcon:function(i){this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(n(args.currentElement).attr("data-tag").toLowerCase().indexOf("[measures]")<0&&!(n(args.currentElement).find(".e-namedSetCDB").length>0)?i.after(t.buildTag("span.e-icon").css("display","none").addClass("treeDrop")[0].outerHTML):i.after(t.buildTag("span.e-icon").css("display","none").addClass("treeDrop")[0].outerHTML))},_removeUndefinedFields:function(n){for(var i in n)(i=="droppedFieldFormat"||i=="droppedFieldFormatString")&&(t.isNullOrUndefined(n[i])||n[i]=="")&&delete n[i];return n},_clientPivotCheckedStateModified:function(i){var u,s,f;if(t.PivotAnalysis._valueSorting=null,!this._isDropAction){var r="",e="",o="";t.isNullOrUndefined(this.model.pivotControl)||t.isNullOrUndefined(this.model.pivotControl._waitingPopup)||this.model.pivotControl._waitingPopup.show();this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot?(r=n.grep(this.model.pivotTableFields,function(t){return t.name.replace(/ /g,"_")==n(i.currentElement).attr("id")})[0].name,e=n.grep(this.model.pivotTableFields,function(t){return t.name.replace(/ /g,"_")==n(i.currentElement).attr("id")})[0].format,o=n.grep(this.model.pivotTableFields,function(t){return t.name.replace(/ /g,"_")==n(i.currentElement).attr("id")})[0].formatString):r=n(i.currentElement).attr("data-tag"):r=n(i.currentElement).find("a")[0].textContent;i.type=="nodeUncheck"?(delete this.model.pivotControl._fieldMembers[r.toLowerCase()],delete this.model.pivotControl._fieldSelectedMembers[r.toLowerCase()],t.Pivot.removeReportItem(this.model.pivotControl.model.dataSource,r,!1),n(i.currentElement).removeClass("filter").find(".treeDrop,.filter").remove()):(u="row",this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&n(i.currentElement).attr("data-tag").toLocaleLowerCase().indexOf("[measures]")>=0&&(u="value"),(this.model.pivotControl.model.analysisMode!=t.Pivot.AnalysisMode.Olap||n(i.currentElement).attr("data-tag").toLowerCase().indexOf("[measures]")<0&&n(i.currentElement).find(".e-namedSetCDB").length==0)&&n(i.currentElement.find(".e-text")).after(t.buildTag("span.e-icon").css("display","none").addClass("treeDrop")[0].outerHTML),s=n(i.currentElement).parents("[data-tag='folderStruct']").length>0&&(n(i.currentElement).find("a")[0].textContent==this._getLocalizedLabels("Status")||n(i.currentElement).find("a")[0].textContent==this._getLocalizedLabels("Trend")||n(i.currentElement).find("a")[0].textContent==this._getLocalizedLabels("Goal")||n(i.currentElement).find("a")[0].textContent==this._getLocalizedLabels("Value"))?n(i.currentElement).parents("li:eq(0)").find("a:eq(0)").text()+" "+n(i.currentElement).find("a")[0].textContent:n(i.currentElement).find("a")[0].textContent,f={droppedFieldName:r,droppedFieldCaption:s,droppedFieldFormat:e,droppedFieldFormatString:o,droppedClass:u,droppedPosition:"",isMeasuresDropped:r.toLocaleLowerCase().indexOf("measures")==0},this._removeUndefinedFields(f),t.Pivot.addReportItem(this.model.pivotControl.model.dataSource,f));this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&t.olap.base.clearDrilledItems(this.model.pivotControl.model.dataSource,{action:"nodeDropped"},this.model.pivotControl);this._refreshPivotButtons();this.model.pivotControl.refreshControl();this._setPivotBtnWidth()}},_clientOnNodeDropped:function(i){var a,c,u,f,v,y,h,o,l,r,k,d,p,rt;t.PivotAnalysis._valueSorting=null;this._isDragging=!1;var w=!1,b=!1,g=this,e=n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejPivotClient"):null;if(this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot)for(a=n(i.dropTarget).parents(),c=0;c<a.length;c++)a[c].className.split(" ")[0]=="e-pivotgrid"&&(w=!0);if(u=this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot?n.grep(this.model.pivotTableFields,function(n){return n.name.replace(/ /g,"_")==i.droppedElement.attr("id")})[0].name:i.droppedElement.attr("data-defaultHierarchy")||(n(i.droppedElement).find("a:eq(0) span[class^='level'],div[class*='level']").length>0?n(i.droppedElement).parents("li:eq(0)").attr("data-tag"):i.droppedElement.attr("data-tag")),f=n(i.droppedElement).find("div:first").text(),b=(f==this._getLocalizedLabels("Goal")||f==this._getLocalizedLabels("Status")||f==this._getLocalizedLabels("Value")||f==this._getLocalizedLabels("Trend"))&&n(i.droppedElement).parents("[data-tag='folderStruct']").length>0,b&&(f=n(i.droppedElement).parents("li:eq(0)").attr("data-tag")+" "+n(i.droppedElement).find("div:first").text()),u==""&&f!=""&&(u=f),v=null,y=null,n(this.element).parents(".e-pivotclient").length>0&&n(i.droppedElement).length>0&&n(i.droppedElement).find(".e-calcMemberCDB").length>0){if(h=this.element.find(".e-schemaFieldTree").data("ejTreeView").getTreeData(n(i.droppedElement).attr("id")),i.dropTarget==""&&n(i.droppedElement).is("li")){n(i.droppedElement).siblings().length==0&&n(i.droppedElement).parents("li:eq(0)").children().find(".e-minus").hide();o="";this.model.pivotControl.model.calculatedMembers=n.grep(this.model.pivotControl.model.calculatedMembers,function(n){if(h[0].name!=n.caption)return n;o=h[0].expression});o!=""&&(this.model.pivotControl.model.dataSource.values[0].measures=this._reArrangeCalcFields(this.model.pivotControl.model.dataSource.values[0].measures,o).reportItem,this.model.pivotControl.model.dataSource.rows=this._reArrangeCalcFields(this.model.pivotControl.model.dataSource.rows,o).reportItem,this.model.pivotControl.model.dataSource.columns=this._reArrangeCalcFields(this.model.pivotControl.model.dataSource.columns,o).reportItem,this.model.pivotControl.refreshControl(),this.refreshControl(),l=this.element.find(".e-schemaFieldTree").data("ejTreeView").model.fields.dataSource,l=n.grep(l,function(t){if(t.id!=n(i.droppedElement).attr("id"))return t}),this.element.find(".e-schemaFieldTree").data("ejTreeView").model.fields.dataSource=l,n(i.droppedElement).remove());return}v=h[0].hierarchyUniqueName;y=h[0].expression}var r="",s=this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot?n.grep(this.model.pivotTableFields,function(n){return n.name.replace(/ /g,"_")==i.droppedElement.attr("id")})[0]:null,nt=t.isNullOrUndefined(s)?"":s.format,tt=t.isNullOrUndefined(s)?"":s.formatString,it=t.isNullOrUndefined(s)?!0:s.showSubTotal;if(r=n(i.dropTarget).hasClass("e-pvtBtn")||n(i.dropTarget).hasClass("e-pivotButton")?n(i.dropTarget).parents("div.e-droppable").hasClass("e-schemaColumn")?"column":n(i.dropTarget).parents("div.e-droppable").hasClass("e-schemaRow")?"row":n(i.dropTarget).parents("div.e-droppable").hasClass("e-schemaFilter")?"filter":n(i.dropTarget).parents("div.e-droppable").hasClass("e-schemaValue")?"value":"":i.dropTarget.hasClass("e-schemaColumn")?"column":i.dropTarget.hasClass("e-schemaRow")?"row":i.dropTarget.hasClass("e-schemaFilter")?"filter":i.dropTarget.hasClass("e-schemaValue")?"value":"",w&&(r=n(i.dropTarget).prop("tagName")=="TH"||n(i.dropTarget).prop("tagName")=="TD"?n(i.dropTarget).hasClass("rowheader")||n(i.dropTarget).hasClass("e-grpRow")?"row":n(i.dropTarget).hasClass("colheader")?"column":n(i.dropTarget).hasClass("value")?"value":"":n(i.dropTarget).hasClass("e-rows")||n(i.dropTarget).hasClass("emptyRows")?"row":n(i.dropTarget).hasClass("columns")?"column":n(i.dropTarget).hasClass("values")?"value":n(i.dropTarget).hasClass("e-drag")?"filter":""),r=="")return!1;if(k=t.isNullOrUndefined(i.event)?"":this._setSplitBtnTargetPos(i.event),d=n(i.dropTarget).hasClass("value")||n(i.dropTarget).hasClass("colheader")||n(i.dropTarget).hasClass("rowheader")||n(i.dropTarget).hasClass("summary")||n(i.dropTarget).parents("table:eq(0)").hasClass("e-pivotGridTable")||n(i.dropTarget).parents(".groupingBarPivot").length>0,u.toLowerCase().indexOf("[measures]")>=0&&r!="value"||u==this._getLocalizedLabels("Measures")&&r!="row"&&r!="column"||this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&u.toLowerCase().indexOf("[measures]")<0&&r=="value"||d&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap){this._createErrorDialog();return}this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(this.model.pivotControl._fieldMembers={},this.model.pivotControl._fieldSelectedMembers={},u.toLowerCase().indexOf("[measures")>=0&&(this.model.pivotControl.model.dataSource.values[0].axis=r=="column"?"columns":r=="row"?"rows":this.model.pivotControl.model.dataSource.values[0].axis,r="value"));t.isNullOrUndefined(e)?this.model.pivotControl._waitingPopup.show():(e._isTimeOut=!0,setTimeout(function(){e._isTimeOut&&g.model.pivotControl._waitingPopup.show()},800));p={droppedFieldName:u,droppedFieldCaption:f,droppedFieldFormat:nt,droppedFieldFormatString:tt,summaryType:i.droppedElement.attr("summarytype"),droppedFieldShowSubTotal:it,droppedClass:r,droppedPosition:k,isMeasuresDropped:u.toLocaleLowerCase().indexOf("measures")==0,droppedExpression:y,droppedHierarchyUniqueName:v};this._removeUndefinedFields(p);t.Pivot.addReportItem(this.model.pivotControl.model.dataSource,p);this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(t.olap.base.clearDrilledItems(this.model.pivotControl.model.dataSource,{action:"nodeDropped"},this.model.pivotControl),this.model.pivotControl.model.dataSource.rows=this._reArrangeCalcFields(this.model.pivotControl.model.dataSource.rows).reportItem,this.model.pivotControl.model.dataSource.columns=this._reArrangeCalcFields(this.model.pivotControl.model.dataSource.columns).reportItem);this.model.pivotControl.element.hasClass("e-pivotclient")&&this.model.pivotControl._pivotChart&&(this.model.pivotControl._pivotChart._labelCurrentTags={});rt=i.droppedElement;this._isDropAction=!0;r==""?this._tableTreeObj.uncheckNode(i.droppedElement):(n(i.droppedElement).find(".treeDrop").length==0&&this.element.parents(".e-pivotclient").length==0&&(this.model.pivotControl.model.analysisMode!=t.Pivot.AnalysisMode.Olap||n(i.droppedElement).attr("data-tag").toLowerCase().indexOf("[measures]")<0&&n(i.droppedElement).find(".e-namedSetCDB").length==0)&&n(i.droppedElement.find(".e-text")).after(t.buildTag("span.e-icon").css("display","none").addClass("treeDrop")[0].outerHTML),this._tableTreeObj.checkNode(i.droppedElement));this._isDropAction=!1;this._refreshPivotButtons();this.model.pivotControl.refreshControl();this._setPivotBtnWidth();t.isNullOrUndefined(e)||e._isTimeOut&&(e._isTimeOut=!1)},_clientOnPvtBtnDropped:function(i){var l,f,p,w,u,b,o,s,h,e,k,c;t.PivotAnalysis._valueSorting=null;l=this;f=n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejPivotClient"):null;t.isNullOrUndefined(this.model.pivotControl)||t.isNullOrUndefined(this.model.pivotControl._waitingPopup)||(t.isNullOrUndefined(f)?this.model.pivotControl._waitingPopup.show():(f._isTimeOut=!0,setTimeout(function(){f._isTimeOut&&f.element.find(".e-errorDialog:visible").length==0&&l.model.pivotControl._waitingPopup.show()},800)));n(i.target).hasClass("e-removeClientPivotBtn")&&(i.target=n(i.target).parent());this._isDragging=!1;n("#"+this._id+"_dragClone").remove();var a=!1,r="",v="",y="";if(this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot?(r=n.grep(this.model.pivotTableFields,function(t){return t.name==n(i.element).attr("data-fieldName")})[0].name,v=n.grep(this.model.pivotTableFields,function(t){return t.name==n(i.element).attr("data-fieldName")})[0].format,y=n.grep(this.model.pivotTableFields,function(t){return t.name==n(i.element).attr("data-fieldName")})[0].formatString):(r=n(i.element).attr("data-fieldName"),n(this.element).parents(".e-pivotclient").length>0&&(p=n.map(this.model.pivotControl.model.calculatedMembers,function(n){if(n.caption==r)return n}),p.length>0&&(a=!0))):r=i.element.text(),w=i.element.text(),t.isNullOrUndefined(this.model.pivotControl._fieldMembers)||(delete this.model.pivotControl._fieldMembers[r.toLowerCase()],delete this.model.pivotControl._fieldSelectedMembers[r.toLowerCase()]),u="",u=n(i.target).hasClass("e-pvtBtn")||this.element.parents(".e-pivotclient").length>0&&n(i.target).hasClass("e-pivotButton")?n(i.target).parents("div.e-droppable").hasClass("e-schemaColumn")?"column":n(i.target).parents("div.e-droppable").hasClass("e-schemaRow")?"row":n(i.target).parents("div.e-droppable").hasClass("e-schemaFilter")?"filter":n(i.target).parents("div.e-droppable").hasClass("e-schemaValue")?"value":"":n(i.target).hasClass("e-schemaColumn")?"column":n(i.target).hasClass("e-schemaRow")?"row":n(i.target).hasClass("e-schemaFilter")?"filter":n(i.target).hasClass("e-schemaValue")?"value":"",a&&u=="filter"||r.toLowerCase().indexOf("[measures]")>=0&&u!="value"&&(u=="row"||u=="column"||u=="filter")||r==this._getLocalizedLabels("Measures")&&u!="row"&&u!="column"&&(u=="value"||u=="filter")||this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&r.toLowerCase().indexOf("[measures]")<0&&u=="value"){this._createErrorDialog();i.element.removeClass("dragHover").parent().removeClass("dragHover");return}if(this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.model.pivotControl._calculatedField!=null&&u!="value"&&n.grep(this.model.pivotControl._calculatedField,function(n){return n.name==r}).length>0){t.Pivot._createErrorDialog(this.model.pivotControl._getLocalizedLabels("CalcValue"),this.model.pivotControl._getLocalizedLabels("Warning"),this);i.element.removeClass("dragHover").parent().removeClass("dragHover");this.element.find(".e-dropIndicator").removeClass("e-dropIndicatorHover");this.model.pivotControl._waitingPopup.hide();return}if(b=t.isNullOrUndefined(i.event)?"":this._setSplitBtnTargetPos(i.event),o={droppedFieldName:r,droppedFieldCaption:w,droppedFieldFormat:v,droppedFieldFormatString:y,droppedClass:u,droppedPosition:b,isMeasuresDropped:r.toLocaleLowerCase().indexOf("measures")==0},this._removeUndefinedFields(o),this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(r==this._getLocalizedLabels("Measures")||r=="[Measures]")&&(s=this.model.pivotControl.model.dataSource.values[0].measures),t.Pivot.addReportItem(this.model.pivotControl.model.dataSource,o),this.model.pivotControl.model.dataSource.rows=this._reArrangeCalcFields(this.model.pivotControl.model.dataSource.rows).reportItem,this.model.pivotControl.model.dataSource.columns=this._reArrangeCalcFields(this.model.pivotControl.model.dataSource.columns).reportItem,this.model.pivotControl._calculatedField!=null&&this.model.pivotControl._calculatedField.length>0&&u!="value")for(h=n.grep(this.model.pivotControl.model.dataSource.values,function(n){return n.isCalculatedField==!0&&n.formula.indexOf(r)>-1}),e=0;e<h.length;e++)this._tableTreeObj.uncheckNode(h[e].fieldName);this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.model.pivotControl._calculatedField!=null&&this.model.pivotControl._calculatedField.length>0&&(k=n.grep(this.model.pivotControl.model.dataSource.values,function(n){return n.isCalculatedField==!0}),this.model.pivotControl.model.dataSource.values=n.grep(this.model.pivotControl.model.dataSource.values,function(n){return t.isNullOrUndefined(n.isCalculatedField)||n.isCalculatedField==!1}),n.merge(this.model.pivotControl.model.dataSource.values,k));this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(this.model.pivotControl._fieldMembers={},this.model.pivotControl._fieldSelectedMembers={},t.olap.base.clearDrilledItems(this.model.pivotControl.model.dataSource,{action:"nodeDropped"},this.model.pivotControl));this.model.pivotControl.element.hasClass("e-pivotclient")&&this.model.pivotControl._pivotChart&&(this.model.pivotControl._pivotChart._labelCurrentTags={});u==""&&(this._isDropAction=!0,this._tableTreeObj.element.find("li[data-tag='"+r+"']").removeClass("filter").find(".treeDrop,.filter").remove(),t.isNullOrUndefined(s)?this._tableTreeObj.uncheckNode(this._tableTreeObj.element.find("li[data-tag='"+r+"']")):(c=this,n.grep(s,function(n){c._tableTreeObj.uncheckNode(c._tableTreeObj.element.find("li[data-tag='"+n.fieldName+"']"))})),this._isDropAction=!1);this._refreshPivotButtons();this.model.pivotControl.refreshControl();this._setPivotBtnWidth()},_reArrangeCalcFields:function(i,u){var f=[],e=[],i=n.grep(i,function(n){if(t.isNullOrUndefined(u))if(n.expression!=r)e.push({caption:n.fieldName,expression:n.expression,hierarchyUniqueName:n.hierarchyUniqueName,formatString:n.formatString?n.formatString:n.format,memberType:"Dimension"}),f.push(n);else return n;else if(n.expression!=u)return n});return{reportItem:n.merge(f,i),calcMems:e}},_refreshPivotButtons:function(){var i,r;this.element.find(".e-axisTable .e-schemaFilter").html(this.model.layout=="normal"?this._createPivotButtons_1(this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?this.model.pivotControl.model.dataSource.filters:JSON.parse(this.model.pivotControl.getOlapReport()).Filters,"filter"):this._createPivotButtons("filters",this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?this.model.pivotControl.model.dataSource.filters:JSON.parse(this.model.pivotControl.getOlapReport()).Filters));this.element.find(".e-axisTable .e-schemaRow").html(this.model.layout=="normal"?this._createPivotButtons_1(this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?this.model.pivotControl.model.dataSource.rows:JSON.parse(this.model.pivotControl.getOlapReport()).PivotRows,"row"):this._createPivotButtons("rows",this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?this.model.pivotControl.model.dataSource.rows:JSON.parse(this.model.pivotControl.getOlapReport()).PivotRows));this.element.find(".e-axisTable .e-schemaColumn").html(this.model.layout=="normal"?this._createPivotButtons_1(this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?this.model.pivotControl.model.dataSource.columns:JSON.parse(this.model.pivotControl.getOlapReport()).PivotColumns,"column"):this._createPivotButtons("columns",this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?this.model.pivotControl.model.dataSource.columns:JSON.parse(this.model.pivotControl.getOlapReport()).PivotColumns));this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.pivotControl.model.dataSource.values.length>0&&!t.isNullOrUndefined(this.model.pivotControl.model.dataSource.values[0].measures)&&this.model.pivotControl.model.dataSource.values[0].measures.length>0&&(this.model.pivotControl.model.dataSource.values[0].axis=="columns"?this.element.find(".e-axisTable .e-schemaColumn").append(this.model.layout=="normal"?this._createPivotButtons_1([{fieldName:"Measures",fieldCaption:this._getLocalizedLabels("Measures")}],"column"):this._createPivotButtons("columns",[{fieldName:"Measures",fieldCaption:this._getLocalizedLabels("Measures")}])):this.model.pivotControl.model.dataSource.values[0].axis=="rows"&&this.element.find(".e-axisTable .e-schemaRow").append(this.model.layout=="normal"?this._createPivotButtons_1([{fieldName:"Measures",fieldCaption:this._getLocalizedLabels("Measures")}],"row"):this._createPivotButtons("rows",[{fieldName:"Measures",fieldCaption:this._getLocalizedLabels("Measures")}])));this.element.find(".e-axisTable .e-schemaValue").html(this.model.layout=="normal"?this._createPivotButtons_1(this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?this.model.pivotControl.model.dataSource.values:JSON.parse(this.model.pivotControl.getOlapReport()).PivotCalculations,"value"):this._createPivotButtons("values",this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?this.model.pivotControl.model.dataSource.values:JSON.parse(this.model.pivotControl.getOlapReport()).PivotCalculations));this.model.enableDragDrop&&this.element.find(".e-pivotButton .e-pvtBtn").ejButton({size:"normal",type:t.ButtonType.Button,enableRTL:this.model.enableRTL}).ejDraggable({handle:"button",clone:!0,cursorAt:{left:-5,top:-5},dragStart:t.proxy(function(){this._isDragging=!0},this),dragStop:t.proxy(this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this._pvtBtnDropped:this._clientOnPvtBtnDropped,this),helper:t.proxy(function(t){if(n(t.element).addClass("dragHover"),t.sender.target.className.indexOf("e-btn")>-1){var i=n(t.sender.target).clone().attr("id",this._id+"_dragClone").appendTo("body");return n("#"+this._id+"_dragClone").removeAttr("style").height(n(t.sender.target).height()),i}return!1},this)});this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(this.model.olap.showKPI||this.model.olap.showKpi)&&!t.isNullOrUndefined(this._tableTreeObj)&&this.element.find(".e-pvtBtn[data-fieldName=KPI]").length>0&&(i=this._tableTreeObj.element.find("li[data-tag=KPI]"),n(i).length>0&&(r=t.buildTag("span.e-icon").css("display","none").addClass("treeDrop")[0].outerHTML,n(n(i).find(".e-text")[0]).after(r)));this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&this.model.pivotControl.model.layout==t.PivotSchemaDesigner.Layouts.OneByOne&&this.element.find(".e-pvtBtn").css("max-width",this.element.find(".e-schemaColumn").width()-this.element.find(".e-removeClientPivotBtn").width()-6);this._createContextMenu();this.model.pivotControl.model.isResponsive&&this.model.pivotControl.model.enableSplitter&&!t.isNullOrUndefined(this.model.pivotControl.element.find(".splitresponsive").data("ejSplitter"))&&this.model.pivotControl.element.find(".splitresponsive").data("ejSplitter").refresh();this.model.pivotControl.model.showUniqueNameOnPivotButton&&n(".pvtBtnDiv").addClass("e-schemaBtnUnique");this.element.parents(".e-pivotclient").length>0?this._setSplitButtonTitle():this._setPivotBtnWidth()},_setSplitButtonTitle:function(){var i,r,f,u;if(this.element.find(".e-pvtBtn").length>0&&this._tableTreeObj!=null)for(i=0;i<this.element.find(".e-pvtBtn").length;i++)r=this.element.find(".e-pvtBtn:eq("+i+")").attr("data-fieldname"),this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap?(f=this._tableTreeObj.element.find("li[data-tag='"+r+"']").parents("li:eq(0)").find("a:eq(0)").text(),u=this.element.find(".e-pvtBtn:eq("+i+")").text(),r.toLowerCase().indexOf("measures")>=0?this.element.find(".e-pvtBtn:eq("+i+")").attr("title",u):this.element.find(".e-pvtBtn:eq("+i+")").attr("title",f+" - "+u)):this.element.find(".e-pvtBtn:eq("+i+")").attr("title",n.grep(this.model.pivotTableFields,function(n){return n.id==r})[0].caption)},_createPivotButtons:function(i,u){var a="",p=i,h,c,l,i,f,o;if(t.isNullOrUndefined(this.model.pivotControl)||this.model.pivotControl.model.operationalMode!=t.Pivot.OperationalMode.ClientMode){if(i=="values"&&(i=this.element.find(".e-pivotButton:contains("+this._getLocalizedLabels("Measures")+")").length>0?this.element.find(".e-pivotButton:contains('"+this._getLocalizedLabels("Measures")+"')").parent()[0].className.split(" ")[0]:"e-schemaColumn",i=i=="e-schemaColumn"?"columns":"rows",this.element.parents(".e-pivotclient").length!=0))for(f=0;f<u.length;f++)u[f].SummaryType!="null"&&(u[f].SummaryType=="Sum"?u[f].FieldHeader=this._getLocalizedLabels("Sum")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="Average"?u[f].FieldHeader=this._getLocalizedLabels("Average")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="Count"?u[f].FieldHeader=this._getLocalizedLabels("Count")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="Min"?u[f].FieldHeader=this._getLocalizedLabels("Min")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="Max"?u[f].FieldHeader=this._getLocalizedLabels("Max")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="DoubleTotalSum"?u[f].FieldHeader=this._getLocalizedLabels("DoubleSum")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="DoubleAverage"?u[f].FieldHeader=this._getLocalizedLabels("DoubleAverage")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="DoubleMinimum"?u[f].FieldHeader=this._getLocalizedLabels("DoubleMin")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="DoubleMaximum"?u[f].FieldHeader=this._getLocalizedLabels("DoubleMax")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="DoubleStandardDeviation"?u[f].FieldHeader=this._getLocalizedLabels("DoubleStandardDeviation")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="DoubleVariance"?u[f].FieldHeader=this._getLocalizedLabels("DoubleVariance")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="DecimalTotalSum"?u[f].FieldHeader=this._getLocalizedLabels("DecimalSum")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="IntTotalSum"?u[f].FieldHeader=this._getLocalizedLabels("IntSum")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="CountNumbers"?u[f].FieldHeader=this._getLocalizedLabels("CountNumbers")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="StdDev"?u[f].FieldHeader=this._getLocalizedLabels("StdDev")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="StdDevP"?u[f].FieldHeader=this._getLocalizedLabels("StdDevP")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="Var"?u[f].FieldHeader=this._getLocalizedLabels("Variance")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader:u[f].SummaryType=="VarP"&&(u[f].FieldHeader=this._getLocalizedLabels("VarP")+" "+this._getLocalizedLabels("SummaryOf")+" "+u[f].FieldHeader));for(f=0;f<u.length;f++)o="",u[f].FieldHeader=u[f].FieldHeader=="Measures"?this._getLocalizedLabels("Measures"):u[f].FieldHeader,t.isNullOrUndefined(this.model.pivotControl)||i!="filters"&&i!="slicers"||(o=t.Pivot._getFilterState("",[],u[f],this.model.pivotControl),i=this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap?"slicers":i),a+=t.buildTag("div.e-pivotButton",t.buildTag("div.e-dropIndicator")[0].outerHTML+(n(this.element).parents(".e-pivotclient").length>0?t.buildTag("button.e-pvtBtn#"+this._id+"_pivotButton"+(u[f].FieldHeader||u[f].FieldName||u[f].DimensionHeader||u[f].DimensionName||u[f].Name),(this.model.pivotControl.model.showUniqueNameOnPivotButton?i!="values"?u[f].Tag:u[f].FieldHeader||u[f].FieldName||u[f].DimensionHeader||u[f].DimensionName||u[f].Name:u[f].FieldHeader||u[f].FieldName||u[f].DimensionHeader||u[f].DimensionName||u[f].Name)+((i=="filters"||i=="slicers")&&o!=""?" ("+o+")":""),{},{"data-fieldName":u[f].FieldName||u[f].DimensionName||u[f].FieldHeader||u[f].DimensionHeader||u[f].Name||u[f],"data-fieldCaption":u[f].FieldHeader||u[f].DimensionHeader||u[f].FieldName||u[f].DimensionName,"data-axis":i,"data-parentuniquename":u[f].ParentHierarchy})[0].outerHTML:t.buildTag("div.pvtBtnDiv",t.buildTag("button.e-pvtBtn#"+this._id+"_pivotButton"+(u[f].FieldHeader||u[f].FieldName||u[f].DimensionHeader||u[f].DimensionName||u[f].Name),(this.model.pivotControl.model.showUniqueNameOnPivotButton?p!="values"?u[f].Tag:u[f].FieldHeader||u[f].FieldName||u[f].DimensionHeader||u[f].DimensionName||u[f].Name:u[f].FieldHeader||u[f].FieldName||u[f].DimensionHeader||u[f].DimensionName||u[f].Name)+((i=="filters"||i=="slicers")&&o!=""?" ("+o+")":""),{},{"data-fieldName":u[f].FieldName||u[f].DimensionName||u[f].FieldHeader||u[f].DimensionHeader||u[f].Name||u[f],"data-fieldCaption":u[f].FieldHeader||u[f].DimensionHeader||u[f].FieldName||u[f].DimensionName,"data-axis":i,"data-parentuniquename":u[f].ParentHierarchy})[0].outerHTML).attr("data-tag",i+":"+u[f].Tag)[0].outerHTML)+t.buildTag("span."+(this.model.layout==t.PivotSchemaDesigner.Layouts.OneByOne?"e-removeClientPivotBtn":"e-removePivotBtn"),{},{display:this.model.layout!=t.PivotSchemaDesigner.Layouts.OneByOne?"none":"inline-block"}).addClass("e-icon")[0].outerHTML).attr("data-tag",i+":"+u[f].Tag)[0].outerHTML}else for(i=="values"&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(u=n.map(u,function(n){return n.measures})),f=0;f<u.length;f++){var y=this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap?"":u[f].fieldName,e=n.grep(this.model.pivotTableFields,function(n){return n.name==u[f].fieldName}),s=n.grep(this.model.pivotTableFields,function(n){return n.name==u[f]});e.length>0&&(e=e[0].caption);s.length>0&&(s=s[0].caption);var o="",v=this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?!0:!1,w=v?"":t.PivotAnalysis.getMembers(u[f].fieldName),b=u[f].filterItems!=null&&u[f].filterItems.values.length>0?"filtered":"";if(i=="filters"&&(b=="filtered"?o=t.Pivot._getFilterState(v,w,u[f],this.model.pivotControl):v?(t.isNullOrUndefined(t.olap.base.olapCtrlObj)&&(t.olap.base.olapCtrlObj=this.model.pivotControl),t.olap._mdxParser.getAllMember(this.model.pivotControl.model.dataSource,u[f].fieldName,this.model.pivotControl),o=this.model.pivotControl._allMember):o=this.model.pivotControl._getLocalizedLabels("All")),i=="values"&&this.element.parents(".e-pivotclient").length!=0){for(u[f].summaryType!=null&&(h=e||s,u[f].summaryType=="sum"?e=this._getLocalizedLabels("Sum")+" "+this._getLocalizedLabels("SummaryOf")+" "+h:u[f].summaryType=="average"?e=this._getLocalizedLabels("Average")+" "+this._getLocalizedLabels("SummaryOf")+" "+h:u[f].summaryType=="count"?e=this._getLocalizedLabels("Count")+" "+this._getLocalizedLabels("SummaryOf")+" "+h:u[f].summaryType=="min"?e=this._getLocalizedLabels("Min")+" "+this._getLocalizedLabels("SummaryOf")+" "+h:u[f].summaryType=="max"&&(e=this._getLocalizedLabels("Max")+" "+this._getLocalizedLabels("SummaryOf")+" "+h)),c=0;c<u.length;c++)for(l=0;l<n(this.element).find("li").length;l++)n(this.element).find("li")[l].textContent==u[c].fieldCaption&&n(this.element.find("li")[l]).attr("summaryType",u[c].summaryType);u[f].summaryType==null&&(e=this._getLocalizedLabels("Sum")+" "+this._getLocalizedLabels("SummaryOf")+" "+e)}a+=t.buildTag("div.e-pivotButton",t.buildTag("div.e-dropIndicator")[0].outerHTML+(n(this.element).parents(".e-pivotclient").length>0?t.buildTag("button.e-pvtBtn#"+this._id+"_pivotButton"+y,(this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap?this.model.pivotControl.model.showUniqueNameOnPivotButton?i!="values"?u[f].fieldName.replace(/\[/g,"").replace(/\]/g,""):(u[f].fieldCaption!=r?u[f].fieldCaption:u[f].fieldName)||u[f]:(u[f].fieldCaption!=r?u[f].fieldCaption:u[f].fieldName)||u[f]:e||s)+(i=="filters"&&o!=""?" ("+o+")":""),{},{"data-fieldName":u[f].fieldName||u[f],"data-fieldCaption":u[f].fieldCaption||u[f].fieldName,"data-axis":i})[0].outerHTML:t.buildTag("div.pvtBtnDiv",t.buildTag("button.e-pvtBtn#"+this._id+"_pivotButton"+y,(this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap?this.model.pivotControl.model.showUniqueNameOnPivotButton?i!="values"?u[f].fieldName.replace(/\[/g,"").replace(/\]/g,""):(u[f].fieldCaption!=r?u[f].fieldCaption:u[f].fieldName)||u[f]:(u[f].fieldCaption!=r?u[f].fieldCaption:u[f].fieldName)||u[f]:e||s)+(i=="filters"&&o!=""?" ("+o+")":""),{},{"data-fieldName":u[f].fieldName||u[f],"data-fieldCaption":u[f].fieldCaption||u[f].fieldName,"data-axis":i})[0].outerHTML).attr("data-tag",i+":"+u[f].fieldName)[0].outerHTML)+t.buildTag("span."+(this.model.layout==t.PivotSchemaDesigner.Layouts.OneByOne?"e-removeClientPivotBtn":"e-removePivotBtn"),{},{display:this.model.layout!=t.PivotSchemaDesigner.Layouts.OneByOne?"none":"inline-block"}).addClass("e-icon")[0].outerHTML).attr("data-tag",i+":"+u[f].fieldName||u[f])[0].outerHTML}return a},_pvtBtnDropped:function(i){var o,w,u,e,v,h,rt,a,l,ut,k,d,ot,f,g,nt,ft,tt,s,et,it,st;if(this.element.find(".e-dropIndicator").removeClass("e-dropIndicatorHover"),n(i.element).parent().removeClass("dragHover"),n(i.element).removeClass("dragHover"),n("#"+this._id+"_dragClone").remove(),o="",w="",this._isDragging=!1,n(this.element).parents(".e-pivotclient").length>0&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot){for(n(i.target).hasClass("e-pivotButton")&&(i.target=i.target.childNodes[1]),u=t.isNullOrUndefined(i.target.className.indexOf)?"":n(i.target).attr("class")?i.target.className.indexOf("e-droppable")>=0?i.target.className.split(" ")[0]:i.target.className.split(" ")[0]=="e-pvtBtn"?n(i.target).parents("div.e-droppable").attr("class").split(" ")[0]:"":"",u.indexOf("e-")>-1&&(u=u.replace("e-","")),e=i.element.attr("data-fieldCaption")||i.element.attr("data-fieldName"),i.element.parents(".e-schemaValue").length>0&&(e=i.element.attr("data-fieldName")),v=i.element.parent().attr("data-tag").split(":")[1],h=t.isNullOrUndefined(i.event)?"":this._setSplitBtnTargetPos(i.event),s=0;s<this.model.pivotTableFields.length;s++)if(this.model.pivotTableFields[s].name==e){this.model.pivotTableFields[s].isSelected=!0;o=this.model.pivotTableFields[s];break}try{a=JSON.parse(this.model.pivotControl.getOlapReport()).Report}catch(ht){a=this.model.pivotControl.getOlapReport()}t.isNullOrUndefined(this.model.pivotControl._waitingPopup)||this.model.pivotControl._waitingPopup.show();u==""&&(delete this.model.pivotControl._fieldMembers[v],delete this.model.pivotControl._fieldSelectedMembers[v],this._clearFilterData(v));n(this.element).parents(".e-pivotclient").length>0&&this.model.pivotControl.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this.model.pivotControl._trigger("beforeServiceInvoke",{action:"nodeDropped",element:this.model.pivotControl.element,customObject:this.model.pivotControl.model.customObject});l=JSON.stringify({action:this.model.pivotControl.model.enableDeferUpdate?"nodeDroppedDeferUpdate":"nodeDropped",args:JSON.stringify({droppedFieldCaption:e,droppedFieldName:v,headerTag:o,dropAxis:u,droppedPosition:h,currentReport:a,sortedHeaders:t.isNullOrUndefined(this.model.pivotControl._ascdes)?"":this.model.pivotControl._ascdes})+"-##-"+JSON.stringify(this.model.pivotControl.model.valueSortSettings),customObject:JSON.stringify(this.model.pivotControl.model.customObject)});this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeDropped,l,this._pvtNodeDroppedSuccess);return}this.model.pivotControl._isUpdateRequired=!0;this._isDragging=!1;var c=null,y="",e,u,p=p=i.element.find("~ .filtered").length>0?!0:!1,b=!1;if(this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap)c=t.isNullOrUndefined(i.target.className)?i.target:i.target.className.indexOf("e-pvtBtn")>-1?n(i.target).parents(".e-droppable")[0]:i.target.className.indexOf("e-droppable")?i.target:i.target[0].tagName.toLowerCase()=="td"?i.target.children(":last")[0]:i.target,t.isNullOrUndefined(c.className)?(y=c.hasClass("e-schemaColumn")?"Categorical":c.hasClass("e-schemaRow")?"Series":c.hasClass("e-schemaFilter")?"Slicer":"",this._droppedClass=u=c.hasClass("e-schemaColumn")?"e-schemaColumn":c.hasClass("e-schemaRow")?"e-schemaRow":c.hasClass("e-schemaFilter")?"e-schemaFilter":c.hasClass("e-schemaValue")?"e-schemaValue":""):(y=c.className.split(" ")[0]=="e-schemaColumn"?"Categorical":c.className.split(" ")[0]=="e-schemaRow"?"Series":c.className.split(" ")[0]=="e-schemaFilter"?"Slicer":"",u=c.className.split(" ")[0],u.indexOf("e-")>-1&&(u=u.replace("e-",""))),y==""&&i.element.parent().attr("data-tag").toLowerCase().indexOf("[measures]")>-1&&(y=this.element.find(".e-schemaRow .e-pivotButton:contains('"+this._getLocalizedLabels("Measures")+"')").length>0?"Series":"Categorical"),this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode?e=o=n(i.element.parent()[0]).attr("data-tag").split(":")[1]:o=n(i.element.parent()[0]).attr("data-tag"),t.isNullOrUndefined(i.element.parent())||(w=i.element.parents(".e-schemaValue").length>0?"schemaValue":i.element.parents(".e-schemaFilter").length>0?"schemaFilter":i.element.parents(".e-schemaRow").length>0?"schemaRow":i.element.parents(".e-schemaColumn").length>0?"schemaColumn":"");else for(t.isNullOrUndefined(i.target.className)?u=n(i.target).hasClass("e-schemaColumn")?"schemaColumn":n(i.target).hasClass("e-schemaRow")?"schemaRow":n(i.target).hasClass("e-schemaFilter")?"schemaFilter":n(i.target).hasClass("e-schemaValue")?"schemaValue":"":(u=i.target.className.indexOf("e-droppable")>=0?i.target.className.split(" ")[0]:i.target.className.split(" ")[0]=="e-pvtBtn"?n(i.target).parents("div.e-droppable").attr("class").split(" ")[0]:"",u.indexOf("e-")>-1&&(u=u.replace("e-",""))),e=this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode?n.grep(this.model.pivotTableFields,function(n){return n.caption==i.element.text()})[0].name:i.element.attr("data-fieldCaption")||i.element.attr("title"),o,p=i.element.find("~ .e-filterIndicator").length>0?!0:!1,b=i.element.find("~ span:eq(0) span.descending").length>0?!0:!1,s=0;s<this.model.pivotTableFields.length;s++)this.model.pivotTableFields[s].name==e&&(this.model.pivotTableFields[s].isSelected=!0,o=this.model.pivotTableFields[s]);if(this._dataModel=="Olap"&&((o.split(":")[1].toLocaleUpperCase()=="MEASURES"||o.split(":")[1].toLocaleUpperCase()=="KPI")&&(u!="schemaColumn"||u!="schemaRow")&&(u=="schemaFilter"||u=="schemaValue")||o.toUpperCase().indexOf("[MEASURES]")>-1&&u!="schemaValue"&&(u=="schemaRow"||u=="schemaColumn"||u=="schemaFilter")||(o.toUpperCase().indexOf("[MEASURES]")==-1||o.toUpperCase().indexOf("KPI")==-1)&&u=="schemaValue"&&w!=u))return this._createErrorDialog(),!1;if(u=="schemaValue"||u=="schemaRow"||u=="schemaColumn"||u=="schemaFilter"){if(this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._dataModel=="Pivot"&&u!="schemaValue"&&(n.grep(this.model.pivotControl._calculatedField,function(n){return n.name==e}).length>0||n.grep(JSON.parse(this.model.pivotControl.getOlapReport()).PivotCalculations,function(n){return n.FieldHeader==e&&n.CalculationType==8}).length>0)){this.model.pivotControl._createErrorDialog(this.model.pivotControl._getLocalizedLabels("CalcValue"),this.model.pivotControl._getLocalizedLabels("Warning"));return}h=t.isNullOrUndefined(i.event)?"":this._setSplitBtnTargetPos(i.event);try{a=JSON.parse(this.model.pivotControl.getOlapReport()).Report}catch(ht){t.isNullOrUndefined(this.model.pivotControl.getOlapReport)||(a=this.model.pivotControl.getOlapReport())}if(this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode){if(this.model.pivotControl.dataSource=this.model.pivotControl._clearDrilledItems(this.model.pivotControl.model.dataSource,{action:"nodeDropped"}),ut=u=="schemaFilter"&&this.model.pivotControl._getNodeByUniqueName(e)!=null&&this.model.pivotControl._getNodeByUniqueName(e).find(".e-namedSetCDB").length>0,k=n(i.element.parent()).attr("data-tag").split(":")[0]=="Columns"&&this._droppedClass=="schemaColumn"&&o.toLowerCase()=="measures"||n(i.element.parent()).attr("data-tag").split(":")[0]=="Rows"&&this._droppedClass=="schemaRow"&&o.toLowerCase()=="measures",k||ut||e.toLowerCase().indexOf("[measures]")>=0&&u!="schemaValue"||u=="schemaValue"&&!(e.toLowerCase().indexOf("[measures]")>=0)||u=="schemaFilter"&&e.toLowerCase().indexOf("measures")>=0)return this._waitingPopup.hide(),k||this._createErrorDialog(),i.Cancel;if(e.toLowerCase().indexOf("measures")>=0&&!(e.toLowerCase().indexOf("[measures]")>=0))return n(i.element.parent().parent()).remove(),this.model.pivotControl.model.dataSource.values[0].axis=u=="schemaRow"?"rows":u=="schemaColumn"?"columns":"columns",this._createPivotButton({fieldName:"Measures",fieldCaption:this._getLocalizedLabels("Measures")},u=="schemaRow"?"row":u=="schemaColumn"?"column":"column","","",h),t.olap.base.getJSONData({action:"nodeDropped"},this.model.pivotControl.model.dataSource,this.model.pivotControl),this._waitingPopup.hide(),this._setPivotBtnWidth(),!1}if(this.model.pivotControl._dataModel=="Pivot"&&u!="schemaValue"&&n.grep(this.model.pivotControl._calculatedField,function(n){return n.name==e}).length>0){alert(this.model.pivotControl._getLocalizedLabels("CalcValue"));return}if(n(i.element.parent().parent()).remove(),this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode)this.model.pivotControl.model.enableDeferUpdate||t.isNullOrUndefined(this.model.pivotControl._waitingPopup)||this.model.pivotControl._waitingPopup.show(),this.element.find(".schemaNoClick").addClass("freeze").width(n(this.element).width()).height(n(this.element).height()).css({top:n(this.element).offset().top,left:n(this.element).offset().left}),this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap?(rt=this._currentCubeName+"--"+o+"--"+y+"--"+h,this.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:"nodeDropped",element:this.element,customObject:this.model.pivotControl.model.customObject}),l=JSON.stringify({action:"nodeDropped",dropType:"SplitButton",nodeInfo:rt,currentReport:a,gridLayout:this.model.pivotControl.model.layout,customObject:JSON.stringify(this.model.pivotControl.model.customObject)}),this.model.pivotControl.model.enableDeferUpdate?this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeDropped,l.replace("nodeDropped","nodeDroppedDeferUpdate"),this._droppedSuccess):this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeDropped,l,this._droppedSuccess)):(this._createPivotButton({fieldName:i.element.attr("data-fieldName")||i.element.attr("title"),fieldCaption:i.element.attr("data-fieldCaption")||i.element.attr("title")},u=="schemaRow"?"row":u=="schemaColumn"?"column":u=="schemaFilter"?"filter":u=="schemaValue"?"value":"",p,b,h),!t.isNullOrUndefined(this.model.pivotControl._calculatedField)&&this.model.pivotControl._calculatedField.length>0&&u!="schemaValue"&&this.model.pivotControl._calculatedFieldNodeRemove(o),this.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:"nodeDropped",element:this.element,customObject:this.model.pivotControl.model.customObject}),d="",d=t.Pivot._getFilterParams(u,this._tempFilterData,e),l=JSON.stringify({action:"nodeDropped",dropAxis:u+"::"+h,filterParams:d,headerTag:JSON.stringify(o),sortedHeaders:this.model.pivotControl._ascdes,currentReport:a,valueSorting:JSON.stringify(this.model.pivotControl.model.valueSortSettings),customObject:JSON.stringify(this.model.pivotControl.model.customObject)}),ot=this.model.pivotControl._renderControlSuccess,this.model.pivotControl.model.enableDeferUpdate?(this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeDropped,l.replace("nodeDropped","nodeDroppedDeferUpdate"),this._pvtNodeDroppedSuccess),t.isNullOrUndefined(this.model.pivotControl._ogridWaitingPopup)||this.model.pivotControl._ogridWaitingPopup.hide()):this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.nodeDropped,l,this._pvtNodeDroppedSuccess));else{if(f=this.model.pivotControl._getDroppedItem(e),g={},f.length>0&&(g=f[0].isNamedSets!=r?{fieldName:f[0].fieldName,fieldCaption:f[0].fieldCaption,isNamedSets:f[0].isNamedSets}:{fieldName:f[0].fieldName,fieldCaption:f[0].fieldCaption}),nt=u=="schemaRow"?"row":u=="schemaColumn"?"column":u=="schemaFilter"?"filter":u=="schemaValue"?"value":"",this._createPivotButton(g,nt,p,b,h),n.isNumeric(h)?u=="schemaRow"?this.model.pivotControl.model.dataSource.rows.splice(h,0,f[0]):u=="schemaColumn"?this.model.pivotControl.model.dataSource.columns.splice(h,0,f[0]):u=="schemaValue"?this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap?this.model.pivotControl.model.dataSource.values[0].measures.splice(h,0,f[0]):this.model.pivotControl.model.dataSource.values.splice(h,0,f[0]):this.model.pivotControl.model.dataSource.filters.splice(h,0,f[0]):u=="schemaRow"?this.model.pivotControl.model.dataSource.rows.push(f[0]):u=="schemaColumn"?this.model.pivotControl.model.dataSource.columns.push(f[0]):u=="schemaValue"?this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap?this.model.pivotControl.model.dataSource.values[0].measures.push(f[0]):this.model.pivotControl.model.dataSource.values.push(f[0]):this.model.pivotControl.model.dataSource.filters.push(f[0]),this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.model.pivotControl._calculatedField.length>0&&u!="schemaValue"){for(ft=n.grep(this.model.pivotControl.model.dataSource.values,function(n){return t.isNullOrUndefined(n.isCalculatedField)||n.isCalculatedField==!1||n.isCalculatedField==!0&&n.formula.indexOf(f[0].fieldName)==-1}),tt=n.grep(this.model.pivotControl.model.dataSource.values,function(n){return n.isCalculatedField==!0&&n.formula.indexOf(f[0].fieldName)>-1}),s=0;s<tt.length;s++)this._tableTreeObj.uncheckNode(tt[s].fieldName);this.model.pivotControl.model.dataSource.values=ft}this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(et=this.model.pivotControl.model.dataSource.values[0].axis=="columns"?"Columns:":"Rows:",it=this.element.find("div[data-tag='"+et+"Measures']:first"),it.appendTo(it.parent()),st=i.element.attr("data-axis").toLowerCase(),this.model.pivotControl.model.dataSource=this.model.pivotControl._clearDrilledItems(this.model.pivotControl.model.dataSource,{action:"nodeDropped"}));this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap?(t.isNullOrUndefined(this.model.pivotControl._ogridWaitingPopup)||this.model.pivotControl._ogridWaitingPopup.show(),t.olap.base.getJSONData({action:"nodeDropped"},this.model.pivotControl.model.dataSource,this.model.pivotControl)):(this._trigger("fieldItemDropped",{axis:nt,fieldItem:f}),this.model.pivotControl.model.editCellsInfo={},this.model.pivotControl._populatePivotGrid())}}else this._removePvtBtn(i);this._setPivotBtnWidth()},_createErrorDialog:function(){if(t.Pivot.openPreventPanel(this),this.element.find(".e-errorDialog").length==0){var n=t.buildTag("div.e-errorDialog#"+this._id+"_ErrorDialog",t.buildTag("div.warningImg")[0].outerHTML+t.buildTag("div.warningContent",this._getLocalizedLabels("AlertMsg"))[0].outerHTML+t.buildTag("div",t.buildTag("button#"+this._id+"_ErrOKBtn.e-errOKBtn","OK")[0].outerHTML)[0].outerHTML).attr("title",this._getLocalizedLabels("Warning"))[0].outerHTML;this.element.append(n);this.element.find(".e-errorDialog").ejDialog({target:"#"+this._id,enableResize:!1,enableRTL:this.model.enableRTL,width:"400px",close:t.proxy(t.Pivot.closePreventPanel,this)});this._errorDialog=this.element.find(".e-errorDialog").data("ejDialog");this.element.find(".e-errOKBtn").ejButton({type:t.ButtonType.Button,click:t.proxy(this._errOKBtnClick,this)});this.element.find(".e-dialog .e-close").attr("title",this._getLocalizedLabels("Close"))}else this._errorDialog.open()},_errOKBtnClick:function(){t.Pivot.closePreventPanel(this);this._errorDialog._ejDialog.find("div.e-dialog-icon").trigger("click")},_hideSchemaDesigner:function(){n("#"+this._id).animate({left:"-"+n("#"+this._id).width()+"px"},500,function(){});this.element.find(".collapseSchema").addClass("expandSchema").removeClass("collapseSchema")},_showSchemaDesigner:function(){n("#"+this._id).animate({left:"5px"});this.element.find(".expandSchema").addClass("collapseSchema").removeClass("expandSchema")},_reSizeHandler:function(){var r,o,s,i,u,f,e,h,c;this.model&&this.model.pivotControl&&(r=n("#"+this._id).width(),this.model.layout==t.PivotSchemaDesigner.Layouts.OneByOne?(this.element.find(".e-axisTd1, .e-axisTd2").css({width:"100%"}),this._pivotClientObj.model.isResponsive&&this._pivotClientObj.model.enableSplitter||(o=this._pivotClientObj.element.find("#"+this._id).width()/2,this._pivotClientObj.element.find("div.e-fieldTable").width(o-6),this._pivotClientObj.element.find("div.e-axisTable").width(o-5)),this._pivotClientObj.model.isResponsive&&this._pivotClientObj.element.find("div.e-axisTable").css({width:"52%"}),this.model.layout==t.PivotSchemaDesigner.Layouts.OneByOne&&this._pivotClientObj.model.enableSplitter&&(this._pivotClientObj.model.enableVirtualScrolling?this._pivotClientObj.element.find("div.e-axisTable").css({"padding-right":"12px"}):this._pivotClientObj.model.isResponsive?this._pivotClientObj.element.find("div.e-fieldTable").css({"padding-right":"2px"}):this._pivotClientObj.element.find("div.e-fieldTable").css({"padding-right":"1px"}))):(this.element.find("div.e-axisTable").width(r),s=(r-2)/2,this.element.find(".e-axisTd1").css({width:s-6}),this.element.find(".e-axisTd2").css({width:s}),this.element.find("div.parentSchemaFieldTree, .e-schemaFieldTree, div.e-fieldTable").css({width:r-23})),i=t.PivotSchemaDesigner.Layouts.OneByOne==this.model.layout?this._pivotClientObj.model.enableVirtualScrolling||this._pivotClientObj.model.enablePaging?this._pivotClientObj.element.height()-((this._pivotClientObj.element.find("div.e-titleText").length>0?50:0)+this._pivotClientObj.element.find("#"+this._pivotClientObj._id+"_reportToolbar").height()+(this._pivotClientObj.model.enablePaging?22:30)):this._pivotClientObj.element.find(".e-controlPanel").height()-17:25/100*n("#"+this._id).height(),this.model.layout==t.PivotSchemaDesigner.Layouts.OneByOne&&(i=this.element.find(".e-cubelists").length>0?i-(this._pivotClientObj.model.enableVirtualScrolling||this._pivotClientObj.model.enablePaging?30:28):i+4),this.element.find("div.parentSchemaFieldTree, .e-schemaFieldTree, div.e-fieldTable").height(i),this.model.layout==t.PivotSchemaDesigner.Layouts.OneByOne&&this.element.find("div.e-schemaFieldTree").height(i-35),n(this.element).parents(".e-pivotclient").length>0||(n("#"+this._id).height()<450&&n("#"+this._id).css("min-height","450px"),n("#"+this._id).width()<270&&n("#"+this._id).css("min-width","270px")),e=n(this.element).parents(".e-pivotclient").length>0?this._pivotClientObj.model.enableVirtualScrolling||this._pivotClientObj.model.enablePaging?this._pivotClientObj.element.height()-((this._pivotClientObj.element.find("div.e-titleText").length>0?50:0)+this._pivotClientObj.element.find("#"+this._pivotClientObj._id+"_reportToolbar").height()+(this._pivotClientObj.model.enablePaging?15:25)):this._pivotClientObj.element.find(".e-controlPanel").height()-10:n("#"+this._id).height()+20,h=t.PivotSchemaDesigner.Layouts.OneByOne==this.model.layout?0:this.element.find("table.headerTable").height()+this.element.find("div.e-fieldTable").height()+this.element.find("div.centerDiv").height()+this.element.find("div.centerHead").height(),this.model.pivotControl==null||this.model.pivotControl.element.hasClass("e-pivotclient")||this.model.pivotControl.model.operationalMode!=t.PivotGrid.OperationalMode.ServerMode?u=t.PivotSchemaDesigner.Layouts.OneByOne==this.model.layout?e:e-(h+55):(u=e-(h+90),this.element.find("#"+this._id+"_axisTd","#"+this._id+"_axisTd3").css("margin-top","5px")),this.element.find("div.e-axisTable").height(u),f=u/(this.model.layout==t.PivotSchemaDesigner.Layouts.OneByOne?4:2),this.element.find(".e-axisTd1,.e-axisTd2").css({height:f}),n(this.element).parents(".e-pivotclient").length>0&&(this._pivotClientObj.element.find(".e-schemaColumn, .e-schemaRow, .e-schemaFilter").height(f-33.5),this._pivotClientObj.element.find(".e-schemaValue").height(f-31.5)),this._setPivotBtnWidth(),this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&(c=n(".e-schemaFieldTree ul>li div").width(),n(n(".e-pivotschemadesigner .e-schemaFieldTree.e-treeview .e-text")).each(function(){var t=(c-50)/c*100;n(this).width(t.toString()+"%")})))},_clientDialogBtnClick:function(i){var f=[],v=[],r=this.model.pivotControl,tt=this._selectedFieldName,y={},nt=this,e=n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejPivotClient"):null,c,d,g,o,p,a,h,u;if(this.element.find(".e-editorTreeView").find("li span.e-searchfilterselection").length>0&&this.element.find(".e-editorTreeView").ejTreeView("removeNode",this.element.find(".e-editorTreeView").find("li span.e-searchfilterselection").closest("li")),this.element.find(".e-dialog, .e-clientDialog").hide(),this.element.find("#preventDiv").remove(),n(i.target).hasClass("e-dialogCancelBtn")){r._currentReportItems=n.extend(!0,[],r._savedReportItems);return}if(r._savedReportItems=n.extend(!0,[],r._currentReportItems),t.isNullOrUndefined(e)?r._waitingPopup.show():(e._isTimeOut=!0,setTimeout(function(){e._isTimeOut&&nt.model.pivotControl._waitingPopup.show()},800)),r.model.analysisMode==t.Pivot.AnalysisMode.Olap){r.element.hasClass("e-pivotclient")&&!t.isNullOrUndefined(r._pivotChart)&&(r._pivotChart._labelCurrentTags={});t.olap.base.clearDrilledItems(r.model.dataSource,{action:"filtering"},r);this._memberTreeObj=t.Pivot.updateTreeView(this);var o=this._selectedFieldName,p=r.model.dataSource,s={};y=t.Pivot.getNodesState(!t.isNullOrUndefined(r)&&(r.model.enableMemberEditorPaging||this._editorTreeData.length>0)?this._editorTreeData:this._memberTreeObj);var w=t.Pivot._getEditorMember(o.toLocaleLowerCase(),r,!0),b="selectedNodes",k="include",l=r.model.dataSource.providerName=="mondrian";if(r._fieldSelectedMembers[o.toLocaleLowerCase()]=n.map(r._fieldMembers[o.toLocaleLowerCase()],function(n){if(!n.checked)return n}).length==0?"All":w!="All"&&w!="multiple"?w:r._getLocalizedLabels("MultipleItems"),u=t.Pivot.getReportItemByFieldName(this._selectedFieldName,r.model.dataSource,this._dataModel).item,l&&(b="unSelectedNodes",k="exclude"),f=n.map(y[b].split("::"),function(n){return{Id:n.split("||")[0],tag:n.split("||")[1],parentId:n.split("||")[2]}}),!t.isNullOrUndefined(r._selectedNodes))for(c=0;c<r._selectedNodes.length;c++)d=r._selectedNodes[c],g=n.map(f,function(n){if(d.tag==n.tag)return n}),g.length==0&&f.push(r._selectedNodes[c]);if(t.isNullOrUndefined(u)||(u.advancedFilter=[],u.filterItems={filterType:k,values:t.Pivot.removeParentSelectedNodes(f)},this._selectedTreeNode=this.element.find(".e-schemaFieldTree").find("li[data-tag='"+u.fieldName+"']"),n(this._selectedTreeNode).parents("li:eq(0) span:eq(0)").hasClass("e-hierarchyCDB")&&(this._selectedTreeNode=n(this._selectedTreeNode).parents("li:eq(0)")),y.unSelectedNodes!=""?(l||(r.model.dataSource=t.olap.base.clearDrilledItems(r.model.dataSource,{action:"filtering"},r)),n(this._selectedTreeNode).find(".filter").length<=0&&(n(n(this._selectedTreeNode).find(".e-text")[0]).after(t.buildTag("span.e-icon").attr("role","button").attr("aria-label","filtered").addClass("filter")[0].outerHTML),this.element.find(".e-pivotButton:contains('"+this._selectedFieldName+"') .filter").addClass("filtered"),this.element.find(".e-pvtBtn[data-fieldName='"+this._selectedFieldName+"']").parent().find(".filter").addClass("filtered"))):r._unSelectedNodes?(l||(r.model.dataSource=t.olap.base.clearDrilledItems(r.model.dataSource,{action:"filtering"},r)),n(this._selectedTreeNode).find(".filter").length<=0&&(n(n(this._selectedTreeNode).find(".e-text")[0]).after(t.buildTag("span.e-icon").attr("role","button").attr("aria-label","filtered").addClass("filter")[0].outerHTML),this.element.find(".e-pivotButton:contains('"+this._selectedFieldName+"') .filter").addClass("filtered"),this.element.find(".e-pvtBtn[data-fieldName='"+this._selectedFieldName+"']").parent().find(".filter").addClass("filtered"))):(this.element.find(".e-pvtBtn[data-fieldName='"+this._selectedFieldName+"']").parent().find(".filter").removeClass("filtered"),this._selectedTreeNode.find(".filter").remove(),delete u.filterItems)),r.model.analysisMode==t.Pivot.AnalysisMode.Olap&&r.model.operationalMode==t.Pivot.OperationalMode.ClientMode){if(o=this._selectedFieldName,p=r.model.dataSource,r._currentReportItems=n.map(r._currentReportItems,function(n){if(n.fieldName!=o&&(t.isNullOrUndefined(n.dataSrc)||n.dataSrc.cube==p.cube&&n.dataSrc.reportName==p.reportName))return n}),s=r.model.enableMemberEditorPaging||this._editorTreeData.length>0?n.extend(!0,[],this._editorTreeData):n.extend([],this._memberTreeObj.dataSource()),l){for(a=2,h=0;h<s.length;h++)!t.isNullOrUndefined(s[h].level)&&s[h].level>a&&(a=s[h].level);r.model.dataSource._maxLevel=a}r._currentReportItems.push({filterItems:s,fieldName:o,dataSrc:r.model.dataSource,pageSettings:this._memberCount})}t.olap.base.getJSONData({action:"filtering"},r.model.dataSource,r)}else t.PivotAnalysis._valueSorting=null,this.model.pivotControl.model.enableMemberEditorPaging||this._editorTreeData.length>0?(jQuery.each(this._editorTreeData,function(n,t){t.id!="All"&&t.id!="(All)_0"&&t.checkedStatus&&f.push(t.name)}),jQuery.each(this._editorTreeData,function(n,t){t.checkedStatus||v.push(t.name=t.name=="(blank)"?"":t.name)})):(jQuery.each(this._memberTreeObj.element.find(":input.nodecheckbox:checked"),function(n,t){t.value!="All"&&f.push(t.value)}),jQuery.each(this._memberTreeObj.element.find(":input.nodecheckbox:not(:checked)"),function(t,i){v.push(i.value=i.value=="(blank)"?"":n(i).parents("li").find("a").text())})),this._selectedFieldName&&(u=u=t.Pivot.getReportItemByFieldName(this._selectedFieldName,r.model.dataSource,this._dataModel).item,u&&(u.advancedFilter=[])),this._pivotFilterItems(f,v),this.model.pivotControl.refreshControl();this._refreshPivotButtons();r._isMemberPageFilter=!0;t.isNullOrUndefined(e)||e._isTimeOut&&(e._isTimeOut=!1)},_pivotFilterItems:function(i,r){var h=this.model.pivotControl.model.dataSource[this._selectedFieldAxis],e=t.PivotAnalysis.getMembers(this._selectedFieldName),o,f,u,s;for(jQuery.each(e,function(n,t){t!=null&&t.toString().replace(/^\s+|\s+$/gm,"")||jQuery.each(r,function(n,i){i.toString().replace(/^\s+|\s+$/gm,"")||(r[n]=t)})}),o=this._selectedFieldName,f=n.grep(h,function(n){return n.fieldName==o}),u=0;u<f.length;u++)f[u].filterItems=i.length==e.length?null:{filterType:t.PivotAnalysis.FilterType.Exclude,values:r};this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&(this._selectedFieldName=this._selectedFieldName.replace(/ /g,"_"));r.length>0&&this.element.find(".e-schemaFieldTree li[id='"+this._selectedFieldName+"']").find(".filter").length<=0?(s=t.buildTag("span.e-icon",{display:"none"}).addClass("filter")[0].outerHTML,this.element.find(".e-schemaFieldTree li[id='"+this._selectedFieldName+"']").find(".e-text").after(s),this.element.find(".e-pvtBtn[data-fieldName='"+this._selectedFieldName+"']").parent().find(".filter").addClass("filtered")):r.length==0&&(this.element.find(".e-pvtBtn[data-fieldName='"+this._selectedFieldName+"']").parent().find(".filter").removeClass("filtered"),this.element.find(".e-schemaFieldTree li[id='"+this._selectedFieldName+"']").find(".filter").remove())},_dialogBtnClick:function(i){var k,e,l,d,tt,v,o,it,g,rt,ut,c,b,u,et,a,ot;if(t.Pivot.closePreventPanel(this),this.element.find(".e-dialog, .e-clientDialog").hide(),k=this,e=n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejPivotClient"):null,this.element.find(".e-editorTreeView").find("li span.e-searchfilterselection").length>0&&this.element.find(".e-editorTreeView").ejTreeView("removeNode",this.element.find(".e-editorTreeView").find("li span.e-searchfilterselection").closest("li")),!this._isFiltered)return!1;if(this.model.pivotControl._isUpdateRequired=!0,this.model.pivotControl!=null&&(this.model.pivotControl._memberTreeObj=this._memberTreeObj),this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode){if(i.model.text.toLowerCase()=="cancel")return;l=[];d=[];this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap?(tt=this._selectedMember,this.model.pivotControl._currentReportItems=n.grep(this.model.pivotControl._currentReportItems,function(n){if(n.fieldName!=r&&n.fieldName.toLocaleLowerCase()!=tt.toLocaleLowerCase())return n}),this._memberTreeObj=t.Pivot.updateTreeView(this),this.model.pivotControl._currentReportItems.push({filterItems:n.extend([],this._memberTreeObj.dataSource()),fieldName:this._selectedMember}),l=n.map(this.model.pivotControl._getSelectedNodes().split("::"),function(n){return{Id:n.split("||")[0],tag:n.split("||")[1],parentId:n.split("||")[2]}}),v=this._selectedMember!=r?this._selectedMember.toLocaleLowerCase():this._selectedMember.toLocaleLowerCase(),o=n.map(this.model.pivotControl.model.dataSource.columns,function(n){if(n.fieldName!=r&&n.fieldName.toLocaleLowerCase()==v)return n}),o.length==0&&(o=n.map(this.model.pivotControl.model.dataSource.rows,function(n){if(n.fieldName!=r&&n.fieldName.toLocaleLowerCase()==v)return n})),o.length==0&&(o=n.map(this.model.pivotControl.model.dataSource.filters,function(n){if(n.fieldName!=r&&n.fieldName.toLocaleLowerCase()==v)return n})),o.length>0&&(this._getUnSelectedNodes()!=""&&(this.model.pivotControl.model.dataSource=this.model.pivotControl._clearDrilledItems(this.model.pivotControl.model.dataSource,{action:"filtering"})),o[0].advancedFilter=[],o[0].filterItems={filterType:"include",values:this.model.pivotControl._removeSelectedNodes(l)},this._selectedTreeNode=this.element.find(".e-schemaFieldTree").find("li[data-tag='"+o[0].fieldName+"']"),n(this._selectedTreeNode).parents("li:eq(0)").children().children("span").hasClass("e-hierarchyCDB")&&(this._selectedTreeNode=n(n(this._selectedTreeNode).parents("li:eq(0)"))),this._getUnSelectedNodes()==""?(this._selectedTreeNode.find(".filter").remove(),this.element.find(".e-pvtBtn[data-fieldName='"+this._selectedMember+"']").parent().find(".filter").removeClass("filtered"),delete o[0].filterItems):n(n(this._selectedTreeNode)).find(".filter").length<=0&&(n(n(this._selectedTreeNode).find(".e-text")[0]).after(t.buildTag("span.e-icon").attr("role","button").attr("aria-label","filtered").addClass("filter")[0].outerHTML),this.element.find(".e-pvtBtn[data-fieldName='"+this._selectedMember+"']").parent().find(".filter").addClass("filtered"))),t.isNullOrUndefined(this.model.pivotControl._waitingPopup)||(t.isNullOrUndefined(e)?this.model.pivotControl._waitingPopup.show():(e._isTimeOut=!0,setTimeout(function(){e._isTimeOut&&k.model.pivotControl._waitingPopup.show()},800))),t.olap.base.getJSONData({action:"filtering"},this.model.pivotControl.model.dataSource,this.model.pivotControl)):(t.PivotAnalysis._valueSorting=null,jQuery.each(this._memberTreeObj.element.find(":input.nodecheckbox:checked"),function(n,t){t.value!="All"&&l.push(t.value)}),jQuery.each(this._memberTreeObj.element.find(":input.nodecheckbox:not(:checked)"),function(n,t){d.push(t.value=t.value=="(blank)"?"":t.value)}),this.element.find(".e-dialog, .e-clientDialog").hide(),this.model.pivotControl._pivotFilterItems(l,d),this.model.pivotControl.model.editCellsInfo={},this.model.pivotControl._populatePivotGrid())}else{if(it="",rt=this.element.find(".e-editorTreeView :input.nodecheckbox"),i.model.text.toLowerCase()!="cancel"){var f=this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap?it=this._getUnSelectedNodes()+"FILTERED"+this._getSelectedNodes(this._curFilteredAxis=="e-schemaFilter"?!0:!1):this.model.pivotControl.model.enableMemberEditorPaging||this._editorTreeData.length>0?this._editorTreeData:this._memberTreeObj.element.find(":input:gt(0).nodecheckbox:not(:checked)"),y=!1,s=this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap?this.model.pivotControl.model.enableMemberEditorPaging||this._editorTreeData.length>0?t.Pivot._getUnSelectedTreeState(this)+"FILTERED"+t.Pivot._getSelectedTreeState(this._curFilteredAxis=="e-schemaFilter"?!0:!1,this):f:this._curFilteredAxis+"::"+this._curFilteredText+"::FILTERED",h=this._curFilteredText,p=[],w={},nt=[];if(g=this.model.pivotControl.model.enableMemberEditorPaging||this._editorTreeData.length>0?t.Pivot._getUnSelectedTreeState(this)!=""?!0:!1:this._getUnSelectedNodes()!=""?!0:!1,this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot){for(this.model.pivotControl&&this.model.pivotControl.element.hasClass("e-pivotclient")&&this.model.pivotControl._removeFilterTag(this._selectedField),t.isNullOrUndefined(this.model.pivotControl._fieldMembers[h])&&(this.model.pivotControl._fieldMembers[h]=this._editorTreeData.length>0?n.map(this._editorTreeData,function(n){if(n.name!="(All)")return n.name}):t.Pivot._getEditorMember(rt,this.model.pivotControl,!0)),f.length>0&&f[0].name=="(All)"&&f.splice(0,1),u=0;u<f.length;u++)this.model.pivotControl.model.enableMemberEditorPaging||this._editorTreeData.length!=0?f[u].checkedStatus==!1?p.push(f[u].key||f[u].name):nt.push(f[u].key||f[u].name):p.push(n(f[u].parentElement).siblings("a").text());for(w[h]=p,this.model.pivotControl._fieldSelectedMembers[h]=nt.length==1?nt[0]:this.model.pivotControl._getLocalizedLabels("MultipleItems"),t.isNullOrUndefined(e)||(ut=e.element.find(".reportlist").data("ejDropDownList"),e._clientReportCollection=n.map(e._clientReportCollection,function(n){return n.name==ut.getValue()&&(n._fieldSelectedMembers=e._fieldSelectedMembers),n})),c=this.element.parents(".e-pivotclient").length>0?this.model.pivotControl._tempFilterData:this._tempFilterData,t.isNullOrUndefined(c)&&(c=[]),u=0;u<c.length;u++)t.isNullOrUndefined(c[u][h])||(c[u][h]=p,y=!0);if(y||c.push(w),this._tempFilterData=this.model.pivotControl._tempFilterData=c,(t.isNullOrUndefined(this._curFilteredAxis)||this._curFilteredAxis!="")&&this._curFilteredAxis!="e-schemaValue")for(u=0;u<f.length;u++)this.model.pivotControl.model.enableMemberEditorPaging||this._editorTreeData.length!=0?f[u].checkedStatus==!1&&(s+="##"+(f[u].key||f[u].name)):s+="##"+n(f[u].parentElement).siblings("a").text()}else{for(b=t.Pivot._getEditorMember(h,this.model.pivotControl,!0),this.model.pivotControl._fieldSelectedMembers[h]=n.map(this.model.pivotControl._fieldMembers[h],function(n){if(!n.checked)return n}).length==0?"All":b!="All"&&b!="multiple"?b:this.model.pivotControl._getLocalizedLabels("MultipleItems"),w[this._curFilteredText]=s,t.isNullOrUndefined(this.model.pivotControl._tempFilterData)&&(this.model.pivotControl._tempFilterData=[]),u=0;u<this.model.pivotControl._tempFilterData.length;u++)t.isNullOrUndefined(this.model.pivotControl._tempFilterData[u][this._curFilteredText])||(this.model.pivotControl._tempFilterData[u][this._curFilteredText]=s,y=!0);y||this.model.pivotControl._tempFilterData.push(w)}var ft=this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot?this._curFilteredText:this._curFilteredText.split(".")[1],st=this._getSortedHeaders(),ht=this.model.layout=="excel"?this.element.find(".e-schemaFieldTree li:contains('"+this._curFilteredText+"')"):this.element.find("."+this._curFilteredAxis+" .e-pivotButton:contains("+this._curFilteredText+") .filterBtn");if((this.model.layout=="excel"||this.model.layout=="normal")&&(n(this._selectedTreeNode).parents("li:eq(0)").children().children("span").hasClass("e-hierarchyCDB")&&(this._selectedTreeNode=n(n(this._selectedTreeNode).parents("li:eq(0)"))),g&&n(n(this._selectedTreeNode)).find(".filter").length<=0?(et=t.buildTag("span.e-icon",{display:"none"}).addClass("filter")[0].outerHTML,n(n(this._selectedTreeNode).find(".e-text")[0]).after(et),this.element.find(".e-pvtBtn:contains('"+ft+"')").parent().find(".filter").addClass("filtered")):g||(n(this._selectedTreeNode).find(".filter").remove(),this.element.find(".e-pvtBtn:contains('"+ft+"')").parent().find(".filter").removeClass("filtered")),this._curFilteredAxis==""))return!1;this.model.pivotControl.model.enableGroupingBar&&(n.each(this.model.pivotControl.element.find(".e-pivotButton .filter"),function(t,i){n(i).removeClass("filtered")}),(this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot?f.length>0:f.split("FILTERED::")[0].length>0)&&this.model.pivotControl.element.find("#"+this._id+"_pivotButton"+this._curFilteredText.split(".")[0]).next().addClass("filtered"));try{a=JSON.parse(this.model.pivotControl.getOlapReport()).Report}catch(ct){t.isNullOrUndefined(this.model.pivotControl.getOlapReport)||(a=this.model.pivotControl.getOlapReport())}this.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:"filtering",element:this.element,customObject:this.model.pivotControl.model.customObject});ot=this.model.pivotControl.model.customObject!={}&&s!=""?JSON.stringify({action:"filtering",filterParams:s+(this.model.pivotControl&&n(this.model.pivotControl.element).hasClass("e-pivotclient")?(!t.isNullOrUndefined(this.model.pivotControl._ascdes)&&this.model.pivotControl.model.enableAdvancedFilter?">>#>#>>"+this.model.pivotControl._ascdes:"")+"-##-"+JSON.stringify(this.model.pivotControl.model.valueSortSettings):""),sortedHeaders:this.model.pivotControl._ascdes,currentReport:a,valueSorting:this.model.pivotControl.model.valueSortSettings,gridLayout:this.model.pivotControl.model.layout,customObject:JSON.stringify(this.model.pivotControl.model.customObject)}):this.model.pivotControl.model.customObject!={}?JSON.stringify({action:"filtering",currentReport:a,valueSorting:this.model.pivotControl.model.valueSortSettings,customObject:serializedCustomObject}):s!=""?JSON.stringify({action:"filtering",filterParams:s,sortedHeaders:this.model.pivotControl._ascdes,currentReport:a,valueSorting:this.model.pivotControl.model.valueSortSettings}):JSON.stringify({action:"filtering",valueSorting:this.model.pivotControl.model.valueSortSettings});t.isNullOrUndefined(this.model.pivotControl._waitingPopup)||(t.isNullOrUndefined(e)?this.model.pivotControl._waitingPopup.show():(e._isTimeOut=!0,setTimeout(function(){e._isTimeOut&&k.model.pivotControl._waitingPopup.show()},800)));this.element.find(".schemaNoClick").addClass("freeze").width(n(this.element).width()).height(n(this.element).height()).css({top:n(this.element).offset().top,left:n(this.element).offset().left});this.model.pivotControl.model.enableDeferUpdate?(this.model.pivotControl._filterUpdate.push(s),t.isNullOrUndefined(this.model.pivotControl._waitingPopup)||(t.isNullOrUndefined(e)||(e._isTimeOut=!1),this.model.pivotControl._waitingPopup.hide()),this.element.find(".schemaNoClick").removeClass("freeze").removeAttr("style"),this.model.pivotControl.model.enableGroupingBar&&this.model.pivotControl._refreshGroupingBar(this.model.pivotControl)):this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.filtering,ot,this._filterElementSuccess)}this._selectedTreeNode=null;this._refreshPivotButtons()}t.isNullOrUndefined(e)||(this._refreshPivotButtons(),e._isTimeOut&&(e._isTimeOut=!1))},_beforeNodeExpand:function(n){t.Pivot.getChildNodes(n,this._selectedFieldName,this.model.pivotControl._currentReportItems,this.model.pivotControl.model.dataSource,this)},_nodeExpand:function(n){this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(n._isSchemaClick=!0,this.model.pivotControl._nodeExpand(n))},_nodeStateModifiedSuccess:function(n){this.model.pivotControl._isUpdateRequired=!0;n[0]!=r?n[2]!=null&&n[2]!=r&&(this.model.customObject=n[2].Value):n.d!=r?n.d[2]!=null&&n.d[2]!=r&&(this.model.customObject=n.d[2].Value):n.customObject!=null&&n.customObject!=r&&(this.model.customObject=n.customObject);this.model.afterServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("afterServiceInvoke",{action:"nodeStateModified",element:this.element,customObject:this.model.customObject});this.model.pivotControl.model.enableDeferUpdate?this.model.pivotControl._deferUpdateSuccess(n):this.model.pivotControl._renderControlSuccess(n);this.element.find(".schemaNoClick").removeClass("freeze").removeAttr("style")},_pvtNodeDroppedSuccess:function(i){var o,e,u,f;if(t.Pivot._updateValueSortingIndex(i,this.model.pivotControl),i[0]!=r?i[2]!=null&&i[2]!=r&&(this.model.customObject=i[2].Value):i.d!=r?(i.d[2]!=null&&i.d[2]!=r&&(this.model.customObject=i.d[2].Value),o=i.d[0].Key,o=="PivotReport"&&this.model.pivotControl.setOlapReport(i.d[0].Value)):(i.customObject!=null&&i.customObject!=r&&(this.model.customObject=i.customObject),this.model.pivotControl.setOlapReport(i.PivotReport)),e=n(this.element).parents(".e-pivotclient").length>0?this.model.pivotControl:this,e.model.afterServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&e._trigger("afterServiceInvoke",{action:"nodeDropped",element:e.element,customObject:e.model.customObject}),this.model.pivotControl.model.enableDeferUpdate)this.model.pivotControl._deferUpdateSuccess(i);else{if(this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&(u=this.model.pivotControl,!t.isNullOrUndefined(u._clientReportCollection)))for(f=0;f<u._clientReportCollection.length;f++)u._clientReportCollection[f].fieldSelectedMembers!=r&&u.element.find("#"+this._id+"_reportList")[0].value==u._clientReportCollection[f].name&&(u._clientReportCollection[f].fieldSelectedMembers={});n(this.element).parents(".e-pivotclient").length>0&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot?(this.model.pivotControl.refreshControl(i),this.model.pivotControl&&this.model.pivotControl._pivotGrid&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.pivotControl._pivotGrid._removeCells(i)):this.model.pivotControl._renderControlSuccess(i)}this.element.find(".schemaNoClick").removeClass("freeze").removeAttr("style")},_pvtBtnDroppedSuccess:function(n){n[0]!=r?n[2]!=null&&n[2]!=r&&(this.model.customObject=n[2].Value):n.d!=r?n.d[2]!=null&&n.d[2]!=r&&(this.model.customObject=n.d[2].Value):n.customObject!=null&&n.customObject!=r&&(this.model.customObject=n.customObject);this.model.afterServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("afterServiceInvoke",{action:"removeButton",element:this.element,customObject:this.model.customObject});this.model.pivotControl._renderControlSuccess(n);this.model.pivotControl&&this.model.pivotControl._pivotGrid&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.pivotControl._pivotGrid._removeCells(n);this.element.find(".schemaNoClick").removeClass("freeze").removeAttr("style")},_filterElementSuccess:function(i){var u,f,e;this._isSearchApplied=!1;t.Pivot._updateValueSortingIndex(i,this.model.pivotControl);n(this.element).parents(".e-pivotclient").length>0?(t.isNullOrUndefined(i.d)||(u={},u.PivotReport=i.d[0].Value,u.GridJSON=i.d[1].Value,u.ChartJSON=i.d[2].Value,f=n.map(i.d,function(n){var t=n.Key;if(t=="FilteredColumnHeaders")return n.Value}),f.length>0&&(u.FilteredColumnHeaders=f[0]),e=n.map(i.d,function(n){var t=n.Key;if(t=="FilteredRowHeaders")return n.Value}),e.length>0&&(u.FilteredRowHeaders=e[0]),i=u),this.model.pivotControl.refreshControl(i),this.model.pivotControl&&this.model.pivotControl._pivotGrid&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.pivotControl._pivotGrid._removeCells(i)):(i[0]!=r?i[2]!=null&&i[2]!=r&&(this.model.customObject=i[2].Value):i.d!=r?i.d[2]!=null&&i.d[2]!=r&&(this.model.customObject=i.d[2].Value):i.customObject!=null&&i.customObject!=r&&(this.model.customObject=i.customObject),this.model.afterServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("afterServiceInvoke",{action:"filtering",element:this.element,customObject:this.model.customObject}),this.model.pivotControl._renderControlSuccess(i));this.element.find(".schemaNoClick").removeClass("freeze").removeAttr("style");t.Pivot.closePreventPanel(this);this.element.find(".e-dialog").remove()},_fetchChildNodeSuccess:function(i){var u,f,e;i.length>1&&i[0]!=r?(u=JSON.parse(i[0].Value),i[1]!=null&&i[1]!=r&&(this.model.customObject=i[1].Value)):i.d!=r?(u=JSON.parse(i.d[0].Value),i.d[1]!=null&&i.d[1]!=r&&(this.model.customObject=i.d[1].Value)):(u=JSON.parse(i.ChildNodes),i.customObject!=null&&i.customObject!=r&&(this.model.customObject=i.customObject));e={id:"id",parentId:"pid",hasChild:"hasChildren",text:"name",isChecked:"checkedStatus"};f=n(this.pNode).parents("li").length>1?n(this.pNode).parents("li").first():n(this.pNode).parents("li");n(n(f).find("input.nodecheckbox")[0]).ejCheckBox({checked:!1});this._memberTreeObj._createSubNodesWhenLoadOnDemand(u,this.pNode,e);n.each(n(f).children().find("li"),function(n,t){t.setAttribute("data-tag",u[n].tag)});this.model.afterServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("afterServiceInvoke",{action:"memberExpanded",element:this.element,customObject:this.model.customObject});this.element.find(".schemaNoClick").removeClass("freeze").removeAttr("style")},_fetchMemberSuccess:function(i){var u,f;i[0]!=r&&i.length>0?(this._currentMembers=i[0].Value,i[1]!=null&&i[1]!=r&&(this.model.customObject=i[1].Value)):i.d!=r&&i.d.length>0?(this._currentMembers=i.d[0].Value,i.d[1]!=null&&i.d[1]!=r&&(this.model.customObject=i.d[1].Value)):i!=r&&i.length>0?(this._currentMembers=i.EditorTreeInfo,i!=null&&i.customObject!=r&&(this.model.customObject=i.customObject)):i!=r&&(this._currentMembers=i.EditorTreeInfo,i!=null&&i.customObject!=r&&(this.model.customObject=i.customObject));u=n(this.element).parents(".e-pivotclient").length>0?this.model.pivotControl:this;u.model.afterServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&u._trigger("afterServiceInvoke",{action:"fetchMembers",element:u.element,customObject:u.model.customObject});this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&!this.model.pivotControl.model.enableMemberEditorPaging&&(this._editorTreeData.length==0||this._isFilterBtnClick)&&(this._editorTreeData=JSON.parse(this._currentMembers),this.model.pivotControl.model.enableAdvancedFilter&&(this._editorTreeData=n.map(this._editorTreeData,function(n){if(t.isNullOrUndefined(n.levels))return n})));this.model.pivotControl._savedReportItems=n.extend(!0,[],this.model.pivotControl._currentReportItems);this._isFilterBtnClick=!1;this._createDialog(this._dialogHead,this._currentMembers);this.model.pivotControl._waitingPopup&&(f=n(this.element).parents(".e-pivotclient").length>0?n(this.element).parents(".e-pivotclient").data("ejPivotClient"):null,t.isNullOrUndefined(f)||(f._isTimeOut=!1),this.model.pivotControl._waitingPopup.hide());this.element.find(".schemaNoClick").removeClass("freeze").removeAttr("style");t.isNullOrUndefined(this.element.find(".e-dialog .e-text:visible").first())||this.element.find(".e-dialog .e-text:visible").first().attr("tabindex","-1").focus().addClass("e-hoverCell")},_droppedSuccess:function(i){var u,f;if(!t.isNullOrUndefined(i.Exception))return t.Pivot._createErrorDialog(i,"Exception",this),this.element.find(".schemaNoClick").removeClass("freeze").removeAttr("style"),this._nodeCheck=!0,this._tableTreeObj.uncheckNode(this._tableTreeObj.element.find("li[data-tag='"+this._currentCheckedNode+"']")),!1;t.Pivot._updateValueSortingIndex(i,this.model.pivotControl);u=null;this.model.pivotControl.model.enableDeferUpdate?u=this._removeButtonDeferUpdate==!1?t.isNullOrUndefined(i.OlapReport)?JSON.parse(i.d[0].Value):JSON.parse(i.OlapReport):u=t.isNullOrUndefined(i.OlapReport)?i.d[0].Value:i.OlapReport:(!t.isNullOrUndefined(i[0])&&i.length>0?(u=JSON.parse(i[1].Value),i[2]!=null&&i[2]!=r&&(this.model.customObject=i[2].Value)):!t.isNullOrUndefined(i.d)&&i.d.length>0?(u=n(this.element).parents(".e-pivotclient").length>0&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot?JSON.parse(n.grep(i.d,function(n){var t=n.Key;return t=="PivotReport"})[0].Value):JSON.parse(i.d[1].Value),i.d[2]!=null&&i.d[2]!=r&&(this.model.customObject=i.d[2].Value)):t.isNullOrUndefined(i)||t.isNullOrUndefined(i.OlapReport)?t.isNullOrUndefined(i)||t.isNullOrUndefined(i.PivotReport)||(u=JSON.parse(i.PivotReport)):u=JSON.parse(i.OlapReport),i.customObject!=null&&i.customObject!=r&&(this.model.customObject=i.customObject));this._removeButtonDeferUpdate==!1&&(this.element.find(".e-axisTable .e-pivotButton").remove(),this._setPivotRows(u.PivotRows),this._setPivotColumns(u.PivotColumns),this._setPivotCalculations(u.PivotCalculations),this._setFilters(u.Filters),this.model.pivotControl.setOlapReport(JSON.stringify(u)),this._refreshPivotButtons(),this.model.enableDragDrop&&this.element.find(".e-pivotButton .e-pvtBtn").ejButton({size:"normal",type:t.ButtonType.Button,enableRTL:this.model.enableRTL}).ejDraggable({handle:"button",clone:!0,cursorAt:{left:-5,top:-5},dragStart:t.proxy(function(){this._isDragging=!0},this),dragStop:t.proxy(this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this._pvtBtnDropped:this._clientOnPvtBtnDropped,this),helper:t.proxy(function(t){if(n(t.element).addClass("dragHover"),t.sender.target.className.indexOf("e-btn")>-1){var i=n(t.sender.target).clone().attr("id",this._id+"_dragClone").appendTo("body");return n("#"+this._id+"_dragClone").removeAttr("style").height(n(t.sender.target).height()),i}return!1},this)}),this._unWireEvents(),this._wireEvents());this.model.pivotControl.model.enableDeferUpdate?(t.isNullOrUndefined(this.model.pivotControl._ogridWaitingPopup)||this.model.pivotControl._ogridWaitingPopup.hide(),t.isNullOrUndefined(i.OlapReport)?(this.model.pivotControl.setOlapReport(i.d[0].Value),n(".e-pivotpager")[0]!=null&&n(".e-pivotpager")[0]!=r&&this.model.pivotControl._pagerObj.initPagerProperties(JSON.parse(i.d[2].Value),JSON.parse(i.d[1].Value))):(this.model.pivotControl.setOlapReport(i.OlapReport),i.HeaderCounts!=r&&n(".e-pivotpager")[0]!=null&&n(".e-pivotpager")[0]!=r&&this.model.pivotControl._pagerObj.initPagerProperties(JSON.parse(i.HeaderCounts),JSON.parse(i.PageSettings))),this._removeButtonDeferUpdate=!1,this.model.pivotControl._deferUpdateSuccess(i)):n(this.element).parents(".e-pivotclient").length>0&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot?(this.model.pivotControl.refreshControl(i),this.model.pivotControl&&this.model.pivotControl._pivotGrid&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.pivotControl._pivotGrid._removeCells(i)):this.model.pivotControl._renderControlSuccess(i);f=n(this.element).parents(".e-pivotclient").length>0?this.model.pivotControl:this;f.model.afterServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&f._trigger("afterServiceInvoke",{action:"nodeDropped",element:f.element,customObject:f.model.customObject});this.element.find(".schemaNoClick").removeClass("freeze").removeAttr("style");this._createContextMenu();this._setPivotBtnWidth()},_removePvtBtn:function(i){var o,r,s,u,e,h,a,c,v,l,f;this.model.pivotControl._waitingPopup.show();n(i.element).length>0?(o=n(i.element).parent().attr("data-tag").split(":")[1],r=n(i.element).parent().attr("data-tag")):(o=n(i.target).parent().attr("data-tag").split(":")[1],r=n(i.target).parent().attr("data-tag"));try{s=JSON.parse(this.model.pivotControl.getOlapReport()).Report}catch(y){t.isNullOrUndefined(this.model.pivotControl.getOlapReport)||(s=this.model.pivotControl.getOlapReport())}if(s==""&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap&&(s=this.model.pivotControl.model.dataSource),this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap)if(this._clearFilterData(o),e=r.indexOf("[Measures]")>-1||r.indexOf("KPI")>-1||n(i.element).parents().hasClass("e-schemaValue")&&r.indexOf("[Measures]")==-1||r.indexOf("[")>-1?r.split(":")[1]:this.model.pivotControl._getNodeUniqueName(r),e=e.indexOf("<>")?e.replace("<>","."):e,e=="[Measures]"||e=="KPI"||n(i.element).parents().hasClass("e-schemaValue")&&r.indexOf("[Measures]")==-1){if(this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode)t.Pivot.removeReportItem(this.model.pivotControl.model.dataSource,o,o.toLocaleLowerCase().indexOf("measures")==0),this.model.pivotControl.refreshControl(),this.refreshControl();else if(this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode){for(r=n(i.element).parents().hasClass("e-schemaValue")&&r.indexOf("[Measures]")==-1?r+":KPI":r,n(i.element).length>0?n(i.element).parent().parent().remove():n(i.target).parent().parent().remove(),f=0;f<this.model.pivotCalculations.length&&r.indexOf(this._getLocalizedLabels("Measures"))>-1;f++)e=this.model.pivotCalculations[f].Tag,this._isMeasureBtnRemove=!0,u=this.model.pivotControl._getNodeByUniqueName(e),this._tableTreeObj.uncheckNode(u);this.element.find(".schemaNoClick").addClass("freeze").width(n(this.element).width()).height(n(this.element).height()).css({top:n(this.element).offset().top,left:n(this.element).offset().left});this.model.beforeServiceInvoke!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode&&this._trigger("beforeServiceInvoke",{action:"removeButton",element:this.element,customObject:this.model.pivotControl.model.customObject});h=JSON.stringify({action:"removeButton",headerInfo:r,currentReport:s,customObject:JSON.stringify(this.model.pivotControl.model.customObject),gridLayout:this.model.pivotControl.model.layout});r.indexOf("[Measures]")<0&&(r.indexOf("Measures")>=0||r.indexOf("KPI")>=0&&r.split(":").length==2)&&(a=r.indexOf("KPI")>=0&&r.split(":").length==2?this.element.find(".e-schemaValue .e-pivotButton").not("[data-tag*='Measures']"):this.element.find(".e-schemaValue .e-pivotButton [data-tag*='Measures']"),a.remove());r.indexOf("KPI")>0&&this.element.find(".e-schemaValue .e-pivotButton").not("[data-tag*='Measures']").length==0&&(c=r.split(":"),v=c[0]=="Rows"?".e-schemaRow":c[0]=="Columns"?".e-schemaColumn":"",this.element.find(".e-schemaValue .e-pivotButton [data-tag*='KPI']").length<=0&&this.element.find(v+" .e-pivotButton:contains('KPI')").remove(),!t.isNullOrUndefined(this._tableTreeObj)&&(this.model.olap.showKPI||this.model.olap.showKpi)&&(l=this._tableTreeObj.element.find("li[data-tag=KPI]"),n(l).length>0&&n(l).find("span.treeDrop").remove()));this.model.pivotControl.model.enableDeferUpdate?(this._removeButtonDeferUpdate=!0,this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.removeButton,h.replace("removeButton","removeButtonDeferUpdate"),this._droppedSuccess),this.element.find(".schemaNoClick").removeClass("freeze").removeAttr("style")):this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.removeButton,h,this._pvtBtnDroppedSuccess)}}else u=this.model.pivotControl._getNodeByUniqueName(e),this._tableTreeObj.uncheckNode(u);else if(this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode){if(t.PivotAnalysis._valueSorting=null,o=n(i.element).length>0?n(i.element).parent().find(".e-pvtBtn").attr("data-fieldName"):n(i.target).parent().find(".e-pvtBtn").attr("data-fieldName"),this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap){if(r.split(":")[1].toLowerCase()=="measures"){for(this.model.pivotControl._ogridWaitingPopup.show(),this.element.find("div[data-tag='"+r+"']").remove(),f=0;f<this.model.pivotControl.model.dataSource.values[0].measures.length;f++)this._nodeCheck=!0,u=this._tableTreeObj.element.find("li[data-tag='"+this.model.pivotControl.model.dataSource.values[0].measures[f].fieldName+"']"),this._tableTreeObj.uncheckNode(u),this.element.find("div[data-tag='values:"+this.model.pivotControl.model.dataSource.values[0].measures[f].fieldName+"']").remove();this.model.pivotControl.model.dataSource.values[0].measures=[];t.olap.base.getJSONData({action:"removeButton"},this.model.pivotControl.model.dataSource,this.model.pivotControl)}if(u=this._tableTreeObj.element.find("li[data-tag='"+o+"']"),u.length>1)for(f=0;f<u.length;f++)this._tableTreeObj.isNodeChecked(u[f])&&(u=u[f])}else u=this._tableTreeObj.element.find("li[id="+o+"]");this._tableTreeObj.uncheckNode(u)}else u=this._tableTreeObj.element.find("li:contains('"+o+"')"),this._tableTreeObj.uncheckNode(u)},_createDialog:function(i,u){var h,ut,ft,u,c,l,s,a,w,et,ot,b,f,ht,v,d;this.element.find(".e-dialog, .e-clientDialog").remove();var y,e,g="",nt="",tt="",ct="",it="",p="",rt="",o=this.model.pivotControl&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.pivotControl.model.enableAdvancedFilter&&this._curFilteredAxis!="e-schemaFilter"&&this._curFilteredAxis!="e-schemaValue"||this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&this.model.pivotControl.model.dataSource.enableAdvancedFilter&&t.Pivot.getReportItemByFieldName(this._selectedFieldName,this.model.pivotControl.model.dataSource,this._dataModel).axis!="filters"&&t.Pivot.getReportItemByFieldName(this._selectedFieldName,this.model.pivotControl.model.dataSource,this._dataModel).axis!="values"&&!t.isNullOrUndefined(t.Pivot.getReportItemByFieldName(this._selectedFieldName,this.model.pivotControl.model.dataSource,this._dataModel).item)?!0:!1;o&&(h=JSON.parse(u),h[0].name="(Select All)",y=this._selectedFieldName,this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?this.model.pivotControl.model.analysisMode==t.PivotGrid.AnalysisMode.Olap?(e=n.map(this.model.pivotControl._currentReportItems,function(n){if(n.fieldName==y)return n.dropdownData}),e.length==0&&(e=n.map(this._tableTreeObj.element.find("li[data-tag ='"+this._selectedFieldName+"'] li"),function(t){return{value:n(t).attr("data-tag"),text:n(t).find("a").text()}}))):e=[{value:this._selectedFieldName,text:this._selectedFieldName}]:this.model.pivotControl.model.analysisMode==t.PivotGrid.AnalysisMode.Olap?(e=this.model.pivotControl.model.enableMemberEditorPaging?this._memberPagingAvdData:h.splice(h.length-1,1),e=JSON.parse(e[0].levels)):e=[{value:this._curFilteredText,text:this._curFilteredText}],u=JSON.stringify(h),g=t.buildTag("div.e-ddlGroupWrap",this._getLocalizedLabels("SelectField")+":"+t.buildTag("input#"+this._id+"_GroupLabelDrop.groupLabelDrop").attr("type","text")[0].outerHTML,{})[0].outerHTML,nt=t.buildTag("ul.e-filterElementTag")[0].outerHTML,ut=t.Pivot.createAdvanceFilterTag({action:"filterTag"},this));tt=t.buildTag("div.e-memberSearchEditorDiv",t.buildTag("input#"+this._id+"_SearchEditorTreeView.searchEditorTreeView").attr("type","text")[0].outerHTML+(this.model.pivotControl.model.enableMemberEditorPaging&&n.parseJSON(u).length>=this.model.pivotControl.model.memberEditorPageSize?t.buildTag("span.e-icon e-searchEditorTree",{})[0].outerHTML:""),{padding:this.model.pivotControl.model.enableAdvancedFilter?this._selectedFieldAxis=="values"||this._curFilteredAxis=="e-schemaValue"?"5px 0px 0px 0px":"5px 5px 0px 9px":0})[0].outerHTML;var lt=t.buildTag("div#"+this._id+"_EditorDiv.e-editorDiv",g+nt+t.buildTag("div",tt+t.buildTag("div.e-memberEditorDiv",t.buildTag("div#"+this._id+"_editorTreeView.e-editorTreeView")[0].outerHTML)[0].outerHTML)[0].outerHTML)[0].outerHTML+(!t.isNullOrUndefined(u)&&this.model.pivotControl.model.enableMemberEditorPaging&&(n.parseJSON(u).length>=this.model.pivotControl.model.memberEditorPageSize||this.model.pivotControl.model.memberEditorPageSize<this._memberPageSettings.endPage)?"":"<\/br>"),at=t.buildTag("div.e-footerArea",t.buildTag("button#"+this._id+"_OKBtn.e-dialogOKBtn",this._getLocalizedLabels("OK")).attr({title:this._getLocalizedLabels("OK"),tabindex:0})[0].outerHTML+t.buildTag("button#"+this._id+"_CancelBtn.e-dialogCancelBtn",this._getLocalizedLabels("Cancel")).attr({title:this._getLocalizedLabels("Cancel"),tabindex:0})[0].outerHTML,{float:"right",margin:(!t.isNullOrUndefined(u)&&n.parseJSON(u).length>=this.model.pivotControl.model.memberEditorPageSize&&this.model.pivotControl.model.enableMemberEditorPaging?"10px":"-12px ")+" "+(o?" 5px ":" 0px ")+" 6px 0px"})[0].outerHTML,ct=t.buildTag("div.e-memberPager",t.buildTag("div#"+this._id+"_NextpageDiv.e-nextPageDiv",t.buildTag("div.e-icon e-media-backward_01 e-firstPage",{})[0].outerHTML+t.buildTag("div.e-icon e-arrowhead-left e-prevPage",{})[0].outerHTML+t.buildTag("input.e-memberCurrentPage#"+this._id+"_memberCurrentPage",{},{width:"20px",height:"10px"})[0].outerHTML+t.buildTag("span.e-memberPageCount#"+this._id+"_memberPageCount")[0].outerHTML+t.buildTag("div.e-icon e-arrowhead-right e-nextPage",{})[0].outerHTML+t.buildTag("div.e-icon e-media-forward_01 e-lastPage",{})[0].outerHTML)[0].outerHTML)[0].outerHTML,it=t.buildTag("div.e-memberSearchPager",t.buildTag("div#"+this._id+"_NextSearchpageDiv.e-nextPageDiv",t.buildTag("div.e-icon e-media-backward_01 e-firstPage",{})[0].outerHTML+t.buildTag("div.e-icon e-arrowhead-left e-prevPage",{})[0].outerHTML+t.buildTag("input.e-memberCurrentSearchPage#"+this._id+"_memberCurrentSearchPage",{},{width:"20px",height:"10px"})[0].outerHTML+t.buildTag("span.e-memberSearchPageCount#"+this._id+"_memberSearchPageCount")[0].outerHTML+t.buildTag("div.e-icon e-arrowhead-right e-nextPage",{})[0].outerHTML+t.buildTag("div.e-icon e-media-forward_01 e-lastPage",{})[0].outerHTML)[0].outerHTML,{}).css("display","none")[0].outerHTML;for(p=t.buildTag("div.e-memberDrillPager",t.buildTag("div#"+this._id+"NextDrillpageDiv.e-nextPageDiv",t.buildTag("div.e-icon e-media-backward_01 e-firstPage",{})[0].outerHTML+t.buildTag("div.e-icon e-arrowhead-left e-prevPage",{})[0].outerHTML+t.buildTag("input.e-memberCurrentDrillPage#"+this._id+"_memberCurrentDrillPage",{},{width:"20px",height:"10px"})[0].outerHTML+t.buildTag("span.e-memberDrillPageCount#"+this._id+"_memberDrillPageCount")[0].outerHTML+t.buildTag("div.e-icon e-arrowhead-right e-nextPage",{})[0].outerHTML+t.buildTag("div.e-icon e-media-forward_01 e-lastPage",{})[0].outerHTML)[0].outerHTML,{}).css("display","none")[0].outerHTML,rt=t.buildTag("div.e-linkOuterPanel",t.buildTag("span.e-infoImg e-icon","",{}).css({float:"left","margin-top":"4px","font-size":"16px"})[0].outerHTML+t.buildTag("a.e-linkPanel",this._getLocalizedLabels("NotAllItemsShowing")).css({display:"inline-block","margin-left":"3px","margin-top":"2px"})[0].outerHTML,{}).css({display:"none","margin-top":"-16px","margin-left":o?"8px":"0px"})[0].outerHTML,ft=t.buildTag("div#"+(o?"clientDialog":this._id+"_clientDialog")+".e-clientDialog",lt+(!t.isNullOrUndefined(this._editorTreeData)&&this.model.pivotControl.model.enableMemberEditorPaging?t.DataManager(this._editorTreeData).executeLocal(t.Query().where("pid","equal",null||r)).length>=this.model.pivotControl.model.memberEditorPageSize||this.model.pivotControl.model.memberEditorPageSize<this._memberPageSettings.endPage?p+it+ct:p+it:rt)+at,{opacity:"1"}).attr("title",i)[0].outerHTML,u=JSON.parse(u),this._isOptionSearch=!1,this._currentFilterList={},this._editorSearchTreeData=[],this._isEditorDrillPaging=!1,this._lastSavedTree=[],this._isSearchApplied=!1,f=0;f<u.length;f++)u[f].name!=null&&u[f].name.toString().replace(/^\s+|\s+$/gm,"")||(u[f].name="(blank)",u[f].id="(blank)"),t.isNullOrUndefined(u[f].id)||typeof u[f].id!="string"||(u[f].id=u[f].id.replace(/ /g,"_"));if(this.model.pivotControl.model.analysisMode==t.PivotGrid.AnalysisMode.Olap&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode?!t.isNullOrUndefined(this.model.pivotControl._fieldMembers):!t.isNullOrUndefined(this._tempFilterData)){if(this.model.pivotControl.model.analysisMode==t.PivotGrid.AnalysisMode.Olap&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ServerMode)for(f=0;f<Object.keys(this.model.pivotControl._fieldMembers).length;f++)t.isNullOrUndefined(this.model.pivotControl._fieldMembers[this._curFilteredText])||(c=n.map(this.model.pivotControl._fieldMembers[this._curFilteredText],function(n){if(!n.checked)return n.name}));else for(f=0;f<this._tempFilterData.length;f++)t.isNullOrUndefined(this._tempFilterData[f][i])||(c=this._tempFilterData[f][i]);if(!t.isNullOrUndefined(c))for(f=0;f<c.length;f++)for(l=0;l<u.length;l++)c[f]==u[l].name&&(u[l].checkedStatus=!1)}for(n(ft).appendTo("#"+this._id),this.model.pivotControl.model.enableMemberEditorPaging&&(u.length>=this.model.pivotControl.model.memberEditorPageSize||this.model.pivotControl.model.memberEditorPageSize<this._memberPageSettings.endPage)&&(this.element.find(".e-prevPage, .e-firstPage").addClass("e-pageDisabled"),this.element.find(".e-nextPage, .e-lastPage").addClass("e-pageEnabled"),s=this._memberCount/this.model.pivotControl.model.memberEditorPageSize,s!=Math.round(s)&&(s=parseInt(s)+1),this.element.find(".e-memberPageCount").html("/ "+s),this.element.find(".e-memberCurrentPage").val(this._memberPageSettings.currentMemeberPage),this._memberPageSettings.currentMemeberPage>1?this.element.find(".e-prevPage, .e-firstPage").removeClass("e-pageDisabled").addClass("e-pageEnabled"):this.element.find(".e-prevPage, .e-firstPage").removeClass("e-pageEnabled").addClass("e-pageDisabled"),this._memberPageSettings.currentMemeberPage==parseInt(n.trim(this.element.find(".e-memberPageCount").text().split("/")[1]))?this.element.find(".e-nextPage, .e-lastPage").removeClass("e-pageEnabled").addClass("e-pageDisabled"):this.element.find(".e-nextPage, .e-lastPage").removeClass("e-pageDisabled").addClass("e-pageEnabled"),this.element.find(".e-nextPageDiv .e-pageDisabled").css("opacity","0.5")),this.element.find("#"+this._id+"_SearchEditorTreeView").ejMaskEdit({name:"inputbox",width:"100%",inputMode:t.InputMode.Text,watermarkText:this._getLocalizedLabels("Search")+" "+i,maskFormat:"",textAlign:this.model.enableRTL?"right":"left",change:t.proxy(function(n){t.Pivot._searchEditorTreeNodes(n,this)},this)}),this.element.find(".e-editorTreeView").ejTreeView({showCheckbox:!0,loadOnDemand:!0,enableRTL:this.model.enableRTL,beforeDelete:function(n){if(!t.isNullOrUndefined(n.event)&&n.event.type=="keydown"&&n.event.originalEvent.key.toLowerCase()=="delete")return!1},height:this.model.enableMemberEditorSorting?"200px":o?"inherit":"245px",fields:{id:"id",text:"name",parentId:"pid",expanded:"expanded",isChecked:"checkedStatus",hasChild:"hasChildren",dataSource:t.Pivot._showEditorLinkPanel(u,this,this.model.pivotControl)}}),o&&(this.element.find(".e-filterElementTag").ejMenu({fields:{dataSource:ut,id:"id",parentId:"parentId",text:"text",spriteCssClass:"spriteCssClass"},menuType:t.MenuType.NormalMenu,width:"100%",enableRTL:this.model.enableRTL,orientation:t.Orientation.Vertical,click:t.proxy(this._filterElementClick,this)}),this.element.find(".groupLabelDrop").ejDropDownList({width:"99%",enableRTL:this.model.enableRTL,dataSource:e,fields:{id:"id",text:"text",value:"value"},change:t.proxy(this._groupLabelChange,this),create:function(){n(this.wrapper.find(".e-input")).focus(function(){n(this).blur()})}}),a=t.isNullOrUndefined(this._selectedLevel)?"":this._selectedLevel,a=n.map(e,function(n){if(n.value.toLowerCase()==a.toLowerCase())return n.text}),w=this.element.find(".groupLabelDrop").data("ejDropDownList"),w.selectItemByText(a.length>0?a[0]:w.model.dataSource[0].text),this.element.find(".e-memberEditorDiv").addClass("advancedFilter")),f=0;f<this._editorTreeData.length;f++)this._editorTreeData[f].name!=null&&this._editorTreeData[f].name.toString().replace(/^\s+|\s+$/gm,"")||(this._editorTreeData[f].name="(blank)",this._editorTreeData[f].id="(blank)"),t.isNullOrUndefined(this._editorTreeData[f].id)||typeof this._editorTreeData[f].id!="string"||(this._editorTreeData[f].id=this._editorTreeData[f].id.replace(/ /g,"_"));for(et=this,!t.isNullOrUndefined(this.model.pivotControl)&&this.model.pivotControl.model.enableMemberEditorPaging&&jQuery.isEmptyObject(this._currentFilterList)?this._currentFilterList=JSON.parse(JSON.stringify(this._editorTreeData)):t.isNullOrUndefined(this.model.pivotControl)||this.model.pivotControl.model.enableMemberEditorPaging||!jQuery.isEmptyObject(this._currentFilterList)||n(u).each(function(n,t){et._currentFilterList[t.id]=t}),ot=n.grep(u,function(n){if(n.hasChildren==!0)return n}).length>0,ot||this.element.find(".e-memberEditorDiv").addClass("noChildNode"),b=this.element.find(".e-editorTreeView").find("li"),f=0;f<b.length;f++)b[f].setAttribute("data-tag",u[f].tag);this.element.find(".e-dialogOKBtn, .e-dialogCancelBtn").ejButton({type:t.ButtonType.Button,click:t.proxy(this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?this._clientDialogBtnClick:this._dialogBtnClick,this)});this._dialogOKBtnObj=this.element.find(".e-dialogOKBtn").data("ejButton");this._memberTreeObj=this.element.find(".e-editorTreeView").data("ejTreeView");var st=this.element.find(".e-editorTreeView li:gt(0)"),k=n(this._memberTreeObj.element.find("li:first")).find("span.e-chk-image"),vt=n(st).find(":input.nodecheckbox:not(:checked)");vt.length>0&&n(k).removeClass("e-checkmark").addClass("e-stop");!t.isNullOrUndefined(this.model.pivotControl)&&this.model.pivotControl.model.enableMemberEditorPaging&&(this._isAllMemberChecked||(this._dialogOKBtnObj.disable(),this.element.find(".e-dialogOKBtn").attr("disabled","disabled"),n(k).removeClass("e-checkmark").removeClass("e-stop").addClass("e-chk-inact")));!t.isNullOrUndefined(this.model.pivotControl._unSelectedNodes)&&this.model.pivotControl._isMemberPageFilter&&(ht=this.model.pivotControl._unSelectedNodes,n.map(st,function(t){var i=t;n.map(ht,function(t){n(i).attr("data-tag")==t.tag&&(n(n(i).find("span.e-chk-image")).removeClass("e-checkmark"),n(k).removeClass("e-checkmark").addClass("e-stop"))})}));this.element.find(".e-clientDialog").ejDialog({width:o?"auto":265,target:"#"+this._id,enableResize:!1,enableRTL:this.model.enableRTL,close:t.proxy(t.Pivot.closePreventPanel,this)});this.element.find(".e-clientDialog").next(".e-scrollbar").hide();this.element.find(".e-dialog .e-close").attr("title",this._getLocalizedLabels("Close"));o&&(this.element.find("#"+this._id+"_clientDialog_wrapper").addClass("e-advancedFilterDlg"),this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.pivotControl.model.analysisMode==t.PivotGrid.AnalysisMode.Olap&&this.element.find("#"+this._id+"_clientDialog_wrapper").addClass("advancedFilterDlgOSM"),this.element.find("#"+this._id+"_clientDialog_wrapper").css("min-width","265px"),this._droppedClass!="e-schemaFilter"&&(this.element.find(".e-clientDialog").css("padding","0px").parents().parents().find(".e-titlebar").remove(),v=t.Pivot.getReportItemByFieldName(y,this.model.pivotControl.model.dataSource).item,v&&v.sortOrder&&v.sortOrder!=t.olap.SortOrder.None&&(d=v.sortOrder==t.olap.SortOrder.Descending?"desc":"asc",t.isNullOrUndefined(d)||d=="asc"?this.element.find(".e-clientDialog .e-ascImage").addClass("e-selectedSort"):this.element.find(".e-clientDialog .e-descImage").addClass("e-selectedSort"))));(this.model.enableMemberEditorSorting||this.model.pivotControl&&n(this.model.pivotControl.element).hasClass("e-pivotclient")&&this.model.pivotControl.model.enableMemberEditorSorting)&&(this._sortType="",t.Pivot._separateAllMember(this,o));this._memberTreeObj.model.nodeCheck=t.proxy(this._nodeCheckChanges,this);this._memberTreeObj.model.nodeUncheck=t.proxy(this._nodeCheckChanges,this);this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?this._memberTreeObj.model.beforeExpand=t.proxy(this._beforeNodeExpand,this):this._memberTreeObj.model.nodeClick=t.proxy(this._nodeExpand,this);this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode&&(this._memberTreeObj.model.beforeCollapse=t.proxy(t.Pivot._onNodeCollapse,this));this._memberTreeObj.element.find(".e-plus").length==0&&this._memberTreeObj.element.find(".e-item").css("padding","0px");this._isFiltered=!1;this.model.pivotControl._isMemberPageFilter=!1;t.isNullOrUndefined(this._waitingPopup)||this._waitingPopup.hide();t.isNullOrUndefined(this._waitingPopup)||this._waitingPopup.hide();this.element.find("#"+this._id+"_sep1").hover(function(){n(this).removeClass("e-mhover")});this._unWireEvents();this._wireEvents()},_applySorting:function(){var i=JSON.stringify(this.model.pivotControl.model.customObject),n,r;try{n=JSON.parse(this.model.pivotControl.getOlapReport()).Report}catch(u){n=this.model.pivotControl.getOlapReport()}this.model.pivotControl.model.beforeServiceInvoke!=null&&this._trigger("beforeServiceInvoke",{action:"sorting",element:this.element,customObject:this.model.customObject});i=JSON.stringify(this.model.pivotControl.model.customObject);r=JSON.stringify({action:"sorting",sortedHeaders:this.model.pivotControl._ascdes,currentReport:n,valueSorting:JSON.stringify(this.model.pivotControl.model.valueSortSettings),customObject:i});this._waitingPopup=this.element.data("ejWaitingPopup");t.isNullOrUndefined(this._waitingPopup)||this._waitingPopup.show();this.model.pivotControl.model.enableDeferUpdate?t.isNullOrUndefined(this._waitingPopup)||this._waitingPopup.hide():this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.sorting,r,this._filterElementSuccess)},_filterElementClick:function(i){var f=this._selectedFieldName,h,s,c,l,a,b,v,e,k,o,u;if(this._filterAction=t.isNullOrUndefined(i.element)?i.menuId=="valueFilterBtn"?"valueFiltering":i.menuId=="labelFilterBtn"?"labelFiltering":"":n(i.element).parents("li:eq(0)#"+this._id+"_valueFilterBtn").length==0?"labelFiltering":"valueFiltering",h=this.element.find(".groupLabelDrop").data("ejDropDownList"),this._selectedLevelUniqueName=h.getSelectedValue(),this.model.pivotControl._selectedField=this._selectedFieldName,this.model.pivotControl&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode){if(this.model.pivotControl._selectedField=this.model.pivotControl._curFilteredText=this._curFilteredText,this.model.pivotControl._curFilteredAxis=this._curFilteredAxis,this._selectedField=this._curFilteredText,this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Olap)this.model.pivotControl._filterElementClick(i,this);else{try{s=JSON.parse(this.model.pivotControl.getOlapReport()).Report}catch(d){s=this.model.pivotControl.getOlapReport()}if(u=[],c=this._selectedLevelUniqueName,n(i.element).find(".e-descImage").length>0||n(i.element).find(".e-ascImage").length>0||n(i.element).find(".e-clrSort").length>0)return t.isNullOrUndefined(this.model.pivotControl._ascdes)&&(this.model.pivotControl._ascdes=""),this.model.pivotControl._ascdes.indexOf(this._selectedField)>-1?this.model.pivotControl._ascdes=this.model.pivotControl._ascdes.replace(this._selectedField+"##",""):n(i.element).find(".e-clrSort").length==0&&(this.model.pivotControl._ascdes=this.model.pivotControl._ascdes+this._selectedField+"##"),this._applySorting(i),!1;if(n(i.element).find(".e-clrFilter").length>0){this.model.pivotControl._removeFilterTag(this._selectedLevelUniqueName);var y=this._selectedField+"::"+this._curFilteredText+"## Clear Filter"+(this.model.pivotControl&&n(this.model.pivotControl.element).hasClass("e-pivotclient")&&!t.isNullOrUndefined(this.model.pivotControl._ascdes)&&this.model.pivotControl.model.enableAdvancedFilter?">>#>#>>"+this.model.pivotControl._ascdes:""),p=JSON.stringify(this.model.customObject),w=JSON.stringify({action:this._filterAction.toLowerCase(),filterParams:y,sortedHeaders:this._ascdes,currentReport:s,customObject:p,gridLayout:this.model.pivotControl.model.layout});this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.filtering,w,this._filterElementSuccess)}else n(this.model.pivotControl.element).hasClass("e-pivotclient")&&this.model.pivotControl._excelFilterInfo.length>0&&n(i.element).siblings("li:eq(0)").attr("disable")!="true"&&(l=this.model.pivotControl._currentReportName,a=this._selectedField,u=n.map(this.model.pivotControl._excelFilterInfo,function(n){if(n.report==l&&n.hierarchyUniqueName==a||n.levelUniqueName==c)return{value1:n.value1,value2:n.value2,operator:n.operator,measure:n.measure}})),t.Pivot.createAdvanceFilterTag({action:this._filterAction=="labelFiltering"?"labelFilterDlg":"valueFilterDlg",selectedArgs:i,filterInfo:u},this)}return!1}if(n(i.element).attr("id")=="clearAllFilters"?this._clearAllFilter(i):n(i.element).attr("id")=="clearSorting"?this.model.pivotControl._clearSorting(i):(n(i.element).attr("id")=="ascOrder"||n(i.element).attr("id")=="descOrder")&&this._sortField(i),!n(i.element).parent().hasClass("e-filterElementTag")){if(this.element.find(".e-dialog, .filterDialog, #preventDiv").remove(),n.trim(i.text)==n.trim(this._getLocalizedLabels("ClearFilter")))return this._clearFilter(f,this._selectedLevelUniqueName,this.model.pivotControl.model.dataSource),this.model.pivotControl&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot?(t.PivotAnalysis._valueFilterArray=n.grep(t.PivotAnalysis._valueFilterArray,function(n){if(n.fieldName!=r&&n.fieldName.toLowerCase()!=f.toLowerCase())return n}),this.model.pivotControl.refreshControl()):t.olap.base.getJSONData({action:"advancedfiltering"},this.model.pivotControl.model.dataSource,this.model.pivotControl),!1;b=!1;v=this._selectedLevelUniqueName;this.model.pivotControl&&(this.model.pivotControl._selectedLevelUniqueName=this._selectedLevelUniqueName);e=this._getAdvancedFiltervalue(f,this._selectedLevelUniqueName);k="";e=n(i.element).find(".e-activeFilter").length>0&&e.length>0?e[0].values:[""];o=t.Pivot.getReportItemByFieldName(f,this.model.pivotControl.model.dataSource).item;u=[];o&&o.advancedFilter&&(u=n.map(o.advancedFilter,function(n){if(n.name!=r&&n.name.toLowerCase()!=v)return n}));t.Pivot.createAdvanceFilterTag({action:this._filterAction=="labelFiltering"?"labelFilterDlg":"valueFilterDlg",selectedArgs:i,filterInfo:u},this)}},_getAdvancedFiltervalue:function(i,u){if(!(this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode)){var f=t.Pivot.getReportItemByFieldName(i,this.model.pivotControl.model.dataSource,"XMLA").item,e=[];return f.advancedFilter&&(e=n.map(f.advancedFilter,function(n){if(n.name!=r&&n.name.toLocaleLowerCase()==u.toLowerCase())return n})),e}return[]},_filterOptionChanged:function(i){var r=this._getAdvancedFiltervalue(this._selectedFieldName,this._selectedLevelUniqueName),u;r=r.length>0?r[0].advancedFilterType=="label"&&i.value.replace(/ /g,"")==r[0].labelFilterOperator?r[0].values:r[0].advancedFilterType=="value"&&i.value.replace(/ /g,"")==r[0].valueFilterOperator?r[0].values:[""]:[""];u=this.element.find(".filterValuesTd")[0];i.value.toLowerCase().indexOf("between")>=0&&n(u).html("<input type='text' id='"+this._id+"_filterValue1' class='e-filterValues' value='"+r[0]+"' style='display:inline'/> <span>"+this._getLocalizedLabels("and")+"<\/span> <input type='text' id='"+this._id+"_filterValue2' value='"+(t.isNullOrUndefined(r[1])?"":r[1])+"' class='e-filterValues' style='display:inline' /> <\/br>");i.value.toLowerCase().indexOf("between")<0&&n(u).html("<input type='text' id='"+this._id+"_filterValue1' class='e-filterValues' value='"+r[0]+"' style='display:inline'/>")},_removeFilterTag:function(i){var r=this.model.operationalMode==t.Pivot.OperationalMode.ServerMode&&this.model.analysisMode==t.Pivot.AnalysisMode.Pivot,u;(i.indexOf("].")>=0||r)&&(u=i.split("].").length>2||r?"levelUniqueName":"hierarchyUniqueName",this._excelFilterInfo.length>0&&(levelName=this._selectedLevelUniqueName,hierarchyName=this._selectedFieldName,this._excelFilterInfo=n.grep(this._excelFilterInfo,function(n){if(n[u]!=i)return n})))},_filterElementOkBtnClick:function(){var f=this.element.find(".filterOptions")[0].value,i=[this.element.find("#"+this._id+"_filterValue1")[0].value],l=this.element.find("#"+this._id+"_filterValue2").length>0?this.element.find("#"+this._id+"_filterValue2")[0].value:"",o=[],a=this._selectedFieldName,w=this._selectedLevelUniqueName.toLowerCase(),s,h,e,c,u;if(this.model.pivotControl&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode){h="";e="";try{s=JSON.parse(this.model.pivotControl.getOlapReport()).Report}catch(b){s=this.model.pivotControl.getOlapReport()}if(this.model.pivotControl._removeFilterTag(this._selectedLevelUniqueName),this.model.pivotControl.element.find(".reportlist")&&(h=this.model.pivotControl.element.find(".reportlist").data("ejDropDownList").model.value),t.isNullOrUndefined(this._excelFilterInfo)&&(this._excelFilterInfo=[]),this._filterAction=="labelFiltering"){this.model.pivotControl._excelFilterInfo.push({report:h,action:this._filterAction,hierarchyUniqueName:this._selectedFieldName,levelUniqueName:this._selectedLevelUniqueName,operator:f,measure:e,value1:i[0],value2:l});c=this._curFilteredText;this._tempFilterData=n.map(this._tempFilterData,function(n){if(!n[c])return n});var v=this._curFilteredAxis+"::"+this._curFilteredText+"##"+f+"::"+i[0]+(this.model.pivotControl&&n(this.model.pivotControl.element).hasClass("e-pivotclient")&&!t.isNullOrUndefined(this.model.pivotControl._ascdes)&&this.model.pivotControl.model.enableAdvancedFilter?">>#>#>>"+this.model.pivotControl._ascdes:""),y=JSON.stringify(this.model.customObject),p=JSON.stringify({action:this._filterAction,filterParams:v+"-##-"+JSON.stringify(this.model.pivotControl.model.valueSortSettings),sortedHeaders:this._ascdes,currentReport:s,valueSorting:JSON.stringify(this.model.valueSortSettings),customObject:y,gridLayout:this.model.pivotControl.model.layout});this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.filtering,p,this._filterElementSuccess)}else{c=this._curFilteredText;this._measureDDL=this.element.find(".filterMeasures").data("ejDropDownList");e=this._measureDDL.getSelectedValue();this.model.pivotControl._excelFilterInfo.push({report:h,action:this._filterAction,hierarchyUniqueName:this._selectedFieldName,levelUniqueName:this._selectedLevelUniqueName,operator:f,measure:e,value1:i[0],value2:l});this._tempFilterData=n.map(this._tempFilterData,function(n){if(!n[c])return n});var v=this._curFilteredText+"::"+f+"::"+e+"::"+i[0]+"::"+l+(this.model.pivotControl&&n(this.model.pivotControl.element).hasClass("e-pivotclient")&&!t.isNullOrUndefined(this.model.pivotControl._ascdes)&&this.model.pivotControl.model.enableAdvancedFilter?">>#>#>>"+this.model.pivotControl._ascdes:""),y=JSON.stringify(this.model.customObject),p=JSON.stringify({action:this._filterAction,filterParams:v+"-##-"+JSON.stringify(this.model.pivotControl.model.valueSortSettings),sortedHeaders:this._ascdes,currentReport:s,valueSorting:JSON.stringify(this.model.valueSortSettings),customObject:y,gridLayout:this.model.layout});this.doAjaxPost("POST",this.model.pivotControl.model.url+"/"+this.model.serviceMethods.filtering,p,this._filterElementSuccess)}return!1}if(this._filterAction=="valueFiltering"){if(!(!isNaN(parseFloat(i[0]))&&isFinite(i[0]))||this._measureDDL.getSelectedValue()=="")return;this.element.find("#"+this._id+"_filterValue2")[0]!=r?","+i.push(this.element.find("#"+this._id+"_filterValue2")[0].value):i}this.element.find(".e-dialog").remove();u=t.Pivot.getReportItemByFieldName(a,this.model.pivotControl.model.dataSource).item;u&&(delete u.filterItems,u.advancedFilter&&(o=n.map(u.advancedFilter,function(n){if(n.name!=r&&n.name.toLocaleLowerCase()!=w.toLowerCase())return n})),this._filterAction.toLowerCase()=="labelfiltering"?o.push({name:this._selectedLevelUniqueName,labelFilterOperator:f.replace(/ /g,""),advancedFilterType:t.olap.AdvancedFilterType.LabelFilter,values:i}):o.push({name:this._selectedLevelUniqueName,valueFilterOperator:f.replace(/ /g,""),advancedFilterType:t.olap.AdvancedFilterType.ValueFilter,values:i,measure:this._measureDDL.getSelectedValue()}),this.model.pivotControl._currentReportItems=n.grep(this.model.pivotControl._currentReportItems,function(n){if(n.fieldName!=a)return n}),u.advancedFilter=o);this.model.pivotControl&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot?(t.PivotAnalysis._valueFilterArray=n.grep(t.PivotAnalysis._valueFilterArray,function(n){if(n.fieldName!=r&&n.fieldName.toLowerCase()!=a.toLowerCase())return n}),this._filterAction!="valueFiltering"||t.isNullOrUndefined(u)||t.PivotAnalysis._valueFilterArray.push(u),this.model.pivotControl.refreshControl()):t.olap.base.getJSONData({action:"advancedfiltering"},this.model.pivotControl.model.dataSource,this.model.pivotControl)},_groupLabelChange:function(i){var f=this.element.find(".e-filterElementTag").data("ejMenu"),r,s,p,e,h,c,w,l,k,d;if(f.disableItemByID("labelClearFilter"),f.disableItemByID("valueClearFilter"),f.disableItemByID("clearAllFilters"),f.disableItemByID("clearSorting"),this.model.pivotControl&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode)return this.model.pivotControl._selectedLevelUniqueName=i.selectedValue,r=[],s=t.buildTag("span.e-filterState").addClass("e-icon").attr("aria-label","filter state")[0].outerHTML,this.model.pivotControl._excelFilterInfo.length>0&&(c=this.model.pivotControl._selectedLevelUniqueName,p=this.model.pivotControl._selectedFieldName,r=n.map(this.model.pivotControl._excelFilterInfo,function(n){if(n.hierarchyUniqueName==p&&n.levelUniqueName==c)return{action:n.action,operator:n.operator,value1:n.value1}})),this.element.find(".e-filterElementTag .e-activeFilter,.e-filterState").remove(),this.element.find("#"+this._id+"_labelClearFilter","#"+this._id+"_valueClearFilter").css("opacity","0.5").attr("disable",!0),r.length>0&&!t.isNullOrUndefined(r[0].operator)?(e="",h="",r[0].action.toLowerCase()=="valuefiltering"?(e="valueFilterBtn",h=h=r[0].operator=="equals"||r[0].operator=="not equals"||r[0].operator=="less than or equal to"||r[0].operator=="greater than or equal to"||r[0].operator=="greater than"||r[0].operator=="less than"?"_valueFilter":""):(e="labelFilterBtn",h=r[0].operator=="equals"||r[0].operator=="not equals"||r[0].operator=="less than or equal to"||r[0].operator=="greater than or equal to"||r[0].operator=="greater than"||r[0].operator=="less than"?"_labelFilter":""),e=="labelFilterBtn"?f.enableItemByID("labelClearFilter"):f.enableItemByID("valueClearFilter"),f.enableItemByID("clearAllFilters"),this.element.find("#"+e+" a:eq(0)").append(s),this.element.find("#"+e+"#"+this._id+"_labelClearFilter").removeAttr("style disable"),this.element.find("#"+e+"#"+this._id+"_valueClearFilter").removeAttr("style disable"),r[0].operator.replace(/ /g,"")=="BottomCount"?this.element.find("#"+e+" li#"+r[0].operator.replace(/ /g,"").replace("Bottom","top")+" a").append(n(t.buildTag("span.e-activeFilter e-icon")[0].outerHTML)):this.element.find("#"+e+" li#"+r[0].operator.replace(/ /g,"")+h+" a").append(n(t.buildTag("span.e-activeFilter e-icon")[0].outerHTML))):this._getUnSelectedNodes()!=""&&this.element.find(".e-memberEditorDiv").before("<div class='e-filterState e-icon' style='top:126px;position:absolute;visibility:hidden'  />"),this.model.pivotControl&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&(l=this.element.find("#"+this._id+"_clearAllFilters a").children().clone(),this.element.find("#"+this._id+"_clearAllFilters a").text(this._getLocalizedLabels("ClearFilter")+' from "'+i.text+'"').append(l),c=this.model.pivotControl._curFilteredText,w=t.isNullOrUndefined(this.model.pivotControl._ascdes)?[]:n.grep(this.model.pivotControl._ascdes.split("##"),function(n){if(n==c)return n}),w.length>0?(this.element.find(".e-descImage").addClass("e-selectedSort"),f.enableItemByID("clearSorting")):this.element.find(".e-ascImage").addClass("e-selectedSort"),r.length>0&&(this.element.find(".e-editorTreeView").data("ejTreeView").checkAll(),this._editorTreeData=n.map(this._editorTreeData,function(n){return n.checkedStatus=!0,n}),this._editorDrillTreeData=n.map(this._editorTreeData,function(n){return n.checkedStatus=!0,n}),this._editorSearchTreeData=n.map(this._editorTreeData,function(n){return n.checkedStatus=!0,n}))),!1;var o=this,g=this.element.find(".groupLabelDrop").length>0?this.element.find(".groupLabelDrop").data("ejDropDownList"):[],u=o._getAdvancedFiltervalue(o._selectedFieldName,g.getSelectedValue()),f=this.element.find(".e-filterElementTag").data("ejMenu"),a=t.Pivot.getReportItemByFieldName(o._selectedFieldName,o.model.pivotControl.model.dataSource).item,s=t.buildTag("span.e-filterState").addClass("e-icon").attr("aria-label","filter state")[0].outerHTML,b=t.buildTag("span.e-activeFilter").addClass("e-icon")[0].outerHTML,v=t.Pivot.getReportItemByFieldName(o._selectedFieldName,o.model.pivotControl.model.dataSource).item,y;v&&(v.sortOrder&&v.sortOrder!=t.olap.SortOrder.None?(y=v.sortOrder==t.olap.SortOrder.Descending?"desc":"asc",t.isNullOrUndefined(y)||y=="asc"?this.element.find(".e-clientDialog .e-ascImage").addClass("e-selectedSort"):(this.element.find(".e-clientDialog .e-descImage").addClass("e-selectedSort"),f.enableItemByID("clearSorting"))):this.element.find("#"+this._id+"_clearSorting").css("opacity","0.5").attr("disabled","disabled"));this.element.find("#"+this._id+"_clearAllFilters").css("opacity","0.5").attr("disabled","disabled");this.element.find(".e-filterState,.e-activeFilter").remove();l=this.element.find("#"+this._id+"_clearAllFilters a").children().clone();this.element.find("#"+this._id+"_clearAllFilters a").text(this._getLocalizedLabels("ClearFilter")+' from "'+i.text+'"').append(l);u.length>0?(u[0].advancedFilterType==t.olap.AdvancedFilterType.LabelFilter?(this.element.find("#"+this._id+"_labelFilterBtn a:eq(0)").append(s),this.element.find("#"+this._id+"_labelFilterBtn .clearFilter").css("opacity","1").removeAttr("disabled"),k=u[0].labelFilterOperator=="equals"||u[0].labelFilterOperator=="notequals"||u[0].labelFilterOperator=="lessthanorequalto"||u[0].labelFilterOperator=="greaterthanorequalto"||u[0].labelFilterOperator=="greaterthan"||u[0].labelFilterOperator=="lessthan"?"_labelFilter":"",this.element.find("#"+this._id+"_labelFilterBtn li#"+u[0].labelFilterOperator+k+" a").append(b),f.enableItemByID("labelClearFilter")):(this.element.find("#"+this._id+"_valueFilterBtn a:eq(0)").append(s),this.element.find("#"+this._id+"_valueFilterBtn .clearFilter").css("opacity","1").removeAttr("disabled"),d=u[0].valueFilterOperator=="equals"||u[0].valueFilterOperator=="notequals"||u[0].valueFilterOperator=="lessthanorequalto"||u[0].valueFilterOperator=="greaterthanorequalto"||u[0].valueFilterOperator=="greaterthan"||u[0].valueFilterOperator=="lessthan"?"_valueFilter":"",this.element.find("#"+this._id+"_valueFilterBtn li#"+u[0].valueFilterOperator+d+" a").append(b),f.enableItemByID("valueClearFilter")),f.enableItemByID("clearAllFilters"),this.element.find("#"+this._id+"_clearAllFilters").css("opacity","1").removeAttr("disabled")):(a.length>0&&a[0].filterItems||this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&a&&a.filterItems)&&(this.element.find(".e-memberEditorDiv").before(s),this.element.find(".e-filterState").addClass("memberFilter").css("visibility","hidden"),this.element.find("#"+this._id+"_clearAllFilters").css("opacity","1").removeAttr("disabled"))},_clearAllFilter:function(){var i,u;t.Pivot.closePreventPanel(this);this.element.find(".e-dialog").remove();this._clearFilter(this._selectedFieldName,this._selectedLevelUniqueName,this.model.pivotControl.model.dataSource);i=t.Pivot.getReportItemByFieldName(this._selectedFieldName,this.model.pivotControl.model.dataSource);i&&i.item&&i.item.filterItems&&(i.item.filterItems.values=[]);this.model.pivotControl&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot?(u=this._selectedFieldName,t.PivotAnalysis._valueFilterArray=n.grep(t.PivotAnalysis._valueFilterArray,function(n){if(n.fieldName!=r&&n.fieldName.toLowerCase()!=u.toLowerCase())return n}),this.model.pivotControl.refreshControl()):t.olap.base.getJSONData({action:"advancedfiltering"},this.model.pivotControl.model.dataSource,this.model.pivotControl)},_clearFilter:function(i,u,f){var e=t.Pivot.getReportItemByFieldName(i,f);e.item&&e.item.advancedFilter&&(u=this.model.pivotControl&&this.model.pivotControl.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ClientMode?i:u,e.item.advancedFilter=n.map(e.item.advancedFilter,function(n){if(n.name!=r&&n.name.toLowerCase()!=u.toLowerCase())return n}))},_createPivotButton:function(i,r,u,f,e){var l="",a="",o,c,w,b,k;if(u&&(l="filtered"),f&&(a="descending"),o=r=="row"?".e-schemaRow":r=="column"?".e-schemaColumn":r=="value"?".e-schemaValue":".e-schemaFilter",this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode)var h=r=="column"?"columns":r=="row"?"rows":r=="filter"?"filters":"values",v=(r=="column"||r=="row")&&this.model.layout!="excel"&&this._dataModel!="Olap"&&this._dataModel!="XMLA"?t.buildTag("span.sorting e-icon "+a).attr("role","button").attr("aria-label","sort").attr("aria-expanded","false")[0].outerHTML:"",y=(r=="column"||r=="row"||r=="filter")&&this.model.layout!="excel"&&i.fieldCaption!="Measures"?t.buildTag("span.filter e-icon "+l).attr("role","button").attr("aria-label","filter")[0].outerHTML:"",p=this.model.layout!="excel"?t.buildTag("span.e-removeBtn e-icon").attr("role","button").attr("aria-label","remove")[0].outerHTML:"",s=t.buildTag("div.e-pivotButton",t.buildTag("div.e-dropIndicator")[0].outerHTML+t.buildTag("div.pvtBtnDiv",t.buildTag("button.e-pvtBtn#"+this._id+"_pivotButton"+i.fieldName,this.model.pivotControl._dataModel!="XMLA"?n.grep(this.model.pivotTableFields,function(n){return n.name==i.fieldName})[0].caption:i.fieldCaption,{},{"data-fieldName":i.fieldName,"data-fieldCaption":i.fieldCaption,"data-axis":h})[0].outerHTML+y+v+p).attr("data-tag",h+":"+i.fieldName)[0].outerHTML).attr("data-tag",h+":"+i.fieldName)[0].outerHTML;else{c="";w={DimensionName:i.fieldName,DimensionHeader:i.fieldCaption,Tag:i.fieldName};t.isNullOrUndefined(this.model.pivotControl)||r!="filter"||(c=t.Pivot._getFilterState("",[],w,this.model.pivotControl));var h=r=="column"?"columns":r=="row"?"rows":r=="filter"?this.model.pivotControl.model.analysisMode==t.PivotGrid.AnalysisMode.Pivot?"filters":"slicers":"values",v=(r=="column"||r=="row")&&this.model.layout!="excel"&&this._dataModel!="Olap"&&this._dataModel!="XMLA"?t.buildTag("span.sorting e-icon "+a).attr("role","button").attr("aria-label","sort").attr("aria-expanded","false")[0].outerHTML:"",y=(r=="column"||r=="row"||r=="filter")&&this.model.layout!="excel"&&i.fieldCaption!="Measures"?t.buildTag("span.filter e-icon "+l).attr("role","button").attr("aria-label","filter")[0].outerHTML:"",p=this.model.layout!="excel"?t.buildTag("span.e-removeBtn e-icon").attr("role","button").attr("aria-label","remove")[0].outerHTML:"",s=t.buildTag("div.e-pivotButton",t.buildTag("div.e-dropIndicator")[0].outerHTML+t.buildTag("div.pvtBtnDiv",t.buildTag("button.e-pvtBtn#"+this._id+"_pivotButton"+i.fieldName,(this._dataModel=="Pivot"?i.fieldCaption:i=="Measures"?i:!t.isNullOrUndefined(i.fieldCaption)&&i.fieldCaption!=""?i.fieldCaption:i.fieldName.split(".")[1])+(c!=""?" ("+c+")":""),{},{"data-fieldName":i.fieldName,"data-fieldCaption":i.fieldCaption,"data-axis":h})[0].outerHTML+y+v+p).attr("data-tag",h+":"+i.fieldName)[0].outerHTML).attr("data-tag",h+":"+(t.isNullOrUndefined(i.fieldName)?i:i.fieldName))[0].outerHTML}typeof e=="number"&&n(this.element.find(o+" .e-pivotButton")[e]).length>0?r=="row"?n(this.element.find(".e-schemaRow .e-pivotButton")[e]).before(s):r=="column"?n(this.element.find(".e-schemaColumn .e-pivotButton")[e]).before(s):r=="value"?n(this.element.find(".e-schemaValue .e-pivotButton")[e]).before(s):r=="filter"?n(this.element.find(".e-schemaFilter .e-pivotButton")[e]).before(s):"":r=="row"?this.element.find(".e-schemaRow").append(s):r=="column"?this.element.find(".e-schemaColumn").append(s):r=="value"?this.element.find(".e-schemaValue").append(s):r=="filter"?this.element.find(".e-schemaFilter").append(s):"";b=typeof e!="number"&&e==""?" .e-pivotButton .e-pvtBtn:last":" .e-pivotButton .e-pvtBtn:eq("+e+")";this.model.enableDragDrop&&this.element.find(o+b).ejButton({size:"normal",type:t.ButtonType.Button,enableRTL:this.model.enableRTL}).ejDraggable({handle:"button",clone:!0,cursorAt:{left:-5,top:-5},dragStart:t.proxy(function(){this._isDragging=!0},this),dragStop:t.proxy(this.model.pivotControl.model.operationalMode==t.Pivot.OperationalMode.ServerMode?this._pvtBtnDropped:this._clientOnPvtBtnDropped,this),helper:t.proxy(function(t){if(n(t.element).addClass("dragHover"),t.sender.target.className.indexOf("e-btn")>-1){var i=n(t.sender.target).clone().attr("id",this._id+"_dragClone").appendTo("body");return n("#"+this._id+"_dragClone").removeAttr("style").height(n(t.sender.target).height()),i}return!1},this)});this.element.find(o+" .e-pivotButton .filterBtn:last").ejButton({size:"normal",enableRTL:this.model.enableRTL,type:t.ButtonType.Button,contentType:"imageonly",prefixIcon:"filter"});this.element.find(o+" .e-pivotButton input:last").ejToggleButton({size:"normal",enableRTL:this.model.enableRTL,contentType:"imageonly",defaultPrefixIcon:"ascending",activePrefixIcon:"descending",click:t.proxy(this._sortBtnClick,this)});f&&(this.element.find(o+" .e-pivotButton:contains("+i+") span:eq(0) span").removeClass("ascending").addClass("descending"),this.element.find(o+" .e-pivotButton:contains("+i+") span:eq(0) button").addClass("e-active"));this.model.pivotControl._dataModel=="XMLA"&&this.element.find(".e-pivotButton[data-tag*=':Measures']").length>0&&(k=n(o+" .e-pivotButton[data-tag*=':Measures']"),n(o).append(k));this.element.find(".e-dropIndicator").removeClass("e-dropIndicatorHover");this._createContextMenu()},_createContextMenu:function(){var u=this.element.find(".e-pivotButton"),r=t.buildTag("li.e-separator")[0].outerHTML,i=this.model.pivotControl,f=r+"<li id="+this._id+"_addsummary ><a>"+this._getLocalizedLabels("SummarizeValueBy")+"<\/a><ul><li id="+this._id+"_sum class=summarytype><a>"+this._getLocalizedLabels("Sum")+"<\/a><li><li id="+this._id+"_average  class=summarytype ><a>"+this._getLocalizedLabels("Average")+"<\/a><li><li id="+this._id+"_count  class=summarytype ><a>"+this._getLocalizedLabels("Count")+"<\/a><li><li id="+this._id+"_min  class=summarytype><a>"+this._getLocalizedLabels("Min")+"<\/a><li><li id="+this._id+"_max  class=summarytype ><a>"+this._getLocalizedLabels("Max")+"<\/a><li><\/ul><\/li>",e=r+"<li id="+this._id+"_addsummary ><a>"+this._getLocalizedLabels("SummarizeValueBy")+"<\/a><ul><li id="+this._id+"_Sum  class=summarytype><a>"+this._getLocalizedLabels("Sum")+"<\/a><li><li id="+this._id+"_Average  class=summarytype ><a>"+this._getLocalizedLabels("Average")+"<\/a><li><li id="+this._id+"_Count  class=summarytype ><a>"+this._getLocalizedLabels("Count")+"<\/a><li><li id="+this._id+"_Min  class=summarytype><a>"+this._getLocalizedLabels("Min")+"<\/a><li><li id="+this._id+"_Max  class=summarytype ><a>"+this._getLocalizedLabels("Max")+"<\/a><li><li id="+this._id+"_Doubletotalsum  class=summarytype><a>"+this._getLocalizedLabels("DoubleSum")+"<\/a><li><li id="+this._id+"_Doubleaverage  class=summarytype ><a>"+this._getLocalizedLabels("DoubleAverage")+"<\/a><li><li id="+this._id+"_Doublemin  class=summarytype><a>"+this._getLocalizedLabels("DoubleMin")+"<\/a><li><li id="+this._id+"_Doublemax  class=summarytype ><a>"+this._getLocalizedLabels("DoubleMax")+"<\/a><li><li id="+this._id+"_Deviation  class=summarytype ><a>"+this._getLocalizedLabels("DoubleStandardDeviation")+"<\/a><li><li id="+this._id+"_Doublevariance  class=summarytype ><a>"+this._getLocalizedLabels("DoubleVariance")+"<\/a><li><li id="+this._id+"_Decimalsum  class=summarytype ><a>"+this._getLocalizedLabels("DecimalSum")+"<\/a><li><li id="+this._id+"_Inttotalsum  class=summarytype ><a>"+this._getLocalizedLabels("IntSum")+"<\/a><li><li id="+this._id+"_CountNumbers  class=summarytype ><a>"+this._getLocalizedLabels("CountNumbers")+"<\/a><li><li id="+this._id+"_StdDev class=summarytype ><a>"+this._getLocalizedLabels("StdDev")+"<\/a><li><li id="+this._id+"_StdDevP class=summarytype ><a>"+this._getLocalizedLabels("StdDevP")+"<\/a><li><li id="+this._id+"_Var class=summarytype ><a>"+this._getLocalizedLabels("Variance")+"<\/a><li><li id="+this._id+"_VarP class=summarytype ><a>"+this._getLocalizedLabels("VarP")+"<\/a><li><\/ul><\/li>",o=t.isNullOrUndefined(i)?"":i.model.analysisMode==t.Pivot.AnalysisMode.Olap||this.element.parents(".e-pivotclient").length==0?" ":i.model.operationalMode==t.Pivot.OperationalMode.ClientMode&&this.element.parents(".e-pivotclient").length!=0?f:this.element.parents(".e-pivotclient").length!=0?e:"",s=t.isNullOrUndefined(i)?"auto":i.model.analysisMode==t.Pivot.AnalysisMode.Pivot&&i.locale()=="en-US"?"180px":"auto",h=t.buildTag("ul.pivotTreeContextMenu#"+this._id+"_pivotTreeContextMenu",t.buildTag("li",t.buildTag("a",this._getLocalizedLabels("AddToFilter"))[0].outerHTML)[0].outerHTML+t.buildTag("li",t.buildTag("a",this._getLocalizedLabels("AddToRow"))[0].outerHTML)[0].outerHTML+t.buildTag("li",t.buildTag("a",this._getLocalizedLabels("AddToColumn"))[0].outerHTML)[0].outerHTML+t.buildTag("li",t.buildTag("a",this._getLocalizedLabels("AddToValues"))[0].outerHTML)[0].outerHTML+o)[0].outerHTML;n(this.element).append(h);n("#"+this._id+"_pivotTreeContextMenu").ejMenu({menuType:t.MenuType.ContextMenu,enableRTL:this.model.enableRTL,openOnClick:!1,contextMenuTarget:u,click:t.proxy(this._contextClick,this),beforeOpen:t.proxy(this._onContextOpen,this),close:t.proxy(t.Pivot.closePreventPanel,this),width:s})},_createPivotButtons_1:function(i,u){var o,e,l,f;if(!t.isNullOrUndefined(i)){if(o="",e=u=="column"?"Columns":u=="row"?"Rows":u=="filter"?"Slicers":"",this.model.pivotControl!=null&&this.model.pivotControl.model.operationalMode==t.PivotGrid.OperationalMode.ClientMode)for(e=u=="column"?"Columns":u=="row"?"Rows":u=="filter"?"Slicers":"values",e=="values"&&this.model.pivotControl._dataModel=="XMLA"&&(i=n.map(i,function(n){return n.measures})),f=0;f<i.length;f++){var a=this.model.pivotControl._dataModel=="XMLA"||this.model.pivotControl._dataModel=="Olap"?"":t.isNullOrUndefined(i[f].filterItems)?"":"filtered",v=this.model.pivotControl._dataModel=="XMLA"||this.model.pivotControl._dataModel=="Olap"?"":i[f].sortOrder==t.PivotAnalysis.SortOrder.Descending?"descending":"",y=this.model.pivotControl._dataModel=="XMLA"?"":i[f].fieldName,s=(u=="column"||u=="row")&&this.model.layout!="excel"&&this.model.pivotControl._dataModel!="Olap"&&this.model.pivotControl._dataModel!="XMLA"?t.buildTag("span.sorting e-icon "+v).attr("role","button").attr("aria-label","sort").attr("aria-expanded","false")[0].outerHTML:"",h=(u=="column"||u=="row"||u=="filter")&&this.model.layout!="excel"&&i[f].fieldCaption!="Measures"?t.buildTag("span.filter e-icon "+a).attr("role","button").attr("aria-label","filter")[0].outerHTML:"",c=this.model.layout!="excel"?t.buildTag("span.e-removeBtn e-icon").attr("role","button").attr("aria-label","remove")[0].outerHTML:"";o+=t.buildTag("div.e-pivotButton",t.buildTag("div.e-dropIndicator")[0].outerHTML+t.buildTag("div.pvtBtnDiv",t.buildTag("button.e-pvtBtn#"+this._id+"_pivotButton"+y,this.model.pivotControl._dataModel=="XMLA"?(i[f].fieldCaption!=r?i[f].fieldCaption:i[f].fieldName)||i[f]:n.grep(this.model.pivotTableFields,function(n){return n.name==i[f].fieldName})[0].caption||n.grep(this.model.pivotTableFields,function(n){return n.name==i[f]})[0].caption,{},{"data-fieldName":i[f].fieldName||i[f],"data-axis":e})[0].outerHTML+h+s+c).attr("data-tag",e+":"+i[f].fieldName||i[f])[0].outerHTML).attr("data-tag",e+":"+i[f].fieldName||i[f])[0].outerHTML}else for(e==""&&u=="values"&&(l=this.element.find(".e-pivotButton:contains("+this._getLocalizedLabels("Measures")+")").length>0?this.element.find(".e-pivotButton:contains('"+this._getLocalizedLabels("Measures")+"')").parent()[0].className.split(" ")[0]:"e-schemaColumn",e=l=="e-schemaColumn"?"Columns":"Rows"),f=0;f<i.length;f++){i[f].FieldHeader=i[f].FieldHeader=="Measures"?this._getLocalizedLabels("Measures"):i[f].FieldHeader;var s=(u=="column"||u=="row")&&this.model.layout!="excel"&&this._dataModel!="Olap"&&this._dataModel!="XMLA"?t.buildTag("span.sorting e-icon ").attr("role","button").attr("aria-label","sort").attr("aria-expanded","false")[0].outerHTML:"",h=(u=="column"||u=="row"||u=="filter")&&this.model.layout!="excel"&&i[f].FieldHeader!="Measures"?t.buildTag("span.filter e-icon").attr("role","button").attr("aria-label","filter")[0].outerHTML:"",c=this.model.layout!="excel"?t.buildTag("span.e-removeBtn e-icon").attr("role","button").attr("aria-label","remove")[0].outerHTML:"";o+=t.buildTag("div.e-pivotButton",t.buildTag("div.e-dropIndicator")[0].outerHTML+t.buildTag("div.pvtBtnDiv",t.buildTag("button.e-pvtBtn#"+this._id+"_pivotButton"+i[f].FieldHeader||i[f].FieldName||i[f].Name||i[f].DimensionName,i[f].FieldHeader||i[f].FieldName||i[f].Name||i[f].DimensionName)[0].outerHTML+h+s+c).attr("data-tag",e+":"+i[f].Tag)[0].outerHTML).attr("data-tag",e+":"+i[f].Tag)[0].outerHTML}return this._createContextMenu(),o}},_setSplitBtnTargetPos:function(i){var s="",e,u,f,o;if(i.event!=r&&i.event.type=="touchend"?(u=i.event.originalEvent.target!=null?n(i.event.originalEvent.target).parents(".e-pivotButton"):n(i.event.originalEvent.srcElement).parents(".e-pivotButton"),f=i.event.originalEvent.target!=null?i.event.originalEvent.target.className:i.event.originalEvent.srcElement.className):(u=t.isNullOrUndefined(i.originalEvent)?n(i.target).parents(".e-pivotButton"):i.originalEvent.target!=null?n(i.originalEvent.target).parents(".e-pivotButton"):n(i.originalEvent.srcElement).parents(".e-pivotButton"),f=t.isNullOrUndefined(i.originalEvent)?n(i.target).attr("class")?n(i.target).attr("class"):"":i.originalEvent.target!=null&&n(i.originalEvent.target).attr("class")?n(i.originalEvent.target).attr("class"):n(i.originalEvent.srcElement).attr("class")?n(i.originalEvent.srcElement).attr("class"):""),this._droppedClass=u.length>0?n(u[0]).parent()[0].className.split(" ")[0]:f.split(" ")[0],u[0]||f!=r&&f!=null&&jQuery.type(f)=="string"&&f.indexOf("e-pivotButton")>-1)for(u=u[0]?u[0]:i.originalEvent.target!=null?i.originalEvent.target:i.originalEvent.srcElement,e=i.event!=r&&i.event.type=="touchend"?i.target:this._droppedClass!=null&&this._droppedClass!=r&&this._droppedClass!=""?this.element.find("."+this._droppedClass)[0]:u.parentNode,e=n(e).children(".e-pivotButton"),o=0;o<e.length;o++)n(e[o]).attr("data-tag")==n(u).attr("data-tag")&&(s=o);return s},_getUnSelectedNodes:function(){for(var r,f=this.element.find(".e-editorTreeView")[0],u="",i=n(f).find(":input.nodecheckbox:not(:checked)"),t=0;t<i.length;t++)n(i[t].parentElement).find("span:nth-child(1)").attr("class").indexOf("e-chk-act")>-1||n(i[t].parentElement).attr("aria-checked")=="mixed"||(r=n(i[t]).parents("li:eq(0)"),u+="::"+r[0].id+"||"+n(r).attr("data-tag")+(this._dialogHead=="KPI"?"~&"+n(n(i[t]).parents("ul:eq(1) li")[1]).attr("data-tag"):""));return u},_getSelectedNodes:function(t){var o,s,e,r,i;if(t){var h=this.element.find(".e-editorTreeView")[0].childNodes[0],f=[],u=n(h).children();for(r=0;r<u.length;r++){if(i=u[r],o={caption:n(i.firstChild).find("a").text(),parentId:i.parentElement.parentElement.className.indexOf("e-editorTreeView")>-1?"None":n(i).parents()[1].id,id:i.id,checked:n(i).find(":input.nodecheckbox")[0].checked||n(i).find("span:nth-child(1)").attr("class").indexOf("e-chk-indeter")>-1,expanded:n(i.firstChild).find(".e-minus").length>0?!0:!1,childNodes:[],tag:n(i).attr("data-tag")},n(i).find("ul:first").children().length>0)for(s=n(i).find("ul:first").children(),e=0;e<s.length;e++)o.childNodes.push(this._getNodeInfo(s[e]));f.push(o)}return JSON.stringify(f)}var h=n(".e-editorTreeView"),f="",u=n(h).find(":input.nodecheckbox");for(r=0;r<u.length;r++)(u[r].checked||n(u[r].parentElement).attr("aria-checked")=="mixed")&&(i=n(u[r]).parents("li:eq(0)"),f+="::"+i[0].id+"||"+n(i).attr("data-tag")+(this._dialogHead=="KPI"?"~&"+n(n(u[r]).parents("ul:eq(1) li")[1]).attr("data-tag"):""));return f},_getNodeInfo:function(t){var u={caption:n(t.firstChild).find("a").text(),parentId:t.parentElement.parentElement.className.indexOf("e-editorTreeView")>-1?"None":n(t).parents()[1].id,id:t.id,checked:n(t).find(":input.nodecheckbox")[0].checked||n(t).find("span:nth-child(1)").attr("class").indexOf("e-chk-indeter")>-1,expanded:n(t.firstChild).find(".e-minus").length>0?!0:!1,childNodes:[],tag:n(t).attr("data-tag")},r,i;if(n(t).find("ul:first").children().length>0)for(r=n(t).find("ul:first").children(),i=0;i<r.length;i++)u.childNodes.push(this._getNodeInfo(r[i]));return u},doAjaxPost:function(i,u,f,e,o,s){var h,c,l,a,v,y;this.model.pivotControl.model.editCellsInfo={};a=!0;v=this.model.enableXHRCredentials||this.model.pivotControl.model.enableXHRCredentials;f.XMLA==r?(h="application/json; charset=utf-8",c="json",l=n.proxy(e,this)):(h="text/xml",c="xml",f=f.XMLA,l=n.proxy(e,this,s),a=!t.isNullOrUndefined(s)&&s.action!="loadFieldElements"?!0:!1);y={type:i,url:u,contentType:h,dataType:c,async:a,data:f,success:l,xhrFields:{withCredentials:v},complete:n.proxy(o,this),error:function(){this.model.pivotControl._ogridWaitingPopup.hide()}};v||delete y.xhrFields;n.ajax(y)}});t.PivotSchemaDesigner.Locale=t.PivotSchemaDesigner.Locale||{};t.PivotSchemaDesigner.Locale["en-US"]={Sort:"Sort",Search:"Search",SelectField:"Select field",LabelFilterLabel:"Show the items for which the label",ValueFilterLabel:"Show the items for which",ClearSorting:"Clear Sorting",ClearFilterFrom:"Clear Filter From",SortAtoZ:"Sort A to Z",SortZtoA:"Sort Z to A",and:"and",LabelFilters:"Label Filters  ",BeginsWith:"Begins With",DoesNotBeginsWith:"Not Begins With",EndsWith:"Ends With",DoesNotEndsWith:"Not Ends With",Contains:"Contains",DoesNotContains:"Not Contains",ValueFilters:"Value Filters",ClearFilter:"Clear Filter",Equals:"Equals",DoesNotEquals:"Not Equals",GreaterThan:"Greater Than",GreaterThanOrEqualTo:"Greater Than Or Equal To",LessThan:"Less Than",LessThanOrEqualTo:"Less Than Or Equal To",Between:"Between",NotBetween:"Not Between",Top10:"Top Count",GreaterThan:"Greater Than",IsGreaterThan:"Is Greater Than",IsGreaterThanOrEqualTo:"Is Greater Than Or Equal To",IsLessThan:"Is Less Than",IsLessThanOrEqualTo:"Is Less Than Or Equal To",ClearFilter:"Clear Filter",SelectField:"Select field",Measures:"Measures",Warning:"Warning",AlertMsg:"The field you are moving cannot be placed in that area of the report",Goal:"Goal",Status:"Status",Trend:"Trend",Value:"Value",AddToFilter:"Add to Filter",AddToRow:"Add to Row",AddToColumn:"Add to Column",AddToValues:"Add to Value",SummarizeValueBy:"Summarize value by",Sum:"Sum",Average:"Average",Count:"Count",Min:"Min",Max:"Max",DoubleSum:"DoubleSum",DoubleAverage:"DoubleAverage",DoubleMin:"DoubleMin",DoubleMax:"DoubleMax",DoubleStandardDeviation:"DoubleStandardDeviation",DoubleVariance:"DoubleVariance",DecimalSum:"DecimalSum",IntSum:"IntSum",CountNumbers:"Count Numbers",StdDev:"StdDev",StdDevP:"StdDevP",Variance:"Var",VarP:"VarP",SummaryOf:"of",PivotTableFieldList:"PivotTable Field List",ChooseFieldsToAddToReport:"Choose fields to add to the report:",DragFieldBetweenAreasBelow:"Drag fields between areas below:",ReportFilter:"Filters",ColumnLabel:"Columns",RowLabel:"Rows",Values:"Values",DeferLayoutUpdate:"Defer Layout Update",Update:"Update",OK:"OK",Remove:"Remove",Cancel:"Cancel",Close:"Close",AddCurrentSelectionToFilter:"Add current selection to filter",NotAllItemsShowing:"Not all child nodes are shown",EditorLinkPanelAlert:"The members have more than 1000 items under one or more parent. Only the first 1000 items are displayed under each parent.",NamedSetAlert:"Named sets cannot be added to the PivotTable report at the same time. Click OK to remove ' <Set 1> ' named set and add ' <Set 2> ' named set."};t.PivotSchemaDesigner.Layouts={Excel:"excel",Normal:"normal",OneByOne:"onebyone"}})(jQuery,Syncfusion)});
