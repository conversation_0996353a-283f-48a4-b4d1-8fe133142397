﻿CREATE TABLE [dbo].[Questionnaires] (
    [QuestionnaireId]    BIGINT         IDENTITY (1, 1) NOT NULL,
    [ContactId]          BIGINT         NOT NULL,
    [Name]               NVARCHAR (50)  CONSTRAINT [DF_Questionnaires_Name] DEFAULT ('') NOT NULL,
    [CreateDate]         DATETIME       CONSTRAINT [DF_Questionnaires_CreateDate] DEFAULT (getdate()) NOT NULL,
    [QuestionnaireInput] NVARCHAR (MAX) CONSTRAINT [DF_Questionnaires_QuestionnaireInput] DEFAULT ('') NOT NULL,
    [QuestionnaireCode]  NVARCHAR (500) CONSTRAINT [DF_Questionnaires_QuestonnaireId] DEFAULT ('') NOT NULL,
    CONSTRAINT [PK_Questionnaires] PRIMARY KEY CLUSTERED ([QuestionnaireId] ASC),
    CONSTRAINT [FK_Questionnaires_Contacts] FOREIGN KEY ([ContactId]) REFERENCES [dbo].[Contacts] ([ContactId]) ON DELETE CASCADE ON UPDATE CASCADE
);










GO
CREATE NONCLUSTERED INDEX [ContactIdIndex]
    ON [dbo].[Questionnaires]([ContactId] ASC);

