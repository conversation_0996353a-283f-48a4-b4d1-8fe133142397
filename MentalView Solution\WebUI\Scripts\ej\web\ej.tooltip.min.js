/*!
*  filename: ej.tooltip.min.js
*  version : *********
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){"use strict";var n=this&&this.__extends||function(n,t){function r(){this.constructor=n}for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)};(function(t){var i=function(i){function r(n,r){return i.call(this),this._rootCSS="e-tooltip",this._setFirst=!1,this.PluginName="ejTooltip",this.id="null",this.model=null,this.defaults={height:"auto",width:"auto",enabled:!0,content:null,containment:"body",target:null,title:null,closeMode:"none",autoCloseTimeout:4e3,position:{stem:{horizontal:"center",vertical:"bottom"},target:{horizontal:"center",vertical:"top"}},associate:"target",collision:"flipfit",showShadow:!1,cssClass:null,animation:{effect:"none",speed:0},isBalloon:!0,showRoundedCorner:!1,enableRTL:!1,allowKeyboardNavigation:!0,tip:{size:{width:20,height:10},adjust:{xValue:0,yValue:0}},trigger:"hover",create:null,click:null,destroy:null,hover:null,tracking:null,beforeOpen:null,beforeClose:null,open:null,close:null},this.dataTypes={enabled:"boolean",closeMode:"enum",autoCloseTimeout:"number",trigger:"enum",position:{stem:"data",target:"data"},associate:"enum",collision:"enum",showShadow:"boolean",animation:{effect:"enum",speed:"number"},isBalloon:"boolean",showRoundedCorner:"boolean",enableRTL:"boolean",allowKeyboardNavigation:"boolean",tip:{size:{width:"number",height:"number"},adjust:{xValue:"number",yValue:"number"}}},this.isTrack=!0,this._isCancel=!1,this._isHidden=!0,this.arrowValue={left:0,top:0,width:0,height:0,display:null},this.tooltipPos={width:0,height:0,left:0,top:0,bottom:0,right:0,position:"absolute"},this.targetPos={width:0,height:0,left:0,top:0,bottom:0,right:0,position:"absolute"},this.mouseTimer=null,this.positionTarget=null,this.positionTooltip=null,this.containerSize=null,this._createTitle=function(){this.tooltipTitle=ej.buildTag("div.e-def e-header"," ",{},{});this.tooltipHeader=ej.buildTag("div"," ",{},{});t(this.tooltipTitle).html(this.model.title).appendTo(this.tooltipHeader)},n&&(n.jquery||(n=t("#"+n)),n.length)?t(n).ejTooltip(r).data(this.PluginName):void 0}return n(r,i),r.prototype.setModel=function(n,t){this.setModel(n,t)},r.prototype.option=function(n,t){this.option(n,t)},r.prototype.triggerEvents=function(n,t){var i,r,u,f,e,o,s;switch(n){case"click":r=t;i=this._trigger(n,r);break;case"hover":u=t;i=this._trigger(n,u);break;case"tracking":f=t;i=this._trigger(n,f);break;case"beforeOpen":e=t;i=this._trigger(n,e);break;case"open":o=t;i=this._trigger(n,o);break;case"beforeClose":case"close":s=t;i=this._trigger(n,s)}return i},r.prototype.enable=function(){this.tooltip[0].classList.contains("e-disable")&&(this.model.enabled=!0,this.tooltip[0].classList.remove("e-disable"))},r.prototype.disable=function(){this.tooltip[0].classList.contains("e-disable")||(this.model.enabled=!1,this.tooltip[0].classList.add("e-disable"))},r.prototype.show=function(n,i){if(this.model.enabled){if(ej.isNullOrUndefined(n)){var r=this.model.target==null?this.element:t(this.element).find(this.model.target+":first");this._positionElement(r)}else this._positionElement(n);ej.isNullOrUndefined(i)?this._showTooltip():typeof i=="string"?t(this.tooltip).show(i):typeof i=="function"&&i.call.apply(this.tooltip)}},r.prototype.hide=function(n){this.model.enabled&&(ej.isNullOrUndefined(n)?this._hideTooltip():typeof n=="string"?t(this.tooltip).hide(n):typeof n=="function"&&n.call.apply(this.tooltip))},r.prototype._destroy=function(){this.mouseTimer&&clearTimeout(this.mouseTimer);this.timer&&clearTimeout(this.timer);t(this.tooltip).remove();this.tooltip=null},r.prototype._setModel=function(n){var i,r;for(i in n)switch(i){case"height":this._setHeight(n[i]);break;case"width":this._setWidth(n[i]);break;case"enabled":this._enabled(n[i]);break;case"content":this._setContent(n[i]);break;case"title":this.model.title=n[i];this.model.title==null?(t(this.tooltipHeader).remove(),this.tooltipHeader=null,this.tooltipTitle=null,this.model.closeMode===ej.Tooltip.CloseMode.Sticky&&this._iconRender()):ej.isNullOrUndefined(this.tooltipHeader)?this._createHeader():t(this.tooltipTitle).html(this.model.title);this.tooltipPos.height=t(this.tooltip).outerHeight();break;case"associate":this.model.associate=n[i];this._wireMouseEvents(!1);this._wireMouseEvents(!0);break;case"position":this._setPosition(n[i]);break;case"collision":this.model.collision=n[i];break;case"closeMode":typeof n[i]!="undefined"&&(this.model.closeMode=n[i],this.model.closeMode==ej.Tooltip.CloseMode.Sticky?this._iconRender():(t(this.tooltipClose).remove(),this.tooltipClose=null),this.tooltipPos.height=t(this.tooltip).outerHeight());break;case"cssClass":this._setSkin(n[i]);break;case"showShadow":this._shadowEffect(n[i],this.model.position);break;case"isBalloon":ej.isNullOrUndefined(n[i])||(this.model.isBalloon=n[i],this.model.isBalloon?this._renderArrow():(t(this.tip).remove(),this.tip=null));break;case"animation":r=n[i];this.model.animation=t.extend(!0,this.model.animation,r);this.model.animation.effect!=ej.Tooltip.Effect.None?(this._off(t(this.tooltip),"mouseenter",this._onTooltipMouseEnter),this._off(t(this.tooltip),"mouseleave",this._onTooltipMouseLeave)):this.model.animation.effect==ej.Tooltip.Effect.None&&(this._on(t(this.tooltip),"mouseenter",this._onTooltipMouseEnter),this._on(t(this.tooltip),"mouseleave",this._onTooltipMouseLeave));break;case"enableRTL":this._setRTL(n[i]);break;case"target":this._wireTriggerEvents(!1);this.model.target=n[i];this._wireTriggerEvents(!0);this._renderTarget();break;case"trigger":this._setTrigger(n[i]);break;case"showRoundedCorner":this.model.showRoundedCorner=n[i];this._roundedCorner(n[i]);break;case"allowKeyboardNavigation":this.model.allowKeyboardNavigation=n[i];this.model.allowKeyboardNavigation?this._on(t(window),"keydown",this._keyDown):this._off(t(window),"keydown",this._keyDown)}},r.prototype._enabled=function(n){n?this.enable(n):this.disable(n);this.model.enabled=n},r.prototype._shadowEffect=function(n,i){this.model.showShadow=n;var r=null;if(t(this.tooltip).removeClass("e-tooltipShadowLeft e-tooltipShadowRight"),this.model.showShadow){if(this.model.isBalloon)switch(i.stem.horizontal){case"center":r=i.stem.vertical=="top"?"e-tooltipShadowLeft":i.stem.vertical=="bottom"?"e-tooltipShadowRight":"e-tooltipShadowLeft";break;case"right":r=i.target.horizontal=="center"&&i.stem.vertical=="top"?"e-tooltipShadowLeft":"e-tooltipShadowRight";break;case"left":r=i.target.horizontal=="center"&&i.stem.vertical=="bottom"?"e-tooltipShadowRight":"e-tooltipShadowLeft"}else r="e-tooltipShadowLeft";t(this.tooltip).addClass(r)}},r.prototype._setContent=function(n){this.model.content=n;t(this.tooltipContent).html(this.model.content);this.tooltipPos.height=t(this.tooltip).outerHeight();this.tooltipPos.width=t(this.tooltip).outerWidth()},r.prototype._setPosition=function(n){this.model.position.stem=t.extend(!0,this.model.position.stem,n.stem);this.model.position.target=t.extend(!0,this.model.position.target,n.target)},r.prototype._setTrigger=function(n){this._wireTriggerEvents(!1);this.model.trigger=n;this._wireTriggerEvents(!0)},r.prototype._init=function(){this.id=this.element[0].id;this.positionTarget=t.extend(!0,{},this.model.position.target);this.positionTooltip=t.extend(!0,{},this.model.position.stem);this.tipSize=t.extend(!0,{},this.model.tip.size);this._initialize();this._render();this.enable(this.model.enabled);this._wireEvents(!0)},r.prototype._initialize=function(){ej.isNullOrUndefined(this.model.target)?ej.isNullOrUndefined(this.model.content)&&!ej.isNullOrUndefined(this.element[0].getAttribute("tittle"))&&(this.model.content=this.element[0].getAttribute("tittle"),this.element[0].setAttribute("data-content",this.model.content),this.element[0].removeAttribute("title")):this._renderTarget()},r.prototype._wireEvents=function(n){var i=n?"_on":"_off";this._wireTriggerEvents(n);this.model.allowKeyboardNavigation&&this[i](t(window),"keydown",this._keyDown);this.model.target!=null?this[i](this.element,"scroll",this.model.target,this._hideTooltip):this[i](this.element,"scroll",this._hideTooltip);this[i](t(this.tooltip),"mouseenter",this._onTooltipMouseEnter);this[i](t(this.tooltip),"mouseleave",this._onTooltipMouseLeave);this[i](t(window),"resize",this._hideTooltip);this[i](t(window),"touchend",this._docTouchEndHandler)},r.prototype._wireTriggerEvents=function(n){var t=n?"_on":"_off",i;this.model.trigger==ej.Tooltip.Trigger.Focus?this.model.target!=null?this[t](this.element,"blur",this.model.target,this._hideTooltip):this[t](this.element,"blur",this._hideTooltip):this.model.target!=null?this[t](this.element,ej.isDevice()?"touchstart":"mouseleave",this.model.target,this._onMouseOut):this[t](this.element,ej.isDevice()?"touchstart":"mouseleave",this._onMouseOut);this._wireMouseEvents(n);i=this.model.trigger==ej.Tooltip.Trigger.Click?ej.isDevice()?"touchstart":"click":this.model.trigger==ej.Tooltip.Trigger.Focus?ej.isDevice()?"touchstart":"focus":ej.isDevice()?"touchstart":"mouseenter";this.model.target!=null?this[t](this.element,i,this.model.target,this._targetHover):this[t](this.element,i,this._targetHover)},r.prototype._wireMouseEvents=function(n){var t=n?"_on":"_off";(this.model.associate==ej.Tooltip.Associate.MouseEnter||this.model.associate==ej.Tooltip.Associate.MouseFollow)&&(this.model.target!=null?this[t](this.element,ej.isDevice()?"touchstart":"mousemove",this.model.target,this._tooltipMove):this[t](this.element,ej.isDevice()?"touchstart":"mousemove",this._tooltipMove))},r.prototype._render=function(){this.tooltip=ej.buildTag("div.e-tooltip-wrap e-widget","",{},{role:"tooltip","aria-readonly":"true","aria-hidden":"true","aria-describedby":this.id+"_content",id:this.id+"_Main"});this.tooltipInter=ej.buildTag("div.e-tipContainer","",{},{});this.tooltip.append(this.tooltipInter);this.model.isBalloon&&this._renderArrow();t(this.model.containment).find("#"+t(this.tooltip).attr("id")).length>0&&t("#"+t(this.tooltip).attr("id")).remove();t(this.model.containment).append(this.tooltip);this._setHeight(this.model.height);this._setWidth(this.model.width);this._createHeader();this._tooltipContent();this.model.cssClass&&this.tooltip.addClass(this.model.cssClass);this.model.showRoundedCorner&&this._roundedCorner(this.model.showRoundedCorner);this.model.enableRTL&&this._setRTL(this.model.enableRTL);this.tooltip[0].style.top="auto";this.tooltip[0].style.left="auto";var n=this.tooltip[0].getBoundingClientRect();this.tooltipPos={width:n.width,height:n.height,left:n.left,top:n.top,position:"absolute"};ej.isNullOrUndefined(this.model.target)&&(this._containerCalc(this.element),this._positionElement(this.element))},r.prototype._containerCalc=function(n){ej.isNullOrUndefined(n)&&(n=this.element);var i=t(n).offset();this.containerSize={height:this.model.containment=="body"?t(window).innerHeight()||document.documentElement.clientHeight||document.body.clientHeight:t(this.model.containment).innerHeight(),width:this.model.containment=="body"?t(window).innerWidth()||document.documentElement.clientWidth||document.body.clientWidth:t(this.model.containment).innerWidth()};this.containerSize.left=this.model.containment!="body"?t(this.model.containment).css("position")=="static"?t(this.model.containment).offset().left-t(this.model.containment).offsetParent().offset().left:0:0;this.containerSize.top=this.model.containment!="body"?t(this.model.containment).css("position")=="static"?t(this.model.containment).offset().top-t(this.model.containment).offsetParent().offset().top:0:0;i.left-=this.model.containment!="body"?t(this.model.containment).css("position")=="static"?t(this.model.containment).offsetParent().offset().left:t(this.model.containment).offset().left:0;i.top-=this.model.containment!="body"?t(this.model.containment).css("position")=="static"?t(this.model.containment).offsetParent().offset().top:t(this.model.containment).offset().top:0;this.targetPos.left=i.left;this.targetPos.top=i.top},r.prototype._setHeight=function(n){this.model.height=n;this.tooltip[0].style.height=!isNaN(+n)&&isFinite(n)?n+"px":n;this.tooltip[0].style.display="block";this.tooltipPos.height=this.tooltip[0].offsetHeight;this.tooltip[0].style.display="none"},r.prototype._setWidth=function(n){this.model.width=n;this.model.width!="auto"&&(n=!isNaN(+n)&&isFinite(n)?n+"px":n,this.tooltip[0].style.maxWidth=n,this.tooltip[0].style.minWidth="0px");this.tooltip[0].style.width=n;this.tooltip[0].style.display="block";this.tooltipPos.height=this.tooltip[0].offsetHeight;this.tooltip[0].style.display="none"},r.prototype._setRTL=function(n){this.model.enableRTL=n;n?this.tooltip[0].classList.add("e-rtl"):this.tooltip[0].classList.remove("e-rtl")},r.prototype._setSkin=function(n){this.model.cssClass!=n&&(this.tooltip.removeClass(this.model.cssClass).addClass(n),this.model.cssClass=n)},r.prototype._roundedCorner=function(){this.model.showRoundedCorner?this.tooltip[0].classList.add("e-corner"):this.tooltip[0].classList.remove("e-corner")},r.prototype._renderArrow=function(){var n,i;ej.isNullOrUndefined(this.tip)&&(this.tip=ej.buildTag("div.e-arrowTip"," ",{id:this.id+"_eTip"},{}),n=document.createElement("div"),n.classList.add("e-arrowTipOuter"),i=document.createElement("div"),i.classList.add("e-arrowTipInner"),t(this.tip).append(n).append(i),t(this.tip).insertBefore(this.tooltipInter))},r.prototype._adjustArrow=function(n){var u,f,e=t(this.tooltip).width(),r=t(this.tooltip).height(),i=n.stem,s=n.target,o={tipHeight:0,tipWidth:0};s.horizontal=="right"||s.horizontal=="left"?(u=i.horizontal=="left"?-this.model.tip.size.height:i.horizontal=="right"?e:e/2-this.tipSize.width/2,f=i.horizontal!="center"?i.vertical=="top"?5:i.vertical=="center"?r/2-this.tipSize.width/2:r-5-this.tipSize.width:i.vertical=="top"?-this.tipSize.height:i.vertical=="bottom"?r:this.tooltipPos.height/2-this.tipSize.width/2,o=this._arrowBinding(n,"horizontal")):(f=i.vertical=="top"?-this.tipSize.height:i.vertical=="bottom"?r:r/2-this.tipSize.width/2,u=i.vertical=="center"?i.horizontal=="left"?-this.model.tip.size.height:e:i.horizontal=="left"?10:i.horizontal=="center"?this.tooltipPos.width/2-this.tipSize.width/2:e-10-this.tipSize.width,o=this._arrowBinding(n,"vertical"));this.arrowValue.left=u;this.arrowValue.top=f;t(this.tip).css({height:o.tipHeight+"px",width:o.tipWidth+"px",left:u+"px",top:f+"px",display:i.horizontal=="center"&&i.vertical=="center"?"none":"block"})},r.prototype._arrowBinding=function(n,i){var e=n.stem,l=i=="horizontal"?e.horizontal!="center":e.vertical=="center",u,f,o=t(this.tooltip).css("border-top-color"),s=t(this.tooltip).css("background-color"),h=this.model.tip.size.height,c=this.model.tip.size.height-1,r={"border-top":"none","border-bottom":"none","border-right":"none","border-left":"none"};return l?(u=this.model.tip.size.height,f=this.model.tip.size.width,t(this.tip).find(".e-arrowTipOuter").css(this._arrow(n,h,o,r,"horizontal")),t(this.tip).find(".e-arrowTipInner").css(this._arrow(n,c,s,r,"horizontal"))):(u=this.model.tip.size.width,f=this.model.tip.size.height,t(this.tip).find(".e-arrowTipOuter").css(this._arrow(n,h,o,r,"vertical")),t(this.tip).find(".e-arrowTipInner").css(this._arrow(n,c,s,r,"vertical"))),{tipHeight:f,tipWidth:u}},r.prototype._arrow=function(n,t,i,r,u){var f=n.stem,e=u=="horizontal"?f.horizontal=="right"?"0px":"1px":"1px",o=u=="horizontal"?"1px":f.vertical=="bottom"?"0px":"1px";return r["border-top"]=u=="horizontal"?t+"px solid transparent":f.vertical=="bottom"?t+"px solid "+i:"none",r["border-bottom"]=u=="horizontal"?t+"px solid transparent":f.vertical=="top"?t+"px solid "+i:"none",r["border-right"]=u=="horizontal"?f.horizontal=="left"?t+"px solid "+i:"none":t+"px solid transparent",r["border-left"]=u=="horizontal"?f.horizontal=="right"?t+"px solid "+i:"none":t+"px solid transparent",t==this.model.tip.size.height-1&&(r.left=e,r.top=o),r},r.prototype._iconRender=function(){this.model.closeMode==ej.Tooltip.CloseMode.Sticky&&(ej.isNullOrUndefined(this.tooltipClose)||t(this.tooltipClose).remove(),this.tooltipClose=ej.buildTag("div .e-icon"," ",{},{id:"_closeIcon"}),this.model.title!=null?t(this.tooltipClose).insertAfter(this.tooltipTitle).addClass("e-close"):t(this.tooltipClose).insertBefore(this.tooltipInter).addClass("e-cross-circle"),this._on(t(this.tooltipClose),"click",this._hideTooltip))},r.prototype._renderTarget=function(){this.targetElement=this.element.find(this.model.target);for(var n=0;n<this.targetElement.length;n++)ej.isNullOrUndefined(this.targetElement[n].getAttribute("title"))||(this.targetElement[n].setAttribute("data-content",this.targetElement[n].title),this.targetElement[n].removeAttribute("title"))},r.prototype._tooltipContent=function(){this.tooltipContent=ej.buildTag("div","",{},{id:this.id+"_content","class":"e-tipcontent e-def"});this.tooltipContent[0].innerHTML=this.model.content;this.model.title!=null?t(this.tooltipContent).insertAfter(this.tooltipHeader):this.tooltipInter[0].appendChild(this.tooltipContent[0])},r.prototype._positionElement=function(n){this.tooltipPos.width=t(this.tooltip).outerWidth();this.tooltipPos.height=t(this.tooltip).outerHeight();this.targetPos.width=t(n).outerWidth();this.targetPos.height=t(n).outerHeight();this._containerCalc(n);this.model.associate==ej.Tooltip.Associate.Window?this._browserPosition():this.model.associate==ej.Tooltip.Associate.Axis?this._axisPosition():this.model.associate==ej.Tooltip.Associate.Target&&this._tooltipPosition(this.model.position);this.model.collision!=ej.Tooltip.Collision.None&&this.model.associate==ej.Tooltip.Associate.Target&&this._calcCollision(this.model.position,n)},r.prototype._browserPosition=function(){if(this.model.containment=="body"){this.containerSize={height:t(window).innerHeight()||document.documentElement.clientHeight||document.body.clientHeight,width:t(window).innerWidth()||document.documentElement.clientWidth||document.body.clientWidth};var i=t.extend(!0,{},this.model.position),n={position:"absolute",left:"auto",top:"auto",bottom:"auto",right:"auto"};ej.isNullOrUndefined(this.tip)||t(this.tip).css({display:"none"});this.model.position.target.horizontal=="right"?n.right=0:n.left=this.model.position.target.horizontal=="left"?0:this.containerSize.width/2-this.tooltipPos.width/2;this.model.position.target.vertical=="top"?n.top=0:this.model.position.target.vertical=="center"?n.top=this.containerSize.height/2-this.tooltipPos.height/2:n.bottom=0;this.model.showShadow&&this._shadowEffect(this.model.showShadow,i);t(this.tooltip).css(n)}},r.prototype._tooltipMove=function(n){if(this.model.closeMode==ej.Tooltip.CloseMode.None&&this.model.enabled){var t=this;if(this._isCancel)return;this.model.associate==ej.Tooltip.Associate.MouseFollow?this._mousePosition(n):this.model.associate==ej.Tooltip.Associate.MouseEnter&&(clearTimeout(this.mouseTimer),this.mouseTimer=setTimeout(function(){t.isTrack&&t._mousePosition(n)},300))}},r.prototype._mousePosition=function(n){var u,f,b,c;n.type=="touchstart"?(n.preventDefault(),u=n.touches[0].pageX,f=n.touches[0].pageY):n.type=="mousemove"&&(u=n.pageX,f=n.pageY);this.isCollision=!0;this._containerCalc(n.currentTarget);var l=0,a=0,e=0,i=t.extend(!0,{},this.model.position.stem),o=t.extend(!0,{},this.model.position),s={left:0,top:0},r={left:u,top:f},h={left:u,top:f},v=this.containerSize.left,y=this.containerSize.top,p=this.model.containment!="body"?u-t(this.model.containment).offset().left:u,w=this.model.containment!="body"?f-t(this.model.containment).offset().top:f;for(o.target.horizontal=o.target.vertical="center",this.model.containment!="body"&&(s=t(this.model.containment).css("position")=="static"?t(this.model.containment).offsetParent().offset():t(this.model.containment).offset()),h.left-=s.left,h.top-=s.top;this.isCollision;)if(r=t.extend(!0,{},h),b=this.model.isBalloon?i.vertical=="top"||i.vertical=="bottom"?5+this.tipSize.height/2:0:0,c=this.model.isBalloon?i.horizontal=="right"||i.horizontal=="left"?10+this.tipSize.width/2:0:0,l=this.model.tip.adjust.xValue!=0?this.model.tip.adjust.xValue:7,a=this.model.tip.adjust.yValue!=0?this.model.tip.adjust.yValue:10,e=this.model.isBalloon?i.horizontal!="center"?this.model.tip.size.height:i.vertical!="center"?this.model.tip.size.height:0:2,r.left+=i.horizontal=="right"?-this.tooltipPos.width:i.horizontal=="left"?0:-(this.tooltipPos.width/2),r.top+=i.vertical=="bottom"?-this.tooltipPos.height:i.vertical=="top"?0:-(this.tooltipPos.height/2),r.left+=i.vertical!="center"?i.horizontal=="right"?c:i.horizontal=="left"?-c:0:0,r.left+=i.vertical=="center"?i.horizontal=="right"?-e:i.horizontal=="left"?+(e+l):0:0,r.top+=i.vertical=="top"?+(e+a):i.vertical=="bottom"?-e:0,this.targetElement!=n.currentTarget&&(this.targetElement=n.currentTarget,(r.left<v||r.left+this.tooltipPos.width>v+this.containerSize.width)&&(this.positionTooltip.horizontal=p>=this.tooltipPos.width?"right":this.containerSize.width-p>=this.tooltipPos.width?"left":"center"),(r.top<y||r.top+this.tooltipPos.height>this.containerSize.height+y)&&(this.positionTooltip.vertical=w>=this.tooltipPos.height?"bottom":this.containerSize.height-w>=this.tooltipPos.height?"top":"center")),this.positionTooltip.horizontal!=i.horizontal||this.positionTooltip.vertical!=i.vertical)this.isCollision=!0,i=t.extend(!0,{},this.positionTooltip);else if(this.isCollision=!1,t(this.tooltip).css({top:r.top+"px",left:r.left+"px",position:"absolute",right:"auto",bottom:"auto"}),o.stem=t.extend(!0,{},this.positionTooltip),this.model.showShadow&&this._shadowEffect(this.model.showShadow,o),this.model.isBalloon&&this._adjustArrow(o),this._showTooltip(),this.model.associate==ej.Tooltip.Associate.MouseEnter&&(this.isTrack=!1),this.model.associate==ej.Tooltip.Associate.MouseFollow&&this.triggerEvents("tracking",{position:this.model.position,event:n}))return},r.prototype._axisPosition=function(){var r=t.extend(!0,{},this.model.position),u,f,n,i;typeof this.model.position.target.horizontal=="number"&&(u=this.model.position.target.horizontal.toString());typeof this.model.position.target.vertical=="number"&&(f=this.model.position.target.vertical.toString());n=parseInt(u);i=parseInt(f);ej.isNullOrUndefined(this.tip)||t(this.tip).css({display:"none"});this.model.showShadow&&this._shadowEffect(this.model.showShadow,r);this.model.isBalloon&&this._adjustArrow(r);isFinite(n)&&isFinite(i)&&t(this.tooltip).css({top:i,left:n,position:"absolute"})},r.prototype._tooltipPosition=function(n){var f=0,i=t.extend(!0,{},n.stem),u=t.extend(!0,{},n.target),r=t.extend(!0,{},this.targetPos),s=i.vertical==="top"||i.vertical==="bottom"?5+this.tipSize.width/2:0,h=i.horizontal==="right"||i.horizontal==="left"?10+this.tipSize.width/2:0,f=this.model.isBalloon?i.horizontal!=="center"?this.model.tip.size.height:i.vertical!=="center"?this.model.tip.size.height:0:0,e=this.model.tip.adjust.xValue!=0?this.model.tip.adjust.xValue:this.model.isBalloon?0:2,o=this.model.tip.adjust.yValue!=0?this.model.tip.adjust.yValue:this.model.isBalloon?0:2;r.left+=u.horizontal==="right"?this.targetPos.width:u.horizontal==="left"?0:this.targetPos.width/2;r.top+=u.vertical==="bottom"?this.targetPos.height:u.vertical==="top"?0:this.targetPos.height/2;r.left+=i.horizontal==="right"?-this.tooltipPos.width:i.horizontal==="left"?0:-(this.tooltipPos.width/2);r.top+=i.vertical==="bottom"?-this.tooltipPos.height:i.vertical==="top"?0:-(this.tooltipPos.height/2);r.left+=u.horizontal!=="center"?i.horizontal==="right"?-f:i.horizontal==="left"?f:0:i.vertical==="center"?i.horizontal==="right"?-f:i.horizontal==="left"?f:0:0;r.top+=u.horizontal==="center"?i.vertical==="bottom"?-f:i.vertical==="top"?f:0:i.horizontal==="center"?i.vertical==="bottom"?-f:i.vertical==="top"?f:0:0;r.left+=u.horizontal==="center"&&i.vertical!=="center"?i.horizontal==="right"?h:i.horizontal==="left"?-h:0:0;r.top+=u.horizontal!=="center"&&i.horizontal!=="center"?i.vertical==="top"?-s:i.vertical==="bottom"?s:0:0;r.left+=u.horizontal!=="center"?i.horizontal==="right"?-e:i.horizontal==="left"?e:0:i.vertical==="center"?i.horizontal==="right"?-e:i.horizontal==="left"?e:0:0;r.top+=u.horizontal==="center"?i.vertical==="bottom"?-o:i.vertical==="top"?o:0:i.horizontal==="center"?i.vertical==="bottom"?-o:i.vertical==="top"?o:0:0;this.tooltipPos.left=r.left;this.tooltipPos.top=r.top;this.model.collision===ej.Tooltip.Collision.None&&(this.model.isBalloon&&this._adjustArrow(n),this._shadowEffect(this.model.showShadow,n),t(this.tooltip).css({top:r.top+"px",left:r.left+"px",position:"absolute"}))},r.prototype._calcCollision=function(n,i){var n=t.extend(!0,{},n),r=t.extend(!0,{},n),s=this.model.tip.size.height,e=!0,u=this.model.containment!="body"?t(i).offset().left-t(this.model.containment).offset().left:t(i).offset().left,f=this.model.containment!="body"?t(i).offset().top-t(this.model.containment).offset().top:t(i).offset().top,o={topSpace:f,rightSpace:this.containerSize.width-(u+this.targetPos.width),bottomSpace:this.containerSize.height-(f+this.targetPos.height),leftSpace:u,centerRight:this.containerSize.width-(u+this.targetPos.width/2),centerLeft:u+this.targetPos.width/2,centerTop:f+this.targetPos.height/2,centerBottom:this.containerSize.height-(f+this.targetPos.height/2),tooltipWidth:this.tooltipPos.width+s,tooltipHeight:this.tooltipPos.height+s};if(this.model.collision===ej.Tooltip.Collision.Fit)this._collisionFit(n,o);else{while(e)r=this._collisionFlip(r,o),r.target.horizontal!=n.target.horizontal||r.target.vertical!=n.target.vertical||r.stem.horizontal!=n.stem.horizontal||r.stem.vertical!=n.stem.vertical?(this._tooltipPosition(r),n=t.extend(!0,{},r)):e=!1;e||(this.model.collision==ej.Tooltip.Collision.FlipFit?this._collisionFit(r,o):(this._adjustArrow(r),this._shadowEffect(this.model.showShadow,r),t(this.tooltip).css({top:this.tooltipPos.top+"px",left:this.tooltipPos.left+"px",position:"absolute"})))}},r.prototype._collisionFlip=function(n,i){var u=t.extend(!0,{},this.tooltipPos),r=t.extend(!0,{},n),o=t(this.model.containment).scrollLeft(),s=t(this.model.containment).scrollTop(),f=this.containerSize.left,e=this.containerSize.top;return(u.left+u.width>f+this.containerSize.width+o||u.left<f)&&(n.target.horizontal!="center"?r.target.horizontal=i.leftSpace>=i.tooltipWidth?"left":i.rightSpace>=i.tooltipWidth?"right":"center":r.stem.horizontal=i.centerLeft>=i.tooltipWidth?"right":i.centerRight>=i.tooltipWidth?"left":"center"),u.top<e&&(r.target.vertical=i.bottomSpace>=i.tooltipHeight?"bottom":"center"),u.top+u.height>this.containerSize.height+s+e&&(r.target.vertical=i.topSpace>=i.tooltipHeight?"top":"center"),(r.target.horizontal!=n.target.horizontal||r.target.vertical!=n.target.vertical)&&(r.stem.horizontal=r.target.horizontal=="center"?i.centerLeft>=i.tooltipWidth?"right":i.centerRight>=i.tooltipWidth?"left":"center":r.target.horizontal=="right"?"left":"right"),(r.target.vertical!=n.target.vertical||r.target.horizontal!=n.target.horizontal)&&(r.stem.vertical=r.target.vertical=="center"?i.centerTop>=i.tooltipHeight?"bottom":i.centerBottom>=i.tooltipHeight?"top":i.centerTop>i.centerBottom?"bottom":"top":r.target.vertical=="top"?"bottom":"top"),r},r.prototype._collisionFit=function(n,i){var r=t.extend(!0,{},this.tooltipPos),s=!1,h=!1,e=1,o=1,l=null,a=t(this.model.containment).scrollLeft(),c,u,f,v,y;c=this.model.containment=="body"?document.documentElement.scrollTop||document.body.scrollTop:t(this.model.containment).scrollTop();u=this.containerSize.left;f=this.containerSize.top;(r.left<u||r.left+r.width>this.containerSize.width+a+u)&&(e=r.left<u?u:r.left+r.width>this.containerSize.width+a+u?r.left-(r.left+r.width-(this.containerSize.width+u)):1,s=!0);(r.top<f||r.top+r.height>this.containerSize.height+c+f)&&(o=r.top<f?f:r.top+r.height>this.containerSize.height+c+f?r.top-(r.top+r.height-(this.containerSize.height+f)):1,h=!0);t(this.tooltip).css({top:o!=1?o+"px":r.top+"px",left:e!=1?e+"px":r.left+"px",position:"absolute"});this._adjustArrow(n);l={left:this.arrowValue.left,top:this.arrowValue.top,height:this.model.tip.size.height,width:this.model.tip.size.width,display:t(this.tip).css("display")};this.tooltipPos.top=o=o!=1?o:r.top;this.tooltipPos.left=e=e!=1?e:r.left;v=t(this.tooltip).width();y=t(this.tooltip).height();(s||h&&l.display!="none")&&(this.model.isBalloon&&(s&&(this.arrowValue.left=this._horizontalAdjustment(n,i)),h&&(this.arrowValue.top=this._verticalAdjustment(n,i))),this.arrowValue.left==-this.model.tip.size.height||this.arrowValue.left==v?t(this.tip).css({left:this.arrowValue.left+"px",top:this.arrowValue.top+"px",display:"block"}):this.arrowValue.top==-this.model.tip.size.height||this.arrowValue.top==y?t(this.tip).css({left:this.arrowValue.left+"px",top:this.arrowValue.top+"px",display:"block"}):t(this.tip).css({left:this.arrowValue.left+"px",top:this.arrowValue.top+"px",display:"none"}));this._shadowEffect(this.model.showShadow,n)},r.prototype._horizontalAdjustment=function(n,i){var u={left:this.arrowValue.left,top:this.arrowValue.top},f,r;return t(this.tooltip).css({display:"block"}),f=n.target.horizontal!="center"?this.model.tip.size.height:this.model.tip.size.height,r=n.target.horizontal!="center"&&n.stem.horizontal=="left"?t(this.tip).offset().left:n.target.horizontal!="center"&&n.stem.horizontal=="right"?t(this.tip).offset().left+f:t(this.tip).offset().left,t(this.tooltip).css({display:"none"}),r>i.leftSpace&&r+f<i.leftSpace+this.targetPos.width?u.left:((r>i.leftSpace+this.targetPos.width||r<i.leftSpace)&&(u.left=i.leftSpace+this.targetPos.width/2-parseInt(this.tooltipPos.left.toString())),u.left)},r.prototype._verticalAdjustment=function(n,i){var u={left:this.arrowValue.left,top:this.arrowValue.top,height:this.model.tip.size.height,width:this.model.tip.size.width,display:this.arrowValue.display},f,r;return t(this.tooltip).css({display:"block"}),f=n.target.horizontal!="center"?this.model.tip.size.height:this.model.tip.size.height,r=n.target.horizontal=="center"&&n.stem.vertical=="top"?t(this.tip).offset().top:t(this.tip).offset().top+f,t(this.tooltip).css({display:"none"}),r>i.topSpace&&r<i.topSpace+this.targetPos.height?u.top:((r<i.topSpace||r+f>i.topSpace+this.targetPos.height)&&(u.top=i.topSpace+this.targetPos.height/2-parseInt(this.tooltipPos.top.toString())),u.top)},r.prototype._createHeader=function(){this.model.title!=null&&(ej.isNullOrUndefined(this.tooltipTitle)&&this._createTitle(),ej.isNullOrUndefined(this.tooltipContent)?t(this.tooltipHeader).appendTo(this.tooltipInter).addClass("e-tooltipHeader"):t(this.tooltipHeader).insertBefore(this.tooltipContent).addClass("e-tooltipHeader"));this.model.closeMode==ej.Tooltip.CloseMode.Sticky&&this._iconRender()},r.prototype._hideTooltip=function(){var n;if(this._isHidden=!0,n=this.model.animation.speed!=0?this.model.animation.speed:this.model.animation.effect==ej.Tooltip.Effect.Slide?200:this.model.animation.effect==ej.Tooltip.Effect.Fade?800:0,this.model.enabled==!0&&t(this.tooltip).css("display")=="block"){if(this.triggerEvents("beforeClose",{}))return;if(this.model.animation.effect==ej.Tooltip.Effect.Fade?t(this.tooltip).fadeOut(n):this.model.animation.effect==ej.Tooltip.Effect.Slide?t(this.tooltip).slideUp(n):t(this.tooltip).css({display:"none"}),t(this.tooltip).css("display")=="none"&&t(this.tooltip).attr("aria-hidden","true").removeClass("e-customAnimation"),this.triggerEvents("close",{}))return}},r.prototype._showTooltip=function(){var r,i,u,f,n,e;this._isHidden&&(r=this.model.animation.speed!=0?this.model.animation.speed:this.model.animation.effect==ej.Tooltip.Effect.Slide?200:this.model.animation.effect==ej.Tooltip.Effect.Fade?800:0,this._isHidden=!1,t(this.tooltip).css("display")=="none"&&this.model.enabled==!0&&(this.model.animation.effect==ej.Tooltip.Effect.Fade?t(this.tooltip).fadeIn(r):this.model.animation.effect==ej.Tooltip.Effect.Slide?t(this.tooltip).slideDown(r):t(this.tooltip).css({display:"block"}),t(this.tooltip).css("display")=="block"&&(i=t(this.tooltip[0]).parents(),u=t("body").children(),t(u).each(function(n,t){i.push(t)}),f=t(this.model.target).parents(),t(f).each(function(n,t){i.push(t)}),n=Math.max.apply(n,t.map(i,function(n){if(t(n).css("position")!="static")return parseInt(t(n).css("z-index"))||1})),!n||n<1e4?n=1e4:n+=1,t(this.tooltip).attr("aria-hidden","false").css({zIndex:n}),this.model.animation.effect==ej.Tooltip.Effect.None&&t(this.tooltip).addClass("e-customAnimation"),e=this._getScrollableParents(),this._on(e,"scroll",this._hideTooltip)),this.triggerEvents("open",{})))},r.prototype._getScrollableParents=function(){return t(this.element).parentsUntil("html").filter(function(){return t(this).css("overflow")!="visible"}).add(t(window))},r.prototype._tooltipAuto=function(){var n=this;this.timer=setTimeout(function(){n._hideTooltip()},n.model.autoCloseTimeout)},r.prototype._beforeOpenTooltip=function(n){this.positionTooltip=t.extend(!0,{},this.model.position.stem);this.positionTarget=t.extend(!0,{},this.model.position.target);this.targetElement=this.element;ej.isNullOrUndefined(this.model.target)||ej.isNullOrUndefined(t(n.currentTarget).attr("data-content"))||(this.model.content=t(n.currentTarget).attr("data-content"),this._setContent(this.model.content))},r.prototype._targetHover=function(n){if(this.isTrack=!0,this.model.enabled)if(this._isHidden||(ej.browserInfo().name=="msie"||ej.browserInfo().name=="edge")&&this.model.associate==ej.Tooltip.Associate.MouseFollow){if(this.tooltip.stop(!0,!0),this.triggerEvents("beforeOpen",{event:n})){this._isCancel=!0;return}this._isCancel=!1;this._beforeOpenTooltip(n);this.model.associate!=ej.Tooltip.Associate.MouseEnter&&this.model.associate!=ej.Tooltip.Associate.MouseFollow?(ej.isNullOrUndefined(this.model.target)?this._positionElement(this.element):this._positionElement(n.currentTarget),clearTimeout(this.timer),this._showTooltip(),this.model.closeMode==ej.Tooltip.CloseMode.Auto&&this._tooltipAuto(),n.type=="click"?this.triggerEvents("click",{event:n}):this.triggerEvents("hover",{event:n})):this.isTrack=!0}else ej.isNullOrUndefined(this.model.target)||this._positionElement(n.currentTarget)},r.prototype._onMouseOut=function(){this.model.enabled&&!this._isHidden&&(this.model.closeMode==ej.Tooltip.CloseMode.None&&this._hideTooltip(),clearTimeout(this.mouseTimer));this.isTrack=!1},r.prototype._onTooltipMouseEnter=function(){var n=this;this.model.enabled&&this.model.animation.effect==ej.Tooltip.Effect.None&&t(n.tooltip).css({display:"block"})},r.prototype._onTooltipMouseLeave=function(){var n=this;this.model.enabled&&this.model.animation.effect==ej.Tooltip.Effect.None&&n.model.closeMode==ej.Tooltip.CloseMode.None&&t(n.tooltip).css({display:"none"})},r.prototype._docTouchEndHandler=function(n){t(n.target).closest(".e-tooltip").length||this.model.closeMode!=ej.Tooltip.CloseMode.None||this._hideTooltip()},r.prototype._keyDown=function(n){var t=n.keyCode?n.keyCode:n.which?n.which:n.charCode;if(this.model.enabled)switch(t){case 27:n.preventDefault();this._hideTooltip()}},r}(ej.WidgetBase);window.ej.widget("ejTooltip","ej.Tooltip",new i);window.ejTooltip=null})(jQuery);ej.Tooltip.CloseMode={Auto:"auto",None:"none",Sticky:"sticky"};ej.Tooltip.Effect={Slide:"slide",Fade:"fade",None:"none"};ej.Tooltip.Trigger={Hover:"hover",Click:"click",Focus:"focus"};ej.Tooltip.Collision={Flip:"flip",FlipFit:"flipfit",None:"none",Fit:"fit"};ej.Tooltip.Associate={Window:"window",MouseFollow:"mousefollow",MouseEnter:"mouseenter",Target:"target",Axis:"axis"}});
