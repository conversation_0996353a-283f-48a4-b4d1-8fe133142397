/*!
*  filename: ej.unobtrusive.min.js
*  version : 18.1.0.42
*  Copyright Syncfusion Inc. 2001 - 2020. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./ej.core.min"],n):n()})(function(){(function($,ej,wd){"use strict";var options=wd.unobtrusive={dataRole:!1,ejRole:!1,directive:!1},propMaps={};String.prototype.trim=String.prototype.trim||function(){return this.replace(/^\s+|\s+$/g,"")};var div=$(document.createElement("div")),processAttrib=function(n,t,i){if(t.indexOf("-")===-1){n[t]=i;return}ej.createObject(t.replace(/-/g,"."),i,n)},processData=function(n){return n==="true"?!0:n==="false"?!1:+n+""===n?+n:n},readAttributes=function(n,t,i){for(var o=n[0].attributes,r,u={},l=i.slice(2).toLowerCase(),s="data-ej-",h=s.length,f="ej-",c=f.length,e=0;e<o.length;e++)r=o[e],t.dataRole&&r.name.startsWith(s)&&processAttrib(u,r.name.slice(h),processData(r.value)),(t.ejRole&&r.name.startsWith(f)||t.dataRole===!1&&t.ejRole===!1&&t.directive===!0&&r.name.startsWith(f))&&processAttrib(u,r.name.slice(c),processData(r.value));return u},iterateAndSetModel=function(model,data,map,type){var field="",current;for(var prop in data){if(field=map&&prop in map?map[prop]:prop,current=data[prop],type&&type[field]==="string"&&(current=current.toString()),ej.isPlainObject(current)&&(map||type)){model[field]={};iterateAndSetModel(model[field],current,map[prop+".value"]?map[prop+".value"]:map,ej.isPlainObject(type[field])?type[field]:type);continue}if(type&&(type[field]==="array"||type[field]==="parent"))try{current=JSON.parse(current)}catch(e){current=eval(current)}else type&&type[field]==="data"&&(/^\[{0,1}\{.+\}\]{0,1}$/.test(current)?current=JSON.parse(current):current.indexOf("/")===-1&&(current=ej.getObject(current.indexOf("window.")===0?current.slice(7):current,window)||current));model[field]=current}},changeTag=function(n,t){var r=$(n),u,i;if(ej.support.outerHTML)return div.insertBefore(r),u=n.tagName,i=[],i[0]="<",i[1]=n.outerHTML.trim().replace(RegExp("^<"+u+"|"+u+">$","ig"),t),i[1].endsWith(">")||(i[2]=">"),n.outerHTML=i.join(""),n=div.next()[0],div.remove(),n;var e=r.clone(),u=n.tagName,f="";return r.html(f),f+="<",f+=div.append(e).html().replace(RegExp("^<"+u+"|"+u+">$","ig"),t),f+=">",div.html(f),e=r.wrap(div.children().first()).parent(),r.remove(),div.empty(),e[0]},findAndChangeTag=function(n,t,i,r){for(var f=!1,e=n.length,u=0;u<e;u++)n[u].offsetParent?n[u]=changeTag(n[u],t):(n.splice(u--,1),f=!0,e--);return f&&n.push.apply(n,findAndChangeTag(r.find(i),t,i,r)),n},findElements=function(n,t,i,r){var u=[],f=n.replace("ej","").toLowerCase(),e={};return t.dataRole===!0&&u.push("[data-role='"+n.toLowerCase()+"']"),t.ejRole===!0&&u.push("[ej-"+f+"]"),e.role=i.find(u.join(",")),t.directive===!0&&(e.directive=findAndChangeTag(i.find(f),r.validTags&&r.validTags[0]||"div",f,i)),e},generatePropMap=function(n){var i={},r;for(var t in n)r=t.toLowerCase(),ej.isPlainObject(n[t])&&(i[r+".value"]=generatePropMap(n[t])),i[r]=t;return i},initControls=function(n,t,i,r,u){for(var e,o,s=r.dataTypes,h=n.length,f=0;f<h;f++)e=n.eq(f),o={},iterateAndSetModel(o,readAttributes(e,t,i),u,s),e[i](o)},checkUnobtrusive=function(n,t,i,r){var f=findElements(n,i,r,t),u=propMaps[n];(f.role&&f.role.length||f.directive&&f.directive.length)&&!u&&(u=generatePropMap(t.defaults),u.serverevents="serverEvents",u.clientid="clientId",u.uniqueid="uniqueId",propMaps[n]=u,t._unobtrusive&&$.extend(!0,u,t._unobtrusive));initControls(f.role,i,n,t,u);f.directive&&initControls(f.directive,i,n,t,u,!0)},readBoolAttr=function(n,t){return n.hasOwnProperty(t)?n[t]!==!1:!1};wd.init=function(n){var t,i;n=n?n.jquery?n:$(n):$(document);t=wd.registeredWidgets;for(i in t)checkUnobtrusive(t[i].name,t[i].proto,options,n)};$(function(){var n=$(document.body).data(),t,i;(options.ejRole=n.hasOwnProperty("ejrole")?readBoolAttr(n,"ejrole"):options.ejRole,options.directive=n.hasOwnProperty("directive")?readBoolAttr(n,"directive"):options.directive,options.ejRole!==!0&&options.directive!==!0&&(options.dataRole=!0),options.dataRole=n.hasOwnProperty("datarole")?readBoolAttr(n,"datarole"):options.dataRole,options.ejRole===!0||options.dataRole===!0||options.directive===!0)&&(wd.autoInit=n.hasOwnProperty("autoinit")?readBoolAttr(n,"autoinit"):!0,wd.autoInit===!0)&&(t=[],options.dataRole===!0&&t.push("[data-ej-init]"),options.ejRole===!0&&t.push("[ej-init]"),i=$(t.join(",")),i.length||(i=$(document)),wd.init(i))})})(window.jQuery,window.Syncfusion,window.Syncfusion.widget)});
