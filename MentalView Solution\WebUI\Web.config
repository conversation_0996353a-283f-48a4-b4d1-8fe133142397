﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
	<appSettings>
		<add key="LoadEJResourcesFromAssembly" value="true" />
		<add key="EJResources" value="jsrender:true;jqueryeasing:true;globalize:true;themes:true;" />
		<add key="DateFormat" value="dd/MM/yyyy" />
		<add key="TimeFormat" value="HH:mm" />
		<add key="domain" value="https://mentalview.gr/" />
		<add key="JoomlaUrl" value="https://site.mentalview.gr/" />
		<add key="EmailHost" value="winzone59.grserver.gr" />
		<add key="EmailPort" value="587" />
		<add key="EmailUseDefaultCredentials" value="false" />
		<add key="EmailEnableSsl" value="true" />
		<add key="EmailAcountUsername" value="<EMAIL>" />
		<add key="EmailAcountPassword" value="infUser@33" />
		<add key="NoReplyAcountUsername" value="<EMAIL>" />
		<add key="NoReplyAcountPassword" value="noReplyUser@33" />
		<add key="SenderEmail" value="<EMAIL>" />
		<add key="SenderName" value="MentalView" />
		<add key="EmailHtmlSignature" value="Με εκτίμηση,&lt;br/&gt;&lt;span style='font-size:14px'&gt;&lt;a href='http://mentalview.gr'&gt;MentalView&lt;/a&gt;&lt;/span&gt;" />
	</appSettings>
	<connectionStrings>
		<add name="MentalViewConnectionString2" connectionString="Data Source=winzone59.grserver.gr,1555;Initial Catalog=mentalview;User=mentalviewAdmin;Password=**************" providerName="System.Data.SqlClient" />
		<add name="MentalViewConnectionString" connectionString="Data Source=MAIN\SQLEXPRESS;Initial Catalog=MentalView;Integrated Security=True;Encrypt=false" providerName="System.Data.SqlClient" />
	</connectionStrings>
	<!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.8.1" />
      </system.Web>
  -->
	<system.web>
		<!-- <trace enabled="true" pageOutput="true" requestLimit="40" localOnly="true"/> -->
		<compilation debug="true" targetFramework="4.7.2">
			<assemblies>
				<add assembly="System.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
				<add assembly="System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
				<add assembly="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
			</assemblies>
		</compilation>
		<httpRuntime targetFramework="4.7.2" />
		<authentication mode="Forms">
			<forms timeout="2880" slidingExpiration="true" />
		</authentication>
		<pages clientIDMode="Static">
			<namespaces>
				<add namespace="System.Web.Optimization" />
			</namespaces>
			<controls>
				<add assembly="Microsoft.AspNet.Web.Optimization.WebForms" namespace="Microsoft.AspNet.Web.Optimization.WebForms" tagPrefix="webopt" />
				<add namespace="Syncfusion.JavaScript.Web" assembly="Syncfusion.EJ, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89" tagPrefix="ej" />
				<add namespace="Syncfusion.JavaScript.DataVisualization.Models" assembly="Syncfusion.EJ, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89" tagPrefix="ej" />
				<add namespace="Syncfusion.JavaScript.Models" assembly="Syncfusion.EJ, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89" tagPrefix="ej" />
				<add namespace="Syncfusion.JavaScript.Web" assembly="Syncfusion.EJ.Web, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89" tagPrefix="ej" />
			</controls>
		</pages>
		<httpHandlers>
			<add verb="*" path="captimage.axd" type="Syncfusion.JavaScript.ImageHandler, Syncfusion.EJ, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89" />
		</httpHandlers>
		<profile defaultProvider="DefaultProfileProvider">
			<providers />
		</profile>
		<membership defaultProvider="DefaultMembershipProvider">
			<providers />
		</membership>
		<roleManager defaultProvider="DefaultRoleProvider">
			<providers />
		</roleManager>
	</system.web>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Web.Infrastructure" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<system.web.extensions>
		<scripting>
			<scriptResourceHandler enableCompression="true" enableCaching="true" />
		</scripting>
	</system.web.extensions>
	<system.webServer>
		<httpCompression>
			<scheme name="gzip" dll="%Windir%\system32\inetsrv\gzip.dll" />
			<dynamicTypes>
				<add mimeType="text/*" enabled="true" />
				<add mimeType="message/*" enabled="true" />
				<add mimeType="application/javascript" enabled="true" />
				<add mimeType="*/*" enabled="true" />
			</dynamicTypes>
			<staticTypes>
				<add mimeType="text/*" enabled="true" />
				<add mimeType="message/*" enabled="true" />
				<add mimeType="application/javascript" enabled="true" />
				<add mimeType="*/*" enabled="true" />
			</staticTypes>
		</httpCompression>
		<urlCompression doStaticCompression="true" doDynamicCompression="true" />
		<staticContent>
			<clientCache cacheControlMode="UseMaxAge" httpExpires="365.00:00:00" />
		</staticContent>
		<validation validateIntegratedModeConfiguration="false" />
		<modules runAllManagedModulesForAllRequests="true" />
		<handlers>
			<add verb="*" path="captimage.axd" name="syncfusion_generatetools" type="Syncfusion.JavaScript.ImageHandler, Syncfusion.EJ, Version=20.3460.0.59, Culture=neutral, PublicKeyToken=3d67ed1f87d44c89" />
			<remove name="ExtensionlessUrlHandler-Integrated-4.0" />
			<remove name="OPTIONSVerbHandler" />
			<remove name="TRACEVerbHandler" />
			<add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
		</handlers>
	</system.webServer>
	<system.codedom>
		<compilers>
			<compiler extension=".cs" language="c#;cs;csharp" warningLevel="4" compilerOptions="/langversion:7.3 /nowarn:1659;1699;1701;612;618" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
			<compiler extension=".vb" language="vb;vbs;visualbasic;vbscript" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008,40000,40008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
		</compilers>
	</system.codedom>
</configuration>