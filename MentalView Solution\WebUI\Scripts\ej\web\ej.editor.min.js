/*!
*  filename: ej.editor.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.globalize.min","./../common/ej.core.min"],n):n()})(function(){(function(n,t,i){t.widget({ejNumericTextbox:["ej.NumericTextbox","e-numerictextbox"],ejPercentageTextbox:["ej.PercentageTextbox","e-percentagetextbox"],ejCurrencyTextbox:["ej.CurrencyTextbox","e-currencytextbox"]},{element:null,model:null,validTags:["input"],_addToPersist:["value"],_setFirst:!1,type:"editor",angular:{require:["?ngModel","^?form","^?ngModelOptions"],requireFormatters:!0,requireParser:!0},defaults:{width:"",height:"",value:null,name:null,htmlAttributes:{},minValue:-Number.MAX_VALUE,maxValue:Number.MAX_VALUE,incrementStep:1,decimalPlaces:0,validateOnType:!1,cssClass:"",enablePersistence:!1,showSpinButton:!0,locale:"en-US",enableStrictMode:!1,showRoundedCorner:!1,readOnly:!1,enabled:!0,enableRTL:!1,watermarkText:"Enter value",validationRules:null,validationMessage:null,groupSeparator:null,groupSize:null,positivePattern:null,currencySymbol:null,negativePattern:null,change:null,focusIn:null,focusOut:null,create:null,destroy:null},dataTypes:{minValue:"number",maxValue:"number",incrementStep:"number",decimalPlaces:"number",showSpinButton:"boolean",enableStrictMode:"boolean",showRoundedCorner:"boolean",enableRTL:"boolean",locale:"string",watermarkText:"string",cssClass:"string",readOnly:"boolean",enabled:"boolean",validationRules:"data",validationMessage:"data",htmlAttributes:"data",validateOnType:"boolean",groupSeparator:"string"},observables:["value"],_setModel:function(n){var r=!1;for(var i in n)switch(i){case"value":t.isNullOrUndefined(n.minValue)&&t.isNullOrUndefined(n.maxValue)?(this._setValue(n[i],!0),n[i]=this.model.value):(this.model.value=this._checkNumValue(n[i]),this._localizedFormat(),this._raiseChangeEvent(!0),r=!0);break;case"enableRTL":this._enableRTL(n[i]);break;case"width":this._setWidth(n[i]);break;case"height":this._setHeight(n[i]);break;case"validationRules":this.model.validationRules!=null&&(this.element.rules("remove"),this.model.validationMessage=null);this.model.validationRules=n[i];this.model.validationRules!=null&&(this._initValidator(),this._setValidation());break;case"validationMessage":this.model.validationMessage=n[i];this.model.validationRules!=null&&this.model.validationMessage!=null&&(this._initValidator(),this._setValidation());break;case"minValue":if(isNaN(n[i]))return;this.model.minValue=this.model.decimalPlaces==-1?n[i]:parseFloat(n[i].toFixed(this.model.decimalPlaces));this._updateValueAttributes(this.model.minValue,this.model.maxValue,this.model.value);r=!0;break;case"maxValue":if(isNaN(n[i]))return;this.model.maxValue=this.model.decimalPlaces==-1?n[i]:parseFloat(n[i].toFixed(this.model.decimalPlaces));this._updateValueAttributes(this.model.minValue,this.model.maxValue,this.model.value);r=!0;break;case"incrementStep":if(isNaN(n[i]))return;this.model.incrementStep=this.model.decimalPlaces==-1?n[i]:parseFloat(n[i].toFixed(this.model.decimalPlaces));break;case"enableStrictMode":this.model.enableStrictMode=n[i];r=!0;break;case"showSpinButton":this._showSpin(n[i]);break;case"showRoundedCorner":this._roundedCorner(n[i]);break;case"locale":this.model.decimalPlaces=t.isNullOrUndefined(this._options.decimalPlaces)&&this.model.decimalPlaces===this.culture.numberFormat.decimals?-1:this._options.decimalPlaces===-1?-1:this.model.decimalPlaces;this._setLocalize(n[i]);this._options.decimalPlaces=this.model.decimalPlaces;n[i]=this.model.locale;break;case"decimalPlaces":this._setDecimal(n[i]);t.isNullOrUndefined(this._options)&&(this._options={});this._options.decimalPlaces=n[i];break;case"cssClass":this._setSkin(n[i]);break;case"readOnly":this._setReadOnly(n[i]);break;case"enabled":n[i]?this.enable():this.disable();break;case"watermarkText":t.isNullOrUndefined(this._options)||(this._options=[]);this._options.watermarkText=this.model.watermarkText=n[i];this._localizedLabels.watermarkText=this.model.watermarkText;this._changeWatermark(n[i]);break;case"htmlAttributes":this._addAttr(n[i]);break;case"groupSeparator":this._checkSeparator(n[i]);n[i]=this.model.groupSeparator;break;case"positivePattern":case"negativePattern":case"groupSize":case"currencySymbol":this.model[i]=n[i];this._options[i]=n[i];this._initCustomValue();n[i]=this.model[i];this._setValue(this.model.value,!0)}r&&(this._validateMinMaxValue(!0,!0),n.value=this.model.value,n.maxValue=this.model.maxValue,n.minValue=this.model.minValue,this.model.minValue!=-Number.MAX_VALUE&&(this._startValue=this.model.minValue),this._updateValueAttributes(this.model.minValue,this.model.maxValue,this.model.value));this._checkSeparator(this.model.groupSeparator);this.element.val(this._removeSeparator(this.model.value));this._updateSeparator();this._checkErrorClass()},_destroy:function(){this.wrapper&&(this.element.insertBefore(this.wrapper),this.wrapper.remove());this._isWatermark&&this.element.removeAttr("placeholder");this.element.val("").removeClass("e-input e-disable e-no-spin").empty();this.element.removeAttr("disabled aria-disabled");this.wrapper.removeAttr("aria-valuemin aria-valuemax aria-valuenow aria-live");this._cloneElement.attr("role")||this.element.removeAttr("role");this.element.css("display","block")},_init:function(n){if(this._cloneElement=this.element.clone(),this._options=t.isNullOrUndefined(n)?{}:n,this.element.is("input")&&(this.element.is("input[type=text]")||this.element.is("input[type=number]")||!this.element.attr("type")))this.model.decimalPlaces>0&&(this.element[0].type=this.element.is("input[type=number]")?"tel":"text"),this.element.is("input[type=number]")&&this.element.addClass("e-no-spin"),this._isWatermark="placeholder"in document.createElement("input"),this.model.locale=t.preferredCulture(this.model.locale).name=="en"?"en-US":t.preferredCulture(this.model.locale).name,this._localizedLabels=this._getLocalizedLabels(),this.culture=t.preferredCulture(this.model.locale),this._browsername=t.browserInfo().name,this._initCustomValue(),this._prevSeparator=null,this._checkSeparator(this.model.groupSeparator),this._checkAttribute(),this._renderControl(),this._setValues(),this._wireEvents(),this._initObjects(),this._addAttr(this.model.htmlAttributes),this.model.validationRules!=null&&(this._initValidator(),this._setValidation()),this._updateSeparator(),n&&n.value!=i&&this._trigger("_change",{value:this.model.value}),this.IsIncremented=!1,this.IsDecremented=!1;else return this._destroy(),!1},_checkAttribute:function(){for(var u=["min","max","step","readonly","disabled","placeholder"],f=["minValue","maxValue","incrementStep","readOnly","enabled","watermarkText"],i,n,r=0;r<u.length;r++)i=this.element.attr(u[r]),n=f[r],t.isNullOrUndefined(i)||t.isNullOrUndefined(this._options)||!t.isNullOrUndefined(this._options[n])||(n=="watermarkText"?this._options[n]=i:this.model[n]=n!="disabled"&&n!="readOnly"?t.parseFloat(i,this.model.locale):n=="readOnly"?this.element.is("[readonly]"):!this.element.is("[disabled]"))},_addAttr:function(i){var r=this;n.map(i,function(n,i){var u=i.toLowerCase();u=="class"?r.wrapper.addClass(n):u=="accesskey"?r._hiddenInput.attr(i,n):u=="disabled"&&n=="disabled"?r.disable():u=="readonly"&&n=="readOnly"?r._setReadOnly(!0):u=="tabindex"?(r._hiddenInput.attr(i,n),r.element.attr(i,n)):u=="style"||u=="id"?r.wrapper.attr(i,n):u=="aria-label"?r.element.attr(i,n):t.isValidAttr(r.element[0],i)?r.element.attr(i,n):r.wrapper.attr(i,n)})},_setValues:function(){var n,i;this._id=this.element[0].id;this._textBox=this._hiddenInput[0];this._error=!1;this._timeout=null;this.isValidState=!0;this._allowkeyboard=!0;this._validateOnType=!1;this._focused=!1;this._startValue=0;this.sfType==="ej.CurrencyTextbox"&&this.model.minValue==-Number.MAX_VALUE&&(this.model.minValue=0);t.isNullOrUndefined(this.model.decimalPlaces)&&(this.model.decimalPlaces=this.sfType==="ej.CurrencyTextbox"?t.preferredCulture(this.model.locale).numberFormat.currency.decimals:t.preferredCulture(this.model.locale).numberFormat.decimals);this.model.decimalPlaces>=0&&(this.model.minValue==-Number.MAX_VALUE?(this.model.minValue=parseFloat(this.model.minValue.toFixed(this.model.decimalPlaces)),this.model.maxValue=parseFloat(this.model.maxValue.toFixed(this.model.decimalPlaces))):(n=Math.pow(10,this.model.decimalPlaces),this.model.minValue=Math.ceil(this.model.minValue*n)/n,this.model.maxValue=Math.floor(this.model.maxValue*n)/n));this.model.minValue!=-Number.MAX_VALUE&&(this._startValue=this.model.minValue);t.isNullOrUndefined(this._options)||t.isNullOrUndefined(this._options.watermarkText)||(this._localizedLabels.watermarkText=this._options.watermarkText);this._localizedLabelToModel();this._localizedFormat();this._validateMinMaxValue(!0);this._updateSymbol(this.model.locale);t.isNullOrUndefined(this.model.value)?(i=this.model.value,this.wrapper.removeClass("e-valid")):(i=this.model.value.toString().indexOf("e")==-1?this._removeSeparator(this.model.value):this._convertToExponetial(this.model.value).unformattedValue,this.wrapper.addClass("e-valid"));this.element.val(i)},_initValidator:function(){this.element.closest("form").data("validator")||this.element.closest("form").validate()},_setValidation:function(){var u=this.model.validationRules,r,e,i,f,o;this.element.rules("add",u);r=this.element.closest("form").data("validator");r=r?r:this.element.closest("form").validate();e=this.element.attr("name");r.settings.messages[e]={};for(i in u)if(f=null,!t.isNullOrUndefined(u[i])){if(t.isNullOrUndefined(u.messages&&u.messages[i])){r.settings.messages[e][i]=n.validator.messages[i];for(o in this.model.validationMessage)i==o?f=this.model.validationMessage[i]:""}else f=u.messages[i];r.settings.messages[e][i]=f!=null?f:n.validator.messages[i]}},_updateValueAttributes:function(n,t,i){n&&n!=-Number.MAX_VALUE&&this.wrapper.attr({"aria-valuemin":n});t&&t!=Number.MAX_VALUE&&this.wrapper.attr({"aria-valuemax":t});i&&this.wrapper.attr({"aria-valuenow":i})},_renderControl:function(){this.wrapper=t.buildTag("span.e-widget e-pinch");this.innerWrap=t.buildTag("span.e-in-wrap e-box ");this.wrapper.attr("style",this.element.attr("style"));this.sfType==="ej.NumericTextbox"?this.wrapper.addClass("e-numeric"):this.sfType==="ej.PercentageTextbox"?this.wrapper.addClass("e-percent"):this.sfType==="ej.CurrencyTextbox"&&this.wrapper.addClass("e-currency");this.wrapper.append(this.innerWrap).insertAfter(this.element);this.innerWrap.append(this.element);this._hiddenInput=t.buildTag("input","",{},{type:"text"}).insertBefore(this.element);this._hiddenInput.attr("data-role","none");this._hiddenInput[0].tabIndex=this.element[0].tabIndex;this._hiddenInput.attr("accesskey",this.element[0].accessKey);this.element[0].accessKey="";this._hiddenInput.css("display","block");this.element.css("display","none");this._isWatermark||(this._hiddenSpan=t.buildTag("span.e-input e-placeholder ").insertAfter(this.element),this._hiddenSpan.text(this._localizedLabels.watermarkText),this._hiddenSpan.css("display","none"),this._on(this._hiddenSpan,"mousedown",this._focusIn),this._on(this._hiddenSpan,"mousewheel",this._mouseWheel),this._on(this._hiddenSpan,"DOMMouseScroll",this._mouseWheel));this.model.name=this.element.attr("name")!=null?this.element.attr("name"):this.model.name!=null?this.model.name:this.element[0].id;this.element.attr("name")==null?this.element.attr("name",this.model.name):"";this.element.addClass("e-input");t.isNullOrUndefined(this.model.value)&&this.element[0].value!=""?(t.preferredCulture(this.model.locale).numberFormat[","]=="."&&(this.element[0].value=this.element[0].value.replace(".",",")),this.model.value=this._checkNumValue(this.element[0].value)):(typeof this.model.value=="string"&&t.preferredCulture(this.model.locale).numberFormat[","]=="."&&(this.model.value=this.model.value.replace(".",",")),this.model.value=this._checkNumValue(this.model.value));this._hiddenInput.attr({value:this.model.value}).addClass("e-input");this.wrapper.attr({role:"spinbutton"});this._updateValueAttributes(this._options.minValue,this._options.maxVal,this.model.value);this.element.attr({"aria-live":"assertive",value:this.model.value});var i=n('<span class="e-select"><span class="e-spin e-spin-up " role="button" aria-label="Increase Value" unselectable="on"><\/span><span class="e-spin e-spin-down" role="button" aria-label="Decrease Value" unselectable="on"><\/span><\/span>');i.find(".e-spin-up").append(t.buildTag("span.e-icon e-arrow e-arrow-sans-up").attr({role:"presentation",unselectable:"on"}));i.find(".e-spin-down").append(t.buildTag("span.e-icon e-arrow e-arrow-sans-down").attr({role:"presentation",unselectable:"on"}));this.innerWrap.append(i);this.spin=this.wrapper.find(".e-select");this.spinUp=this.wrapper.find(".e-spin-up");this.spinDown=this.wrapper.find(".e-spin-down");this._setWidth(this.model.width);this._setHeight(this.model.height);this.model.cssClass!=""&&this._setSkin(this.model.cssClass);this._showSpin(this.model.showSpinButton);this.model.showRoundedCorner&&this._roundedCorner(this.model.showRoundedCorner);this.model.enableRTL&&this._enableRTL(this.model.enableRTL);this.model.readOnly&&this._setReadOnly(this.model.readOnly);this.model.enabled?this.model.enabled&&this.element.hasClass("e-disable")&&this.enable():this.disable()},_initObjects:function(){this._preVal=this.model.value;this.model.value===null?(this.isValidState=!0,this._hiddenInput.val(null)):(this.model.value<this.model.minValue||this.model.value>this.model.maxValue)&&(this.isValidState=!1);this._checkErrorClass();this._setWaterMark()},_showSpin:function(n){n?(this.spin&&(this.spin.show(),this.innerWrap.addClass("e-padding")),this._spinEvents("_on")):(this.spin&&(this.spin.hide(),this.innerWrap.removeClass("e-padding")),this._spinEvents("_off"))},_roundedCorner:function(n){n&&!this.innerWrap.hasClass("e-corner")?this.innerWrap.addClass("e-corner"):this.innerWrap.hasClass("e-corner")&&this.innerWrap.removeClass("e-corner")},_enableRTL:function(n){n?this.spin?this.wrapper.addClass("e-rtl"):this.element.addClass("e-rtl"):this.spin?this.wrapper.removeClass("e-rtl"):this.element.removeClass("e-rtl")},_setWidth:function(n){n!=""?this.wrapper.width(n):this.model.width=this.wrapper.outerWidth()},_setHeight:function(n){n!=""?this.wrapper.height(n):this.model.height=this.wrapper.outerHeight()},_setSkin:function(n){this.wrapper.removeClass(this.model.cssClass);this.wrapper.addClass(n)},_setValue:function(n,t){this._isWatermark||this._hiddenSpan.css("display","none");this.model.value=this._checkNumValue(n);this._validateMinMaxValue(!1);this._checkErrorClass();this._localizedFormat();this._raiseChangeEvent(t);this._setWaterMark()},_setLocalize:function(n){var r=t.preferredCulture(this.model.locale).numberFormat[","],i;this.model.locale=t.preferredCulture(n).name=="en"?"en-US":t.preferredCulture(n).name;this.model.groupSeparator=t.isNullOrUndefined(this._options.groupSeparator)&&r===this.model.groupSeparator?t.preferredCulture(this.model.locale).numberFormat[","]:this.model.groupSeparator;this._localizedLabels=this._getLocalizedLabels();t.isNullOrUndefined(this._options)||t.isNullOrUndefined(this._options.watermarkText)||(this._localizedLabels.watermarkText=this._options.watermarkText);this._localizedLabelToModel();this.culture=t.preferredCulture(this.model.locale);this._initCustomValue();this._updateSymbol(this.model.locale);this._localizedFormat();this._changeWatermark(this.model.watermarkText);i=t.isNullOrUndefined(this.model.value)?this.model.value:this.model.value.toString().indexOf("e")==-1?this._formatValue(this.model.value,"n",!0):this._convertToExponetial(this.model.value).unformattedValue;this.element.val(i)},_localizedLabelToModel:function(){this.model.watermarkText=this._localizedLabels.watermarkText},_updateSymbol:function(n){this._percentSymbol=t.preferredCulture(n).numberFormat.percent.symbol;this._currencySymbol=t.preferredCulture(n).numberFormat.currency.symbol},_setDecimal:function(n){this.model.decimalPlaces=n;this._localizedFormat()},_validateMinMaxValue:function(n,i){var u=!1,r;this.model.minValue>this.model.maxValue&&(this.model.minValue=this.model.maxValue);!t.isNullOrUndefined(this.model.value)&&this.model.minValue>this.model.value?(this.model.enableStrictMode!=!0?(this.isValidState=!0,this._startValue=this.model.value=this.model.minValue):this.isValidState=!1,u=!0):!t.isNullOrUndefined(this.model.value)&&this.model.maxValue<this.model.value?(this.model.enableStrictMode!=!0?(this.isValidState=!0,this.model.value=this.model.maxValue):this.isValidState=!1,u=!0):this.isValidState=!0;this.model.minValue==this.model.maxValue&&(this._startValue=this.model.minValue);u&&n&&(r=this._formatValue(this.model.value,"n",!0),this._hiddenInput.val(r),r=t.isNullOrUndefined(this.model.value)?this.model.value:this.model.value.toString().indexOf("e")==-1?this._formatValue(this.model.value,"n",!0):this._convertToExponetial(this.model.value).unformattedValue,this.element.val(r),this._localizedFormat(),this._raiseChangeEvent(i))},_convertToExponetial:function(n){var h="",u,i,c,e,r=n<0?!0:!1,o,s,f;for(n=n.toString(),i=t.preferredCulture(this.model.locale).numberFormat,n=n.replace(".",i["."]),c=n,s=null,this.sfType==="ej.NumericTextbox"?(u=r?i.pattern[0]:t.isNullOrUndefined(i.pattern[1])?"n":i.pattern[1],n=r?n.replace("-",""):n,e=""):this.sfType==="ej.PercentageTextbox"?(u=r?i.percent.pattern[0]:i.percent.pattern[1],n=r?n.replace("-",""):n,e=i.percent.symbol):this.sfType==="ej.CurrencyTextbox"&&(u=r?i.currency.pattern[0]:i.currency.pattern[1],n=r?n.replace("-",""):n,e=i.currency.symbol),o=0,s=u.length;o<s;o++)f=u.charAt(o),h+=f==="n"?n:f==="$"||f==="%"?e:f;return{formattedValue:h,unformattedValue:c}},_localizedFormat:function(){var i,r,n,u;if(this.culture=t.preferredCulture(this.model.locale),this._decimalSep=t.preferredCulture(this.model.locale).numberFormat["."],t.isNullOrUndefined(this.model.value)){this._textBox.value="";return}this.model.value.toString().indexOf("e")==-1?(this._textBox.value=this._removeSeparator(this.model.value),this._focused||this._textBox.value==""?(u=this._convertToExponetial(this.model.value),this._textBox.value=u.unformattedValue,this._focused||this._textBox.value==""||(this._textBox.value=u.formattedValue)):(this._textBox.value=this._formatValue(this.model.value,"n",!0),this.sfType==="ej.PercentageTextbox"?this._appendPercentSymbol(this._textBox.value):this.sfType==="ej.CurrencyTextbox"&&this._appendCurrencySymbol(this._textBox.value),i=this.sfType==="ej.CurrencyTextbox"?this._textBox.value.indexOf(this._currencySymbol):this.sfType==="ej.PercentageTextbox"?this._textBox.value.indexOf(this._percentSymbol):-1,this.model.decimalPlaces==-1&&this._afterDec!=0&&(this._textBox.value=this._textBox.value.substr(0,this._textBox.value.lastIndexOf(this._decimalSep)),i>1&&(t.isNullOrUndefined(this._afterDec)||this._afterDec=="")&&(this.sfType==="ej.CurrencyTextbox"&&(this._textBox.value=this._textBox.value+" "+this._currencySymbol),this.sfType==="ej.PercentageTextbox"&&(this._textBox.value=this._textBox.value+" "+this._percentSymbol)),this.model.decimalPlaces!=-1||t.isNullOrUndefined(this._afterDec)||this._afterDec==""||(r=this._textBox.value.lastIndexOf(this._decimalSep),r>=0&&(this._textBox.value=this._textBox.value.substr(0,r)),n=this._afterDec,i>1&&(this.sfType==="ej.CurrencyTextbox"&&(n=n+" "+this._currencySymbol),this.sfType==="ej.PercentageTextbox"&&(n=n+" "+this._percentSymbol)),this._textBox.value=this._textBox.value+this._decimalSep+n)))):this._textBox.value=this.model.value.toString()},_checkNumValue:function(n){return typeof n=="string"&&(n=this._changeSeparator?this._replaceSeparator(n,this.model.groupSeparator,t.preferredCulture(this.model.locale).numberFormat[","]):n),typeof n!="string"||isNaN(this._parseValue(n))?typeof n!="number"||isNaN(n)?null:n:(n=this._parseValue(n),parseFloat(n))},_setReadOnly:function(n){this.model.readOnly=n;n?(this.element.attr("readonly",!0),this._hiddenInput.attr("readonly",!0)):(this.element.prop("readonly",!1),this._hiddenInput.prop("readonly",!1))},_setWaterMark:function(){this._localizedLabels.watermarkText!=null&&this._textBox.value===""&&n.trim(this._hiddenInput.val())===""&&(this._isWatermark?(this._hiddenInput.attr("placeholder",this._localizedLabels.watermarkText),this.element.attr("placeholder",this._localizedLabels.watermarkText)):this._hiddenSpan.css("display","block").text(this._localizedLabels.watermarkText))},_changeWatermark:function(n){if(!this.model.enabled)return!1;this._isWatermark?(this._hiddenInput.attr("placeholder",n),this.element.attr("placeholder",n)):this._hiddenSpan.text(n)},_setSelectionRange:function(n,t){function f(i){i.collapse(!0);i.moveEnd("character",t);i.moveStart("character",n);i.select()}var i=this._textBox,r,u;try{i.setSelectionRange?this._browsername=="edge"?setTimeout(function(){i.setSelectionRange(n,t)}):i.setSelectionRange(n,t):i.createTextRange&&(r=i.createTextRange(),f(r))}catch(e){u=this;window.setTimeout(function(){if(document.body.focus(),u._textBox.select(),document.selection){var n=document.selection.createRange();f(n)}},1)}},_getSelection:function(n){if(this._textBox.type!="number"){var t=null;return document.selection?(t=document.selection.createRange(),t.text===""?t.text:this._removeFormats(t.text)):n==null?this.model.value:this._removeFormats(n.substring(this._textBox.selectionStart,this._textBox.selectionEnd))}},_caretPosition:function(){var n=this._textBox,t=0,i;if(n.type!="number")return document.selection?(n.focus(),i=document.selection.createRange(),i.moveStart("character",-n.value.length),t=i.text.length):(n.selectionStart||n.selectionStart=="0")&&(t=n.selectionEnd),t},_appendPercentSymbol:function(n){this._percentSymbol&&(n=n.replace(this._percentSymbol,""));n.indexOf(t.preferredCulture(this.model.locale).numberFormat.percent.symbol)<0&&(this._textBox.value=this._formatValue(this._parseValue(n)/100,"p",!0));this._percentSymbol=t.preferredCulture(this.model.locale).numberFormat.percent.symbol},_appendCurrencySymbol:function(n){this._currencySymbol&&(n=n.replace(this._currencySymbol,""));n.indexOf(t.preferredCulture(this.model.locale).numberFormat.currency.symbol)<0&&(this._textBox.value=this._formatValue(this._parseValue(n),"c",!0));this._currencySymbol=t.preferredCulture(this.model.locale).numberFormat.currency.symbol},_removeFormats:function(n){var i=t.preferredCulture(this.model.locale).numberFormat[","];return n!=null?i=="."?n.toString().replace(/["."]/g,""):n.toString().match(new RegExp(i,"g"))?n.toString().replace(new RegExp(i,"g"),""):n:null},_checkErrorClass:function(){this.isValidState?this.wrapper.removeClass("e-error"):this.wrapper.addClass("e-error")},enable:function(){this.model.enabled=!0;this.element[0].disabled=!1;this.element.prop("disabled",!1);this._hiddenInput.prop("disabled",!1);this.element.removeClass("e-disable").attr({"aria-disabled":!1});this._hiddenInput.removeClass("e-disable").attr({"aria-disabled":!1});this.wrapper.find(".e-select").removeClass("e-disable").attr({"aria-disabled":!1});this.wrapper.find(".e-select span.e-icon.e-arrow").removeClass("e-disable");this.wrapper.removeClass("e-disable-wrap")},disable:function(){this.model.enabled=!1;this.element[0].disabled=!0;this.element.attr("disabled","disabled");this._hiddenInput.attr("disabled","disabled");this.element.addClass("e-disable").attr({"aria-disabled":!0});this._hiddenInput.addClass("e-disable").attr({"aria-disabled":!0});this.wrapper.find(".e-select").addClass("e-disable").attr({"aria-disabled":!0});this.wrapper.find(".e-select span.e-icon.e-arrow").addClass("e-disable");this.wrapper.addClass("e-disable-wrap")},getValue:function(){return this.model.value},_wireEvents:function(){this._on(this._hiddenInput,"focus",this._focusIn);this._on(this.element,"paste",this._paste);this._on(this.element,"blur",this._focusOut);this._on(this.element,"keydown",this._keyDown);this._on(this.element,"keypress",this._keyPress);this._on(this.element,"mousewheel",this._mouseWheel);this._on(this.element,"DOMMouseScroll",this._mouseWheel)},_spinEvents:function(n){this[n](this.spinUp,"mousedown mouseup touchstart touchend",this._spinUpClick);this[n](this.spinDown,"mousedown mouseup touchstart touchend",this._spinDownClick)},_isIE8:function(){var n=!1,i=t.browserInfo();return i.name=="msie"&&i.version=="8.0"&&(n=!0),n},_spinUpClick:function(t){var r=!1,i;(t.button?r=this._isIE8()?t.button!=1:t.button!=0:t.which&&(r=t.which==3),r)||(i=this,t.preventDefault(),clearTimeout(this._timeout),this.model.enabled&&!this.model.readOnly)&&(this.wrapper.find(".e-animate").removeClass("e-animate"),this.spinUp.addClass("e-animate"),this._on(this.spinUp,"mouseleave",this._mouseUpClick),this.spinUp.addClass("e-active"),i=this,t.type=="mouseup"||t.type=="touchend"?(i.IsIncremented||this._updateInputField("increment"),i.IsIncremented=!1,this.spinUp.removeClass("e-active"),this._off(n(document),"mouseup",this._mouseUpClick)):(t.type=="mousedown"||t.type=="touchstart")&&(this._focused||this._hiddenInput[0].focus(),this._timeout=setInterval(function(){i._updateInputField("increment");i.IsIncremented=!0},150),this._on(n(document),"mouseup",this._mouseUpClick)))},_spinDownClick:function(t){var r=!1,i;(t.button?r=this._isIE8()?t.button!=1:t.button!=0:t.which&&(r=t.which==3),r)||(i=this,t.preventDefault(),clearTimeout(this._timeout),this.model.enabled&&!this.model.readOnly)&&(this.wrapper.find(".e-animate").removeClass("e-animate"),this.spinDown.addClass("e-animate"),this._on(this.spinDown,"mouseleave",this._mouseUpClick),this.spinDown.addClass("e-active"),t.type=="mouseup"||t.type=="touchend"?(i.IsDecremented||this._updateInputField("decrement"),i.IsDecremented=!1,this.spinDown.removeClass("e-active"),this._off(n(document),"mouseup",this._mouseUpClick)):(t.type=="mousedown"||t.type=="touchstart")&&(this._focused||this._hiddenInput[0].focus(),this._timeout=setInterval(function(){i._updateInputField("decrement");i.IsDecremented=!0},150),this._on(n(document),"mouseup",this._mouseUpClick)))},_mouseUpClick:function(n){n.stopPropagation();clearTimeout(this._timeout);this._off(this.spinUp,"mouseleave",this._mouseUpClick);this._off(this.spinDown,"mouseleave",this._mouseUpClick);this.spinDown.removeClass("e-active");this.spinUp.removeClass("e-active")},_mouseWheel:function(n){if(n.preventDefault(),this._focused||this.element[0].focus(),this.model.enabled&&!this.model.readOnly){var t,i=n.originalEvent;i.wheelDelta?t=i.wheelDelta/120:i.detail&&(t=-i.detail/3);t>0?this._updateInputField("increment"):t<0&&this._updateInputField("decrement");this._cancelEvent(n)}},_numberValue:function(){var n=this._textBox.value;return this.sfType==="ej.NumericTextbox"&&(n=this._formatValue(this.model.value,"n",!0)),this.sfType==="ej.PercentageTextbox"?n=this._formatValue(this.model.value,"n",!0):this.sfType==="ej.CurrencyTextbox"&&(n=this._formatValue(this.model.value,"n",!0)),typeof n=="string"&&!isNaN(this._parseValue(n))?n:""},_formatValue:function(n,i,r){r&&this._updateCultureInfo();var n;return n=this.model.decimalPlaces!=-1||this.model.decimalPlaces==-1&&this.model.value==null?t.format(n,i+this.model.decimalPlaces,this.model.locale):t.format(n,i+this._afterDec.length,this.model.locale),r&&this._restCultureInfo(),n},_parseValue:function(n){this._updateCultureInfo();var n=t.parseFloat(n,this.model.locale);return this._restCultureInfo(),n},_initCustomValue:function(){var n,i=this.sfType=="ej.PercentageTextbox"?"percent":this.sfType=="ej.CurrencyTextbox"?"currency":"numeric";switch(i){case"percent":case"currency":n=this.culture.numberFormat[i];t.isNullOrUndefined(this._options.negativePattern)&&(this.model.negativePattern=n.pattern[0]);t.isNullOrUndefined(this._options.positivePattern)&&(this.model.positivePattern=n.pattern[1]);t.isNullOrUndefined(this._options.currencySymbol)&&i=="currency"&&(this.model.currencySymbol=n.symbol);t.isNullOrUndefined(this._options.groupSize)&&(this.model.groupSize=n.groupSizes[0]);break;case"numeric":n=this.culture.numberFormat;t.isNullOrUndefined(this._options.negativePattern)&&(this.model.negativePattern=n.pattern[0]);t.isNullOrUndefined(this._options.positivePattern)&&(this.model.positivePattern=t.isNullOrUndefined(n.pattern[1])?"n":n.pattern[1]);t.isNullOrUndefined(this._options.groupSize)&&(this.model.groupSize=n.groupSizes[0])}},_updateCultureInfo:function(){var n,t=this.sfType=="ej.PercentageTextbox"?"percent":this.sfType=="ej.CurrencyTextbox"?"currency":"numeric";n=t=="numeric"?this.culture.numberFormat:this.culture.numberFormat[t];this._oldNegativePattern=n.pattern[0];this._oldGroupSize=n.groupSizes[0];n.pattern[0]=this.model.negativePattern;n.groupSizes[0]=this.model.groupSize;this._oldPositivePattern=n.pattern[1];n.pattern[1]=this.model.positivePattern;t=="currency"&&(this._oldcurrencySymbol=n.symbol,n.symbol=this.model.currencySymbol)},_restCultureInfo:function(){var n,t=this.sfType=="ej.PercentageTextbox"?"percent":this.sfType=="ej.CurrencyTextbox"?"currency":"numeric";n=t=="numeric"?this.culture.numberFormat:this.culture.numberFormat[t];n.pattern[0]=this._oldNegativePattern;n.groupSizes[0]=this._oldGroupSize;n.pattern[1]=this._oldPositivePattern;t=="currency"&&(n.symbol=this._oldcurrencySymbol)},_toggleTextbox:function(n){var t=this;t._hiddenInput.toggle(n);t.element.toggle(!n)},_paste:function(n){var i,r;window.clipboardData&&window.clipboardData.getData?i=window.clipboardData.getData("Text"):n.originalEvent.clipboardData&&n.originalEvent.clipboardData.getData&&(i=n.originalEvent.clipboardData.getData("text/plain"));r=t.parseFloat(i,this.model.locale);isNaN(r)&&i&&this._cancelEvent(n)},_focusIn:function(){if(!this.model.readOnly){if(this._focused=!0,t.isNullOrUndefined(this.model.value)||this.element.val(this._removeSeparator(this.model.value)),this._toggleTextbox(!1),this._textBox=this.element[0],this.element[0].focus(),this._preVal=this.model.value,this._isWatermark||this._hiddenSpan.css("display","none"),this.wrapper.addClass("e-focus"),this.wrapper.removeClass("e-error"),!this._error){if(this._textBox.value!=""){var n=this._formatValue(this._textBox.value,"n",!0);this.model.decimalPlaces==-1&&(this._separateValue(this._textBox.value.toString(),!0,!0),t.isNullOrUndefined(this._afterDec)||this._afterDec==""||(n=this._beforeDec+this._decimalSep+this._afterDec));this._textBox.value=n;this._hiddenInput.val(n)}this._setSelectionRange(0,this._textBox.value.length)}this._trigger("focusIn",{value:this.model.value})}},_separateValue:function(n,t,i){var r=t?n.lastIndexOf(this._decimalSep):n.lastIndexOf(".");this._beforeDec=r>=0?n.substr(0,r):n;!t&&i&&(this._afterDec=r>=0?n.substr(r+1):"")},_focusOut:function(){var n;this._focused=!1;this.wrapper.removeClass("e-focus");this._separateValue(this._textBox.value,!1,!0);this._error||(this._textBox.value!=""?(n=t.parseFloat(this._textBox.value,this.model.locale),n<this.model.minValue?this.model.enableStrictMode?this.isValidState=!1:this._textBox.value=this._formatValue(this.model.minValue,"n",!1):n>this.model.maxValue&&(this.model.enableStrictMode?this.isValidState=!1:this._textBox.value=this._formatValue(this.model.maxValue,"n",!1)),this.model.value=this.model.decimalPlaces==-1&&!t.isNullOrUndefined(this._afterDec)&&this._textBox.value.lastIndexOf(this._decimalSep)==-1&&n.toString().indexOf("e")==-1?t.parseFloat(this._textBox.value+this._decimalSep+this._afterDec,this.model.locale):t.parseFloat(this._textBox.value,this.model.locale),isNaN(this.model.value)&&!this.model.value&&(this.model.value=null,this.isValidState=!1),this._toggleTextbox(!0),this._textBox=this._hiddenInput[0],this._localizedFormat(),this._checkErrorClass()):(n=this._textBox.value==""?null:this._textBox.value,this.model.value=n,this._hiddenInput.val(n),this._toggleTextbox(!0),this._textBox=this._hiddenInput[0]),this.model.value===null||this.model.value>=this.model.minValue&&this.model.value<=this.model.maxValue?this.isValidState=!0:this.model.enableStrictMode&&(this.isValidState=!1),this._raiseChangeEvent(),this._setWaterMark(),n=t.isNullOrUndefined(this.model.value)?this.model.value:this.model.value.toString().indexOf("e")==-1?this._removeSeparator(this.model.value):this._convertToExponetial(this.model.value).unformattedValue,this.element.val(n),this._updateSeparator(),this._trigger("focusOut",{value:this.model.value}));this._checkErrorClass();this._afterDec=""},_cancelEvent:function(n){return n.cancelBubble=!0,n.returnValue=!1,n.stopPropagation(),n.preventDefault(),!1},_updateInputField:function(r){var e,o,f,u;this.isValidState=!0;this._focused||n(this._hiddenInput[0]).focus();e=this.model.incrementStep;o=this.model.value;this._textBox.value===""?(this._textBox.value=this._formatValue(this._startValue,"n",!0),e=0):this._textBox.value.indexOf(" ")>=0&&(this._textBox.value=this._textBox.value.replace(" ",""));this.sfType==="ej.PercentageTextbox"||this.sfType==="ej.CurrencyTextbox"?(f=this._textBox.value,u=f,f.indexOf(t.preferredCulture(this.model.locale).numberFormat.percent.symbol)>-1&&(u=f.substring(0,f.length-1)),this.model.value=this._parseValue(u)):this.sfType=="ej.NumericTextbox"&&(this.model.value=t.parseFloat(this._textBox.value,this.model.locale));isNaN(this.model.value)&&!this.model.value&&(this.model.value=this._startValue);this.model.value>=this.model.minValue&&this.model.value>this.model.maxValue?(this.model.value=this.model.maxValue,this._setValue(this.model.value)):this.model.value<this.model.minValue&&this.model.value<=this.model.maxValue?(this.model.value=this.model.minValue,this._setValue(this.model.value)):this.model.value>=this.model.minValue&&this.model.value<=this.model.maxValue&&(u=r=="increment"?this.model.value+e:this.model.value-e,this.model.decimalPlaces==-1&&(this.value==i&&this._setValue(this.model.value),u=this.model.value.toString().indexOf("e")==-1?parseFloat(u.toFixed(this._afterDec.length)):parseFloat(u)),u>=this.model.minValue&&u<=this.model.maxValue&&this._setValue(u));this._checkErrorClass()},_validateDecimal:function(n){var i=String.fromCharCode(n.keyCode);return(i=n.keyCode==188?",":n.keyCode==190?".":n.keyCode==110?t.preferredCulture(this.model.locale).numberFormat["."]:i,t.preferredCulture(this.model.locale).numberFormat["."].charCodeAt(0)==i.charCodeAt(0))?!0:!1},_allowKeyCodes:function(n){for(var i=[38,40,35,36,109,189,46,8,127,37,39,190,9,13,16,17,18,20,110,173,86,88,67],t=0;t<i.length;t++)if(n.keyCode==i[t]||this._validateDecimal(n)&&this.model.decimalPlaces!=0)return!0;return!1},_raiseChangeEvent:function(n){var r=this.model.value,i;this._checkNumValue(this._preVal)!==this._checkNumValue(r)&&(this._preVal=r,this.model.value=this.model.decimalPlaces==-1&&!t.isNullOrUndefined(this.model.value)?parseFloat(this.model.value):this._checkNumValue(this._formatValue(this._preVal,"n",!1)),this._updateHiddenField(),t.isNullOrUndefined(this.model.value)?(i=this.model.value,this.wrapper.removeClass("e-valid")):(i=this.model.value.toString().indexOf("e")==-1?this._removeSeparator(this.model.value):this._convertToExponetial(this.model.value).unformattedValue,this.model.decimalPlaces==-1&&(this._separateValue(this.model.value.toString(),!1,!0),t.isNullOrUndefined(this._afterDec)||this._afterDec==""||(i=this._beforeDec+this._decimalSep+this._afterDec)),this.wrapper.addClass("e-valid")),this.element.val(i),this.wrapper.attr("aria-valuenow",i),this._updateSeparator(),this.element.trigger("change"),this._trigger("_change",{value:this.model.value,isInteraction:!n}),this._trigger("change",{value:this.model.value,isInteraction:!n}))},_updateHiddenField:function(){var n=this._textBox;this._textBox=this._hiddenInput[0];this._localizedFormat();this._textBox=n},_removeSeparator:function(n){if(!t.isNullOrUndefined(n)){var i,r;return this.model.decimalPlaces==-1&&this._separateValue(n.toString(),!1,!0),n.toString().indexOf("e")==-1&&(n=this._checkNumValue(this._formatValue(n,"n",!1))),i=n.toString(),r=t.preferredCulture(this.model.locale).numberFormat,i.replace(".",r["."])}},_updateSeparator:function(){var n,r,u,f,i;this._changeSeparator&&this.model.value&&(this.sfType==="ej.NumericTextbox"?n=this._formatValue(this.model.value,"n",!0):this.sfType==="ej.PercentageTextbox"?n=this._formatValue(this.model.value/100,"p",!0):this.sfType==="ej.CurrencyTextbox"&&(n=this._formatValue(this.model.value,"c",!0)),this.model.decimalPlaces==-1&&(r=n.lastIndexOf(this._decimalSep),u=n.substr(r+1),this._separateValue(n,!0,!1),f=this.sfType==="ej.CurrencyTextbox"?n.indexOf(this._currencySymbol):this.sfType==="ej.PercentageTextbox"?this._textBox.value.indexOf(this._percentSymbol):-1,i=this._afterDec,f>1&&(this.sfType==="ej.CurrencyTextbox"&&(i=i+" "+this._currencySymbol),this.sfType==="ej.PercentageTextbox"&&(i=i+" "+this._percentSymbol)),t.isNullOrUndefined(this._afterDec)||this._afterDec==""||(n=this._beforeDec+this._decimalSep+i),t.isNullOrUndefined(this._afterDec)||this._afterDec!=""||(n=this._beforeDec+this._decimalSep+u)),this._hiddenInput.val(this._replaceSeparator(n,t.preferredCulture(this.model.locale).numberFormat[","],this.model.groupSeparator)))},_replaceSeparator:function(n,i,r){var u,f,o,e;return(this._decimalSep=t.preferredCulture(this.model.locale).numberFormat["."],u=i===""?new RegExp("\\s","g"):new RegExp("\\"+i,"g"),this.model.groupSeparator==t.preferredCulture(this.model.locale).numberFormat["."])?(f=this.model.decimalPlaces,this.model.decimalPlaces==-1&&(o=n.lastIndexOf(this._decimalSep),f=n.substr(o+1).length),e=n.length-f-1,n.substring(0,e).replace(u,r)+n.substring(e,n.length)):n.replace(u,r)},_checkSeparator:function(n){this.model.groupSeparator=n!=null?this._validateSeparator(n):t.preferredCulture(this.model.locale).numberFormat[","];this._changeSeparator=t.preferredCulture(this.model.locale).numberFormat[","]!=this.model.groupSeparator?!0:!1;this._prevSeparator=this.model.groupSeparator},_validateSeparator:function(n){var i=n.toString(),r=new RegExp("[a-zA-Z0-9]");return i=i.length>1?i[0]:i,r.test(i)?this._prevSeparator!=null?this._prevSeparator:t.preferredCulture(this.model.locale).numberFormat[","]:i},_keyPress:function(n){var c,i;if(n.which!==0&&!n.metaKey&&!n.ctrlKey&&n.keyCode!==8&&n.keyCode!==13){var l=this,u=null,f=".",r,e,o,s,h;return r=this._caretPosition(),e=this._textBox.value.toString().substring(0,r),o=this._textBox.value.toString().substring(r),s=t.preferredCulture(this.model.locale).numberFormat,c=String.fromCharCode(n.which),h=e+c+o,i=s[f],i=i===f?"\\"+i:i,u=l.model.decimalPlaces===0?new RegExp("^((-)?(\\d*)(-)?)?$"):new RegExp("^(-)?(((\\d+("+i+"\\d*)?)|("+i+"\\d*)))?((-)?)$"),u.test(h)?void 0:!1}},_validateDecimalOnType:function(n){var r,e,s,h,o,u,i,f;return r=this._caretPosition(),s=this._textBox.value.toString().substring(0,r),h=this._textBox.value.toString().substring(r),o=this._getSelection(this._textBox.value),i=t.preferredCulture(this.model.locale).numberFormat["."],u=t.isNullOrUndefined(this.model.value)?"":this.model.value.toString(),this.model.decimalPlaces!=0?(f=this._textBox.value.split(i)[1],e=u.indexOf(i)>0?u.substring(u.indexOf(i)+1,u.length).length>this.model.decimalPlaces?!0:o.length==0&&r>this._textBox.value.indexOf(i)&&f&&f.length>=this.model.decimalPlaces?!0:!1:o.length==0&&r>this._textBox.value.indexOf(i)&&f&&f.length>=this.model.decimalPlaces?!0:!1):e=!1,e?(this._keypressFlag=!1,this._cancelEvent(n),!1):void 0},_keyDown:function(r){var s,v,k,d,y,p,tt,b;if(!this.model.readOnly)if(this._CurrentCultureInfo=JSON.parse(JSON.stringify(t.preferredCulture())),r.keyCode>=48&&r.keyCode<=57||r.keyCode>=96&&r.keyCode<=105||this._allowKeyCodes(r)){if(r.shiftKey&&(r.keyCode==35||r.keyCode==36||r.keyCode==37||r.keyCode==39||r.keyCode==46||r.keyCode==127)||r.ctrlKey&&(r.keyCode==86||r.keyCode==118||r.keyCode==67||r.keyCode==88))return!0;if(r.ctrlKey==!0&&r.keyCode!=9&&r.keyCode!=17&&r.keyCode!=86&&r.keyCode!=67||r.keyCode==67||r.keyCode==86||r.keyCode==88)return this._keypressFlag=!1,this._cancelEvent(r),!1;if((r.keyCode>=48&&r.keyCode<=57||r.keyCode>=96&&r.keyCode<=105||r.keyCode==110)&&(s=this._caretPosition(),k=this._textBox.value.toString().substring(0,s),d=this._textBox.value.toString().substring(s),y=this._getSelection(this._textBox.value),r.keyCode>=96&&r.keyCode<=105&&(r.keyCode-=48),this.model.validateOnType&&this.model.decimalPlaces!=-1&&this._validateDecimalOnType(r),this._validateOnType)){var u=t.preferredCulture(this.model.locale).numberFormat["."],f=this.model.value.toString(),it=f.indexOf(u),g=this.model.value,e=this.model.decimalPlaces==-1?this.model.minValue:this.model.minValue.toFixed(this.model.decimalPlaces),o=this.model.decimalPlaces==-1?this.model.maxValue:this.model.maxValue.toFixed(this.model.decimalPlaces),w=!1;if((e.toString().indexOf("e")>0||o.toString().indexOf("e")>0)&&(w=!0),w)this.model.decimalPlaces!=0?(p=this._textBox.value.split(".")[1],v=f.indexOf(u)>0||Number(f)<Number(e)||Number(f)>Number(o)?f.substring(f.indexOf(u)+1,f.length).length>this.model.decimalPlaces?!0:y.length==0&&s>this._textBox.value.indexOf(u)&&p&&p.length>=this.model.decimalPlaces?!0:!1:y.length==0&&s>this._textBox.value.indexOf(u)&&p&&p.length>=this.model.decimalPlaces?!0:!1):v=!1;else if(u!="."&&(f=this._textBox.value.toString(),this.model.minValue.toString().match(new RegExp(".","g"))&&(e=this.model.minValue.toString().replace(/["."]/g,u)),this.model.maxValue.toString().match(new RegExp(".","g"))&&(o=this.model.maxValue.toString().replace(/["."]/g,u))),h=e.toString().indexOf(u)>0?Number(e.toString().substring(0,e.toString().indexOf(u))):this.model.minValue,c=o.toString().indexOf(u)>0?Number(o.toString().toString().substring(0,o.toString().indexOf(u))):this.model.maxValue,it>0){g=Number(f.substring(f.indexOf(u)+1,f.toString().length));h=e.toString().indexOf(u)>0?Number(e.toString().substring(e.toString().indexOf(u)+1,e.toString().length)):0;c=o.toString().indexOf(u)>0?Number(o.toString().substring(o.toString().indexOf(u)+1,o.toString().length)):0;var rt=Number(f.substring(0,f.indexOf(u))),ut=Number(e.toString().substring(0,e.toString().indexOf(u))),ft=Number(o.toString().substring(0,o.toString().indexOf(u)));v=this._validateValue(g,h,c,!0,rt,ut,ft)?!1:!0}else v=this._validateValue(g,h,c,!1)?!1:!0;if(v)return this._keypressFlag=!1,this._cancelEvent(r),!1}if(r.keyCode==38&&this._allowkeyboard&&(this._updateInputField("increment"),this._cancelEvent(r)),r.keyCode==40&&this._allowkeyboard&&(this._updateInputField("decrement"),this._cancelEvent(r)),r.keyCode==8&&(s=this._caretPosition(),k=this._textBox.value.substring(0,s),d=this._textBox.value.substring(s),y=this._getSelection(this._textBox.value)),(r.keyCode==46||r.keyCode==127)&&(s=this._caretPosition(),k=this._textBox.value.substring(0,s),d=this._textBox.value.substring(s),y=this._getSelection(this._textBox.value)),this._validateDecimal(r)&&this.model.decimalPlaces!=0&&r.keyCode!=46){var l=t.preferredCulture(this.model.locale).numberFormat["."],h,c,nt=this._textBox.value.split(l),a=this._caretPosition();if(this._textBox.selectionEnd-this._textBox.selectionStart==this._textBox.value.length)return this._textBox.value=l,this._setSelectionRange(a+1,a+1),this._keypressFlag=!1,this._cancelEvent(r),!1;if(nt[1]==i){if(tt=nt[0].substring(0,a),b=nt[0].substring(a),(this.model.minValue.toString().indexOf("e")>0||this.model.maxValue.toString().indexOf("e")>0)&&(w=!0),this.model.decimalPlaces!=-1&&b.length>this.model.decimalPlaces&&this.model.validateOnType==!0)return this._keypressFlag=!1,this._cancelEvent(r),!1;if(this._validateOnType&&!w)if(this.model.minValue.toString().match(new RegExp(".","g"))&&(h=this.model.minValue.toString().replace(/["."]/g,l)),this.model.maxValue.toString().match(new RegExp(".","g"))&&(c=this.model.maxValue.toString().replace(/["."]/g,l)),h=h.indexOf(l)>0?Number(h.substring(0,h.indexOf(l))):Number(h),c=c.indexOf(l)>0?Number(c.substring(0,c.indexOf(l))):Number(c),this._validateValue(this.model.value,h,c,"DecimalKeyPressed"))this._textBox.value=tt+l+b;else return this._keypressFlag=!1,this._cancelEvent(r),!1;else this._textBox.value=tt+l+b;this._setSelectionRange(a+1,a+1)}this._cancelEvent(r)}else(r.keyCode==190||r.keyCode==110)&&this._cancelEvent(r);(r.keyCode==109||r.keyCode==189||r.keyCode==173)&&((this.model.value===this._preVal||this.model.value===null)&&(this.model.value=this._textBox.value),this._caretPosition()!=0&&this._getSelection(this._removeFormats(this._textBox.value))!=this.model.value||this.model.minValue>=0&&!this.model.enableStrictMode||this._textBox.value.toString().match(new RegExp("-","g"))&&this._getSelection(this._textBox.value)===""?(this._preVal=this.model.value,this.model.value=this._textBox.value,this._cancelEvent(r)):this._getSelection()==this.model.value&&(this._preVal=this.model.value,this.model.value=null));r.keyCode==13&&this._checkNumValue(this._preVal)!==this._checkNumValue(this._textBox.value)&&this._setValue(this._textBox.value)}else(r.keyCode!=27&&!r.ctrlKey||r.ctrlKey&&r.keyCode==90&&n.trim(this._textBox.value)==="")&&(this._keypressFlag=!1,this._cancelEvent(r))},_getLocalizedLabels:function(){return t.getLocalizedConstants(this.sfType,this.model.locale)}});t.NumericTextbox.Locale=t.NumericTextbox.Locale||{};t.NumericTextbox.Locale["default"]=t.NumericTextbox.Locale["en-US"]={watermarkText:"Enter value"};t.PercentageTextbox.Locale=t.PercentageTextbox.Locale||{};t.PercentageTextbox.Locale["default"]=t.PercentageTextbox.Locale["en-US"]={watermarkText:"Enter value"};t.CurrencyTextbox.Locale=t.CurrencyTextbox.Locale||{};t.CurrencyTextbox.Locale["default"]=t.CurrencyTextbox.Locale["en-US"]={watermarkText:"Enter value"}})(jQuery,Syncfusion)});
