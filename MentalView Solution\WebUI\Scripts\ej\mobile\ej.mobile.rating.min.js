/*!
*  filename: ej.mobile.rating.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function($,ej){ej.widget("ejmRating","ej.mobile.Rating",{_setFirst:!0,_rootCSS:"e-m-rating",defaults:{maxValue:5,minValue:0,value:1,incrementStep:1,precision:"full",enabled:!0,renderMode:"auto",shape:"star",shapeWidth:25,shapeHeight:25,spaceBetweenShapes:15,tap:null,change:null,touchMove:null,orientation:"horizontal",readOnly:!1,enablePersistence:!1,backgroundColor:null,selectionColor:null,hoverColor:null,borderColor:null},dataTypes:{enabled:"boolean",renderMode:"enum",precision:"enum",shape:"enum",orientation:"enum",readOnly:"boolean",enablePersistence:"boolean"},observables:["value"],value:ej.util.valueFunction("value"),_init:function(){App.activePage||App.createAppView();this._cloneElement=this.element.clone();this._renderControl();this._wireEvents(!1);this._isLoaded=!0},_initObjects:function(){this.element.css({display:"inline-block"});this._canvasElement=this.element.find("canvas")[0];this._renderRatingControl();this._hiddenField=this.element.find("input")[0]},_renderControl:function(){ej.setRenderMode(this);this.element.addClass("e-m-"+this.model.renderMode);this.model.enabled||this.element.addClass("e-m-state-disabled");this._renderColorCodes();this["_"+this.model.precision+"PrecisionProperties"]();this._canvasElement=ej.buildTag("canvas");this._canvasElement.css("-ms-touch-action","none");this.element.append(this._canvasElement);this._hiddenField=ej.buildTag("input ",{},{},{type:"hidden",value:this.model.minValue+this.value()});this.element.append(this._hiddenField);this._initObjects()},_fullPrecisionProperties:function(){var n=Math.round(this.value());this.value(n-n%this.model.incrementStep);this._currentIndex=Number(this.value()/this.model.incrementStep)},_exactPrecisionProperties:function(){var n=Math.ceil(this.value());this.value(Math.round(this.value(),1));this._pricisionProperties(n)},_halfPrecisionProperties:function(){var n=Math.ceil(this.value());this.value(this.value()-this.value()%(Number(this.model.incrementStep)/2));this._pricisionProperties(n)},_pricisionProperties:function(n){this.value()%this.model.incrementStep!=0?(n=n+n%this.model.incrementStep,currentItemIndex=Number(n/this.model.incrementStep)):(n=this.value(),currentItemIndex=Number(n/this.model.incrementStep))},_hexTorgb:function(n){var n=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(n);return n[1]=parseInt(n[1],16),n[2]=parseInt(n[2],16),n[3]=parseInt(n[3],16),color=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:n[1],G:n[2],B:n[3],A:0,Name:"64b7b7b7"}}]})},_renderColorCodes:function(){var bgcolor,shapebgcolor,shapeselectcolor,shapehovercolor,glowcolor;this.model.renderMode=="ios7"?(bgcolor=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:13,G:122,B:255,A:0,Name:"Transparent"}}]}),shapebgcolor=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:238,G:238,B:238,A:100,Name:"64ff9d09"}}]}),shapeselectcolor=JSON.stringify({ColorInfo:[{ColorStop:0,Color:{R:13,G:122,B:255,A:100,Name:"64c1dff7"}}]}),shapehovercolor=JSON.stringify({ColorInfo:[{ColorStop:0,Color:{R:13,G:122,B:255,A:100,Name:"64c1dff7"}}]})):this.model.renderMode=="android"?(bgcolor=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:255,G:255,B:255,A:100,Name:"6433b5e5"}}]}),shapebgcolor=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:188,G:188,B:188,A:40,Name:"64b7b7b7"}}]}),shapeselectcolor=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:54,G:190,B:240,A:100,Name:"6433b5e5"}}]}),shapehovercolor=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:54,G:190,B:240,A:100,Name:"6433b5e5"}}]}),glowcolor=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:74,G:188,B:230,A:100,Name:"644abce6"}}]})):this.model.renderMode=="windows"?(bgcolor=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:255,G:255,B:255,A:100,Name:"6433b5e5"}}]}),shapebgcolor=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:209,G:207,B:207,A:100,Name:"64b7b7b7"}}]}),shapeselectcolor=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:70,G:70,B:70,A:100,Name:"6433b5e5"}}]}),shapehovercolor=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:70,G:70,B:70,A:100,Name:"6433b5e5"}}]})):this.model.renderMode=="flat"&&(bgcolor=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:51,G:181,B:229,A:100,Name:"6433b5e5"}}]}),shapebgcolor=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:242,G:242,B:242,A:100,Name:"64b7b7b7"}}]}),shapeselectcolor=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:243,G:133,B:41,A:100,Name:"6433b5e5"}}]}),shapehovercolor=JSON.stringify({ColorInfo:[{ColorStop:NaN,Color:{R:243,G:133,B:41,A:100,Name:"6433b5e5"}}]}));typeof this.model.backgroundColor=="string"&&(shapebgcolor=this._hexTorgb(this.model.backgroundColor));typeof this.model.selectionColor=="string"&&(shapeselectcolor=this._hexTorgb(this.model.selectionColor));typeof this.model.hoverColor=="string"&&(shapehovercolor=this._hexTorgb(this.model.hoverColor));bgcolor=bgcolor.replace("null",NaN);shapebgcolor=shapebgcolor.replace("null",NaN);shapeselectcolor=shapeselectcolor.replace("null",NaN);shapehovercolor=shapehovercolor.replace("null",NaN);this._backgroundColor=eval("("+bgcolor+")");this._shapeBackgroundColor=eval("("+shapebgcolor+")");this._shapeSelectedColor=eval("("+shapeselectcolor+")");this._shapeHoverColor=eval("("+shapehovercolor+")");this.model.renderMode=="android"&&(glowcolor=glowcolor.replace("null",NaN),this.model.glowColor=eval("("+glowcolor+")"))},_createDelegates:function(){this._canvasMDDelegate=$.proxy(this._canvasMouseDownHandler,this);this._canvasMMDelegate=$.proxy(this._canvasMouseMoveHandler,this);this._canvasMUDelegate=$.proxy(this._canvasMouseUpHandler,this);this._documentEndDelegate=$.proxy(this._documentEndHandler,this)},_wireEvents:function(n){this.model.enabled&&(this._createDelegates(),this.model.readOnly||ej.listenEvents([this._canvasElement,this._canvasElement,document],[ej.startEvent(),ej.endEvent(),ej.endEvent()],[this._canvasMDDelegate,this._canvasMUDelegate,this._documentEndDelegate],n))},_canvasMouseDownHandler:function(n){ej.blockDefaultActions(n);this._move=!1;this._canvasAction="mouseDown";ej.listenTouchEvent(this._canvasElement,ej.moveEvent(),this._canvasMMDelegate,!1);var t={value:this.value()};this.model.tap&&this._trigger("tap",t)},_canvasMouseMoveHandler:function(n){ej.blockDefaultActions(n);this._move=!0;ej.isTouchDevice()&&(n=n.touches?n.touches[0]:n);this._canvasAction="mouseMove";this._getCursorPosition(n);this._mouseMove(n)},_mouseMove:function(n){var r,u,t,i;this._getCursorPosition(n);this._mousepos=this.model.orientation=="vertical"?this._mousePosition.y:this._mousePosition.x;r=0;u=0;this["_"+this.model.orientation+"MouseMoveOrUpProperties"]();t=this._getRoundOffValue(this._value);this._currentMouseMove=t;this._canvasContext.canvas.title=this.model.minValue+parseFloat(t);this._canvasContext.canvas.alt=this.model.minValue+parseFloat(t);this._renderRatingControl();i={value:this.value()};this.model.touchMove&&this._trigger("touchMove",i)},_verticalMouseMoveOrUpProperties:function(){shapesCount=Math.floor(this._mousepos-1)/(this.model.shapeHeight+this.model.spaceBetweenShapes);this._value=((this._mousepos-shapesCount*this.model.spaceBetweenShapes)*this.model.incrementStep/this.model.shapeHeight).toFixed(1)},_horizontalMouseMoveOrUpProperties:function(){shapesCount=Math.floor(this._mousepos-1)/(this.model.shapeWidth+this.model.spaceBetweenShapes);this._value=((this._mousepos-shapesCount*this.model.spaceBetweenShapes)*this.model.incrementStep/this.model.shapeWidth).toFixed(1)},_canvasMouseUpHandler:function(n){ej.blockDefaultActions(n);ej.isTouchDevice()&&(n=n.changedTouches?n.changedTouches[0]:n);ej.listenTouchEvent(this._canvasElement,ej.moveEvent(),this._canvasMMDelegate,!0);this._canvasAction="mouseUp";this._getCursorPosition(n);this._mouseUp(n)},_documentEndHandler:function(n){$(n.target).closest(".e-m-rating").length==0&&(ej.isTouchDevice()&&(n=n.touches?n.touches[0]:n),ej.listenTouchEvent(this._canvasElement,ej.moveEvent(),this._canvasMMDelegate,!0))},_mouseUp:function(n){var r,u,t,i;if(this.model.precision=="exact"&&this._move){this.model.change&&this._trigger("change",i);return}this._move=!1;r=this._shapesCount;u=0;this._mousepos=this.model.orientation=="vertical"?this._mousePosition.y:this._mousePosition.x;this._getCursorPosition(n);var f=this.model.shapeWidth,e=this.model.shapeHeight;this["_"+this.model.orientation+"MouseMoveOrUpProperties"]();t=this._getRoundOffValue(this._value);this.value(t);this._currentMouseMove=t;this._canvasContext.canvas.title=this.model.minValue+parseFloat(t);this._canvasContext.canvas.alt=this.model.minValue+parseFloat(t);$(this._hiddenField).val(this._canvasContext.canvas.title);this._renderRatingControl();i={value:this.value()};this.model.change&&this._trigger("change",i)},_renderRatingControl:function(){var t=this._canvasElement,n,i;(typeof G_vmlCanvasManager!="undefined"&&(t=window.G_vmlCanvasManager.initElement(t)),t&&t.getContext)&&this._canvasElement.getContext&&(this._canvasContext=this._canvasElement.getContext("2d"),n=this._canvasContext,this._shapesCount=Math.floor((this.model.maxValue-this.model.minValue)/this.model.incrementStep),this["_"+this.model.orientation+"RenderRatingProperties"](),this._canvasWidth=this.tempCanvasWidth,this._canvasHeight=this.tempCanvasHeight,n.canvas.width=this._canvasWidth,n.canvas.height=this._canvasHeight,i=n.createLinearGradient(0,0,this._canvasWidth,this._canvasHeight),this._backgroundColor.ColorInfo[0].ColorStop.toString()!="NaN"&&$.each(this._backgroundColor.ColorInfo,function(n,t,r){i.addColorStop(r.ColorStop.toString()=="NaN"?0:r.ColorStop,"#"+(r.Color.Name?ej.hexFromRGB(r.Color):r.Color))}),this.model.orientation=="horizontal"?n.translate(7,15):n.translate(15,7),n.fillStyle=i,n.fillRect(0,0,n.canvas.width,n.canvas.height),this._renderRatingShapes(n))},_verticalRenderRatingProperties:function(){this._renderRatingProperties(this.model.shapeWidth,this.model.shapeHeight,this._canvasWidth,this._canvasHeight)},_horizontalRenderRatingProperties:function(){this._renderRatingProperties(this.model.shapeHeight,this.model.shapeWidth,this._canvasHeight,this._canvasWidth)},_renderRatingProperties:function(n,t,i,r){i=n+3+30;r=this._shapesCount*t;r+=this._shapesCount*this.model.spaceBetweenShapes;this.tempCanvasWidth=this.model.orientation=="horizontal"?r:i;this.tempCanvasHeight=this.model.orientation=="horizontal"?i:r},_renderRatingShapes:function(n){var e=this._shapesCount,r,i=this.value(),u,t,f;for(r=e,st=0,u=Math.floor(i/this.model.incrementStep),t=i%this.model.incrementStep,i=this.value(),st=st;st<r;st++)n.save(),this._drawRatingShape(n,st),st<u?(this._notClipped=!0,this._setClippingRegion(n,st,this.model.shapeWidth,!0)):t!=0?(this._notClipped=!0,f=t*this.model.shapeWidth/this.model.incrementStep,this._setClippingRegion(n,st,f,!0),t=0):this._setClippingRegion(n,st,this.model.shapeWidth,!1),n.restore()},_drawRatingShape:function(n,t){this._drawShapeProperties(t);this._drawShape(n,this.x1,this.y1,this.width,this.height)},_drawShapeProperties:function(n){this.width=this.model.shapeWidth;this.height=this.model.shapeHeight;this.x1;this.y1;this.model.orientation=="vertical"?(this.x1=1,this.y1=n*this.height+n*this.model.spaceBetweenShapes):(this.x1=n*this.width+n*this.model.spaceBetweenShapes,this.y1=0)},_drawShape:function(n,t,i,r,u){n.beginPath();switch(this.model.shape){case"heart":this._drawHeartenShape(n,t,i,r,u);break;case"pentagon":this._drawPentagonShape(n,t,i,r,u);break;case"diamond":this._drawDiamondShape(n,t,i,r,u);break;case"triangle":this._drawTriangleShape(n,t,i,r,u);break;case"square":this._drawSquareShape(n,t,i,r,u);break;case"circle":this._drawCircleShape(n,t,i,r,u);break;default:this._drawStarShape(n,t,i,r,u)}n.closePath();this.model.borderColor?(n.strokeStyle=this.model.borderColor,n.stroke()):this.model.renderMode!="windows"&&(n.strokeStyle=this.model.renderMode=="ios7"?this._generateGradient(n,this._backgroundColor,t,i):"",n.stroke());n.save();n.fillStyle=this._generateGradient(n,this._shapeBackgroundColor,t,i);n.fill();n.restore()},_drawStarShape:function(n,t,i,r,u){n.moveTo(t+r/6,i+u);n.lineTo(t+r,i+u/3);n.lineTo(t,i+u/3);n.lineTo(t+r-r/6,i+u);n.lineTo(t+r/2,i);n.lineWidth=this.model.renderMode=="android"?2:1},_drawCircleShape:function(n,t,i,r,u){this._draw?(n.arc(t+r/2,i+u/2,u/9,0,Math.PI*2,!0),this._draw=!1):n.arc(t+r/2,i+u/2,u/2,0,Math.PI*2,!0)},_drawSquareShape:function(n,t,i,r,u){n.moveTo(t,i);n.lineTo(t+r,i);n.lineTo(t+r,i+u);n.lineTo(t,i+u)},_drawTriangleShape:function(n,t,i,r,u){n.moveTo(t+r/2,i);n.lineTo(t+r,i+u);n.lineTo(t,i+u)},_drawDiamondShape:function(n,t,i,r,u){n.moveTo(t+r/2,i);n.lineTo(t+r,i+u/2);n.lineTo(t+r/2,i+u);n.lineTo(t,i+u/2)},_drawPentagonShape:function(n,t,i,r,u){n.moveTo(t+r/2,i);n.lineTo(t+r,i+u/2);n.lineTo(t+r-r/4,i+u);n.lineTo(t+r/4,i+u);n.lineTo(t,i+u/2)},_drawHeartenShape:function(n,t,i,r,u){t+=r/2;i+=u/4;u-=u*.25;n.beginPath();n.moveTo(t,i);n.bezierCurveTo(t,i-u*.4,t-r/2,i-u*.4,t-r/2,i);n.bezierCurveTo(t-r/2,i+u/2,t,i+u/2,t,i+u);n.bezierCurveTo(t,i+u/2,t+r/2,i+u/2,t+r/2,i);n.bezierCurveTo(t+r/2,i-u*.4,t,i-u*.4,t,i)},_setClippingRegion:function(n,t,i,r){if(n.save(),this._drawShapeProperties(t),n.clip(),n.beginPath(),this._canvasAction=="mouseMove"){var u,f;this._mouseMoveValue!=null?(u=this._mouseMoveValue*this.width+Math.floor(this._mouseMoveValue)*this.model.spaceBetweenShapes-7,f=this._mouseMoveValue*this.height+Math.floor(this._mouseMoveValue)*this.model.spaceBetweenShapes-7):(u=this._mousePosition.x-7,f=this._mousePosition.y-7);n.fillStyle=this._generateGradient(n,this._shapeHoverColor,this.x1,this.y1);n.fill();this.model.orientation=="vertical"?n.fillRect(0,0,this.width,f):n.fillRect(0,0,u,this.height)}else r&&(n.fillStyle=this._generateGradient(n,this._shapeSelectedColor,this.x1,this.y1),n.fill(),r=!1),this.model.orientation=="vertical"?n.fillRect(this.x1,this.y1,this.width,i):n.fillRect(this.x1,this.y1,i,this.height);n.restore()},_generateGradient:function(n,t,i,r){var u=this.model.renderMode=="ios7"?n.createLinearGradient(i+this.model.shapeWidth/2,r,i+this.model.shapeWidth/2,r+this.model.shapeHeight):n.createLinearGradient(i+this.model.shapeWidth/2,r+this.model.shapeHeight,i+this.model.shapeWidth/2,r);return $.each(t.ColorInfo,function(n,t){u.addColorStop(t.ColorStop.toString()=="NaN"?0:t.ColorStop,"#"+(t.Color.Name?ej.hexFromRGB(t.Color):t.Color))}),u},_clearRatingControl:function(n){n.clearRect(0,0,this._canvasWidth,this._canvasHeight)},_getCursorPosition:function(n){var t,i;return n.pageX||n.pageY?(t=n.pageX,i=n.pageY):(t=n.clientX+document.body.scrollLeft+document.documentElement.scrollLeft,i=n.clientY+document.body.scrollTop+document.documentElement.scrollTop),t-=$(this._canvasElement).offset().left,i-=$(this._canvasElement).offset().top,this._mousePosition={x:t,y:i},this._mousePosition},_getRoundOffValue:function(n){var t;return this.model.precision=="full"?(t=Math.round(n),t=t-t%this.model.incrementStep):this.model.precision=="exact"?t=n:(n=Math.round(n/.5)*.5,n=n-n%(this.model.incrementStep/2),t=n),t},_setRenderMode:function(n){this.model.renderMode=n;this._refresh()},_changeShape:function(n){this.model.shape=n;this._renderRatingControl()},_refresh:function(){this._clearElement();this.element.addClass("e-m-rating");this._renderControl();this._wireEvents(!1)},_setModel:function(n){for(var t in n)switch(t){case"renderMode":this._setRenderMode(n.renderMode);break;case"shape":this._changeShape(n.shape);break;case"shapeWidth":this._refresh();break;case"shapeHeight":this._refresh();break;case"incrementStep":this._refresh();break;case"orientation":this._refresh();break;case"precision":this._refresh();break;case"value":this._refresh();break;case"maxValue":this._refresh();break;case"enabled":this._refresh();break;case"readOnly":this._refresh();break;case"backgroundColor":this._refresh();break;case"selectionColor":this._refresh();break;case"hoverColor":this._refresh()}},_clearElement:function(){this._wireEvents(!0);this.element.removeAttr("class");this.element.removeAttr("style");this.element.html("")},_destroy:function(){this._cloneElement.insertBefore(this.element).removeClass("e-m-rating");this.element.remove()},disable:function(){this.model.enabled=!1;this._refresh()},enable:function(){this.model.enabled=!0;this._refresh()},show:function(){if(!this.model.enabled)return!1;this.element.css("display","block")},hide:function(){if(!this.model.enabled)return!1;this.element.css("display","none")},getValue:function(){return this.value()},reset:function(){this.value(0);this._refresh()},setValue:function(n){this.value(n);this._refresh()}});ej.mobile.Rating.Precision={Full:"full",Exact:"exact",Half:"half"};ej.mobile.Rating.Shape={Star:"star",Circle:"circle",Diamond:"diamond",Heart:"heart",Pentagon:"pentagon",Square:"square",Triangle:"triangle"};ej.mobile.Rating.Orientation={Horizontal:"horizontal",Vertical:"vertical"}})(jQuery,Syncfusion)});
