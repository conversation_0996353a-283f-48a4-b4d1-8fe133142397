/*!
*  filename: ej.rating.min.js
*  version : *********
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./ej.tooltip.min"],n):n()})(function(){(function(n,t){t.widget("ejRating","ej.Rating",{_rootCSS:"e-rating",element:null,model:null,validTags:["input"],_addToPersist:["value"],_setFirst:!1,angular:{require:["?ngModel","^?form","^?ngModelOptions"]},defaults:{maxValue:5,minValue:0,value:1,allowReset:!0,shapeWidth:23,shapeHeight:23,orientation:"horizontal",incrementStep:1,readOnly:!1,htmlAttributes:{},enabled:!0,showTooltip:!0,precision:"full",cssClass:"",width:null,height:null,enablePersistence:!1,create:null,click:null,mouseover:null,mouseout:null,mousemove:null,change:null,destroy:null},dataTypes:{maxValue:"number",minValue:"number",allowReset:"boolean",shapeWidth:"number",shapeHeight:"number",orientation:"enum",incrementStep:"number",readOnly:"boolean",precision:"enum",enabled:"boolean",htmlAttributes:"data"},observables:["value"],value:t.util.valueFunction("value"),_setModel:function(n){for(var i in n)switch(i){case"allowReset":n[i]?this._showResetButton():this._hideResetButton();break;case"value":this.setValue(t.util.getVal(n[i]));n[i]=this.model.value;break;case"enabled":this._enabledAction(n[i]);break;case"cssClass":this._changeSkin(n[i]);break;case"height":this._mainDiv.height(n[i]);break;case"width":this._mainDiv.width(n[i]);break;case"readOnly":this.model.readOnly=n[i];n[i]?(this._unWireEvents(),this._on(this._mainDiv.find("li"),"mouseleave touchend",this._MouseOutHandler)):this.refresh();break;case"orientation":this.model.orientation=n[i];this.refresh();break;case"maxValue":this.model.maxValue=n[i];this.refresh();break;case"minValue":this.model.minValue=n[i];this.refresh();break;case"incrementStep":this.model.incrementStep=n[i];this.refresh();break;case"shapeWidth":this.model.shapeWidth=n[i];this.refresh();break;case"shapeHeight":this.model.shapeHeight=n[i];this.refresh();break;case"htmlAttributes":this._addAttr(n[i])}},_destroy:function(){this.element.show();this.element.val(this.element.attr("value"));this._unWireEvents();this._mainDiv.remove()},_init:function(){this._initialize()},_initialize:function(){this.element.hide();this._mainDiv=t.buildTag("div.e-rating e-widget "+this.model.cssClass,"",{},{tabindex:"0",role:"group","aria-label":"Rating"});t.isNullOrUndefined(this.model.width)||this._mainDiv.width(this.model.width);t.isNullOrUndefined(this.model.height)||this._mainDiv.height(this.model.height);this._mainDiv.insertBefore(this.element);this.model.orientation==t.Orientation.Horizontal?this._mainDiv.addClass("e-horizontal"):this._mainDiv.addClass("e-vertical");this.model.allowReset&&!this.model.readOnly&&this._createReset();this._validation();this.value()==1&&this.element[0].value!=""&&this.value(this.element[0].value);this._renderShape();this._shapes=this._mainDiv.find("div.e-shape");this._wireEvents();this._CurrentIndex=0;this._initCurrentValue();this.element.val(this.value());this._enabledAction(this.model.enabled);this._checkNameAttr();this._addAttr(this.model.htmlAttributes);this.model.showTooltip&&this._renderTooltip()},_checkNameAttr:function(){this.element.attr("name")||this.element.attr({name:this.element[0].id})},_addAttr:function(i){var r=this;n.map(i,function(n,i){var u=i.toLowerCase();u=="class"?r._mainDiv.addClass(n):u=="readonly"?r.model.readOnly=n:u=="disabled"&&n=="disabled"?r._enabledAction(!1):u=="style"?r._mainDiv.attr(i,n):t.isValidAttr(r.element[0],i)?r.element.attr(i,n):r._mainDiv.attr(i,n)})},_changeSkin:function(n){this.model.cssClass!=n&&this._mainDiv.removeClass(this.model.cssClass).addClass(n)},_enabledAction:function(n){n?(this._mainDiv.removeClass("e-disable"),this.element.removeAttr("disabled")):(this._mainDiv.addClass("e-disable"),this.element.attr("disabled","disabled"))},_validation:function(){this.model.incrementStep<1?this.model.incrementStep=1:this.model.incrementStep>this.model.maxValue&&(this.model.incrementStep=this.model.maxValue)},_createReset:function(){this._mainDiv.find("div.e-reset").length<=0?t.buildTag("div.e-reset","",{width:this.model.shapeWidth+"px",height:this.model.shapeHeight,title:"Reset"},{role:"button","aria-label":"reset","aria-hidden":!1}).appendTo(this._mainDiv):this._mainDiv.find("div.e-reset").show()},_renderShape:function(){var n=Math.round(this.model.maxValue/this.model.incrementStep),o=Math.round(this.model.minValue/this.model.incrementStep),i,r,u,f,e;if(this.model.orientation==t.Rating.Orientation.Horizontal?(i=(n+1)*this.model.shapeWidth,r=this.model.shapeHeight):this.model.orientation==t.Rating.Orientation.Vertical&&(i=this.model.shapeWidth,r=this.model.shapeHeight*n),n>0){for(u=t.buildTag("ul.e-ul","",{width:i+"px",height:r},{}),f=o;f<n;f++)e=t.buildTag("li.e-shape-list","",{width:this.model.shapeWidth+"px",height:this.model.shapeHeight},{tabindex:-1,"aria-describedby":this.element.prop("id")+"_tooltip"}),t.buildTag("div.e-shape inactive","",{width:this.model.shapeWidth+"px",height:this.model.shapeHeight}).appendTo(e),e.appendTo(u);u.appendTo(this._mainDiv)}},_initCurrentValue:function(){var r,i;if(t.isNullOrUndefined(this.value())||this.value()==""||this.value()==0)this._CurrentIndex=0;else if(this.value()>this.model.minValue)for(r=this.value()/this.model.incrementStep-this.model.minValue/this.model.incrementStep,this._CurrentIndex=r,this._valueRefresh(this._CurrentIndex),i=0;i<r;i++)if(this._shapes[i])n(this._shapes[i]).removeClass("inactive").removeClass("active").addClass("selected");else{this._CurrentIndex=i;break}this.value(this._CurrentIndex*this.model.incrementStep+this.model.minValue)},_valueRefresh:function(i){var r,f,u;if(i=Math.ceil(i),r=(this.value()/this.model.incrementStep).toFixed(1),this.model.orientation==t.Rating.Orientation.Horizontal){for(f=this.model.precision=="exact"?(r%1).toFixed(1):this.model.precision=="half"?r%1>.5?1:.5:1,r=((r%1).toFixed(1)!=0?f:r/this._CurrentIndex)*this.model.shapeWidth,u=0;u<i;u++)n(this._shapes[u]).css({width:this.model.shapeWidth+"px"});this._shapes!=null&&n(this._shapes[i-1]).css({width:r+"px"})}else{for(r=((r%1).toFixed(1)!=0?(r%1).toFixed(1):r/this._CurrentIndex)*this.model.shapeHeight,u=0;u<i;u++)n(this._shapes[u]).css({height:this.model.shapeHeight+"px"});this._shapes!=null&&n(this._shapes[i-1]).css({height:r+"px"})}},_reset:function(){if(!this.model.enabled)return!1;n(this._shapes).removeClass("active").removeClass("selected").addClass("inactive");this._prevValue=this.value();this.value(0);this._CurrentIndex=0;this._currValue=this.value();this.element.value=this._currValue;this._valueChanged()},_fillExactPrecision:function(i,r){var e=this._shapes.index(i)+this.model.minValue/this.model.incrementStep+1,f=this.model,u;if(f.orientation==t.Rating.Orientation.Horizontal){for(u=0;u<this._shapes.length;u++)u<e?n(this._shapes[u]).css({width:f.shapeWidth+"px"}):n(this._shapes[u]).css({width:"0px"});n(i).css({width:r+"px"});this.toolTipValue=((e-1)*f.incrementStep+r/f.shapeWidth*f.incrementStep).toFixed(1)}else{for(u=0;u<e;u++)n(this._shapes[u]).css({height:f.shapeHeight+"px"});for(u=e;u<this._shapes.length;u++)n(this._shapes[u]).css({height:"0px"});n(i).css({height:r+"px"});this.toolTipValue=((e-1)*f.incrementStep+r/f.shapeHeight*f.incrementStep).toFixed(1)}},_fillHalfPrecision:function(i,r){var e=this._shapes.index(i)+this.model.minValue/this.model.incrementStep+1,u=this.model,f;if(u.orientation==t.Rating.Orientation.Horizontal){for(f=0;f<this._shapes.length;f++)f<e?n(this._shapes[f]).css({width:u.shapeWidth+"px"}):n(this._shapes[f]).css({width:"0px"});r<this.model.shapeWidth/2?(r=u.shapeWidth/2,n(i).css({width:r+"px"}),this.toolTipValue=((e-1)*u.incrementStep+r/u.shapeWidth*u.incrementStep).toFixed(1)):(r=u.shapeWidth,n(i).css({width:r+"px"}),this.toolTipValue=((e-1)*u.incrementStep+r/u.shapeWidth*u.incrementStep).toFixed(1))}else{for(f=0;f<this._shapes.length;f++)f<e?n(this._shapes[f]).css({height:this._starHeight+"px"}):n(this._shapes[f]).css({height:"0px"});r<=u.shapeHeight/2?(r=u.shapeHeight/2,n(i).css({height:r+"px"}),this.toolTipValue=((e-1)*u.incrementStep+r/u.shapeHeight*u.incrementStep).toFixed(1)):(r=u.shapeHeight,n(i).css({height:r+"px"}),this.toolTipValue=((e-1)*u.incrementStep+r/u.shapeHeight*u.incrementStep).toFixed(1))}},_fillFullPrecision:function(i){var f=this._shapes.index(i)+this.model.minValue/this.model.incrementStep+1,u=this.model,r;if(u.orientation==t.Rating.Orientation.Horizontal){for(r=0;r<this._shapes.length;r++)r<f?n(this._shapes[r]).css({width:u.shapeWidth+"px"}):n(this._shapes[r]).css({width:"0px"});n(i).css({width:u.shapeWidth+"px"});this.toolTipValue=(this.model.minValue+(this._shapes.index(i)+1))*this.model.incrementStep}else{for(r=0;r<this._shapes.length;r++)r<f?n(this._shapes[r]).css({height:u.shapeHeight+"px"}):n(this._shapes[r]).css({height:"0px"});n(i).css({height:u.shapeHeight+"px"});this.toolTipValue=(this.model.minValue+(this._shapes.index(i)+1))*this.model.incrementStep}},_ClickHandler:function(i){var u,r,f,e;if(this.model.enabled){if(i.target.tagName=="LI"?u=i.target.firstChild:i.target.parentNode.tagName=="LI"&&(u=i.target),u){for(r=this._shapes.index(u)+1,f=0;f<r;f++)this._shapes[f]&&n(this._shapes[f]).removeClass("inactive").removeClass("active").addClass("selected");this._prevValue=this.value();this._CurrentIndex=r;r=r+this.model.minValue/this.model.incrementStep;e=this.model.incrementStep;this._currValue=this.model.orientation==t.Rating.Orientation.Horizontal?((r-1)*e+u.clientWidth/this.model.shapeWidth*e).toFixed(1):((r-1)*e+u.clientHeight/this.model.shapeHeight*e).toFixed(1);this.element.value=this._currValue;this.value(parseFloat(this._currValue));this._trigger("click",{event:i,value:this.value(),prevValue:this._prevValue});this._valueChanged()}n(i.target).hasClass("e-reset")&&this._reset()}},_MouseOverHandler:function(t){var u,r,f,e,i;if(this.model.enabled){if(r=t.target,t.type=="touchmove"&&(f=t.originalEvent.changedTouches[0],r=document.elementFromPoint(f.pageX,f.pageY)),r.tagName=="LI")u=r.firstChild;else if(r.parentNode.tagName=="LI")u=r;else return!1;for(e=this._shapes.index(u)+1,i=0;i<=this._shapes.length;i++)this._shapes[i]&&(i<e?n(this._shapes[i]).removeClass("inactive").removeClass("selected").addClass("active"):n(this._shapes[i]).removeClass("active").removeClass("selected").addClass("inactive"));this._trigger("mouseover",{event:t,index:e,value:this.value()})}},_MouseOutHandler:function(t){var r,i;if(!this.model.readOnly&&this.model.enabled){if(r=this._CurrentIndex,r!=0)for(i=0;i<this._shapes.length;i++)this._shapes[i]&&(i<r?n(this._shapes[i]).removeClass("active").removeClass("inactive").addClass("selected"):n(this._shapes[i]).removeClass("active").addClass("inactive"));else n(this._shapes).removeClass("active").addClass("inactive");this._valueRefresh(r);this._trigger("mouseout",{event:t,value:this.value()})}},_MouseMoveHandler:function(i){var u,f,r,e,o;if(!this.model.readOnly&&this.model.enabled){if(r=i.target,i.type=="touchmove"&&(e=i.originalEvent.changedTouches[0],r=document.elementFromPoint(e.pageX,e.pageY)),r.tagName=="LI")u=r.firstChild;else if(r.parentNode.tagName=="LI")u=r;else return!1;f=this.model.orientation==t.Rating.Orientation.Horizontal?t.isNullOrUndefined(i.offsetX)?i.pageX-n(r).offset().left:i.offsetX+1:t.isNullOrUndefined(i.offsetY)?i.pageY-n(r).offset().top:i.offsetY+1;this.model.precision==t.Rating.Precision.Exact?this._fillExactPrecision(u,f):this.model.precision==t.Rating.Precision.Half?this._fillHalfPrecision(u,Math.round(f)):this._fillFullPrecision(u)}else this.toolTipValue=(this.value()==null||this.value()==""?0:this.value())+" / "+this.model.maxValue;n(i.target).attr("aria-label",this.toolTipValue);this.model.showTooltip&&parseFloat(this.model.maxValue)>=parseFloat(this.toolTipValue)&&(o=n(this._mainDiv).data("ejTooltip"),o._setModel({content:this.toolTipValue}));this._trigger("mousemove",{event:i,value:this.toolTipValue})},_renderTooltip:function(){var u=this,i,r;this.model.showTooltip&&(i={target:{horizontal:"center",vertical:"top"},stem:{horizontal:"center",vertical:"bottom"}},this.model.orientation==t.Rating.Orientation.Vertical&&(i={target:{horizontal:"right",vertical:"center"},stem:{horizontal:"left",vertical:"center"}}),r=n(this._mainDiv).ejTooltip({target:".e-shape-list",content:this.value(),isBalloon:!1,collision:"flip",position:i,showRoundedCorner:!0,cssClass:this.model.cssClass,beforeOpen:function(n){u._updateTooltipValue(n)}}).data("ejTooltip"),n(r.tooltip).css({"min-width":"auto"}))},_updateTooltipValue:function(t){var i=n(this._mainDiv).data("ejTooltip");i._setModel({content:this.toolTipValue});i.show(t.event.target);t.cancel=!0},_wireEvents:function(){this.model.readOnly||(this._on(this._mainDiv,"mousedown",this._ClickHandler),this._on(this._mainDiv.find("li"),"mouseenter touchmove",this._MouseOverHandler));this._on(this._mainDiv.find("li"),"mouseleave touchend",this._MouseOutHandler);this._on(this._mainDiv.find("li"),t.eventType.mouseMove,this._MouseMoveHandler)},_unWireEvents:function(){this._mainDiv.find("li").off("mouseenter touchmove");this._mainDiv.find("li").off("mouseleave touchend");this._mainDiv.off("mousedown");this.model.precision!==t.Rating.Precision.Full&&this._mainDiv.find("li").off(t.eventType.mouseMove)},_valueChanged:function(){this.element.val(this.value());this._prevValue!=this._currValue&&this._trigger("change",{value:this.value(),prevValue:this._prevValue})},reset:function(){this._reset()},show:function(){if(!this.model.enabled)return!1;this._mainDiv.css("visibility","visible")},hide:function(){if(!this.model.enabled)return!1;this._mainDiv.css("visibility","hidden")},_showResetButton:function(){if(!this.model.enabled)return!1;this._createReset()},_hideResetButton:function(){if(!this.model.enabled)return!1;this._mainDiv.find("div.e-reset").hide()},getValue:function(){return this.value()==null||this.value()===""?"":this.value()},setValue:function(t){t!=null&&t!="null"&&(this.model.maxValue<t?t=this.model.maxValue:this.model.minValue>t&&(t=this.model.minValue),this.value(t),n(this._shapes).removeClass("selected").addClass("inactive"),this._initCurrentValue(),this.element.val(this.value()))},refresh:function(){this._destroy();this._unWireEvents();this._initialize()}});t.Rating.Precision={Full:"full",Half:"half",Exact:"exact"};t.Rating.Orientation={Horizontal:"horizontal",Vertical:"vertical"}})(jQuery,Syncfusion)});
