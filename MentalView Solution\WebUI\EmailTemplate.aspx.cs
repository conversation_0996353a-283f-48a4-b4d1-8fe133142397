﻿using Data;
using Newtonsoft.Json;
//using Syncfusion.JavaScript.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using static System.Net.WebRequestMethods;

namespace WebUI
{
    public partial class EmailTemplate : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = null;

                ((Main)this.Master).ServerMessage.ButtonClicked += new ButtonClickedHandler(this.ServerMessageButtonClicked);
                ((Main)this.Master).PageTitle = GetLocalResourceObject("Page.Title").ToString();
                ScriptManager.RegisterStartupScript(this, this.GetType(), "temp", "<script language='javascript'>Initialization();</script>", false);

                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                Int64 loggedEmailTemplateId = Convert.ToInt64(userData["UserId"]);
                string roleName = userData["Role"].ToString();

                if (roleName == "Guest")
                {
                    Response.StatusCode = 404;
                    Response.End();
                }

                if (this.emailTemplateCategoryIdDDL.Items.Count == 0)
                {
                    MentalViewDataSet emailTemplateCategoriesDS = (Data.MentalViewDataSet)Data.Business.EmailTemplatesBusiness.GetEmailTemplateCategoriesByTenantId(tenantId);

                    DataRow emptyRow = emailTemplateCategoriesDS.EmailTemplateCategories.NewRow();
                    emptyRow["CategoryName"] = "\xA0";
                    emptyRow["EmailTemplateCategoryId"] = -1;
                    emptyRow["TenantId"] = -1;
                    emailTemplateCategoriesDS.EmailTemplateCategories.Rows.InsertAt(emptyRow, 0);
                    this.emailTemplateCategoryIdDDL.DataSource = emailTemplateCategoriesDS.EmailTemplateCategories;
                    this.emailTemplateCategoryIdDDL.DataBind();
                }

                if (!IsPostBack)
                {
                    //Αν εμφανίζουμε ένα υπάρχον EmailTemplate με βάση το Title του EmailTemplate.
                    if (string.IsNullOrEmpty(this.Request.Params["EmailTemplateTitle"]) == false)
                    {
                        string emailTemplateTitle = this.Request.Params["EmailTemplateTitle"].ToString();
                        ds = (Data.MentalViewDataSet)Data.Business.EmailTemplatesBusiness.GetEmailTemplateByTitle(tenantId, emailTemplateTitle);
                        if (ds.EmailTemplates.Count == 0)
                        {
                            ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("EmailTemplateNotFoundMessage").ToString(), ServerMessageButtons.Ok);
                            return;
                        }
                        else
                        {
                            //ViewState["EmailTemplate"] = ds;
                            Response.Redirect("EmailTemplate?EmailTemplateId=" + ds.EmailTemplates[0].EmailTemplateId.ToString());
                        }
                    }

                    //Αν δημιουργούμε νέο EmailTemplate
                    if (string.IsNullOrEmpty(this.Request.Params["EmailTemplateId"]))
                    {
                        ds = new Data.MentalViewDataSet();
                        ds.EnforceConstraints = false;
                        Data.Business.ConfigureDataSet(ref ds);

                        //Δημιουργεί το νεο EmailTemplate
                        MentalViewDataSet.EmailTemplatesRow emailTemplatesRow = ds.EmailTemplates.NewEmailTemplatesRow();

                        emailTemplatesRow.TenantId = tenantId;
                        emailTemplatesRow.Subject = "";
                        emailTemplatesRow.Title = "";
                        emailTemplatesRow.Body = "";
                        emailTemplatesRow.RowIndex = 1000;  //Βάζουμε ένα πολύ μεγάλο για να παει στο τέλος.
                        ds.EmailTemplates.AddEmailTemplatesRow(emailTemplatesRow);

                        ViewState["EmailTemplate"] = ds;
                    }
                    else  //Αν κάνουμε επεξεργασία
                    {
                        int emailTemplateId = int.Parse(this.Request.Params["EmailTemplateId"]);

                        ds = (Data.MentalViewDataSet)Data.Business.EmailTemplatesBusiness.GetEmailTemplateById(emailTemplateId);

                        ViewState["EmailTemplate"] = ds;
                    }

                    //Βρίσκει τα στοιχεία του EmailTemplate για το emailTemplate
                    //MentalViewDataSet emailTemplateDS = Data.Business.AdministrationBusiness.GetEmailTemplateByEmailTemplateId(ds.EmailTemplates[0].EmailTemplateId);
                    //ViewState["EmailTemplateEmailTemplateFullName"] = emailTemplateDS.EmailTemplates[0].FullName;

                    this.SetDataOnUIControls(ds);
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void SetDataOnUIControls(Data.MentalViewDataSet ds)
        {
            this.titleTxtBox.Value = ds.EmailTemplates[0].Title;
            this.subjectTxtBox.Value = ds.EmailTemplates[0].Subject;
            this.bodyHiddenFld.Value = ds.EmailTemplates[0].Body;
            if (ds.EmailTemplates[0].IsEmailTemplateCategoryIdNull())
            {
                this.emailTemplateCategoryIdDDL.Value = "-1";
            }
            else
            {
                this.emailTemplateCategoryIdDDL.Value = ds.EmailTemplates[0].EmailTemplateCategoryId.ToString();
            }
        }

        private void GetDataFromUIControls(ref MentalViewDataSet ds)
        {
            try
            {
                ds.EmailTemplates[0].Title = this.titleTxtBox.Value;
                ds.EmailTemplates[0].Subject = this.subjectTxtBox.Value;
                ds.EmailTemplates[0].Body = this.bodyHiddenFld.Value.Replace("&lt;", "<");
                if (this.emailTemplateCategoryIdDDL.Value != "-1")
                {
                    ds.EmailTemplates[0].EmailTemplateCategoryId = int.Parse(this.emailTemplateCategoryIdDDL.Value);
                }
                else
                {
                    ds.EmailTemplates[0].SetEmailTemplateCategoryIdNull();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void saveCloseBtn_Click(object sender, EventArgs e)
        {
            try
            {
                this.Save(true);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void Save(bool close)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["EmailTemplate"];
                ds.EnforceConstraints = false;

                this.GetDataFromUIControls(ref ds);

                if (this.ValidateData(ds.EmailTemplates[0]) == true)
                {
                    Data.Business.SaveAllData(ds);
                    ViewState["EmailTemplate"] = ds;

                    if (close)
                    {
                        ViewState.Remove("EmailTemplate");
                        Response.Redirect(@"~\EmailTemplates.aspx");
                    }
                    else
                    {
                        this.SetDataOnUIControls(ds);
                    }
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void saveBtn_Click(object sender, EventArgs e)
        {
            try
            {
                this.Save(false);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void cancelBtn_Click(object sender, EventArgs e)
        {
            ViewState.Remove("EmailTemplate");
            Response.Redirect(@"EmailTemplates.aspx");
        }

        protected void deleteBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["EmailTemplate"];

                //if (ds.EmailTemplates[0].RowState != System.Data.DataRowState.Added)
                //{
                //Page.ClientScript.RegisterStartupScript(this.GetType(), "ReplaceCharactersInBody", "ReplaceCharactersInBody()", true);
                ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete");

                //Page.ClientScript.RegisterStartupScript(this.GetType(), "ReplaceCharactersInBody", "ReplaceCharactersInBody()", true);
                //}
                //else
                //{
                //    ViewState.Remove("EmailTemplate");
                //    Response.Redirect(@"EmailTemplates.aspx");
                //}

                this.GetDataFromUIControls(ref ds);
                ViewState["EmailTemplate"] = ds;
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void Delete()
        {
            try
            {
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["EmailTemplate"];

                ds.EmailTemplates[0].Delete();
                Data.Business.SaveAllData(ds);

                ViewState.Remove("EmailTemplate");
                Response.Redirect(@"EmailTemplates.aspx");
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void ServerMessageButtonClicked(object sender, ButtonClickedArgs args)
        {
            try
            {
                if (args.Action == "Delete")
                {
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        //Page.ClientScript.RegisterStartupScript(this.GetType(), "ReplaceCharactersInBody", "ReplaceCharactersInBody()", true);
                        this.Delete();
                    }
                }
                else if (args.Action == "SessionExpired")
                {
                    Response.Redirect("Default.aspx");
                }

                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["EmailTemplate"];
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private bool ValidateData(Data.MentalViewDataSet.EmailTemplatesRow emailTemplatesRow)
        {
            try
            {
                //Αν το πεδίο Title δεν είναι συμπληρωμένο
                if (emailTemplatesRow.Title == "")
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("TitleRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                //Αν το πεδίο Subject δεν είναι συμπληρωμένο
                if (emailTemplatesRow.Subject == "")
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("SubjectRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                if (Data.Business.EmailTemplatesBusiness.CheckEmailTemplateTitleExists(emailTemplatesRow.EmailTemplateId, emailTemplatesRow.Title) == true)
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("EmailTemplateTitleExistsMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                return true;
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }


        protected void importExcelUploadBox_Complete(object sender, Syncfusion.JavaScript.Web.UploadBoxCompleteEventArgs e)
        {
            int i = 0;
        }

        protected void importExcelFileBtn_Click(object sender, EventArgs e)
        {
            string rootDirectory = Server.MapPath("~");
            string outputFile = rootDirectory + "Output.html";
            Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["EmailTemplate"];
            ds.EnforceConstraints = false;
            this.GetDataFromUIControls(ref ds);

            if (fileUpload.HasFile)
            {
                try
                {
                    fileUpload.SaveAs(rootDirectory + fileUpload.FileName);

                    string fileDataPath = rootDirectory + fileUpload.FileName;
                    byte[] data = System.IO.File.ReadAllBytes(fileDataPath);
                    Dictionary<string, MemoryStream> diction = new Dictionary<string, MemoryStream>();
                    diction.Add("file", new MemoryStream(data));
                    WorksheetToHTMLService service = new WorksheetToHTMLService(diction);

                    string html = service.WorksheetToHTMLXlsIO("Worksheet", outputFile);
                    ds.EmailTemplates[0].Body = ds.EmailTemplates[0].Body + html;
                    service.Close();

                    SetDataOnUIControls(ds);
                }
                catch (Exception ex)
                {
                    Data.ExceptionLogger.LogException(ex);
                    ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
                }
                finally
                {
                    System.IO.File.Delete(outputFile);
                }
            }
            else
            {
                //lblmessage.Text = sb.ToString();
            }
        }


    }
}