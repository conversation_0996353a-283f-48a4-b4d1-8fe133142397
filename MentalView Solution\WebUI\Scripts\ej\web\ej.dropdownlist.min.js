/*!
*  filename: ej.dropdownlist.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.globalize.min","./../common/ej.data.min","./../common/ej.scroller.min","./../common/ej.draggable.min"],n):n()})(function(){(function(n,t,i){t.widget("ejDropDownList","ej.DropDownList",{element:null,model:null,validTags:["select","input"],_addToPersist:["value","text","selectedIndex","selectedItemIndex","selectedItems","selectedIndices","popupWidth","popupHeight","itemValue"],_setFirst:!1,_rootCSS:"e-dropdownlist",angular:{require:["?ngModel","^?form","^?ngModelOptions"]},_requiresID:!0,defaults:{cssClass:"",value:null,htmlAttributes:{},text:null,itemValue:"",itemsCount:0,dataSource:null,delimiterChar:",",query:null,fields:{id:null,text:null,value:null,category:null,groupBy:null,imageUrl:null,imageAttributes:null,spriteCssClass:null,htmlAttributes:null,selected:null,tableName:null},locale:"en-US",watermarkText:null,height:"",loadOnDemand:!1,width:"",popupHeight:"152px",popupWidth:"auto",popupTarget:"body",maxPopupHeight:null,minPopupHeight:"20",maxPopupWidth:null,minPopupWidth:"0",targetID:null,template:null,headerTemplate:null,selectedItemIndex:-1,selectedIndex:-1,disableItemsByIndex:null,enableItemsByIndex:null,selectedItems:[],selectedIndices:[],cascadeTo:null,enablePopupResize:!1,allowVirtualScrolling:!1,virtualScrollMode:"normal",showRoundedCorner:!1,showPopupOnLoad:!1,enableRTL:!1,enabled:!0,filterType:"contains",sortOrder:"ascending",caseSensitiveSearch:!1,showCheckbox:!1,checkAll:!1,uncheckAll:!1,enablePersistence:!1,enableFilterSearch:!1,enableServerFiltering:!1,enableIncrementalSearch:!0,incrementalSearchDelay:500,readOnly:!1,enableAnimation:!1,multiSelectMode:"none",allowGrouping:!1,enableSorting:!1,validationRules:null,validationMessage:null,actionBegin:null,actionComplete:null,actionFailure:null,actionSuccess:null,create:null,popupHide:null,popupShown:null,beforePopupShown:null,beforePopupHide:null,popupResizeStart:null,popupResize:null,popupResizeStop:null,change:null,select:null,dataBound:null,search:null,checkChange:null,cascade:null,destroy:null},dataTypes:{cssClass:"string",itemsCount:"number",watermarkText:"string",template:"string",disableItemsByIndex:"string",enableItemsByIndex:"string",enableIncrementalSearch:"boolean",incrementalSearchDelay:"number",cascadeTo:"string",delimiterChar:"string",showRoundedCorner:"boolean",showPopupOnLoad:"boolean",enableRTL:"boolean",enablePersistence:"boolean",allowVirtualScrolling:"boolean",virtualScrollMode:"enum",enabled:"boolean",readOnly:"boolean",multiSelectMode:"enum",dataSource:"data",query:"data",fields:"data",selectedItems:"array",selectedIndices:"array",enableAnimation:"boolean",allowGrouping:"boolean",enableSorting:"boolean",validationRules:"data",validationMessage:"data",htmlAttributes:"data",locale:"string"},observables:["value","selectedItemIndex","selectedIndex","dataSource"],value:t.util.valueFunction("value"),selectedItemIndex:t.util.valueFunction("selectedItemIndex"),selectedIndex:t.util.valueFunction("selectedIndex"),_dataSource:t.util.valueFunction("dataSource"),enable:function(){if(this._visibleInput.hasClass("e-disable")){this.target.disabled=!1;this.model.enabled=!0;this.model.multiSelectMode=="visualmode"&&this.wrapper.removeClass("e-disable");this.container.removeClass("e-disable");this._visibleInput.removeClass("e-disable");this.dropdownbutton.removeClass("e-disable");t.isNullOrUndefined(this.popupListWrapper)||this.popupListWrapper.removeClass("e-disable");this._isIE8&&this.drpbtnspan.removeClass("e-disable");this.container.on("mousedown",n.proxy(this._OnDropdownClick,this));this.model.multiSelectMode=="visualmode"&&this._ulBox&&this._ulBox.removeClass("e-disable")}this._wireEvents();this.wrapper.attr("tabindex",0)},disable:function(){this._visibleInput.hasClass("e-disable")||(this.target.disabled=!0,this.model.enabled=!1,this.model.multiSelectMode=="visualmode"&&this.wrapper.addClass("e-disable"),this.container.addClass("e-disable"),this._visibleInput.addClass("e-disable"),t.isNullOrUndefined(this.popupListWrapper)||this.popupListWrapper.addClass("e-disable"),this.dropdownbutton.addClass("e-disable"),this._isIE8&&this.drpbtnspan.addClass("e-disable"),this.model.multiSelectMode=="visualmode"&&this._ulBox&&this._ulBox.addClass("e-disable"),this.container.off("mousedown",n.proxy(this._OnDropdownClick,this)),this._unwireEvents(),this.wrapper.removeAttr("tabindex"),this._isPopupShown()&&this._hideResult())},getValue:function(){return this._visibleInput.val()},_setValue:function(n,i){t.isNullOrUndefined(n)||(this.model.loadOnDemand&&(t.isNullOrUndefined(this.ultag)||this.ultag.children().length==0)&&this._showFullList(),this.ultag.children().length!=0&&this._setDynamicSelectByVal(this,n,i))},_setDynamicSelectByVal:function(n,i,r){n._raiseEvents=!1;t.isNullOrUndefined(n.model.text)||n.unselectItemByText(n.model.text,r);n._raiseEvents=!0;n.model.allowVirtualScrolling&&n._addValue(i);n.selectItemByValue(i)},_addValue:function(n){var i,h,r,f,o,e,s,u;if(this.model.itemsCount>0&&n!=""&&(this._checkValue=!0,i=typeof n=="object"?n:typeof n=="number"?[n]:n.split(this.model.delimiterChar),t.isNullOrUndefined(this._mapFields())||this._mapFields(),h=this.mapFld._value,t.isNullOrUndefined(this._rawList)||this._addListItems(i,this._rawList,"local"),this._checkValue))if(r=this._dataSource(),t.DataManager&&r instanceof t.DataManager)if(r.dataSource.offline&&r.dataSource.json&&r.dataSource.json.length>0)this._addListItems(i,r.dataSource.json,"remote");else if(f=this,o=n,e=this.model.fields.value?this.model.fields.value:this.model.fields.text,this._isSingleSelect())r.executeQuery(t.Query().select(e,this.model.fields.text).where(e,"equal",o,!1)).done(function(n){f._addListItems(i,n.result,"remote");f.selectItemByValue(o)});else for(u=0;u<i.length;u++)r.executeQuery(t.Query().select(e,this.model.fields.text).where(e,"equal",i[u],!1)).done(function(n){s=n.result.slice(0,u);u==i.length&&(f._addListItems(i,s,"remote"),f.selectItemByValue(o))});else this._addListItems(i,r,"remote")},_addListItems:function(n,t,i){for(var r,f=this.mapFld._value==this.model.fields.value?this.mapFld._value:this.model.fields.text,u=0;u<n.length;u++)for(r=0;r<t.length;r++)i=="local"&&t[r][f]==n[u]&&(this._checkValue=!1),i=="remote"&&t[r][f]==n[u]&&this.addItem(t[r])},_setText:function(n){t.isNullOrUndefined(n)||(this.model.loadOnDemand&&(t.isNullOrUndefined(this.ultag)||this.ultag.children().length==0)&&this._showFullList(),this._raiseEvents=!1,this.unselectItemByText(this.model.text),this._raiseEvents=!0,this.selectItemByText(n))},_setItemValue:function(n){this.model.itemValue=n},_changeWatermark:function(n){if(!this.model.enabled)return!1;this._isWatermark?this._visibleInput.attr("placeholder",n):this._hiddenSpan.text(n)},hidePopup:function(){if(!this.model.enabled)return!1;this.ultag.find("li").length>0&&this._hideResult()},showPopup:function(){if(!this.model.enabled)return!1;var n=t.isNullOrUndefined(this.ultag)?this.model.loadOnDemand?!0:!1:this.ultag.find("li").length>0;n&&this._showResult()},clearText:function(){this._clearTextboxValue();this._isWatermark||this._setWatermark()},addItem:function(i){var u,r,f,o,e,s;if((this.model.loadOnDemand&&(t.isNullOrUndefined(this.ultag)||this.ultag.children().length==0)&&this._showFullList(),!this.model.enabled||!i)||(this._mapFields(),u=n.isArray(i)?i:[i],u.length<1))return!1;for(r=this.mapFld,f={_id:null,_imageUrl:null,_imageAttributes:null,_spriteCSS:null,_text:null,_value:null,_htmlAttributes:null,_selected:null,_category:null},f._id=u[0][r._id]?r._id:"id",f._imageUrl=u[0][r._imageUrl]?r._imageUrl:"imageUrl",f._imageAttributes=u[0][r._imageAttributes]?r._imageAttributes:"imageAttributes",f._spriteCSS=u[0][r._spriteCSS]?r._spriteCSS:"spriteCssClass",f._text=u[0][r._text]?r._text:"text",f._value=u[0][r._value]?r._value:"value",f._htmlAttributes=u[0][r._htmlAttributes]?r._htmlAttributes:"htmlAttributes",f._selected=u[0][r._selected]?r._selected:"selected",f._category=u[0][r._category]?r._category:"groupBy",this._generateLi(u,f),o=this.dummyUl,e=0;e<u.length;e++)this._listItem(u[e],"add");this.model.showCheckbox?this._appendCheckbox(o,!0):this._isSingleSelect()||this._multiItemSelection(o,!0);this._virtualUl.append(n(this.dummyUl).clone(!0));this.ultag.append(this.dummyUl);this._isPopupShown()&&(s=this.scrollerObj?this.scrollerObj.scrollTop():0,this._refreshScroller(),this.scrollerObj&&this.scrollerObj.option("scrollTop",s))},_toArray:function(i,r){var u;return typeof i=="function"&&(i=t.util.getVal(i)),n.isArray(i)?u=i:typeof i=="string"?r&&(this.model.multiSelectMode=="visualmode"||this.model.multiSelectMode=="delimiter"||this.model.showCheckbox)?(u=this._getUpdatedListData(i),u.length==0&&(u=[i])):r?u=[i]:(u=this._getUpdatedListData(i),u.length==0&&(u=[i])):u=[i],u},_trim:function(t){return typeof t=="string"?n.trim(t):t},selectItemByIndex:function(n){this._selectItemByIndex(n)},selectItemsByIndices:function(n){this._selectItemByIndex(n)},_selectItemByIndex:function(i){var e,r,f,u;if(this.model.loadOnDemand&&(t.isNullOrUndefined(this.ultag)||this.ultag.children().length==0)&&this._showFullList(),this.listitems=this._getLi(),this._selectedIndices=n.map(this._selectedIndices,function(n){return parseInt(n)}),this.model.selectedItems=this.model.selectedIndices=this._selectedIndices,parseInt(i)==-1&&this.model.selectedItems.length>0)this._clearTextboxValue(),this._trigger("change",{text:this._visibleInput[0].value,selectedText:"",selectedValue:"",value:""});else for(e=this._toArray(i,!0),f=0;f<e.length;f++)if(r=parseInt(e[f]),r!=null&&r>=0){if(n.inArray(r,this._selectedIndices)==-1)for(u=0;u<this.listitems.length;u++)n(this.listitems[u]).hasClass("e-disable")||u==r&&(this.selectedIndexValue=u,this._activeItem=r,this._enterTextBoxValue())}else!this.model.showCheckbox&&this.model.multiSelectMode=="none"&&this.model.selectedItems.length>0&&(this._clearTextboxValue(),this._trigger("change",{text:this._visibleInput[0].value,selectedText:"",selectedValue:"",value:""}))},unselectItemsByIndices:function(n){this._unselectItemByIndex(n)},unselectItemByIndex:function(n){this._unselectItemByIndex(n)},_unselectItemByIndex:function(i){var r,f,e,u;for(this.model.loadOnDemand&&(t.isNullOrUndefined(this.ultag)||this.ultag.children().length==0)&&this._showFullList(),this._selectedIndices=n.map(this._selectedIndices,function(n){return parseInt(n)}),this.model.selectedItems=this.model.selectedIndices=this._selectedIndices,f=this._toArray(i,!0),this.listitems=this._getLi(),u=0;u<f.length;u++)for(e=parseInt(f[u]),r=0;r<this.listitems.length;r++)r==e&&(this.selectedIndexValue=r,this._activeItem=e,this._activeItem==this._aselectedItem&&(this._aselectedItem=null),this._removeTextBoxValue())},setSelectedValue:function(n){this.selectItemByValue(n)},selectItemByValue:function(i){var r,f,u;if(this.model.loadOnDemand&&(t.isNullOrUndefined(this.ultag)||this.ultag.children().length==0)&&this._showFullList(),this.listitems=this._getLi(),this.inputSearch&&this.inputSearch.val()!=""&&this.model.enableServerFiltering&&!t.isNullOrUndefined(this._searchresult)){for(f=this.model.fields&&this.model.fields.value?this.model.fields.value:"value",r=0;r<this._searchresult.length;r++)for(u=0;u<this.listitems.length;u++)n(this.listitems[u]).attr("data-value")==this._searchresult[r][f]&&(this._searchresult=null);this.addItem(this._searchresult)}t.isNullOrUndefined(i)?this._clearTextboxValue():(this._unselectText&&(this._unselectText=!1),this._selectUnSelectValue(i,"selectValue"))},_selectUnSelectValue:function(i,r,u,f){for(var e,h,s=this._toArray(i,!0),o=0;o<s.length;o++)for(e=0;e<this.listitems.length;e++)if(n(this.listitems[e]).hasClass("e-disable")||r!="selectValue"&&r!="selectText"){if(this._getAttributeValue(this.listitems[e])&&(r=="unselectValue"&&(this._selectedValue=this._getAttributeValue(this.listitems[e]),this._selectedValue=this._selectedValue==s[o]),r=="unselectText"&&(this.unselectedTextValue=this._isPlainType(this.popupListItems)?this.popupListItems[e]:this._getField(this.popupListItems[e],this.mapFld._text),this._selectedValue=this.unselectedTextValue==s[o]||f&&this.unselectedTextValue.toString().toLowerCase()==s[o].toString().toLowerCase(),this._selectedValue&&this._boxValue&&this.model.showCheckbox&&this.model.enableFilterSearch&&(this._activeItem=e,this._selectedValue=this._boxValue.indexOf(n(this._getActiveItem(this._activeItem)).attr("data-value"))==-1?!1:this._selectedValue)),this._selectedValue)){this._activeItem=e;this._activeItem==this._aselectedItem&&(this._aselectedItem=null);(this._visibleInput.val()!=this._checkedValues||u&&u.type=="keyup"||this.model.enablePersistence)&&this._removeTextBoxValue();break}}else if(r=="selectValue"&&(h=t.isNullOrUndefined(this._getAttributeValue(this.listitems[e]))?n(this.listitems[e]).text():this._getAttributeValue(this.listitems[e]),this._selectedValue=h==s[o]),r=="selectText"&&(this.selectedTextValue=this._isPlainType(this.popupListItems)?this.popupListItems[e]:this._getField(this.popupListItems[e],this.mapFld._text),this._selectedValue=this.selectedTextValue==s[o]||f&&this.selectedTextValue.toString().toLowerCase()==s[o].toString().toLowerCase()),this._selectedValue){this._activeItem=e;this._aselectedItem=this._activeItem;this._enterTextBoxValue();break}},unselectItemByValue:function(n){this.model.loadOnDemand&&(t.isNullOrUndefined(this.ultag)||this.ultag.children().length==0)&&this._showFullList();this.listitems=this._getLi();this._selectUnSelectValue(n,"unselectValue")},setSelectedText:function(n){this.selectItemByText(n)},selectItemByText:function(n,i){this.model.loadOnDemand&&(t.isNullOrUndefined(this.ultag)||this.ultag.children().length==0)&&this._showFullList();this.listitems=this._getLi();this._mapFields();t.isNullOrUndefined(n)?this._clearTextboxValue():this._selectUnSelectValue(n,"selectText",i)},unselectItemByText:function(n,t,i){this.listitems=this._getLi();this._mapFields();this._unselectText=!0;this._selectUnSelectValue(n,"unselectText",t,i)},getSelectedValue:function(){return this.element.val()},getSelectedItem:function(){var n,t=[];for(this.listitems=this._getLi(),n=0;n<this._selectedIndices.length;n++)t.push(this.listitems[this._selectedIndices[n]]);return t},getItemDataByValue:function(n){var r=this._toArray(n,!1),u=t.isNullOrUndefined(this.resultList)?this._rawList:this._rawList.concat(this.resultList),i,f,o=[],e=this.model.fields&&this.model.fields.value?this.model.fields.value:"value",s;if(r.length>0&&(u.indexOf(r[0])>-1||u.indexOf(Number(r[0]))>-1))for(i=0;i<r.length&&(u.indexOf(r[i])>-1||u.indexOf(Number(r[i]))>-1);i++)o.push({text:r[i],value:r[i]});else for(u.length>0&&t.isNullOrUndefined(u[0][e])&&(s=this.model.fields&&this.model.fields.text?this.model.fields.text:"text",t.isNullOrUndefined(u[0][s])||(e=s)),i=0;i<r.length;i++)for(f=0;f<u.length;f++)(u[f][e]==r[i]||u[f][e]==Number(r[i]))&&o.push(u[f]);return o},getListData:function(){return this._rawList},getSelectedItemsID:function(){return this._selectedItemsID},disableItemsByIndices:function(n){this._getLi().length>0&&this._disableItemByIndex(n)},disableItemByIndex:function(n){this._getLi().length>0&&this._disableItemByIndex(n,!0)},_disableItemByIndex:function(i,r){var u,e,f;if(t.isNullOrUndefined(i)||typeof i!="string"&&typeof i!="number"||(i=i.toString().split(",")),this._isInteralCall||(this._initDisabledItems=r?this._initDisabledItems.push(i):i),!this.model.enabled)return!1;for(this.model.loadOnDemand&&(t.isNullOrUndefined(this.ultag)||this.ultag.children().length==0)&&this._showFullList(),u=this._toArray(i,!1),f=0;f<u.length;f++)u[f]==null||isNaN(parseInt(u[f]))||u.length>0&&!(n.inArray(parseInt(u[f]),this._disabledItems)>-1)&&(e=n.inArray(u[f],this._disabledItems),this._setClass(this._getLi()[parseInt(u[f])],"e-disable"),this._disabledItems.push(parseInt(u[f])),this.model.disableItemsByIndex=String(this._disabledItems.join(",")));this._prevDisabledItems=this._disabledItems},enableItemsByIndices:function(n){this._enableItemByIndex(n)},enableItemByIndex:function(n){this._enableItemByIndex(n)},_enableItemByIndex:function(i){var r,f,u;for(t.isNullOrUndefined(i)||typeof i!="string"&&typeof i!="number"||(i=i.toString().split(",")),this.model.loadOnDemand&&(t.isNullOrUndefined(this.ultag)||this.ultag.children().length==0)&&this._showFullList(),r=this._toArray(i,!1),this.model.enableItemsByIndex=i,u=0;u<r.length;u++)r.length>0&&n.inArray(parseInt(r[u]),this._disabledItems)>-1&&!isNaN(parseInt(r[u]))&&(f=n.inArray(parseInt(r[u]),this._disabledItems),this._removeClass(this._getLi()[parseInt(r[u])],"e-disable"),this._disabledItems.splice(f,1));this.model.enableItemsByIndex=null;this.model.disableItemsByIndex=this._disabledItems.join(this.model.delimiterChar)},_validateDelimiter:function(n){if(this._trim(n).length==1)if(!/^[a-zA-Z0-9]+$/.test(n))return n;return","},_removeText:function(i){var f,e,r,u;this.removeVal=(this.checkChange||this.delIconClick)&&(this.model.fields.text||this.model.fields.value||this.mapFld._text||this.mapFld._value)?!1:!0;this._unselectText&&(this.removeVal=!1,this._unselectText=!1);f=this._getUpdatedListData(this.element[0].value,!0);this.removeVal=(this.checkChange||this.delIconClick)&&(this.model.fields.text||this.model.fields.value);this._unselectText||(this._unselectText=!0);e=this._getUpdatedListData(this._visibleInput[0].value,this.model.selectedIndices.length===this._visibleInput[0].value.split(this.model.delimiterChar).length?!0:!1);this._unselectText=!1;r=this.getItemDataByValue(i)[0];u=this.model.fields&&this.model.fields.text?this.model.fields.text:"text";n.inArray(i,f)>=0&&f.splice(n.inArray(i,f),1);!t.isNullOrUndefined(r[u])&&n.inArray(this._decode(r[u]).toString(),e)>=0&&e.splice(n.inArray(this._decode(r[u]).toString(),e),1);n.inArray(i,this._valueContainer)>=0&&this._valueContainer.splice(n.inArray(i,this._valueContainer),1);!t.isNullOrUndefined(r[u])&&n.inArray(r[u].toString(),this._textContainer)>=0&&this._textContainer.splice(n.inArray(r[u].toString(),this._textContainer),1);this.element[0].value=f.join(this.model.delimiterChar);this._visibleInput[0].value=e.join(this.model.delimiterChar);this.removeVal=!1},_getUpdatedListData:function(n,i){var e,u=this.getListData(),o;if(i)if(e=[],this.model.fields.value||this.mapFld._value)e=n.split(this.model.delimiterChar);else for(o=0;o<this.model.selectedIndices.length;o++)e.push(u[this.model.selectedIndices[o]][this.model.fields.text?this.model.fields.text:"text"]);else e=n.split(this.model.delimiterChar);var f,r=0,h=0,c=[],l=[],s="";if(this.model.fields.value||this.model.fields.text?(f=this.model.fields.text,this.removeVal||this._unselectText||(f=this.model.fields.value?this.model.fields.value:f)):(this.mapFld._text||this.mapFld._value)&&(f=this.mapFld._text,this.removeVal||this._unselectText||(f=this.mapFld._value?this.mapFld._value:f)),!t.isNullOrUndefined(u))while(r<u.length){if(s=i?f?this._decode(u[r][f]):this._decode(u[r].text?u[r].text:u[r]):f?this._decode(u[r][f]):this._decode(u[r].value?u[r].value:u[r]),e.indexOf(s)!==-1)i&&(e[e.indexOf(s)]=h),c.push(s),h++;else if(!i&&e==="")break;r++}for(r=0;r<h;)i?l[e.indexOf(r)]=c[r]:l[r]=c[r],r++;return l},_addText:function(t){var o,r,u,i,e,f;if(this._checkContains(this._hiddenValue)||this.value()&&(o=this.value().split(","),o.indexOf(t)>0))return!1;for(r=["element","_visibleInput"],i=0;i<r.length;i++)u=r[i]=="element"?this._hiddenValue:t,e=r[i]=="element"?this._valueContainer:this._textContainer,this[r[i]][0].value&&this[r[i]][0].value!=""?(f=this[r[i]][0].value.split(this.model.delimiterChar),r[i]=="element"&&n.inArray(u,f)===-1?f.push(u):r[i]=="_visibleInput"&&f.push(u),this[r[i]][0].value=f.join(this.model.delimiterChar)):u==""?this[r[i]][0].value=u:u!=""&&this[r[i]][0].value==""&&(n.inArray("",e)!=-1?(f=this[r[i]][0].value.split(this.model.delimiterChar),f.push(u),this[r[i]][0].value=f.join(this.model.delimiterChar)):this[r[i]][0].value=u),e.push(u)},_checkContains:function(n){if(this.contains=!1,this._rawList!==i&&(this.model.dataSource!==null||this.selectOptions!==i||this.model.targetID!==null))for(var t=0;t<this._valueContainer.length;t++)if(this._parseValue(this._valueContainer[t])===this._parseValue(n)){this.contains=!0;break}return this.contains},_parseValue:function(n){return isNaN(parseInt(n))||this.mapFld&&typeof this._rawList[0][this.mapFld._value]=="string"?n:parseInt(n)},_updateLocalConstant:function(){this._localizedLabels=t.getLocalizedConstants("ej.DropDownList",this.model.locale)},_init:function(){var i=t.browserInfo();this._updateLocalConstant();this._isIE8=i.name=="msie"&&i.version=="8.0";this._textContent=this._isIE8?"innerText":"textContent";(this.element.is("input")&&(this.element.is("input[type=text]")||!this.element.attr("type"))||this.element.is("select"))&&(this._isWatermark="placeholder"in n(document.createElement("input")).attr("placeholder","")[0],this._id=this.element[0].id,this._initialize(),this._render(),this._addAttr(this.model.htmlAttributes),this._enabled(this.model.enabled),this._initValue=!1,this._checkboxValue=!1,this._unselectText=!1,this.model.validationRules!=null&&(this._initValidator(),this._setValidation()))},_initValidator:function(){this.element.closest("form").data("validator")||this.element.closest("form").validate()},_setValidation:function(){var r,f,i,u,e;if(this.element.closest("form").length!=0){this.element.rules("add",this.model.validationRules);r=this.element.closest("form").data("validator");r||(r=this.element.closest("form").validate());f=this.element.attr("name");r.settings.messages[f]={};for(i in this.model.validationRules)if(u=null,!t.isNullOrUndefined(this.model.validationRules[i])){if(t.isNullOrUndefined(this.model.validationRules.messages&&this.model.validationRules.messages[i])){r.settings.messages[f][i]=n.validator.messages[i];for(e in this.model.validationMessage)i==e?u=this.model.validationMessage[i]:""}else u=this.model.validationRules.messages[i];r.settings.messages[f][i]=u!=null?u:n.validator.messages[i]}}},_setInitialPopup:function(n){this.model.enabled&&!this.model.readOnly&&(n==!1?this._hideResult():this._showResult())},_changeSkin:function(n){this.wrapper.removeClass(this.model.cssClass).addClass(n);t.isNullOrUndefined(this.popupListWrapper)||this.popupListWrapper.removeClass(this.model.cssClass).addClass(n)},_setRTL:function(n){this.model.enableRTL!=n&&(this.model.enableRTL=n,this._RightToLeft(),t.isNullOrUndefined(this.popupListWrapper)||this._dropbtnRTL())},_changeHeight:function(n){this.wrapper.height(n);this._setListHeight()},_changeWidth:function(n){this.wrapper.width(n);this._setListWidth()},_setModel:function(r){var u,o,s,e,f;for(u in r)switch(u){case"dataSource":this._isEqualDataSource(t.util.getVal(r[u]))||(this.model.loadOnDemand&&(o=n("#"+this.element[0].id+"_popup_wrapper").get(0),o&&n(o).remove(),this.popupListWrapper=i,this.inputSearch=i),this._checkModelDataBinding(t.util.getVal(r[u]),this.model.query));this._refreshScroller();break;case"query":this._checkModelDataBinding(this._dataSource(),r[u]);break;case"fields":this.model.fields=n.extend(this.model.fields,r[u]);this._checkModelDataBinding(this._dataSource(),this.model.query);break;case"itemsCount":this.model.itemsCount=r[u];this._checkModelDataBinding(this._dataSource(),this.model.query);break;case"template":this.model.template=r[u];this._checkModelDataBinding(this._dataSource(),this.model.query);break;case"value":this._optionValue=t.util.getVal(r[u]);(t.isNullOrUndefined(this._optionValue)||this._optionValue==="")&&this._clearTextboxValue();t.isNullOrUndefined(this._optionValue)||this._optionValue===""?this._clearTextboxValue():(this._checkedValues.push(r.value),this._setValue(t.util.getVal(r[u])),r[u]=this.model.value);break;case"incrementalSearchDelay":this.model.incrementalSearchDelay=r[u];break;case"delimiterChar":s=this.model.delimiterChar;r[u]=this._validateDelimiter(r[u]);this.model.delimiterChar=r[u];this._isSingleSelect()||(this.model.text&&(this.model.text=this.model.text.split(s).join(this.model.delimiterChar),this._visibleInput.val(this.model.text)),t.isNullOrUndefined(this.value())||(this.value(this.value().split(s).join(this.model.delimiterChar)),this.element.val(this.value())));break;case"text":t.isNullOrUndefined(r[u])||r[u]===""?this._clearTextboxValue():(this._setText(r[u]),r[u]=this.model.text);break;case"itemValue":this._setItemValue(r[u]);break;case"enableRTL":this._setRTL(r[u]);break;case"enabled":this._enabled(r[u]);break;case"height":this._changeHeight(r[u]);break;case"width":this._changeWidth(r[u]);break;case"popupHeight":this.model.popupHeight=r[u];this._setListHeight(r[u]);break;case"popupWidth":this.model.popupWidth=r[u];this._setListWidth();break;case"minPopupHeight":this.model.minPopupHeight=r[u];this._setListHeight(r[u]);break;case"minPopupWidth":this.model.minPopupWidth=r[u];this._setListWidth();break;case"maxPopupHeight":this.model.maxPopupHeight=r[u];this._setListHeight(r[u]);break;case"maxPopupWidth":this.model.maxPopupWidth=r[u];this._setListWidth();break;case"popupTarget":this.model.popupTarget=r[u];this._renderPopupPanelWrapper();break;case"cssClass":this._changeSkin(r[u]);break;case"showCheckbox":this.model.showCheckbox=r[u];e=this.model.text;f=this.model.text?this.model.text.split(this.model.delimiterChar):this.model.text;this._raiseEvents=!1;this._clearTextboxValue();this._raiseEvents=!0;this._checkboxHideShow(r[u]);this.model.showCheckbox||this.model.multiSelectMode!="none"?(this.model.multiSelectMode=="visualmode"&&this._renderBoxModel(),this._setText(e)):f&&f.length&&this._setText(f[0]);this._isPopupShown()&&this._setListPosition();break;case"checkAll":return this._setCheckAll(r[u]),!1;case"uncheckAll":return this._setUncheckAll(r[u]),!1;case"watermarkText":this._changeWatermark(r[u]);break;case"validationRules":this.element.closest("form").length!=0&&(this.model.validationRules!=null&&(this.element.rules("remove"),this.model.validationMessage=null),this.model.validationRules=r[u],this.model.validationRules!=null&&(this._initValidator(),this._setValidation()));break;case"locale":this.model.locale=r[u];this._updateLocalConstant();break;case"validationMessage":this.element.closest("form").length!=0&&(this.model.validationMessage=r[u],this.model.validationRules!=null&&this.model.validationMessage!=null&&(this._initValidator(),this._setValidation()));break;case"showRoundedCorner":this._roundedCorner(r[u]);this.model.showRoundedCorner=r[u];break;case"showPopupOnLoad":this._setInitialPopup(r[u]);break;case"targetID":this.model.targetID=r[u];this._showFullList();break;case"selectedItemIndex":case"selectedIndex":this._selectItemByIndex(r[u]);this.model.selectedItemIndex=this.model.selectedIndex=r[u];break;case"unselectItemByIndex":this._unselectItemByIndex(r[u]);break;case"disableItemsByIndex":this._disableItemByIndex(r[u]);break;case"enableItemsByIndex":this._enableItemByIndex(r[u]);break;case"selectedItems":case"selectedIndices":this._selectCheckedItem(r[u]);r[u]=this.model.selectedItems=this.model.selectedIndices=this._selectedIndices;break;case"multiSelectMode":this.model.multiSelectMode=="visualmode"&&(this._swapUlandInput(!1),this._ulBox.remove(),this._ulBox=null);this.model.multiSelectMode=r[u];e=this.model.text;f=this.model.text?this.model.text.split(this.model.delimiterChar):this.model.text;this._raiseEvents=!1;this._clearTextboxValue();this._raiseEvents=!0;this.model.showCheckbox||this.model.multiSelectMode!="none"?(this.model.multiSelectMode=="visualmode"&&this._renderBoxModel(),this._setText(e)):(this._setText(f[0]),this.wrapper.find("input[name="+this._name+"]").remove());this._isPopupShown()&&this._setListPosition();break;case"allowGrouping":this._setGroupingAndSorting("allowGrouping",r[u]);break;case"enableSorting":this._setGroupingAndSorting("enableSorting",r[u]);break;case"htmlAttributes":this._addAttr(r[u]);break;case"enablePopupResize":this.model.enablePopupResize=r[u];r[u]?this._enablePopupResize():this.popupListWrapper.removeClass("e-resizable").find(".e-resizebar").remove()&&this._hideResult();break;case"enableFilterSearch":if(r[u]){this.model.enableFilterSearch=!0;this._enableSearch();break}else this._removeSearch();case"enableServerFiltering":this._enableSearch()}},_clearTextboxValue:function(){this.element.val("");this._visibleInput.val("");this._updateValue("");this._valueContainer=[];this._boxValue=[];this._textContainer=[];this.selectedTextValue=this._selectedValue=this._hiddenValue=this.model.itemValue="";this._updateText();this.selectedIndexValue=this._hiddenDelimiterIndex=this._activeItem=-1;this._selectedItemsID=[];this._selectedIndices=[];this.model.selectedItems=[];this.model.selectedIndices=[];this.selectedIndex(null);t.isNullOrUndefined(this.ultag)||(this.ultag.children("li").removeClass("e-hover").removeClass("e-active"),this.model.showCheckbox&&this._resetCheck());this.wrapper.find("ul.e-ul.e-boxes").length!=0&&(this._ulBox.children("li").remove(),this._name===""?this.wrapper.find("input:hidden[id^='#']").remove():this.wrapper.find("input:hidden[id^='#'][name="+this._name+"]").remove(),n(this.element).attr("name",this._name))},_destroy:function(){this.selectOptions?(!this._dataSource()&&this.docbdy?this.docbdy.append(this.selectOptions.removeClass("e-dropdownlist e-js").show()).show():this.selectOptions.insertAfter(this.wrapper).removeClass("e-dropdownlist e-js").show(),this.element.remove()):(this.element.insertAfter(this.wrapper),this.element.width(this.element.width()+this.dropdownbutton.outerWidth()),this._visibleInput.removeClass("e-input "),this._setAttr(this.element[0],{accesskey:this.wrapper.attr("accesskey"),type:"text"}),this._isWatermark&&this._visibleInput.removeAttr("placeholder"),this.element[0].value="",this.element.removeAttr("aria-expanded aria-autocomplete aria-haspopup aria-owns accesskey role").css({width:"",display:"block"}),!this._dataSource()&&this.docbdy&&this.ultag.find("li").removeClass("e-active")&&this.docbdy.append(this.ultag.html()).show());this.wrapper.remove();this.container.off("mousedown",n.proxy(this._OnDropdownClick,this));this._hideResult();this.popupPanelWrapper.remove();this._unwireEvents()},_finalize:function(){this.value()==""&&this._visibleInput[0].value!==""&&this._updateValue(this.element[0].value);(t.isNullOrUndefined(this.value())||this.value()!="")&&this.value()===this.element.val()||this._setValue(this.value());(t.isNullOrUndefined(this.model.text)||this.model.text!="")&&this.model.text==this._visibleInput.val()||this._setText(this.model.text);this.selectedIndex(this.selectedIndex()!=-1?this.selectedIndex():this.selectedItemIndex());this.selectedIndex()!=-1?this._selectItemByIndex(this.selectedIndex()):this._selectedIndices.length>0&&this._selectCheckedItem(this._selectedIndices);this.model.disableItemsByIndex!=null&&this._disableItemByIndex(this.model.disableItemsByIndex);this.model.enableItemsByIndex!=null&&this._enableItemByIndex(this.model.enableItemsByIndex)},_initialize:function(){this._selectedIndices=this.model.selectedIndices.length>0?this.model.selectedIndices:this.model.selectedItems;this.model.selectedItems=this.model.selectedIndices=this._selectedIndices;this.model.selectedIndex=this.model.selectedIndex!=-1?this.model.selectedIndex:this.model.selectedItemIndex;this.element.is("select")&&(this.selectelement=!0,this._renderSelectToDropdown());this._initDisabledItems=[];this._isInteralCall=!1;this._prevSearchValue="";this._selectedItemsID=[];this._hiddenInputElement=[];this._boxValue=[];this._valueContainer=[];this._textContainer=[];this._checkedValues=[];this.target=this.element[0];this._disabledItems=[];this._queryString=null;this.suggLen=0;this._itemId=null;this.checkedStatus=!1;this._incqueryString="";this._activeItem=null;this.ddWidth=0;this._initValue=!0;this._virtualCount=0;this._raiseEvents=!0;this.popUpShow=!1;this._matchedListItems=[];this._prevQueryString=""},_renderSelectToDropdown:function(){var i,f,u,r;if(this.inputElement=t.buildTag("input.e-dropdownlist#"+this._id+"_input","",{},{type:"text","data-role":"none"}),this.inputElement.insertAfter(this.element),this.element.attr("name")&&(this.inputElement.attr("name",this.element.attr("name")),this.element.removeAttr("name")),this.selectOptions=this.element,this.selectOptions.attr("id",this._id),this._dataSource()==null){for(this.optionDiv=t.buildTag("div#"+this._id+"_list"),this.optionDiv.insertAfter(this.inputElement),this.optionUl=t.buildTag("ul"),this.optionDiv.append(this.optionUl),this.selectOptionItems=this.element.children("option"),f=this.selectOptionItems.length,this.optionDummyUl=n(),i=0;i<f;i++)r=this.selectOptionItems[i],u=n(r).attr("label")?n(r).attr("label"):r.innerHTML,u!=null&&(this.optionLi=t.buildTag("li",u,{},{"data-value":r.value,unselectable:"on"}),this.optionDummyUl.push(this.optionLi[0]),n(r).attr("selected")&&this.model.showCheckbox&&(n.inArray(i,this._selectedIndices)==-1?(this._selectedIndices.push(i),this.model.selectedItems=this.model.selectedIndices=this._selectedIndices):t.isNullOrUndefined(this.selectedIndex())&&this.selectedIndex(i)));this.optionUl.append(this.optionDummyUl)}this.element.css("display","none");this.element=this.inputElement},_render:function(){var r,i,u;this._renderDropdown();this._setWatermark();this.model.loadOnDemand&&(!this._dataSource()||this._dataSource().length!==0&&this._dataSource().length<1)&&(r=this.element.parents().last(),this.docbdy=this.model.targetID?r.find("#"+this.model.targetID):this.optionDiv?this.optionDiv:null,this.itemsContainer=this.docbdy[0].nodeName=="UL"?this.docbdy:this.docbdy.children("ol,ul"),this.itemsContainer.css("display","none"));this.model.loadOnDemand||(this._renderPopupPanelWrapper(),this._showFullList());this._roundedCorner(this.model.showRoundedCorner);i=this._dataSource();t.DataManager&&i instanceof t.DataManager?i.dataSource.offline&&i.dataSource.json&&i.dataSource.json.length>0&&(u=this,u._finalize()):this._finalize();t.DataManager&&this._dataSource()instanceof t.DataManager||this._finalize();this.model.loadOnDemand&&t.DataManager&&this._dataSource()instanceof t.DataManager&&!this._dataSource().dataSource.offline&&this._finalize();this._setCheckAll(this.model.checkAll);(this.element.attr("disabled")||n(this.selectOptions).attr("disabled"))&&this.disable();this.model.loadOnDemand&&this.model.showPopupOnLoad&&this._showResult()},_isEqualDataSource:function(n){var r,i,u;if(!this._dataSource()||!n||this._rawList&&!(this._rawList.length===n.length)||t.DataManager&&n instanceof t.DataManager||t.isNullOrUndefined(this._rawList)||this._dataSource().length===0)return!1;for(r=!0,i=0,u=this._dataSource().length;i<u;i++)if(this._dataSource()[i]!==n[i]){r=!1;break}return r},_checkModelDataBinding:function(n,i){this.element.val("");this._visibleInput.val("");this.value()!==null&&this.value()!==""&&this._updateValue("");this.selectedTextValue=this._selectedValue=this._hiddenValue="";this._updateText();this.selectedIndexValue=this._hiddenDelimiterIndex=this._activeItem=-1;this._selectedItemsID=[];this._textContainer=[];this._valueContainer=[];t.isNullOrUndefined(n)&&t.isNullOrUndefined(this.model.query)&&t.isNullOrUndefined(this.model.template)&&this.model.itemsCount==0&&(this._rawList=[],this.popupListItems=[],this.listitems=[],this.ultag=this.popupListWrapper.find(".e-ul"));this.model.selectedItems=this.model.selectedIndices=this._selectedIndices=[];this.model.selectedIndex=this.model.selectedItemIndex=-1;this.model.multiSelectMode=="visualmode"&&this._destroyBoxModel();JSON.stringify(this._dataSource())!=JSON.stringify(n)&&this._dataSource(n);this.model.query=i;t.isNullOrUndefined(this.ultag)||this.ultag.empty();this._showFullList()},_initDataSource:function(n){var i=this,r;t.DataManager&&n instanceof t.DataManager&&(i._addLoadingClass(),i._trigger("actionBegin",{requestFrom:"default"})||(r=n.executeQuery(this._getQuery()),r.done(function(n){i._trigger("actionSuccess",{e:n,requestFrom:"default"});i._totalCount=n.count;i._listItem(n.result);i._removeLoadingClass();i._renderPopupList();i._finalize();i._setDynamicSelectByVal(i,i._optionValue,n);this._optionValue=null}).fail(function(n){i._dataSource(null);i._addLoadingClass();i._trigger("actionFailure",{e:n,requestFrom:"default"})}).always(function(n){i._trigger("actionComplete",{e:n,requestFrom:"default"})})))},_listItem:function(t,i){i=="add"?(this.popupListItems.push(t),this._rawList.push(t)):n.isArray(t)&&(this.popupListItems=t.slice(0),this._rawList=t.slice(0))},_getQuery:function(n){var i,r=this.model.fields,u=t.Query(),f,e;if(!t.isNullOrUndefined(this.model.query)||this.model.template||n)this.model.query&&(u=this.model.query.clone());else{f=[];for(e in r)e!=="tableName"&&r[e]&&f.push(r[e]);f.length>0&&u.select(f)}return this.model.allowVirtualScrolling&&u.requiresCount(),this.model.itemsCount>0&&u.take(this.model.itemsCount),i=this._dataSource().dataSource,r&&(i&&i.url&&!i.url.match(r.tableName+"$")||i&&!i.url||!i)&&(t.isNullOrUndefined(r.tableName)||u.from(r.tableName)),u},_addLoadingClass:function(){this._isPopupShown()?this.popupListWrapper.addClass("e-load"):(this.dropdownbutton.addClass("e-load"),this.drpbtnspan.removeClass("e-icon e-arrow-sans-down"));this._readOnly=!0},_removeLoadingClass:function(){this.dropdownbutton.removeClass("e-load");this.drpbtnspan.addClass("e-icon e-arrow-sans-down");this._readOnly=!1;t.isNullOrUndefined(this.popupListWrapper)||this.popupListWrapper.removeClass("e-load")},_renderDropdown:function(){if(this.wrapper=t.buildTag("span.e-ddl e-widget "+this.model.cssClass+"#"+this._id+"_wrapper","",{},{accesskey:this.element.attr("accesskey"),role:"listbox","aria-expanded":!1,"aria-haspopup":!0,"aria-owns":this._id+"_popup",tabIndex:"0"}),this.container=t.buildTag("span.e-in-wrap e-box #"+this._id+"_container"),this.element.removeAttr("accesskey"),this.model.value==null&&this.element.attr("value")!=null&&(this.model.value=this.element.attr("value")),this.element.attr("value","").val(""),this._isIE8?this._setAttr(this.element[0],{unselectable:"on"}).element.hide():this._setAttr(this.element[0],{type:"hidden",unselectable:"on"}).element.hide(),this.drpbtnspan=t.buildTag("span.e-icon e-arrow-sans-down","",{},{"aria-label":"select",unselectable:"on"}),this.dropdownbutton=t.buildTag("span.e-select#"+this._id+"_dropdown","",{},{role:"button",unselectable:"on"}).append(this.drpbtnspan),this.container.insertAfter(this.element),this.container.append(this.element),this.container.append(this.dropdownbutton),this.wrapper.insertBefore(this.container),this.wrapper.append(this.container),this.selectelement&&this.selectOptions.insertBefore(this.element),this._visibleInput=t.buildTag("input#"+this._id+"_hidden","",{}).insertAfter(this.element),this._visibleInput.addClass("e-input "),this._setAttr(this._visibleInput[0],{readonly:"readonly",tabindex:"-1","data-role":"textbox"}),!this._isWatermark){var i=this.model.watermarkText!=null?this.model.watermarkText:this._localizedLabels.watermarkText;this._hiddenSpan=t.buildTag("span.e-input e-placeholder ").insertAfter(this.element);this._hiddenSpan.text(i);this._hiddenSpan.css("display","none");this._hiddenSpan.on("mousedown",n.proxy(this._OnDropdownClick,this))}this._checkNameAttr();this._setDimentions();this._RightToLeft();this.ddWidth=this.dropdownbutton.outerWidth()>0?this.dropdownbutton.outerWidth():24;this.container.on("mousedown",n.proxy(this._OnDropdownClick,this))},_checkNameAttr:function(){this._name=t.isNullOrUndefined(this.element.attr("name"))?this._id:this.element.attr("name");this.element.attr("name",this._name)},_addAttr:function(i){var r=this;n.map(i,function(i,u){var f=u.toLowerCase();f=="class"?r.wrapper.addClass(i):f=="disabled"&&i=="disabled"?r.disable():f=="readOnly"&&i=="readOnly"?r.model.readOnly=!0:f=="style"?r.wrapper.attr(u,i):f=="name"?n(r.element).attr(u,i):t.isValidAttr(r._visibleInput[0],u)?n(r._visibleInput).attr(u,i):r.wrapper.attr(u,i)})},_renderBoxModel:function(){if(!t.isNullOrUndefined(this._ulBox)||this.model.multiSelectMode!="visualmode")return!1;this._ulBox=t.buildTag("ul.e-ul e-boxes");this.container.prepend(this._ulBox);this._ulBox.css("min-height","30px");this._ulBox.css("display","none");this._on(this.container,"click",function(t){if(!this.model.enabled)return!1;var i=n(t.target);i.hasClass("e-options")&&(!t.ctrlKey&&i.siblings().hasClass("e-active")&&this._removeActive(),i.hasClass("e-active")?i.removeClass("e-active"):i.addClass("e-active"));!t.ctrlKey&&i.hasClass("e-boxes")&&this._removeActive()})},_renderPopupPanelWrapper:function(){var i=n("#"+this.element[0].id+"_popup_wrapper").get(0);i&&n(i).remove();this.popupPanelWrapper=t.buildTag("div#"+this._id+"_popup_wrapper");n(this.model.popupTarget).append(this.popupPanelWrapper);this.popupListWrapper=t.buildTag("div.e-ddl-popup e-box e-widget  e-popup#"+this._id+"_popup_list_wrapper","",{display:"none",overflow:"hidden"});this._setAttr(this.popupListWrapper[0],{"data-role":"popup","aria-hidden":!0});this.popupList=t.buildTag("div",{tabIndex:0});this.popupListWrapper.addClass(this.model.cssClass);this.popup=this.popupList;this.popupScroller=t.buildTag("div");t.isNullOrUndefined(this.ultag)&&(this.ultag=t.buildTag("ul.e-ul","",{},{role:"listbox"}));this._setAttr(this.ultag[0],{id:this._id+"_popup","aria-hidden":!0,unselectable:"on"});this.popupScroller.append(this.ultag);this.popupList.append(this.popupScroller);this.model.headerTemplate&&(this.headerTemplate=n("<div>").append(this.model.headerTemplate),this.popupListWrapper.append(this.headerTemplate));this.popupListWrapper.append(this.popupList);this.popupPanelWrapper.append(this.popupListWrapper);this.ultag.on({mouseenter:n.proxy(this._OnMouseEnter,this),mouseleave:n.proxy(this._OnMouseLeave,this),click:n.proxy(this._OnMouseClick,this)},"li:not('.e-category')");if(t.isTouchDevice())this.ultag.on({tap:n.proxy(this._OnMouseEnter,this)},"li:not('.e-category')");n(window).on("resize",n.proxy(this._OnWindowResize,this))},_updateText:function(){var n=this._visibleInput.val();this.model.text=n==""?this._textContainer.length==0?null:"":n},_updateValue:function(n){this.value(n==""?this._valueContainer.length==0?null:"":n)},_setGroupingAndSorting:function(n,t){var i,u,r;if(this.model[n]=t,i=this.model.text,this._updateValue(""),this._selectedIndices=[],this.ultag.empty(),this._showFullList(),this.model.showCheckbox&&i)for(u=i.split(this.model.delimiterChar),r=0;r<u.length;r++)this.selectItemByText(u[r]);else this.selectItemByText(i)},_setSortingList:function(){var t=document.createElement("ul"),r,i;if(n(t).append(this.itemsContainer.children()),this.model.allowGrouping||n(t).find(">.e-category").length>0)for(this.popupListWrapper.addClass("e-atc-popup"),r=0;r<n(t).find(">.e-category").length;r++)i=n(t).find(">.e-category").eq(0).first().nextUntil(".e-category").get(),this._setSortList(t,i);else n(t).children(">.e-category").remove(),i=n(t).children("li").get(),this._setSortList(t,i);this.itemsContainer=n(t)},_setSortList:function(t,i){i.sort(function(t,i){var r=n(t).text().toUpperCase(),u=n(i).text().toUpperCase();return r<u?-1:r>u?1:0});this.model.sortOrder=="descending"&&i.reverse();(this.model.allowGrouping||n(t).find(">.e-category").length>0)&&(n(t).append(n("<li>").text(n(t).find(">.e-category").eq(0).text()).addClass("e-category")),n(t).find(">.e-category").eq(0).remove());n.each(i,function(i,r){n(t).append(r)})},_renderPopupList:function(){this._doDataBind();this.model.loadOnDemand&&t.DataManager&&this._dataSource()instanceof t.DataManager&&!this._dataSource().dataSource.offline&&this.model.showCheckbox&&this._checkboxHideShow(this.model.showCheckbox);(t.isNullOrUndefined(this.scrollerObj)||this._ulBox==null)&&this._renderRemaining();this.model.loadOnDemand&&this.popUpShow&&t.DataManager&&this._dataSource()instanceof t.DataManager&&!this._dataSource().dataSource.offline&&(this._refreshPopup(),this._refreshScroller())},_renderRemaining:function(){var i=this;t.isNullOrUndefined(this.popupListWrapper)||this._dropbtnRTL();this.model.enableFilterSearch&&this._enableSearch();this.model.enablePopupResize&&this._enablePopupResize();this.model.allowVirtualScrolling&&this.model.virtualScrollMode=="normal"&&this._totalCount&&this._totalCount>0&&(this._totalHeight=this._totalCount*29,this._totalPages=this._totalCount/(this.model.itemsCount*29),this._loadedItemHeight=this._getLi().length*29,this._getLi().attr("page",0),this._virtualPages=[0],this.ultag.append(n("<span>").addClass("e-virtual").css({height:this._totalHeight-this._loadedItemHeight,display:"block"})));this._virtualUl=this.ultag.clone(!0);t.isNullOrUndefined(this.popupListWrapper)||(this._setListWidth(),this._setListHeight());this._isSingleSelect()||(this.model.showCheckbox?this._checkboxHideShow(this.model.showCheckbox):this._multiItemSelection(this._getLi()));this._setUncheckAll(this.model.uncheckAll);t.isNullOrUndefined(this.popupListWrapper)||(this.popupScroller.css({height:"",width:""}),this.popupList.ejScroller({height:this._getPopupHeight(),width:0,scrollerSize:20,scroll:function(n){i.model.allowVirtualScrolling&&i._onScroll(n)},preventDefault:!0}),this.scrollerObj=this.popupList.ejScroller("instance"),this.popupList.find("div.e-scrollbar div").attr("unselectable","on"),this._setListPosition(),this.popUpShow||this.popupListWrapper.css({display:"none",visibility:"visible"}),this._changeSkin(this.model.cssClass));this.model.loadOnDemand||this.model.showPopupOnLoad&&this._showResult()},_enableSearch:function(){if(this.model.enableFilterSearch&&!t.isNullOrUndefined(this.popupListWrapper)&&!this.inputSearch){this.inputSearch=t.buildTag("input#"+this._id+"_inputSearch.e-input","",{},{type:"text","data-role":"none"});this.popupListWrapper.prepend(n("<span>").addClass("e-atc e-search").append(n("<span>").addClass("e-in-wrap ").append(this.inputSearch).append(n("<span>").addClass(" e-icon e-search"))));var i=this.model.enableServerFiltering?this._debounce(this._OnSearchEnter,200):this._OnSearchEnter;this._on(this.inputSearch,"keyup",i)._on(this.inputSearch,"keydown",function(n){var t=n.keyCode||n.which;t==9&&(n.preventDefault(),this.wrapper.focus(),this._hideResult())})}},_removeSearch:function(){this.model.enableFilterSearch=!1;this.popupListWrapper.find(".e-atc.e-search").remove();this._isPopupShown()&&this.hidePopup();this.inputSearch=null},_OnSearchEnter:function(i){var u,f,r;(this._prevSearchValue==""||this._prevSearchValue!=this.inputSearch.val())&&((this._prevSearchValue=this.inputSearch.val(),t.isNullOrUndefined(this._rawList))||(i.keyCode==8&&this.model.enableFilterSearch&&(this._disabledItems=this._initDisabledItems,this._prevDisabledItems=this._disabledItems),u=this,n.inArray(i.keyCode,[38,40,13])!=-1&&this.ultag.find("li.e-nosuggestion").length<=0?i.keyCode==13?this._OnKeyUp(i):this._OnKeyDown(i):(this._activeItem=-1,this._queryString=this.inputSearch.val(),this._queryString==""&&this._virtualUl?(r={searchString:this._queryString,searchQuery:null,items:this._rawList},this._trigger("search",r),this._resetList(i),this._updateSelectedIndexByValue(this.value()),this._refreshScroller(),this._setListPosition(),this._prevDisabledItems&&this._prevDisabledItems.length>0&&(this._disabledItems=[],this._isInteralCall=!0,this.disableItemsByIndices(this._prevDisabledItems),this._isInteralCall=!1,this._prevDisabledItems=this._disabledItems)):(this._mapFields(),f=this._addSearchQuery(t.Query(),!this._isPlainType(this._rawList)),r={searchString:this._queryString,items:this._rawList,searchQuery:f},this.popupListWrapper.find(".e-atc.e-search .e-search").addClass("e-cross-circle").removeClass("e-search"),this._on(this.popupListWrapper.find(".e-atc.e-search .e-cross-circle"),"mousedown",this._refreshSearch),this._trigger("search",r)||u._onActionComplete(r)))))},_debounce:function(n,t){var i,r=this;return function(){var u=arguments,f=function(){return i=null,n.apply(r,u)};clearTimeout(i);i=setTimeout(f,t)}},_onActionComplete:function(n){var r=this,n,i,u;this._queryString=this.inputSearch.val();i=this._addSearchQuery(t.Query(),!this._isPlainType(this._rawList));n={searchString:this._queryString,items:this._rawList,searchQuery:i};t.DataManager&&this._dataSource()instanceof t.DataManager&&this.model.enableServerFiltering&&(window.getSelection().type=="Caret"||t.browserInfo().name=="msie")?(i=n.searchQuery.clone(),u=r._dataSource().executeQuery(i),u.done(function(t){r._filterSearch(n.searchQuery,t)})):r._filterSearch(i,n)},_refreshSearch:function(){this._resetSearch();this._refreshPopup();this._initDisabledItems&&this._initDisabledItems.length>0&&(this._disabledItems=[],this.disableItemsByIndices(this._initDisabledItems),this._prevDisabledItems=this._disabledItems)},_filterSearch:function(n,i){var o=!1,u,f,r,e,s;this.resultList=i.result?i.result:t.DataManager(this._rawList).executeLocal(n);this.resultList.length==0&&(o=!0,this.resultList.push(this._getLocalizedLabels("emptyResultText")));u=[];this._initDisabledItems.forEach(function(n){this._rawList.forEach(function(t){this._rawList.indexOf(t)==n&&u.push(t)},this)},this);f=[];u.length>0&&this.resultList.forEach(function(n){u.indexOf(n)>=0&&f.push(this.resultList.indexOf(n))},this);this.popupListItems=this.resultList;this.ultag.empty();this._hiddenInputElement.length==this.listitems.length&&(this._valueContainer=[]);this._isPlainType(this.popupListItems)?this._plainArrayTypeBinding(this.resultList):this._objectArrayTypeBinding(this.resultList,"search");o&&this.ultag.find("li").length==1&&this.ultag.find("li").eq(0).addClass("e-nosuggestion");this.model.showCheckbox&&!o&&this._appendCheckbox(this._getLi());this._onSearch=!0;r=this.value();e=this._visibleInput[0].value;this._setValue(this.value());s=typeof this.model.value=="function"?this.model.value():this.model.value;s!=r&&(this.element[0].value=r,this._visibleInput[0].value=e,this.model.text=e==""?null:e,this.value()==r||this.value()==null&&r==""||this._updateValue(r));this._onSearch=!1;this._updateSelectedIndexByValue(this.value());this._refreshScroller();this._setListPosition();this._prevDisabledItems=this._disabledItems;this._disabledItems=[];f.length>0&&(this._isInteralCall=!0,this.disableItemsByIndices(f),this._isInteralCall=!1)},_updateSelectedIndexByValue:function(n){var r,i,t;if(n&&this.model.enableFilterSearch){for(this._selectedIndices=this.model.selectedItems=this.model.selectedIndices=[],this._virtualList=this._getLi(),r=this._toArray(n),i=0;i<r.length;i++)for(t=0;t<this._virtualList.length;t++)if(r[i]==this._getIndexedValue(this._virtualList[t])){this._selectedIndices.push(t);break}this.model.selectedItems=this.model.selectedIndices=this._selectedIndices}},_getIndexedValue:function(n){return this._getAttributeValue(n)?this._getAttributeValue(n):n.innerText},_resetSearch:function(){this.inputSearch&&this.model&&this.model.enableFilterSearch&&this.inputSearch.val()!=""&&this._virtualUl&&(this.inputSearch.val(""),this._resetList())},_resetList:function(n){var r,i,u;if(this.popupListWrapper.find(".e-atc.e-search .e-cross-circle").length==1&&(this.popupListWrapper.find(".e-atc.e-search .e-cross-circle").addClass("e-search").removeClass("e-cross-circle"),this._off(this.popupListWrapper.find(".e-atc.e-search .e-cross-circle"),"mousedown",this._refreshSearch)),this.model.enableServerFiltering){for(r=this.model.fields&&this.model.fields.value?this.model.fields.value:"value",i=0;i<this._rawList.length;i++)this._rawList[i][r]==this._selectedValue&&(this._searchresult=null);t.isNullOrUndefined(this._searchresult)||this.model.allowVirtualScrolling&&this.model.virtualScrollMode=="normal"||this.addItem(this._searchresult)}this._listItem(this._rawList);this.model.enableSorting&&(u=t.Query().sortBy(this.mapFld._text,this.model.sortOrder,!0),this._rawList=t.DataManager(this._rawList).executeLocal(u),this.popupListItems=this._rawList);this.ultag.empty().append(this._virtualUl.children().clone(!0));this._onSearch=!0;this._setValue(this.value(),n);this._onSearch=!1;this._searchresult=[];this._initDisabledItems&&this._initDisabledItems.length>0&&(this._disabledItems=[],this.disableItemsByIndices(this._initDisabledItems),this._prevDisabledItems=this._disabledItems)},_addSearchQuery:function(n,t){var r="",i;return t&&(i=this.model.fields,r=i&&i.text?i.text:"text"),this._queryString&&n.where(r,this.model.filterType,this._queryString,!this.model.caseSensitiveSearch),this.model.itemsCount>0&&n.take(this.model.itemsCount),n},_targetElementBinding:function(){var e=this.element.parents().last(),r,f,t,u,i;if(this.docbdy=this.model.targetID?e.find("#"+this.model.targetID):this.optionDiv?this.optionDiv:null,!this.docbdy)return!1;if(this.itemsContainer=this.docbdy[0].nodeName=="UL"?this.docbdy:this.docbdy.children("ol,ul"),(this.model.allowGrouping||this.itemsContainer.find(">.e-category").length>0)&&!this.model.enableSorting)for(this.popupListWrapper.addClass("e-atc-popup"),r=0;r<this.itemsContainer.find(">.e-category").length;r++)f=this.itemsContainer.find(">.e-category").eq(r),f.replaceWith(n("<li>").text(f.text()).addClass("e-category"));else this.model.enableSorting&&this._setSortingList();for(this.itemsContainer.children("ol,ul").remove(),this.items=this.model.loadOnDemand?this.itemsContainer.children().length>0?this.itemsContainer.children("li"):this.items:this.itemsContainer.children("li"),this.items.children("img,div").addClass("e-align"),this._listItem([]),t=0;t<this.items.length;t++)u=n(this.items[t]).text(),i=this._getAttributeValue(this.items[t]),this.items[t].getAttribute("id")&&this.items[t].getAttribute("id")!=""||n(this.items[t]).attr("id",this._id+"_"+t+"_popup_option"),n(this.items[t]).attr("data-value")||n(this.items[t]).attr("data-value",i?i:u),this._listItem({text:u,value:i?i:u},"add");this.model.loadOnDemand?this.ultag.empty().append(this.items):this.ultag.empty().append(this.itemsContainer.children());this.ultag.children("li").attr("role","option").attr("unselectable","on").attr("aria-selected","false").attr("tabindex","-1");this.docbdy.css({display:"none"}).children("ol,ul").remove()},_plainArrayTypeBinding:function(i){var r,u;if(this.dummyUl=n(),this.model.enableSorting&&(i.sort(),this.model.sortOrder=="descending"&&i.reverse()),i.length>0){for(r=0;r<i.length;r++)t.isNullOrUndefined(i[r])||(u=t.buildTag("li",i[r],{},{"data-value":i[r],unselectable:"on"}),this.dummyUl.push(u[0]));this.ultag.append(this.dummyUl);this._trigger("dataBound",{data:i})}},_mapFields:function(){this.model.fields.groupBy=this.model.fields.groupBy?this.model.fields.groupBy:this.model.fields.category;var n=this.model.fields;this.mapFld={_id:null,_imageUrl:null,_imageAttributes:null,_spriteCSS:null,_text:null,_value:null,_htmlAttributes:null,_selected:null};this.mapFld._id=n&&n.id?n.id:"id";this.mapFld._imageUrl=n&&n.imageUrl?n.imageUrl:"imageUrl";this.mapFld._imageAttributes=n&&n.imageAttributes?n.imageAttributes:"imageAttributes";this.mapFld._spriteCSS=n&&n.spriteCssClass?n.spriteCssClass:"spriteCssClass";this.mapFld._text=n&&n.text?n.text:"text";this.mapFld._value=n&&n.value?n.value:"value";this.mapFld._htmlAttributes=n&&n.htmlAttributes?n.htmlAttributes:"htmlAttributes";this.mapFld._selected=n&&n.selected?n.selected:"selected";this.mapFld._category=n&&n.groupBy?n.groupBy:"groupBy"},_doDataBind:function(){var t=this._dataSource(),n=this.popupListItems;!t||!n||!n.length||n.length<1?this._targetElementBinding():this._isPlainType(n)?this._plainArrayTypeBinding(n):this._objectArrayTypeBinding(n)},_isPlainType:function(n){return typeof n[0]!="object"},_objectArrayTypeBinding:function(i,r){var s,h,u,o,f,e;if(this.dummyUl=n(),this._mapFields(),this.model.enableSorting&&(s=t.Query().sortBy(this.mapFld._text,this.model.sortOrder,!0),i=t.DataManager(i).executeLocal(s),this.popupListItems=i),this.model.allowGrouping||this.model.fields.groupBy)for(t.isNullOrUndefined(this.popupListWrapper)||this.popupListWrapper.addClass("e-atc-popup"),h=this.mapFld._category,o=t.Query().group(h),this.model.enableSorting||o.queries.splice(0,1),u=t.DataManager(i).executeLocal(o),this._swapUnCategorized(u),r=="search"?this.popupListItems=[]:this._listItem([]),f=0;f<u.length;f++)for(u[f].key&&this.ultag.append(t.buildTag("li.e-category",u[f].key).attr("role","option")[0]),this._generateLi(u[f].items,this.mapFld),this.ultag.append(this.dummyUl),e=0;e<u[f].items.length;e++)r=="search"?this.popupListItems.push(u[f].items[e]):this._listItem(u[f].items[e],"add");else this._generateLi(i,this.mapFld),this.ultag.append(this.dummyUl);this._trigger("dataBound",{data:i})},_onScroll:function(i){var e,o,f;if(i.scrollTop){var s=i.scrollTop,r=this,u=this._dataSource();r.model.allowVirtualScrolling&&r.model.virtualScrollMode=="continuous"?(f=t.Query().skip(r._rawList.length).take(r.model.itemsCount).clone(),s>=Math.round(n(r.popupList).find("ul,ol").height()-n(r.popupList).height())&&r._rawList.length<r._totalCount&&(r._addLoadingClass(),t.DataManager&&r._dataSource()instanceof t.DataManager&&!t.isNullOrUndefined(r._dataSource().dataSource.url)?(f=r.inputSearch&&r.inputSearch.val()!=""&&this.model.enableServerFiltering?r._addSearchQuery(t.Query(),!r._isPlainType(r._rawList)).skip(r._getLi().length).clone():r._getQuery().skip(r._rawList.length).take(r.model.itemsCount).clone(),r._trigger("actionBegin",{requestFrom:"scroll"})||(o=r._dataSource().executeQuery(f),o.done(function(n){!t.isNullOrUndefined(r.model.value)&&r.model.enableServerFiltering&&(n.result=r._removeSelectedValue(n.result));r.addItem(n.result);r._removeLoadingClass();r._trigger("actionSuccess",{e:n,requestFrom:"scroll"})}).fail(function(){r._dataSource(null);r._removeLoadingClass();r._trigger("actionFailure",{e:i,requestFrom:"scroll"})}).always(function(n){r._trigger("actionComplete",{e:n,requestFrom:"scroll"})}))):t.DataManager&&u instanceof t.DataManager&&u.dataSource.offline&&u.dataSource.json&&u.dataSource.json.length>0?(r.addItem(this._localDataVirtualScroll()),window.setTimeout(function(){r._removeLoadingClass()},100)):(e=t.DataManager(r._dataSource()).executeLocal(f),r.addItem(r._removeSelectedValue(e)),r._removeLoadingClass()))):r.model.allowVirtualScrolling&&r.model.virtualScrollMode=="normal"&&window.setTimeout(function(){r._virtualCount==0&&r._loadList()},300)}},_localDataVirtualScroll:function(){var n=this,i=t.isNullOrUndefined(n.value())?0:typeof n.value()=="number"?1:n.value().split(n.model.delimiterChar).length,r=n._checkValue?n._rawList.length-i:n._rawList.length,u=t.DataManager(n._dataSource().dataSource.json).executeLocal(t.Query().skip(r).take(n.model.itemsCount).clone());return n._removeSelectedValue(u)},_removeSelectedValue:function(n){var u,r,i;if(t.isNullOrUndefined(this.value()))return n;for(u=typeof this.value()=="number"?this.value():this.value().split(this.model.delimiterChar),r=0;r<u.length;r++)for(i=0;i<n.length;i++)n[i][this.mapFld._value]==u[r]&&n.splice(n.indexOf(n[i]),1);return n},_loadList:function(){var r,h,c,l,a,u;this._virtualCount++;var e=this._dataSource(),v=this.scrollerObj.scrollTop(),i=this,o=0,f,s=null;if(this._currentPage=Math.round(v/(29*this.model.itemsCount)),this._virtualPages){if(n.inArray(this._currentPage,this._virtualPages.sort(function(n,t){return n-t}))!=-1)if(this._currentPage==0){if(n.inArray(this._currentPage+1,this._virtualPages)!=-1)return this._virtualCount--,!1;this._currentPage=this._currentPage+1}else if(n.inArray(this._currentPage-1,this._virtualPages)!=-1){if(n.inArray(this._currentPage+1,this._virtualPages)!=-1)return this._virtualCount--,!1;this._currentPage=this._currentPage+1}else this._currentPage=this._currentPage-1;for(f=!(n.inArray(this._currentPage-1,this._virtualPages)!=-1),this._addLoadingClass(),r=this._virtualPages.length-1;r>=0;r--)if(this._virtualPages[r]<this._currentPage){o=this._virtualPages[r];r+1==this._virtualPages.length||(s=this._virtualPages[r+1]);break}}if(h=f?(this._currentPage-1)*this.model.itemsCount:this._currentPage*this.model.itemsCount,u=t.Query().range(h,this._currentPage*this.model.itemsCount+this.model.itemsCount),t.DataManager&&i._dataSource()instanceof t.DataManager&&!t.isNullOrUndefined(i._dataSource().dataSource.url)){if(a=f?(this._currentPage-1)*this.model.itemsCount:this._currentPage*this.model.itemsCount,u=this._getQuery().skip(a),f){for(r=0;r<u.queries.length;r++)if(u.queries[r].fn=="onTake"){u.queries.splice(r,1);break}u.take(2*this.model.itemsCount)}i._trigger("actionBegin",{requestFrom:"scroll"})||(i.inputSearch&&i.inputSearch.val()!=""&&this.model.enableServerFiltering&&(u=i._addSearchQuery(t.Query(),!i._isPlainType(i._rawList)).skip(i._getLi().length).clone()),c=i._dataSource().executeQuery(u),c.done(function(n){n.result=i._removeSelectedValue(n.result);i._appendVirtualList(n.result,o,i._currentPage,s,f);i._removeLoadingClass();i._trigger("actionSuccess",{e:n,requestFrom:"scroll"})}).fail(function(n){i._virtualCount--;i._removeLoadingClass();i._trigger("actionFailure",{e:n,requestFrom:"scroll"})}).always(function(n){i._trigger("actionComplete",{e:n,requestFrom:"scroll"})}))}else t.DataManager&&e instanceof t.DataManager&&e.dataSource.offline&&e.dataSource.json&&e.dataSource.json.length>0?(i._appendVirtualList(this._localDataVirtualScroll(),o,i._currentPage,s,f),window.setTimeout(function(){i._removeLoadingClass()},100)):(l=t.DataManager(i._dataSource()).executeLocal(u),i._appendVirtualList(i._removeSelectedValue(l),o,i._currentPage,s,f),i._removeLoadingClass())},_appendVirtualList:function(i,r,u,f,e){var o,h,l,s,a,c;if(this._virtualPages){if(this._virtualCount--,this.ultag.find("span.e-virtual").remove(),t.isNullOrUndefined(this.activeItem)||this.activeItem.attr("page","0"),n.inArray(u,this._virtualPages.sort(function(n,t){return n-t}))!=-1)return!1;for(e&&n.inArray(u-1,this._virtualPages.sort())!=-1&&(i.splice(0,this.model.itemsCount),e=!1),o=this.model.itemsCount,h=n("<ul>"),l=e?(u-1)*o*29-(r*o+o)*29:u*o*29-(r*o+o)*29,l!=0&&h.append(n("<span>").addClass("e-virtual").css({display:"block",height:l})),this._mapFields(),this._generateLi(i,this.mapFld),n(this.dummyUl).attr("page",u),e&&n(this.dummyUl).slice(0,o).attr("page",u-1),this.model.showCheckbox&&this._appendCheckbox(this.dummyUl),h.append(this.dummyUl),s=(u*o+o)*29,s=f!=null?f*o*29-s:this.ultag.height()-s,s!=0&&h.append(n("<span>").addClass("e-virtual").css({display:"block",height:s})),a=this.model.itemsCount>0&&this.value()!=""&&this._dataSource()instanceof t.DataManager&&this._dataSource().dataSource.offline?this.ultag.find("li").last():this.ultag.find("li[page="+r+"]").last(),a.next().remove(),h.children().insertAfter(a),this._virtualPages.push(u),e&&this._virtualPages.push(u-1),c=0;c<i.length;c++)this._listItem(i[c],"add");this._virtualUl=this.ultag.clone(!0);this._renderBoxModel()}},_generateLi:function(i,r){var u,s,p,c;if(this.mapFld=r,this.dummyUl=[],!i||!i.length||i.length<1)return!1;for(u=0;u<i.length;u++){var e=this._getField(i[u],this.mapFld._id),l=this._getField(i[u],this.mapFld._imageUrl),a=this._getField(i[u],this.mapFld._imageAttributes),v=this._getField(i[u],this.mapFld._spriteCSS),o=this._getField(i[u],this.mapFld._text),h=this._getField(i[u],this.mapFld._value),y=this._getField(i[u],this.mapFld._htmlAttributes),w=this._getField(i[u],this.mapFld._selected),f=document.createElement("li");f.setAttribute("data-uid",Math.random().toString(36).substr(2,4));t.isNullOrUndefined(h)?f.setAttribute("data-value",o):f.setAttribute("data-value",typeof h=="object"?JSON.stringify(h):h);t.isNullOrUndefined(e)||e===""&&e==0||f.setAttribute("id",e);t.isNullOrUndefined(y)||y==""||this._setAttr(f,y);(t.isNullOrUndefined(e)||e==""&&e!=0)&&this._setAttr(f,{id:this._id+"_"+u+"_popup_option","aria-selected":!1,tabindex:-1});this.model.template?n(f).append(this._getTemplatedString(i[u])):(t.isNullOrUndefined(l)||l==""||(s=document.createElement("img"),this._setClass(s,"e-align")._setAttr(s,{src:l,alt:o}),a&&a!=""&&this._setAttr(s,a),f.appendChild(s)),t.isNullOrUndefined(v)||v==""||(p=document.createElement("div"),this._setClass(p,"div.e-align "+v+" sprite-image"),f.appendChild(p)),w&&this._setClass(f,"chkselect"),t.isNullOrUndefined(o)&&(o=String(o)),c=document.createElement("span"),c.appendChild(document.createTextNode(o)),this._setClass(c,"e-ddltxt"),f.appendChild(c));this._setAttr(f,{role:"option",unselectable:"on"});this.dummyUl.push(f)}},_setAttr:function(t,i){var u,r,f;if(typeof i=="string")u=i.replace(/['"]/g,"").split("="),u.length==2&&(t.hasAttribute(u[0])&&u[0]=="class"?n(t).addClass(u[1]):t.setAttribute(u[0],u[1]));else for(r in i)if((r=="styles"||r=="style")&&typeof i[r]=="object")for(f in i[r])t.style[f]=i[r][f];else t.setAttribute(r,i[r]);return this},_setClass:function(n,t){return n.className+=" "+t,this},_removeClass:function(n,t){var i=n.className.indexOf(t);return i>=0&&(n.className=i!=0&&n.className[i-1]===" "?n.className.replace(" "+t,""):n.className.replace(t,"")),this},_hasClass:function(n,t){return n.className.indexOf(t)>=0},_swapUnCategorized:function(t){n(t).each(function(n,i){if(!i.key){for(var r=n;r>0;r--)t[r]=t[r-1];return t[r]=i,!1}})},_getField:function(n,i){return t.pvt.getObject(i,n)},_getTemplatedString:function(n){for(var t=this.model.template,i=t.indexOf("${"),r=t.indexOf("}"),u,f;i!=-1&&r!=-1;)u=t.substring(i,r+1),f=u.replace("${","").replace("}",""),t=t.replace(u,this._getField(n,f)),i=t.indexOf("${"),r=t.indexOf("}");return t},_setWatermark:function(){if(this.element.val()==""&&this._trim(this._visibleInput.val())==""){var n=this.model.watermarkText!=null?this.model.watermarkText:this._localizedLabels.watermarkText;this._isWatermark?this._visibleInput.attr("placeholder",n):this._hiddenSpan.css("display","block").text(n);this.model.multiSelectMode=="visualmode"&&this._ulBox&&this._ulBox.find("li").length==0&&this._swapUlandInput(!1)}},_checkboxHideShow:function(n){if(!t.isNullOrUndefined(this.ultag)){if(n){this.listitems=this._getLi();var i=this.listitems.find("input[type=checkbox]");i.length==0&&this._appendCheckbox(this.listitems)}else this._removeCheck(this.popupList);this.model.showCheckbox=n;this._virtualUl=this.ultag.clone(!0)}},_setCheckAll:function(n){!this._isSingleSelect()&&n?this.checkAll():this.model.checkAll=!1},_setUncheckAll:function(n){!this._isSingleSelect()&&n?this.uncheckAll():this.model.uncheckAll=!1},checkAll:function(){var f,r,i,u,e;if(this.model.loadOnDemand&&(t.isNullOrUndefined(this.ultag)||this.ultag.children().length==0)&&this._showFullList(),f=this._selectedIndices,r=!1,this._mapFields(),this.listitems=this._getLi(),this._isWatermark||this._hiddenSpan.css("display","none"),!this._isSingleSelect()){for(i=0;i<this.listitems.length;i++)this._currentText=this._isPlainType(this.popupListItems)?this.popupListItems[i]:this._decode(this._getField(this.popupListItems[i],this.mapFld._text)),this._hiddenValue=this._getAttributeValue(this.listitems[i])||this._currentText,this._checkContains(this._hiddenValue)?r=!0:this.model.showCheckbox?(u=n(this.listitems[i]).children(".e-checkwrap")[0],u&&!this._isChecked(u)?(this._setClass(u,"e-check-act"),this._setAttr(u,{"aria-checked":!0}),u.firstChild.checked=!0,r=!1):r=!0):(r=!1,n(this.listitems[i]).addClass("e-active")),r||(this.checkedStatus=!0,this._itemID=n(this.listitems[i]).attr("id"),t.isNullOrUndefined(this._itemID)||this._itemID==""&&this._itemID!=0||this._selectedItemsID.push(this._itemID),this._createListHidden(this._hiddenValue),this.model.multiSelectMode=="visualmode"&&(this._ulBox.append(this._createBox(this._currentText,this._hiddenValue)),this._isPopupShown()&&this._setListPosition()),this._addText(this._currentText),n.inArray(i,f)==-1&&(this._selectedIndices.push(i),this.model.selectedItems=this.model.selectedIndices=this._selectedIndices),this._selectedValue=this._getAttributeValue(this.listitems[i])||"",this._initValue||this._trigger("checkChange",{isChecked:this.checkedStatus,data:this.model}),e={text:this._visibleInput[0].value,selectedText:this._currentText,itemId:i,selectedValue:this._selectedValue,value:this._selectedValue,isChecked:this.checkedStatus},this._updateValue(this.element.val()),this._updateText(),this._initValue||this._trigger("change",e),this._activeItem=i,this.activeItem=this._getActiveItem(),this._cascadeAction());this.model.itemValue=this._selectedValue;this.model.uncheckAll=!1;this.model.checkAll=!0;this._activeItem=-1;this._setWatermark()}},_createListHidden:function(t){var i=document.createElement("input"),r=/'/;r.test(t)&&(t=t.replace(r,"&apos;"));this._setAttr(i,{type:"hidden",name:this._name,value:t,id:"#"+t});n.inArray(i.value,this._hiddenInputElement)==-1&&(this._hiddenInputElement.push(i.value),this._boxValue.push(i.value),this.container.append(i),n(this.element).attr("name","hiddenEle"))},_removeListHidden:function(t){var u=/'/,i,r;u.test(t)&&(t=t.replace(u,"&apos;"));i=this.container.find("[id='#"+t+"']");this._hiddenInputElement.splice(n.inArray(i.value,this._hiddenInputElement),1);r=this._boxValue.indexOf(t);r!=-1&&this._boxValue.splice(r,1);n(i).remove();this._isSingleSelect()||this.element.val()!=""||this.element.attr("name",this._name)},_getAttributeValue:function(n){if(!t.isNullOrUndefined(n))var i=t.isNullOrUndefined(n.getAttribute("data-value"))?n.getAttribute("value"):n.getAttribute("data-value");return n?i:null},_selectCheckedItem:function(n){this.model.loadOnDemand&&(t.isNullOrUndefined(this.ultag)||this.ultag.children().length==0)&&this._showFullList();for(var i=0;i<n.length;i++)this._activeItem=n[i],this._enterTextBoxValue()},unCheckAll:function(){this.uncheckAll()},uncheckAll:function(){var u=!1,i,r,f,e;if(this.listitems=this._getLi(),this._mapFields(),!this._isSingleSelect()){for(i=0;i<this.listitems.length;i++)this._currentText=this._isPlainType(this.popupListItems)?this.popupListItems[i]:this._getField(this.popupListItems[i],this.mapFld._text),this._hiddenValue=this._getAttributeValue(this.listitems[i])||this._currentText,this._checkContains(this._hiddenValue)?this.model.showCheckbox?(r=n(this.listitems[i]).children(".e-checkwrap")[0],r&&this._isChecked(r)?(this._removeClass(r,"e-check-act"),this._setAttr(r,{"aria-checked":!1}),r.firstChild.checked=!1,u=!0):u=!1):(n(this.listitems[i]).removeClass("e-active"),u=!0):u=!1,u&&(this.checkedStatus=!1,this._activeItem=i,this.activeItem=this._getActiveItem(),this._removeText(this._hiddenValue),this._removeListHidden(this._hiddenValue),f=this._selectedIndices,n.inArray(i,f)>-1&&(this._selectedIndices.splice(n.inArray(i,f),1),this.model.selectedItems=this.model.selectedIndices=this._selectedIndices),this._selectedValue=this._getAttributeValue(this.listitems[i])||"",this._itemID=n(this.listitems[i]).attr("id"),t.isNullOrUndefined(this._itemID)||this._itemID==""&&this._itemID!=0||this._removeSelectedItemsID(),this._initValue||this._trigger("checkChange",{isChecked:this.checkedStatus,data:this.model}),e={text:this._visibleInput[0].value,selectedText:this._currentText,itemId:i,selectedValue:this._selectedValue,value:this._selectedValue,isChecked:this.checkedStatus},this._updateValue(this.element.val()),this._updateText(),this._initValue||this._trigger("change",e),this._cascadeAction(),this.model.multiSelectMode=="visualmode"&&(this._deleteBoxCheck(this._hiddenValue),this._isPopupShown()&&this._setListPosition()));this.model.itemValue=this._selectedValue;this.model.checkAll=!1;this.model.uncheckAll=!0;this._setWatermark();this._activeItem=-1;this._clearTextboxValue()}},_removeSelectedItemsID:function(){var n;n=this._selectedItemsID.indexOf(this._itemID);this._selectedItemsID.splice(n,1);this._itemID=""},_refreshScroller:function(){if(!t.isNullOrUndefined(this.popupListWrapper)){this.model.enablePopupResize||(this.popupList.css("height","auto"),this.popupListWrapper.css("height","auto"));this.popupList.find(".e-content, .e-vhandle").removeAttr("style");this.popupList.find(".e-vhandle div").removeAttr("style");this.popupList.children(".e-content").removeClass("e-content");var n=this._isPopupShown();this.popupListWrapper.css("display","block");this.popupList.css({display:"block"});this.scrollerObj.model.height=Math.ceil(this._getPopupHeight());this.scrollerObj.refresh();this.model.enablePopupResize||(this.popupList.css("height","auto"),this.popupListWrapper.css("height","auto"));this.scrollerObj.option("scrollTop",0);n||this.popupListWrapper.css("display","none")}},_enablePopupResize:function(){this.model.enablePopupResize&&!t.isNullOrUndefined(this.popupListWrapper)&&(this.popupListWrapper.addClass("e-resizable").append(t.buildTag("div.e-resizebar").append(t.buildTag("div.e-icon e-resize-handle"))).find(".e-resize-handle").addClass(this.model.enableRTL?"e-rtl-resize":""),this._resizePopup())},_resizePopup:function(){var t=this,i=!1;this.popupListWrapper.find("div.e-resize-handle").ejResizable({minHeight:t._validatePixelData(t.model.minPopupHeight),minWidth:t._validatePixelData(t.model.minPopupWidth),maxHeight:t._validatePixelData(t.model.maxPopupHeight),maxWidth:t._validatePixelData(t.model.maxPopupWidth),handle:"e-ddl-popup",resizeStart:function(n){if(!t.model.enabled)return!1;i||t._trigger("popupResizeStart",{event:n});i=!0},resize:function(i){var r=n(i.element).parents("div.e-ddl-popup");t._refreshPopupOnResize(n(r).outerHeight(),n(r).outerWidth());t._trigger("popupResize",{event:i})},resizeStop:function(n){i&&(t._refreshPopupOnResize(t.model.popupHeight,t.model.popupWidth),i&&t._trigger("popupResizeStop",{event:n}),i=!1)},helper:function(i){var r=n(i.element).parents("div.e-ddl-popup");return t._refreshPopupOnResize(n(r).outerHeight(),n(r).outerWidth()),n(t.popupListWrapper)}})},_refreshPopupOnResize:function(n,t){n&&(this.model.popupHeight=n);t&&(this.model.popupWidth=t);this.popupListWrapper.css({height:this._validatePixelData(this.model.popupHeight),"min-height":this._validatePixelData(this.model.minPopupHeight),"max-height":this._validatePixelData(this.model.maxPopupHeight)});this._setListWidth();this._refreshScroller()},_setListWidth:function(){if(!t.isNullOrUndefined(this.popupListWrapper)){var n=this.model.popupWidth;n!="auto"?this.popupListWrapper.css({width:n}):this.popupListWrapper.css({"min-width":this._validatePixelData(this.model.minPopupWidth)});this.popupListWrapper.css({"max-width":this._validatePixelData(this.model.maxPopupWidth)})}},_setListHeight:function(){t.isNullOrUndefined(this.popupListWrapper)||(this.model.enablePopupResize&&this.model.enableFilterSearch&&this.model.minPopupHeight&&this.model.minPopupHeight.toString().indexOf("%")<0&&this._validatePixelData(this.model.minPopupHeight)==20&&(this.model.minPopupHeight="65"),this.model.enablePopupResize?this.popupListWrapper.css({"min-height":this._validatePixelData(this.model.minPopupHeight),"max-height":this._validatePixelData(this.model.maxPopupHeight),height:this._validatePixelData(this.model.popupHeight)}):this.popupListWrapper.css({"max-height":this._validatePixelData(this.model.popupHeight),"min-height":this._validatePixelData(this.model.minPopupHeight)}))},_validatePixelData:function(n){return n&&!isNaN(n)?Number(n):n},_getPopupHeight:function(){var n=this.popupListWrapper.height(),t;return this.model.enablePopupResize&&(n-=this.popupListWrapper.find(">div.e-resizebar").height()),this.model.headerTemplate&&this.headerTemplate&&(n-=this.headerTemplate.height()),this.model.enableFilterSearch&&this.inputSearch&&(t=this.inputSearch.parent(".e-in-wrap"),n-=parseInt(t.css("height"))+parseInt(t.css("margin-top"))+parseInt(t.css("margin-bottom"))),n},_refreshPopup:function(){this.model.popupWidth!="auto"||this._validatePixelData(this.model.minPopupWidth)?this._validatePixelData(this.model.minPopupWidth)&&this.popupListWrapper.css({"min-width":this._validatePixelData(this.model.minPopupWidth)}):this.popupListWrapper.css({"min-width":this.wrapper.width()});this.scrollerObj!=i&&this._refreshScroller();this._setListPosition()},_setListPosition:function(){var t=this.wrapper,i=this._getOffset(t),f,h=n(document).scrollTop()+n(window).height()-(i.top+n(t).outerHeight()),c=i.top-n(document).scrollTop(),u=this.popupListWrapper.outerHeight(),e=this.popupListWrapper.outerWidth(),r=i.left,o=t.outerHeight(),l=(o-t.height())/2,a=this._getZindexPartial(),s=3,v=(u<h||u>c?i.top+o+s:i.top-u-s)-l;f=n(document).scrollLeft()+n(window).width()-r;(this.model.enableRTL||e>f&&e<r+t.outerWidth())&&(r-=this.popupListWrapper.outerWidth()-t.outerWidth());this.popupListWrapper.css({left:r+"px",top:v+"px","z-index":a})},_getOffset:function(n){return t.util.getOffset(n)},_getZindexPartial:function(){return t.util.getZindexPartial(this.element,this.popupListWrapper)},_showResult:function(){var i=this,r;(this.popUpShow=!0,this.model.loadOnDemand&&(t.isNullOrUndefined(this.popupListWrapper)&&this._renderPopupPanelWrapper(),(t.isNullOrUndefined(this.ultag)||this.ultag.children().length==0)&&this._showFullList(),this._renderRemaining()),(this.model.allowGrouping||this.model.fields.groupBy)&&this.popupListWrapper.addClass("e-atc-popup"),r={text:this._visibleInput[0].value,value:this._selectedValue,refreshPopup:!0},this._trigger("beforePopupShown",r))||(r.refreshPopup&&this._refreshPopup(),n(this.popupListWrapper).slideDown(this.model.enableAnimation?200:1,function(){n(document).on("mousedown",n.proxy(i._OnDocumentClick,i));t.isDevice()||i._on(t.getScrollableParents(i.wrapper),"scroll",i._hideResult)}),this._setAttr(this.wrapper[0],{"aria-expanded":!0}),this._setAttr(this.popupListWrapper[0],{"aria-hidden":!1}),this._setAttr(this.ultag[0],{"aria-hidden":!1}),this._listSize=this._getLi().length,this.wrapper.addClass("e-popactive"),this._trigger("popupShown",{text:this._visibleInput[0].value,value:this._selectedValue}),this.scrollerObj.setModel({scrollTop:this._calcScrollTop("active")}))},_OnWindowResize:function(){!t.isNullOrUndefined(this.model)&&this._isPopupShown()&&this._refreshPopup()},_hideResult:function(i){if(this.model&&this._isPopupShown()){if(!t.isNullOrUndefined(i)&&!t.isNullOrUndefined(this.inputSearch)&&n(this.inputSearch).is(":focus")&&i.type=="scroll"&&t.isTouchDevice())return!1;var r=this;if(this._trigger("beforePopupHide",{text:this._visibleInput[0].value,value:this._selectedValue}))return;n(this.popupListWrapper).slideUp(this.model.enableAnimation?100:0,function(){n(document).off("mousedown",n.proxy(r._OnDocumentClick,r))});this.element!=null&&this._setAttr(this.wrapper[0],{"aria-expanded":!1});this.popupListWrapper[0]&&this._setAttr(this.popupListWrapper[0],{"aria-hidden":!0});this.ultag[0]&&this._setAttr(this.ultag[0],{"aria-hidden":!0});t.isDevice()||this._off(t.getScrollableParents(this.wrapper),"scroll",this._hideResult);this._visibleInput!=null&&this.wrapper.removeClass("e-popactive");this.popUpShow=!1;this._trigger("popupHide",{text:this._visibleInput[0].value,value:this._selectedValue});setTimeout(function(){r._resetSearch()},100);this._getLi().find(".e-ddl-anim").removeClass("e-ddl-anim")}},_isPopupShown:function(){if(!t.isNullOrUndefined(this.popupListWrapper))return this.popupListWrapper.css("display")=="block"},_enterTextBoxValue:function(t){var u,f=!0,i,r;if(this.removeID=!1,this.checkedStatus=!1,this._isWatermark||this._hiddenSpan.css("display","none"),this._chooseSelectionType(),(this._activeItem>=0||this._activeItem!=null)&&(this.model.showCheckbox?(i=this.activeItem.find(".e-checkwrap")[0],n(i).removeClass("e-check-inact"),this.checkedStatus=this._isChecked(i)):this.checkedStatus=this.activeItem.hasClass("e-active")),u={text:this._currentText,selectedText:this._currentText,itemId:this.selectedIndexValue,value:this._selectedValue,isChecked:this.checkedStatus,isInteraction:!!this._uiInteract},!this._initValue&&!this._onSearch&&this._raiseEvents&&this._trigger("select",u)){this._setWatermark();return}(this._activeItem>=0||this._activeItem!=null)&&(!this._isSingleSelect()&&(!this._checkContains(this._selectedValue)||this._checkedValues.length>0)?(this.model.showCheckbox?(i=this.activeItem.find(".e-checkwrap")[0],this._isChecked(i)||(this._removeClass(i,"e-ddl-anim"),this._setClass(i,"e-check-act e-ddl-anim"),this._setAttr(i,{"aria-checked":!0}),n(i).find(".e-check-input")[0].checked=!0)):this.activeItem.addClass("e-active"),this.model.multiSelectMode=="visualmode"&&(r=this._createBox(this._currentText,this._selectedValue),(this._ulBox.children()&&this._ulBox.children().length>0?this._checkForDuplicates(r):!0)&&this._ulBox.append(r),this._isPopupShown()&&this._setListPosition()),this._maintainHiddenValue(),this._addText(this._currentText),this._isSingleSelect()||JSON.stringify(this._boxValue)==JSON.stringify(this._valueContainer)||this._createListHidden(this._hiddenValue),n.inArray(this.selectedIndexValue,this._selectedIndices)==-1&&(this._selectedIndices.push(this.selectedIndexValue),this.model.selectedItems=this.model.selectedIndices=this._selectedIndices)):this._isSingleSelect()?(this.ultag.children("li").removeClass("e-hover").removeClass("e-active"),this.activeItem.addClass("e-active"),this._maintainHiddenValue(),this._visibleInput.val(this._currentText),this.element.val(this._hiddenValue),this.selectedItemIndex(this.selectedIndexValue),this.selectedIndex(this.selectedIndexValue),this._selectedIndices[0]=this.selectedIndexValue):f=!1,f&&(this.checkedStatus=!0,t&&this._checkedValues.push(this.activeItem.text()),this._onValueChange(),this._cascadeAction(),this.selectelement&&n("#"+this._id).children().length>this.selectedIndexValue&&(n("#"+this._id).children()[this.selectedIndexValue].selected=!0)));this.model.uncheckAll=!1;this._setWatermark();this._uiInteract=!1},_checkForDuplicates:function(t){var i=!0,r=this._ulBox.children().toArray();return r.forEach(function(r){if(t.attr("data-value")==n(r).attr("data-value"))return i=!1,!1},this),i},_onValueChange:function(){if(this.model.itemValue=this._selectedValue,this._updateText(),this._isSingleSelect()||JSON.stringify(this._boxValue)==JSON.stringify(this._valueContainer)||(this._valueContainer=this._boxValue),this._visibleInput.val()==""&&this.element.val()&&this.element.val(""),this.value()!=this.element.val()||this.value()==null&&n.inArray("",this._valueContainer)!=-1){t.isNullOrUndefined(n(this.getSelectedItem()).last()[0])||(this.model.enableFilterSearch&&this.model.enableServerFiltering?this._updateSelectedIndex(this.element.val(),n(this.getSelectedItem()).last().attr("data-uid")):this._updateSelectedIndexByValue(this.element.val()));this._isSingleSelect()?this._updateValue(this.element.val()):this._updateValue(this._valueContainer.toString());this.selectedTextValue=this.model.text;this.model.showCheckbox||this.model.multiSelectMode!="none"||this.model.value!=null&&this.model.value!=""||(this.model.itemValue="");var i={text:this._visibleInput[0].value,selectedText:this._currentText,itemId:this.selectedIndexValue,selectedValue:this._selectedValue,value:this._selectedValue,isChecked:this.checkedStatus,isInteraction:!!this._uiInteract};this._initValue||this._onSearch||!this._raiseEvents||(typeof this.model._change=="function"&&this._trigger("_change",{isChecked:this.checkedStatus,text:this._visibleInput.val(),itemId:this.selectedIndexValue,selectedText:this._currentText,selectedValue:this._selectedValue,value:this.value(),data:this.model,isInteraction:!!this._uiInteract}),this._trigger("change",i),this.model.showCheckbox&&this._trigger("checkChange",{isChecked:this.checkedStatus,text:this._visibleInput.val(),itemId:this.selectedIndexValue,selectedText:this._currentText,selectedValue:this._selectedValue,value:this._selectedValue,data:this.model}));this._uiInteract=!1;this.model.enableFilterSearch&&this.model.enableServerFiltering&&(this._searchresult=[],this._searchresult=this.getItemDataByValue(this.value()))}},_updateSelectedIndex:function(t,i){var f,u,r;if(t){for(this._selectedIndices=this.model.selectedItems=this.model.selectedIndices=[],this._virtualList=this._virtualUl.children("li:not('.e-category')"),f=this._toArray(t),u=0;u<f.length;u++)for(r=0;r<this._virtualList.length;r++)if(f[u]==this._getIndexedValue(this._virtualList[r])&&i==n(this._virtualList[r]).attr("data-uid")){this._selectedIndices.push(r);break}this.model.selectedItems=this.model.selectedIndices=this._selectedIndices}},_decode:function(t){return n("<span>").html(t).text()},_chooseSelectionType:function(){this.activeItem=this._getActiveItem();this.selectedIndexValue=this._activeItem;this._mapFields();var i=/([^"]*)\&[gl]t;([^"]*)/g;this._dataSource()==null||this._isPlainType(this._dataSource())&&this._isPlainType(this.popupListItems)?(this._currentText=this.activeItem.text(),this._getAttributeValue(this.activeItem[0])?this._selectedValue=this._getAttributeValue(this.activeItem[0]):this._currentText!=null?(t.isNullOrUndefined(this.activeItem[0])||this.activeItem[0].setAttribute("value",this._currentText),this._selectedValue=this._currentText):this._selectedValue="",this._itemID=n(this.activeItem).attr("id")):(this._currentText=this.model.enableFilterSearch&&this.ultag.children()[this._activeItem].textContent==this.activeItem.text()?i.test(this.ultag.children()[this._activeItem].textContent)?this._decode(this.ultag.children()[this._activeItem].textContent):this.ultag.children()[this._activeItem].textContent:i.test(this._getField(this.popupListItems[this._activeItem],this.mapFld._text))?this._decode(this._getField(this.popupListItems[this._activeItem],this.mapFld._text)):this._getField(this.popupListItems[this._activeItem],this.mapFld._text),this._currentText=this._currentText===""||this._currentText==null?this.activeItem.text():this._currentText,this._selectedValue=this._getField(this.popupListItems[this._activeItem],this.mapFld._value),this._selectedValue=this._selectedValue!=null?this._selectedValue:this._currentText,this._itemID=this._getField(this.popupListItems[this._activeItem],this.mapFld._id));t.isNullOrUndefined(this._itemID)||this._itemID==""&&this._itemID!=0||(this.model.showCheckbox?this.removeID?this._removeSelectedItemsID():this._selectedItemsID.push(this._itemID):(this._selectedItemsID=[],this.removeID||this._selectedItemsID.push(this._itemID)));this.selectedTextValue=this._currentText},_maintainHiddenValue:function(){var n=this._getAttributeValue(this.activeItem[0]);this._hiddenValue=t.isNullOrUndefined(n)?this._currentText:n},_removeTextBoxValue:function(i){var u,f,r;if(this._uiInteract=!0,this.removeID=!0,this.checkedStatus=!0,this._isFilterInput())for(u=0;u<this._getLi().length;u++)n(this._getLi()[u]).attr("data-value")==i&&(this._activeItem=u);if(this._chooseSelectionType(),(this._activeItem>=0||this._activeItem!=null)&&(this.model.showCheckbox?(r=this.activeItem.find(".e-checkwrap")[0],n(r).removeClass("e-ddl-anim").addClass("e-check-inact e-ddl-anim"),this.checkedStatus=this._isChecked(r)):this.checkedStatus=this.activeItem.hasClass("e-active")),f={text:this._currentText,selectedText:this._currentText,itemId:this.selectedIndexValue,value:this._selectedValue,isChecked:this.checkedStatus},!this._initValue&&!this._onSearch&&this._raiseEvents&&this._trigger("select",f)){this._setWatermark();return}this._maintainHiddenValue();this._hiddenValue=this._isFilterInput()&&!t.isNullOrUndefined(i)?i:this._hiddenValue;this._removeText(this._hiddenValue);this._isSingleSelect()||JSON.stringify(this._boxValue)==JSON.stringify(this._valueContainer)||this._removeListHidden(this._hiddenValue);this._isFilterInput()&&!this.activeItem.attr("data-value")==i&&this.activeItem.removeClass("e-active");this._isSingleSelect()||(this.model.showCheckbox?(r=this.activeItem.find(".e-checkwrap")[0],this._isChecked(r)&&(this._removeClass(r,"e-check-act"),this._setAttr(r,{"aria-checked":!1}),n(r).find(".e-check-input")[0].checked=!0)):this.activeItem.removeClass("e-active"),n.inArray(this.selectedIndexValue,this._selectedIndices)>-1&&(this._selectedIndices.splice(n.inArray(this.selectedIndexValue,this._selectedIndices),1),this.model.selectedItems=this.model.selectedIndices=this._selectedIndices),this.model.multiSelectMode=="visualmode"&&(this._deleteBoxCheck(this._hiddenValue),this._isPopupShown()&&this._setListPosition()));this.checkedStatus=!1;this._onValueChange();this.model.cascadeTo==null||this._isSingleSelect()||this._initValue||this._cascadeAction();this.model.checkAll=!1;this._setWatermark()},_createBox:function(i,r){if(!this._checkContains(r)||this._checkedValues.length>0){this._ulBox.css("display")=="none"&&this._visibleInput.css("display")!="none"&&this._swapUlandInput(!0);var u=t.buildTag("span.e-icon e-close","",{},{unselectable:"on"}),f=t.buildTag("li.e-options").text(i).attr("data-value",r).append(u);return this._on(u,"click",function(t){if(!this.model.enabled)return!1;this.delIconClick=!0;this._deleteBox(n(t.target).parent());this.delIconClick=!1}),f}},_deleteBoxCheck:function(t){for(var r=this._ulBox.children("li"),i=0;i<r.length;i++)n(r[i]).attr("data-value")==t&&(n(r[i]).remove(),this._checkedValues.splice(i,1))},_deleteLastBox:function(){var t=this._ulBox.children("li:not(.e-search-box)"),n=t.last();n.hasClass("e-active")?this._deleteBox(n):(this._removeActive(),n.addClass("e-active"))},_deleteBox:function(t){for(var f,r,o,e,i,u=0;u<t.length;u++)if(f=n(t[u]).attr("data-value"),this._isFilterInput()){for(r=this.getListData(),i=0;i<r.length;i++)if(o=this._getField(r[i],this.mapFld._value)?this._getField(r[i],this.mapFld._value):this._getField(r[i],this.mapFld._text),o==f){this._removeTextBoxValue(f);break}this._resetSearch()}else for(e=this._getLi(),i=0;i<e.length;i++)if(n(e[i]).attr("data-value")==f){this._activeItem=i;this._removeTextBoxValue();break}this._isFocused||this._isPopupShown()||this._setWatermark()},_isFilterInput:function(){if(this.model.enableFilterSearch&&this.inputSearch){if(!this.inputSearch.val()=="")return!0}else return!1},_swapUlandInput:function(n){n?(this._visibleInput.css("display","none"),this._ulBox.css("display","block"),this.wrapper.css({height:"auto"})):(this._visibleInput.css("display","block"),this._ulBox.css("display","none"),this.wrapper.css({height:this.model.height}))},_removeActive:function(){this._ulBox.children("li").removeClass("e-active")},_adjustWidth:function(){var n=t.buildTag("span",this._visibleInput.val());this.container.append(n);this._visibleInput.width(n.width()+30);n.remove()},_destroyBoxModel:function(){this._visibleInput.css("display","block");this.wrapper.height(this.model.height);this._ulBox.remove();this._ulBox=null;this._off(this.container,"click")},_removeListHover:function(){this.ultag.children("li").removeClass("e-hover")},_addListHover:function(){var n=this._getActiveItem();this._setAttr(n[0],{"aria-selected":!0});this._setAttr(this.wrapper[0],{"aria-activedescendant":n[0].id.toString(),"aria-labelledby":n[0].id.toString()});n.addClass("e-hover");this.scrollerObj.setModel({scrollTop:this._calcScrollTop("hover")})},_getLi:function(){if(this.ultag)return this.ultag.children("li:not('.e-category'):not('.e-nosuggestion')")},_calcScrollTop:function(t){var s=this.ultag.outerHeight(),u=this.ultag.find("li"),o=0,i,f,r,e;for(this._selectedIndices&&this._selectedIndices.length>0&&t=="active"?(e=this._getLi(),i=this._selectedIndices.length==e.length?0:this._selectedIndices[this._selectedIndices.length-1],(this.model.fields.groupBy!=null||this.ultag.find("li.e-category").length>0)&&(i=n.inArray(e.eq(i)[0],u))):i=this.ultag.find("li.e-"+t).index(),r=0;r<i;r++)o+=u.eq(r).outerHeight(!0);return f=o-(this.popupList.outerHeight()-u.eq(i).outerHeight(!0))/2,f<0?0:f},_getActiveItem:function(){return this._getLi().eq(this._activeItem)},_setDimentions:function(){this.model.height&&this.wrapper.height(this.model.height);this.model.width&&this.wrapper.width(this.model.width)},_roundedCorner:function(n){n?(this.container.addClass("e-corner"),t.isNullOrUndefined(this.popupListWrapper)||this.popupListWrapper.addClass("e-corner"),this.inputSearch&&this.inputSearch.parent(".e-in-wrap").addClass("e-corner")):(this.container.removeClass("e-corner"),t.isNullOrUndefined(this.popupListWrapper)||this.popupListWrapper.removeClass("e-corner"),this.inputSearch&&this.inputSearch.parent(".e-in-wrap").removeClass("e-corner"))},_enabled:function(n){n?this.enable():this.disable()},_RightToLeft:function(){this.model.enableRTL?this.wrapper.addClass("e-rtl"):this.wrapper.removeClass("e-rtl")},_dropbtnRTL:function(){this.model.enableRTL?(this.popupListWrapper.addClass("e-rtl").find(".e-resize-handle").addClass("e-rtl-resize"),this.popupList.addClass("e-rtl")):(this.popupListWrapper.removeClass("e-rtl").find(".e-resize-handle").removeClass("e-rtl-resize"),this.popupList.removeClass("e-rtl"))},_OnDropdownClick:function(i){if((this._preventDefaultAction(i),n(i.target).is("li")&&n(i.target).parent().hasClass("e-boxes")||n(i.target).parents("ul").hasClass("e-boxes")&&n(i.target).hasClass("e-icon e-close"))||this.model.readOnly||this._readOnly)return!1;var r=t.isNullOrUndefined(this.ultag)?this.model.loadOnDemand&&!t.isNullOrUndefined(this.model.dataSource)?!0:!1:this.ultag.find("li").length>0;r&&(i.which&&i.which==1||i.button&&i.button==0)&&this._OnPopupHideShow()},_OnPopupHideShow:function(){if(this._isPopupShown())this._hideResult();else if(this._showResult(),this.model.enableFilterSearch){if(this.getSelectedItem().length==0)this.ultag.find("li:first").addClass("e-hover");else{var t=this.getSelectedItem().length;n(this.getSelectedItem()[t-1]).addClass("e-hover")}n(this.inputSearch).focus()}else this.wrapper.focus()},_showFullList:function(){var n=this._dataSource(),i;t.isNullOrUndefined(this.ultag)&&(this.ultag=t.buildTag("ul.e-ul","",{},{role:"listbox"}));t.DataManager&&n instanceof t.DataManager?n.dataSource.offline||n.dataSource.json&&n.dataSource.json.length>0?this._getFilteredList(n.dataSource.json):(i=this,i._initDataSource(n)):this._getFilteredList(n)},_getFilteredList:function(n){if(!n||!n.length||n.length<1)this._targetElementBinding(),this._renderRemaining();else{var i=t.DataManager(n).executeLocal(this._isPlainType(n)?t.Query():this._getQuery(!0));this._totalCount=i.count;this._listItem(i.result?i.result:i);this._renderPopupList();this._rawList=this.popupListItems.slice()}},_cascadeAction:function(){if(this.model.cascadeTo)for(var r=this.model.cascadeTo.split(","),i=0;i<r.length;i++)if(n("#"+r[i]).hasClass("e-dropdownlist"))this._doCascadeAction(r[i],this,this.checkedStatus);else n("#"+r[i]).on("ejDropDownListcreate",{Obj:this,status:this.checkedStatus},function(n){t.isNullOrUndefined(n.data.Obj.getValue())||n.data.Obj.getValue()==""||n.data.Obj._doCascadeAction(this.id,n.data.Obj,n.data.status)})},_doCascadeAction:function(i,r,u){r._currentValue=r._getField(r.popupListItems[r._activeItem],r.mapFld._value);r.selectDropObj=n("#"+i).ejDropDownList("instance");var f={cascadeModel:r.selectDropObj.model,cascadeValue:r._currentValue,setCascadeModel:{},requiresDefaultFilter:!0};this._trigger("cascade",f);r.selectDropObj._setCascadeModel=f.setCascadeModel;t.isNullOrUndefined(r[i])&&(r[i]=r.selectDropObj._dataSource());t.DataManager&&r[i]instanceof t.DataManager?r._cascadeOdataInit(r[i],f.requiresDefaultFilter,u,f.cascadeQuery):r._cascadeJsonInit(r.selectDropObj,r[i],r.mapFld._value,f.requiresDefaultFilter,u,f.cascadeQuery)},_cascadeOdataInit:function(n,t,i,r){var u=this,e,f;u._dQuery=this.selectDropObj._getQuery().clone();f=u._dQuery.clone();t?f.where(u.mapFld._value,"equal",u._currentValue):f=r;u.selectDropObj._addLoadingClass();u._trigger("actionBegin",{requestFrom:"cascade"})||(e=n.executeQuery(f),e.fail(function(n){u._changedSource=null;u.selectDropObj.setModel({dataSource:u._changedSource,enabled:!1});u._trigger("actionFailure",{e:n,requestFrom:"cascade"})}).done(function(n){u._trigger("actionSuccess",{e:n,requestFrom:"cascade"});u._cascadeDataBind(u.selectDropObj,n.result,i);u.selectDropObj._removeLoadingClass()}).always(function(n){u._trigger("actionComplete",{e:n,requestFrom:"cascade"})}))},_cascadeJsonInit:function(n,i,r,u,f,e){var o=u?t.Query().where(r,"==",this._currentValue):e,s=t.DataManager(i).executeLocal(o);this._cascadeDataBind(n,s,f)},_cascadeDataBind:function(i,r,u){var l=i.value(),f,e,h,c,s,o;if(this.model.showCheckbox&&u||this.model.multiSelectMode!="none"&&this.activeItem.hasClass("e-active"))this._changedSource=t.isNullOrUndefined(this._changedSource)?r:this._changedSource.concat(r);else if(this.model.showCheckbox||this.model.multiSelectMode!="none"){for(f=0;f<r.length;f++)if(this._isPlainType(r)&&this._isPlainType(this._changedSource))this._changedSource.splice(this._changedSource.indexOf(r[f]),1);else for(e=0;e<this._changedSource.length;e++)JSON.stringify(this._changedSource[e])==JSON.stringify(r[f])&&this._changedSource.splice(e,1);i.setModel({dataSource:null})}else this._changedSource=r;if(h=i.model.value,c=JSON.parse(JSON.stringify(i.model)),i.setModel({dataSource:this._changedSource,enabled:this._changedSource.length>0}),this._isSingleSelect()||i.selectItemByValue(l),(i.model.showCheckbox||i.model.multiSelectMode!="none")&&n("input:hidden[id^='#'][name="+i._id+"]").remove(),i._setSelectedItem)i.setModel(i._setCascadeModel);else{for(s=["value","text","selectedIndex","selectedIndices"],o=0;o<s.length;o++)i.model[s[o]]=c[s[o]];i.model.value=h;i._finalize()}i._setSelectedItem=!0},_OnMouseEnter:function(t){var i,r;if(!this.model.enabled||this.model.readOnly)return!1;this.ultag.children("li").removeClass("e-hover");n(t.target).is("li:not('.e-category')")&&n(t.target).addClass("e-hover");n(t.target).hasClass("e-disable")?n(t.target).removeClass("e-hover"):t.target.tagName!="li"&&(i=n(t.target).parents("li:not('.e-category')"),n(i).addClass("e-hover"));this.ultag.children("li:not('.e-category')").each(function(t){if(n(this).hasClass("e-hover"))return r=t,!1});this._activeItem=r},_OnMouseLeave:function(){if(!this.model.enabled||this.model.readOnly||this._readOnly)return!1;this.ultag.children("li").removeClass("e-hover")},_OnMouseClick:function(t){var r,u,i;if((this._uiInteract=!0,!this.model.enabled||this.model.readOnly||this._readOnly)||this.model.enableFilterSearch&&n(t.target).is("li")&&n(t.target).hasClass("e-nosuggestion"))return!1;(!n(t.target).is("li")||n(t.target).hasClass("e-disable"))&&(n(t.target).is("li")||n(t.target).closest("li").hasClass("e-disable"))||(this._isSingleSelect()?(this._enterTextBoxValue(),this._hideResult()):this.model.showCheckbox?(r=t.target.nodeName==="LI"?t.target:n(t.target).parents("li.e-hover"),u=n(r).find(".e-checkwrap")[0],this._onCheckChange({target:u})):(i=n(t.target).is("li")?t.target:n(t.target).closest("li")[0],this._activeItem=n.inArray(i,this._getLi()),n(i).hasClass("e-active")?this._removeTextBoxValue():this._enterTextBoxValue()))},_OnDocumentClick:function(t){if(this.model&&(!this.model.enabled||this.model.readOnly||this._readOnly))return!1;n(t.target).is(this.popupList)||n(t.target).parents(".e-ddl-popup").is(this.popupListWrapper)||n(t.target).is(this._visibleInput)||n(t.target).parents(".e-ddl").is(this.wrapper)?n(t.target).is(this.inputSearch)?this.inputSearch.focus():(n(t.target).is(this.popupList)||n(t.target).parents(".e-ddl-popup").is(this.popupListWrapper))&&this._preventDefaultAction(t):this._hideResult()},_OnKeyPress:function(n){this.model.enableIncrementalSearch&&n.keyCode!=13&&this._OnTextEnter(t.browserInfo().name=="mozilla"?n.charCode:n.keyCode);n.keyCode==32&&this._preventDefaultAction(n)},_OnTextEnter:function(i){var l=this,s,h,r,v,e;this._incqueryString+=String.fromCharCode(i);this._incqueryString.length>0&&setTimeout(function(){l._incqueryString=""},this.model.incrementalSearchDelay);var u=this._getLi(),r,o=null,c=this.model.caseSensitiveSearch,f=this._incqueryString,a=this._incqueryString.length;if(c||(f=f.toLowerCase()),this._prevQueryString==f){for(r=0;r<this._matchedListItems.length;r++)if(n(this._matchedListItems[r]).hasClass("e-active")||this.model.showCheckbox&&n(this._matchedListItems[r]).hasClass("e-hover")){o=r==this._matchedListItems.length-1?this._matchedListItems[0]:this._matchedListItems[r+1];break}}else if(!t.isNullOrUndefined(u)){for(this._matchedListItems=[],s=this.selectedIndex(),r=0;r<u.length;r++)r!=s&&((r==u.length||r==-1)&&(r=-1),s==-1&&(s=u.length,r=0),h=r==-1?0:r,v=u[h],e=n(u[h]).text(),e=c?e:e.toLowerCase(),e.substr(0,a)==f&&this._matchedListItems.push(u[h]));this._prevQueryString=f;o=this._matchedListItems[0]}t.isNullOrUndefined(o)||(this._activeItem=n.inArray(o,u),this._isSingleSelect()&&!t.isNullOrUndefined(this.scrollerObj)?(this._enterTextBoxValue(),this.scrollerObj.setModel({scrollTop:this._calcScrollTop("active")})):this._isPopupShown()&&(this._removeListHover(),this._addListHover()))},_selectItem:function(n){this._isSingle||this._clearTextboxValue();this.activeItem&&this._setAttr(this.activeItem[0],{"aria-selected":!1});this._activeItem=n;this._addListHover();this.wrapper.focus();this._enterTextBoxValue()},_focusItem:function(n){this._removeListHover();this._activeItem=n;this._addListHover()},_selectFocusedItem:function(n){this._focusItem(n);this._enterTextBoxValue()},_selectShiftDown:function(t,i,r){r||this._clearTextboxValue();for(var u=t;u<=i;u++)n.inArray(u,this._disabledItems)<0&&n.inArray(u,this.model.selectedIndices)<0&&this._selectFocusedItem(u)},_selectShiftUp:function(t,i,r){r||this._clearTextboxValue();for(var u=i;u>=t;u--)n.inArray(u,this._disabledItems)<0&&n.inArray(u,this.model.selectedIndices)<0&&this._selectFocusedItem(u)},_selectShiftHome:function(t,i,r){if(r||this._clearTextboxValue(),t>=0&&t<=this._listSize-1){if(t==0)this._clearTextboxValue();else for(var u=t;u>=i;u--)n.inArray(u,this._disabledItems)<0&&n.inArray(u,this.model.selectedIndices)<0&&(this._activeItem=u,this._enterTextBoxValue());this._activeItem=t;t==0&&this._enterTextBoxValue();this.scrollerObj.setModel({scrollTop:0})}},_selectShiftEnd:function(t,i,r){if(r||this._clearTextboxValue(),t<=this._listSize-1){if(t==i)this._clearTextboxValue();else for(var u=t;u<=i;u++)n.inArray(u,this._disabledItems)<0&&n.inArray(u,this.model.selectedIndices)<0&&(this._activeItem=u,this._enterTextBoxValue());this._activeItem=t;t==i&&this._enterTextBoxValue();this.scrollerObj.setModel({scrollTop:this.ultag.outerHeight()})}},_getLastFocusedLi:function(){return this._selectedIndices&&this._selectedIndices.length>0?this._selectedIndices[this._selectedIndices.length-1]:null},_getLastShiftFocusedLi:function(t,i){var r=i?t-1:t+1;return n.inArray(r,this._selectedIndices)<0?t:this._getLastShiftFocusedLi(r,i)},_shiftUp:function(n,t,i){var u,r,f;if(n==null||n<0)this._checkDisableStep(0,t,!1,!1,!0);else if(n>0&&n<=this._listSize-1&&(u=this._disableItemSelectUp(n-t),u!=null))if(this._getLastFocusedLi()!=null)if(this._selectedIndices.length>1&&n-1==this._selectedIndices[this._selectedIndices.length-2])for(r=1;r<=t;r++)if(n-r==this._selectedIndices[this._selectedIndices.length-2])this._activeItem=n+1-r,this._removeTextBoxValue(),this._focusItem(n-r);else break;else f=this._getLastShiftFocusedLi(this._getLastFocusedLi(),!1),this._selectShiftUp(u,f,i);else this._moveUp(n,t,!1)},_shiftDown:function(n,t,i){var u,r,f;if(n==null||n<0)this._checkDisableStep(-1,t,!0,!1,!0);else if(n<this._listSize-1&&(u=this._disableItemSelectDown(n+t),u!=null))if(this._getLastFocusedLi()!=null)if(this._selectedIndices.length>1&&n+1==this._selectedIndices[this._selectedIndices.length-2])for(r=1;r<=t;r++)if(n+r==this._selectedIndices[this._selectedIndices.length-2])this._activeItem=n-1+r,this._removeTextBoxValue(),this._focusItem(n+r);else break;else f=this._getLastShiftFocusedLi(this._getLastFocusedLi(),!0),this._selectShiftDown(f,u,i);else this._moveDown(n,t,!1)},_moveUp:function(n,t,i){n==null||n<=0?this._checkDisableStep(0,t,!1,i):n>this._listSize-1?this._checkDisableStep(this._listSize-1,t,!1,i):n>0&&n<=this._listSize-1&&this._checkDisableStep(n,t,!1,i)},_moveDown:function(n,t,i){n==null||n<0?this._checkDisableStep(-1,t,!0,i):n==0?this._checkDisableStep(0,t,!0,i):n>=this._listSize-1?this._checkDisableStep(this._listSize-1,t,!0,i):n<this._listSize-1&&this._checkDisableStep(n,t,!0,i)},_checkDisableStep:function(n,t,i,r,u){var s=i?"_disableItemSelectDown":"_disableItemSelectUp",o=i?n+t:n-t,f=this[s](o),e;if(f==null)for(e=t;e>=0;e--)if(o=i?n+e:n-e,f=this[s](o),f!=null)break;f!=null&&(r?this._focusItem(f):this._selectItem(f),u&&r&&this._enterTextBoxValue())},_disableItemSelectDown:function(t){return(t==null||t<0)&&(t=0),t<this._listSize?n.inArray(t,this._disabledItems)<0?t:this._disableItemSelectDown(t+1):this._listSize-1},_disableItemSelectUp:function(t){if((t==null||t<0)&&(t=0),t<this._listSize){if(n.inArray(t,this._disabledItems)<0)return t;if(t>0)return this._disableItemSelectUp(t-1)}},_preventDefaultAction:function(n,t){n.preventDefault?n.preventDefault():n.returnValue=!1;t&&(n.stopPropagation?n.stopPropagation():n.cancelBubble=!0)},_OnKeyDown:function(n){var e,u,f,r,i;if(this._uiInteract=!0,this.model.enabled&&!t.isNullOrUndefined(this.popupListWrapper))if(this._itemId=null,e=this._getLi(),this._listSize=e.length,f=this.popupList.height(),u=this.ultag.children("li").outerHeight(),r=Math.round(f/u)!=0?Math.round(f/u):5,this._isSingle=this._isSingleSelect(),this._isSingle)switch(n.keyCode){case 38:if(n.altKey){this.ultag.find("li").length>0&&this._hideResult();break}case 33:i=n.keyCode==33?r:1;this._moveUp(this._activeItem,i);this._preventDefaultAction(n,!0);break;case 8:this._preventDefaultAction(n);break;case 40:if(n.altKey){this.ultag.find("li").length>0&&this._showResult();break}case 34:i=n.keyCode==34?r:1;this._moveDown(this._activeItem,i);this._preventDefaultAction(n,!0);break;case 37:this.model.enableRTL?this._moveDown(this._activeItem,1):this._moveUp(this._activeItem,1);this._preventDefaultAction(n);break;case 39:this.model.enableRTL?this._moveUp(this._activeItem,1):this._moveDown(this._activeItem,1);this._preventDefaultAction(n);break;case 9:case 27:this._isPopupShown()&&this._hideResult();break;case 35:this._moveDown(this._listSize-1,0);this._preventDefaultAction(n);break;case 36:i=this._activeItem!=null?this._activeItem:this._listSize-1;this._moveUp(i,i);this._preventDefaultAction(n)}else switch(n.keyCode){case 38:n.altKey?this.ultag.find("li").length>0&&this._hideResult():n.shiftKey?this._shiftUp(this._activeItem,1,n.ctrlKey):this._moveUp(this._activeItem,1,n.ctrlKey);this._preventDefaultAction(n);break;case 33:n.shiftKey?this._shiftUp(this._activeItem,r,n.ctrlKey):this._moveUp(this._activeItem,r,n.ctrlKey);this._preventDefaultAction(n);break;case 8:this._preventDefaultAction(n);break;case 40:n.altKey?this.ultag.find("li").length>0&&this._showResult():n.shiftKey?this._shiftDown(this._activeItem,1,n.ctrlKey):this._moveDown(this._activeItem,1,n.ctrlKey);this._preventDefaultAction(n);break;case 34:n.shiftKey?this._shiftDown(this._activeItem,r,n.ctrlKey):this._moveDown(this._activeItem,r,n.ctrlKey);this._preventDefaultAction(n);break;case 37:this.model.enableRTL?this._moveDown(this._activeItem,1,!1):this._moveUp(this._activeItem,1,!1);this._preventDefaultAction(n);break;case 39:this.model.enableRTL?this._moveUp(this._activeItem,1,!1):this._moveDown(this._activeItem,1,!1);this._preventDefaultAction(n);break;case 9:case 27:this._isPopupShown()&&this._hideResult();break;case 35:n.shiftKey?this._selectShiftEnd(this._activeItem,this._listSize-1,n.ctrlKey):this._moveDown(this._activeItem,this._listSize,n.ctrlKey);this._preventDefaultAction(n);break;case 36:i=this._activeItem!=null?this._activeItem:this._listSize-1;n.shiftKey?this._selectShiftHome(this._activeItem,0,n.ctrlKey):this._moveUp(this._activeItem,i,n.ctrlKey);this._preventDefaultAction(n)}this._uiInteract=!1},_OnKeyUp:function(i){var u,r;if(this.model.enabled&&!t.isNullOrUndefined(this.popupListWrapper)){if(this._preventDefaultAction(i),u=i.target,this._activeItem==null&&(this._activeItem=this._getLi().index(this.popupList.find("ol,ul").children("li.e-hover"))),this._trim(this._visibleInput.val())==""&&i.keyCode==38&&i.keyCode==40)return this._hideResult(),!1;switch(i.keyCode){case 13:!this._isSingle&&this._isPopupShown()&&(i.ctrlKey||i.shiftKey)&&this._activeItem>=0?this._selectAndUnselect():!this._isPopupShown()||i.ctrlKey||i.shiftKey?this._isPopupShown()&&this._hideResult():(t.isNullOrUndefined(this.inputSearch)||(this.getSelectedItem().length==0||!this.listitems[0].classList.contains("e-active")&&this._activeItem>=0?(this.selectItemByIndex(this._activeItem),n(this.listitems[this._activeItem]).removeClass("e-hover")):(r=this._getLastFocusedLi(),(this.model.multiSelectMode!="none"||this.model.showCheckbox)&&(this.unselectItemByIndex(r),n(this.listitems[r]).removeClass("e-hover")))),this._hideResult());this._preventDefaultAction(i);break;case 32:this._isPopupShown()&&this._isSingle&&this._hideResult();!this._isSingle&&this._isPopupShown()&&this._activeItem>=0&&this._selectAndUnselect();this._preventDefaultAction(i);break;case 8:this.model.multiSelectMode=="visualmode"&&this._deleteLastBox();this._preventDefaultAction(i);break;case 46:if(this.model.multiSelectMode=="visualmode"||this.model.showCheckbox){this._deleteBox(this._ulBox.children("li.e-active"));break}}}},_isSingleSelect:function(){return!this.model.showCheckbox&&this.model.multiSelectMode=="none"},_selectAndUnselect:function(){this.model.showCheckbox?this._isChecked(this._getActiveItem().find(".e-checkwrap")[0])?this._removeTextBoxValue():this._enterTextBoxValue():this.model.MultiSelectMode!="none"&&(this._getActiveItem().hasClass("e-active")?this._removeTextBoxValue():this._enterTextBoxValue())},_targetFocus:function(){this.model.enabled&&!this._isFocused&&(this._isWatermark||this._hiddenSpan.css("display","none"),this.wrapper.addClass("e-focus e-popactive"),this._isFocused=!0,this._trigger("focusIn"))},_targetBlur:function(){this.model.enabled&&(this._isFocused=!1,this.wrapper.removeClass("e-focus e-popactive"),this._setWatermark(),this._trigger("focusOut"))},_getLocalizedLabels:function(n){return this._localizedLabels[n]===i?t.DropDownList.Locale["en-US"][n]:this._localizedLabels[n]},_wireEvents:function(){this._on(this.wrapper,"focus",this._targetFocus);this._on(this.wrapper,"blur",this._targetBlur);this._on(this.wrapper,"keydown",this._OnKeyDown);t.isNullOrUndefined(this.popupList)||this._on(this.popupList,"keydown",this._OnKeyDown);t.isNullOrUndefined(this.popupList)||this._on(this.popupList,"keyup",this._OnKeyUp);this._on(this.wrapper,"keyup",this._OnKeyUp);t.isNullOrUndefined(this.popupList)||this._on(this.popupList,"keypress",this._OnKeyPress);this._on(this.wrapper,"keypress",this._OnKeyPress)},_unwireEvents:function(){this._off(this.wrapper,"focus",this._targetFocus);this._off(this.wrapper,"blur",this._targetBlur);this._off(this.wrapper,"keydown",this._OnKeyDown);t.isNullOrUndefined(this.popupList)||this._off(this.popupList,"keydown",this._OnKeyDown);t.isNullOrUndefined(this.popupList)||this._off(this.popupList,"keyup",this._OnKeyUp);this._off(this.wrapper,"keyup",this._OnKeyUp);t.isNullOrUndefined(this.popupList)||this._off(this.popupList,"keypress",this._OnKeyPress);this._off(this.wrapper,"keypress",this._OnKeyPress);n(window).off("resize",n.proxy(this._OnWindowResize,this))},_multiItemSelection:function(n,t){var i,r;for(this._ulBox||this.model.multiSelectMode!="visualmode"||this._renderBoxModel(),i=0;i<n.length;i++)r=t?this._rawList.length-(n.length-i):i,this._hasClass(n[i],"chkselect")&&(this._activeItem=r,this._enterTextBoxValue(),this._removeClass(n[i],"chkselect"));this._setWatermark()},_appendCheckbox:function(n,t){var i;for(this._ulBox||this.model.multiSelectMode!="visualmode"||this._renderBoxModel(),i=0;i<n.length;i++){var r=t?this._rawList.length-(n.length-i):i,f=document.createElement("input"),u=document.createElement("span");this._setAttr(f,{type:"checkbox",name:"list"+r,"data-role":"none",id:this._id+"_check"+r})._setClass(f,"e-check-input")._setAttr(u,{name:"list"+r+"_wrap","data-role":"none",id:this._id+"_check"+r+"wrap",unselectable:"on","aria-checked":!1})._setClass(u,"e-checkwrap e-icon ");u.appendChild(f);n[i].insertBefore(u,n[i].childNodes[0]);this._hasClass(n[i],"chkselect")&&(this._activeItem=r,this._enterTextBoxValue(),this._removeClass(n[i],"chkselect"))}this._setWatermark()},_onCheckChange:function(t){this.checkChange=!0;var i=t.target.nodeName==="INPUT"?t.target.parentElement:t.target;this._activeItem=n.inArray(n(i).parents("li")[0],this._getLi());this._hasClass(i,"e-check-act")?this._removeTextBoxValue():this._enterTextBoxValue(!0);this.checkChange=!1},_isChecked:function(t){return this._hasClass(t,"e-check-act")&&n(t).children(".e-check-input")[0].checked==!0},_removeCheck:function(){this._getLi().find(".e-checkwrap").remove()},_resetCheck:function(){var i=this._getLi(),t,n;for(i.find(".e-check-act").removeClass("e-check-act").attr("aria-checked",!1),t=i.find(".e-check-input:checked"),n=0;n<t.length;n++)t[n].checked=!1}});t.DropDownList.Locale=t.DropDownList.Locale||{};t.DropDownList.Locale["default"]=t.DropDownList.Locale["en-US"]={emptyResultText:"No suggestions",watermarkText:""};t.MultiSelectMode={None:"none",Delimiter:"delimiter",VisualMode:"visualmode"};t.VirtualScrollMode={Normal:"normal",Continuous:"continuous"}})(jQuery,Syncfusion)});
