/*!
*  filename: ej.widget.ko.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["knockout","./ej.core.min"],n):n()})(function(n){return function(n,t,i,r,u){"use strict";var f={binder:function(t,i,u,e,s,h,c){var d=n(t).data(c),p=!d,l=o(i,h["ob.ignore"],!p),w,b,v,k,y,a;if(!p&&JSON)for(y in l)v=l[y],v instanceof Array&&(k=d.model[y])instanceof Array&&v.length===k.length&&JSON.stringify(v)===JSON.stringify(k)&&delete l[y];p&&h.type==="editor"&&r.isObservable(l.value)&&(b=l.value,w=f.modelChange(b),l=n.extend({},l,{value:l.value()}));n(t)[c](l);a=n(t).data(c);"tmpl.$bindingContext"in a&&f.refreshTemplate(t,a["tmpl.$bindingContext"]);n(t).on(c+"refresh",function(){"tmpl.$bindingContext"in a&&f.refreshTemplate(t,a["tmpl.$bindingContext"])});w&&(n(t).data(c).kosubscribe=b.subscribe(f.valueChange(n(t)[c]("model._change",w).data(c))))},modelChange:function(n){return function(t){n(t.value)}},valueChange:function(n){return function(t){n.option("value",f.processData(t))}},processData:function(n){return n==="true"?!0:n==="false"?!1:+n+""===n?+n:n},bindKoHandler:function(t,i){i["ob.ignore"]=[];[].push.apply(i["ob.ignore"],i.observables||[]);r.bindingHandlers[t]={init:function(i){r.utils.domNodeDisposal.addDisposeCallback(i,function(){var o,e,s,r,u,f;n(i).off(t+"refresh");o=n(i).data(t);e=o["tmpl.$bindingContext"]||{};for(s in e)delete e[s];if(r=n(i).data(t).kosubscribe,r)if(typeof r.dispose=="function")r.dispose();else for(u in r)for(f in r[u])r[u].hasOwnProperty(f)&&(r[u][f].dispose(),delete r[u][f]);n(i)[t]("destroy")})},update:function(n,r,u,e,o){f.binder(n,r,u,e,o,i,t)}};r.bindingHandlers.ejTemplate={init:function(){return{controlsDescendantBindings:!0}}}},refreshTemplate:function(t,i){var o=n(t).find(".ej-knockout-template"),u,e,f;for(u in i){if(e=o.filter("."+i[u].key),!e.length){delete i[u];continue}for(f=0;f<e.length;f++)r.renderTemplate(u.replace("#",""),i[u].bindingContext.items[f],{},e[f])}}},o=function(n,i,u,f){var s=r.utils.unwrapObservable(n),h={},e;if(f=f||"",typeof s=="function"&&(s=s()),t.isPlainObject(s))for(e in s)if(i.indexOf(f+e)===-1)h[e]=r.utils.unwrapObservable(s[e]),(t.isPlainObject(h[e])||r.isObservable(h[e]))&&(h[e]=o(h[e],i,u,f+e+"."));else{if(u)continue;h[e]=s[e]}return h},e=i.registeredWidgets;for(var s in e)f.bindKoHandler(e[s].name,e[s].proto);t.widget.extensions={registerWidget:function(n){f.bindKoHandler(n,e[n].proto)}};t.extensions.ko={subscriptions:{},register:function(n,i,f){if(!r||!r.isObservable(n))return n;var e=n,o={};return n.ejComputed!==u&&(e=n.ejComputed,n=n.ejValue,f.isejObservableArray=!0),t.widget.observables.subscriptions[i]=e.subscribe(t.proxy(t.widget.observables.raise,{instance:f,field:i})),f.kosubscribe||(f.kosubscribe=[]),o[i]=t.widget.observables.subscriptions[i],f.kosubscribe.push(o),n},raise:function(i){var r=this.instance["ob.values"],u=this.field in r;u||(r[this.field]=this.instance.option(this.field));(r[this.field]!==i||n.isArray(this.instance.option(this.field)()))&&(this.instance.option(this.field,t.extensions.modelGUID,!0),r[this.field]=i)},getValue:function(n,t){return t?n:typeof n=="function"?n():n}};r.extenders.ejObservableArray=function(n,t){var i=r.computed({read:function(){return r.toJS(n)},write:function(t,i,r){var u,f,e;switch(r){case"insert":u=new n.ejObsFunc(t);n.splice(i,0,u);break;case"remove":n.splice(i,1);break;case"update":for(u=Object.keys(t),f=0;f<u.length;f++)e=u[f],n()[i][e](t[e])}}},this);return n.ejValue=r.observableArray(i)(),n.ejComputed=i,n.ejObsFunc=t,n};t.template["text/ko-template"]=function(n,i,r,u){var e,f=n["tmpl.$bindingContext"];return f&&f[i]||(f=f||{},f[i]={bindingContext:{},key:t.getGuid("tmpl")},e=f[i].bindingContext,n["tmpl.$bindingContext"]=f),e=f[i].bindingContext,t.isNullOrUndefined(u)?(u=1,e.items=[r]):(e.items||(e.items=[]),e.items[u]=r),"<div data-bind='ejTemplate:{}' ej-prop='"+u+"' class='"+f[i].key+" ej-knockout-template'><\/div>"};t.widget.observables=t.extensions.ko}(window.jQuery,window.Syncfusion,window.Syncfusion.widget,n||window.ko),n||window.ko});
