﻿<%@ Page Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Contacts.aspx.cs" Inherits="WebUI.Contacts" Culture="auto" meta:resourcekey="Page" UICulture="auto" %>

<asp:Content ID="mainHeadContent" ContentPlaceHolderID="mainHead" runat="server">
    <script src="LocalizedResources/ej.culture.en-US.min.js"></script>
    <script type="text/javascript">
        function Initialization() {
            SetLocalization();

            $('#filterTxtBox').keydown(function (e) {
                if (e.keyCode == 13) {
                    e.preventDefault();
                    javascript: __doPostBack('searchBtn', '');
                }
            });
        }
    </script>

    <script type="text/x-template" id="activeColumnTemplate">
        {{if Active}}
            <i class="fa fa-check"/>
        {{else}}
            
        {{/if}}
    </script>

    <script type="text/x-template" id="waitingColumnTemplate">
        {{if Waiting}}
            <i class="fa fa-check"/>
        {{else}}
            
        {{/if}}
    </script>
</asp:Content>
<asp:Content ID="mainBodyContent" ContentPlaceHolderID="mainBody" runat="server">
    <div class="row">
        <div class="col-xs-12">
            <a id="newContactBtn" href="Contact.aspx" runat="server" type="button" class="btn btn-primary btn-flat margin-bottom margin-r-5">
                <asp:Literal meta:resourcekey="newContactBtn" runat="server"></asp:Literal></a>
        </div>
    </div>
    <%--    <asp:UpdatePanel ID="contactsUpdatePanel" runat="server">
        <ContentTemplate>--%>
    <div class="row">
        <div class="col-xs-12 ">
            <div class="input-group input-group-sm margin-bottom pull-right" style="min-width: 200px; max-width: 250px;">
                <input type="text" runat="server" id="filterTxtBox" class="form-control" maxlength="30">
                <span class="input-group-btn">
                    <button type="button" id="clearSearchBtn" runat="server" class="btn btn-default btn-flat" style="border-radius: unset !important" onserverclick="clearSearchBtn_ServerClick">
                        <i class="fa fa-remove"></i>
                    </button>
                </span>
                <span class="input-group-btn">
                    <button type="button" id="searchBtn" runat="server" class="btn btn-info btn-flat" onserverclick="searchBtn_ServerClick">
                        <i class="fa fa-search  hidden-md hidden-lg"></i>
                        <asp:Label meta:resourcekey="searchBtn" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></button>
                </span>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <ej:Grid ID="contactsGrid" runat="server" AllowSelection="false" Locale="el-GR" IsResponsive="true" EnableResponsiveRow="true" MinWidth="900" OnServerRecordDoubleClick="contactsGrid_ServerRecordDoubleClick" OnServerCommandButtonClick="contactsGrid_ServerCommandButtonClick">
                <SortedColumns>
                    <ej:SortedColumn Field="LastName" Direction="Ascending" />
                    <ej:SortedColumn Field="FirstName" Direction="Ascending" />
                </SortedColumns>
                <Columns>
                    <ej:Column Field="FullName" HeaderText="Ονοματεπώνυμο" Width="200">
                    </ej:Column>
                    <ej:Column Field="LastName" HeaderText="Επώνυμο" Visible="false">
                    </ej:Column>
                    <ej:Column Field="FirstName" HeaderText="Όνομα" Visible="false">
                    </ej:Column>
                    <ej:Column Field="Active" HeaderText="Ενεργός" Width="80" TextAlign="Center" TemplateID="#activeColumnTemplate">
                    </ej:Column>
                    <ej:Column Field="Waiting" HeaderText="Σε Αναμονή" Width="80" TextAlign="Center" TemplateID="#waitingColumnTemplate">
                    </ej:Column>
                    <ej:Column Field="Email" HeaderText="Email" Width="150">
                    </ej:Column>
                    <ej:Column Field="Mobile1" HeaderText="Κινητό" Width="110">
                    </ej:Column>
                    <ej:Column Field="TherapistFullName" HeaderText="Θεραπευτής" Width="200">
                    </ej:Column>
                    <ej:Column Field="DiagnosticianFullName" HeaderText="Διαγνωστικά" Width="200">
                    </ej:Column>
                    <ej:Column Field="ClinicSupervisorFullName" HeaderText="Κλινική Εποπτεία" Width="200">
                    </ej:Column>
                    <ej:Column HeaderText=" " IsUnbound="True" Width="100" AllowResizing="False" AllowTextWrap="False">
                        <Command>
                            <ej:Commands Type="Edit">
                                <ButtonOptions Size="Small" Text="Edit" Type="Button" ContentType="ImageOnly" PrefixIcon="e-icon e-edit" Width="30"></ButtonOptions>
                            </ej:Commands>
                            <ej:Commands Type="Delete">
                                <ButtonOptions Size="Small" Text="Delete" Type="Button" ContentType="ImageOnly" PrefixIcon="e-icon e-delete" Width="30"></ButtonOptions>
                            </ej:Commands>
                        </Command>
                    </ej:Column>
                    <ej:Column Field="ContactId" HeaderText="A/A" IsIdentity="True" IsPrimaryKey="True" Visible="False" Width="0">
                    </ej:Column>
                </Columns>
            </ej:Grid>
            <ej:Pager ID="contactsPager" runat="server" OnChange="contactsPager_Change" IsResponsive="true" Locale="el-GR"></ej:Pager>
        </div>
    </div>
    <%-- </ContentTemplate>
    </asp:UpdatePanel>--%>
</asp:Content>
