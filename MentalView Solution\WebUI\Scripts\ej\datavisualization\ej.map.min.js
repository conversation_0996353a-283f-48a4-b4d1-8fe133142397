/*!
*  filename: ej.map.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./../common/ej.data.min","./../common/ej.globalize.min","./../web/ej.slider.min","./../common/ej.scroller.min"],n):n()})(function(){(function(n,t,i){t.widget({ejMap:"ej.datavisualization.Map"},{validTags:["div"],_initPrivateProperties:function(){this._prevEnterElement="";this._rootgroup=null;this._bubblegroup=null;this._scale=1;this._prevDistance=0;this._tileTranslatePoint={x:0,y:0};this._translatePoint={x:0,y:0};this._prevPoint=null;this._Tiles=[];this._prevScale=0;this._tileDiv=null;this._containerWidth=500;this._containerHeight=400;this._baseTranslatePoint={x:0,y:0};this._isDragging=!1;this._prevLevel=1;this._startPoint={x:0,y:0};this._stopPoint={x:0,y:0};this.mouseClickable=!1;this._browser=null;this._baseScale=0;this._mapBounds=null;this._svgns="http://www.w3.org/2000/svg";this._ispanning=!1;this._dragStartX=0;this._isNavigationPressed=!1;this._iskeyboardKeysPressed=!1;this._isPolygonSelected=!1;this._dragStartY=0;this._width=350;this._height=350;this._isMapCoordinates=!0;this._margintop=0;this._marginleft=0;this._legendDivWidth=0;this._legendDivHeight=0;this._svgDocument=null;this._tooltipElement=null;this._templateDiv=null;this._scrollLegendDiv=null;this._legendContainer=null;this._legendDiv=null;this._legendDivHeight=0;this._legendDivWidth=0;this._mapContainer=null;this._isSVG=!0;this._VMLPathFractionCount=0;this._sliderControl=null;this._isTileMap=!1;this._isRendered=!1;this._urlTemplate=null;this._polylineCount=0;this._pointscount=0;this._isPinching=!1;this._groupSize=null;this._groupBorder={x:0,y:0}},defaults:{locale:null,enableRTL:!1,enableGroupSeparator:!1,background:"transparent",zoomSettings:{minValue:1,maxValue:100,factor:1,level:1,enableZoomOnSelection:!1,enableMouseWheelZoom:!0,enableZoom:!0,animationDuration:600},centerPosition:null,enableResize:!0,isResponsive:!0,enableAnimation:!1,draggingOnSelection:!1,navigationControl:{enableNavigation:!1,content:null,orientation:"vertical",absolutePosition:{x:0,y:0},dockPosition:"centerleft"},enableLayerChangeAnimation:!1,enablePan:!0,zoomLevel:1,minZoom:1,maxZoom:100,zoomFactor:1,baseMapIndex:0,shapeSelected:"",markerSelected:"",markerEnter:"",markerLeave:"",legendItemRendering:"",displayTextRendering:"",legendItemClick:"",bubbleRendering:"",shapeRendering:"",zoomedIn:"",onRenderComplete:"",refreshed:"",panned:"",zoomedOut:"",mouseover:"",mouseleave:"",click:"",doubleClick:"",rightClick:"",onLoad:"",layers:[]},observables:["baseMapIndex","enablePan","enableResize","enableAnimation","zoomLevel","minZoom","maxZoom","zoomFactor",],_zoomLevel:t.util.valueFunction("zoomLevel"),_minZoom:t.util.valueFunction("minZoom"),_maxZoom:t.util.valueFunction("maxZoom"),_zoomFactor:t.util.valueFunction("zoomFactor"),baseMapIndex:t.util.valueFunction("baseMapIndex"),enablePan:t.util.valueFunction("enablePan"),enableResize:t.util.valueFunction("enableResize"),enableAnimation:t.util.valueFunction("enableAnimation"),_tags:[{tag:"layers",attr:["legendSettings.showLegend","legendSettings.toggleVisibility","legendSettings.positionX","legendSettings.positionY","legendSettings.type","legendSettings.labelOrientation","legendSettings.title","legendSettings.mode","legendSettings.position","legendSettings.dockOnMap","legendSettings.dockPosition","legendSettings.leftLabel","legendSettings.rightLabel","legendSettings.icon","legendSettings.iconHeight","legendSettings.iconWidth","legendSettings.height","legendSettings.width","legendSettings.showLabels","labelSettings.showLabels","labelSettings.labelPath","labelSettings.enableSmartLabel","labelSettings.labelLength","labelSettings.smartLabelSize","selectedMapShapes","selectionMode","bubbleSettings.showBubble","bubbleSettings.valuePath","bubbleSettings.minValue","bubbleSettings.bubbleOpacity","bubbleSettings.showTooltip","bubbleSettings.tooltipTemplate","bubbleSettings.colorValuePath","bubbleSettings.maxValue","bubbleSettings.color","enableAnimation","enableSelection","enableMouseHover","showTooltip","showMapItems","mapItemsTemplate","shapeData","dataSource","shapePropertyPath","shapeDataPath","layerType","bingMapType","urltemplate","shapeSettings.highlightColor","shapeSettings.highlightBorderWidth","shapeSettings.selectionColor","shapeSettings.fill","shapeSettings.strokeThickness","shapeSettings.selectionStrokeWidth","shapeSettings.stroke","shapeSettings.selectionStroke","shapeSettings.highlightStroke","shapeSettings.colorValuePath","shapeSettings.valuePath","shapeSettings.autoFill","shapeSettings.enableGradient","shapeSettings.colorPalette","shapeSettings.customPalette","key",[{tag:"markers",attr:["label","latitude","longitude"],singular:"marker"},{tag:"bubbleSettings.colorMappings.rangeColorMapping",attr:["from","to","color"],singular:"bubblerangeColorMap"},{tag:"bubbleSettings.colorMappings.equalColorMapping",attr:["value","color"],singular:"bubbleequalColorMap"},{tag:"shapeSettings.colorMappings.rangeColorMapping",attr:["from","to","color","gradientColors"],singular:"shaperangeColorMap"},{tag:"shapeSettings.colorMappings.equalColorMapping",attr:["value","color"],singular:"shapeequalColorMap"},{tag:"subLayers",attr:["legendSettings.showLegend","legendSettings.toggleVisibility","legendSettings.positionX","legendSettings.positionY","legendSettings.type","legendSettings.labelOrientation","legendSettings.title","legendSettings.mode","legendSettings.position","legendSettings.dockOnMap","legendSettings.dockPosition","legendSettings.leftLabel","legendSettings.rightLabel","legendSettings.icon","legendSettings.iconHeight","legendSettings.iconWidth","legendSettings.height","legendSettings.width","legendSettings.showLabels","labelSettings.showLabels","labelSettings.labelPath","labelSettings.enableSmartLabel","labelSettings.labelLength","labelSettings.smartLabelSize","selectedMapShapes","selectionMode","bubbleSettings.showBubble","bubbleSettings.valuePath","bubbleSettings.minValue","bubbleSettings.bubbleOpacity","bubbleSettings.showTooltip","bubbleSettings.tooltipTemplate","bubbleSettings.colorValuePath","bubbleSettings.maxValue","bubbleSettings.color","enableAnimation","enableSelection","enableMouseHover","showTooltip","showMapItems","mapItemsTemplate","shapeData","dataSource","shapePropertyPath","shapeDataPath","layerType","bingMapType","urltemplate","shapeSettings.highlightColor","shapeSettings.highlightBorderWidth","shapeSettings.selectionColor","shapeSettings.fill","shapeSettings.strokeThickness","shapeSettings.selectionStrokeWidth","shapeSettings.stroke","shapeSettings.selectionStroke","shapeSettings.highlightStroke","shapeSettings.colorValuePath","shapeSettings.valuePath","shapeSettings.autoFill","shapeSettings.enableGradient","shapeSettings.colorPalette","shapeSettings.customPalette","key",[{tag:"markers",attr:["label","latitude","longitude"],singular:"marker"},{tag:"bubbleSettings.colorMappings.rangeColorMapping",attr:["from","to","color"],singular:"bubblerangeColorMap"},{tag:"bubbleSettings.colorMappings.equalColorMapping",attr:["value","color"],singular:"bubbleequalColorMap"},{tag:"shapeSettings.colorMappings.rangeColorMapping",attr:["from","to","color","highlightcolor"],singular:"shaperangeColorMap"},{tag:"shapeSettings.colorMappings.equalColorMapping",attr:["value","color"],singular:"shapeequalColorMap"}],],singular:"subLayer"}]],singular:"layer"}],dataTypes:{layers:"array"},_destroy:function(){this._unWireEvents();this._isSVG?n(this.element).removeClass("e-datavisualization-map e-js").find("#svgDocument").remove():n(this.element).removeClass("e-datavisualization-map e-js").find("#rootGroup").remove();n(this.element).removeClass("e-map-navigation").find("#ejNavigation").remove();n(this.element).find(".e-TemplateDiv").remove()},_setModel:function(t){var i,r;for(i in t)switch(i){case"zoomSettings":this.model.zoomSettings.enableZoom&&(this._zoomLevel(this.model.zoomSettings.level),this._zoomFactor(this.model.zoomSettings.factor),this._maxZoom(this.model.zoomSettings.maxValue),this._minZoom(this.model.zoomSettings.minValue),this.zoom(this._zoomLevel()));break;case"zoomLevel":this.model.zoomSettings.level=this._zoomLevel();this.zoom(this._zoomLevel());break;case"zoomFactor":this.model.zoomSettings.factor=this._zoomFactor();this.zoom(this._zoomLevel());break;case"minZoom":this.model.zoomSettings.minValue=this._minZoom();this.zoom(this._zoomLevel());break;case"maxZoom":this.model.zoomSettings.maxValue=this._maxZoom();this.zoom(this._zoomLevel());break;case"baseMapIndex":this.baseMapIndex(t[i]);r=this;this._groupSize=null;this.enableAnimation()?(this.model.enableLayerChangeAnimation=!0,n(this._mapContainer).animate({opacity:0},500,function(){r.refresh(!0)})):this.refresh(!0);break;case"background":baseLayer.layerType=="geometry"&&n(this._svgDocument).css("background",this.model.background)}},_layers:function(n,t){t=="shapeSettings.fill"||t=="shapeSettings.strokeThickness"||t=="shapeSettings.selectionColor"||t=="shapeSettings.highlightColor"?this.clearShapeSelection():t=="showMapItems"||t=="dataSource"?this.refreshLayers():this.refresh(!0)},_layers_markers:function(){this.refreshMarkers()},_layers_subLayers:function(n,t){t=="showMapItems"||t=="dataSource"?this.refreshLayers():this.refresh(!0)},_init:function(){var t=this;this._navigationStyle=null;this.navigationControlData=null;this._initPrivateProperties();this._setZoomProperties();this._localizedLabels=this._getLocalizedLabels();n.each(this.model.layers,function(n){t._layerInitialize(n)});this._mapContainer=this.element;n(this._mapContainer).css({overflow:"hidden"});document.documentMode==8&&n(this._mapContainer).css({overflow:"hidden",position:"relative"});this._wireEvents();this.refresh();this._trigger("onRenderComplete");this._isRendered=!0;this._isSVG&&this.model.enablePan&&n(this._svgDocument).pinchZoom(this._rootgroup,this)},_setZoomProperties:function(){typeof this.model.zoomSettings.factor!="function"&&(this._zoomFactor(this.model.zoomSettings.factor),this._zoomLevel(this.model.zoomSettings.level),this._minZoom(this.model.zoomSettings.minValue),this._maxZoom(this.model.zoomSettings.maxValue))},_getLocalizedLabels:function(){return t.getLocalizedConstants(this.sfType,this.model.locale)},_layerInitialize:function(t){var i=this;this.model.layers[t]!=null?n.each(this.model.layers,function(t,u){u=i._checkArrayObject(u,t);var f=new r;n.extend(f,u);n.extend(u,f);n.each(u.subLayers,function(t,u){u=i._checkArrayObject(u,t);var f=new r;n.extend(f,u);n.extend(u,f)})}):this.layers[0]=new r},_checkArrayObject:function(t,i){var f=["background","zoomSettings","bubbleSettings","minValue","maxValue","factor","level","enableZoomOnSelection","enableZoom","centerPosition","enableResize","isResponsive","enableAnimation","draggingOnSelection","navigationControl","enableNavigation","orientation","absolutePosition","dockPosition","enablePan","baseMapIndex","enableSelection","selectionMode","enableMouseHover","shapeData","dataSource","showTooltip","legendSettings","showLegend","showLabels","position","height","width","type","mode","dockOnMap","dockPosition","labelSettings","labelPath","enableSmartLabel","smartLabelSize","layerType","showMapItems","shapeSettings","highlightColor","highlightBorderWidth","selectionColor","fill","strokeThickness","stroke","highlightStroke","selectionStroke","selectionStrokeWidth","colorValuePath","valuePath","enableGradient","autoFill",""],u=this;return n.each(t,function(n,t){if(f.indexOf(n)>-1)if(t instanceof Array)u._checkArrayObject(t,n);else if(t!=null&&typeof t=="object"){var e=new r;u._loadIndividualDefaultValues(t,e,typeof n=="number"?i:n)}}),t},_loadIndividualDefaultValues:function(t,i,r){var u=null,f=this;return n.each(i,function(n,t){if(r==n){u=t;return}}),u instanceof Array&&(u=u[0]),n.each(t,function(n,t){t instanceof Array?f._checkArrayObject(t,r):t!=null&&typeof t=="object"&&f._loadIndividualDefaultValues(t,t,typeof n=="number"?r:n)}),n.extend(u,t),n.extend(t,u),t},_refreshWithAnimation:function(){this.model.layers[this.baseMapIndex()]._setMapItemsPositionWithAnimation(this);for(var n=0;n<this.model.layers[this.baseMapIndex()].subLayers.length;n++)this.model.layers[this.baseMapIndex()].subLayers[n]._setMapItemsPositionWithAnimation(this)},_resizeShape:function(){this.model.layers[this.baseMapIndex()]._resizeShapes(this);for(var n=0;n<this.model.layers[this.baseMapIndex()].subLayers.length;n++)this.model.layers[this.baseMapIndex()].subLayers[n]._resizeShapes(this)},_refrshLayers:function(){var n=this.model.layers[this.baseMapIndex()],t,i;for((n._bubbles.length>0||n._mapItems.length>0||n._mapMarkers.length>0||n._labelCollection.length>0)&&n._setMapItemsPosition(this),i=0;i<n.subLayers.length;i++)t=n.subLayers[i],(t._bubbles.length>0||t._mapItems.length>0||t._mapMarkers.length>0||t._labelCollection.length>0)&&t._setMapItemsPosition(this)},_panTileMap:function(n,t,i){var u=Math.pow(2,this._zoomLevel())*256,r;this._tileTranslatePoint.x=n/2-u/2;this._tileTranslatePoint.y=t/2-u/2;r=this._convertTileLatLongtoPoint(i[0],i[1]);this._tileTranslatePoint.x-=r.x-n/2;this._tileTranslatePoint.y-=r.y-t/2},_generateTiles:function(i,r,f,e){var d=navigator.language||navigator.userLanguage,g,nt,c,l,a,v,s,k,y,h,p,w,b;this.Tiles=[];g=nt=Math.pow(2,i);var o=this.model.layers[this.baseMapIndex()],tt=Math.min(nt,(-this._tileTranslatePoint.y+this._height)/256+1),it=Math.min(g,(-this._tileTranslatePoint.x+this._width)/256+1),rt=-(this._tileTranslatePoint.x+256)/256,ut=-(this._tileTranslatePoint.y+256)/256;for(c=Math.round(rt);c<Math.round(it);c++)for(l=Math.round(ut);l<Math.round(tt);l++)a=256*c+this._tileTranslatePoint.x,v=256*l+this._tileTranslatePoint.y,a>-256&&a<=this._width&&v>-256&&v<this._height&&c>=0&&l>=0&&(s=new u(c,l),s.left=a,s.top=v,o.layerType==t.datavisualization.Map.LayerType.Bing?(r&&o.imageUrl!=r&&(o.imageUrl=r),f&&o.subDomains!=f&&(o.subDomains=f),e&&o.maxZoom!=e&&(o.maxZoom=e),s.src=this._getBingMap(s,o.key,o.bingMapType,d,o.imageUrl,o.subDomains,o.maxZoom)):s.src=this._urlTemplate.replace("level",this._zoomLevel()).replace("tileX",s.X).replace("tileY",s.Y),this.Tiles.push(s));for(k=n.extend(!0,[],this.Tiles),y=0;y<this.model.layers[this.baseMapIndex()].subLayers.length;y++)if(h=this.model.layers[this.baseMapIndex()].subLayers[y],h.layerType==t.datavisualization.Map.LayerType.OSM||h.layerType==t.datavisualization.Map.LayerType.Bing)for(p=0;p<k.length;p++)w=k[p],b=n.extend(!0,{},w),b.src=h.layerType==t.datavisualization.Map.LayerType.Bing?this._getBingMap(b,h.key,h.bingMapType,d):h.urlTemplate.replace("level",this._zoomLevel()).replace("tileX",w.X).replace("tileY",w.Y),this.Tiles.push(b);this._arrangeTiles()},_getBingMap:function(n,t,i,r,u,f,e){for(var s,c,o="",h=Math.min(this._zoomLevel(),e);h>0;h--)s=0,c=1<<h-1,(n.X&c)!=0&&s++,(n.Y&c)!=0&&(s+=2),o=o+""+s;return u=u.replace("{quadkey}",o).replace("{subdomain}",f[Math.min(o.substr(o.length-1,1),f.length)]),u+("&mkt="+r+"&ur=IN&Key="+t)},_arrangeTiles:function(){var t=n.templates("<div><div style='position:absolute;left:{{:left}}px;top:{{:top}}px;height:{{:height}}px;width:{{:width}}px;'><img src={{:src}}><\/img><\/div><\/div>"),i=t.render(this.Tiles),r=this._mapContainer[0].children[0];this._tileDiv.html(i)},refreshLayer:function(i,r){var f,e,l,o,a,s,v,h,u,c;for(s=t.util.isNullOrUndefined(r),f=s?this.model.layers[i]:this.model.layers[i].subLayers[r],e=s?"_layerIndex"+i:"_sublayerIndex"+r,l=document.getElementById("rootGroup").childNodes.length,o=n("#"+this._id).find("path[id*='"+e+"']"),o.remove(),a=this._calculatePath(i,r,this.model.layers[i]),h=jQuery(a),f.bubbleSettings.showBubble&&(v=n("#"+this._id).find("circle[id*='"+e+"']"),v.remove()),u=0;u<h.length;u++)c=document.createElementNS("http://www.w3.org/2000/svg","path"),this._cloneAttributes(c,h[u]),n("#rootGroup").append(c);this._renderLayers(f,l-o.length,this);this._refrshLayers()},_cloneAttributes:function(n,t){for(var i,r=Array.prototype.slice.call(t.attributes);i=r.pop();)n.setAttribute(i.nodeName,i.nodeValue)},_calculatePath:function(n,r,u){var f=t.util.isNullOrUndefined(r)?u.subLayers:u.subLayers[r],n=t.util.isNullOrUndefined(n)?this.baseMapIndex():n,o="",e;if(this._isSVG=window.SVGSVGElement?!0:!1,t.util.isNullOrUndefined(r)&&(u.layerType.toLowerCase()==t.datavisualization.Map.LayerType.Geometry?u!=i&&u.shapeData!=null&&(u._isBaseLayer=!0,this._isTileMap=!1,this._scale=this._zoomLevel(),o=this._readShapeData(u,n,null)):(this._isTileMap=!0,this._scale=Math.pow(2,this._zoomLevel()-this._zoomFactor()))),t.util.isNullOrUndefined(f.length))f.layerType==t.datavisualization.Map.LayerType.Geometry&&f.shapeData!=null&&(f._isBaseLayer=!1,o+=this._readShapeData(f,n,r));else for(e=0;e<f.length;e++)f[e].layerType==t.datavisualization.Map.LayerType.Geometry&&f[e].shapeData!=null&&(f[e]._isBaseLayer=!1,o+=this._readShapeData(f[e],n,e));return o},_generatePath:function(i,r){var f=t.util.isNullOrUndefined(i)?this.model.layers[this.baseMapIndex()]:this.model.layers[i],e=this._calculatePath(i,r,f),o,s,u;this._isSVG?(o='<div style="position:relative;" id="tileDiv"/><svg xmlns="http://www.w3.org/2000/svg" style= "overflow:hidden;z-index:0;float:left;left:0,top:0"id="svgDocument"> <g id="rootGroup">'+e+"<\/g><\/svg>",this._mapContainer.html(o),this._svgDocument=this.element.find("#svgDocument")[0],this._tileDiv=this.element.find("#tileDiv")):(document.createStyleSheet().addRule(".vml","behavior:url(#default#VML);display:inline-block"),document.namespaces.add("v","urn:schemas-microsoft-com:vml","#default#VML"),s='<div id="tileDiv"/><v:group   id = "rootGroup" style="position:absolute; width:'+this._width+"px;height:"+this._height+'px;" coordorigin = "0 0" coordsize="'+this._width+" "+this._height+'">'+e+"<\/v:group>",this._mapContainer.html(s),this._tileDiv=this.element.find("#tileDiv"),this._bubblegroup=this._createGroup(!1,"bubbleGroup"),this._bubblegroup.style.position="absolute",this.element.append(this._bubblegroup));f.layerType==t.datavisualization.Map.LayerType.OSM?(this._urlTemplate=f.urlTemplate,this.model.centerPosition!=null?this._panTileMap(this._width,this._height,this.model.centerPosition):this._panTileMap(this._width,this._height,[0,0]),this._generateTiles(this._zoomLevel()),this._isSVG&&n(this._svgDocument).css("position","relative")):f.layerType==t.datavisualization.Map.LayerType.Bing&&(u=this,n.ajax({url:"http://dev.virtualearth.net/REST/V1/Imagery/Metadata/"+f.bingMapType+"?output=json&include=ImageryProviders&key="+f.key,success:function(t){if(t&&t.statusCode==200){var i=t.resourceSets[0].resources[0],r=i.imageUrl,e=i.imageUrlSubdomains,o=i.zoomMax;u._urlTemplate=f.urlTemplate;u.model.centerPosition!=null?u._panTileMap(u._width,u._height,u.model.centerPosition):u._panTileMap(u._width,u._height,[0,0]);u._generateTiles(u._zoomLevel(),r,e,o);u._isSVG&&n(u._svgDocument).css("position","relative")}}}),u.model.centerPosition!=null?u._panTileMap(u._width,u._height,u.model.centerPosition):u._panTileMap(u._width,u._height,[0,0]));this._isSVG&&n(this._mapContainer).css("position","relative");this._rootgroup=this.element.find("#rootGroup")[0];this._on(n(this.element),t.eventType.mouseDown,{map:this},this.dragDown);this._on(n(this.element),t.eventType.mouseMove,{map:this},this.dragMove);this._on(n(this.element),t.eventType.mouseUp,{map:this},this.dragUp)},isMultipolygon:function(n){return n.length>0&&n[0].length>0&&n[0][0].length>0&&n[0][0][0].length>0},calculateMultiPolygonBBox:function(n,t,i){for(var r=0;r<n.length;r++)t=this.calculatePolygonBBox(n[r],t,i||r>0);return!0},calculatePolygonBBox:function(n,t,i){for(var r,u,e=n.length,f=0;f<e;f++)if(r="",e>1?(r=n[f][0],r.length<=2&&(r=n[f])):r=n[f],r.length>2)for(u=0;u<r.length;u++)r[u][1]!=null&&r[u][0]!=null&&(i?(t.minLatitude=Math.min(t.minLatitude,r[u][1]),t.maxLatitude=Math.max(t.maxLatitude,r[u][1]),t.minLongitude=Math.min(t.minLongitude,r[u][0]),t.maxLongitude=Math.max(t.maxLongitude,r[u][0])):(t.minLatitude=t.maxLatitude=r[u][1],t.minLongitude=t.maxLongitude=r[u][0],i=!0));else r[u][1]!=null&&r[u][0]!=null&&(i?(t.minLatitude=Math.min(t.minLatitude,r[1]),t.maxLatitude=Math.max(t.maxLatitude,r[1]),t.minLongitude=Math.min(t.minLongitude,r[0]),t.maxLongitude=Math.max(t.maxLongitude,r[0])):(t.minLatitude=t.maxLatitude=r[1],t.minLongitude=t.maxLongitude=r[0],i=!0));return t},_calculateBBox:function(n,t,i){var u,f,e,o,v,l={minLatitude:0,maxLatitude:0,minLongitude:0,maxLongitude:0},y=!1,a,c,r,h,s;if(u=f=e=o=0,n!=null&&n.length>0)for(r=0;r<n.length;r++)a=n[r],v=a.geometry!=null?a.geometry.coordinates:a.coordinates,this.isMultipolygon(v)?this.calculateMultiPolygonBBox(v,l,r>0):this.calculatePolygonBBox(v,l,r>0);if(u=l.minLatitude,f=l.maxLatitude,e=l.minLongitude,o=l.maxLongitude,i!=null&&i.length>0)for(r=0;r<i.length;r++)a=i[r],c=a.geometry.coordinates,y?(u=Math.min(u,c[1]),f=Math.max(f,c[1]),e=Math.min(e,c[0]),o=Math.max(o,c[0])):(u=f=c[1],e=o=c[0],y=!0);if(t!=null&&t.length>0)for(r=0;r<t.length;r++)for(h=t[r].geometry==null?t[r].coordinates:t[r].geometry.coordinates,s=0;s<h.length;s++)y?(u=Math.min(u,h[s][1]),f=Math.max(f,h[s][1]),e=Math.min(e,h[s][0]),o=Math.max(o,h[s][0])):(u=f=h[s][1],e=o=h[s][0],y=!0);return this._groupSize!=null||this._isMapCoordinates||(this._groupSize={minX:e,maxX:o,minY:u,maxY:f}),[[e,u],[o,f]]},_readShapeData:function(n,i,r){var e=this,u,f;if(n.shapeData!=null)if(typeof n.shapeData=="string")u=new t.DataManager(n.shapeData),f=t.Query().from(n),u.executeQuery(f).done(function(t){if(t.result!=null)return e._getPathCollection(t.result,n,i,r)});else return this._getPathCollection(n.shapeData,n,i,r)},_getFactor:function(){var n,t,i,r;if(this._mapBounds!=null){var u=this._convertTileLatLongtoPointForShapes(this._mapBounds[0][1],this._mapBounds[0][0],this._mapBounds),f=this._convertTileLatLongtoPointForShapes(this._mapBounds[1][1],this._mapBounds[1][0],this._mapBounds);r=i=1;n=f.y-u.y;t=f.x-u.x}else n=t=500;return i=n<this._height?parseFloat(Math.abs(this._height/n+"e+1").toString().split(".")[0]/10):this._height/n,r=t<this._width?parseFloat(Math.abs(this._width/t+"e+1").toString().split(".")[0]/10):this._width/t,Math.min(r,i)},_getPathCollection:function(i,r,u,f){var dt="",st="",ht="",nt,tt,pt,v,y,p,w,et,ni,bt,kt,lt,k,ot,h,i,e,ct,a,b,s,c,o,vt,yt,l,at,g,d,ii;if(i.features!=null?(ht=new t.DataManager(i.features),st="geometry.type",r._isBaseLayer&&(this._mapBounds=i.bbox)):i.geometries!=null&&(ht=new t.DataManager(i.geometries),st="type",r._isBaseLayer&&i.bbox!=null&&(this._mapBounds=[[i.bbox[0],i.bbox[1]],[i.bbox[2],i.bbox[3]]])),i.geometries!=null||i.features!=null){var ri=t.Query().from(r).where(st,t.FilterOperators.equal,"LineString"),ui=t.Query().from(r).where(st,t.FilterOperators.equal,"Polygon"),fi=t.Query().from(r).where(st,t.FilterOperators.equal,"MultiPolygon"),it=ht.executeLocal(ri),ut=ht.executeLocal(ui),ei=t.Query().from(r).where(st,t.FilterOperators.equal,"Point"),ft=ht.executeLocal(ei),oi=ht.executeLocal(fi);if(this._polylineCount=this._polylineCount+it.length,this._pointscount=this._pointscount+ft.length,n.merge(ut,oi),r._isBaseLayer&&r.geometryType==t.datavisualization.Map.GeometryType.Normal&&(this._isMapCoordinates=!1),this._mapBounds==null&&r._isBaseLayer?this._mapBounds=this._calculateBBox(ut,it,ft):this._mapBounds==null&&(this._mapBounds=[[-180,-85],[180,85]]),at=this._getFactor(),r._newBounds=[],pt="",this._groupSize!=null&&(v=this._groupSize.minX,y=this._groupSize.maxX,p=this._groupSize.minY,w=this._groupSize.maxY),et=!0,ft.length>0)for(o=0;o<ft.length;o++){var wt=ft[o],h=wt.geometry.coordinates,i=[],e=[];i.push({lat:parseFloat(h[1]),lng:parseFloat(h[0])});ct=0;a=i[0];nt=a.lat;tt=a.lng;s=this._convertTileLatLongtoPointForShapes(nt,tt,this._mapBounds,at);b=this._mapBounds;tt>=b[0][0]&&tt<=b[1][0]&&nt>=b[0][1]&&nt<=b[1][1]?(et&&this._groupSize==null?(v=y=s.x,p=w=s.y,et=!1):(v=Math.min(v,s.x),y=Math.max(y,s.x),p=Math.min(p,s.y),w=Math.max(w,s.y)),this._isSVG?e.push({x:s.x,y:s.y,lat:a.lat,lng:a.lng}):e.push({x:s.x-5/this._scale,y:s.y-5/this._scale,lat:a.lat,lng:a.lng}),d=this._id+"_layerIndex"+u+(t.util.isNullOrUndefined(f)?"":"_sublayerIndex"+f)+"_shapeIndex"+o,this._isSVG?pt+='<circle cx="'+e[0].x+'" cy="'+e[0].y+'" r="'+r.shapeSettings.radius/this._scale+'" stroke="'+r.shapeSettings.stroke+'" stroke-width="'+r.shapeSettings.strokeThickness+'" id="'+d+'" fill="'+r.shapeSettings.fill+'" />':(ni='<v:oval display="block" fill="'+r.shapeSettings.fill+'" style="position:absolute;top: '+e[0].y+"px;left:"+e[0].x+"px;width:"+10/this._scale+"px;height:"+10/this._scale+"px;stroke-width:"+r.shapeSettings.strokeThickness+'px;"" id="'+d+"><\/v:oval>",pt+=ni)):(ft.splice(o,1),o--)}if(ut.length>0)for(o=0;o<ut.length;o++){var wt=ut[o],si=o,ti=0,gt,rt;if(rt=wt.geometry!=null?wt.geometry.coordinates:wt.coordinates,gt=rt[0][0].length,bt=rt.length,bt>1)for(kt=rt,lt=0;lt<kt.length;lt++)gt<kt[lt][0].length&&(gt=kt[lt][0].length,ti=lt);for(k="",ot=0;ot<bt;ot++){if(h="",bt>1?(h=rt[ot][0],h.length<=2&&(h=rt[ot])):h=rt[ot],i=[],e=[],h.length>2)for(l=0;l<h.length;l++)i.push({lat:parseFloat(h[l][1]),lng:parseFloat(h[l][0])});else if(this.isMultipolygon(rt)){for(c=0;c<h.length;c++)if(h[c].length>2)for(l=0;l<h[c].length;l++)i.push({lat:parseFloat(h[c][l][1]),lng:parseFloat(h[c][l][0])})}else i.push({lat:parseFloat(h[1]),lng:parseFloat(h[0])});for(ct=0;ct<i.length-1||i.length==1&&ct==0;ct++)a=i[ct],nt=a.lat,tt=a.lng,b=this._mapBounds,(this._isTileMap||tt>=b[0][0]&&tt<=b[1][0]&&nt>=b[0][1]&&nt<=b[1][1])&&(s=this._convertTileLatLongtoPointForShapes(nt,tt,b,at),et&&this._groupSize==null?(v=y=s.x,p=w=s.y,et=!1):(v=Math.min(v,s.x),y=Math.max(y,s.x),p=Math.min(p,s.y),w=Math.max(w,s.y)),this._isSVG?e.push({x:s.x,y:s.y,lat:a.lat,lng:a.lng}):e.push({x:s.x,y:s.y,lat:a.lat,lng:a.lng}));if(ot==ti&&r._newBounds.push(this._findMidPointofPoylgon(e)),e.length>0)if(this._isSVG){for(k+="M"+e[0].x+","+e[0].y,c=1;c<e.length;c++)k+=","+e[c].x+","+e[c].y;k+="Z"}else{for(k+="m"+parseInt(e[0].x)+","+parseInt(e[0].y),k+=" l"+parseInt(e[0].x)+","+parseInt(e[0].y),c=1;c<e.length;c++)k+=","+parseInt(e[c].x)+","+parseInt(e[c].y);k+=" e"}}k!=""?(d=this._id+"_layerIndex"+u+(t.util.isNullOrUndefined(f)?"":"_sublayerIndex"+f)+"_shapeIndex"+o,dt+=this._isSVG?'<path class="e-mapShape" stroke='+r.shapeSettings.stroke+" stroke-width="+r.shapeSettings.strokeThickness/this._scale+" fill= "+r.shapeSettings.fill+' d="'+k+'"  stroke-linejoin= round stroke-linecap= square id="'+d+'"><\/path>':'<v:shape  style="width:'+this._width+"px;height:"+this._height+'px;"  coordsize="'+this._width+" "+this._height+'" coordorigin="0 0" strokecolor='+r.shapeSettings.stroke+" stroke-width="+r.shapeSettings.strokeThickness/this._scale+'px" fillcolor= '+r.shapeSettings.fill+" id="+d+' path="'+k+'"><\/v:shape>'):(ut.splice(o,1),o--)}if(it.length>0)for(o=0;o<it.length;o++){for(vt=it[o].geometry==null?it[o].coordinates:it[o].geometry.coordinates,yt="",l=0;l<vt.length;l++)at=this._getFactor(),g=this._convertTileLatLongtoPointForShapes(vt[l][1],vt[l][0],this._mapBounds,at),et&&this._groupSize==null?(v=y=g.x,p=w=g.y,et=!1):(v=Math.min(v,g.x),y=Math.max(y,g.x),p=Math.min(p,g.y),w=Math.max(w,g.y)),yt+=g.x+","+g.y,l!=vt.length-1&&(yt+=",");d=this._id+"_layerIndex"+u+(t.util.isNullOrUndefined(f)?"":"_sublayerIndex"+f)+"_shapeIndex"+o;dt+=this._isSVG?"<polyline stroke="+r.shapeSettings.stroke+" id="+d+" stroke-width="+r.shapeSettings.strokeThickness/this._scale+' fill="transparent" stroke-width="1" points="'+yt+'" ><\/polyline>':'<v:polyline coordsize="'+this._width+", id="+d+this._height+'" coordorigin="0 0" strokecolor='+r.shapeSettings.stroke+" strokeweight="+r.shapeSettings.strokeThickness/this._scale+'px" fillcolor='+r.shapeSettings.fill+'  points="'+yt+'"/>'}return this._isMapCoordinates&&(this._groupSize={minX:v,maxX:y,minY:p,maxY:w}),ii=[].concat(ut,it),r._polygonData=n.merge(ii,ft),dt+pt}return r._polygonData=[],null},_createGroup:function(n,t){var i;return i=document.createElement("<v:group class="+t+">"),i.style.width=this._width+"px",i.style.height=this._height+"px",i.coordorigin="0 0",i.coordsize=this._width+" "+this._height,n&&(this._rootgroup=i,i.style.left="20px",i.style.top="20px"),i},_generatePaletteColorsForShape:function(n,t,i,r){r!=null&&(t.shapeSettings.highlightColor=r.highlightColor,t.shapeSettings.highlightStroke=r.highlightStroke,t.shapeSettings.selectionColor=r.SelectionColor,t.shapeSettings.selectionStroke=r.SelectionStroke);this._isSVG?n.setAttribute("fill",i[t._prevPaletteIndex]):n.fillcolor=i[t._prevPaletteIndex];t._prevPaletteIndex=t._prevPaletteIndex+1;t._prevPaletteIndex>i.length-1&&(t._prevPaletteIndex=0)},_renderLayers:function(r,u,f){var nt,d,ot,dt,tt,v,s,w,h,yt,gt,pt,g,ni,wt,c,b,ct,lt,ti,ii,l,bt,ri,it,at,rt,kt,a,k,ut,y;r._prevPaletteIndex=0;r._initializeLocalValues();r.selectedItems==null&&(r.selectedItems=[]);var fi=this._rootgroup,ui=this._svgDocument,o=this,e=r,ft=0,et=0;if(e.dataSource!=null&&e.bubbleSettings!=null)for(e.bubbleSettings.colorValuePath==null&&(e.bubbleSettings.colorValuePath=e.bubbleSettings.valuePath),nt=0;nt<e.dataSource.length;nt++)d=parseFloat(this._reflection(e.dataSource[nt],e.bubbleSettings.valuePath)),nt!=0?d>et?et=d:d<ft&&(ft=d):ft=et=d;if(r._renderedShapes=e.dataSource,e.showTooltip&&this._generateTooltipForLayers(e),e.shapeData!=null){if(ot=[],e.dataSource!=null)for(b=0;b<e.dataSource.length;b++)dt=e.dataSource[b],tt=o._reflection(dt,e.shapeDataPath),tt!=null&&(tt=tt.toLowerCase()),ot.push(tt);for(v=0;v<e._polygonData.length;v++){var st=e._polygonData[v].properties,ht=o._reflection(st,e.shapePropertyPath),vt,p=e.bubbleSettings.colorPath;if(ht!=null&&typeof ht=="string"&&(vt=ht.toLowerCase()),s=this._rootgroup.childNodes[v+u],f._isSVG?s.setAttribute("fill",e.shapeSettings.fill):(s.fillcolor=e.shapeSettings.fill,s.strokecolor=e.shapeSettings.stroke),c={},w=e._newBounds[v],e.shapeSettings.autoFill)switch(e.shapeSettings.colorPalette){case t.datavisualization.Map.ColorPalette.Palette1:this._generatePaletteColorsForShape(s,e,e.colorPalettes.Palette1,e._colorPaletteSettings.Palette1);break;case t.datavisualization.Map.ColorPalette.Palette2:this._generatePaletteColorsForShape(s,e,e.colorPalettes.Palette2,e._colorPaletteSettings.Palette2);break;case t.datavisualization.Map.ColorPalette.Palette3:this._generatePaletteColorsForShape(s,e,e.colorPalettes.Palette3,e._colorPaletteSettings.Palette3);break;case t.datavisualization.Map.ColorPalette.CustomPalette:this._generatePaletteColorsForShape(s,e,e.shapeSettings.customPalette,null)}if(ot.indexOf(vt)!=-1&&(h=e.dataSource[ot.indexOf(vt)],yt=ht,h.shape=s,h.shapeIndex=v,h._showBubble=!0,h!=null)){if(s!=null&&(pt=yt),r._bounds.push(w),e.showMapItems){if(g=n("<div class='e-mapItems' style='display:block;position:absolute;pointer-events: none;'><\/div>"),g[0].className=yt,o._templateDiv.append(g),n(g).css({left:w.x,top:w.y}),wt=this.model.locale,c=n.extend({},h),wt&&this.model.enableGroupSeparator)for(b in c)c[b]=isNaN(parseFloat(c[b]))?c[b]:parseFloat(c[b]).toLocaleString(wt);ni=e.mapItemsTemplate==null?n(" <div><label>{{:"+e.shapeSettings.valuePath+"}}<\/label><\/div>").render(c):n("#"+e.mapItemsTemplate).render(c);n(g).html(ni);r._mapItems.push(g)}e.shapeSettings.colorMappings!=null||e.shapeSettings.colorPath?(e.shapeSettings.colorValuePath==null&&(e.shapeSettings.colorValuePath=e.shapeSettings.valuePath),ct=o._reflection(h,e.shapeSettings.colorValuePath),ct==null||e.shapeSettings.autoFill||((e.legendSettings!=null&&e.legendSettings.mode==t.datavisualization.Map.LegendMode.Interactive||e.shapeSettings.enableGradient)&&e.shapeSettings.colorMappings.rangeColorMapping!=null?(c.legendrect=e._updateLegendRange(ct,e,s),lt=e.shapeSettings.fill,c.legendrect!=i&&(lt=c.legendrect.color),f._isSVG?s.setAttribute("fill",lt):s.fillcolor=lt):e._fillColors(ct,e.shapeSettings.colorMappings,s,f,h,st))):e.shapeSettings.autoFill||(f._isSVG?s.setAttribute("fill",e.shapeSettings.fill):(s.fillcolor=e.shapeSettings.fill,s.strokecolor=e.shapeSettings.stroke));e.bubbleSettings.showBubble&&e.bubbleSettings!=null&&e.bubbleSettings.valuePath!=null&&(f._isSVG?l=document.createElementNS(o._svgns,"circle"):(ti=this._id+"bubble_"+pt,ii='<v:oval class="e-mapBubble" id='+ti+' display="block" style="position:absolute;top: 0px;left:0px;width:100px;height:100px;stroke-width:0px;"><v:fill opacity="0.9"/><\/v:oval>',this._bubblegroup.innerHTML=this._bubblegroup.innerHTML+ii,l=document.getElementById("bubble_"+pt)),bt=o._reflection(h,e.bubbleSettings.valuePath),ri=o._reflection(h,e.bubbleSettings.colorValuePath),gt=e.bubbleSettings.color,e.bubbleSettings.colorMappings!=null&&(e.bubbleSettings.colorMappings.rangeColorMapping||e.bubbleSettings.colorMappings.equalColorMapping)?(e._fillColors(ri,e.bubbleSettings.colorMappings,l,f,h,this,!0),y=l.getAttribute("fill"),y==null&&(y=gt,l.setAttribute("fill",y))):(it={fill:p?h[p]:e.bubbleSettings.color,bubbleOpacity:e.bubbleSettings.bubbleOpacity,shapeData:h,shapePropertyData:st},this._trigger("bubbleRendering",{model:this.model,data:it}),it.fill!=h[p]&&(h._bubblecolor=it[p]),f._isSVG?(l.setAttribute("fill",h._bubblecolor?h._bubblecolor:p?h[p]:e.bubbleSettings.color),l.setAttribute("opacity",it.bubbleOpacity)):(l.strokecolor=h._color?h._color:p?h[p]:e.bubbleSettings.color,l.fillcolor=h._color?h._color:p?h[p]:e.bubbleSettings.color)),at=o._getRatioOfBubble(e.bubbleSettings.minValue,e.bubbleSettings.maxValue,bt,ft,et),f._isSVG?(n(l).attr({cx:w.x,cy:w.y,id:s.id+"_bubble_"+v,"fill-opacity":e.bubbleSettings.bubbleOpacity,r:at,"class":"e-mapBubble"}),o.enableAnimation()&&!this._isTileMap&&n(l).css("display","none")):n(l).css({height:2*at+"px",width:2*at+"px"}),e.bubbleSettings.showTooltip==!0?(rt=n("<div class='e-bubbleToolTip'  style='position:absolute;display:none;z-index:9999'/>"),kt=null,e.bubbleSettings.tooltipTemplate!=null?kt=e.bubbleSettings.tooltipTemplate:n(rt).append('<div class="e-bubbleToolTip"  style="margin-left:10px;margin-top:-25px;"><div class="e-defaultToolTip"><p style="margin-top:-4px"><label  style="color:rgb(27, 20, 20);font-size:14px;font-weight:normal;font-family:Segoe UI">'+bt+"<\/label><\/p><\/div><\/div>"),n(l).mouseenter({htmlobj:rt,templateID:kt,itemsrc:h},o._bubbleEnterFunction),n(l).mousemove({htmlobj:rt},o._bubbleOverFunction),n(l).mouseleave({htmlobj:rt},o._bubbleleaveFunction)):n(l).css("pointer-events","none"),f._isSVG?(ui.appendChild(l),r._bubbles.push(l)):r._bubbles.push(l));c.data=h}r.labelSettings!=null&&r.labelSettings.showLabels&&r.labelSettings.labelPath!=null&&e.geometryType!=t.datavisualization.Map.GeometryType.Normal&&(k=o._reflection(st,r.labelSettings.labelPath),k!=null&&(w==null,a=n("<div class='e-labelStyle' id='"+this._id+"labelStyle_"+v+"'><\/div>"),n(a).css({"pointer-events":"none",position:"absolute"}),a[0].innerHTML=k,ut={data:{text:k,labelSettings:r.labelSettings,id:this._id+"labelStyle_"+v,index:v},model:this.model},this._trigger("displayTextRendering",ut),k==ut.data.text||ut.cancel||(k=ut.data.text),this._templateDiv.append(a),r.legendSettings.showLegend&&r.legendSettings.dockOnMap&&r.legendSettings.dockPosition=="top"?n(this._templateDiv).css({"margin-top":r.legendSettings.height}):r.legendSettings.showLegend&&r.legendSettings.dockOnMap&&r.legendSettings.dockPosition=="left"&&n(this._templateDiv).css({"margin-left":r.legendSettings.width}),n(a).css({left:w.x,top:w.y}),o._off(n(a),t.eventType.mouseDown,o._polyMouseDown),o._on(n(a),t.eventType.mouseDown,{Param1:null,Param2:r,Param3:s},o._polyMouseDown),o._off(n(a),t.eventType.mouseUp,o._polyClickFunction),o._on(n(a),t.eventType.mouseUp,{Param1:null,Param2:r,Param3:s},o._polyClickFunction),o._off(n(a),t.eventType.mouseLeave,o._polyLeaveFunction),o._on(n(a),t.eventType.mouseLeave,{Param1:r,Param2:c,map:this},o._polyLeaveFunction),a[0].setAttribute("data-nodeValue",s.getAttribute("fill")),r._labelCollection.push(a),r._labelBounds.push(w),r._labelData.push(k)));c.shapeIndex=v;c.shape=s;c.shapeData=r._polygonData[v];o._off(n(a),t.eventType.mouseMove,o._polyMoveFunction);o._on(n(a),t.eventType.mouseMove,{Param1:e,Param2:c},o._polyMoveFunction);s!=null&&(y=null,f._isSVG?(y=s.getAttribute("fill"),y=="undefined"&&(y=r.shapeSettings.fill),s.setAttribute("data-nodeValue",y)):(y=s.fillcolor.value,y=="undefined"&&(y=r.shapeSettings.fill),s.style.behavior=y));o.model.zoomSettings.enableZoomOnSelection&&n(s).hover(function(){n(this).css("cursor","pointer")},function(){n(this).css("cursor","auto")});r._mapShapes.push(c);v==0&&(this._legendDiv==null&&(this._legendDiv=n("<div class='e-LegendDiv'/>")),e.legendSettings!=null&&(e.shapeSettings.colorMappings!=null||e.shapeSettings.colorPath!=null)&&(e.legendSettings.type==i||e.legendSettings.type.toLowerCase()=="layers")&&e._generateLegends(this),this.model.enableRTL&&e.shapeSettings.colorMappings.rangeColorMapping.reverse(),e.legendSettings!=null&&(e.bubbleSettings.colorMappings!=null||e.bubbleSettings.colorPath!=null)&&e.legendSettings.type.toLowerCase()=="bubbles"&&e._generateBubbleLegends(this),e.shapeSettings.colorMappings!=null&&e.shapeSettings.colorMappings.rangeColorMapping!=null&&e.shapeSettings.colorMappings.rangeColorMapping.sort(this._orderByNameAscending),e.bubbleSettings.colorMappings!=null&&e.bubbleSettings.colorMappings.rangeColorMapping!=null&&e.bubbleSettings.colorMappings.rangeColorMapping.sort(this._orderByNameAscending))}}e._isBaseLayer&&e.geometryType==t.datavisualization.Map.GeometryType.Normal&&e.labelSettings.showLabels&&(e._generateLabels(this),e._labelSizeCalculation(this),this._applyTransform(this._scale,this._translatePoint))},isTouch:function(n){var t=n.originalEvent?n.originalEvent:n;return t.pointerType=="touch"||t.pointerType==2||t.type.indexOf("touch")>-1?!0:!1},validateBubblePosition:function(n,t,i){var e=[],o=[],f,u,r;if(n==null&&(n=[],n.push(t)),!this.isPointInPolygon(n,t,!0)){for(f=0,r=0;r<n.length;r++)u={x:(n[r].x+this._translatePoint.x)*this._scale,y:(n[r].y+this._translatePoint.y)*this._scale},(u.y<t.y&&f.y>=t.y||f.y<t.y&&u.y>=t.y)&&(u.x<t.x?e.push(u.x):o.push(u.x)),f=u;if(!e.length>0||!o.length>0){if(e.length>0){for(r=t.x+i;!this.isPointInPolygon(n,{x:r-2,y:t.y},!0);)r=r-2;t.x=parseFloat(r-2-i)}if(o.length>0){for(r=t.x-i;!this.isPointInPolygon(n,{x:r+2,y:t.y},!0);)r=r+2;t.x=parseFloat(r+2+i)}}}return t},isPointInPolygon:function(n,t,i){for(var r,f,o=!1,e=0,u=0;u<n.length;u++){r={x:n[u].x,y:n[u].y};f={x:n[e].x,y:n[e].y};i&&(r={x:(n[u].x+this._translatePoint.x)*this._scale,y:(n[u].y+this._translatePoint.y)*this._scale},f={x:(n[e].x+this._translatePoint.x)*this._scale,y:(n[e].y+this._translatePoint.y)*this._scale});var s=t.y-r.y,h=f.y-r.y,c=f.x-r.x;(r.y<t.y&&f.y>=t.y||f.y<t.y&&r.y>=t.y)&&r.x+s/h*c<t.x&&(o=!0);e=u}return o},dragDown:function(n){var r=n.data.map,t=r.model.layers[0],i=this._mapContainer,u;if(n.type=="mousedown"?(r._startPoint={x:n.pageX-i[0].offsetLeft,y:n.pageY-i[0].offsetTop},r._stopPoint={x:n.pageX-i[0].offsetLeft,y:n.pageY-i[0].offsetTop}):n.type=="touchstart"?(r._startPoint={x:n.originalEvent.changedTouches[0].pageX-i[0].offsetLeft,y:n.originalEvent.changedTouches[0].pageY-i[0].offsetTop},r._stopPoint={x:n.originalEvent.changedTouches[0].pageX-i[0].offsetLeft,y:n.originalEvent.changedTouches[0].pageY-i[0].offsetTop}):n.type=="MSPointerDown"&&(r._startPoint={x:n.originalEvent.pageX-i[0].offsetLeft,y:n.originalEvent.pageY-i[0].offsetTop},r._stopPoint={x:n.originalEvent.pageX-i[0].offsetLeft,y:n.originalEvent.pageY-i[0].offsetTop}),r.mouseClickable=!0,r.model.draggingOnSelection&&t._prevSelectedShapes.length>0){for(u=0;u<t._prevSelectedShapes.length;u++)t._prevSelectedShapes[u].setAttribute("class","e-mapShape"),this._isSVG?(t._prevSelectedShapes[u].setAttribute("fill",t._prevSelectedShapes[u].getAttribute("data-nodeValue")),t._prevSelectedShapes[u].setAttribute("stroke",t.shapeSettings.stroke),t._prevSelectedShapes[u].setAttribute("stroke-width",t.shapeSettings.strokeThickness/this._scale)):(t.fillcolor=t._prevSelectedShapes[u].getAttribute("data-nodeValue"),t.strokecolor=t.shapeSettings.stroke,t.strokeweight=t.shapeSettings.strokeThickness/this._scale);t._prevSelectedShapes=[]}},dragMove:function(t){var i=t.data.map,o=i.model.layers[0],f,e,r=this._mapContainer,u;i.model.draggingOnSelection&&i.mouseClickable&&(i.ispanning=!1,t.type=="mousemove"?i._stopPoint={x:t.pageX-r[0].offsetLeft,y:t.pageY-r[0].offsetTop}:t.type=="touchmove"?i._stopPoint={x:t.originalEvent.changedTouches[0].pageX-r[0].offsetLeft,y:t.originalEvent.changedTouches[0].pageY-r[0].offsetTop}:t.type=="MSPointerMove"&&(i._stopPoint={x:t.originalEvent.pageX-r[0].offsetLeft,y:t.originalEvent.pageY-r[0].offsetTop}),n(".e-mapDragSelection").remove(),u=n('<div class = "e-mapDragSelection"><\/div>'),f=Math.abs(i._stopPoint.x-i._startPoint.x),e=Math.abs(i._stopPoint.y-i._startPoint.y),n(u).css({top:Math.min(i._startPoint.y,i._stopPoint.y),left:Math.min(i._startPoint.x,i._stopPoint.x),width:f,height:e,position:"absolute","z-index":100}),n(u).appendTo("#maps"))},dragUp:function(t){var i=t.data.map,s=Math.abs(i._stopPoint.x-i._startPoint.x),h=Math.abs(i._stopPoint.y-i._startPoint.y),e=i.model.layers[0],o=this._mapContainer,u,r,f;if(i.model.draggingOnSelection){for(n(".e-mapDragSelection").remove(),n(".e-mapDragSelection").css({display:"none"}),i.mouseClickable=!1,u=0;u<i.model.layers[0]._mapShapes.length;u++)r=i.model.layers[0]._mapShapes[u].shape,f=r.getBoundingClientRect(),f.left-o[0].offsetLeft+f.width<Math.min(i._startPoint.x,i._stopPoint.x)||Math.min(i._startPoint.x,i._stopPoint.x)+s<f.left-o[0].offsetLeft||f.top-o[0].offsetTop+f.height<Math.min(i._startPoint.y,i._stopPoint.y)||Math.min(i._startPoint.y,i._stopPoint.y)+h<f.top-o[0].offsetTop||i.model.layers[0]._contains(e._prevSelectedShapes,r)||e._prevSelectedShapes.push(r);for(u=0;u<e._prevSelectedShapes.length;u++)r=e._prevSelectedShapes[u],f=r.getBoundingClientRect(),r.setAttribute("class","e-mapSelectedShape mapShape"),this._isSVG&&(r.setAttribute("fill",e.shapeSettings.selectionColor),r.setAttribute("stroke",e.shapeSettings.selectionStroke),r.setAttribute("stroke-width",e.shapeSettings.selectionStrokeWidth/i._scale))}},isIntersectBubbles:function(){for(var r,n,i,t=[],u=0;u<this.model.layers.length;u++)for(r=this.model.layers[u],Array.prototype.push.apply(t,r._bubbleCollection),n=0;n<r.subLayers.length;n++)Array.prototype.push.apply(t,r.subLayers[n]._bubbleCollection);for(n=0;n<t.length;n++){var f=t[n].getAttribute("cx"),e=t[n].getAttribute("cy"),h=t[n].getAttribute("r");for(i=n+1;i<t.length;i++){var o=t[i].getAttribute("cx"),s=t[i].getAttribute("cy"),c=t[i].getAttribute("r"),l=Math.sqrt((f-o)*(f-o)+(e-s)*(e-s)),a=parseFloat(h)+parseFloat(c);if(l<=a)return!0}}return!1},_zooming:function(n,t){var i=this._getCurrentPoint(t);n>=120?this._zoomingIn(i.x,i.y,t):this._zoomingOut(i.x,i.y,t)},_zoomingIn:function(n,t,i){var r=this,f,e,u;(r.model.zoomSettings.enableZoom||r.model.zoomSettings.enableMouseWheelZoom)&&r._zoomLevel()>=r._minZoom()&&r._zoomLevel()<=r._maxZoom()&&(f=r._scale,e={x:n,y:t},r._prevScale=f,this._isTileMap?(this._tileZoom(r._zoomLevel()-r._zoomFactor(),r._zoomLevel(),e),u=r._zoomLevel(),r.model.zoomSettings.level=r._zoomLevel()+r._zoomFactor(),r._zoomLevel(r._zoomLevel()+r._zoomFactor()),this._generateTiles(this._zoomLevel()),r._translatePoint.x=(r._tileTranslatePoint.x-.5*Math.pow(2,u))/Math.pow(2,u),r._translatePoint.y=(r._tileTranslatePoint.y-.5*Math.pow(2,u))/Math.pow(2,u),r._scale=Math.pow(2,u)):(r._prevPoint={x:r._translatePoint.x,y:r._translatePoint.y},r._translatePoint.x-=(r._width/r._scale-r._width/(r._scale+r._zoomFactor()))/(r._width/n),r._translatePoint.y-=(r._height/r._scale-r._height/(r._scale+r._zoomFactor()))/(r._height/t),r._scale=f+r._zoomFactor(),r.model.zoomSettings.level=r._zoomLevel()+r._zoomFactor(),r._zoomLevel(r._zoomLevel()+r._zoomFactor())),r._trigger("zoomedIn",{originalEvent:i,zoomLevel:r._zoomLevel()}),r.enableAnimation()&&!this._isTileMap&&r._animate(r.model.zoomSettings.animationDuration),r._applyTransform(r._scale,r._translatePoint),r._refrshLayers(),r._resizeShape(),r._updateSliderValue())},_zoomingOut:function(n,t,i){var r=this,u,f,e;(r.model.zoomSettings.enableZoom||r.model.zoomSettings.enableMouseWheelZoom)&&(r._zoomLevel()>this._minZoom()?(u=r._scale,f={x:n,y:t},r._prevScale=u,r._isTileMap?(r._tileZoom(r._zoomLevel()+r._zoomFactor(),r._zoomLevel(),f),e=r._zoomLevel(),r.model.zoomSettings.level=r._zoomLevel()-r._zoomFactor(),r._zoomLevel(r._zoomLevel()-r._zoomFactor()),r._generateTiles(r._zoomLevel()),r._translatePoint.x=(r._tileTranslatePoint.x-.5*Math.pow(2,r._zoomLevel()-r._zoomFactor()))/Math.pow(2,r._zoomLevel()-r._zoomFactor()),r._translatePoint.y=(r._tileTranslatePoint.y-.5*Math.pow(2,r._zoomLevel()-r._zoomFactor()))/Math.pow(2,r._zoomLevel()-r._zoomFactor()),r._scale=Math.pow(2,r._zoomLevel()-r._zoomFactor())):r._scale>1&&(r._prevPoint={x:r._translatePoint.x,y:r._translatePoint.y},r._translatePoint.x-=(r._width/r._scale-r._width/(r._scale-r._zoomFactor()))/(r._width/n),r._translatePoint.y-=(r._height/r._scale-r._height/(r._scale-r._zoomFactor()))/(r._height/t),r._scale=r._scale-r._zoomFactor(),r.model.zoomSettings.level=r._zoomLevel()-r._zoomFactor(),r._zoomLevel(r._zoomLevel()-r._zoomFactor()),r._scale=r._zoomLevel()),r._zoomLevel()<r._minZoom()&&(r.model.zoomSettings.level=r._minZoom(),r._zoomLevel(r._minZoom()),r.zoom(r._zoomLevel())),r._applyTransform(r._scale,r._translatePoint),r._refrshLayers(),r._resizeShape(),r._updateSliderValue()):r.zoom(r._minZoom(),!1),r._trigger("zoomedOut",{originalEvent:i,zoomLevel:r._zoomLevel()}))},_getRatioOfBubble:function(n,t,i,r,u){var e=100/(u-r)*(i-r),f=(parseFloat(t)-parseFloat(n))/100*e+parseFloat(n);return u==r&&(f=(parseFloat(t)-parseFloat(n))/100+parseFloat(n)),f},_reflection:function(t,r){var u=t,f,e,o;if(r!=null&&t!=null){for(f=r.split("."),e=0,f.push(r);e<f.length;e++)t!=i&&u!=null&&(o=u.hasOwnProperty(f[e]),o&&n.each(u,function(n,t){if(n==f[e])return u=t,!1}));return u}return null},clearShapeSelection:function(){var t=this.baseMapIndex(),n;for(this.model.layers[t]._clearShapeWidth(this._scale,this),n=0;n<this.model.layers[t].subLayers.length;n++)this.model.layers[t].subLayers[n]._clearShapeWidth(this._scale,this)},shapeSelectionOnResize:function(){var t=this.baseMapIndex(),n;for(this.model.layers[t]._shapeSelection(),n=0;n<this.model.layers[t].subLayers.length;n++)this.model.layers[t].subLayers[n]._shapeSelection()},addMarkers:function(i,r,u){var e,h,c,f,l,a,s,v,o;for(u=u.length?u:[u],e=t.util.isNullOrUndefined(r)?this.model.layers[i]:this.model.layers[i].subLayers[r],h=e._mapMarkers.length,e.markers=e.markers.concat(u),this._generateMarkers(u,e,this),v=this._getFactor(),o=e._mapMarkers.length-1;o>h-1;o--)c=e._mapMarkers[o],f=e.markers[o],s=this._isTileMap?this._convertTileLatLongtoPoint(f.latitude!=null?f.latitude:f.Latitude,f.longitude!=null?f.longitude:f.Longitude):this._convertLatLongtoPointforMarker(f.latitude!=null?f.latitude:f.Latitude,f.longitude!=null?f.longitude:f.Longitude,v),l=s.x,a=s.y,n(c).css({left:l,top:a})},_generateMarkers:function(i,r,u){for(var h,f,e,s,o=0;o<i.length;o++)e=i[o],r.markerTemplate!=null?(f=n("<div class='e-mapMarker' style='display:block;position:absolute;pointer-events: stroke;'><\/div>"),u._templateDiv.append(f),n(f).css({height:r._height,width:r._width,"margin-top":r._margintop,"margin-left":r._marginleft}),s=n("#"+r.markerTemplate).render(e),u.model.markerSelected==null&&n(f).css({"pointer-events":"none"}),n(f).html(s)):(f=n(' <div class="e-mapMarker" style="display:block;position:absolute;pointer-events: stroke;"><label>'+e.label+"<\/label><\/div>"),r._isSVG?u._templateDiv.append(f):f.appendTo(u._templateDiv),u.model.markerSelected==null&&n(f).css({"pointer-events":"none"})),u._on(n(f),t.eventType.mouseDown,{marker:n(f),data:e},u._markerPressed),u._on(n(f),"dblclick",{marker:n(f),data:e},u._markerPressed),u._on(n(f),"contextmenu",{marker:n(f),data:e},u._markerPressed),u._on(n(f),t.eventType.mouseMove,{markerElement:n(f),markerData:e},u._markerEntered),u._on(n(f),t.eventType.mouseLeave,{markerElement:n(f),markerData:e},u._markerLeaved),r._mapMarkers.push(f),h=r,h.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Left&&(r._height=r._height,r._width=r._width-parseFloat(this._legendDivWidth),r._marginleft=parseFloat(this._legendDivWidth))},refreshMarkers:function(){for(var n,i,u=document.getElementsByClassName("e-mapMarker"),t=!1,f,r=u.length-1;r>=0;r--)f||(f=u[r].parentNode),f.removeChild(u[r]);for(n=this.model.layers[this.baseMapIndex()],n.markers.length>0&&(n._generateMarkerForLayers(this),t=!0),i=0;i<n.subLayers.length;i++)n.subLayers[i].markers&&(n.subLayers[i]._generateMarkerForLayers(this),t=!0);t=n._labelCollection.length>0?!0:t;t&&this._refrshLayers()},_generateTooltipForLayers:function(t){var i,r,u;i=document.documentMode==8?document.querySelectorAll("e-shapeToolTip"):document.getElementsByClassName("e-shapeToolTip");i!=null&&i.length==0?(r=n("<div class='e-shapeToolTip' style='display:none;position:absolute;z-index:1000;pointer-events:none;'><\/div>"),n(document.body).append(r),t._tooltipElement=r):t._tooltipElement=i[0];t.tooltipTemplate==null&&(u=t.shapeSettings.valuePath,u!=null&&(t.tooltipTemplate="defaultTooltip",this._mapContainer.append(n('<div  id="defaultTooltip" style="display:none;"><div style="margin-left:10px;margin-top:-25px;"><div class="e-defaultToolTip" style="display:table"><p style="margin-top:-4px"><label  style="color:rgb(27, 20, 20);font-size:14px;font-weight:normal;font-family:Segoe UI">{{:#data["'+u+'"]}}<\/label><\/p><\/div><\/div><\/div>'))))},_orderByNameAscending:function(n,t){return n.Range==t.Range?0:n.Range>t.Range?1:-1},_resizingContainer:function(){this._scale||(this._scale=this._isTileMap?Math.pow(2,this._zoomLevel()-1):1);this._isTileMap?(this._translatePoint.x=(this._tileTranslatePoint.x-.5*Math.pow(2,this._zoomLevel()-this._zoomFactor()))/Math.pow(2,this._zoomLevel()-this._zoomFactor()),this._translatePoint.y=(this._tileTranslatePoint.y-.5*Math.pow(2,this._zoomLevel()-this._zoomFactor()))/Math.pow(2,this._zoomLevel()-this._zoomFactor())):(!this._translatePoint||this._scale&&this._scale<=1)&&(this._translatePoint.x=this._tileTranslatePoint.x/this._scale,this._translatePoint.y=this._tileTranslatePoint.y/this._scale);this._applyTransform(this._scale,this._translatePoint);this._zoomLevel()>1&&(this._scale=this._isTileMap?Math.pow(2,this._zoomLevel()-this._zoomFactor()):this._zoomLevel(),this._applyTransform(this._scale,this._translatePoint));this._baseScale=1;this._baseTranslatePoint={x:this._translatePoint.x*2,y:this._translatePoint.y*2}},_calculateMarginConditions:function(){var n=this._calculateMinMax();this._translatePoint.y>n.maxY?this._translatePoint.y=n.maxY:this._translatePoint.y<n.minY&&(this._translatePoint.y=n.minY);this._translatePoint.x>n.maxX?this._translatePoint.x=n.maxX:this._translatePoint.x<n.minX&&(this._translatePoint.x=n.minX)},_calculateMinMax:function(){var n=this._groupBorder,t,i,r,u;return this._containerHeight*this._scale<=this._height?(i=(this._height-this._containerHeight*this._scale)/(2*this._scale)-n.y,u=(this._height-this._containerHeight*this._scale)/(2*this._scale)-n.y):(i=-n.y+1,u=(this._height-this._containerHeight*this._scale)/this._scale-n.y-1),this._containerWidth*this._scale<=this._width?(t=(this._width-this._containerWidth*this._scale)/(2*this._scale)-n.x,r=(this._width-this._containerWidth*this._scale)/(2*this._scale)-n.x):(t=-n.x+1,r=(this._width-this._containerWidth*this._scale)/this._scale-n.x-1),{minX:r,maxX:t,minY:u,maxY:i}},_findMidPointofPoylgon:function(n){var y,p,r,t;if(n.length>0){var k=0,v=n.length,u=n[0],d=u.x,g=u.x,nt=u.y,tt=u.y,h,c,f,e,o=0,s=0,i=0;for(r=k;r<=v-1;r++)y=n[r],h=y.x,c=y.y,r==v-1?(f=u.x,e=u.y):(p=n[r+1],f=p.x,e=p.y),o=o+Math.abs(h*e-f*c),s=s+Math.abs((h+f)*(h*e-f*c)),i=i+Math.abs((c+e)*(h*e-f*c));o=.5*o;s=1/(4*o)*s;i=1/(4*o)*i;var w={x:0,y:0},l={x:0,y:0},b={x:0,y:0},a={x:0,y:0};for(r=k;r<=v-1;r++)t=n[r],t.x>s?t.y<i&&i-t.y<i-w.y?w={x:t.x,y:t.y}:t.y>i&&(l.y==0||t.y-i<l.y-i)&&(l={x:t.x,y:t.y}):t.y<i&&i-t.y<i-b.y?b={x:t.x,y:t.y}:t.y>i&&(a.y==0||t.y-i<a.y-i)&&(a={x:t.x,y:t.y});return{x:s,y:i,rightMin:w,rightMax:l,leftMin:b,leftMax:a,points:n}}},_convertPointToLatLong:function(n,t,i,r){var u,f,e,o,s;return i==null&&(i=[[-180,-85],[180,85]]),u=this._isTileMap?Math.pow(2,1)*256:r==null?Math.min(this._height,this._width):Math.min(this._height,this._width)*r,o=Math.min(n,0,u-1)/u-.5,s=.5-Math.min(t,0,u-1)/u,f=90-360*Math.atan(Math.exp(-s*2*Math.PI))/Math.PI,e=360*o,{x:f,y:e}},_convertTileLatLongtoPointForShapes:function(n,t,i,r){var u,s,f,h,e,o;return i==null&&(i=[[-180,-85],[180,85]]),u=this._isTileMap?Math.pow(2,1)*256:r==null?Math.min(this._height,this._width):Math.min(this._height,this._width)*r,s=(t+180)/360,f=Math.sin(n*Math.PI/180),f==-1&&(f=-.5),h=.5-Math.log((1+f)/(1-f))/(4*Math.PI),this._isMapCoordinates?(e=Math.min(Math.max(s*u+.5,0),u-1),o=Math.min(Math.max(h*u+.5,0),u-1)):r!=null?(e=Math.abs((t-this._mapBounds[0][0])*r),o=Math.abs((this._mapBounds[1][1]-n)*r)):(e=t,o=n),{x:e,y:o}},_convertTileLatLongtoPoint:function(n,t){var u=Math.pow(2,this._zoomLevel())*256,e=(t+180)/360,f=Math.sin(n*Math.PI/180),o=.5-Math.log((1+f)/(1-f))/(4*Math.PI),i,r;return this._isMapCoordinates?(i=e*u+.5+this._tileTranslatePoint.x,r=o*u+.5+this._tileTranslatePoint.y):factor!=null?(i=Math.abs((t-this._mapBounds[0][0])*factor),r=Math.abs((this._mapBounds[1][1]-n)*factor)):(i=t,r=n),{x:i,y:r}},_convertLatLongtoPointforMarker:function(n,t,i){var r=this._convertTileLatLongtoPointForShapes(n,t,this._mapBounds,i);return{x:(r.x+this._translatePoint.x)*this._scale,y:(r.y+this._translatePoint.y)*this._scale}},_animate:function(t){function s(n,t){return n[0]==t[0]?null:(t[1]-n[1])/(t[0]-n[0])}function h(n,t){return t===null?n[0]:n[1]-t*n[0]}this._calculateMarginConditions();n(this._rootgroup).stop(!0,!1);this._sliderControl!=null&&n(this._sliderControl).stop(!0,!1);var i=this,e={x:this._translatePoint.x,y:this._translatePoint.y},r=[this._prevPoint.x,this._prevPoint.y],u=[this._translatePoint.x,this._translatePoint.y];var f=s(r,u),c=h(r,f),l=u[0]-r[0],a=u[1]-r[1],v=this._scale-this._prevScale,o=this._scale;this._updateSliderValue();n(this._rootgroup).animate({count:10},{duration:t,step:function(n,t){var u=i._prevScale+n*(v/t.end),e=r[0]+n*(l/t.end)/(u/o),s;s=f==null?r[1]+n*(a/t.end):f*e+c;i._isSVG?i._rootgroup.setAttribute("transform","scale("+u+") translate("+e+", "+s+")"):(i._rootgroup.coordorigin=-e+","+-s,i._rootgroup.coordsize=i._width/u+","+i._height/u);i._translatePoint.x=e;i._translatePoint.y=s;i._scale=u;i._refrshLayers();i._resizeShape()},complete:function(){i._translatePoint.x=e.x;i._translatePoint.y=e.y;i._scale=o;this.count=0}})},_applyTransform:function(n,t){this._scale=n;this._translatePoint=t;this._isTileMap||this._calculateMarginConditions();this._translatePoint.x!=Number.POSITIVE_INFINITY&&this._translatePoint.y!=Number.POSITIVE_INFINITY&&(this._isSVG?this._rootgroup.setAttribute("transform","scale("+this._scale+") translate("+this._translatePoint.x+", "+this._translatePoint.y+")"):(this._rootgroup.coordorigin=-this._translatePoint.x+","+-this._translatePoint.y,this._rootgroup.coordsize=this._width/n+","+this._height/n))},_mouseMove:function(n){var t,i,f,e,r,u;n.type=="touchmove"&&n.originalEvent.touches.length>1?this._isPinching=!0:(t=this,t.ispanning&&t.enablePan()&&(n.preventDefault(),n.type=="mousemove"||n.type=="pointermove"?i={x:n.pageX,y:n.pageY}:n.type=="touchmove"?i={x:n.originalEvent.changedTouches[0].pageX,y:n.originalEvent.changedTouches[0].pageY}:n.type=="MSPointerMove"&&(i={x:n.originalEvent.pageX,y:n.originalEvent.pageY}),this._isTileMap&&(f=this._tileTranslatePoint.x-(this._dragStartX-i.x),e=this._tileTranslatePoint.y-(this._dragStartY-i.y),this._tileTranslatePoint.x=f,this._tileTranslatePoint.y=e,this._generateTiles(this._zoomLevel())),r=t._translatePoint.x-(t._dragStartX-i.x)/t._scale,u=t._translatePoint.y-(t._dragStartY-i.y)/t._scale,(r!=t._translatePoint.x||u!=t._translatePoint.y)&&(t._isDragging=!0),this._translatePoint.x=r,this._translatePoint.y=u,t._applyTransform(t._scale,t._translatePoint),t._dragStartX=i.x,t._dragStartY=i.y,t._refrshLayers()))},_svgMouseLeave:function(){var n=this;n.ispanning=!1},_mouseWheel:function(t){if(this.model.zoomSettings.enableMouseWheelZoom){this.enableAnimation()&&n(this._rootgroup).stop(!0,!1);var i=t.originalEvent,r=t;this._isSVG||(i=t);t.target.className!="e-LegendDiv"&&t.target.className!="e-defaultLegendLabel"&&(i.preventDefault?i.preventDefault():r.preventDefault(),this._isSVG?this._zooming(t.originalEvent.wheelDelta,t.originalEvent):this._zooming(t.originalEvent.wheelDelta,i))}},_mouseButtonUp:function(n){var t=this;t.ispanning&&t._trigger("panned",{originalEvent:n});t.ispanning=!1},_mouseUp:function(){this.ispanning=!1;this._isDragging=!1},_mouseButtonDown:function(t){var r,i;this._isPinching=!1;t.target.className!="e-vhandle"&&(this._isNavigationPressed=!1,this.enableAnimation()&&(n(this._rootgroup).stop(!0,!1),this._sliderControl!=null&&n(this._sliderControl).stop(!0,!1)),t.type=="touchstart"&&t.originalEvent.touches.length>1||(t.type=="mousedown"||t.type=="pointerdown"?r={x:t.pageX,y:t.pageY}:t.type=="touchstart"?r={x:t.originalEvent.changedTouches[0].pageX,y:t.originalEvent.changedTouches[0].pageY}:t.type=="MSPointerDown"&&(r={x:t.originalEvent.pageX,y:t.originalEvent.pageY}),i=this,i._scale>1&&i.model.enablePan&&(t.preventDefault(),i.ispanning=!0),i._dragStartX=r.x,i._dragStartY=r.y))},_getCurrentPoint:function(n){var t=this._mapContainer,i=n.pageX-t.offset().left,r=n.pageY-t.offset().top;return{x:i,y:r}},_legendDoubleClick:function(n){for(var t=n.target;t.parentNode!=null&&t.parentNode.className!="ScrollLegendDiv e-scroller e-js e-widget";)t=t.parentNode;t.parentNode!=null&&t.parentNode.className=="ScrollLegendDiv e-scroller e-js e-widget"&&(this._isNavigationPressed=!0)},_mapRightClick:function(n){this.model.rightClick!=""&&this._trigger("rightClick",{data:{event:n}})},_mapDown:function(){this.model.rightClick!=""&&(this._longPressTimer=new Date)},_mapClick:function(n){var i=new Date,t;if(this.model.click!=""&&this._trigger("click",{data:{event:n}}),this.model.layers[this.baseMapIndex()].legendSettings.toggleVisibility)for(this.model.layers[this.baseMapIndex()]._legendToggleVisibility(this.model.layers[this.baseMapIndex()],n,this),t=0;t<this.model.layers[this.baseMapIndex()].subLayers.length;t++)this.model.layers[this.baseMapIndex()].subLayers[t]._legendToggleVisibility(this.model.layers[this.baseMapIndex()].subLayers[t],n,this);this._doubleTapTimer!=null&&i-this._doubleTapTimer<500&&this._mapDoubleClick(n);this._doubleTapTimer=i;i-this._longPressTimer>1500&&this._mapRightClick(n)},_doubleClick:function(n){var t=this,f=t._zoomLevel(),r,u,i;t._legendDoubleClick(n);this._isNavigationPressed||n.target.className.toString().indexOf("e-icon1")!=-1||(r=this._getCurrentPoint(n),t.model.zoomSettings.enableZoom&&t._zoomLevel()+t._zoomFactor()>=t._minZoom()&&t._zoomLevel()+t._zoomFactor()<=t._maxZoom()&&(u=t._scale,t._prevScale=u,this._isTileMap?(this._tileZoom(t._zoomLevel()-t._zoomFactor(),t._zoomLevel(),r),i=t._zoomLevel(),t.model.zoomSettings.level=t._zoomLevel()+t._zoomFactor(),t._zoomLevel(t._zoomLevel()+t._zoomFactor()),this._generateTiles(this._zoomLevel()),t._translatePoint.x=(t._tileTranslatePoint.x-.5*Math.pow(2,i))/Math.pow(2,i),t._translatePoint.y=(t._tileTranslatePoint.y-.5*Math.pow(2,i))/Math.pow(2,i),t._scale=Math.pow(2,i)):(t._prevPoint={x:t._translatePoint.x,y:t._translatePoint.y},t._translatePoint.x-=(t._width/t._scale-t._width/(t._scale+t._zoomFactor()))/(t._width/r.x),t._translatePoint.y-=(t._height/t._scale-t._height/(t._scale+t._zoomFactor()))/(t._height/r.y),t._scale=u+t._zoomFactor(),t.model.zoomSettings.level=t._zoomLevel()+t._zoomFactor(),t._zoomLevel(t._zoomLevel()+t._zoomFactor())),f<t._zoomLevel()&&t._trigger("zoomedIn",{originalEvent:null,zoomLevel:t._zoomLevel()}),t.enableAnimation()&&!this._isTileMap?t._animate(t.model.zoomSettings.animationDuration):(t._applyTransform(t._scale,t._translatePoint),t._refrshLayers(),t._resizeShape()),t._updateSliderValue()))},_mapDoubleClick:function(n){this.model.doubleClick!=""&&this._trigger("doubleClick",{data:{event:n}})},_tileZoom:function(n,t,r){var c=this,u;if(t>0&&t<20){this._tileDiv.html("");u=Math.pow(2,n)*256;r==i&&(r={x:this._width/2,y:this._height/2});var f=Math.pow(2,t)*256,e=(r.x-this._tileTranslatePoint.x)/u*100,o=(r.y-this._tileTranslatePoint.y)/u*100,s=e*f/100,h=o*f/100;this._tileTranslatePoint.x=r.x-s;this._tileTranslatePoint.y=r.y-h}},_bubbleEnterFunction:function(t){if(t.data.templateID!=null){var i=n("#"+t.data.templateID).render(t.data.itemsrc);n(t.data.htmlobj).html(i)}return n(document.body).append(t.data.htmlobj),n(t.data.htmlobj).css("display","block").css({left:t.pageX+8+"px",top:t.pageY+6+"px"})},_bubbleleaveFunction:function(t){return n(t.data.htmlobj).remove(),n(t.data.htmlobj).css("display","none")},_bubbleOverFunction:function(t){return n(t.data.htmlobj).css("display","block").css({left:t.pageX+8+"px",top:t.pageY+6+"px"})},_polyEnterFunction:function(r){var p=jQuery.uaMatch(navigator.userAgent),u=r.data.Param1,f,w=this.isTouch?this.isTouch(r):!1,h,e,o,s,a,c,l,v,y;if(w&&p.browser=="chrome"?(h=r.originalEvent.changedTouches[0],f=document.elementFromPoint(h.clientX,h.clientY)):f=r.data.Param2.shape,e=r.data.map,o=jQuery.grep(u._smartLabels,function(n){return n.shape==f}),u.enableMouseHover){if(u._clearShapeWidth(e._scale,this),u._prevHoverdLegend==null||u._contains(u._prevSelectedLegends,u._prevHoverdLegend)||u._prevHoverdLegend.css("background-color",e._isSVG?u._prevHoveredShape.getAttribute("fill"):u._prevHoveredShape.fillcolor.value),e._isSVG?(f.setAttribute("stroke-width",u.shapeSettings.highlightBorderWidth/e._scale),f.setAttribute("stroke",u.shapeSettings.highlightStroke)):(f.strokeweight=u.shapeSettings.highlightBorderWidth,f.strokecolor=u.shapeSettings.highlightStroke/e._scale),u.shapeSettings.highlightColor!=null&&(u.shapeSettings.highlightColor=="transparent"||u._contains(u._prevSelectedShapes,f)||(f.setAttribute("class","e-mapHighlightedShape mapShape"),e._isSVG?(u.shapeSettings.highlightColor!="none"&&f.highlightcolor==null?f.setAttribute("fill",u.shapeSettings.highlightColor):f.setAttribute("fill",f.highlightcolor),f.setAttribute("stroke-width",u.shapeSettings.highlightBorderWidth/e._scale),f.setAttribute("stroke",u.shapeSettings.highlightStroke)):f.fillcolor=u.shapeSettings.highlightColor),o.length>0&&!u._contains(u._prevSelectedLegends,o[0].legend)&&(o[0].legend[0].setAttribute("class","e-mapHighlightedShape mapShape"),o[0].legend.css("background-color",u.shapeSettings.highlightColor),u._prevHoverdLegend=o[0].legend),u._prevHoveredShape=f),u.legendSettings!=null&&u.legendSettings.mode==t.datavisualization.Map.LegendMode.Interactive&&u.shapeSettings.colorMappings!=null&&u.shapeSettings.colorMappings.rangeColorMapping!=null&&!u.shapeSettings.autoFill&&(u.legendSettings.showLegend==i||u.legendSettings.showLegend))for(s=0;s<u._mapShapes.length;s++)if(a=u._mapShapes[s].shape,a==f&&(c=null,u.shapeSettings.colorMappings.rangeColorMapping!=i&&(c=u.shapeSettings.colorMappings.rangeColorMapping),l=u.legendSettings.width,u.legendSettings.width==i&&(l=150),v=l/c.length/10,u._mapShapes[s].legendrect!=null)){y=u._mapShapes[s].legendrect.marginLeft;n(u._interactiveArrow).css({"margin-left":y+Math.ceil(v)-u._interactiveArrow.width()/2,display:"block",visibility:"visible"});break}e._trigger("mouseover",{originalEvent:r.data.Param2})}},_updateShapeRect:function(n){for(var i,t=0;t<n._mapShapes.length;t++)i=n._mapShapes[t],i.left==null&&(i.left=n._mapShapes[t].shape.getBoundingClientRect().left,i.right=n._mapShapes[t].shape.getBoundingClientRect().right,i.top=n._mapShapes[t].shape.getBoundingClientRect().top,i.bottom=n._mapShapes[t].shape.getBoundingClientRect().bottom,n._mapShapes[t]=i)},_polyUpFunction:function(t){var i=t.data.Param2,o=t.data.Param3,s=this.isTouch(t),h,r,f,e,b;if(s&&t.stopImmediatePropagation(),this.model.zoomSettings.enableZoomOnSelection&&!this._isDragging&&!this._isTileMap&&!this._isPinching){if(this._isSVG)h=o.getBBox(),this._zoomOnSelection(h);else for(this._updateShapeRect(i),r=0;r<i._mapShapes.length;r++)if(o==i._mapShapes[r].shape){var c=i._mapShapes[r].left,l=i._mapShapes[r].top,k=i._mapShapes[r].right,d=i._mapShapes[r].bottom,g={x:c,y:l,width:k-c,height:d-l};this._zoomOnSelection(g);break}this.ispanning=!1}if(f=i._tooltipElement,f!=null&&n(f).delay(200).queue(function(t){n(this).css("display","none");t()}),s&&i.showTooltip){var a=this.getEventXY(t),u=this,v=u.model.cachedXY,y=10,p,w;p=Math.abs(v.x-a.X);w=Math.abs(v.y-a.Y);p<y&&w<y&&(this.touchEnd=!0,e=t.data.Param1,t.data.Param1=i,t.data.Param2=e,u._polyMoveFunction(t),t.data.Param1=e,t.data.Param2=i,u.tooltipElement=i._tooltipElement[0],n(u.tooltipElement).stop(!0,!0),b=u,u._timeOut=function(){b._timeOut()},u.touchTimeOut=window.setTimeout(u._timeOut,1e3))}},_timeOut:function(){n(this.tooltipElement).fadeOut()},_markerPressed:function(n){this._trigger("markerSelected",{originalEvent:n.data})},_markerEntered:function(n){this._trigger("markerEnter",{data:n.data,originalEvent:n})},_markerLeaved:function(n){this._trigger("markerLeave",{data:n.data,originalEvent:n})},_polyMouseDown:function(i){var o=i.data.Param2,f,p,w,b;if(i.type=="mousedown"||i.type=="pointerdown"?f={x:i.pageX||i.originalEvent.pageX,y:i.pageY||i.originalEvent.pageY}:i.type=="touchstart"?f={x:i.originalEvent.changedTouches[0].pageX,y:i.originalEvent.changedTouches[0].pageY}:i.type=="MSPointerDown"&&(f={x:i.originalEvent.pageX,y:i.originalEvent.pageY}),this.model.cachedXY=f,!this.isTouch(i)&&!t.util.isNullOrUndefined(i.data.Param1)&&i.data.Param1.hasOwnProperty("data")&&o.showTooltip){var r=o._tooltipElement,y=o.tooltipTemplate,e=n(this._mapContainer).offset();e==null&&(e={left:0,width:0,height:0});var u=o._tooltipSize,s=this._height,h=this._width;if(r!=null&&y!=null){n(r).css({display:"block"});p=n("#"+y).render(i.data.Param1.data);n(r).html(p);w=r[0]!=null?r[0].clientHeight:r.clientHeight;b=r[0]!=null?r[0].clientWidth:r.clientWidth;u={height:w,width:b};var l=f.x-n(this._mapContainer).offset().left,a=f.y-n(this._mapContainer).offset().top,c=f.x,v=f.y;u.width+l>=h&&(c-u.width>e.left?c=e.left+h-(h-l)-u.width:c-=u.width+l-h);u.height>s?s+u.height>this._height+a&&(v-=e.top+10):u.height+a>=s&&(v-=u.height+a-s);n(r).css({left:c+10,top:v+10})}}},getEventXY:function(n){var t,i;return t=n.pageX||n.originalEvent.pageX||n.originalEvent.changedTouches[0].pageX,i=n.pageY||n.originalEvent.pageY||n.originalEvent.changedTouches[0].pageY,{X:t,Y:i}},findData:function(n,t,i){for(var r=0;r<n.length;r++)if(n[r][i]===t)return n[r]},_getEventArgs:function(n,i){var u,f,e,r,o,s,h,i;return i=i?i:n.target.id,u=parseInt(i.split("_layerIndex")[1].split("_")[0],10),f=parseInt(i.split("_shapeIndex")[1].split("_")[0],10),i.indexOf("_sublayerIndex")>-1&&(e=parseInt(i.split("_sublayerIndex")[1].split("_")[0],10)),r=t.util.isNullOrUndefined(e)?this.model.layers[u]:this.model.layers[u].subLayers[e],r.dataSource&&(h=this.findData(r.dataSource,r._polygonData[f].properties[r.shapePropertyPath],r.shapeDataPath)),o=document.getElementById(i),s=r._polygonData[f],{shapeData:s,shape:o,shapeIndex:f,layerIndex:u,subLayerIndex:e,layer:r,data:h}},_mapMouseClick:function(n){if(n.target.id.indexOf("layerIndex")>-1){var t=this._getEventArgs(n);this._polyClickFunction(this._mouseClickEventArgs(t,n))}},_mapMouseDown:function(n){if(n.target.id.indexOf("layerIndex")>-1){var t=this._getEventArgs(n);n.data={Param1:{shape:t.shape,shapeData:t.shapeData,shapeIndex:t.shapeIndex},Param2:t.layer};t.data&&(n.data.Param1.data=t.data);this._polyMouseDown(n)}},_mapMouseMove:function(n){if(n.target.id.indexOf("layerIndex")>-1){var t=this._getEventArgs(n);n.data={Param1:t.layer,Param2:{shapeIndex:t.shapeIndex,shape:t.shape,shapeData:t.shapeData},Param3:t.shape};t.data&&(n.data.Param2.data=t.data);this._polyMoveFunction(n)}},_mapMouseUp:function(n){if(n.target.id.indexOf("layerIndex")>-1){var t=this._getEventArgs(n);this._polyUpFunction(this._mouseClickEventArgs(t,n))}},_mouseClickEventArgs:function(n,t){return t.data={Param1:{shapeIndex:n.shapeIndex,shape:n.shape,shapeData:n.shapeData},Param2:n.layer,Param3:n.shape},n.data&&(t.data.Param1.data=n.data),t},_mouseMoveEventArgs:function(n,t){return t.data={Param1:n.layer,Param2:{shape:n.shape,shapeData:n.shapeData,shapeIndex:n.shapeIndex},map:this},n.data&&(t.data.Param2.data=n.data),t},_mapMouseEnter:function(n){var t,i;n.target.id.indexOf("layerIndex")>-1?this._prevEnterElement!=n.target.id&&(this._prevEnterElement!=""&&(t=this._getEventArgs(n,this._prevEnterElement),this._polyLeaveFunction(this._mouseMoveEventArgs(t,n))),t=this._getEventArgs(n),this._polyEnterFunction(this._mouseMoveEventArgs(t,n)),this._prevEnterElement=n.target.id):this._prevEnterElement!=""&&(t=this._getEventArgs(n,this._prevEnterElement),this._polyLeaveFunction(this._mouseMoveEventArgs(t,n)),this._prevEnterElement="",n.target.id=i)},_polyClickFunction:function(r){var k=r.ctrlKey,u=r.data.Param2,h=this,a=10,v=h.model.cachedXY,y,p,w=h.getEventXY(r),f,s,o,b,c,l,e;if(y=Math.abs(v.x-w.X),p=Math.abs(v.y-w.Y),y<a&&p<a){if(f=r.data.Param3,r.data.param4!=i&&(s=r.data.param4),o=jQuery.grep(u._smartLabels,function(n){return n.shape==f}),h._isPolygonSelected=!0,u.enableSelection)if(k&&u.selectionMode==t.datavisualization.Map.SelectionMode.Multiple)o!=null&&o.length>0&&(n.inArray(o[0].legend,u._prevSelectedLegends)==-1?(o[0].legend[0].setAttribute("class","e-mapSelectedShape mapShape"),o[0].legend.css("background-color",u.shapeSettings.selectionColor),u._prevSelectedLegends.push(o[0].legend)):(c=u._prevSelectedLegends.indexOf(o[0].legend),o[0].legend[0].setAttribute("class","e-mapShape"),o[0].legend.css("background-color",o[0].legend[0].getAttribute("data-nodeValue")),u._prevSelectedLegends.splice(c,1))),s!=null&&(l=document.getElementsByClassName("e-map-labelContainer"),n.inArray(s,u._prevSelectedTemp)==-1?(n(s).css("background-color",u.shapeSettings.selectionColor),n(s).css("width",l[0].clientWidth),u._prevSelectedTemp.push(s)):(n(s).css("background-color",""),b=u._prevSelectedTemp.indexOf(s),u._prevSelectedTemp.splice(b,1))),n.inArray(f,u._prevSelectedShapes)==-1?(f.setAttribute("class","e-mapSelectedShape mapShape"),this._isSVG?(f.setAttribute("fill",u.shapeSettings.selectionColor),f.setAttribute("stroke",u.shapeSettings.selectionStroke),f.setAttribute("stroke-width",u.shapeSettings.selectionStrokeWidth/h._scale)):(f.fillcolor=u.shapeSettings.selectionColor,f.strokecolor=u.shapeSettings.selectionStroke,f.strokeweight=u.shapeSettings.selectionStrokeWidth/h._scale),u._prevSelectedShapes.push(f),u.selectedItems.push(r.data.Param1)):(f.setAttribute("class","e-mapShape"),f.setAttribute("fill",f.getAttribute("data-nodeValue")),f.setAttribute("stroke",u.shapeSettings.stroke),f.setAttribute("stroke-width",u.shapeSettings.strokeThickness/this._scale),c=u._prevSelectedShapes.indexOf(f),u._prevSelectedShapes.splice(c,1),u.selectedItems.splice(c,1));else{for(e=0;e<u._prevSelectedLegends.length;e++)u._prevSelectedLegends[e][0].setAttribute("class","e-mapShape"),u._prevSelectedLegends[e].css("background-color",u._prevSelectedLegends[e][0].getAttribute("data-nodeValue"));for(o!=null&&o.length>0&&(o[0].legend[0].setAttribute("class","e-mapSelectedShape mapShape"),o[0].legend.css("background-color",u.shapeSettings.selectionColor),u._prevSelectedLegends.push(o[0].legend)),e=0;e<u._prevSelectedShapes.length;e++)u._prevSelectedShapes[e].setAttribute("class","e-mapShape"),this._isSVG?(u._prevSelectedShapes[e].setAttribute("fill",u._prevSelectedShapes[e].getAttribute("data-nodeValue")),u._prevSelectedShapes[e].setAttribute("stroke",u.shapeSettings.stroke),u._prevSelectedShapes[e].setAttribute("stroke-width",u.shapeSettings.strokeThickness/this._scale)):(u.fillcolor=u._prevSelectedShapes[e].getAttribute("data-nodeValue"),u.strokecolor=u.shapeSettings.stroke,u.strokeweight=u.shapeSettings.strokeThickness/this._scale);if(s!=null)if(l=document.getElementsByClassName("e-map-labelContainer"),n.inArray(s,u._prevSelectedTemp)==-1){for(e=0;e<u._prevSelectedTemp.length;e++)n(u._prevSelectedTemp[e]).css("background-color","");u._prevSelectedTemp=[];n(s).css("background-color",u.shapeSettings.selectionColor);n(s).css("width",l[0].clientWidth);u._prevSelectedTemp.push(s)}else{for(e=0;e<u._prevSelectedTemp.length;e++)n(u._prevSelectedTemp[e]).css("background-color","");u._prevSelectedTemp=[]}this._isPinching||n.inArray(f,u._prevSelectedShapes)!=-1?(u._prevSelectedShapes=[],u.selectedItems=[]):(f.setAttribute("class","e-mapSelectedShape mapShape"),u._prevSelectedShapes=[],u.selectedItems=[],this._isSVG?(f.setAttribute("fill",u.shapeSettings.selectionColor),f.setAttribute("stroke",u.shapeSettings.selectionStroke),f.setAttribute("stroke-width",u.shapeSettings.selectionStrokeWidth/h._scale)):(f.fillcolor=u.shapeSettings.selectionColor,f.strokecolor=u.shapeSettings.selectionStroke,f.strokeweight=u.shapeSettings.selectionStrokeWidth/h._scale),u._prevSelectedShapes.push(f),u.selectedItems.push(r.data.Param1))}h._trigger("shapeSelected",{originalEvent:u.selectedItems})}},_updateSelection:function(n,i,r){n.enableSelection&&(n.selectionMode!=t.datavisualization.Map.SelectionMode.Multiple&&(n._prevSelectedShapes.pop(),n.selectedItems.pop()),n._prevSelectedShapes.push(i),n.selectedItems.push(r))},_zoomOnSelection:function(n){var u,t,i,r;this._isSVG||(n.x=n.x-this._baseTranslatePoint.x/2-n.width/2,n.y=n.y-this._baseTranslatePoint.y/2-n.height/2);t=n.width;i=n.height;u=(this._width-100)/(this._height-100)>t/i?(this._height-100)/i:(this._width-100)/t;this._prevScale=this._scale;this._scale=u;this._prevPoint={x:this._translatePoint.x,y:this._translatePoint.y};this.model.zoomSettings.level=this._scale-this._baseScale+1;this._zoomLevel(this._scale-this._baseScale+1);r=this._zoomLevel();r>this._minZoom()&&r<this._maxZoom()||(this.model.zoomSettings.level=r>(this._maxZoom()-this._minZoom())/2+this._minZoom()?this._maxZoom():this._minZoom(),this._zoomLevel(this.model.zoomSettings.level),this._scale=this._zoomLevel()+this._baseScale-1);var f=this._width/2-t*this._scale/2,e=f/this._scale,o=this._height/2-i*this._scale/2,s=o/this._scale;this._translatePoint.x=-n.x+e;this._translatePoint.y=-n.y+s;this.enableAnimation()&&!this._isTileMap?this._animate(2*this.model.zoomSettings.animationDuration):(this._applyTransform(this._scale,this._translatePoint),this._updateSliderValue())},_polyMoveFunction:function(i){var s=i.data.Param1,it=this.isTouch?this.isTouch(i):!1,k,e,c,nt,tt,l;if((!it&&i.type.toString().indexOf("move")>-1||this.touchEnd)&&(this.touchEnd=!1,this.model!=null)){var h=this.model.layers[this.baseMapIndex()],r=s._tooltipElement,d=t.util.isNullOrUndefined(s.tooltiptemplate)?s.tooltipTemplate:s.tooltiptemplate,g=i.data.Param2,u=s._tooltipSize,f=n(this._mapContainer).offset();f==null&&(f={left:0,top:0,width:0,height:0});l=h.legendSettings.dockOnMap&&h.legendSettings.dockPosition=="right"?this._width+h.legendSettings.width+20+h.legendSettings.leftLabel.length*10+h.legendSettings.rightLabel.length*10:this._width;var a=this._height,v=l,y=this.getEventXY(i),p=y.X-f.left,w=y.Y-f.top,o=y.X,b=y.Y;if(r!=null&&d!=null&&g.hasOwnProperty("data")&&i.data.Param1.showTooltip){if(n(r).css({display:"block"}),k=this.model.locale,e=n.extend({},g.data),k&&this.model.enableGroupSeparator)for(c in e)e[c]=isNaN(parseFloat(e[c]))?e[c]:parseFloat(e[c]).toLocaleString(k);nt=n("#"+d).render(e);n(r).html(nt);tt=r[0]!=null?r[0].clientHeight:r.clientHeight;l=r[0]!=null?r[0].clientWidth:r.clientWidth;u={height:tt,width:l};t.util.isNullOrUndefined(this.touchTimeOut)||window.clearTimeout(this.touchTimeOut);u.width+p>=v&&!this.model.enableRTL&&(o-u.width>f.left?o=f.left+v-(v-p)-u.width:o-=u.width+p-v);u.height>a?a+u.height>this._height+w&&(b-=f.top+10):u.height+w>=a&&(b-=u.height+w-a);n(r).css({left:this.model.enableRTL?(p-u.width<0)?o+10:o-l:o+10,top:b})}}},_polyLeaveFunction:function(i){var r=i.data.Param1,h=this.isTouch(i),e=i.data.map,o=i.data.Param2.shape,s=r._tooltipElement,f=r._prevSelectedShapes,u;if(f.length>0)for(o.setAttribute("class","e-mapShape"),u=0;u<f.length;u++)f[u].setAttribute("class","e-mapSelectedShape mapShape");else o.setAttribute("class","e-mapShape");s==null||h||n(s).css("display","none");r.legendSettings!=null&&r.legendSettings.mode==t.datavisualization.Map.LegendMode.Interactive&&r._interactiveArrow!=null&&n(r._interactiveArrow).css("display","none");r._clearShapeWidth(e._scale,this);e._trigger("mouseleave",{originalEvent:i.data.Param2})},_wireEvents:function(){function y(t){i.enableAnimation()&&n(i._rootgroup).stop(!0,!1);t.preventDefault(t);i._zooming(-40*t.detail,t)}var o=t.browserInfo(),r=o.isMSPointerEnabled,u=o.pointerEnabled,s=r?u?"pointerup":"MSPointerUp":"touchend mouseup",l=r?u?"pointerdown":"MSPointerDown":"touchstart mousedown",a=r?u?"pointermove":"MSPointerMove":"touchmove mousemove",v=jQuery.uaMatch(navigator.userAgent),f=v.browser.toLowerCase(),h,i,e,c;n(this._mapContainer).off();this._on(n(this._mapContainer),t.eventType.mouseDown,this._mouseButtonDown);n(document).keydown({className:"home",map:this},this._keyboardKeysPressed);this._on(n(this._mapContainer),t.eventType.mouseMove,this._mouseMove);this._on(n(document),t.eventType.mouseUp,this._mouseUp);h=!!navigator.userAgent.match(/Trident\/7\./);h&&(f="msie");window.navigator.msPointerEnabled?(this._on(n(this._mapContainer),"MSPointerUp",this._mouseButtonUp),this.model.zoomSettings.enableZoom&&n(this._mapContainer).css("-ms-touch-action","none")):this._on(n(this._mapContainer),"mouseup",this._mouseButtonUp);i=this;this._browser=f;f!="mozilla"?this._on(n(this._mapContainer),"mousewheel",this._mouseWheel):(e=this._svgDocument||this._mapContainer[0],e.addEventListener&&e.addEventListener("DOMMouseScroll",y,!1));this._on(n(this._mapContainer),"touchstart",this._mapDown);this._on(n(this._mapContainer),s,this._mapMouseClick);this._on(n(this._mapContainer),l,this._mapMouseDown);this._on(n(this._mapContainer),a,this._mapMouseMove);this._on(n(this._mapContainer),s,this._mapMouseUp);this._on(n(this._mapContainer),t.eventType.mouseMove,this._mapMouseEnter);c=this;n(this._mapContainer).get(0).addEventListener(t.isTouchDevice()?"touchend":"mouseup",function(){c._mapClick()},!0);this._on(n(this._mapContainer),"dblclick",this._doubleClick);(this.model.enableResize||this.model.isResponsive)&&(t.isTouchDevice()?this._on(n(window),"orientationchange",this._mapResize):this._on(n(window),"resize",this._mapResize))},clip:function(n,t,i){return Math.min(Math.max(n,t),i)},pointToLatLong:function(n,t){var i=this,u,f,e=i._getFactor(),o=n/i._scale-i._translatePoint.x,s=t/i._scale-i._translatePoint.y,r=Math.min(i._height,i._width)*e,h=i.clip(o,0,r-1)/r-.5,c=.5-i.clip(s,0,r-1)/r;return u=90-360*Math.atan(Math.exp(-c*2*Math.PI))/Math.PI,f=360*h,{latitude:u,longitude:f}},_mapResize:function(n){n.preventDefault();n.stopPropagation();var i={width:this._width,height:this._height},r=this._translatePoint,t=this;this.resizeTO&&clearTimeout(this.resizeTO);this.resizeTO=setTimeout(function(){if(t.model!=null){var n=t.pointToLatLong(t._width/2,t._height/2);t.refresh(!0);t._isTileMap||t.navigateTo(n.latitude,n.longitude,t._zoomLevel(),!1)}},500)},_isDevice:function(){return/mobile|tablet|android|kindle/i.test(navigator.userAgent.toLowerCase())},_unWireEvents:function(){var f=t.browserInfo(),r=f.isMSPointerEnabled,u=f.pointerEnabled,e=r?u?"pointerup":"MSPointerUp":"touchend mouseup",s=r?u?"pointerdown":"MSPointerDown":"touchstart mousedown",h=r?u?"pointermove":"MSPointerMove":"touchmove mousemove",c=jQuery.uaMatch(navigator.userAgent),i=c.browser.toLowerCase(),o;this._off(n(this._mapContainer),"touchstart",this._mapDown);this._off(n(document),"keydown",this._keyboardKeysPressed);this._off(n(document),t.eventType.mouseUp,this._mouseUp);o=!!navigator.userAgent.match(/Trident\/7\./);o&&(i="msie");i!="mozilla"&&this._off(n(this._mapContainer),"mousewheel",this._mouseWheel);window.navigator.msPointerEnabled?(this._off(n(this._mapContainer),"MSPointerDown",this._mouseButtonDown),this._off(n(this._mapContainer),"MSPointerMove",this._mouseMove),this._off(n(this._mapContainer),"MSPointerUp",this._mouseButtonUp)):i=="webkit"||i=="chrome"||i=="mozilla"?(this._off(n(this._mapContainer),"mousedown",this._mouseButtonDown),this._off(n(this._mapContainer),"mousemove",this._mouseMove),this._off(n(this._mapContainer),"mouseup",this._mouseButtonUp)):(this._off(n(this._mapContainer),"mousedown",this._mouseButtonDown),this._off(n(this._mapContainer),"mousemove",this._mouseMove),this._off(n(this._mapContainer),"mouseup",this._mouseButtonUp));n(this._mapContainer).get(0).removeEventListener(t.isTouchDevice()?"touchend":"mouseup",this._mapClick,!0);this._off(n(this._mapContainer),"dblclick",this._doubleClick);this._off(n(this._mapContainer),e,this._mapMouseClick);this._off(n(this._mapContainer),s,this._mapMouseDown);this._off(n(this._mapContainer),h,this._mapMouseMove);this._off(n(this._mapContainer),e,this._mapMouseUp);this._off(n(this._mapContainer),t.eventType.mouseMove,this._mapMouseEnter)},navigateTo:function(n,t,r,u){var e,f,o,s;u==i&&(u=this.enableAnimation());r=parseFloat(r);r==i&&(r=this._zoomLevel());r>this._minZoom()&&r<this._maxZoom()?(this.model.zoomSettings.level=r,this._zoomLevel(r)):(this.model.zoomSettings.level=r>(this._maxZoom()-this._minZoom())/2+this._minZoom()?this._maxZoom():this._minZoom(),this._zoomLevel(this.model.zoomSettings.level));e=this._getFactor();f=this._convertTileLatLongtoPointForShapes(n,t,this._mapBounds,e);this._prevPoint={x:this._translatePoint.x,y:this._translatePoint.y};this._prevScale=this._scale;this._scale=this._baseScale+(r-1)*this._zoomFactor();o=(this._containerWidth+this._baseTranslatePoint.x)/2/this._scale;s=(this._containerHeight+this._baseTranslatePoint.y)/2/this._scale;this._translatePoint.x=-f.x+o;this._translatePoint.y=-f.y+s;u&&!this._isTileMap?this._animate(2*this.model.zoomSettings.animationDuration):this._applyTransform(this._scale,this._translatePoint);this._updateSliderValue();this._refrshLayers()},selectShape:function(n,i,r){var e,f,u;if(n!=null)for(i==null&&(i=this.model.layers[this.baseMapIndex()]),u=0;u<i._mapShapes.length;u++)if(e=i._mapShapes[u].data,f=i._mapShapes[u].shape,e!=null&&n==this._reflection(e,i.shapeSettings.valuePath)){if(i._prevSelectedShapes.length!=0&&i.selectionMode!=t.datavisualization.Map.SelectionMode.Multiple)for(u=0;u<i._prevSelectedShapes.length;u++)this._isSVG?(i._prevSelectedShapes[u].setAttribute("fill",i._prevSelectedShapes[u].getAttribute("data-nodeValue")),i._prevSelectedShapes[u].setAttribute("stroke",i.shapeSettings.stroke),i._prevSelectedShapes[u].setAttribute("stroke-width",i.shapeSettings.strokeThickness/this._scale)):(i._prevSelectedShapes[u].fillcolor=i._prevSelectedShapes[u].style.behavior,i._prevSelectedShapes[u].strokecolor=i.shapeSettings.stroke,i._prevSelectedShapes[u].strokeweight=i.shapeSettings.strokeThickness/this._scale);i.enableSelection&&(this._isSVG?(i.shapeSettings.selectionColor!="none"&&f.setAttribute("fill",i.shapeSettings.selectionColor),f.setAttribute("stroke",i.shapeSettings.selectionStroke),f.setAttribute("stroke-width",i.shapeSettings.selectionStrokeWidth/this._scale)):(i.shapeSettings.selectionColor!="none"&&(f.fillcolor=i.shapeSettings.selectionColor),f.strokecolor=i.shapeSettings.selectionStroke,f.strokeweight=i.shapeSettings.selectionStrokeWidth/this._scale),this._updateSelection(i,f,n),i._contains(i.selectedItems,i._mapShapes[u])||i.selectedItems.push(i._mapShapes[u]),this._trigger("shapeSelected",{originalEvent:i.selectedItems}));r&&this.model.zoomSettings.enableZoomOnSelection&&this._zoomOnSelection(f.getBBox());u=i._mapShapes.length}},_getIntersectedElements:function(t,i){var r,u;if(t.width+=5,t.height+=5,this._isSVG&&this._browser!="mozilla"&&this._browser!="webkit")return r=this._svgDocument.createSVGRect(),r.x=t.left,r.y=t.top,r.width=t.width,r.height=t.height,this._svgDocument.getIntersectionList(r,null);var o=[],s=n(this._mapContainer).offset(),h=s.left,c=s.top;for(u=0;u<i.length;u++){var f=i[u].shape,e=f.getBoundingClientRect(),l=n(f).offset().left-h,a=n(f).offset().top-c;if(this._isIntersect(t,{left:l,top:a,height:e.bottom-e.top,width:e.right-e.left}))return o.push(f),o}return o},_isIntersect:function(n,t){return n.left>=t.left+t.width||n.left+n.width<=t.left||n.top>=t.top+t.height||n.top+n.height<=t.top?!1:!0},pan:function(n){var t=this,i=0,r=0,u,f,e,o;if(this._zoomLevel()!=1){switch(n){case"right":i=this._width/7;r=0;break;case"top":i=0;r=-(this._height/7);break;case"left":i=-(this._width/7);r=0;break;case"bottom":i=0;r=this._height/7}t.enablePan()&&(this._isTileMap&&(u=this._tileTranslatePoint.x-i/t._scale,f=this._tileTranslatePoint.y-r/t._scale,this._tileTranslatePoint.x=u,this._tileTranslatePoint.y=f,this._generateTiles(this._zoomLevel())),e=t._translatePoint.x-i/t._scale,t._prevScale=t._scale,o=t._translatePoint.y-r/t._scale,t._prevPoint={x:this._translatePoint.x,y:this._translatePoint.y},this._translatePoint.x=e,this._translatePoint.y=o,t.enableAnimation()&&!this._isTileMap?t._animate(t.model.zoomSettings.animationDuration):t._applyTransform(t._scale,t._translatePoint),t._refrshLayers())}},zoom:function(n,t){var r=this,u=r._zoomLevel(),f;n<=this._maxZoom()&&n>=this._minZoom()?(this._isTileMap?(this._tileZoom(r._zoomLevel(),n,{x:this._width/2,y:this._height/2}),u=r._zoomLevel(),r.model.zoomSettings.level=n,r._zoomLevel(n),this._generateTiles(this._zoomLevel()),u=u<n?u:n-1,r._translatePoint.x=(r._tileTranslatePoint.x-.5*Math.pow(2,u))/Math.pow(2,u),r._translatePoint.y=(r._tileTranslatePoint.y-.5*Math.pow(2,u))/Math.pow(2,u),r._scale=Math.pow(2,u)):(f=r._baseScale+(n-1)*r._zoomFactor(),r._prevPoint={x:r._translatePoint.x,y:r._translatePoint.y},r._prevScale=r._scale,r._translatePoint.x-=(r._width/r._scale-r._width/f)/2,r._translatePoint.y-=(r._height/r._scale-r._height/f)/2,r._scale=f,r.model.zoomSettings.level=n,r._zoomLevel(n),(r.enableAnimation()&&t||t==i)&&r._animate(r.model.zoomSettings.animationDuration)),r.enableAnimation()&&t&&!r._isTileMap||(r._applyTransform(r._scale,r._translatePoint),r._refrshLayers(),r._resizeShape()),r._updateSliderValue()):n<=this._minZoom()?(this.model.zoomSettings.level=this._minZoom(),this._zoomLevel(this._minZoom()),this.zoom(this._zoomLevel())):n>=this._maxZoom()&&(this.model.zoomSettings.level=this._maxZoom(),this._zoomLevel(this._maxZoom()),this.zoom(this._zoomLevel()));u<n?r._trigger("zoomedIn",{originalEvent:null,zoomLevel:r._zoomLevel()}):u>n&&r._trigger("zoomedOut",{originalEvent:null,zoomLevel:r._zoomLevel()})},refresh:function(t){var u,i,r;this._prevEnterElement="";this._trigger("onLoad");this._id!=""&&n("#"+this._id).find(".e-legendDiv").parent().children().empty();n(this._svgDocument).children().remove();n(this._svgDocument).empty();n(this._mapContainer).empty();this.baseMapIndex()>=this.model.layers.length&&this.baseMapIndex(0);this.model.layers[this.baseMapIndex()]._mapItems&&(this.model.layers[this.baseMapIndex()]._mapItems=null);this._svgDocument!=null&&(this._svgDocument=null);this._scale||(this._scale=1);this._margintop=0;this._marginleft=0;(!this._translatePoint||this._scale&&this._scale<=1)&&(this._translatePoint={x:0,y:0});this._height=this._mapContainer.height();this._width=this._mapContainer.width();this._height==0&&(this._height=this._mapContainer[0].parentElement.clientHeight!=0?this._mapContainer[0].parentElement.clientHeight:n(document).height());this._width==0&&(this._width=this._mapContainer[0].parentElement.clientWidth!=0?this._mapContainer[0].parentElement.clientWidth:n(document).width());i=this.model.layers[this.baseMapIndex()];i.legendSettings!=null&&i.legendSettings.showLegend&&(i.shapeSettings.colorMappings!=null||i.shapeSettings.colorPath!=null||i.bubbleSettings.colorPath!=null)&&i._sizeCalculation(this);u=this._scale;this._groupSize=null;this._generatePath();this._groupSize!=null&&(this._isMapCoordinates?(this._containerHeight=this._groupSize.maxY-this._groupSize.minY,this._containerWidth=this._groupSize.maxX-this._groupSize.minX,this._groupBorder={x:this._groupSize.minX,y:this._groupSize.minY}):(r=this._getFactor(),this._containerHeight=Math.abs((this._groupSize.maxY-this._groupSize.minY)*r),this._containerWidth=Math.abs((this._groupSize.maxX-this._groupSize.minX)*r)));i.layerType=="geometry"&&n(this._svgDocument).css("background",this.model.background);this._resizingContainer();this._renderMapElements();u!=this._scale&&this._resetThickness();i.legendSettings!=null&&i.legendSettings.showLegend&&(i.shapeSettings.colorMappings!=null||i.shapeSettings.colorPath!=null||i.bubbleSettings.colorPath!=null)&&this._renderLegend();this._isSVG&&n(this._svgDocument).css({height:this._height,width:this._width,"margin-top":this._margintop,"margin-left":this._marginleft});this.model.enableLayerChangeAnimation&&n(this._mapContainer).animate({opacity:1},500);this.model.centerPosition==null||this._isTileMap||this.navigateTo(this.model.centerPosition[0],this.model.centerPosition[1],this._zoomLevel(),!1);this._legendContainer!=null&&this._legendContainer.css({width:this._legendSize.width,height:this._legendSize.height});this._trigger("refreshed");t||(this._events=null,n(this._mapContainer).removeData(),n(this._mapContainer).data("ejMap",this))},_resetThickness:function(){for(var f=this._scale,t=0,i=0,r=0,u=this.model.layers,n;t<u.length;t++)for(n=u[t],i=0;n._mapShapes&&i<n._mapShapes.length;i++)for(this._resetLayerThickness(n);r<n.subLayers.length;r++)this._resetLayerThickness(n.subLayers[r])},_resetLayerThickness:function(t){for(var i=0,r=t._mapShapes?t._mapShapes.length:0,u=this._scale;i<r;i++)n(t._mapShapes[i]).attr("stroke-width",t.shapeSettings.strokeThickness/u)},_updateSliderValue:function(n){var t=this._sliderControl,r;n==i&&(n=this.enableAnimation());t!=null&&(r=t.data("ejSlider"),r.option("value",this._zoomLevel()))},_createDivElement:function(n,i,r,u){var f=n,e=i;e.appendTo(f);f.appendTo(r);this._on(e,t.eventType.mouseDown,{className:u,map:this},this._navigationControlPressed)},_navigationControlPressed:function(n){n.stopPropagation();this._isNavigationPressed=!0;var t=n.data.map,i=t.enableAnimation();n.data.className=="zoomIn"?(t.zoom(t._zoomLevel()+1,!0),i||t._updateSliderValue(),t._refrshLayers()):n.data.className=="zoomOut"?(t.zoom(t._zoomLevel()-1,!0),i||t._updateSliderValue()):n.data.className=="panLeft"?t.pan("left"):n.data.className=="panRight"?t.pan("right"):n.data.className=="panTop"?t.pan("top"):n.data.className=="panBottom"?t.pan("bottom"):n.data.className=="home"&&(t.zoom(t._isTileMap&&t._minZoom()>1?t._minZoom():1,!0),i||t._updateSliderValue())},_keyboardKeysPressed:function(n){this._iskeyboardKeysPressed=!0;var t=n.data.map,i=t.enableAnimation();n.ctrlKey&&n.keyCode==38?t._zoomingIn(t._width/2,t._height/2,n,i):n.ctrlKey&&n.keyCode==40?t._zoomingOut(t._width/2,t._height/2,n,i):n.keyCode==37?t.pan("left"):n.keyCode==39?t.pan("right"):n.keyCode==38?t.pan("top"):n.keyCode==40&&t.pan("bottom")},refreshNavigationControl:function(r){function it(n){o!=null&&o._isRendered&&o._zoomLevel()!=n.value&&o.zoom(n.value,!1)}function rt(n){o!=null&&o._isRendered&&o._zoomLevel()!=n.value&&o.zoom(n.value,!1)}var f=this.model.layers[this.baseMapIndex()],g=this._mapContainer.find("#ejNavigation"),c,e,a,s,v,h,y,o,w,b,k,d,u;if(g.length>0&&this._mapContainer[0].removeChild(g[0]),this.model.navigationControl!=null&&this.model.navigationControl.enableNavigation){r==i&&(r=this.model.navigationControl);var p=t.datavisualization.Map.LabelOrientation.Vertical,l,nt=120,tt=12;l={width:90,height:320};e=n("<div id='ejNavigation' class='e-map-navigation e-orientation-vert'/>");(this.model.navigationControl.content==null||this.model.navigationControl.content=="")&&(a=n("<div style='height:120px;width:10px;margin-top:-197px;margin-left: 34px;' />"));r.orientation=="horizontal"&&(p=t.datavisualization.Map.LabelOrientation.Horizontal,nt=12,tt=120,l={width:320,height:90},e=n("<div id='ejNavigation' class='e-map-navigation e-orientation-horz' />"),(this.model.navigationControl.content==null||this.model.navigationControl.content=="")&&(a=n("<div style='height:10px;width:120px;margin-top:-18px' />")));(this.model.navigationControl.content==null||this.model.navigationControl.content=="")&&(s=n("<div class='e-panContainer'/>"),v=p=="horizontal"?"Horz":"Vert",r.orientation=="horizontal"?(h=n("<div style='margin-left: 94px;' />"),y=n("<div style='margin-left: 137px;'/>")):(h=n("<div />"),y=n("<div style='margin-top: 34px;'/>")),s.appendTo(e),h.appendTo(e),a.appendTo(y),y.appendTo(e),this._sliderControl=a,u={x:0,y:0},r.dockPosition==null||r.dockPosition==t.datavisualization.Map.Position.None?(u.x=this._width*r.absolutePosition.x/100,u.y=this._height*r.absolutePosition.y/100):u=this._getPosition(r.dockPosition,l),e.css({"margin-left":u.x+"px","margin-top":u.y+"px"}));o=this;w=this._height*.0025;this.model.navigationControl.content==null||this.model.navigationControl.content==""?c=this._browser!="chrome"&&this._browser!="msie"?n("<style> .e-map-navigation {width: 90px;height: 320px;position:absolute;z-index:2;zoom:"+w+";}<\/style>"):n("<style> .e-map-navigation {width: 90px;height: 320px;position:absolute;z-index:2;-moz-transform: scale("+w+");}<\/style>"):(b=n("#"+this.model.navigationControl.content).height()||n(this.navigationControlData[0]).height(),k=n("#"+this.model.navigationControl.content).width()||n(this.navigationControlData[0]).width(),c=this._browser!="mozilla"&&this._browser!="chrome"?n("<style> .e-map-navigation {width: "+k+"px;height: "+b+"px;position:absolute;z-index:2;}<\/style>"):n("<style> .e-map-navigation {width: "+k+"px;height: "+b+"px;position:absolute;z-index:2;}<\/style>"));this._browser!="mozilla"&&this._browser!="chrome"?(c.remove(),n("html > head").append(c)):n("html > head").append(c);this.model.navigationControl.content==null||this.model.navigationControl.content==""?(n(a).ejSlider({orientation:p,sliderType:t.SliderType.MinRange,value:1,animationSpeed:1200,minValue:this._minZoom(),showTooltip:!0,enableAnimation:!1,maxValue:this._maxZoom(),incrementStep:this._zoomFactor(),slide:rt,change:it,height:nt,width:tt}),e.appendTo(this._mapContainer),r.orientation=="horizontal"?(this._createDivElement(n("<div title='"+this._localizedLabels.zoomIn+"' class='e-icon1 e-incrementButton  icon_margin1' />"),n("<div class='e-icon1 nav-inc-"+v+"  e-innerIncrement'/>"),h,"zoomIn"),this._createDivElement(n("<div title='"+this._localizedLabels.zoomOut+"' class='e-icon1 e-incrementButton icon_margin2'/>"),n("<div class='e-icon1 nav-dec-"+v+" e-innerDecrement'/>"),h,"zoomOut")):(this._createDivElement(n("<div title='"+this._localizedLabels.zoomIn+"' class='e-icon1 e-incrementButton  icon_margin1' />"),n("<div class='e-icon1 nav-inc-"+v+"  e-innerIncrement'/>"),h,"zoomIn"),this._createDivElement(n("<div title='"+this._localizedLabels.zoomOut+"' class='e-icon1 e-incrementButton icon_margin2'/>"),n("<div class='e-icon1 nav-dec-"+v+" e-innerDecrement'/>"),h,"zoomOut")),this._createDivElement(n("<div title='"+this._localizedLabels.panTop+"' class='e-icon1 e-radialTop'/>"),n("<div class='e-icon1 e-arrowUp'/>"),s,"panTop"),this._createDivElement(n("<div title='"+this._localizedLabels.panLeft+"' class='e-icon1 e-radialLeft'/>"),n("<div class='e-icon1 e-arrowLeft'/>"),s,"panLeft"),this._createDivElement(n("<div title='"+this._localizedLabels.panRight+"' class='e-icon1 e-radialRight'/>"),n("<div class='e-icon1 e-arrowRight'/>"),s,"panRight"),this._createDivElement(n("<div title='"+this._localizedLabels.panBottom+"' class='e-icon1 e-radialBottom'/>"),n("<div class='e-icon1 e-arrowDown'/>"),s,"panBottom"),d=n("<div title='"+this._localizedLabels.home+"' class='e-icon1 e-home-bg'><div class='e-icon1 e-map-home'><\/div>"),d.appendTo(s),d.mousedown({className:"home",map:this},this._navigationControlPressed)):(this.navigationControlData==null?(this.navigationControlData=n("#"+this.model.navigationControl.content),this.navigationControlData.css({display:"block"}),this.navigationControlData.appendTo(e)):this.navigationControlData.appendTo(e),e.appendTo(this._mapContainer),l={width:this.navigationControlData[0].getBoundingClientRect().right-this.navigationControlData[0].getBoundingClientRect().left,height:this.navigationControlData[0].getBoundingClientRect().bottom-this.navigationControlData[0].getBoundingClientRect().top},u={x:0,y:0},r.dockPosition==null||r.dockPosition==t.datavisualization.Map.Position.None?(u.x=this._width*r.absolutePosition.x/100,u.y=this._height*r.absolutePosition.y/100):u=this._getPosition(r.dockPosition,l),f.legendSettings.dockOnMap&&f.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Top&&f.legendSettings.mode==t.datavisualization.Map.LegendMode.Interactive?e.css({"margin-left":u.x+"px","margin-top":u.y+f.legendSettings.height+"px"}):f.legendSettings.dockOnMap&&f.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Left&&f.legendSettings.mode==t.datavisualization.Map.LegendMode.Interactive?e.css({"margin-left":u.x+f.legendSettings.width+20+f.legendSettings.leftLabel.length*10+f.legendSettings.rightLabel.length*10+"px","margin-top":u.y+"px"}):f.legendSettings.dockOnMap&&f.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Top&&f.legendSettings.mode==t.datavisualization.Map.LegendMode.Default?e.css({"margin-left":u.x+"px","margin-top":u.y+f.legendSettings.height+"px"}):f.legendSettings.dockOnMap&&f.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Left&&f.legendSettings.mode==t.datavisualization.Map.LegendMode.Default?e.css({"margin-left":u.x+f.legendSettings.width+"px","margin-top":u.y+"px"}):e.css({"margin-left":u.x+"px","margin-top":u.y+"px"}))}},_getPosition:function(n,i){var r={x:0,y:0};switch(n){case t.datavisualization.Map.Position.TopCenter:r.x=this._width/2-i.width/2;break;case t.datavisualization.Map.Position.TopRight:r.x=this._width-i.width;break;case t.datavisualization.Map.Position.CenterLeft:r.y=this._height/2-i.height/2;break;case t.datavisualization.Map.Position.Center:r.x=this._width/2-i.width/2;r.y=this._height/2-i.height/2;break;case t.datavisualization.Map.Position.CenterRight:r.x=this._width-i.width;r.y=this._height/2-i.height/2;break;case t.datavisualization.Map.Position.BottomLeft:r.y=this._height-i.height;break;case t.datavisualization.Map.Position.BottomCenter:r.x=this._width/2-i.width/2;r.y=this._height-i.height;break;case t.datavisualization.Map.Position.BottomRight:r.x=this._width-i.width;r.y=this._height-i.height}return r},_renderMapElements:function(t){this._templateDiv=n("<div class='e-TemplateDiv'/>");this._templateDiv.appendTo(this._mapContainer);this._templateDiv.css({"pointer-events":"none",overflow:"hidden",position:"absolute","z-index":"1",height:this._height,width:this._width});this.refreshLayers(t);this.refreshNavigationControl(this.model.navigationControl)},_renderLegend:function(){var r=this.model.layers[this.baseMapIndex()],f,e,o,s,u;this._scrollLegendDiv=n("<div class='e-ScrollLegendDiv'/>");this._scrollLegendDiv.appendTo(this._mapContainer);this._legendContainer=n("<div id='e-LegendcontainerDiv'/>");this._legendContainer.appendTo(this._scrollLegendDiv);this._legendDiv.css({height:this._legendDivHeight+"px",width:this._legendDivWidth+"px"});this._isNavigationPressed=!0;this._legendDiv.appendTo(this._legendContainer);this._legendDiv[0].getBoundingClientRect();this._legendContainer.css({position:"relative"});this._scrollLegendDiv.ejScroller({height:Math.round(this._legendSize.height),width:Math.round(this._legendSize.width)});r.legendSettings.labelOrientation==i&&r.legendSettings.labelOrientation==t.datavisualization.Map.LabelOrientation.Vertical;r.legendSettings.dockOnMap?(r.legendSettings.mode==t.datavisualization.Map.LegendMode.Interactive?(f="none",o=r.legendSettings.height+50,e=r.legendSettings.width+20+r.legendSettings.leftLabel.length*10+r.legendSettings.rightLabel.length*10):(f="auto",o=r.legendSettings.height,e=r.legendSettings.width),this._scrollLegendDiv.css({position:"absolute","z-index":"2",height:this._legendSize.height,width:this._legendSize.width}),(r.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Top||r.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Bottom)&&r.legendSettings.alignment==t.datavisualization.Map.Alignment.Center?this._scrollLegendDiv.css({"margin-left":this._width/2-this._legendSize.width/2}):(r.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Top||r.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Bottom)&&r.legendSettings.alignment==t.datavisualization.Map.Alignment.Far?this._scrollLegendDiv.css({"margin-left":this._width-this._legendSize.width}):(r.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Left||r.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Right)&&r.legendSettings.alignment==t.datavisualization.Map.Alignment.Center?this._scrollLegendDiv.css({"margin-top":this._height/2-this._legendSize.height/2}):(r.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Left||r.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Right)&&r.legendSettings.alignment==t.datavisualization.Map.Alignment.Far&&this._scrollLegendDiv.css({"margin-top":this._height-this._legendSize.height}),r.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Bottom?this._scrollLegendDiv.css({"margin-top":this._height}):r.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Right&&this._scrollLegendDiv.css({"margin-left":this._width})):(r.legendSettings.labelOrientation==t.datavisualization.Map.LabelOrientation.Horizontal||r.legendSettings.labelOrientation==t.datavisualization.Map.LabelOrientation.Vertical)&&(s=r.legendSettings.position==i?"topleft":r.legendSettings.position,u=this._getPosition(s,this._legendSize),this._scrollLegendDiv.css({position:"absolute","z-index":"2","margin-left":u.x+"px","margin-top":u.y}));this.refreshNavigationControl(this.model.navigationControl)},_renderMapLayers:function(){var f,i,o,e,u,s;for(n(this._templateDiv).empty(),f=0,i=this.model.layers[this.baseMapIndex()],i.layerType==t.datavisualization.Map.LayerType.Geometry?(this._renderLayers(i,f,this),i.shapeData!=null&&(f=i._polygonData.length)):(i._initializeLocalValues(),o=n("<div class='e-map-contribution'>"),o[0].innerHTML=i.contribution,this._mapContainer.append(o)),e=0;e<this.model.layers[this.baseMapIndex()].subLayers.length;e++)u=this.model.layers[this.baseMapIndex()].subLayers[e],u._isBaseLayer=!1,u.layerType==t.datavisualization.Map.LayerType.Geometry&&u.shapeData!=null?(t.util.isNullOrUndefined(u._initializeLocalValues)&&(s=new r,n.extend(s,u),n.extend(u,s)),this._renderLayers(u,f,this),f+=u._polygonData.length):u._initializeLocalValues();(i.markers.length>0||i.subLayers.length>0||i._labelCollection.length>0)&&this.refreshMarkers();this._isSVG&&this.enableAnimation()&&!this._isTileMap?this._refreshWithAnimation():this._refrshLayers()},refreshLayers:function(n){var t=this.model.layers[this.baseMapIndex()];this._processOData(t,this);n&&this.shapeSelectionOnResize()},_processOData:function(n,i){if(n.dataSource!=null)if(n.dataSource instanceof t.DataManager){var r=n.dataSource.executeQuery(n.query);r.done(function(t){t.result!=null&&(n.dataSource=t.result,i._renderMapLayers())})}else i._renderMapLayers();else i._renderMapLayers()}});var u=function(n,t){this.X=n;this.Y=t;this.left=0;this.top=0;this.height=256;this.width=256;this.src=null},r=function(){this.enableSelection=!0;this.selectionMode="default";this.bingMapType="aerial";this.key="";this.selectedItems=[];this.enableMouseHover=!1;this.shapeData=null;this.markers=[];this.dataSource=null;this.urlTemplate="http://a.tile.openstreetmap.org/level/tileX/tileY.png";this.showTooltip=!1;this.tooltipTemplate=null;this.mapItemsTemplate=null;this.enableAnimation=!1;this.legendSettings={showLegend:!1,showLabels:!1,rowSpacing:10,textPath:null,columnSpacing:10,position:"topleft",positionX:0,positionY:0,height:0,width:0,iconHeight:20,iconWidth:20,type:"layers",mode:"default",title:null,leftLabel:null,rightLabel:null,icon:"rectangle",dockOnMap:!1,dockPosition:"top",labelOrientation:"vertical",alignment:"bottom",columnCount:0,toggleVisibility:!1};this.labelSettings={showLabels:!1,labelPath:"",enableSmartLabel:!1,smartLabelSize:"fixed",labelLength:2,font:{fontFamily:"Segoe UI",fontStyle:"Normal",fontWeight:"Regular",color:null,size:"14px",opacity:1}};this.markerTemplate=null;this.showMapItems=!1;this.layerType="geometry";this.geometryType="geographic";this._colorPaletteSettings={Palette1:{highlightColor:"#F7CD1C",highlightStroke:"white",SelectionColor:"#F96C0D",SelectionStroke:"white"},Palette2:{highlightColor:"#68A3EA",highlightStroke:"White",SelectionColor:"#116EF4",SelectionStroke:"White"},Palette3:{highlightColor:"#CCCCCC",highlightStroke:"white",SelectionColor:"#4D4D4D",SelectionStroke:"white"}};this.colorPalettes={Palette1:["#4A3825","#736F3D","#F2DABD","#BF9D7E","#7F6039","#7F715F","#70845D","#CC995C","#736F3D","#89541B"],Palette2:["#E51400","#730202","#EF6535","#C63477","#BF004D","#F0A30B","#CE1B1B","#97993D","#D6BF38","#835A2C"],Palette3:["#A4C400","#008B00","#1BA0E2","#0050EF","#AA00FF","#D90073","#E51400","#F96800","#E3C800","#A20026"]};this._prevPaletteIndex=0;this._newBounds=[];this.subLayers=[];this.shapeSettings={highlightColor:"gray",highlightBorderWidth:1,selectionColor:"gray",fill:"#E5E5E5",radius:5,strokeThickness:"0.2",stroke:"#C1C1C1",colorPalette:"palette1",highlightStroke:"#C1C1C1",selectionStroke:"#C1C1C1",selectionStrokeWidth:1,colorPath:null,colorValuePath:null,valuePath:null,enableGradient:!1,colorMappings:null,autoFill:!1};this.bubbleSettings={showBubble:!0,bubbleOpacity:"0.9",minValue:10,maxValue:20,color:"gray",colorValuePath:null,valuePath:null,colorMappings:null,showTooltip:!1,tooltipTemplate:null,colorPath:null}};r.prototype={dataTypes:{dataSource:"data",markers:"array",subLayers:"array",shapeSettings:{colorMappings:"array"},bubbleSettings:{colorMappings:"array"}},_initializeLocalValues:function(){this._svgns="http://www.w3.org/2000/svg";this._bounds=[];this._bubbleCollection=[];this._prevSelectedShapes=[];this._prevSelectedTemp=[];this._prevSelectedLegends=[];this._isBaseLayer=!0;this._prevHoverdLegend=null;this._prevHoveredShape=null;this._labelCollection=[];this._scrollBar=null;this._mapShapes=[];this._bubbles=[];this._labelBounds=[];this._bubbleCount=0;this._mapItems=[];this._mapMarkers=[];this.selectedItems;this._tooltipSize={height:0,width:0};this._smartLabels=[];this._labelData=[];this._interactiveArrow=null;this._legendRects=[]},_generateMarkerForLayers:function(n){this._mapMarkers=[];var t=n._rootgroup.getBoundingClientRect().top,i=n._rootgroup.getBoundingClientRect().left;n._generateMarkers(this.markers,this,n)},_contains:function(n,t){var i=n.length;if(i>0)while(i--)if(n[i]===t)return!0;return!1},_shapeContains:function(n,t){var i=n.length;if(i>0)while(i--)if(n[i].shapeIndex===t)return{isContains:!0,index:n.length-i};return{isContains:!1}},_labelSizeCalculation:function(n){var t=document.getElementsByClassName("e-map-labelContainer"),r=t[0].offsetHeight,i=t[0].offsetWidth;n._width=n._width-i;n._marginleft=i},_sizeCalculation:function(n){var r=this.shapeSettings.colorMappings,v=!1,y=!1,u;if(r!=null||this.shapeSettings.colorPath!=null||this.bubbleSettings.colorPath!=null){r!=null?r.rangeColorMapping!=null?(r=r.rangeColorMapping,v=!0):r.equalColorMapping!=null&&(r=r.equalColorMapping):(this.shapeSettings.colorPath!=null||this.bubbleSettings.colorPath!=null)&&(r=this.dataSource,y=!0);var o=0,c=0,h=this.legendSettings.rowSpacing,p=this.legendSettings.columnSpacing,w=this.legendSettings.iconWidth+5,a=this.legendSettings.iconHeight+5,d=a+h,f=this.legendSettings.iconHeight+h,e=0,i=this.legendSettings,l=this.legendSettings.columnCount;if(i.height==0&&i.width==0&&l==0){for(u=0;u<r.length;u++){var b=r[u].legendLabel!=null?r[u].legendLabel:v?r[u].from:y?r[u][i.textPath]:r[u].value,k=this._calcWidth(b),s=w+k+p;i.dockOnMap&&(i.dockPosition==t.datavisualization.Map.DockPosition.Top||i.dockPosition==t.datavisualization.Map.DockPosition.Bottom)||!i.dockOnMap?n._width<o+s?(f=a+h+f,e=Math.max(e,o),o=s):o+=s+5:n._height<c+a+h?(e+=o,f=Math.max(f,c),o=s,c=0):(c+=a+h,o=Math.max(s,o))}i.dockOnMap&&(i.dockPosition==t.datavisualization.Map.DockPosition.Top||i.dockPosition==t.datavisualization.Map.DockPosition.Bottom)||!i.dockOnMap?(e=Math.max(e,o),f+=5):(e+=o+p,f=Math.max(f,c))}else l==0&&(f=i.height.toString().indexOf("%")!=-1?n._height/100*parseInt(i.height.toString().replace("%","")):i.height,e=i.width.toString().indexOf("%")!=-1?n._width/100*parseInt(i.width.replace("%","")):i.width);if(i.height==0&&i.width==0&&l!=0)for(u=0;u<r.length;u++){var b=r[u].legendLabel!=null?r[u].legendLabel:v?r[u].from:y?r[u][i.textPath]:r[u].value,k=this._calcWidth(b),s=w+k+p;u%l!=0?(o+=s,u==l-1&&(e=Math.max(e,o))):(u!=0&&(f=w+h+f),e=Math.max(e,o),o=s)}}i.height!=0&&i.width!=0&&(f=i.height,e=i.width);n._legendSize={height:f,width:e};i.dockOnMap&&(i.mode==t.datavisualization.Map.LegendMode.Interactive&&(f=55),this.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Bottom?(n._height=n._height-parseFloat(f),this.legendSettings.tempWidth=o):this.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Top?(n._height=n._height-parseFloat(f),n._margintop=parseFloat(f)):this.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Left?(n._width=n._width-e,n._marginleft=e):this.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Right&&(n._height=n._height,n._width=n._width-e))},_clearShapeWidth:function(n,t){var r,i;for(n==null&&(n=1),r=0;r<this._mapShapes.length;r++)i=this._mapShapes[r],this._contains(this._prevSelectedShapes,i.shape)||(t._isSVG?(this.shapeSettings.colorMappings!=null||this.shapeSettings.colorPath||this.shapeSettings.autoFill?i.shape.setAttribute("fill",i.shape.getAttribute("data-nodeValue")):(i.shape.setAttribute("data-nodeValue",this.shapeSettings.fill),i.shape.setAttribute("fill",this.shapeSettings.fill)),i.shape.setAttribute("stroke-width",this.shapeSettings.strokeThickness/n),i.shape.setAttribute("stroke",this.shapeSettings.stroke)):(i.shape.fillcolor=i.shape.style.behavior,i.shape.strokeweight=this.shapeSettings.strokeThickness,i.shape.strokecolor=this.shapeSettings.stroke))},_shapeSelection:function(){for(var n,i,t=0;t<this._mapShapes.length;t++)n=this._mapShapes[t],i=this._shapeContains(this.selectedItems,n.shapeIndex),i.isContains&&(this._contains(this._prevSelectedShapes,n.shape)||this._prevSelectedShapes.push(n.shape),this._isSVG?(n.shape.setAttribute("fill",this.shapeSettings.selectionColor),n.shape.setAttribute("stroke-width",this.shapeSettings.selectionStrokeWidth),n.shape.setAttribute("stroke",this.shapeSettings.selectionStroke)):(n.shape.fillcolor=this.shapeSettings.selectionColor,n.shape.strokeweight=this.shapeSettings.selectionStrokeWidth,n.shape.strokecolor=this.shapeSettings.selectionStroke))},_createDefs:function(){return document.createElementNS(this._svgns,"defs")},_createGradientElement:function(n,t,i,r,u,f,e,o){if(Object.prototype.toString.call(t)=="[object Array]"){var h={id:n.id+"Gradient_"+o,x1:i+"%",y1:r+"%",x2:u+"%",y2:f+"%"},s="#"+n.id+"Gradient_"+o;return this._drawGradient(h,t,e,s),"url("+s+")"}},_drawGradient:function(t,i,r,u){var c=u.substring(1,u.length/2)=="rootGroup"?r.shapeSettings.colorMappings.rangeColorMapping.length:r.bubbleSettings.colorMappings.rangeColorMapping.length,o,e,f,h,s;for(n(svgDocument).find("defs").length>c&&n(svgDocument).find("defs").remove(),o=this._createDefs(),e=document.createElementNS(this._svgns,"linearGradient"),n(e).attr(t),f=0;f<i.length;f++)h=100/(i.length-1),s=document.createElementNS(this._svgns,"stop"),n(s).attr({offset:f*h+"%","stop-color":i[f],"stop-opacity":1}),n(s).appendTo(e);n(u).length==0&&(n(e).appendTo(o),n(o).appendTo(svgDocument))},_calculateTextWidth:function(t){var i=n("<span>"+t+"<\/span>"),r;return n("body").append(i),r=i.width(),i.remove(),r},_trimFunction:function(t,i){for(var u=n("#spantag").text(t),r=t;u.width()>i;)r=r.slice(0,-2),u.text(r+"...");return r},_createLabel:function(i,r,u,f,e,o,s){var h;return h=this.legendSettings.mode=="interactive"?n("<div class="+f+"><\/div>"):n("<div class=' "+f+" ' id='"+o._id+"LegendText_"+e+"'><\/div>"),h[0].innerHTML=i,h.css({"margin-left":r+"px","margin-top":u+"px",position:"absolute",cursor:!t.util.isNullOrUndefined(s)&&s.legendSettings.toggleVisibility?"pointer":"default"}),h},_createInteractiveArrow:function(t,i){var r=n("<div class='e-icon1 e-interactiveArrow'><\/div>");return r[0].innerHTML="&#9650",r.css({"margin-left":t+"px","margin-top":i+"px",position:"absolute",visibility:"hidden"}),r},_getEllipseLegend:function(t,i,r,u,f){var e=n("<div class='e-mapLegend' id='"+f._id+"Legend_"+u+"'/>");return e.css({height:r.legendSettings.iconHeight+"px",width:r.legendSettings.iconWidth+"px","border-radius":r.legendSettings.iconHeight/2+"px",left:t+"px",top:i+"px",position:"absolute",cursor:r.legendSettings.toggleVisibility?"pointer":"default"}),e},_getRectLegend:function(t,i,r,u,f){var e=n("<div id='"+f._id+"Legend_"+u+"'/>");return e.css({height:r.legendSettings.iconHeight+"px",width:r.legendSettings.iconWidth+"px",left:t+"px",top:i+"px",position:"absolute",cursor:r.legendSettings.toggleVisibility?"pointer":"default"}),e},_generateLegend:function(r){var h=!0,y=!1,o=null,u,d,g;this.shapeSettings.colorMappings!=null?this.shapeSettings.colorMappings.rangeColorMapping!=i?o=this.shapeSettings.colorMappings.rangeColorMapping:this.shapeSettings.colorMappings.equalColorMapping!=i&&(o=this.shapeSettings.colorMappings.equalColorMapping,h=!1):this.shapeSettings.colorPath!=null&&(y=!0,h=!1,o=this.dataSource);var e=0,s=0,l=this.legendSettings,v=0,p,w,f,a=l.rowSpacing,b=l.columnSpacing,it=l.iconWidth+5,rt=l.iconHeight+a,k=l.columnCount;for(u=0;u<o.length;u++){d=n.extend(!0,null,this.legendSettings);g=o[u].legendLabel=o[u].legendLabel!=null?o[u].legendLabel:h?o[u].from:y?o[u][l.textPath]:o[u].value;f={legendSettings:d,legendLabel:o[u].legendLabel,fill:h?o[u].color:o[u][this.shapeSettings.colorPath],mapping:h?o[u]:i,dataSource:h?i:o[u]};r._trigger("legendItemRendering",{model:r.model,data:f});var nt=this._calcWidth(f.legendLabel),c=f.legendSettings.iconWidth+b+nt,tt=f.legendSettings.iconHeight;k!=0?u%k!=0?(this._drawLegend(f,e,s,r,h,u),e+=c+5):(e=0,this._drawLegend(f,e,s,r,h,u),e+=c+5,s+=f.legendSettings.iconHeight+a):f.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Top||f.legendSettings.dockPosition==t.datavisualization.Map.DockPosition.Bottom?r._legendSize.width<e+c?(e=0,this._drawLegend(f,e,s,r,h,u),e+=c,s+=f.legendSettings.iconHeight+a):(this._drawLegend(f,e,s,r,h,u),e+=c):r._legendSize.height<s+f.legendSettings.iconHeight?(s=0,e+=v+b,this._drawLegend(f,e,s,r,h,u),v=0,s+=f.legendSettings.iconHeight+a):(this._drawLegend(f,e,s,r,h,u),v=Math.max(v,c),s+=f.legendSettings.iconHeight+a);p=r._legendDivHeight>s?r._legendDivHeight:s+tt;w=r._legendDivWidth>e+c?r._legendDivWidth:e+c;r._legendDivWidth=w;r._legendDivHeight=p}},_drawLegendShape:function(r,u,f,e,o,s){var h;return h=r.legendSettings.icon==t.datavisualization.Map.LegendIcons.Circle?this._getEllipseLegend(u,f,r,s,e):this._getRectLegend(u,f,r,s,e),u+=r.legendSettings.iconWidth+5,n(h).css("background-color",o?r.mapping._color?r.mapping._color:r.fill:r.dataSource._color!=i?r.dataSource._color:r.fill!=i?r.fill:r.dataSource.color),h.appendTo(e._legendDiv),u},_drawLegendText:function(t,i,r,u,f,e){var o=n("<div class='e-defaultLegendLabel' id='"+u._id+"LegendText_"+e+"'/>");return o.css({left:i+"px",top:r+"px",position:"absolute","text-overflow":"ellipsis","white-space":"nowrap",overflow:"hidden",cursor:t.legendSettings.toggleVisibility?"pointer":"default"}),o[0].title=t.legendLabel,o[0].innerHTML=t.legendLabel,o.appendTo(u._legendDiv),i+this._calcWidth(t.legendLabel)},_drawLegend:function(n,t,i,r,u,f){r.model.enableRTL?(t=this._drawLegendText(n,t,i,r,u,f),this._drawLegendShape(n,t,i,r,u,f)):(t=this._drawLegendShape(n,t,i,r,u,f),this._drawLegendText(n,t,i,r,u,f))},_generateLegends:function(r){var w=this.shapeSettings.colorMappings,b,ot,pt,tt,a,c,p,e,k,it,bt,ct,kt,lt,rt,s,ft,o,gt,ni;if(this.shapeSettings.colorPath!=null||w.rangeColorMapping!=null||w.equalColorMapping!=null){var fi=this.legendSettings,ei=this.legendSettings.iconWidth+5,ii=this.legendSettings.iconHeight+5,oi=this.legendSettings.rowSpacing,si=this.legendSettings.columnSpacing,hi=this.legendSettings.iconHeight+this.legendSettings.rowSpacing,ci=ii+this.legendSettings.rowSpacing,et,d,g,nt,vt=r.model.enableRTL?this.legendSettings.rightLabel:this.legendSettings.leftLabel,yt=r.model.enableRTL?this.legendSettings.leftLabel:this.legendSettings.rightLabel;if((this.legendSettings.showLegend==i||this.legendSettings.showLegend)&&!this.shapeSettings.autoFill){n(r._mapContainer).append(n('<div  id="labelTooltip" style="display:none;background-color:grey;padding-left:5px; padding-right:5px;position:absolute;z-index:1000;pointer-events:none;"/>'));var v=this,f=10,h=0,ci=this.iconHeight+this.legendSettings.rowSpacing,y=this.legendSettings.height,l=this.legendSettings.width,u=n("<div/>");if(v.legendSettings.dockOnMap?u=n(r._legendDiv):v.legendSettings.dockOnMap||v.legendSettings.mode==t.datavisualization.Map.LegendMode.Interactive?r._isSVG?u.appendTo(r._templateDiv):r._templateDiv.append(u):u=n(r._legendDiv),(this.legendSettings.type==i||this.legendSettings.type==t.datavisualization.Map.LegendType.Layers)&&(this.shapeSettings.colorMappings!=null||this.shapeSettings.colorPath!=null)&&(this.legendSettings.mode==i||this.legendSettings.mode==t.datavisualization.Map.LegendMode.Default||this.legendSettings.mode==t.datavisualization.Map.LegendMode.Interactive&&this.shapeSettings.colorMappings.equalColorMapping!=null))this._generateLegend(r);else if((this.legendSettings.type==i||this.legendSettings.type==t.datavisualization.Map.LegendType.Layers)&&this.legendSettings.mode==t.datavisualization.Map.LegendMode.Interactive&&this.shapeSettings.colorMappings!=null){if(o="",this.legendSettings.height==0&&(y=18),this.legendSettings.width==0&&(l=150),this.legendSettings.leftLabel==null&&(this.legendSettings.leftLabel=""),this.legendSettings.rightLabel==null&&(this.legendSettings.rightLabel=""),this.legendSettings.title!=null){if(b=document.createElement("Label"),b.innerHTML=this.legendSettings.title,document.body.appendChild(b),ot=f,this.legendSettings.showLabels||(ot=r.model.enableRTL?f+(l-b.offsetWidth/2):f+this.legendSettings.leftLabel.length*10),document.body.removeChild(b),pt=this.legendSettings.title.length*10,et=d=this.legendSettings.title,pt>l)for(s=1;s<d.toString().length;s++)et=d.toString().substring(0,s-1)+"...";o=this._createLabel(et,ot,h,"e-interactivelegend-title");o[0].title=d;o.css({width:l+"px"});r._isSVG?o.appendTo(u):u.append(o);h+=25}for(this.legendSettings.showLabels&&(h+=25),vt==null||this.legendSettings.showLabels||(o=this._createLabel(vt,f,h-3,"e-interactivelegend-leftlabel"),r._isSVG?o.appendTo(u):u.append(o),f=f+this.legendSettings.leftLabel.length*10),tt=this._createInteractiveArrow(f,h+y),tt.appendTo(u),this._interactiveArrow=tt,a=null,!r._isSVG&&this.shapeSettings.enableGradient&&(a=r._createGroup(!1,"legendGroup"),a.style.left="0px",a.style.top="0px",a.style.position="relative",u.append(a)),c=r.model.enableRTL?w.rangeColorMapping.reverse():w.rangeColorMapping,c=w.rangeColorMapping,p=0;p<c.length;p++)if(e=c[p],!e.hideLegend){if(k=[],this.shapeSettings.enableGradient&&(k=e.gradientColors),it={},this.shapeSettings.enableGradient)if(r._isSVG){var st=n("<canvas/>"),ht=st[0].getContext("2d"),wt=ht.createLinearGradient(0,0,300,0);for(s=0;s<k.length;s++)bt=s/(k.length-1),wt.addColorStop(bt,k[s]);ht.fillStyle=wt;ht.fillRect(0,0,300,300);st.css({height:y+"px",width:l/c.length+"px","margin-left":f+"px","margin-top":h+"px",opacity:"0.9",filter:"alpha(opacity=90)",position:"absolute"});st.appendTo(u)}else ct="legend"+p,kt="<v:rect id="+ct+' display="block" style="position:absolute;top: '+(h-2)+"px;left:"+f+"px;width:"+l/c.length+"px;height:"+y+'px;"><v:fill opacity="0.9px" type="gradient" method="linear sigma" angle="270"/><v:stroke opacity="0px"/><\/v:rect>',a.innerHTML=a.innerHTML+kt,lt=document.getElementById(ct),lt.fillcolor=e.gradientColors[0],lt.fill.color2=e.gradientColors[1];else rt=n("<div/>"),rt.css({height:y+"px",width:l/this.shapeSettings.colorMappings.rangeColorMapping.length+"px","background-color":e.color,"margin-left":f+"px","margin-top":h+"px",opacity:"0.9",filter:"alpha(opacity=90)",position:"absolute"}),r._isSVG?rt.appendTo(u):u.append(rt);for(s=0;s<10;s++)it={},it.marginLeft=f,this._legendRects.push(it),f=f+l/c.length/10;if(r.model.enableRTL&&p==c.length-2&&tt.css({"margin-left":f+"px"}),this.legendSettings.showLabels){var ut=f-l/c.length,at=h-25,dt=this._createLabel(e.from,ut,at,"e-legend-rangestartlabel");ut=f;ft=this._createLabel(e.to,ut,at);e.legendLabel!=i&&(ft=this._createLabel(e.legendLabel,ut-e.legendLabel.length*5,at,"e-legend-rangeendlabel",e));r._isSVG?(e==c[0]&&dt.appendTo(u),ft.appendTo(u)):(e==c[0]&&u.append(dt),u.append(ft))}}yt==null||this.legendSettings.showLabels||(o=this._createLabel(yt,f+10,h-3,"e-interactivelegend-rightlabel"),r._isSVG?o.appendTo(u):u.append(o),f=f+this.legendSettings.rightLabel.length*10);g=f+10;nt=h+y+10;r._legendSize={width:g,height:nt};v.legendSettings.dockOnMap&&(this.legendSettings.dockPosition=="left"?r._marginleft=g:this.legendSettings.dockPosition=="top"&&(r._margintop=nt))}if(v.legendSettings.dockOnMap||this.legendSettings.position!="none"){if(!v.legendSettings.dockOnMap&&this.legendSettings.mode==t.datavisualization.Map.LegendMode.Interactive){var ri=this.legendSettings.position==i?"topleft":this.legendSettings.position,ui={width:g,height:nt},ti=r._getPosition(ri,ui);u.css({"margin-left":ti.x+"px","margin-top":ti.y})}}else gt=r._width*this.legendSettings.positionX/100,ni=r._height*this.legendSettings.positionY/100,u.css({"margin-left":gt+"px","margin-top":ni+"px"})}}},_generateLabels:function(i){var f=n("<div class='e-map-labelContainer'><\/div>"),r;for(n(f).css({position:"absolute",overflow:"scroll"}),r=0;r<this._polygonData.length;r++){var e=this._polygonData[r].properties,o=i._reflection(e,this.labelSettings.labelPath),u=n("<div class='e-map-label'><\/div>");n(u).css({"margin-top":r*20,position:"absolute"});u[0].innerHTML=o;f.append(u);i._mapContainer.append(f);u.mouseenter({Param1:this,Param2:this._mapShapes[r],map:i},i._polyEnterFunction);i._off(n(u),t.eventType.mouseUp,i._polyClickFunction);i._on(n(u),t.eventType.mouseUp,{Param1:this._mapShapes[r],Param2:this,Param3:this._mapShapes[r].shape,param4:u},i._polyClickFunction)}},_calcWidth:function(t){var i=n('<span  class="e-defaultLegendLabel">'+t+"<\/span>"),r;return n("body").append(i),r=i.width()+5,i.remove(),r},_generateBubbleLegends:function(r){var f,k,u,c,o,p,w,d,g;if(this.legendSettings.showLegend==i||this.legendSettings.showLegend){var s=10,h=10,it=this.legendSettings.iconHeight,rt=this.legendSettings.iconWidth,l=n("<div/>");r._isSVG?l.appendTo(r._templateDiv):r._templateDiv.append(l);var a=0,y=0,v=!0,e=null,b=!1;if(this.bubbleSettings.colorMappings!=null?this.bubbleSettings.colorMappings.rangeColorMapping!=i?e=this.bubbleSettings.colorMappings.rangeColorMapping:this.bubbleSettings.colorMappings.equalColorMapping!=i&&(e=this.bubbleSettings.colorMappings.equalColorMapping,v=!1):this.bubbleSettings.colorPath&&(e=this.dataSource,v=!1,b=!0),this.legendSettings.type==t.datavisualization.Map.LegendType.Bubbles&&(this.bubbleSettings.colorMappings!=null||this.bubbleSettings.colorPath)&&(this.legendSettings.mode==i||this.legendSettings.mode==t.datavisualization.Map.LegendMode.Default||this.legendSettings.mode==t.datavisualization.Map.LegendMode.Interactive&&this.bubbleSettings.colorMappings.equalColorMapping!=null)){for(this.legendSettings.iconHeight==i&&(this.legendSettings.iconHeight=20),this.legendSettings.iconWidth==i&&(this.legendSettings.iconWidth=20),f=0;f<e.length;f++)k=n.extend(!0,null,this.legendSettings),u={fill:e[f]._bubblecolor?e[f]._bubblecolor:e[f].color?e[f].color:e[f][this.bubbleSettings.colorPath],legendLabel:e[f].legendLabel!=null?e[f].legendLabel:v?e[f].from:e[f].value!=null?e[f].value:e[f][this.legendSettings.textPath],dataSource:e[f],legendSettings:k},r._trigger("legendItemRendering",{model:r.model,data:u}),u.hideLegend||(c=n("<div class='e-mapBubbleLegend' id='"+r._id+"Legend_"+f+"'/>"),r.model.enableRTL?(s=10,o=this._createLabel(u.legendLabel,s,h,"e-legendlabeltext",f,r,u),s+=this._calcWidth(u.legendLabel),this._drawBubbleLegendIcon(u,s,h,c)):(this._drawBubbleLegendIcon(u,s,h,c),o=this._createLabel(u.legendLabel,s+u.legendSettings.iconWidth+5,h,"e-legendlabeltext",f,r,u)),u.legendLabel!=null?o[0].innerText=u.legendLabel:u.legendLabel=o[0].innerText=v?u.from:b?u[u.legendSettings.textPath]:u.value,r._isSVG?(c.appendTo(r._legendDiv),o.appendTo(r._legendDiv)):(l.append(c),l.append(o)),a<o[0].innerText.length*10+u.legendSettings.iconWidth&&(a=o[0].innerText.length*10+u.legendSettings.iconWidth),p=o.height(),w=c.height(),h+=p>w?p+5:w+5);y=h}if(r._legendDivHeight=y,r._legendDivWidth=a,u.legendSettings.position=="none")d=r._width*u.legendSettings.positionX/100,g=r._height*u.legendSettings.positionY/100,r._legendDiv&&r._legendDiv.css({left:d+"px",top:g+"px"});else{var tt=this.legendSettings.position==i?"topleft":this.legendSettings.position,ut={width:a,height:y},nt=r._getPosition(tt,r._legendSize);r._legendDiv&&r._legendDiv.css({left:nt.x+"px",top:nt.y+"px"})}}},_drawBubbleLegendIcon:function(n,i,r,u){n.legendSettings.icon==t.datavisualization.Map.LegendIcons.Circle?u.css({height:n.legendSettings.iconHeight+"px",width:n.legendSettings.iconWidth+"px","border-radius":n.legendSettings.iconHeight/2+"px","background-color":n.fill,left:i+"px",top:r+"px",position:"absolute",cursor:n.legendSettings.toggleVisibility?"pointer":"default"}):u.css({height:n.legendSettings.iconHeight+"px",width:n.legendSettings.iconWidth+"px","background-color":n.fill,left:i+"px",top:r+"px",position:"absolute",cursor:n.legendSettings.toggleVisibility?"pointer":"default"})},_animateBubble:function(t,i,r){var f={fx:t.getAttribute("r")/2},o=f.fx*2,e=n(t),u=this;n(f).delay(i).each(function(){}).animate({fx:o},{duration:700,step:function(n){r._isSVG&&(e.attr("style","display:block;pointer-events:stroke;"),e.attr("r",n))},complete:function(){u._bubbleCount++;u._bubbleCount==u._bubbles.length&&u._setMapElements()}})},_setMapElements:function(){for(var i,t=0;t<this._mapItems.length;t++)i=this._mapItems[t],n(i).css({display:"block"})},_setMapItemsPositionWithAnimation:function(t){var a,u,s,o,i,r,f;for(this._bubbleCollection=[],i=0;i<this._bubbles.length;i++){var h=this._bubbles[i],v=this._bubbles[i].getAttribute("r"),f=this._bounds[i],s=(f.x+t._translatePoint.x)*t._scale,o=(f.y+t._translatePoint.y)*t._scale,c=t.validateBubblePosition(f.points,{x:s,y:o},v),l=0,y=Math.floor(Math.random()*(20-l)+l),p=parseInt(y*50);t._isSVG&&n(h).attr({cx:c.x,cy:c.y});this._animateBubble(h,p,t);this._bubbleCollection.push(h)}for(a=t._getFactor(),i=0;i<this._mapMarkers.length;i++)r=this._mapMarkers[i],this.markers.length>0&&(u=this.markers[i]),f=t._isTileMap?t._convertTileLatLongtoPoint(u.latitude!=null?u.latitude:u.Latitude,u.longitude!=null?u.longitude:u.Longitude):t._convertLatLongtoPointforMarker(u.latitude!=null?u.latitude:u.Latitude,u.longitude!=null?u.longitude:u.Longitude,a),s=this._isSVG?f.x:(f.x+t._transformX)*t._scale,o=f.y,n(r).css({display:"block",left:s,top:o-100}),n(r).delay(500).each(function(){}).animate({top:o},500);for(i=0;i<this._mapItems.length;i++){var r=this._mapItems[i],f=this._bounds[i],e=r[0].getBoundingClientRect(),w=this._bubbleCollection[i]?parseFloat(this._bubbleCollection[i].getAttribute("cx")):0,b=this._bubbleCollection[i]?parseFloat(this._bubbleCollection[i].getAttribute("cy")):0,s=this._bubbleCollection[i]?w-e.width/2:(f.x+t._translatePoint.x)*t._scale-e.width/2,o=this._bubbleCollection[i]?b-e.height/4:(f.y+t._translatePoint.y)*t._scale-e.height/4;this._bubbles.length>0?n(r).css({left:s,top:o,display:"none"}):n(r).css({left:s,top:o,display:"block"})}for(i=0;i<this._labelCollection.length;i++){r=this._labelCollection[i];f=this._labelBounds[i];n(r).css("display","block");var e=r[0].getBoundingClientRect(),k=t._isSVG?e.width:e.right-e.left,d=t._isSVG?e.height:e.bottom-e.top,s=(f.x+t._translatePoint.x)*t._scale-k/2,o=(f.y+t._translatePoint.y)*t._scale-d/2;(r[0].className="e-smartLabelStyle")&&(r[0].className="e-labelStyle",n(r[0]).css({"pointer-events":"none",position:"absolute"}));r[0].innerHTML=this._labelData[i];var g=this.labelSettings.font.fontStyle,nt=this.labelSettings.font.fontFamily,tt=this.labelSettings.font.size,it=this.labelSettings.font.fontWeight,rt=this.labelSettings.font.color,ut=this.labelSettings.font.opacity;n(r).css({left:s,top:o,"font-family":nt,"font-style":g,"font-size":tt,"font-weight":it,color:rt,opacity:ut})}this.labelSettings!=null&&this.labelSettings.showLabels&&this._validateSmartLabel(t)},_legendToggleVisibility:function(t,i,r){var nt=t.legendSettings,p={data:nt,model:r},w,c,l,e,u,k,a,y,d,g;if(t.dataSource!=null)for(w=t._renderedShapes.length,u=0;u<w;u++){var o=i.target.id,h=t._renderedShapes[u].shapeIndex,tt=o.length,it=o.indexOf("_"),v=parseFloat(o.substring(it+1,tt)),f,s,b;b=t.legendSettings.type=="bubbles"?t.bubbleSettings.colorPath:t.shapeSettings.colorPath;k=t._renderedShapes[u];a=r._reflection(k,b);a!=null&&typeof a=="string"&&(y=a.toLowerCase());t.shapeSettings.colorMappings!=null&&t.legendSettings.type=="layers"?t._renderedShapes[u].mapRangeShapeIndex!=null&&(c=t._renderedShapes[u].mapRangeShapeIndex.index,f=c,s="#"+t._renderedShapes[u].shape.id,e=t.shapeSettings.colorMappings.rangeColorMapping!=null?t.shapeSettings.colorMappings.rangeColorMapping[c].color:t.shapeSettings.colorMappings.equalColorMapping[c].color):t.bubbleSettings.colorMappings!=null&&t.legendSettings.type=="bubbles"?t._renderedShapes[u].mapRangeBubbleIndex!=null&&(l=t._renderedShapes[u].mapRangeBubbleIndex.index,f=l,s="#"+t._renderedShapes[u].shape.id+"_bubble_"+h,e=t.bubbleSettings.colorMappings.rangeColorMapping!=null?t.bubbleSettings.colorMappings.rangeColorMapping[l].color:t.bubbleSettings.colorMappings.equalColorMapping[l].color):t.legendSettings.type=="layers"?(f=u,s="#"+t._renderedShapes[u].shape.id,e=t.shapeSettings.colorPath!=null?y:t.shapeSettings.fill):t.legendSettings.type=="bubbles"&&(f=u,s="#"+t._renderedShapes[u].shape.id+"_bubble_"+h,e=t.bubbleSettings.colorPath!=null?y:t.bubbleSettings.fill);(t.shapeSettings.colorMappings!=null||t.bubbleSettings.colorMappings!=null)&&f==null&&(f=v,h=null,d=n("#"+o).css("background-color"),t._renderedShapes[u]._showBubble?this._legendFillColor=d:e=this._legendFillColor);g=o==r._id+"Legend_"+f||o==r._id+"LegendText_"+f;g&&(r._trigger("legendItemClick",p),!p.cancel&&t.legendSettings.toggleVisibility&&(v==f&&t._renderedShapes[u]._showBubble?(n(s).css({visibility:"hidden"}),n("#"+r._id+"Legend_"+f).css({"background-color":"grey"}),n("#"+r._id+"LegendText_"+f).css({color:"grey"}),n("#"+r._id+"labelStyle_"+h).css({visibility:"hidden"}),t._renderedShapes[u]._showBubble=!1):v!=f||t._renderedShapes[u]._showBubble||(n(s).css({visibility:"visible"}),n("#"+r._id+"Legend_"+f).css({"background-color":e}),n("#"+r._id+"LegendText_"+f).css({color:"black"}),n("#"+r._id+"labelStyle_"+h).css({visibility:"visible"}),t._renderedShapes[u]._showBubble=!0)))}},_resizeShapes:function(n){var u=this.shapeSettings.strokeThickness/n._scale,r,t;if(this._mapShapes!=i)for(r=0;r<this._mapShapes.length;r++)t=this._mapShapes[r].shape,n._isSVG?t.localName=="circle"?t.setAttribute("r",this.shapeSettings.radius/n._scale):this._contains(this._prevSelectedShapes,t)?t.setAttribute("stroke-width",this.shapeSettings.selectionStrokeWidth/n._scale):t.setAttribute("stroke-width",u):t.nodeName=="oval"||(t.strokeweight=this._contains(this._prevSelectedShapes,t)?this.shapeSettings.selectionStrokeWidth/n._scale:u)},_setMapItemsPosition:function(t){var v,o,h,c,r,e,f;if(this._bubbleCollection=[],this._bubbles!=i)for(r=0;r<this._bubbles.length;r++){var s=this._bubbles[r],a=this._bubbles[r].getAttribute("r"),f=this._bounds[r];if(t._isSVG){var h=(f.x+t._translatePoint.x)*t._scale,c=(f.y+t._translatePoint.y)*t._scale,l=t.validateBubblePosition(f.points,{x:h,y:c},a);n(s).attr({cx:l.x,cy:l.y})}else{s=document.getElementById(s.id);var h=(f.x+t._translatePoint.x)*t._scale,c=(f.y+t._translatePoint.y)*t._scale,l=t.validateBubblePosition(f.points,{x:h,y:c},a),y=l.y-(s.getBoundingClientRect().bottom-s.getBoundingClientRect().top)/2,p=l.x-(s.getBoundingClientRect().right-s.getBoundingClientRect().left)/2;n(s).css({left:p,top:y})}this._bubbleCollection.push(s)}if(this._mapItems!=i)for(r=0;r<this._mapItems.length;r++){e=this._mapItems[r];f=this._bounds[r];n(e).css({display:"block"});var u=e[0].getBoundingClientRect(),w=t._isSVG?u.width:u.right-u.left,b=t._isSVG?u.height:u.bottom-u.top,k=this._bubbleCollection[r]?parseFloat(this._bubbleCollection[r].getAttribute("cx")):0,d=this._bubbleCollection[r]?parseFloat(this._bubbleCollection[r].getAttribute("cy")):0,h=this._bubbleCollection[r]?k-u.width/2:(f.x+t._translatePoint.x)*t._scale-u.width/2,c=this._bubbleCollection[r]?d-u.height/4:(f.y+t._translatePoint.y)*t._scale-u.height/4;n(e).css({left:h,top:c})}if(this._mapMarkers!=i)for(v=t._getFactor(),r=0;r<this._mapMarkers.length;r++)e=this._mapMarkers[r],this.markers.length>0&&(o=this.markers[r]),f=t._isTileMap?t._convertTileLatLongtoPoint(o.latitude!=null?o.latitude:o.Latitude,o.longitude!=null?o.longitude:o.Longitude):t._convertLatLongtoPointforMarker(o.latitude!=null?o.latitude:o.Latitude,o.longitude!=null?o.longitude:o.Longitude,v),h=f.x,c=f.y,n(e).css({left:h,top:c});if(this._labelCollection!=i)for(r=0;r<this._labelCollection.length;r++){e=this._labelCollection[r];f=this._labelBounds[r];n(e).css("display","block");var u=e[0].getBoundingClientRect(),w=t._isSVG?u.width:u.right-u.left,b=t._isSVG?u.height:u.bottom-u.top,h=(f.x+t._translatePoint.x)*t._scale-w/2,c=(f.y+t._translatePoint.y)*t._scale-b/2;(e[0].className="e-smartLabelStyle")&&(e[0].className="e-labelStyle",n(e[0]).css("background-color","transparent"),n(e[0]).css({"pointer-events":"none",position:"absolute"}));e[0].innerHTML=this._labelData[r];var g=this.labelSettings.font.fontStyle,nt=this.labelSettings.font.fontFamily,tt=this.labelSettings.font.size,it=this.labelSettings.font.fontWeight,rt=this.labelSettings.font.color,ut=this.labelSettings.font.opacity;n(e).css({left:h,top:c,"font-family":nt,"font-style":g,"font-size":tt,"font-weight":it,color:rt,opacity:ut})}this.labelSettings!=null&&this.labelSettings.showLabels&&this._validateSmartLabel(t)},_validateSmartLabel:function(r){function fi(n){var i=[],t;for(i.push(0),t=10;t<=n;t+=20)i.push(-t),i.push(t);return i}function ei(n){for(var t=0;t<ht.length;t++)if(oi(n,ht[t]))return!1;return!0}function oi(n,t){return n.left-2>=t.left+t.width||n.left-2+n.width<=t.left-2||n.top-2>=t.top+t.height||n.top+n.height<=t.top-2?!1:!0}var ht,p,yt,pt,it,rt,lt,bt,at,ui,tt,v,et,c,e,st,f;if(this._smartLabels=[],ht=[],this._labelCollection.length>0)for(p=0;p<this._mapShapes.length;p++){var o=this._labelBounds[p],u={x:(o.x+r._translatePoint.x)*r._scale,y:(o.y+r._translatePoint.y)*r._scale},dt={x:(o.rightMin.x+r._translatePoint.x)*r._scale,y:(o.rightMin.y+r._translatePoint.y)*r._scale},gt={x:(o.rightMax.x+r._translatePoint.x)*r._scale,y:(o.rightMax.y+r._translatePoint.y)*r._scale},ni={x:(o.leftMin.x+r._translatePoint.x)*r._scale,y:(o.leftMin.y+r._translatePoint.y)*r._scale},ti={x:(o.leftMax.x+r._translatePoint.x)*r._scale,y:(o.leftMax.y+r._translatePoint.y)*r._scale},l=this._labelCollection[p];if(u.x>0&&u.x<r._width&&u.y>0&&u.y<r._height){var ct=this._mapShapes[p].shape,a=ct.getBoundingClientRect(),w=!1,nt=l[0].getBoundingClientRect(),s=nt.height,h=nt.width;r._isSVG||(a={width:a.right*r._scale-a.left*r._scale,height:a.bottom*r._scale-a.top*r._scale},s=nt.bottom-nt.top,h=nt.right-nt.left);yt=!1;pt=!1;(h/2>dt.x-u.x||h/2>gt.x-u.x)&&(s/2>u.y-dt.y||s/2>gt.y-u.y)?yt=!0:(h/2>u.x-ni.x||h/2>u.x-ti.x)&&(s/2>u.y-ni.y||s/2>ti.y-u.y)&&(pt=!0);var b=0,k=0,wt=20,d=[],ii=[];if(s>a.height||h>a.width||yt||pt)if(this.labelSettings.enableSmartLabel)for(this.labelSettings.smartLabelSize==t.datavisualization.Map.LabelSize.Fixed?(s=25,h=15*this.labelSettings.labelLength):(h*=1.3,s=25);!w;){for(wt>400&&(w=!0),d=fi(wt),ii=d,it=0;it<d.length;it++)for(rt=0;rt<d.length;rt++)b=ii[rt],k=d[it],u.x+b+h>r._width&&u.x<r._width&&(b-=u.x+b+h-r._width),u.x+b<0&&(b=0),u.y+k<0&&(k=0),u.y+k+s>r._height&&u.y<r._height&&(k-=u.y+k+s-r._height),lt={left:u.x+b,top:u.y+k,height:s,width:h},ei(lt)&&(bt=r._getIntersectedElements(lt,this._mapShapes),bt!=null&&bt.length==0&&(w=!0,it=d.length,rt=d.length,l[0].className="e-smartLabelStyle",n(l).css({"pointer-events":"stroke",position:"absolute"}),this.labelSettings.smartLabelSize==t.datavisualization.Map.LabelSize.Fixed&&(l[0].innerHTML=l[0].innerHTML.substring(0,this.labelSettings.labelLength)),l.mouseenter({Param1:this,Param2:this._mapShapes[p],map:r},r._polyEnterFunction),l.mousemove({Param1:this,Param2:this._mapShapes[p]},r._polyMoveFunction),n(l).css({left:u.x+b,top:u.y+k,"background-color":r._isSVG?ct.getAttribute("fill"):ct.fillcolor.value}),ht.push(lt),at={},at.shape=ct,at.legend=l,this._smartLabels.push(at)));wt+=10}else{var ri=!0,ut=0,ft=0,w=!0;for(c=0;c<o.points.length;c++)v={x:(o.points[c].x+r._translatePoint.x)*r._scale,y:(o.points[c].y+r._translatePoint.y)*r._scale},w?(ut=v.y,ft=v.y,w=!1):(ut=Math.min(ut,v.y),ft=Math.max(ft,v.y));for(ui=Math.floor((ft-ut)/s),tt=[],f=0;f<ui;f++)e=[],tt.push(e);for(c=0;c<o.points.length;c++)v={x:(o.points[c].x+r._translatePoint.x)*r._scale,y:(o.points[c].y+r._translatePoint.y)*r._scale},et=Math.floor((v.y-ut)/s),et>0&&(et-=1),e=tt[et],e==i&&(e=[]),e.push({x:v.x,y:v.y}),tt[et]=e;for(c=0;c<tt.length;c++){e=tt[c];w=!0;var y=0,g=0,ot=0,vt=[],kt=[];for(f=0;f<e.length;f++)w?(y=e[f].x,g=e[f].x,ot=e[f].y,w=!1):(y=Math.min(y,e[f].x),g=Math.max(g,e[f].x),e[f].x==g&&(ot=e[f].y)),e[f].x<u.x&&vt.push(e[f].x),e[f].x>u.x&&kt.push(e[f].x);for(a.left<r._mapContainer[0].getBoundingClientRect().left+1&&(y=r._mapContainer[0].getBoundingClientRect().left+1-a.left,vt.push(y)),st=!1,f=0;f<vt.length;f++)if(g-vt[f]<h+1){st=!0;break}if(!st)for(f=0;f<kt.length;f++)if(kt[f]-y<h+1){st=!0;break}if(g-y>h+1&&!st){l.css("left",a.left*r._scale+(y-a.left*r._scale)+(g-y)/2-h/2+"px");l.css("top",ot+"px");ft-ot<s&&l.css("top",ot-s+"px");ri=!1;break}}ri&&l.css("display","none")}}}},_fillColors:function(n,t,i,r,u,f,e){t!=null&&t.rangeColorMapping!=null?this._fillRangeColors(n,t.rangeColorMapping,i,r,e,u,f):t!=null&&t.equalColorMapping!=null?this._fillEqualColors(n,t,i,r,f,e,u):this.shapeSettings.colorPath!=null&&this._fillIndividualColors(u,i,this.shapeSettings,r,e,f)},_fillIndividualColors:function(n,t,i,r,u,f){var e={fill:n[i.colorPath],stroke:i.stroke,strokeThickness:i.strokeThickness,shapeData:n,shapeProperties:f};r._trigger("shapeRendering",{model:r.model,data:e});(e.fill!=n[i.colorPath]||e.fill==null)&&(e.fill=e.fill==null?f[i.colorPath]?f[i.colorPath]:i.fill:e.fill,n._color=e.fill);r._isSVG?(t.setAttribute("fill",e.fill),t.setAttribute("stroke",e.stroke),t.setAttribute("stroke-width",e.strokeThickness)):(t.fillcolor=e.fill,t.strokecolor=e.stroke,t.strokeweight=e.strokeThickness);t.highlightcolor=t.highlightcolor},_fillEqualColors:function(t,i,r,u,f,e,o){var h=this,s,c,l;n.each(i.equalColorMapping,function(n,i){i.value==t&&(s={fill:i.color,stroke:h.shapeSettings.stroke,strokeThickness:h.shapeSettings.strokeThickness,shapeData:o,shapeProperties:f},e?(u._trigger("bubbleRendering",{model:u.model,data:s}),l={index:n},o.mapRangeBubbleIndex=l):(u._trigger("shapeRendering",{model:u.model,data:s}),c={index:n},o.mapRangeShapeIndex=c),s.fill!=i.color&&(i._color=s.fill),u._isSVG?(r.setAttribute("fill",s.fill),r.setAttribute("stroke",s.stroke),r.setAttribute("stroke-width",s.strokeThickness)):(r.fillcolor=s.color,r.strokecolor=s.stroke,r.strokeweight=s.strokeThickness),r.highlightcolor=i.highlightcolor)})},_updateLegendRange:function(t,r,u){for(var w,f,h,l,o,s,c,p,a=r.shapeSettings.colorMappings.rangeColorMapping,v,y,e=0;e<a.length;e++)if(w=null,f=a[e],r.shapeSettings.enableGradient&&(v=document.getElementById("rootGroup"),y=r._createGradientElement(v,f.gradientColors,0,0,100,0,r,e)),t>=f.from&&t<=f.to)for(h=e,e!=0&&(h=e*10),l=f.from,o=f.from+(f.to-f.from)/10,s=h;s<h+10;s++){if(t>=l&&t<=o)return c={},p=this._getColorRatio(.7,1,t,f.from,f.to),r._legendRects[s]!=i&&(c=r._legendRects[s]),c.color=r.shapeSettings.enableGradient?y:f.color,n(u).css({opacity:p}),c;l=o;o=o+(f.to-f.from)/10}},_fillRangeColors:function(t,i,r,u,f,e,o){for(var s,p,l,a,v,y,c,h=0;h<i.length;h++)s=i[h],this.bubbleSettings.enableGradient&&(v=document.getElementById("svgDocument"),y=this._createGradientElement(v,i[h].gradientColors,0,0,100,0,this,h),s.color=y),t>=s.from&&t<=s.to&&(p=this._getColorRatio(.7,1,t,s.from,s.to),c={from:s.from,to:s.to,fill:s.color,value:t,bubbleOpacity:this.bubbleSettings.bubbleOpacity,shapeData:e,shapeProperty:o},f?(u._trigger("bubbleRendering",{model:u.model,data:c}),a={index:h,color:s.color,isBubble:f},e.mapRangeBubbleIndex=a):(u._trigger("shapeRendering",{model:u.model,data:c}),l={index:h,color:s.color},e.mapRangeShapeIndex=l),u._isSVG?(r.setAttribute("fill",c.fill),r.setAttribute("opacity",c.bubbleOpacity)):r.fillcolor=c.fill,r.highlightcolor=s.highlightcolor,n(r).css({opacity:p}))},_getColorRatio:function(n,t,i,r,u){var f=100/(u-r)*(i-r);return(parseFloat(t)-parseFloat(n))/100*f+parseFloat(n)}};n.fn.pinchZoom=function(t,i){var f,u,e=this[0],r=[],o=function(n){var t=e.createSVGPoint(),r=n.targetTouches,u,f,i;return r.length&&(i=r[0],u=i.pageX,f=i.pageY),t.x=u,t.y=f,t},l=function(i){var y=t,e,s,c,l,h,a,v;if(u=y.getCTM().inverse(),f=o(i).matrixTransform(u),e=i.touches,e.length>=2)for(i.preventDefault(),s=0;s<e.length;s++){for(c=e[s],l=!1,h=0;h<r.length;h++)if(a=r[h],c.identifier===a.identifier){l=!0;break}l||(v=n.extend({},c),r.push(v))}},s=function(n,t){var i=t.pageX-n.pageX,r=t.pageY-n.pageY;return Math.sqrt(i*i+r*r)},h=function(n,t){var r=null,u="matrix("+t.a+","+t.b+","+t.c+","+t.d+","+t.e+","+t.f+")";t.a>i._baseScale&&i.model.zoomSettings.enableZoom&&i.enablePan()&&(t.a>i._scale?r=!0:t.a<i._scale&&(r=!1),i._scale=t.a,i._translatePoint.x=t.e/t.a,i._translatePoint.y=t.f/t.a,i._applyTransform(i._scale,i._translatePoint),i.model.zoomSettings.level=i._scale-i._baseScale+1,i._zoomLevel(i._scale-i._baseScale+1),r!=null&&r?(i._trigger("zoomedIn",{originalEvent:null,zoomLevel:i._zoomLevel()}),i._updateSliderValue(!1),i._resizeShape()):r!=null&&(i._trigger("zoomedOut",{originalEvent:null,zoomLevel:i._zoomLevel()}),i._updateSliderValue(!1),i._resizeShape()),i._isDragging=!0,i._refrshLayers())},a=function(n){for(var e,t,u,o,f=n.changedTouches,i=0;i<f.length;i++)for(e=f[i],t=0;t<r.length;t++)u=r[t],u.identifier===e.identifier&&(o=r.indexOf(u),r.splice(o,1))},c=function(n,t){n.pageX=t.pageX;n.pageY=t.pageY},v=function(n){var l=t,y=n.touches,w,i,p;if(y.length>=2){n.preventDefault();var a=y[0],v=y[1],b=r[0],k=r[1];w=s(a,v)/s(b,k);i=e.createSVGPoint();i.x=(a.pageX+v.pageX)/2;i.y=(a.pageY+v.pageY)/2;i=i.matrixTransform(l.getCTM().inverse());p=e.createSVGMatrix().translate(i.x,i.y).scale(w).translate(-i.x,-i.y);h(l,l.getCTM().multiply(p));typeof u=="undefined"&&(u=l.getCTM().inverse());u=u.multiply(p.inverse());c(b,a);c(k,v)}else r.length||(i=o(n).matrixTransform(u),h(l,u.inverse().translate(i.x-f.x,i.y-f.y)))};this[0].addEventListener("touchstart",l,!1);this[0].addEventListener("touchend",a,!1);this[0].addEventListener("touchmove",v,!1)};jQuery.uaMatch=function(n){n=n.toLowerCase();var t=/(chrome)[ \/]([\w.]+)/.exec(n)||/(webkit)[ \/]([\w.]+)/.exec(n)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(n)||/(msie) ([\w.]+)/.exec(n)||n.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(n)||[];return{browser:t[1]||"",version:t[2]||"0"}};t.datavisualization.Map.Locale=t.datavisualization.Map.Locale||{};t.datavisualization.Map.Locale["default"]=t.datavisualization.Map.Locale["en-US"]={zoomIn:"Zoom In",zoomOut:"Zoom Out",panTop:"Pan Top",panBottom:"Pan Bottom",panLeft:"Pan Left",panRight:"Pan Right",home:"Home"};t.datavisualization.Map.LayerType={Geometry:"geometry",OSM:"osm",Bing:"bing"};t.datavisualization.Map.LegendIcons={Rectangle:"rectangle",Circle:"circle"};t.datavisualization.Map.LabelSize={Fixed:"fixed",Default:"default"};t.datavisualization.Map.LegendMode={Default:"default",Interactive:"interactive"};t.datavisualization.Map.BingMapType={Aerial:"aerial",AerialWithLabel:"aerialwithlabel",Road:"road"};t.datavisualization.Map.GeometryType={Normal:"normal",Geographic:"geographic"};t.datavisualization.Map.SelectionMode={Default:"default",Multiple:"multiple"};t.datavisualization.Map.ColorPalette={Palette1:"palette1",Palette2:"palette2",Palette3:"palette3",CustomPalette:"custompalette"};t.datavisualization.Map.LegendType={Layers:"layers",Bubbles:"bubbles"};t.datavisualization.Map.Position={None:"none",TopLeft:"topleft",TopCenter:"topcenter",TopRight:"topright",CenterLeft:"centerleft",Center:"center",CenterRight:"centerright",BottomLeft:"bottomleft",BottomCenter:"bottomcenter",BottomRight:"bottomright"};t.datavisualization.Map.DockPosition={Top:"top",Bottom:"bottom",Right:"right",Left:"left"};t.datavisualization.Map.Alignment={Near:"near",Center:"center",Far:"far"};t.datavisualization.Map.LabelOrientation={Horizontal:"horizontal",Vertical:"vertical"};t.datavisualization.Map.FontStyle={Normal:"normal",Italic:"italic"};t.datavisualization.Map.FontWeight={Regular:"regular",Bold:"bold",Lighter:"lighter"}})(jQuery,Syncfusion)});
