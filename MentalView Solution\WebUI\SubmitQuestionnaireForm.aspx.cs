﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebUI
{
    public partial class SubmitQuestionnaireForm : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                StreamWriter writer = null;
                string formText = "";
                formText += "\r\n\r\n\r\nForm\r\n\r\n\r\n";
                foreach (string key in Request.Form.Keys)
                {
                    formText += key + ": " + Request.Form[key] + "<br/>";
                }
                formText += "\r\n\r\n\r\nParams\r\n\r\n\r\n";
                foreach (string key in Request.Params.Keys)
                {
                    formText += key + ": " + Request.Params[key] + "<br/>";
                }
                formText += "\r\n\r\n\r\nQuerystring\r\n\r\n\r\n";
                foreach (string key in Request.QueryString.Keys)
                {
                    formText += key + ": " + Request.QueryString[key] + "<br/>";
                }
                this.Label1.Text = formText;

                string questionnaiersSubmitsFile = HttpContext.Current.Server.MapPath("~/QuestionnaiersSubmits.txt");

                if (File.Exists(questionnaiersSubmitsFile) == false)  //Αν το αρχείο δεν υπάρχει
                {
                    FileStream stream = File.Create(questionnaiersSubmitsFile);  //τοτε το δημιουργεί
                    stream.Close();  //και το κλείνει
                }
                using (writer = File.AppendText(questionnaiersSubmitsFile))
                {
                    writer.WriteLine("START\r\n");
                    writer.WriteLine(formText);
                    writer.WriteLine("\r\nEND\r\n\r\n");
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
            }
        }
    }
}