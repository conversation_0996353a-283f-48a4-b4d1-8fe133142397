/*!
*  filename: ej.mobile.listview.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["jsrender","./../common/ej.core.min","./../common/ej.globalize.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function($,ej){ej.widget("ejmListView","ej.mobile.ListView",{_setFirst:!0,_rootCSS:"e-m-lv",validTags:["ul"],_tags:[{tag:"items",attr:["text","value","href","imageUrl","enabled","badge.value","badge.maxValue","allowSelection"],content:"template"}],defaults:{renderMode:"auto",cssClass:"",dataSource:[],templateId:"",enabled:!0,enableChecklist:!1,persistSelection:!1,enablePersistence:!1,enableRippleEffect:ej.isAndroid()?!0:!1,allowSelection:!0,selectedIndex:null,checkedIndices:[],query:null,deleteMode:"none",fields:{text:null,value:null,image:null,groupBy:null,checkBy:null,enabled:null,href:null,allowSelection:null,badge:{value:null,maxValue:100}},windows:{preventSkew:!1},itemsCount:0,items:[],touchStart:null,select:null,touchEnd:null,beforeDelete:null,afterDelete:null,actionSuccess:null,actionFailure:null,actionComplete:null},dataTypes:{renderMode:"enum",dataSource:"data",items:"data",enabled:"boolean",enableRippleEffect:"boolean",selectedIndex:["string","number"],checkedIndices:"array",enableChecklist:"boolean",persistSelection:"boolean",allowSelection:"boolean",windows:{preventSkew:"boolean"},templateId:"string",itemsCount:"number"},observables:["selectedIndex"],selectedIndex:ej.util.valueFunction("selectedIndex"),_init:function(){ej.setRenderMode(this);this._dataObj=this.model.dataSource;this._index=0;this._initialize()},_initialize:function(){this._listItems=[];this._orgEle=this.element.clone();typeof this.model.dataSource=="string"&&(this.model.dataSource=eval(this.model.dataSource));Object.keys(this.model.dataSource).length?this._dataProcess(this.model.dataSource,null):this._render();this._browser=ej.browser().toLowerCase();this._doc=$(document);this._relDis=0;this._move=!1},_dataProcess:function(data,action){var proxy=this,query;query=action=="append"?this.model.query?this.model.query:"ej.Query().range("+this._index+","+(this._index+this.model.itemsCount)+")":this.model.query&&this.model.itemsCount?this.model.query+".take("+this.model.itemsCount+")":!this.model.query&&this.model.itemsCount?"ej.Query().take("+this.model.itemsCount+")":this.model.query?this.model.query:"ej.Query()";data instanceof ej.DataManager?data.executeQuery(eval(query)).done(function(n){action=="refresh"&&proxy.element.children().remove();proxy.model.dataSource=action=="append"?proxy.model.dataSource.concat(n.result):n.result;proxy._dataSource=n.result;proxy._render();proxy._trigger("actionSuccess",n);proxy.model.itemsCount&&(proxy._index+=proxy.model.itemsCount)}).fail(function(n){proxy._trigger("actionFailure",n)}).always(function(n){proxy._trigger("actionComplete",n)}):(this._dataSource=ej.DataManager(data).executeLocal(eval(query)),action=="refresh"&&this.element.children().remove(),this.model.dataSource=action=="append"?this.model.dataSource.concat(this._dataSource):this._dataSource,this._render(),this.model.itemsCount&&(this._index+=this.model.itemsCount))},_render:function(){this._renderControl();this._setValues();this._wireEvents()},_setValues:function(){this.model.enabled?(this.model.selectedIndex!=null&&this.selectItemByIndex(this.selectedIndex()),this.model.checkedIndices.length&&this.checkItemsByIndices(this.model.checkedIndices)):this.disable()},_renderItems:function(){if(this.model.items.length<1){var n=this.element.find("li");for(i=0;i<n.length;i++)this.model.items.push({text:ej.getAttrVal(n[i],"data-ej-text",null),value:ej.getAttrVal(n[i],"data-ej-value",null),href:ej.getAttrVal(n[i],"data-ej-href",null),imageUrl:ej.getAttrVal(n[i],"data-ej-imageurl",null),enabled:ej.getAttrVal(n[i],"data-ej-enabled",!0),allowSelection:ej.getAttrVal(n[i],"data-ej-allowselection",!0),badge:{value:ej.getAttrVal(n[i],"data-ej-badge-value",null),maxValue:ej.getAttrVal(n[i],"data-ej-badge-maxvalue",null)}})}else for(i=0;i<this.model.items.length;i++)this.model.items[i].template&&this.element.append(ej.buildTag("li",this.model.items[i].template))},_renderControl:function(){this.element.addClass("e-m-"+this.model.renderMode+" e-m-lv e-m-user-select"+(this.model.allowSelection?"":" e-m-lv-preventselection")+" "+this.model.cssClass);this.model.templateId.length?this._renderTemplateList():this._dataObj.length==0?(this._renderItems(),this._renderList()):this.model.dataSource.length&&(this.model.fields.groupBy?this._dataSourceGroupList():this._dataSourceList(this._dataSource))},_wireEvents:function(n){this._touchStart=$.proxy(this._touchStartHandler,this);this._touchEnd=$.proxy(this._touchEndHandler,this);this._touchMove=$.proxy(this._touchMoveHandler,this);ej.listenEvents([this.element],[ej.startEvent()],[this._touchStart],n)},_renderList:function(){var n,t,i;for(this._listItems=this.element.find("li").addClass("e-m-lv-item"),this._setEnableRippleEffect(),this._listHeaders=this.element.find(">span").addClass("e-m-lv-group"),n=0;n<this._listItems.length;n++)t=$(this._listItems[n]).attr({index:n,"data-value":this.model.items[n].value,"data-allowselection":this.model.items[n].allowSelection}).addClass(this.model.items[n].enabled=="false"?"e-m-state-disabled":""),i=ej.buildTag("a.e-m-lv-content",this.model.items[n].text?this.model.items[n].text:t.html(),{},{"data-ej-appajax":!1,href:this.model.items[n].href}),t.empty().append(i),this._slideDeleteButton(t,i),this.model.items[n].imageUrl&&i.css("background-image","url("+this.model.items[n].imageUrl+")").addClass("e-m-lv-image"),this._setBadge(t)},_setEnableRippleEffect:function(){this._listItems[this.model.enableRippleEffect?"addClass":"removeClass"]("e-ripple")},_slideDeleteButton:function(n,t){if(this.model.deleteMode==ej.mobile.ListView.DeleteMode.SlideButton){var i=ej.buildTag("div.e-m-lv-slideitem"),r=ej.buildTag("span.e-m-lv-delete","Delete");n.append(i.append(r));t.addClass("e-m-lv-swipeout");this._swipeDis=this.element.find(".e-m-lv-slideitem").width()}},_renderTemplateList:function(){var n=$("#"+this.model.templateId);n.html("<li class='e-m-lv-item'>"+n.html()+"<\/li>");this._dataSource.length&&this.element.html(n.render(this._dataSource));ej.widget.init(this.element)},_dataSourceList:function(n){var t=this;$(n).each(function(n,i){var u=ej.buildTag("a.e-m-lv-content",i[t.model.fields.text],{},{"data-ej-appajax":!1,href:i[t.model.fields.href]}),f={badge:{value:ej.isNullOrUndefined(i[t.model.fields.badge.value])?null:i[t.model.fields.badge.value],maxValue:ej.isNullOrUndefined(i[t.model.fields.badge.value])?null:i[t.model.fields.badge.maxValue]}},r=ej.buildTag("li.e-m-lv-item"+(i[t.model.fields.checkBy]?" e-m-lv-checked":"")+(i[t.model.fields.enabled]?" e-m-state-disabled":""),u,{},{"data-ej-badge-value":ej.isNullOrUndefined(i[t.model.fields.badge.value])?null:i[t.model.fields.badge.value],"data-ej-badge-maxvalue":ej.isNullOrUndefined(i[t.model.fields.badge.maxValue])?null:i[t.model.fields.badge.maxValue]});ej.isNullOrUndefined(i[t.model.fields.image])||u.css("background-image","url("+i[t.model.fields.image]+")").addClass("e-m-lv-image");t.model.items.push(f);t._listItems.push(r);t._slideDeleteButton(r,u);t.element.append(r.attr({index:n,"data-value":i[t.model.fields.value],"data-allowselection":i[t.model.fields.allowSelection]}));t._setBadge(r)})},_dataSourceGroupList:function(){var n=this,t=ej.DataManager(n._dataSource).executeLocal(ej.Query().group(n.model.fields.groupBy));$(t).each(function(t,i){n.element.append(ej.buildTag("span.e-m-lv-group",i.key));n._dataSourceList(i.items)})},_touchStartHandler:function(n){ej.isTouchDevice()&&(n=n.touches?n.touches[0]:n);this._startX=n.clientX;this._startY=n.clientY;this._timeStart=n.timeStamp||Date.now();var t=$(n.target);this._curentEle=t.closest("li");this._removeSelect();t.hasClass("e-m-lv-delete")||this._curentEle.hasClass("swipeend")||(this.model.allowSelection&&this._curentEle.attr("data-allowselection")!="false"&&this._curentEle.addClass("e-m-lv-active e-m-lv-selected"),this.model.renderMode!="windows"||this.model.windows.preventSkew||this._curentEle.addClass(ej.isMobile()?ej._getSkewClass($(this._curentEle),n.pageX,n.pageY):"e-m-skew-center"),this.element.find("a,span").css("-"+this._browser+"-transform",""),this.element.find(".e-m-lv-slideitem,.e-m-lv-delete").removeClass("e-m-lv-swipe"),this.element.find("li").removeClass("swipeend"));ej.listenEvents([this._doc,this.element],[ej.moveEvent(),ej.endEvent()],[this._touchMove,this._touchEnd],!1);this.model.touchStart&&this._trigger("touchStart",this._getItemObject(this._curentEle,n))},_touchMoveHandler:function(n){var i,t,r;this._isMoved=n.clientX!=this._startX?!0:!1;this._isMoved&&n.preventDefault();i=$(n.target);ej.isTouchDevice()&&(n=n.touches?n.touches[0]:n);t=n.clientX-this._startX;r=n.clientY-this._startY;Math.abs(r)>=10&&(this._move=!0,this._removeActive(),ej._removeSkewClass(this._curentEle));this.model.deleteMode!=ej.mobile.ListView.DeleteMode.Default&&this._removeActive();i.closest(this.element).length!=0?(this.model.deleteMode==ej.mobile.ListView.DeleteMode.SlideButton&&(t<0||this._curentEle.hasClass("swipeend"))&&(this._relDis=this._curentEle.hasClass("swipeend")?-this._swipeDis+t:t,this._relDis<=0&&(this._curentEle.find("a,span").css("-"+this._browser+"-transform","translate3d("+this._relDis+"px,0px,0px) scale(1)").css("-"+this._browser+"-transition","none"),this._curentEle.find(".e-m-lv-slideitem,.e-m-lv-delete").addClass("e-m-lv-swipe"))),this.model.deleteMode==ej.mobile.ListView.DeleteMode.Swipe&&(this._relDis=t,this._curentEle.find("a").css("-"+this._browser+"-transform","translate3d("+t+"px,0px,0px) scale(1)").css("-"+this._browser+"-transition","none"))):this._touchEndHandler(n)},_touchEndHandler:function(n){var r=$(n.target),i=(n.timeStamp||Date.now())-this._timeStart,t;this.model.persistSelection||this._curentEle.removeClass("e-m-lv-active");this.model.deleteMode==ej.mobile.ListView.DeleteMode.Swipe&&(this._curentEle.find("a").css("-"+this._browser+"-transform","translate3d(0px,0px,0px) scale(1)").css("-"+this._browser+"-transition","").addClass("e-m-lv-swipeout"),(this._curentEle.width()/2<=Math.abs(this._relDis)||i>=120&&i<=200&&Math.abs(this._relDis)>=10)&&this._deleteItem(this._curentEle,n));this.model.deleteMode==ej.mobile.ListView.DeleteMode.SlideButton&&(this._relDis!=0?(t=-this._swipeDis/2<=this._relDis?0:-this._swipeDis,t!=0?this._curentEle.addClass("swipeend"):this._curentEle.removeClass("swipeend"),this._curentEle.find("a,span").css("-"+this._browser+"-transform","translate3d("+t+"px,0px,0px) scale(1)").css("-"+this._browser+"-transition","").addClass("e-m-lv-swipeout")):r.hasClass("e-m-lv-delete")&&this._deleteItem(this._curentEle,n),this._relDis=0);this._move||(this.model.enableChecklist&&this._curentEle.toggleClass("e-m-lv-checked"),this.model.renderMode!="windows"||this.model.windows.preventSkew||ej._removeSkewClass(this._curentEle),this._setModelValues(),this.model.touchEnd&&this._trigger("touchEnd",this._getItemObject(this._curentEle,n)),this.model.select&&this._trigger("select",this._getItemObject(this._curentEle,n)));ej.listenEvents([this._doc,this.element],[ej.moveEvent(),ej.endEvent()],[this._touchMove,this._touchEnd],!0);this._move=!1},_deleteItem:function(n,t){var i=n.index();this.model.beforeDelete&&this._trigger("beforeDelete",this._getItemObject(n,t));this.model.dataSource.length&&this.model.dataSource.splice(i,1);n.remove();this.model.afterDelete&&this._trigger("afterDelete",this._getItemObject(n,t))},_removeSelect:function(){this.element.find(".e-m-lv-active,.e-m-lv-selected").removeClass("e-m-lv-active e-m-lv-selected")},_removeActive:function(){this.element.find(".e-m-lv-active").removeClass("e-m-lv-active")},_setModelValues:function(){var n=this;this.model.selectedIndex=parseInt(this.element.find(".e-m-lv-selected").attr("index"));this.model.checkedIndices=[];this.element.find(".e-m-lv-checked").each(function(){n.model.checkedIndices.push(parseInt($(this).attr("index")))})},_setBadge:function(n,t){var i=t||(this.model.items[n.attr("index")].badge?this.model.items[n.attr("index")].badge.value:null),r;maxBadgeValue=this.model.items[n.attr("index")].badge?this.model.items[n.attr("index")].badge.maxValue:this.model.fields.bage.maxValue;ej.isNullOrUndefined(i)||(r=parseInt(i)>parseInt(maxBadgeValue)?maxBadgeValue+"+":i,n.find(".e-m-lv-content").attr("badgeValue",r).addClass("e-m-lv-badge"))},_getItemObject:function(n,t){var i=this.element.find("li").index(n);return{item:n,index:i,text:n.find("a").html(),badge:n.find("a").attr("badgeValue")||null,isSelected:n.hasClass("e-m-lv-selected"),isChecked:n.hasClass("e-m-lv-checked"),data:this.model.dataSource[i]||null,event:t,isInteraction:t?!0:!1}},addItem:function(n,t){var i=this;this.model.dataSource.length?($(n).each(function(n,r){i.model.dataSource.splice(t?t:i.model.dataSource.length,0,r)}),this._refresh()):(ItemObj=$.isArray(n)?n:$.makeArray(n),$(ItemObj).each(function(n,r){var u=ej.buildTag("a.e-m-lv-content"),f=ej.buildTag("li.e-m-lv-item");t?$(i.element.find("li")[t]).before(f.append(u.html(r))):$(i.element.find("li")[i.element.find("li").length-1]).after(f.append(u.html(r)))}))},refresh:function(n){this._dataProcess(n,"refresh")},append:function(n){this._dataProcess(n,"append")},enable:function(){this.element.removeClass("e-m-state-disabled");this.model.enabled=!0},disable:function(){this.element.addClass("e-m-state-disabled");this.model.enabled=!1},enableItemByIndex:function(n){this.model.enabled&&$(this.element.find("li")[n]).removeClass("e-m-state-disabled")},disableItemByIndex:function(n){this.model.enabled&&$(this.element.find("li")[n]).addClass("e-m-state-disabled")},enableItemsByIndices:function(n){if(this.model.enabled){var t=this;$(n).each(function(n,i){t.enableItemByIndex(i)})}},disableItemsByIndices:function(n){if(this.model.enabled){var t=this;$(n).each(function(n,i){t.disableItemByIndex(i)})}},enableAll:function(){this.model.enabled&&$(this.element.find("li").removeClass("e-m-state-disabled"))},disableAll:function(){this.model.enabled&&$(this.element.find("li").addClass("e-m-state-disabled"))},selectItemByIndex:function(n){this.model.enabled&&n>=0&&(this._curentEle=$(this.element.find("li").removeClass("e-m-lv-selected e-m-lv-active")[this.model.selectedIndex=n]),this._curentEle.addClass("e-m-lv-selected "+(this.model.persistSelection?"e-m-lv-active":"")),this.model.select&&this._trigger("select",this._getItemObject(this._curentEle)))},deselectItem:function(){this.model.enabled&&($(this.element.find("li").removeClass("e-m-lv-selected e-m-lv-active")),this.model.selectedIndex=null)},checkItemsByIndex:function(n){this.model.enableChecklist&&this.model.enabled&&($(this.element.find("li")[n]).addClass("e-m-lv-checked"),this._setModelValues())},checkItemsByIndices:function(n){if(this.model.enableChecklist&&this.model.enabled){var t=this;$(n).each(function(n,i){$(t.element.find("li")[i]).addClass("e-m-lv-checked")});this._setModelValues()}},uncheckItemsByIndex:function(n){this.model.enableChecklist&&this.model.enabled&&($(this.element.find("li")[n]).removeClass("e-m-lv-checked"),this._setModelValues())},uncheckItemsByIndices:function(n){if(this.model.enableChecklist&&this.model.enabled){var t=this;$(n).each(function(n,i){$(t.element.find("li")[i]).removeClass("e-m-lv-checked")});this._setModelValues()}},checkAll:function(){this.model.enableChecklist&&this.model.enabled&&($(this.element.find("li").addClass("e-m-lv-checked")),this._setModelValues())},uncheckAll:function(){this.model.enableChecklist&&this.model.enabled&&($(this.element.find("li").removeClass("e-m-lv-checked")),this._setModelValues())},getItemByIndex:function(n){if(this.model.enabled)return $(this.element.find("li")[n])},getItemByText:function(n){if(this.model.enabled){var t;return $(this.element.find("li")).each(function(i,r){$(r).find("a").html()==n&&(t=i)}),this.getItemByIndex(t)}},getTextByIndex:function(n){if(this.model.enabled)return $(this.element.find("li")[n]).find("a").html()},getIndexByText:function(n){if(this.model.enabled){var t;return $(this.element.find("li")).each(function(i,r){$(r).find("a").html()==n&&(t=i)}),t}},deleteItemByIndex:function(n){if(this.model.enabled){var t=$(this.element.find("li")[n]).remove().find("a").html();return this.model.dataSource.length&&(t=this.model.dataSource.splice(n,1)),t}},deleteItemByText:function(n){if(this.model.enabled)return this.deleteItemByIndex(this.getIndexByText(n))},setBadge:function(n,t){this.model.enabled&&this._setBadge(this.getItemByIndex(n),t)},_setModel:function(n){var t,i=!1;for(t in n){if(!this.model.enabled&&t!="enabled")return!1;switch(t){case"renderMode":this.element.removeClass("e-m-ios7 e-m-android e-m-windows e-m-flat").addClass("e-m-"+this.model.renderMode);case"selectedIndex":this.selectItemByIndex(n[t]);break;case"checkedIndices":this.checkItemsByIndices(n[t]);break;case"enabled":n[t]?this.enable():this.disable();break;case"enableRippleEffect":this._setEnableRippleEffect();break;case"allowSelection":this.element[n[t]?"addClass":"removeClass"]("e-m-lv-preventselection")}(n.dataSource||n.fields||n.query)&&(n.dataSource||(this.model.dataSource=this._dataObj),this.deselectItem(),this.uncheckAll(),i=!0)}i&&this._refresh();i=!1},_refresh:function(){this._destroy();this.element.addClass(this._rootCSS);this._initialize()},_clearElement:function(){this.element.removeAttr("class").removeAttr("style");this.element.empty().html(this._orgEle.html())},_destroy:function(){this._wireEvents(!0);this._clearElement()}});ej.mobile.ListView.DeleteMode={Default:"none",Swipe:"swipe",SlideButton:"slideButton"}})(jQuery,Syncfusion)});
