/*!
*  filename: ej.mobile.slider.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min","./ej.mobile.core.min","./../common/ej.unobtrusive.min","./../common/ej.touch.min"],n):n()})(function(){(function(n,t){t.widget("ejmSlider","ej.mobile.Slider",{_setFirst:!0,_rootCSS:"e-m-slider",defaults:{theme:"auto",orientation:"horizontal",maxValue:100,minValue:0,enableRange:!1,values:[20,80],enableAnimation:!1,animationSpeed:400,readOnly:!1,incrementStep:1,value:0,enabled:!0,enablePersistence:!1,touchStart:null,touchEnd:null,change:null,slide:null,load:null,windows:{renderDefault:!1},ios7:{thumbStyle:"normal"},renderMode:"auto"},dataTypes:{theme:"enum",orientation:"enum",enableRange:"boolean",thumbStyle:"enum",enableAnimation:"boolean",enabled:"boolean",renderMode:"enum",values:"array",enablePersistence:"boolean"},observables:["value"],observableArray:["values"],value:t.util.valueFunction("value"),values:t.util.valueFunction("values"),_init:function(){App.activePage||App.createAppView();this._cloneElement=this.element.clone();this._load();t.setRenderMode(this);t.setTheme(this);this._renderControl();this._initObjects();this.model.readOnly||this._wireEvents(!1)},_renderControl:function(){var n=this.model,r,i,u;this.model.enabled||this.element.addClass("e-m-state-disabled");this.model.renderMode=="windows"&&this.model.windows.renderDefault&&(this.model.theme="default");this._sliderHandle=t.buildTag("div .e-m-slider-outer");this._selectedRegion=t.buildTag("div .e-m-slider-inner").appendTo(this._sliderHandle);this._dragHandle=this.model.renderMode=="ios7"&&this.model.ios7.thumbStyle=="small"?t.buildTag("div .e-m-slider-handleout e-m-small-handle e-m-slider-left ").appendTo(this._sliderHandle):t.buildTag("div .e-m-slider-handleout e-m-slider-left ").appendTo(this._sliderHandle);r=t.buildTag("div .e-m-slider-handlein").appendTo(this._dragHandle);n.enableRange&&(i=this.model.renderMode=="ios7"&&this.model.ios7.thumbStyle=="small"?t.buildTag("div .e-m-slider-handleout e-m-small-handle e-m-slider-right"):t.buildTag("div .e-m-slider-handleout e-m-slider-right"),u=t.buildTag("div .e-m-slider-handlein").appendTo(i),i.appendTo(this._sliderHandle));this.element.addClass("e-m-"+n.orientation+" e-m-"+n.theme).addClass("e-m-slider e-m-"+n.renderMode).append(this._sliderHandle);this.element.append(t.buildTag("input ",{},{},{type:"hidden",name:"sliderValue"}));this._dragHandle=this.element.find(".e-m-slider-handleout");this._inputValue=this.element.find("input");this._wrapperDiv=this.element;this._currentValue=this.value();this["_"+n.orientation+"RenderMode"]()},_horizontalRenderMode:function(){this._setOrientationProperties("width","left")},_verticalRenderMode:function(){this._setOrientationProperties("height","bottom")},_setOrientationProperties:function(t,i){var r=this.model;r.enableRange?(this.values()[0]<=this.values()[1]?(this.values()[0]=this.values()[0]<r.minValue&&this.values()[0]<=r.maxValue?r.minValue:this.values()[0],this.values()[1]=this.values()[1]>=r.minValue&&this.values()[1]>r.maxValue?r.maxValue:this.values()[1]):this.values()[1]=this.values()[0],this._selectedRegion.css(i,parseFloat((this.values()[0]-r.minValue)/(r.maxValue-r.minValue)*100)+"%").css(t,parseFloat((this.values()[1]-r.minValue)/(r.maxValue-r.minValue)*100)-parseFloat((this.values()[0]-r.minValue)/(r.maxValue-r.minValue)*100)+"%"),n(this._dragHandle[0]).css(i,parseFloat((this.values()[0]-r.minValue)/(r.maxValue-r.minValue)*100)+"%"),n(this._dragHandle[1]).css(i,parseFloat((this.values()[1]-r.minValue)/(r.maxValue-r.minValue)*100)+"%")):(this.value(this.value()<r.minValue?r.minValue:this.value()),this.value(this.value()>r.maxValue?r.maxValue:this.value()),this._selectedRegion.css(t,parseFloat((this.value()-r.minValue)/(r.maxValue-r.minValue)*100)+"%"),this._dragHandle.css(i,parseFloat((this.value()-r.minValue)/(r.maxValue-r.minValue)*100)+"%"))},_initObjects:function(){this._createDelegates()},_createDelegates:function(){this._mouseDownHandler=n.proxy(this._onMouseDownHandler,this);this._mouseMoveHandler=n.proxy(this._onMouseMove,this);this._mouseUpHandler=n.proxy(this._onMouseUp,this)},_onMouseDownHandler:function(i){var u,o,f,r,e;if(t.blockDefaultActions(i),t.isTouchDevice()&&(i=i.touches?i.touches[0]:i),!this.model.enabled)return!1;jQuery.fx.off=!1;this.sliderClick=i;this.model.enableRange||this._dragHandle.addClass("e-m-state-active");t.listenEvents([document,document],[t.moveEvent(),t.endEvent()],[this._mouseMoveHandler,this._mouseUpHandler],!1);o={x:i.pageX,y:i.pageY};f=this._getNormalValue(o);this._currentValue=f;r=this;e=this.model.maxValue-this.model.minValue+1;n(this._dragHandle).each(function(t){var i=Math.abs(f-r._getNewValues(t));e>=i&&r._getNewValues(0)!=r._getNewValues(1)&&(e=i,r._closestHandle=n(this),u=t)});n(this.model.enableRange?this._closestHandle:this._dragHandle).removeClass("e-m-state-hover").addClass("e-m-state-active");this._start(i,u);this.model.enableRange&&(this._setZindex(),r._getNewValues(0)==r._getNewValues(1)&&(r._closestHandle=n(this),u=this._handlingIndex),this._setRangeValueProperties(i,u))},_setZindex:function(){this._dragHandle.css("z-index",0);this._closestHandle.css("z-index",5)},_setRangeValueProperties:function(t,i){var f=[],u=[],e,o,s=this.values()[i],r;for(this._closestHandle=n(this._dragHandle[i]),this._handlingIndex=i,r=0;r<this.values().length;r++)r!=i&&u.push(this.values()[r]),f.push(this.values()[r]);for(r=0;r<u.length;r++)if(s>=u[r]){this._closestLeftValue=u[r];break}else if(s<=u[r]){this._closestRightValue=u[r];break}return o=f[0],e=f[f.length-1],o!=e?s<=o?this._closestLeftValue=this.model.minValue:s>=e&&(this._closestRightValue=this.model.maxValue):o==e&&(this._closestHandle.hasClass("e-m-slider-left")?this._closestLeftValue=this.model.minValue:this._closestHandle.hasClass("e-m-slider-right")&&(this._closestRightValue=this.model.maxValue)),t.preventDefault&&t.preventDefault(),!0},_onMouseMove:function(i){if(t.blockDefaultActions(i),t.isTouchDevice()&&(i=i.touches?i.touches[0]:i),!this.model.enabled)return!1;jQuery.fx.off=!0;this.element.css("cursor","pointer");var u={x:i.pageX,y:i.pageY},r=this._getNormalValue(u);this._currentValue=r;this.model.enableRange?this._slide(i,r,this._handlingIndex):this._slide(i,r);n(this.model.enableRange?this._closestHandle:this._dragHandle).addClass("e-m-state-active");i.preventDefault&&i.preventDefault()},_onMouseUp:function(i){if(t.blockDefaultActions(i),t.isTouchDevice()&&(i=i.changedTouches?i.changedTouches[0]:i),!this.model.enabled)return!1;jQuery.fx.off=!1;t.listenEvents([document,document],[t.moveEvent(),t.endEvent()],[this._mouseMoveHandler,this._mouseUpHandler],!0);this._mouseMoveStarted&&(this._mouseMoveStarted=!1,i.target==this.sliderClick.target&&n.data(i.target,this.model.sliderId+".preventClickEvent",!0));this.element.css("cursor","default");var u={x:i.pageX,y:i.pageY},r=this._getNormalValue(u);return this.model.enableRange?this._slide(i,r,this._handlingIndex):this._slide(i,r),this._stop(i,this._handlingIndex),this._change(i,this._handlingIndex),n(this.model.enableRange?this._closestHandle:this._dragHandle).removeClass("e-m-state-hover").removeClass("e-m-state-active"),!1},_getNewValues:function(n){return this.model.enableRange?this._trimValue(this.values()[n]):this.value()},_getCurrentValue:function(n,t){return this.model.enableRange?(this.values()[t]=this._trimValue(n),this.value(this.values()[t])):(this.value(this._trimValue(n)),this._currentValue=n),this._refreshValue(),this._trimValue(this.value())},_refreshValue:function(){var r=this.model.enableAnimation?"animate":"css",u,t,i;this.model.enableRange?(u=this.values()[0]+","+this.values()[1],this._inputValue.val(u)):this._inputValue.val(this._currentValue);correctedPercent=this._dragHandle.width()/2/this._dragHandle.parent().width()*100;this.model.enableRange?this._rangeRefreshProperties():(t=this.model.minValue!=this.model.maxValue?(this.value()-this.model.minValue)/(this.model.maxValue-this.model.minValue)*100:0,i=t-this._dragHandle.width()/n(this._dragHandle.parent()).width()*100,i<0&&(i=t),this.model.orientation=="horizontal"?this._rangeRefreshOrientationProperties({width:t+"%"},{left:t+"%"},this.model.animationSpeed,r):this._rangeRefreshOrientationProperties({height:t+"%"},{bottom:t+"%"},this.model.animationSpeed,r))},_rangeRefreshProperties:function(){var r=this,t=this.model.enableAnimation?"animate":"css",i=this.model.animationSpeed,f=(this.values()[1]-this.model.minValue)/(this.model.maxValue-this.model.minValue)*100,u=(this.values()[0]-this.model.minValue)/(this.model.maxValue-this.model.minValue)*100;this.model.orientation=="horizontal"?this._selectedRegion[t]({left:u+"%"},i)[t]({width:f-u+"%"},i):this._selectedRegion[t]({bottom:u+"%"},i)[t]({height:f-u+"%"},i);this._dragHandle.each(function(u){percentValue=(r.values()[u]-r.model.minValue)/(r.model.maxValue-r.model.minValue)*100;handlePercent=percentValue-n(this).width()/n(n(this).parent()).width()*100;handlePercent<0&&(handlePercent=percentValue);r.model.orientation=="horizontal"?n(this)[t]({left:percentValue+"%"},i):n(this)[t]({bottom:percentValue+"%"},i);lastPercentValue=percentValue})},_rangeRefreshOrientationProperties:function(n,t,i,r){this._selectedRegion[r](n,i);this._dragHandle[r](t,i)},_getNormalValue:function(n){var i,t,r,u;return this.model.orientation=="vertical"?(i=n.y-this._sliderHandle.offset().top,t=i/this._sliderHandle[0].offsetHeight):(i=n.x-this._sliderHandle.offset().left,t=i/this._sliderHandle[0].offsetWidth),this.model.orientation=="vertical"&&(t=1-t),r=this.model.maxValue-this.model.minValue,u=this.model.minValue+t*r,this._trimValue(u)},_trimValue:function(n){this.model.enableRange||(n<this.model.minValue?n=this.model.minValue:n>this.model.maxValue&&(n=this.model.maxValue));var t=this.model.incrementStep>0?this.model.incrementStep:1,i=(n-this.model.minValue)%t,r=n-i;return Math.abs(i)*2>=t&&(r+=i>0?t:-t),parseFloat(r.toFixed(5))},_slide:function(n,t,i){var u,r;this.model.enableRange?t!=this._getCurrentValue()&&(this.value(t),t<this._closestLeftValue?t=this._closestLeftValue:t>this._closestRightValue&&(t=this._closestRightValue),t!=this._getNewValues(i)&&(u=this._getCurrentValue(t,i),u[i]=t,r={value:this.value(),values:this.values()},this.values([this.values()[0],this.values()[1]]),this._triggerSlide(r,t,this._getNewValues(i),i))):t!=this.value()&&(this._getCurrentValue(t),r={value:this.value(),values:this.values()},this._triggerSlide(r,t,this.value()))},_triggerSlide:function(n,t,i,r){this.model.slide&&this._trigger("slide",n);this._getCurrentValue(t,r);this.prevValue=this._getNewValues(r)},_load:function(){var n=this.model.enableRange?{values:this._getNewValues()}:{value:this.value()};this.model.load&&this._trigger("load",n)},_start:function(n,t){var i={value:this.value(),values:this.values()};this.model.enableRange?(i._getCurrentValue=this._getNewValues(t),i.values=this._getNewValues()):(i._getCurrentValue=this.value(),i.value=this.value());this.model.touchStart&&this._trigger("touchStart",i)},_stop:function(n,t){var i={value:this.value(),values:this.values()};this.model.enableRange?(i._getCurrentValue=this._getNewValues(t),i.values=this._getNewValues()):(i._getCurrentValue=this.value(),i.value=this.value());this.model.touchEnd&&this._trigger("touchEnd",i)},_change:function(n,t){var i={value:this.value(),values:this.values()};this.model.enableRange?(i._getCurrentValue=this._getNewValues(t),i.values=this._getNewValues()):(i._getCurrentValue=this.value(),i.value=this.value());this.model.change&&this._trigger("change",i)},_wireEvents:function(n){t.listenEvents([this._sliderHandle,this.element],[t.startEvent(),t.startEvent()],[this._mouseDownHandler,this._mouseDownHandler],n)},_setRenderMode:function(n){this.model.enabled&&(this.model.renderMode=n,n=="auto"&&t.setRenderMode(this),this.element.removeClass("e-m-ios7 e-m-android e-m-windows e-m-flat").addClass("e-m-"+this.model.renderMode))},_setTheme:function(n){this.model.enabled&&(this.model.theme=n,this.element.removeClass("e-m-dark e-m-light").addClass("e-m-"+n))},_validateValue:function(){this.model.enabled&&(this.model.enableRange||this["_"+this.model.orientation+"RenderMode"]())},_validateRangeValue:function(){this["_"+this.model.orientation+"RenderMode"]();this.values()[0]==this.values()[1]&&(this.model.maxValue==this.values()[1]?this._setRangeValueProperties("",0):this._setRangeValueProperties("",1))},_disable:function(){this.model.enabled=!1;this.element.addClass("e-m-state-disabled");this._wireEvents(!0)},_enable:function(){this.model.enabled=!0;this.element.removeClass("e-m-state-disabled");this._wireEvents()},_enableAnimation:function(n){this.model.enabled&&(this.model.enableAnimation=n)},_refresh:function(){this.model.enabled&&(this._clearElements(),this._renderControl(),this._wireEvents(!1))},_setModel:function(n){for(var t in n)switch(t){case"renderMode":this._setRenderMode(n.renderMode);break;case"theme":this._setTheme(n.theme);break;case"orientation":this._refresh();break;case"value":this._validateValue(n.value);break;case"values":this.model.enableRange&&this.model.enabled&&this._validateRangeValue(n.values);break;case"enabled":n.enabled?this._enable():this._disable();break;case"enableRange":this._refresh();break;case"minValue":this.model.minValue=n.minValue;break;case"maxValue":this.model.maxValue=n.maxValue;break;case"enableAnimation":this._enableAnimation(n.enableAnimation);break;case"readOnly":n.readOnly?this._wireEvents(!0):this._wireEvents(!1)}},_destroy:function(){this._cloneElement.insertBefore(this.element);this.element.remove()},_clearElements:function(){this._wireEvents(!0);this.element.removeAttr("class");this.element.removeAttr("style");this.element.html("")},getValue:function(){return this.model.enableRange?this.values()[0]+","+this.values()[1]:this._trimValue(this.value())},dispose:function(){return this._destroy()}});t.mobile.Slider.Orientation={Horizontal:"horizontal",Vertical:"vertical"};t.mobile.Slider.ThumbStyle={Normal:"normal",Small:"small"}})(jQuery,Syncfusion)});
