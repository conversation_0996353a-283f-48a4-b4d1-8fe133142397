﻿<%@ Page Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="Appointments.aspx.cs" Inherits="WebUI.Appointments" meta:resourcekey="Page" %>

<%@ Register Assembly="Syncfusion.EJ.Web" Namespace="Syncfusion.JavaScript.Web" TagPrefix="ej" %>
<%@ Register Assembly="Syncfusion.EJ" Namespace="Syncfusion.JavaScript.Models" TagPrefix="ej" %>

<asp:Content ID="mainHeadContent" ContentPlaceHolderID="mainHead" runat="server">
    <%--    <script src="LocalizedResources/ej.culture.el-GR.min.js"></script>--%>
    <script type="text/javascript">
        function Initialization() {
            SetLocalization();

            $('#filterTxtBox').keydown(function (e) {
                if (e.keyCode == 13) {
                    e.preventDefault();
                    //return false;
                    //var btnID = '<%=searchBtn.ClientID %>';
                    javascript: __doPostBack('searchBtn', '');
                }
            });
        }

        function onAppointmentClick(args) {
            console.log('onAppointmentClick');
            //var schObj = $("#schedule").data("schedule");
            //schObj.editAppointment(args.appointment.Guid);

            //$('#appointmentsScheduleRecurrenceEdit').css("display", "none !important");
            //$('#appointmentsScheduleRecurrenceEdit').css("z-index", "-1 !important");

            //if (ej.isNullOrUndefined(this._recurEditWindow)) this._renderRecurEditWindow();
            //this._recurEditWindow.css("display", "none !important") 

            if (args.appointment.AppointmentType == 'Appointment')  //Αν κάνουμε επεξεργασία ένα τύπου Appointment.
            {
                window.location.href = 'Appointment.aspx?AppointmentId=' + args.appointment.AppointmentId;
            }
            else if (args.appointment.AppointmentType == 'Event')  //Αν κάνουμε επεξεργασία ένα τύπου Event.
            {
                window.location.href = 'Task.aspx?AppointmentId=' + args.appointment.AppointmentId;
            }
            args.cancel = true; // cancels the click action on appointments.

        }

        function onAppointmentHover(args) {
            console.log('onAppointmentHover');
            args.cancel = true;
            //this._showAppointmentDetails(args.appointment.Guid, true); 
        }

        function onAppointmentSave(args) {
            args.cancel = true; // cancels the save action on appointments.
        }

        function onAppointmentEdit(args) {
            console.log('onAppointmentEdit');
            args.cancel = true; // cancels the edit action on appointments.
        }

        function onAppointmentDelete(args) {
            args.cancel = true; // cancels the delete action on appointments.
        }

        function onMenuItemClick(args) {
            console.log('onMenuItemClick');
            //args.events contains information of the clicked menu item.
            alert(args.appointment.AppointmentType + '--' + args.events.ID);
            if (args.events.ID == "edit") {  //alert(args.eventId);

                if (args.appointment.AppointmentType == 'Appointment')  //Αν κάνουμε επεξεργασία ένα τύπου Appointment.
                {
                    window.location.href = 'Appointment.aspx?AppointmentId=' + args.appointment.AppointmentId;
                }
                else if (args.appointment.AppointmentType == 'Event')  //Αν κάνουμε επεξεργασία ένα τύπου Event.
                {
                    window.location.href = 'Task.aspx?AppointmentId=' + args.appointment.AppointmentId;
                }
            }
        }

        function onCellDoubleClick(args) {
            console.log('onCellDoubleClick');
            args.cancel = true;
        }

        function onAppointmentWindowOpen(args) {
            console.log('onAppointmentWindowOpen');
            //var schObj = $("#schedule").data("ejSchedule");
            //schObj.editAppointment(args.appointment.Guid);

            if (args.appointment.AppointmentType == 'Appointment')  //Αν κάνουμε επεξεργασία ένα τύπου Appointment.
            {
                window.location.href = 'Appointment.aspx?AppointmentId=' + args.appointment.AppointmentId;
            }
            else if (args.appointment.AppointmentType == 'Event')  //Αν κάνουμε επεξεργασία ένα τύπου Event.
            {
                window.location.href = 'Task.aspx?AppointmentId=' + args.appointment.AppointmentId;
            }
            args.cancel = true;
        }

        function ActionComplete(args) {
            if (args.requestType === 'save') { }
            else if (args.requestType === 'viewNavigate') {
                //alert(args.currentDate);
                args.cancel = true;
            }
            //else if (args.requestType === 'dateNavigate') {
            //    if (args.data.target.prevDate == undefined) {
            //        args.cancel = true;
            //    }
            //}
            console.log('ActionComplete: type=' + args.requestType);
        }

        function OnCreate() {
            //var fullWidth = '100%';
            //$('#schedule').ejSchedule({ width: fullWidth });
            console.log('OnCreate');
        }

        function onNavigation(args) {
            //args.target – target element which is clicked.
            //args.currentDate – current date of the Scheduler.
            //args.requestType – Specifies the navigation type.
            //Αν είναι ένα undefined requestType
            if (!args.requestType) {
                args.cancel = true;  //Το ακυρώνουμε γιατί κατά το παρατεταμένο drapndrop δημιουργεί postback.
            }
            else if (args.requestType == "dateNavigate") {
                //alert(args.currentDate);
                //args.cancel = true;
                var date = new Date(args.currentDate);
                //alert(date);
                javascript: __doPostBack('DateNavigate', date.getFullYear() + '/' + (date.getMonth() + 1) + '/' + date.getDate());
            }

        }

        function onCellClick(args) {
            //args.target – target element which is clicked.
            //args.currentDate – current date of the Scheduler.
            //args.requestType – Specifies the navigation type.
            //alert(args.requestType);
            //if (args.requestType == "dateNavigate")
            //alert('cell click');

            console.log('onCellClick');
            args.cancel = true;
        }

        function onDragStop(args) {
            //Aλλάζουμε την ημερομηνία/ώρα του dragndrop appointment
            //var newStartTime = new Date(args.appointment.StartTime);
            //var minute = newStartTime.getMinutes();
            //newStartTime.setMinutes(0);
            //newStartTime.setSeconds(0);
            //newStartTime.setMilliseconds(0);
            //alert(args.appointment.StartTime);
            //args.appointment.StartTime = newStartTime;  
            args.cancel = true;
        }

        function onExportScheduleClick(args) {
            var obj = $("#appointmentsSchedule").data("ejSchedule");
            obj.exportSchedule(null, "exportPDF", null);
        }

        function onPrintScheduleClick(args) {
            var obj = $("#appointmentsSchedule").data("ejSchedule");
            obj.print();

            //var obj = $("#appointmentsSchedule").data("ejSchedule");
            //obj.exportToExcel();
        }
    </script>
    <script type="text/template" id="cellTooltipTemplate">
        {{:value }}
    </script>

    <script type="text/x-template" id="notesColumnTemplate">
      <span style="white-space: break-spaces;">{{:Notes}}</span>
    </script>

    <script id="appointmentTooltipTemplate" type="text/x-jsrender">
       {{if AppointmentType=='Appointment'}} {{:ContactFullName}} {{/if}}
       {{if AppointmentType=='Event'}} {{:Subject}} {{/if}} 
       ({{:~tmTemplate(StartTime)}}-{{:~tmTemplate(EndTime)}})
    </script>

     <script>
         function _timeFormat(date) {
             var dFormat = ej.format(new Date(date), "HH:mm");
             return dFormat;
         }
         $.views.helpers({ tmTemplate: _timeFormat });
     </script>

    <script id="AppointmentTemplate" type="text/x-jsrender">
        <div {{if Canceled == true }} style='display: block;height: inherit; background:repeating-linear-gradient(45deg, rgb(0 0 0 / 0%),  rgb(0 0 0 / 0%) 4px,  rgba(0, 0, 0, 0.2) 2px,  rgba(0, 0, 0, 0.2) 8px);' {{/if}}>
             <div>{{if CustomRecurrence == true}}<i class="fa fa-repeat"></i>&nbsp;{{/if}} 
                 {{if AppointmentType=='Appointment'}} {{:ContactFullName}} {{/if}}
                 {{if AppointmentType=='Event'}} {{:Subject}} {{/if}}</div>
        </div>
    </script>

    <style>
        /*Μειώνει τις διαστάσεις των κελιών στα κινητά*/
        @media (max-width : 767px) {
            .e-schedule .e-datecolumn {
                width: 135px !important;
            }

            .e-schedule .e-timecolumn {
                width: 65px !important;
            }
        }

        /*Διορθώνει το ύψος της ώρας στα αριστερά*/
        .e-timecells {
            height: 41px !important;
        }
    </style>
</asp:Content>
<asp:Content ID="mainBodyContent" ContentPlaceHolderID="mainBody" runat="server">
    <div class="row margin-bottom">
        <div class="col-xs-6">
            <a id="newAppointmentBtn" href="Appointment.aspx" runat="server" type="button" class="btn btn-primary btn-flat margin-r-5">
                <asp:Literal meta:resourcekey="newAppointmentBtn" runat="server"></asp:Literal></a>
            <a id="newTaskBtn" href="Task.aspx" runat="server" type="button" class="btn btn-primary btn-flat margin-r-5">
                <asp:Literal meta:resourcekey="newTaskBtn" runat="server"></asp:Literal></a>
        </div>
        <div class="col-xs-6">
            <button id="exportScheduleBtn" runat="server" class="btn btn-default btn-flat margin-bottom margin-r-5 pull-right" onclick="onExportScheduleClick()">
                <span class="hidden-md hidden-lg"><i class="fa fa-file-export"></i></span>
                <asp:Label meta:resourceKey="exportScheduleLbl" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></button>
            <%-- <button id="printScheduleBtn" runat="server" class="btn btn-default btn-flat margin-bottom margin-r-5" onclick="onPrintScheduleClick();">
                        <span class="hidden-md hidden-lg"><i class="fa fa-print"></i></span>
                        <asp:Label meta:resourceKey="printScheduleLbl" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></button>--%>
            <ej:DropDownList ID="scheduleResourcesDDL" runat="server" CssClass="pull-right margin-r-5" OnValueSelect="scheduleResourcesDDL_ValueSelect" DataTextField="FullName" DataValueField="UserId" EnableSorting="true" ShowCheckbox="true" MultiSelectMode="VisualMode" Width="auto" Height="30px"></ej:DropDownList>
        </div>
    </div>
    <asp:UpdatePanel ID="appointmentsUpdatePanel" runat="server" UpdateMode="Conditional">
        <Triggers>
            <asp:AsyncPostBackTrigger ControlID="appointmentsSchedule" />
        </Triggers>
        <ContentTemplate>
            <div class="row">
                <div class="col-xs-12">
                    <div id="gridDateFilters" runat="server" class="btn-group">
                        <button type="button" id="currentDayBtn" runat="server" onserverclick="currentDayBtn_ServerClick" class="btn btn-default">
                            <span class="hidden-md hidden-lg"><i class="fa fa-calendar-day "></i></span>
                            <asp:Label meta:resourceKey="currentDayBtn" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></button>
                        <button type="button" id="currentWeekBtn" runat="server" onserverclick="currentWeekBtn_ServerClick" class="btn btn-default">
                            <span class="hidden-md hidden-lg"><i class="fa fa-calendar-week "></i></span>
                            <asp:Label meta:resourceKey="currentWeekBtn" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></button>
                        <button type="button" id="currentMonthBtn" runat="server" onserverclick="currentMonthBtn_ServerClick" class="btn btn-default">
                            <span class="hidden-md hidden-lg"><i class="fa fa-calendar "></i></span>
                            <asp:Label meta:resourceKey="currentMonthBtn" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></button>
                    </div>
                    <div id="gridSortDiv" runat="server" class="input-group input-group-sm margin-bottom pull-right" style="min-width: 60px; max-width: 200px;">
                        <span class="input-group-addon borderless"><i class="fa fa-sort-alpha-desc"></i></span>
                        <ej:DropDownList ID="sortDDL" runat="server" OnValueSelect="sortDDL_ValueSelect">
                            <Items>
                                <ej:DropDownListItem Text="Έναρξη (Αυξ.)" Value="StartTime ASC"></ej:DropDownListItem>
                                <ej:DropDownListItem Text="Έναρξη (Φθίν.)" Value="StartTime DESC"></ej:DropDownListItem>
                                <ej:DropDownListItem Text="Λήξη (Αυξ.)" Value="EndTime ASC"></ej:DropDownListItem>
                                <ej:DropDownListItem Text="Λήξη (Φθίν.)" Value="EndTime DESC"></ej:DropDownListItem>
                            </Items>
                        </ej:DropDownList>
                    </div>
                    <div id="filterDiv" runat="server" class="input-group input-group-sm margin-bottom pull-right margin-r-5" style="min-width: 200px; max-width: 250px;">
                        <input type="text" runat="server" id="filterTxtBox" class="form-control" maxlength="30">
                        <span class="input-group-btn">
                            <button type="button" id="clearSearchBtn" runat="server" class="btn btn-default btn-flat" style="border-radius: unset !important" onserverclick="clearSearchBtn_ServerClick">
                                <i class="fa fa-remove"></i>
                            </button>
                        </span>
                        <span class="input-group-btn">
                            <button type="button" id="searchBtn" runat="server" class="btn btn-info btn-flat" onserverclick="searchBtn_ServerClick">
                                <i class="fa fa-search  hidden-md hidden-lg"></i>
                                <asp:Label meta:resourcekey="searchBtn" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></button>
                        </span>
                    </div>
                </div>
            </div>
            <div runat="server" id="appointmentsScheduleDiv" class="row">
                <div class="col-xs-12">
                    <ej:Schedule ID="appointmentsSchedule" ClientIDMode="Static" runat="server" Locale="el-GR" Width="100%" IsResponsive="true" StartHour="8" DragStop="onDragStop" AppointmentTemplateId="#AppointmentTemplate" CellClick="onCellClick" Create="OnCreate" AppointmentHover="onAppointmentHover" AppointmentClick="onAppointmentClick" OnServerExportPDF="appointmentsSchedule_ServerExportPDF" OnServerResizeStop="appointmentsSchedule_ServerResizeStop" OnServerNavigation="appointmentsSchedule_ServerNavigation" OnServerDragStop="appointmentsSchedule_ServerDragStop" BeforeAppointmentChange="onAppointmentEdit"   CellDoubleClick="onCellDoubleClick" AppointmentWindowOpen="onAppointmentWindowOpen" ActionComplete="ActionComplete" Navigation="onNavigation" OnServerAppointmentClick="appointmentsSchedule_ServerAppointmentClick">
                        <AppointmentSettings ApplyTimeOffset="false" Id="AppointmentId" Subject="Summary" StartTime="StartTime" EndTime="EndTime" AllDay="AllDay" Description="Summary" Categorize="Room" Recurrence="Recurrence" RecurrenceRule="RecurrenceRule" RecurrenceId="RecurrenceId" RecurrenceExDate="RecurrenceExDate" ResourceFields="UserId" />
                        <TooltipSettings Enable="true" TemplateId="#appointmentTooltipTemplate" />
                        <ContextMenuSettings Enable="false">
                            <MenuItems>
                                <AppointmentCollection>
                                    <ej:Appointment Id="open" Text="Επεξεργασία" />
                                </AppointmentCollection>
                                <CellsCollection></CellsCollection>
                            </MenuItems>
                        </ContextMenuSettings>
                        <CategorizeSettings Enable="true" Id="RoomId" Color="BackColor" FontColor="FontColor" Text="RoomName">
                        </CategorizeSettings>
                        <Resources>
                            <ej:Resources Field="UserId" Name="Doctors" Title="Γιατροί" AllowMultiple="true">
                                <ResourceSettings Color="Color" Id="UserId" Text="FullName">
                                </ResourceSettings>
                            </ej:Resources>
                        </Resources>
                        <Group Resources="Doctors" />
                    </ej:Schedule>

                </div>
            </div>
            <div runat="server" id="appointmentsGridDiv" class="row">
                <div class="col-xs-12">
                    <ej:Grid ID="appointmentsGrid" runat="server" AllowSelection="false" IsResponsive="true" EnableResponsiveRow="true" MinWidth="900" OnServerRecordDoubleClick="appointmentsGrid_ServerRecordDoubleClick" OnServerCommandButtonClick="appointmentsGrid_ServerCommandButtonClick">
                        <%--<SortedColumns>
                            <ej:SortedColumn Field="StartTime" Direction="Descending">
                            </ej:SortedColumn>
                        </SortedColumns>--%>
                        <Columns>
                            <ej:Column Field="StartTime" HeaderText="Ημ/νία" Width="90" Format="{0:dd/MM/yyyy}">
                            </ej:Column>
                            <ej:Column Field="ContactFullName" HeaderText="Πελάτης" Width="190">
                            </ej:Column>
                            <ej:Column Field="ContactMobile1" HeaderText="Κινητό 1" Width="110">
                            </ej:Column>
                            <ej:Column Field="StartTime" HeaderText="Ώρα Έναρξης" Width="90" Format="{0:HH:mm}">
                            </ej:Column>
                            <ej:Column Field="EndTime" HeaderText="Ώρα Λήξης" Width="90" Format="{0:HH:mm}">
                            </ej:Column>
                            <ej:Column Field="Notes" HeaderText="Σημειώσεις" AllowTextWrap="true" Tooltip="#cellTooltipTemplate" TemplateID="#notesColumnTemplate">
                            </ej:Column>
                            <ej:Column HeaderText=" " IsUnbound="True" Width="100" AllowResizing="False" AllowTextWrap="False">
                                <Command>
                                    <ej:Commands Type="Edit">
                                        <ButtonOptions Size="Small" Text="Edit" Type="Button" ContentType="ImageOnly" PrefixIcon="e-icon e-edit" Width="30"></ButtonOptions>
                                    </ej:Commands>
                                    <ej:Commands Type="Delete">
                                        <ButtonOptions Size="Small" Text="Delete" Type="Button" ContentType="ImageOnly" PrefixIcon="e-icon e-delete" Width="30"></ButtonOptions>
                                    </ej:Commands>
                                </Command>
                            </ej:Column>
                            <ej:Column Field="AppointmentId" HeaderText="A/A" IsIdentity="True" IsPrimaryKey="True" Visible="False" Width="0">
                            </ej:Column>
                            <%--<ej:Column HeaderText="" Width="40" Template="<span style='height:20px;width:20px;display:block;background-color:{{:AppointmentCategoryColor}}'></span>">
                            </ej:Column>--%>
                        </Columns>
                    </ej:Grid>
                    <ej:Pager ID="appointmentsPager" runat="server" OnChange="appointmentsPager_Change" IsResponsive="true" Locale="el-GR"></ej:Pager>
                </div>
            </div>
        </ContentTemplate>
    </asp:UpdatePanel>
</asp:Content>
