﻿using C1.C1Report;
using Syncfusion.DocIO.DLS;
using Syncfusion.DocToPDFConverter;
using Syncfusion.EJ.Export;
using Syncfusion.Pdf;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebUI.Reporting
{
    public partial class ExportWordReport : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                if (Session["Report"] != null)
                {
                    WordDocument document = Session["Report"] as WordDocument;
                    Session.Remove("Report");
                    string reportName = Session["ReportName"].ToString();
                    Session.Remove("ReportName");

                    bool inline = Convert.ToBoolean(Request.QueryString["Inline"]);

                    if (document != null)
                    {
                        Export(document, reportName, inline);
                    }
                }
                else if (Session["PdfReport"] != null)
                {
                    PdfDocument pdfDocument = Session["PdfReport"] as PdfDocument;
                    Session.Remove("PdfReport");
                    string reportName = Session["ReportName"].ToString();
                    Session.Remove("ReportName");

                    bool inline = Convert.ToBoolean(Request.QueryString["Inline"]);

                    if (pdfDocument != null)
                    {
                        Export(pdfDocument, reportName, inline);
                    }
                }
                else if (Session["C1Report"] != null)
                {
                    C1Report report = Session["C1Report"] as C1Report;
                    bool inline = Convert.ToBoolean(Request.QueryString["Inline"]);

                    if (report != null)
                    {
                        Export(report, report.ReportName + " " + DateTime.Now.ToString(), inline);
                    }
                }
                else if (Session["ExcelReport"] != null)
                {
                    C1Report report = Session["ExcelReport"] as C1Report;
                    bool inline = Convert.ToBoolean(Request.QueryString["Inline"]);

                    if (report != null)
                    {
                        ExportExcel(report, report.ReportName + " " + DateTime.Now.ToString(), inline);
                    }
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        public void Export(WordDocument document, string fileName, bool inline)
        {
            try
            {
                //Initializes the ChartToImageConverter for converting charts during Word to pdf conversion
                //wordDocument.ChartToImageConverter =  new ChartToImageConverter();
                //Creates an instance of the DocToPDFConverter
                DocToPDFConverter converter = new DocToPDFConverter();
                converter.Settings.EmbedFonts = true;
                //Converts Word document into PDF document
                PdfDocument pdfDocument = converter.ConvertToPDF(document);
                //Saves the PDF file 
                //pdfDocument.Save("WordtoPDF.pdf");
                //MemoryStream stream = new MemoryStream();
                //Saves the document to stream
                pdfDocument.Save(fileName, Response, HttpReadType.Open);
                //Closes the instance of document objects
                pdfDocument.Close(true);
                document.Close();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        public void Export(PdfDocument pdfDocument, string fileName, bool inline)
        {
            try
            {
                //Saves the PDF file 
                //pdfDocument.Save("WordtoPDF.pdf");
                //MemoryStream stream = new MemoryStream();
                //Saves the document to stream
                pdfDocument.Save(fileName, Response, HttpReadType.Open);
                //Closes the instance of document objects
                pdfDocument.Close(true);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        public void Export(C1Report report, string fileName, bool inline)
        {
            try
            {
                MemoryStream stream = new MemoryStream();
                string fileType = "pdf";
                Response.Clear();

                //if (fileType == "xls")
                //    report.(stream);
                if (fileType == "pdf")
                    report.RenderToStream(stream, C1.C1Report.FileFormatEnum.PDFEmbedFonts);
                //if (fileType == "rtf")
                //    report.ExportToRtf(stream);
                //if (fileType == "csv")
                //    report.ExportToCsv(stream);

                Response.ClearHeaders();
                Response.ContentType = "application/" + fileType;
                Response.AddHeader("Accept-Header", stream.Length.ToString());
                Response.AddHeader("Content-Disposition", (inline ? "Inline" : "Attachment") + "; filename=" + fileName + "." + fileType);
                Response.AddHeader("Content-Length", stream.Length.ToString());
                //Response.ContentEncoding = System.Text.Encoding.Default;

                Response.BinaryWrite(stream.ToArray());
                Response.Flush();
                Response.End();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                if (exp.GetType() != typeof(ThreadAbortException))
                {
                    throw;
                }
            }
        }

        public void ExportExcel(C1Report report, string fileName, bool inline)
        {
            try
            {
                MemoryStream stream = new MemoryStream();
                string fileType = "xls";
                Response.Clear();

                if (fileType == "xls")
                    report.RenderToStream(stream, FileFormatEnum.Excel);
                //if (fileType == "pdf")
                //    report.RenderToStream(stream, C1.C1Report.FileFormatEnum.PDFEmbedFonts);
                //if (fileType == "rtf")
                //    report.ExportToRtf(stream);
                //if (fileType == "csv")
                //    report.ExportToCsv(stream);

                Response.ClearHeaders();
                Response.ContentType = "application/" + fileType;
                Response.AddHeader("Accept-Header", stream.Length.ToString());
                Response.AddHeader("Content-Disposition", (inline ? "Inline" : "Attachment") + "; filename=" + fileName + "." + fileType);
                Response.AddHeader("Content-Length", stream.Length.ToString());
                //Response.ContentEncoding = System.Text.Encoding.Default;

                Response.BinaryWrite(stream.ToArray());
                Response.Flush();
                Response.End();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                if (exp.GetType() != typeof(ThreadAbortException))
                {
                    throw;
                }
            }
        }
    }
}