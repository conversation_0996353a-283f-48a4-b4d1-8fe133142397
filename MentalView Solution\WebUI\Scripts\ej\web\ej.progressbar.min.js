/*!
*  filename: ej.progressbar.min.js
*  version : 20.3.0.59
*  Copyright Syncfusion Inc. 2001 - 2022. All rights reserved.
*  Use of this code is subject to the terms of our license.
*  A copy of the current license can be obtained at any time by e-mailing
*  <EMAIL>. Any infringement will be prosecuted under
*  applicable laws. 
*/
(function(n){typeof define=="function"&&define.amd?define(["./../common/ej.core.min"],n):n()})(function(){(function(n,t){t.widget("ejProgressBar","ej.ProgressBar",{element:null,model:null,validTags:["div","span"],_addToPersist:["value","percentage"],_setFirst:!1,_rootCSS:"e-progressbar",defaults:{text:"",cssClass:"",minValue:0,maxValue:100,value:0,percentage:0,height:null,htmlAttributes:{},width:null,enabled:!0,enableRTL:!1,showRoundedCorner:!1,enablePersistence:!1,start:null,complete:null,change:null,create:null,destroy:null},dataTypes:{cssClass:"string",minValue:"number",maxValue:"number",enabled:"boolean",enableRTL:"boolean",showRoundedCorner:"boolean",htmlAttributes:"data"},_setValue:function(n){n==null?n=this.model.minValue:typeof n=="string"&&(n=parseFloat(n));this._isNumber(n)?this.model.value=n:this._isNumber(this.model.value)||(this.model.value=this.model.minValue);this.model.value=this._validateRange(this.model.value,this.model.minValue,this.model.maxValue);this._setProgressValue()},_setPercent:function(n){this.initial=this.model.percentage;this.initial==100&&(this.initial=0);n==null?n=0:typeof n=="string"&&(n=parseFloat(n));this._isNumber(n)?this.model.percentage=n:this._isNumber(this.model.percentage)||(this.model.percentage=0);this.model.percentage=this._validateRange(this.model.percentage,0,100);this.model.value=this._percentToValue(this.model.percentage);this._increaseProgressWidth()},_validateMinMax:function(){isNaN(this.model.minValue)&&(this.model.minValue=0);isNaN(this.model.maxValue)&&(this.model.maxValue=100)},_setText:function(n){n?this.text?this.text.html(n):(this.text=t.buildTag("div.e-progress-txt",n),this.element.append(this.text),this._setTop()):this.text&&(this.text.remove(),this.text=null)},_changeSkin:function(n){this.model.cssClass!=n&&this.element.removeClass(this.model.cssClass).addClass(n)},enable:function(){this.element.removeClass("e-disable");this.model.enabled=!0},disable:function(){this.element.addClass("e-disable");this.model.enabled=!1},getValue:function(){return this.model.value},getPercentage:function(){return this.model.percentage},_init:function(){this._initialize();this._render()},_setModel:function(n){for(var t in n)switch(t){case"value":this._setValue(n[t]);n[t]=this.model.value;break;case"percentage":this._setPercent(n[t]);n[t]=this.model.percentage;break;case"minValue":isNaN(n[t])||this._minValidation(n[t]);n[t]=this.model.minValue;break;case"maxValue":isNaN(n[t])||this._maxValidation(n[t]);n[t]=this.model.maxValue;break;case"text":this._setText(n[t]);break;case"height":this._setHeight(n[t]);this.text&&this._setTop();break;case"width":this._setWidth(n[t]);break;case"enabled":this._disabled(!n[t]);break;case"cssClass":this._changeSkin(n[t]);break;case"enableRTL":this._rtl(n[t]);break;case"showRoundedCorner":this._roundedCorner(n[t]);break;case"htmlAttributes":this._addAttr(n[t])}},_destroy:function(){this.element.empty();this.element.removeClass("e-widget e-box e-corner "+this.model.cssClass).css("height","")},_initialize:function(){this.text=null;this.header=null;this._preVal=null},_render:function(){this.initialRender=!0;this.element.addClass("e-widget e-box "+this.model.cssClass).attr("role","progressbar");this._setDimention();this.header=t.buildTag("div.e-progress");this.element.append(this.header);this._setText(this.model.text);this._setInitialValue();this._addAttr(this.model.htmlAttributes);this._checkProperties();this._roundedCorner(this.model.showRoundedCorner)},_addAttr:function(t){var i=this;n.map(t,function(n,t){t=="class"?i.element.addClass(n):t=="disabled"&&n=="disabled"?i._disabled(!1):i.element.attr(t,n)})},_setDimention:function(){this._setHeight(this.model.height);this._setWidth(this.model.width)},_setHeight:function(n){n&&this.element.height(n)},_setWidth:function(n){n&&this.element.css("width",n)},_setInitialValue:function(){this._validateMinMax();this.model.percentage?this._setPercent(this.model.percentage):this._setValue(this.model.value)},_disabled:function(n){n?this.disable():this.enable()},_checkProperties:function(){this.model.enableRTL&&this._rtl(this.model.enableRTL);this._minValidation(this.model.minValue);this._maxValidation(this.model.maxValue);this.model.enabled||this._disabled(!0)},_rtl:function(n){n?this.element.addClass("e-rtl"):this.element.removeClass("e-rtl")},_roundedCorner:function(n){n&&!this.element.hasClass("e-corner")?this.element.addClass("e-corner"):this.element.hasClass("e-corner")&&this.element.removeClass("e-corner")},_minValidation:function(n){this.model.maxValue&&this.model.maxValue<n&&(this.model.maxValue=n);this.model.value<n&&(this.model.value=n);this.model.minValue=n;this._setProgressValue()},_maxValidation:function(n){this.model.minValue&&this.model.minValue>n&&(this.model.minValue=n);this.model.value>n&&(this.model.value=n);this.model.maxValue=n;this._setProgressValue()},_setTop:function(){var n=(this.element.height()-this.text.height())/2;this.text.css("top",Math.floor(n))},_increaseProgressWidth:function(){this.header.css("width",this.model.percentage+"%");this.initial==0&&this.model.percentage!=this.initial&&this._raiseEvent("start");this._preVal!=this.model.value&&(this._preVal=this.model.value,n(this.header).attr("aria-label",this.model.percentage),this.header.hasClass("e-complete")&&this.header.removeClass("e-complete"),this.initialRender?this.initialRender=!1:this._raiseEvent("change"),this.model.percentage==100&&(this.header.addClass("e-complete"),this._raiseEvent("complete")))},_raiseEvent:function(n){this._trigger(n,{value:this.model.value,percentage:this.model.percentage})},_setProgressValue:function(){this.initial=this.model.percentage;this.model.percentage=this._valueToPercent(this.model.value);this._increaseProgressWidth()},_isNumber:function(n){return typeof n=="number"&&!isNaN(n)},_validateRange:function(n,t,i){return n<t?t:n>i?i:n},_valueToPercent:function(n){return this.model.maxValue<=this.model.minValue?100:(n=this._validateRange(n,this.model.minValue,this.model.maxValue),100*(n-this.model.minValue)/(this.model.maxValue-this.model.minValue))},_percentToValue:function(n){if(this.model.maxValue<=this.model.minValue)return this.model.minValue;if(n>=0&&n<=100){var t=this.model.maxValue-this.model.minValue,i=t*n/100;n=i+this.model.minValue}else n<0?n=this.model.minValue:n>100&&(n=this.model.maxValue);return n}})})(jQuery,Syncfusion)});
